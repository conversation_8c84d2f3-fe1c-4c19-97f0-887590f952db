{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport const diffEditorDefaultOptions = {\n  enableSplitViewResizing: true,\n  splitViewDefaultRatio: 0.5,\n  renderSideBySide: true,\n  renderMarginRevertIcon: true,\n  renderGutterMenu: true,\n  maxComputationTime: 5000,\n  maxFileSize: 50,\n  ignoreTrimWhitespace: true,\n  renderIndicators: true,\n  originalEditable: false,\n  diffCodeLens: false,\n  renderOverviewRuler: true,\n  diffWordWrap: 'inherit',\n  diffAlgorithm: 'advanced',\n  accessibilityVerbose: false,\n  experimental: {\n    showMoves: false,\n    showEmptyDecorations: true,\n    useTrueInlineView: false\n  },\n  hideUnchangedRegions: {\n    enabled: false,\n    contextLineCount: 3,\n    minimumLineCount: 3,\n    revealLineCount: 20\n  },\n  isInEmbeddedEditor: false,\n  onlyShowAccessibleDiffViewer: false,\n  renderSideBySideInlineBreakpoint: 900,\n  useInlineViewWhenSpaceIsLimited: true,\n  compactMode: false\n};", "map": {"version": 3, "names": ["diffEditorDefaultOptions", "enableSplitViewResizing", "splitViewDefaultRatio", "renderSideBySide", "renderMarginRevertIcon", "renderGutterMenu", "maxComputationTime", "maxFileSize", "ignoreTrimWhitespace", "renderIndicators", "originalEditable", "diffCodeLens", "renderOverviewRuler", "diffWordWrap", "diffAlgorithm", "accessibilityVerbose", "experimental", "showMoves", "showEmptyDecorations", "useTrueInlineView", "hideUnchangedRegions", "enabled", "contextLineCount", "minimumLineCount", "revealLineCount", "isInEmbeddedEditor", "onlyShowAccessibleDiffViewer", "renderSideBySideInlineBreakpoint", "useInlineViewWhenSpaceIsLimited", "compactMode"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/config/diffEditor.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport const diffEditorDefaultOptions = {\n    enableSplitViewResizing: true,\n    splitViewDefaultRatio: 0.5,\n    renderSideBySide: true,\n    renderMarginRevertIcon: true,\n    renderGutterMenu: true,\n    maxComputationTime: 5000,\n    maxFileSize: 50,\n    ignoreTrimWhitespace: true,\n    renderIndicators: true,\n    originalEditable: false,\n    diffCodeLens: false,\n    renderOverviewRuler: true,\n    diffWordWrap: 'inherit',\n    diffAlgorithm: 'advanced',\n    accessibilityVerbose: false,\n    experimental: {\n        showMoves: false,\n        showEmptyDecorations: true,\n        useTrueInlineView: false,\n    },\n    hideUnchangedRegions: {\n        enabled: false,\n        contextLineCount: 3,\n        minimumLineCount: 3,\n        revealLineCount: 20,\n    },\n    isInEmbeddedEditor: false,\n    onlyShowAccessibleDiffViewer: false,\n    renderSideBySideInlineBreakpoint: 900,\n    useInlineViewWhenSpaceIsLimited: true,\n    compactMode: false,\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,MAAMA,wBAAwB,GAAG;EACpCC,uBAAuB,EAAE,IAAI;EAC7BC,qBAAqB,EAAE,GAAG;EAC1BC,gBAAgB,EAAE,IAAI;EACtBC,sBAAsB,EAAE,IAAI;EAC5BC,gBAAgB,EAAE,IAAI;EACtBC,kBAAkB,EAAE,IAAI;EACxBC,WAAW,EAAE,EAAE;EACfC,oBAAoB,EAAE,IAAI;EAC1BC,gBAAgB,EAAE,IAAI;EACtBC,gBAAgB,EAAE,KAAK;EACvBC,YAAY,EAAE,KAAK;EACnBC,mBAAmB,EAAE,IAAI;EACzBC,YAAY,EAAE,SAAS;EACvBC,aAAa,EAAE,UAAU;EACzBC,oBAAoB,EAAE,KAAK;EAC3BC,YAAY,EAAE;IACVC,SAAS,EAAE,KAAK;IAChBC,oBAAoB,EAAE,IAAI;IAC1BC,iBAAiB,EAAE;EACvB,CAAC;EACDC,oBAAoB,EAAE;IAClBC,OAAO,EAAE,KAAK;IACdC,gBAAgB,EAAE,CAAC;IACnBC,gBAAgB,EAAE,CAAC;IACnBC,eAAe,EAAE;EACrB,CAAC;EACDC,kBAAkB,EAAE,KAAK;EACzBC,4BAA4B,EAAE,KAAK;EACnCC,gCAAgC,EAAE,GAAG;EACrCC,+BAA+B,EAAE,IAAI;EACrCC,WAAW,EAAE;AACjB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}