{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { strictEquals } from '../equals.js';\nimport { ObservableValue } from './base.js';\nimport { DebugNameData } from './debugName.js';\nimport { LazyObservableValue } from './lazyObservableValue.js';\nexport function observableValueOpts(options, initialValue) {\n  if (options.lazy) {\n    return new LazyObservableValue(new DebugNameData(options.owner, options.debugName, undefined), initialValue, options.equalsFn ?? strictEquals);\n  }\n  return new ObservableValue(new DebugNameData(options.owner, options.debugName, undefined), initialValue, options.equalsFn ?? strictEquals);\n}", "map": {"version": 3, "names": ["strictEquals", "ObservableValue", "DebugNameData", "LazyObservableValue", "observableValueOpts", "options", "initialValue", "lazy", "owner", "debugName", "undefined", "equalsFn"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/common/observableInternal/api.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { strictEquals } from '../equals.js';\nimport { ObservableValue } from './base.js';\nimport { DebugNameData } from './debugName.js';\nimport { LazyObservableValue } from './lazyObservableValue.js';\nexport function observableValueOpts(options, initialValue) {\n    if (options.lazy) {\n        return new LazyObservableValue(new DebugNameData(options.owner, options.debugName, undefined), initialValue, options.equalsFn ?? strictEquals);\n    }\n    return new ObservableValue(new DebugNameData(options.owner, options.debugName, undefined), initialValue, options.equalsFn ?? strictEquals);\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,YAAY,QAAQ,cAAc;AAC3C,SAASC,eAAe,QAAQ,WAAW;AAC3C,SAASC,aAAa,QAAQ,gBAAgB;AAC9C,SAASC,mBAAmB,QAAQ,0BAA0B;AAC9D,OAAO,SAASC,mBAAmBA,CAACC,OAAO,EAAEC,YAAY,EAAE;EACvD,IAAID,OAAO,CAACE,IAAI,EAAE;IACd,OAAO,IAAIJ,mBAAmB,CAAC,IAAID,aAAa,CAACG,OAAO,CAACG,KAAK,EAAEH,OAAO,CAACI,SAAS,EAAEC,SAAS,CAAC,EAAEJ,YAAY,EAAED,OAAO,CAACM,QAAQ,IAAIX,YAAY,CAAC;EAClJ;EACA,OAAO,IAAIC,eAAe,CAAC,IAAIC,aAAa,CAACG,OAAO,CAACG,KAAK,EAAEH,OAAO,CAACI,SAAS,EAAEC,SAAS,CAAC,EAAEJ,YAAY,EAAED,OAAO,CAACM,QAAQ,IAAIX,YAAY,CAAC;AAC9I", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}