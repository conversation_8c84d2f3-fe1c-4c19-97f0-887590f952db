{"ast": null, "code": "import _asyncToGenerator from \"C:/console/aava-ui-web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { AsyncIterableObject } from '../../../../base/common/async.js';\nimport { CancellationToken } from '../../../../base/common/cancellation.js';\nimport { onUnexpectedExternalError } from '../../../../base/common/errors.js';\nimport { registerModelAndPositionCommand } from '../../../browser/editorExtensions.js';\nimport { ILanguageFeaturesService } from '../../../common/services/languageFeatures.js';\nexport class HoverProviderResult {\n  constructor(provider, hover, ordinal) {\n    this.provider = provider;\n    this.hover = hover;\n    this.ordinal = ordinal;\n  }\n}\n/**\n * Does not throw or return a rejected promise (returns undefined instead).\n */\nfunction executeProvider(_x, _x2, _x3, _x4, _x5) {\n  return _executeProvider.apply(this, arguments);\n}\nfunction _executeProvider() {\n  _executeProvider = _asyncToGenerator(function* (provider, ordinal, model, position, token) {\n    const result = yield Promise.resolve(provider.provideHover(model, position, token)).catch(onUnexpectedExternalError);\n    if (!result || !isValid(result)) {\n      return undefined;\n    }\n    return new HoverProviderResult(provider, result, ordinal);\n  });\n  return _executeProvider.apply(this, arguments);\n}\nexport function getHoverProviderResultsAsAsyncIterable(registry, model, position, token, recursive = false) {\n  const providers = registry.ordered(model, recursive);\n  const promises = providers.map((provider, index) => executeProvider(provider, index, model, position, token));\n  return AsyncIterableObject.fromPromises(promises).coalesce();\n}\nexport function getHoversPromise(registry, model, position, token, recursive = false) {\n  return getHoverProviderResultsAsAsyncIterable(registry, model, position, token, recursive).map(item => item.hover).toPromise();\n}\nregisterModelAndPositionCommand('_executeHoverProvider', (accessor, model, position) => {\n  const languageFeaturesService = accessor.get(ILanguageFeaturesService);\n  return getHoversPromise(languageFeaturesService.hoverProvider, model, position, CancellationToken.None);\n});\nregisterModelAndPositionCommand('_executeHoverProvider_recursive', (accessor, model, position) => {\n  const languageFeaturesService = accessor.get(ILanguageFeaturesService);\n  return getHoversPromise(languageFeaturesService.hoverProvider, model, position, CancellationToken.None, true);\n});\nfunction isValid(result) {\n  const hasRange = typeof result.range !== 'undefined';\n  const hasHtmlContent = typeof result.contents !== 'undefined' && result.contents && result.contents.length > 0;\n  return hasRange && hasHtmlContent;\n}", "map": {"version": 3, "names": ["AsyncIterableObject", "CancellationToken", "onUnexpectedExternalError", "registerModelAndPositionCommand", "ILanguageFeaturesService", "HoverProviderResult", "constructor", "provider", "hover", "ordinal", "executeProvider", "_x", "_x2", "_x3", "_x4", "_x5", "_executeProvider", "apply", "arguments", "_asyncToGenerator", "model", "position", "token", "result", "Promise", "resolve", "provideHover", "catch", "<PERSON><PERSON><PERSON><PERSON>", "undefined", "getHoverProviderResultsAsAsyncIterable", "registry", "recursive", "providers", "ordered", "promises", "map", "index", "fromPromises", "coalesce", "getHoversPromise", "item", "to<PERSON>romise", "accessor", "languageFeaturesService", "get", "hoverProvider", "None", "has<PERSON><PERSON><PERSON>", "range", "hasHtmlContent", "contents", "length"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/contrib/hover/browser/getHover.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { AsyncIterableObject } from '../../../../base/common/async.js';\nimport { CancellationToken } from '../../../../base/common/cancellation.js';\nimport { onUnexpectedExternalError } from '../../../../base/common/errors.js';\nimport { registerModelAndPositionCommand } from '../../../browser/editorExtensions.js';\nimport { ILanguageFeaturesService } from '../../../common/services/languageFeatures.js';\nexport class HoverProviderResult {\n    constructor(provider, hover, ordinal) {\n        this.provider = provider;\n        this.hover = hover;\n        this.ordinal = ordinal;\n    }\n}\n/**\n * Does not throw or return a rejected promise (returns undefined instead).\n */\nasync function executeProvider(provider, ordinal, model, position, token) {\n    const result = await Promise\n        .resolve(provider.provideHover(model, position, token))\n        .catch(onUnexpectedExternalError);\n    if (!result || !isValid(result)) {\n        return undefined;\n    }\n    return new HoverProviderResult(provider, result, ordinal);\n}\nexport function getHoverProviderResultsAsAsyncIterable(registry, model, position, token, recursive = false) {\n    const providers = registry.ordered(model, recursive);\n    const promises = providers.map((provider, index) => executeProvider(provider, index, model, position, token));\n    return AsyncIterableObject.fromPromises(promises).coalesce();\n}\nexport function getHoversPromise(registry, model, position, token, recursive = false) {\n    return getHoverProviderResultsAsAsyncIterable(registry, model, position, token, recursive).map(item => item.hover).toPromise();\n}\nregisterModelAndPositionCommand('_executeHoverProvider', (accessor, model, position) => {\n    const languageFeaturesService = accessor.get(ILanguageFeaturesService);\n    return getHoversPromise(languageFeaturesService.hoverProvider, model, position, CancellationToken.None);\n});\nregisterModelAndPositionCommand('_executeHoverProvider_recursive', (accessor, model, position) => {\n    const languageFeaturesService = accessor.get(ILanguageFeaturesService);\n    return getHoversPromise(languageFeaturesService.hoverProvider, model, position, CancellationToken.None, true);\n});\nfunction isValid(result) {\n    const hasRange = (typeof result.range !== 'undefined');\n    const hasHtmlContent = typeof result.contents !== 'undefined' && result.contents && result.contents.length > 0;\n    return hasRange && hasHtmlContent;\n}\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA,SAASA,mBAAmB,QAAQ,kCAAkC;AACtE,SAASC,iBAAiB,QAAQ,yCAAyC;AAC3E,SAASC,yBAAyB,QAAQ,mCAAmC;AAC7E,SAASC,+BAA+B,QAAQ,sCAAsC;AACtF,SAASC,wBAAwB,QAAQ,8CAA8C;AACvF,OAAO,MAAMC,mBAAmB,CAAC;EAC7BC,WAAWA,CAACC,QAAQ,EAAEC,KAAK,EAAEC,OAAO,EAAE;IAClC,IAAI,CAACF,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,OAAO,GAAGA,OAAO;EAC1B;AACJ;AACA;AACA;AACA;AAFA,SAGeC,eAAeA,CAAAC,EAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;EAAA,OAAAC,gBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAF,iBAAA;EAAAA,gBAAA,GAAAG,iBAAA,CAA9B,WAA+BZ,QAAQ,EAAEE,OAAO,EAAEW,KAAK,EAAEC,QAAQ,EAAEC,KAAK,EAAE;IACtE,MAAMC,MAAM,SAASC,OAAO,CACvBC,OAAO,CAAClB,QAAQ,CAACmB,YAAY,CAACN,KAAK,EAAEC,QAAQ,EAAEC,KAAK,CAAC,CAAC,CACtDK,KAAK,CAACzB,yBAAyB,CAAC;IACrC,IAAI,CAACqB,MAAM,IAAI,CAACK,OAAO,CAACL,MAAM,CAAC,EAAE;MAC7B,OAAOM,SAAS;IACpB;IACA,OAAO,IAAIxB,mBAAmB,CAACE,QAAQ,EAAEgB,MAAM,EAAEd,OAAO,CAAC;EAC7D,CAAC;EAAA,OAAAO,gBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AACD,OAAO,SAASY,sCAAsCA,CAACC,QAAQ,EAAEX,KAAK,EAAEC,QAAQ,EAAEC,KAAK,EAAEU,SAAS,GAAG,KAAK,EAAE;EACxG,MAAMC,SAAS,GAAGF,QAAQ,CAACG,OAAO,CAACd,KAAK,EAAEY,SAAS,CAAC;EACpD,MAAMG,QAAQ,GAAGF,SAAS,CAACG,GAAG,CAAC,CAAC7B,QAAQ,EAAE8B,KAAK,KAAK3B,eAAe,CAACH,QAAQ,EAAE8B,KAAK,EAAEjB,KAAK,EAAEC,QAAQ,EAAEC,KAAK,CAAC,CAAC;EAC7G,OAAOtB,mBAAmB,CAACsC,YAAY,CAACH,QAAQ,CAAC,CAACI,QAAQ,CAAC,CAAC;AAChE;AACA,OAAO,SAASC,gBAAgBA,CAACT,QAAQ,EAAEX,KAAK,EAAEC,QAAQ,EAAEC,KAAK,EAAEU,SAAS,GAAG,KAAK,EAAE;EAClF,OAAOF,sCAAsC,CAACC,QAAQ,EAAEX,KAAK,EAAEC,QAAQ,EAAEC,KAAK,EAAEU,SAAS,CAAC,CAACI,GAAG,CAACK,IAAI,IAAIA,IAAI,CAACjC,KAAK,CAAC,CAACkC,SAAS,CAAC,CAAC;AAClI;AACAvC,+BAA+B,CAAC,uBAAuB,EAAE,CAACwC,QAAQ,EAAEvB,KAAK,EAAEC,QAAQ,KAAK;EACpF,MAAMuB,uBAAuB,GAAGD,QAAQ,CAACE,GAAG,CAACzC,wBAAwB,CAAC;EACtE,OAAOoC,gBAAgB,CAACI,uBAAuB,CAACE,aAAa,EAAE1B,KAAK,EAAEC,QAAQ,EAAEpB,iBAAiB,CAAC8C,IAAI,CAAC;AAC3G,CAAC,CAAC;AACF5C,+BAA+B,CAAC,iCAAiC,EAAE,CAACwC,QAAQ,EAAEvB,KAAK,EAAEC,QAAQ,KAAK;EAC9F,MAAMuB,uBAAuB,GAAGD,QAAQ,CAACE,GAAG,CAACzC,wBAAwB,CAAC;EACtE,OAAOoC,gBAAgB,CAACI,uBAAuB,CAACE,aAAa,EAAE1B,KAAK,EAAEC,QAAQ,EAAEpB,iBAAiB,CAAC8C,IAAI,EAAE,IAAI,CAAC;AACjH,CAAC,CAAC;AACF,SAASnB,OAAOA,CAACL,MAAM,EAAE;EACrB,MAAMyB,QAAQ,GAAI,OAAOzB,MAAM,CAAC0B,KAAK,KAAK,WAAY;EACtD,MAAMC,cAAc,GAAG,OAAO3B,MAAM,CAAC4B,QAAQ,KAAK,WAAW,IAAI5B,MAAM,CAAC4B,QAAQ,IAAI5B,MAAM,CAAC4B,QAAQ,CAACC,MAAM,GAAG,CAAC;EAC9G,OAAOJ,QAAQ,IAAIE,cAAc;AACrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}