{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { $, append, clearNode, createStyleSheet } from '../../dom.js';\nimport { getBaseLayerHoverDelegate } from '../hover/hoverDelegate2.js';\nimport { getDefaultHoverDelegate } from '../hover/hoverDelegateFactory.js';\nimport { List, unthemedListStyles } from '../list/listWidget.js';\nimport { SplitView } from '../splitview/splitview.js';\nimport { Emitter, Event } from '../../../common/event.js';\nimport { Disposable, DisposableStore } from '../../../common/lifecycle.js';\nimport './table.css';\nlet TableListRenderer = /*#__PURE__*/(() => {\n  class TableListRenderer {\n    static {\n      this.TemplateId = 'row';\n    }\n    constructor(columns, renderers, getColumnSize) {\n      this.columns = columns;\n      this.getColumnSize = getColumnSize;\n      this.templateId = TableListRenderer.TemplateId;\n      this.renderedTemplates = new Set();\n      const rendererMap = new Map(renderers.map(r => [r.templateId, r]));\n      this.renderers = [];\n      for (const column of columns) {\n        const renderer = rendererMap.get(column.templateId);\n        if (!renderer) {\n          throw new Error(`Table cell renderer for template id ${column.templateId} not found.`);\n        }\n        this.renderers.push(renderer);\n      }\n    }\n    renderTemplate(container) {\n      const rowContainer = append(container, $('.monaco-table-tr'));\n      const cellContainers = [];\n      const cellTemplateData = [];\n      for (let i = 0; i < this.columns.length; i++) {\n        const renderer = this.renderers[i];\n        const cellContainer = append(rowContainer, $('.monaco-table-td', {\n          'data-col-index': i\n        }));\n        cellContainer.style.width = `${this.getColumnSize(i)}px`;\n        cellContainers.push(cellContainer);\n        cellTemplateData.push(renderer.renderTemplate(cellContainer));\n      }\n      const result = {\n        container,\n        cellContainers,\n        cellTemplateData\n      };\n      this.renderedTemplates.add(result);\n      return result;\n    }\n    renderElement(element, index, templateData, height) {\n      for (let i = 0; i < this.columns.length; i++) {\n        const column = this.columns[i];\n        const cell = column.project(element);\n        const renderer = this.renderers[i];\n        renderer.renderElement(cell, index, templateData.cellTemplateData[i], height);\n      }\n    }\n    disposeElement(element, index, templateData, height) {\n      for (let i = 0; i < this.columns.length; i++) {\n        const renderer = this.renderers[i];\n        if (renderer.disposeElement) {\n          const column = this.columns[i];\n          const cell = column.project(element);\n          renderer.disposeElement(cell, index, templateData.cellTemplateData[i], height);\n        }\n      }\n    }\n    disposeTemplate(templateData) {\n      for (let i = 0; i < this.columns.length; i++) {\n        const renderer = this.renderers[i];\n        renderer.disposeTemplate(templateData.cellTemplateData[i]);\n      }\n      clearNode(templateData.container);\n      this.renderedTemplates.delete(templateData);\n    }\n    layoutColumn(index, size) {\n      for (const {\n        cellContainers\n      } of this.renderedTemplates) {\n        cellContainers[index].style.width = `${size}px`;\n      }\n    }\n  }\n  return TableListRenderer;\n})();\nfunction asListVirtualDelegate(delegate) {\n  return {\n    getHeight(row) {\n      return delegate.getHeight(row);\n    },\n    getTemplateId() {\n      return TableListRenderer.TemplateId;\n    }\n  };\n}\nclass ColumnHeader extends Disposable {\n  get minimumSize() {\n    return this.column.minimumWidth ?? 120;\n  }\n  get maximumSize() {\n    return this.column.maximumWidth ?? Number.POSITIVE_INFINITY;\n  }\n  get onDidChange() {\n    return this.column.onDidChangeWidthConstraints ?? Event.None;\n  }\n  constructor(column, index) {\n    super();\n    this.column = column;\n    this.index = index;\n    this._onDidLayout = new Emitter();\n    this.onDidLayout = this._onDidLayout.event;\n    this.element = $('.monaco-table-th', {\n      'data-col-index': index\n    }, column.label);\n    if (column.tooltip) {\n      this._register(getBaseLayerHoverDelegate().setupManagedHover(getDefaultHoverDelegate('mouse'), this.element, column.tooltip));\n    }\n  }\n  layout(size) {\n    this._onDidLayout.fire([this.index, size]);\n  }\n}\nexport let Table = /*#__PURE__*/(() => {\n  class Table {\n    static {\n      this.InstanceCount = 0;\n    }\n    get onDidChangeFocus() {\n      return this.list.onDidChangeFocus;\n    }\n    get onDidChangeSelection() {\n      return this.list.onDidChangeSelection;\n    }\n    get onDidScroll() {\n      return this.list.onDidScroll;\n    }\n    get onMouseDblClick() {\n      return this.list.onMouseDblClick;\n    }\n    get onPointer() {\n      return this.list.onPointer;\n    }\n    get onDidFocus() {\n      return this.list.onDidFocus;\n    }\n    get scrollTop() {\n      return this.list.scrollTop;\n    }\n    set scrollTop(scrollTop) {\n      this.list.scrollTop = scrollTop;\n    }\n    get scrollHeight() {\n      return this.list.scrollHeight;\n    }\n    get renderHeight() {\n      return this.list.renderHeight;\n    }\n    get onDidDispose() {\n      return this.list.onDidDispose;\n    }\n    constructor(user, container, virtualDelegate, columns, renderers, _options) {\n      this.virtualDelegate = virtualDelegate;\n      this.columns = columns;\n      this.domId = `table_id_${++Table.InstanceCount}`;\n      this.disposables = new DisposableStore();\n      this.cachedWidth = 0;\n      this.cachedHeight = 0;\n      this.domNode = append(container, $(`.monaco-table.${this.domId}`));\n      const headers = columns.map((c, i) => this.disposables.add(new ColumnHeader(c, i)));\n      const descriptor = {\n        size: headers.reduce((a, b) => a + b.column.weight, 0),\n        views: headers.map(view => ({\n          size: view.column.weight,\n          view\n        }))\n      };\n      this.splitview = this.disposables.add(new SplitView(this.domNode, {\n        orientation: 1 /* Orientation.HORIZONTAL */,\n        scrollbarVisibility: 2 /* ScrollbarVisibility.Hidden */,\n        getSashOrthogonalSize: () => this.cachedHeight,\n        descriptor\n      }));\n      this.splitview.el.style.height = `${virtualDelegate.headerRowHeight}px`;\n      this.splitview.el.style.lineHeight = `${virtualDelegate.headerRowHeight}px`;\n      const renderer = new TableListRenderer(columns, renderers, i => this.splitview.getViewSize(i));\n      this.list = this.disposables.add(new List(user, this.domNode, asListVirtualDelegate(virtualDelegate), [renderer], _options));\n      Event.any(...headers.map(h => h.onDidLayout))(([index, size]) => renderer.layoutColumn(index, size), null, this.disposables);\n      this.splitview.onDidSashReset(index => {\n        const totalWeight = columns.reduce((r, c) => r + c.weight, 0);\n        const size = columns[index].weight / totalWeight * this.cachedWidth;\n        this.splitview.resizeView(index, size);\n      }, null, this.disposables);\n      this.styleElement = createStyleSheet(this.domNode);\n      this.style(unthemedListStyles);\n    }\n    updateOptions(options) {\n      this.list.updateOptions(options);\n    }\n    splice(start, deleteCount, elements = []) {\n      this.list.splice(start, deleteCount, elements);\n    }\n    getHTMLElement() {\n      return this.domNode;\n    }\n    style(styles) {\n      const content = [];\n      content.push(`.monaco-table.${this.domId} > .monaco-split-view2 .monaco-sash.vertical::before {\n\t\t\ttop: ${this.virtualDelegate.headerRowHeight + 1}px;\n\t\t\theight: calc(100% - ${this.virtualDelegate.headerRowHeight}px);\n\t\t}`);\n      this.styleElement.textContent = content.join('\\n');\n      this.list.style(styles);\n    }\n    getSelectedElements() {\n      return this.list.getSelectedElements();\n    }\n    getSelection() {\n      return this.list.getSelection();\n    }\n    getFocus() {\n      return this.list.getFocus();\n    }\n    dispose() {\n      this.disposables.dispose();\n    }\n  }\n  return Table;\n})();", "map": {"version": 3, "names": ["$", "append", "clearNode", "createStyleSheet", "getBaseLayerHoverDelegate", "getDefaultHoverDelegate", "List", "unthemedListStyles", "SplitView", "Emitter", "Event", "Disposable", "DisposableStore", "TableList<PERSON><PERSON><PERSON>", "TemplateId", "constructor", "columns", "renderers", "getColumnSize", "templateId", "renderedTemplates", "Set", "rendererMap", "Map", "map", "r", "column", "renderer", "get", "Error", "push", "renderTemplate", "container", "rowC<PERSON>r", "cellContainers", "cellTemplateData", "i", "length", "cellContainer", "style", "width", "result", "add", "renderElement", "element", "index", "templateData", "height", "cell", "project", "disposeElement", "disposeTemplate", "delete", "layoutColumn", "size", "asListVirtualDelegate", "delegate", "getHeight", "row", "getTemplateId", "<PERSON>umn<PERSON><PERSON><PERSON>", "minimumSize", "minimumWidth", "maximumSize", "maximumWidth", "Number", "POSITIVE_INFINITY", "onDidChange", "onDidChangeWidthConstraints", "None", "_onDidLayout", "onDidLayout", "event", "label", "tooltip", "_register", "setupManagedHover", "layout", "fire", "Table", "InstanceCount", "onDidChangeFocus", "list", "onDidChangeSelection", "onDidScroll", "onMouseDblClick", "onPointer", "onDidFocus", "scrollTop", "scrollHeight", "renderHeight", "onDidDispose", "user", "virtualDelegate", "_options", "domId", "disposables", "cachedWidth", "cachedHeight", "domNode", "headers", "c", "descriptor", "reduce", "a", "b", "weight", "views", "view", "splitview", "orientation", "scrollbarVisibility", "getSashOrthogonalSize", "el", "headerRowHeight", "lineHeight", "getViewSize", "any", "h", "onDidSashReset", "totalWeight", "resizeView", "styleElement", "updateOptions", "options", "splice", "start", "deleteCount", "elements", "getHTMLElement", "styles", "content", "textContent", "join", "getSelectedElements", "getSelection", "getFocus", "dispose"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/browser/ui/table/tableWidget.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { $, append, clearNode, createStyleSheet } from '../../dom.js';\nimport { getBaseLayerHoverDelegate } from '../hover/hoverDelegate2.js';\nimport { getDefaultHoverDelegate } from '../hover/hoverDelegateFactory.js';\nimport { List, unthemedListStyles } from '../list/listWidget.js';\nimport { SplitView } from '../splitview/splitview.js';\nimport { Emitter, Event } from '../../../common/event.js';\nimport { Disposable, DisposableStore } from '../../../common/lifecycle.js';\nimport './table.css';\nclass TableListRenderer {\n    static { this.TemplateId = 'row'; }\n    constructor(columns, renderers, getColumnSize) {\n        this.columns = columns;\n        this.getColumnSize = getColumnSize;\n        this.templateId = TableListRenderer.TemplateId;\n        this.renderedTemplates = new Set();\n        const rendererMap = new Map(renderers.map(r => [r.templateId, r]));\n        this.renderers = [];\n        for (const column of columns) {\n            const renderer = rendererMap.get(column.templateId);\n            if (!renderer) {\n                throw new Error(`Table cell renderer for template id ${column.templateId} not found.`);\n            }\n            this.renderers.push(renderer);\n        }\n    }\n    renderTemplate(container) {\n        const rowContainer = append(container, $('.monaco-table-tr'));\n        const cellContainers = [];\n        const cellTemplateData = [];\n        for (let i = 0; i < this.columns.length; i++) {\n            const renderer = this.renderers[i];\n            const cellContainer = append(rowContainer, $('.monaco-table-td', { 'data-col-index': i }));\n            cellContainer.style.width = `${this.getColumnSize(i)}px`;\n            cellContainers.push(cellContainer);\n            cellTemplateData.push(renderer.renderTemplate(cellContainer));\n        }\n        const result = { container, cellContainers, cellTemplateData };\n        this.renderedTemplates.add(result);\n        return result;\n    }\n    renderElement(element, index, templateData, height) {\n        for (let i = 0; i < this.columns.length; i++) {\n            const column = this.columns[i];\n            const cell = column.project(element);\n            const renderer = this.renderers[i];\n            renderer.renderElement(cell, index, templateData.cellTemplateData[i], height);\n        }\n    }\n    disposeElement(element, index, templateData, height) {\n        for (let i = 0; i < this.columns.length; i++) {\n            const renderer = this.renderers[i];\n            if (renderer.disposeElement) {\n                const column = this.columns[i];\n                const cell = column.project(element);\n                renderer.disposeElement(cell, index, templateData.cellTemplateData[i], height);\n            }\n        }\n    }\n    disposeTemplate(templateData) {\n        for (let i = 0; i < this.columns.length; i++) {\n            const renderer = this.renderers[i];\n            renderer.disposeTemplate(templateData.cellTemplateData[i]);\n        }\n        clearNode(templateData.container);\n        this.renderedTemplates.delete(templateData);\n    }\n    layoutColumn(index, size) {\n        for (const { cellContainers } of this.renderedTemplates) {\n            cellContainers[index].style.width = `${size}px`;\n        }\n    }\n}\nfunction asListVirtualDelegate(delegate) {\n    return {\n        getHeight(row) { return delegate.getHeight(row); },\n        getTemplateId() { return TableListRenderer.TemplateId; },\n    };\n}\nclass ColumnHeader extends Disposable {\n    get minimumSize() { return this.column.minimumWidth ?? 120; }\n    get maximumSize() { return this.column.maximumWidth ?? Number.POSITIVE_INFINITY; }\n    get onDidChange() { return this.column.onDidChangeWidthConstraints ?? Event.None; }\n    constructor(column, index) {\n        super();\n        this.column = column;\n        this.index = index;\n        this._onDidLayout = new Emitter();\n        this.onDidLayout = this._onDidLayout.event;\n        this.element = $('.monaco-table-th', { 'data-col-index': index }, column.label);\n        if (column.tooltip) {\n            this._register(getBaseLayerHoverDelegate().setupManagedHover(getDefaultHoverDelegate('mouse'), this.element, column.tooltip));\n        }\n    }\n    layout(size) {\n        this._onDidLayout.fire([this.index, size]);\n    }\n}\nexport class Table {\n    static { this.InstanceCount = 0; }\n    get onDidChangeFocus() { return this.list.onDidChangeFocus; }\n    get onDidChangeSelection() { return this.list.onDidChangeSelection; }\n    get onDidScroll() { return this.list.onDidScroll; }\n    get onMouseDblClick() { return this.list.onMouseDblClick; }\n    get onPointer() { return this.list.onPointer; }\n    get onDidFocus() { return this.list.onDidFocus; }\n    get scrollTop() { return this.list.scrollTop; }\n    set scrollTop(scrollTop) { this.list.scrollTop = scrollTop; }\n    get scrollHeight() { return this.list.scrollHeight; }\n    get renderHeight() { return this.list.renderHeight; }\n    get onDidDispose() { return this.list.onDidDispose; }\n    constructor(user, container, virtualDelegate, columns, renderers, _options) {\n        this.virtualDelegate = virtualDelegate;\n        this.columns = columns;\n        this.domId = `table_id_${++Table.InstanceCount}`;\n        this.disposables = new DisposableStore();\n        this.cachedWidth = 0;\n        this.cachedHeight = 0;\n        this.domNode = append(container, $(`.monaco-table.${this.domId}`));\n        const headers = columns.map((c, i) => this.disposables.add(new ColumnHeader(c, i)));\n        const descriptor = {\n            size: headers.reduce((a, b) => a + b.column.weight, 0),\n            views: headers.map(view => ({ size: view.column.weight, view }))\n        };\n        this.splitview = this.disposables.add(new SplitView(this.domNode, {\n            orientation: 1 /* Orientation.HORIZONTAL */,\n            scrollbarVisibility: 2 /* ScrollbarVisibility.Hidden */,\n            getSashOrthogonalSize: () => this.cachedHeight,\n            descriptor\n        }));\n        this.splitview.el.style.height = `${virtualDelegate.headerRowHeight}px`;\n        this.splitview.el.style.lineHeight = `${virtualDelegate.headerRowHeight}px`;\n        const renderer = new TableListRenderer(columns, renderers, i => this.splitview.getViewSize(i));\n        this.list = this.disposables.add(new List(user, this.domNode, asListVirtualDelegate(virtualDelegate), [renderer], _options));\n        Event.any(...headers.map(h => h.onDidLayout))(([index, size]) => renderer.layoutColumn(index, size), null, this.disposables);\n        this.splitview.onDidSashReset(index => {\n            const totalWeight = columns.reduce((r, c) => r + c.weight, 0);\n            const size = columns[index].weight / totalWeight * this.cachedWidth;\n            this.splitview.resizeView(index, size);\n        }, null, this.disposables);\n        this.styleElement = createStyleSheet(this.domNode);\n        this.style(unthemedListStyles);\n    }\n    updateOptions(options) {\n        this.list.updateOptions(options);\n    }\n    splice(start, deleteCount, elements = []) {\n        this.list.splice(start, deleteCount, elements);\n    }\n    getHTMLElement() {\n        return this.domNode;\n    }\n    style(styles) {\n        const content = [];\n        content.push(`.monaco-table.${this.domId} > .monaco-split-view2 .monaco-sash.vertical::before {\n\t\t\ttop: ${this.virtualDelegate.headerRowHeight + 1}px;\n\t\t\theight: calc(100% - ${this.virtualDelegate.headerRowHeight}px);\n\t\t}`);\n        this.styleElement.textContent = content.join('\\n');\n        this.list.style(styles);\n    }\n    getSelectedElements() {\n        return this.list.getSelectedElements();\n    }\n    getSelection() {\n        return this.list.getSelection();\n    }\n    getFocus() {\n        return this.list.getFocus();\n    }\n    dispose() {\n        this.disposables.dispose();\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,CAAC,EAAEC,MAAM,EAAEC,SAAS,EAAEC,gBAAgB,QAAQ,cAAc;AACrE,SAASC,yBAAyB,QAAQ,4BAA4B;AACtE,SAASC,uBAAuB,QAAQ,kCAAkC;AAC1E,SAASC,IAAI,EAAEC,kBAAkB,QAAQ,uBAAuB;AAChE,SAASC,SAAS,QAAQ,2BAA2B;AACrD,SAASC,OAAO,EAAEC,KAAK,QAAQ,0BAA0B;AACzD,SAASC,UAAU,EAAEC,eAAe,QAAQ,8BAA8B;AAC1E,OAAO,aAAa;AAAC,IACfC,iBAAiB;EAAvB,MAAMA,iBAAiB,CAAC;IACpB;MAAS,IAAI,CAACC,UAAU,GAAG,KAAK;IAAE;IAClCC,WAAWA,CAACC,OAAO,EAAEC,SAAS,EAAEC,aAAa,EAAE;MAC3C,IAAI,CAACF,OAAO,GAAGA,OAAO;MACtB,IAAI,CAACE,aAAa,GAAGA,aAAa;MAClC,IAAI,CAACC,UAAU,GAAGN,iBAAiB,CAACC,UAAU;MAC9C,IAAI,CAACM,iBAAiB,GAAG,IAAIC,GAAG,CAAC,CAAC;MAClC,MAAMC,WAAW,GAAG,IAAIC,GAAG,CAACN,SAAS,CAACO,GAAG,CAACC,CAAC,IAAI,CAACA,CAAC,CAACN,UAAU,EAAEM,CAAC,CAAC,CAAC,CAAC;MAClE,IAAI,CAACR,SAAS,GAAG,EAAE;MACnB,KAAK,MAAMS,MAAM,IAAIV,OAAO,EAAE;QAC1B,MAAMW,QAAQ,GAAGL,WAAW,CAACM,GAAG,CAACF,MAAM,CAACP,UAAU,CAAC;QACnD,IAAI,CAACQ,QAAQ,EAAE;UACX,MAAM,IAAIE,KAAK,CAAC,uCAAuCH,MAAM,CAACP,UAAU,aAAa,CAAC;QAC1F;QACA,IAAI,CAACF,SAAS,CAACa,IAAI,CAACH,QAAQ,CAAC;MACjC;IACJ;IACAI,cAAcA,CAACC,SAAS,EAAE;MACtB,MAAMC,YAAY,GAAGhC,MAAM,CAAC+B,SAAS,EAAEhC,CAAC,CAAC,kBAAkB,CAAC,CAAC;MAC7D,MAAMkC,cAAc,GAAG,EAAE;MACzB,MAAMC,gBAAgB,GAAG,EAAE;MAC3B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACpB,OAAO,CAACqB,MAAM,EAAED,CAAC,EAAE,EAAE;QAC1C,MAAMT,QAAQ,GAAG,IAAI,CAACV,SAAS,CAACmB,CAAC,CAAC;QAClC,MAAME,aAAa,GAAGrC,MAAM,CAACgC,YAAY,EAAEjC,CAAC,CAAC,kBAAkB,EAAE;UAAE,gBAAgB,EAAEoC;QAAE,CAAC,CAAC,CAAC;QAC1FE,aAAa,CAACC,KAAK,CAACC,KAAK,GAAG,GAAG,IAAI,CAACtB,aAAa,CAACkB,CAAC,CAAC,IAAI;QACxDF,cAAc,CAACJ,IAAI,CAACQ,aAAa,CAAC;QAClCH,gBAAgB,CAACL,IAAI,CAACH,QAAQ,CAACI,cAAc,CAACO,aAAa,CAAC,CAAC;MACjE;MACA,MAAMG,MAAM,GAAG;QAAET,SAAS;QAAEE,cAAc;QAAEC;MAAiB,CAAC;MAC9D,IAAI,CAACf,iBAAiB,CAACsB,GAAG,CAACD,MAAM,CAAC;MAClC,OAAOA,MAAM;IACjB;IACAE,aAAaA,CAACC,OAAO,EAAEC,KAAK,EAAEC,YAAY,EAAEC,MAAM,EAAE;MAChD,KAAK,IAAIX,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACpB,OAAO,CAACqB,MAAM,EAAED,CAAC,EAAE,EAAE;QAC1C,MAAMV,MAAM,GAAG,IAAI,CAACV,OAAO,CAACoB,CAAC,CAAC;QAC9B,MAAMY,IAAI,GAAGtB,MAAM,CAACuB,OAAO,CAACL,OAAO,CAAC;QACpC,MAAMjB,QAAQ,GAAG,IAAI,CAACV,SAAS,CAACmB,CAAC,CAAC;QAClCT,QAAQ,CAACgB,aAAa,CAACK,IAAI,EAAEH,KAAK,EAAEC,YAAY,CAACX,gBAAgB,CAACC,CAAC,CAAC,EAAEW,MAAM,CAAC;MACjF;IACJ;IACAG,cAAcA,CAACN,OAAO,EAAEC,KAAK,EAAEC,YAAY,EAAEC,MAAM,EAAE;MACjD,KAAK,IAAIX,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACpB,OAAO,CAACqB,MAAM,EAAED,CAAC,EAAE,EAAE;QAC1C,MAAMT,QAAQ,GAAG,IAAI,CAACV,SAAS,CAACmB,CAAC,CAAC;QAClC,IAAIT,QAAQ,CAACuB,cAAc,EAAE;UACzB,MAAMxB,MAAM,GAAG,IAAI,CAACV,OAAO,CAACoB,CAAC,CAAC;UAC9B,MAAMY,IAAI,GAAGtB,MAAM,CAACuB,OAAO,CAACL,OAAO,CAAC;UACpCjB,QAAQ,CAACuB,cAAc,CAACF,IAAI,EAAEH,KAAK,EAAEC,YAAY,CAACX,gBAAgB,CAACC,CAAC,CAAC,EAAEW,MAAM,CAAC;QAClF;MACJ;IACJ;IACAI,eAAeA,CAACL,YAAY,EAAE;MAC1B,KAAK,IAAIV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACpB,OAAO,CAACqB,MAAM,EAAED,CAAC,EAAE,EAAE;QAC1C,MAAMT,QAAQ,GAAG,IAAI,CAACV,SAAS,CAACmB,CAAC,CAAC;QAClCT,QAAQ,CAACwB,eAAe,CAACL,YAAY,CAACX,gBAAgB,CAACC,CAAC,CAAC,CAAC;MAC9D;MACAlC,SAAS,CAAC4C,YAAY,CAACd,SAAS,CAAC;MACjC,IAAI,CAACZ,iBAAiB,CAACgC,MAAM,CAACN,YAAY,CAAC;IAC/C;IACAO,YAAYA,CAACR,KAAK,EAAES,IAAI,EAAE;MACtB,KAAK,MAAM;QAAEpB;MAAe,CAAC,IAAI,IAAI,CAACd,iBAAiB,EAAE;QACrDc,cAAc,CAACW,KAAK,CAAC,CAACN,KAAK,CAACC,KAAK,GAAG,GAAGc,IAAI,IAAI;MACnD;IACJ;EACJ;EAAC,OA/DKzC,iBAAiB;AAAA;AAgEvB,SAAS0C,qBAAqBA,CAACC,QAAQ,EAAE;EACrC,OAAO;IACHC,SAASA,CAACC,GAAG,EAAE;MAAE,OAAOF,QAAQ,CAACC,SAAS,CAACC,GAAG,CAAC;IAAE,CAAC;IAClDC,aAAaA,CAAA,EAAG;MAAE,OAAO9C,iBAAiB,CAACC,UAAU;IAAE;EAC3D,CAAC;AACL;AACA,MAAM8C,YAAY,SAASjD,UAAU,CAAC;EAClC,IAAIkD,WAAWA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACnC,MAAM,CAACoC,YAAY,IAAI,GAAG;EAAE;EAC5D,IAAIC,WAAWA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACrC,MAAM,CAACsC,YAAY,IAAIC,MAAM,CAACC,iBAAiB;EAAE;EACjF,IAAIC,WAAWA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACzC,MAAM,CAAC0C,2BAA2B,IAAI1D,KAAK,CAAC2D,IAAI;EAAE;EAClFtD,WAAWA,CAACW,MAAM,EAAEmB,KAAK,EAAE;IACvB,KAAK,CAAC,CAAC;IACP,IAAI,CAACnB,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACmB,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACyB,YAAY,GAAG,IAAI7D,OAAO,CAAC,CAAC;IACjC,IAAI,CAAC8D,WAAW,GAAG,IAAI,CAACD,YAAY,CAACE,KAAK;IAC1C,IAAI,CAAC5B,OAAO,GAAG5C,CAAC,CAAC,kBAAkB,EAAE;MAAE,gBAAgB,EAAE6C;IAAM,CAAC,EAAEnB,MAAM,CAAC+C,KAAK,CAAC;IAC/E,IAAI/C,MAAM,CAACgD,OAAO,EAAE;MAChB,IAAI,CAACC,SAAS,CAACvE,yBAAyB,CAAC,CAAC,CAACwE,iBAAiB,CAACvE,uBAAuB,CAAC,OAAO,CAAC,EAAE,IAAI,CAACuC,OAAO,EAAElB,MAAM,CAACgD,OAAO,CAAC,CAAC;IACjI;EACJ;EACAG,MAAMA,CAACvB,IAAI,EAAE;IACT,IAAI,CAACgB,YAAY,CAACQ,IAAI,CAAC,CAAC,IAAI,CAACjC,KAAK,EAAES,IAAI,CAAC,CAAC;EAC9C;AACJ;AACA,WAAayB,KAAK;EAAX,MAAMA,KAAK,CAAC;IACf;MAAS,IAAI,CAACC,aAAa,GAAG,CAAC;IAAE;IACjC,IAAIC,gBAAgBA,CAAA,EAAG;MAAE,OAAO,IAAI,CAACC,IAAI,CAACD,gBAAgB;IAAE;IAC5D,IAAIE,oBAAoBA,CAAA,EAAG;MAAE,OAAO,IAAI,CAACD,IAAI,CAACC,oBAAoB;IAAE;IACpE,IAAIC,WAAWA,CAAA,EAAG;MAAE,OAAO,IAAI,CAACF,IAAI,CAACE,WAAW;IAAE;IAClD,IAAIC,eAAeA,CAAA,EAAG;MAAE,OAAO,IAAI,CAACH,IAAI,CAACG,eAAe;IAAE;IAC1D,IAAIC,SAASA,CAAA,EAAG;MAAE,OAAO,IAAI,CAACJ,IAAI,CAACI,SAAS;IAAE;IAC9C,IAAIC,UAAUA,CAAA,EAAG;MAAE,OAAO,IAAI,CAACL,IAAI,CAACK,UAAU;IAAE;IAChD,IAAIC,SAASA,CAAA,EAAG;MAAE,OAAO,IAAI,CAACN,IAAI,CAACM,SAAS;IAAE;IAC9C,IAAIA,SAASA,CAACA,SAAS,EAAE;MAAE,IAAI,CAACN,IAAI,CAACM,SAAS,GAAGA,SAAS;IAAE;IAC5D,IAAIC,YAAYA,CAAA,EAAG;MAAE,OAAO,IAAI,CAACP,IAAI,CAACO,YAAY;IAAE;IACpD,IAAIC,YAAYA,CAAA,EAAG;MAAE,OAAO,IAAI,CAACR,IAAI,CAACQ,YAAY;IAAE;IACpD,IAAIC,YAAYA,CAAA,EAAG;MAAE,OAAO,IAAI,CAACT,IAAI,CAACS,YAAY;IAAE;IACpD5E,WAAWA,CAAC6E,IAAI,EAAE5D,SAAS,EAAE6D,eAAe,EAAE7E,OAAO,EAAEC,SAAS,EAAE6E,QAAQ,EAAE;MACxE,IAAI,CAACD,eAAe,GAAGA,eAAe;MACtC,IAAI,CAAC7E,OAAO,GAAGA,OAAO;MACtB,IAAI,CAAC+E,KAAK,GAAG,YAAY,EAAEhB,KAAK,CAACC,aAAa,EAAE;MAChD,IAAI,CAACgB,WAAW,GAAG,IAAIpF,eAAe,CAAC,CAAC;MACxC,IAAI,CAACqF,WAAW,GAAG,CAAC;MACpB,IAAI,CAACC,YAAY,GAAG,CAAC;MACrB,IAAI,CAACC,OAAO,GAAGlG,MAAM,CAAC+B,SAAS,EAAEhC,CAAC,CAAC,iBAAiB,IAAI,CAAC+F,KAAK,EAAE,CAAC,CAAC;MAClE,MAAMK,OAAO,GAAGpF,OAAO,CAACQ,GAAG,CAAC,CAAC6E,CAAC,EAAEjE,CAAC,KAAK,IAAI,CAAC4D,WAAW,CAACtD,GAAG,CAAC,IAAIkB,YAAY,CAACyC,CAAC,EAAEjE,CAAC,CAAC,CAAC,CAAC;MACnF,MAAMkE,UAAU,GAAG;QACfhD,IAAI,EAAE8C,OAAO,CAACG,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC/E,MAAM,CAACgF,MAAM,EAAE,CAAC,CAAC;QACtDC,KAAK,EAAEP,OAAO,CAAC5E,GAAG,CAACoF,IAAI,KAAK;UAAEtD,IAAI,EAAEsD,IAAI,CAAClF,MAAM,CAACgF,MAAM;UAAEE;QAAK,CAAC,CAAC;MACnE,CAAC;MACD,IAAI,CAACC,SAAS,GAAG,IAAI,CAACb,WAAW,CAACtD,GAAG,CAAC,IAAIlC,SAAS,CAAC,IAAI,CAAC2F,OAAO,EAAE;QAC9DW,WAAW,EAAE,CAAC,CAAC;QACfC,mBAAmB,EAAE,CAAC,CAAC;QACvBC,qBAAqB,EAAEA,CAAA,KAAM,IAAI,CAACd,YAAY;QAC9CI;MACJ,CAAC,CAAC,CAAC;MACH,IAAI,CAACO,SAAS,CAACI,EAAE,CAAC1E,KAAK,CAACQ,MAAM,GAAG,GAAG8C,eAAe,CAACqB,eAAe,IAAI;MACvE,IAAI,CAACL,SAAS,CAACI,EAAE,CAAC1E,KAAK,CAAC4E,UAAU,GAAG,GAAGtB,eAAe,CAACqB,eAAe,IAAI;MAC3E,MAAMvF,QAAQ,GAAG,IAAId,iBAAiB,CAACG,OAAO,EAAEC,SAAS,EAAEmB,CAAC,IAAI,IAAI,CAACyE,SAAS,CAACO,WAAW,CAAChF,CAAC,CAAC,CAAC;MAC9F,IAAI,CAAC8C,IAAI,GAAG,IAAI,CAACc,WAAW,CAACtD,GAAG,CAAC,IAAIpC,IAAI,CAACsF,IAAI,EAAE,IAAI,CAACO,OAAO,EAAE5C,qBAAqB,CAACsC,eAAe,CAAC,EAAE,CAAClE,QAAQ,CAAC,EAAEmE,QAAQ,CAAC,CAAC;MAC5HpF,KAAK,CAAC2G,GAAG,CAAC,GAAGjB,OAAO,CAAC5E,GAAG,CAAC8F,CAAC,IAAIA,CAAC,CAAC/C,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC1B,KAAK,EAAES,IAAI,CAAC,KAAK3B,QAAQ,CAAC0B,YAAY,CAACR,KAAK,EAAES,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC0C,WAAW,CAAC;MAC5H,IAAI,CAACa,SAAS,CAACU,cAAc,CAAC1E,KAAK,IAAI;QACnC,MAAM2E,WAAW,GAAGxG,OAAO,CAACuF,MAAM,CAAC,CAAC9E,CAAC,EAAE4E,CAAC,KAAK5E,CAAC,GAAG4E,CAAC,CAACK,MAAM,EAAE,CAAC,CAAC;QAC7D,MAAMpD,IAAI,GAAGtC,OAAO,CAAC6B,KAAK,CAAC,CAAC6D,MAAM,GAAGc,WAAW,GAAG,IAAI,CAACvB,WAAW;QACnE,IAAI,CAACY,SAAS,CAACY,UAAU,CAAC5E,KAAK,EAAES,IAAI,CAAC;MAC1C,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC0C,WAAW,CAAC;MAC1B,IAAI,CAAC0B,YAAY,GAAGvH,gBAAgB,CAAC,IAAI,CAACgG,OAAO,CAAC;MAClD,IAAI,CAAC5D,KAAK,CAAChC,kBAAkB,CAAC;IAClC;IACAoH,aAAaA,CAACC,OAAO,EAAE;MACnB,IAAI,CAAC1C,IAAI,CAACyC,aAAa,CAACC,OAAO,CAAC;IACpC;IACAC,MAAMA,CAACC,KAAK,EAAEC,WAAW,EAAEC,QAAQ,GAAG,EAAE,EAAE;MACtC,IAAI,CAAC9C,IAAI,CAAC2C,MAAM,CAACC,KAAK,EAAEC,WAAW,EAAEC,QAAQ,CAAC;IAClD;IACAC,cAAcA,CAAA,EAAG;MACb,OAAO,IAAI,CAAC9B,OAAO;IACvB;IACA5D,KAAKA,CAAC2F,MAAM,EAAE;MACV,MAAMC,OAAO,GAAG,EAAE;MAClBA,OAAO,CAACrG,IAAI,CAAC,iBAAiB,IAAI,CAACiE,KAAK;AAChD,UAAU,IAAI,CAACF,eAAe,CAACqB,eAAe,GAAG,CAAC;AAClD,yBAAyB,IAAI,CAACrB,eAAe,CAACqB,eAAe;AAC7D,IAAI,CAAC;MACG,IAAI,CAACQ,YAAY,CAACU,WAAW,GAAGD,OAAO,CAACE,IAAI,CAAC,IAAI,CAAC;MAClD,IAAI,CAACnD,IAAI,CAAC3C,KAAK,CAAC2F,MAAM,CAAC;IAC3B;IACAI,mBAAmBA,CAAA,EAAG;MAClB,OAAO,IAAI,CAACpD,IAAI,CAACoD,mBAAmB,CAAC,CAAC;IAC1C;IACAC,YAAYA,CAAA,EAAG;MACX,OAAO,IAAI,CAACrD,IAAI,CAACqD,YAAY,CAAC,CAAC;IACnC;IACAC,QAAQA,CAAA,EAAG;MACP,OAAO,IAAI,CAACtD,IAAI,CAACsD,QAAQ,CAAC,CAAC;IAC/B;IACAC,OAAOA,CAAA,EAAG;MACN,IAAI,CAACzC,WAAW,CAACyC,OAAO,CAAC,CAAC;IAC9B;EACJ;EAAC,OA3EY1D,KAAK;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}