{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = this && this.__param || function (paramIndex, decorator) {\n  return function (target, key) {\n    decorator(target, key, paramIndex);\n  };\n};\nvar OverviewRulerFeature_1;\nimport { EventType, addDisposableListener, addStandardDisposableListener, h } from '../../../../../base/browser/dom.js';\nimport { createFastDomNode } from '../../../../../base/browser/fastDomNode.js';\nimport { ScrollbarState } from '../../../../../base/browser/ui/scrollbar/scrollbarState.js';\nimport { Disposable } from '../../../../../base/common/lifecycle.js';\nimport { autorun, autorunWithStore, derived, observableFromEvent, observableSignalFromEvent } from '../../../../../base/common/observable.js';\nimport { appendRemoveOnDispose } from '../utils.js';\nimport { Position } from '../../../../common/core/position.js';\nimport { OverviewRulerZone } from '../../../../common/viewModel/overviewZoneManager.js';\nimport { defaultInsertColor, defaultRemoveColor, diffInserted, diffOverviewRulerInserted, diffOverviewRulerRemoved, diffRemoved } from '../../../../../platform/theme/common/colorRegistry.js';\nimport { IThemeService } from '../../../../../platform/theme/common/themeService.js';\nlet OverviewRulerFeature = class OverviewRulerFeature extends Disposable {\n  static {\n    OverviewRulerFeature_1 = this;\n  }\n  static {\n    this.ONE_OVERVIEW_WIDTH = 15;\n  }\n  static {\n    this.ENTIRE_DIFF_OVERVIEW_WIDTH = this.ONE_OVERVIEW_WIDTH * 2;\n  }\n  constructor(_editors, _rootElement, _diffModel, _rootWidth, _rootHeight, _modifiedEditorLayoutInfo, _themeService) {\n    super();\n    this._editors = _editors;\n    this._rootElement = _rootElement;\n    this._diffModel = _diffModel;\n    this._rootWidth = _rootWidth;\n    this._rootHeight = _rootHeight;\n    this._modifiedEditorLayoutInfo = _modifiedEditorLayoutInfo;\n    this._themeService = _themeService;\n    this.width = OverviewRulerFeature_1.ENTIRE_DIFF_OVERVIEW_WIDTH;\n    const currentColorTheme = observableFromEvent(this._themeService.onDidColorThemeChange, () => this._themeService.getColorTheme());\n    const currentColors = derived(reader => {\n      /** @description colors */\n      const theme = currentColorTheme.read(reader);\n      const insertColor = theme.getColor(diffOverviewRulerInserted) || (theme.getColor(diffInserted) || defaultInsertColor).transparent(2);\n      const removeColor = theme.getColor(diffOverviewRulerRemoved) || (theme.getColor(diffRemoved) || defaultRemoveColor).transparent(2);\n      return {\n        insertColor,\n        removeColor\n      };\n    });\n    const viewportDomElement = createFastDomNode(document.createElement('div'));\n    viewportDomElement.setClassName('diffViewport');\n    viewportDomElement.setPosition('absolute');\n    const diffOverviewRoot = h('div.diffOverview', {\n      style: {\n        position: 'absolute',\n        top: '0px',\n        width: OverviewRulerFeature_1.ENTIRE_DIFF_OVERVIEW_WIDTH + 'px'\n      }\n    }).root;\n    this._register(appendRemoveOnDispose(diffOverviewRoot, viewportDomElement.domNode));\n    this._register(addStandardDisposableListener(diffOverviewRoot, EventType.POINTER_DOWN, e => {\n      this._editors.modified.delegateVerticalScrollbarPointerDown(e);\n    }));\n    this._register(addDisposableListener(diffOverviewRoot, EventType.MOUSE_WHEEL, e => {\n      this._editors.modified.delegateScrollFromMouseWheelEvent(e);\n    }, {\n      passive: false\n    }));\n    this._register(appendRemoveOnDispose(this._rootElement, diffOverviewRoot));\n    this._register(autorunWithStore((reader, store) => {\n      /** @description recreate overview rules when model changes */\n      const m = this._diffModel.read(reader);\n      const originalOverviewRuler = this._editors.original.createOverviewRuler('original diffOverviewRuler');\n      if (originalOverviewRuler) {\n        store.add(originalOverviewRuler);\n        store.add(appendRemoveOnDispose(diffOverviewRoot, originalOverviewRuler.getDomNode()));\n      }\n      const modifiedOverviewRuler = this._editors.modified.createOverviewRuler('modified diffOverviewRuler');\n      if (modifiedOverviewRuler) {\n        store.add(modifiedOverviewRuler);\n        store.add(appendRemoveOnDispose(diffOverviewRoot, modifiedOverviewRuler.getDomNode()));\n      }\n      if (!originalOverviewRuler || !modifiedOverviewRuler) {\n        // probably no model\n        return;\n      }\n      const origViewZonesChanged = observableSignalFromEvent('viewZoneChanged', this._editors.original.onDidChangeViewZones);\n      const modViewZonesChanged = observableSignalFromEvent('viewZoneChanged', this._editors.modified.onDidChangeViewZones);\n      const origHiddenRangesChanged = observableSignalFromEvent('hiddenRangesChanged', this._editors.original.onDidChangeHiddenAreas);\n      const modHiddenRangesChanged = observableSignalFromEvent('hiddenRangesChanged', this._editors.modified.onDidChangeHiddenAreas);\n      store.add(autorun(reader => {\n        /** @description set overview ruler zones */\n        origViewZonesChanged.read(reader);\n        modViewZonesChanged.read(reader);\n        origHiddenRangesChanged.read(reader);\n        modHiddenRangesChanged.read(reader);\n        const colors = currentColors.read(reader);\n        const diff = m?.diff.read(reader)?.mappings;\n        function createZones(ranges, color, editor) {\n          const vm = editor._getViewModel();\n          if (!vm) {\n            return [];\n          }\n          return ranges.filter(d => d.length > 0).map(r => {\n            const start = vm.coordinatesConverter.convertModelPositionToViewPosition(new Position(r.startLineNumber, 1));\n            const end = vm.coordinatesConverter.convertModelPositionToViewPosition(new Position(r.endLineNumberExclusive, 1));\n            // By computing the lineCount, we won't ask the view model later for the bottom vertical position.\n            // (The view model will take into account the alignment viewzones, which will give\n            // modifications and deletetions always the same height.)\n            const lineCount = end.lineNumber - start.lineNumber;\n            return new OverviewRulerZone(start.lineNumber, end.lineNumber, lineCount, color.toString());\n          });\n        }\n        const originalZones = createZones((diff || []).map(d => d.lineRangeMapping.original), colors.removeColor, this._editors.original);\n        const modifiedZones = createZones((diff || []).map(d => d.lineRangeMapping.modified), colors.insertColor, this._editors.modified);\n        originalOverviewRuler?.setZones(originalZones);\n        modifiedOverviewRuler?.setZones(modifiedZones);\n      }));\n      store.add(autorun(reader => {\n        /** @description layout overview ruler */\n        const height = this._rootHeight.read(reader);\n        const width = this._rootWidth.read(reader);\n        const layoutInfo = this._modifiedEditorLayoutInfo.read(reader);\n        if (layoutInfo) {\n          const freeSpace = OverviewRulerFeature_1.ENTIRE_DIFF_OVERVIEW_WIDTH - 2 * OverviewRulerFeature_1.ONE_OVERVIEW_WIDTH;\n          originalOverviewRuler.setLayout({\n            top: 0,\n            height: height,\n            right: freeSpace + OverviewRulerFeature_1.ONE_OVERVIEW_WIDTH,\n            width: OverviewRulerFeature_1.ONE_OVERVIEW_WIDTH\n          });\n          modifiedOverviewRuler.setLayout({\n            top: 0,\n            height: height,\n            right: 0,\n            width: OverviewRulerFeature_1.ONE_OVERVIEW_WIDTH\n          });\n          const scrollTop = this._editors.modifiedScrollTop.read(reader);\n          const scrollHeight = this._editors.modifiedScrollHeight.read(reader);\n          const scrollBarOptions = this._editors.modified.getOption(104 /* EditorOption.scrollbar */);\n          const state = new ScrollbarState(scrollBarOptions.verticalHasArrows ? scrollBarOptions.arrowSize : 0, scrollBarOptions.verticalScrollbarSize, 0, layoutInfo.height, scrollHeight, scrollTop);\n          viewportDomElement.setTop(state.getSliderPosition());\n          viewportDomElement.setHeight(state.getSliderSize());\n        } else {\n          viewportDomElement.setTop(0);\n          viewportDomElement.setHeight(0);\n        }\n        diffOverviewRoot.style.height = height + 'px';\n        diffOverviewRoot.style.left = width - OverviewRulerFeature_1.ENTIRE_DIFF_OVERVIEW_WIDTH + 'px';\n        viewportDomElement.setWidth(OverviewRulerFeature_1.ENTIRE_DIFF_OVERVIEW_WIDTH);\n      }));\n    }));\n  }\n};\nOverviewRulerFeature = OverviewRulerFeature_1 = __decorate([__param(6, IThemeService)], OverviewRulerFeature);\nexport { OverviewRulerFeature };", "map": {"version": 3, "names": ["__decorate", "decorators", "target", "key", "desc", "c", "arguments", "length", "r", "Object", "getOwnPropertyDescriptor", "d", "Reflect", "decorate", "i", "defineProperty", "__param", "paramIndex", "decorator", "OverviewRulerFeature_1", "EventType", "addDisposableListener", "addStandardDisposableListener", "h", "createFastDomNode", "ScrollbarState", "Disposable", "autorun", "autorunWithStore", "derived", "observableFromEvent", "observableSignalFromEvent", "appendRemoveOnDispose", "Position", "OverviewRulerZone", "defaultInsertColor", "defaultRemoveColor", "diffInserted", "diffOverviewRulerInserted", "diffOverviewRulerRemoved", "diffRemoved", "IThemeService", "OverviewRulerFeature", "ONE_OVERVIEW_WIDTH", "ENTIRE_DIFF_OVERVIEW_WIDTH", "constructor", "_editors", "_rootElement", "_diffModel", "_rootWidth", "_rootHeight", "_modifiedEditorLayoutInfo", "_themeService", "width", "currentColorTheme", "onDidColorThemeChange", "getColorTheme", "currentColors", "reader", "theme", "read", "insertColor", "getColor", "transparent", "removeColor", "viewportDomElement", "document", "createElement", "setClassName", "setPosition", "diffOverviewRoot", "style", "position", "top", "root", "_register", "domNode", "POINTER_DOWN", "e", "modified", "delegateVerticalScrollbarPointerDown", "MOUSE_WHEEL", "delegateScrollFromMouseWheelEvent", "passive", "store", "m", "originalOverviewRuler", "original", "createOverviewRuler", "add", "getDomNode", "modifiedOverviewRuler", "origViewZonesChanged", "onDidChangeViewZones", "modViewZonesChanged", "origHiddenRangesChanged", "onDidChangeHiddenAreas", "modHiddenRangesChanged", "colors", "diff", "mappings", "createZones", "ranges", "color", "editor", "vm", "_getViewModel", "filter", "map", "start", "coordinatesConverter", "convertModelPositionToViewPosition", "startLineNumber", "end", "endLineNumberExclusive", "lineCount", "lineNumber", "toString", "originalZones", "lineRangeMapping", "modifiedZones", "setZones", "height", "layoutInfo", "freeSpace", "setLayout", "right", "scrollTop", "modifiedScrollTop", "scrollHeight", "modifiedScrollHeight", "scrollBarOptions", "getOption", "state", "verticalHasArrows", "arrowSize", "verticalScrollbarSize", "setTop", "getSliderPosition", "setHeight", "getSliderSize", "left", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/browser/widget/diffEditor/features/overviewRulerFeature.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = (this && this.__param) || function (paramIndex, decorator) {\n    return function (target, key) { decorator(target, key, paramIndex); }\n};\nvar OverviewRulerFeature_1;\nimport { EventType, addDisposableListener, addStandardDisposableListener, h } from '../../../../../base/browser/dom.js';\nimport { createFastDomNode } from '../../../../../base/browser/fastDomNode.js';\nimport { ScrollbarState } from '../../../../../base/browser/ui/scrollbar/scrollbarState.js';\nimport { Disposable } from '../../../../../base/common/lifecycle.js';\nimport { autorun, autorunWithStore, derived, observableFromEvent, observableSignalFromEvent } from '../../../../../base/common/observable.js';\nimport { appendRemoveOnDispose } from '../utils.js';\nimport { Position } from '../../../../common/core/position.js';\nimport { OverviewRulerZone } from '../../../../common/viewModel/overviewZoneManager.js';\nimport { defaultInsertColor, defaultRemoveColor, diffInserted, diffOverviewRulerInserted, diffOverviewRulerRemoved, diffRemoved } from '../../../../../platform/theme/common/colorRegistry.js';\nimport { IThemeService } from '../../../../../platform/theme/common/themeService.js';\nlet OverviewRulerFeature = class OverviewRulerFeature extends Disposable {\n    static { OverviewRulerFeature_1 = this; }\n    static { this.ONE_OVERVIEW_WIDTH = 15; }\n    static { this.ENTIRE_DIFF_OVERVIEW_WIDTH = this.ONE_OVERVIEW_WIDTH * 2; }\n    constructor(_editors, _rootElement, _diffModel, _rootWidth, _rootHeight, _modifiedEditorLayoutInfo, _themeService) {\n        super();\n        this._editors = _editors;\n        this._rootElement = _rootElement;\n        this._diffModel = _diffModel;\n        this._rootWidth = _rootWidth;\n        this._rootHeight = _rootHeight;\n        this._modifiedEditorLayoutInfo = _modifiedEditorLayoutInfo;\n        this._themeService = _themeService;\n        this.width = OverviewRulerFeature_1.ENTIRE_DIFF_OVERVIEW_WIDTH;\n        const currentColorTheme = observableFromEvent(this._themeService.onDidColorThemeChange, () => this._themeService.getColorTheme());\n        const currentColors = derived(reader => {\n            /** @description colors */\n            const theme = currentColorTheme.read(reader);\n            const insertColor = theme.getColor(diffOverviewRulerInserted) || (theme.getColor(diffInserted) || defaultInsertColor).transparent(2);\n            const removeColor = theme.getColor(diffOverviewRulerRemoved) || (theme.getColor(diffRemoved) || defaultRemoveColor).transparent(2);\n            return { insertColor, removeColor };\n        });\n        const viewportDomElement = createFastDomNode(document.createElement('div'));\n        viewportDomElement.setClassName('diffViewport');\n        viewportDomElement.setPosition('absolute');\n        const diffOverviewRoot = h('div.diffOverview', {\n            style: { position: 'absolute', top: '0px', width: OverviewRulerFeature_1.ENTIRE_DIFF_OVERVIEW_WIDTH + 'px' }\n        }).root;\n        this._register(appendRemoveOnDispose(diffOverviewRoot, viewportDomElement.domNode));\n        this._register(addStandardDisposableListener(diffOverviewRoot, EventType.POINTER_DOWN, (e) => {\n            this._editors.modified.delegateVerticalScrollbarPointerDown(e);\n        }));\n        this._register(addDisposableListener(diffOverviewRoot, EventType.MOUSE_WHEEL, (e) => {\n            this._editors.modified.delegateScrollFromMouseWheelEvent(e);\n        }, { passive: false }));\n        this._register(appendRemoveOnDispose(this._rootElement, diffOverviewRoot));\n        this._register(autorunWithStore((reader, store) => {\n            /** @description recreate overview rules when model changes */\n            const m = this._diffModel.read(reader);\n            const originalOverviewRuler = this._editors.original.createOverviewRuler('original diffOverviewRuler');\n            if (originalOverviewRuler) {\n                store.add(originalOverviewRuler);\n                store.add(appendRemoveOnDispose(diffOverviewRoot, originalOverviewRuler.getDomNode()));\n            }\n            const modifiedOverviewRuler = this._editors.modified.createOverviewRuler('modified diffOverviewRuler');\n            if (modifiedOverviewRuler) {\n                store.add(modifiedOverviewRuler);\n                store.add(appendRemoveOnDispose(diffOverviewRoot, modifiedOverviewRuler.getDomNode()));\n            }\n            if (!originalOverviewRuler || !modifiedOverviewRuler) {\n                // probably no model\n                return;\n            }\n            const origViewZonesChanged = observableSignalFromEvent('viewZoneChanged', this._editors.original.onDidChangeViewZones);\n            const modViewZonesChanged = observableSignalFromEvent('viewZoneChanged', this._editors.modified.onDidChangeViewZones);\n            const origHiddenRangesChanged = observableSignalFromEvent('hiddenRangesChanged', this._editors.original.onDidChangeHiddenAreas);\n            const modHiddenRangesChanged = observableSignalFromEvent('hiddenRangesChanged', this._editors.modified.onDidChangeHiddenAreas);\n            store.add(autorun(reader => {\n                /** @description set overview ruler zones */\n                origViewZonesChanged.read(reader);\n                modViewZonesChanged.read(reader);\n                origHiddenRangesChanged.read(reader);\n                modHiddenRangesChanged.read(reader);\n                const colors = currentColors.read(reader);\n                const diff = m?.diff.read(reader)?.mappings;\n                function createZones(ranges, color, editor) {\n                    const vm = editor._getViewModel();\n                    if (!vm) {\n                        return [];\n                    }\n                    return ranges\n                        .filter(d => d.length > 0)\n                        .map(r => {\n                        const start = vm.coordinatesConverter.convertModelPositionToViewPosition(new Position(r.startLineNumber, 1));\n                        const end = vm.coordinatesConverter.convertModelPositionToViewPosition(new Position(r.endLineNumberExclusive, 1));\n                        // By computing the lineCount, we won't ask the view model later for the bottom vertical position.\n                        // (The view model will take into account the alignment viewzones, which will give\n                        // modifications and deletetions always the same height.)\n                        const lineCount = end.lineNumber - start.lineNumber;\n                        return new OverviewRulerZone(start.lineNumber, end.lineNumber, lineCount, color.toString());\n                    });\n                }\n                const originalZones = createZones((diff || []).map(d => d.lineRangeMapping.original), colors.removeColor, this._editors.original);\n                const modifiedZones = createZones((diff || []).map(d => d.lineRangeMapping.modified), colors.insertColor, this._editors.modified);\n                originalOverviewRuler?.setZones(originalZones);\n                modifiedOverviewRuler?.setZones(modifiedZones);\n            }));\n            store.add(autorun(reader => {\n                /** @description layout overview ruler */\n                const height = this._rootHeight.read(reader);\n                const width = this._rootWidth.read(reader);\n                const layoutInfo = this._modifiedEditorLayoutInfo.read(reader);\n                if (layoutInfo) {\n                    const freeSpace = OverviewRulerFeature_1.ENTIRE_DIFF_OVERVIEW_WIDTH - 2 * OverviewRulerFeature_1.ONE_OVERVIEW_WIDTH;\n                    originalOverviewRuler.setLayout({\n                        top: 0,\n                        height: height,\n                        right: freeSpace + OverviewRulerFeature_1.ONE_OVERVIEW_WIDTH,\n                        width: OverviewRulerFeature_1.ONE_OVERVIEW_WIDTH,\n                    });\n                    modifiedOverviewRuler.setLayout({\n                        top: 0,\n                        height: height,\n                        right: 0,\n                        width: OverviewRulerFeature_1.ONE_OVERVIEW_WIDTH,\n                    });\n                    const scrollTop = this._editors.modifiedScrollTop.read(reader);\n                    const scrollHeight = this._editors.modifiedScrollHeight.read(reader);\n                    const scrollBarOptions = this._editors.modified.getOption(104 /* EditorOption.scrollbar */);\n                    const state = new ScrollbarState(scrollBarOptions.verticalHasArrows ? scrollBarOptions.arrowSize : 0, scrollBarOptions.verticalScrollbarSize, 0, layoutInfo.height, scrollHeight, scrollTop);\n                    viewportDomElement.setTop(state.getSliderPosition());\n                    viewportDomElement.setHeight(state.getSliderSize());\n                }\n                else {\n                    viewportDomElement.setTop(0);\n                    viewportDomElement.setHeight(0);\n                }\n                diffOverviewRoot.style.height = height + 'px';\n                diffOverviewRoot.style.left = (width - OverviewRulerFeature_1.ENTIRE_DIFF_OVERVIEW_WIDTH) + 'px';\n                viewportDomElement.setWidth(OverviewRulerFeature_1.ENTIRE_DIFF_OVERVIEW_WIDTH);\n            }));\n        }));\n    }\n};\nOverviewRulerFeature = OverviewRulerFeature_1 = __decorate([\n    __param(6, IThemeService)\n], OverviewRulerFeature);\nexport { OverviewRulerFeature };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,IAAIA,UAAU,GAAI,IAAI,IAAI,IAAI,CAACA,UAAU,IAAK,UAAUC,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAE;EACnF,IAAIC,CAAC,GAAGC,SAAS,CAACC,MAAM;IAAEC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGH,MAAM,GAAGE,IAAI,KAAK,IAAI,GAAGA,IAAI,GAAGK,MAAM,CAACC,wBAAwB,CAACR,MAAM,EAAEC,GAAG,CAAC,GAAGC,IAAI;IAAEO,CAAC;EAC5H,IAAI,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,CAACC,QAAQ,KAAK,UAAU,EAAEL,CAAC,GAAGI,OAAO,CAACC,QAAQ,CAACZ,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,CAAC,CAAC,KAC1H,KAAK,IAAIU,CAAC,GAAGb,UAAU,CAACM,MAAM,GAAG,CAAC,EAAEO,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE,IAAIH,CAAC,GAAGV,UAAU,CAACa,CAAC,CAAC,EAAEN,CAAC,GAAG,CAACH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACH,CAAC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACT,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,GAAGG,CAAC,CAACT,MAAM,EAAEC,GAAG,CAAC,KAAKK,CAAC;EACjJ,OAAOH,CAAC,GAAG,CAAC,IAAIG,CAAC,IAAIC,MAAM,CAACM,cAAc,CAACb,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,EAAEA,CAAC;AACjE,CAAC;AACD,IAAIQ,OAAO,GAAI,IAAI,IAAI,IAAI,CAACA,OAAO,IAAK,UAAUC,UAAU,EAAEC,SAAS,EAAE;EACrE,OAAO,UAAUhB,MAAM,EAAEC,GAAG,EAAE;IAAEe,SAAS,CAAChB,MAAM,EAAEC,GAAG,EAAEc,UAAU,CAAC;EAAE,CAAC;AACzE,CAAC;AACD,IAAIE,sBAAsB;AAC1B,SAASC,SAAS,EAAEC,qBAAqB,EAAEC,6BAA6B,EAAEC,CAAC,QAAQ,oCAAoC;AACvH,SAASC,iBAAiB,QAAQ,4CAA4C;AAC9E,SAASC,cAAc,QAAQ,4DAA4D;AAC3F,SAASC,UAAU,QAAQ,yCAAyC;AACpE,SAASC,OAAO,EAAEC,gBAAgB,EAAEC,OAAO,EAAEC,mBAAmB,EAAEC,yBAAyB,QAAQ,0CAA0C;AAC7I,SAASC,qBAAqB,QAAQ,aAAa;AACnD,SAASC,QAAQ,QAAQ,qCAAqC;AAC9D,SAASC,iBAAiB,QAAQ,qDAAqD;AACvF,SAASC,kBAAkB,EAAEC,kBAAkB,EAAEC,YAAY,EAAEC,yBAAyB,EAAEC,wBAAwB,EAAEC,WAAW,QAAQ,uDAAuD;AAC9L,SAASC,aAAa,QAAQ,sDAAsD;AACpF,IAAIC,oBAAoB,GAAG,MAAMA,oBAAoB,SAAShB,UAAU,CAAC;EACrE;IAASP,sBAAsB,GAAG,IAAI;EAAE;EACxC;IAAS,IAAI,CAACwB,kBAAkB,GAAG,EAAE;EAAE;EACvC;IAAS,IAAI,CAACC,0BAA0B,GAAG,IAAI,CAACD,kBAAkB,GAAG,CAAC;EAAE;EACxEE,WAAWA,CAACC,QAAQ,EAAEC,YAAY,EAAEC,UAAU,EAAEC,UAAU,EAAEC,WAAW,EAAEC,yBAAyB,EAAEC,aAAa,EAAE;IAC/G,KAAK,CAAC,CAAC;IACP,IAAI,CAACN,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,yBAAyB,GAAGA,yBAAyB;IAC1D,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,KAAK,GAAGlC,sBAAsB,CAACyB,0BAA0B;IAC9D,MAAMU,iBAAiB,GAAGxB,mBAAmB,CAAC,IAAI,CAACsB,aAAa,CAACG,qBAAqB,EAAE,MAAM,IAAI,CAACH,aAAa,CAACI,aAAa,CAAC,CAAC,CAAC;IACjI,MAAMC,aAAa,GAAG5B,OAAO,CAAC6B,MAAM,IAAI;MACpC;MACA,MAAMC,KAAK,GAAGL,iBAAiB,CAACM,IAAI,CAACF,MAAM,CAAC;MAC5C,MAAMG,WAAW,GAAGF,KAAK,CAACG,QAAQ,CAACxB,yBAAyB,CAAC,IAAI,CAACqB,KAAK,CAACG,QAAQ,CAACzB,YAAY,CAAC,IAAIF,kBAAkB,EAAE4B,WAAW,CAAC,CAAC,CAAC;MACpI,MAAMC,WAAW,GAAGL,KAAK,CAACG,QAAQ,CAACvB,wBAAwB,CAAC,IAAI,CAACoB,KAAK,CAACG,QAAQ,CAACtB,WAAW,CAAC,IAAIJ,kBAAkB,EAAE2B,WAAW,CAAC,CAAC,CAAC;MAClI,OAAO;QAAEF,WAAW;QAAEG;MAAY,CAAC;IACvC,CAAC,CAAC;IACF,MAAMC,kBAAkB,GAAGzC,iBAAiB,CAAC0C,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC3EF,kBAAkB,CAACG,YAAY,CAAC,cAAc,CAAC;IAC/CH,kBAAkB,CAACI,WAAW,CAAC,UAAU,CAAC;IAC1C,MAAMC,gBAAgB,GAAG/C,CAAC,CAAC,kBAAkB,EAAE;MAC3CgD,KAAK,EAAE;QAAEC,QAAQ,EAAE,UAAU;QAAEC,GAAG,EAAE,KAAK;QAAEpB,KAAK,EAAElC,sBAAsB,CAACyB,0BAA0B,GAAG;MAAK;IAC/G,CAAC,CAAC,CAAC8B,IAAI;IACP,IAAI,CAACC,SAAS,CAAC3C,qBAAqB,CAACsC,gBAAgB,EAAEL,kBAAkB,CAACW,OAAO,CAAC,CAAC;IACnF,IAAI,CAACD,SAAS,CAACrD,6BAA6B,CAACgD,gBAAgB,EAAElD,SAAS,CAACyD,YAAY,EAAGC,CAAC,IAAK;MAC1F,IAAI,CAAChC,QAAQ,CAACiC,QAAQ,CAACC,oCAAoC,CAACF,CAAC,CAAC;IAClE,CAAC,CAAC,CAAC;IACH,IAAI,CAACH,SAAS,CAACtD,qBAAqB,CAACiD,gBAAgB,EAAElD,SAAS,CAAC6D,WAAW,EAAGH,CAAC,IAAK;MACjF,IAAI,CAAChC,QAAQ,CAACiC,QAAQ,CAACG,iCAAiC,CAACJ,CAAC,CAAC;IAC/D,CAAC,EAAE;MAAEK,OAAO,EAAE;IAAM,CAAC,CAAC,CAAC;IACvB,IAAI,CAACR,SAAS,CAAC3C,qBAAqB,CAAC,IAAI,CAACe,YAAY,EAAEuB,gBAAgB,CAAC,CAAC;IAC1E,IAAI,CAACK,SAAS,CAAC/C,gBAAgB,CAAC,CAAC8B,MAAM,EAAE0B,KAAK,KAAK;MAC/C;MACA,MAAMC,CAAC,GAAG,IAAI,CAACrC,UAAU,CAACY,IAAI,CAACF,MAAM,CAAC;MACtC,MAAM4B,qBAAqB,GAAG,IAAI,CAACxC,QAAQ,CAACyC,QAAQ,CAACC,mBAAmB,CAAC,4BAA4B,CAAC;MACtG,IAAIF,qBAAqB,EAAE;QACvBF,KAAK,CAACK,GAAG,CAACH,qBAAqB,CAAC;QAChCF,KAAK,CAACK,GAAG,CAACzD,qBAAqB,CAACsC,gBAAgB,EAAEgB,qBAAqB,CAACI,UAAU,CAAC,CAAC,CAAC,CAAC;MAC1F;MACA,MAAMC,qBAAqB,GAAG,IAAI,CAAC7C,QAAQ,CAACiC,QAAQ,CAACS,mBAAmB,CAAC,4BAA4B,CAAC;MACtG,IAAIG,qBAAqB,EAAE;QACvBP,KAAK,CAACK,GAAG,CAACE,qBAAqB,CAAC;QAChCP,KAAK,CAACK,GAAG,CAACzD,qBAAqB,CAACsC,gBAAgB,EAAEqB,qBAAqB,CAACD,UAAU,CAAC,CAAC,CAAC,CAAC;MAC1F;MACA,IAAI,CAACJ,qBAAqB,IAAI,CAACK,qBAAqB,EAAE;QAClD;QACA;MACJ;MACA,MAAMC,oBAAoB,GAAG7D,yBAAyB,CAAC,iBAAiB,EAAE,IAAI,CAACe,QAAQ,CAACyC,QAAQ,CAACM,oBAAoB,CAAC;MACtH,MAAMC,mBAAmB,GAAG/D,yBAAyB,CAAC,iBAAiB,EAAE,IAAI,CAACe,QAAQ,CAACiC,QAAQ,CAACc,oBAAoB,CAAC;MACrH,MAAME,uBAAuB,GAAGhE,yBAAyB,CAAC,qBAAqB,EAAE,IAAI,CAACe,QAAQ,CAACyC,QAAQ,CAACS,sBAAsB,CAAC;MAC/H,MAAMC,sBAAsB,GAAGlE,yBAAyB,CAAC,qBAAqB,EAAE,IAAI,CAACe,QAAQ,CAACiC,QAAQ,CAACiB,sBAAsB,CAAC;MAC9HZ,KAAK,CAACK,GAAG,CAAC9D,OAAO,CAAC+B,MAAM,IAAI;QACxB;QACAkC,oBAAoB,CAAChC,IAAI,CAACF,MAAM,CAAC;QACjCoC,mBAAmB,CAAClC,IAAI,CAACF,MAAM,CAAC;QAChCqC,uBAAuB,CAACnC,IAAI,CAACF,MAAM,CAAC;QACpCuC,sBAAsB,CAACrC,IAAI,CAACF,MAAM,CAAC;QACnC,MAAMwC,MAAM,GAAGzC,aAAa,CAACG,IAAI,CAACF,MAAM,CAAC;QACzC,MAAMyC,IAAI,GAAGd,CAAC,EAAEc,IAAI,CAACvC,IAAI,CAACF,MAAM,CAAC,EAAE0C,QAAQ;QAC3C,SAASC,WAAWA,CAACC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAE;UACxC,MAAMC,EAAE,GAAGD,MAAM,CAACE,aAAa,CAAC,CAAC;UACjC,IAAI,CAACD,EAAE,EAAE;YACL,OAAO,EAAE;UACb;UACA,OAAOH,MAAM,CACRK,MAAM,CAAChG,CAAC,IAAIA,CAAC,CAACJ,MAAM,GAAG,CAAC,CAAC,CACzBqG,GAAG,CAACpG,CAAC,IAAI;YACV,MAAMqG,KAAK,GAAGJ,EAAE,CAACK,oBAAoB,CAACC,kCAAkC,CAAC,IAAI9E,QAAQ,CAACzB,CAAC,CAACwG,eAAe,EAAE,CAAC,CAAC,CAAC;YAC5G,MAAMC,GAAG,GAAGR,EAAE,CAACK,oBAAoB,CAACC,kCAAkC,CAAC,IAAI9E,QAAQ,CAACzB,CAAC,CAAC0G,sBAAsB,EAAE,CAAC,CAAC,CAAC;YACjH;YACA;YACA;YACA,MAAMC,SAAS,GAAGF,GAAG,CAACG,UAAU,GAAGP,KAAK,CAACO,UAAU;YACnD,OAAO,IAAIlF,iBAAiB,CAAC2E,KAAK,CAACO,UAAU,EAAEH,GAAG,CAACG,UAAU,EAAED,SAAS,EAAEZ,KAAK,CAACc,QAAQ,CAAC,CAAC,CAAC;UAC/F,CAAC,CAAC;QACN;QACA,MAAMC,aAAa,GAAGjB,WAAW,CAAC,CAACF,IAAI,IAAI,EAAE,EAAES,GAAG,CAACjG,CAAC,IAAIA,CAAC,CAAC4G,gBAAgB,CAAChC,QAAQ,CAAC,EAAEW,MAAM,CAAClC,WAAW,EAAE,IAAI,CAAClB,QAAQ,CAACyC,QAAQ,CAAC;QACjI,MAAMiC,aAAa,GAAGnB,WAAW,CAAC,CAACF,IAAI,IAAI,EAAE,EAAES,GAAG,CAACjG,CAAC,IAAIA,CAAC,CAAC4G,gBAAgB,CAACxC,QAAQ,CAAC,EAAEmB,MAAM,CAACrC,WAAW,EAAE,IAAI,CAACf,QAAQ,CAACiC,QAAQ,CAAC;QACjIO,qBAAqB,EAAEmC,QAAQ,CAACH,aAAa,CAAC;QAC9C3B,qBAAqB,EAAE8B,QAAQ,CAACD,aAAa,CAAC;MAClD,CAAC,CAAC,CAAC;MACHpC,KAAK,CAACK,GAAG,CAAC9D,OAAO,CAAC+B,MAAM,IAAI;QACxB;QACA,MAAMgE,MAAM,GAAG,IAAI,CAACxE,WAAW,CAACU,IAAI,CAACF,MAAM,CAAC;QAC5C,MAAML,KAAK,GAAG,IAAI,CAACJ,UAAU,CAACW,IAAI,CAACF,MAAM,CAAC;QAC1C,MAAMiE,UAAU,GAAG,IAAI,CAACxE,yBAAyB,CAACS,IAAI,CAACF,MAAM,CAAC;QAC9D,IAAIiE,UAAU,EAAE;UACZ,MAAMC,SAAS,GAAGzG,sBAAsB,CAACyB,0BAA0B,GAAG,CAAC,GAAGzB,sBAAsB,CAACwB,kBAAkB;UACnH2C,qBAAqB,CAACuC,SAAS,CAAC;YAC5BpD,GAAG,EAAE,CAAC;YACNiD,MAAM,EAAEA,MAAM;YACdI,KAAK,EAAEF,SAAS,GAAGzG,sBAAsB,CAACwB,kBAAkB;YAC5DU,KAAK,EAAElC,sBAAsB,CAACwB;UAClC,CAAC,CAAC;UACFgD,qBAAqB,CAACkC,SAAS,CAAC;YAC5BpD,GAAG,EAAE,CAAC;YACNiD,MAAM,EAAEA,MAAM;YACdI,KAAK,EAAE,CAAC;YACRzE,KAAK,EAAElC,sBAAsB,CAACwB;UAClC,CAAC,CAAC;UACF,MAAMoF,SAAS,GAAG,IAAI,CAACjF,QAAQ,CAACkF,iBAAiB,CAACpE,IAAI,CAACF,MAAM,CAAC;UAC9D,MAAMuE,YAAY,GAAG,IAAI,CAACnF,QAAQ,CAACoF,oBAAoB,CAACtE,IAAI,CAACF,MAAM,CAAC;UACpE,MAAMyE,gBAAgB,GAAG,IAAI,CAACrF,QAAQ,CAACiC,QAAQ,CAACqD,SAAS,CAAC,GAAG,CAAC,4BAA4B,CAAC;UAC3F,MAAMC,KAAK,GAAG,IAAI5G,cAAc,CAAC0G,gBAAgB,CAACG,iBAAiB,GAAGH,gBAAgB,CAACI,SAAS,GAAG,CAAC,EAAEJ,gBAAgB,CAACK,qBAAqB,EAAE,CAAC,EAAEb,UAAU,CAACD,MAAM,EAAEO,YAAY,EAAEF,SAAS,CAAC;UAC5L9D,kBAAkB,CAACwE,MAAM,CAACJ,KAAK,CAACK,iBAAiB,CAAC,CAAC,CAAC;UACpDzE,kBAAkB,CAAC0E,SAAS,CAACN,KAAK,CAACO,aAAa,CAAC,CAAC,CAAC;QACvD,CAAC,MACI;UACD3E,kBAAkB,CAACwE,MAAM,CAAC,CAAC,CAAC;UAC5BxE,kBAAkB,CAAC0E,SAAS,CAAC,CAAC,CAAC;QACnC;QACArE,gBAAgB,CAACC,KAAK,CAACmD,MAAM,GAAGA,MAAM,GAAG,IAAI;QAC7CpD,gBAAgB,CAACC,KAAK,CAACsE,IAAI,GAAIxF,KAAK,GAAGlC,sBAAsB,CAACyB,0BAA0B,GAAI,IAAI;QAChGqB,kBAAkB,CAAC6E,QAAQ,CAAC3H,sBAAsB,CAACyB,0BAA0B,CAAC;MAClF,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC;EACP;AACJ,CAAC;AACDF,oBAAoB,GAAGvB,sBAAsB,GAAGnB,UAAU,CAAC,CACvDgB,OAAO,CAAC,CAAC,EAAEyB,aAAa,CAAC,CAC5B,EAAEC,oBAAoB,CAAC;AACxB,SAASA,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}