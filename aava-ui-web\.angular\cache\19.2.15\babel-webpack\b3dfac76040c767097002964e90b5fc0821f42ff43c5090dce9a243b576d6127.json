{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = this && this.__param || function (paramIndex, decorator) {\n  return function (target, key) {\n    decorator(target, key, paramIndex);\n  };\n};\nimport { createTrustedTypesPolicy } from '../../../../../base/browser/trustedTypes.js';\nimport { Event } from '../../../../../base/common/event.js';\nimport { Disposable, toDisposable } from '../../../../../base/common/lifecycle.js';\nimport { autorun, derived, observableFromEvent, observableSignalFromEvent, observableValue } from '../../../../../base/common/observable.js';\nimport * as strings from '../../../../../base/common/strings.js';\nimport './ghostTextView.css';\nimport { applyFontInfo } from '../../../../browser/config/domFontInfo.js';\nimport { EditorFontLigatures } from '../../../../common/config/editorOptions.js';\nimport { Position } from '../../../../common/core/position.js';\nimport { Range } from '../../../../common/core/range.js';\nimport { StringBuilder } from '../../../../common/core/stringBuilder.js';\nimport { ILanguageService } from '../../../../common/languages/language.js';\nimport { InjectedTextCursorStops } from '../../../../common/model.js';\nimport { LineTokens } from '../../../../common/tokens/lineTokens.js';\nimport { LineDecoration } from '../../../../common/viewLayout/lineDecorations.js';\nimport { RenderLineInput, renderViewLine } from '../../../../common/viewLayout/viewLineRenderer.js';\nimport { GhostTextReplacement } from '../model/ghostText.js';\nimport { ColumnRange, applyObservableDecorations } from '../utils.js';\nexport const GHOST_TEXT_DESCRIPTION = 'ghost-text';\nlet GhostTextView = class GhostTextView extends Disposable {\n  constructor(editor, model, languageService) {\n    super();\n    this.editor = editor;\n    this.model = model;\n    this.languageService = languageService;\n    this.isDisposed = observableValue(this, false);\n    this.currentTextModel = observableFromEvent(this, this.editor.onDidChangeModel, () => /** @description editor.model */this.editor.getModel());\n    this.uiState = derived(this, reader => {\n      if (this.isDisposed.read(reader)) {\n        return undefined;\n      }\n      const textModel = this.currentTextModel.read(reader);\n      if (textModel !== this.model.targetTextModel.read(reader)) {\n        return undefined;\n      }\n      const ghostText = this.model.ghostText.read(reader);\n      if (!ghostText) {\n        return undefined;\n      }\n      const replacedRange = ghostText instanceof GhostTextReplacement ? ghostText.columnRange : undefined;\n      const inlineTexts = [];\n      const additionalLines = [];\n      function addToAdditionalLines(lines, className) {\n        if (additionalLines.length > 0) {\n          const lastLine = additionalLines[additionalLines.length - 1];\n          if (className) {\n            lastLine.decorations.push(new LineDecoration(lastLine.content.length + 1, lastLine.content.length + 1 + lines[0].length, className, 0 /* InlineDecorationType.Regular */));\n          }\n          lastLine.content += lines[0];\n          lines = lines.slice(1);\n        }\n        for (const line of lines) {\n          additionalLines.push({\n            content: line,\n            decorations: className ? [new LineDecoration(1, line.length + 1, className, 0 /* InlineDecorationType.Regular */)] : []\n          });\n        }\n      }\n      const textBufferLine = textModel.getLineContent(ghostText.lineNumber);\n      let hiddenTextStartColumn = undefined;\n      let lastIdx = 0;\n      for (const part of ghostText.parts) {\n        let lines = part.lines;\n        if (hiddenTextStartColumn === undefined) {\n          inlineTexts.push({\n            column: part.column,\n            text: lines[0],\n            preview: part.preview\n          });\n          lines = lines.slice(1);\n        } else {\n          addToAdditionalLines([textBufferLine.substring(lastIdx, part.column - 1)], undefined);\n        }\n        if (lines.length > 0) {\n          addToAdditionalLines(lines, GHOST_TEXT_DESCRIPTION);\n          if (hiddenTextStartColumn === undefined && part.column <= textBufferLine.length) {\n            hiddenTextStartColumn = part.column;\n          }\n        }\n        lastIdx = part.column - 1;\n      }\n      if (hiddenTextStartColumn !== undefined) {\n        addToAdditionalLines([textBufferLine.substring(lastIdx)], undefined);\n      }\n      const hiddenRange = hiddenTextStartColumn !== undefined ? new ColumnRange(hiddenTextStartColumn, textBufferLine.length + 1) : undefined;\n      return {\n        replacedRange,\n        inlineTexts,\n        additionalLines,\n        hiddenRange,\n        lineNumber: ghostText.lineNumber,\n        additionalReservedLineCount: this.model.minReservedLineCount.read(reader),\n        targetTextModel: textModel\n      };\n    });\n    this.decorations = derived(this, reader => {\n      const uiState = this.uiState.read(reader);\n      if (!uiState) {\n        return [];\n      }\n      const decorations = [];\n      if (uiState.replacedRange) {\n        decorations.push({\n          range: uiState.replacedRange.toRange(uiState.lineNumber),\n          options: {\n            inlineClassName: 'inline-completion-text-to-replace',\n            description: 'GhostTextReplacement'\n          }\n        });\n      }\n      if (uiState.hiddenRange) {\n        decorations.push({\n          range: uiState.hiddenRange.toRange(uiState.lineNumber),\n          options: {\n            inlineClassName: 'ghost-text-hidden',\n            description: 'ghost-text-hidden'\n          }\n        });\n      }\n      for (const p of uiState.inlineTexts) {\n        decorations.push({\n          range: Range.fromPositions(new Position(uiState.lineNumber, p.column)),\n          options: {\n            description: GHOST_TEXT_DESCRIPTION,\n            after: {\n              content: p.text,\n              inlineClassName: p.preview ? 'ghost-text-decoration-preview' : 'ghost-text-decoration',\n              cursorStops: InjectedTextCursorStops.Left\n            },\n            showIfCollapsed: true\n          }\n        });\n      }\n      return decorations;\n    });\n    this.additionalLinesWidget = this._register(new AdditionalLinesWidget(this.editor, this.languageService.languageIdCodec, derived(reader => {\n      /** @description lines */\n      const uiState = this.uiState.read(reader);\n      return uiState ? {\n        lineNumber: uiState.lineNumber,\n        additionalLines: uiState.additionalLines,\n        minReservedLineCount: uiState.additionalReservedLineCount,\n        targetTextModel: uiState.targetTextModel\n      } : undefined;\n    })));\n    this._register(toDisposable(() => {\n      this.isDisposed.set(true, undefined);\n    }));\n    this._register(applyObservableDecorations(this.editor, this.decorations));\n  }\n  ownsViewZone(viewZoneId) {\n    return this.additionalLinesWidget.viewZoneId === viewZoneId;\n  }\n};\nGhostTextView = __decorate([__param(2, ILanguageService)], GhostTextView);\nexport { GhostTextView };\nexport class AdditionalLinesWidget extends Disposable {\n  get viewZoneId() {\n    return this._viewZoneId;\n  }\n  constructor(editor, languageIdCodec, lines) {\n    super();\n    this.editor = editor;\n    this.languageIdCodec = languageIdCodec;\n    this.lines = lines;\n    this._viewZoneId = undefined;\n    this.editorOptionsChanged = observableSignalFromEvent('editorOptionChanged', Event.filter(this.editor.onDidChangeConfiguration, e => e.hasChanged(33 /* EditorOption.disableMonospaceOptimizations */) || e.hasChanged(118 /* EditorOption.stopRenderingLineAfter */) || e.hasChanged(100 /* EditorOption.renderWhitespace */) || e.hasChanged(95 /* EditorOption.renderControlCharacters */) || e.hasChanged(51 /* EditorOption.fontLigatures */) || e.hasChanged(50 /* EditorOption.fontInfo */) || e.hasChanged(67 /* EditorOption.lineHeight */)));\n    this._register(autorun(reader => {\n      /** @description update view zone */\n      const lines = this.lines.read(reader);\n      this.editorOptionsChanged.read(reader);\n      if (lines) {\n        this.updateLines(lines.lineNumber, lines.additionalLines, lines.minReservedLineCount);\n      } else {\n        this.clear();\n      }\n    }));\n  }\n  dispose() {\n    super.dispose();\n    this.clear();\n  }\n  clear() {\n    this.editor.changeViewZones(changeAccessor => {\n      if (this._viewZoneId) {\n        changeAccessor.removeZone(this._viewZoneId);\n        this._viewZoneId = undefined;\n      }\n    });\n  }\n  updateLines(lineNumber, additionalLines, minReservedLineCount) {\n    const textModel = this.editor.getModel();\n    if (!textModel) {\n      return;\n    }\n    const {\n      tabSize\n    } = textModel.getOptions();\n    this.editor.changeViewZones(changeAccessor => {\n      if (this._viewZoneId) {\n        changeAccessor.removeZone(this._viewZoneId);\n        this._viewZoneId = undefined;\n      }\n      const heightInLines = Math.max(additionalLines.length, minReservedLineCount);\n      if (heightInLines > 0) {\n        const domNode = document.createElement('div');\n        renderLines(domNode, tabSize, additionalLines, this.editor.getOptions(), this.languageIdCodec);\n        this._viewZoneId = changeAccessor.addZone({\n          afterLineNumber: lineNumber,\n          heightInLines: heightInLines,\n          domNode,\n          afterColumnAffinity: 1 /* PositionAffinity.Right */\n        });\n      }\n    });\n  }\n}\nfunction renderLines(domNode, tabSize, lines, opts, languageIdCodec) {\n  const disableMonospaceOptimizations = opts.get(33 /* EditorOption.disableMonospaceOptimizations */);\n  const stopRenderingLineAfter = opts.get(118 /* EditorOption.stopRenderingLineAfter */);\n  // To avoid visual confusion, we don't want to render visible whitespace\n  const renderWhitespace = 'none';\n  const renderControlCharacters = opts.get(95 /* EditorOption.renderControlCharacters */);\n  const fontLigatures = opts.get(51 /* EditorOption.fontLigatures */);\n  const fontInfo = opts.get(50 /* EditorOption.fontInfo */);\n  const lineHeight = opts.get(67 /* EditorOption.lineHeight */);\n  const sb = new StringBuilder(10000);\n  sb.appendString('<div class=\"suggest-preview-text\">');\n  for (let i = 0, len = lines.length; i < len; i++) {\n    const lineData = lines[i];\n    const line = lineData.content;\n    sb.appendString('<div class=\"view-line');\n    sb.appendString('\" style=\"top:');\n    sb.appendString(String(i * lineHeight));\n    sb.appendString('px;width:1000000px;\">');\n    const isBasicASCII = strings.isBasicASCII(line);\n    const containsRTL = strings.containsRTL(line);\n    const lineTokens = LineTokens.createEmpty(line, languageIdCodec);\n    renderViewLine(new RenderLineInput(fontInfo.isMonospace && !disableMonospaceOptimizations, fontInfo.canUseHalfwidthRightwardsArrow, line, false, isBasicASCII, containsRTL, 0, lineTokens, lineData.decorations, tabSize, 0, fontInfo.spaceWidth, fontInfo.middotWidth, fontInfo.wsmiddotWidth, stopRenderingLineAfter, renderWhitespace, renderControlCharacters, fontLigatures !== EditorFontLigatures.OFF, null), sb);\n    sb.appendString('</div>');\n  }\n  sb.appendString('</div>');\n  applyFontInfo(domNode, fontInfo);\n  const html = sb.build();\n  const trustedhtml = ttPolicy ? ttPolicy.createHTML(html) : html;\n  domNode.innerHTML = trustedhtml;\n}\nexport const ttPolicy = createTrustedTypesPolicy('editorGhostText', {\n  createHTML: value => value\n});", "map": {"version": 3, "names": ["__decorate", "decorators", "target", "key", "desc", "c", "arguments", "length", "r", "Object", "getOwnPropertyDescriptor", "d", "Reflect", "decorate", "i", "defineProperty", "__param", "paramIndex", "decorator", "createTrustedTypesPolicy", "Event", "Disposable", "toDisposable", "autorun", "derived", "observableFromEvent", "observableSignalFromEvent", "observableValue", "strings", "applyFontInfo", "EditorFontLigatures", "Position", "Range", "StringBuilder", "ILanguageService", "InjectedTextCursorStops", "LineTokens", "LineDecoration", "RenderLineInput", "renderViewLine", "GhostTextReplacement", "ColumnRange", "applyObservableDecorations", "GHOST_TEXT_DESCRIPTION", "GhostTextView", "constructor", "editor", "model", "languageService", "isDisposed", "currentTextModel", "onDidChangeModel", "getModel", "uiState", "reader", "read", "undefined", "textModel", "targetTextModel", "ghostText", "replaced<PERSON><PERSON><PERSON>", "columnRange", "inlineTexts", "additionalLines", "addToAdditionalLines", "lines", "className", "lastLine", "decorations", "push", "content", "slice", "line", "textBufferLine", "get<PERSON>ineC<PERSON>nt", "lineNumber", "hiddenTextStartColumn", "lastIdx", "part", "parts", "column", "text", "preview", "substring", "hiddenRange", "additionalReservedLineCount", "minReservedLineCount", "range", "to<PERSON><PERSON><PERSON>", "options", "inlineClassName", "description", "p", "fromPositions", "after", "cursorStops", "Left", "showIfCollapsed", "additionalLinesWidget", "_register", "AdditionalLinesWidget", "languageIdCodec", "set", "ownsViewZone", "viewZoneId", "_viewZoneId", "editorO<PERSON>s<PERSON><PERSON>ed", "filter", "onDidChangeConfiguration", "e", "has<PERSON><PERSON>ed", "updateLines", "clear", "dispose", "changeViewZones", "changeAccessor", "removeZone", "tabSize", "getOptions", "heightInLines", "Math", "max", "domNode", "document", "createElement", "renderLines", "addZone", "afterLineNumber", "afterColumnAffinity", "opts", "disableMonospaceOptimizations", "get", "stopRenderingLineAfter", "renderWhitespace", "renderControlCharacters", "fontLigatures", "fontInfo", "lineHeight", "sb", "appendString", "len", "lineData", "String", "isBasicASCII", "containsRTL", "lineTokens", "createEmpty", "isMonospace", "canUseHalfwidthRightwardsArrow", "spaceWidth", "middotWidth", "wsmid<PERSON>t<PERSON><PERSON>th", "OFF", "html", "build", "trustedhtml", "ttPolicy", "createHTML", "innerHTML", "value"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/contrib/inlineCompletions/browser/view/ghostTextView.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = (this && this.__param) || function (paramIndex, decorator) {\n    return function (target, key) { decorator(target, key, paramIndex); }\n};\nimport { createTrustedTypesPolicy } from '../../../../../base/browser/trustedTypes.js';\nimport { Event } from '../../../../../base/common/event.js';\nimport { Disposable, toDisposable } from '../../../../../base/common/lifecycle.js';\nimport { autorun, derived, observableFromEvent, observableSignalFromEvent, observableValue } from '../../../../../base/common/observable.js';\nimport * as strings from '../../../../../base/common/strings.js';\nimport './ghostTextView.css';\nimport { applyFontInfo } from '../../../../browser/config/domFontInfo.js';\nimport { EditorFontLigatures } from '../../../../common/config/editorOptions.js';\nimport { Position } from '../../../../common/core/position.js';\nimport { Range } from '../../../../common/core/range.js';\nimport { StringBuilder } from '../../../../common/core/stringBuilder.js';\nimport { ILanguageService } from '../../../../common/languages/language.js';\nimport { InjectedTextCursorStops } from '../../../../common/model.js';\nimport { LineTokens } from '../../../../common/tokens/lineTokens.js';\nimport { LineDecoration } from '../../../../common/viewLayout/lineDecorations.js';\nimport { RenderLineInput, renderViewLine } from '../../../../common/viewLayout/viewLineRenderer.js';\nimport { GhostTextReplacement } from '../model/ghostText.js';\nimport { ColumnRange, applyObservableDecorations } from '../utils.js';\nexport const GHOST_TEXT_DESCRIPTION = 'ghost-text';\nlet GhostTextView = class GhostTextView extends Disposable {\n    constructor(editor, model, languageService) {\n        super();\n        this.editor = editor;\n        this.model = model;\n        this.languageService = languageService;\n        this.isDisposed = observableValue(this, false);\n        this.currentTextModel = observableFromEvent(this, this.editor.onDidChangeModel, () => /** @description editor.model */ this.editor.getModel());\n        this.uiState = derived(this, reader => {\n            if (this.isDisposed.read(reader)) {\n                return undefined;\n            }\n            const textModel = this.currentTextModel.read(reader);\n            if (textModel !== this.model.targetTextModel.read(reader)) {\n                return undefined;\n            }\n            const ghostText = this.model.ghostText.read(reader);\n            if (!ghostText) {\n                return undefined;\n            }\n            const replacedRange = ghostText instanceof GhostTextReplacement ? ghostText.columnRange : undefined;\n            const inlineTexts = [];\n            const additionalLines = [];\n            function addToAdditionalLines(lines, className) {\n                if (additionalLines.length > 0) {\n                    const lastLine = additionalLines[additionalLines.length - 1];\n                    if (className) {\n                        lastLine.decorations.push(new LineDecoration(lastLine.content.length + 1, lastLine.content.length + 1 + lines[0].length, className, 0 /* InlineDecorationType.Regular */));\n                    }\n                    lastLine.content += lines[0];\n                    lines = lines.slice(1);\n                }\n                for (const line of lines) {\n                    additionalLines.push({\n                        content: line,\n                        decorations: className ? [new LineDecoration(1, line.length + 1, className, 0 /* InlineDecorationType.Regular */)] : []\n                    });\n                }\n            }\n            const textBufferLine = textModel.getLineContent(ghostText.lineNumber);\n            let hiddenTextStartColumn = undefined;\n            let lastIdx = 0;\n            for (const part of ghostText.parts) {\n                let lines = part.lines;\n                if (hiddenTextStartColumn === undefined) {\n                    inlineTexts.push({\n                        column: part.column,\n                        text: lines[0],\n                        preview: part.preview,\n                    });\n                    lines = lines.slice(1);\n                }\n                else {\n                    addToAdditionalLines([textBufferLine.substring(lastIdx, part.column - 1)], undefined);\n                }\n                if (lines.length > 0) {\n                    addToAdditionalLines(lines, GHOST_TEXT_DESCRIPTION);\n                    if (hiddenTextStartColumn === undefined && part.column <= textBufferLine.length) {\n                        hiddenTextStartColumn = part.column;\n                    }\n                }\n                lastIdx = part.column - 1;\n            }\n            if (hiddenTextStartColumn !== undefined) {\n                addToAdditionalLines([textBufferLine.substring(lastIdx)], undefined);\n            }\n            const hiddenRange = hiddenTextStartColumn !== undefined ? new ColumnRange(hiddenTextStartColumn, textBufferLine.length + 1) : undefined;\n            return {\n                replacedRange,\n                inlineTexts,\n                additionalLines,\n                hiddenRange,\n                lineNumber: ghostText.lineNumber,\n                additionalReservedLineCount: this.model.minReservedLineCount.read(reader),\n                targetTextModel: textModel,\n            };\n        });\n        this.decorations = derived(this, reader => {\n            const uiState = this.uiState.read(reader);\n            if (!uiState) {\n                return [];\n            }\n            const decorations = [];\n            if (uiState.replacedRange) {\n                decorations.push({\n                    range: uiState.replacedRange.toRange(uiState.lineNumber),\n                    options: { inlineClassName: 'inline-completion-text-to-replace', description: 'GhostTextReplacement' }\n                });\n            }\n            if (uiState.hiddenRange) {\n                decorations.push({\n                    range: uiState.hiddenRange.toRange(uiState.lineNumber),\n                    options: { inlineClassName: 'ghost-text-hidden', description: 'ghost-text-hidden', }\n                });\n            }\n            for (const p of uiState.inlineTexts) {\n                decorations.push({\n                    range: Range.fromPositions(new Position(uiState.lineNumber, p.column)),\n                    options: {\n                        description: GHOST_TEXT_DESCRIPTION,\n                        after: { content: p.text, inlineClassName: p.preview ? 'ghost-text-decoration-preview' : 'ghost-text-decoration', cursorStops: InjectedTextCursorStops.Left },\n                        showIfCollapsed: true,\n                    }\n                });\n            }\n            return decorations;\n        });\n        this.additionalLinesWidget = this._register(new AdditionalLinesWidget(this.editor, this.languageService.languageIdCodec, derived(reader => {\n            /** @description lines */\n            const uiState = this.uiState.read(reader);\n            return uiState ? {\n                lineNumber: uiState.lineNumber,\n                additionalLines: uiState.additionalLines,\n                minReservedLineCount: uiState.additionalReservedLineCount,\n                targetTextModel: uiState.targetTextModel,\n            } : undefined;\n        })));\n        this._register(toDisposable(() => { this.isDisposed.set(true, undefined); }));\n        this._register(applyObservableDecorations(this.editor, this.decorations));\n    }\n    ownsViewZone(viewZoneId) {\n        return this.additionalLinesWidget.viewZoneId === viewZoneId;\n    }\n};\nGhostTextView = __decorate([\n    __param(2, ILanguageService)\n], GhostTextView);\nexport { GhostTextView };\nexport class AdditionalLinesWidget extends Disposable {\n    get viewZoneId() { return this._viewZoneId; }\n    constructor(editor, languageIdCodec, lines) {\n        super();\n        this.editor = editor;\n        this.languageIdCodec = languageIdCodec;\n        this.lines = lines;\n        this._viewZoneId = undefined;\n        this.editorOptionsChanged = observableSignalFromEvent('editorOptionChanged', Event.filter(this.editor.onDidChangeConfiguration, e => e.hasChanged(33 /* EditorOption.disableMonospaceOptimizations */)\n            || e.hasChanged(118 /* EditorOption.stopRenderingLineAfter */)\n            || e.hasChanged(100 /* EditorOption.renderWhitespace */)\n            || e.hasChanged(95 /* EditorOption.renderControlCharacters */)\n            || e.hasChanged(51 /* EditorOption.fontLigatures */)\n            || e.hasChanged(50 /* EditorOption.fontInfo */)\n            || e.hasChanged(67 /* EditorOption.lineHeight */)));\n        this._register(autorun(reader => {\n            /** @description update view zone */\n            const lines = this.lines.read(reader);\n            this.editorOptionsChanged.read(reader);\n            if (lines) {\n                this.updateLines(lines.lineNumber, lines.additionalLines, lines.minReservedLineCount);\n            }\n            else {\n                this.clear();\n            }\n        }));\n    }\n    dispose() {\n        super.dispose();\n        this.clear();\n    }\n    clear() {\n        this.editor.changeViewZones((changeAccessor) => {\n            if (this._viewZoneId) {\n                changeAccessor.removeZone(this._viewZoneId);\n                this._viewZoneId = undefined;\n            }\n        });\n    }\n    updateLines(lineNumber, additionalLines, minReservedLineCount) {\n        const textModel = this.editor.getModel();\n        if (!textModel) {\n            return;\n        }\n        const { tabSize } = textModel.getOptions();\n        this.editor.changeViewZones((changeAccessor) => {\n            if (this._viewZoneId) {\n                changeAccessor.removeZone(this._viewZoneId);\n                this._viewZoneId = undefined;\n            }\n            const heightInLines = Math.max(additionalLines.length, minReservedLineCount);\n            if (heightInLines > 0) {\n                const domNode = document.createElement('div');\n                renderLines(domNode, tabSize, additionalLines, this.editor.getOptions(), this.languageIdCodec);\n                this._viewZoneId = changeAccessor.addZone({\n                    afterLineNumber: lineNumber,\n                    heightInLines: heightInLines,\n                    domNode,\n                    afterColumnAffinity: 1 /* PositionAffinity.Right */\n                });\n            }\n        });\n    }\n}\nfunction renderLines(domNode, tabSize, lines, opts, languageIdCodec) {\n    const disableMonospaceOptimizations = opts.get(33 /* EditorOption.disableMonospaceOptimizations */);\n    const stopRenderingLineAfter = opts.get(118 /* EditorOption.stopRenderingLineAfter */);\n    // To avoid visual confusion, we don't want to render visible whitespace\n    const renderWhitespace = 'none';\n    const renderControlCharacters = opts.get(95 /* EditorOption.renderControlCharacters */);\n    const fontLigatures = opts.get(51 /* EditorOption.fontLigatures */);\n    const fontInfo = opts.get(50 /* EditorOption.fontInfo */);\n    const lineHeight = opts.get(67 /* EditorOption.lineHeight */);\n    const sb = new StringBuilder(10000);\n    sb.appendString('<div class=\"suggest-preview-text\">');\n    for (let i = 0, len = lines.length; i < len; i++) {\n        const lineData = lines[i];\n        const line = lineData.content;\n        sb.appendString('<div class=\"view-line');\n        sb.appendString('\" style=\"top:');\n        sb.appendString(String(i * lineHeight));\n        sb.appendString('px;width:1000000px;\">');\n        const isBasicASCII = strings.isBasicASCII(line);\n        const containsRTL = strings.containsRTL(line);\n        const lineTokens = LineTokens.createEmpty(line, languageIdCodec);\n        renderViewLine(new RenderLineInput((fontInfo.isMonospace && !disableMonospaceOptimizations), fontInfo.canUseHalfwidthRightwardsArrow, line, false, isBasicASCII, containsRTL, 0, lineTokens, lineData.decorations, tabSize, 0, fontInfo.spaceWidth, fontInfo.middotWidth, fontInfo.wsmiddotWidth, stopRenderingLineAfter, renderWhitespace, renderControlCharacters, fontLigatures !== EditorFontLigatures.OFF, null), sb);\n        sb.appendString('</div>');\n    }\n    sb.appendString('</div>');\n    applyFontInfo(domNode, fontInfo);\n    const html = sb.build();\n    const trustedhtml = ttPolicy ? ttPolicy.createHTML(html) : html;\n    domNode.innerHTML = trustedhtml;\n}\nexport const ttPolicy = createTrustedTypesPolicy('editorGhostText', { createHTML: value => value });\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,IAAIA,UAAU,GAAI,IAAI,IAAI,IAAI,CAACA,UAAU,IAAK,UAAUC,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAE;EACnF,IAAIC,CAAC,GAAGC,SAAS,CAACC,MAAM;IAAEC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGH,MAAM,GAAGE,IAAI,KAAK,IAAI,GAAGA,IAAI,GAAGK,MAAM,CAACC,wBAAwB,CAACR,MAAM,EAAEC,GAAG,CAAC,GAAGC,IAAI;IAAEO,CAAC;EAC5H,IAAI,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,CAACC,QAAQ,KAAK,UAAU,EAAEL,CAAC,GAAGI,OAAO,CAACC,QAAQ,CAACZ,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,CAAC,CAAC,KAC1H,KAAK,IAAIU,CAAC,GAAGb,UAAU,CAACM,MAAM,GAAG,CAAC,EAAEO,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE,IAAIH,CAAC,GAAGV,UAAU,CAACa,CAAC,CAAC,EAAEN,CAAC,GAAG,CAACH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACH,CAAC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACT,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,GAAGG,CAAC,CAACT,MAAM,EAAEC,GAAG,CAAC,KAAKK,CAAC;EACjJ,OAAOH,CAAC,GAAG,CAAC,IAAIG,CAAC,IAAIC,MAAM,CAACM,cAAc,CAACb,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,EAAEA,CAAC;AACjE,CAAC;AACD,IAAIQ,OAAO,GAAI,IAAI,IAAI,IAAI,CAACA,OAAO,IAAK,UAAUC,UAAU,EAAEC,SAAS,EAAE;EACrE,OAAO,UAAUhB,MAAM,EAAEC,GAAG,EAAE;IAAEe,SAAS,CAAChB,MAAM,EAAEC,GAAG,EAAEc,UAAU,CAAC;EAAE,CAAC;AACzE,CAAC;AACD,SAASE,wBAAwB,QAAQ,6CAA6C;AACtF,SAASC,KAAK,QAAQ,qCAAqC;AAC3D,SAASC,UAAU,EAAEC,YAAY,QAAQ,yCAAyC;AAClF,SAASC,OAAO,EAAEC,OAAO,EAAEC,mBAAmB,EAAEC,yBAAyB,EAAEC,eAAe,QAAQ,0CAA0C;AAC5I,OAAO,KAAKC,OAAO,MAAM,uCAAuC;AAChE,OAAO,qBAAqB;AAC5B,SAASC,aAAa,QAAQ,2CAA2C;AACzE,SAASC,mBAAmB,QAAQ,4CAA4C;AAChF,SAASC,QAAQ,QAAQ,qCAAqC;AAC9D,SAASC,KAAK,QAAQ,kCAAkC;AACxD,SAASC,aAAa,QAAQ,0CAA0C;AACxE,SAASC,gBAAgB,QAAQ,0CAA0C;AAC3E,SAASC,uBAAuB,QAAQ,6BAA6B;AACrE,SAASC,UAAU,QAAQ,yCAAyC;AACpE,SAASC,cAAc,QAAQ,kDAAkD;AACjF,SAASC,eAAe,EAAEC,cAAc,QAAQ,mDAAmD;AACnG,SAASC,oBAAoB,QAAQ,uBAAuB;AAC5D,SAASC,WAAW,EAAEC,0BAA0B,QAAQ,aAAa;AACrE,OAAO,MAAMC,sBAAsB,GAAG,YAAY;AAClD,IAAIC,aAAa,GAAG,MAAMA,aAAa,SAASvB,UAAU,CAAC;EACvDwB,WAAWA,CAACC,MAAM,EAAEC,KAAK,EAAEC,eAAe,EAAE;IACxC,KAAK,CAAC,CAAC;IACP,IAAI,CAACF,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,UAAU,GAAGtB,eAAe,CAAC,IAAI,EAAE,KAAK,CAAC;IAC9C,IAAI,CAACuB,gBAAgB,GAAGzB,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAACqB,MAAM,CAACK,gBAAgB,EAAE,MAAM,gCAAiC,IAAI,CAACL,MAAM,CAACM,QAAQ,CAAC,CAAC,CAAC;IAC9I,IAAI,CAACC,OAAO,GAAG7B,OAAO,CAAC,IAAI,EAAE8B,MAAM,IAAI;MACnC,IAAI,IAAI,CAACL,UAAU,CAACM,IAAI,CAACD,MAAM,CAAC,EAAE;QAC9B,OAAOE,SAAS;MACpB;MACA,MAAMC,SAAS,GAAG,IAAI,CAACP,gBAAgB,CAACK,IAAI,CAACD,MAAM,CAAC;MACpD,IAAIG,SAAS,KAAK,IAAI,CAACV,KAAK,CAACW,eAAe,CAACH,IAAI,CAACD,MAAM,CAAC,EAAE;QACvD,OAAOE,SAAS;MACpB;MACA,MAAMG,SAAS,GAAG,IAAI,CAACZ,KAAK,CAACY,SAAS,CAACJ,IAAI,CAACD,MAAM,CAAC;MACnD,IAAI,CAACK,SAAS,EAAE;QACZ,OAAOH,SAAS;MACpB;MACA,MAAMI,aAAa,GAAGD,SAAS,YAAYnB,oBAAoB,GAAGmB,SAAS,CAACE,WAAW,GAAGL,SAAS;MACnG,MAAMM,WAAW,GAAG,EAAE;MACtB,MAAMC,eAAe,GAAG,EAAE;MAC1B,SAASC,oBAAoBA,CAACC,KAAK,EAAEC,SAAS,EAAE;QAC5C,IAAIH,eAAe,CAACxD,MAAM,GAAG,CAAC,EAAE;UAC5B,MAAM4D,QAAQ,GAAGJ,eAAe,CAACA,eAAe,CAACxD,MAAM,GAAG,CAAC,CAAC;UAC5D,IAAI2D,SAAS,EAAE;YACXC,QAAQ,CAACC,WAAW,CAACC,IAAI,CAAC,IAAIhC,cAAc,CAAC8B,QAAQ,CAACG,OAAO,CAAC/D,MAAM,GAAG,CAAC,EAAE4D,QAAQ,CAACG,OAAO,CAAC/D,MAAM,GAAG,CAAC,GAAG0D,KAAK,CAAC,CAAC,CAAC,CAAC1D,MAAM,EAAE2D,SAAS,EAAE,CAAC,CAAC,kCAAkC,CAAC,CAAC;UAC9K;UACAC,QAAQ,CAACG,OAAO,IAAIL,KAAK,CAAC,CAAC,CAAC;UAC5BA,KAAK,GAAGA,KAAK,CAACM,KAAK,CAAC,CAAC,CAAC;QAC1B;QACA,KAAK,MAAMC,IAAI,IAAIP,KAAK,EAAE;UACtBF,eAAe,CAACM,IAAI,CAAC;YACjBC,OAAO,EAAEE,IAAI;YACbJ,WAAW,EAAEF,SAAS,GAAG,CAAC,IAAI7B,cAAc,CAAC,CAAC,EAAEmC,IAAI,CAACjE,MAAM,GAAG,CAAC,EAAE2D,SAAS,EAAE,CAAC,CAAC,kCAAkC,CAAC,CAAC,GAAG;UACzH,CAAC,CAAC;QACN;MACJ;MACA,MAAMO,cAAc,GAAGhB,SAAS,CAACiB,cAAc,CAACf,SAAS,CAACgB,UAAU,CAAC;MACrE,IAAIC,qBAAqB,GAAGpB,SAAS;MACrC,IAAIqB,OAAO,GAAG,CAAC;MACf,KAAK,MAAMC,IAAI,IAAInB,SAAS,CAACoB,KAAK,EAAE;QAChC,IAAId,KAAK,GAAGa,IAAI,CAACb,KAAK;QACtB,IAAIW,qBAAqB,KAAKpB,SAAS,EAAE;UACrCM,WAAW,CAACO,IAAI,CAAC;YACbW,MAAM,EAAEF,IAAI,CAACE,MAAM;YACnBC,IAAI,EAAEhB,KAAK,CAAC,CAAC,CAAC;YACdiB,OAAO,EAAEJ,IAAI,CAACI;UAClB,CAAC,CAAC;UACFjB,KAAK,GAAGA,KAAK,CAACM,KAAK,CAAC,CAAC,CAAC;QAC1B,CAAC,MACI;UACDP,oBAAoB,CAAC,CAACS,cAAc,CAACU,SAAS,CAACN,OAAO,EAAEC,IAAI,CAACE,MAAM,GAAG,CAAC,CAAC,CAAC,EAAExB,SAAS,CAAC;QACzF;QACA,IAAIS,KAAK,CAAC1D,MAAM,GAAG,CAAC,EAAE;UAClByD,oBAAoB,CAACC,KAAK,EAAEtB,sBAAsB,CAAC;UACnD,IAAIiC,qBAAqB,KAAKpB,SAAS,IAAIsB,IAAI,CAACE,MAAM,IAAIP,cAAc,CAAClE,MAAM,EAAE;YAC7EqE,qBAAqB,GAAGE,IAAI,CAACE,MAAM;UACvC;QACJ;QACAH,OAAO,GAAGC,IAAI,CAACE,MAAM,GAAG,CAAC;MAC7B;MACA,IAAIJ,qBAAqB,KAAKpB,SAAS,EAAE;QACrCQ,oBAAoB,CAAC,CAACS,cAAc,CAACU,SAAS,CAACN,OAAO,CAAC,CAAC,EAAErB,SAAS,CAAC;MACxE;MACA,MAAM4B,WAAW,GAAGR,qBAAqB,KAAKpB,SAAS,GAAG,IAAIf,WAAW,CAACmC,qBAAqB,EAAEH,cAAc,CAAClE,MAAM,GAAG,CAAC,CAAC,GAAGiD,SAAS;MACvI,OAAO;QACHI,aAAa;QACbE,WAAW;QACXC,eAAe;QACfqB,WAAW;QACXT,UAAU,EAAEhB,SAAS,CAACgB,UAAU;QAChCU,2BAA2B,EAAE,IAAI,CAACtC,KAAK,CAACuC,oBAAoB,CAAC/B,IAAI,CAACD,MAAM,CAAC;QACzEI,eAAe,EAAED;MACrB,CAAC;IACL,CAAC,CAAC;IACF,IAAI,CAACW,WAAW,GAAG5C,OAAO,CAAC,IAAI,EAAE8B,MAAM,IAAI;MACvC,MAAMD,OAAO,GAAG,IAAI,CAACA,OAAO,CAACE,IAAI,CAACD,MAAM,CAAC;MACzC,IAAI,CAACD,OAAO,EAAE;QACV,OAAO,EAAE;MACb;MACA,MAAMe,WAAW,GAAG,EAAE;MACtB,IAAIf,OAAO,CAACO,aAAa,EAAE;QACvBQ,WAAW,CAACC,IAAI,CAAC;UACbkB,KAAK,EAAElC,OAAO,CAACO,aAAa,CAAC4B,OAAO,CAACnC,OAAO,CAACsB,UAAU,CAAC;UACxDc,OAAO,EAAE;YAAEC,eAAe,EAAE,mCAAmC;YAAEC,WAAW,EAAE;UAAuB;QACzG,CAAC,CAAC;MACN;MACA,IAAItC,OAAO,CAAC+B,WAAW,EAAE;QACrBhB,WAAW,CAACC,IAAI,CAAC;UACbkB,KAAK,EAAElC,OAAO,CAAC+B,WAAW,CAACI,OAAO,CAACnC,OAAO,CAACsB,UAAU,CAAC;UACtDc,OAAO,EAAE;YAAEC,eAAe,EAAE,mBAAmB;YAAEC,WAAW,EAAE;UAAqB;QACvF,CAAC,CAAC;MACN;MACA,KAAK,MAAMC,CAAC,IAAIvC,OAAO,CAACS,WAAW,EAAE;QACjCM,WAAW,CAACC,IAAI,CAAC;UACbkB,KAAK,EAAEvD,KAAK,CAAC6D,aAAa,CAAC,IAAI9D,QAAQ,CAACsB,OAAO,CAACsB,UAAU,EAAEiB,CAAC,CAACZ,MAAM,CAAC,CAAC;UACtES,OAAO,EAAE;YACLE,WAAW,EAAEhD,sBAAsB;YACnCmD,KAAK,EAAE;cAAExB,OAAO,EAAEsB,CAAC,CAACX,IAAI;cAAES,eAAe,EAAEE,CAAC,CAACV,OAAO,GAAG,+BAA+B,GAAG,uBAAuB;cAAEa,WAAW,EAAE5D,uBAAuB,CAAC6D;YAAK,CAAC;YAC7JC,eAAe,EAAE;UACrB;QACJ,CAAC,CAAC;MACN;MACA,OAAO7B,WAAW;IACtB,CAAC,CAAC;IACF,IAAI,CAAC8B,qBAAqB,GAAG,IAAI,CAACC,SAAS,CAAC,IAAIC,qBAAqB,CAAC,IAAI,CAACtD,MAAM,EAAE,IAAI,CAACE,eAAe,CAACqD,eAAe,EAAE7E,OAAO,CAAC8B,MAAM,IAAI;MACvI;MACA,MAAMD,OAAO,GAAG,IAAI,CAACA,OAAO,CAACE,IAAI,CAACD,MAAM,CAAC;MACzC,OAAOD,OAAO,GAAG;QACbsB,UAAU,EAAEtB,OAAO,CAACsB,UAAU;QAC9BZ,eAAe,EAAEV,OAAO,CAACU,eAAe;QACxCuB,oBAAoB,EAAEjC,OAAO,CAACgC,2BAA2B;QACzD3B,eAAe,EAAEL,OAAO,CAACK;MAC7B,CAAC,GAAGF,SAAS;IACjB,CAAC,CAAC,CAAC,CAAC;IACJ,IAAI,CAAC2C,SAAS,CAAC7E,YAAY,CAAC,MAAM;MAAE,IAAI,CAAC2B,UAAU,CAACqD,GAAG,CAAC,IAAI,EAAE9C,SAAS,CAAC;IAAE,CAAC,CAAC,CAAC;IAC7E,IAAI,CAAC2C,SAAS,CAACzD,0BAA0B,CAAC,IAAI,CAACI,MAAM,EAAE,IAAI,CAACsB,WAAW,CAAC,CAAC;EAC7E;EACAmC,YAAYA,CAACC,UAAU,EAAE;IACrB,OAAO,IAAI,CAACN,qBAAqB,CAACM,UAAU,KAAKA,UAAU;EAC/D;AACJ,CAAC;AACD5D,aAAa,GAAG5C,UAAU,CAAC,CACvBgB,OAAO,CAAC,CAAC,EAAEkB,gBAAgB,CAAC,CAC/B,EAAEU,aAAa,CAAC;AACjB,SAASA,aAAa;AACtB,OAAO,MAAMwD,qBAAqB,SAAS/E,UAAU,CAAC;EAClD,IAAImF,UAAUA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACC,WAAW;EAAE;EAC5C5D,WAAWA,CAACC,MAAM,EAAEuD,eAAe,EAAEpC,KAAK,EAAE;IACxC,KAAK,CAAC,CAAC;IACP,IAAI,CAACnB,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACuD,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACpC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACwC,WAAW,GAAGjD,SAAS;IAC5B,IAAI,CAACkD,oBAAoB,GAAGhF,yBAAyB,CAAC,qBAAqB,EAAEN,KAAK,CAACuF,MAAM,CAAC,IAAI,CAAC7D,MAAM,CAAC8D,wBAAwB,EAAEC,CAAC,IAAIA,CAAC,CAACC,UAAU,CAAC,EAAE,CAAC,gDAAgD,CAAC,IAC/LD,CAAC,CAACC,UAAU,CAAC,GAAG,CAAC,yCAAyC,CAAC,IAC3DD,CAAC,CAACC,UAAU,CAAC,GAAG,CAAC,mCAAmC,CAAC,IACrDD,CAAC,CAACC,UAAU,CAAC,EAAE,CAAC,0CAA0C,CAAC,IAC3DD,CAAC,CAACC,UAAU,CAAC,EAAE,CAAC,gCAAgC,CAAC,IACjDD,CAAC,CAACC,UAAU,CAAC,EAAE,CAAC,2BAA2B,CAAC,IAC5CD,CAAC,CAACC,UAAU,CAAC,EAAE,CAAC,6BAA6B,CAAC,CAAC,CAAC;IACvD,IAAI,CAACX,SAAS,CAAC5E,OAAO,CAAC+B,MAAM,IAAI;MAC7B;MACA,MAAMW,KAAK,GAAG,IAAI,CAACA,KAAK,CAACV,IAAI,CAACD,MAAM,CAAC;MACrC,IAAI,CAACoD,oBAAoB,CAACnD,IAAI,CAACD,MAAM,CAAC;MACtC,IAAIW,KAAK,EAAE;QACP,IAAI,CAAC8C,WAAW,CAAC9C,KAAK,CAACU,UAAU,EAAEV,KAAK,CAACF,eAAe,EAAEE,KAAK,CAACqB,oBAAoB,CAAC;MACzF,CAAC,MACI;QACD,IAAI,CAAC0B,KAAK,CAAC,CAAC;MAChB;IACJ,CAAC,CAAC,CAAC;EACP;EACAC,OAAOA,CAAA,EAAG;IACN,KAAK,CAACA,OAAO,CAAC,CAAC;IACf,IAAI,CAACD,KAAK,CAAC,CAAC;EAChB;EACAA,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAClE,MAAM,CAACoE,eAAe,CAAEC,cAAc,IAAK;MAC5C,IAAI,IAAI,CAACV,WAAW,EAAE;QAClBU,cAAc,CAACC,UAAU,CAAC,IAAI,CAACX,WAAW,CAAC;QAC3C,IAAI,CAACA,WAAW,GAAGjD,SAAS;MAChC;IACJ,CAAC,CAAC;EACN;EACAuD,WAAWA,CAACpC,UAAU,EAAEZ,eAAe,EAAEuB,oBAAoB,EAAE;IAC3D,MAAM7B,SAAS,GAAG,IAAI,CAACX,MAAM,CAACM,QAAQ,CAAC,CAAC;IACxC,IAAI,CAACK,SAAS,EAAE;MACZ;IACJ;IACA,MAAM;MAAE4D;IAAQ,CAAC,GAAG5D,SAAS,CAAC6D,UAAU,CAAC,CAAC;IAC1C,IAAI,CAACxE,MAAM,CAACoE,eAAe,CAAEC,cAAc,IAAK;MAC5C,IAAI,IAAI,CAACV,WAAW,EAAE;QAClBU,cAAc,CAACC,UAAU,CAAC,IAAI,CAACX,WAAW,CAAC;QAC3C,IAAI,CAACA,WAAW,GAAGjD,SAAS;MAChC;MACA,MAAM+D,aAAa,GAAGC,IAAI,CAACC,GAAG,CAAC1D,eAAe,CAACxD,MAAM,EAAE+E,oBAAoB,CAAC;MAC5E,IAAIiC,aAAa,GAAG,CAAC,EAAE;QACnB,MAAMG,OAAO,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QAC7CC,WAAW,CAACH,OAAO,EAAEL,OAAO,EAAEtD,eAAe,EAAE,IAAI,CAACjB,MAAM,CAACwE,UAAU,CAAC,CAAC,EAAE,IAAI,CAACjB,eAAe,CAAC;QAC9F,IAAI,CAACI,WAAW,GAAGU,cAAc,CAACW,OAAO,CAAC;UACtCC,eAAe,EAAEpD,UAAU;UAC3B4C,aAAa,EAAEA,aAAa;UAC5BG,OAAO;UACPM,mBAAmB,EAAE,CAAC,CAAC;QAC3B,CAAC,CAAC;MACN;IACJ,CAAC,CAAC;EACN;AACJ;AACA,SAASH,WAAWA,CAACH,OAAO,EAAEL,OAAO,EAAEpD,KAAK,EAAEgE,IAAI,EAAE5B,eAAe,EAAE;EACjE,MAAM6B,6BAA6B,GAAGD,IAAI,CAACE,GAAG,CAAC,EAAE,CAAC,gDAAgD,CAAC;EACnG,MAAMC,sBAAsB,GAAGH,IAAI,CAACE,GAAG,CAAC,GAAG,CAAC,yCAAyC,CAAC;EACtF;EACA,MAAME,gBAAgB,GAAG,MAAM;EAC/B,MAAMC,uBAAuB,GAAGL,IAAI,CAACE,GAAG,CAAC,EAAE,CAAC,0CAA0C,CAAC;EACvF,MAAMI,aAAa,GAAGN,IAAI,CAACE,GAAG,CAAC,EAAE,CAAC,gCAAgC,CAAC;EACnE,MAAMK,QAAQ,GAAGP,IAAI,CAACE,GAAG,CAAC,EAAE,CAAC,2BAA2B,CAAC;EACzD,MAAMM,UAAU,GAAGR,IAAI,CAACE,GAAG,CAAC,EAAE,CAAC,6BAA6B,CAAC;EAC7D,MAAMO,EAAE,GAAG,IAAIzG,aAAa,CAAC,KAAK,CAAC;EACnCyG,EAAE,CAACC,YAAY,CAAC,oCAAoC,CAAC;EACrD,KAAK,IAAI7H,CAAC,GAAG,CAAC,EAAE8H,GAAG,GAAG3E,KAAK,CAAC1D,MAAM,EAAEO,CAAC,GAAG8H,GAAG,EAAE9H,CAAC,EAAE,EAAE;IAC9C,MAAM+H,QAAQ,GAAG5E,KAAK,CAACnD,CAAC,CAAC;IACzB,MAAM0D,IAAI,GAAGqE,QAAQ,CAACvE,OAAO;IAC7BoE,EAAE,CAACC,YAAY,CAAC,uBAAuB,CAAC;IACxCD,EAAE,CAACC,YAAY,CAAC,eAAe,CAAC;IAChCD,EAAE,CAACC,YAAY,CAACG,MAAM,CAAChI,CAAC,GAAG2H,UAAU,CAAC,CAAC;IACvCC,EAAE,CAACC,YAAY,CAAC,uBAAuB,CAAC;IACxC,MAAMI,YAAY,GAAGnH,OAAO,CAACmH,YAAY,CAACvE,IAAI,CAAC;IAC/C,MAAMwE,WAAW,GAAGpH,OAAO,CAACoH,WAAW,CAACxE,IAAI,CAAC;IAC7C,MAAMyE,UAAU,GAAG7G,UAAU,CAAC8G,WAAW,CAAC1E,IAAI,EAAE6B,eAAe,CAAC;IAChE9D,cAAc,CAAC,IAAID,eAAe,CAAEkG,QAAQ,CAACW,WAAW,IAAI,CAACjB,6BAA6B,EAAGM,QAAQ,CAACY,8BAA8B,EAAE5E,IAAI,EAAE,KAAK,EAAEuE,YAAY,EAAEC,WAAW,EAAE,CAAC,EAAEC,UAAU,EAAEJ,QAAQ,CAACzE,WAAW,EAAEiD,OAAO,EAAE,CAAC,EAAEmB,QAAQ,CAACa,UAAU,EAAEb,QAAQ,CAACc,WAAW,EAAEd,QAAQ,CAACe,aAAa,EAAEnB,sBAAsB,EAAEC,gBAAgB,EAAEC,uBAAuB,EAAEC,aAAa,KAAKzG,mBAAmB,CAAC0H,GAAG,EAAE,IAAI,CAAC,EAAEd,EAAE,CAAC;IAC1ZA,EAAE,CAACC,YAAY,CAAC,QAAQ,CAAC;EAC7B;EACAD,EAAE,CAACC,YAAY,CAAC,QAAQ,CAAC;EACzB9G,aAAa,CAAC6F,OAAO,EAAEc,QAAQ,CAAC;EAChC,MAAMiB,IAAI,GAAGf,EAAE,CAACgB,KAAK,CAAC,CAAC;EACvB,MAAMC,WAAW,GAAGC,QAAQ,GAAGA,QAAQ,CAACC,UAAU,CAACJ,IAAI,CAAC,GAAGA,IAAI;EAC/D/B,OAAO,CAACoC,SAAS,GAAGH,WAAW;AACnC;AACA,OAAO,MAAMC,QAAQ,GAAGzI,wBAAwB,CAAC,iBAAiB,EAAE;EAAE0I,UAAU,EAAEE,KAAK,IAAIA;AAAM,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}