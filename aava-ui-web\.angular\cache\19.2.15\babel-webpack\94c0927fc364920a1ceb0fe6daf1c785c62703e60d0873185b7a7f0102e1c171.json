{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { forEachWithNeighbors } from '../../../../base/common/arrays.js';\nimport { OffsetRange } from '../../core/offsetRange.js';\nimport { OffsetPair, SequenceDiff } from './algorithms/diffAlgorithm.js';\nexport function optimizeSequenceDiffs(sequence1, sequence2, sequenceDiffs) {\n  let result = sequenceDiffs;\n  result = joinSequenceDiffsByShifting(sequence1, sequence2, result);\n  // Sometimes, calling this function twice improves the result.\n  // Uncomment the second invocation and run the tests to see the difference.\n  result = joinSequenceDiffsByShifting(sequence1, sequence2, result);\n  result = shiftSequenceDiffs(sequence1, sequence2, result);\n  return result;\n}\n/**\n * This function fixes issues like this:\n * ```\n * import { Baz, Bar } from \"foo\";\n * ```\n * <->\n * ```\n * import { Baz, Bar, Foo } from \"foo\";\n * ```\n * Computed diff: [ {Add \",\" after Bar}, {Add \"Foo \" after space} }\n * Improved diff: [{Add \", Foo\" after Bar}]\n */\nfunction joinSequenceDiffsByShifting(sequence1, sequence2, sequenceDiffs) {\n  if (sequenceDiffs.length === 0) {\n    return sequenceDiffs;\n  }\n  const result = [];\n  result.push(sequenceDiffs[0]);\n  // First move them all to the left as much as possible and join them if possible\n  for (let i = 1; i < sequenceDiffs.length; i++) {\n    const prevResult = result[result.length - 1];\n    let cur = sequenceDiffs[i];\n    if (cur.seq1Range.isEmpty || cur.seq2Range.isEmpty) {\n      const length = cur.seq1Range.start - prevResult.seq1Range.endExclusive;\n      let d;\n      for (d = 1; d <= length; d++) {\n        if (sequence1.getElement(cur.seq1Range.start - d) !== sequence1.getElement(cur.seq1Range.endExclusive - d) || sequence2.getElement(cur.seq2Range.start - d) !== sequence2.getElement(cur.seq2Range.endExclusive - d)) {\n          break;\n        }\n      }\n      d--;\n      if (d === length) {\n        // Merge previous and current diff\n        result[result.length - 1] = new SequenceDiff(new OffsetRange(prevResult.seq1Range.start, cur.seq1Range.endExclusive - length), new OffsetRange(prevResult.seq2Range.start, cur.seq2Range.endExclusive - length));\n        continue;\n      }\n      cur = cur.delta(-d);\n    }\n    result.push(cur);\n  }\n  const result2 = [];\n  // Then move them all to the right and join them again if possible\n  for (let i = 0; i < result.length - 1; i++) {\n    const nextResult = result[i + 1];\n    let cur = result[i];\n    if (cur.seq1Range.isEmpty || cur.seq2Range.isEmpty) {\n      const length = nextResult.seq1Range.start - cur.seq1Range.endExclusive;\n      let d;\n      for (d = 0; d < length; d++) {\n        if (!sequence1.isStronglyEqual(cur.seq1Range.start + d, cur.seq1Range.endExclusive + d) || !sequence2.isStronglyEqual(cur.seq2Range.start + d, cur.seq2Range.endExclusive + d)) {\n          break;\n        }\n      }\n      if (d === length) {\n        // Merge previous and current diff, write to result!\n        result[i + 1] = new SequenceDiff(new OffsetRange(cur.seq1Range.start + length, nextResult.seq1Range.endExclusive), new OffsetRange(cur.seq2Range.start + length, nextResult.seq2Range.endExclusive));\n        continue;\n      }\n      if (d > 0) {\n        cur = cur.delta(d);\n      }\n    }\n    result2.push(cur);\n  }\n  if (result.length > 0) {\n    result2.push(result[result.length - 1]);\n  }\n  return result2;\n}\n// align character level diffs at whitespace characters\n// import { IBar } from \"foo\";\n// import { I[Arr, I]Bar } from \"foo\";\n// ->\n// import { [IArr, ]IBar } from \"foo\";\n// import { ITransaction, observableValue, transaction } from 'vs/base/common/observable';\n// import { ITransaction, observable[FromEvent, observable]Value, transaction } from 'vs/base/common/observable';\n// ->\n// import { ITransaction, [observableFromEvent, ]observableValue, transaction } from 'vs/base/common/observable';\n// collectBrackets(level + 1, levelPerBracketType);\n// collectBrackets(level + 1, levelPerBracket[ + 1, levelPerBracket]Type);\n// ->\n// collectBrackets(level + 1, [levelPerBracket + 1, ]levelPerBracketType);\nfunction shiftSequenceDiffs(sequence1, sequence2, sequenceDiffs) {\n  if (!sequence1.getBoundaryScore || !sequence2.getBoundaryScore) {\n    return sequenceDiffs;\n  }\n  for (let i = 0; i < sequenceDiffs.length; i++) {\n    const prevDiff = i > 0 ? sequenceDiffs[i - 1] : undefined;\n    const diff = sequenceDiffs[i];\n    const nextDiff = i + 1 < sequenceDiffs.length ? sequenceDiffs[i + 1] : undefined;\n    const seq1ValidRange = new OffsetRange(prevDiff ? prevDiff.seq1Range.endExclusive + 1 : 0, nextDiff ? nextDiff.seq1Range.start - 1 : sequence1.length);\n    const seq2ValidRange = new OffsetRange(prevDiff ? prevDiff.seq2Range.endExclusive + 1 : 0, nextDiff ? nextDiff.seq2Range.start - 1 : sequence2.length);\n    if (diff.seq1Range.isEmpty) {\n      sequenceDiffs[i] = shiftDiffToBetterPosition(diff, sequence1, sequence2, seq1ValidRange, seq2ValidRange);\n    } else if (diff.seq2Range.isEmpty) {\n      sequenceDiffs[i] = shiftDiffToBetterPosition(diff.swap(), sequence2, sequence1, seq2ValidRange, seq1ValidRange).swap();\n    }\n  }\n  return sequenceDiffs;\n}\nfunction shiftDiffToBetterPosition(diff, sequence1, sequence2, seq1ValidRange, seq2ValidRange) {\n  const maxShiftLimit = 100; // To prevent performance issues\n  // don't touch previous or next!\n  let deltaBefore = 1;\n  while (diff.seq1Range.start - deltaBefore >= seq1ValidRange.start && diff.seq2Range.start - deltaBefore >= seq2ValidRange.start && sequence2.isStronglyEqual(diff.seq2Range.start - deltaBefore, diff.seq2Range.endExclusive - deltaBefore) && deltaBefore < maxShiftLimit) {\n    deltaBefore++;\n  }\n  deltaBefore--;\n  let deltaAfter = 0;\n  while (diff.seq1Range.start + deltaAfter < seq1ValidRange.endExclusive && diff.seq2Range.endExclusive + deltaAfter < seq2ValidRange.endExclusive && sequence2.isStronglyEqual(diff.seq2Range.start + deltaAfter, diff.seq2Range.endExclusive + deltaAfter) && deltaAfter < maxShiftLimit) {\n    deltaAfter++;\n  }\n  if (deltaBefore === 0 && deltaAfter === 0) {\n    return diff;\n  }\n  // Visualize `[sequence1.text, diff.seq1Range.start + deltaAfter]`\n  // and `[sequence2.text, diff.seq2Range.start + deltaAfter, diff.seq2Range.endExclusive + deltaAfter]`\n  let bestDelta = 0;\n  let bestScore = -1;\n  // find best scored delta\n  for (let delta = -deltaBefore; delta <= deltaAfter; delta++) {\n    const seq2OffsetStart = diff.seq2Range.start + delta;\n    const seq2OffsetEndExclusive = diff.seq2Range.endExclusive + delta;\n    const seq1Offset = diff.seq1Range.start + delta;\n    const score = sequence1.getBoundaryScore(seq1Offset) + sequence2.getBoundaryScore(seq2OffsetStart) + sequence2.getBoundaryScore(seq2OffsetEndExclusive);\n    if (score > bestScore) {\n      bestScore = score;\n      bestDelta = delta;\n    }\n  }\n  return diff.delta(bestDelta);\n}\nexport function removeShortMatches(sequence1, sequence2, sequenceDiffs) {\n  const result = [];\n  for (const s of sequenceDiffs) {\n    const last = result[result.length - 1];\n    if (!last) {\n      result.push(s);\n      continue;\n    }\n    if (s.seq1Range.start - last.seq1Range.endExclusive <= 2 || s.seq2Range.start - last.seq2Range.endExclusive <= 2) {\n      result[result.length - 1] = new SequenceDiff(last.seq1Range.join(s.seq1Range), last.seq2Range.join(s.seq2Range));\n    } else {\n      result.push(s);\n    }\n  }\n  return result;\n}\nexport function extendDiffsToEntireWordIfAppropriate(sequence1, sequence2, sequenceDiffs) {\n  const equalMappings = SequenceDiff.invert(sequenceDiffs, sequence1.length);\n  const additional = [];\n  let lastPoint = new OffsetPair(0, 0);\n  function scanWord(pair, equalMapping) {\n    if (pair.offset1 < lastPoint.offset1 || pair.offset2 < lastPoint.offset2) {\n      return;\n    }\n    const w1 = sequence1.findWordContaining(pair.offset1);\n    const w2 = sequence2.findWordContaining(pair.offset2);\n    if (!w1 || !w2) {\n      return;\n    }\n    let w = new SequenceDiff(w1, w2);\n    const equalPart = w.intersect(equalMapping);\n    let equalChars1 = equalPart.seq1Range.length;\n    let equalChars2 = equalPart.seq2Range.length;\n    // The words do not touch previous equals mappings, as we would have processed them already.\n    // But they might touch the next ones.\n    while (equalMappings.length > 0) {\n      const next = equalMappings[0];\n      const intersects = next.seq1Range.intersects(w.seq1Range) || next.seq2Range.intersects(w.seq2Range);\n      if (!intersects) {\n        break;\n      }\n      const v1 = sequence1.findWordContaining(next.seq1Range.start);\n      const v2 = sequence2.findWordContaining(next.seq2Range.start);\n      // Because there is an intersection, we know that the words are not empty.\n      const v = new SequenceDiff(v1, v2);\n      const equalPart = v.intersect(next);\n      equalChars1 += equalPart.seq1Range.length;\n      equalChars2 += equalPart.seq2Range.length;\n      w = w.join(v);\n      if (w.seq1Range.endExclusive >= next.seq1Range.endExclusive) {\n        // The word extends beyond the next equal mapping.\n        equalMappings.shift();\n      } else {\n        break;\n      }\n    }\n    if (equalChars1 + equalChars2 < (w.seq1Range.length + w.seq2Range.length) * 2 / 3) {\n      additional.push(w);\n    }\n    lastPoint = w.getEndExclusives();\n  }\n  while (equalMappings.length > 0) {\n    const next = equalMappings.shift();\n    if (next.seq1Range.isEmpty) {\n      continue;\n    }\n    scanWord(next.getStarts(), next);\n    // The equal parts are not empty, so -1 gives us a character that is equal in both parts.\n    scanWord(next.getEndExclusives().delta(-1), next);\n  }\n  const merged = mergeSequenceDiffs(sequenceDiffs, additional);\n  return merged;\n}\nfunction mergeSequenceDiffs(sequenceDiffs1, sequenceDiffs2) {\n  const result = [];\n  while (sequenceDiffs1.length > 0 || sequenceDiffs2.length > 0) {\n    const sd1 = sequenceDiffs1[0];\n    const sd2 = sequenceDiffs2[0];\n    let next;\n    if (sd1 && (!sd2 || sd1.seq1Range.start < sd2.seq1Range.start)) {\n      next = sequenceDiffs1.shift();\n    } else {\n      next = sequenceDiffs2.shift();\n    }\n    if (result.length > 0 && result[result.length - 1].seq1Range.endExclusive >= next.seq1Range.start) {\n      result[result.length - 1] = result[result.length - 1].join(next);\n    } else {\n      result.push(next);\n    }\n  }\n  return result;\n}\nexport function removeVeryShortMatchingLinesBetweenDiffs(sequence1, _sequence2, sequenceDiffs) {\n  let diffs = sequenceDiffs;\n  if (diffs.length === 0) {\n    return diffs;\n  }\n  let counter = 0;\n  let shouldRepeat;\n  do {\n    shouldRepeat = false;\n    const result = [diffs[0]];\n    for (let i = 1; i < diffs.length; i++) {\n      const cur = diffs[i];\n      const lastResult = result[result.length - 1];\n      function shouldJoinDiffs(before, after) {\n        const unchangedRange = new OffsetRange(lastResult.seq1Range.endExclusive, cur.seq1Range.start);\n        const unchangedText = sequence1.getText(unchangedRange);\n        const unchangedTextWithoutWs = unchangedText.replace(/\\s/g, '');\n        if (unchangedTextWithoutWs.length <= 4 && (before.seq1Range.length + before.seq2Range.length > 5 || after.seq1Range.length + after.seq2Range.length > 5)) {\n          return true;\n        }\n        return false;\n      }\n      const shouldJoin = shouldJoinDiffs(lastResult, cur);\n      if (shouldJoin) {\n        shouldRepeat = true;\n        result[result.length - 1] = result[result.length - 1].join(cur);\n      } else {\n        result.push(cur);\n      }\n    }\n    diffs = result;\n  } while (counter++ < 10 && shouldRepeat);\n  return diffs;\n}\nexport function removeVeryShortMatchingTextBetweenLongDiffs(sequence1, sequence2, sequenceDiffs) {\n  let diffs = sequenceDiffs;\n  if (diffs.length === 0) {\n    return diffs;\n  }\n  let counter = 0;\n  let shouldRepeat;\n  do {\n    shouldRepeat = false;\n    const result = [diffs[0]];\n    for (let i = 1; i < diffs.length; i++) {\n      const cur = diffs[i];\n      const lastResult = result[result.length - 1];\n      function shouldJoinDiffs(before, after) {\n        const unchangedRange = new OffsetRange(lastResult.seq1Range.endExclusive, cur.seq1Range.start);\n        const unchangedLineCount = sequence1.countLinesIn(unchangedRange);\n        if (unchangedLineCount > 5 || unchangedRange.length > 500) {\n          return false;\n        }\n        const unchangedText = sequence1.getText(unchangedRange).trim();\n        if (unchangedText.length > 20 || unchangedText.split(/\\r\\n|\\r|\\n/).length > 1) {\n          return false;\n        }\n        const beforeLineCount1 = sequence1.countLinesIn(before.seq1Range);\n        const beforeSeq1Length = before.seq1Range.length;\n        const beforeLineCount2 = sequence2.countLinesIn(before.seq2Range);\n        const beforeSeq2Length = before.seq2Range.length;\n        const afterLineCount1 = sequence1.countLinesIn(after.seq1Range);\n        const afterSeq1Length = after.seq1Range.length;\n        const afterLineCount2 = sequence2.countLinesIn(after.seq2Range);\n        const afterSeq2Length = after.seq2Range.length;\n        // TODO: Maybe a neural net can be used to derive the result from these numbers\n        const max = 2 * 40 + 50;\n        function cap(v) {\n          return Math.min(v, max);\n        }\n        if (Math.pow(Math.pow(cap(beforeLineCount1 * 40 + beforeSeq1Length), 1.5) + Math.pow(cap(beforeLineCount2 * 40 + beforeSeq2Length), 1.5), 1.5) + Math.pow(Math.pow(cap(afterLineCount1 * 40 + afterSeq1Length), 1.5) + Math.pow(cap(afterLineCount2 * 40 + afterSeq2Length), 1.5), 1.5) > (max ** 1.5) ** 1.5 * 1.3) {\n          return true;\n        }\n        return false;\n      }\n      const shouldJoin = shouldJoinDiffs(lastResult, cur);\n      if (shouldJoin) {\n        shouldRepeat = true;\n        result[result.length - 1] = result[result.length - 1].join(cur);\n      } else {\n        result.push(cur);\n      }\n    }\n    diffs = result;\n  } while (counter++ < 10 && shouldRepeat);\n  const newDiffs = [];\n  // Remove short suffixes/prefixes\n  forEachWithNeighbors(diffs, (prev, cur, next) => {\n    let newDiff = cur;\n    function shouldMarkAsChanged(text) {\n      return text.length > 0 && text.trim().length <= 3 && cur.seq1Range.length + cur.seq2Range.length > 100;\n    }\n    const fullRange1 = sequence1.extendToFullLines(cur.seq1Range);\n    const prefix = sequence1.getText(new OffsetRange(fullRange1.start, cur.seq1Range.start));\n    if (shouldMarkAsChanged(prefix)) {\n      newDiff = newDiff.deltaStart(-prefix.length);\n    }\n    const suffix = sequence1.getText(new OffsetRange(cur.seq1Range.endExclusive, fullRange1.endExclusive));\n    if (shouldMarkAsChanged(suffix)) {\n      newDiff = newDiff.deltaEnd(suffix.length);\n    }\n    const availableSpace = SequenceDiff.fromOffsetPairs(prev ? prev.getEndExclusives() : OffsetPair.zero, next ? next.getStarts() : OffsetPair.max);\n    const result = newDiff.intersect(availableSpace);\n    if (newDiffs.length > 0 && result.getStarts().equals(newDiffs[newDiffs.length - 1].getEndExclusives())) {\n      newDiffs[newDiffs.length - 1] = newDiffs[newDiffs.length - 1].join(result);\n    } else {\n      newDiffs.push(result);\n    }\n  });\n  return newDiffs;\n}", "map": {"version": 3, "names": ["forEachWithNeighbors", "OffsetRange", "OffsetPair", "SequenceDiff", "optimizeSequenceDiffs", "sequence1", "sequence2", "sequenceDiffs", "result", "joinSequenceDiffsByShifting", "shiftSequenceDiffs", "length", "push", "i", "prevResult", "cur", "seq1Range", "isEmpty", "seq2Range", "start", "endExclusive", "d", "getElement", "delta", "result2", "nextResult", "isStronglyEqual", "getBoundaryScore", "prevDiff", "undefined", "diff", "nextDiff", "seq1ValidRange", "seq2ValidRange", "shiftDiffToBetterPosition", "swap", "maxShiftLimit", "deltaBefore", "deltaAfter", "best<PERSON><PERSON><PERSON>", "bestScore", "seq2OffsetStart", "seq2OffsetEndExclusive", "seq1Offset", "score", "removeShortMatches", "s", "last", "join", "extendDiffsToEntireWordIfAppropriate", "equalMappings", "invert", "additional", "lastPoint", "scanWord", "pair", "equalMapping", "offset1", "offset2", "w1", "findWordContaining", "w2", "w", "equalPart", "intersect", "equalChars1", "equalChars2", "next", "intersects", "v1", "v2", "v", "shift", "getEndExclusives", "getStarts", "merged", "mergeSequenceDiffs", "sequenceDiffs1", "sequenceDiffs2", "sd1", "sd2", "removeVeryShortMatchingLinesBetweenDiffs", "_sequence2", "diffs", "counter", "shouldRepeat", "lastResult", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "before", "after", "unchangedRange", "unchangedText", "getText", "unchangedTextWithoutWs", "replace", "<PERSON><PERSON><PERSON><PERSON>", "removeVeryShortMatchingTextBetweenLongDiffs", "unchangedLineCount", "countLinesIn", "trim", "split", "beforeLineCount1", "beforeSeq1Length", "beforeLineCount2", "beforeSeq2Length", "afterLineCount1", "afterSeq1Length", "afterLineCount2", "afterSeq2Length", "max", "cap", "Math", "min", "pow", "newDiffs", "prev", "newDiff", "shouldMarkAsChanged", "text", "fullRange1", "extendToFullLines", "prefix", "deltaStart", "suffix", "deltaEnd", "availableSpace", "fromOffsetPairs", "zero", "equals"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/diff/defaultLinesDiffComputer/heuristicSequenceOptimizations.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { forEachWithNeighbors } from '../../../../base/common/arrays.js';\nimport { OffsetRange } from '../../core/offsetRange.js';\nimport { OffsetPair, SequenceDiff } from './algorithms/diffAlgorithm.js';\nexport function optimizeSequenceDiffs(sequence1, sequence2, sequenceDiffs) {\n    let result = sequenceDiffs;\n    result = joinSequenceDiffsByShifting(sequence1, sequence2, result);\n    // Sometimes, calling this function twice improves the result.\n    // Uncomment the second invocation and run the tests to see the difference.\n    result = joinSequenceDiffsByShifting(sequence1, sequence2, result);\n    result = shiftSequenceDiffs(sequence1, sequence2, result);\n    return result;\n}\n/**\n * This function fixes issues like this:\n * ```\n * import { Baz, Bar } from \"foo\";\n * ```\n * <->\n * ```\n * import { Baz, Bar, Foo } from \"foo\";\n * ```\n * Computed diff: [ {Add \",\" after Bar}, {Add \"Foo \" after space} }\n * Improved diff: [{Add \", Foo\" after Bar}]\n */\nfunction joinSequenceDiffsByShifting(sequence1, sequence2, sequenceDiffs) {\n    if (sequenceDiffs.length === 0) {\n        return sequenceDiffs;\n    }\n    const result = [];\n    result.push(sequenceDiffs[0]);\n    // First move them all to the left as much as possible and join them if possible\n    for (let i = 1; i < sequenceDiffs.length; i++) {\n        const prevResult = result[result.length - 1];\n        let cur = sequenceDiffs[i];\n        if (cur.seq1Range.isEmpty || cur.seq2Range.isEmpty) {\n            const length = cur.seq1Range.start - prevResult.seq1Range.endExclusive;\n            let d;\n            for (d = 1; d <= length; d++) {\n                if (sequence1.getElement(cur.seq1Range.start - d) !== sequence1.getElement(cur.seq1Range.endExclusive - d) ||\n                    sequence2.getElement(cur.seq2Range.start - d) !== sequence2.getElement(cur.seq2Range.endExclusive - d)) {\n                    break;\n                }\n            }\n            d--;\n            if (d === length) {\n                // Merge previous and current diff\n                result[result.length - 1] = new SequenceDiff(new OffsetRange(prevResult.seq1Range.start, cur.seq1Range.endExclusive - length), new OffsetRange(prevResult.seq2Range.start, cur.seq2Range.endExclusive - length));\n                continue;\n            }\n            cur = cur.delta(-d);\n        }\n        result.push(cur);\n    }\n    const result2 = [];\n    // Then move them all to the right and join them again if possible\n    for (let i = 0; i < result.length - 1; i++) {\n        const nextResult = result[i + 1];\n        let cur = result[i];\n        if (cur.seq1Range.isEmpty || cur.seq2Range.isEmpty) {\n            const length = nextResult.seq1Range.start - cur.seq1Range.endExclusive;\n            let d;\n            for (d = 0; d < length; d++) {\n                if (!sequence1.isStronglyEqual(cur.seq1Range.start + d, cur.seq1Range.endExclusive + d) ||\n                    !sequence2.isStronglyEqual(cur.seq2Range.start + d, cur.seq2Range.endExclusive + d)) {\n                    break;\n                }\n            }\n            if (d === length) {\n                // Merge previous and current diff, write to result!\n                result[i + 1] = new SequenceDiff(new OffsetRange(cur.seq1Range.start + length, nextResult.seq1Range.endExclusive), new OffsetRange(cur.seq2Range.start + length, nextResult.seq2Range.endExclusive));\n                continue;\n            }\n            if (d > 0) {\n                cur = cur.delta(d);\n            }\n        }\n        result2.push(cur);\n    }\n    if (result.length > 0) {\n        result2.push(result[result.length - 1]);\n    }\n    return result2;\n}\n// align character level diffs at whitespace characters\n// import { IBar } from \"foo\";\n// import { I[Arr, I]Bar } from \"foo\";\n// ->\n// import { [IArr, ]IBar } from \"foo\";\n// import { ITransaction, observableValue, transaction } from 'vs/base/common/observable';\n// import { ITransaction, observable[FromEvent, observable]Value, transaction } from 'vs/base/common/observable';\n// ->\n// import { ITransaction, [observableFromEvent, ]observableValue, transaction } from 'vs/base/common/observable';\n// collectBrackets(level + 1, levelPerBracketType);\n// collectBrackets(level + 1, levelPerBracket[ + 1, levelPerBracket]Type);\n// ->\n// collectBrackets(level + 1, [levelPerBracket + 1, ]levelPerBracketType);\nfunction shiftSequenceDiffs(sequence1, sequence2, sequenceDiffs) {\n    if (!sequence1.getBoundaryScore || !sequence2.getBoundaryScore) {\n        return sequenceDiffs;\n    }\n    for (let i = 0; i < sequenceDiffs.length; i++) {\n        const prevDiff = (i > 0 ? sequenceDiffs[i - 1] : undefined);\n        const diff = sequenceDiffs[i];\n        const nextDiff = (i + 1 < sequenceDiffs.length ? sequenceDiffs[i + 1] : undefined);\n        const seq1ValidRange = new OffsetRange(prevDiff ? prevDiff.seq1Range.endExclusive + 1 : 0, nextDiff ? nextDiff.seq1Range.start - 1 : sequence1.length);\n        const seq2ValidRange = new OffsetRange(prevDiff ? prevDiff.seq2Range.endExclusive + 1 : 0, nextDiff ? nextDiff.seq2Range.start - 1 : sequence2.length);\n        if (diff.seq1Range.isEmpty) {\n            sequenceDiffs[i] = shiftDiffToBetterPosition(diff, sequence1, sequence2, seq1ValidRange, seq2ValidRange);\n        }\n        else if (diff.seq2Range.isEmpty) {\n            sequenceDiffs[i] = shiftDiffToBetterPosition(diff.swap(), sequence2, sequence1, seq2ValidRange, seq1ValidRange).swap();\n        }\n    }\n    return sequenceDiffs;\n}\nfunction shiftDiffToBetterPosition(diff, sequence1, sequence2, seq1ValidRange, seq2ValidRange) {\n    const maxShiftLimit = 100; // To prevent performance issues\n    // don't touch previous or next!\n    let deltaBefore = 1;\n    while (diff.seq1Range.start - deltaBefore >= seq1ValidRange.start &&\n        diff.seq2Range.start - deltaBefore >= seq2ValidRange.start &&\n        sequence2.isStronglyEqual(diff.seq2Range.start - deltaBefore, diff.seq2Range.endExclusive - deltaBefore) && deltaBefore < maxShiftLimit) {\n        deltaBefore++;\n    }\n    deltaBefore--;\n    let deltaAfter = 0;\n    while (diff.seq1Range.start + deltaAfter < seq1ValidRange.endExclusive &&\n        diff.seq2Range.endExclusive + deltaAfter < seq2ValidRange.endExclusive &&\n        sequence2.isStronglyEqual(diff.seq2Range.start + deltaAfter, diff.seq2Range.endExclusive + deltaAfter) && deltaAfter < maxShiftLimit) {\n        deltaAfter++;\n    }\n    if (deltaBefore === 0 && deltaAfter === 0) {\n        return diff;\n    }\n    // Visualize `[sequence1.text, diff.seq1Range.start + deltaAfter]`\n    // and `[sequence2.text, diff.seq2Range.start + deltaAfter, diff.seq2Range.endExclusive + deltaAfter]`\n    let bestDelta = 0;\n    let bestScore = -1;\n    // find best scored delta\n    for (let delta = -deltaBefore; delta <= deltaAfter; delta++) {\n        const seq2OffsetStart = diff.seq2Range.start + delta;\n        const seq2OffsetEndExclusive = diff.seq2Range.endExclusive + delta;\n        const seq1Offset = diff.seq1Range.start + delta;\n        const score = sequence1.getBoundaryScore(seq1Offset) + sequence2.getBoundaryScore(seq2OffsetStart) + sequence2.getBoundaryScore(seq2OffsetEndExclusive);\n        if (score > bestScore) {\n            bestScore = score;\n            bestDelta = delta;\n        }\n    }\n    return diff.delta(bestDelta);\n}\nexport function removeShortMatches(sequence1, sequence2, sequenceDiffs) {\n    const result = [];\n    for (const s of sequenceDiffs) {\n        const last = result[result.length - 1];\n        if (!last) {\n            result.push(s);\n            continue;\n        }\n        if (s.seq1Range.start - last.seq1Range.endExclusive <= 2 || s.seq2Range.start - last.seq2Range.endExclusive <= 2) {\n            result[result.length - 1] = new SequenceDiff(last.seq1Range.join(s.seq1Range), last.seq2Range.join(s.seq2Range));\n        }\n        else {\n            result.push(s);\n        }\n    }\n    return result;\n}\nexport function extendDiffsToEntireWordIfAppropriate(sequence1, sequence2, sequenceDiffs) {\n    const equalMappings = SequenceDiff.invert(sequenceDiffs, sequence1.length);\n    const additional = [];\n    let lastPoint = new OffsetPair(0, 0);\n    function scanWord(pair, equalMapping) {\n        if (pair.offset1 < lastPoint.offset1 || pair.offset2 < lastPoint.offset2) {\n            return;\n        }\n        const w1 = sequence1.findWordContaining(pair.offset1);\n        const w2 = sequence2.findWordContaining(pair.offset2);\n        if (!w1 || !w2) {\n            return;\n        }\n        let w = new SequenceDiff(w1, w2);\n        const equalPart = w.intersect(equalMapping);\n        let equalChars1 = equalPart.seq1Range.length;\n        let equalChars2 = equalPart.seq2Range.length;\n        // The words do not touch previous equals mappings, as we would have processed them already.\n        // But they might touch the next ones.\n        while (equalMappings.length > 0) {\n            const next = equalMappings[0];\n            const intersects = next.seq1Range.intersects(w.seq1Range) || next.seq2Range.intersects(w.seq2Range);\n            if (!intersects) {\n                break;\n            }\n            const v1 = sequence1.findWordContaining(next.seq1Range.start);\n            const v2 = sequence2.findWordContaining(next.seq2Range.start);\n            // Because there is an intersection, we know that the words are not empty.\n            const v = new SequenceDiff(v1, v2);\n            const equalPart = v.intersect(next);\n            equalChars1 += equalPart.seq1Range.length;\n            equalChars2 += equalPart.seq2Range.length;\n            w = w.join(v);\n            if (w.seq1Range.endExclusive >= next.seq1Range.endExclusive) {\n                // The word extends beyond the next equal mapping.\n                equalMappings.shift();\n            }\n            else {\n                break;\n            }\n        }\n        if (equalChars1 + equalChars2 < (w.seq1Range.length + w.seq2Range.length) * 2 / 3) {\n            additional.push(w);\n        }\n        lastPoint = w.getEndExclusives();\n    }\n    while (equalMappings.length > 0) {\n        const next = equalMappings.shift();\n        if (next.seq1Range.isEmpty) {\n            continue;\n        }\n        scanWord(next.getStarts(), next);\n        // The equal parts are not empty, so -1 gives us a character that is equal in both parts.\n        scanWord(next.getEndExclusives().delta(-1), next);\n    }\n    const merged = mergeSequenceDiffs(sequenceDiffs, additional);\n    return merged;\n}\nfunction mergeSequenceDiffs(sequenceDiffs1, sequenceDiffs2) {\n    const result = [];\n    while (sequenceDiffs1.length > 0 || sequenceDiffs2.length > 0) {\n        const sd1 = sequenceDiffs1[0];\n        const sd2 = sequenceDiffs2[0];\n        let next;\n        if (sd1 && (!sd2 || sd1.seq1Range.start < sd2.seq1Range.start)) {\n            next = sequenceDiffs1.shift();\n        }\n        else {\n            next = sequenceDiffs2.shift();\n        }\n        if (result.length > 0 && result[result.length - 1].seq1Range.endExclusive >= next.seq1Range.start) {\n            result[result.length - 1] = result[result.length - 1].join(next);\n        }\n        else {\n            result.push(next);\n        }\n    }\n    return result;\n}\nexport function removeVeryShortMatchingLinesBetweenDiffs(sequence1, _sequence2, sequenceDiffs) {\n    let diffs = sequenceDiffs;\n    if (diffs.length === 0) {\n        return diffs;\n    }\n    let counter = 0;\n    let shouldRepeat;\n    do {\n        shouldRepeat = false;\n        const result = [\n            diffs[0]\n        ];\n        for (let i = 1; i < diffs.length; i++) {\n            const cur = diffs[i];\n            const lastResult = result[result.length - 1];\n            function shouldJoinDiffs(before, after) {\n                const unchangedRange = new OffsetRange(lastResult.seq1Range.endExclusive, cur.seq1Range.start);\n                const unchangedText = sequence1.getText(unchangedRange);\n                const unchangedTextWithoutWs = unchangedText.replace(/\\s/g, '');\n                if (unchangedTextWithoutWs.length <= 4\n                    && (before.seq1Range.length + before.seq2Range.length > 5 || after.seq1Range.length + after.seq2Range.length > 5)) {\n                    return true;\n                }\n                return false;\n            }\n            const shouldJoin = shouldJoinDiffs(lastResult, cur);\n            if (shouldJoin) {\n                shouldRepeat = true;\n                result[result.length - 1] = result[result.length - 1].join(cur);\n            }\n            else {\n                result.push(cur);\n            }\n        }\n        diffs = result;\n    } while (counter++ < 10 && shouldRepeat);\n    return diffs;\n}\nexport function removeVeryShortMatchingTextBetweenLongDiffs(sequence1, sequence2, sequenceDiffs) {\n    let diffs = sequenceDiffs;\n    if (diffs.length === 0) {\n        return diffs;\n    }\n    let counter = 0;\n    let shouldRepeat;\n    do {\n        shouldRepeat = false;\n        const result = [\n            diffs[0]\n        ];\n        for (let i = 1; i < diffs.length; i++) {\n            const cur = diffs[i];\n            const lastResult = result[result.length - 1];\n            function shouldJoinDiffs(before, after) {\n                const unchangedRange = new OffsetRange(lastResult.seq1Range.endExclusive, cur.seq1Range.start);\n                const unchangedLineCount = sequence1.countLinesIn(unchangedRange);\n                if (unchangedLineCount > 5 || unchangedRange.length > 500) {\n                    return false;\n                }\n                const unchangedText = sequence1.getText(unchangedRange).trim();\n                if (unchangedText.length > 20 || unchangedText.split(/\\r\\n|\\r|\\n/).length > 1) {\n                    return false;\n                }\n                const beforeLineCount1 = sequence1.countLinesIn(before.seq1Range);\n                const beforeSeq1Length = before.seq1Range.length;\n                const beforeLineCount2 = sequence2.countLinesIn(before.seq2Range);\n                const beforeSeq2Length = before.seq2Range.length;\n                const afterLineCount1 = sequence1.countLinesIn(after.seq1Range);\n                const afterSeq1Length = after.seq1Range.length;\n                const afterLineCount2 = sequence2.countLinesIn(after.seq2Range);\n                const afterSeq2Length = after.seq2Range.length;\n                // TODO: Maybe a neural net can be used to derive the result from these numbers\n                const max = 2 * 40 + 50;\n                function cap(v) {\n                    return Math.min(v, max);\n                }\n                if (Math.pow(Math.pow(cap(beforeLineCount1 * 40 + beforeSeq1Length), 1.5) + Math.pow(cap(beforeLineCount2 * 40 + beforeSeq2Length), 1.5), 1.5)\n                    + Math.pow(Math.pow(cap(afterLineCount1 * 40 + afterSeq1Length), 1.5) + Math.pow(cap(afterLineCount2 * 40 + afterSeq2Length), 1.5), 1.5) > ((max ** 1.5) ** 1.5) * 1.3) {\n                    return true;\n                }\n                return false;\n            }\n            const shouldJoin = shouldJoinDiffs(lastResult, cur);\n            if (shouldJoin) {\n                shouldRepeat = true;\n                result[result.length - 1] = result[result.length - 1].join(cur);\n            }\n            else {\n                result.push(cur);\n            }\n        }\n        diffs = result;\n    } while (counter++ < 10 && shouldRepeat);\n    const newDiffs = [];\n    // Remove short suffixes/prefixes\n    forEachWithNeighbors(diffs, (prev, cur, next) => {\n        let newDiff = cur;\n        function shouldMarkAsChanged(text) {\n            return text.length > 0 && text.trim().length <= 3 && cur.seq1Range.length + cur.seq2Range.length > 100;\n        }\n        const fullRange1 = sequence1.extendToFullLines(cur.seq1Range);\n        const prefix = sequence1.getText(new OffsetRange(fullRange1.start, cur.seq1Range.start));\n        if (shouldMarkAsChanged(prefix)) {\n            newDiff = newDiff.deltaStart(-prefix.length);\n        }\n        const suffix = sequence1.getText(new OffsetRange(cur.seq1Range.endExclusive, fullRange1.endExclusive));\n        if (shouldMarkAsChanged(suffix)) {\n            newDiff = newDiff.deltaEnd(suffix.length);\n        }\n        const availableSpace = SequenceDiff.fromOffsetPairs(prev ? prev.getEndExclusives() : OffsetPair.zero, next ? next.getStarts() : OffsetPair.max);\n        const result = newDiff.intersect(availableSpace);\n        if (newDiffs.length > 0 && result.getStarts().equals(newDiffs[newDiffs.length - 1].getEndExclusives())) {\n            newDiffs[newDiffs.length - 1] = newDiffs[newDiffs.length - 1].join(result);\n        }\n        else {\n            newDiffs.push(result);\n        }\n    });\n    return newDiffs;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,oBAAoB,QAAQ,mCAAmC;AACxE,SAASC,WAAW,QAAQ,2BAA2B;AACvD,SAASC,UAAU,EAAEC,YAAY,QAAQ,+BAA+B;AACxE,OAAO,SAASC,qBAAqBA,CAACC,SAAS,EAAEC,SAAS,EAAEC,aAAa,EAAE;EACvE,IAAIC,MAAM,GAAGD,aAAa;EAC1BC,MAAM,GAAGC,2BAA2B,CAACJ,SAAS,EAAEC,SAAS,EAAEE,MAAM,CAAC;EAClE;EACA;EACAA,MAAM,GAAGC,2BAA2B,CAACJ,SAAS,EAAEC,SAAS,EAAEE,MAAM,CAAC;EAClEA,MAAM,GAAGE,kBAAkB,CAACL,SAAS,EAAEC,SAAS,EAAEE,MAAM,CAAC;EACzD,OAAOA,MAAM;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,2BAA2BA,CAACJ,SAAS,EAAEC,SAAS,EAAEC,aAAa,EAAE;EACtE,IAAIA,aAAa,CAACI,MAAM,KAAK,CAAC,EAAE;IAC5B,OAAOJ,aAAa;EACxB;EACA,MAAMC,MAAM,GAAG,EAAE;EACjBA,MAAM,CAACI,IAAI,CAACL,aAAa,CAAC,CAAC,CAAC,CAAC;EAC7B;EACA,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,aAAa,CAACI,MAAM,EAAEE,CAAC,EAAE,EAAE;IAC3C,MAAMC,UAAU,GAAGN,MAAM,CAACA,MAAM,CAACG,MAAM,GAAG,CAAC,CAAC;IAC5C,IAAII,GAAG,GAAGR,aAAa,CAACM,CAAC,CAAC;IAC1B,IAAIE,GAAG,CAACC,SAAS,CAACC,OAAO,IAAIF,GAAG,CAACG,SAAS,CAACD,OAAO,EAAE;MAChD,MAAMN,MAAM,GAAGI,GAAG,CAACC,SAAS,CAACG,KAAK,GAAGL,UAAU,CAACE,SAAS,CAACI,YAAY;MACtE,IAAIC,CAAC;MACL,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIV,MAAM,EAAEU,CAAC,EAAE,EAAE;QAC1B,IAAIhB,SAAS,CAACiB,UAAU,CAACP,GAAG,CAACC,SAAS,CAACG,KAAK,GAAGE,CAAC,CAAC,KAAKhB,SAAS,CAACiB,UAAU,CAACP,GAAG,CAACC,SAAS,CAACI,YAAY,GAAGC,CAAC,CAAC,IACtGf,SAAS,CAACgB,UAAU,CAACP,GAAG,CAACG,SAAS,CAACC,KAAK,GAAGE,CAAC,CAAC,KAAKf,SAAS,CAACgB,UAAU,CAACP,GAAG,CAACG,SAAS,CAACE,YAAY,GAAGC,CAAC,CAAC,EAAE;UACxG;QACJ;MACJ;MACAA,CAAC,EAAE;MACH,IAAIA,CAAC,KAAKV,MAAM,EAAE;QACd;QACAH,MAAM,CAACA,MAAM,CAACG,MAAM,GAAG,CAAC,CAAC,GAAG,IAAIR,YAAY,CAAC,IAAIF,WAAW,CAACa,UAAU,CAACE,SAAS,CAACG,KAAK,EAAEJ,GAAG,CAACC,SAAS,CAACI,YAAY,GAAGT,MAAM,CAAC,EAAE,IAAIV,WAAW,CAACa,UAAU,CAACI,SAAS,CAACC,KAAK,EAAEJ,GAAG,CAACG,SAAS,CAACE,YAAY,GAAGT,MAAM,CAAC,CAAC;QAChN;MACJ;MACAI,GAAG,GAAGA,GAAG,CAACQ,KAAK,CAAC,CAACF,CAAC,CAAC;IACvB;IACAb,MAAM,CAACI,IAAI,CAACG,GAAG,CAAC;EACpB;EACA,MAAMS,OAAO,GAAG,EAAE;EAClB;EACA,KAAK,IAAIX,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,MAAM,CAACG,MAAM,GAAG,CAAC,EAAEE,CAAC,EAAE,EAAE;IACxC,MAAMY,UAAU,GAAGjB,MAAM,CAACK,CAAC,GAAG,CAAC,CAAC;IAChC,IAAIE,GAAG,GAAGP,MAAM,CAACK,CAAC,CAAC;IACnB,IAAIE,GAAG,CAACC,SAAS,CAACC,OAAO,IAAIF,GAAG,CAACG,SAAS,CAACD,OAAO,EAAE;MAChD,MAAMN,MAAM,GAAGc,UAAU,CAACT,SAAS,CAACG,KAAK,GAAGJ,GAAG,CAACC,SAAS,CAACI,YAAY;MACtE,IAAIC,CAAC;MACL,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,MAAM,EAAEU,CAAC,EAAE,EAAE;QACzB,IAAI,CAAChB,SAAS,CAACqB,eAAe,CAACX,GAAG,CAACC,SAAS,CAACG,KAAK,GAAGE,CAAC,EAAEN,GAAG,CAACC,SAAS,CAACI,YAAY,GAAGC,CAAC,CAAC,IACnF,CAACf,SAAS,CAACoB,eAAe,CAACX,GAAG,CAACG,SAAS,CAACC,KAAK,GAAGE,CAAC,EAAEN,GAAG,CAACG,SAAS,CAACE,YAAY,GAAGC,CAAC,CAAC,EAAE;UACrF;QACJ;MACJ;MACA,IAAIA,CAAC,KAAKV,MAAM,EAAE;QACd;QACAH,MAAM,CAACK,CAAC,GAAG,CAAC,CAAC,GAAG,IAAIV,YAAY,CAAC,IAAIF,WAAW,CAACc,GAAG,CAACC,SAAS,CAACG,KAAK,GAAGR,MAAM,EAAEc,UAAU,CAACT,SAAS,CAACI,YAAY,CAAC,EAAE,IAAInB,WAAW,CAACc,GAAG,CAACG,SAAS,CAACC,KAAK,GAAGR,MAAM,EAAEc,UAAU,CAACP,SAAS,CAACE,YAAY,CAAC,CAAC;QACpM;MACJ;MACA,IAAIC,CAAC,GAAG,CAAC,EAAE;QACPN,GAAG,GAAGA,GAAG,CAACQ,KAAK,CAACF,CAAC,CAAC;MACtB;IACJ;IACAG,OAAO,CAACZ,IAAI,CAACG,GAAG,CAAC;EACrB;EACA,IAAIP,MAAM,CAACG,MAAM,GAAG,CAAC,EAAE;IACnBa,OAAO,CAACZ,IAAI,CAACJ,MAAM,CAACA,MAAM,CAACG,MAAM,GAAG,CAAC,CAAC,CAAC;EAC3C;EACA,OAAOa,OAAO;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASd,kBAAkBA,CAACL,SAAS,EAAEC,SAAS,EAAEC,aAAa,EAAE;EAC7D,IAAI,CAACF,SAAS,CAACsB,gBAAgB,IAAI,CAACrB,SAAS,CAACqB,gBAAgB,EAAE;IAC5D,OAAOpB,aAAa;EACxB;EACA,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,aAAa,CAACI,MAAM,EAAEE,CAAC,EAAE,EAAE;IAC3C,MAAMe,QAAQ,GAAIf,CAAC,GAAG,CAAC,GAAGN,aAAa,CAACM,CAAC,GAAG,CAAC,CAAC,GAAGgB,SAAU;IAC3D,MAAMC,IAAI,GAAGvB,aAAa,CAACM,CAAC,CAAC;IAC7B,MAAMkB,QAAQ,GAAIlB,CAAC,GAAG,CAAC,GAAGN,aAAa,CAACI,MAAM,GAAGJ,aAAa,CAACM,CAAC,GAAG,CAAC,CAAC,GAAGgB,SAAU;IAClF,MAAMG,cAAc,GAAG,IAAI/B,WAAW,CAAC2B,QAAQ,GAAGA,QAAQ,CAACZ,SAAS,CAACI,YAAY,GAAG,CAAC,GAAG,CAAC,EAAEW,QAAQ,GAAGA,QAAQ,CAACf,SAAS,CAACG,KAAK,GAAG,CAAC,GAAGd,SAAS,CAACM,MAAM,CAAC;IACtJ,MAAMsB,cAAc,GAAG,IAAIhC,WAAW,CAAC2B,QAAQ,GAAGA,QAAQ,CAACV,SAAS,CAACE,YAAY,GAAG,CAAC,GAAG,CAAC,EAAEW,QAAQ,GAAGA,QAAQ,CAACb,SAAS,CAACC,KAAK,GAAG,CAAC,GAAGb,SAAS,CAACK,MAAM,CAAC;IACtJ,IAAImB,IAAI,CAACd,SAAS,CAACC,OAAO,EAAE;MACxBV,aAAa,CAACM,CAAC,CAAC,GAAGqB,yBAAyB,CAACJ,IAAI,EAAEzB,SAAS,EAAEC,SAAS,EAAE0B,cAAc,EAAEC,cAAc,CAAC;IAC5G,CAAC,MACI,IAAIH,IAAI,CAACZ,SAAS,CAACD,OAAO,EAAE;MAC7BV,aAAa,CAACM,CAAC,CAAC,GAAGqB,yBAAyB,CAACJ,IAAI,CAACK,IAAI,CAAC,CAAC,EAAE7B,SAAS,EAAED,SAAS,EAAE4B,cAAc,EAAED,cAAc,CAAC,CAACG,IAAI,CAAC,CAAC;IAC1H;EACJ;EACA,OAAO5B,aAAa;AACxB;AACA,SAAS2B,yBAAyBA,CAACJ,IAAI,EAAEzB,SAAS,EAAEC,SAAS,EAAE0B,cAAc,EAAEC,cAAc,EAAE;EAC3F,MAAMG,aAAa,GAAG,GAAG,CAAC,CAAC;EAC3B;EACA,IAAIC,WAAW,GAAG,CAAC;EACnB,OAAOP,IAAI,CAACd,SAAS,CAACG,KAAK,GAAGkB,WAAW,IAAIL,cAAc,CAACb,KAAK,IAC7DW,IAAI,CAACZ,SAAS,CAACC,KAAK,GAAGkB,WAAW,IAAIJ,cAAc,CAACd,KAAK,IAC1Db,SAAS,CAACoB,eAAe,CAACI,IAAI,CAACZ,SAAS,CAACC,KAAK,GAAGkB,WAAW,EAAEP,IAAI,CAACZ,SAAS,CAACE,YAAY,GAAGiB,WAAW,CAAC,IAAIA,WAAW,GAAGD,aAAa,EAAE;IACzIC,WAAW,EAAE;EACjB;EACAA,WAAW,EAAE;EACb,IAAIC,UAAU,GAAG,CAAC;EAClB,OAAOR,IAAI,CAACd,SAAS,CAACG,KAAK,GAAGmB,UAAU,GAAGN,cAAc,CAACZ,YAAY,IAClEU,IAAI,CAACZ,SAAS,CAACE,YAAY,GAAGkB,UAAU,GAAGL,cAAc,CAACb,YAAY,IACtEd,SAAS,CAACoB,eAAe,CAACI,IAAI,CAACZ,SAAS,CAACC,KAAK,GAAGmB,UAAU,EAAER,IAAI,CAACZ,SAAS,CAACE,YAAY,GAAGkB,UAAU,CAAC,IAAIA,UAAU,GAAGF,aAAa,EAAE;IACtIE,UAAU,EAAE;EAChB;EACA,IAAID,WAAW,KAAK,CAAC,IAAIC,UAAU,KAAK,CAAC,EAAE;IACvC,OAAOR,IAAI;EACf;EACA;EACA;EACA,IAAIS,SAAS,GAAG,CAAC;EACjB,IAAIC,SAAS,GAAG,CAAC,CAAC;EAClB;EACA,KAAK,IAAIjB,KAAK,GAAG,CAACc,WAAW,EAAEd,KAAK,IAAIe,UAAU,EAAEf,KAAK,EAAE,EAAE;IACzD,MAAMkB,eAAe,GAAGX,IAAI,CAACZ,SAAS,CAACC,KAAK,GAAGI,KAAK;IACpD,MAAMmB,sBAAsB,GAAGZ,IAAI,CAACZ,SAAS,CAACE,YAAY,GAAGG,KAAK;IAClE,MAAMoB,UAAU,GAAGb,IAAI,CAACd,SAAS,CAACG,KAAK,GAAGI,KAAK;IAC/C,MAAMqB,KAAK,GAAGvC,SAAS,CAACsB,gBAAgB,CAACgB,UAAU,CAAC,GAAGrC,SAAS,CAACqB,gBAAgB,CAACc,eAAe,CAAC,GAAGnC,SAAS,CAACqB,gBAAgB,CAACe,sBAAsB,CAAC;IACvJ,IAAIE,KAAK,GAAGJ,SAAS,EAAE;MACnBA,SAAS,GAAGI,KAAK;MACjBL,SAAS,GAAGhB,KAAK;IACrB;EACJ;EACA,OAAOO,IAAI,CAACP,KAAK,CAACgB,SAAS,CAAC;AAChC;AACA,OAAO,SAASM,kBAAkBA,CAACxC,SAAS,EAAEC,SAAS,EAAEC,aAAa,EAAE;EACpE,MAAMC,MAAM,GAAG,EAAE;EACjB,KAAK,MAAMsC,CAAC,IAAIvC,aAAa,EAAE;IAC3B,MAAMwC,IAAI,GAAGvC,MAAM,CAACA,MAAM,CAACG,MAAM,GAAG,CAAC,CAAC;IACtC,IAAI,CAACoC,IAAI,EAAE;MACPvC,MAAM,CAACI,IAAI,CAACkC,CAAC,CAAC;MACd;IACJ;IACA,IAAIA,CAAC,CAAC9B,SAAS,CAACG,KAAK,GAAG4B,IAAI,CAAC/B,SAAS,CAACI,YAAY,IAAI,CAAC,IAAI0B,CAAC,CAAC5B,SAAS,CAACC,KAAK,GAAG4B,IAAI,CAAC7B,SAAS,CAACE,YAAY,IAAI,CAAC,EAAE;MAC9GZ,MAAM,CAACA,MAAM,CAACG,MAAM,GAAG,CAAC,CAAC,GAAG,IAAIR,YAAY,CAAC4C,IAAI,CAAC/B,SAAS,CAACgC,IAAI,CAACF,CAAC,CAAC9B,SAAS,CAAC,EAAE+B,IAAI,CAAC7B,SAAS,CAAC8B,IAAI,CAACF,CAAC,CAAC5B,SAAS,CAAC,CAAC;IACpH,CAAC,MACI;MACDV,MAAM,CAACI,IAAI,CAACkC,CAAC,CAAC;IAClB;EACJ;EACA,OAAOtC,MAAM;AACjB;AACA,OAAO,SAASyC,oCAAoCA,CAAC5C,SAAS,EAAEC,SAAS,EAAEC,aAAa,EAAE;EACtF,MAAM2C,aAAa,GAAG/C,YAAY,CAACgD,MAAM,CAAC5C,aAAa,EAAEF,SAAS,CAACM,MAAM,CAAC;EAC1E,MAAMyC,UAAU,GAAG,EAAE;EACrB,IAAIC,SAAS,GAAG,IAAInD,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC;EACpC,SAASoD,QAAQA,CAACC,IAAI,EAAEC,YAAY,EAAE;IAClC,IAAID,IAAI,CAACE,OAAO,GAAGJ,SAAS,CAACI,OAAO,IAAIF,IAAI,CAACG,OAAO,GAAGL,SAAS,CAACK,OAAO,EAAE;MACtE;IACJ;IACA,MAAMC,EAAE,GAAGtD,SAAS,CAACuD,kBAAkB,CAACL,IAAI,CAACE,OAAO,CAAC;IACrD,MAAMI,EAAE,GAAGvD,SAAS,CAACsD,kBAAkB,CAACL,IAAI,CAACG,OAAO,CAAC;IACrD,IAAI,CAACC,EAAE,IAAI,CAACE,EAAE,EAAE;MACZ;IACJ;IACA,IAAIC,CAAC,GAAG,IAAI3D,YAAY,CAACwD,EAAE,EAAEE,EAAE,CAAC;IAChC,MAAME,SAAS,GAAGD,CAAC,CAACE,SAAS,CAACR,YAAY,CAAC;IAC3C,IAAIS,WAAW,GAAGF,SAAS,CAAC/C,SAAS,CAACL,MAAM;IAC5C,IAAIuD,WAAW,GAAGH,SAAS,CAAC7C,SAAS,CAACP,MAAM;IAC5C;IACA;IACA,OAAOuC,aAAa,CAACvC,MAAM,GAAG,CAAC,EAAE;MAC7B,MAAMwD,IAAI,GAAGjB,aAAa,CAAC,CAAC,CAAC;MAC7B,MAAMkB,UAAU,GAAGD,IAAI,CAACnD,SAAS,CAACoD,UAAU,CAACN,CAAC,CAAC9C,SAAS,CAAC,IAAImD,IAAI,CAACjD,SAAS,CAACkD,UAAU,CAACN,CAAC,CAAC5C,SAAS,CAAC;MACnG,IAAI,CAACkD,UAAU,EAAE;QACb;MACJ;MACA,MAAMC,EAAE,GAAGhE,SAAS,CAACuD,kBAAkB,CAACO,IAAI,CAACnD,SAAS,CAACG,KAAK,CAAC;MAC7D,MAAMmD,EAAE,GAAGhE,SAAS,CAACsD,kBAAkB,CAACO,IAAI,CAACjD,SAAS,CAACC,KAAK,CAAC;MAC7D;MACA,MAAMoD,CAAC,GAAG,IAAIpE,YAAY,CAACkE,EAAE,EAAEC,EAAE,CAAC;MAClC,MAAMP,SAAS,GAAGQ,CAAC,CAACP,SAAS,CAACG,IAAI,CAAC;MACnCF,WAAW,IAAIF,SAAS,CAAC/C,SAAS,CAACL,MAAM;MACzCuD,WAAW,IAAIH,SAAS,CAAC7C,SAAS,CAACP,MAAM;MACzCmD,CAAC,GAAGA,CAAC,CAACd,IAAI,CAACuB,CAAC,CAAC;MACb,IAAIT,CAAC,CAAC9C,SAAS,CAACI,YAAY,IAAI+C,IAAI,CAACnD,SAAS,CAACI,YAAY,EAAE;QACzD;QACA8B,aAAa,CAACsB,KAAK,CAAC,CAAC;MACzB,CAAC,MACI;QACD;MACJ;IACJ;IACA,IAAIP,WAAW,GAAGC,WAAW,GAAG,CAACJ,CAAC,CAAC9C,SAAS,CAACL,MAAM,GAAGmD,CAAC,CAAC5C,SAAS,CAACP,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE;MAC/EyC,UAAU,CAACxC,IAAI,CAACkD,CAAC,CAAC;IACtB;IACAT,SAAS,GAAGS,CAAC,CAACW,gBAAgB,CAAC,CAAC;EACpC;EACA,OAAOvB,aAAa,CAACvC,MAAM,GAAG,CAAC,EAAE;IAC7B,MAAMwD,IAAI,GAAGjB,aAAa,CAACsB,KAAK,CAAC,CAAC;IAClC,IAAIL,IAAI,CAACnD,SAAS,CAACC,OAAO,EAAE;MACxB;IACJ;IACAqC,QAAQ,CAACa,IAAI,CAACO,SAAS,CAAC,CAAC,EAAEP,IAAI,CAAC;IAChC;IACAb,QAAQ,CAACa,IAAI,CAACM,gBAAgB,CAAC,CAAC,CAAClD,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE4C,IAAI,CAAC;EACrD;EACA,MAAMQ,MAAM,GAAGC,kBAAkB,CAACrE,aAAa,EAAE6C,UAAU,CAAC;EAC5D,OAAOuB,MAAM;AACjB;AACA,SAASC,kBAAkBA,CAACC,cAAc,EAAEC,cAAc,EAAE;EACxD,MAAMtE,MAAM,GAAG,EAAE;EACjB,OAAOqE,cAAc,CAAClE,MAAM,GAAG,CAAC,IAAImE,cAAc,CAACnE,MAAM,GAAG,CAAC,EAAE;IAC3D,MAAMoE,GAAG,GAAGF,cAAc,CAAC,CAAC,CAAC;IAC7B,MAAMG,GAAG,GAAGF,cAAc,CAAC,CAAC,CAAC;IAC7B,IAAIX,IAAI;IACR,IAAIY,GAAG,KAAK,CAACC,GAAG,IAAID,GAAG,CAAC/D,SAAS,CAACG,KAAK,GAAG6D,GAAG,CAAChE,SAAS,CAACG,KAAK,CAAC,EAAE;MAC5DgD,IAAI,GAAGU,cAAc,CAACL,KAAK,CAAC,CAAC;IACjC,CAAC,MACI;MACDL,IAAI,GAAGW,cAAc,CAACN,KAAK,CAAC,CAAC;IACjC;IACA,IAAIhE,MAAM,CAACG,MAAM,GAAG,CAAC,IAAIH,MAAM,CAACA,MAAM,CAACG,MAAM,GAAG,CAAC,CAAC,CAACK,SAAS,CAACI,YAAY,IAAI+C,IAAI,CAACnD,SAAS,CAACG,KAAK,EAAE;MAC/FX,MAAM,CAACA,MAAM,CAACG,MAAM,GAAG,CAAC,CAAC,GAAGH,MAAM,CAACA,MAAM,CAACG,MAAM,GAAG,CAAC,CAAC,CAACqC,IAAI,CAACmB,IAAI,CAAC;IACpE,CAAC,MACI;MACD3D,MAAM,CAACI,IAAI,CAACuD,IAAI,CAAC;IACrB;EACJ;EACA,OAAO3D,MAAM;AACjB;AACA,OAAO,SAASyE,wCAAwCA,CAAC5E,SAAS,EAAE6E,UAAU,EAAE3E,aAAa,EAAE;EAC3F,IAAI4E,KAAK,GAAG5E,aAAa;EACzB,IAAI4E,KAAK,CAACxE,MAAM,KAAK,CAAC,EAAE;IACpB,OAAOwE,KAAK;EAChB;EACA,IAAIC,OAAO,GAAG,CAAC;EACf,IAAIC,YAAY;EAChB,GAAG;IACCA,YAAY,GAAG,KAAK;IACpB,MAAM7E,MAAM,GAAG,CACX2E,KAAK,CAAC,CAAC,CAAC,CACX;IACD,KAAK,IAAItE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsE,KAAK,CAACxE,MAAM,EAAEE,CAAC,EAAE,EAAE;MACnC,MAAME,GAAG,GAAGoE,KAAK,CAACtE,CAAC,CAAC;MACpB,MAAMyE,UAAU,GAAG9E,MAAM,CAACA,MAAM,CAACG,MAAM,GAAG,CAAC,CAAC;MAC5C,SAAS4E,eAAeA,CAACC,MAAM,EAAEC,KAAK,EAAE;QACpC,MAAMC,cAAc,GAAG,IAAIzF,WAAW,CAACqF,UAAU,CAACtE,SAAS,CAACI,YAAY,EAAEL,GAAG,CAACC,SAAS,CAACG,KAAK,CAAC;QAC9F,MAAMwE,aAAa,GAAGtF,SAAS,CAACuF,OAAO,CAACF,cAAc,CAAC;QACvD,MAAMG,sBAAsB,GAAGF,aAAa,CAACG,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;QAC/D,IAAID,sBAAsB,CAAClF,MAAM,IAAI,CAAC,KAC9B6E,MAAM,CAACxE,SAAS,CAACL,MAAM,GAAG6E,MAAM,CAACtE,SAAS,CAACP,MAAM,GAAG,CAAC,IAAI8E,KAAK,CAACzE,SAAS,CAACL,MAAM,GAAG8E,KAAK,CAACvE,SAAS,CAACP,MAAM,GAAG,CAAC,CAAC,EAAE;UACnH,OAAO,IAAI;QACf;QACA,OAAO,KAAK;MAChB;MACA,MAAMoF,UAAU,GAAGR,eAAe,CAACD,UAAU,EAAEvE,GAAG,CAAC;MACnD,IAAIgF,UAAU,EAAE;QACZV,YAAY,GAAG,IAAI;QACnB7E,MAAM,CAACA,MAAM,CAACG,MAAM,GAAG,CAAC,CAAC,GAAGH,MAAM,CAACA,MAAM,CAACG,MAAM,GAAG,CAAC,CAAC,CAACqC,IAAI,CAACjC,GAAG,CAAC;MACnE,CAAC,MACI;QACDP,MAAM,CAACI,IAAI,CAACG,GAAG,CAAC;MACpB;IACJ;IACAoE,KAAK,GAAG3E,MAAM;EAClB,CAAC,QAAQ4E,OAAO,EAAE,GAAG,EAAE,IAAIC,YAAY;EACvC,OAAOF,KAAK;AAChB;AACA,OAAO,SAASa,2CAA2CA,CAAC3F,SAAS,EAAEC,SAAS,EAAEC,aAAa,EAAE;EAC7F,IAAI4E,KAAK,GAAG5E,aAAa;EACzB,IAAI4E,KAAK,CAACxE,MAAM,KAAK,CAAC,EAAE;IACpB,OAAOwE,KAAK;EAChB;EACA,IAAIC,OAAO,GAAG,CAAC;EACf,IAAIC,YAAY;EAChB,GAAG;IACCA,YAAY,GAAG,KAAK;IACpB,MAAM7E,MAAM,GAAG,CACX2E,KAAK,CAAC,CAAC,CAAC,CACX;IACD,KAAK,IAAItE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsE,KAAK,CAACxE,MAAM,EAAEE,CAAC,EAAE,EAAE;MACnC,MAAME,GAAG,GAAGoE,KAAK,CAACtE,CAAC,CAAC;MACpB,MAAMyE,UAAU,GAAG9E,MAAM,CAACA,MAAM,CAACG,MAAM,GAAG,CAAC,CAAC;MAC5C,SAAS4E,eAAeA,CAACC,MAAM,EAAEC,KAAK,EAAE;QACpC,MAAMC,cAAc,GAAG,IAAIzF,WAAW,CAACqF,UAAU,CAACtE,SAAS,CAACI,YAAY,EAAEL,GAAG,CAACC,SAAS,CAACG,KAAK,CAAC;QAC9F,MAAM8E,kBAAkB,GAAG5F,SAAS,CAAC6F,YAAY,CAACR,cAAc,CAAC;QACjE,IAAIO,kBAAkB,GAAG,CAAC,IAAIP,cAAc,CAAC/E,MAAM,GAAG,GAAG,EAAE;UACvD,OAAO,KAAK;QAChB;QACA,MAAMgF,aAAa,GAAGtF,SAAS,CAACuF,OAAO,CAACF,cAAc,CAAC,CAACS,IAAI,CAAC,CAAC;QAC9D,IAAIR,aAAa,CAAChF,MAAM,GAAG,EAAE,IAAIgF,aAAa,CAACS,KAAK,CAAC,YAAY,CAAC,CAACzF,MAAM,GAAG,CAAC,EAAE;UAC3E,OAAO,KAAK;QAChB;QACA,MAAM0F,gBAAgB,GAAGhG,SAAS,CAAC6F,YAAY,CAACV,MAAM,CAACxE,SAAS,CAAC;QACjE,MAAMsF,gBAAgB,GAAGd,MAAM,CAACxE,SAAS,CAACL,MAAM;QAChD,MAAM4F,gBAAgB,GAAGjG,SAAS,CAAC4F,YAAY,CAACV,MAAM,CAACtE,SAAS,CAAC;QACjE,MAAMsF,gBAAgB,GAAGhB,MAAM,CAACtE,SAAS,CAACP,MAAM;QAChD,MAAM8F,eAAe,GAAGpG,SAAS,CAAC6F,YAAY,CAACT,KAAK,CAACzE,SAAS,CAAC;QAC/D,MAAM0F,eAAe,GAAGjB,KAAK,CAACzE,SAAS,CAACL,MAAM;QAC9C,MAAMgG,eAAe,GAAGrG,SAAS,CAAC4F,YAAY,CAACT,KAAK,CAACvE,SAAS,CAAC;QAC/D,MAAM0F,eAAe,GAAGnB,KAAK,CAACvE,SAAS,CAACP,MAAM;QAC9C;QACA,MAAMkG,GAAG,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE;QACvB,SAASC,GAAGA,CAACvC,CAAC,EAAE;UACZ,OAAOwC,IAAI,CAACC,GAAG,CAACzC,CAAC,EAAEsC,GAAG,CAAC;QAC3B;QACA,IAAIE,IAAI,CAACE,GAAG,CAACF,IAAI,CAACE,GAAG,CAACH,GAAG,CAACT,gBAAgB,GAAG,EAAE,GAAGC,gBAAgB,CAAC,EAAE,GAAG,CAAC,GAAGS,IAAI,CAACE,GAAG,CAACH,GAAG,CAACP,gBAAgB,GAAG,EAAE,GAAGC,gBAAgB,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,GACxIO,IAAI,CAACE,GAAG,CAACF,IAAI,CAACE,GAAG,CAACH,GAAG,CAACL,eAAe,GAAG,EAAE,GAAGC,eAAe,CAAC,EAAE,GAAG,CAAC,GAAGK,IAAI,CAACE,GAAG,CAACH,GAAG,CAACH,eAAe,GAAG,EAAE,GAAGC,eAAe,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,GAAI,CAACC,GAAG,IAAI,GAAG,KAAK,GAAG,GAAI,GAAG,EAAE;UACxK,OAAO,IAAI;QACf;QACA,OAAO,KAAK;MAChB;MACA,MAAMd,UAAU,GAAGR,eAAe,CAACD,UAAU,EAAEvE,GAAG,CAAC;MACnD,IAAIgF,UAAU,EAAE;QACZV,YAAY,GAAG,IAAI;QACnB7E,MAAM,CAACA,MAAM,CAACG,MAAM,GAAG,CAAC,CAAC,GAAGH,MAAM,CAACA,MAAM,CAACG,MAAM,GAAG,CAAC,CAAC,CAACqC,IAAI,CAACjC,GAAG,CAAC;MACnE,CAAC,MACI;QACDP,MAAM,CAACI,IAAI,CAACG,GAAG,CAAC;MACpB;IACJ;IACAoE,KAAK,GAAG3E,MAAM;EAClB,CAAC,QAAQ4E,OAAO,EAAE,GAAG,EAAE,IAAIC,YAAY;EACvC,MAAM6B,QAAQ,GAAG,EAAE;EACnB;EACAlH,oBAAoB,CAACmF,KAAK,EAAE,CAACgC,IAAI,EAAEpG,GAAG,EAAEoD,IAAI,KAAK;IAC7C,IAAIiD,OAAO,GAAGrG,GAAG;IACjB,SAASsG,mBAAmBA,CAACC,IAAI,EAAE;MAC/B,OAAOA,IAAI,CAAC3G,MAAM,GAAG,CAAC,IAAI2G,IAAI,CAACnB,IAAI,CAAC,CAAC,CAACxF,MAAM,IAAI,CAAC,IAAII,GAAG,CAACC,SAAS,CAACL,MAAM,GAAGI,GAAG,CAACG,SAAS,CAACP,MAAM,GAAG,GAAG;IAC1G;IACA,MAAM4G,UAAU,GAAGlH,SAAS,CAACmH,iBAAiB,CAACzG,GAAG,CAACC,SAAS,CAAC;IAC7D,MAAMyG,MAAM,GAAGpH,SAAS,CAACuF,OAAO,CAAC,IAAI3F,WAAW,CAACsH,UAAU,CAACpG,KAAK,EAAEJ,GAAG,CAACC,SAAS,CAACG,KAAK,CAAC,CAAC;IACxF,IAAIkG,mBAAmB,CAACI,MAAM,CAAC,EAAE;MAC7BL,OAAO,GAAGA,OAAO,CAACM,UAAU,CAAC,CAACD,MAAM,CAAC9G,MAAM,CAAC;IAChD;IACA,MAAMgH,MAAM,GAAGtH,SAAS,CAACuF,OAAO,CAAC,IAAI3F,WAAW,CAACc,GAAG,CAACC,SAAS,CAACI,YAAY,EAAEmG,UAAU,CAACnG,YAAY,CAAC,CAAC;IACtG,IAAIiG,mBAAmB,CAACM,MAAM,CAAC,EAAE;MAC7BP,OAAO,GAAGA,OAAO,CAACQ,QAAQ,CAACD,MAAM,CAAChH,MAAM,CAAC;IAC7C;IACA,MAAMkH,cAAc,GAAG1H,YAAY,CAAC2H,eAAe,CAACX,IAAI,GAAGA,IAAI,CAAC1C,gBAAgB,CAAC,CAAC,GAAGvE,UAAU,CAAC6H,IAAI,EAAE5D,IAAI,GAAGA,IAAI,CAACO,SAAS,CAAC,CAAC,GAAGxE,UAAU,CAAC2G,GAAG,CAAC;IAC/I,MAAMrG,MAAM,GAAG4G,OAAO,CAACpD,SAAS,CAAC6D,cAAc,CAAC;IAChD,IAAIX,QAAQ,CAACvG,MAAM,GAAG,CAAC,IAAIH,MAAM,CAACkE,SAAS,CAAC,CAAC,CAACsD,MAAM,CAACd,QAAQ,CAACA,QAAQ,CAACvG,MAAM,GAAG,CAAC,CAAC,CAAC8D,gBAAgB,CAAC,CAAC,CAAC,EAAE;MACpGyC,QAAQ,CAACA,QAAQ,CAACvG,MAAM,GAAG,CAAC,CAAC,GAAGuG,QAAQ,CAACA,QAAQ,CAACvG,MAAM,GAAG,CAAC,CAAC,CAACqC,IAAI,CAACxC,MAAM,CAAC;IAC9E,CAAC,MACI;MACD0G,QAAQ,CAACtG,IAAI,CAACJ,MAAM,CAAC;IACzB;EACJ,CAAC,CAAC;EACF,OAAO0G,QAAQ;AACnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}