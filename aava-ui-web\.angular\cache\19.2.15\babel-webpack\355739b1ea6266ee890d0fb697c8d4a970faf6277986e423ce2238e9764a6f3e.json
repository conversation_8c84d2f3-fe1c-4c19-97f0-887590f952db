{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { ActionRunner } from '../../../../base/common/actions.js';\nexport class ActionRunnerWithContext extends ActionRunner {\n  constructor(_getContext) {\n    super();\n    this._getContext = _getContext;\n  }\n  runAction(action, _context) {\n    const ctx = this._getContext();\n    return super.runAction(action, ctx);\n  }\n}", "map": {"version": 3, "names": ["ActionRunner", "ActionRunnerWithContext", "constructor", "_getContext", "runAction", "action", "_context", "ctx"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/browser/widget/multiDiffEditor/utils.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { ActionRunner } from '../../../../base/common/actions.js';\nexport class ActionRunnerWithContext extends ActionRunner {\n    constructor(_getContext) {\n        super();\n        this._getContext = _getContext;\n    }\n    runAction(action, _context) {\n        const ctx = this._getContext();\n        return super.runAction(action, ctx);\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,YAAY,QAAQ,oCAAoC;AACjE,OAAO,MAAMC,uBAAuB,SAASD,YAAY,CAAC;EACtDE,WAAWA,CAACC,WAAW,EAAE;IACrB,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,WAAW,GAAGA,WAAW;EAClC;EACAC,SAASA,CAACC,MAAM,EAAEC,QAAQ,EAAE;IACxB,MAAMC,GAAG,GAAG,IAAI,CAACJ,WAAW,CAAC,CAAC;IAC9B,OAAO,KAAK,CAACC,SAAS,CAACC,MAAM,EAAEE,GAAG,CAAC;EACvC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}