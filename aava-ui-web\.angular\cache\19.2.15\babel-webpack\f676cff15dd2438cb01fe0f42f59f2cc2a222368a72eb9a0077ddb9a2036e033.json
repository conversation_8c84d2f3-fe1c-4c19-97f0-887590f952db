{"ast": null, "code": "import _asyncToGenerator from \"C:/console/aava-ui-web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { createCancelablePromise, TimeoutTimer } from '../../../../base/common/async.js';\nimport { isCancellationError } from '../../../../base/common/errors.js';\nimport { Emitter } from '../../../../base/common/event.js';\nimport { Disposable, MutableDisposable } from '../../../../base/common/lifecycle.js';\nimport { isEqual } from '../../../../base/common/resources.js';\nimport { ShowLightbulbIconMode } from '../../../common/config/editorOptions.js';\nimport { Position } from '../../../common/core/position.js';\nimport { Selection } from '../../../common/core/selection.js';\nimport { RawContextKey } from '../../../../platform/contextkey/common/contextkey.js';\nimport { Progress } from '../../../../platform/progress/common/progress.js';\nimport { CodeActionKind, CodeActionTriggerSource } from '../common/types.js';\nimport { getCodeActions } from './codeAction.js';\nimport { HierarchicalKind } from '../../../../base/common/hierarchicalKind.js';\nimport { StopWatch } from '../../../../base/common/stopwatch.js';\nexport const SUPPORTED_CODE_ACTIONS = new RawContextKey('supportedCodeAction', '');\nexport const APPLY_FIX_ALL_COMMAND_ID = '_typescript.applyFixAllCodeAction';\nclass CodeActionOracle extends Disposable {\n  constructor(_editor, _markerService, _signalChange, _delay = 250) {\n    super();\n    this._editor = _editor;\n    this._markerService = _markerService;\n    this._signalChange = _signalChange;\n    this._delay = _delay;\n    this._autoTriggerTimer = this._register(new TimeoutTimer());\n    this._register(this._markerService.onMarkerChanged(e => this._onMarkerChanges(e)));\n    this._register(this._editor.onDidChangeCursorPosition(() => this._tryAutoTrigger()));\n  }\n  trigger(trigger) {\n    const selection = this._getRangeOfSelectionUnlessWhitespaceEnclosed(trigger);\n    this._signalChange(selection ? {\n      trigger,\n      selection\n    } : undefined);\n  }\n  _onMarkerChanges(resources) {\n    const model = this._editor.getModel();\n    if (model && resources.some(resource => isEqual(resource, model.uri))) {\n      this._tryAutoTrigger();\n    }\n  }\n  _tryAutoTrigger() {\n    this._autoTriggerTimer.cancelAndSet(() => {\n      this.trigger({\n        type: 2 /* CodeActionTriggerType.Auto */,\n        triggerAction: CodeActionTriggerSource.Default\n      });\n    }, this._delay);\n  }\n  _getRangeOfSelectionUnlessWhitespaceEnclosed(trigger) {\n    if (!this._editor.hasModel()) {\n      return undefined;\n    }\n    const selection = this._editor.getSelection();\n    if (trigger.type === 1 /* CodeActionTriggerType.Invoke */) {\n      return selection;\n    }\n    const enabled = this._editor.getOption(65 /* EditorOption.lightbulb */).enabled;\n    if (enabled === ShowLightbulbIconMode.Off) {\n      return undefined;\n    } else if (enabled === ShowLightbulbIconMode.On) {\n      return selection;\n    } else if (enabled === ShowLightbulbIconMode.OnCode) {\n      const isSelectionEmpty = selection.isEmpty();\n      if (!isSelectionEmpty) {\n        return selection;\n      }\n      const model = this._editor.getModel();\n      const {\n        lineNumber,\n        column\n      } = selection.getPosition();\n      const line = model.getLineContent(lineNumber);\n      if (line.length === 0) {\n        // empty line\n        return undefined;\n      } else if (column === 1) {\n        // look only right\n        if (/\\s/.test(line[0])) {\n          return undefined;\n        }\n      } else if (column === model.getLineMaxColumn(lineNumber)) {\n        // look only left\n        if (/\\s/.test(line[line.length - 1])) {\n          return undefined;\n        }\n      } else {\n        // look left and right\n        if (/\\s/.test(line[column - 2]) && /\\s/.test(line[column - 1])) {\n          return undefined;\n        }\n      }\n    }\n    return selection;\n  }\n}\nexport var CodeActionsState;\n(function (CodeActionsState) {\n  CodeActionsState.Empty = {\n    type: 0 /* Type.Empty */\n  };\n  class Triggered {\n    constructor(trigger, position, _cancellablePromise) {\n      this.trigger = trigger;\n      this.position = position;\n      this._cancellablePromise = _cancellablePromise;\n      this.type = 1 /* Type.Triggered */;\n      this.actions = _cancellablePromise.catch(e => {\n        if (isCancellationError(e)) {\n          return emptyCodeActionSet;\n        }\n        throw e;\n      });\n    }\n    cancel() {\n      this._cancellablePromise.cancel();\n    }\n  }\n  CodeActionsState.Triggered = Triggered;\n})(CodeActionsState || (CodeActionsState = {}));\nconst emptyCodeActionSet = Object.freeze({\n  allActions: [],\n  validActions: [],\n  dispose: () => {},\n  documentation: [],\n  hasAutoFix: false,\n  hasAIFix: false,\n  allAIFixes: false\n});\nexport class CodeActionModel extends Disposable {\n  constructor(_editor, _registry, _markerService, contextKeyService, _progressService, _configurationService, _telemetryService) {\n    super();\n    this._editor = _editor;\n    this._registry = _registry;\n    this._markerService = _markerService;\n    this._progressService = _progressService;\n    this._configurationService = _configurationService;\n    this._telemetryService = _telemetryService;\n    this._codeActionOracle = this._register(new MutableDisposable());\n    this._state = CodeActionsState.Empty;\n    this._onDidChangeState = this._register(new Emitter());\n    this.onDidChangeState = this._onDidChangeState.event;\n    this._disposed = false;\n    this._supportedCodeActions = SUPPORTED_CODE_ACTIONS.bindTo(contextKeyService);\n    this._register(this._editor.onDidChangeModel(() => this._update()));\n    this._register(this._editor.onDidChangeModelLanguage(() => this._update()));\n    this._register(this._registry.onDidChange(() => this._update()));\n    this._register(this._editor.onDidChangeConfiguration(e => {\n      if (e.hasChanged(65 /* EditorOption.lightbulb */)) {\n        this._update();\n      }\n    }));\n    this._update();\n  }\n  dispose() {\n    if (this._disposed) {\n      return;\n    }\n    this._disposed = true;\n    super.dispose();\n    this.setState(CodeActionsState.Empty, true);\n  }\n  _settingEnabledNearbyQuickfixes() {\n    const model = this._editor?.getModel();\n    return this._configurationService ? this._configurationService.getValue('editor.codeActionWidget.includeNearbyQuickFixes', {\n      resource: model?.uri\n    }) : false;\n  }\n  _update() {\n    var _this = this;\n    if (this._disposed) {\n      return;\n    }\n    this._codeActionOracle.value = undefined;\n    this.setState(CodeActionsState.Empty);\n    const model = this._editor.getModel();\n    if (model && this._registry.has(model) && !this._editor.getOption(92 /* EditorOption.readOnly */)) {\n      const supportedActions = this._registry.all(model).flatMap(provider => provider.providedCodeActionKinds ?? []);\n      this._supportedCodeActions.set(supportedActions.join(' '));\n      this._codeActionOracle.value = new CodeActionOracle(this._editor, this._markerService, trigger => {\n        if (!trigger) {\n          this.setState(CodeActionsState.Empty);\n          return;\n        }\n        const startPosition = trigger.selection.getStartPosition();\n        const actions = createCancelablePromise(/*#__PURE__*/function () {\n          var _ref = _asyncToGenerator(function* (token) {\n            if (_this._settingEnabledNearbyQuickfixes() && trigger.trigger.type === 1 /* CodeActionTriggerType.Invoke */ && (trigger.trigger.triggerAction === CodeActionTriggerSource.QuickFix || trigger.trigger.filter?.include?.contains(CodeActionKind.QuickFix))) {\n              const codeActionSet = yield getCodeActions(_this._registry, model, trigger.selection, trigger.trigger, Progress.None, token);\n              const allCodeActions = [...codeActionSet.allActions];\n              if (token.isCancellationRequested) {\n                return emptyCodeActionSet;\n              }\n              // Search for quickfixes in the curret code action set.\n              const foundQuickfix = codeActionSet.validActions?.some(action => action.action.kind ? CodeActionKind.QuickFix.contains(new HierarchicalKind(action.action.kind)) : false);\n              const allMarkers = _this._markerService.read({\n                resource: model.uri\n              });\n              if (foundQuickfix) {\n                for (const action of codeActionSet.validActions) {\n                  if (action.action.command?.arguments?.some(arg => typeof arg === 'string' && arg.includes(APPLY_FIX_ALL_COMMAND_ID))) {\n                    action.action.diagnostics = [...allMarkers.filter(marker => marker.relatedInformation)];\n                  }\n                }\n                return {\n                  validActions: codeActionSet.validActions,\n                  allActions: allCodeActions,\n                  documentation: codeActionSet.documentation,\n                  hasAutoFix: codeActionSet.hasAutoFix,\n                  hasAIFix: codeActionSet.hasAIFix,\n                  allAIFixes: codeActionSet.allAIFixes,\n                  dispose: () => {\n                    codeActionSet.dispose();\n                  }\n                };\n              } else if (!foundQuickfix) {\n                // If markers exists, and there are no quickfixes found or length is zero, check for quickfixes on that line.\n                if (allMarkers.length > 0) {\n                  const currPosition = trigger.selection.getPosition();\n                  let trackedPosition = currPosition;\n                  let distance = Number.MAX_VALUE;\n                  const currentActions = [...codeActionSet.validActions];\n                  for (const marker of allMarkers) {\n                    const col = marker.endColumn;\n                    const row = marker.endLineNumber;\n                    const startRow = marker.startLineNumber;\n                    // Found quickfix on the same line and check relative distance to other markers\n                    if (row === currPosition.lineNumber || startRow === currPosition.lineNumber) {\n                      trackedPosition = new Position(row, col);\n                      const newCodeActionTrigger = {\n                        type: trigger.trigger.type,\n                        triggerAction: trigger.trigger.triggerAction,\n                        filter: {\n                          include: trigger.trigger.filter?.include ? trigger.trigger.filter?.include : CodeActionKind.QuickFix\n                        },\n                        autoApply: trigger.trigger.autoApply,\n                        context: {\n                          notAvailableMessage: trigger.trigger.context?.notAvailableMessage || '',\n                          position: trackedPosition\n                        }\n                      };\n                      const selectionAsPosition = new Selection(trackedPosition.lineNumber, trackedPosition.column, trackedPosition.lineNumber, trackedPosition.column);\n                      const actionsAtMarker = yield getCodeActions(_this._registry, model, selectionAsPosition, newCodeActionTrigger, Progress.None, token);\n                      if (actionsAtMarker.validActions.length !== 0) {\n                        for (const action of actionsAtMarker.validActions) {\n                          if (action.action.command?.arguments?.some(arg => typeof arg === 'string' && arg.includes(APPLY_FIX_ALL_COMMAND_ID))) {\n                            action.action.diagnostics = [...allMarkers.filter(marker => marker.relatedInformation)];\n                          }\n                        }\n                        if (codeActionSet.allActions.length === 0) {\n                          allCodeActions.push(...actionsAtMarker.allActions);\n                        }\n                        // Already filtered through to only get quickfixes, so no need to filter again.\n                        if (Math.abs(currPosition.column - col) < distance) {\n                          currentActions.unshift(...actionsAtMarker.validActions);\n                        } else {\n                          currentActions.push(...actionsAtMarker.validActions);\n                        }\n                      }\n                      distance = Math.abs(currPosition.column - col);\n                    }\n                  }\n                  const filteredActions = currentActions.filter((action, index, self) => self.findIndex(a => a.action.title === action.action.title) === index);\n                  filteredActions.sort((a, b) => {\n                    if (a.action.isPreferred && !b.action.isPreferred) {\n                      return -1;\n                    } else if (!a.action.isPreferred && b.action.isPreferred) {\n                      return 1;\n                    } else if (a.action.isAI && !b.action.isAI) {\n                      return 1;\n                    } else if (!a.action.isAI && b.action.isAI) {\n                      return -1;\n                    } else {\n                      return 0;\n                    }\n                  });\n                  // Only retriggers if actually found quickfix on the same line as cursor\n                  return {\n                    validActions: filteredActions,\n                    allActions: allCodeActions,\n                    documentation: codeActionSet.documentation,\n                    hasAutoFix: codeActionSet.hasAutoFix,\n                    hasAIFix: codeActionSet.hasAIFix,\n                    allAIFixes: codeActionSet.allAIFixes,\n                    dispose: () => {\n                      codeActionSet.dispose();\n                    }\n                  };\n                }\n              }\n            }\n            // Case for manual triggers - specifically Source Actions and Refactors\n            if (trigger.trigger.type === 1 /* CodeActionTriggerType.Invoke */) {\n              const sw = new StopWatch();\n              const codeActions = yield getCodeActions(_this._registry, model, trigger.selection, trigger.trigger, Progress.None, token);\n              // Telemetry for duration of each code action on save.\n              if (_this._telemetryService) {\n                _this._telemetryService.publicLog2('codeAction.invokedDurations', {\n                  codeActions: codeActions.validActions.length,\n                  duration: sw.elapsed()\n                });\n              }\n              return codeActions;\n            }\n            return getCodeActions(_this._registry, model, trigger.selection, trigger.trigger, Progress.None, token);\n          });\n          return function (_x) {\n            return _ref.apply(this, arguments);\n          };\n        }());\n        if (trigger.trigger.type === 1 /* CodeActionTriggerType.Invoke */) {\n          this._progressService?.showWhile(actions, 250);\n        }\n        const newState = new CodeActionsState.Triggered(trigger.trigger, startPosition, actions);\n        let isManualToAutoTransition = false;\n        if (this._state.type === 1 /* CodeActionsState.Type.Triggered */) {\n          // Check if the current state is manual and the new state is automatic\n          isManualToAutoTransition = this._state.trigger.type === 1 /* CodeActionTriggerType.Invoke */ && newState.type === 1 /* CodeActionsState.Type.Triggered */ && newState.trigger.type === 2 /* CodeActionTriggerType.Auto */ && this._state.position !== newState.position;\n        }\n        // Do not trigger state if current state is manual and incoming state is automatic\n        if (!isManualToAutoTransition) {\n          this.setState(newState);\n        } else {\n          // Reset the new state after getting code actions back.\n          setTimeout(() => {\n            this.setState(newState);\n          }, 500);\n        }\n      }, undefined);\n      this._codeActionOracle.value.trigger({\n        type: 2 /* CodeActionTriggerType.Auto */,\n        triggerAction: CodeActionTriggerSource.Default\n      });\n    } else {\n      this._supportedCodeActions.reset();\n    }\n  }\n  trigger(trigger) {\n    this._codeActionOracle.value?.trigger(trigger);\n  }\n  setState(newState, skipNotify) {\n    if (newState === this._state) {\n      return;\n    }\n    // Cancel old request\n    if (this._state.type === 1 /* CodeActionsState.Type.Triggered */) {\n      this._state.cancel();\n    }\n    this._state = newState;\n    if (!skipNotify && !this._disposed) {\n      this._onDidChangeState.fire(newState);\n    }\n  }\n}", "map": {"version": 3, "names": ["createCancelablePromise", "TimeoutTimer", "isCancellationError", "Emitter", "Disposable", "MutableDisposable", "isEqual", "ShowLightbulbIconMode", "Position", "Selection", "RawContextKey", "Progress", "CodeActionKind", "CodeActionTriggerSource", "getCodeActions", "HierarchicalKind", "StopWatch", "SUPPORTED_CODE_ACTIONS", "APPLY_FIX_ALL_COMMAND_ID", "CodeActionOracle", "constructor", "_editor", "_markerService", "_signalChange", "_delay", "_autoTriggerTimer", "_register", "onMarkerChanged", "e", "_onMarkerChanges", "onDidChangeCursorPosition", "_tryAutoTrigger", "trigger", "selection", "_getRangeOfSelectionUnlessWhitespaceEnclosed", "undefined", "resources", "model", "getModel", "some", "resource", "uri", "cancelAndSet", "type", "triggerAction", "<PERSON><PERSON><PERSON>", "hasModel", "getSelection", "enabled", "getOption", "Off", "On", "OnCode", "isSelectionEmpty", "isEmpty", "lineNumber", "column", "getPosition", "line", "get<PERSON>ineC<PERSON>nt", "length", "test", "getLineMaxColumn", "CodeActionsState", "Empty", "Triggered", "position", "_cancellablePromise", "actions", "catch", "emptyCodeActionSet", "cancel", "Object", "freeze", "allActions", "validActions", "dispose", "documentation", "hasAutoFix", "hasAIFix", "allAIFixes", "CodeActionModel", "_registry", "contextKeyService", "_progressService", "_configurationService", "_telemetryService", "_codeActionOracle", "_state", "_onDidChangeState", "onDidChangeState", "event", "_disposed", "_supportedCodeActions", "bindTo", "onDidChangeModel", "_update", "onDidChangeModelLanguage", "onDidChange", "onDidChangeConfiguration", "has<PERSON><PERSON>ed", "setState", "_settingEnabledNearbyQuickfixes", "getValue", "_this", "value", "has", "supportedActions", "all", "flatMap", "provider", "providedCodeActionKinds", "set", "join", "startPosition", "getStartPosition", "_ref", "_asyncToGenerator", "token", "QuickFix", "filter", "include", "contains", "codeActionSet", "None", "allCodeActions", "isCancellationRequested", "foundQuickfix", "action", "kind", "allMarkers", "read", "command", "arguments", "arg", "includes", "diagnostics", "marker", "relatedInformation", "currPosition", "trackedPosition", "distance", "Number", "MAX_VALUE", "currentActions", "col", "endColumn", "row", "endLineNumber", "startRow", "startLineNumber", "newCodeActionTrigger", "autoApply", "context", "notAvailableMessage", "selectionAsPosition", "actionsAtMarker", "push", "Math", "abs", "unshift", "filteredActions", "index", "self", "findIndex", "a", "title", "sort", "b", "isPreferred", "isAI", "sw", "codeActions", "publicLog2", "duration", "elapsed", "_x", "apply", "showWhile", "newState", "isManualToAutoTransition", "setTimeout", "reset", "skipNotify", "fire"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/contrib/codeAction/browser/codeActionModel.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { createCancelablePromise, TimeoutTimer } from '../../../../base/common/async.js';\nimport { isCancellationError } from '../../../../base/common/errors.js';\nimport { Emitter } from '../../../../base/common/event.js';\nimport { Disposable, MutableDisposable } from '../../../../base/common/lifecycle.js';\nimport { isEqual } from '../../../../base/common/resources.js';\nimport { ShowLightbulbIconMode } from '../../../common/config/editorOptions.js';\nimport { Position } from '../../../common/core/position.js';\nimport { Selection } from '../../../common/core/selection.js';\nimport { RawContextKey } from '../../../../platform/contextkey/common/contextkey.js';\nimport { Progress } from '../../../../platform/progress/common/progress.js';\nimport { CodeActionKind, CodeActionTriggerSource } from '../common/types.js';\nimport { getCodeActions } from './codeAction.js';\nimport { HierarchicalKind } from '../../../../base/common/hierarchicalKind.js';\nimport { StopWatch } from '../../../../base/common/stopwatch.js';\nexport const SUPPORTED_CODE_ACTIONS = new RawContextKey('supportedCodeAction', '');\nexport const APPLY_FIX_ALL_COMMAND_ID = '_typescript.applyFixAllCodeAction';\nclass CodeActionOracle extends Disposable {\n    constructor(_editor, _markerService, _signalChange, _delay = 250) {\n        super();\n        this._editor = _editor;\n        this._markerService = _markerService;\n        this._signalChange = _signalChange;\n        this._delay = _delay;\n        this._autoTriggerTimer = this._register(new TimeoutTimer());\n        this._register(this._markerService.onMarkerChanged(e => this._onMarkerChanges(e)));\n        this._register(this._editor.onDidChangeCursorPosition(() => this._tryAutoTrigger()));\n    }\n    trigger(trigger) {\n        const selection = this._getRangeOfSelectionUnlessWhitespaceEnclosed(trigger);\n        this._signalChange(selection ? { trigger, selection } : undefined);\n    }\n    _onMarkerChanges(resources) {\n        const model = this._editor.getModel();\n        if (model && resources.some(resource => isEqual(resource, model.uri))) {\n            this._tryAutoTrigger();\n        }\n    }\n    _tryAutoTrigger() {\n        this._autoTriggerTimer.cancelAndSet(() => {\n            this.trigger({ type: 2 /* CodeActionTriggerType.Auto */, triggerAction: CodeActionTriggerSource.Default });\n        }, this._delay);\n    }\n    _getRangeOfSelectionUnlessWhitespaceEnclosed(trigger) {\n        if (!this._editor.hasModel()) {\n            return undefined;\n        }\n        const selection = this._editor.getSelection();\n        if (trigger.type === 1 /* CodeActionTriggerType.Invoke */) {\n            return selection;\n        }\n        const enabled = this._editor.getOption(65 /* EditorOption.lightbulb */).enabled;\n        if (enabled === ShowLightbulbIconMode.Off) {\n            return undefined;\n        }\n        else if (enabled === ShowLightbulbIconMode.On) {\n            return selection;\n        }\n        else if (enabled === ShowLightbulbIconMode.OnCode) {\n            const isSelectionEmpty = selection.isEmpty();\n            if (!isSelectionEmpty) {\n                return selection;\n            }\n            const model = this._editor.getModel();\n            const { lineNumber, column } = selection.getPosition();\n            const line = model.getLineContent(lineNumber);\n            if (line.length === 0) {\n                // empty line\n                return undefined;\n            }\n            else if (column === 1) {\n                // look only right\n                if (/\\s/.test(line[0])) {\n                    return undefined;\n                }\n            }\n            else if (column === model.getLineMaxColumn(lineNumber)) {\n                // look only left\n                if (/\\s/.test(line[line.length - 1])) {\n                    return undefined;\n                }\n            }\n            else {\n                // look left and right\n                if (/\\s/.test(line[column - 2]) && /\\s/.test(line[column - 1])) {\n                    return undefined;\n                }\n            }\n        }\n        return selection;\n    }\n}\nexport var CodeActionsState;\n(function (CodeActionsState) {\n    CodeActionsState.Empty = { type: 0 /* Type.Empty */ };\n    class Triggered {\n        constructor(trigger, position, _cancellablePromise) {\n            this.trigger = trigger;\n            this.position = position;\n            this._cancellablePromise = _cancellablePromise;\n            this.type = 1 /* Type.Triggered */;\n            this.actions = _cancellablePromise.catch((e) => {\n                if (isCancellationError(e)) {\n                    return emptyCodeActionSet;\n                }\n                throw e;\n            });\n        }\n        cancel() {\n            this._cancellablePromise.cancel();\n        }\n    }\n    CodeActionsState.Triggered = Triggered;\n})(CodeActionsState || (CodeActionsState = {}));\nconst emptyCodeActionSet = Object.freeze({\n    allActions: [],\n    validActions: [],\n    dispose: () => { },\n    documentation: [],\n    hasAutoFix: false,\n    hasAIFix: false,\n    allAIFixes: false,\n});\nexport class CodeActionModel extends Disposable {\n    constructor(_editor, _registry, _markerService, contextKeyService, _progressService, _configurationService, _telemetryService) {\n        super();\n        this._editor = _editor;\n        this._registry = _registry;\n        this._markerService = _markerService;\n        this._progressService = _progressService;\n        this._configurationService = _configurationService;\n        this._telemetryService = _telemetryService;\n        this._codeActionOracle = this._register(new MutableDisposable());\n        this._state = CodeActionsState.Empty;\n        this._onDidChangeState = this._register(new Emitter());\n        this.onDidChangeState = this._onDidChangeState.event;\n        this._disposed = false;\n        this._supportedCodeActions = SUPPORTED_CODE_ACTIONS.bindTo(contextKeyService);\n        this._register(this._editor.onDidChangeModel(() => this._update()));\n        this._register(this._editor.onDidChangeModelLanguage(() => this._update()));\n        this._register(this._registry.onDidChange(() => this._update()));\n        this._register(this._editor.onDidChangeConfiguration((e) => {\n            if (e.hasChanged(65 /* EditorOption.lightbulb */)) {\n                this._update();\n            }\n        }));\n        this._update();\n    }\n    dispose() {\n        if (this._disposed) {\n            return;\n        }\n        this._disposed = true;\n        super.dispose();\n        this.setState(CodeActionsState.Empty, true);\n    }\n    _settingEnabledNearbyQuickfixes() {\n        const model = this._editor?.getModel();\n        return this._configurationService ? this._configurationService.getValue('editor.codeActionWidget.includeNearbyQuickFixes', { resource: model?.uri }) : false;\n    }\n    _update() {\n        if (this._disposed) {\n            return;\n        }\n        this._codeActionOracle.value = undefined;\n        this.setState(CodeActionsState.Empty);\n        const model = this._editor.getModel();\n        if (model\n            && this._registry.has(model)\n            && !this._editor.getOption(92 /* EditorOption.readOnly */)) {\n            const supportedActions = this._registry.all(model).flatMap(provider => provider.providedCodeActionKinds ?? []);\n            this._supportedCodeActions.set(supportedActions.join(' '));\n            this._codeActionOracle.value = new CodeActionOracle(this._editor, this._markerService, trigger => {\n                if (!trigger) {\n                    this.setState(CodeActionsState.Empty);\n                    return;\n                }\n                const startPosition = trigger.selection.getStartPosition();\n                const actions = createCancelablePromise(async (token) => {\n                    if (this._settingEnabledNearbyQuickfixes() && trigger.trigger.type === 1 /* CodeActionTriggerType.Invoke */ && (trigger.trigger.triggerAction === CodeActionTriggerSource.QuickFix || trigger.trigger.filter?.include?.contains(CodeActionKind.QuickFix))) {\n                        const codeActionSet = await getCodeActions(this._registry, model, trigger.selection, trigger.trigger, Progress.None, token);\n                        const allCodeActions = [...codeActionSet.allActions];\n                        if (token.isCancellationRequested) {\n                            return emptyCodeActionSet;\n                        }\n                        // Search for quickfixes in the curret code action set.\n                        const foundQuickfix = codeActionSet.validActions?.some(action => action.action.kind ? CodeActionKind.QuickFix.contains(new HierarchicalKind(action.action.kind)) : false);\n                        const allMarkers = this._markerService.read({ resource: model.uri });\n                        if (foundQuickfix) {\n                            for (const action of codeActionSet.validActions) {\n                                if (action.action.command?.arguments?.some(arg => typeof arg === 'string' && arg.includes(APPLY_FIX_ALL_COMMAND_ID))) {\n                                    action.action.diagnostics = [...allMarkers.filter(marker => marker.relatedInformation)];\n                                }\n                            }\n                            return { validActions: codeActionSet.validActions, allActions: allCodeActions, documentation: codeActionSet.documentation, hasAutoFix: codeActionSet.hasAutoFix, hasAIFix: codeActionSet.hasAIFix, allAIFixes: codeActionSet.allAIFixes, dispose: () => { codeActionSet.dispose(); } };\n                        }\n                        else if (!foundQuickfix) {\n                            // If markers exists, and there are no quickfixes found or length is zero, check for quickfixes on that line.\n                            if (allMarkers.length > 0) {\n                                const currPosition = trigger.selection.getPosition();\n                                let trackedPosition = currPosition;\n                                let distance = Number.MAX_VALUE;\n                                const currentActions = [...codeActionSet.validActions];\n                                for (const marker of allMarkers) {\n                                    const col = marker.endColumn;\n                                    const row = marker.endLineNumber;\n                                    const startRow = marker.startLineNumber;\n                                    // Found quickfix on the same line and check relative distance to other markers\n                                    if ((row === currPosition.lineNumber || startRow === currPosition.lineNumber)) {\n                                        trackedPosition = new Position(row, col);\n                                        const newCodeActionTrigger = {\n                                            type: trigger.trigger.type,\n                                            triggerAction: trigger.trigger.triggerAction,\n                                            filter: { include: trigger.trigger.filter?.include ? trigger.trigger.filter?.include : CodeActionKind.QuickFix },\n                                            autoApply: trigger.trigger.autoApply,\n                                            context: { notAvailableMessage: trigger.trigger.context?.notAvailableMessage || '', position: trackedPosition }\n                                        };\n                                        const selectionAsPosition = new Selection(trackedPosition.lineNumber, trackedPosition.column, trackedPosition.lineNumber, trackedPosition.column);\n                                        const actionsAtMarker = await getCodeActions(this._registry, model, selectionAsPosition, newCodeActionTrigger, Progress.None, token);\n                                        if (actionsAtMarker.validActions.length !== 0) {\n                                            for (const action of actionsAtMarker.validActions) {\n                                                if (action.action.command?.arguments?.some(arg => typeof arg === 'string' && arg.includes(APPLY_FIX_ALL_COMMAND_ID))) {\n                                                    action.action.diagnostics = [...allMarkers.filter(marker => marker.relatedInformation)];\n                                                }\n                                            }\n                                            if (codeActionSet.allActions.length === 0) {\n                                                allCodeActions.push(...actionsAtMarker.allActions);\n                                            }\n                                            // Already filtered through to only get quickfixes, so no need to filter again.\n                                            if (Math.abs(currPosition.column - col) < distance) {\n                                                currentActions.unshift(...actionsAtMarker.validActions);\n                                            }\n                                            else {\n                                                currentActions.push(...actionsAtMarker.validActions);\n                                            }\n                                        }\n                                        distance = Math.abs(currPosition.column - col);\n                                    }\n                                }\n                                const filteredActions = currentActions.filter((action, index, self) => self.findIndex((a) => a.action.title === action.action.title) === index);\n                                filteredActions.sort((a, b) => {\n                                    if (a.action.isPreferred && !b.action.isPreferred) {\n                                        return -1;\n                                    }\n                                    else if (!a.action.isPreferred && b.action.isPreferred) {\n                                        return 1;\n                                    }\n                                    else if (a.action.isAI && !b.action.isAI) {\n                                        return 1;\n                                    }\n                                    else if (!a.action.isAI && b.action.isAI) {\n                                        return -1;\n                                    }\n                                    else {\n                                        return 0;\n                                    }\n                                });\n                                // Only retriggers if actually found quickfix on the same line as cursor\n                                return { validActions: filteredActions, allActions: allCodeActions, documentation: codeActionSet.documentation, hasAutoFix: codeActionSet.hasAutoFix, hasAIFix: codeActionSet.hasAIFix, allAIFixes: codeActionSet.allAIFixes, dispose: () => { codeActionSet.dispose(); } };\n                            }\n                        }\n                    }\n                    // Case for manual triggers - specifically Source Actions and Refactors\n                    if (trigger.trigger.type === 1 /* CodeActionTriggerType.Invoke */) {\n                        const sw = new StopWatch();\n                        const codeActions = await getCodeActions(this._registry, model, trigger.selection, trigger.trigger, Progress.None, token);\n                        // Telemetry for duration of each code action on save.\n                        if (this._telemetryService) {\n                            this._telemetryService.publicLog2('codeAction.invokedDurations', {\n                                codeActions: codeActions.validActions.length,\n                                duration: sw.elapsed()\n                            });\n                        }\n                        return codeActions;\n                    }\n                    return getCodeActions(this._registry, model, trigger.selection, trigger.trigger, Progress.None, token);\n                });\n                if (trigger.trigger.type === 1 /* CodeActionTriggerType.Invoke */) {\n                    this._progressService?.showWhile(actions, 250);\n                }\n                const newState = new CodeActionsState.Triggered(trigger.trigger, startPosition, actions);\n                let isManualToAutoTransition = false;\n                if (this._state.type === 1 /* CodeActionsState.Type.Triggered */) {\n                    // Check if the current state is manual and the new state is automatic\n                    isManualToAutoTransition = this._state.trigger.type === 1 /* CodeActionTriggerType.Invoke */ &&\n                        newState.type === 1 /* CodeActionsState.Type.Triggered */ &&\n                        newState.trigger.type === 2 /* CodeActionTriggerType.Auto */ &&\n                        this._state.position !== newState.position;\n                }\n                // Do not trigger state if current state is manual and incoming state is automatic\n                if (!isManualToAutoTransition) {\n                    this.setState(newState);\n                }\n                else {\n                    // Reset the new state after getting code actions back.\n                    setTimeout(() => {\n                        this.setState(newState);\n                    }, 500);\n                }\n            }, undefined);\n            this._codeActionOracle.value.trigger({ type: 2 /* CodeActionTriggerType.Auto */, triggerAction: CodeActionTriggerSource.Default });\n        }\n        else {\n            this._supportedCodeActions.reset();\n        }\n    }\n    trigger(trigger) {\n        this._codeActionOracle.value?.trigger(trigger);\n    }\n    setState(newState, skipNotify) {\n        if (newState === this._state) {\n            return;\n        }\n        // Cancel old request\n        if (this._state.type === 1 /* CodeActionsState.Type.Triggered */) {\n            this._state.cancel();\n        }\n        this._state = newState;\n        if (!skipNotify && !this._disposed) {\n            this._onDidChangeState.fire(newState);\n        }\n    }\n}\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA,SAASA,uBAAuB,EAAEC,YAAY,QAAQ,kCAAkC;AACxF,SAASC,mBAAmB,QAAQ,mCAAmC;AACvE,SAASC,OAAO,QAAQ,kCAAkC;AAC1D,SAASC,UAAU,EAAEC,iBAAiB,QAAQ,sCAAsC;AACpF,SAASC,OAAO,QAAQ,sCAAsC;AAC9D,SAASC,qBAAqB,QAAQ,yCAAyC;AAC/E,SAASC,QAAQ,QAAQ,kCAAkC;AAC3D,SAASC,SAAS,QAAQ,mCAAmC;AAC7D,SAASC,aAAa,QAAQ,sDAAsD;AACpF,SAASC,QAAQ,QAAQ,kDAAkD;AAC3E,SAASC,cAAc,EAAEC,uBAAuB,QAAQ,oBAAoB;AAC5E,SAASC,cAAc,QAAQ,iBAAiB;AAChD,SAASC,gBAAgB,QAAQ,6CAA6C;AAC9E,SAASC,SAAS,QAAQ,sCAAsC;AAChE,OAAO,MAAMC,sBAAsB,GAAG,IAAIP,aAAa,CAAC,qBAAqB,EAAE,EAAE,CAAC;AAClF,OAAO,MAAMQ,wBAAwB,GAAG,mCAAmC;AAC3E,MAAMC,gBAAgB,SAASf,UAAU,CAAC;EACtCgB,WAAWA,CAACC,OAAO,EAAEC,cAAc,EAAEC,aAAa,EAAEC,MAAM,GAAG,GAAG,EAAE;IAC9D,KAAK,CAAC,CAAC;IACP,IAAI,CAACH,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,iBAAiB,GAAG,IAAI,CAACC,SAAS,CAAC,IAAIzB,YAAY,CAAC,CAAC,CAAC;IAC3D,IAAI,CAACyB,SAAS,CAAC,IAAI,CAACJ,cAAc,CAACK,eAAe,CAACC,CAAC,IAAI,IAAI,CAACC,gBAAgB,CAACD,CAAC,CAAC,CAAC,CAAC;IAClF,IAAI,CAACF,SAAS,CAAC,IAAI,CAACL,OAAO,CAACS,yBAAyB,CAAC,MAAM,IAAI,CAACC,eAAe,CAAC,CAAC,CAAC,CAAC;EACxF;EACAC,OAAOA,CAACA,OAAO,EAAE;IACb,MAAMC,SAAS,GAAG,IAAI,CAACC,4CAA4C,CAACF,OAAO,CAAC;IAC5E,IAAI,CAACT,aAAa,CAACU,SAAS,GAAG;MAAED,OAAO;MAAEC;IAAU,CAAC,GAAGE,SAAS,CAAC;EACtE;EACAN,gBAAgBA,CAACO,SAAS,EAAE;IACxB,MAAMC,KAAK,GAAG,IAAI,CAAChB,OAAO,CAACiB,QAAQ,CAAC,CAAC;IACrC,IAAID,KAAK,IAAID,SAAS,CAACG,IAAI,CAACC,QAAQ,IAAIlC,OAAO,CAACkC,QAAQ,EAAEH,KAAK,CAACI,GAAG,CAAC,CAAC,EAAE;MACnE,IAAI,CAACV,eAAe,CAAC,CAAC;IAC1B;EACJ;EACAA,eAAeA,CAAA,EAAG;IACd,IAAI,CAACN,iBAAiB,CAACiB,YAAY,CAAC,MAAM;MACtC,IAAI,CAACV,OAAO,CAAC;QAAEW,IAAI,EAAE,CAAC,CAAC;QAAkCC,aAAa,EAAE/B,uBAAuB,CAACgC;MAAQ,CAAC,CAAC;IAC9G,CAAC,EAAE,IAAI,CAACrB,MAAM,CAAC;EACnB;EACAU,4CAA4CA,CAACF,OAAO,EAAE;IAClD,IAAI,CAAC,IAAI,CAACX,OAAO,CAACyB,QAAQ,CAAC,CAAC,EAAE;MAC1B,OAAOX,SAAS;IACpB;IACA,MAAMF,SAAS,GAAG,IAAI,CAACZ,OAAO,CAAC0B,YAAY,CAAC,CAAC;IAC7C,IAAIf,OAAO,CAACW,IAAI,KAAK,CAAC,CAAC,oCAAoC;MACvD,OAAOV,SAAS;IACpB;IACA,MAAMe,OAAO,GAAG,IAAI,CAAC3B,OAAO,CAAC4B,SAAS,CAAC,EAAE,CAAC,4BAA4B,CAAC,CAACD,OAAO;IAC/E,IAAIA,OAAO,KAAKzC,qBAAqB,CAAC2C,GAAG,EAAE;MACvC,OAAOf,SAAS;IACpB,CAAC,MACI,IAAIa,OAAO,KAAKzC,qBAAqB,CAAC4C,EAAE,EAAE;MAC3C,OAAOlB,SAAS;IACpB,CAAC,MACI,IAAIe,OAAO,KAAKzC,qBAAqB,CAAC6C,MAAM,EAAE;MAC/C,MAAMC,gBAAgB,GAAGpB,SAAS,CAACqB,OAAO,CAAC,CAAC;MAC5C,IAAI,CAACD,gBAAgB,EAAE;QACnB,OAAOpB,SAAS;MACpB;MACA,MAAMI,KAAK,GAAG,IAAI,CAAChB,OAAO,CAACiB,QAAQ,CAAC,CAAC;MACrC,MAAM;QAAEiB,UAAU;QAAEC;MAAO,CAAC,GAAGvB,SAAS,CAACwB,WAAW,CAAC,CAAC;MACtD,MAAMC,IAAI,GAAGrB,KAAK,CAACsB,cAAc,CAACJ,UAAU,CAAC;MAC7C,IAAIG,IAAI,CAACE,MAAM,KAAK,CAAC,EAAE;QACnB;QACA,OAAOzB,SAAS;MACpB,CAAC,MACI,IAAIqB,MAAM,KAAK,CAAC,EAAE;QACnB;QACA,IAAI,IAAI,CAACK,IAAI,CAACH,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;UACpB,OAAOvB,SAAS;QACpB;MACJ,CAAC,MACI,IAAIqB,MAAM,KAAKnB,KAAK,CAACyB,gBAAgB,CAACP,UAAU,CAAC,EAAE;QACpD;QACA,IAAI,IAAI,CAACM,IAAI,CAACH,IAAI,CAACA,IAAI,CAACE,MAAM,GAAG,CAAC,CAAC,CAAC,EAAE;UAClC,OAAOzB,SAAS;QACpB;MACJ,CAAC,MACI;QACD;QACA,IAAI,IAAI,CAAC0B,IAAI,CAACH,IAAI,CAACF,MAAM,GAAG,CAAC,CAAC,CAAC,IAAI,IAAI,CAACK,IAAI,CAACH,IAAI,CAACF,MAAM,GAAG,CAAC,CAAC,CAAC,EAAE;UAC5D,OAAOrB,SAAS;QACpB;MACJ;IACJ;IACA,OAAOF,SAAS;EACpB;AACJ;AACA,OAAO,IAAI8B,gBAAgB;AAC3B,CAAC,UAAUA,gBAAgB,EAAE;EACzBA,gBAAgB,CAACC,KAAK,GAAG;IAAErB,IAAI,EAAE,CAAC,CAAC;EAAiB,CAAC;EACrD,MAAMsB,SAAS,CAAC;IACZ7C,WAAWA,CAACY,OAAO,EAAEkC,QAAQ,EAAEC,mBAAmB,EAAE;MAChD,IAAI,CAACnC,OAAO,GAAGA,OAAO;MACtB,IAAI,CAACkC,QAAQ,GAAGA,QAAQ;MACxB,IAAI,CAACC,mBAAmB,GAAGA,mBAAmB;MAC9C,IAAI,CAACxB,IAAI,GAAG,CAAC,CAAC;MACd,IAAI,CAACyB,OAAO,GAAGD,mBAAmB,CAACE,KAAK,CAAEzC,CAAC,IAAK;QAC5C,IAAI1B,mBAAmB,CAAC0B,CAAC,CAAC,EAAE;UACxB,OAAO0C,kBAAkB;QAC7B;QACA,MAAM1C,CAAC;MACX,CAAC,CAAC;IACN;IACA2C,MAAMA,CAAA,EAAG;MACL,IAAI,CAACJ,mBAAmB,CAACI,MAAM,CAAC,CAAC;IACrC;EACJ;EACAR,gBAAgB,CAACE,SAAS,GAAGA,SAAS;AAC1C,CAAC,EAAEF,gBAAgB,KAAKA,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/C,MAAMO,kBAAkB,GAAGE,MAAM,CAACC,MAAM,CAAC;EACrCC,UAAU,EAAE,EAAE;EACdC,YAAY,EAAE,EAAE;EAChBC,OAAO,EAAEA,CAAA,KAAM,CAAE,CAAC;EAClBC,aAAa,EAAE,EAAE;EACjBC,UAAU,EAAE,KAAK;EACjBC,QAAQ,EAAE,KAAK;EACfC,UAAU,EAAE;AAChB,CAAC,CAAC;AACF,OAAO,MAAMC,eAAe,SAAS7E,UAAU,CAAC;EAC5CgB,WAAWA,CAACC,OAAO,EAAE6D,SAAS,EAAE5D,cAAc,EAAE6D,iBAAiB,EAAEC,gBAAgB,EAAEC,qBAAqB,EAAEC,iBAAiB,EAAE;IAC3H,KAAK,CAAC,CAAC;IACP,IAAI,CAACjE,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC6D,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAAC5D,cAAc,GAAGA,cAAc;IACpC,IAAI,CAAC8D,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACC,qBAAqB,GAAGA,qBAAqB;IAClD,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACC,iBAAiB,GAAG,IAAI,CAAC7D,SAAS,CAAC,IAAIrB,iBAAiB,CAAC,CAAC,CAAC;IAChE,IAAI,CAACmF,MAAM,GAAGzB,gBAAgB,CAACC,KAAK;IACpC,IAAI,CAACyB,iBAAiB,GAAG,IAAI,CAAC/D,SAAS,CAAC,IAAIvB,OAAO,CAAC,CAAC,CAAC;IACtD,IAAI,CAACuF,gBAAgB,GAAG,IAAI,CAACD,iBAAiB,CAACE,KAAK;IACpD,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,qBAAqB,GAAG5E,sBAAsB,CAAC6E,MAAM,CAACX,iBAAiB,CAAC;IAC7E,IAAI,CAACzD,SAAS,CAAC,IAAI,CAACL,OAAO,CAAC0E,gBAAgB,CAAC,MAAM,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC;IACnE,IAAI,CAACtE,SAAS,CAAC,IAAI,CAACL,OAAO,CAAC4E,wBAAwB,CAAC,MAAM,IAAI,CAACD,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3E,IAAI,CAACtE,SAAS,CAAC,IAAI,CAACwD,SAAS,CAACgB,WAAW,CAAC,MAAM,IAAI,CAACF,OAAO,CAAC,CAAC,CAAC,CAAC;IAChE,IAAI,CAACtE,SAAS,CAAC,IAAI,CAACL,OAAO,CAAC8E,wBAAwB,CAAEvE,CAAC,IAAK;MACxD,IAAIA,CAAC,CAACwE,UAAU,CAAC,EAAE,CAAC,4BAA4B,CAAC,EAAE;QAC/C,IAAI,CAACJ,OAAO,CAAC,CAAC;MAClB;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,CAACA,OAAO,CAAC,CAAC;EAClB;EACApB,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACgB,SAAS,EAAE;MAChB;IACJ;IACA,IAAI,CAACA,SAAS,GAAG,IAAI;IACrB,KAAK,CAAChB,OAAO,CAAC,CAAC;IACf,IAAI,CAACyB,QAAQ,CAACtC,gBAAgB,CAACC,KAAK,EAAE,IAAI,CAAC;EAC/C;EACAsC,+BAA+BA,CAAA,EAAG;IAC9B,MAAMjE,KAAK,GAAG,IAAI,CAAChB,OAAO,EAAEiB,QAAQ,CAAC,CAAC;IACtC,OAAO,IAAI,CAAC+C,qBAAqB,GAAG,IAAI,CAACA,qBAAqB,CAACkB,QAAQ,CAAC,iDAAiD,EAAE;MAAE/D,QAAQ,EAAEH,KAAK,EAAEI;IAAI,CAAC,CAAC,GAAG,KAAK;EAChK;EACAuD,OAAOA,CAAA,EAAG;IAAA,IAAAQ,KAAA;IACN,IAAI,IAAI,CAACZ,SAAS,EAAE;MAChB;IACJ;IACA,IAAI,CAACL,iBAAiB,CAACkB,KAAK,GAAGtE,SAAS;IACxC,IAAI,CAACkE,QAAQ,CAACtC,gBAAgB,CAACC,KAAK,CAAC;IACrC,MAAM3B,KAAK,GAAG,IAAI,CAAChB,OAAO,CAACiB,QAAQ,CAAC,CAAC;IACrC,IAAID,KAAK,IACF,IAAI,CAAC6C,SAAS,CAACwB,GAAG,CAACrE,KAAK,CAAC,IACzB,CAAC,IAAI,CAAChB,OAAO,CAAC4B,SAAS,CAAC,EAAE,CAAC,2BAA2B,CAAC,EAAE;MAC5D,MAAM0D,gBAAgB,GAAG,IAAI,CAACzB,SAAS,CAAC0B,GAAG,CAACvE,KAAK,CAAC,CAACwE,OAAO,CAACC,QAAQ,IAAIA,QAAQ,CAACC,uBAAuB,IAAI,EAAE,CAAC;MAC9G,IAAI,CAAClB,qBAAqB,CAACmB,GAAG,CAACL,gBAAgB,CAACM,IAAI,CAAC,GAAG,CAAC,CAAC;MAC1D,IAAI,CAAC1B,iBAAiB,CAACkB,KAAK,GAAG,IAAItF,gBAAgB,CAAC,IAAI,CAACE,OAAO,EAAE,IAAI,CAACC,cAAc,EAAEU,OAAO,IAAI;QAC9F,IAAI,CAACA,OAAO,EAAE;UACV,IAAI,CAACqE,QAAQ,CAACtC,gBAAgB,CAACC,KAAK,CAAC;UACrC;QACJ;QACA,MAAMkD,aAAa,GAAGlF,OAAO,CAACC,SAAS,CAACkF,gBAAgB,CAAC,CAAC;QAC1D,MAAM/C,OAAO,GAAGpE,uBAAuB;UAAA,IAAAoH,IAAA,GAAAC,iBAAA,CAAC,WAAOC,KAAK,EAAK;YACrD,IAAId,KAAI,CAACF,+BAA+B,CAAC,CAAC,IAAItE,OAAO,CAACA,OAAO,CAACW,IAAI,KAAK,CAAC,CAAC,uCAAuCX,OAAO,CAACA,OAAO,CAACY,aAAa,KAAK/B,uBAAuB,CAAC0G,QAAQ,IAAIvF,OAAO,CAACA,OAAO,CAACwF,MAAM,EAAEC,OAAO,EAAEC,QAAQ,CAAC9G,cAAc,CAAC2G,QAAQ,CAAC,CAAC,EAAE;cACvP,MAAMI,aAAa,SAAS7G,cAAc,CAAC0F,KAAI,CAACtB,SAAS,EAAE7C,KAAK,EAAEL,OAAO,CAACC,SAAS,EAAED,OAAO,CAACA,OAAO,EAAErB,QAAQ,CAACiH,IAAI,EAAEN,KAAK,CAAC;cAC3H,MAAMO,cAAc,GAAG,CAAC,GAAGF,aAAa,CAACjD,UAAU,CAAC;cACpD,IAAI4C,KAAK,CAACQ,uBAAuB,EAAE;gBAC/B,OAAOxD,kBAAkB;cAC7B;cACA;cACA,MAAMyD,aAAa,GAAGJ,aAAa,CAAChD,YAAY,EAAEpC,IAAI,CAACyF,MAAM,IAAIA,MAAM,CAACA,MAAM,CAACC,IAAI,GAAGrH,cAAc,CAAC2G,QAAQ,CAACG,QAAQ,CAAC,IAAI3G,gBAAgB,CAACiH,MAAM,CAACA,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC;cACzK,MAAMC,UAAU,GAAG1B,KAAI,CAAClF,cAAc,CAAC6G,IAAI,CAAC;gBAAE3F,QAAQ,EAAEH,KAAK,CAACI;cAAI,CAAC,CAAC;cACpE,IAAIsF,aAAa,EAAE;gBACf,KAAK,MAAMC,MAAM,IAAIL,aAAa,CAAChD,YAAY,EAAE;kBAC7C,IAAIqD,MAAM,CAACA,MAAM,CAACI,OAAO,EAAEC,SAAS,EAAE9F,IAAI,CAAC+F,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAIA,GAAG,CAACC,QAAQ,CAACrH,wBAAwB,CAAC,CAAC,EAAE;oBAClH8G,MAAM,CAACA,MAAM,CAACQ,WAAW,GAAG,CAAC,GAAGN,UAAU,CAACV,MAAM,CAACiB,MAAM,IAAIA,MAAM,CAACC,kBAAkB,CAAC,CAAC;kBAC3F;gBACJ;gBACA,OAAO;kBAAE/D,YAAY,EAAEgD,aAAa,CAAChD,YAAY;kBAAED,UAAU,EAAEmD,cAAc;kBAAEhD,aAAa,EAAE8C,aAAa,CAAC9C,aAAa;kBAAEC,UAAU,EAAE6C,aAAa,CAAC7C,UAAU;kBAAEC,QAAQ,EAAE4C,aAAa,CAAC5C,QAAQ;kBAAEC,UAAU,EAAE2C,aAAa,CAAC3C,UAAU;kBAAEJ,OAAO,EAAEA,CAAA,KAAM;oBAAE+C,aAAa,CAAC/C,OAAO,CAAC,CAAC;kBAAE;gBAAE,CAAC;cAC1R,CAAC,MACI,IAAI,CAACmD,aAAa,EAAE;gBACrB;gBACA,IAAIG,UAAU,CAACtE,MAAM,GAAG,CAAC,EAAE;kBACvB,MAAM+E,YAAY,GAAG3G,OAAO,CAACC,SAAS,CAACwB,WAAW,CAAC,CAAC;kBACpD,IAAImF,eAAe,GAAGD,YAAY;kBAClC,IAAIE,QAAQ,GAAGC,MAAM,CAACC,SAAS;kBAC/B,MAAMC,cAAc,GAAG,CAAC,GAAGrB,aAAa,CAAChD,YAAY,CAAC;kBACtD,KAAK,MAAM8D,MAAM,IAAIP,UAAU,EAAE;oBAC7B,MAAMe,GAAG,GAAGR,MAAM,CAACS,SAAS;oBAC5B,MAAMC,GAAG,GAAGV,MAAM,CAACW,aAAa;oBAChC,MAAMC,QAAQ,GAAGZ,MAAM,CAACa,eAAe;oBACvC;oBACA,IAAKH,GAAG,KAAKR,YAAY,CAACpF,UAAU,IAAI8F,QAAQ,KAAKV,YAAY,CAACpF,UAAU,EAAG;sBAC3EqF,eAAe,GAAG,IAAIpI,QAAQ,CAAC2I,GAAG,EAAEF,GAAG,CAAC;sBACxC,MAAMM,oBAAoB,GAAG;wBACzB5G,IAAI,EAAEX,OAAO,CAACA,OAAO,CAACW,IAAI;wBAC1BC,aAAa,EAAEZ,OAAO,CAACA,OAAO,CAACY,aAAa;wBAC5C4E,MAAM,EAAE;0BAAEC,OAAO,EAAEzF,OAAO,CAACA,OAAO,CAACwF,MAAM,EAAEC,OAAO,GAAGzF,OAAO,CAACA,OAAO,CAACwF,MAAM,EAAEC,OAAO,GAAG7G,cAAc,CAAC2G;wBAAS,CAAC;wBAChHiC,SAAS,EAAExH,OAAO,CAACA,OAAO,CAACwH,SAAS;wBACpCC,OAAO,EAAE;0BAAEC,mBAAmB,EAAE1H,OAAO,CAACA,OAAO,CAACyH,OAAO,EAAEC,mBAAmB,IAAI,EAAE;0BAAExF,QAAQ,EAAE0E;wBAAgB;sBAClH,CAAC;sBACD,MAAMe,mBAAmB,GAAG,IAAIlJ,SAAS,CAACmI,eAAe,CAACrF,UAAU,EAAEqF,eAAe,CAACpF,MAAM,EAAEoF,eAAe,CAACrF,UAAU,EAAEqF,eAAe,CAACpF,MAAM,CAAC;sBACjJ,MAAMoG,eAAe,SAAS9I,cAAc,CAAC0F,KAAI,CAACtB,SAAS,EAAE7C,KAAK,EAAEsH,mBAAmB,EAAEJ,oBAAoB,EAAE5I,QAAQ,CAACiH,IAAI,EAAEN,KAAK,CAAC;sBACpI,IAAIsC,eAAe,CAACjF,YAAY,CAACf,MAAM,KAAK,CAAC,EAAE;wBAC3C,KAAK,MAAMoE,MAAM,IAAI4B,eAAe,CAACjF,YAAY,EAAE;0BAC/C,IAAIqD,MAAM,CAACA,MAAM,CAACI,OAAO,EAAEC,SAAS,EAAE9F,IAAI,CAAC+F,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAIA,GAAG,CAACC,QAAQ,CAACrH,wBAAwB,CAAC,CAAC,EAAE;4BAClH8G,MAAM,CAACA,MAAM,CAACQ,WAAW,GAAG,CAAC,GAAGN,UAAU,CAACV,MAAM,CAACiB,MAAM,IAAIA,MAAM,CAACC,kBAAkB,CAAC,CAAC;0BAC3F;wBACJ;wBACA,IAAIf,aAAa,CAACjD,UAAU,CAACd,MAAM,KAAK,CAAC,EAAE;0BACvCiE,cAAc,CAACgC,IAAI,CAAC,GAAGD,eAAe,CAAClF,UAAU,CAAC;wBACtD;wBACA;wBACA,IAAIoF,IAAI,CAACC,GAAG,CAACpB,YAAY,CAACnF,MAAM,GAAGyF,GAAG,CAAC,GAAGJ,QAAQ,EAAE;0BAChDG,cAAc,CAACgB,OAAO,CAAC,GAAGJ,eAAe,CAACjF,YAAY,CAAC;wBAC3D,CAAC,MACI;0BACDqE,cAAc,CAACa,IAAI,CAAC,GAAGD,eAAe,CAACjF,YAAY,CAAC;wBACxD;sBACJ;sBACAkE,QAAQ,GAAGiB,IAAI,CAACC,GAAG,CAACpB,YAAY,CAACnF,MAAM,GAAGyF,GAAG,CAAC;oBAClD;kBACJ;kBACA,MAAMgB,eAAe,GAAGjB,cAAc,CAACxB,MAAM,CAAC,CAACQ,MAAM,EAAEkC,KAAK,EAAEC,IAAI,KAAKA,IAAI,CAACC,SAAS,CAAEC,CAAC,IAAKA,CAAC,CAACrC,MAAM,CAACsC,KAAK,KAAKtC,MAAM,CAACA,MAAM,CAACsC,KAAK,CAAC,KAAKJ,KAAK,CAAC;kBAC/ID,eAAe,CAACM,IAAI,CAAC,CAACF,CAAC,EAAEG,CAAC,KAAK;oBAC3B,IAAIH,CAAC,CAACrC,MAAM,CAACyC,WAAW,IAAI,CAACD,CAAC,CAACxC,MAAM,CAACyC,WAAW,EAAE;sBAC/C,OAAO,CAAC,CAAC;oBACb,CAAC,MACI,IAAI,CAACJ,CAAC,CAACrC,MAAM,CAACyC,WAAW,IAAID,CAAC,CAACxC,MAAM,CAACyC,WAAW,EAAE;sBACpD,OAAO,CAAC;oBACZ,CAAC,MACI,IAAIJ,CAAC,CAACrC,MAAM,CAAC0C,IAAI,IAAI,CAACF,CAAC,CAACxC,MAAM,CAAC0C,IAAI,EAAE;sBACtC,OAAO,CAAC;oBACZ,CAAC,MACI,IAAI,CAACL,CAAC,CAACrC,MAAM,CAAC0C,IAAI,IAAIF,CAAC,CAACxC,MAAM,CAAC0C,IAAI,EAAE;sBACtC,OAAO,CAAC,CAAC;oBACb,CAAC,MACI;sBACD,OAAO,CAAC;oBACZ;kBACJ,CAAC,CAAC;kBACF;kBACA,OAAO;oBAAE/F,YAAY,EAAEsF,eAAe;oBAAEvF,UAAU,EAAEmD,cAAc;oBAAEhD,aAAa,EAAE8C,aAAa,CAAC9C,aAAa;oBAAEC,UAAU,EAAE6C,aAAa,CAAC7C,UAAU;oBAAEC,QAAQ,EAAE4C,aAAa,CAAC5C,QAAQ;oBAAEC,UAAU,EAAE2C,aAAa,CAAC3C,UAAU;oBAAEJ,OAAO,EAAEA,CAAA,KAAM;sBAAE+C,aAAa,CAAC/C,OAAO,CAAC,CAAC;oBAAE;kBAAE,CAAC;gBAC/Q;cACJ;YACJ;YACA;YACA,IAAI5C,OAAO,CAACA,OAAO,CAACW,IAAI,KAAK,CAAC,CAAC,oCAAoC;cAC/D,MAAMgI,EAAE,GAAG,IAAI3J,SAAS,CAAC,CAAC;cAC1B,MAAM4J,WAAW,SAAS9J,cAAc,CAAC0F,KAAI,CAACtB,SAAS,EAAE7C,KAAK,EAAEL,OAAO,CAACC,SAAS,EAAED,OAAO,CAACA,OAAO,EAAErB,QAAQ,CAACiH,IAAI,EAAEN,KAAK,CAAC;cACzH;cACA,IAAId,KAAI,CAAClB,iBAAiB,EAAE;gBACxBkB,KAAI,CAAClB,iBAAiB,CAACuF,UAAU,CAAC,6BAA6B,EAAE;kBAC7DD,WAAW,EAAEA,WAAW,CAACjG,YAAY,CAACf,MAAM;kBAC5CkH,QAAQ,EAAEH,EAAE,CAACI,OAAO,CAAC;gBACzB,CAAC,CAAC;cACN;cACA,OAAOH,WAAW;YACtB;YACA,OAAO9J,cAAc,CAAC0F,KAAI,CAACtB,SAAS,EAAE7C,KAAK,EAAEL,OAAO,CAACC,SAAS,EAAED,OAAO,CAACA,OAAO,EAAErB,QAAQ,CAACiH,IAAI,EAAEN,KAAK,CAAC;UAC1G,CAAC;UAAA,iBAAA0D,EAAA;YAAA,OAAA5D,IAAA,CAAA6D,KAAA,OAAA5C,SAAA;UAAA;QAAA,IAAC;QACF,IAAIrG,OAAO,CAACA,OAAO,CAACW,IAAI,KAAK,CAAC,CAAC,oCAAoC;UAC/D,IAAI,CAACyC,gBAAgB,EAAE8F,SAAS,CAAC9G,OAAO,EAAE,GAAG,CAAC;QAClD;QACA,MAAM+G,QAAQ,GAAG,IAAIpH,gBAAgB,CAACE,SAAS,CAACjC,OAAO,CAACA,OAAO,EAAEkF,aAAa,EAAE9C,OAAO,CAAC;QACxF,IAAIgH,wBAAwB,GAAG,KAAK;QACpC,IAAI,IAAI,CAAC5F,MAAM,CAAC7C,IAAI,KAAK,CAAC,CAAC,uCAAuC;UAC9D;UACAyI,wBAAwB,GAAG,IAAI,CAAC5F,MAAM,CAACxD,OAAO,CAACW,IAAI,KAAK,CAAC,CAAC,sCACtDwI,QAAQ,CAACxI,IAAI,KAAK,CAAC,CAAC,yCACpBwI,QAAQ,CAACnJ,OAAO,CAACW,IAAI,KAAK,CAAC,CAAC,oCAC5B,IAAI,CAAC6C,MAAM,CAACtB,QAAQ,KAAKiH,QAAQ,CAACjH,QAAQ;QAClD;QACA;QACA,IAAI,CAACkH,wBAAwB,EAAE;UAC3B,IAAI,CAAC/E,QAAQ,CAAC8E,QAAQ,CAAC;QAC3B,CAAC,MACI;UACD;UACAE,UAAU,CAAC,MAAM;YACb,IAAI,CAAChF,QAAQ,CAAC8E,QAAQ,CAAC;UAC3B,CAAC,EAAE,GAAG,CAAC;QACX;MACJ,CAAC,EAAEhJ,SAAS,CAAC;MACb,IAAI,CAACoD,iBAAiB,CAACkB,KAAK,CAACzE,OAAO,CAAC;QAAEW,IAAI,EAAE,CAAC,CAAC;QAAkCC,aAAa,EAAE/B,uBAAuB,CAACgC;MAAQ,CAAC,CAAC;IACtI,CAAC,MACI;MACD,IAAI,CAACgD,qBAAqB,CAACyF,KAAK,CAAC,CAAC;IACtC;EACJ;EACAtJ,OAAOA,CAACA,OAAO,EAAE;IACb,IAAI,CAACuD,iBAAiB,CAACkB,KAAK,EAAEzE,OAAO,CAACA,OAAO,CAAC;EAClD;EACAqE,QAAQA,CAAC8E,QAAQ,EAAEI,UAAU,EAAE;IAC3B,IAAIJ,QAAQ,KAAK,IAAI,CAAC3F,MAAM,EAAE;MAC1B;IACJ;IACA;IACA,IAAI,IAAI,CAACA,MAAM,CAAC7C,IAAI,KAAK,CAAC,CAAC,uCAAuC;MAC9D,IAAI,CAAC6C,MAAM,CAACjB,MAAM,CAAC,CAAC;IACxB;IACA,IAAI,CAACiB,MAAM,GAAG2F,QAAQ;IACtB,IAAI,CAACI,UAAU,IAAI,CAAC,IAAI,CAAC3F,SAAS,EAAE;MAChC,IAAI,CAACH,iBAAiB,CAAC+F,IAAI,CAACL,QAAQ,CAAC;IACzC;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}