{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as dom from '../../dom.js';\nimport { EventType, Gesture } from '../../touch.js';\nimport * as arrays from '../../../common/arrays.js';\nimport { Emitter } from '../../../common/event.js';\nimport { Disposable } from '../../../common/lifecycle.js';\nimport { isMacintosh } from '../../../common/platform.js';\nexport class SelectBoxNative extends Disposable {\n  constructor(options, selected, styles, selectBoxOptions) {\n    super();\n    this.selected = 0;\n    this.selectBoxOptions = selectBoxOptions || Object.create(null);\n    this.options = [];\n    this.selectElement = document.createElement('select');\n    this.selectElement.className = 'monaco-select-box';\n    if (typeof this.selectBoxOptions.ariaLabel === 'string') {\n      this.selectElement.setAttribute('aria-label', this.selectBoxOptions.ariaLabel);\n    }\n    if (typeof this.selectBoxOptions.ariaDescription === 'string') {\n      this.selectElement.setAttribute('aria-description', this.selectBoxOptions.ariaDescription);\n    }\n    this._onDidSelect = this._register(new Emitter());\n    this.styles = styles;\n    this.registerListeners();\n    this.setOptions(options, selected);\n  }\n  registerListeners() {\n    this._register(Gesture.addTarget(this.selectElement));\n    [EventType.Tap].forEach(eventType => {\n      this._register(dom.addDisposableListener(this.selectElement, eventType, e => {\n        this.selectElement.focus();\n      }));\n    });\n    this._register(dom.addStandardDisposableListener(this.selectElement, 'click', e => {\n      dom.EventHelper.stop(e, true);\n    }));\n    this._register(dom.addStandardDisposableListener(this.selectElement, 'change', e => {\n      this.selectElement.title = e.target.value;\n      this._onDidSelect.fire({\n        index: e.target.selectedIndex,\n        selected: e.target.value\n      });\n    }));\n    this._register(dom.addStandardDisposableListener(this.selectElement, 'keydown', e => {\n      let showSelect = false;\n      if (isMacintosh) {\n        if (e.keyCode === 18 /* KeyCode.DownArrow */ || e.keyCode === 16 /* KeyCode.UpArrow */ || e.keyCode === 10 /* KeyCode.Space */) {\n          showSelect = true;\n        }\n      } else {\n        if (e.keyCode === 18 /* KeyCode.DownArrow */ && e.altKey || e.keyCode === 10 /* KeyCode.Space */ || e.keyCode === 3 /* KeyCode.Enter */) {\n          showSelect = true;\n        }\n      }\n      if (showSelect) {\n        // Space, Enter, is used to expand select box, do not propagate it (prevent action bar action run)\n        e.stopPropagation();\n      }\n    }));\n  }\n  get onDidSelect() {\n    return this._onDidSelect.event;\n  }\n  setOptions(options, selected) {\n    if (!this.options || !arrays.equals(this.options, options)) {\n      this.options = options;\n      this.selectElement.options.length = 0;\n      this.options.forEach((option, index) => {\n        this.selectElement.add(this.createOption(option.text, index, option.isDisabled));\n      });\n    }\n    if (selected !== undefined) {\n      this.select(selected);\n    }\n  }\n  select(index) {\n    if (this.options.length === 0) {\n      this.selected = 0;\n    } else if (index >= 0 && index < this.options.length) {\n      this.selected = index;\n    } else if (index > this.options.length - 1) {\n      // Adjust index to end of list\n      // This could make client out of sync with the select\n      this.select(this.options.length - 1);\n    } else if (this.selected < 0) {\n      this.selected = 0;\n    }\n    this.selectElement.selectedIndex = this.selected;\n    if (this.selected < this.options.length && typeof this.options[this.selected].text === 'string') {\n      this.selectElement.title = this.options[this.selected].text;\n    } else {\n      this.selectElement.title = '';\n    }\n  }\n  focus() {\n    if (this.selectElement) {\n      this.selectElement.tabIndex = 0;\n      this.selectElement.focus();\n    }\n  }\n  blur() {\n    if (this.selectElement) {\n      this.selectElement.tabIndex = -1;\n      this.selectElement.blur();\n    }\n  }\n  setFocusable(focusable) {\n    this.selectElement.tabIndex = focusable ? 0 : -1;\n  }\n  render(container) {\n    container.classList.add('select-container');\n    container.appendChild(this.selectElement);\n    this.setOptions(this.options, this.selected);\n    this.applyStyles();\n  }\n  applyStyles() {\n    // Style native select\n    if (this.selectElement) {\n      this.selectElement.style.backgroundColor = this.styles.selectBackground ?? '';\n      this.selectElement.style.color = this.styles.selectForeground ?? '';\n      this.selectElement.style.borderColor = this.styles.selectBorder ?? '';\n    }\n  }\n  createOption(value, index, disabled) {\n    const option = document.createElement('option');\n    option.value = value;\n    option.text = value;\n    option.disabled = !!disabled;\n    return option;\n  }\n}", "map": {"version": 3, "names": ["dom", "EventType", "Gesture", "arrays", "Emitter", "Disposable", "isMacintosh", "SelectBoxNative", "constructor", "options", "selected", "styles", "selectBoxOptions", "Object", "create", "selectElement", "document", "createElement", "className", "aria<PERSON><PERSON><PERSON>", "setAttribute", "ariaDescription", "_onDidSelect", "_register", "registerListeners", "setOptions", "addTarget", "Tap", "for<PERSON>ach", "eventType", "addDisposableListener", "e", "focus", "addStandardDisposableListener", "EventHelper", "stop", "title", "target", "value", "fire", "index", "selectedIndex", "showSelect", "keyCode", "altKey", "stopPropagation", "onDidSelect", "event", "equals", "length", "option", "add", "createOption", "text", "isDisabled", "undefined", "select", "tabIndex", "blur", "setFocusable", "focusable", "render", "container", "classList", "append<PERSON><PERSON><PERSON>", "applyStyles", "style", "backgroundColor", "selectBackground", "color", "selectForeground", "borderColor", "selectBorder", "disabled"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/browser/ui/selectBox/selectBoxNative.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as dom from '../../dom.js';\nimport { EventType, Gesture } from '../../touch.js';\nimport * as arrays from '../../../common/arrays.js';\nimport { Emitter } from '../../../common/event.js';\nimport { Disposable } from '../../../common/lifecycle.js';\nimport { isMacintosh } from '../../../common/platform.js';\nexport class SelectBoxNative extends Disposable {\n    constructor(options, selected, styles, selectBoxOptions) {\n        super();\n        this.selected = 0;\n        this.selectBoxOptions = selectBoxOptions || Object.create(null);\n        this.options = [];\n        this.selectElement = document.createElement('select');\n        this.selectElement.className = 'monaco-select-box';\n        if (typeof this.selectBoxOptions.ariaLabel === 'string') {\n            this.selectElement.setAttribute('aria-label', this.selectBoxOptions.ariaLabel);\n        }\n        if (typeof this.selectBoxOptions.ariaDescription === 'string') {\n            this.selectElement.setAttribute('aria-description', this.selectBoxOptions.ariaDescription);\n        }\n        this._onDidSelect = this._register(new Emitter());\n        this.styles = styles;\n        this.registerListeners();\n        this.setOptions(options, selected);\n    }\n    registerListeners() {\n        this._register(Gesture.addTarget(this.selectElement));\n        [EventType.Tap].forEach(eventType => {\n            this._register(dom.addDisposableListener(this.selectElement, eventType, (e) => {\n                this.selectElement.focus();\n            }));\n        });\n        this._register(dom.addStandardDisposableListener(this.selectElement, 'click', (e) => {\n            dom.EventHelper.stop(e, true);\n        }));\n        this._register(dom.addStandardDisposableListener(this.selectElement, 'change', (e) => {\n            this.selectElement.title = e.target.value;\n            this._onDidSelect.fire({\n                index: e.target.selectedIndex,\n                selected: e.target.value\n            });\n        }));\n        this._register(dom.addStandardDisposableListener(this.selectElement, 'keydown', (e) => {\n            let showSelect = false;\n            if (isMacintosh) {\n                if (e.keyCode === 18 /* KeyCode.DownArrow */ || e.keyCode === 16 /* KeyCode.UpArrow */ || e.keyCode === 10 /* KeyCode.Space */) {\n                    showSelect = true;\n                }\n            }\n            else {\n                if (e.keyCode === 18 /* KeyCode.DownArrow */ && e.altKey || e.keyCode === 10 /* KeyCode.Space */ || e.keyCode === 3 /* KeyCode.Enter */) {\n                    showSelect = true;\n                }\n            }\n            if (showSelect) {\n                // Space, Enter, is used to expand select box, do not propagate it (prevent action bar action run)\n                e.stopPropagation();\n            }\n        }));\n    }\n    get onDidSelect() {\n        return this._onDidSelect.event;\n    }\n    setOptions(options, selected) {\n        if (!this.options || !arrays.equals(this.options, options)) {\n            this.options = options;\n            this.selectElement.options.length = 0;\n            this.options.forEach((option, index) => {\n                this.selectElement.add(this.createOption(option.text, index, option.isDisabled));\n            });\n        }\n        if (selected !== undefined) {\n            this.select(selected);\n        }\n    }\n    select(index) {\n        if (this.options.length === 0) {\n            this.selected = 0;\n        }\n        else if (index >= 0 && index < this.options.length) {\n            this.selected = index;\n        }\n        else if (index > this.options.length - 1) {\n            // Adjust index to end of list\n            // This could make client out of sync with the select\n            this.select(this.options.length - 1);\n        }\n        else if (this.selected < 0) {\n            this.selected = 0;\n        }\n        this.selectElement.selectedIndex = this.selected;\n        if ((this.selected < this.options.length) && typeof this.options[this.selected].text === 'string') {\n            this.selectElement.title = this.options[this.selected].text;\n        }\n        else {\n            this.selectElement.title = '';\n        }\n    }\n    focus() {\n        if (this.selectElement) {\n            this.selectElement.tabIndex = 0;\n            this.selectElement.focus();\n        }\n    }\n    blur() {\n        if (this.selectElement) {\n            this.selectElement.tabIndex = -1;\n            this.selectElement.blur();\n        }\n    }\n    setFocusable(focusable) {\n        this.selectElement.tabIndex = focusable ? 0 : -1;\n    }\n    render(container) {\n        container.classList.add('select-container');\n        container.appendChild(this.selectElement);\n        this.setOptions(this.options, this.selected);\n        this.applyStyles();\n    }\n    applyStyles() {\n        // Style native select\n        if (this.selectElement) {\n            this.selectElement.style.backgroundColor = this.styles.selectBackground ?? '';\n            this.selectElement.style.color = this.styles.selectForeground ?? '';\n            this.selectElement.style.borderColor = this.styles.selectBorder ?? '';\n        }\n    }\n    createOption(value, index, disabled) {\n        const option = document.createElement('option');\n        option.value = value;\n        option.text = value;\n        option.disabled = !!disabled;\n        return option;\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,KAAKA,GAAG,MAAM,cAAc;AACnC,SAASC,SAAS,EAAEC,OAAO,QAAQ,gBAAgB;AACnD,OAAO,KAAKC,MAAM,MAAM,2BAA2B;AACnD,SAASC,OAAO,QAAQ,0BAA0B;AAClD,SAASC,UAAU,QAAQ,8BAA8B;AACzD,SAASC,WAAW,QAAQ,6BAA6B;AACzD,OAAO,MAAMC,eAAe,SAASF,UAAU,CAAC;EAC5CG,WAAWA,CAACC,OAAO,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,gBAAgB,EAAE;IACrD,KAAK,CAAC,CAAC;IACP,IAAI,CAACF,QAAQ,GAAG,CAAC;IACjB,IAAI,CAACE,gBAAgB,GAAGA,gBAAgB,IAAIC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAC/D,IAAI,CAACL,OAAO,GAAG,EAAE;IACjB,IAAI,CAACM,aAAa,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IACrD,IAAI,CAACF,aAAa,CAACG,SAAS,GAAG,mBAAmB;IAClD,IAAI,OAAO,IAAI,CAACN,gBAAgB,CAACO,SAAS,KAAK,QAAQ,EAAE;MACrD,IAAI,CAACJ,aAAa,CAACK,YAAY,CAAC,YAAY,EAAE,IAAI,CAACR,gBAAgB,CAACO,SAAS,CAAC;IAClF;IACA,IAAI,OAAO,IAAI,CAACP,gBAAgB,CAACS,eAAe,KAAK,QAAQ,EAAE;MAC3D,IAAI,CAACN,aAAa,CAACK,YAAY,CAAC,kBAAkB,EAAE,IAAI,CAACR,gBAAgB,CAACS,eAAe,CAAC;IAC9F;IACA,IAAI,CAACC,YAAY,GAAG,IAAI,CAACC,SAAS,CAAC,IAAInB,OAAO,CAAC,CAAC,CAAC;IACjD,IAAI,CAACO,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACa,iBAAiB,CAAC,CAAC;IACxB,IAAI,CAACC,UAAU,CAAChB,OAAO,EAAEC,QAAQ,CAAC;EACtC;EACAc,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACD,SAAS,CAACrB,OAAO,CAACwB,SAAS,CAAC,IAAI,CAACX,aAAa,CAAC,CAAC;IACrD,CAACd,SAAS,CAAC0B,GAAG,CAAC,CAACC,OAAO,CAACC,SAAS,IAAI;MACjC,IAAI,CAACN,SAAS,CAACvB,GAAG,CAAC8B,qBAAqB,CAAC,IAAI,CAACf,aAAa,EAAEc,SAAS,EAAGE,CAAC,IAAK;QAC3E,IAAI,CAAChB,aAAa,CAACiB,KAAK,CAAC,CAAC;MAC9B,CAAC,CAAC,CAAC;IACP,CAAC,CAAC;IACF,IAAI,CAACT,SAAS,CAACvB,GAAG,CAACiC,6BAA6B,CAAC,IAAI,CAAClB,aAAa,EAAE,OAAO,EAAGgB,CAAC,IAAK;MACjF/B,GAAG,CAACkC,WAAW,CAACC,IAAI,CAACJ,CAAC,EAAE,IAAI,CAAC;IACjC,CAAC,CAAC,CAAC;IACH,IAAI,CAACR,SAAS,CAACvB,GAAG,CAACiC,6BAA6B,CAAC,IAAI,CAAClB,aAAa,EAAE,QAAQ,EAAGgB,CAAC,IAAK;MAClF,IAAI,CAAChB,aAAa,CAACqB,KAAK,GAAGL,CAAC,CAACM,MAAM,CAACC,KAAK;MACzC,IAAI,CAAChB,YAAY,CAACiB,IAAI,CAAC;QACnBC,KAAK,EAAET,CAAC,CAACM,MAAM,CAACI,aAAa;QAC7B/B,QAAQ,EAAEqB,CAAC,CAACM,MAAM,CAACC;MACvB,CAAC,CAAC;IACN,CAAC,CAAC,CAAC;IACH,IAAI,CAACf,SAAS,CAACvB,GAAG,CAACiC,6BAA6B,CAAC,IAAI,CAAClB,aAAa,EAAE,SAAS,EAAGgB,CAAC,IAAK;MACnF,IAAIW,UAAU,GAAG,KAAK;MACtB,IAAIpC,WAAW,EAAE;QACb,IAAIyB,CAAC,CAACY,OAAO,KAAK,EAAE,CAAC,2BAA2BZ,CAAC,CAACY,OAAO,KAAK,EAAE,CAAC,yBAAyBZ,CAAC,CAACY,OAAO,KAAK,EAAE,CAAC,qBAAqB;UAC5HD,UAAU,GAAG,IAAI;QACrB;MACJ,CAAC,MACI;QACD,IAAIX,CAAC,CAACY,OAAO,KAAK,EAAE,CAAC,2BAA2BZ,CAAC,CAACa,MAAM,IAAIb,CAAC,CAACY,OAAO,KAAK,EAAE,CAAC,uBAAuBZ,CAAC,CAACY,OAAO,KAAK,CAAC,CAAC,qBAAqB;UACrID,UAAU,GAAG,IAAI;QACrB;MACJ;MACA,IAAIA,UAAU,EAAE;QACZ;QACAX,CAAC,CAACc,eAAe,CAAC,CAAC;MACvB;IACJ,CAAC,CAAC,CAAC;EACP;EACA,IAAIC,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACxB,YAAY,CAACyB,KAAK;EAClC;EACAtB,UAAUA,CAAChB,OAAO,EAAEC,QAAQ,EAAE;IAC1B,IAAI,CAAC,IAAI,CAACD,OAAO,IAAI,CAACN,MAAM,CAAC6C,MAAM,CAAC,IAAI,CAACvC,OAAO,EAAEA,OAAO,CAAC,EAAE;MACxD,IAAI,CAACA,OAAO,GAAGA,OAAO;MACtB,IAAI,CAACM,aAAa,CAACN,OAAO,CAACwC,MAAM,GAAG,CAAC;MACrC,IAAI,CAACxC,OAAO,CAACmB,OAAO,CAAC,CAACsB,MAAM,EAAEV,KAAK,KAAK;QACpC,IAAI,CAACzB,aAAa,CAACoC,GAAG,CAAC,IAAI,CAACC,YAAY,CAACF,MAAM,CAACG,IAAI,EAAEb,KAAK,EAAEU,MAAM,CAACI,UAAU,CAAC,CAAC;MACpF,CAAC,CAAC;IACN;IACA,IAAI5C,QAAQ,KAAK6C,SAAS,EAAE;MACxB,IAAI,CAACC,MAAM,CAAC9C,QAAQ,CAAC;IACzB;EACJ;EACA8C,MAAMA,CAAChB,KAAK,EAAE;IACV,IAAI,IAAI,CAAC/B,OAAO,CAACwC,MAAM,KAAK,CAAC,EAAE;MAC3B,IAAI,CAACvC,QAAQ,GAAG,CAAC;IACrB,CAAC,MACI,IAAI8B,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAG,IAAI,CAAC/B,OAAO,CAACwC,MAAM,EAAE;MAChD,IAAI,CAACvC,QAAQ,GAAG8B,KAAK;IACzB,CAAC,MACI,IAAIA,KAAK,GAAG,IAAI,CAAC/B,OAAO,CAACwC,MAAM,GAAG,CAAC,EAAE;MACtC;MACA;MACA,IAAI,CAACO,MAAM,CAAC,IAAI,CAAC/C,OAAO,CAACwC,MAAM,GAAG,CAAC,CAAC;IACxC,CAAC,MACI,IAAI,IAAI,CAACvC,QAAQ,GAAG,CAAC,EAAE;MACxB,IAAI,CAACA,QAAQ,GAAG,CAAC;IACrB;IACA,IAAI,CAACK,aAAa,CAAC0B,aAAa,GAAG,IAAI,CAAC/B,QAAQ;IAChD,IAAK,IAAI,CAACA,QAAQ,GAAG,IAAI,CAACD,OAAO,CAACwC,MAAM,IAAK,OAAO,IAAI,CAACxC,OAAO,CAAC,IAAI,CAACC,QAAQ,CAAC,CAAC2C,IAAI,KAAK,QAAQ,EAAE;MAC/F,IAAI,CAACtC,aAAa,CAACqB,KAAK,GAAG,IAAI,CAAC3B,OAAO,CAAC,IAAI,CAACC,QAAQ,CAAC,CAAC2C,IAAI;IAC/D,CAAC,MACI;MACD,IAAI,CAACtC,aAAa,CAACqB,KAAK,GAAG,EAAE;IACjC;EACJ;EACAJ,KAAKA,CAAA,EAAG;IACJ,IAAI,IAAI,CAACjB,aAAa,EAAE;MACpB,IAAI,CAACA,aAAa,CAAC0C,QAAQ,GAAG,CAAC;MAC/B,IAAI,CAAC1C,aAAa,CAACiB,KAAK,CAAC,CAAC;IAC9B;EACJ;EACA0B,IAAIA,CAAA,EAAG;IACH,IAAI,IAAI,CAAC3C,aAAa,EAAE;MACpB,IAAI,CAACA,aAAa,CAAC0C,QAAQ,GAAG,CAAC,CAAC;MAChC,IAAI,CAAC1C,aAAa,CAAC2C,IAAI,CAAC,CAAC;IAC7B;EACJ;EACAC,YAAYA,CAACC,SAAS,EAAE;IACpB,IAAI,CAAC7C,aAAa,CAAC0C,QAAQ,GAAGG,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC;EACpD;EACAC,MAAMA,CAACC,SAAS,EAAE;IACdA,SAAS,CAACC,SAAS,CAACZ,GAAG,CAAC,kBAAkB,CAAC;IAC3CW,SAAS,CAACE,WAAW,CAAC,IAAI,CAACjD,aAAa,CAAC;IACzC,IAAI,CAACU,UAAU,CAAC,IAAI,CAAChB,OAAO,EAAE,IAAI,CAACC,QAAQ,CAAC;IAC5C,IAAI,CAACuD,WAAW,CAAC,CAAC;EACtB;EACAA,WAAWA,CAAA,EAAG;IACV;IACA,IAAI,IAAI,CAAClD,aAAa,EAAE;MACpB,IAAI,CAACA,aAAa,CAACmD,KAAK,CAACC,eAAe,GAAG,IAAI,CAACxD,MAAM,CAACyD,gBAAgB,IAAI,EAAE;MAC7E,IAAI,CAACrD,aAAa,CAACmD,KAAK,CAACG,KAAK,GAAG,IAAI,CAAC1D,MAAM,CAAC2D,gBAAgB,IAAI,EAAE;MACnE,IAAI,CAACvD,aAAa,CAACmD,KAAK,CAACK,WAAW,GAAG,IAAI,CAAC5D,MAAM,CAAC6D,YAAY,IAAI,EAAE;IACzE;EACJ;EACApB,YAAYA,CAACd,KAAK,EAAEE,KAAK,EAAEiC,QAAQ,EAAE;IACjC,MAAMvB,MAAM,GAAGlC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IAC/CiC,MAAM,CAACZ,KAAK,GAAGA,KAAK;IACpBY,MAAM,CAACG,IAAI,GAAGf,KAAK;IACnBY,MAAM,CAACuB,QAAQ,GAAG,CAAC,CAACA,QAAQ;IAC5B,OAAOvB,MAAM;EACjB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}