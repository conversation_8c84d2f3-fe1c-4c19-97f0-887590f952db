{"ast": null, "code": "import _asyncToGenerator from \"C:/console/aava-ui-web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { asCssValueWithDefault, createStyleSheet, EventHelper, getActiveElement, getWindow, isHTMLElement, isMouseEvent } from '../../dom.js';\nimport { DomEmitter } from '../../event.js';\nimport { StandardKeyboardEvent } from '../../keyboardEvent.js';\nimport { Gesture } from '../../touch.js';\nimport { alert } from '../aria/aria.js';\nimport { CombinedSpliceable } from './splice.js';\nimport { binarySearch, firstOrDefault, range } from '../../../common/arrays.js';\nimport { timeout } from '../../../common/async.js';\nimport { Color } from '../../../common/color.js';\nimport { memoize } from '../../../common/decorators.js';\nimport { Emitter, Event, EventBufferer } from '../../../common/event.js';\nimport { matchesFuzzy2, matchesPrefix } from '../../../common/filters.js';\nimport { DisposableStore, dispose } from '../../../common/lifecycle.js';\nimport { clamp } from '../../../common/numbers.js';\nimport * as platform from '../../../common/platform.js';\nimport { isNumber } from '../../../common/types.js';\nimport './list.css';\nimport { ListError } from './list.js';\nimport { ListView } from './listView.js';\nimport { StandardMouseEvent } from '../../mouseEvent.js';\nimport { autorun, constObservable } from '../../../common/observable.js';\nclass TraitRenderer {\n  constructor(trait) {\n    this.trait = trait;\n    this.renderedElements = [];\n  }\n  get templateId() {\n    return `template:${this.trait.name}`;\n  }\n  renderTemplate(container) {\n    return container;\n  }\n  renderElement(element, index, templateData) {\n    const renderedElementIndex = this.renderedElements.findIndex(el => el.templateData === templateData);\n    if (renderedElementIndex >= 0) {\n      const rendered = this.renderedElements[renderedElementIndex];\n      this.trait.unrender(templateData);\n      rendered.index = index;\n    } else {\n      const rendered = {\n        index,\n        templateData\n      };\n      this.renderedElements.push(rendered);\n    }\n    this.trait.renderIndex(index, templateData);\n  }\n  splice(start, deleteCount, insertCount) {\n    const rendered = [];\n    for (const renderedElement of this.renderedElements) {\n      if (renderedElement.index < start) {\n        rendered.push(renderedElement);\n      } else if (renderedElement.index >= start + deleteCount) {\n        rendered.push({\n          index: renderedElement.index + insertCount - deleteCount,\n          templateData: renderedElement.templateData\n        });\n      }\n    }\n    this.renderedElements = rendered;\n  }\n  renderIndexes(indexes) {\n    for (const {\n      index,\n      templateData\n    } of this.renderedElements) {\n      if (indexes.indexOf(index) > -1) {\n        this.trait.renderIndex(index, templateData);\n      }\n    }\n  }\n  disposeTemplate(templateData) {\n    const index = this.renderedElements.findIndex(el => el.templateData === templateData);\n    if (index < 0) {\n      return;\n    }\n    this.renderedElements.splice(index, 1);\n  }\n}\nclass Trait {\n  get name() {\n    return this._trait;\n  }\n  get renderer() {\n    return new TraitRenderer(this);\n  }\n  constructor(_trait) {\n    this._trait = _trait;\n    this.indexes = [];\n    this.sortedIndexes = [];\n    this._onChange = new Emitter();\n    this.onChange = this._onChange.event;\n  }\n  splice(start, deleteCount, elements) {\n    const diff = elements.length - deleteCount;\n    const end = start + deleteCount;\n    const sortedIndexes = [];\n    let i = 0;\n    while (i < this.sortedIndexes.length && this.sortedIndexes[i] < start) {\n      sortedIndexes.push(this.sortedIndexes[i++]);\n    }\n    for (let j = 0; j < elements.length; j++) {\n      if (elements[j]) {\n        sortedIndexes.push(j + start);\n      }\n    }\n    while (i < this.sortedIndexes.length && this.sortedIndexes[i] >= end) {\n      sortedIndexes.push(this.sortedIndexes[i++] + diff);\n    }\n    this.renderer.splice(start, deleteCount, elements.length);\n    this._set(sortedIndexes, sortedIndexes);\n  }\n  renderIndex(index, container) {\n    container.classList.toggle(this._trait, this.contains(index));\n  }\n  unrender(container) {\n    container.classList.remove(this._trait);\n  }\n  /**\n   * Sets the indexes which should have this trait.\n   *\n   * @param indexes Indexes which should have this trait.\n   * @return The old indexes which had this trait.\n   */\n  set(indexes, browserEvent) {\n    return this._set(indexes, [...indexes].sort(numericSort), browserEvent);\n  }\n  _set(indexes, sortedIndexes, browserEvent) {\n    const result = this.indexes;\n    const sortedResult = this.sortedIndexes;\n    this.indexes = indexes;\n    this.sortedIndexes = sortedIndexes;\n    const toRender = disjunction(sortedResult, indexes);\n    this.renderer.renderIndexes(toRender);\n    this._onChange.fire({\n      indexes,\n      browserEvent\n    });\n    return result;\n  }\n  get() {\n    return this.indexes;\n  }\n  contains(index) {\n    return binarySearch(this.sortedIndexes, index, numericSort) >= 0;\n  }\n  dispose() {\n    dispose(this._onChange);\n  }\n}\n__decorate([memoize], Trait.prototype, \"renderer\", null);\nclass SelectionTrait extends Trait {\n  constructor(setAriaSelected) {\n    super('selected');\n    this.setAriaSelected = setAriaSelected;\n  }\n  renderIndex(index, container) {\n    super.renderIndex(index, container);\n    if (this.setAriaSelected) {\n      if (this.contains(index)) {\n        container.setAttribute('aria-selected', 'true');\n      } else {\n        container.setAttribute('aria-selected', 'false');\n      }\n    }\n  }\n}\n/**\n * The TraitSpliceable is used as a util class to be able\n * to preserve traits across splice calls, given an identity\n * provider.\n */\nclass TraitSpliceable {\n  constructor(trait, view, identityProvider) {\n    this.trait = trait;\n    this.view = view;\n    this.identityProvider = identityProvider;\n  }\n  splice(start, deleteCount, elements) {\n    if (!this.identityProvider) {\n      return this.trait.splice(start, deleteCount, new Array(elements.length).fill(false));\n    }\n    const pastElementsWithTrait = this.trait.get().map(i => this.identityProvider.getId(this.view.element(i)).toString());\n    if (pastElementsWithTrait.length === 0) {\n      return this.trait.splice(start, deleteCount, new Array(elements.length).fill(false));\n    }\n    const pastElementsWithTraitSet = new Set(pastElementsWithTrait);\n    const elementsWithTrait = elements.map(e => pastElementsWithTraitSet.has(this.identityProvider.getId(e).toString()));\n    this.trait.splice(start, deleteCount, elementsWithTrait);\n  }\n}\nexport function isInputElement(e) {\n  return e.tagName === 'INPUT' || e.tagName === 'TEXTAREA';\n}\nfunction isListElementDescendantOfClass(e, className) {\n  if (e.classList.contains(className)) {\n    return true;\n  }\n  if (e.classList.contains('monaco-list')) {\n    return false;\n  }\n  if (!e.parentElement) {\n    return false;\n  }\n  return isListElementDescendantOfClass(e.parentElement, className);\n}\nexport function isMonacoEditor(e) {\n  return isListElementDescendantOfClass(e, 'monaco-editor');\n}\nexport function isMonacoCustomToggle(e) {\n  return isListElementDescendantOfClass(e, 'monaco-custom-toggle');\n}\nexport function isActionItem(e) {\n  return isListElementDescendantOfClass(e, 'action-item');\n}\nexport function isStickyScrollElement(e) {\n  return isListElementDescendantOfClass(e, 'monaco-tree-sticky-row');\n}\nexport function isStickyScrollContainer(e) {\n  return e.classList.contains('monaco-tree-sticky-container');\n}\nexport function isButton(e) {\n  if (e.tagName === 'A' && e.classList.contains('monaco-button') || e.tagName === 'DIV' && e.classList.contains('monaco-button-dropdown')) {\n    return true;\n  }\n  if (e.classList.contains('monaco-list')) {\n    return false;\n  }\n  if (!e.parentElement) {\n    return false;\n  }\n  return isButton(e.parentElement);\n}\nclass KeyboardController {\n  get onKeyDown() {\n    return Event.chain(this.disposables.add(new DomEmitter(this.view.domNode, 'keydown')).event, $ => $.filter(e => !isInputElement(e.target)).map(e => new StandardKeyboardEvent(e)));\n  }\n  constructor(list, view, options) {\n    this.list = list;\n    this.view = view;\n    this.disposables = new DisposableStore();\n    this.multipleSelectionDisposables = new DisposableStore();\n    this.multipleSelectionSupport = options.multipleSelectionSupport;\n    this.disposables.add(this.onKeyDown(e => {\n      switch (e.keyCode) {\n        case 3 /* KeyCode.Enter */:\n          return this.onEnter(e);\n        case 16 /* KeyCode.UpArrow */:\n          return this.onUpArrow(e);\n        case 18 /* KeyCode.DownArrow */:\n          return this.onDownArrow(e);\n        case 11 /* KeyCode.PageUp */:\n          return this.onPageUpArrow(e);\n        case 12 /* KeyCode.PageDown */:\n          return this.onPageDownArrow(e);\n        case 9 /* KeyCode.Escape */:\n          return this.onEscape(e);\n        case 31 /* KeyCode.KeyA */:\n          if (this.multipleSelectionSupport && (platform.isMacintosh ? e.metaKey : e.ctrlKey)) {\n            this.onCtrlA(e);\n          }\n      }\n    }));\n  }\n  updateOptions(optionsUpdate) {\n    if (optionsUpdate.multipleSelectionSupport !== undefined) {\n      this.multipleSelectionSupport = optionsUpdate.multipleSelectionSupport;\n    }\n  }\n  onEnter(e) {\n    e.preventDefault();\n    e.stopPropagation();\n    this.list.setSelection(this.list.getFocus(), e.browserEvent);\n  }\n  onUpArrow(e) {\n    e.preventDefault();\n    e.stopPropagation();\n    this.list.focusPrevious(1, false, e.browserEvent);\n    const el = this.list.getFocus()[0];\n    this.list.setAnchor(el);\n    this.list.reveal(el);\n    this.view.domNode.focus();\n  }\n  onDownArrow(e) {\n    e.preventDefault();\n    e.stopPropagation();\n    this.list.focusNext(1, false, e.browserEvent);\n    const el = this.list.getFocus()[0];\n    this.list.setAnchor(el);\n    this.list.reveal(el);\n    this.view.domNode.focus();\n  }\n  onPageUpArrow(e) {\n    e.preventDefault();\n    e.stopPropagation();\n    this.list.focusPreviousPage(e.browserEvent);\n    const el = this.list.getFocus()[0];\n    this.list.setAnchor(el);\n    this.list.reveal(el);\n    this.view.domNode.focus();\n  }\n  onPageDownArrow(e) {\n    e.preventDefault();\n    e.stopPropagation();\n    this.list.focusNextPage(e.browserEvent);\n    const el = this.list.getFocus()[0];\n    this.list.setAnchor(el);\n    this.list.reveal(el);\n    this.view.domNode.focus();\n  }\n  onCtrlA(e) {\n    e.preventDefault();\n    e.stopPropagation();\n    this.list.setSelection(range(this.list.length), e.browserEvent);\n    this.list.setAnchor(undefined);\n    this.view.domNode.focus();\n  }\n  onEscape(e) {\n    if (this.list.getSelection().length) {\n      e.preventDefault();\n      e.stopPropagation();\n      this.list.setSelection([], e.browserEvent);\n      this.list.setAnchor(undefined);\n      this.view.domNode.focus();\n    }\n  }\n  dispose() {\n    this.disposables.dispose();\n    this.multipleSelectionDisposables.dispose();\n  }\n}\n__decorate([memoize], KeyboardController.prototype, \"onKeyDown\", null);\nexport var TypeNavigationMode = /*#__PURE__*/function (TypeNavigationMode) {\n  TypeNavigationMode[TypeNavigationMode[\"Automatic\"] = 0] = \"Automatic\";\n  TypeNavigationMode[TypeNavigationMode[\"Trigger\"] = 1] = \"Trigger\";\n  return TypeNavigationMode;\n}(TypeNavigationMode || {});\nvar TypeNavigationControllerState = /*#__PURE__*/function (TypeNavigationControllerState) {\n  TypeNavigationControllerState[TypeNavigationControllerState[\"Idle\"] = 0] = \"Idle\";\n  TypeNavigationControllerState[TypeNavigationControllerState[\"Typing\"] = 1] = \"Typing\";\n  return TypeNavigationControllerState;\n}(TypeNavigationControllerState || {});\nexport const DefaultKeyboardNavigationDelegate = new class {\n  mightProducePrintableCharacter(event) {\n    if (event.ctrlKey || event.metaKey || event.altKey) {\n      return false;\n    }\n    return event.keyCode >= 31 /* KeyCode.KeyA */ && event.keyCode <= 56 /* KeyCode.KeyZ */ || event.keyCode >= 21 /* KeyCode.Digit0 */ && event.keyCode <= 30 /* KeyCode.Digit9 */ || event.keyCode >= 98 /* KeyCode.Numpad0 */ && event.keyCode <= 107 /* KeyCode.Numpad9 */ || event.keyCode >= 85 /* KeyCode.Semicolon */ && event.keyCode <= 95 /* KeyCode.Quote */;\n  }\n}();\nclass TypeNavigationController {\n  constructor(list, view, keyboardNavigationLabelProvider, keyboardNavigationEventFilter, delegate) {\n    this.list = list;\n    this.view = view;\n    this.keyboardNavigationLabelProvider = keyboardNavigationLabelProvider;\n    this.keyboardNavigationEventFilter = keyboardNavigationEventFilter;\n    this.delegate = delegate;\n    this.enabled = false;\n    this.state = TypeNavigationControllerState.Idle;\n    this.mode = TypeNavigationMode.Automatic;\n    this.triggered = false;\n    this.previouslyFocused = -1;\n    this.enabledDisposables = new DisposableStore();\n    this.disposables = new DisposableStore();\n    this.updateOptions(list.options);\n  }\n  updateOptions(options) {\n    if (options.typeNavigationEnabled ?? true) {\n      this.enable();\n    } else {\n      this.disable();\n    }\n    this.mode = options.typeNavigationMode ?? TypeNavigationMode.Automatic;\n  }\n  enable() {\n    if (this.enabled) {\n      return;\n    }\n    let typing = false;\n    const onChar = Event.chain(this.enabledDisposables.add(new DomEmitter(this.view.domNode, 'keydown')).event, $ => $.filter(e => !isInputElement(e.target)).filter(() => this.mode === TypeNavigationMode.Automatic || this.triggered).map(event => new StandardKeyboardEvent(event)).filter(e => typing || this.keyboardNavigationEventFilter(e)).filter(e => this.delegate.mightProducePrintableCharacter(e)).forEach(e => EventHelper.stop(e, true)).map(event => event.browserEvent.key));\n    const onClear = Event.debounce(onChar, () => null, 800, undefined, undefined, undefined, this.enabledDisposables);\n    const onInput = Event.reduce(Event.any(onChar, onClear), (r, i) => i === null ? null : (r || '') + i, undefined, this.enabledDisposables);\n    onInput(this.onInput, this, this.enabledDisposables);\n    onClear(this.onClear, this, this.enabledDisposables);\n    onChar(() => typing = true, undefined, this.enabledDisposables);\n    onClear(() => typing = false, undefined, this.enabledDisposables);\n    this.enabled = true;\n    this.triggered = false;\n  }\n  disable() {\n    if (!this.enabled) {\n      return;\n    }\n    this.enabledDisposables.clear();\n    this.enabled = false;\n    this.triggered = false;\n  }\n  onClear() {\n    const focus = this.list.getFocus();\n    if (focus.length > 0 && focus[0] === this.previouslyFocused) {\n      // List: re-announce element on typing end since typed keys will interrupt aria label of focused element\n      // Do not announce if there was a focus change at the end to prevent duplication https://github.com/microsoft/vscode/issues/95961\n      const ariaLabel = this.list.options.accessibilityProvider?.getAriaLabel(this.list.element(focus[0]));\n      if (typeof ariaLabel === 'string') {\n        alert(ariaLabel);\n      } else if (ariaLabel) {\n        alert(ariaLabel.get());\n      }\n    }\n    this.previouslyFocused = -1;\n  }\n  onInput(word) {\n    if (!word) {\n      this.state = TypeNavigationControllerState.Idle;\n      this.triggered = false;\n      return;\n    }\n    const focus = this.list.getFocus();\n    const start = focus.length > 0 ? focus[0] : 0;\n    const delta = this.state === TypeNavigationControllerState.Idle ? 1 : 0;\n    this.state = TypeNavigationControllerState.Typing;\n    for (let i = 0; i < this.list.length; i++) {\n      const index = (start + i + delta) % this.list.length;\n      const label = this.keyboardNavigationLabelProvider.getKeyboardNavigationLabel(this.view.element(index));\n      const labelStr = label && label.toString();\n      if (this.list.options.typeNavigationEnabled) {\n        if (typeof labelStr !== 'undefined') {\n          // If prefix is found, focus and return early\n          if (matchesPrefix(word, labelStr)) {\n            this.previouslyFocused = start;\n            this.list.setFocus([index]);\n            this.list.reveal(index);\n            return;\n          }\n          const fuzzy = matchesFuzzy2(word, labelStr);\n          if (fuzzy) {\n            const fuzzyScore = fuzzy[0].end - fuzzy[0].start;\n            // ensures that when fuzzy matching, doesn't clash with prefix matching (1 input vs 1+ should be prefix and fuzzy respecitvely). Also makes sure that exact matches are prioritized.\n            if (fuzzyScore > 1 && fuzzy.length === 1) {\n              this.previouslyFocused = start;\n              this.list.setFocus([index]);\n              this.list.reveal(index);\n              return;\n            }\n          }\n        }\n      } else if (typeof labelStr === 'undefined' || matchesPrefix(word, labelStr)) {\n        this.previouslyFocused = start;\n        this.list.setFocus([index]);\n        this.list.reveal(index);\n        return;\n      }\n    }\n  }\n  dispose() {\n    this.disable();\n    this.enabledDisposables.dispose();\n    this.disposables.dispose();\n  }\n}\nclass DOMFocusController {\n  constructor(list, view) {\n    this.list = list;\n    this.view = view;\n    this.disposables = new DisposableStore();\n    const onKeyDown = Event.chain(this.disposables.add(new DomEmitter(view.domNode, 'keydown')).event, $ => $.filter(e => !isInputElement(e.target)).map(e => new StandardKeyboardEvent(e)));\n    const onTab = Event.chain(onKeyDown, $ => $.filter(e => e.keyCode === 2 /* KeyCode.Tab */ && !e.ctrlKey && !e.metaKey && !e.shiftKey && !e.altKey));\n    onTab(this.onTab, this, this.disposables);\n  }\n  onTab(e) {\n    if (e.target !== this.view.domNode) {\n      return;\n    }\n    const focus = this.list.getFocus();\n    if (focus.length === 0) {\n      return;\n    }\n    const focusedDomElement = this.view.domElement(focus[0]);\n    if (!focusedDomElement) {\n      return;\n    }\n    const tabIndexElement = focusedDomElement.querySelector('[tabIndex]');\n    if (!tabIndexElement || !isHTMLElement(tabIndexElement) || tabIndexElement.tabIndex === -1) {\n      return;\n    }\n    const style = getWindow(tabIndexElement).getComputedStyle(tabIndexElement);\n    if (style.visibility === 'hidden' || style.display === 'none') {\n      return;\n    }\n    e.preventDefault();\n    e.stopPropagation();\n    tabIndexElement.focus();\n  }\n  dispose() {\n    this.disposables.dispose();\n  }\n}\nexport function isSelectionSingleChangeEvent(event) {\n  return platform.isMacintosh ? event.browserEvent.metaKey : event.browserEvent.ctrlKey;\n}\nexport function isSelectionRangeChangeEvent(event) {\n  return event.browserEvent.shiftKey;\n}\nfunction isMouseRightClick(event) {\n  return isMouseEvent(event) && event.button === 2;\n}\nconst DefaultMultipleSelectionController = {\n  isSelectionSingleChangeEvent,\n  isSelectionRangeChangeEvent\n};\nexport class MouseController {\n  constructor(list) {\n    this.list = list;\n    this.disposables = new DisposableStore();\n    this._onPointer = new Emitter();\n    this.onPointer = this._onPointer.event;\n    if (list.options.multipleSelectionSupport !== false) {\n      this.multipleSelectionController = this.list.options.multipleSelectionController || DefaultMultipleSelectionController;\n    }\n    this.mouseSupport = typeof list.options.mouseSupport === 'undefined' || !!list.options.mouseSupport;\n    if (this.mouseSupport) {\n      list.onMouseDown(this.onMouseDown, this, this.disposables);\n      list.onContextMenu(this.onContextMenu, this, this.disposables);\n      list.onMouseDblClick(this.onDoubleClick, this, this.disposables);\n      list.onTouchStart(this.onMouseDown, this, this.disposables);\n      this.disposables.add(Gesture.addTarget(list.getHTMLElement()));\n    }\n    Event.any(list.onMouseClick, list.onMouseMiddleClick, list.onTap)(this.onViewPointer, this, this.disposables);\n  }\n  updateOptions(optionsUpdate) {\n    if (optionsUpdate.multipleSelectionSupport !== undefined) {\n      this.multipleSelectionController = undefined;\n      if (optionsUpdate.multipleSelectionSupport) {\n        this.multipleSelectionController = this.list.options.multipleSelectionController || DefaultMultipleSelectionController;\n      }\n    }\n  }\n  isSelectionSingleChangeEvent(event) {\n    if (!this.multipleSelectionController) {\n      return false;\n    }\n    return this.multipleSelectionController.isSelectionSingleChangeEvent(event);\n  }\n  isSelectionRangeChangeEvent(event) {\n    if (!this.multipleSelectionController) {\n      return false;\n    }\n    return this.multipleSelectionController.isSelectionRangeChangeEvent(event);\n  }\n  isSelectionChangeEvent(event) {\n    return this.isSelectionSingleChangeEvent(event) || this.isSelectionRangeChangeEvent(event);\n  }\n  onMouseDown(e) {\n    if (isMonacoEditor(e.browserEvent.target)) {\n      return;\n    }\n    if (getActiveElement() !== e.browserEvent.target) {\n      this.list.domFocus();\n    }\n  }\n  onContextMenu(e) {\n    if (isInputElement(e.browserEvent.target) || isMonacoEditor(e.browserEvent.target)) {\n      return;\n    }\n    const focus = typeof e.index === 'undefined' ? [] : [e.index];\n    this.list.setFocus(focus, e.browserEvent);\n  }\n  onViewPointer(e) {\n    if (!this.mouseSupport) {\n      return;\n    }\n    if (isInputElement(e.browserEvent.target) || isMonacoEditor(e.browserEvent.target)) {\n      return;\n    }\n    if (e.browserEvent.isHandledByList) {\n      return;\n    }\n    e.browserEvent.isHandledByList = true;\n    const focus = e.index;\n    if (typeof focus === 'undefined') {\n      this.list.setFocus([], e.browserEvent);\n      this.list.setSelection([], e.browserEvent);\n      this.list.setAnchor(undefined);\n      return;\n    }\n    if (this.isSelectionChangeEvent(e)) {\n      return this.changeSelection(e);\n    }\n    this.list.setFocus([focus], e.browserEvent);\n    this.list.setAnchor(focus);\n    if (!isMouseRightClick(e.browserEvent)) {\n      this.list.setSelection([focus], e.browserEvent);\n    }\n    this._onPointer.fire(e);\n  }\n  onDoubleClick(e) {\n    if (isInputElement(e.browserEvent.target) || isMonacoEditor(e.browserEvent.target)) {\n      return;\n    }\n    if (this.isSelectionChangeEvent(e)) {\n      return;\n    }\n    if (e.browserEvent.isHandledByList) {\n      return;\n    }\n    e.browserEvent.isHandledByList = true;\n    const focus = this.list.getFocus();\n    this.list.setSelection(focus, e.browserEvent);\n  }\n  changeSelection(e) {\n    const focus = e.index;\n    let anchor = this.list.getAnchor();\n    if (this.isSelectionRangeChangeEvent(e)) {\n      if (typeof anchor === 'undefined') {\n        const currentFocus = this.list.getFocus()[0];\n        anchor = currentFocus ?? focus;\n        this.list.setAnchor(anchor);\n      }\n      const min = Math.min(anchor, focus);\n      const max = Math.max(anchor, focus);\n      const rangeSelection = range(min, max + 1);\n      const selection = this.list.getSelection();\n      const contiguousRange = getContiguousRangeContaining(disjunction(selection, [anchor]), anchor);\n      if (contiguousRange.length === 0) {\n        return;\n      }\n      const newSelection = disjunction(rangeSelection, relativeComplement(selection, contiguousRange));\n      this.list.setSelection(newSelection, e.browserEvent);\n      this.list.setFocus([focus], e.browserEvent);\n    } else if (this.isSelectionSingleChangeEvent(e)) {\n      const selection = this.list.getSelection();\n      const newSelection = selection.filter(i => i !== focus);\n      this.list.setFocus([focus]);\n      this.list.setAnchor(focus);\n      if (selection.length === newSelection.length) {\n        this.list.setSelection([...newSelection, focus], e.browserEvent);\n      } else {\n        this.list.setSelection(newSelection, e.browserEvent);\n      }\n    }\n  }\n  dispose() {\n    this.disposables.dispose();\n  }\n}\nexport class DefaultStyleController {\n  constructor(styleElement, selectorSuffix) {\n    this.styleElement = styleElement;\n    this.selectorSuffix = selectorSuffix;\n  }\n  style(styles) {\n    const suffix = this.selectorSuffix && `.${this.selectorSuffix}`;\n    const content = [];\n    if (styles.listBackground) {\n      content.push(`.monaco-list${suffix} .monaco-list-rows { background: ${styles.listBackground}; }`);\n    }\n    if (styles.listFocusBackground) {\n      content.push(`.monaco-list${suffix}:focus .monaco-list-row.focused { background-color: ${styles.listFocusBackground}; }`);\n      content.push(`.monaco-list${suffix}:focus .monaco-list-row.focused:hover { background-color: ${styles.listFocusBackground}; }`); // overwrite :hover style in this case!\n    }\n    if (styles.listFocusForeground) {\n      content.push(`.monaco-list${suffix}:focus .monaco-list-row.focused { color: ${styles.listFocusForeground}; }`);\n    }\n    if (styles.listActiveSelectionBackground) {\n      content.push(`.monaco-list${suffix}:focus .monaco-list-row.selected { background-color: ${styles.listActiveSelectionBackground}; }`);\n      content.push(`.monaco-list${suffix}:focus .monaco-list-row.selected:hover { background-color: ${styles.listActiveSelectionBackground}; }`); // overwrite :hover style in this case!\n    }\n    if (styles.listActiveSelectionForeground) {\n      content.push(`.monaco-list${suffix}:focus .monaco-list-row.selected { color: ${styles.listActiveSelectionForeground}; }`);\n    }\n    if (styles.listActiveSelectionIconForeground) {\n      content.push(`.monaco-list${suffix}:focus .monaco-list-row.selected .codicon { color: ${styles.listActiveSelectionIconForeground}; }`);\n    }\n    if (styles.listFocusAndSelectionBackground) {\n      content.push(`\n\t\t\t\t.monaco-drag-image,\n\t\t\t\t.monaco-list${suffix}:focus .monaco-list-row.selected.focused { background-color: ${styles.listFocusAndSelectionBackground}; }\n\t\t\t`);\n    }\n    if (styles.listFocusAndSelectionForeground) {\n      content.push(`\n\t\t\t\t.monaco-drag-image,\n\t\t\t\t.monaco-list${suffix}:focus .monaco-list-row.selected.focused { color: ${styles.listFocusAndSelectionForeground}; }\n\t\t\t`);\n    }\n    if (styles.listInactiveFocusForeground) {\n      content.push(`.monaco-list${suffix} .monaco-list-row.focused { color:  ${styles.listInactiveFocusForeground}; }`);\n      content.push(`.monaco-list${suffix} .monaco-list-row.focused:hover { color:  ${styles.listInactiveFocusForeground}; }`); // overwrite :hover style in this case!\n    }\n    if (styles.listInactiveSelectionIconForeground) {\n      content.push(`.monaco-list${suffix} .monaco-list-row.focused .codicon { color:  ${styles.listInactiveSelectionIconForeground}; }`);\n    }\n    if (styles.listInactiveFocusBackground) {\n      content.push(`.monaco-list${suffix} .monaco-list-row.focused { background-color:  ${styles.listInactiveFocusBackground}; }`);\n      content.push(`.monaco-list${suffix} .monaco-list-row.focused:hover { background-color:  ${styles.listInactiveFocusBackground}; }`); // overwrite :hover style in this case!\n    }\n    if (styles.listInactiveSelectionBackground) {\n      content.push(`.monaco-list${suffix} .monaco-list-row.selected { background-color:  ${styles.listInactiveSelectionBackground}; }`);\n      content.push(`.monaco-list${suffix} .monaco-list-row.selected:hover { background-color:  ${styles.listInactiveSelectionBackground}; }`); // overwrite :hover style in this case!\n    }\n    if (styles.listInactiveSelectionForeground) {\n      content.push(`.monaco-list${suffix} .monaco-list-row.selected { color: ${styles.listInactiveSelectionForeground}; }`);\n    }\n    if (styles.listHoverBackground) {\n      content.push(`.monaco-list${suffix}:not(.drop-target):not(.dragging) .monaco-list-row:hover:not(.selected):not(.focused) { background-color: ${styles.listHoverBackground}; }`);\n    }\n    if (styles.listHoverForeground) {\n      content.push(`.monaco-list${suffix}:not(.drop-target):not(.dragging) .monaco-list-row:hover:not(.selected):not(.focused) { color:  ${styles.listHoverForeground}; }`);\n    }\n    /**\n     * Outlines\n     */\n    const focusAndSelectionOutline = asCssValueWithDefault(styles.listFocusAndSelectionOutline, asCssValueWithDefault(styles.listSelectionOutline, styles.listFocusOutline ?? ''));\n    if (focusAndSelectionOutline) {\n      // default: listFocusOutline\n      content.push(`.monaco-list${suffix}:focus .monaco-list-row.focused.selected { outline: 1px solid ${focusAndSelectionOutline}; outline-offset: -1px;}`);\n    }\n    if (styles.listFocusOutline) {\n      // default: set\n      content.push(`\n\t\t\t\t.monaco-drag-image,\n\t\t\t\t.monaco-list${suffix}:focus .monaco-list-row.focused { outline: 1px solid ${styles.listFocusOutline}; outline-offset: -1px; }\n\t\t\t\t.monaco-workbench.context-menu-visible .monaco-list${suffix}.last-focused .monaco-list-row.focused { outline: 1px solid ${styles.listFocusOutline}; outline-offset: -1px; }\n\t\t\t`);\n    }\n    const inactiveFocusAndSelectionOutline = asCssValueWithDefault(styles.listSelectionOutline, styles.listInactiveFocusOutline ?? '');\n    if (inactiveFocusAndSelectionOutline) {\n      content.push(`.monaco-list${suffix} .monaco-list-row.focused.selected { outline: 1px dotted ${inactiveFocusAndSelectionOutline}; outline-offset: -1px; }`);\n    }\n    if (styles.listSelectionOutline) {\n      // default: activeContrastBorder\n      content.push(`.monaco-list${suffix} .monaco-list-row.selected { outline: 1px dotted ${styles.listSelectionOutline}; outline-offset: -1px; }`);\n    }\n    if (styles.listInactiveFocusOutline) {\n      // default: null\n      content.push(`.monaco-list${suffix} .monaco-list-row.focused { outline: 1px dotted ${styles.listInactiveFocusOutline}; outline-offset: -1px; }`);\n    }\n    if (styles.listHoverOutline) {\n      // default: activeContrastBorder\n      content.push(`.monaco-list${suffix} .monaco-list-row:hover { outline: 1px dashed ${styles.listHoverOutline}; outline-offset: -1px; }`);\n    }\n    if (styles.listDropOverBackground) {\n      content.push(`\n\t\t\t\t.monaco-list${suffix}.drop-target,\n\t\t\t\t.monaco-list${suffix} .monaco-list-rows.drop-target,\n\t\t\t\t.monaco-list${suffix} .monaco-list-row.drop-target { background-color: ${styles.listDropOverBackground} !important; color: inherit !important; }\n\t\t\t`);\n    }\n    if (styles.listDropBetweenBackground) {\n      content.push(`\n\t\t\t.monaco-list${suffix} .monaco-list-rows.drop-target-before .monaco-list-row:first-child::before,\n\t\t\t.monaco-list${suffix} .monaco-list-row.drop-target-before::before {\n\t\t\t\tcontent: \"\"; position: absolute; top: 0px; left: 0px; width: 100%; height: 1px;\n\t\t\t\tbackground-color: ${styles.listDropBetweenBackground};\n\t\t\t}`);\n      content.push(`\n\t\t\t.monaco-list${suffix} .monaco-list-rows.drop-target-after .monaco-list-row:last-child::after,\n\t\t\t.monaco-list${suffix} .monaco-list-row.drop-target-after::after {\n\t\t\t\tcontent: \"\"; position: absolute; bottom: 0px; left: 0px; width: 100%; height: 1px;\n\t\t\t\tbackground-color: ${styles.listDropBetweenBackground};\n\t\t\t}`);\n    }\n    if (styles.tableColumnsBorder) {\n      content.push(`\n\t\t\t\t.monaco-table > .monaco-split-view2,\n\t\t\t\t.monaco-table > .monaco-split-view2 .monaco-sash.vertical::before,\n\t\t\t\t.monaco-workbench:not(.reduce-motion) .monaco-table:hover > .monaco-split-view2,\n\t\t\t\t.monaco-workbench:not(.reduce-motion) .monaco-table:hover > .monaco-split-view2 .monaco-sash.vertical::before {\n\t\t\t\t\tborder-color: ${styles.tableColumnsBorder};\n\t\t\t\t}\n\n\t\t\t\t.monaco-workbench:not(.reduce-motion) .monaco-table > .monaco-split-view2,\n\t\t\t\t.monaco-workbench:not(.reduce-motion) .monaco-table > .monaco-split-view2 .monaco-sash.vertical::before {\n\t\t\t\t\tborder-color: transparent;\n\t\t\t\t}\n\t\t\t`);\n    }\n    if (styles.tableOddRowsBackgroundColor) {\n      content.push(`\n\t\t\t\t.monaco-table .monaco-list-row[data-parity=odd]:not(.focused):not(.selected):not(:hover) .monaco-table-tr,\n\t\t\t\t.monaco-table .monaco-list:not(:focus) .monaco-list-row[data-parity=odd].focused:not(.selected):not(:hover) .monaco-table-tr,\n\t\t\t\t.monaco-table .monaco-list:not(.focused) .monaco-list-row[data-parity=odd].focused:not(.selected):not(:hover) .monaco-table-tr {\n\t\t\t\t\tbackground-color: ${styles.tableOddRowsBackgroundColor};\n\t\t\t\t}\n\t\t\t`);\n    }\n    this.styleElement.textContent = content.join('\\n');\n  }\n}\nexport const unthemedListStyles = {\n  listFocusBackground: '#7FB0D0',\n  listActiveSelectionBackground: '#0E639C',\n  listActiveSelectionForeground: '#FFFFFF',\n  listActiveSelectionIconForeground: '#FFFFFF',\n  listFocusAndSelectionOutline: '#90C2F9',\n  listFocusAndSelectionBackground: '#094771',\n  listFocusAndSelectionForeground: '#FFFFFF',\n  listInactiveSelectionBackground: '#3F3F46',\n  listInactiveSelectionIconForeground: '#FFFFFF',\n  listHoverBackground: '#2A2D2E',\n  listDropOverBackground: '#383B3D',\n  listDropBetweenBackground: '#EEEEEE',\n  treeIndentGuidesStroke: '#a9a9a9',\n  treeInactiveIndentGuidesStroke: Color.fromHex('#a9a9a9').transparent(0.4).toString(),\n  tableColumnsBorder: Color.fromHex('#cccccc').transparent(0.2).toString(),\n  tableOddRowsBackgroundColor: Color.fromHex('#cccccc').transparent(0.04).toString(),\n  listBackground: undefined,\n  listFocusForeground: undefined,\n  listInactiveSelectionForeground: undefined,\n  listInactiveFocusForeground: undefined,\n  listInactiveFocusBackground: undefined,\n  listHoverForeground: undefined,\n  listFocusOutline: undefined,\n  listInactiveFocusOutline: undefined,\n  listSelectionOutline: undefined,\n  listHoverOutline: undefined,\n  treeStickyScrollBackground: undefined,\n  treeStickyScrollBorder: undefined,\n  treeStickyScrollShadow: undefined\n};\nconst DefaultOptions = {\n  keyboardSupport: true,\n  mouseSupport: true,\n  multipleSelectionSupport: true,\n  dnd: {\n    getDragURI() {\n      return null;\n    },\n    onDragStart() {},\n    onDragOver() {\n      return false;\n    },\n    drop() {},\n    dispose() {}\n  }\n};\n// TODO@Joao: move these utils into a SortedArray class\nfunction getContiguousRangeContaining(range, value) {\n  const index = range.indexOf(value);\n  if (index === -1) {\n    return [];\n  }\n  const result = [];\n  let i = index - 1;\n  while (i >= 0 && range[i] === value - (index - i)) {\n    result.push(range[i--]);\n  }\n  result.reverse();\n  i = index;\n  while (i < range.length && range[i] === value + (i - index)) {\n    result.push(range[i++]);\n  }\n  return result;\n}\n/**\n * Given two sorted collections of numbers, returns the intersection\n * between them (OR).\n */\nfunction disjunction(one, other) {\n  const result = [];\n  let i = 0,\n    j = 0;\n  while (i < one.length || j < other.length) {\n    if (i >= one.length) {\n      result.push(other[j++]);\n    } else if (j >= other.length) {\n      result.push(one[i++]);\n    } else if (one[i] === other[j]) {\n      result.push(one[i]);\n      i++;\n      j++;\n      continue;\n    } else if (one[i] < other[j]) {\n      result.push(one[i++]);\n    } else {\n      result.push(other[j++]);\n    }\n  }\n  return result;\n}\n/**\n * Given two sorted collections of numbers, returns the relative\n * complement between them (XOR).\n */\nfunction relativeComplement(one, other) {\n  const result = [];\n  let i = 0,\n    j = 0;\n  while (i < one.length || j < other.length) {\n    if (i >= one.length) {\n      result.push(other[j++]);\n    } else if (j >= other.length) {\n      result.push(one[i++]);\n    } else if (one[i] === other[j]) {\n      i++;\n      j++;\n      continue;\n    } else if (one[i] < other[j]) {\n      result.push(one[i++]);\n    } else {\n      j++;\n    }\n  }\n  return result;\n}\nconst numericSort = (a, b) => a - b;\nclass PipelineRenderer {\n  constructor(_templateId, renderers) {\n    this._templateId = _templateId;\n    this.renderers = renderers;\n  }\n  get templateId() {\n    return this._templateId;\n  }\n  renderTemplate(container) {\n    return this.renderers.map(r => r.renderTemplate(container));\n  }\n  renderElement(element, index, templateData, height) {\n    let i = 0;\n    for (const renderer of this.renderers) {\n      renderer.renderElement(element, index, templateData[i++], height);\n    }\n  }\n  disposeElement(element, index, templateData, height) {\n    let i = 0;\n    for (const renderer of this.renderers) {\n      renderer.disposeElement?.(element, index, templateData[i], height);\n      i += 1;\n    }\n  }\n  disposeTemplate(templateData) {\n    let i = 0;\n    for (const renderer of this.renderers) {\n      renderer.disposeTemplate(templateData[i++]);\n    }\n  }\n}\nclass AccessibiltyRenderer {\n  constructor(accessibilityProvider) {\n    this.accessibilityProvider = accessibilityProvider;\n    this.templateId = 'a18n';\n  }\n  renderTemplate(container) {\n    return {\n      container,\n      disposables: new DisposableStore()\n    };\n  }\n  renderElement(element, index, data) {\n    const ariaLabel = this.accessibilityProvider.getAriaLabel(element);\n    const observable = ariaLabel && typeof ariaLabel !== 'string' ? ariaLabel : constObservable(ariaLabel);\n    data.disposables.add(autorun(reader => {\n      this.setAriaLabel(reader.readObservable(observable), data.container);\n    }));\n    const ariaLevel = this.accessibilityProvider.getAriaLevel && this.accessibilityProvider.getAriaLevel(element);\n    if (typeof ariaLevel === 'number') {\n      data.container.setAttribute('aria-level', `${ariaLevel}`);\n    } else {\n      data.container.removeAttribute('aria-level');\n    }\n  }\n  setAriaLabel(ariaLabel, element) {\n    if (ariaLabel) {\n      element.setAttribute('aria-label', ariaLabel);\n    } else {\n      element.removeAttribute('aria-label');\n    }\n  }\n  disposeElement(element, index, templateData, height) {\n    templateData.disposables.clear();\n  }\n  disposeTemplate(templateData) {\n    templateData.disposables.dispose();\n  }\n}\nclass ListViewDragAndDrop {\n  constructor(list, dnd) {\n    this.list = list;\n    this.dnd = dnd;\n  }\n  getDragElements(element) {\n    const selection = this.list.getSelectedElements();\n    const elements = selection.indexOf(element) > -1 ? selection : [element];\n    return elements;\n  }\n  getDragURI(element) {\n    return this.dnd.getDragURI(element);\n  }\n  getDragLabel(elements, originalEvent) {\n    if (this.dnd.getDragLabel) {\n      return this.dnd.getDragLabel(elements, originalEvent);\n    }\n    return undefined;\n  }\n  onDragStart(data, originalEvent) {\n    this.dnd.onDragStart?.(data, originalEvent);\n  }\n  onDragOver(data, targetElement, targetIndex, targetSector, originalEvent) {\n    return this.dnd.onDragOver(data, targetElement, targetIndex, targetSector, originalEvent);\n  }\n  onDragLeave(data, targetElement, targetIndex, originalEvent) {\n    this.dnd.onDragLeave?.(data, targetElement, targetIndex, originalEvent);\n  }\n  onDragEnd(originalEvent) {\n    this.dnd.onDragEnd?.(originalEvent);\n  }\n  drop(data, targetElement, targetIndex, targetSector, originalEvent) {\n    this.dnd.drop(data, targetElement, targetIndex, targetSector, originalEvent);\n  }\n  dispose() {\n    this.dnd.dispose();\n  }\n}\n/**\n * The {@link List} is a virtual scrolling widget, built on top of the {@link ListView}\n * widget.\n *\n * Features:\n * - Customizable keyboard and mouse support\n * - Element traits: focus, selection, achor\n * - Accessibility support\n * - Touch support\n * - Performant template-based rendering\n * - Horizontal scrolling\n * - Variable element height support\n * - Dynamic element height support\n * - Drag-and-drop support\n */\nexport class List {\n  get onDidChangeFocus() {\n    return Event.map(this.eventBufferer.wrapEvent(this.focus.onChange), e => this.toListEvent(e), this.disposables);\n  }\n  get onDidChangeSelection() {\n    return Event.map(this.eventBufferer.wrapEvent(this.selection.onChange), e => this.toListEvent(e), this.disposables);\n  }\n  get domId() {\n    return this.view.domId;\n  }\n  get onDidScroll() {\n    return this.view.onDidScroll;\n  }\n  get onMouseClick() {\n    return this.view.onMouseClick;\n  }\n  get onMouseDblClick() {\n    return this.view.onMouseDblClick;\n  }\n  get onMouseMiddleClick() {\n    return this.view.onMouseMiddleClick;\n  }\n  get onPointer() {\n    return this.mouseController.onPointer;\n  }\n  get onMouseDown() {\n    return this.view.onMouseDown;\n  }\n  get onMouseOver() {\n    return this.view.onMouseOver;\n  }\n  get onMouseOut() {\n    return this.view.onMouseOut;\n  }\n  get onTouchStart() {\n    return this.view.onTouchStart;\n  }\n  get onTap() {\n    return this.view.onTap;\n  }\n  /**\n   * Possible context menu trigger events:\n   * - ContextMenu key\n   * - Shift F10\n   * - Ctrl Option Shift M (macOS with VoiceOver)\n   * - Mouse right click\n   */\n  get onContextMenu() {\n    let didJustPressContextMenuKey = false;\n    const fromKeyDown = Event.chain(this.disposables.add(new DomEmitter(this.view.domNode, 'keydown')).event, $ => $.map(e => new StandardKeyboardEvent(e)).filter(e => didJustPressContextMenuKey = e.keyCode === 58 /* KeyCode.ContextMenu */ || e.shiftKey && e.keyCode === 68 /* KeyCode.F10 */).map(e => EventHelper.stop(e, true)).filter(() => false));\n    const fromKeyUp = Event.chain(this.disposables.add(new DomEmitter(this.view.domNode, 'keyup')).event, $ => $.forEach(() => didJustPressContextMenuKey = false).map(e => new StandardKeyboardEvent(e)).filter(e => e.keyCode === 58 /* KeyCode.ContextMenu */ || e.shiftKey && e.keyCode === 68 /* KeyCode.F10 */).map(e => EventHelper.stop(e, true)).map(({\n      browserEvent\n    }) => {\n      const focus = this.getFocus();\n      const index = focus.length ? focus[0] : undefined;\n      const element = typeof index !== 'undefined' ? this.view.element(index) : undefined;\n      const anchor = typeof index !== 'undefined' ? this.view.domElement(index) : this.view.domNode;\n      return {\n        index,\n        element,\n        anchor,\n        browserEvent\n      };\n    }));\n    const fromMouse = Event.chain(this.view.onContextMenu, $ => $.filter(_ => !didJustPressContextMenuKey).map(({\n      element,\n      index,\n      browserEvent\n    }) => ({\n      element,\n      index,\n      anchor: new StandardMouseEvent(getWindow(this.view.domNode), browserEvent),\n      browserEvent\n    })));\n    return Event.any(fromKeyDown, fromKeyUp, fromMouse);\n  }\n  get onKeyDown() {\n    return this.disposables.add(new DomEmitter(this.view.domNode, 'keydown')).event;\n  }\n  get onDidFocus() {\n    return Event.signal(this.disposables.add(new DomEmitter(this.view.domNode, 'focus', true)).event);\n  }\n  get onDidBlur() {\n    return Event.signal(this.disposables.add(new DomEmitter(this.view.domNode, 'blur', true)).event);\n  }\n  constructor(user, container, virtualDelegate, renderers, _options = DefaultOptions) {\n    this.user = user;\n    this._options = _options;\n    this.focus = new Trait('focused');\n    this.anchor = new Trait('anchor');\n    this.eventBufferer = new EventBufferer();\n    this._ariaLabel = '';\n    this.disposables = new DisposableStore();\n    this._onDidDispose = new Emitter();\n    this.onDidDispose = this._onDidDispose.event;\n    const role = this._options.accessibilityProvider && this._options.accessibilityProvider.getWidgetRole ? this._options.accessibilityProvider?.getWidgetRole() : 'list';\n    this.selection = new SelectionTrait(role !== 'listbox');\n    const baseRenderers = [this.focus.renderer, this.selection.renderer];\n    this.accessibilityProvider = _options.accessibilityProvider;\n    if (this.accessibilityProvider) {\n      baseRenderers.push(new AccessibiltyRenderer(this.accessibilityProvider));\n      this.accessibilityProvider.onDidChangeActiveDescendant?.(this.onDidChangeActiveDescendant, this, this.disposables);\n    }\n    renderers = renderers.map(r => new PipelineRenderer(r.templateId, [...baseRenderers, r]));\n    const viewOptions = {\n      ..._options,\n      dnd: _options.dnd && new ListViewDragAndDrop(this, _options.dnd)\n    };\n    this.view = this.createListView(container, virtualDelegate, renderers, viewOptions);\n    this.view.domNode.setAttribute('role', role);\n    if (_options.styleController) {\n      this.styleController = _options.styleController(this.view.domId);\n    } else {\n      const styleElement = createStyleSheet(this.view.domNode);\n      this.styleController = new DefaultStyleController(styleElement, this.view.domId);\n    }\n    this.spliceable = new CombinedSpliceable([new TraitSpliceable(this.focus, this.view, _options.identityProvider), new TraitSpliceable(this.selection, this.view, _options.identityProvider), new TraitSpliceable(this.anchor, this.view, _options.identityProvider), this.view]);\n    this.disposables.add(this.focus);\n    this.disposables.add(this.selection);\n    this.disposables.add(this.anchor);\n    this.disposables.add(this.view);\n    this.disposables.add(this._onDidDispose);\n    this.disposables.add(new DOMFocusController(this, this.view));\n    if (typeof _options.keyboardSupport !== 'boolean' || _options.keyboardSupport) {\n      this.keyboardController = new KeyboardController(this, this.view, _options);\n      this.disposables.add(this.keyboardController);\n    }\n    if (_options.keyboardNavigationLabelProvider) {\n      const delegate = _options.keyboardNavigationDelegate || DefaultKeyboardNavigationDelegate;\n      this.typeNavigationController = new TypeNavigationController(this, this.view, _options.keyboardNavigationLabelProvider, _options.keyboardNavigationEventFilter ?? (() => true), delegate);\n      this.disposables.add(this.typeNavigationController);\n    }\n    this.mouseController = this.createMouseController(_options);\n    this.disposables.add(this.mouseController);\n    this.onDidChangeFocus(this._onFocusChange, this, this.disposables);\n    this.onDidChangeSelection(this._onSelectionChange, this, this.disposables);\n    if (this.accessibilityProvider) {\n      this.ariaLabel = this.accessibilityProvider.getWidgetAriaLabel();\n    }\n    if (this._options.multipleSelectionSupport !== false) {\n      this.view.domNode.setAttribute('aria-multiselectable', 'true');\n    }\n  }\n  createListView(container, virtualDelegate, renderers, viewOptions) {\n    return new ListView(container, virtualDelegate, renderers, viewOptions);\n  }\n  createMouseController(options) {\n    return new MouseController(this);\n  }\n  updateOptions(optionsUpdate = {}) {\n    this._options = {\n      ...this._options,\n      ...optionsUpdate\n    };\n    this.typeNavigationController?.updateOptions(this._options);\n    if (this._options.multipleSelectionController !== undefined) {\n      if (this._options.multipleSelectionSupport) {\n        this.view.domNode.setAttribute('aria-multiselectable', 'true');\n      } else {\n        this.view.domNode.removeAttribute('aria-multiselectable');\n      }\n    }\n    this.mouseController.updateOptions(optionsUpdate);\n    this.keyboardController?.updateOptions(optionsUpdate);\n    this.view.updateOptions(optionsUpdate);\n  }\n  get options() {\n    return this._options;\n  }\n  splice(start, deleteCount, elements = []) {\n    if (start < 0 || start > this.view.length) {\n      throw new ListError(this.user, `Invalid start index: ${start}`);\n    }\n    if (deleteCount < 0) {\n      throw new ListError(this.user, `Invalid delete count: ${deleteCount}`);\n    }\n    if (deleteCount === 0 && elements.length === 0) {\n      return;\n    }\n    this.eventBufferer.bufferEvents(() => this.spliceable.splice(start, deleteCount, elements));\n  }\n  rerender() {\n    this.view.rerender();\n  }\n  element(index) {\n    return this.view.element(index);\n  }\n  indexOf(element) {\n    return this.view.indexOf(element);\n  }\n  indexAt(position) {\n    return this.view.indexAt(position);\n  }\n  get length() {\n    return this.view.length;\n  }\n  get contentHeight() {\n    return this.view.contentHeight;\n  }\n  get onDidChangeContentHeight() {\n    return this.view.onDidChangeContentHeight;\n  }\n  get scrollTop() {\n    return this.view.getScrollTop();\n  }\n  set scrollTop(scrollTop) {\n    this.view.setScrollTop(scrollTop);\n  }\n  get scrollHeight() {\n    return this.view.scrollHeight;\n  }\n  get renderHeight() {\n    return this.view.renderHeight;\n  }\n  get firstVisibleIndex() {\n    return this.view.firstVisibleIndex;\n  }\n  get ariaLabel() {\n    return this._ariaLabel;\n  }\n  set ariaLabel(value) {\n    this._ariaLabel = value;\n    this.view.domNode.setAttribute('aria-label', value);\n  }\n  domFocus() {\n    this.view.domNode.focus({\n      preventScroll: true\n    });\n  }\n  layout(height, width) {\n    this.view.layout(height, width);\n  }\n  setSelection(indexes, browserEvent) {\n    for (const index of indexes) {\n      if (index < 0 || index >= this.length) {\n        throw new ListError(this.user, `Invalid index ${index}`);\n      }\n    }\n    this.selection.set(indexes, browserEvent);\n  }\n  getSelection() {\n    return this.selection.get();\n  }\n  getSelectedElements() {\n    return this.getSelection().map(i => this.view.element(i));\n  }\n  setAnchor(index) {\n    if (typeof index === 'undefined') {\n      this.anchor.set([]);\n      return;\n    }\n    if (index < 0 || index >= this.length) {\n      throw new ListError(this.user, `Invalid index ${index}`);\n    }\n    this.anchor.set([index]);\n  }\n  getAnchor() {\n    return firstOrDefault(this.anchor.get(), undefined);\n  }\n  getAnchorElement() {\n    const anchor = this.getAnchor();\n    return typeof anchor === 'undefined' ? undefined : this.element(anchor);\n  }\n  setFocus(indexes, browserEvent) {\n    for (const index of indexes) {\n      if (index < 0 || index >= this.length) {\n        throw new ListError(this.user, `Invalid index ${index}`);\n      }\n    }\n    this.focus.set(indexes, browserEvent);\n  }\n  focusNext(n = 1, loop = false, browserEvent, filter) {\n    if (this.length === 0) {\n      return;\n    }\n    const focus = this.focus.get();\n    const index = this.findNextIndex(focus.length > 0 ? focus[0] + n : 0, loop, filter);\n    if (index > -1) {\n      this.setFocus([index], browserEvent);\n    }\n  }\n  focusPrevious(n = 1, loop = false, browserEvent, filter) {\n    if (this.length === 0) {\n      return;\n    }\n    const focus = this.focus.get();\n    const index = this.findPreviousIndex(focus.length > 0 ? focus[0] - n : 0, loop, filter);\n    if (index > -1) {\n      this.setFocus([index], browserEvent);\n    }\n  }\n  focusNextPage(browserEvent, filter) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      let lastPageIndex = _this.view.indexAt(_this.view.getScrollTop() + _this.view.renderHeight);\n      lastPageIndex = lastPageIndex === 0 ? 0 : lastPageIndex - 1;\n      const currentlyFocusedElementIndex = _this.getFocus()[0];\n      if (currentlyFocusedElementIndex !== lastPageIndex && (currentlyFocusedElementIndex === undefined || lastPageIndex > currentlyFocusedElementIndex)) {\n        const lastGoodPageIndex = _this.findPreviousIndex(lastPageIndex, false, filter);\n        if (lastGoodPageIndex > -1 && currentlyFocusedElementIndex !== lastGoodPageIndex) {\n          _this.setFocus([lastGoodPageIndex], browserEvent);\n        } else {\n          _this.setFocus([lastPageIndex], browserEvent);\n        }\n      } else {\n        const previousScrollTop = _this.view.getScrollTop();\n        let nextpageScrollTop = previousScrollTop + _this.view.renderHeight;\n        if (lastPageIndex > currentlyFocusedElementIndex) {\n          // scroll last page element to the top only if the last page element is below the focused element\n          nextpageScrollTop -= _this.view.elementHeight(lastPageIndex);\n        }\n        _this.view.setScrollTop(nextpageScrollTop);\n        if (_this.view.getScrollTop() !== previousScrollTop) {\n          _this.setFocus([]);\n          // Let the scroll event listener run\n          yield timeout(0);\n          yield _this.focusNextPage(browserEvent, filter);\n        }\n      }\n    })();\n  }\n  focusPreviousPage(_x, _x2) {\n    var _this2 = this;\n    return _asyncToGenerator(function* (browserEvent, filter, getPaddingTop = () => 0) {\n      let firstPageIndex;\n      const paddingTop = getPaddingTop();\n      const scrollTop = _this2.view.getScrollTop() + paddingTop;\n      if (scrollTop === 0) {\n        firstPageIndex = _this2.view.indexAt(scrollTop);\n      } else {\n        firstPageIndex = _this2.view.indexAfter(scrollTop - 1);\n      }\n      const currentlyFocusedElementIndex = _this2.getFocus()[0];\n      if (currentlyFocusedElementIndex !== firstPageIndex && (currentlyFocusedElementIndex === undefined || currentlyFocusedElementIndex >= firstPageIndex)) {\n        const firstGoodPageIndex = _this2.findNextIndex(firstPageIndex, false, filter);\n        if (firstGoodPageIndex > -1 && currentlyFocusedElementIndex !== firstGoodPageIndex) {\n          _this2.setFocus([firstGoodPageIndex], browserEvent);\n        } else {\n          _this2.setFocus([firstPageIndex], browserEvent);\n        }\n      } else {\n        const previousScrollTop = scrollTop;\n        _this2.view.setScrollTop(scrollTop - _this2.view.renderHeight - paddingTop);\n        if (_this2.view.getScrollTop() + getPaddingTop() !== previousScrollTop) {\n          _this2.setFocus([]);\n          // Let the scroll event listener run\n          yield timeout(0);\n          yield _this2.focusPreviousPage(browserEvent, filter, getPaddingTop);\n        }\n      }\n    }).apply(this, arguments);\n  }\n  focusLast(browserEvent, filter) {\n    if (this.length === 0) {\n      return;\n    }\n    const index = this.findPreviousIndex(this.length - 1, false, filter);\n    if (index > -1) {\n      this.setFocus([index], browserEvent);\n    }\n  }\n  focusFirst(browserEvent, filter) {\n    this.focusNth(0, browserEvent, filter);\n  }\n  focusNth(n, browserEvent, filter) {\n    if (this.length === 0) {\n      return;\n    }\n    const index = this.findNextIndex(n, false, filter);\n    if (index > -1) {\n      this.setFocus([index], browserEvent);\n    }\n  }\n  findNextIndex(index, loop = false, filter) {\n    for (let i = 0; i < this.length; i++) {\n      if (index >= this.length && !loop) {\n        return -1;\n      }\n      index = index % this.length;\n      if (!filter || filter(this.element(index))) {\n        return index;\n      }\n      index++;\n    }\n    return -1;\n  }\n  findPreviousIndex(index, loop = false, filter) {\n    for (let i = 0; i < this.length; i++) {\n      if (index < 0 && !loop) {\n        return -1;\n      }\n      index = (this.length + index % this.length) % this.length;\n      if (!filter || filter(this.element(index))) {\n        return index;\n      }\n      index--;\n    }\n    return -1;\n  }\n  getFocus() {\n    return this.focus.get();\n  }\n  getFocusedElements() {\n    return this.getFocus().map(i => this.view.element(i));\n  }\n  reveal(index, relativeTop, paddingTop = 0) {\n    if (index < 0 || index >= this.length) {\n      throw new ListError(this.user, `Invalid index ${index}`);\n    }\n    const scrollTop = this.view.getScrollTop();\n    const elementTop = this.view.elementTop(index);\n    const elementHeight = this.view.elementHeight(index);\n    if (isNumber(relativeTop)) {\n      // y = mx + b\n      const m = elementHeight - this.view.renderHeight + paddingTop;\n      this.view.setScrollTop(m * clamp(relativeTop, 0, 1) + elementTop - paddingTop);\n    } else {\n      const viewItemBottom = elementTop + elementHeight;\n      const scrollBottom = scrollTop + this.view.renderHeight;\n      if (elementTop < scrollTop + paddingTop && viewItemBottom >= scrollBottom) {\n        // The element is already overflowing the viewport, no-op\n      } else if (elementTop < scrollTop + paddingTop || viewItemBottom >= scrollBottom && elementHeight >= this.view.renderHeight) {\n        this.view.setScrollTop(elementTop - paddingTop);\n      } else if (viewItemBottom >= scrollBottom) {\n        this.view.setScrollTop(viewItemBottom - this.view.renderHeight);\n      }\n    }\n  }\n  /**\n   * Returns the relative position of an element rendered in the list.\n   * Returns `null` if the element isn't *entirely* in the visible viewport.\n   */\n  getRelativeTop(index, paddingTop = 0) {\n    if (index < 0 || index >= this.length) {\n      throw new ListError(this.user, `Invalid index ${index}`);\n    }\n    const scrollTop = this.view.getScrollTop();\n    const elementTop = this.view.elementTop(index);\n    const elementHeight = this.view.elementHeight(index);\n    if (elementTop < scrollTop + paddingTop || elementTop + elementHeight > scrollTop + this.view.renderHeight) {\n      return null;\n    }\n    // y = mx + b\n    const m = elementHeight - this.view.renderHeight + paddingTop;\n    return Math.abs((scrollTop + paddingTop - elementTop) / m);\n  }\n  getHTMLElement() {\n    return this.view.domNode;\n  }\n  getScrollableElement() {\n    return this.view.scrollableElementDomNode;\n  }\n  getElementID(index) {\n    return this.view.getElementDomId(index);\n  }\n  getElementTop(index) {\n    return this.view.elementTop(index);\n  }\n  style(styles) {\n    this.styleController.style(styles);\n  }\n  toListEvent({\n    indexes,\n    browserEvent\n  }) {\n    return {\n      indexes,\n      elements: indexes.map(i => this.view.element(i)),\n      browserEvent\n    };\n  }\n  _onFocusChange() {\n    const focus = this.focus.get();\n    this.view.domNode.classList.toggle('element-focused', focus.length > 0);\n    this.onDidChangeActiveDescendant();\n  }\n  onDidChangeActiveDescendant() {\n    const focus = this.focus.get();\n    if (focus.length > 0) {\n      let id;\n      if (this.accessibilityProvider?.getActiveDescendantId) {\n        id = this.accessibilityProvider.getActiveDescendantId(this.view.element(focus[0]));\n      }\n      this.view.domNode.setAttribute('aria-activedescendant', id || this.view.getElementDomId(focus[0]));\n    } else {\n      this.view.domNode.removeAttribute('aria-activedescendant');\n    }\n  }\n  _onSelectionChange() {\n    const selection = this.selection.get();\n    this.view.domNode.classList.toggle('selection-none', selection.length === 0);\n    this.view.domNode.classList.toggle('selection-single', selection.length === 1);\n    this.view.domNode.classList.toggle('selection-multiple', selection.length > 1);\n  }\n  dispose() {\n    this._onDidDispose.fire();\n    this.disposables.dispose();\n    this._onDidDispose.dispose();\n  }\n}\n__decorate([memoize], List.prototype, \"onDidChangeFocus\", null);\n__decorate([memoize], List.prototype, \"onDidChangeSelection\", null);\n__decorate([memoize], List.prototype, \"onContextMenu\", null);\n__decorate([memoize], List.prototype, \"onKeyDown\", null);\n__decorate([memoize], List.prototype, \"onDidFocus\", null);\n__decorate([memoize], List.prototype, \"onDidBlur\", null);", "map": {"version": 3, "names": ["__decorate", "decorators", "target", "key", "desc", "c", "arguments", "length", "r", "Object", "getOwnPropertyDescriptor", "d", "Reflect", "decorate", "i", "defineProperty", "asCssValueWithDefault", "createStyleSheet", "EventHelper", "getActiveElement", "getWindow", "isHTMLElement", "isMouseEvent", "DomEmitter", "StandardKeyboardEvent", "Gesture", "alert", "CombinedSpliceable", "binarySearch", "firstOrDefault", "range", "timeout", "Color", "memoize", "Emitter", "Event", "EventBuff<PERSON>", "matchesFuzzy2", "matchesPrefix", "DisposableStore", "dispose", "clamp", "platform", "isNumber", "ListError", "ListView", "StandardMouseEvent", "autorun", "constObservable", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "trait", "renderedElements", "templateId", "name", "renderTemplate", "container", "renderElement", "element", "index", "templateData", "renderedElementIndex", "findIndex", "el", "rendered", "unrender", "push", "renderIndex", "splice", "start", "deleteCount", "insertCount", "renderedElement", "renderIndexes", "indexes", "indexOf", "disposeTemplate", "<PERSON><PERSON><PERSON>", "_trait", "renderer", "sortedIndexes", "_onChange", "onChange", "event", "elements", "diff", "end", "j", "_set", "classList", "toggle", "contains", "remove", "set", "browserEvent", "sort", "numericSort", "result", "sortedResult", "to<PERSON><PERSON>", "disjunction", "fire", "get", "prototype", "SelectionTrait", "setAriaSelected", "setAttribute", "TraitSpliceable", "view", "identity<PERSON><PERSON><PERSON>", "Array", "fill", "pastElementsWithTrait", "map", "getId", "toString", "pastElementsWithTraitSet", "Set", "elementsWithTrait", "e", "has", "isInputElement", "tagName", "isListElementDescendantOfClass", "className", "parentElement", "isMonacoEditor", "isMonacoCustomToggle", "isActionItem", "isStickyScrollElement", "isStickyScrollContainer", "isButton", "KeyboardController", "onKeyDown", "chain", "disposables", "add", "domNode", "$", "filter", "list", "options", "multipleSelectionDisposables", "multipleSelectionSupport", "keyCode", "onEnter", "onUpArrow", "onDownArrow", "onPageUpArrow", "onPageDownArrow", "onEscape", "isMacintosh", "metaKey", "ctrl<PERSON>ey", "onCtrlA", "updateOptions", "optionsUpdate", "undefined", "preventDefault", "stopPropagation", "setSelection", "getFocus", "focusPrevious", "setAnchor", "reveal", "focus", "focusNext", "focusPreviousPage", "focusNextPage", "getSelection", "TypeNavigationMode", "TypeNavigationControllerState", "DefaultKeyboardNavigationDelegate", "mightProducePrintableCharacter", "altKey", "TypeNavigationController", "keyboardNavigationLabe<PERSON>", "keyboardNavigationEventFilter", "delegate", "enabled", "state", "Idle", "mode", "Automatic", "triggered", "previouslyFocused", "enabledDisposables", "typeNavigationEnabled", "enable", "disable", "typeNavigationMode", "typing", "onChar", "for<PERSON>ach", "stop", "onClear", "debounce", "onInput", "reduce", "any", "clear", "aria<PERSON><PERSON><PERSON>", "accessibilityProvider", "getAriaLabel", "word", "delta", "Typing", "label", "getKeyboardNavigationLabel", "labelStr", "setFocus", "fuzzy", "fuzzyScore", "DOMFocusController", "onTab", "shift<PERSON>ey", "focusedDomElement", "dom<PERSON>lement", "tabIndexElement", "querySelector", "tabIndex", "style", "getComputedStyle", "visibility", "display", "isSelectionSingleChangeEvent", "isSelectionRangeChangeEvent", "isMouseRightClick", "button", "DefaultMultipleSelectionController", "MouseController", "_onPointer", "onPointer", "multipleSelectionController", "mouseSupport", "onMouseDown", "onContextMenu", "onMouseDblClick", "onDoubleClick", "onTouchStart", "addTarget", "getHTMLElement", "onMouseClick", "onMouseMiddleClick", "onTap", "on<PERSON>iewPointer", "isSelectionChangeEvent", "domFocus", "isHandledByList", "changeSelection", "anchor", "getAnchor", "currentFocus", "min", "Math", "max", "rangeSelection", "selection", "contiguousRange", "getContiguousRangeContaining", "newSelection", "relativeComplement", "DefaultStyleController", "styleElement", "selectorSuffix", "styles", "suffix", "content", "listBackground", "listFocusBackground", "listFocusForeground", "listActiveSelectionBackground", "listActiveSelectionForeground", "listActiveSelectionIconForeground", "listFocusAndSelectionBackground", "listFocusAndSelectionForeground", "listInactiveFocusForeground", "listInactiveSelectionIconForeground", "listInactiveFocusBackground", "listInactiveSelectionBackground", "listInactiveSelectionForeground", "listHoverBackground", "listHoverForeground", "focusAndSelectionOutline", "listFocusAndSelectionOutline", "listSelectionOutline", "listFocusOutline", "inactiveFocusAndSelectionOutline", "listInactiveFocusOutline", "listHoverOutline", "listDropOverBackground", "listDropBetweenBackground", "tableColumnsBorder", "tableOddRowsBackgroundColor", "textContent", "join", "unthemedListStyles", "treeIndentGuidesStroke", "treeInactiveIndentGuidesStroke", "fromHex", "transparent", "treeStickyScrollBackground", "treeStickyScrollBorder", "treeStickyScrollShadow", "DefaultOptions", "keyboardSupport", "dnd", "getDragURI", "onDragStart", "onDragOver", "drop", "value", "reverse", "one", "other", "a", "b", "Pipeline<PERSON><PERSON><PERSON>", "_templateId", "renderers", "height", "disposeElement", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "data", "observable", "reader", "setAriaLabel", "readObservable", "ariaLevel", "getAriaLevel", "removeAttribute", "ListViewDragAndDrop", "getDragElements", "getSelectedElements", "getDragLabel", "originalEvent", "targetElement", "targetIndex", "targetSector", "onDragLeave", "onDragEnd", "List", "onDidChangeFocus", "event<PERSON><PERSON><PERSON>", "wrapEvent", "toListEvent", "onDidChangeSelection", "domId", "onDidScroll", "mouseController", "onMouseOver", "onMouseOut", "didJustPressContextMenuKey", "fromKeyDown", "fromKeyUp", "fromMouse", "_", "onDidFocus", "signal", "onDidBlur", "user", "virtualDelegate", "_options", "_a<PERSON><PERSON><PERSON><PERSON>", "_onDidDispose", "onDidDispose", "role", "getWidgetRole", "baseRenderers", "onDidChangeActiveDescendant", "viewOptions", "createListView", "styleController", "spliceable", "keyboardController", "keyboardNavigationDelegate", "typeNavigationController", "createMouseController", "_onFocusChange", "_onSelectionChange", "getWidgetAriaLabel", "bufferEvents", "rerender", "indexAt", "position", "contentHeight", "onDidChangeContentHeight", "scrollTop", "getScrollTop", "setScrollTop", "scrollHeight", "renderHeight", "firstVisibleIndex", "preventScroll", "layout", "width", "getAnchorElement", "n", "loop", "findNextIndex", "findPreviousIndex", "_this", "_asyncToGenerator", "lastPageIndex", "currentlyFocusedElementIndex", "lastGoodPageIndex", "previousScrollTop", "nextpageScrollTop", "elementHeight", "_x", "_x2", "_this2", "getPaddingTop", "firstPageIndex", "paddingTop", "indexAfter", "firstGoodPageIndex", "apply", "focusLast", "focusFirst", "focusNth", "getFocusedElements", "relativeTop", "elementTop", "m", "viewItemBottom", "scrollBottom", "getRelativeTop", "abs", "getScrollableElement", "scrollableElementDomNode", "getElementID", "getElementDomId", "getElementTop", "id", "getActiveDescendantId"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/browser/ui/list/listWidget.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { asCssValueWithDefault, createStyleSheet, EventHelper, getActiveElement, getWindow, isHTMLElement, isMouseEvent } from '../../dom.js';\nimport { DomEmitter } from '../../event.js';\nimport { StandardKeyboardEvent } from '../../keyboardEvent.js';\nimport { Gesture } from '../../touch.js';\nimport { alert } from '../aria/aria.js';\nimport { CombinedSpliceable } from './splice.js';\nimport { binarySearch, firstOrDefault, range } from '../../../common/arrays.js';\nimport { timeout } from '../../../common/async.js';\nimport { Color } from '../../../common/color.js';\nimport { memoize } from '../../../common/decorators.js';\nimport { Emitter, Event, EventBufferer } from '../../../common/event.js';\nimport { matchesFuzzy2, matchesPrefix } from '../../../common/filters.js';\nimport { DisposableStore, dispose } from '../../../common/lifecycle.js';\nimport { clamp } from '../../../common/numbers.js';\nimport * as platform from '../../../common/platform.js';\nimport { isNumber } from '../../../common/types.js';\nimport './list.css';\nimport { ListError } from './list.js';\nimport { ListView } from './listView.js';\nimport { StandardMouseEvent } from '../../mouseEvent.js';\nimport { autorun, constObservable } from '../../../common/observable.js';\nclass TraitRenderer {\n    constructor(trait) {\n        this.trait = trait;\n        this.renderedElements = [];\n    }\n    get templateId() {\n        return `template:${this.trait.name}`;\n    }\n    renderTemplate(container) {\n        return container;\n    }\n    renderElement(element, index, templateData) {\n        const renderedElementIndex = this.renderedElements.findIndex(el => el.templateData === templateData);\n        if (renderedElementIndex >= 0) {\n            const rendered = this.renderedElements[renderedElementIndex];\n            this.trait.unrender(templateData);\n            rendered.index = index;\n        }\n        else {\n            const rendered = { index, templateData };\n            this.renderedElements.push(rendered);\n        }\n        this.trait.renderIndex(index, templateData);\n    }\n    splice(start, deleteCount, insertCount) {\n        const rendered = [];\n        for (const renderedElement of this.renderedElements) {\n            if (renderedElement.index < start) {\n                rendered.push(renderedElement);\n            }\n            else if (renderedElement.index >= start + deleteCount) {\n                rendered.push({\n                    index: renderedElement.index + insertCount - deleteCount,\n                    templateData: renderedElement.templateData\n                });\n            }\n        }\n        this.renderedElements = rendered;\n    }\n    renderIndexes(indexes) {\n        for (const { index, templateData } of this.renderedElements) {\n            if (indexes.indexOf(index) > -1) {\n                this.trait.renderIndex(index, templateData);\n            }\n        }\n    }\n    disposeTemplate(templateData) {\n        const index = this.renderedElements.findIndex(el => el.templateData === templateData);\n        if (index < 0) {\n            return;\n        }\n        this.renderedElements.splice(index, 1);\n    }\n}\nclass Trait {\n    get name() { return this._trait; }\n    get renderer() {\n        return new TraitRenderer(this);\n    }\n    constructor(_trait) {\n        this._trait = _trait;\n        this.indexes = [];\n        this.sortedIndexes = [];\n        this._onChange = new Emitter();\n        this.onChange = this._onChange.event;\n    }\n    splice(start, deleteCount, elements) {\n        const diff = elements.length - deleteCount;\n        const end = start + deleteCount;\n        const sortedIndexes = [];\n        let i = 0;\n        while (i < this.sortedIndexes.length && this.sortedIndexes[i] < start) {\n            sortedIndexes.push(this.sortedIndexes[i++]);\n        }\n        for (let j = 0; j < elements.length; j++) {\n            if (elements[j]) {\n                sortedIndexes.push(j + start);\n            }\n        }\n        while (i < this.sortedIndexes.length && this.sortedIndexes[i] >= end) {\n            sortedIndexes.push(this.sortedIndexes[i++] + diff);\n        }\n        this.renderer.splice(start, deleteCount, elements.length);\n        this._set(sortedIndexes, sortedIndexes);\n    }\n    renderIndex(index, container) {\n        container.classList.toggle(this._trait, this.contains(index));\n    }\n    unrender(container) {\n        container.classList.remove(this._trait);\n    }\n    /**\n     * Sets the indexes which should have this trait.\n     *\n     * @param indexes Indexes which should have this trait.\n     * @return The old indexes which had this trait.\n     */\n    set(indexes, browserEvent) {\n        return this._set(indexes, [...indexes].sort(numericSort), browserEvent);\n    }\n    _set(indexes, sortedIndexes, browserEvent) {\n        const result = this.indexes;\n        const sortedResult = this.sortedIndexes;\n        this.indexes = indexes;\n        this.sortedIndexes = sortedIndexes;\n        const toRender = disjunction(sortedResult, indexes);\n        this.renderer.renderIndexes(toRender);\n        this._onChange.fire({ indexes, browserEvent });\n        return result;\n    }\n    get() {\n        return this.indexes;\n    }\n    contains(index) {\n        return binarySearch(this.sortedIndexes, index, numericSort) >= 0;\n    }\n    dispose() {\n        dispose(this._onChange);\n    }\n}\n__decorate([\n    memoize\n], Trait.prototype, \"renderer\", null);\nclass SelectionTrait extends Trait {\n    constructor(setAriaSelected) {\n        super('selected');\n        this.setAriaSelected = setAriaSelected;\n    }\n    renderIndex(index, container) {\n        super.renderIndex(index, container);\n        if (this.setAriaSelected) {\n            if (this.contains(index)) {\n                container.setAttribute('aria-selected', 'true');\n            }\n            else {\n                container.setAttribute('aria-selected', 'false');\n            }\n        }\n    }\n}\n/**\n * The TraitSpliceable is used as a util class to be able\n * to preserve traits across splice calls, given an identity\n * provider.\n */\nclass TraitSpliceable {\n    constructor(trait, view, identityProvider) {\n        this.trait = trait;\n        this.view = view;\n        this.identityProvider = identityProvider;\n    }\n    splice(start, deleteCount, elements) {\n        if (!this.identityProvider) {\n            return this.trait.splice(start, deleteCount, new Array(elements.length).fill(false));\n        }\n        const pastElementsWithTrait = this.trait.get().map(i => this.identityProvider.getId(this.view.element(i)).toString());\n        if (pastElementsWithTrait.length === 0) {\n            return this.trait.splice(start, deleteCount, new Array(elements.length).fill(false));\n        }\n        const pastElementsWithTraitSet = new Set(pastElementsWithTrait);\n        const elementsWithTrait = elements.map(e => pastElementsWithTraitSet.has(this.identityProvider.getId(e).toString()));\n        this.trait.splice(start, deleteCount, elementsWithTrait);\n    }\n}\nexport function isInputElement(e) {\n    return e.tagName === 'INPUT' || e.tagName === 'TEXTAREA';\n}\nfunction isListElementDescendantOfClass(e, className) {\n    if (e.classList.contains(className)) {\n        return true;\n    }\n    if (e.classList.contains('monaco-list')) {\n        return false;\n    }\n    if (!e.parentElement) {\n        return false;\n    }\n    return isListElementDescendantOfClass(e.parentElement, className);\n}\nexport function isMonacoEditor(e) {\n    return isListElementDescendantOfClass(e, 'monaco-editor');\n}\nexport function isMonacoCustomToggle(e) {\n    return isListElementDescendantOfClass(e, 'monaco-custom-toggle');\n}\nexport function isActionItem(e) {\n    return isListElementDescendantOfClass(e, 'action-item');\n}\nexport function isStickyScrollElement(e) {\n    return isListElementDescendantOfClass(e, 'monaco-tree-sticky-row');\n}\nexport function isStickyScrollContainer(e) {\n    return e.classList.contains('monaco-tree-sticky-container');\n}\nexport function isButton(e) {\n    if ((e.tagName === 'A' && e.classList.contains('monaco-button')) ||\n        (e.tagName === 'DIV' && e.classList.contains('monaco-button-dropdown'))) {\n        return true;\n    }\n    if (e.classList.contains('monaco-list')) {\n        return false;\n    }\n    if (!e.parentElement) {\n        return false;\n    }\n    return isButton(e.parentElement);\n}\nclass KeyboardController {\n    get onKeyDown() {\n        return Event.chain(this.disposables.add(new DomEmitter(this.view.domNode, 'keydown')).event, $ => $.filter(e => !isInputElement(e.target))\n            .map(e => new StandardKeyboardEvent(e)));\n    }\n    constructor(list, view, options) {\n        this.list = list;\n        this.view = view;\n        this.disposables = new DisposableStore();\n        this.multipleSelectionDisposables = new DisposableStore();\n        this.multipleSelectionSupport = options.multipleSelectionSupport;\n        this.disposables.add(this.onKeyDown(e => {\n            switch (e.keyCode) {\n                case 3 /* KeyCode.Enter */:\n                    return this.onEnter(e);\n                case 16 /* KeyCode.UpArrow */:\n                    return this.onUpArrow(e);\n                case 18 /* KeyCode.DownArrow */:\n                    return this.onDownArrow(e);\n                case 11 /* KeyCode.PageUp */:\n                    return this.onPageUpArrow(e);\n                case 12 /* KeyCode.PageDown */:\n                    return this.onPageDownArrow(e);\n                case 9 /* KeyCode.Escape */:\n                    return this.onEscape(e);\n                case 31 /* KeyCode.KeyA */:\n                    if (this.multipleSelectionSupport && (platform.isMacintosh ? e.metaKey : e.ctrlKey)) {\n                        this.onCtrlA(e);\n                    }\n            }\n        }));\n    }\n    updateOptions(optionsUpdate) {\n        if (optionsUpdate.multipleSelectionSupport !== undefined) {\n            this.multipleSelectionSupport = optionsUpdate.multipleSelectionSupport;\n        }\n    }\n    onEnter(e) {\n        e.preventDefault();\n        e.stopPropagation();\n        this.list.setSelection(this.list.getFocus(), e.browserEvent);\n    }\n    onUpArrow(e) {\n        e.preventDefault();\n        e.stopPropagation();\n        this.list.focusPrevious(1, false, e.browserEvent);\n        const el = this.list.getFocus()[0];\n        this.list.setAnchor(el);\n        this.list.reveal(el);\n        this.view.domNode.focus();\n    }\n    onDownArrow(e) {\n        e.preventDefault();\n        e.stopPropagation();\n        this.list.focusNext(1, false, e.browserEvent);\n        const el = this.list.getFocus()[0];\n        this.list.setAnchor(el);\n        this.list.reveal(el);\n        this.view.domNode.focus();\n    }\n    onPageUpArrow(e) {\n        e.preventDefault();\n        e.stopPropagation();\n        this.list.focusPreviousPage(e.browserEvent);\n        const el = this.list.getFocus()[0];\n        this.list.setAnchor(el);\n        this.list.reveal(el);\n        this.view.domNode.focus();\n    }\n    onPageDownArrow(e) {\n        e.preventDefault();\n        e.stopPropagation();\n        this.list.focusNextPage(e.browserEvent);\n        const el = this.list.getFocus()[0];\n        this.list.setAnchor(el);\n        this.list.reveal(el);\n        this.view.domNode.focus();\n    }\n    onCtrlA(e) {\n        e.preventDefault();\n        e.stopPropagation();\n        this.list.setSelection(range(this.list.length), e.browserEvent);\n        this.list.setAnchor(undefined);\n        this.view.domNode.focus();\n    }\n    onEscape(e) {\n        if (this.list.getSelection().length) {\n            e.preventDefault();\n            e.stopPropagation();\n            this.list.setSelection([], e.browserEvent);\n            this.list.setAnchor(undefined);\n            this.view.domNode.focus();\n        }\n    }\n    dispose() {\n        this.disposables.dispose();\n        this.multipleSelectionDisposables.dispose();\n    }\n}\n__decorate([\n    memoize\n], KeyboardController.prototype, \"onKeyDown\", null);\nexport var TypeNavigationMode;\n(function (TypeNavigationMode) {\n    TypeNavigationMode[TypeNavigationMode[\"Automatic\"] = 0] = \"Automatic\";\n    TypeNavigationMode[TypeNavigationMode[\"Trigger\"] = 1] = \"Trigger\";\n})(TypeNavigationMode || (TypeNavigationMode = {}));\nvar TypeNavigationControllerState;\n(function (TypeNavigationControllerState) {\n    TypeNavigationControllerState[TypeNavigationControllerState[\"Idle\"] = 0] = \"Idle\";\n    TypeNavigationControllerState[TypeNavigationControllerState[\"Typing\"] = 1] = \"Typing\";\n})(TypeNavigationControllerState || (TypeNavigationControllerState = {}));\nexport const DefaultKeyboardNavigationDelegate = new class {\n    mightProducePrintableCharacter(event) {\n        if (event.ctrlKey || event.metaKey || event.altKey) {\n            return false;\n        }\n        return (event.keyCode >= 31 /* KeyCode.KeyA */ && event.keyCode <= 56 /* KeyCode.KeyZ */)\n            || (event.keyCode >= 21 /* KeyCode.Digit0 */ && event.keyCode <= 30 /* KeyCode.Digit9 */)\n            || (event.keyCode >= 98 /* KeyCode.Numpad0 */ && event.keyCode <= 107 /* KeyCode.Numpad9 */)\n            || (event.keyCode >= 85 /* KeyCode.Semicolon */ && event.keyCode <= 95 /* KeyCode.Quote */);\n    }\n};\nclass TypeNavigationController {\n    constructor(list, view, keyboardNavigationLabelProvider, keyboardNavigationEventFilter, delegate) {\n        this.list = list;\n        this.view = view;\n        this.keyboardNavigationLabelProvider = keyboardNavigationLabelProvider;\n        this.keyboardNavigationEventFilter = keyboardNavigationEventFilter;\n        this.delegate = delegate;\n        this.enabled = false;\n        this.state = TypeNavigationControllerState.Idle;\n        this.mode = TypeNavigationMode.Automatic;\n        this.triggered = false;\n        this.previouslyFocused = -1;\n        this.enabledDisposables = new DisposableStore();\n        this.disposables = new DisposableStore();\n        this.updateOptions(list.options);\n    }\n    updateOptions(options) {\n        if (options.typeNavigationEnabled ?? true) {\n            this.enable();\n        }\n        else {\n            this.disable();\n        }\n        this.mode = options.typeNavigationMode ?? TypeNavigationMode.Automatic;\n    }\n    enable() {\n        if (this.enabled) {\n            return;\n        }\n        let typing = false;\n        const onChar = Event.chain(this.enabledDisposables.add(new DomEmitter(this.view.domNode, 'keydown')).event, $ => $.filter(e => !isInputElement(e.target))\n            .filter(() => this.mode === TypeNavigationMode.Automatic || this.triggered)\n            .map(event => new StandardKeyboardEvent(event))\n            .filter(e => typing || this.keyboardNavigationEventFilter(e))\n            .filter(e => this.delegate.mightProducePrintableCharacter(e))\n            .forEach(e => EventHelper.stop(e, true))\n            .map(event => event.browserEvent.key));\n        const onClear = Event.debounce(onChar, () => null, 800, undefined, undefined, undefined, this.enabledDisposables);\n        const onInput = Event.reduce(Event.any(onChar, onClear), (r, i) => i === null ? null : ((r || '') + i), undefined, this.enabledDisposables);\n        onInput(this.onInput, this, this.enabledDisposables);\n        onClear(this.onClear, this, this.enabledDisposables);\n        onChar(() => typing = true, undefined, this.enabledDisposables);\n        onClear(() => typing = false, undefined, this.enabledDisposables);\n        this.enabled = true;\n        this.triggered = false;\n    }\n    disable() {\n        if (!this.enabled) {\n            return;\n        }\n        this.enabledDisposables.clear();\n        this.enabled = false;\n        this.triggered = false;\n    }\n    onClear() {\n        const focus = this.list.getFocus();\n        if (focus.length > 0 && focus[0] === this.previouslyFocused) {\n            // List: re-announce element on typing end since typed keys will interrupt aria label of focused element\n            // Do not announce if there was a focus change at the end to prevent duplication https://github.com/microsoft/vscode/issues/95961\n            const ariaLabel = this.list.options.accessibilityProvider?.getAriaLabel(this.list.element(focus[0]));\n            if (typeof ariaLabel === 'string') {\n                alert(ariaLabel);\n            }\n            else if (ariaLabel) {\n                alert(ariaLabel.get());\n            }\n        }\n        this.previouslyFocused = -1;\n    }\n    onInput(word) {\n        if (!word) {\n            this.state = TypeNavigationControllerState.Idle;\n            this.triggered = false;\n            return;\n        }\n        const focus = this.list.getFocus();\n        const start = focus.length > 0 ? focus[0] : 0;\n        const delta = this.state === TypeNavigationControllerState.Idle ? 1 : 0;\n        this.state = TypeNavigationControllerState.Typing;\n        for (let i = 0; i < this.list.length; i++) {\n            const index = (start + i + delta) % this.list.length;\n            const label = this.keyboardNavigationLabelProvider.getKeyboardNavigationLabel(this.view.element(index));\n            const labelStr = label && label.toString();\n            if (this.list.options.typeNavigationEnabled) {\n                if (typeof labelStr !== 'undefined') {\n                    // If prefix is found, focus and return early\n                    if (matchesPrefix(word, labelStr)) {\n                        this.previouslyFocused = start;\n                        this.list.setFocus([index]);\n                        this.list.reveal(index);\n                        return;\n                    }\n                    const fuzzy = matchesFuzzy2(word, labelStr);\n                    if (fuzzy) {\n                        const fuzzyScore = fuzzy[0].end - fuzzy[0].start;\n                        // ensures that when fuzzy matching, doesn't clash with prefix matching (1 input vs 1+ should be prefix and fuzzy respecitvely). Also makes sure that exact matches are prioritized.\n                        if (fuzzyScore > 1 && fuzzy.length === 1) {\n                            this.previouslyFocused = start;\n                            this.list.setFocus([index]);\n                            this.list.reveal(index);\n                            return;\n                        }\n                    }\n                }\n            }\n            else if (typeof labelStr === 'undefined' || matchesPrefix(word, labelStr)) {\n                this.previouslyFocused = start;\n                this.list.setFocus([index]);\n                this.list.reveal(index);\n                return;\n            }\n        }\n    }\n    dispose() {\n        this.disable();\n        this.enabledDisposables.dispose();\n        this.disposables.dispose();\n    }\n}\nclass DOMFocusController {\n    constructor(list, view) {\n        this.list = list;\n        this.view = view;\n        this.disposables = new DisposableStore();\n        const onKeyDown = Event.chain(this.disposables.add(new DomEmitter(view.domNode, 'keydown')).event, $ => $\n            .filter(e => !isInputElement(e.target))\n            .map(e => new StandardKeyboardEvent(e)));\n        const onTab = Event.chain(onKeyDown, $ => $.filter(e => e.keyCode === 2 /* KeyCode.Tab */ && !e.ctrlKey && !e.metaKey && !e.shiftKey && !e.altKey));\n        onTab(this.onTab, this, this.disposables);\n    }\n    onTab(e) {\n        if (e.target !== this.view.domNode) {\n            return;\n        }\n        const focus = this.list.getFocus();\n        if (focus.length === 0) {\n            return;\n        }\n        const focusedDomElement = this.view.domElement(focus[0]);\n        if (!focusedDomElement) {\n            return;\n        }\n        const tabIndexElement = focusedDomElement.querySelector('[tabIndex]');\n        if (!tabIndexElement || !(isHTMLElement(tabIndexElement)) || tabIndexElement.tabIndex === -1) {\n            return;\n        }\n        const style = getWindow(tabIndexElement).getComputedStyle(tabIndexElement);\n        if (style.visibility === 'hidden' || style.display === 'none') {\n            return;\n        }\n        e.preventDefault();\n        e.stopPropagation();\n        tabIndexElement.focus();\n    }\n    dispose() {\n        this.disposables.dispose();\n    }\n}\nexport function isSelectionSingleChangeEvent(event) {\n    return platform.isMacintosh ? event.browserEvent.metaKey : event.browserEvent.ctrlKey;\n}\nexport function isSelectionRangeChangeEvent(event) {\n    return event.browserEvent.shiftKey;\n}\nfunction isMouseRightClick(event) {\n    return isMouseEvent(event) && event.button === 2;\n}\nconst DefaultMultipleSelectionController = {\n    isSelectionSingleChangeEvent,\n    isSelectionRangeChangeEvent\n};\nexport class MouseController {\n    constructor(list) {\n        this.list = list;\n        this.disposables = new DisposableStore();\n        this._onPointer = new Emitter();\n        this.onPointer = this._onPointer.event;\n        if (list.options.multipleSelectionSupport !== false) {\n            this.multipleSelectionController = this.list.options.multipleSelectionController || DefaultMultipleSelectionController;\n        }\n        this.mouseSupport = typeof list.options.mouseSupport === 'undefined' || !!list.options.mouseSupport;\n        if (this.mouseSupport) {\n            list.onMouseDown(this.onMouseDown, this, this.disposables);\n            list.onContextMenu(this.onContextMenu, this, this.disposables);\n            list.onMouseDblClick(this.onDoubleClick, this, this.disposables);\n            list.onTouchStart(this.onMouseDown, this, this.disposables);\n            this.disposables.add(Gesture.addTarget(list.getHTMLElement()));\n        }\n        Event.any(list.onMouseClick, list.onMouseMiddleClick, list.onTap)(this.onViewPointer, this, this.disposables);\n    }\n    updateOptions(optionsUpdate) {\n        if (optionsUpdate.multipleSelectionSupport !== undefined) {\n            this.multipleSelectionController = undefined;\n            if (optionsUpdate.multipleSelectionSupport) {\n                this.multipleSelectionController = this.list.options.multipleSelectionController || DefaultMultipleSelectionController;\n            }\n        }\n    }\n    isSelectionSingleChangeEvent(event) {\n        if (!this.multipleSelectionController) {\n            return false;\n        }\n        return this.multipleSelectionController.isSelectionSingleChangeEvent(event);\n    }\n    isSelectionRangeChangeEvent(event) {\n        if (!this.multipleSelectionController) {\n            return false;\n        }\n        return this.multipleSelectionController.isSelectionRangeChangeEvent(event);\n    }\n    isSelectionChangeEvent(event) {\n        return this.isSelectionSingleChangeEvent(event) || this.isSelectionRangeChangeEvent(event);\n    }\n    onMouseDown(e) {\n        if (isMonacoEditor(e.browserEvent.target)) {\n            return;\n        }\n        if (getActiveElement() !== e.browserEvent.target) {\n            this.list.domFocus();\n        }\n    }\n    onContextMenu(e) {\n        if (isInputElement(e.browserEvent.target) || isMonacoEditor(e.browserEvent.target)) {\n            return;\n        }\n        const focus = typeof e.index === 'undefined' ? [] : [e.index];\n        this.list.setFocus(focus, e.browserEvent);\n    }\n    onViewPointer(e) {\n        if (!this.mouseSupport) {\n            return;\n        }\n        if (isInputElement(e.browserEvent.target) || isMonacoEditor(e.browserEvent.target)) {\n            return;\n        }\n        if (e.browserEvent.isHandledByList) {\n            return;\n        }\n        e.browserEvent.isHandledByList = true;\n        const focus = e.index;\n        if (typeof focus === 'undefined') {\n            this.list.setFocus([], e.browserEvent);\n            this.list.setSelection([], e.browserEvent);\n            this.list.setAnchor(undefined);\n            return;\n        }\n        if (this.isSelectionChangeEvent(e)) {\n            return this.changeSelection(e);\n        }\n        this.list.setFocus([focus], e.browserEvent);\n        this.list.setAnchor(focus);\n        if (!isMouseRightClick(e.browserEvent)) {\n            this.list.setSelection([focus], e.browserEvent);\n        }\n        this._onPointer.fire(e);\n    }\n    onDoubleClick(e) {\n        if (isInputElement(e.browserEvent.target) || isMonacoEditor(e.browserEvent.target)) {\n            return;\n        }\n        if (this.isSelectionChangeEvent(e)) {\n            return;\n        }\n        if (e.browserEvent.isHandledByList) {\n            return;\n        }\n        e.browserEvent.isHandledByList = true;\n        const focus = this.list.getFocus();\n        this.list.setSelection(focus, e.browserEvent);\n    }\n    changeSelection(e) {\n        const focus = e.index;\n        let anchor = this.list.getAnchor();\n        if (this.isSelectionRangeChangeEvent(e)) {\n            if (typeof anchor === 'undefined') {\n                const currentFocus = this.list.getFocus()[0];\n                anchor = currentFocus ?? focus;\n                this.list.setAnchor(anchor);\n            }\n            const min = Math.min(anchor, focus);\n            const max = Math.max(anchor, focus);\n            const rangeSelection = range(min, max + 1);\n            const selection = this.list.getSelection();\n            const contiguousRange = getContiguousRangeContaining(disjunction(selection, [anchor]), anchor);\n            if (contiguousRange.length === 0) {\n                return;\n            }\n            const newSelection = disjunction(rangeSelection, relativeComplement(selection, contiguousRange));\n            this.list.setSelection(newSelection, e.browserEvent);\n            this.list.setFocus([focus], e.browserEvent);\n        }\n        else if (this.isSelectionSingleChangeEvent(e)) {\n            const selection = this.list.getSelection();\n            const newSelection = selection.filter(i => i !== focus);\n            this.list.setFocus([focus]);\n            this.list.setAnchor(focus);\n            if (selection.length === newSelection.length) {\n                this.list.setSelection([...newSelection, focus], e.browserEvent);\n            }\n            else {\n                this.list.setSelection(newSelection, e.browserEvent);\n            }\n        }\n    }\n    dispose() {\n        this.disposables.dispose();\n    }\n}\nexport class DefaultStyleController {\n    constructor(styleElement, selectorSuffix) {\n        this.styleElement = styleElement;\n        this.selectorSuffix = selectorSuffix;\n    }\n    style(styles) {\n        const suffix = this.selectorSuffix && `.${this.selectorSuffix}`;\n        const content = [];\n        if (styles.listBackground) {\n            content.push(`.monaco-list${suffix} .monaco-list-rows { background: ${styles.listBackground}; }`);\n        }\n        if (styles.listFocusBackground) {\n            content.push(`.monaco-list${suffix}:focus .monaco-list-row.focused { background-color: ${styles.listFocusBackground}; }`);\n            content.push(`.monaco-list${suffix}:focus .monaco-list-row.focused:hover { background-color: ${styles.listFocusBackground}; }`); // overwrite :hover style in this case!\n        }\n        if (styles.listFocusForeground) {\n            content.push(`.monaco-list${suffix}:focus .monaco-list-row.focused { color: ${styles.listFocusForeground}; }`);\n        }\n        if (styles.listActiveSelectionBackground) {\n            content.push(`.monaco-list${suffix}:focus .monaco-list-row.selected { background-color: ${styles.listActiveSelectionBackground}; }`);\n            content.push(`.monaco-list${suffix}:focus .monaco-list-row.selected:hover { background-color: ${styles.listActiveSelectionBackground}; }`); // overwrite :hover style in this case!\n        }\n        if (styles.listActiveSelectionForeground) {\n            content.push(`.monaco-list${suffix}:focus .monaco-list-row.selected { color: ${styles.listActiveSelectionForeground}; }`);\n        }\n        if (styles.listActiveSelectionIconForeground) {\n            content.push(`.monaco-list${suffix}:focus .monaco-list-row.selected .codicon { color: ${styles.listActiveSelectionIconForeground}; }`);\n        }\n        if (styles.listFocusAndSelectionBackground) {\n            content.push(`\n\t\t\t\t.monaco-drag-image,\n\t\t\t\t.monaco-list${suffix}:focus .monaco-list-row.selected.focused { background-color: ${styles.listFocusAndSelectionBackground}; }\n\t\t\t`);\n        }\n        if (styles.listFocusAndSelectionForeground) {\n            content.push(`\n\t\t\t\t.monaco-drag-image,\n\t\t\t\t.monaco-list${suffix}:focus .monaco-list-row.selected.focused { color: ${styles.listFocusAndSelectionForeground}; }\n\t\t\t`);\n        }\n        if (styles.listInactiveFocusForeground) {\n            content.push(`.monaco-list${suffix} .monaco-list-row.focused { color:  ${styles.listInactiveFocusForeground}; }`);\n            content.push(`.monaco-list${suffix} .monaco-list-row.focused:hover { color:  ${styles.listInactiveFocusForeground}; }`); // overwrite :hover style in this case!\n        }\n        if (styles.listInactiveSelectionIconForeground) {\n            content.push(`.monaco-list${suffix} .monaco-list-row.focused .codicon { color:  ${styles.listInactiveSelectionIconForeground}; }`);\n        }\n        if (styles.listInactiveFocusBackground) {\n            content.push(`.monaco-list${suffix} .monaco-list-row.focused { background-color:  ${styles.listInactiveFocusBackground}; }`);\n            content.push(`.monaco-list${suffix} .monaco-list-row.focused:hover { background-color:  ${styles.listInactiveFocusBackground}; }`); // overwrite :hover style in this case!\n        }\n        if (styles.listInactiveSelectionBackground) {\n            content.push(`.monaco-list${suffix} .monaco-list-row.selected { background-color:  ${styles.listInactiveSelectionBackground}; }`);\n            content.push(`.monaco-list${suffix} .monaco-list-row.selected:hover { background-color:  ${styles.listInactiveSelectionBackground}; }`); // overwrite :hover style in this case!\n        }\n        if (styles.listInactiveSelectionForeground) {\n            content.push(`.monaco-list${suffix} .monaco-list-row.selected { color: ${styles.listInactiveSelectionForeground}; }`);\n        }\n        if (styles.listHoverBackground) {\n            content.push(`.monaco-list${suffix}:not(.drop-target):not(.dragging) .monaco-list-row:hover:not(.selected):not(.focused) { background-color: ${styles.listHoverBackground}; }`);\n        }\n        if (styles.listHoverForeground) {\n            content.push(`.monaco-list${suffix}:not(.drop-target):not(.dragging) .monaco-list-row:hover:not(.selected):not(.focused) { color:  ${styles.listHoverForeground}; }`);\n        }\n        /**\n         * Outlines\n         */\n        const focusAndSelectionOutline = asCssValueWithDefault(styles.listFocusAndSelectionOutline, asCssValueWithDefault(styles.listSelectionOutline, styles.listFocusOutline ?? ''));\n        if (focusAndSelectionOutline) { // default: listFocusOutline\n            content.push(`.monaco-list${suffix}:focus .monaco-list-row.focused.selected { outline: 1px solid ${focusAndSelectionOutline}; outline-offset: -1px;}`);\n        }\n        if (styles.listFocusOutline) { // default: set\n            content.push(`\n\t\t\t\t.monaco-drag-image,\n\t\t\t\t.monaco-list${suffix}:focus .monaco-list-row.focused { outline: 1px solid ${styles.listFocusOutline}; outline-offset: -1px; }\n\t\t\t\t.monaco-workbench.context-menu-visible .monaco-list${suffix}.last-focused .monaco-list-row.focused { outline: 1px solid ${styles.listFocusOutline}; outline-offset: -1px; }\n\t\t\t`);\n        }\n        const inactiveFocusAndSelectionOutline = asCssValueWithDefault(styles.listSelectionOutline, styles.listInactiveFocusOutline ?? '');\n        if (inactiveFocusAndSelectionOutline) {\n            content.push(`.monaco-list${suffix} .monaco-list-row.focused.selected { outline: 1px dotted ${inactiveFocusAndSelectionOutline}; outline-offset: -1px; }`);\n        }\n        if (styles.listSelectionOutline) { // default: activeContrastBorder\n            content.push(`.monaco-list${suffix} .monaco-list-row.selected { outline: 1px dotted ${styles.listSelectionOutline}; outline-offset: -1px; }`);\n        }\n        if (styles.listInactiveFocusOutline) { // default: null\n            content.push(`.monaco-list${suffix} .monaco-list-row.focused { outline: 1px dotted ${styles.listInactiveFocusOutline}; outline-offset: -1px; }`);\n        }\n        if (styles.listHoverOutline) { // default: activeContrastBorder\n            content.push(`.monaco-list${suffix} .monaco-list-row:hover { outline: 1px dashed ${styles.listHoverOutline}; outline-offset: -1px; }`);\n        }\n        if (styles.listDropOverBackground) {\n            content.push(`\n\t\t\t\t.monaco-list${suffix}.drop-target,\n\t\t\t\t.monaco-list${suffix} .monaco-list-rows.drop-target,\n\t\t\t\t.monaco-list${suffix} .monaco-list-row.drop-target { background-color: ${styles.listDropOverBackground} !important; color: inherit !important; }\n\t\t\t`);\n        }\n        if (styles.listDropBetweenBackground) {\n            content.push(`\n\t\t\t.monaco-list${suffix} .monaco-list-rows.drop-target-before .monaco-list-row:first-child::before,\n\t\t\t.monaco-list${suffix} .monaco-list-row.drop-target-before::before {\n\t\t\t\tcontent: \"\"; position: absolute; top: 0px; left: 0px; width: 100%; height: 1px;\n\t\t\t\tbackground-color: ${styles.listDropBetweenBackground};\n\t\t\t}`);\n            content.push(`\n\t\t\t.monaco-list${suffix} .monaco-list-rows.drop-target-after .monaco-list-row:last-child::after,\n\t\t\t.monaco-list${suffix} .monaco-list-row.drop-target-after::after {\n\t\t\t\tcontent: \"\"; position: absolute; bottom: 0px; left: 0px; width: 100%; height: 1px;\n\t\t\t\tbackground-color: ${styles.listDropBetweenBackground};\n\t\t\t}`);\n        }\n        if (styles.tableColumnsBorder) {\n            content.push(`\n\t\t\t\t.monaco-table > .monaco-split-view2,\n\t\t\t\t.monaco-table > .monaco-split-view2 .monaco-sash.vertical::before,\n\t\t\t\t.monaco-workbench:not(.reduce-motion) .monaco-table:hover > .monaco-split-view2,\n\t\t\t\t.monaco-workbench:not(.reduce-motion) .monaco-table:hover > .monaco-split-view2 .monaco-sash.vertical::before {\n\t\t\t\t\tborder-color: ${styles.tableColumnsBorder};\n\t\t\t\t}\n\n\t\t\t\t.monaco-workbench:not(.reduce-motion) .monaco-table > .monaco-split-view2,\n\t\t\t\t.monaco-workbench:not(.reduce-motion) .monaco-table > .monaco-split-view2 .monaco-sash.vertical::before {\n\t\t\t\t\tborder-color: transparent;\n\t\t\t\t}\n\t\t\t`);\n        }\n        if (styles.tableOddRowsBackgroundColor) {\n            content.push(`\n\t\t\t\t.monaco-table .monaco-list-row[data-parity=odd]:not(.focused):not(.selected):not(:hover) .monaco-table-tr,\n\t\t\t\t.monaco-table .monaco-list:not(:focus) .monaco-list-row[data-parity=odd].focused:not(.selected):not(:hover) .monaco-table-tr,\n\t\t\t\t.monaco-table .monaco-list:not(.focused) .monaco-list-row[data-parity=odd].focused:not(.selected):not(:hover) .monaco-table-tr {\n\t\t\t\t\tbackground-color: ${styles.tableOddRowsBackgroundColor};\n\t\t\t\t}\n\t\t\t`);\n        }\n        this.styleElement.textContent = content.join('\\n');\n    }\n}\nexport const unthemedListStyles = {\n    listFocusBackground: '#7FB0D0',\n    listActiveSelectionBackground: '#0E639C',\n    listActiveSelectionForeground: '#FFFFFF',\n    listActiveSelectionIconForeground: '#FFFFFF',\n    listFocusAndSelectionOutline: '#90C2F9',\n    listFocusAndSelectionBackground: '#094771',\n    listFocusAndSelectionForeground: '#FFFFFF',\n    listInactiveSelectionBackground: '#3F3F46',\n    listInactiveSelectionIconForeground: '#FFFFFF',\n    listHoverBackground: '#2A2D2E',\n    listDropOverBackground: '#383B3D',\n    listDropBetweenBackground: '#EEEEEE',\n    treeIndentGuidesStroke: '#a9a9a9',\n    treeInactiveIndentGuidesStroke: Color.fromHex('#a9a9a9').transparent(0.4).toString(),\n    tableColumnsBorder: Color.fromHex('#cccccc').transparent(0.2).toString(),\n    tableOddRowsBackgroundColor: Color.fromHex('#cccccc').transparent(0.04).toString(),\n    listBackground: undefined,\n    listFocusForeground: undefined,\n    listInactiveSelectionForeground: undefined,\n    listInactiveFocusForeground: undefined,\n    listInactiveFocusBackground: undefined,\n    listHoverForeground: undefined,\n    listFocusOutline: undefined,\n    listInactiveFocusOutline: undefined,\n    listSelectionOutline: undefined,\n    listHoverOutline: undefined,\n    treeStickyScrollBackground: undefined,\n    treeStickyScrollBorder: undefined,\n    treeStickyScrollShadow: undefined\n};\nconst DefaultOptions = {\n    keyboardSupport: true,\n    mouseSupport: true,\n    multipleSelectionSupport: true,\n    dnd: {\n        getDragURI() { return null; },\n        onDragStart() { },\n        onDragOver() { return false; },\n        drop() { },\n        dispose() { }\n    }\n};\n// TODO@Joao: move these utils into a SortedArray class\nfunction getContiguousRangeContaining(range, value) {\n    const index = range.indexOf(value);\n    if (index === -1) {\n        return [];\n    }\n    const result = [];\n    let i = index - 1;\n    while (i >= 0 && range[i] === value - (index - i)) {\n        result.push(range[i--]);\n    }\n    result.reverse();\n    i = index;\n    while (i < range.length && range[i] === value + (i - index)) {\n        result.push(range[i++]);\n    }\n    return result;\n}\n/**\n * Given two sorted collections of numbers, returns the intersection\n * between them (OR).\n */\nfunction disjunction(one, other) {\n    const result = [];\n    let i = 0, j = 0;\n    while (i < one.length || j < other.length) {\n        if (i >= one.length) {\n            result.push(other[j++]);\n        }\n        else if (j >= other.length) {\n            result.push(one[i++]);\n        }\n        else if (one[i] === other[j]) {\n            result.push(one[i]);\n            i++;\n            j++;\n            continue;\n        }\n        else if (one[i] < other[j]) {\n            result.push(one[i++]);\n        }\n        else {\n            result.push(other[j++]);\n        }\n    }\n    return result;\n}\n/**\n * Given two sorted collections of numbers, returns the relative\n * complement between them (XOR).\n */\nfunction relativeComplement(one, other) {\n    const result = [];\n    let i = 0, j = 0;\n    while (i < one.length || j < other.length) {\n        if (i >= one.length) {\n            result.push(other[j++]);\n        }\n        else if (j >= other.length) {\n            result.push(one[i++]);\n        }\n        else if (one[i] === other[j]) {\n            i++;\n            j++;\n            continue;\n        }\n        else if (one[i] < other[j]) {\n            result.push(one[i++]);\n        }\n        else {\n            j++;\n        }\n    }\n    return result;\n}\nconst numericSort = (a, b) => a - b;\nclass PipelineRenderer {\n    constructor(_templateId, renderers) {\n        this._templateId = _templateId;\n        this.renderers = renderers;\n    }\n    get templateId() {\n        return this._templateId;\n    }\n    renderTemplate(container) {\n        return this.renderers.map(r => r.renderTemplate(container));\n    }\n    renderElement(element, index, templateData, height) {\n        let i = 0;\n        for (const renderer of this.renderers) {\n            renderer.renderElement(element, index, templateData[i++], height);\n        }\n    }\n    disposeElement(element, index, templateData, height) {\n        let i = 0;\n        for (const renderer of this.renderers) {\n            renderer.disposeElement?.(element, index, templateData[i], height);\n            i += 1;\n        }\n    }\n    disposeTemplate(templateData) {\n        let i = 0;\n        for (const renderer of this.renderers) {\n            renderer.disposeTemplate(templateData[i++]);\n        }\n    }\n}\nclass AccessibiltyRenderer {\n    constructor(accessibilityProvider) {\n        this.accessibilityProvider = accessibilityProvider;\n        this.templateId = 'a18n';\n    }\n    renderTemplate(container) {\n        return { container, disposables: new DisposableStore() };\n    }\n    renderElement(element, index, data) {\n        const ariaLabel = this.accessibilityProvider.getAriaLabel(element);\n        const observable = (ariaLabel && typeof ariaLabel !== 'string') ? ariaLabel : constObservable(ariaLabel);\n        data.disposables.add(autorun(reader => {\n            this.setAriaLabel(reader.readObservable(observable), data.container);\n        }));\n        const ariaLevel = this.accessibilityProvider.getAriaLevel && this.accessibilityProvider.getAriaLevel(element);\n        if (typeof ariaLevel === 'number') {\n            data.container.setAttribute('aria-level', `${ariaLevel}`);\n        }\n        else {\n            data.container.removeAttribute('aria-level');\n        }\n    }\n    setAriaLabel(ariaLabel, element) {\n        if (ariaLabel) {\n            element.setAttribute('aria-label', ariaLabel);\n        }\n        else {\n            element.removeAttribute('aria-label');\n        }\n    }\n    disposeElement(element, index, templateData, height) {\n        templateData.disposables.clear();\n    }\n    disposeTemplate(templateData) {\n        templateData.disposables.dispose();\n    }\n}\nclass ListViewDragAndDrop {\n    constructor(list, dnd) {\n        this.list = list;\n        this.dnd = dnd;\n    }\n    getDragElements(element) {\n        const selection = this.list.getSelectedElements();\n        const elements = selection.indexOf(element) > -1 ? selection : [element];\n        return elements;\n    }\n    getDragURI(element) {\n        return this.dnd.getDragURI(element);\n    }\n    getDragLabel(elements, originalEvent) {\n        if (this.dnd.getDragLabel) {\n            return this.dnd.getDragLabel(elements, originalEvent);\n        }\n        return undefined;\n    }\n    onDragStart(data, originalEvent) {\n        this.dnd.onDragStart?.(data, originalEvent);\n    }\n    onDragOver(data, targetElement, targetIndex, targetSector, originalEvent) {\n        return this.dnd.onDragOver(data, targetElement, targetIndex, targetSector, originalEvent);\n    }\n    onDragLeave(data, targetElement, targetIndex, originalEvent) {\n        this.dnd.onDragLeave?.(data, targetElement, targetIndex, originalEvent);\n    }\n    onDragEnd(originalEvent) {\n        this.dnd.onDragEnd?.(originalEvent);\n    }\n    drop(data, targetElement, targetIndex, targetSector, originalEvent) {\n        this.dnd.drop(data, targetElement, targetIndex, targetSector, originalEvent);\n    }\n    dispose() {\n        this.dnd.dispose();\n    }\n}\n/**\n * The {@link List} is a virtual scrolling widget, built on top of the {@link ListView}\n * widget.\n *\n * Features:\n * - Customizable keyboard and mouse support\n * - Element traits: focus, selection, achor\n * - Accessibility support\n * - Touch support\n * - Performant template-based rendering\n * - Horizontal scrolling\n * - Variable element height support\n * - Dynamic element height support\n * - Drag-and-drop support\n */\nexport class List {\n    get onDidChangeFocus() {\n        return Event.map(this.eventBufferer.wrapEvent(this.focus.onChange), e => this.toListEvent(e), this.disposables);\n    }\n    get onDidChangeSelection() {\n        return Event.map(this.eventBufferer.wrapEvent(this.selection.onChange), e => this.toListEvent(e), this.disposables);\n    }\n    get domId() { return this.view.domId; }\n    get onDidScroll() { return this.view.onDidScroll; }\n    get onMouseClick() { return this.view.onMouseClick; }\n    get onMouseDblClick() { return this.view.onMouseDblClick; }\n    get onMouseMiddleClick() { return this.view.onMouseMiddleClick; }\n    get onPointer() { return this.mouseController.onPointer; }\n    get onMouseDown() { return this.view.onMouseDown; }\n    get onMouseOver() { return this.view.onMouseOver; }\n    get onMouseOut() { return this.view.onMouseOut; }\n    get onTouchStart() { return this.view.onTouchStart; }\n    get onTap() { return this.view.onTap; }\n    /**\n     * Possible context menu trigger events:\n     * - ContextMenu key\n     * - Shift F10\n     * - Ctrl Option Shift M (macOS with VoiceOver)\n     * - Mouse right click\n     */\n    get onContextMenu() {\n        let didJustPressContextMenuKey = false;\n        const fromKeyDown = Event.chain(this.disposables.add(new DomEmitter(this.view.domNode, 'keydown')).event, $ => $.map(e => new StandardKeyboardEvent(e))\n            .filter(e => didJustPressContextMenuKey = e.keyCode === 58 /* KeyCode.ContextMenu */ || (e.shiftKey && e.keyCode === 68 /* KeyCode.F10 */))\n            .map(e => EventHelper.stop(e, true))\n            .filter(() => false));\n        const fromKeyUp = Event.chain(this.disposables.add(new DomEmitter(this.view.domNode, 'keyup')).event, $ => $.forEach(() => didJustPressContextMenuKey = false)\n            .map(e => new StandardKeyboardEvent(e))\n            .filter(e => e.keyCode === 58 /* KeyCode.ContextMenu */ || (e.shiftKey && e.keyCode === 68 /* KeyCode.F10 */))\n            .map(e => EventHelper.stop(e, true))\n            .map(({ browserEvent }) => {\n            const focus = this.getFocus();\n            const index = focus.length ? focus[0] : undefined;\n            const element = typeof index !== 'undefined' ? this.view.element(index) : undefined;\n            const anchor = typeof index !== 'undefined' ? this.view.domElement(index) : this.view.domNode;\n            return { index, element, anchor, browserEvent };\n        }));\n        const fromMouse = Event.chain(this.view.onContextMenu, $ => $.filter(_ => !didJustPressContextMenuKey)\n            .map(({ element, index, browserEvent }) => ({ element, index, anchor: new StandardMouseEvent(getWindow(this.view.domNode), browserEvent), browserEvent })));\n        return Event.any(fromKeyDown, fromKeyUp, fromMouse);\n    }\n    get onKeyDown() { return this.disposables.add(new DomEmitter(this.view.domNode, 'keydown')).event; }\n    get onDidFocus() { return Event.signal(this.disposables.add(new DomEmitter(this.view.domNode, 'focus', true)).event); }\n    get onDidBlur() { return Event.signal(this.disposables.add(new DomEmitter(this.view.domNode, 'blur', true)).event); }\n    constructor(user, container, virtualDelegate, renderers, _options = DefaultOptions) {\n        this.user = user;\n        this._options = _options;\n        this.focus = new Trait('focused');\n        this.anchor = new Trait('anchor');\n        this.eventBufferer = new EventBufferer();\n        this._ariaLabel = '';\n        this.disposables = new DisposableStore();\n        this._onDidDispose = new Emitter();\n        this.onDidDispose = this._onDidDispose.event;\n        const role = this._options.accessibilityProvider && this._options.accessibilityProvider.getWidgetRole ? this._options.accessibilityProvider?.getWidgetRole() : 'list';\n        this.selection = new SelectionTrait(role !== 'listbox');\n        const baseRenderers = [this.focus.renderer, this.selection.renderer];\n        this.accessibilityProvider = _options.accessibilityProvider;\n        if (this.accessibilityProvider) {\n            baseRenderers.push(new AccessibiltyRenderer(this.accessibilityProvider));\n            this.accessibilityProvider.onDidChangeActiveDescendant?.(this.onDidChangeActiveDescendant, this, this.disposables);\n        }\n        renderers = renderers.map(r => new PipelineRenderer(r.templateId, [...baseRenderers, r]));\n        const viewOptions = {\n            ..._options,\n            dnd: _options.dnd && new ListViewDragAndDrop(this, _options.dnd)\n        };\n        this.view = this.createListView(container, virtualDelegate, renderers, viewOptions);\n        this.view.domNode.setAttribute('role', role);\n        if (_options.styleController) {\n            this.styleController = _options.styleController(this.view.domId);\n        }\n        else {\n            const styleElement = createStyleSheet(this.view.domNode);\n            this.styleController = new DefaultStyleController(styleElement, this.view.domId);\n        }\n        this.spliceable = new CombinedSpliceable([\n            new TraitSpliceable(this.focus, this.view, _options.identityProvider),\n            new TraitSpliceable(this.selection, this.view, _options.identityProvider),\n            new TraitSpliceable(this.anchor, this.view, _options.identityProvider),\n            this.view\n        ]);\n        this.disposables.add(this.focus);\n        this.disposables.add(this.selection);\n        this.disposables.add(this.anchor);\n        this.disposables.add(this.view);\n        this.disposables.add(this._onDidDispose);\n        this.disposables.add(new DOMFocusController(this, this.view));\n        if (typeof _options.keyboardSupport !== 'boolean' || _options.keyboardSupport) {\n            this.keyboardController = new KeyboardController(this, this.view, _options);\n            this.disposables.add(this.keyboardController);\n        }\n        if (_options.keyboardNavigationLabelProvider) {\n            const delegate = _options.keyboardNavigationDelegate || DefaultKeyboardNavigationDelegate;\n            this.typeNavigationController = new TypeNavigationController(this, this.view, _options.keyboardNavigationLabelProvider, _options.keyboardNavigationEventFilter ?? (() => true), delegate);\n            this.disposables.add(this.typeNavigationController);\n        }\n        this.mouseController = this.createMouseController(_options);\n        this.disposables.add(this.mouseController);\n        this.onDidChangeFocus(this._onFocusChange, this, this.disposables);\n        this.onDidChangeSelection(this._onSelectionChange, this, this.disposables);\n        if (this.accessibilityProvider) {\n            this.ariaLabel = this.accessibilityProvider.getWidgetAriaLabel();\n        }\n        if (this._options.multipleSelectionSupport !== false) {\n            this.view.domNode.setAttribute('aria-multiselectable', 'true');\n        }\n    }\n    createListView(container, virtualDelegate, renderers, viewOptions) {\n        return new ListView(container, virtualDelegate, renderers, viewOptions);\n    }\n    createMouseController(options) {\n        return new MouseController(this);\n    }\n    updateOptions(optionsUpdate = {}) {\n        this._options = { ...this._options, ...optionsUpdate };\n        this.typeNavigationController?.updateOptions(this._options);\n        if (this._options.multipleSelectionController !== undefined) {\n            if (this._options.multipleSelectionSupport) {\n                this.view.domNode.setAttribute('aria-multiselectable', 'true');\n            }\n            else {\n                this.view.domNode.removeAttribute('aria-multiselectable');\n            }\n        }\n        this.mouseController.updateOptions(optionsUpdate);\n        this.keyboardController?.updateOptions(optionsUpdate);\n        this.view.updateOptions(optionsUpdate);\n    }\n    get options() {\n        return this._options;\n    }\n    splice(start, deleteCount, elements = []) {\n        if (start < 0 || start > this.view.length) {\n            throw new ListError(this.user, `Invalid start index: ${start}`);\n        }\n        if (deleteCount < 0) {\n            throw new ListError(this.user, `Invalid delete count: ${deleteCount}`);\n        }\n        if (deleteCount === 0 && elements.length === 0) {\n            return;\n        }\n        this.eventBufferer.bufferEvents(() => this.spliceable.splice(start, deleteCount, elements));\n    }\n    rerender() {\n        this.view.rerender();\n    }\n    element(index) {\n        return this.view.element(index);\n    }\n    indexOf(element) {\n        return this.view.indexOf(element);\n    }\n    indexAt(position) {\n        return this.view.indexAt(position);\n    }\n    get length() {\n        return this.view.length;\n    }\n    get contentHeight() {\n        return this.view.contentHeight;\n    }\n    get onDidChangeContentHeight() {\n        return this.view.onDidChangeContentHeight;\n    }\n    get scrollTop() {\n        return this.view.getScrollTop();\n    }\n    set scrollTop(scrollTop) {\n        this.view.setScrollTop(scrollTop);\n    }\n    get scrollHeight() {\n        return this.view.scrollHeight;\n    }\n    get renderHeight() {\n        return this.view.renderHeight;\n    }\n    get firstVisibleIndex() {\n        return this.view.firstVisibleIndex;\n    }\n    get ariaLabel() {\n        return this._ariaLabel;\n    }\n    set ariaLabel(value) {\n        this._ariaLabel = value;\n        this.view.domNode.setAttribute('aria-label', value);\n    }\n    domFocus() {\n        this.view.domNode.focus({ preventScroll: true });\n    }\n    layout(height, width) {\n        this.view.layout(height, width);\n    }\n    setSelection(indexes, browserEvent) {\n        for (const index of indexes) {\n            if (index < 0 || index >= this.length) {\n                throw new ListError(this.user, `Invalid index ${index}`);\n            }\n        }\n        this.selection.set(indexes, browserEvent);\n    }\n    getSelection() {\n        return this.selection.get();\n    }\n    getSelectedElements() {\n        return this.getSelection().map(i => this.view.element(i));\n    }\n    setAnchor(index) {\n        if (typeof index === 'undefined') {\n            this.anchor.set([]);\n            return;\n        }\n        if (index < 0 || index >= this.length) {\n            throw new ListError(this.user, `Invalid index ${index}`);\n        }\n        this.anchor.set([index]);\n    }\n    getAnchor() {\n        return firstOrDefault(this.anchor.get(), undefined);\n    }\n    getAnchorElement() {\n        const anchor = this.getAnchor();\n        return typeof anchor === 'undefined' ? undefined : this.element(anchor);\n    }\n    setFocus(indexes, browserEvent) {\n        for (const index of indexes) {\n            if (index < 0 || index >= this.length) {\n                throw new ListError(this.user, `Invalid index ${index}`);\n            }\n        }\n        this.focus.set(indexes, browserEvent);\n    }\n    focusNext(n = 1, loop = false, browserEvent, filter) {\n        if (this.length === 0) {\n            return;\n        }\n        const focus = this.focus.get();\n        const index = this.findNextIndex(focus.length > 0 ? focus[0] + n : 0, loop, filter);\n        if (index > -1) {\n            this.setFocus([index], browserEvent);\n        }\n    }\n    focusPrevious(n = 1, loop = false, browserEvent, filter) {\n        if (this.length === 0) {\n            return;\n        }\n        const focus = this.focus.get();\n        const index = this.findPreviousIndex(focus.length > 0 ? focus[0] - n : 0, loop, filter);\n        if (index > -1) {\n            this.setFocus([index], browserEvent);\n        }\n    }\n    async focusNextPage(browserEvent, filter) {\n        let lastPageIndex = this.view.indexAt(this.view.getScrollTop() + this.view.renderHeight);\n        lastPageIndex = lastPageIndex === 0 ? 0 : lastPageIndex - 1;\n        const currentlyFocusedElementIndex = this.getFocus()[0];\n        if (currentlyFocusedElementIndex !== lastPageIndex && (currentlyFocusedElementIndex === undefined || lastPageIndex > currentlyFocusedElementIndex)) {\n            const lastGoodPageIndex = this.findPreviousIndex(lastPageIndex, false, filter);\n            if (lastGoodPageIndex > -1 && currentlyFocusedElementIndex !== lastGoodPageIndex) {\n                this.setFocus([lastGoodPageIndex], browserEvent);\n            }\n            else {\n                this.setFocus([lastPageIndex], browserEvent);\n            }\n        }\n        else {\n            const previousScrollTop = this.view.getScrollTop();\n            let nextpageScrollTop = previousScrollTop + this.view.renderHeight;\n            if (lastPageIndex > currentlyFocusedElementIndex) {\n                // scroll last page element to the top only if the last page element is below the focused element\n                nextpageScrollTop -= this.view.elementHeight(lastPageIndex);\n            }\n            this.view.setScrollTop(nextpageScrollTop);\n            if (this.view.getScrollTop() !== previousScrollTop) {\n                this.setFocus([]);\n                // Let the scroll event listener run\n                await timeout(0);\n                await this.focusNextPage(browserEvent, filter);\n            }\n        }\n    }\n    async focusPreviousPage(browserEvent, filter, getPaddingTop = () => 0) {\n        let firstPageIndex;\n        const paddingTop = getPaddingTop();\n        const scrollTop = this.view.getScrollTop() + paddingTop;\n        if (scrollTop === 0) {\n            firstPageIndex = this.view.indexAt(scrollTop);\n        }\n        else {\n            firstPageIndex = this.view.indexAfter(scrollTop - 1);\n        }\n        const currentlyFocusedElementIndex = this.getFocus()[0];\n        if (currentlyFocusedElementIndex !== firstPageIndex && (currentlyFocusedElementIndex === undefined || currentlyFocusedElementIndex >= firstPageIndex)) {\n            const firstGoodPageIndex = this.findNextIndex(firstPageIndex, false, filter);\n            if (firstGoodPageIndex > -1 && currentlyFocusedElementIndex !== firstGoodPageIndex) {\n                this.setFocus([firstGoodPageIndex], browserEvent);\n            }\n            else {\n                this.setFocus([firstPageIndex], browserEvent);\n            }\n        }\n        else {\n            const previousScrollTop = scrollTop;\n            this.view.setScrollTop(scrollTop - this.view.renderHeight - paddingTop);\n            if (this.view.getScrollTop() + getPaddingTop() !== previousScrollTop) {\n                this.setFocus([]);\n                // Let the scroll event listener run\n                await timeout(0);\n                await this.focusPreviousPage(browserEvent, filter, getPaddingTop);\n            }\n        }\n    }\n    focusLast(browserEvent, filter) {\n        if (this.length === 0) {\n            return;\n        }\n        const index = this.findPreviousIndex(this.length - 1, false, filter);\n        if (index > -1) {\n            this.setFocus([index], browserEvent);\n        }\n    }\n    focusFirst(browserEvent, filter) {\n        this.focusNth(0, browserEvent, filter);\n    }\n    focusNth(n, browserEvent, filter) {\n        if (this.length === 0) {\n            return;\n        }\n        const index = this.findNextIndex(n, false, filter);\n        if (index > -1) {\n            this.setFocus([index], browserEvent);\n        }\n    }\n    findNextIndex(index, loop = false, filter) {\n        for (let i = 0; i < this.length; i++) {\n            if (index >= this.length && !loop) {\n                return -1;\n            }\n            index = index % this.length;\n            if (!filter || filter(this.element(index))) {\n                return index;\n            }\n            index++;\n        }\n        return -1;\n    }\n    findPreviousIndex(index, loop = false, filter) {\n        for (let i = 0; i < this.length; i++) {\n            if (index < 0 && !loop) {\n                return -1;\n            }\n            index = (this.length + (index % this.length)) % this.length;\n            if (!filter || filter(this.element(index))) {\n                return index;\n            }\n            index--;\n        }\n        return -1;\n    }\n    getFocus() {\n        return this.focus.get();\n    }\n    getFocusedElements() {\n        return this.getFocus().map(i => this.view.element(i));\n    }\n    reveal(index, relativeTop, paddingTop = 0) {\n        if (index < 0 || index >= this.length) {\n            throw new ListError(this.user, `Invalid index ${index}`);\n        }\n        const scrollTop = this.view.getScrollTop();\n        const elementTop = this.view.elementTop(index);\n        const elementHeight = this.view.elementHeight(index);\n        if (isNumber(relativeTop)) {\n            // y = mx + b\n            const m = elementHeight - this.view.renderHeight + paddingTop;\n            this.view.setScrollTop(m * clamp(relativeTop, 0, 1) + elementTop - paddingTop);\n        }\n        else {\n            const viewItemBottom = elementTop + elementHeight;\n            const scrollBottom = scrollTop + this.view.renderHeight;\n            if (elementTop < scrollTop + paddingTop && viewItemBottom >= scrollBottom) {\n                // The element is already overflowing the viewport, no-op\n            }\n            else if (elementTop < scrollTop + paddingTop || (viewItemBottom >= scrollBottom && elementHeight >= this.view.renderHeight)) {\n                this.view.setScrollTop(elementTop - paddingTop);\n            }\n            else if (viewItemBottom >= scrollBottom) {\n                this.view.setScrollTop(viewItemBottom - this.view.renderHeight);\n            }\n        }\n    }\n    /**\n     * Returns the relative position of an element rendered in the list.\n     * Returns `null` if the element isn't *entirely* in the visible viewport.\n     */\n    getRelativeTop(index, paddingTop = 0) {\n        if (index < 0 || index >= this.length) {\n            throw new ListError(this.user, `Invalid index ${index}`);\n        }\n        const scrollTop = this.view.getScrollTop();\n        const elementTop = this.view.elementTop(index);\n        const elementHeight = this.view.elementHeight(index);\n        if (elementTop < scrollTop + paddingTop || elementTop + elementHeight > scrollTop + this.view.renderHeight) {\n            return null;\n        }\n        // y = mx + b\n        const m = elementHeight - this.view.renderHeight + paddingTop;\n        return Math.abs((scrollTop + paddingTop - elementTop) / m);\n    }\n    getHTMLElement() {\n        return this.view.domNode;\n    }\n    getScrollableElement() {\n        return this.view.scrollableElementDomNode;\n    }\n    getElementID(index) {\n        return this.view.getElementDomId(index);\n    }\n    getElementTop(index) {\n        return this.view.elementTop(index);\n    }\n    style(styles) {\n        this.styleController.style(styles);\n    }\n    toListEvent({ indexes, browserEvent }) {\n        return { indexes, elements: indexes.map(i => this.view.element(i)), browserEvent };\n    }\n    _onFocusChange() {\n        const focus = this.focus.get();\n        this.view.domNode.classList.toggle('element-focused', focus.length > 0);\n        this.onDidChangeActiveDescendant();\n    }\n    onDidChangeActiveDescendant() {\n        const focus = this.focus.get();\n        if (focus.length > 0) {\n            let id;\n            if (this.accessibilityProvider?.getActiveDescendantId) {\n                id = this.accessibilityProvider.getActiveDescendantId(this.view.element(focus[0]));\n            }\n            this.view.domNode.setAttribute('aria-activedescendant', id || this.view.getElementDomId(focus[0]));\n        }\n        else {\n            this.view.domNode.removeAttribute('aria-activedescendant');\n        }\n    }\n    _onSelectionChange() {\n        const selection = this.selection.get();\n        this.view.domNode.classList.toggle('selection-none', selection.length === 0);\n        this.view.domNode.classList.toggle('selection-single', selection.length === 1);\n        this.view.domNode.classList.toggle('selection-multiple', selection.length > 1);\n    }\n    dispose() {\n        this._onDidDispose.fire();\n        this.disposables.dispose();\n        this._onDidDispose.dispose();\n    }\n}\n__decorate([\n    memoize\n], List.prototype, \"onDidChangeFocus\", null);\n__decorate([\n    memoize\n], List.prototype, \"onDidChangeSelection\", null);\n__decorate([\n    memoize\n], List.prototype, \"onContextMenu\", null);\n__decorate([\n    memoize\n], List.prototype, \"onKeyDown\", null);\n__decorate([\n    memoize\n], List.prototype, \"onDidFocus\", null);\n__decorate([\n    memoize\n], List.prototype, \"onDidBlur\", null);\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA,IAAIA,UAAU,GAAI,IAAI,IAAI,IAAI,CAACA,UAAU,IAAK,UAAUC,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAE;EACnF,IAAIC,CAAC,GAAGC,SAAS,CAACC,MAAM;IAAEC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGH,MAAM,GAAGE,IAAI,KAAK,IAAI,GAAGA,IAAI,GAAGK,MAAM,CAACC,wBAAwB,CAACR,MAAM,EAAEC,GAAG,CAAC,GAAGC,IAAI;IAAEO,CAAC;EAC5H,IAAI,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,CAACC,QAAQ,KAAK,UAAU,EAAEL,CAAC,GAAGI,OAAO,CAACC,QAAQ,CAACZ,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,CAAC,CAAC,KAC1H,KAAK,IAAIU,CAAC,GAAGb,UAAU,CAACM,MAAM,GAAG,CAAC,EAAEO,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE,IAAIH,CAAC,GAAGV,UAAU,CAACa,CAAC,CAAC,EAAEN,CAAC,GAAG,CAACH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACH,CAAC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACT,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,GAAGG,CAAC,CAACT,MAAM,EAAEC,GAAG,CAAC,KAAKK,CAAC;EACjJ,OAAOH,CAAC,GAAG,CAAC,IAAIG,CAAC,IAAIC,MAAM,CAACM,cAAc,CAACb,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,EAAEA,CAAC;AACjE,CAAC;AACD,SAASQ,qBAAqB,EAAEC,gBAAgB,EAAEC,WAAW,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,aAAa,EAAEC,YAAY,QAAQ,cAAc;AAC7I,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,SAASC,qBAAqB,QAAQ,wBAAwB;AAC9D,SAASC,OAAO,QAAQ,gBAAgB;AACxC,SAASC,KAAK,QAAQ,iBAAiB;AACvC,SAASC,kBAAkB,QAAQ,aAAa;AAChD,SAASC,YAAY,EAAEC,cAAc,EAAEC,KAAK,QAAQ,2BAA2B;AAC/E,SAASC,OAAO,QAAQ,0BAA0B;AAClD,SAASC,KAAK,QAAQ,0BAA0B;AAChD,SAASC,OAAO,QAAQ,+BAA+B;AACvD,SAASC,OAAO,EAAEC,KAAK,EAAEC,aAAa,QAAQ,0BAA0B;AACxE,SAASC,aAAa,EAAEC,aAAa,QAAQ,4BAA4B;AACzE,SAASC,eAAe,EAAEC,OAAO,QAAQ,8BAA8B;AACvE,SAASC,KAAK,QAAQ,4BAA4B;AAClD,OAAO,KAAKC,QAAQ,MAAM,6BAA6B;AACvD,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,OAAO,YAAY;AACnB,SAASC,SAAS,QAAQ,WAAW;AACrC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,kBAAkB,QAAQ,qBAAqB;AACxD,SAASC,OAAO,EAAEC,eAAe,QAAQ,+BAA+B;AACxE,MAAMC,aAAa,CAAC;EAChBC,WAAWA,CAACC,KAAK,EAAE;IACf,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,gBAAgB,GAAG,EAAE;EAC9B;EACA,IAAIC,UAAUA,CAAA,EAAG;IACb,OAAO,YAAY,IAAI,CAACF,KAAK,CAACG,IAAI,EAAE;EACxC;EACAC,cAAcA,CAACC,SAAS,EAAE;IACtB,OAAOA,SAAS;EACpB;EACAC,aAAaA,CAACC,OAAO,EAAEC,KAAK,EAAEC,YAAY,EAAE;IACxC,MAAMC,oBAAoB,GAAG,IAAI,CAACT,gBAAgB,CAACU,SAAS,CAACC,EAAE,IAAIA,EAAE,CAACH,YAAY,KAAKA,YAAY,CAAC;IACpG,IAAIC,oBAAoB,IAAI,CAAC,EAAE;MAC3B,MAAMG,QAAQ,GAAG,IAAI,CAACZ,gBAAgB,CAACS,oBAAoB,CAAC;MAC5D,IAAI,CAACV,KAAK,CAACc,QAAQ,CAACL,YAAY,CAAC;MACjCI,QAAQ,CAACL,KAAK,GAAGA,KAAK;IAC1B,CAAC,MACI;MACD,MAAMK,QAAQ,GAAG;QAAEL,KAAK;QAAEC;MAAa,CAAC;MACxC,IAAI,CAACR,gBAAgB,CAACc,IAAI,CAACF,QAAQ,CAAC;IACxC;IACA,IAAI,CAACb,KAAK,CAACgB,WAAW,CAACR,KAAK,EAAEC,YAAY,CAAC;EAC/C;EACAQ,MAAMA,CAACC,KAAK,EAAEC,WAAW,EAAEC,WAAW,EAAE;IACpC,MAAMP,QAAQ,GAAG,EAAE;IACnB,KAAK,MAAMQ,eAAe,IAAI,IAAI,CAACpB,gBAAgB,EAAE;MACjD,IAAIoB,eAAe,CAACb,KAAK,GAAGU,KAAK,EAAE;QAC/BL,QAAQ,CAACE,IAAI,CAACM,eAAe,CAAC;MAClC,CAAC,MACI,IAAIA,eAAe,CAACb,KAAK,IAAIU,KAAK,GAAGC,WAAW,EAAE;QACnDN,QAAQ,CAACE,IAAI,CAAC;UACVP,KAAK,EAAEa,eAAe,CAACb,KAAK,GAAGY,WAAW,GAAGD,WAAW;UACxDV,YAAY,EAAEY,eAAe,CAACZ;QAClC,CAAC,CAAC;MACN;IACJ;IACA,IAAI,CAACR,gBAAgB,GAAGY,QAAQ;EACpC;EACAS,aAAaA,CAACC,OAAO,EAAE;IACnB,KAAK,MAAM;MAAEf,KAAK;MAAEC;IAAa,CAAC,IAAI,IAAI,CAACR,gBAAgB,EAAE;MACzD,IAAIsB,OAAO,CAACC,OAAO,CAAChB,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE;QAC7B,IAAI,CAACR,KAAK,CAACgB,WAAW,CAACR,KAAK,EAAEC,YAAY,CAAC;MAC/C;IACJ;EACJ;EACAgB,eAAeA,CAAChB,YAAY,EAAE;IAC1B,MAAMD,KAAK,GAAG,IAAI,CAACP,gBAAgB,CAACU,SAAS,CAACC,EAAE,IAAIA,EAAE,CAACH,YAAY,KAAKA,YAAY,CAAC;IACrF,IAAID,KAAK,GAAG,CAAC,EAAE;MACX;IACJ;IACA,IAAI,CAACP,gBAAgB,CAACgB,MAAM,CAACT,KAAK,EAAE,CAAC,CAAC;EAC1C;AACJ;AACA,MAAMkB,KAAK,CAAC;EACR,IAAIvB,IAAIA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACwB,MAAM;EAAE;EACjC,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI9B,aAAa,CAAC,IAAI,CAAC;EAClC;EACAC,WAAWA,CAAC4B,MAAM,EAAE;IAChB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACJ,OAAO,GAAG,EAAE;IACjB,IAAI,CAACM,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,SAAS,GAAG,IAAI/C,OAAO,CAAC,CAAC;IAC9B,IAAI,CAACgD,QAAQ,GAAG,IAAI,CAACD,SAAS,CAACE,KAAK;EACxC;EACAf,MAAMA,CAACC,KAAK,EAAEC,WAAW,EAAEc,QAAQ,EAAE;IACjC,MAAMC,IAAI,GAAGD,QAAQ,CAAC7E,MAAM,GAAG+D,WAAW;IAC1C,MAAMgB,GAAG,GAAGjB,KAAK,GAAGC,WAAW;IAC/B,MAAMU,aAAa,GAAG,EAAE;IACxB,IAAIlE,CAAC,GAAG,CAAC;IACT,OAAOA,CAAC,GAAG,IAAI,CAACkE,aAAa,CAACzE,MAAM,IAAI,IAAI,CAACyE,aAAa,CAAClE,CAAC,CAAC,GAAGuD,KAAK,EAAE;MACnEW,aAAa,CAACd,IAAI,CAAC,IAAI,CAACc,aAAa,CAAClE,CAAC,EAAE,CAAC,CAAC;IAC/C;IACA,KAAK,IAAIyE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,QAAQ,CAAC7E,MAAM,EAAEgF,CAAC,EAAE,EAAE;MACtC,IAAIH,QAAQ,CAACG,CAAC,CAAC,EAAE;QACbP,aAAa,CAACd,IAAI,CAACqB,CAAC,GAAGlB,KAAK,CAAC;MACjC;IACJ;IACA,OAAOvD,CAAC,GAAG,IAAI,CAACkE,aAAa,CAACzE,MAAM,IAAI,IAAI,CAACyE,aAAa,CAAClE,CAAC,CAAC,IAAIwE,GAAG,EAAE;MAClEN,aAAa,CAACd,IAAI,CAAC,IAAI,CAACc,aAAa,CAAClE,CAAC,EAAE,CAAC,GAAGuE,IAAI,CAAC;IACtD;IACA,IAAI,CAACN,QAAQ,CAACX,MAAM,CAACC,KAAK,EAAEC,WAAW,EAAEc,QAAQ,CAAC7E,MAAM,CAAC;IACzD,IAAI,CAACiF,IAAI,CAACR,aAAa,EAAEA,aAAa,CAAC;EAC3C;EACAb,WAAWA,CAACR,KAAK,EAAEH,SAAS,EAAE;IAC1BA,SAAS,CAACiC,SAAS,CAACC,MAAM,CAAC,IAAI,CAACZ,MAAM,EAAE,IAAI,CAACa,QAAQ,CAAChC,KAAK,CAAC,CAAC;EACjE;EACAM,QAAQA,CAACT,SAAS,EAAE;IAChBA,SAAS,CAACiC,SAAS,CAACG,MAAM,CAAC,IAAI,CAACd,MAAM,CAAC;EAC3C;EACA;AACJ;AACA;AACA;AACA;AACA;EACIe,GAAGA,CAACnB,OAAO,EAAEoB,YAAY,EAAE;IACvB,OAAO,IAAI,CAACN,IAAI,CAACd,OAAO,EAAE,CAAC,GAAGA,OAAO,CAAC,CAACqB,IAAI,CAACC,WAAW,CAAC,EAAEF,YAAY,CAAC;EAC3E;EACAN,IAAIA,CAACd,OAAO,EAAEM,aAAa,EAAEc,YAAY,EAAE;IACvC,MAAMG,MAAM,GAAG,IAAI,CAACvB,OAAO;IAC3B,MAAMwB,YAAY,GAAG,IAAI,CAAClB,aAAa;IACvC,IAAI,CAACN,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACM,aAAa,GAAGA,aAAa;IAClC,MAAMmB,QAAQ,GAAGC,WAAW,CAACF,YAAY,EAAExB,OAAO,CAAC;IACnD,IAAI,CAACK,QAAQ,CAACN,aAAa,CAAC0B,QAAQ,CAAC;IACrC,IAAI,CAAClB,SAAS,CAACoB,IAAI,CAAC;MAAE3B,OAAO;MAAEoB;IAAa,CAAC,CAAC;IAC9C,OAAOG,MAAM;EACjB;EACAK,GAAGA,CAAA,EAAG;IACF,OAAO,IAAI,CAAC5B,OAAO;EACvB;EACAiB,QAAQA,CAAChC,KAAK,EAAE;IACZ,OAAO/B,YAAY,CAAC,IAAI,CAACoD,aAAa,EAAErB,KAAK,EAAEqC,WAAW,CAAC,IAAI,CAAC;EACpE;EACAxD,OAAOA,CAAA,EAAG;IACNA,OAAO,CAAC,IAAI,CAACyC,SAAS,CAAC;EAC3B;AACJ;AACAjF,UAAU,CAAC,CACPiC,OAAO,CACV,EAAE4C,KAAK,CAAC0B,SAAS,EAAE,UAAU,EAAE,IAAI,CAAC;AACrC,MAAMC,cAAc,SAAS3B,KAAK,CAAC;EAC/B3B,WAAWA,CAACuD,eAAe,EAAE;IACzB,KAAK,CAAC,UAAU,CAAC;IACjB,IAAI,CAACA,eAAe,GAAGA,eAAe;EAC1C;EACAtC,WAAWA,CAACR,KAAK,EAAEH,SAAS,EAAE;IAC1B,KAAK,CAACW,WAAW,CAACR,KAAK,EAAEH,SAAS,CAAC;IACnC,IAAI,IAAI,CAACiD,eAAe,EAAE;MACtB,IAAI,IAAI,CAACd,QAAQ,CAAChC,KAAK,CAAC,EAAE;QACtBH,SAAS,CAACkD,YAAY,CAAC,eAAe,EAAE,MAAM,CAAC;MACnD,CAAC,MACI;QACDlD,SAAS,CAACkD,YAAY,CAAC,eAAe,EAAE,OAAO,CAAC;MACpD;IACJ;EACJ;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,CAAC;EAClBzD,WAAWA,CAACC,KAAK,EAAEyD,IAAI,EAAEC,gBAAgB,EAAE;IACvC,IAAI,CAAC1D,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACyD,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;EAC5C;EACAzC,MAAMA,CAACC,KAAK,EAAEC,WAAW,EAAEc,QAAQ,EAAE;IACjC,IAAI,CAAC,IAAI,CAACyB,gBAAgB,EAAE;MACxB,OAAO,IAAI,CAAC1D,KAAK,CAACiB,MAAM,CAACC,KAAK,EAAEC,WAAW,EAAE,IAAIwC,KAAK,CAAC1B,QAAQ,CAAC7E,MAAM,CAAC,CAACwG,IAAI,CAAC,KAAK,CAAC,CAAC;IACxF;IACA,MAAMC,qBAAqB,GAAG,IAAI,CAAC7D,KAAK,CAACmD,GAAG,CAAC,CAAC,CAACW,GAAG,CAACnG,CAAC,IAAI,IAAI,CAAC+F,gBAAgB,CAACK,KAAK,CAAC,IAAI,CAACN,IAAI,CAAClD,OAAO,CAAC5C,CAAC,CAAC,CAAC,CAACqG,QAAQ,CAAC,CAAC,CAAC;IACrH,IAAIH,qBAAqB,CAACzG,MAAM,KAAK,CAAC,EAAE;MACpC,OAAO,IAAI,CAAC4C,KAAK,CAACiB,MAAM,CAACC,KAAK,EAAEC,WAAW,EAAE,IAAIwC,KAAK,CAAC1B,QAAQ,CAAC7E,MAAM,CAAC,CAACwG,IAAI,CAAC,KAAK,CAAC,CAAC;IACxF;IACA,MAAMK,wBAAwB,GAAG,IAAIC,GAAG,CAACL,qBAAqB,CAAC;IAC/D,MAAMM,iBAAiB,GAAGlC,QAAQ,CAAC6B,GAAG,CAACM,CAAC,IAAIH,wBAAwB,CAACI,GAAG,CAAC,IAAI,CAACX,gBAAgB,CAACK,KAAK,CAACK,CAAC,CAAC,CAACJ,QAAQ,CAAC,CAAC,CAAC,CAAC;IACpH,IAAI,CAAChE,KAAK,CAACiB,MAAM,CAACC,KAAK,EAAEC,WAAW,EAAEgD,iBAAiB,CAAC;EAC5D;AACJ;AACA,OAAO,SAASG,cAAcA,CAACF,CAAC,EAAE;EAC9B,OAAOA,CAAC,CAACG,OAAO,KAAK,OAAO,IAAIH,CAAC,CAACG,OAAO,KAAK,UAAU;AAC5D;AACA,SAASC,8BAA8BA,CAACJ,CAAC,EAAEK,SAAS,EAAE;EAClD,IAAIL,CAAC,CAAC9B,SAAS,CAACE,QAAQ,CAACiC,SAAS,CAAC,EAAE;IACjC,OAAO,IAAI;EACf;EACA,IAAIL,CAAC,CAAC9B,SAAS,CAACE,QAAQ,CAAC,aAAa,CAAC,EAAE;IACrC,OAAO,KAAK;EAChB;EACA,IAAI,CAAC4B,CAAC,CAACM,aAAa,EAAE;IAClB,OAAO,KAAK;EAChB;EACA,OAAOF,8BAA8B,CAACJ,CAAC,CAACM,aAAa,EAAED,SAAS,CAAC;AACrE;AACA,OAAO,SAASE,cAAcA,CAACP,CAAC,EAAE;EAC9B,OAAOI,8BAA8B,CAACJ,CAAC,EAAE,eAAe,CAAC;AAC7D;AACA,OAAO,SAASQ,oBAAoBA,CAACR,CAAC,EAAE;EACpC,OAAOI,8BAA8B,CAACJ,CAAC,EAAE,sBAAsB,CAAC;AACpE;AACA,OAAO,SAASS,YAAYA,CAACT,CAAC,EAAE;EAC5B,OAAOI,8BAA8B,CAACJ,CAAC,EAAE,aAAa,CAAC;AAC3D;AACA,OAAO,SAASU,qBAAqBA,CAACV,CAAC,EAAE;EACrC,OAAOI,8BAA8B,CAACJ,CAAC,EAAE,wBAAwB,CAAC;AACtE;AACA,OAAO,SAASW,uBAAuBA,CAACX,CAAC,EAAE;EACvC,OAAOA,CAAC,CAAC9B,SAAS,CAACE,QAAQ,CAAC,8BAA8B,CAAC;AAC/D;AACA,OAAO,SAASwC,QAAQA,CAACZ,CAAC,EAAE;EACxB,IAAKA,CAAC,CAACG,OAAO,KAAK,GAAG,IAAIH,CAAC,CAAC9B,SAAS,CAACE,QAAQ,CAAC,eAAe,CAAC,IAC1D4B,CAAC,CAACG,OAAO,KAAK,KAAK,IAAIH,CAAC,CAAC9B,SAAS,CAACE,QAAQ,CAAC,wBAAwB,CAAE,EAAE;IACzE,OAAO,IAAI;EACf;EACA,IAAI4B,CAAC,CAAC9B,SAAS,CAACE,QAAQ,CAAC,aAAa,CAAC,EAAE;IACrC,OAAO,KAAK;EAChB;EACA,IAAI,CAAC4B,CAAC,CAACM,aAAa,EAAE;IAClB,OAAO,KAAK;EAChB;EACA,OAAOM,QAAQ,CAACZ,CAAC,CAACM,aAAa,CAAC;AACpC;AACA,MAAMO,kBAAkB,CAAC;EACrB,IAAIC,SAASA,CAAA,EAAG;IACZ,OAAOlG,KAAK,CAACmG,KAAK,CAAC,IAAI,CAACC,WAAW,CAACC,GAAG,CAAC,IAAIjH,UAAU,CAAC,IAAI,CAACqF,IAAI,CAAC6B,OAAO,EAAE,SAAS,CAAC,CAAC,CAACtD,KAAK,EAAEuD,CAAC,IAAIA,CAAC,CAACC,MAAM,CAACpB,CAAC,IAAI,CAACE,cAAc,CAACF,CAAC,CAACrH,MAAM,CAAC,CAAC,CACrI+G,GAAG,CAACM,CAAC,IAAI,IAAI/F,qBAAqB,CAAC+F,CAAC,CAAC,CAAC,CAAC;EAChD;EACArE,WAAWA,CAAC0F,IAAI,EAAEhC,IAAI,EAAEiC,OAAO,EAAE;IAC7B,IAAI,CAACD,IAAI,GAAGA,IAAI;IAChB,IAAI,CAAChC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAAC2B,WAAW,GAAG,IAAIhG,eAAe,CAAC,CAAC;IACxC,IAAI,CAACuG,4BAA4B,GAAG,IAAIvG,eAAe,CAAC,CAAC;IACzD,IAAI,CAACwG,wBAAwB,GAAGF,OAAO,CAACE,wBAAwB;IAChE,IAAI,CAACR,WAAW,CAACC,GAAG,CAAC,IAAI,CAACH,SAAS,CAACd,CAAC,IAAI;MACrC,QAAQA,CAAC,CAACyB,OAAO;QACb,KAAK,CAAC,CAAC;UACH,OAAO,IAAI,CAACC,OAAO,CAAC1B,CAAC,CAAC;QAC1B,KAAK,EAAE,CAAC;UACJ,OAAO,IAAI,CAAC2B,SAAS,CAAC3B,CAAC,CAAC;QAC5B,KAAK,EAAE,CAAC;UACJ,OAAO,IAAI,CAAC4B,WAAW,CAAC5B,CAAC,CAAC;QAC9B,KAAK,EAAE,CAAC;UACJ,OAAO,IAAI,CAAC6B,aAAa,CAAC7B,CAAC,CAAC;QAChC,KAAK,EAAE,CAAC;UACJ,OAAO,IAAI,CAAC8B,eAAe,CAAC9B,CAAC,CAAC;QAClC,KAAK,CAAC,CAAC;UACH,OAAO,IAAI,CAAC+B,QAAQ,CAAC/B,CAAC,CAAC;QAC3B,KAAK,EAAE,CAAC;UACJ,IAAI,IAAI,CAACwB,wBAAwB,KAAKrG,QAAQ,CAAC6G,WAAW,GAAGhC,CAAC,CAACiC,OAAO,GAAGjC,CAAC,CAACkC,OAAO,CAAC,EAAE;YACjF,IAAI,CAACC,OAAO,CAACnC,CAAC,CAAC;UACnB;MACR;IACJ,CAAC,CAAC,CAAC;EACP;EACAoC,aAAaA,CAACC,aAAa,EAAE;IACzB,IAAIA,aAAa,CAACb,wBAAwB,KAAKc,SAAS,EAAE;MACtD,IAAI,CAACd,wBAAwB,GAAGa,aAAa,CAACb,wBAAwB;IAC1E;EACJ;EACAE,OAAOA,CAAC1B,CAAC,EAAE;IACPA,CAAC,CAACuC,cAAc,CAAC,CAAC;IAClBvC,CAAC,CAACwC,eAAe,CAAC,CAAC;IACnB,IAAI,CAACnB,IAAI,CAACoB,YAAY,CAAC,IAAI,CAACpB,IAAI,CAACqB,QAAQ,CAAC,CAAC,EAAE1C,CAAC,CAACzB,YAAY,CAAC;EAChE;EACAoD,SAASA,CAAC3B,CAAC,EAAE;IACTA,CAAC,CAACuC,cAAc,CAAC,CAAC;IAClBvC,CAAC,CAACwC,eAAe,CAAC,CAAC;IACnB,IAAI,CAACnB,IAAI,CAACsB,aAAa,CAAC,CAAC,EAAE,KAAK,EAAE3C,CAAC,CAACzB,YAAY,CAAC;IACjD,MAAM/B,EAAE,GAAG,IAAI,CAAC6E,IAAI,CAACqB,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;IAClC,IAAI,CAACrB,IAAI,CAACuB,SAAS,CAACpG,EAAE,CAAC;IACvB,IAAI,CAAC6E,IAAI,CAACwB,MAAM,CAACrG,EAAE,CAAC;IACpB,IAAI,CAAC6C,IAAI,CAAC6B,OAAO,CAAC4B,KAAK,CAAC,CAAC;EAC7B;EACAlB,WAAWA,CAAC5B,CAAC,EAAE;IACXA,CAAC,CAACuC,cAAc,CAAC,CAAC;IAClBvC,CAAC,CAACwC,eAAe,CAAC,CAAC;IACnB,IAAI,CAACnB,IAAI,CAAC0B,SAAS,CAAC,CAAC,EAAE,KAAK,EAAE/C,CAAC,CAACzB,YAAY,CAAC;IAC7C,MAAM/B,EAAE,GAAG,IAAI,CAAC6E,IAAI,CAACqB,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;IAClC,IAAI,CAACrB,IAAI,CAACuB,SAAS,CAACpG,EAAE,CAAC;IACvB,IAAI,CAAC6E,IAAI,CAACwB,MAAM,CAACrG,EAAE,CAAC;IACpB,IAAI,CAAC6C,IAAI,CAAC6B,OAAO,CAAC4B,KAAK,CAAC,CAAC;EAC7B;EACAjB,aAAaA,CAAC7B,CAAC,EAAE;IACbA,CAAC,CAACuC,cAAc,CAAC,CAAC;IAClBvC,CAAC,CAACwC,eAAe,CAAC,CAAC;IACnB,IAAI,CAACnB,IAAI,CAAC2B,iBAAiB,CAAChD,CAAC,CAACzB,YAAY,CAAC;IAC3C,MAAM/B,EAAE,GAAG,IAAI,CAAC6E,IAAI,CAACqB,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;IAClC,IAAI,CAACrB,IAAI,CAACuB,SAAS,CAACpG,EAAE,CAAC;IACvB,IAAI,CAAC6E,IAAI,CAACwB,MAAM,CAACrG,EAAE,CAAC;IACpB,IAAI,CAAC6C,IAAI,CAAC6B,OAAO,CAAC4B,KAAK,CAAC,CAAC;EAC7B;EACAhB,eAAeA,CAAC9B,CAAC,EAAE;IACfA,CAAC,CAACuC,cAAc,CAAC,CAAC;IAClBvC,CAAC,CAACwC,eAAe,CAAC,CAAC;IACnB,IAAI,CAACnB,IAAI,CAAC4B,aAAa,CAACjD,CAAC,CAACzB,YAAY,CAAC;IACvC,MAAM/B,EAAE,GAAG,IAAI,CAAC6E,IAAI,CAACqB,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;IAClC,IAAI,CAACrB,IAAI,CAACuB,SAAS,CAACpG,EAAE,CAAC;IACvB,IAAI,CAAC6E,IAAI,CAACwB,MAAM,CAACrG,EAAE,CAAC;IACpB,IAAI,CAAC6C,IAAI,CAAC6B,OAAO,CAAC4B,KAAK,CAAC,CAAC;EAC7B;EACAX,OAAOA,CAACnC,CAAC,EAAE;IACPA,CAAC,CAACuC,cAAc,CAAC,CAAC;IAClBvC,CAAC,CAACwC,eAAe,CAAC,CAAC;IACnB,IAAI,CAACnB,IAAI,CAACoB,YAAY,CAAClI,KAAK,CAAC,IAAI,CAAC8G,IAAI,CAACrI,MAAM,CAAC,EAAEgH,CAAC,CAACzB,YAAY,CAAC;IAC/D,IAAI,CAAC8C,IAAI,CAACuB,SAAS,CAACN,SAAS,CAAC;IAC9B,IAAI,CAACjD,IAAI,CAAC6B,OAAO,CAAC4B,KAAK,CAAC,CAAC;EAC7B;EACAf,QAAQA,CAAC/B,CAAC,EAAE;IACR,IAAI,IAAI,CAACqB,IAAI,CAAC6B,YAAY,CAAC,CAAC,CAAClK,MAAM,EAAE;MACjCgH,CAAC,CAACuC,cAAc,CAAC,CAAC;MAClBvC,CAAC,CAACwC,eAAe,CAAC,CAAC;MACnB,IAAI,CAACnB,IAAI,CAACoB,YAAY,CAAC,EAAE,EAAEzC,CAAC,CAACzB,YAAY,CAAC;MAC1C,IAAI,CAAC8C,IAAI,CAACuB,SAAS,CAACN,SAAS,CAAC;MAC9B,IAAI,CAACjD,IAAI,CAAC6B,OAAO,CAAC4B,KAAK,CAAC,CAAC;IAC7B;EACJ;EACA7H,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC+F,WAAW,CAAC/F,OAAO,CAAC,CAAC;IAC1B,IAAI,CAACsG,4BAA4B,CAACtG,OAAO,CAAC,CAAC;EAC/C;AACJ;AACAxC,UAAU,CAAC,CACPiC,OAAO,CACV,EAAEmG,kBAAkB,CAAC7B,SAAS,EAAE,WAAW,EAAE,IAAI,CAAC;AACnD,OAAO,IAAImE,kBAAkB,gBAC5B,UAAUA,kBAAkB,EAAE;EAC3BA,kBAAkB,CAACA,kBAAkB,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW;EACrEA,kBAAkB,CAACA,kBAAkB,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS;EAAC,OAF3DA,kBAAkB;AAG7B,CAAC,CAAEA,kBAAkB,IAA0B,CAAC,CAAE,CAJrB;AAK7B,IAAIC,6BAA6B,gBAChC,UAAUA,6BAA6B,EAAE;EACtCA,6BAA6B,CAACA,6BAA6B,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EACjFA,6BAA6B,CAACA,6BAA6B,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ;EAAC,OAF/EA,6BAA6B;AAGxC,CAAC,CAAEA,6BAA6B,IAAqC,CAAC,CAAE,CAJvC;AAKjC,OAAO,MAAMC,iCAAiC,GAAG,IAAI,MAAM;EACvDC,8BAA8BA,CAAC1F,KAAK,EAAE;IAClC,IAAIA,KAAK,CAACsE,OAAO,IAAItE,KAAK,CAACqE,OAAO,IAAIrE,KAAK,CAAC2F,MAAM,EAAE;MAChD,OAAO,KAAK;IAChB;IACA,OAAQ3F,KAAK,CAAC6D,OAAO,IAAI,EAAE,CAAC,sBAAsB7D,KAAK,CAAC6D,OAAO,IAAI,EAAE,CAAC,sBAC9D7D,KAAK,CAAC6D,OAAO,IAAI,EAAE,CAAC,wBAAwB7D,KAAK,CAAC6D,OAAO,IAAI,EAAE,CAAC,oBAAqB,IACrF7D,KAAK,CAAC6D,OAAO,IAAI,EAAE,CAAC,yBAAyB7D,KAAK,CAAC6D,OAAO,IAAI,GAAG,CAAC,qBAAsB,IACxF7D,KAAK,CAAC6D,OAAO,IAAI,EAAE,CAAC,2BAA2B7D,KAAK,CAAC6D,OAAO,IAAI,EAAE,CAAC,mBAAoB;EACnG;AACJ,CAAC,CAAD,CAAC;AACD,MAAM+B,wBAAwB,CAAC;EAC3B7H,WAAWA,CAAC0F,IAAI,EAAEhC,IAAI,EAAEoE,+BAA+B,EAAEC,6BAA6B,EAAEC,QAAQ,EAAE;IAC9F,IAAI,CAACtC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAAChC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACoE,+BAA+B,GAAGA,+BAA+B;IACtE,IAAI,CAACC,6BAA6B,GAAGA,6BAA6B;IAClE,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,KAAK,GAAGT,6BAA6B,CAACU,IAAI;IAC/C,IAAI,CAACC,IAAI,GAAGZ,kBAAkB,CAACa,SAAS;IACxC,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,iBAAiB,GAAG,CAAC,CAAC;IAC3B,IAAI,CAACC,kBAAkB,GAAG,IAAInJ,eAAe,CAAC,CAAC;IAC/C,IAAI,CAACgG,WAAW,GAAG,IAAIhG,eAAe,CAAC,CAAC;IACxC,IAAI,CAACoH,aAAa,CAACf,IAAI,CAACC,OAAO,CAAC;EACpC;EACAc,aAAaA,CAACd,OAAO,EAAE;IACnB,IAAIA,OAAO,CAAC8C,qBAAqB,IAAI,IAAI,EAAE;MACvC,IAAI,CAACC,MAAM,CAAC,CAAC;IACjB,CAAC,MACI;MACD,IAAI,CAACC,OAAO,CAAC,CAAC;IAClB;IACA,IAAI,CAACP,IAAI,GAAGzC,OAAO,CAACiD,kBAAkB,IAAIpB,kBAAkB,CAACa,SAAS;EAC1E;EACAK,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAACT,OAAO,EAAE;MACd;IACJ;IACA,IAAIY,MAAM,GAAG,KAAK;IAClB,MAAMC,MAAM,GAAG7J,KAAK,CAACmG,KAAK,CAAC,IAAI,CAACoD,kBAAkB,CAAClD,GAAG,CAAC,IAAIjH,UAAU,CAAC,IAAI,CAACqF,IAAI,CAAC6B,OAAO,EAAE,SAAS,CAAC,CAAC,CAACtD,KAAK,EAAEuD,CAAC,IAAIA,CAAC,CAACC,MAAM,CAACpB,CAAC,IAAI,CAACE,cAAc,CAACF,CAAC,CAACrH,MAAM,CAAC,CAAC,CACpJyI,MAAM,CAAC,MAAM,IAAI,CAAC2C,IAAI,KAAKZ,kBAAkB,CAACa,SAAS,IAAI,IAAI,CAACC,SAAS,CAAC,CAC1EvE,GAAG,CAAC9B,KAAK,IAAI,IAAI3D,qBAAqB,CAAC2D,KAAK,CAAC,CAAC,CAC9CwD,MAAM,CAACpB,CAAC,IAAIwE,MAAM,IAAI,IAAI,CAACd,6BAA6B,CAAC1D,CAAC,CAAC,CAAC,CAC5DoB,MAAM,CAACpB,CAAC,IAAI,IAAI,CAAC2D,QAAQ,CAACL,8BAA8B,CAACtD,CAAC,CAAC,CAAC,CAC5D0E,OAAO,CAAC1E,CAAC,IAAIrG,WAAW,CAACgL,IAAI,CAAC3E,CAAC,EAAE,IAAI,CAAC,CAAC,CACvCN,GAAG,CAAC9B,KAAK,IAAIA,KAAK,CAACW,YAAY,CAAC3F,GAAG,CAAC,CAAC;IAC1C,MAAMgM,OAAO,GAAGhK,KAAK,CAACiK,QAAQ,CAACJ,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,EAAEnC,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAE,IAAI,CAAC6B,kBAAkB,CAAC;IACjH,MAAMW,OAAO,GAAGlK,KAAK,CAACmK,MAAM,CAACnK,KAAK,CAACoK,GAAG,CAACP,MAAM,EAAEG,OAAO,CAAC,EAAE,CAAC3L,CAAC,EAAEM,CAAC,KAAKA,CAAC,KAAK,IAAI,GAAG,IAAI,GAAI,CAACN,CAAC,IAAI,EAAE,IAAIM,CAAE,EAAE+I,SAAS,EAAE,IAAI,CAAC6B,kBAAkB,CAAC;IAC3IW,OAAO,CAAC,IAAI,CAACA,OAAO,EAAE,IAAI,EAAE,IAAI,CAACX,kBAAkB,CAAC;IACpDS,OAAO,CAAC,IAAI,CAACA,OAAO,EAAE,IAAI,EAAE,IAAI,CAACT,kBAAkB,CAAC;IACpDM,MAAM,CAAC,MAAMD,MAAM,GAAG,IAAI,EAAElC,SAAS,EAAE,IAAI,CAAC6B,kBAAkB,CAAC;IAC/DS,OAAO,CAAC,MAAMJ,MAAM,GAAG,KAAK,EAAElC,SAAS,EAAE,IAAI,CAAC6B,kBAAkB,CAAC;IACjE,IAAI,CAACP,OAAO,GAAG,IAAI;IACnB,IAAI,CAACK,SAAS,GAAG,KAAK;EAC1B;EACAK,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC,IAAI,CAACV,OAAO,EAAE;MACf;IACJ;IACA,IAAI,CAACO,kBAAkB,CAACc,KAAK,CAAC,CAAC;IAC/B,IAAI,CAACrB,OAAO,GAAG,KAAK;IACpB,IAAI,CAACK,SAAS,GAAG,KAAK;EAC1B;EACAW,OAAOA,CAAA,EAAG;IACN,MAAM9B,KAAK,GAAG,IAAI,CAACzB,IAAI,CAACqB,QAAQ,CAAC,CAAC;IAClC,IAAII,KAAK,CAAC9J,MAAM,GAAG,CAAC,IAAI8J,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI,CAACoB,iBAAiB,EAAE;MACzD;MACA;MACA,MAAMgB,SAAS,GAAG,IAAI,CAAC7D,IAAI,CAACC,OAAO,CAAC6D,qBAAqB,EAAEC,YAAY,CAAC,IAAI,CAAC/D,IAAI,CAAClF,OAAO,CAAC2G,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;MACpG,IAAI,OAAOoC,SAAS,KAAK,QAAQ,EAAE;QAC/B/K,KAAK,CAAC+K,SAAS,CAAC;MACpB,CAAC,MACI,IAAIA,SAAS,EAAE;QAChB/K,KAAK,CAAC+K,SAAS,CAACnG,GAAG,CAAC,CAAC,CAAC;MAC1B;IACJ;IACA,IAAI,CAACmF,iBAAiB,GAAG,CAAC,CAAC;EAC/B;EACAY,OAAOA,CAACO,IAAI,EAAE;IACV,IAAI,CAACA,IAAI,EAAE;MACP,IAAI,CAACxB,KAAK,GAAGT,6BAA6B,CAACU,IAAI;MAC/C,IAAI,CAACG,SAAS,GAAG,KAAK;MACtB;IACJ;IACA,MAAMnB,KAAK,GAAG,IAAI,CAACzB,IAAI,CAACqB,QAAQ,CAAC,CAAC;IAClC,MAAM5F,KAAK,GAAGgG,KAAK,CAAC9J,MAAM,GAAG,CAAC,GAAG8J,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;IAC7C,MAAMwC,KAAK,GAAG,IAAI,CAACzB,KAAK,KAAKT,6BAA6B,CAACU,IAAI,GAAG,CAAC,GAAG,CAAC;IACvE,IAAI,CAACD,KAAK,GAAGT,6BAA6B,CAACmC,MAAM;IACjD,KAAK,IAAIhM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC8H,IAAI,CAACrI,MAAM,EAAEO,CAAC,EAAE,EAAE;MACvC,MAAM6C,KAAK,GAAG,CAACU,KAAK,GAAGvD,CAAC,GAAG+L,KAAK,IAAI,IAAI,CAACjE,IAAI,CAACrI,MAAM;MACpD,MAAMwM,KAAK,GAAG,IAAI,CAAC/B,+BAA+B,CAACgC,0BAA0B,CAAC,IAAI,CAACpG,IAAI,CAAClD,OAAO,CAACC,KAAK,CAAC,CAAC;MACvG,MAAMsJ,QAAQ,GAAGF,KAAK,IAAIA,KAAK,CAAC5F,QAAQ,CAAC,CAAC;MAC1C,IAAI,IAAI,CAACyB,IAAI,CAACC,OAAO,CAAC8C,qBAAqB,EAAE;QACzC,IAAI,OAAOsB,QAAQ,KAAK,WAAW,EAAE;UACjC;UACA,IAAI3K,aAAa,CAACsK,IAAI,EAAEK,QAAQ,CAAC,EAAE;YAC/B,IAAI,CAACxB,iBAAiB,GAAGpH,KAAK;YAC9B,IAAI,CAACuE,IAAI,CAACsE,QAAQ,CAAC,CAACvJ,KAAK,CAAC,CAAC;YAC3B,IAAI,CAACiF,IAAI,CAACwB,MAAM,CAACzG,KAAK,CAAC;YACvB;UACJ;UACA,MAAMwJ,KAAK,GAAG9K,aAAa,CAACuK,IAAI,EAAEK,QAAQ,CAAC;UAC3C,IAAIE,KAAK,EAAE;YACP,MAAMC,UAAU,GAAGD,KAAK,CAAC,CAAC,CAAC,CAAC7H,GAAG,GAAG6H,KAAK,CAAC,CAAC,CAAC,CAAC9I,KAAK;YAChD;YACA,IAAI+I,UAAU,GAAG,CAAC,IAAID,KAAK,CAAC5M,MAAM,KAAK,CAAC,EAAE;cACtC,IAAI,CAACkL,iBAAiB,GAAGpH,KAAK;cAC9B,IAAI,CAACuE,IAAI,CAACsE,QAAQ,CAAC,CAACvJ,KAAK,CAAC,CAAC;cAC3B,IAAI,CAACiF,IAAI,CAACwB,MAAM,CAACzG,KAAK,CAAC;cACvB;YACJ;UACJ;QACJ;MACJ,CAAC,MACI,IAAI,OAAOsJ,QAAQ,KAAK,WAAW,IAAI3K,aAAa,CAACsK,IAAI,EAAEK,QAAQ,CAAC,EAAE;QACvE,IAAI,CAACxB,iBAAiB,GAAGpH,KAAK;QAC9B,IAAI,CAACuE,IAAI,CAACsE,QAAQ,CAAC,CAACvJ,KAAK,CAAC,CAAC;QAC3B,IAAI,CAACiF,IAAI,CAACwB,MAAM,CAACzG,KAAK,CAAC;QACvB;MACJ;IACJ;EACJ;EACAnB,OAAOA,CAAA,EAAG;IACN,IAAI,CAACqJ,OAAO,CAAC,CAAC;IACd,IAAI,CAACH,kBAAkB,CAAClJ,OAAO,CAAC,CAAC;IACjC,IAAI,CAAC+F,WAAW,CAAC/F,OAAO,CAAC,CAAC;EAC9B;AACJ;AACA,MAAM6K,kBAAkB,CAAC;EACrBnK,WAAWA,CAAC0F,IAAI,EAAEhC,IAAI,EAAE;IACpB,IAAI,CAACgC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAAChC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAAC2B,WAAW,GAAG,IAAIhG,eAAe,CAAC,CAAC;IACxC,MAAM8F,SAAS,GAAGlG,KAAK,CAACmG,KAAK,CAAC,IAAI,CAACC,WAAW,CAACC,GAAG,CAAC,IAAIjH,UAAU,CAACqF,IAAI,CAAC6B,OAAO,EAAE,SAAS,CAAC,CAAC,CAACtD,KAAK,EAAEuD,CAAC,IAAIA,CAAC,CACpGC,MAAM,CAACpB,CAAC,IAAI,CAACE,cAAc,CAACF,CAAC,CAACrH,MAAM,CAAC,CAAC,CACtC+G,GAAG,CAACM,CAAC,IAAI,IAAI/F,qBAAqB,CAAC+F,CAAC,CAAC,CAAC,CAAC;IAC5C,MAAM+F,KAAK,GAAGnL,KAAK,CAACmG,KAAK,CAACD,SAAS,EAAEK,CAAC,IAAIA,CAAC,CAACC,MAAM,CAACpB,CAAC,IAAIA,CAAC,CAACyB,OAAO,KAAK,CAAC,CAAC,qBAAqB,CAACzB,CAAC,CAACkC,OAAO,IAAI,CAAClC,CAAC,CAACiC,OAAO,IAAI,CAACjC,CAAC,CAACgG,QAAQ,IAAI,CAAChG,CAAC,CAACuD,MAAM,CAAC,CAAC;IACnJwC,KAAK,CAAC,IAAI,CAACA,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC/E,WAAW,CAAC;EAC7C;EACA+E,KAAKA,CAAC/F,CAAC,EAAE;IACL,IAAIA,CAAC,CAACrH,MAAM,KAAK,IAAI,CAAC0G,IAAI,CAAC6B,OAAO,EAAE;MAChC;IACJ;IACA,MAAM4B,KAAK,GAAG,IAAI,CAACzB,IAAI,CAACqB,QAAQ,CAAC,CAAC;IAClC,IAAII,KAAK,CAAC9J,MAAM,KAAK,CAAC,EAAE;MACpB;IACJ;IACA,MAAMiN,iBAAiB,GAAG,IAAI,CAAC5G,IAAI,CAAC6G,UAAU,CAACpD,KAAK,CAAC,CAAC,CAAC,CAAC;IACxD,IAAI,CAACmD,iBAAiB,EAAE;MACpB;IACJ;IACA,MAAME,eAAe,GAAGF,iBAAiB,CAACG,aAAa,CAAC,YAAY,CAAC;IACrE,IAAI,CAACD,eAAe,IAAI,CAAErM,aAAa,CAACqM,eAAe,CAAE,IAAIA,eAAe,CAACE,QAAQ,KAAK,CAAC,CAAC,EAAE;MAC1F;IACJ;IACA,MAAMC,KAAK,GAAGzM,SAAS,CAACsM,eAAe,CAAC,CAACI,gBAAgB,CAACJ,eAAe,CAAC;IAC1E,IAAIG,KAAK,CAACE,UAAU,KAAK,QAAQ,IAAIF,KAAK,CAACG,OAAO,KAAK,MAAM,EAAE;MAC3D;IACJ;IACAzG,CAAC,CAACuC,cAAc,CAAC,CAAC;IAClBvC,CAAC,CAACwC,eAAe,CAAC,CAAC;IACnB2D,eAAe,CAACrD,KAAK,CAAC,CAAC;EAC3B;EACA7H,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC+F,WAAW,CAAC/F,OAAO,CAAC,CAAC;EAC9B;AACJ;AACA,OAAO,SAASyL,4BAA4BA,CAAC9I,KAAK,EAAE;EAChD,OAAOzC,QAAQ,CAAC6G,WAAW,GAAGpE,KAAK,CAACW,YAAY,CAAC0D,OAAO,GAAGrE,KAAK,CAACW,YAAY,CAAC2D,OAAO;AACzF;AACA,OAAO,SAASyE,2BAA2BA,CAAC/I,KAAK,EAAE;EAC/C,OAAOA,KAAK,CAACW,YAAY,CAACyH,QAAQ;AACtC;AACA,SAASY,iBAAiBA,CAAChJ,KAAK,EAAE;EAC9B,OAAO7D,YAAY,CAAC6D,KAAK,CAAC,IAAIA,KAAK,CAACiJ,MAAM,KAAK,CAAC;AACpD;AACA,MAAMC,kCAAkC,GAAG;EACvCJ,4BAA4B;EAC5BC;AACJ,CAAC;AACD,OAAO,MAAMI,eAAe,CAAC;EACzBpL,WAAWA,CAAC0F,IAAI,EAAE;IACd,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACL,WAAW,GAAG,IAAIhG,eAAe,CAAC,CAAC;IACxC,IAAI,CAACgM,UAAU,GAAG,IAAIrM,OAAO,CAAC,CAAC;IAC/B,IAAI,CAACsM,SAAS,GAAG,IAAI,CAACD,UAAU,CAACpJ,KAAK;IACtC,IAAIyD,IAAI,CAACC,OAAO,CAACE,wBAAwB,KAAK,KAAK,EAAE;MACjD,IAAI,CAAC0F,2BAA2B,GAAG,IAAI,CAAC7F,IAAI,CAACC,OAAO,CAAC4F,2BAA2B,IAAIJ,kCAAkC;IAC1H;IACA,IAAI,CAACK,YAAY,GAAG,OAAO9F,IAAI,CAACC,OAAO,CAAC6F,YAAY,KAAK,WAAW,IAAI,CAAC,CAAC9F,IAAI,CAACC,OAAO,CAAC6F,YAAY;IACnG,IAAI,IAAI,CAACA,YAAY,EAAE;MACnB9F,IAAI,CAAC+F,WAAW,CAAC,IAAI,CAACA,WAAW,EAAE,IAAI,EAAE,IAAI,CAACpG,WAAW,CAAC;MAC1DK,IAAI,CAACgG,aAAa,CAAC,IAAI,CAACA,aAAa,EAAE,IAAI,EAAE,IAAI,CAACrG,WAAW,CAAC;MAC9DK,IAAI,CAACiG,eAAe,CAAC,IAAI,CAACC,aAAa,EAAE,IAAI,EAAE,IAAI,CAACvG,WAAW,CAAC;MAChEK,IAAI,CAACmG,YAAY,CAAC,IAAI,CAACJ,WAAW,EAAE,IAAI,EAAE,IAAI,CAACpG,WAAW,CAAC;MAC3D,IAAI,CAACA,WAAW,CAACC,GAAG,CAAC/G,OAAO,CAACuN,SAAS,CAACpG,IAAI,CAACqG,cAAc,CAAC,CAAC,CAAC,CAAC;IAClE;IACA9M,KAAK,CAACoK,GAAG,CAAC3D,IAAI,CAACsG,YAAY,EAAEtG,IAAI,CAACuG,kBAAkB,EAAEvG,IAAI,CAACwG,KAAK,CAAC,CAAC,IAAI,CAACC,aAAa,EAAE,IAAI,EAAE,IAAI,CAAC9G,WAAW,CAAC;EACjH;EACAoB,aAAaA,CAACC,aAAa,EAAE;IACzB,IAAIA,aAAa,CAACb,wBAAwB,KAAKc,SAAS,EAAE;MACtD,IAAI,CAAC4E,2BAA2B,GAAG5E,SAAS;MAC5C,IAAID,aAAa,CAACb,wBAAwB,EAAE;QACxC,IAAI,CAAC0F,2BAA2B,GAAG,IAAI,CAAC7F,IAAI,CAACC,OAAO,CAAC4F,2BAA2B,IAAIJ,kCAAkC;MAC1H;IACJ;EACJ;EACAJ,4BAA4BA,CAAC9I,KAAK,EAAE;IAChC,IAAI,CAAC,IAAI,CAACsJ,2BAA2B,EAAE;MACnC,OAAO,KAAK;IAChB;IACA,OAAO,IAAI,CAACA,2BAA2B,CAACR,4BAA4B,CAAC9I,KAAK,CAAC;EAC/E;EACA+I,2BAA2BA,CAAC/I,KAAK,EAAE;IAC/B,IAAI,CAAC,IAAI,CAACsJ,2BAA2B,EAAE;MACnC,OAAO,KAAK;IAChB;IACA,OAAO,IAAI,CAACA,2BAA2B,CAACP,2BAA2B,CAAC/I,KAAK,CAAC;EAC9E;EACAmK,sBAAsBA,CAACnK,KAAK,EAAE;IAC1B,OAAO,IAAI,CAAC8I,4BAA4B,CAAC9I,KAAK,CAAC,IAAI,IAAI,CAAC+I,2BAA2B,CAAC/I,KAAK,CAAC;EAC9F;EACAwJ,WAAWA,CAACpH,CAAC,EAAE;IACX,IAAIO,cAAc,CAACP,CAAC,CAACzB,YAAY,CAAC5F,MAAM,CAAC,EAAE;MACvC;IACJ;IACA,IAAIiB,gBAAgB,CAAC,CAAC,KAAKoG,CAAC,CAACzB,YAAY,CAAC5F,MAAM,EAAE;MAC9C,IAAI,CAAC0I,IAAI,CAAC2G,QAAQ,CAAC,CAAC;IACxB;EACJ;EACAX,aAAaA,CAACrH,CAAC,EAAE;IACb,IAAIE,cAAc,CAACF,CAAC,CAACzB,YAAY,CAAC5F,MAAM,CAAC,IAAI4H,cAAc,CAACP,CAAC,CAACzB,YAAY,CAAC5F,MAAM,CAAC,EAAE;MAChF;IACJ;IACA,MAAMmK,KAAK,GAAG,OAAO9C,CAAC,CAAC5D,KAAK,KAAK,WAAW,GAAG,EAAE,GAAG,CAAC4D,CAAC,CAAC5D,KAAK,CAAC;IAC7D,IAAI,CAACiF,IAAI,CAACsE,QAAQ,CAAC7C,KAAK,EAAE9C,CAAC,CAACzB,YAAY,CAAC;EAC7C;EACAuJ,aAAaA,CAAC9H,CAAC,EAAE;IACb,IAAI,CAAC,IAAI,CAACmH,YAAY,EAAE;MACpB;IACJ;IACA,IAAIjH,cAAc,CAACF,CAAC,CAACzB,YAAY,CAAC5F,MAAM,CAAC,IAAI4H,cAAc,CAACP,CAAC,CAACzB,YAAY,CAAC5F,MAAM,CAAC,EAAE;MAChF;IACJ;IACA,IAAIqH,CAAC,CAACzB,YAAY,CAAC0J,eAAe,EAAE;MAChC;IACJ;IACAjI,CAAC,CAACzB,YAAY,CAAC0J,eAAe,GAAG,IAAI;IACrC,MAAMnF,KAAK,GAAG9C,CAAC,CAAC5D,KAAK;IACrB,IAAI,OAAO0G,KAAK,KAAK,WAAW,EAAE;MAC9B,IAAI,CAACzB,IAAI,CAACsE,QAAQ,CAAC,EAAE,EAAE3F,CAAC,CAACzB,YAAY,CAAC;MACtC,IAAI,CAAC8C,IAAI,CAACoB,YAAY,CAAC,EAAE,EAAEzC,CAAC,CAACzB,YAAY,CAAC;MAC1C,IAAI,CAAC8C,IAAI,CAACuB,SAAS,CAACN,SAAS,CAAC;MAC9B;IACJ;IACA,IAAI,IAAI,CAACyF,sBAAsB,CAAC/H,CAAC,CAAC,EAAE;MAChC,OAAO,IAAI,CAACkI,eAAe,CAAClI,CAAC,CAAC;IAClC;IACA,IAAI,CAACqB,IAAI,CAACsE,QAAQ,CAAC,CAAC7C,KAAK,CAAC,EAAE9C,CAAC,CAACzB,YAAY,CAAC;IAC3C,IAAI,CAAC8C,IAAI,CAACuB,SAAS,CAACE,KAAK,CAAC;IAC1B,IAAI,CAAC8D,iBAAiB,CAAC5G,CAAC,CAACzB,YAAY,CAAC,EAAE;MACpC,IAAI,CAAC8C,IAAI,CAACoB,YAAY,CAAC,CAACK,KAAK,CAAC,EAAE9C,CAAC,CAACzB,YAAY,CAAC;IACnD;IACA,IAAI,CAACyI,UAAU,CAAClI,IAAI,CAACkB,CAAC,CAAC;EAC3B;EACAuH,aAAaA,CAACvH,CAAC,EAAE;IACb,IAAIE,cAAc,CAACF,CAAC,CAACzB,YAAY,CAAC5F,MAAM,CAAC,IAAI4H,cAAc,CAACP,CAAC,CAACzB,YAAY,CAAC5F,MAAM,CAAC,EAAE;MAChF;IACJ;IACA,IAAI,IAAI,CAACoP,sBAAsB,CAAC/H,CAAC,CAAC,EAAE;MAChC;IACJ;IACA,IAAIA,CAAC,CAACzB,YAAY,CAAC0J,eAAe,EAAE;MAChC;IACJ;IACAjI,CAAC,CAACzB,YAAY,CAAC0J,eAAe,GAAG,IAAI;IACrC,MAAMnF,KAAK,GAAG,IAAI,CAACzB,IAAI,CAACqB,QAAQ,CAAC,CAAC;IAClC,IAAI,CAACrB,IAAI,CAACoB,YAAY,CAACK,KAAK,EAAE9C,CAAC,CAACzB,YAAY,CAAC;EACjD;EACA2J,eAAeA,CAAClI,CAAC,EAAE;IACf,MAAM8C,KAAK,GAAG9C,CAAC,CAAC5D,KAAK;IACrB,IAAI+L,MAAM,GAAG,IAAI,CAAC9G,IAAI,CAAC+G,SAAS,CAAC,CAAC;IAClC,IAAI,IAAI,CAACzB,2BAA2B,CAAC3G,CAAC,CAAC,EAAE;MACrC,IAAI,OAAOmI,MAAM,KAAK,WAAW,EAAE;QAC/B,MAAME,YAAY,GAAG,IAAI,CAAChH,IAAI,CAACqB,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5CyF,MAAM,GAAGE,YAAY,IAAIvF,KAAK;QAC9B,IAAI,CAACzB,IAAI,CAACuB,SAAS,CAACuF,MAAM,CAAC;MAC/B;MACA,MAAMG,GAAG,GAAGC,IAAI,CAACD,GAAG,CAACH,MAAM,EAAErF,KAAK,CAAC;MACnC,MAAM0F,GAAG,GAAGD,IAAI,CAACC,GAAG,CAACL,MAAM,EAAErF,KAAK,CAAC;MACnC,MAAM2F,cAAc,GAAGlO,KAAK,CAAC+N,GAAG,EAAEE,GAAG,GAAG,CAAC,CAAC;MAC1C,MAAME,SAAS,GAAG,IAAI,CAACrH,IAAI,CAAC6B,YAAY,CAAC,CAAC;MAC1C,MAAMyF,eAAe,GAAGC,4BAA4B,CAAC/J,WAAW,CAAC6J,SAAS,EAAE,CAACP,MAAM,CAAC,CAAC,EAAEA,MAAM,CAAC;MAC9F,IAAIQ,eAAe,CAAC3P,MAAM,KAAK,CAAC,EAAE;QAC9B;MACJ;MACA,MAAM6P,YAAY,GAAGhK,WAAW,CAAC4J,cAAc,EAAEK,kBAAkB,CAACJ,SAAS,EAAEC,eAAe,CAAC,CAAC;MAChG,IAAI,CAACtH,IAAI,CAACoB,YAAY,CAACoG,YAAY,EAAE7I,CAAC,CAACzB,YAAY,CAAC;MACpD,IAAI,CAAC8C,IAAI,CAACsE,QAAQ,CAAC,CAAC7C,KAAK,CAAC,EAAE9C,CAAC,CAACzB,YAAY,CAAC;IAC/C,CAAC,MACI,IAAI,IAAI,CAACmI,4BAA4B,CAAC1G,CAAC,CAAC,EAAE;MAC3C,MAAM0I,SAAS,GAAG,IAAI,CAACrH,IAAI,CAAC6B,YAAY,CAAC,CAAC;MAC1C,MAAM2F,YAAY,GAAGH,SAAS,CAACtH,MAAM,CAAC7H,CAAC,IAAIA,CAAC,KAAKuJ,KAAK,CAAC;MACvD,IAAI,CAACzB,IAAI,CAACsE,QAAQ,CAAC,CAAC7C,KAAK,CAAC,CAAC;MAC3B,IAAI,CAACzB,IAAI,CAACuB,SAAS,CAACE,KAAK,CAAC;MAC1B,IAAI4F,SAAS,CAAC1P,MAAM,KAAK6P,YAAY,CAAC7P,MAAM,EAAE;QAC1C,IAAI,CAACqI,IAAI,CAACoB,YAAY,CAAC,CAAC,GAAGoG,YAAY,EAAE/F,KAAK,CAAC,EAAE9C,CAAC,CAACzB,YAAY,CAAC;MACpE,CAAC,MACI;QACD,IAAI,CAAC8C,IAAI,CAACoB,YAAY,CAACoG,YAAY,EAAE7I,CAAC,CAACzB,YAAY,CAAC;MACxD;IACJ;EACJ;EACAtD,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC+F,WAAW,CAAC/F,OAAO,CAAC,CAAC;EAC9B;AACJ;AACA,OAAO,MAAM8N,sBAAsB,CAAC;EAChCpN,WAAWA,CAACqN,YAAY,EAAEC,cAAc,EAAE;IACtC,IAAI,CAACD,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,cAAc,GAAGA,cAAc;EACxC;EACA3C,KAAKA,CAAC4C,MAAM,EAAE;IACV,MAAMC,MAAM,GAAG,IAAI,CAACF,cAAc,IAAI,IAAI,IAAI,CAACA,cAAc,EAAE;IAC/D,MAAMG,OAAO,GAAG,EAAE;IAClB,IAAIF,MAAM,CAACG,cAAc,EAAE;MACvBD,OAAO,CAACzM,IAAI,CAAC,eAAewM,MAAM,oCAAoCD,MAAM,CAACG,cAAc,KAAK,CAAC;IACrG;IACA,IAAIH,MAAM,CAACI,mBAAmB,EAAE;MAC5BF,OAAO,CAACzM,IAAI,CAAC,eAAewM,MAAM,uDAAuDD,MAAM,CAACI,mBAAmB,KAAK,CAAC;MACzHF,OAAO,CAACzM,IAAI,CAAC,eAAewM,MAAM,6DAA6DD,MAAM,CAACI,mBAAmB,KAAK,CAAC,CAAC,CAAC;IACrI;IACA,IAAIJ,MAAM,CAACK,mBAAmB,EAAE;MAC5BH,OAAO,CAACzM,IAAI,CAAC,eAAewM,MAAM,4CAA4CD,MAAM,CAACK,mBAAmB,KAAK,CAAC;IAClH;IACA,IAAIL,MAAM,CAACM,6BAA6B,EAAE;MACtCJ,OAAO,CAACzM,IAAI,CAAC,eAAewM,MAAM,wDAAwDD,MAAM,CAACM,6BAA6B,KAAK,CAAC;MACpIJ,OAAO,CAACzM,IAAI,CAAC,eAAewM,MAAM,8DAA8DD,MAAM,CAACM,6BAA6B,KAAK,CAAC,CAAC,CAAC;IAChJ;IACA,IAAIN,MAAM,CAACO,6BAA6B,EAAE;MACtCL,OAAO,CAACzM,IAAI,CAAC,eAAewM,MAAM,6CAA6CD,MAAM,CAACO,6BAA6B,KAAK,CAAC;IAC7H;IACA,IAAIP,MAAM,CAACQ,iCAAiC,EAAE;MAC1CN,OAAO,CAACzM,IAAI,CAAC,eAAewM,MAAM,sDAAsDD,MAAM,CAACQ,iCAAiC,KAAK,CAAC;IAC1I;IACA,IAAIR,MAAM,CAACS,+BAA+B,EAAE;MACxCP,OAAO,CAACzM,IAAI,CAAC;AACzB;AACA,kBAAkBwM,MAAM,gEAAgED,MAAM,CAACS,+BAA+B;AAC9H,IAAI,CAAC;IACG;IACA,IAAIT,MAAM,CAACU,+BAA+B,EAAE;MACxCR,OAAO,CAACzM,IAAI,CAAC;AACzB;AACA,kBAAkBwM,MAAM,qDAAqDD,MAAM,CAACU,+BAA+B;AACnH,IAAI,CAAC;IACG;IACA,IAAIV,MAAM,CAACW,2BAA2B,EAAE;MACpCT,OAAO,CAACzM,IAAI,CAAC,eAAewM,MAAM,uCAAuCD,MAAM,CAACW,2BAA2B,KAAK,CAAC;MACjHT,OAAO,CAACzM,IAAI,CAAC,eAAewM,MAAM,6CAA6CD,MAAM,CAACW,2BAA2B,KAAK,CAAC,CAAC,CAAC;IAC7H;IACA,IAAIX,MAAM,CAACY,mCAAmC,EAAE;MAC5CV,OAAO,CAACzM,IAAI,CAAC,eAAewM,MAAM,gDAAgDD,MAAM,CAACY,mCAAmC,KAAK,CAAC;IACtI;IACA,IAAIZ,MAAM,CAACa,2BAA2B,EAAE;MACpCX,OAAO,CAACzM,IAAI,CAAC,eAAewM,MAAM,kDAAkDD,MAAM,CAACa,2BAA2B,KAAK,CAAC;MAC5HX,OAAO,CAACzM,IAAI,CAAC,eAAewM,MAAM,wDAAwDD,MAAM,CAACa,2BAA2B,KAAK,CAAC,CAAC,CAAC;IACxI;IACA,IAAIb,MAAM,CAACc,+BAA+B,EAAE;MACxCZ,OAAO,CAACzM,IAAI,CAAC,eAAewM,MAAM,mDAAmDD,MAAM,CAACc,+BAA+B,KAAK,CAAC;MACjIZ,OAAO,CAACzM,IAAI,CAAC,eAAewM,MAAM,yDAAyDD,MAAM,CAACc,+BAA+B,KAAK,CAAC,CAAC,CAAC;IAC7I;IACA,IAAId,MAAM,CAACe,+BAA+B,EAAE;MACxCb,OAAO,CAACzM,IAAI,CAAC,eAAewM,MAAM,uCAAuCD,MAAM,CAACe,+BAA+B,KAAK,CAAC;IACzH;IACA,IAAIf,MAAM,CAACgB,mBAAmB,EAAE;MAC5Bd,OAAO,CAACzM,IAAI,CAAC,eAAewM,MAAM,6GAA6GD,MAAM,CAACgB,mBAAmB,KAAK,CAAC;IACnL;IACA,IAAIhB,MAAM,CAACiB,mBAAmB,EAAE;MAC5Bf,OAAO,CAACzM,IAAI,CAAC,eAAewM,MAAM,mGAAmGD,MAAM,CAACiB,mBAAmB,KAAK,CAAC;IACzK;IACA;AACR;AACA;IACQ,MAAMC,wBAAwB,GAAG3Q,qBAAqB,CAACyP,MAAM,CAACmB,4BAA4B,EAAE5Q,qBAAqB,CAACyP,MAAM,CAACoB,oBAAoB,EAAEpB,MAAM,CAACqB,gBAAgB,IAAI,EAAE,CAAC,CAAC;IAC9K,IAAIH,wBAAwB,EAAE;MAAE;MAC5BhB,OAAO,CAACzM,IAAI,CAAC,eAAewM,MAAM,iEAAiEiB,wBAAwB,0BAA0B,CAAC;IAC1J;IACA,IAAIlB,MAAM,CAACqB,gBAAgB,EAAE;MAAE;MAC3BnB,OAAO,CAACzM,IAAI,CAAC;AACzB;AACA,kBAAkBwM,MAAM,wDAAwDD,MAAM,CAACqB,gBAAgB;AACvG,yDAAyDpB,MAAM,+DAA+DD,MAAM,CAACqB,gBAAgB;AACrJ,IAAI,CAAC;IACG;IACA,MAAMC,gCAAgC,GAAG/Q,qBAAqB,CAACyP,MAAM,CAACoB,oBAAoB,EAAEpB,MAAM,CAACuB,wBAAwB,IAAI,EAAE,CAAC;IAClI,IAAID,gCAAgC,EAAE;MAClCpB,OAAO,CAACzM,IAAI,CAAC,eAAewM,MAAM,4DAA4DqB,gCAAgC,2BAA2B,CAAC;IAC9J;IACA,IAAItB,MAAM,CAACoB,oBAAoB,EAAE;MAAE;MAC/BlB,OAAO,CAACzM,IAAI,CAAC,eAAewM,MAAM,oDAAoDD,MAAM,CAACoB,oBAAoB,2BAA2B,CAAC;IACjJ;IACA,IAAIpB,MAAM,CAACuB,wBAAwB,EAAE;MAAE;MACnCrB,OAAO,CAACzM,IAAI,CAAC,eAAewM,MAAM,mDAAmDD,MAAM,CAACuB,wBAAwB,2BAA2B,CAAC;IACpJ;IACA,IAAIvB,MAAM,CAACwB,gBAAgB,EAAE;MAAE;MAC3BtB,OAAO,CAACzM,IAAI,CAAC,eAAewM,MAAM,iDAAiDD,MAAM,CAACwB,gBAAgB,2BAA2B,CAAC;IAC1I;IACA,IAAIxB,MAAM,CAACyB,sBAAsB,EAAE;MAC/BvB,OAAO,CAACzM,IAAI,CAAC;AACzB,kBAAkBwM,MAAM;AACxB,kBAAkBA,MAAM;AACxB,kBAAkBA,MAAM,qDAAqDD,MAAM,CAACyB,sBAAsB;AAC1G,IAAI,CAAC;IACG;IACA,IAAIzB,MAAM,CAAC0B,yBAAyB,EAAE;MAClCxB,OAAO,CAACzM,IAAI,CAAC;AACzB,iBAAiBwM,MAAM;AACvB,iBAAiBA,MAAM;AACvB;AACA,wBAAwBD,MAAM,CAAC0B,yBAAyB;AACxD,KAAK,CAAC;MACMxB,OAAO,CAACzM,IAAI,CAAC;AACzB,iBAAiBwM,MAAM;AACvB,iBAAiBA,MAAM;AACvB;AACA,wBAAwBD,MAAM,CAAC0B,yBAAyB;AACxD,KAAK,CAAC;IACE;IACA,IAAI1B,MAAM,CAAC2B,kBAAkB,EAAE;MAC3BzB,OAAO,CAACzM,IAAI,CAAC;AACzB;AACA;AACA;AACA;AACA,qBAAqBuM,MAAM,CAAC2B,kBAAkB;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,CAAC;IACG;IACA,IAAI3B,MAAM,CAAC4B,2BAA2B,EAAE;MACpC1B,OAAO,CAACzM,IAAI,CAAC;AACzB;AACA;AACA;AACA,yBAAyBuM,MAAM,CAAC4B,2BAA2B;AAC3D;AACA,IAAI,CAAC;IACG;IACA,IAAI,CAAC9B,YAAY,CAAC+B,WAAW,GAAG3B,OAAO,CAAC4B,IAAI,CAAC,IAAI,CAAC;EACtD;AACJ;AACA,OAAO,MAAMC,kBAAkB,GAAG;EAC9B3B,mBAAmB,EAAE,SAAS;EAC9BE,6BAA6B,EAAE,SAAS;EACxCC,6BAA6B,EAAE,SAAS;EACxCC,iCAAiC,EAAE,SAAS;EAC5CW,4BAA4B,EAAE,SAAS;EACvCV,+BAA+B,EAAE,SAAS;EAC1CC,+BAA+B,EAAE,SAAS;EAC1CI,+BAA+B,EAAE,SAAS;EAC1CF,mCAAmC,EAAE,SAAS;EAC9CI,mBAAmB,EAAE,SAAS;EAC9BS,sBAAsB,EAAE,SAAS;EACjCC,yBAAyB,EAAE,SAAS;EACpCM,sBAAsB,EAAE,SAAS;EACjCC,8BAA8B,EAAE1Q,KAAK,CAAC2Q,OAAO,CAAC,SAAS,CAAC,CAACC,WAAW,CAAC,GAAG,CAAC,CAACzL,QAAQ,CAAC,CAAC;EACpFiL,kBAAkB,EAAEpQ,KAAK,CAAC2Q,OAAO,CAAC,SAAS,CAAC,CAACC,WAAW,CAAC,GAAG,CAAC,CAACzL,QAAQ,CAAC,CAAC;EACxEkL,2BAA2B,EAAErQ,KAAK,CAAC2Q,OAAO,CAAC,SAAS,CAAC,CAACC,WAAW,CAAC,IAAI,CAAC,CAACzL,QAAQ,CAAC,CAAC;EAClFyJ,cAAc,EAAE/G,SAAS;EACzBiH,mBAAmB,EAAEjH,SAAS;EAC9B2H,+BAA+B,EAAE3H,SAAS;EAC1CuH,2BAA2B,EAAEvH,SAAS;EACtCyH,2BAA2B,EAAEzH,SAAS;EACtC6H,mBAAmB,EAAE7H,SAAS;EAC9BiI,gBAAgB,EAAEjI,SAAS;EAC3BmI,wBAAwB,EAAEnI,SAAS;EACnCgI,oBAAoB,EAAEhI,SAAS;EAC/BoI,gBAAgB,EAAEpI,SAAS;EAC3BgJ,0BAA0B,EAAEhJ,SAAS;EACrCiJ,sBAAsB,EAAEjJ,SAAS;EACjCkJ,sBAAsB,EAAElJ;AAC5B,CAAC;AACD,MAAMmJ,cAAc,GAAG;EACnBC,eAAe,EAAE,IAAI;EACrBvE,YAAY,EAAE,IAAI;EAClB3F,wBAAwB,EAAE,IAAI;EAC9BmK,GAAG,EAAE;IACDC,UAAUA,CAAA,EAAG;MAAE,OAAO,IAAI;IAAE,CAAC;IAC7BC,WAAWA,CAAA,EAAG,CAAE,CAAC;IACjBC,UAAUA,CAAA,EAAG;MAAE,OAAO,KAAK;IAAE,CAAC;IAC9BC,IAAIA,CAAA,EAAG,CAAE,CAAC;IACV9Q,OAAOA,CAAA,EAAG,CAAE;EAChB;AACJ,CAAC;AACD;AACA,SAAS2N,4BAA4BA,CAACrO,KAAK,EAAEyR,KAAK,EAAE;EAChD,MAAM5P,KAAK,GAAG7B,KAAK,CAAC6C,OAAO,CAAC4O,KAAK,CAAC;EAClC,IAAI5P,KAAK,KAAK,CAAC,CAAC,EAAE;IACd,OAAO,EAAE;EACb;EACA,MAAMsC,MAAM,GAAG,EAAE;EACjB,IAAInF,CAAC,GAAG6C,KAAK,GAAG,CAAC;EACjB,OAAO7C,CAAC,IAAI,CAAC,IAAIgB,KAAK,CAAChB,CAAC,CAAC,KAAKyS,KAAK,IAAI5P,KAAK,GAAG7C,CAAC,CAAC,EAAE;IAC/CmF,MAAM,CAAC/B,IAAI,CAACpC,KAAK,CAAChB,CAAC,EAAE,CAAC,CAAC;EAC3B;EACAmF,MAAM,CAACuN,OAAO,CAAC,CAAC;EAChB1S,CAAC,GAAG6C,KAAK;EACT,OAAO7C,CAAC,GAAGgB,KAAK,CAACvB,MAAM,IAAIuB,KAAK,CAAChB,CAAC,CAAC,KAAKyS,KAAK,IAAIzS,CAAC,GAAG6C,KAAK,CAAC,EAAE;IACzDsC,MAAM,CAAC/B,IAAI,CAACpC,KAAK,CAAChB,CAAC,EAAE,CAAC,CAAC;EAC3B;EACA,OAAOmF,MAAM;AACjB;AACA;AACA;AACA;AACA;AACA,SAASG,WAAWA,CAACqN,GAAG,EAAEC,KAAK,EAAE;EAC7B,MAAMzN,MAAM,GAAG,EAAE;EACjB,IAAInF,CAAC,GAAG,CAAC;IAAEyE,CAAC,GAAG,CAAC;EAChB,OAAOzE,CAAC,GAAG2S,GAAG,CAAClT,MAAM,IAAIgF,CAAC,GAAGmO,KAAK,CAACnT,MAAM,EAAE;IACvC,IAAIO,CAAC,IAAI2S,GAAG,CAAClT,MAAM,EAAE;MACjB0F,MAAM,CAAC/B,IAAI,CAACwP,KAAK,CAACnO,CAAC,EAAE,CAAC,CAAC;IAC3B,CAAC,MACI,IAAIA,CAAC,IAAImO,KAAK,CAACnT,MAAM,EAAE;MACxB0F,MAAM,CAAC/B,IAAI,CAACuP,GAAG,CAAC3S,CAAC,EAAE,CAAC,CAAC;IACzB,CAAC,MACI,IAAI2S,GAAG,CAAC3S,CAAC,CAAC,KAAK4S,KAAK,CAACnO,CAAC,CAAC,EAAE;MAC1BU,MAAM,CAAC/B,IAAI,CAACuP,GAAG,CAAC3S,CAAC,CAAC,CAAC;MACnBA,CAAC,EAAE;MACHyE,CAAC,EAAE;MACH;IACJ,CAAC,MACI,IAAIkO,GAAG,CAAC3S,CAAC,CAAC,GAAG4S,KAAK,CAACnO,CAAC,CAAC,EAAE;MACxBU,MAAM,CAAC/B,IAAI,CAACuP,GAAG,CAAC3S,CAAC,EAAE,CAAC,CAAC;IACzB,CAAC,MACI;MACDmF,MAAM,CAAC/B,IAAI,CAACwP,KAAK,CAACnO,CAAC,EAAE,CAAC,CAAC;IAC3B;EACJ;EACA,OAAOU,MAAM;AACjB;AACA;AACA;AACA;AACA;AACA,SAASoK,kBAAkBA,CAACoD,GAAG,EAAEC,KAAK,EAAE;EACpC,MAAMzN,MAAM,GAAG,EAAE;EACjB,IAAInF,CAAC,GAAG,CAAC;IAAEyE,CAAC,GAAG,CAAC;EAChB,OAAOzE,CAAC,GAAG2S,GAAG,CAAClT,MAAM,IAAIgF,CAAC,GAAGmO,KAAK,CAACnT,MAAM,EAAE;IACvC,IAAIO,CAAC,IAAI2S,GAAG,CAAClT,MAAM,EAAE;MACjB0F,MAAM,CAAC/B,IAAI,CAACwP,KAAK,CAACnO,CAAC,EAAE,CAAC,CAAC;IAC3B,CAAC,MACI,IAAIA,CAAC,IAAImO,KAAK,CAACnT,MAAM,EAAE;MACxB0F,MAAM,CAAC/B,IAAI,CAACuP,GAAG,CAAC3S,CAAC,EAAE,CAAC,CAAC;IACzB,CAAC,MACI,IAAI2S,GAAG,CAAC3S,CAAC,CAAC,KAAK4S,KAAK,CAACnO,CAAC,CAAC,EAAE;MAC1BzE,CAAC,EAAE;MACHyE,CAAC,EAAE;MACH;IACJ,CAAC,MACI,IAAIkO,GAAG,CAAC3S,CAAC,CAAC,GAAG4S,KAAK,CAACnO,CAAC,CAAC,EAAE;MACxBU,MAAM,CAAC/B,IAAI,CAACuP,GAAG,CAAC3S,CAAC,EAAE,CAAC,CAAC;IACzB,CAAC,MACI;MACDyE,CAAC,EAAE;IACP;EACJ;EACA,OAAOU,MAAM;AACjB;AACA,MAAMD,WAAW,GAAGA,CAAC2N,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC;AACnC,MAAMC,gBAAgB,CAAC;EACnB3Q,WAAWA,CAAC4Q,WAAW,EAAEC,SAAS,EAAE;IAChC,IAAI,CAACD,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,SAAS,GAAGA,SAAS;EAC9B;EACA,IAAI1Q,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACyQ,WAAW;EAC3B;EACAvQ,cAAcA,CAACC,SAAS,EAAE;IACtB,OAAO,IAAI,CAACuQ,SAAS,CAAC9M,GAAG,CAACzG,CAAC,IAAIA,CAAC,CAAC+C,cAAc,CAACC,SAAS,CAAC,CAAC;EAC/D;EACAC,aAAaA,CAACC,OAAO,EAAEC,KAAK,EAAEC,YAAY,EAAEoQ,MAAM,EAAE;IAChD,IAAIlT,CAAC,GAAG,CAAC;IACT,KAAK,MAAMiE,QAAQ,IAAI,IAAI,CAACgP,SAAS,EAAE;MACnChP,QAAQ,CAACtB,aAAa,CAACC,OAAO,EAAEC,KAAK,EAAEC,YAAY,CAAC9C,CAAC,EAAE,CAAC,EAAEkT,MAAM,CAAC;IACrE;EACJ;EACAC,cAAcA,CAACvQ,OAAO,EAAEC,KAAK,EAAEC,YAAY,EAAEoQ,MAAM,EAAE;IACjD,IAAIlT,CAAC,GAAG,CAAC;IACT,KAAK,MAAMiE,QAAQ,IAAI,IAAI,CAACgP,SAAS,EAAE;MACnChP,QAAQ,CAACkP,cAAc,GAAGvQ,OAAO,EAAEC,KAAK,EAAEC,YAAY,CAAC9C,CAAC,CAAC,EAAEkT,MAAM,CAAC;MAClElT,CAAC,IAAI,CAAC;IACV;EACJ;EACA8D,eAAeA,CAAChB,YAAY,EAAE;IAC1B,IAAI9C,CAAC,GAAG,CAAC;IACT,KAAK,MAAMiE,QAAQ,IAAI,IAAI,CAACgP,SAAS,EAAE;MACnChP,QAAQ,CAACH,eAAe,CAAChB,YAAY,CAAC9C,CAAC,EAAE,CAAC,CAAC;IAC/C;EACJ;AACJ;AACA,MAAMoT,oBAAoB,CAAC;EACvBhR,WAAWA,CAACwJ,qBAAqB,EAAE;IAC/B,IAAI,CAACA,qBAAqB,GAAGA,qBAAqB;IAClD,IAAI,CAACrJ,UAAU,GAAG,MAAM;EAC5B;EACAE,cAAcA,CAACC,SAAS,EAAE;IACtB,OAAO;MAAEA,SAAS;MAAE+E,WAAW,EAAE,IAAIhG,eAAe,CAAC;IAAE,CAAC;EAC5D;EACAkB,aAAaA,CAACC,OAAO,EAAEC,KAAK,EAAEwQ,IAAI,EAAE;IAChC,MAAM1H,SAAS,GAAG,IAAI,CAACC,qBAAqB,CAACC,YAAY,CAACjJ,OAAO,CAAC;IAClE,MAAM0Q,UAAU,GAAI3H,SAAS,IAAI,OAAOA,SAAS,KAAK,QAAQ,GAAIA,SAAS,GAAGzJ,eAAe,CAACyJ,SAAS,CAAC;IACxG0H,IAAI,CAAC5L,WAAW,CAACC,GAAG,CAACzF,OAAO,CAACsR,MAAM,IAAI;MACnC,IAAI,CAACC,YAAY,CAACD,MAAM,CAACE,cAAc,CAACH,UAAU,CAAC,EAAED,IAAI,CAAC3Q,SAAS,CAAC;IACxE,CAAC,CAAC,CAAC;IACH,MAAMgR,SAAS,GAAG,IAAI,CAAC9H,qBAAqB,CAAC+H,YAAY,IAAI,IAAI,CAAC/H,qBAAqB,CAAC+H,YAAY,CAAC/Q,OAAO,CAAC;IAC7G,IAAI,OAAO8Q,SAAS,KAAK,QAAQ,EAAE;MAC/BL,IAAI,CAAC3Q,SAAS,CAACkD,YAAY,CAAC,YAAY,EAAE,GAAG8N,SAAS,EAAE,CAAC;IAC7D,CAAC,MACI;MACDL,IAAI,CAAC3Q,SAAS,CAACkR,eAAe,CAAC,YAAY,CAAC;IAChD;EACJ;EACAJ,YAAYA,CAAC7H,SAAS,EAAE/I,OAAO,EAAE;IAC7B,IAAI+I,SAAS,EAAE;MACX/I,OAAO,CAACgD,YAAY,CAAC,YAAY,EAAE+F,SAAS,CAAC;IACjD,CAAC,MACI;MACD/I,OAAO,CAACgR,eAAe,CAAC,YAAY,CAAC;IACzC;EACJ;EACAT,cAAcA,CAACvQ,OAAO,EAAEC,KAAK,EAAEC,YAAY,EAAEoQ,MAAM,EAAE;IACjDpQ,YAAY,CAAC2E,WAAW,CAACiE,KAAK,CAAC,CAAC;EACpC;EACA5H,eAAeA,CAAChB,YAAY,EAAE;IAC1BA,YAAY,CAAC2E,WAAW,CAAC/F,OAAO,CAAC,CAAC;EACtC;AACJ;AACA,MAAMmS,mBAAmB,CAAC;EACtBzR,WAAWA,CAAC0F,IAAI,EAAEsK,GAAG,EAAE;IACnB,IAAI,CAACtK,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACsK,GAAG,GAAGA,GAAG;EAClB;EACA0B,eAAeA,CAAClR,OAAO,EAAE;IACrB,MAAMuM,SAAS,GAAG,IAAI,CAACrH,IAAI,CAACiM,mBAAmB,CAAC,CAAC;IACjD,MAAMzP,QAAQ,GAAG6K,SAAS,CAACtL,OAAO,CAACjB,OAAO,CAAC,GAAG,CAAC,CAAC,GAAGuM,SAAS,GAAG,CAACvM,OAAO,CAAC;IACxE,OAAO0B,QAAQ;EACnB;EACA+N,UAAUA,CAACzP,OAAO,EAAE;IAChB,OAAO,IAAI,CAACwP,GAAG,CAACC,UAAU,CAACzP,OAAO,CAAC;EACvC;EACAoR,YAAYA,CAAC1P,QAAQ,EAAE2P,aAAa,EAAE;IAClC,IAAI,IAAI,CAAC7B,GAAG,CAAC4B,YAAY,EAAE;MACvB,OAAO,IAAI,CAAC5B,GAAG,CAAC4B,YAAY,CAAC1P,QAAQ,EAAE2P,aAAa,CAAC;IACzD;IACA,OAAOlL,SAAS;EACpB;EACAuJ,WAAWA,CAACe,IAAI,EAAEY,aAAa,EAAE;IAC7B,IAAI,CAAC7B,GAAG,CAACE,WAAW,GAAGe,IAAI,EAAEY,aAAa,CAAC;EAC/C;EACA1B,UAAUA,CAACc,IAAI,EAAEa,aAAa,EAAEC,WAAW,EAAEC,YAAY,EAAEH,aAAa,EAAE;IACtE,OAAO,IAAI,CAAC7B,GAAG,CAACG,UAAU,CAACc,IAAI,EAAEa,aAAa,EAAEC,WAAW,EAAEC,YAAY,EAAEH,aAAa,CAAC;EAC7F;EACAI,WAAWA,CAAChB,IAAI,EAAEa,aAAa,EAAEC,WAAW,EAAEF,aAAa,EAAE;IACzD,IAAI,CAAC7B,GAAG,CAACiC,WAAW,GAAGhB,IAAI,EAAEa,aAAa,EAAEC,WAAW,EAAEF,aAAa,CAAC;EAC3E;EACAK,SAASA,CAACL,aAAa,EAAE;IACrB,IAAI,CAAC7B,GAAG,CAACkC,SAAS,GAAGL,aAAa,CAAC;EACvC;EACAzB,IAAIA,CAACa,IAAI,EAAEa,aAAa,EAAEC,WAAW,EAAEC,YAAY,EAAEH,aAAa,EAAE;IAChE,IAAI,CAAC7B,GAAG,CAACI,IAAI,CAACa,IAAI,EAAEa,aAAa,EAAEC,WAAW,EAAEC,YAAY,EAAEH,aAAa,CAAC;EAChF;EACAvS,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC0Q,GAAG,CAAC1Q,OAAO,CAAC,CAAC;EACtB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM6S,IAAI,CAAC;EACd,IAAIC,gBAAgBA,CAAA,EAAG;IACnB,OAAOnT,KAAK,CAAC8E,GAAG,CAAC,IAAI,CAACsO,aAAa,CAACC,SAAS,CAAC,IAAI,CAACnL,KAAK,CAACnF,QAAQ,CAAC,EAAEqC,CAAC,IAAI,IAAI,CAACkO,WAAW,CAAClO,CAAC,CAAC,EAAE,IAAI,CAACgB,WAAW,CAAC;EACnH;EACA,IAAImN,oBAAoBA,CAAA,EAAG;IACvB,OAAOvT,KAAK,CAAC8E,GAAG,CAAC,IAAI,CAACsO,aAAa,CAACC,SAAS,CAAC,IAAI,CAACvF,SAAS,CAAC/K,QAAQ,CAAC,EAAEqC,CAAC,IAAI,IAAI,CAACkO,WAAW,CAAClO,CAAC,CAAC,EAAE,IAAI,CAACgB,WAAW,CAAC;EACvH;EACA,IAAIoN,KAAKA,CAAA,EAAG;IAAE,OAAO,IAAI,CAAC/O,IAAI,CAAC+O,KAAK;EAAE;EACtC,IAAIC,WAAWA,CAAA,EAAG;IAAE,OAAO,IAAI,CAAChP,IAAI,CAACgP,WAAW;EAAE;EAClD,IAAI1G,YAAYA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACtI,IAAI,CAACsI,YAAY;EAAE;EACpD,IAAIL,eAAeA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACjI,IAAI,CAACiI,eAAe;EAAE;EAC1D,IAAIM,kBAAkBA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACvI,IAAI,CAACuI,kBAAkB;EAAE;EAChE,IAAIX,SAASA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACqH,eAAe,CAACrH,SAAS;EAAE;EACzD,IAAIG,WAAWA,CAAA,EAAG;IAAE,OAAO,IAAI,CAAC/H,IAAI,CAAC+H,WAAW;EAAE;EAClD,IAAImH,WAAWA,CAAA,EAAG;IAAE,OAAO,IAAI,CAAClP,IAAI,CAACkP,WAAW;EAAE;EAClD,IAAIC,UAAUA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACnP,IAAI,CAACmP,UAAU;EAAE;EAChD,IAAIhH,YAAYA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACnI,IAAI,CAACmI,YAAY;EAAE;EACpD,IAAIK,KAAKA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACxI,IAAI,CAACwI,KAAK;EAAE;EACtC;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,IAAIR,aAAaA,CAAA,EAAG;IAChB,IAAIoH,0BAA0B,GAAG,KAAK;IACtC,MAAMC,WAAW,GAAG9T,KAAK,CAACmG,KAAK,CAAC,IAAI,CAACC,WAAW,CAACC,GAAG,CAAC,IAAIjH,UAAU,CAAC,IAAI,CAACqF,IAAI,CAAC6B,OAAO,EAAE,SAAS,CAAC,CAAC,CAACtD,KAAK,EAAEuD,CAAC,IAAIA,CAAC,CAACzB,GAAG,CAACM,CAAC,IAAI,IAAI/F,qBAAqB,CAAC+F,CAAC,CAAC,CAAC,CAClJoB,MAAM,CAACpB,CAAC,IAAIyO,0BAA0B,GAAGzO,CAAC,CAACyB,OAAO,KAAK,EAAE,CAAC,6BAA8BzB,CAAC,CAACgG,QAAQ,IAAIhG,CAAC,CAACyB,OAAO,KAAK,EAAE,CAAC,iBAAkB,CAAC,CAC1I/B,GAAG,CAACM,CAAC,IAAIrG,WAAW,CAACgL,IAAI,CAAC3E,CAAC,EAAE,IAAI,CAAC,CAAC,CACnCoB,MAAM,CAAC,MAAM,KAAK,CAAC,CAAC;IACzB,MAAMuN,SAAS,GAAG/T,KAAK,CAACmG,KAAK,CAAC,IAAI,CAACC,WAAW,CAACC,GAAG,CAAC,IAAIjH,UAAU,CAAC,IAAI,CAACqF,IAAI,CAAC6B,OAAO,EAAE,OAAO,CAAC,CAAC,CAACtD,KAAK,EAAEuD,CAAC,IAAIA,CAAC,CAACuD,OAAO,CAAC,MAAM+J,0BAA0B,GAAG,KAAK,CAAC,CACzJ/O,GAAG,CAACM,CAAC,IAAI,IAAI/F,qBAAqB,CAAC+F,CAAC,CAAC,CAAC,CACtCoB,MAAM,CAACpB,CAAC,IAAIA,CAAC,CAACyB,OAAO,KAAK,EAAE,CAAC,6BAA8BzB,CAAC,CAACgG,QAAQ,IAAIhG,CAAC,CAACyB,OAAO,KAAK,EAAE,CAAC,iBAAkB,CAAC,CAC7G/B,GAAG,CAACM,CAAC,IAAIrG,WAAW,CAACgL,IAAI,CAAC3E,CAAC,EAAE,IAAI,CAAC,CAAC,CACnCN,GAAG,CAAC,CAAC;MAAEnB;IAAa,CAAC,KAAK;MAC3B,MAAMuE,KAAK,GAAG,IAAI,CAACJ,QAAQ,CAAC,CAAC;MAC7B,MAAMtG,KAAK,GAAG0G,KAAK,CAAC9J,MAAM,GAAG8J,KAAK,CAAC,CAAC,CAAC,GAAGR,SAAS;MACjD,MAAMnG,OAAO,GAAG,OAAOC,KAAK,KAAK,WAAW,GAAG,IAAI,CAACiD,IAAI,CAAClD,OAAO,CAACC,KAAK,CAAC,GAAGkG,SAAS;MACnF,MAAM6F,MAAM,GAAG,OAAO/L,KAAK,KAAK,WAAW,GAAG,IAAI,CAACiD,IAAI,CAAC6G,UAAU,CAAC9J,KAAK,CAAC,GAAG,IAAI,CAACiD,IAAI,CAAC6B,OAAO;MAC7F,OAAO;QAAE9E,KAAK;QAAED,OAAO;QAAEgM,MAAM;QAAE5J;MAAa,CAAC;IACnD,CAAC,CAAC,CAAC;IACH,MAAMqQ,SAAS,GAAGhU,KAAK,CAACmG,KAAK,CAAC,IAAI,CAAC1B,IAAI,CAACgI,aAAa,EAAElG,CAAC,IAAIA,CAAC,CAACC,MAAM,CAACyN,CAAC,IAAI,CAACJ,0BAA0B,CAAC,CACjG/O,GAAG,CAAC,CAAC;MAAEvD,OAAO;MAAEC,KAAK;MAAEmC;IAAa,CAAC,MAAM;MAAEpC,OAAO;MAAEC,KAAK;MAAE+L,MAAM,EAAE,IAAI5M,kBAAkB,CAAC1B,SAAS,CAAC,IAAI,CAACwF,IAAI,CAAC6B,OAAO,CAAC,EAAE3C,YAAY,CAAC;MAAEA;IAAa,CAAC,CAAC,CAAC,CAAC;IAC/J,OAAO3D,KAAK,CAACoK,GAAG,CAAC0J,WAAW,EAAEC,SAAS,EAAEC,SAAS,CAAC;EACvD;EACA,IAAI9N,SAASA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACE,WAAW,CAACC,GAAG,CAAC,IAAIjH,UAAU,CAAC,IAAI,CAACqF,IAAI,CAAC6B,OAAO,EAAE,SAAS,CAAC,CAAC,CAACtD,KAAK;EAAE;EACnG,IAAIkR,UAAUA,CAAA,EAAG;IAAE,OAAOlU,KAAK,CAACmU,MAAM,CAAC,IAAI,CAAC/N,WAAW,CAACC,GAAG,CAAC,IAAIjH,UAAU,CAAC,IAAI,CAACqF,IAAI,CAAC6B,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC,CAACtD,KAAK,CAAC;EAAE;EACtH,IAAIoR,SAASA,CAAA,EAAG;IAAE,OAAOpU,KAAK,CAACmU,MAAM,CAAC,IAAI,CAAC/N,WAAW,CAACC,GAAG,CAAC,IAAIjH,UAAU,CAAC,IAAI,CAACqF,IAAI,CAAC6B,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,CAACtD,KAAK,CAAC;EAAE;EACpHjC,WAAWA,CAACsT,IAAI,EAAEhT,SAAS,EAAEiT,eAAe,EAAE1C,SAAS,EAAE2C,QAAQ,GAAG1D,cAAc,EAAE;IAChF,IAAI,CAACwD,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACE,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACrM,KAAK,GAAG,IAAIxF,KAAK,CAAC,SAAS,CAAC;IACjC,IAAI,CAAC6K,MAAM,GAAG,IAAI7K,KAAK,CAAC,QAAQ,CAAC;IACjC,IAAI,CAAC0Q,aAAa,GAAG,IAAInT,aAAa,CAAC,CAAC;IACxC,IAAI,CAACuU,UAAU,GAAG,EAAE;IACpB,IAAI,CAACpO,WAAW,GAAG,IAAIhG,eAAe,CAAC,CAAC;IACxC,IAAI,CAACqU,aAAa,GAAG,IAAI1U,OAAO,CAAC,CAAC;IAClC,IAAI,CAAC2U,YAAY,GAAG,IAAI,CAACD,aAAa,CAACzR,KAAK;IAC5C,MAAM2R,IAAI,GAAG,IAAI,CAACJ,QAAQ,CAAChK,qBAAqB,IAAI,IAAI,CAACgK,QAAQ,CAAChK,qBAAqB,CAACqK,aAAa,GAAG,IAAI,CAACL,QAAQ,CAAChK,qBAAqB,EAAEqK,aAAa,CAAC,CAAC,GAAG,MAAM;IACrK,IAAI,CAAC9G,SAAS,GAAG,IAAIzJ,cAAc,CAACsQ,IAAI,KAAK,SAAS,CAAC;IACvD,MAAME,aAAa,GAAG,CAAC,IAAI,CAAC3M,KAAK,CAACtF,QAAQ,EAAE,IAAI,CAACkL,SAAS,CAAClL,QAAQ,CAAC;IACpE,IAAI,CAAC2H,qBAAqB,GAAGgK,QAAQ,CAAChK,qBAAqB;IAC3D,IAAI,IAAI,CAACA,qBAAqB,EAAE;MAC5BsK,aAAa,CAAC9S,IAAI,CAAC,IAAIgQ,oBAAoB,CAAC,IAAI,CAACxH,qBAAqB,CAAC,CAAC;MACxE,IAAI,CAACA,qBAAqB,CAACuK,2BAA2B,GAAG,IAAI,CAACA,2BAA2B,EAAE,IAAI,EAAE,IAAI,CAAC1O,WAAW,CAAC;IACtH;IACAwL,SAAS,GAAGA,SAAS,CAAC9M,GAAG,CAACzG,CAAC,IAAI,IAAIqT,gBAAgB,CAACrT,CAAC,CAAC6C,UAAU,EAAE,CAAC,GAAG2T,aAAa,EAAExW,CAAC,CAAC,CAAC,CAAC;IACzF,MAAM0W,WAAW,GAAG;MAChB,GAAGR,QAAQ;MACXxD,GAAG,EAAEwD,QAAQ,CAACxD,GAAG,IAAI,IAAIyB,mBAAmB,CAAC,IAAI,EAAE+B,QAAQ,CAACxD,GAAG;IACnE,CAAC;IACD,IAAI,CAACtM,IAAI,GAAG,IAAI,CAACuQ,cAAc,CAAC3T,SAAS,EAAEiT,eAAe,EAAE1C,SAAS,EAAEmD,WAAW,CAAC;IACnF,IAAI,CAACtQ,IAAI,CAAC6B,OAAO,CAAC/B,YAAY,CAAC,MAAM,EAAEoQ,IAAI,CAAC;IAC5C,IAAIJ,QAAQ,CAACU,eAAe,EAAE;MAC1B,IAAI,CAACA,eAAe,GAAGV,QAAQ,CAACU,eAAe,CAAC,IAAI,CAACxQ,IAAI,CAAC+O,KAAK,CAAC;IACpE,CAAC,MACI;MACD,MAAMpF,YAAY,GAAGtP,gBAAgB,CAAC,IAAI,CAAC2F,IAAI,CAAC6B,OAAO,CAAC;MACxD,IAAI,CAAC2O,eAAe,GAAG,IAAI9G,sBAAsB,CAACC,YAAY,EAAE,IAAI,CAAC3J,IAAI,CAAC+O,KAAK,CAAC;IACpF;IACA,IAAI,CAAC0B,UAAU,GAAG,IAAI1V,kBAAkB,CAAC,CACrC,IAAIgF,eAAe,CAAC,IAAI,CAAC0D,KAAK,EAAE,IAAI,CAACzD,IAAI,EAAE8P,QAAQ,CAAC7P,gBAAgB,CAAC,EACrE,IAAIF,eAAe,CAAC,IAAI,CAACsJ,SAAS,EAAE,IAAI,CAACrJ,IAAI,EAAE8P,QAAQ,CAAC7P,gBAAgB,CAAC,EACzE,IAAIF,eAAe,CAAC,IAAI,CAAC+I,MAAM,EAAE,IAAI,CAAC9I,IAAI,EAAE8P,QAAQ,CAAC7P,gBAAgB,CAAC,EACtE,IAAI,CAACD,IAAI,CACZ,CAAC;IACF,IAAI,CAAC2B,WAAW,CAACC,GAAG,CAAC,IAAI,CAAC6B,KAAK,CAAC;IAChC,IAAI,CAAC9B,WAAW,CAACC,GAAG,CAAC,IAAI,CAACyH,SAAS,CAAC;IACpC,IAAI,CAAC1H,WAAW,CAACC,GAAG,CAAC,IAAI,CAACkH,MAAM,CAAC;IACjC,IAAI,CAACnH,WAAW,CAACC,GAAG,CAAC,IAAI,CAAC5B,IAAI,CAAC;IAC/B,IAAI,CAAC2B,WAAW,CAACC,GAAG,CAAC,IAAI,CAACoO,aAAa,CAAC;IACxC,IAAI,CAACrO,WAAW,CAACC,GAAG,CAAC,IAAI6E,kBAAkB,CAAC,IAAI,EAAE,IAAI,CAACzG,IAAI,CAAC,CAAC;IAC7D,IAAI,OAAO8P,QAAQ,CAACzD,eAAe,KAAK,SAAS,IAAIyD,QAAQ,CAACzD,eAAe,EAAE;MAC3E,IAAI,CAACqE,kBAAkB,GAAG,IAAIlP,kBAAkB,CAAC,IAAI,EAAE,IAAI,CAACxB,IAAI,EAAE8P,QAAQ,CAAC;MAC3E,IAAI,CAACnO,WAAW,CAACC,GAAG,CAAC,IAAI,CAAC8O,kBAAkB,CAAC;IACjD;IACA,IAAIZ,QAAQ,CAAC1L,+BAA+B,EAAE;MAC1C,MAAME,QAAQ,GAAGwL,QAAQ,CAACa,0BAA0B,IAAI3M,iCAAiC;MACzF,IAAI,CAAC4M,wBAAwB,GAAG,IAAIzM,wBAAwB,CAAC,IAAI,EAAE,IAAI,CAACnE,IAAI,EAAE8P,QAAQ,CAAC1L,+BAA+B,EAAE0L,QAAQ,CAACzL,6BAA6B,KAAK,MAAM,IAAI,CAAC,EAAEC,QAAQ,CAAC;MACzL,IAAI,CAAC3C,WAAW,CAACC,GAAG,CAAC,IAAI,CAACgP,wBAAwB,CAAC;IACvD;IACA,IAAI,CAAC3B,eAAe,GAAG,IAAI,CAAC4B,qBAAqB,CAACf,QAAQ,CAAC;IAC3D,IAAI,CAACnO,WAAW,CAACC,GAAG,CAAC,IAAI,CAACqN,eAAe,CAAC;IAC1C,IAAI,CAACP,gBAAgB,CAAC,IAAI,CAACoC,cAAc,EAAE,IAAI,EAAE,IAAI,CAACnP,WAAW,CAAC;IAClE,IAAI,CAACmN,oBAAoB,CAAC,IAAI,CAACiC,kBAAkB,EAAE,IAAI,EAAE,IAAI,CAACpP,WAAW,CAAC;IAC1E,IAAI,IAAI,CAACmE,qBAAqB,EAAE;MAC5B,IAAI,CAACD,SAAS,GAAG,IAAI,CAACC,qBAAqB,CAACkL,kBAAkB,CAAC,CAAC;IACpE;IACA,IAAI,IAAI,CAAClB,QAAQ,CAAC3N,wBAAwB,KAAK,KAAK,EAAE;MAClD,IAAI,CAACnC,IAAI,CAAC6B,OAAO,CAAC/B,YAAY,CAAC,sBAAsB,EAAE,MAAM,CAAC;IAClE;EACJ;EACAyQ,cAAcA,CAAC3T,SAAS,EAAEiT,eAAe,EAAE1C,SAAS,EAAEmD,WAAW,EAAE;IAC/D,OAAO,IAAIrU,QAAQ,CAACW,SAAS,EAAEiT,eAAe,EAAE1C,SAAS,EAAEmD,WAAW,CAAC;EAC3E;EACAO,qBAAqBA,CAAC5O,OAAO,EAAE;IAC3B,OAAO,IAAIyF,eAAe,CAAC,IAAI,CAAC;EACpC;EACA3E,aAAaA,CAACC,aAAa,GAAG,CAAC,CAAC,EAAE;IAC9B,IAAI,CAAC8M,QAAQ,GAAG;MAAE,GAAG,IAAI,CAACA,QAAQ;MAAE,GAAG9M;IAAc,CAAC;IACtD,IAAI,CAAC4N,wBAAwB,EAAE7N,aAAa,CAAC,IAAI,CAAC+M,QAAQ,CAAC;IAC3D,IAAI,IAAI,CAACA,QAAQ,CAACjI,2BAA2B,KAAK5E,SAAS,EAAE;MACzD,IAAI,IAAI,CAAC6M,QAAQ,CAAC3N,wBAAwB,EAAE;QACxC,IAAI,CAACnC,IAAI,CAAC6B,OAAO,CAAC/B,YAAY,CAAC,sBAAsB,EAAE,MAAM,CAAC;MAClE,CAAC,MACI;QACD,IAAI,CAACE,IAAI,CAAC6B,OAAO,CAACiM,eAAe,CAAC,sBAAsB,CAAC;MAC7D;IACJ;IACA,IAAI,CAACmB,eAAe,CAAClM,aAAa,CAACC,aAAa,CAAC;IACjD,IAAI,CAAC0N,kBAAkB,EAAE3N,aAAa,CAACC,aAAa,CAAC;IACrD,IAAI,CAAChD,IAAI,CAAC+C,aAAa,CAACC,aAAa,CAAC;EAC1C;EACA,IAAIf,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC6N,QAAQ;EACxB;EACAtS,MAAMA,CAACC,KAAK,EAAEC,WAAW,EAAEc,QAAQ,GAAG,EAAE,EAAE;IACtC,IAAIf,KAAK,GAAG,CAAC,IAAIA,KAAK,GAAG,IAAI,CAACuC,IAAI,CAACrG,MAAM,EAAE;MACvC,MAAM,IAAIqC,SAAS,CAAC,IAAI,CAAC4T,IAAI,EAAE,wBAAwBnS,KAAK,EAAE,CAAC;IACnE;IACA,IAAIC,WAAW,GAAG,CAAC,EAAE;MACjB,MAAM,IAAI1B,SAAS,CAAC,IAAI,CAAC4T,IAAI,EAAE,yBAAyBlS,WAAW,EAAE,CAAC;IAC1E;IACA,IAAIA,WAAW,KAAK,CAAC,IAAIc,QAAQ,CAAC7E,MAAM,KAAK,CAAC,EAAE;MAC5C;IACJ;IACA,IAAI,CAACgV,aAAa,CAACsC,YAAY,CAAC,MAAM,IAAI,CAACR,UAAU,CAACjT,MAAM,CAACC,KAAK,EAAEC,WAAW,EAAEc,QAAQ,CAAC,CAAC;EAC/F;EACA0S,QAAQA,CAAA,EAAG;IACP,IAAI,CAAClR,IAAI,CAACkR,QAAQ,CAAC,CAAC;EACxB;EACApU,OAAOA,CAACC,KAAK,EAAE;IACX,OAAO,IAAI,CAACiD,IAAI,CAAClD,OAAO,CAACC,KAAK,CAAC;EACnC;EACAgB,OAAOA,CAACjB,OAAO,EAAE;IACb,OAAO,IAAI,CAACkD,IAAI,CAACjC,OAAO,CAACjB,OAAO,CAAC;EACrC;EACAqU,OAAOA,CAACC,QAAQ,EAAE;IACd,OAAO,IAAI,CAACpR,IAAI,CAACmR,OAAO,CAACC,QAAQ,CAAC;EACtC;EACA,IAAIzX,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACqG,IAAI,CAACrG,MAAM;EAC3B;EACA,IAAI0X,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACrR,IAAI,CAACqR,aAAa;EAClC;EACA,IAAIC,wBAAwBA,CAAA,EAAG;IAC3B,OAAO,IAAI,CAACtR,IAAI,CAACsR,wBAAwB;EAC7C;EACA,IAAIC,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACvR,IAAI,CAACwR,YAAY,CAAC,CAAC;EACnC;EACA,IAAID,SAASA,CAACA,SAAS,EAAE;IACrB,IAAI,CAACvR,IAAI,CAACyR,YAAY,CAACF,SAAS,CAAC;EACrC;EACA,IAAIG,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAAC1R,IAAI,CAAC0R,YAAY;EACjC;EACA,IAAIC,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAAC3R,IAAI,CAAC2R,YAAY;EACjC;EACA,IAAIC,iBAAiBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAAC5R,IAAI,CAAC4R,iBAAiB;EACtC;EACA,IAAI/L,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACkK,UAAU;EAC1B;EACA,IAAIlK,SAASA,CAAC8G,KAAK,EAAE;IACjB,IAAI,CAACoD,UAAU,GAAGpD,KAAK;IACvB,IAAI,CAAC3M,IAAI,CAAC6B,OAAO,CAAC/B,YAAY,CAAC,YAAY,EAAE6M,KAAK,CAAC;EACvD;EACAhE,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC3I,IAAI,CAAC6B,OAAO,CAAC4B,KAAK,CAAC;MAAEoO,aAAa,EAAE;IAAK,CAAC,CAAC;EACpD;EACAC,MAAMA,CAAC1E,MAAM,EAAE2E,KAAK,EAAE;IAClB,IAAI,CAAC/R,IAAI,CAAC8R,MAAM,CAAC1E,MAAM,EAAE2E,KAAK,CAAC;EACnC;EACA3O,YAAYA,CAACtF,OAAO,EAAEoB,YAAY,EAAE;IAChC,KAAK,MAAMnC,KAAK,IAAIe,OAAO,EAAE;MACzB,IAAIf,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAI,IAAI,CAACpD,MAAM,EAAE;QACnC,MAAM,IAAIqC,SAAS,CAAC,IAAI,CAAC4T,IAAI,EAAE,iBAAiB7S,KAAK,EAAE,CAAC;MAC5D;IACJ;IACA,IAAI,CAACsM,SAAS,CAACpK,GAAG,CAACnB,OAAO,EAAEoB,YAAY,CAAC;EAC7C;EACA2E,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAACwF,SAAS,CAAC3J,GAAG,CAAC,CAAC;EAC/B;EACAuO,mBAAmBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACpK,YAAY,CAAC,CAAC,CAACxD,GAAG,CAACnG,CAAC,IAAI,IAAI,CAAC8F,IAAI,CAAClD,OAAO,CAAC5C,CAAC,CAAC,CAAC;EAC7D;EACAqJ,SAASA,CAACxG,KAAK,EAAE;IACb,IAAI,OAAOA,KAAK,KAAK,WAAW,EAAE;MAC9B,IAAI,CAAC+L,MAAM,CAAC7J,GAAG,CAAC,EAAE,CAAC;MACnB;IACJ;IACA,IAAIlC,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAI,IAAI,CAACpD,MAAM,EAAE;MACnC,MAAM,IAAIqC,SAAS,CAAC,IAAI,CAAC4T,IAAI,EAAE,iBAAiB7S,KAAK,EAAE,CAAC;IAC5D;IACA,IAAI,CAAC+L,MAAM,CAAC7J,GAAG,CAAC,CAAClC,KAAK,CAAC,CAAC;EAC5B;EACAgM,SAASA,CAAA,EAAG;IACR,OAAO9N,cAAc,CAAC,IAAI,CAAC6N,MAAM,CAACpJ,GAAG,CAAC,CAAC,EAAEuD,SAAS,CAAC;EACvD;EACA+O,gBAAgBA,CAAA,EAAG;IACf,MAAMlJ,MAAM,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC;IAC/B,OAAO,OAAOD,MAAM,KAAK,WAAW,GAAG7F,SAAS,GAAG,IAAI,CAACnG,OAAO,CAACgM,MAAM,CAAC;EAC3E;EACAxC,QAAQA,CAACxI,OAAO,EAAEoB,YAAY,EAAE;IAC5B,KAAK,MAAMnC,KAAK,IAAIe,OAAO,EAAE;MACzB,IAAIf,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAI,IAAI,CAACpD,MAAM,EAAE;QACnC,MAAM,IAAIqC,SAAS,CAAC,IAAI,CAAC4T,IAAI,EAAE,iBAAiB7S,KAAK,EAAE,CAAC;MAC5D;IACJ;IACA,IAAI,CAAC0G,KAAK,CAACxE,GAAG,CAACnB,OAAO,EAAEoB,YAAY,CAAC;EACzC;EACAwE,SAASA,CAACuO,CAAC,GAAG,CAAC,EAAEC,IAAI,GAAG,KAAK,EAAEhT,YAAY,EAAE6C,MAAM,EAAE;IACjD,IAAI,IAAI,CAACpI,MAAM,KAAK,CAAC,EAAE;MACnB;IACJ;IACA,MAAM8J,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC/D,GAAG,CAAC,CAAC;IAC9B,MAAM3C,KAAK,GAAG,IAAI,CAACoV,aAAa,CAAC1O,KAAK,CAAC9J,MAAM,GAAG,CAAC,GAAG8J,KAAK,CAAC,CAAC,CAAC,GAAGwO,CAAC,GAAG,CAAC,EAAEC,IAAI,EAAEnQ,MAAM,CAAC;IACnF,IAAIhF,KAAK,GAAG,CAAC,CAAC,EAAE;MACZ,IAAI,CAACuJ,QAAQ,CAAC,CAACvJ,KAAK,CAAC,EAAEmC,YAAY,CAAC;IACxC;EACJ;EACAoE,aAAaA,CAAC2O,CAAC,GAAG,CAAC,EAAEC,IAAI,GAAG,KAAK,EAAEhT,YAAY,EAAE6C,MAAM,EAAE;IACrD,IAAI,IAAI,CAACpI,MAAM,KAAK,CAAC,EAAE;MACnB;IACJ;IACA,MAAM8J,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC/D,GAAG,CAAC,CAAC;IAC9B,MAAM3C,KAAK,GAAG,IAAI,CAACqV,iBAAiB,CAAC3O,KAAK,CAAC9J,MAAM,GAAG,CAAC,GAAG8J,KAAK,CAAC,CAAC,CAAC,GAAGwO,CAAC,GAAG,CAAC,EAAEC,IAAI,EAAEnQ,MAAM,CAAC;IACvF,IAAIhF,KAAK,GAAG,CAAC,CAAC,EAAE;MACZ,IAAI,CAACuJ,QAAQ,CAAC,CAACvJ,KAAK,CAAC,EAAEmC,YAAY,CAAC;IACxC;EACJ;EACM0E,aAAaA,CAAC1E,YAAY,EAAE6C,MAAM,EAAE;IAAA,IAAAsQ,KAAA;IAAA,OAAAC,iBAAA;MACtC,IAAIC,aAAa,GAAGF,KAAI,CAACrS,IAAI,CAACmR,OAAO,CAACkB,KAAI,CAACrS,IAAI,CAACwR,YAAY,CAAC,CAAC,GAAGa,KAAI,CAACrS,IAAI,CAAC2R,YAAY,CAAC;MACxFY,aAAa,GAAGA,aAAa,KAAK,CAAC,GAAG,CAAC,GAAGA,aAAa,GAAG,CAAC;MAC3D,MAAMC,4BAA4B,GAAGH,KAAI,CAAChP,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;MACvD,IAAImP,4BAA4B,KAAKD,aAAa,KAAKC,4BAA4B,KAAKvP,SAAS,IAAIsP,aAAa,GAAGC,4BAA4B,CAAC,EAAE;QAChJ,MAAMC,iBAAiB,GAAGJ,KAAI,CAACD,iBAAiB,CAACG,aAAa,EAAE,KAAK,EAAExQ,MAAM,CAAC;QAC9E,IAAI0Q,iBAAiB,GAAG,CAAC,CAAC,IAAID,4BAA4B,KAAKC,iBAAiB,EAAE;UAC9EJ,KAAI,CAAC/L,QAAQ,CAAC,CAACmM,iBAAiB,CAAC,EAAEvT,YAAY,CAAC;QACpD,CAAC,MACI;UACDmT,KAAI,CAAC/L,QAAQ,CAAC,CAACiM,aAAa,CAAC,EAAErT,YAAY,CAAC;QAChD;MACJ,CAAC,MACI;QACD,MAAMwT,iBAAiB,GAAGL,KAAI,CAACrS,IAAI,CAACwR,YAAY,CAAC,CAAC;QAClD,IAAImB,iBAAiB,GAAGD,iBAAiB,GAAGL,KAAI,CAACrS,IAAI,CAAC2R,YAAY;QAClE,IAAIY,aAAa,GAAGC,4BAA4B,EAAE;UAC9C;UACAG,iBAAiB,IAAIN,KAAI,CAACrS,IAAI,CAAC4S,aAAa,CAACL,aAAa,CAAC;QAC/D;QACAF,KAAI,CAACrS,IAAI,CAACyR,YAAY,CAACkB,iBAAiB,CAAC;QACzC,IAAIN,KAAI,CAACrS,IAAI,CAACwR,YAAY,CAAC,CAAC,KAAKkB,iBAAiB,EAAE;UAChDL,KAAI,CAAC/L,QAAQ,CAAC,EAAE,CAAC;UACjB;UACA,MAAMnL,OAAO,CAAC,CAAC,CAAC;UAChB,MAAMkX,KAAI,CAACzO,aAAa,CAAC1E,YAAY,EAAE6C,MAAM,CAAC;QAClD;MACJ;IAAC;EACL;EACM4B,iBAAiBA,CAAAkP,EAAA,EAAAC,GAAA,EAAgD;IAAA,IAAAC,MAAA;IAAA,OAAAT,iBAAA,YAA/CpT,YAAY,EAAE6C,MAAM,EAAEiR,aAAa,GAAGA,CAAA,KAAM,CAAC;MACjE,IAAIC,cAAc;MAClB,MAAMC,UAAU,GAAGF,aAAa,CAAC,CAAC;MAClC,MAAMzB,SAAS,GAAGwB,MAAI,CAAC/S,IAAI,CAACwR,YAAY,CAAC,CAAC,GAAG0B,UAAU;MACvD,IAAI3B,SAAS,KAAK,CAAC,EAAE;QACjB0B,cAAc,GAAGF,MAAI,CAAC/S,IAAI,CAACmR,OAAO,CAACI,SAAS,CAAC;MACjD,CAAC,MACI;QACD0B,cAAc,GAAGF,MAAI,CAAC/S,IAAI,CAACmT,UAAU,CAAC5B,SAAS,GAAG,CAAC,CAAC;MACxD;MACA,MAAMiB,4BAA4B,GAAGO,MAAI,CAAC1P,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;MACvD,IAAImP,4BAA4B,KAAKS,cAAc,KAAKT,4BAA4B,KAAKvP,SAAS,IAAIuP,4BAA4B,IAAIS,cAAc,CAAC,EAAE;QACnJ,MAAMG,kBAAkB,GAAGL,MAAI,CAACZ,aAAa,CAACc,cAAc,EAAE,KAAK,EAAElR,MAAM,CAAC;QAC5E,IAAIqR,kBAAkB,GAAG,CAAC,CAAC,IAAIZ,4BAA4B,KAAKY,kBAAkB,EAAE;UAChFL,MAAI,CAACzM,QAAQ,CAAC,CAAC8M,kBAAkB,CAAC,EAAElU,YAAY,CAAC;QACrD,CAAC,MACI;UACD6T,MAAI,CAACzM,QAAQ,CAAC,CAAC2M,cAAc,CAAC,EAAE/T,YAAY,CAAC;QACjD;MACJ,CAAC,MACI;QACD,MAAMwT,iBAAiB,GAAGnB,SAAS;QACnCwB,MAAI,CAAC/S,IAAI,CAACyR,YAAY,CAACF,SAAS,GAAGwB,MAAI,CAAC/S,IAAI,CAAC2R,YAAY,GAAGuB,UAAU,CAAC;QACvE,IAAIH,MAAI,CAAC/S,IAAI,CAACwR,YAAY,CAAC,CAAC,GAAGwB,aAAa,CAAC,CAAC,KAAKN,iBAAiB,EAAE;UAClEK,MAAI,CAACzM,QAAQ,CAAC,EAAE,CAAC;UACjB;UACA,MAAMnL,OAAO,CAAC,CAAC,CAAC;UAChB,MAAM4X,MAAI,CAACpP,iBAAiB,CAACzE,YAAY,EAAE6C,MAAM,EAAEiR,aAAa,CAAC;QACrE;MACJ;IAAC,GAAAK,KAAA,OAAA3Z,SAAA;EACL;EACA4Z,SAASA,CAACpU,YAAY,EAAE6C,MAAM,EAAE;IAC5B,IAAI,IAAI,CAACpI,MAAM,KAAK,CAAC,EAAE;MACnB;IACJ;IACA,MAAMoD,KAAK,GAAG,IAAI,CAACqV,iBAAiB,CAAC,IAAI,CAACzY,MAAM,GAAG,CAAC,EAAE,KAAK,EAAEoI,MAAM,CAAC;IACpE,IAAIhF,KAAK,GAAG,CAAC,CAAC,EAAE;MACZ,IAAI,CAACuJ,QAAQ,CAAC,CAACvJ,KAAK,CAAC,EAAEmC,YAAY,CAAC;IACxC;EACJ;EACAqU,UAAUA,CAACrU,YAAY,EAAE6C,MAAM,EAAE;IAC7B,IAAI,CAACyR,QAAQ,CAAC,CAAC,EAAEtU,YAAY,EAAE6C,MAAM,CAAC;EAC1C;EACAyR,QAAQA,CAACvB,CAAC,EAAE/S,YAAY,EAAE6C,MAAM,EAAE;IAC9B,IAAI,IAAI,CAACpI,MAAM,KAAK,CAAC,EAAE;MACnB;IACJ;IACA,MAAMoD,KAAK,GAAG,IAAI,CAACoV,aAAa,CAACF,CAAC,EAAE,KAAK,EAAElQ,MAAM,CAAC;IAClD,IAAIhF,KAAK,GAAG,CAAC,CAAC,EAAE;MACZ,IAAI,CAACuJ,QAAQ,CAAC,CAACvJ,KAAK,CAAC,EAAEmC,YAAY,CAAC;IACxC;EACJ;EACAiT,aAAaA,CAACpV,KAAK,EAAEmV,IAAI,GAAG,KAAK,EAAEnQ,MAAM,EAAE;IACvC,KAAK,IAAI7H,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACP,MAAM,EAAEO,CAAC,EAAE,EAAE;MAClC,IAAI6C,KAAK,IAAI,IAAI,CAACpD,MAAM,IAAI,CAACuY,IAAI,EAAE;QAC/B,OAAO,CAAC,CAAC;MACb;MACAnV,KAAK,GAAGA,KAAK,GAAG,IAAI,CAACpD,MAAM;MAC3B,IAAI,CAACoI,MAAM,IAAIA,MAAM,CAAC,IAAI,CAACjF,OAAO,CAACC,KAAK,CAAC,CAAC,EAAE;QACxC,OAAOA,KAAK;MAChB;MACAA,KAAK,EAAE;IACX;IACA,OAAO,CAAC,CAAC;EACb;EACAqV,iBAAiBA,CAACrV,KAAK,EAAEmV,IAAI,GAAG,KAAK,EAAEnQ,MAAM,EAAE;IAC3C,KAAK,IAAI7H,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACP,MAAM,EAAEO,CAAC,EAAE,EAAE;MAClC,IAAI6C,KAAK,GAAG,CAAC,IAAI,CAACmV,IAAI,EAAE;QACpB,OAAO,CAAC,CAAC;MACb;MACAnV,KAAK,GAAG,CAAC,IAAI,CAACpD,MAAM,GAAIoD,KAAK,GAAG,IAAI,CAACpD,MAAO,IAAI,IAAI,CAACA,MAAM;MAC3D,IAAI,CAACoI,MAAM,IAAIA,MAAM,CAAC,IAAI,CAACjF,OAAO,CAACC,KAAK,CAAC,CAAC,EAAE;QACxC,OAAOA,KAAK;MAChB;MACAA,KAAK,EAAE;IACX;IACA,OAAO,CAAC,CAAC;EACb;EACAsG,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACI,KAAK,CAAC/D,GAAG,CAAC,CAAC;EAC3B;EACA+T,kBAAkBA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACpQ,QAAQ,CAAC,CAAC,CAAChD,GAAG,CAACnG,CAAC,IAAI,IAAI,CAAC8F,IAAI,CAAClD,OAAO,CAAC5C,CAAC,CAAC,CAAC;EACzD;EACAsJ,MAAMA,CAACzG,KAAK,EAAE2W,WAAW,EAAER,UAAU,GAAG,CAAC,EAAE;IACvC,IAAInW,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAI,IAAI,CAACpD,MAAM,EAAE;MACnC,MAAM,IAAIqC,SAAS,CAAC,IAAI,CAAC4T,IAAI,EAAE,iBAAiB7S,KAAK,EAAE,CAAC;IAC5D;IACA,MAAMwU,SAAS,GAAG,IAAI,CAACvR,IAAI,CAACwR,YAAY,CAAC,CAAC;IAC1C,MAAMmC,UAAU,GAAG,IAAI,CAAC3T,IAAI,CAAC2T,UAAU,CAAC5W,KAAK,CAAC;IAC9C,MAAM6V,aAAa,GAAG,IAAI,CAAC5S,IAAI,CAAC4S,aAAa,CAAC7V,KAAK,CAAC;IACpD,IAAIhB,QAAQ,CAAC2X,WAAW,CAAC,EAAE;MACvB;MACA,MAAME,CAAC,GAAGhB,aAAa,GAAG,IAAI,CAAC5S,IAAI,CAAC2R,YAAY,GAAGuB,UAAU;MAC7D,IAAI,CAAClT,IAAI,CAACyR,YAAY,CAACmC,CAAC,GAAG/X,KAAK,CAAC6X,WAAW,EAAE,CAAC,EAAE,CAAC,CAAC,GAAGC,UAAU,GAAGT,UAAU,CAAC;IAClF,CAAC,MACI;MACD,MAAMW,cAAc,GAAGF,UAAU,GAAGf,aAAa;MACjD,MAAMkB,YAAY,GAAGvC,SAAS,GAAG,IAAI,CAACvR,IAAI,CAAC2R,YAAY;MACvD,IAAIgC,UAAU,GAAGpC,SAAS,GAAG2B,UAAU,IAAIW,cAAc,IAAIC,YAAY,EAAE;QACvE;MAAA,CACH,MACI,IAAIH,UAAU,GAAGpC,SAAS,GAAG2B,UAAU,IAAKW,cAAc,IAAIC,YAAY,IAAIlB,aAAa,IAAI,IAAI,CAAC5S,IAAI,CAAC2R,YAAa,EAAE;QACzH,IAAI,CAAC3R,IAAI,CAACyR,YAAY,CAACkC,UAAU,GAAGT,UAAU,CAAC;MACnD,CAAC,MACI,IAAIW,cAAc,IAAIC,YAAY,EAAE;QACrC,IAAI,CAAC9T,IAAI,CAACyR,YAAY,CAACoC,cAAc,GAAG,IAAI,CAAC7T,IAAI,CAAC2R,YAAY,CAAC;MACnE;IACJ;EACJ;EACA;AACJ;AACA;AACA;EACIoC,cAAcA,CAAChX,KAAK,EAAEmW,UAAU,GAAG,CAAC,EAAE;IAClC,IAAInW,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAI,IAAI,CAACpD,MAAM,EAAE;MACnC,MAAM,IAAIqC,SAAS,CAAC,IAAI,CAAC4T,IAAI,EAAE,iBAAiB7S,KAAK,EAAE,CAAC;IAC5D;IACA,MAAMwU,SAAS,GAAG,IAAI,CAACvR,IAAI,CAACwR,YAAY,CAAC,CAAC;IAC1C,MAAMmC,UAAU,GAAG,IAAI,CAAC3T,IAAI,CAAC2T,UAAU,CAAC5W,KAAK,CAAC;IAC9C,MAAM6V,aAAa,GAAG,IAAI,CAAC5S,IAAI,CAAC4S,aAAa,CAAC7V,KAAK,CAAC;IACpD,IAAI4W,UAAU,GAAGpC,SAAS,GAAG2B,UAAU,IAAIS,UAAU,GAAGf,aAAa,GAAGrB,SAAS,GAAG,IAAI,CAACvR,IAAI,CAAC2R,YAAY,EAAE;MACxG,OAAO,IAAI;IACf;IACA;IACA,MAAMiC,CAAC,GAAGhB,aAAa,GAAG,IAAI,CAAC5S,IAAI,CAAC2R,YAAY,GAAGuB,UAAU;IAC7D,OAAOhK,IAAI,CAAC8K,GAAG,CAAC,CAACzC,SAAS,GAAG2B,UAAU,GAAGS,UAAU,IAAIC,CAAC,CAAC;EAC9D;EACAvL,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAACrI,IAAI,CAAC6B,OAAO;EAC5B;EACAoS,oBAAoBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACjU,IAAI,CAACkU,wBAAwB;EAC7C;EACAC,YAAYA,CAACpX,KAAK,EAAE;IAChB,OAAO,IAAI,CAACiD,IAAI,CAACoU,eAAe,CAACrX,KAAK,CAAC;EAC3C;EACAsX,aAAaA,CAACtX,KAAK,EAAE;IACjB,OAAO,IAAI,CAACiD,IAAI,CAAC2T,UAAU,CAAC5W,KAAK,CAAC;EACtC;EACAkK,KAAKA,CAAC4C,MAAM,EAAE;IACV,IAAI,CAAC2G,eAAe,CAACvJ,KAAK,CAAC4C,MAAM,CAAC;EACtC;EACAgF,WAAWA,CAAC;IAAE/Q,OAAO;IAAEoB;EAAa,CAAC,EAAE;IACnC,OAAO;MAAEpB,OAAO;MAAEU,QAAQ,EAAEV,OAAO,CAACuC,GAAG,CAACnG,CAAC,IAAI,IAAI,CAAC8F,IAAI,CAAClD,OAAO,CAAC5C,CAAC,CAAC,CAAC;MAAEgF;IAAa,CAAC;EACtF;EACA4R,cAAcA,CAAA,EAAG;IACb,MAAMrN,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC/D,GAAG,CAAC,CAAC;IAC9B,IAAI,CAACM,IAAI,CAAC6B,OAAO,CAAChD,SAAS,CAACC,MAAM,CAAC,iBAAiB,EAAE2E,KAAK,CAAC9J,MAAM,GAAG,CAAC,CAAC;IACvE,IAAI,CAAC0W,2BAA2B,CAAC,CAAC;EACtC;EACAA,2BAA2BA,CAAA,EAAG;IAC1B,MAAM5M,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC/D,GAAG,CAAC,CAAC;IAC9B,IAAI+D,KAAK,CAAC9J,MAAM,GAAG,CAAC,EAAE;MAClB,IAAI2a,EAAE;MACN,IAAI,IAAI,CAACxO,qBAAqB,EAAEyO,qBAAqB,EAAE;QACnDD,EAAE,GAAG,IAAI,CAACxO,qBAAqB,CAACyO,qBAAqB,CAAC,IAAI,CAACvU,IAAI,CAAClD,OAAO,CAAC2G,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;MACtF;MACA,IAAI,CAACzD,IAAI,CAAC6B,OAAO,CAAC/B,YAAY,CAAC,uBAAuB,EAAEwU,EAAE,IAAI,IAAI,CAACtU,IAAI,CAACoU,eAAe,CAAC3Q,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IACtG,CAAC,MACI;MACD,IAAI,CAACzD,IAAI,CAAC6B,OAAO,CAACiM,eAAe,CAAC,uBAAuB,CAAC;IAC9D;EACJ;EACAiD,kBAAkBA,CAAA,EAAG;IACjB,MAAM1H,SAAS,GAAG,IAAI,CAACA,SAAS,CAAC3J,GAAG,CAAC,CAAC;IACtC,IAAI,CAACM,IAAI,CAAC6B,OAAO,CAAChD,SAAS,CAACC,MAAM,CAAC,gBAAgB,EAAEuK,SAAS,CAAC1P,MAAM,KAAK,CAAC,CAAC;IAC5E,IAAI,CAACqG,IAAI,CAAC6B,OAAO,CAAChD,SAAS,CAACC,MAAM,CAAC,kBAAkB,EAAEuK,SAAS,CAAC1P,MAAM,KAAK,CAAC,CAAC;IAC9E,IAAI,CAACqG,IAAI,CAAC6B,OAAO,CAAChD,SAAS,CAACC,MAAM,CAAC,oBAAoB,EAAEuK,SAAS,CAAC1P,MAAM,GAAG,CAAC,CAAC;EAClF;EACAiC,OAAOA,CAAA,EAAG;IACN,IAAI,CAACoU,aAAa,CAACvQ,IAAI,CAAC,CAAC;IACzB,IAAI,CAACkC,WAAW,CAAC/F,OAAO,CAAC,CAAC;IAC1B,IAAI,CAACoU,aAAa,CAACpU,OAAO,CAAC,CAAC;EAChC;AACJ;AACAxC,UAAU,CAAC,CACPiC,OAAO,CACV,EAAEoT,IAAI,CAAC9O,SAAS,EAAE,kBAAkB,EAAE,IAAI,CAAC;AAC5CvG,UAAU,CAAC,CACPiC,OAAO,CACV,EAAEoT,IAAI,CAAC9O,SAAS,EAAE,sBAAsB,EAAE,IAAI,CAAC;AAChDvG,UAAU,CAAC,CACPiC,OAAO,CACV,EAAEoT,IAAI,CAAC9O,SAAS,EAAE,eAAe,EAAE,IAAI,CAAC;AACzCvG,UAAU,CAAC,CACPiC,OAAO,CACV,EAAEoT,IAAI,CAAC9O,SAAS,EAAE,WAAW,EAAE,IAAI,CAAC;AACrCvG,UAAU,CAAC,CACPiC,OAAO,CACV,EAAEoT,IAAI,CAAC9O,SAAS,EAAE,YAAY,EAAE,IAAI,CAAC;AACtCvG,UAAU,CAAC,CACPiC,OAAO,CACV,EAAEoT,IAAI,CAAC9O,SAAS,EAAE,WAAW,EAAE,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}