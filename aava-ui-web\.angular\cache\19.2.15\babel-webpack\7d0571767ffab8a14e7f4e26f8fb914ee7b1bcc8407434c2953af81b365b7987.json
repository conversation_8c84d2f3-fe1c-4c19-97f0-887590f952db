{"ast": null, "code": "import _asyncToGenerator from \"C:/console/aava-ui-web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { onUnexpectedExternalError } from '../../../../base/common/errors.js';\nimport { HierarchicalKind } from '../../../../base/common/hierarchicalKind.js';\nexport const CodeActionKind = new class {\n  constructor() {\n    this.QuickFix = new HierarchicalKind('quickfix');\n    this.Refactor = new HierarchicalKind('refactor');\n    this.RefactorExtract = this.Refactor.append('extract');\n    this.RefactorInline = this.Refactor.append('inline');\n    this.RefactorMove = this.Refactor.append('move');\n    this.RefactorRewrite = this.Refactor.append('rewrite');\n    this.Notebook = new HierarchicalKind('notebook');\n    this.Source = new HierarchicalKind('source');\n    this.SourceOrganizeImports = this.Source.append('organizeImports');\n    this.SourceFixAll = this.Source.append('fixAll');\n    this.SurroundWith = this.Refactor.append('surround');\n  }\n}();\nexport var CodeActionTriggerSource = /*#__PURE__*/function (CodeActionTriggerSource) {\n  CodeActionTriggerSource[\"Refactor\"] = \"refactor\";\n  CodeActionTriggerSource[\"RefactorPreview\"] = \"refactor preview\";\n  CodeActionTriggerSource[\"Lightbulb\"] = \"lightbulb\";\n  CodeActionTriggerSource[\"Default\"] = \"other (default)\";\n  CodeActionTriggerSource[\"SourceAction\"] = \"source action\";\n  CodeActionTriggerSource[\"QuickFix\"] = \"quick fix action\";\n  CodeActionTriggerSource[\"FixAll\"] = \"fix all\";\n  CodeActionTriggerSource[\"OrganizeImports\"] = \"organize imports\";\n  CodeActionTriggerSource[\"AutoFix\"] = \"auto fix\";\n  CodeActionTriggerSource[\"QuickFixHover\"] = \"quick fix hover window\";\n  CodeActionTriggerSource[\"OnSave\"] = \"save participants\";\n  CodeActionTriggerSource[\"ProblemsView\"] = \"problems view\";\n  return CodeActionTriggerSource;\n}(CodeActionTriggerSource || {});\nexport function mayIncludeActionsOfKind(filter, providedKind) {\n  // A provided kind may be a subset or superset of our filtered kind.\n  if (filter.include && !filter.include.intersects(providedKind)) {\n    return false;\n  }\n  if (filter.excludes) {\n    if (filter.excludes.some(exclude => excludesAction(providedKind, exclude, filter.include))) {\n      return false;\n    }\n  }\n  // Don't return source actions unless they are explicitly requested\n  if (!filter.includeSourceActions && CodeActionKind.Source.contains(providedKind)) {\n    return false;\n  }\n  return true;\n}\nexport function filtersAction(filter, action) {\n  const actionKind = action.kind ? new HierarchicalKind(action.kind) : undefined;\n  // Filter out actions by kind\n  if (filter.include) {\n    if (!actionKind || !filter.include.contains(actionKind)) {\n      return false;\n    }\n  }\n  if (filter.excludes) {\n    if (actionKind && filter.excludes.some(exclude => excludesAction(actionKind, exclude, filter.include))) {\n      return false;\n    }\n  }\n  // Don't return source actions unless they are explicitly requested\n  if (!filter.includeSourceActions) {\n    if (actionKind && CodeActionKind.Source.contains(actionKind)) {\n      return false;\n    }\n  }\n  if (filter.onlyIncludePreferredActions) {\n    if (!action.isPreferred) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction excludesAction(providedKind, exclude, include) {\n  if (!exclude.contains(providedKind)) {\n    return false;\n  }\n  if (include && exclude.contains(include)) {\n    // The include is more specific, don't filter out\n    return false;\n  }\n  return true;\n}\nexport class CodeActionCommandArgs {\n  static fromUser(arg, defaults) {\n    if (!arg || typeof arg !== 'object') {\n      return new CodeActionCommandArgs(defaults.kind, defaults.apply, false);\n    }\n    return new CodeActionCommandArgs(CodeActionCommandArgs.getKindFromUser(arg, defaults.kind), CodeActionCommandArgs.getApplyFromUser(arg, defaults.apply), CodeActionCommandArgs.getPreferredUser(arg));\n  }\n  static getApplyFromUser(arg, defaultAutoApply) {\n    switch (typeof arg.apply === 'string' ? arg.apply.toLowerCase() : '') {\n      case 'first':\n        return \"first\" /* CodeActionAutoApply.First */;\n      case 'never':\n        return \"never\" /* CodeActionAutoApply.Never */;\n      case 'ifsingle':\n        return \"ifSingle\" /* CodeActionAutoApply.IfSingle */;\n      default:\n        return defaultAutoApply;\n    }\n  }\n  static getKindFromUser(arg, defaultKind) {\n    return typeof arg.kind === 'string' ? new HierarchicalKind(arg.kind) : defaultKind;\n  }\n  static getPreferredUser(arg) {\n    return typeof arg.preferred === 'boolean' ? arg.preferred : false;\n  }\n  constructor(kind, apply, preferred) {\n    this.kind = kind;\n    this.apply = apply;\n    this.preferred = preferred;\n  }\n}\nexport class CodeActionItem {\n  constructor(action, provider, highlightRange) {\n    this.action = action;\n    this.provider = provider;\n    this.highlightRange = highlightRange;\n  }\n  resolve(token) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (_this.provider?.resolveCodeAction && !_this.action.edit) {\n        let action;\n        try {\n          action = yield _this.provider.resolveCodeAction(_this.action, token);\n        } catch (err) {\n          onUnexpectedExternalError(err);\n        }\n        if (action) {\n          _this.action.edit = action.edit;\n        }\n      }\n      return _this;\n    })();\n  }\n}", "map": {"version": 3, "names": ["onUnexpectedExternalError", "HierarchicalKind", "CodeActionKind", "constructor", "QuickFix", "Refa<PERSON>", "RefactorExtract", "append", "RefactorInline", "Refactor<PERSON>ove", "RefactorRewrite", "Notebook", "Source", "SourceOrganizeImports", "SourceFixAll", "SurroundWith", "CodeActionTriggerSource", "mayIncludeActionsOfKind", "filter", "provided<PERSON><PERSON>", "include", "intersects", "excludes", "some", "exclude", "excludesAction", "includeSourceActions", "contains", "filtersAction", "action", "actionKind", "kind", "undefined", "onlyIncludePreferredActions", "isPreferred", "CodeActionCommandArgs", "fromUser", "arg", "defaults", "apply", "getKindFromUser", "getApplyFromUser", "getPreferredUser", "defaultAutoApply", "toLowerCase", "defaultKind", "preferred", "CodeActionItem", "provider", "highlightRange", "resolve", "token", "_this", "_asyncToGenerator", "resolveCodeAction", "edit", "err"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/contrib/codeAction/common/types.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { onUnexpectedExternalError } from '../../../../base/common/errors.js';\nimport { HierarchicalKind } from '../../../../base/common/hierarchicalKind.js';\nexport const CodeActionKind = new class {\n    constructor() {\n        this.QuickFix = new HierarchicalKind('quickfix');\n        this.Refactor = new HierarchicalKind('refactor');\n        this.RefactorExtract = this.Refactor.append('extract');\n        this.RefactorInline = this.Refactor.append('inline');\n        this.RefactorMove = this.Refactor.append('move');\n        this.RefactorRewrite = this.Refactor.append('rewrite');\n        this.Notebook = new HierarchicalKind('notebook');\n        this.Source = new HierarchicalKind('source');\n        this.SourceOrganizeImports = this.Source.append('organizeImports');\n        this.SourceFixAll = this.Source.append('fixAll');\n        this.SurroundWith = this.Refactor.append('surround');\n    }\n};\nexport var CodeActionTriggerSource;\n(function (CodeActionTriggerSource) {\n    CodeActionTriggerSource[\"Refactor\"] = \"refactor\";\n    CodeActionTriggerSource[\"RefactorPreview\"] = \"refactor preview\";\n    CodeActionTriggerSource[\"Lightbulb\"] = \"lightbulb\";\n    CodeActionTriggerSource[\"Default\"] = \"other (default)\";\n    CodeActionTriggerSource[\"SourceAction\"] = \"source action\";\n    CodeActionTriggerSource[\"QuickFix\"] = \"quick fix action\";\n    CodeActionTriggerSource[\"FixAll\"] = \"fix all\";\n    CodeActionTriggerSource[\"OrganizeImports\"] = \"organize imports\";\n    CodeActionTriggerSource[\"AutoFix\"] = \"auto fix\";\n    CodeActionTriggerSource[\"QuickFixHover\"] = \"quick fix hover window\";\n    CodeActionTriggerSource[\"OnSave\"] = \"save participants\";\n    CodeActionTriggerSource[\"ProblemsView\"] = \"problems view\";\n})(CodeActionTriggerSource || (CodeActionTriggerSource = {}));\nexport function mayIncludeActionsOfKind(filter, providedKind) {\n    // A provided kind may be a subset or superset of our filtered kind.\n    if (filter.include && !filter.include.intersects(providedKind)) {\n        return false;\n    }\n    if (filter.excludes) {\n        if (filter.excludes.some(exclude => excludesAction(providedKind, exclude, filter.include))) {\n            return false;\n        }\n    }\n    // Don't return source actions unless they are explicitly requested\n    if (!filter.includeSourceActions && CodeActionKind.Source.contains(providedKind)) {\n        return false;\n    }\n    return true;\n}\nexport function filtersAction(filter, action) {\n    const actionKind = action.kind ? new HierarchicalKind(action.kind) : undefined;\n    // Filter out actions by kind\n    if (filter.include) {\n        if (!actionKind || !filter.include.contains(actionKind)) {\n            return false;\n        }\n    }\n    if (filter.excludes) {\n        if (actionKind && filter.excludes.some(exclude => excludesAction(actionKind, exclude, filter.include))) {\n            return false;\n        }\n    }\n    // Don't return source actions unless they are explicitly requested\n    if (!filter.includeSourceActions) {\n        if (actionKind && CodeActionKind.Source.contains(actionKind)) {\n            return false;\n        }\n    }\n    if (filter.onlyIncludePreferredActions) {\n        if (!action.isPreferred) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction excludesAction(providedKind, exclude, include) {\n    if (!exclude.contains(providedKind)) {\n        return false;\n    }\n    if (include && exclude.contains(include)) {\n        // The include is more specific, don't filter out\n        return false;\n    }\n    return true;\n}\nexport class CodeActionCommandArgs {\n    static fromUser(arg, defaults) {\n        if (!arg || typeof arg !== 'object') {\n            return new CodeActionCommandArgs(defaults.kind, defaults.apply, false);\n        }\n        return new CodeActionCommandArgs(CodeActionCommandArgs.getKindFromUser(arg, defaults.kind), CodeActionCommandArgs.getApplyFromUser(arg, defaults.apply), CodeActionCommandArgs.getPreferredUser(arg));\n    }\n    static getApplyFromUser(arg, defaultAutoApply) {\n        switch (typeof arg.apply === 'string' ? arg.apply.toLowerCase() : '') {\n            case 'first': return \"first\" /* CodeActionAutoApply.First */;\n            case 'never': return \"never\" /* CodeActionAutoApply.Never */;\n            case 'ifsingle': return \"ifSingle\" /* CodeActionAutoApply.IfSingle */;\n            default: return defaultAutoApply;\n        }\n    }\n    static getKindFromUser(arg, defaultKind) {\n        return typeof arg.kind === 'string'\n            ? new HierarchicalKind(arg.kind)\n            : defaultKind;\n    }\n    static getPreferredUser(arg) {\n        return typeof arg.preferred === 'boolean'\n            ? arg.preferred\n            : false;\n    }\n    constructor(kind, apply, preferred) {\n        this.kind = kind;\n        this.apply = apply;\n        this.preferred = preferred;\n    }\n}\nexport class CodeActionItem {\n    constructor(action, provider, highlightRange) {\n        this.action = action;\n        this.provider = provider;\n        this.highlightRange = highlightRange;\n    }\n    async resolve(token) {\n        if (this.provider?.resolveCodeAction && !this.action.edit) {\n            let action;\n            try {\n                action = await this.provider.resolveCodeAction(this.action, token);\n            }\n            catch (err) {\n                onUnexpectedExternalError(err);\n            }\n            if (action) {\n                this.action.edit = action.edit;\n            }\n        }\n        return this;\n    }\n}\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA,SAASA,yBAAyB,QAAQ,mCAAmC;AAC7E,SAASC,gBAAgB,QAAQ,6CAA6C;AAC9E,OAAO,MAAMC,cAAc,GAAG,IAAI,MAAM;EACpCC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,QAAQ,GAAG,IAAIH,gBAAgB,CAAC,UAAU,CAAC;IAChD,IAAI,CAACI,QAAQ,GAAG,IAAIJ,gBAAgB,CAAC,UAAU,CAAC;IAChD,IAAI,CAACK,eAAe,GAAG,IAAI,CAACD,QAAQ,CAACE,MAAM,CAAC,SAAS,CAAC;IACtD,IAAI,CAACC,cAAc,GAAG,IAAI,CAACH,QAAQ,CAACE,MAAM,CAAC,QAAQ,CAAC;IACpD,IAAI,CAACE,YAAY,GAAG,IAAI,CAACJ,QAAQ,CAACE,MAAM,CAAC,MAAM,CAAC;IAChD,IAAI,CAACG,eAAe,GAAG,IAAI,CAACL,QAAQ,CAACE,MAAM,CAAC,SAAS,CAAC;IACtD,IAAI,CAACI,QAAQ,GAAG,IAAIV,gBAAgB,CAAC,UAAU,CAAC;IAChD,IAAI,CAACW,MAAM,GAAG,IAAIX,gBAAgB,CAAC,QAAQ,CAAC;IAC5C,IAAI,CAACY,qBAAqB,GAAG,IAAI,CAACD,MAAM,CAACL,MAAM,CAAC,iBAAiB,CAAC;IAClE,IAAI,CAACO,YAAY,GAAG,IAAI,CAACF,MAAM,CAACL,MAAM,CAAC,QAAQ,CAAC;IAChD,IAAI,CAACQ,YAAY,GAAG,IAAI,CAACV,QAAQ,CAACE,MAAM,CAAC,UAAU,CAAC;EACxD;AACJ,CAAC,CAAD,CAAC;AACD,OAAO,IAAIS,uBAAuB,gBACjC,UAAUA,uBAAuB,EAAE;EAChCA,uBAAuB,CAAC,UAAU,CAAC,GAAG,UAAU;EAChDA,uBAAuB,CAAC,iBAAiB,CAAC,GAAG,kBAAkB;EAC/DA,uBAAuB,CAAC,WAAW,CAAC,GAAG,WAAW;EAClDA,uBAAuB,CAAC,SAAS,CAAC,GAAG,iBAAiB;EACtDA,uBAAuB,CAAC,cAAc,CAAC,GAAG,eAAe;EACzDA,uBAAuB,CAAC,UAAU,CAAC,GAAG,kBAAkB;EACxDA,uBAAuB,CAAC,QAAQ,CAAC,GAAG,SAAS;EAC7CA,uBAAuB,CAAC,iBAAiB,CAAC,GAAG,kBAAkB;EAC/DA,uBAAuB,CAAC,SAAS,CAAC,GAAG,UAAU;EAC/CA,uBAAuB,CAAC,eAAe,CAAC,GAAG,wBAAwB;EACnEA,uBAAuB,CAAC,QAAQ,CAAC,GAAG,mBAAmB;EACvDA,uBAAuB,CAAC,cAAc,CAAC,GAAG,eAAe;EAAC,OAZnDA,uBAAuB;AAalC,CAAC,CAAEA,uBAAuB,IAA+B,CAAC,CAAE,CAd1B;AAelC,OAAO,SAASC,uBAAuBA,CAACC,MAAM,EAAEC,YAAY,EAAE;EAC1D;EACA,IAAID,MAAM,CAACE,OAAO,IAAI,CAACF,MAAM,CAACE,OAAO,CAACC,UAAU,CAACF,YAAY,CAAC,EAAE;IAC5D,OAAO,KAAK;EAChB;EACA,IAAID,MAAM,CAACI,QAAQ,EAAE;IACjB,IAAIJ,MAAM,CAACI,QAAQ,CAACC,IAAI,CAACC,OAAO,IAAIC,cAAc,CAACN,YAAY,EAAEK,OAAO,EAAEN,MAAM,CAACE,OAAO,CAAC,CAAC,EAAE;MACxF,OAAO,KAAK;IAChB;EACJ;EACA;EACA,IAAI,CAACF,MAAM,CAACQ,oBAAoB,IAAIxB,cAAc,CAACU,MAAM,CAACe,QAAQ,CAACR,YAAY,CAAC,EAAE;IAC9E,OAAO,KAAK;EAChB;EACA,OAAO,IAAI;AACf;AACA,OAAO,SAASS,aAAaA,CAACV,MAAM,EAAEW,MAAM,EAAE;EAC1C,MAAMC,UAAU,GAAGD,MAAM,CAACE,IAAI,GAAG,IAAI9B,gBAAgB,CAAC4B,MAAM,CAACE,IAAI,CAAC,GAAGC,SAAS;EAC9E;EACA,IAAId,MAAM,CAACE,OAAO,EAAE;IAChB,IAAI,CAACU,UAAU,IAAI,CAACZ,MAAM,CAACE,OAAO,CAACO,QAAQ,CAACG,UAAU,CAAC,EAAE;MACrD,OAAO,KAAK;IAChB;EACJ;EACA,IAAIZ,MAAM,CAACI,QAAQ,EAAE;IACjB,IAAIQ,UAAU,IAAIZ,MAAM,CAACI,QAAQ,CAACC,IAAI,CAACC,OAAO,IAAIC,cAAc,CAACK,UAAU,EAAEN,OAAO,EAAEN,MAAM,CAACE,OAAO,CAAC,CAAC,EAAE;MACpG,OAAO,KAAK;IAChB;EACJ;EACA;EACA,IAAI,CAACF,MAAM,CAACQ,oBAAoB,EAAE;IAC9B,IAAII,UAAU,IAAI5B,cAAc,CAACU,MAAM,CAACe,QAAQ,CAACG,UAAU,CAAC,EAAE;MAC1D,OAAO,KAAK;IAChB;EACJ;EACA,IAAIZ,MAAM,CAACe,2BAA2B,EAAE;IACpC,IAAI,CAACJ,MAAM,CAACK,WAAW,EAAE;MACrB,OAAO,KAAK;IAChB;EACJ;EACA,OAAO,IAAI;AACf;AACA,SAAST,cAAcA,CAACN,YAAY,EAAEK,OAAO,EAAEJ,OAAO,EAAE;EACpD,IAAI,CAACI,OAAO,CAACG,QAAQ,CAACR,YAAY,CAAC,EAAE;IACjC,OAAO,KAAK;EAChB;EACA,IAAIC,OAAO,IAAII,OAAO,CAACG,QAAQ,CAACP,OAAO,CAAC,EAAE;IACtC;IACA,OAAO,KAAK;EAChB;EACA,OAAO,IAAI;AACf;AACA,OAAO,MAAMe,qBAAqB,CAAC;EAC/B,OAAOC,QAAQA,CAACC,GAAG,EAAEC,QAAQ,EAAE;IAC3B,IAAI,CAACD,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;MACjC,OAAO,IAAIF,qBAAqB,CAACG,QAAQ,CAACP,IAAI,EAAEO,QAAQ,CAACC,KAAK,EAAE,KAAK,CAAC;IAC1E;IACA,OAAO,IAAIJ,qBAAqB,CAACA,qBAAqB,CAACK,eAAe,CAACH,GAAG,EAAEC,QAAQ,CAACP,IAAI,CAAC,EAAEI,qBAAqB,CAACM,gBAAgB,CAACJ,GAAG,EAAEC,QAAQ,CAACC,KAAK,CAAC,EAAEJ,qBAAqB,CAACO,gBAAgB,CAACL,GAAG,CAAC,CAAC;EACzM;EACA,OAAOI,gBAAgBA,CAACJ,GAAG,EAAEM,gBAAgB,EAAE;IAC3C,QAAQ,OAAON,GAAG,CAACE,KAAK,KAAK,QAAQ,GAAGF,GAAG,CAACE,KAAK,CAACK,WAAW,CAAC,CAAC,GAAG,EAAE;MAChE,KAAK,OAAO;QAAE,OAAO,OAAO,CAAC;MAC7B,KAAK,OAAO;QAAE,OAAO,OAAO,CAAC;MAC7B,KAAK,UAAU;QAAE,OAAO,UAAU,CAAC;MACnC;QAAS,OAAOD,gBAAgB;IACpC;EACJ;EACA,OAAOH,eAAeA,CAACH,GAAG,EAAEQ,WAAW,EAAE;IACrC,OAAO,OAAOR,GAAG,CAACN,IAAI,KAAK,QAAQ,GAC7B,IAAI9B,gBAAgB,CAACoC,GAAG,CAACN,IAAI,CAAC,GAC9Bc,WAAW;EACrB;EACA,OAAOH,gBAAgBA,CAACL,GAAG,EAAE;IACzB,OAAO,OAAOA,GAAG,CAACS,SAAS,KAAK,SAAS,GACnCT,GAAG,CAACS,SAAS,GACb,KAAK;EACf;EACA3C,WAAWA,CAAC4B,IAAI,EAAEQ,KAAK,EAAEO,SAAS,EAAE;IAChC,IAAI,CAACf,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACQ,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACO,SAAS,GAAGA,SAAS;EAC9B;AACJ;AACA,OAAO,MAAMC,cAAc,CAAC;EACxB5C,WAAWA,CAAC0B,MAAM,EAAEmB,QAAQ,EAAEC,cAAc,EAAE;IAC1C,IAAI,CAACpB,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACmB,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,cAAc,GAAGA,cAAc;EACxC;EACMC,OAAOA,CAACC,KAAK,EAAE;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACjB,IAAID,KAAI,CAACJ,QAAQ,EAAEM,iBAAiB,IAAI,CAACF,KAAI,CAACvB,MAAM,CAAC0B,IAAI,EAAE;QACvD,IAAI1B,MAAM;QACV,IAAI;UACAA,MAAM,SAASuB,KAAI,CAACJ,QAAQ,CAACM,iBAAiB,CAACF,KAAI,CAACvB,MAAM,EAAEsB,KAAK,CAAC;QACtE,CAAC,CACD,OAAOK,GAAG,EAAE;UACRxD,yBAAyB,CAACwD,GAAG,CAAC;QAClC;QACA,IAAI3B,MAAM,EAAE;UACRuB,KAAI,CAACvB,MAAM,CAAC0B,IAAI,GAAG1B,MAAM,CAAC0B,IAAI;QAClC;MACJ;MACA,OAAOH,KAAI;IAAC;EAChB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}