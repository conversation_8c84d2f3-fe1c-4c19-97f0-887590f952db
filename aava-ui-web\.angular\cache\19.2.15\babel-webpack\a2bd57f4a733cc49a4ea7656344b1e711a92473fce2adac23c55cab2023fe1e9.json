{"ast": null, "code": "import _asyncToGenerator from \"C:/console/aava-ui-web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = this && this.__param || function (paramIndex, decorator) {\n  return function (target, key) {\n    decorator(target, key, paramIndex);\n  };\n};\nvar InlineEditSideBySideWidget_1, InlineEditSideBySideContentWidget_1;\nimport { $ } from '../../../../base/browser/dom.js';\nimport { CancellationToken } from '../../../../base/common/cancellation.js';\nimport { Disposable, toDisposable } from '../../../../base/common/lifecycle.js';\nimport { ObservablePromise, autorun, autorunWithStore, derived, observableSignalFromEvent } from '../../../../base/common/observable.js';\nimport { derivedDisposable } from '../../../../base/common/observableInternal/derived.js';\nimport { URI } from '../../../../base/common/uri.js';\nimport './inlineEditSideBySideWidget.css';\nimport { observableCodeEditor } from '../../../browser/observableCodeEditor.js';\nimport { EmbeddedCodeEditorWidget } from '../../../browser/widget/codeEditor/embeddedCodeEditorWidget.js';\nimport { IDiffProviderFactoryService } from '../../../browser/widget/diffEditor/diffProviderFactoryService.js';\nimport { diffAddDecoration, diffAddDecorationEmpty, diffDeleteDecoration, diffDeleteDecorationEmpty, diffLineAddDecorationBackgroundWithIndicator, diffLineDeleteDecorationBackgroundWithIndicator, diffWholeLineAddDecoration, diffWholeLineDeleteDecoration } from '../../../browser/widget/diffEditor/registrations.contribution.js';\nimport { Position } from '../../../common/core/position.js';\nimport { Range } from '../../../common/core/range.js';\nimport { PLAINTEXT_LANGUAGE_ID } from '../../../common/languages/modesRegistry.js';\nimport { TextModel } from '../../../common/model/textModel.js';\nimport { IModelService } from '../../../common/services/model.js';\nimport { IInstantiationService } from '../../../../platform/instantiation/common/instantiation.js';\nfunction* range(start, end, step = 1) {\n  if (end === undefined) {\n    [end, start] = [start, 0];\n  }\n  for (let n = start; n < end; n += step) {\n    yield n;\n  }\n}\nfunction removeIndentation(lines) {\n  const indentation = lines[0].match(/^\\s*/)?.[0] ?? '';\n  const length = indentation.length;\n  return {\n    text: lines.map(l => l.replace(new RegExp('^' + indentation), '')),\n    shift: length\n  };\n}\nlet InlineEditSideBySideWidget = /*#__PURE__*/(() => {\n  let InlineEditSideBySideWidget = class InlineEditSideBySideWidget extends Disposable {\n    static {\n      InlineEditSideBySideWidget_1 = this;\n    }\n    static {\n      this._modelId = 0;\n    }\n    static _createUniqueUri() {\n      return URI.from({\n        scheme: 'inline-edit-widget',\n        path: new Date().toString() + String(InlineEditSideBySideWidget_1._modelId++)\n      });\n    }\n    constructor(_editor, _model, _instantiationService, _diffProviderFactoryService, _modelService) {\n      var _this;\n      super();\n      _this = this;\n      this._editor = _editor;\n      this._model = _model;\n      this._instantiationService = _instantiationService;\n      this._diffProviderFactoryService = _diffProviderFactoryService;\n      this._modelService = _modelService;\n      this._position = derived(this, reader => {\n        const ghostText = this._model.read(reader);\n        if (!ghostText || ghostText.text.length === 0) {\n          return null;\n        }\n        if (ghostText.range.startLineNumber === ghostText.range.endLineNumber && !(ghostText.range.startColumn === ghostText.range.endColumn && ghostText.range.startColumn === 1)) {\n          //for inner-line suggestions we still want to use minimal ghost text\n          return null;\n        }\n        const editorModel = this._editor.getModel();\n        if (!editorModel) {\n          return null;\n        }\n        const lines = Array.from(range(ghostText.range.startLineNumber, ghostText.range.endLineNumber + 1));\n        const lengths = lines.map(lineNumber => editorModel.getLineLastNonWhitespaceColumn(lineNumber));\n        const maxColumn = Math.max(...lengths);\n        const lineOfMaxColumn = lines[lengths.indexOf(maxColumn)];\n        const position = new Position(lineOfMaxColumn, maxColumn);\n        const pos = {\n          top: ghostText.range.startLineNumber,\n          left: position\n        };\n        return pos;\n      });\n      this._text = derived(this, reader => {\n        const ghostText = this._model.read(reader);\n        if (!ghostText) {\n          return {\n            text: '',\n            shift: 0\n          };\n        }\n        const t = removeIndentation(ghostText.text.split('\\n'));\n        return {\n          text: t.text.join('\\n'),\n          shift: t.shift\n        };\n      });\n      this._originalModel = derivedDisposable(() => this._modelService.createModel('', null, InlineEditSideBySideWidget_1._createUniqueUri())).keepObserved(this._store);\n      this._modifiedModel = derivedDisposable(() => this._modelService.createModel('', null, InlineEditSideBySideWidget_1._createUniqueUri())).keepObserved(this._store);\n      this._diff = derived(this, reader => {\n        return this._diffPromise.read(reader)?.promiseResult.read(reader)?.data;\n      });\n      this._diffPromise = derived(this, reader => {\n        const ghostText = this._model.read(reader);\n        if (!ghostText) {\n          return;\n        }\n        const editorModel = this._editor.getModel();\n        if (!editorModel) {\n          return;\n        }\n        const originalText = removeIndentation(editorModel.getValueInRange(ghostText.range).split('\\n')).text.join('\\n');\n        const modifiedText = removeIndentation(ghostText.text.split('\\n')).text.join('\\n');\n        this._originalModel.get().setValue(originalText);\n        this._modifiedModel.get().setValue(modifiedText);\n        const d = this._diffProviderFactoryService.createDiffProvider({\n          diffAlgorithm: 'advanced'\n        });\n        return ObservablePromise.fromFn(/*#__PURE__*/_asyncToGenerator(function* () {\n          const result = yield d.computeDiff(_this._originalModel.get(), _this._modifiedModel.get(), {\n            computeMoves: false,\n            ignoreTrimWhitespace: false,\n            maxComputationTimeMs: 1000\n          }, CancellationToken.None);\n          if (result.identical) {\n            return undefined;\n          }\n          return result.changes;\n        }));\n      });\n      this._register(autorunWithStore((reader, store) => {\n        /** @description setup content widget */\n        const model = this._model.read(reader);\n        if (!model) {\n          return;\n        }\n        if (this._position.get() === null) {\n          return;\n        }\n        const contentWidget = store.add(this._instantiationService.createInstance(InlineEditSideBySideContentWidget, this._editor, this._position, this._text.map(t => t.text), this._text.map(t => t.shift), this._diff));\n        _editor.addOverlayWidget(contentWidget);\n        store.add(toDisposable(() => _editor.removeOverlayWidget(contentWidget)));\n      }));\n    }\n  };\n  return InlineEditSideBySideWidget;\n})();\nInlineEditSideBySideWidget = InlineEditSideBySideWidget_1 = __decorate([__param(2, IInstantiationService), __param(3, IDiffProviderFactoryService), __param(4, IModelService)], InlineEditSideBySideWidget);\nexport { InlineEditSideBySideWidget };\nlet InlineEditSideBySideContentWidget = /*#__PURE__*/(() => {\n  let InlineEditSideBySideContentWidget = class InlineEditSideBySideContentWidget extends Disposable {\n    static {\n      InlineEditSideBySideContentWidget_1 = this;\n    }\n    static {\n      this.id = 0;\n    }\n    constructor(_editor, _position, _text, _shift, _diff, _instantiationService) {\n      super();\n      this._editor = _editor;\n      this._position = _position;\n      this._text = _text;\n      this._shift = _shift;\n      this._diff = _diff;\n      this._instantiationService = _instantiationService;\n      this.id = `InlineEditSideBySideContentWidget${InlineEditSideBySideContentWidget_1.id++}`;\n      this.allowEditorOverflow = false;\n      this._nodes = $('div.inlineEditSideBySide', undefined);\n      this._scrollChanged = observableSignalFromEvent('editor.onDidScrollChange', this._editor.onDidScrollChange);\n      this._previewEditor = this._register(this._instantiationService.createInstance(EmbeddedCodeEditorWidget, this._nodes, {\n        glyphMargin: false,\n        lineNumbers: 'off',\n        minimap: {\n          enabled: false\n        },\n        guides: {\n          indentation: false,\n          bracketPairs: false,\n          bracketPairsHorizontal: false,\n          highlightActiveIndentation: false\n        },\n        folding: false,\n        selectOnLineNumbers: false,\n        selectionHighlight: false,\n        columnSelection: false,\n        overviewRulerBorder: false,\n        overviewRulerLanes: 0,\n        lineDecorationsWidth: 0,\n        lineNumbersMinChars: 0,\n        scrollbar: {\n          vertical: 'hidden',\n          horizontal: 'hidden',\n          alwaysConsumeMouseWheel: false,\n          handleMouseWheel: false\n        },\n        readOnly: true,\n        wordWrap: 'off',\n        wordWrapOverride1: 'off',\n        wordWrapOverride2: 'off',\n        wrappingIndent: 'none',\n        wrappingStrategy: undefined\n      }, {\n        contributions: [],\n        isSimpleWidget: true\n      }, this._editor));\n      this._previewEditorObs = observableCodeEditor(this._previewEditor);\n      this._editorObs = observableCodeEditor(this._editor);\n      this._previewTextModel = this._register(this._instantiationService.createInstance(TextModel, '', this._editor.getModel()?.getLanguageId() ?? PLAINTEXT_LANGUAGE_ID, TextModel.DEFAULT_CREATION_OPTIONS, null));\n      this._setText = derived(reader => {\n        const edit = this._text.read(reader);\n        if (!edit) {\n          return;\n        }\n        this._previewTextModel.setValue(edit);\n      }).recomputeInitiallyAndOnChange(this._store);\n      this._decorations = derived(this, reader => {\n        this._setText.read(reader);\n        const position = this._position.read(reader);\n        if (!position) {\n          return {\n            org: [],\n            mod: []\n          };\n        }\n        const diff = this._diff.read(reader);\n        if (!diff) {\n          return {\n            org: [],\n            mod: []\n          };\n        }\n        const originalDecorations = [];\n        const modifiedDecorations = [];\n        if (diff.length === 1 && diff[0].innerChanges[0].modifiedRange.equalsRange(this._previewTextModel.getFullModelRange())) {\n          return {\n            org: [],\n            mod: []\n          };\n        }\n        const shift = this._shift.get();\n        const moveRange = range => {\n          return new Range(range.startLineNumber + position.top - 1, range.startColumn + shift, range.endLineNumber + position.top - 1, range.endColumn + shift);\n        };\n        for (const m of diff) {\n          if (!m.original.isEmpty) {\n            originalDecorations.push({\n              range: moveRange(m.original.toInclusiveRange()),\n              options: diffLineDeleteDecorationBackgroundWithIndicator\n            });\n          }\n          if (!m.modified.isEmpty) {\n            modifiedDecorations.push({\n              range: m.modified.toInclusiveRange(),\n              options: diffLineAddDecorationBackgroundWithIndicator\n            });\n          }\n          if (m.modified.isEmpty || m.original.isEmpty) {\n            if (!m.original.isEmpty) {\n              originalDecorations.push({\n                range: moveRange(m.original.toInclusiveRange()),\n                options: diffWholeLineDeleteDecoration\n              });\n            }\n            if (!m.modified.isEmpty) {\n              modifiedDecorations.push({\n                range: m.modified.toInclusiveRange(),\n                options: diffWholeLineAddDecoration\n              });\n            }\n          } else {\n            for (const i of m.innerChanges || []) {\n              // Don't show empty markers outside the line range\n              if (m.original.contains(i.originalRange.startLineNumber)) {\n                originalDecorations.push({\n                  range: moveRange(i.originalRange),\n                  options: i.originalRange.isEmpty() ? diffDeleteDecorationEmpty : diffDeleteDecoration\n                });\n              }\n              if (m.modified.contains(i.modifiedRange.startLineNumber)) {\n                modifiedDecorations.push({\n                  range: i.modifiedRange,\n                  options: i.modifiedRange.isEmpty() ? diffAddDecorationEmpty : diffAddDecoration\n                });\n              }\n            }\n          }\n        }\n        return {\n          org: originalDecorations,\n          mod: modifiedDecorations\n        };\n      });\n      this._originalDecorations = derived(this, reader => {\n        return this._decorations.read(reader).org;\n      });\n      this._modifiedDecorations = derived(this, reader => {\n        return this._decorations.read(reader).mod;\n      });\n      this._previewEditor.setModel(this._previewTextModel);\n      this._register(this._editorObs.setDecorations(this._originalDecorations));\n      this._register(this._previewEditorObs.setDecorations(this._modifiedDecorations));\n      this._register(autorun(reader => {\n        const width = this._previewEditorObs.contentWidth.read(reader);\n        const lines = this._text.read(reader).split('\\n').length - 1;\n        const height = this._editor.getOption(67 /* EditorOption.lineHeight */) * lines;\n        if (width <= 0) {\n          return;\n        }\n        this._previewEditor.layout({\n          height: height,\n          width: width\n        });\n      }));\n      this._register(autorun(reader => {\n        /** @description update position */\n        this._position.read(reader);\n        this._editor.layoutOverlayWidget(this);\n      }));\n      this._register(autorun(reader => {\n        /** @description scroll change */\n        this._scrollChanged.read(reader);\n        const position = this._position.read(reader);\n        if (!position) {\n          return;\n        }\n        this._editor.layoutOverlayWidget(this);\n      }));\n    }\n    getId() {\n      return this.id;\n    }\n    getDomNode() {\n      return this._nodes;\n    }\n    getPosition() {\n      const position = this._position.get();\n      if (!position) {\n        return null;\n      }\n      const layoutInfo = this._editor.getLayoutInfo();\n      const visibPos = this._editor.getScrolledVisiblePosition(new Position(position.top, 1));\n      if (!visibPos) {\n        return null;\n      }\n      const top = visibPos.top - 1; //-1 to offset the border width\n      const offset = this._editor.getOffsetForColumn(position.left.lineNumber, position.left.column);\n      const left = layoutInfo.contentLeft + offset + 10;\n      return {\n        preference: {\n          left,\n          top\n        }\n      };\n    }\n  };\n  return InlineEditSideBySideContentWidget;\n})();\nInlineEditSideBySideContentWidget = InlineEditSideBySideContentWidget_1 = __decorate([__param(5, IInstantiationService)], InlineEditSideBySideContentWidget);", "map": {"version": 3, "names": ["__decorate", "decorators", "target", "key", "desc", "c", "arguments", "length", "r", "Object", "getOwnPropertyDescriptor", "d", "Reflect", "decorate", "i", "defineProperty", "__param", "paramIndex", "decorator", "InlineEditSideBySideWidget_1", "InlineEditSideBySideContentWidget_1", "$", "CancellationToken", "Disposable", "toDisposable", "ObservablePromise", "autorun", "autorunWithStore", "derived", "observableSignalFromEvent", "derivedDisposable", "URI", "observableCodeEditor", "EmbeddedCodeEditorWidget", "IDiffProviderFactoryService", "diffAddDecoration", "diffAddDecorationEmpty", "diffDeleteDecoration", "diffDeleteDecorationEmpty", "diffLineAddDecorationBackgroundWithIndicator", "diffLineDeleteDecorationBackgroundWithIndicator", "diffWholeLineAddDecoration", "diffWholeLineDeleteDecoration", "Position", "Range", "PLAINTEXT_LANGUAGE_ID", "TextModel", "IModelService", "IInstantiationService", "range", "start", "end", "step", "undefined", "n", "removeIndentation", "lines", "indentation", "match", "text", "map", "l", "replace", "RegExp", "shift", "InlineEditSideBySideWidget", "_modelId", "_createUniqueUri", "from", "scheme", "path", "Date", "toString", "String", "constructor", "_editor", "_model", "_instantiationService", "_diffProviderFactoryService", "_modelService", "_this", "this", "_position", "reader", "ghostText", "read", "startLineNumber", "endLineNumber", "startColumn", "endColumn", "editor<PERSON><PERSON><PERSON>", "getModel", "Array", "lengths", "lineNumber", "getLineLastNonWhitespaceColumn", "maxColumn", "Math", "max", "lineOfMaxColumn", "indexOf", "position", "pos", "top", "left", "_text", "t", "split", "join", "_originalModel", "createModel", "keepObserved", "_store", "_modifiedModel", "_diff", "_diffPromise", "promiseResult", "data", "originalText", "getValueInRange", "modifiedText", "get", "setValue", "createDiffProvider", "diffAlgorithm", "fromFn", "_asyncToGenerator", "result", "computeDiff", "computeMoves", "ignoreTrimWhitespace", "maxComputationTimeMs", "None", "identical", "changes", "_register", "store", "model", "contentWidget", "add", "createInstance", "InlineEditSideBySideContentWidget", "addOverlayWidget", "removeOverlayWidget", "id", "_shift", "allowEditorOverflow", "_nodes", "_scrollChanged", "onDidScrollChange", "_previewEditor", "glyphMargin", "lineNumbers", "minimap", "enabled", "guides", "bracketPairs", "bracketPairsHorizontal", "highlightActiveIndentation", "folding", "selectOnLineNumbers", "selection<PERSON>ighlight", "columnSelection", "overviewRulerBorder", "overviewRulerLanes", "lineDecorationsWidth", "lineNumbersMinChars", "scrollbar", "vertical", "horizontal", "alwaysConsumeMouseWheel", "handleMouseWheel", "readOnly", "wordWrap", "wordWrapOverride1", "wordWrapOverride2", "wrappingIndent", "wrappingStrategy", "contributions", "isSimpleWidget", "_previewEditorObs", "_editorObs", "_previewTextModel", "getLanguageId", "DEFAULT_CREATION_OPTIONS", "_setText", "edit", "recomputeInitiallyAndOnChange", "_decorations", "org", "mod", "diff", "originalDecorations", "modifiedDecorations", "innerChanges", "modifiedRange", "equalsRange", "getFullModelRange", "moveRange", "m", "original", "isEmpty", "push", "toInclusiveRange", "options", "modified", "contains", "originalRange", "_originalDecorations", "_modifiedDecorations", "setModel", "setDecorations", "width", "contentWidth", "height", "getOption", "layout", "layoutOverlayWidget", "getId", "getDomNode", "getPosition", "layoutInfo", "getLayoutInfo", "visibPos", "getScrolledVisiblePosition", "offset", "getOffsetForColumn", "column", "contentLeft", "preference"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/contrib/inlineEdit/browser/inlineEditSideBySideWidget.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = (this && this.__param) || function (paramIndex, decorator) {\n    return function (target, key) { decorator(target, key, paramIndex); }\n};\nvar InlineEditSideBySideWidget_1, InlineEditSideBySideContentWidget_1;\nimport { $ } from '../../../../base/browser/dom.js';\nimport { CancellationToken } from '../../../../base/common/cancellation.js';\nimport { Disposable, toDisposable } from '../../../../base/common/lifecycle.js';\nimport { ObservablePromise, autorun, autorunWithStore, derived, observableSignalFromEvent } from '../../../../base/common/observable.js';\nimport { derivedDisposable } from '../../../../base/common/observableInternal/derived.js';\nimport { URI } from '../../../../base/common/uri.js';\nimport './inlineEditSideBySideWidget.css';\nimport { observableCodeEditor } from '../../../browser/observableCodeEditor.js';\nimport { EmbeddedCodeEditorWidget } from '../../../browser/widget/codeEditor/embeddedCodeEditorWidget.js';\nimport { IDiffProviderFactoryService } from '../../../browser/widget/diffEditor/diffProviderFactoryService.js';\nimport { diffAddDecoration, diffAddDecorationEmpty, diffDeleteDecoration, diffDeleteDecorationEmpty, diffLineAddDecorationBackgroundWithIndicator, diffLineDeleteDecorationBackgroundWithIndicator, diffWholeLineAddDecoration, diffWholeLineDeleteDecoration } from '../../../browser/widget/diffEditor/registrations.contribution.js';\nimport { Position } from '../../../common/core/position.js';\nimport { Range } from '../../../common/core/range.js';\nimport { PLAINTEXT_LANGUAGE_ID } from '../../../common/languages/modesRegistry.js';\nimport { TextModel } from '../../../common/model/textModel.js';\nimport { IModelService } from '../../../common/services/model.js';\nimport { IInstantiationService } from '../../../../platform/instantiation/common/instantiation.js';\nfunction* range(start, end, step = 1) {\n    if (end === undefined) {\n        [end, start] = [start, 0];\n    }\n    for (let n = start; n < end; n += step) {\n        yield n;\n    }\n}\nfunction removeIndentation(lines) {\n    const indentation = lines[0].match(/^\\s*/)?.[0] ?? '';\n    const length = indentation.length;\n    return {\n        text: lines.map(l => l.replace(new RegExp('^' + indentation), '')),\n        shift: length\n    };\n}\nlet InlineEditSideBySideWidget = class InlineEditSideBySideWidget extends Disposable {\n    static { InlineEditSideBySideWidget_1 = this; }\n    static { this._modelId = 0; }\n    static _createUniqueUri() {\n        return URI.from({ scheme: 'inline-edit-widget', path: new Date().toString() + String(InlineEditSideBySideWidget_1._modelId++) });\n    }\n    constructor(_editor, _model, _instantiationService, _diffProviderFactoryService, _modelService) {\n        super();\n        this._editor = _editor;\n        this._model = _model;\n        this._instantiationService = _instantiationService;\n        this._diffProviderFactoryService = _diffProviderFactoryService;\n        this._modelService = _modelService;\n        this._position = derived(this, reader => {\n            const ghostText = this._model.read(reader);\n            if (!ghostText || ghostText.text.length === 0) {\n                return null;\n            }\n            if (ghostText.range.startLineNumber === ghostText.range.endLineNumber && !(ghostText.range.startColumn === ghostText.range.endColumn && ghostText.range.startColumn === 1)) {\n                //for inner-line suggestions we still want to use minimal ghost text\n                return null;\n            }\n            const editorModel = this._editor.getModel();\n            if (!editorModel) {\n                return null;\n            }\n            const lines = Array.from(range(ghostText.range.startLineNumber, ghostText.range.endLineNumber + 1));\n            const lengths = lines.map(lineNumber => editorModel.getLineLastNonWhitespaceColumn(lineNumber));\n            const maxColumn = Math.max(...lengths);\n            const lineOfMaxColumn = lines[lengths.indexOf(maxColumn)];\n            const position = new Position(lineOfMaxColumn, maxColumn);\n            const pos = {\n                top: ghostText.range.startLineNumber,\n                left: position\n            };\n            return pos;\n        });\n        this._text = derived(this, reader => {\n            const ghostText = this._model.read(reader);\n            if (!ghostText) {\n                return { text: '', shift: 0 };\n            }\n            const t = removeIndentation(ghostText.text.split('\\n'));\n            return {\n                text: t.text.join('\\n'),\n                shift: t.shift\n            };\n        });\n        this._originalModel = derivedDisposable(() => this._modelService.createModel('', null, InlineEditSideBySideWidget_1._createUniqueUri())).keepObserved(this._store);\n        this._modifiedModel = derivedDisposable(() => this._modelService.createModel('', null, InlineEditSideBySideWidget_1._createUniqueUri())).keepObserved(this._store);\n        this._diff = derived(this, reader => {\n            return this._diffPromise.read(reader)?.promiseResult.read(reader)?.data;\n        });\n        this._diffPromise = derived(this, reader => {\n            const ghostText = this._model.read(reader);\n            if (!ghostText) {\n                return;\n            }\n            const editorModel = this._editor.getModel();\n            if (!editorModel) {\n                return;\n            }\n            const originalText = removeIndentation(editorModel.getValueInRange(ghostText.range).split('\\n')).text.join('\\n');\n            const modifiedText = removeIndentation(ghostText.text.split('\\n')).text.join('\\n');\n            this._originalModel.get().setValue(originalText);\n            this._modifiedModel.get().setValue(modifiedText);\n            const d = this._diffProviderFactoryService.createDiffProvider({ diffAlgorithm: 'advanced' });\n            return ObservablePromise.fromFn(async () => {\n                const result = await d.computeDiff(this._originalModel.get(), this._modifiedModel.get(), {\n                    computeMoves: false,\n                    ignoreTrimWhitespace: false,\n                    maxComputationTimeMs: 1000,\n                }, CancellationToken.None);\n                if (result.identical) {\n                    return undefined;\n                }\n                return result.changes;\n            });\n        });\n        this._register(autorunWithStore((reader, store) => {\n            /** @description setup content widget */\n            const model = this._model.read(reader);\n            if (!model) {\n                return;\n            }\n            if (this._position.get() === null) {\n                return;\n            }\n            const contentWidget = store.add(this._instantiationService.createInstance(InlineEditSideBySideContentWidget, this._editor, this._position, this._text.map(t => t.text), this._text.map(t => t.shift), this._diff));\n            _editor.addOverlayWidget(contentWidget);\n            store.add(toDisposable(() => _editor.removeOverlayWidget(contentWidget)));\n        }));\n    }\n};\nInlineEditSideBySideWidget = InlineEditSideBySideWidget_1 = __decorate([\n    __param(2, IInstantiationService),\n    __param(3, IDiffProviderFactoryService),\n    __param(4, IModelService)\n], InlineEditSideBySideWidget);\nexport { InlineEditSideBySideWidget };\nlet InlineEditSideBySideContentWidget = class InlineEditSideBySideContentWidget extends Disposable {\n    static { InlineEditSideBySideContentWidget_1 = this; }\n    static { this.id = 0; }\n    constructor(_editor, _position, _text, _shift, _diff, _instantiationService) {\n        super();\n        this._editor = _editor;\n        this._position = _position;\n        this._text = _text;\n        this._shift = _shift;\n        this._diff = _diff;\n        this._instantiationService = _instantiationService;\n        this.id = `InlineEditSideBySideContentWidget${InlineEditSideBySideContentWidget_1.id++}`;\n        this.allowEditorOverflow = false;\n        this._nodes = $('div.inlineEditSideBySide', undefined);\n        this._scrollChanged = observableSignalFromEvent('editor.onDidScrollChange', this._editor.onDidScrollChange);\n        this._previewEditor = this._register(this._instantiationService.createInstance(EmbeddedCodeEditorWidget, this._nodes, {\n            glyphMargin: false,\n            lineNumbers: 'off',\n            minimap: { enabled: false },\n            guides: {\n                indentation: false,\n                bracketPairs: false,\n                bracketPairsHorizontal: false,\n                highlightActiveIndentation: false,\n            },\n            folding: false,\n            selectOnLineNumbers: false,\n            selectionHighlight: false,\n            columnSelection: false,\n            overviewRulerBorder: false,\n            overviewRulerLanes: 0,\n            lineDecorationsWidth: 0,\n            lineNumbersMinChars: 0,\n            scrollbar: { vertical: 'hidden', horizontal: 'hidden', alwaysConsumeMouseWheel: false, handleMouseWheel: false },\n            readOnly: true,\n            wordWrap: 'off',\n            wordWrapOverride1: 'off',\n            wordWrapOverride2: 'off',\n            wrappingIndent: 'none',\n            wrappingStrategy: undefined,\n        }, { contributions: [], isSimpleWidget: true }, this._editor));\n        this._previewEditorObs = observableCodeEditor(this._previewEditor);\n        this._editorObs = observableCodeEditor(this._editor);\n        this._previewTextModel = this._register(this._instantiationService.createInstance(TextModel, '', this._editor.getModel()?.getLanguageId() ?? PLAINTEXT_LANGUAGE_ID, TextModel.DEFAULT_CREATION_OPTIONS, null));\n        this._setText = derived(reader => {\n            const edit = this._text.read(reader);\n            if (!edit) {\n                return;\n            }\n            this._previewTextModel.setValue(edit);\n        }).recomputeInitiallyAndOnChange(this._store);\n        this._decorations = derived(this, (reader) => {\n            this._setText.read(reader);\n            const position = this._position.read(reader);\n            if (!position) {\n                return { org: [], mod: [] };\n            }\n            const diff = this._diff.read(reader);\n            if (!diff) {\n                return { org: [], mod: [] };\n            }\n            const originalDecorations = [];\n            const modifiedDecorations = [];\n            if (diff.length === 1 && diff[0].innerChanges[0].modifiedRange.equalsRange(this._previewTextModel.getFullModelRange())) {\n                return { org: [], mod: [] };\n            }\n            const shift = this._shift.get();\n            const moveRange = (range) => {\n                return new Range(range.startLineNumber + position.top - 1, range.startColumn + shift, range.endLineNumber + position.top - 1, range.endColumn + shift);\n            };\n            for (const m of diff) {\n                if (!m.original.isEmpty) {\n                    originalDecorations.push({ range: moveRange(m.original.toInclusiveRange()), options: diffLineDeleteDecorationBackgroundWithIndicator });\n                }\n                if (!m.modified.isEmpty) {\n                    modifiedDecorations.push({ range: m.modified.toInclusiveRange(), options: diffLineAddDecorationBackgroundWithIndicator });\n                }\n                if (m.modified.isEmpty || m.original.isEmpty) {\n                    if (!m.original.isEmpty) {\n                        originalDecorations.push({ range: moveRange(m.original.toInclusiveRange()), options: diffWholeLineDeleteDecoration });\n                    }\n                    if (!m.modified.isEmpty) {\n                        modifiedDecorations.push({ range: m.modified.toInclusiveRange(), options: diffWholeLineAddDecoration });\n                    }\n                }\n                else {\n                    for (const i of m.innerChanges || []) {\n                        // Don't show empty markers outside the line range\n                        if (m.original.contains(i.originalRange.startLineNumber)) {\n                            originalDecorations.push({ range: moveRange(i.originalRange), options: i.originalRange.isEmpty() ? diffDeleteDecorationEmpty : diffDeleteDecoration });\n                        }\n                        if (m.modified.contains(i.modifiedRange.startLineNumber)) {\n                            modifiedDecorations.push({ range: i.modifiedRange, options: i.modifiedRange.isEmpty() ? diffAddDecorationEmpty : diffAddDecoration });\n                        }\n                    }\n                }\n            }\n            return { org: originalDecorations, mod: modifiedDecorations };\n        });\n        this._originalDecorations = derived(this, reader => {\n            return this._decorations.read(reader).org;\n        });\n        this._modifiedDecorations = derived(this, reader => {\n            return this._decorations.read(reader).mod;\n        });\n        this._previewEditor.setModel(this._previewTextModel);\n        this._register(this._editorObs.setDecorations(this._originalDecorations));\n        this._register(this._previewEditorObs.setDecorations(this._modifiedDecorations));\n        this._register(autorun(reader => {\n            const width = this._previewEditorObs.contentWidth.read(reader);\n            const lines = this._text.read(reader).split('\\n').length - 1;\n            const height = this._editor.getOption(67 /* EditorOption.lineHeight */) * lines;\n            if (width <= 0) {\n                return;\n            }\n            this._previewEditor.layout({ height: height, width: width });\n        }));\n        this._register(autorun(reader => {\n            /** @description update position */\n            this._position.read(reader);\n            this._editor.layoutOverlayWidget(this);\n        }));\n        this._register(autorun(reader => {\n            /** @description scroll change */\n            this._scrollChanged.read(reader);\n            const position = this._position.read(reader);\n            if (!position) {\n                return;\n            }\n            this._editor.layoutOverlayWidget(this);\n        }));\n    }\n    getId() { return this.id; }\n    getDomNode() {\n        return this._nodes;\n    }\n    getPosition() {\n        const position = this._position.get();\n        if (!position) {\n            return null;\n        }\n        const layoutInfo = this._editor.getLayoutInfo();\n        const visibPos = this._editor.getScrolledVisiblePosition(new Position(position.top, 1));\n        if (!visibPos) {\n            return null;\n        }\n        const top = visibPos.top - 1; //-1 to offset the border width\n        const offset = this._editor.getOffsetForColumn(position.left.lineNumber, position.left.column);\n        const left = layoutInfo.contentLeft + offset + 10;\n        return {\n            preference: {\n                left,\n                top,\n            }\n        };\n    }\n};\nInlineEditSideBySideContentWidget = InlineEditSideBySideContentWidget_1 = __decorate([\n    __param(5, IInstantiationService)\n], InlineEditSideBySideContentWidget);\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA,IAAIA,UAAU,GAAI,IAAI,IAAI,IAAI,CAACA,UAAU,IAAK,UAAUC,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAE;EACnF,IAAIC,CAAC,GAAGC,SAAS,CAACC,MAAM;IAAEC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGH,MAAM,GAAGE,IAAI,KAAK,IAAI,GAAGA,IAAI,GAAGK,MAAM,CAACC,wBAAwB,CAACR,MAAM,EAAEC,GAAG,CAAC,GAAGC,IAAI;IAAEO,CAAC;EAC5H,IAAI,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,CAACC,QAAQ,KAAK,UAAU,EAAEL,CAAC,GAAGI,OAAO,CAACC,QAAQ,CAACZ,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,CAAC,CAAC,KAC1H,KAAK,IAAIU,CAAC,GAAGb,UAAU,CAACM,MAAM,GAAG,CAAC,EAAEO,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE,IAAIH,CAAC,GAAGV,UAAU,CAACa,CAAC,CAAC,EAAEN,CAAC,GAAG,CAACH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACH,CAAC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACT,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,GAAGG,CAAC,CAACT,MAAM,EAAEC,GAAG,CAAC,KAAKK,CAAC;EACjJ,OAAOH,CAAC,GAAG,CAAC,IAAIG,CAAC,IAAIC,MAAM,CAACM,cAAc,CAACb,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,EAAEA,CAAC;AACjE,CAAC;AACD,IAAIQ,OAAO,GAAI,IAAI,IAAI,IAAI,CAACA,OAAO,IAAK,UAAUC,UAAU,EAAEC,SAAS,EAAE;EACrE,OAAO,UAAUhB,MAAM,EAAEC,GAAG,EAAE;IAAEe,SAAS,CAAChB,MAAM,EAAEC,GAAG,EAAEc,UAAU,CAAC;EAAE,CAAC;AACzE,CAAC;AACD,IAAIE,4BAA4B,EAAEC,mCAAmC;AACrE,SAASC,CAAC,QAAQ,iCAAiC;AACnD,SAASC,iBAAiB,QAAQ,yCAAyC;AAC3E,SAASC,UAAU,EAAEC,YAAY,QAAQ,sCAAsC;AAC/E,SAASC,iBAAiB,EAAEC,OAAO,EAAEC,gBAAgB,EAAEC,OAAO,EAAEC,yBAAyB,QAAQ,uCAAuC;AACxI,SAASC,iBAAiB,QAAQ,uDAAuD;AACzF,SAASC,GAAG,QAAQ,gCAAgC;AACpD,OAAO,kCAAkC;AACzC,SAASC,oBAAoB,QAAQ,0CAA0C;AAC/E,SAASC,wBAAwB,QAAQ,gEAAgE;AACzG,SAASC,2BAA2B,QAAQ,kEAAkE;AAC9G,SAASC,iBAAiB,EAAEC,sBAAsB,EAAEC,oBAAoB,EAAEC,yBAAyB,EAAEC,4CAA4C,EAAEC,+CAA+C,EAAEC,0BAA0B,EAAEC,6BAA6B,QAAQ,kEAAkE;AACvU,SAASC,QAAQ,QAAQ,kCAAkC;AAC3D,SAASC,KAAK,QAAQ,+BAA+B;AACrD,SAASC,qBAAqB,QAAQ,4CAA4C;AAClF,SAASC,SAAS,QAAQ,oCAAoC;AAC9D,SAASC,aAAa,QAAQ,mCAAmC;AACjE,SAASC,qBAAqB,QAAQ,4DAA4D;AAClG,UAAUC,KAAKA,CAACC,KAAK,EAAEC,GAAG,EAAEC,IAAI,GAAG,CAAC,EAAE;EAClC,IAAID,GAAG,KAAKE,SAAS,EAAE;IACnB,CAACF,GAAG,EAAED,KAAK,CAAC,GAAG,CAACA,KAAK,EAAE,CAAC,CAAC;EAC7B;EACA,KAAK,IAAII,CAAC,GAAGJ,KAAK,EAAEI,CAAC,GAAGH,GAAG,EAAEG,CAAC,IAAIF,IAAI,EAAE;IACpC,MAAME,CAAC;EACX;AACJ;AACA,SAASC,iBAAiBA,CAACC,KAAK,EAAE;EAC9B,MAAMC,WAAW,GAAGD,KAAK,CAAC,CAAC,CAAC,CAACE,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE;EACrD,MAAMnD,MAAM,GAAGkD,WAAW,CAAClD,MAAM;EACjC,OAAO;IACHoD,IAAI,EAAEH,KAAK,CAACI,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,OAAO,CAAC,IAAIC,MAAM,CAAC,GAAG,GAAGN,WAAW,CAAC,EAAE,EAAE,CAAC,CAAC;IAClEO,KAAK,EAAEzD;EACX,CAAC;AACL;AACA,IAAI0D,0BAA0B;EAAA,IAA1BA,0BAA0B,GAAG,MAAMA,0BAA0B,SAAS1C,UAAU,CAAC;IACjF;MAASJ,4BAA4B,GAAG,IAAI;IAAE;IAC9C;MAAS,IAAI,CAAC+C,QAAQ,GAAG,CAAC;IAAE;IAC5B,OAAOC,gBAAgBA,CAAA,EAAG;MACtB,OAAOpC,GAAG,CAACqC,IAAI,CAAC;QAAEC,MAAM,EAAE,oBAAoB;QAAEC,IAAI,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,GAAGC,MAAM,CAACtD,4BAA4B,CAAC+C,QAAQ,EAAE;MAAE,CAAC,CAAC;IACpI;IACAQ,WAAWA,CAACC,OAAO,EAAEC,MAAM,EAAEC,qBAAqB,EAAEC,2BAA2B,EAAEC,aAAa,EAAE;MAAA,IAAAC,KAAA;MAC5F,KAAK,CAAC,CAAC;MAAAA,KAAA,GAAAC,IAAA;MACP,IAAI,CAACN,OAAO,GAAGA,OAAO;MACtB,IAAI,CAACC,MAAM,GAAGA,MAAM;MACpB,IAAI,CAACC,qBAAqB,GAAGA,qBAAqB;MAClD,IAAI,CAACC,2BAA2B,GAAGA,2BAA2B;MAC9D,IAAI,CAACC,aAAa,GAAGA,aAAa;MAClC,IAAI,CAACG,SAAS,GAAGtD,OAAO,CAAC,IAAI,EAAEuD,MAAM,IAAI;QACrC,MAAMC,SAAS,GAAG,IAAI,CAACR,MAAM,CAACS,IAAI,CAACF,MAAM,CAAC;QAC1C,IAAI,CAACC,SAAS,IAAIA,SAAS,CAACzB,IAAI,CAACpD,MAAM,KAAK,CAAC,EAAE;UAC3C,OAAO,IAAI;QACf;QACA,IAAI6E,SAAS,CAACnC,KAAK,CAACqC,eAAe,KAAKF,SAAS,CAACnC,KAAK,CAACsC,aAAa,IAAI,EAAEH,SAAS,CAACnC,KAAK,CAACuC,WAAW,KAAKJ,SAAS,CAACnC,KAAK,CAACwC,SAAS,IAAIL,SAAS,CAACnC,KAAK,CAACuC,WAAW,KAAK,CAAC,CAAC,EAAE;UACxK;UACA,OAAO,IAAI;QACf;QACA,MAAME,WAAW,GAAG,IAAI,CAACf,OAAO,CAACgB,QAAQ,CAAC,CAAC;QAC3C,IAAI,CAACD,WAAW,EAAE;UACd,OAAO,IAAI;QACf;QACA,MAAMlC,KAAK,GAAGoC,KAAK,CAACxB,IAAI,CAACnB,KAAK,CAACmC,SAAS,CAACnC,KAAK,CAACqC,eAAe,EAAEF,SAAS,CAACnC,KAAK,CAACsC,aAAa,GAAG,CAAC,CAAC,CAAC;QACnG,MAAMM,OAAO,GAAGrC,KAAK,CAACI,GAAG,CAACkC,UAAU,IAAIJ,WAAW,CAACK,8BAA8B,CAACD,UAAU,CAAC,CAAC;QAC/F,MAAME,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAGL,OAAO,CAAC;QACtC,MAAMM,eAAe,GAAG3C,KAAK,CAACqC,OAAO,CAACO,OAAO,CAACJ,SAAS,CAAC,CAAC;QACzD,MAAMK,QAAQ,GAAG,IAAI1D,QAAQ,CAACwD,eAAe,EAAEH,SAAS,CAAC;QACzD,MAAMM,GAAG,GAAG;UACRC,GAAG,EAAEnB,SAAS,CAACnC,KAAK,CAACqC,eAAe;UACpCkB,IAAI,EAAEH;QACV,CAAC;QACD,OAAOC,GAAG;MACd,CAAC,CAAC;MACF,IAAI,CAACG,KAAK,GAAG7E,OAAO,CAAC,IAAI,EAAEuD,MAAM,IAAI;QACjC,MAAMC,SAAS,GAAG,IAAI,CAACR,MAAM,CAACS,IAAI,CAACF,MAAM,CAAC;QAC1C,IAAI,CAACC,SAAS,EAAE;UACZ,OAAO;YAAEzB,IAAI,EAAE,EAAE;YAAEK,KAAK,EAAE;UAAE,CAAC;QACjC;QACA,MAAM0C,CAAC,GAAGnD,iBAAiB,CAAC6B,SAAS,CAACzB,IAAI,CAACgD,KAAK,CAAC,IAAI,CAAC,CAAC;QACvD,OAAO;UACHhD,IAAI,EAAE+C,CAAC,CAAC/C,IAAI,CAACiD,IAAI,CAAC,IAAI,CAAC;UACvB5C,KAAK,EAAE0C,CAAC,CAAC1C;QACb,CAAC;MACL,CAAC,CAAC;MACF,IAAI,CAAC6C,cAAc,GAAG/E,iBAAiB,CAAC,MAAM,IAAI,CAACiD,aAAa,CAAC+B,WAAW,CAAC,EAAE,EAAE,IAAI,EAAE3F,4BAA4B,CAACgD,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC4C,YAAY,CAAC,IAAI,CAACC,MAAM,CAAC;MAClK,IAAI,CAACC,cAAc,GAAGnF,iBAAiB,CAAC,MAAM,IAAI,CAACiD,aAAa,CAAC+B,WAAW,CAAC,EAAE,EAAE,IAAI,EAAE3F,4BAA4B,CAACgD,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC4C,YAAY,CAAC,IAAI,CAACC,MAAM,CAAC;MAClK,IAAI,CAACE,KAAK,GAAGtF,OAAO,CAAC,IAAI,EAAEuD,MAAM,IAAI;QACjC,OAAO,IAAI,CAACgC,YAAY,CAAC9B,IAAI,CAACF,MAAM,CAAC,EAAEiC,aAAa,CAAC/B,IAAI,CAACF,MAAM,CAAC,EAAEkC,IAAI;MAC3E,CAAC,CAAC;MACF,IAAI,CAACF,YAAY,GAAGvF,OAAO,CAAC,IAAI,EAAEuD,MAAM,IAAI;QACxC,MAAMC,SAAS,GAAG,IAAI,CAACR,MAAM,CAACS,IAAI,CAACF,MAAM,CAAC;QAC1C,IAAI,CAACC,SAAS,EAAE;UACZ;QACJ;QACA,MAAMM,WAAW,GAAG,IAAI,CAACf,OAAO,CAACgB,QAAQ,CAAC,CAAC;QAC3C,IAAI,CAACD,WAAW,EAAE;UACd;QACJ;QACA,MAAM4B,YAAY,GAAG/D,iBAAiB,CAACmC,WAAW,CAAC6B,eAAe,CAACnC,SAAS,CAACnC,KAAK,CAAC,CAAC0D,KAAK,CAAC,IAAI,CAAC,CAAC,CAAChD,IAAI,CAACiD,IAAI,CAAC,IAAI,CAAC;QAChH,MAAMY,YAAY,GAAGjE,iBAAiB,CAAC6B,SAAS,CAACzB,IAAI,CAACgD,KAAK,CAAC,IAAI,CAAC,CAAC,CAAChD,IAAI,CAACiD,IAAI,CAAC,IAAI,CAAC;QAClF,IAAI,CAACC,cAAc,CAACY,GAAG,CAAC,CAAC,CAACC,QAAQ,CAACJ,YAAY,CAAC;QAChD,IAAI,CAACL,cAAc,CAACQ,GAAG,CAAC,CAAC,CAACC,QAAQ,CAACF,YAAY,CAAC;QAChD,MAAM7G,CAAC,GAAG,IAAI,CAACmE,2BAA2B,CAAC6C,kBAAkB,CAAC;UAAEC,aAAa,EAAE;QAAW,CAAC,CAAC;QAC5F,OAAOnG,iBAAiB,CAACoG,MAAM,cAAAC,iBAAA,CAAC,aAAY;UACxC,MAAMC,MAAM,SAASpH,CAAC,CAACqH,WAAW,CAAChD,KAAI,CAAC6B,cAAc,CAACY,GAAG,CAAC,CAAC,EAAEzC,KAAI,CAACiC,cAAc,CAACQ,GAAG,CAAC,CAAC,EAAE;YACrFQ,YAAY,EAAE,KAAK;YACnBC,oBAAoB,EAAE,KAAK;YAC3BC,oBAAoB,EAAE;UAC1B,CAAC,EAAE7G,iBAAiB,CAAC8G,IAAI,CAAC;UAC1B,IAAIL,MAAM,CAACM,SAAS,EAAE;YAClB,OAAOhF,SAAS;UACpB;UACA,OAAO0E,MAAM,CAACO,OAAO;QACzB,CAAC,EAAC;MACN,CAAC,CAAC;MACF,IAAI,CAACC,SAAS,CAAC5G,gBAAgB,CAAC,CAACwD,MAAM,EAAEqD,KAAK,KAAK;QAC/C;QACA,MAAMC,KAAK,GAAG,IAAI,CAAC7D,MAAM,CAACS,IAAI,CAACF,MAAM,CAAC;QACtC,IAAI,CAACsD,KAAK,EAAE;UACR;QACJ;QACA,IAAI,IAAI,CAACvD,SAAS,CAACuC,GAAG,CAAC,CAAC,KAAK,IAAI,EAAE;UAC/B;QACJ;QACA,MAAMiB,aAAa,GAAGF,KAAK,CAACG,GAAG,CAAC,IAAI,CAAC9D,qBAAqB,CAAC+D,cAAc,CAACC,iCAAiC,EAAE,IAAI,CAAClE,OAAO,EAAE,IAAI,CAACO,SAAS,EAAE,IAAI,CAACuB,KAAK,CAAC7C,GAAG,CAAC8C,CAAC,IAAIA,CAAC,CAAC/C,IAAI,CAAC,EAAE,IAAI,CAAC8C,KAAK,CAAC7C,GAAG,CAAC8C,CAAC,IAAIA,CAAC,CAAC1C,KAAK,CAAC,EAAE,IAAI,CAACkD,KAAK,CAAC,CAAC;QAClNvC,OAAO,CAACmE,gBAAgB,CAACJ,aAAa,CAAC;QACvCF,KAAK,CAACG,GAAG,CAACnH,YAAY,CAAC,MAAMmD,OAAO,CAACoE,mBAAmB,CAACL,aAAa,CAAC,CAAC,CAAC;MAC7E,CAAC,CAAC,CAAC;IACP;EACJ,CAAC;EAAA,OA7FGzE,0BAA0B;AAAA,IA6F7B;AACDA,0BAA0B,GAAG9C,4BAA4B,GAAGnB,UAAU,CAAC,CACnEgB,OAAO,CAAC,CAAC,EAAEgC,qBAAqB,CAAC,EACjChC,OAAO,CAAC,CAAC,EAAEkB,2BAA2B,CAAC,EACvClB,OAAO,CAAC,CAAC,EAAE+B,aAAa,CAAC,CAC5B,EAAEkB,0BAA0B,CAAC;AAC9B,SAASA,0BAA0B;AACnC,IAAI4E,iCAAiC;EAAA,IAAjCA,iCAAiC,GAAG,MAAMA,iCAAiC,SAAStH,UAAU,CAAC;IAC/F;MAASH,mCAAmC,GAAG,IAAI;IAAE;IACrD;MAAS,IAAI,CAAC4H,EAAE,GAAG,CAAC;IAAE;IACtBtE,WAAWA,CAACC,OAAO,EAAEO,SAAS,EAAEuB,KAAK,EAAEwC,MAAM,EAAE/B,KAAK,EAAErC,qBAAqB,EAAE;MACzE,KAAK,CAAC,CAAC;MACP,IAAI,CAACF,OAAO,GAAGA,OAAO;MACtB,IAAI,CAACO,SAAS,GAAGA,SAAS;MAC1B,IAAI,CAACuB,KAAK,GAAGA,KAAK;MAClB,IAAI,CAACwC,MAAM,GAAGA,MAAM;MACpB,IAAI,CAAC/B,KAAK,GAAGA,KAAK;MAClB,IAAI,CAACrC,qBAAqB,GAAGA,qBAAqB;MAClD,IAAI,CAACmE,EAAE,GAAG,oCAAoC5H,mCAAmC,CAAC4H,EAAE,EAAE,EAAE;MACxF,IAAI,CAACE,mBAAmB,GAAG,KAAK;MAChC,IAAI,CAACC,MAAM,GAAG9H,CAAC,CAAC,0BAA0B,EAAEgC,SAAS,CAAC;MACtD,IAAI,CAAC+F,cAAc,GAAGvH,yBAAyB,CAAC,0BAA0B,EAAE,IAAI,CAAC8C,OAAO,CAAC0E,iBAAiB,CAAC;MAC3G,IAAI,CAACC,cAAc,GAAG,IAAI,CAACf,SAAS,CAAC,IAAI,CAAC1D,qBAAqB,CAAC+D,cAAc,CAAC3G,wBAAwB,EAAE,IAAI,CAACkH,MAAM,EAAE;QAClHI,WAAW,EAAE,KAAK;QAClBC,WAAW,EAAE,KAAK;QAClBC,OAAO,EAAE;UAAEC,OAAO,EAAE;QAAM,CAAC;QAC3BC,MAAM,EAAE;UACJlG,WAAW,EAAE,KAAK;UAClBmG,YAAY,EAAE,KAAK;UACnBC,sBAAsB,EAAE,KAAK;UAC7BC,0BAA0B,EAAE;QAChC,CAAC;QACDC,OAAO,EAAE,KAAK;QACdC,mBAAmB,EAAE,KAAK;QAC1BC,kBAAkB,EAAE,KAAK;QACzBC,eAAe,EAAE,KAAK;QACtBC,mBAAmB,EAAE,KAAK;QAC1BC,kBAAkB,EAAE,CAAC;QACrBC,oBAAoB,EAAE,CAAC;QACvBC,mBAAmB,EAAE,CAAC;QACtBC,SAAS,EAAE;UAAEC,QAAQ,EAAE,QAAQ;UAAEC,UAAU,EAAE,QAAQ;UAAEC,uBAAuB,EAAE,KAAK;UAAEC,gBAAgB,EAAE;QAAM,CAAC;QAChHC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,KAAK;QACfC,iBAAiB,EAAE,KAAK;QACxBC,iBAAiB,EAAE,KAAK;QACxBC,cAAc,EAAE,MAAM;QACtBC,gBAAgB,EAAE5H;MACtB,CAAC,EAAE;QAAE6H,aAAa,EAAE,EAAE;QAAEC,cAAc,EAAE;MAAK,CAAC,EAAE,IAAI,CAACxG,OAAO,CAAC,CAAC;MAC9D,IAAI,CAACyG,iBAAiB,GAAGpJ,oBAAoB,CAAC,IAAI,CAACsH,cAAc,CAAC;MAClE,IAAI,CAAC+B,UAAU,GAAGrJ,oBAAoB,CAAC,IAAI,CAAC2C,OAAO,CAAC;MACpD,IAAI,CAAC2G,iBAAiB,GAAG,IAAI,CAAC/C,SAAS,CAAC,IAAI,CAAC1D,qBAAqB,CAAC+D,cAAc,CAAC9F,SAAS,EAAE,EAAE,EAAE,IAAI,CAAC6B,OAAO,CAACgB,QAAQ,CAAC,CAAC,EAAE4F,aAAa,CAAC,CAAC,IAAI1I,qBAAqB,EAAEC,SAAS,CAAC0I,wBAAwB,EAAE,IAAI,CAAC,CAAC;MAC9M,IAAI,CAACC,QAAQ,GAAG7J,OAAO,CAACuD,MAAM,IAAI;QAC9B,MAAMuG,IAAI,GAAG,IAAI,CAACjF,KAAK,CAACpB,IAAI,CAACF,MAAM,CAAC;QACpC,IAAI,CAACuG,IAAI,EAAE;UACP;QACJ;QACA,IAAI,CAACJ,iBAAiB,CAAC5D,QAAQ,CAACgE,IAAI,CAAC;MACzC,CAAC,CAAC,CAACC,6BAA6B,CAAC,IAAI,CAAC3E,MAAM,CAAC;MAC7C,IAAI,CAAC4E,YAAY,GAAGhK,OAAO,CAAC,IAAI,EAAGuD,MAAM,IAAK;QAC1C,IAAI,CAACsG,QAAQ,CAACpG,IAAI,CAACF,MAAM,CAAC;QAC1B,MAAMkB,QAAQ,GAAG,IAAI,CAACnB,SAAS,CAACG,IAAI,CAACF,MAAM,CAAC;QAC5C,IAAI,CAACkB,QAAQ,EAAE;UACX,OAAO;YAAEwF,GAAG,EAAE,EAAE;YAAEC,GAAG,EAAE;UAAG,CAAC;QAC/B;QACA,MAAMC,IAAI,GAAG,IAAI,CAAC7E,KAAK,CAAC7B,IAAI,CAACF,MAAM,CAAC;QACpC,IAAI,CAAC4G,IAAI,EAAE;UACP,OAAO;YAAEF,GAAG,EAAE,EAAE;YAAEC,GAAG,EAAE;UAAG,CAAC;QAC/B;QACA,MAAME,mBAAmB,GAAG,EAAE;QAC9B,MAAMC,mBAAmB,GAAG,EAAE;QAC9B,IAAIF,IAAI,CAACxL,MAAM,KAAK,CAAC,IAAIwL,IAAI,CAAC,CAAC,CAAC,CAACG,YAAY,CAAC,CAAC,CAAC,CAACC,aAAa,CAACC,WAAW,CAAC,IAAI,CAACd,iBAAiB,CAACe,iBAAiB,CAAC,CAAC,CAAC,EAAE;UACpH,OAAO;YAAER,GAAG,EAAE,EAAE;YAAEC,GAAG,EAAE;UAAG,CAAC;QAC/B;QACA,MAAM9H,KAAK,GAAG,IAAI,CAACiF,MAAM,CAACxB,GAAG,CAAC,CAAC;QAC/B,MAAM6E,SAAS,GAAIrJ,KAAK,IAAK;UACzB,OAAO,IAAIL,KAAK,CAACK,KAAK,CAACqC,eAAe,GAAGe,QAAQ,CAACE,GAAG,GAAG,CAAC,EAAEtD,KAAK,CAACuC,WAAW,GAAGxB,KAAK,EAAEf,KAAK,CAACsC,aAAa,GAAGc,QAAQ,CAACE,GAAG,GAAG,CAAC,EAAEtD,KAAK,CAACwC,SAAS,GAAGzB,KAAK,CAAC;QAC1J,CAAC;QACD,KAAK,MAAMuI,CAAC,IAAIR,IAAI,EAAE;UAClB,IAAI,CAACQ,CAAC,CAACC,QAAQ,CAACC,OAAO,EAAE;YACrBT,mBAAmB,CAACU,IAAI,CAAC;cAAEzJ,KAAK,EAAEqJ,SAAS,CAACC,CAAC,CAACC,QAAQ,CAACG,gBAAgB,CAAC,CAAC,CAAC;cAAEC,OAAO,EAAEpK;YAAgD,CAAC,CAAC;UAC3I;UACA,IAAI,CAAC+J,CAAC,CAACM,QAAQ,CAACJ,OAAO,EAAE;YACrBR,mBAAmB,CAACS,IAAI,CAAC;cAAEzJ,KAAK,EAAEsJ,CAAC,CAACM,QAAQ,CAACF,gBAAgB,CAAC,CAAC;cAAEC,OAAO,EAAErK;YAA6C,CAAC,CAAC;UAC7H;UACA,IAAIgK,CAAC,CAACM,QAAQ,CAACJ,OAAO,IAAIF,CAAC,CAACC,QAAQ,CAACC,OAAO,EAAE;YAC1C,IAAI,CAACF,CAAC,CAACC,QAAQ,CAACC,OAAO,EAAE;cACrBT,mBAAmB,CAACU,IAAI,CAAC;gBAAEzJ,KAAK,EAAEqJ,SAAS,CAACC,CAAC,CAACC,QAAQ,CAACG,gBAAgB,CAAC,CAAC,CAAC;gBAAEC,OAAO,EAAElK;cAA8B,CAAC,CAAC;YACzH;YACA,IAAI,CAAC6J,CAAC,CAACM,QAAQ,CAACJ,OAAO,EAAE;cACrBR,mBAAmB,CAACS,IAAI,CAAC;gBAAEzJ,KAAK,EAAEsJ,CAAC,CAACM,QAAQ,CAACF,gBAAgB,CAAC,CAAC;gBAAEC,OAAO,EAAEnK;cAA2B,CAAC,CAAC;YAC3G;UACJ,CAAC,MACI;YACD,KAAK,MAAM3B,CAAC,IAAIyL,CAAC,CAACL,YAAY,IAAI,EAAE,EAAE;cAClC;cACA,IAAIK,CAAC,CAACC,QAAQ,CAACM,QAAQ,CAAChM,CAAC,CAACiM,aAAa,CAACzH,eAAe,CAAC,EAAE;gBACtD0G,mBAAmB,CAACU,IAAI,CAAC;kBAAEzJ,KAAK,EAAEqJ,SAAS,CAACxL,CAAC,CAACiM,aAAa,CAAC;kBAAEH,OAAO,EAAE9L,CAAC,CAACiM,aAAa,CAACN,OAAO,CAAC,CAAC,GAAGnK,yBAAyB,GAAGD;gBAAqB,CAAC,CAAC;cAC1J;cACA,IAAIkK,CAAC,CAACM,QAAQ,CAACC,QAAQ,CAAChM,CAAC,CAACqL,aAAa,CAAC7G,eAAe,CAAC,EAAE;gBACtD2G,mBAAmB,CAACS,IAAI,CAAC;kBAAEzJ,KAAK,EAAEnC,CAAC,CAACqL,aAAa;kBAAES,OAAO,EAAE9L,CAAC,CAACqL,aAAa,CAACM,OAAO,CAAC,CAAC,GAAGrK,sBAAsB,GAAGD;gBAAkB,CAAC,CAAC;cACzI;YACJ;UACJ;QACJ;QACA,OAAO;UAAE0J,GAAG,EAAEG,mBAAmB;UAAEF,GAAG,EAAEG;QAAoB,CAAC;MACjE,CAAC,CAAC;MACF,IAAI,CAACe,oBAAoB,GAAGpL,OAAO,CAAC,IAAI,EAAEuD,MAAM,IAAI;QAChD,OAAO,IAAI,CAACyG,YAAY,CAACvG,IAAI,CAACF,MAAM,CAAC,CAAC0G,GAAG;MAC7C,CAAC,CAAC;MACF,IAAI,CAACoB,oBAAoB,GAAGrL,OAAO,CAAC,IAAI,EAAEuD,MAAM,IAAI;QAChD,OAAO,IAAI,CAACyG,YAAY,CAACvG,IAAI,CAACF,MAAM,CAAC,CAAC2G,GAAG;MAC7C,CAAC,CAAC;MACF,IAAI,CAACxC,cAAc,CAAC4D,QAAQ,CAAC,IAAI,CAAC5B,iBAAiB,CAAC;MACpD,IAAI,CAAC/C,SAAS,CAAC,IAAI,CAAC8C,UAAU,CAAC8B,cAAc,CAAC,IAAI,CAACH,oBAAoB,CAAC,CAAC;MACzE,IAAI,CAACzE,SAAS,CAAC,IAAI,CAAC6C,iBAAiB,CAAC+B,cAAc,CAAC,IAAI,CAACF,oBAAoB,CAAC,CAAC;MAChF,IAAI,CAAC1E,SAAS,CAAC7G,OAAO,CAACyD,MAAM,IAAI;QAC7B,MAAMiI,KAAK,GAAG,IAAI,CAAChC,iBAAiB,CAACiC,YAAY,CAAChI,IAAI,CAACF,MAAM,CAAC;QAC9D,MAAM3B,KAAK,GAAG,IAAI,CAACiD,KAAK,CAACpB,IAAI,CAACF,MAAM,CAAC,CAACwB,KAAK,CAAC,IAAI,CAAC,CAACpG,MAAM,GAAG,CAAC;QAC5D,MAAM+M,MAAM,GAAG,IAAI,CAAC3I,OAAO,CAAC4I,SAAS,CAAC,EAAE,CAAC,6BAA6B,CAAC,GAAG/J,KAAK;QAC/E,IAAI4J,KAAK,IAAI,CAAC,EAAE;UACZ;QACJ;QACA,IAAI,CAAC9D,cAAc,CAACkE,MAAM,CAAC;UAAEF,MAAM,EAAEA,MAAM;UAAEF,KAAK,EAAEA;QAAM,CAAC,CAAC;MAChE,CAAC,CAAC,CAAC;MACH,IAAI,CAAC7E,SAAS,CAAC7G,OAAO,CAACyD,MAAM,IAAI;QAC7B;QACA,IAAI,CAACD,SAAS,CAACG,IAAI,CAACF,MAAM,CAAC;QAC3B,IAAI,CAACR,OAAO,CAAC8I,mBAAmB,CAAC,IAAI,CAAC;MAC1C,CAAC,CAAC,CAAC;MACH,IAAI,CAAClF,SAAS,CAAC7G,OAAO,CAACyD,MAAM,IAAI;QAC7B;QACA,IAAI,CAACiE,cAAc,CAAC/D,IAAI,CAACF,MAAM,CAAC;QAChC,MAAMkB,QAAQ,GAAG,IAAI,CAACnB,SAAS,CAACG,IAAI,CAACF,MAAM,CAAC;QAC5C,IAAI,CAACkB,QAAQ,EAAE;UACX;QACJ;QACA,IAAI,CAAC1B,OAAO,CAAC8I,mBAAmB,CAAC,IAAI,CAAC;MAC1C,CAAC,CAAC,CAAC;IACP;IACAC,KAAKA,CAAA,EAAG;MAAE,OAAO,IAAI,CAAC1E,EAAE;IAAE;IAC1B2E,UAAUA,CAAA,EAAG;MACT,OAAO,IAAI,CAACxE,MAAM;IACtB;IACAyE,WAAWA,CAAA,EAAG;MACV,MAAMvH,QAAQ,GAAG,IAAI,CAACnB,SAAS,CAACuC,GAAG,CAAC,CAAC;MACrC,IAAI,CAACpB,QAAQ,EAAE;QACX,OAAO,IAAI;MACf;MACA,MAAMwH,UAAU,GAAG,IAAI,CAAClJ,OAAO,CAACmJ,aAAa,CAAC,CAAC;MAC/C,MAAMC,QAAQ,GAAG,IAAI,CAACpJ,OAAO,CAACqJ,0BAA0B,CAAC,IAAIrL,QAAQ,CAAC0D,QAAQ,CAACE,GAAG,EAAE,CAAC,CAAC,CAAC;MACvF,IAAI,CAACwH,QAAQ,EAAE;QACX,OAAO,IAAI;MACf;MACA,MAAMxH,GAAG,GAAGwH,QAAQ,CAACxH,GAAG,GAAG,CAAC,CAAC,CAAC;MAC9B,MAAM0H,MAAM,GAAG,IAAI,CAACtJ,OAAO,CAACuJ,kBAAkB,CAAC7H,QAAQ,CAACG,IAAI,CAACV,UAAU,EAAEO,QAAQ,CAACG,IAAI,CAAC2H,MAAM,CAAC;MAC9F,MAAM3H,IAAI,GAAGqH,UAAU,CAACO,WAAW,GAAGH,MAAM,GAAG,EAAE;MACjD,OAAO;QACHI,UAAU,EAAE;UACR7H,IAAI;UACJD;QACJ;MACJ,CAAC;IACL;EACJ,CAAC;EAAA,OA5JGsC,iCAAiC;AAAA,IA4JpC;AACDA,iCAAiC,GAAGzH,mCAAmC,GAAGpB,UAAU,CAAC,CACjFgB,OAAO,CAAC,CAAC,EAAEgC,qBAAqB,CAAC,CACpC,EAAE6F,iCAAiC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}