{"ast": null, "code": "import { Subscription } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"./auth.service\";\nimport * as i3 from \"./token-storage.service\";\nexport let AuthTokenService = /*#__PURE__*/(() => {\n  class AuthTokenService {\n    router;\n    authService;\n    tokenStorageService;\n    tokenCheckInterval;\n    checkIntervalMs = 30000; // Check every 30 seconds\n    subscription = new Subscription();\n    constructor(router, authService, tokenStorageService) {\n      this.router = router;\n      this.authService = authService;\n      this.tokenStorageService = tokenStorageService;\n    }\n    handleAuthCodeAndToken() {\n      const authCode = new URLSearchParams(window.location.search).get('code');\n      const accessToken = this.tokenStorageService.getAccessToken();\n      const refreshToken = this.tokenStorageService.getRefreshToken();\n      const loginType = this.tokenStorageService.getLoginType();\n      if (loginType === 'basic') {\n        this.handleBasicLoginFlow(accessToken, refreshToken);\n      } else {\n        this.handleSSOFlow(accessToken, refreshToken, authCode);\n      }\n    }\n    handleBasicLoginFlow(accessToken, refreshToken) {\n      if (!accessToken && refreshToken) {\n        this.handleBasicTokenRefresh();\n      } else if (!accessToken && !refreshToken) {\n        this.router.navigateByUrl('/login');\n      }\n    }\n    handleSSOFlow(accessToken, refreshToken, authCode) {\n      if (!accessToken && refreshToken) {\n        this.router.navigateByUrl(`/callback?refresh_token=${refreshToken}`);\n      } else if (!accessToken && !refreshToken && authCode) {\n        this.router.navigateByUrl(`/callback?code=${authCode}`);\n      }\n    }\n    startTokenCheck() {\n      this.tokenCheckInterval = setInterval(() => {\n        const accessToken = this.tokenStorageService.getAccessToken();\n        const refreshToken = this.tokenStorageService.getRefreshToken();\n        const loginType = this.tokenStorageService.getLoginType();\n        // Only redirect to login if we have no tokens at all and we're not already on login/callback/marketplace pages\n        if (!accessToken && !refreshToken) {\n          const currentUrl = this.router.url;\n          if (!currentUrl.includes('/login') && !currentUrl.includes('/callback') && !currentUrl.includes('/marketplace')) {\n            console.info('No tokens found, redirecting to login');\n            this.tokenStorageService.clearTokens();\n            this.router.navigateByUrl('/login');\n          }\n        } else if (!accessToken && refreshToken) {\n          // Try to refresh the token\n          console.info('Access token missing, attempting refresh');\n          if (loginType === 'basic') {\n            this.handleBasicTokenRefresh();\n          } else {\n            this.handleTokenRefresh(refreshToken);\n          }\n        }\n      }, this.checkIntervalMs);\n    }\n    handleBasicTokenRefresh() {\n      const refreshSub = this.authService.basicRefreshToken().subscribe({\n        next: () => {\n          console.info('Basic token refreshed successfully');\n          // Don't redirect on successful refresh, let user stay on current page\n        },\n        error: err => {\n          console.error('Basic token refresh failed:', err);\n          // Only clear tokens and redirect if refresh fails with 401/403\n          if (err.status === 401 || err.status === 403) {\n            this.tokenStorageService.clearTokens();\n            this.router.navigateByUrl('/login');\n          }\n        }\n      });\n      this.subscription.add(refreshSub);\n    }\n    handleTokenRefresh(refreshToken) {\n      const refreshSub = this.authService.refreshToken(refreshToken).subscribe({\n        next: () => {\n          console.info('Token refreshed successfully');\n        },\n        error: err => {\n          console.error('Token refresh failed:', err);\n          // Only clear tokens and redirect if refresh fails with 401/403\n          if (err.status === 401 || err.status === 403) {\n            this.tokenStorageService.clearTokens();\n            this.router.navigateByUrl('/login');\n          }\n        }\n      });\n      this.subscription.add(refreshSub);\n    }\n    stopTokenCheck() {\n      if (this.tokenCheckInterval) {\n        clearInterval(this.tokenCheckInterval);\n      }\n      this.subscription.unsubscribe();\n    }\n    static ɵfac = function AuthTokenService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AuthTokenService)(i0.ɵɵinject(i1.Router), i0.ɵɵinject(i2.AuthService), i0.ɵɵinject(i3.TokenStorageService));\n    };\n    static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthTokenService,\n      factory: AuthTokenService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return AuthTokenService;\n})();", "map": {"version": 3, "names": ["Subscription", "AuthTokenService", "router", "authService", "tokenStorageService", "tokenCheckInterval", "checkIntervalMs", "subscription", "constructor", "handleAuthCodeAndToken", "authCode", "URLSearchParams", "window", "location", "search", "get", "accessToken", "getAccessToken", "refreshToken", "getRefreshToken", "loginType", "getLoginType", "handleBasicLoginFlow", "handleSSOFlow", "handleBasicTokenRefresh", "navigateByUrl", "startTokenCheck", "setInterval", "currentUrl", "url", "includes", "console", "info", "clearTokens", "handleTokenRefresh", "refreshSub", "basicRefreshToken", "subscribe", "next", "error", "err", "status", "add", "stopTokenCheck", "clearInterval", "unsubscribe", "i0", "ɵɵinject", "i1", "Router", "i2", "AuthService", "i3", "TokenStorageService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\auth\\services\\auth-token.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { Subscription } from 'rxjs';\r\nimport { AuthService } from './auth.service';\r\nimport { HttpErrorResponse } from '@angular/common/http';\r\nimport { TokenStorageService } from './token-storage.service';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class AuthTokenService {\r\n  private tokenCheckInterval: any;\r\n  private checkIntervalMs = 30000; // Check every 30 seconds\r\n  private subscription = new Subscription();\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private authService: AuthService,\r\n    private tokenStorageService: TokenStorageService,\r\n  ) {}\r\n\r\n  public handleAuthCodeAndToken(): void {\r\n    const authCode = new URLSearchParams(window.location.search).get('code');\r\n    const accessToken = this.tokenStorageService.getAccessToken();\r\n    const refreshToken = this.tokenStorageService.getRefreshToken();\r\n    const loginType = this.tokenStorageService.getLoginType();\r\n\r\n    if (loginType === 'basic') {\r\n      this.handleBasicLoginFlow(accessToken, refreshToken);\r\n    } else {\r\n      this.handleSSOFlow(accessToken, refreshToken, authCode);\r\n    }\r\n  }\r\n\r\n  private handleBasicLoginFlow(\r\n    accessToken: string | null,\r\n    refreshToken: string | null,\r\n  ): void {\r\n    if (!accessToken && refreshToken) {\r\n      this.handleBasicTokenRefresh();\r\n    } else if (!accessToken && !refreshToken) {\r\n      this.router.navigateByUrl('/login');\r\n    }\r\n  }\r\n\r\n  private handleSSOFlow(\r\n    accessToken: string | null,\r\n    refreshToken: string | null,\r\n    authCode: string | null,\r\n  ): void {\r\n    if (!accessToken && refreshToken) {\r\n      this.router.navigateByUrl(`/callback?refresh_token=${refreshToken}`);\r\n    } else if (!accessToken && !refreshToken && authCode) {\r\n      this.router.navigateByUrl(`/callback?code=${authCode}`);\r\n    }\r\n  }\r\n\r\n  public startTokenCheck(): void {\r\n    this.tokenCheckInterval = setInterval(() => {\r\n      const accessToken = this.tokenStorageService.getAccessToken();\r\n      const refreshToken = this.tokenStorageService.getRefreshToken();\r\n      const loginType = this.tokenStorageService.getLoginType();\r\n\r\n      // Only redirect to login if we have no tokens at all and we're not already on login/callback/marketplace pages\r\n      if (!accessToken && !refreshToken) {\r\n        const currentUrl = this.router.url;\r\n        if (!currentUrl.includes('/login') && !currentUrl.includes('/callback') && !currentUrl.includes('/marketplace')) {\r\n          console.info('No tokens found, redirecting to login');\r\n          this.tokenStorageService.clearTokens();\r\n          this.router.navigateByUrl('/login');\r\n        }\r\n      } else if (!accessToken && refreshToken) {\r\n        // Try to refresh the token\r\n        console.info('Access token missing, attempting refresh');\r\n        if (loginType === 'basic') {\r\n          this.handleBasicTokenRefresh();\r\n        } else {\r\n          this.handleTokenRefresh(refreshToken);\r\n        }\r\n      }\r\n    }, this.checkIntervalMs);\r\n  }\r\n\r\n  private handleBasicTokenRefresh(): void {\r\n    const refreshSub = this.authService.basicRefreshToken().subscribe({\r\n      next: () => {\r\n        console.info('Basic token refreshed successfully');\r\n        // Don't redirect on successful refresh, let user stay on current page\r\n      },\r\n      error: (err: HttpErrorResponse) => {\r\n        console.error('Basic token refresh failed:', err);\r\n        // Only clear tokens and redirect if refresh fails with 401/403\r\n        if (err.status === 401 || err.status === 403) {\r\n          this.tokenStorageService.clearTokens();\r\n          this.router.navigateByUrl('/login');\r\n        }\r\n      },\r\n    });\r\n    this.subscription.add(refreshSub);\r\n  }\r\n\r\n  private handleTokenRefresh(refreshToken: string): void {\r\n    const refreshSub = this.authService.refreshToken(refreshToken).subscribe({\r\n      next: () => {\r\n        console.info('Token refreshed successfully');\r\n      },\r\n      error: (err: HttpErrorResponse) => {\r\n        console.error('Token refresh failed:', err);\r\n        // Only clear tokens and redirect if refresh fails with 401/403\r\n        if (err.status === 401 || err.status === 403) {\r\n          this.tokenStorageService.clearTokens();\r\n          this.router.navigateByUrl('/login');\r\n        }\r\n      },\r\n    });\r\n    this.subscription.add(refreshSub);\r\n  }\r\n\r\n  public stopTokenCheck(): void {\r\n    if (this.tokenCheckInterval) {\r\n      clearInterval(this.tokenCheckInterval);\r\n    }\r\n    this.subscription.unsubscribe();\r\n  }\r\n} "], "mappings": "AAEA,SAASA,YAAY,QAAQ,MAAM;;;;;AAQnC,WAAaC,gBAAgB;EAAvB,MAAOA,gBAAgB;IAMjBC,MAAA;IACAC,WAAA;IACAC,mBAAA;IAPFC,kBAAkB;IAClBC,eAAe,GAAG,KAAK,CAAC,CAAC;IACzBC,YAAY,GAAG,IAAIP,YAAY,EAAE;IAEzCQ,YACUN,MAAc,EACdC,WAAwB,EACxBC,mBAAwC;MAFxC,KAAAF,MAAM,GAANA,MAAM;MACN,KAAAC,WAAW,GAAXA,WAAW;MACX,KAAAC,mBAAmB,GAAnBA,mBAAmB;IAC1B;IAEIK,sBAAsBA,CAAA;MAC3B,MAAMC,QAAQ,GAAG,IAAIC,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAACC,GAAG,CAAC,MAAM,CAAC;MACxE,MAAMC,WAAW,GAAG,IAAI,CAACZ,mBAAmB,CAACa,cAAc,EAAE;MAC7D,MAAMC,YAAY,GAAG,IAAI,CAACd,mBAAmB,CAACe,eAAe,EAAE;MAC/D,MAAMC,SAAS,GAAG,IAAI,CAAChB,mBAAmB,CAACiB,YAAY,EAAE;MAEzD,IAAID,SAAS,KAAK,OAAO,EAAE;QACzB,IAAI,CAACE,oBAAoB,CAACN,WAAW,EAAEE,YAAY,CAAC;MACtD,CAAC,MAAM;QACL,IAAI,CAACK,aAAa,CAACP,WAAW,EAAEE,YAAY,EAAER,QAAQ,CAAC;MACzD;IACF;IAEQY,oBAAoBA,CAC1BN,WAA0B,EAC1BE,YAA2B;MAE3B,IAAI,CAACF,WAAW,IAAIE,YAAY,EAAE;QAChC,IAAI,CAACM,uBAAuB,EAAE;MAChC,CAAC,MAAM,IAAI,CAACR,WAAW,IAAI,CAACE,YAAY,EAAE;QACxC,IAAI,CAAChB,MAAM,CAACuB,aAAa,CAAC,QAAQ,CAAC;MACrC;IACF;IAEQF,aAAaA,CACnBP,WAA0B,EAC1BE,YAA2B,EAC3BR,QAAuB;MAEvB,IAAI,CAACM,WAAW,IAAIE,YAAY,EAAE;QAChC,IAAI,CAAChB,MAAM,CAACuB,aAAa,CAAC,2BAA2BP,YAAY,EAAE,CAAC;MACtE,CAAC,MAAM,IAAI,CAACF,WAAW,IAAI,CAACE,YAAY,IAAIR,QAAQ,EAAE;QACpD,IAAI,CAACR,MAAM,CAACuB,aAAa,CAAC,kBAAkBf,QAAQ,EAAE,CAAC;MACzD;IACF;IAEOgB,eAAeA,CAAA;MACpB,IAAI,CAACrB,kBAAkB,GAAGsB,WAAW,CAAC,MAAK;QACzC,MAAMX,WAAW,GAAG,IAAI,CAACZ,mBAAmB,CAACa,cAAc,EAAE;QAC7D,MAAMC,YAAY,GAAG,IAAI,CAACd,mBAAmB,CAACe,eAAe,EAAE;QAC/D,MAAMC,SAAS,GAAG,IAAI,CAAChB,mBAAmB,CAACiB,YAAY,EAAE;QAEzD;QACA,IAAI,CAACL,WAAW,IAAI,CAACE,YAAY,EAAE;UACjC,MAAMU,UAAU,GAAG,IAAI,CAAC1B,MAAM,CAAC2B,GAAG;UAClC,IAAI,CAACD,UAAU,CAACE,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAACF,UAAU,CAACE,QAAQ,CAAC,WAAW,CAAC,IAAI,CAACF,UAAU,CAACE,QAAQ,CAAC,cAAc,CAAC,EAAE;YAC/GC,OAAO,CAACC,IAAI,CAAC,uCAAuC,CAAC;YACrD,IAAI,CAAC5B,mBAAmB,CAAC6B,WAAW,EAAE;YACtC,IAAI,CAAC/B,MAAM,CAACuB,aAAa,CAAC,QAAQ,CAAC;UACrC;QACF,CAAC,MAAM,IAAI,CAACT,WAAW,IAAIE,YAAY,EAAE;UACvC;UACAa,OAAO,CAACC,IAAI,CAAC,0CAA0C,CAAC;UACxD,IAAIZ,SAAS,KAAK,OAAO,EAAE;YACzB,IAAI,CAACI,uBAAuB,EAAE;UAChC,CAAC,MAAM;YACL,IAAI,CAACU,kBAAkB,CAAChB,YAAY,CAAC;UACvC;QACF;MACF,CAAC,EAAE,IAAI,CAACZ,eAAe,CAAC;IAC1B;IAEQkB,uBAAuBA,CAAA;MAC7B,MAAMW,UAAU,GAAG,IAAI,CAAChC,WAAW,CAACiC,iBAAiB,EAAE,CAACC,SAAS,CAAC;QAChEC,IAAI,EAAEA,CAAA,KAAK;UACTP,OAAO,CAACC,IAAI,CAAC,oCAAoC,CAAC;UAClD;QACF,CAAC;QACDO,KAAK,EAAGC,GAAsB,IAAI;UAChCT,OAAO,CAACQ,KAAK,CAAC,6BAA6B,EAAEC,GAAG,CAAC;UACjD;UACA,IAAIA,GAAG,CAACC,MAAM,KAAK,GAAG,IAAID,GAAG,CAACC,MAAM,KAAK,GAAG,EAAE;YAC5C,IAAI,CAACrC,mBAAmB,CAAC6B,WAAW,EAAE;YACtC,IAAI,CAAC/B,MAAM,CAACuB,aAAa,CAAC,QAAQ,CAAC;UACrC;QACF;OACD,CAAC;MACF,IAAI,CAAClB,YAAY,CAACmC,GAAG,CAACP,UAAU,CAAC;IACnC;IAEQD,kBAAkBA,CAAChB,YAAoB;MAC7C,MAAMiB,UAAU,GAAG,IAAI,CAAChC,WAAW,CAACe,YAAY,CAACA,YAAY,CAAC,CAACmB,SAAS,CAAC;QACvEC,IAAI,EAAEA,CAAA,KAAK;UACTP,OAAO,CAACC,IAAI,CAAC,8BAA8B,CAAC;QAC9C,CAAC;QACDO,KAAK,EAAGC,GAAsB,IAAI;UAChCT,OAAO,CAACQ,KAAK,CAAC,uBAAuB,EAAEC,GAAG,CAAC;UAC3C;UACA,IAAIA,GAAG,CAACC,MAAM,KAAK,GAAG,IAAID,GAAG,CAACC,MAAM,KAAK,GAAG,EAAE;YAC5C,IAAI,CAACrC,mBAAmB,CAAC6B,WAAW,EAAE;YACtC,IAAI,CAAC/B,MAAM,CAACuB,aAAa,CAAC,QAAQ,CAAC;UACrC;QACF;OACD,CAAC;MACF,IAAI,CAAClB,YAAY,CAACmC,GAAG,CAACP,UAAU,CAAC;IACnC;IAEOQ,cAAcA,CAAA;MACnB,IAAI,IAAI,CAACtC,kBAAkB,EAAE;QAC3BuC,aAAa,CAAC,IAAI,CAACvC,kBAAkB,CAAC;MACxC;MACA,IAAI,CAACE,YAAY,CAACsC,WAAW,EAAE;IACjC;;uCAjHW5C,gBAAgB,EAAA6C,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,mBAAA;IAAA;;aAAhBpD,gBAAgB;MAAAqD,OAAA,EAAhBrD,gBAAgB,CAAAsD,IAAA;MAAAC,UAAA,EAFf;IAAM;;SAEPvD,gBAAgB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}