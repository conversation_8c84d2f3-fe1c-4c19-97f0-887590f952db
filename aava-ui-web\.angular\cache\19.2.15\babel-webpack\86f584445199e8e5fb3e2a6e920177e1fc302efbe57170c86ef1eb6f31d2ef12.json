{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { equalsIfDefined, itemsEquals } from '../../base/common/equals.js';\nimport { Disposable, DisposableStore, toDisposable } from '../../base/common/lifecycle.js';\nimport { autorun, autorunOpts, autorunWithStoreHandleChanges, derived, derivedOpts, observableFromEvent, observableSignal, observableValue, observableValueOpts } from '../../base/common/observable.js';\nimport { TransactionImpl } from '../../base/common/observableInternal/base.js';\nimport { derivedWithSetter } from '../../base/common/observableInternal/derived.js';\nimport { Selection } from '../common/core/selection.js';\n/**\n * Returns a facade for the code editor that provides observables for various states/events.\n*/\nexport function observableCodeEditor(editor) {\n  return ObservableCodeEditor.get(editor);\n}\nexport class ObservableCodeEditor extends Disposable {\n  static {\n    this._map = new Map();\n  }\n  /**\n   * Make sure that editor is not disposed yet!\n  */\n  static get(editor) {\n    let result = ObservableCodeEditor._map.get(editor);\n    if (!result) {\n      result = new ObservableCodeEditor(editor);\n      ObservableCodeEditor._map.set(editor, result);\n      const d = editor.onDidDispose(() => {\n        const item = ObservableCodeEditor._map.get(editor);\n        if (item) {\n          ObservableCodeEditor._map.delete(editor);\n          item.dispose();\n          d.dispose();\n        }\n      });\n    }\n    return result;\n  }\n  _beginUpdate() {\n    this._updateCounter++;\n    if (this._updateCounter === 1) {\n      this._currentTransaction = new TransactionImpl(() => {\n        /** @description Update editor state */\n      });\n    }\n  }\n  _endUpdate() {\n    this._updateCounter--;\n    if (this._updateCounter === 0) {\n      const t = this._currentTransaction;\n      this._currentTransaction = undefined;\n      t.finish();\n    }\n  }\n  constructor(editor) {\n    super();\n    this.editor = editor;\n    this._updateCounter = 0;\n    this._currentTransaction = undefined;\n    this._model = observableValue(this, this.editor.getModel());\n    this.model = this._model;\n    this.isReadonly = observableFromEvent(this, this.editor.onDidChangeConfiguration, () => this.editor.getOption(92 /* EditorOption.readOnly */));\n    this._versionId = observableValueOpts({\n      owner: this,\n      lazy: true\n    }, this.editor.getModel()?.getVersionId() ?? null);\n    this.versionId = this._versionId;\n    this._selections = observableValueOpts({\n      owner: this,\n      equalsFn: equalsIfDefined(itemsEquals(Selection.selectionsEqual)),\n      lazy: true\n    }, this.editor.getSelections() ?? null);\n    this.selections = this._selections;\n    this.isFocused = observableFromEvent(this, e => {\n      const d1 = this.editor.onDidFocusEditorWidget(e);\n      const d2 = this.editor.onDidBlurEditorWidget(e);\n      return {\n        dispose() {\n          d1.dispose();\n          d2.dispose();\n        }\n      };\n    }, () => this.editor.hasWidgetFocus());\n    this.value = derivedWithSetter(this, reader => {\n      this.versionId.read(reader);\n      return this.model.read(reader)?.getValue() ?? '';\n    }, (value, tx) => {\n      const model = this.model.get();\n      if (model !== null) {\n        if (value !== model.getValue()) {\n          model.setValue(value);\n        }\n      }\n    });\n    this.valueIsEmpty = derived(this, reader => {\n      this.versionId.read(reader);\n      return this.editor.getModel()?.getValueLength() === 0;\n    });\n    this.cursorSelection = derivedOpts({\n      owner: this,\n      equalsFn: equalsIfDefined(Selection.selectionsEqual)\n    }, reader => this.selections.read(reader)?.[0] ?? null);\n    this.onDidType = observableSignal(this);\n    this.scrollTop = observableFromEvent(this.editor.onDidScrollChange, () => this.editor.getScrollTop());\n    this.scrollLeft = observableFromEvent(this.editor.onDidScrollChange, () => this.editor.getScrollLeft());\n    this.layoutInfo = observableFromEvent(this.editor.onDidLayoutChange, () => this.editor.getLayoutInfo());\n    this.layoutInfoContentLeft = this.layoutInfo.map(l => l.contentLeft);\n    this.layoutInfoDecorationsLeft = this.layoutInfo.map(l => l.decorationsLeft);\n    this.contentWidth = observableFromEvent(this.editor.onDidContentSizeChange, () => this.editor.getContentWidth());\n    this._overlayWidgetCounter = 0;\n    this._register(this.editor.onBeginUpdate(() => this._beginUpdate()));\n    this._register(this.editor.onEndUpdate(() => this._endUpdate()));\n    this._register(this.editor.onDidChangeModel(() => {\n      this._beginUpdate();\n      try {\n        this._model.set(this.editor.getModel(), this._currentTransaction);\n        this._forceUpdate();\n      } finally {\n        this._endUpdate();\n      }\n    }));\n    this._register(this.editor.onDidType(e => {\n      this._beginUpdate();\n      try {\n        this._forceUpdate();\n        this.onDidType.trigger(this._currentTransaction, e);\n      } finally {\n        this._endUpdate();\n      }\n    }));\n    this._register(this.editor.onDidChangeModelContent(e => {\n      this._beginUpdate();\n      try {\n        this._versionId.set(this.editor.getModel()?.getVersionId() ?? null, this._currentTransaction, e);\n        this._forceUpdate();\n      } finally {\n        this._endUpdate();\n      }\n    }));\n    this._register(this.editor.onDidChangeCursorSelection(e => {\n      this._beginUpdate();\n      try {\n        this._selections.set(this.editor.getSelections(), this._currentTransaction, e);\n        this._forceUpdate();\n      } finally {\n        this._endUpdate();\n      }\n    }));\n  }\n  forceUpdate(cb) {\n    this._beginUpdate();\n    try {\n      this._forceUpdate();\n      if (!cb) {\n        return undefined;\n      }\n      return cb(this._currentTransaction);\n    } finally {\n      this._endUpdate();\n    }\n  }\n  _forceUpdate() {\n    this._beginUpdate();\n    try {\n      this._model.set(this.editor.getModel(), this._currentTransaction);\n      this._versionId.set(this.editor.getModel()?.getVersionId() ?? null, this._currentTransaction, undefined);\n      this._selections.set(this.editor.getSelections(), this._currentTransaction, undefined);\n    } finally {\n      this._endUpdate();\n    }\n  }\n  getOption(id) {\n    return observableFromEvent(this, cb => this.editor.onDidChangeConfiguration(e => {\n      if (e.hasChanged(id)) {\n        cb(undefined);\n      }\n    }), () => this.editor.getOption(id));\n  }\n  setDecorations(decorations) {\n    const d = new DisposableStore();\n    const decorationsCollection = this.editor.createDecorationsCollection();\n    d.add(autorunOpts({\n      owner: this,\n      debugName: () => `Apply decorations from ${decorations.debugName}`\n    }, reader => {\n      const d = decorations.read(reader);\n      decorationsCollection.set(d);\n    }));\n    d.add({\n      dispose: () => {\n        decorationsCollection.clear();\n      }\n    });\n    return d;\n  }\n  createOverlayWidget(widget) {\n    const overlayWidgetId = 'observableOverlayWidget' + this._overlayWidgetCounter++;\n    const w = {\n      getDomNode: () => widget.domNode,\n      getPosition: () => widget.position.get(),\n      getId: () => overlayWidgetId,\n      allowEditorOverflow: widget.allowEditorOverflow,\n      getMinContentWidthInPx: () => widget.minContentWidthInPx.get()\n    };\n    this.editor.addOverlayWidget(w);\n    const d = autorun(reader => {\n      widget.position.read(reader);\n      widget.minContentWidthInPx.read(reader);\n      this.editor.layoutOverlayWidget(w);\n    });\n    return toDisposable(() => {\n      d.dispose();\n      this.editor.removeOverlayWidget(w);\n    });\n  }\n}\nexport function reactToChange(observable, cb) {\n  return autorunWithStoreHandleChanges({\n    createEmptyChangeSummary: () => ({\n      deltas: [],\n      didChange: false\n    }),\n    handleChange: (context, changeSummary) => {\n      if (context.didChange(observable)) {\n        const e = context.change;\n        if (e !== undefined) {\n          changeSummary.deltas.push(e);\n        }\n        changeSummary.didChange = true;\n      }\n      return true;\n    }\n  }, (reader, changeSummary) => {\n    const value = observable.read(reader);\n    if (changeSummary.didChange) {\n      cb(value, changeSummary.deltas);\n    }\n  });\n}\nexport function reactToChangeWithStore(observable, cb) {\n  const store = new DisposableStore();\n  const disposable = reactToChange(observable, (value, deltas) => {\n    store.clear();\n    cb(value, deltas, store);\n  });\n  return {\n    dispose() {\n      disposable.dispose();\n      store.dispose();\n    }\n  };\n}", "map": {"version": 3, "names": ["equalsIfDefined", "itemsEquals", "Disposable", "DisposableStore", "toDisposable", "autorun", "autorunOpts", "autorunWithStoreHandleChanges", "derived", "derivedOpts", "observableFromEvent", "observableSignal", "observableValue", "observableValueOpts", "TransactionImpl", "derivedWithSetter", "Selection", "observableCodeEditor", "editor", "ObservableCodeEditor", "get", "_map", "Map", "result", "set", "d", "onDidDispose", "item", "delete", "dispose", "_beginUpdate", "_updateCounter", "_currentTransaction", "_endUpdate", "t", "undefined", "finish", "constructor", "_model", "getModel", "model", "is<PERSON><PERSON><PERSON>ly", "onDidChangeConfiguration", "getOption", "_versionId", "owner", "lazy", "getVersionId", "versionId", "_selections", "equalsFn", "selectionsEqual", "getSelections", "selections", "isFocused", "e", "d1", "onDidFocusEditorWidget", "d2", "onDidBlurEditorWidget", "hasWidgetFocus", "value", "reader", "read", "getValue", "tx", "setValue", "valueIsEmpty", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cursorSelection", "onDidType", "scrollTop", "onDidScrollChange", "getScrollTop", "scrollLeft", "getScrollLeft", "layoutInfo", "onDidLayoutChange", "getLayoutInfo", "layoutInfoContentLeft", "map", "l", "contentLeft", "layoutInfoDecorationsLeft", "decorationsLeft", "contentWidth", "onDidContentSizeChange", "getContentWidth", "_overlayWidgetCounter", "_register", "onBeginUpdate", "onEndUpdate", "onDidChangeModel", "_forceUpdate", "trigger", "onDidChangeModelContent", "onDidChangeCursorSelection", "forceUpdate", "cb", "id", "has<PERSON><PERSON>ed", "setDecorations", "decorations", "decorationsCollection", "createDecorationsCollection", "add", "debugName", "clear", "createOverlayWidget", "widget", "overlayWidgetId", "w", "getDomNode", "domNode", "getPosition", "position", "getId", "allowEditorOverflow", "getMinContentWidthInPx", "minContentWidthInPx", "addOverlayWidget", "layoutOverlayWidget", "removeOverlayWidget", "reactToChange", "observable", "createEmptyChangeSummary", "deltas", "<PERSON><PERSON><PERSON><PERSON>", "handleChange", "context", "changeSummary", "change", "push", "reactToChangeWithStore", "store", "disposable"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/browser/observableCodeEditor.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { equalsIfDefined, itemsEquals } from '../../base/common/equals.js';\nimport { Disposable, DisposableStore, toDisposable } from '../../base/common/lifecycle.js';\nimport { autorun, autorunOpts, autorunWithStoreHandleChanges, derived, derivedOpts, observableFromEvent, observableSignal, observableValue, observableValueOpts } from '../../base/common/observable.js';\nimport { TransactionImpl } from '../../base/common/observableInternal/base.js';\nimport { derivedWithSetter } from '../../base/common/observableInternal/derived.js';\nimport { Selection } from '../common/core/selection.js';\n/**\n * Returns a facade for the code editor that provides observables for various states/events.\n*/\nexport function observableCodeEditor(editor) {\n    return ObservableCodeEditor.get(editor);\n}\nexport class ObservableCodeEditor extends Disposable {\n    static { this._map = new Map(); }\n    /**\n     * Make sure that editor is not disposed yet!\n    */\n    static get(editor) {\n        let result = ObservableCodeEditor._map.get(editor);\n        if (!result) {\n            result = new ObservableCodeEditor(editor);\n            ObservableCodeEditor._map.set(editor, result);\n            const d = editor.onDidDispose(() => {\n                const item = ObservableCodeEditor._map.get(editor);\n                if (item) {\n                    ObservableCodeEditor._map.delete(editor);\n                    item.dispose();\n                    d.dispose();\n                }\n            });\n        }\n        return result;\n    }\n    _beginUpdate() {\n        this._updateCounter++;\n        if (this._updateCounter === 1) {\n            this._currentTransaction = new TransactionImpl(() => {\n                /** @description Update editor state */\n            });\n        }\n    }\n    _endUpdate() {\n        this._updateCounter--;\n        if (this._updateCounter === 0) {\n            const t = this._currentTransaction;\n            this._currentTransaction = undefined;\n            t.finish();\n        }\n    }\n    constructor(editor) {\n        super();\n        this.editor = editor;\n        this._updateCounter = 0;\n        this._currentTransaction = undefined;\n        this._model = observableValue(this, this.editor.getModel());\n        this.model = this._model;\n        this.isReadonly = observableFromEvent(this, this.editor.onDidChangeConfiguration, () => this.editor.getOption(92 /* EditorOption.readOnly */));\n        this._versionId = observableValueOpts({ owner: this, lazy: true }, this.editor.getModel()?.getVersionId() ?? null);\n        this.versionId = this._versionId;\n        this._selections = observableValueOpts({ owner: this, equalsFn: equalsIfDefined(itemsEquals(Selection.selectionsEqual)), lazy: true }, this.editor.getSelections() ?? null);\n        this.selections = this._selections;\n        this.isFocused = observableFromEvent(this, e => {\n            const d1 = this.editor.onDidFocusEditorWidget(e);\n            const d2 = this.editor.onDidBlurEditorWidget(e);\n            return {\n                dispose() {\n                    d1.dispose();\n                    d2.dispose();\n                }\n            };\n        }, () => this.editor.hasWidgetFocus());\n        this.value = derivedWithSetter(this, reader => { this.versionId.read(reader); return this.model.read(reader)?.getValue() ?? ''; }, (value, tx) => {\n            const model = this.model.get();\n            if (model !== null) {\n                if (value !== model.getValue()) {\n                    model.setValue(value);\n                }\n            }\n        });\n        this.valueIsEmpty = derived(this, reader => { this.versionId.read(reader); return this.editor.getModel()?.getValueLength() === 0; });\n        this.cursorSelection = derivedOpts({ owner: this, equalsFn: equalsIfDefined(Selection.selectionsEqual) }, reader => this.selections.read(reader)?.[0] ?? null);\n        this.onDidType = observableSignal(this);\n        this.scrollTop = observableFromEvent(this.editor.onDidScrollChange, () => this.editor.getScrollTop());\n        this.scrollLeft = observableFromEvent(this.editor.onDidScrollChange, () => this.editor.getScrollLeft());\n        this.layoutInfo = observableFromEvent(this.editor.onDidLayoutChange, () => this.editor.getLayoutInfo());\n        this.layoutInfoContentLeft = this.layoutInfo.map(l => l.contentLeft);\n        this.layoutInfoDecorationsLeft = this.layoutInfo.map(l => l.decorationsLeft);\n        this.contentWidth = observableFromEvent(this.editor.onDidContentSizeChange, () => this.editor.getContentWidth());\n        this._overlayWidgetCounter = 0;\n        this._register(this.editor.onBeginUpdate(() => this._beginUpdate()));\n        this._register(this.editor.onEndUpdate(() => this._endUpdate()));\n        this._register(this.editor.onDidChangeModel(() => {\n            this._beginUpdate();\n            try {\n                this._model.set(this.editor.getModel(), this._currentTransaction);\n                this._forceUpdate();\n            }\n            finally {\n                this._endUpdate();\n            }\n        }));\n        this._register(this.editor.onDidType((e) => {\n            this._beginUpdate();\n            try {\n                this._forceUpdate();\n                this.onDidType.trigger(this._currentTransaction, e);\n            }\n            finally {\n                this._endUpdate();\n            }\n        }));\n        this._register(this.editor.onDidChangeModelContent(e => {\n            this._beginUpdate();\n            try {\n                this._versionId.set(this.editor.getModel()?.getVersionId() ?? null, this._currentTransaction, e);\n                this._forceUpdate();\n            }\n            finally {\n                this._endUpdate();\n            }\n        }));\n        this._register(this.editor.onDidChangeCursorSelection(e => {\n            this._beginUpdate();\n            try {\n                this._selections.set(this.editor.getSelections(), this._currentTransaction, e);\n                this._forceUpdate();\n            }\n            finally {\n                this._endUpdate();\n            }\n        }));\n    }\n    forceUpdate(cb) {\n        this._beginUpdate();\n        try {\n            this._forceUpdate();\n            if (!cb) {\n                return undefined;\n            }\n            return cb(this._currentTransaction);\n        }\n        finally {\n            this._endUpdate();\n        }\n    }\n    _forceUpdate() {\n        this._beginUpdate();\n        try {\n            this._model.set(this.editor.getModel(), this._currentTransaction);\n            this._versionId.set(this.editor.getModel()?.getVersionId() ?? null, this._currentTransaction, undefined);\n            this._selections.set(this.editor.getSelections(), this._currentTransaction, undefined);\n        }\n        finally {\n            this._endUpdate();\n        }\n    }\n    getOption(id) {\n        return observableFromEvent(this, cb => this.editor.onDidChangeConfiguration(e => {\n            if (e.hasChanged(id)) {\n                cb(undefined);\n            }\n        }), () => this.editor.getOption(id));\n    }\n    setDecorations(decorations) {\n        const d = new DisposableStore();\n        const decorationsCollection = this.editor.createDecorationsCollection();\n        d.add(autorunOpts({ owner: this, debugName: () => `Apply decorations from ${decorations.debugName}` }, reader => {\n            const d = decorations.read(reader);\n            decorationsCollection.set(d);\n        }));\n        d.add({\n            dispose: () => {\n                decorationsCollection.clear();\n            }\n        });\n        return d;\n    }\n    createOverlayWidget(widget) {\n        const overlayWidgetId = 'observableOverlayWidget' + (this._overlayWidgetCounter++);\n        const w = {\n            getDomNode: () => widget.domNode,\n            getPosition: () => widget.position.get(),\n            getId: () => overlayWidgetId,\n            allowEditorOverflow: widget.allowEditorOverflow,\n            getMinContentWidthInPx: () => widget.minContentWidthInPx.get(),\n        };\n        this.editor.addOverlayWidget(w);\n        const d = autorun(reader => {\n            widget.position.read(reader);\n            widget.minContentWidthInPx.read(reader);\n            this.editor.layoutOverlayWidget(w);\n        });\n        return toDisposable(() => {\n            d.dispose();\n            this.editor.removeOverlayWidget(w);\n        });\n    }\n}\nexport function reactToChange(observable, cb) {\n    return autorunWithStoreHandleChanges({\n        createEmptyChangeSummary: () => ({ deltas: [], didChange: false }),\n        handleChange: (context, changeSummary) => {\n            if (context.didChange(observable)) {\n                const e = context.change;\n                if (e !== undefined) {\n                    changeSummary.deltas.push(e);\n                }\n                changeSummary.didChange = true;\n            }\n            return true;\n        },\n    }, (reader, changeSummary) => {\n        const value = observable.read(reader);\n        if (changeSummary.didChange) {\n            cb(value, changeSummary.deltas);\n        }\n    });\n}\nexport function reactToChangeWithStore(observable, cb) {\n    const store = new DisposableStore();\n    const disposable = reactToChange(observable, (value, deltas) => {\n        store.clear();\n        cb(value, deltas, store);\n    });\n    return {\n        dispose() {\n            disposable.dispose();\n            store.dispose();\n        }\n    };\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,eAAe,EAAEC,WAAW,QAAQ,6BAA6B;AAC1E,SAASC,UAAU,EAAEC,eAAe,EAAEC,YAAY,QAAQ,gCAAgC;AAC1F,SAASC,OAAO,EAAEC,WAAW,EAAEC,6BAA6B,EAAEC,OAAO,EAAEC,WAAW,EAAEC,mBAAmB,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,mBAAmB,QAAQ,iCAAiC;AACxM,SAASC,eAAe,QAAQ,8CAA8C;AAC9E,SAASC,iBAAiB,QAAQ,iDAAiD;AACnF,SAASC,SAAS,QAAQ,6BAA6B;AACvD;AACA;AACA;AACA,OAAO,SAASC,oBAAoBA,CAACC,MAAM,EAAE;EACzC,OAAOC,oBAAoB,CAACC,GAAG,CAACF,MAAM,CAAC;AAC3C;AACA,OAAO,MAAMC,oBAAoB,SAASjB,UAAU,CAAC;EACjD;IAAS,IAAI,CAACmB,IAAI,GAAG,IAAIC,GAAG,CAAC,CAAC;EAAE;EAChC;AACJ;AACA;EACI,OAAOF,GAAGA,CAACF,MAAM,EAAE;IACf,IAAIK,MAAM,GAAGJ,oBAAoB,CAACE,IAAI,CAACD,GAAG,CAACF,MAAM,CAAC;IAClD,IAAI,CAACK,MAAM,EAAE;MACTA,MAAM,GAAG,IAAIJ,oBAAoB,CAACD,MAAM,CAAC;MACzCC,oBAAoB,CAACE,IAAI,CAACG,GAAG,CAACN,MAAM,EAAEK,MAAM,CAAC;MAC7C,MAAME,CAAC,GAAGP,MAAM,CAACQ,YAAY,CAAC,MAAM;QAChC,MAAMC,IAAI,GAAGR,oBAAoB,CAACE,IAAI,CAACD,GAAG,CAACF,MAAM,CAAC;QAClD,IAAIS,IAAI,EAAE;UACNR,oBAAoB,CAACE,IAAI,CAACO,MAAM,CAACV,MAAM,CAAC;UACxCS,IAAI,CAACE,OAAO,CAAC,CAAC;UACdJ,CAAC,CAACI,OAAO,CAAC,CAAC;QACf;MACJ,CAAC,CAAC;IACN;IACA,OAAON,MAAM;EACjB;EACAO,YAAYA,CAAA,EAAG;IACX,IAAI,CAACC,cAAc,EAAE;IACrB,IAAI,IAAI,CAACA,cAAc,KAAK,CAAC,EAAE;MAC3B,IAAI,CAACC,mBAAmB,GAAG,IAAIlB,eAAe,CAAC,MAAM;QACjD;MAAA,CACH,CAAC;IACN;EACJ;EACAmB,UAAUA,CAAA,EAAG;IACT,IAAI,CAACF,cAAc,EAAE;IACrB,IAAI,IAAI,CAACA,cAAc,KAAK,CAAC,EAAE;MAC3B,MAAMG,CAAC,GAAG,IAAI,CAACF,mBAAmB;MAClC,IAAI,CAACA,mBAAmB,GAAGG,SAAS;MACpCD,CAAC,CAACE,MAAM,CAAC,CAAC;IACd;EACJ;EACAC,WAAWA,CAACnB,MAAM,EAAE;IAChB,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACa,cAAc,GAAG,CAAC;IACvB,IAAI,CAACC,mBAAmB,GAAGG,SAAS;IACpC,IAAI,CAACG,MAAM,GAAG1B,eAAe,CAAC,IAAI,EAAE,IAAI,CAACM,MAAM,CAACqB,QAAQ,CAAC,CAAC,CAAC;IAC3D,IAAI,CAACC,KAAK,GAAG,IAAI,CAACF,MAAM;IACxB,IAAI,CAACG,UAAU,GAAG/B,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAACQ,MAAM,CAACwB,wBAAwB,EAAE,MAAM,IAAI,CAACxB,MAAM,CAACyB,SAAS,CAAC,EAAE,CAAC,2BAA2B,CAAC,CAAC;IAC9I,IAAI,CAACC,UAAU,GAAG/B,mBAAmB,CAAC;MAAEgC,KAAK,EAAE,IAAI;MAAEC,IAAI,EAAE;IAAK,CAAC,EAAE,IAAI,CAAC5B,MAAM,CAACqB,QAAQ,CAAC,CAAC,EAAEQ,YAAY,CAAC,CAAC,IAAI,IAAI,CAAC;IAClH,IAAI,CAACC,SAAS,GAAG,IAAI,CAACJ,UAAU;IAChC,IAAI,CAACK,WAAW,GAAGpC,mBAAmB,CAAC;MAAEgC,KAAK,EAAE,IAAI;MAAEK,QAAQ,EAAElD,eAAe,CAACC,WAAW,CAACe,SAAS,CAACmC,eAAe,CAAC,CAAC;MAAEL,IAAI,EAAE;IAAK,CAAC,EAAE,IAAI,CAAC5B,MAAM,CAACkC,aAAa,CAAC,CAAC,IAAI,IAAI,CAAC;IAC3K,IAAI,CAACC,UAAU,GAAG,IAAI,CAACJ,WAAW;IAClC,IAAI,CAACK,SAAS,GAAG5C,mBAAmB,CAAC,IAAI,EAAE6C,CAAC,IAAI;MAC5C,MAAMC,EAAE,GAAG,IAAI,CAACtC,MAAM,CAACuC,sBAAsB,CAACF,CAAC,CAAC;MAChD,MAAMG,EAAE,GAAG,IAAI,CAACxC,MAAM,CAACyC,qBAAqB,CAACJ,CAAC,CAAC;MAC/C,OAAO;QACH1B,OAAOA,CAAA,EAAG;UACN2B,EAAE,CAAC3B,OAAO,CAAC,CAAC;UACZ6B,EAAE,CAAC7B,OAAO,CAAC,CAAC;QAChB;MACJ,CAAC;IACL,CAAC,EAAE,MAAM,IAAI,CAACX,MAAM,CAAC0C,cAAc,CAAC,CAAC,CAAC;IACtC,IAAI,CAACC,KAAK,GAAG9C,iBAAiB,CAAC,IAAI,EAAE+C,MAAM,IAAI;MAAE,IAAI,CAACd,SAAS,CAACe,IAAI,CAACD,MAAM,CAAC;MAAE,OAAO,IAAI,CAACtB,KAAK,CAACuB,IAAI,CAACD,MAAM,CAAC,EAAEE,QAAQ,CAAC,CAAC,IAAI,EAAE;IAAE,CAAC,EAAE,CAACH,KAAK,EAAEI,EAAE,KAAK;MAC9I,MAAMzB,KAAK,GAAG,IAAI,CAACA,KAAK,CAACpB,GAAG,CAAC,CAAC;MAC9B,IAAIoB,KAAK,KAAK,IAAI,EAAE;QAChB,IAAIqB,KAAK,KAAKrB,KAAK,CAACwB,QAAQ,CAAC,CAAC,EAAE;UAC5BxB,KAAK,CAAC0B,QAAQ,CAACL,KAAK,CAAC;QACzB;MACJ;IACJ,CAAC,CAAC;IACF,IAAI,CAACM,YAAY,GAAG3D,OAAO,CAAC,IAAI,EAAEsD,MAAM,IAAI;MAAE,IAAI,CAACd,SAAS,CAACe,IAAI,CAACD,MAAM,CAAC;MAAE,OAAO,IAAI,CAAC5C,MAAM,CAACqB,QAAQ,CAAC,CAAC,EAAE6B,cAAc,CAAC,CAAC,KAAK,CAAC;IAAE,CAAC,CAAC;IACpI,IAAI,CAACC,eAAe,GAAG5D,WAAW,CAAC;MAAEoC,KAAK,EAAE,IAAI;MAAEK,QAAQ,EAAElD,eAAe,CAACgB,SAAS,CAACmC,eAAe;IAAE,CAAC,EAAEW,MAAM,IAAI,IAAI,CAACT,UAAU,CAACU,IAAI,CAACD,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC;IAC9J,IAAI,CAACQ,SAAS,GAAG3D,gBAAgB,CAAC,IAAI,CAAC;IACvC,IAAI,CAAC4D,SAAS,GAAG7D,mBAAmB,CAAC,IAAI,CAACQ,MAAM,CAACsD,iBAAiB,EAAE,MAAM,IAAI,CAACtD,MAAM,CAACuD,YAAY,CAAC,CAAC,CAAC;IACrG,IAAI,CAACC,UAAU,GAAGhE,mBAAmB,CAAC,IAAI,CAACQ,MAAM,CAACsD,iBAAiB,EAAE,MAAM,IAAI,CAACtD,MAAM,CAACyD,aAAa,CAAC,CAAC,CAAC;IACvG,IAAI,CAACC,UAAU,GAAGlE,mBAAmB,CAAC,IAAI,CAACQ,MAAM,CAAC2D,iBAAiB,EAAE,MAAM,IAAI,CAAC3D,MAAM,CAAC4D,aAAa,CAAC,CAAC,CAAC;IACvG,IAAI,CAACC,qBAAqB,GAAG,IAAI,CAACH,UAAU,CAACI,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,WAAW,CAAC;IACpE,IAAI,CAACC,yBAAyB,GAAG,IAAI,CAACP,UAAU,CAACI,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACG,eAAe,CAAC;IAC5E,IAAI,CAACC,YAAY,GAAG3E,mBAAmB,CAAC,IAAI,CAACQ,MAAM,CAACoE,sBAAsB,EAAE,MAAM,IAAI,CAACpE,MAAM,CAACqE,eAAe,CAAC,CAAC,CAAC;IAChH,IAAI,CAACC,qBAAqB,GAAG,CAAC;IAC9B,IAAI,CAACC,SAAS,CAAC,IAAI,CAACvE,MAAM,CAACwE,aAAa,CAAC,MAAM,IAAI,CAAC5D,YAAY,CAAC,CAAC,CAAC,CAAC;IACpE,IAAI,CAAC2D,SAAS,CAAC,IAAI,CAACvE,MAAM,CAACyE,WAAW,CAAC,MAAM,IAAI,CAAC1D,UAAU,CAAC,CAAC,CAAC,CAAC;IAChE,IAAI,CAACwD,SAAS,CAAC,IAAI,CAACvE,MAAM,CAAC0E,gBAAgB,CAAC,MAAM;MAC9C,IAAI,CAAC9D,YAAY,CAAC,CAAC;MACnB,IAAI;QACA,IAAI,CAACQ,MAAM,CAACd,GAAG,CAAC,IAAI,CAACN,MAAM,CAACqB,QAAQ,CAAC,CAAC,EAAE,IAAI,CAACP,mBAAmB,CAAC;QACjE,IAAI,CAAC6D,YAAY,CAAC,CAAC;MACvB,CAAC,SACO;QACJ,IAAI,CAAC5D,UAAU,CAAC,CAAC;MACrB;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,CAACwD,SAAS,CAAC,IAAI,CAACvE,MAAM,CAACoD,SAAS,CAAEf,CAAC,IAAK;MACxC,IAAI,CAACzB,YAAY,CAAC,CAAC;MACnB,IAAI;QACA,IAAI,CAAC+D,YAAY,CAAC,CAAC;QACnB,IAAI,CAACvB,SAAS,CAACwB,OAAO,CAAC,IAAI,CAAC9D,mBAAmB,EAAEuB,CAAC,CAAC;MACvD,CAAC,SACO;QACJ,IAAI,CAACtB,UAAU,CAAC,CAAC;MACrB;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,CAACwD,SAAS,CAAC,IAAI,CAACvE,MAAM,CAAC6E,uBAAuB,CAACxC,CAAC,IAAI;MACpD,IAAI,CAACzB,YAAY,CAAC,CAAC;MACnB,IAAI;QACA,IAAI,CAACc,UAAU,CAACpB,GAAG,CAAC,IAAI,CAACN,MAAM,CAACqB,QAAQ,CAAC,CAAC,EAAEQ,YAAY,CAAC,CAAC,IAAI,IAAI,EAAE,IAAI,CAACf,mBAAmB,EAAEuB,CAAC,CAAC;QAChG,IAAI,CAACsC,YAAY,CAAC,CAAC;MACvB,CAAC,SACO;QACJ,IAAI,CAAC5D,UAAU,CAAC,CAAC;MACrB;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,CAACwD,SAAS,CAAC,IAAI,CAACvE,MAAM,CAAC8E,0BAA0B,CAACzC,CAAC,IAAI;MACvD,IAAI,CAACzB,YAAY,CAAC,CAAC;MACnB,IAAI;QACA,IAAI,CAACmB,WAAW,CAACzB,GAAG,CAAC,IAAI,CAACN,MAAM,CAACkC,aAAa,CAAC,CAAC,EAAE,IAAI,CAACpB,mBAAmB,EAAEuB,CAAC,CAAC;QAC9E,IAAI,CAACsC,YAAY,CAAC,CAAC;MACvB,CAAC,SACO;QACJ,IAAI,CAAC5D,UAAU,CAAC,CAAC;MACrB;IACJ,CAAC,CAAC,CAAC;EACP;EACAgE,WAAWA,CAACC,EAAE,EAAE;IACZ,IAAI,CAACpE,YAAY,CAAC,CAAC;IACnB,IAAI;MACA,IAAI,CAAC+D,YAAY,CAAC,CAAC;MACnB,IAAI,CAACK,EAAE,EAAE;QACL,OAAO/D,SAAS;MACpB;MACA,OAAO+D,EAAE,CAAC,IAAI,CAAClE,mBAAmB,CAAC;IACvC,CAAC,SACO;MACJ,IAAI,CAACC,UAAU,CAAC,CAAC;IACrB;EACJ;EACA4D,YAAYA,CAAA,EAAG;IACX,IAAI,CAAC/D,YAAY,CAAC,CAAC;IACnB,IAAI;MACA,IAAI,CAACQ,MAAM,CAACd,GAAG,CAAC,IAAI,CAACN,MAAM,CAACqB,QAAQ,CAAC,CAAC,EAAE,IAAI,CAACP,mBAAmB,CAAC;MACjE,IAAI,CAACY,UAAU,CAACpB,GAAG,CAAC,IAAI,CAACN,MAAM,CAACqB,QAAQ,CAAC,CAAC,EAAEQ,YAAY,CAAC,CAAC,IAAI,IAAI,EAAE,IAAI,CAACf,mBAAmB,EAAEG,SAAS,CAAC;MACxG,IAAI,CAACc,WAAW,CAACzB,GAAG,CAAC,IAAI,CAACN,MAAM,CAACkC,aAAa,CAAC,CAAC,EAAE,IAAI,CAACpB,mBAAmB,EAAEG,SAAS,CAAC;IAC1F,CAAC,SACO;MACJ,IAAI,CAACF,UAAU,CAAC,CAAC;IACrB;EACJ;EACAU,SAASA,CAACwD,EAAE,EAAE;IACV,OAAOzF,mBAAmB,CAAC,IAAI,EAAEwF,EAAE,IAAI,IAAI,CAAChF,MAAM,CAACwB,wBAAwB,CAACa,CAAC,IAAI;MAC7E,IAAIA,CAAC,CAAC6C,UAAU,CAACD,EAAE,CAAC,EAAE;QAClBD,EAAE,CAAC/D,SAAS,CAAC;MACjB;IACJ,CAAC,CAAC,EAAE,MAAM,IAAI,CAACjB,MAAM,CAACyB,SAAS,CAACwD,EAAE,CAAC,CAAC;EACxC;EACAE,cAAcA,CAACC,WAAW,EAAE;IACxB,MAAM7E,CAAC,GAAG,IAAItB,eAAe,CAAC,CAAC;IAC/B,MAAMoG,qBAAqB,GAAG,IAAI,CAACrF,MAAM,CAACsF,2BAA2B,CAAC,CAAC;IACvE/E,CAAC,CAACgF,GAAG,CAACnG,WAAW,CAAC;MAAEuC,KAAK,EAAE,IAAI;MAAE6D,SAAS,EAAEA,CAAA,KAAM,0BAA0BJ,WAAW,CAACI,SAAS;IAAG,CAAC,EAAE5C,MAAM,IAAI;MAC7G,MAAMrC,CAAC,GAAG6E,WAAW,CAACvC,IAAI,CAACD,MAAM,CAAC;MAClCyC,qBAAqB,CAAC/E,GAAG,CAACC,CAAC,CAAC;IAChC,CAAC,CAAC,CAAC;IACHA,CAAC,CAACgF,GAAG,CAAC;MACF5E,OAAO,EAAEA,CAAA,KAAM;QACX0E,qBAAqB,CAACI,KAAK,CAAC,CAAC;MACjC;IACJ,CAAC,CAAC;IACF,OAAOlF,CAAC;EACZ;EACAmF,mBAAmBA,CAACC,MAAM,EAAE;IACxB,MAAMC,eAAe,GAAG,yBAAyB,GAAI,IAAI,CAACtB,qBAAqB,EAAG;IAClF,MAAMuB,CAAC,GAAG;MACNC,UAAU,EAAEA,CAAA,KAAMH,MAAM,CAACI,OAAO;MAChCC,WAAW,EAAEA,CAAA,KAAML,MAAM,CAACM,QAAQ,CAAC/F,GAAG,CAAC,CAAC;MACxCgG,KAAK,EAAEA,CAAA,KAAMN,eAAe;MAC5BO,mBAAmB,EAAER,MAAM,CAACQ,mBAAmB;MAC/CC,sBAAsB,EAAEA,CAAA,KAAMT,MAAM,CAACU,mBAAmB,CAACnG,GAAG,CAAC;IACjE,CAAC;IACD,IAAI,CAACF,MAAM,CAACsG,gBAAgB,CAACT,CAAC,CAAC;IAC/B,MAAMtF,CAAC,GAAGpB,OAAO,CAACyD,MAAM,IAAI;MACxB+C,MAAM,CAACM,QAAQ,CAACpD,IAAI,CAACD,MAAM,CAAC;MAC5B+C,MAAM,CAACU,mBAAmB,CAACxD,IAAI,CAACD,MAAM,CAAC;MACvC,IAAI,CAAC5C,MAAM,CAACuG,mBAAmB,CAACV,CAAC,CAAC;IACtC,CAAC,CAAC;IACF,OAAO3G,YAAY,CAAC,MAAM;MACtBqB,CAAC,CAACI,OAAO,CAAC,CAAC;MACX,IAAI,CAACX,MAAM,CAACwG,mBAAmB,CAACX,CAAC,CAAC;IACtC,CAAC,CAAC;EACN;AACJ;AACA,OAAO,SAASY,aAAaA,CAACC,UAAU,EAAE1B,EAAE,EAAE;EAC1C,OAAO3F,6BAA6B,CAAC;IACjCsH,wBAAwB,EAAEA,CAAA,MAAO;MAAEC,MAAM,EAAE,EAAE;MAAEC,SAAS,EAAE;IAAM,CAAC,CAAC;IAClEC,YAAY,EAAEA,CAACC,OAAO,EAAEC,aAAa,KAAK;MACtC,IAAID,OAAO,CAACF,SAAS,CAACH,UAAU,CAAC,EAAE;QAC/B,MAAMrE,CAAC,GAAG0E,OAAO,CAACE,MAAM;QACxB,IAAI5E,CAAC,KAAKpB,SAAS,EAAE;UACjB+F,aAAa,CAACJ,MAAM,CAACM,IAAI,CAAC7E,CAAC,CAAC;QAChC;QACA2E,aAAa,CAACH,SAAS,GAAG,IAAI;MAClC;MACA,OAAO,IAAI;IACf;EACJ,CAAC,EAAE,CAACjE,MAAM,EAAEoE,aAAa,KAAK;IAC1B,MAAMrE,KAAK,GAAG+D,UAAU,CAAC7D,IAAI,CAACD,MAAM,CAAC;IACrC,IAAIoE,aAAa,CAACH,SAAS,EAAE;MACzB7B,EAAE,CAACrC,KAAK,EAAEqE,aAAa,CAACJ,MAAM,CAAC;IACnC;EACJ,CAAC,CAAC;AACN;AACA,OAAO,SAASO,sBAAsBA,CAACT,UAAU,EAAE1B,EAAE,EAAE;EACnD,MAAMoC,KAAK,GAAG,IAAInI,eAAe,CAAC,CAAC;EACnC,MAAMoI,UAAU,GAAGZ,aAAa,CAACC,UAAU,EAAE,CAAC/D,KAAK,EAAEiE,MAAM,KAAK;IAC5DQ,KAAK,CAAC3B,KAAK,CAAC,CAAC;IACbT,EAAE,CAACrC,KAAK,EAAEiE,MAAM,EAAEQ,KAAK,CAAC;EAC5B,CAAC,CAAC;EACF,OAAO;IACHzG,OAAOA,CAAA,EAAG;MACN0G,UAAU,CAAC1G,OAAO,CAAC,CAAC;MACpByG,KAAK,CAACzG,OAAO,CAAC,CAAC;IACnB;EACJ,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}