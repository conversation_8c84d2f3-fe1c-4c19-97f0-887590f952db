{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { AbstractTree } from './abstractTree.js';\nimport { ObjectTreeModel } from './objectTreeModel.js';\nexport class DataTree extends AbstractTree {\n  constructor(user, container, delegate, renderers, dataSource, options = {}) {\n    super(user, container, delegate, renderers, options);\n    this.user = user;\n    this.dataSource = dataSource;\n    this.identityProvider = options.identityProvider;\n  }\n  createModel(user, view, options) {\n    return new ObjectTreeModel(user, view, options);\n  }\n}", "map": {"version": 3, "names": ["AbstractTree", "ObjectTreeModel", "DataTree", "constructor", "user", "container", "delegate", "renderers", "dataSource", "options", "identity<PERSON><PERSON><PERSON>", "createModel", "view"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/browser/ui/tree/dataTree.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { AbstractTree } from './abstractTree.js';\nimport { ObjectTreeModel } from './objectTreeModel.js';\nexport class DataTree extends AbstractTree {\n    constructor(user, container, delegate, renderers, dataSource, options = {}) {\n        super(user, container, delegate, renderers, options);\n        this.user = user;\n        this.dataSource = dataSource;\n        this.identityProvider = options.identityProvider;\n    }\n    createModel(user, view, options) {\n        return new ObjectTreeModel(user, view, options);\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,YAAY,QAAQ,mBAAmB;AAChD,SAASC,eAAe,QAAQ,sBAAsB;AACtD,OAAO,MAAMC,QAAQ,SAASF,YAAY,CAAC;EACvCG,WAAWA,CAACC,IAAI,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IACxE,KAAK,CAACL,IAAI,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,SAAS,EAAEE,OAAO,CAAC;IACpD,IAAI,CAACL,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACI,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACE,gBAAgB,GAAGD,OAAO,CAACC,gBAAgB;EACpD;EACAC,WAAWA,CAACP,IAAI,EAAEQ,IAAI,EAAEH,OAAO,EAAE;IAC7B,OAAO,IAAIR,eAAe,CAACG,IAAI,EAAEQ,IAAI,EAAEH,OAAO,CAAC;EACnD;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}