{"ast": null, "code": "export default function (callback, that) {\n  var node = this,\n    nodes = [node],\n    children,\n    i,\n    index = -1;\n  while (node = nodes.pop()) {\n    callback.call(that, node, ++index, this);\n    if (children = node.children) {\n      for (i = children.length - 1; i >= 0; --i) {\n        nodes.push(children[i]);\n      }\n    }\n  }\n  return this;\n}", "map": {"version": 3, "names": ["callback", "that", "node", "nodes", "children", "i", "index", "pop", "call", "length", "push"], "sources": ["C:/console/aava-ui-web/node_modules/d3-hierarchy/src/hierarchy/eachBefore.js"], "sourcesContent": ["export default function(callback, that) {\n  var node = this, nodes = [node], children, i, index = -1;\n  while (node = nodes.pop()) {\n    callback.call(that, node, ++index, this);\n    if (children = node.children) {\n      for (i = children.length - 1; i >= 0; --i) {\n        nodes.push(children[i]);\n      }\n    }\n  }\n  return this;\n}\n"], "mappings": "AAAA,eAAe,UAASA,QAAQ,EAAEC,IAAI,EAAE;EACtC,IAAIC,IAAI,GAAG,IAAI;IAAEC,KAAK,GAAG,CAACD,IAAI,CAAC;IAAEE,QAAQ;IAAEC,CAAC;IAAEC,KAAK,GAAG,CAAC,CAAC;EACxD,OAAOJ,IAAI,GAAGC,KAAK,CAACI,GAAG,CAAC,CAAC,EAAE;IACzBP,QAAQ,CAACQ,IAAI,CAACP,IAAI,EAAEC,IAAI,EAAE,EAAEI,KAAK,EAAE,IAAI,CAAC;IACxC,IAAIF,QAAQ,GAAGF,IAAI,CAACE,QAAQ,EAAE;MAC5B,KAAKC,CAAC,GAAGD,QAAQ,CAACK,MAAM,GAAG,CAAC,EAAEJ,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;QACzCF,KAAK,CAACO,IAAI,CAACN,QAAQ,CAACC,CAAC,CAAC,CAAC;MACzB;IACF;EACF;EACA,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}