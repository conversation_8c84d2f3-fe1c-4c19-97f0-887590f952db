{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { SequenceDiff } from './algorithms/diffAlgorithm.js';\nimport { LineRangeMapping } from '../rangeMapping.js';\nimport { pushMany, compareBy, numberComparator, reverseOrder } from '../../../../base/common/arrays.js';\nimport { MonotonousArray, findLastMonotonous } from '../../../../base/common/arraysFind.js';\nimport { SetMap } from '../../../../base/common/map.js';\nimport { LineRange, LineRangeSet } from '../../core/lineRange.js';\nimport { LinesSliceCharSequence } from './linesSliceCharSequence.js';\nimport { LineRangeFragment, isSpace } from './utils.js';\nimport { MyersDiffAlgorithm } from './algorithms/myersDiffAlgorithm.js';\nimport { Range } from '../../core/range.js';\nexport function computeMovedLines(changes, originalLines, modifiedLines, hashedOriginalLines, hashedModifiedLines, timeout) {\n  let {\n    moves,\n    excludedChanges\n  } = computeMovesFromSimpleDeletionsToSimpleInsertions(changes, originalLines, modifiedLines, timeout);\n  if (!timeout.isValid()) {\n    return [];\n  }\n  const filteredChanges = changes.filter(c => !excludedChanges.has(c));\n  const unchangedMoves = computeUnchangedMoves(filteredChanges, hashedOriginalLines, hashedModifiedLines, originalLines, modifiedLines, timeout);\n  pushMany(moves, unchangedMoves);\n  moves = joinCloseConsecutiveMoves(moves);\n  // Ignore too short moves\n  moves = moves.filter(current => {\n    const lines = current.original.toOffsetRange().slice(originalLines).map(l => l.trim());\n    const originalText = lines.join('\\n');\n    return originalText.length >= 15 && countWhere(lines, l => l.length >= 2) >= 2;\n  });\n  moves = removeMovesInSameDiff(changes, moves);\n  return moves;\n}\nfunction countWhere(arr, predicate) {\n  let count = 0;\n  for (const t of arr) {\n    if (predicate(t)) {\n      count++;\n    }\n  }\n  return count;\n}\nfunction computeMovesFromSimpleDeletionsToSimpleInsertions(changes, originalLines, modifiedLines, timeout) {\n  const moves = [];\n  const deletions = changes.filter(c => c.modified.isEmpty && c.original.length >= 3).map(d => new LineRangeFragment(d.original, originalLines, d));\n  const insertions = new Set(changes.filter(c => c.original.isEmpty && c.modified.length >= 3).map(d => new LineRangeFragment(d.modified, modifiedLines, d)));\n  const excludedChanges = new Set();\n  for (const deletion of deletions) {\n    let highestSimilarity = -1;\n    let best;\n    for (const insertion of insertions) {\n      const similarity = deletion.computeSimilarity(insertion);\n      if (similarity > highestSimilarity) {\n        highestSimilarity = similarity;\n        best = insertion;\n      }\n    }\n    if (highestSimilarity > 0.90 && best) {\n      insertions.delete(best);\n      moves.push(new LineRangeMapping(deletion.range, best.range));\n      excludedChanges.add(deletion.source);\n      excludedChanges.add(best.source);\n    }\n    if (!timeout.isValid()) {\n      return {\n        moves,\n        excludedChanges\n      };\n    }\n  }\n  return {\n    moves,\n    excludedChanges\n  };\n}\nfunction computeUnchangedMoves(changes, hashedOriginalLines, hashedModifiedLines, originalLines, modifiedLines, timeout) {\n  const moves = [];\n  const original3LineHashes = new SetMap();\n  for (const change of changes) {\n    for (let i = change.original.startLineNumber; i < change.original.endLineNumberExclusive - 2; i++) {\n      const key = `${hashedOriginalLines[i - 1]}:${hashedOriginalLines[i + 1 - 1]}:${hashedOriginalLines[i + 2 - 1]}`;\n      original3LineHashes.add(key, {\n        range: new LineRange(i, i + 3)\n      });\n    }\n  }\n  const possibleMappings = [];\n  changes.sort(compareBy(c => c.modified.startLineNumber, numberComparator));\n  for (const change of changes) {\n    let lastMappings = [];\n    for (let i = change.modified.startLineNumber; i < change.modified.endLineNumberExclusive - 2; i++) {\n      const key = `${hashedModifiedLines[i - 1]}:${hashedModifiedLines[i + 1 - 1]}:${hashedModifiedLines[i + 2 - 1]}`;\n      const currentModifiedRange = new LineRange(i, i + 3);\n      const nextMappings = [];\n      original3LineHashes.forEach(key, ({\n        range\n      }) => {\n        for (const lastMapping of lastMappings) {\n          // does this match extend some last match?\n          if (lastMapping.originalLineRange.endLineNumberExclusive + 1 === range.endLineNumberExclusive && lastMapping.modifiedLineRange.endLineNumberExclusive + 1 === currentModifiedRange.endLineNumberExclusive) {\n            lastMapping.originalLineRange = new LineRange(lastMapping.originalLineRange.startLineNumber, range.endLineNumberExclusive);\n            lastMapping.modifiedLineRange = new LineRange(lastMapping.modifiedLineRange.startLineNumber, currentModifiedRange.endLineNumberExclusive);\n            nextMappings.push(lastMapping);\n            return;\n          }\n        }\n        const mapping = {\n          modifiedLineRange: currentModifiedRange,\n          originalLineRange: range\n        };\n        possibleMappings.push(mapping);\n        nextMappings.push(mapping);\n      });\n      lastMappings = nextMappings;\n    }\n    if (!timeout.isValid()) {\n      return [];\n    }\n  }\n  possibleMappings.sort(reverseOrder(compareBy(m => m.modifiedLineRange.length, numberComparator)));\n  const modifiedSet = new LineRangeSet();\n  const originalSet = new LineRangeSet();\n  for (const mapping of possibleMappings) {\n    const diffOrigToMod = mapping.modifiedLineRange.startLineNumber - mapping.originalLineRange.startLineNumber;\n    const modifiedSections = modifiedSet.subtractFrom(mapping.modifiedLineRange);\n    const originalTranslatedSections = originalSet.subtractFrom(mapping.originalLineRange).getWithDelta(diffOrigToMod);\n    const modifiedIntersectedSections = modifiedSections.getIntersection(originalTranslatedSections);\n    for (const s of modifiedIntersectedSections.ranges) {\n      if (s.length < 3) {\n        continue;\n      }\n      const modifiedLineRange = s;\n      const originalLineRange = s.delta(-diffOrigToMod);\n      moves.push(new LineRangeMapping(originalLineRange, modifiedLineRange));\n      modifiedSet.addRange(modifiedLineRange);\n      originalSet.addRange(originalLineRange);\n    }\n  }\n  moves.sort(compareBy(m => m.original.startLineNumber, numberComparator));\n  const monotonousChanges = new MonotonousArray(changes);\n  for (let i = 0; i < moves.length; i++) {\n    const move = moves[i];\n    const firstTouchingChangeOrig = monotonousChanges.findLastMonotonous(c => c.original.startLineNumber <= move.original.startLineNumber);\n    const firstTouchingChangeMod = findLastMonotonous(changes, c => c.modified.startLineNumber <= move.modified.startLineNumber);\n    const linesAbove = Math.max(move.original.startLineNumber - firstTouchingChangeOrig.original.startLineNumber, move.modified.startLineNumber - firstTouchingChangeMod.modified.startLineNumber);\n    const lastTouchingChangeOrig = monotonousChanges.findLastMonotonous(c => c.original.startLineNumber < move.original.endLineNumberExclusive);\n    const lastTouchingChangeMod = findLastMonotonous(changes, c => c.modified.startLineNumber < move.modified.endLineNumberExclusive);\n    const linesBelow = Math.max(lastTouchingChangeOrig.original.endLineNumberExclusive - move.original.endLineNumberExclusive, lastTouchingChangeMod.modified.endLineNumberExclusive - move.modified.endLineNumberExclusive);\n    let extendToTop;\n    for (extendToTop = 0; extendToTop < linesAbove; extendToTop++) {\n      const origLine = move.original.startLineNumber - extendToTop - 1;\n      const modLine = move.modified.startLineNumber - extendToTop - 1;\n      if (origLine > originalLines.length || modLine > modifiedLines.length) {\n        break;\n      }\n      if (modifiedSet.contains(modLine) || originalSet.contains(origLine)) {\n        break;\n      }\n      if (!areLinesSimilar(originalLines[origLine - 1], modifiedLines[modLine - 1], timeout)) {\n        break;\n      }\n    }\n    if (extendToTop > 0) {\n      originalSet.addRange(new LineRange(move.original.startLineNumber - extendToTop, move.original.startLineNumber));\n      modifiedSet.addRange(new LineRange(move.modified.startLineNumber - extendToTop, move.modified.startLineNumber));\n    }\n    let extendToBottom;\n    for (extendToBottom = 0; extendToBottom < linesBelow; extendToBottom++) {\n      const origLine = move.original.endLineNumberExclusive + extendToBottom;\n      const modLine = move.modified.endLineNumberExclusive + extendToBottom;\n      if (origLine > originalLines.length || modLine > modifiedLines.length) {\n        break;\n      }\n      if (modifiedSet.contains(modLine) || originalSet.contains(origLine)) {\n        break;\n      }\n      if (!areLinesSimilar(originalLines[origLine - 1], modifiedLines[modLine - 1], timeout)) {\n        break;\n      }\n    }\n    if (extendToBottom > 0) {\n      originalSet.addRange(new LineRange(move.original.endLineNumberExclusive, move.original.endLineNumberExclusive + extendToBottom));\n      modifiedSet.addRange(new LineRange(move.modified.endLineNumberExclusive, move.modified.endLineNumberExclusive + extendToBottom));\n    }\n    if (extendToTop > 0 || extendToBottom > 0) {\n      moves[i] = new LineRangeMapping(new LineRange(move.original.startLineNumber - extendToTop, move.original.endLineNumberExclusive + extendToBottom), new LineRange(move.modified.startLineNumber - extendToTop, move.modified.endLineNumberExclusive + extendToBottom));\n    }\n  }\n  return moves;\n}\nfunction areLinesSimilar(line1, line2, timeout) {\n  if (line1.trim() === line2.trim()) {\n    return true;\n  }\n  if (line1.length > 300 && line2.length > 300) {\n    return false;\n  }\n  const myersDiffingAlgorithm = new MyersDiffAlgorithm();\n  const result = myersDiffingAlgorithm.compute(new LinesSliceCharSequence([line1], new Range(1, 1, 1, line1.length), false), new LinesSliceCharSequence([line2], new Range(1, 1, 1, line2.length), false), timeout);\n  let commonNonSpaceCharCount = 0;\n  const inverted = SequenceDiff.invert(result.diffs, line1.length);\n  for (const seq of inverted) {\n    seq.seq1Range.forEach(idx => {\n      if (!isSpace(line1.charCodeAt(idx))) {\n        commonNonSpaceCharCount++;\n      }\n    });\n  }\n  function countNonWsChars(str) {\n    let count = 0;\n    for (let i = 0; i < line1.length; i++) {\n      if (!isSpace(str.charCodeAt(i))) {\n        count++;\n      }\n    }\n    return count;\n  }\n  const longerLineLength = countNonWsChars(line1.length > line2.length ? line1 : line2);\n  const r = commonNonSpaceCharCount / longerLineLength > 0.6 && longerLineLength > 10;\n  return r;\n}\nfunction joinCloseConsecutiveMoves(moves) {\n  if (moves.length === 0) {\n    return moves;\n  }\n  moves.sort(compareBy(m => m.original.startLineNumber, numberComparator));\n  const result = [moves[0]];\n  for (let i = 1; i < moves.length; i++) {\n    const last = result[result.length - 1];\n    const current = moves[i];\n    const originalDist = current.original.startLineNumber - last.original.endLineNumberExclusive;\n    const modifiedDist = current.modified.startLineNumber - last.modified.endLineNumberExclusive;\n    const currentMoveAfterLast = originalDist >= 0 && modifiedDist >= 0;\n    if (currentMoveAfterLast && originalDist + modifiedDist <= 2) {\n      result[result.length - 1] = last.join(current);\n      continue;\n    }\n    result.push(current);\n  }\n  return result;\n}\nfunction removeMovesInSameDiff(changes, moves) {\n  const changesMonotonous = new MonotonousArray(changes);\n  moves = moves.filter(m => {\n    const diffBeforeEndOfMoveOriginal = changesMonotonous.findLastMonotonous(c => c.original.startLineNumber < m.original.endLineNumberExclusive) || new LineRangeMapping(new LineRange(1, 1), new LineRange(1, 1));\n    const diffBeforeEndOfMoveModified = findLastMonotonous(changes, c => c.modified.startLineNumber < m.modified.endLineNumberExclusive);\n    const differentDiffs = diffBeforeEndOfMoveOriginal !== diffBeforeEndOfMoveModified;\n    return differentDiffs;\n  });\n  return moves;\n}", "map": {"version": 3, "names": ["SequenceDiff", "LineRangeMapping", "pushMany", "compareBy", "numberComparator", "reverseOrder", "MonotonousArray", "findLastMonotonous", "SetMap", "LineRange", "LineRangeSet", "LinesSliceCharSequence", "LineRangeFragment", "isSpace", "MyersDiffAlgorithm", "Range", "computeMovedLines", "changes", "originalLines", "modifiedLines", "hashedOriginalLines", "hashedModifiedLines", "timeout", "moves", "excluded<PERSON><PERSON><PERSON>", "computeMovesFromSimpleDeletionsToSimpleInsertions", "<PERSON><PERSON><PERSON><PERSON>", "filteredChanges", "filter", "c", "has", "unchangedMoves", "computeUnchangedMoves", "joinCloseConsecutiveMoves", "current", "lines", "original", "toOffsetRange", "slice", "map", "l", "trim", "originalText", "join", "length", "countWhere", "removeMovesInSameDiff", "arr", "predicate", "count", "t", "deletions", "modified", "isEmpty", "d", "insertions", "Set", "deletion", "highestSimilarity", "best", "insertion", "similarity", "computeSimilarity", "delete", "push", "range", "add", "source", "original3LineHashes", "change", "i", "startLineNumber", "endLineNumberExclusive", "key", "possibleMappings", "sort", "lastMappings", "currentModifiedRange", "nextMappings", "for<PERSON>ach", "lastMapping", "originalLineRange", "modifiedLineRange", "mapping", "m", "modifiedSet", "originalSet", "diffOrigToMod", "modifiedSections", "subtractFrom", "originalTranslatedSections", "getWithDelta", "modifiedIntersectedSections", "getIntersection", "s", "ranges", "delta", "addRange", "monotonousChanges", "move", "firstTouchingChangeOrig", "firstTouchingChangeMod", "linesAbove", "Math", "max", "lastTouchingChangeOrig", "lastTouchingChangeMod", "linesBelow", "extendToTop", "origLine", "modLine", "contains", "areLinesSimilar", "extendToBottom", "line1", "line2", "myersDiffingAlgorithm", "result", "compute", "commonNonSpaceCharCount", "inverted", "invert", "diffs", "seq", "seq1Range", "idx", "charCodeAt", "countNonWsChars", "str", "longerLineLength", "r", "last", "originalDist", "modifiedDist", "currentMoveAfterLast", "changesMonotonous", "diffBeforeEndOfMoveOriginal", "diffBeforeEndOfMoveModified", "differentDiffs"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/diff/defaultLinesDiffComputer/computeMovedLines.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { SequenceDiff } from './algorithms/diffAlgorithm.js';\nimport { LineRangeMapping } from '../rangeMapping.js';\nimport { pushMany, compareBy, numberComparator, reverseOrder } from '../../../../base/common/arrays.js';\nimport { MonotonousArray, findLastMonotonous } from '../../../../base/common/arraysFind.js';\nimport { SetMap } from '../../../../base/common/map.js';\nimport { LineRange, LineRangeSet } from '../../core/lineRange.js';\nimport { LinesSliceCharSequence } from './linesSliceCharSequence.js';\nimport { LineRangeFragment, isSpace } from './utils.js';\nimport { MyersDiffAlgorithm } from './algorithms/myersDiffAlgorithm.js';\nimport { Range } from '../../core/range.js';\nexport function computeMovedLines(changes, originalLines, modifiedLines, hashedOriginalLines, hashedModifiedLines, timeout) {\n    let { moves, excludedChanges } = computeMovesFromSimpleDeletionsToSimpleInsertions(changes, originalLines, modifiedLines, timeout);\n    if (!timeout.isValid()) {\n        return [];\n    }\n    const filteredChanges = changes.filter(c => !excludedChanges.has(c));\n    const unchangedMoves = computeUnchangedMoves(filteredChanges, hashedOriginalLines, hashedModifiedLines, originalLines, modifiedLines, timeout);\n    pushMany(moves, unchangedMoves);\n    moves = joinCloseConsecutiveMoves(moves);\n    // Ignore too short moves\n    moves = moves.filter(current => {\n        const lines = current.original.toOffsetRange().slice(originalLines).map(l => l.trim());\n        const originalText = lines.join('\\n');\n        return originalText.length >= 15 && countWhere(lines, l => l.length >= 2) >= 2;\n    });\n    moves = removeMovesInSameDiff(changes, moves);\n    return moves;\n}\nfunction countWhere(arr, predicate) {\n    let count = 0;\n    for (const t of arr) {\n        if (predicate(t)) {\n            count++;\n        }\n    }\n    return count;\n}\nfunction computeMovesFromSimpleDeletionsToSimpleInsertions(changes, originalLines, modifiedLines, timeout) {\n    const moves = [];\n    const deletions = changes\n        .filter(c => c.modified.isEmpty && c.original.length >= 3)\n        .map(d => new LineRangeFragment(d.original, originalLines, d));\n    const insertions = new Set(changes\n        .filter(c => c.original.isEmpty && c.modified.length >= 3)\n        .map(d => new LineRangeFragment(d.modified, modifiedLines, d)));\n    const excludedChanges = new Set();\n    for (const deletion of deletions) {\n        let highestSimilarity = -1;\n        let best;\n        for (const insertion of insertions) {\n            const similarity = deletion.computeSimilarity(insertion);\n            if (similarity > highestSimilarity) {\n                highestSimilarity = similarity;\n                best = insertion;\n            }\n        }\n        if (highestSimilarity > 0.90 && best) {\n            insertions.delete(best);\n            moves.push(new LineRangeMapping(deletion.range, best.range));\n            excludedChanges.add(deletion.source);\n            excludedChanges.add(best.source);\n        }\n        if (!timeout.isValid()) {\n            return { moves, excludedChanges };\n        }\n    }\n    return { moves, excludedChanges };\n}\nfunction computeUnchangedMoves(changes, hashedOriginalLines, hashedModifiedLines, originalLines, modifiedLines, timeout) {\n    const moves = [];\n    const original3LineHashes = new SetMap();\n    for (const change of changes) {\n        for (let i = change.original.startLineNumber; i < change.original.endLineNumberExclusive - 2; i++) {\n            const key = `${hashedOriginalLines[i - 1]}:${hashedOriginalLines[i + 1 - 1]}:${hashedOriginalLines[i + 2 - 1]}`;\n            original3LineHashes.add(key, { range: new LineRange(i, i + 3) });\n        }\n    }\n    const possibleMappings = [];\n    changes.sort(compareBy(c => c.modified.startLineNumber, numberComparator));\n    for (const change of changes) {\n        let lastMappings = [];\n        for (let i = change.modified.startLineNumber; i < change.modified.endLineNumberExclusive - 2; i++) {\n            const key = `${hashedModifiedLines[i - 1]}:${hashedModifiedLines[i + 1 - 1]}:${hashedModifiedLines[i + 2 - 1]}`;\n            const currentModifiedRange = new LineRange(i, i + 3);\n            const nextMappings = [];\n            original3LineHashes.forEach(key, ({ range }) => {\n                for (const lastMapping of lastMappings) {\n                    // does this match extend some last match?\n                    if (lastMapping.originalLineRange.endLineNumberExclusive + 1 === range.endLineNumberExclusive &&\n                        lastMapping.modifiedLineRange.endLineNumberExclusive + 1 === currentModifiedRange.endLineNumberExclusive) {\n                        lastMapping.originalLineRange = new LineRange(lastMapping.originalLineRange.startLineNumber, range.endLineNumberExclusive);\n                        lastMapping.modifiedLineRange = new LineRange(lastMapping.modifiedLineRange.startLineNumber, currentModifiedRange.endLineNumberExclusive);\n                        nextMappings.push(lastMapping);\n                        return;\n                    }\n                }\n                const mapping = {\n                    modifiedLineRange: currentModifiedRange,\n                    originalLineRange: range,\n                };\n                possibleMappings.push(mapping);\n                nextMappings.push(mapping);\n            });\n            lastMappings = nextMappings;\n        }\n        if (!timeout.isValid()) {\n            return [];\n        }\n    }\n    possibleMappings.sort(reverseOrder(compareBy(m => m.modifiedLineRange.length, numberComparator)));\n    const modifiedSet = new LineRangeSet();\n    const originalSet = new LineRangeSet();\n    for (const mapping of possibleMappings) {\n        const diffOrigToMod = mapping.modifiedLineRange.startLineNumber - mapping.originalLineRange.startLineNumber;\n        const modifiedSections = modifiedSet.subtractFrom(mapping.modifiedLineRange);\n        const originalTranslatedSections = originalSet.subtractFrom(mapping.originalLineRange).getWithDelta(diffOrigToMod);\n        const modifiedIntersectedSections = modifiedSections.getIntersection(originalTranslatedSections);\n        for (const s of modifiedIntersectedSections.ranges) {\n            if (s.length < 3) {\n                continue;\n            }\n            const modifiedLineRange = s;\n            const originalLineRange = s.delta(-diffOrigToMod);\n            moves.push(new LineRangeMapping(originalLineRange, modifiedLineRange));\n            modifiedSet.addRange(modifiedLineRange);\n            originalSet.addRange(originalLineRange);\n        }\n    }\n    moves.sort(compareBy(m => m.original.startLineNumber, numberComparator));\n    const monotonousChanges = new MonotonousArray(changes);\n    for (let i = 0; i < moves.length; i++) {\n        const move = moves[i];\n        const firstTouchingChangeOrig = monotonousChanges.findLastMonotonous(c => c.original.startLineNumber <= move.original.startLineNumber);\n        const firstTouchingChangeMod = findLastMonotonous(changes, c => c.modified.startLineNumber <= move.modified.startLineNumber);\n        const linesAbove = Math.max(move.original.startLineNumber - firstTouchingChangeOrig.original.startLineNumber, move.modified.startLineNumber - firstTouchingChangeMod.modified.startLineNumber);\n        const lastTouchingChangeOrig = monotonousChanges.findLastMonotonous(c => c.original.startLineNumber < move.original.endLineNumberExclusive);\n        const lastTouchingChangeMod = findLastMonotonous(changes, c => c.modified.startLineNumber < move.modified.endLineNumberExclusive);\n        const linesBelow = Math.max(lastTouchingChangeOrig.original.endLineNumberExclusive - move.original.endLineNumberExclusive, lastTouchingChangeMod.modified.endLineNumberExclusive - move.modified.endLineNumberExclusive);\n        let extendToTop;\n        for (extendToTop = 0; extendToTop < linesAbove; extendToTop++) {\n            const origLine = move.original.startLineNumber - extendToTop - 1;\n            const modLine = move.modified.startLineNumber - extendToTop - 1;\n            if (origLine > originalLines.length || modLine > modifiedLines.length) {\n                break;\n            }\n            if (modifiedSet.contains(modLine) || originalSet.contains(origLine)) {\n                break;\n            }\n            if (!areLinesSimilar(originalLines[origLine - 1], modifiedLines[modLine - 1], timeout)) {\n                break;\n            }\n        }\n        if (extendToTop > 0) {\n            originalSet.addRange(new LineRange(move.original.startLineNumber - extendToTop, move.original.startLineNumber));\n            modifiedSet.addRange(new LineRange(move.modified.startLineNumber - extendToTop, move.modified.startLineNumber));\n        }\n        let extendToBottom;\n        for (extendToBottom = 0; extendToBottom < linesBelow; extendToBottom++) {\n            const origLine = move.original.endLineNumberExclusive + extendToBottom;\n            const modLine = move.modified.endLineNumberExclusive + extendToBottom;\n            if (origLine > originalLines.length || modLine > modifiedLines.length) {\n                break;\n            }\n            if (modifiedSet.contains(modLine) || originalSet.contains(origLine)) {\n                break;\n            }\n            if (!areLinesSimilar(originalLines[origLine - 1], modifiedLines[modLine - 1], timeout)) {\n                break;\n            }\n        }\n        if (extendToBottom > 0) {\n            originalSet.addRange(new LineRange(move.original.endLineNumberExclusive, move.original.endLineNumberExclusive + extendToBottom));\n            modifiedSet.addRange(new LineRange(move.modified.endLineNumberExclusive, move.modified.endLineNumberExclusive + extendToBottom));\n        }\n        if (extendToTop > 0 || extendToBottom > 0) {\n            moves[i] = new LineRangeMapping(new LineRange(move.original.startLineNumber - extendToTop, move.original.endLineNumberExclusive + extendToBottom), new LineRange(move.modified.startLineNumber - extendToTop, move.modified.endLineNumberExclusive + extendToBottom));\n        }\n    }\n    return moves;\n}\nfunction areLinesSimilar(line1, line2, timeout) {\n    if (line1.trim() === line2.trim()) {\n        return true;\n    }\n    if (line1.length > 300 && line2.length > 300) {\n        return false;\n    }\n    const myersDiffingAlgorithm = new MyersDiffAlgorithm();\n    const result = myersDiffingAlgorithm.compute(new LinesSliceCharSequence([line1], new Range(1, 1, 1, line1.length), false), new LinesSliceCharSequence([line2], new Range(1, 1, 1, line2.length), false), timeout);\n    let commonNonSpaceCharCount = 0;\n    const inverted = SequenceDiff.invert(result.diffs, line1.length);\n    for (const seq of inverted) {\n        seq.seq1Range.forEach(idx => {\n            if (!isSpace(line1.charCodeAt(idx))) {\n                commonNonSpaceCharCount++;\n            }\n        });\n    }\n    function countNonWsChars(str) {\n        let count = 0;\n        for (let i = 0; i < line1.length; i++) {\n            if (!isSpace(str.charCodeAt(i))) {\n                count++;\n            }\n        }\n        return count;\n    }\n    const longerLineLength = countNonWsChars(line1.length > line2.length ? line1 : line2);\n    const r = commonNonSpaceCharCount / longerLineLength > 0.6 && longerLineLength > 10;\n    return r;\n}\nfunction joinCloseConsecutiveMoves(moves) {\n    if (moves.length === 0) {\n        return moves;\n    }\n    moves.sort(compareBy(m => m.original.startLineNumber, numberComparator));\n    const result = [moves[0]];\n    for (let i = 1; i < moves.length; i++) {\n        const last = result[result.length - 1];\n        const current = moves[i];\n        const originalDist = current.original.startLineNumber - last.original.endLineNumberExclusive;\n        const modifiedDist = current.modified.startLineNumber - last.modified.endLineNumberExclusive;\n        const currentMoveAfterLast = originalDist >= 0 && modifiedDist >= 0;\n        if (currentMoveAfterLast && originalDist + modifiedDist <= 2) {\n            result[result.length - 1] = last.join(current);\n            continue;\n        }\n        result.push(current);\n    }\n    return result;\n}\nfunction removeMovesInSameDiff(changes, moves) {\n    const changesMonotonous = new MonotonousArray(changes);\n    moves = moves.filter(m => {\n        const diffBeforeEndOfMoveOriginal = changesMonotonous.findLastMonotonous(c => c.original.startLineNumber < m.original.endLineNumberExclusive)\n            || new LineRangeMapping(new LineRange(1, 1), new LineRange(1, 1));\n        const diffBeforeEndOfMoveModified = findLastMonotonous(changes, c => c.modified.startLineNumber < m.modified.endLineNumberExclusive);\n        const differentDiffs = diffBeforeEndOfMoveOriginal !== diffBeforeEndOfMoveModified;\n        return differentDiffs;\n    });\n    return moves;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,YAAY,QAAQ,+BAA+B;AAC5D,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,QAAQ,EAAEC,SAAS,EAAEC,gBAAgB,EAAEC,YAAY,QAAQ,mCAAmC;AACvG,SAASC,eAAe,EAAEC,kBAAkB,QAAQ,uCAAuC;AAC3F,SAASC,MAAM,QAAQ,gCAAgC;AACvD,SAASC,SAAS,EAAEC,YAAY,QAAQ,yBAAyB;AACjE,SAASC,sBAAsB,QAAQ,6BAA6B;AACpE,SAASC,iBAAiB,EAAEC,OAAO,QAAQ,YAAY;AACvD,SAASC,kBAAkB,QAAQ,oCAAoC;AACvE,SAASC,KAAK,QAAQ,qBAAqB;AAC3C,OAAO,SAASC,iBAAiBA,CAACC,OAAO,EAAEC,aAAa,EAAEC,aAAa,EAAEC,mBAAmB,EAAEC,mBAAmB,EAAEC,OAAO,EAAE;EACxH,IAAI;IAAEC,KAAK;IAAEC;EAAgB,CAAC,GAAGC,iDAAiD,CAACR,OAAO,EAAEC,aAAa,EAAEC,aAAa,EAAEG,OAAO,CAAC;EAClI,IAAI,CAACA,OAAO,CAACI,OAAO,CAAC,CAAC,EAAE;IACpB,OAAO,EAAE;EACb;EACA,MAAMC,eAAe,GAAGV,OAAO,CAACW,MAAM,CAACC,CAAC,IAAI,CAACL,eAAe,CAACM,GAAG,CAACD,CAAC,CAAC,CAAC;EACpE,MAAME,cAAc,GAAGC,qBAAqB,CAACL,eAAe,EAAEP,mBAAmB,EAAEC,mBAAmB,EAAEH,aAAa,EAAEC,aAAa,EAAEG,OAAO,CAAC;EAC9IpB,QAAQ,CAACqB,KAAK,EAAEQ,cAAc,CAAC;EAC/BR,KAAK,GAAGU,yBAAyB,CAACV,KAAK,CAAC;EACxC;EACAA,KAAK,GAAGA,KAAK,CAACK,MAAM,CAACM,OAAO,IAAI;IAC5B,MAAMC,KAAK,GAAGD,OAAO,CAACE,QAAQ,CAACC,aAAa,CAAC,CAAC,CAACC,KAAK,CAACpB,aAAa,CAAC,CAACqB,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC;IACtF,MAAMC,YAAY,GAAGP,KAAK,CAACQ,IAAI,CAAC,IAAI,CAAC;IACrC,OAAOD,YAAY,CAACE,MAAM,IAAI,EAAE,IAAIC,UAAU,CAACV,KAAK,EAAEK,CAAC,IAAIA,CAAC,CAACI,MAAM,IAAI,CAAC,CAAC,IAAI,CAAC;EAClF,CAAC,CAAC;EACFrB,KAAK,GAAGuB,qBAAqB,CAAC7B,OAAO,EAAEM,KAAK,CAAC;EAC7C,OAAOA,KAAK;AAChB;AACA,SAASsB,UAAUA,CAACE,GAAG,EAAEC,SAAS,EAAE;EAChC,IAAIC,KAAK,GAAG,CAAC;EACb,KAAK,MAAMC,CAAC,IAAIH,GAAG,EAAE;IACjB,IAAIC,SAAS,CAACE,CAAC,CAAC,EAAE;MACdD,KAAK,EAAE;IACX;EACJ;EACA,OAAOA,KAAK;AAChB;AACA,SAASxB,iDAAiDA,CAACR,OAAO,EAAEC,aAAa,EAAEC,aAAa,EAAEG,OAAO,EAAE;EACvG,MAAMC,KAAK,GAAG,EAAE;EAChB,MAAM4B,SAAS,GAAGlC,OAAO,CACpBW,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACuB,QAAQ,CAACC,OAAO,IAAIxB,CAAC,CAACO,QAAQ,CAACQ,MAAM,IAAI,CAAC,CAAC,CACzDL,GAAG,CAACe,CAAC,IAAI,IAAI1C,iBAAiB,CAAC0C,CAAC,CAAClB,QAAQ,EAAElB,aAAa,EAAEoC,CAAC,CAAC,CAAC;EAClE,MAAMC,UAAU,GAAG,IAAIC,GAAG,CAACvC,OAAO,CAC7BW,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACO,QAAQ,CAACiB,OAAO,IAAIxB,CAAC,CAACuB,QAAQ,CAACR,MAAM,IAAI,CAAC,CAAC,CACzDL,GAAG,CAACe,CAAC,IAAI,IAAI1C,iBAAiB,CAAC0C,CAAC,CAACF,QAAQ,EAAEjC,aAAa,EAAEmC,CAAC,CAAC,CAAC,CAAC;EACnE,MAAM9B,eAAe,GAAG,IAAIgC,GAAG,CAAC,CAAC;EACjC,KAAK,MAAMC,QAAQ,IAAIN,SAAS,EAAE;IAC9B,IAAIO,iBAAiB,GAAG,CAAC,CAAC;IAC1B,IAAIC,IAAI;IACR,KAAK,MAAMC,SAAS,IAAIL,UAAU,EAAE;MAChC,MAAMM,UAAU,GAAGJ,QAAQ,CAACK,iBAAiB,CAACF,SAAS,CAAC;MACxD,IAAIC,UAAU,GAAGH,iBAAiB,EAAE;QAChCA,iBAAiB,GAAGG,UAAU;QAC9BF,IAAI,GAAGC,SAAS;MACpB;IACJ;IACA,IAAIF,iBAAiB,GAAG,IAAI,IAAIC,IAAI,EAAE;MAClCJ,UAAU,CAACQ,MAAM,CAACJ,IAAI,CAAC;MACvBpC,KAAK,CAACyC,IAAI,CAAC,IAAI/D,gBAAgB,CAACwD,QAAQ,CAACQ,KAAK,EAAEN,IAAI,CAACM,KAAK,CAAC,CAAC;MAC5DzC,eAAe,CAAC0C,GAAG,CAACT,QAAQ,CAACU,MAAM,CAAC;MACpC3C,eAAe,CAAC0C,GAAG,CAACP,IAAI,CAACQ,MAAM,CAAC;IACpC;IACA,IAAI,CAAC7C,OAAO,CAACI,OAAO,CAAC,CAAC,EAAE;MACpB,OAAO;QAAEH,KAAK;QAAEC;MAAgB,CAAC;IACrC;EACJ;EACA,OAAO;IAAED,KAAK;IAAEC;EAAgB,CAAC;AACrC;AACA,SAASQ,qBAAqBA,CAACf,OAAO,EAAEG,mBAAmB,EAAEC,mBAAmB,EAAEH,aAAa,EAAEC,aAAa,EAAEG,OAAO,EAAE;EACrH,MAAMC,KAAK,GAAG,EAAE;EAChB,MAAM6C,mBAAmB,GAAG,IAAI5D,MAAM,CAAC,CAAC;EACxC,KAAK,MAAM6D,MAAM,IAAIpD,OAAO,EAAE;IAC1B,KAAK,IAAIqD,CAAC,GAAGD,MAAM,CAACjC,QAAQ,CAACmC,eAAe,EAAED,CAAC,GAAGD,MAAM,CAACjC,QAAQ,CAACoC,sBAAsB,GAAG,CAAC,EAAEF,CAAC,EAAE,EAAE;MAC/F,MAAMG,GAAG,GAAG,GAAGrD,mBAAmB,CAACkD,CAAC,GAAG,CAAC,CAAC,IAAIlD,mBAAmB,CAACkD,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAIlD,mBAAmB,CAACkD,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;MAC/GF,mBAAmB,CAACF,GAAG,CAACO,GAAG,EAAE;QAAER,KAAK,EAAE,IAAIxD,SAAS,CAAC6D,CAAC,EAAEA,CAAC,GAAG,CAAC;MAAE,CAAC,CAAC;IACpE;EACJ;EACA,MAAMI,gBAAgB,GAAG,EAAE;EAC3BzD,OAAO,CAAC0D,IAAI,CAACxE,SAAS,CAAC0B,CAAC,IAAIA,CAAC,CAACuB,QAAQ,CAACmB,eAAe,EAAEnE,gBAAgB,CAAC,CAAC;EAC1E,KAAK,MAAMiE,MAAM,IAAIpD,OAAO,EAAE;IAC1B,IAAI2D,YAAY,GAAG,EAAE;IACrB,KAAK,IAAIN,CAAC,GAAGD,MAAM,CAACjB,QAAQ,CAACmB,eAAe,EAAED,CAAC,GAAGD,MAAM,CAACjB,QAAQ,CAACoB,sBAAsB,GAAG,CAAC,EAAEF,CAAC,EAAE,EAAE;MAC/F,MAAMG,GAAG,GAAG,GAAGpD,mBAAmB,CAACiD,CAAC,GAAG,CAAC,CAAC,IAAIjD,mBAAmB,CAACiD,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAIjD,mBAAmB,CAACiD,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;MAC/G,MAAMO,oBAAoB,GAAG,IAAIpE,SAAS,CAAC6D,CAAC,EAAEA,CAAC,GAAG,CAAC,CAAC;MACpD,MAAMQ,YAAY,GAAG,EAAE;MACvBV,mBAAmB,CAACW,OAAO,CAACN,GAAG,EAAE,CAAC;QAAER;MAAM,CAAC,KAAK;QAC5C,KAAK,MAAMe,WAAW,IAAIJ,YAAY,EAAE;UACpC;UACA,IAAII,WAAW,CAACC,iBAAiB,CAACT,sBAAsB,GAAG,CAAC,KAAKP,KAAK,CAACO,sBAAsB,IACzFQ,WAAW,CAACE,iBAAiB,CAACV,sBAAsB,GAAG,CAAC,KAAKK,oBAAoB,CAACL,sBAAsB,EAAE;YAC1GQ,WAAW,CAACC,iBAAiB,GAAG,IAAIxE,SAAS,CAACuE,WAAW,CAACC,iBAAiB,CAACV,eAAe,EAAEN,KAAK,CAACO,sBAAsB,CAAC;YAC1HQ,WAAW,CAACE,iBAAiB,GAAG,IAAIzE,SAAS,CAACuE,WAAW,CAACE,iBAAiB,CAACX,eAAe,EAAEM,oBAAoB,CAACL,sBAAsB,CAAC;YACzIM,YAAY,CAACd,IAAI,CAACgB,WAAW,CAAC;YAC9B;UACJ;QACJ;QACA,MAAMG,OAAO,GAAG;UACZD,iBAAiB,EAAEL,oBAAoB;UACvCI,iBAAiB,EAAEhB;QACvB,CAAC;QACDS,gBAAgB,CAACV,IAAI,CAACmB,OAAO,CAAC;QAC9BL,YAAY,CAACd,IAAI,CAACmB,OAAO,CAAC;MAC9B,CAAC,CAAC;MACFP,YAAY,GAAGE,YAAY;IAC/B;IACA,IAAI,CAACxD,OAAO,CAACI,OAAO,CAAC,CAAC,EAAE;MACpB,OAAO,EAAE;IACb;EACJ;EACAgD,gBAAgB,CAACC,IAAI,CAACtE,YAAY,CAACF,SAAS,CAACiF,CAAC,IAAIA,CAAC,CAACF,iBAAiB,CAACtC,MAAM,EAAExC,gBAAgB,CAAC,CAAC,CAAC;EACjG,MAAMiF,WAAW,GAAG,IAAI3E,YAAY,CAAC,CAAC;EACtC,MAAM4E,WAAW,GAAG,IAAI5E,YAAY,CAAC,CAAC;EACtC,KAAK,MAAMyE,OAAO,IAAIT,gBAAgB,EAAE;IACpC,MAAMa,aAAa,GAAGJ,OAAO,CAACD,iBAAiB,CAACX,eAAe,GAAGY,OAAO,CAACF,iBAAiB,CAACV,eAAe;IAC3G,MAAMiB,gBAAgB,GAAGH,WAAW,CAACI,YAAY,CAACN,OAAO,CAACD,iBAAiB,CAAC;IAC5E,MAAMQ,0BAA0B,GAAGJ,WAAW,CAACG,YAAY,CAACN,OAAO,CAACF,iBAAiB,CAAC,CAACU,YAAY,CAACJ,aAAa,CAAC;IAClH,MAAMK,2BAA2B,GAAGJ,gBAAgB,CAACK,eAAe,CAACH,0BAA0B,CAAC;IAChG,KAAK,MAAMI,CAAC,IAAIF,2BAA2B,CAACG,MAAM,EAAE;MAChD,IAAID,CAAC,CAAClD,MAAM,GAAG,CAAC,EAAE;QACd;MACJ;MACA,MAAMsC,iBAAiB,GAAGY,CAAC;MAC3B,MAAMb,iBAAiB,GAAGa,CAAC,CAACE,KAAK,CAAC,CAACT,aAAa,CAAC;MACjDhE,KAAK,CAACyC,IAAI,CAAC,IAAI/D,gBAAgB,CAACgF,iBAAiB,EAAEC,iBAAiB,CAAC,CAAC;MACtEG,WAAW,CAACY,QAAQ,CAACf,iBAAiB,CAAC;MACvCI,WAAW,CAACW,QAAQ,CAAChB,iBAAiB,CAAC;IAC3C;EACJ;EACA1D,KAAK,CAACoD,IAAI,CAACxE,SAAS,CAACiF,CAAC,IAAIA,CAAC,CAAChD,QAAQ,CAACmC,eAAe,EAAEnE,gBAAgB,CAAC,CAAC;EACxE,MAAM8F,iBAAiB,GAAG,IAAI5F,eAAe,CAACW,OAAO,CAAC;EACtD,KAAK,IAAIqD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG/C,KAAK,CAACqB,MAAM,EAAE0B,CAAC,EAAE,EAAE;IACnC,MAAM6B,IAAI,GAAG5E,KAAK,CAAC+C,CAAC,CAAC;IACrB,MAAM8B,uBAAuB,GAAGF,iBAAiB,CAAC3F,kBAAkB,CAACsB,CAAC,IAAIA,CAAC,CAACO,QAAQ,CAACmC,eAAe,IAAI4B,IAAI,CAAC/D,QAAQ,CAACmC,eAAe,CAAC;IACtI,MAAM8B,sBAAsB,GAAG9F,kBAAkB,CAACU,OAAO,EAAEY,CAAC,IAAIA,CAAC,CAACuB,QAAQ,CAACmB,eAAe,IAAI4B,IAAI,CAAC/C,QAAQ,CAACmB,eAAe,CAAC;IAC5H,MAAM+B,UAAU,GAAGC,IAAI,CAACC,GAAG,CAACL,IAAI,CAAC/D,QAAQ,CAACmC,eAAe,GAAG6B,uBAAuB,CAAChE,QAAQ,CAACmC,eAAe,EAAE4B,IAAI,CAAC/C,QAAQ,CAACmB,eAAe,GAAG8B,sBAAsB,CAACjD,QAAQ,CAACmB,eAAe,CAAC;IAC9L,MAAMkC,sBAAsB,GAAGP,iBAAiB,CAAC3F,kBAAkB,CAACsB,CAAC,IAAIA,CAAC,CAACO,QAAQ,CAACmC,eAAe,GAAG4B,IAAI,CAAC/D,QAAQ,CAACoC,sBAAsB,CAAC;IAC3I,MAAMkC,qBAAqB,GAAGnG,kBAAkB,CAACU,OAAO,EAAEY,CAAC,IAAIA,CAAC,CAACuB,QAAQ,CAACmB,eAAe,GAAG4B,IAAI,CAAC/C,QAAQ,CAACoB,sBAAsB,CAAC;IACjI,MAAMmC,UAAU,GAAGJ,IAAI,CAACC,GAAG,CAACC,sBAAsB,CAACrE,QAAQ,CAACoC,sBAAsB,GAAG2B,IAAI,CAAC/D,QAAQ,CAACoC,sBAAsB,EAAEkC,qBAAqB,CAACtD,QAAQ,CAACoB,sBAAsB,GAAG2B,IAAI,CAAC/C,QAAQ,CAACoB,sBAAsB,CAAC;IACxN,IAAIoC,WAAW;IACf,KAAKA,WAAW,GAAG,CAAC,EAAEA,WAAW,GAAGN,UAAU,EAAEM,WAAW,EAAE,EAAE;MAC3D,MAAMC,QAAQ,GAAGV,IAAI,CAAC/D,QAAQ,CAACmC,eAAe,GAAGqC,WAAW,GAAG,CAAC;MAChE,MAAME,OAAO,GAAGX,IAAI,CAAC/C,QAAQ,CAACmB,eAAe,GAAGqC,WAAW,GAAG,CAAC;MAC/D,IAAIC,QAAQ,GAAG3F,aAAa,CAAC0B,MAAM,IAAIkE,OAAO,GAAG3F,aAAa,CAACyB,MAAM,EAAE;QACnE;MACJ;MACA,IAAIyC,WAAW,CAAC0B,QAAQ,CAACD,OAAO,CAAC,IAAIxB,WAAW,CAACyB,QAAQ,CAACF,QAAQ,CAAC,EAAE;QACjE;MACJ;MACA,IAAI,CAACG,eAAe,CAAC9F,aAAa,CAAC2F,QAAQ,GAAG,CAAC,CAAC,EAAE1F,aAAa,CAAC2F,OAAO,GAAG,CAAC,CAAC,EAAExF,OAAO,CAAC,EAAE;QACpF;MACJ;IACJ;IACA,IAAIsF,WAAW,GAAG,CAAC,EAAE;MACjBtB,WAAW,CAACW,QAAQ,CAAC,IAAIxF,SAAS,CAAC0F,IAAI,CAAC/D,QAAQ,CAACmC,eAAe,GAAGqC,WAAW,EAAET,IAAI,CAAC/D,QAAQ,CAACmC,eAAe,CAAC,CAAC;MAC/Gc,WAAW,CAACY,QAAQ,CAAC,IAAIxF,SAAS,CAAC0F,IAAI,CAAC/C,QAAQ,CAACmB,eAAe,GAAGqC,WAAW,EAAET,IAAI,CAAC/C,QAAQ,CAACmB,eAAe,CAAC,CAAC;IACnH;IACA,IAAI0C,cAAc;IAClB,KAAKA,cAAc,GAAG,CAAC,EAAEA,cAAc,GAAGN,UAAU,EAAEM,cAAc,EAAE,EAAE;MACpE,MAAMJ,QAAQ,GAAGV,IAAI,CAAC/D,QAAQ,CAACoC,sBAAsB,GAAGyC,cAAc;MACtE,MAAMH,OAAO,GAAGX,IAAI,CAAC/C,QAAQ,CAACoB,sBAAsB,GAAGyC,cAAc;MACrE,IAAIJ,QAAQ,GAAG3F,aAAa,CAAC0B,MAAM,IAAIkE,OAAO,GAAG3F,aAAa,CAACyB,MAAM,EAAE;QACnE;MACJ;MACA,IAAIyC,WAAW,CAAC0B,QAAQ,CAACD,OAAO,CAAC,IAAIxB,WAAW,CAACyB,QAAQ,CAACF,QAAQ,CAAC,EAAE;QACjE;MACJ;MACA,IAAI,CAACG,eAAe,CAAC9F,aAAa,CAAC2F,QAAQ,GAAG,CAAC,CAAC,EAAE1F,aAAa,CAAC2F,OAAO,GAAG,CAAC,CAAC,EAAExF,OAAO,CAAC,EAAE;QACpF;MACJ;IACJ;IACA,IAAI2F,cAAc,GAAG,CAAC,EAAE;MACpB3B,WAAW,CAACW,QAAQ,CAAC,IAAIxF,SAAS,CAAC0F,IAAI,CAAC/D,QAAQ,CAACoC,sBAAsB,EAAE2B,IAAI,CAAC/D,QAAQ,CAACoC,sBAAsB,GAAGyC,cAAc,CAAC,CAAC;MAChI5B,WAAW,CAACY,QAAQ,CAAC,IAAIxF,SAAS,CAAC0F,IAAI,CAAC/C,QAAQ,CAACoB,sBAAsB,EAAE2B,IAAI,CAAC/C,QAAQ,CAACoB,sBAAsB,GAAGyC,cAAc,CAAC,CAAC;IACpI;IACA,IAAIL,WAAW,GAAG,CAAC,IAAIK,cAAc,GAAG,CAAC,EAAE;MACvC1F,KAAK,CAAC+C,CAAC,CAAC,GAAG,IAAIrE,gBAAgB,CAAC,IAAIQ,SAAS,CAAC0F,IAAI,CAAC/D,QAAQ,CAACmC,eAAe,GAAGqC,WAAW,EAAET,IAAI,CAAC/D,QAAQ,CAACoC,sBAAsB,GAAGyC,cAAc,CAAC,EAAE,IAAIxG,SAAS,CAAC0F,IAAI,CAAC/C,QAAQ,CAACmB,eAAe,GAAGqC,WAAW,EAAET,IAAI,CAAC/C,QAAQ,CAACoB,sBAAsB,GAAGyC,cAAc,CAAC,CAAC;IACzQ;EACJ;EACA,OAAO1F,KAAK;AAChB;AACA,SAASyF,eAAeA,CAACE,KAAK,EAAEC,KAAK,EAAE7F,OAAO,EAAE;EAC5C,IAAI4F,KAAK,CAACzE,IAAI,CAAC,CAAC,KAAK0E,KAAK,CAAC1E,IAAI,CAAC,CAAC,EAAE;IAC/B,OAAO,IAAI;EACf;EACA,IAAIyE,KAAK,CAACtE,MAAM,GAAG,GAAG,IAAIuE,KAAK,CAACvE,MAAM,GAAG,GAAG,EAAE;IAC1C,OAAO,KAAK;EAChB;EACA,MAAMwE,qBAAqB,GAAG,IAAItG,kBAAkB,CAAC,CAAC;EACtD,MAAMuG,MAAM,GAAGD,qBAAqB,CAACE,OAAO,CAAC,IAAI3G,sBAAsB,CAAC,CAACuG,KAAK,CAAC,EAAE,IAAInG,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAEmG,KAAK,CAACtE,MAAM,CAAC,EAAE,KAAK,CAAC,EAAE,IAAIjC,sBAAsB,CAAC,CAACwG,KAAK,CAAC,EAAE,IAAIpG,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAEoG,KAAK,CAACvE,MAAM,CAAC,EAAE,KAAK,CAAC,EAAEtB,OAAO,CAAC;EACjN,IAAIiG,uBAAuB,GAAG,CAAC;EAC/B,MAAMC,QAAQ,GAAGxH,YAAY,CAACyH,MAAM,CAACJ,MAAM,CAACK,KAAK,EAAER,KAAK,CAACtE,MAAM,CAAC;EAChE,KAAK,MAAM+E,GAAG,IAAIH,QAAQ,EAAE;IACxBG,GAAG,CAACC,SAAS,CAAC7C,OAAO,CAAC8C,GAAG,IAAI;MACzB,IAAI,CAAChH,OAAO,CAACqG,KAAK,CAACY,UAAU,CAACD,GAAG,CAAC,CAAC,EAAE;QACjCN,uBAAuB,EAAE;MAC7B;IACJ,CAAC,CAAC;EACN;EACA,SAASQ,eAAeA,CAACC,GAAG,EAAE;IAC1B,IAAI/E,KAAK,GAAG,CAAC;IACb,KAAK,IAAIqB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4C,KAAK,CAACtE,MAAM,EAAE0B,CAAC,EAAE,EAAE;MACnC,IAAI,CAACzD,OAAO,CAACmH,GAAG,CAACF,UAAU,CAACxD,CAAC,CAAC,CAAC,EAAE;QAC7BrB,KAAK,EAAE;MACX;IACJ;IACA,OAAOA,KAAK;EAChB;EACA,MAAMgF,gBAAgB,GAAGF,eAAe,CAACb,KAAK,CAACtE,MAAM,GAAGuE,KAAK,CAACvE,MAAM,GAAGsE,KAAK,GAAGC,KAAK,CAAC;EACrF,MAAMe,CAAC,GAAGX,uBAAuB,GAAGU,gBAAgB,GAAG,GAAG,IAAIA,gBAAgB,GAAG,EAAE;EACnF,OAAOC,CAAC;AACZ;AACA,SAASjG,yBAAyBA,CAACV,KAAK,EAAE;EACtC,IAAIA,KAAK,CAACqB,MAAM,KAAK,CAAC,EAAE;IACpB,OAAOrB,KAAK;EAChB;EACAA,KAAK,CAACoD,IAAI,CAACxE,SAAS,CAACiF,CAAC,IAAIA,CAAC,CAAChD,QAAQ,CAACmC,eAAe,EAAEnE,gBAAgB,CAAC,CAAC;EACxE,MAAMiH,MAAM,GAAG,CAAC9F,KAAK,CAAC,CAAC,CAAC,CAAC;EACzB,KAAK,IAAI+C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG/C,KAAK,CAACqB,MAAM,EAAE0B,CAAC,EAAE,EAAE;IACnC,MAAM6D,IAAI,GAAGd,MAAM,CAACA,MAAM,CAACzE,MAAM,GAAG,CAAC,CAAC;IACtC,MAAMV,OAAO,GAAGX,KAAK,CAAC+C,CAAC,CAAC;IACxB,MAAM8D,YAAY,GAAGlG,OAAO,CAACE,QAAQ,CAACmC,eAAe,GAAG4D,IAAI,CAAC/F,QAAQ,CAACoC,sBAAsB;IAC5F,MAAM6D,YAAY,GAAGnG,OAAO,CAACkB,QAAQ,CAACmB,eAAe,GAAG4D,IAAI,CAAC/E,QAAQ,CAACoB,sBAAsB;IAC5F,MAAM8D,oBAAoB,GAAGF,YAAY,IAAI,CAAC,IAAIC,YAAY,IAAI,CAAC;IACnE,IAAIC,oBAAoB,IAAIF,YAAY,GAAGC,YAAY,IAAI,CAAC,EAAE;MAC1DhB,MAAM,CAACA,MAAM,CAACzE,MAAM,GAAG,CAAC,CAAC,GAAGuF,IAAI,CAACxF,IAAI,CAACT,OAAO,CAAC;MAC9C;IACJ;IACAmF,MAAM,CAACrD,IAAI,CAAC9B,OAAO,CAAC;EACxB;EACA,OAAOmF,MAAM;AACjB;AACA,SAASvE,qBAAqBA,CAAC7B,OAAO,EAAEM,KAAK,EAAE;EAC3C,MAAMgH,iBAAiB,GAAG,IAAIjI,eAAe,CAACW,OAAO,CAAC;EACtDM,KAAK,GAAGA,KAAK,CAACK,MAAM,CAACwD,CAAC,IAAI;IACtB,MAAMoD,2BAA2B,GAAGD,iBAAiB,CAAChI,kBAAkB,CAACsB,CAAC,IAAIA,CAAC,CAACO,QAAQ,CAACmC,eAAe,GAAGa,CAAC,CAAChD,QAAQ,CAACoC,sBAAsB,CAAC,IACtI,IAAIvE,gBAAgB,CAAC,IAAIQ,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,IAAIA,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACrE,MAAMgI,2BAA2B,GAAGlI,kBAAkB,CAACU,OAAO,EAAEY,CAAC,IAAIA,CAAC,CAACuB,QAAQ,CAACmB,eAAe,GAAGa,CAAC,CAAChC,QAAQ,CAACoB,sBAAsB,CAAC;IACpI,MAAMkE,cAAc,GAAGF,2BAA2B,KAAKC,2BAA2B;IAClF,OAAOC,cAAc;EACzB,CAAC,CAAC;EACF,OAAOnH,KAAK;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}