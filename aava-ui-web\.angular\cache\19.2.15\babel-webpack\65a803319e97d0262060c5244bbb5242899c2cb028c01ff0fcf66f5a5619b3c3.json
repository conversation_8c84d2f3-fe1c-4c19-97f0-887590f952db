{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport function diffSets(before, after) {\n  const removed = [];\n  const added = [];\n  for (const element of before) {\n    if (!after.has(element)) {\n      removed.push(element);\n    }\n  }\n  for (const element of after) {\n    if (!before.has(element)) {\n      added.push(element);\n    }\n  }\n  return {\n    removed,\n    added\n  };\n}\n/**\n * Computes the intersection of two sets.\n *\n * @param setA - The first set.\n * @param setB - The second iterable.\n * @returns A new set containing the elements that are in both `setA` and `setB`.\n */\nexport function intersection(setA, setB) {\n  const result = new Set();\n  for (const elem of setB) {\n    if (setA.has(elem)) {\n      result.add(elem);\n    }\n  }\n  return result;\n}", "map": {"version": 3, "names": ["diffSets", "before", "after", "removed", "added", "element", "has", "push", "intersection", "setA", "setB", "result", "Set", "elem", "add"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/common/collections.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport function diffSets(before, after) {\n    const removed = [];\n    const added = [];\n    for (const element of before) {\n        if (!after.has(element)) {\n            removed.push(element);\n        }\n    }\n    for (const element of after) {\n        if (!before.has(element)) {\n            added.push(element);\n        }\n    }\n    return { removed, added };\n}\n/**\n * Computes the intersection of two sets.\n *\n * @param setA - The first set.\n * @param setB - The second iterable.\n * @returns A new set containing the elements that are in both `setA` and `setB`.\n */\nexport function intersection(setA, setB) {\n    const result = new Set();\n    for (const elem of setB) {\n        if (setA.has(elem)) {\n            result.add(elem);\n        }\n    }\n    return result;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,SAASA,QAAQA,CAACC,MAAM,EAAEC,KAAK,EAAE;EACpC,MAAMC,OAAO,GAAG,EAAE;EAClB,MAAMC,KAAK,GAAG,EAAE;EAChB,KAAK,MAAMC,OAAO,IAAIJ,MAAM,EAAE;IAC1B,IAAI,CAACC,KAAK,CAACI,GAAG,CAACD,OAAO,CAAC,EAAE;MACrBF,OAAO,CAACI,IAAI,CAACF,OAAO,CAAC;IACzB;EACJ;EACA,KAAK,MAAMA,OAAO,IAAIH,KAAK,EAAE;IACzB,IAAI,CAACD,MAAM,CAACK,GAAG,CAACD,OAAO,CAAC,EAAE;MACtBD,KAAK,CAACG,IAAI,CAACF,OAAO,CAAC;IACvB;EACJ;EACA,OAAO;IAAEF,OAAO;IAAEC;EAAM,CAAC;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASI,YAAYA,CAACC,IAAI,EAAEC,IAAI,EAAE;EACrC,MAAMC,MAAM,GAAG,IAAIC,GAAG,CAAC,CAAC;EACxB,KAAK,MAAMC,IAAI,IAAIH,IAAI,EAAE;IACrB,IAAID,IAAI,CAACH,GAAG,CAACO,IAAI,CAAC,EAAE;MAChBF,MAAM,CAACG,GAAG,CAACD,IAAI,CAAC;IACpB;EACJ;EACA,OAAOF,MAAM;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}