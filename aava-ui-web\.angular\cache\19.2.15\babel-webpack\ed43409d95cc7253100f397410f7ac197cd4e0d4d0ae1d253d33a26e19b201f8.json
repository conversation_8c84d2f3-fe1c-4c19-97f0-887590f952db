{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { $, append } from '../../dom.js';\nimport { format } from '../../../common/strings.js';\nimport './countBadge.css';\nexport class CountBadge {\n  constructor(container, options, styles) {\n    this.options = options;\n    this.styles = styles;\n    this.count = 0;\n    this.element = append(container, $('.monaco-count-badge'));\n    this.countFormat = this.options.countFormat || '{0}';\n    this.titleFormat = this.options.titleFormat || '';\n    this.setCount(this.options.count || 0);\n  }\n  setCount(count) {\n    this.count = count;\n    this.render();\n  }\n  setTitleFormat(titleFormat) {\n    this.titleFormat = titleFormat;\n    this.render();\n  }\n  render() {\n    this.element.textContent = format(this.countFormat, this.count);\n    this.element.title = format(this.titleFormat, this.count);\n    this.element.style.backgroundColor = this.styles.badgeBackground ?? '';\n    this.element.style.color = this.styles.badgeForeground ?? '';\n    if (this.styles.badgeBorder) {\n      this.element.style.border = `1px solid ${this.styles.badgeBorder}`;\n    }\n  }\n}", "map": {"version": 3, "names": ["$", "append", "format", "Count<PERSON>adge", "constructor", "container", "options", "styles", "count", "element", "countFormat", "titleFormat", "setCount", "render", "setTitleFormat", "textContent", "title", "style", "backgroundColor", "badgeBackground", "color", "badgeForeground", "badgeBorder", "border"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/browser/ui/countBadge/countBadge.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { $, append } from '../../dom.js';\nimport { format } from '../../../common/strings.js';\nimport './countBadge.css';\nexport class CountBadge {\n    constructor(container, options, styles) {\n        this.options = options;\n        this.styles = styles;\n        this.count = 0;\n        this.element = append(container, $('.monaco-count-badge'));\n        this.countFormat = this.options.countFormat || '{0}';\n        this.titleFormat = this.options.titleFormat || '';\n        this.setCount(this.options.count || 0);\n    }\n    setCount(count) {\n        this.count = count;\n        this.render();\n    }\n    setTitleFormat(titleFormat) {\n        this.titleFormat = titleFormat;\n        this.render();\n    }\n    render() {\n        this.element.textContent = format(this.countFormat, this.count);\n        this.element.title = format(this.titleFormat, this.count);\n        this.element.style.backgroundColor = this.styles.badgeBackground ?? '';\n        this.element.style.color = this.styles.badgeForeground ?? '';\n        if (this.styles.badgeBorder) {\n            this.element.style.border = `1px solid ${this.styles.badgeBorder}`;\n        }\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,CAAC,EAAEC,MAAM,QAAQ,cAAc;AACxC,SAASC,MAAM,QAAQ,4BAA4B;AACnD,OAAO,kBAAkB;AACzB,OAAO,MAAMC,UAAU,CAAC;EACpBC,WAAWA,CAACC,SAAS,EAAEC,OAAO,EAAEC,MAAM,EAAE;IACpC,IAAI,CAACD,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,KAAK,GAAG,CAAC;IACd,IAAI,CAACC,OAAO,GAAGR,MAAM,CAACI,SAAS,EAAEL,CAAC,CAAC,qBAAqB,CAAC,CAAC;IAC1D,IAAI,CAACU,WAAW,GAAG,IAAI,CAACJ,OAAO,CAACI,WAAW,IAAI,KAAK;IACpD,IAAI,CAACC,WAAW,GAAG,IAAI,CAACL,OAAO,CAACK,WAAW,IAAI,EAAE;IACjD,IAAI,CAACC,QAAQ,CAAC,IAAI,CAACN,OAAO,CAACE,KAAK,IAAI,CAAC,CAAC;EAC1C;EACAI,QAAQA,CAACJ,KAAK,EAAE;IACZ,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACK,MAAM,CAAC,CAAC;EACjB;EACAC,cAAcA,CAACH,WAAW,EAAE;IACxB,IAAI,CAACA,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACE,MAAM,CAAC,CAAC;EACjB;EACAA,MAAMA,CAAA,EAAG;IACL,IAAI,CAACJ,OAAO,CAACM,WAAW,GAAGb,MAAM,CAAC,IAAI,CAACQ,WAAW,EAAE,IAAI,CAACF,KAAK,CAAC;IAC/D,IAAI,CAACC,OAAO,CAACO,KAAK,GAAGd,MAAM,CAAC,IAAI,CAACS,WAAW,EAAE,IAAI,CAACH,KAAK,CAAC;IACzD,IAAI,CAACC,OAAO,CAACQ,KAAK,CAACC,eAAe,GAAG,IAAI,CAACX,MAAM,CAACY,eAAe,IAAI,EAAE;IACtE,IAAI,CAACV,OAAO,CAACQ,KAAK,CAACG,KAAK,GAAG,IAAI,CAACb,MAAM,CAACc,eAAe,IAAI,EAAE;IAC5D,IAAI,IAAI,CAACd,MAAM,CAACe,WAAW,EAAE;MACzB,IAAI,CAACb,OAAO,CAACQ,KAAK,CAACM,MAAM,GAAG,aAAa,IAAI,CAAChB,MAAM,CAACe,WAAW,EAAE;IACtE;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}