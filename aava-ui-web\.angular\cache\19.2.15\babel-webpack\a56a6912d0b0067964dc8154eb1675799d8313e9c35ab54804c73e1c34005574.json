{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { posix, sep } from './path.js';\nimport { isWindows } from './platform.js';\nimport { startsWithIgnoreCase } from './strings.js';\nexport function isPathSeparator(code) {\n  return code === 47 /* CharCode.Slash */ || code === 92 /* CharCode.Backslash */;\n}\n/**\n * Takes a Windows OS path and changes backward slashes to forward slashes.\n * This should only be done for OS paths from Windows (or user provided paths potentially from Windows).\n * Using it on a Linux or MaxOS path might change it.\n */\nexport function toSlashes(osPath) {\n  return osPath.replace(/[\\\\/]/g, posix.sep);\n}\n/**\n * Takes a Windows OS path (using backward or forward slashes) and turns it into a posix path:\n * - turns backward slashes into forward slashes\n * - makes it absolute if it starts with a drive letter\n * This should only be done for OS paths from Windows (or user provided paths potentially from Windows).\n * Using it on a Linux or MaxOS path might change it.\n */\nexport function toPosixPath(osPath) {\n  if (osPath.indexOf('/') === -1) {\n    osPath = toSlashes(osPath);\n  }\n  if (/^[a-zA-Z]:(\\/|$)/.test(osPath)) {\n    // starts with a drive letter\n    osPath = '/' + osPath;\n  }\n  return osPath;\n}\n/**\n * Computes the _root_ this path, like `getRoot('c:\\files') === c:\\`,\n * `getRoot('files:///files/path') === files:///`,\n * or `getRoot('\\\\server\\shares\\path') === \\\\server\\shares\\`\n */\nexport function getRoot(path, sep = posix.sep) {\n  if (!path) {\n    return '';\n  }\n  const len = path.length;\n  const firstLetter = path.charCodeAt(0);\n  if (isPathSeparator(firstLetter)) {\n    if (isPathSeparator(path.charCodeAt(1))) {\n      // UNC candidate \\\\localhost\\shares\\ddd\n      //               ^^^^^^^^^^^^^^^^^^^\n      if (!isPathSeparator(path.charCodeAt(2))) {\n        let pos = 3;\n        const start = pos;\n        for (; pos < len; pos++) {\n          if (isPathSeparator(path.charCodeAt(pos))) {\n            break;\n          }\n        }\n        if (start !== pos && !isPathSeparator(path.charCodeAt(pos + 1))) {\n          pos += 1;\n          for (; pos < len; pos++) {\n            if (isPathSeparator(path.charCodeAt(pos))) {\n              return path.slice(0, pos + 1) // consume this separator\n              .replace(/[\\\\/]/g, sep);\n            }\n          }\n        }\n      }\n    }\n    // /user/far\n    // ^\n    return sep;\n  } else if (isWindowsDriveLetter(firstLetter)) {\n    // check for windows drive letter c:\\ or c:\n    if (path.charCodeAt(1) === 58 /* CharCode.Colon */) {\n      if (isPathSeparator(path.charCodeAt(2))) {\n        // C:\\fff\n        // ^^^\n        return path.slice(0, 2) + sep;\n      } else {\n        // C:\n        // ^^\n        return path.slice(0, 2);\n      }\n    }\n  }\n  // check for URI\n  // scheme://authority/path\n  // ^^^^^^^^^^^^^^^^^^^\n  let pos = path.indexOf('://');\n  if (pos !== -1) {\n    pos += 3; // 3 -> \"://\".length\n    for (; pos < len; pos++) {\n      if (isPathSeparator(path.charCodeAt(pos))) {\n        return path.slice(0, pos + 1); // consume this separator\n      }\n    }\n  }\n  return '';\n}\n/**\n * @deprecated please use `IUriIdentityService.extUri.isEqualOrParent` instead. If\n * you are in a context without services, consider to pass down the `extUri` from the\n * outside, or use `extUriBiasedIgnorePathCase` if you know what you are doing.\n */\nexport function isEqualOrParent(base, parentCandidate, ignoreCase, separator = sep) {\n  if (base === parentCandidate) {\n    return true;\n  }\n  if (!base || !parentCandidate) {\n    return false;\n  }\n  if (parentCandidate.length > base.length) {\n    return false;\n  }\n  if (ignoreCase) {\n    const beginsWith = startsWithIgnoreCase(base, parentCandidate);\n    if (!beginsWith) {\n      return false;\n    }\n    if (parentCandidate.length === base.length) {\n      return true; // same path, different casing\n    }\n    let sepOffset = parentCandidate.length;\n    if (parentCandidate.charAt(parentCandidate.length - 1) === separator) {\n      sepOffset--; // adjust the expected sep offset in case our candidate already ends in separator character\n    }\n    return base.charAt(sepOffset) === separator;\n  }\n  if (parentCandidate.charAt(parentCandidate.length - 1) !== separator) {\n    parentCandidate += separator;\n  }\n  return base.indexOf(parentCandidate) === 0;\n}\nexport function isWindowsDriveLetter(char0) {\n  return char0 >= 65 /* CharCode.A */ && char0 <= 90 /* CharCode.Z */ || char0 >= 97 /* CharCode.a */ && char0 <= 122 /* CharCode.z */;\n}\nexport function hasDriveLetter(path, isWindowsOS = isWindows) {\n  if (isWindowsOS) {\n    return isWindowsDriveLetter(path.charCodeAt(0)) && path.charCodeAt(1) === 58 /* CharCode.Colon */;\n  }\n  return false;\n}", "map": {"version": 3, "names": ["posix", "sep", "isWindows", "startsWithIgnoreCase", "isPathSeparator", "code", "toSlashes", "osPath", "replace", "toPosixPath", "indexOf", "test", "getRoot", "path", "len", "length", "firstLetter", "charCodeAt", "pos", "start", "slice", "isWindowsDriveLetter", "isEqualOrParent", "base", "parentCandidate", "ignoreCase", "separator", "beginsWith", "sepOffset", "char<PERSON>t", "char0", "hasDriveLetter", "isWindowsOS"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/common/extpath.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { posix, sep } from './path.js';\nimport { isWindows } from './platform.js';\nimport { startsWithIgnoreCase } from './strings.js';\nexport function isPathSeparator(code) {\n    return code === 47 /* CharCode.Slash */ || code === 92 /* CharCode.Backslash */;\n}\n/**\n * Takes a Windows OS path and changes backward slashes to forward slashes.\n * This should only be done for OS paths from Windows (or user provided paths potentially from Windows).\n * Using it on a Linux or MaxOS path might change it.\n */\nexport function toSlashes(osPath) {\n    return osPath.replace(/[\\\\/]/g, posix.sep);\n}\n/**\n * Takes a Windows OS path (using backward or forward slashes) and turns it into a posix path:\n * - turns backward slashes into forward slashes\n * - makes it absolute if it starts with a drive letter\n * This should only be done for OS paths from Windows (or user provided paths potentially from Windows).\n * Using it on a Linux or MaxOS path might change it.\n */\nexport function toPosixPath(osPath) {\n    if (osPath.indexOf('/') === -1) {\n        osPath = toSlashes(osPath);\n    }\n    if (/^[a-zA-Z]:(\\/|$)/.test(osPath)) { // starts with a drive letter\n        osPath = '/' + osPath;\n    }\n    return osPath;\n}\n/**\n * Computes the _root_ this path, like `getRoot('c:\\files') === c:\\`,\n * `getRoot('files:///files/path') === files:///`,\n * or `getRoot('\\\\server\\shares\\path') === \\\\server\\shares\\`\n */\nexport function getRoot(path, sep = posix.sep) {\n    if (!path) {\n        return '';\n    }\n    const len = path.length;\n    const firstLetter = path.charCodeAt(0);\n    if (isPathSeparator(firstLetter)) {\n        if (isPathSeparator(path.charCodeAt(1))) {\n            // UNC candidate \\\\localhost\\shares\\ddd\n            //               ^^^^^^^^^^^^^^^^^^^\n            if (!isPathSeparator(path.charCodeAt(2))) {\n                let pos = 3;\n                const start = pos;\n                for (; pos < len; pos++) {\n                    if (isPathSeparator(path.charCodeAt(pos))) {\n                        break;\n                    }\n                }\n                if (start !== pos && !isPathSeparator(path.charCodeAt(pos + 1))) {\n                    pos += 1;\n                    for (; pos < len; pos++) {\n                        if (isPathSeparator(path.charCodeAt(pos))) {\n                            return path.slice(0, pos + 1) // consume this separator\n                                .replace(/[\\\\/]/g, sep);\n                        }\n                    }\n                }\n            }\n        }\n        // /user/far\n        // ^\n        return sep;\n    }\n    else if (isWindowsDriveLetter(firstLetter)) {\n        // check for windows drive letter c:\\ or c:\n        if (path.charCodeAt(1) === 58 /* CharCode.Colon */) {\n            if (isPathSeparator(path.charCodeAt(2))) {\n                // C:\\fff\n                // ^^^\n                return path.slice(0, 2) + sep;\n            }\n            else {\n                // C:\n                // ^^\n                return path.slice(0, 2);\n            }\n        }\n    }\n    // check for URI\n    // scheme://authority/path\n    // ^^^^^^^^^^^^^^^^^^^\n    let pos = path.indexOf('://');\n    if (pos !== -1) {\n        pos += 3; // 3 -> \"://\".length\n        for (; pos < len; pos++) {\n            if (isPathSeparator(path.charCodeAt(pos))) {\n                return path.slice(0, pos + 1); // consume this separator\n            }\n        }\n    }\n    return '';\n}\n/**\n * @deprecated please use `IUriIdentityService.extUri.isEqualOrParent` instead. If\n * you are in a context without services, consider to pass down the `extUri` from the\n * outside, or use `extUriBiasedIgnorePathCase` if you know what you are doing.\n */\nexport function isEqualOrParent(base, parentCandidate, ignoreCase, separator = sep) {\n    if (base === parentCandidate) {\n        return true;\n    }\n    if (!base || !parentCandidate) {\n        return false;\n    }\n    if (parentCandidate.length > base.length) {\n        return false;\n    }\n    if (ignoreCase) {\n        const beginsWith = startsWithIgnoreCase(base, parentCandidate);\n        if (!beginsWith) {\n            return false;\n        }\n        if (parentCandidate.length === base.length) {\n            return true; // same path, different casing\n        }\n        let sepOffset = parentCandidate.length;\n        if (parentCandidate.charAt(parentCandidate.length - 1) === separator) {\n            sepOffset--; // adjust the expected sep offset in case our candidate already ends in separator character\n        }\n        return base.charAt(sepOffset) === separator;\n    }\n    if (parentCandidate.charAt(parentCandidate.length - 1) !== separator) {\n        parentCandidate += separator;\n    }\n    return base.indexOf(parentCandidate) === 0;\n}\nexport function isWindowsDriveLetter(char0) {\n    return char0 >= 65 /* CharCode.A */ && char0 <= 90 /* CharCode.Z */ || char0 >= 97 /* CharCode.a */ && char0 <= 122 /* CharCode.z */;\n}\nexport function hasDriveLetter(path, isWindowsOS = isWindows) {\n    if (isWindowsOS) {\n        return isWindowsDriveLetter(path.charCodeAt(0)) && path.charCodeAt(1) === 58 /* CharCode.Colon */;\n    }\n    return false;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,KAAK,EAAEC,GAAG,QAAQ,WAAW;AACtC,SAASC,SAAS,QAAQ,eAAe;AACzC,SAASC,oBAAoB,QAAQ,cAAc;AACnD,OAAO,SAASC,eAAeA,CAACC,IAAI,EAAE;EAClC,OAAOA,IAAI,KAAK,EAAE,CAAC,wBAAwBA,IAAI,KAAK,EAAE,CAAC;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,SAASA,CAACC,MAAM,EAAE;EAC9B,OAAOA,MAAM,CAACC,OAAO,CAAC,QAAQ,EAAER,KAAK,CAACC,GAAG,CAAC;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASQ,WAAWA,CAACF,MAAM,EAAE;EAChC,IAAIA,MAAM,CAACG,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;IAC5BH,MAAM,GAAGD,SAAS,CAACC,MAAM,CAAC;EAC9B;EACA,IAAI,kBAAkB,CAACI,IAAI,CAACJ,MAAM,CAAC,EAAE;IAAE;IACnCA,MAAM,GAAG,GAAG,GAAGA,MAAM;EACzB;EACA,OAAOA,MAAM;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASK,OAAOA,CAACC,IAAI,EAAEZ,GAAG,GAAGD,KAAK,CAACC,GAAG,EAAE;EAC3C,IAAI,CAACY,IAAI,EAAE;IACP,OAAO,EAAE;EACb;EACA,MAAMC,GAAG,GAAGD,IAAI,CAACE,MAAM;EACvB,MAAMC,WAAW,GAAGH,IAAI,CAACI,UAAU,CAAC,CAAC,CAAC;EACtC,IAAIb,eAAe,CAACY,WAAW,CAAC,EAAE;IAC9B,IAAIZ,eAAe,CAACS,IAAI,CAACI,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE;MACrC;MACA;MACA,IAAI,CAACb,eAAe,CAACS,IAAI,CAACI,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE;QACtC,IAAIC,GAAG,GAAG,CAAC;QACX,MAAMC,KAAK,GAAGD,GAAG;QACjB,OAAOA,GAAG,GAAGJ,GAAG,EAAEI,GAAG,EAAE,EAAE;UACrB,IAAId,eAAe,CAACS,IAAI,CAACI,UAAU,CAACC,GAAG,CAAC,CAAC,EAAE;YACvC;UACJ;QACJ;QACA,IAAIC,KAAK,KAAKD,GAAG,IAAI,CAACd,eAAe,CAACS,IAAI,CAACI,UAAU,CAACC,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE;UAC7DA,GAAG,IAAI,CAAC;UACR,OAAOA,GAAG,GAAGJ,GAAG,EAAEI,GAAG,EAAE,EAAE;YACrB,IAAId,eAAe,CAACS,IAAI,CAACI,UAAU,CAACC,GAAG,CAAC,CAAC,EAAE;cACvC,OAAOL,IAAI,CAACO,KAAK,CAAC,CAAC,EAAEF,GAAG,GAAG,CAAC,CAAC,CAAC;cAAA,CACzBV,OAAO,CAAC,QAAQ,EAAEP,GAAG,CAAC;YAC/B;UACJ;QACJ;MACJ;IACJ;IACA;IACA;IACA,OAAOA,GAAG;EACd,CAAC,MACI,IAAIoB,oBAAoB,CAACL,WAAW,CAAC,EAAE;IACxC;IACA,IAAIH,IAAI,CAACI,UAAU,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,sBAAsB;MAChD,IAAIb,eAAe,CAACS,IAAI,CAACI,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE;QACrC;QACA;QACA,OAAOJ,IAAI,CAACO,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGnB,GAAG;MACjC,CAAC,MACI;QACD;QACA;QACA,OAAOY,IAAI,CAACO,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;MAC3B;IACJ;EACJ;EACA;EACA;EACA;EACA,IAAIF,GAAG,GAAGL,IAAI,CAACH,OAAO,CAAC,KAAK,CAAC;EAC7B,IAAIQ,GAAG,KAAK,CAAC,CAAC,EAAE;IACZA,GAAG,IAAI,CAAC,CAAC,CAAC;IACV,OAAOA,GAAG,GAAGJ,GAAG,EAAEI,GAAG,EAAE,EAAE;MACrB,IAAId,eAAe,CAACS,IAAI,CAACI,UAAU,CAACC,GAAG,CAAC,CAAC,EAAE;QACvC,OAAOL,IAAI,CAACO,KAAK,CAAC,CAAC,EAAEF,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;MACnC;IACJ;EACJ;EACA,OAAO,EAAE;AACb;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASI,eAAeA,CAACC,IAAI,EAAEC,eAAe,EAAEC,UAAU,EAAEC,SAAS,GAAGzB,GAAG,EAAE;EAChF,IAAIsB,IAAI,KAAKC,eAAe,EAAE;IAC1B,OAAO,IAAI;EACf;EACA,IAAI,CAACD,IAAI,IAAI,CAACC,eAAe,EAAE;IAC3B,OAAO,KAAK;EAChB;EACA,IAAIA,eAAe,CAACT,MAAM,GAAGQ,IAAI,CAACR,MAAM,EAAE;IACtC,OAAO,KAAK;EAChB;EACA,IAAIU,UAAU,EAAE;IACZ,MAAME,UAAU,GAAGxB,oBAAoB,CAACoB,IAAI,EAAEC,eAAe,CAAC;IAC9D,IAAI,CAACG,UAAU,EAAE;MACb,OAAO,KAAK;IAChB;IACA,IAAIH,eAAe,CAACT,MAAM,KAAKQ,IAAI,CAACR,MAAM,EAAE;MACxC,OAAO,IAAI,CAAC,CAAC;IACjB;IACA,IAAIa,SAAS,GAAGJ,eAAe,CAACT,MAAM;IACtC,IAAIS,eAAe,CAACK,MAAM,CAACL,eAAe,CAACT,MAAM,GAAG,CAAC,CAAC,KAAKW,SAAS,EAAE;MAClEE,SAAS,EAAE,CAAC,CAAC;IACjB;IACA,OAAOL,IAAI,CAACM,MAAM,CAACD,SAAS,CAAC,KAAKF,SAAS;EAC/C;EACA,IAAIF,eAAe,CAACK,MAAM,CAACL,eAAe,CAACT,MAAM,GAAG,CAAC,CAAC,KAAKW,SAAS,EAAE;IAClEF,eAAe,IAAIE,SAAS;EAChC;EACA,OAAOH,IAAI,CAACb,OAAO,CAACc,eAAe,CAAC,KAAK,CAAC;AAC9C;AACA,OAAO,SAASH,oBAAoBA,CAACS,KAAK,EAAE;EACxC,OAAOA,KAAK,IAAI,EAAE,CAAC,oBAAoBA,KAAK,IAAI,EAAE,CAAC,oBAAoBA,KAAK,IAAI,EAAE,CAAC,oBAAoBA,KAAK,IAAI,GAAG,CAAC;AACxH;AACA,OAAO,SAASC,cAAcA,CAAClB,IAAI,EAAEmB,WAAW,GAAG9B,SAAS,EAAE;EAC1D,IAAI8B,WAAW,EAAE;IACb,OAAOX,oBAAoB,CAACR,IAAI,CAACI,UAAU,CAAC,CAAC,CAAC,CAAC,IAAIJ,IAAI,CAACI,UAAU,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;EACjF;EACA,OAAO,KAAK;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}