{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { LineTokens } from '../tokens/lineTokens.js';\nimport { Position } from '../core/position.js';\nimport { LineInjectedText } from '../textModelEvents.js';\nimport { SingleLineInlineDecoration, ViewLineData } from '../viewModel.js';\nexport function createModelLineProjection(lineBreakData, isVisible) {\n  if (lineBreakData === null) {\n    // No mapping needed\n    if (isVisible) {\n      return IdentityModelLineProjection.INSTANCE;\n    }\n    return HiddenModelLineProjection.INSTANCE;\n  } else {\n    return new ModelLineProjection(lineBreakData, isVisible);\n  }\n}\n/**\n * This projection is used to\n * * wrap model lines\n * * inject text\n */\nclass ModelLineProjection {\n  constructor(lineBreakData, isVisible) {\n    this._projectionData = lineBreakData;\n    this._isVisible = isVisible;\n  }\n  isVisible() {\n    return this._isVisible;\n  }\n  setVisible(isVisible) {\n    this._isVisible = isVisible;\n    return this;\n  }\n  getProjectionData() {\n    return this._projectionData;\n  }\n  getViewLineCount() {\n    if (!this._isVisible) {\n      return 0;\n    }\n    return this._projectionData.getOutputLineCount();\n  }\n  getViewLineContent(model, modelLineNumber, outputLineIndex) {\n    this._assertVisible();\n    const startOffsetInInputWithInjections = outputLineIndex > 0 ? this._projectionData.breakOffsets[outputLineIndex - 1] : 0;\n    const endOffsetInInputWithInjections = this._projectionData.breakOffsets[outputLineIndex];\n    let r;\n    if (this._projectionData.injectionOffsets !== null) {\n      const injectedTexts = this._projectionData.injectionOffsets.map((offset, idx) => new LineInjectedText(0, 0, offset + 1, this._projectionData.injectionOptions[idx], 0));\n      const lineWithInjections = LineInjectedText.applyInjectedText(model.getLineContent(modelLineNumber), injectedTexts);\n      r = lineWithInjections.substring(startOffsetInInputWithInjections, endOffsetInInputWithInjections);\n    } else {\n      r = model.getValueInRange({\n        startLineNumber: modelLineNumber,\n        startColumn: startOffsetInInputWithInjections + 1,\n        endLineNumber: modelLineNumber,\n        endColumn: endOffsetInInputWithInjections + 1\n      });\n    }\n    if (outputLineIndex > 0) {\n      r = spaces(this._projectionData.wrappedTextIndentLength) + r;\n    }\n    return r;\n  }\n  getViewLineLength(model, modelLineNumber, outputLineIndex) {\n    this._assertVisible();\n    return this._projectionData.getLineLength(outputLineIndex);\n  }\n  getViewLineMinColumn(_model, _modelLineNumber, outputLineIndex) {\n    this._assertVisible();\n    return this._projectionData.getMinOutputOffset(outputLineIndex) + 1;\n  }\n  getViewLineMaxColumn(model, modelLineNumber, outputLineIndex) {\n    this._assertVisible();\n    return this._projectionData.getMaxOutputOffset(outputLineIndex) + 1;\n  }\n  /**\n   * Try using {@link getViewLinesData} instead.\n  */\n  getViewLineData(model, modelLineNumber, outputLineIndex) {\n    const arr = new Array();\n    this.getViewLinesData(model, modelLineNumber, outputLineIndex, 1, 0, [true], arr);\n    return arr[0];\n  }\n  getViewLinesData(model, modelLineNumber, outputLineIdx, lineCount, globalStartIndex, needed, result) {\n    this._assertVisible();\n    const lineBreakData = this._projectionData;\n    const injectionOffsets = lineBreakData.injectionOffsets;\n    const injectionOptions = lineBreakData.injectionOptions;\n    let inlineDecorationsPerOutputLine = null;\n    if (injectionOffsets) {\n      inlineDecorationsPerOutputLine = [];\n      let totalInjectedTextLengthBefore = 0;\n      let currentInjectedOffset = 0;\n      for (let outputLineIndex = 0; outputLineIndex < lineBreakData.getOutputLineCount(); outputLineIndex++) {\n        const inlineDecorations = new Array();\n        inlineDecorationsPerOutputLine[outputLineIndex] = inlineDecorations;\n        const lineStartOffsetInInputWithInjections = outputLineIndex > 0 ? lineBreakData.breakOffsets[outputLineIndex - 1] : 0;\n        const lineEndOffsetInInputWithInjections = lineBreakData.breakOffsets[outputLineIndex];\n        while (currentInjectedOffset < injectionOffsets.length) {\n          const length = injectionOptions[currentInjectedOffset].content.length;\n          const injectedTextStartOffsetInInputWithInjections = injectionOffsets[currentInjectedOffset] + totalInjectedTextLengthBefore;\n          const injectedTextEndOffsetInInputWithInjections = injectedTextStartOffsetInInputWithInjections + length;\n          if (injectedTextStartOffsetInInputWithInjections > lineEndOffsetInInputWithInjections) {\n            // Injected text only starts in later wrapped lines.\n            break;\n          }\n          if (lineStartOffsetInInputWithInjections < injectedTextEndOffsetInInputWithInjections) {\n            // Injected text ends after or in this line (but also starts in or before this line).\n            const options = injectionOptions[currentInjectedOffset];\n            if (options.inlineClassName) {\n              const offset = outputLineIndex > 0 ? lineBreakData.wrappedTextIndentLength : 0;\n              const start = offset + Math.max(injectedTextStartOffsetInInputWithInjections - lineStartOffsetInInputWithInjections, 0);\n              const end = offset + Math.min(injectedTextEndOffsetInInputWithInjections - lineStartOffsetInInputWithInjections, lineEndOffsetInInputWithInjections - lineStartOffsetInInputWithInjections);\n              if (start !== end) {\n                inlineDecorations.push(new SingleLineInlineDecoration(start, end, options.inlineClassName, options.inlineClassNameAffectsLetterSpacing));\n              }\n            }\n          }\n          if (injectedTextEndOffsetInInputWithInjections <= lineEndOffsetInInputWithInjections) {\n            totalInjectedTextLengthBefore += length;\n            currentInjectedOffset++;\n          } else {\n            // injected text breaks into next line, process it again\n            break;\n          }\n        }\n      }\n    }\n    let lineWithInjections;\n    if (injectionOffsets) {\n      lineWithInjections = model.tokenization.getLineTokens(modelLineNumber).withInserted(injectionOffsets.map((offset, idx) => ({\n        offset,\n        text: injectionOptions[idx].content,\n        tokenMetadata: LineTokens.defaultTokenMetadata\n      })));\n    } else {\n      lineWithInjections = model.tokenization.getLineTokens(modelLineNumber);\n    }\n    for (let outputLineIndex = outputLineIdx; outputLineIndex < outputLineIdx + lineCount; outputLineIndex++) {\n      const globalIndex = globalStartIndex + outputLineIndex - outputLineIdx;\n      if (!needed[globalIndex]) {\n        result[globalIndex] = null;\n        continue;\n      }\n      result[globalIndex] = this._getViewLineData(lineWithInjections, inlineDecorationsPerOutputLine ? inlineDecorationsPerOutputLine[outputLineIndex] : null, outputLineIndex);\n    }\n  }\n  _getViewLineData(lineWithInjections, inlineDecorations, outputLineIndex) {\n    this._assertVisible();\n    const lineBreakData = this._projectionData;\n    const deltaStartIndex = outputLineIndex > 0 ? lineBreakData.wrappedTextIndentLength : 0;\n    const lineStartOffsetInInputWithInjections = outputLineIndex > 0 ? lineBreakData.breakOffsets[outputLineIndex - 1] : 0;\n    const lineEndOffsetInInputWithInjections = lineBreakData.breakOffsets[outputLineIndex];\n    const tokens = lineWithInjections.sliceAndInflate(lineStartOffsetInInputWithInjections, lineEndOffsetInInputWithInjections, deltaStartIndex);\n    let lineContent = tokens.getLineContent();\n    if (outputLineIndex > 0) {\n      lineContent = spaces(lineBreakData.wrappedTextIndentLength) + lineContent;\n    }\n    const minColumn = this._projectionData.getMinOutputOffset(outputLineIndex) + 1;\n    const maxColumn = lineContent.length + 1;\n    const continuesWithWrappedLine = outputLineIndex + 1 < this.getViewLineCount();\n    const startVisibleColumn = outputLineIndex === 0 ? 0 : lineBreakData.breakOffsetsVisibleColumn[outputLineIndex - 1];\n    return new ViewLineData(lineContent, continuesWithWrappedLine, minColumn, maxColumn, startVisibleColumn, tokens, inlineDecorations);\n  }\n  getModelColumnOfViewPosition(outputLineIndex, outputColumn) {\n    this._assertVisible();\n    return this._projectionData.translateToInputOffset(outputLineIndex, outputColumn - 1) + 1;\n  }\n  getViewPositionOfModelPosition(deltaLineNumber, inputColumn, affinity = 2 /* PositionAffinity.None */) {\n    this._assertVisible();\n    const r = this._projectionData.translateToOutputPosition(inputColumn - 1, affinity);\n    return r.toPosition(deltaLineNumber);\n  }\n  getViewLineNumberOfModelPosition(deltaLineNumber, inputColumn) {\n    this._assertVisible();\n    const r = this._projectionData.translateToOutputPosition(inputColumn - 1);\n    return deltaLineNumber + r.outputLineIndex;\n  }\n  normalizePosition(outputLineIndex, outputPosition, affinity) {\n    const baseViewLineNumber = outputPosition.lineNumber - outputLineIndex;\n    const normalizedOutputPosition = this._projectionData.normalizeOutputPosition(outputLineIndex, outputPosition.column - 1, affinity);\n    const result = normalizedOutputPosition.toPosition(baseViewLineNumber);\n    return result;\n  }\n  getInjectedTextAt(outputLineIndex, outputColumn) {\n    return this._projectionData.getInjectedText(outputLineIndex, outputColumn - 1);\n  }\n  _assertVisible() {\n    if (!this._isVisible) {\n      throw new Error('Not supported');\n    }\n  }\n}\n/**\n * This projection does not change the model line.\n*/\nclass IdentityModelLineProjection {\n  static {\n    this.INSTANCE = new IdentityModelLineProjection();\n  }\n  constructor() {}\n  isVisible() {\n    return true;\n  }\n  setVisible(isVisible) {\n    if (isVisible) {\n      return this;\n    }\n    return HiddenModelLineProjection.INSTANCE;\n  }\n  getProjectionData() {\n    return null;\n  }\n  getViewLineCount() {\n    return 1;\n  }\n  getViewLineContent(model, modelLineNumber, _outputLineIndex) {\n    return model.getLineContent(modelLineNumber);\n  }\n  getViewLineLength(model, modelLineNumber, _outputLineIndex) {\n    return model.getLineLength(modelLineNumber);\n  }\n  getViewLineMinColumn(model, modelLineNumber, _outputLineIndex) {\n    return model.getLineMinColumn(modelLineNumber);\n  }\n  getViewLineMaxColumn(model, modelLineNumber, _outputLineIndex) {\n    return model.getLineMaxColumn(modelLineNumber);\n  }\n  getViewLineData(model, modelLineNumber, _outputLineIndex) {\n    const lineTokens = model.tokenization.getLineTokens(modelLineNumber);\n    const lineContent = lineTokens.getLineContent();\n    return new ViewLineData(lineContent, false, 1, lineContent.length + 1, 0, lineTokens.inflate(), null);\n  }\n  getViewLinesData(model, modelLineNumber, _fromOuputLineIndex, _toOutputLineIndex, globalStartIndex, needed, result) {\n    if (!needed[globalStartIndex]) {\n      result[globalStartIndex] = null;\n      return;\n    }\n    result[globalStartIndex] = this.getViewLineData(model, modelLineNumber, 0);\n  }\n  getModelColumnOfViewPosition(_outputLineIndex, outputColumn) {\n    return outputColumn;\n  }\n  getViewPositionOfModelPosition(deltaLineNumber, inputColumn) {\n    return new Position(deltaLineNumber, inputColumn);\n  }\n  getViewLineNumberOfModelPosition(deltaLineNumber, _inputColumn) {\n    return deltaLineNumber;\n  }\n  normalizePosition(outputLineIndex, outputPosition, affinity) {\n    return outputPosition;\n  }\n  getInjectedTextAt(_outputLineIndex, _outputColumn) {\n    return null;\n  }\n}\n/**\n * This projection hides the model line.\n */\nclass HiddenModelLineProjection {\n  static {\n    this.INSTANCE = new HiddenModelLineProjection();\n  }\n  constructor() {}\n  isVisible() {\n    return false;\n  }\n  setVisible(isVisible) {\n    if (!isVisible) {\n      return this;\n    }\n    return IdentityModelLineProjection.INSTANCE;\n  }\n  getProjectionData() {\n    return null;\n  }\n  getViewLineCount() {\n    return 0;\n  }\n  getViewLineContent(_model, _modelLineNumber, _outputLineIndex) {\n    throw new Error('Not supported');\n  }\n  getViewLineLength(_model, _modelLineNumber, _outputLineIndex) {\n    throw new Error('Not supported');\n  }\n  getViewLineMinColumn(_model, _modelLineNumber, _outputLineIndex) {\n    throw new Error('Not supported');\n  }\n  getViewLineMaxColumn(_model, _modelLineNumber, _outputLineIndex) {\n    throw new Error('Not supported');\n  }\n  getViewLineData(_model, _modelLineNumber, _outputLineIndex) {\n    throw new Error('Not supported');\n  }\n  getViewLinesData(_model, _modelLineNumber, _fromOuputLineIndex, _toOutputLineIndex, _globalStartIndex, _needed, _result) {\n    throw new Error('Not supported');\n  }\n  getModelColumnOfViewPosition(_outputLineIndex, _outputColumn) {\n    throw new Error('Not supported');\n  }\n  getViewPositionOfModelPosition(_deltaLineNumber, _inputColumn) {\n    throw new Error('Not supported');\n  }\n  getViewLineNumberOfModelPosition(_deltaLineNumber, _inputColumn) {\n    throw new Error('Not supported');\n  }\n  normalizePosition(outputLineIndex, outputPosition, affinity) {\n    throw new Error('Not supported');\n  }\n  getInjectedTextAt(_outputLineIndex, _outputColumn) {\n    throw new Error('Not supported');\n  }\n}\nconst _spaces = [''];\nfunction spaces(count) {\n  if (count >= _spaces.length) {\n    for (let i = 1; i <= count; i++) {\n      _spaces[i] = _makeSpaces(i);\n    }\n  }\n  return _spaces[count];\n}\nfunction _makeSpaces(count) {\n  return new Array(count + 1).join(' ');\n}", "map": {"version": 3, "names": ["LineTokens", "Position", "LineInjectedText", "SingleLineInlineDecoration", "ViewLineData", "createModelLineProjection", "lineBreakData", "isVisible", "IdentityModelLineProjection", "INSTANCE", "HiddenModelLineProjection", "ModelLineProjection", "constructor", "_projectionData", "_isVisible", "setVisible", "getProjectionData", "getViewLineCount", "getOutputLineCount", "getViewLineContent", "model", "modelLineNumber", "outputLineIndex", "_assertVisible", "startOffsetInInputWithInjections", "breakOffsets", "endOffsetInInputWithInjections", "r", "injectionOffsets", "injectedTexts", "map", "offset", "idx", "injectionOptions", "lineWithInjections", "applyInjectedText", "get<PERSON>ineC<PERSON>nt", "substring", "getValueInRange", "startLineNumber", "startColumn", "endLineNumber", "endColumn", "spaces", "wrappedTextIndentLength", "getViewLineLength", "getLine<PERSON><PERSON>th", "getViewLineMinColumn", "_model", "_modelLineNumber", "getMinOutputOffset", "getViewLineMaxColumn", "getMaxOutputOffset", "getViewLineData", "arr", "Array", "getViewLinesData", "outputLineIdx", "lineCount", "globalStartIndex", "needed", "result", "inlineDecorationsPerOutputLine", "totalInjectedTextLengthBefore", "currentInjectedOffset", "inlineDecorations", "lineStartOffsetInInputWithInjections", "lineEndOffsetInInputWithInjections", "length", "content", "injectedTextStartOffsetInInputWithInjections", "injectedTextEndOffsetInInputWithInjections", "options", "inlineClassName", "start", "Math", "max", "end", "min", "push", "inlineClassNameAffectsLetterSpacing", "tokenization", "getLineTokens", "withInserted", "text", "tokenMetadata", "defaultTokenMetadata", "globalIndex", "_getViewLineData", "deltaStartIndex", "tokens", "sliceAndInflate", "lineContent", "minColumn", "maxColumn", "continuesWithWrappedLine", "startVisibleColumn", "breakOffsetsVisibleColumn", "getModelColumnOfViewPosition", "outputColumn", "translateToInputOffset", "getViewPositionOfModelPosition", "deltaLineNumber", "inputColumn", "affinity", "translateToOutputPosition", "toPosition", "getViewLineNumberOfModelPosition", "normalizePosition", "outputPosition", "baseViewLineNumber", "lineNumber", "normalizedOutputPosition", "normalizeOutputPosition", "column", "getInjectedTextAt", "getInjectedText", "Error", "_outputLineIndex", "getLineMinColumn", "getLineMaxColumn", "lineTokens", "inflate", "_fromOuputLineIndex", "_toOutputLineIndex", "_inputColumn", "_outputColumn", "_globalStartIndex", "_needed", "_result", "_deltaLineNumber", "_spaces", "count", "i", "_makeSpaces", "join"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/viewModel/modelLineProjection.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { LineTokens } from '../tokens/lineTokens.js';\nimport { Position } from '../core/position.js';\nimport { LineInjectedText } from '../textModelEvents.js';\nimport { SingleLineInlineDecoration, ViewLineData } from '../viewModel.js';\nexport function createModelLineProjection(lineBreakData, isVisible) {\n    if (lineBreakData === null) {\n        // No mapping needed\n        if (isVisible) {\n            return IdentityModelLineProjection.INSTANCE;\n        }\n        return HiddenModelLineProjection.INSTANCE;\n    }\n    else {\n        return new ModelLineProjection(lineBreakData, isVisible);\n    }\n}\n/**\n * This projection is used to\n * * wrap model lines\n * * inject text\n */\nclass ModelLineProjection {\n    constructor(lineBreakData, isVisible) {\n        this._projectionData = lineBreakData;\n        this._isVisible = isVisible;\n    }\n    isVisible() {\n        return this._isVisible;\n    }\n    setVisible(isVisible) {\n        this._isVisible = isVisible;\n        return this;\n    }\n    getProjectionData() {\n        return this._projectionData;\n    }\n    getViewLineCount() {\n        if (!this._isVisible) {\n            return 0;\n        }\n        return this._projectionData.getOutputLineCount();\n    }\n    getViewLineContent(model, modelLineNumber, outputLineIndex) {\n        this._assertVisible();\n        const startOffsetInInputWithInjections = outputLineIndex > 0 ? this._projectionData.breakOffsets[outputLineIndex - 1] : 0;\n        const endOffsetInInputWithInjections = this._projectionData.breakOffsets[outputLineIndex];\n        let r;\n        if (this._projectionData.injectionOffsets !== null) {\n            const injectedTexts = this._projectionData.injectionOffsets.map((offset, idx) => new LineInjectedText(0, 0, offset + 1, this._projectionData.injectionOptions[idx], 0));\n            const lineWithInjections = LineInjectedText.applyInjectedText(model.getLineContent(modelLineNumber), injectedTexts);\n            r = lineWithInjections.substring(startOffsetInInputWithInjections, endOffsetInInputWithInjections);\n        }\n        else {\n            r = model.getValueInRange({\n                startLineNumber: modelLineNumber,\n                startColumn: startOffsetInInputWithInjections + 1,\n                endLineNumber: modelLineNumber,\n                endColumn: endOffsetInInputWithInjections + 1\n            });\n        }\n        if (outputLineIndex > 0) {\n            r = spaces(this._projectionData.wrappedTextIndentLength) + r;\n        }\n        return r;\n    }\n    getViewLineLength(model, modelLineNumber, outputLineIndex) {\n        this._assertVisible();\n        return this._projectionData.getLineLength(outputLineIndex);\n    }\n    getViewLineMinColumn(_model, _modelLineNumber, outputLineIndex) {\n        this._assertVisible();\n        return this._projectionData.getMinOutputOffset(outputLineIndex) + 1;\n    }\n    getViewLineMaxColumn(model, modelLineNumber, outputLineIndex) {\n        this._assertVisible();\n        return this._projectionData.getMaxOutputOffset(outputLineIndex) + 1;\n    }\n    /**\n     * Try using {@link getViewLinesData} instead.\n    */\n    getViewLineData(model, modelLineNumber, outputLineIndex) {\n        const arr = new Array();\n        this.getViewLinesData(model, modelLineNumber, outputLineIndex, 1, 0, [true], arr);\n        return arr[0];\n    }\n    getViewLinesData(model, modelLineNumber, outputLineIdx, lineCount, globalStartIndex, needed, result) {\n        this._assertVisible();\n        const lineBreakData = this._projectionData;\n        const injectionOffsets = lineBreakData.injectionOffsets;\n        const injectionOptions = lineBreakData.injectionOptions;\n        let inlineDecorationsPerOutputLine = null;\n        if (injectionOffsets) {\n            inlineDecorationsPerOutputLine = [];\n            let totalInjectedTextLengthBefore = 0;\n            let currentInjectedOffset = 0;\n            for (let outputLineIndex = 0; outputLineIndex < lineBreakData.getOutputLineCount(); outputLineIndex++) {\n                const inlineDecorations = new Array();\n                inlineDecorationsPerOutputLine[outputLineIndex] = inlineDecorations;\n                const lineStartOffsetInInputWithInjections = outputLineIndex > 0 ? lineBreakData.breakOffsets[outputLineIndex - 1] : 0;\n                const lineEndOffsetInInputWithInjections = lineBreakData.breakOffsets[outputLineIndex];\n                while (currentInjectedOffset < injectionOffsets.length) {\n                    const length = injectionOptions[currentInjectedOffset].content.length;\n                    const injectedTextStartOffsetInInputWithInjections = injectionOffsets[currentInjectedOffset] + totalInjectedTextLengthBefore;\n                    const injectedTextEndOffsetInInputWithInjections = injectedTextStartOffsetInInputWithInjections + length;\n                    if (injectedTextStartOffsetInInputWithInjections > lineEndOffsetInInputWithInjections) {\n                        // Injected text only starts in later wrapped lines.\n                        break;\n                    }\n                    if (lineStartOffsetInInputWithInjections < injectedTextEndOffsetInInputWithInjections) {\n                        // Injected text ends after or in this line (but also starts in or before this line).\n                        const options = injectionOptions[currentInjectedOffset];\n                        if (options.inlineClassName) {\n                            const offset = (outputLineIndex > 0 ? lineBreakData.wrappedTextIndentLength : 0);\n                            const start = offset + Math.max(injectedTextStartOffsetInInputWithInjections - lineStartOffsetInInputWithInjections, 0);\n                            const end = offset + Math.min(injectedTextEndOffsetInInputWithInjections - lineStartOffsetInInputWithInjections, lineEndOffsetInInputWithInjections - lineStartOffsetInInputWithInjections);\n                            if (start !== end) {\n                                inlineDecorations.push(new SingleLineInlineDecoration(start, end, options.inlineClassName, options.inlineClassNameAffectsLetterSpacing));\n                            }\n                        }\n                    }\n                    if (injectedTextEndOffsetInInputWithInjections <= lineEndOffsetInInputWithInjections) {\n                        totalInjectedTextLengthBefore += length;\n                        currentInjectedOffset++;\n                    }\n                    else {\n                        // injected text breaks into next line, process it again\n                        break;\n                    }\n                }\n            }\n        }\n        let lineWithInjections;\n        if (injectionOffsets) {\n            lineWithInjections = model.tokenization.getLineTokens(modelLineNumber).withInserted(injectionOffsets.map((offset, idx) => ({\n                offset,\n                text: injectionOptions[idx].content,\n                tokenMetadata: LineTokens.defaultTokenMetadata\n            })));\n        }\n        else {\n            lineWithInjections = model.tokenization.getLineTokens(modelLineNumber);\n        }\n        for (let outputLineIndex = outputLineIdx; outputLineIndex < outputLineIdx + lineCount; outputLineIndex++) {\n            const globalIndex = globalStartIndex + outputLineIndex - outputLineIdx;\n            if (!needed[globalIndex]) {\n                result[globalIndex] = null;\n                continue;\n            }\n            result[globalIndex] = this._getViewLineData(lineWithInjections, inlineDecorationsPerOutputLine ? inlineDecorationsPerOutputLine[outputLineIndex] : null, outputLineIndex);\n        }\n    }\n    _getViewLineData(lineWithInjections, inlineDecorations, outputLineIndex) {\n        this._assertVisible();\n        const lineBreakData = this._projectionData;\n        const deltaStartIndex = (outputLineIndex > 0 ? lineBreakData.wrappedTextIndentLength : 0);\n        const lineStartOffsetInInputWithInjections = outputLineIndex > 0 ? lineBreakData.breakOffsets[outputLineIndex - 1] : 0;\n        const lineEndOffsetInInputWithInjections = lineBreakData.breakOffsets[outputLineIndex];\n        const tokens = lineWithInjections.sliceAndInflate(lineStartOffsetInInputWithInjections, lineEndOffsetInInputWithInjections, deltaStartIndex);\n        let lineContent = tokens.getLineContent();\n        if (outputLineIndex > 0) {\n            lineContent = spaces(lineBreakData.wrappedTextIndentLength) + lineContent;\n        }\n        const minColumn = this._projectionData.getMinOutputOffset(outputLineIndex) + 1;\n        const maxColumn = lineContent.length + 1;\n        const continuesWithWrappedLine = (outputLineIndex + 1 < this.getViewLineCount());\n        const startVisibleColumn = (outputLineIndex === 0 ? 0 : lineBreakData.breakOffsetsVisibleColumn[outputLineIndex - 1]);\n        return new ViewLineData(lineContent, continuesWithWrappedLine, minColumn, maxColumn, startVisibleColumn, tokens, inlineDecorations);\n    }\n    getModelColumnOfViewPosition(outputLineIndex, outputColumn) {\n        this._assertVisible();\n        return this._projectionData.translateToInputOffset(outputLineIndex, outputColumn - 1) + 1;\n    }\n    getViewPositionOfModelPosition(deltaLineNumber, inputColumn, affinity = 2 /* PositionAffinity.None */) {\n        this._assertVisible();\n        const r = this._projectionData.translateToOutputPosition(inputColumn - 1, affinity);\n        return r.toPosition(deltaLineNumber);\n    }\n    getViewLineNumberOfModelPosition(deltaLineNumber, inputColumn) {\n        this._assertVisible();\n        const r = this._projectionData.translateToOutputPosition(inputColumn - 1);\n        return deltaLineNumber + r.outputLineIndex;\n    }\n    normalizePosition(outputLineIndex, outputPosition, affinity) {\n        const baseViewLineNumber = outputPosition.lineNumber - outputLineIndex;\n        const normalizedOutputPosition = this._projectionData.normalizeOutputPosition(outputLineIndex, outputPosition.column - 1, affinity);\n        const result = normalizedOutputPosition.toPosition(baseViewLineNumber);\n        return result;\n    }\n    getInjectedTextAt(outputLineIndex, outputColumn) {\n        return this._projectionData.getInjectedText(outputLineIndex, outputColumn - 1);\n    }\n    _assertVisible() {\n        if (!this._isVisible) {\n            throw new Error('Not supported');\n        }\n    }\n}\n/**\n * This projection does not change the model line.\n*/\nclass IdentityModelLineProjection {\n    static { this.INSTANCE = new IdentityModelLineProjection(); }\n    constructor() { }\n    isVisible() {\n        return true;\n    }\n    setVisible(isVisible) {\n        if (isVisible) {\n            return this;\n        }\n        return HiddenModelLineProjection.INSTANCE;\n    }\n    getProjectionData() {\n        return null;\n    }\n    getViewLineCount() {\n        return 1;\n    }\n    getViewLineContent(model, modelLineNumber, _outputLineIndex) {\n        return model.getLineContent(modelLineNumber);\n    }\n    getViewLineLength(model, modelLineNumber, _outputLineIndex) {\n        return model.getLineLength(modelLineNumber);\n    }\n    getViewLineMinColumn(model, modelLineNumber, _outputLineIndex) {\n        return model.getLineMinColumn(modelLineNumber);\n    }\n    getViewLineMaxColumn(model, modelLineNumber, _outputLineIndex) {\n        return model.getLineMaxColumn(modelLineNumber);\n    }\n    getViewLineData(model, modelLineNumber, _outputLineIndex) {\n        const lineTokens = model.tokenization.getLineTokens(modelLineNumber);\n        const lineContent = lineTokens.getLineContent();\n        return new ViewLineData(lineContent, false, 1, lineContent.length + 1, 0, lineTokens.inflate(), null);\n    }\n    getViewLinesData(model, modelLineNumber, _fromOuputLineIndex, _toOutputLineIndex, globalStartIndex, needed, result) {\n        if (!needed[globalStartIndex]) {\n            result[globalStartIndex] = null;\n            return;\n        }\n        result[globalStartIndex] = this.getViewLineData(model, modelLineNumber, 0);\n    }\n    getModelColumnOfViewPosition(_outputLineIndex, outputColumn) {\n        return outputColumn;\n    }\n    getViewPositionOfModelPosition(deltaLineNumber, inputColumn) {\n        return new Position(deltaLineNumber, inputColumn);\n    }\n    getViewLineNumberOfModelPosition(deltaLineNumber, _inputColumn) {\n        return deltaLineNumber;\n    }\n    normalizePosition(outputLineIndex, outputPosition, affinity) {\n        return outputPosition;\n    }\n    getInjectedTextAt(_outputLineIndex, _outputColumn) {\n        return null;\n    }\n}\n/**\n * This projection hides the model line.\n */\nclass HiddenModelLineProjection {\n    static { this.INSTANCE = new HiddenModelLineProjection(); }\n    constructor() { }\n    isVisible() {\n        return false;\n    }\n    setVisible(isVisible) {\n        if (!isVisible) {\n            return this;\n        }\n        return IdentityModelLineProjection.INSTANCE;\n    }\n    getProjectionData() {\n        return null;\n    }\n    getViewLineCount() {\n        return 0;\n    }\n    getViewLineContent(_model, _modelLineNumber, _outputLineIndex) {\n        throw new Error('Not supported');\n    }\n    getViewLineLength(_model, _modelLineNumber, _outputLineIndex) {\n        throw new Error('Not supported');\n    }\n    getViewLineMinColumn(_model, _modelLineNumber, _outputLineIndex) {\n        throw new Error('Not supported');\n    }\n    getViewLineMaxColumn(_model, _modelLineNumber, _outputLineIndex) {\n        throw new Error('Not supported');\n    }\n    getViewLineData(_model, _modelLineNumber, _outputLineIndex) {\n        throw new Error('Not supported');\n    }\n    getViewLinesData(_model, _modelLineNumber, _fromOuputLineIndex, _toOutputLineIndex, _globalStartIndex, _needed, _result) {\n        throw new Error('Not supported');\n    }\n    getModelColumnOfViewPosition(_outputLineIndex, _outputColumn) {\n        throw new Error('Not supported');\n    }\n    getViewPositionOfModelPosition(_deltaLineNumber, _inputColumn) {\n        throw new Error('Not supported');\n    }\n    getViewLineNumberOfModelPosition(_deltaLineNumber, _inputColumn) {\n        throw new Error('Not supported');\n    }\n    normalizePosition(outputLineIndex, outputPosition, affinity) {\n        throw new Error('Not supported');\n    }\n    getInjectedTextAt(_outputLineIndex, _outputColumn) {\n        throw new Error('Not supported');\n    }\n}\nconst _spaces = [''];\nfunction spaces(count) {\n    if (count >= _spaces.length) {\n        for (let i = 1; i <= count; i++) {\n            _spaces[i] = _makeSpaces(i);\n        }\n    }\n    return _spaces[count];\n}\nfunction _makeSpaces(count) {\n    return new Array(count + 1).join(' ');\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,UAAU,QAAQ,yBAAyB;AACpD,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,gBAAgB,QAAQ,uBAAuB;AACxD,SAASC,0BAA0B,EAAEC,YAAY,QAAQ,iBAAiB;AAC1E,OAAO,SAASC,yBAAyBA,CAACC,aAAa,EAAEC,SAAS,EAAE;EAChE,IAAID,aAAa,KAAK,IAAI,EAAE;IACxB;IACA,IAAIC,SAAS,EAAE;MACX,OAAOC,2BAA2B,CAACC,QAAQ;IAC/C;IACA,OAAOC,yBAAyB,CAACD,QAAQ;EAC7C,CAAC,MACI;IACD,OAAO,IAAIE,mBAAmB,CAACL,aAAa,EAAEC,SAAS,CAAC;EAC5D;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,MAAMI,mBAAmB,CAAC;EACtBC,WAAWA,CAACN,aAAa,EAAEC,SAAS,EAAE;IAClC,IAAI,CAACM,eAAe,GAAGP,aAAa;IACpC,IAAI,CAACQ,UAAU,GAAGP,SAAS;EAC/B;EACAA,SAASA,CAAA,EAAG;IACR,OAAO,IAAI,CAACO,UAAU;EAC1B;EACAC,UAAUA,CAACR,SAAS,EAAE;IAClB,IAAI,CAACO,UAAU,GAAGP,SAAS;IAC3B,OAAO,IAAI;EACf;EACAS,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACH,eAAe;EAC/B;EACAI,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAAC,IAAI,CAACH,UAAU,EAAE;MAClB,OAAO,CAAC;IACZ;IACA,OAAO,IAAI,CAACD,eAAe,CAACK,kBAAkB,CAAC,CAAC;EACpD;EACAC,kBAAkBA,CAACC,KAAK,EAAEC,eAAe,EAAEC,eAAe,EAAE;IACxD,IAAI,CAACC,cAAc,CAAC,CAAC;IACrB,MAAMC,gCAAgC,GAAGF,eAAe,GAAG,CAAC,GAAG,IAAI,CAACT,eAAe,CAACY,YAAY,CAACH,eAAe,GAAG,CAAC,CAAC,GAAG,CAAC;IACzH,MAAMI,8BAA8B,GAAG,IAAI,CAACb,eAAe,CAACY,YAAY,CAACH,eAAe,CAAC;IACzF,IAAIK,CAAC;IACL,IAAI,IAAI,CAACd,eAAe,CAACe,gBAAgB,KAAK,IAAI,EAAE;MAChD,MAAMC,aAAa,GAAG,IAAI,CAAChB,eAAe,CAACe,gBAAgB,CAACE,GAAG,CAAC,CAACC,MAAM,EAAEC,GAAG,KAAK,IAAI9B,gBAAgB,CAAC,CAAC,EAAE,CAAC,EAAE6B,MAAM,GAAG,CAAC,EAAE,IAAI,CAAClB,eAAe,CAACoB,gBAAgB,CAACD,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;MACvK,MAAME,kBAAkB,GAAGhC,gBAAgB,CAACiC,iBAAiB,CAACf,KAAK,CAACgB,cAAc,CAACf,eAAe,CAAC,EAAEQ,aAAa,CAAC;MACnHF,CAAC,GAAGO,kBAAkB,CAACG,SAAS,CAACb,gCAAgC,EAAEE,8BAA8B,CAAC;IACtG,CAAC,MACI;MACDC,CAAC,GAAGP,KAAK,CAACkB,eAAe,CAAC;QACtBC,eAAe,EAAElB,eAAe;QAChCmB,WAAW,EAAEhB,gCAAgC,GAAG,CAAC;QACjDiB,aAAa,EAAEpB,eAAe;QAC9BqB,SAAS,EAAEhB,8BAA8B,GAAG;MAChD,CAAC,CAAC;IACN;IACA,IAAIJ,eAAe,GAAG,CAAC,EAAE;MACrBK,CAAC,GAAGgB,MAAM,CAAC,IAAI,CAAC9B,eAAe,CAAC+B,uBAAuB,CAAC,GAAGjB,CAAC;IAChE;IACA,OAAOA,CAAC;EACZ;EACAkB,iBAAiBA,CAACzB,KAAK,EAAEC,eAAe,EAAEC,eAAe,EAAE;IACvD,IAAI,CAACC,cAAc,CAAC,CAAC;IACrB,OAAO,IAAI,CAACV,eAAe,CAACiC,aAAa,CAACxB,eAAe,CAAC;EAC9D;EACAyB,oBAAoBA,CAACC,MAAM,EAAEC,gBAAgB,EAAE3B,eAAe,EAAE;IAC5D,IAAI,CAACC,cAAc,CAAC,CAAC;IACrB,OAAO,IAAI,CAACV,eAAe,CAACqC,kBAAkB,CAAC5B,eAAe,CAAC,GAAG,CAAC;EACvE;EACA6B,oBAAoBA,CAAC/B,KAAK,EAAEC,eAAe,EAAEC,eAAe,EAAE;IAC1D,IAAI,CAACC,cAAc,CAAC,CAAC;IACrB,OAAO,IAAI,CAACV,eAAe,CAACuC,kBAAkB,CAAC9B,eAAe,CAAC,GAAG,CAAC;EACvE;EACA;AACJ;AACA;EACI+B,eAAeA,CAACjC,KAAK,EAAEC,eAAe,EAAEC,eAAe,EAAE;IACrD,MAAMgC,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC;IACvB,IAAI,CAACC,gBAAgB,CAACpC,KAAK,EAAEC,eAAe,EAAEC,eAAe,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,EAAEgC,GAAG,CAAC;IACjF,OAAOA,GAAG,CAAC,CAAC,CAAC;EACjB;EACAE,gBAAgBA,CAACpC,KAAK,EAAEC,eAAe,EAAEoC,aAAa,EAAEC,SAAS,EAAEC,gBAAgB,EAAEC,MAAM,EAAEC,MAAM,EAAE;IACjG,IAAI,CAACtC,cAAc,CAAC,CAAC;IACrB,MAAMjB,aAAa,GAAG,IAAI,CAACO,eAAe;IAC1C,MAAMe,gBAAgB,GAAGtB,aAAa,CAACsB,gBAAgB;IACvD,MAAMK,gBAAgB,GAAG3B,aAAa,CAAC2B,gBAAgB;IACvD,IAAI6B,8BAA8B,GAAG,IAAI;IACzC,IAAIlC,gBAAgB,EAAE;MAClBkC,8BAA8B,GAAG,EAAE;MACnC,IAAIC,6BAA6B,GAAG,CAAC;MACrC,IAAIC,qBAAqB,GAAG,CAAC;MAC7B,KAAK,IAAI1C,eAAe,GAAG,CAAC,EAAEA,eAAe,GAAGhB,aAAa,CAACY,kBAAkB,CAAC,CAAC,EAAEI,eAAe,EAAE,EAAE;QACnG,MAAM2C,iBAAiB,GAAG,IAAIV,KAAK,CAAC,CAAC;QACrCO,8BAA8B,CAACxC,eAAe,CAAC,GAAG2C,iBAAiB;QACnE,MAAMC,oCAAoC,GAAG5C,eAAe,GAAG,CAAC,GAAGhB,aAAa,CAACmB,YAAY,CAACH,eAAe,GAAG,CAAC,CAAC,GAAG,CAAC;QACtH,MAAM6C,kCAAkC,GAAG7D,aAAa,CAACmB,YAAY,CAACH,eAAe,CAAC;QACtF,OAAO0C,qBAAqB,GAAGpC,gBAAgB,CAACwC,MAAM,EAAE;UACpD,MAAMA,MAAM,GAAGnC,gBAAgB,CAAC+B,qBAAqB,CAAC,CAACK,OAAO,CAACD,MAAM;UACrE,MAAME,4CAA4C,GAAG1C,gBAAgB,CAACoC,qBAAqB,CAAC,GAAGD,6BAA6B;UAC5H,MAAMQ,0CAA0C,GAAGD,4CAA4C,GAAGF,MAAM;UACxG,IAAIE,4CAA4C,GAAGH,kCAAkC,EAAE;YACnF;YACA;UACJ;UACA,IAAID,oCAAoC,GAAGK,0CAA0C,EAAE;YACnF;YACA,MAAMC,OAAO,GAAGvC,gBAAgB,CAAC+B,qBAAqB,CAAC;YACvD,IAAIQ,OAAO,CAACC,eAAe,EAAE;cACzB,MAAM1C,MAAM,GAAIT,eAAe,GAAG,CAAC,GAAGhB,aAAa,CAACsC,uBAAuB,GAAG,CAAE;cAChF,MAAM8B,KAAK,GAAG3C,MAAM,GAAG4C,IAAI,CAACC,GAAG,CAACN,4CAA4C,GAAGJ,oCAAoC,EAAE,CAAC,CAAC;cACvH,MAAMW,GAAG,GAAG9C,MAAM,GAAG4C,IAAI,CAACG,GAAG,CAACP,0CAA0C,GAAGL,oCAAoC,EAAEC,kCAAkC,GAAGD,oCAAoC,CAAC;cAC3L,IAAIQ,KAAK,KAAKG,GAAG,EAAE;gBACfZ,iBAAiB,CAACc,IAAI,CAAC,IAAI5E,0BAA0B,CAACuE,KAAK,EAAEG,GAAG,EAAEL,OAAO,CAACC,eAAe,EAAED,OAAO,CAACQ,mCAAmC,CAAC,CAAC;cAC5I;YACJ;UACJ;UACA,IAAIT,0CAA0C,IAAIJ,kCAAkC,EAAE;YAClFJ,6BAA6B,IAAIK,MAAM;YACvCJ,qBAAqB,EAAE;UAC3B,CAAC,MACI;YACD;YACA;UACJ;QACJ;MACJ;IACJ;IACA,IAAI9B,kBAAkB;IACtB,IAAIN,gBAAgB,EAAE;MAClBM,kBAAkB,GAAGd,KAAK,CAAC6D,YAAY,CAACC,aAAa,CAAC7D,eAAe,CAAC,CAAC8D,YAAY,CAACvD,gBAAgB,CAACE,GAAG,CAAC,CAACC,MAAM,EAAEC,GAAG,MAAM;QACvHD,MAAM;QACNqD,IAAI,EAAEnD,gBAAgB,CAACD,GAAG,CAAC,CAACqC,OAAO;QACnCgB,aAAa,EAAErF,UAAU,CAACsF;MAC9B,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,MACI;MACDpD,kBAAkB,GAAGd,KAAK,CAAC6D,YAAY,CAACC,aAAa,CAAC7D,eAAe,CAAC;IAC1E;IACA,KAAK,IAAIC,eAAe,GAAGmC,aAAa,EAAEnC,eAAe,GAAGmC,aAAa,GAAGC,SAAS,EAAEpC,eAAe,EAAE,EAAE;MACtG,MAAMiE,WAAW,GAAG5B,gBAAgB,GAAGrC,eAAe,GAAGmC,aAAa;MACtE,IAAI,CAACG,MAAM,CAAC2B,WAAW,CAAC,EAAE;QACtB1B,MAAM,CAAC0B,WAAW,CAAC,GAAG,IAAI;QAC1B;MACJ;MACA1B,MAAM,CAAC0B,WAAW,CAAC,GAAG,IAAI,CAACC,gBAAgB,CAACtD,kBAAkB,EAAE4B,8BAA8B,GAAGA,8BAA8B,CAACxC,eAAe,CAAC,GAAG,IAAI,EAAEA,eAAe,CAAC;IAC7K;EACJ;EACAkE,gBAAgBA,CAACtD,kBAAkB,EAAE+B,iBAAiB,EAAE3C,eAAe,EAAE;IACrE,IAAI,CAACC,cAAc,CAAC,CAAC;IACrB,MAAMjB,aAAa,GAAG,IAAI,CAACO,eAAe;IAC1C,MAAM4E,eAAe,GAAInE,eAAe,GAAG,CAAC,GAAGhB,aAAa,CAACsC,uBAAuB,GAAG,CAAE;IACzF,MAAMsB,oCAAoC,GAAG5C,eAAe,GAAG,CAAC,GAAGhB,aAAa,CAACmB,YAAY,CAACH,eAAe,GAAG,CAAC,CAAC,GAAG,CAAC;IACtH,MAAM6C,kCAAkC,GAAG7D,aAAa,CAACmB,YAAY,CAACH,eAAe,CAAC;IACtF,MAAMoE,MAAM,GAAGxD,kBAAkB,CAACyD,eAAe,CAACzB,oCAAoC,EAAEC,kCAAkC,EAAEsB,eAAe,CAAC;IAC5I,IAAIG,WAAW,GAAGF,MAAM,CAACtD,cAAc,CAAC,CAAC;IACzC,IAAId,eAAe,GAAG,CAAC,EAAE;MACrBsE,WAAW,GAAGjD,MAAM,CAACrC,aAAa,CAACsC,uBAAuB,CAAC,GAAGgD,WAAW;IAC7E;IACA,MAAMC,SAAS,GAAG,IAAI,CAAChF,eAAe,CAACqC,kBAAkB,CAAC5B,eAAe,CAAC,GAAG,CAAC;IAC9E,MAAMwE,SAAS,GAAGF,WAAW,CAACxB,MAAM,GAAG,CAAC;IACxC,MAAM2B,wBAAwB,GAAIzE,eAAe,GAAG,CAAC,GAAG,IAAI,CAACL,gBAAgB,CAAC,CAAE;IAChF,MAAM+E,kBAAkB,GAAI1E,eAAe,KAAK,CAAC,GAAG,CAAC,GAAGhB,aAAa,CAAC2F,yBAAyB,CAAC3E,eAAe,GAAG,CAAC,CAAE;IACrH,OAAO,IAAIlB,YAAY,CAACwF,WAAW,EAAEG,wBAAwB,EAAEF,SAAS,EAAEC,SAAS,EAAEE,kBAAkB,EAAEN,MAAM,EAAEzB,iBAAiB,CAAC;EACvI;EACAiC,4BAA4BA,CAAC5E,eAAe,EAAE6E,YAAY,EAAE;IACxD,IAAI,CAAC5E,cAAc,CAAC,CAAC;IACrB,OAAO,IAAI,CAACV,eAAe,CAACuF,sBAAsB,CAAC9E,eAAe,EAAE6E,YAAY,GAAG,CAAC,CAAC,GAAG,CAAC;EAC7F;EACAE,8BAA8BA,CAACC,eAAe,EAAEC,WAAW,EAAEC,QAAQ,GAAG,CAAC,CAAC,6BAA6B;IACnG,IAAI,CAACjF,cAAc,CAAC,CAAC;IACrB,MAAMI,CAAC,GAAG,IAAI,CAACd,eAAe,CAAC4F,yBAAyB,CAACF,WAAW,GAAG,CAAC,EAAEC,QAAQ,CAAC;IACnF,OAAO7E,CAAC,CAAC+E,UAAU,CAACJ,eAAe,CAAC;EACxC;EACAK,gCAAgCA,CAACL,eAAe,EAAEC,WAAW,EAAE;IAC3D,IAAI,CAAChF,cAAc,CAAC,CAAC;IACrB,MAAMI,CAAC,GAAG,IAAI,CAACd,eAAe,CAAC4F,yBAAyB,CAACF,WAAW,GAAG,CAAC,CAAC;IACzE,OAAOD,eAAe,GAAG3E,CAAC,CAACL,eAAe;EAC9C;EACAsF,iBAAiBA,CAACtF,eAAe,EAAEuF,cAAc,EAAEL,QAAQ,EAAE;IACzD,MAAMM,kBAAkB,GAAGD,cAAc,CAACE,UAAU,GAAGzF,eAAe;IACtE,MAAM0F,wBAAwB,GAAG,IAAI,CAACnG,eAAe,CAACoG,uBAAuB,CAAC3F,eAAe,EAAEuF,cAAc,CAACK,MAAM,GAAG,CAAC,EAAEV,QAAQ,CAAC;IACnI,MAAM3C,MAAM,GAAGmD,wBAAwB,CAACN,UAAU,CAACI,kBAAkB,CAAC;IACtE,OAAOjD,MAAM;EACjB;EACAsD,iBAAiBA,CAAC7F,eAAe,EAAE6E,YAAY,EAAE;IAC7C,OAAO,IAAI,CAACtF,eAAe,CAACuG,eAAe,CAAC9F,eAAe,EAAE6E,YAAY,GAAG,CAAC,CAAC;EAClF;EACA5E,cAAcA,CAAA,EAAG;IACb,IAAI,CAAC,IAAI,CAACT,UAAU,EAAE;MAClB,MAAM,IAAIuG,KAAK,CAAC,eAAe,CAAC;IACpC;EACJ;AACJ;AACA;AACA;AACA;AACA,MAAM7G,2BAA2B,CAAC;EAC9B;IAAS,IAAI,CAACC,QAAQ,GAAG,IAAID,2BAA2B,CAAC,CAAC;EAAE;EAC5DI,WAAWA,CAAA,EAAG,CAAE;EAChBL,SAASA,CAAA,EAAG;IACR,OAAO,IAAI;EACf;EACAQ,UAAUA,CAACR,SAAS,EAAE;IAClB,IAAIA,SAAS,EAAE;MACX,OAAO,IAAI;IACf;IACA,OAAOG,yBAAyB,CAACD,QAAQ;EAC7C;EACAO,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI;EACf;EACAC,gBAAgBA,CAAA,EAAG;IACf,OAAO,CAAC;EACZ;EACAE,kBAAkBA,CAACC,KAAK,EAAEC,eAAe,EAAEiG,gBAAgB,EAAE;IACzD,OAAOlG,KAAK,CAACgB,cAAc,CAACf,eAAe,CAAC;EAChD;EACAwB,iBAAiBA,CAACzB,KAAK,EAAEC,eAAe,EAAEiG,gBAAgB,EAAE;IACxD,OAAOlG,KAAK,CAAC0B,aAAa,CAACzB,eAAe,CAAC;EAC/C;EACA0B,oBAAoBA,CAAC3B,KAAK,EAAEC,eAAe,EAAEiG,gBAAgB,EAAE;IAC3D,OAAOlG,KAAK,CAACmG,gBAAgB,CAAClG,eAAe,CAAC;EAClD;EACA8B,oBAAoBA,CAAC/B,KAAK,EAAEC,eAAe,EAAEiG,gBAAgB,EAAE;IAC3D,OAAOlG,KAAK,CAACoG,gBAAgB,CAACnG,eAAe,CAAC;EAClD;EACAgC,eAAeA,CAACjC,KAAK,EAAEC,eAAe,EAAEiG,gBAAgB,EAAE;IACtD,MAAMG,UAAU,GAAGrG,KAAK,CAAC6D,YAAY,CAACC,aAAa,CAAC7D,eAAe,CAAC;IACpE,MAAMuE,WAAW,GAAG6B,UAAU,CAACrF,cAAc,CAAC,CAAC;IAC/C,OAAO,IAAIhC,YAAY,CAACwF,WAAW,EAAE,KAAK,EAAE,CAAC,EAAEA,WAAW,CAACxB,MAAM,GAAG,CAAC,EAAE,CAAC,EAAEqD,UAAU,CAACC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC;EACzG;EACAlE,gBAAgBA,CAACpC,KAAK,EAAEC,eAAe,EAAEsG,mBAAmB,EAAEC,kBAAkB,EAAEjE,gBAAgB,EAAEC,MAAM,EAAEC,MAAM,EAAE;IAChH,IAAI,CAACD,MAAM,CAACD,gBAAgB,CAAC,EAAE;MAC3BE,MAAM,CAACF,gBAAgB,CAAC,GAAG,IAAI;MAC/B;IACJ;IACAE,MAAM,CAACF,gBAAgB,CAAC,GAAG,IAAI,CAACN,eAAe,CAACjC,KAAK,EAAEC,eAAe,EAAE,CAAC,CAAC;EAC9E;EACA6E,4BAA4BA,CAACoB,gBAAgB,EAAEnB,YAAY,EAAE;IACzD,OAAOA,YAAY;EACvB;EACAE,8BAA8BA,CAACC,eAAe,EAAEC,WAAW,EAAE;IACzD,OAAO,IAAItG,QAAQ,CAACqG,eAAe,EAAEC,WAAW,CAAC;EACrD;EACAI,gCAAgCA,CAACL,eAAe,EAAEuB,YAAY,EAAE;IAC5D,OAAOvB,eAAe;EAC1B;EACAM,iBAAiBA,CAACtF,eAAe,EAAEuF,cAAc,EAAEL,QAAQ,EAAE;IACzD,OAAOK,cAAc;EACzB;EACAM,iBAAiBA,CAACG,gBAAgB,EAAEQ,aAAa,EAAE;IAC/C,OAAO,IAAI;EACf;AACJ;AACA;AACA;AACA;AACA,MAAMpH,yBAAyB,CAAC;EAC5B;IAAS,IAAI,CAACD,QAAQ,GAAG,IAAIC,yBAAyB,CAAC,CAAC;EAAE;EAC1DE,WAAWA,CAAA,EAAG,CAAE;EAChBL,SAASA,CAAA,EAAG;IACR,OAAO,KAAK;EAChB;EACAQ,UAAUA,CAACR,SAAS,EAAE;IAClB,IAAI,CAACA,SAAS,EAAE;MACZ,OAAO,IAAI;IACf;IACA,OAAOC,2BAA2B,CAACC,QAAQ;EAC/C;EACAO,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI;EACf;EACAC,gBAAgBA,CAAA,EAAG;IACf,OAAO,CAAC;EACZ;EACAE,kBAAkBA,CAAC6B,MAAM,EAAEC,gBAAgB,EAAEqE,gBAAgB,EAAE;IAC3D,MAAM,IAAID,KAAK,CAAC,eAAe,CAAC;EACpC;EACAxE,iBAAiBA,CAACG,MAAM,EAAEC,gBAAgB,EAAEqE,gBAAgB,EAAE;IAC1D,MAAM,IAAID,KAAK,CAAC,eAAe,CAAC;EACpC;EACAtE,oBAAoBA,CAACC,MAAM,EAAEC,gBAAgB,EAAEqE,gBAAgB,EAAE;IAC7D,MAAM,IAAID,KAAK,CAAC,eAAe,CAAC;EACpC;EACAlE,oBAAoBA,CAACH,MAAM,EAAEC,gBAAgB,EAAEqE,gBAAgB,EAAE;IAC7D,MAAM,IAAID,KAAK,CAAC,eAAe,CAAC;EACpC;EACAhE,eAAeA,CAACL,MAAM,EAAEC,gBAAgB,EAAEqE,gBAAgB,EAAE;IACxD,MAAM,IAAID,KAAK,CAAC,eAAe,CAAC;EACpC;EACA7D,gBAAgBA,CAACR,MAAM,EAAEC,gBAAgB,EAAE0E,mBAAmB,EAAEC,kBAAkB,EAAEG,iBAAiB,EAAEC,OAAO,EAAEC,OAAO,EAAE;IACrH,MAAM,IAAIZ,KAAK,CAAC,eAAe,CAAC;EACpC;EACAnB,4BAA4BA,CAACoB,gBAAgB,EAAEQ,aAAa,EAAE;IAC1D,MAAM,IAAIT,KAAK,CAAC,eAAe,CAAC;EACpC;EACAhB,8BAA8BA,CAAC6B,gBAAgB,EAAEL,YAAY,EAAE;IAC3D,MAAM,IAAIR,KAAK,CAAC,eAAe,CAAC;EACpC;EACAV,gCAAgCA,CAACuB,gBAAgB,EAAEL,YAAY,EAAE;IAC7D,MAAM,IAAIR,KAAK,CAAC,eAAe,CAAC;EACpC;EACAT,iBAAiBA,CAACtF,eAAe,EAAEuF,cAAc,EAAEL,QAAQ,EAAE;IACzD,MAAM,IAAIa,KAAK,CAAC,eAAe,CAAC;EACpC;EACAF,iBAAiBA,CAACG,gBAAgB,EAAEQ,aAAa,EAAE;IAC/C,MAAM,IAAIT,KAAK,CAAC,eAAe,CAAC;EACpC;AACJ;AACA,MAAMc,OAAO,GAAG,CAAC,EAAE,CAAC;AACpB,SAASxF,MAAMA,CAACyF,KAAK,EAAE;EACnB,IAAIA,KAAK,IAAID,OAAO,CAAC/D,MAAM,EAAE;IACzB,KAAK,IAAIiE,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAID,KAAK,EAAEC,CAAC,EAAE,EAAE;MAC7BF,OAAO,CAACE,CAAC,CAAC,GAAGC,WAAW,CAACD,CAAC,CAAC;IAC/B;EACJ;EACA,OAAOF,OAAO,CAACC,KAAK,CAAC;AACzB;AACA,SAASE,WAAWA,CAACF,KAAK,EAAE;EACxB,OAAO,IAAI7E,KAAK,CAAC6E,KAAK,GAAG,CAAC,CAAC,CAACG,IAAI,CAAC,GAAG,CAAC;AACzC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}