{"ast": null, "code": "var __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = this && this.__param || function (paramIndex, decorator) {\n  return function (target, key) {\n    decorator(target, key, paramIndex);\n  };\n};\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as dom from '../../../base/browser/dom.js';\nimport { ActionBar } from '../../../base/browser/ui/actionbar/actionbar.js';\nimport { Disposable, DisposableStore, MutableDisposable } from '../../../base/common/lifecycle.js';\nimport './actionWidget.css';\nimport { localize, localize2 } from '../../../nls.js';\nimport { acceptSelectedActionCommand, ActionList, previewSelectedActionCommand } from './actionList.js';\nimport { Action2, registerAction2 } from '../../actions/common/actions.js';\nimport { IContextKeyService, RawContextKey } from '../../contextkey/common/contextkey.js';\nimport { IContextViewService } from '../../contextview/browser/contextView.js';\nimport { registerSingleton } from '../../instantiation/common/extensions.js';\nimport { createDecorator, IInstantiationService } from '../../instantiation/common/instantiation.js';\nimport { inputActiveOptionBackground, registerColor } from '../../theme/common/colorRegistry.js';\nregisterColor('actionBar.toggledBackground', inputActiveOptionBackground, localize('actionBar.toggledBackground', 'Background color for toggled action items in action bar.'));\nconst ActionWidgetContextKeys = {\n  Visible: new RawContextKey('codeActionMenuVisible', false, localize('codeActionMenuVisible', \"Whether the action widget list is visible\"))\n};\nexport const IActionWidgetService = createDecorator('actionWidgetService');\nlet ActionWidgetService = class ActionWidgetService extends Disposable {\n  get isVisible() {\n    return ActionWidgetContextKeys.Visible.getValue(this._contextKeyService) || false;\n  }\n  constructor(_contextViewService, _contextKeyService, _instantiationService) {\n    super();\n    this._contextViewService = _contextViewService;\n    this._contextKeyService = _contextKeyService;\n    this._instantiationService = _instantiationService;\n    this._list = this._register(new MutableDisposable());\n  }\n  show(user, supportsPreview, items, delegate, anchor, container, actionBarActions) {\n    const visibleContext = ActionWidgetContextKeys.Visible.bindTo(this._contextKeyService);\n    const list = this._instantiationService.createInstance(ActionList, user, supportsPreview, items, delegate);\n    this._contextViewService.showContextView({\n      getAnchor: () => anchor,\n      render: container => {\n        visibleContext.set(true);\n        return this._renderWidget(container, list, actionBarActions ?? []);\n      },\n      onHide: didCancel => {\n        visibleContext.reset();\n        this._onWidgetClosed(didCancel);\n      }\n    }, container, false);\n  }\n  acceptSelected(preview) {\n    this._list.value?.acceptSelected(preview);\n  }\n  focusPrevious() {\n    this._list?.value?.focusPrevious();\n  }\n  focusNext() {\n    this._list?.value?.focusNext();\n  }\n  hide(didCancel) {\n    this._list.value?.hide(didCancel);\n    this._list.clear();\n  }\n  _renderWidget(element, list, actionBarActions) {\n    const widget = document.createElement('div');\n    widget.classList.add('action-widget');\n    element.appendChild(widget);\n    this._list.value = list;\n    if (this._list.value) {\n      widget.appendChild(this._list.value.domNode);\n    } else {\n      throw new Error('List has no value');\n    }\n    const renderDisposables = new DisposableStore();\n    // Invisible div to block mouse interaction in the rest of the UI\n    const menuBlock = document.createElement('div');\n    const block = element.appendChild(menuBlock);\n    block.classList.add('context-view-block');\n    renderDisposables.add(dom.addDisposableListener(block, dom.EventType.MOUSE_DOWN, e => e.stopPropagation()));\n    // Invisible div to block mouse interaction with the menu\n    const pointerBlockDiv = document.createElement('div');\n    const pointerBlock = element.appendChild(pointerBlockDiv);\n    pointerBlock.classList.add('context-view-pointerBlock');\n    // Removes block on click INSIDE widget or ANY mouse movement\n    renderDisposables.add(dom.addDisposableListener(pointerBlock, dom.EventType.POINTER_MOVE, () => pointerBlock.remove()));\n    renderDisposables.add(dom.addDisposableListener(pointerBlock, dom.EventType.MOUSE_DOWN, () => pointerBlock.remove()));\n    // Action bar\n    let actionBarWidth = 0;\n    if (actionBarActions.length) {\n      const actionBar = this._createActionBar('.action-widget-action-bar', actionBarActions);\n      if (actionBar) {\n        widget.appendChild(actionBar.getContainer().parentElement);\n        renderDisposables.add(actionBar);\n        actionBarWidth = actionBar.getContainer().offsetWidth;\n      }\n    }\n    const width = this._list.value?.layout(actionBarWidth);\n    widget.style.width = `${width}px`;\n    const focusTracker = renderDisposables.add(dom.trackFocus(element));\n    renderDisposables.add(focusTracker.onDidBlur(() => this.hide(true)));\n    return renderDisposables;\n  }\n  _createActionBar(className, actions) {\n    if (!actions.length) {\n      return undefined;\n    }\n    const container = dom.$(className);\n    const actionBar = new ActionBar(container);\n    actionBar.push(actions, {\n      icon: false,\n      label: true\n    });\n    return actionBar;\n  }\n  _onWidgetClosed(didCancel) {\n    this._list.value?.hide(didCancel);\n  }\n};\nActionWidgetService = __decorate([__param(0, IContextViewService), __param(1, IContextKeyService), __param(2, IInstantiationService)], ActionWidgetService);\nregisterSingleton(IActionWidgetService, ActionWidgetService, 1 /* InstantiationType.Delayed */);\nconst weight = 100 /* KeybindingWeight.EditorContrib */ + 1000;\nregisterAction2(class extends Action2 {\n  constructor() {\n    super({\n      id: 'hideCodeActionWidget',\n      title: localize2('hideCodeActionWidget.title', \"Hide action widget\"),\n      precondition: ActionWidgetContextKeys.Visible,\n      keybinding: {\n        weight,\n        primary: 9 /* KeyCode.Escape */,\n        secondary: [1024 /* KeyMod.Shift */ | 9 /* KeyCode.Escape */]\n      }\n    });\n  }\n  run(accessor) {\n    accessor.get(IActionWidgetService).hide(true);\n  }\n});\nregisterAction2(class extends Action2 {\n  constructor() {\n    super({\n      id: 'selectPrevCodeAction',\n      title: localize2('selectPrevCodeAction.title', \"Select previous action\"),\n      precondition: ActionWidgetContextKeys.Visible,\n      keybinding: {\n        weight,\n        primary: 16 /* KeyCode.UpArrow */,\n        secondary: [2048 /* KeyMod.CtrlCmd */ | 16 /* KeyCode.UpArrow */],\n        mac: {\n          primary: 16 /* KeyCode.UpArrow */,\n          secondary: [2048 /* KeyMod.CtrlCmd */ | 16 /* KeyCode.UpArrow */, 256 /* KeyMod.WinCtrl */ | 46 /* KeyCode.KeyP */]\n        }\n      }\n    });\n  }\n  run(accessor) {\n    const widgetService = accessor.get(IActionWidgetService);\n    if (widgetService instanceof ActionWidgetService) {\n      widgetService.focusPrevious();\n    }\n  }\n});\nregisterAction2(class extends Action2 {\n  constructor() {\n    super({\n      id: 'selectNextCodeAction',\n      title: localize2('selectNextCodeAction.title', \"Select next action\"),\n      precondition: ActionWidgetContextKeys.Visible,\n      keybinding: {\n        weight,\n        primary: 18 /* KeyCode.DownArrow */,\n        secondary: [2048 /* KeyMod.CtrlCmd */ | 18 /* KeyCode.DownArrow */],\n        mac: {\n          primary: 18 /* KeyCode.DownArrow */,\n          secondary: [2048 /* KeyMod.CtrlCmd */ | 18 /* KeyCode.DownArrow */, 256 /* KeyMod.WinCtrl */ | 44 /* KeyCode.KeyN */]\n        }\n      }\n    });\n  }\n  run(accessor) {\n    const widgetService = accessor.get(IActionWidgetService);\n    if (widgetService instanceof ActionWidgetService) {\n      widgetService.focusNext();\n    }\n  }\n});\nregisterAction2(class extends Action2 {\n  constructor() {\n    super({\n      id: acceptSelectedActionCommand,\n      title: localize2('acceptSelected.title', \"Accept selected action\"),\n      precondition: ActionWidgetContextKeys.Visible,\n      keybinding: {\n        weight,\n        primary: 3 /* KeyCode.Enter */,\n        secondary: [2048 /* KeyMod.CtrlCmd */ | 89 /* KeyCode.Period */]\n      }\n    });\n  }\n  run(accessor) {\n    const widgetService = accessor.get(IActionWidgetService);\n    if (widgetService instanceof ActionWidgetService) {\n      widgetService.acceptSelected();\n    }\n  }\n});\nregisterAction2(class extends Action2 {\n  constructor() {\n    super({\n      id: previewSelectedActionCommand,\n      title: localize2('previewSelected.title', \"Preview selected action\"),\n      precondition: ActionWidgetContextKeys.Visible,\n      keybinding: {\n        weight,\n        primary: 2048 /* KeyMod.CtrlCmd */ | 3 /* KeyCode.Enter */\n      }\n    });\n  }\n  run(accessor) {\n    const widgetService = accessor.get(IActionWidgetService);\n    if (widgetService instanceof ActionWidgetService) {\n      widgetService.acceptSelected(true);\n    }\n  }\n});", "map": {"version": 3, "names": ["__decorate", "decorators", "target", "key", "desc", "c", "arguments", "length", "r", "Object", "getOwnPropertyDescriptor", "d", "Reflect", "decorate", "i", "defineProperty", "__param", "paramIndex", "decorator", "dom", "ActionBar", "Disposable", "DisposableStore", "MutableDisposable", "localize", "localize2", "acceptSelectedActionCommand", "ActionList", "previewSelectedActionCommand", "Action2", "registerAction2", "IContextKeyService", "RawContextKey", "IContextViewService", "registerSingleton", "createDecorator", "IInstantiationService", "inputActiveOptionBackground", "registerColor", "ActionWidgetContextKeys", "Visible", "IActionWidgetService", "ActionWidgetService", "isVisible", "getValue", "_contextKeyService", "constructor", "_contextViewService", "_instantiationService", "_list", "_register", "show", "user", "supportsPreview", "items", "delegate", "anchor", "container", "actionBarActions", "visibleContext", "bindTo", "list", "createInstance", "showContextView", "getAnchor", "render", "set", "_renderWidget", "onHide", "didCancel", "reset", "_onWidgetClosed", "acceptSelected", "preview", "value", "focusPrevious", "focusNext", "hide", "clear", "element", "widget", "document", "createElement", "classList", "add", "append<PERSON><PERSON><PERSON>", "domNode", "Error", "renderDisposables", "menuBlock", "block", "addDisposableListener", "EventType", "MOUSE_DOWN", "e", "stopPropagation", "pointerBlockDiv", "pointer<PERSON>lock", "POINTER_MOVE", "remove", "actionBarWidth", "actionBar", "_createActionBar", "getContainer", "parentElement", "offsetWidth", "width", "layout", "style", "focusTracker", "trackFocus", "onDidBlur", "className", "actions", "undefined", "$", "push", "icon", "label", "weight", "id", "title", "precondition", "keybinding", "primary", "secondary", "run", "accessor", "get", "mac", "widgetService"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/platform/actionWidget/browser/actionWidget.js"], "sourcesContent": ["var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = (this && this.__param) || function (paramIndex, decorator) {\n    return function (target, key) { decorator(target, key, paramIndex); }\n};\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as dom from '../../../base/browser/dom.js';\nimport { ActionBar } from '../../../base/browser/ui/actionbar/actionbar.js';\nimport { Disposable, DisposableStore, MutableDisposable } from '../../../base/common/lifecycle.js';\nimport './actionWidget.css';\nimport { localize, localize2 } from '../../../nls.js';\nimport { acceptSelectedActionCommand, ActionList, previewSelectedActionCommand } from './actionList.js';\nimport { Action2, registerAction2 } from '../../actions/common/actions.js';\nimport { IContextKeyService, RawContextKey } from '../../contextkey/common/contextkey.js';\nimport { IContextViewService } from '../../contextview/browser/contextView.js';\nimport { registerSingleton } from '../../instantiation/common/extensions.js';\nimport { createDecorator, IInstantiationService } from '../../instantiation/common/instantiation.js';\nimport { inputActiveOptionBackground, registerColor } from '../../theme/common/colorRegistry.js';\nregisterColor('actionBar.toggledBackground', inputActiveOptionBackground, localize('actionBar.toggledBackground', 'Background color for toggled action items in action bar.'));\nconst ActionWidgetContextKeys = {\n    Visible: new RawContextKey('codeActionMenuVisible', false, localize('codeActionMenuVisible', \"Whether the action widget list is visible\"))\n};\nexport const IActionWidgetService = createDecorator('actionWidgetService');\nlet ActionWidgetService = class ActionWidgetService extends Disposable {\n    get isVisible() {\n        return ActionWidgetContextKeys.Visible.getValue(this._contextKeyService) || false;\n    }\n    constructor(_contextViewService, _contextKeyService, _instantiationService) {\n        super();\n        this._contextViewService = _contextViewService;\n        this._contextKeyService = _contextKeyService;\n        this._instantiationService = _instantiationService;\n        this._list = this._register(new MutableDisposable());\n    }\n    show(user, supportsPreview, items, delegate, anchor, container, actionBarActions) {\n        const visibleContext = ActionWidgetContextKeys.Visible.bindTo(this._contextKeyService);\n        const list = this._instantiationService.createInstance(ActionList, user, supportsPreview, items, delegate);\n        this._contextViewService.showContextView({\n            getAnchor: () => anchor,\n            render: (container) => {\n                visibleContext.set(true);\n                return this._renderWidget(container, list, actionBarActions ?? []);\n            },\n            onHide: (didCancel) => {\n                visibleContext.reset();\n                this._onWidgetClosed(didCancel);\n            },\n        }, container, false);\n    }\n    acceptSelected(preview) {\n        this._list.value?.acceptSelected(preview);\n    }\n    focusPrevious() {\n        this._list?.value?.focusPrevious();\n    }\n    focusNext() {\n        this._list?.value?.focusNext();\n    }\n    hide(didCancel) {\n        this._list.value?.hide(didCancel);\n        this._list.clear();\n    }\n    _renderWidget(element, list, actionBarActions) {\n        const widget = document.createElement('div');\n        widget.classList.add('action-widget');\n        element.appendChild(widget);\n        this._list.value = list;\n        if (this._list.value) {\n            widget.appendChild(this._list.value.domNode);\n        }\n        else {\n            throw new Error('List has no value');\n        }\n        const renderDisposables = new DisposableStore();\n        // Invisible div to block mouse interaction in the rest of the UI\n        const menuBlock = document.createElement('div');\n        const block = element.appendChild(menuBlock);\n        block.classList.add('context-view-block');\n        renderDisposables.add(dom.addDisposableListener(block, dom.EventType.MOUSE_DOWN, e => e.stopPropagation()));\n        // Invisible div to block mouse interaction with the menu\n        const pointerBlockDiv = document.createElement('div');\n        const pointerBlock = element.appendChild(pointerBlockDiv);\n        pointerBlock.classList.add('context-view-pointerBlock');\n        // Removes block on click INSIDE widget or ANY mouse movement\n        renderDisposables.add(dom.addDisposableListener(pointerBlock, dom.EventType.POINTER_MOVE, () => pointerBlock.remove()));\n        renderDisposables.add(dom.addDisposableListener(pointerBlock, dom.EventType.MOUSE_DOWN, () => pointerBlock.remove()));\n        // Action bar\n        let actionBarWidth = 0;\n        if (actionBarActions.length) {\n            const actionBar = this._createActionBar('.action-widget-action-bar', actionBarActions);\n            if (actionBar) {\n                widget.appendChild(actionBar.getContainer().parentElement);\n                renderDisposables.add(actionBar);\n                actionBarWidth = actionBar.getContainer().offsetWidth;\n            }\n        }\n        const width = this._list.value?.layout(actionBarWidth);\n        widget.style.width = `${width}px`;\n        const focusTracker = renderDisposables.add(dom.trackFocus(element));\n        renderDisposables.add(focusTracker.onDidBlur(() => this.hide(true)));\n        return renderDisposables;\n    }\n    _createActionBar(className, actions) {\n        if (!actions.length) {\n            return undefined;\n        }\n        const container = dom.$(className);\n        const actionBar = new ActionBar(container);\n        actionBar.push(actions, { icon: false, label: true });\n        return actionBar;\n    }\n    _onWidgetClosed(didCancel) {\n        this._list.value?.hide(didCancel);\n    }\n};\nActionWidgetService = __decorate([\n    __param(0, IContextViewService),\n    __param(1, IContextKeyService),\n    __param(2, IInstantiationService)\n], ActionWidgetService);\nregisterSingleton(IActionWidgetService, ActionWidgetService, 1 /* InstantiationType.Delayed */);\nconst weight = 100 /* KeybindingWeight.EditorContrib */ + 1000;\nregisterAction2(class extends Action2 {\n    constructor() {\n        super({\n            id: 'hideCodeActionWidget',\n            title: localize2('hideCodeActionWidget.title', \"Hide action widget\"),\n            precondition: ActionWidgetContextKeys.Visible,\n            keybinding: {\n                weight,\n                primary: 9 /* KeyCode.Escape */,\n                secondary: [1024 /* KeyMod.Shift */ | 9 /* KeyCode.Escape */]\n            },\n        });\n    }\n    run(accessor) {\n        accessor.get(IActionWidgetService).hide(true);\n    }\n});\nregisterAction2(class extends Action2 {\n    constructor() {\n        super({\n            id: 'selectPrevCodeAction',\n            title: localize2('selectPrevCodeAction.title', \"Select previous action\"),\n            precondition: ActionWidgetContextKeys.Visible,\n            keybinding: {\n                weight,\n                primary: 16 /* KeyCode.UpArrow */,\n                secondary: [2048 /* KeyMod.CtrlCmd */ | 16 /* KeyCode.UpArrow */],\n                mac: { primary: 16 /* KeyCode.UpArrow */, secondary: [2048 /* KeyMod.CtrlCmd */ | 16 /* KeyCode.UpArrow */, 256 /* KeyMod.WinCtrl */ | 46 /* KeyCode.KeyP */] },\n            }\n        });\n    }\n    run(accessor) {\n        const widgetService = accessor.get(IActionWidgetService);\n        if (widgetService instanceof ActionWidgetService) {\n            widgetService.focusPrevious();\n        }\n    }\n});\nregisterAction2(class extends Action2 {\n    constructor() {\n        super({\n            id: 'selectNextCodeAction',\n            title: localize2('selectNextCodeAction.title', \"Select next action\"),\n            precondition: ActionWidgetContextKeys.Visible,\n            keybinding: {\n                weight,\n                primary: 18 /* KeyCode.DownArrow */,\n                secondary: [2048 /* KeyMod.CtrlCmd */ | 18 /* KeyCode.DownArrow */],\n                mac: { primary: 18 /* KeyCode.DownArrow */, secondary: [2048 /* KeyMod.CtrlCmd */ | 18 /* KeyCode.DownArrow */, 256 /* KeyMod.WinCtrl */ | 44 /* KeyCode.KeyN */] }\n            }\n        });\n    }\n    run(accessor) {\n        const widgetService = accessor.get(IActionWidgetService);\n        if (widgetService instanceof ActionWidgetService) {\n            widgetService.focusNext();\n        }\n    }\n});\nregisterAction2(class extends Action2 {\n    constructor() {\n        super({\n            id: acceptSelectedActionCommand,\n            title: localize2('acceptSelected.title', \"Accept selected action\"),\n            precondition: ActionWidgetContextKeys.Visible,\n            keybinding: {\n                weight,\n                primary: 3 /* KeyCode.Enter */,\n                secondary: [2048 /* KeyMod.CtrlCmd */ | 89 /* KeyCode.Period */],\n            }\n        });\n    }\n    run(accessor) {\n        const widgetService = accessor.get(IActionWidgetService);\n        if (widgetService instanceof ActionWidgetService) {\n            widgetService.acceptSelected();\n        }\n    }\n});\nregisterAction2(class extends Action2 {\n    constructor() {\n        super({\n            id: previewSelectedActionCommand,\n            title: localize2('previewSelected.title', \"Preview selected action\"),\n            precondition: ActionWidgetContextKeys.Visible,\n            keybinding: {\n                weight,\n                primary: 2048 /* KeyMod.CtrlCmd */ | 3 /* KeyCode.Enter */,\n            }\n        });\n    }\n    run(accessor) {\n        const widgetService = accessor.get(IActionWidgetService);\n        if (widgetService instanceof ActionWidgetService) {\n            widgetService.acceptSelected(true);\n        }\n    }\n});\n"], "mappings": "AAAA,IAAIA,UAAU,GAAI,IAAI,IAAI,IAAI,CAACA,UAAU,IAAK,UAAUC,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAE;EACnF,IAAIC,CAAC,GAAGC,SAAS,CAACC,MAAM;IAAEC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGH,MAAM,GAAGE,IAAI,KAAK,IAAI,GAAGA,IAAI,GAAGK,MAAM,CAACC,wBAAwB,CAACR,MAAM,EAAEC,GAAG,CAAC,GAAGC,IAAI;IAAEO,CAAC;EAC5H,IAAI,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,CAACC,QAAQ,KAAK,UAAU,EAAEL,CAAC,GAAGI,OAAO,CAACC,QAAQ,CAACZ,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,CAAC,CAAC,KAC1H,KAAK,IAAIU,CAAC,GAAGb,UAAU,CAACM,MAAM,GAAG,CAAC,EAAEO,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE,IAAIH,CAAC,GAAGV,UAAU,CAACa,CAAC,CAAC,EAAEN,CAAC,GAAG,CAACH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACH,CAAC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACT,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,GAAGG,CAAC,CAACT,MAAM,EAAEC,GAAG,CAAC,KAAKK,CAAC;EACjJ,OAAOH,CAAC,GAAG,CAAC,IAAIG,CAAC,IAAIC,MAAM,CAACM,cAAc,CAACb,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,EAAEA,CAAC;AACjE,CAAC;AACD,IAAIQ,OAAO,GAAI,IAAI,IAAI,IAAI,CAACA,OAAO,IAAK,UAAUC,UAAU,EAAEC,SAAS,EAAE;EACrE,OAAO,UAAUhB,MAAM,EAAEC,GAAG,EAAE;IAAEe,SAAS,CAAChB,MAAM,EAAEC,GAAG,EAAEc,UAAU,CAAC;EAAE,CAAC;AACzE,CAAC;AACD;AACA;AACA;AACA;AACA,OAAO,KAAKE,GAAG,MAAM,8BAA8B;AACnD,SAASC,SAAS,QAAQ,iDAAiD;AAC3E,SAASC,UAAU,EAAEC,eAAe,EAAEC,iBAAiB,QAAQ,mCAAmC;AAClG,OAAO,oBAAoB;AAC3B,SAASC,QAAQ,EAAEC,SAAS,QAAQ,iBAAiB;AACrD,SAASC,2BAA2B,EAAEC,UAAU,EAAEC,4BAA4B,QAAQ,iBAAiB;AACvG,SAASC,OAAO,EAAEC,eAAe,QAAQ,iCAAiC;AAC1E,SAASC,kBAAkB,EAAEC,aAAa,QAAQ,uCAAuC;AACzF,SAASC,mBAAmB,QAAQ,0CAA0C;AAC9E,SAASC,iBAAiB,QAAQ,0CAA0C;AAC5E,SAASC,eAAe,EAAEC,qBAAqB,QAAQ,6CAA6C;AACpG,SAASC,2BAA2B,EAAEC,aAAa,QAAQ,qCAAqC;AAChGA,aAAa,CAAC,6BAA6B,EAAED,2BAA2B,EAAEb,QAAQ,CAAC,6BAA6B,EAAE,0DAA0D,CAAC,CAAC;AAC9K,MAAMe,uBAAuB,GAAG;EAC5BC,OAAO,EAAE,IAAIR,aAAa,CAAC,uBAAuB,EAAE,KAAK,EAAER,QAAQ,CAAC,uBAAuB,EAAE,2CAA2C,CAAC;AAC7I,CAAC;AACD,OAAO,MAAMiB,oBAAoB,GAAGN,eAAe,CAAC,qBAAqB,CAAC;AAC1E,IAAIO,mBAAmB,GAAG,MAAMA,mBAAmB,SAASrB,UAAU,CAAC;EACnE,IAAIsB,SAASA,CAAA,EAAG;IACZ,OAAOJ,uBAAuB,CAACC,OAAO,CAACI,QAAQ,CAAC,IAAI,CAACC,kBAAkB,CAAC,IAAI,KAAK;EACrF;EACAC,WAAWA,CAACC,mBAAmB,EAAEF,kBAAkB,EAAEG,qBAAqB,EAAE;IACxE,KAAK,CAAC,CAAC;IACP,IAAI,CAACD,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACF,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACG,qBAAqB,GAAGA,qBAAqB;IAClD,IAAI,CAACC,KAAK,GAAG,IAAI,CAACC,SAAS,CAAC,IAAI3B,iBAAiB,CAAC,CAAC,CAAC;EACxD;EACA4B,IAAIA,CAACC,IAAI,EAAEC,eAAe,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,gBAAgB,EAAE;IAC9E,MAAMC,cAAc,GAAGpB,uBAAuB,CAACC,OAAO,CAACoB,MAAM,CAAC,IAAI,CAACf,kBAAkB,CAAC;IACtF,MAAMgB,IAAI,GAAG,IAAI,CAACb,qBAAqB,CAACc,cAAc,CAACnC,UAAU,EAAEyB,IAAI,EAAEC,eAAe,EAAEC,KAAK,EAAEC,QAAQ,CAAC;IAC1G,IAAI,CAACR,mBAAmB,CAACgB,eAAe,CAAC;MACrCC,SAAS,EAAEA,CAAA,KAAMR,MAAM;MACvBS,MAAM,EAAGR,SAAS,IAAK;QACnBE,cAAc,CAACO,GAAG,CAAC,IAAI,CAAC;QACxB,OAAO,IAAI,CAACC,aAAa,CAACV,SAAS,EAAEI,IAAI,EAAEH,gBAAgB,IAAI,EAAE,CAAC;MACtE,CAAC;MACDU,MAAM,EAAGC,SAAS,IAAK;QACnBV,cAAc,CAACW,KAAK,CAAC,CAAC;QACtB,IAAI,CAACC,eAAe,CAACF,SAAS,CAAC;MACnC;IACJ,CAAC,EAAEZ,SAAS,EAAE,KAAK,CAAC;EACxB;EACAe,cAAcA,CAACC,OAAO,EAAE;IACpB,IAAI,CAACxB,KAAK,CAACyB,KAAK,EAAEF,cAAc,CAACC,OAAO,CAAC;EAC7C;EACAE,aAAaA,CAAA,EAAG;IACZ,IAAI,CAAC1B,KAAK,EAAEyB,KAAK,EAAEC,aAAa,CAAC,CAAC;EACtC;EACAC,SAASA,CAAA,EAAG;IACR,IAAI,CAAC3B,KAAK,EAAEyB,KAAK,EAAEE,SAAS,CAAC,CAAC;EAClC;EACAC,IAAIA,CAACR,SAAS,EAAE;IACZ,IAAI,CAACpB,KAAK,CAACyB,KAAK,EAAEG,IAAI,CAACR,SAAS,CAAC;IACjC,IAAI,CAACpB,KAAK,CAAC6B,KAAK,CAAC,CAAC;EACtB;EACAX,aAAaA,CAACY,OAAO,EAAElB,IAAI,EAAEH,gBAAgB,EAAE;IAC3C,MAAMsB,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC5CF,MAAM,CAACG,SAAS,CAACC,GAAG,CAAC,eAAe,CAAC;IACrCL,OAAO,CAACM,WAAW,CAACL,MAAM,CAAC;IAC3B,IAAI,CAAC/B,KAAK,CAACyB,KAAK,GAAGb,IAAI;IACvB,IAAI,IAAI,CAACZ,KAAK,CAACyB,KAAK,EAAE;MAClBM,MAAM,CAACK,WAAW,CAAC,IAAI,CAACpC,KAAK,CAACyB,KAAK,CAACY,OAAO,CAAC;IAChD,CAAC,MACI;MACD,MAAM,IAAIC,KAAK,CAAC,mBAAmB,CAAC;IACxC;IACA,MAAMC,iBAAiB,GAAG,IAAIlE,eAAe,CAAC,CAAC;IAC/C;IACA,MAAMmE,SAAS,GAAGR,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC/C,MAAMQ,KAAK,GAAGX,OAAO,CAACM,WAAW,CAACI,SAAS,CAAC;IAC5CC,KAAK,CAACP,SAAS,CAACC,GAAG,CAAC,oBAAoB,CAAC;IACzCI,iBAAiB,CAACJ,GAAG,CAACjE,GAAG,CAACwE,qBAAqB,CAACD,KAAK,EAAEvE,GAAG,CAACyE,SAAS,CAACC,UAAU,EAAEC,CAAC,IAAIA,CAAC,CAACC,eAAe,CAAC,CAAC,CAAC,CAAC;IAC3G;IACA,MAAMC,eAAe,GAAGf,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IACrD,MAAMe,YAAY,GAAGlB,OAAO,CAACM,WAAW,CAACW,eAAe,CAAC;IACzDC,YAAY,CAACd,SAAS,CAACC,GAAG,CAAC,2BAA2B,CAAC;IACvD;IACAI,iBAAiB,CAACJ,GAAG,CAACjE,GAAG,CAACwE,qBAAqB,CAACM,YAAY,EAAE9E,GAAG,CAACyE,SAAS,CAACM,YAAY,EAAE,MAAMD,YAAY,CAACE,MAAM,CAAC,CAAC,CAAC,CAAC;IACvHX,iBAAiB,CAACJ,GAAG,CAACjE,GAAG,CAACwE,qBAAqB,CAACM,YAAY,EAAE9E,GAAG,CAACyE,SAAS,CAACC,UAAU,EAAE,MAAMI,YAAY,CAACE,MAAM,CAAC,CAAC,CAAC,CAAC;IACrH;IACA,IAAIC,cAAc,GAAG,CAAC;IACtB,IAAI1C,gBAAgB,CAACnD,MAAM,EAAE;MACzB,MAAM8F,SAAS,GAAG,IAAI,CAACC,gBAAgB,CAAC,2BAA2B,EAAE5C,gBAAgB,CAAC;MACtF,IAAI2C,SAAS,EAAE;QACXrB,MAAM,CAACK,WAAW,CAACgB,SAAS,CAACE,YAAY,CAAC,CAAC,CAACC,aAAa,CAAC;QAC1DhB,iBAAiB,CAACJ,GAAG,CAACiB,SAAS,CAAC;QAChCD,cAAc,GAAGC,SAAS,CAACE,YAAY,CAAC,CAAC,CAACE,WAAW;MACzD;IACJ;IACA,MAAMC,KAAK,GAAG,IAAI,CAACzD,KAAK,CAACyB,KAAK,EAAEiC,MAAM,CAACP,cAAc,CAAC;IACtDpB,MAAM,CAAC4B,KAAK,CAACF,KAAK,GAAG,GAAGA,KAAK,IAAI;IACjC,MAAMG,YAAY,GAAGrB,iBAAiB,CAACJ,GAAG,CAACjE,GAAG,CAAC2F,UAAU,CAAC/B,OAAO,CAAC,CAAC;IACnES,iBAAiB,CAACJ,GAAG,CAACyB,YAAY,CAACE,SAAS,CAAC,MAAM,IAAI,CAAClC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACpE,OAAOW,iBAAiB;EAC5B;EACAc,gBAAgBA,CAACU,SAAS,EAAEC,OAAO,EAAE;IACjC,IAAI,CAACA,OAAO,CAAC1G,MAAM,EAAE;MACjB,OAAO2G,SAAS;IACpB;IACA,MAAMzD,SAAS,GAAGtC,GAAG,CAACgG,CAAC,CAACH,SAAS,CAAC;IAClC,MAAMX,SAAS,GAAG,IAAIjF,SAAS,CAACqC,SAAS,CAAC;IAC1C4C,SAAS,CAACe,IAAI,CAACH,OAAO,EAAE;MAAEI,IAAI,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAK,CAAC,CAAC;IACrD,OAAOjB,SAAS;EACpB;EACA9B,eAAeA,CAACF,SAAS,EAAE;IACvB,IAAI,CAACpB,KAAK,CAACyB,KAAK,EAAEG,IAAI,CAACR,SAAS,CAAC;EACrC;AACJ,CAAC;AACD3B,mBAAmB,GAAG1C,UAAU,CAAC,CAC7BgB,OAAO,CAAC,CAAC,EAAEiB,mBAAmB,CAAC,EAC/BjB,OAAO,CAAC,CAAC,EAAEe,kBAAkB,CAAC,EAC9Bf,OAAO,CAAC,CAAC,EAAEoB,qBAAqB,CAAC,CACpC,EAAEM,mBAAmB,CAAC;AACvBR,iBAAiB,CAACO,oBAAoB,EAAEC,mBAAmB,EAAE,CAAC,CAAC,+BAA+B,CAAC;AAC/F,MAAM6E,MAAM,GAAG,GAAG,CAAC,uCAAuC,IAAI;AAC9DzF,eAAe,CAAC,cAAcD,OAAO,CAAC;EAClCiB,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC;MACF0E,EAAE,EAAE,sBAAsB;MAC1BC,KAAK,EAAEhG,SAAS,CAAC,4BAA4B,EAAE,oBAAoB,CAAC;MACpEiG,YAAY,EAAEnF,uBAAuB,CAACC,OAAO;MAC7CmF,UAAU,EAAE;QACRJ,MAAM;QACNK,OAAO,EAAE,CAAC,CAAC;QACXC,SAAS,EAAE,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;MAC5C;IACJ,CAAC,CAAC;EACN;EACAC,GAAGA,CAACC,QAAQ,EAAE;IACVA,QAAQ,CAACC,GAAG,CAACvF,oBAAoB,CAAC,CAACoC,IAAI,CAAC,IAAI,CAAC;EACjD;AACJ,CAAC,CAAC;AACF/C,eAAe,CAAC,cAAcD,OAAO,CAAC;EAClCiB,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC;MACF0E,EAAE,EAAE,sBAAsB;MAC1BC,KAAK,EAAEhG,SAAS,CAAC,4BAA4B,EAAE,wBAAwB,CAAC;MACxEiG,YAAY,EAAEnF,uBAAuB,CAACC,OAAO;MAC7CmF,UAAU,EAAE;QACRJ,MAAM;QACNK,OAAO,EAAE,EAAE,CAAC;QACZC,SAAS,EAAE,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC,sBAAsB;QACjEI,GAAG,EAAE;UAAEL,OAAO,EAAE,EAAE,CAAC;UAAuBC,SAAS,EAAE,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC,uBAAuB,GAAG,CAAC,uBAAuB,EAAE,CAAC;QAAoB;MAClK;IACJ,CAAC,CAAC;EACN;EACAC,GAAGA,CAACC,QAAQ,EAAE;IACV,MAAMG,aAAa,GAAGH,QAAQ,CAACC,GAAG,CAACvF,oBAAoB,CAAC;IACxD,IAAIyF,aAAa,YAAYxF,mBAAmB,EAAE;MAC9CwF,aAAa,CAACvD,aAAa,CAAC,CAAC;IACjC;EACJ;AACJ,CAAC,CAAC;AACF7C,eAAe,CAAC,cAAcD,OAAO,CAAC;EAClCiB,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC;MACF0E,EAAE,EAAE,sBAAsB;MAC1BC,KAAK,EAAEhG,SAAS,CAAC,4BAA4B,EAAE,oBAAoB,CAAC;MACpEiG,YAAY,EAAEnF,uBAAuB,CAACC,OAAO;MAC7CmF,UAAU,EAAE;QACRJ,MAAM;QACNK,OAAO,EAAE,EAAE,CAAC;QACZC,SAAS,EAAE,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC,wBAAwB;QACnEI,GAAG,EAAE;UAAEL,OAAO,EAAE,EAAE,CAAC;UAAyBC,SAAS,EAAE,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC,yBAAyB,GAAG,CAAC,uBAAuB,EAAE,CAAC;QAAoB;MACtK;IACJ,CAAC,CAAC;EACN;EACAC,GAAGA,CAACC,QAAQ,EAAE;IACV,MAAMG,aAAa,GAAGH,QAAQ,CAACC,GAAG,CAACvF,oBAAoB,CAAC;IACxD,IAAIyF,aAAa,YAAYxF,mBAAmB,EAAE;MAC9CwF,aAAa,CAACtD,SAAS,CAAC,CAAC;IAC7B;EACJ;AACJ,CAAC,CAAC;AACF9C,eAAe,CAAC,cAAcD,OAAO,CAAC;EAClCiB,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC;MACF0E,EAAE,EAAE9F,2BAA2B;MAC/B+F,KAAK,EAAEhG,SAAS,CAAC,sBAAsB,EAAE,wBAAwB,CAAC;MAClEiG,YAAY,EAAEnF,uBAAuB,CAACC,OAAO;MAC7CmF,UAAU,EAAE;QACRJ,MAAM;QACNK,OAAO,EAAE,CAAC,CAAC;QACXC,SAAS,EAAE,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC;MAC/C;IACJ,CAAC,CAAC;EACN;EACAC,GAAGA,CAACC,QAAQ,EAAE;IACV,MAAMG,aAAa,GAAGH,QAAQ,CAACC,GAAG,CAACvF,oBAAoB,CAAC;IACxD,IAAIyF,aAAa,YAAYxF,mBAAmB,EAAE;MAC9CwF,aAAa,CAAC1D,cAAc,CAAC,CAAC;IAClC;EACJ;AACJ,CAAC,CAAC;AACF1C,eAAe,CAAC,cAAcD,OAAO,CAAC;EAClCiB,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC;MACF0E,EAAE,EAAE5F,4BAA4B;MAChC6F,KAAK,EAAEhG,SAAS,CAAC,uBAAuB,EAAE,yBAAyB,CAAC;MACpEiG,YAAY,EAAEnF,uBAAuB,CAACC,OAAO;MAC7CmF,UAAU,EAAE;QACRJ,MAAM;QACNK,OAAO,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;MAC3C;IACJ,CAAC,CAAC;EACN;EACAE,GAAGA,CAACC,QAAQ,EAAE;IACV,MAAMG,aAAa,GAAGH,QAAQ,CAACC,GAAG,CAACvF,oBAAoB,CAAC;IACxD,IAAIyF,aAAa,YAAYxF,mBAAmB,EAAE;MAC9CwF,aAAa,CAAC1D,cAAc,CAAC,IAAI,CAAC;IACtC;EACJ;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}