{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as dom from '../../../../base/browser/dom.js';\nimport { Sash } from '../../../../base/browser/ui/sash/sash.js';\nimport { Color, RGBA } from '../../../../base/common/color.js';\nimport { IdGenerator } from '../../../../base/common/idGenerator.js';\nimport { DisposableStore } from '../../../../base/common/lifecycle.js';\nimport * as objects from '../../../../base/common/objects.js';\nimport './zoneWidget.css';\nimport { Range } from '../../../common/core/range.js';\nimport { ModelDecorationOptions } from '../../../common/model/textModel.js';\nconst defaultColor = new Color(new RGBA(0, 122, 204));\nconst defaultOptions = {\n  showArrow: true,\n  showFrame: true,\n  className: '',\n  frameColor: defaultColor,\n  arrowColor: defaultColor,\n  keepEditorSelection: false\n};\nconst WIDGET_ID = 'vs.editor.contrib.zoneWidget';\nclass ViewZoneDelegate {\n  constructor(domNode, afterLineNumber, afterColumn, heightInLines, onDomNodeTop, onComputedHeight, showInHiddenAreas, ordinal) {\n    this.id = ''; // A valid zone id should be greater than 0\n    this.domNode = domNode;\n    this.afterLineNumber = afterLineNumber;\n    this.afterColumn = afterColumn;\n    this.heightInLines = heightInLines;\n    this.showInHiddenAreas = showInHiddenAreas;\n    this.ordinal = ordinal;\n    this._onDomNodeTop = onDomNodeTop;\n    this._onComputedHeight = onComputedHeight;\n  }\n  onDomNodeTop(top) {\n    this._onDomNodeTop(top);\n  }\n  onComputedHeight(height) {\n    this._onComputedHeight(height);\n  }\n}\nexport class OverlayWidgetDelegate {\n  constructor(id, domNode) {\n    this._id = id;\n    this._domNode = domNode;\n  }\n  getId() {\n    return this._id;\n  }\n  getDomNode() {\n    return this._domNode;\n  }\n  getPosition() {\n    return null;\n  }\n}\nclass Arrow {\n  static {\n    this._IdGenerator = new IdGenerator('.arrow-decoration-');\n  }\n  constructor(_editor) {\n    this._editor = _editor;\n    this._ruleName = Arrow._IdGenerator.nextId();\n    this._decorations = this._editor.createDecorationsCollection();\n    this._color = null;\n    this._height = -1;\n  }\n  dispose() {\n    this.hide();\n    dom.removeCSSRulesContainingSelector(this._ruleName);\n  }\n  set color(value) {\n    if (this._color !== value) {\n      this._color = value;\n      this._updateStyle();\n    }\n  }\n  set height(value) {\n    if (this._height !== value) {\n      this._height = value;\n      this._updateStyle();\n    }\n  }\n  _updateStyle() {\n    dom.removeCSSRulesContainingSelector(this._ruleName);\n    dom.createCSSRule(`.monaco-editor ${this._ruleName}`, `border-style: solid; border-color: transparent; border-bottom-color: ${this._color}; border-width: ${this._height}px; bottom: -${this._height}px !important; margin-left: -${this._height}px; `);\n  }\n  show(where) {\n    if (where.column === 1) {\n      // the arrow isn't pretty at column 1 and we need to push it out a little\n      where = {\n        lineNumber: where.lineNumber,\n        column: 2\n      };\n    }\n    this._decorations.set([{\n      range: Range.fromPositions(where),\n      options: {\n        description: 'zone-widget-arrow',\n        className: this._ruleName,\n        stickiness: 1 /* TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges */\n      }\n    }]);\n  }\n  hide() {\n    this._decorations.clear();\n  }\n}\nexport class ZoneWidget {\n  constructor(editor, options = {}) {\n    this._arrow = null;\n    this._overlayWidget = null;\n    this._resizeSash = null;\n    this._viewZone = null;\n    this._disposables = new DisposableStore();\n    this.container = null;\n    this._isShowing = false;\n    this.editor = editor;\n    this._positionMarkerId = this.editor.createDecorationsCollection();\n    this.options = objects.deepClone(options);\n    objects.mixin(this.options, defaultOptions, false);\n    this.domNode = document.createElement('div');\n    if (!this.options.isAccessible) {\n      this.domNode.setAttribute('aria-hidden', 'true');\n      this.domNode.setAttribute('role', 'presentation');\n    }\n    this._disposables.add(this.editor.onDidLayoutChange(info => {\n      const width = this._getWidth(info);\n      this.domNode.style.width = width + 'px';\n      this.domNode.style.left = this._getLeft(info) + 'px';\n      this._onWidth(width);\n    }));\n  }\n  dispose() {\n    if (this._overlayWidget) {\n      this.editor.removeOverlayWidget(this._overlayWidget);\n      this._overlayWidget = null;\n    }\n    if (this._viewZone) {\n      this.editor.changeViewZones(accessor => {\n        if (this._viewZone) {\n          accessor.removeZone(this._viewZone.id);\n        }\n        this._viewZone = null;\n      });\n    }\n    this._positionMarkerId.clear();\n    this._disposables.dispose();\n  }\n  create() {\n    this.domNode.classList.add('zone-widget');\n    if (this.options.className) {\n      this.domNode.classList.add(this.options.className);\n    }\n    this.container = document.createElement('div');\n    this.container.classList.add('zone-widget-container');\n    this.domNode.appendChild(this.container);\n    if (this.options.showArrow) {\n      this._arrow = new Arrow(this.editor);\n      this._disposables.add(this._arrow);\n    }\n    this._fillContainer(this.container);\n    this._initSash();\n    this._applyStyles();\n  }\n  style(styles) {\n    if (styles.frameColor) {\n      this.options.frameColor = styles.frameColor;\n    }\n    if (styles.arrowColor) {\n      this.options.arrowColor = styles.arrowColor;\n    }\n    this._applyStyles();\n  }\n  _applyStyles() {\n    if (this.container && this.options.frameColor) {\n      const frameColor = this.options.frameColor.toString();\n      this.container.style.borderTopColor = frameColor;\n      this.container.style.borderBottomColor = frameColor;\n    }\n    if (this._arrow && this.options.arrowColor) {\n      const arrowColor = this.options.arrowColor.toString();\n      this._arrow.color = arrowColor;\n    }\n  }\n  _getWidth(info) {\n    return info.width - info.minimap.minimapWidth - info.verticalScrollbarWidth;\n  }\n  _getLeft(info) {\n    // If minimap is to the left, we move beyond it\n    if (info.minimap.minimapWidth > 0 && info.minimap.minimapLeft === 0) {\n      return info.minimap.minimapWidth;\n    }\n    return 0;\n  }\n  _onViewZoneTop(top) {\n    this.domNode.style.top = top + 'px';\n  }\n  _onViewZoneHeight(height) {\n    this.domNode.style.height = `${height}px`;\n    if (this.container) {\n      const containerHeight = height - this._decoratingElementsHeight();\n      this.container.style.height = `${containerHeight}px`;\n      const layoutInfo = this.editor.getLayoutInfo();\n      this._doLayout(containerHeight, this._getWidth(layoutInfo));\n    }\n    this._resizeSash?.layout();\n  }\n  get position() {\n    const range = this._positionMarkerId.getRange(0);\n    if (!range) {\n      return undefined;\n    }\n    return range.getStartPosition();\n  }\n  show(rangeOrPos, heightInLines) {\n    const range = Range.isIRange(rangeOrPos) ? Range.lift(rangeOrPos) : Range.fromPositions(rangeOrPos);\n    this._isShowing = true;\n    this._showImpl(range, heightInLines);\n    this._isShowing = false;\n    this._positionMarkerId.set([{\n      range,\n      options: ModelDecorationOptions.EMPTY\n    }]);\n  }\n  hide() {\n    if (this._viewZone) {\n      this.editor.changeViewZones(accessor => {\n        if (this._viewZone) {\n          accessor.removeZone(this._viewZone.id);\n        }\n      });\n      this._viewZone = null;\n    }\n    if (this._overlayWidget) {\n      this.editor.removeOverlayWidget(this._overlayWidget);\n      this._overlayWidget = null;\n    }\n    this._arrow?.hide();\n    this._positionMarkerId.clear();\n  }\n  _decoratingElementsHeight() {\n    const lineHeight = this.editor.getOption(67 /* EditorOption.lineHeight */);\n    let result = 0;\n    if (this.options.showArrow) {\n      const arrowHeight = Math.round(lineHeight / 3);\n      result += 2 * arrowHeight;\n    }\n    if (this.options.showFrame) {\n      const frameThickness = Math.round(lineHeight / 9);\n      result += 2 * frameThickness;\n    }\n    return result;\n  }\n  _showImpl(where, heightInLines) {\n    const position = where.getStartPosition();\n    const layoutInfo = this.editor.getLayoutInfo();\n    const width = this._getWidth(layoutInfo);\n    this.domNode.style.width = `${width}px`;\n    this.domNode.style.left = this._getLeft(layoutInfo) + 'px';\n    // Render the widget as zone (rendering) and widget (lifecycle)\n    const viewZoneDomNode = document.createElement('div');\n    viewZoneDomNode.style.overflow = 'hidden';\n    const lineHeight = this.editor.getOption(67 /* EditorOption.lineHeight */);\n    // adjust heightInLines to viewport\n    if (!this.options.allowUnlimitedHeight) {\n      const maxHeightInLines = Math.max(12, this.editor.getLayoutInfo().height / lineHeight * 0.8);\n      heightInLines = Math.min(heightInLines, maxHeightInLines);\n    }\n    let arrowHeight = 0;\n    let frameThickness = 0;\n    // Render the arrow one 1/3 of an editor line height\n    if (this._arrow && this.options.showArrow) {\n      arrowHeight = Math.round(lineHeight / 3);\n      this._arrow.height = arrowHeight;\n      this._arrow.show(position);\n    }\n    // Render the frame as 1/9 of an editor line height\n    if (this.options.showFrame) {\n      frameThickness = Math.round(lineHeight / 9);\n    }\n    // insert zone widget\n    this.editor.changeViewZones(accessor => {\n      if (this._viewZone) {\n        accessor.removeZone(this._viewZone.id);\n      }\n      if (this._overlayWidget) {\n        this.editor.removeOverlayWidget(this._overlayWidget);\n        this._overlayWidget = null;\n      }\n      this.domNode.style.top = '-1000px';\n      this._viewZone = new ViewZoneDelegate(viewZoneDomNode, position.lineNumber, position.column, heightInLines, top => this._onViewZoneTop(top), height => this._onViewZoneHeight(height), this.options.showInHiddenAreas, this.options.ordinal);\n      this._viewZone.id = accessor.addZone(this._viewZone);\n      this._overlayWidget = new OverlayWidgetDelegate(WIDGET_ID + this._viewZone.id, this.domNode);\n      this.editor.addOverlayWidget(this._overlayWidget);\n    });\n    if (this.container && this.options.showFrame) {\n      const width = this.options.frameWidth ? this.options.frameWidth : frameThickness;\n      this.container.style.borderTopWidth = width + 'px';\n      this.container.style.borderBottomWidth = width + 'px';\n    }\n    const containerHeight = heightInLines * lineHeight - this._decoratingElementsHeight();\n    if (this.container) {\n      this.container.style.top = arrowHeight + 'px';\n      this.container.style.height = containerHeight + 'px';\n      this.container.style.overflow = 'hidden';\n    }\n    this._doLayout(containerHeight, width);\n    if (!this.options.keepEditorSelection) {\n      this.editor.setSelection(where);\n    }\n    const model = this.editor.getModel();\n    if (model) {\n      const range = model.validateRange(new Range(where.startLineNumber, 1, where.endLineNumber + 1, 1));\n      this.revealRange(range, range.startLineNumber === model.getLineCount());\n    }\n  }\n  revealRange(range, isLastLine) {\n    if (isLastLine) {\n      this.editor.revealLineNearTop(range.endLineNumber, 0 /* ScrollType.Smooth */);\n    } else {\n      this.editor.revealRange(range, 0 /* ScrollType.Smooth */);\n    }\n  }\n  setCssClass(className, classToReplace) {\n    if (!this.container) {\n      return;\n    }\n    if (classToReplace) {\n      this.container.classList.remove(classToReplace);\n    }\n    this.container.classList.add(className);\n  }\n  _onWidth(widthInPixel) {\n    // implement in subclass\n  }\n  _doLayout(heightInPixel, widthInPixel) {\n    // implement in subclass\n  }\n  _relayout(newHeightInLines) {\n    if (this._viewZone && this._viewZone.heightInLines !== newHeightInLines) {\n      this.editor.changeViewZones(accessor => {\n        if (this._viewZone) {\n          this._viewZone.heightInLines = newHeightInLines;\n          accessor.layoutZone(this._viewZone.id);\n        }\n      });\n    }\n  }\n  // --- sash\n  _initSash() {\n    if (this._resizeSash) {\n      return;\n    }\n    this._resizeSash = this._disposables.add(new Sash(this.domNode, this, {\n      orientation: 1 /* Orientation.HORIZONTAL */\n    }));\n    if (!this.options.isResizeable) {\n      this._resizeSash.state = 0 /* SashState.Disabled */;\n    }\n    let data;\n    this._disposables.add(this._resizeSash.onDidStart(e => {\n      if (this._viewZone) {\n        data = {\n          startY: e.startY,\n          heightInLines: this._viewZone.heightInLines\n        };\n      }\n    }));\n    this._disposables.add(this._resizeSash.onDidEnd(() => {\n      data = undefined;\n    }));\n    this._disposables.add(this._resizeSash.onDidChange(evt => {\n      if (data) {\n        const lineDelta = (evt.currentY - data.startY) / this.editor.getOption(67 /* EditorOption.lineHeight */);\n        const roundedLineDelta = lineDelta < 0 ? Math.ceil(lineDelta) : Math.floor(lineDelta);\n        const newHeightInLines = data.heightInLines + roundedLineDelta;\n        if (newHeightInLines > 5 && newHeightInLines < 35) {\n          this._relayout(newHeightInLines);\n        }\n      }\n    }));\n  }\n  getHorizontalSashLeft() {\n    return 0;\n  }\n  getHorizontalSashTop() {\n    return (this.domNode.style.height === null ? 0 : parseInt(this.domNode.style.height)) - this._decoratingElementsHeight() / 2;\n  }\n  getHorizontalSashWidth() {\n    const layoutInfo = this.editor.getLayoutInfo();\n    return layoutInfo.width - layoutInfo.minimap.minimapWidth;\n  }\n}", "map": {"version": 3, "names": ["dom", "<PERSON>sh", "Color", "RGBA", "IdGenerator", "DisposableStore", "objects", "Range", "ModelDecorationOptions", "defaultColor", "defaultOptions", "showArrow", "showFrame", "className", "frameColor", "arrowColor", "keepEditorSelection", "WIDGET_ID", "ViewZoneDelegate", "constructor", "domNode", "afterLineNumber", "afterColumn", "heightInLines", "onDomNodeTop", "onComputedHeight", "showInHiddenAreas", "ordinal", "id", "_onDomNodeTop", "_onComputedHeight", "top", "height", "OverlayWidgetDelegate", "_id", "_domNode", "getId", "getDomNode", "getPosition", "Arrow", "_IdGenerator", "_editor", "_ruleName", "nextId", "_decorations", "createDecorationsCollection", "_color", "_height", "dispose", "hide", "removeCSSRulesContainingSelector", "color", "value", "_updateStyle", "createCSSRule", "show", "where", "column", "lineNumber", "set", "range", "fromPositions", "options", "description", "stickiness", "clear", "ZoneWidget", "editor", "_arrow", "_overlayWidget", "_resizeSash", "_viewZone", "_disposables", "container", "_isShowing", "_positionMarkerId", "deepClone", "mixin", "document", "createElement", "isAccessible", "setAttribute", "add", "onDidLayoutChange", "info", "width", "_getWidth", "style", "left", "_getLeft", "_onWidth", "removeOverlayWidget", "changeViewZones", "accessor", "removeZone", "create", "classList", "append<PERSON><PERSON><PERSON>", "_fill<PERSON><PERSON>r", "_initSash", "_applyStyles", "styles", "toString", "borderTopColor", "borderBottomColor", "minimap", "minimapWidth", "verticalScrollbarWidth", "minimapLeft", "_onViewZoneTop", "_onViewZoneHeight", "containerHeight", "_decoratingElementsHeight", "layoutInfo", "getLayoutInfo", "_doLayout", "layout", "position", "getRange", "undefined", "getStartPosition", "rangeOrPos", "isIRange", "lift", "_showImpl", "EMPTY", "lineHeight", "getOption", "result", "arrowHeight", "Math", "round", "frameThickness", "viewZoneDomNode", "overflow", "allowUnlimitedHeight", "maxHeightInLines", "max", "min", "addZone", "addOverlayWidget", "frameWidth", "borderTopWidth", "borderBottomWidth", "setSelection", "model", "getModel", "validate<PERSON><PERSON><PERSON>", "startLineNumber", "endLineNumber", "revealRange", "getLineCount", "isLastLine", "revealLineNearTop", "setCssClass", "classToReplace", "remove", "widthInPixel", "heightInPixel", "_relayout", "newHeightInLines", "layoutZone", "orientation", "isResizeable", "state", "data", "onDidStart", "e", "startY", "onDidEnd", "onDidChange", "evt", "lineDelta", "currentY", "roundedLineDelta", "ceil", "floor", "getHorizontalSashLeft", "getHorizontalSashTop", "parseInt", "getHorizontalSashWidth"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/contrib/zoneWidget/browser/zoneWidget.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as dom from '../../../../base/browser/dom.js';\nimport { Sash } from '../../../../base/browser/ui/sash/sash.js';\nimport { Color, RGBA } from '../../../../base/common/color.js';\nimport { IdGenerator } from '../../../../base/common/idGenerator.js';\nimport { DisposableStore } from '../../../../base/common/lifecycle.js';\nimport * as objects from '../../../../base/common/objects.js';\nimport './zoneWidget.css';\nimport { Range } from '../../../common/core/range.js';\nimport { ModelDecorationOptions } from '../../../common/model/textModel.js';\nconst defaultColor = new Color(new RGBA(0, 122, 204));\nconst defaultOptions = {\n    showArrow: true,\n    showFrame: true,\n    className: '',\n    frameColor: defaultColor,\n    arrowColor: defaultColor,\n    keepEditorSelection: false\n};\nconst WIDGET_ID = 'vs.editor.contrib.zoneWidget';\nclass ViewZoneDelegate {\n    constructor(domNode, afterLineNumber, afterColumn, heightInLines, onDomNodeTop, onComputedHeight, showInHiddenAreas, ordinal) {\n        this.id = ''; // A valid zone id should be greater than 0\n        this.domNode = domNode;\n        this.afterLineNumber = afterLineNumber;\n        this.afterColumn = afterColumn;\n        this.heightInLines = heightInLines;\n        this.showInHiddenAreas = showInHiddenAreas;\n        this.ordinal = ordinal;\n        this._onDomNodeTop = onDomNodeTop;\n        this._onComputedHeight = onComputedHeight;\n    }\n    onDomNodeTop(top) {\n        this._onDomNodeTop(top);\n    }\n    onComputedHeight(height) {\n        this._onComputedHeight(height);\n    }\n}\nexport class OverlayWidgetDelegate {\n    constructor(id, domNode) {\n        this._id = id;\n        this._domNode = domNode;\n    }\n    getId() {\n        return this._id;\n    }\n    getDomNode() {\n        return this._domNode;\n    }\n    getPosition() {\n        return null;\n    }\n}\nclass Arrow {\n    static { this._IdGenerator = new IdGenerator('.arrow-decoration-'); }\n    constructor(_editor) {\n        this._editor = _editor;\n        this._ruleName = Arrow._IdGenerator.nextId();\n        this._decorations = this._editor.createDecorationsCollection();\n        this._color = null;\n        this._height = -1;\n    }\n    dispose() {\n        this.hide();\n        dom.removeCSSRulesContainingSelector(this._ruleName);\n    }\n    set color(value) {\n        if (this._color !== value) {\n            this._color = value;\n            this._updateStyle();\n        }\n    }\n    set height(value) {\n        if (this._height !== value) {\n            this._height = value;\n            this._updateStyle();\n        }\n    }\n    _updateStyle() {\n        dom.removeCSSRulesContainingSelector(this._ruleName);\n        dom.createCSSRule(`.monaco-editor ${this._ruleName}`, `border-style: solid; border-color: transparent; border-bottom-color: ${this._color}; border-width: ${this._height}px; bottom: -${this._height}px !important; margin-left: -${this._height}px; `);\n    }\n    show(where) {\n        if (where.column === 1) {\n            // the arrow isn't pretty at column 1 and we need to push it out a little\n            where = { lineNumber: where.lineNumber, column: 2 };\n        }\n        this._decorations.set([{\n                range: Range.fromPositions(where),\n                options: {\n                    description: 'zone-widget-arrow',\n                    className: this._ruleName,\n                    stickiness: 1 /* TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges */\n                }\n            }]);\n    }\n    hide() {\n        this._decorations.clear();\n    }\n}\nexport class ZoneWidget {\n    constructor(editor, options = {}) {\n        this._arrow = null;\n        this._overlayWidget = null;\n        this._resizeSash = null;\n        this._viewZone = null;\n        this._disposables = new DisposableStore();\n        this.container = null;\n        this._isShowing = false;\n        this.editor = editor;\n        this._positionMarkerId = this.editor.createDecorationsCollection();\n        this.options = objects.deepClone(options);\n        objects.mixin(this.options, defaultOptions, false);\n        this.domNode = document.createElement('div');\n        if (!this.options.isAccessible) {\n            this.domNode.setAttribute('aria-hidden', 'true');\n            this.domNode.setAttribute('role', 'presentation');\n        }\n        this._disposables.add(this.editor.onDidLayoutChange((info) => {\n            const width = this._getWidth(info);\n            this.domNode.style.width = width + 'px';\n            this.domNode.style.left = this._getLeft(info) + 'px';\n            this._onWidth(width);\n        }));\n    }\n    dispose() {\n        if (this._overlayWidget) {\n            this.editor.removeOverlayWidget(this._overlayWidget);\n            this._overlayWidget = null;\n        }\n        if (this._viewZone) {\n            this.editor.changeViewZones(accessor => {\n                if (this._viewZone) {\n                    accessor.removeZone(this._viewZone.id);\n                }\n                this._viewZone = null;\n            });\n        }\n        this._positionMarkerId.clear();\n        this._disposables.dispose();\n    }\n    create() {\n        this.domNode.classList.add('zone-widget');\n        if (this.options.className) {\n            this.domNode.classList.add(this.options.className);\n        }\n        this.container = document.createElement('div');\n        this.container.classList.add('zone-widget-container');\n        this.domNode.appendChild(this.container);\n        if (this.options.showArrow) {\n            this._arrow = new Arrow(this.editor);\n            this._disposables.add(this._arrow);\n        }\n        this._fillContainer(this.container);\n        this._initSash();\n        this._applyStyles();\n    }\n    style(styles) {\n        if (styles.frameColor) {\n            this.options.frameColor = styles.frameColor;\n        }\n        if (styles.arrowColor) {\n            this.options.arrowColor = styles.arrowColor;\n        }\n        this._applyStyles();\n    }\n    _applyStyles() {\n        if (this.container && this.options.frameColor) {\n            const frameColor = this.options.frameColor.toString();\n            this.container.style.borderTopColor = frameColor;\n            this.container.style.borderBottomColor = frameColor;\n        }\n        if (this._arrow && this.options.arrowColor) {\n            const arrowColor = this.options.arrowColor.toString();\n            this._arrow.color = arrowColor;\n        }\n    }\n    _getWidth(info) {\n        return info.width - info.minimap.minimapWidth - info.verticalScrollbarWidth;\n    }\n    _getLeft(info) {\n        // If minimap is to the left, we move beyond it\n        if (info.minimap.minimapWidth > 0 && info.minimap.minimapLeft === 0) {\n            return info.minimap.minimapWidth;\n        }\n        return 0;\n    }\n    _onViewZoneTop(top) {\n        this.domNode.style.top = top + 'px';\n    }\n    _onViewZoneHeight(height) {\n        this.domNode.style.height = `${height}px`;\n        if (this.container) {\n            const containerHeight = height - this._decoratingElementsHeight();\n            this.container.style.height = `${containerHeight}px`;\n            const layoutInfo = this.editor.getLayoutInfo();\n            this._doLayout(containerHeight, this._getWidth(layoutInfo));\n        }\n        this._resizeSash?.layout();\n    }\n    get position() {\n        const range = this._positionMarkerId.getRange(0);\n        if (!range) {\n            return undefined;\n        }\n        return range.getStartPosition();\n    }\n    show(rangeOrPos, heightInLines) {\n        const range = Range.isIRange(rangeOrPos) ? Range.lift(rangeOrPos) : Range.fromPositions(rangeOrPos);\n        this._isShowing = true;\n        this._showImpl(range, heightInLines);\n        this._isShowing = false;\n        this._positionMarkerId.set([{ range, options: ModelDecorationOptions.EMPTY }]);\n    }\n    hide() {\n        if (this._viewZone) {\n            this.editor.changeViewZones(accessor => {\n                if (this._viewZone) {\n                    accessor.removeZone(this._viewZone.id);\n                }\n            });\n            this._viewZone = null;\n        }\n        if (this._overlayWidget) {\n            this.editor.removeOverlayWidget(this._overlayWidget);\n            this._overlayWidget = null;\n        }\n        this._arrow?.hide();\n        this._positionMarkerId.clear();\n    }\n    _decoratingElementsHeight() {\n        const lineHeight = this.editor.getOption(67 /* EditorOption.lineHeight */);\n        let result = 0;\n        if (this.options.showArrow) {\n            const arrowHeight = Math.round(lineHeight / 3);\n            result += 2 * arrowHeight;\n        }\n        if (this.options.showFrame) {\n            const frameThickness = Math.round(lineHeight / 9);\n            result += 2 * frameThickness;\n        }\n        return result;\n    }\n    _showImpl(where, heightInLines) {\n        const position = where.getStartPosition();\n        const layoutInfo = this.editor.getLayoutInfo();\n        const width = this._getWidth(layoutInfo);\n        this.domNode.style.width = `${width}px`;\n        this.domNode.style.left = this._getLeft(layoutInfo) + 'px';\n        // Render the widget as zone (rendering) and widget (lifecycle)\n        const viewZoneDomNode = document.createElement('div');\n        viewZoneDomNode.style.overflow = 'hidden';\n        const lineHeight = this.editor.getOption(67 /* EditorOption.lineHeight */);\n        // adjust heightInLines to viewport\n        if (!this.options.allowUnlimitedHeight) {\n            const maxHeightInLines = Math.max(12, (this.editor.getLayoutInfo().height / lineHeight) * 0.8);\n            heightInLines = Math.min(heightInLines, maxHeightInLines);\n        }\n        let arrowHeight = 0;\n        let frameThickness = 0;\n        // Render the arrow one 1/3 of an editor line height\n        if (this._arrow && this.options.showArrow) {\n            arrowHeight = Math.round(lineHeight / 3);\n            this._arrow.height = arrowHeight;\n            this._arrow.show(position);\n        }\n        // Render the frame as 1/9 of an editor line height\n        if (this.options.showFrame) {\n            frameThickness = Math.round(lineHeight / 9);\n        }\n        // insert zone widget\n        this.editor.changeViewZones((accessor) => {\n            if (this._viewZone) {\n                accessor.removeZone(this._viewZone.id);\n            }\n            if (this._overlayWidget) {\n                this.editor.removeOverlayWidget(this._overlayWidget);\n                this._overlayWidget = null;\n            }\n            this.domNode.style.top = '-1000px';\n            this._viewZone = new ViewZoneDelegate(viewZoneDomNode, position.lineNumber, position.column, heightInLines, (top) => this._onViewZoneTop(top), (height) => this._onViewZoneHeight(height), this.options.showInHiddenAreas, this.options.ordinal);\n            this._viewZone.id = accessor.addZone(this._viewZone);\n            this._overlayWidget = new OverlayWidgetDelegate(WIDGET_ID + this._viewZone.id, this.domNode);\n            this.editor.addOverlayWidget(this._overlayWidget);\n        });\n        if (this.container && this.options.showFrame) {\n            const width = this.options.frameWidth ? this.options.frameWidth : frameThickness;\n            this.container.style.borderTopWidth = width + 'px';\n            this.container.style.borderBottomWidth = width + 'px';\n        }\n        const containerHeight = heightInLines * lineHeight - this._decoratingElementsHeight();\n        if (this.container) {\n            this.container.style.top = arrowHeight + 'px';\n            this.container.style.height = containerHeight + 'px';\n            this.container.style.overflow = 'hidden';\n        }\n        this._doLayout(containerHeight, width);\n        if (!this.options.keepEditorSelection) {\n            this.editor.setSelection(where);\n        }\n        const model = this.editor.getModel();\n        if (model) {\n            const range = model.validateRange(new Range(where.startLineNumber, 1, where.endLineNumber + 1, 1));\n            this.revealRange(range, range.startLineNumber === model.getLineCount());\n        }\n    }\n    revealRange(range, isLastLine) {\n        if (isLastLine) {\n            this.editor.revealLineNearTop(range.endLineNumber, 0 /* ScrollType.Smooth */);\n        }\n        else {\n            this.editor.revealRange(range, 0 /* ScrollType.Smooth */);\n        }\n    }\n    setCssClass(className, classToReplace) {\n        if (!this.container) {\n            return;\n        }\n        if (classToReplace) {\n            this.container.classList.remove(classToReplace);\n        }\n        this.container.classList.add(className);\n    }\n    _onWidth(widthInPixel) {\n        // implement in subclass\n    }\n    _doLayout(heightInPixel, widthInPixel) {\n        // implement in subclass\n    }\n    _relayout(newHeightInLines) {\n        if (this._viewZone && this._viewZone.heightInLines !== newHeightInLines) {\n            this.editor.changeViewZones(accessor => {\n                if (this._viewZone) {\n                    this._viewZone.heightInLines = newHeightInLines;\n                    accessor.layoutZone(this._viewZone.id);\n                }\n            });\n        }\n    }\n    // --- sash\n    _initSash() {\n        if (this._resizeSash) {\n            return;\n        }\n        this._resizeSash = this._disposables.add(new Sash(this.domNode, this, { orientation: 1 /* Orientation.HORIZONTAL */ }));\n        if (!this.options.isResizeable) {\n            this._resizeSash.state = 0 /* SashState.Disabled */;\n        }\n        let data;\n        this._disposables.add(this._resizeSash.onDidStart((e) => {\n            if (this._viewZone) {\n                data = {\n                    startY: e.startY,\n                    heightInLines: this._viewZone.heightInLines,\n                };\n            }\n        }));\n        this._disposables.add(this._resizeSash.onDidEnd(() => {\n            data = undefined;\n        }));\n        this._disposables.add(this._resizeSash.onDidChange((evt) => {\n            if (data) {\n                const lineDelta = (evt.currentY - data.startY) / this.editor.getOption(67 /* EditorOption.lineHeight */);\n                const roundedLineDelta = lineDelta < 0 ? Math.ceil(lineDelta) : Math.floor(lineDelta);\n                const newHeightInLines = data.heightInLines + roundedLineDelta;\n                if (newHeightInLines > 5 && newHeightInLines < 35) {\n                    this._relayout(newHeightInLines);\n                }\n            }\n        }));\n    }\n    getHorizontalSashLeft() {\n        return 0;\n    }\n    getHorizontalSashTop() {\n        return (this.domNode.style.height === null ? 0 : parseInt(this.domNode.style.height)) - (this._decoratingElementsHeight() / 2);\n    }\n    getHorizontalSashWidth() {\n        const layoutInfo = this.editor.getLayoutInfo();\n        return layoutInfo.width - layoutInfo.minimap.minimapWidth;\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,KAAKA,GAAG,MAAM,iCAAiC;AACtD,SAASC,IAAI,QAAQ,0CAA0C;AAC/D,SAASC,KAAK,EAAEC,IAAI,QAAQ,kCAAkC;AAC9D,SAASC,WAAW,QAAQ,wCAAwC;AACpE,SAASC,eAAe,QAAQ,sCAAsC;AACtE,OAAO,KAAKC,OAAO,MAAM,oCAAoC;AAC7D,OAAO,kBAAkB;AACzB,SAASC,KAAK,QAAQ,+BAA+B;AACrD,SAASC,sBAAsB,QAAQ,oCAAoC;AAC3E,MAAMC,YAAY,GAAG,IAAIP,KAAK,CAAC,IAAIC,IAAI,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACrD,MAAMO,cAAc,GAAG;EACnBC,SAAS,EAAE,IAAI;EACfC,SAAS,EAAE,IAAI;EACfC,SAAS,EAAE,EAAE;EACbC,UAAU,EAAEL,YAAY;EACxBM,UAAU,EAAEN,YAAY;EACxBO,mBAAmB,EAAE;AACzB,CAAC;AACD,MAAMC,SAAS,GAAG,8BAA8B;AAChD,MAAMC,gBAAgB,CAAC;EACnBC,WAAWA,CAACC,OAAO,EAAEC,eAAe,EAAEC,WAAW,EAAEC,aAAa,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,iBAAiB,EAAEC,OAAO,EAAE;IAC1H,IAAI,CAACC,EAAE,GAAG,EAAE,CAAC,CAAC;IACd,IAAI,CAACR,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACG,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACE,aAAa,GAAGL,YAAY;IACjC,IAAI,CAACM,iBAAiB,GAAGL,gBAAgB;EAC7C;EACAD,YAAYA,CAACO,GAAG,EAAE;IACd,IAAI,CAACF,aAAa,CAACE,GAAG,CAAC;EAC3B;EACAN,gBAAgBA,CAACO,MAAM,EAAE;IACrB,IAAI,CAACF,iBAAiB,CAACE,MAAM,CAAC;EAClC;AACJ;AACA,OAAO,MAAMC,qBAAqB,CAAC;EAC/Bd,WAAWA,CAACS,EAAE,EAAER,OAAO,EAAE;IACrB,IAAI,CAACc,GAAG,GAAGN,EAAE;IACb,IAAI,CAACO,QAAQ,GAAGf,OAAO;EAC3B;EACAgB,KAAKA,CAAA,EAAG;IACJ,OAAO,IAAI,CAACF,GAAG;EACnB;EACAG,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAACF,QAAQ;EACxB;EACAG,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI;EACf;AACJ;AACA,MAAMC,KAAK,CAAC;EACR;IAAS,IAAI,CAACC,YAAY,GAAG,IAAIpC,WAAW,CAAC,oBAAoB,CAAC;EAAE;EACpEe,WAAWA,CAACsB,OAAO,EAAE;IACjB,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,SAAS,GAAGH,KAAK,CAACC,YAAY,CAACG,MAAM,CAAC,CAAC;IAC5C,IAAI,CAACC,YAAY,GAAG,IAAI,CAACH,OAAO,CAACI,2BAA2B,CAAC,CAAC;IAC9D,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,OAAO,GAAG,CAAC,CAAC;EACrB;EACAC,OAAOA,CAAA,EAAG;IACN,IAAI,CAACC,IAAI,CAAC,CAAC;IACXjD,GAAG,CAACkD,gCAAgC,CAAC,IAAI,CAACR,SAAS,CAAC;EACxD;EACA,IAAIS,KAAKA,CAACC,KAAK,EAAE;IACb,IAAI,IAAI,CAACN,MAAM,KAAKM,KAAK,EAAE;MACvB,IAAI,CAACN,MAAM,GAAGM,KAAK;MACnB,IAAI,CAACC,YAAY,CAAC,CAAC;IACvB;EACJ;EACA,IAAIrB,MAAMA,CAACoB,KAAK,EAAE;IACd,IAAI,IAAI,CAACL,OAAO,KAAKK,KAAK,EAAE;MACxB,IAAI,CAACL,OAAO,GAAGK,KAAK;MACpB,IAAI,CAACC,YAAY,CAAC,CAAC;IACvB;EACJ;EACAA,YAAYA,CAAA,EAAG;IACXrD,GAAG,CAACkD,gCAAgC,CAAC,IAAI,CAACR,SAAS,CAAC;IACpD1C,GAAG,CAACsD,aAAa,CAAC,kBAAkB,IAAI,CAACZ,SAAS,EAAE,EAAE,wEAAwE,IAAI,CAACI,MAAM,mBAAmB,IAAI,CAACC,OAAO,gBAAgB,IAAI,CAACA,OAAO,gCAAgC,IAAI,CAACA,OAAO,MAAM,CAAC;EAC3P;EACAQ,IAAIA,CAACC,KAAK,EAAE;IACR,IAAIA,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;MACpB;MACAD,KAAK,GAAG;QAAEE,UAAU,EAAEF,KAAK,CAACE,UAAU;QAAED,MAAM,EAAE;MAAE,CAAC;IACvD;IACA,IAAI,CAACb,YAAY,CAACe,GAAG,CAAC,CAAC;MACfC,KAAK,EAAErD,KAAK,CAACsD,aAAa,CAACL,KAAK,CAAC;MACjCM,OAAO,EAAE;QACLC,WAAW,EAAE,mBAAmB;QAChClD,SAAS,EAAE,IAAI,CAAC6B,SAAS;QACzBsB,UAAU,EAAE,CAAC,CAAC;MAClB;IACJ,CAAC,CAAC,CAAC;EACX;EACAf,IAAIA,CAAA,EAAG;IACH,IAAI,CAACL,YAAY,CAACqB,KAAK,CAAC,CAAC;EAC7B;AACJ;AACA,OAAO,MAAMC,UAAU,CAAC;EACpB/C,WAAWA,CAACgD,MAAM,EAAEL,OAAO,GAAG,CAAC,CAAC,EAAE;IAC9B,IAAI,CAACM,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACC,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,YAAY,GAAG,IAAInE,eAAe,CAAC,CAAC;IACzC,IAAI,CAACoE,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACP,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACQ,iBAAiB,GAAG,IAAI,CAACR,MAAM,CAACtB,2BAA2B,CAAC,CAAC;IAClE,IAAI,CAACiB,OAAO,GAAGxD,OAAO,CAACsE,SAAS,CAACd,OAAO,CAAC;IACzCxD,OAAO,CAACuE,KAAK,CAAC,IAAI,CAACf,OAAO,EAAEpD,cAAc,EAAE,KAAK,CAAC;IAClD,IAAI,CAACU,OAAO,GAAG0D,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC5C,IAAI,CAAC,IAAI,CAACjB,OAAO,CAACkB,YAAY,EAAE;MAC5B,IAAI,CAAC5D,OAAO,CAAC6D,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;MAChD,IAAI,CAAC7D,OAAO,CAAC6D,YAAY,CAAC,MAAM,EAAE,cAAc,CAAC;IACrD;IACA,IAAI,CAACT,YAAY,CAACU,GAAG,CAAC,IAAI,CAACf,MAAM,CAACgB,iBAAiB,CAAEC,IAAI,IAAK;MAC1D,MAAMC,KAAK,GAAG,IAAI,CAACC,SAAS,CAACF,IAAI,CAAC;MAClC,IAAI,CAAChE,OAAO,CAACmE,KAAK,CAACF,KAAK,GAAGA,KAAK,GAAG,IAAI;MACvC,IAAI,CAACjE,OAAO,CAACmE,KAAK,CAACC,IAAI,GAAG,IAAI,CAACC,QAAQ,CAACL,IAAI,CAAC,GAAG,IAAI;MACpD,IAAI,CAACM,QAAQ,CAACL,KAAK,CAAC;IACxB,CAAC,CAAC,CAAC;EACP;EACArC,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACqB,cAAc,EAAE;MACrB,IAAI,CAACF,MAAM,CAACwB,mBAAmB,CAAC,IAAI,CAACtB,cAAc,CAAC;MACpD,IAAI,CAACA,cAAc,GAAG,IAAI;IAC9B;IACA,IAAI,IAAI,CAACE,SAAS,EAAE;MAChB,IAAI,CAACJ,MAAM,CAACyB,eAAe,CAACC,QAAQ,IAAI;QACpC,IAAI,IAAI,CAACtB,SAAS,EAAE;UAChBsB,QAAQ,CAACC,UAAU,CAAC,IAAI,CAACvB,SAAS,CAAC3C,EAAE,CAAC;QAC1C;QACA,IAAI,CAAC2C,SAAS,GAAG,IAAI;MACzB,CAAC,CAAC;IACN;IACA,IAAI,CAACI,iBAAiB,CAACV,KAAK,CAAC,CAAC;IAC9B,IAAI,CAACO,YAAY,CAACxB,OAAO,CAAC,CAAC;EAC/B;EACA+C,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC3E,OAAO,CAAC4E,SAAS,CAACd,GAAG,CAAC,aAAa,CAAC;IACzC,IAAI,IAAI,CAACpB,OAAO,CAACjD,SAAS,EAAE;MACxB,IAAI,CAACO,OAAO,CAAC4E,SAAS,CAACd,GAAG,CAAC,IAAI,CAACpB,OAAO,CAACjD,SAAS,CAAC;IACtD;IACA,IAAI,CAAC4D,SAAS,GAAGK,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC9C,IAAI,CAACN,SAAS,CAACuB,SAAS,CAACd,GAAG,CAAC,uBAAuB,CAAC;IACrD,IAAI,CAAC9D,OAAO,CAAC6E,WAAW,CAAC,IAAI,CAACxB,SAAS,CAAC;IACxC,IAAI,IAAI,CAACX,OAAO,CAACnD,SAAS,EAAE;MACxB,IAAI,CAACyD,MAAM,GAAG,IAAI7B,KAAK,CAAC,IAAI,CAAC4B,MAAM,CAAC;MACpC,IAAI,CAACK,YAAY,CAACU,GAAG,CAAC,IAAI,CAACd,MAAM,CAAC;IACtC;IACA,IAAI,CAAC8B,cAAc,CAAC,IAAI,CAACzB,SAAS,CAAC;IACnC,IAAI,CAAC0B,SAAS,CAAC,CAAC;IAChB,IAAI,CAACC,YAAY,CAAC,CAAC;EACvB;EACAb,KAAKA,CAACc,MAAM,EAAE;IACV,IAAIA,MAAM,CAACvF,UAAU,EAAE;MACnB,IAAI,CAACgD,OAAO,CAAChD,UAAU,GAAGuF,MAAM,CAACvF,UAAU;IAC/C;IACA,IAAIuF,MAAM,CAACtF,UAAU,EAAE;MACnB,IAAI,CAAC+C,OAAO,CAAC/C,UAAU,GAAGsF,MAAM,CAACtF,UAAU;IAC/C;IACA,IAAI,CAACqF,YAAY,CAAC,CAAC;EACvB;EACAA,YAAYA,CAAA,EAAG;IACX,IAAI,IAAI,CAAC3B,SAAS,IAAI,IAAI,CAACX,OAAO,CAAChD,UAAU,EAAE;MAC3C,MAAMA,UAAU,GAAG,IAAI,CAACgD,OAAO,CAAChD,UAAU,CAACwF,QAAQ,CAAC,CAAC;MACrD,IAAI,CAAC7B,SAAS,CAACc,KAAK,CAACgB,cAAc,GAAGzF,UAAU;MAChD,IAAI,CAAC2D,SAAS,CAACc,KAAK,CAACiB,iBAAiB,GAAG1F,UAAU;IACvD;IACA,IAAI,IAAI,CAACsD,MAAM,IAAI,IAAI,CAACN,OAAO,CAAC/C,UAAU,EAAE;MACxC,MAAMA,UAAU,GAAG,IAAI,CAAC+C,OAAO,CAAC/C,UAAU,CAACuF,QAAQ,CAAC,CAAC;MACrD,IAAI,CAAClC,MAAM,CAACjB,KAAK,GAAGpC,UAAU;IAClC;EACJ;EACAuE,SAASA,CAACF,IAAI,EAAE;IACZ,OAAOA,IAAI,CAACC,KAAK,GAAGD,IAAI,CAACqB,OAAO,CAACC,YAAY,GAAGtB,IAAI,CAACuB,sBAAsB;EAC/E;EACAlB,QAAQA,CAACL,IAAI,EAAE;IACX;IACA,IAAIA,IAAI,CAACqB,OAAO,CAACC,YAAY,GAAG,CAAC,IAAItB,IAAI,CAACqB,OAAO,CAACG,WAAW,KAAK,CAAC,EAAE;MACjE,OAAOxB,IAAI,CAACqB,OAAO,CAACC,YAAY;IACpC;IACA,OAAO,CAAC;EACZ;EACAG,cAAcA,CAAC9E,GAAG,EAAE;IAChB,IAAI,CAACX,OAAO,CAACmE,KAAK,CAACxD,GAAG,GAAGA,GAAG,GAAG,IAAI;EACvC;EACA+E,iBAAiBA,CAAC9E,MAAM,EAAE;IACtB,IAAI,CAACZ,OAAO,CAACmE,KAAK,CAACvD,MAAM,GAAG,GAAGA,MAAM,IAAI;IACzC,IAAI,IAAI,CAACyC,SAAS,EAAE;MAChB,MAAMsC,eAAe,GAAG/E,MAAM,GAAG,IAAI,CAACgF,yBAAyB,CAAC,CAAC;MACjE,IAAI,CAACvC,SAAS,CAACc,KAAK,CAACvD,MAAM,GAAG,GAAG+E,eAAe,IAAI;MACpD,MAAME,UAAU,GAAG,IAAI,CAAC9C,MAAM,CAAC+C,aAAa,CAAC,CAAC;MAC9C,IAAI,CAACC,SAAS,CAACJ,eAAe,EAAE,IAAI,CAACzB,SAAS,CAAC2B,UAAU,CAAC,CAAC;IAC/D;IACA,IAAI,CAAC3C,WAAW,EAAE8C,MAAM,CAAC,CAAC;EAC9B;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,MAAMzD,KAAK,GAAG,IAAI,CAACe,iBAAiB,CAAC2C,QAAQ,CAAC,CAAC,CAAC;IAChD,IAAI,CAAC1D,KAAK,EAAE;MACR,OAAO2D,SAAS;IACpB;IACA,OAAO3D,KAAK,CAAC4D,gBAAgB,CAAC,CAAC;EACnC;EACAjE,IAAIA,CAACkE,UAAU,EAAElG,aAAa,EAAE;IAC5B,MAAMqC,KAAK,GAAGrD,KAAK,CAACmH,QAAQ,CAACD,UAAU,CAAC,GAAGlH,KAAK,CAACoH,IAAI,CAACF,UAAU,CAAC,GAAGlH,KAAK,CAACsD,aAAa,CAAC4D,UAAU,CAAC;IACnG,IAAI,CAAC/C,UAAU,GAAG,IAAI;IACtB,IAAI,CAACkD,SAAS,CAAChE,KAAK,EAAErC,aAAa,CAAC;IACpC,IAAI,CAACmD,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,iBAAiB,CAAChB,GAAG,CAAC,CAAC;MAAEC,KAAK;MAAEE,OAAO,EAAEtD,sBAAsB,CAACqH;IAAM,CAAC,CAAC,CAAC;EAClF;EACA5E,IAAIA,CAAA,EAAG;IACH,IAAI,IAAI,CAACsB,SAAS,EAAE;MAChB,IAAI,CAACJ,MAAM,CAACyB,eAAe,CAACC,QAAQ,IAAI;QACpC,IAAI,IAAI,CAACtB,SAAS,EAAE;UAChBsB,QAAQ,CAACC,UAAU,CAAC,IAAI,CAACvB,SAAS,CAAC3C,EAAE,CAAC;QAC1C;MACJ,CAAC,CAAC;MACF,IAAI,CAAC2C,SAAS,GAAG,IAAI;IACzB;IACA,IAAI,IAAI,CAACF,cAAc,EAAE;MACrB,IAAI,CAACF,MAAM,CAACwB,mBAAmB,CAAC,IAAI,CAACtB,cAAc,CAAC;MACpD,IAAI,CAACA,cAAc,GAAG,IAAI;IAC9B;IACA,IAAI,CAACD,MAAM,EAAEnB,IAAI,CAAC,CAAC;IACnB,IAAI,CAAC0B,iBAAiB,CAACV,KAAK,CAAC,CAAC;EAClC;EACA+C,yBAAyBA,CAAA,EAAG;IACxB,MAAMc,UAAU,GAAG,IAAI,CAAC3D,MAAM,CAAC4D,SAAS,CAAC,EAAE,CAAC,6BAA6B,CAAC;IAC1E,IAAIC,MAAM,GAAG,CAAC;IACd,IAAI,IAAI,CAAClE,OAAO,CAACnD,SAAS,EAAE;MACxB,MAAMsH,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACL,UAAU,GAAG,CAAC,CAAC;MAC9CE,MAAM,IAAI,CAAC,GAAGC,WAAW;IAC7B;IACA,IAAI,IAAI,CAACnE,OAAO,CAAClD,SAAS,EAAE;MACxB,MAAMwH,cAAc,GAAGF,IAAI,CAACC,KAAK,CAACL,UAAU,GAAG,CAAC,CAAC;MACjDE,MAAM,IAAI,CAAC,GAAGI,cAAc;IAChC;IACA,OAAOJ,MAAM;EACjB;EACAJ,SAASA,CAACpE,KAAK,EAAEjC,aAAa,EAAE;IAC5B,MAAM8F,QAAQ,GAAG7D,KAAK,CAACgE,gBAAgB,CAAC,CAAC;IACzC,MAAMP,UAAU,GAAG,IAAI,CAAC9C,MAAM,CAAC+C,aAAa,CAAC,CAAC;IAC9C,MAAM7B,KAAK,GAAG,IAAI,CAACC,SAAS,CAAC2B,UAAU,CAAC;IACxC,IAAI,CAAC7F,OAAO,CAACmE,KAAK,CAACF,KAAK,GAAG,GAAGA,KAAK,IAAI;IACvC,IAAI,CAACjE,OAAO,CAACmE,KAAK,CAACC,IAAI,GAAG,IAAI,CAACC,QAAQ,CAACwB,UAAU,CAAC,GAAG,IAAI;IAC1D;IACA,MAAMoB,eAAe,GAAGvD,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IACrDsD,eAAe,CAAC9C,KAAK,CAAC+C,QAAQ,GAAG,QAAQ;IACzC,MAAMR,UAAU,GAAG,IAAI,CAAC3D,MAAM,CAAC4D,SAAS,CAAC,EAAE,CAAC,6BAA6B,CAAC;IAC1E;IACA,IAAI,CAAC,IAAI,CAACjE,OAAO,CAACyE,oBAAoB,EAAE;MACpC,MAAMC,gBAAgB,GAAGN,IAAI,CAACO,GAAG,CAAC,EAAE,EAAG,IAAI,CAACtE,MAAM,CAAC+C,aAAa,CAAC,CAAC,CAAClF,MAAM,GAAG8F,UAAU,GAAI,GAAG,CAAC;MAC9FvG,aAAa,GAAG2G,IAAI,CAACQ,GAAG,CAACnH,aAAa,EAAEiH,gBAAgB,CAAC;IAC7D;IACA,IAAIP,WAAW,GAAG,CAAC;IACnB,IAAIG,cAAc,GAAG,CAAC;IACtB;IACA,IAAI,IAAI,CAAChE,MAAM,IAAI,IAAI,CAACN,OAAO,CAACnD,SAAS,EAAE;MACvCsH,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACL,UAAU,GAAG,CAAC,CAAC;MACxC,IAAI,CAAC1D,MAAM,CAACpC,MAAM,GAAGiG,WAAW;MAChC,IAAI,CAAC7D,MAAM,CAACb,IAAI,CAAC8D,QAAQ,CAAC;IAC9B;IACA;IACA,IAAI,IAAI,CAACvD,OAAO,CAAClD,SAAS,EAAE;MACxBwH,cAAc,GAAGF,IAAI,CAACC,KAAK,CAACL,UAAU,GAAG,CAAC,CAAC;IAC/C;IACA;IACA,IAAI,CAAC3D,MAAM,CAACyB,eAAe,CAAEC,QAAQ,IAAK;MACtC,IAAI,IAAI,CAACtB,SAAS,EAAE;QAChBsB,QAAQ,CAACC,UAAU,CAAC,IAAI,CAACvB,SAAS,CAAC3C,EAAE,CAAC;MAC1C;MACA,IAAI,IAAI,CAACyC,cAAc,EAAE;QACrB,IAAI,CAACF,MAAM,CAACwB,mBAAmB,CAAC,IAAI,CAACtB,cAAc,CAAC;QACpD,IAAI,CAACA,cAAc,GAAG,IAAI;MAC9B;MACA,IAAI,CAACjD,OAAO,CAACmE,KAAK,CAACxD,GAAG,GAAG,SAAS;MAClC,IAAI,CAACwC,SAAS,GAAG,IAAIrD,gBAAgB,CAACmH,eAAe,EAAEhB,QAAQ,CAAC3D,UAAU,EAAE2D,QAAQ,CAAC5D,MAAM,EAAElC,aAAa,EAAGQ,GAAG,IAAK,IAAI,CAAC8E,cAAc,CAAC9E,GAAG,CAAC,EAAGC,MAAM,IAAK,IAAI,CAAC8E,iBAAiB,CAAC9E,MAAM,CAAC,EAAE,IAAI,CAAC8B,OAAO,CAACpC,iBAAiB,EAAE,IAAI,CAACoC,OAAO,CAACnC,OAAO,CAAC;MAChP,IAAI,CAAC4C,SAAS,CAAC3C,EAAE,GAAGiE,QAAQ,CAAC8C,OAAO,CAAC,IAAI,CAACpE,SAAS,CAAC;MACpD,IAAI,CAACF,cAAc,GAAG,IAAIpC,qBAAqB,CAAChB,SAAS,GAAG,IAAI,CAACsD,SAAS,CAAC3C,EAAE,EAAE,IAAI,CAACR,OAAO,CAAC;MAC5F,IAAI,CAAC+C,MAAM,CAACyE,gBAAgB,CAAC,IAAI,CAACvE,cAAc,CAAC;IACrD,CAAC,CAAC;IACF,IAAI,IAAI,CAACI,SAAS,IAAI,IAAI,CAACX,OAAO,CAAClD,SAAS,EAAE;MAC1C,MAAMyE,KAAK,GAAG,IAAI,CAACvB,OAAO,CAAC+E,UAAU,GAAG,IAAI,CAAC/E,OAAO,CAAC+E,UAAU,GAAGT,cAAc;MAChF,IAAI,CAAC3D,SAAS,CAACc,KAAK,CAACuD,cAAc,GAAGzD,KAAK,GAAG,IAAI;MAClD,IAAI,CAACZ,SAAS,CAACc,KAAK,CAACwD,iBAAiB,GAAG1D,KAAK,GAAG,IAAI;IACzD;IACA,MAAM0B,eAAe,GAAGxF,aAAa,GAAGuG,UAAU,GAAG,IAAI,CAACd,yBAAyB,CAAC,CAAC;IACrF,IAAI,IAAI,CAACvC,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,CAACc,KAAK,CAACxD,GAAG,GAAGkG,WAAW,GAAG,IAAI;MAC7C,IAAI,CAACxD,SAAS,CAACc,KAAK,CAACvD,MAAM,GAAG+E,eAAe,GAAG,IAAI;MACpD,IAAI,CAACtC,SAAS,CAACc,KAAK,CAAC+C,QAAQ,GAAG,QAAQ;IAC5C;IACA,IAAI,CAACnB,SAAS,CAACJ,eAAe,EAAE1B,KAAK,CAAC;IACtC,IAAI,CAAC,IAAI,CAACvB,OAAO,CAAC9C,mBAAmB,EAAE;MACnC,IAAI,CAACmD,MAAM,CAAC6E,YAAY,CAACxF,KAAK,CAAC;IACnC;IACA,MAAMyF,KAAK,GAAG,IAAI,CAAC9E,MAAM,CAAC+E,QAAQ,CAAC,CAAC;IACpC,IAAID,KAAK,EAAE;MACP,MAAMrF,KAAK,GAAGqF,KAAK,CAACE,aAAa,CAAC,IAAI5I,KAAK,CAACiD,KAAK,CAAC4F,eAAe,EAAE,CAAC,EAAE5F,KAAK,CAAC6F,aAAa,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;MAClG,IAAI,CAACC,WAAW,CAAC1F,KAAK,EAAEA,KAAK,CAACwF,eAAe,KAAKH,KAAK,CAACM,YAAY,CAAC,CAAC,CAAC;IAC3E;EACJ;EACAD,WAAWA,CAAC1F,KAAK,EAAE4F,UAAU,EAAE;IAC3B,IAAIA,UAAU,EAAE;MACZ,IAAI,CAACrF,MAAM,CAACsF,iBAAiB,CAAC7F,KAAK,CAACyF,aAAa,EAAE,CAAC,CAAC,uBAAuB,CAAC;IACjF,CAAC,MACI;MACD,IAAI,CAAClF,MAAM,CAACmF,WAAW,CAAC1F,KAAK,EAAE,CAAC,CAAC,uBAAuB,CAAC;IAC7D;EACJ;EACA8F,WAAWA,CAAC7I,SAAS,EAAE8I,cAAc,EAAE;IACnC,IAAI,CAAC,IAAI,CAAClF,SAAS,EAAE;MACjB;IACJ;IACA,IAAIkF,cAAc,EAAE;MAChB,IAAI,CAAClF,SAAS,CAACuB,SAAS,CAAC4D,MAAM,CAACD,cAAc,CAAC;IACnD;IACA,IAAI,CAAClF,SAAS,CAACuB,SAAS,CAACd,GAAG,CAACrE,SAAS,CAAC;EAC3C;EACA6E,QAAQA,CAACmE,YAAY,EAAE;IACnB;EAAA;EAEJ1C,SAASA,CAAC2C,aAAa,EAAED,YAAY,EAAE;IACnC;EAAA;EAEJE,SAASA,CAACC,gBAAgB,EAAE;IACxB,IAAI,IAAI,CAACzF,SAAS,IAAI,IAAI,CAACA,SAAS,CAAChD,aAAa,KAAKyI,gBAAgB,EAAE;MACrE,IAAI,CAAC7F,MAAM,CAACyB,eAAe,CAACC,QAAQ,IAAI;QACpC,IAAI,IAAI,CAACtB,SAAS,EAAE;UAChB,IAAI,CAACA,SAAS,CAAChD,aAAa,GAAGyI,gBAAgB;UAC/CnE,QAAQ,CAACoE,UAAU,CAAC,IAAI,CAAC1F,SAAS,CAAC3C,EAAE,CAAC;QAC1C;MACJ,CAAC,CAAC;IACN;EACJ;EACA;EACAuE,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAAC7B,WAAW,EAAE;MAClB;IACJ;IACA,IAAI,CAACA,WAAW,GAAG,IAAI,CAACE,YAAY,CAACU,GAAG,CAAC,IAAIjF,IAAI,CAAC,IAAI,CAACmB,OAAO,EAAE,IAAI,EAAE;MAAE8I,WAAW,EAAE,CAAC,CAAC;IAA6B,CAAC,CAAC,CAAC;IACvH,IAAI,CAAC,IAAI,CAACpG,OAAO,CAACqG,YAAY,EAAE;MAC5B,IAAI,CAAC7F,WAAW,CAAC8F,KAAK,GAAG,CAAC,CAAC;IAC/B;IACA,IAAIC,IAAI;IACR,IAAI,CAAC7F,YAAY,CAACU,GAAG,CAAC,IAAI,CAACZ,WAAW,CAACgG,UAAU,CAAEC,CAAC,IAAK;MACrD,IAAI,IAAI,CAAChG,SAAS,EAAE;QAChB8F,IAAI,GAAG;UACHG,MAAM,EAAED,CAAC,CAACC,MAAM;UAChBjJ,aAAa,EAAE,IAAI,CAACgD,SAAS,CAAChD;QAClC,CAAC;MACL;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,CAACiD,YAAY,CAACU,GAAG,CAAC,IAAI,CAACZ,WAAW,CAACmG,QAAQ,CAAC,MAAM;MAClDJ,IAAI,GAAG9C,SAAS;IACpB,CAAC,CAAC,CAAC;IACH,IAAI,CAAC/C,YAAY,CAACU,GAAG,CAAC,IAAI,CAACZ,WAAW,CAACoG,WAAW,CAAEC,GAAG,IAAK;MACxD,IAAIN,IAAI,EAAE;QACN,MAAMO,SAAS,GAAG,CAACD,GAAG,CAACE,QAAQ,GAAGR,IAAI,CAACG,MAAM,IAAI,IAAI,CAACrG,MAAM,CAAC4D,SAAS,CAAC,EAAE,CAAC,6BAA6B,CAAC;QACxG,MAAM+C,gBAAgB,GAAGF,SAAS,GAAG,CAAC,GAAG1C,IAAI,CAAC6C,IAAI,CAACH,SAAS,CAAC,GAAG1C,IAAI,CAAC8C,KAAK,CAACJ,SAAS,CAAC;QACrF,MAAMZ,gBAAgB,GAAGK,IAAI,CAAC9I,aAAa,GAAGuJ,gBAAgB;QAC9D,IAAId,gBAAgB,GAAG,CAAC,IAAIA,gBAAgB,GAAG,EAAE,EAAE;UAC/C,IAAI,CAACD,SAAS,CAACC,gBAAgB,CAAC;QACpC;MACJ;IACJ,CAAC,CAAC,CAAC;EACP;EACAiB,qBAAqBA,CAAA,EAAG;IACpB,OAAO,CAAC;EACZ;EACAC,oBAAoBA,CAAA,EAAG;IACnB,OAAO,CAAC,IAAI,CAAC9J,OAAO,CAACmE,KAAK,CAACvD,MAAM,KAAK,IAAI,GAAG,CAAC,GAAGmJ,QAAQ,CAAC,IAAI,CAAC/J,OAAO,CAACmE,KAAK,CAACvD,MAAM,CAAC,IAAK,IAAI,CAACgF,yBAAyB,CAAC,CAAC,GAAG,CAAE;EAClI;EACAoE,sBAAsBA,CAAA,EAAG;IACrB,MAAMnE,UAAU,GAAG,IAAI,CAAC9C,MAAM,CAAC+C,aAAa,CAAC,CAAC;IAC9C,OAAOD,UAAU,CAAC5B,KAAK,GAAG4B,UAAU,CAACR,OAAO,CAACC,YAAY;EAC7D;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}