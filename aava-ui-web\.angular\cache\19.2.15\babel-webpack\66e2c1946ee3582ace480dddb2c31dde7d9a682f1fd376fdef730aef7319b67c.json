{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { FloatHorizontalRange } from '../../view/renderingContext.js';\nexport class RangeUtil {\n  static _createRange() {\n    if (!this._handyReadyRange) {\n      this._handyReadyRange = document.createRange();\n    }\n    return this._handyReadyRange;\n  }\n  static _detachRange(range, endNode) {\n    // Move range out of the span node, <PERSON><PERSON> doesn't like having many ranges in\n    // the same spot and will act badly for lines containing dashes ('-')\n    range.selectNodeContents(endNode);\n  }\n  static _readClientRects(startElement, startOffset, endElement, endOffset, endNode) {\n    const range = this._createRange();\n    try {\n      range.setStart(startElement, startOffset);\n      range.setEnd(endElement, endOffset);\n      return range.getClientRects();\n    } catch (e) {\n      // This is life ...\n      return null;\n    } finally {\n      this._detachRange(range, endNode);\n    }\n  }\n  static _mergeAdjacentRanges(ranges) {\n    if (ranges.length === 1) {\n      // There is nothing to merge\n      return ranges;\n    }\n    ranges.sort(FloatHorizontalRange.compare);\n    const result = [];\n    let resultLen = 0;\n    let prev = ranges[0];\n    for (let i = 1, len = ranges.length; i < len; i++) {\n      const range = ranges[i];\n      if (prev.left + prev.width + 0.9 /* account for browser's rounding errors*/ >= range.left) {\n        prev.width = Math.max(prev.width, range.left + range.width - prev.left);\n      } else {\n        result[resultLen++] = prev;\n        prev = range;\n      }\n    }\n    result[resultLen++] = prev;\n    return result;\n  }\n  static _createHorizontalRangesFromClientRects(clientRects, clientRectDeltaLeft, clientRectScale) {\n    if (!clientRects || clientRects.length === 0) {\n      return null;\n    }\n    // We go through FloatHorizontalRange because it has been observed in bi-di text\n    // that the clientRects are not coming in sorted from the browser\n    const result = [];\n    for (let i = 0, len = clientRects.length; i < len; i++) {\n      const clientRect = clientRects[i];\n      result[i] = new FloatHorizontalRange(Math.max(0, (clientRect.left - clientRectDeltaLeft) / clientRectScale), clientRect.width / clientRectScale);\n    }\n    return this._mergeAdjacentRanges(result);\n  }\n  static readHorizontalRanges(domNode, startChildIndex, startOffset, endChildIndex, endOffset, context) {\n    // Panic check\n    const min = 0;\n    const max = domNode.children.length - 1;\n    if (min > max) {\n      return null;\n    }\n    startChildIndex = Math.min(max, Math.max(min, startChildIndex));\n    endChildIndex = Math.min(max, Math.max(min, endChildIndex));\n    if (startChildIndex === endChildIndex && startOffset === endOffset && startOffset === 0 && !domNode.children[startChildIndex].firstChild) {\n      // We must find the position at the beginning of a <span>\n      // To cover cases of empty <span>s, avoid using a range and use the <span>'s bounding box\n      const clientRects = domNode.children[startChildIndex].getClientRects();\n      context.markDidDomLayout();\n      return this._createHorizontalRangesFromClientRects(clientRects, context.clientRectDeltaLeft, context.clientRectScale);\n    }\n    // If crossing over to a span only to select offset 0, then use the previous span's maximum offset\n    // Chrome is buggy and doesn't handle 0 offsets well sometimes.\n    if (startChildIndex !== endChildIndex) {\n      if (endChildIndex > 0 && endOffset === 0) {\n        endChildIndex--;\n        endOffset = 1073741824 /* Constants.MAX_SAFE_SMALL_INTEGER */;\n      }\n    }\n    let startElement = domNode.children[startChildIndex].firstChild;\n    let endElement = domNode.children[endChildIndex].firstChild;\n    if (!startElement || !endElement) {\n      // When having an empty <span> (without any text content), try to move to the previous <span>\n      if (!startElement && startOffset === 0 && startChildIndex > 0) {\n        startElement = domNode.children[startChildIndex - 1].firstChild;\n        startOffset = 1073741824 /* Constants.MAX_SAFE_SMALL_INTEGER */;\n      }\n      if (!endElement && endOffset === 0 && endChildIndex > 0) {\n        endElement = domNode.children[endChildIndex - 1].firstChild;\n        endOffset = 1073741824 /* Constants.MAX_SAFE_SMALL_INTEGER */;\n      }\n    }\n    if (!startElement || !endElement) {\n      return null;\n    }\n    startOffset = Math.min(startElement.textContent.length, Math.max(0, startOffset));\n    endOffset = Math.min(endElement.textContent.length, Math.max(0, endOffset));\n    const clientRects = this._readClientRects(startElement, startOffset, endElement, endOffset, context.endNode);\n    context.markDidDomLayout();\n    return this._createHorizontalRangesFromClientRects(clientRects, context.clientRectDeltaLeft, context.clientRectScale);\n  }\n}", "map": {"version": 3, "names": ["FloatHorizontalRange", "RangeUtil", "_createRange", "_handyReadyRange", "document", "createRange", "_detachRange", "range", "endNode", "selectNodeContents", "_readClientRects", "startElement", "startOffset", "endElement", "endOffset", "setStart", "setEnd", "getClientRects", "e", "_mergeAdjacentRanges", "ranges", "length", "sort", "compare", "result", "resultLen", "prev", "i", "len", "left", "width", "Math", "max", "_createHorizontalRangesFromClientRects", "clientRects", "clientRectDeltaLeft", "clientRectScale", "clientRect", "readHorizontalRanges", "domNode", "startChildIndex", "endChildIndex", "context", "min", "children", "<PERSON><PERSON><PERSON><PERSON>", "markDidDomLayout", "textContent"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/browser/viewParts/lines/rangeUtil.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { FloatHorizontalRange } from '../../view/renderingContext.js';\nexport class RangeUtil {\n    static _createRange() {\n        if (!this._handyReadyRange) {\n            this._handyReadyRange = document.createRange();\n        }\n        return this._handyReadyRange;\n    }\n    static _detachRange(range, endNode) {\n        // Move range out of the span node, <PERSON><PERSON> doesn't like having many ranges in\n        // the same spot and will act badly for lines containing dashes ('-')\n        range.selectNodeContents(endNode);\n    }\n    static _readClientRects(startElement, startOffset, endElement, endOffset, endNode) {\n        const range = this._createRange();\n        try {\n            range.setStart(startElement, startOffset);\n            range.setEnd(endElement, endOffset);\n            return range.getClientRects();\n        }\n        catch (e) {\n            // This is life ...\n            return null;\n        }\n        finally {\n            this._detachRange(range, endNode);\n        }\n    }\n    static _mergeAdjacentRanges(ranges) {\n        if (ranges.length === 1) {\n            // There is nothing to merge\n            return ranges;\n        }\n        ranges.sort(FloatHorizontalRange.compare);\n        const result = [];\n        let resultLen = 0;\n        let prev = ranges[0];\n        for (let i = 1, len = ranges.length; i < len; i++) {\n            const range = ranges[i];\n            if (prev.left + prev.width + 0.9 /* account for browser's rounding errors*/ >= range.left) {\n                prev.width = Math.max(prev.width, range.left + range.width - prev.left);\n            }\n            else {\n                result[resultLen++] = prev;\n                prev = range;\n            }\n        }\n        result[resultLen++] = prev;\n        return result;\n    }\n    static _createHorizontalRangesFromClientRects(clientRects, clientRectDeltaLeft, clientRectScale) {\n        if (!clientRects || clientRects.length === 0) {\n            return null;\n        }\n        // We go through FloatHorizontalRange because it has been observed in bi-di text\n        // that the clientRects are not coming in sorted from the browser\n        const result = [];\n        for (let i = 0, len = clientRects.length; i < len; i++) {\n            const clientRect = clientRects[i];\n            result[i] = new FloatHorizontalRange(Math.max(0, (clientRect.left - clientRectDeltaLeft) / clientRectScale), clientRect.width / clientRectScale);\n        }\n        return this._mergeAdjacentRanges(result);\n    }\n    static readHorizontalRanges(domNode, startChildIndex, startOffset, endChildIndex, endOffset, context) {\n        // Panic check\n        const min = 0;\n        const max = domNode.children.length - 1;\n        if (min > max) {\n            return null;\n        }\n        startChildIndex = Math.min(max, Math.max(min, startChildIndex));\n        endChildIndex = Math.min(max, Math.max(min, endChildIndex));\n        if (startChildIndex === endChildIndex && startOffset === endOffset && startOffset === 0 && !domNode.children[startChildIndex].firstChild) {\n            // We must find the position at the beginning of a <span>\n            // To cover cases of empty <span>s, avoid using a range and use the <span>'s bounding box\n            const clientRects = domNode.children[startChildIndex].getClientRects();\n            context.markDidDomLayout();\n            return this._createHorizontalRangesFromClientRects(clientRects, context.clientRectDeltaLeft, context.clientRectScale);\n        }\n        // If crossing over to a span only to select offset 0, then use the previous span's maximum offset\n        // Chrome is buggy and doesn't handle 0 offsets well sometimes.\n        if (startChildIndex !== endChildIndex) {\n            if (endChildIndex > 0 && endOffset === 0) {\n                endChildIndex--;\n                endOffset = 1073741824 /* Constants.MAX_SAFE_SMALL_INTEGER */;\n            }\n        }\n        let startElement = domNode.children[startChildIndex].firstChild;\n        let endElement = domNode.children[endChildIndex].firstChild;\n        if (!startElement || !endElement) {\n            // When having an empty <span> (without any text content), try to move to the previous <span>\n            if (!startElement && startOffset === 0 && startChildIndex > 0) {\n                startElement = domNode.children[startChildIndex - 1].firstChild;\n                startOffset = 1073741824 /* Constants.MAX_SAFE_SMALL_INTEGER */;\n            }\n            if (!endElement && endOffset === 0 && endChildIndex > 0) {\n                endElement = domNode.children[endChildIndex - 1].firstChild;\n                endOffset = 1073741824 /* Constants.MAX_SAFE_SMALL_INTEGER */;\n            }\n        }\n        if (!startElement || !endElement) {\n            return null;\n        }\n        startOffset = Math.min(startElement.textContent.length, Math.max(0, startOffset));\n        endOffset = Math.min(endElement.textContent.length, Math.max(0, endOffset));\n        const clientRects = this._readClientRects(startElement, startOffset, endElement, endOffset, context.endNode);\n        context.markDidDomLayout();\n        return this._createHorizontalRangesFromClientRects(clientRects, context.clientRectDeltaLeft, context.clientRectScale);\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,oBAAoB,QAAQ,gCAAgC;AACrE,OAAO,MAAMC,SAAS,CAAC;EACnB,OAAOC,YAAYA,CAAA,EAAG;IAClB,IAAI,CAAC,IAAI,CAACC,gBAAgB,EAAE;MACxB,IAAI,CAACA,gBAAgB,GAAGC,QAAQ,CAACC,WAAW,CAAC,CAAC;IAClD;IACA,OAAO,IAAI,CAACF,gBAAgB;EAChC;EACA,OAAOG,YAAYA,CAACC,KAAK,EAAEC,OAAO,EAAE;IAChC;IACA;IACAD,KAAK,CAACE,kBAAkB,CAACD,OAAO,CAAC;EACrC;EACA,OAAOE,gBAAgBA,CAACC,YAAY,EAAEC,WAAW,EAAEC,UAAU,EAAEC,SAAS,EAAEN,OAAO,EAAE;IAC/E,MAAMD,KAAK,GAAG,IAAI,CAACL,YAAY,CAAC,CAAC;IACjC,IAAI;MACAK,KAAK,CAACQ,QAAQ,CAACJ,YAAY,EAAEC,WAAW,CAAC;MACzCL,KAAK,CAACS,MAAM,CAACH,UAAU,EAAEC,SAAS,CAAC;MACnC,OAAOP,KAAK,CAACU,cAAc,CAAC,CAAC;IACjC,CAAC,CACD,OAAOC,CAAC,EAAE;MACN;MACA,OAAO,IAAI;IACf,CAAC,SACO;MACJ,IAAI,CAACZ,YAAY,CAACC,KAAK,EAAEC,OAAO,CAAC;IACrC;EACJ;EACA,OAAOW,oBAAoBA,CAACC,MAAM,EAAE;IAChC,IAAIA,MAAM,CAACC,MAAM,KAAK,CAAC,EAAE;MACrB;MACA,OAAOD,MAAM;IACjB;IACAA,MAAM,CAACE,IAAI,CAACtB,oBAAoB,CAACuB,OAAO,CAAC;IACzC,MAAMC,MAAM,GAAG,EAAE;IACjB,IAAIC,SAAS,GAAG,CAAC;IACjB,IAAIC,IAAI,GAAGN,MAAM,CAAC,CAAC,CAAC;IACpB,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGR,MAAM,CAACC,MAAM,EAAEM,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;MAC/C,MAAMpB,KAAK,GAAGa,MAAM,CAACO,CAAC,CAAC;MACvB,IAAID,IAAI,CAACG,IAAI,GAAGH,IAAI,CAACI,KAAK,GAAG,GAAG,CAAC,8CAA8CvB,KAAK,CAACsB,IAAI,EAAE;QACvFH,IAAI,CAACI,KAAK,GAAGC,IAAI,CAACC,GAAG,CAACN,IAAI,CAACI,KAAK,EAAEvB,KAAK,CAACsB,IAAI,GAAGtB,KAAK,CAACuB,KAAK,GAAGJ,IAAI,CAACG,IAAI,CAAC;MAC3E,CAAC,MACI;QACDL,MAAM,CAACC,SAAS,EAAE,CAAC,GAAGC,IAAI;QAC1BA,IAAI,GAAGnB,KAAK;MAChB;IACJ;IACAiB,MAAM,CAACC,SAAS,EAAE,CAAC,GAAGC,IAAI;IAC1B,OAAOF,MAAM;EACjB;EACA,OAAOS,sCAAsCA,CAACC,WAAW,EAAEC,mBAAmB,EAAEC,eAAe,EAAE;IAC7F,IAAI,CAACF,WAAW,IAAIA,WAAW,CAACb,MAAM,KAAK,CAAC,EAAE;MAC1C,OAAO,IAAI;IACf;IACA;IACA;IACA,MAAMG,MAAM,GAAG,EAAE;IACjB,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGM,WAAW,CAACb,MAAM,EAAEM,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;MACpD,MAAMU,UAAU,GAAGH,WAAW,CAACP,CAAC,CAAC;MACjCH,MAAM,CAACG,CAAC,CAAC,GAAG,IAAI3B,oBAAoB,CAAC+B,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAACK,UAAU,CAACR,IAAI,GAAGM,mBAAmB,IAAIC,eAAe,CAAC,EAAEC,UAAU,CAACP,KAAK,GAAGM,eAAe,CAAC;IACpJ;IACA,OAAO,IAAI,CAACjB,oBAAoB,CAACK,MAAM,CAAC;EAC5C;EACA,OAAOc,oBAAoBA,CAACC,OAAO,EAAEC,eAAe,EAAE5B,WAAW,EAAE6B,aAAa,EAAE3B,SAAS,EAAE4B,OAAO,EAAE;IAClG;IACA,MAAMC,GAAG,GAAG,CAAC;IACb,MAAMX,GAAG,GAAGO,OAAO,CAACK,QAAQ,CAACvB,MAAM,GAAG,CAAC;IACvC,IAAIsB,GAAG,GAAGX,GAAG,EAAE;MACX,OAAO,IAAI;IACf;IACAQ,eAAe,GAAGT,IAAI,CAACY,GAAG,CAACX,GAAG,EAAED,IAAI,CAACC,GAAG,CAACW,GAAG,EAAEH,eAAe,CAAC,CAAC;IAC/DC,aAAa,GAAGV,IAAI,CAACY,GAAG,CAACX,GAAG,EAAED,IAAI,CAACC,GAAG,CAACW,GAAG,EAAEF,aAAa,CAAC,CAAC;IAC3D,IAAID,eAAe,KAAKC,aAAa,IAAI7B,WAAW,KAAKE,SAAS,IAAIF,WAAW,KAAK,CAAC,IAAI,CAAC2B,OAAO,CAACK,QAAQ,CAACJ,eAAe,CAAC,CAACK,UAAU,EAAE;MACtI;MACA;MACA,MAAMX,WAAW,GAAGK,OAAO,CAACK,QAAQ,CAACJ,eAAe,CAAC,CAACvB,cAAc,CAAC,CAAC;MACtEyB,OAAO,CAACI,gBAAgB,CAAC,CAAC;MAC1B,OAAO,IAAI,CAACb,sCAAsC,CAACC,WAAW,EAAEQ,OAAO,CAACP,mBAAmB,EAAEO,OAAO,CAACN,eAAe,CAAC;IACzH;IACA;IACA;IACA,IAAII,eAAe,KAAKC,aAAa,EAAE;MACnC,IAAIA,aAAa,GAAG,CAAC,IAAI3B,SAAS,KAAK,CAAC,EAAE;QACtC2B,aAAa,EAAE;QACf3B,SAAS,GAAG,UAAU,CAAC;MAC3B;IACJ;IACA,IAAIH,YAAY,GAAG4B,OAAO,CAACK,QAAQ,CAACJ,eAAe,CAAC,CAACK,UAAU;IAC/D,IAAIhC,UAAU,GAAG0B,OAAO,CAACK,QAAQ,CAACH,aAAa,CAAC,CAACI,UAAU;IAC3D,IAAI,CAAClC,YAAY,IAAI,CAACE,UAAU,EAAE;MAC9B;MACA,IAAI,CAACF,YAAY,IAAIC,WAAW,KAAK,CAAC,IAAI4B,eAAe,GAAG,CAAC,EAAE;QAC3D7B,YAAY,GAAG4B,OAAO,CAACK,QAAQ,CAACJ,eAAe,GAAG,CAAC,CAAC,CAACK,UAAU;QAC/DjC,WAAW,GAAG,UAAU,CAAC;MAC7B;MACA,IAAI,CAACC,UAAU,IAAIC,SAAS,KAAK,CAAC,IAAI2B,aAAa,GAAG,CAAC,EAAE;QACrD5B,UAAU,GAAG0B,OAAO,CAACK,QAAQ,CAACH,aAAa,GAAG,CAAC,CAAC,CAACI,UAAU;QAC3D/B,SAAS,GAAG,UAAU,CAAC;MAC3B;IACJ;IACA,IAAI,CAACH,YAAY,IAAI,CAACE,UAAU,EAAE;MAC9B,OAAO,IAAI;IACf;IACAD,WAAW,GAAGmB,IAAI,CAACY,GAAG,CAAChC,YAAY,CAACoC,WAAW,CAAC1B,MAAM,EAAEU,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEpB,WAAW,CAAC,CAAC;IACjFE,SAAS,GAAGiB,IAAI,CAACY,GAAG,CAAC9B,UAAU,CAACkC,WAAW,CAAC1B,MAAM,EAAEU,IAAI,CAACC,GAAG,CAAC,CAAC,EAAElB,SAAS,CAAC,CAAC;IAC3E,MAAMoB,WAAW,GAAG,IAAI,CAACxB,gBAAgB,CAACC,YAAY,EAAEC,WAAW,EAAEC,UAAU,EAAEC,SAAS,EAAE4B,OAAO,CAAClC,OAAO,CAAC;IAC5GkC,OAAO,CAACI,gBAAgB,CAAC,CAAC;IAC1B,OAAO,IAAI,CAACb,sCAAsC,CAACC,WAAW,EAAEQ,OAAO,CAACP,mBAAmB,EAAEO,OAAO,CAACN,eAAe,CAAC;EACzH;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}