{"ast": null, "code": "import { compare, compareIgnoreCase, compareSubstring, compareSubstringIgnoreCase } from './strings.js';\nexport class StringIterator {\n  constructor() {\n    this._value = '';\n    this._pos = 0;\n  }\n  reset(key) {\n    this._value = key;\n    this._pos = 0;\n    return this;\n  }\n  next() {\n    this._pos += 1;\n    return this;\n  }\n  hasNext() {\n    return this._pos < this._value.length - 1;\n  }\n  cmp(a) {\n    const aCode = a.charCodeAt(0);\n    const thisCode = this._value.charCodeAt(this._pos);\n    return aCode - thisCode;\n  }\n  value() {\n    return this._value[this._pos];\n  }\n}\nexport class ConfigKeysIterator {\n  constructor(_caseSensitive = true) {\n    this._caseSensitive = _caseSensitive;\n  }\n  reset(key) {\n    this._value = key;\n    this._from = 0;\n    this._to = 0;\n    return this.next();\n  }\n  hasNext() {\n    return this._to < this._value.length;\n  }\n  next() {\n    // this._data = key.split(/[\\\\/]/).filter(s => !!s);\n    this._from = this._to;\n    let justSeps = true;\n    for (; this._to < this._value.length; this._to++) {\n      const ch = this._value.charCodeAt(this._to);\n      if (ch === 46 /* CharCode.Period */) {\n        if (justSeps) {\n          this._from++;\n        } else {\n          break;\n        }\n      } else {\n        justSeps = false;\n      }\n    }\n    return this;\n  }\n  cmp(a) {\n    return this._caseSensitive ? compareSubstring(a, this._value, 0, a.length, this._from, this._to) : compareSubstringIgnoreCase(a, this._value, 0, a.length, this._from, this._to);\n  }\n  value() {\n    return this._value.substring(this._from, this._to);\n  }\n}\nexport class PathIterator {\n  constructor(_splitOnBackslash = true, _caseSensitive = true) {\n    this._splitOnBackslash = _splitOnBackslash;\n    this._caseSensitive = _caseSensitive;\n  }\n  reset(key) {\n    this._from = 0;\n    this._to = 0;\n    this._value = key;\n    this._valueLen = key.length;\n    for (let pos = key.length - 1; pos >= 0; pos--, this._valueLen--) {\n      const ch = this._value.charCodeAt(pos);\n      if (!(ch === 47 /* CharCode.Slash */ || this._splitOnBackslash && ch === 92 /* CharCode.Backslash */)) {\n        break;\n      }\n    }\n    return this.next();\n  }\n  hasNext() {\n    return this._to < this._valueLen;\n  }\n  next() {\n    // this._data = key.split(/[\\\\/]/).filter(s => !!s);\n    this._from = this._to;\n    let justSeps = true;\n    for (; this._to < this._valueLen; this._to++) {\n      const ch = this._value.charCodeAt(this._to);\n      if (ch === 47 /* CharCode.Slash */ || this._splitOnBackslash && ch === 92 /* CharCode.Backslash */) {\n        if (justSeps) {\n          this._from++;\n        } else {\n          break;\n        }\n      } else {\n        justSeps = false;\n      }\n    }\n    return this;\n  }\n  cmp(a) {\n    return this._caseSensitive ? compareSubstring(a, this._value, 0, a.length, this._from, this._to) : compareSubstringIgnoreCase(a, this._value, 0, a.length, this._from, this._to);\n  }\n  value() {\n    return this._value.substring(this._from, this._to);\n  }\n}\nexport class UriIterator {\n  constructor(_ignorePathCasing, _ignoreQueryAndFragment) {\n    this._ignorePathCasing = _ignorePathCasing;\n    this._ignoreQueryAndFragment = _ignoreQueryAndFragment;\n    this._states = [];\n    this._stateIdx = 0;\n  }\n  reset(key) {\n    this._value = key;\n    this._states = [];\n    if (this._value.scheme) {\n      this._states.push(1 /* UriIteratorState.Scheme */);\n    }\n    if (this._value.authority) {\n      this._states.push(2 /* UriIteratorState.Authority */);\n    }\n    if (this._value.path) {\n      this._pathIterator = new PathIterator(false, !this._ignorePathCasing(key));\n      this._pathIterator.reset(key.path);\n      if (this._pathIterator.value()) {\n        this._states.push(3 /* UriIteratorState.Path */);\n      }\n    }\n    if (!this._ignoreQueryAndFragment(key)) {\n      if (this._value.query) {\n        this._states.push(4 /* UriIteratorState.Query */);\n      }\n      if (this._value.fragment) {\n        this._states.push(5 /* UriIteratorState.Fragment */);\n      }\n    }\n    this._stateIdx = 0;\n    return this;\n  }\n  next() {\n    if (this._states[this._stateIdx] === 3 /* UriIteratorState.Path */ && this._pathIterator.hasNext()) {\n      this._pathIterator.next();\n    } else {\n      this._stateIdx += 1;\n    }\n    return this;\n  }\n  hasNext() {\n    return this._states[this._stateIdx] === 3 /* UriIteratorState.Path */ && this._pathIterator.hasNext() || this._stateIdx < this._states.length - 1;\n  }\n  cmp(a) {\n    if (this._states[this._stateIdx] === 1 /* UriIteratorState.Scheme */) {\n      return compareIgnoreCase(a, this._value.scheme);\n    } else if (this._states[this._stateIdx] === 2 /* UriIteratorState.Authority */) {\n      return compareIgnoreCase(a, this._value.authority);\n    } else if (this._states[this._stateIdx] === 3 /* UriIteratorState.Path */) {\n      return this._pathIterator.cmp(a);\n    } else if (this._states[this._stateIdx] === 4 /* UriIteratorState.Query */) {\n      return compare(a, this._value.query);\n    } else if (this._states[this._stateIdx] === 5 /* UriIteratorState.Fragment */) {\n      return compare(a, this._value.fragment);\n    }\n    throw new Error();\n  }\n  value() {\n    if (this._states[this._stateIdx] === 1 /* UriIteratorState.Scheme */) {\n      return this._value.scheme;\n    } else if (this._states[this._stateIdx] === 2 /* UriIteratorState.Authority */) {\n      return this._value.authority;\n    } else if (this._states[this._stateIdx] === 3 /* UriIteratorState.Path */) {\n      return this._pathIterator.value();\n    } else if (this._states[this._stateIdx] === 4 /* UriIteratorState.Query */) {\n      return this._value.query;\n    } else if (this._states[this._stateIdx] === 5 /* UriIteratorState.Fragment */) {\n      return this._value.fragment;\n    }\n    throw new Error();\n  }\n}\nclass TernarySearchTreeNode {\n  constructor() {\n    this.height = 1;\n  }\n  rotateLeft() {\n    const tmp = this.right;\n    this.right = tmp.left;\n    tmp.left = this;\n    this.updateHeight();\n    tmp.updateHeight();\n    return tmp;\n  }\n  rotateRight() {\n    const tmp = this.left;\n    this.left = tmp.right;\n    tmp.right = this;\n    this.updateHeight();\n    tmp.updateHeight();\n    return tmp;\n  }\n  updateHeight() {\n    this.height = 1 + Math.max(this.heightLeft, this.heightRight);\n  }\n  balanceFactor() {\n    return this.heightRight - this.heightLeft;\n  }\n  get heightLeft() {\n    return this.left?.height ?? 0;\n  }\n  get heightRight() {\n    return this.right?.height ?? 0;\n  }\n}\nexport class TernarySearchTree {\n  static forUris(ignorePathCasing = () => false, ignoreQueryAndFragment = () => false) {\n    return new TernarySearchTree(new UriIterator(ignorePathCasing, ignoreQueryAndFragment));\n  }\n  static forStrings() {\n    return new TernarySearchTree(new StringIterator());\n  }\n  static forConfigKeys() {\n    return new TernarySearchTree(new ConfigKeysIterator());\n  }\n  constructor(segments) {\n    this._iter = segments;\n  }\n  clear() {\n    this._root = undefined;\n  }\n  set(key, element) {\n    const iter = this._iter.reset(key);\n    let node;\n    if (!this._root) {\n      this._root = new TernarySearchTreeNode();\n      this._root.segment = iter.value();\n    }\n    const stack = [];\n    // find insert_node\n    node = this._root;\n    while (true) {\n      const val = iter.cmp(node.segment);\n      if (val > 0) {\n        // left\n        if (!node.left) {\n          node.left = new TernarySearchTreeNode();\n          node.left.segment = iter.value();\n        }\n        stack.push([-1 /* Dir.Left */, node]);\n        node = node.left;\n      } else if (val < 0) {\n        // right\n        if (!node.right) {\n          node.right = new TernarySearchTreeNode();\n          node.right.segment = iter.value();\n        }\n        stack.push([1 /* Dir.Right */, node]);\n        node = node.right;\n      } else if (iter.hasNext()) {\n        // mid\n        iter.next();\n        if (!node.mid) {\n          node.mid = new TernarySearchTreeNode();\n          node.mid.segment = iter.value();\n        }\n        stack.push([0 /* Dir.Mid */, node]);\n        node = node.mid;\n      } else {\n        break;\n      }\n    }\n    // set value\n    const oldElement = node.value;\n    node.value = element;\n    node.key = key;\n    // balance\n    for (let i = stack.length - 1; i >= 0; i--) {\n      const node = stack[i][1];\n      node.updateHeight();\n      const bf = node.balanceFactor();\n      if (bf < -1 || bf > 1) {\n        // needs rotate\n        const d1 = stack[i][0];\n        const d2 = stack[i + 1][0];\n        if (d1 === 1 /* Dir.Right */ && d2 === 1 /* Dir.Right */) {\n          //right, right -> rotate left\n          stack[i][1] = node.rotateLeft();\n        } else if (d1 === -1 /* Dir.Left */ && d2 === -1 /* Dir.Left */) {\n          // left, left -> rotate right\n          stack[i][1] = node.rotateRight();\n        } else if (d1 === 1 /* Dir.Right */ && d2 === -1 /* Dir.Left */) {\n          // right, left -> double rotate right, left\n          node.right = stack[i + 1][1] = stack[i + 1][1].rotateRight();\n          stack[i][1] = node.rotateLeft();\n        } else if (d1 === -1 /* Dir.Left */ && d2 === 1 /* Dir.Right */) {\n          // left, right -> double rotate left, right\n          node.left = stack[i + 1][1] = stack[i + 1][1].rotateLeft();\n          stack[i][1] = node.rotateRight();\n        } else {\n          throw new Error();\n        }\n        // patch path to parent\n        if (i > 0) {\n          switch (stack[i - 1][0]) {\n            case -1 /* Dir.Left */:\n              stack[i - 1][1].left = stack[i][1];\n              break;\n            case 1 /* Dir.Right */:\n              stack[i - 1][1].right = stack[i][1];\n              break;\n            case 0 /* Dir.Mid */:\n              stack[i - 1][1].mid = stack[i][1];\n              break;\n          }\n        } else {\n          this._root = stack[0][1];\n        }\n      }\n    }\n    return oldElement;\n  }\n  get(key) {\n    return this._getNode(key)?.value;\n  }\n  _getNode(key) {\n    const iter = this._iter.reset(key);\n    let node = this._root;\n    while (node) {\n      const val = iter.cmp(node.segment);\n      if (val > 0) {\n        // left\n        node = node.left;\n      } else if (val < 0) {\n        // right\n        node = node.right;\n      } else if (iter.hasNext()) {\n        // mid\n        iter.next();\n        node = node.mid;\n      } else {\n        break;\n      }\n    }\n    return node;\n  }\n  has(key) {\n    const node = this._getNode(key);\n    return !(node?.value === undefined && node?.mid === undefined);\n  }\n  delete(key) {\n    return this._delete(key, false);\n  }\n  deleteSuperstr(key) {\n    return this._delete(key, true);\n  }\n  _delete(key, superStr) {\n    const iter = this._iter.reset(key);\n    const stack = [];\n    let node = this._root;\n    // find node\n    while (node) {\n      const val = iter.cmp(node.segment);\n      if (val > 0) {\n        // left\n        stack.push([-1 /* Dir.Left */, node]);\n        node = node.left;\n      } else if (val < 0) {\n        // right\n        stack.push([1 /* Dir.Right */, node]);\n        node = node.right;\n      } else if (iter.hasNext()) {\n        // mid\n        iter.next();\n        stack.push([0 /* Dir.Mid */, node]);\n        node = node.mid;\n      } else {\n        break;\n      }\n    }\n    if (!node) {\n      // node not found\n      return;\n    }\n    if (superStr) {\n      // removing children, reset height\n      node.left = undefined;\n      node.mid = undefined;\n      node.right = undefined;\n      node.height = 1;\n    } else {\n      // removing element\n      node.key = undefined;\n      node.value = undefined;\n    }\n    // BST node removal\n    if (!node.mid && !node.value) {\n      if (node.left && node.right) {\n        // full node\n        // replace deleted-node with the min-node of the right branch.\n        // If there is no true min-node leave things as they are\n        const min = this._min(node.right);\n        if (min.key) {\n          const {\n            key,\n            value,\n            segment\n          } = min;\n          this._delete(min.key, false);\n          node.key = key;\n          node.value = value;\n          node.segment = segment;\n        }\n      } else {\n        // empty or half empty\n        const newChild = node.left ?? node.right;\n        if (stack.length > 0) {\n          const [dir, parent] = stack[stack.length - 1];\n          switch (dir) {\n            case -1 /* Dir.Left */:\n              parent.left = newChild;\n              break;\n            case 0 /* Dir.Mid */:\n              parent.mid = newChild;\n              break;\n            case 1 /* Dir.Right */:\n              parent.right = newChild;\n              break;\n          }\n        } else {\n          this._root = newChild;\n        }\n      }\n    }\n    // AVL balance\n    for (let i = stack.length - 1; i >= 0; i--) {\n      const node = stack[i][1];\n      node.updateHeight();\n      const bf = node.balanceFactor();\n      if (bf > 1) {\n        // right heavy\n        if (node.right.balanceFactor() >= 0) {\n          // right, right -> rotate left\n          stack[i][1] = node.rotateLeft();\n        } else {\n          // right, left -> double rotate\n          node.right = node.right.rotateRight();\n          stack[i][1] = node.rotateLeft();\n        }\n      } else if (bf < -1) {\n        // left heavy\n        if (node.left.balanceFactor() <= 0) {\n          // left, left -> rotate right\n          stack[i][1] = node.rotateRight();\n        } else {\n          // left, right -> double rotate\n          node.left = node.left.rotateLeft();\n          stack[i][1] = node.rotateRight();\n        }\n      }\n      // patch path to parent\n      if (i > 0) {\n        switch (stack[i - 1][0]) {\n          case -1 /* Dir.Left */:\n            stack[i - 1][1].left = stack[i][1];\n            break;\n          case 1 /* Dir.Right */:\n            stack[i - 1][1].right = stack[i][1];\n            break;\n          case 0 /* Dir.Mid */:\n            stack[i - 1][1].mid = stack[i][1];\n            break;\n        }\n      } else {\n        this._root = stack[0][1];\n      }\n    }\n  }\n  _min(node) {\n    while (node.left) {\n      node = node.left;\n    }\n    return node;\n  }\n  findSubstr(key) {\n    const iter = this._iter.reset(key);\n    let node = this._root;\n    let candidate = undefined;\n    while (node) {\n      const val = iter.cmp(node.segment);\n      if (val > 0) {\n        // left\n        node = node.left;\n      } else if (val < 0) {\n        // right\n        node = node.right;\n      } else if (iter.hasNext()) {\n        // mid\n        iter.next();\n        candidate = node.value || candidate;\n        node = node.mid;\n      } else {\n        break;\n      }\n    }\n    return node && node.value || candidate;\n  }\n  findSuperstr(key) {\n    return this._findSuperstrOrElement(key, false);\n  }\n  _findSuperstrOrElement(key, allowValue) {\n    const iter = this._iter.reset(key);\n    let node = this._root;\n    while (node) {\n      const val = iter.cmp(node.segment);\n      if (val > 0) {\n        // left\n        node = node.left;\n      } else if (val < 0) {\n        // right\n        node = node.right;\n      } else if (iter.hasNext()) {\n        // mid\n        iter.next();\n        node = node.mid;\n      } else {\n        // collect\n        if (!node.mid) {\n          if (allowValue) {\n            return node.value;\n          } else {\n            return undefined;\n          }\n        } else {\n          return this._entries(node.mid);\n        }\n      }\n    }\n    return undefined;\n  }\n  forEach(callback) {\n    for (const [key, value] of this) {\n      callback(value, key);\n    }\n  }\n  *[Symbol.iterator]() {\n    yield* this._entries(this._root);\n  }\n  _entries(node) {\n    const result = [];\n    this._dfsEntries(node, result);\n    return result[Symbol.iterator]();\n  }\n  _dfsEntries(node, bucket) {\n    // DFS\n    if (!node) {\n      return;\n    }\n    if (node.left) {\n      this._dfsEntries(node.left, bucket);\n    }\n    if (node.value) {\n      bucket.push([node.key, node.value]);\n    }\n    if (node.mid) {\n      this._dfsEntries(node.mid, bucket);\n    }\n    if (node.right) {\n      this._dfsEntries(node.right, bucket);\n    }\n  }\n}", "map": {"version": 3, "names": ["compare", "compareIgnoreCase", "compareSubstring", "compareSubstringIgnoreCase", "StringIterator", "constructor", "_value", "_pos", "reset", "key", "next", "hasNext", "length", "cmp", "a", "aCode", "charCodeAt", "thisCode", "value", "ConfigKeysIterator", "_caseSensitive", "_from", "_to", "justSeps", "ch", "substring", "PathIterator", "_splitOnBackslash", "_valueLen", "pos", "UriIterator", "_ignorePathCasing", "_ignoreQueryAndFragment", "_states", "_stateIdx", "scheme", "push", "authority", "path", "_pathIterator", "query", "fragment", "Error", "TernarySearchTreeNode", "height", "rotateLeft", "tmp", "right", "left", "updateHeight", "rotateRight", "Math", "max", "heightLeft", "heightRight", "balanceFactor", "TernarySearchTree", "for<PERSON>ris", "ignorePathCasing", "ignoreQueryAndFragment", "forStrings", "forConfigKeys", "segments", "_iter", "clear", "_root", "undefined", "set", "element", "iter", "node", "segment", "stack", "val", "mid", "oldElement", "i", "bf", "d1", "d2", "get", "_getNode", "has", "delete", "_delete", "deleteSuperstr", "superStr", "min", "_min", "<PERSON><PERSON><PERSON><PERSON>", "dir", "parent", "findSubstr", "candidate", "find<PERSON><PERSON><PERSON><PERSON>", "_findSuperstrOrElement", "allowValue", "_entries", "for<PERSON>ach", "callback", "Symbol", "iterator", "result", "_dfsEntries", "bucket"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/common/ternarySearchTree.js"], "sourcesContent": ["import { compare, compareIgnoreCase, compareSubstring, compareSubstringIgnoreCase } from './strings.js';\nexport class StringIterator {\n    constructor() {\n        this._value = '';\n        this._pos = 0;\n    }\n    reset(key) {\n        this._value = key;\n        this._pos = 0;\n        return this;\n    }\n    next() {\n        this._pos += 1;\n        return this;\n    }\n    hasNext() {\n        return this._pos < this._value.length - 1;\n    }\n    cmp(a) {\n        const aCode = a.charCodeAt(0);\n        const thisCode = this._value.charCodeAt(this._pos);\n        return aCode - thisCode;\n    }\n    value() {\n        return this._value[this._pos];\n    }\n}\nexport class ConfigKeysIterator {\n    constructor(_caseSensitive = true) {\n        this._caseSensitive = _caseSensitive;\n    }\n    reset(key) {\n        this._value = key;\n        this._from = 0;\n        this._to = 0;\n        return this.next();\n    }\n    hasNext() {\n        return this._to < this._value.length;\n    }\n    next() {\n        // this._data = key.split(/[\\\\/]/).filter(s => !!s);\n        this._from = this._to;\n        let justSeps = true;\n        for (; this._to < this._value.length; this._to++) {\n            const ch = this._value.charCodeAt(this._to);\n            if (ch === 46 /* CharCode.Period */) {\n                if (justSeps) {\n                    this._from++;\n                }\n                else {\n                    break;\n                }\n            }\n            else {\n                justSeps = false;\n            }\n        }\n        return this;\n    }\n    cmp(a) {\n        return this._caseSensitive\n            ? compareSubstring(a, this._value, 0, a.length, this._from, this._to)\n            : compareSubstringIgnoreCase(a, this._value, 0, a.length, this._from, this._to);\n    }\n    value() {\n        return this._value.substring(this._from, this._to);\n    }\n}\nexport class PathIterator {\n    constructor(_splitOnBackslash = true, _caseSensitive = true) {\n        this._splitOnBackslash = _splitOnBackslash;\n        this._caseSensitive = _caseSensitive;\n    }\n    reset(key) {\n        this._from = 0;\n        this._to = 0;\n        this._value = key;\n        this._valueLen = key.length;\n        for (let pos = key.length - 1; pos >= 0; pos--, this._valueLen--) {\n            const ch = this._value.charCodeAt(pos);\n            if (!(ch === 47 /* CharCode.Slash */ || this._splitOnBackslash && ch === 92 /* CharCode.Backslash */)) {\n                break;\n            }\n        }\n        return this.next();\n    }\n    hasNext() {\n        return this._to < this._valueLen;\n    }\n    next() {\n        // this._data = key.split(/[\\\\/]/).filter(s => !!s);\n        this._from = this._to;\n        let justSeps = true;\n        for (; this._to < this._valueLen; this._to++) {\n            const ch = this._value.charCodeAt(this._to);\n            if (ch === 47 /* CharCode.Slash */ || this._splitOnBackslash && ch === 92 /* CharCode.Backslash */) {\n                if (justSeps) {\n                    this._from++;\n                }\n                else {\n                    break;\n                }\n            }\n            else {\n                justSeps = false;\n            }\n        }\n        return this;\n    }\n    cmp(a) {\n        return this._caseSensitive\n            ? compareSubstring(a, this._value, 0, a.length, this._from, this._to)\n            : compareSubstringIgnoreCase(a, this._value, 0, a.length, this._from, this._to);\n    }\n    value() {\n        return this._value.substring(this._from, this._to);\n    }\n}\nexport class UriIterator {\n    constructor(_ignorePathCasing, _ignoreQueryAndFragment) {\n        this._ignorePathCasing = _ignorePathCasing;\n        this._ignoreQueryAndFragment = _ignoreQueryAndFragment;\n        this._states = [];\n        this._stateIdx = 0;\n    }\n    reset(key) {\n        this._value = key;\n        this._states = [];\n        if (this._value.scheme) {\n            this._states.push(1 /* UriIteratorState.Scheme */);\n        }\n        if (this._value.authority) {\n            this._states.push(2 /* UriIteratorState.Authority */);\n        }\n        if (this._value.path) {\n            this._pathIterator = new PathIterator(false, !this._ignorePathCasing(key));\n            this._pathIterator.reset(key.path);\n            if (this._pathIterator.value()) {\n                this._states.push(3 /* UriIteratorState.Path */);\n            }\n        }\n        if (!this._ignoreQueryAndFragment(key)) {\n            if (this._value.query) {\n                this._states.push(4 /* UriIteratorState.Query */);\n            }\n            if (this._value.fragment) {\n                this._states.push(5 /* UriIteratorState.Fragment */);\n            }\n        }\n        this._stateIdx = 0;\n        return this;\n    }\n    next() {\n        if (this._states[this._stateIdx] === 3 /* UriIteratorState.Path */ && this._pathIterator.hasNext()) {\n            this._pathIterator.next();\n        }\n        else {\n            this._stateIdx += 1;\n        }\n        return this;\n    }\n    hasNext() {\n        return (this._states[this._stateIdx] === 3 /* UriIteratorState.Path */ && this._pathIterator.hasNext())\n            || this._stateIdx < this._states.length - 1;\n    }\n    cmp(a) {\n        if (this._states[this._stateIdx] === 1 /* UriIteratorState.Scheme */) {\n            return compareIgnoreCase(a, this._value.scheme);\n        }\n        else if (this._states[this._stateIdx] === 2 /* UriIteratorState.Authority */) {\n            return compareIgnoreCase(a, this._value.authority);\n        }\n        else if (this._states[this._stateIdx] === 3 /* UriIteratorState.Path */) {\n            return this._pathIterator.cmp(a);\n        }\n        else if (this._states[this._stateIdx] === 4 /* UriIteratorState.Query */) {\n            return compare(a, this._value.query);\n        }\n        else if (this._states[this._stateIdx] === 5 /* UriIteratorState.Fragment */) {\n            return compare(a, this._value.fragment);\n        }\n        throw new Error();\n    }\n    value() {\n        if (this._states[this._stateIdx] === 1 /* UriIteratorState.Scheme */) {\n            return this._value.scheme;\n        }\n        else if (this._states[this._stateIdx] === 2 /* UriIteratorState.Authority */) {\n            return this._value.authority;\n        }\n        else if (this._states[this._stateIdx] === 3 /* UriIteratorState.Path */) {\n            return this._pathIterator.value();\n        }\n        else if (this._states[this._stateIdx] === 4 /* UriIteratorState.Query */) {\n            return this._value.query;\n        }\n        else if (this._states[this._stateIdx] === 5 /* UriIteratorState.Fragment */) {\n            return this._value.fragment;\n        }\n        throw new Error();\n    }\n}\nclass TernarySearchTreeNode {\n    constructor() {\n        this.height = 1;\n    }\n    rotateLeft() {\n        const tmp = this.right;\n        this.right = tmp.left;\n        tmp.left = this;\n        this.updateHeight();\n        tmp.updateHeight();\n        return tmp;\n    }\n    rotateRight() {\n        const tmp = this.left;\n        this.left = tmp.right;\n        tmp.right = this;\n        this.updateHeight();\n        tmp.updateHeight();\n        return tmp;\n    }\n    updateHeight() {\n        this.height = 1 + Math.max(this.heightLeft, this.heightRight);\n    }\n    balanceFactor() {\n        return this.heightRight - this.heightLeft;\n    }\n    get heightLeft() {\n        return this.left?.height ?? 0;\n    }\n    get heightRight() {\n        return this.right?.height ?? 0;\n    }\n}\nexport class TernarySearchTree {\n    static forUris(ignorePathCasing = () => false, ignoreQueryAndFragment = () => false) {\n        return new TernarySearchTree(new UriIterator(ignorePathCasing, ignoreQueryAndFragment));\n    }\n    static forStrings() {\n        return new TernarySearchTree(new StringIterator());\n    }\n    static forConfigKeys() {\n        return new TernarySearchTree(new ConfigKeysIterator());\n    }\n    constructor(segments) {\n        this._iter = segments;\n    }\n    clear() {\n        this._root = undefined;\n    }\n    set(key, element) {\n        const iter = this._iter.reset(key);\n        let node;\n        if (!this._root) {\n            this._root = new TernarySearchTreeNode();\n            this._root.segment = iter.value();\n        }\n        const stack = [];\n        // find insert_node\n        node = this._root;\n        while (true) {\n            const val = iter.cmp(node.segment);\n            if (val > 0) {\n                // left\n                if (!node.left) {\n                    node.left = new TernarySearchTreeNode();\n                    node.left.segment = iter.value();\n                }\n                stack.push([-1 /* Dir.Left */, node]);\n                node = node.left;\n            }\n            else if (val < 0) {\n                // right\n                if (!node.right) {\n                    node.right = new TernarySearchTreeNode();\n                    node.right.segment = iter.value();\n                }\n                stack.push([1 /* Dir.Right */, node]);\n                node = node.right;\n            }\n            else if (iter.hasNext()) {\n                // mid\n                iter.next();\n                if (!node.mid) {\n                    node.mid = new TernarySearchTreeNode();\n                    node.mid.segment = iter.value();\n                }\n                stack.push([0 /* Dir.Mid */, node]);\n                node = node.mid;\n            }\n            else {\n                break;\n            }\n        }\n        // set value\n        const oldElement = node.value;\n        node.value = element;\n        node.key = key;\n        // balance\n        for (let i = stack.length - 1; i >= 0; i--) {\n            const node = stack[i][1];\n            node.updateHeight();\n            const bf = node.balanceFactor();\n            if (bf < -1 || bf > 1) {\n                // needs rotate\n                const d1 = stack[i][0];\n                const d2 = stack[i + 1][0];\n                if (d1 === 1 /* Dir.Right */ && d2 === 1 /* Dir.Right */) {\n                    //right, right -> rotate left\n                    stack[i][1] = node.rotateLeft();\n                }\n                else if (d1 === -1 /* Dir.Left */ && d2 === -1 /* Dir.Left */) {\n                    // left, left -> rotate right\n                    stack[i][1] = node.rotateRight();\n                }\n                else if (d1 === 1 /* Dir.Right */ && d2 === -1 /* Dir.Left */) {\n                    // right, left -> double rotate right, left\n                    node.right = stack[i + 1][1] = stack[i + 1][1].rotateRight();\n                    stack[i][1] = node.rotateLeft();\n                }\n                else if (d1 === -1 /* Dir.Left */ && d2 === 1 /* Dir.Right */) {\n                    // left, right -> double rotate left, right\n                    node.left = stack[i + 1][1] = stack[i + 1][1].rotateLeft();\n                    stack[i][1] = node.rotateRight();\n                }\n                else {\n                    throw new Error();\n                }\n                // patch path to parent\n                if (i > 0) {\n                    switch (stack[i - 1][0]) {\n                        case -1 /* Dir.Left */:\n                            stack[i - 1][1].left = stack[i][1];\n                            break;\n                        case 1 /* Dir.Right */:\n                            stack[i - 1][1].right = stack[i][1];\n                            break;\n                        case 0 /* Dir.Mid */:\n                            stack[i - 1][1].mid = stack[i][1];\n                            break;\n                    }\n                }\n                else {\n                    this._root = stack[0][1];\n                }\n            }\n        }\n        return oldElement;\n    }\n    get(key) {\n        return this._getNode(key)?.value;\n    }\n    _getNode(key) {\n        const iter = this._iter.reset(key);\n        let node = this._root;\n        while (node) {\n            const val = iter.cmp(node.segment);\n            if (val > 0) {\n                // left\n                node = node.left;\n            }\n            else if (val < 0) {\n                // right\n                node = node.right;\n            }\n            else if (iter.hasNext()) {\n                // mid\n                iter.next();\n                node = node.mid;\n            }\n            else {\n                break;\n            }\n        }\n        return node;\n    }\n    has(key) {\n        const node = this._getNode(key);\n        return !(node?.value === undefined && node?.mid === undefined);\n    }\n    delete(key) {\n        return this._delete(key, false);\n    }\n    deleteSuperstr(key) {\n        return this._delete(key, true);\n    }\n    _delete(key, superStr) {\n        const iter = this._iter.reset(key);\n        const stack = [];\n        let node = this._root;\n        // find node\n        while (node) {\n            const val = iter.cmp(node.segment);\n            if (val > 0) {\n                // left\n                stack.push([-1 /* Dir.Left */, node]);\n                node = node.left;\n            }\n            else if (val < 0) {\n                // right\n                stack.push([1 /* Dir.Right */, node]);\n                node = node.right;\n            }\n            else if (iter.hasNext()) {\n                // mid\n                iter.next();\n                stack.push([0 /* Dir.Mid */, node]);\n                node = node.mid;\n            }\n            else {\n                break;\n            }\n        }\n        if (!node) {\n            // node not found\n            return;\n        }\n        if (superStr) {\n            // removing children, reset height\n            node.left = undefined;\n            node.mid = undefined;\n            node.right = undefined;\n            node.height = 1;\n        }\n        else {\n            // removing element\n            node.key = undefined;\n            node.value = undefined;\n        }\n        // BST node removal\n        if (!node.mid && !node.value) {\n            if (node.left && node.right) {\n                // full node\n                // replace deleted-node with the min-node of the right branch.\n                // If there is no true min-node leave things as they are\n                const min = this._min(node.right);\n                if (min.key) {\n                    const { key, value, segment } = min;\n                    this._delete(min.key, false);\n                    node.key = key;\n                    node.value = value;\n                    node.segment = segment;\n                }\n            }\n            else {\n                // empty or half empty\n                const newChild = node.left ?? node.right;\n                if (stack.length > 0) {\n                    const [dir, parent] = stack[stack.length - 1];\n                    switch (dir) {\n                        case -1 /* Dir.Left */:\n                            parent.left = newChild;\n                            break;\n                        case 0 /* Dir.Mid */:\n                            parent.mid = newChild;\n                            break;\n                        case 1 /* Dir.Right */:\n                            parent.right = newChild;\n                            break;\n                    }\n                }\n                else {\n                    this._root = newChild;\n                }\n            }\n        }\n        // AVL balance\n        for (let i = stack.length - 1; i >= 0; i--) {\n            const node = stack[i][1];\n            node.updateHeight();\n            const bf = node.balanceFactor();\n            if (bf > 1) {\n                // right heavy\n                if (node.right.balanceFactor() >= 0) {\n                    // right, right -> rotate left\n                    stack[i][1] = node.rotateLeft();\n                }\n                else {\n                    // right, left -> double rotate\n                    node.right = node.right.rotateRight();\n                    stack[i][1] = node.rotateLeft();\n                }\n            }\n            else if (bf < -1) {\n                // left heavy\n                if (node.left.balanceFactor() <= 0) {\n                    // left, left -> rotate right\n                    stack[i][1] = node.rotateRight();\n                }\n                else {\n                    // left, right -> double rotate\n                    node.left = node.left.rotateLeft();\n                    stack[i][1] = node.rotateRight();\n                }\n            }\n            // patch path to parent\n            if (i > 0) {\n                switch (stack[i - 1][0]) {\n                    case -1 /* Dir.Left */:\n                        stack[i - 1][1].left = stack[i][1];\n                        break;\n                    case 1 /* Dir.Right */:\n                        stack[i - 1][1].right = stack[i][1];\n                        break;\n                    case 0 /* Dir.Mid */:\n                        stack[i - 1][1].mid = stack[i][1];\n                        break;\n                }\n            }\n            else {\n                this._root = stack[0][1];\n            }\n        }\n    }\n    _min(node) {\n        while (node.left) {\n            node = node.left;\n        }\n        return node;\n    }\n    findSubstr(key) {\n        const iter = this._iter.reset(key);\n        let node = this._root;\n        let candidate = undefined;\n        while (node) {\n            const val = iter.cmp(node.segment);\n            if (val > 0) {\n                // left\n                node = node.left;\n            }\n            else if (val < 0) {\n                // right\n                node = node.right;\n            }\n            else if (iter.hasNext()) {\n                // mid\n                iter.next();\n                candidate = node.value || candidate;\n                node = node.mid;\n            }\n            else {\n                break;\n            }\n        }\n        return node && node.value || candidate;\n    }\n    findSuperstr(key) {\n        return this._findSuperstrOrElement(key, false);\n    }\n    _findSuperstrOrElement(key, allowValue) {\n        const iter = this._iter.reset(key);\n        let node = this._root;\n        while (node) {\n            const val = iter.cmp(node.segment);\n            if (val > 0) {\n                // left\n                node = node.left;\n            }\n            else if (val < 0) {\n                // right\n                node = node.right;\n            }\n            else if (iter.hasNext()) {\n                // mid\n                iter.next();\n                node = node.mid;\n            }\n            else {\n                // collect\n                if (!node.mid) {\n                    if (allowValue) {\n                        return node.value;\n                    }\n                    else {\n                        return undefined;\n                    }\n                }\n                else {\n                    return this._entries(node.mid);\n                }\n            }\n        }\n        return undefined;\n    }\n    forEach(callback) {\n        for (const [key, value] of this) {\n            callback(value, key);\n        }\n    }\n    *[Symbol.iterator]() {\n        yield* this._entries(this._root);\n    }\n    _entries(node) {\n        const result = [];\n        this._dfsEntries(node, result);\n        return result[Symbol.iterator]();\n    }\n    _dfsEntries(node, bucket) {\n        // DFS\n        if (!node) {\n            return;\n        }\n        if (node.left) {\n            this._dfsEntries(node.left, bucket);\n        }\n        if (node.value) {\n            bucket.push([node.key, node.value]);\n        }\n        if (node.mid) {\n            this._dfsEntries(node.mid, bucket);\n        }\n        if (node.right) {\n            this._dfsEntries(node.right, bucket);\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,OAAO,EAAEC,iBAAiB,EAAEC,gBAAgB,EAAEC,0BAA0B,QAAQ,cAAc;AACvG,OAAO,MAAMC,cAAc,CAAC;EACxBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,MAAM,GAAG,EAAE;IAChB,IAAI,CAACC,IAAI,GAAG,CAAC;EACjB;EACAC,KAAKA,CAACC,GAAG,EAAE;IACP,IAAI,CAACH,MAAM,GAAGG,GAAG;IACjB,IAAI,CAACF,IAAI,GAAG,CAAC;IACb,OAAO,IAAI;EACf;EACAG,IAAIA,CAAA,EAAG;IACH,IAAI,CAACH,IAAI,IAAI,CAAC;IACd,OAAO,IAAI;EACf;EACAI,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACJ,IAAI,GAAG,IAAI,CAACD,MAAM,CAACM,MAAM,GAAG,CAAC;EAC7C;EACAC,GAAGA,CAACC,CAAC,EAAE;IACH,MAAMC,KAAK,GAAGD,CAAC,CAACE,UAAU,CAAC,CAAC,CAAC;IAC7B,MAAMC,QAAQ,GAAG,IAAI,CAACX,MAAM,CAACU,UAAU,CAAC,IAAI,CAACT,IAAI,CAAC;IAClD,OAAOQ,KAAK,GAAGE,QAAQ;EAC3B;EACAC,KAAKA,CAAA,EAAG;IACJ,OAAO,IAAI,CAACZ,MAAM,CAAC,IAAI,CAACC,IAAI,CAAC;EACjC;AACJ;AACA,OAAO,MAAMY,kBAAkB,CAAC;EAC5Bd,WAAWA,CAACe,cAAc,GAAG,IAAI,EAAE;IAC/B,IAAI,CAACA,cAAc,GAAGA,cAAc;EACxC;EACAZ,KAAKA,CAACC,GAAG,EAAE;IACP,IAAI,CAACH,MAAM,GAAGG,GAAG;IACjB,IAAI,CAACY,KAAK,GAAG,CAAC;IACd,IAAI,CAACC,GAAG,GAAG,CAAC;IACZ,OAAO,IAAI,CAACZ,IAAI,CAAC,CAAC;EACtB;EACAC,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACW,GAAG,GAAG,IAAI,CAAChB,MAAM,CAACM,MAAM;EACxC;EACAF,IAAIA,CAAA,EAAG;IACH;IACA,IAAI,CAACW,KAAK,GAAG,IAAI,CAACC,GAAG;IACrB,IAAIC,QAAQ,GAAG,IAAI;IACnB,OAAO,IAAI,CAACD,GAAG,GAAG,IAAI,CAAChB,MAAM,CAACM,MAAM,EAAE,IAAI,CAACU,GAAG,EAAE,EAAE;MAC9C,MAAME,EAAE,GAAG,IAAI,CAAClB,MAAM,CAACU,UAAU,CAAC,IAAI,CAACM,GAAG,CAAC;MAC3C,IAAIE,EAAE,KAAK,EAAE,CAAC,uBAAuB;QACjC,IAAID,QAAQ,EAAE;UACV,IAAI,CAACF,KAAK,EAAE;QAChB,CAAC,MACI;UACD;QACJ;MACJ,CAAC,MACI;QACDE,QAAQ,GAAG,KAAK;MACpB;IACJ;IACA,OAAO,IAAI;EACf;EACAV,GAAGA,CAACC,CAAC,EAAE;IACH,OAAO,IAAI,CAACM,cAAc,GACpBlB,gBAAgB,CAACY,CAAC,EAAE,IAAI,CAACR,MAAM,EAAE,CAAC,EAAEQ,CAAC,CAACF,MAAM,EAAE,IAAI,CAACS,KAAK,EAAE,IAAI,CAACC,GAAG,CAAC,GACnEnB,0BAA0B,CAACW,CAAC,EAAE,IAAI,CAACR,MAAM,EAAE,CAAC,EAAEQ,CAAC,CAACF,MAAM,EAAE,IAAI,CAACS,KAAK,EAAE,IAAI,CAACC,GAAG,CAAC;EACvF;EACAJ,KAAKA,CAAA,EAAG;IACJ,OAAO,IAAI,CAACZ,MAAM,CAACmB,SAAS,CAAC,IAAI,CAACJ,KAAK,EAAE,IAAI,CAACC,GAAG,CAAC;EACtD;AACJ;AACA,OAAO,MAAMI,YAAY,CAAC;EACtBrB,WAAWA,CAACsB,iBAAiB,GAAG,IAAI,EAAEP,cAAc,GAAG,IAAI,EAAE;IACzD,IAAI,CAACO,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACP,cAAc,GAAGA,cAAc;EACxC;EACAZ,KAAKA,CAACC,GAAG,EAAE;IACP,IAAI,CAACY,KAAK,GAAG,CAAC;IACd,IAAI,CAACC,GAAG,GAAG,CAAC;IACZ,IAAI,CAAChB,MAAM,GAAGG,GAAG;IACjB,IAAI,CAACmB,SAAS,GAAGnB,GAAG,CAACG,MAAM;IAC3B,KAAK,IAAIiB,GAAG,GAAGpB,GAAG,CAACG,MAAM,GAAG,CAAC,EAAEiB,GAAG,IAAI,CAAC,EAAEA,GAAG,EAAE,EAAE,IAAI,CAACD,SAAS,EAAE,EAAE;MAC9D,MAAMJ,EAAE,GAAG,IAAI,CAAClB,MAAM,CAACU,UAAU,CAACa,GAAG,CAAC;MACtC,IAAI,EAAEL,EAAE,KAAK,EAAE,CAAC,wBAAwB,IAAI,CAACG,iBAAiB,IAAIH,EAAE,KAAK,EAAE,CAAC,yBAAyB,EAAE;QACnG;MACJ;IACJ;IACA,OAAO,IAAI,CAACd,IAAI,CAAC,CAAC;EACtB;EACAC,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACW,GAAG,GAAG,IAAI,CAACM,SAAS;EACpC;EACAlB,IAAIA,CAAA,EAAG;IACH;IACA,IAAI,CAACW,KAAK,GAAG,IAAI,CAACC,GAAG;IACrB,IAAIC,QAAQ,GAAG,IAAI;IACnB,OAAO,IAAI,CAACD,GAAG,GAAG,IAAI,CAACM,SAAS,EAAE,IAAI,CAACN,GAAG,EAAE,EAAE;MAC1C,MAAME,EAAE,GAAG,IAAI,CAAClB,MAAM,CAACU,UAAU,CAAC,IAAI,CAACM,GAAG,CAAC;MAC3C,IAAIE,EAAE,KAAK,EAAE,CAAC,wBAAwB,IAAI,CAACG,iBAAiB,IAAIH,EAAE,KAAK,EAAE,CAAC,0BAA0B;QAChG,IAAID,QAAQ,EAAE;UACV,IAAI,CAACF,KAAK,EAAE;QAChB,CAAC,MACI;UACD;QACJ;MACJ,CAAC,MACI;QACDE,QAAQ,GAAG,KAAK;MACpB;IACJ;IACA,OAAO,IAAI;EACf;EACAV,GAAGA,CAACC,CAAC,EAAE;IACH,OAAO,IAAI,CAACM,cAAc,GACpBlB,gBAAgB,CAACY,CAAC,EAAE,IAAI,CAACR,MAAM,EAAE,CAAC,EAAEQ,CAAC,CAACF,MAAM,EAAE,IAAI,CAACS,KAAK,EAAE,IAAI,CAACC,GAAG,CAAC,GACnEnB,0BAA0B,CAACW,CAAC,EAAE,IAAI,CAACR,MAAM,EAAE,CAAC,EAAEQ,CAAC,CAACF,MAAM,EAAE,IAAI,CAACS,KAAK,EAAE,IAAI,CAACC,GAAG,CAAC;EACvF;EACAJ,KAAKA,CAAA,EAAG;IACJ,OAAO,IAAI,CAACZ,MAAM,CAACmB,SAAS,CAAC,IAAI,CAACJ,KAAK,EAAE,IAAI,CAACC,GAAG,CAAC;EACtD;AACJ;AACA,OAAO,MAAMQ,WAAW,CAAC;EACrBzB,WAAWA,CAAC0B,iBAAiB,EAAEC,uBAAuB,EAAE;IACpD,IAAI,CAACD,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACC,uBAAuB,GAAGA,uBAAuB;IACtD,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACC,SAAS,GAAG,CAAC;EACtB;EACA1B,KAAKA,CAACC,GAAG,EAAE;IACP,IAAI,CAACH,MAAM,GAAGG,GAAG;IACjB,IAAI,CAACwB,OAAO,GAAG,EAAE;IACjB,IAAI,IAAI,CAAC3B,MAAM,CAAC6B,MAAM,EAAE;MACpB,IAAI,CAACF,OAAO,CAACG,IAAI,CAAC,CAAC,CAAC,6BAA6B,CAAC;IACtD;IACA,IAAI,IAAI,CAAC9B,MAAM,CAAC+B,SAAS,EAAE;MACvB,IAAI,CAACJ,OAAO,CAACG,IAAI,CAAC,CAAC,CAAC,gCAAgC,CAAC;IACzD;IACA,IAAI,IAAI,CAAC9B,MAAM,CAACgC,IAAI,EAAE;MAClB,IAAI,CAACC,aAAa,GAAG,IAAIb,YAAY,CAAC,KAAK,EAAE,CAAC,IAAI,CAACK,iBAAiB,CAACtB,GAAG,CAAC,CAAC;MAC1E,IAAI,CAAC8B,aAAa,CAAC/B,KAAK,CAACC,GAAG,CAAC6B,IAAI,CAAC;MAClC,IAAI,IAAI,CAACC,aAAa,CAACrB,KAAK,CAAC,CAAC,EAAE;QAC5B,IAAI,CAACe,OAAO,CAACG,IAAI,CAAC,CAAC,CAAC,2BAA2B,CAAC;MACpD;IACJ;IACA,IAAI,CAAC,IAAI,CAACJ,uBAAuB,CAACvB,GAAG,CAAC,EAAE;MACpC,IAAI,IAAI,CAACH,MAAM,CAACkC,KAAK,EAAE;QACnB,IAAI,CAACP,OAAO,CAACG,IAAI,CAAC,CAAC,CAAC,4BAA4B,CAAC;MACrD;MACA,IAAI,IAAI,CAAC9B,MAAM,CAACmC,QAAQ,EAAE;QACtB,IAAI,CAACR,OAAO,CAACG,IAAI,CAAC,CAAC,CAAC,+BAA+B,CAAC;MACxD;IACJ;IACA,IAAI,CAACF,SAAS,GAAG,CAAC;IAClB,OAAO,IAAI;EACf;EACAxB,IAAIA,CAAA,EAAG;IACH,IAAI,IAAI,CAACuB,OAAO,CAAC,IAAI,CAACC,SAAS,CAAC,KAAK,CAAC,CAAC,+BAA+B,IAAI,CAACK,aAAa,CAAC5B,OAAO,CAAC,CAAC,EAAE;MAChG,IAAI,CAAC4B,aAAa,CAAC7B,IAAI,CAAC,CAAC;IAC7B,CAAC,MACI;MACD,IAAI,CAACwB,SAAS,IAAI,CAAC;IACvB;IACA,OAAO,IAAI;EACf;EACAvB,OAAOA,CAAA,EAAG;IACN,OAAQ,IAAI,CAACsB,OAAO,CAAC,IAAI,CAACC,SAAS,CAAC,KAAK,CAAC,CAAC,+BAA+B,IAAI,CAACK,aAAa,CAAC5B,OAAO,CAAC,CAAC,IAC/F,IAAI,CAACuB,SAAS,GAAG,IAAI,CAACD,OAAO,CAACrB,MAAM,GAAG,CAAC;EACnD;EACAC,GAAGA,CAACC,CAAC,EAAE;IACH,IAAI,IAAI,CAACmB,OAAO,CAAC,IAAI,CAACC,SAAS,CAAC,KAAK,CAAC,CAAC,+BAA+B;MAClE,OAAOjC,iBAAiB,CAACa,CAAC,EAAE,IAAI,CAACR,MAAM,CAAC6B,MAAM,CAAC;IACnD,CAAC,MACI,IAAI,IAAI,CAACF,OAAO,CAAC,IAAI,CAACC,SAAS,CAAC,KAAK,CAAC,CAAC,kCAAkC;MAC1E,OAAOjC,iBAAiB,CAACa,CAAC,EAAE,IAAI,CAACR,MAAM,CAAC+B,SAAS,CAAC;IACtD,CAAC,MACI,IAAI,IAAI,CAACJ,OAAO,CAAC,IAAI,CAACC,SAAS,CAAC,KAAK,CAAC,CAAC,6BAA6B;MACrE,OAAO,IAAI,CAACK,aAAa,CAAC1B,GAAG,CAACC,CAAC,CAAC;IACpC,CAAC,MACI,IAAI,IAAI,CAACmB,OAAO,CAAC,IAAI,CAACC,SAAS,CAAC,KAAK,CAAC,CAAC,8BAA8B;MACtE,OAAOlC,OAAO,CAACc,CAAC,EAAE,IAAI,CAACR,MAAM,CAACkC,KAAK,CAAC;IACxC,CAAC,MACI,IAAI,IAAI,CAACP,OAAO,CAAC,IAAI,CAACC,SAAS,CAAC,KAAK,CAAC,CAAC,iCAAiC;MACzE,OAAOlC,OAAO,CAACc,CAAC,EAAE,IAAI,CAACR,MAAM,CAACmC,QAAQ,CAAC;IAC3C;IACA,MAAM,IAAIC,KAAK,CAAC,CAAC;EACrB;EACAxB,KAAKA,CAAA,EAAG;IACJ,IAAI,IAAI,CAACe,OAAO,CAAC,IAAI,CAACC,SAAS,CAAC,KAAK,CAAC,CAAC,+BAA+B;MAClE,OAAO,IAAI,CAAC5B,MAAM,CAAC6B,MAAM;IAC7B,CAAC,MACI,IAAI,IAAI,CAACF,OAAO,CAAC,IAAI,CAACC,SAAS,CAAC,KAAK,CAAC,CAAC,kCAAkC;MAC1E,OAAO,IAAI,CAAC5B,MAAM,CAAC+B,SAAS;IAChC,CAAC,MACI,IAAI,IAAI,CAACJ,OAAO,CAAC,IAAI,CAACC,SAAS,CAAC,KAAK,CAAC,CAAC,6BAA6B;MACrE,OAAO,IAAI,CAACK,aAAa,CAACrB,KAAK,CAAC,CAAC;IACrC,CAAC,MACI,IAAI,IAAI,CAACe,OAAO,CAAC,IAAI,CAACC,SAAS,CAAC,KAAK,CAAC,CAAC,8BAA8B;MACtE,OAAO,IAAI,CAAC5B,MAAM,CAACkC,KAAK;IAC5B,CAAC,MACI,IAAI,IAAI,CAACP,OAAO,CAAC,IAAI,CAACC,SAAS,CAAC,KAAK,CAAC,CAAC,iCAAiC;MACzE,OAAO,IAAI,CAAC5B,MAAM,CAACmC,QAAQ;IAC/B;IACA,MAAM,IAAIC,KAAK,CAAC,CAAC;EACrB;AACJ;AACA,MAAMC,qBAAqB,CAAC;EACxBtC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACuC,MAAM,GAAG,CAAC;EACnB;EACAC,UAAUA,CAAA,EAAG;IACT,MAAMC,GAAG,GAAG,IAAI,CAACC,KAAK;IACtB,IAAI,CAACA,KAAK,GAAGD,GAAG,CAACE,IAAI;IACrBF,GAAG,CAACE,IAAI,GAAG,IAAI;IACf,IAAI,CAACC,YAAY,CAAC,CAAC;IACnBH,GAAG,CAACG,YAAY,CAAC,CAAC;IAClB,OAAOH,GAAG;EACd;EACAI,WAAWA,CAAA,EAAG;IACV,MAAMJ,GAAG,GAAG,IAAI,CAACE,IAAI;IACrB,IAAI,CAACA,IAAI,GAAGF,GAAG,CAACC,KAAK;IACrBD,GAAG,CAACC,KAAK,GAAG,IAAI;IAChB,IAAI,CAACE,YAAY,CAAC,CAAC;IACnBH,GAAG,CAACG,YAAY,CAAC,CAAC;IAClB,OAAOH,GAAG;EACd;EACAG,YAAYA,CAAA,EAAG;IACX,IAAI,CAACL,MAAM,GAAG,CAAC,GAAGO,IAAI,CAACC,GAAG,CAAC,IAAI,CAACC,UAAU,EAAE,IAAI,CAACC,WAAW,CAAC;EACjE;EACAC,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACD,WAAW,GAAG,IAAI,CAACD,UAAU;EAC7C;EACA,IAAIA,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACL,IAAI,EAAEJ,MAAM,IAAI,CAAC;EACjC;EACA,IAAIU,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACP,KAAK,EAAEH,MAAM,IAAI,CAAC;EAClC;AACJ;AACA,OAAO,MAAMY,iBAAiB,CAAC;EAC3B,OAAOC,OAAOA,CAACC,gBAAgB,GAAGA,CAAA,KAAM,KAAK,EAAEC,sBAAsB,GAAGA,CAAA,KAAM,KAAK,EAAE;IACjF,OAAO,IAAIH,iBAAiB,CAAC,IAAI1B,WAAW,CAAC4B,gBAAgB,EAAEC,sBAAsB,CAAC,CAAC;EAC3F;EACA,OAAOC,UAAUA,CAAA,EAAG;IAChB,OAAO,IAAIJ,iBAAiB,CAAC,IAAIpD,cAAc,CAAC,CAAC,CAAC;EACtD;EACA,OAAOyD,aAAaA,CAAA,EAAG;IACnB,OAAO,IAAIL,iBAAiB,CAAC,IAAIrC,kBAAkB,CAAC,CAAC,CAAC;EAC1D;EACAd,WAAWA,CAACyD,QAAQ,EAAE;IAClB,IAAI,CAACC,KAAK,GAAGD,QAAQ;EACzB;EACAE,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACC,KAAK,GAAGC,SAAS;EAC1B;EACAC,GAAGA,CAAC1D,GAAG,EAAE2D,OAAO,EAAE;IACd,MAAMC,IAAI,GAAG,IAAI,CAACN,KAAK,CAACvD,KAAK,CAACC,GAAG,CAAC;IAClC,IAAI6D,IAAI;IACR,IAAI,CAAC,IAAI,CAACL,KAAK,EAAE;MACb,IAAI,CAACA,KAAK,GAAG,IAAItB,qBAAqB,CAAC,CAAC;MACxC,IAAI,CAACsB,KAAK,CAACM,OAAO,GAAGF,IAAI,CAACnD,KAAK,CAAC,CAAC;IACrC;IACA,MAAMsD,KAAK,GAAG,EAAE;IAChB;IACAF,IAAI,GAAG,IAAI,CAACL,KAAK;IACjB,OAAO,IAAI,EAAE;MACT,MAAMQ,GAAG,GAAGJ,IAAI,CAACxD,GAAG,CAACyD,IAAI,CAACC,OAAO,CAAC;MAClC,IAAIE,GAAG,GAAG,CAAC,EAAE;QACT;QACA,IAAI,CAACH,IAAI,CAACtB,IAAI,EAAE;UACZsB,IAAI,CAACtB,IAAI,GAAG,IAAIL,qBAAqB,CAAC,CAAC;UACvC2B,IAAI,CAACtB,IAAI,CAACuB,OAAO,GAAGF,IAAI,CAACnD,KAAK,CAAC,CAAC;QACpC;QACAsD,KAAK,CAACpC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgBkC,IAAI,CAAC,CAAC;QACrCA,IAAI,GAAGA,IAAI,CAACtB,IAAI;MACpB,CAAC,MACI,IAAIyB,GAAG,GAAG,CAAC,EAAE;QACd;QACA,IAAI,CAACH,IAAI,CAACvB,KAAK,EAAE;UACbuB,IAAI,CAACvB,KAAK,GAAG,IAAIJ,qBAAqB,CAAC,CAAC;UACxC2B,IAAI,CAACvB,KAAK,CAACwB,OAAO,GAAGF,IAAI,CAACnD,KAAK,CAAC,CAAC;QACrC;QACAsD,KAAK,CAACpC,IAAI,CAAC,CAAC,CAAC,CAAC,iBAAiBkC,IAAI,CAAC,CAAC;QACrCA,IAAI,GAAGA,IAAI,CAACvB,KAAK;MACrB,CAAC,MACI,IAAIsB,IAAI,CAAC1D,OAAO,CAAC,CAAC,EAAE;QACrB;QACA0D,IAAI,CAAC3D,IAAI,CAAC,CAAC;QACX,IAAI,CAAC4D,IAAI,CAACI,GAAG,EAAE;UACXJ,IAAI,CAACI,GAAG,GAAG,IAAI/B,qBAAqB,CAAC,CAAC;UACtC2B,IAAI,CAACI,GAAG,CAACH,OAAO,GAAGF,IAAI,CAACnD,KAAK,CAAC,CAAC;QACnC;QACAsD,KAAK,CAACpC,IAAI,CAAC,CAAC,CAAC,CAAC,eAAekC,IAAI,CAAC,CAAC;QACnCA,IAAI,GAAGA,IAAI,CAACI,GAAG;MACnB,CAAC,MACI;QACD;MACJ;IACJ;IACA;IACA,MAAMC,UAAU,GAAGL,IAAI,CAACpD,KAAK;IAC7BoD,IAAI,CAACpD,KAAK,GAAGkD,OAAO;IACpBE,IAAI,CAAC7D,GAAG,GAAGA,GAAG;IACd;IACA,KAAK,IAAImE,CAAC,GAAGJ,KAAK,CAAC5D,MAAM,GAAG,CAAC,EAAEgE,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MACxC,MAAMN,IAAI,GAAGE,KAAK,CAACI,CAAC,CAAC,CAAC,CAAC,CAAC;MACxBN,IAAI,CAACrB,YAAY,CAAC,CAAC;MACnB,MAAM4B,EAAE,GAAGP,IAAI,CAACf,aAAa,CAAC,CAAC;MAC/B,IAAIsB,EAAE,GAAG,CAAC,CAAC,IAAIA,EAAE,GAAG,CAAC,EAAE;QACnB;QACA,MAAMC,EAAE,GAAGN,KAAK,CAACI,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,MAAMG,EAAE,GAAGP,KAAK,CAACI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,IAAIE,EAAE,KAAK,CAAC,CAAC,mBAAmBC,EAAE,KAAK,CAAC,CAAC,iBAAiB;UACtD;UACAP,KAAK,CAACI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGN,IAAI,CAACzB,UAAU,CAAC,CAAC;QACnC,CAAC,MACI,IAAIiC,EAAE,KAAK,CAAC,CAAC,CAAC,kBAAkBC,EAAE,KAAK,CAAC,CAAC,CAAC,gBAAgB;UAC3D;UACAP,KAAK,CAACI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGN,IAAI,CAACpB,WAAW,CAAC,CAAC;QACpC,CAAC,MACI,IAAI4B,EAAE,KAAK,CAAC,CAAC,mBAAmBC,EAAE,KAAK,CAAC,CAAC,CAAC,gBAAgB;UAC3D;UACAT,IAAI,CAACvB,KAAK,GAAGyB,KAAK,CAACI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGJ,KAAK,CAACI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC1B,WAAW,CAAC,CAAC;UAC5DsB,KAAK,CAACI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGN,IAAI,CAACzB,UAAU,CAAC,CAAC;QACnC,CAAC,MACI,IAAIiC,EAAE,KAAK,CAAC,CAAC,CAAC,kBAAkBC,EAAE,KAAK,CAAC,CAAC,iBAAiB;UAC3D;UACAT,IAAI,CAACtB,IAAI,GAAGwB,KAAK,CAACI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGJ,KAAK,CAACI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC/B,UAAU,CAAC,CAAC;UAC1D2B,KAAK,CAACI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGN,IAAI,CAACpB,WAAW,CAAC,CAAC;QACpC,CAAC,MACI;UACD,MAAM,IAAIR,KAAK,CAAC,CAAC;QACrB;QACA;QACA,IAAIkC,CAAC,GAAG,CAAC,EAAE;UACP,QAAQJ,KAAK,CAACI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACnB,KAAK,CAAC,CAAC,CAAC;cACJJ,KAAK,CAACI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC5B,IAAI,GAAGwB,KAAK,CAACI,CAAC,CAAC,CAAC,CAAC,CAAC;cAClC;YACJ,KAAK,CAAC,CAAC;cACHJ,KAAK,CAACI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC7B,KAAK,GAAGyB,KAAK,CAACI,CAAC,CAAC,CAAC,CAAC,CAAC;cACnC;YACJ,KAAK,CAAC,CAAC;cACHJ,KAAK,CAACI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAACF,GAAG,GAAGF,KAAK,CAACI,CAAC,CAAC,CAAC,CAAC,CAAC;cACjC;UACR;QACJ,CAAC,MACI;UACD,IAAI,CAACX,KAAK,GAAGO,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B;MACJ;IACJ;IACA,OAAOG,UAAU;EACrB;EACAK,GAAGA,CAACvE,GAAG,EAAE;IACL,OAAO,IAAI,CAACwE,QAAQ,CAACxE,GAAG,CAAC,EAAES,KAAK;EACpC;EACA+D,QAAQA,CAACxE,GAAG,EAAE;IACV,MAAM4D,IAAI,GAAG,IAAI,CAACN,KAAK,CAACvD,KAAK,CAACC,GAAG,CAAC;IAClC,IAAI6D,IAAI,GAAG,IAAI,CAACL,KAAK;IACrB,OAAOK,IAAI,EAAE;MACT,MAAMG,GAAG,GAAGJ,IAAI,CAACxD,GAAG,CAACyD,IAAI,CAACC,OAAO,CAAC;MAClC,IAAIE,GAAG,GAAG,CAAC,EAAE;QACT;QACAH,IAAI,GAAGA,IAAI,CAACtB,IAAI;MACpB,CAAC,MACI,IAAIyB,GAAG,GAAG,CAAC,EAAE;QACd;QACAH,IAAI,GAAGA,IAAI,CAACvB,KAAK;MACrB,CAAC,MACI,IAAIsB,IAAI,CAAC1D,OAAO,CAAC,CAAC,EAAE;QACrB;QACA0D,IAAI,CAAC3D,IAAI,CAAC,CAAC;QACX4D,IAAI,GAAGA,IAAI,CAACI,GAAG;MACnB,CAAC,MACI;QACD;MACJ;IACJ;IACA,OAAOJ,IAAI;EACf;EACAY,GAAGA,CAACzE,GAAG,EAAE;IACL,MAAM6D,IAAI,GAAG,IAAI,CAACW,QAAQ,CAACxE,GAAG,CAAC;IAC/B,OAAO,EAAE6D,IAAI,EAAEpD,KAAK,KAAKgD,SAAS,IAAII,IAAI,EAAEI,GAAG,KAAKR,SAAS,CAAC;EAClE;EACAiB,MAAMA,CAAC1E,GAAG,EAAE;IACR,OAAO,IAAI,CAAC2E,OAAO,CAAC3E,GAAG,EAAE,KAAK,CAAC;EACnC;EACA4E,cAAcA,CAAC5E,GAAG,EAAE;IAChB,OAAO,IAAI,CAAC2E,OAAO,CAAC3E,GAAG,EAAE,IAAI,CAAC;EAClC;EACA2E,OAAOA,CAAC3E,GAAG,EAAE6E,QAAQ,EAAE;IACnB,MAAMjB,IAAI,GAAG,IAAI,CAACN,KAAK,CAACvD,KAAK,CAACC,GAAG,CAAC;IAClC,MAAM+D,KAAK,GAAG,EAAE;IAChB,IAAIF,IAAI,GAAG,IAAI,CAACL,KAAK;IACrB;IACA,OAAOK,IAAI,EAAE;MACT,MAAMG,GAAG,GAAGJ,IAAI,CAACxD,GAAG,CAACyD,IAAI,CAACC,OAAO,CAAC;MAClC,IAAIE,GAAG,GAAG,CAAC,EAAE;QACT;QACAD,KAAK,CAACpC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgBkC,IAAI,CAAC,CAAC;QACrCA,IAAI,GAAGA,IAAI,CAACtB,IAAI;MACpB,CAAC,MACI,IAAIyB,GAAG,GAAG,CAAC,EAAE;QACd;QACAD,KAAK,CAACpC,IAAI,CAAC,CAAC,CAAC,CAAC,iBAAiBkC,IAAI,CAAC,CAAC;QACrCA,IAAI,GAAGA,IAAI,CAACvB,KAAK;MACrB,CAAC,MACI,IAAIsB,IAAI,CAAC1D,OAAO,CAAC,CAAC,EAAE;QACrB;QACA0D,IAAI,CAAC3D,IAAI,CAAC,CAAC;QACX8D,KAAK,CAACpC,IAAI,CAAC,CAAC,CAAC,CAAC,eAAekC,IAAI,CAAC,CAAC;QACnCA,IAAI,GAAGA,IAAI,CAACI,GAAG;MACnB,CAAC,MACI;QACD;MACJ;IACJ;IACA,IAAI,CAACJ,IAAI,EAAE;MACP;MACA;IACJ;IACA,IAAIgB,QAAQ,EAAE;MACV;MACAhB,IAAI,CAACtB,IAAI,GAAGkB,SAAS;MACrBI,IAAI,CAACI,GAAG,GAAGR,SAAS;MACpBI,IAAI,CAACvB,KAAK,GAAGmB,SAAS;MACtBI,IAAI,CAAC1B,MAAM,GAAG,CAAC;IACnB,CAAC,MACI;MACD;MACA0B,IAAI,CAAC7D,GAAG,GAAGyD,SAAS;MACpBI,IAAI,CAACpD,KAAK,GAAGgD,SAAS;IAC1B;IACA;IACA,IAAI,CAACI,IAAI,CAACI,GAAG,IAAI,CAACJ,IAAI,CAACpD,KAAK,EAAE;MAC1B,IAAIoD,IAAI,CAACtB,IAAI,IAAIsB,IAAI,CAACvB,KAAK,EAAE;QACzB;QACA;QACA;QACA,MAAMwC,GAAG,GAAG,IAAI,CAACC,IAAI,CAAClB,IAAI,CAACvB,KAAK,CAAC;QACjC,IAAIwC,GAAG,CAAC9E,GAAG,EAAE;UACT,MAAM;YAAEA,GAAG;YAAES,KAAK;YAAEqD;UAAQ,CAAC,GAAGgB,GAAG;UACnC,IAAI,CAACH,OAAO,CAACG,GAAG,CAAC9E,GAAG,EAAE,KAAK,CAAC;UAC5B6D,IAAI,CAAC7D,GAAG,GAAGA,GAAG;UACd6D,IAAI,CAACpD,KAAK,GAAGA,KAAK;UAClBoD,IAAI,CAACC,OAAO,GAAGA,OAAO;QAC1B;MACJ,CAAC,MACI;QACD;QACA,MAAMkB,QAAQ,GAAGnB,IAAI,CAACtB,IAAI,IAAIsB,IAAI,CAACvB,KAAK;QACxC,IAAIyB,KAAK,CAAC5D,MAAM,GAAG,CAAC,EAAE;UAClB,MAAM,CAAC8E,GAAG,EAAEC,MAAM,CAAC,GAAGnB,KAAK,CAACA,KAAK,CAAC5D,MAAM,GAAG,CAAC,CAAC;UAC7C,QAAQ8E,GAAG;YACP,KAAK,CAAC,CAAC,CAAC;cACJC,MAAM,CAAC3C,IAAI,GAAGyC,QAAQ;cACtB;YACJ,KAAK,CAAC,CAAC;cACHE,MAAM,CAACjB,GAAG,GAAGe,QAAQ;cACrB;YACJ,KAAK,CAAC,CAAC;cACHE,MAAM,CAAC5C,KAAK,GAAG0C,QAAQ;cACvB;UACR;QACJ,CAAC,MACI;UACD,IAAI,CAACxB,KAAK,GAAGwB,QAAQ;QACzB;MACJ;IACJ;IACA;IACA,KAAK,IAAIb,CAAC,GAAGJ,KAAK,CAAC5D,MAAM,GAAG,CAAC,EAAEgE,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MACxC,MAAMN,IAAI,GAAGE,KAAK,CAACI,CAAC,CAAC,CAAC,CAAC,CAAC;MACxBN,IAAI,CAACrB,YAAY,CAAC,CAAC;MACnB,MAAM4B,EAAE,GAAGP,IAAI,CAACf,aAAa,CAAC,CAAC;MAC/B,IAAIsB,EAAE,GAAG,CAAC,EAAE;QACR;QACA,IAAIP,IAAI,CAACvB,KAAK,CAACQ,aAAa,CAAC,CAAC,IAAI,CAAC,EAAE;UACjC;UACAiB,KAAK,CAACI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGN,IAAI,CAACzB,UAAU,CAAC,CAAC;QACnC,CAAC,MACI;UACD;UACAyB,IAAI,CAACvB,KAAK,GAAGuB,IAAI,CAACvB,KAAK,CAACG,WAAW,CAAC,CAAC;UACrCsB,KAAK,CAACI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGN,IAAI,CAACzB,UAAU,CAAC,CAAC;QACnC;MACJ,CAAC,MACI,IAAIgC,EAAE,GAAG,CAAC,CAAC,EAAE;QACd;QACA,IAAIP,IAAI,CAACtB,IAAI,CAACO,aAAa,CAAC,CAAC,IAAI,CAAC,EAAE;UAChC;UACAiB,KAAK,CAACI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGN,IAAI,CAACpB,WAAW,CAAC,CAAC;QACpC,CAAC,MACI;UACD;UACAoB,IAAI,CAACtB,IAAI,GAAGsB,IAAI,CAACtB,IAAI,CAACH,UAAU,CAAC,CAAC;UAClC2B,KAAK,CAACI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGN,IAAI,CAACpB,WAAW,CAAC,CAAC;QACpC;MACJ;MACA;MACA,IAAI0B,CAAC,GAAG,CAAC,EAAE;QACP,QAAQJ,KAAK,CAACI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,KAAK,CAAC,CAAC,CAAC;YACJJ,KAAK,CAACI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC5B,IAAI,GAAGwB,KAAK,CAACI,CAAC,CAAC,CAAC,CAAC,CAAC;YAClC;UACJ,KAAK,CAAC,CAAC;YACHJ,KAAK,CAACI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC7B,KAAK,GAAGyB,KAAK,CAACI,CAAC,CAAC,CAAC,CAAC,CAAC;YACnC;UACJ,KAAK,CAAC,CAAC;YACHJ,KAAK,CAACI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAACF,GAAG,GAAGF,KAAK,CAACI,CAAC,CAAC,CAAC,CAAC,CAAC;YACjC;QACR;MACJ,CAAC,MACI;QACD,IAAI,CAACX,KAAK,GAAGO,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B;IACJ;EACJ;EACAgB,IAAIA,CAAClB,IAAI,EAAE;IACP,OAAOA,IAAI,CAACtB,IAAI,EAAE;MACdsB,IAAI,GAAGA,IAAI,CAACtB,IAAI;IACpB;IACA,OAAOsB,IAAI;EACf;EACAsB,UAAUA,CAACnF,GAAG,EAAE;IACZ,MAAM4D,IAAI,GAAG,IAAI,CAACN,KAAK,CAACvD,KAAK,CAACC,GAAG,CAAC;IAClC,IAAI6D,IAAI,GAAG,IAAI,CAACL,KAAK;IACrB,IAAI4B,SAAS,GAAG3B,SAAS;IACzB,OAAOI,IAAI,EAAE;MACT,MAAMG,GAAG,GAAGJ,IAAI,CAACxD,GAAG,CAACyD,IAAI,CAACC,OAAO,CAAC;MAClC,IAAIE,GAAG,GAAG,CAAC,EAAE;QACT;QACAH,IAAI,GAAGA,IAAI,CAACtB,IAAI;MACpB,CAAC,MACI,IAAIyB,GAAG,GAAG,CAAC,EAAE;QACd;QACAH,IAAI,GAAGA,IAAI,CAACvB,KAAK;MACrB,CAAC,MACI,IAAIsB,IAAI,CAAC1D,OAAO,CAAC,CAAC,EAAE;QACrB;QACA0D,IAAI,CAAC3D,IAAI,CAAC,CAAC;QACXmF,SAAS,GAAGvB,IAAI,CAACpD,KAAK,IAAI2E,SAAS;QACnCvB,IAAI,GAAGA,IAAI,CAACI,GAAG;MACnB,CAAC,MACI;QACD;MACJ;IACJ;IACA,OAAOJ,IAAI,IAAIA,IAAI,CAACpD,KAAK,IAAI2E,SAAS;EAC1C;EACAC,YAAYA,CAACrF,GAAG,EAAE;IACd,OAAO,IAAI,CAACsF,sBAAsB,CAACtF,GAAG,EAAE,KAAK,CAAC;EAClD;EACAsF,sBAAsBA,CAACtF,GAAG,EAAEuF,UAAU,EAAE;IACpC,MAAM3B,IAAI,GAAG,IAAI,CAACN,KAAK,CAACvD,KAAK,CAACC,GAAG,CAAC;IAClC,IAAI6D,IAAI,GAAG,IAAI,CAACL,KAAK;IACrB,OAAOK,IAAI,EAAE;MACT,MAAMG,GAAG,GAAGJ,IAAI,CAACxD,GAAG,CAACyD,IAAI,CAACC,OAAO,CAAC;MAClC,IAAIE,GAAG,GAAG,CAAC,EAAE;QACT;QACAH,IAAI,GAAGA,IAAI,CAACtB,IAAI;MACpB,CAAC,MACI,IAAIyB,GAAG,GAAG,CAAC,EAAE;QACd;QACAH,IAAI,GAAGA,IAAI,CAACvB,KAAK;MACrB,CAAC,MACI,IAAIsB,IAAI,CAAC1D,OAAO,CAAC,CAAC,EAAE;QACrB;QACA0D,IAAI,CAAC3D,IAAI,CAAC,CAAC;QACX4D,IAAI,GAAGA,IAAI,CAACI,GAAG;MACnB,CAAC,MACI;QACD;QACA,IAAI,CAACJ,IAAI,CAACI,GAAG,EAAE;UACX,IAAIsB,UAAU,EAAE;YACZ,OAAO1B,IAAI,CAACpD,KAAK;UACrB,CAAC,MACI;YACD,OAAOgD,SAAS;UACpB;QACJ,CAAC,MACI;UACD,OAAO,IAAI,CAAC+B,QAAQ,CAAC3B,IAAI,CAACI,GAAG,CAAC;QAClC;MACJ;IACJ;IACA,OAAOR,SAAS;EACpB;EACAgC,OAAOA,CAACC,QAAQ,EAAE;IACd,KAAK,MAAM,CAAC1F,GAAG,EAAES,KAAK,CAAC,IAAI,IAAI,EAAE;MAC7BiF,QAAQ,CAACjF,KAAK,EAAET,GAAG,CAAC;IACxB;EACJ;EACA,EAAE2F,MAAM,CAACC,QAAQ,IAAI;IACjB,OAAO,IAAI,CAACJ,QAAQ,CAAC,IAAI,CAAChC,KAAK,CAAC;EACpC;EACAgC,QAAQA,CAAC3B,IAAI,EAAE;IACX,MAAMgC,MAAM,GAAG,EAAE;IACjB,IAAI,CAACC,WAAW,CAACjC,IAAI,EAAEgC,MAAM,CAAC;IAC9B,OAAOA,MAAM,CAACF,MAAM,CAACC,QAAQ,CAAC,CAAC,CAAC;EACpC;EACAE,WAAWA,CAACjC,IAAI,EAAEkC,MAAM,EAAE;IACtB;IACA,IAAI,CAAClC,IAAI,EAAE;MACP;IACJ;IACA,IAAIA,IAAI,CAACtB,IAAI,EAAE;MACX,IAAI,CAACuD,WAAW,CAACjC,IAAI,CAACtB,IAAI,EAAEwD,MAAM,CAAC;IACvC;IACA,IAAIlC,IAAI,CAACpD,KAAK,EAAE;MACZsF,MAAM,CAACpE,IAAI,CAAC,CAACkC,IAAI,CAAC7D,GAAG,EAAE6D,IAAI,CAACpD,KAAK,CAAC,CAAC;IACvC;IACA,IAAIoD,IAAI,CAACI,GAAG,EAAE;MACV,IAAI,CAAC6B,WAAW,CAACjC,IAAI,CAACI,GAAG,EAAE8B,MAAM,CAAC;IACtC;IACA,IAAIlC,IAAI,CAACvB,KAAK,EAAE;MACZ,IAAI,CAACwD,WAAW,CAACjC,IAAI,CAACvB,KAAK,EAAEyD,MAAM,CAAC;IACxC;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}