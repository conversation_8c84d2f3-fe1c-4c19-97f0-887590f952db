{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, ViewChild } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Subject, takeUntil } from 'rxjs';\nimport { FormsModule, Validators } from '@angular/forms';\n// Import child components\nimport { ChatInterfaceComponent } from '@shared/components/chat-interface/chat-interface.component';\nimport { AgentActivityComponent } from './components/agent-activity/agent-activity.component';\n// Removed AgentOutputComponent - now using inline output display\nimport { ButtonComponent, IconComponent, TabsComponent } from '@ava/play-comp-library';\nimport { environment } from '@shared/environments/environment';\nimport workflowConstants from './../constants/workflows.json';\nimport { ExecutionStatus } from '@shared/models/execution.model';\nimport { WorkflowPlaygroundComponent } from './components/workflow-playground/workflow-playground.component';\nlet WorkflowExecutionComponent = class WorkflowExecutionComponent {\n  route;\n  router;\n  workflowService;\n  inputExtractorService;\n  tokenStorage;\n  loaderService;\n  formBuilder;\n  navigationTabs = [{\n    id: 'nav-home',\n    label: 'Execution'\n  }, {\n    id: 'nav-products',\n    label: 'Output'\n  }, {\n    id: 'nav-services',\n    label: 'Configuration'\n  }];\n  // Workflow details\n  workflowId = null;\n  workflowName = 'Workflow';\n  constants = workflowConstants;\n  chatInterfaceComp;\n  // Activity logs\n  activityLogs = [];\n  activityProgress = 0;\n  executionDetails;\n  isRunning = false;\n  status = ExecutionStatus.notStarted;\n  // Chat messages\n  chatMessages = [];\n  isProcessingChat = false;\n  inputText = '';\n  // Agent outputs\n  agentOutputs = [];\n  workflowForm;\n  fileType = '.zip';\n  // Execution state\n  executionStartTime = null;\n  executionCompleted = false;\n  executionId;\n  workflowLogs = [];\n  displayedLogsCount = 10;\n  showAllLogs = false;\n  enableStreamingLog = environment.enableLogStreaming || 'all';\n  isExecutionComplete = false;\n  progressInterval;\n  // Component lifecycle\n  destroy$ = new Subject();\n  selectedTab = 'Agent Activity';\n  demoTabs = [{\n    id: 'activity',\n    label: 'Agent Activity'\n  }, {\n    id: 'agents',\n    label: 'Agent Output'\n  }, {\n    id: 'preview',\n    label: 'Preview',\n    disabled: true\n  }];\n  errorMsg = false;\n  resMessage;\n  taskMessage = [];\n  isJsonValid = false;\n  disableChat = false;\n  selectedFiles = [];\n  workflowAgents = [];\n  userInputList = [];\n  progress = 0;\n  isLoading = false;\n  loaderColor = '';\n  inputFieldOrder = [];\n  currentInputIndex = 0;\n  activeTabId = 'nav-home';\n  // New properties for workflow playground\n  agents = [];\n  isPlaygroundCollapsed = false;\n  pipelineAgents = [];\n  constructor(route, router, workflowService, inputExtractorService, tokenStorage, loaderService, formBuilder) {\n    this.route = route;\n    this.router = router;\n    this.workflowService = workflowService;\n    this.inputExtractorService = inputExtractorService;\n    this.tokenStorage = tokenStorage;\n    this.loaderService = loaderService;\n    this.formBuilder = formBuilder;\n  }\n  ngOnInit() {\n    this.loaderService.disableLoader();\n    this.selectedTab = 'Agent Activity';\n    this.executionId = crypto.randomUUID();\n    // Get workflow ID from route params\n    this.route.paramMap.pipe(takeUntil(this.destroy$)).subscribe(params => {\n      this.workflowId = params.get('id');\n      if (this.workflowId) {\n        this.loadWorkflow(this.workflowId);\n      } else {\n        // No workflow ID, redirect back to workflows page\n        this.router.navigate(['/build/workflows']);\n      }\n    });\n    // this.executeWorkflow()\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n    this.loaderService.enableLoader();\n  }\n  onTabChange(event) {\n    this.selectedTab = event.label;\n    this.activeTabId = event.id;\n    console.log('Tab changed:', event);\n  }\n  // Load workflow data\n  loadWorkflow(id) {\n    // In a real app, this would fetch the workflow from a service\n    console.log(`Loading workflow with ID: ${id}`);\n    this.chatMessages = [{\n      from: 'ai',\n      text: 'I am your workflow assistant. I will help you in executing this workflow.'\n    }];\n    this.workflowForm = this.formBuilder.group({});\n    // Load workflow data for chat interface\n    this.workflowService.getWorkflowById(id).subscribe({\n      next: res => {\n        this.workflowAgents = res.workflowAgents;\n        this.userInputList = this.extractInputField(this.workflowAgents);\n        if (this.userInputList.length === 0) {\n          this.disableChat = true;\n        }\n        this.workflowName = res.name;\n        this.initializeForm();\n        this.startInputCollection();\n        this.initializeDemoLogs(); // Initialize demo logs for testing\n      },\n      error: err => {\n        this.disableChat = true;\n        this.chatInterfaceComp.addAiResponse('No input is required for this workflow. Click on send button to execute the workflow.');\n        console.log(err);\n      }\n    });\n    // Also load workflow details for the new playground component\n    this.getWorkflowDetails(id);\n  }\n  // New method to get workflow details using workflow service\n  getWorkflowDetails(id) {\n    this.workflowService.getOneWorkflow(id).subscribe({\n      next: res => {\n        if (res?.pipeLineAgents?.length) {\n          this.pipelineAgents = res.pipeLineAgents;\n          this.workflowName = res.name;\n          // Convert pipeline agents to AgentData format\n          try {\n            this.agents = this.inputExtractorService.convertToAgentData(this.pipelineAgents);\n            console.log('Converted agents:', this.agents);\n          } catch (error) {\n            console.error('Error converting pipeline agents to AgentData:', error);\n            this.agents = [];\n          }\n          // Extract input fields for form initialization\n          this.userInputList = this.extractInputField(res.pipeLineAgents);\n          this.initializeForm();\n        } else {\n          console.warn('No pipeline agents found in workflow response');\n          this.agents = [];\n        }\n      },\n      error: e => console.error(e)\n    });\n  }\n  extractInputField(pipeLineAgents) {\n    const PLACEHOLDER_PATTERNS = /(%\\d+\\$s)|\\{\\{([a-zA-Z0-9-_]+)\\}\\}/g;\n    const placeholderMap = {};\n    pipeLineAgents.forEach(pipelineAgent => {\n      const agentName = pipelineAgent?.agent?.name;\n      const agentDescription = pipelineAgent?.agent?.task?.description;\n      const matches = agentDescription?.matchAll(PLACEHOLDER_PATTERNS) || [];\n      for (const match of matches) {\n        const placeholder = match[1] || match[2];\n        const placeholderInput = match[0];\n        if (!placeholderMap[placeholder]) {\n          placeholderMap[placeholder] = {\n            agents: new Set(),\n            inputs: new Set()\n          };\n          ;\n        }\n        if (agentName) {\n          placeholderMap[placeholder].agents.add(agentName);\n        }\n        placeholderMap[placeholder].inputs.add(placeholderInput);\n      }\n    });\n    return Object.entries(placeholderMap).map(([placeholder, {\n      agents,\n      inputs\n    }]) => ({\n      name: [...agents].length > 2 ? `${[...agents].slice(0, -1).join(\", \")} and ${[...agents].at(-1)}` : [...agents].join(\" and \"),\n      placeholder,\n      input: [...inputs][0]\n    }));\n  }\n  isImageInput(input) {\n    const match = input.match(/{{(.*?)}}/);\n    if (match && match[1]) {\n      const variableName = match[1].trim();\n      return variableName.startsWith('image') || variableName.startsWith('Image');\n    }\n    return false;\n  }\n  initializeForm() {\n    console.log('Initializing form with userInputList:', this.userInputList);\n    this.userInputList.forEach(label => {\n      console.log('Adding form control for:', label.input);\n      this.workflowForm.addControl(label.input, this.formBuilder.control('', Validators.required));\n    });\n    // Also add form controls for agent inputs to ensure compatibility\n    if (this.agents) {\n      this.agents.forEach(agent => {\n        if (agent.inputs) {\n          agent.inputs.forEach(input => {\n            const controlName = input.placeholder;\n            if (!this.workflowForm.get(controlName)) {\n              console.log('Adding additional form control for agent input:', controlName);\n              this.workflowForm.addControl(controlName, this.formBuilder.control('', Validators.required));\n            }\n          });\n        }\n      });\n    }\n    console.log('Final form controls:', Object.keys(this.workflowForm.controls));\n  }\n  isInputValid() {\n    return this.workflowForm.valid && this.workflowId;\n  }\n  startFakeProgress() {\n    this.progress = 0;\n    this.progressInterval = setInterval(() => {\n      if (this.progress < 90) {\n        this.progress += 5; // Increase slowly\n      }\n    }, 200); // Adjust speed\n  }\n  stopFakeProgress() {\n    clearInterval(this.progressInterval);\n    this.progress = 100;\n    setTimeout(() => {\n      this.isLoading = false;\n    }, 500); // Small delay to let user see 100%\n  }\n  // Handle new chat message from user\n  handleChatMessage(message) {\n    // console.log('message ', message, 'is blank', message.trim() === '');\n    this.isProcessingChat = true;\n    if (message.trim() === '') {\n      if (this.inputFieldOrder.length === 0) {\n        this.chatInterfaceComp.addAiResponse('Executing the workflow...');\n        this.executeWorkflow();\n      }\n      return;\n    }\n    if (this.isExecutionComplete || this.currentInputIndex === this.inputFieldOrder.length) {\n      this.chatInterfaceComp.addAiResponse('Executing the workflow...');\n      this.executeWorkflow();\n      return;\n    }\n    const field = this.inputFieldOrder[this.currentInputIndex];\n    if (this.isImageInput(field)) {\n      // Ignore text input, wait for file input\n      this.chatInterfaceComp.addAiResponse(`Please upload an image file for ${field}`);\n      return;\n    }\n    this.workflowForm.get(field)?.setValue(message);\n    this.currentInputIndex++;\n    if (this.currentInputIndex < this.inputFieldOrder.length) {\n      this.promptForCurrentField();\n    } else {\n      this.chatInterfaceComp.addAiResponse('Thank you for the input! Executing the workflow...');\n      this.executeWorkflow();\n    }\n  }\n  // Save execution logs\n  saveLogs() {\n    console.log('Saving execution logs...');\n    // This would typically save to a service\n  }\n  // Export results\n  exportResults(section) {\n    console.log(`Exporting ${section} data...`);\n    if (section === 'activity') {\n      const data = this.activityLogs.map(log => `[${log.timestamp}] ${log.message}`).join('\\n');\n      this.downloadAsFile(data, 'workflow-activity-logs.txt', 'text/plain');\n    } else {\n      const data = JSON.stringify(this.agentOutputs, null, 2);\n      this.downloadAsFile(data, 'workflow-outputs.json', 'application/json');\n    }\n  }\n  // Helper method to download data as a file\n  downloadAsFile(data, filename, type) {\n    const blob = new Blob([data], {\n      type\n    });\n    const url = URL.createObjectURL(blob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = filename;\n    link.click();\n    URL.revokeObjectURL(url);\n  }\n  // Handle controls for execution\n  handleControlAction(action) {\n    console.log(`Control action: ${action}`);\n    // In a real app, this would control the workflow execution\n    if (action === 'play') {\n      this.isRunning = true;\n    } else if (action === 'pause' || action === 'stop') {\n      this.isRunning = false;\n    }\n  }\n  // Navigate back to workflow listing\n  navigateBack() {\n    this.router.navigate(['/build/workflows']);\n  }\n  // Navigate to edit workflow\n  editWorkflow() {\n    if (this.workflowId) {\n      this.router.navigate(['/build/workflows/edit', this.workflowId]);\n    }\n  }\n  logExecutionStatus(delay = 2000) {\n    setTimeout(() => {\n      if (!this.isExecutionComplete) {\n        console.log(this.constants);\n        console.log(this.constants['labels'].workflowExecProcessing);\n        this.workflowLogs.push({\n          content: this.constants['labels'].workflowExecProcessing,\n          color: '#F9DB24'\n        });\n      }\n    }, delay);\n  }\n  // public parseAnsiString(ansiString: string) {\n  //   const regex = ansiRegex();\n  //   const parts = ansiString.split(regex);\n  //   const matches = [...ansiString.matchAll(regex)];\n  //   parts.forEach((part, index) => {\n  //     if (part.trim() !== '') {\n  //       let colorCode = matches[index - 1][0];\n  //       if (index - 2 >= 0 && matches[index - 2]?.includes('\\u001b[1m')) {\n  //         colorCode = `\\u001b[1m${colorCode}`;\n  //       }\n  //       this.workflowLogs.push({\n  //         content: part,\n  //         color: this.colorMap[colorCode] || 'white',\n  //       });\n  //     }\n  //   });\n  // }\n  getWorkflowLogs(executionId) {\n    console.log('Attempting to connect to WebSocket for executionId:', executionId);\n    try {\n      this.workflowService.workflowLogConnect(executionId).pipe(takeUntil(this.destroy$)).subscribe({\n        next: message => {\n          console.log('WebSocket message received:', message);\n          const {\n            content,\n            color\n          } = message;\n          if (color) {\n            this.workflowLogs.push({\n              content,\n              color\n            });\n          } else if (this.enableStreamingLog === 'all') {\n            // this.parseAnsiString(content);\n            this.workflowLogs.push({\n              content,\n              color: '#6B7280'\n            });\n          }\n        },\n        error: err => {\n          console.error('WebSocket connection error:', err);\n          this.workflowLogs.push({\n            content: 'WebSocket connection failed. Using demo logs instead.',\n            color: 'red'\n          });\n        },\n        complete: () => {\n          this.logExecutionStatus();\n          console.log('WebSocket connection closed');\n        }\n      });\n    } catch (error) {\n      console.error('Failed to establish WebSocket connection:', error);\n      this.workflowLogs.push({\n        content: 'Failed to connect to log streaming service. Using demo logs.',\n        color: 'red'\n      });\n    }\n  }\n  // public parseAnsiString(ansiString: string) {\n  //   const regex = ansiRegex();\n  //   const parts = ansiString.split(regex);\n  //   const matches = [...ansiString.matchAll(regex)];\n  //   parts.forEach((part, index) => {\n  //     if (part.trim() !== '') {\n  //       let colorCode = matches[index-1][0];\n  //       if(index - 2 >= 0 && matches[index-2]?.includes('\\u001b[1m')) {\n  //         colorCode = `\\u001b[1m${colorCode}`;\n  //       }\n  //       this.workflowLogs.push({\n  //         content: part, \n  //         color: this.colorMap[colorCode] || 'white', \n  //       });\n  //     }\n  //   });\n  // }\n  validateJson(output) {\n    this.isJsonValid = false;\n    try {\n      const parsedOutput = JSON.parse(output);\n      this.isJsonValid = true;\n      return parsedOutput;\n    } catch (e) {\n      return null;\n    }\n  }\n  executeWorkflow() {\n    let payload = new FormData();\n    let queryString = '';\n    console.log('Executing workflow with form value:', this.workflowForm.value);\n    console.log('Form controls:', Object.keys(this.workflowForm.controls));\n    console.log('Agent inputs:', this.agents.map(a => ({\n      name: a.name,\n      inputs: a.inputs\n    })));\n    this.status = ExecutionStatus.running;\n    if (this.selectedFiles.length) {\n      this.selectedFiles.forEach(file => {\n        payload.append('files', file);\n      });\n      payload.append('workflowId', this.workflowId);\n      payload.append('userInputs', JSON.stringify(this.workflowForm.value));\n      payload.append('user', this.tokenStorage.getDaUsername());\n      payload.append('executionId', this.executionId);\n      queryString = '/files';\n    } else {\n      payload = {\n        pipeLineId: this.workflowId,\n        userInputs: this.workflowForm.value,\n        executionId: this.executionId,\n        user: this.tokenStorage.getDaUsername()\n      };\n    }\n    console.log('Final payload:', payload);\n    this.getWorkflowLogs(this.executionId);\n    this.startFakeProgress();\n    this.workflowService.executeWorkflow(payload, queryString).pipe(takeUntil(this.destroy$)).subscribe({\n      next: res => {\n        this.isProcessingChat = false;\n        this.isRunning = false;\n        this.chatInterfaceComp.addAiResponse(res?.message || \"Workflow execution completed successfully!\");\n        console.log('Workflow execution response:', res);\n        // Check if we have workflow response and output\n        if (res?.workflowResponse?.pipeline) {\n          const pipeline = res.workflowResponse.pipeline;\n          console.log('Pipeline data:', pipeline);\n          this.isExecutionComplete = true;\n          this.workflowLogs.push({\n            content: this.constants['labels'].workflowExecComplete,\n            color: '#0F8251'\n          });\n          this.errorMsg = false;\n          // Set the output message\n          this.resMessage = pipeline.output || 'Workflow completed successfully';\n          console.log('Setting resMessage to:', this.resMessage);\n          // Always process task outputs if they exist (remove the length check)\n          if (pipeline.tasksOutputs && Array.isArray(pipeline.tasksOutputs)) {\n            console.log('Processing tasksOutputs:', pipeline.tasksOutputs);\n            this.agentOutputs = pipeline.tasksOutputs.map(task => {\n              return {\n                id: task?.id || Math.random().toString(36).substring(2, 11),\n                title: task?.title || 'Task Output',\n                content: task?.raw || task?.content || '',\n                agentName: task?.agentName || 'Agent',\n                timestamp: task?.timestamp || new Date().toISOString(),\n                type: task?.type || 'text',\n                description: task?.description || '',\n                expected_output: task?.expected_output || '',\n                summary: task?.summary || '',\n                raw: task?.raw || ''\n              };\n            });\n            console.log('Agent outputs for display:', this.agentOutputs);\n            console.log('Agent outputs length:', this.agentOutputs.length);\n          } else {\n            console.log('No tasksOutputs found, checking for main output');\n            if (pipeline.output) {\n              // If no task outputs but we have main output, create a single output item\n              console.log('Creating output from main pipeline output');\n              this.agentOutputs = [{\n                id: 'main-output',\n                title: 'Workflow Output',\n                content: pipeline.output,\n                agentName: 'Workflow',\n                timestamp: new Date().toISOString(),\n                type: 'text',\n                description: 'Main workflow output',\n                expected_output: 'Workflow execution result',\n                summary: 'Workflow completed successfully',\n                raw: pipeline.output\n              }];\n              console.log('Created fallback agent output:', this.agentOutputs);\n            }\n          }\n          // Process task messages - this should always run if tasksOutputs exists\n          if (pipeline.tasksOutputs && Array.isArray(pipeline.tasksOutputs)) {\n            this.taskMessage = pipeline.tasksOutputs.map(task => {\n              return {\n                description: task.description,\n                summary: task.summary,\n                raw: task.raw,\n                expected_output: task.expected_output\n              };\n            });\n            console.log('Task messages processed:', this.taskMessage);\n            console.log('Task messages length:', this.taskMessage.length);\n          } else {\n            console.log('No tasksOutputs found or not an array:', pipeline.tasksOutputs);\n          }\n          // if(\"file_download_url\" in res?.pipeline){\n          //   this.isFileWriter = true;\n          //   this.fileDownloadLink = res?.pipeline?.file_download_url;\n          //   if(!this.fileDownloadLink){\n          //     this.fileDownloadUrlError = [];\n          //     this.fileDownloadUrlError.push(\"Output file is not generated yet!\")\n          //   }\n          // }\n          // this.isAccordian = true\n        }\n        this.validateJson(this.resMessage);\n        this.status = ExecutionStatus.completed;\n        this.stopFakeProgress();\n        this.selectedFiles = [];\n      },\n      error: error => {\n        this.isExecutionComplete = true;\n        this.isProcessingChat = false;\n        this.errorMsg = true;\n        this.resMessage = error?.error?.detail;\n        this.workflowService.workflowLogDisconnect();\n        this.workflowLogs.push({\n          content: this.constants['labels'].workflowLogFailed,\n          color: 'red'\n        });\n        this.chatInterfaceComp.addAiResponse('Something went wrong, Workflow execution has failed.');\n        this.selectedFiles = [];\n        this.stopFakeProgress();\n        console.log('error is', error.message);\n      }\n    });\n  }\n  // public asyncExecutePipeline() {\n  //   const payload: FormData = new FormData();\n  //   if (this.selectedFiles?.length) {\n  //     for (const element of this.selectedFiles) {\n  //       payload.append('files', element)\n  //     }\n  //   }\n  //   payload.append('pipeLineId', String(this.workflowId));\n  //   payload.append('userInputs', JSON.stringify(this.workflowForm.value));\n  //   payload.append('user', this.tokenStorage.getDaUsername() || '');\n  //   payload.append('executionId', this.executionId);\n  //   this.workflowService.asyncExecutePipeline(payload).pipe().subscribe({\n  //     next: (res: any) => {\n  //       if(res) {\n  //         // res handling\n  //         console.log(res);\n  //       }\n  //     },\n  //     error: e => {\n  //       // error handling\n  //       console.log(e);\n  //     }\n  //   })\n  // }\n  handleAttachment() {\n    console.log('handleAttachment');\n  }\n  onAttachmentsSelected(files) {\n    if (this.currentInputIndex === this.inputFieldOrder.length || this.inputFieldOrder.length === 0) {\n      this.selectedFiles = files;\n      return;\n    }\n    const field = this.inputFieldOrder[this.currentInputIndex];\n    if (this.isImageInput(field)) {\n      if (files && files.length > 0) {\n        this.onImageSelected(files[0]);\n      }\n    } else {\n      this.selectedFiles = files;\n    }\n  }\n  startInputCollection() {\n    this.inputFieldOrder = Object.keys(this.workflowForm.controls);\n    this.currentInputIndex = 0;\n    if (this.inputFieldOrder.length > 0) {\n      this.promptForCurrentField();\n    } else {\n      this.disableChat = true;\n      this.chatInterfaceComp.addAiResponse('No input is required for this workflow. Click on send button to execute the workflow.');\n    }\n  }\n  promptForCurrentField() {\n    const field = this.inputFieldOrder[this.currentInputIndex];\n    if (this.isImageInput(field)) {\n      this.fileType = '.jpeg,.png,.jpg,.svg';\n      this.chatInterfaceComp.addAiResponse(`Please upload an image for ${field}`);\n      // UI should now show a file input for the user\n    } else {\n      this.fileType = '.zip'; // or whatever default you want for non-image\n      this.chatInterfaceComp.addAiResponse(`Please enter the value of ${field}`);\n    }\n  }\n  onImageSelected(file) {\n    const field = this.inputFieldOrder[this.currentInputIndex];\n    if (!this.isImageInput(field)) return;\n    const reader = new FileReader();\n    reader.onload = () => {\n      const base64String = reader.result;\n      this.workflowForm.get(field)?.setValue(base64String);\n      this.currentInputIndex++;\n      if (this.currentInputIndex < this.inputFieldOrder.length) {\n        this.promptForCurrentField();\n      } else {\n        this.chatInterfaceComp.addAiResponse('Thank you! Running the workflow...');\n        this.executeWorkflow();\n      }\n    };\n    reader.readAsDataURL(file);\n  }\n  // Event handlers for workflow playground\n  onPlaygroundBackClicked() {\n    this.navigateBack();\n  }\n  onPlaygroundCollapseToggled(isCollapsed) {\n    this.isPlaygroundCollapsed = isCollapsed;\n  }\n  onAgentInputChanged(event) {\n    // Find the agent and update the input value\n    const agent = this.agents.find(a => a.id === event.agentId);\n    if (agent && agent.inputs && agent.inputs[event.inputIndex]) {\n      agent.inputs[event.inputIndex].value = event.value;\n      // Update the form control if it exists\n      const placeholder = agent.inputs[event.inputIndex].placeholder;\n      if (this.workflowForm.get(placeholder)) {\n        this.workflowForm.get(placeholder)?.setValue(event.value);\n      }\n    }\n  }\n  onAgentFileSelected(event) {\n    // Find the agent and update the files\n    const agent = this.agents.find(a => a.id === event.agentId);\n    if (agent && agent.inputs && agent.inputs[event.inputIndex]) {\n      agent.inputs[event.inputIndex].files = event.files;\n      // Add files to selectedFiles array for execution\n      this.selectedFiles = [...this.selectedFiles, ...event.files];\n    }\n  }\n  onMessageSent(event) {\n    console.log('Message sent from agent:', event);\n    // Find the agent and update the input value\n    const agent = this.agents.find(a => a.id === event.agentId);\n    if (agent && agent.inputs && agent.inputs[event.inputIndex]) {\n      agent.inputs[event.inputIndex].value = event.value;\n      // Update files if provided\n      if (event.files) {\n        agent.inputs[event.inputIndex].files = event.files;\n        this.selectedFiles = [...this.selectedFiles, ...event.files];\n      }\n      // Update the form control if it exists\n      const placeholder = agent.inputs[event.inputIndex].placeholder;\n      console.log('Updating form field:', placeholder, 'with value:', event.value);\n      console.log('Form controls available:', Object.keys(this.workflowForm.controls));\n      console.log('Current form value before update:', this.workflowForm.value);\n      if (this.workflowForm.get(placeholder)) {\n        this.workflowForm.get(placeholder)?.setValue(event.value);\n        console.log('Form value after update:', this.workflowForm.value);\n      } else {\n        console.warn('Form control not found for placeholder:', placeholder);\n        // Try to add the control dynamically\n        this.workflowForm.addControl(placeholder, this.formBuilder.control(event.value));\n        console.log('Added new form control. Form value:', this.workflowForm.value);\n      }\n      // Log the input submission\n      this.workflowLogs.push({\n        content: `Agent \"${agent.name}\" - Input \"${agent.inputs[event.inputIndex].inputName}\": ${event.value}`,\n        color: '#10B981'\n      });\n    }\n  }\n  /**\n   * Get the logs to display based on the current view state\n   */\n  get displayedLogs() {\n    if (this.showAllLogs) {\n      return this.workflowLogs;\n    }\n    return this.workflowLogs.slice(0, this.displayedLogsCount);\n  }\n  /**\n   * Toggle between showing limited logs and all logs\n   */\n  toggleShowAllLogs() {\n    this.showAllLogs = !this.showAllLogs;\n  }\n  /**\n   * Check if there are more logs to show\n   */\n  get hasMoreLogs() {\n    return this.workflowLogs.length > this.displayedLogsCount && !this.showAllLogs;\n  }\n  /**\n   * Initialize demo logs for testing the log display functionality\n   */\n  initializeDemoLogs() {\n    // Only add demo logs if there are no existing logs\n    if (this.workflowLogs.length === 0) {\n      const demoLogs = [{\n        content: 'Workflow execution started',\n        color: '#0F8251'\n      }, {\n        content: 'Loading pipeline configuration...',\n        color: '#6B7280'\n      }, {\n        content: 'Initializing Test Case Normalizer agent',\n        color: '#2563EB'\n      }, {\n        content: 'Processing input parameters',\n        color: '#6B7280'\n      }, {\n        content: 'Agent 1 is currently working',\n        color: '#F9DB24'\n      }, {\n        content: 'Analyzing test case structure...',\n        color: '#6B7280'\n      }, {\n        content: 'Normalizing test case format',\n        color: '#6B7280'\n      }, {\n        content: 'Validating output format',\n        color: '#6B7280'\n      }, {\n        content: 'Test Case Normalizer completed successfully',\n        color: '#0F8251'\n      }, {\n        content: 'Initializing ELT_OAMA Frameworkagent',\n        color: '#2563EB'\n      }, {\n        content: 'Loading framework configuration',\n        color: '#6B7280'\n      }, {\n        content: 'Processing framework parameters',\n        color: '#6B7280'\n      }, {\n        content: 'Agent 2 is currently working',\n        color: '#F9DB24'\n      }, {\n        content: 'Executing framework analysis...',\n        color: '#6B7280'\n      }, {\n        content: 'Generating framework recommendations',\n        color: '#6B7280'\n      }, {\n        content: 'ELT_OAMA Framework analysis completed',\n        color: '#0F8251'\n      }, {\n        content: 'Initializing ELT_QE_HPX_Automated_Script agent',\n        color: '#2563EB'\n      }, {\n        content: 'Loading automation script templates',\n        color: '#6B7280'\n      }, {\n        content: 'Agent 3 is currently working',\n        color: '#F9DB24'\n      }, {\n        content: 'Generating automated test scripts...',\n        color: '#6B7280'\n      }, {\n        content: 'Validating script syntax and structure',\n        color: '#6B7280'\n      }, {\n        content: 'Script generation completed successfully',\n        color: '#0F8251'\n      }, {\n        content: 'All pipeline agents completed successfully',\n        color: '#0F8251'\n      }, {\n        content: 'Workflow execution completed',\n        color: '#0F8251'\n      }];\n      this.workflowLogs = demoLogs;\n    }\n  }\n};\n__decorate([ViewChild(ChatInterfaceComponent, {\n  static: false\n})], WorkflowExecutionComponent.prototype, \"chatInterfaceComp\", void 0);\nWorkflowExecutionComponent = __decorate([Component({\n  selector: 'app-workflow-execution',\n  standalone: true,\n  imports: [CommonModule, FormsModule, ChatInterfaceComponent, AgentActivityComponent, AgentOutputComponent, TabsComponent, ButtonComponent, IconComponent, WorkflowPlaygroundComponent],\n  templateUrl: './workflow-execution.component.html',\n  styleUrls: ['./workflow-execution.component.scss']\n})], WorkflowExecutionComponent);\nexport { WorkflowExecutionComponent };", "map": {"version": 3, "names": ["Component", "ViewChild", "CommonModule", "Subject", "takeUntil", "FormsModule", "Validators", "ChatInterfaceComponent", "AgentActivityComponent", "ButtonComponent", "IconComponent", "TabsComponent", "environment", "workflowConstants", "ExecutionStatus", "WorkflowPlaygroundComponent", "WorkflowExecutionComponent", "route", "router", "workflowService", "inputExtractorService", "tokenStorage", "loaderService", "formBuilder", "navigationTabs", "id", "label", "workflowId", "workflowName", "constants", "chatInterfaceComp", "activityLogs", "activityProgress", "executionDetails", "isRunning", "status", "notStarted", "chatMessages", "isProcessingChat", "inputText", "agentOutputs", "workflowForm", "fileType", "executionStartTime", "executionCompleted", "executionId", "workflowLogs", "displayedLogsCount", "showAllLogs", "enableStreamingLog", "enableLogStreaming", "isExecutionComplete", "progressInterval", "destroy$", "selectedTab", "demoTabs", "disabled", "errorMsg", "resMessage", "taskMessage", "isJsonValid", "disable<PERSON>hat", "selectedFiles", "workflowAgents", "userInputList", "progress", "isLoading", "loaderColor", "inputFieldOrder", "currentInputIndex", "activeTabId", "agents", "isPlaygroundCollapsed", "pipelineAgents", "constructor", "ngOnInit", "disable<PERSON><PERSON><PERSON>", "crypto", "randomUUID", "paramMap", "pipe", "subscribe", "params", "get", "loadWorkflow", "navigate", "ngOnDestroy", "next", "complete", "<PERSON><PERSON><PERSON><PERSON>", "onTabChange", "event", "console", "log", "from", "text", "group", "getWorkflowById", "res", "extractInputField", "length", "name", "initializeForm", "startInputCollection", "initializeDemoLogs", "error", "err", "addAiResponse", "getWorkflowDetails", "getOneWorkflow", "pipeLineAgents", "convertToAgentData", "warn", "e", "PLACEHOLDER_PATTERNS", "placeholderM<PERSON>", "for<PERSON>ach", "pipelineAgent", "<PERSON><PERSON><PERSON>", "agent", "agentDescription", "task", "description", "matches", "matchAll", "match", "placeholder", "placeholderInput", "Set", "inputs", "add", "Object", "entries", "map", "slice", "join", "at", "input", "isImageInput", "variableName", "trim", "startsWith", "addControl", "control", "required", "controlName", "keys", "controls", "isInputValid", "valid", "startFakeProgress", "setInterval", "stopFakeProgress", "clearInterval", "setTimeout", "handleChatMessage", "message", "executeWorkflow", "field", "setValue", "promptForCurrentField", "saveLogs", "exportResults", "section", "data", "timestamp", "downloadAsFile", "JSON", "stringify", "filename", "type", "blob", "Blob", "url", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "click", "revokeObjectURL", "handleControlAction", "action", "navigateBack", "editWorkflow", "logExecutionStatus", "delay", "workflowExecProcessing", "push", "content", "color", "getWorkflowLogs", "workflowLogConnect", "validate<PERSON><PERSON>", "output", "parsedOutput", "parse", "payload", "FormData", "queryString", "value", "a", "running", "file", "append", "getDaUsername", "pipeLineId", "userInputs", "user", "workflowResponse", "pipeline", "workflowExecComplete", "tasksOutputs", "Array", "isArray", "Math", "random", "toString", "substring", "title", "raw", "Date", "toISOString", "expected_output", "summary", "completed", "detail", "workflowLogDisconnect", "workflowLogFailed", "handleAttachment", "onAttachmentsSelected", "files", "onImageSelected", "reader", "FileReader", "onload", "base64String", "result", "readAsDataURL", "onPlaygroundBackClicked", "onPlaygroundCollapseToggled", "isCollapsed", "onAgentInputChanged", "find", "agentId", "inputIndex", "onAgentFileSelected", "onMessageSent", "inputName", "displayedLogs", "toggleShowAllLogs", "hasMoreLogs", "demoLogs", "__decorate", "static", "selector", "standalone", "imports", "AgentOutputComponent", "templateUrl", "styleUrls"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\pages\\workflows\\workflow-execution\\workflow-execution.component.ts"], "sourcesContent": ["import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { CommonModule } from '@angular/common';\r\nimport { Subject, takeUntil } from 'rxjs';\r\nimport { FormBuilder, FormGroup, FormsModule, Validators } from '@angular/forms';\r\n\r\n// Import child components\r\nimport { ChatInterfaceComponent } from '@shared/components/chat-interface/chat-interface.component';\r\nimport { ChatMessage } from '@shared/components/chat-window/chat-window.component';\r\nimport {\r\n  AgentActivityComponent,\r\n} from './components/agent-activity/agent-activity.component';\r\n// Removed AgentOutputComponent - now using inline output display\r\nimport {\r\n  ButtonComponent,\r\n  IconComponent,\r\n  TabItem,\r\n  TabsComponent,\r\n} from '@ava/play-comp-library';\r\nimport { WorkflowService } from '@shared/services/workflow.service';\r\nimport { WorkflowInputExtractorService } from '@shared/services/workflow-input-extractor.service';\r\nimport { environment } from '@shared/environments/environment';\r\nimport workflowConstants from './../constants/workflows.json';\r\nimport { TokenStorageService, LoaderService } from '@shared/index';\r\nimport { AvaTab } from '@shared/models/tab.model';\r\nimport { ExecutionStatus, ActivityLog } from '@shared/models/execution.model';\r\nimport { AgentActivityExecutionDetails } from './components/agent-activity/agent-activity.component';\r\nimport { WorkflowPlaygroundComponent, AgentData } from './components/workflow-playground/workflow-playground.component';\r\n\r\n\r\n@Component({\r\n  selector: 'app-workflow-execution',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    ChatInterfaceComponent,\r\n    AgentActivityComponent,\r\n    AgentOutputComponent,\r\n    TabsComponent,\r\n    ButtonComponent,\r\n    IconComponent,\r\n    WorkflowPlaygroundComponent,\r\n  ],\r\n  templateUrl: './workflow-execution.component.html',\r\n  styleUrls: ['./workflow-execution.component.scss'],\r\n})\r\nexport class WorkflowExecutionComponent implements OnInit, OnDestroy {\r\n  navigationTabs: TabItem[] = [\r\n    { id: 'nav-home', label: 'Execution' },\r\n    { id: 'nav-products', label: 'Output' },\r\n    { id: 'nav-services', label: 'Configuration' },\r\n  ];\r\n  // Workflow details\r\n  workflowId: string | null = null;\r\n  workflowName: string = 'Workflow';\r\n\r\n  constants = workflowConstants as Record<string, any>;\r\n\r\n  @ViewChild(ChatInterfaceComponent, { static: false })\r\n  chatInterfaceComp!: ChatInterfaceComponent;\r\n\r\n  // Activity logs\r\n  activityLogs: ActivityLog[] = [];\r\n  activityProgress: number = 0;\r\n  executionDetails?: AgentActivityExecutionDetails;\r\n  isRunning: boolean = false;\r\n  status: ExecutionStatus = ExecutionStatus.notStarted;\r\n\r\n  // Chat messages\r\n  chatMessages: ChatMessage[] = [];\r\n  isProcessingChat: boolean = false;\r\n  inputText = '';\r\n\r\n  // Agent outputs\r\n  agentOutputs: OutputItem[] = [];\r\n  public workflowForm!: FormGroup;\r\n  public fileType : string = '.zip';\r\n\r\n  // Execution state\r\n  executionStartTime: Date | null = null;\r\n  executionCompleted: boolean = false;\r\n  executionId!: string;\r\n\r\n  public workflowLogs: any[] = [];\r\n  public displayedLogsCount: number = 10;\r\n  public showAllLogs: boolean = false;\r\n  enableStreamingLog = environment.enableLogStreaming || 'all';\r\n\r\n  public isExecutionComplete: boolean = false;\r\n  progressInterval: any;\r\n\r\n  // Component lifecycle\r\n  private destroy$ = new Subject<void>();\r\n  selectedTab: string = 'Agent Activity';\r\n  demoTabs: AvaTab[] = [\r\n    { id: 'activity', label: 'Agent Activity' },\r\n    { id: 'agents', label: 'Agent Output' },\r\n    { id: 'preview', label: 'Preview', disabled: true },\r\n  ];\r\n  errorMsg = false;\r\n  resMessage: any;\r\n  taskMessage = [];\r\n  isJsonValid = false;\r\n  disableChat : boolean = false;\r\n  selectedFiles: File[] = [];\r\n  workflowAgents: any[] = [];\r\n  userInputList: any[] = [];\r\n  progress = 0;\r\n  isLoading = false;\r\n  loaderColor: string = '';\r\n\r\n  inputFieldOrder: string[] = [];\r\n  currentInputIndex: number = 0;\r\n  activeTabId: string = 'nav-home';\r\n  \r\n\r\n  // New properties for workflow playground\r\n  agents: AgentData[] = [];\r\n  isPlaygroundCollapsed: boolean = false;\r\n  pipelineAgents: any[] = [];\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private workflowService: WorkflowService,\r\n    private inputExtractorService: WorkflowInputExtractorService,\r\n    private tokenStorage: TokenStorageService,\r\n    private loaderService: LoaderService,\r\n    private formBuilder: FormBuilder,\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.loaderService.disableLoader();\r\n    this.selectedTab = 'Agent Activity';\r\n    this.executionId = crypto.randomUUID();\r\n    // Get workflow ID from route params\r\n    this.route.paramMap.pipe(takeUntil(this.destroy$)).subscribe((params) => {\r\n      this.workflowId = params.get('id');\r\n      if (this.workflowId) {\r\n        this.loadWorkflow(this.workflowId);\r\n      } else {\r\n        // No workflow ID, redirect back to workflows page\r\n        this.router.navigate(['/build/workflows']);\r\n      }\r\n    });\r\n    // this.executeWorkflow()\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.destroy$.next();\r\n    this.destroy$.complete();\r\n    this.loaderService.enableLoader();\r\n  }\r\n  onTabChange(event: { id: string, label: string }) {\r\n    this.selectedTab = event.label;\r\n    this.activeTabId = event.id;\r\n    console.log('Tab changed:', event);\r\n  }\r\n\r\n  // Load workflow data\r\n  loadWorkflow(id: string): void {\r\n    // In a real app, this would fetch the workflow from a service\r\n    console.log(`Loading workflow with ID: ${id}`);\r\n    this.chatMessages = [\r\n      {\r\n        from: 'ai',\r\n        text: 'I am your workflow assistant. I will help you in executing this workflow.',\r\n      } as ChatMessage,\r\n    ]\r\n    this.workflowForm = this.formBuilder.group({});\r\n    \r\n    // Load workflow data for chat interface\r\n    this.workflowService.getWorkflowById(id).subscribe({\r\n        next: (res) => {\r\n        this.workflowAgents = res.workflowAgents;\r\n        this.userInputList = this.extractInputField(this.workflowAgents);\r\n        if(this.userInputList.length === 0){\r\n          this.disableChat = true;\r\n        }\r\n        this.workflowName = res.name;\r\n        this.initializeForm();\r\n        this.startInputCollection();\r\n        this.initializeDemoLogs(); // Initialize demo logs for testing\r\n      },\r\n        error: (err) => {\r\n          this.disableChat = true;\r\n          this.chatInterfaceComp.addAiResponse('No input is required for this workflow. Click on send button to execute the workflow.');\r\n          console.log(err);\r\n        }\r\n    });\r\n\r\n    // Also load workflow details for the new playground component\r\n    this.getWorkflowDetails(id);\r\n\r\n  }\r\n\r\n  // New method to get workflow details using workflow service\r\n  public getWorkflowDetails(id: string) {\r\n    this.workflowService.getOneWorkflow(id).subscribe({\r\n      next: (res: any) => {\r\n        if (res?.pipeLineAgents?.length) {\r\n          this.pipelineAgents = res.pipeLineAgents;\r\n          this.workflowName = res.name;\r\n\r\n          // Convert pipeline agents to AgentData format\r\n          try {\r\n            this.agents = this.inputExtractorService.convertToAgentData(this.pipelineAgents);\r\n            console.log('Converted agents:', this.agents);\r\n          } catch (error) {\r\n            console.error('Error converting pipeline agents to AgentData:', error);\r\n            this.agents = [];\r\n          }\r\n\r\n          // Extract input fields for form initialization\r\n          this.userInputList = this.extractInputField(res.pipeLineAgents);\r\n          this.initializeForm();\r\n        } else {\r\n          console.warn('No pipeline agents found in workflow response');\r\n          this.agents = [];\r\n        }\r\n      },\r\n      error: (e: any) => console.error(e)\r\n    });\r\n  }\r\n\r\n  public extractInputField(pipeLineAgents: any) {\r\n    const PLACEHOLDER_PATTERNS =  /(%\\d+\\$s)|\\{\\{([a-zA-Z0-9-_]+)\\}\\}/g;\r\n    const placeholderMap: { [key: string]: { agents: Set<string>; inputs: Set<string> } } = {};\r\n\r\n    pipeLineAgents.forEach((pipelineAgent: any) => {\r\n      const agentName = pipelineAgent?.agent?.name;\r\n      const agentDescription = pipelineAgent?.agent?.task?.description;\r\n      const matches = agentDescription?.matchAll(PLACEHOLDER_PATTERNS) || [];\r\n\r\n      for (const match of matches) {\r\n        const placeholder = match[1] || match[2];\r\n        const placeholderInput = match[0];\r\n        if (!placeholderMap[placeholder]) {\r\n          placeholderMap[placeholder] = { agents: new Set(), inputs: new Set() };;\r\n        }\r\n        if (agentName) {\r\n          placeholderMap[placeholder].agents.add(agentName);\r\n        }\r\n        placeholderMap[placeholder].inputs.add(placeholderInput);\r\n      }\r\n    })\r\n\r\n    return Object.entries(placeholderMap).map(([placeholder, { agents, inputs }]) => ({\r\n      name: [...agents].length > 2\r\n        ? `${[...agents].slice(0, -1).join(\", \")} and ${[...agents].at(-1)}`\r\n        : [...agents].join(\" and \"),\r\n      placeholder,\r\n      input: [...inputs][0],\r\n    }));\r\n  }\r\n\r\n  public isImageInput(input: string): boolean {\r\n    const match = input.match(/{{(.*?)}}/);\r\n    if (match && match[1]) {\r\n      const variableName = match[1].trim();\r\n      return variableName.startsWith('image') || variableName.startsWith('Image');\r\n    }\r\n    return false;\r\n  }\r\n\r\n  public initializeForm() {\r\n    console.log('Initializing form with userInputList:', this.userInputList);\r\n    this.userInputList.forEach((label: any) => {\r\n      console.log('Adding form control for:', label.input);\r\n      this.workflowForm.addControl(label.input, this.formBuilder.control('', Validators.required));\r\n    });\r\n\r\n    // Also add form controls for agent inputs to ensure compatibility\r\n    if (this.agents) {\r\n      this.agents.forEach(agent => {\r\n        if (agent.inputs) {\r\n          agent.inputs.forEach(input => {\r\n            const controlName = input.placeholder;\r\n            if (!this.workflowForm.get(controlName)) {\r\n              console.log('Adding additional form control for agent input:', controlName);\r\n              this.workflowForm.addControl(controlName, this.formBuilder.control('', Validators.required));\r\n            }\r\n          });\r\n        }\r\n      });\r\n    }\r\n\r\n    console.log('Final form controls:', Object.keys(this.workflowForm.controls));\r\n  }\r\n\r\n  public isInputValid() {\r\n    return this.workflowForm.valid && this.workflowId;\r\n  }\r\n\r\n  startFakeProgress() {\r\n    this.progress = 0;\r\n    this.progressInterval = setInterval(() => {\r\n      if (this.progress < 90) {\r\n        this.progress += 5; // Increase slowly\r\n      }\r\n    }, 200); // Adjust speed\r\n  }\r\n\r\n  stopFakeProgress() {\r\n    clearInterval(this.progressInterval);\r\n    this.progress = 100;\r\n\r\n    setTimeout(() => {\r\n      this.isLoading = false;\r\n    }, 500); // Small delay to let user see 100%\r\n  }\r\n\r\n  // Handle new chat message from user\r\n  handleChatMessage(message: string): void {\r\n    // console.log('message ', message, 'is blank', message.trim() === '');\r\n    this.isProcessingChat = true;\r\n    if(message.trim() === ''){\r\n      if(this.inputFieldOrder.length === 0){\r\n        this.chatInterfaceComp.addAiResponse('Executing the workflow...');\r\n        this.executeWorkflow();\r\n      }\r\n      return;\r\n    }\r\n\r\n    if(this.isExecutionComplete || this.currentInputIndex===this.inputFieldOrder.length){\r\n        this.chatInterfaceComp.addAiResponse('Executing the workflow...');\r\n        this.executeWorkflow();\r\n      return;\r\n    }\r\n\r\n    const field = this.inputFieldOrder[this.currentInputIndex];\r\n    if (this.isImageInput(field)) {\r\n      // Ignore text input, wait for file input\r\n      this.chatInterfaceComp.addAiResponse(`Please upload an image file for ${field}`);\r\n      return;\r\n    }\r\n\r\n    this.workflowForm.get(field)?.setValue(message);\r\n    this.currentInputIndex++;\r\n\r\n    if (this.currentInputIndex < this.inputFieldOrder.length) {\r\n      this.promptForCurrentField();\r\n    } else {\r\n      this.chatInterfaceComp.addAiResponse('Thank you for the input! Executing the workflow...');\r\n      this.executeWorkflow();\r\n    }\r\n  }\r\n\r\n  // Save execution logs\r\n  saveLogs(): void {\r\n    console.log('Saving execution logs...');\r\n    // This would typically save to a service\r\n  }\r\n\r\n  // Export results\r\n  exportResults(section: 'activity' | 'output'): void {\r\n    console.log(`Exporting ${section} data...`);\r\n\r\n    if (section === 'activity') {\r\n      const data = this.activityLogs\r\n        .map((log) => `[${log.timestamp}] ${log.message}`)\r\n        .join('\\n');\r\n      this.downloadAsFile(data, 'workflow-activity-logs.txt', 'text/plain');\r\n    } else {\r\n      const data = JSON.stringify(this.agentOutputs, null, 2);\r\n      this.downloadAsFile(data, 'workflow-outputs.json', 'application/json');\r\n    }\r\n  }\r\n\r\n  // Helper method to download data as a file\r\n  private downloadAsFile(data: string, filename: string, type: string): void {\r\n    const blob = new Blob([data], { type });\r\n    const url = URL.createObjectURL(blob);\r\n    const link = document.createElement('a');\r\n    link.href = url;\r\n    link.download = filename;\r\n    link.click();\r\n    URL.revokeObjectURL(url);\r\n  }\r\n\r\n  // Handle controls for execution\r\n  handleControlAction(action: 'play' | 'pause' | 'stop'): void {\r\n    console.log(`Control action: ${action}`);\r\n    // In a real app, this would control the workflow execution\r\n\r\n    if (action === 'play') {\r\n      this.isRunning = true;\r\n    } else if (action === 'pause' || action === 'stop') {\r\n      this.isRunning = false;\r\n    }\r\n  }\r\n\r\n  // Navigate back to workflow listing\r\n  navigateBack(): void {\r\n    this.router.navigate(['/build/workflows']);\r\n  }\r\n\r\n  // Navigate to edit workflow\r\n  editWorkflow(): void {\r\n    if (this.workflowId) {\r\n      this.router.navigate(['/build/workflows/edit', this.workflowId]);\r\n    }\r\n  }\r\n\r\n  public logExecutionStatus(delay: number = 2000) {\r\n    setTimeout(() => {\r\n      if (!this.isExecutionComplete) {\r\n        console.log(this.constants);\r\n        console.log(this.constants['labels'].workflowExecProcessing);\r\n        this.workflowLogs.push({\r\n          content: this.constants['labels'].workflowExecProcessing,\r\n          color: '#F9DB24',\r\n        });\r\n      }\r\n    }, delay);\r\n  }\r\n\r\n  // public parseAnsiString(ansiString: string) {\r\n  //   const regex = ansiRegex();\r\n  //   const parts = ansiString.split(regex);\r\n  //   const matches = [...ansiString.matchAll(regex)];\r\n  //   parts.forEach((part, index) => {\r\n  //     if (part.trim() !== '') {\r\n  //       let colorCode = matches[index - 1][0];\r\n  //       if (index - 2 >= 0 && matches[index - 2]?.includes('\\u001b[1m')) {\r\n  //         colorCode = `\\u001b[1m${colorCode}`;\r\n  //       }\r\n  //       this.workflowLogs.push({\r\n  //         content: part,\r\n  //         color: this.colorMap[colorCode] || 'white',\r\n  //       });\r\n  //     }\r\n  //   });\r\n  // }\r\n\r\n  public getWorkflowLogs(executionId: string) {\r\n    console.log('Attempting to connect to WebSocket for executionId:', executionId);\r\n\r\n    try {\r\n      this.workflowService\r\n        .workflowLogConnect(executionId)\r\n        .pipe(takeUntil(this.destroy$))\r\n        .subscribe({\r\n          next: (message) => {\r\n            console.log('WebSocket message received:', message);\r\n            const { content, color } = message;\r\n            if (color) {\r\n              this.workflowLogs.push({ content, color });\r\n            } else if (this.enableStreamingLog === 'all') {\r\n              // this.parseAnsiString(content);\r\n              this.workflowLogs.push({ content, color: '#6B7280' });\r\n            }\r\n          },\r\n          error: (err) => {\r\n            console.error('WebSocket connection error:', err);\r\n            this.workflowLogs.push({\r\n              content: 'WebSocket connection failed. Using demo logs instead.',\r\n              color: 'red',\r\n            });\r\n          },\r\n          complete: () => {\r\n            this.logExecutionStatus();\r\n            console.log('WebSocket connection closed');\r\n          },\r\n        });\r\n    } catch (error) {\r\n      console.error('Failed to establish WebSocket connection:', error);\r\n      this.workflowLogs.push({\r\n        content: 'Failed to connect to log streaming service. Using demo logs.',\r\n        color: 'red',\r\n      });\r\n    }\r\n  }\r\n\r\n  // public parseAnsiString(ansiString: string) {\r\n  //   const regex = ansiRegex();\r\n  //   const parts = ansiString.split(regex);\r\n  //   const matches = [...ansiString.matchAll(regex)];\r\n  //   parts.forEach((part, index) => {\r\n  //     if (part.trim() !== '') {\r\n  //       let colorCode = matches[index-1][0];\r\n  //       if(index - 2 >= 0 && matches[index-2]?.includes('\\u001b[1m')) {\r\n  //         colorCode = `\\u001b[1m${colorCode}`;\r\n  //       }\r\n  //       this.workflowLogs.push({\r\n  //         content: part, \r\n  //         color: this.colorMap[colorCode] || 'white', \r\n  //       });\r\n  //     }\r\n  //   });\r\n  // }\r\n\r\n  public validateJson(output: string): any | null {\r\n    this.isJsonValid = false;\r\n    try {\r\n      const parsedOutput = JSON.parse(output);\r\n      this.isJsonValid = true;\r\n      return parsedOutput;\r\n    } catch (e) {\r\n      return null;\r\n    }\r\n  }\r\n\r\n  public executeWorkflow() {\r\n    let payload: FormData | Record<string, any> = new FormData();\r\n    let queryString = '';\r\n\r\n    console.log('Executing workflow with form value:', this.workflowForm.value);\r\n    console.log('Form controls:', Object.keys(this.workflowForm.controls));\r\n    console.log('Agent inputs:', this.agents.map(a => ({ name: a.name, inputs: a.inputs })));\r\n\r\n    this.status = ExecutionStatus.running;\r\n    if (this.selectedFiles.length) {\r\n      this.selectedFiles.forEach((file) => {\r\n        payload.append('files', file);\r\n      });\r\n      payload.append('workflowId', this.workflowId);\r\n      payload.append('userInputs', JSON.stringify(this.workflowForm.value));\r\n      payload.append('user', this.tokenStorage.getDaUsername());\r\n      payload.append('executionId', this.executionId);\r\n      queryString = '/files';\r\n    } else {\r\n      payload = {\r\n        pipeLineId: this.workflowId,\r\n        userInputs: this.workflowForm.value,\r\n        executionId: this.executionId,\r\n        user: this.tokenStorage.getDaUsername(),\r\n      };\r\n    }\r\n\r\n    console.log('Final payload:', payload);\r\n\r\n    this.getWorkflowLogs(this.executionId);\r\n    this.startFakeProgress();\r\n\r\n    this.workflowService\r\n      .executeWorkflow(payload, queryString)\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe({\r\n        next: (res) => {\r\n          this.isProcessingChat = false;\r\n          this.isRunning = false;\r\n          this.chatInterfaceComp.addAiResponse(res?.message || \"Workflow execution completed successfully!\");\r\n\r\n          console.log('Workflow execution response:', res);\r\n\r\n          // Check if we have workflow response and output\r\n          if (res?.workflowResponse?.pipeline) {\r\n            const pipeline = res.workflowResponse.pipeline;\r\n            console.log('Pipeline data:', pipeline);\r\n\r\n            this.isExecutionComplete = true;\r\n            this.workflowLogs.push({\r\n              content: this.constants['labels'].workflowExecComplete,\r\n              color: '#0F8251',\r\n            });\r\n            this.errorMsg = false;\r\n\r\n            // Set the output message\r\n            this.resMessage = pipeline.output || 'Workflow completed successfully';\r\n            console.log('Setting resMessage to:', this.resMessage);\r\n\r\n            // Always process task outputs if they exist (remove the length check)\r\n            if (pipeline.tasksOutputs && Array.isArray(pipeline.tasksOutputs)) {\r\n              console.log('Processing tasksOutputs:', pipeline.tasksOutputs);\r\n              this.agentOutputs = pipeline.tasksOutputs.map((task: any) => {\r\n                return {\r\n                  id: task?.id || Math.random().toString(36).substring(2, 11),\r\n                  title: task?.title || 'Task Output',\r\n                  content: task?.raw || task?.content || '',\r\n                  agentName: task?.agentName || 'Agent',\r\n                  timestamp: task?.timestamp || new Date().toISOString(),\r\n                  type: task?.type || 'text',\r\n                  description: task?.description || '',\r\n                  expected_output: task?.expected_output || '',\r\n                  summary: task?.summary || '',\r\n                  raw: task?.raw || '',\r\n                };\r\n              });\r\n              console.log('Agent outputs for display:', this.agentOutputs);\r\n              console.log('Agent outputs length:', this.agentOutputs.length);\r\n            } else {\r\n              console.log('No tasksOutputs found, checking for main output');\r\n              if (pipeline.output) {\r\n                // If no task outputs but we have main output, create a single output item\r\n                console.log('Creating output from main pipeline output');\r\n                this.agentOutputs = [{\r\n                  id: 'main-output',\r\n                  title: 'Workflow Output',\r\n                  content: pipeline.output,\r\n                  agentName: 'Workflow',\r\n                  timestamp: new Date().toISOString(),\r\n                  type: 'text',\r\n                  description: 'Main workflow output',\r\n                  expected_output: 'Workflow execution result',\r\n                  summary: 'Workflow completed successfully',\r\n                  raw: pipeline.output,\r\n                }];\r\n                console.log('Created fallback agent output:', this.agentOutputs);\r\n              }\r\n            }\r\n\r\n            // Process task messages - this should always run if tasksOutputs exists\r\n            if (pipeline.tasksOutputs && Array.isArray(pipeline.tasksOutputs)) {\r\n              this.taskMessage = pipeline.tasksOutputs.map(\r\n                (task: {\r\n                  description: any;\r\n                  summary: any;\r\n                  raw: any;\r\n                  expected_output: any;\r\n                }) => {\r\n                  return {\r\n                    description: task.description,\r\n                    summary: task.summary,\r\n                    raw: task.raw,\r\n                    expected_output: task.expected_output,\r\n                  };\r\n                },\r\n              );\r\n              console.log('Task messages processed:', this.taskMessage);\r\n              console.log('Task messages length:', this.taskMessage.length);\r\n            } else {\r\n              console.log('No tasksOutputs found or not an array:', pipeline.tasksOutputs);\r\n            }\r\n\r\n            // if(\"file_download_url\" in res?.pipeline){\r\n            //   this.isFileWriter = true;\r\n            //   this.fileDownloadLink = res?.pipeline?.file_download_url;\r\n\r\n            //   if(!this.fileDownloadLink){\r\n            //     this.fileDownloadUrlError = [];\r\n            //     this.fileDownloadUrlError.push(\"Output file is not generated yet!\")\r\n            //   }\r\n            // }\r\n            // this.isAccordian = true\r\n          }\r\n          this.validateJson(this.resMessage);\r\n          this.status = ExecutionStatus.completed;\r\n          this.stopFakeProgress();\r\n          this.selectedFiles = [];\r\n        },\r\n        error: (error) => {\r\n          this.isExecutionComplete = true;\r\n          this.isProcessingChat = false;\r\n          this.errorMsg = true;\r\n          this.resMessage = error?.error?.detail;\r\n          this.workflowService.workflowLogDisconnect();\r\n          this.workflowLogs.push({\r\n            content: this.constants['labels'].workflowLogFailed,\r\n            color: 'red',\r\n          });\r\n          this.chatInterfaceComp.addAiResponse(\r\n            'Something went wrong, Workflow execution has failed.',\r\n          );\r\n          this.selectedFiles = [];\r\n          this.stopFakeProgress();\r\n          console.log('error is', error.message);\r\n        },\r\n      });\r\n  }\r\n\r\n  // public asyncExecutePipeline() {\r\n  //   const payload: FormData = new FormData();\r\n  //   if (this.selectedFiles?.length) {\r\n  //     for (const element of this.selectedFiles) {\r\n  //       payload.append('files', element)\r\n  //     }\r\n  //   }\r\n  //   payload.append('pipeLineId', String(this.workflowId));\r\n  //   payload.append('userInputs', JSON.stringify(this.workflowForm.value));\r\n  //   payload.append('user', this.tokenStorage.getDaUsername() || '');\r\n  //   payload.append('executionId', this.executionId);\r\n\r\n  //   this.workflowService.asyncExecutePipeline(payload).pipe().subscribe({\r\n  //     next: (res: any) => {\r\n  //       if(res) {\r\n  //         // res handling\r\n  //         console.log(res);\r\n  //       }\r\n  //     },\r\n  //     error: e => {\r\n  //       // error handling\r\n  //       console.log(e);\r\n  //     }\r\n  //   })\r\n  // }\r\n\r\n  handleAttachment() {\r\n    console.log('handleAttachment');\r\n  }\r\n\r\n  onAttachmentsSelected(files: File[]) {\r\n    if(this.currentInputIndex===this.inputFieldOrder.length || this.inputFieldOrder.length===0){\r\n      this.selectedFiles = files;\r\n      return;\r\n    }\r\n\r\n    const field = this.inputFieldOrder[this.currentInputIndex];\r\n    if(this.isImageInput(field)){\r\n      if (files && files.length > 0) {\r\n        this.onImageSelected(files[0]);\r\n      }\r\n    } else {\r\n      this.selectedFiles = files;\r\n    }\r\n  }\r\n\r\n  startInputCollection(){\r\n    this.inputFieldOrder = Object.keys(this.workflowForm.controls);\r\n    this.currentInputIndex = 0;\r\n    if (this.inputFieldOrder.length > 0) {\r\n      this.promptForCurrentField();\r\n    }\r\n    else{\r\n      this.disableChat = true;\r\n      this.chatInterfaceComp.addAiResponse('No input is required for this workflow. Click on send button to execute the workflow.');\r\n    }\r\n  }\r\n\r\n  promptForCurrentField() {\r\n    const field = this.inputFieldOrder[this.currentInputIndex];\r\n    if (this.isImageInput(field)) {\r\n      this.fileType = '.jpeg,.png,.jpg,.svg';\r\n      this.chatInterfaceComp.addAiResponse(`Please upload an image for ${field}`);\r\n      // UI should now show a file input for the user\r\n    } else {\r\n      this.fileType = '.zip'; // or whatever default you want for non-image\r\n      this.chatInterfaceComp.addAiResponse(`Please enter the value of ${field}`);\r\n    }\r\n  }\r\n\r\n  onImageSelected(file: File) {\r\n    const field = this.inputFieldOrder[this.currentInputIndex];\r\n    if (!this.isImageInput(field)) return;\r\n\r\n    const reader = new FileReader();\r\n    reader.onload = () => {\r\n      const base64String = (reader.result as string); \r\n      this.workflowForm.get(field)?.setValue(base64String);\r\n      this.currentInputIndex++;\r\n      if (this.currentInputIndex < this.inputFieldOrder.length) {\r\n        this.promptForCurrentField();\r\n      } else {\r\n        this.chatInterfaceComp.addAiResponse('Thank you! Running the workflow...');\r\n        this.executeWorkflow();\r\n      }\r\n    };\r\n    reader.readAsDataURL(file);\r\n  }\r\n\r\n  // Event handlers for workflow playground\r\n  onPlaygroundBackClicked(): void {\r\n    this.navigateBack();\r\n  }\r\n\r\n  onPlaygroundCollapseToggled(isCollapsed: boolean): void {\r\n    this.isPlaygroundCollapsed = isCollapsed;\r\n  }\r\n\r\n  onAgentInputChanged(event: {agentId: number, inputIndex: number, value: string}): void {\r\n    // Find the agent and update the input value\r\n    const agent = this.agents.find(a => a.id === event.agentId);\r\n    if (agent && agent.inputs && agent.inputs[event.inputIndex]) {\r\n      agent.inputs[event.inputIndex].value = event.value;\r\n\r\n      // Update the form control if it exists\r\n      const placeholder = agent.inputs[event.inputIndex].placeholder;\r\n      if (this.workflowForm.get(placeholder)) {\r\n        this.workflowForm.get(placeholder)?.setValue(event.value);\r\n      }\r\n    }\r\n  }\r\n\r\n  onAgentFileSelected(event: {agentId: number, inputIndex: number, files: File[]}): void {\r\n    // Find the agent and update the files\r\n    const agent = this.agents.find(a => a.id === event.agentId);\r\n    if (agent && agent.inputs && agent.inputs[event.inputIndex]) {\r\n      agent.inputs[event.inputIndex].files = event.files;\r\n\r\n      // Add files to selectedFiles array for execution\r\n      this.selectedFiles = [...this.selectedFiles, ...event.files];\r\n    }\r\n  }\r\n\r\n  onMessageSent(event: {agentId: number, inputIndex: number, value: string, files?: File[]}): void {\r\n    console.log('Message sent from agent:', event);\r\n\r\n    // Find the agent and update the input value\r\n    const agent = this.agents.find(a => a.id === event.agentId);\r\n    if (agent && agent.inputs && agent.inputs[event.inputIndex]) {\r\n      agent.inputs[event.inputIndex].value = event.value;\r\n\r\n      // Update files if provided\r\n      if (event.files) {\r\n        agent.inputs[event.inputIndex].files = event.files;\r\n        this.selectedFiles = [...this.selectedFiles, ...event.files];\r\n      }\r\n\r\n      // Update the form control if it exists\r\n      const placeholder = agent.inputs[event.inputIndex].placeholder;\r\n      console.log('Updating form field:', placeholder, 'with value:', event.value);\r\n      console.log('Form controls available:', Object.keys(this.workflowForm.controls));\r\n      console.log('Current form value before update:', this.workflowForm.value);\r\n\r\n      if (this.workflowForm.get(placeholder)) {\r\n        this.workflowForm.get(placeholder)?.setValue(event.value);\r\n        console.log('Form value after update:', this.workflowForm.value);\r\n      } else {\r\n        console.warn('Form control not found for placeholder:', placeholder);\r\n        // Try to add the control dynamically\r\n        this.workflowForm.addControl(placeholder, this.formBuilder.control(event.value));\r\n        console.log('Added new form control. Form value:', this.workflowForm.value);\r\n      }\r\n\r\n      // Log the input submission\r\n      this.workflowLogs.push({\r\n        content: `Agent \"${agent.name}\" - Input \"${agent.inputs[event.inputIndex].inputName}\": ${event.value}`,\r\n        color: '#10B981'\r\n      });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get the logs to display based on the current view state\r\n   */\r\n  get displayedLogs(): any[] {\r\n    if (this.showAllLogs) {\r\n      return this.workflowLogs;\r\n    }\r\n    return this.workflowLogs.slice(0, this.displayedLogsCount);\r\n  }\r\n\r\n  /**\r\n   * Toggle between showing limited logs and all logs\r\n   */\r\n  toggleShowAllLogs(): void {\r\n    this.showAllLogs = !this.showAllLogs;\r\n  }\r\n\r\n  /**\r\n   * Check if there are more logs to show\r\n   */\r\n  get hasMoreLogs(): boolean {\r\n    return this.workflowLogs.length > this.displayedLogsCount && !this.showAllLogs;\r\n  }\r\n\r\n  /**\r\n   * Initialize demo logs for testing the log display functionality\r\n   */\r\n  private initializeDemoLogs(): void {\r\n    // Only add demo logs if there are no existing logs\r\n    if (this.workflowLogs.length === 0) {\r\n      const demoLogs = [\r\n        { content: 'Workflow execution started', color: '#0F8251' },\r\n        { content: 'Loading pipeline configuration...', color: '#6B7280' },\r\n        { content: 'Initializing Test Case Normalizer agent', color: '#2563EB' },\r\n        { content: 'Processing input parameters', color: '#6B7280' },\r\n        { content: 'Agent 1 is currently working', color: '#F9DB24' },\r\n        { content: 'Analyzing test case structure...', color: '#6B7280' },\r\n        { content: 'Normalizing test case format', color: '#6B7280' },\r\n        { content: 'Validating output format', color: '#6B7280' },\r\n        { content: 'Test Case Normalizer completed successfully', color: '#0F8251' },\r\n        { content: 'Initializing ELT_OAMA Frameworkagent', color: '#2563EB' },\r\n        { content: 'Loading framework configuration', color: '#6B7280' },\r\n        { content: 'Processing framework parameters', color: '#6B7280' },\r\n        { content: 'Agent 2 is currently working', color: '#F9DB24' },\r\n        { content: 'Executing framework analysis...', color: '#6B7280' },\r\n        { content: 'Generating framework recommendations', color: '#6B7280' },\r\n        { content: 'ELT_OAMA Framework analysis completed', color: '#0F8251' },\r\n        { content: 'Initializing ELT_QE_HPX_Automated_Script agent', color: '#2563EB' },\r\n        { content: 'Loading automation script templates', color: '#6B7280' },\r\n        { content: 'Agent 3 is currently working', color: '#F9DB24' },\r\n        { content: 'Generating automated test scripts...', color: '#6B7280' },\r\n        { content: 'Validating script syntax and structure', color: '#6B7280' },\r\n        { content: 'Script generation completed successfully', color: '#0F8251' },\r\n        { content: 'All pipeline agents completed successfully', color: '#0F8251' },\r\n        { content: 'Workflow execution completed', color: '#0F8251' }\r\n      ];\r\n\r\n      this.workflowLogs = demoLogs;\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,EAAqBC,SAAS,QAAQ,eAAe;AAEvE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,OAAO,EAAEC,SAAS,QAAQ,MAAM;AACzC,SAAiCC,WAAW,EAAEC,UAAU,QAAQ,gBAAgB;AAEhF;AACA,SAASC,sBAAsB,QAAQ,4DAA4D;AAEnG,SACEC,sBAAsB,QACjB,sDAAsD;AAC7D;AACA,SACEC,eAAe,EACfC,aAAa,EAEbC,aAAa,QACR,wBAAwB;AAG/B,SAASC,WAAW,QAAQ,kCAAkC;AAC9D,OAAOC,iBAAiB,MAAM,+BAA+B;AAG7D,SAASC,eAAe,QAAqB,gCAAgC;AAE7E,SAASC,2BAA2B,QAAmB,gEAAgE;AAoBhH,IAAMC,0BAA0B,GAAhC,MAAMA,0BAA0B;EA4E3BC,KAAA;EACAC,MAAA;EACAC,eAAA;EACAC,qBAAA;EACAC,YAAA;EACAC,aAAA;EACAC,WAAA;EAjFVC,cAAc,GAAc,CAC1B;IAAEC,EAAE,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAW,CAAE,EACtC;IAAED,EAAE,EAAE,cAAc;IAAEC,KAAK,EAAE;EAAQ,CAAE,EACvC;IAAED,EAAE,EAAE,cAAc;IAAEC,KAAK,EAAE;EAAe,CAAE,CAC/C;EACD;EACAC,UAAU,GAAkB,IAAI;EAChCC,YAAY,GAAW,UAAU;EAEjCC,SAAS,GAAGhB,iBAAwC;EAGpDiB,iBAAiB;EAEjB;EACAC,YAAY,GAAkB,EAAE;EAChCC,gBAAgB,GAAW,CAAC;EAC5BC,gBAAgB;EAChBC,SAAS,GAAY,KAAK;EAC1BC,MAAM,GAAoBrB,eAAe,CAACsB,UAAU;EAEpD;EACAC,YAAY,GAAkB,EAAE;EAChCC,gBAAgB,GAAY,KAAK;EACjCC,SAAS,GAAG,EAAE;EAEd;EACAC,YAAY,GAAiB,EAAE;EACxBC,YAAY;EACZC,QAAQ,GAAY,MAAM;EAEjC;EACAC,kBAAkB,GAAgB,IAAI;EACtCC,kBAAkB,GAAY,KAAK;EACnCC,WAAW;EAEJC,YAAY,GAAU,EAAE;EACxBC,kBAAkB,GAAW,EAAE;EAC/BC,WAAW,GAAY,KAAK;EACnCC,kBAAkB,GAAGrC,WAAW,CAACsC,kBAAkB,IAAI,KAAK;EAErDC,mBAAmB,GAAY,KAAK;EAC3CC,gBAAgB;EAEhB;EACQC,QAAQ,GAAG,IAAIlD,OAAO,EAAQ;EACtCmD,WAAW,GAAW,gBAAgB;EACtCC,QAAQ,GAAa,CACnB;IAAE9B,EAAE,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAgB,CAAE,EAC3C;IAAED,EAAE,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAc,CAAE,EACvC;IAAED,EAAE,EAAE,SAAS;IAAEC,KAAK,EAAE,SAAS;IAAE8B,QAAQ,EAAE;EAAI,CAAE,CACpD;EACDC,QAAQ,GAAG,KAAK;EAChBC,UAAU;EACVC,WAAW,GAAG,EAAE;EAChBC,WAAW,GAAG,KAAK;EACnBC,WAAW,GAAa,KAAK;EAC7BC,aAAa,GAAW,EAAE;EAC1BC,cAAc,GAAU,EAAE;EAC1BC,aAAa,GAAU,EAAE;EACzBC,QAAQ,GAAG,CAAC;EACZC,SAAS,GAAG,KAAK;EACjBC,WAAW,GAAW,EAAE;EAExBC,eAAe,GAAa,EAAE;EAC9BC,iBAAiB,GAAW,CAAC;EAC7BC,WAAW,GAAW,UAAU;EAGhC;EACAC,MAAM,GAAgB,EAAE;EACxBC,qBAAqB,GAAY,KAAK;EACtCC,cAAc,GAAU,EAAE;EAE1BC,YACUzD,KAAqB,EACrBC,MAAc,EACdC,eAAgC,EAChCC,qBAAoD,EACpDC,YAAiC,EACjCC,aAA4B,EAC5BC,WAAwB;IANxB,KAAAN,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;EAClB;EAEHoD,QAAQA,CAAA;IACN,IAAI,CAACrD,aAAa,CAACsD,aAAa,EAAE;IAClC,IAAI,CAACtB,WAAW,GAAG,gBAAgB;IACnC,IAAI,CAACT,WAAW,GAAGgC,MAAM,CAACC,UAAU,EAAE;IACtC;IACA,IAAI,CAAC7D,KAAK,CAAC8D,QAAQ,CAACC,IAAI,CAAC5E,SAAS,CAAC,IAAI,CAACiD,QAAQ,CAAC,CAAC,CAAC4B,SAAS,CAAEC,MAAM,IAAI;MACtE,IAAI,CAACvD,UAAU,GAAGuD,MAAM,CAACC,GAAG,CAAC,IAAI,CAAC;MAClC,IAAI,IAAI,CAACxD,UAAU,EAAE;QACnB,IAAI,CAACyD,YAAY,CAAC,IAAI,CAACzD,UAAU,CAAC;MACpC,CAAC,MAAM;QACL;QACA,IAAI,CAACT,MAAM,CAACmE,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;MAC5C;IACF,CAAC,CAAC;IACF;EACF;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACjC,QAAQ,CAACkC,IAAI,EAAE;IACpB,IAAI,CAAClC,QAAQ,CAACmC,QAAQ,EAAE;IACxB,IAAI,CAAClE,aAAa,CAACmE,YAAY,EAAE;EACnC;EACAC,WAAWA,CAACC,KAAoC;IAC9C,IAAI,CAACrC,WAAW,GAAGqC,KAAK,CAACjE,KAAK;IAC9B,IAAI,CAAC4C,WAAW,GAAGqB,KAAK,CAAClE,EAAE;IAC3BmE,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEF,KAAK,CAAC;EACpC;EAEA;EACAP,YAAYA,CAAC3D,EAAU;IACrB;IACAmE,OAAO,CAACC,GAAG,CAAC,6BAA6BpE,EAAE,EAAE,CAAC;IAC9C,IAAI,CAACY,YAAY,GAAG,CAClB;MACEyD,IAAI,EAAE,IAAI;MACVC,IAAI,EAAE;KACQ,CACjB;IACD,IAAI,CAACtD,YAAY,GAAG,IAAI,CAAClB,WAAW,CAACyE,KAAK,CAAC,EAAE,CAAC;IAE9C;IACA,IAAI,CAAC7E,eAAe,CAAC8E,eAAe,CAACxE,EAAE,CAAC,CAACwD,SAAS,CAAC;MAC/CM,IAAI,EAAGW,GAAG,IAAI;QACd,IAAI,CAACnC,cAAc,GAAGmC,GAAG,CAACnC,cAAc;QACxC,IAAI,CAACC,aAAa,GAAG,IAAI,CAACmC,iBAAiB,CAAC,IAAI,CAACpC,cAAc,CAAC;QAChE,IAAG,IAAI,CAACC,aAAa,CAACoC,MAAM,KAAK,CAAC,EAAC;UACjC,IAAI,CAACvC,WAAW,GAAG,IAAI;QACzB;QACA,IAAI,CAACjC,YAAY,GAAGsE,GAAG,CAACG,IAAI;QAC5B,IAAI,CAACC,cAAc,EAAE;QACrB,IAAI,CAACC,oBAAoB,EAAE;QAC3B,IAAI,CAACC,kBAAkB,EAAE,CAAC,CAAC;MAC7B,CAAC;MACCC,KAAK,EAAGC,GAAG,IAAI;QACb,IAAI,CAAC7C,WAAW,GAAG,IAAI;QACvB,IAAI,CAAC/B,iBAAiB,CAAC6E,aAAa,CAAC,uFAAuF,CAAC;QAC7Hf,OAAO,CAACC,GAAG,CAACa,GAAG,CAAC;MAClB;KACH,CAAC;IAEF;IACA,IAAI,CAACE,kBAAkB,CAACnF,EAAE,CAAC;EAE7B;EAEA;EACOmF,kBAAkBA,CAACnF,EAAU;IAClC,IAAI,CAACN,eAAe,CAAC0F,cAAc,CAACpF,EAAE,CAAC,CAACwD,SAAS,CAAC;MAChDM,IAAI,EAAGW,GAAQ,IAAI;QACjB,IAAIA,GAAG,EAAEY,cAAc,EAAEV,MAAM,EAAE;UAC/B,IAAI,CAAC3B,cAAc,GAAGyB,GAAG,CAACY,cAAc;UACxC,IAAI,CAAClF,YAAY,GAAGsE,GAAG,CAACG,IAAI;UAE5B;UACA,IAAI;YACF,IAAI,CAAC9B,MAAM,GAAG,IAAI,CAACnD,qBAAqB,CAAC2F,kBAAkB,CAAC,IAAI,CAACtC,cAAc,CAAC;YAChFmB,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAACtB,MAAM,CAAC;UAC/C,CAAC,CAAC,OAAOkC,KAAK,EAAE;YACdb,OAAO,CAACa,KAAK,CAAC,gDAAgD,EAAEA,KAAK,CAAC;YACtE,IAAI,CAAClC,MAAM,GAAG,EAAE;UAClB;UAEA;UACA,IAAI,CAACP,aAAa,GAAG,IAAI,CAACmC,iBAAiB,CAACD,GAAG,CAACY,cAAc,CAAC;UAC/D,IAAI,CAACR,cAAc,EAAE;QACvB,CAAC,MAAM;UACLV,OAAO,CAACoB,IAAI,CAAC,+CAA+C,CAAC;UAC7D,IAAI,CAACzC,MAAM,GAAG,EAAE;QAClB;MACF,CAAC;MACDkC,KAAK,EAAGQ,CAAM,IAAKrB,OAAO,CAACa,KAAK,CAACQ,CAAC;KACnC,CAAC;EACJ;EAEOd,iBAAiBA,CAACW,cAAmB;IAC1C,MAAMI,oBAAoB,GAAI,qCAAqC;IACnE,MAAMC,cAAc,GAAoE,EAAE;IAE1FL,cAAc,CAACM,OAAO,CAAEC,aAAkB,IAAI;MAC5C,MAAMC,SAAS,GAAGD,aAAa,EAAEE,KAAK,EAAElB,IAAI;MAC5C,MAAMmB,gBAAgB,GAAGH,aAAa,EAAEE,KAAK,EAAEE,IAAI,EAAEC,WAAW;MAChE,MAAMC,OAAO,GAAGH,gBAAgB,EAAEI,QAAQ,CAACV,oBAAoB,CAAC,IAAI,EAAE;MAEtE,KAAK,MAAMW,KAAK,IAAIF,OAAO,EAAE;QAC3B,MAAMG,WAAW,GAAGD,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC;QACxC,MAAME,gBAAgB,GAAGF,KAAK,CAAC,CAAC,CAAC;QACjC,IAAI,CAACV,cAAc,CAACW,WAAW,CAAC,EAAE;UAChCX,cAAc,CAACW,WAAW,CAAC,GAAG;YAAEvD,MAAM,EAAE,IAAIyD,GAAG,EAAE;YAAEC,MAAM,EAAE,IAAID,GAAG;UAAE,CAAE;UAAC;QACzE;QACA,IAAIV,SAAS,EAAE;UACbH,cAAc,CAACW,WAAW,CAAC,CAACvD,MAAM,CAAC2D,GAAG,CAACZ,SAAS,CAAC;QACnD;QACAH,cAAc,CAACW,WAAW,CAAC,CAACG,MAAM,CAACC,GAAG,CAACH,gBAAgB,CAAC;MAC1D;IACF,CAAC,CAAC;IAEF,OAAOI,MAAM,CAACC,OAAO,CAACjB,cAAc,CAAC,CAACkB,GAAG,CAAC,CAAC,CAACP,WAAW,EAAE;MAAEvD,MAAM;MAAE0D;IAAM,CAAE,CAAC,MAAM;MAChF5B,IAAI,EAAE,CAAC,GAAG9B,MAAM,CAAC,CAAC6B,MAAM,GAAG,CAAC,GACxB,GAAG,CAAC,GAAG7B,MAAM,CAAC,CAAC+D,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAGhE,MAAM,CAAC,CAACiE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,GAClE,CAAC,GAAGjE,MAAM,CAAC,CAACgE,IAAI,CAAC,OAAO,CAAC;MAC7BT,WAAW;MACXW,KAAK,EAAE,CAAC,GAAGR,MAAM,CAAC,CAAC,CAAC;KACrB,CAAC,CAAC;EACL;EAEOS,YAAYA,CAACD,KAAa;IAC/B,MAAMZ,KAAK,GAAGY,KAAK,CAACZ,KAAK,CAAC,WAAW,CAAC;IACtC,IAAIA,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC,EAAE;MACrB,MAAMc,YAAY,GAAGd,KAAK,CAAC,CAAC,CAAC,CAACe,IAAI,EAAE;MACpC,OAAOD,YAAY,CAACE,UAAU,CAAC,OAAO,CAAC,IAAIF,YAAY,CAACE,UAAU,CAAC,OAAO,CAAC;IAC7E;IACA,OAAO,KAAK;EACd;EAEOvC,cAAcA,CAAA;IACnBV,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE,IAAI,CAAC7B,aAAa,CAAC;IACxE,IAAI,CAACA,aAAa,CAACoD,OAAO,CAAE1F,KAAU,IAAI;MACxCkE,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEnE,KAAK,CAAC+G,KAAK,CAAC;MACpD,IAAI,CAAChG,YAAY,CAACqG,UAAU,CAACpH,KAAK,CAAC+G,KAAK,EAAE,IAAI,CAAClH,WAAW,CAACwH,OAAO,CAAC,EAAE,EAAEzI,UAAU,CAAC0I,QAAQ,CAAC,CAAC;IAC9F,CAAC,CAAC;IAEF;IACA,IAAI,IAAI,CAACzE,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAAC6C,OAAO,CAACG,KAAK,IAAG;QAC1B,IAAIA,KAAK,CAACU,MAAM,EAAE;UAChBV,KAAK,CAACU,MAAM,CAACb,OAAO,CAACqB,KAAK,IAAG;YAC3B,MAAMQ,WAAW,GAAGR,KAAK,CAACX,WAAW;YACrC,IAAI,CAAC,IAAI,CAACrF,YAAY,CAAC0C,GAAG,CAAC8D,WAAW,CAAC,EAAE;cACvCrD,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAEoD,WAAW,CAAC;cAC3E,IAAI,CAACxG,YAAY,CAACqG,UAAU,CAACG,WAAW,EAAE,IAAI,CAAC1H,WAAW,CAACwH,OAAO,CAAC,EAAE,EAAEzI,UAAU,CAAC0I,QAAQ,CAAC,CAAC;YAC9F;UACF,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ;IAEApD,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEsC,MAAM,CAACe,IAAI,CAAC,IAAI,CAACzG,YAAY,CAAC0G,QAAQ,CAAC,CAAC;EAC9E;EAEOC,YAAYA,CAAA;IACjB,OAAO,IAAI,CAAC3G,YAAY,CAAC4G,KAAK,IAAI,IAAI,CAAC1H,UAAU;EACnD;EAEA2H,iBAAiBA,CAAA;IACf,IAAI,CAACrF,QAAQ,GAAG,CAAC;IACjB,IAAI,CAACb,gBAAgB,GAAGmG,WAAW,CAAC,MAAK;MACvC,IAAI,IAAI,CAACtF,QAAQ,GAAG,EAAE,EAAE;QACtB,IAAI,CAACA,QAAQ,IAAI,CAAC,CAAC,CAAC;MACtB;IACF,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;EACX;EAEAuF,gBAAgBA,CAAA;IACdC,aAAa,CAAC,IAAI,CAACrG,gBAAgB,CAAC;IACpC,IAAI,CAACa,QAAQ,GAAG,GAAG;IAEnByF,UAAU,CAAC,MAAK;MACd,IAAI,CAACxF,SAAS,GAAG,KAAK;IACxB,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;EACX;EAEA;EACAyF,iBAAiBA,CAACC,OAAe;IAC/B;IACA,IAAI,CAACtH,gBAAgB,GAAG,IAAI;IAC5B,IAAGsH,OAAO,CAAChB,IAAI,EAAE,KAAK,EAAE,EAAC;MACvB,IAAG,IAAI,CAACxE,eAAe,CAACgC,MAAM,KAAK,CAAC,EAAC;QACnC,IAAI,CAACtE,iBAAiB,CAAC6E,aAAa,CAAC,2BAA2B,CAAC;QACjE,IAAI,CAACkD,eAAe,EAAE;MACxB;MACA;IACF;IAEA,IAAG,IAAI,CAAC1G,mBAAmB,IAAI,IAAI,CAACkB,iBAAiB,KAAG,IAAI,CAACD,eAAe,CAACgC,MAAM,EAAC;MAChF,IAAI,CAACtE,iBAAiB,CAAC6E,aAAa,CAAC,2BAA2B,CAAC;MACjE,IAAI,CAACkD,eAAe,EAAE;MACxB;IACF;IAEA,MAAMC,KAAK,GAAG,IAAI,CAAC1F,eAAe,CAAC,IAAI,CAACC,iBAAiB,CAAC;IAC1D,IAAI,IAAI,CAACqE,YAAY,CAACoB,KAAK,CAAC,EAAE;MAC5B;MACA,IAAI,CAAChI,iBAAiB,CAAC6E,aAAa,CAAC,mCAAmCmD,KAAK,EAAE,CAAC;MAChF;IACF;IAEA,IAAI,CAACrH,YAAY,CAAC0C,GAAG,CAAC2E,KAAK,CAAC,EAAEC,QAAQ,CAACH,OAAO,CAAC;IAC/C,IAAI,CAACvF,iBAAiB,EAAE;IAExB,IAAI,IAAI,CAACA,iBAAiB,GAAG,IAAI,CAACD,eAAe,CAACgC,MAAM,EAAE;MACxD,IAAI,CAAC4D,qBAAqB,EAAE;IAC9B,CAAC,MAAM;MACL,IAAI,CAAClI,iBAAiB,CAAC6E,aAAa,CAAC,oDAAoD,CAAC;MAC1F,IAAI,CAACkD,eAAe,EAAE;IACxB;EACF;EAEA;EACAI,QAAQA,CAAA;IACNrE,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;IACvC;EACF;EAEA;EACAqE,aAAaA,CAACC,OAA8B;IAC1CvE,OAAO,CAACC,GAAG,CAAC,aAAasE,OAAO,UAAU,CAAC;IAE3C,IAAIA,OAAO,KAAK,UAAU,EAAE;MAC1B,MAAMC,IAAI,GAAG,IAAI,CAACrI,YAAY,CAC3BsG,GAAG,CAAExC,GAAG,IAAK,IAAIA,GAAG,CAACwE,SAAS,KAAKxE,GAAG,CAAC+D,OAAO,EAAE,CAAC,CACjDrB,IAAI,CAAC,IAAI,CAAC;MACb,IAAI,CAAC+B,cAAc,CAACF,IAAI,EAAE,4BAA4B,EAAE,YAAY,CAAC;IACvE,CAAC,MAAM;MACL,MAAMA,IAAI,GAAGG,IAAI,CAACC,SAAS,CAAC,IAAI,CAAChI,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC;MACvD,IAAI,CAAC8H,cAAc,CAACF,IAAI,EAAE,uBAAuB,EAAE,kBAAkB,CAAC;IACxE;EACF;EAEA;EACQE,cAAcA,CAACF,IAAY,EAAEK,QAAgB,EAAEC,IAAY;IACjE,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACR,IAAI,CAAC,EAAE;MAAEM;IAAI,CAAE,CAAC;IACvC,MAAMG,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;IACrC,MAAMK,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCF,IAAI,CAACG,IAAI,GAAGN,GAAG;IACfG,IAAI,CAACI,QAAQ,GAAGX,QAAQ;IACxBO,IAAI,CAACK,KAAK,EAAE;IACZP,GAAG,CAACQ,eAAe,CAACT,GAAG,CAAC;EAC1B;EAEA;EACAU,mBAAmBA,CAACC,MAAiC;IACnD5F,OAAO,CAACC,GAAG,CAAC,mBAAmB2F,MAAM,EAAE,CAAC;IACxC;IAEA,IAAIA,MAAM,KAAK,MAAM,EAAE;MACrB,IAAI,CAACtJ,SAAS,GAAG,IAAI;IACvB,CAAC,MAAM,IAAIsJ,MAAM,KAAK,OAAO,IAAIA,MAAM,KAAK,MAAM,EAAE;MAClD,IAAI,CAACtJ,SAAS,GAAG,KAAK;IACxB;EACF;EAEA;EACAuJ,YAAYA,CAAA;IACV,IAAI,CAACvK,MAAM,CAACmE,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;EAC5C;EAEA;EACAqG,YAAYA,CAAA;IACV,IAAI,IAAI,CAAC/J,UAAU,EAAE;MACnB,IAAI,CAACT,MAAM,CAACmE,QAAQ,CAAC,CAAC,uBAAuB,EAAE,IAAI,CAAC1D,UAAU,CAAC,CAAC;IAClE;EACF;EAEOgK,kBAAkBA,CAACC,KAAA,GAAgB,IAAI;IAC5ClC,UAAU,CAAC,MAAK;MACd,IAAI,CAAC,IAAI,CAACvG,mBAAmB,EAAE;QAC7ByC,OAAO,CAACC,GAAG,CAAC,IAAI,CAAChE,SAAS,CAAC;QAC3B+D,OAAO,CAACC,GAAG,CAAC,IAAI,CAAChE,SAAS,CAAC,QAAQ,CAAC,CAACgK,sBAAsB,CAAC;QAC5D,IAAI,CAAC/I,YAAY,CAACgJ,IAAI,CAAC;UACrBC,OAAO,EAAE,IAAI,CAAClK,SAAS,CAAC,QAAQ,CAAC,CAACgK,sBAAsB;UACxDG,KAAK,EAAE;SACR,CAAC;MACJ;IACF,CAAC,EAAEJ,KAAK,CAAC;EACX;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEOK,eAAeA,CAACpJ,WAAmB;IACxC+C,OAAO,CAACC,GAAG,CAAC,qDAAqD,EAAEhD,WAAW,CAAC;IAE/E,IAAI;MACF,IAAI,CAAC1B,eAAe,CACjB+K,kBAAkB,CAACrJ,WAAW,CAAC,CAC/BmC,IAAI,CAAC5E,SAAS,CAAC,IAAI,CAACiD,QAAQ,CAAC,CAAC,CAC9B4B,SAAS,CAAC;QACTM,IAAI,EAAGqE,OAAO,IAAI;UAChBhE,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE+D,OAAO,CAAC;UACnD,MAAM;YAAEmC,OAAO;YAAEC;UAAK,CAAE,GAAGpC,OAAO;UAClC,IAAIoC,KAAK,EAAE;YACT,IAAI,CAAClJ,YAAY,CAACgJ,IAAI,CAAC;cAAEC,OAAO;cAAEC;YAAK,CAAE,CAAC;UAC5C,CAAC,MAAM,IAAI,IAAI,CAAC/I,kBAAkB,KAAK,KAAK,EAAE;YAC5C;YACA,IAAI,CAACH,YAAY,CAACgJ,IAAI,CAAC;cAAEC,OAAO;cAAEC,KAAK,EAAE;YAAS,CAAE,CAAC;UACvD;QACF,CAAC;QACDvF,KAAK,EAAGC,GAAG,IAAI;UACbd,OAAO,CAACa,KAAK,CAAC,6BAA6B,EAAEC,GAAG,CAAC;UACjD,IAAI,CAAC5D,YAAY,CAACgJ,IAAI,CAAC;YACrBC,OAAO,EAAE,uDAAuD;YAChEC,KAAK,EAAE;WACR,CAAC;QACJ,CAAC;QACDxG,QAAQ,EAAEA,CAAA,KAAK;UACb,IAAI,CAACmG,kBAAkB,EAAE;UACzB/F,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;QAC5C;OACD,CAAC;IACN,CAAC,CAAC,OAAOY,KAAK,EAAE;MACdb,OAAO,CAACa,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACjE,IAAI,CAAC3D,YAAY,CAACgJ,IAAI,CAAC;QACrBC,OAAO,EAAE,8DAA8D;QACvEC,KAAK,EAAE;OACR,CAAC;IACJ;EACF;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEOG,YAAYA,CAACC,MAAc;IAChC,IAAI,CAACxI,WAAW,GAAG,KAAK;IACxB,IAAI;MACF,MAAMyI,YAAY,GAAG9B,IAAI,CAAC+B,KAAK,CAACF,MAAM,CAAC;MACvC,IAAI,CAACxI,WAAW,GAAG,IAAI;MACvB,OAAOyI,YAAY;IACrB,CAAC,CAAC,OAAOpF,CAAC,EAAE;MACV,OAAO,IAAI;IACb;EACF;EAEO4C,eAAeA,CAAA;IACpB,IAAI0C,OAAO,GAAmC,IAAIC,QAAQ,EAAE;IAC5D,IAAIC,WAAW,GAAG,EAAE;IAEpB7G,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE,IAAI,CAACpD,YAAY,CAACiK,KAAK,CAAC;IAC3E9G,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEsC,MAAM,CAACe,IAAI,CAAC,IAAI,CAACzG,YAAY,CAAC0G,QAAQ,CAAC,CAAC;IACtEvD,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAACtB,MAAM,CAAC8D,GAAG,CAACsE,CAAC,KAAK;MAAEtG,IAAI,EAAEsG,CAAC,CAACtG,IAAI;MAAE4B,MAAM,EAAE0E,CAAC,CAAC1E;IAAM,CAAE,CAAC,CAAC,CAAC;IAExF,IAAI,CAAC9F,MAAM,GAAGrB,eAAe,CAAC8L,OAAO;IACrC,IAAI,IAAI,CAAC9I,aAAa,CAACsC,MAAM,EAAE;MAC7B,IAAI,CAACtC,aAAa,CAACsD,OAAO,CAAEyF,IAAI,IAAI;QAClCN,OAAO,CAACO,MAAM,CAAC,OAAO,EAAED,IAAI,CAAC;MAC/B,CAAC,CAAC;MACFN,OAAO,CAACO,MAAM,CAAC,YAAY,EAAE,IAAI,CAACnL,UAAU,CAAC;MAC7C4K,OAAO,CAACO,MAAM,CAAC,YAAY,EAAEvC,IAAI,CAACC,SAAS,CAAC,IAAI,CAAC/H,YAAY,CAACiK,KAAK,CAAC,CAAC;MACrEH,OAAO,CAACO,MAAM,CAAC,MAAM,EAAE,IAAI,CAACzL,YAAY,CAAC0L,aAAa,EAAE,CAAC;MACzDR,OAAO,CAACO,MAAM,CAAC,aAAa,EAAE,IAAI,CAACjK,WAAW,CAAC;MAC/C4J,WAAW,GAAG,QAAQ;IACxB,CAAC,MAAM;MACLF,OAAO,GAAG;QACRS,UAAU,EAAE,IAAI,CAACrL,UAAU;QAC3BsL,UAAU,EAAE,IAAI,CAACxK,YAAY,CAACiK,KAAK;QACnC7J,WAAW,EAAE,IAAI,CAACA,WAAW;QAC7BqK,IAAI,EAAE,IAAI,CAAC7L,YAAY,CAAC0L,aAAa;OACtC;IACH;IAEAnH,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE0G,OAAO,CAAC;IAEtC,IAAI,CAACN,eAAe,CAAC,IAAI,CAACpJ,WAAW,CAAC;IACtC,IAAI,CAACyG,iBAAiB,EAAE;IAExB,IAAI,CAACnI,eAAe,CACjB0I,eAAe,CAAC0C,OAAO,EAAEE,WAAW,CAAC,CACrCzH,IAAI,CAAC5E,SAAS,CAAC,IAAI,CAACiD,QAAQ,CAAC,CAAC,CAC9B4B,SAAS,CAAC;MACTM,IAAI,EAAGW,GAAG,IAAI;QACZ,IAAI,CAAC5D,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAACJ,SAAS,GAAG,KAAK;QACtB,IAAI,CAACJ,iBAAiB,CAAC6E,aAAa,CAACT,GAAG,EAAE0D,OAAO,IAAI,4CAA4C,CAAC;QAElGhE,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEK,GAAG,CAAC;QAEhD;QACA,IAAIA,GAAG,EAAEiH,gBAAgB,EAAEC,QAAQ,EAAE;UACnC,MAAMA,QAAQ,GAAGlH,GAAG,CAACiH,gBAAgB,CAACC,QAAQ;UAC9CxH,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEuH,QAAQ,CAAC;UAEvC,IAAI,CAACjK,mBAAmB,GAAG,IAAI;UAC/B,IAAI,CAACL,YAAY,CAACgJ,IAAI,CAAC;YACrBC,OAAO,EAAE,IAAI,CAAClK,SAAS,CAAC,QAAQ,CAAC,CAACwL,oBAAoB;YACtDrB,KAAK,EAAE;WACR,CAAC;UACF,IAAI,CAACvI,QAAQ,GAAG,KAAK;UAErB;UACA,IAAI,CAACC,UAAU,GAAG0J,QAAQ,CAAChB,MAAM,IAAI,iCAAiC;UACtExG,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE,IAAI,CAACnC,UAAU,CAAC;UAEtD;UACA,IAAI0J,QAAQ,CAACE,YAAY,IAAIC,KAAK,CAACC,OAAO,CAACJ,QAAQ,CAACE,YAAY,CAAC,EAAE;YACjE1H,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEuH,QAAQ,CAACE,YAAY,CAAC;YAC9D,IAAI,CAAC9K,YAAY,GAAG4K,QAAQ,CAACE,YAAY,CAACjF,GAAG,CAAEZ,IAAS,IAAI;cAC1D,OAAO;gBACLhG,EAAE,EAAEgG,IAAI,EAAEhG,EAAE,IAAIgM,IAAI,CAACC,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;gBAC3DC,KAAK,EAAEpG,IAAI,EAAEoG,KAAK,IAAI,aAAa;gBACnC9B,OAAO,EAAEtE,IAAI,EAAEqG,GAAG,IAAIrG,IAAI,EAAEsE,OAAO,IAAI,EAAE;gBACzCzE,SAAS,EAAEG,IAAI,EAAEH,SAAS,IAAI,OAAO;gBACrC+C,SAAS,EAAE5C,IAAI,EAAE4C,SAAS,IAAI,IAAI0D,IAAI,EAAE,CAACC,WAAW,EAAE;gBACtDtD,IAAI,EAAEjD,IAAI,EAAEiD,IAAI,IAAI,MAAM;gBAC1BhD,WAAW,EAAED,IAAI,EAAEC,WAAW,IAAI,EAAE;gBACpCuG,eAAe,EAAExG,IAAI,EAAEwG,eAAe,IAAI,EAAE;gBAC5CC,OAAO,EAAEzG,IAAI,EAAEyG,OAAO,IAAI,EAAE;gBAC5BJ,GAAG,EAAErG,IAAI,EAAEqG,GAAG,IAAI;eACnB;YACH,CAAC,CAAC;YACFlI,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAACrD,YAAY,CAAC;YAC5DoD,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAACrD,YAAY,CAAC4D,MAAM,CAAC;UAChE,CAAC,MAAM;YACLR,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;YAC9D,IAAIuH,QAAQ,CAAChB,MAAM,EAAE;cACnB;cACAxG,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;cACxD,IAAI,CAACrD,YAAY,GAAG,CAAC;gBACnBf,EAAE,EAAE,aAAa;gBACjBoM,KAAK,EAAE,iBAAiB;gBACxB9B,OAAO,EAAEqB,QAAQ,CAAChB,MAAM;gBACxB9E,SAAS,EAAE,UAAU;gBACrB+C,SAAS,EAAE,IAAI0D,IAAI,EAAE,CAACC,WAAW,EAAE;gBACnCtD,IAAI,EAAE,MAAM;gBACZhD,WAAW,EAAE,sBAAsB;gBACnCuG,eAAe,EAAE,2BAA2B;gBAC5CC,OAAO,EAAE,iCAAiC;gBAC1CJ,GAAG,EAAEV,QAAQ,CAAChB;eACf,CAAC;cACFxG,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE,IAAI,CAACrD,YAAY,CAAC;YAClE;UACF;UAEA;UACA,IAAI4K,QAAQ,CAACE,YAAY,IAAIC,KAAK,CAACC,OAAO,CAACJ,QAAQ,CAACE,YAAY,CAAC,EAAE;YACjE,IAAI,CAAC3J,WAAW,GAAGyJ,QAAQ,CAACE,YAAY,CAACjF,GAAG,CACzCZ,IAKA,IAAI;cACH,OAAO;gBACLC,WAAW,EAAED,IAAI,CAACC,WAAW;gBAC7BwG,OAAO,EAAEzG,IAAI,CAACyG,OAAO;gBACrBJ,GAAG,EAAErG,IAAI,CAACqG,GAAG;gBACbG,eAAe,EAAExG,IAAI,CAACwG;eACvB;YACH,CAAC,CACF;YACDrI,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAAClC,WAAW,CAAC;YACzDiC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAAClC,WAAW,CAACyC,MAAM,CAAC;UAC/D,CAAC,MAAM;YACLR,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEuH,QAAQ,CAACE,YAAY,CAAC;UAC9E;UAEA;UACA;UACA;UAEA;UACA;UACA;UACA;UACA;UACA;QACF;QACA,IAAI,CAACnB,YAAY,CAAC,IAAI,CAACzI,UAAU,CAAC;QAClC,IAAI,CAACvB,MAAM,GAAGrB,eAAe,CAACqN,SAAS;QACvC,IAAI,CAAC3E,gBAAgB,EAAE;QACvB,IAAI,CAAC1F,aAAa,GAAG,EAAE;MACzB,CAAC;MACD2C,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACtD,mBAAmB,GAAG,IAAI;QAC/B,IAAI,CAACb,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAACmB,QAAQ,GAAG,IAAI;QACpB,IAAI,CAACC,UAAU,GAAG+C,KAAK,EAAEA,KAAK,EAAE2H,MAAM;QACtC,IAAI,CAACjN,eAAe,CAACkN,qBAAqB,EAAE;QAC5C,IAAI,CAACvL,YAAY,CAACgJ,IAAI,CAAC;UACrBC,OAAO,EAAE,IAAI,CAAClK,SAAS,CAAC,QAAQ,CAAC,CAACyM,iBAAiB;UACnDtC,KAAK,EAAE;SACR,CAAC;QACF,IAAI,CAAClK,iBAAiB,CAAC6E,aAAa,CAClC,sDAAsD,CACvD;QACD,IAAI,CAAC7C,aAAa,GAAG,EAAE;QACvB,IAAI,CAAC0F,gBAAgB,EAAE;QACvB5D,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEY,KAAK,CAACmD,OAAO,CAAC;MACxC;KACD,CAAC;EACN;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA2E,gBAAgBA,CAAA;IACd3I,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;EACjC;EAEA2I,qBAAqBA,CAACC,KAAa;IACjC,IAAG,IAAI,CAACpK,iBAAiB,KAAG,IAAI,CAACD,eAAe,CAACgC,MAAM,IAAI,IAAI,CAAChC,eAAe,CAACgC,MAAM,KAAG,CAAC,EAAC;MACzF,IAAI,CAACtC,aAAa,GAAG2K,KAAK;MAC1B;IACF;IAEA,MAAM3E,KAAK,GAAG,IAAI,CAAC1F,eAAe,CAAC,IAAI,CAACC,iBAAiB,CAAC;IAC1D,IAAG,IAAI,CAACqE,YAAY,CAACoB,KAAK,CAAC,EAAC;MAC1B,IAAI2E,KAAK,IAAIA,KAAK,CAACrI,MAAM,GAAG,CAAC,EAAE;QAC7B,IAAI,CAACsI,eAAe,CAACD,KAAK,CAAC,CAAC,CAAC,CAAC;MAChC;IACF,CAAC,MAAM;MACL,IAAI,CAAC3K,aAAa,GAAG2K,KAAK;IAC5B;EACF;EAEAlI,oBAAoBA,CAAA;IAClB,IAAI,CAACnC,eAAe,GAAG+D,MAAM,CAACe,IAAI,CAAC,IAAI,CAACzG,YAAY,CAAC0G,QAAQ,CAAC;IAC9D,IAAI,CAAC9E,iBAAiB,GAAG,CAAC;IAC1B,IAAI,IAAI,CAACD,eAAe,CAACgC,MAAM,GAAG,CAAC,EAAE;MACnC,IAAI,CAAC4D,qBAAqB,EAAE;IAC9B,CAAC,MACG;MACF,IAAI,CAACnG,WAAW,GAAG,IAAI;MACvB,IAAI,CAAC/B,iBAAiB,CAAC6E,aAAa,CAAC,uFAAuF,CAAC;IAC/H;EACF;EAEAqD,qBAAqBA,CAAA;IACnB,MAAMF,KAAK,GAAG,IAAI,CAAC1F,eAAe,CAAC,IAAI,CAACC,iBAAiB,CAAC;IAC1D,IAAI,IAAI,CAACqE,YAAY,CAACoB,KAAK,CAAC,EAAE;MAC5B,IAAI,CAACpH,QAAQ,GAAG,sBAAsB;MACtC,IAAI,CAACZ,iBAAiB,CAAC6E,aAAa,CAAC,8BAA8BmD,KAAK,EAAE,CAAC;MAC3E;IACF,CAAC,MAAM;MACL,IAAI,CAACpH,QAAQ,GAAG,MAAM,CAAC,CAAC;MACxB,IAAI,CAACZ,iBAAiB,CAAC6E,aAAa,CAAC,6BAA6BmD,KAAK,EAAE,CAAC;IAC5E;EACF;EAEA4E,eAAeA,CAAC7B,IAAU;IACxB,MAAM/C,KAAK,GAAG,IAAI,CAAC1F,eAAe,CAAC,IAAI,CAACC,iBAAiB,CAAC;IAC1D,IAAI,CAAC,IAAI,CAACqE,YAAY,CAACoB,KAAK,CAAC,EAAE;IAE/B,MAAM6E,MAAM,GAAG,IAAIC,UAAU,EAAE;IAC/BD,MAAM,CAACE,MAAM,GAAG,MAAK;MACnB,MAAMC,YAAY,GAAIH,MAAM,CAACI,MAAiB;MAC9C,IAAI,CAACtM,YAAY,CAAC0C,GAAG,CAAC2E,KAAK,CAAC,EAAEC,QAAQ,CAAC+E,YAAY,CAAC;MACpD,IAAI,CAACzK,iBAAiB,EAAE;MACxB,IAAI,IAAI,CAACA,iBAAiB,GAAG,IAAI,CAACD,eAAe,CAACgC,MAAM,EAAE;QACxD,IAAI,CAAC4D,qBAAqB,EAAE;MAC9B,CAAC,MAAM;QACL,IAAI,CAAClI,iBAAiB,CAAC6E,aAAa,CAAC,oCAAoC,CAAC;QAC1E,IAAI,CAACkD,eAAe,EAAE;MACxB;IACF,CAAC;IACD8E,MAAM,CAACK,aAAa,CAACnC,IAAI,CAAC;EAC5B;EAEA;EACAoC,uBAAuBA,CAAA;IACrB,IAAI,CAACxD,YAAY,EAAE;EACrB;EAEAyD,2BAA2BA,CAACC,WAAoB;IAC9C,IAAI,CAAC3K,qBAAqB,GAAG2K,WAAW;EAC1C;EAEAC,mBAAmBA,CAACzJ,KAA2D;IAC7E;IACA,MAAM4B,KAAK,GAAG,IAAI,CAAChD,MAAM,CAAC8K,IAAI,CAAC1C,CAAC,IAAIA,CAAC,CAAClL,EAAE,KAAKkE,KAAK,CAAC2J,OAAO,CAAC;IAC3D,IAAI/H,KAAK,IAAIA,KAAK,CAACU,MAAM,IAAIV,KAAK,CAACU,MAAM,CAACtC,KAAK,CAAC4J,UAAU,CAAC,EAAE;MAC3DhI,KAAK,CAACU,MAAM,CAACtC,KAAK,CAAC4J,UAAU,CAAC,CAAC7C,KAAK,GAAG/G,KAAK,CAAC+G,KAAK;MAElD;MACA,MAAM5E,WAAW,GAAGP,KAAK,CAACU,MAAM,CAACtC,KAAK,CAAC4J,UAAU,CAAC,CAACzH,WAAW;MAC9D,IAAI,IAAI,CAACrF,YAAY,CAAC0C,GAAG,CAAC2C,WAAW,CAAC,EAAE;QACtC,IAAI,CAACrF,YAAY,CAAC0C,GAAG,CAAC2C,WAAW,CAAC,EAAEiC,QAAQ,CAACpE,KAAK,CAAC+G,KAAK,CAAC;MAC3D;IACF;EACF;EAEA8C,mBAAmBA,CAAC7J,KAA2D;IAC7E;IACA,MAAM4B,KAAK,GAAG,IAAI,CAAChD,MAAM,CAAC8K,IAAI,CAAC1C,CAAC,IAAIA,CAAC,CAAClL,EAAE,KAAKkE,KAAK,CAAC2J,OAAO,CAAC;IAC3D,IAAI/H,KAAK,IAAIA,KAAK,CAACU,MAAM,IAAIV,KAAK,CAACU,MAAM,CAACtC,KAAK,CAAC4J,UAAU,CAAC,EAAE;MAC3DhI,KAAK,CAACU,MAAM,CAACtC,KAAK,CAAC4J,UAAU,CAAC,CAACd,KAAK,GAAG9I,KAAK,CAAC8I,KAAK;MAElD;MACA,IAAI,CAAC3K,aAAa,GAAG,CAAC,GAAG,IAAI,CAACA,aAAa,EAAE,GAAG6B,KAAK,CAAC8I,KAAK,CAAC;IAC9D;EACF;EAEAgB,aAAaA,CAAC9J,KAA2E;IACvFC,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEF,KAAK,CAAC;IAE9C;IACA,MAAM4B,KAAK,GAAG,IAAI,CAAChD,MAAM,CAAC8K,IAAI,CAAC1C,CAAC,IAAIA,CAAC,CAAClL,EAAE,KAAKkE,KAAK,CAAC2J,OAAO,CAAC;IAC3D,IAAI/H,KAAK,IAAIA,KAAK,CAACU,MAAM,IAAIV,KAAK,CAACU,MAAM,CAACtC,KAAK,CAAC4J,UAAU,CAAC,EAAE;MAC3DhI,KAAK,CAACU,MAAM,CAACtC,KAAK,CAAC4J,UAAU,CAAC,CAAC7C,KAAK,GAAG/G,KAAK,CAAC+G,KAAK;MAElD;MACA,IAAI/G,KAAK,CAAC8I,KAAK,EAAE;QACflH,KAAK,CAACU,MAAM,CAACtC,KAAK,CAAC4J,UAAU,CAAC,CAACd,KAAK,GAAG9I,KAAK,CAAC8I,KAAK;QAClD,IAAI,CAAC3K,aAAa,GAAG,CAAC,GAAG,IAAI,CAACA,aAAa,EAAE,GAAG6B,KAAK,CAAC8I,KAAK,CAAC;MAC9D;MAEA;MACA,MAAM3G,WAAW,GAAGP,KAAK,CAACU,MAAM,CAACtC,KAAK,CAAC4J,UAAU,CAAC,CAACzH,WAAW;MAC9DlC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEiC,WAAW,EAAE,aAAa,EAAEnC,KAAK,CAAC+G,KAAK,CAAC;MAC5E9G,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEsC,MAAM,CAACe,IAAI,CAAC,IAAI,CAACzG,YAAY,CAAC0G,QAAQ,CAAC,CAAC;MAChFvD,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE,IAAI,CAACpD,YAAY,CAACiK,KAAK,CAAC;MAEzE,IAAI,IAAI,CAACjK,YAAY,CAAC0C,GAAG,CAAC2C,WAAW,CAAC,EAAE;QACtC,IAAI,CAACrF,YAAY,CAAC0C,GAAG,CAAC2C,WAAW,CAAC,EAAEiC,QAAQ,CAACpE,KAAK,CAAC+G,KAAK,CAAC;QACzD9G,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAACpD,YAAY,CAACiK,KAAK,CAAC;MAClE,CAAC,MAAM;QACL9G,OAAO,CAACoB,IAAI,CAAC,yCAAyC,EAAEc,WAAW,CAAC;QACpE;QACA,IAAI,CAACrF,YAAY,CAACqG,UAAU,CAAChB,WAAW,EAAE,IAAI,CAACvG,WAAW,CAACwH,OAAO,CAACpD,KAAK,CAAC+G,KAAK,CAAC,CAAC;QAChF9G,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE,IAAI,CAACpD,YAAY,CAACiK,KAAK,CAAC;MAC7E;MAEA;MACA,IAAI,CAAC5J,YAAY,CAACgJ,IAAI,CAAC;QACrBC,OAAO,EAAE,UAAUxE,KAAK,CAAClB,IAAI,cAAckB,KAAK,CAACU,MAAM,CAACtC,KAAK,CAAC4J,UAAU,CAAC,CAACG,SAAS,MAAM/J,KAAK,CAAC+G,KAAK,EAAE;QACtGV,KAAK,EAAE;OACR,CAAC;IACJ;EACF;EAEA;;;EAGA,IAAI2D,aAAaA,CAAA;IACf,IAAI,IAAI,CAAC3M,WAAW,EAAE;MACpB,OAAO,IAAI,CAACF,YAAY;IAC1B;IACA,OAAO,IAAI,CAACA,YAAY,CAACwF,KAAK,CAAC,CAAC,EAAE,IAAI,CAACvF,kBAAkB,CAAC;EAC5D;EAEA;;;EAGA6M,iBAAiBA,CAAA;IACf,IAAI,CAAC5M,WAAW,GAAG,CAAC,IAAI,CAACA,WAAW;EACtC;EAEA;;;EAGA,IAAI6M,WAAWA,CAAA;IACb,OAAO,IAAI,CAAC/M,YAAY,CAACsD,MAAM,GAAG,IAAI,CAACrD,kBAAkB,IAAI,CAAC,IAAI,CAACC,WAAW;EAChF;EAEA;;;EAGQwD,kBAAkBA,CAAA;IACxB;IACA,IAAI,IAAI,CAAC1D,YAAY,CAACsD,MAAM,KAAK,CAAC,EAAE;MAClC,MAAM0J,QAAQ,GAAG,CACf;QAAE/D,OAAO,EAAE,4BAA4B;QAAEC,KAAK,EAAE;MAAS,CAAE,EAC3D;QAAED,OAAO,EAAE,mCAAmC;QAAEC,KAAK,EAAE;MAAS,CAAE,EAClE;QAAED,OAAO,EAAE,yCAAyC;QAAEC,KAAK,EAAE;MAAS,CAAE,EACxE;QAAED,OAAO,EAAE,6BAA6B;QAAEC,KAAK,EAAE;MAAS,CAAE,EAC5D;QAAED,OAAO,EAAE,8BAA8B;QAAEC,KAAK,EAAE;MAAS,CAAE,EAC7D;QAAED,OAAO,EAAE,kCAAkC;QAAEC,KAAK,EAAE;MAAS,CAAE,EACjE;QAAED,OAAO,EAAE,8BAA8B;QAAEC,KAAK,EAAE;MAAS,CAAE,EAC7D;QAAED,OAAO,EAAE,0BAA0B;QAAEC,KAAK,EAAE;MAAS,CAAE,EACzD;QAAED,OAAO,EAAE,6CAA6C;QAAEC,KAAK,EAAE;MAAS,CAAE,EAC5E;QAAED,OAAO,EAAE,sCAAsC;QAAEC,KAAK,EAAE;MAAS,CAAE,EACrE;QAAED,OAAO,EAAE,iCAAiC;QAAEC,KAAK,EAAE;MAAS,CAAE,EAChE;QAAED,OAAO,EAAE,iCAAiC;QAAEC,KAAK,EAAE;MAAS,CAAE,EAChE;QAAED,OAAO,EAAE,8BAA8B;QAAEC,KAAK,EAAE;MAAS,CAAE,EAC7D;QAAED,OAAO,EAAE,iCAAiC;QAAEC,KAAK,EAAE;MAAS,CAAE,EAChE;QAAED,OAAO,EAAE,sCAAsC;QAAEC,KAAK,EAAE;MAAS,CAAE,EACrE;QAAED,OAAO,EAAE,uCAAuC;QAAEC,KAAK,EAAE;MAAS,CAAE,EACtE;QAAED,OAAO,EAAE,gDAAgD;QAAEC,KAAK,EAAE;MAAS,CAAE,EAC/E;QAAED,OAAO,EAAE,qCAAqC;QAAEC,KAAK,EAAE;MAAS,CAAE,EACpE;QAAED,OAAO,EAAE,8BAA8B;QAAEC,KAAK,EAAE;MAAS,CAAE,EAC7D;QAAED,OAAO,EAAE,sCAAsC;QAAEC,KAAK,EAAE;MAAS,CAAE,EACrE;QAAED,OAAO,EAAE,wCAAwC;QAAEC,KAAK,EAAE;MAAS,CAAE,EACvE;QAAED,OAAO,EAAE,0CAA0C;QAAEC,KAAK,EAAE;MAAS,CAAE,EACzE;QAAED,OAAO,EAAE,4CAA4C;QAAEC,KAAK,EAAE;MAAS,CAAE,EAC3E;QAAED,OAAO,EAAE,8BAA8B;QAAEC,KAAK,EAAE;MAAS,CAAE,CAC9D;MAED,IAAI,CAAClJ,YAAY,GAAGgN,QAAQ;IAC9B;EACF;CACD;AAvzBCC,UAAA,EADC9P,SAAS,CAACM,sBAAsB,EAAE;EAAEyP,MAAM,EAAE;AAAK,CAAE,CAAC,C,oEACV;AAbhChP,0BAA0B,GAAA+O,UAAA,EAjBtC/P,SAAS,CAAC;EACTiQ,QAAQ,EAAE,wBAAwB;EAClCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPjQ,YAAY,EACZG,WAAW,EACXE,sBAAsB,EACtBC,sBAAsB,EACtB4P,oBAAoB,EACpBzP,aAAa,EACbF,eAAe,EACfC,aAAa,EACbK,2BAA2B,CAC5B;EACDsP,WAAW,EAAE,qCAAqC;EAClDC,SAAS,EAAE,CAAC,qCAAqC;CAClD,CAAC,C,EACWtP,0BAA0B,CAo0BtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}