{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = this && this.__param || function (paramIndex, decorator) {\n  return function (target, key) {\n    decorator(target, key, paramIndex);\n  };\n};\nimport { $, append, EventHelper } from '../../../base/browser/dom.js';\nimport { DomEmitter } from '../../../base/browser/event.js';\nimport { StandardKeyboardEvent } from '../../../base/browser/keyboardEvent.js';\nimport { EventType as TouchEventType, Gesture } from '../../../base/browser/touch.js';\nimport { Event } from '../../../base/common/event.js';\nimport { Disposable } from '../../../base/common/lifecycle.js';\nimport { IOpenerService } from '../common/opener.js';\nimport './link.css';\nimport { getDefaultHoverDelegate } from '../../../base/browser/ui/hover/hoverDelegateFactory.js';\nimport { IHoverService } from '../../hover/browser/hover.js';\nlet Link = class Link extends Disposable {\n  get enabled() {\n    return this._enabled;\n  }\n  set enabled(enabled) {\n    if (enabled) {\n      this.el.setAttribute('aria-disabled', 'false');\n      this.el.tabIndex = 0;\n      this.el.style.pointerEvents = 'auto';\n      this.el.style.opacity = '1';\n      this.el.style.cursor = 'pointer';\n      this._enabled = false;\n    } else {\n      this.el.setAttribute('aria-disabled', 'true');\n      this.el.tabIndex = -1;\n      this.el.style.pointerEvents = 'none';\n      this.el.style.opacity = '0.4';\n      this.el.style.cursor = 'default';\n      this._enabled = true;\n    }\n    this._enabled = enabled;\n  }\n  constructor(container, _link, options = {}, _hoverService, openerService) {\n    super();\n    this._link = _link;\n    this._hoverService = _hoverService;\n    this._enabled = true;\n    this.el = append(container, $('a.monaco-link', {\n      tabIndex: _link.tabIndex ?? 0,\n      href: _link.href\n    }, _link.label));\n    this.hoverDelegate = options.hoverDelegate ?? getDefaultHoverDelegate('mouse');\n    this.setTooltip(_link.title);\n    this.el.setAttribute('role', 'button');\n    const onClickEmitter = this._register(new DomEmitter(this.el, 'click'));\n    const onKeyPress = this._register(new DomEmitter(this.el, 'keypress'));\n    const onEnterPress = Event.chain(onKeyPress.event, $ => $.map(e => new StandardKeyboardEvent(e)).filter(e => e.keyCode === 3 /* KeyCode.Enter */));\n    const onTap = this._register(new DomEmitter(this.el, TouchEventType.Tap)).event;\n    this._register(Gesture.addTarget(this.el));\n    const onOpen = Event.any(onClickEmitter.event, onEnterPress, onTap);\n    this._register(onOpen(e => {\n      if (!this.enabled) {\n        return;\n      }\n      EventHelper.stop(e, true);\n      if (options?.opener) {\n        options.opener(this._link.href);\n      } else {\n        openerService.open(this._link.href, {\n          allowCommands: true\n        });\n      }\n    }));\n    this.enabled = true;\n  }\n  setTooltip(title) {\n    if (this.hoverDelegate.showNativeHover) {\n      this.el.title = title ?? '';\n    } else if (!this.hover && title) {\n      this.hover = this._register(this._hoverService.setupManagedHover(this.hoverDelegate, this.el, title));\n    } else if (this.hover) {\n      this.hover.update(title);\n    }\n  }\n};\nLink = __decorate([__param(3, IHoverService), __param(4, IOpenerService)], Link);\nexport { Link };", "map": {"version": 3, "names": ["__decorate", "decorators", "target", "key", "desc", "c", "arguments", "length", "r", "Object", "getOwnPropertyDescriptor", "d", "Reflect", "decorate", "i", "defineProperty", "__param", "paramIndex", "decorator", "$", "append", "EventHelper", "DomEmitter", "StandardKeyboardEvent", "EventType", "TouchEventType", "Gesture", "Event", "Disposable", "IOpenerService", "getDefaultHoverDelegate", "IHoverService", "Link", "enabled", "_enabled", "el", "setAttribute", "tabIndex", "style", "pointerEvents", "opacity", "cursor", "constructor", "container", "_link", "options", "_hoverService", "openerService", "href", "label", "hoverDelegate", "setTooltip", "title", "onClickEmitter", "_register", "onKeyPress", "onEnterPress", "chain", "event", "map", "e", "filter", "keyCode", "onTap", "Tap", "addTarget", "onOpen", "any", "stop", "opener", "open", "allowCommands", "showNativeHover", "hover", "setupManagedHover", "update"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/platform/opener/browser/link.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = (this && this.__param) || function (paramIndex, decorator) {\n    return function (target, key) { decorator(target, key, paramIndex); }\n};\nimport { $, append, EventHelper } from '../../../base/browser/dom.js';\nimport { DomEmitter } from '../../../base/browser/event.js';\nimport { StandardKeyboardEvent } from '../../../base/browser/keyboardEvent.js';\nimport { EventType as TouchEventType, Gesture } from '../../../base/browser/touch.js';\nimport { Event } from '../../../base/common/event.js';\nimport { Disposable } from '../../../base/common/lifecycle.js';\nimport { IOpenerService } from '../common/opener.js';\nimport './link.css';\nimport { getDefaultHoverDelegate } from '../../../base/browser/ui/hover/hoverDelegateFactory.js';\nimport { IHoverService } from '../../hover/browser/hover.js';\nlet Link = class Link extends Disposable {\n    get enabled() {\n        return this._enabled;\n    }\n    set enabled(enabled) {\n        if (enabled) {\n            this.el.setAttribute('aria-disabled', 'false');\n            this.el.tabIndex = 0;\n            this.el.style.pointerEvents = 'auto';\n            this.el.style.opacity = '1';\n            this.el.style.cursor = 'pointer';\n            this._enabled = false;\n        }\n        else {\n            this.el.setAttribute('aria-disabled', 'true');\n            this.el.tabIndex = -1;\n            this.el.style.pointerEvents = 'none';\n            this.el.style.opacity = '0.4';\n            this.el.style.cursor = 'default';\n            this._enabled = true;\n        }\n        this._enabled = enabled;\n    }\n    constructor(container, _link, options = {}, _hoverService, openerService) {\n        super();\n        this._link = _link;\n        this._hoverService = _hoverService;\n        this._enabled = true;\n        this.el = append(container, $('a.monaco-link', {\n            tabIndex: _link.tabIndex ?? 0,\n            href: _link.href,\n        }, _link.label));\n        this.hoverDelegate = options.hoverDelegate ?? getDefaultHoverDelegate('mouse');\n        this.setTooltip(_link.title);\n        this.el.setAttribute('role', 'button');\n        const onClickEmitter = this._register(new DomEmitter(this.el, 'click'));\n        const onKeyPress = this._register(new DomEmitter(this.el, 'keypress'));\n        const onEnterPress = Event.chain(onKeyPress.event, $ => $.map(e => new StandardKeyboardEvent(e))\n            .filter(e => e.keyCode === 3 /* KeyCode.Enter */));\n        const onTap = this._register(new DomEmitter(this.el, TouchEventType.Tap)).event;\n        this._register(Gesture.addTarget(this.el));\n        const onOpen = Event.any(onClickEmitter.event, onEnterPress, onTap);\n        this._register(onOpen(e => {\n            if (!this.enabled) {\n                return;\n            }\n            EventHelper.stop(e, true);\n            if (options?.opener) {\n                options.opener(this._link.href);\n            }\n            else {\n                openerService.open(this._link.href, { allowCommands: true });\n            }\n        }));\n        this.enabled = true;\n    }\n    setTooltip(title) {\n        if (this.hoverDelegate.showNativeHover) {\n            this.el.title = title ?? '';\n        }\n        else if (!this.hover && title) {\n            this.hover = this._register(this._hoverService.setupManagedHover(this.hoverDelegate, this.el, title));\n        }\n        else if (this.hover) {\n            this.hover.update(title);\n        }\n    }\n};\nLink = __decorate([\n    __param(3, IHoverService),\n    __param(4, IOpenerService)\n], Link);\nexport { Link };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,IAAIA,UAAU,GAAI,IAAI,IAAI,IAAI,CAACA,UAAU,IAAK,UAAUC,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAE;EACnF,IAAIC,CAAC,GAAGC,SAAS,CAACC,MAAM;IAAEC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGH,MAAM,GAAGE,IAAI,KAAK,IAAI,GAAGA,IAAI,GAAGK,MAAM,CAACC,wBAAwB,CAACR,MAAM,EAAEC,GAAG,CAAC,GAAGC,IAAI;IAAEO,CAAC;EAC5H,IAAI,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,CAACC,QAAQ,KAAK,UAAU,EAAEL,CAAC,GAAGI,OAAO,CAACC,QAAQ,CAACZ,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,CAAC,CAAC,KAC1H,KAAK,IAAIU,CAAC,GAAGb,UAAU,CAACM,MAAM,GAAG,CAAC,EAAEO,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE,IAAIH,CAAC,GAAGV,UAAU,CAACa,CAAC,CAAC,EAAEN,CAAC,GAAG,CAACH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACH,CAAC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACT,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,GAAGG,CAAC,CAACT,MAAM,EAAEC,GAAG,CAAC,KAAKK,CAAC;EACjJ,OAAOH,CAAC,GAAG,CAAC,IAAIG,CAAC,IAAIC,MAAM,CAACM,cAAc,CAACb,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,EAAEA,CAAC;AACjE,CAAC;AACD,IAAIQ,OAAO,GAAI,IAAI,IAAI,IAAI,CAACA,OAAO,IAAK,UAAUC,UAAU,EAAEC,SAAS,EAAE;EACrE,OAAO,UAAUhB,MAAM,EAAEC,GAAG,EAAE;IAAEe,SAAS,CAAChB,MAAM,EAAEC,GAAG,EAAEc,UAAU,CAAC;EAAE,CAAC;AACzE,CAAC;AACD,SAASE,CAAC,EAAEC,MAAM,EAAEC,WAAW,QAAQ,8BAA8B;AACrE,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,qBAAqB,QAAQ,wCAAwC;AAC9E,SAASC,SAAS,IAAIC,cAAc,EAAEC,OAAO,QAAQ,gCAAgC;AACrF,SAASC,KAAK,QAAQ,+BAA+B;AACrD,SAASC,UAAU,QAAQ,mCAAmC;AAC9D,SAASC,cAAc,QAAQ,qBAAqB;AACpD,OAAO,YAAY;AACnB,SAASC,uBAAuB,QAAQ,wDAAwD;AAChG,SAASC,aAAa,QAAQ,8BAA8B;AAC5D,IAAIC,IAAI,GAAG,MAAMA,IAAI,SAASJ,UAAU,CAAC;EACrC,IAAIK,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACC,QAAQ;EACxB;EACA,IAAID,OAAOA,CAACA,OAAO,EAAE;IACjB,IAAIA,OAAO,EAAE;MACT,IAAI,CAACE,EAAE,CAACC,YAAY,CAAC,eAAe,EAAE,OAAO,CAAC;MAC9C,IAAI,CAACD,EAAE,CAACE,QAAQ,GAAG,CAAC;MACpB,IAAI,CAACF,EAAE,CAACG,KAAK,CAACC,aAAa,GAAG,MAAM;MACpC,IAAI,CAACJ,EAAE,CAACG,KAAK,CAACE,OAAO,GAAG,GAAG;MAC3B,IAAI,CAACL,EAAE,CAACG,KAAK,CAACG,MAAM,GAAG,SAAS;MAChC,IAAI,CAACP,QAAQ,GAAG,KAAK;IACzB,CAAC,MACI;MACD,IAAI,CAACC,EAAE,CAACC,YAAY,CAAC,eAAe,EAAE,MAAM,CAAC;MAC7C,IAAI,CAACD,EAAE,CAACE,QAAQ,GAAG,CAAC,CAAC;MACrB,IAAI,CAACF,EAAE,CAACG,KAAK,CAACC,aAAa,GAAG,MAAM;MACpC,IAAI,CAACJ,EAAE,CAACG,KAAK,CAACE,OAAO,GAAG,KAAK;MAC7B,IAAI,CAACL,EAAE,CAACG,KAAK,CAACG,MAAM,GAAG,SAAS;MAChC,IAAI,CAACP,QAAQ,GAAG,IAAI;IACxB;IACA,IAAI,CAACA,QAAQ,GAAGD,OAAO;EAC3B;EACAS,WAAWA,CAACC,SAAS,EAAEC,KAAK,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAEC,aAAa,EAAEC,aAAa,EAAE;IACtE,KAAK,CAAC,CAAC;IACP,IAAI,CAACH,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACE,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACZ,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,EAAE,GAAGf,MAAM,CAACuB,SAAS,EAAExB,CAAC,CAAC,eAAe,EAAE;MAC3CkB,QAAQ,EAAEO,KAAK,CAACP,QAAQ,IAAI,CAAC;MAC7BW,IAAI,EAAEJ,KAAK,CAACI;IAChB,CAAC,EAAEJ,KAAK,CAACK,KAAK,CAAC,CAAC;IAChB,IAAI,CAACC,aAAa,GAAGL,OAAO,CAACK,aAAa,IAAIpB,uBAAuB,CAAC,OAAO,CAAC;IAC9E,IAAI,CAACqB,UAAU,CAACP,KAAK,CAACQ,KAAK,CAAC;IAC5B,IAAI,CAACjB,EAAE,CAACC,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC;IACtC,MAAMiB,cAAc,GAAG,IAAI,CAACC,SAAS,CAAC,IAAIhC,UAAU,CAAC,IAAI,CAACa,EAAE,EAAE,OAAO,CAAC,CAAC;IACvE,MAAMoB,UAAU,GAAG,IAAI,CAACD,SAAS,CAAC,IAAIhC,UAAU,CAAC,IAAI,CAACa,EAAE,EAAE,UAAU,CAAC,CAAC;IACtE,MAAMqB,YAAY,GAAG7B,KAAK,CAAC8B,KAAK,CAACF,UAAU,CAACG,KAAK,EAAEvC,CAAC,IAAIA,CAAC,CAACwC,GAAG,CAACC,CAAC,IAAI,IAAIrC,qBAAqB,CAACqC,CAAC,CAAC,CAAC,CAC3FC,MAAM,CAACD,CAAC,IAAIA,CAAC,CAACE,OAAO,KAAK,CAAC,CAAC,mBAAmB,CAAC,CAAC;IACtD,MAAMC,KAAK,GAAG,IAAI,CAACT,SAAS,CAAC,IAAIhC,UAAU,CAAC,IAAI,CAACa,EAAE,EAAEV,cAAc,CAACuC,GAAG,CAAC,CAAC,CAACN,KAAK;IAC/E,IAAI,CAACJ,SAAS,CAAC5B,OAAO,CAACuC,SAAS,CAAC,IAAI,CAAC9B,EAAE,CAAC,CAAC;IAC1C,MAAM+B,MAAM,GAAGvC,KAAK,CAACwC,GAAG,CAACd,cAAc,CAACK,KAAK,EAAEF,YAAY,EAAEO,KAAK,CAAC;IACnE,IAAI,CAACT,SAAS,CAACY,MAAM,CAACN,CAAC,IAAI;MACvB,IAAI,CAAC,IAAI,CAAC3B,OAAO,EAAE;QACf;MACJ;MACAZ,WAAW,CAAC+C,IAAI,CAACR,CAAC,EAAE,IAAI,CAAC;MACzB,IAAIf,OAAO,EAAEwB,MAAM,EAAE;QACjBxB,OAAO,CAACwB,MAAM,CAAC,IAAI,CAACzB,KAAK,CAACI,IAAI,CAAC;MACnC,CAAC,MACI;QACDD,aAAa,CAACuB,IAAI,CAAC,IAAI,CAAC1B,KAAK,CAACI,IAAI,EAAE;UAAEuB,aAAa,EAAE;QAAK,CAAC,CAAC;MAChE;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,CAACtC,OAAO,GAAG,IAAI;EACvB;EACAkB,UAAUA,CAACC,KAAK,EAAE;IACd,IAAI,IAAI,CAACF,aAAa,CAACsB,eAAe,EAAE;MACpC,IAAI,CAACrC,EAAE,CAACiB,KAAK,GAAGA,KAAK,IAAI,EAAE;IAC/B,CAAC,MACI,IAAI,CAAC,IAAI,CAACqB,KAAK,IAAIrB,KAAK,EAAE;MAC3B,IAAI,CAACqB,KAAK,GAAG,IAAI,CAACnB,SAAS,CAAC,IAAI,CAACR,aAAa,CAAC4B,iBAAiB,CAAC,IAAI,CAACxB,aAAa,EAAE,IAAI,CAACf,EAAE,EAAEiB,KAAK,CAAC,CAAC;IACzG,CAAC,MACI,IAAI,IAAI,CAACqB,KAAK,EAAE;MACjB,IAAI,CAACA,KAAK,CAACE,MAAM,CAACvB,KAAK,CAAC;IAC5B;EACJ;AACJ,CAAC;AACDpB,IAAI,GAAGhC,UAAU,CAAC,CACdgB,OAAO,CAAC,CAAC,EAAEe,aAAa,CAAC,EACzBf,OAAO,CAAC,CAAC,EAAEa,cAAc,CAAC,CAC7B,EAAEG,IAAI,CAAC;AACR,SAASA,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}