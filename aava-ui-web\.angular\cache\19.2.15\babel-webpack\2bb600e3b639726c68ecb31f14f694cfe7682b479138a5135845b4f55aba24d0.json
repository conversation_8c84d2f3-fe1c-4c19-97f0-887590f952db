{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n/**\n * A very VM friendly rgba datastructure.\n * Please don't touch unless you take a look at the IR.\n */\nexport class RGBA8 {\n  static {\n    this.Empty = new RGBA8(0, 0, 0, 0);\n  }\n  constructor(r, g, b, a) {\n    this._rgba8Brand = undefined;\n    this.r = RGBA8._clamp(r);\n    this.g = RGBA8._clamp(g);\n    this.b = RGBA8._clamp(b);\n    this.a = RGBA8._clamp(a);\n  }\n  equals(other) {\n    return this.r === other.r && this.g === other.g && this.b === other.b && this.a === other.a;\n  }\n  static _clamp(c) {\n    if (c < 0) {\n      return 0;\n    }\n    if (c > 255) {\n      return 255;\n    }\n    return c | 0;\n  }\n}", "map": {"version": 3, "names": ["RGBA8", "Empty", "constructor", "r", "g", "b", "a", "_rgba8Brand", "undefined", "_clamp", "equals", "other", "c"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/core/rgba.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n/**\n * A very VM friendly rgba datastructure.\n * Please don't touch unless you take a look at the IR.\n */\nexport class RGBA8 {\n    static { this.Empty = new RGBA8(0, 0, 0, 0); }\n    constructor(r, g, b, a) {\n        this._rgba8Brand = undefined;\n        this.r = RGBA8._clamp(r);\n        this.g = RGBA8._clamp(g);\n        this.b = RGBA8._clamp(b);\n        this.a = RGBA8._clamp(a);\n    }\n    equals(other) {\n        return (this.r === other.r\n            && this.g === other.g\n            && this.b === other.b\n            && this.a === other.a);\n    }\n    static _clamp(c) {\n        if (c < 0) {\n            return 0;\n        }\n        if (c > 255) {\n            return 255;\n        }\n        return c | 0;\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,KAAK,CAAC;EACf;IAAS,IAAI,CAACC,KAAK,GAAG,IAAID,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAAE;EAC7CE,WAAWA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;IACpB,IAAI,CAACC,WAAW,GAAGC,SAAS;IAC5B,IAAI,CAACL,CAAC,GAAGH,KAAK,CAACS,MAAM,CAACN,CAAC,CAAC;IACxB,IAAI,CAACC,CAAC,GAAGJ,KAAK,CAACS,MAAM,CAACL,CAAC,CAAC;IACxB,IAAI,CAACC,CAAC,GAAGL,KAAK,CAACS,MAAM,CAACJ,CAAC,CAAC;IACxB,IAAI,CAACC,CAAC,GAAGN,KAAK,CAACS,MAAM,CAACH,CAAC,CAAC;EAC5B;EACAI,MAAMA,CAACC,KAAK,EAAE;IACV,OAAQ,IAAI,CAACR,CAAC,KAAKQ,KAAK,CAACR,CAAC,IACnB,IAAI,CAACC,CAAC,KAAKO,KAAK,CAACP,CAAC,IAClB,IAAI,CAACC,CAAC,KAAKM,KAAK,CAACN,CAAC,IAClB,IAAI,CAACC,CAAC,KAAKK,KAAK,CAACL,CAAC;EAC7B;EACA,OAAOG,MAAMA,CAACG,CAAC,EAAE;IACb,IAAIA,CAAC,GAAG,CAAC,EAAE;MACP,OAAO,CAAC;IACZ;IACA,IAAIA,CAAC,GAAG,GAAG,EAAE;MACT,OAAO,GAAG;IACd;IACA,OAAOA,CAAC,GAAG,CAAC;EAChB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}