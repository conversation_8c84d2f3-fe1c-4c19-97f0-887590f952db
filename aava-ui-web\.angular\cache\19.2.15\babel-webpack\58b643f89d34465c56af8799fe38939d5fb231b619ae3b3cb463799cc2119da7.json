{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as dom from '../dom.js';\nimport { StandardKeyboardEvent } from '../keyboardEvent.js';\nimport { StandardMouseEvent } from '../mouseEvent.js';\nimport { Gesture } from '../touch.js';\nimport { Disposable } from '../../common/lifecycle.js';\nexport class Widget extends Disposable {\n  onclick(domNode, listener) {\n    this._register(dom.addDisposableListener(domNode, dom.EventType.CLICK, e => listener(new StandardMouseEvent(dom.getWindow(domNode), e))));\n  }\n  onmousedown(domNode, listener) {\n    this._register(dom.addDisposableListener(domNode, dom.EventType.MOUSE_DOWN, e => listener(new StandardMouseEvent(dom.getWindow(domNode), e))));\n  }\n  onmouseover(domNode, listener) {\n    this._register(dom.addDisposableListener(domNode, dom.EventType.MOUSE_OVER, e => listener(new StandardMouseEvent(dom.getWindow(domNode), e))));\n  }\n  onmouseleave(domNode, listener) {\n    this._register(dom.addDisposableListener(domNode, dom.EventType.MOUSE_LEAVE, e => listener(new StandardMouseEvent(dom.getWindow(domNode), e))));\n  }\n  onkeydown(domNode, listener) {\n    this._register(dom.addDisposableListener(domNode, dom.EventType.KEY_DOWN, e => listener(new StandardKeyboardEvent(e))));\n  }\n  onkeyup(domNode, listener) {\n    this._register(dom.addDisposableListener(domNode, dom.EventType.KEY_UP, e => listener(new StandardKeyboardEvent(e))));\n  }\n  oninput(domNode, listener) {\n    this._register(dom.addDisposableListener(domNode, dom.EventType.INPUT, listener));\n  }\n  onblur(domNode, listener) {\n    this._register(dom.addDisposableListener(domNode, dom.EventType.BLUR, listener));\n  }\n  onfocus(domNode, listener) {\n    this._register(dom.addDisposableListener(domNode, dom.EventType.FOCUS, listener));\n  }\n  ignoreGesture(domNode) {\n    return Gesture.ignoreTarget(domNode);\n  }\n}", "map": {"version": 3, "names": ["dom", "StandardKeyboardEvent", "StandardMouseEvent", "Gesture", "Disposable", "Widget", "onclick", "domNode", "listener", "_register", "addDisposableListener", "EventType", "CLICK", "e", "getWindow", "onmousedown", "MOUSE_DOWN", "on<PERSON><PERSON>ver", "MOUSE_OVER", "onmouseleave", "MOUSE_LEAVE", "onkeydown", "KEY_DOWN", "onkeyup", "KEY_UP", "oninput", "INPUT", "onblur", "BLUR", "onfocus", "FOCUS", "ignoreGesture", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/browser/ui/widget.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as dom from '../dom.js';\nimport { StandardKeyboardEvent } from '../keyboardEvent.js';\nimport { StandardMouseEvent } from '../mouseEvent.js';\nimport { Gesture } from '../touch.js';\nimport { Disposable } from '../../common/lifecycle.js';\nexport class Widget extends Disposable {\n    onclick(domNode, listener) {\n        this._register(dom.addDisposableListener(domNode, dom.EventType.CLICK, (e) => listener(new StandardMouseEvent(dom.getWindow(domNode), e))));\n    }\n    onmousedown(domNode, listener) {\n        this._register(dom.addDisposableListener(domNode, dom.EventType.MOUSE_DOWN, (e) => listener(new StandardMouseEvent(dom.getWindow(domNode), e))));\n    }\n    onmouseover(domNode, listener) {\n        this._register(dom.addDisposableListener(domNode, dom.EventType.MOUSE_OVER, (e) => listener(new StandardMouseEvent(dom.getWindow(domNode), e))));\n    }\n    onmouseleave(domNode, listener) {\n        this._register(dom.addDisposableListener(domNode, dom.EventType.MOUSE_LEAVE, (e) => listener(new StandardMouseEvent(dom.getWindow(domNode), e))));\n    }\n    onkeydown(domNode, listener) {\n        this._register(dom.addDisposableListener(domNode, dom.EventType.KEY_DOWN, (e) => listener(new StandardKeyboardEvent(e))));\n    }\n    onkeyup(domNode, listener) {\n        this._register(dom.addDisposableListener(domNode, dom.EventType.KEY_UP, (e) => listener(new StandardKeyboardEvent(e))));\n    }\n    oninput(domNode, listener) {\n        this._register(dom.addDisposableListener(domNode, dom.EventType.INPUT, listener));\n    }\n    onblur(domNode, listener) {\n        this._register(dom.addDisposableListener(domNode, dom.EventType.BLUR, listener));\n    }\n    onfocus(domNode, listener) {\n        this._register(dom.addDisposableListener(domNode, dom.EventType.FOCUS, listener));\n    }\n    ignoreGesture(domNode) {\n        return Gesture.ignoreTarget(domNode);\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,KAAKA,GAAG,MAAM,WAAW;AAChC,SAASC,qBAAqB,QAAQ,qBAAqB;AAC3D,SAASC,kBAAkB,QAAQ,kBAAkB;AACrD,SAASC,OAAO,QAAQ,aAAa;AACrC,SAASC,UAAU,QAAQ,2BAA2B;AACtD,OAAO,MAAMC,MAAM,SAASD,UAAU,CAAC;EACnCE,OAAOA,CAACC,OAAO,EAAEC,QAAQ,EAAE;IACvB,IAAI,CAACC,SAAS,CAACT,GAAG,CAACU,qBAAqB,CAACH,OAAO,EAAEP,GAAG,CAACW,SAAS,CAACC,KAAK,EAAGC,CAAC,IAAKL,QAAQ,CAAC,IAAIN,kBAAkB,CAACF,GAAG,CAACc,SAAS,CAACP,OAAO,CAAC,EAAEM,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/I;EACAE,WAAWA,CAACR,OAAO,EAAEC,QAAQ,EAAE;IAC3B,IAAI,CAACC,SAAS,CAACT,GAAG,CAACU,qBAAqB,CAACH,OAAO,EAAEP,GAAG,CAACW,SAAS,CAACK,UAAU,EAAGH,CAAC,IAAKL,QAAQ,CAAC,IAAIN,kBAAkB,CAACF,GAAG,CAACc,SAAS,CAACP,OAAO,CAAC,EAAEM,CAAC,CAAC,CAAC,CAAC,CAAC;EACpJ;EACAI,WAAWA,CAACV,OAAO,EAAEC,QAAQ,EAAE;IAC3B,IAAI,CAACC,SAAS,CAACT,GAAG,CAACU,qBAAqB,CAACH,OAAO,EAAEP,GAAG,CAACW,SAAS,CAACO,UAAU,EAAGL,CAAC,IAAKL,QAAQ,CAAC,IAAIN,kBAAkB,CAACF,GAAG,CAACc,SAAS,CAACP,OAAO,CAAC,EAAEM,CAAC,CAAC,CAAC,CAAC,CAAC;EACpJ;EACAM,YAAYA,CAACZ,OAAO,EAAEC,QAAQ,EAAE;IAC5B,IAAI,CAACC,SAAS,CAACT,GAAG,CAACU,qBAAqB,CAACH,OAAO,EAAEP,GAAG,CAACW,SAAS,CAACS,WAAW,EAAGP,CAAC,IAAKL,QAAQ,CAAC,IAAIN,kBAAkB,CAACF,GAAG,CAACc,SAAS,CAACP,OAAO,CAAC,EAAEM,CAAC,CAAC,CAAC,CAAC,CAAC;EACrJ;EACAQ,SAASA,CAACd,OAAO,EAAEC,QAAQ,EAAE;IACzB,IAAI,CAACC,SAAS,CAACT,GAAG,CAACU,qBAAqB,CAACH,OAAO,EAAEP,GAAG,CAACW,SAAS,CAACW,QAAQ,EAAGT,CAAC,IAAKL,QAAQ,CAAC,IAAIP,qBAAqB,CAACY,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7H;EACAU,OAAOA,CAAChB,OAAO,EAAEC,QAAQ,EAAE;IACvB,IAAI,CAACC,SAAS,CAACT,GAAG,CAACU,qBAAqB,CAACH,OAAO,EAAEP,GAAG,CAACW,SAAS,CAACa,MAAM,EAAGX,CAAC,IAAKL,QAAQ,CAAC,IAAIP,qBAAqB,CAACY,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3H;EACAY,OAAOA,CAAClB,OAAO,EAAEC,QAAQ,EAAE;IACvB,IAAI,CAACC,SAAS,CAACT,GAAG,CAACU,qBAAqB,CAACH,OAAO,EAAEP,GAAG,CAACW,SAAS,CAACe,KAAK,EAAElB,QAAQ,CAAC,CAAC;EACrF;EACAmB,MAAMA,CAACpB,OAAO,EAAEC,QAAQ,EAAE;IACtB,IAAI,CAACC,SAAS,CAACT,GAAG,CAACU,qBAAqB,CAACH,OAAO,EAAEP,GAAG,CAACW,SAAS,CAACiB,IAAI,EAAEpB,QAAQ,CAAC,CAAC;EACpF;EACAqB,OAAOA,CAACtB,OAAO,EAAEC,QAAQ,EAAE;IACvB,IAAI,CAACC,SAAS,CAACT,GAAG,CAACU,qBAAqB,CAACH,OAAO,EAAEP,GAAG,CAACW,SAAS,CAACmB,KAAK,EAAEtB,QAAQ,CAAC,CAAC;EACrF;EACAuB,aAAaA,CAACxB,OAAO,EAAE;IACnB,OAAOJ,OAAO,CAAC6B,YAAY,CAACzB,OAAO,CAAC;EACxC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}