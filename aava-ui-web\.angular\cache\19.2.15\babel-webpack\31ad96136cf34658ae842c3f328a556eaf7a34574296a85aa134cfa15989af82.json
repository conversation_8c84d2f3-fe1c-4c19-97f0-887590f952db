{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { ResizableHTMLElement } from '../../../../base/browser/ui/resizable/resizable.js';\nimport { Disposable } from '../../../../base/common/lifecycle.js';\nimport { Position } from '../../../common/core/position.js';\nimport * as dom from '../../../../base/browser/dom.js';\nconst TOP_HEIGHT = 30;\nconst BOTTOM_HEIGHT = 24;\nexport class ResizableContentWidget extends Disposable {\n  constructor(_editor, minimumSize = new dom.Dimension(10, 10)) {\n    super();\n    this._editor = _editor;\n    this.allowEditorOverflow = true;\n    this.suppressMouseDown = false;\n    this._resizableNode = this._register(new ResizableHTMLElement());\n    this._contentPosition = null;\n    this._isResizing = false;\n    this._resizableNode.domNode.style.position = 'absolute';\n    this._resizableNode.minSize = dom.Dimension.lift(minimumSize);\n    this._resizableNode.layout(minimumSize.height, minimumSize.width);\n    this._resizableNode.enableSashes(true, true, true, true);\n    this._register(this._resizableNode.onDidResize(e => {\n      this._resize(new dom.Dimension(e.dimension.width, e.dimension.height));\n      if (e.done) {\n        this._isResizing = false;\n      }\n    }));\n    this._register(this._resizableNode.onDidWillResize(() => {\n      this._isResizing = true;\n    }));\n  }\n  get isResizing() {\n    return this._isResizing;\n  }\n  getDomNode() {\n    return this._resizableNode.domNode;\n  }\n  getPosition() {\n    return this._contentPosition;\n  }\n  get position() {\n    return this._contentPosition?.position ? Position.lift(this._contentPosition.position) : undefined;\n  }\n  _availableVerticalSpaceAbove(position) {\n    const editorDomNode = this._editor.getDomNode();\n    const mouseBox = this._editor.getScrolledVisiblePosition(position);\n    if (!editorDomNode || !mouseBox) {\n      return;\n    }\n    const editorBox = dom.getDomNodePagePosition(editorDomNode);\n    return editorBox.top + mouseBox.top - TOP_HEIGHT;\n  }\n  _availableVerticalSpaceBelow(position) {\n    const editorDomNode = this._editor.getDomNode();\n    const mouseBox = this._editor.getScrolledVisiblePosition(position);\n    if (!editorDomNode || !mouseBox) {\n      return;\n    }\n    const editorBox = dom.getDomNodePagePosition(editorDomNode);\n    const bodyBox = dom.getClientArea(editorDomNode.ownerDocument.body);\n    const mouseBottom = editorBox.top + mouseBox.top + mouseBox.height;\n    return bodyBox.height - mouseBottom - BOTTOM_HEIGHT;\n  }\n  _findPositionPreference(widgetHeight, showAtPosition) {\n    const maxHeightBelow = Math.min(this._availableVerticalSpaceBelow(showAtPosition) ?? Infinity, widgetHeight);\n    const maxHeightAbove = Math.min(this._availableVerticalSpaceAbove(showAtPosition) ?? Infinity, widgetHeight);\n    const maxHeight = Math.min(Math.max(maxHeightAbove, maxHeightBelow), widgetHeight);\n    const height = Math.min(widgetHeight, maxHeight);\n    let renderingAbove;\n    if (this._editor.getOption(60 /* EditorOption.hover */).above) {\n      renderingAbove = height <= maxHeightAbove ? 1 /* ContentWidgetPositionPreference.ABOVE */ : 2 /* ContentWidgetPositionPreference.BELOW */;\n    } else {\n      renderingAbove = height <= maxHeightBelow ? 2 /* ContentWidgetPositionPreference.BELOW */ : 1 /* ContentWidgetPositionPreference.ABOVE */;\n    }\n    if (renderingAbove === 1 /* ContentWidgetPositionPreference.ABOVE */) {\n      this._resizableNode.enableSashes(true, true, false, false);\n    } else {\n      this._resizableNode.enableSashes(false, true, true, false);\n    }\n    return renderingAbove;\n  }\n  _resize(dimension) {\n    this._resizableNode.layout(dimension.height, dimension.width);\n  }\n}", "map": {"version": 3, "names": ["ResizableHTMLElement", "Disposable", "Position", "dom", "TOP_HEIGHT", "BOTTOM_HEIGHT", "ResizableContentWidget", "constructor", "_editor", "minimumSize", "Dimension", "allowEditorOverflow", "suppressMouseDown", "_resizableNode", "_register", "_contentPosition", "_isResizing", "domNode", "style", "position", "minSize", "lift", "layout", "height", "width", "enableSashes", "onDidResize", "e", "_resize", "dimension", "done", "onDidWillResize", "isResizing", "getDomNode", "getPosition", "undefined", "_availableVerticalSpaceAbove", "editorDomNode", "mouseBox", "getScrolledVisiblePosition", "editorBox", "getDomNodePagePosition", "top", "_availableVerticalSpaceBelow", "bodyBox", "getClientArea", "ownerDocument", "body", "mouseBottom", "_findPositionPreference", "widgetHeight", "showAtPosition", "maxHeightBelow", "Math", "min", "Infinity", "maxHeightAbove", "maxHeight", "max", "renderingAbove", "getOption", "above"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/contrib/hover/browser/resizableContentWidget.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { ResizableHTMLElement } from '../../../../base/browser/ui/resizable/resizable.js';\nimport { Disposable } from '../../../../base/common/lifecycle.js';\nimport { Position } from '../../../common/core/position.js';\nimport * as dom from '../../../../base/browser/dom.js';\nconst TOP_HEIGHT = 30;\nconst BOTTOM_HEIGHT = 24;\nexport class ResizableContentWidget extends Disposable {\n    constructor(_editor, minimumSize = new dom.Dimension(10, 10)) {\n        super();\n        this._editor = _editor;\n        this.allowEditorOverflow = true;\n        this.suppressMouseDown = false;\n        this._resizableNode = this._register(new ResizableHTMLElement());\n        this._contentPosition = null;\n        this._isResizing = false;\n        this._resizableNode.domNode.style.position = 'absolute';\n        this._resizableNode.minSize = dom.Dimension.lift(minimumSize);\n        this._resizableNode.layout(minimumSize.height, minimumSize.width);\n        this._resizableNode.enableSashes(true, true, true, true);\n        this._register(this._resizableNode.onDidResize(e => {\n            this._resize(new dom.Dimension(e.dimension.width, e.dimension.height));\n            if (e.done) {\n                this._isResizing = false;\n            }\n        }));\n        this._register(this._resizableNode.onDidWillResize(() => {\n            this._isResizing = true;\n        }));\n    }\n    get isResizing() {\n        return this._isResizing;\n    }\n    getDomNode() {\n        return this._resizableNode.domNode;\n    }\n    getPosition() {\n        return this._contentPosition;\n    }\n    get position() {\n        return this._contentPosition?.position ? Position.lift(this._contentPosition.position) : undefined;\n    }\n    _availableVerticalSpaceAbove(position) {\n        const editorDomNode = this._editor.getDomNode();\n        const mouseBox = this._editor.getScrolledVisiblePosition(position);\n        if (!editorDomNode || !mouseBox) {\n            return;\n        }\n        const editorBox = dom.getDomNodePagePosition(editorDomNode);\n        return editorBox.top + mouseBox.top - TOP_HEIGHT;\n    }\n    _availableVerticalSpaceBelow(position) {\n        const editorDomNode = this._editor.getDomNode();\n        const mouseBox = this._editor.getScrolledVisiblePosition(position);\n        if (!editorDomNode || !mouseBox) {\n            return;\n        }\n        const editorBox = dom.getDomNodePagePosition(editorDomNode);\n        const bodyBox = dom.getClientArea(editorDomNode.ownerDocument.body);\n        const mouseBottom = editorBox.top + mouseBox.top + mouseBox.height;\n        return bodyBox.height - mouseBottom - BOTTOM_HEIGHT;\n    }\n    _findPositionPreference(widgetHeight, showAtPosition) {\n        const maxHeightBelow = Math.min(this._availableVerticalSpaceBelow(showAtPosition) ?? Infinity, widgetHeight);\n        const maxHeightAbove = Math.min(this._availableVerticalSpaceAbove(showAtPosition) ?? Infinity, widgetHeight);\n        const maxHeight = Math.min(Math.max(maxHeightAbove, maxHeightBelow), widgetHeight);\n        const height = Math.min(widgetHeight, maxHeight);\n        let renderingAbove;\n        if (this._editor.getOption(60 /* EditorOption.hover */).above) {\n            renderingAbove = height <= maxHeightAbove ? 1 /* ContentWidgetPositionPreference.ABOVE */ : 2 /* ContentWidgetPositionPreference.BELOW */;\n        }\n        else {\n            renderingAbove = height <= maxHeightBelow ? 2 /* ContentWidgetPositionPreference.BELOW */ : 1 /* ContentWidgetPositionPreference.ABOVE */;\n        }\n        if (renderingAbove === 1 /* ContentWidgetPositionPreference.ABOVE */) {\n            this._resizableNode.enableSashes(true, true, false, false);\n        }\n        else {\n            this._resizableNode.enableSashes(false, true, true, false);\n        }\n        return renderingAbove;\n    }\n    _resize(dimension) {\n        this._resizableNode.layout(dimension.height, dimension.width);\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,oBAAoB,QAAQ,oDAAoD;AACzF,SAASC,UAAU,QAAQ,sCAAsC;AACjE,SAASC,QAAQ,QAAQ,kCAAkC;AAC3D,OAAO,KAAKC,GAAG,MAAM,iCAAiC;AACtD,MAAMC,UAAU,GAAG,EAAE;AACrB,MAAMC,aAAa,GAAG,EAAE;AACxB,OAAO,MAAMC,sBAAsB,SAASL,UAAU,CAAC;EACnDM,WAAWA,CAACC,OAAO,EAAEC,WAAW,GAAG,IAAIN,GAAG,CAACO,SAAS,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE;IAC1D,KAAK,CAAC,CAAC;IACP,IAAI,CAACF,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACG,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACC,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACC,cAAc,GAAG,IAAI,CAACC,SAAS,CAAC,IAAId,oBAAoB,CAAC,CAAC,CAAC;IAChE,IAAI,CAACe,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACH,cAAc,CAACI,OAAO,CAACC,KAAK,CAACC,QAAQ,GAAG,UAAU;IACvD,IAAI,CAACN,cAAc,CAACO,OAAO,GAAGjB,GAAG,CAACO,SAAS,CAACW,IAAI,CAACZ,WAAW,CAAC;IAC7D,IAAI,CAACI,cAAc,CAACS,MAAM,CAACb,WAAW,CAACc,MAAM,EAAEd,WAAW,CAACe,KAAK,CAAC;IACjE,IAAI,CAACX,cAAc,CAACY,YAAY,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACxD,IAAI,CAACX,SAAS,CAAC,IAAI,CAACD,cAAc,CAACa,WAAW,CAACC,CAAC,IAAI;MAChD,IAAI,CAACC,OAAO,CAAC,IAAIzB,GAAG,CAACO,SAAS,CAACiB,CAAC,CAACE,SAAS,CAACL,KAAK,EAAEG,CAAC,CAACE,SAAS,CAACN,MAAM,CAAC,CAAC;MACtE,IAAII,CAAC,CAACG,IAAI,EAAE;QACR,IAAI,CAACd,WAAW,GAAG,KAAK;MAC5B;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,CAACF,SAAS,CAAC,IAAI,CAACD,cAAc,CAACkB,eAAe,CAAC,MAAM;MACrD,IAAI,CAACf,WAAW,GAAG,IAAI;IAC3B,CAAC,CAAC,CAAC;EACP;EACA,IAAIgB,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAAChB,WAAW;EAC3B;EACAiB,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAACpB,cAAc,CAACI,OAAO;EACtC;EACAiB,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACnB,gBAAgB;EAChC;EACA,IAAII,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACJ,gBAAgB,EAAEI,QAAQ,GAAGjB,QAAQ,CAACmB,IAAI,CAAC,IAAI,CAACN,gBAAgB,CAACI,QAAQ,CAAC,GAAGgB,SAAS;EACtG;EACAC,4BAA4BA,CAACjB,QAAQ,EAAE;IACnC,MAAMkB,aAAa,GAAG,IAAI,CAAC7B,OAAO,CAACyB,UAAU,CAAC,CAAC;IAC/C,MAAMK,QAAQ,GAAG,IAAI,CAAC9B,OAAO,CAAC+B,0BAA0B,CAACpB,QAAQ,CAAC;IAClE,IAAI,CAACkB,aAAa,IAAI,CAACC,QAAQ,EAAE;MAC7B;IACJ;IACA,MAAME,SAAS,GAAGrC,GAAG,CAACsC,sBAAsB,CAACJ,aAAa,CAAC;IAC3D,OAAOG,SAAS,CAACE,GAAG,GAAGJ,QAAQ,CAACI,GAAG,GAAGtC,UAAU;EACpD;EACAuC,4BAA4BA,CAACxB,QAAQ,EAAE;IACnC,MAAMkB,aAAa,GAAG,IAAI,CAAC7B,OAAO,CAACyB,UAAU,CAAC,CAAC;IAC/C,MAAMK,QAAQ,GAAG,IAAI,CAAC9B,OAAO,CAAC+B,0BAA0B,CAACpB,QAAQ,CAAC;IAClE,IAAI,CAACkB,aAAa,IAAI,CAACC,QAAQ,EAAE;MAC7B;IACJ;IACA,MAAME,SAAS,GAAGrC,GAAG,CAACsC,sBAAsB,CAACJ,aAAa,CAAC;IAC3D,MAAMO,OAAO,GAAGzC,GAAG,CAAC0C,aAAa,CAACR,aAAa,CAACS,aAAa,CAACC,IAAI,CAAC;IACnE,MAAMC,WAAW,GAAGR,SAAS,CAACE,GAAG,GAAGJ,QAAQ,CAACI,GAAG,GAAGJ,QAAQ,CAACf,MAAM;IAClE,OAAOqB,OAAO,CAACrB,MAAM,GAAGyB,WAAW,GAAG3C,aAAa;EACvD;EACA4C,uBAAuBA,CAACC,YAAY,EAAEC,cAAc,EAAE;IAClD,MAAMC,cAAc,GAAGC,IAAI,CAACC,GAAG,CAAC,IAAI,CAACX,4BAA4B,CAACQ,cAAc,CAAC,IAAII,QAAQ,EAAEL,YAAY,CAAC;IAC5G,MAAMM,cAAc,GAAGH,IAAI,CAACC,GAAG,CAAC,IAAI,CAAClB,4BAA4B,CAACe,cAAc,CAAC,IAAII,QAAQ,EAAEL,YAAY,CAAC;IAC5G,MAAMO,SAAS,GAAGJ,IAAI,CAACC,GAAG,CAACD,IAAI,CAACK,GAAG,CAACF,cAAc,EAAEJ,cAAc,CAAC,EAAEF,YAAY,CAAC;IAClF,MAAM3B,MAAM,GAAG8B,IAAI,CAACC,GAAG,CAACJ,YAAY,EAAEO,SAAS,CAAC;IAChD,IAAIE,cAAc;IAClB,IAAI,IAAI,CAACnD,OAAO,CAACoD,SAAS,CAAC,EAAE,CAAC,wBAAwB,CAAC,CAACC,KAAK,EAAE;MAC3DF,cAAc,GAAGpC,MAAM,IAAIiC,cAAc,GAAG,CAAC,CAAC,8CAA8C,CAAC,CAAC;IAClG,CAAC,MACI;MACDG,cAAc,GAAGpC,MAAM,IAAI6B,cAAc,GAAG,CAAC,CAAC,8CAA8C,CAAC,CAAC;IAClG;IACA,IAAIO,cAAc,KAAK,CAAC,CAAC,6CAA6C;MAClE,IAAI,CAAC9C,cAAc,CAACY,YAAY,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC;IAC9D,CAAC,MACI;MACD,IAAI,CAACZ,cAAc,CAACY,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC;IAC9D;IACA,OAAOkC,cAAc;EACzB;EACA/B,OAAOA,CAACC,SAAS,EAAE;IACf,IAAI,CAAChB,cAAc,CAACS,MAAM,CAACO,SAAS,CAACN,MAAM,EAAEM,SAAS,CAACL,KAAK,CAAC;EACjE;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}