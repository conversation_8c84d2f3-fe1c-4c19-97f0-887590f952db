{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { IndexTreeModel } from './indexTreeModel.js';\nimport { ObjectTreeElementCollapseState, TreeError } from './tree.js';\nimport { Iterable } from '../../../common/iterator.js';\nexport class ObjectTreeModel {\n  constructor(user, list, options = {}) {\n    this.user = user;\n    this.rootRef = null;\n    this.nodes = new Map();\n    this.nodesByIdentity = new Map();\n    this.model = new IndexTreeModel(user, list, null, options);\n    this.onDidSplice = this.model.onDidSplice;\n    this.onDidChangeCollapseState = this.model.onDidChangeCollapseState;\n    this.onDidChangeRenderNodeCount = this.model.onDidChangeRenderNodeCount;\n    if (options.sorter) {\n      this.sorter = {\n        compare(a, b) {\n          return options.sorter.compare(a.element, b.element);\n        }\n      };\n    }\n    this.identityProvider = options.identityProvider;\n  }\n  setChildren(element, children = Iterable.empty(), options = {}) {\n    const location = this.getElementLocation(element);\n    this._setChildren(location, this.preserveCollapseState(children), options);\n  }\n  _setChildren(location, children = Iterable.empty(), options) {\n    const insertedElements = new Set();\n    const insertedElementIds = new Set();\n    const onDidCreateNode = node => {\n      if (node.element === null) {\n        return;\n      }\n      const tnode = node;\n      insertedElements.add(tnode.element);\n      this.nodes.set(tnode.element, tnode);\n      if (this.identityProvider) {\n        const id = this.identityProvider.getId(tnode.element).toString();\n        insertedElementIds.add(id);\n        this.nodesByIdentity.set(id, tnode);\n      }\n      options.onDidCreateNode?.(tnode);\n    };\n    const onDidDeleteNode = node => {\n      if (node.element === null) {\n        return;\n      }\n      const tnode = node;\n      if (!insertedElements.has(tnode.element)) {\n        this.nodes.delete(tnode.element);\n      }\n      if (this.identityProvider) {\n        const id = this.identityProvider.getId(tnode.element).toString();\n        if (!insertedElementIds.has(id)) {\n          this.nodesByIdentity.delete(id);\n        }\n      }\n      options.onDidDeleteNode?.(tnode);\n    };\n    this.model.splice([...location, 0], Number.MAX_VALUE, children, {\n      ...options,\n      onDidCreateNode,\n      onDidDeleteNode\n    });\n  }\n  preserveCollapseState(elements = Iterable.empty()) {\n    if (this.sorter) {\n      elements = [...elements].sort(this.sorter.compare.bind(this.sorter));\n    }\n    return Iterable.map(elements, treeElement => {\n      let node = this.nodes.get(treeElement.element);\n      if (!node && this.identityProvider) {\n        const id = this.identityProvider.getId(treeElement.element).toString();\n        node = this.nodesByIdentity.get(id);\n      }\n      if (!node) {\n        let collapsed;\n        if (typeof treeElement.collapsed === 'undefined') {\n          collapsed = undefined;\n        } else if (treeElement.collapsed === ObjectTreeElementCollapseState.Collapsed || treeElement.collapsed === ObjectTreeElementCollapseState.PreserveOrCollapsed) {\n          collapsed = true;\n        } else if (treeElement.collapsed === ObjectTreeElementCollapseState.Expanded || treeElement.collapsed === ObjectTreeElementCollapseState.PreserveOrExpanded) {\n          collapsed = false;\n        } else {\n          collapsed = Boolean(treeElement.collapsed);\n        }\n        return {\n          ...treeElement,\n          children: this.preserveCollapseState(treeElement.children),\n          collapsed\n        };\n      }\n      const collapsible = typeof treeElement.collapsible === 'boolean' ? treeElement.collapsible : node.collapsible;\n      let collapsed;\n      if (typeof treeElement.collapsed === 'undefined' || treeElement.collapsed === ObjectTreeElementCollapseState.PreserveOrCollapsed || treeElement.collapsed === ObjectTreeElementCollapseState.PreserveOrExpanded) {\n        collapsed = node.collapsed;\n      } else if (treeElement.collapsed === ObjectTreeElementCollapseState.Collapsed) {\n        collapsed = true;\n      } else if (treeElement.collapsed === ObjectTreeElementCollapseState.Expanded) {\n        collapsed = false;\n      } else {\n        collapsed = Boolean(treeElement.collapsed);\n      }\n      return {\n        ...treeElement,\n        collapsible,\n        collapsed,\n        children: this.preserveCollapseState(treeElement.children)\n      };\n    });\n  }\n  rerender(element) {\n    const location = this.getElementLocation(element);\n    this.model.rerender(location);\n  }\n  getFirstElementChild(ref = null) {\n    const location = this.getElementLocation(ref);\n    return this.model.getFirstElementChild(location);\n  }\n  has(element) {\n    return this.nodes.has(element);\n  }\n  getListIndex(element) {\n    const location = this.getElementLocation(element);\n    return this.model.getListIndex(location);\n  }\n  getListRenderCount(element) {\n    const location = this.getElementLocation(element);\n    return this.model.getListRenderCount(location);\n  }\n  isCollapsible(element) {\n    const location = this.getElementLocation(element);\n    return this.model.isCollapsible(location);\n  }\n  setCollapsible(element, collapsible) {\n    const location = this.getElementLocation(element);\n    return this.model.setCollapsible(location, collapsible);\n  }\n  isCollapsed(element) {\n    const location = this.getElementLocation(element);\n    return this.model.isCollapsed(location);\n  }\n  setCollapsed(element, collapsed, recursive) {\n    const location = this.getElementLocation(element);\n    return this.model.setCollapsed(location, collapsed, recursive);\n  }\n  expandTo(element) {\n    const location = this.getElementLocation(element);\n    this.model.expandTo(location);\n  }\n  refilter() {\n    this.model.refilter();\n  }\n  getNode(element = null) {\n    if (element === null) {\n      return this.model.getNode(this.model.rootRef);\n    }\n    const node = this.nodes.get(element);\n    if (!node) {\n      throw new TreeError(this.user, `Tree element not found: ${element}`);\n    }\n    return node;\n  }\n  getNodeLocation(node) {\n    return node.element;\n  }\n  getParentNodeLocation(element) {\n    if (element === null) {\n      throw new TreeError(this.user, `Invalid getParentNodeLocation call`);\n    }\n    const node = this.nodes.get(element);\n    if (!node) {\n      throw new TreeError(this.user, `Tree element not found: ${element}`);\n    }\n    const location = this.model.getNodeLocation(node);\n    const parentLocation = this.model.getParentNodeLocation(location);\n    const parent = this.model.getNode(parentLocation);\n    return parent.element;\n  }\n  getElementLocation(element) {\n    if (element === null) {\n      return [];\n    }\n    const node = this.nodes.get(element);\n    if (!node) {\n      throw new TreeError(this.user, `Tree element not found: ${element}`);\n    }\n    return this.model.getNodeLocation(node);\n  }\n}", "map": {"version": 3, "names": ["IndexTreeModel", "ObjectTreeElementCollapseState", "TreeError", "Iterable", "ObjectTreeModel", "constructor", "user", "list", "options", "rootRef", "nodes", "Map", "nodesByIdentity", "model", "onDidSplice", "onDidChangeCollapseState", "onDidChangeRenderNodeCount", "sorter", "compare", "a", "b", "element", "identity<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "empty", "location", "getElementLocation", "_set<PERSON><PERSON><PERSON>n", "preserveCollapseState", "insertedElements", "Set", "insertedElementIds", "onDidCreateNode", "node", "tnode", "add", "set", "id", "getId", "toString", "onDidDeleteNode", "has", "delete", "splice", "Number", "MAX_VALUE", "elements", "sort", "bind", "map", "treeElement", "get", "collapsed", "undefined", "Collapsed", "PreserveOrCollapsed", "Expanded", "PreserveOrExpanded", "Boolean", "collapsible", "rerender", "getFirstElementChild", "ref", "getListIndex", "getListRenderCount", "isCollapsible", "setCollapsible", "isCollapsed", "setCollapsed", "recursive", "expandTo", "refilter", "getNode", "getNodeLocation", "getParentNodeLocation", "parentLocation", "parent"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/browser/ui/tree/objectTreeModel.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { IndexTreeModel } from './indexTreeModel.js';\nimport { ObjectTreeElementCollapseState, TreeError } from './tree.js';\nimport { Iterable } from '../../../common/iterator.js';\nexport class ObjectTreeModel {\n    constructor(user, list, options = {}) {\n        this.user = user;\n        this.rootRef = null;\n        this.nodes = new Map();\n        this.nodesByIdentity = new Map();\n        this.model = new IndexTreeModel(user, list, null, options);\n        this.onDidSplice = this.model.onDidSplice;\n        this.onDidChangeCollapseState = this.model.onDidChangeCollapseState;\n        this.onDidChangeRenderNodeCount = this.model.onDidChangeRenderNodeCount;\n        if (options.sorter) {\n            this.sorter = {\n                compare(a, b) {\n                    return options.sorter.compare(a.element, b.element);\n                }\n            };\n        }\n        this.identityProvider = options.identityProvider;\n    }\n    setChildren(element, children = Iterable.empty(), options = {}) {\n        const location = this.getElementLocation(element);\n        this._setChildren(location, this.preserveCollapseState(children), options);\n    }\n    _setChildren(location, children = Iterable.empty(), options) {\n        const insertedElements = new Set();\n        const insertedElementIds = new Set();\n        const onDidCreateNode = (node) => {\n            if (node.element === null) {\n                return;\n            }\n            const tnode = node;\n            insertedElements.add(tnode.element);\n            this.nodes.set(tnode.element, tnode);\n            if (this.identityProvider) {\n                const id = this.identityProvider.getId(tnode.element).toString();\n                insertedElementIds.add(id);\n                this.nodesByIdentity.set(id, tnode);\n            }\n            options.onDidCreateNode?.(tnode);\n        };\n        const onDidDeleteNode = (node) => {\n            if (node.element === null) {\n                return;\n            }\n            const tnode = node;\n            if (!insertedElements.has(tnode.element)) {\n                this.nodes.delete(tnode.element);\n            }\n            if (this.identityProvider) {\n                const id = this.identityProvider.getId(tnode.element).toString();\n                if (!insertedElementIds.has(id)) {\n                    this.nodesByIdentity.delete(id);\n                }\n            }\n            options.onDidDeleteNode?.(tnode);\n        };\n        this.model.splice([...location, 0], Number.MAX_VALUE, children, { ...options, onDidCreateNode, onDidDeleteNode });\n    }\n    preserveCollapseState(elements = Iterable.empty()) {\n        if (this.sorter) {\n            elements = [...elements].sort(this.sorter.compare.bind(this.sorter));\n        }\n        return Iterable.map(elements, treeElement => {\n            let node = this.nodes.get(treeElement.element);\n            if (!node && this.identityProvider) {\n                const id = this.identityProvider.getId(treeElement.element).toString();\n                node = this.nodesByIdentity.get(id);\n            }\n            if (!node) {\n                let collapsed;\n                if (typeof treeElement.collapsed === 'undefined') {\n                    collapsed = undefined;\n                }\n                else if (treeElement.collapsed === ObjectTreeElementCollapseState.Collapsed || treeElement.collapsed === ObjectTreeElementCollapseState.PreserveOrCollapsed) {\n                    collapsed = true;\n                }\n                else if (treeElement.collapsed === ObjectTreeElementCollapseState.Expanded || treeElement.collapsed === ObjectTreeElementCollapseState.PreserveOrExpanded) {\n                    collapsed = false;\n                }\n                else {\n                    collapsed = Boolean(treeElement.collapsed);\n                }\n                return {\n                    ...treeElement,\n                    children: this.preserveCollapseState(treeElement.children),\n                    collapsed\n                };\n            }\n            const collapsible = typeof treeElement.collapsible === 'boolean' ? treeElement.collapsible : node.collapsible;\n            let collapsed;\n            if (typeof treeElement.collapsed === 'undefined' || treeElement.collapsed === ObjectTreeElementCollapseState.PreserveOrCollapsed || treeElement.collapsed === ObjectTreeElementCollapseState.PreserveOrExpanded) {\n                collapsed = node.collapsed;\n            }\n            else if (treeElement.collapsed === ObjectTreeElementCollapseState.Collapsed) {\n                collapsed = true;\n            }\n            else if (treeElement.collapsed === ObjectTreeElementCollapseState.Expanded) {\n                collapsed = false;\n            }\n            else {\n                collapsed = Boolean(treeElement.collapsed);\n            }\n            return {\n                ...treeElement,\n                collapsible,\n                collapsed,\n                children: this.preserveCollapseState(treeElement.children)\n            };\n        });\n    }\n    rerender(element) {\n        const location = this.getElementLocation(element);\n        this.model.rerender(location);\n    }\n    getFirstElementChild(ref = null) {\n        const location = this.getElementLocation(ref);\n        return this.model.getFirstElementChild(location);\n    }\n    has(element) {\n        return this.nodes.has(element);\n    }\n    getListIndex(element) {\n        const location = this.getElementLocation(element);\n        return this.model.getListIndex(location);\n    }\n    getListRenderCount(element) {\n        const location = this.getElementLocation(element);\n        return this.model.getListRenderCount(location);\n    }\n    isCollapsible(element) {\n        const location = this.getElementLocation(element);\n        return this.model.isCollapsible(location);\n    }\n    setCollapsible(element, collapsible) {\n        const location = this.getElementLocation(element);\n        return this.model.setCollapsible(location, collapsible);\n    }\n    isCollapsed(element) {\n        const location = this.getElementLocation(element);\n        return this.model.isCollapsed(location);\n    }\n    setCollapsed(element, collapsed, recursive) {\n        const location = this.getElementLocation(element);\n        return this.model.setCollapsed(location, collapsed, recursive);\n    }\n    expandTo(element) {\n        const location = this.getElementLocation(element);\n        this.model.expandTo(location);\n    }\n    refilter() {\n        this.model.refilter();\n    }\n    getNode(element = null) {\n        if (element === null) {\n            return this.model.getNode(this.model.rootRef);\n        }\n        const node = this.nodes.get(element);\n        if (!node) {\n            throw new TreeError(this.user, `Tree element not found: ${element}`);\n        }\n        return node;\n    }\n    getNodeLocation(node) {\n        return node.element;\n    }\n    getParentNodeLocation(element) {\n        if (element === null) {\n            throw new TreeError(this.user, `Invalid getParentNodeLocation call`);\n        }\n        const node = this.nodes.get(element);\n        if (!node) {\n            throw new TreeError(this.user, `Tree element not found: ${element}`);\n        }\n        const location = this.model.getNodeLocation(node);\n        const parentLocation = this.model.getParentNodeLocation(location);\n        const parent = this.model.getNode(parentLocation);\n        return parent.element;\n    }\n    getElementLocation(element) {\n        if (element === null) {\n            return [];\n        }\n        const node = this.nodes.get(element);\n        if (!node) {\n            throw new TreeError(this.user, `Tree element not found: ${element}`);\n        }\n        return this.model.getNodeLocation(node);\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,cAAc,QAAQ,qBAAqB;AACpD,SAASC,8BAA8B,EAAEC,SAAS,QAAQ,WAAW;AACrE,SAASC,QAAQ,QAAQ,6BAA6B;AACtD,OAAO,MAAMC,eAAe,CAAC;EACzBC,WAAWA,CAACC,IAAI,EAAEC,IAAI,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IAClC,IAAI,CAACF,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACG,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,KAAK,GAAG,IAAIC,GAAG,CAAC,CAAC;IACtB,IAAI,CAACC,eAAe,GAAG,IAAID,GAAG,CAAC,CAAC;IAChC,IAAI,CAACE,KAAK,GAAG,IAAIb,cAAc,CAACM,IAAI,EAAEC,IAAI,EAAE,IAAI,EAAEC,OAAO,CAAC;IAC1D,IAAI,CAACM,WAAW,GAAG,IAAI,CAACD,KAAK,CAACC,WAAW;IACzC,IAAI,CAACC,wBAAwB,GAAG,IAAI,CAACF,KAAK,CAACE,wBAAwB;IACnE,IAAI,CAACC,0BAA0B,GAAG,IAAI,CAACH,KAAK,CAACG,0BAA0B;IACvE,IAAIR,OAAO,CAACS,MAAM,EAAE;MAChB,IAAI,CAACA,MAAM,GAAG;QACVC,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;UACV,OAAOZ,OAAO,CAACS,MAAM,CAACC,OAAO,CAACC,CAAC,CAACE,OAAO,EAAED,CAAC,CAACC,OAAO,CAAC;QACvD;MACJ,CAAC;IACL;IACA,IAAI,CAACC,gBAAgB,GAAGd,OAAO,CAACc,gBAAgB;EACpD;EACAC,WAAWA,CAACF,OAAO,EAAEG,QAAQ,GAAGrB,QAAQ,CAACsB,KAAK,CAAC,CAAC,EAAEjB,OAAO,GAAG,CAAC,CAAC,EAAE;IAC5D,MAAMkB,QAAQ,GAAG,IAAI,CAACC,kBAAkB,CAACN,OAAO,CAAC;IACjD,IAAI,CAACO,YAAY,CAACF,QAAQ,EAAE,IAAI,CAACG,qBAAqB,CAACL,QAAQ,CAAC,EAAEhB,OAAO,CAAC;EAC9E;EACAoB,YAAYA,CAACF,QAAQ,EAAEF,QAAQ,GAAGrB,QAAQ,CAACsB,KAAK,CAAC,CAAC,EAAEjB,OAAO,EAAE;IACzD,MAAMsB,gBAAgB,GAAG,IAAIC,GAAG,CAAC,CAAC;IAClC,MAAMC,kBAAkB,GAAG,IAAID,GAAG,CAAC,CAAC;IACpC,MAAME,eAAe,GAAIC,IAAI,IAAK;MAC9B,IAAIA,IAAI,CAACb,OAAO,KAAK,IAAI,EAAE;QACvB;MACJ;MACA,MAAMc,KAAK,GAAGD,IAAI;MAClBJ,gBAAgB,CAACM,GAAG,CAACD,KAAK,CAACd,OAAO,CAAC;MACnC,IAAI,CAACX,KAAK,CAAC2B,GAAG,CAACF,KAAK,CAACd,OAAO,EAAEc,KAAK,CAAC;MACpC,IAAI,IAAI,CAACb,gBAAgB,EAAE;QACvB,MAAMgB,EAAE,GAAG,IAAI,CAAChB,gBAAgB,CAACiB,KAAK,CAACJ,KAAK,CAACd,OAAO,CAAC,CAACmB,QAAQ,CAAC,CAAC;QAChER,kBAAkB,CAACI,GAAG,CAACE,EAAE,CAAC;QAC1B,IAAI,CAAC1B,eAAe,CAACyB,GAAG,CAACC,EAAE,EAAEH,KAAK,CAAC;MACvC;MACA3B,OAAO,CAACyB,eAAe,GAAGE,KAAK,CAAC;IACpC,CAAC;IACD,MAAMM,eAAe,GAAIP,IAAI,IAAK;MAC9B,IAAIA,IAAI,CAACb,OAAO,KAAK,IAAI,EAAE;QACvB;MACJ;MACA,MAAMc,KAAK,GAAGD,IAAI;MAClB,IAAI,CAACJ,gBAAgB,CAACY,GAAG,CAACP,KAAK,CAACd,OAAO,CAAC,EAAE;QACtC,IAAI,CAACX,KAAK,CAACiC,MAAM,CAACR,KAAK,CAACd,OAAO,CAAC;MACpC;MACA,IAAI,IAAI,CAACC,gBAAgB,EAAE;QACvB,MAAMgB,EAAE,GAAG,IAAI,CAAChB,gBAAgB,CAACiB,KAAK,CAACJ,KAAK,CAACd,OAAO,CAAC,CAACmB,QAAQ,CAAC,CAAC;QAChE,IAAI,CAACR,kBAAkB,CAACU,GAAG,CAACJ,EAAE,CAAC,EAAE;UAC7B,IAAI,CAAC1B,eAAe,CAAC+B,MAAM,CAACL,EAAE,CAAC;QACnC;MACJ;MACA9B,OAAO,CAACiC,eAAe,GAAGN,KAAK,CAAC;IACpC,CAAC;IACD,IAAI,CAACtB,KAAK,CAAC+B,MAAM,CAAC,CAAC,GAAGlB,QAAQ,EAAE,CAAC,CAAC,EAAEmB,MAAM,CAACC,SAAS,EAAEtB,QAAQ,EAAE;MAAE,GAAGhB,OAAO;MAAEyB,eAAe;MAAEQ;IAAgB,CAAC,CAAC;EACrH;EACAZ,qBAAqBA,CAACkB,QAAQ,GAAG5C,QAAQ,CAACsB,KAAK,CAAC,CAAC,EAAE;IAC/C,IAAI,IAAI,CAACR,MAAM,EAAE;MACb8B,QAAQ,GAAG,CAAC,GAAGA,QAAQ,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC/B,MAAM,CAACC,OAAO,CAAC+B,IAAI,CAAC,IAAI,CAAChC,MAAM,CAAC,CAAC;IACxE;IACA,OAAOd,QAAQ,CAAC+C,GAAG,CAACH,QAAQ,EAAEI,WAAW,IAAI;MACzC,IAAIjB,IAAI,GAAG,IAAI,CAACxB,KAAK,CAAC0C,GAAG,CAACD,WAAW,CAAC9B,OAAO,CAAC;MAC9C,IAAI,CAACa,IAAI,IAAI,IAAI,CAACZ,gBAAgB,EAAE;QAChC,MAAMgB,EAAE,GAAG,IAAI,CAAChB,gBAAgB,CAACiB,KAAK,CAACY,WAAW,CAAC9B,OAAO,CAAC,CAACmB,QAAQ,CAAC,CAAC;QACtEN,IAAI,GAAG,IAAI,CAACtB,eAAe,CAACwC,GAAG,CAACd,EAAE,CAAC;MACvC;MACA,IAAI,CAACJ,IAAI,EAAE;QACP,IAAImB,SAAS;QACb,IAAI,OAAOF,WAAW,CAACE,SAAS,KAAK,WAAW,EAAE;UAC9CA,SAAS,GAAGC,SAAS;QACzB,CAAC,MACI,IAAIH,WAAW,CAACE,SAAS,KAAKpD,8BAA8B,CAACsD,SAAS,IAAIJ,WAAW,CAACE,SAAS,KAAKpD,8BAA8B,CAACuD,mBAAmB,EAAE;UACzJH,SAAS,GAAG,IAAI;QACpB,CAAC,MACI,IAAIF,WAAW,CAACE,SAAS,KAAKpD,8BAA8B,CAACwD,QAAQ,IAAIN,WAAW,CAACE,SAAS,KAAKpD,8BAA8B,CAACyD,kBAAkB,EAAE;UACvJL,SAAS,GAAG,KAAK;QACrB,CAAC,MACI;UACDA,SAAS,GAAGM,OAAO,CAACR,WAAW,CAACE,SAAS,CAAC;QAC9C;QACA,OAAO;UACH,GAAGF,WAAW;UACd3B,QAAQ,EAAE,IAAI,CAACK,qBAAqB,CAACsB,WAAW,CAAC3B,QAAQ,CAAC;UAC1D6B;QACJ,CAAC;MACL;MACA,MAAMO,WAAW,GAAG,OAAOT,WAAW,CAACS,WAAW,KAAK,SAAS,GAAGT,WAAW,CAACS,WAAW,GAAG1B,IAAI,CAAC0B,WAAW;MAC7G,IAAIP,SAAS;MACb,IAAI,OAAOF,WAAW,CAACE,SAAS,KAAK,WAAW,IAAIF,WAAW,CAACE,SAAS,KAAKpD,8BAA8B,CAACuD,mBAAmB,IAAIL,WAAW,CAACE,SAAS,KAAKpD,8BAA8B,CAACyD,kBAAkB,EAAE;QAC7ML,SAAS,GAAGnB,IAAI,CAACmB,SAAS;MAC9B,CAAC,MACI,IAAIF,WAAW,CAACE,SAAS,KAAKpD,8BAA8B,CAACsD,SAAS,EAAE;QACzEF,SAAS,GAAG,IAAI;MACpB,CAAC,MACI,IAAIF,WAAW,CAACE,SAAS,KAAKpD,8BAA8B,CAACwD,QAAQ,EAAE;QACxEJ,SAAS,GAAG,KAAK;MACrB,CAAC,MACI;QACDA,SAAS,GAAGM,OAAO,CAACR,WAAW,CAACE,SAAS,CAAC;MAC9C;MACA,OAAO;QACH,GAAGF,WAAW;QACdS,WAAW;QACXP,SAAS;QACT7B,QAAQ,EAAE,IAAI,CAACK,qBAAqB,CAACsB,WAAW,CAAC3B,QAAQ;MAC7D,CAAC;IACL,CAAC,CAAC;EACN;EACAqC,QAAQA,CAACxC,OAAO,EAAE;IACd,MAAMK,QAAQ,GAAG,IAAI,CAACC,kBAAkB,CAACN,OAAO,CAAC;IACjD,IAAI,CAACR,KAAK,CAACgD,QAAQ,CAACnC,QAAQ,CAAC;EACjC;EACAoC,oBAAoBA,CAACC,GAAG,GAAG,IAAI,EAAE;IAC7B,MAAMrC,QAAQ,GAAG,IAAI,CAACC,kBAAkB,CAACoC,GAAG,CAAC;IAC7C,OAAO,IAAI,CAAClD,KAAK,CAACiD,oBAAoB,CAACpC,QAAQ,CAAC;EACpD;EACAgB,GAAGA,CAACrB,OAAO,EAAE;IACT,OAAO,IAAI,CAACX,KAAK,CAACgC,GAAG,CAACrB,OAAO,CAAC;EAClC;EACA2C,YAAYA,CAAC3C,OAAO,EAAE;IAClB,MAAMK,QAAQ,GAAG,IAAI,CAACC,kBAAkB,CAACN,OAAO,CAAC;IACjD,OAAO,IAAI,CAACR,KAAK,CAACmD,YAAY,CAACtC,QAAQ,CAAC;EAC5C;EACAuC,kBAAkBA,CAAC5C,OAAO,EAAE;IACxB,MAAMK,QAAQ,GAAG,IAAI,CAACC,kBAAkB,CAACN,OAAO,CAAC;IACjD,OAAO,IAAI,CAACR,KAAK,CAACoD,kBAAkB,CAACvC,QAAQ,CAAC;EAClD;EACAwC,aAAaA,CAAC7C,OAAO,EAAE;IACnB,MAAMK,QAAQ,GAAG,IAAI,CAACC,kBAAkB,CAACN,OAAO,CAAC;IACjD,OAAO,IAAI,CAACR,KAAK,CAACqD,aAAa,CAACxC,QAAQ,CAAC;EAC7C;EACAyC,cAAcA,CAAC9C,OAAO,EAAEuC,WAAW,EAAE;IACjC,MAAMlC,QAAQ,GAAG,IAAI,CAACC,kBAAkB,CAACN,OAAO,CAAC;IACjD,OAAO,IAAI,CAACR,KAAK,CAACsD,cAAc,CAACzC,QAAQ,EAAEkC,WAAW,CAAC;EAC3D;EACAQ,WAAWA,CAAC/C,OAAO,EAAE;IACjB,MAAMK,QAAQ,GAAG,IAAI,CAACC,kBAAkB,CAACN,OAAO,CAAC;IACjD,OAAO,IAAI,CAACR,KAAK,CAACuD,WAAW,CAAC1C,QAAQ,CAAC;EAC3C;EACA2C,YAAYA,CAAChD,OAAO,EAAEgC,SAAS,EAAEiB,SAAS,EAAE;IACxC,MAAM5C,QAAQ,GAAG,IAAI,CAACC,kBAAkB,CAACN,OAAO,CAAC;IACjD,OAAO,IAAI,CAACR,KAAK,CAACwD,YAAY,CAAC3C,QAAQ,EAAE2B,SAAS,EAAEiB,SAAS,CAAC;EAClE;EACAC,QAAQA,CAAClD,OAAO,EAAE;IACd,MAAMK,QAAQ,GAAG,IAAI,CAACC,kBAAkB,CAACN,OAAO,CAAC;IACjD,IAAI,CAACR,KAAK,CAAC0D,QAAQ,CAAC7C,QAAQ,CAAC;EACjC;EACA8C,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC3D,KAAK,CAAC2D,QAAQ,CAAC,CAAC;EACzB;EACAC,OAAOA,CAACpD,OAAO,GAAG,IAAI,EAAE;IACpB,IAAIA,OAAO,KAAK,IAAI,EAAE;MAClB,OAAO,IAAI,CAACR,KAAK,CAAC4D,OAAO,CAAC,IAAI,CAAC5D,KAAK,CAACJ,OAAO,CAAC;IACjD;IACA,MAAMyB,IAAI,GAAG,IAAI,CAACxB,KAAK,CAAC0C,GAAG,CAAC/B,OAAO,CAAC;IACpC,IAAI,CAACa,IAAI,EAAE;MACP,MAAM,IAAIhC,SAAS,CAAC,IAAI,CAACI,IAAI,EAAE,2BAA2Be,OAAO,EAAE,CAAC;IACxE;IACA,OAAOa,IAAI;EACf;EACAwC,eAAeA,CAACxC,IAAI,EAAE;IAClB,OAAOA,IAAI,CAACb,OAAO;EACvB;EACAsD,qBAAqBA,CAACtD,OAAO,EAAE;IAC3B,IAAIA,OAAO,KAAK,IAAI,EAAE;MAClB,MAAM,IAAInB,SAAS,CAAC,IAAI,CAACI,IAAI,EAAE,oCAAoC,CAAC;IACxE;IACA,MAAM4B,IAAI,GAAG,IAAI,CAACxB,KAAK,CAAC0C,GAAG,CAAC/B,OAAO,CAAC;IACpC,IAAI,CAACa,IAAI,EAAE;MACP,MAAM,IAAIhC,SAAS,CAAC,IAAI,CAACI,IAAI,EAAE,2BAA2Be,OAAO,EAAE,CAAC;IACxE;IACA,MAAMK,QAAQ,GAAG,IAAI,CAACb,KAAK,CAAC6D,eAAe,CAACxC,IAAI,CAAC;IACjD,MAAM0C,cAAc,GAAG,IAAI,CAAC/D,KAAK,CAAC8D,qBAAqB,CAACjD,QAAQ,CAAC;IACjE,MAAMmD,MAAM,GAAG,IAAI,CAAChE,KAAK,CAAC4D,OAAO,CAACG,cAAc,CAAC;IACjD,OAAOC,MAAM,CAACxD,OAAO;EACzB;EACAM,kBAAkBA,CAACN,OAAO,EAAE;IACxB,IAAIA,OAAO,KAAK,IAAI,EAAE;MAClB,OAAO,EAAE;IACb;IACA,MAAMa,IAAI,GAAG,IAAI,CAACxB,KAAK,CAAC0C,GAAG,CAAC/B,OAAO,CAAC;IACpC,IAAI,CAACa,IAAI,EAAE;MACP,MAAM,IAAIhC,SAAS,CAAC,IAAI,CAACI,IAAI,EAAE,2BAA2Be,OAAO,EAAE,CAAC;IACxE;IACA,OAAO,IAAI,CAACR,KAAK,CAAC6D,eAAe,CAACxC,IAAI,CAAC;EAC3C;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}