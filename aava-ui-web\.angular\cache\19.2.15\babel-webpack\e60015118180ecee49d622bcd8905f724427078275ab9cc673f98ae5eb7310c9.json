{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { BugIndicatingError } from '../../../base/common/errors.js';\n/**\n * A range of offsets (0-based).\n*/\nexport class OffsetRange {\n  static addRange(range, sortedRanges) {\n    let i = 0;\n    while (i < sortedRanges.length && sortedRanges[i].endExclusive < range.start) {\n      i++;\n    }\n    let j = i;\n    while (j < sortedRanges.length && sortedRanges[j].start <= range.endExclusive) {\n      j++;\n    }\n    if (i === j) {\n      sortedRanges.splice(i, 0, range);\n    } else {\n      const start = Math.min(range.start, sortedRanges[i].start);\n      const end = Math.max(range.endExclusive, sortedRanges[j - 1].endExclusive);\n      sortedRanges.splice(i, j - i, new OffsetRange(start, end));\n    }\n  }\n  static tryCreate(start, endExclusive) {\n    if (start > endExclusive) {\n      return undefined;\n    }\n    return new OffsetRange(start, endExclusive);\n  }\n  static ofLength(length) {\n    return new OffsetRange(0, length);\n  }\n  static ofStartAndLength(start, length) {\n    return new OffsetRange(start, start + length);\n  }\n  constructor(start, endExclusive) {\n    this.start = start;\n    this.endExclusive = endExclusive;\n    if (start > endExclusive) {\n      throw new BugIndicatingError(`Invalid range: ${this.toString()}`);\n    }\n  }\n  get isEmpty() {\n    return this.start === this.endExclusive;\n  }\n  delta(offset) {\n    return new OffsetRange(this.start + offset, this.endExclusive + offset);\n  }\n  deltaStart(offset) {\n    return new OffsetRange(this.start + offset, this.endExclusive);\n  }\n  deltaEnd(offset) {\n    return new OffsetRange(this.start, this.endExclusive + offset);\n  }\n  get length() {\n    return this.endExclusive - this.start;\n  }\n  toString() {\n    return `[${this.start}, ${this.endExclusive})`;\n  }\n  contains(offset) {\n    return this.start <= offset && offset < this.endExclusive;\n  }\n  /**\n   * for all numbers n: range1.contains(n) or range2.contains(n) => range1.join(range2).contains(n)\n   * The joined range is the smallest range that contains both ranges.\n   */\n  join(other) {\n    return new OffsetRange(Math.min(this.start, other.start), Math.max(this.endExclusive, other.endExclusive));\n  }\n  /**\n   * for all numbers n: range1.contains(n) and range2.contains(n) <=> range1.intersect(range2).contains(n)\n   *\n   * The resulting range is empty if the ranges do not intersect, but touch.\n   * If the ranges don't even touch, the result is undefined.\n   */\n  intersect(other) {\n    const start = Math.max(this.start, other.start);\n    const end = Math.min(this.endExclusive, other.endExclusive);\n    if (start <= end) {\n      return new OffsetRange(start, end);\n    }\n    return undefined;\n  }\n  intersects(other) {\n    const start = Math.max(this.start, other.start);\n    const end = Math.min(this.endExclusive, other.endExclusive);\n    return start < end;\n  }\n  isBefore(other) {\n    return this.endExclusive <= other.start;\n  }\n  isAfter(other) {\n    return this.start >= other.endExclusive;\n  }\n  slice(arr) {\n    return arr.slice(this.start, this.endExclusive);\n  }\n  substring(str) {\n    return str.substring(this.start, this.endExclusive);\n  }\n  /**\n   * Returns the given value if it is contained in this instance, otherwise the closest value that is contained.\n   * The range must not be empty.\n   */\n  clip(value) {\n    if (this.isEmpty) {\n      throw new BugIndicatingError(`Invalid clipping range: ${this.toString()}`);\n    }\n    return Math.max(this.start, Math.min(this.endExclusive - 1, value));\n  }\n  /**\n   * Returns `r := value + k * length` such that `r` is contained in this range.\n   * The range must not be empty.\n   *\n   * E.g. `[5, 10).clipCyclic(10) === 5`, `[5, 10).clipCyclic(11) === 6` and `[5, 10).clipCyclic(4) === 9`.\n   */\n  clipCyclic(value) {\n    if (this.isEmpty) {\n      throw new BugIndicatingError(`Invalid clipping range: ${this.toString()}`);\n    }\n    if (value < this.start) {\n      return this.endExclusive - (this.start - value) % this.length;\n    }\n    if (value >= this.endExclusive) {\n      return this.start + (value - this.start) % this.length;\n    }\n    return value;\n  }\n  forEach(f) {\n    for (let i = this.start; i < this.endExclusive; i++) {\n      f(i);\n    }\n  }\n}\nexport class OffsetRangeSet {\n  constructor() {\n    this._sortedRanges = [];\n  }\n  addRange(range) {\n    let i = 0;\n    while (i < this._sortedRanges.length && this._sortedRanges[i].endExclusive < range.start) {\n      i++;\n    }\n    let j = i;\n    while (j < this._sortedRanges.length && this._sortedRanges[j].start <= range.endExclusive) {\n      j++;\n    }\n    if (i === j) {\n      this._sortedRanges.splice(i, 0, range);\n    } else {\n      const start = Math.min(range.start, this._sortedRanges[i].start);\n      const end = Math.max(range.endExclusive, this._sortedRanges[j - 1].endExclusive);\n      this._sortedRanges.splice(i, j - i, new OffsetRange(start, end));\n    }\n  }\n  toString() {\n    return this._sortedRanges.map(r => r.toString()).join(', ');\n  }\n  /**\n   * Returns of there is a value that is contained in this instance and the given range.\n   */\n  intersectsStrict(other) {\n    // TODO use binary search\n    let i = 0;\n    while (i < this._sortedRanges.length && this._sortedRanges[i].endExclusive <= other.start) {\n      i++;\n    }\n    return i < this._sortedRanges.length && this._sortedRanges[i].start < other.endExclusive;\n  }\n  intersectWithRange(other) {\n    // TODO use binary search + slice\n    const result = new OffsetRangeSet();\n    for (const range of this._sortedRanges) {\n      const intersection = range.intersect(other);\n      if (intersection) {\n        result.addRange(intersection);\n      }\n    }\n    return result;\n  }\n  intersectWithRangeLength(other) {\n    return this.intersectWithRange(other).length;\n  }\n  get length() {\n    return this._sortedRanges.reduce((prev, cur) => prev + cur.length, 0);\n  }\n}", "map": {"version": 3, "names": ["BugIndicatingError", "OffsetRange", "addRange", "range", "sortedRanges", "i", "length", "endExclusive", "start", "j", "splice", "Math", "min", "end", "max", "tryCreate", "undefined", "of<PERSON>ength", "ofStartAndLength", "constructor", "toString", "isEmpty", "delta", "offset", "deltaStart", "deltaEnd", "contains", "join", "other", "intersect", "intersects", "isBefore", "isAfter", "slice", "arr", "substring", "str", "clip", "value", "clipCyclic", "for<PERSON>ach", "f", "OffsetRangeSet", "_sortedRanges", "map", "r", "intersectsStrict", "intersectWithRange", "result", "intersection", "intersectWithRangeLength", "reduce", "prev", "cur"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/core/offsetRange.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { BugIndicatingError } from '../../../base/common/errors.js';\n/**\n * A range of offsets (0-based).\n*/\nexport class OffsetRange {\n    static addRange(range, sortedRanges) {\n        let i = 0;\n        while (i < sortedRanges.length && sortedRanges[i].endExclusive < range.start) {\n            i++;\n        }\n        let j = i;\n        while (j < sortedRanges.length && sortedRanges[j].start <= range.endExclusive) {\n            j++;\n        }\n        if (i === j) {\n            sortedRanges.splice(i, 0, range);\n        }\n        else {\n            const start = Math.min(range.start, sortedRanges[i].start);\n            const end = Math.max(range.endExclusive, sortedRanges[j - 1].endExclusive);\n            sortedRanges.splice(i, j - i, new OffsetRange(start, end));\n        }\n    }\n    static tryCreate(start, endExclusive) {\n        if (start > endExclusive) {\n            return undefined;\n        }\n        return new OffsetRange(start, endExclusive);\n    }\n    static ofLength(length) {\n        return new OffsetRange(0, length);\n    }\n    static ofStartAndLength(start, length) {\n        return new OffsetRange(start, start + length);\n    }\n    constructor(start, endExclusive) {\n        this.start = start;\n        this.endExclusive = endExclusive;\n        if (start > endExclusive) {\n            throw new BugIndicatingError(`Invalid range: ${this.toString()}`);\n        }\n    }\n    get isEmpty() {\n        return this.start === this.endExclusive;\n    }\n    delta(offset) {\n        return new OffsetRange(this.start + offset, this.endExclusive + offset);\n    }\n    deltaStart(offset) {\n        return new OffsetRange(this.start + offset, this.endExclusive);\n    }\n    deltaEnd(offset) {\n        return new OffsetRange(this.start, this.endExclusive + offset);\n    }\n    get length() {\n        return this.endExclusive - this.start;\n    }\n    toString() {\n        return `[${this.start}, ${this.endExclusive})`;\n    }\n    contains(offset) {\n        return this.start <= offset && offset < this.endExclusive;\n    }\n    /**\n     * for all numbers n: range1.contains(n) or range2.contains(n) => range1.join(range2).contains(n)\n     * The joined range is the smallest range that contains both ranges.\n     */\n    join(other) {\n        return new OffsetRange(Math.min(this.start, other.start), Math.max(this.endExclusive, other.endExclusive));\n    }\n    /**\n     * for all numbers n: range1.contains(n) and range2.contains(n) <=> range1.intersect(range2).contains(n)\n     *\n     * The resulting range is empty if the ranges do not intersect, but touch.\n     * If the ranges don't even touch, the result is undefined.\n     */\n    intersect(other) {\n        const start = Math.max(this.start, other.start);\n        const end = Math.min(this.endExclusive, other.endExclusive);\n        if (start <= end) {\n            return new OffsetRange(start, end);\n        }\n        return undefined;\n    }\n    intersects(other) {\n        const start = Math.max(this.start, other.start);\n        const end = Math.min(this.endExclusive, other.endExclusive);\n        return start < end;\n    }\n    isBefore(other) {\n        return this.endExclusive <= other.start;\n    }\n    isAfter(other) {\n        return this.start >= other.endExclusive;\n    }\n    slice(arr) {\n        return arr.slice(this.start, this.endExclusive);\n    }\n    substring(str) {\n        return str.substring(this.start, this.endExclusive);\n    }\n    /**\n     * Returns the given value if it is contained in this instance, otherwise the closest value that is contained.\n     * The range must not be empty.\n     */\n    clip(value) {\n        if (this.isEmpty) {\n            throw new BugIndicatingError(`Invalid clipping range: ${this.toString()}`);\n        }\n        return Math.max(this.start, Math.min(this.endExclusive - 1, value));\n    }\n    /**\n     * Returns `r := value + k * length` such that `r` is contained in this range.\n     * The range must not be empty.\n     *\n     * E.g. `[5, 10).clipCyclic(10) === 5`, `[5, 10).clipCyclic(11) === 6` and `[5, 10).clipCyclic(4) === 9`.\n     */\n    clipCyclic(value) {\n        if (this.isEmpty) {\n            throw new BugIndicatingError(`Invalid clipping range: ${this.toString()}`);\n        }\n        if (value < this.start) {\n            return this.endExclusive - ((this.start - value) % this.length);\n        }\n        if (value >= this.endExclusive) {\n            return this.start + ((value - this.start) % this.length);\n        }\n        return value;\n    }\n    forEach(f) {\n        for (let i = this.start; i < this.endExclusive; i++) {\n            f(i);\n        }\n    }\n}\nexport class OffsetRangeSet {\n    constructor() {\n        this._sortedRanges = [];\n    }\n    addRange(range) {\n        let i = 0;\n        while (i < this._sortedRanges.length && this._sortedRanges[i].endExclusive < range.start) {\n            i++;\n        }\n        let j = i;\n        while (j < this._sortedRanges.length && this._sortedRanges[j].start <= range.endExclusive) {\n            j++;\n        }\n        if (i === j) {\n            this._sortedRanges.splice(i, 0, range);\n        }\n        else {\n            const start = Math.min(range.start, this._sortedRanges[i].start);\n            const end = Math.max(range.endExclusive, this._sortedRanges[j - 1].endExclusive);\n            this._sortedRanges.splice(i, j - i, new OffsetRange(start, end));\n        }\n    }\n    toString() {\n        return this._sortedRanges.map(r => r.toString()).join(', ');\n    }\n    /**\n     * Returns of there is a value that is contained in this instance and the given range.\n     */\n    intersectsStrict(other) {\n        // TODO use binary search\n        let i = 0;\n        while (i < this._sortedRanges.length && this._sortedRanges[i].endExclusive <= other.start) {\n            i++;\n        }\n        return i < this._sortedRanges.length && this._sortedRanges[i].start < other.endExclusive;\n    }\n    intersectWithRange(other) {\n        // TODO use binary search + slice\n        const result = new OffsetRangeSet();\n        for (const range of this._sortedRanges) {\n            const intersection = range.intersect(other);\n            if (intersection) {\n                result.addRange(intersection);\n            }\n        }\n        return result;\n    }\n    intersectWithRangeLength(other) {\n        return this.intersectWithRange(other).length;\n    }\n    get length() {\n        return this._sortedRanges.reduce((prev, cur) => prev + cur.length, 0);\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,kBAAkB,QAAQ,gCAAgC;AACnE;AACA;AACA;AACA,OAAO,MAAMC,WAAW,CAAC;EACrB,OAAOC,QAAQA,CAACC,KAAK,EAAEC,YAAY,EAAE;IACjC,IAAIC,CAAC,GAAG,CAAC;IACT,OAAOA,CAAC,GAAGD,YAAY,CAACE,MAAM,IAAIF,YAAY,CAACC,CAAC,CAAC,CAACE,YAAY,GAAGJ,KAAK,CAACK,KAAK,EAAE;MAC1EH,CAAC,EAAE;IACP;IACA,IAAII,CAAC,GAAGJ,CAAC;IACT,OAAOI,CAAC,GAAGL,YAAY,CAACE,MAAM,IAAIF,YAAY,CAACK,CAAC,CAAC,CAACD,KAAK,IAAIL,KAAK,CAACI,YAAY,EAAE;MAC3EE,CAAC,EAAE;IACP;IACA,IAAIJ,CAAC,KAAKI,CAAC,EAAE;MACTL,YAAY,CAACM,MAAM,CAACL,CAAC,EAAE,CAAC,EAAEF,KAAK,CAAC;IACpC,CAAC,MACI;MACD,MAAMK,KAAK,GAAGG,IAAI,CAACC,GAAG,CAACT,KAAK,CAACK,KAAK,EAAEJ,YAAY,CAACC,CAAC,CAAC,CAACG,KAAK,CAAC;MAC1D,MAAMK,GAAG,GAAGF,IAAI,CAACG,GAAG,CAACX,KAAK,CAACI,YAAY,EAAEH,YAAY,CAACK,CAAC,GAAG,CAAC,CAAC,CAACF,YAAY,CAAC;MAC1EH,YAAY,CAACM,MAAM,CAACL,CAAC,EAAEI,CAAC,GAAGJ,CAAC,EAAE,IAAIJ,WAAW,CAACO,KAAK,EAAEK,GAAG,CAAC,CAAC;IAC9D;EACJ;EACA,OAAOE,SAASA,CAACP,KAAK,EAAED,YAAY,EAAE;IAClC,IAAIC,KAAK,GAAGD,YAAY,EAAE;MACtB,OAAOS,SAAS;IACpB;IACA,OAAO,IAAIf,WAAW,CAACO,KAAK,EAAED,YAAY,CAAC;EAC/C;EACA,OAAOU,QAAQA,CAACX,MAAM,EAAE;IACpB,OAAO,IAAIL,WAAW,CAAC,CAAC,EAAEK,MAAM,CAAC;EACrC;EACA,OAAOY,gBAAgBA,CAACV,KAAK,EAAEF,MAAM,EAAE;IACnC,OAAO,IAAIL,WAAW,CAACO,KAAK,EAAEA,KAAK,GAAGF,MAAM,CAAC;EACjD;EACAa,WAAWA,CAACX,KAAK,EAAED,YAAY,EAAE;IAC7B,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACD,YAAY,GAAGA,YAAY;IAChC,IAAIC,KAAK,GAAGD,YAAY,EAAE;MACtB,MAAM,IAAIP,kBAAkB,CAAC,kBAAkB,IAAI,CAACoB,QAAQ,CAAC,CAAC,EAAE,CAAC;IACrE;EACJ;EACA,IAAIC,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACb,KAAK,KAAK,IAAI,CAACD,YAAY;EAC3C;EACAe,KAAKA,CAACC,MAAM,EAAE;IACV,OAAO,IAAItB,WAAW,CAAC,IAAI,CAACO,KAAK,GAAGe,MAAM,EAAE,IAAI,CAAChB,YAAY,GAAGgB,MAAM,CAAC;EAC3E;EACAC,UAAUA,CAACD,MAAM,EAAE;IACf,OAAO,IAAItB,WAAW,CAAC,IAAI,CAACO,KAAK,GAAGe,MAAM,EAAE,IAAI,CAAChB,YAAY,CAAC;EAClE;EACAkB,QAAQA,CAACF,MAAM,EAAE;IACb,OAAO,IAAItB,WAAW,CAAC,IAAI,CAACO,KAAK,EAAE,IAAI,CAACD,YAAY,GAAGgB,MAAM,CAAC;EAClE;EACA,IAAIjB,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACC,YAAY,GAAG,IAAI,CAACC,KAAK;EACzC;EACAY,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,IAAI,CAACZ,KAAK,KAAK,IAAI,CAACD,YAAY,GAAG;EAClD;EACAmB,QAAQA,CAACH,MAAM,EAAE;IACb,OAAO,IAAI,CAACf,KAAK,IAAIe,MAAM,IAAIA,MAAM,GAAG,IAAI,CAAChB,YAAY;EAC7D;EACA;AACJ;AACA;AACA;EACIoB,IAAIA,CAACC,KAAK,EAAE;IACR,OAAO,IAAI3B,WAAW,CAACU,IAAI,CAACC,GAAG,CAAC,IAAI,CAACJ,KAAK,EAAEoB,KAAK,CAACpB,KAAK,CAAC,EAAEG,IAAI,CAACG,GAAG,CAAC,IAAI,CAACP,YAAY,EAAEqB,KAAK,CAACrB,YAAY,CAAC,CAAC;EAC9G;EACA;AACJ;AACA;AACA;AACA;AACA;EACIsB,SAASA,CAACD,KAAK,EAAE;IACb,MAAMpB,KAAK,GAAGG,IAAI,CAACG,GAAG,CAAC,IAAI,CAACN,KAAK,EAAEoB,KAAK,CAACpB,KAAK,CAAC;IAC/C,MAAMK,GAAG,GAAGF,IAAI,CAACC,GAAG,CAAC,IAAI,CAACL,YAAY,EAAEqB,KAAK,CAACrB,YAAY,CAAC;IAC3D,IAAIC,KAAK,IAAIK,GAAG,EAAE;MACd,OAAO,IAAIZ,WAAW,CAACO,KAAK,EAAEK,GAAG,CAAC;IACtC;IACA,OAAOG,SAAS;EACpB;EACAc,UAAUA,CAACF,KAAK,EAAE;IACd,MAAMpB,KAAK,GAAGG,IAAI,CAACG,GAAG,CAAC,IAAI,CAACN,KAAK,EAAEoB,KAAK,CAACpB,KAAK,CAAC;IAC/C,MAAMK,GAAG,GAAGF,IAAI,CAACC,GAAG,CAAC,IAAI,CAACL,YAAY,EAAEqB,KAAK,CAACrB,YAAY,CAAC;IAC3D,OAAOC,KAAK,GAAGK,GAAG;EACtB;EACAkB,QAAQA,CAACH,KAAK,EAAE;IACZ,OAAO,IAAI,CAACrB,YAAY,IAAIqB,KAAK,CAACpB,KAAK;EAC3C;EACAwB,OAAOA,CAACJ,KAAK,EAAE;IACX,OAAO,IAAI,CAACpB,KAAK,IAAIoB,KAAK,CAACrB,YAAY;EAC3C;EACA0B,KAAKA,CAACC,GAAG,EAAE;IACP,OAAOA,GAAG,CAACD,KAAK,CAAC,IAAI,CAACzB,KAAK,EAAE,IAAI,CAACD,YAAY,CAAC;EACnD;EACA4B,SAASA,CAACC,GAAG,EAAE;IACX,OAAOA,GAAG,CAACD,SAAS,CAAC,IAAI,CAAC3B,KAAK,EAAE,IAAI,CAACD,YAAY,CAAC;EACvD;EACA;AACJ;AACA;AACA;EACI8B,IAAIA,CAACC,KAAK,EAAE;IACR,IAAI,IAAI,CAACjB,OAAO,EAAE;MACd,MAAM,IAAIrB,kBAAkB,CAAC,2BAA2B,IAAI,CAACoB,QAAQ,CAAC,CAAC,EAAE,CAAC;IAC9E;IACA,OAAOT,IAAI,CAACG,GAAG,CAAC,IAAI,CAACN,KAAK,EAAEG,IAAI,CAACC,GAAG,CAAC,IAAI,CAACL,YAAY,GAAG,CAAC,EAAE+B,KAAK,CAAC,CAAC;EACvE;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,UAAUA,CAACD,KAAK,EAAE;IACd,IAAI,IAAI,CAACjB,OAAO,EAAE;MACd,MAAM,IAAIrB,kBAAkB,CAAC,2BAA2B,IAAI,CAACoB,QAAQ,CAAC,CAAC,EAAE,CAAC;IAC9E;IACA,IAAIkB,KAAK,GAAG,IAAI,CAAC9B,KAAK,EAAE;MACpB,OAAO,IAAI,CAACD,YAAY,GAAI,CAAC,IAAI,CAACC,KAAK,GAAG8B,KAAK,IAAI,IAAI,CAAChC,MAAO;IACnE;IACA,IAAIgC,KAAK,IAAI,IAAI,CAAC/B,YAAY,EAAE;MAC5B,OAAO,IAAI,CAACC,KAAK,GAAI,CAAC8B,KAAK,GAAG,IAAI,CAAC9B,KAAK,IAAI,IAAI,CAACF,MAAO;IAC5D;IACA,OAAOgC,KAAK;EAChB;EACAE,OAAOA,CAACC,CAAC,EAAE;IACP,KAAK,IAAIpC,CAAC,GAAG,IAAI,CAACG,KAAK,EAAEH,CAAC,GAAG,IAAI,CAACE,YAAY,EAAEF,CAAC,EAAE,EAAE;MACjDoC,CAAC,CAACpC,CAAC,CAAC;IACR;EACJ;AACJ;AACA,OAAO,MAAMqC,cAAc,CAAC;EACxBvB,WAAWA,CAAA,EAAG;IACV,IAAI,CAACwB,aAAa,GAAG,EAAE;EAC3B;EACAzC,QAAQA,CAACC,KAAK,EAAE;IACZ,IAAIE,CAAC,GAAG,CAAC;IACT,OAAOA,CAAC,GAAG,IAAI,CAACsC,aAAa,CAACrC,MAAM,IAAI,IAAI,CAACqC,aAAa,CAACtC,CAAC,CAAC,CAACE,YAAY,GAAGJ,KAAK,CAACK,KAAK,EAAE;MACtFH,CAAC,EAAE;IACP;IACA,IAAII,CAAC,GAAGJ,CAAC;IACT,OAAOI,CAAC,GAAG,IAAI,CAACkC,aAAa,CAACrC,MAAM,IAAI,IAAI,CAACqC,aAAa,CAAClC,CAAC,CAAC,CAACD,KAAK,IAAIL,KAAK,CAACI,YAAY,EAAE;MACvFE,CAAC,EAAE;IACP;IACA,IAAIJ,CAAC,KAAKI,CAAC,EAAE;MACT,IAAI,CAACkC,aAAa,CAACjC,MAAM,CAACL,CAAC,EAAE,CAAC,EAAEF,KAAK,CAAC;IAC1C,CAAC,MACI;MACD,MAAMK,KAAK,GAAGG,IAAI,CAACC,GAAG,CAACT,KAAK,CAACK,KAAK,EAAE,IAAI,CAACmC,aAAa,CAACtC,CAAC,CAAC,CAACG,KAAK,CAAC;MAChE,MAAMK,GAAG,GAAGF,IAAI,CAACG,GAAG,CAACX,KAAK,CAACI,YAAY,EAAE,IAAI,CAACoC,aAAa,CAAClC,CAAC,GAAG,CAAC,CAAC,CAACF,YAAY,CAAC;MAChF,IAAI,CAACoC,aAAa,CAACjC,MAAM,CAACL,CAAC,EAAEI,CAAC,GAAGJ,CAAC,EAAE,IAAIJ,WAAW,CAACO,KAAK,EAAEK,GAAG,CAAC,CAAC;IACpE;EACJ;EACAO,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACuB,aAAa,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACzB,QAAQ,CAAC,CAAC,CAAC,CAACO,IAAI,CAAC,IAAI,CAAC;EAC/D;EACA;AACJ;AACA;EACImB,gBAAgBA,CAAClB,KAAK,EAAE;IACpB;IACA,IAAIvB,CAAC,GAAG,CAAC;IACT,OAAOA,CAAC,GAAG,IAAI,CAACsC,aAAa,CAACrC,MAAM,IAAI,IAAI,CAACqC,aAAa,CAACtC,CAAC,CAAC,CAACE,YAAY,IAAIqB,KAAK,CAACpB,KAAK,EAAE;MACvFH,CAAC,EAAE;IACP;IACA,OAAOA,CAAC,GAAG,IAAI,CAACsC,aAAa,CAACrC,MAAM,IAAI,IAAI,CAACqC,aAAa,CAACtC,CAAC,CAAC,CAACG,KAAK,GAAGoB,KAAK,CAACrB,YAAY;EAC5F;EACAwC,kBAAkBA,CAACnB,KAAK,EAAE;IACtB;IACA,MAAMoB,MAAM,GAAG,IAAIN,cAAc,CAAC,CAAC;IACnC,KAAK,MAAMvC,KAAK,IAAI,IAAI,CAACwC,aAAa,EAAE;MACpC,MAAMM,YAAY,GAAG9C,KAAK,CAAC0B,SAAS,CAACD,KAAK,CAAC;MAC3C,IAAIqB,YAAY,EAAE;QACdD,MAAM,CAAC9C,QAAQ,CAAC+C,YAAY,CAAC;MACjC;IACJ;IACA,OAAOD,MAAM;EACjB;EACAE,wBAAwBA,CAACtB,KAAK,EAAE;IAC5B,OAAO,IAAI,CAACmB,kBAAkB,CAACnB,KAAK,CAAC,CAACtB,MAAM;EAChD;EACA,IAAIA,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACqC,aAAa,CAACQ,MAAM,CAAC,CAACC,IAAI,EAAEC,GAAG,KAAKD,IAAI,GAAGC,GAAG,CAAC/C,MAAM,EAAE,CAAC,CAAC;EACzE;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}