{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { AbstractText } from '../core/textEdit.js';\nimport { TextLength } from '../core/textLength.js';\nexport class TextModelText extends AbstractText {\n  constructor(_textModel) {\n    super();\n    this._textModel = _textModel;\n  }\n  getValueOfRange(range) {\n    return this._textModel.getValueInRange(range);\n  }\n  get length() {\n    const lastLineNumber = this._textModel.getLineCount();\n    const lastLineLen = this._textModel.getLineLength(lastLineNumber);\n    return new TextLength(lastLineNumber - 1, lastLineLen);\n  }\n}", "map": {"version": 3, "names": ["AbstractText", "TextLength", "TextModelText", "constructor", "_textModel", "getValueOfRange", "range", "getValueInRange", "length", "lastLineNumber", "getLineCount", "lastLineLen", "getLine<PERSON><PERSON>th"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/model/textModelText.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { AbstractText } from '../core/textEdit.js';\nimport { TextLength } from '../core/textLength.js';\nexport class TextModelText extends AbstractText {\n    constructor(_textModel) {\n        super();\n        this._textModel = _textModel;\n    }\n    getValueOfRange(range) {\n        return this._textModel.getValueInRange(range);\n    }\n    get length() {\n        const lastLineNumber = this._textModel.getLineCount();\n        const lastLineLen = this._textModel.getLineLength(lastLineNumber);\n        return new TextLength(lastLineNumber - 1, lastLineLen);\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,YAAY,QAAQ,qBAAqB;AAClD,SAASC,UAAU,QAAQ,uBAAuB;AAClD,OAAO,MAAMC,aAAa,SAASF,YAAY,CAAC;EAC5CG,WAAWA,CAACC,UAAU,EAAE;IACpB,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,UAAU,GAAGA,UAAU;EAChC;EACAC,eAAeA,CAACC,KAAK,EAAE;IACnB,OAAO,IAAI,CAACF,UAAU,CAACG,eAAe,CAACD,KAAK,CAAC;EACjD;EACA,IAAIE,MAAMA,CAAA,EAAG;IACT,MAAMC,cAAc,GAAG,IAAI,CAACL,UAAU,CAACM,YAAY,CAAC,CAAC;IACrD,MAAMC,WAAW,GAAG,IAAI,CAACP,UAAU,CAACQ,aAAa,CAACH,cAAc,CAAC;IACjE,OAAO,IAAIR,UAAU,CAACQ,cAAc,GAAG,CAAC,EAAEE,WAAW,CAAC;EAC1D;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}