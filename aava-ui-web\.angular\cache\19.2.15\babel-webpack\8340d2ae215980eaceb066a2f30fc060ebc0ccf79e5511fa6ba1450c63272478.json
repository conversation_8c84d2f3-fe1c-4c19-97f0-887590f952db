{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { ObjectTreeModel } from './objectTreeModel.js';\nimport { TreeError, WeakMapper } from './tree.js';\nimport { equals } from '../../../common/arrays.js';\nimport { Event } from '../../../common/event.js';\nimport { Iterable } from '../../../common/iterator.js';\nfunction noCompress(element) {\n  const elements = [element.element];\n  const incompressible = element.incompressible || false;\n  return {\n    element: {\n      elements,\n      incompressible\n    },\n    children: Iterable.map(Iterable.from(element.children), noCompress),\n    collapsible: element.collapsible,\n    collapsed: element.collapsed\n  };\n}\n// Exported only for test reasons, do not use directly\nexport function compress(element) {\n  const elements = [element.element];\n  const incompressible = element.incompressible || false;\n  let childrenIterator;\n  let children;\n  while (true) {\n    [children, childrenIterator] = Iterable.consume(Iterable.from(element.children), 2);\n    if (children.length !== 1) {\n      break;\n    }\n    if (children[0].incompressible) {\n      break;\n    }\n    element = children[0];\n    elements.push(element.element);\n  }\n  return {\n    element: {\n      elements,\n      incompressible\n    },\n    children: Iterable.map(Iterable.concat(children, childrenIterator), compress),\n    collapsible: element.collapsible,\n    collapsed: element.collapsed\n  };\n}\nfunction _decompress(element, index = 0) {\n  let children;\n  if (index < element.element.elements.length - 1) {\n    children = [_decompress(element, index + 1)];\n  } else {\n    children = Iterable.map(Iterable.from(element.children), el => _decompress(el, 0));\n  }\n  if (index === 0 && element.element.incompressible) {\n    return {\n      element: element.element.elements[index],\n      children,\n      incompressible: true,\n      collapsible: element.collapsible,\n      collapsed: element.collapsed\n    };\n  }\n  return {\n    element: element.element.elements[index],\n    children,\n    collapsible: element.collapsible,\n    collapsed: element.collapsed\n  };\n}\n// Exported only for test reasons, do not use directly\nexport function decompress(element) {\n  return _decompress(element, 0);\n}\nfunction splice(treeElement, element, children) {\n  if (treeElement.element === element) {\n    return {\n      ...treeElement,\n      children\n    };\n  }\n  return {\n    ...treeElement,\n    children: Iterable.map(Iterable.from(treeElement.children), e => splice(e, element, children))\n  };\n}\nconst wrapIdentityProvider = base => ({\n  getId(node) {\n    return node.elements.map(e => base.getId(e).toString()).join('\\0');\n  }\n});\n// Exported only for test reasons, do not use directly\nexport class CompressedObjectTreeModel {\n  get onDidSplice() {\n    return this.model.onDidSplice;\n  }\n  get onDidChangeCollapseState() {\n    return this.model.onDidChangeCollapseState;\n  }\n  get onDidChangeRenderNodeCount() {\n    return this.model.onDidChangeRenderNodeCount;\n  }\n  constructor(user, list, options = {}) {\n    this.user = user;\n    this.rootRef = null;\n    this.nodes = new Map();\n    this.model = new ObjectTreeModel(user, list, options);\n    this.enabled = typeof options.compressionEnabled === 'undefined' ? true : options.compressionEnabled;\n    this.identityProvider = options.identityProvider;\n  }\n  setChildren(element, children = Iterable.empty(), options) {\n    // Diffs must be deep, since the compression can affect nested elements.\n    // @see https://github.com/microsoft/vscode/pull/114237#issuecomment-759425034\n    const diffIdentityProvider = options.diffIdentityProvider && wrapIdentityProvider(options.diffIdentityProvider);\n    if (element === null) {\n      const compressedChildren = Iterable.map(children, this.enabled ? compress : noCompress);\n      this._setChildren(null, compressedChildren, {\n        diffIdentityProvider,\n        diffDepth: Infinity\n      });\n      return;\n    }\n    const compressedNode = this.nodes.get(element);\n    if (!compressedNode) {\n      throw new TreeError(this.user, 'Unknown compressed tree node');\n    }\n    const node = this.model.getNode(compressedNode);\n    const compressedParentNode = this.model.getParentNodeLocation(compressedNode);\n    const parent = this.model.getNode(compressedParentNode);\n    const decompressedElement = decompress(node);\n    const splicedElement = splice(decompressedElement, element, children);\n    const recompressedElement = (this.enabled ? compress : noCompress)(splicedElement);\n    // If the recompressed node is identical to the original, just set its children.\n    // Saves work and churn diffing the parent element.\n    const elementComparator = options.diffIdentityProvider ? (a, b) => options.diffIdentityProvider.getId(a) === options.diffIdentityProvider.getId(b) : undefined;\n    if (equals(recompressedElement.element.elements, node.element.elements, elementComparator)) {\n      this._setChildren(compressedNode, recompressedElement.children || Iterable.empty(), {\n        diffIdentityProvider,\n        diffDepth: 1\n      });\n      return;\n    }\n    const parentChildren = parent.children.map(child => child === node ? recompressedElement : child);\n    this._setChildren(parent.element, parentChildren, {\n      diffIdentityProvider,\n      diffDepth: node.depth - parent.depth\n    });\n  }\n  isCompressionEnabled() {\n    return this.enabled;\n  }\n  setCompressionEnabled(enabled) {\n    if (enabled === this.enabled) {\n      return;\n    }\n    this.enabled = enabled;\n    const root = this.model.getNode();\n    const rootChildren = root.children;\n    const decompressedRootChildren = Iterable.map(rootChildren, decompress);\n    const recompressedRootChildren = Iterable.map(decompressedRootChildren, enabled ? compress : noCompress);\n    // it should be safe to always use deep diff mode here if an identity\n    // provider is available, since we know the raw nodes are unchanged.\n    this._setChildren(null, recompressedRootChildren, {\n      diffIdentityProvider: this.identityProvider,\n      diffDepth: Infinity\n    });\n  }\n  _setChildren(node, children, options) {\n    const insertedElements = new Set();\n    const onDidCreateNode = node => {\n      for (const element of node.element.elements) {\n        insertedElements.add(element);\n        this.nodes.set(element, node.element);\n      }\n    };\n    const onDidDeleteNode = node => {\n      for (const element of node.element.elements) {\n        if (!insertedElements.has(element)) {\n          this.nodes.delete(element);\n        }\n      }\n    };\n    this.model.setChildren(node, children, {\n      ...options,\n      onDidCreateNode,\n      onDidDeleteNode\n    });\n  }\n  has(element) {\n    return this.nodes.has(element);\n  }\n  getListIndex(location) {\n    const node = this.getCompressedNode(location);\n    return this.model.getListIndex(node);\n  }\n  getListRenderCount(location) {\n    const node = this.getCompressedNode(location);\n    return this.model.getListRenderCount(node);\n  }\n  getNode(location) {\n    if (typeof location === 'undefined') {\n      return this.model.getNode();\n    }\n    const node = this.getCompressedNode(location);\n    return this.model.getNode(node);\n  }\n  // TODO: review this\n  getNodeLocation(node) {\n    const compressedNode = this.model.getNodeLocation(node);\n    if (compressedNode === null) {\n      return null;\n    }\n    return compressedNode.elements[compressedNode.elements.length - 1];\n  }\n  // TODO: review this\n  getParentNodeLocation(location) {\n    const compressedNode = this.getCompressedNode(location);\n    const parentNode = this.model.getParentNodeLocation(compressedNode);\n    if (parentNode === null) {\n      return null;\n    }\n    return parentNode.elements[parentNode.elements.length - 1];\n  }\n  getFirstElementChild(location) {\n    const compressedNode = this.getCompressedNode(location);\n    return this.model.getFirstElementChild(compressedNode);\n  }\n  isCollapsible(location) {\n    const compressedNode = this.getCompressedNode(location);\n    return this.model.isCollapsible(compressedNode);\n  }\n  setCollapsible(location, collapsible) {\n    const compressedNode = this.getCompressedNode(location);\n    return this.model.setCollapsible(compressedNode, collapsible);\n  }\n  isCollapsed(location) {\n    const compressedNode = this.getCompressedNode(location);\n    return this.model.isCollapsed(compressedNode);\n  }\n  setCollapsed(location, collapsed, recursive) {\n    const compressedNode = this.getCompressedNode(location);\n    return this.model.setCollapsed(compressedNode, collapsed, recursive);\n  }\n  expandTo(location) {\n    const compressedNode = this.getCompressedNode(location);\n    this.model.expandTo(compressedNode);\n  }\n  rerender(location) {\n    const compressedNode = this.getCompressedNode(location);\n    this.model.rerender(compressedNode);\n  }\n  refilter() {\n    this.model.refilter();\n  }\n  getCompressedNode(element) {\n    if (element === null) {\n      return null;\n    }\n    const node = this.nodes.get(element);\n    if (!node) {\n      throw new TreeError(this.user, `Tree element not found: ${element}`);\n    }\n    return node;\n  }\n}\nexport const DefaultElementMapper = elements => elements[elements.length - 1];\nclass CompressedTreeNodeWrapper {\n  get element() {\n    return this.node.element === null ? null : this.unwrapper(this.node.element);\n  }\n  get children() {\n    return this.node.children.map(node => new CompressedTreeNodeWrapper(this.unwrapper, node));\n  }\n  get depth() {\n    return this.node.depth;\n  }\n  get visibleChildrenCount() {\n    return this.node.visibleChildrenCount;\n  }\n  get visibleChildIndex() {\n    return this.node.visibleChildIndex;\n  }\n  get collapsible() {\n    return this.node.collapsible;\n  }\n  get collapsed() {\n    return this.node.collapsed;\n  }\n  get visible() {\n    return this.node.visible;\n  }\n  get filterData() {\n    return this.node.filterData;\n  }\n  constructor(unwrapper, node) {\n    this.unwrapper = unwrapper;\n    this.node = node;\n  }\n}\nfunction mapList(nodeMapper, list) {\n  return {\n    splice(start, deleteCount, toInsert) {\n      list.splice(start, deleteCount, toInsert.map(node => nodeMapper.map(node)));\n    },\n    updateElementHeight(index, height) {\n      list.updateElementHeight(index, height);\n    }\n  };\n}\nfunction mapOptions(compressedNodeUnwrapper, options) {\n  return {\n    ...options,\n    identityProvider: options.identityProvider && {\n      getId(node) {\n        return options.identityProvider.getId(compressedNodeUnwrapper(node));\n      }\n    },\n    sorter: options.sorter && {\n      compare(node, otherNode) {\n        return options.sorter.compare(node.elements[0], otherNode.elements[0]);\n      }\n    },\n    filter: options.filter && {\n      filter(node, parentVisibility) {\n        return options.filter.filter(compressedNodeUnwrapper(node), parentVisibility);\n      }\n    }\n  };\n}\nexport class CompressibleObjectTreeModel {\n  get onDidSplice() {\n    return Event.map(this.model.onDidSplice, ({\n      insertedNodes,\n      deletedNodes\n    }) => ({\n      insertedNodes: insertedNodes.map(node => this.nodeMapper.map(node)),\n      deletedNodes: deletedNodes.map(node => this.nodeMapper.map(node))\n    }));\n  }\n  get onDidChangeCollapseState() {\n    return Event.map(this.model.onDidChangeCollapseState, ({\n      node,\n      deep\n    }) => ({\n      node: this.nodeMapper.map(node),\n      deep\n    }));\n  }\n  get onDidChangeRenderNodeCount() {\n    return Event.map(this.model.onDidChangeRenderNodeCount, node => this.nodeMapper.map(node));\n  }\n  constructor(user, list, options = {}) {\n    this.rootRef = null;\n    this.elementMapper = options.elementMapper || DefaultElementMapper;\n    const compressedNodeUnwrapper = node => this.elementMapper(node.elements);\n    this.nodeMapper = new WeakMapper(node => new CompressedTreeNodeWrapper(compressedNodeUnwrapper, node));\n    this.model = new CompressedObjectTreeModel(user, mapList(this.nodeMapper, list), mapOptions(compressedNodeUnwrapper, options));\n  }\n  setChildren(element, children = Iterable.empty(), options = {}) {\n    this.model.setChildren(element, children, options);\n  }\n  isCompressionEnabled() {\n    return this.model.isCompressionEnabled();\n  }\n  setCompressionEnabled(enabled) {\n    this.model.setCompressionEnabled(enabled);\n  }\n  has(location) {\n    return this.model.has(location);\n  }\n  getListIndex(location) {\n    return this.model.getListIndex(location);\n  }\n  getListRenderCount(location) {\n    return this.model.getListRenderCount(location);\n  }\n  getNode(location) {\n    return this.nodeMapper.map(this.model.getNode(location));\n  }\n  getNodeLocation(node) {\n    return node.element;\n  }\n  getParentNodeLocation(location) {\n    return this.model.getParentNodeLocation(location);\n  }\n  getFirstElementChild(location) {\n    const result = this.model.getFirstElementChild(location);\n    if (result === null || typeof result === 'undefined') {\n      return result;\n    }\n    return this.elementMapper(result.elements);\n  }\n  isCollapsible(location) {\n    return this.model.isCollapsible(location);\n  }\n  setCollapsible(location, collapsed) {\n    return this.model.setCollapsible(location, collapsed);\n  }\n  isCollapsed(location) {\n    return this.model.isCollapsed(location);\n  }\n  setCollapsed(location, collapsed, recursive) {\n    return this.model.setCollapsed(location, collapsed, recursive);\n  }\n  expandTo(location) {\n    return this.model.expandTo(location);\n  }\n  rerender(location) {\n    return this.model.rerender(location);\n  }\n  refilter() {\n    return this.model.refilter();\n  }\n  getCompressedTreeNode(location = null) {\n    return this.model.getNode(location);\n  }\n}", "map": {"version": 3, "names": ["ObjectTreeModel", "TreeError", "WeakMapper", "equals", "Event", "Iterable", "noCompress", "element", "elements", "incompressible", "children", "map", "from", "collapsible", "collapsed", "compress", "childrenIterator", "consume", "length", "push", "concat", "_decompress", "index", "el", "decompress", "splice", "treeElement", "e", "wrapIdentityProvider", "base", "getId", "node", "toString", "join", "CompressedObjectTreeModel", "onDidSplice", "model", "onDidChangeCollapseState", "onDidChangeRenderNodeCount", "constructor", "user", "list", "options", "rootRef", "nodes", "Map", "enabled", "compressionEnabled", "identity<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "empty", "diffIdentityProvider", "compressedC<PERSON><PERSON>n", "_set<PERSON><PERSON><PERSON>n", "diff<PERSON><PERSON>h", "Infinity", "compressedNode", "get", "getNode", "compressedParentNode", "getParentNodeLocation", "parent", "decompressedElement", "splicedElement", "recompressedElement", "elementComparator", "a", "b", "undefined", "parent<PERSON><PERSON><PERSON><PERSON>", "child", "depth", "isCompressionEnabled", "setCompressionEnabled", "root", "rootChildren", "decompressedRootChildren", "recompressed<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "insertedElements", "Set", "onDidCreateNode", "add", "set", "onDidDeleteNode", "has", "delete", "getListIndex", "location", "getCompressedNode", "getListRenderCount", "getNodeLocation", "parentNode", "getFirstElementChild", "isCollapsible", "setCollapsible", "isCollapsed", "setCollapsed", "recursive", "expandTo", "rerender", "refilter", "DefaultElementMapper", "CompressedTreeNodeWrapper", "unwrapper", "visible<PERSON><PERSON><PERSON>n<PERSON>ount", "visibleChildIndex", "visible", "filterData", "mapList", "nodeMapper", "start", "deleteCount", "toInsert", "updateElementHeight", "height", "mapOptions", "compressedNodeUnwrapper", "sorter", "compare", "otherNode", "filter", "parentVisibility", "CompressibleObjectTreeModel", "insertedNodes", "deletedNodes", "deep", "elementMapper", "result", "getCompressedTreeNode"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/browser/ui/tree/compressedObjectTreeModel.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { ObjectTreeModel } from './objectTreeModel.js';\nimport { TreeError, WeakMapper } from './tree.js';\nimport { equals } from '../../../common/arrays.js';\nimport { Event } from '../../../common/event.js';\nimport { Iterable } from '../../../common/iterator.js';\nfunction noCompress(element) {\n    const elements = [element.element];\n    const incompressible = element.incompressible || false;\n    return {\n        element: { elements, incompressible },\n        children: Iterable.map(Iterable.from(element.children), noCompress),\n        collapsible: element.collapsible,\n        collapsed: element.collapsed\n    };\n}\n// Exported only for test reasons, do not use directly\nexport function compress(element) {\n    const elements = [element.element];\n    const incompressible = element.incompressible || false;\n    let childrenIterator;\n    let children;\n    while (true) {\n        [children, childrenIterator] = Iterable.consume(Iterable.from(element.children), 2);\n        if (children.length !== 1) {\n            break;\n        }\n        if (children[0].incompressible) {\n            break;\n        }\n        element = children[0];\n        elements.push(element.element);\n    }\n    return {\n        element: { elements, incompressible },\n        children: Iterable.map(Iterable.concat(children, childrenIterator), compress),\n        collapsible: element.collapsible,\n        collapsed: element.collapsed\n    };\n}\nfunction _decompress(element, index = 0) {\n    let children;\n    if (index < element.element.elements.length - 1) {\n        children = [_decompress(element, index + 1)];\n    }\n    else {\n        children = Iterable.map(Iterable.from(element.children), el => _decompress(el, 0));\n    }\n    if (index === 0 && element.element.incompressible) {\n        return {\n            element: element.element.elements[index],\n            children,\n            incompressible: true,\n            collapsible: element.collapsible,\n            collapsed: element.collapsed\n        };\n    }\n    return {\n        element: element.element.elements[index],\n        children,\n        collapsible: element.collapsible,\n        collapsed: element.collapsed\n    };\n}\n// Exported only for test reasons, do not use directly\nexport function decompress(element) {\n    return _decompress(element, 0);\n}\nfunction splice(treeElement, element, children) {\n    if (treeElement.element === element) {\n        return { ...treeElement, children };\n    }\n    return { ...treeElement, children: Iterable.map(Iterable.from(treeElement.children), e => splice(e, element, children)) };\n}\nconst wrapIdentityProvider = (base) => ({\n    getId(node) {\n        return node.elements.map(e => base.getId(e).toString()).join('\\0');\n    }\n});\n// Exported only for test reasons, do not use directly\nexport class CompressedObjectTreeModel {\n    get onDidSplice() { return this.model.onDidSplice; }\n    get onDidChangeCollapseState() { return this.model.onDidChangeCollapseState; }\n    get onDidChangeRenderNodeCount() { return this.model.onDidChangeRenderNodeCount; }\n    constructor(user, list, options = {}) {\n        this.user = user;\n        this.rootRef = null;\n        this.nodes = new Map();\n        this.model = new ObjectTreeModel(user, list, options);\n        this.enabled = typeof options.compressionEnabled === 'undefined' ? true : options.compressionEnabled;\n        this.identityProvider = options.identityProvider;\n    }\n    setChildren(element, children = Iterable.empty(), options) {\n        // Diffs must be deep, since the compression can affect nested elements.\n        // @see https://github.com/microsoft/vscode/pull/114237#issuecomment-759425034\n        const diffIdentityProvider = options.diffIdentityProvider && wrapIdentityProvider(options.diffIdentityProvider);\n        if (element === null) {\n            const compressedChildren = Iterable.map(children, this.enabled ? compress : noCompress);\n            this._setChildren(null, compressedChildren, { diffIdentityProvider, diffDepth: Infinity });\n            return;\n        }\n        const compressedNode = this.nodes.get(element);\n        if (!compressedNode) {\n            throw new TreeError(this.user, 'Unknown compressed tree node');\n        }\n        const node = this.model.getNode(compressedNode);\n        const compressedParentNode = this.model.getParentNodeLocation(compressedNode);\n        const parent = this.model.getNode(compressedParentNode);\n        const decompressedElement = decompress(node);\n        const splicedElement = splice(decompressedElement, element, children);\n        const recompressedElement = (this.enabled ? compress : noCompress)(splicedElement);\n        // If the recompressed node is identical to the original, just set its children.\n        // Saves work and churn diffing the parent element.\n        const elementComparator = options.diffIdentityProvider\n            ? ((a, b) => options.diffIdentityProvider.getId(a) === options.diffIdentityProvider.getId(b))\n            : undefined;\n        if (equals(recompressedElement.element.elements, node.element.elements, elementComparator)) {\n            this._setChildren(compressedNode, recompressedElement.children || Iterable.empty(), { diffIdentityProvider, diffDepth: 1 });\n            return;\n        }\n        const parentChildren = parent.children\n            .map(child => child === node ? recompressedElement : child);\n        this._setChildren(parent.element, parentChildren, {\n            diffIdentityProvider,\n            diffDepth: node.depth - parent.depth,\n        });\n    }\n    isCompressionEnabled() {\n        return this.enabled;\n    }\n    setCompressionEnabled(enabled) {\n        if (enabled === this.enabled) {\n            return;\n        }\n        this.enabled = enabled;\n        const root = this.model.getNode();\n        const rootChildren = root.children;\n        const decompressedRootChildren = Iterable.map(rootChildren, decompress);\n        const recompressedRootChildren = Iterable.map(decompressedRootChildren, enabled ? compress : noCompress);\n        // it should be safe to always use deep diff mode here if an identity\n        // provider is available, since we know the raw nodes are unchanged.\n        this._setChildren(null, recompressedRootChildren, {\n            diffIdentityProvider: this.identityProvider,\n            diffDepth: Infinity,\n        });\n    }\n    _setChildren(node, children, options) {\n        const insertedElements = new Set();\n        const onDidCreateNode = (node) => {\n            for (const element of node.element.elements) {\n                insertedElements.add(element);\n                this.nodes.set(element, node.element);\n            }\n        };\n        const onDidDeleteNode = (node) => {\n            for (const element of node.element.elements) {\n                if (!insertedElements.has(element)) {\n                    this.nodes.delete(element);\n                }\n            }\n        };\n        this.model.setChildren(node, children, { ...options, onDidCreateNode, onDidDeleteNode });\n    }\n    has(element) {\n        return this.nodes.has(element);\n    }\n    getListIndex(location) {\n        const node = this.getCompressedNode(location);\n        return this.model.getListIndex(node);\n    }\n    getListRenderCount(location) {\n        const node = this.getCompressedNode(location);\n        return this.model.getListRenderCount(node);\n    }\n    getNode(location) {\n        if (typeof location === 'undefined') {\n            return this.model.getNode();\n        }\n        const node = this.getCompressedNode(location);\n        return this.model.getNode(node);\n    }\n    // TODO: review this\n    getNodeLocation(node) {\n        const compressedNode = this.model.getNodeLocation(node);\n        if (compressedNode === null) {\n            return null;\n        }\n        return compressedNode.elements[compressedNode.elements.length - 1];\n    }\n    // TODO: review this\n    getParentNodeLocation(location) {\n        const compressedNode = this.getCompressedNode(location);\n        const parentNode = this.model.getParentNodeLocation(compressedNode);\n        if (parentNode === null) {\n            return null;\n        }\n        return parentNode.elements[parentNode.elements.length - 1];\n    }\n    getFirstElementChild(location) {\n        const compressedNode = this.getCompressedNode(location);\n        return this.model.getFirstElementChild(compressedNode);\n    }\n    isCollapsible(location) {\n        const compressedNode = this.getCompressedNode(location);\n        return this.model.isCollapsible(compressedNode);\n    }\n    setCollapsible(location, collapsible) {\n        const compressedNode = this.getCompressedNode(location);\n        return this.model.setCollapsible(compressedNode, collapsible);\n    }\n    isCollapsed(location) {\n        const compressedNode = this.getCompressedNode(location);\n        return this.model.isCollapsed(compressedNode);\n    }\n    setCollapsed(location, collapsed, recursive) {\n        const compressedNode = this.getCompressedNode(location);\n        return this.model.setCollapsed(compressedNode, collapsed, recursive);\n    }\n    expandTo(location) {\n        const compressedNode = this.getCompressedNode(location);\n        this.model.expandTo(compressedNode);\n    }\n    rerender(location) {\n        const compressedNode = this.getCompressedNode(location);\n        this.model.rerender(compressedNode);\n    }\n    refilter() {\n        this.model.refilter();\n    }\n    getCompressedNode(element) {\n        if (element === null) {\n            return null;\n        }\n        const node = this.nodes.get(element);\n        if (!node) {\n            throw new TreeError(this.user, `Tree element not found: ${element}`);\n        }\n        return node;\n    }\n}\nexport const DefaultElementMapper = elements => elements[elements.length - 1];\nclass CompressedTreeNodeWrapper {\n    get element() { return this.node.element === null ? null : this.unwrapper(this.node.element); }\n    get children() { return this.node.children.map(node => new CompressedTreeNodeWrapper(this.unwrapper, node)); }\n    get depth() { return this.node.depth; }\n    get visibleChildrenCount() { return this.node.visibleChildrenCount; }\n    get visibleChildIndex() { return this.node.visibleChildIndex; }\n    get collapsible() { return this.node.collapsible; }\n    get collapsed() { return this.node.collapsed; }\n    get visible() { return this.node.visible; }\n    get filterData() { return this.node.filterData; }\n    constructor(unwrapper, node) {\n        this.unwrapper = unwrapper;\n        this.node = node;\n    }\n}\nfunction mapList(nodeMapper, list) {\n    return {\n        splice(start, deleteCount, toInsert) {\n            list.splice(start, deleteCount, toInsert.map(node => nodeMapper.map(node)));\n        },\n        updateElementHeight(index, height) {\n            list.updateElementHeight(index, height);\n        }\n    };\n}\nfunction mapOptions(compressedNodeUnwrapper, options) {\n    return {\n        ...options,\n        identityProvider: options.identityProvider && {\n            getId(node) {\n                return options.identityProvider.getId(compressedNodeUnwrapper(node));\n            }\n        },\n        sorter: options.sorter && {\n            compare(node, otherNode) {\n                return options.sorter.compare(node.elements[0], otherNode.elements[0]);\n            }\n        },\n        filter: options.filter && {\n            filter(node, parentVisibility) {\n                return options.filter.filter(compressedNodeUnwrapper(node), parentVisibility);\n            }\n        }\n    };\n}\nexport class CompressibleObjectTreeModel {\n    get onDidSplice() {\n        return Event.map(this.model.onDidSplice, ({ insertedNodes, deletedNodes }) => ({\n            insertedNodes: insertedNodes.map(node => this.nodeMapper.map(node)),\n            deletedNodes: deletedNodes.map(node => this.nodeMapper.map(node)),\n        }));\n    }\n    get onDidChangeCollapseState() {\n        return Event.map(this.model.onDidChangeCollapseState, ({ node, deep }) => ({\n            node: this.nodeMapper.map(node),\n            deep\n        }));\n    }\n    get onDidChangeRenderNodeCount() {\n        return Event.map(this.model.onDidChangeRenderNodeCount, node => this.nodeMapper.map(node));\n    }\n    constructor(user, list, options = {}) {\n        this.rootRef = null;\n        this.elementMapper = options.elementMapper || DefaultElementMapper;\n        const compressedNodeUnwrapper = node => this.elementMapper(node.elements);\n        this.nodeMapper = new WeakMapper(node => new CompressedTreeNodeWrapper(compressedNodeUnwrapper, node));\n        this.model = new CompressedObjectTreeModel(user, mapList(this.nodeMapper, list), mapOptions(compressedNodeUnwrapper, options));\n    }\n    setChildren(element, children = Iterable.empty(), options = {}) {\n        this.model.setChildren(element, children, options);\n    }\n    isCompressionEnabled() {\n        return this.model.isCompressionEnabled();\n    }\n    setCompressionEnabled(enabled) {\n        this.model.setCompressionEnabled(enabled);\n    }\n    has(location) {\n        return this.model.has(location);\n    }\n    getListIndex(location) {\n        return this.model.getListIndex(location);\n    }\n    getListRenderCount(location) {\n        return this.model.getListRenderCount(location);\n    }\n    getNode(location) {\n        return this.nodeMapper.map(this.model.getNode(location));\n    }\n    getNodeLocation(node) {\n        return node.element;\n    }\n    getParentNodeLocation(location) {\n        return this.model.getParentNodeLocation(location);\n    }\n    getFirstElementChild(location) {\n        const result = this.model.getFirstElementChild(location);\n        if (result === null || typeof result === 'undefined') {\n            return result;\n        }\n        return this.elementMapper(result.elements);\n    }\n    isCollapsible(location) {\n        return this.model.isCollapsible(location);\n    }\n    setCollapsible(location, collapsed) {\n        return this.model.setCollapsible(location, collapsed);\n    }\n    isCollapsed(location) {\n        return this.model.isCollapsed(location);\n    }\n    setCollapsed(location, collapsed, recursive) {\n        return this.model.setCollapsed(location, collapsed, recursive);\n    }\n    expandTo(location) {\n        return this.model.expandTo(location);\n    }\n    rerender(location) {\n        return this.model.rerender(location);\n    }\n    refilter() {\n        return this.model.refilter();\n    }\n    getCompressedTreeNode(location = null) {\n        return this.model.getNode(location);\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,eAAe,QAAQ,sBAAsB;AACtD,SAASC,SAAS,EAAEC,UAAU,QAAQ,WAAW;AACjD,SAASC,MAAM,QAAQ,2BAA2B;AAClD,SAASC,KAAK,QAAQ,0BAA0B;AAChD,SAASC,QAAQ,QAAQ,6BAA6B;AACtD,SAASC,UAAUA,CAACC,OAAO,EAAE;EACzB,MAAMC,QAAQ,GAAG,CAACD,OAAO,CAACA,OAAO,CAAC;EAClC,MAAME,cAAc,GAAGF,OAAO,CAACE,cAAc,IAAI,KAAK;EACtD,OAAO;IACHF,OAAO,EAAE;MAAEC,QAAQ;MAAEC;IAAe,CAAC;IACrCC,QAAQ,EAAEL,QAAQ,CAACM,GAAG,CAACN,QAAQ,CAACO,IAAI,CAACL,OAAO,CAACG,QAAQ,CAAC,EAAEJ,UAAU,CAAC;IACnEO,WAAW,EAAEN,OAAO,CAACM,WAAW;IAChCC,SAAS,EAAEP,OAAO,CAACO;EACvB,CAAC;AACL;AACA;AACA,OAAO,SAASC,QAAQA,CAACR,OAAO,EAAE;EAC9B,MAAMC,QAAQ,GAAG,CAACD,OAAO,CAACA,OAAO,CAAC;EAClC,MAAME,cAAc,GAAGF,OAAO,CAACE,cAAc,IAAI,KAAK;EACtD,IAAIO,gBAAgB;EACpB,IAAIN,QAAQ;EACZ,OAAO,IAAI,EAAE;IACT,CAACA,QAAQ,EAAEM,gBAAgB,CAAC,GAAGX,QAAQ,CAACY,OAAO,CAACZ,QAAQ,CAACO,IAAI,CAACL,OAAO,CAACG,QAAQ,CAAC,EAAE,CAAC,CAAC;IACnF,IAAIA,QAAQ,CAACQ,MAAM,KAAK,CAAC,EAAE;MACvB;IACJ;IACA,IAAIR,QAAQ,CAAC,CAAC,CAAC,CAACD,cAAc,EAAE;MAC5B;IACJ;IACAF,OAAO,GAAGG,QAAQ,CAAC,CAAC,CAAC;IACrBF,QAAQ,CAACW,IAAI,CAACZ,OAAO,CAACA,OAAO,CAAC;EAClC;EACA,OAAO;IACHA,OAAO,EAAE;MAAEC,QAAQ;MAAEC;IAAe,CAAC;IACrCC,QAAQ,EAAEL,QAAQ,CAACM,GAAG,CAACN,QAAQ,CAACe,MAAM,CAACV,QAAQ,EAAEM,gBAAgB,CAAC,EAAED,QAAQ,CAAC;IAC7EF,WAAW,EAAEN,OAAO,CAACM,WAAW;IAChCC,SAAS,EAAEP,OAAO,CAACO;EACvB,CAAC;AACL;AACA,SAASO,WAAWA,CAACd,OAAO,EAAEe,KAAK,GAAG,CAAC,EAAE;EACrC,IAAIZ,QAAQ;EACZ,IAAIY,KAAK,GAAGf,OAAO,CAACA,OAAO,CAACC,QAAQ,CAACU,MAAM,GAAG,CAAC,EAAE;IAC7CR,QAAQ,GAAG,CAACW,WAAW,CAACd,OAAO,EAAEe,KAAK,GAAG,CAAC,CAAC,CAAC;EAChD,CAAC,MACI;IACDZ,QAAQ,GAAGL,QAAQ,CAACM,GAAG,CAACN,QAAQ,CAACO,IAAI,CAACL,OAAO,CAACG,QAAQ,CAAC,EAAEa,EAAE,IAAIF,WAAW,CAACE,EAAE,EAAE,CAAC,CAAC,CAAC;EACtF;EACA,IAAID,KAAK,KAAK,CAAC,IAAIf,OAAO,CAACA,OAAO,CAACE,cAAc,EAAE;IAC/C,OAAO;MACHF,OAAO,EAAEA,OAAO,CAACA,OAAO,CAACC,QAAQ,CAACc,KAAK,CAAC;MACxCZ,QAAQ;MACRD,cAAc,EAAE,IAAI;MACpBI,WAAW,EAAEN,OAAO,CAACM,WAAW;MAChCC,SAAS,EAAEP,OAAO,CAACO;IACvB,CAAC;EACL;EACA,OAAO;IACHP,OAAO,EAAEA,OAAO,CAACA,OAAO,CAACC,QAAQ,CAACc,KAAK,CAAC;IACxCZ,QAAQ;IACRG,WAAW,EAAEN,OAAO,CAACM,WAAW;IAChCC,SAAS,EAAEP,OAAO,CAACO;EACvB,CAAC;AACL;AACA;AACA,OAAO,SAASU,UAAUA,CAACjB,OAAO,EAAE;EAChC,OAAOc,WAAW,CAACd,OAAO,EAAE,CAAC,CAAC;AAClC;AACA,SAASkB,MAAMA,CAACC,WAAW,EAAEnB,OAAO,EAAEG,QAAQ,EAAE;EAC5C,IAAIgB,WAAW,CAACnB,OAAO,KAAKA,OAAO,EAAE;IACjC,OAAO;MAAE,GAAGmB,WAAW;MAAEhB;IAAS,CAAC;EACvC;EACA,OAAO;IAAE,GAAGgB,WAAW;IAAEhB,QAAQ,EAAEL,QAAQ,CAACM,GAAG,CAACN,QAAQ,CAACO,IAAI,CAACc,WAAW,CAAChB,QAAQ,CAAC,EAAEiB,CAAC,IAAIF,MAAM,CAACE,CAAC,EAAEpB,OAAO,EAAEG,QAAQ,CAAC;EAAE,CAAC;AAC7H;AACA,MAAMkB,oBAAoB,GAAIC,IAAI,KAAM;EACpCC,KAAKA,CAACC,IAAI,EAAE;IACR,OAAOA,IAAI,CAACvB,QAAQ,CAACG,GAAG,CAACgB,CAAC,IAAIE,IAAI,CAACC,KAAK,CAACH,CAAC,CAAC,CAACK,QAAQ,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;EACtE;AACJ,CAAC,CAAC;AACF;AACA,OAAO,MAAMC,yBAAyB,CAAC;EACnC,IAAIC,WAAWA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACC,KAAK,CAACD,WAAW;EAAE;EACnD,IAAIE,wBAAwBA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACD,KAAK,CAACC,wBAAwB;EAAE;EAC7E,IAAIC,0BAA0BA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACF,KAAK,CAACE,0BAA0B;EAAE;EACjFC,WAAWA,CAACC,IAAI,EAAEC,IAAI,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IAClC,IAAI,CAACF,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACG,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,KAAK,GAAG,IAAIC,GAAG,CAAC,CAAC;IACtB,IAAI,CAACT,KAAK,GAAG,IAAIpC,eAAe,CAACwC,IAAI,EAAEC,IAAI,EAAEC,OAAO,CAAC;IACrD,IAAI,CAACI,OAAO,GAAG,OAAOJ,OAAO,CAACK,kBAAkB,KAAK,WAAW,GAAG,IAAI,GAAGL,OAAO,CAACK,kBAAkB;IACpG,IAAI,CAACC,gBAAgB,GAAGN,OAAO,CAACM,gBAAgB;EACpD;EACAC,WAAWA,CAAC1C,OAAO,EAAEG,QAAQ,GAAGL,QAAQ,CAAC6C,KAAK,CAAC,CAAC,EAAER,OAAO,EAAE;IACvD;IACA;IACA,MAAMS,oBAAoB,GAAGT,OAAO,CAACS,oBAAoB,IAAIvB,oBAAoB,CAACc,OAAO,CAACS,oBAAoB,CAAC;IAC/G,IAAI5C,OAAO,KAAK,IAAI,EAAE;MAClB,MAAM6C,kBAAkB,GAAG/C,QAAQ,CAACM,GAAG,CAACD,QAAQ,EAAE,IAAI,CAACoC,OAAO,GAAG/B,QAAQ,GAAGT,UAAU,CAAC;MACvF,IAAI,CAAC+C,YAAY,CAAC,IAAI,EAAED,kBAAkB,EAAE;QAAED,oBAAoB;QAAEG,SAAS,EAAEC;MAAS,CAAC,CAAC;MAC1F;IACJ;IACA,MAAMC,cAAc,GAAG,IAAI,CAACZ,KAAK,CAACa,GAAG,CAAClD,OAAO,CAAC;IAC9C,IAAI,CAACiD,cAAc,EAAE;MACjB,MAAM,IAAIvD,SAAS,CAAC,IAAI,CAACuC,IAAI,EAAE,8BAA8B,CAAC;IAClE;IACA,MAAMT,IAAI,GAAG,IAAI,CAACK,KAAK,CAACsB,OAAO,CAACF,cAAc,CAAC;IAC/C,MAAMG,oBAAoB,GAAG,IAAI,CAACvB,KAAK,CAACwB,qBAAqB,CAACJ,cAAc,CAAC;IAC7E,MAAMK,MAAM,GAAG,IAAI,CAACzB,KAAK,CAACsB,OAAO,CAACC,oBAAoB,CAAC;IACvD,MAAMG,mBAAmB,GAAGtC,UAAU,CAACO,IAAI,CAAC;IAC5C,MAAMgC,cAAc,GAAGtC,MAAM,CAACqC,mBAAmB,EAAEvD,OAAO,EAAEG,QAAQ,CAAC;IACrE,MAAMsD,mBAAmB,GAAG,CAAC,IAAI,CAAClB,OAAO,GAAG/B,QAAQ,GAAGT,UAAU,EAAEyD,cAAc,CAAC;IAClF;IACA;IACA,MAAME,iBAAiB,GAAGvB,OAAO,CAACS,oBAAoB,GAC/C,CAACe,CAAC,EAAEC,CAAC,KAAKzB,OAAO,CAACS,oBAAoB,CAACrB,KAAK,CAACoC,CAAC,CAAC,KAAKxB,OAAO,CAACS,oBAAoB,CAACrB,KAAK,CAACqC,CAAC,CAAC,GAC1FC,SAAS;IACf,IAAIjE,MAAM,CAAC6D,mBAAmB,CAACzD,OAAO,CAACC,QAAQ,EAAEuB,IAAI,CAACxB,OAAO,CAACC,QAAQ,EAAEyD,iBAAiB,CAAC,EAAE;MACxF,IAAI,CAACZ,YAAY,CAACG,cAAc,EAAEQ,mBAAmB,CAACtD,QAAQ,IAAIL,QAAQ,CAAC6C,KAAK,CAAC,CAAC,EAAE;QAAEC,oBAAoB;QAAEG,SAAS,EAAE;MAAE,CAAC,CAAC;MAC3H;IACJ;IACA,MAAMe,cAAc,GAAGR,MAAM,CAACnD,QAAQ,CACjCC,GAAG,CAAC2D,KAAK,IAAIA,KAAK,KAAKvC,IAAI,GAAGiC,mBAAmB,GAAGM,KAAK,CAAC;IAC/D,IAAI,CAACjB,YAAY,CAACQ,MAAM,CAACtD,OAAO,EAAE8D,cAAc,EAAE;MAC9ClB,oBAAoB;MACpBG,SAAS,EAAEvB,IAAI,CAACwC,KAAK,GAAGV,MAAM,CAACU;IACnC,CAAC,CAAC;EACN;EACAC,oBAAoBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAAC1B,OAAO;EACvB;EACA2B,qBAAqBA,CAAC3B,OAAO,EAAE;IAC3B,IAAIA,OAAO,KAAK,IAAI,CAACA,OAAO,EAAE;MAC1B;IACJ;IACA,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,MAAM4B,IAAI,GAAG,IAAI,CAACtC,KAAK,CAACsB,OAAO,CAAC,CAAC;IACjC,MAAMiB,YAAY,GAAGD,IAAI,CAAChE,QAAQ;IAClC,MAAMkE,wBAAwB,GAAGvE,QAAQ,CAACM,GAAG,CAACgE,YAAY,EAAEnD,UAAU,CAAC;IACvE,MAAMqD,wBAAwB,GAAGxE,QAAQ,CAACM,GAAG,CAACiE,wBAAwB,EAAE9B,OAAO,GAAG/B,QAAQ,GAAGT,UAAU,CAAC;IACxG;IACA;IACA,IAAI,CAAC+C,YAAY,CAAC,IAAI,EAAEwB,wBAAwB,EAAE;MAC9C1B,oBAAoB,EAAE,IAAI,CAACH,gBAAgB;MAC3CM,SAAS,EAAEC;IACf,CAAC,CAAC;EACN;EACAF,YAAYA,CAACtB,IAAI,EAAErB,QAAQ,EAAEgC,OAAO,EAAE;IAClC,MAAMoC,gBAAgB,GAAG,IAAIC,GAAG,CAAC,CAAC;IAClC,MAAMC,eAAe,GAAIjD,IAAI,IAAK;MAC9B,KAAK,MAAMxB,OAAO,IAAIwB,IAAI,CAACxB,OAAO,CAACC,QAAQ,EAAE;QACzCsE,gBAAgB,CAACG,GAAG,CAAC1E,OAAO,CAAC;QAC7B,IAAI,CAACqC,KAAK,CAACsC,GAAG,CAAC3E,OAAO,EAAEwB,IAAI,CAACxB,OAAO,CAAC;MACzC;IACJ,CAAC;IACD,MAAM4E,eAAe,GAAIpD,IAAI,IAAK;MAC9B,KAAK,MAAMxB,OAAO,IAAIwB,IAAI,CAACxB,OAAO,CAACC,QAAQ,EAAE;QACzC,IAAI,CAACsE,gBAAgB,CAACM,GAAG,CAAC7E,OAAO,CAAC,EAAE;UAChC,IAAI,CAACqC,KAAK,CAACyC,MAAM,CAAC9E,OAAO,CAAC;QAC9B;MACJ;IACJ,CAAC;IACD,IAAI,CAAC6B,KAAK,CAACa,WAAW,CAAClB,IAAI,EAAErB,QAAQ,EAAE;MAAE,GAAGgC,OAAO;MAAEsC,eAAe;MAAEG;IAAgB,CAAC,CAAC;EAC5F;EACAC,GAAGA,CAAC7E,OAAO,EAAE;IACT,OAAO,IAAI,CAACqC,KAAK,CAACwC,GAAG,CAAC7E,OAAO,CAAC;EAClC;EACA+E,YAAYA,CAACC,QAAQ,EAAE;IACnB,MAAMxD,IAAI,GAAG,IAAI,CAACyD,iBAAiB,CAACD,QAAQ,CAAC;IAC7C,OAAO,IAAI,CAACnD,KAAK,CAACkD,YAAY,CAACvD,IAAI,CAAC;EACxC;EACA0D,kBAAkBA,CAACF,QAAQ,EAAE;IACzB,MAAMxD,IAAI,GAAG,IAAI,CAACyD,iBAAiB,CAACD,QAAQ,CAAC;IAC7C,OAAO,IAAI,CAACnD,KAAK,CAACqD,kBAAkB,CAAC1D,IAAI,CAAC;EAC9C;EACA2B,OAAOA,CAAC6B,QAAQ,EAAE;IACd,IAAI,OAAOA,QAAQ,KAAK,WAAW,EAAE;MACjC,OAAO,IAAI,CAACnD,KAAK,CAACsB,OAAO,CAAC,CAAC;IAC/B;IACA,MAAM3B,IAAI,GAAG,IAAI,CAACyD,iBAAiB,CAACD,QAAQ,CAAC;IAC7C,OAAO,IAAI,CAACnD,KAAK,CAACsB,OAAO,CAAC3B,IAAI,CAAC;EACnC;EACA;EACA2D,eAAeA,CAAC3D,IAAI,EAAE;IAClB,MAAMyB,cAAc,GAAG,IAAI,CAACpB,KAAK,CAACsD,eAAe,CAAC3D,IAAI,CAAC;IACvD,IAAIyB,cAAc,KAAK,IAAI,EAAE;MACzB,OAAO,IAAI;IACf;IACA,OAAOA,cAAc,CAAChD,QAAQ,CAACgD,cAAc,CAAChD,QAAQ,CAACU,MAAM,GAAG,CAAC,CAAC;EACtE;EACA;EACA0C,qBAAqBA,CAAC2B,QAAQ,EAAE;IAC5B,MAAM/B,cAAc,GAAG,IAAI,CAACgC,iBAAiB,CAACD,QAAQ,CAAC;IACvD,MAAMI,UAAU,GAAG,IAAI,CAACvD,KAAK,CAACwB,qBAAqB,CAACJ,cAAc,CAAC;IACnE,IAAImC,UAAU,KAAK,IAAI,EAAE;MACrB,OAAO,IAAI;IACf;IACA,OAAOA,UAAU,CAACnF,QAAQ,CAACmF,UAAU,CAACnF,QAAQ,CAACU,MAAM,GAAG,CAAC,CAAC;EAC9D;EACA0E,oBAAoBA,CAACL,QAAQ,EAAE;IAC3B,MAAM/B,cAAc,GAAG,IAAI,CAACgC,iBAAiB,CAACD,QAAQ,CAAC;IACvD,OAAO,IAAI,CAACnD,KAAK,CAACwD,oBAAoB,CAACpC,cAAc,CAAC;EAC1D;EACAqC,aAAaA,CAACN,QAAQ,EAAE;IACpB,MAAM/B,cAAc,GAAG,IAAI,CAACgC,iBAAiB,CAACD,QAAQ,CAAC;IACvD,OAAO,IAAI,CAACnD,KAAK,CAACyD,aAAa,CAACrC,cAAc,CAAC;EACnD;EACAsC,cAAcA,CAACP,QAAQ,EAAE1E,WAAW,EAAE;IAClC,MAAM2C,cAAc,GAAG,IAAI,CAACgC,iBAAiB,CAACD,QAAQ,CAAC;IACvD,OAAO,IAAI,CAACnD,KAAK,CAAC0D,cAAc,CAACtC,cAAc,EAAE3C,WAAW,CAAC;EACjE;EACAkF,WAAWA,CAACR,QAAQ,EAAE;IAClB,MAAM/B,cAAc,GAAG,IAAI,CAACgC,iBAAiB,CAACD,QAAQ,CAAC;IACvD,OAAO,IAAI,CAACnD,KAAK,CAAC2D,WAAW,CAACvC,cAAc,CAAC;EACjD;EACAwC,YAAYA,CAACT,QAAQ,EAAEzE,SAAS,EAAEmF,SAAS,EAAE;IACzC,MAAMzC,cAAc,GAAG,IAAI,CAACgC,iBAAiB,CAACD,QAAQ,CAAC;IACvD,OAAO,IAAI,CAACnD,KAAK,CAAC4D,YAAY,CAACxC,cAAc,EAAE1C,SAAS,EAAEmF,SAAS,CAAC;EACxE;EACAC,QAAQA,CAACX,QAAQ,EAAE;IACf,MAAM/B,cAAc,GAAG,IAAI,CAACgC,iBAAiB,CAACD,QAAQ,CAAC;IACvD,IAAI,CAACnD,KAAK,CAAC8D,QAAQ,CAAC1C,cAAc,CAAC;EACvC;EACA2C,QAAQA,CAACZ,QAAQ,EAAE;IACf,MAAM/B,cAAc,GAAG,IAAI,CAACgC,iBAAiB,CAACD,QAAQ,CAAC;IACvD,IAAI,CAACnD,KAAK,CAAC+D,QAAQ,CAAC3C,cAAc,CAAC;EACvC;EACA4C,QAAQA,CAAA,EAAG;IACP,IAAI,CAAChE,KAAK,CAACgE,QAAQ,CAAC,CAAC;EACzB;EACAZ,iBAAiBA,CAACjF,OAAO,EAAE;IACvB,IAAIA,OAAO,KAAK,IAAI,EAAE;MAClB,OAAO,IAAI;IACf;IACA,MAAMwB,IAAI,GAAG,IAAI,CAACa,KAAK,CAACa,GAAG,CAAClD,OAAO,CAAC;IACpC,IAAI,CAACwB,IAAI,EAAE;MACP,MAAM,IAAI9B,SAAS,CAAC,IAAI,CAACuC,IAAI,EAAE,2BAA2BjC,OAAO,EAAE,CAAC;IACxE;IACA,OAAOwB,IAAI;EACf;AACJ;AACA,OAAO,MAAMsE,oBAAoB,GAAG7F,QAAQ,IAAIA,QAAQ,CAACA,QAAQ,CAACU,MAAM,GAAG,CAAC,CAAC;AAC7E,MAAMoF,yBAAyB,CAAC;EAC5B,IAAI/F,OAAOA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACwB,IAAI,CAACxB,OAAO,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI,CAACgG,SAAS,CAAC,IAAI,CAACxE,IAAI,CAACxB,OAAO,CAAC;EAAE;EAC9F,IAAIG,QAAQA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACqB,IAAI,CAACrB,QAAQ,CAACC,GAAG,CAACoB,IAAI,IAAI,IAAIuE,yBAAyB,CAAC,IAAI,CAACC,SAAS,EAAExE,IAAI,CAAC,CAAC;EAAE;EAC7G,IAAIwC,KAAKA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACxC,IAAI,CAACwC,KAAK;EAAE;EACtC,IAAIiC,oBAAoBA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACzE,IAAI,CAACyE,oBAAoB;EAAE;EACpE,IAAIC,iBAAiBA,CAAA,EAAG;IAAE,OAAO,IAAI,CAAC1E,IAAI,CAAC0E,iBAAiB;EAAE;EAC9D,IAAI5F,WAAWA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACkB,IAAI,CAAClB,WAAW;EAAE;EAClD,IAAIC,SAASA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACiB,IAAI,CAACjB,SAAS;EAAE;EAC9C,IAAI4F,OAAOA,CAAA,EAAG;IAAE,OAAO,IAAI,CAAC3E,IAAI,CAAC2E,OAAO;EAAE;EAC1C,IAAIC,UAAUA,CAAA,EAAG;IAAE,OAAO,IAAI,CAAC5E,IAAI,CAAC4E,UAAU;EAAE;EAChDpE,WAAWA,CAACgE,SAAS,EAAExE,IAAI,EAAE;IACzB,IAAI,CAACwE,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACxE,IAAI,GAAGA,IAAI;EACpB;AACJ;AACA,SAAS6E,OAAOA,CAACC,UAAU,EAAEpE,IAAI,EAAE;EAC/B,OAAO;IACHhB,MAAMA,CAACqF,KAAK,EAAEC,WAAW,EAAEC,QAAQ,EAAE;MACjCvE,IAAI,CAAChB,MAAM,CAACqF,KAAK,EAAEC,WAAW,EAAEC,QAAQ,CAACrG,GAAG,CAACoB,IAAI,IAAI8E,UAAU,CAAClG,GAAG,CAACoB,IAAI,CAAC,CAAC,CAAC;IAC/E,CAAC;IACDkF,mBAAmBA,CAAC3F,KAAK,EAAE4F,MAAM,EAAE;MAC/BzE,IAAI,CAACwE,mBAAmB,CAAC3F,KAAK,EAAE4F,MAAM,CAAC;IAC3C;EACJ,CAAC;AACL;AACA,SAASC,UAAUA,CAACC,uBAAuB,EAAE1E,OAAO,EAAE;EAClD,OAAO;IACH,GAAGA,OAAO;IACVM,gBAAgB,EAAEN,OAAO,CAACM,gBAAgB,IAAI;MAC1ClB,KAAKA,CAACC,IAAI,EAAE;QACR,OAAOW,OAAO,CAACM,gBAAgB,CAAClB,KAAK,CAACsF,uBAAuB,CAACrF,IAAI,CAAC,CAAC;MACxE;IACJ,CAAC;IACDsF,MAAM,EAAE3E,OAAO,CAAC2E,MAAM,IAAI;MACtBC,OAAOA,CAACvF,IAAI,EAAEwF,SAAS,EAAE;QACrB,OAAO7E,OAAO,CAAC2E,MAAM,CAACC,OAAO,CAACvF,IAAI,CAACvB,QAAQ,CAAC,CAAC,CAAC,EAAE+G,SAAS,CAAC/G,QAAQ,CAAC,CAAC,CAAC,CAAC;MAC1E;IACJ,CAAC;IACDgH,MAAM,EAAE9E,OAAO,CAAC8E,MAAM,IAAI;MACtBA,MAAMA,CAACzF,IAAI,EAAE0F,gBAAgB,EAAE;QAC3B,OAAO/E,OAAO,CAAC8E,MAAM,CAACA,MAAM,CAACJ,uBAAuB,CAACrF,IAAI,CAAC,EAAE0F,gBAAgB,CAAC;MACjF;IACJ;EACJ,CAAC;AACL;AACA,OAAO,MAAMC,2BAA2B,CAAC;EACrC,IAAIvF,WAAWA,CAAA,EAAG;IACd,OAAO/B,KAAK,CAACO,GAAG,CAAC,IAAI,CAACyB,KAAK,CAACD,WAAW,EAAE,CAAC;MAAEwF,aAAa;MAAEC;IAAa,CAAC,MAAM;MAC3ED,aAAa,EAAEA,aAAa,CAAChH,GAAG,CAACoB,IAAI,IAAI,IAAI,CAAC8E,UAAU,CAAClG,GAAG,CAACoB,IAAI,CAAC,CAAC;MACnE6F,YAAY,EAAEA,YAAY,CAACjH,GAAG,CAACoB,IAAI,IAAI,IAAI,CAAC8E,UAAU,CAAClG,GAAG,CAACoB,IAAI,CAAC;IACpE,CAAC,CAAC,CAAC;EACP;EACA,IAAIM,wBAAwBA,CAAA,EAAG;IAC3B,OAAOjC,KAAK,CAACO,GAAG,CAAC,IAAI,CAACyB,KAAK,CAACC,wBAAwB,EAAE,CAAC;MAAEN,IAAI;MAAE8F;IAAK,CAAC,MAAM;MACvE9F,IAAI,EAAE,IAAI,CAAC8E,UAAU,CAAClG,GAAG,CAACoB,IAAI,CAAC;MAC/B8F;IACJ,CAAC,CAAC,CAAC;EACP;EACA,IAAIvF,0BAA0BA,CAAA,EAAG;IAC7B,OAAOlC,KAAK,CAACO,GAAG,CAAC,IAAI,CAACyB,KAAK,CAACE,0BAA0B,EAAEP,IAAI,IAAI,IAAI,CAAC8E,UAAU,CAAClG,GAAG,CAACoB,IAAI,CAAC,CAAC;EAC9F;EACAQ,WAAWA,CAACC,IAAI,EAAEC,IAAI,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IAClC,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACmF,aAAa,GAAGpF,OAAO,CAACoF,aAAa,IAAIzB,oBAAoB;IAClE,MAAMe,uBAAuB,GAAGrF,IAAI,IAAI,IAAI,CAAC+F,aAAa,CAAC/F,IAAI,CAACvB,QAAQ,CAAC;IACzE,IAAI,CAACqG,UAAU,GAAG,IAAI3G,UAAU,CAAC6B,IAAI,IAAI,IAAIuE,yBAAyB,CAACc,uBAAuB,EAAErF,IAAI,CAAC,CAAC;IACtG,IAAI,CAACK,KAAK,GAAG,IAAIF,yBAAyB,CAACM,IAAI,EAAEoE,OAAO,CAAC,IAAI,CAACC,UAAU,EAAEpE,IAAI,CAAC,EAAE0E,UAAU,CAACC,uBAAuB,EAAE1E,OAAO,CAAC,CAAC;EAClI;EACAO,WAAWA,CAAC1C,OAAO,EAAEG,QAAQ,GAAGL,QAAQ,CAAC6C,KAAK,CAAC,CAAC,EAAER,OAAO,GAAG,CAAC,CAAC,EAAE;IAC5D,IAAI,CAACN,KAAK,CAACa,WAAW,CAAC1C,OAAO,EAAEG,QAAQ,EAAEgC,OAAO,CAAC;EACtD;EACA8B,oBAAoBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACpC,KAAK,CAACoC,oBAAoB,CAAC,CAAC;EAC5C;EACAC,qBAAqBA,CAAC3B,OAAO,EAAE;IAC3B,IAAI,CAACV,KAAK,CAACqC,qBAAqB,CAAC3B,OAAO,CAAC;EAC7C;EACAsC,GAAGA,CAACG,QAAQ,EAAE;IACV,OAAO,IAAI,CAACnD,KAAK,CAACgD,GAAG,CAACG,QAAQ,CAAC;EACnC;EACAD,YAAYA,CAACC,QAAQ,EAAE;IACnB,OAAO,IAAI,CAACnD,KAAK,CAACkD,YAAY,CAACC,QAAQ,CAAC;EAC5C;EACAE,kBAAkBA,CAACF,QAAQ,EAAE;IACzB,OAAO,IAAI,CAACnD,KAAK,CAACqD,kBAAkB,CAACF,QAAQ,CAAC;EAClD;EACA7B,OAAOA,CAAC6B,QAAQ,EAAE;IACd,OAAO,IAAI,CAACsB,UAAU,CAAClG,GAAG,CAAC,IAAI,CAACyB,KAAK,CAACsB,OAAO,CAAC6B,QAAQ,CAAC,CAAC;EAC5D;EACAG,eAAeA,CAAC3D,IAAI,EAAE;IAClB,OAAOA,IAAI,CAACxB,OAAO;EACvB;EACAqD,qBAAqBA,CAAC2B,QAAQ,EAAE;IAC5B,OAAO,IAAI,CAACnD,KAAK,CAACwB,qBAAqB,CAAC2B,QAAQ,CAAC;EACrD;EACAK,oBAAoBA,CAACL,QAAQ,EAAE;IAC3B,MAAMwC,MAAM,GAAG,IAAI,CAAC3F,KAAK,CAACwD,oBAAoB,CAACL,QAAQ,CAAC;IACxD,IAAIwC,MAAM,KAAK,IAAI,IAAI,OAAOA,MAAM,KAAK,WAAW,EAAE;MAClD,OAAOA,MAAM;IACjB;IACA,OAAO,IAAI,CAACD,aAAa,CAACC,MAAM,CAACvH,QAAQ,CAAC;EAC9C;EACAqF,aAAaA,CAACN,QAAQ,EAAE;IACpB,OAAO,IAAI,CAACnD,KAAK,CAACyD,aAAa,CAACN,QAAQ,CAAC;EAC7C;EACAO,cAAcA,CAACP,QAAQ,EAAEzE,SAAS,EAAE;IAChC,OAAO,IAAI,CAACsB,KAAK,CAAC0D,cAAc,CAACP,QAAQ,EAAEzE,SAAS,CAAC;EACzD;EACAiF,WAAWA,CAACR,QAAQ,EAAE;IAClB,OAAO,IAAI,CAACnD,KAAK,CAAC2D,WAAW,CAACR,QAAQ,CAAC;EAC3C;EACAS,YAAYA,CAACT,QAAQ,EAAEzE,SAAS,EAAEmF,SAAS,EAAE;IACzC,OAAO,IAAI,CAAC7D,KAAK,CAAC4D,YAAY,CAACT,QAAQ,EAAEzE,SAAS,EAAEmF,SAAS,CAAC;EAClE;EACAC,QAAQA,CAACX,QAAQ,EAAE;IACf,OAAO,IAAI,CAACnD,KAAK,CAAC8D,QAAQ,CAACX,QAAQ,CAAC;EACxC;EACAY,QAAQA,CAACZ,QAAQ,EAAE;IACf,OAAO,IAAI,CAACnD,KAAK,CAAC+D,QAAQ,CAACZ,QAAQ,CAAC;EACxC;EACAa,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAAChE,KAAK,CAACgE,QAAQ,CAAC,CAAC;EAChC;EACA4B,qBAAqBA,CAACzC,QAAQ,GAAG,IAAI,EAAE;IACnC,OAAO,IAAI,CAACnD,KAAK,CAACsB,OAAO,CAAC6B,QAAQ,CAAC;EACvC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}