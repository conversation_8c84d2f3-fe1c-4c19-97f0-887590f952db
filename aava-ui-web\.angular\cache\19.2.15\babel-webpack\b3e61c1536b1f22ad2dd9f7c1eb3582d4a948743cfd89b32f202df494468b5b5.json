{"ast": null, "code": "import _asyncToGenerator from \"C:/console/aava-ui-web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { distinct } from './arrays.js';\nimport { Iterable } from './iterator.js';\nimport { generateUuid } from './uuid.js';\nexport function createStringDataTransferItem(stringOrPromise) {\n  return {\n    asString: function () {\n      var _ref = _asyncToGenerator(function* () {\n        return stringOrPromise;\n      });\n      return function asString() {\n        return _ref.apply(this, arguments);\n      };\n    }(),\n    asFile: () => undefined,\n    value: typeof stringOrPromise === 'string' ? stringOrPromise : undefined\n  };\n}\nexport function createFileDataTransferItem(fileName, uri, data) {\n  const file = {\n    id: generateUuid(),\n    name: fileName,\n    uri,\n    data\n  };\n  return {\n    asString: function () {\n      var _ref2 = _asyncToGenerator(function* () {\n        return '';\n      });\n      return function asString() {\n        return _ref2.apply(this, arguments);\n      };\n    }(),\n    asFile: () => file,\n    value: undefined\n  };\n}\nexport class VSDataTransfer {\n  constructor() {\n    this._entries = new Map();\n  }\n  get size() {\n    let size = 0;\n    for (const _ of this._entries) {\n      size++;\n    }\n    return size;\n  }\n  has(mimeType) {\n    return this._entries.has(this.toKey(mimeType));\n  }\n  matches(pattern) {\n    const mimes = [...this._entries.keys()];\n    if (Iterable.some(this, ([_, item]) => item.asFile())) {\n      mimes.push('files');\n    }\n    return matchesMimeType_normalized(normalizeMimeType(pattern), mimes);\n  }\n  get(mimeType) {\n    return this._entries.get(this.toKey(mimeType))?.[0];\n  }\n  /**\n   * Add a new entry to this data transfer.\n   *\n   * This does not replace existing entries for `mimeType`.\n   */\n  append(mimeType, value) {\n    const existing = this._entries.get(mimeType);\n    if (existing) {\n      existing.push(value);\n    } else {\n      this._entries.set(this.toKey(mimeType), [value]);\n    }\n  }\n  /**\n   * Set the entry for a given mime type.\n   *\n   * This replaces all existing entries for `mimeType`.\n   */\n  replace(mimeType, value) {\n    this._entries.set(this.toKey(mimeType), [value]);\n  }\n  /**\n   * Remove all entries for `mimeType`.\n   */\n  delete(mimeType) {\n    this._entries.delete(this.toKey(mimeType));\n  }\n  /**\n   * Iterate over all `[mime, item]` pairs in this data transfer.\n   *\n   * There may be multiple entries for each mime type.\n   */\n  *[Symbol.iterator]() {\n    for (const [mine, items] of this._entries) {\n      for (const item of items) {\n        yield [mine, item];\n      }\n    }\n  }\n  toKey(mimeType) {\n    return normalizeMimeType(mimeType);\n  }\n}\nfunction normalizeMimeType(mimeType) {\n  return mimeType.toLowerCase();\n}\nexport function matchesMimeType(pattern, mimeTypes) {\n  return matchesMimeType_normalized(normalizeMimeType(pattern), mimeTypes.map(normalizeMimeType));\n}\nfunction matchesMimeType_normalized(normalizedPattern, normalizedMimeTypes) {\n  // Anything wildcard\n  if (normalizedPattern === '*/*') {\n    return normalizedMimeTypes.length > 0;\n  }\n  // Exact match\n  if (normalizedMimeTypes.includes(normalizedPattern)) {\n    return true;\n  }\n  // Wildcard, such as `image/*`\n  const wildcard = normalizedPattern.match(/^([a-z]+)\\/([a-z]+|\\*)$/i);\n  if (!wildcard) {\n    return false;\n  }\n  const [_, type, subtype] = wildcard;\n  if (subtype === '*') {\n    return normalizedMimeTypes.some(mime => mime.startsWith(type + '/'));\n  }\n  return false;\n}\nexport const UriList = Object.freeze({\n  // http://amundsen.com/hypermedia/urilist/\n  create: entries => {\n    return distinct(entries.map(x => x.toString())).join('\\r\\n');\n  },\n  split: str => {\n    return str.split('\\r\\n');\n  },\n  parse: str => {\n    return UriList.split(str).filter(value => !value.startsWith('#'));\n  }\n});", "map": {"version": 3, "names": ["distinct", "Iterable", "generateUuid", "createStringDataTransferItem", "stringOrPromise", "asString", "_ref", "_asyncToGenerator", "apply", "arguments", "asFile", "undefined", "value", "createFileDataTransferItem", "fileName", "uri", "data", "file", "id", "name", "_ref2", "VSDataTransfer", "constructor", "_entries", "Map", "size", "_", "has", "mimeType", "to<PERSON><PERSON>", "matches", "pattern", "mimes", "keys", "some", "item", "push", "matchesMimeType_normalized", "normalizeMimeType", "get", "append", "existing", "set", "replace", "delete", "Symbol", "iterator", "mine", "items", "toLowerCase", "matchesMimeType", "mimeTypes", "map", "normalizedPattern", "normalizedMimeTypes", "length", "includes", "wildcard", "match", "type", "subtype", "mime", "startsWith", "UriList", "Object", "freeze", "create", "entries", "x", "toString", "join", "split", "str", "parse", "filter"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/common/dataTransfer.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { distinct } from './arrays.js';\nimport { Iterable } from './iterator.js';\nimport { generateUuid } from './uuid.js';\nexport function createStringDataTransferItem(stringOrPromise) {\n    return {\n        asString: async () => stringOrPromise,\n        asFile: () => undefined,\n        value: typeof stringOrPromise === 'string' ? stringOrPromise : undefined,\n    };\n}\nexport function createFileDataTransferItem(fileName, uri, data) {\n    const file = { id: generateUuid(), name: fileName, uri, data };\n    return {\n        asString: async () => '',\n        asFile: () => file,\n        value: undefined,\n    };\n}\nexport class VSDataTransfer {\n    constructor() {\n        this._entries = new Map();\n    }\n    get size() {\n        let size = 0;\n        for (const _ of this._entries) {\n            size++;\n        }\n        return size;\n    }\n    has(mimeType) {\n        return this._entries.has(this.toKey(mimeType));\n    }\n    matches(pattern) {\n        const mimes = [...this._entries.keys()];\n        if (Iterable.some(this, ([_, item]) => item.asFile())) {\n            mimes.push('files');\n        }\n        return matchesMimeType_normalized(normalizeMimeType(pattern), mimes);\n    }\n    get(mimeType) {\n        return this._entries.get(this.toKey(mimeType))?.[0];\n    }\n    /**\n     * Add a new entry to this data transfer.\n     *\n     * This does not replace existing entries for `mimeType`.\n     */\n    append(mimeType, value) {\n        const existing = this._entries.get(mimeType);\n        if (existing) {\n            existing.push(value);\n        }\n        else {\n            this._entries.set(this.toKey(mimeType), [value]);\n        }\n    }\n    /**\n     * Set the entry for a given mime type.\n     *\n     * This replaces all existing entries for `mimeType`.\n     */\n    replace(mimeType, value) {\n        this._entries.set(this.toKey(mimeType), [value]);\n    }\n    /**\n     * Remove all entries for `mimeType`.\n     */\n    delete(mimeType) {\n        this._entries.delete(this.toKey(mimeType));\n    }\n    /**\n     * Iterate over all `[mime, item]` pairs in this data transfer.\n     *\n     * There may be multiple entries for each mime type.\n     */\n    *[Symbol.iterator]() {\n        for (const [mine, items] of this._entries) {\n            for (const item of items) {\n                yield [mine, item];\n            }\n        }\n    }\n    toKey(mimeType) {\n        return normalizeMimeType(mimeType);\n    }\n}\nfunction normalizeMimeType(mimeType) {\n    return mimeType.toLowerCase();\n}\nexport function matchesMimeType(pattern, mimeTypes) {\n    return matchesMimeType_normalized(normalizeMimeType(pattern), mimeTypes.map(normalizeMimeType));\n}\nfunction matchesMimeType_normalized(normalizedPattern, normalizedMimeTypes) {\n    // Anything wildcard\n    if (normalizedPattern === '*/*') {\n        return normalizedMimeTypes.length > 0;\n    }\n    // Exact match\n    if (normalizedMimeTypes.includes(normalizedPattern)) {\n        return true;\n    }\n    // Wildcard, such as `image/*`\n    const wildcard = normalizedPattern.match(/^([a-z]+)\\/([a-z]+|\\*)$/i);\n    if (!wildcard) {\n        return false;\n    }\n    const [_, type, subtype] = wildcard;\n    if (subtype === '*') {\n        return normalizedMimeTypes.some(mime => mime.startsWith(type + '/'));\n    }\n    return false;\n}\nexport const UriList = Object.freeze({\n    // http://amundsen.com/hypermedia/urilist/\n    create: (entries) => {\n        return distinct(entries.map(x => x.toString())).join('\\r\\n');\n    },\n    split: (str) => {\n        return str.split('\\r\\n');\n    },\n    parse: (str) => {\n        return UriList.split(str).filter(value => !value.startsWith('#'));\n    }\n});\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA,SAASA,QAAQ,QAAQ,aAAa;AACtC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,WAAW;AACxC,OAAO,SAASC,4BAA4BA,CAACC,eAAe,EAAE;EAC1D,OAAO;IACHC,QAAQ;MAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAE;QAAA,OAAYH,eAAe;MAAA;MAAA,gBAArCC,QAAQA,CAAA;QAAA,OAAAC,IAAA,CAAAE,KAAA,OAAAC,SAAA;MAAA;IAAA,GAA6B;IACrCC,MAAM,EAAEA,CAAA,KAAMC,SAAS;IACvBC,KAAK,EAAE,OAAOR,eAAe,KAAK,QAAQ,GAAGA,eAAe,GAAGO;EACnE,CAAC;AACL;AACA,OAAO,SAASE,0BAA0BA,CAACC,QAAQ,EAAEC,GAAG,EAAEC,IAAI,EAAE;EAC5D,MAAMC,IAAI,GAAG;IAAEC,EAAE,EAAEhB,YAAY,CAAC,CAAC;IAAEiB,IAAI,EAAEL,QAAQ;IAAEC,GAAG;IAAEC;EAAK,CAAC;EAC9D,OAAO;IACHX,QAAQ;MAAA,IAAAe,KAAA,GAAAb,iBAAA,CAAE;QAAA,OAAY,EAAE;MAAA;MAAA,gBAAxBF,QAAQA,CAAA;QAAA,OAAAe,KAAA,CAAAZ,KAAA,OAAAC,SAAA;MAAA;IAAA,GAAgB;IACxBC,MAAM,EAAEA,CAAA,KAAMO,IAAI;IAClBL,KAAK,EAAED;EACX,CAAC;AACL;AACA,OAAO,MAAMU,cAAc,CAAC;EACxBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC7B;EACA,IAAIC,IAAIA,CAAA,EAAG;IACP,IAAIA,IAAI,GAAG,CAAC;IACZ,KAAK,MAAMC,CAAC,IAAI,IAAI,CAACH,QAAQ,EAAE;MAC3BE,IAAI,EAAE;IACV;IACA,OAAOA,IAAI;EACf;EACAE,GAAGA,CAACC,QAAQ,EAAE;IACV,OAAO,IAAI,CAACL,QAAQ,CAACI,GAAG,CAAC,IAAI,CAACE,KAAK,CAACD,QAAQ,CAAC,CAAC;EAClD;EACAE,OAAOA,CAACC,OAAO,EAAE;IACb,MAAMC,KAAK,GAAG,CAAC,GAAG,IAAI,CAACT,QAAQ,CAACU,IAAI,CAAC,CAAC,CAAC;IACvC,IAAIhC,QAAQ,CAACiC,IAAI,CAAC,IAAI,EAAE,CAAC,CAACR,CAAC,EAAES,IAAI,CAAC,KAAKA,IAAI,CAACzB,MAAM,CAAC,CAAC,CAAC,EAAE;MACnDsB,KAAK,CAACI,IAAI,CAAC,OAAO,CAAC;IACvB;IACA,OAAOC,0BAA0B,CAACC,iBAAiB,CAACP,OAAO,CAAC,EAAEC,KAAK,CAAC;EACxE;EACAO,GAAGA,CAACX,QAAQ,EAAE;IACV,OAAO,IAAI,CAACL,QAAQ,CAACgB,GAAG,CAAC,IAAI,CAACV,KAAK,CAACD,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;EACvD;EACA;AACJ;AACA;AACA;AACA;EACIY,MAAMA,CAACZ,QAAQ,EAAEhB,KAAK,EAAE;IACpB,MAAM6B,QAAQ,GAAG,IAAI,CAAClB,QAAQ,CAACgB,GAAG,CAACX,QAAQ,CAAC;IAC5C,IAAIa,QAAQ,EAAE;MACVA,QAAQ,CAACL,IAAI,CAACxB,KAAK,CAAC;IACxB,CAAC,MACI;MACD,IAAI,CAACW,QAAQ,CAACmB,GAAG,CAAC,IAAI,CAACb,KAAK,CAACD,QAAQ,CAAC,EAAE,CAAChB,KAAK,CAAC,CAAC;IACpD;EACJ;EACA;AACJ;AACA;AACA;AACA;EACI+B,OAAOA,CAACf,QAAQ,EAAEhB,KAAK,EAAE;IACrB,IAAI,CAACW,QAAQ,CAACmB,GAAG,CAAC,IAAI,CAACb,KAAK,CAACD,QAAQ,CAAC,EAAE,CAAChB,KAAK,CAAC,CAAC;EACpD;EACA;AACJ;AACA;EACIgC,MAAMA,CAAChB,QAAQ,EAAE;IACb,IAAI,CAACL,QAAQ,CAACqB,MAAM,CAAC,IAAI,CAACf,KAAK,CAACD,QAAQ,CAAC,CAAC;EAC9C;EACA;AACJ;AACA;AACA;AACA;EACI,EAAEiB,MAAM,CAACC,QAAQ,IAAI;IACjB,KAAK,MAAM,CAACC,IAAI,EAAEC,KAAK,CAAC,IAAI,IAAI,CAACzB,QAAQ,EAAE;MACvC,KAAK,MAAMY,IAAI,IAAIa,KAAK,EAAE;QACtB,MAAM,CAACD,IAAI,EAAEZ,IAAI,CAAC;MACtB;IACJ;EACJ;EACAN,KAAKA,CAACD,QAAQ,EAAE;IACZ,OAAOU,iBAAiB,CAACV,QAAQ,CAAC;EACtC;AACJ;AACA,SAASU,iBAAiBA,CAACV,QAAQ,EAAE;EACjC,OAAOA,QAAQ,CAACqB,WAAW,CAAC,CAAC;AACjC;AACA,OAAO,SAASC,eAAeA,CAACnB,OAAO,EAAEoB,SAAS,EAAE;EAChD,OAAOd,0BAA0B,CAACC,iBAAiB,CAACP,OAAO,CAAC,EAAEoB,SAAS,CAACC,GAAG,CAACd,iBAAiB,CAAC,CAAC;AACnG;AACA,SAASD,0BAA0BA,CAACgB,iBAAiB,EAAEC,mBAAmB,EAAE;EACxE;EACA,IAAID,iBAAiB,KAAK,KAAK,EAAE;IAC7B,OAAOC,mBAAmB,CAACC,MAAM,GAAG,CAAC;EACzC;EACA;EACA,IAAID,mBAAmB,CAACE,QAAQ,CAACH,iBAAiB,CAAC,EAAE;IACjD,OAAO,IAAI;EACf;EACA;EACA,MAAMI,QAAQ,GAAGJ,iBAAiB,CAACK,KAAK,CAAC,0BAA0B,CAAC;EACpE,IAAI,CAACD,QAAQ,EAAE;IACX,OAAO,KAAK;EAChB;EACA,MAAM,CAAC/B,CAAC,EAAEiC,IAAI,EAAEC,OAAO,CAAC,GAAGH,QAAQ;EACnC,IAAIG,OAAO,KAAK,GAAG,EAAE;IACjB,OAAON,mBAAmB,CAACpB,IAAI,CAAC2B,IAAI,IAAIA,IAAI,CAACC,UAAU,CAACH,IAAI,GAAG,GAAG,CAAC,CAAC;EACxE;EACA,OAAO,KAAK;AAChB;AACA,OAAO,MAAMI,OAAO,GAAGC,MAAM,CAACC,MAAM,CAAC;EACjC;EACAC,MAAM,EAAGC,OAAO,IAAK;IACjB,OAAOnE,QAAQ,CAACmE,OAAO,CAACf,GAAG,CAACgB,CAAC,IAAIA,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;EAChE,CAAC;EACDC,KAAK,EAAGC,GAAG,IAAK;IACZ,OAAOA,GAAG,CAACD,KAAK,CAAC,MAAM,CAAC;EAC5B,CAAC;EACDE,KAAK,EAAGD,GAAG,IAAK;IACZ,OAAOT,OAAO,CAACQ,KAAK,CAACC,GAAG,CAAC,CAACE,MAAM,CAAC9D,KAAK,IAAI,CAACA,KAAK,CAACkD,UAAU,CAAC,GAAG,CAAC,CAAC;EACrE;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}