{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { EventEmitter } from '@angular/core';\nimport { FormsModule, FormControl, ReactiveFormsModule } from '@angular/forms';\nimport { ButtonComponent, DropdownComponent, IconComponent, ToggleComponent, AvaTextboxComponent } from '@ava/play-comp-library';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/forms\";\nconst _c0 = [\"messagesContainer\"];\nconst _c1 = [\"fileInput\"];\nconst _c2 = () => ({\n  background: \"linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)\",\n  \"--button-effect-color\": \"33, 90, 214\"\n});\nconst _c3 = (a0, a1) => ({\n  \"bot-response\": a0,\n  \"user-message\": a1\n});\nfunction PlaygroundComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.playgroundTitle);\n  }\n}\nfunction PlaygroundComponent_div_3_ava_dropdown_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ava-dropdown\", 27);\n    i0.ɵɵlistener(\"selectionChange\", function PlaygroundComponent_div_3_ava_dropdown_1_Template_ava_dropdown_selectionChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onPromptChange($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"options\", ctx_r1.promptOptions)(\"selectedValue\", ctx_r1.selectedPrompt)(\"enableSearch\", true)(\"singleSelect\", true);\n  }\n}\nfunction PlaygroundComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵtemplate(1, PlaygroundComponent_div_3_ava_dropdown_1_Template, 1, 4, \"ava-dropdown\", 26);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isMinimalView);\n  }\n}\nfunction PlaygroundComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵelement(1, \"ava-textbox\", 29);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"placeholder\", ctx_r1.agentNamePlaceholder)(\"formControl\", ctx_r1.agentNameDisplayControl);\n  }\n}\nfunction PlaygroundComponent_ava_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ava-button\", 30);\n    i0.ɵɵlistener(\"userClick\", function PlaygroundComponent_ava_button_6_Template_ava_button_userClick_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onApprovalClick());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"customStyles\", i0.ɵɵpureFunction0(1, _c2));\n  }\n}\nfunction PlaygroundComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function PlaygroundComponent_div_12_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.save());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 33);\n    i0.ɵɵelement(3, \"path\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Save \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(5, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function PlaygroundComponent_div_12_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.export());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(6, \"svg\", 33);\n    i0.ɵɵelement(7, \"path\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8, \" Export \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PlaygroundComponent_div_15_button_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function PlaygroundComponent_div_15_button_3_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const msg_r7 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.copyToClipboard(msg_r7.text));\n    });\n    i0.ɵɵelement(1, \"ava-icon\", 41);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"iconSize\", 16);\n  }\n}\nfunction PlaygroundComponent_div_15_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵtext(1, \"Copied!\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PlaygroundComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"div\", 37);\n    i0.ɵɵtext(2);\n    i0.ɵɵtemplate(3, PlaygroundComponent_div_15_button_3_Template, 2, 1, \"button\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, PlaygroundComponent_div_15_div_4_Template, 2, 0, \"div\", 39);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const msg_r7 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", msg_r7.from);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(5, _c3, msg_r7.from === \"ai\", msg_r7.from === \"user\"));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", msg_r7.text, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", msg_r7.from === \"ai\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showCopiedToast);\n  }\n}\nfunction PlaygroundComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43)(1, \"div\", 44)(2, \"div\", 45);\n    i0.ɵɵelement(3, \"span\", 46)(4, \"span\", 46)(5, \"span\", 46);\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction PlaygroundComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43)(1, \"div\", 47);\n    i0.ɵɵtext(2, \"...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PlaygroundComponent_button_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function PlaygroundComponent_button_20_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      i0.ɵɵnextContext();\n      const fileInput_r9 = i0.ɵɵreference(22);\n      return i0.ɵɵresetView(fileInput_r9.click());\n    });\n    i0.ɵɵelement(1, \"ava-icon\", 49);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"iconSize\", 16);\n  }\n}\nfunction PlaygroundComponent_div_23_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"span\", 53);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 54);\n    i0.ɵɵlistener(\"click\", function PlaygroundComponent_div_23_div_1_Template_button_click_3_listener() {\n      const i_r11 = i0.ɵɵrestoreView(_r10).index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.removeFile(i_r11));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r12 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(file_r12.documentName);\n  }\n}\nfunction PlaygroundComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50);\n    i0.ɵɵtemplate(1, PlaygroundComponent_div_23_div_1_Template, 4, 1, \"div\", 51);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.filesUploadedData);\n  }\n}\nfunction PlaygroundComponent_div_27_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 57)(1, \"ava-toggle\", 58);\n    i0.ɵɵlistener(\"checkedChange\", function PlaygroundComponent_div_27_div_1_Template_ava_toggle_checkedChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onConversationalToggle($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"checked\", ctx_r1.isConvChecked)(\"title\", \"Conversational\")(\"position\", \"left\");\n  }\n}\nfunction PlaygroundComponent_div_27_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 57)(1, \"ava-toggle\", 58);\n    i0.ɵɵlistener(\"checkedChange\", function PlaygroundComponent_div_27_div_2_Template_ava_toggle_checkedChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onTemplateToggle($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"checked\", ctx_r1.isUseTemplate)(\"title\", \"Use Template\")(\"position\", \"left\");\n  }\n}\nfunction PlaygroundComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵtemplate(1, PlaygroundComponent_div_27_div_1_Template, 2, 3, \"div\", 56)(2, PlaygroundComponent_div_27_div_2_Template, 2, 3, \"div\", 56);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showChatInteractionToggles);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showChatInteractionToggles);\n  }\n}\nexport let PlaygroundComponent = /*#__PURE__*/(() => {\n  class PlaygroundComponent {\n    isMenuOpen = false;\n    isToolMenuOpen = false;\n    promptChange = new EventEmitter();\n    promptOptions = [];\n    selectedValue = 'default'; // Input for pre-selected value\n    agentType = 'individual'; // Input for agent type ('individual' or 'collaborative')\n    showChatInteractionToggles = false; // Input to show conversational and template toggles\n    showAiPrincipleToggle = false; // Input to show AI principle toggle\n    showDropdown = true; // Input to control dropdown visibility\n    showAgentNameInput = false; // Input to show disabled agent name input field\n    agentNamePlaceholder = 'Agent Name'; // Placeholder for agent name input\n    displayedAgentName = ''; // Agent name to display in disabled input\n    showFileUploadButton = false; // Controls visibility of attach file button\n    selectedPrompt = 'default';\n    playgroundTitle = '';\n    // Form control for agent name display\n    agentNameDisplayControl = new FormControl({\n      value: '',\n      disabled: true\n    });\n    // Chat data\n    showCopiedToast = false;\n    inputText = '';\n    previousMessagesLength = 0;\n    shouldScrollToBottom = false;\n    messages = [];\n    isLoading = false;\n    isDisabled = false;\n    showLoader = true;\n    messageSent = new EventEmitter();\n    conversationalToggle = new EventEmitter();\n    templateToggle = new EventEmitter();\n    filesSelected = new EventEmitter();\n    messagesContainer;\n    fileInput;\n    showApprovalButton = true;\n    approvalRequested = new EventEmitter();\n    isMinimalView = false;\n    // Simple toggle properties for display only\n    isConvChecked = true;\n    isUseTemplate = false;\n    // File upload properties\n    filesUploadedData = [];\n    acceptedFileType = '.pdf,.doc,.docx,.txt,.csv,.xlsx,.xls,.ppt,.pptx,.png,.jpg,.jpeg,.gif,.bmp,.svg';\n    ngOnInit() {\n      this.messages = [{\n        from: 'ai',\n        text: 'Hi there, how can I help you today?'\n      }];\n      this.shouldScrollToBottom = true;\n      // Set selected prompt from input\n      if (this.selectedValue) {\n        this.selectedPrompt = this.selectedValue;\n        // Update displayed agent name if showing agent name input\n        if (this.showAgentNameInput && !this.displayedAgentName) {\n          this.displayedAgentName = this.selectedValue;\n          this.agentNameDisplayControl.setValue(this.selectedValue);\n        }\n      }\n      // Initialize agent name display control\n      if (this.displayedAgentName) {\n        this.agentNameDisplayControl.setValue(this.displayedAgentName);\n      }\n    }\n    ngOnChanges(changes) {\n      // Update selectedPrompt when selectedValue input changes\n      if (changes['selectedValue'] && changes['selectedValue'].currentValue) {\n        // The selectedValue from parent should be the name (for dropdown display)\n        this.selectedPrompt = changes['selectedValue'].currentValue;\n      }\n      // Update agent name display control when displayedAgentName input changes\n      if (changes['displayedAgentName'] && changes['displayedAgentName'].currentValue !== undefined) {\n        this.agentNameDisplayControl.setValue(changes['displayedAgentName'].currentValue);\n      }\n    }\n    ngAfterViewChecked() {\n      if (this.shouldScrollToBottom) {\n        this.scrollToBottom();\n        this.shouldScrollToBottom = false;\n      }\n    }\n    scrollToBottom() {\n      try {\n        if (this.messagesContainer && this.messagesContainer.nativeElement) {\n          // Scroll to bottom to show latest messages\n          this.messagesContainer.nativeElement.scrollTop = this.messagesContainer.nativeElement.scrollHeight;\n        }\n      } catch (err) {\n        console.error('Error scrolling to bottom:', err);\n      }\n    }\n    handleSendMessage() {\n      if (!this.inputText.trim() || this.isDisabled) {\n        return;\n      }\n      // Add user message to the chat\n      this.messages = [...this.messages, {\n        from: 'user',\n        text: this.inputText\n      }];\n      this.shouldScrollToBottom = true;\n      // Emit the message to parent component\n      const messageText = this.inputText;\n      this.inputText = '';\n      this.messageSent.emit(messageText);\n      // Clear uploaded files after sending message\n      this.clearFiles();\n    }\n    clearFiles() {\n      this.filesUploadedData = [];\n      this.filesSelected.emit(this.filesUploadedData);\n    }\n    toggleMenu() {\n      this.isMenuOpen = !this.isMenuOpen;\n    }\n    onAiPrincipleToggle(event) {\n      console.log('AI Principles toggle:', event);\n    }\n    onConversationalToggle(event) {\n      this.isConvChecked = event;\n      // If conversational is enabled, disable template\n      if (event && this.isUseTemplate) {\n        this.isUseTemplate = false;\n        this.templateToggle.emit(false);\n      }\n      console.log('Conversational mode:', event);\n      this.conversationalToggle.emit(event);\n    }\n    onTemplateToggle(event) {\n      this.isUseTemplate = event;\n      // If template is enabled, disable conversational\n      if (event && this.isConvChecked) {\n        this.isConvChecked = false;\n        this.conversationalToggle.emit(false);\n      }\n      console.log('Use template:', event);\n      this.templateToggle.emit(event);\n    }\n    onPromptChange(selectionData) {\n      // The dropdown component emits an object with selectedOptions and selectedValue\n      // selectedValue contains the name of the selected option\n      let selectedName;\n      if (typeof selectionData === 'string') {\n        selectedName = selectionData;\n      } else if (selectionData && selectionData.selectedValue) {\n        selectedName = selectionData.selectedValue;\n      } else if (selectionData && selectionData.selectedOptions && selectionData.selectedOptions.length > 0) {\n        selectedName = selectionData.selectedOptions[0].name;\n      } else {\n        return;\n      }\n      this.selectedPrompt = selectedName;\n      // Update displayed agent name if showing agent name input\n      if (this.showAgentNameInput) {\n        this.displayedAgentName = selectedName;\n        this.agentNameDisplayControl.setValue(selectedName);\n      }\n      // Find the option by name\n      const selectedOption = this.promptOptions.find(opt => opt.name === selectedName);\n      if (selectedOption) {\n        this.promptChange.emit(selectedOption);\n      }\n    }\n    copyToClipboard(text) {\n      navigator.clipboard.writeText(text).then(() => {\n        this.showCopiedToast = true;\n        setTimeout(() => {\n          this.showCopiedToast = false;\n        }, 2000);\n      });\n    }\n    save() {\n      this.isMenuOpen = false;\n      console.log('Save clicked');\n      // your save logic here\n    }\n    export() {\n      this.isMenuOpen = false;\n      console.log('Export clicked');\n      // your export logic here\n    }\n    // Hide menu when clicking outside\n    onClickOutside(event) {\n      const target = event.target;\n      if (!target.closest('.btn-menu')) {\n        this.isMenuOpen = false;\n      }\n    }\n    onEnterKeydown(event) {\n      // Only prevent default and send if Shift key is not pressed\n      if (!event.shiftKey) {\n        event.preventDefault();\n        this.handleSendMessage();\n      }\n    }\n    // File upload methods\n    onFileSelected(event) {\n      const files = event.target.files;\n      if (files && files.length > 0) {\n        this.filesUploadedData = [];\n        for (let i = 0; i < files.length; i++) {\n          const file = files[i];\n          this.filesUploadedData.push({\n            id: `file_${Date.now()}_${i}`,\n            documentName: file.name,\n            isImage: file.type.startsWith('image/'),\n            file: file\n          });\n        }\n        console.log('Files selected:', this.filesUploadedData);\n        this.filesSelected.emit(this.filesUploadedData);\n      }\n    }\n    removeFile(index) {\n      this.filesUploadedData.splice(index, 1);\n      this.filesSelected.emit(this.filesUploadedData);\n    }\n    // Track by function for ngFor performance\n    trackByIndex(index, _item) {\n      return index;\n    }\n    onApprovalClick() {\n      this.approvalRequested.emit();\n    }\n    static ɵfac = function PlaygroundComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PlaygroundComponent)();\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PlaygroundComponent,\n      selectors: [[\"app-playground\"]],\n      viewQuery: function PlaygroundComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.messagesContainer = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n        }\n      },\n      hostBindings: function PlaygroundComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function PlaygroundComponent_click_HostBindingHandler($event) {\n            return ctx.onClickOutside($event);\n          }, false, i0.ɵɵresolveDocument)(\"keydown.enter\", function PlaygroundComponent_keydown_enter_HostBindingHandler($event) {\n            return ctx.onEnterKeydown($event);\n          });\n        }\n      },\n      inputs: {\n        promptOptions: \"promptOptions\",\n        selectedValue: \"selectedValue\",\n        agentType: \"agentType\",\n        showChatInteractionToggles: \"showChatInteractionToggles\",\n        showAiPrincipleToggle: \"showAiPrincipleToggle\",\n        showDropdown: \"showDropdown\",\n        showAgentNameInput: \"showAgentNameInput\",\n        agentNamePlaceholder: \"agentNamePlaceholder\",\n        displayedAgentName: \"displayedAgentName\",\n        showFileUploadButton: \"showFileUploadButton\",\n        playgroundTitle: \"playgroundTitle\",\n        messages: \"messages\",\n        isLoading: \"isLoading\",\n        isDisabled: \"isDisabled\",\n        showLoader: \"showLoader\",\n        showApprovalButton: \"showApprovalButton\",\n        isMinimalView: \"isMinimalView\",\n        acceptedFileType: \"acceptedFileType\"\n      },\n      outputs: {\n        promptChange: \"promptChange\",\n        messageSent: \"messageSent\",\n        conversationalToggle: \"conversationalToggle\",\n        templateToggle: \"templateToggle\",\n        filesSelected: \"filesSelected\",\n        approvalRequested: \"approvalRequested\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature],\n      decls: 28,\n      vars: 17,\n      consts: [[\"menuIcon\", \"\"], [\"messagesContainer\", \"\"], [\"fileInput\", \"\"], [1, \"playground-container\"], [1, \"button-container\"], [\"class\", \"editors-title\", 4, \"ngIf\"], [\"class\", \"dropdown-container\", 4, \"ngIf\"], [\"class\", \"agent-name-display\", 4, \"ngIf\"], [1, \"btn-menu\"], [\"label\", \"Send for Approval\", \"size\", \"small\", \"state\", \"active\", \"variant\", \"primary\", 3, \"customStyles\", \"userClick\", 4, \"ngIf\"], [1, \"menu-icon\", 3, \"click\"], [\"class\", \"dot-dropdown-menu\", 4, \"ngIf\"], [1, \"layout\"], [\"class\", \"message-row\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"message-row ai\", 4, \"ngIf\"], [1, \"input-container\"], [\"placeholder\", \"Enter something to test\", 3, \"ngModelChange\", \"keydown.enter\", \"ngModel\", \"disabled\"], [\"class\", \"attach-btn\", \"title\", \"Attach File\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"file\", \"multiple\", \"\", 2, \"display\", \"none\", 3, \"change\", \"accept\"], [\"class\", \"uploaded-files\", 4, \"ngIf\"], [1, \"right-icons\"], [\"title\", \"Send\", 1, \"send-btn\", 3, \"click\", \"disabled\"], [\"slot\", \"icon-start\", \"iconName\", \"send-horizontal\", \"iconColor\", \"var(--color-brand-primary)\", 3, \"iconSize\"], [\"class\", \"toggle-container\", 4, \"ngIf\"], [1, \"editors-title\"], [1, \"dropdown-container\"], [\"dropdownTitle\", \"Choose Agent\", 3, \"options\", \"selectedValue\", \"enableSearch\", \"singleSelect\", \"selectionChange\", 4, \"ngIf\"], [\"dropdownTitle\", \"Choose Agent\", 3, \"selectionChange\", \"options\", \"selectedValue\", \"enableSearch\", \"singleSelect\"], [1, \"agent-name-display\"], [\"variant\", \"default\", \"size\", \"md\", 1, \"disabled-agent-name-input\", 3, \"placeholder\", \"formControl\"], [\"label\", \"Send for Approval\", \"size\", \"small\", \"state\", \"active\", \"variant\", \"primary\", 3, \"userClick\", \"customStyles\"], [1, \"dot-dropdown-menu\"], [3, \"click\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"width\", \"24\", \"height\", \"24\", \"fill\", \"currentColor\", \"viewBox\", \"0 0 24 24\"], [\"d\", \"M17 3H5a2 2 0 0 0-2 2v14l4-4h10a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2z\"], [\"d\", \"M5 20h14v-2H5m14-9h-4V3H9v6H5l7 7 7-7z\"], [1, \"message-row\", 3, \"ngClass\"], [3, \"ngClass\"], [\"class\", \"copy-btn\", \"title\", \"Copy\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"copied-toast\", 4, \"ngIf\"], [\"title\", \"Copy\", 1, \"copy-btn\", 3, \"click\"], [\"slot\", \"icon-start\", \"iconName\", \"copy\", \"iconColor\", \"var(--color-brand-primary)\", 3, \"iconSize\"], [1, \"copied-toast\"], [1, \"message-row\", \"ai\"], [1, \"bot-response\", \"loading-message\"], [1, \"typing-indicator\"], [1, \"dot\"], [1, \"bot-response\"], [\"title\", \"Attach File\", 1, \"attach-btn\", 3, \"click\"], [\"slot\", \"icon-start\", \"iconName\", \"paperclip\", \"iconColor\", \"var(--color-brand-primary)\", 3, \"iconSize\"], [1, \"uploaded-files\"], [\"class\", \"file-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"file-item\"], [1, \"file-name\"], [\"title\", \"Remove file\", 1, \"remove-file\", 3, \"click\"], [1, \"toggle-container\"], [\"class\", \"toggle-row\", 4, \"ngIf\"], [1, \"toggle-row\"], [\"size\", \"small\", 3, \"checkedChange\", \"checked\", \"title\", \"position\"]],\n      template: function PlaygroundComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4);\n          i0.ɵɵtemplate(2, PlaygroundComponent_div_2_Template, 2, 1, \"div\", 5)(3, PlaygroundComponent_div_3_Template, 2, 1, \"div\", 6)(4, PlaygroundComponent_div_4_Template, 2, 2, \"div\", 7);\n          i0.ɵɵelementStart(5, \"div\", 8);\n          i0.ɵɵtemplate(6, PlaygroundComponent_ava_button_6_Template, 1, 2, \"ava-button\", 9);\n          i0.ɵɵelementStart(7, \"div\", 10, 0);\n          i0.ɵɵlistener(\"click\", function PlaygroundComponent_Template_div_click_7_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.toggleMenu());\n          });\n          i0.ɵɵelement(9, \"span\")(10, \"span\")(11, \"span\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(12, PlaygroundComponent_div_12_Template, 9, 0, \"div\", 11);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"div\", 12, 1);\n          i0.ɵɵtemplate(15, PlaygroundComponent_div_15_Template, 5, 8, \"div\", 13)(16, PlaygroundComponent_div_16_Template, 6, 0, \"div\", 14)(17, PlaygroundComponent_div_17_Template, 3, 0, \"div\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"div\", 15)(19, \"textarea\", 16);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function PlaygroundComponent_Template_textarea_ngModelChange_19_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.inputText, $event) || (ctx.inputText = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"keydown.enter\", function PlaygroundComponent_Template_textarea_keydown_enter_19_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            ctx.handleSendMessage();\n            return i0.ɵɵresetView($event.preventDefault());\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(20, PlaygroundComponent_button_20_Template, 2, 1, \"button\", 17);\n          i0.ɵɵelementStart(21, \"input\", 18, 2);\n          i0.ɵɵlistener(\"change\", function PlaygroundComponent_Template_input_change_21_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onFileSelected($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(23, PlaygroundComponent_div_23_Template, 2, 1, \"div\", 19);\n          i0.ɵɵelementStart(24, \"div\", 20)(25, \"button\", 21);\n          i0.ɵɵlistener(\"click\", function PlaygroundComponent_Template_button_click_25_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.handleSendMessage());\n          });\n          i0.ɵɵelement(26, \"ava-icon\", 22);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(27, PlaygroundComponent_div_27_Template, 3, 2, \"div\", 23);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.playgroundTitle);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showDropdown);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showAgentNameInput);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.showApprovalButton);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.isMenuOpen);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.messages)(\"ngForTrackBy\", ctx.trackByIndex);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading && ctx.showLoader);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading && !ctx.showLoader);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.inputText);\n          i0.ɵɵproperty(\"disabled\", ctx.isDisabled || ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showFileUploadButton);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"accept\", ctx.acceptedFileType);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.filesUploadedData.length > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading || ctx.isDisabled);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"iconSize\", 16);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showChatInteractionToggles || ctx.showAiPrincipleToggle);\n        }\n      },\n      dependencies: [CommonModule, i1.NgClass, i1.NgForOf, i1.NgIf, DropdownComponent, FormsModule, i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgModel, ReactiveFormsModule, i2.FormControlDirective, ButtonComponent, ToggleComponent, IconComponent, AvaTextboxComponent],\n      styles: [\"@charset \\\"UTF-8\\\";\\n.playground-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  font-family: \\\"Inter\\\", sans-serif;\\n  width: 100%;\\n  height: 78vh; \\n\\n  max-height: 100%; \\n\\n  overflow: hidden; \\n\\n}\\n@media (max-width: 1400px) {\\n  .playground-container[_ngcontent-%COMP%] {\\n    width: 60%;\\n  }\\n}\\n@media (max-width: 1200px) {\\n  .playground-container[_ngcontent-%COMP%] {\\n    width: 60%;\\n  }\\n}\\n\\n\\n\\n.button-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: row;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 8px 12px;\\n  height: 48px;\\n  flex-shrink: 0;\\n  border-bottom: 1px solid #eee;\\n  background-color: #ffffff;\\n}\\n\\n\\n\\n.dropdown-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 8px 12px;\\n  border: 1px solid #ccc;\\n  border-radius: 6px;\\n  background: white;\\n  color: #333;\\n  font-size: 14px;\\n  cursor: pointer;\\n  min-width: 100px;\\n}\\n\\n.dropdown-btn[_ngcontent-%COMP%]   .arrow-down[_ngcontent-%COMP%] {\\n  width: 10px;\\n  height: 6px;\\n  margin-left: 8px;\\n}\\n\\n\\n\\n.btn-menu[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n\\n\\n.menu-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n  gap: 3px;\\n  cursor: pointer;\\n}\\n\\n.menu-icon[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  display: block;\\n  width: 3px;\\n  height: 3px;\\n  background: black;\\n  border-radius: 50%;\\n}\\n\\n\\n\\n.layout[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n  flex: 1; \\n\\n  padding: 16px;\\n  overflow-y: auto; \\n\\n  overflow-x: hidden; \\n\\n  background: #fff;\\n  align-items: stretch;\\n  justify-content: flex-start;\\n  min-height: 300px; \\n\\n  max-height: none; \\n\\n}\\n\\n\\n\\n.message[_ngcontent-%COMP%] {\\n  max-width: 60%;\\n  font-size: 14px;\\n  border-radius: 8px;\\n  padding: 16px 24px;\\n}\\n\\n.message-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  width: 100%;\\n}\\n\\n.message-row.ai[_ngcontent-%COMP%] {\\n  justify-content: flex-start;\\n}\\n\\n.message-row.user[_ngcontent-%COMP%] {\\n  justify-content: flex-end;\\n}\\n\\n\\n\\n.user-message[_ngcontent-%COMP%] {\\n  display: inline-flex; \\n\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: flex-start; \\n\\n  align-self: flex-end; \\n\\n  background: #c2c4cd;\\n  color: #333;\\n  border-radius: 8px;\\n  padding: 16px 24px;\\n  max-width: 60%;\\n  word-wrap: break-word;\\n  text-align: left;\\n  margin-bottom: 0.5rem;\\n}\\n\\n.user-message[_ngcontent-%COMP%]:empty {\\n  background: none;\\n  padding: 0;\\n}\\n\\n\\n\\n.bot-response[_ngcontent-%COMP%] {\\n  align-self: flex-start;\\n  background: #f1f1f1; \\n\\n  color: #333;\\n  display: flex;\\n  padding: 16px 24px;\\n  border-radius: 8px;\\n  flex-direction: column;\\n  justify-content: center;\\n  gap: 10px;\\n  margin-bottom: 0.5rem;\\n  position: relative;\\n  padding-right: 40px;\\n}\\n\\n.copy-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 16px;\\n  right: 16px;\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  padding: 4px;\\n  display: flex;\\n  align-items: center;\\n}\\n.copy-btn[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  fill: var(--color-brand-primary, #144692);\\n  transition: fill 0.2s ease;\\n}\\n.copy-btn[_ngcontent-%COMP%]:hover   svg[_ngcontent-%COMP%] {\\n  fill: #1d4ed8;\\n}\\n\\n\\n\\n.loading-message[_ngcontent-%COMP%] {\\n  min-height: 20px;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n\\n\\n.typing-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   .dot[_ngcontent-%COMP%] {\\n  width: 8px;\\n  height: 8px;\\n  border-radius: 50%;\\n  background-color: #666;\\n  animation: _ngcontent-%COMP%_typing 1.4s infinite ease-in-out;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   .dot[_ngcontent-%COMP%]:nth-child(1) {\\n  animation-delay: 0s;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   .dot[_ngcontent-%COMP%]:nth-child(2) {\\n  animation-delay: 0.2s;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   .dot[_ngcontent-%COMP%]:nth-child(3) {\\n  animation-delay: 0.4s;\\n}\\n\\n@keyframes _ngcontent-%COMP%_typing {\\n  0%, 60%, 100% {\\n    transform: scale(1);\\n    opacity: 0.5;\\n  }\\n  30% {\\n    transform: scale(1.2);\\n    opacity: 1;\\n  }\\n}\\n\\n\\n.result-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  width: 186px;\\n  padding: 8px 12px;\\n  justify-content: center;\\n  align-items: center;\\n  border-radius: 8px;\\n  background: #f1f1f1;\\n  font-size: 14px;\\n  color: #333;\\n  margin-bottom: 0.5rem;\\n}\\n\\n\\n\\n\\n\\n.toggle-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-size: 14px;\\n  padding: 8px 24px;\\n  font-family: \\\"Inter\\\", sans-serif;\\n  flex-shrink: 0; \\n\\n  height: 40px; \\n\\n  min-height: 40px; \\n\\n}\\n\\n\\n\\n.toggle-switch[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: inline-block;\\n  width: 40px;\\n  height: 20px;\\n}\\n\\n.toggle-switch[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  opacity: 0;\\n  width: 0;\\n  height: 0;\\n}\\n\\n.slider[_ngcontent-%COMP%] {\\n  position: absolute;\\n  cursor: pointer;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background-color: #ccc;\\n  transition: 0.4s;\\n  border-radius: 20px;\\n}\\n\\n.slider[_ngcontent-%COMP%]:before {\\n  position: absolute;\\n  content: \\\"\\\";\\n  height: 14px;\\n  width: 14px;\\n  left: 3px;\\n  bottom: 3px;\\n  background-color: white;\\n  transition: 0.4s;\\n  border-radius: 50%;\\n}\\n\\n.toggle-switch[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:checked    + .slider[_ngcontent-%COMP%] {\\n  background-color: #4caf50;\\n}\\n\\n.toggle-switch[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:checked    + .slider[_ngcontent-%COMP%]:before {\\n  transform: translateX(20px);\\n}\\n\\n.toggle-label[_ngcontent-%COMP%] {\\n  color: #333;\\n}\\n\\n.dot-dropdown-menu[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 12px;\\n  top: 120px;\\n  background: white;\\n  border: 1px solid #ccc;\\n  border-radius: 6px;\\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);\\n  z-index: 10;\\n  display: flex;\\n  flex-direction: column;\\n  min-width: 120px;\\n}\\n\\n.dot-dropdown-menu[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover {\\n  background: #f0f0f0;\\n}\\n\\n.dot-dropdown-menu[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: flex-start;\\n  gap: 8px;\\n  padding: 10px 16px;\\n  border: none;\\n  background: none;\\n  text-align: left;\\n  cursor: pointer;\\n  font-size: 14px;\\n  color: #333;\\n  width: 100%;\\n  line-height: 1.2;\\n}\\n\\n.dot-dropdown-menu[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n  display: block;\\n  height: 16px;\\n  width: 16px;\\n  vertical-align: middle;\\n}\\n\\n.dropdown-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: inline-block;\\n  width: 30%;\\n}\\n\\n\\n\\n.agent-name-display[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: inline-block;\\n  width: 30%;\\n}\\n.agent-name-display[_ngcontent-%COMP%]   .disabled-agent-name-input[_ngcontent-%COMP%]     .ava-textbox input {\\n  background-color: #f8f9fa !important;\\n  color: #6c757d !important;\\n  cursor: not-allowed !important;\\n  border-color: #e9ecef !important;\\n}\\n\\n.tool-dropdown-menu[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 100%; \\n\\n  left: 0;\\n  display: flex;\\n  flex-direction: column;\\n  background: white;\\n  border: 1px solid #ccc;\\n  border-radius: 6px;\\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);\\n  z-index: 10;\\n  min-width: 140px;\\n}\\n\\n\\n\\n.toggle-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: row;\\n  gap: 16px;\\n  align-items: center;\\n  padding: 12px 16px;\\n  border-top: 1px solid #e9ecef;\\n  background: #f8f9fa;\\n  min-height: 56px;\\n  flex-shrink: 0; \\n\\n  border-bottom-left-radius: 8px;\\n  border-bottom-right-radius: 8px;\\n}\\n\\n.toggle-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: flex-start;\\n  min-height: 32px;\\n  white-space: nowrap;\\n}\\n\\n\\n\\n.uploaded-files[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 100%;\\n  left: 0;\\n  right: 0;\\n  background: white;\\n  border: 1px solid #e0e0e0;\\n  border-bottom: none;\\n  border-radius: 8px 8px 0 0;\\n  padding: 12px;\\n  max-height: 120px;\\n  overflow-y: auto;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);\\n}\\n\\n.file-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  background: #f8f9fa;\\n  border: 1px solid #e9ecef;\\n  border-radius: 8px;\\n  padding: 12px 16px;\\n  font-size: 14px;\\n  min-height: 48px;\\n  transition: all 0.2s ease;\\n  position: relative;\\n}\\n.file-item[_ngcontent-%COMP%]:hover {\\n  background: #e9ecef;\\n  border-color: #ced4da;\\n}\\n\\n.file-name[_ngcontent-%COMP%] {\\n  flex: 1;\\n  font-weight: 500;\\n  color: #495057;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n  margin-right: 12px;\\n}\\n.file-name[_ngcontent-%COMP%]::before {\\n  content: \\\"\\uD83D\\uDCC4\\\";\\n  margin-right: 8px;\\n  font-size: 16px;\\n}\\n\\n.remove-file[_ngcontent-%COMP%] {\\n  background: #dc3545;\\n  border: none;\\n  color: white;\\n  cursor: pointer;\\n  font-size: 12px;\\n  line-height: 1;\\n  padding: 4px 8px;\\n  border-radius: 4px;\\n  font-weight: 500;\\n  transition: background-color 0.2s ease;\\n}\\n.remove-file[_ngcontent-%COMP%]:hover {\\n  background: #c82333;\\n}\\n.remove-file[_ngcontent-%COMP%]::after {\\n  content: \\\"Remove\\\";\\n}\\n\\n\\n\\n[_nghost-%COMP%]     .message-content .file-attachment-info {\\n  background: #f8f9fa;\\n  border: 1px solid #e9ecef;\\n  border-radius: 6px;\\n  padding: 8px 12px;\\n  margin-top: 8px;\\n  font-size: 12px;\\n  color: #6c757d;\\n}\\n[_nghost-%COMP%]     .message-content .file-attachment-info::before {\\n  content: \\\"\\uD83D\\uDCCE\\\";\\n  margin-right: 6px;\\n}\\n\\n.copied-toast[_ngcontent-%COMP%] {\\n  position: fixed;\\n  bottom: 24px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  background-color: #333;\\n  color: #fff;\\n  padding: 8px 16px;\\n  border-radius: 8px;\\n  font-size: 14px;\\n  z-index: 1000;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.25);\\n  animation: _ngcontent-%COMP%_fadeInOut 2s ease-in-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeInOut {\\n  0% {\\n    opacity: 0;\\n    transform: translateX(-50%) translateY(10px);\\n  }\\n  10%, 90% {\\n    opacity: 1;\\n    transform: translateX(-50%) translateY(0);\\n  }\\n  100% {\\n    opacity: 0;\\n    transform: translateX(-50%) translateY(-10px);\\n  }\\n}\\n.input-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  background: #fff;\\n  border: 2px solid #03ACC1;\\n  border-radius: 16px;\\n  padding: 12px;\\n  margin: 1rem;\\n  box-sizing: border-box;\\n  min-height: 80px;\\n}\\n\\n\\n\\n.input-container[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border: none;\\n  resize: none;\\n  background: transparent;\\n  font-size: 14px;\\n  font-family: \\\"Inter\\\", sans-serif;\\n  line-height: 1.4;\\n  outline: none;\\n  padding: 0;\\n  padding-right: 48px; \\n\\n  box-sizing: border-box;\\n  min-height: 3em; \\n\\n  max-height: 4.2em; \\n\\n  overflow-y: auto;\\n}\\n\\n.input-container[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]::placeholder {\\n  color: #999;\\n}\\n\\n\\n\\n.attach-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 8px;\\n  left: 12px;\\n  background: none;\\n  border: none;\\n  padding: 4px;\\n  cursor: pointer;\\n}\\n\\n.attach-btn[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 18px;\\n  height: 18px;\\n  fill: #e91e63;\\n}\\n\\n\\n\\n.right-icons[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 8px;\\n  right: 12px;\\n  display: flex;\\n  gap: 8px;\\n}\\n\\n.right-icons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  padding: 4px;\\n}\\n\\n.right-icons[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 18px;\\n  height: 18px;\\n  fill: #e91e63;\\n}\\n\\n\\n\\n.agent-details-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  margin-right: 16px;\\n  padding: 12px 16px;\\n  background: var(--color-surface-secondary, #f8f9fa);\\n  border: 1px solid var(--color-border-primary, #e1e5e9);\\n  border-radius: 8px;\\n}\\n.agent-details-container[_ngcontent-%COMP%]   .agent-info[_ngcontent-%COMP%]   .agent-name[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: var(--color-text-primary, #1a1d21);\\n  margin: 0 0 4px 0;\\n}\\n.agent-details-container[_ngcontent-%COMP%]   .agent-info[_ngcontent-%COMP%]   .agent-description[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: var(--color-text-secondary, #6c757d);\\n  margin: 0 0 8px 0;\\n  line-height: 1.4;\\n}\\n.agent-details-container[_ngcontent-%COMP%]   .agent-info[_ngcontent-%COMP%]   .agent-meta[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 4px;\\n}\\n.agent-details-container[_ngcontent-%COMP%]   .agent-info[_ngcontent-%COMP%]   .agent-meta[_ngcontent-%COMP%]   .agent-role[_ngcontent-%COMP%], \\n.agent-details-container[_ngcontent-%COMP%]   .agent-info[_ngcontent-%COMP%]   .agent-meta[_ngcontent-%COMP%]   .agent-goal[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: var(--color-text-tertiary, #8e9297);\\n  font-weight: 500;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3Byb2plY3RzL3NoYXJlZC9jb21wb25lbnRzL3BsYXlncm91bmQvcGxheWdyb3VuZC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSxnQkFBZ0I7QUFBaEI7RUFDRSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSxnQ0FBQTtFQUNBLFdBQUE7RUFDQSxZQUFBLEVBQUEsaUNBQUE7RUFDQSxnQkFBQSxFQUFBLHlCQUFBO0VBQ0EsZ0JBQUEsRUFBQSwrQkFBQTtBQUVGO0FBREU7RUFSRjtJQVNNLFVBQUE7RUFJSjtBQUNGO0FBRkk7RUFaSjtJQWFNLFVBQUE7RUFLSjtBQUNGOztBQURBLHlCQUFBO0FBQ0E7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxtQkFBQTtFQUNBLDhCQUFBO0VBQ0EsaUJBQUE7RUFDQSxZQUFBO0VBQ0EsY0FBQTtFQUNBLDZCQUFBO0VBQ0EseUJBQUE7QUFJRjs7QUFEQSxvQkFBQTtBQUNBO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsOEJBQUE7RUFDQSxpQkFBQTtFQUNBLHNCQUFBO0VBQ0Esa0JBQUE7RUFDQSxpQkFBQTtFQUNBLFdBQUE7RUFDQSxlQUFBO0VBQ0EsZUFBQTtFQUNBLGdCQUFBO0FBSUY7O0FBREE7RUFDRSxXQUFBO0VBQ0EsV0FBQTtFQUNBLGdCQUFBO0FBSUY7O0FBREEsMkJBQUE7QUFDQTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFFBQUE7QUFJRjs7QUFVQSxtQkFBQTtBQUNBO0VBQ0UsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsdUJBQUE7RUFDQSxtQkFBQTtFQUNBLFFBQUE7RUFDQSxlQUFBO0FBUEY7O0FBVUE7RUFDRSxjQUFBO0VBQ0EsVUFBQTtFQUNBLFdBQUE7RUFDQSxpQkFBQTtFQUNBLGtCQUFBO0FBUEY7O0FBVUEsdUJBQUE7QUFDQTtFQUNFLGFBQUE7RUFDQSxzQkFBQTtFQUNBLFNBQUE7RUFDQSxPQUFBLEVBQUEseUJBQUE7RUFDQSxhQUFBO0VBQ0EsZ0JBQUEsRUFBQSwwQ0FBQTtFQUNBLGtCQUFBLEVBQUEsMkJBQUE7RUFDQSxnQkFBQTtFQUNBLG9CQUFBO0VBQ0EsMkJBQUE7RUFDQSxpQkFBQSxFQUFBLHlDQUFBO0VBQ0EsZ0JBQUEsRUFBQSw4QkFBQTtBQVBGOztBQVVBLCtCQUFBO0FBQ0E7RUFDRSxjQUFBO0VBQ0EsZUFBQTtFQUNBLGtCQUFBO0VBQ0Esa0JBQUE7QUFQRjs7QUFVQTtFQUNFLGFBQUE7RUFDQSxXQUFBO0FBUEY7O0FBVUE7RUFDRSwyQkFBQTtBQVBGOztBQVVBO0VBQ0UseUJBQUE7QUFQRjs7QUFVQSwyQ0FBQTtBQUVBO0VBQ0Usb0JBQUEsRUFBQSwyQkFBQTtFQUNBLHNCQUFBO0VBQ0EsdUJBQUE7RUFDQSx1QkFBQSxFQUFBLDZCQUFBO0VBQ0Esb0JBQUEsRUFBQSx1Q0FBQTtFQUNBLG1CQUFBO0VBQ0EsV0FBQTtFQUNBLGtCQUFBO0VBQ0Esa0JBQUE7RUFDQSxjQUFBO0VBQ0EscUJBQUE7RUFDQSxnQkFBQTtFQUNBLHFCQUFBO0FBUkY7O0FBV0E7RUFDRSxnQkFBQTtFQUNBLFVBQUE7QUFSRjs7QUFXQSw2QkFBQTtBQUNBO0VBQ0Usc0JBQUE7RUFDQSxtQkFBQSxFQUFBLG1CQUFBO0VBQ0EsV0FBQTtFQUNBLGFBQUE7RUFDQSxrQkFBQTtFQUNBLGtCQUFBO0VBQ0Esc0JBQUE7RUFDQSx1QkFBQTtFQUNBLFNBQUE7RUFDQSxxQkFBQTtFQUNBLGtCQUFBO0VBQ0EsbUJBQUE7QUFSRjs7QUFZQTtFQUNFLGtCQUFBO0VBQ0EsU0FBQTtFQUNBLFdBQUE7RUFDQSxnQkFBQTtFQUNBLFlBQUE7RUFDQSxlQUFBO0VBQ0EsWUFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtBQVRGO0FBV0U7RUFDRSx5Q0FBQTtFQUNBLDBCQUFBO0FBVEo7QUFZRTtFQUNFLGFBQUE7QUFWSjs7QUFjQSw0QkFBQTtBQUNBO0VBQ0UsZ0JBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7QUFYRjs7QUFjQSx3Q0FBQTtBQUNBO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsUUFBQTtBQVhGOztBQWNBO0VBQ0UsVUFBQTtFQUNBLFdBQUE7RUFDQSxrQkFBQTtFQUNBLHNCQUFBO0VBQ0EsMkNBQUE7QUFYRjs7QUFjQTtFQUNFLG1CQUFBO0FBWEY7O0FBY0E7RUFDRSxxQkFBQTtBQVhGOztBQWNBO0VBQ0UscUJBQUE7QUFYRjs7QUFjQTtFQUNFO0lBQ0UsbUJBQUE7SUFDQSxZQUFBO0VBWEY7RUFhQTtJQUNFLHFCQUFBO0lBQ0EsVUFBQTtFQVhGO0FBQ0Y7QUFjQSw0QkFBQTtBQUNBO0VBQ0UsYUFBQTtFQUNBLFlBQUE7RUFDQSxpQkFBQTtFQUNBLHVCQUFBO0VBQ0EsbUJBQUE7RUFDQSxrQkFBQTtFQUNBLG1CQUFBO0VBQ0EsZUFBQTtFQUNBLFdBQUE7RUFDQSxxQkFBQTtBQVpGOztBQWVBLDhCQUFBO0FBRUEscUJBQUE7QUFDQTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFFBQUE7RUFDQSxlQUFBO0VBQ0EsaUJBQUE7RUFDQSxnQ0FBQTtFQUNBLGNBQUEsRUFBQSxrQ0FBQTtFQUNBLFlBQUEsRUFBQSxpQkFBQTtFQUNBLGdCQUFBLEVBQUEseUJBQUE7QUFiRjs7QUFnQkEsa0JBQUE7QUFDQTtFQUNFLGtCQUFBO0VBQ0EscUJBQUE7RUFDQSxXQUFBO0VBQ0EsWUFBQTtBQWJGOztBQWdCQTtFQUNFLFVBQUE7RUFDQSxRQUFBO0VBQ0EsU0FBQTtBQWJGOztBQWdCQTtFQUNFLGtCQUFBO0VBQ0EsZUFBQTtFQUNBLE1BQUE7RUFDQSxPQUFBO0VBQ0EsUUFBQTtFQUNBLFNBQUE7RUFDQSxzQkFBQTtFQUNBLGdCQUFBO0VBQ0EsbUJBQUE7QUFiRjs7QUFnQkE7RUFDRSxrQkFBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0VBQ0EsV0FBQTtFQUNBLFNBQUE7RUFDQSxXQUFBO0VBQ0EsdUJBQUE7RUFDQSxnQkFBQTtFQUNBLGtCQUFBO0FBYkY7O0FBZ0JBO0VBQ0UseUJBQUE7QUFiRjs7QUFnQkE7RUFDRSwyQkFBQTtBQWJGOztBQWdCQTtFQUNFLFdBQUE7QUFiRjs7QUFnQkE7RUFDRSxrQkFBQTtFQUNBLFdBQUE7RUFDQSxVQUFBO0VBQ0EsaUJBQUE7RUFDQSxzQkFBQTtFQUNBLGtCQUFBO0VBQ0EseUNBQUE7RUFDQSxXQUFBO0VBQ0EsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsZ0JBQUE7QUFiRjs7QUFnQkE7RUFDRSxtQkFBQTtBQWJGOztBQWdCQTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLDJCQUFBO0VBQ0EsUUFBQTtFQUNBLGtCQUFBO0VBQ0EsWUFBQTtFQUNBLGdCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxlQUFBO0VBQ0EsZUFBQTtFQUNBLFdBQUE7RUFDQSxXQUFBO0VBQ0EsZ0JBQUE7QUFiRjs7QUFnQkE7RUFDRSxjQUFBO0VBQ0EsY0FBQTtFQUNBLFlBQUE7RUFDQSxXQUFBO0VBQ0Esc0JBQUE7QUFiRjs7QUFpQkE7RUFDRSxrQkFBQTtFQUNBLHFCQUFBO0VBQ0EsVUFBQTtBQWRGOztBQWlCQSx1QkFBQTtBQUNBO0VBQ0Usa0JBQUE7RUFDQSxxQkFBQTtFQUNBLFVBQUE7QUFkRjtBQWtCTTtFQUNFLG9DQUFBO0VBQ0EseUJBQUE7RUFDQSw4QkFBQTtFQUNBLGdDQUFBO0FBaEJSOztBQXNCQTtFQUNFLGtCQUFBO0VBQ0EsU0FBQSxFQUFBLHVCQUFBO0VBQ0EsT0FBQTtFQUNBLGFBQUE7RUFDQSxzQkFBQTtFQUNBLGlCQUFBO0VBQ0Esc0JBQUE7RUFDQSxrQkFBQTtFQUNBLHlDQUFBO0VBQ0EsV0FBQTtFQUNBLGdCQUFBO0FBbkJGOztBQXNCQSw4Q0FBQTtBQUNBO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsU0FBQTtFQUNBLG1CQUFBO0VBQ0Esa0JBQUE7RUFDQSw2QkFBQTtFQUNBLG1CQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBLEVBQUEsc0JBQUE7RUFDQSw4QkFBQTtFQUNBLCtCQUFBO0FBbkJGOztBQXNCQTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLDJCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxtQkFBQTtBQW5CRjs7QUFzQkEsdUJBQUE7QUFDQTtFQUNFLGtCQUFBO0VBQ0EsWUFBQTtFQUNBLE9BQUE7RUFDQSxRQUFBO0VBQ0EsaUJBQUE7RUFDQSx5QkFBQTtFQUNBLG1CQUFBO0VBQ0EsMEJBQUE7RUFDQSxhQUFBO0VBQ0EsaUJBQUE7RUFDQSxnQkFBQTtFQUNBLGFBQUE7RUFDQSxzQkFBQTtFQUNBLFFBQUE7RUFDQSx5Q0FBQTtBQW5CRjs7QUFzQkE7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxtQkFBQTtFQUNBLHlCQUFBO0VBQ0Esa0JBQUE7RUFDQSxrQkFBQTtFQUNBLGVBQUE7RUFDQSxnQkFBQTtFQUNBLHlCQUFBO0VBQ0Esa0JBQUE7QUFuQkY7QUFxQkU7RUFDRSxtQkFBQTtFQUNBLHFCQUFBO0FBbkJKOztBQXVCQTtFQUNFLE9BQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxnQkFBQTtFQUNBLHVCQUFBO0VBQ0EsbUJBQUE7RUFDQSxrQkFBQTtBQXBCRjtBQXNCRTtFQUNFLGFBQUE7RUFDQSxpQkFBQTtFQUNBLGVBQUE7QUFwQko7O0FBd0JBO0VBQ0UsbUJBQUE7RUFDQSxZQUFBO0VBQ0EsWUFBQTtFQUNBLGVBQUE7RUFDQSxlQUFBO0VBQ0EsY0FBQTtFQUNBLGdCQUFBO0VBQ0Esa0JBQUE7RUFDQSxnQkFBQTtFQUNBLHNDQUFBO0FBckJGO0FBdUJFO0VBQ0UsbUJBQUE7QUFyQko7QUF3QkU7RUFDRSxpQkFBQTtBQXRCSjs7QUEwQkEseUNBQUE7QUFFRTtFQUNFLG1CQUFBO0VBQ0EseUJBQUE7RUFDQSxrQkFBQTtFQUNBLGlCQUFBO0VBQ0EsZUFBQTtFQUNBLGVBQUE7RUFDQSxjQUFBO0FBeEJKO0FBMEJJO0VBQ0UsYUFBQTtFQUNBLGlCQUFBO0FBeEJOOztBQStCQTtFQUNFLGVBQUE7RUFDQSxZQUFBO0VBQ0EsU0FBQTtFQUNBLDJCQUFBO0VBQ0Esc0JBQUE7RUFDQSxXQUFBO0VBQ0EsaUJBQUE7RUFDQSxrQkFBQTtFQUNBLGVBQUE7RUFDQSxhQUFBO0VBQ0EseUNBQUE7RUFDQSxtQ0FBQTtBQTVCRjs7QUErQkE7RUFDRTtJQUNFLFVBQUE7SUFDQSw0Q0FBQTtFQTVCRjtFQThCQTtJQUNFLFVBQUE7SUFDQSx5Q0FBQTtFQTVCRjtFQThCQTtJQUNFLFVBQUE7SUFDQSw2Q0FBQTtFQTVCRjtBQUNGO0FBZ0NBO0VBQ0Usa0JBQUE7RUFDQSxnQkFBQTtFQUNBLHlCQUFBO0VBQ0EsbUJBQUE7RUFDQSxhQUFBO0VBQ0EsWUFBQTtFQUNBLHNCQUFBO0VBQ0EsZ0JBQUE7QUE5QkY7O0FBaUNBLGlDQUFBO0FBQ0E7RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLFlBQUE7RUFDQSx1QkFBQTtFQUNBLGVBQUE7RUFDQSxnQ0FBQTtFQUNBLGdCQUFBO0VBQ0EsYUFBQTtFQUNBLFVBQUE7RUFDQSxtQkFBQSxFQUFBLDBCQUFBO0VBQ0Esc0JBQUE7RUFDQSxlQUFBLEVBQUEsZ0JBQUE7RUFDQSxpQkFBQSxFQUFBLGFBQUE7RUFDQSxnQkFBQTtBQTlCRjs7QUFpQ0E7RUFDRSxXQUFBO0FBOUJGOztBQWlDQSw0QkFBQTtBQUNBO0VBQ0Usa0JBQUE7RUFDQSxXQUFBO0VBQ0EsVUFBQTtFQUNBLGdCQUFBO0VBQ0EsWUFBQTtFQUNBLFlBQUE7RUFDQSxlQUFBO0FBOUJGOztBQWlDQTtFQUNFLFdBQUE7RUFDQSxZQUFBO0VBQ0EsYUFBQTtBQTlCRjs7QUFpQ0EsK0JBQUE7QUFDQTtFQUNFLGtCQUFBO0VBQ0EsV0FBQTtFQUNBLFdBQUE7RUFDQSxhQUFBO0VBQ0EsUUFBQTtBQTlCRjs7QUFpQ0E7RUFDRSxnQkFBQTtFQUNBLFlBQUE7RUFDQSxlQUFBO0VBQ0EsWUFBQTtBQTlCRjs7QUFpQ0E7RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGFBQUE7QUE5QkY7O0FBaUNBLDRCQUFBO0FBQ0E7RUFDRSxPQUFBO0VBQ0Esa0JBQUE7RUFDQSxrQkFBQTtFQUNBLG1EQUFBO0VBQ0Esc0RBQUE7RUFDQSxrQkFBQTtBQTlCRjtBQWlDSTtFQUNFLGVBQUE7RUFDQSxnQkFBQTtFQUNBLHlDQUFBO0VBQ0EsaUJBQUE7QUEvQk47QUFrQ0k7RUFDRSxlQUFBO0VBQ0EsMkNBQUE7RUFDQSxpQkFBQTtFQUNBLGdCQUFBO0FBaENOO0FBbUNJO0VBQ0UsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsUUFBQTtBQWpDTjtBQW1DTTs7RUFFRSxlQUFBO0VBQ0EsMENBQUE7RUFDQSxnQkFBQTtBQWpDUiIsInNvdXJjZXNDb250ZW50IjpbIi5wbGF5Z3JvdW5kLWNvbnRhaW5lciB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gIGZvbnQtZmFtaWx5OiBcIkludGVyXCIsIHNhbnMtc2VyaWY7XHJcbiAgd2lkdGg6IDEwMCU7XHJcbiAgaGVpZ2h0OiA3OHZoOyAvKiBBdXRvIGhlaWdodCBpbnN0ZWFkIG9mIGZpeGVkICovXHJcbiAgbWF4LWhlaWdodDogMTAwJTsgLyogVXNlIGF2YWlsYWJsZSBoZWlnaHQgKi9cclxuICBvdmVyZmxvdzogaGlkZGVuOyAvKiBQcmV2ZW50IGNvbnRhaW5lciBvdmVyZmxvdyAqL1xyXG4gIEBtZWRpYSAobWF4LXdpZHRoOiAxNDAwcHgpIHtcclxuICAgICAgd2lkdGg6IDYwJTtcclxuICAgIH1cclxuXHJcbiAgICBAbWVkaWEgKG1heC13aWR0aDogMTIwMHB4KSB7XHJcbiAgICAgIHdpZHRoOiA2MCU7XHJcbiAgICB9XHJcblxyXG59XHJcblxyXG4vKiBUb3AgQnV0dG9uIENvbnRhaW5lciAqL1xyXG4uYnV0dG9uLWNvbnRhaW5lciB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBmbGV4LWRpcmVjdGlvbjogcm93O1xyXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xyXG4gIHBhZGRpbmc6IDhweCAxMnB4O1xyXG4gIGhlaWdodDogNDhweDtcclxuICBmbGV4LXNocmluazogMDtcclxuICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2VlZTtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiNmZmZmZmY7XHJcbn1cclxuXHJcbi8qIERyb3Bkb3duIEJ1dHRvbiAqL1xyXG4uZHJvcGRvd24tYnRuIHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xyXG4gIHBhZGRpbmc6IDhweCAxMnB4O1xyXG4gIGJvcmRlcjogMXB4IHNvbGlkICNjY2M7XHJcbiAgYm9yZGVyLXJhZGl1czogNnB4O1xyXG4gIGJhY2tncm91bmQ6IHdoaXRlO1xyXG4gIGNvbG9yOiAjMzMzO1xyXG4gIGZvbnQtc2l6ZTogMTRweDtcclxuICBjdXJzb3I6IHBvaW50ZXI7XHJcbiAgbWluLXdpZHRoOiAxMDBweDtcclxufVxyXG5cclxuLmRyb3Bkb3duLWJ0biAuYXJyb3ctZG93biB7XHJcbiAgd2lkdGg6IDEwcHg7XHJcbiAgaGVpZ2h0OiA2cHg7XHJcbiAgbWFyZ2luLWxlZnQ6IDhweDtcclxufVxyXG5cclxuLyogQWN0aW9uIEJ1dHRvbiBhbmQgTWVudSAqL1xyXG4uYnRuLW1lbnUge1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICBnYXA6IDhweDtcclxufVxyXG5cclxuLy8uYWN0aW9uLWJ0biB7XHJcbiAgLy9wYWRkaW5nOiAwLjI1cmVtIDAuNXJlbTtcclxuICAvL2JhY2tncm91bmQ6IGxpZ2h0Ymx1ZTsgLy9yZXBsYWNlIHRoaXNcclxuICAvL2NvbG9yOiB3aGl0ZTtcclxuICAvL2JvcmRlcjogbm9uZTtcclxuICAvL2JvcmRlci1yYWRpdXM6IDZweDtcclxuICAvL2N1cnNvcjogcG9pbnRlcjtcclxuICAvL2ZvbnQtc2l6ZTogMTRweDtcclxuLy99XHJcblxyXG5cclxuLyogVGhyZWUgRG90IE1lbnUgKi9cclxuLm1lbnUtaWNvbiB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xyXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgZ2FwOiAzcHg7XHJcbiAgY3Vyc29yOiBwb2ludGVyO1xyXG59XHJcblxyXG4ubWVudS1pY29uIHNwYW4ge1xyXG4gIGRpc3BsYXk6IGJsb2NrO1xyXG4gIHdpZHRoOiAzcHg7XHJcbiAgaGVpZ2h0OiAzcHg7XHJcbiAgYmFja2dyb3VuZDogYmxhY2s7XHJcbiAgYm9yZGVyLXJhZGl1czogNTAlO1xyXG59XHJcblxyXG4vKiBMYXlvdXQgKGNoYXQgYXJlYSkgKi9cclxuLmxheW91dCB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gIGdhcDogMTZweDtcclxuICBmbGV4OiAxOyAvKiBmaWxsIGF2YWlsYWJsZSBzcGFjZSAqL1xyXG4gIHBhZGRpbmc6IDE2cHg7XHJcbiAgb3ZlcmZsb3cteTogYXV0bzsgLyogRW5hYmxlIHNjcm9sbGluZyB3aXRoaW4gdGhlIGNoYXQgYXJlYSAqL1xyXG4gIG92ZXJmbG93LXg6IGhpZGRlbjsgLyogSGlkZSBob3Jpem9udGFsIHNjcm9sbCAqL1xyXG4gIGJhY2tncm91bmQ6ICNmZmY7XHJcbiAgYWxpZ24taXRlbXM6IHN0cmV0Y2g7XHJcbiAganVzdGlmeS1jb250ZW50OiBmbGV4LXN0YXJ0O1xyXG4gIG1pbi1oZWlnaHQ6IDMwMHB4OyAvKiBNaW5pbXVtIGhlaWdodCBmb3IgYmV0dGVyIGFwcGVhcmFuY2UgKi9cclxuICBtYXgtaGVpZ2h0OiBub25lOyAvKiBSZW1vdmUgaGVpZ2h0IHJlc3RyaWN0aW9uICovXHJcbn1cclxuXHJcbi8qIENoYXQgbWVzc2FnZXMgY29tbW9uIHN0eWxlICovXHJcbi5tZXNzYWdlIHtcclxuICBtYXgtd2lkdGg6IDYwJTtcclxuICBmb250LXNpemU6IDE0cHg7XHJcbiAgYm9yZGVyLXJhZGl1czogOHB4O1xyXG4gIHBhZGRpbmc6IDE2cHggMjRweDtcclxufVxyXG5cclxuLm1lc3NhZ2Utcm93IHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIHdpZHRoOiAxMDAlO1xyXG59XHJcblxyXG4ubWVzc2FnZS1yb3cuYWkge1xyXG4gIGp1c3RpZnktY29udGVudDogZmxleC1zdGFydDtcclxufVxyXG5cclxuLm1lc3NhZ2Utcm93LnVzZXIge1xyXG4gIGp1c3RpZnktY29udGVudDogZmxleC1lbmQ7XHJcbn1cclxuXHJcbi8qIFVzZXIgbWVzc2FnZXMgKGxlZnQgc2lkZSwgc2FtZSBhcyBib3QpICovXHJcblxyXG4udXNlci1tZXNzYWdlIHtcclxuICBkaXNwbGF5OiBpbmxpbmUtZmxleDsgLyogYnViYmxlIGZpdHMgdG8gY29udGVudCAqL1xyXG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcbiAgYWxpZ24taXRlbXM6IGZsZXgtc3RhcnQ7IC8qIHRleHQgbGVmdC1hbGlnbmVkIGluc2lkZSAqL1xyXG4gIGFsaWduLXNlbGY6IGZsZXgtZW5kOyAvKiBwb3NpdGlvbnMgZW50aXJlIGJ1YmJsZSByaWdodCBzaWRlICovXHJcbiAgYmFja2dyb3VuZDogI2MyYzRjZDtcclxuICBjb2xvcjogIzMzMztcclxuICBib3JkZXItcmFkaXVzOiA4cHg7XHJcbiAgcGFkZGluZzogMTZweCAyNHB4O1xyXG4gIG1heC13aWR0aDogNjAlO1xyXG4gIHdvcmQtd3JhcDogYnJlYWstd29yZDtcclxuICB0ZXh0LWFsaWduOiBsZWZ0O1xyXG4gIG1hcmdpbi1ib3R0b206IDAuNXJlbTtcclxufVxyXG5cclxuLnVzZXItbWVzc2FnZTplbXB0eSB7XHJcbiAgYmFja2dyb3VuZDogbm9uZTtcclxuICBwYWRkaW5nOiAwO1xyXG59XHJcblxyXG4vKiBCb3QgbWVzc2FnZXMgKGxlZnQgc2lkZSkgKi9cclxuLmJvdC1yZXNwb25zZSB7XHJcbiAgYWxpZ24tc2VsZjogZmxleC1zdGFydDtcclxuICBiYWNrZ3JvdW5kOiAjZjFmMWYxOyAvKiBOZXV0cmFscy1OLTEwMCAqL1xyXG4gIGNvbG9yOiAjMzMzO1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgcGFkZGluZzogMTZweCAyNHB4O1xyXG4gIGJvcmRlci1yYWRpdXM6IDhweDtcclxuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xyXG4gIGdhcDogMTBweDtcclxuICBtYXJnaW4tYm90dG9tOiAwLjVyZW07XHJcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xyXG4gIHBhZGRpbmctcmlnaHQ6IDQwcHg7XHJcbn1cclxuXHJcblxyXG4uY29weS1idG4ge1xyXG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcclxuICB0b3A6IDE2cHg7XHJcbiAgcmlnaHQ6IDE2cHg7XHJcbiAgYmFja2dyb3VuZDogbm9uZTtcclxuICBib3JkZXI6IG5vbmU7XHJcbiAgY3Vyc29yOiBwb2ludGVyO1xyXG4gIHBhZGRpbmc6IDRweDtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcblxyXG4gIHN2ZyB7XHJcbiAgICBmaWxsOiB2YXIoLS1jb2xvci1icmFuZC1wcmltYXJ5LCAjMTQ0NjkyKTsgLy8gb3IgeW91ciBicmFuZCBjb2xvclxyXG4gICAgdHJhbnNpdGlvbjogZmlsbCAwLjJzIGVhc2U7XHJcbiAgfVxyXG5cclxuICAmOmhvdmVyIHN2ZyB7XHJcbiAgICBmaWxsOiAjMWQ0ZWQ4OyAvLyBkYXJrZXIgYmx1ZSBvbiBob3ZlclxyXG4gIH1cclxufVxyXG5cclxuLyogTG9hZGluZyBtZXNzYWdlIHN0eWxpbmcgKi9cclxuLmxvYWRpbmctbWVzc2FnZSB7XHJcbiAgbWluLWhlaWdodDogMjBweDtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbn1cclxuXHJcbi8qIFR5cGluZyBpbmRpY2F0b3Igd2l0aCBhbmltYXRlZCBkb3RzICovXHJcbi50eXBpbmctaW5kaWNhdG9yIHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgZ2FwOiA0cHg7XHJcbn1cclxuXHJcbi50eXBpbmctaW5kaWNhdG9yIC5kb3Qge1xyXG4gIHdpZHRoOiA4cHg7XHJcbiAgaGVpZ2h0OiA4cHg7XHJcbiAgYm9yZGVyLXJhZGl1czogNTAlO1xyXG4gIGJhY2tncm91bmQtY29sb3I6ICM2NjY7XHJcbiAgYW5pbWF0aW9uOiB0eXBpbmcgMS40cyBpbmZpbml0ZSBlYXNlLWluLW91dDtcclxufVxyXG5cclxuLnR5cGluZy1pbmRpY2F0b3IgLmRvdDpudGgtY2hpbGQoMSkge1xyXG4gIGFuaW1hdGlvbi1kZWxheTogMHM7XHJcbn1cclxuXHJcbi50eXBpbmctaW5kaWNhdG9yIC5kb3Q6bnRoLWNoaWxkKDIpIHtcclxuICBhbmltYXRpb24tZGVsYXk6IDAuMnM7XHJcbn1cclxuXHJcbi50eXBpbmctaW5kaWNhdG9yIC5kb3Q6bnRoLWNoaWxkKDMpIHtcclxuICBhbmltYXRpb24tZGVsYXk6IDAuNHM7XHJcbn1cclxuXHJcbkBrZXlmcmFtZXMgdHlwaW5nIHtcclxuICAwJSwgNjAlLCAxMDAlIHtcclxuICAgIHRyYW5zZm9ybTogc2NhbGUoMSk7XHJcbiAgICBvcGFjaXR5OiAwLjU7XHJcbiAgfVxyXG4gIDMwJSB7XHJcbiAgICB0cmFuc2Zvcm06IHNjYWxlKDEuMik7XHJcbiAgICBvcGFjaXR5OiAxO1xyXG4gIH1cclxufVxyXG5cclxuLyogUmVzdWx0IExhYmVsIChvcHRpb25hbCkgKi9cclxuLnJlc3VsdC1sYWJlbCB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICB3aWR0aDogMTg2cHg7XHJcbiAgcGFkZGluZzogOHB4IDEycHg7XHJcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICBib3JkZXItcmFkaXVzOiA4cHg7XHJcbiAgYmFja2dyb3VuZDogI2YxZjFmMTtcclxuICBmb250LXNpemU6IDE0cHg7XHJcbiAgY29sb3I6ICMzMzM7XHJcbiAgbWFyZ2luLWJvdHRvbTogMC41cmVtO1xyXG59XHJcblxyXG4vKiBJbnB1dCBjb250YWluZXIgYXQgYm90dG9tICovXHJcblxyXG4vKiBUb2dnbGUgY29udGFpbmVyICovXHJcbi50b2dnbGUtY29udGFpbmVyIHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgZ2FwOiA4cHg7XHJcbiAgZm9udC1zaXplOiAxNHB4O1xyXG4gIHBhZGRpbmc6IDhweCAyNHB4O1xyXG4gIGZvbnQtZmFtaWx5OiBcIkludGVyXCIsIHNhbnMtc2VyaWY7XHJcbiAgZmxleC1zaHJpbms6IDA7IC8qIFByZXZlbnQgdG9nZ2xlIGZyb20gc2hyaW5raW5nICovXHJcbiAgaGVpZ2h0OiA0MHB4OyAvKiBGaXhlZCBoZWlnaHQgKi9cclxuICBtaW4taGVpZ2h0OiA0MHB4OyAvKiBGaXhlZCBtaW5pbXVtIGhlaWdodCAqL1xyXG59XHJcblxyXG4vKiBUb2dnbGUgU3dpdGNoICovXHJcbi50b2dnbGUtc3dpdGNoIHtcclxuICBwb3NpdGlvbjogcmVsYXRpdmU7XHJcbiAgZGlzcGxheTogaW5saW5lLWJsb2NrO1xyXG4gIHdpZHRoOiA0MHB4O1xyXG4gIGhlaWdodDogMjBweDtcclxufVxyXG5cclxuLnRvZ2dsZS1zd2l0Y2ggaW5wdXQge1xyXG4gIG9wYWNpdHk6IDA7XHJcbiAgd2lkdGg6IDA7XHJcbiAgaGVpZ2h0OiAwO1xyXG59XHJcblxyXG4uc2xpZGVyIHtcclxuICBwb3NpdGlvbjogYWJzb2x1dGU7XHJcbiAgY3Vyc29yOiBwb2ludGVyO1xyXG4gIHRvcDogMDtcclxuICBsZWZ0OiAwO1xyXG4gIHJpZ2h0OiAwO1xyXG4gIGJvdHRvbTogMDtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiAjY2NjO1xyXG4gIHRyYW5zaXRpb246IDAuNHM7XHJcbiAgYm9yZGVyLXJhZGl1czogMjBweDtcclxufVxyXG5cclxuLnNsaWRlcjpiZWZvcmUge1xyXG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcclxuICBjb250ZW50OiBcIlwiO1xyXG4gIGhlaWdodDogMTRweDtcclxuICB3aWR0aDogMTRweDtcclxuICBsZWZ0OiAzcHg7XHJcbiAgYm90dG9tOiAzcHg7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogd2hpdGU7XHJcbiAgdHJhbnNpdGlvbjogMC40cztcclxuICBib3JkZXItcmFkaXVzOiA1MCU7XHJcbn1cclxuXHJcbi50b2dnbGUtc3dpdGNoIGlucHV0OmNoZWNrZWQgKyAuc2xpZGVyIHtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiAjNGNhZjUwO1xyXG59XHJcblxyXG4udG9nZ2xlLXN3aXRjaCBpbnB1dDpjaGVja2VkICsgLnNsaWRlcjpiZWZvcmUge1xyXG4gIHRyYW5zZm9ybTogdHJhbnNsYXRlWCgyMHB4KTtcclxufVxyXG5cclxuLnRvZ2dsZS1sYWJlbCB7XHJcbiAgY29sb3I6ICMzMzM7XHJcbn1cclxuXHJcbi5kb3QtZHJvcGRvd24tbWVudSB7XHJcbiAgcG9zaXRpb246IGFic29sdXRlO1xyXG4gIHJpZ2h0OiAxMnB4O1xyXG4gIHRvcDogMTIwcHg7XHJcbiAgYmFja2dyb3VuZDogd2hpdGU7XHJcbiAgYm9yZGVyOiAxcHggc29saWQgI2NjYztcclxuICBib3JkZXItcmFkaXVzOiA2cHg7XHJcbiAgYm94LXNoYWRvdzogMCAycHggNnB4IHJnYmEoMCwgMCwgMCwgMC4xNSk7XHJcbiAgei1pbmRleDogMTA7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gIG1pbi13aWR0aDogMTIwcHg7XHJcbn1cclxuXHJcbi5kb3QtZHJvcGRvd24tbWVudSBidXR0b246aG92ZXIge1xyXG4gIGJhY2tncm91bmQ6ICNmMGYwZjA7XHJcbn1cclxuXHJcbi5kb3QtZHJvcGRvd24tbWVudSBidXR0b24ge1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICBqdXN0aWZ5LWNvbnRlbnQ6IGZsZXgtc3RhcnQ7XHJcbiAgZ2FwOiA4cHg7XHJcbiAgcGFkZGluZzogMTBweCAxNnB4O1xyXG4gIGJvcmRlcjogbm9uZTtcclxuICBiYWNrZ3JvdW5kOiBub25lO1xyXG4gIHRleHQtYWxpZ246IGxlZnQ7XHJcbiAgY3Vyc29yOiBwb2ludGVyO1xyXG4gIGZvbnQtc2l6ZTogMTRweDtcclxuICBjb2xvcjogIzMzMztcclxuICB3aWR0aDogMTAwJTtcclxuICBsaW5lLWhlaWdodDogMS4yO1xyXG59XHJcblxyXG4uZG90LWRyb3Bkb3duLW1lbnUgYnV0dG9uIHN2ZyB7XHJcbiAgZmxleC1zaHJpbms6IDA7XHJcbiAgZGlzcGxheTogYmxvY2s7XHJcbiAgaGVpZ2h0OiAxNnB4O1xyXG4gIHdpZHRoOiAxNnB4O1xyXG4gIHZlcnRpY2FsLWFsaWduOiBtaWRkbGU7XHJcbn1cclxuXHJcbi8vbGlzdCBkcm9wIGRvd25cclxuLmRyb3Bkb3duLWNvbnRhaW5lciB7XHJcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xyXG4gIGRpc3BsYXk6IGlubGluZS1ibG9jaztcclxuICB3aWR0aDogMzAlO1xyXG59XHJcblxyXG4vKiBBZ2VudCBOYW1lIERpc3BsYXkgKi9cclxuLmFnZW50LW5hbWUtZGlzcGxheSB7XHJcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xyXG4gIGRpc3BsYXk6IGlubGluZS1ibG9jaztcclxuICB3aWR0aDogMzAlO1xyXG5cclxuICAuZGlzYWJsZWQtYWdlbnQtbmFtZS1pbnB1dCB7XHJcbiAgICA6Om5nLWRlZXAgLmF2YS10ZXh0Ym94IHtcclxuICAgICAgaW5wdXQge1xyXG4gICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmOGY5ZmEgIWltcG9ydGFudDtcclxuICAgICAgICBjb2xvcjogIzZjNzU3ZCAhaW1wb3J0YW50O1xyXG4gICAgICAgIGN1cnNvcjogbm90LWFsbG93ZWQgIWltcG9ydGFudDtcclxuICAgICAgICBib3JkZXItY29sb3I6ICNlOWVjZWYgIWltcG9ydGFudDtcclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxufVxyXG5cclxuLnRvb2wtZHJvcGRvd24tbWVudSB7XHJcbiAgcG9zaXRpb246IGFic29sdXRlO1xyXG4gIHRvcDogMTAwJTsgLyogw6LCnMKFIGJlbG93IHRoZSBidXR0b24gKi9cclxuICBsZWZ0OiAwO1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxuICBiYWNrZ3JvdW5kOiB3aGl0ZTtcclxuICBib3JkZXI6IDFweCBzb2xpZCAjY2NjO1xyXG4gIGJvcmRlci1yYWRpdXM6IDZweDtcclxuICBib3gtc2hhZG93OiAwIDJweCA2cHggcmdiYSgwLCAwLCAwLCAwLjE1KTtcclxuICB6LWluZGV4OiAxMDtcclxuICBtaW4td2lkdGg6IDE0MHB4O1xyXG59XHJcblxyXG4vKiBUb2dnbGUgQ29udGFpbmVyIC0gcG9zaXRpb25lZCBiZWxvdyBpbnB1dCAqL1xyXG4udG9nZ2xlLWNvbnRhaW5lciB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBmbGV4LWRpcmVjdGlvbjogcm93O1xyXG4gIGdhcDogMTZweDtcclxuICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gIHBhZGRpbmc6IDEycHggMTZweDtcclxuICBib3JkZXItdG9wOiAxcHggc29saWQgI2U5ZWNlZjtcclxuICBiYWNrZ3JvdW5kOiAjZjhmOWZhO1xyXG4gIG1pbi1oZWlnaHQ6IDU2cHg7XHJcbiAgZmxleC1zaHJpbms6IDA7IC8qIFByZXZlbnQgc2hyaW5raW5nICovXHJcbiAgYm9yZGVyLWJvdHRvbS1sZWZ0LXJhZGl1czogOHB4O1xyXG4gIGJvcmRlci1ib3R0b20tcmlnaHQtcmFkaXVzOiA4cHg7XHJcbn1cclxuXHJcbi50b2dnbGUtcm93IHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAganVzdGlmeS1jb250ZW50OiBmbGV4LXN0YXJ0O1xyXG4gIG1pbi1oZWlnaHQ6IDMycHg7XHJcbiAgd2hpdGUtc3BhY2U6IG5vd3JhcDtcclxufVxyXG5cclxuLyogRmlsZSBVcGxvYWQgU3R5bGVzICovXHJcbi51cGxvYWRlZC1maWxlcyB7XHJcbiAgcG9zaXRpb246IGFic29sdXRlO1xyXG4gIGJvdHRvbTogMTAwJTtcclxuICBsZWZ0OiAwO1xyXG4gIHJpZ2h0OiAwO1xyXG4gIGJhY2tncm91bmQ6IHdoaXRlO1xyXG4gIGJvcmRlcjogMXB4IHNvbGlkICNlMGUwZTA7XHJcbiAgYm9yZGVyLWJvdHRvbTogbm9uZTtcclxuICBib3JkZXItcmFkaXVzOiA4cHggOHB4IDAgMDtcclxuICBwYWRkaW5nOiAxMnB4O1xyXG4gIG1heC1oZWlnaHQ6IDEyMHB4O1xyXG4gIG92ZXJmbG93LXk6IGF1dG87XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gIGdhcDogOHB4O1xyXG4gIGJveC1zaGFkb3c6IDAgLTJweCA4cHggcmdiYSgwLCAwLCAwLCAwLjEpO1xyXG59XHJcblxyXG4uZmlsZS1pdGVtIHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgYmFja2dyb3VuZDogI2Y4ZjlmYTtcclxuICBib3JkZXI6IDFweCBzb2xpZCAjZTllY2VmO1xyXG4gIGJvcmRlci1yYWRpdXM6IDhweDtcclxuICBwYWRkaW5nOiAxMnB4IDE2cHg7XHJcbiAgZm9udC1zaXplOiAxNHB4O1xyXG4gIG1pbi1oZWlnaHQ6IDQ4cHg7XHJcbiAgdHJhbnNpdGlvbjogYWxsIDAuMnMgZWFzZTtcclxuICBwb3NpdGlvbjogcmVsYXRpdmU7XHJcblxyXG4gICY6aG92ZXIge1xyXG4gICAgYmFja2dyb3VuZDogI2U5ZWNlZjtcclxuICAgIGJvcmRlci1jb2xvcjogI2NlZDRkYTtcclxuICB9XHJcbn1cclxuXHJcbi5maWxlLW5hbWUge1xyXG4gIGZsZXg6IDE7XHJcbiAgZm9udC13ZWlnaHQ6IDUwMDtcclxuICBjb2xvcjogIzQ5NTA1NztcclxuICBvdmVyZmxvdzogaGlkZGVuO1xyXG4gIHRleHQtb3ZlcmZsb3c6IGVsbGlwc2lzO1xyXG4gIHdoaXRlLXNwYWNlOiBub3dyYXA7XHJcbiAgbWFyZ2luLXJpZ2h0OiAxMnB4O1xyXG5cclxuICAmOjpiZWZvcmUge1xyXG4gICAgY29udGVudDogXCLDsMKfwpPChFwiO1xyXG4gICAgbWFyZ2luLXJpZ2h0OiA4cHg7XHJcbiAgICBmb250LXNpemU6IDE2cHg7XHJcbiAgfVxyXG59XHJcblxyXG4ucmVtb3ZlLWZpbGUge1xyXG4gIGJhY2tncm91bmQ6ICNkYzM1NDU7XHJcbiAgYm9yZGVyOiBub25lO1xyXG4gIGNvbG9yOiB3aGl0ZTtcclxuICBjdXJzb3I6IHBvaW50ZXI7XHJcbiAgZm9udC1zaXplOiAxMnB4O1xyXG4gIGxpbmUtaGVpZ2h0OiAxO1xyXG4gIHBhZGRpbmc6IDRweCA4cHg7XHJcbiAgYm9yZGVyLXJhZGl1czogNHB4O1xyXG4gIGZvbnQtd2VpZ2h0OiA1MDA7XHJcbiAgdHJhbnNpdGlvbjogYmFja2dyb3VuZC1jb2xvciAwLjJzIGVhc2U7XHJcblxyXG4gICY6aG92ZXIge1xyXG4gICAgYmFja2dyb3VuZDogI2M4MjMzMztcclxuICB9XHJcblxyXG4gICY6OmFmdGVyIHtcclxuICAgIGNvbnRlbnQ6IFwiUmVtb3ZlXCI7XHJcbiAgfVxyXG59XHJcblxyXG4vKiBDaGF0IG1lc3NhZ2UgZmlsZSBhdHRhY2htZW50IHN0eWxpbmcgKi9cclxuOmhvc3QgOjpuZy1kZWVwIC5tZXNzYWdlLWNvbnRlbnQge1xyXG4gIC5maWxlLWF0dGFjaG1lbnQtaW5mbyB7XHJcbiAgICBiYWNrZ3JvdW5kOiAjZjhmOWZhO1xyXG4gICAgYm9yZGVyOiAxcHggc29saWQgI2U5ZWNlZjtcclxuICAgIGJvcmRlci1yYWRpdXM6IDZweDtcclxuICAgIHBhZGRpbmc6IDhweCAxMnB4O1xyXG4gICAgbWFyZ2luLXRvcDogOHB4O1xyXG4gICAgZm9udC1zaXplOiAxMnB4O1xyXG4gICAgY29sb3I6ICM2Yzc1N2Q7XHJcblxyXG4gICAgJjo6YmVmb3JlIHtcclxuICAgICAgY29udGVudDogXCLDsMKfwpPCjlwiO1xyXG4gICAgICBtYXJnaW4tcmlnaHQ6IDZweDtcclxuICAgIH1cclxuICB9XHJcbn1cclxuXHJcblxyXG5cclxuLmNvcGllZC10b2FzdCB7XHJcbiAgcG9zaXRpb246IGZpeGVkO1xyXG4gIGJvdHRvbTogMjRweDtcclxuICBsZWZ0OiA1MCU7XHJcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVYKC01MCUpO1xyXG4gIGJhY2tncm91bmQtY29sb3I6ICMzMzM7XHJcbiAgY29sb3I6ICNmZmY7XHJcbiAgcGFkZGluZzogOHB4IDE2cHg7XHJcbiAgYm9yZGVyLXJhZGl1czogOHB4O1xyXG4gIGZvbnQtc2l6ZTogMTRweDtcclxuICB6LWluZGV4OiAxMDAwO1xyXG4gIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDAsIDAsIDAsIDAuMjUpO1xyXG4gIGFuaW1hdGlvbjogZmFkZUluT3V0IDJzIGVhc2UtaW4tb3V0O1xyXG59XHJcblxyXG5Aa2V5ZnJhbWVzIGZhZGVJbk91dCB7XHJcbiAgMCUge1xyXG4gICAgb3BhY2l0eTogMDtcclxuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWCgtNTAlKSB0cmFuc2xhdGVZKDEwcHgpO1xyXG4gIH1cclxuICAxMCUsIDkwJSB7XHJcbiAgICBvcGFjaXR5OiAxO1xyXG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVYKC01MCUpIHRyYW5zbGF0ZVkoMCk7XHJcbiAgfVxyXG4gIDEwMCUge1xyXG4gICAgb3BhY2l0eTogMDtcclxuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWCgtNTAlKSB0cmFuc2xhdGVZKC0xMHB4KTtcclxuICB9XHJcbn1cclxuXHJcblxyXG4uaW5wdXQtY29udGFpbmVyIHtcclxuICBwb3NpdGlvbjogcmVsYXRpdmU7XHJcbiAgYmFja2dyb3VuZDogI2ZmZjtcclxuICBib3JkZXI6IDJweCBzb2xpZCAjMDNBQ0MxO1xyXG4gIGJvcmRlci1yYWRpdXM6IDE2cHg7XHJcbiAgcGFkZGluZzogMTJweDtcclxuICBtYXJnaW46IDFyZW07XHJcbiAgYm94LXNpemluZzogYm9yZGVyLWJveDtcclxuICBtaW4taGVpZ2h0OiA4MHB4O1xyXG59XHJcblxyXG4vKiBUZXh0YXJlYSBmaWxscyB0b3AgcGFydCBvbmx5ICovXHJcbi5pbnB1dC1jb250YWluZXIgdGV4dGFyZWEge1xyXG4gIHdpZHRoOiAxMDAlO1xyXG4gIGJvcmRlcjogbm9uZTtcclxuICByZXNpemU6IG5vbmU7XHJcbiAgYmFja2dyb3VuZDogdHJhbnNwYXJlbnQ7XHJcbiAgZm9udC1zaXplOiAxNHB4O1xyXG4gIGZvbnQtZmFtaWx5OiBcIkludGVyXCIsIHNhbnMtc2VyaWY7XHJcbiAgbGluZS1oZWlnaHQ6IDEuNDtcclxuICBvdXRsaW5lOiBub25lO1xyXG4gIHBhZGRpbmc6IDA7XHJcbiAgcGFkZGluZy1yaWdodDogNDhweDsgLyogc3BhY2UgZm9yIHNlbmQgYnV0dG9uICovXHJcbiAgYm94LXNpemluZzogYm9yZGVyLWJveDtcclxuICBtaW4taGVpZ2h0OiBjYWxjKDNlbSAqIDEpOyAgLyogc3RhcnQgc21hbGwgKi9cclxuICBtYXgtaGVpZ2h0OiBjYWxjKDEuNGVtICogMyk7ICAvKiDDosKJwogzIGxpbmVzICovXHJcbiAgb3ZlcmZsb3cteTogYXV0bztcclxufVxyXG5cclxuLmlucHV0LWNvbnRhaW5lciB0ZXh0YXJlYTo6cGxhY2Vob2xkZXIge1xyXG4gIGNvbG9yOiAjOTk5O1xyXG59XHJcblxyXG4vKiBBdHRhY2ggaWNvbiBib3R0b20tbGVmdCAqL1xyXG4uYXR0YWNoLWJ0biB7XHJcbiAgcG9zaXRpb246IGFic29sdXRlO1xyXG4gIGJvdHRvbTogOHB4O1xyXG4gIGxlZnQ6IDEycHg7XHJcbiAgYmFja2dyb3VuZDogbm9uZTtcclxuICBib3JkZXI6IG5vbmU7XHJcbiAgcGFkZGluZzogNHB4O1xyXG4gIGN1cnNvcjogcG9pbnRlcjtcclxufVxyXG5cclxuLmF0dGFjaC1idG4gc3ZnIHtcclxuICB3aWR0aDogMThweDtcclxuICBoZWlnaHQ6IDE4cHg7XHJcbiAgZmlsbDogI2U5MWU2MztcclxufVxyXG5cclxuLyogRWRpdCBhbmQgU2VuZCBib3R0b20tcmlnaHQgKi9cclxuLnJpZ2h0LWljb25zIHtcclxuICBwb3NpdGlvbjogYWJzb2x1dGU7XHJcbiAgYm90dG9tOiA4cHg7XHJcbiAgcmlnaHQ6IDEycHg7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBnYXA6IDhweDtcclxufVxyXG5cclxuLnJpZ2h0LWljb25zIGJ1dHRvbiB7XHJcbiAgYmFja2dyb3VuZDogbm9uZTtcclxuICBib3JkZXI6IG5vbmU7XHJcbiAgY3Vyc29yOiBwb2ludGVyO1xyXG4gIHBhZGRpbmc6IDRweDtcclxufVxyXG5cclxuLnJpZ2h0LWljb25zIHN2ZyB7XHJcbiAgd2lkdGg6IDE4cHg7XHJcbiAgaGVpZ2h0OiAxOHB4O1xyXG4gIGZpbGw6ICNlOTFlNjM7XHJcbn1cclxuXHJcbi8qIEFnZW50IERldGFpbHMgQ29udGFpbmVyICovXHJcbi5hZ2VudC1kZXRhaWxzLWNvbnRhaW5lciB7XHJcbiAgZmxleDogMTtcclxuICBtYXJnaW4tcmlnaHQ6IDE2cHg7XHJcbiAgcGFkZGluZzogMTJweCAxNnB4O1xyXG4gIGJhY2tncm91bmQ6IHZhcigtLWNvbG9yLXN1cmZhY2Utc2Vjb25kYXJ5LCAjZjhmOWZhKTtcclxuICBib3JkZXI6IDFweCBzb2xpZCB2YXIoLS1jb2xvci1ib3JkZXItcHJpbWFyeSwgI2UxZTVlOSk7XHJcbiAgYm9yZGVyLXJhZGl1czogOHB4O1xyXG5cclxuICAuYWdlbnQtaW5mbyB7XHJcbiAgICAuYWdlbnQtbmFtZSB7XHJcbiAgICAgIGZvbnQtc2l6ZTogMTZweDtcclxuICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcclxuICAgICAgY29sb3I6IHZhcigtLWNvbG9yLXRleHQtcHJpbWFyeSwgIzFhMWQyMSk7XHJcbiAgICAgIG1hcmdpbjogMCAwIDRweCAwO1xyXG4gICAgfVxyXG5cclxuICAgIC5hZ2VudC1kZXNjcmlwdGlvbiB7XHJcbiAgICAgIGZvbnQtc2l6ZTogMTRweDtcclxuICAgICAgY29sb3I6IHZhcigtLWNvbG9yLXRleHQtc2Vjb25kYXJ5LCAjNmM3NTdkKTtcclxuICAgICAgbWFyZ2luOiAwIDAgOHB4IDA7XHJcbiAgICAgIGxpbmUtaGVpZ2h0OiAxLjQ7XHJcbiAgICB9XHJcblxyXG4gICAgLmFnZW50LW1ldGEge1xyXG4gICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gICAgICBnYXA6IDRweDtcclxuXHJcbiAgICAgIC5hZ2VudC1yb2xlLFxyXG4gICAgICAuYWdlbnQtZ29hbCB7XHJcbiAgICAgICAgZm9udC1zaXplOiAxMnB4O1xyXG4gICAgICAgIGNvbG9yOiB2YXIoLS1jb2xvci10ZXh0LXRlcnRpYXJ5LCAjOGU5Mjk3KTtcclxuICAgICAgICBmb250LXdlaWdodDogNTAwO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcblxyXG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n  return PlaygroundComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "EventEmitter", "FormsModule", "FormControl", "ReactiveFormsModule", "ButtonComponent", "DropdownComponent", "IconComponent", "ToggleComponent", "AvaTextboxComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r1", "playgroundTitle", "ɵɵlistener", "PlaygroundComponent_div_3_ava_dropdown_1_Template_ava_dropdown_selectionChange_0_listener", "$event", "ɵɵrestoreView", "_r3", "ɵɵnextContext", "ɵɵresetView", "onPromptChange", "ɵɵproperty", "promptOptions", "selected<PERSON><PERSON><PERSON>", "ɵɵtemplate", "PlaygroundComponent_div_3_ava_dropdown_1_Template", "isMinimalView", "ɵɵelement", "agentNamePlaceholder", "agentNameDisplayControl", "PlaygroundComponent_ava_button_6_Template_ava_button_userClick_0_listener", "_r4", "onApprovalClick", "ɵɵpureFunction0", "_c2", "PlaygroundComponent_div_12_Template_button_click_1_listener", "_r5", "save", "PlaygroundComponent_div_12_Template_button_click_5_listener", "export", "PlaygroundComponent_div_15_button_3_Template_button_click_0_listener", "_r6", "msg_r7", "$implicit", "copyToClipboard", "text", "PlaygroundComponent_div_15_button_3_Template", "PlaygroundComponent_div_15_div_4_Template", "from", "ɵɵpureFunction2", "_c3", "ɵɵtextInterpolate1", "showCopiedToast", "PlaygroundComponent_button_20_Template_button_click_0_listener", "_r8", "fileInput_r9", "ɵɵreference", "click", "PlaygroundComponent_div_23_div_1_Template_button_click_3_listener", "i_r11", "_r10", "index", "removeFile", "file_r12", "documentName", "PlaygroundComponent_div_23_div_1_Template", "filesUploadedData", "PlaygroundComponent_div_27_div_1_Template_ava_toggle_checkedChange_1_listener", "_r13", "onConversationalToggle", "isConvChecked", "PlaygroundComponent_div_27_div_2_Template_ava_toggle_checkedChange_1_listener", "_r14", "onTemplateToggle", "isUseTemplate", "PlaygroundComponent_div_27_div_1_Template", "PlaygroundComponent_div_27_div_2_Template", "showChatInteractionToggles", "PlaygroundComponent", "isMenuOpen", "isToolMenuOpen", "prompt<PERSON><PERSON>e", "selected<PERSON><PERSON><PERSON>", "agentType", "showAiPrincipleToggle", "showDropdown", "showAgentNameInput", "displayedAgentName", "showFileUploadButton", "value", "disabled", "inputText", "previousMessages<PERSON><PERSON><PERSON>", "shouldScrollToBottom", "messages", "isLoading", "isDisabled", "<PERSON><PERSON><PERSON><PERSON>", "messageSent", "conversationalToggle", "templateToggle", "filesSelected", "messagesContainer", "fileInput", "showApprovalButton", "approvalRequested", "acceptedFileType", "ngOnInit", "setValue", "ngOnChanges", "changes", "currentValue", "undefined", "ngAfterViewChecked", "scrollToBottom", "nativeElement", "scrollTop", "scrollHeight", "err", "console", "error", "handleSendMessage", "trim", "messageText", "emit", "clearFiles", "toggleMenu", "onAiPrincipleToggle", "event", "log", "selectionData", "<PERSON><PERSON><PERSON>", "selectedOptions", "length", "name", "selectedOption", "find", "opt", "navigator", "clipboard", "writeText", "then", "setTimeout", "onClickOutside", "target", "closest", "onEnterKeydown", "shift<PERSON>ey", "preventDefault", "onFileSelected", "files", "i", "file", "push", "id", "Date", "now", "isImage", "type", "startsWith", "splice", "trackByIndex", "_item", "selectors", "viewQuery", "PlaygroundComponent_Query", "rf", "ctx", "PlaygroundComponent_click_HostBindingHandler", "ɵɵresolveDocument", "PlaygroundComponent_keydown_enter_HostBindingHandler", "PlaygroundComponent_div_2_Template", "PlaygroundComponent_div_3_Template", "PlaygroundComponent_div_4_Template", "PlaygroundComponent_ava_button_6_Template", "PlaygroundComponent_Template_div_click_7_listener", "_r1", "PlaygroundComponent_div_12_Template", "PlaygroundComponent_div_15_Template", "PlaygroundComponent_div_16_Template", "PlaygroundComponent_div_17_Template", "ɵɵtwoWayListener", "PlaygroundComponent_Template_textarea_ngModelChange_19_listener", "ɵɵtwoWayBindingSet", "PlaygroundComponent_Template_textarea_keydown_enter_19_listener", "PlaygroundComponent_button_20_Template", "PlaygroundComponent_Template_input_change_21_listener", "PlaygroundComponent_div_23_Template", "PlaygroundComponent_Template_button_click_25_listener", "PlaygroundComponent_div_27_Template", "ɵɵtwoWayProperty", "i1", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i2", "DefaultValueAccessor", "NgControlStatus", "NgModel", "FormControlDirective", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\components\\playground\\playground.component.ts", "C:\\console\\aava-ui-web\\projects\\shared\\components\\playground\\playground.component.html"], "sourcesContent": ["import { CommonModule } from '@angular/common';\r\nimport {\r\n  AfterViewChecked,\r\n  Component,\r\n  ElementRef,\r\n  EventEmitter,\r\n  HostListener,\r\n  Input,\r\n  OnChanges,\r\n  OnInit,\r\n  Output,\r\n  ViewChild\r\n} from '@angular/core';\r\nimport { ChatMessage } from '../chat-window';\r\nimport { FormsModule, FormControl, ReactiveFormsModule } from '@angular/forms';\r\nimport {\r\n  ButtonComponent,\r\n  DropdownComponent,\r\n  IconComponent,\r\n  ToggleComponent,\r\n  DropdownOption,\r\n  AvaTextboxComponent,\r\n} from '@ava/play-comp-library';\r\n\r\ninterface Tool {\r\n  id: number;\r\n  name: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-playground',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    DropdownComponent,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    ButtonComponent,\r\n    ToggleComponent,\r\n    IconComponent,\r\n    DropdownComponent,\r\n    AvaTextboxComponent,\r\n  ],\r\n  templateUrl: './playground.component.html',\r\n  styleUrl: './playground.component.scss',\r\n})\r\nexport class PlaygroundComponent implements OnInit, OnChanges, AfterViewChecked {\r\n  isMenuOpen = false;\r\n  isToolMenuOpen = false;\r\n  @Output() promptChange = new EventEmitter<DropdownOption>();\r\n  @Input() promptOptions: DropdownOption[] = [];\r\n  @Input() selectedValue: string = 'default'; // Input for pre-selected value\r\n  @Input() agentType: string = 'individual'; // Input for agent type ('individual' or 'collaborative')\r\n  @Input() showChatInteractionToggles: boolean = false; // Input to show conversational and template toggles\r\n  @Input() showAiPrincipleToggle: boolean = false; // Input to show AI principle toggle\r\n  @Input() showDropdown: boolean = true; // Input to control dropdown visibility\r\n  @Input() showAgentNameInput: boolean = false; // Input to show disabled agent name input field\r\n  @Input() agentNamePlaceholder: string = 'Agent Name'; // Placeholder for agent name input\r\n  @Input() displayedAgentName: string = ''; // Agent name to display in disabled input\r\n  @Input() showFileUploadButton: boolean = false; // Controls visibility of attach file button\r\n  selectedPrompt: string = 'default';\r\n  @Input() playgroundTitle: string = '';\r\n  // Form control for agent name display\r\n  agentNameDisplayControl = new FormControl({ value: '', disabled: true });\r\n\r\n  // Chat data\r\n  showCopiedToast = false;\r\n  inputText: string = '';\r\n  previousMessagesLength = 0;\r\n  shouldScrollToBottom = false;\r\n  @Input() messages: ChatMessage[] = [];\r\n  @Input() isLoading: boolean = false;\r\n  @Input() isDisabled: boolean = false;\r\n  @Input() showLoader: boolean = true;\r\n  @Output() messageSent = new EventEmitter<string>();\r\n  @Output() conversationalToggle = new EventEmitter<boolean>();\r\n  @Output() templateToggle = new EventEmitter<boolean>();\r\n  @Output() filesSelected = new EventEmitter<any[]>();\r\n  @ViewChild('messagesContainer') messagesContainer!: ElementRef;\r\n  @ViewChild('fileInput') fileInput!: ElementRef;\r\n  @Input() showApprovalButton: boolean = true;\r\n  @Output() approvalRequested = new EventEmitter<void>();\r\n  @Input() isMinimalView: boolean = false;\r\n\r\n  // Simple toggle properties for display only\r\n  public isConvChecked: boolean = true;\r\n  public isUseTemplate: boolean = false;\r\n\r\n  // File upload properties\r\n  public filesUploadedData: any[] = [];\r\n  @Input() acceptedFileType: string = '.pdf,.doc,.docx,.txt,.csv,.xlsx,.xls,.ppt,.pptx,.png,.jpg,.jpeg,.gif,.bmp,.svg';\r\n\r\n  ngOnInit(): void {\r\n    this.messages = [\r\n      {\r\n        from: 'ai',\r\n        text: 'Hi there, how can I help you today?',\r\n      },\r\n    ];\r\n    this.shouldScrollToBottom = true;\r\n\r\n    // Set selected prompt from input\r\n    if (this.selectedValue) {\r\n      this.selectedPrompt = this.selectedValue;\r\n      // Update displayed agent name if showing agent name input\r\n      if (this.showAgentNameInput && !this.displayedAgentName) {\r\n        this.displayedAgentName = this.selectedValue;\r\n        this.agentNameDisplayControl.setValue(this.selectedValue);\r\n      }\r\n    }\r\n\r\n    // Initialize agent name display control\r\n    if (this.displayedAgentName) {\r\n      this.agentNameDisplayControl.setValue(this.displayedAgentName);\r\n    }\r\n  }\r\n\r\n  ngOnChanges(changes: any): void {\r\n    // Update selectedPrompt when selectedValue input changes\r\n    if (changes['selectedValue'] && changes['selectedValue'].currentValue) {\r\n      // The selectedValue from parent should be the name (for dropdown display)\r\n      this.selectedPrompt = changes['selectedValue'].currentValue;\r\n    }\r\n\r\n    // Update agent name display control when displayedAgentName input changes\r\n    if (changes['displayedAgentName'] && changes['displayedAgentName'].currentValue !== undefined) {\r\n      this.agentNameDisplayControl.setValue(changes['displayedAgentName'].currentValue);\r\n    }\r\n  }\r\n\r\n  ngAfterViewChecked() {\r\n    if (this.shouldScrollToBottom) {\r\n      this.scrollToBottom();\r\n      this.shouldScrollToBottom = false;\r\n    }\r\n  }\r\n\r\n  scrollToBottom(): void {\r\n    try {\r\n      if (this.messagesContainer && this.messagesContainer.nativeElement) {\r\n        // Scroll to bottom to show latest messages\r\n        this.messagesContainer.nativeElement.scrollTop =\r\n          this.messagesContainer.nativeElement.scrollHeight;\r\n      }\r\n    } catch (err) {\r\n      console.error('Error scrolling to bottom:', err);\r\n    }\r\n  }\r\n\r\n  handleSendMessage(): void {\r\n    if (!this.inputText.trim() || this.isDisabled) {\r\n      return;\r\n    }\r\n\r\n    // Add user message to the chat\r\n    this.messages = [\r\n      ...this.messages,\r\n      {\r\n        from: 'user',\r\n        text: this.inputText,\r\n      },\r\n    ];\r\n    this.shouldScrollToBottom = true;\r\n\r\n    // Emit the message to parent component\r\n    const messageText = this.inputText;\r\n    this.inputText = '';\r\n    this.messageSent.emit(messageText);\r\n\r\n    // Clear uploaded files after sending message\r\n    this.clearFiles();\r\n  }\r\n\r\n  private clearFiles(): void {\r\n    this.filesUploadedData = [];\r\n    this.filesSelected.emit(this.filesUploadedData);\r\n  }\r\n\r\n  toggleMenu() {\r\n    this.isMenuOpen = !this.isMenuOpen;\r\n  }\r\n\r\n  onAiPrincipleToggle(event: any) {\r\n    console.log('AI Principles toggle:', event);\r\n  }\r\n\r\n  onConversationalToggle(event: any) {\r\n    this.isConvChecked = event;\r\n\r\n    // If conversational is enabled, disable template\r\n    if (event && this.isUseTemplate) {\r\n      this.isUseTemplate = false;\r\n      this.templateToggle.emit(false);\r\n    }\r\n\r\n    console.log('Conversational mode:', event);\r\n    this.conversationalToggle.emit(event);\r\n  }\r\n\r\n  onTemplateToggle(event: any) {\r\n    this.isUseTemplate = event;\r\n\r\n    // If template is enabled, disable conversational\r\n    if (event && this.isConvChecked) {\r\n      this.isConvChecked = false;\r\n      this.conversationalToggle.emit(false);\r\n    }\r\n\r\n    console.log('Use template:', event);\r\n    this.templateToggle.emit(event);\r\n  }\r\n  \r\n  onPromptChange(selectionData: any): void {\r\n    // The dropdown component emits an object with selectedOptions and selectedValue\r\n    // selectedValue contains the name of the selected option\r\n    let selectedName: string;\r\n\r\n    if (typeof selectionData === 'string') {\r\n      selectedName = selectionData;\r\n    } else if (selectionData && selectionData.selectedValue) {\r\n      selectedName = selectionData.selectedValue;\r\n    } else if (selectionData && selectionData.selectedOptions && selectionData.selectedOptions.length > 0) {\r\n      selectedName = selectionData.selectedOptions[0].name;\r\n    } else {\r\n      return;\r\n    }\r\n\r\n    this.selectedPrompt = selectedName;\r\n\r\n    // Update displayed agent name if showing agent name input\r\n    if (this.showAgentNameInput) {\r\n      this.displayedAgentName = selectedName;\r\n      this.agentNameDisplayControl.setValue(selectedName);\r\n    }\r\n\r\n    // Find the option by name\r\n    const selectedOption = this.promptOptions.find(\r\n      (opt) => opt.name === selectedName,\r\n    );\r\n\r\n    if (selectedOption) {\r\n      this.promptChange.emit(selectedOption);\r\n    }\r\n  }\r\n\r\n  copyToClipboard(text: string): void {\r\n  navigator.clipboard.writeText(text).then(() => {\r\n    this.showCopiedToast = true;\r\n    setTimeout(() => {\r\n      this.showCopiedToast = false;\r\n    }, 2000);\r\n  });\r\n}\r\n  save() {\r\n    this.isMenuOpen = false;\r\n    console.log('Save clicked');\r\n    // your save logic here\r\n  }\r\n\r\n  export() {\r\n    this.isMenuOpen = false;\r\n    console.log('Export clicked');\r\n    // your export logic here\r\n  }\r\n\r\n  // Hide menu when clicking outside\r\n  @HostListener('document:click', ['$event'])\r\n  onClickOutside(event: Event) {\r\n    const target = event.target as HTMLElement;\r\n    if (!target.closest('.btn-menu')) {\r\n      this.isMenuOpen = false;\r\n    }\r\n  }\r\n\r\n  @HostListener('keydown.enter', ['$event'])\r\n  onEnterKeydown(event: KeyboardEvent): void {\r\n    // Only prevent default and send if Shift key is not pressed\r\n    if (!event.shiftKey) {\r\n      event.preventDefault();\r\n      this.handleSendMessage();\r\n    }\r\n  }\r\n\r\n  // File upload methods\r\n  onFileSelected(event: any): void {\r\n    const files = event.target.files;\r\n    if (files && files.length > 0) {\r\n      this.filesUploadedData = [];\r\n\r\n      for (let i = 0; i < files.length; i++) {\r\n        const file = files[i];\r\n        this.filesUploadedData.push({\r\n          id: `file_${Date.now()}_${i}`,\r\n          documentName: file.name,\r\n          isImage: file.type.startsWith('image/'),\r\n          file: file\r\n        });\r\n      }\r\n\r\n      console.log('Files selected:', this.filesUploadedData);\r\n      this.filesSelected.emit(this.filesUploadedData);\r\n    }\r\n  }\r\n\r\n  removeFile(index: number): void {\r\n    this.filesUploadedData.splice(index, 1);\r\n    this.filesSelected.emit(this.filesUploadedData);\r\n  }\r\n\r\n  // Track by function for ngFor performance\r\n  trackByIndex(index: number, _item: any): number {\r\n    return index;\r\n  }\r\n\r\n  onApprovalClick() {\r\n    this.approvalRequested.emit();\r\n  }\r\n}\r\n", "<div class=\"playground-container\">\r\n  <div class=\"button-container\">\r\n    <div class=\"editors-title\" *ngIf=\"playgroundTitle\">{{playgroundTitle}}</div>\r\n    <!-- Agent Selection Dropdown -->\r\n    <div class=\"dropdown-container\" *ngIf=\"showDropdown\">\r\n      <ava-dropdown\r\n        *ngIf=\"!isMinimalView\"\r\n        dropdownTitle=\"Choose Agent\"\r\n        [options]=\"promptOptions\"\r\n        [selectedValue]=\"selectedPrompt\"\r\n        [enableSearch]=\"true\"\r\n        [singleSelect]=\"true\"\r\n        (selectionChange)=\"onPromptChange($event)\">\r\n      </ava-dropdown>\r\n    </div>\r\n\r\n    <!-- Disabled Agent Name Input Field -->\r\n    <div class=\"agent-name-display\" *ngIf=\"showAgentNameInput\">\r\n      <ava-textbox\r\n        [placeholder]=\"agentNamePlaceholder\"\r\n        [formControl]=\"agentNameDisplayControl\"\r\n        variant=\"default\"\r\n        size=\"md\"\r\n        class=\"disabled-agent-name-input\">\r\n      </ava-textbox>\r\n    </div>\r\n\r\n    <div class=\"btn-menu\">\r\n      <!-- <button class=\"action-btn\">Send for Approval</button> -->\r\n      <ava-button *ngIf=\"showApprovalButton\" label=\"Send for Approval\" size=\"small\" state=\"active\"\r\n        variant=\"primary\" [customStyles]=\"{\r\n          background:\r\n            'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',\r\n          '--button-effect-color': '33, 90, 214',\r\n        }\" (userClick)=\"onApprovalClick()\"></ava-button>\r\n      <div class=\"menu-icon\" (click)=\"toggleMenu()\" #menuIcon>\r\n        <span></span>\r\n        <span></span>\r\n        <span></span>\r\n      </div>\r\n\r\n      <div class=\"dot-dropdown-menu\" *ngIf=\"isMenuOpen\">\r\n        <button (click)=\"save()\">\r\n          <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path d=\"M17 3H5a2 2 0 0 0-2 2v14l4-4h10a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2z\" />\r\n          </svg>\r\n          Save\r\n        </button>\r\n\r\n        <button (click)=\"export()\">\r\n          <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path d=\"M5 20h14v-2H5m14-9h-4V3H9v6H5l7 7 7-7z\" />\r\n          </svg>\r\n          Export\r\n        </button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n  <div class=\"layout\" #messagesContainer>\r\n    <!-- Messages in normal order -->\r\n    <div *ngFor=\"let msg of messages; trackBy: trackByIndex\" class=\"message-row\" [ngClass]=\"msg.from\">\r\n      <div [ngClass]=\"{\r\n          'bot-response': msg.from === 'ai',\r\n          'user-message': msg.from === 'user',\r\n        }\">\r\n        {{ msg.text }}\r\n        <button *ngIf=\"msg.from === 'ai'\" class=\"copy-btn\" (click)=\"copyToClipboard(msg.text)\" title=\"Copy\">\r\n          <ava-icon slot=\"icon-start\" iconName=\"copy\" [iconSize]=\"16\" iconColor=\"var(--color-brand-primary)\">\r\n          </ava-icon>\r\n        </button>\r\n      </div>\r\n      <div *ngIf=\"showCopiedToast\" class=\"copied-toast\">Copied!</div>\r\n    </div>\r\n\r\n    <!-- Animated loading indicator when API call is in progress -->\r\n    <div *ngIf=\"isLoading && showLoader\" class=\"message-row ai\">\r\n      <div class=\"bot-response loading-message\">\r\n        <div class=\"typing-indicator\">\r\n          <span class=\"dot\"></span>\r\n          <span class=\"dot\"></span>\r\n          <span class=\"dot\"></span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Simple text loading indicator for tool testing -->\r\n    <div *ngIf=\"isLoading && !showLoader\" class=\"message-row ai\">\r\n      <div class=\"bot-response\">...</div>\r\n    </div>\r\n  </div>\r\n\r\n \r\n  <div class=\"input-container\">\r\n  <textarea\r\n    [(ngModel)]=\"inputText\"\r\n    [disabled]=\"isDisabled || isLoading\"\r\n    (keydown.enter)=\"handleSendMessage(); $event.preventDefault()\"\r\n    placeholder=\"Enter something to test\"\r\n  ></textarea>\r\n\r\n  <button *ngIf=\"showFileUploadButton\" class=\"attach-btn\" title=\"Attach File\" (click)=\"fileInput.click()\">\r\n    <ava-icon slot=\"icon-start\" iconName=\"paperclip\" [iconSize]=\"16\" iconColor=\"var(--color-brand-primary)\">\r\n        </ava-icon>\r\n  </button> \r\n\r\n    <!-- Hidden file input -->\r\n    <input\r\n      #fileInput\r\n      type=\"file\"\r\n      [accept]=\"acceptedFileType\"\r\n      multiple\r\n      style=\"display: none;\"\r\n      (change)=\"onFileSelected($event)\">\r\n\r\n    <!-- Display uploaded files -->\r\n    <div class=\"uploaded-files\" *ngIf=\"filesUploadedData.length > 0\">\r\n      <div class=\"file-item\" *ngFor=\"let file of filesUploadedData; let i = index\">\r\n        <span class=\"file-name\">{{ file.documentName }}</span>\r\n        <button class=\"remove-file\" (click)=\"removeFile(i)\" title=\"Remove file\"></button>\r\n      </div>\r\n    </div>\r\n\r\n  <div class=\"right-icons\">\r\n    <!-- <button class=\"edit-btn\" title=\"Edit\">\r\n     <ava-icon slot=\"icon-start\" iconName=\"wand-sparkles\" [iconSize]=\"16\" iconColor=\"var(--color-brand-primary)\">\r\n        </ava-icon>\r\n    </button> -->\r\n    <button class=\"send-btn\" title=\"Send\" (click)=\"handleSendMessage()\" [disabled]=\"isLoading || isDisabled\">\r\n      <ava-icon slot=\"icon-start\" iconName=\"send-horizontal\" [iconSize]=\"16\" iconColor=\"var(--color-brand-primary)\">\r\n        </ava-icon>\r\n    </button>\r\n  </div>\r\n</div>\r\n\r\n\r\n\r\n  <!-- Toggles Container - All toggles in same line when present -->\r\n  <div class=\"toggle-container\" *ngIf=\"showChatInteractionToggles || showAiPrincipleToggle\">\r\n    <!-- Conversational Toggle -->\r\n    <div class=\"toggle-row\" *ngIf=\"showChatInteractionToggles\">\r\n      <ava-toggle\r\n        [checked]=\"isConvChecked\"\r\n        [title]=\"'Conversational'\"\r\n        [position]=\"'left'\"\r\n        size=\"small\"\r\n        (checkedChange)=\"onConversationalToggle($event)\">\r\n      </ava-toggle>\r\n    </div>\r\n\r\n    <!-- Use Template Toggle -->\r\n    <div class=\"toggle-row\" *ngIf=\"showChatInteractionToggles\">\r\n      <ava-toggle\r\n        [checked]=\"isUseTemplate\"\r\n        [title]=\"'Use Template'\"\r\n        [position]=\"'left'\"\r\n        size=\"small\"\r\n        (checkedChange)=\"onTemplateToggle($event)\">\r\n      </ava-toggle>\r\n    </div>\r\n\r\n    <!-- AI Principles Toggle -->\r\n    <!-- <div class=\"toggle-row\" *ngIf=\"showAiPrincipleToggle\">\r\n      <ava-toggle\r\n        [checked]=\"true\"\r\n        [title]=\"'AI Principles'\"\r\n        [position]=\"'left'\"\r\n        size=\"small\"\r\n        (checkedChange)=\"onAiPrincipleToggle($event)\">\r\n      </ava-toggle>\r\n    </div> -->\r\n  </div>\r\n</div>"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAIEC,YAAY,QAOP,eAAe;AAEtB,SAASC,WAAW,EAAEC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AAC9E,SACEC,eAAe,EACfC,iBAAiB,EACjBC,aAAa,EACbC,eAAe,EAEfC,mBAAmB,QACd,wBAAwB;;;;;;;;;;;;;;;;ICpB3BC,EAAA,CAAAC,cAAA,cAAmD;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAAzBH,EAAA,CAAAI,SAAA,EAAmB;IAAnBJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,eAAA,CAAmB;;;;;;IAGpEP,EAAA,CAAAC,cAAA,uBAO6C;IAA3CD,EAAA,CAAAQ,UAAA,6BAAAC,0FAAAC,MAAA;MAAAV,EAAA,CAAAW,aAAA,CAAAC,GAAA;MAAA,MAAAN,MAAA,GAAAN,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAAmBR,MAAA,CAAAS,cAAA,CAAAL,MAAA,CAAsB;IAAA,EAAC;IAC5CV,EAAA,CAAAG,YAAA,EAAe;;;;IAFbH,EAHA,CAAAgB,UAAA,YAAAV,MAAA,CAAAW,aAAA,CAAyB,kBAAAX,MAAA,CAAAY,cAAA,CACO,sBACX,sBACA;;;;;IAPzBlB,EAAA,CAAAC,cAAA,cAAqD;IACnDD,EAAA,CAAAmB,UAAA,IAAAC,iDAAA,2BAO6C;IAE/CpB,EAAA,CAAAG,YAAA,EAAM;;;;IARDH,EAAA,CAAAI,SAAA,EAAoB;IAApBJ,EAAA,CAAAgB,UAAA,UAAAV,MAAA,CAAAe,aAAA,CAAoB;;;;;IAWzBrB,EAAA,CAAAC,cAAA,cAA2D;IACzDD,EAAA,CAAAsB,SAAA,sBAMc;IAChBtB,EAAA,CAAAG,YAAA,EAAM;;;;IANFH,EAAA,CAAAI,SAAA,EAAoC;IACpCJ,EADA,CAAAgB,UAAA,gBAAAV,MAAA,CAAAiB,oBAAA,CAAoC,gBAAAjB,MAAA,CAAAkB,uBAAA,CACG;;;;;;IASzCxB,EAAA,CAAAC,cAAA,qBAKqC;IAAhCD,EAAA,CAAAQ,UAAA,uBAAAiB,0EAAA;MAAAzB,EAAA,CAAAW,aAAA,CAAAe,GAAA;MAAA,MAAApB,MAAA,GAAAN,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAAaR,MAAA,CAAAqB,eAAA,EAAiB;IAAA,EAAC;IAAC3B,EAAA,CAAAG,YAAA,EAAa;;;IAJ9BH,EAAA,CAAAgB,UAAA,iBAAAhB,EAAA,CAAA4B,eAAA,IAAAC,GAAA,EAIhB;;;;;;IAQF7B,EADF,CAAAC,cAAA,cAAkD,iBACvB;IAAjBD,EAAA,CAAAQ,UAAA,mBAAAsB,4DAAA;MAAA9B,EAAA,CAAAW,aAAA,CAAAoB,GAAA;MAAA,MAAAzB,MAAA,GAAAN,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASR,MAAA,CAAA0B,IAAA,EAAM;IAAA,EAAC;;IACtBhC,EAAA,CAAAC,cAAA,cAAuG;IACrGD,EAAA,CAAAsB,SAAA,eAA2E;IAC7EtB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAE,MAAA,aACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;IAETH,EAAA,CAAAC,cAAA,iBAA2B;IAAnBD,EAAA,CAAAQ,UAAA,mBAAAyB,4DAAA;MAAAjC,EAAA,CAAAW,aAAA,CAAAoB,GAAA;MAAA,MAAAzB,MAAA,GAAAN,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASR,MAAA,CAAA4B,MAAA,EAAQ;IAAA,EAAC;;IACxBlC,EAAA,CAAAC,cAAA,cAAuG;IACrGD,EAAA,CAAAsB,SAAA,eAAmD;IACrDtB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAE,MAAA,eACF;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;;;;;;IAWJH,EAAA,CAAAC,cAAA,iBAAoG;IAAjDD,EAAA,CAAAQ,UAAA,mBAAA2B,qEAAA;MAAAnC,EAAA,CAAAW,aAAA,CAAAyB,GAAA;MAAA,MAAAC,MAAA,GAAArC,EAAA,CAAAa,aAAA,GAAAyB,SAAA;MAAA,MAAAhC,MAAA,GAAAN,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASR,MAAA,CAAAiC,eAAA,CAAAF,MAAA,CAAAG,IAAA,CAAyB;IAAA,EAAC;IACpFxC,EAAA,CAAAsB,SAAA,mBACW;IACbtB,EAAA,CAAAG,YAAA,EAAS;;;IAFqCH,EAAA,CAAAI,SAAA,EAAe;IAAfJ,EAAA,CAAAgB,UAAA,gBAAe;;;;;IAI/DhB,EAAA,CAAAC,cAAA,cAAkD;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAV/DH,EADF,CAAAC,cAAA,cAAkG,cAI3F;IACHD,EAAA,CAAAE,MAAA,GACA;IAAAF,EAAA,CAAAmB,UAAA,IAAAsB,4CAAA,qBAAoG;IAItGzC,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAmB,UAAA,IAAAuB,yCAAA,kBAAkD;IACpD1C,EAAA,CAAAG,YAAA,EAAM;;;;;IAZuEH,EAAA,CAAAgB,UAAA,YAAAqB,MAAA,CAAAM,IAAA,CAAoB;IAC1F3C,EAAA,CAAAI,SAAA,EAGD;IAHCJ,EAAA,CAAAgB,UAAA,YAAAhB,EAAA,CAAA4C,eAAA,IAAAC,GAAA,EAAAR,MAAA,CAAAM,IAAA,WAAAN,MAAA,CAAAM,IAAA,aAGD;IACF3C,EAAA,CAAAI,SAAA,EACA;IADAJ,EAAA,CAAA8C,kBAAA,MAAAT,MAAA,CAAAG,IAAA,MACA;IAASxC,EAAA,CAAAI,SAAA,EAAuB;IAAvBJ,EAAA,CAAAgB,UAAA,SAAAqB,MAAA,CAAAM,IAAA,UAAuB;IAK5B3C,EAAA,CAAAI,SAAA,EAAqB;IAArBJ,EAAA,CAAAgB,UAAA,SAAAV,MAAA,CAAAyC,eAAA,CAAqB;;;;;IAMzB/C,EAFJ,CAAAC,cAAA,cAA4D,cAChB,cACV;IAG5BD,EAFA,CAAAsB,SAAA,eAAyB,eACA,eACA;IAG/BtB,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;;IAIJH,EADF,CAAAC,cAAA,cAA6D,cACjC;IAAAD,EAAA,CAAAE,MAAA,UAAG;IAC/BF,EAD+B,CAAAG,YAAA,EAAM,EAC/B;;;;;;IAYRH,EAAA,CAAAC,cAAA,iBAAwG;IAA5BD,EAAA,CAAAQ,UAAA,mBAAAwC,+DAAA;MAAAhD,EAAA,CAAAW,aAAA,CAAAsC,GAAA;MAAAjD,EAAA,CAAAa,aAAA;MAAA,MAAAqC,YAAA,GAAAlD,EAAA,CAAAmD,WAAA;MAAA,OAAAnD,EAAA,CAAAc,WAAA,CAASoC,YAAA,CAAAE,KAAA,EAAiB;IAAA,EAAC;IACrGpD,EAAA,CAAAsB,SAAA,mBACe;IACjBtB,EAAA,CAAAG,YAAA,EAAS;;;IAF0CH,EAAA,CAAAI,SAAA,EAAe;IAAfJ,EAAA,CAAAgB,UAAA,gBAAe;;;;;;IAgB5DhB,EADF,CAAAC,cAAA,cAA6E,eACnD;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACtDH,EAAA,CAAAC,cAAA,iBAAwE;IAA5CD,EAAA,CAAAQ,UAAA,mBAAA6C,kEAAA;MAAA,MAAAC,KAAA,GAAAtD,EAAA,CAAAW,aAAA,CAAA4C,IAAA,EAAAC,KAAA;MAAA,MAAAlD,MAAA,GAAAN,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASR,MAAA,CAAAmD,UAAA,CAAAH,KAAA,CAAa;IAAA,EAAC;IACrDtD,EAD0E,CAAAG,YAAA,EAAS,EAC7E;;;;IAFoBH,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAK,iBAAA,CAAAqD,QAAA,CAAAC,YAAA,CAAuB;;;;;IAFnD3D,EAAA,CAAAC,cAAA,cAAiE;IAC/DD,EAAA,CAAAmB,UAAA,IAAAyC,yCAAA,kBAA6E;IAI/E5D,EAAA,CAAAG,YAAA,EAAM;;;;IAJoCH,EAAA,CAAAI,SAAA,EAAsB;IAAtBJ,EAAA,CAAAgB,UAAA,YAAAV,MAAA,CAAAuD,iBAAA,CAAsB;;;;;;IAwB9D7D,EADF,CAAAC,cAAA,cAA2D,qBAMN;IAAjDD,EAAA,CAAAQ,UAAA,2BAAAsD,8EAAApD,MAAA;MAAAV,EAAA,CAAAW,aAAA,CAAAoD,IAAA;MAAA,MAAAzD,MAAA,GAAAN,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAAiBR,MAAA,CAAA0D,sBAAA,CAAAtD,MAAA,CAA8B;IAAA,EAAC;IAEpDV,EADE,CAAAG,YAAA,EAAa,EACT;;;;IANFH,EAAA,CAAAI,SAAA,EAAyB;IAEzBJ,EAFA,CAAAgB,UAAA,YAAAV,MAAA,CAAA2D,aAAA,CAAyB,2BACC,oBACP;;;;;;IAQrBjE,EADF,CAAAC,cAAA,cAA2D,qBAMZ;IAA3CD,EAAA,CAAAQ,UAAA,2BAAA0D,8EAAAxD,MAAA;MAAAV,EAAA,CAAAW,aAAA,CAAAwD,IAAA;MAAA,MAAA7D,MAAA,GAAAN,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAAiBR,MAAA,CAAA8D,gBAAA,CAAA1D,MAAA,CAAwB;IAAA,EAAC;IAE9CV,EADE,CAAAG,YAAA,EAAa,EACT;;;;IANFH,EAAA,CAAAI,SAAA,EAAyB;IAEzBJ,EAFA,CAAAgB,UAAA,YAAAV,MAAA,CAAA+D,aAAA,CAAyB,yBACD,oBACL;;;;;IAjBzBrE,EAAA,CAAAC,cAAA,cAA0F;IAaxFD,EAXA,CAAAmB,UAAA,IAAAmD,yCAAA,kBAA2D,IAAAC,yCAAA,kBAWA;IAoB7DvE,EAAA,CAAAG,YAAA,EAAM;;;;IA/BqBH,EAAA,CAAAI,SAAA,EAAgC;IAAhCJ,EAAA,CAAAgB,UAAA,SAAAV,MAAA,CAAAkE,0BAAA,CAAgC;IAWhCxE,EAAA,CAAAI,SAAA,EAAgC;IAAhCJ,EAAA,CAAAgB,UAAA,SAAAV,MAAA,CAAAkE,0BAAA,CAAgC;;;ADxG7D,WAAaC,mBAAmB;EAA1B,MAAOA,mBAAmB;IAC9BC,UAAU,GAAG,KAAK;IAClBC,cAAc,GAAG,KAAK;IACZC,YAAY,GAAG,IAAIrF,YAAY,EAAkB;IAClD0B,aAAa,GAAqB,EAAE;IACpC4D,aAAa,GAAW,SAAS,CAAC,CAAC;IACnCC,SAAS,GAAW,YAAY,CAAC,CAAC;IAClCN,0BAA0B,GAAY,KAAK,CAAC,CAAC;IAC7CO,qBAAqB,GAAY,KAAK,CAAC,CAAC;IACxCC,YAAY,GAAY,IAAI,CAAC,CAAC;IAC9BC,kBAAkB,GAAY,KAAK,CAAC,CAAC;IACrC1D,oBAAoB,GAAW,YAAY,CAAC,CAAC;IAC7C2D,kBAAkB,GAAW,EAAE,CAAC,CAAC;IACjCC,oBAAoB,GAAY,KAAK,CAAC,CAAC;IAChDjE,cAAc,GAAW,SAAS;IACzBX,eAAe,GAAW,EAAE;IACrC;IACAiB,uBAAuB,GAAG,IAAI/B,WAAW,CAAC;MAAE2F,KAAK,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAI,CAAE,CAAC;IAExE;IACAtC,eAAe,GAAG,KAAK;IACvBuC,SAAS,GAAW,EAAE;IACtBC,sBAAsB,GAAG,CAAC;IAC1BC,oBAAoB,GAAG,KAAK;IACnBC,QAAQ,GAAkB,EAAE;IAC5BC,SAAS,GAAY,KAAK;IAC1BC,UAAU,GAAY,KAAK;IAC3BC,UAAU,GAAY,IAAI;IACzBC,WAAW,GAAG,IAAItG,YAAY,EAAU;IACxCuG,oBAAoB,GAAG,IAAIvG,YAAY,EAAW;IAClDwG,cAAc,GAAG,IAAIxG,YAAY,EAAW;IAC5CyG,aAAa,GAAG,IAAIzG,YAAY,EAAS;IACnB0G,iBAAiB;IACzBC,SAAS;IACxBC,kBAAkB,GAAY,IAAI;IACjCC,iBAAiB,GAAG,IAAI7G,YAAY,EAAQ;IAC7C8B,aAAa,GAAY,KAAK;IAEvC;IACO4C,aAAa,GAAY,IAAI;IAC7BI,aAAa,GAAY,KAAK;IAErC;IACOR,iBAAiB,GAAU,EAAE;IAC3BwC,gBAAgB,GAAW,gFAAgF;IAEpHC,QAAQA,CAAA;MACN,IAAI,CAACb,QAAQ,GAAG,CACd;QACE9C,IAAI,EAAE,IAAI;QACVH,IAAI,EAAE;OACP,CACF;MACD,IAAI,CAACgD,oBAAoB,GAAG,IAAI;MAEhC;MACA,IAAI,IAAI,CAACX,aAAa,EAAE;QACtB,IAAI,CAAC3D,cAAc,GAAG,IAAI,CAAC2D,aAAa;QACxC;QACA,IAAI,IAAI,CAACI,kBAAkB,IAAI,CAAC,IAAI,CAACC,kBAAkB,EAAE;UACvD,IAAI,CAACA,kBAAkB,GAAG,IAAI,CAACL,aAAa;UAC5C,IAAI,CAACrD,uBAAuB,CAAC+E,QAAQ,CAAC,IAAI,CAAC1B,aAAa,CAAC;QAC3D;MACF;MAEA;MACA,IAAI,IAAI,CAACK,kBAAkB,EAAE;QAC3B,IAAI,CAAC1D,uBAAuB,CAAC+E,QAAQ,CAAC,IAAI,CAACrB,kBAAkB,CAAC;MAChE;IACF;IAEAsB,WAAWA,CAACC,OAAY;MACtB;MACA,IAAIA,OAAO,CAAC,eAAe,CAAC,IAAIA,OAAO,CAAC,eAAe,CAAC,CAACC,YAAY,EAAE;QACrE;QACA,IAAI,CAACxF,cAAc,GAAGuF,OAAO,CAAC,eAAe,CAAC,CAACC,YAAY;MAC7D;MAEA;MACA,IAAID,OAAO,CAAC,oBAAoB,CAAC,IAAIA,OAAO,CAAC,oBAAoB,CAAC,CAACC,YAAY,KAAKC,SAAS,EAAE;QAC7F,IAAI,CAACnF,uBAAuB,CAAC+E,QAAQ,CAACE,OAAO,CAAC,oBAAoB,CAAC,CAACC,YAAY,CAAC;MACnF;IACF;IAEAE,kBAAkBA,CAAA;MAChB,IAAI,IAAI,CAACpB,oBAAoB,EAAE;QAC7B,IAAI,CAACqB,cAAc,EAAE;QACrB,IAAI,CAACrB,oBAAoB,GAAG,KAAK;MACnC;IACF;IAEAqB,cAAcA,CAAA;MACZ,IAAI;QACF,IAAI,IAAI,CAACZ,iBAAiB,IAAI,IAAI,CAACA,iBAAiB,CAACa,aAAa,EAAE;UAClE;UACA,IAAI,CAACb,iBAAiB,CAACa,aAAa,CAACC,SAAS,GAC5C,IAAI,CAACd,iBAAiB,CAACa,aAAa,CAACE,YAAY;QACrD;MACF,CAAC,CAAC,OAAOC,GAAG,EAAE;QACZC,OAAO,CAACC,KAAK,CAAC,4BAA4B,EAAEF,GAAG,CAAC;MAClD;IACF;IAEAG,iBAAiBA,CAAA;MACf,IAAI,CAAC,IAAI,CAAC9B,SAAS,CAAC+B,IAAI,EAAE,IAAI,IAAI,CAAC1B,UAAU,EAAE;QAC7C;MACF;MAEA;MACA,IAAI,CAACF,QAAQ,GAAG,CACd,GAAG,IAAI,CAACA,QAAQ,EAChB;QACE9C,IAAI,EAAE,MAAM;QACZH,IAAI,EAAE,IAAI,CAAC8C;OACZ,CACF;MACD,IAAI,CAACE,oBAAoB,GAAG,IAAI;MAEhC;MACA,MAAM8B,WAAW,GAAG,IAAI,CAAChC,SAAS;MAClC,IAAI,CAACA,SAAS,GAAG,EAAE;MACnB,IAAI,CAACO,WAAW,CAAC0B,IAAI,CAACD,WAAW,CAAC;MAElC;MACA,IAAI,CAACE,UAAU,EAAE;IACnB;IAEQA,UAAUA,CAAA;MAChB,IAAI,CAAC3D,iBAAiB,GAAG,EAAE;MAC3B,IAAI,CAACmC,aAAa,CAACuB,IAAI,CAAC,IAAI,CAAC1D,iBAAiB,CAAC;IACjD;IAEA4D,UAAUA,CAAA;MACR,IAAI,CAAC/C,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;IACpC;IAEAgD,mBAAmBA,CAACC,KAAU;MAC5BT,OAAO,CAACU,GAAG,CAAC,uBAAuB,EAAED,KAAK,CAAC;IAC7C;IAEA3D,sBAAsBA,CAAC2D,KAAU;MAC/B,IAAI,CAAC1D,aAAa,GAAG0D,KAAK;MAE1B;MACA,IAAIA,KAAK,IAAI,IAAI,CAACtD,aAAa,EAAE;QAC/B,IAAI,CAACA,aAAa,GAAG,KAAK;QAC1B,IAAI,CAAC0B,cAAc,CAACwB,IAAI,CAAC,KAAK,CAAC;MACjC;MAEAL,OAAO,CAACU,GAAG,CAAC,sBAAsB,EAAED,KAAK,CAAC;MAC1C,IAAI,CAAC7B,oBAAoB,CAACyB,IAAI,CAACI,KAAK,CAAC;IACvC;IAEAvD,gBAAgBA,CAACuD,KAAU;MACzB,IAAI,CAACtD,aAAa,GAAGsD,KAAK;MAE1B;MACA,IAAIA,KAAK,IAAI,IAAI,CAAC1D,aAAa,EAAE;QAC/B,IAAI,CAACA,aAAa,GAAG,KAAK;QAC1B,IAAI,CAAC6B,oBAAoB,CAACyB,IAAI,CAAC,KAAK,CAAC;MACvC;MAEAL,OAAO,CAACU,GAAG,CAAC,eAAe,EAAED,KAAK,CAAC;MACnC,IAAI,CAAC5B,cAAc,CAACwB,IAAI,CAACI,KAAK,CAAC;IACjC;IAEA5G,cAAcA,CAAC8G,aAAkB;MAC/B;MACA;MACA,IAAIC,YAAoB;MAExB,IAAI,OAAOD,aAAa,KAAK,QAAQ,EAAE;QACrCC,YAAY,GAAGD,aAAa;MAC9B,CAAC,MAAM,IAAIA,aAAa,IAAIA,aAAa,CAAChD,aAAa,EAAE;QACvDiD,YAAY,GAAGD,aAAa,CAAChD,aAAa;MAC5C,CAAC,MAAM,IAAIgD,aAAa,IAAIA,aAAa,CAACE,eAAe,IAAIF,aAAa,CAACE,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACrGF,YAAY,GAAGD,aAAa,CAACE,eAAe,CAAC,CAAC,CAAC,CAACE,IAAI;MACtD,CAAC,MAAM;QACL;MACF;MAEA,IAAI,CAAC/G,cAAc,GAAG4G,YAAY;MAElC;MACA,IAAI,IAAI,CAAC7C,kBAAkB,EAAE;QAC3B,IAAI,CAACC,kBAAkB,GAAG4C,YAAY;QACtC,IAAI,CAACtG,uBAAuB,CAAC+E,QAAQ,CAACuB,YAAY,CAAC;MACrD;MAEA;MACA,MAAMI,cAAc,GAAG,IAAI,CAACjH,aAAa,CAACkH,IAAI,CAC3CC,GAAG,IAAKA,GAAG,CAACH,IAAI,KAAKH,YAAY,CACnC;MAED,IAAII,cAAc,EAAE;QAClB,IAAI,CAACtD,YAAY,CAAC2C,IAAI,CAACW,cAAc,CAAC;MACxC;IACF;IAEA3F,eAAeA,CAACC,IAAY;MAC5B6F,SAAS,CAACC,SAAS,CAACC,SAAS,CAAC/F,IAAI,CAAC,CAACgG,IAAI,CAAC,MAAK;QAC5C,IAAI,CAACzF,eAAe,GAAG,IAAI;QAC3B0F,UAAU,CAAC,MAAK;UACd,IAAI,CAAC1F,eAAe,GAAG,KAAK;QAC9B,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,CAAC;IACJ;IACEf,IAAIA,CAAA;MACF,IAAI,CAAC0C,UAAU,GAAG,KAAK;MACvBwC,OAAO,CAACU,GAAG,CAAC,cAAc,CAAC;MAC3B;IACF;IAEA1F,MAAMA,CAAA;MACJ,IAAI,CAACwC,UAAU,GAAG,KAAK;MACvBwC,OAAO,CAACU,GAAG,CAAC,gBAAgB,CAAC;MAC7B;IACF;IAEA;IAEAc,cAAcA,CAACf,KAAY;MACzB,MAAMgB,MAAM,GAAGhB,KAAK,CAACgB,MAAqB;MAC1C,IAAI,CAACA,MAAM,CAACC,OAAO,CAAC,WAAW,CAAC,EAAE;QAChC,IAAI,CAAClE,UAAU,GAAG,KAAK;MACzB;IACF;IAGAmE,cAAcA,CAAClB,KAAoB;MACjC;MACA,IAAI,CAACA,KAAK,CAACmB,QAAQ,EAAE;QACnBnB,KAAK,CAACoB,cAAc,EAAE;QACtB,IAAI,CAAC3B,iBAAiB,EAAE;MAC1B;IACF;IAEA;IACA4B,cAAcA,CAACrB,KAAU;MACvB,MAAMsB,KAAK,GAAGtB,KAAK,CAACgB,MAAM,CAACM,KAAK;MAChC,IAAIA,KAAK,IAAIA,KAAK,CAACjB,MAAM,GAAG,CAAC,EAAE;QAC7B,IAAI,CAACnE,iBAAiB,GAAG,EAAE;QAE3B,KAAK,IAAIqF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACjB,MAAM,EAAEkB,CAAC,EAAE,EAAE;UACrC,MAAMC,IAAI,GAAGF,KAAK,CAACC,CAAC,CAAC;UACrB,IAAI,CAACrF,iBAAiB,CAACuF,IAAI,CAAC;YAC1BC,EAAE,EAAE,QAAQC,IAAI,CAACC,GAAG,EAAE,IAAIL,CAAC,EAAE;YAC7BvF,YAAY,EAAEwF,IAAI,CAAClB,IAAI;YACvBuB,OAAO,EAAEL,IAAI,CAACM,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC;YACvCP,IAAI,EAAEA;WACP,CAAC;QACJ;QAEAjC,OAAO,CAACU,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAAC/D,iBAAiB,CAAC;QACtD,IAAI,CAACmC,aAAa,CAACuB,IAAI,CAAC,IAAI,CAAC1D,iBAAiB,CAAC;MACjD;IACF;IAEAJ,UAAUA,CAACD,KAAa;MACtB,IAAI,CAACK,iBAAiB,CAAC8F,MAAM,CAACnG,KAAK,EAAE,CAAC,CAAC;MACvC,IAAI,CAACwC,aAAa,CAACuB,IAAI,CAAC,IAAI,CAAC1D,iBAAiB,CAAC;IACjD;IAEA;IACA+F,YAAYA,CAACpG,KAAa,EAAEqG,KAAU;MACpC,OAAOrG,KAAK;IACd;IAEA7B,eAAeA,CAAA;MACb,IAAI,CAACyE,iBAAiB,CAACmB,IAAI,EAAE;IAC/B;;uCA9QW9C,mBAAmB;IAAA;;YAAnBA,mBAAmB;MAAAqF,SAAA;MAAAC,SAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;UAAnBjK,EAAA,CAAAQ,UAAA,mBAAA2J,6CAAAzJ,MAAA;YAAA,OAAAwJ,GAAA,CAAAxB,cAAA,CAAAhI,MAAA,CAAsB;UAAA,UAAAV,EAAA,CAAAoK,iBAAA,CAAH,2BAAAC,qDAAA3J,MAAA;YAAA,OAAnBwJ,GAAA,CAAArB,cAAA,CAAAnI,MAAA,CAAsB;UAAA,EAAH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;UC7C9BV,EADF,CAAAC,cAAA,aAAkC,aACF;UAgB5BD,EAfA,CAAAmB,UAAA,IAAAmJ,kCAAA,iBAAmD,IAAAC,kCAAA,iBAEE,IAAAC,kCAAA,iBAaM;UAU3DxK,EAAA,CAAAC,cAAA,aAAsB;UAEpBD,EAAA,CAAAmB,UAAA,IAAAsJ,yCAAA,wBAKqC;UACrCzK,EAAA,CAAAC,cAAA,iBAAwD;UAAjCD,EAAA,CAAAQ,UAAA,mBAAAkK,kDAAA;YAAA1K,EAAA,CAAAW,aAAA,CAAAgK,GAAA;YAAA,OAAA3K,EAAA,CAAAc,WAAA,CAASoJ,GAAA,CAAAzC,UAAA,EAAY;UAAA,EAAC;UAG3CzH,EAFA,CAAAsB,SAAA,WAAa,YACA,YACA;UACftB,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAmB,UAAA,KAAAyJ,mCAAA,kBAAkD;UAgBtD5K,EADE,CAAAG,YAAA,EAAM,EACF;UACNH,EAAA,CAAAC,cAAA,kBAAuC;UA4BrCD,EA1BA,CAAAmB,UAAA,KAAA0J,mCAAA,kBAAkG,KAAAC,mCAAA,kBAetC,KAAAC,mCAAA,kBAWC;UAG/D/K,EAAA,CAAAG,YAAA,EAAM;UAINH,EADA,CAAAC,cAAA,eAA6B,oBAM5B;UAJCD,EAAA,CAAAgL,gBAAA,2BAAAC,gEAAAvK,MAAA;YAAAV,EAAA,CAAAW,aAAA,CAAAgK,GAAA;YAAA3K,EAAA,CAAAkL,kBAAA,CAAAhB,GAAA,CAAA5E,SAAA,EAAA5E,MAAA,MAAAwJ,GAAA,CAAA5E,SAAA,GAAA5E,MAAA;YAAA,OAAAV,EAAA,CAAAc,WAAA,CAAAJ,MAAA;UAAA,EAAuB;UAEvBV,EAAA,CAAAQ,UAAA,2BAAA2K,gEAAAzK,MAAA;YAAAV,EAAA,CAAAW,aAAA,CAAAgK,GAAA;YAAiBT,GAAA,CAAA9C,iBAAA,EAAmB;YAAA,OAAApH,EAAA,CAAAc,WAAA,CAAEJ,MAAA,CAAAqI,cAAA,EAAuB;UAAA,EAAC;UAE/D/I,EAAA,CAAAG,YAAA,EAAW;UAEZH,EAAA,CAAAmB,UAAA,KAAAiK,sCAAA,qBAAwG;UAMtGpL,EAAA,CAAAC,cAAA,oBAMoC;UAAlCD,EAAA,CAAAQ,UAAA,oBAAA6K,sDAAA3K,MAAA;YAAAV,EAAA,CAAAW,aAAA,CAAAgK,GAAA;YAAA,OAAA3K,EAAA,CAAAc,WAAA,CAAUoJ,GAAA,CAAAlB,cAAA,CAAAtI,MAAA,CAAsB;UAAA,EAAC;UANnCV,EAAA,CAAAG,YAAA,EAMoC;UAGpCH,EAAA,CAAAmB,UAAA,KAAAmK,mCAAA,kBAAiE;UAYjEtL,EALF,CAAAC,cAAA,eAAyB,kBAKkF;UAAnED,EAAA,CAAAQ,UAAA,mBAAA+K,sDAAA;YAAAvL,EAAA,CAAAW,aAAA,CAAAgK,GAAA;YAAA,OAAA3K,EAAA,CAAAc,WAAA,CAASoJ,GAAA,CAAA9C,iBAAA,EAAmB;UAAA,EAAC;UACjEpH,EAAA,CAAAsB,SAAA,oBACa;UAGnBtB,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;UAKJH,EAAA,CAAAmB,UAAA,KAAAqK,mCAAA,kBAA0F;UAkC5FxL,EAAA,CAAAG,YAAA,EAAM;;;UAzK0BH,EAAA,CAAAI,SAAA,GAAqB;UAArBJ,EAAA,CAAAgB,UAAA,SAAAkJ,GAAA,CAAA3J,eAAA,CAAqB;UAEhBP,EAAA,CAAAI,SAAA,EAAkB;UAAlBJ,EAAA,CAAAgB,UAAA,SAAAkJ,GAAA,CAAAlF,YAAA,CAAkB;UAalBhF,EAAA,CAAAI,SAAA,EAAwB;UAAxBJ,EAAA,CAAAgB,UAAA,SAAAkJ,GAAA,CAAAjF,kBAAA,CAAwB;UAY1CjF,EAAA,CAAAI,SAAA,GAAwB;UAAxBJ,EAAA,CAAAgB,UAAA,SAAAkJ,GAAA,CAAA/D,kBAAA,CAAwB;UAYLnG,EAAA,CAAAI,SAAA,GAAgB;UAAhBJ,EAAA,CAAAgB,UAAA,SAAAkJ,GAAA,CAAAxF,UAAA,CAAgB;UAmB7B1E,EAAA,CAAAI,SAAA,GAAa;UAAAJ,EAAb,CAAAgB,UAAA,YAAAkJ,GAAA,CAAAzE,QAAA,CAAa,iBAAAyE,GAAA,CAAAN,YAAA,CAAqB;UAejD5J,EAAA,CAAAI,SAAA,EAA6B;UAA7BJ,EAAA,CAAAgB,UAAA,SAAAkJ,GAAA,CAAAxE,SAAA,IAAAwE,GAAA,CAAAtE,UAAA,CAA6B;UAW7B5F,EAAA,CAAAI,SAAA,EAA8B;UAA9BJ,EAAA,CAAAgB,UAAA,SAAAkJ,GAAA,CAAAxE,SAAA,KAAAwE,GAAA,CAAAtE,UAAA,CAA8B;UAQpC5F,EAAA,CAAAI,SAAA,GAAuB;UAAvBJ,EAAA,CAAAyL,gBAAA,YAAAvB,GAAA,CAAA5E,SAAA,CAAuB;UACvBtF,EAAA,CAAAgB,UAAA,aAAAkJ,GAAA,CAAAvE,UAAA,IAAAuE,GAAA,CAAAxE,SAAA,CAAoC;UAK7B1F,EAAA,CAAAI,SAAA,EAA0B;UAA1BJ,EAAA,CAAAgB,UAAA,SAAAkJ,GAAA,CAAA/E,oBAAA,CAA0B;UAS/BnF,EAAA,CAAAI,SAAA,EAA2B;UAA3BJ,EAAA,CAAAgB,UAAA,WAAAkJ,GAAA,CAAA7D,gBAAA,CAA2B;UAMArG,EAAA,CAAAI,SAAA,GAAkC;UAAlCJ,EAAA,CAAAgB,UAAA,SAAAkJ,GAAA,CAAArG,iBAAA,CAAAmE,MAAA,KAAkC;UAYKhI,EAAA,CAAAI,SAAA,GAAoC;UAApCJ,EAAA,CAAAgB,UAAA,aAAAkJ,GAAA,CAAAxE,SAAA,IAAAwE,GAAA,CAAAvE,UAAA,CAAoC;UAC/C3F,EAAA,CAAAI,SAAA,EAAe;UAAfJ,EAAA,CAAAgB,UAAA,gBAAe;UAS3ChB,EAAA,CAAAI,SAAA,EAAyD;UAAzDJ,EAAA,CAAAgB,UAAA,SAAAkJ,GAAA,CAAA1F,0BAAA,IAAA0F,GAAA,CAAAnF,qBAAA,CAAyD;;;qBDxGtFzF,YAAY,EAAAoM,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EACZjM,iBAAiB,EACjBJ,WAAW,EAAAsM,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EACXvM,mBAAmB,EAAAoM,EAAA,CAAAI,oBAAA,EACnBvM,eAAe,EACfG,eAAe,EACfD,aAAa,EAEbE,mBAAmB;MAAAoM,MAAA;IAAA;;SAKV1H,mBAAmB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}