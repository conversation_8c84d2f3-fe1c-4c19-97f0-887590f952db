{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { InvalidBracketAstNode, ListAstNode, PairAstNode, TextAstNode } from './ast.js';\nimport { BeforeEditPositionMapper } from './beforeEditPositionMapper.js';\nimport { SmallImmutableSet } from './smallImmutableSet.js';\nimport { lengthIs<PERSON>ero, lengthLessThan } from './length.js';\nimport { concat23Trees, concat23TreesOfSameHeight } from './concat23Trees.js';\nimport { NodeReader } from './nodeReader.js';\n/**\n * Non incrementally built ASTs are immutable.\n*/\nexport function parseDocument(tokenizer, edits, oldNode, createImmutableLists) {\n  const parser = new Parser(tokenizer, edits, oldNode, createImmutableLists);\n  return parser.parseDocument();\n}\n/**\n * Non incrementally built ASTs are immutable.\n*/\nclass Parser {\n  constructor(tokenizer, edits, oldNode, createImmutableLists) {\n    this.tokenizer = tokenizer;\n    this.createImmutableLists = createImmutableLists;\n    this._itemsConstructed = 0;\n    this._itemsFromCache = 0;\n    if (oldNode && createImmutableLists) {\n      throw new Error('Not supported');\n    }\n    this.oldNodeReader = oldNode ? new NodeReader(oldNode) : undefined;\n    this.positionMapper = new BeforeEditPositionMapper(edits);\n  }\n  parseDocument() {\n    this._itemsConstructed = 0;\n    this._itemsFromCache = 0;\n    let result = this.parseList(SmallImmutableSet.getEmpty(), 0);\n    if (!result) {\n      result = ListAstNode.getEmpty();\n    }\n    return result;\n  }\n  parseList(openedBracketIds, level) {\n    const items = [];\n    while (true) {\n      let child = this.tryReadChildFromCache(openedBracketIds);\n      if (!child) {\n        const token = this.tokenizer.peek();\n        if (!token || token.kind === 2 /* TokenKind.ClosingBracket */ && token.bracketIds.intersects(openedBracketIds)) {\n          break;\n        }\n        child = this.parseChild(openedBracketIds, level + 1);\n      }\n      if (child.kind === 4 /* AstNodeKind.List */ && child.childrenLength === 0) {\n        continue;\n      }\n      items.push(child);\n    }\n    // When there is no oldNodeReader, all items are created from scratch and must have the same height.\n    const result = this.oldNodeReader ? concat23Trees(items) : concat23TreesOfSameHeight(items, this.createImmutableLists);\n    return result;\n  }\n  tryReadChildFromCache(openedBracketIds) {\n    if (this.oldNodeReader) {\n      const maxCacheableLength = this.positionMapper.getDistanceToNextChange(this.tokenizer.offset);\n      if (maxCacheableLength === null || !lengthIsZero(maxCacheableLength)) {\n        const cachedNode = this.oldNodeReader.readLongestNodeAt(this.positionMapper.getOffsetBeforeChange(this.tokenizer.offset), curNode => {\n          // The edit could extend the ending token, thus we cannot re-use nodes that touch the edit.\n          // If there is no edit anymore, we can re-use the node in any case.\n          if (maxCacheableLength !== null && !lengthLessThan(curNode.length, maxCacheableLength)) {\n            // Either the node contains edited text or touches edited text.\n            // In the latter case, brackets might have been extended (`end` -> `ending`), so even touching nodes cannot be reused.\n            return false;\n          }\n          const canBeReused = curNode.canBeReused(openedBracketIds);\n          return canBeReused;\n        });\n        if (cachedNode) {\n          this._itemsFromCache++;\n          this.tokenizer.skip(cachedNode.length);\n          return cachedNode;\n        }\n      }\n    }\n    return undefined;\n  }\n  parseChild(openedBracketIds, level) {\n    this._itemsConstructed++;\n    const token = this.tokenizer.read();\n    switch (token.kind) {\n      case 2 /* TokenKind.ClosingBracket */:\n        return new InvalidBracketAstNode(token.bracketIds, token.length);\n      case 0 /* TokenKind.Text */:\n        return token.astNode;\n      case 1 /* TokenKind.OpeningBracket */:\n        {\n          if (level > 300) {\n            // To prevent stack overflows\n            return new TextAstNode(token.length);\n          }\n          const set = openedBracketIds.merge(token.bracketIds);\n          const child = this.parseList(set, level + 1);\n          const nextToken = this.tokenizer.peek();\n          if (nextToken && nextToken.kind === 2 /* TokenKind.ClosingBracket */ && (nextToken.bracketId === token.bracketId || nextToken.bracketIds.intersects(token.bracketIds))) {\n            this.tokenizer.read();\n            return PairAstNode.create(token.astNode, child, nextToken.astNode);\n          } else {\n            return PairAstNode.create(token.astNode, child, null);\n          }\n        }\n      default:\n        throw new Error('unexpected');\n    }\n  }\n}", "map": {"version": 3, "names": ["InvalidBracketAstNode", "ListAstNode", "PairAstNode", "TextAstNode", "BeforeEditPositionMapper", "SmallImmutableSet", "lengthIsZero", "lengthLessThan", "concat23Trees", "concat23TreesOfSameHeight", "NodeReader", "parseDocument", "tokenizer", "edits", "oldNode", "createImmutableLists", "parser", "<PERSON><PERSON><PERSON>", "constructor", "_itemsConstructed", "_itemsFromCache", "Error", "oldNodeReader", "undefined", "positionMapper", "result", "parseList", "getEmpty", "openedBracketIds", "level", "items", "child", "tryReadChildFromCache", "token", "peek", "kind", "bracketIds", "intersects", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "push", "maxC<PERSON><PERSON><PERSON><PERSON><PERSON>", "getDistanceToNextChange", "offset", "cachedNode", "readLongestNodeAt", "getOffsetBeforeChange", "curNode", "length", "canBeReused", "skip", "read", "astNode", "set", "merge", "nextToken", "bracketId", "create"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/parser.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { InvalidBracketAstNode, ListAstNode, PairAstNode, TextAstNode } from './ast.js';\nimport { BeforeEditPositionMapper } from './beforeEditPositionMapper.js';\nimport { SmallImmutableSet } from './smallImmutableSet.js';\nimport { lengthIs<PERSON>ero, lengthLessThan } from './length.js';\nimport { concat23Trees, concat23TreesOfSameHeight } from './concat23Trees.js';\nimport { NodeReader } from './nodeReader.js';\n/**\n * Non incrementally built ASTs are immutable.\n*/\nexport function parseDocument(tokenizer, edits, oldNode, createImmutableLists) {\n    const parser = new Parser(tokenizer, edits, oldNode, createImmutableLists);\n    return parser.parseDocument();\n}\n/**\n * Non incrementally built ASTs are immutable.\n*/\nclass Parser {\n    constructor(tokenizer, edits, oldNode, createImmutableLists) {\n        this.tokenizer = tokenizer;\n        this.createImmutableLists = createImmutableLists;\n        this._itemsConstructed = 0;\n        this._itemsFromCache = 0;\n        if (oldNode && createImmutableLists) {\n            throw new Error('Not supported');\n        }\n        this.oldNodeReader = oldNode ? new NodeReader(oldNode) : undefined;\n        this.positionMapper = new BeforeEditPositionMapper(edits);\n    }\n    parseDocument() {\n        this._itemsConstructed = 0;\n        this._itemsFromCache = 0;\n        let result = this.parseList(SmallImmutableSet.getEmpty(), 0);\n        if (!result) {\n            result = ListAstNode.getEmpty();\n        }\n        return result;\n    }\n    parseList(openedBracketIds, level) {\n        const items = [];\n        while (true) {\n            let child = this.tryReadChildFromCache(openedBracketIds);\n            if (!child) {\n                const token = this.tokenizer.peek();\n                if (!token ||\n                    (token.kind === 2 /* TokenKind.ClosingBracket */ &&\n                        token.bracketIds.intersects(openedBracketIds))) {\n                    break;\n                }\n                child = this.parseChild(openedBracketIds, level + 1);\n            }\n            if (child.kind === 4 /* AstNodeKind.List */ && child.childrenLength === 0) {\n                continue;\n            }\n            items.push(child);\n        }\n        // When there is no oldNodeReader, all items are created from scratch and must have the same height.\n        const result = this.oldNodeReader ? concat23Trees(items) : concat23TreesOfSameHeight(items, this.createImmutableLists);\n        return result;\n    }\n    tryReadChildFromCache(openedBracketIds) {\n        if (this.oldNodeReader) {\n            const maxCacheableLength = this.positionMapper.getDistanceToNextChange(this.tokenizer.offset);\n            if (maxCacheableLength === null || !lengthIsZero(maxCacheableLength)) {\n                const cachedNode = this.oldNodeReader.readLongestNodeAt(this.positionMapper.getOffsetBeforeChange(this.tokenizer.offset), curNode => {\n                    // The edit could extend the ending token, thus we cannot re-use nodes that touch the edit.\n                    // If there is no edit anymore, we can re-use the node in any case.\n                    if (maxCacheableLength !== null && !lengthLessThan(curNode.length, maxCacheableLength)) {\n                        // Either the node contains edited text or touches edited text.\n                        // In the latter case, brackets might have been extended (`end` -> `ending`), so even touching nodes cannot be reused.\n                        return false;\n                    }\n                    const canBeReused = curNode.canBeReused(openedBracketIds);\n                    return canBeReused;\n                });\n                if (cachedNode) {\n                    this._itemsFromCache++;\n                    this.tokenizer.skip(cachedNode.length);\n                    return cachedNode;\n                }\n            }\n        }\n        return undefined;\n    }\n    parseChild(openedBracketIds, level) {\n        this._itemsConstructed++;\n        const token = this.tokenizer.read();\n        switch (token.kind) {\n            case 2 /* TokenKind.ClosingBracket */:\n                return new InvalidBracketAstNode(token.bracketIds, token.length);\n            case 0 /* TokenKind.Text */:\n                return token.astNode;\n            case 1 /* TokenKind.OpeningBracket */: {\n                if (level > 300) {\n                    // To prevent stack overflows\n                    return new TextAstNode(token.length);\n                }\n                const set = openedBracketIds.merge(token.bracketIds);\n                const child = this.parseList(set, level + 1);\n                const nextToken = this.tokenizer.peek();\n                if (nextToken &&\n                    nextToken.kind === 2 /* TokenKind.ClosingBracket */ &&\n                    (nextToken.bracketId === token.bracketId || nextToken.bracketIds.intersects(token.bracketIds))) {\n                    this.tokenizer.read();\n                    return PairAstNode.create(token.astNode, child, nextToken.astNode);\n                }\n                else {\n                    return PairAstNode.create(token.astNode, child, null);\n                }\n            }\n            default:\n                throw new Error('unexpected');\n        }\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,qBAAqB,EAAEC,WAAW,EAAEC,WAAW,EAAEC,WAAW,QAAQ,UAAU;AACvF,SAASC,wBAAwB,QAAQ,+BAA+B;AACxE,SAASC,iBAAiB,QAAQ,wBAAwB;AAC1D,SAASC,YAAY,EAAEC,cAAc,QAAQ,aAAa;AAC1D,SAASC,aAAa,EAAEC,yBAAyB,QAAQ,oBAAoB;AAC7E,SAASC,UAAU,QAAQ,iBAAiB;AAC5C;AACA;AACA;AACA,OAAO,SAASC,aAAaA,CAACC,SAAS,EAAEC,KAAK,EAAEC,OAAO,EAAEC,oBAAoB,EAAE;EAC3E,MAAMC,MAAM,GAAG,IAAIC,MAAM,CAACL,SAAS,EAAEC,KAAK,EAAEC,OAAO,EAAEC,oBAAoB,CAAC;EAC1E,OAAOC,MAAM,CAACL,aAAa,CAAC,CAAC;AACjC;AACA;AACA;AACA;AACA,MAAMM,MAAM,CAAC;EACTC,WAAWA,CAACN,SAAS,EAAEC,KAAK,EAAEC,OAAO,EAAEC,oBAAoB,EAAE;IACzD,IAAI,CAACH,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACG,oBAAoB,GAAGA,oBAAoB;IAChD,IAAI,CAACI,iBAAiB,GAAG,CAAC;IAC1B,IAAI,CAACC,eAAe,GAAG,CAAC;IACxB,IAAIN,OAAO,IAAIC,oBAAoB,EAAE;MACjC,MAAM,IAAIM,KAAK,CAAC,eAAe,CAAC;IACpC;IACA,IAAI,CAACC,aAAa,GAAGR,OAAO,GAAG,IAAIJ,UAAU,CAACI,OAAO,CAAC,GAAGS,SAAS;IAClE,IAAI,CAACC,cAAc,GAAG,IAAIpB,wBAAwB,CAACS,KAAK,CAAC;EAC7D;EACAF,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACQ,iBAAiB,GAAG,CAAC;IAC1B,IAAI,CAACC,eAAe,GAAG,CAAC;IACxB,IAAIK,MAAM,GAAG,IAAI,CAACC,SAAS,CAACrB,iBAAiB,CAACsB,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;IAC5D,IAAI,CAACF,MAAM,EAAE;MACTA,MAAM,GAAGxB,WAAW,CAAC0B,QAAQ,CAAC,CAAC;IACnC;IACA,OAAOF,MAAM;EACjB;EACAC,SAASA,CAACE,gBAAgB,EAAEC,KAAK,EAAE;IAC/B,MAAMC,KAAK,GAAG,EAAE;IAChB,OAAO,IAAI,EAAE;MACT,IAAIC,KAAK,GAAG,IAAI,CAACC,qBAAqB,CAACJ,gBAAgB,CAAC;MACxD,IAAI,CAACG,KAAK,EAAE;QACR,MAAME,KAAK,GAAG,IAAI,CAACrB,SAAS,CAACsB,IAAI,CAAC,CAAC;QACnC,IAAI,CAACD,KAAK,IACLA,KAAK,CAACE,IAAI,KAAK,CAAC,CAAC,kCACdF,KAAK,CAACG,UAAU,CAACC,UAAU,CAACT,gBAAgB,CAAE,EAAE;UACpD;QACJ;QACAG,KAAK,GAAG,IAAI,CAACO,UAAU,CAACV,gBAAgB,EAAEC,KAAK,GAAG,CAAC,CAAC;MACxD;MACA,IAAIE,KAAK,CAACI,IAAI,KAAK,CAAC,CAAC,0BAA0BJ,KAAK,CAACQ,cAAc,KAAK,CAAC,EAAE;QACvE;MACJ;MACAT,KAAK,CAACU,IAAI,CAACT,KAAK,CAAC;IACrB;IACA;IACA,MAAMN,MAAM,GAAG,IAAI,CAACH,aAAa,GAAGd,aAAa,CAACsB,KAAK,CAAC,GAAGrB,yBAAyB,CAACqB,KAAK,EAAE,IAAI,CAACf,oBAAoB,CAAC;IACtH,OAAOU,MAAM;EACjB;EACAO,qBAAqBA,CAACJ,gBAAgB,EAAE;IACpC,IAAI,IAAI,CAACN,aAAa,EAAE;MACpB,MAAMmB,kBAAkB,GAAG,IAAI,CAACjB,cAAc,CAACkB,uBAAuB,CAAC,IAAI,CAAC9B,SAAS,CAAC+B,MAAM,CAAC;MAC7F,IAAIF,kBAAkB,KAAK,IAAI,IAAI,CAACnC,YAAY,CAACmC,kBAAkB,CAAC,EAAE;QAClE,MAAMG,UAAU,GAAG,IAAI,CAACtB,aAAa,CAACuB,iBAAiB,CAAC,IAAI,CAACrB,cAAc,CAACsB,qBAAqB,CAAC,IAAI,CAAClC,SAAS,CAAC+B,MAAM,CAAC,EAAEI,OAAO,IAAI;UACjI;UACA;UACA,IAAIN,kBAAkB,KAAK,IAAI,IAAI,CAAClC,cAAc,CAACwC,OAAO,CAACC,MAAM,EAAEP,kBAAkB,CAAC,EAAE;YACpF;YACA;YACA,OAAO,KAAK;UAChB;UACA,MAAMQ,WAAW,GAAGF,OAAO,CAACE,WAAW,CAACrB,gBAAgB,CAAC;UACzD,OAAOqB,WAAW;QACtB,CAAC,CAAC;QACF,IAAIL,UAAU,EAAE;UACZ,IAAI,CAACxB,eAAe,EAAE;UACtB,IAAI,CAACR,SAAS,CAACsC,IAAI,CAACN,UAAU,CAACI,MAAM,CAAC;UACtC,OAAOJ,UAAU;QACrB;MACJ;IACJ;IACA,OAAOrB,SAAS;EACpB;EACAe,UAAUA,CAACV,gBAAgB,EAAEC,KAAK,EAAE;IAChC,IAAI,CAACV,iBAAiB,EAAE;IACxB,MAAMc,KAAK,GAAG,IAAI,CAACrB,SAAS,CAACuC,IAAI,CAAC,CAAC;IACnC,QAAQlB,KAAK,CAACE,IAAI;MACd,KAAK,CAAC,CAAC;QACH,OAAO,IAAInC,qBAAqB,CAACiC,KAAK,CAACG,UAAU,EAAEH,KAAK,CAACe,MAAM,CAAC;MACpE,KAAK,CAAC,CAAC;QACH,OAAOf,KAAK,CAACmB,OAAO;MACxB,KAAK,CAAC,CAAC;QAAgC;UACnC,IAAIvB,KAAK,GAAG,GAAG,EAAE;YACb;YACA,OAAO,IAAI1B,WAAW,CAAC8B,KAAK,CAACe,MAAM,CAAC;UACxC;UACA,MAAMK,GAAG,GAAGzB,gBAAgB,CAAC0B,KAAK,CAACrB,KAAK,CAACG,UAAU,CAAC;UACpD,MAAML,KAAK,GAAG,IAAI,CAACL,SAAS,CAAC2B,GAAG,EAAExB,KAAK,GAAG,CAAC,CAAC;UAC5C,MAAM0B,SAAS,GAAG,IAAI,CAAC3C,SAAS,CAACsB,IAAI,CAAC,CAAC;UACvC,IAAIqB,SAAS,IACTA,SAAS,CAACpB,IAAI,KAAK,CAAC,CAAC,mCACpBoB,SAAS,CAACC,SAAS,KAAKvB,KAAK,CAACuB,SAAS,IAAID,SAAS,CAACnB,UAAU,CAACC,UAAU,CAACJ,KAAK,CAACG,UAAU,CAAC,CAAC,EAAE;YAChG,IAAI,CAACxB,SAAS,CAACuC,IAAI,CAAC,CAAC;YACrB,OAAOjD,WAAW,CAACuD,MAAM,CAACxB,KAAK,CAACmB,OAAO,EAAErB,KAAK,EAAEwB,SAAS,CAACH,OAAO,CAAC;UACtE,CAAC,MACI;YACD,OAAOlD,WAAW,CAACuD,MAAM,CAACxB,KAAK,CAACmB,OAAO,EAAErB,KAAK,EAAE,IAAI,CAAC;UACzD;QACJ;MACA;QACI,MAAM,IAAIV,KAAK,CAAC,YAAY,CAAC;IACrC;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}