{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Range } from '../../../common/core/range.js';\nexport class ReplaceAllCommand {\n  constructor(editorSelection, ranges, replaceStrings) {\n    this._editorSelection = editorSelection;\n    this._ranges = ranges;\n    this._replaceStrings = replaceStrings;\n    this._trackedEditorSelectionId = null;\n  }\n  getEditOperations(model, builder) {\n    if (this._ranges.length > 0) {\n      // Collect all edit operations\n      const ops = [];\n      for (let i = 0; i < this._ranges.length; i++) {\n        ops.push({\n          range: this._ranges[i],\n          text: this._replaceStrings[i]\n        });\n      }\n      // Sort them in ascending order by range starts\n      ops.sort((o1, o2) => {\n        return Range.compareRangesUsingStarts(o1.range, o2.range);\n      });\n      // Merge operations that touch each other\n      const resultOps = [];\n      let previousOp = ops[0];\n      for (let i = 1; i < ops.length; i++) {\n        if (previousOp.range.endLineNumber === ops[i].range.startLineNumber && previousOp.range.endColumn === ops[i].range.startColumn) {\n          // These operations are one after another and can be merged\n          previousOp.range = previousOp.range.plusRange(ops[i].range);\n          previousOp.text = previousOp.text + ops[i].text;\n        } else {\n          resultOps.push(previousOp);\n          previousOp = ops[i];\n        }\n      }\n      resultOps.push(previousOp);\n      for (const op of resultOps) {\n        builder.addEditOperation(op.range, op.text);\n      }\n    }\n    this._trackedEditorSelectionId = builder.trackSelection(this._editorSelection);\n  }\n  computeCursorState(model, helper) {\n    return helper.getTrackedSelection(this._trackedEditorSelectionId);\n  }\n}", "map": {"version": 3, "names": ["Range", "ReplaceAllCommand", "constructor", "editorSelection", "ranges", "replaceStrings", "_editorSelection", "_ranges", "_replaceStrings", "_trackedEditorSelectionId", "getEditOperations", "model", "builder", "length", "ops", "i", "push", "range", "text", "sort", "o1", "o2", "compareRangesUsingStarts", "resultOps", "previousOp", "endLineNumber", "startLineNumber", "endColumn", "startColumn", "plusRange", "op", "addEditOperation", "trackSelection", "computeCursorState", "helper", "getTrackedSelection"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/contrib/find/browser/replaceAllCommand.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Range } from '../../../common/core/range.js';\nexport class ReplaceAllCommand {\n    constructor(editorSelection, ranges, replaceStrings) {\n        this._editorSelection = editorSelection;\n        this._ranges = ranges;\n        this._replaceStrings = replaceStrings;\n        this._trackedEditorSelectionId = null;\n    }\n    getEditOperations(model, builder) {\n        if (this._ranges.length > 0) {\n            // Collect all edit operations\n            const ops = [];\n            for (let i = 0; i < this._ranges.length; i++) {\n                ops.push({\n                    range: this._ranges[i],\n                    text: this._replaceStrings[i]\n                });\n            }\n            // Sort them in ascending order by range starts\n            ops.sort((o1, o2) => {\n                return Range.compareRangesUsingStarts(o1.range, o2.range);\n            });\n            // Merge operations that touch each other\n            const resultOps = [];\n            let previousOp = ops[0];\n            for (let i = 1; i < ops.length; i++) {\n                if (previousOp.range.endLineNumber === ops[i].range.startLineNumber && previousOp.range.endColumn === ops[i].range.startColumn) {\n                    // These operations are one after another and can be merged\n                    previousOp.range = previousOp.range.plusRange(ops[i].range);\n                    previousOp.text = previousOp.text + ops[i].text;\n                }\n                else {\n                    resultOps.push(previousOp);\n                    previousOp = ops[i];\n                }\n            }\n            resultOps.push(previousOp);\n            for (const op of resultOps) {\n                builder.addEditOperation(op.range, op.text);\n            }\n        }\n        this._trackedEditorSelectionId = builder.trackSelection(this._editorSelection);\n    }\n    computeCursorState(model, helper) {\n        return helper.getTrackedSelection(this._trackedEditorSelectionId);\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,KAAK,QAAQ,+BAA+B;AACrD,OAAO,MAAMC,iBAAiB,CAAC;EAC3BC,WAAWA,CAACC,eAAe,EAAEC,MAAM,EAAEC,cAAc,EAAE;IACjD,IAAI,CAACC,gBAAgB,GAAGH,eAAe;IACvC,IAAI,CAACI,OAAO,GAAGH,MAAM;IACrB,IAAI,CAACI,eAAe,GAAGH,cAAc;IACrC,IAAI,CAACI,yBAAyB,GAAG,IAAI;EACzC;EACAC,iBAAiBA,CAACC,KAAK,EAAEC,OAAO,EAAE;IAC9B,IAAI,IAAI,CAACL,OAAO,CAACM,MAAM,GAAG,CAAC,EAAE;MACzB;MACA,MAAMC,GAAG,GAAG,EAAE;MACd,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACR,OAAO,CAACM,MAAM,EAAEE,CAAC,EAAE,EAAE;QAC1CD,GAAG,CAACE,IAAI,CAAC;UACLC,KAAK,EAAE,IAAI,CAACV,OAAO,CAACQ,CAAC,CAAC;UACtBG,IAAI,EAAE,IAAI,CAACV,eAAe,CAACO,CAAC;QAChC,CAAC,CAAC;MACN;MACA;MACAD,GAAG,CAACK,IAAI,CAAC,CAACC,EAAE,EAAEC,EAAE,KAAK;QACjB,OAAOrB,KAAK,CAACsB,wBAAwB,CAACF,EAAE,CAACH,KAAK,EAAEI,EAAE,CAACJ,KAAK,CAAC;MAC7D,CAAC,CAAC;MACF;MACA,MAAMM,SAAS,GAAG,EAAE;MACpB,IAAIC,UAAU,GAAGV,GAAG,CAAC,CAAC,CAAC;MACvB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,GAAG,CAACD,MAAM,EAAEE,CAAC,EAAE,EAAE;QACjC,IAAIS,UAAU,CAACP,KAAK,CAACQ,aAAa,KAAKX,GAAG,CAACC,CAAC,CAAC,CAACE,KAAK,CAACS,eAAe,IAAIF,UAAU,CAACP,KAAK,CAACU,SAAS,KAAKb,GAAG,CAACC,CAAC,CAAC,CAACE,KAAK,CAACW,WAAW,EAAE;UAC5H;UACAJ,UAAU,CAACP,KAAK,GAAGO,UAAU,CAACP,KAAK,CAACY,SAAS,CAACf,GAAG,CAACC,CAAC,CAAC,CAACE,KAAK,CAAC;UAC3DO,UAAU,CAACN,IAAI,GAAGM,UAAU,CAACN,IAAI,GAAGJ,GAAG,CAACC,CAAC,CAAC,CAACG,IAAI;QACnD,CAAC,MACI;UACDK,SAAS,CAACP,IAAI,CAACQ,UAAU,CAAC;UAC1BA,UAAU,GAAGV,GAAG,CAACC,CAAC,CAAC;QACvB;MACJ;MACAQ,SAAS,CAACP,IAAI,CAACQ,UAAU,CAAC;MAC1B,KAAK,MAAMM,EAAE,IAAIP,SAAS,EAAE;QACxBX,OAAO,CAACmB,gBAAgB,CAACD,EAAE,CAACb,KAAK,EAAEa,EAAE,CAACZ,IAAI,CAAC;MAC/C;IACJ;IACA,IAAI,CAACT,yBAAyB,GAAGG,OAAO,CAACoB,cAAc,CAAC,IAAI,CAAC1B,gBAAgB,CAAC;EAClF;EACA2B,kBAAkBA,CAACtB,KAAK,EAAEuB,MAAM,EAAE;IAC9B,OAAOA,MAAM,CAACC,mBAAmB,CAAC,IAAI,CAAC1B,yBAAyB,CAAC;EACrE;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}