{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = this && this.__param || function (paramIndex, decorator) {\n  return function (target, key) {\n    decorator(target, key, paramIndex);\n  };\n};\nvar FileReferencesRenderer_1;\nimport * as dom from '../../../../../base/browser/dom.js';\nimport { CountBadge } from '../../../../../base/browser/ui/countBadge/countBadge.js';\nimport { HighlightedLabel } from '../../../../../base/browser/ui/highlightedlabel/highlightedLabel.js';\nimport { IconLabel } from '../../../../../base/browser/ui/iconLabel/iconLabel.js';\nimport { createMatches, FuzzyScore } from '../../../../../base/common/filters.js';\nimport { Disposable } from '../../../../../base/common/lifecycle.js';\nimport { basename, dirname } from '../../../../../base/common/resources.js';\nimport { ITextModelService } from '../../../../common/services/resolverService.js';\nimport { localize } from '../../../../../nls.js';\nimport { IInstantiationService } from '../../../../../platform/instantiation/common/instantiation.js';\nimport { IKeybindingService } from '../../../../../platform/keybinding/common/keybinding.js';\nimport { ILabelService } from '../../../../../platform/label/common/label.js';\nimport { defaultCountBadgeStyles } from '../../../../../platform/theme/browser/defaultStyles.js';\nimport { FileReferences, OneReference, ReferencesModel } from '../referencesModel.js';\nlet DataSource = class DataSource {\n  constructor(_resolverService) {\n    this._resolverService = _resolverService;\n  }\n  hasChildren(element) {\n    if (element instanceof ReferencesModel) {\n      return true;\n    }\n    if (element instanceof FileReferences) {\n      return true;\n    }\n    return false;\n  }\n  getChildren(element) {\n    if (element instanceof ReferencesModel) {\n      return element.groups;\n    }\n    if (element instanceof FileReferences) {\n      return element.resolve(this._resolverService).then(val => {\n        // if (element.failure) {\n        // \t// refresh the element on failure so that\n        // \t// we can update its rendering\n        // \treturn tree.refresh(element).then(() => val.children);\n        // }\n        return val.children;\n      });\n    }\n    throw new Error('bad tree');\n  }\n};\nDataSource = __decorate([__param(0, ITextModelService)], DataSource);\nexport { DataSource };\n//#endregion\nexport class Delegate {\n  getHeight() {\n    return 23;\n  }\n  getTemplateId(element) {\n    if (element instanceof FileReferences) {\n      return FileReferencesRenderer.id;\n    } else {\n      return OneReferenceRenderer.id;\n    }\n  }\n}\nlet StringRepresentationProvider = class StringRepresentationProvider {\n  constructor(_keybindingService) {\n    this._keybindingService = _keybindingService;\n  }\n  getKeyboardNavigationLabel(element) {\n    if (element instanceof OneReference) {\n      const parts = element.parent.getPreview(element)?.preview(element.range);\n      if (parts) {\n        return parts.value;\n      }\n    }\n    // FileReferences or unresolved OneReference\n    return basename(element.uri);\n  }\n};\nStringRepresentationProvider = __decorate([__param(0, IKeybindingService)], StringRepresentationProvider);\nexport { StringRepresentationProvider };\nexport class IdentityProvider {\n  getId(element) {\n    return element instanceof OneReference ? element.id : element.uri;\n  }\n}\n//#region render: File\nlet FileReferencesTemplate = class FileReferencesTemplate extends Disposable {\n  constructor(container, _labelService) {\n    super();\n    this._labelService = _labelService;\n    const parent = document.createElement('div');\n    parent.classList.add('reference-file');\n    this.file = this._register(new IconLabel(parent, {\n      supportHighlights: true\n    }));\n    this.badge = new CountBadge(dom.append(parent, dom.$('.count')), {}, defaultCountBadgeStyles);\n    container.appendChild(parent);\n  }\n  set(element, matches) {\n    const parent = dirname(element.uri);\n    this.file.setLabel(this._labelService.getUriBasenameLabel(element.uri), this._labelService.getUriLabel(parent, {\n      relative: true\n    }), {\n      title: this._labelService.getUriLabel(element.uri),\n      matches\n    });\n    const len = element.children.length;\n    this.badge.setCount(len);\n    if (len > 1) {\n      this.badge.setTitleFormat(localize('referencesCount', \"{0} references\", len));\n    } else {\n      this.badge.setTitleFormat(localize('referenceCount', \"{0} reference\", len));\n    }\n  }\n};\nFileReferencesTemplate = __decorate([__param(1, ILabelService)], FileReferencesTemplate);\nlet FileReferencesRenderer = /*#__PURE__*/(() => {\n  let FileReferencesRenderer = class FileReferencesRenderer {\n    static {\n      FileReferencesRenderer_1 = this;\n    }\n    static {\n      this.id = 'FileReferencesRenderer';\n    }\n    constructor(_instantiationService) {\n      this._instantiationService = _instantiationService;\n      this.templateId = FileReferencesRenderer_1.id;\n    }\n    renderTemplate(container) {\n      return this._instantiationService.createInstance(FileReferencesTemplate, container);\n    }\n    renderElement(node, index, template) {\n      template.set(node.element, createMatches(node.filterData));\n    }\n    disposeTemplate(templateData) {\n      templateData.dispose();\n    }\n  };\n  return FileReferencesRenderer;\n})();\nFileReferencesRenderer = FileReferencesRenderer_1 = __decorate([__param(0, IInstantiationService)], FileReferencesRenderer);\nexport { FileReferencesRenderer };\n//#endregion\n//#region render: Reference\nclass OneReferenceTemplate extends Disposable {\n  constructor(container) {\n    super();\n    this.label = this._register(new HighlightedLabel(container));\n  }\n  set(element, score) {\n    const preview = element.parent.getPreview(element)?.preview(element.range);\n    if (!preview || !preview.value) {\n      // this means we FAILED to resolve the document or the value is the empty string\n      this.label.set(`${basename(element.uri)}:${element.range.startLineNumber + 1}:${element.range.startColumn + 1}`);\n    } else {\n      // render search match as highlight unless\n      // we have score, then render the score\n      const {\n        value,\n        highlight\n      } = preview;\n      if (score && !FuzzyScore.isDefault(score)) {\n        this.label.element.classList.toggle('referenceMatch', false);\n        this.label.set(value, createMatches(score));\n      } else {\n        this.label.element.classList.toggle('referenceMatch', true);\n        this.label.set(value, [highlight]);\n      }\n    }\n  }\n}\nexport let OneReferenceRenderer = /*#__PURE__*/(() => {\n  class OneReferenceRenderer {\n    constructor() {\n      this.templateId = OneReferenceRenderer.id;\n    }\n    static {\n      this.id = 'OneReferenceRenderer';\n    }\n    renderTemplate(container) {\n      return new OneReferenceTemplate(container);\n    }\n    renderElement(node, index, templateData) {\n      templateData.set(node.element, node.filterData);\n    }\n    disposeTemplate(templateData) {\n      templateData.dispose();\n    }\n  }\n  return OneReferenceRenderer;\n})();\n//#endregion\nexport class AccessibilityProvider {\n  getWidgetAriaLabel() {\n    return localize('treeAriaLabel', \"References\");\n  }\n  getAriaLabel(element) {\n    return element.ariaMessage;\n  }\n}", "map": {"version": 3, "names": ["__decorate", "decorators", "target", "key", "desc", "c", "arguments", "length", "r", "Object", "getOwnPropertyDescriptor", "d", "Reflect", "decorate", "i", "defineProperty", "__param", "paramIndex", "decorator", "FileReferencesRenderer_1", "dom", "Count<PERSON>adge", "HighlightedLabel", "IconLabel", "createMatches", "FuzzyScore", "Disposable", "basename", "dirname", "ITextModelService", "localize", "IInstantiationService", "IKeybindingService", "ILabelService", "defaultCountBadgeStyles", "FileReferences", "OneReference", "ReferencesModel", "DataSource", "constructor", "_resolverService", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "element", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "groups", "resolve", "then", "val", "children", "Error", "Delegate", "getHeight", "getTemplateId", "FileReferences<PERSON><PERSON><PERSON>", "id", "OneReferenceRenderer", "StringRepresentationProvider", "_keybindingService", "getKeyboardNavigationLabel", "parts", "parent", "getPreview", "preview", "range", "value", "uri", "IdentityProvider", "getId", "FileReferencesTemplate", "container", "_labelService", "document", "createElement", "classList", "add", "file", "_register", "supportHighlights", "badge", "append", "$", "append<PERSON><PERSON><PERSON>", "set", "matches", "<PERSON><PERSON><PERSON><PERSON>", "getUriBasenameLabel", "getUriLabel", "relative", "title", "len", "setCount", "setTitleFormat", "_instantiationService", "templateId", "renderTemplate", "createInstance", "renderElement", "node", "index", "template", "filterData", "disposeTemplate", "templateData", "dispose", "OneReferenceTemplate", "label", "score", "startLineNumber", "startColumn", "highlight", "isDefault", "toggle", "AccessibilityProvider", "getWidgetAriaLabel", "getAriaLabel", "ariaMessage"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/contrib/gotoSymbol/browser/peek/referencesTree.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = (this && this.__param) || function (paramIndex, decorator) {\n    return function (target, key) { decorator(target, key, paramIndex); }\n};\nvar FileReferencesRenderer_1;\nimport * as dom from '../../../../../base/browser/dom.js';\nimport { CountBadge } from '../../../../../base/browser/ui/countBadge/countBadge.js';\nimport { HighlightedLabel } from '../../../../../base/browser/ui/highlightedlabel/highlightedLabel.js';\nimport { IconLabel } from '../../../../../base/browser/ui/iconLabel/iconLabel.js';\nimport { createMatches, FuzzyScore } from '../../../../../base/common/filters.js';\nimport { Disposable } from '../../../../../base/common/lifecycle.js';\nimport { basename, dirname } from '../../../../../base/common/resources.js';\nimport { ITextModelService } from '../../../../common/services/resolverService.js';\nimport { localize } from '../../../../../nls.js';\nimport { IInstantiationService } from '../../../../../platform/instantiation/common/instantiation.js';\nimport { IKeybindingService } from '../../../../../platform/keybinding/common/keybinding.js';\nimport { ILabelService } from '../../../../../platform/label/common/label.js';\nimport { defaultCountBadgeStyles } from '../../../../../platform/theme/browser/defaultStyles.js';\nimport { FileReferences, OneReference, ReferencesModel } from '../referencesModel.js';\nlet DataSource = class DataSource {\n    constructor(_resolverService) {\n        this._resolverService = _resolverService;\n    }\n    hasChildren(element) {\n        if (element instanceof ReferencesModel) {\n            return true;\n        }\n        if (element instanceof FileReferences) {\n            return true;\n        }\n        return false;\n    }\n    getChildren(element) {\n        if (element instanceof ReferencesModel) {\n            return element.groups;\n        }\n        if (element instanceof FileReferences) {\n            return element.resolve(this._resolverService).then(val => {\n                // if (element.failure) {\n                // \t// refresh the element on failure so that\n                // \t// we can update its rendering\n                // \treturn tree.refresh(element).then(() => val.children);\n                // }\n                return val.children;\n            });\n        }\n        throw new Error('bad tree');\n    }\n};\nDataSource = __decorate([\n    __param(0, ITextModelService)\n], DataSource);\nexport { DataSource };\n//#endregion\nexport class Delegate {\n    getHeight() {\n        return 23;\n    }\n    getTemplateId(element) {\n        if (element instanceof FileReferences) {\n            return FileReferencesRenderer.id;\n        }\n        else {\n            return OneReferenceRenderer.id;\n        }\n    }\n}\nlet StringRepresentationProvider = class StringRepresentationProvider {\n    constructor(_keybindingService) {\n        this._keybindingService = _keybindingService;\n    }\n    getKeyboardNavigationLabel(element) {\n        if (element instanceof OneReference) {\n            const parts = element.parent.getPreview(element)?.preview(element.range);\n            if (parts) {\n                return parts.value;\n            }\n        }\n        // FileReferences or unresolved OneReference\n        return basename(element.uri);\n    }\n};\nStringRepresentationProvider = __decorate([\n    __param(0, IKeybindingService)\n], StringRepresentationProvider);\nexport { StringRepresentationProvider };\nexport class IdentityProvider {\n    getId(element) {\n        return element instanceof OneReference ? element.id : element.uri;\n    }\n}\n//#region render: File\nlet FileReferencesTemplate = class FileReferencesTemplate extends Disposable {\n    constructor(container, _labelService) {\n        super();\n        this._labelService = _labelService;\n        const parent = document.createElement('div');\n        parent.classList.add('reference-file');\n        this.file = this._register(new IconLabel(parent, { supportHighlights: true }));\n        this.badge = new CountBadge(dom.append(parent, dom.$('.count')), {}, defaultCountBadgeStyles);\n        container.appendChild(parent);\n    }\n    set(element, matches) {\n        const parent = dirname(element.uri);\n        this.file.setLabel(this._labelService.getUriBasenameLabel(element.uri), this._labelService.getUriLabel(parent, { relative: true }), { title: this._labelService.getUriLabel(element.uri), matches });\n        const len = element.children.length;\n        this.badge.setCount(len);\n        if (len > 1) {\n            this.badge.setTitleFormat(localize('referencesCount', \"{0} references\", len));\n        }\n        else {\n            this.badge.setTitleFormat(localize('referenceCount', \"{0} reference\", len));\n        }\n    }\n};\nFileReferencesTemplate = __decorate([\n    __param(1, ILabelService)\n], FileReferencesTemplate);\nlet FileReferencesRenderer = class FileReferencesRenderer {\n    static { FileReferencesRenderer_1 = this; }\n    static { this.id = 'FileReferencesRenderer'; }\n    constructor(_instantiationService) {\n        this._instantiationService = _instantiationService;\n        this.templateId = FileReferencesRenderer_1.id;\n    }\n    renderTemplate(container) {\n        return this._instantiationService.createInstance(FileReferencesTemplate, container);\n    }\n    renderElement(node, index, template) {\n        template.set(node.element, createMatches(node.filterData));\n    }\n    disposeTemplate(templateData) {\n        templateData.dispose();\n    }\n};\nFileReferencesRenderer = FileReferencesRenderer_1 = __decorate([\n    __param(0, IInstantiationService)\n], FileReferencesRenderer);\nexport { FileReferencesRenderer };\n//#endregion\n//#region render: Reference\nclass OneReferenceTemplate extends Disposable {\n    constructor(container) {\n        super();\n        this.label = this._register(new HighlightedLabel(container));\n    }\n    set(element, score) {\n        const preview = element.parent.getPreview(element)?.preview(element.range);\n        if (!preview || !preview.value) {\n            // this means we FAILED to resolve the document or the value is the empty string\n            this.label.set(`${basename(element.uri)}:${element.range.startLineNumber + 1}:${element.range.startColumn + 1}`);\n        }\n        else {\n            // render search match as highlight unless\n            // we have score, then render the score\n            const { value, highlight } = preview;\n            if (score && !FuzzyScore.isDefault(score)) {\n                this.label.element.classList.toggle('referenceMatch', false);\n                this.label.set(value, createMatches(score));\n            }\n            else {\n                this.label.element.classList.toggle('referenceMatch', true);\n                this.label.set(value, [highlight]);\n            }\n        }\n    }\n}\nexport class OneReferenceRenderer {\n    constructor() {\n        this.templateId = OneReferenceRenderer.id;\n    }\n    static { this.id = 'OneReferenceRenderer'; }\n    renderTemplate(container) {\n        return new OneReferenceTemplate(container);\n    }\n    renderElement(node, index, templateData) {\n        templateData.set(node.element, node.filterData);\n    }\n    disposeTemplate(templateData) {\n        templateData.dispose();\n    }\n}\n//#endregion\nexport class AccessibilityProvider {\n    getWidgetAriaLabel() {\n        return localize('treeAriaLabel', \"References\");\n    }\n    getAriaLabel(element) {\n        return element.ariaMessage;\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,IAAIA,UAAU,GAAI,IAAI,IAAI,IAAI,CAACA,UAAU,IAAK,UAAUC,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAE;EACnF,IAAIC,CAAC,GAAGC,SAAS,CAACC,MAAM;IAAEC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGH,MAAM,GAAGE,IAAI,KAAK,IAAI,GAAGA,IAAI,GAAGK,MAAM,CAACC,wBAAwB,CAACR,MAAM,EAAEC,GAAG,CAAC,GAAGC,IAAI;IAAEO,CAAC;EAC5H,IAAI,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,CAACC,QAAQ,KAAK,UAAU,EAAEL,CAAC,GAAGI,OAAO,CAACC,QAAQ,CAACZ,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,CAAC,CAAC,KAC1H,KAAK,IAAIU,CAAC,GAAGb,UAAU,CAACM,MAAM,GAAG,CAAC,EAAEO,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE,IAAIH,CAAC,GAAGV,UAAU,CAACa,CAAC,CAAC,EAAEN,CAAC,GAAG,CAACH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACH,CAAC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACT,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,GAAGG,CAAC,CAACT,MAAM,EAAEC,GAAG,CAAC,KAAKK,CAAC;EACjJ,OAAOH,CAAC,GAAG,CAAC,IAAIG,CAAC,IAAIC,MAAM,CAACM,cAAc,CAACb,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,EAAEA,CAAC;AACjE,CAAC;AACD,IAAIQ,OAAO,GAAI,IAAI,IAAI,IAAI,CAACA,OAAO,IAAK,UAAUC,UAAU,EAAEC,SAAS,EAAE;EACrE,OAAO,UAAUhB,MAAM,EAAEC,GAAG,EAAE;IAAEe,SAAS,CAAChB,MAAM,EAAEC,GAAG,EAAEc,UAAU,CAAC;EAAE,CAAC;AACzE,CAAC;AACD,IAAIE,wBAAwB;AAC5B,OAAO,KAAKC,GAAG,MAAM,oCAAoC;AACzD,SAASC,UAAU,QAAQ,yDAAyD;AACpF,SAASC,gBAAgB,QAAQ,qEAAqE;AACtG,SAASC,SAAS,QAAQ,uDAAuD;AACjF,SAASC,aAAa,EAAEC,UAAU,QAAQ,uCAAuC;AACjF,SAASC,UAAU,QAAQ,yCAAyC;AACpE,SAASC,QAAQ,EAAEC,OAAO,QAAQ,yCAAyC;AAC3E,SAASC,iBAAiB,QAAQ,gDAAgD;AAClF,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,qBAAqB,QAAQ,+DAA+D;AACrG,SAASC,kBAAkB,QAAQ,yDAAyD;AAC5F,SAASC,aAAa,QAAQ,+CAA+C;AAC7E,SAASC,uBAAuB,QAAQ,wDAAwD;AAChG,SAASC,cAAc,EAAEC,YAAY,EAAEC,eAAe,QAAQ,uBAAuB;AACrF,IAAIC,UAAU,GAAG,MAAMA,UAAU,CAAC;EAC9BC,WAAWA,CAACC,gBAAgB,EAAE;IAC1B,IAAI,CAACA,gBAAgB,GAAGA,gBAAgB;EAC5C;EACAC,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAIA,OAAO,YAAYL,eAAe,EAAE;MACpC,OAAO,IAAI;IACf;IACA,IAAIK,OAAO,YAAYP,cAAc,EAAE;MACnC,OAAO,IAAI;IACf;IACA,OAAO,KAAK;EAChB;EACAQ,WAAWA,CAACD,OAAO,EAAE;IACjB,IAAIA,OAAO,YAAYL,eAAe,EAAE;MACpC,OAAOK,OAAO,CAACE,MAAM;IACzB;IACA,IAAIF,OAAO,YAAYP,cAAc,EAAE;MACnC,OAAOO,OAAO,CAACG,OAAO,CAAC,IAAI,CAACL,gBAAgB,CAAC,CAACM,IAAI,CAACC,GAAG,IAAI;QACtD;QACA;QACA;QACA;QACA;QACA,OAAOA,GAAG,CAACC,QAAQ;MACvB,CAAC,CAAC;IACN;IACA,MAAM,IAAIC,KAAK,CAAC,UAAU,CAAC;EAC/B;AACJ,CAAC;AACDX,UAAU,GAAGtC,UAAU,CAAC,CACpBgB,OAAO,CAAC,CAAC,EAAEa,iBAAiB,CAAC,CAChC,EAAES,UAAU,CAAC;AACd,SAASA,UAAU;AACnB;AACA,OAAO,MAAMY,QAAQ,CAAC;EAClBC,SAASA,CAAA,EAAG;IACR,OAAO,EAAE;EACb;EACAC,aAAaA,CAACV,OAAO,EAAE;IACnB,IAAIA,OAAO,YAAYP,cAAc,EAAE;MACnC,OAAOkB,sBAAsB,CAACC,EAAE;IACpC,CAAC,MACI;MACD,OAAOC,oBAAoB,CAACD,EAAE;IAClC;EACJ;AACJ;AACA,IAAIE,4BAA4B,GAAG,MAAMA,4BAA4B,CAAC;EAClEjB,WAAWA,CAACkB,kBAAkB,EAAE;IAC5B,IAAI,CAACA,kBAAkB,GAAGA,kBAAkB;EAChD;EACAC,0BAA0BA,CAAChB,OAAO,EAAE;IAChC,IAAIA,OAAO,YAAYN,YAAY,EAAE;MACjC,MAAMuB,KAAK,GAAGjB,OAAO,CAACkB,MAAM,CAACC,UAAU,CAACnB,OAAO,CAAC,EAAEoB,OAAO,CAACpB,OAAO,CAACqB,KAAK,CAAC;MACxE,IAAIJ,KAAK,EAAE;QACP,OAAOA,KAAK,CAACK,KAAK;MACtB;IACJ;IACA;IACA,OAAOrC,QAAQ,CAACe,OAAO,CAACuB,GAAG,CAAC;EAChC;AACJ,CAAC;AACDT,4BAA4B,GAAGxD,UAAU,CAAC,CACtCgB,OAAO,CAAC,CAAC,EAAEgB,kBAAkB,CAAC,CACjC,EAAEwB,4BAA4B,CAAC;AAChC,SAASA,4BAA4B;AACrC,OAAO,MAAMU,gBAAgB,CAAC;EAC1BC,KAAKA,CAACzB,OAAO,EAAE;IACX,OAAOA,OAAO,YAAYN,YAAY,GAAGM,OAAO,CAACY,EAAE,GAAGZ,OAAO,CAACuB,GAAG;EACrE;AACJ;AACA;AACA,IAAIG,sBAAsB,GAAG,MAAMA,sBAAsB,SAAS1C,UAAU,CAAC;EACzEa,WAAWA,CAAC8B,SAAS,EAAEC,aAAa,EAAE;IAClC,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,aAAa,GAAGA,aAAa;IAClC,MAAMV,MAAM,GAAGW,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC5CZ,MAAM,CAACa,SAAS,CAACC,GAAG,CAAC,gBAAgB,CAAC;IACtC,IAAI,CAACC,IAAI,GAAG,IAAI,CAACC,SAAS,CAAC,IAAIrD,SAAS,CAACqC,MAAM,EAAE;MAAEiB,iBAAiB,EAAE;IAAK,CAAC,CAAC,CAAC;IAC9E,IAAI,CAACC,KAAK,GAAG,IAAIzD,UAAU,CAACD,GAAG,CAAC2D,MAAM,CAACnB,MAAM,EAAExC,GAAG,CAAC4D,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE9C,uBAAuB,CAAC;IAC7FmC,SAAS,CAACY,WAAW,CAACrB,MAAM,CAAC;EACjC;EACAsB,GAAGA,CAACxC,OAAO,EAAEyC,OAAO,EAAE;IAClB,MAAMvB,MAAM,GAAGhC,OAAO,CAACc,OAAO,CAACuB,GAAG,CAAC;IACnC,IAAI,CAACU,IAAI,CAACS,QAAQ,CAAC,IAAI,CAACd,aAAa,CAACe,mBAAmB,CAAC3C,OAAO,CAACuB,GAAG,CAAC,EAAE,IAAI,CAACK,aAAa,CAACgB,WAAW,CAAC1B,MAAM,EAAE;MAAE2B,QAAQ,EAAE;IAAK,CAAC,CAAC,EAAE;MAAEC,KAAK,EAAE,IAAI,CAAClB,aAAa,CAACgB,WAAW,CAAC5C,OAAO,CAACuB,GAAG,CAAC;MAAEkB;IAAQ,CAAC,CAAC;IACpM,MAAMM,GAAG,GAAG/C,OAAO,CAACM,QAAQ,CAACzC,MAAM;IACnC,IAAI,CAACuE,KAAK,CAACY,QAAQ,CAACD,GAAG,CAAC;IACxB,IAAIA,GAAG,GAAG,CAAC,EAAE;MACT,IAAI,CAACX,KAAK,CAACa,cAAc,CAAC7D,QAAQ,CAAC,iBAAiB,EAAE,gBAAgB,EAAE2D,GAAG,CAAC,CAAC;IACjF,CAAC,MACI;MACD,IAAI,CAACX,KAAK,CAACa,cAAc,CAAC7D,QAAQ,CAAC,gBAAgB,EAAE,eAAe,EAAE2D,GAAG,CAAC,CAAC;IAC/E;EACJ;AACJ,CAAC;AACDrB,sBAAsB,GAAGpE,UAAU,CAAC,CAChCgB,OAAO,CAAC,CAAC,EAAEiB,aAAa,CAAC,CAC5B,EAAEmC,sBAAsB,CAAC;AAC1B,IAAIf,sBAAsB;EAAA,IAAtBA,sBAAsB,GAAG,MAAMA,sBAAsB,CAAC;IACtD;MAASlC,wBAAwB,GAAG,IAAI;IAAE;IAC1C;MAAS,IAAI,CAACmC,EAAE,GAAG,wBAAwB;IAAE;IAC7Cf,WAAWA,CAACqD,qBAAqB,EAAE;MAC/B,IAAI,CAACA,qBAAqB,GAAGA,qBAAqB;MAClD,IAAI,CAACC,UAAU,GAAG1E,wBAAwB,CAACmC,EAAE;IACjD;IACAwC,cAAcA,CAACzB,SAAS,EAAE;MACtB,OAAO,IAAI,CAACuB,qBAAqB,CAACG,cAAc,CAAC3B,sBAAsB,EAAEC,SAAS,CAAC;IACvF;IACA2B,aAAaA,CAACC,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAE;MACjCA,QAAQ,CAACjB,GAAG,CAACe,IAAI,CAACvD,OAAO,EAAElB,aAAa,CAACyE,IAAI,CAACG,UAAU,CAAC,CAAC;IAC9D;IACAC,eAAeA,CAACC,YAAY,EAAE;MAC1BA,YAAY,CAACC,OAAO,CAAC,CAAC;IAC1B;EACJ,CAAC;EAAA,OAhBGlD,sBAAsB;AAAA,IAgBzB;AACDA,sBAAsB,GAAGlC,wBAAwB,GAAGnB,UAAU,CAAC,CAC3DgB,OAAO,CAAC,CAAC,EAAEe,qBAAqB,CAAC,CACpC,EAAEsB,sBAAsB,CAAC;AAC1B,SAASA,sBAAsB;AAC/B;AACA;AACA,MAAMmD,oBAAoB,SAAS9E,UAAU,CAAC;EAC1Ca,WAAWA,CAAC8B,SAAS,EAAE;IACnB,KAAK,CAAC,CAAC;IACP,IAAI,CAACoC,KAAK,GAAG,IAAI,CAAC7B,SAAS,CAAC,IAAItD,gBAAgB,CAAC+C,SAAS,CAAC,CAAC;EAChE;EACAa,GAAGA,CAACxC,OAAO,EAAEgE,KAAK,EAAE;IAChB,MAAM5C,OAAO,GAAGpB,OAAO,CAACkB,MAAM,CAACC,UAAU,CAACnB,OAAO,CAAC,EAAEoB,OAAO,CAACpB,OAAO,CAACqB,KAAK,CAAC;IAC1E,IAAI,CAACD,OAAO,IAAI,CAACA,OAAO,CAACE,KAAK,EAAE;MAC5B;MACA,IAAI,CAACyC,KAAK,CAACvB,GAAG,CAAC,GAAGvD,QAAQ,CAACe,OAAO,CAACuB,GAAG,CAAC,IAAIvB,OAAO,CAACqB,KAAK,CAAC4C,eAAe,GAAG,CAAC,IAAIjE,OAAO,CAACqB,KAAK,CAAC6C,WAAW,GAAG,CAAC,EAAE,CAAC;IACpH,CAAC,MACI;MACD;MACA;MACA,MAAM;QAAE5C,KAAK;QAAE6C;MAAU,CAAC,GAAG/C,OAAO;MACpC,IAAI4C,KAAK,IAAI,CAACjF,UAAU,CAACqF,SAAS,CAACJ,KAAK,CAAC,EAAE;QACvC,IAAI,CAACD,KAAK,CAAC/D,OAAO,CAAC+B,SAAS,CAACsC,MAAM,CAAC,gBAAgB,EAAE,KAAK,CAAC;QAC5D,IAAI,CAACN,KAAK,CAACvB,GAAG,CAAClB,KAAK,EAAExC,aAAa,CAACkF,KAAK,CAAC,CAAC;MAC/C,CAAC,MACI;QACD,IAAI,CAACD,KAAK,CAAC/D,OAAO,CAAC+B,SAAS,CAACsC,MAAM,CAAC,gBAAgB,EAAE,IAAI,CAAC;QAC3D,IAAI,CAACN,KAAK,CAACvB,GAAG,CAAClB,KAAK,EAAE,CAAC6C,SAAS,CAAC,CAAC;MACtC;IACJ;EACJ;AACJ;AACA,WAAatD,oBAAoB;EAA1B,MAAMA,oBAAoB,CAAC;IAC9BhB,WAAWA,CAAA,EAAG;MACV,IAAI,CAACsD,UAAU,GAAGtC,oBAAoB,CAACD,EAAE;IAC7C;IACA;MAAS,IAAI,CAACA,EAAE,GAAG,sBAAsB;IAAE;IAC3CwC,cAAcA,CAACzB,SAAS,EAAE;MACtB,OAAO,IAAImC,oBAAoB,CAACnC,SAAS,CAAC;IAC9C;IACA2B,aAAaA,CAACC,IAAI,EAAEC,KAAK,EAAEI,YAAY,EAAE;MACrCA,YAAY,CAACpB,GAAG,CAACe,IAAI,CAACvD,OAAO,EAAEuD,IAAI,CAACG,UAAU,CAAC;IACnD;IACAC,eAAeA,CAACC,YAAY,EAAE;MAC1BA,YAAY,CAACC,OAAO,CAAC,CAAC;IAC1B;EACJ;EAAC,OAdYhD,oBAAoB;AAAA;AAejC;AACA,OAAO,MAAMyD,qBAAqB,CAAC;EAC/BC,kBAAkBA,CAAA,EAAG;IACjB,OAAOnF,QAAQ,CAAC,eAAe,EAAE,YAAY,CAAC;EAClD;EACAoF,YAAYA,CAACxE,OAAO,EAAE;IAClB,OAAOA,OAAO,CAACyE,WAAW;EAC9B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}