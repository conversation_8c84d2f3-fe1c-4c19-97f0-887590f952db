{"ast": null, "code": "export default function (end) {\n  var start = this,\n    ancestor = leastCommonAncestor(start, end),\n    nodes = [start];\n  while (start !== ancestor) {\n    start = start.parent;\n    nodes.push(start);\n  }\n  var k = nodes.length;\n  while (end !== ancestor) {\n    nodes.splice(k, 0, end);\n    end = end.parent;\n  }\n  return nodes;\n}\nfunction leastCommonAncestor(a, b) {\n  if (a === b) return a;\n  var aNodes = a.ancestors(),\n    bNodes = b.ancestors(),\n    c = null;\n  a = aNodes.pop();\n  b = bNodes.pop();\n  while (a === b) {\n    c = a;\n    a = aNodes.pop();\n    b = bNodes.pop();\n  }\n  return c;\n}", "map": {"version": 3, "names": ["end", "start", "ancestor", "leastCommonAncestor", "nodes", "parent", "push", "k", "length", "splice", "a", "b", "aNodes", "ancestors", "bNodes", "c", "pop"], "sources": ["C:/console/aava-ui-web/node_modules/d3-hierarchy/src/hierarchy/path.js"], "sourcesContent": ["export default function(end) {\n  var start = this,\n      ancestor = leastCommonAncestor(start, end),\n      nodes = [start];\n  while (start !== ancestor) {\n    start = start.parent;\n    nodes.push(start);\n  }\n  var k = nodes.length;\n  while (end !== ancestor) {\n    nodes.splice(k, 0, end);\n    end = end.parent;\n  }\n  return nodes;\n}\n\nfunction leastCommonAncestor(a, b) {\n  if (a === b) return a;\n  var aNodes = a.ancestors(),\n      bNodes = b.ancestors(),\n      c = null;\n  a = aNodes.pop();\n  b = bNodes.pop();\n  while (a === b) {\n    c = a;\n    a = aNodes.pop();\n    b = bNodes.pop();\n  }\n  return c;\n}\n"], "mappings": "AAAA,eAAe,UAASA,GAAG,EAAE;EAC3B,IAAIC,KAAK,GAAG,IAAI;IACZC,QAAQ,GAAGC,mBAAmB,CAACF,KAAK,EAAED,GAAG,CAAC;IAC1CI,KAAK,GAAG,CAACH,KAAK,CAAC;EACnB,OAAOA,KAAK,KAAKC,QAAQ,EAAE;IACzBD,KAAK,GAAGA,KAAK,CAACI,MAAM;IACpBD,KAAK,CAACE,IAAI,CAACL,KAAK,CAAC;EACnB;EACA,IAAIM,CAAC,GAAGH,KAAK,CAACI,MAAM;EACpB,OAAOR,GAAG,KAAKE,QAAQ,EAAE;IACvBE,KAAK,CAACK,MAAM,CAACF,CAAC,EAAE,CAAC,EAAEP,GAAG,CAAC;IACvBA,GAAG,GAAGA,GAAG,CAACK,MAAM;EAClB;EACA,OAAOD,KAAK;AACd;AAEA,SAASD,mBAAmBA,CAACO,CAAC,EAAEC,CAAC,EAAE;EACjC,IAAID,CAAC,KAAKC,CAAC,EAAE,OAAOD,CAAC;EACrB,IAAIE,MAAM,GAAGF,CAAC,CAACG,SAAS,CAAC,CAAC;IACtBC,MAAM,GAAGH,CAAC,CAACE,SAAS,CAAC,CAAC;IACtBE,CAAC,GAAG,IAAI;EACZL,CAAC,GAAGE,MAAM,CAACI,GAAG,CAAC,CAAC;EAChBL,CAAC,GAAGG,MAAM,CAACE,GAAG,CAAC,CAAC;EAChB,OAAON,CAAC,KAAKC,CAAC,EAAE;IACdI,CAAC,GAAGL,CAAC;IACLA,CAAC,GAAGE,MAAM,CAACI,GAAG,CAAC,CAAC;IAChBL,CAAC,GAAGG,MAAM,CAACE,GAAG,CAAC,CAAC;EAClB;EACA,OAAOD,CAAC;AACV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}