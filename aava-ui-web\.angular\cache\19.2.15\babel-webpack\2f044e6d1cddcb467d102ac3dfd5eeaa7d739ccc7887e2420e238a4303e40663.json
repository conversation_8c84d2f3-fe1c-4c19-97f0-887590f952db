{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { NotSupportedError } from '../../../../../base/common/errors.js';\nimport { TokenMetadata } from '../../../encodedTokenAttributes.js';\nimport { TextAstNode } from './ast.js';\nimport { lengthAdd, lengthDiff, lengthGetColumnCountIfZeroLineCount, lengthToObj, lengthZero, toLength } from './length.js';\nimport { SmallImmutableSet } from './smallImmutableSet.js';\nexport class Token {\n  constructor(length, kind,\n  /**\n   * If this token is an opening bracket, this is the id of the opening bracket.\n   * If this token is a closing bracket, this is the id of the first opening bracket that is closed by this bracket.\n   * Otherwise, it is -1.\n   */\n  bracketId,\n  /**\n   * If this token is an opening bracket, this just contains `bracketId`.\n   * If this token is a closing bracket, this lists all opening bracket ids, that it closes.\n   * Otherwise, it is empty.\n   */\n  bracketIds, astNode) {\n    this.length = length;\n    this.kind = kind;\n    this.bracketId = bracketId;\n    this.bracketIds = bracketIds;\n    this.astNode = astNode;\n  }\n}\nexport class TextBufferTokenizer {\n  constructor(textModel, bracketTokens) {\n    this.textModel = textModel;\n    this.bracketTokens = bracketTokens;\n    this.reader = new NonPeekableTextBufferTokenizer(this.textModel, this.bracketTokens);\n    this._offset = lengthZero;\n    this.didPeek = false;\n    this.peeked = null;\n    this.textBufferLineCount = textModel.getLineCount();\n    this.textBufferLastLineLength = textModel.getLineLength(this.textBufferLineCount);\n  }\n  get offset() {\n    return this._offset;\n  }\n  get length() {\n    return toLength(this.textBufferLineCount - 1, this.textBufferLastLineLength);\n  }\n  skip(length) {\n    this.didPeek = false;\n    this._offset = lengthAdd(this._offset, length);\n    const obj = lengthToObj(this._offset);\n    this.reader.setPosition(obj.lineCount, obj.columnCount);\n  }\n  read() {\n    let token;\n    if (this.peeked) {\n      this.didPeek = false;\n      token = this.peeked;\n    } else {\n      token = this.reader.read();\n    }\n    if (token) {\n      this._offset = lengthAdd(this._offset, token.length);\n    }\n    return token;\n  }\n  peek() {\n    if (!this.didPeek) {\n      this.peeked = this.reader.read();\n      this.didPeek = true;\n    }\n    return this.peeked;\n  }\n}\n/**\n * Does not support peek.\n*/\nclass NonPeekableTextBufferTokenizer {\n  constructor(textModel, bracketTokens) {\n    this.textModel = textModel;\n    this.bracketTokens = bracketTokens;\n    this.lineIdx = 0;\n    this.line = null;\n    this.lineCharOffset = 0;\n    this.lineTokens = null;\n    this.lineTokenOffset = 0;\n    /** Must be a zero line token. The end of the document cannot be peeked. */\n    this.peekedToken = null;\n    this.textBufferLineCount = textModel.getLineCount();\n    this.textBufferLastLineLength = textModel.getLineLength(this.textBufferLineCount);\n  }\n  setPosition(lineIdx, column) {\n    // We must not jump into a token!\n    if (lineIdx === this.lineIdx) {\n      this.lineCharOffset = column;\n      if (this.line !== null) {\n        this.lineTokenOffset = this.lineCharOffset === 0 ? 0 : this.lineTokens.findTokenIndexAtOffset(this.lineCharOffset);\n      }\n    } else {\n      this.lineIdx = lineIdx;\n      this.lineCharOffset = column;\n      this.line = null;\n    }\n    this.peekedToken = null;\n  }\n  read() {\n    if (this.peekedToken) {\n      const token = this.peekedToken;\n      this.peekedToken = null;\n      this.lineCharOffset += lengthGetColumnCountIfZeroLineCount(token.length);\n      return token;\n    }\n    if (this.lineIdx > this.textBufferLineCount - 1 || this.lineIdx === this.textBufferLineCount - 1 && this.lineCharOffset >= this.textBufferLastLineLength) {\n      // We are after the end\n      return null;\n    }\n    if (this.line === null) {\n      this.lineTokens = this.textModel.tokenization.getLineTokens(this.lineIdx + 1);\n      this.line = this.lineTokens.getLineContent();\n      this.lineTokenOffset = this.lineCharOffset === 0 ? 0 : this.lineTokens.findTokenIndexAtOffset(this.lineCharOffset);\n    }\n    const startLineIdx = this.lineIdx;\n    const startLineCharOffset = this.lineCharOffset;\n    // limits the length of text tokens.\n    // If text tokens get too long, incremental updates will be slow\n    let lengthHeuristic = 0;\n    while (true) {\n      const lineTokens = this.lineTokens;\n      const tokenCount = lineTokens.getCount();\n      let peekedBracketToken = null;\n      if (this.lineTokenOffset < tokenCount) {\n        const tokenMetadata = lineTokens.getMetadata(this.lineTokenOffset);\n        while (this.lineTokenOffset + 1 < tokenCount && tokenMetadata === lineTokens.getMetadata(this.lineTokenOffset + 1)) {\n          // Skip tokens that are identical.\n          // Sometimes, (bracket) identifiers are split up into multiple tokens.\n          this.lineTokenOffset++;\n        }\n        const isOther = TokenMetadata.getTokenType(tokenMetadata) === 0 /* StandardTokenType.Other */;\n        const containsBracketType = TokenMetadata.containsBalancedBrackets(tokenMetadata);\n        const endOffset = lineTokens.getEndOffset(this.lineTokenOffset);\n        // Is there a bracket token next? Only consume text.\n        if (containsBracketType && isOther && this.lineCharOffset < endOffset) {\n          const languageId = lineTokens.getLanguageId(this.lineTokenOffset);\n          const text = this.line.substring(this.lineCharOffset, endOffset);\n          const brackets = this.bracketTokens.getSingleLanguageBracketTokens(languageId);\n          const regexp = brackets.regExpGlobal;\n          if (regexp) {\n            regexp.lastIndex = 0;\n            const match = regexp.exec(text);\n            if (match) {\n              peekedBracketToken = brackets.getToken(match[0]);\n              if (peekedBracketToken) {\n                // Consume leading text of the token\n                this.lineCharOffset += match.index;\n              }\n            }\n          }\n        }\n        lengthHeuristic += endOffset - this.lineCharOffset;\n        if (peekedBracketToken) {\n          // Don't skip the entire token, as a single token could contain multiple brackets.\n          if (startLineIdx !== this.lineIdx || startLineCharOffset !== this.lineCharOffset) {\n            // There is text before the bracket\n            this.peekedToken = peekedBracketToken;\n            break;\n          } else {\n            // Consume the peeked token\n            this.lineCharOffset += lengthGetColumnCountIfZeroLineCount(peekedBracketToken.length);\n            return peekedBracketToken;\n          }\n        } else {\n          // Skip the entire token, as the token contains no brackets at all.\n          this.lineTokenOffset++;\n          this.lineCharOffset = endOffset;\n        }\n      } else {\n        if (this.lineIdx === this.textBufferLineCount - 1) {\n          break;\n        }\n        this.lineIdx++;\n        this.lineTokens = this.textModel.tokenization.getLineTokens(this.lineIdx + 1);\n        this.lineTokenOffset = 0;\n        this.line = this.lineTokens.getLineContent();\n        this.lineCharOffset = 0;\n        lengthHeuristic += 33; // max 1000/33 = 30 lines\n        // This limits the amount of work to recompute min-indentation\n        if (lengthHeuristic > 1000) {\n          // only break (automatically) at the end of line.\n          break;\n        }\n      }\n      if (lengthHeuristic > 1500) {\n        // Eventually break regardless of the line length so that\n        // very long lines do not cause bad performance.\n        // This effective limits max indentation to 500, as\n        // indentation is not computed across multiple text nodes.\n        break;\n      }\n    }\n    // If a token contains some proper indentation, it also contains \\n{INDENTATION+}(?!{INDENTATION}),\n    // unless the line is too long.\n    // Thus, the min indentation of the document is the minimum min indentation of every text node.\n    const length = lengthDiff(startLineIdx, startLineCharOffset, this.lineIdx, this.lineCharOffset);\n    return new Token(length, 0 /* TokenKind.Text */, -1, SmallImmutableSet.getEmpty(), new TextAstNode(length));\n  }\n}\nexport class FastTokenizer {\n  constructor(text, brackets) {\n    this.text = text;\n    this._offset = lengthZero;\n    this.idx = 0;\n    const regExpStr = brackets.getRegExpStr();\n    const regexp = regExpStr ? new RegExp(regExpStr + '|\\n', 'gi') : null;\n    const tokens = [];\n    let match;\n    let curLineCount = 0;\n    let lastLineBreakOffset = 0;\n    let lastTokenEndOffset = 0;\n    let lastTokenEndLine = 0;\n    const smallTextTokens0Line = [];\n    for (let i = 0; i < 60; i++) {\n      smallTextTokens0Line.push(new Token(toLength(0, i), 0 /* TokenKind.Text */, -1, SmallImmutableSet.getEmpty(), new TextAstNode(toLength(0, i))));\n    }\n    const smallTextTokens1Line = [];\n    for (let i = 0; i < 60; i++) {\n      smallTextTokens1Line.push(new Token(toLength(1, i), 0 /* TokenKind.Text */, -1, SmallImmutableSet.getEmpty(), new TextAstNode(toLength(1, i))));\n    }\n    if (regexp) {\n      regexp.lastIndex = 0;\n      // If a token contains indentation, it also contains \\n{INDENTATION+}(?!{INDENTATION})\n      while ((match = regexp.exec(text)) !== null) {\n        const curOffset = match.index;\n        const value = match[0];\n        if (value === '\\n') {\n          curLineCount++;\n          lastLineBreakOffset = curOffset + 1;\n        } else {\n          if (lastTokenEndOffset !== curOffset) {\n            let token;\n            if (lastTokenEndLine === curLineCount) {\n              const colCount = curOffset - lastTokenEndOffset;\n              if (colCount < smallTextTokens0Line.length) {\n                token = smallTextTokens0Line[colCount];\n              } else {\n                const length = toLength(0, colCount);\n                token = new Token(length, 0 /* TokenKind.Text */, -1, SmallImmutableSet.getEmpty(), new TextAstNode(length));\n              }\n            } else {\n              const lineCount = curLineCount - lastTokenEndLine;\n              const colCount = curOffset - lastLineBreakOffset;\n              if (lineCount === 1 && colCount < smallTextTokens1Line.length) {\n                token = smallTextTokens1Line[colCount];\n              } else {\n                const length = toLength(lineCount, colCount);\n                token = new Token(length, 0 /* TokenKind.Text */, -1, SmallImmutableSet.getEmpty(), new TextAstNode(length));\n              }\n            }\n            tokens.push(token);\n          }\n          // value is matched by regexp, so the token must exist\n          tokens.push(brackets.getToken(value));\n          lastTokenEndOffset = curOffset + value.length;\n          lastTokenEndLine = curLineCount;\n        }\n      }\n    }\n    const offset = text.length;\n    if (lastTokenEndOffset !== offset) {\n      const length = lastTokenEndLine === curLineCount ? toLength(0, offset - lastTokenEndOffset) : toLength(curLineCount - lastTokenEndLine, offset - lastLineBreakOffset);\n      tokens.push(new Token(length, 0 /* TokenKind.Text */, -1, SmallImmutableSet.getEmpty(), new TextAstNode(length)));\n    }\n    this.length = toLength(curLineCount, offset - lastLineBreakOffset);\n    this.tokens = tokens;\n  }\n  get offset() {\n    return this._offset;\n  }\n  read() {\n    return this.tokens[this.idx++] || null;\n  }\n  peek() {\n    return this.tokens[this.idx] || null;\n  }\n  skip(length) {\n    throw new NotSupportedError();\n  }\n}", "map": {"version": 3, "names": ["NotSupportedError", "TokenMetadata", "TextAstNode", "lengthAdd", "lengthDiff", "lengthGetColumnCountIfZeroLineCount", "lengthToObj", "lengthZero", "to<PERSON><PERSON><PERSON>", "SmallImmutableSet", "Token", "constructor", "length", "kind", "bracketId", "bracketIds", "astNode", "TextBufferTokenizer", "textModel", "bracketTokens", "reader", "NonPeekableTextBufferTokenizer", "_offset", "didPeek", "peeked", "textBufferLineCount", "getLineCount", "textBufferLastLineLength", "getLine<PERSON><PERSON>th", "offset", "skip", "obj", "setPosition", "lineCount", "columnCount", "read", "token", "peek", "lineIdx", "line", "lineCharOffset", "lineTokens", "lineTokenOffset", "peekedToken", "column", "findTokenIndexAtOffset", "tokenization", "getLineTokens", "get<PERSON>ineC<PERSON>nt", "startLineIdx", "startLineCharOffset", "lengthHeuristic", "tokenCount", "getCount", "peekedBracketToken", "tokenMetadata", "getMetadata", "isOther", "getTokenType", "containsBracketType", "containsBalancedBrackets", "endOffset", "getEndOffset", "languageId", "getLanguageId", "text", "substring", "brackets", "getSingleLanguageBracketTokens", "regexp", "regExpGlobal", "lastIndex", "match", "exec", "getToken", "index", "getEmpty", "FastTokenizer", "idx", "regExpStr", "getRegExpStr", "RegExp", "tokens", "curLineCount", "lastLineBreakOffset", "lastTokenEndOffset", "lastTokenEndLine", "smallTextTokens0Line", "i", "push", "smallTextTokens1Line", "curOffset", "value", "col<PERSON>ount"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/tokenizer.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { NotSupportedError } from '../../../../../base/common/errors.js';\nimport { TokenMetadata } from '../../../encodedTokenAttributes.js';\nimport { TextAstNode } from './ast.js';\nimport { lengthAdd, lengthDiff, lengthGetColumnCountIfZeroLineCount, lengthToObj, lengthZero, toLength } from './length.js';\nimport { SmallImmutableSet } from './smallImmutableSet.js';\nexport class Token {\n    constructor(length, kind, \n    /**\n     * If this token is an opening bracket, this is the id of the opening bracket.\n     * If this token is a closing bracket, this is the id of the first opening bracket that is closed by this bracket.\n     * Otherwise, it is -1.\n     */\n    bracketId, \n    /**\n     * If this token is an opening bracket, this just contains `bracketId`.\n     * If this token is a closing bracket, this lists all opening bracket ids, that it closes.\n     * Otherwise, it is empty.\n     */\n    bracketIds, astNode) {\n        this.length = length;\n        this.kind = kind;\n        this.bracketId = bracketId;\n        this.bracketIds = bracketIds;\n        this.astNode = astNode;\n    }\n}\nexport class TextBufferTokenizer {\n    constructor(textModel, bracketTokens) {\n        this.textModel = textModel;\n        this.bracketTokens = bracketTokens;\n        this.reader = new NonPeekableTextBufferTokenizer(this.textModel, this.bracketTokens);\n        this._offset = lengthZero;\n        this.didPeek = false;\n        this.peeked = null;\n        this.textBufferLineCount = textModel.getLineCount();\n        this.textBufferLastLineLength = textModel.getLineLength(this.textBufferLineCount);\n    }\n    get offset() {\n        return this._offset;\n    }\n    get length() {\n        return toLength(this.textBufferLineCount - 1, this.textBufferLastLineLength);\n    }\n    skip(length) {\n        this.didPeek = false;\n        this._offset = lengthAdd(this._offset, length);\n        const obj = lengthToObj(this._offset);\n        this.reader.setPosition(obj.lineCount, obj.columnCount);\n    }\n    read() {\n        let token;\n        if (this.peeked) {\n            this.didPeek = false;\n            token = this.peeked;\n        }\n        else {\n            token = this.reader.read();\n        }\n        if (token) {\n            this._offset = lengthAdd(this._offset, token.length);\n        }\n        return token;\n    }\n    peek() {\n        if (!this.didPeek) {\n            this.peeked = this.reader.read();\n            this.didPeek = true;\n        }\n        return this.peeked;\n    }\n}\n/**\n * Does not support peek.\n*/\nclass NonPeekableTextBufferTokenizer {\n    constructor(textModel, bracketTokens) {\n        this.textModel = textModel;\n        this.bracketTokens = bracketTokens;\n        this.lineIdx = 0;\n        this.line = null;\n        this.lineCharOffset = 0;\n        this.lineTokens = null;\n        this.lineTokenOffset = 0;\n        /** Must be a zero line token. The end of the document cannot be peeked. */\n        this.peekedToken = null;\n        this.textBufferLineCount = textModel.getLineCount();\n        this.textBufferLastLineLength = textModel.getLineLength(this.textBufferLineCount);\n    }\n    setPosition(lineIdx, column) {\n        // We must not jump into a token!\n        if (lineIdx === this.lineIdx) {\n            this.lineCharOffset = column;\n            if (this.line !== null) {\n                this.lineTokenOffset = this.lineCharOffset === 0 ? 0 : this.lineTokens.findTokenIndexAtOffset(this.lineCharOffset);\n            }\n        }\n        else {\n            this.lineIdx = lineIdx;\n            this.lineCharOffset = column;\n            this.line = null;\n        }\n        this.peekedToken = null;\n    }\n    read() {\n        if (this.peekedToken) {\n            const token = this.peekedToken;\n            this.peekedToken = null;\n            this.lineCharOffset += lengthGetColumnCountIfZeroLineCount(token.length);\n            return token;\n        }\n        if (this.lineIdx > this.textBufferLineCount - 1 || (this.lineIdx === this.textBufferLineCount - 1 && this.lineCharOffset >= this.textBufferLastLineLength)) {\n            // We are after the end\n            return null;\n        }\n        if (this.line === null) {\n            this.lineTokens = this.textModel.tokenization.getLineTokens(this.lineIdx + 1);\n            this.line = this.lineTokens.getLineContent();\n            this.lineTokenOffset = this.lineCharOffset === 0 ? 0 : this.lineTokens.findTokenIndexAtOffset(this.lineCharOffset);\n        }\n        const startLineIdx = this.lineIdx;\n        const startLineCharOffset = this.lineCharOffset;\n        // limits the length of text tokens.\n        // If text tokens get too long, incremental updates will be slow\n        let lengthHeuristic = 0;\n        while (true) {\n            const lineTokens = this.lineTokens;\n            const tokenCount = lineTokens.getCount();\n            let peekedBracketToken = null;\n            if (this.lineTokenOffset < tokenCount) {\n                const tokenMetadata = lineTokens.getMetadata(this.lineTokenOffset);\n                while (this.lineTokenOffset + 1 < tokenCount && tokenMetadata === lineTokens.getMetadata(this.lineTokenOffset + 1)) {\n                    // Skip tokens that are identical.\n                    // Sometimes, (bracket) identifiers are split up into multiple tokens.\n                    this.lineTokenOffset++;\n                }\n                const isOther = TokenMetadata.getTokenType(tokenMetadata) === 0 /* StandardTokenType.Other */;\n                const containsBracketType = TokenMetadata.containsBalancedBrackets(tokenMetadata);\n                const endOffset = lineTokens.getEndOffset(this.lineTokenOffset);\n                // Is there a bracket token next? Only consume text.\n                if (containsBracketType && isOther && this.lineCharOffset < endOffset) {\n                    const languageId = lineTokens.getLanguageId(this.lineTokenOffset);\n                    const text = this.line.substring(this.lineCharOffset, endOffset);\n                    const brackets = this.bracketTokens.getSingleLanguageBracketTokens(languageId);\n                    const regexp = brackets.regExpGlobal;\n                    if (regexp) {\n                        regexp.lastIndex = 0;\n                        const match = regexp.exec(text);\n                        if (match) {\n                            peekedBracketToken = brackets.getToken(match[0]);\n                            if (peekedBracketToken) {\n                                // Consume leading text of the token\n                                this.lineCharOffset += match.index;\n                            }\n                        }\n                    }\n                }\n                lengthHeuristic += endOffset - this.lineCharOffset;\n                if (peekedBracketToken) {\n                    // Don't skip the entire token, as a single token could contain multiple brackets.\n                    if (startLineIdx !== this.lineIdx || startLineCharOffset !== this.lineCharOffset) {\n                        // There is text before the bracket\n                        this.peekedToken = peekedBracketToken;\n                        break;\n                    }\n                    else {\n                        // Consume the peeked token\n                        this.lineCharOffset += lengthGetColumnCountIfZeroLineCount(peekedBracketToken.length);\n                        return peekedBracketToken;\n                    }\n                }\n                else {\n                    // Skip the entire token, as the token contains no brackets at all.\n                    this.lineTokenOffset++;\n                    this.lineCharOffset = endOffset;\n                }\n            }\n            else {\n                if (this.lineIdx === this.textBufferLineCount - 1) {\n                    break;\n                }\n                this.lineIdx++;\n                this.lineTokens = this.textModel.tokenization.getLineTokens(this.lineIdx + 1);\n                this.lineTokenOffset = 0;\n                this.line = this.lineTokens.getLineContent();\n                this.lineCharOffset = 0;\n                lengthHeuristic += 33; // max 1000/33 = 30 lines\n                // This limits the amount of work to recompute min-indentation\n                if (lengthHeuristic > 1000) {\n                    // only break (automatically) at the end of line.\n                    break;\n                }\n            }\n            if (lengthHeuristic > 1500) {\n                // Eventually break regardless of the line length so that\n                // very long lines do not cause bad performance.\n                // This effective limits max indentation to 500, as\n                // indentation is not computed across multiple text nodes.\n                break;\n            }\n        }\n        // If a token contains some proper indentation, it also contains \\n{INDENTATION+}(?!{INDENTATION}),\n        // unless the line is too long.\n        // Thus, the min indentation of the document is the minimum min indentation of every text node.\n        const length = lengthDiff(startLineIdx, startLineCharOffset, this.lineIdx, this.lineCharOffset);\n        return new Token(length, 0 /* TokenKind.Text */, -1, SmallImmutableSet.getEmpty(), new TextAstNode(length));\n    }\n}\nexport class FastTokenizer {\n    constructor(text, brackets) {\n        this.text = text;\n        this._offset = lengthZero;\n        this.idx = 0;\n        const regExpStr = brackets.getRegExpStr();\n        const regexp = regExpStr ? new RegExp(regExpStr + '|\\n', 'gi') : null;\n        const tokens = [];\n        let match;\n        let curLineCount = 0;\n        let lastLineBreakOffset = 0;\n        let lastTokenEndOffset = 0;\n        let lastTokenEndLine = 0;\n        const smallTextTokens0Line = [];\n        for (let i = 0; i < 60; i++) {\n            smallTextTokens0Line.push(new Token(toLength(0, i), 0 /* TokenKind.Text */, -1, SmallImmutableSet.getEmpty(), new TextAstNode(toLength(0, i))));\n        }\n        const smallTextTokens1Line = [];\n        for (let i = 0; i < 60; i++) {\n            smallTextTokens1Line.push(new Token(toLength(1, i), 0 /* TokenKind.Text */, -1, SmallImmutableSet.getEmpty(), new TextAstNode(toLength(1, i))));\n        }\n        if (regexp) {\n            regexp.lastIndex = 0;\n            // If a token contains indentation, it also contains \\n{INDENTATION+}(?!{INDENTATION})\n            while ((match = regexp.exec(text)) !== null) {\n                const curOffset = match.index;\n                const value = match[0];\n                if (value === '\\n') {\n                    curLineCount++;\n                    lastLineBreakOffset = curOffset + 1;\n                }\n                else {\n                    if (lastTokenEndOffset !== curOffset) {\n                        let token;\n                        if (lastTokenEndLine === curLineCount) {\n                            const colCount = curOffset - lastTokenEndOffset;\n                            if (colCount < smallTextTokens0Line.length) {\n                                token = smallTextTokens0Line[colCount];\n                            }\n                            else {\n                                const length = toLength(0, colCount);\n                                token = new Token(length, 0 /* TokenKind.Text */, -1, SmallImmutableSet.getEmpty(), new TextAstNode(length));\n                            }\n                        }\n                        else {\n                            const lineCount = curLineCount - lastTokenEndLine;\n                            const colCount = curOffset - lastLineBreakOffset;\n                            if (lineCount === 1 && colCount < smallTextTokens1Line.length) {\n                                token = smallTextTokens1Line[colCount];\n                            }\n                            else {\n                                const length = toLength(lineCount, colCount);\n                                token = new Token(length, 0 /* TokenKind.Text */, -1, SmallImmutableSet.getEmpty(), new TextAstNode(length));\n                            }\n                        }\n                        tokens.push(token);\n                    }\n                    // value is matched by regexp, so the token must exist\n                    tokens.push(brackets.getToken(value));\n                    lastTokenEndOffset = curOffset + value.length;\n                    lastTokenEndLine = curLineCount;\n                }\n            }\n        }\n        const offset = text.length;\n        if (lastTokenEndOffset !== offset) {\n            const length = (lastTokenEndLine === curLineCount)\n                ? toLength(0, offset - lastTokenEndOffset)\n                : toLength(curLineCount - lastTokenEndLine, offset - lastLineBreakOffset);\n            tokens.push(new Token(length, 0 /* TokenKind.Text */, -1, SmallImmutableSet.getEmpty(), new TextAstNode(length)));\n        }\n        this.length = toLength(curLineCount, offset - lastLineBreakOffset);\n        this.tokens = tokens;\n    }\n    get offset() {\n        return this._offset;\n    }\n    read() {\n        return this.tokens[this.idx++] || null;\n    }\n    peek() {\n        return this.tokens[this.idx] || null;\n    }\n    skip(length) {\n        throw new NotSupportedError();\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,iBAAiB,QAAQ,sCAAsC;AACxE,SAASC,aAAa,QAAQ,oCAAoC;AAClE,SAASC,WAAW,QAAQ,UAAU;AACtC,SAASC,SAAS,EAAEC,UAAU,EAAEC,mCAAmC,EAAEC,WAAW,EAAEC,UAAU,EAAEC,QAAQ,QAAQ,aAAa;AAC3H,SAASC,iBAAiB,QAAQ,wBAAwB;AAC1D,OAAO,MAAMC,KAAK,CAAC;EACfC,WAAWA,CAACC,MAAM,EAAEC,IAAI;EACxB;AACJ;AACA;AACA;AACA;EACIC,SAAS;EACT;AACJ;AACA;AACA;AACA;EACIC,UAAU,EAAEC,OAAO,EAAE;IACjB,IAAI,CAACJ,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,OAAO,GAAGA,OAAO;EAC1B;AACJ;AACA,OAAO,MAAMC,mBAAmB,CAAC;EAC7BN,WAAWA,CAACO,SAAS,EAAEC,aAAa,EAAE;IAClC,IAAI,CAACD,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,MAAM,GAAG,IAAIC,8BAA8B,CAAC,IAAI,CAACH,SAAS,EAAE,IAAI,CAACC,aAAa,CAAC;IACpF,IAAI,CAACG,OAAO,GAAGf,UAAU;IACzB,IAAI,CAACgB,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,mBAAmB,GAAGP,SAAS,CAACQ,YAAY,CAAC,CAAC;IACnD,IAAI,CAACC,wBAAwB,GAAGT,SAAS,CAACU,aAAa,CAAC,IAAI,CAACH,mBAAmB,CAAC;EACrF;EACA,IAAII,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACP,OAAO;EACvB;EACA,IAAIV,MAAMA,CAAA,EAAG;IACT,OAAOJ,QAAQ,CAAC,IAAI,CAACiB,mBAAmB,GAAG,CAAC,EAAE,IAAI,CAACE,wBAAwB,CAAC;EAChF;EACAG,IAAIA,CAAClB,MAAM,EAAE;IACT,IAAI,CAACW,OAAO,GAAG,KAAK;IACpB,IAAI,CAACD,OAAO,GAAGnB,SAAS,CAAC,IAAI,CAACmB,OAAO,EAAEV,MAAM,CAAC;IAC9C,MAAMmB,GAAG,GAAGzB,WAAW,CAAC,IAAI,CAACgB,OAAO,CAAC;IACrC,IAAI,CAACF,MAAM,CAACY,WAAW,CAACD,GAAG,CAACE,SAAS,EAAEF,GAAG,CAACG,WAAW,CAAC;EAC3D;EACAC,IAAIA,CAAA,EAAG;IACH,IAAIC,KAAK;IACT,IAAI,IAAI,CAACZ,MAAM,EAAE;MACb,IAAI,CAACD,OAAO,GAAG,KAAK;MACpBa,KAAK,GAAG,IAAI,CAACZ,MAAM;IACvB,CAAC,MACI;MACDY,KAAK,GAAG,IAAI,CAAChB,MAAM,CAACe,IAAI,CAAC,CAAC;IAC9B;IACA,IAAIC,KAAK,EAAE;MACP,IAAI,CAACd,OAAO,GAAGnB,SAAS,CAAC,IAAI,CAACmB,OAAO,EAAEc,KAAK,CAACxB,MAAM,CAAC;IACxD;IACA,OAAOwB,KAAK;EAChB;EACAC,IAAIA,CAAA,EAAG;IACH,IAAI,CAAC,IAAI,CAACd,OAAO,EAAE;MACf,IAAI,CAACC,MAAM,GAAG,IAAI,CAACJ,MAAM,CAACe,IAAI,CAAC,CAAC;MAChC,IAAI,CAACZ,OAAO,GAAG,IAAI;IACvB;IACA,OAAO,IAAI,CAACC,MAAM;EACtB;AACJ;AACA;AACA;AACA;AACA,MAAMH,8BAA8B,CAAC;EACjCV,WAAWA,CAACO,SAAS,EAAEC,aAAa,EAAE;IAClC,IAAI,CAACD,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACmB,OAAO,GAAG,CAAC;IAChB,IAAI,CAACC,IAAI,GAAG,IAAI;IAChB,IAAI,CAACC,cAAc,GAAG,CAAC;IACvB,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,eAAe,GAAG,CAAC;IACxB;IACA,IAAI,CAACC,WAAW,GAAG,IAAI;IACvB,IAAI,CAAClB,mBAAmB,GAAGP,SAAS,CAACQ,YAAY,CAAC,CAAC;IACnD,IAAI,CAACC,wBAAwB,GAAGT,SAAS,CAACU,aAAa,CAAC,IAAI,CAACH,mBAAmB,CAAC;EACrF;EACAO,WAAWA,CAACM,OAAO,EAAEM,MAAM,EAAE;IACzB;IACA,IAAIN,OAAO,KAAK,IAAI,CAACA,OAAO,EAAE;MAC1B,IAAI,CAACE,cAAc,GAAGI,MAAM;MAC5B,IAAI,IAAI,CAACL,IAAI,KAAK,IAAI,EAAE;QACpB,IAAI,CAACG,eAAe,GAAG,IAAI,CAACF,cAAc,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAACC,UAAU,CAACI,sBAAsB,CAAC,IAAI,CAACL,cAAc,CAAC;MACtH;IACJ,CAAC,MACI;MACD,IAAI,CAACF,OAAO,GAAGA,OAAO;MACtB,IAAI,CAACE,cAAc,GAAGI,MAAM;MAC5B,IAAI,CAACL,IAAI,GAAG,IAAI;IACpB;IACA,IAAI,CAACI,WAAW,GAAG,IAAI;EAC3B;EACAR,IAAIA,CAAA,EAAG;IACH,IAAI,IAAI,CAACQ,WAAW,EAAE;MAClB,MAAMP,KAAK,GAAG,IAAI,CAACO,WAAW;MAC9B,IAAI,CAACA,WAAW,GAAG,IAAI;MACvB,IAAI,CAACH,cAAc,IAAInC,mCAAmC,CAAC+B,KAAK,CAACxB,MAAM,CAAC;MACxE,OAAOwB,KAAK;IAChB;IACA,IAAI,IAAI,CAACE,OAAO,GAAG,IAAI,CAACb,mBAAmB,GAAG,CAAC,IAAK,IAAI,CAACa,OAAO,KAAK,IAAI,CAACb,mBAAmB,GAAG,CAAC,IAAI,IAAI,CAACe,cAAc,IAAI,IAAI,CAACb,wBAAyB,EAAE;MACxJ;MACA,OAAO,IAAI;IACf;IACA,IAAI,IAAI,CAACY,IAAI,KAAK,IAAI,EAAE;MACpB,IAAI,CAACE,UAAU,GAAG,IAAI,CAACvB,SAAS,CAAC4B,YAAY,CAACC,aAAa,CAAC,IAAI,CAACT,OAAO,GAAG,CAAC,CAAC;MAC7E,IAAI,CAACC,IAAI,GAAG,IAAI,CAACE,UAAU,CAACO,cAAc,CAAC,CAAC;MAC5C,IAAI,CAACN,eAAe,GAAG,IAAI,CAACF,cAAc,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAACC,UAAU,CAACI,sBAAsB,CAAC,IAAI,CAACL,cAAc,CAAC;IACtH;IACA,MAAMS,YAAY,GAAG,IAAI,CAACX,OAAO;IACjC,MAAMY,mBAAmB,GAAG,IAAI,CAACV,cAAc;IAC/C;IACA;IACA,IAAIW,eAAe,GAAG,CAAC;IACvB,OAAO,IAAI,EAAE;MACT,MAAMV,UAAU,GAAG,IAAI,CAACA,UAAU;MAClC,MAAMW,UAAU,GAAGX,UAAU,CAACY,QAAQ,CAAC,CAAC;MACxC,IAAIC,kBAAkB,GAAG,IAAI;MAC7B,IAAI,IAAI,CAACZ,eAAe,GAAGU,UAAU,EAAE;QACnC,MAAMG,aAAa,GAAGd,UAAU,CAACe,WAAW,CAAC,IAAI,CAACd,eAAe,CAAC;QAClE,OAAO,IAAI,CAACA,eAAe,GAAG,CAAC,GAAGU,UAAU,IAAIG,aAAa,KAAKd,UAAU,CAACe,WAAW,CAAC,IAAI,CAACd,eAAe,GAAG,CAAC,CAAC,EAAE;UAChH;UACA;UACA,IAAI,CAACA,eAAe,EAAE;QAC1B;QACA,MAAMe,OAAO,GAAGxD,aAAa,CAACyD,YAAY,CAACH,aAAa,CAAC,KAAK,CAAC,CAAC;QAChE,MAAMI,mBAAmB,GAAG1D,aAAa,CAAC2D,wBAAwB,CAACL,aAAa,CAAC;QACjF,MAAMM,SAAS,GAAGpB,UAAU,CAACqB,YAAY,CAAC,IAAI,CAACpB,eAAe,CAAC;QAC/D;QACA,IAAIiB,mBAAmB,IAAIF,OAAO,IAAI,IAAI,CAACjB,cAAc,GAAGqB,SAAS,EAAE;UACnE,MAAME,UAAU,GAAGtB,UAAU,CAACuB,aAAa,CAAC,IAAI,CAACtB,eAAe,CAAC;UACjE,MAAMuB,IAAI,GAAG,IAAI,CAAC1B,IAAI,CAAC2B,SAAS,CAAC,IAAI,CAAC1B,cAAc,EAAEqB,SAAS,CAAC;UAChE,MAAMM,QAAQ,GAAG,IAAI,CAAChD,aAAa,CAACiD,8BAA8B,CAACL,UAAU,CAAC;UAC9E,MAAMM,MAAM,GAAGF,QAAQ,CAACG,YAAY;UACpC,IAAID,MAAM,EAAE;YACRA,MAAM,CAACE,SAAS,GAAG,CAAC;YACpB,MAAMC,KAAK,GAAGH,MAAM,CAACI,IAAI,CAACR,IAAI,CAAC;YAC/B,IAAIO,KAAK,EAAE;cACPlB,kBAAkB,GAAGa,QAAQ,CAACO,QAAQ,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC;cAChD,IAAIlB,kBAAkB,EAAE;gBACpB;gBACA,IAAI,CAACd,cAAc,IAAIgC,KAAK,CAACG,KAAK;cACtC;YACJ;UACJ;QACJ;QACAxB,eAAe,IAAIU,SAAS,GAAG,IAAI,CAACrB,cAAc;QAClD,IAAIc,kBAAkB,EAAE;UACpB;UACA,IAAIL,YAAY,KAAK,IAAI,CAACX,OAAO,IAAIY,mBAAmB,KAAK,IAAI,CAACV,cAAc,EAAE;YAC9E;YACA,IAAI,CAACG,WAAW,GAAGW,kBAAkB;YACrC;UACJ,CAAC,MACI;YACD;YACA,IAAI,CAACd,cAAc,IAAInC,mCAAmC,CAACiD,kBAAkB,CAAC1C,MAAM,CAAC;YACrF,OAAO0C,kBAAkB;UAC7B;QACJ,CAAC,MACI;UACD;UACA,IAAI,CAACZ,eAAe,EAAE;UACtB,IAAI,CAACF,cAAc,GAAGqB,SAAS;QACnC;MACJ,CAAC,MACI;QACD,IAAI,IAAI,CAACvB,OAAO,KAAK,IAAI,CAACb,mBAAmB,GAAG,CAAC,EAAE;UAC/C;QACJ;QACA,IAAI,CAACa,OAAO,EAAE;QACd,IAAI,CAACG,UAAU,GAAG,IAAI,CAACvB,SAAS,CAAC4B,YAAY,CAACC,aAAa,CAAC,IAAI,CAACT,OAAO,GAAG,CAAC,CAAC;QAC7E,IAAI,CAACI,eAAe,GAAG,CAAC;QACxB,IAAI,CAACH,IAAI,GAAG,IAAI,CAACE,UAAU,CAACO,cAAc,CAAC,CAAC;QAC5C,IAAI,CAACR,cAAc,GAAG,CAAC;QACvBW,eAAe,IAAI,EAAE,CAAC,CAAC;QACvB;QACA,IAAIA,eAAe,GAAG,IAAI,EAAE;UACxB;UACA;QACJ;MACJ;MACA,IAAIA,eAAe,GAAG,IAAI,EAAE;QACxB;QACA;QACA;QACA;QACA;MACJ;IACJ;IACA;IACA;IACA;IACA,MAAMvC,MAAM,GAAGR,UAAU,CAAC6C,YAAY,EAAEC,mBAAmB,EAAE,IAAI,CAACZ,OAAO,EAAE,IAAI,CAACE,cAAc,CAAC;IAC/F,OAAO,IAAI9B,KAAK,CAACE,MAAM,EAAE,CAAC,CAAC,sBAAsB,CAAC,CAAC,EAAEH,iBAAiB,CAACmE,QAAQ,CAAC,CAAC,EAAE,IAAI1E,WAAW,CAACU,MAAM,CAAC,CAAC;EAC/G;AACJ;AACA,OAAO,MAAMiE,aAAa,CAAC;EACvBlE,WAAWA,CAACsD,IAAI,EAAEE,QAAQ,EAAE;IACxB,IAAI,CAACF,IAAI,GAAGA,IAAI;IAChB,IAAI,CAAC3C,OAAO,GAAGf,UAAU;IACzB,IAAI,CAACuE,GAAG,GAAG,CAAC;IACZ,MAAMC,SAAS,GAAGZ,QAAQ,CAACa,YAAY,CAAC,CAAC;IACzC,MAAMX,MAAM,GAAGU,SAAS,GAAG,IAAIE,MAAM,CAACF,SAAS,GAAG,KAAK,EAAE,IAAI,CAAC,GAAG,IAAI;IACrE,MAAMG,MAAM,GAAG,EAAE;IACjB,IAAIV,KAAK;IACT,IAAIW,YAAY,GAAG,CAAC;IACpB,IAAIC,mBAAmB,GAAG,CAAC;IAC3B,IAAIC,kBAAkB,GAAG,CAAC;IAC1B,IAAIC,gBAAgB,GAAG,CAAC;IACxB,MAAMC,oBAAoB,GAAG,EAAE;IAC/B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MACzBD,oBAAoB,CAACE,IAAI,CAAC,IAAI/E,KAAK,CAACF,QAAQ,CAAC,CAAC,EAAEgF,CAAC,CAAC,EAAE,CAAC,CAAC,sBAAsB,CAAC,CAAC,EAAE/E,iBAAiB,CAACmE,QAAQ,CAAC,CAAC,EAAE,IAAI1E,WAAW,CAACM,QAAQ,CAAC,CAAC,EAAEgF,CAAC,CAAC,CAAC,CAAC,CAAC;IACnJ;IACA,MAAME,oBAAoB,GAAG,EAAE;IAC/B,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MACzBE,oBAAoB,CAACD,IAAI,CAAC,IAAI/E,KAAK,CAACF,QAAQ,CAAC,CAAC,EAAEgF,CAAC,CAAC,EAAE,CAAC,CAAC,sBAAsB,CAAC,CAAC,EAAE/E,iBAAiB,CAACmE,QAAQ,CAAC,CAAC,EAAE,IAAI1E,WAAW,CAACM,QAAQ,CAAC,CAAC,EAAEgF,CAAC,CAAC,CAAC,CAAC,CAAC;IACnJ;IACA,IAAInB,MAAM,EAAE;MACRA,MAAM,CAACE,SAAS,GAAG,CAAC;MACpB;MACA,OAAO,CAACC,KAAK,GAAGH,MAAM,CAACI,IAAI,CAACR,IAAI,CAAC,MAAM,IAAI,EAAE;QACzC,MAAM0B,SAAS,GAAGnB,KAAK,CAACG,KAAK;QAC7B,MAAMiB,KAAK,GAAGpB,KAAK,CAAC,CAAC,CAAC;QACtB,IAAIoB,KAAK,KAAK,IAAI,EAAE;UAChBT,YAAY,EAAE;UACdC,mBAAmB,GAAGO,SAAS,GAAG,CAAC;QACvC,CAAC,MACI;UACD,IAAIN,kBAAkB,KAAKM,SAAS,EAAE;YAClC,IAAIvD,KAAK;YACT,IAAIkD,gBAAgB,KAAKH,YAAY,EAAE;cACnC,MAAMU,QAAQ,GAAGF,SAAS,GAAGN,kBAAkB;cAC/C,IAAIQ,QAAQ,GAAGN,oBAAoB,CAAC3E,MAAM,EAAE;gBACxCwB,KAAK,GAAGmD,oBAAoB,CAACM,QAAQ,CAAC;cAC1C,CAAC,MACI;gBACD,MAAMjF,MAAM,GAAGJ,QAAQ,CAAC,CAAC,EAAEqF,QAAQ,CAAC;gBACpCzD,KAAK,GAAG,IAAI1B,KAAK,CAACE,MAAM,EAAE,CAAC,CAAC,sBAAsB,CAAC,CAAC,EAAEH,iBAAiB,CAACmE,QAAQ,CAAC,CAAC,EAAE,IAAI1E,WAAW,CAACU,MAAM,CAAC,CAAC;cAChH;YACJ,CAAC,MACI;cACD,MAAMqB,SAAS,GAAGkD,YAAY,GAAGG,gBAAgB;cACjD,MAAMO,QAAQ,GAAGF,SAAS,GAAGP,mBAAmB;cAChD,IAAInD,SAAS,KAAK,CAAC,IAAI4D,QAAQ,GAAGH,oBAAoB,CAAC9E,MAAM,EAAE;gBAC3DwB,KAAK,GAAGsD,oBAAoB,CAACG,QAAQ,CAAC;cAC1C,CAAC,MACI;gBACD,MAAMjF,MAAM,GAAGJ,QAAQ,CAACyB,SAAS,EAAE4D,QAAQ,CAAC;gBAC5CzD,KAAK,GAAG,IAAI1B,KAAK,CAACE,MAAM,EAAE,CAAC,CAAC,sBAAsB,CAAC,CAAC,EAAEH,iBAAiB,CAACmE,QAAQ,CAAC,CAAC,EAAE,IAAI1E,WAAW,CAACU,MAAM,CAAC,CAAC;cAChH;YACJ;YACAsE,MAAM,CAACO,IAAI,CAACrD,KAAK,CAAC;UACtB;UACA;UACA8C,MAAM,CAACO,IAAI,CAACtB,QAAQ,CAACO,QAAQ,CAACkB,KAAK,CAAC,CAAC;UACrCP,kBAAkB,GAAGM,SAAS,GAAGC,KAAK,CAAChF,MAAM;UAC7C0E,gBAAgB,GAAGH,YAAY;QACnC;MACJ;IACJ;IACA,MAAMtD,MAAM,GAAGoC,IAAI,CAACrD,MAAM;IAC1B,IAAIyE,kBAAkB,KAAKxD,MAAM,EAAE;MAC/B,MAAMjB,MAAM,GAAI0E,gBAAgB,KAAKH,YAAY,GAC3C3E,QAAQ,CAAC,CAAC,EAAEqB,MAAM,GAAGwD,kBAAkB,CAAC,GACxC7E,QAAQ,CAAC2E,YAAY,GAAGG,gBAAgB,EAAEzD,MAAM,GAAGuD,mBAAmB,CAAC;MAC7EF,MAAM,CAACO,IAAI,CAAC,IAAI/E,KAAK,CAACE,MAAM,EAAE,CAAC,CAAC,sBAAsB,CAAC,CAAC,EAAEH,iBAAiB,CAACmE,QAAQ,CAAC,CAAC,EAAE,IAAI1E,WAAW,CAACU,MAAM,CAAC,CAAC,CAAC;IACrH;IACA,IAAI,CAACA,MAAM,GAAGJ,QAAQ,CAAC2E,YAAY,EAAEtD,MAAM,GAAGuD,mBAAmB,CAAC;IAClE,IAAI,CAACF,MAAM,GAAGA,MAAM;EACxB;EACA,IAAIrD,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACP,OAAO;EACvB;EACAa,IAAIA,CAAA,EAAG;IACH,OAAO,IAAI,CAAC+C,MAAM,CAAC,IAAI,CAACJ,GAAG,EAAE,CAAC,IAAI,IAAI;EAC1C;EACAzC,IAAIA,CAAA,EAAG;IACH,OAAO,IAAI,CAAC6C,MAAM,CAAC,IAAI,CAACJ,GAAG,CAAC,IAAI,IAAI;EACxC;EACAhD,IAAIA,CAAClB,MAAM,EAAE;IACT,MAAM,IAAIZ,iBAAiB,CAAC,CAAC;EACjC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}