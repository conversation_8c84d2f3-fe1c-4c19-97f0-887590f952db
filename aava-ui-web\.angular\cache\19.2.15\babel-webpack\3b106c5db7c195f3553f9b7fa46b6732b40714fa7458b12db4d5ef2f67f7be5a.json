{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport var HorizontalGuidesState = /*#__PURE__*/function (HorizontalGuidesState) {\n  HorizontalGuidesState[HorizontalGuidesState[\"Disabled\"] = 0] = \"Disabled\";\n  HorizontalGuidesState[HorizontalGuidesState[\"EnabledForActive\"] = 1] = \"EnabledForActive\";\n  HorizontalGuidesState[HorizontalGuidesState[\"Enabled\"] = 2] = \"Enabled\";\n  return HorizontalGuidesState;\n}(HorizontalGuidesState || {});\nexport class IndentGuide {\n  constructor(visibleColumn, column, className,\n  /**\n   * If set, this indent guide is a horizontal guide (no vertical part).\n   * It starts at visibleColumn and continues until endColumn.\n  */\n  horizontalLine,\n  /**\n   * If set (!= -1), only show this guide for wrapped lines that don't contain this model column, but are after it.\n  */\n  forWrappedLinesAfterColumn, forWrappedLinesBeforeOrAtColumn) {\n    this.visibleColumn = visibleColumn;\n    this.column = column;\n    this.className = className;\n    this.horizontalLine = horizontalLine;\n    this.forWrappedLinesAfterColumn = forWrappedLinesAfterColumn;\n    this.forWrappedLinesBeforeOrAtColumn = forWrappedLinesBeforeOrAtColumn;\n    if (visibleColumn !== -1 === (column !== -1)) {\n      throw new Error();\n    }\n  }\n}\nexport class IndentGuideHorizontalLine {\n  constructor(top, endColumn) {\n    this.top = top;\n    this.endColumn = endColumn;\n  }\n}", "map": {"version": 3, "names": ["HorizontalGuidesState", "IndentGuide", "constructor", "visibleColumn", "column", "className", "horizontalLine", "forWrappedLinesAfterColumn", "forWrappedLinesBeforeOrAtColumn", "Error", "IndentGuideHorizontalLine", "top", "endColumn"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/textModelGuides.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport var HorizontalGuidesState;\n(function (HorizontalGuidesState) {\n    HorizontalGuidesState[HorizontalGuidesState[\"Disabled\"] = 0] = \"Disabled\";\n    HorizontalGuidesState[HorizontalGuidesState[\"EnabledForActive\"] = 1] = \"EnabledForActive\";\n    HorizontalGuidesState[HorizontalGuidesState[\"Enabled\"] = 2] = \"Enabled\";\n})(HorizontalGuidesState || (HorizontalGuidesState = {}));\nexport class IndentGuide {\n    constructor(visibleColumn, column, className, \n    /**\n     * If set, this indent guide is a horizontal guide (no vertical part).\n     * It starts at visibleColumn and continues until endColumn.\n    */\n    horizontalLine, \n    /**\n     * If set (!= -1), only show this guide for wrapped lines that don't contain this model column, but are after it.\n    */\n    forWrappedLinesAfterColumn, forWrappedLinesBeforeOrAtColumn) {\n        this.visibleColumn = visibleColumn;\n        this.column = column;\n        this.className = className;\n        this.horizontalLine = horizontalLine;\n        this.forWrappedLinesAfterColumn = forWrappedLinesAfterColumn;\n        this.forWrappedLinesBeforeOrAtColumn = forWrappedLinesBeforeOrAtColumn;\n        if ((visibleColumn !== -1) === (column !== -1)) {\n            throw new Error();\n        }\n    }\n}\nexport class IndentGuideHorizontalLine {\n    constructor(top, endColumn) {\n        this.top = top;\n        this.endColumn = endColumn;\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,IAAIA,qBAAqB,gBAC/B,UAAUA,qBAAqB,EAAE;EAC9BA,qBAAqB,CAACA,qBAAqB,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU;EACzEA,qBAAqB,CAACA,qBAAqB,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,GAAG,kBAAkB;EACzFA,qBAAqB,CAACA,qBAAqB,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS;EAAC,OAHjEA,qBAAqB;AAIhC,CAAC,CAAEA,qBAAqB,IAA6B,CAAC,CAAE,CALxB;AAMhC,OAAO,MAAMC,WAAW,CAAC;EACrBC,WAAWA,CAACC,aAAa,EAAEC,MAAM,EAAEC,SAAS;EAC5C;AACJ;AACA;AACA;EACIC,cAAc;EACd;AACJ;AACA;EACIC,0BAA0B,EAAEC,+BAA+B,EAAE;IACzD,IAAI,CAACL,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,0BAA0B,GAAGA,0BAA0B;IAC5D,IAAI,CAACC,+BAA+B,GAAGA,+BAA+B;IACtE,IAAKL,aAAa,KAAK,CAAC,CAAC,MAAOC,MAAM,KAAK,CAAC,CAAC,CAAC,EAAE;MAC5C,MAAM,IAAIK,KAAK,CAAC,CAAC;IACrB;EACJ;AACJ;AACA,OAAO,MAAMC,yBAAyB,CAAC;EACnCR,WAAWA,CAACS,GAAG,EAAEC,SAAS,EAAE;IACxB,IAAI,CAACD,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,SAAS,GAAGA,SAAS;EAC9B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}