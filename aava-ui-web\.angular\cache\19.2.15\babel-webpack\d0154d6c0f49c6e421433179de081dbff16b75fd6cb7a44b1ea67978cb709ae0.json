{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Schemas } from '../../../base/common/network.js';\nimport { DataUri } from '../../../base/common/resources.js';\nimport { URI } from '../../../base/common/uri.js';\nimport { PLAINTEXT_LANGUAGE_ID } from '../languages/modesRegistry.js';\nimport { FileKind } from '../../../platform/files/common/files.js';\nimport { ThemeIcon } from '../../../base/common/themables.js';\nconst fileIconDirectoryRegex = /(?:\\/|^)(?:([^\\/]+)\\/)?([^\\/]+)$/;\nexport function getIconClasses(modelService, languageService, resource, fileKind, icon) {\n  if (ThemeIcon.isThemeIcon(icon)) {\n    return [`codicon-${icon.id}`, 'predefined-file-icon'];\n  }\n  if (URI.isUri(icon)) {\n    return [];\n  }\n  // we always set these base classes even if we do not have a path\n  const classes = fileKind === FileKind.ROOT_FOLDER ? ['rootfolder-icon'] : fileKind === FileKind.FOLDER ? ['folder-icon'] : ['file-icon'];\n  if (resource) {\n    // Get the path and name of the resource. For data-URIs, we need to parse specially\n    let name;\n    if (resource.scheme === Schemas.data) {\n      const metadata = DataUri.parseMetaData(resource);\n      name = metadata.get(DataUri.META_DATA_LABEL);\n    } else {\n      const match = resource.path.match(fileIconDirectoryRegex);\n      if (match) {\n        name = cssEscape(match[2].toLowerCase());\n        if (match[1]) {\n          classes.push(`${cssEscape(match[1].toLowerCase())}-name-dir-icon`); // parent directory\n        }\n      } else {\n        name = cssEscape(resource.authority.toLowerCase());\n      }\n    }\n    // Root Folders\n    if (fileKind === FileKind.ROOT_FOLDER) {\n      classes.push(`${name}-root-name-folder-icon`);\n    }\n    // Folders\n    else if (fileKind === FileKind.FOLDER) {\n      classes.push(`${name}-name-folder-icon`);\n    }\n    // Files\n    else {\n      // Name & Extension(s)\n      if (name) {\n        classes.push(`${name}-name-file-icon`);\n        classes.push(`name-file-icon`); // extra segment to increase file-name score\n        // Avoid doing an explosive combination of extensions for very long filenames\n        // (most file systems do not allow files > 255 length) with lots of `.` characters\n        // https://github.com/microsoft/vscode/issues/116199\n        if (name.length <= 255) {\n          const dotSegments = name.split('.');\n          for (let i = 1; i < dotSegments.length; i++) {\n            classes.push(`${dotSegments.slice(i).join('.')}-ext-file-icon`); // add each combination of all found extensions if more than one\n          }\n        }\n        classes.push(`ext-file-icon`); // extra segment to increase file-ext score\n      }\n      // Detected Mode\n      const detectedLanguageId = detectLanguageId(modelService, languageService, resource);\n      if (detectedLanguageId) {\n        classes.push(`${cssEscape(detectedLanguageId)}-lang-file-icon`);\n      }\n    }\n  }\n  return classes;\n}\nfunction detectLanguageId(modelService, languageService, resource) {\n  if (!resource) {\n    return null; // we need a resource at least\n  }\n  let languageId = null;\n  // Data URI: check for encoded metadata\n  if (resource.scheme === Schemas.data) {\n    const metadata = DataUri.parseMetaData(resource);\n    const mime = metadata.get(DataUri.META_DATA_MIME);\n    if (mime) {\n      languageId = languageService.getLanguageIdByMimeType(mime);\n    }\n  }\n  // Any other URI: check for model if existing\n  else {\n    const model = modelService.getModel(resource);\n    if (model) {\n      languageId = model.getLanguageId();\n    }\n  }\n  // only take if the language id is specific (aka no just plain text)\n  if (languageId && languageId !== PLAINTEXT_LANGUAGE_ID) {\n    return languageId;\n  }\n  // otherwise fallback to path based detection\n  return languageService.guessLanguageIdByFilepathOrFirstLine(resource);\n}\nfunction cssEscape(str) {\n  return str.replace(/[\\s]/g, '/'); // HTML class names can not contain certain whitespace characters (https://dom.spec.whatwg.org/#interface-domtokenlist), use / instead, which doesn't exist in file names.\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "DataUri", "URI", "PLAINTEXT_LANGUAGE_ID", "FileKind", "ThemeIcon", "fileIconDirectoryRegex", "getIconClasses", "modelService", "languageService", "resource", "fileKind", "icon", "isThemeIcon", "id", "<PERSON><PERSON><PERSON>", "classes", "ROOT_FOLDER", "FOLDER", "name", "scheme", "data", "metadata", "parseMetaData", "get", "META_DATA_LABEL", "match", "path", "cssEscape", "toLowerCase", "push", "authority", "length", "dotSegments", "split", "i", "slice", "join", "detectedLanguageId", "detectLanguageId", "languageId", "mime", "META_DATA_MIME", "getLanguageIdByMimeType", "model", "getModel", "getLanguageId", "guessLanguageIdByFilepathOrFirstLine", "str", "replace"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/services/getIconClasses.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Schemas } from '../../../base/common/network.js';\nimport { DataUri } from '../../../base/common/resources.js';\nimport { URI } from '../../../base/common/uri.js';\nimport { PLAINTEXT_LANGUAGE_ID } from '../languages/modesRegistry.js';\nimport { FileKind } from '../../../platform/files/common/files.js';\nimport { ThemeIcon } from '../../../base/common/themables.js';\nconst fileIconDirectoryRegex = /(?:\\/|^)(?:([^\\/]+)\\/)?([^\\/]+)$/;\nexport function getIconClasses(modelService, languageService, resource, fileKind, icon) {\n    if (ThemeIcon.isThemeIcon(icon)) {\n        return [`codicon-${icon.id}`, 'predefined-file-icon'];\n    }\n    if (URI.isUri(icon)) {\n        return [];\n    }\n    // we always set these base classes even if we do not have a path\n    const classes = fileKind === FileKind.ROOT_FOLDER ? ['rootfolder-icon'] : fileKind === FileKind.FOLDER ? ['folder-icon'] : ['file-icon'];\n    if (resource) {\n        // Get the path and name of the resource. For data-URIs, we need to parse specially\n        let name;\n        if (resource.scheme === Schemas.data) {\n            const metadata = DataUri.parseMetaData(resource);\n            name = metadata.get(DataUri.META_DATA_LABEL);\n        }\n        else {\n            const match = resource.path.match(fileIconDirectoryRegex);\n            if (match) {\n                name = cssEscape(match[2].toLowerCase());\n                if (match[1]) {\n                    classes.push(`${cssEscape(match[1].toLowerCase())}-name-dir-icon`); // parent directory\n                }\n            }\n            else {\n                name = cssEscape(resource.authority.toLowerCase());\n            }\n        }\n        // Root Folders\n        if (fileKind === FileKind.ROOT_FOLDER) {\n            classes.push(`${name}-root-name-folder-icon`);\n        }\n        // Folders\n        else if (fileKind === FileKind.FOLDER) {\n            classes.push(`${name}-name-folder-icon`);\n        }\n        // Files\n        else {\n            // Name & Extension(s)\n            if (name) {\n                classes.push(`${name}-name-file-icon`);\n                classes.push(`name-file-icon`); // extra segment to increase file-name score\n                // Avoid doing an explosive combination of extensions for very long filenames\n                // (most file systems do not allow files > 255 length) with lots of `.` characters\n                // https://github.com/microsoft/vscode/issues/116199\n                if (name.length <= 255) {\n                    const dotSegments = name.split('.');\n                    for (let i = 1; i < dotSegments.length; i++) {\n                        classes.push(`${dotSegments.slice(i).join('.')}-ext-file-icon`); // add each combination of all found extensions if more than one\n                    }\n                }\n                classes.push(`ext-file-icon`); // extra segment to increase file-ext score\n            }\n            // Detected Mode\n            const detectedLanguageId = detectLanguageId(modelService, languageService, resource);\n            if (detectedLanguageId) {\n                classes.push(`${cssEscape(detectedLanguageId)}-lang-file-icon`);\n            }\n        }\n    }\n    return classes;\n}\nfunction detectLanguageId(modelService, languageService, resource) {\n    if (!resource) {\n        return null; // we need a resource at least\n    }\n    let languageId = null;\n    // Data URI: check for encoded metadata\n    if (resource.scheme === Schemas.data) {\n        const metadata = DataUri.parseMetaData(resource);\n        const mime = metadata.get(DataUri.META_DATA_MIME);\n        if (mime) {\n            languageId = languageService.getLanguageIdByMimeType(mime);\n        }\n    }\n    // Any other URI: check for model if existing\n    else {\n        const model = modelService.getModel(resource);\n        if (model) {\n            languageId = model.getLanguageId();\n        }\n    }\n    // only take if the language id is specific (aka no just plain text)\n    if (languageId && languageId !== PLAINTEXT_LANGUAGE_ID) {\n        return languageId;\n    }\n    // otherwise fallback to path based detection\n    return languageService.guessLanguageIdByFilepathOrFirstLine(resource);\n}\nfunction cssEscape(str) {\n    return str.replace(/[\\s]/g, '/'); // HTML class names can not contain certain whitespace characters (https://dom.spec.whatwg.org/#interface-domtokenlist), use / instead, which doesn't exist in file names.\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,OAAO,QAAQ,iCAAiC;AACzD,SAASC,OAAO,QAAQ,mCAAmC;AAC3D,SAASC,GAAG,QAAQ,6BAA6B;AACjD,SAASC,qBAAqB,QAAQ,+BAA+B;AACrE,SAASC,QAAQ,QAAQ,yCAAyC;AAClE,SAASC,SAAS,QAAQ,mCAAmC;AAC7D,MAAMC,sBAAsB,GAAG,kCAAkC;AACjE,OAAO,SAASC,cAAcA,CAACC,YAAY,EAAEC,eAAe,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,IAAI,EAAE;EACpF,IAAIP,SAAS,CAACQ,WAAW,CAACD,IAAI,CAAC,EAAE;IAC7B,OAAO,CAAC,WAAWA,IAAI,CAACE,EAAE,EAAE,EAAE,sBAAsB,CAAC;EACzD;EACA,IAAIZ,GAAG,CAACa,KAAK,CAACH,IAAI,CAAC,EAAE;IACjB,OAAO,EAAE;EACb;EACA;EACA,MAAMI,OAAO,GAAGL,QAAQ,KAAKP,QAAQ,CAACa,WAAW,GAAG,CAAC,iBAAiB,CAAC,GAAGN,QAAQ,KAAKP,QAAQ,CAACc,MAAM,GAAG,CAAC,aAAa,CAAC,GAAG,CAAC,WAAW,CAAC;EACxI,IAAIR,QAAQ,EAAE;IACV;IACA,IAAIS,IAAI;IACR,IAAIT,QAAQ,CAACU,MAAM,KAAKpB,OAAO,CAACqB,IAAI,EAAE;MAClC,MAAMC,QAAQ,GAAGrB,OAAO,CAACsB,aAAa,CAACb,QAAQ,CAAC;MAChDS,IAAI,GAAGG,QAAQ,CAACE,GAAG,CAACvB,OAAO,CAACwB,eAAe,CAAC;IAChD,CAAC,MACI;MACD,MAAMC,KAAK,GAAGhB,QAAQ,CAACiB,IAAI,CAACD,KAAK,CAACpB,sBAAsB,CAAC;MACzD,IAAIoB,KAAK,EAAE;QACPP,IAAI,GAAGS,SAAS,CAACF,KAAK,CAAC,CAAC,CAAC,CAACG,WAAW,CAAC,CAAC,CAAC;QACxC,IAAIH,KAAK,CAAC,CAAC,CAAC,EAAE;UACVV,OAAO,CAACc,IAAI,CAAC,GAAGF,SAAS,CAACF,KAAK,CAAC,CAAC,CAAC,CAACG,WAAW,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC;QACxE;MACJ,CAAC,MACI;QACDV,IAAI,GAAGS,SAAS,CAAClB,QAAQ,CAACqB,SAAS,CAACF,WAAW,CAAC,CAAC,CAAC;MACtD;IACJ;IACA;IACA,IAAIlB,QAAQ,KAAKP,QAAQ,CAACa,WAAW,EAAE;MACnCD,OAAO,CAACc,IAAI,CAAC,GAAGX,IAAI,wBAAwB,CAAC;IACjD;IACA;IAAA,KACK,IAAIR,QAAQ,KAAKP,QAAQ,CAACc,MAAM,EAAE;MACnCF,OAAO,CAACc,IAAI,CAAC,GAAGX,IAAI,mBAAmB,CAAC;IAC5C;IACA;IAAA,KACK;MACD;MACA,IAAIA,IAAI,EAAE;QACNH,OAAO,CAACc,IAAI,CAAC,GAAGX,IAAI,iBAAiB,CAAC;QACtCH,OAAO,CAACc,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC;QAChC;QACA;QACA;QACA,IAAIX,IAAI,CAACa,MAAM,IAAI,GAAG,EAAE;UACpB,MAAMC,WAAW,GAAGd,IAAI,CAACe,KAAK,CAAC,GAAG,CAAC;UACnC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,WAAW,CAACD,MAAM,EAAEG,CAAC,EAAE,EAAE;YACzCnB,OAAO,CAACc,IAAI,CAAC,GAAGG,WAAW,CAACG,KAAK,CAACD,CAAC,CAAC,CAACE,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC,CAAC;UACrE;QACJ;QACArB,OAAO,CAACc,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;MACnC;MACA;MACA,MAAMQ,kBAAkB,GAAGC,gBAAgB,CAAC/B,YAAY,EAAEC,eAAe,EAAEC,QAAQ,CAAC;MACpF,IAAI4B,kBAAkB,EAAE;QACpBtB,OAAO,CAACc,IAAI,CAAC,GAAGF,SAAS,CAACU,kBAAkB,CAAC,iBAAiB,CAAC;MACnE;IACJ;EACJ;EACA,OAAOtB,OAAO;AAClB;AACA,SAASuB,gBAAgBA,CAAC/B,YAAY,EAAEC,eAAe,EAAEC,QAAQ,EAAE;EAC/D,IAAI,CAACA,QAAQ,EAAE;IACX,OAAO,IAAI,CAAC,CAAC;EACjB;EACA,IAAI8B,UAAU,GAAG,IAAI;EACrB;EACA,IAAI9B,QAAQ,CAACU,MAAM,KAAKpB,OAAO,CAACqB,IAAI,EAAE;IAClC,MAAMC,QAAQ,GAAGrB,OAAO,CAACsB,aAAa,CAACb,QAAQ,CAAC;IAChD,MAAM+B,IAAI,GAAGnB,QAAQ,CAACE,GAAG,CAACvB,OAAO,CAACyC,cAAc,CAAC;IACjD,IAAID,IAAI,EAAE;MACND,UAAU,GAAG/B,eAAe,CAACkC,uBAAuB,CAACF,IAAI,CAAC;IAC9D;EACJ;EACA;EAAA,KACK;IACD,MAAMG,KAAK,GAAGpC,YAAY,CAACqC,QAAQ,CAACnC,QAAQ,CAAC;IAC7C,IAAIkC,KAAK,EAAE;MACPJ,UAAU,GAAGI,KAAK,CAACE,aAAa,CAAC,CAAC;IACtC;EACJ;EACA;EACA,IAAIN,UAAU,IAAIA,UAAU,KAAKrC,qBAAqB,EAAE;IACpD,OAAOqC,UAAU;EACrB;EACA;EACA,OAAO/B,eAAe,CAACsC,oCAAoC,CAACrC,QAAQ,CAAC;AACzE;AACA,SAASkB,SAASA,CAACoB,GAAG,EAAE;EACpB,OAAOA,GAAG,CAACC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;AACtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}