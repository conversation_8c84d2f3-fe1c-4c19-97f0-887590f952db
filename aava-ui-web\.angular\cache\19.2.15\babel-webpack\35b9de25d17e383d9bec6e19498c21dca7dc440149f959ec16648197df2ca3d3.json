{"ast": null, "code": "export default function (onenter, onupdate, onexit) {\n  var enter = this.enter(),\n    update = this,\n    exit = this.exit();\n  if (typeof onenter === \"function\") {\n    enter = onenter(enter);\n    if (enter) enter = enter.selection();\n  } else {\n    enter = enter.append(onenter + \"\");\n  }\n  if (onupdate != null) {\n    update = onupdate(update);\n    if (update) update = update.selection();\n  }\n  if (onexit == null) exit.remove();else onexit(exit);\n  return enter && update ? enter.merge(update).order() : update;\n}", "map": {"version": 3, "names": ["onenter", "onupdate", "onexit", "enter", "update", "exit", "selection", "append", "remove", "merge", "order"], "sources": ["C:/console/aava-ui-web/node_modules/d3-selection/src/selection/join.js"], "sourcesContent": ["export default function(onenter, onupdate, onexit) {\n  var enter = this.enter(), update = this, exit = this.exit();\n  if (typeof onenter === \"function\") {\n    enter = onenter(enter);\n    if (enter) enter = enter.selection();\n  } else {\n    enter = enter.append(onenter + \"\");\n  }\n  if (onupdate != null) {\n    update = onupdate(update);\n    if (update) update = update.selection();\n  }\n  if (onexit == null) exit.remove(); else onexit(exit);\n  return enter && update ? enter.merge(update).order() : update;\n}\n"], "mappings": "AAAA,eAAe,UAASA,OAAO,EAAEC,QAAQ,EAAEC,MAAM,EAAE;EACjD,IAAIC,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC,CAAC;IAAEC,MAAM,GAAG,IAAI;IAAEC,IAAI,GAAG,IAAI,CAACA,IAAI,CAAC,CAAC;EAC3D,IAAI,OAAOL,OAAO,KAAK,UAAU,EAAE;IACjCG,KAAK,GAAGH,OAAO,CAACG,KAAK,CAAC;IACtB,IAAIA,KAAK,EAAEA,KAAK,GAAGA,KAAK,CAACG,SAAS,CAAC,CAAC;EACtC,CAAC,MAAM;IACLH,KAAK,GAAGA,KAAK,CAACI,MAAM,CAACP,OAAO,GAAG,EAAE,CAAC;EACpC;EACA,IAAIC,QAAQ,IAAI,IAAI,EAAE;IACpBG,MAAM,GAAGH,QAAQ,CAACG,MAAM,CAAC;IACzB,IAAIA,MAAM,EAAEA,MAAM,GAAGA,MAAM,CAACE,SAAS,CAAC,CAAC;EACzC;EACA,IAAIJ,MAAM,IAAI,IAAI,EAAEG,IAAI,CAACG,MAAM,CAAC,CAAC,CAAC,KAAMN,MAAM,CAACG,IAAI,CAAC;EACpD,OAAOF,KAAK,IAAIC,MAAM,GAAGD,KAAK,CAACM,KAAK,CAACL,MAAM,CAAC,CAACM,KAAK,CAAC,CAAC,GAAGN,MAAM;AAC/D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}