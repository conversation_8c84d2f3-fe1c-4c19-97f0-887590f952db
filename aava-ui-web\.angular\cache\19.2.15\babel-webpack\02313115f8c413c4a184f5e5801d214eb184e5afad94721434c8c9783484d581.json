{"ast": null, "code": "import { interpolateTransformSvg as interpolateTransform } from \"d3-interpolate\";\nimport { namespace } from \"d3-selection\";\nimport { tweenValue } from \"./tween.js\";\nimport interpolate from \"./interpolate.js\";\nfunction attrRemove(name) {\n  return function () {\n    this.removeAttribute(name);\n  };\n}\nfunction attrRemoveNS(fullname) {\n  return function () {\n    this.removeAttributeNS(fullname.space, fullname.local);\n  };\n}\nfunction attrConstant(name, interpolate, value1) {\n  var string00,\n    string1 = value1 + \"\",\n    interpolate0;\n  return function () {\n    var string0 = this.getAttribute(name);\n    return string0 === string1 ? null : string0 === string00 ? interpolate0 : interpolate0 = interpolate(string00 = string0, value1);\n  };\n}\nfunction attrConstantNS(fullname, interpolate, value1) {\n  var string00,\n    string1 = value1 + \"\",\n    interpolate0;\n  return function () {\n    var string0 = this.getAttributeNS(fullname.space, fullname.local);\n    return string0 === string1 ? null : string0 === string00 ? interpolate0 : interpolate0 = interpolate(string00 = string0, value1);\n  };\n}\nfunction attrFunction(name, interpolate, value) {\n  var string00, string10, interpolate0;\n  return function () {\n    var string0,\n      value1 = value(this),\n      string1;\n    if (value1 == null) return void this.removeAttribute(name);\n    string0 = this.getAttribute(name);\n    string1 = value1 + \"\";\n    return string0 === string1 ? null : string0 === string00 && string1 === string10 ? interpolate0 : (string10 = string1, interpolate0 = interpolate(string00 = string0, value1));\n  };\n}\nfunction attrFunctionNS(fullname, interpolate, value) {\n  var string00, string10, interpolate0;\n  return function () {\n    var string0,\n      value1 = value(this),\n      string1;\n    if (value1 == null) return void this.removeAttributeNS(fullname.space, fullname.local);\n    string0 = this.getAttributeNS(fullname.space, fullname.local);\n    string1 = value1 + \"\";\n    return string0 === string1 ? null : string0 === string00 && string1 === string10 ? interpolate0 : (string10 = string1, interpolate0 = interpolate(string00 = string0, value1));\n  };\n}\nexport default function (name, value) {\n  var fullname = namespace(name),\n    i = fullname === \"transform\" ? interpolateTransform : interpolate;\n  return this.attrTween(name, typeof value === \"function\" ? (fullname.local ? attrFunctionNS : attrFunction)(fullname, i, tweenValue(this, \"attr.\" + name, value)) : value == null ? (fullname.local ? attrRemoveNS : attrRemove)(fullname) : (fullname.local ? attrConstantNS : attrConstant)(fullname, i, value));\n}", "map": {"version": 3, "names": ["interpolateTransformSvg", "interpolateTransform", "namespace", "tweenValue", "interpolate", "attrRemove", "name", "removeAttribute", "attrRemoveNS", "fullname", "removeAttributeNS", "space", "local", "attrConstant", "value1", "string00", "string1", "interpolate0", "string0", "getAttribute", "attrConstantNS", "getAttributeNS", "attrFunction", "value", "string10", "attrFunctionNS", "i", "attrTween"], "sources": ["C:/console/aava-ui-web/node_modules/d3-transition/src/transition/attr.js"], "sourcesContent": ["import {interpolateTransformSvg as interpolateTransform} from \"d3-interpolate\";\nimport {namespace} from \"d3-selection\";\nimport {tweenValue} from \"./tween.js\";\nimport interpolate from \"./interpolate.js\";\n\nfunction attrRemove(name) {\n  return function() {\n    this.removeAttribute(name);\n  };\n}\n\nfunction attrRemoveNS(fullname) {\n  return function() {\n    this.removeAttributeNS(fullname.space, fullname.local);\n  };\n}\n\nfunction attrConstant(name, interpolate, value1) {\n  var string00,\n      string1 = value1 + \"\",\n      interpolate0;\n  return function() {\n    var string0 = this.getAttribute(name);\n    return string0 === string1 ? null\n        : string0 === string00 ? interpolate0\n        : interpolate0 = interpolate(string00 = string0, value1);\n  };\n}\n\nfunction attrConstantNS(fullname, interpolate, value1) {\n  var string00,\n      string1 = value1 + \"\",\n      interpolate0;\n  return function() {\n    var string0 = this.getAttributeNS(fullname.space, fullname.local);\n    return string0 === string1 ? null\n        : string0 === string00 ? interpolate0\n        : interpolate0 = interpolate(string00 = string0, value1);\n  };\n}\n\nfunction attrFunction(name, interpolate, value) {\n  var string00,\n      string10,\n      interpolate0;\n  return function() {\n    var string0, value1 = value(this), string1;\n    if (value1 == null) return void this.removeAttribute(name);\n    string0 = this.getAttribute(name);\n    string1 = value1 + \"\";\n    return string0 === string1 ? null\n        : string0 === string00 && string1 === string10 ? interpolate0\n        : (string10 = string1, interpolate0 = interpolate(string00 = string0, value1));\n  };\n}\n\nfunction attrFunctionNS(fullname, interpolate, value) {\n  var string00,\n      string10,\n      interpolate0;\n  return function() {\n    var string0, value1 = value(this), string1;\n    if (value1 == null) return void this.removeAttributeNS(fullname.space, fullname.local);\n    string0 = this.getAttributeNS(fullname.space, fullname.local);\n    string1 = value1 + \"\";\n    return string0 === string1 ? null\n        : string0 === string00 && string1 === string10 ? interpolate0\n        : (string10 = string1, interpolate0 = interpolate(string00 = string0, value1));\n  };\n}\n\nexport default function(name, value) {\n  var fullname = namespace(name), i = fullname === \"transform\" ? interpolateTransform : interpolate;\n  return this.attrTween(name, typeof value === \"function\"\n      ? (fullname.local ? attrFunctionNS : attrFunction)(fullname, i, tweenValue(this, \"attr.\" + name, value))\n      : value == null ? (fullname.local ? attrRemoveNS : attrRemove)(fullname)\n      : (fullname.local ? attrConstantNS : attrConstant)(fullname, i, value));\n}\n"], "mappings": "AAAA,SAAQA,uBAAuB,IAAIC,oBAAoB,QAAO,gBAAgB;AAC9E,SAAQC,SAAS,QAAO,cAAc;AACtC,SAAQC,UAAU,QAAO,YAAY;AACrC,OAAOC,WAAW,MAAM,kBAAkB;AAE1C,SAASC,UAAUA,CAACC,IAAI,EAAE;EACxB,OAAO,YAAW;IAChB,IAAI,CAACC,eAAe,CAACD,IAAI,CAAC;EAC5B,CAAC;AACH;AAEA,SAASE,YAAYA,CAACC,QAAQ,EAAE;EAC9B,OAAO,YAAW;IAChB,IAAI,CAACC,iBAAiB,CAACD,QAAQ,CAACE,KAAK,EAAEF,QAAQ,CAACG,KAAK,CAAC;EACxD,CAAC;AACH;AAEA,SAASC,YAAYA,CAACP,IAAI,EAAEF,WAAW,EAAEU,MAAM,EAAE;EAC/C,IAAIC,QAAQ;IACRC,OAAO,GAAGF,MAAM,GAAG,EAAE;IACrBG,YAAY;EAChB,OAAO,YAAW;IAChB,IAAIC,OAAO,GAAG,IAAI,CAACC,YAAY,CAACb,IAAI,CAAC;IACrC,OAAOY,OAAO,KAAKF,OAAO,GAAG,IAAI,GAC3BE,OAAO,KAAKH,QAAQ,GAAGE,YAAY,GACnCA,YAAY,GAAGb,WAAW,CAACW,QAAQ,GAAGG,OAAO,EAAEJ,MAAM,CAAC;EAC9D,CAAC;AACH;AAEA,SAASM,cAAcA,CAACX,QAAQ,EAAEL,WAAW,EAAEU,MAAM,EAAE;EACrD,IAAIC,QAAQ;IACRC,OAAO,GAAGF,MAAM,GAAG,EAAE;IACrBG,YAAY;EAChB,OAAO,YAAW;IAChB,IAAIC,OAAO,GAAG,IAAI,CAACG,cAAc,CAACZ,QAAQ,CAACE,KAAK,EAAEF,QAAQ,CAACG,KAAK,CAAC;IACjE,OAAOM,OAAO,KAAKF,OAAO,GAAG,IAAI,GAC3BE,OAAO,KAAKH,QAAQ,GAAGE,YAAY,GACnCA,YAAY,GAAGb,WAAW,CAACW,QAAQ,GAAGG,OAAO,EAAEJ,MAAM,CAAC;EAC9D,CAAC;AACH;AAEA,SAASQ,YAAYA,CAAChB,IAAI,EAAEF,WAAW,EAAEmB,KAAK,EAAE;EAC9C,IAAIR,QAAQ,EACRS,QAAQ,EACRP,YAAY;EAChB,OAAO,YAAW;IAChB,IAAIC,OAAO;MAAEJ,MAAM,GAAGS,KAAK,CAAC,IAAI,CAAC;MAAEP,OAAO;IAC1C,IAAIF,MAAM,IAAI,IAAI,EAAE,OAAO,KAAK,IAAI,CAACP,eAAe,CAACD,IAAI,CAAC;IAC1DY,OAAO,GAAG,IAAI,CAACC,YAAY,CAACb,IAAI,CAAC;IACjCU,OAAO,GAAGF,MAAM,GAAG,EAAE;IACrB,OAAOI,OAAO,KAAKF,OAAO,GAAG,IAAI,GAC3BE,OAAO,KAAKH,QAAQ,IAAIC,OAAO,KAAKQ,QAAQ,GAAGP,YAAY,IAC1DO,QAAQ,GAAGR,OAAO,EAAEC,YAAY,GAAGb,WAAW,CAACW,QAAQ,GAAGG,OAAO,EAAEJ,MAAM,CAAC,CAAC;EACpF,CAAC;AACH;AAEA,SAASW,cAAcA,CAAChB,QAAQ,EAAEL,WAAW,EAAEmB,KAAK,EAAE;EACpD,IAAIR,QAAQ,EACRS,QAAQ,EACRP,YAAY;EAChB,OAAO,YAAW;IAChB,IAAIC,OAAO;MAAEJ,MAAM,GAAGS,KAAK,CAAC,IAAI,CAAC;MAAEP,OAAO;IAC1C,IAAIF,MAAM,IAAI,IAAI,EAAE,OAAO,KAAK,IAAI,CAACJ,iBAAiB,CAACD,QAAQ,CAACE,KAAK,EAAEF,QAAQ,CAACG,KAAK,CAAC;IACtFM,OAAO,GAAG,IAAI,CAACG,cAAc,CAACZ,QAAQ,CAACE,KAAK,EAAEF,QAAQ,CAACG,KAAK,CAAC;IAC7DI,OAAO,GAAGF,MAAM,GAAG,EAAE;IACrB,OAAOI,OAAO,KAAKF,OAAO,GAAG,IAAI,GAC3BE,OAAO,KAAKH,QAAQ,IAAIC,OAAO,KAAKQ,QAAQ,GAAGP,YAAY,IAC1DO,QAAQ,GAAGR,OAAO,EAAEC,YAAY,GAAGb,WAAW,CAACW,QAAQ,GAAGG,OAAO,EAAEJ,MAAM,CAAC,CAAC;EACpF,CAAC;AACH;AAEA,eAAe,UAASR,IAAI,EAAEiB,KAAK,EAAE;EACnC,IAAId,QAAQ,GAAGP,SAAS,CAACI,IAAI,CAAC;IAAEoB,CAAC,GAAGjB,QAAQ,KAAK,WAAW,GAAGR,oBAAoB,GAAGG,WAAW;EACjG,OAAO,IAAI,CAACuB,SAAS,CAACrB,IAAI,EAAE,OAAOiB,KAAK,KAAK,UAAU,GACjD,CAACd,QAAQ,CAACG,KAAK,GAAGa,cAAc,GAAGH,YAAY,EAAEb,QAAQ,EAAEiB,CAAC,EAAEvB,UAAU,CAAC,IAAI,EAAE,OAAO,GAAGG,IAAI,EAAEiB,KAAK,CAAC,CAAC,GACtGA,KAAK,IAAI,IAAI,GAAG,CAACd,QAAQ,CAACG,KAAK,GAAGJ,YAAY,GAAGH,UAAU,EAAEI,QAAQ,CAAC,GACtE,CAACA,QAAQ,CAACG,KAAK,GAAGQ,cAAc,GAAGP,YAAY,EAAEJ,QAAQ,EAAEiB,CAAC,EAAEH,KAAK,CAAC,CAAC;AAC7E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}