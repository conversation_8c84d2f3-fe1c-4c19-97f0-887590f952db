{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { HeroBannerComponent } from '../../shared/components/hero-banner/hero-banner.component';\nimport { MarketplaceFooterComponent } from '../../shared/components/marketplace-footer/marketplace-footer.component';\nimport { NewsBlogsComponent } from '../../shared/components/news-blogs/news-blogs.component';\nimport { TestimonialsSectionComponent } from '../../shared/components/testimonials-section/testimonials-section.component';\nimport { ClientsSectionComponent } from '../../shared/components/clients-section/clients-section.component';\nimport { AnalyticsComponent } from '../../shared/components/analytics/analytics.component';\nimport { MarketplaceAgentsSectionComponent } from '../../shared/components/marketplace-agents-section/marketplace-agents-section.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../shared/auth/services/token-storage.service\";\nexport let MarketplaceComponent = /*#__PURE__*/(() => {\n  class MarketplaceComponent {\n    router;\n    tokenStorage;\n    showAuthStatus = false;\n    authStatusMessage = '';\n    heroConfig = {\n      title: 'The Next Generation',\n      brandText: 'Software Engineering',\n      subtitle: 'Powered by AI. Crafted for engineers.',\n      description: 'Revolutionize your development workflow with intelligent Studios, autonomous Agents, and seamless Workflows that adapt to your team needs.',\n      imageSrc: 'marketplace-hero-image.png',\n      // Updated path - files in public/ folder are served from root\n      imageAlt: 'AI Robot working on development tasks',\n      actions: [{\n        label: 'Let’s hop in to AAVA',\n        variant: 'primary',\n        size: 'large',\n        action: () => this.signIn()\n      }],\n      backgroundColor: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n      textColor: 'white',\n      layout: 'default'\n    };\n    constructor(router, tokenStorage) {\n      this.router = router;\n      this.tokenStorage = tokenStorage;\n    }\n    ngOnInit() {\n      console.log('🏪 Marketplace component loaded');\n      this.checkAuthenticationStatus();\n    }\n    checkAuthenticationStatus() {\n      const accessToken = this.tokenStorage.getAccessToken();\n      const refreshToken = this.tokenStorage.getRefreshToken();\n      if (accessToken) {\n        this.showAuthStatus = true;\n        // this.authStatusMessage =\n        //   '✅ You are already signed in. Click \"Sign In to Continue\" to access your dashboard.';\n      } else if (refreshToken) {\n        this.showAuthStatus = true;\n        // this.authStatusMessage =\n        //   '🔄 Session expired. Please sign in again to continue.';\n      } else {\n        this.showAuthStatus = true;\n        // this.authStatusMessage =\n        //   '👋 Welcome! Sign in to access your personalized AI workspace.';\n      }\n    }\n    signIn() {\n      console.log('🔐 Navigating to login...');\n      this.router.navigate(['/login']);\n    }\n    exploreMarketplace() {\n      // Smooth scroll to features section\n      const featuresSection = document.querySelector('.features-section');\n      if (featuresSection) {\n        featuresSection.scrollIntoView({\n          behavior: 'smooth',\n          block: 'start'\n        });\n      }\n    }\n    static ɵfac = function MarketplaceComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MarketplaceComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.TokenStorageService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: MarketplaceComponent,\n      selectors: [[\"app-marketplace\"]],\n      decls: 8,\n      vars: 12,\n      consts: [[1, \"marketplace-container\"], [3, \"title\", \"brandText\", \"subtitle\", \"description\", \"imageSrc\", \"imageAlt\", \"actions\", \"statusMessage\", \"showStatus\", \"backgroundColor\", \"textColor\", \"layout\"]],\n      template: function MarketplaceComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵelement(1, \"app-hero-banner\", 1)(2, \"app-marketplace-agents-section\")(3, \"app-clients-section\")(4, \"app-analytics\")(5, \"app-testimonials-section\")(6, \"app-news-blogs\")(7, \"app-marketplace-footer\");\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"title\", ctx.heroConfig.title)(\"brandText\", ctx.heroConfig.brandText)(\"subtitle\", ctx.heroConfig.subtitle)(\"description\", ctx.heroConfig.description)(\"imageSrc\", ctx.heroConfig.imageSrc)(\"imageAlt\", ctx.heroConfig.imageAlt)(\"actions\", ctx.heroConfig.actions)(\"statusMessage\", ctx.authStatusMessage)(\"showStatus\", ctx.showAuthStatus)(\"backgroundColor\", ctx.heroConfig.backgroundColor)(\"textColor\", ctx.heroConfig.textColor)(\"layout\", ctx.heroConfig.layout);\n        }\n      },\n      dependencies: [CommonModule, HeroBannerComponent, MarketplaceFooterComponent, NewsBlogsComponent, TestimonialsSectionComponent, ClientsSectionComponent, AnalyticsComponent, MarketplaceAgentsSectionComponent],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n  return MarketplaceComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "HeroBannerComponent", "MarketplaceFooterComponent", "NewsBlogsComponent", "TestimonialsSectionComponent", "ClientsSectionComponent", "AnalyticsComponent", "MarketplaceAgentsSectionComponent", "MarketplaceComponent", "router", "tokenStorage", "showAuthStatus", "authStatusMessage", "hero<PERSON>onfig", "title", "brandText", "subtitle", "description", "imageSrc", "imageAlt", "actions", "label", "variant", "size", "action", "signIn", "backgroundColor", "textColor", "layout", "constructor", "ngOnInit", "console", "log", "checkAuthenticationStatus", "accessToken", "getAccessToken", "refreshToken", "getRefreshToken", "navigate", "exploreMarketplace", "featuresSection", "document", "querySelector", "scrollIntoView", "behavior", "block", "i0", "ɵɵdirectiveInject", "i1", "Router", "i2", "TokenStorageService", "selectors", "decls", "vars", "consts", "template", "MarketplaceComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\marketing\\src\\app\\pages\\marketplace\\marketplace.component.ts", "C:\\console\\aava-ui-web\\projects\\marketing\\src\\app\\pages\\marketplace\\marketplace.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { Router } from '@angular/router';\r\nimport { TokenStorageService } from '../../shared/auth/services/token-storage.service';\r\nimport {\r\n  HeroBannerComponent,\r\n  HeroAction,\r\n} from '../../shared/components/hero-banner/hero-banner.component';\r\nimport { MarketplaceFooterComponent } from '../../shared/components/marketplace-footer/marketplace-footer.component';\r\nimport { NewsBlogsComponent } from '../../shared/components/news-blogs/news-blogs.component';\r\nimport { TestimonialsSectionComponent } from '../../shared/components/testimonials-section/testimonials-section.component';\r\nimport { ClientsSectionComponent } from '../../shared/components/clients-section/clients-section.component';\r\nimport { AnalyticsComponent } from '../../shared/components/analytics/analytics.component';\r\nimport { MarketplaceAgentsSectionComponent } from '../../shared/components/marketplace-agents-section/marketplace-agents-section.component';\r\n\r\ninterface HeroConfig {\r\n  title: string;\r\n  brandText: string;\r\n  subtitle: string;\r\n  description: string;\r\n  imageSrc: string;\r\n  imageAlt: string;\r\n  actions: HeroAction[];\r\n  backgroundColor: string;\r\n  textColor: string;\r\n  layout: 'default' | 'centered' | 'image-left' | 'image-right';\r\n}\r\n\r\n@Component({\r\n  selector: 'app-marketplace',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    HeroBannerComponent,\r\n    MarketplaceFooterComponent,\r\n    NewsBlogsComponent,\r\n    TestimonialsSectionComponent,\r\n    ClientsSectionComponent,\r\n    AnalyticsComponent,\r\n    MarketplaceAgentsSectionComponent,\r\n  ],\r\n  templateUrl: './marketplace.component.html',\r\n  styleUrl: './marketplace.component.scss',\r\n})\r\nexport class MarketplaceComponent implements OnInit {\r\n  showAuthStatus = false;\r\n  authStatusMessage = '';\r\n\r\n  heroConfig: HeroConfig = {\r\n    title: 'The Next Generation',\r\n    brandText: 'Software Engineering',\r\n    subtitle: 'Powered by AI. Crafted for engineers.',\r\n    description:\r\n      'Revolutionize your development workflow with intelligent Studios, autonomous Agents, and seamless Workflows that adapt to your team needs.',\r\n    imageSrc: 'marketplace-hero-image.png', // Updated path - files in public/ folder are served from root\r\n    imageAlt: 'AI Robot working on development tasks',\r\n    actions: [\r\n      {\r\n        label: 'Let’s hop in to AAVA',\r\n        variant: 'primary',\r\n        size: 'large',\r\n        action: () => this.signIn(),\r\n      },\r\n    ],\r\n    backgroundColor: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\r\n    textColor: 'white',\r\n    layout: 'default',\r\n  };\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private tokenStorage: TokenStorageService,\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    console.log('🏪 Marketplace component loaded');\r\n    this.checkAuthenticationStatus();\r\n  }\r\n\r\n  private checkAuthenticationStatus(): void {\r\n    const accessToken = this.tokenStorage.getAccessToken();\r\n    const refreshToken = this.tokenStorage.getRefreshToken();\r\n\r\n    if (accessToken) {\r\n      this.showAuthStatus = true;\r\n      // this.authStatusMessage =\r\n      //   '✅ You are already signed in. Click \"Sign In to Continue\" to access your dashboard.';\r\n    } else if (refreshToken) {\r\n      this.showAuthStatus = true;\r\n      // this.authStatusMessage =\r\n      //   '🔄 Session expired. Please sign in again to continue.';\r\n    } else {\r\n      this.showAuthStatus = true;\r\n      // this.authStatusMessage =\r\n      //   '👋 Welcome! Sign in to access your personalized AI workspace.';\r\n    }\r\n  }\r\n\r\n  signIn(): void {\r\n    console.log('🔐 Navigating to login...');\r\n    this.router.navigate(['/login']);\r\n  }\r\n\r\n  exploreMarketplace(): void {\r\n    // Smooth scroll to features section\r\n    const featuresSection = document.querySelector('.features-section');\r\n    if (featuresSection) {\r\n      featuresSection.scrollIntoView({\r\n        behavior: 'smooth',\r\n        block: 'start',\r\n      });\r\n    }\r\n  }\r\n}\r\n", "<div class=\"marketplace-container\">\r\n  <!-- Hero Section using reusable component -->\r\n  <app-hero-banner\r\n    [title]=\"heroConfig.title\"\r\n    [brandText]=\"heroConfig.brandText\"\r\n    [subtitle]=\"heroConfig.subtitle\"\r\n    [description]=\"heroConfig.description\"\r\n    [imageSrc]=\"heroConfig.imageSrc\"\r\n    [imageAlt]=\"heroConfig.imageAlt\"\r\n    [actions]=\"heroConfig.actions\"\r\n    [statusMessage]=\"authStatusMessage\"\r\n    [showStatus]=\"showAuthStatus\"\r\n    [backgroundColor]=\"heroConfig.backgroundColor\"\r\n    [textColor]=\"heroConfig.textColor\"\r\n    [layout]=\"heroConfig.layout\"\r\n  ></app-hero-banner>\r\n  \r\n  <!-- Marketplace Agents Section -->\r\n  <app-marketplace-agents-section></app-marketplace-agents-section>\r\n  \r\n  <!-- <app-agents></app-agents> -->\r\n  <!-- <div class=\"row\"><app-studios class=\"col-5\"></app-studios></div> -->\r\n  <app-clients-section></app-clients-section>\r\n  <app-analytics></app-analytics>\r\n  <app-testimonials-section></app-testimonials-section>\r\n  <app-news-blogs></app-news-blogs>\r\n  <app-marketplace-footer></app-marketplace-footer>\r\n</div>\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAG9C,SACEC,mBAAmB,QAEd,2DAA2D;AAClE,SAASC,0BAA0B,QAAQ,yEAAyE;AACpH,SAASC,kBAAkB,QAAQ,yDAAyD;AAC5F,SAASC,4BAA4B,QAAQ,6EAA6E;AAC1H,SAASC,uBAAuB,QAAQ,mEAAmE;AAC3G,SAASC,kBAAkB,QAAQ,uDAAuD;AAC1F,SAASC,iCAAiC,QAAQ,yFAAyF;;;;AA+B3I,WAAaC,oBAAoB;EAA3B,MAAOA,oBAAoB;IA0BrBC,MAAA;IACAC,YAAA;IA1BVC,cAAc,GAAG,KAAK;IACtBC,iBAAiB,GAAG,EAAE;IAEtBC,UAAU,GAAe;MACvBC,KAAK,EAAE,qBAAqB;MAC5BC,SAAS,EAAE,sBAAsB;MACjCC,QAAQ,EAAE,uCAAuC;MACjDC,WAAW,EACT,4IAA4I;MAC9IC,QAAQ,EAAE,4BAA4B;MAAE;MACxCC,QAAQ,EAAE,uCAAuC;MACjDC,OAAO,EAAE,CACP;QACEC,KAAK,EAAE,sBAAsB;QAC7BC,OAAO,EAAE,SAAS;QAClBC,IAAI,EAAE,OAAO;QACbC,MAAM,EAAEA,CAAA,KAAM,IAAI,CAACC,MAAM;OAC1B,CACF;MACDC,eAAe,EAAE,mDAAmD;MACpEC,SAAS,EAAE,OAAO;MAClBC,MAAM,EAAE;KACT;IAEDC,YACUpB,MAAc,EACdC,YAAiC;MADjC,KAAAD,MAAM,GAANA,MAAM;MACN,KAAAC,YAAY,GAAZA,YAAY;IACnB;IAEHoB,QAAQA,CAAA;MACNC,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;MAC9C,IAAI,CAACC,yBAAyB,EAAE;IAClC;IAEQA,yBAAyBA,CAAA;MAC/B,MAAMC,WAAW,GAAG,IAAI,CAACxB,YAAY,CAACyB,cAAc,EAAE;MACtD,MAAMC,YAAY,GAAG,IAAI,CAAC1B,YAAY,CAAC2B,eAAe,EAAE;MAExD,IAAIH,WAAW,EAAE;QACf,IAAI,CAACvB,cAAc,GAAG,IAAI;QAC1B;QACA;MACF,CAAC,MAAM,IAAIyB,YAAY,EAAE;QACvB,IAAI,CAACzB,cAAc,GAAG,IAAI;QAC1B;QACA;MACF,CAAC,MAAM;QACL,IAAI,CAACA,cAAc,GAAG,IAAI;QAC1B;QACA;MACF;IACF;IAEAc,MAAMA,CAAA;MACJM,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;MACxC,IAAI,CAACvB,MAAM,CAAC6B,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;IAClC;IAEAC,kBAAkBA,CAAA;MAChB;MACA,MAAMC,eAAe,GAAGC,QAAQ,CAACC,aAAa,CAAC,mBAAmB,CAAC;MACnE,IAAIF,eAAe,EAAE;QACnBA,eAAe,CAACG,cAAc,CAAC;UAC7BC,QAAQ,EAAE,QAAQ;UAClBC,KAAK,EAAE;SACR,CAAC;MACJ;IACF;;uCApEWrC,oBAAoB,EAAAsC,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,mBAAA;IAAA;;YAApB3C,oBAAoB;MAAA4C,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC5CjCZ,EAAA,CAAAc,cAAA,aAAmC;UA0BjCd,EAxBA,CAAAe,SAAA,yBAamB,qCAG8C,0BAItB,oBACZ,+BACsB,qBACpB,6BACgB;UACnDf,EAAA,CAAAgB,YAAA,EAAM;;;UAxBFhB,EAAA,CAAAiB,SAAA,EAA0B;UAW1BjB,EAXA,CAAAkB,UAAA,UAAAL,GAAA,CAAA9C,UAAA,CAAAC,KAAA,CAA0B,cAAA6C,GAAA,CAAA9C,UAAA,CAAAE,SAAA,CACQ,aAAA4C,GAAA,CAAA9C,UAAA,CAAAG,QAAA,CACF,gBAAA2C,GAAA,CAAA9C,UAAA,CAAAI,WAAA,CACM,aAAA0C,GAAA,CAAA9C,UAAA,CAAAK,QAAA,CACN,aAAAyC,GAAA,CAAA9C,UAAA,CAAAM,QAAA,CACA,YAAAwC,GAAA,CAAA9C,UAAA,CAAAO,OAAA,CACF,kBAAAuC,GAAA,CAAA/C,iBAAA,CACK,eAAA+C,GAAA,CAAAhD,cAAA,CACN,oBAAAgD,GAAA,CAAA9C,UAAA,CAAAa,eAAA,CACiB,cAAAiC,GAAA,CAAA9C,UAAA,CAAAc,SAAA,CACZ,WAAAgC,GAAA,CAAA9C,UAAA,CAAAe,MAAA,CACN;;;qBDkB5B5B,YAAY,EACZC,mBAAmB,EACnBC,0BAA0B,EAC1BC,kBAAkB,EAClBC,4BAA4B,EAC5BC,uBAAuB,EACvBC,kBAAkB,EAClBC,iCAAiC;MAAA0D,MAAA;IAAA;;SAKxBzD,oBAAoB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}