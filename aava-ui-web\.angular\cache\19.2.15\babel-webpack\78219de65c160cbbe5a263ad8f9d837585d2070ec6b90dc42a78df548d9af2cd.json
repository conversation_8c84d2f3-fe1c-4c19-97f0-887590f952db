{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport const inlineSuggestCommitId = 'editor.action.inlineSuggest.commit';\nexport const showPreviousInlineSuggestionActionId = 'editor.action.inlineSuggest.showPrevious';\nexport const showNextInlineSuggestionActionId = 'editor.action.inlineSuggest.showNext';", "map": {"version": 3, "names": ["inlineSuggestCommitId", "showPreviousInlineSuggestionActionId", "showNextInlineSuggestionActionId"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/contrib/inlineCompletions/browser/controller/commandIds.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport const inlineSuggestCommitId = 'editor.action.inlineSuggest.commit';\nexport const showPreviousInlineSuggestionActionId = 'editor.action.inlineSuggest.showPrevious';\nexport const showNextInlineSuggestionActionId = 'editor.action.inlineSuggest.showNext';\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,MAAMA,qBAAqB,GAAG,oCAAoC;AACzE,OAAO,MAAMC,oCAAoC,GAAG,0CAA0C;AAC9F,OAAO,MAAMC,gCAAgC,GAAG,sCAAsC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}