{"ast": null, "code": "import { $, append } from '../../dom.js';\nimport { BaseActionViewItem } from '../actionbar/actionViewItems.js';\nimport { DropdownMenu } from './dropdown.js';\nimport { Emitter } from '../../../common/event.js';\nimport './dropdown.css';\nimport { getDefaultHoverDelegate } from '../hover/hoverDelegateFactory.js';\nimport { getBaseLayerHoverDelegate } from '../hover/hoverDelegate2.js';\nexport class DropdownMenuActionViewItem extends BaseActionViewItem {\n  constructor(action, menuActionsOrProvider, contextMenuProvider, options = Object.create(null)) {\n    super(null, action, options);\n    this.actionItem = null;\n    this._onDidChangeVisibility = this._register(new Emitter());\n    this.onDidChangeVisibility = this._onDidChangeVisibility.event;\n    this.menuActionsOrProvider = menuActionsOrProvider;\n    this.contextMenuProvider = contextMenuProvider;\n    this.options = options;\n    if (this.options.actionRunner) {\n      this.actionRunner = this.options.actionRunner;\n    }\n  }\n  render(container) {\n    this.actionItem = container;\n    const labelRenderer = el => {\n      this.element = append(el, $('a.action-label'));\n      let classNames = [];\n      if (typeof this.options.classNames === 'string') {\n        classNames = this.options.classNames.split(/\\s+/g).filter(s => !!s);\n      } else if (this.options.classNames) {\n        classNames = this.options.classNames;\n      }\n      // todo@aeschli: remove codicon, should come through `this.options.classNames`\n      if (!classNames.find(c => c === 'icon')) {\n        classNames.push('codicon');\n      }\n      this.element.classList.add(...classNames);\n      this.element.setAttribute('role', 'button');\n      this.element.setAttribute('aria-haspopup', 'true');\n      this.element.setAttribute('aria-expanded', 'false');\n      if (this._action.label) {\n        this._register(getBaseLayerHoverDelegate().setupManagedHover(this.options.hoverDelegate ?? getDefaultHoverDelegate('mouse'), this.element, this._action.label));\n      }\n      this.element.ariaLabel = this._action.label || '';\n      return null;\n    };\n    const isActionsArray = Array.isArray(this.menuActionsOrProvider);\n    const options = {\n      contextMenuProvider: this.contextMenuProvider,\n      labelRenderer: labelRenderer,\n      menuAsChild: this.options.menuAsChild,\n      actions: isActionsArray ? this.menuActionsOrProvider : undefined,\n      actionProvider: isActionsArray ? undefined : this.menuActionsOrProvider,\n      skipTelemetry: this.options.skipTelemetry\n    };\n    this.dropdownMenu = this._register(new DropdownMenu(container, options));\n    this._register(this.dropdownMenu.onDidChangeVisibility(visible => {\n      this.element?.setAttribute('aria-expanded', `${visible}`);\n      this._onDidChangeVisibility.fire(visible);\n    }));\n    this.dropdownMenu.menuOptions = {\n      actionViewItemProvider: this.options.actionViewItemProvider,\n      actionRunner: this.actionRunner,\n      getKeyBinding: this.options.keybindingProvider,\n      context: this._context\n    };\n    if (this.options.anchorAlignmentProvider) {\n      const that = this;\n      this.dropdownMenu.menuOptions = {\n        ...this.dropdownMenu.menuOptions,\n        get anchorAlignment() {\n          return that.options.anchorAlignmentProvider();\n        }\n      };\n    }\n    this.updateTooltip();\n    this.updateEnabled();\n  }\n  getTooltip() {\n    let title = null;\n    if (this.action.tooltip) {\n      title = this.action.tooltip;\n    } else if (this.action.label) {\n      title = this.action.label;\n    }\n    return title ?? undefined;\n  }\n  setActionContext(newContext) {\n    super.setActionContext(newContext);\n    if (this.dropdownMenu) {\n      if (this.dropdownMenu.menuOptions) {\n        this.dropdownMenu.menuOptions.context = newContext;\n      } else {\n        this.dropdownMenu.menuOptions = {\n          context: newContext\n        };\n      }\n    }\n  }\n  show() {\n    this.dropdownMenu?.show();\n  }\n  updateEnabled() {\n    const disabled = !this.action.enabled;\n    this.actionItem?.classList.toggle('disabled', disabled);\n    this.element?.classList.toggle('disabled', disabled);\n  }\n}", "map": {"version": 3, "names": ["$", "append", "BaseActionViewItem", "DropdownMenu", "Emitter", "getDefaultHoverDelegate", "getBaseLayerHoverDelegate", "DropdownMenuActionViewItem", "constructor", "action", "menuActionsOrProvider", "contextMenuProvider", "options", "Object", "create", "actionItem", "_onDidChangeVisibility", "_register", "onDidChangeVisibility", "event", "actionRunner", "render", "container", "labelRenderer", "el", "element", "classNames", "split", "filter", "s", "find", "c", "push", "classList", "add", "setAttribute", "_action", "label", "setupManagedHover", "hoverDelegate", "aria<PERSON><PERSON><PERSON>", "isActionsArray", "Array", "isArray", "menuAsChild", "actions", "undefined", "actionProvider", "skipTelemetry", "dropdownMenu", "visible", "fire", "menuOptions", "actionViewItemProvider", "getKeyBinding", "keybindingProvider", "context", "_context", "anchorAlignmentProvider", "that", "anchorAlignment", "updateTooltip", "updateEnabled", "getTooltip", "title", "tooltip", "setActionContext", "newContext", "show", "disabled", "enabled", "toggle"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/browser/ui/dropdown/dropdownActionViewItem.js"], "sourcesContent": ["import { $, append } from '../../dom.js';\nimport { BaseActionViewItem } from '../actionbar/actionViewItems.js';\nimport { DropdownMenu } from './dropdown.js';\nimport { Emitter } from '../../../common/event.js';\nimport './dropdown.css';\nimport { getDefaultHoverDelegate } from '../hover/hoverDelegateFactory.js';\nimport { getBaseLayerHoverDelegate } from '../hover/hoverDelegate2.js';\nexport class DropdownMenuActionViewItem extends BaseActionViewItem {\n    constructor(action, menuActionsOrProvider, contextMenuProvider, options = Object.create(null)) {\n        super(null, action, options);\n        this.actionItem = null;\n        this._onDidChangeVisibility = this._register(new Emitter());\n        this.onDidChangeVisibility = this._onDidChangeVisibility.event;\n        this.menuActionsOrProvider = menuActionsOrProvider;\n        this.contextMenuProvider = contextMenuProvider;\n        this.options = options;\n        if (this.options.actionRunner) {\n            this.actionRunner = this.options.actionRunner;\n        }\n    }\n    render(container) {\n        this.actionItem = container;\n        const labelRenderer = (el) => {\n            this.element = append(el, $('a.action-label'));\n            let classNames = [];\n            if (typeof this.options.classNames === 'string') {\n                classNames = this.options.classNames.split(/\\s+/g).filter(s => !!s);\n            }\n            else if (this.options.classNames) {\n                classNames = this.options.classNames;\n            }\n            // todo@aeschli: remove codicon, should come through `this.options.classNames`\n            if (!classNames.find(c => c === 'icon')) {\n                classNames.push('codicon');\n            }\n            this.element.classList.add(...classNames);\n            this.element.setAttribute('role', 'button');\n            this.element.setAttribute('aria-haspopup', 'true');\n            this.element.setAttribute('aria-expanded', 'false');\n            if (this._action.label) {\n                this._register(getBaseLayerHoverDelegate().setupManagedHover(this.options.hoverDelegate ?? getDefaultHoverDelegate('mouse'), this.element, this._action.label));\n            }\n            this.element.ariaLabel = this._action.label || '';\n            return null;\n        };\n        const isActionsArray = Array.isArray(this.menuActionsOrProvider);\n        const options = {\n            contextMenuProvider: this.contextMenuProvider,\n            labelRenderer: labelRenderer,\n            menuAsChild: this.options.menuAsChild,\n            actions: isActionsArray ? this.menuActionsOrProvider : undefined,\n            actionProvider: isActionsArray ? undefined : this.menuActionsOrProvider,\n            skipTelemetry: this.options.skipTelemetry\n        };\n        this.dropdownMenu = this._register(new DropdownMenu(container, options));\n        this._register(this.dropdownMenu.onDidChangeVisibility(visible => {\n            this.element?.setAttribute('aria-expanded', `${visible}`);\n            this._onDidChangeVisibility.fire(visible);\n        }));\n        this.dropdownMenu.menuOptions = {\n            actionViewItemProvider: this.options.actionViewItemProvider,\n            actionRunner: this.actionRunner,\n            getKeyBinding: this.options.keybindingProvider,\n            context: this._context\n        };\n        if (this.options.anchorAlignmentProvider) {\n            const that = this;\n            this.dropdownMenu.menuOptions = {\n                ...this.dropdownMenu.menuOptions,\n                get anchorAlignment() {\n                    return that.options.anchorAlignmentProvider();\n                }\n            };\n        }\n        this.updateTooltip();\n        this.updateEnabled();\n    }\n    getTooltip() {\n        let title = null;\n        if (this.action.tooltip) {\n            title = this.action.tooltip;\n        }\n        else if (this.action.label) {\n            title = this.action.label;\n        }\n        return title ?? undefined;\n    }\n    setActionContext(newContext) {\n        super.setActionContext(newContext);\n        if (this.dropdownMenu) {\n            if (this.dropdownMenu.menuOptions) {\n                this.dropdownMenu.menuOptions.context = newContext;\n            }\n            else {\n                this.dropdownMenu.menuOptions = { context: newContext };\n            }\n        }\n    }\n    show() {\n        this.dropdownMenu?.show();\n    }\n    updateEnabled() {\n        const disabled = !this.action.enabled;\n        this.actionItem?.classList.toggle('disabled', disabled);\n        this.element?.classList.toggle('disabled', disabled);\n    }\n}\n"], "mappings": "AAAA,SAASA,CAAC,EAAEC,MAAM,QAAQ,cAAc;AACxC,SAASC,kBAAkB,QAAQ,iCAAiC;AACpE,SAASC,YAAY,QAAQ,eAAe;AAC5C,SAASC,OAAO,QAAQ,0BAA0B;AAClD,OAAO,gBAAgB;AACvB,SAASC,uBAAuB,QAAQ,kCAAkC;AAC1E,SAASC,yBAAyB,QAAQ,4BAA4B;AACtE,OAAO,MAAMC,0BAA0B,SAASL,kBAAkB,CAAC;EAC/DM,WAAWA,CAACC,MAAM,EAAEC,qBAAqB,EAAEC,mBAAmB,EAAEC,OAAO,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC,EAAE;IAC3F,KAAK,CAAC,IAAI,EAAEL,MAAM,EAAEG,OAAO,CAAC;IAC5B,IAAI,CAACG,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,sBAAsB,GAAG,IAAI,CAACC,SAAS,CAAC,IAAIb,OAAO,CAAC,CAAC,CAAC;IAC3D,IAAI,CAACc,qBAAqB,GAAG,IAAI,CAACF,sBAAsB,CAACG,KAAK;IAC9D,IAAI,CAACT,qBAAqB,GAAGA,qBAAqB;IAClD,IAAI,CAACC,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,IAAI,CAACA,OAAO,CAACQ,YAAY,EAAE;MAC3B,IAAI,CAACA,YAAY,GAAG,IAAI,CAACR,OAAO,CAACQ,YAAY;IACjD;EACJ;EACAC,MAAMA,CAACC,SAAS,EAAE;IACd,IAAI,CAACP,UAAU,GAAGO,SAAS;IAC3B,MAAMC,aAAa,GAAIC,EAAE,IAAK;MAC1B,IAAI,CAACC,OAAO,GAAGxB,MAAM,CAACuB,EAAE,EAAExB,CAAC,CAAC,gBAAgB,CAAC,CAAC;MAC9C,IAAI0B,UAAU,GAAG,EAAE;MACnB,IAAI,OAAO,IAAI,CAACd,OAAO,CAACc,UAAU,KAAK,QAAQ,EAAE;QAC7CA,UAAU,GAAG,IAAI,CAACd,OAAO,CAACc,UAAU,CAACC,KAAK,CAAC,MAAM,CAAC,CAACC,MAAM,CAACC,CAAC,IAAI,CAAC,CAACA,CAAC,CAAC;MACvE,CAAC,MACI,IAAI,IAAI,CAACjB,OAAO,CAACc,UAAU,EAAE;QAC9BA,UAAU,GAAG,IAAI,CAACd,OAAO,CAACc,UAAU;MACxC;MACA;MACA,IAAI,CAACA,UAAU,CAACI,IAAI,CAACC,CAAC,IAAIA,CAAC,KAAK,MAAM,CAAC,EAAE;QACrCL,UAAU,CAACM,IAAI,CAAC,SAAS,CAAC;MAC9B;MACA,IAAI,CAACP,OAAO,CAACQ,SAAS,CAACC,GAAG,CAAC,GAAGR,UAAU,CAAC;MACzC,IAAI,CAACD,OAAO,CAACU,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC;MAC3C,IAAI,CAACV,OAAO,CAACU,YAAY,CAAC,eAAe,EAAE,MAAM,CAAC;MAClD,IAAI,CAACV,OAAO,CAACU,YAAY,CAAC,eAAe,EAAE,OAAO,CAAC;MACnD,IAAI,IAAI,CAACC,OAAO,CAACC,KAAK,EAAE;QACpB,IAAI,CAACpB,SAAS,CAACX,yBAAyB,CAAC,CAAC,CAACgC,iBAAiB,CAAC,IAAI,CAAC1B,OAAO,CAAC2B,aAAa,IAAIlC,uBAAuB,CAAC,OAAO,CAAC,EAAE,IAAI,CAACoB,OAAO,EAAE,IAAI,CAACW,OAAO,CAACC,KAAK,CAAC,CAAC;MACnK;MACA,IAAI,CAACZ,OAAO,CAACe,SAAS,GAAG,IAAI,CAACJ,OAAO,CAACC,KAAK,IAAI,EAAE;MACjD,OAAO,IAAI;IACf,CAAC;IACD,MAAMI,cAAc,GAAGC,KAAK,CAACC,OAAO,CAAC,IAAI,CAACjC,qBAAqB,CAAC;IAChE,MAAME,OAAO,GAAG;MACZD,mBAAmB,EAAE,IAAI,CAACA,mBAAmB;MAC7CY,aAAa,EAAEA,aAAa;MAC5BqB,WAAW,EAAE,IAAI,CAAChC,OAAO,CAACgC,WAAW;MACrCC,OAAO,EAAEJ,cAAc,GAAG,IAAI,CAAC/B,qBAAqB,GAAGoC,SAAS;MAChEC,cAAc,EAAEN,cAAc,GAAGK,SAAS,GAAG,IAAI,CAACpC,qBAAqB;MACvEsC,aAAa,EAAE,IAAI,CAACpC,OAAO,CAACoC;IAChC,CAAC;IACD,IAAI,CAACC,YAAY,GAAG,IAAI,CAAChC,SAAS,CAAC,IAAId,YAAY,CAACmB,SAAS,EAAEV,OAAO,CAAC,CAAC;IACxE,IAAI,CAACK,SAAS,CAAC,IAAI,CAACgC,YAAY,CAAC/B,qBAAqB,CAACgC,OAAO,IAAI;MAC9D,IAAI,CAACzB,OAAO,EAAEU,YAAY,CAAC,eAAe,EAAE,GAAGe,OAAO,EAAE,CAAC;MACzD,IAAI,CAAClC,sBAAsB,CAACmC,IAAI,CAACD,OAAO,CAAC;IAC7C,CAAC,CAAC,CAAC;IACH,IAAI,CAACD,YAAY,CAACG,WAAW,GAAG;MAC5BC,sBAAsB,EAAE,IAAI,CAACzC,OAAO,CAACyC,sBAAsB;MAC3DjC,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/BkC,aAAa,EAAE,IAAI,CAAC1C,OAAO,CAAC2C,kBAAkB;MAC9CC,OAAO,EAAE,IAAI,CAACC;IAClB,CAAC;IACD,IAAI,IAAI,CAAC7C,OAAO,CAAC8C,uBAAuB,EAAE;MACtC,MAAMC,IAAI,GAAG,IAAI;MACjB,IAAI,CAACV,YAAY,CAACG,WAAW,GAAG;QAC5B,GAAG,IAAI,CAACH,YAAY,CAACG,WAAW;QAChC,IAAIQ,eAAeA,CAAA,EAAG;UAClB,OAAOD,IAAI,CAAC/C,OAAO,CAAC8C,uBAAuB,CAAC,CAAC;QACjD;MACJ,CAAC;IACL;IACA,IAAI,CAACG,aAAa,CAAC,CAAC;IACpB,IAAI,CAACC,aAAa,CAAC,CAAC;EACxB;EACAC,UAAUA,CAAA,EAAG;IACT,IAAIC,KAAK,GAAG,IAAI;IAChB,IAAI,IAAI,CAACvD,MAAM,CAACwD,OAAO,EAAE;MACrBD,KAAK,GAAG,IAAI,CAACvD,MAAM,CAACwD,OAAO;IAC/B,CAAC,MACI,IAAI,IAAI,CAACxD,MAAM,CAAC4B,KAAK,EAAE;MACxB2B,KAAK,GAAG,IAAI,CAACvD,MAAM,CAAC4B,KAAK;IAC7B;IACA,OAAO2B,KAAK,IAAIlB,SAAS;EAC7B;EACAoB,gBAAgBA,CAACC,UAAU,EAAE;IACzB,KAAK,CAACD,gBAAgB,CAACC,UAAU,CAAC;IAClC,IAAI,IAAI,CAAClB,YAAY,EAAE;MACnB,IAAI,IAAI,CAACA,YAAY,CAACG,WAAW,EAAE;QAC/B,IAAI,CAACH,YAAY,CAACG,WAAW,CAACI,OAAO,GAAGW,UAAU;MACtD,CAAC,MACI;QACD,IAAI,CAAClB,YAAY,CAACG,WAAW,GAAG;UAAEI,OAAO,EAAEW;QAAW,CAAC;MAC3D;IACJ;EACJ;EACAC,IAAIA,CAAA,EAAG;IACH,IAAI,CAACnB,YAAY,EAAEmB,IAAI,CAAC,CAAC;EAC7B;EACAN,aAAaA,CAAA,EAAG;IACZ,MAAMO,QAAQ,GAAG,CAAC,IAAI,CAAC5D,MAAM,CAAC6D,OAAO;IACrC,IAAI,CAACvD,UAAU,EAAEkB,SAAS,CAACsC,MAAM,CAAC,UAAU,EAAEF,QAAQ,CAAC;IACvD,IAAI,CAAC5C,OAAO,EAAEQ,SAAS,CAACsC,MAAM,CAAC,UAAU,EAAEF,QAAQ,CAAC;EACxD;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}