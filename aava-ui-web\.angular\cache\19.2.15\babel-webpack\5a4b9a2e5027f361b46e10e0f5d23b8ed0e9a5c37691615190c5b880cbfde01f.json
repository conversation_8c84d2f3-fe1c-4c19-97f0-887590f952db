{"ast": null, "code": "import { CommonModule, formatDate } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { startWith, debounceTime, distinctUntilChanged, map } from 'rxjs';\nimport approvalText from './constants/approval.json';\nimport { LucideAngularModule } from 'lucide-angular';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../shared/services/shared-api-service.service\";\nimport * as i3 from \"../../shared/services/approval.service\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@ava/play-comp-library\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"lucide-angular\";\nfunction ApprovalComponent_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 9);\n    i0.ɵɵtext(1, \"Category\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ApprovalComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"div\", 11)(2, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function ApprovalComponent_div_6_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.redirectToListOfApproval(ctx_r1.labels.agents));\n    });\n    i0.ɵɵelementStart(3, \"div\", 13);\n    i0.ɵɵelement(4, \"img\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 15);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function ApprovalComponent_div_6_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.redirectToListOfApproval(ctx_r1.labels.workflows));\n    });\n    i0.ɵɵelementStart(8, \"div\", 13);\n    i0.ɵɵelement(9, \"img\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\", 15);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function ApprovalComponent_div_6_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.redirectToListOfApproval(ctx_r1.labels.tools));\n    });\n    i0.ɵɵelementStart(13, \"div\", 13);\n    i0.ɵɵelement(14, \"img\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\", 15);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"active-action\", ctx_r1.currentTab === ctx_r1.labels.agents);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"title\", ctx_r1.labels.agents);\n    i0.ɵɵproperty(\"src\", \"svgs/icons/\" + \"awe_agents\" + \".svg\", i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.labels.agents);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.labels.agents);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active-action\", ctx_r1.currentTab === ctx_r1.labels.workflows);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"title\", ctx_r1.labels.workflows);\n    i0.ɵɵproperty(\"src\", \"svgs/icons/\" + \"awe_workflows\" + \".svg\", i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.labels.workflows);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.labels.workflows);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active-action\", ctx_r1.currentTab === ctx_r1.labels.tools);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"title\", ctx_r1.labels.tools);\n    i0.ɵɵproperty(\"src\", \"svgs/icons/\" + \"awe_tools\" + \".svg\", i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.labels.tools);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.labels.tools);\n  }\n}\nfunction ApprovalComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function ApprovalComponent_div_7_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.redirectToListOfApproval(ctx_r1.labels.agents));\n    });\n    i0.ɵɵelementStart(2, \"div\", 18);\n    i0.ɵɵelement(3, \"img\", 14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function ApprovalComponent_div_7_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.redirectToListOfApproval(ctx_r1.labels.workflows));\n    });\n    i0.ɵɵelementStart(5, \"div\", 18);\n    i0.ɵɵelement(6, \"img\", 14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function ApprovalComponent_div_7_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.redirectToListOfApproval(ctx_r1.labels.tools));\n    });\n    i0.ɵɵelementStart(8, \"div\", 18);\n    i0.ɵɵelement(9, \"img\", 14);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active-action\", ctx_r1.currentTab === ctx_r1.labels.agents);\n    i0.ɵɵpropertyInterpolate(\"title\", ctx_r1.labels.agents);\n    i0.ɵɵproperty(\"title\", ctx_r1.labels.agents);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"title\", ctx_r1.labels.agents);\n    i0.ɵɵproperty(\"src\", \"svgs/icons/\" + \"awe_agents\" + \".svg\", i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.labels.agents);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active-action\", ctx_r1.currentTab === ctx_r1.labels.workflows);\n    i0.ɵɵpropertyInterpolate(\"title\", ctx_r1.labels.workflows);\n    i0.ɵɵproperty(\"title\", ctx_r1.labels.workflows);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"title\", ctx_r1.labels.workflows);\n    i0.ɵɵproperty(\"src\", \"svgs/icons/\" + \"awe_workflows\" + \".svg\", i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.labels.workflows);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active-action\", ctx_r1.currentTab === ctx_r1.labels.tools);\n    i0.ɵɵpropertyInterpolate(\"title\", ctx_r1.labels.tools);\n    i0.ɵɵproperty(\"title\", ctx_r1.labels.tools);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"title\", ctx_r1.labels.tools);\n    i0.ɵɵproperty(\"src\", \"svgs/icons/\" + \"awe_tools\" + \".svg\", i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.labels.tools);\n  }\n}\nexport let ApprovalComponent = /*#__PURE__*/(() => {\n  class ApprovalComponent {\n    router;\n    route;\n    apiService;\n    approvalService;\n    fb;\n    dialogService;\n    // Labels from constants file\n    appLabels = approvalText.labels;\n    searchValue = '';\n    totalApprovedApprovals = 20;\n    totalPendingApprovals = 15;\n    totalApprovals = 60;\n    isBasicCollapsed = false;\n    quickActionsExpanded = true;\n    consoleApproval = {};\n    options = [];\n    basicSidebarItems = [];\n    quickActions = [];\n    toolReviews = [];\n    workflowReviews = [];\n    agentsReviews = [];\n    currentToolsPage = 1;\n    currentAgentsPage = 1;\n    currentWorkflowsPage = 1;\n    pageSize = 50;\n    totalRecords = 0;\n    isDeleted = false;\n    currentTab = '';\n    showToolApprovalPopup = false;\n    showInfoPopup = false;\n    showErrorPopup = false;\n    infoMessage = '';\n    selectedIndex = 0;\n    showFeedbackPopup = false;\n    searchForm;\n    labels = approvalText.labels;\n    approvedAgentId = null;\n    constructor(router, route, apiService, approvalService, fb, dialogService) {\n      this.router = router;\n      this.route = route;\n      this.apiService = apiService;\n      this.approvalService = approvalService;\n      this.fb = fb;\n      this.dialogService = dialogService;\n      this.labels = approvalText.labels;\n      this.options = [{\n        name: this.labels.electronics,\n        value: 'electronics'\n      }, {\n        name: this.labels.clothing,\n        value: 'clothing'\n      }, {\n        name: this.labels.books,\n        value: 'books'\n      }];\n      this.basicSidebarItems = [{\n        id: '1',\n        icon: 'hammer',\n        text: this.labels.agents,\n        route: '',\n        active: true\n      }, {\n        id: '2',\n        icon: 'circle-check',\n        text: this.labels.workflows,\n        route: ''\n      }, {\n        id: '3',\n        icon: 'bot',\n        text: this.labels.tools,\n        route: ''\n      }];\n      this.quickActions = [{\n        icon: 'awe_agents',\n        label: this.labels.agents,\n        route: ''\n      }, {\n        icon: 'awe_workflows',\n        label: this.labels.workflows,\n        route: ''\n      }, {\n        icon: 'awe_tools',\n        label: this.labels.tools,\n        route: ''\n      }];\n      this.searchForm = this.fb.group({\n        search: ['']\n      });\n    }\n    ngOnInit() {\n      this.searchList();\n      this.totalApprovals = 60;\n      // Initialize currentTab to the first option (Agents)\n      this.currentTab = this.labels.agents;\n      if (this.router.url.includes('approval-agents')) {\n        this.currentTab = this.labels.agents;\n      } else if (this.router.url.includes('approval-tools')) {\n        this.currentTab = this.labels.tools;\n      } else if (this.router.url.includes('approval-workflows')) {\n        this.currentTab = this.labels.workflows;\n      }\n    }\n    searchList() {\n      console.log(this.searchForm.get('search')?.value);\n      this.searchForm.get('search').valueChanges.pipe(startWith(''), debounceTime(300), distinctUntilChanged(), map(value => value?.toLowerCase() ?? '')).subscribe(searchText => {\n        this.applyFilter(searchText);\n      });\n    }\n    applyFilter(text) {\n      const lower = text;\n      if (!this.searchValue) {\n        if (this.currentTab === 'Agents') {\n          this.updateConsoleApproval(this.agentsReviews, 'agent');\n        } else if (this.currentTab === 'Tools') {\n          this.updateConsoleApproval(this.toolReviews, 'tool');\n        } else {\n          this.updateConsoleApproval(this.workflowReviews, 'workflow');\n        }\n        return;\n      }\n      if (this.currentTab === 'Agents') {\n        const filtered = this.agentsReviews.filter(item => item.agentName?.toLowerCase().includes(lower));\n        this.updateConsoleApproval(filtered, 'agent');\n      } else if (this.currentTab === 'Tools') {\n        const filtered = this.toolReviews.filter(item => item.toolName?.toLowerCase().includes(lower));\n        this.updateConsoleApproval(filtered, 'tool');\n      } else {\n        const filtered = this.workflowReviews.filter(item => item.name?.toLowerCase().includes(lower));\n        this.updateConsoleApproval(filtered, 'workflow');\n      }\n    }\n    onSelectionChange(data) {\n      console.log('Selection changed:', data);\n    }\n    uClick(i) {\n      console.log('log' + i);\n    }\n    toggleQuickActions() {\n      this.quickActionsExpanded = !this.quickActionsExpanded;\n    }\n    onBasicCollapseToggle(isCollapsed) {\n      this.isBasicCollapsed = isCollapsed;\n      console.log('Basic sidebar collapsed:', isCollapsed);\n    }\n    onBasicItemClick(item) {\n      this.basicSidebarItems.forEach(i => i.active = false);\n      item.active = true;\n      console.log(item);\n    }\n    toRequestStatus(value) {\n      return value === 'approved' || value === 'rejected' || value === 'review' ? value : 'review';\n    }\n    loadToolReviews() {\n      this.approvalService.getAllReviewTools(this.currentToolsPage, this.pageSize, this.isDeleted).subscribe(response => {\n        if (this.currentToolsPage > 1) {\n          this.toolReviews = [...this.toolReviews, ...response.userToolReviewDetails];\n        } else {\n          this.toolReviews = response?.userToolReviewDetails;\n        }\n        this.toolReviews = this.toolReviews.filter(r => r.status !== 'approved');\n        this.totalRecords = this.toolReviews.length;\n        this.updateConsoleApproval(this.toolReviews, 'tool');\n      });\n    }\n    loadWorkflowReviews() {\n      this.approvalService.getAllReviewWorkflows(this.currentWorkflowsPage, this.pageSize, this.isDeleted).subscribe(response => {\n        if (this.currentWorkflowsPage > 1) {\n          this.workflowReviews = [...this.workflowReviews, ...response.workflowReviewDetails];\n        } else {\n          this.workflowReviews = response?.workflowReviewDetails;\n        }\n        this.workflowReviews = this.workflowReviews.filter(r => r.status !== 'approved');\n        this.totalRecords = this.workflowReviews.length;\n        console.log('reviews ', this.workflowReviews);\n        this.updateConsoleApproval(this.workflowReviews, 'workflow');\n      });\n    }\n    loadAgentsReviews() {\n      this.approvalService.getAllReviewAgents(this.currentAgentsPage, this.pageSize, this.isDeleted).subscribe(response => {\n        if (this.currentAgentsPage > 1) {\n          this.agentsReviews = [...this.agentsReviews, ...response.agentReviewDetails];\n        } else {\n          this.agentsReviews = response?.agentReviewDetails;\n        }\n        this.agentsReviews = this.agentsReviews.filter(r => r.status !== 'approved');\n        this.totalRecords = this.agentsReviews.length;\n        this.updateConsoleApproval(this.agentsReviews, 'agent');\n      });\n    }\n    loadMoreTools(page) {\n      this.currentToolsPage = page;\n      this.loadToolReviews();\n    }\n    loadMoreAgents(page) {\n      this.currentAgentsPage = page;\n      this.loadAgentsReviews();\n    }\n    loadMoreWorkflows(page) {\n      this.currentWorkflowsPage = page;\n      this.loadWorkflowReviews();\n    }\n    loadReviews(name) {\n      this.currentTab = name;\n      if (name == 'Tools') {\n        this.loadToolReviews();\n      } else if (name == 'Agents') {\n        this.loadAgentsReviews();\n      } else {\n        this.loadWorkflowReviews();\n      }\n    }\n    redirectToListOfApproval(name) {\n      console.log('Redirecting to:', name);\n      console.log('Current labels:', this.labels);\n      this.currentTab = name;\n      console.log('Updated currentTab to:', this.currentTab);\n      if (name === this.labels.tools) {\n        this.router.navigate(['approval-tools'], {\n          relativeTo: this.route\n        });\n      } else if (name === this.labels.agents) {\n        this.router.navigate(['approval-agents'], {\n          relativeTo: this.route\n        });\n      } else if (name === this.labels.workflows) {\n        this.router.navigate(['approval-workflows'], {\n          relativeTo: this.route\n        });\n      }\n    }\n    rejectApproval(idx) {\n      console.log(idx);\n      this.selectedIndex = idx;\n      this.showFeedbackDialog();\n    }\n    approveApproval(idx) {\n      console.log(idx);\n      this.selectedIndex = idx;\n      this.showApprovalDialog();\n    }\n    // Replace popup methods with DialogService methods\n    showApprovalDialog() {\n      this.dialogService.confirmation({\n        title: this.labels.confirmApproval,\n        message: `${this.labels.youAreAboutToApproveThis} ${this.currentTab}. ${this.labels.itWillBeActiveAndAvailableIn} ${this.currentTab} ${this.labels.catalogueForUsersToExecute}`,\n        confirmButtonText: this.labels.approve,\n        cancelButtonText: 'Cancel',\n        confirmButtonVariant: 'danger',\n        icon: 'circle-check'\n      }).then(result => {\n        if (result.confirmed) {\n          this.handleApproval();\n        }\n      });\n    }\n    showFeedbackDialog() {\n      const customButtons = [{\n        label: 'Cancel',\n        variant: 'secondary',\n        action: 'cancel'\n      }, {\n        label: 'Send Back',\n        variant: 'primary',\n        action: 'sendback'\n      }];\n      this.dialogService.feedback({\n        title: 'Confirm Send Back',\n        message: `This ${this.currentTab} will be send back for corrections and modification. Kindly comment what needs to be done.`,\n        buttons: customButtons\n      }).then(result => {\n        if (result.confirmed && result.confirmed === true) {\n          this.handleRejection(result.data);\n        }\n      });\n    }\n    handleTesting(index) {\n      console.log(index);\n      if (this.currentTab == 'Tools') {\n        const toolId = this.toolReviews[index].toolId;\n        this.redirectToToolPlayground(toolId);\n      } else if (this.currentTab == 'Agents') {\n        const agentId = this.agentsReviews[index].agentId;\n        this.redirectToAgentsPlayground(agentId, 'collaborative');\n      } else {\n        const workflowId = this.workflowReviews[index].workflowId;\n        this.redirectToWorkflowPlayground(workflowId);\n      }\n    }\n    redirectToToolPlayground(id) {\n      this.router.navigate(['/libraries/tools/execute', id]);\n    }\n    redirectToWorkflowPlayground(id) {\n      this.router.navigate(['/build/workflows/execute', id]);\n    }\n    redirectToAgentsPlayground(id, type) {\n      this.router.navigate(['/build/agents', type, 'execute'], {\n        queryParams: {\n          id: id\n        }\n      });\n    }\n    handleApproval() {\n      if (this.currentTab == 'Tools') {\n        this.handleToolApproval();\n      } else if (this.currentTab == 'Agents') {\n        this.handleAgentApproval();\n      } else {\n        this.handleWorkflowApproval();\n      }\n    }\n    handleRejection(feedback) {\n      if (this.currentTab == 'Tools') {\n        this.handleToolRejection(feedback);\n      } else if (this.currentTab == 'Agents') {\n        this.handleAgentRejection(feedback);\n      } else {\n        this.handleWorkflowRejection(feedback);\n      }\n    }\n    handleToolApproval() {\n      const toolDetails = this.toolReviews[this.selectedIndex];\n      const id = toolDetails.id;\n      const toolId = toolDetails.toolId;\n      const status = 'approved';\n      const reviewedBy = toolDetails.reviewedBy;\n      // Show loading dialog\n      this.dialogService.loading({\n        title: 'Approving Tool...',\n        message: 'Please wait while we approve the tool.',\n        showProgress: false,\n        showCancelButton: false\n      });\n      this.approvalService.approveTool(id, toolId, status, reviewedBy).subscribe({\n        next: response => {\n          this.dialogService.close(); // Close loading dialog\n          const message = response?.message || this.labels.toolSuccessApproveMessage;\n          this.dialogService.success({\n            title: 'Tool Approved',\n            message: message\n          }).then(() => {\n            this.loadToolReviews(); // Refresh the list\n          });\n        },\n        error: error => {\n          this.dialogService.close(); // Close loading dialog\n          const errorMessage = error?.error?.message || this.labels.defaultErrorMessage;\n          this.dialogService.error({\n            title: 'Approval Failed',\n            message: errorMessage,\n            showRetryButton: true,\n            retryButtonText: 'Retry'\n          }).then(result => {\n            if (result.action === 'retry') {\n              this.handleToolApproval();\n            }\n          });\n        }\n      });\n    }\n    handleToolRejection(feedback) {\n      const toolDetails = this.toolReviews[this.selectedIndex];\n      const id = toolDetails.id;\n      const toolId = toolDetails.toolId;\n      const status = 'rejected';\n      const reviewedBy = toolDetails.reviewedBy;\n      const message = feedback;\n      // Show loading dialog\n      this.dialogService.loading({\n        title: 'Rejecting Tool...',\n        message: 'Please wait while we reject the tool.',\n        showProgress: false,\n        showCancelButton: false\n      });\n      this.approvalService.rejectTool(id, toolId, status, reviewedBy, message).subscribe({\n        next: response => {\n          this.dialogService.close(); // Close loading dialog\n          const message = response?.message || this.labels.toolSuccessRejectMessage;\n          this.dialogService.success({\n            title: 'Tool Rejected',\n            message: message\n          }).then(() => {\n            this.loadToolReviews(); // Refresh the list\n          });\n        },\n        error: error => {\n          this.dialogService.close(); // Close loading dialog\n          const errorMessage = error?.error?.message || this.labels.defaultErrorMessage;\n          this.dialogService.error({\n            title: 'Rejection Failed',\n            message: errorMessage,\n            showRetryButton: true,\n            retryButtonText: 'Retry'\n          }).then(result => {\n            if (result.action === 'retry') {\n              this.handleToolRejection(feedback);\n            }\n          });\n        }\n      });\n    }\n    handleAgentApproval() {\n      const agentDetails = this.agentsReviews[this.selectedIndex];\n      const id = agentDetails.id;\n      const agentId = agentDetails.agentId;\n      const status = 'approved';\n      const reviewedBy = agentDetails.reviewedBy;\n      // Show loading dialog\n      this.dialogService.loading({\n        title: 'Approving Agent...',\n        message: 'Please wait while we approve the agent.',\n        showProgress: false,\n        showCancelButton: false\n      });\n      this.approvalService.approveAgent(id, agentId, status, reviewedBy).subscribe({\n        next: response => {\n          this.dialogService.close(); // Close loading dialog\n          const message = response?.message || this.labels.agentSuccessApproveMessage;\n          // Store agent ID for navigation after popup confirmation\n          this.approvedAgentId = agentId;\n          this.dialogService.success({\n            title: 'Agent Approved',\n            message: message\n          }).then(result => {\n            if (result.action === 'secondary') {\n              // Navigate to build agent screen with the approved agent ID\n              this.router.navigate(['/build/agents/collaborative'], {\n                queryParams: {\n                  id: this.approvedAgentId,\n                  mode: 'edit'\n                }\n              });\n            } else {\n              this.loadAgentsReviews(); // Refresh the list\n            }\n            // Reset the approved agent ID\n            this.approvedAgentId = null;\n          });\n        },\n        error: error => {\n          this.dialogService.close(); // Close loading dialog\n          const errorMessage = error?.error?.message || this.labels.defaultErrorMessage;\n          this.dialogService.error({\n            title: 'Approval Failed',\n            message: errorMessage,\n            showRetryButton: true,\n            retryButtonText: 'Retry'\n          }).then(result => {\n            if (result.action === 'retry') {\n              this.handleAgentApproval();\n            }\n          });\n        }\n      });\n    }\n    handleAgentRejection(feedback) {\n      const agentDetails = this.agentsReviews[this.selectedIndex];\n      const id = agentDetails.id;\n      const agentId = agentDetails.agentId;\n      const status = 'rejected';\n      const reviewedBy = agentDetails.reviewedBy;\n      const message = feedback;\n      // Show loading dialog\n      this.dialogService.loading({\n        title: 'Rejecting Agent...',\n        message: 'Please wait while we reject the agent.',\n        showProgress: false,\n        showCancelButton: false\n      });\n      this.approvalService.rejectAgent(id, agentId, status, reviewedBy, message).subscribe({\n        next: response => {\n          this.dialogService.close(); // Close loading dialog\n          const message = response?.message || this.labels.agentSuccessRejectMessage;\n          this.dialogService.success({\n            title: 'Agent Rejected',\n            message: message\n          }).then(() => {\n            this.loadAgentsReviews(); // Refresh the list\n          });\n        },\n        error: error => {\n          this.dialogService.close(); // Close loading dialog\n          const errorMessage = error?.error?.message || this.labels.defaultErrorMessage;\n          this.dialogService.error({\n            title: 'Rejection Failed',\n            message: errorMessage,\n            showRetryButton: true,\n            retryButtonText: 'Retry'\n          }).then(result => {\n            if (result.action === 'retry') {\n              this.handleAgentRejection(feedback);\n            }\n          });\n        }\n      });\n    }\n    handleWorkflowApproval() {\n      const workflowDetails = this.workflowReviews[this.selectedIndex];\n      const id = workflowDetails?.id;\n      const workflowId = workflowDetails?.workflowId;\n      const status = 'approved';\n      const reviewedBy = workflowDetails?.reviewedBy;\n      console.log(id, workflowId, status, reviewedBy);\n      // Show loading dialog\n      this.dialogService.loading({\n        title: 'Approving Workflow...',\n        message: 'Please wait while we approve the workflow.',\n        showProgress: false,\n        showCancelButton: false\n      });\n      this.approvalService.approveWorkflow(id, workflowId, status, reviewedBy).subscribe({\n        next: response => {\n          this.dialogService.close(); // Close loading dialog\n          const message = response?.message || this.labels.workflowSuccessApproveMessage;\n          this.dialogService.success({\n            title: 'Workflow Approved',\n            message: message\n          }).then(() => {\n            this.loadWorkflowReviews(); // Refresh the list\n          });\n        },\n        error: error => {\n          this.dialogService.close(); // Close loading dialog\n          const errorMessage = error?.error?.message || this.labels.defaultErrorMessage;\n          this.dialogService.error({\n            title: 'Approval Failed',\n            message: errorMessage,\n            showRetryButton: true,\n            retryButtonText: 'Retry'\n          }).then(result => {\n            if (result.action === 'retry') {\n              this.handleWorkflowApproval();\n            }\n          });\n        }\n      });\n    }\n    handleWorkflowRejection(feedback) {\n      const workflowDetails = this.workflowReviews[this.selectedIndex];\n      const id = workflowDetails?.id;\n      const workflowId = workflowDetails?.workflowId;\n      const status = 'rejected';\n      const reviewedBy = workflowDetails?.reviewedBy;\n      const message = feedback;\n      console.log(id, workflowId, status, reviewedBy, message);\n      // Show loading dialog\n      this.dialogService.loading({\n        title: 'Rejecting Workflow...',\n        message: 'Please wait while we reject the workflow.',\n        showProgress: false,\n        showCancelButton: false\n      });\n      this.approvalService.rejectWorkflow(id, workflowId, status, reviewedBy, message).subscribe({\n        next: response => {\n          this.dialogService.close(); // Close loading dialog\n          const message = response?.message || this.labels.workflowSuccessRejectMessage;\n          this.dialogService.success({\n            title: 'Workflow Rejected',\n            message: message\n          }).then(() => {\n            this.loadWorkflowReviews(); // Refresh the list\n          });\n        },\n        error: error => {\n          this.dialogService.close(); // Close loading dialog\n          const errorMessage = error?.error?.message || this.labels.defaultErrorMessage;\n          this.dialogService.error({\n            title: 'Rejection Failed',\n            message: errorMessage,\n            showRetryButton: true,\n            retryButtonText: 'Retry'\n          }).then(result => {\n            if (result.action === 'retry') {\n              this.handleWorkflowRejection(feedback);\n            }\n          });\n        }\n      });\n    }\n    // Remove old handleInfoPopup method - no longer needed as DialogService handles navigation automatically\n    updateConsoleApproval(data, type) {\n      this.consoleApproval = {\n        contents: data?.map(req => {\n          const statusIcons = {\n            approved: 'circle-check-big',\n            rejected: 'circle-x',\n            review: 'clock'\n          };\n          const statusTexts = {\n            approved: this.labels.approved,\n            rejected: this.labels.rejected,\n            review: this.labels.review\n          };\n          const statusKey = this.toRequestStatus(req?.status);\n          let specificId = 0;\n          let title = '';\n          if (type === 'tool') {\n            specificId = req.toolId;\n            title = req.toolName;\n          } else if (type === 'agent') {\n            specificId = req.agentId;\n            title = req.agentName;\n          } else {\n            specificId = req.workflowId;\n            title = req.workflowName;\n          }\n          return {\n            id: req.id,\n            refId: specificId,\n            type: type,\n            session1: {\n              title: title,\n              labels: [{\n                name: type,\n                color: 'success',\n                background: 'red',\n                type: 'normal'\n              }, {\n                name: req.changeRequestType,\n                color: req.changeRequestType === 'update' ? 'error' : 'info',\n                background: 'red',\n                type: 'pill'\n              }]\n            },\n            session2: [{\n              name: type,\n              color: 'default',\n              background: 'red',\n              type: 'normal'\n            }, {\n              name: req.status,\n              color: 'default',\n              background: 'red',\n              type: 'normal'\n            }],\n            session3: [{\n              iconName: 'user',\n              label: req.requestedBy\n            }, {\n              iconName: 'calendar-days',\n              label: formatDate(req?.requestedAt, 'dd MMM yyyy', 'en-IN')\n            }],\n            session4: {\n              status: statusTexts[statusKey],\n              iconName: statusIcons[statusKey]\n            }\n          };\n        }),\n        footer: {}\n      };\n    }\n    static ɵfac = function ApprovalComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ApprovalComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.SharedApiServiceService), i0.ɵɵdirectiveInject(i3.ApprovalService), i0.ɵɵdirectiveInject(i4.FormBuilder), i0.ɵɵdirectiveInject(i5.DialogService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ApprovalComponent,\n      selectors: [[\"app-approval\"]],\n      decls: 10,\n      vars: 7,\n      consts: [[1, \"approval\"], [1, \"approval-left-screen\"], [1, \"quick-actions-wrapper\"], [1, \"quick-actions-toggle\"], [\"class\", \"left-title\", 4, \"ngIf\"], [\"name\", \"panel-left\", 1, \"collapse-icon\", 3, \"click\"], [\"class\", \"quick-actions-content\", 4, \"ngIf\"], [\"class\", \"quick-actions-icons\", 4, \"ngIf\"], [1, \"approval-right-screen\"], [1, \"left-title\"], [1, \"quick-actions-content\"], [1, \"action-buttons\"], [1, \"action-button\", 3, \"click\"], [1, \"action-icon\"], [\"width\", \"24\", \"height\", \"24\", 3, \"src\", \"alt\", \"title\"], [1, \"action-label\"], [1, \"quick-actions-icons\"], [1, \"icon-button\", 3, \"click\", \"title\"], [1, \"icon-wrapper\"]],\n      template: function ApprovalComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵtemplate(4, ApprovalComponent_span_4_Template, 2, 0, \"span\", 4);\n          i0.ɵɵelementStart(5, \"lucide-icon\", 5);\n          i0.ɵɵlistener(\"click\", function ApprovalComponent_Template_lucide_icon_click_5_listener() {\n            return ctx.toggleQuickActions();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(6, ApprovalComponent_div_6_Template, 17, 18, \"div\", 6)(7, ApprovalComponent_div_7_Template, 10, 21, \"div\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 8);\n          i0.ɵɵelement(9, \"router-outlet\");\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"quick-actions-expanded\", ctx.quickActionsExpanded);\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"expanded\", ctx.quickActionsExpanded);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.quickActionsExpanded);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.quickActionsExpanded);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.quickActionsExpanded);\n        }\n      },\n      dependencies: [CommonModule, i6.NgIf, RouterModule, i1.RouterOutlet, ReactiveFormsModule, LucideAngularModule, i7.LucideAngularComponent],\n      styles: [\".approval[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: row;\\n  width: 100%;\\n  height: 100%;\\n}\\n\\n.approval-left-screen[_ngcontent-%COMP%] {\\n  flex: 0 0 70px; \\n\\n  width: 70px;\\n  transition: all var(--transition-speed) ease;\\n  height: 100%; \\n\\n  overflow: hidden;\\n}\\n.approval-left-screen.quick-actions-expanded[_ngcontent-%COMP%] {\\n  flex: 0 0 250px;\\n  margin-right: 15px;\\n}\\n\\n\\n\\n.approval-right-screen[_ngcontent-%COMP%] {\\n  flex: 1; \\n\\n  padding: 1rem; \\n\\n  overflow-y: auto; \\n\\n}\\n\\n\\n\\n.approval-title-filter[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  margin-bottom: 1rem; \\n\\n}\\n\\n\\n\\n.approvals-title[_ngcontent-%COMP%] {\\n  font-weight: bold;\\n  font-size: 1.2rem;\\n  margin-bottom: 0.5rem;\\n}\\n\\n\\n\\n.filter-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 0 1rem; \\n\\n  margin-bottom: 1rem; \\n\\n}\\n\\n\\n\\n.search-bars[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem; \\n\\n}\\n\\n\\n\\n.textbox.section[_ngcontent-%COMP%] {\\n  margin-left: 1rem;\\n}\\n\\n.textbox.section[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center; \\n\\n  gap: 8px; \\n\\n}\\n\\n.approval-card-header[_ngcontent-%COMP%] {\\n  font-size: 1.25rem; \\n\\n  font-weight: 600; \\n\\n  color: #333; \\n\\n  padding: 0.75rem 1rem; \\n\\n  margin-bottom: 1rem; \\n\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between; \\n\\n}\\n\\n.approval-title-filter[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: row;\\n  justify-content: space-between;\\n  align-items: center;\\n  gap: 1rem;\\n  flex-wrap: nowrap;\\n  width: 100%;\\n  padding: 1rem 0;\\n}\\n\\n.approval-title-filter[_ngcontent-%COMP%]    > ava-text-card[_ngcontent-%COMP%] {\\n  flex: 1 1 22%;\\n  min-width: 200px;\\n}\\n\\n.quick-actions-wrapper[_ngcontent-%COMP%] {\\n  grid-area: quick-actions;\\n  background-color: var(--dashboard-card-bg);\\n  border-radius: var(--border-radius-standard);\\n  display: flex;\\n  flex-direction: column;\\n  width: 55px;\\n  height: 95%;\\n  transition: all 0.35s cubic-bezier(0.4, 0, 0.2, 1);\\n  overflow: hidden;\\n  box-shadow: var(--shadow-medium);\\n  border: var(--border-thin);\\n  position: relative;\\n  \\n\\n  \\n\\n  \\n\\n  \\n\\n}\\n.quick-actions-wrapper[_ngcontent-%COMP%]:hover {\\n  box-shadow: var(--shadow-hover);\\n}\\n.quick-actions-wrapper.expanded[_ngcontent-%COMP%] {\\n  width: 100%; \\n\\n}\\n@media (min-width: 1900px) and (max-width: 1930px) and (max-height: 1100px) {\\n  .quick-actions-wrapper[_ngcontent-%COMP%] {\\n    height: 595px;\\n  }\\n}\\n@media (min-width: 1200px) and (max-width: 1400px) {\\n  .quick-actions-wrapper[_ngcontent-%COMP%] {\\n    height: 580px !important;\\n    max-height: 580px !important;\\n  }\\n}\\n@media (max-width: 1200px) {\\n  .quick-actions-wrapper[_ngcontent-%COMP%] {\\n    height: 320px;\\n  }\\n}\\n@media (max-width: 992px) {\\n  .quick-actions-wrapper[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    width: 100%;\\n    height: 48px;\\n  }\\n  .quick-actions-wrapper.expanded[_ngcontent-%COMP%] {\\n    height: auto;\\n    max-height: 320px;\\n    width: 100%;\\n  }\\n}\\n@media (max-width: 576px) {\\n  .quick-actions-wrapper[_ngcontent-%COMP%] {\\n    height: 280px;\\n  }\\n}\\n@media (min-width: 1200px) and (max-width: 1366px) and (max-height: 800px) {\\n  .quick-actions-wrapper[_ngcontent-%COMP%] {\\n    height: 100%;\\n  }\\n  .quick-actions-wrapper[_ngcontent-%COMP%]     .card-container {\\n    height: 100%;\\n  }\\n}\\n.quick-actions-wrapper[_ngcontent-%COMP%]     .quick-actions-card {\\n  height: 100% !important;\\n  width: 100% !important;\\n  \\n\\n  \\n\\n}\\n.quick-actions-wrapper[_ngcontent-%COMP%]     .quick-actions-card .card-container {\\n  height: 100% !important;\\n  width: 100% !important;\\n  padding: 0 !important;\\n  overflow: hidden !important;\\n  display: flex !important;\\n  flex-direction: column !important;\\n}\\n.quick-actions-wrapper[_ngcontent-%COMP%]     .quick-actions-card .card-container .card-body {\\n  padding: 0 !important;\\n  height: 100% !important;\\n  display: flex !important;\\n  flex-direction: column !important;\\n}\\n.quick-actions-wrapper[_ngcontent-%COMP%]     .quick-actions-card .card-content {\\n  height: 100% !important;\\n  display: flex !important;\\n  flex-direction: column !important;\\n  padding: 0 !important;\\n}\\n@media (max-width: 992px) {\\n  .quick-actions-wrapper[_ngcontent-%COMP%]     .quick-actions-card .card-content {\\n    flex-direction: row !important;\\n  }\\n}\\n@media (max-width: 992px) {\\n  .quick-actions-wrapper[_ngcontent-%COMP%]     .quick-actions-card .card-container {\\n    height: 48px !important;\\n    width: 100% !important;\\n    flex-direction: row !important;\\n  }\\n  .quick-actions-wrapper[_ngcontent-%COMP%]     .quick-actions-card .card-container.expanded {\\n    height: auto !important;\\n  }\\n}\\n@media (min-width: 1200px) and (max-width: 1366px) and (max-height: 800px) {\\n  .quick-actions-wrapper[_ngcontent-%COMP%]     .quick-actions-card .card-container {\\n    width: 100% !important;\\n  }\\n}\\n\\n.quick-actions-content[_ngcontent-%COMP%] {\\n  padding: 20px 16px;\\n  overflow-y: auto;\\n  flex-grow: 1;\\n  background: linear-gradient(to bottom, #ffffff, #f0f5ff);\\n}\\n.quick-actions-content[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px; \\n\\n}\\n.quick-actions-content[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: flex-start;\\n  gap: 16px; \\n\\n  padding: 16px 20px; \\n\\n  border-radius: 12px; \\n\\n  border: 1px solid #e5e7eb;\\n  background: #fff;\\n  background-origin: border-box;\\n  background-clip: padding-box, border-box;\\n  --button-effect-color: 33, 90, 214;\\n  cursor: pointer;\\n  transition: all var(--transition-speed) ease;\\n  width: 100%;\\n  text-align: left;\\n  color: #fff;\\n}\\n.quick-actions-content[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%]   .action-icon[_ngcontent-%COMP%], \\n.quick-actions-content[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%]   .action-label[_ngcontent-%COMP%] {\\n  color: #000000;\\n}\\n.quick-actions-content[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%]:hover {\\n  opacity: 0.9;\\n  background-color: #f0f5ff;\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px var(--dashboard-shadow-hover);\\n}\\n.quick-actions-content[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%]:hover   .action-icon[_ngcontent-%COMP%], .quick-actions-content[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%]:hover   .action-label[_ngcontent-%COMP%] {\\n  color: #1d4ed8;\\n}\\n.quick-actions-content[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%]:hover   .action-icon[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  filter: brightness(0) saturate(100%) invert(21%) sepia(94%) saturate(1627%) hue-rotate(215deg) brightness(91%) contrast(101%);\\n}\\n.quick-actions-content[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%]   .action-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 24px;\\n  height: 24px;\\n}\\n.quick-actions-content[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%]   .action-icon[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  color: black;\\n  filter: none; \\n\\n}\\n.quick-actions-content[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%]   .action-label[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 500;\\n  color: linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%); \\n\\n}\\n\\n.quick-actions-toggle[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 100px;\\n  padding: 20px 16px;\\n  padding-bottom: 0px;\\n  cursor: pointer;\\n  transition: background-color 0.35s cubic-bezier(0.4, 0, 0.2, 1);\\n  flex-shrink: 0;\\n  height: 48px;\\n  padding: 0 16px;\\n  background: linear-gradient(to right, #ffffff, #f0f5ff);\\n  \\n\\n  \\n\\n}\\n.quick-actions-toggle[_ngcontent-%COMP%]   .left-title[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 1000;\\n  color: #000000;\\n  margin-bottom: 5px;\\n}\\n.quick-actions-wrapper[_ngcontent-%COMP%]:not(.expanded)   .quick-actions-toggle[_ngcontent-%COMP%] {\\n  padding: 20px 0;\\n  justify-content: center;\\n}\\n.quick-actions-toggle[_ngcontent-%COMP%]   .toggle-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 36px;\\n  height: 36px;\\n  border-radius: 8px;\\n  background-color: transparent;\\n  position: relative;\\n  \\n\\n}\\n.quick-actions-toggle[_ngcontent-%COMP%]   .toggle-button[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  inset: 0;\\n  border-radius: 8px;\\n  padding: 1px;\\n  background: var(--dashboard-gradient);\\n  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);\\n  mask-composite: exclude;\\n  transition: opacity 0.35s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.quick-actions-toggle[_ngcontent-%COMP%]   .toggle-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  transition: transform 0.35s cubic-bezier(0.4, 0, 0.2, 1);\\n  width: 16px;\\n  height: 16px;\\n  stroke: var(--dashboard-toggle-stroke);\\n  z-index: 1;\\n}\\n.quick-actions-toggle[_ngcontent-%COMP%]   .toggle-button[_ngcontent-%COMP%]   svg.rotate[_ngcontent-%COMP%] {\\n  transform: rotate(180deg);\\n}\\n.quick-actions-toggle[_ngcontent-%COMP%]   .quick-actions-wrapper[_ngcontent-%COMP%]:not(.expanded)   .toggle-button[_ngcontent-%COMP%] {\\n  background: var(--dashboard-gradient);\\n  transition: background 0.35s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.quick-actions-toggle[_ngcontent-%COMP%]   .quick-actions-wrapper[_ngcontent-%COMP%]:not(.expanded)   .toggle-button[_ngcontent-%COMP%]::before {\\n  display: none;\\n}\\n.quick-actions-toggle[_ngcontent-%COMP%]   .quick-actions-wrapper[_ngcontent-%COMP%]:not(.expanded)   .toggle-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  stroke: var(--dashboard-toggle-stroke-collapsed);\\n  transition: stroke 0.35s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.quick-actions-toggle[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-weight: 580;\\n  font-size: 16px;\\n  color: var(--dashboard-text-primary);\\n  opacity: 1;\\n  transition: opacity 0.35s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n\\n.quick-actions-icons[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 16px; \\n\\n  padding: 20px 0; \\n\\n  height: 150vh;\\n  background-color: linear-gradient(to right, #ffffff, #f0f5ff);\\n}\\n@media (max-width: 992px) {\\n  .quick-actions-icons[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    justify-content: center;\\n    flex-wrap: wrap;\\n    padding: 8px;\\n  }\\n}\\n.quick-actions-icons[_ngcontent-%COMP%]   .icon-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 36px; \\n\\n  height: 36px; \\n\\n  border-radius: 8px; \\n\\n  border: none;\\n  background: #fff;\\n  cursor: pointer;\\n  transition: all var(--transition-speed) ease;\\n}\\n.quick-actions-icons[_ngcontent-%COMP%]   .icon-button[_ngcontent-%COMP%]:hover {\\n  background-color: #f0f5ff;\\n  opacity: 0.9;\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px var(--dashboard-shadow-hover);\\n}\\n.quick-actions-icons[_ngcontent-%COMP%]   .icon-button[_ngcontent-%COMP%]:hover   .icon-wrapper[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  filter: brightness(0) saturate(100%) invert(21%) sepia(94%) saturate(1627%) hue-rotate(215deg) brightness(91%) contrast(101%);\\n}\\n.quick-actions-icons[_ngcontent-%COMP%]   .icon-button[_ngcontent-%COMP%]   .icon-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.quick-actions-icons[_ngcontent-%COMP%]   .icon-button[_ngcontent-%COMP%]   .icon-wrapper[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n}\\n\\n.icon-button.active-action[_ngcontent-%COMP%] {\\n  background: linear-gradient(to right, #ffffff, #f0f5ff);\\n}\\n.icon-button.active-action[_ngcontent-%COMP%]   .icon-wrapper[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  filter: brightness(0) saturate(100%) invert(21%) sepia(94%) saturate(1627%) hue-rotate(215deg) brightness(91%) contrast(101%);\\n}\\n\\n.action-button.active-action[_ngcontent-%COMP%] {\\n  border: 1px solid #f0f5ff !important;\\n  background-color: #f0f5ff !important;\\n}\\n.action-button.active-action[_ngcontent-%COMP%]   .action-icon[_ngcontent-%COMP%], .action-button.active-action[_ngcontent-%COMP%]   .action-label[_ngcontent-%COMP%] {\\n  color: #1d4ed8 !important;\\n}\\n.action-button.active-action[_ngcontent-%COMP%]   .action-icon[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  filter: brightness(0) saturate(100%) invert(21%) sepia(94%) saturate(1627%) hue-rotate(215deg) brightness(91%) contrast(101%) !important;\\n}\\n\\n.approval-card-section[_ngcontent-%COMP%] {\\n  border-radius: 24px;\\n  border: 1px solid #DCDCDC;\\n  background: #F8F8F8;\\n  height: 780px;\\n  overflow-y: auto;\\n}\\n\\n.approval-card-wrapper[_ngcontent-%COMP%] {\\n  margin: 10px;\\n}\\n\\n.no-pending-message[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin-top: 20%;\\n  font-size: 1.2rem;\\n  color: #000000;\\n  font-weight: 500;\\n  text-align: center;\\n  background: #f8f8f8;\\n  border-radius: 16px;\\n  min-height: 100px;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .filter-section[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: stretch;\\n  }\\n  .search-bars[_ngcontent-%COMP%], \\n   .textbox.section[_ngcontent-%COMP%] {\\n    width: 100%;\\n    margin: 0 0 0.5rem 0;\\n    justify-content: center;\\n  }\\n  .search-bars[_ngcontent-%COMP%] {\\n    flex-wrap: wrap;\\n    gap: 0.5rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return ApprovalComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "formatDate", "RouterModule", "ReactiveFormsModule", "startWith", "debounceTime", "distinctUntilChanged", "map", "approvalText", "LucideAngularModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "ApprovalComponent_div_6_Template_button_click_2_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "redirectToListOfApproval", "labels", "agents", "ɵɵelement", "ApprovalComponent_div_6_Template_button_click_7_listener", "workflows", "ApprovalComponent_div_6_Template_button_click_12_listener", "tools", "ɵɵadvance", "ɵɵclassProp", "currentTab", "ɵɵpropertyInterpolate", "ɵɵproperty", "ɵɵsanitizeUrl", "ɵɵtextInterpolate", "ApprovalComponent_div_7_Template_button_click_1_listener", "_r3", "ApprovalComponent_div_7_Template_button_click_4_listener", "ApprovalComponent_div_7_Template_button_click_7_listener", "ApprovalComponent", "router", "route", "apiService", "approvalService", "fb", "dialogService", "appLabels", "searchValue", "totalApprovedApprovals", "totalPendingApprovals", "totalApprovals", "isBasicCollapsed", "quickActionsExpanded", "consoleApproval", "options", "basicSidebarItems", "quickActions", "toolReviews", "workflowReviews", "agentsReviews", "currentToolsPage", "currentAgentsPage", "currentWorkflowsPage", "pageSize", "totalRecords", "isDeleted", "showToolApprovalPopup", "showInfoPopup", "showErrorPopup", "infoMessage", "selectedIndex", "showFeedbackPopup", "searchForm", "approvedAgentId", "constructor", "name", "electronics", "value", "clothing", "books", "id", "icon", "text", "active", "label", "group", "search", "ngOnInit", "searchList", "url", "includes", "console", "log", "get", "valueChanges", "pipe", "toLowerCase", "subscribe", "searchText", "applyFilter", "lower", "updateConsoleApproval", "filtered", "filter", "item", "<PERSON><PERSON><PERSON>", "toolName", "onSelectionChange", "data", "uClick", "i", "toggleQuickActions", "onBasicCollapseToggle", "isCollapsed", "onBasicItemClick", "for<PERSON>ach", "toRequestStatus", "loadToolReviews", "getAllReviewTools", "response", "userToolReviewDetails", "r", "status", "length", "loadWorkflowReviews", "getAllReviewWorkflows", "workflowReviewDetails", "loadAgentsReviews", "getAllReviewAgents", "agentReviewDetails", "loadMoreTools", "page", "loadMoreAgents", "loadMoreWorkflows", "loadReviews", "navigate", "relativeTo", "rejectApproval", "idx", "showFeedbackDialog", "approveApproval", "showApprovalDialog", "confirmation", "title", "confirmApproval", "message", "youAreAboutToApproveThis", "itWillBeActiveAndAvailableIn", "catalogueForUsersToExecute", "confirmButtonText", "approve", "cancelButtonText", "confirmButtonVariant", "then", "result", "confirmed", "handleApproval", "customButtons", "variant", "action", "feedback", "buttons", "handleRejection", "handleTesting", "index", "toolId", "redirectToToolPlayground", "agentId", "redirectToAgentsPlayground", "workflowId", "redirectToWorkflowPlayground", "type", "queryParams", "handleToolApproval", "handleAgentApproval", "handleWorkflowApproval", "handleToolRejection", "handleAgentRejection", "handleWorkflowRejection", "toolDetails", "reviewedBy", "loading", "showProgress", "showCancelButton", "approveTool", "next", "close", "toolSuccessApproveMessage", "success", "error", "errorMessage", "defaultErrorMessage", "showRetryButton", "retryButtonText", "rejectTool", "toolSuccessRejectMessage", "agentDetails", "approveAgent", "agentSuccessApproveMessage", "mode", "rejectAgent", "agentSuccessRejectMessage", "workflowDetails", "approveWorkflow", "workflowSuccessApproveMessage", "rejectWorkflow", "workflowSuccessRejectMessage", "contents", "req", "statusIcons", "approved", "rejected", "review", "statusTexts", "statusKey", "specificId", "workflowName", "refId", "session1", "color", "background", "changeRequestType", "session2", "session3", "iconName", "requestedBy", "requestedAt", "session4", "footer", "ɵɵdirectiveInject", "i1", "Router", "ActivatedRoute", "i2", "SharedApiServiceService", "i3", "ApprovalService", "i4", "FormBuilder", "i5", "DialogService", "selectors", "decls", "vars", "consts", "template", "ApprovalComponent_Template", "rf", "ctx", "ɵɵtemplate", "ApprovalComponent_span_4_Template", "ApprovalComponent_Template_lucide_icon_click_5_listener", "ApprovalComponent_div_6_Template", "ApprovalComponent_div_7_Template", "i6", "NgIf", "RouterOutlet", "i7", "LucideAngularComponent", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\console\\src\\app\\pages\\approval\\approval.component.ts", "C:\\console\\aava-ui-web\\projects\\console\\src\\app\\pages\\approval\\approval.component.html"], "sourcesContent": ["import { Component, CUSTOM_ELEMENTS_SCHEMA, OnInit } from '@angular/core';\r\nimport { CommonModule, formatDate } from '@angular/common';\r\nimport {\r\n  ActivatedRoute,\r\n  Router,\r\n  RouterModule,\r\n} from '@angular/router';\r\nimport {\r\n  PopupComponent,\r\n  ConfirmationPopupComponent,\r\n  DropdownOption,\r\n  DialogService,\r\n  DialogButton\r\n} from '@ava/play-comp-library';\r\nimport { ApprovalService } from '../../shared/services/approval.service';\r\nimport { SharedApiServiceService } from '../../shared/services/shared-api-service.service';\r\nimport { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';\r\nimport {\r\n  startWith,\r\n  debounceTime,\r\n  distinctUntilChanged,\r\n  map,\r\n} from 'rxjs';\r\nimport approvalText from './constants/approval.json';\r\nimport { LucideAngularModule } from 'lucide-angular';\r\n\r\nexport type RequestStatus = 'approved' | 'rejected' | 'review';\r\n@Component({\r\n  selector: 'app-approval',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    RouterModule,\r\n    PopupComponent,\r\n    ConfirmationPopupComponent,\r\n    ReactiveFormsModule,\r\n    LucideAngularModule\r\n  ],\r\n  schemas: [CUSTOM_ELEMENTS_SCHEMA],\r\n  templateUrl: './approval.component.html',\r\n  styleUrl: './approval.component.scss',\r\n})\r\nexport class ApprovalComponent implements OnInit {\r\n  // Labels from constants file\r\n  appLabels = approvalText.labels;\r\n\r\n  public searchValue: string = '';\r\n  public totalApprovedApprovals: number = 20;\r\n  public totalPendingApprovals: number = 15;\r\n  public totalApprovals: number = 60;\r\n  public isBasicCollapsed: boolean = false;\r\n  public quickActionsExpanded: boolean = true;\r\n  public consoleApproval: any = {};\r\n  public options: DropdownOption[] = [];\r\n  public basicSidebarItems: any[] = [];\r\n  public quickActions: any[] = [];\r\n  public toolReviews: any[] = [];\r\n  public workflowReviews: any[] = [];\r\n  public agentsReviews: any[] = [];\r\n  public currentToolsPage = 1;\r\n  public currentAgentsPage = 1;\r\n  public currentWorkflowsPage = 1;\r\n  public pageSize = 50;\r\n  public totalRecords = 0;\r\n  public isDeleted = false;\r\n  public currentTab = '';\r\n  public showToolApprovalPopup = false;\r\n  public showInfoPopup = false;\r\n  public showErrorPopup = false;\r\n  public infoMessage = '';\r\n  public selectedIndex = 0;\r\n  public showFeedbackPopup = false;\r\n  public searchForm!: FormGroup;\r\n  public labels: any = approvalText.labels;\r\n  public approvedAgentId: number | null = null;\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private route: ActivatedRoute,\r\n    private apiService: SharedApiServiceService,\r\n    private approvalService: ApprovalService,\r\n    private fb: FormBuilder,\r\n    private dialogService: DialogService,\r\n  ) {\r\n    this.labels = approvalText.labels;\r\n    this.options = [\r\n      { name: this.labels.electronics, value: 'electronics' },\r\n      { name: this.labels.clothing, value: 'clothing' },\r\n      { name: this.labels.books, value: 'books' },\r\n    ];\r\n    this.basicSidebarItems = [\r\n      {\r\n        id: '1',\r\n        icon: 'hammer',\r\n        text: this.labels.agents,\r\n        route: '',\r\n        active: true,\r\n      },\r\n      { id: '2', icon: 'circle-check', text: this.labels.workflows, route: '' },\r\n      { id: '3', icon: 'bot', text: this.labels.tools, route: '' },\r\n    ];\r\n    this.quickActions = [\r\n      {\r\n        icon: 'awe_agents',\r\n        label: this.labels.agents,\r\n        route: '',\r\n      },\r\n      {\r\n        icon: 'awe_workflows',\r\n        label: this.labels.workflows,\r\n        route: '',\r\n      },\r\n      {\r\n        icon: 'awe_tools',\r\n        label: this.labels.tools,\r\n        route: '',\r\n      },\r\n    ];\r\n    this.searchForm = this.fb.group({\r\n      search: [''],\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.searchList();\r\n    this.totalApprovals = 60;\r\n    // Initialize currentTab to the first option (Agents)\r\n    this.currentTab = this.labels.agents;\r\n    if(this.router.url.includes('approval-agents')){\r\n      this.currentTab = this.labels.agents;\r\n    }else if(this.router.url.includes('approval-tools')){\r\n      this.currentTab = this.labels.tools;\r\n    }else if(this.router.url.includes('approval-workflows')){\r\n      this.currentTab = this.labels.workflows;\r\n    }\r\n  }\r\n\r\n  public searchList() {\r\n    console.log(this.searchForm.get('search')?.value);\r\n    this.searchForm\r\n      .get('search')!\r\n      .valueChanges.pipe(\r\n        startWith(''),\r\n        debounceTime(300),\r\n        distinctUntilChanged(),\r\n        map((value) => value?.toLowerCase() ?? ''),\r\n      )\r\n      .subscribe((searchText) => {\r\n        this.applyFilter(searchText);\r\n      });\r\n  }\r\n\r\n  public applyFilter(text: string) {\r\n    const lower = text;\r\n\r\n    if (!this.searchValue) {\r\n      if (this.currentTab === 'Agents') {\r\n        this.updateConsoleApproval(this.agentsReviews, 'agent');\r\n      } else if (this.currentTab === 'Tools') {\r\n        this.updateConsoleApproval(this.toolReviews, 'tool');\r\n      } else {\r\n        this.updateConsoleApproval(this.workflowReviews, 'workflow');\r\n      }\r\n      return;\r\n    }\r\n\r\n    if (this.currentTab === 'Agents') {\r\n      const filtered = this.agentsReviews.filter((item) =>\r\n        item.agentName?.toLowerCase().includes(lower),\r\n      );\r\n      this.updateConsoleApproval(filtered, 'agent');\r\n    } else if (this.currentTab === 'Tools') {\r\n      const filtered = this.toolReviews.filter((item) =>\r\n        item.toolName?.toLowerCase().includes(lower),\r\n      );\r\n      this.updateConsoleApproval(filtered, 'tool');\r\n    } else {\r\n      const filtered = this.workflowReviews.filter((item) =>\r\n        item.name?.toLowerCase().includes(lower),\r\n      );\r\n      this.updateConsoleApproval(filtered, 'workflow');\r\n    }\r\n  }\r\n\r\n  public onSelectionChange(data: any) {\r\n    console.log('Selection changed:', data);\r\n  }\r\n\r\n  public uClick(i: any) {\r\n    console.log('log' + i);\r\n  }\r\n\r\n  public toggleQuickActions(): void {\r\n    this.quickActionsExpanded = !this.quickActionsExpanded;\r\n  }\r\n\r\n  public onBasicCollapseToggle(isCollapsed: boolean): void {\r\n    this.isBasicCollapsed = isCollapsed;\r\n    console.log('Basic sidebar collapsed:', isCollapsed);\r\n  }\r\n\r\n  public onBasicItemClick(item: any): void {\r\n    this.basicSidebarItems.forEach((i) => (i.active = false));\r\n    item.active = true;\r\n    console.log(item);\r\n  }\r\n\r\n  public toRequestStatus(value: string | null | undefined): RequestStatus {\r\n    return value === 'approved' || value === 'rejected' || value === 'review'\r\n      ? value\r\n      : 'review';\r\n  }\r\n\r\n  public loadToolReviews() {\r\n    this.approvalService\r\n      .getAllReviewTools(this.currentToolsPage, this.pageSize, this.isDeleted)\r\n      .subscribe((response) => {\r\n        if (this.currentToolsPage > 1) {\r\n          this.toolReviews = [\r\n            ...this.toolReviews,\r\n            ...response.userToolReviewDetails,\r\n          ];\r\n        } else {\r\n          this.toolReviews = response?.userToolReviewDetails;\r\n        }\r\n        this.toolReviews = this.toolReviews.filter(\r\n          (r) => r.status !== 'approved',\r\n        );\r\n        this.totalRecords = this.toolReviews.length;\r\n        this.updateConsoleApproval(this.toolReviews, 'tool');\r\n      });\r\n  }\r\n\r\n  public loadWorkflowReviews() {\r\n    this.approvalService\r\n      .getAllReviewWorkflows(\r\n        this.currentWorkflowsPage,\r\n        this.pageSize,\r\n        this.isDeleted,\r\n      )\r\n      .subscribe((response) => {\r\n        if (this.currentWorkflowsPage > 1) {\r\n          this.workflowReviews = [\r\n            ...this.workflowReviews,\r\n            ...response.workflowReviewDetails,\r\n          ];\r\n        } else {\r\n          this.workflowReviews = response?.workflowReviewDetails;\r\n        }\r\n        this.workflowReviews = this.workflowReviews.filter(\r\n          (r) => r.status !== 'approved',\r\n        );\r\n        this.totalRecords = this.workflowReviews.length;\r\n        console.log('reviews ', this.workflowReviews);\r\n        this.updateConsoleApproval(this.workflowReviews, 'workflow');\r\n      });\r\n  }\r\n\r\n  public loadAgentsReviews() {\r\n    this.approvalService\r\n      .getAllReviewAgents(this.currentAgentsPage, this.pageSize, this.isDeleted)\r\n      .subscribe((response) => {\r\n        if (this.currentAgentsPage > 1) {\r\n          this.agentsReviews = [\r\n            ...this.agentsReviews,\r\n            ...response.agentReviewDetails,\r\n          ];\r\n        } else {\r\n          this.agentsReviews = response?.agentReviewDetails;\r\n        }\r\n        this.agentsReviews = this.agentsReviews.filter(\r\n          (r) => r.status !== 'approved',\r\n        );\r\n        this.totalRecords = this.agentsReviews.length;\r\n        this.updateConsoleApproval(this.agentsReviews, 'agent');\r\n      });\r\n  }\r\n\r\n  public loadMoreTools(page: number) {\r\n    this.currentToolsPage = page;\r\n    this.loadToolReviews();\r\n  }\r\n\r\n  public loadMoreAgents(page: number) {\r\n    this.currentAgentsPage = page;\r\n    this.loadAgentsReviews();\r\n  }\r\n\r\n  public loadMoreWorkflows(page: number) {\r\n    this.currentWorkflowsPage = page;\r\n    this.loadWorkflowReviews();\r\n  }\r\n\r\n  public loadReviews(name: string) {\r\n    this.currentTab = name;\r\n    if (name == 'Tools') {\r\n      this.loadToolReviews();\r\n    } else if (name == 'Agents') {\r\n      this.loadAgentsReviews();\r\n    } else {\r\n      this.loadWorkflowReviews();\r\n    }\r\n  }\r\n\r\n  public redirectToListOfApproval(name: string) {\r\n    console.log('Redirecting to:', name);\r\n    console.log('Current labels:', this.labels);\r\n    this.currentTab = name;\r\n    console.log('Updated currentTab to:', this.currentTab);\r\n    \r\n    if (name === this.labels.tools) {\r\n      this.router.navigate(['approval-tools'], { relativeTo: this.route });\r\n    } else if (name === this.labels.agents) {\r\n      this.router.navigate(['approval-agents'], { relativeTo: this.route });\r\n    } else if (name === this.labels.workflows) {\r\n      this.router.navigate(['approval-workflows'], { relativeTo: this.route });\r\n    }\r\n  }\r\n\r\n  public rejectApproval(idx: any) {\r\n    console.log(idx);\r\n    this.selectedIndex = idx;\r\n    this.showFeedbackDialog();\r\n  }\r\n\r\n  public approveApproval(idx: any) {\r\n    console.log(idx);\r\n    this.selectedIndex = idx;\r\n    this.showApprovalDialog();\r\n  }\r\n\r\n  // Replace popup methods with DialogService methods\r\n  private showApprovalDialog(): void {\r\n    this.dialogService.confirmation({\r\n      title: this.labels.confirmApproval,\r\n      message: `${this.labels.youAreAboutToApproveThis} ${this.currentTab}. ${this.labels.itWillBeActiveAndAvailableIn} ${this.currentTab} ${this.labels.catalogueForUsersToExecute}`,\r\n      confirmButtonText: this.labels.approve,\r\n      cancelButtonText: 'Cancel',\r\n      confirmButtonVariant: 'danger',\r\n      icon:'circle-check'\r\n    }).then(result => {\r\n      if (result.confirmed) {\r\n        this.handleApproval();\r\n      }\r\n    });\r\n  }\r\n\r\n  private showFeedbackDialog(): void {\r\n     const customButtons: DialogButton[] = [\r\n      { label: 'Cancel', variant: 'secondary', action: 'cancel' },\r\n      { label: 'Send Back', variant: 'primary', action: 'sendback' }\r\n    ];\r\n    this.dialogService.feedback({\r\n      title: 'Confirm Send Back',\r\n      message: `This ${this.currentTab} will be send back for corrections and modification. Kindly comment what needs to be done.`,\r\n      buttons:customButtons\r\n    }).then(result => {\r\n      if (result.confirmed && result.confirmed === true) {\r\n        this.handleRejection(result.data);\r\n      }\r\n    });\r\n  }\r\n\r\n  public handleTesting(index: any) {\r\n    console.log(index);\r\n    if (this.currentTab == 'Tools') {\r\n      const toolId = this.toolReviews[index].toolId;\r\n      this.redirectToToolPlayground(toolId);\r\n    } else if (this.currentTab == 'Agents') {\r\n      const agentId = this.agentsReviews[index].agentId;\r\n      this.redirectToAgentsPlayground(agentId, 'collaborative');\r\n    } else {\r\n      const workflowId = this.workflowReviews[index].workflowId;\r\n      this.redirectToWorkflowPlayground(workflowId);\r\n    }\r\n  }\r\n\r\n  public redirectToToolPlayground(id: number): void {\r\n    this.router.navigate(['/libraries/tools/execute', id]);\r\n  }\r\n\r\n  public redirectToWorkflowPlayground(id: number): void {\r\n    this.router.navigate(['/build/workflows/execute', id]);\r\n  }\r\n\r\n  public redirectToAgentsPlayground(id: number, type: string): void {\r\n    this.router.navigate(['/build/agents', type, 'execute'], {\r\n      queryParams: { id: id },\r\n    });\r\n  }\r\n\r\n  public handleApproval() {\r\n    if (this.currentTab == 'Tools') {\r\n      this.handleToolApproval();\r\n    } else if (this.currentTab == 'Agents') {\r\n      this.handleAgentApproval();\r\n    } else {\r\n      this.handleWorkflowApproval();\r\n    }\r\n  }\r\n\r\n  public handleRejection(feedback: any) {\r\n    if (this.currentTab == 'Tools') {\r\n      this.handleToolRejection(feedback);\r\n    } else if (this.currentTab == 'Agents') {\r\n      this.handleAgentRejection(feedback);\r\n    } else {\r\n      this.handleWorkflowRejection(feedback);\r\n    }\r\n  }\r\n\r\n  public handleToolApproval() {\r\n    const toolDetails = this.toolReviews[this.selectedIndex];\r\n    const id = toolDetails.id;\r\n    const toolId = toolDetails.toolId;\r\n    const status = 'approved';\r\n    const reviewedBy = toolDetails.reviewedBy;\r\n\r\n    // Show loading dialog\r\n    this.dialogService.loading({\r\n      title: 'Approving Tool...',\r\n      message: 'Please wait while we approve the tool.',\r\n      showProgress: false,\r\n      showCancelButton: false\r\n    });\r\n\r\n    this.approvalService.approveTool(id, toolId, status, reviewedBy).subscribe({\r\n      next: (response: any) => {\r\n        this.dialogService.close(); // Close loading dialog\r\n        \r\n        const message = response?.message || this.labels.toolSuccessApproveMessage;\r\n        this.dialogService.success({\r\n          title: 'Tool Approved',\r\n          message: message\r\n        }).then(() => {\r\n          this.loadToolReviews(); // Refresh the list\r\n        });\r\n      },\r\n      error: (error) => {\r\n        this.dialogService.close(); // Close loading dialog\r\n        \r\n        const errorMessage = error?.error?.message || this.labels.defaultErrorMessage;\r\n        this.dialogService.error({\r\n          title: 'Approval Failed',\r\n          message: errorMessage,\r\n          showRetryButton: true,\r\n          retryButtonText: 'Retry'\r\n        }).then(result => {\r\n          if (result.action === 'retry') {\r\n            this.handleToolApproval();\r\n          }\r\n        });\r\n      },\r\n    });\r\n  }\r\n\r\n  public handleToolRejection(feedback: any) {\r\n    const toolDetails = this.toolReviews[this.selectedIndex];\r\n    const id = toolDetails.id;\r\n    const toolId = toolDetails.toolId;\r\n    const status = 'rejected';\r\n    const reviewedBy = toolDetails.reviewedBy;\r\n    const message = feedback;\r\n\r\n    // Show loading dialog\r\n    this.dialogService.loading({\r\n      title: 'Rejecting Tool...',\r\n      message: 'Please wait while we reject the tool.',\r\n      showProgress: false,\r\n      showCancelButton: false\r\n    });\r\n\r\n    this.approvalService\r\n      .rejectTool(id, toolId, status, reviewedBy, message)\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.dialogService.close(); // Close loading dialog\r\n          \r\n          const message = response?.message || this.labels.toolSuccessRejectMessage;\r\n          this.dialogService.success({\r\n            title: 'Tool Rejected',\r\n            message: message\r\n          }).then(() => {\r\n            this.loadToolReviews(); // Refresh the list\r\n          });\r\n        },\r\n        error: (error) => {\r\n          this.dialogService.close(); // Close loading dialog\r\n          \r\n          const errorMessage = error?.error?.message || this.labels.defaultErrorMessage;\r\n          this.dialogService.error({\r\n            title: 'Rejection Failed',\r\n            message: errorMessage,\r\n            showRetryButton: true,\r\n            retryButtonText: 'Retry'\r\n          }).then(result => {\r\n            if (result.action === 'retry') {\r\n              this.handleToolRejection(feedback);\r\n            }\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  public handleAgentApproval() {\r\n    const agentDetails = this.agentsReviews[this.selectedIndex];\r\n    const id = agentDetails.id;\r\n    const agentId = agentDetails.agentId;\r\n    const status = 'approved';\r\n    const reviewedBy = agentDetails.reviewedBy;\r\n\r\n    // Show loading dialog\r\n    this.dialogService.loading({\r\n      title: 'Approving Agent...',\r\n      message: 'Please wait while we approve the agent.',\r\n      showProgress: false,\r\n      showCancelButton: false\r\n    });\r\n\r\n    this.approvalService\r\n      .approveAgent(id, agentId, status, reviewedBy)\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.dialogService.close(); // Close loading dialog\r\n          \r\n          const message = response?.message || this.labels.agentSuccessApproveMessage;\r\n          \r\n          // Store agent ID for navigation after popup confirmation\r\n          this.approvedAgentId = agentId;\r\n          \r\n          this.dialogService.success({\r\n            title: 'Agent Approved',\r\n            message: message,\r\n          }).then(result => {\r\n            if (result.action === 'secondary') {\r\n              // Navigate to build agent screen with the approved agent ID\r\n              this.router.navigate(['/build/agents/collaborative'], {\r\n                queryParams: {\r\n                  id: this.approvedAgentId,\r\n                  mode: 'edit',\r\n                },\r\n              });\r\n            } else {\r\n              this.loadAgentsReviews(); // Refresh the list\r\n            }\r\n            // Reset the approved agent ID\r\n            this.approvedAgentId = null;\r\n          });\r\n        },\r\n        error: (error) => {\r\n          this.dialogService.close(); // Close loading dialog\r\n          \r\n          const errorMessage = error?.error?.message || this.labels.defaultErrorMessage;\r\n          this.dialogService.error({\r\n            title: 'Approval Failed',\r\n            message: errorMessage,\r\n            showRetryButton: true,\r\n            retryButtonText: 'Retry'\r\n          }).then(result => {\r\n            if (result.action === 'retry') {\r\n              this.handleAgentApproval();\r\n            }\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  public handleAgentRejection(feedback: any) {\r\n    const agentDetails = this.agentsReviews[this.selectedIndex];\r\n    const id = agentDetails.id;\r\n    const agentId = agentDetails.agentId;\r\n    const status = 'rejected';\r\n    const reviewedBy = agentDetails.reviewedBy;\r\n    const message = feedback;\r\n\r\n    // Show loading dialog\r\n    this.dialogService.loading({\r\n      title: 'Rejecting Agent...',\r\n      message: 'Please wait while we reject the agent.',\r\n      showProgress: false,\r\n      showCancelButton: false\r\n    });\r\n\r\n    this.approvalService\r\n      .rejectAgent(id, agentId, status, reviewedBy, message)\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.dialogService.close(); // Close loading dialog\r\n          \r\n          const message = response?.message || this.labels.agentSuccessRejectMessage;\r\n          this.dialogService.success({\r\n            title: 'Agent Rejected',\r\n            message: message\r\n          }).then(() => {\r\n            this.loadAgentsReviews(); // Refresh the list\r\n          });\r\n        },\r\n        error: (error) => {\r\n          this.dialogService.close(); // Close loading dialog\r\n          \r\n          const errorMessage = error?.error?.message || this.labels.defaultErrorMessage;\r\n          this.dialogService.error({\r\n            title: 'Rejection Failed',\r\n            message: errorMessage,\r\n            showRetryButton: true,\r\n            retryButtonText: 'Retry'\r\n          }).then(result => {\r\n            if (result.action === 'retry') {\r\n              this.handleAgentRejection(feedback);\r\n            }\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  public handleWorkflowApproval() {\r\n    const workflowDetails = this.workflowReviews[this.selectedIndex];\r\n    const id = workflowDetails?.id;\r\n    const workflowId = workflowDetails?.workflowId;\r\n    const status = 'approved';\r\n    const reviewedBy = workflowDetails?.reviewedBy;\r\n    console.log(id, workflowId, status, reviewedBy);\r\n\r\n    // Show loading dialog\r\n    this.dialogService.loading({\r\n      title: 'Approving Workflow...',\r\n      message: 'Please wait while we approve the workflow.',\r\n      showProgress: false,\r\n      showCancelButton: false\r\n    });\r\n\r\n    this.approvalService\r\n      .approveWorkflow(id, workflowId, status, reviewedBy)\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.dialogService.close(); // Close loading dialog\r\n          \r\n          const message = response?.message || this.labels.workflowSuccessApproveMessage;\r\n          this.dialogService.success({\r\n            title: 'Workflow Approved',\r\n            message: message\r\n          }).then(() => {\r\n            this.loadWorkflowReviews(); // Refresh the list\r\n          });\r\n        },\r\n        error: (error) => {\r\n          this.dialogService.close(); // Close loading dialog\r\n          \r\n          const errorMessage = error?.error?.message || this.labels.defaultErrorMessage;\r\n          this.dialogService.error({\r\n            title: 'Approval Failed',\r\n            message: errorMessage,\r\n            showRetryButton: true,\r\n            retryButtonText: 'Retry'\r\n          }).then(result => {\r\n            if (result.action === 'retry') {\r\n              this.handleWorkflowApproval();\r\n            }\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  public handleWorkflowRejection(feedback: any) {\r\n    const workflowDetails = this.workflowReviews[this.selectedIndex];\r\n    const id = workflowDetails?.id;\r\n    const workflowId = workflowDetails?.workflowId;\r\n    const status = 'rejected';\r\n    const reviewedBy = workflowDetails?.reviewedBy;\r\n    const message = feedback;\r\n    console.log(id, workflowId, status, reviewedBy, message);\r\n\r\n    // Show loading dialog\r\n    this.dialogService.loading({\r\n      title: 'Rejecting Workflow...',\r\n      message: 'Please wait while we reject the workflow.',\r\n      showProgress: false,\r\n      showCancelButton: false\r\n    });\r\n\r\n    this.approvalService\r\n      .rejectWorkflow(id, workflowId, status, reviewedBy, message)\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.dialogService.close(); // Close loading dialog\r\n          \r\n          const message = response?.message || this.labels.workflowSuccessRejectMessage;\r\n          this.dialogService.success({\r\n            title: 'Workflow Rejected',\r\n            message: message\r\n          }).then(() => {\r\n            this.loadWorkflowReviews(); // Refresh the list\r\n          });\r\n        },\r\n        error: (error) => {\r\n          this.dialogService.close(); // Close loading dialog\r\n          \r\n          const errorMessage = error?.error?.message || this.labels.defaultErrorMessage;\r\n          this.dialogService.error({\r\n            title: 'Rejection Failed',\r\n            message: errorMessage,\r\n            showRetryButton: true,\r\n            retryButtonText: 'Retry'\r\n          }).then(result => {\r\n            if (result.action === 'retry') {\r\n              this.handleWorkflowRejection(feedback);\r\n            }\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  // Remove old handleInfoPopup method - no longer needed as DialogService handles navigation automatically\r\n\r\n  public updateConsoleApproval(data: any[], type: string) {\r\n    this.consoleApproval = {\r\n      contents: data?.map((req: any) => {\r\n        const statusIcons: Record<RequestStatus, string> = {\r\n          approved: 'circle-check-big',\r\n          rejected: 'circle-x',\r\n          review: 'clock',\r\n        };\r\n        const statusTexts: Record<RequestStatus, string> = {\r\n          approved: this.labels.approved,\r\n          rejected: this.labels.rejected,\r\n          review: this.labels.review,\r\n        };\r\n        const statusKey = this.toRequestStatus(req?.status);\r\n        let specificId = 0;\r\n        let title = '';\r\n\r\n        if (type === 'tool') {\r\n          specificId = req.toolId;\r\n          title = req.toolName;\r\n        } else if (type === 'agent') {\r\n          specificId = req.agentId;\r\n          title = req.agentName;\r\n        } else {\r\n          specificId = req.workflowId;\r\n          title = req.workflowName;\r\n        }\r\n\r\n        return {\r\n          id: req.id,\r\n          refId: specificId,\r\n          type: type,\r\n          session1: {\r\n            title: title,\r\n            labels: [\r\n              {\r\n                name: type,\r\n                color: 'success',\r\n                background: 'red',\r\n                type: 'normal',\r\n              },\r\n              {\r\n                name: req.changeRequestType,\r\n                color: req.changeRequestType === 'update' ? 'error' : 'info',\r\n                background: 'red',\r\n                type: 'pill',\r\n              },\r\n            ],\r\n          },\r\n          session2: [\r\n            {\r\n              name: type,\r\n              color: 'default',\r\n              background: 'red',\r\n              type: 'normal',\r\n            },\r\n            {\r\n              name: req.status,\r\n              color: 'default',\r\n              background: 'red',\r\n              type: 'normal',\r\n          },\r\n          ],\r\n          session3: [\r\n            {\r\n              iconName: 'user',\r\n              label: req.requestedBy,\r\n            },\r\n            {\r\n              iconName: 'calendar-days',\r\n              label: formatDate(req?.requestedAt, 'dd MMM yyyy', 'en-IN'),\r\n            },\r\n          ],\r\n          session4: {\r\n            status: statusTexts[statusKey],\r\n            iconName: statusIcons[statusKey],\r\n          },\r\n        };\r\n      }),\r\n      footer: {},\r\n    };\r\n  }\r\n}\r\n", "<div class=\"approval\">\r\n    <div class=\"approval-left-screen\" [class.quick-actions-expanded]=\"quickActionsExpanded\">\r\n        <div class=\"quick-actions-wrapper\" [class.expanded]=\"quickActionsExpanded\">\r\n            <div class=\"quick-actions-toggle\">\r\n                <span class=\"left-title\" *ngIf=\"quickActionsExpanded\">Category</span>\r\n                <lucide-icon name=\"panel-left\" class=\"collapse-icon\" (click)=\"toggleQuickActions()\"></lucide-icon>\r\n            </div>\r\n\r\n            <!-- Expanded view with text labels -->\r\n            <div class=\"quick-actions-content\" *ngIf=\"quickActionsExpanded\">\r\n                <div class=\"action-buttons\">\r\n                    <button class=\"action-button\" \r\n                            [class.active-action]=\"currentTab === labels.agents\"\r\n                            (click)=\"redirectToListOfApproval(labels.agents)\">\r\n                        <div class=\"action-icon\">\r\n                            <img [src]=\"'svgs/icons/' + 'awe_agents' + '.svg'\" [alt]=\"labels.agents\" width=\"24\" height=\"24\" title=\"{{labels.agents}}\" />\r\n                        </div>\r\n                        <span class=\"action-label\">{{labels.agents}}</span>\r\n                    </button>\r\n                    \r\n                    <button class=\"action-button\" \r\n                            [class.active-action]=\"currentTab === labels.workflows\"\r\n                            (click)=\"redirectToListOfApproval(labels.workflows)\">\r\n                        <div class=\"action-icon\">\r\n                            <img [src]=\"'svgs/icons/' + 'awe_workflows' + '.svg'\" [alt]=\"labels.workflows\" width=\"24\" height=\"24\" title=\"{{labels.workflows}}\" />\r\n                        </div>\r\n                        <span class=\"action-label\">{{labels.workflows}}</span>\r\n                    </button>\r\n                    \r\n                    <button class=\"action-button\" \r\n                            [class.active-action]=\"currentTab === labels.tools\"\r\n                            (click)=\"redirectToListOfApproval(labels.tools)\">\r\n                        <div class=\"action-icon\">\r\n                            <img [src]=\"'svgs/icons/' + 'awe_tools' + '.svg'\" [alt]=\"labels.tools\" width=\"24\" height=\"24\" title=\"{{labels.tools}}\" />\r\n                        </div>\r\n                        <span class=\"action-label\">{{labels.tools}}</span>\r\n                    </button>\r\n                </div>\r\n            </div>\r\n\r\n            <!-- Collapsed view with icons only -->\r\n            <div class=\"quick-actions-icons\" *ngIf=\"!quickActionsExpanded\">\r\n                <button class=\"icon-button\" \r\n                        [class.active-action]=\"currentTab === labels.agents\"\r\n                        (click)=\"redirectToListOfApproval(labels.agents)\" \r\n                        [title]=\"labels.agents\" \r\n                        title=\"{{labels.agents}}\">\r\n                    <div class=\"icon-wrapper\">\r\n                        <img [src]=\"'svgs/icons/' + 'awe_agents' + '.svg'\" [alt]=\"labels.agents\" width=\"24\" height=\"24\" title=\"{{labels.agents}}\" />\r\n                    </div>\r\n                </button>\r\n                 <button class=\"icon-button\" \r\n                         [class.active-action]=\"currentTab === labels.workflows\"\r\n                         (click)=\"redirectToListOfApproval(labels.workflows)\" \r\n                         [title]=\"labels.workflows\" \r\n                         title=\"{{labels.workflows}}\">\r\n                    <div class=\"icon-wrapper\">\r\n                        <img [src]=\"'svgs/icons/' + 'awe_workflows' + '.svg'\" [alt]=\"labels.workflows\" width=\"24\" height=\"24\" title=\"{{labels.workflows}}\" />\r\n                    </div>\r\n                </button>\r\n                <button class=\"icon-button\" \r\n                        [class.active-action]=\"currentTab === labels.tools\"\r\n                        (click)=\"redirectToListOfApproval(labels.tools)\" \r\n                        [title]=\"labels.tools\" \r\n                        title=\"{{labels.tools}}\">\r\n                    <div class=\"icon-wrapper\">\r\n                        <img [src]=\"'svgs/icons/' + 'awe_tools' + '.svg'\" [alt]=\"labels.tools\" width=\"24\" height=\"24\" title=\"{{labels.tools}}\" />\r\n                    </div>\r\n                </button>\r\n            </div>\r\n        </div>\r\n    </div>\r\n    \r\n    <div class=\"approval-right-screen\">\r\n        <router-outlet></router-outlet>\r\n    </div>\r\n</div>\r\n\r\n"], "mappings": "AACA,SAASA,YAAY,EAAEC,UAAU,QAAQ,iBAAiB;AAC1D,SAGEC,YAAY,QACP,iBAAiB;AAUxB,SAAiCC,mBAAmB,QAAQ,gBAAgB;AAC5E,SACEC,SAAS,EACTC,YAAY,EACZC,oBAAoB,EACpBC,GAAG,QACE,MAAM;AACb,OAAOC,YAAY,MAAM,2BAA2B;AACpD,SAASC,mBAAmB,QAAQ,gBAAgB;;;;;;;;;;;ICpBpCC,EAAA,CAAAC,cAAA,cAAsD;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAOjEH,EAFR,CAAAC,cAAA,cAAgE,cAChC,iBAGkC;IAAlDD,EAAA,CAAAI,UAAA,mBAAAC,yDAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAG,wBAAA,CAAAH,MAAA,CAAAI,MAAA,CAAAC,MAAA,CAAuC;IAAA,EAAC;IACrDb,EAAA,CAAAC,cAAA,cAAyB;IACrBD,EAAA,CAAAc,SAAA,cAA4H;IAChId,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAChDF,EADgD,CAAAG,YAAA,EAAO,EAC9C;IAETH,EAAA,CAAAC,cAAA,iBAE6D;IAArDD,EAAA,CAAAI,UAAA,mBAAAW,yDAAA;MAAAf,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAG,wBAAA,CAAAH,MAAA,CAAAI,MAAA,CAAAI,SAAA,CAA0C;IAAA,EAAC;IACxDhB,EAAA,CAAAC,cAAA,cAAyB;IACrBD,EAAA,CAAAc,SAAA,cAAqI;IACzId,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,IAAoB;IACnDF,EADmD,CAAAG,YAAA,EAAO,EACjD;IAETH,EAAA,CAAAC,cAAA,kBAEyD;IAAjDD,EAAA,CAAAI,UAAA,mBAAAa,0DAAA;MAAAjB,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAG,wBAAA,CAAAH,MAAA,CAAAI,MAAA,CAAAM,KAAA,CAAsC;IAAA,EAAC;IACpDlB,EAAA,CAAAC,cAAA,eAAyB;IACrBD,EAAA,CAAAc,SAAA,eAAyH;IAC7Hd,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,IAAgB;IAGvDF,EAHuD,CAAAG,YAAA,EAAO,EAC7C,EACP,EACJ;;;;IA1BUH,EAAA,CAAAmB,SAAA,GAAoD;IAApDnB,EAAA,CAAAoB,WAAA,kBAAAZ,MAAA,CAAAa,UAAA,KAAAb,MAAA,CAAAI,MAAA,CAAAC,MAAA,CAAoD;IAG4Cb,EAAA,CAAAmB,SAAA,GAAyB;IAAzBnB,EAAA,CAAAsB,qBAAA,UAAAd,MAAA,CAAAI,MAAA,CAAAC,MAAA,CAAyB;IAAtEb,EAA9C,CAAAuB,UAAA,+CAAAvB,EAAA,CAAAwB,aAAA,CAA6C,QAAAhB,MAAA,CAAAI,MAAA,CAAAC,MAAA,CAAsB;IAEjDb,EAAA,CAAAmB,SAAA,GAAiB;IAAjBnB,EAAA,CAAAyB,iBAAA,CAAAjB,MAAA,CAAAI,MAAA,CAAAC,MAAA,CAAiB;IAIxCb,EAAA,CAAAmB,SAAA,EAAuD;IAAvDnB,EAAA,CAAAoB,WAAA,kBAAAZ,MAAA,CAAAa,UAAA,KAAAb,MAAA,CAAAI,MAAA,CAAAI,SAAA,CAAuD;IAG+ChB,EAAA,CAAAmB,SAAA,GAA4B;IAA5BnB,EAAA,CAAAsB,qBAAA,UAAAd,MAAA,CAAAI,MAAA,CAAAI,SAAA,CAA4B;IAA5EhB,EAAjD,CAAAuB,UAAA,kDAAAvB,EAAA,CAAAwB,aAAA,CAAgD,QAAAhB,MAAA,CAAAI,MAAA,CAAAI,SAAA,CAAyB;IAEvDhB,EAAA,CAAAmB,SAAA,GAAoB;IAApBnB,EAAA,CAAAyB,iBAAA,CAAAjB,MAAA,CAAAI,MAAA,CAAAI,SAAA,CAAoB;IAI3ChB,EAAA,CAAAmB,SAAA,EAAmD;IAAnDnB,EAAA,CAAAoB,WAAA,kBAAAZ,MAAA,CAAAa,UAAA,KAAAb,MAAA,CAAAI,MAAA,CAAAM,KAAA,CAAmD;IAG2ClB,EAAA,CAAAmB,SAAA,GAAwB;IAAxBnB,EAAA,CAAAsB,qBAAA,UAAAd,MAAA,CAAAI,MAAA,CAAAM,KAAA,CAAwB;IAApElB,EAA7C,CAAAuB,UAAA,8CAAAvB,EAAA,CAAAwB,aAAA,CAA4C,QAAAhB,MAAA,CAAAI,MAAA,CAAAM,KAAA,CAAqB;IAE/ClB,EAAA,CAAAmB,SAAA,GAAgB;IAAhBnB,EAAA,CAAAyB,iBAAA,CAAAjB,MAAA,CAAAI,MAAA,CAAAM,KAAA,CAAgB;;;;;;IAOnDlB,EADJ,CAAAC,cAAA,cAA+D,iBAKzB;IAF1BD,EAAA,CAAAI,UAAA,mBAAAsB,yDAAA;MAAA1B,EAAA,CAAAM,aAAA,CAAAqB,GAAA;MAAA,MAAAnB,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAG,wBAAA,CAAAH,MAAA,CAAAI,MAAA,CAAAC,MAAA,CAAuC;IAAA,EAAC;IAGrDb,EAAA,CAAAC,cAAA,cAA0B;IACtBD,EAAA,CAAAc,SAAA,cAA4H;IAEpId,EADI,CAAAG,YAAA,EAAM,EACD;IACRH,EAAA,CAAAC,cAAA,iBAIqC;IAF7BD,EAAA,CAAAI,UAAA,mBAAAwB,yDAAA;MAAA5B,EAAA,CAAAM,aAAA,CAAAqB,GAAA;MAAA,MAAAnB,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAG,wBAAA,CAAAH,MAAA,CAAAI,MAAA,CAAAI,SAAA,CAA0C;IAAA,EAAC;IAGzDhB,EAAA,CAAAC,cAAA,cAA0B;IACtBD,EAAA,CAAAc,SAAA,cAAqI;IAE7Id,EADI,CAAAG,YAAA,EAAM,EACD;IACTH,EAAA,CAAAC,cAAA,iBAIiC;IAFzBD,EAAA,CAAAI,UAAA,mBAAAyB,yDAAA;MAAA7B,EAAA,CAAAM,aAAA,CAAAqB,GAAA;MAAA,MAAAnB,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAG,wBAAA,CAAAH,MAAA,CAAAI,MAAA,CAAAM,KAAA,CAAsC;IAAA,EAAC;IAGpDlB,EAAA,CAAAC,cAAA,cAA0B;IACtBD,EAAA,CAAAc,SAAA,cAAyH;IAGrId,EAFQ,CAAAG,YAAA,EAAM,EACD,EACP;;;;IA1BMH,EAAA,CAAAmB,SAAA,EAAoD;IAApDnB,EAAA,CAAAoB,WAAA,kBAAAZ,MAAA,CAAAa,UAAA,KAAAb,MAAA,CAAAI,MAAA,CAAAC,MAAA,CAAoD;IAGpDb,EAAA,CAAAsB,qBAAA,UAAAd,MAAA,CAAAI,MAAA,CAAAC,MAAA,CAAyB;IADzBb,EAAA,CAAAuB,UAAA,UAAAf,MAAA,CAAAI,MAAA,CAAAC,MAAA,CAAuB;IAGyEb,EAAA,CAAAmB,SAAA,GAAyB;IAAzBnB,EAAA,CAAAsB,qBAAA,UAAAd,MAAA,CAAAI,MAAA,CAAAC,MAAA,CAAyB;IAAtEb,EAA9C,CAAAuB,UAAA,+CAAAvB,EAAA,CAAAwB,aAAA,CAA6C,QAAAhB,MAAA,CAAAI,MAAA,CAAAC,MAAA,CAAsB;IAIvEb,EAAA,CAAAmB,SAAA,EAAuD;IAAvDnB,EAAA,CAAAoB,WAAA,kBAAAZ,MAAA,CAAAa,UAAA,KAAAb,MAAA,CAAAI,MAAA,CAAAI,SAAA,CAAuD;IAGvDhB,EAAA,CAAAsB,qBAAA,UAAAd,MAAA,CAAAI,MAAA,CAAAI,SAAA,CAA4B;IAD5BhB,EAAA,CAAAuB,UAAA,UAAAf,MAAA,CAAAI,MAAA,CAAAI,SAAA,CAA0B;IAG2EhB,EAAA,CAAAmB,SAAA,GAA4B;IAA5BnB,EAAA,CAAAsB,qBAAA,UAAAd,MAAA,CAAAI,MAAA,CAAAI,SAAA,CAA4B;IAA5EhB,EAAjD,CAAAuB,UAAA,kDAAAvB,EAAA,CAAAwB,aAAA,CAAgD,QAAAhB,MAAA,CAAAI,MAAA,CAAAI,SAAA,CAAyB;IAI9EhB,EAAA,CAAAmB,SAAA,EAAmD;IAAnDnB,EAAA,CAAAoB,WAAA,kBAAAZ,MAAA,CAAAa,UAAA,KAAAb,MAAA,CAAAI,MAAA,CAAAM,KAAA,CAAmD;IAGnDlB,EAAA,CAAAsB,qBAAA,UAAAd,MAAA,CAAAI,MAAA,CAAAM,KAAA,CAAwB;IADxBlB,EAAA,CAAAuB,UAAA,UAAAf,MAAA,CAAAI,MAAA,CAAAM,KAAA,CAAsB;IAGwElB,EAAA,CAAAmB,SAAA,GAAwB;IAAxBnB,EAAA,CAAAsB,qBAAA,UAAAd,MAAA,CAAAI,MAAA,CAAAM,KAAA,CAAwB;IAApElB,EAA7C,CAAAuB,UAAA,8CAAAvB,EAAA,CAAAwB,aAAA,CAA4C,QAAAhB,MAAA,CAAAI,MAAA,CAAAM,KAAA,CAAqB;;;ADxB9F,WAAaY,iBAAiB;EAAxB,MAAOA,iBAAiB;IAmClBC,MAAA;IACAC,KAAA;IACAC,UAAA;IACAC,eAAA;IACAC,EAAA;IACAC,aAAA;IAvCV;IACAC,SAAS,GAAGvC,YAAY,CAACc,MAAM;IAExB0B,WAAW,GAAW,EAAE;IACxBC,sBAAsB,GAAW,EAAE;IACnCC,qBAAqB,GAAW,EAAE;IAClCC,cAAc,GAAW,EAAE;IAC3BC,gBAAgB,GAAY,KAAK;IACjCC,oBAAoB,GAAY,IAAI;IACpCC,eAAe,GAAQ,EAAE;IACzBC,OAAO,GAAqB,EAAE;IAC9BC,iBAAiB,GAAU,EAAE;IAC7BC,YAAY,GAAU,EAAE;IACxBC,WAAW,GAAU,EAAE;IACvBC,eAAe,GAAU,EAAE;IAC3BC,aAAa,GAAU,EAAE;IACzBC,gBAAgB,GAAG,CAAC;IACpBC,iBAAiB,GAAG,CAAC;IACrBC,oBAAoB,GAAG,CAAC;IACxBC,QAAQ,GAAG,EAAE;IACbC,YAAY,GAAG,CAAC;IAChBC,SAAS,GAAG,KAAK;IACjBnC,UAAU,GAAG,EAAE;IACfoC,qBAAqB,GAAG,KAAK;IAC7BC,aAAa,GAAG,KAAK;IACrBC,cAAc,GAAG,KAAK;IACtBC,WAAW,GAAG,EAAE;IAChBC,aAAa,GAAG,CAAC;IACjBC,iBAAiB,GAAG,KAAK;IACzBC,UAAU;IACVnD,MAAM,GAAQd,YAAY,CAACc,MAAM;IACjCoD,eAAe,GAAkB,IAAI;IAE5CC,YACUlC,MAAc,EACdC,KAAqB,EACrBC,UAAmC,EACnCC,eAAgC,EAChCC,EAAe,EACfC,aAA4B;MAL5B,KAAAL,MAAM,GAANA,MAAM;MACN,KAAAC,KAAK,GAALA,KAAK;MACL,KAAAC,UAAU,GAAVA,UAAU;MACV,KAAAC,eAAe,GAAfA,eAAe;MACf,KAAAC,EAAE,GAAFA,EAAE;MACF,KAAAC,aAAa,GAAbA,aAAa;MAErB,IAAI,CAACxB,MAAM,GAAGd,YAAY,CAACc,MAAM;MACjC,IAAI,CAACiC,OAAO,GAAG,CACb;QAAEqB,IAAI,EAAE,IAAI,CAACtD,MAAM,CAACuD,WAAW;QAAEC,KAAK,EAAE;MAAa,CAAE,EACvD;QAAEF,IAAI,EAAE,IAAI,CAACtD,MAAM,CAACyD,QAAQ;QAAED,KAAK,EAAE;MAAU,CAAE,EACjD;QAAEF,IAAI,EAAE,IAAI,CAACtD,MAAM,CAAC0D,KAAK;QAAEF,KAAK,EAAE;MAAO,CAAE,CAC5C;MACD,IAAI,CAACtB,iBAAiB,GAAG,CACvB;QACEyB,EAAE,EAAE,GAAG;QACPC,IAAI,EAAE,QAAQ;QACdC,IAAI,EAAE,IAAI,CAAC7D,MAAM,CAACC,MAAM;QACxBmB,KAAK,EAAE,EAAE;QACT0C,MAAM,EAAE;OACT,EACD;QAAEH,EAAE,EAAE,GAAG;QAAEC,IAAI,EAAE,cAAc;QAAEC,IAAI,EAAE,IAAI,CAAC7D,MAAM,CAACI,SAAS;QAAEgB,KAAK,EAAE;MAAE,CAAE,EACzE;QAAEuC,EAAE,EAAE,GAAG;QAAEC,IAAI,EAAE,KAAK;QAAEC,IAAI,EAAE,IAAI,CAAC7D,MAAM,CAACM,KAAK;QAAEc,KAAK,EAAE;MAAE,CAAE,CAC7D;MACD,IAAI,CAACe,YAAY,GAAG,CAClB;QACEyB,IAAI,EAAE,YAAY;QAClBG,KAAK,EAAE,IAAI,CAAC/D,MAAM,CAACC,MAAM;QACzBmB,KAAK,EAAE;OACR,EACD;QACEwC,IAAI,EAAE,eAAe;QACrBG,KAAK,EAAE,IAAI,CAAC/D,MAAM,CAACI,SAAS;QAC5BgB,KAAK,EAAE;OACR,EACD;QACEwC,IAAI,EAAE,WAAW;QACjBG,KAAK,EAAE,IAAI,CAAC/D,MAAM,CAACM,KAAK;QACxBc,KAAK,EAAE;OACR,CACF;MACD,IAAI,CAAC+B,UAAU,GAAG,IAAI,CAAC5B,EAAE,CAACyC,KAAK,CAAC;QAC9BC,MAAM,EAAE,CAAC,EAAE;OACZ,CAAC;IACJ;IAEAC,QAAQA,CAAA;MACN,IAAI,CAACC,UAAU,EAAE;MACjB,IAAI,CAACtC,cAAc,GAAG,EAAE;MACxB;MACA,IAAI,CAACpB,UAAU,GAAG,IAAI,CAACT,MAAM,CAACC,MAAM;MACpC,IAAG,IAAI,CAACkB,MAAM,CAACiD,GAAG,CAACC,QAAQ,CAAC,iBAAiB,CAAC,EAAC;QAC7C,IAAI,CAAC5D,UAAU,GAAG,IAAI,CAACT,MAAM,CAACC,MAAM;MACtC,CAAC,MAAK,IAAG,IAAI,CAACkB,MAAM,CAACiD,GAAG,CAACC,QAAQ,CAAC,gBAAgB,CAAC,EAAC;QAClD,IAAI,CAAC5D,UAAU,GAAG,IAAI,CAACT,MAAM,CAACM,KAAK;MACrC,CAAC,MAAK,IAAG,IAAI,CAACa,MAAM,CAACiD,GAAG,CAACC,QAAQ,CAAC,oBAAoB,CAAC,EAAC;QACtD,IAAI,CAAC5D,UAAU,GAAG,IAAI,CAACT,MAAM,CAACI,SAAS;MACzC;IACF;IAEO+D,UAAUA,CAAA;MACfG,OAAO,CAACC,GAAG,CAAC,IAAI,CAACpB,UAAU,CAACqB,GAAG,CAAC,QAAQ,CAAC,EAAEhB,KAAK,CAAC;MACjD,IAAI,CAACL,UAAU,CACZqB,GAAG,CAAC,QAAQ,CAAE,CACdC,YAAY,CAACC,IAAI,CAChB5F,SAAS,CAAC,EAAE,CAAC,EACbC,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBC,GAAG,CAAEuE,KAAK,IAAKA,KAAK,EAAEmB,WAAW,EAAE,IAAI,EAAE,CAAC,CAC3C,CACAC,SAAS,CAAEC,UAAU,IAAI;QACxB,IAAI,CAACC,WAAW,CAACD,UAAU,CAAC;MAC9B,CAAC,CAAC;IACN;IAEOC,WAAWA,CAACjB,IAAY;MAC7B,MAAMkB,KAAK,GAAGlB,IAAI;MAElB,IAAI,CAAC,IAAI,CAACnC,WAAW,EAAE;QACrB,IAAI,IAAI,CAACjB,UAAU,KAAK,QAAQ,EAAE;UAChC,IAAI,CAACuE,qBAAqB,CAAC,IAAI,CAAC1C,aAAa,EAAE,OAAO,CAAC;QACzD,CAAC,MAAM,IAAI,IAAI,CAAC7B,UAAU,KAAK,OAAO,EAAE;UACtC,IAAI,CAACuE,qBAAqB,CAAC,IAAI,CAAC5C,WAAW,EAAE,MAAM,CAAC;QACtD,CAAC,MAAM;UACL,IAAI,CAAC4C,qBAAqB,CAAC,IAAI,CAAC3C,eAAe,EAAE,UAAU,CAAC;QAC9D;QACA;MACF;MAEA,IAAI,IAAI,CAAC5B,UAAU,KAAK,QAAQ,EAAE;QAChC,MAAMwE,QAAQ,GAAG,IAAI,CAAC3C,aAAa,CAAC4C,MAAM,CAAEC,IAAI,IAC9CA,IAAI,CAACC,SAAS,EAAET,WAAW,EAAE,CAACN,QAAQ,CAACU,KAAK,CAAC,CAC9C;QACD,IAAI,CAACC,qBAAqB,CAACC,QAAQ,EAAE,OAAO,CAAC;MAC/C,CAAC,MAAM,IAAI,IAAI,CAACxE,UAAU,KAAK,OAAO,EAAE;QACtC,MAAMwE,QAAQ,GAAG,IAAI,CAAC7C,WAAW,CAAC8C,MAAM,CAAEC,IAAI,IAC5CA,IAAI,CAACE,QAAQ,EAAEV,WAAW,EAAE,CAACN,QAAQ,CAACU,KAAK,CAAC,CAC7C;QACD,IAAI,CAACC,qBAAqB,CAACC,QAAQ,EAAE,MAAM,CAAC;MAC9C,CAAC,MAAM;QACL,MAAMA,QAAQ,GAAG,IAAI,CAAC5C,eAAe,CAAC6C,MAAM,CAAEC,IAAI,IAChDA,IAAI,CAAC7B,IAAI,EAAEqB,WAAW,EAAE,CAACN,QAAQ,CAACU,KAAK,CAAC,CACzC;QACD,IAAI,CAACC,qBAAqB,CAACC,QAAQ,EAAE,UAAU,CAAC;MAClD;IACF;IAEOK,iBAAiBA,CAACC,IAAS;MAChCjB,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEgB,IAAI,CAAC;IACzC;IAEOC,MAAMA,CAACC,CAAM;MAClBnB,OAAO,CAACC,GAAG,CAAC,KAAK,GAAGkB,CAAC,CAAC;IACxB;IAEOC,kBAAkBA,CAAA;MACvB,IAAI,CAAC3D,oBAAoB,GAAG,CAAC,IAAI,CAACA,oBAAoB;IACxD;IAEO4D,qBAAqBA,CAACC,WAAoB;MAC/C,IAAI,CAAC9D,gBAAgB,GAAG8D,WAAW;MACnCtB,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEqB,WAAW,CAAC;IACtD;IAEOC,gBAAgBA,CAACV,IAAS;MAC/B,IAAI,CAACjD,iBAAiB,CAAC4D,OAAO,CAAEL,CAAC,IAAMA,CAAC,CAAC3B,MAAM,GAAG,KAAM,CAAC;MACzDqB,IAAI,CAACrB,MAAM,GAAG,IAAI;MAClBQ,OAAO,CAACC,GAAG,CAACY,IAAI,CAAC;IACnB;IAEOY,eAAeA,CAACvC,KAAgC;MACrD,OAAOA,KAAK,KAAK,UAAU,IAAIA,KAAK,KAAK,UAAU,IAAIA,KAAK,KAAK,QAAQ,GACrEA,KAAK,GACL,QAAQ;IACd;IAEOwC,eAAeA,CAAA;MACpB,IAAI,CAAC1E,eAAe,CACjB2E,iBAAiB,CAAC,IAAI,CAAC1D,gBAAgB,EAAE,IAAI,CAACG,QAAQ,EAAE,IAAI,CAACE,SAAS,CAAC,CACvEgC,SAAS,CAAEsB,QAAQ,IAAI;QACtB,IAAI,IAAI,CAAC3D,gBAAgB,GAAG,CAAC,EAAE;UAC7B,IAAI,CAACH,WAAW,GAAG,CACjB,GAAG,IAAI,CAACA,WAAW,EACnB,GAAG8D,QAAQ,CAACC,qBAAqB,CAClC;QACH,CAAC,MAAM;UACL,IAAI,CAAC/D,WAAW,GAAG8D,QAAQ,EAAEC,qBAAqB;QACpD;QACA,IAAI,CAAC/D,WAAW,GAAG,IAAI,CAACA,WAAW,CAAC8C,MAAM,CACvCkB,CAAC,IAAKA,CAAC,CAACC,MAAM,KAAK,UAAU,CAC/B;QACD,IAAI,CAAC1D,YAAY,GAAG,IAAI,CAACP,WAAW,CAACkE,MAAM;QAC3C,IAAI,CAACtB,qBAAqB,CAAC,IAAI,CAAC5C,WAAW,EAAE,MAAM,CAAC;MACtD,CAAC,CAAC;IACN;IAEOmE,mBAAmBA,CAAA;MACxB,IAAI,CAACjF,eAAe,CACjBkF,qBAAqB,CACpB,IAAI,CAAC/D,oBAAoB,EACzB,IAAI,CAACC,QAAQ,EACb,IAAI,CAACE,SAAS,CACf,CACAgC,SAAS,CAAEsB,QAAQ,IAAI;QACtB,IAAI,IAAI,CAACzD,oBAAoB,GAAG,CAAC,EAAE;UACjC,IAAI,CAACJ,eAAe,GAAG,CACrB,GAAG,IAAI,CAACA,eAAe,EACvB,GAAG6D,QAAQ,CAACO,qBAAqB,CAClC;QACH,CAAC,MAAM;UACL,IAAI,CAACpE,eAAe,GAAG6D,QAAQ,EAAEO,qBAAqB;QACxD;QACA,IAAI,CAACpE,eAAe,GAAG,IAAI,CAACA,eAAe,CAAC6C,MAAM,CAC/CkB,CAAC,IAAKA,CAAC,CAACC,MAAM,KAAK,UAAU,CAC/B;QACD,IAAI,CAAC1D,YAAY,GAAG,IAAI,CAACN,eAAe,CAACiE,MAAM;QAC/ChC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAClC,eAAe,CAAC;QAC7C,IAAI,CAAC2C,qBAAqB,CAAC,IAAI,CAAC3C,eAAe,EAAE,UAAU,CAAC;MAC9D,CAAC,CAAC;IACN;IAEOqE,iBAAiBA,CAAA;MACtB,IAAI,CAACpF,eAAe,CACjBqF,kBAAkB,CAAC,IAAI,CAACnE,iBAAiB,EAAE,IAAI,CAACE,QAAQ,EAAE,IAAI,CAACE,SAAS,CAAC,CACzEgC,SAAS,CAAEsB,QAAQ,IAAI;QACtB,IAAI,IAAI,CAAC1D,iBAAiB,GAAG,CAAC,EAAE;UAC9B,IAAI,CAACF,aAAa,GAAG,CACnB,GAAG,IAAI,CAACA,aAAa,EACrB,GAAG4D,QAAQ,CAACU,kBAAkB,CAC/B;QACH,CAAC,MAAM;UACL,IAAI,CAACtE,aAAa,GAAG4D,QAAQ,EAAEU,kBAAkB;QACnD;QACA,IAAI,CAACtE,aAAa,GAAG,IAAI,CAACA,aAAa,CAAC4C,MAAM,CAC3CkB,CAAC,IAAKA,CAAC,CAACC,MAAM,KAAK,UAAU,CAC/B;QACD,IAAI,CAAC1D,YAAY,GAAG,IAAI,CAACL,aAAa,CAACgE,MAAM;QAC7C,IAAI,CAACtB,qBAAqB,CAAC,IAAI,CAAC1C,aAAa,EAAE,OAAO,CAAC;MACzD,CAAC,CAAC;IACN;IAEOuE,aAAaA,CAACC,IAAY;MAC/B,IAAI,CAACvE,gBAAgB,GAAGuE,IAAI;MAC5B,IAAI,CAACd,eAAe,EAAE;IACxB;IAEOe,cAAcA,CAACD,IAAY;MAChC,IAAI,CAACtE,iBAAiB,GAAGsE,IAAI;MAC7B,IAAI,CAACJ,iBAAiB,EAAE;IAC1B;IAEOM,iBAAiBA,CAACF,IAAY;MACnC,IAAI,CAACrE,oBAAoB,GAAGqE,IAAI;MAChC,IAAI,CAACP,mBAAmB,EAAE;IAC5B;IAEOU,WAAWA,CAAC3D,IAAY;MAC7B,IAAI,CAAC7C,UAAU,GAAG6C,IAAI;MACtB,IAAIA,IAAI,IAAI,OAAO,EAAE;QACnB,IAAI,CAAC0C,eAAe,EAAE;MACxB,CAAC,MAAM,IAAI1C,IAAI,IAAI,QAAQ,EAAE;QAC3B,IAAI,CAACoD,iBAAiB,EAAE;MAC1B,CAAC,MAAM;QACL,IAAI,CAACH,mBAAmB,EAAE;MAC5B;IACF;IAEOxG,wBAAwBA,CAACuD,IAAY;MAC1CgB,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEjB,IAAI,CAAC;MACpCgB,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAACvE,MAAM,CAAC;MAC3C,IAAI,CAACS,UAAU,GAAG6C,IAAI;MACtBgB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE,IAAI,CAAC9D,UAAU,CAAC;MAEtD,IAAI6C,IAAI,KAAK,IAAI,CAACtD,MAAM,CAACM,KAAK,EAAE;QAC9B,IAAI,CAACa,MAAM,CAAC+F,QAAQ,CAAC,CAAC,gBAAgB,CAAC,EAAE;UAAEC,UAAU,EAAE,IAAI,CAAC/F;QAAK,CAAE,CAAC;MACtE,CAAC,MAAM,IAAIkC,IAAI,KAAK,IAAI,CAACtD,MAAM,CAACC,MAAM,EAAE;QACtC,IAAI,CAACkB,MAAM,CAAC+F,QAAQ,CAAC,CAAC,iBAAiB,CAAC,EAAE;UAAEC,UAAU,EAAE,IAAI,CAAC/F;QAAK,CAAE,CAAC;MACvE,CAAC,MAAM,IAAIkC,IAAI,KAAK,IAAI,CAACtD,MAAM,CAACI,SAAS,EAAE;QACzC,IAAI,CAACe,MAAM,CAAC+F,QAAQ,CAAC,CAAC,oBAAoB,CAAC,EAAE;UAAEC,UAAU,EAAE,IAAI,CAAC/F;QAAK,CAAE,CAAC;MAC1E;IACF;IAEOgG,cAAcA,CAACC,GAAQ;MAC5B/C,OAAO,CAACC,GAAG,CAAC8C,GAAG,CAAC;MAChB,IAAI,CAACpE,aAAa,GAAGoE,GAAG;MACxB,IAAI,CAACC,kBAAkB,EAAE;IAC3B;IAEOC,eAAeA,CAACF,GAAQ;MAC7B/C,OAAO,CAACC,GAAG,CAAC8C,GAAG,CAAC;MAChB,IAAI,CAACpE,aAAa,GAAGoE,GAAG;MACxB,IAAI,CAACG,kBAAkB,EAAE;IAC3B;IAEA;IACQA,kBAAkBA,CAAA;MACxB,IAAI,CAAChG,aAAa,CAACiG,YAAY,CAAC;QAC9BC,KAAK,EAAE,IAAI,CAAC1H,MAAM,CAAC2H,eAAe;QAClCC,OAAO,EAAE,GAAG,IAAI,CAAC5H,MAAM,CAAC6H,wBAAwB,IAAI,IAAI,CAACpH,UAAU,KAAK,IAAI,CAACT,MAAM,CAAC8H,4BAA4B,IAAI,IAAI,CAACrH,UAAU,IAAI,IAAI,CAACT,MAAM,CAAC+H,0BAA0B,EAAE;QAC/KC,iBAAiB,EAAE,IAAI,CAAChI,MAAM,CAACiI,OAAO;QACtCC,gBAAgB,EAAE,QAAQ;QAC1BC,oBAAoB,EAAE,QAAQ;QAC9BvE,IAAI,EAAC;OACN,CAAC,CAACwE,IAAI,CAACC,MAAM,IAAG;QACf,IAAIA,MAAM,CAACC,SAAS,EAAE;UACpB,IAAI,CAACC,cAAc,EAAE;QACvB;MACF,CAAC,CAAC;IACJ;IAEQjB,kBAAkBA,CAAA;MACvB,MAAMkB,aAAa,GAAmB,CACrC;QAAEzE,KAAK,EAAE,QAAQ;QAAE0E,OAAO,EAAE,WAAW;QAAEC,MAAM,EAAE;MAAQ,CAAE,EAC3D;QAAE3E,KAAK,EAAE,WAAW;QAAE0E,OAAO,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAU,CAAE,CAC/D;MACD,IAAI,CAAClH,aAAa,CAACmH,QAAQ,CAAC;QAC1BjB,KAAK,EAAE,mBAAmB;QAC1BE,OAAO,EAAE,QAAQ,IAAI,CAACnH,UAAU,4FAA4F;QAC5HmI,OAAO,EAACJ;OACT,CAAC,CAACJ,IAAI,CAACC,MAAM,IAAG;QACf,IAAIA,MAAM,CAACC,SAAS,IAAID,MAAM,CAACC,SAAS,KAAK,IAAI,EAAE;UACjD,IAAI,CAACO,eAAe,CAACR,MAAM,CAAC9C,IAAI,CAAC;QACnC;MACF,CAAC,CAAC;IACJ;IAEOuD,aAAaA,CAACC,KAAU;MAC7BzE,OAAO,CAACC,GAAG,CAACwE,KAAK,CAAC;MAClB,IAAI,IAAI,CAACtI,UAAU,IAAI,OAAO,EAAE;QAC9B,MAAMuI,MAAM,GAAG,IAAI,CAAC5G,WAAW,CAAC2G,KAAK,CAAC,CAACC,MAAM;QAC7C,IAAI,CAACC,wBAAwB,CAACD,MAAM,CAAC;MACvC,CAAC,MAAM,IAAI,IAAI,CAACvI,UAAU,IAAI,QAAQ,EAAE;QACtC,MAAMyI,OAAO,GAAG,IAAI,CAAC5G,aAAa,CAACyG,KAAK,CAAC,CAACG,OAAO;QACjD,IAAI,CAACC,0BAA0B,CAACD,OAAO,EAAE,eAAe,CAAC;MAC3D,CAAC,MAAM;QACL,MAAME,UAAU,GAAG,IAAI,CAAC/G,eAAe,CAAC0G,KAAK,CAAC,CAACK,UAAU;QACzD,IAAI,CAACC,4BAA4B,CAACD,UAAU,CAAC;MAC/C;IACF;IAEOH,wBAAwBA,CAACtF,EAAU;MACxC,IAAI,CAACxC,MAAM,CAAC+F,QAAQ,CAAC,CAAC,0BAA0B,EAAEvD,EAAE,CAAC,CAAC;IACxD;IAEO0F,4BAA4BA,CAAC1F,EAAU;MAC5C,IAAI,CAACxC,MAAM,CAAC+F,QAAQ,CAAC,CAAC,0BAA0B,EAAEvD,EAAE,CAAC,CAAC;IACxD;IAEOwF,0BAA0BA,CAACxF,EAAU,EAAE2F,IAAY;MACxD,IAAI,CAACnI,MAAM,CAAC+F,QAAQ,CAAC,CAAC,eAAe,EAAEoC,IAAI,EAAE,SAAS,CAAC,EAAE;QACvDC,WAAW,EAAE;UAAE5F,EAAE,EAAEA;QAAE;OACtB,CAAC;IACJ;IAEO4E,cAAcA,CAAA;MACnB,IAAI,IAAI,CAAC9H,UAAU,IAAI,OAAO,EAAE;QAC9B,IAAI,CAAC+I,kBAAkB,EAAE;MAC3B,CAAC,MAAM,IAAI,IAAI,CAAC/I,UAAU,IAAI,QAAQ,EAAE;QACtC,IAAI,CAACgJ,mBAAmB,EAAE;MAC5B,CAAC,MAAM;QACL,IAAI,CAACC,sBAAsB,EAAE;MAC/B;IACF;IAEOb,eAAeA,CAACF,QAAa;MAClC,IAAI,IAAI,CAAClI,UAAU,IAAI,OAAO,EAAE;QAC9B,IAAI,CAACkJ,mBAAmB,CAAChB,QAAQ,CAAC;MACpC,CAAC,MAAM,IAAI,IAAI,CAAClI,UAAU,IAAI,QAAQ,EAAE;QACtC,IAAI,CAACmJ,oBAAoB,CAACjB,QAAQ,CAAC;MACrC,CAAC,MAAM;QACL,IAAI,CAACkB,uBAAuB,CAAClB,QAAQ,CAAC;MACxC;IACF;IAEOa,kBAAkBA,CAAA;MACvB,MAAMM,WAAW,GAAG,IAAI,CAAC1H,WAAW,CAAC,IAAI,CAACa,aAAa,CAAC;MACxD,MAAMU,EAAE,GAAGmG,WAAW,CAACnG,EAAE;MACzB,MAAMqF,MAAM,GAAGc,WAAW,CAACd,MAAM;MACjC,MAAM3C,MAAM,GAAG,UAAU;MACzB,MAAM0D,UAAU,GAAGD,WAAW,CAACC,UAAU;MAEzC;MACA,IAAI,CAACvI,aAAa,CAACwI,OAAO,CAAC;QACzBtC,KAAK,EAAE,mBAAmB;QAC1BE,OAAO,EAAE,wCAAwC;QACjDqC,YAAY,EAAE,KAAK;QACnBC,gBAAgB,EAAE;OACnB,CAAC;MAEF,IAAI,CAAC5I,eAAe,CAAC6I,WAAW,CAACxG,EAAE,EAAEqF,MAAM,EAAE3C,MAAM,EAAE0D,UAAU,CAAC,CAACnF,SAAS,CAAC;QACzEwF,IAAI,EAAGlE,QAAa,IAAI;UACtB,IAAI,CAAC1E,aAAa,CAAC6I,KAAK,EAAE,CAAC,CAAC;UAE5B,MAAMzC,OAAO,GAAG1B,QAAQ,EAAE0B,OAAO,IAAI,IAAI,CAAC5H,MAAM,CAACsK,yBAAyB;UAC1E,IAAI,CAAC9I,aAAa,CAAC+I,OAAO,CAAC;YACzB7C,KAAK,EAAE,eAAe;YACtBE,OAAO,EAAEA;WACV,CAAC,CAACQ,IAAI,CAAC,MAAK;YACX,IAAI,CAACpC,eAAe,EAAE,CAAC,CAAC;UAC1B,CAAC,CAAC;QACJ,CAAC;QACDwE,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAAChJ,aAAa,CAAC6I,KAAK,EAAE,CAAC,CAAC;UAE5B,MAAMI,YAAY,GAAGD,KAAK,EAAEA,KAAK,EAAE5C,OAAO,IAAI,IAAI,CAAC5H,MAAM,CAAC0K,mBAAmB;UAC7E,IAAI,CAAClJ,aAAa,CAACgJ,KAAK,CAAC;YACvB9C,KAAK,EAAE,iBAAiB;YACxBE,OAAO,EAAE6C,YAAY;YACrBE,eAAe,EAAE,IAAI;YACrBC,eAAe,EAAE;WAClB,CAAC,CAACxC,IAAI,CAACC,MAAM,IAAG;YACf,IAAIA,MAAM,CAACK,MAAM,KAAK,OAAO,EAAE;cAC7B,IAAI,CAACc,kBAAkB,EAAE;YAC3B;UACF,CAAC,CAAC;QACJ;OACD,CAAC;IACJ;IAEOG,mBAAmBA,CAAChB,QAAa;MACtC,MAAMmB,WAAW,GAAG,IAAI,CAAC1H,WAAW,CAAC,IAAI,CAACa,aAAa,CAAC;MACxD,MAAMU,EAAE,GAAGmG,WAAW,CAACnG,EAAE;MACzB,MAAMqF,MAAM,GAAGc,WAAW,CAACd,MAAM;MACjC,MAAM3C,MAAM,GAAG,UAAU;MACzB,MAAM0D,UAAU,GAAGD,WAAW,CAACC,UAAU;MACzC,MAAMnC,OAAO,GAAGe,QAAQ;MAExB;MACA,IAAI,CAACnH,aAAa,CAACwI,OAAO,CAAC;QACzBtC,KAAK,EAAE,mBAAmB;QAC1BE,OAAO,EAAE,uCAAuC;QAChDqC,YAAY,EAAE,KAAK;QACnBC,gBAAgB,EAAE;OACnB,CAAC;MAEF,IAAI,CAAC5I,eAAe,CACjBuJ,UAAU,CAAClH,EAAE,EAAEqF,MAAM,EAAE3C,MAAM,EAAE0D,UAAU,EAAEnC,OAAO,CAAC,CACnDhD,SAAS,CAAC;QACTwF,IAAI,EAAGlE,QAAa,IAAI;UACtB,IAAI,CAAC1E,aAAa,CAAC6I,KAAK,EAAE,CAAC,CAAC;UAE5B,MAAMzC,OAAO,GAAG1B,QAAQ,EAAE0B,OAAO,IAAI,IAAI,CAAC5H,MAAM,CAAC8K,wBAAwB;UACzE,IAAI,CAACtJ,aAAa,CAAC+I,OAAO,CAAC;YACzB7C,KAAK,EAAE,eAAe;YACtBE,OAAO,EAAEA;WACV,CAAC,CAACQ,IAAI,CAAC,MAAK;YACX,IAAI,CAACpC,eAAe,EAAE,CAAC,CAAC;UAC1B,CAAC,CAAC;QACJ,CAAC;QACDwE,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAAChJ,aAAa,CAAC6I,KAAK,EAAE,CAAC,CAAC;UAE5B,MAAMI,YAAY,GAAGD,KAAK,EAAEA,KAAK,EAAE5C,OAAO,IAAI,IAAI,CAAC5H,MAAM,CAAC0K,mBAAmB;UAC7E,IAAI,CAAClJ,aAAa,CAACgJ,KAAK,CAAC;YACvB9C,KAAK,EAAE,kBAAkB;YACzBE,OAAO,EAAE6C,YAAY;YACrBE,eAAe,EAAE,IAAI;YACrBC,eAAe,EAAE;WAClB,CAAC,CAACxC,IAAI,CAACC,MAAM,IAAG;YACf,IAAIA,MAAM,CAACK,MAAM,KAAK,OAAO,EAAE;cAC7B,IAAI,CAACiB,mBAAmB,CAAChB,QAAQ,CAAC;YACpC;UACF,CAAC,CAAC;QACJ;OACD,CAAC;IACN;IAEOc,mBAAmBA,CAAA;MACxB,MAAMsB,YAAY,GAAG,IAAI,CAACzI,aAAa,CAAC,IAAI,CAACW,aAAa,CAAC;MAC3D,MAAMU,EAAE,GAAGoH,YAAY,CAACpH,EAAE;MAC1B,MAAMuF,OAAO,GAAG6B,YAAY,CAAC7B,OAAO;MACpC,MAAM7C,MAAM,GAAG,UAAU;MACzB,MAAM0D,UAAU,GAAGgB,YAAY,CAAChB,UAAU;MAE1C;MACA,IAAI,CAACvI,aAAa,CAACwI,OAAO,CAAC;QACzBtC,KAAK,EAAE,oBAAoB;QAC3BE,OAAO,EAAE,yCAAyC;QAClDqC,YAAY,EAAE,KAAK;QACnBC,gBAAgB,EAAE;OACnB,CAAC;MAEF,IAAI,CAAC5I,eAAe,CACjB0J,YAAY,CAACrH,EAAE,EAAEuF,OAAO,EAAE7C,MAAM,EAAE0D,UAAU,CAAC,CAC7CnF,SAAS,CAAC;QACTwF,IAAI,EAAGlE,QAAa,IAAI;UACtB,IAAI,CAAC1E,aAAa,CAAC6I,KAAK,EAAE,CAAC,CAAC;UAE5B,MAAMzC,OAAO,GAAG1B,QAAQ,EAAE0B,OAAO,IAAI,IAAI,CAAC5H,MAAM,CAACiL,0BAA0B;UAE3E;UACA,IAAI,CAAC7H,eAAe,GAAG8F,OAAO;UAE9B,IAAI,CAAC1H,aAAa,CAAC+I,OAAO,CAAC;YACzB7C,KAAK,EAAE,gBAAgB;YACvBE,OAAO,EAAEA;WACV,CAAC,CAACQ,IAAI,CAACC,MAAM,IAAG;YACf,IAAIA,MAAM,CAACK,MAAM,KAAK,WAAW,EAAE;cACjC;cACA,IAAI,CAACvH,MAAM,CAAC+F,QAAQ,CAAC,CAAC,6BAA6B,CAAC,EAAE;gBACpDqC,WAAW,EAAE;kBACX5F,EAAE,EAAE,IAAI,CAACP,eAAe;kBACxB8H,IAAI,EAAE;;eAET,CAAC;YACJ,CAAC,MAAM;cACL,IAAI,CAACxE,iBAAiB,EAAE,CAAC,CAAC;YAC5B;YACA;YACA,IAAI,CAACtD,eAAe,GAAG,IAAI;UAC7B,CAAC,CAAC;QACJ,CAAC;QACDoH,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAAChJ,aAAa,CAAC6I,KAAK,EAAE,CAAC,CAAC;UAE5B,MAAMI,YAAY,GAAGD,KAAK,EAAEA,KAAK,EAAE5C,OAAO,IAAI,IAAI,CAAC5H,MAAM,CAAC0K,mBAAmB;UAC7E,IAAI,CAAClJ,aAAa,CAACgJ,KAAK,CAAC;YACvB9C,KAAK,EAAE,iBAAiB;YACxBE,OAAO,EAAE6C,YAAY;YACrBE,eAAe,EAAE,IAAI;YACrBC,eAAe,EAAE;WAClB,CAAC,CAACxC,IAAI,CAACC,MAAM,IAAG;YACf,IAAIA,MAAM,CAACK,MAAM,KAAK,OAAO,EAAE;cAC7B,IAAI,CAACe,mBAAmB,EAAE;YAC5B;UACF,CAAC,CAAC;QACJ;OACD,CAAC;IACN;IAEOG,oBAAoBA,CAACjB,QAAa;MACvC,MAAMoC,YAAY,GAAG,IAAI,CAACzI,aAAa,CAAC,IAAI,CAACW,aAAa,CAAC;MAC3D,MAAMU,EAAE,GAAGoH,YAAY,CAACpH,EAAE;MAC1B,MAAMuF,OAAO,GAAG6B,YAAY,CAAC7B,OAAO;MACpC,MAAM7C,MAAM,GAAG,UAAU;MACzB,MAAM0D,UAAU,GAAGgB,YAAY,CAAChB,UAAU;MAC1C,MAAMnC,OAAO,GAAGe,QAAQ;MAExB;MACA,IAAI,CAACnH,aAAa,CAACwI,OAAO,CAAC;QACzBtC,KAAK,EAAE,oBAAoB;QAC3BE,OAAO,EAAE,wCAAwC;QACjDqC,YAAY,EAAE,KAAK;QACnBC,gBAAgB,EAAE;OACnB,CAAC;MAEF,IAAI,CAAC5I,eAAe,CACjB6J,WAAW,CAACxH,EAAE,EAAEuF,OAAO,EAAE7C,MAAM,EAAE0D,UAAU,EAAEnC,OAAO,CAAC,CACrDhD,SAAS,CAAC;QACTwF,IAAI,EAAGlE,QAAa,IAAI;UACtB,IAAI,CAAC1E,aAAa,CAAC6I,KAAK,EAAE,CAAC,CAAC;UAE5B,MAAMzC,OAAO,GAAG1B,QAAQ,EAAE0B,OAAO,IAAI,IAAI,CAAC5H,MAAM,CAACoL,yBAAyB;UAC1E,IAAI,CAAC5J,aAAa,CAAC+I,OAAO,CAAC;YACzB7C,KAAK,EAAE,gBAAgB;YACvBE,OAAO,EAAEA;WACV,CAAC,CAACQ,IAAI,CAAC,MAAK;YACX,IAAI,CAAC1B,iBAAiB,EAAE,CAAC,CAAC;UAC5B,CAAC,CAAC;QACJ,CAAC;QACD8D,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAAChJ,aAAa,CAAC6I,KAAK,EAAE,CAAC,CAAC;UAE5B,MAAMI,YAAY,GAAGD,KAAK,EAAEA,KAAK,EAAE5C,OAAO,IAAI,IAAI,CAAC5H,MAAM,CAAC0K,mBAAmB;UAC7E,IAAI,CAAClJ,aAAa,CAACgJ,KAAK,CAAC;YACvB9C,KAAK,EAAE,kBAAkB;YACzBE,OAAO,EAAE6C,YAAY;YACrBE,eAAe,EAAE,IAAI;YACrBC,eAAe,EAAE;WAClB,CAAC,CAACxC,IAAI,CAACC,MAAM,IAAG;YACf,IAAIA,MAAM,CAACK,MAAM,KAAK,OAAO,EAAE;cAC7B,IAAI,CAACkB,oBAAoB,CAACjB,QAAQ,CAAC;YACrC;UACF,CAAC,CAAC;QACJ;OACD,CAAC;IACN;IAEOe,sBAAsBA,CAAA;MAC3B,MAAM2B,eAAe,GAAG,IAAI,CAAChJ,eAAe,CAAC,IAAI,CAACY,aAAa,CAAC;MAChE,MAAMU,EAAE,GAAG0H,eAAe,EAAE1H,EAAE;MAC9B,MAAMyF,UAAU,GAAGiC,eAAe,EAAEjC,UAAU;MAC9C,MAAM/C,MAAM,GAAG,UAAU;MACzB,MAAM0D,UAAU,GAAGsB,eAAe,EAAEtB,UAAU;MAC9CzF,OAAO,CAACC,GAAG,CAACZ,EAAE,EAAEyF,UAAU,EAAE/C,MAAM,EAAE0D,UAAU,CAAC;MAE/C;MACA,IAAI,CAACvI,aAAa,CAACwI,OAAO,CAAC;QACzBtC,KAAK,EAAE,uBAAuB;QAC9BE,OAAO,EAAE,4CAA4C;QACrDqC,YAAY,EAAE,KAAK;QACnBC,gBAAgB,EAAE;OACnB,CAAC;MAEF,IAAI,CAAC5I,eAAe,CACjBgK,eAAe,CAAC3H,EAAE,EAAEyF,UAAU,EAAE/C,MAAM,EAAE0D,UAAU,CAAC,CACnDnF,SAAS,CAAC;QACTwF,IAAI,EAAGlE,QAAa,IAAI;UACtB,IAAI,CAAC1E,aAAa,CAAC6I,KAAK,EAAE,CAAC,CAAC;UAE5B,MAAMzC,OAAO,GAAG1B,QAAQ,EAAE0B,OAAO,IAAI,IAAI,CAAC5H,MAAM,CAACuL,6BAA6B;UAC9E,IAAI,CAAC/J,aAAa,CAAC+I,OAAO,CAAC;YACzB7C,KAAK,EAAE,mBAAmB;YAC1BE,OAAO,EAAEA;WACV,CAAC,CAACQ,IAAI,CAAC,MAAK;YACX,IAAI,CAAC7B,mBAAmB,EAAE,CAAC,CAAC;UAC9B,CAAC,CAAC;QACJ,CAAC;QACDiE,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAAChJ,aAAa,CAAC6I,KAAK,EAAE,CAAC,CAAC;UAE5B,MAAMI,YAAY,GAAGD,KAAK,EAAEA,KAAK,EAAE5C,OAAO,IAAI,IAAI,CAAC5H,MAAM,CAAC0K,mBAAmB;UAC7E,IAAI,CAAClJ,aAAa,CAACgJ,KAAK,CAAC;YACvB9C,KAAK,EAAE,iBAAiB;YACxBE,OAAO,EAAE6C,YAAY;YACrBE,eAAe,EAAE,IAAI;YACrBC,eAAe,EAAE;WAClB,CAAC,CAACxC,IAAI,CAACC,MAAM,IAAG;YACf,IAAIA,MAAM,CAACK,MAAM,KAAK,OAAO,EAAE;cAC7B,IAAI,CAACgB,sBAAsB,EAAE;YAC/B;UACF,CAAC,CAAC;QACJ;OACD,CAAC;IACN;IAEOG,uBAAuBA,CAAClB,QAAa;MAC1C,MAAM0C,eAAe,GAAG,IAAI,CAAChJ,eAAe,CAAC,IAAI,CAACY,aAAa,CAAC;MAChE,MAAMU,EAAE,GAAG0H,eAAe,EAAE1H,EAAE;MAC9B,MAAMyF,UAAU,GAAGiC,eAAe,EAAEjC,UAAU;MAC9C,MAAM/C,MAAM,GAAG,UAAU;MACzB,MAAM0D,UAAU,GAAGsB,eAAe,EAAEtB,UAAU;MAC9C,MAAMnC,OAAO,GAAGe,QAAQ;MACxBrE,OAAO,CAACC,GAAG,CAACZ,EAAE,EAAEyF,UAAU,EAAE/C,MAAM,EAAE0D,UAAU,EAAEnC,OAAO,CAAC;MAExD;MACA,IAAI,CAACpG,aAAa,CAACwI,OAAO,CAAC;QACzBtC,KAAK,EAAE,uBAAuB;QAC9BE,OAAO,EAAE,2CAA2C;QACpDqC,YAAY,EAAE,KAAK;QACnBC,gBAAgB,EAAE;OACnB,CAAC;MAEF,IAAI,CAAC5I,eAAe,CACjBkK,cAAc,CAAC7H,EAAE,EAAEyF,UAAU,EAAE/C,MAAM,EAAE0D,UAAU,EAAEnC,OAAO,CAAC,CAC3DhD,SAAS,CAAC;QACTwF,IAAI,EAAGlE,QAAa,IAAI;UACtB,IAAI,CAAC1E,aAAa,CAAC6I,KAAK,EAAE,CAAC,CAAC;UAE5B,MAAMzC,OAAO,GAAG1B,QAAQ,EAAE0B,OAAO,IAAI,IAAI,CAAC5H,MAAM,CAACyL,4BAA4B;UAC7E,IAAI,CAACjK,aAAa,CAAC+I,OAAO,CAAC;YACzB7C,KAAK,EAAE,mBAAmB;YAC1BE,OAAO,EAAEA;WACV,CAAC,CAACQ,IAAI,CAAC,MAAK;YACX,IAAI,CAAC7B,mBAAmB,EAAE,CAAC,CAAC;UAC9B,CAAC,CAAC;QACJ,CAAC;QACDiE,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAAChJ,aAAa,CAAC6I,KAAK,EAAE,CAAC,CAAC;UAE5B,MAAMI,YAAY,GAAGD,KAAK,EAAEA,KAAK,EAAE5C,OAAO,IAAI,IAAI,CAAC5H,MAAM,CAAC0K,mBAAmB;UAC7E,IAAI,CAAClJ,aAAa,CAACgJ,KAAK,CAAC;YACvB9C,KAAK,EAAE,kBAAkB;YACzBE,OAAO,EAAE6C,YAAY;YACrBE,eAAe,EAAE,IAAI;YACrBC,eAAe,EAAE;WAClB,CAAC,CAACxC,IAAI,CAACC,MAAM,IAAG;YACf,IAAIA,MAAM,CAACK,MAAM,KAAK,OAAO,EAAE;cAC7B,IAAI,CAACmB,uBAAuB,CAAClB,QAAQ,CAAC;YACxC;UACF,CAAC,CAAC;QACJ;OACD,CAAC;IACN;IAEA;IAEO3D,qBAAqBA,CAACO,IAAW,EAAE+D,IAAY;MACpD,IAAI,CAACtH,eAAe,GAAG;QACrB0J,QAAQ,EAAEnG,IAAI,EAAEtG,GAAG,CAAE0M,GAAQ,IAAI;UAC/B,MAAMC,WAAW,GAAkC;YACjDC,QAAQ,EAAE,kBAAkB;YAC5BC,QAAQ,EAAE,UAAU;YACpBC,MAAM,EAAE;WACT;UACD,MAAMC,WAAW,GAAkC;YACjDH,QAAQ,EAAE,IAAI,CAAC7L,MAAM,CAAC6L,QAAQ;YAC9BC,QAAQ,EAAE,IAAI,CAAC9L,MAAM,CAAC8L,QAAQ;YAC9BC,MAAM,EAAE,IAAI,CAAC/L,MAAM,CAAC+L;WACrB;UACD,MAAME,SAAS,GAAG,IAAI,CAAClG,eAAe,CAAC4F,GAAG,EAAEtF,MAAM,CAAC;UACnD,IAAI6F,UAAU,GAAG,CAAC;UAClB,IAAIxE,KAAK,GAAG,EAAE;UAEd,IAAI4B,IAAI,KAAK,MAAM,EAAE;YACnB4C,UAAU,GAAGP,GAAG,CAAC3C,MAAM;YACvBtB,KAAK,GAAGiE,GAAG,CAACtG,QAAQ;UACtB,CAAC,MAAM,IAAIiE,IAAI,KAAK,OAAO,EAAE;YAC3B4C,UAAU,GAAGP,GAAG,CAACzC,OAAO;YACxBxB,KAAK,GAAGiE,GAAG,CAACvG,SAAS;UACvB,CAAC,MAAM;YACL8G,UAAU,GAAGP,GAAG,CAACvC,UAAU;YAC3B1B,KAAK,GAAGiE,GAAG,CAACQ,YAAY;UAC1B;UAEA,OAAO;YACLxI,EAAE,EAAEgI,GAAG,CAAChI,EAAE;YACVyI,KAAK,EAAEF,UAAU;YACjB5C,IAAI,EAAEA,IAAI;YACV+C,QAAQ,EAAE;cACR3E,KAAK,EAAEA,KAAK;cACZ1H,MAAM,EAAE,CACN;gBACEsD,IAAI,EAAEgG,IAAI;gBACVgD,KAAK,EAAE,SAAS;gBAChBC,UAAU,EAAE,KAAK;gBACjBjD,IAAI,EAAE;eACP,EACD;gBACEhG,IAAI,EAAEqI,GAAG,CAACa,iBAAiB;gBAC3BF,KAAK,EAAEX,GAAG,CAACa,iBAAiB,KAAK,QAAQ,GAAG,OAAO,GAAG,MAAM;gBAC5DD,UAAU,EAAE,KAAK;gBACjBjD,IAAI,EAAE;eACP;aAEJ;YACDmD,QAAQ,EAAE,CACR;cACEnJ,IAAI,EAAEgG,IAAI;cACVgD,KAAK,EAAE,SAAS;cAChBC,UAAU,EAAE,KAAK;cACjBjD,IAAI,EAAE;aACP,EACD;cACEhG,IAAI,EAAEqI,GAAG,CAACtF,MAAM;cAChBiG,KAAK,EAAE,SAAS;cAChBC,UAAU,EAAE,KAAK;cACjBjD,IAAI,EAAE;aACT,CACA;YACDoD,QAAQ,EAAE,CACR;cACEC,QAAQ,EAAE,MAAM;cAChB5I,KAAK,EAAE4H,GAAG,CAACiB;aACZ,EACD;cACED,QAAQ,EAAE,eAAe;cACzB5I,KAAK,EAAEpF,UAAU,CAACgN,GAAG,EAAEkB,WAAW,EAAE,aAAa,EAAE,OAAO;aAC3D,CACF;YACDC,QAAQ,EAAE;cACRzG,MAAM,EAAE2F,WAAW,CAACC,SAAS,CAAC;cAC9BU,QAAQ,EAAEf,WAAW,CAACK,SAAS;;WAElC;QACH,CAAC,CAAC;QACFc,MAAM,EAAE;OACT;IACH;;uCAjvBW7L,iBAAiB,EAAA9B,EAAA,CAAA4N,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAA9N,EAAA,CAAA4N,iBAAA,CAAAC,EAAA,CAAAE,cAAA,GAAA/N,EAAA,CAAA4N,iBAAA,CAAAI,EAAA,CAAAC,uBAAA,GAAAjO,EAAA,CAAA4N,iBAAA,CAAAM,EAAA,CAAAC,eAAA,GAAAnO,EAAA,CAAA4N,iBAAA,CAAAQ,EAAA,CAAAC,WAAA,GAAArO,EAAA,CAAA4N,iBAAA,CAAAU,EAAA,CAAAC,aAAA;IAAA;;YAAjBzM,iBAAiB;MAAA0M,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCvClB9O,EAHZ,CAAAC,cAAA,aAAsB,aACsE,aACT,aACrC;UAC9BD,EAAA,CAAAgP,UAAA,IAAAC,iCAAA,kBAAsD;UACtDjP,EAAA,CAAAC,cAAA,qBAAoF;UAA/BD,EAAA,CAAAI,UAAA,mBAAA8O,wDAAA;YAAA,OAASH,GAAA,CAAAzI,kBAAA,EAAoB;UAAA,EAAC;UACvFtG,EADwF,CAAAG,YAAA,EAAc,EAChG;UAmCNH,EAhCA,CAAAgP,UAAA,IAAAG,gCAAA,mBAAgE,IAAAC,gCAAA,mBAgCD;UA8BvEpP,EADI,CAAAG,YAAA,EAAM,EACJ;UAENH,EAAA,CAAAC,cAAA,aAAmC;UAC/BD,EAAA,CAAAc,SAAA,oBAA+B;UAEvCd,EADI,CAAAG,YAAA,EAAM,EACJ;;;UA3EgCH,EAAA,CAAAmB,SAAA,EAAqD;UAArDnB,EAAA,CAAAoB,WAAA,2BAAA2N,GAAA,CAAApM,oBAAA,CAAqD;UAChD3C,EAAA,CAAAmB,SAAA,EAAuC;UAAvCnB,EAAA,CAAAoB,WAAA,aAAA2N,GAAA,CAAApM,oBAAA,CAAuC;UAExC3C,EAAA,CAAAmB,SAAA,GAA0B;UAA1BnB,EAAA,CAAAuB,UAAA,SAAAwN,GAAA,CAAApM,oBAAA,CAA0B;UAKpB3C,EAAA,CAAAmB,SAAA,GAA0B;UAA1BnB,EAAA,CAAAuB,UAAA,SAAAwN,GAAA,CAAApM,oBAAA,CAA0B;UAgC5B3C,EAAA,CAAAmB,SAAA,EAA2B;UAA3BnB,EAAA,CAAAuB,UAAA,UAAAwN,GAAA,CAAApM,oBAAA,CAA2B;;;qBDVrErD,YAAY,EAAA+P,EAAA,CAAAC,IAAA,EACZ9P,YAAY,EAAAqO,EAAA,CAAA0B,YAAA,EAGZ9P,mBAAmB,EACnBM,mBAAmB,EAAAyP,EAAA,CAAAC,sBAAA;MAAAC,MAAA;IAAA;;SAMV5N,iBAAiB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}