{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = this && this.__param || function (paramIndex, decorator) {\n  return function (target, key) {\n    decorator(target, key, paramIndex);\n  };\n};\nimport { h, svgElem } from '../../../../base/browser/dom.js';\nimport { DEFAULT_FONT_FAMILY } from '../../../../base/browser/fonts.js';\nimport { Disposable, DisposableStore } from '../../../../base/common/lifecycle.js';\nimport { autorun, constObservable, derived } from '../../../../base/common/observable.js';\nimport { derivedWithSetter } from '../../../../base/common/observableInternal/derived.js';\nimport './inlineEditsWidget.css';\nimport { EditorExtensionsRegistry } from '../../../browser/editorExtensions.js';\nimport { observableCodeEditor } from '../../../browser/observableCodeEditor.js';\nimport { EmbeddedCodeEditorWidget } from '../../../browser/widget/codeEditor/embeddedCodeEditorWidget.js';\nimport { diffAddDecoration, diffAddDecorationEmpty, diffDeleteDecoration, diffDeleteDecorationEmpty, diffLineAddDecorationBackgroundWithIndicator, diffLineDeleteDecorationBackgroundWithIndicator, diffWholeLineAddDecoration, diffWholeLineDeleteDecoration } from '../../../browser/widget/diffEditor/registrations.contribution.js';\nimport { appendRemoveOnDispose, applyStyle } from '../../../browser/widget/diffEditor/utils.js';\nimport { PLAINTEXT_LANGUAGE_ID } from '../../../common/languages/modesRegistry.js';\nimport { TextModel } from '../../../common/model/textModel.js';\nimport { ContextMenuController } from '../../contextmenu/browser/contextmenu.js';\nimport { PlaceholderTextContribution } from '../../placeholderText/browser/placeholderTextContribution.js';\nimport { SuggestController } from '../../suggest/browser/suggestController.js';\nimport { IInstantiationService } from '../../../../platform/instantiation/common/instantiation.js';\nexport class InlineEdit {\n  constructor(range, newLines, changes) {\n    this.range = range;\n    this.newLines = newLines;\n    this.changes = changes;\n  }\n}\nlet InlineEditsWidget = class InlineEditsWidget extends Disposable {\n  constructor(_editor, _edit, _userPrompt, _instantiationService) {\n    super();\n    this._editor = _editor;\n    this._edit = _edit;\n    this._userPrompt = _userPrompt;\n    this._instantiationService = _instantiationService;\n    this._editorObs = observableCodeEditor(this._editor);\n    this._elements = h('div.inline-edits-widget', {\n      style: {\n        position: 'absolute',\n        overflow: 'visible',\n        top: '0px',\n        left: '0px'\n      }\n    }, [h('div@editorContainer', {\n      style: {\n        position: 'absolute',\n        top: '0px',\n        left: '0px',\n        width: '500px',\n        height: '500px'\n      }\n    }, [h('div.toolbar@toolbar', {\n      style: {\n        position: 'absolute',\n        top: '-25px',\n        left: '0px'\n      }\n    }), h('div.promptEditor@promptEditor', {\n      style: {\n        position: 'absolute',\n        top: '-25px',\n        left: '80px',\n        width: '300px',\n        height: '22px'\n      }\n    }), h('div.preview@editor', {\n      style: {\n        position: 'absolute',\n        top: '0px',\n        left: '0px'\n      }\n    })]), svgElem('svg', {\n      style: {\n        overflow: 'visible',\n        pointerEvents: 'none'\n      }\n    }, [svgElem('defs', [svgElem('linearGradient', {\n      id: 'Gradient2',\n      x1: '0',\n      y1: '0',\n      x2: '1',\n      y2: '0'\n    }, [\n    /*svgElem('stop', { offset: '0%', class: 'gradient-start', }),\n    svgElem('stop', { offset: '0%', class: 'gradient-start', }),\n    svgElem('stop', { offset: '20%', class: 'gradient-stop', }),*/\n    svgElem('stop', {\n      offset: '0%',\n      class: 'gradient-stop'\n    }), svgElem('stop', {\n      offset: '100%',\n      class: 'gradient-stop'\n    })])]), svgElem('path@path', {\n      d: '',\n      fill: 'url(#Gradient2)'\n    })])]);\n    this._previewTextModel = this._register(this._instantiationService.createInstance(TextModel, '', PLAINTEXT_LANGUAGE_ID, TextModel.DEFAULT_CREATION_OPTIONS, null));\n    this._setText = derived(reader => {\n      const edit = this._edit.read(reader);\n      if (!edit) {\n        return;\n      }\n      this._previewTextModel.setValue(edit.newLines.join('\\n'));\n    }).recomputeInitiallyAndOnChange(this._store);\n    this._promptTextModel = this._register(this._instantiationService.createInstance(TextModel, '', PLAINTEXT_LANGUAGE_ID, TextModel.DEFAULT_CREATION_OPTIONS, null));\n    this._promptEditor = this._register(this._instantiationService.createInstance(EmbeddedCodeEditorWidget, this._elements.promptEditor, {\n      glyphMargin: false,\n      lineNumbers: 'off',\n      minimap: {\n        enabled: false\n      },\n      guides: {\n        indentation: false,\n        bracketPairs: false,\n        bracketPairsHorizontal: false,\n        highlightActiveIndentation: false\n      },\n      folding: false,\n      selectOnLineNumbers: false,\n      selectionHighlight: false,\n      columnSelection: false,\n      overviewRulerBorder: false,\n      overviewRulerLanes: 0,\n      lineDecorationsWidth: 0,\n      lineNumbersMinChars: 0,\n      placeholder: 'Describe the change you want...',\n      fontFamily: DEFAULT_FONT_FAMILY\n    }, {\n      contributions: EditorExtensionsRegistry.getSomeEditorContributions([SuggestController.ID, PlaceholderTextContribution.ID, ContextMenuController.ID]),\n      isSimpleWidget: true\n    }, this._editor));\n    this._previewEditor = this._register(this._instantiationService.createInstance(EmbeddedCodeEditorWidget, this._elements.editor, {\n      glyphMargin: false,\n      lineNumbers: 'off',\n      minimap: {\n        enabled: false\n      },\n      guides: {\n        indentation: false,\n        bracketPairs: false,\n        bracketPairsHorizontal: false,\n        highlightActiveIndentation: false\n      },\n      folding: false,\n      selectOnLineNumbers: false,\n      selectionHighlight: false,\n      columnSelection: false,\n      overviewRulerBorder: false,\n      overviewRulerLanes: 0,\n      lineDecorationsWidth: 0,\n      lineNumbersMinChars: 0\n    }, {\n      contributions: []\n    }, this._editor));\n    this._previewEditorObs = observableCodeEditor(this._previewEditor);\n    this._decorations = derived(this, reader => {\n      this._setText.read(reader);\n      const diff = this._edit.read(reader)?.changes;\n      if (!diff) {\n        return [];\n      }\n      const originalDecorations = [];\n      const modifiedDecorations = [];\n      if (diff.length === 1 && diff[0].innerChanges[0].modifiedRange.equalsRange(this._previewTextModel.getFullModelRange())) {\n        return [];\n      }\n      for (const m of diff) {\n        if (!m.original.isEmpty) {\n          originalDecorations.push({\n            range: m.original.toInclusiveRange(),\n            options: diffLineDeleteDecorationBackgroundWithIndicator\n          });\n        }\n        if (!m.modified.isEmpty) {\n          modifiedDecorations.push({\n            range: m.modified.toInclusiveRange(),\n            options: diffLineAddDecorationBackgroundWithIndicator\n          });\n        }\n        if (m.modified.isEmpty || m.original.isEmpty) {\n          if (!m.original.isEmpty) {\n            originalDecorations.push({\n              range: m.original.toInclusiveRange(),\n              options: diffWholeLineDeleteDecoration\n            });\n          }\n          if (!m.modified.isEmpty) {\n            modifiedDecorations.push({\n              range: m.modified.toInclusiveRange(),\n              options: diffWholeLineAddDecoration\n            });\n          }\n        } else {\n          for (const i of m.innerChanges || []) {\n            // Don't show empty markers outside the line range\n            if (m.original.contains(i.originalRange.startLineNumber)) {\n              originalDecorations.push({\n                range: i.originalRange,\n                options: i.originalRange.isEmpty() ? diffDeleteDecorationEmpty : diffDeleteDecoration\n              });\n            }\n            if (m.modified.contains(i.modifiedRange.startLineNumber)) {\n              modifiedDecorations.push({\n                range: i.modifiedRange,\n                options: i.modifiedRange.isEmpty() ? diffAddDecorationEmpty : diffAddDecoration\n              });\n            }\n          }\n        }\n      }\n      return modifiedDecorations;\n    });\n    this._layout1 = derived(this, reader => {\n      const model = this._editor.getModel();\n      const inlineEdit = this._edit.read(reader);\n      if (!inlineEdit) {\n        return null;\n      }\n      const range = inlineEdit.range;\n      let maxLeft = 0;\n      for (let i = range.startLineNumber; i < range.endLineNumberExclusive; i++) {\n        const column = model.getLineMaxColumn(i);\n        const left = this._editor.getOffsetForColumn(i, column);\n        maxLeft = Math.max(maxLeft, left);\n      }\n      const layoutInfo = this._editor.getLayoutInfo();\n      const contentLeft = layoutInfo.contentLeft;\n      return {\n        left: contentLeft + maxLeft\n      };\n    });\n    this._layout = derived(this, reader => {\n      const inlineEdit = this._edit.read(reader);\n      if (!inlineEdit) {\n        return null;\n      }\n      const range = inlineEdit.range;\n      const scrollLeft = this._editorObs.scrollLeft.read(reader);\n      const left = this._layout1.read(reader).left + 20 - scrollLeft;\n      const selectionTop = this._editor.getTopForLineNumber(range.startLineNumber) - this._editorObs.scrollTop.read(reader);\n      const selectionBottom = this._editor.getTopForLineNumber(range.endLineNumberExclusive) - this._editorObs.scrollTop.read(reader);\n      const topCode = new Point(left, selectionTop);\n      const bottomCode = new Point(left, selectionBottom);\n      const codeHeight = selectionBottom - selectionTop;\n      const codeEditDist = 50;\n      const editHeight = this._editor.getOption(67 /* EditorOption.lineHeight */) * inlineEdit.newLines.length;\n      const difference = codeHeight - editHeight;\n      const topEdit = new Point(left + codeEditDist, selectionTop + difference / 2);\n      const bottomEdit = new Point(left + codeEditDist, selectionBottom - difference / 2);\n      return {\n        topCode,\n        bottomCode,\n        codeHeight,\n        topEdit,\n        bottomEdit,\n        editHeight\n      };\n    });\n    const visible = derived(this, reader => this._edit.read(reader) !== undefined || this._userPrompt.read(reader) !== undefined);\n    this._register(applyStyle(this._elements.root, {\n      display: derived(this, reader => visible.read(reader) ? 'block' : 'none')\n    }));\n    this._register(appendRemoveOnDispose(this._editor.getDomNode(), this._elements.root));\n    this._register(observableCodeEditor(_editor).createOverlayWidget({\n      domNode: this._elements.root,\n      position: constObservable(null),\n      allowEditorOverflow: false,\n      minContentWidthInPx: derived(reader => {\n        const x = this._layout1.read(reader)?.left;\n        if (x === undefined) {\n          return 0;\n        }\n        const width = this._previewEditorObs.contentWidth.read(reader);\n        return x + width;\n      })\n    }));\n    this._previewEditor.setModel(this._previewTextModel);\n    this._register(this._previewEditorObs.setDecorations(this._decorations));\n    this._register(autorun(reader => {\n      const layoutInfo = this._layout.read(reader);\n      if (!layoutInfo) {\n        return;\n      }\n      const {\n        topCode,\n        bottomCode,\n        topEdit,\n        bottomEdit,\n        editHeight\n      } = layoutInfo;\n      const straightWidthCode = 10;\n      const straightWidthEdit = 0;\n      const bezierDist = 40;\n      const path = new PathBuilder().moveTo(topCode).lineTo(topCode.deltaX(straightWidthCode)).curveTo(topCode.deltaX(straightWidthCode + bezierDist), topEdit.deltaX(-bezierDist - straightWidthEdit), topEdit.deltaX(-straightWidthEdit)).lineTo(topEdit).lineTo(bottomEdit).lineTo(bottomEdit.deltaX(-straightWidthEdit)).curveTo(bottomEdit.deltaX(-bezierDist - straightWidthEdit), bottomCode.deltaX(straightWidthCode + bezierDist), bottomCode.deltaX(straightWidthCode)).lineTo(bottomCode).build();\n      this._elements.path.setAttribute('d', path);\n      this._elements.editorContainer.style.top = `${topEdit.y}px`;\n      this._elements.editorContainer.style.left = `${topEdit.x}px`;\n      this._elements.editorContainer.style.height = `${editHeight}px`;\n      const width = this._previewEditorObs.contentWidth.read(reader);\n      this._previewEditor.layout({\n        height: editHeight,\n        width\n      });\n    }));\n    this._promptEditor.setModel(this._promptTextModel);\n    this._promptEditor.layout();\n    this._register(createTwoWaySync(mapSettableObservable(this._userPrompt, v => v ?? '', v => v), observableCodeEditor(this._promptEditor).value));\n    this._register(autorun(reader => {\n      const isFocused = observableCodeEditor(this._promptEditor).isFocused.read(reader);\n      this._elements.root.classList.toggle('focused', isFocused);\n    }));\n  }\n};\nInlineEditsWidget = __decorate([__param(3, IInstantiationService)], InlineEditsWidget);\nexport { InlineEditsWidget };\nfunction mapSettableObservable(obs, fn1, fn2) {\n  return derivedWithSetter(undefined, reader => fn1(obs.read(reader)), (value, tx) => obs.set(fn2(value), tx));\n}\nclass Point {\n  constructor(x, y) {\n    this.x = x;\n    this.y = y;\n  }\n  deltaX(delta) {\n    return new Point(this.x + delta, this.y);\n  }\n}\nclass PathBuilder {\n  constructor() {\n    this._data = '';\n  }\n  moveTo(point) {\n    this._data += `M ${point.x} ${point.y} `;\n    return this;\n  }\n  lineTo(point) {\n    this._data += `L ${point.x} ${point.y} `;\n    return this;\n  }\n  curveTo(cp1, cp2, to) {\n    this._data += `C ${cp1.x} ${cp1.y} ${cp2.x} ${cp2.y} ${to.x} ${to.y} `;\n    return this;\n  }\n  build() {\n    return this._data;\n  }\n}\nfunction createTwoWaySync(main, target) {\n  const store = new DisposableStore();\n  store.add(autorun(reader => {\n    const value = main.read(reader);\n    target.set(value, undefined);\n  }));\n  store.add(autorun(reader => {\n    const value = target.read(reader);\n    main.set(value, undefined);\n  }));\n  return store;\n}", "map": {"version": 3, "names": ["__decorate", "decorators", "target", "key", "desc", "c", "arguments", "length", "r", "Object", "getOwnPropertyDescriptor", "d", "Reflect", "decorate", "i", "defineProperty", "__param", "paramIndex", "decorator", "h", "svgElem", "DEFAULT_FONT_FAMILY", "Disposable", "DisposableStore", "autorun", "constObservable", "derived", "derivedWithSetter", "EditorExtensionsRegistry", "observableCodeEditor", "EmbeddedCodeEditorWidget", "diffAddDecoration", "diffAddDecorationEmpty", "diffDeleteDecoration", "diffDeleteDecorationEmpty", "diffLineAddDecorationBackgroundWithIndicator", "diffLineDeleteDecorationBackgroundWithIndicator", "diffWholeLineAddDecoration", "diffWholeLineDeleteDecoration", "appendRemoveOnDispose", "applyStyle", "PLAINTEXT_LANGUAGE_ID", "TextModel", "ContextMenuController", "PlaceholderTextContribution", "SuggestController", "IInstantiationService", "InlineEdit", "constructor", "range", "newLines", "changes", "InlineEditsWidget", "_editor", "_edit", "_userPrompt", "_instantiationService", "_editorObs", "_elements", "style", "position", "overflow", "top", "left", "width", "height", "pointerEvents", "id", "x1", "y1", "x2", "y2", "offset", "class", "fill", "_previewTextModel", "_register", "createInstance", "DEFAULT_CREATION_OPTIONS", "_setText", "reader", "edit", "read", "setValue", "join", "recomputeInitiallyAndOnChange", "_store", "_promptTextModel", "_promptEditor", "promptEditor", "glyphMargin", "lineNumbers", "minimap", "enabled", "guides", "indentation", "bracketPairs", "bracketPairsHorizontal", "highlightActiveIndentation", "folding", "selectOnLineNumbers", "selection<PERSON>ighlight", "columnSelection", "overviewRulerBorder", "overviewRulerLanes", "lineDecorationsWidth", "lineNumbersMinChars", "placeholder", "fontFamily", "contributions", "getSomeEditorContributions", "ID", "isSimpleWidget", "_previewEditor", "editor", "_previewEditorObs", "_decorations", "diff", "originalDecorations", "modifiedDecorations", "innerChanges", "modifiedRange", "equalsRange", "getFullModelRange", "m", "original", "isEmpty", "push", "toInclusiveRange", "options", "modified", "contains", "originalRange", "startLineNumber", "_layout1", "model", "getModel", "inlineEdit", "maxLeft", "endLineNumberExclusive", "column", "getLineMaxColumn", "getOffsetForColumn", "Math", "max", "layoutInfo", "getLayoutInfo", "contentLeft", "_layout", "scrollLeft", "selectionTop", "getTopForLineNumber", "scrollTop", "selectionBottom", "topCode", "Point", "bottomCode", "codeHeight", "codeEditDist", "edit<PERSON><PERSON><PERSON>", "getOption", "difference", "topEdit", "bottomEdit", "visible", "undefined", "root", "display", "getDomNode", "createOverlayWidget", "domNode", "allowEditorOverflow", "minContentWidthInPx", "x", "contentWidth", "setModel", "setDecorations", "straightWidthCode", "straightWidthEdit", "bezierDist", "path", "PathBuilder", "moveTo", "lineTo", "deltaX", "curveTo", "build", "setAttribute", "<PERSON><PERSON><PERSON><PERSON>", "y", "layout", "createTwoWaySync", "mapSettableObservable", "v", "value", "isFocused", "classList", "toggle", "obs", "fn1", "fn2", "tx", "set", "delta", "_data", "point", "cp1", "cp2", "to", "main", "store", "add"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/contrib/inlineEdits/browser/inlineEditsWidget.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = (this && this.__param) || function (paramIndex, decorator) {\n    return function (target, key) { decorator(target, key, paramIndex); }\n};\nimport { h, svgElem } from '../../../../base/browser/dom.js';\nimport { DEFAULT_FONT_FAMILY } from '../../../../base/browser/fonts.js';\nimport { Disposable, DisposableStore } from '../../../../base/common/lifecycle.js';\nimport { autorun, constObservable, derived } from '../../../../base/common/observable.js';\nimport { derivedWithSetter } from '../../../../base/common/observableInternal/derived.js';\nimport './inlineEditsWidget.css';\nimport { EditorExtensionsRegistry } from '../../../browser/editorExtensions.js';\nimport { observableCodeEditor } from '../../../browser/observableCodeEditor.js';\nimport { EmbeddedCodeEditorWidget } from '../../../browser/widget/codeEditor/embeddedCodeEditorWidget.js';\nimport { diffAddDecoration, diffAddDecorationEmpty, diffDeleteDecoration, diffDeleteDecorationEmpty, diffLineAddDecorationBackgroundWithIndicator, diffLineDeleteDecorationBackgroundWithIndicator, diffWholeLineAddDecoration, diffWholeLineDeleteDecoration } from '../../../browser/widget/diffEditor/registrations.contribution.js';\nimport { appendRemoveOnDispose, applyStyle } from '../../../browser/widget/diffEditor/utils.js';\nimport { PLAINTEXT_LANGUAGE_ID } from '../../../common/languages/modesRegistry.js';\nimport { TextModel } from '../../../common/model/textModel.js';\nimport { ContextMenuController } from '../../contextmenu/browser/contextmenu.js';\nimport { PlaceholderTextContribution } from '../../placeholderText/browser/placeholderTextContribution.js';\nimport { SuggestController } from '../../suggest/browser/suggestController.js';\nimport { IInstantiationService } from '../../../../platform/instantiation/common/instantiation.js';\nexport class InlineEdit {\n    constructor(range, newLines, changes) {\n        this.range = range;\n        this.newLines = newLines;\n        this.changes = changes;\n    }\n}\nlet InlineEditsWidget = class InlineEditsWidget extends Disposable {\n    constructor(_editor, _edit, _userPrompt, _instantiationService) {\n        super();\n        this._editor = _editor;\n        this._edit = _edit;\n        this._userPrompt = _userPrompt;\n        this._instantiationService = _instantiationService;\n        this._editorObs = observableCodeEditor(this._editor);\n        this._elements = h('div.inline-edits-widget', {\n            style: {\n                position: 'absolute',\n                overflow: 'visible',\n                top: '0px',\n                left: '0px',\n            },\n        }, [\n            h('div@editorContainer', { style: { position: 'absolute', top: '0px', left: '0px', width: '500px', height: '500px', } }, [\n                h('div.toolbar@toolbar', { style: { position: 'absolute', top: '-25px', left: '0px' } }),\n                h('div.promptEditor@promptEditor', { style: { position: 'absolute', top: '-25px', left: '80px', width: '300px', height: '22px' } }),\n                h('div.preview@editor', { style: { position: 'absolute', top: '0px', left: '0px' } }),\n            ]),\n            svgElem('svg', { style: { overflow: 'visible', pointerEvents: 'none' }, }, [\n                svgElem('defs', [\n                    svgElem('linearGradient', {\n                        id: 'Gradient2',\n                        x1: '0',\n                        y1: '0',\n                        x2: '1',\n                        y2: '0',\n                    }, [\n                        /*svgElem('stop', { offset: '0%', class: 'gradient-start', }),\n                        svgElem('stop', { offset: '0%', class: 'gradient-start', }),\n                        svgElem('stop', { offset: '20%', class: 'gradient-stop', }),*/\n                        svgElem('stop', { offset: '0%', class: 'gradient-stop', }),\n                        svgElem('stop', { offset: '100%', class: 'gradient-stop', }),\n                    ]),\n                ]),\n                svgElem('path@path', {\n                    d: '',\n                    fill: 'url(#Gradient2)',\n                }),\n            ]),\n        ]);\n        this._previewTextModel = this._register(this._instantiationService.createInstance(TextModel, '', PLAINTEXT_LANGUAGE_ID, TextModel.DEFAULT_CREATION_OPTIONS, null));\n        this._setText = derived(reader => {\n            const edit = this._edit.read(reader);\n            if (!edit) {\n                return;\n            }\n            this._previewTextModel.setValue(edit.newLines.join('\\n'));\n        }).recomputeInitiallyAndOnChange(this._store);\n        this._promptTextModel = this._register(this._instantiationService.createInstance(TextModel, '', PLAINTEXT_LANGUAGE_ID, TextModel.DEFAULT_CREATION_OPTIONS, null));\n        this._promptEditor = this._register(this._instantiationService.createInstance(EmbeddedCodeEditorWidget, this._elements.promptEditor, {\n            glyphMargin: false,\n            lineNumbers: 'off',\n            minimap: { enabled: false },\n            guides: {\n                indentation: false,\n                bracketPairs: false,\n                bracketPairsHorizontal: false,\n                highlightActiveIndentation: false,\n            },\n            folding: false,\n            selectOnLineNumbers: false,\n            selectionHighlight: false,\n            columnSelection: false,\n            overviewRulerBorder: false,\n            overviewRulerLanes: 0,\n            lineDecorationsWidth: 0,\n            lineNumbersMinChars: 0,\n            placeholder: 'Describe the change you want...',\n            fontFamily: DEFAULT_FONT_FAMILY,\n        }, {\n            contributions: EditorExtensionsRegistry.getSomeEditorContributions([\n                SuggestController.ID,\n                PlaceholderTextContribution.ID,\n                ContextMenuController.ID,\n            ]),\n            isSimpleWidget: true\n        }, this._editor));\n        this._previewEditor = this._register(this._instantiationService.createInstance(EmbeddedCodeEditorWidget, this._elements.editor, {\n            glyphMargin: false,\n            lineNumbers: 'off',\n            minimap: { enabled: false },\n            guides: {\n                indentation: false,\n                bracketPairs: false,\n                bracketPairsHorizontal: false,\n                highlightActiveIndentation: false,\n            },\n            folding: false,\n            selectOnLineNumbers: false,\n            selectionHighlight: false,\n            columnSelection: false,\n            overviewRulerBorder: false,\n            overviewRulerLanes: 0,\n            lineDecorationsWidth: 0,\n            lineNumbersMinChars: 0,\n        }, { contributions: [], }, this._editor));\n        this._previewEditorObs = observableCodeEditor(this._previewEditor);\n        this._decorations = derived(this, (reader) => {\n            this._setText.read(reader);\n            const diff = this._edit.read(reader)?.changes;\n            if (!diff) {\n                return [];\n            }\n            const originalDecorations = [];\n            const modifiedDecorations = [];\n            if (diff.length === 1 && diff[0].innerChanges[0].modifiedRange.equalsRange(this._previewTextModel.getFullModelRange())) {\n                return [];\n            }\n            for (const m of diff) {\n                if (!m.original.isEmpty) {\n                    originalDecorations.push({ range: m.original.toInclusiveRange(), options: diffLineDeleteDecorationBackgroundWithIndicator });\n                }\n                if (!m.modified.isEmpty) {\n                    modifiedDecorations.push({ range: m.modified.toInclusiveRange(), options: diffLineAddDecorationBackgroundWithIndicator });\n                }\n                if (m.modified.isEmpty || m.original.isEmpty) {\n                    if (!m.original.isEmpty) {\n                        originalDecorations.push({ range: m.original.toInclusiveRange(), options: diffWholeLineDeleteDecoration });\n                    }\n                    if (!m.modified.isEmpty) {\n                        modifiedDecorations.push({ range: m.modified.toInclusiveRange(), options: diffWholeLineAddDecoration });\n                    }\n                }\n                else {\n                    for (const i of m.innerChanges || []) {\n                        // Don't show empty markers outside the line range\n                        if (m.original.contains(i.originalRange.startLineNumber)) {\n                            originalDecorations.push({ range: i.originalRange, options: i.originalRange.isEmpty() ? diffDeleteDecorationEmpty : diffDeleteDecoration });\n                        }\n                        if (m.modified.contains(i.modifiedRange.startLineNumber)) {\n                            modifiedDecorations.push({ range: i.modifiedRange, options: i.modifiedRange.isEmpty() ? diffAddDecorationEmpty : diffAddDecoration });\n                        }\n                    }\n                }\n            }\n            return modifiedDecorations;\n        });\n        this._layout1 = derived(this, reader => {\n            const model = this._editor.getModel();\n            const inlineEdit = this._edit.read(reader);\n            if (!inlineEdit) {\n                return null;\n            }\n            const range = inlineEdit.range;\n            let maxLeft = 0;\n            for (let i = range.startLineNumber; i < range.endLineNumberExclusive; i++) {\n                const column = model.getLineMaxColumn(i);\n                const left = this._editor.getOffsetForColumn(i, column);\n                maxLeft = Math.max(maxLeft, left);\n            }\n            const layoutInfo = this._editor.getLayoutInfo();\n            const contentLeft = layoutInfo.contentLeft;\n            return { left: contentLeft + maxLeft };\n        });\n        this._layout = derived(this, (reader) => {\n            const inlineEdit = this._edit.read(reader);\n            if (!inlineEdit) {\n                return null;\n            }\n            const range = inlineEdit.range;\n            const scrollLeft = this._editorObs.scrollLeft.read(reader);\n            const left = this._layout1.read(reader).left + 20 - scrollLeft;\n            const selectionTop = this._editor.getTopForLineNumber(range.startLineNumber) - this._editorObs.scrollTop.read(reader);\n            const selectionBottom = this._editor.getTopForLineNumber(range.endLineNumberExclusive) - this._editorObs.scrollTop.read(reader);\n            const topCode = new Point(left, selectionTop);\n            const bottomCode = new Point(left, selectionBottom);\n            const codeHeight = selectionBottom - selectionTop;\n            const codeEditDist = 50;\n            const editHeight = this._editor.getOption(67 /* EditorOption.lineHeight */) * inlineEdit.newLines.length;\n            const difference = codeHeight - editHeight;\n            const topEdit = new Point(left + codeEditDist, selectionTop + (difference / 2));\n            const bottomEdit = new Point(left + codeEditDist, selectionBottom - (difference / 2));\n            return {\n                topCode,\n                bottomCode,\n                codeHeight,\n                topEdit,\n                bottomEdit,\n                editHeight,\n            };\n        });\n        const visible = derived(this, reader => this._edit.read(reader) !== undefined || this._userPrompt.read(reader) !== undefined);\n        this._register(applyStyle(this._elements.root, {\n            display: derived(this, reader => visible.read(reader) ? 'block' : 'none')\n        }));\n        this._register(appendRemoveOnDispose(this._editor.getDomNode(), this._elements.root));\n        this._register(observableCodeEditor(_editor).createOverlayWidget({\n            domNode: this._elements.root,\n            position: constObservable(null),\n            allowEditorOverflow: false,\n            minContentWidthInPx: derived(reader => {\n                const x = this._layout1.read(reader)?.left;\n                if (x === undefined) {\n                    return 0;\n                }\n                const width = this._previewEditorObs.contentWidth.read(reader);\n                return x + width;\n            }),\n        }));\n        this._previewEditor.setModel(this._previewTextModel);\n        this._register(this._previewEditorObs.setDecorations(this._decorations));\n        this._register(autorun(reader => {\n            const layoutInfo = this._layout.read(reader);\n            if (!layoutInfo) {\n                return;\n            }\n            const { topCode, bottomCode, topEdit, bottomEdit, editHeight } = layoutInfo;\n            const straightWidthCode = 10;\n            const straightWidthEdit = 0;\n            const bezierDist = 40;\n            const path = new PathBuilder()\n                .moveTo(topCode)\n                .lineTo(topCode.deltaX(straightWidthCode))\n                .curveTo(topCode.deltaX(straightWidthCode + bezierDist), topEdit.deltaX(-bezierDist - straightWidthEdit), topEdit.deltaX(-straightWidthEdit))\n                .lineTo(topEdit)\n                .lineTo(bottomEdit)\n                .lineTo(bottomEdit.deltaX(-straightWidthEdit))\n                .curveTo(bottomEdit.deltaX(-bezierDist - straightWidthEdit), bottomCode.deltaX(straightWidthCode + bezierDist), bottomCode.deltaX(straightWidthCode))\n                .lineTo(bottomCode)\n                .build();\n            this._elements.path.setAttribute('d', path);\n            this._elements.editorContainer.style.top = `${topEdit.y}px`;\n            this._elements.editorContainer.style.left = `${topEdit.x}px`;\n            this._elements.editorContainer.style.height = `${editHeight}px`;\n            const width = this._previewEditorObs.contentWidth.read(reader);\n            this._previewEditor.layout({ height: editHeight, width });\n        }));\n        this._promptEditor.setModel(this._promptTextModel);\n        this._promptEditor.layout();\n        this._register(createTwoWaySync(mapSettableObservable(this._userPrompt, v => v ?? '', v => v), observableCodeEditor(this._promptEditor).value));\n        this._register(autorun(reader => {\n            const isFocused = observableCodeEditor(this._promptEditor).isFocused.read(reader);\n            this._elements.root.classList.toggle('focused', isFocused);\n        }));\n    }\n};\nInlineEditsWidget = __decorate([\n    __param(3, IInstantiationService)\n], InlineEditsWidget);\nexport { InlineEditsWidget };\nfunction mapSettableObservable(obs, fn1, fn2) {\n    return derivedWithSetter(undefined, reader => fn1(obs.read(reader)), (value, tx) => obs.set(fn2(value), tx));\n}\nclass Point {\n    constructor(x, y) {\n        this.x = x;\n        this.y = y;\n    }\n    deltaX(delta) {\n        return new Point(this.x + delta, this.y);\n    }\n}\nclass PathBuilder {\n    constructor() {\n        this._data = '';\n    }\n    moveTo(point) {\n        this._data += `M ${point.x} ${point.y} `;\n        return this;\n    }\n    lineTo(point) {\n        this._data += `L ${point.x} ${point.y} `;\n        return this;\n    }\n    curveTo(cp1, cp2, to) {\n        this._data += `C ${cp1.x} ${cp1.y} ${cp2.x} ${cp2.y} ${to.x} ${to.y} `;\n        return this;\n    }\n    build() {\n        return this._data;\n    }\n}\nfunction createTwoWaySync(main, target) {\n    const store = new DisposableStore();\n    store.add(autorun(reader => {\n        const value = main.read(reader);\n        target.set(value, undefined);\n    }));\n    store.add(autorun(reader => {\n        const value = target.read(reader);\n        main.set(value, undefined);\n    }));\n    return store;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,IAAIA,UAAU,GAAI,IAAI,IAAI,IAAI,CAACA,UAAU,IAAK,UAAUC,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAE;EACnF,IAAIC,CAAC,GAAGC,SAAS,CAACC,MAAM;IAAEC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGH,MAAM,GAAGE,IAAI,KAAK,IAAI,GAAGA,IAAI,GAAGK,MAAM,CAACC,wBAAwB,CAACR,MAAM,EAAEC,GAAG,CAAC,GAAGC,IAAI;IAAEO,CAAC;EAC5H,IAAI,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,CAACC,QAAQ,KAAK,UAAU,EAAEL,CAAC,GAAGI,OAAO,CAACC,QAAQ,CAACZ,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,CAAC,CAAC,KAC1H,KAAK,IAAIU,CAAC,GAAGb,UAAU,CAACM,MAAM,GAAG,CAAC,EAAEO,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE,IAAIH,CAAC,GAAGV,UAAU,CAACa,CAAC,CAAC,EAAEN,CAAC,GAAG,CAACH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACH,CAAC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACT,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,GAAGG,CAAC,CAACT,MAAM,EAAEC,GAAG,CAAC,KAAKK,CAAC;EACjJ,OAAOH,CAAC,GAAG,CAAC,IAAIG,CAAC,IAAIC,MAAM,CAACM,cAAc,CAACb,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,EAAEA,CAAC;AACjE,CAAC;AACD,IAAIQ,OAAO,GAAI,IAAI,IAAI,IAAI,CAACA,OAAO,IAAK,UAAUC,UAAU,EAAEC,SAAS,EAAE;EACrE,OAAO,UAAUhB,MAAM,EAAEC,GAAG,EAAE;IAAEe,SAAS,CAAChB,MAAM,EAAEC,GAAG,EAAEc,UAAU,CAAC;EAAE,CAAC;AACzE,CAAC;AACD,SAASE,CAAC,EAAEC,OAAO,QAAQ,iCAAiC;AAC5D,SAASC,mBAAmB,QAAQ,mCAAmC;AACvE,SAASC,UAAU,EAAEC,eAAe,QAAQ,sCAAsC;AAClF,SAASC,OAAO,EAAEC,eAAe,EAAEC,OAAO,QAAQ,uCAAuC;AACzF,SAASC,iBAAiB,QAAQ,uDAAuD;AACzF,OAAO,yBAAyB;AAChC,SAASC,wBAAwB,QAAQ,sCAAsC;AAC/E,SAASC,oBAAoB,QAAQ,0CAA0C;AAC/E,SAASC,wBAAwB,QAAQ,gEAAgE;AACzG,SAASC,iBAAiB,EAAEC,sBAAsB,EAAEC,oBAAoB,EAAEC,yBAAyB,EAAEC,4CAA4C,EAAEC,+CAA+C,EAAEC,0BAA0B,EAAEC,6BAA6B,QAAQ,kEAAkE;AACvU,SAASC,qBAAqB,EAAEC,UAAU,QAAQ,6CAA6C;AAC/F,SAASC,qBAAqB,QAAQ,4CAA4C;AAClF,SAASC,SAAS,QAAQ,oCAAoC;AAC9D,SAASC,qBAAqB,QAAQ,0CAA0C;AAChF,SAASC,2BAA2B,QAAQ,8DAA8D;AAC1G,SAASC,iBAAiB,QAAQ,4CAA4C;AAC9E,SAASC,qBAAqB,QAAQ,4DAA4D;AAClG,OAAO,MAAMC,UAAU,CAAC;EACpBC,WAAWA,CAACC,KAAK,EAAEC,QAAQ,EAAEC,OAAO,EAAE;IAClC,IAAI,CAACF,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,OAAO,GAAGA,OAAO;EAC1B;AACJ;AACA,IAAIC,iBAAiB,GAAG,MAAMA,iBAAiB,SAAS9B,UAAU,CAAC;EAC/D0B,WAAWA,CAACK,OAAO,EAAEC,KAAK,EAAEC,WAAW,EAAEC,qBAAqB,EAAE;IAC5D,KAAK,CAAC,CAAC;IACP,IAAI,CAACH,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,qBAAqB,GAAGA,qBAAqB;IAClD,IAAI,CAACC,UAAU,GAAG5B,oBAAoB,CAAC,IAAI,CAACwB,OAAO,CAAC;IACpD,IAAI,CAACK,SAAS,GAAGvC,CAAC,CAAC,yBAAyB,EAAE;MAC1CwC,KAAK,EAAE;QACHC,QAAQ,EAAE,UAAU;QACpBC,QAAQ,EAAE,SAAS;QACnBC,GAAG,EAAE,KAAK;QACVC,IAAI,EAAE;MACV;IACJ,CAAC,EAAE,CACC5C,CAAC,CAAC,qBAAqB,EAAE;MAAEwC,KAAK,EAAE;QAAEC,QAAQ,EAAE,UAAU;QAAEE,GAAG,EAAE,KAAK;QAAEC,IAAI,EAAE,KAAK;QAAEC,KAAK,EAAE,OAAO;QAAEC,MAAM,EAAE;MAAS;IAAE,CAAC,EAAE,CACrH9C,CAAC,CAAC,qBAAqB,EAAE;MAAEwC,KAAK,EAAE;QAAEC,QAAQ,EAAE,UAAU;QAAEE,GAAG,EAAE,OAAO;QAAEC,IAAI,EAAE;MAAM;IAAE,CAAC,CAAC,EACxF5C,CAAC,CAAC,+BAA+B,EAAE;MAAEwC,KAAK,EAAE;QAAEC,QAAQ,EAAE,UAAU;QAAEE,GAAG,EAAE,OAAO;QAAEC,IAAI,EAAE,MAAM;QAAEC,KAAK,EAAE,OAAO;QAAEC,MAAM,EAAE;MAAO;IAAE,CAAC,CAAC,EACnI9C,CAAC,CAAC,oBAAoB,EAAE;MAAEwC,KAAK,EAAE;QAAEC,QAAQ,EAAE,UAAU;QAAEE,GAAG,EAAE,KAAK;QAAEC,IAAI,EAAE;MAAM;IAAE,CAAC,CAAC,CACxF,CAAC,EACF3C,OAAO,CAAC,KAAK,EAAE;MAAEuC,KAAK,EAAE;QAAEE,QAAQ,EAAE,SAAS;QAAEK,aAAa,EAAE;MAAO;IAAG,CAAC,EAAE,CACvE9C,OAAO,CAAC,MAAM,EAAE,CACZA,OAAO,CAAC,gBAAgB,EAAE;MACtB+C,EAAE,EAAE,WAAW;MACfC,EAAE,EAAE,GAAG;MACPC,EAAE,EAAE,GAAG;MACPC,EAAE,EAAE,GAAG;MACPC,EAAE,EAAE;IACR,CAAC,EAAE;IACC;AACxB;AACA;IACwBnD,OAAO,CAAC,MAAM,EAAE;MAAEoD,MAAM,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAiB,CAAC,CAAC,EAC1DrD,OAAO,CAAC,MAAM,EAAE;MAAEoD,MAAM,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAiB,CAAC,CAAC,CAC/D,CAAC,CACL,CAAC,EACFrD,OAAO,CAAC,WAAW,EAAE;MACjBT,CAAC,EAAE,EAAE;MACL+D,IAAI,EAAE;IACV,CAAC,CAAC,CACL,CAAC,CACL,CAAC;IACF,IAAI,CAACC,iBAAiB,GAAG,IAAI,CAACC,SAAS,CAAC,IAAI,CAACpB,qBAAqB,CAACqB,cAAc,CAACnC,SAAS,EAAE,EAAE,EAAED,qBAAqB,EAAEC,SAAS,CAACoC,wBAAwB,EAAE,IAAI,CAAC,CAAC;IAClK,IAAI,CAACC,QAAQ,GAAGrD,OAAO,CAACsD,MAAM,IAAI;MAC9B,MAAMC,IAAI,GAAG,IAAI,CAAC3B,KAAK,CAAC4B,IAAI,CAACF,MAAM,CAAC;MACpC,IAAI,CAACC,IAAI,EAAE;QACP;MACJ;MACA,IAAI,CAACN,iBAAiB,CAACQ,QAAQ,CAACF,IAAI,CAAC/B,QAAQ,CAACkC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC7D,CAAC,CAAC,CAACC,6BAA6B,CAAC,IAAI,CAACC,MAAM,CAAC;IAC7C,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACX,SAAS,CAAC,IAAI,CAACpB,qBAAqB,CAACqB,cAAc,CAACnC,SAAS,EAAE,EAAE,EAAED,qBAAqB,EAAEC,SAAS,CAACoC,wBAAwB,EAAE,IAAI,CAAC,CAAC;IACjK,IAAI,CAACU,aAAa,GAAG,IAAI,CAACZ,SAAS,CAAC,IAAI,CAACpB,qBAAqB,CAACqB,cAAc,CAAC/C,wBAAwB,EAAE,IAAI,CAAC4B,SAAS,CAAC+B,YAAY,EAAE;MACjIC,WAAW,EAAE,KAAK;MAClBC,WAAW,EAAE,KAAK;MAClBC,OAAO,EAAE;QAAEC,OAAO,EAAE;MAAM,CAAC;MAC3BC,MAAM,EAAE;QACJC,WAAW,EAAE,KAAK;QAClBC,YAAY,EAAE,KAAK;QACnBC,sBAAsB,EAAE,KAAK;QAC7BC,0BAA0B,EAAE;MAChC,CAAC;MACDC,OAAO,EAAE,KAAK;MACdC,mBAAmB,EAAE,KAAK;MAC1BC,kBAAkB,EAAE,KAAK;MACzBC,eAAe,EAAE,KAAK;MACtBC,mBAAmB,EAAE,KAAK;MAC1BC,kBAAkB,EAAE,CAAC;MACrBC,oBAAoB,EAAE,CAAC;MACvBC,mBAAmB,EAAE,CAAC;MACtBC,WAAW,EAAE,iCAAiC;MAC9CC,UAAU,EAAEvF;IAChB,CAAC,EAAE;MACCwF,aAAa,EAAEjF,wBAAwB,CAACkF,0BAA0B,CAAC,CAC/DjE,iBAAiB,CAACkE,EAAE,EACpBnE,2BAA2B,CAACmE,EAAE,EAC9BpE,qBAAqB,CAACoE,EAAE,CAC3B,CAAC;MACFC,cAAc,EAAE;IACpB,CAAC,EAAE,IAAI,CAAC3D,OAAO,CAAC,CAAC;IACjB,IAAI,CAAC4D,cAAc,GAAG,IAAI,CAACrC,SAAS,CAAC,IAAI,CAACpB,qBAAqB,CAACqB,cAAc,CAAC/C,wBAAwB,EAAE,IAAI,CAAC4B,SAAS,CAACwD,MAAM,EAAE;MAC5HxB,WAAW,EAAE,KAAK;MAClBC,WAAW,EAAE,KAAK;MAClBC,OAAO,EAAE;QAAEC,OAAO,EAAE;MAAM,CAAC;MAC3BC,MAAM,EAAE;QACJC,WAAW,EAAE,KAAK;QAClBC,YAAY,EAAE,KAAK;QACnBC,sBAAsB,EAAE,KAAK;QAC7BC,0BAA0B,EAAE;MAChC,CAAC;MACDC,OAAO,EAAE,KAAK;MACdC,mBAAmB,EAAE,KAAK;MAC1BC,kBAAkB,EAAE,KAAK;MACzBC,eAAe,EAAE,KAAK;MACtBC,mBAAmB,EAAE,KAAK;MAC1BC,kBAAkB,EAAE,CAAC;MACrBC,oBAAoB,EAAE,CAAC;MACvBC,mBAAmB,EAAE;IACzB,CAAC,EAAE;MAAEG,aAAa,EAAE;IAAI,CAAC,EAAE,IAAI,CAACxD,OAAO,CAAC,CAAC;IACzC,IAAI,CAAC8D,iBAAiB,GAAGtF,oBAAoB,CAAC,IAAI,CAACoF,cAAc,CAAC;IAClE,IAAI,CAACG,YAAY,GAAG1F,OAAO,CAAC,IAAI,EAAGsD,MAAM,IAAK;MAC1C,IAAI,CAACD,QAAQ,CAACG,IAAI,CAACF,MAAM,CAAC;MAC1B,MAAMqC,IAAI,GAAG,IAAI,CAAC/D,KAAK,CAAC4B,IAAI,CAACF,MAAM,CAAC,EAAE7B,OAAO;MAC7C,IAAI,CAACkE,IAAI,EAAE;QACP,OAAO,EAAE;MACb;MACA,MAAMC,mBAAmB,GAAG,EAAE;MAC9B,MAAMC,mBAAmB,GAAG,EAAE;MAC9B,IAAIF,IAAI,CAAC9G,MAAM,KAAK,CAAC,IAAI8G,IAAI,CAAC,CAAC,CAAC,CAACG,YAAY,CAAC,CAAC,CAAC,CAACC,aAAa,CAACC,WAAW,CAAC,IAAI,CAAC/C,iBAAiB,CAACgD,iBAAiB,CAAC,CAAC,CAAC,EAAE;QACpH,OAAO,EAAE;MACb;MACA,KAAK,MAAMC,CAAC,IAAIP,IAAI,EAAE;QAClB,IAAI,CAACO,CAAC,CAACC,QAAQ,CAACC,OAAO,EAAE;UACrBR,mBAAmB,CAACS,IAAI,CAAC;YAAE9E,KAAK,EAAE2E,CAAC,CAACC,QAAQ,CAACG,gBAAgB,CAAC,CAAC;YAAEC,OAAO,EAAE7F;UAAgD,CAAC,CAAC;QAChI;QACA,IAAI,CAACwF,CAAC,CAACM,QAAQ,CAACJ,OAAO,EAAE;UACrBP,mBAAmB,CAACQ,IAAI,CAAC;YAAE9E,KAAK,EAAE2E,CAAC,CAACM,QAAQ,CAACF,gBAAgB,CAAC,CAAC;YAAEC,OAAO,EAAE9F;UAA6C,CAAC,CAAC;QAC7H;QACA,IAAIyF,CAAC,CAACM,QAAQ,CAACJ,OAAO,IAAIF,CAAC,CAACC,QAAQ,CAACC,OAAO,EAAE;UAC1C,IAAI,CAACF,CAAC,CAACC,QAAQ,CAACC,OAAO,EAAE;YACrBR,mBAAmB,CAACS,IAAI,CAAC;cAAE9E,KAAK,EAAE2E,CAAC,CAACC,QAAQ,CAACG,gBAAgB,CAAC,CAAC;cAAEC,OAAO,EAAE3F;YAA8B,CAAC,CAAC;UAC9G;UACA,IAAI,CAACsF,CAAC,CAACM,QAAQ,CAACJ,OAAO,EAAE;YACrBP,mBAAmB,CAACQ,IAAI,CAAC;cAAE9E,KAAK,EAAE2E,CAAC,CAACM,QAAQ,CAACF,gBAAgB,CAAC,CAAC;cAAEC,OAAO,EAAE5F;YAA2B,CAAC,CAAC;UAC3G;QACJ,CAAC,MACI;UACD,KAAK,MAAMvB,CAAC,IAAI8G,CAAC,CAACJ,YAAY,IAAI,EAAE,EAAE;YAClC;YACA,IAAII,CAAC,CAACC,QAAQ,CAACM,QAAQ,CAACrH,CAAC,CAACsH,aAAa,CAACC,eAAe,CAAC,EAAE;cACtDf,mBAAmB,CAACS,IAAI,CAAC;gBAAE9E,KAAK,EAAEnC,CAAC,CAACsH,aAAa;gBAAEH,OAAO,EAAEnH,CAAC,CAACsH,aAAa,CAACN,OAAO,CAAC,CAAC,GAAG5F,yBAAyB,GAAGD;cAAqB,CAAC,CAAC;YAC/I;YACA,IAAI2F,CAAC,CAACM,QAAQ,CAACC,QAAQ,CAACrH,CAAC,CAAC2G,aAAa,CAACY,eAAe,CAAC,EAAE;cACtDd,mBAAmB,CAACQ,IAAI,CAAC;gBAAE9E,KAAK,EAAEnC,CAAC,CAAC2G,aAAa;gBAAEQ,OAAO,EAAEnH,CAAC,CAAC2G,aAAa,CAACK,OAAO,CAAC,CAAC,GAAG9F,sBAAsB,GAAGD;cAAkB,CAAC,CAAC;YACzI;UACJ;QACJ;MACJ;MACA,OAAOwF,mBAAmB;IAC9B,CAAC,CAAC;IACF,IAAI,CAACe,QAAQ,GAAG5G,OAAO,CAAC,IAAI,EAAEsD,MAAM,IAAI;MACpC,MAAMuD,KAAK,GAAG,IAAI,CAAClF,OAAO,CAACmF,QAAQ,CAAC,CAAC;MACrC,MAAMC,UAAU,GAAG,IAAI,CAACnF,KAAK,CAAC4B,IAAI,CAACF,MAAM,CAAC;MAC1C,IAAI,CAACyD,UAAU,EAAE;QACb,OAAO,IAAI;MACf;MACA,MAAMxF,KAAK,GAAGwF,UAAU,CAACxF,KAAK;MAC9B,IAAIyF,OAAO,GAAG,CAAC;MACf,KAAK,IAAI5H,CAAC,GAAGmC,KAAK,CAACoF,eAAe,EAAEvH,CAAC,GAAGmC,KAAK,CAAC0F,sBAAsB,EAAE7H,CAAC,EAAE,EAAE;QACvE,MAAM8H,MAAM,GAAGL,KAAK,CAACM,gBAAgB,CAAC/H,CAAC,CAAC;QACxC,MAAMiD,IAAI,GAAG,IAAI,CAACV,OAAO,CAACyF,kBAAkB,CAAChI,CAAC,EAAE8H,MAAM,CAAC;QACvDF,OAAO,GAAGK,IAAI,CAACC,GAAG,CAACN,OAAO,EAAE3E,IAAI,CAAC;MACrC;MACA,MAAMkF,UAAU,GAAG,IAAI,CAAC5F,OAAO,CAAC6F,aAAa,CAAC,CAAC;MAC/C,MAAMC,WAAW,GAAGF,UAAU,CAACE,WAAW;MAC1C,OAAO;QAAEpF,IAAI,EAAEoF,WAAW,GAAGT;MAAQ,CAAC;IAC1C,CAAC,CAAC;IACF,IAAI,CAACU,OAAO,GAAG1H,OAAO,CAAC,IAAI,EAAGsD,MAAM,IAAK;MACrC,MAAMyD,UAAU,GAAG,IAAI,CAACnF,KAAK,CAAC4B,IAAI,CAACF,MAAM,CAAC;MAC1C,IAAI,CAACyD,UAAU,EAAE;QACb,OAAO,IAAI;MACf;MACA,MAAMxF,KAAK,GAAGwF,UAAU,CAACxF,KAAK;MAC9B,MAAMoG,UAAU,GAAG,IAAI,CAAC5F,UAAU,CAAC4F,UAAU,CAACnE,IAAI,CAACF,MAAM,CAAC;MAC1D,MAAMjB,IAAI,GAAG,IAAI,CAACuE,QAAQ,CAACpD,IAAI,CAACF,MAAM,CAAC,CAACjB,IAAI,GAAG,EAAE,GAAGsF,UAAU;MAC9D,MAAMC,YAAY,GAAG,IAAI,CAACjG,OAAO,CAACkG,mBAAmB,CAACtG,KAAK,CAACoF,eAAe,CAAC,GAAG,IAAI,CAAC5E,UAAU,CAAC+F,SAAS,CAACtE,IAAI,CAACF,MAAM,CAAC;MACrH,MAAMyE,eAAe,GAAG,IAAI,CAACpG,OAAO,CAACkG,mBAAmB,CAACtG,KAAK,CAAC0F,sBAAsB,CAAC,GAAG,IAAI,CAAClF,UAAU,CAAC+F,SAAS,CAACtE,IAAI,CAACF,MAAM,CAAC;MAC/H,MAAM0E,OAAO,GAAG,IAAIC,KAAK,CAAC5F,IAAI,EAAEuF,YAAY,CAAC;MAC7C,MAAMM,UAAU,GAAG,IAAID,KAAK,CAAC5F,IAAI,EAAE0F,eAAe,CAAC;MACnD,MAAMI,UAAU,GAAGJ,eAAe,GAAGH,YAAY;MACjD,MAAMQ,YAAY,GAAG,EAAE;MACvB,MAAMC,UAAU,GAAG,IAAI,CAAC1G,OAAO,CAAC2G,SAAS,CAAC,EAAE,CAAC,6BAA6B,CAAC,GAAGvB,UAAU,CAACvF,QAAQ,CAAC3C,MAAM;MACxG,MAAM0J,UAAU,GAAGJ,UAAU,GAAGE,UAAU;MAC1C,MAAMG,OAAO,GAAG,IAAIP,KAAK,CAAC5F,IAAI,GAAG+F,YAAY,EAAER,YAAY,GAAIW,UAAU,GAAG,CAAE,CAAC;MAC/E,MAAME,UAAU,GAAG,IAAIR,KAAK,CAAC5F,IAAI,GAAG+F,YAAY,EAAEL,eAAe,GAAIQ,UAAU,GAAG,CAAE,CAAC;MACrF,OAAO;QACHP,OAAO;QACPE,UAAU;QACVC,UAAU;QACVK,OAAO;QACPC,UAAU;QACVJ;MACJ,CAAC;IACL,CAAC,CAAC;IACF,MAAMK,OAAO,GAAG1I,OAAO,CAAC,IAAI,EAAEsD,MAAM,IAAI,IAAI,CAAC1B,KAAK,CAAC4B,IAAI,CAACF,MAAM,CAAC,KAAKqF,SAAS,IAAI,IAAI,CAAC9G,WAAW,CAAC2B,IAAI,CAACF,MAAM,CAAC,KAAKqF,SAAS,CAAC;IAC7H,IAAI,CAACzF,SAAS,CAACpC,UAAU,CAAC,IAAI,CAACkB,SAAS,CAAC4G,IAAI,EAAE;MAC3CC,OAAO,EAAE7I,OAAO,CAAC,IAAI,EAAEsD,MAAM,IAAIoF,OAAO,CAAClF,IAAI,CAACF,MAAM,CAAC,GAAG,OAAO,GAAG,MAAM;IAC5E,CAAC,CAAC,CAAC;IACH,IAAI,CAACJ,SAAS,CAACrC,qBAAqB,CAAC,IAAI,CAACc,OAAO,CAACmH,UAAU,CAAC,CAAC,EAAE,IAAI,CAAC9G,SAAS,CAAC4G,IAAI,CAAC,CAAC;IACrF,IAAI,CAAC1F,SAAS,CAAC/C,oBAAoB,CAACwB,OAAO,CAAC,CAACoH,mBAAmB,CAAC;MAC7DC,OAAO,EAAE,IAAI,CAAChH,SAAS,CAAC4G,IAAI;MAC5B1G,QAAQ,EAAEnC,eAAe,CAAC,IAAI,CAAC;MAC/BkJ,mBAAmB,EAAE,KAAK;MAC1BC,mBAAmB,EAAElJ,OAAO,CAACsD,MAAM,IAAI;QACnC,MAAM6F,CAAC,GAAG,IAAI,CAACvC,QAAQ,CAACpD,IAAI,CAACF,MAAM,CAAC,EAAEjB,IAAI;QAC1C,IAAI8G,CAAC,KAAKR,SAAS,EAAE;UACjB,OAAO,CAAC;QACZ;QACA,MAAMrG,KAAK,GAAG,IAAI,CAACmD,iBAAiB,CAAC2D,YAAY,CAAC5F,IAAI,CAACF,MAAM,CAAC;QAC9D,OAAO6F,CAAC,GAAG7G,KAAK;MACpB,CAAC;IACL,CAAC,CAAC,CAAC;IACH,IAAI,CAACiD,cAAc,CAAC8D,QAAQ,CAAC,IAAI,CAACpG,iBAAiB,CAAC;IACpD,IAAI,CAACC,SAAS,CAAC,IAAI,CAACuC,iBAAiB,CAAC6D,cAAc,CAAC,IAAI,CAAC5D,YAAY,CAAC,CAAC;IACxE,IAAI,CAACxC,SAAS,CAACpD,OAAO,CAACwD,MAAM,IAAI;MAC7B,MAAMiE,UAAU,GAAG,IAAI,CAACG,OAAO,CAAClE,IAAI,CAACF,MAAM,CAAC;MAC5C,IAAI,CAACiE,UAAU,EAAE;QACb;MACJ;MACA,MAAM;QAAES,OAAO;QAAEE,UAAU;QAAEM,OAAO;QAAEC,UAAU;QAAEJ;MAAW,CAAC,GAAGd,UAAU;MAC3E,MAAMgC,iBAAiB,GAAG,EAAE;MAC5B,MAAMC,iBAAiB,GAAG,CAAC;MAC3B,MAAMC,UAAU,GAAG,EAAE;MACrB,MAAMC,IAAI,GAAG,IAAIC,WAAW,CAAC,CAAC,CACzBC,MAAM,CAAC5B,OAAO,CAAC,CACf6B,MAAM,CAAC7B,OAAO,CAAC8B,MAAM,CAACP,iBAAiB,CAAC,CAAC,CACzCQ,OAAO,CAAC/B,OAAO,CAAC8B,MAAM,CAACP,iBAAiB,GAAGE,UAAU,CAAC,EAAEjB,OAAO,CAACsB,MAAM,CAAC,CAACL,UAAU,GAAGD,iBAAiB,CAAC,EAAEhB,OAAO,CAACsB,MAAM,CAAC,CAACN,iBAAiB,CAAC,CAAC,CAC5IK,MAAM,CAACrB,OAAO,CAAC,CACfqB,MAAM,CAACpB,UAAU,CAAC,CAClBoB,MAAM,CAACpB,UAAU,CAACqB,MAAM,CAAC,CAACN,iBAAiB,CAAC,CAAC,CAC7CO,OAAO,CAACtB,UAAU,CAACqB,MAAM,CAAC,CAACL,UAAU,GAAGD,iBAAiB,CAAC,EAAEtB,UAAU,CAAC4B,MAAM,CAACP,iBAAiB,GAAGE,UAAU,CAAC,EAAEvB,UAAU,CAAC4B,MAAM,CAACP,iBAAiB,CAAC,CAAC,CACpJM,MAAM,CAAC3B,UAAU,CAAC,CAClB8B,KAAK,CAAC,CAAC;MACZ,IAAI,CAAChI,SAAS,CAAC0H,IAAI,CAACO,YAAY,CAAC,GAAG,EAAEP,IAAI,CAAC;MAC3C,IAAI,CAAC1H,SAAS,CAACkI,eAAe,CAACjI,KAAK,CAACG,GAAG,GAAG,GAAGoG,OAAO,CAAC2B,CAAC,IAAI;MAC3D,IAAI,CAACnI,SAAS,CAACkI,eAAe,CAACjI,KAAK,CAACI,IAAI,GAAG,GAAGmG,OAAO,CAACW,CAAC,IAAI;MAC5D,IAAI,CAACnH,SAAS,CAACkI,eAAe,CAACjI,KAAK,CAACM,MAAM,GAAG,GAAG8F,UAAU,IAAI;MAC/D,MAAM/F,KAAK,GAAG,IAAI,CAACmD,iBAAiB,CAAC2D,YAAY,CAAC5F,IAAI,CAACF,MAAM,CAAC;MAC9D,IAAI,CAACiC,cAAc,CAAC6E,MAAM,CAAC;QAAE7H,MAAM,EAAE8F,UAAU;QAAE/F;MAAM,CAAC,CAAC;IAC7D,CAAC,CAAC,CAAC;IACH,IAAI,CAACwB,aAAa,CAACuF,QAAQ,CAAC,IAAI,CAACxF,gBAAgB,CAAC;IAClD,IAAI,CAACC,aAAa,CAACsG,MAAM,CAAC,CAAC;IAC3B,IAAI,CAAClH,SAAS,CAACmH,gBAAgB,CAACC,qBAAqB,CAAC,IAAI,CAACzI,WAAW,EAAE0I,CAAC,IAAIA,CAAC,IAAI,EAAE,EAAEA,CAAC,IAAIA,CAAC,CAAC,EAAEpK,oBAAoB,CAAC,IAAI,CAAC2D,aAAa,CAAC,CAAC0G,KAAK,CAAC,CAAC;IAC/I,IAAI,CAACtH,SAAS,CAACpD,OAAO,CAACwD,MAAM,IAAI;MAC7B,MAAMmH,SAAS,GAAGtK,oBAAoB,CAAC,IAAI,CAAC2D,aAAa,CAAC,CAAC2G,SAAS,CAACjH,IAAI,CAACF,MAAM,CAAC;MACjF,IAAI,CAACtB,SAAS,CAAC4G,IAAI,CAAC8B,SAAS,CAACC,MAAM,CAAC,SAAS,EAAEF,SAAS,CAAC;IAC9D,CAAC,CAAC,CAAC;EACP;AACJ,CAAC;AACD/I,iBAAiB,GAAGpD,UAAU,CAAC,CAC3BgB,OAAO,CAAC,CAAC,EAAE8B,qBAAqB,CAAC,CACpC,EAAEM,iBAAiB,CAAC;AACrB,SAASA,iBAAiB;AAC1B,SAAS4I,qBAAqBA,CAACM,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAE;EAC1C,OAAO7K,iBAAiB,CAAC0I,SAAS,EAAErF,MAAM,IAAIuH,GAAG,CAACD,GAAG,CAACpH,IAAI,CAACF,MAAM,CAAC,CAAC,EAAE,CAACkH,KAAK,EAAEO,EAAE,KAAKH,GAAG,CAACI,GAAG,CAACF,GAAG,CAACN,KAAK,CAAC,EAAEO,EAAE,CAAC,CAAC;AAChH;AACA,MAAM9C,KAAK,CAAC;EACR3G,WAAWA,CAAC6H,CAAC,EAAEgB,CAAC,EAAE;IACd,IAAI,CAAChB,CAAC,GAAGA,CAAC;IACV,IAAI,CAACgB,CAAC,GAAGA,CAAC;EACd;EACAL,MAAMA,CAACmB,KAAK,EAAE;IACV,OAAO,IAAIhD,KAAK,CAAC,IAAI,CAACkB,CAAC,GAAG8B,KAAK,EAAE,IAAI,CAACd,CAAC,CAAC;EAC5C;AACJ;AACA,MAAMR,WAAW,CAAC;EACdrI,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC4J,KAAK,GAAG,EAAE;EACnB;EACAtB,MAAMA,CAACuB,KAAK,EAAE;IACV,IAAI,CAACD,KAAK,IAAI,KAAKC,KAAK,CAAChC,CAAC,IAAIgC,KAAK,CAAChB,CAAC,GAAG;IACxC,OAAO,IAAI;EACf;EACAN,MAAMA,CAACsB,KAAK,EAAE;IACV,IAAI,CAACD,KAAK,IAAI,KAAKC,KAAK,CAAChC,CAAC,IAAIgC,KAAK,CAAChB,CAAC,GAAG;IACxC,OAAO,IAAI;EACf;EACAJ,OAAOA,CAACqB,GAAG,EAAEC,GAAG,EAAEC,EAAE,EAAE;IAClB,IAAI,CAACJ,KAAK,IAAI,KAAKE,GAAG,CAACjC,CAAC,IAAIiC,GAAG,CAACjB,CAAC,IAAIkB,GAAG,CAAClC,CAAC,IAAIkC,GAAG,CAAClB,CAAC,IAAImB,EAAE,CAACnC,CAAC,IAAImC,EAAE,CAACnB,CAAC,GAAG;IACtE,OAAO,IAAI;EACf;EACAH,KAAKA,CAAA,EAAG;IACJ,OAAO,IAAI,CAACkB,KAAK;EACrB;AACJ;AACA,SAASb,gBAAgBA,CAACkB,IAAI,EAAE/M,MAAM,EAAE;EACpC,MAAMgN,KAAK,GAAG,IAAI3L,eAAe,CAAC,CAAC;EACnC2L,KAAK,CAACC,GAAG,CAAC3L,OAAO,CAACwD,MAAM,IAAI;IACxB,MAAMkH,KAAK,GAAGe,IAAI,CAAC/H,IAAI,CAACF,MAAM,CAAC;IAC/B9E,MAAM,CAACwM,GAAG,CAACR,KAAK,EAAE7B,SAAS,CAAC;EAChC,CAAC,CAAC,CAAC;EACH6C,KAAK,CAACC,GAAG,CAAC3L,OAAO,CAACwD,MAAM,IAAI;IACxB,MAAMkH,KAAK,GAAGhM,MAAM,CAACgF,IAAI,CAACF,MAAM,CAAC;IACjCiI,IAAI,CAACP,GAAG,CAACR,KAAK,EAAE7B,SAAS,CAAC;EAC9B,CAAC,CAAC,CAAC;EACH,OAAO6C,KAAK;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}