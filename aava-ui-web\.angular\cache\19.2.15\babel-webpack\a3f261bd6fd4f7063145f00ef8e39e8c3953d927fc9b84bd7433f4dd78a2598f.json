{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Emitter } from '../../../../../base/common/event.js';\nimport { Disposable } from '../../../../../base/common/lifecycle.js';\nimport { BracketInfo, BracketPairWithMinIndentationInfo } from '../../../textModelBracketPairs.js';\nimport { TextEditInfo } from './beforeEditPositionMapper.js';\nimport { LanguageAgnosticBracketTokens } from './brackets.js';\nimport { lengthAdd, lengthGreaterThanEqual, lengthLessThan, lengthLessThanEqual, lengthsToRange, lengthZero, positionToLength, toLength } from './length.js';\nimport { parseDocument } from './parser.js';\nimport { DenseKeyProvider } from './smallImmutableSet.js';\nimport { FastTokenizer, TextBufferTokenizer } from './tokenizer.js';\nimport { CallbackIterable } from '../../../../../base/common/arrays.js';\nimport { combineTextEditInfos } from './combineTextEditInfos.js';\nexport class BracketPairsTree extends Disposable {\n  didLanguageChange(languageId) {\n    return this.brackets.didLanguageChange(languageId);\n  }\n  constructor(textModel, getLanguageConfiguration) {\n    super();\n    this.textModel = textModel;\n    this.getLanguageConfiguration = getLanguageConfiguration;\n    this.didChangeEmitter = new Emitter();\n    this.denseKeyProvider = new DenseKeyProvider();\n    this.brackets = new LanguageAgnosticBracketTokens(this.denseKeyProvider, this.getLanguageConfiguration);\n    this.onDidChange = this.didChangeEmitter.event;\n    this.queuedTextEditsForInitialAstWithoutTokens = [];\n    this.queuedTextEdits = [];\n    if (!textModel.tokenization.hasTokens) {\n      const brackets = this.brackets.getSingleLanguageBracketTokens(this.textModel.getLanguageId());\n      const tokenizer = new FastTokenizer(this.textModel.getValue(), brackets);\n      this.initialAstWithoutTokens = parseDocument(tokenizer, [], undefined, true);\n      this.astWithTokens = this.initialAstWithoutTokens;\n    } else if (textModel.tokenization.backgroundTokenizationState === 2 /* BackgroundTokenizationState.Completed */) {\n      // Skip the initial ast, as there is no flickering.\n      // Directly create the tree with token information.\n      this.initialAstWithoutTokens = undefined;\n      this.astWithTokens = this.parseDocumentFromTextBuffer([], undefined, false);\n    } else {\n      // We missed some token changes already, so we cannot use the fast tokenizer + delta increments\n      this.initialAstWithoutTokens = this.parseDocumentFromTextBuffer([], undefined, true);\n      this.astWithTokens = this.initialAstWithoutTokens;\n    }\n  }\n  //#region TextModel events\n  handleDidChangeBackgroundTokenizationState() {\n    if (this.textModel.tokenization.backgroundTokenizationState === 2 /* BackgroundTokenizationState.Completed */) {\n      const wasUndefined = this.initialAstWithoutTokens === undefined;\n      // Clear the initial tree as we can use the tree with token information now.\n      this.initialAstWithoutTokens = undefined;\n      if (!wasUndefined) {\n        this.didChangeEmitter.fire();\n      }\n    }\n  }\n  handleDidChangeTokens({\n    ranges\n  }) {\n    const edits = ranges.map(r => new TextEditInfo(toLength(r.fromLineNumber - 1, 0), toLength(r.toLineNumber, 0), toLength(r.toLineNumber - r.fromLineNumber + 1, 0)));\n    this.handleEdits(edits, true);\n    if (!this.initialAstWithoutTokens) {\n      this.didChangeEmitter.fire();\n    }\n  }\n  handleContentChanged(change) {\n    const edits = TextEditInfo.fromModelContentChanges(change.changes);\n    this.handleEdits(edits, false);\n  }\n  handleEdits(edits, tokenChange) {\n    // Lazily queue the edits and only apply them when the tree is accessed.\n    const result = combineTextEditInfos(this.queuedTextEdits, edits);\n    this.queuedTextEdits = result;\n    if (this.initialAstWithoutTokens && !tokenChange) {\n      this.queuedTextEditsForInitialAstWithoutTokens = combineTextEditInfos(this.queuedTextEditsForInitialAstWithoutTokens, edits);\n    }\n  }\n  //#endregion\n  flushQueue() {\n    if (this.queuedTextEdits.length > 0) {\n      this.astWithTokens = this.parseDocumentFromTextBuffer(this.queuedTextEdits, this.astWithTokens, false);\n      this.queuedTextEdits = [];\n    }\n    if (this.queuedTextEditsForInitialAstWithoutTokens.length > 0) {\n      if (this.initialAstWithoutTokens) {\n        this.initialAstWithoutTokens = this.parseDocumentFromTextBuffer(this.queuedTextEditsForInitialAstWithoutTokens, this.initialAstWithoutTokens, false);\n      }\n      this.queuedTextEditsForInitialAstWithoutTokens = [];\n    }\n  }\n  /**\n   * @pure (only if isPure = true)\n  */\n  parseDocumentFromTextBuffer(edits, previousAst, immutable) {\n    // Is much faster if `isPure = false`.\n    const isPure = false;\n    const previousAstClone = isPure ? previousAst?.deepClone() : previousAst;\n    const tokenizer = new TextBufferTokenizer(this.textModel, this.brackets);\n    const result = parseDocument(tokenizer, edits, previousAstClone, immutable);\n    return result;\n  }\n  getBracketsInRange(range, onlyColorizedBrackets) {\n    this.flushQueue();\n    const startOffset = toLength(range.startLineNumber - 1, range.startColumn - 1);\n    const endOffset = toLength(range.endLineNumber - 1, range.endColumn - 1);\n    return new CallbackIterable(cb => {\n      const node = this.initialAstWithoutTokens || this.astWithTokens;\n      collectBrackets(node, lengthZero, node.length, startOffset, endOffset, cb, 0, 0, new Map(), onlyColorizedBrackets);\n    });\n  }\n  getBracketPairsInRange(range, includeMinIndentation) {\n    this.flushQueue();\n    const startLength = positionToLength(range.getStartPosition());\n    const endLength = positionToLength(range.getEndPosition());\n    return new CallbackIterable(cb => {\n      const node = this.initialAstWithoutTokens || this.astWithTokens;\n      const context = new CollectBracketPairsContext(cb, includeMinIndentation, this.textModel);\n      collectBracketPairs(node, lengthZero, node.length, startLength, endLength, context, 0, new Map());\n    });\n  }\n  getFirstBracketAfter(position) {\n    this.flushQueue();\n    const node = this.initialAstWithoutTokens || this.astWithTokens;\n    return getFirstBracketAfter(node, lengthZero, node.length, positionToLength(position));\n  }\n  getFirstBracketBefore(position) {\n    this.flushQueue();\n    const node = this.initialAstWithoutTokens || this.astWithTokens;\n    return getFirstBracketBefore(node, lengthZero, node.length, positionToLength(position));\n  }\n}\nfunction getFirstBracketBefore(node, nodeOffsetStart, nodeOffsetEnd, position) {\n  if (node.kind === 4 /* AstNodeKind.List */ || node.kind === 2 /* AstNodeKind.Pair */) {\n    const lengths = [];\n    for (const child of node.children) {\n      nodeOffsetEnd = lengthAdd(nodeOffsetStart, child.length);\n      lengths.push({\n        nodeOffsetStart,\n        nodeOffsetEnd\n      });\n      nodeOffsetStart = nodeOffsetEnd;\n    }\n    for (let i = lengths.length - 1; i >= 0; i--) {\n      const {\n        nodeOffsetStart,\n        nodeOffsetEnd\n      } = lengths[i];\n      if (lengthLessThan(nodeOffsetStart, position)) {\n        const result = getFirstBracketBefore(node.children[i], nodeOffsetStart, nodeOffsetEnd, position);\n        if (result) {\n          return result;\n        }\n      }\n    }\n    return null;\n  } else if (node.kind === 3 /* AstNodeKind.UnexpectedClosingBracket */) {\n    return null;\n  } else if (node.kind === 1 /* AstNodeKind.Bracket */) {\n    const range = lengthsToRange(nodeOffsetStart, nodeOffsetEnd);\n    return {\n      bracketInfo: node.bracketInfo,\n      range\n    };\n  }\n  return null;\n}\nfunction getFirstBracketAfter(node, nodeOffsetStart, nodeOffsetEnd, position) {\n  if (node.kind === 4 /* AstNodeKind.List */ || node.kind === 2 /* AstNodeKind.Pair */) {\n    for (const child of node.children) {\n      nodeOffsetEnd = lengthAdd(nodeOffsetStart, child.length);\n      if (lengthLessThan(position, nodeOffsetEnd)) {\n        const result = getFirstBracketAfter(child, nodeOffsetStart, nodeOffsetEnd, position);\n        if (result) {\n          return result;\n        }\n      }\n      nodeOffsetStart = nodeOffsetEnd;\n    }\n    return null;\n  } else if (node.kind === 3 /* AstNodeKind.UnexpectedClosingBracket */) {\n    return null;\n  } else if (node.kind === 1 /* AstNodeKind.Bracket */) {\n    const range = lengthsToRange(nodeOffsetStart, nodeOffsetEnd);\n    return {\n      bracketInfo: node.bracketInfo,\n      range\n    };\n  }\n  return null;\n}\nfunction collectBrackets(node, nodeOffsetStart, nodeOffsetEnd, startOffset, endOffset, push, level, nestingLevelOfEqualBracketType, levelPerBracketType, onlyColorizedBrackets, parentPairIsIncomplete = false) {\n  if (level > 200) {\n    return true;\n  }\n  whileLoop: while (true) {\n    switch (node.kind) {\n      case 4 /* AstNodeKind.List */:\n        {\n          const childCount = node.childrenLength;\n          for (let i = 0; i < childCount; i++) {\n            const child = node.getChild(i);\n            if (!child) {\n              continue;\n            }\n            nodeOffsetEnd = lengthAdd(nodeOffsetStart, child.length);\n            if (lengthLessThanEqual(nodeOffsetStart, endOffset) && lengthGreaterThanEqual(nodeOffsetEnd, startOffset)) {\n              const childEndsAfterEnd = lengthGreaterThanEqual(nodeOffsetEnd, endOffset);\n              if (childEndsAfterEnd) {\n                // No child after this child in the requested window, don't recurse\n                node = child;\n                continue whileLoop;\n              }\n              const shouldContinue = collectBrackets(child, nodeOffsetStart, nodeOffsetEnd, startOffset, endOffset, push, level, 0, levelPerBracketType, onlyColorizedBrackets);\n              if (!shouldContinue) {\n                return false;\n              }\n            }\n            nodeOffsetStart = nodeOffsetEnd;\n          }\n          return true;\n        }\n      case 2 /* AstNodeKind.Pair */:\n        {\n          const colorize = !onlyColorizedBrackets || !node.closingBracket || node.closingBracket.bracketInfo.closesColorized(node.openingBracket.bracketInfo);\n          let levelPerBracket = 0;\n          if (levelPerBracketType) {\n            let existing = levelPerBracketType.get(node.openingBracket.text);\n            if (existing === undefined) {\n              existing = 0;\n            }\n            levelPerBracket = existing;\n            if (colorize) {\n              existing++;\n              levelPerBracketType.set(node.openingBracket.text, existing);\n            }\n          }\n          const childCount = node.childrenLength;\n          for (let i = 0; i < childCount; i++) {\n            const child = node.getChild(i);\n            if (!child) {\n              continue;\n            }\n            nodeOffsetEnd = lengthAdd(nodeOffsetStart, child.length);\n            if (lengthLessThanEqual(nodeOffsetStart, endOffset) && lengthGreaterThanEqual(nodeOffsetEnd, startOffset)) {\n              const childEndsAfterEnd = lengthGreaterThanEqual(nodeOffsetEnd, endOffset);\n              if (childEndsAfterEnd && child.kind !== 1 /* AstNodeKind.Bracket */) {\n                // No child after this child in the requested window, don't recurse\n                // Don't do this for brackets because of unclosed/unopened brackets\n                node = child;\n                if (colorize) {\n                  level++;\n                  nestingLevelOfEqualBracketType = levelPerBracket + 1;\n                } else {\n                  nestingLevelOfEqualBracketType = levelPerBracket;\n                }\n                continue whileLoop;\n              }\n              if (colorize || child.kind !== 1 /* AstNodeKind.Bracket */ || !node.closingBracket) {\n                const shouldContinue = collectBrackets(child, nodeOffsetStart, nodeOffsetEnd, startOffset, endOffset, push, colorize ? level + 1 : level, colorize ? levelPerBracket + 1 : levelPerBracket, levelPerBracketType, onlyColorizedBrackets, !node.closingBracket);\n                if (!shouldContinue) {\n                  return false;\n                }\n              }\n            }\n            nodeOffsetStart = nodeOffsetEnd;\n          }\n          levelPerBracketType?.set(node.openingBracket.text, levelPerBracket);\n          return true;\n        }\n      case 3 /* AstNodeKind.UnexpectedClosingBracket */:\n        {\n          const range = lengthsToRange(nodeOffsetStart, nodeOffsetEnd);\n          return push(new BracketInfo(range, level - 1, 0, true));\n        }\n      case 1 /* AstNodeKind.Bracket */:\n        {\n          const range = lengthsToRange(nodeOffsetStart, nodeOffsetEnd);\n          return push(new BracketInfo(range, level - 1, nestingLevelOfEqualBracketType - 1, parentPairIsIncomplete));\n        }\n      case 0 /* AstNodeKind.Text */:\n        return true;\n    }\n  }\n}\nclass CollectBracketPairsContext {\n  constructor(push, includeMinIndentation, textModel) {\n    this.push = push;\n    this.includeMinIndentation = includeMinIndentation;\n    this.textModel = textModel;\n  }\n}\nfunction collectBracketPairs(node, nodeOffsetStart, nodeOffsetEnd, startOffset, endOffset, context, level, levelPerBracketType) {\n  if (level > 200) {\n    return true;\n  }\n  let shouldContinue = true;\n  if (node.kind === 2 /* AstNodeKind.Pair */) {\n    let levelPerBracket = 0;\n    if (levelPerBracketType) {\n      let existing = levelPerBracketType.get(node.openingBracket.text);\n      if (existing === undefined) {\n        existing = 0;\n      }\n      levelPerBracket = existing;\n      existing++;\n      levelPerBracketType.set(node.openingBracket.text, existing);\n    }\n    const openingBracketEnd = lengthAdd(nodeOffsetStart, node.openingBracket.length);\n    let minIndentation = -1;\n    if (context.includeMinIndentation) {\n      minIndentation = node.computeMinIndentation(nodeOffsetStart, context.textModel);\n    }\n    shouldContinue = context.push(new BracketPairWithMinIndentationInfo(lengthsToRange(nodeOffsetStart, nodeOffsetEnd), lengthsToRange(nodeOffsetStart, openingBracketEnd), node.closingBracket ? lengthsToRange(lengthAdd(openingBracketEnd, node.child?.length || lengthZero), nodeOffsetEnd) : undefined, level, levelPerBracket, node, minIndentation));\n    nodeOffsetStart = openingBracketEnd;\n    if (shouldContinue && node.child) {\n      const child = node.child;\n      nodeOffsetEnd = lengthAdd(nodeOffsetStart, child.length);\n      if (lengthLessThanEqual(nodeOffsetStart, endOffset) && lengthGreaterThanEqual(nodeOffsetEnd, startOffset)) {\n        shouldContinue = collectBracketPairs(child, nodeOffsetStart, nodeOffsetEnd, startOffset, endOffset, context, level + 1, levelPerBracketType);\n        if (!shouldContinue) {\n          return false;\n        }\n      }\n    }\n    levelPerBracketType?.set(node.openingBracket.text, levelPerBracket);\n  } else {\n    let curOffset = nodeOffsetStart;\n    for (const child of node.children) {\n      const childOffset = curOffset;\n      curOffset = lengthAdd(curOffset, child.length);\n      if (lengthLessThanEqual(childOffset, endOffset) && lengthLessThanEqual(startOffset, curOffset)) {\n        shouldContinue = collectBracketPairs(child, childOffset, curOffset, startOffset, endOffset, context, level, levelPerBracketType);\n        if (!shouldContinue) {\n          return false;\n        }\n      }\n    }\n  }\n  return shouldContinue;\n}", "map": {"version": 3, "names": ["Emitter", "Disposable", "BracketInfo", "BracketPairWithMinIndentationInfo", "TextEditInfo", "LanguageAgnosticBracketTokens", "lengthAdd", "lengthGreaterThanEqual", "lengthLessThan", "lengthLessThanEqual", "lengthsToRange", "lengthZero", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "to<PERSON><PERSON><PERSON>", "parseDocument", "<PERSON><PERSON><PERSON><PERSON>", "FastTokenizer", "TextBufferTokenizer", "CallbackIterable", "combineTextEditInfos", "BracketPairsTree", "didLanguageChange", "languageId", "brackets", "constructor", "textModel", "getLanguageConfiguration", "didChangeEmitter", "denseKeyProvider", "onDidChange", "event", "queuedTextEditsForInitialAstWithoutTokens", "queuedTextEdits", "tokenization", "hasTokens", "getSingleLanguageBracketTokens", "getLanguageId", "tokenizer", "getValue", "initialAstWithoutTokens", "undefined", "astWithTokens", "backgroundTokenizationState", "parseDocumentFromTextBuffer", "handleDidChangeBackgroundTokenizationState", "wasUndefined", "fire", "handleDidChangeTokens", "ranges", "edits", "map", "r", "fromLineNumber", "toLineNumber", "handleEdits", "handleContentChanged", "change", "fromModelContentChanges", "changes", "tokenChange", "result", "flushQueue", "length", "previousAst", "immutable", "isPure", "previousAstClone", "deepClone", "getBracketsInRange", "range", "onlyColorizedBrackets", "startOffset", "startLineNumber", "startColumn", "endOffset", "endLineNumber", "endColumn", "cb", "node", "collectBrackets", "Map", "getBracketPairsInRange", "includeMinIndentation", "startLength", "getStartPosition", "<PERSON><PERSON><PERSON><PERSON>", "getEndPosition", "context", "CollectBracketPairsContext", "collectBracketPairs", "getFirstBracketAfter", "position", "getFirstBracketBefore", "nodeOffsetStart", "nodeOffsetEnd", "kind", "lengths", "child", "children", "push", "i", "bracketInfo", "level", "nestingLevelOfEqualBracketType", "levelPerBracketType", "parentPairIsIncomplete", "<PERSON><PERSON><PERSON>", "childCount", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "childEndsAfterEnd", "shouldC<PERSON><PERSON>ue", "colorize", "closingBracket", "closesColorized", "openingBracket", "levelPerBracket", "existing", "get", "text", "set", "openingBracketEnd", "minIndentation", "computeMinIndentation", "curOffset", "childOffset"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/bracketPairsTree.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Emitter } from '../../../../../base/common/event.js';\nimport { Disposable } from '../../../../../base/common/lifecycle.js';\nimport { BracketInfo, BracketPairWithMinIndentationInfo } from '../../../textModelBracketPairs.js';\nimport { TextEditInfo } from './beforeEditPositionMapper.js';\nimport { LanguageAgnosticBracketTokens } from './brackets.js';\nimport { lengthAdd, lengthGreaterThanEqual, lengthLessThan, lengthLessThanEqual, lengthsToRange, lengthZero, positionToLength, toLength } from './length.js';\nimport { parseDocument } from './parser.js';\nimport { DenseKeyProvider } from './smallImmutableSet.js';\nimport { FastTokenizer, TextBufferTokenizer } from './tokenizer.js';\nimport { CallbackIterable } from '../../../../../base/common/arrays.js';\nimport { combineTextEditInfos } from './combineTextEditInfos.js';\nexport class BracketPairsTree extends Disposable {\n    didLanguageChange(languageId) {\n        return this.brackets.didLanguageChange(languageId);\n    }\n    constructor(textModel, getLanguageConfiguration) {\n        super();\n        this.textModel = textModel;\n        this.getLanguageConfiguration = getLanguageConfiguration;\n        this.didChangeEmitter = new Emitter();\n        this.denseKeyProvider = new DenseKeyProvider();\n        this.brackets = new LanguageAgnosticBracketTokens(this.denseKeyProvider, this.getLanguageConfiguration);\n        this.onDidChange = this.didChangeEmitter.event;\n        this.queuedTextEditsForInitialAstWithoutTokens = [];\n        this.queuedTextEdits = [];\n        if (!textModel.tokenization.hasTokens) {\n            const brackets = this.brackets.getSingleLanguageBracketTokens(this.textModel.getLanguageId());\n            const tokenizer = new FastTokenizer(this.textModel.getValue(), brackets);\n            this.initialAstWithoutTokens = parseDocument(tokenizer, [], undefined, true);\n            this.astWithTokens = this.initialAstWithoutTokens;\n        }\n        else if (textModel.tokenization.backgroundTokenizationState === 2 /* BackgroundTokenizationState.Completed */) {\n            // Skip the initial ast, as there is no flickering.\n            // Directly create the tree with token information.\n            this.initialAstWithoutTokens = undefined;\n            this.astWithTokens = this.parseDocumentFromTextBuffer([], undefined, false);\n        }\n        else {\n            // We missed some token changes already, so we cannot use the fast tokenizer + delta increments\n            this.initialAstWithoutTokens = this.parseDocumentFromTextBuffer([], undefined, true);\n            this.astWithTokens = this.initialAstWithoutTokens;\n        }\n    }\n    //#region TextModel events\n    handleDidChangeBackgroundTokenizationState() {\n        if (this.textModel.tokenization.backgroundTokenizationState === 2 /* BackgroundTokenizationState.Completed */) {\n            const wasUndefined = this.initialAstWithoutTokens === undefined;\n            // Clear the initial tree as we can use the tree with token information now.\n            this.initialAstWithoutTokens = undefined;\n            if (!wasUndefined) {\n                this.didChangeEmitter.fire();\n            }\n        }\n    }\n    handleDidChangeTokens({ ranges }) {\n        const edits = ranges.map(r => new TextEditInfo(toLength(r.fromLineNumber - 1, 0), toLength(r.toLineNumber, 0), toLength(r.toLineNumber - r.fromLineNumber + 1, 0)));\n        this.handleEdits(edits, true);\n        if (!this.initialAstWithoutTokens) {\n            this.didChangeEmitter.fire();\n        }\n    }\n    handleContentChanged(change) {\n        const edits = TextEditInfo.fromModelContentChanges(change.changes);\n        this.handleEdits(edits, false);\n    }\n    handleEdits(edits, tokenChange) {\n        // Lazily queue the edits and only apply them when the tree is accessed.\n        const result = combineTextEditInfos(this.queuedTextEdits, edits);\n        this.queuedTextEdits = result;\n        if (this.initialAstWithoutTokens && !tokenChange) {\n            this.queuedTextEditsForInitialAstWithoutTokens = combineTextEditInfos(this.queuedTextEditsForInitialAstWithoutTokens, edits);\n        }\n    }\n    //#endregion\n    flushQueue() {\n        if (this.queuedTextEdits.length > 0) {\n            this.astWithTokens = this.parseDocumentFromTextBuffer(this.queuedTextEdits, this.astWithTokens, false);\n            this.queuedTextEdits = [];\n        }\n        if (this.queuedTextEditsForInitialAstWithoutTokens.length > 0) {\n            if (this.initialAstWithoutTokens) {\n                this.initialAstWithoutTokens = this.parseDocumentFromTextBuffer(this.queuedTextEditsForInitialAstWithoutTokens, this.initialAstWithoutTokens, false);\n            }\n            this.queuedTextEditsForInitialAstWithoutTokens = [];\n        }\n    }\n    /**\n     * @pure (only if isPure = true)\n    */\n    parseDocumentFromTextBuffer(edits, previousAst, immutable) {\n        // Is much faster if `isPure = false`.\n        const isPure = false;\n        const previousAstClone = isPure ? previousAst?.deepClone() : previousAst;\n        const tokenizer = new TextBufferTokenizer(this.textModel, this.brackets);\n        const result = parseDocument(tokenizer, edits, previousAstClone, immutable);\n        return result;\n    }\n    getBracketsInRange(range, onlyColorizedBrackets) {\n        this.flushQueue();\n        const startOffset = toLength(range.startLineNumber - 1, range.startColumn - 1);\n        const endOffset = toLength(range.endLineNumber - 1, range.endColumn - 1);\n        return new CallbackIterable(cb => {\n            const node = this.initialAstWithoutTokens || this.astWithTokens;\n            collectBrackets(node, lengthZero, node.length, startOffset, endOffset, cb, 0, 0, new Map(), onlyColorizedBrackets);\n        });\n    }\n    getBracketPairsInRange(range, includeMinIndentation) {\n        this.flushQueue();\n        const startLength = positionToLength(range.getStartPosition());\n        const endLength = positionToLength(range.getEndPosition());\n        return new CallbackIterable(cb => {\n            const node = this.initialAstWithoutTokens || this.astWithTokens;\n            const context = new CollectBracketPairsContext(cb, includeMinIndentation, this.textModel);\n            collectBracketPairs(node, lengthZero, node.length, startLength, endLength, context, 0, new Map());\n        });\n    }\n    getFirstBracketAfter(position) {\n        this.flushQueue();\n        const node = this.initialAstWithoutTokens || this.astWithTokens;\n        return getFirstBracketAfter(node, lengthZero, node.length, positionToLength(position));\n    }\n    getFirstBracketBefore(position) {\n        this.flushQueue();\n        const node = this.initialAstWithoutTokens || this.astWithTokens;\n        return getFirstBracketBefore(node, lengthZero, node.length, positionToLength(position));\n    }\n}\nfunction getFirstBracketBefore(node, nodeOffsetStart, nodeOffsetEnd, position) {\n    if (node.kind === 4 /* AstNodeKind.List */ || node.kind === 2 /* AstNodeKind.Pair */) {\n        const lengths = [];\n        for (const child of node.children) {\n            nodeOffsetEnd = lengthAdd(nodeOffsetStart, child.length);\n            lengths.push({ nodeOffsetStart, nodeOffsetEnd });\n            nodeOffsetStart = nodeOffsetEnd;\n        }\n        for (let i = lengths.length - 1; i >= 0; i--) {\n            const { nodeOffsetStart, nodeOffsetEnd } = lengths[i];\n            if (lengthLessThan(nodeOffsetStart, position)) {\n                const result = getFirstBracketBefore(node.children[i], nodeOffsetStart, nodeOffsetEnd, position);\n                if (result) {\n                    return result;\n                }\n            }\n        }\n        return null;\n    }\n    else if (node.kind === 3 /* AstNodeKind.UnexpectedClosingBracket */) {\n        return null;\n    }\n    else if (node.kind === 1 /* AstNodeKind.Bracket */) {\n        const range = lengthsToRange(nodeOffsetStart, nodeOffsetEnd);\n        return {\n            bracketInfo: node.bracketInfo,\n            range\n        };\n    }\n    return null;\n}\nfunction getFirstBracketAfter(node, nodeOffsetStart, nodeOffsetEnd, position) {\n    if (node.kind === 4 /* AstNodeKind.List */ || node.kind === 2 /* AstNodeKind.Pair */) {\n        for (const child of node.children) {\n            nodeOffsetEnd = lengthAdd(nodeOffsetStart, child.length);\n            if (lengthLessThan(position, nodeOffsetEnd)) {\n                const result = getFirstBracketAfter(child, nodeOffsetStart, nodeOffsetEnd, position);\n                if (result) {\n                    return result;\n                }\n            }\n            nodeOffsetStart = nodeOffsetEnd;\n        }\n        return null;\n    }\n    else if (node.kind === 3 /* AstNodeKind.UnexpectedClosingBracket */) {\n        return null;\n    }\n    else if (node.kind === 1 /* AstNodeKind.Bracket */) {\n        const range = lengthsToRange(nodeOffsetStart, nodeOffsetEnd);\n        return {\n            bracketInfo: node.bracketInfo,\n            range\n        };\n    }\n    return null;\n}\nfunction collectBrackets(node, nodeOffsetStart, nodeOffsetEnd, startOffset, endOffset, push, level, nestingLevelOfEqualBracketType, levelPerBracketType, onlyColorizedBrackets, parentPairIsIncomplete = false) {\n    if (level > 200) {\n        return true;\n    }\n    whileLoop: while (true) {\n        switch (node.kind) {\n            case 4 /* AstNodeKind.List */: {\n                const childCount = node.childrenLength;\n                for (let i = 0; i < childCount; i++) {\n                    const child = node.getChild(i);\n                    if (!child) {\n                        continue;\n                    }\n                    nodeOffsetEnd = lengthAdd(nodeOffsetStart, child.length);\n                    if (lengthLessThanEqual(nodeOffsetStart, endOffset) &&\n                        lengthGreaterThanEqual(nodeOffsetEnd, startOffset)) {\n                        const childEndsAfterEnd = lengthGreaterThanEqual(nodeOffsetEnd, endOffset);\n                        if (childEndsAfterEnd) {\n                            // No child after this child in the requested window, don't recurse\n                            node = child;\n                            continue whileLoop;\n                        }\n                        const shouldContinue = collectBrackets(child, nodeOffsetStart, nodeOffsetEnd, startOffset, endOffset, push, level, 0, levelPerBracketType, onlyColorizedBrackets);\n                        if (!shouldContinue) {\n                            return false;\n                        }\n                    }\n                    nodeOffsetStart = nodeOffsetEnd;\n                }\n                return true;\n            }\n            case 2 /* AstNodeKind.Pair */: {\n                const colorize = !onlyColorizedBrackets || !node.closingBracket || node.closingBracket.bracketInfo.closesColorized(node.openingBracket.bracketInfo);\n                let levelPerBracket = 0;\n                if (levelPerBracketType) {\n                    let existing = levelPerBracketType.get(node.openingBracket.text);\n                    if (existing === undefined) {\n                        existing = 0;\n                    }\n                    levelPerBracket = existing;\n                    if (colorize) {\n                        existing++;\n                        levelPerBracketType.set(node.openingBracket.text, existing);\n                    }\n                }\n                const childCount = node.childrenLength;\n                for (let i = 0; i < childCount; i++) {\n                    const child = node.getChild(i);\n                    if (!child) {\n                        continue;\n                    }\n                    nodeOffsetEnd = lengthAdd(nodeOffsetStart, child.length);\n                    if (lengthLessThanEqual(nodeOffsetStart, endOffset) &&\n                        lengthGreaterThanEqual(nodeOffsetEnd, startOffset)) {\n                        const childEndsAfterEnd = lengthGreaterThanEqual(nodeOffsetEnd, endOffset);\n                        if (childEndsAfterEnd && child.kind !== 1 /* AstNodeKind.Bracket */) {\n                            // No child after this child in the requested window, don't recurse\n                            // Don't do this for brackets because of unclosed/unopened brackets\n                            node = child;\n                            if (colorize) {\n                                level++;\n                                nestingLevelOfEqualBracketType = levelPerBracket + 1;\n                            }\n                            else {\n                                nestingLevelOfEqualBracketType = levelPerBracket;\n                            }\n                            continue whileLoop;\n                        }\n                        if (colorize || child.kind !== 1 /* AstNodeKind.Bracket */ || !node.closingBracket) {\n                            const shouldContinue = collectBrackets(child, nodeOffsetStart, nodeOffsetEnd, startOffset, endOffset, push, colorize ? level + 1 : level, colorize ? levelPerBracket + 1 : levelPerBracket, levelPerBracketType, onlyColorizedBrackets, !node.closingBracket);\n                            if (!shouldContinue) {\n                                return false;\n                            }\n                        }\n                    }\n                    nodeOffsetStart = nodeOffsetEnd;\n                }\n                levelPerBracketType?.set(node.openingBracket.text, levelPerBracket);\n                return true;\n            }\n            case 3 /* AstNodeKind.UnexpectedClosingBracket */: {\n                const range = lengthsToRange(nodeOffsetStart, nodeOffsetEnd);\n                return push(new BracketInfo(range, level - 1, 0, true));\n            }\n            case 1 /* AstNodeKind.Bracket */: {\n                const range = lengthsToRange(nodeOffsetStart, nodeOffsetEnd);\n                return push(new BracketInfo(range, level - 1, nestingLevelOfEqualBracketType - 1, parentPairIsIncomplete));\n            }\n            case 0 /* AstNodeKind.Text */:\n                return true;\n        }\n    }\n}\nclass CollectBracketPairsContext {\n    constructor(push, includeMinIndentation, textModel) {\n        this.push = push;\n        this.includeMinIndentation = includeMinIndentation;\n        this.textModel = textModel;\n    }\n}\nfunction collectBracketPairs(node, nodeOffsetStart, nodeOffsetEnd, startOffset, endOffset, context, level, levelPerBracketType) {\n    if (level > 200) {\n        return true;\n    }\n    let shouldContinue = true;\n    if (node.kind === 2 /* AstNodeKind.Pair */) {\n        let levelPerBracket = 0;\n        if (levelPerBracketType) {\n            let existing = levelPerBracketType.get(node.openingBracket.text);\n            if (existing === undefined) {\n                existing = 0;\n            }\n            levelPerBracket = existing;\n            existing++;\n            levelPerBracketType.set(node.openingBracket.text, existing);\n        }\n        const openingBracketEnd = lengthAdd(nodeOffsetStart, node.openingBracket.length);\n        let minIndentation = -1;\n        if (context.includeMinIndentation) {\n            minIndentation = node.computeMinIndentation(nodeOffsetStart, context.textModel);\n        }\n        shouldContinue = context.push(new BracketPairWithMinIndentationInfo(lengthsToRange(nodeOffsetStart, nodeOffsetEnd), lengthsToRange(nodeOffsetStart, openingBracketEnd), node.closingBracket\n            ? lengthsToRange(lengthAdd(openingBracketEnd, node.child?.length || lengthZero), nodeOffsetEnd)\n            : undefined, level, levelPerBracket, node, minIndentation));\n        nodeOffsetStart = openingBracketEnd;\n        if (shouldContinue && node.child) {\n            const child = node.child;\n            nodeOffsetEnd = lengthAdd(nodeOffsetStart, child.length);\n            if (lengthLessThanEqual(nodeOffsetStart, endOffset) &&\n                lengthGreaterThanEqual(nodeOffsetEnd, startOffset)) {\n                shouldContinue = collectBracketPairs(child, nodeOffsetStart, nodeOffsetEnd, startOffset, endOffset, context, level + 1, levelPerBracketType);\n                if (!shouldContinue) {\n                    return false;\n                }\n            }\n        }\n        levelPerBracketType?.set(node.openingBracket.text, levelPerBracket);\n    }\n    else {\n        let curOffset = nodeOffsetStart;\n        for (const child of node.children) {\n            const childOffset = curOffset;\n            curOffset = lengthAdd(curOffset, child.length);\n            if (lengthLessThanEqual(childOffset, endOffset) &&\n                lengthLessThanEqual(startOffset, curOffset)) {\n                shouldContinue = collectBracketPairs(child, childOffset, curOffset, startOffset, endOffset, context, level, levelPerBracketType);\n                if (!shouldContinue) {\n                    return false;\n                }\n            }\n        }\n    }\n    return shouldContinue;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,OAAO,QAAQ,qCAAqC;AAC7D,SAASC,UAAU,QAAQ,yCAAyC;AACpE,SAASC,WAAW,EAAEC,iCAAiC,QAAQ,mCAAmC;AAClG,SAASC,YAAY,QAAQ,+BAA+B;AAC5D,SAASC,6BAA6B,QAAQ,eAAe;AAC7D,SAASC,SAAS,EAAEC,sBAAsB,EAAEC,cAAc,EAAEC,mBAAmB,EAAEC,cAAc,EAAEC,UAAU,EAAEC,gBAAgB,EAAEC,QAAQ,QAAQ,aAAa;AAC5J,SAASC,aAAa,QAAQ,aAAa;AAC3C,SAASC,gBAAgB,QAAQ,wBAAwB;AACzD,SAASC,aAAa,EAAEC,mBAAmB,QAAQ,gBAAgB;AACnE,SAASC,gBAAgB,QAAQ,sCAAsC;AACvE,SAASC,oBAAoB,QAAQ,2BAA2B;AAChE,OAAO,MAAMC,gBAAgB,SAASnB,UAAU,CAAC;EAC7CoB,iBAAiBA,CAACC,UAAU,EAAE;IAC1B,OAAO,IAAI,CAACC,QAAQ,CAACF,iBAAiB,CAACC,UAAU,CAAC;EACtD;EACAE,WAAWA,CAACC,SAAS,EAAEC,wBAAwB,EAAE;IAC7C,KAAK,CAAC,CAAC;IACP,IAAI,CAACD,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,wBAAwB,GAAGA,wBAAwB;IACxD,IAAI,CAACC,gBAAgB,GAAG,IAAI3B,OAAO,CAAC,CAAC;IACrC,IAAI,CAAC4B,gBAAgB,GAAG,IAAIb,gBAAgB,CAAC,CAAC;IAC9C,IAAI,CAACQ,QAAQ,GAAG,IAAIlB,6BAA6B,CAAC,IAAI,CAACuB,gBAAgB,EAAE,IAAI,CAACF,wBAAwB,CAAC;IACvG,IAAI,CAACG,WAAW,GAAG,IAAI,CAACF,gBAAgB,CAACG,KAAK;IAC9C,IAAI,CAACC,yCAAyC,GAAG,EAAE;IACnD,IAAI,CAACC,eAAe,GAAG,EAAE;IACzB,IAAI,CAACP,SAAS,CAACQ,YAAY,CAACC,SAAS,EAAE;MACnC,MAAMX,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACY,8BAA8B,CAAC,IAAI,CAACV,SAAS,CAACW,aAAa,CAAC,CAAC,CAAC;MAC7F,MAAMC,SAAS,GAAG,IAAIrB,aAAa,CAAC,IAAI,CAACS,SAAS,CAACa,QAAQ,CAAC,CAAC,EAAEf,QAAQ,CAAC;MACxE,IAAI,CAACgB,uBAAuB,GAAGzB,aAAa,CAACuB,SAAS,EAAE,EAAE,EAAEG,SAAS,EAAE,IAAI,CAAC;MAC5E,IAAI,CAACC,aAAa,GAAG,IAAI,CAACF,uBAAuB;IACrD,CAAC,MACI,IAAId,SAAS,CAACQ,YAAY,CAACS,2BAA2B,KAAK,CAAC,CAAC,6CAA6C;MAC3G;MACA;MACA,IAAI,CAACH,uBAAuB,GAAGC,SAAS;MACxC,IAAI,CAACC,aAAa,GAAG,IAAI,CAACE,2BAA2B,CAAC,EAAE,EAAEH,SAAS,EAAE,KAAK,CAAC;IAC/E,CAAC,MACI;MACD;MACA,IAAI,CAACD,uBAAuB,GAAG,IAAI,CAACI,2BAA2B,CAAC,EAAE,EAAEH,SAAS,EAAE,IAAI,CAAC;MACpF,IAAI,CAACC,aAAa,GAAG,IAAI,CAACF,uBAAuB;IACrD;EACJ;EACA;EACAK,0CAA0CA,CAAA,EAAG;IACzC,IAAI,IAAI,CAACnB,SAAS,CAACQ,YAAY,CAACS,2BAA2B,KAAK,CAAC,CAAC,6CAA6C;MAC3G,MAAMG,YAAY,GAAG,IAAI,CAACN,uBAAuB,KAAKC,SAAS;MAC/D;MACA,IAAI,CAACD,uBAAuB,GAAGC,SAAS;MACxC,IAAI,CAACK,YAAY,EAAE;QACf,IAAI,CAAClB,gBAAgB,CAACmB,IAAI,CAAC,CAAC;MAChC;IACJ;EACJ;EACAC,qBAAqBA,CAAC;IAAEC;EAAO,CAAC,EAAE;IAC9B,MAAMC,KAAK,GAAGD,MAAM,CAACE,GAAG,CAACC,CAAC,IAAI,IAAI/C,YAAY,CAACS,QAAQ,CAACsC,CAAC,CAACC,cAAc,GAAG,CAAC,EAAE,CAAC,CAAC,EAAEvC,QAAQ,CAACsC,CAAC,CAACE,YAAY,EAAE,CAAC,CAAC,EAAExC,QAAQ,CAACsC,CAAC,CAACE,YAAY,GAAGF,CAAC,CAACC,cAAc,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnK,IAAI,CAACE,WAAW,CAACL,KAAK,EAAE,IAAI,CAAC;IAC7B,IAAI,CAAC,IAAI,CAACV,uBAAuB,EAAE;MAC/B,IAAI,CAACZ,gBAAgB,CAACmB,IAAI,CAAC,CAAC;IAChC;EACJ;EACAS,oBAAoBA,CAACC,MAAM,EAAE;IACzB,MAAMP,KAAK,GAAG7C,YAAY,CAACqD,uBAAuB,CAACD,MAAM,CAACE,OAAO,CAAC;IAClE,IAAI,CAACJ,WAAW,CAACL,KAAK,EAAE,KAAK,CAAC;EAClC;EACAK,WAAWA,CAACL,KAAK,EAAEU,WAAW,EAAE;IAC5B;IACA,MAAMC,MAAM,GAAGzC,oBAAoB,CAAC,IAAI,CAACa,eAAe,EAAEiB,KAAK,CAAC;IAChE,IAAI,CAACjB,eAAe,GAAG4B,MAAM;IAC7B,IAAI,IAAI,CAACrB,uBAAuB,IAAI,CAACoB,WAAW,EAAE;MAC9C,IAAI,CAAC5B,yCAAyC,GAAGZ,oBAAoB,CAAC,IAAI,CAACY,yCAAyC,EAAEkB,KAAK,CAAC;IAChI;EACJ;EACA;EACAY,UAAUA,CAAA,EAAG;IACT,IAAI,IAAI,CAAC7B,eAAe,CAAC8B,MAAM,GAAG,CAAC,EAAE;MACjC,IAAI,CAACrB,aAAa,GAAG,IAAI,CAACE,2BAA2B,CAAC,IAAI,CAACX,eAAe,EAAE,IAAI,CAACS,aAAa,EAAE,KAAK,CAAC;MACtG,IAAI,CAACT,eAAe,GAAG,EAAE;IAC7B;IACA,IAAI,IAAI,CAACD,yCAAyC,CAAC+B,MAAM,GAAG,CAAC,EAAE;MAC3D,IAAI,IAAI,CAACvB,uBAAuB,EAAE;QAC9B,IAAI,CAACA,uBAAuB,GAAG,IAAI,CAACI,2BAA2B,CAAC,IAAI,CAACZ,yCAAyC,EAAE,IAAI,CAACQ,uBAAuB,EAAE,KAAK,CAAC;MACxJ;MACA,IAAI,CAACR,yCAAyC,GAAG,EAAE;IACvD;EACJ;EACA;AACJ;AACA;EACIY,2BAA2BA,CAACM,KAAK,EAAEc,WAAW,EAAEC,SAAS,EAAE;IACvD;IACA,MAAMC,MAAM,GAAG,KAAK;IACpB,MAAMC,gBAAgB,GAAGD,MAAM,GAAGF,WAAW,EAAEI,SAAS,CAAC,CAAC,GAAGJ,WAAW;IACxE,MAAM1B,SAAS,GAAG,IAAIpB,mBAAmB,CAAC,IAAI,CAACQ,SAAS,EAAE,IAAI,CAACF,QAAQ,CAAC;IACxE,MAAMqC,MAAM,GAAG9C,aAAa,CAACuB,SAAS,EAAEY,KAAK,EAAEiB,gBAAgB,EAAEF,SAAS,CAAC;IAC3E,OAAOJ,MAAM;EACjB;EACAQ,kBAAkBA,CAACC,KAAK,EAAEC,qBAAqB,EAAE;IAC7C,IAAI,CAACT,UAAU,CAAC,CAAC;IACjB,MAAMU,WAAW,GAAG1D,QAAQ,CAACwD,KAAK,CAACG,eAAe,GAAG,CAAC,EAAEH,KAAK,CAACI,WAAW,GAAG,CAAC,CAAC;IAC9E,MAAMC,SAAS,GAAG7D,QAAQ,CAACwD,KAAK,CAACM,aAAa,GAAG,CAAC,EAAEN,KAAK,CAACO,SAAS,GAAG,CAAC,CAAC;IACxE,OAAO,IAAI1D,gBAAgB,CAAC2D,EAAE,IAAI;MAC9B,MAAMC,IAAI,GAAG,IAAI,CAACvC,uBAAuB,IAAI,IAAI,CAACE,aAAa;MAC/DsC,eAAe,CAACD,IAAI,EAAEnE,UAAU,EAAEmE,IAAI,CAAChB,MAAM,EAAES,WAAW,EAAEG,SAAS,EAAEG,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,IAAIG,GAAG,CAAC,CAAC,EAAEV,qBAAqB,CAAC;IACtH,CAAC,CAAC;EACN;EACAW,sBAAsBA,CAACZ,KAAK,EAAEa,qBAAqB,EAAE;IACjD,IAAI,CAACrB,UAAU,CAAC,CAAC;IACjB,MAAMsB,WAAW,GAAGvE,gBAAgB,CAACyD,KAAK,CAACe,gBAAgB,CAAC,CAAC,CAAC;IAC9D,MAAMC,SAAS,GAAGzE,gBAAgB,CAACyD,KAAK,CAACiB,cAAc,CAAC,CAAC,CAAC;IAC1D,OAAO,IAAIpE,gBAAgB,CAAC2D,EAAE,IAAI;MAC9B,MAAMC,IAAI,GAAG,IAAI,CAACvC,uBAAuB,IAAI,IAAI,CAACE,aAAa;MAC/D,MAAM8C,OAAO,GAAG,IAAIC,0BAA0B,CAACX,EAAE,EAAEK,qBAAqB,EAAE,IAAI,CAACzD,SAAS,CAAC;MACzFgE,mBAAmB,CAACX,IAAI,EAAEnE,UAAU,EAAEmE,IAAI,CAAChB,MAAM,EAAEqB,WAAW,EAAEE,SAAS,EAAEE,OAAO,EAAE,CAAC,EAAE,IAAIP,GAAG,CAAC,CAAC,CAAC;IACrG,CAAC,CAAC;EACN;EACAU,oBAAoBA,CAACC,QAAQ,EAAE;IAC3B,IAAI,CAAC9B,UAAU,CAAC,CAAC;IACjB,MAAMiB,IAAI,GAAG,IAAI,CAACvC,uBAAuB,IAAI,IAAI,CAACE,aAAa;IAC/D,OAAOiD,oBAAoB,CAACZ,IAAI,EAAEnE,UAAU,EAAEmE,IAAI,CAAChB,MAAM,EAAElD,gBAAgB,CAAC+E,QAAQ,CAAC,CAAC;EAC1F;EACAC,qBAAqBA,CAACD,QAAQ,EAAE;IAC5B,IAAI,CAAC9B,UAAU,CAAC,CAAC;IACjB,MAAMiB,IAAI,GAAG,IAAI,CAACvC,uBAAuB,IAAI,IAAI,CAACE,aAAa;IAC/D,OAAOmD,qBAAqB,CAACd,IAAI,EAAEnE,UAAU,EAAEmE,IAAI,CAAChB,MAAM,EAAElD,gBAAgB,CAAC+E,QAAQ,CAAC,CAAC;EAC3F;AACJ;AACA,SAASC,qBAAqBA,CAACd,IAAI,EAAEe,eAAe,EAAEC,aAAa,EAAEH,QAAQ,EAAE;EAC3E,IAAIb,IAAI,CAACiB,IAAI,KAAK,CAAC,CAAC,0BAA0BjB,IAAI,CAACiB,IAAI,KAAK,CAAC,CAAC,wBAAwB;IAClF,MAAMC,OAAO,GAAG,EAAE;IAClB,KAAK,MAAMC,KAAK,IAAInB,IAAI,CAACoB,QAAQ,EAAE;MAC/BJ,aAAa,GAAGxF,SAAS,CAACuF,eAAe,EAAEI,KAAK,CAACnC,MAAM,CAAC;MACxDkC,OAAO,CAACG,IAAI,CAAC;QAAEN,eAAe;QAAEC;MAAc,CAAC,CAAC;MAChDD,eAAe,GAAGC,aAAa;IACnC;IACA,KAAK,IAAIM,CAAC,GAAGJ,OAAO,CAAClC,MAAM,GAAG,CAAC,EAAEsC,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC1C,MAAM;QAAEP,eAAe;QAAEC;MAAc,CAAC,GAAGE,OAAO,CAACI,CAAC,CAAC;MACrD,IAAI5F,cAAc,CAACqF,eAAe,EAAEF,QAAQ,CAAC,EAAE;QAC3C,MAAM/B,MAAM,GAAGgC,qBAAqB,CAACd,IAAI,CAACoB,QAAQ,CAACE,CAAC,CAAC,EAAEP,eAAe,EAAEC,aAAa,EAAEH,QAAQ,CAAC;QAChG,IAAI/B,MAAM,EAAE;UACR,OAAOA,MAAM;QACjB;MACJ;IACJ;IACA,OAAO,IAAI;EACf,CAAC,MACI,IAAIkB,IAAI,CAACiB,IAAI,KAAK,CAAC,CAAC,4CAA4C;IACjE,OAAO,IAAI;EACf,CAAC,MACI,IAAIjB,IAAI,CAACiB,IAAI,KAAK,CAAC,CAAC,2BAA2B;IAChD,MAAM1B,KAAK,GAAG3D,cAAc,CAACmF,eAAe,EAAEC,aAAa,CAAC;IAC5D,OAAO;MACHO,WAAW,EAAEvB,IAAI,CAACuB,WAAW;MAC7BhC;IACJ,CAAC;EACL;EACA,OAAO,IAAI;AACf;AACA,SAASqB,oBAAoBA,CAACZ,IAAI,EAAEe,eAAe,EAAEC,aAAa,EAAEH,QAAQ,EAAE;EAC1E,IAAIb,IAAI,CAACiB,IAAI,KAAK,CAAC,CAAC,0BAA0BjB,IAAI,CAACiB,IAAI,KAAK,CAAC,CAAC,wBAAwB;IAClF,KAAK,MAAME,KAAK,IAAInB,IAAI,CAACoB,QAAQ,EAAE;MAC/BJ,aAAa,GAAGxF,SAAS,CAACuF,eAAe,EAAEI,KAAK,CAACnC,MAAM,CAAC;MACxD,IAAItD,cAAc,CAACmF,QAAQ,EAAEG,aAAa,CAAC,EAAE;QACzC,MAAMlC,MAAM,GAAG8B,oBAAoB,CAACO,KAAK,EAAEJ,eAAe,EAAEC,aAAa,EAAEH,QAAQ,CAAC;QACpF,IAAI/B,MAAM,EAAE;UACR,OAAOA,MAAM;QACjB;MACJ;MACAiC,eAAe,GAAGC,aAAa;IACnC;IACA,OAAO,IAAI;EACf,CAAC,MACI,IAAIhB,IAAI,CAACiB,IAAI,KAAK,CAAC,CAAC,4CAA4C;IACjE,OAAO,IAAI;EACf,CAAC,MACI,IAAIjB,IAAI,CAACiB,IAAI,KAAK,CAAC,CAAC,2BAA2B;IAChD,MAAM1B,KAAK,GAAG3D,cAAc,CAACmF,eAAe,EAAEC,aAAa,CAAC;IAC5D,OAAO;MACHO,WAAW,EAAEvB,IAAI,CAACuB,WAAW;MAC7BhC;IACJ,CAAC;EACL;EACA,OAAO,IAAI;AACf;AACA,SAASU,eAAeA,CAACD,IAAI,EAAEe,eAAe,EAAEC,aAAa,EAAEvB,WAAW,EAAEG,SAAS,EAAEyB,IAAI,EAAEG,KAAK,EAAEC,8BAA8B,EAAEC,mBAAmB,EAAElC,qBAAqB,EAAEmC,sBAAsB,GAAG,KAAK,EAAE;EAC5M,IAAIH,KAAK,GAAG,GAAG,EAAE;IACb,OAAO,IAAI;EACf;EACAI,SAAS,EAAE,OAAO,IAAI,EAAE;IACpB,QAAQ5B,IAAI,CAACiB,IAAI;MACb,KAAK,CAAC,CAAC;QAAwB;UAC3B,MAAMY,UAAU,GAAG7B,IAAI,CAAC8B,cAAc;UACtC,KAAK,IAAIR,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGO,UAAU,EAAEP,CAAC,EAAE,EAAE;YACjC,MAAMH,KAAK,GAAGnB,IAAI,CAAC+B,QAAQ,CAACT,CAAC,CAAC;YAC9B,IAAI,CAACH,KAAK,EAAE;cACR;YACJ;YACAH,aAAa,GAAGxF,SAAS,CAACuF,eAAe,EAAEI,KAAK,CAACnC,MAAM,CAAC;YACxD,IAAIrD,mBAAmB,CAACoF,eAAe,EAAEnB,SAAS,CAAC,IAC/CnE,sBAAsB,CAACuF,aAAa,EAAEvB,WAAW,CAAC,EAAE;cACpD,MAAMuC,iBAAiB,GAAGvG,sBAAsB,CAACuF,aAAa,EAAEpB,SAAS,CAAC;cAC1E,IAAIoC,iBAAiB,EAAE;gBACnB;gBACAhC,IAAI,GAAGmB,KAAK;gBACZ,SAASS,SAAS;cACtB;cACA,MAAMK,cAAc,GAAGhC,eAAe,CAACkB,KAAK,EAAEJ,eAAe,EAAEC,aAAa,EAAEvB,WAAW,EAAEG,SAAS,EAAEyB,IAAI,EAAEG,KAAK,EAAE,CAAC,EAAEE,mBAAmB,EAAElC,qBAAqB,CAAC;cACjK,IAAI,CAACyC,cAAc,EAAE;gBACjB,OAAO,KAAK;cAChB;YACJ;YACAlB,eAAe,GAAGC,aAAa;UACnC;UACA,OAAO,IAAI;QACf;MACA,KAAK,CAAC,CAAC;QAAwB;UAC3B,MAAMkB,QAAQ,GAAG,CAAC1C,qBAAqB,IAAI,CAACQ,IAAI,CAACmC,cAAc,IAAInC,IAAI,CAACmC,cAAc,CAACZ,WAAW,CAACa,eAAe,CAACpC,IAAI,CAACqC,cAAc,CAACd,WAAW,CAAC;UACnJ,IAAIe,eAAe,GAAG,CAAC;UACvB,IAAIZ,mBAAmB,EAAE;YACrB,IAAIa,QAAQ,GAAGb,mBAAmB,CAACc,GAAG,CAACxC,IAAI,CAACqC,cAAc,CAACI,IAAI,CAAC;YAChE,IAAIF,QAAQ,KAAK7E,SAAS,EAAE;cACxB6E,QAAQ,GAAG,CAAC;YAChB;YACAD,eAAe,GAAGC,QAAQ;YAC1B,IAAIL,QAAQ,EAAE;cACVK,QAAQ,EAAE;cACVb,mBAAmB,CAACgB,GAAG,CAAC1C,IAAI,CAACqC,cAAc,CAACI,IAAI,EAAEF,QAAQ,CAAC;YAC/D;UACJ;UACA,MAAMV,UAAU,GAAG7B,IAAI,CAAC8B,cAAc;UACtC,KAAK,IAAIR,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGO,UAAU,EAAEP,CAAC,EAAE,EAAE;YACjC,MAAMH,KAAK,GAAGnB,IAAI,CAAC+B,QAAQ,CAACT,CAAC,CAAC;YAC9B,IAAI,CAACH,KAAK,EAAE;cACR;YACJ;YACAH,aAAa,GAAGxF,SAAS,CAACuF,eAAe,EAAEI,KAAK,CAACnC,MAAM,CAAC;YACxD,IAAIrD,mBAAmB,CAACoF,eAAe,EAAEnB,SAAS,CAAC,IAC/CnE,sBAAsB,CAACuF,aAAa,EAAEvB,WAAW,CAAC,EAAE;cACpD,MAAMuC,iBAAiB,GAAGvG,sBAAsB,CAACuF,aAAa,EAAEpB,SAAS,CAAC;cAC1E,IAAIoC,iBAAiB,IAAIb,KAAK,CAACF,IAAI,KAAK,CAAC,CAAC,2BAA2B;gBACjE;gBACA;gBACAjB,IAAI,GAAGmB,KAAK;gBACZ,IAAIe,QAAQ,EAAE;kBACVV,KAAK,EAAE;kBACPC,8BAA8B,GAAGa,eAAe,GAAG,CAAC;gBACxD,CAAC,MACI;kBACDb,8BAA8B,GAAGa,eAAe;gBACpD;gBACA,SAASV,SAAS;cACtB;cACA,IAAIM,QAAQ,IAAIf,KAAK,CAACF,IAAI,KAAK,CAAC,CAAC,6BAA6B,CAACjB,IAAI,CAACmC,cAAc,EAAE;gBAChF,MAAMF,cAAc,GAAGhC,eAAe,CAACkB,KAAK,EAAEJ,eAAe,EAAEC,aAAa,EAAEvB,WAAW,EAAEG,SAAS,EAAEyB,IAAI,EAAEa,QAAQ,GAAGV,KAAK,GAAG,CAAC,GAAGA,KAAK,EAAEU,QAAQ,GAAGI,eAAe,GAAG,CAAC,GAAGA,eAAe,EAAEZ,mBAAmB,EAAElC,qBAAqB,EAAE,CAACQ,IAAI,CAACmC,cAAc,CAAC;gBAC7P,IAAI,CAACF,cAAc,EAAE;kBACjB,OAAO,KAAK;gBAChB;cACJ;YACJ;YACAlB,eAAe,GAAGC,aAAa;UACnC;UACAU,mBAAmB,EAAEgB,GAAG,CAAC1C,IAAI,CAACqC,cAAc,CAACI,IAAI,EAAEH,eAAe,CAAC;UACnE,OAAO,IAAI;QACf;MACA,KAAK,CAAC,CAAC;QAA4C;UAC/C,MAAM/C,KAAK,GAAG3D,cAAc,CAACmF,eAAe,EAAEC,aAAa,CAAC;UAC5D,OAAOK,IAAI,CAAC,IAAIjG,WAAW,CAACmE,KAAK,EAAEiC,KAAK,GAAG,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;QAC3D;MACA,KAAK,CAAC,CAAC;QAA2B;UAC9B,MAAMjC,KAAK,GAAG3D,cAAc,CAACmF,eAAe,EAAEC,aAAa,CAAC;UAC5D,OAAOK,IAAI,CAAC,IAAIjG,WAAW,CAACmE,KAAK,EAAEiC,KAAK,GAAG,CAAC,EAAEC,8BAA8B,GAAG,CAAC,EAAEE,sBAAsB,CAAC,CAAC;QAC9G;MACA,KAAK,CAAC,CAAC;QACH,OAAO,IAAI;IACnB;EACJ;AACJ;AACA,MAAMjB,0BAA0B,CAAC;EAC7BhE,WAAWA,CAAC2E,IAAI,EAAEjB,qBAAqB,EAAEzD,SAAS,EAAE;IAChD,IAAI,CAAC0E,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACjB,qBAAqB,GAAGA,qBAAqB;IAClD,IAAI,CAACzD,SAAS,GAAGA,SAAS;EAC9B;AACJ;AACA,SAASgE,mBAAmBA,CAACX,IAAI,EAAEe,eAAe,EAAEC,aAAa,EAAEvB,WAAW,EAAEG,SAAS,EAAEa,OAAO,EAAEe,KAAK,EAAEE,mBAAmB,EAAE;EAC5H,IAAIF,KAAK,GAAG,GAAG,EAAE;IACb,OAAO,IAAI;EACf;EACA,IAAIS,cAAc,GAAG,IAAI;EACzB,IAAIjC,IAAI,CAACiB,IAAI,KAAK,CAAC,CAAC,wBAAwB;IACxC,IAAIqB,eAAe,GAAG,CAAC;IACvB,IAAIZ,mBAAmB,EAAE;MACrB,IAAIa,QAAQ,GAAGb,mBAAmB,CAACc,GAAG,CAACxC,IAAI,CAACqC,cAAc,CAACI,IAAI,CAAC;MAChE,IAAIF,QAAQ,KAAK7E,SAAS,EAAE;QACxB6E,QAAQ,GAAG,CAAC;MAChB;MACAD,eAAe,GAAGC,QAAQ;MAC1BA,QAAQ,EAAE;MACVb,mBAAmB,CAACgB,GAAG,CAAC1C,IAAI,CAACqC,cAAc,CAACI,IAAI,EAAEF,QAAQ,CAAC;IAC/D;IACA,MAAMI,iBAAiB,GAAGnH,SAAS,CAACuF,eAAe,EAAEf,IAAI,CAACqC,cAAc,CAACrD,MAAM,CAAC;IAChF,IAAI4D,cAAc,GAAG,CAAC,CAAC;IACvB,IAAInC,OAAO,CAACL,qBAAqB,EAAE;MAC/BwC,cAAc,GAAG5C,IAAI,CAAC6C,qBAAqB,CAAC9B,eAAe,EAAEN,OAAO,CAAC9D,SAAS,CAAC;IACnF;IACAsF,cAAc,GAAGxB,OAAO,CAACY,IAAI,CAAC,IAAIhG,iCAAiC,CAACO,cAAc,CAACmF,eAAe,EAAEC,aAAa,CAAC,EAAEpF,cAAc,CAACmF,eAAe,EAAE4B,iBAAiB,CAAC,EAAE3C,IAAI,CAACmC,cAAc,GACrLvG,cAAc,CAACJ,SAAS,CAACmH,iBAAiB,EAAE3C,IAAI,CAACmB,KAAK,EAAEnC,MAAM,IAAInD,UAAU,CAAC,EAAEmF,aAAa,CAAC,GAC7FtD,SAAS,EAAE8D,KAAK,EAAEc,eAAe,EAAEtC,IAAI,EAAE4C,cAAc,CAAC,CAAC;IAC/D7B,eAAe,GAAG4B,iBAAiB;IACnC,IAAIV,cAAc,IAAIjC,IAAI,CAACmB,KAAK,EAAE;MAC9B,MAAMA,KAAK,GAAGnB,IAAI,CAACmB,KAAK;MACxBH,aAAa,GAAGxF,SAAS,CAACuF,eAAe,EAAEI,KAAK,CAACnC,MAAM,CAAC;MACxD,IAAIrD,mBAAmB,CAACoF,eAAe,EAAEnB,SAAS,CAAC,IAC/CnE,sBAAsB,CAACuF,aAAa,EAAEvB,WAAW,CAAC,EAAE;QACpDwC,cAAc,GAAGtB,mBAAmB,CAACQ,KAAK,EAAEJ,eAAe,EAAEC,aAAa,EAAEvB,WAAW,EAAEG,SAAS,EAAEa,OAAO,EAAEe,KAAK,GAAG,CAAC,EAAEE,mBAAmB,CAAC;QAC5I,IAAI,CAACO,cAAc,EAAE;UACjB,OAAO,KAAK;QAChB;MACJ;IACJ;IACAP,mBAAmB,EAAEgB,GAAG,CAAC1C,IAAI,CAACqC,cAAc,CAACI,IAAI,EAAEH,eAAe,CAAC;EACvE,CAAC,MACI;IACD,IAAIQ,SAAS,GAAG/B,eAAe;IAC/B,KAAK,MAAMI,KAAK,IAAInB,IAAI,CAACoB,QAAQ,EAAE;MAC/B,MAAM2B,WAAW,GAAGD,SAAS;MAC7BA,SAAS,GAAGtH,SAAS,CAACsH,SAAS,EAAE3B,KAAK,CAACnC,MAAM,CAAC;MAC9C,IAAIrD,mBAAmB,CAACoH,WAAW,EAAEnD,SAAS,CAAC,IAC3CjE,mBAAmB,CAAC8D,WAAW,EAAEqD,SAAS,CAAC,EAAE;QAC7Cb,cAAc,GAAGtB,mBAAmB,CAACQ,KAAK,EAAE4B,WAAW,EAAED,SAAS,EAAErD,WAAW,EAAEG,SAAS,EAAEa,OAAO,EAAEe,KAAK,EAAEE,mBAAmB,CAAC;QAChI,IAAI,CAACO,cAAc,EAAE;UACjB,OAAO,KAAK;QAChB;MACJ;IACJ;EACJ;EACA,OAAOA,cAAc;AACzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}