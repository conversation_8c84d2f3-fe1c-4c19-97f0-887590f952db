{"ast": null, "code": "import { epsilon, splitter, resulterrbound, estimate, vec, sum, sum_three, scale } from './util.js';\nconst iccerrboundA = (10 + 96 * epsilon) * epsilon;\nconst iccerrboundB = (4 + 48 * epsilon) * epsilon;\nconst iccerrboundC = (44 + 576 * epsilon) * epsilon * epsilon;\nconst bc = vec(4);\nconst ca = vec(4);\nconst ab = vec(4);\nconst aa = vec(4);\nconst bb = vec(4);\nconst cc = vec(4);\nconst u = vec(4);\nconst v = vec(4);\nconst axtbc = vec(8);\nconst aytbc = vec(8);\nconst bxtca = vec(8);\nconst bytca = vec(8);\nconst cxtab = vec(8);\nconst cytab = vec(8);\nconst abt = vec(8);\nconst bct = vec(8);\nconst cat = vec(8);\nconst abtt = vec(4);\nconst bctt = vec(4);\nconst catt = vec(4);\nconst _8 = vec(8);\nconst _16 = vec(16);\nconst _16b = vec(16);\nconst _16c = vec(16);\nconst _32 = vec(32);\nconst _32b = vec(32);\nconst _48 = vec(48);\nconst _64 = vec(64);\nlet fin = vec(1152);\nlet fin2 = vec(1152);\nfunction finadd(finlen, a, alen) {\n  finlen = sum(finlen, fin, a, alen, fin2);\n  const tmp = fin;\n  fin = fin2;\n  fin2 = tmp;\n  return finlen;\n}\nfunction incircleadapt(ax, ay, bx, by, cx, cy, dx, dy, permanent) {\n  let finlen;\n  let adxtail, bdxtail, cdxtail, adytail, bdytail, cdytail;\n  let axtbclen, aytbclen, bxtcalen, bytcalen, cxtablen, cytablen;\n  let abtlen, bctlen, catlen;\n  let abttlen, bcttlen, cattlen;\n  let n1, n0;\n  let bvirt, c, ahi, alo, bhi, blo, _i, _j, _0, s1, s0, t1, t0, u3;\n  const adx = ax - dx;\n  const bdx = bx - dx;\n  const cdx = cx - dx;\n  const ady = ay - dy;\n  const bdy = by - dy;\n  const cdy = cy - dy;\n  s1 = bdx * cdy;\n  c = splitter * bdx;\n  ahi = c - (c - bdx);\n  alo = bdx - ahi;\n  c = splitter * cdy;\n  bhi = c - (c - cdy);\n  blo = cdy - bhi;\n  s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n  t1 = cdx * bdy;\n  c = splitter * cdx;\n  ahi = c - (c - cdx);\n  alo = cdx - ahi;\n  c = splitter * bdy;\n  bhi = c - (c - bdy);\n  blo = bdy - bhi;\n  t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n  _i = s0 - t0;\n  bvirt = s0 - _i;\n  bc[0] = s0 - (_i + bvirt) + (bvirt - t0);\n  _j = s1 + _i;\n  bvirt = _j - s1;\n  _0 = s1 - (_j - bvirt) + (_i - bvirt);\n  _i = _0 - t1;\n  bvirt = _0 - _i;\n  bc[1] = _0 - (_i + bvirt) + (bvirt - t1);\n  u3 = _j + _i;\n  bvirt = u3 - _j;\n  bc[2] = _j - (u3 - bvirt) + (_i - bvirt);\n  bc[3] = u3;\n  s1 = cdx * ady;\n  c = splitter * cdx;\n  ahi = c - (c - cdx);\n  alo = cdx - ahi;\n  c = splitter * ady;\n  bhi = c - (c - ady);\n  blo = ady - bhi;\n  s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n  t1 = adx * cdy;\n  c = splitter * adx;\n  ahi = c - (c - adx);\n  alo = adx - ahi;\n  c = splitter * cdy;\n  bhi = c - (c - cdy);\n  blo = cdy - bhi;\n  t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n  _i = s0 - t0;\n  bvirt = s0 - _i;\n  ca[0] = s0 - (_i + bvirt) + (bvirt - t0);\n  _j = s1 + _i;\n  bvirt = _j - s1;\n  _0 = s1 - (_j - bvirt) + (_i - bvirt);\n  _i = _0 - t1;\n  bvirt = _0 - _i;\n  ca[1] = _0 - (_i + bvirt) + (bvirt - t1);\n  u3 = _j + _i;\n  bvirt = u3 - _j;\n  ca[2] = _j - (u3 - bvirt) + (_i - bvirt);\n  ca[3] = u3;\n  s1 = adx * bdy;\n  c = splitter * adx;\n  ahi = c - (c - adx);\n  alo = adx - ahi;\n  c = splitter * bdy;\n  bhi = c - (c - bdy);\n  blo = bdy - bhi;\n  s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n  t1 = bdx * ady;\n  c = splitter * bdx;\n  ahi = c - (c - bdx);\n  alo = bdx - ahi;\n  c = splitter * ady;\n  bhi = c - (c - ady);\n  blo = ady - bhi;\n  t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n  _i = s0 - t0;\n  bvirt = s0 - _i;\n  ab[0] = s0 - (_i + bvirt) + (bvirt - t0);\n  _j = s1 + _i;\n  bvirt = _j - s1;\n  _0 = s1 - (_j - bvirt) + (_i - bvirt);\n  _i = _0 - t1;\n  bvirt = _0 - _i;\n  ab[1] = _0 - (_i + bvirt) + (bvirt - t1);\n  u3 = _j + _i;\n  bvirt = u3 - _j;\n  ab[2] = _j - (u3 - bvirt) + (_i - bvirt);\n  ab[3] = u3;\n  finlen = sum(sum(sum(scale(scale(4, bc, adx, _8), _8, adx, _16), _16, scale(scale(4, bc, ady, _8), _8, ady, _16b), _16b, _32), _32, sum(scale(scale(4, ca, bdx, _8), _8, bdx, _16), _16, scale(scale(4, ca, bdy, _8), _8, bdy, _16b), _16b, _32b), _32b, _64), _64, sum(scale(scale(4, ab, cdx, _8), _8, cdx, _16), _16, scale(scale(4, ab, cdy, _8), _8, cdy, _16b), _16b, _32), _32, fin);\n  let det = estimate(finlen, fin);\n  let errbound = iccerrboundB * permanent;\n  if (det >= errbound || -det >= errbound) {\n    return det;\n  }\n  bvirt = ax - adx;\n  adxtail = ax - (adx + bvirt) + (bvirt - dx);\n  bvirt = ay - ady;\n  adytail = ay - (ady + bvirt) + (bvirt - dy);\n  bvirt = bx - bdx;\n  bdxtail = bx - (bdx + bvirt) + (bvirt - dx);\n  bvirt = by - bdy;\n  bdytail = by - (bdy + bvirt) + (bvirt - dy);\n  bvirt = cx - cdx;\n  cdxtail = cx - (cdx + bvirt) + (bvirt - dx);\n  bvirt = cy - cdy;\n  cdytail = cy - (cdy + bvirt) + (bvirt - dy);\n  if (adxtail === 0 && bdxtail === 0 && cdxtail === 0 && adytail === 0 && bdytail === 0 && cdytail === 0) {\n    return det;\n  }\n  errbound = iccerrboundC * permanent + resulterrbound * Math.abs(det);\n  det += (adx * adx + ady * ady) * (bdx * cdytail + cdy * bdxtail - (bdy * cdxtail + cdx * bdytail)) + 2 * (adx * adxtail + ady * adytail) * (bdx * cdy - bdy * cdx) + ((bdx * bdx + bdy * bdy) * (cdx * adytail + ady * cdxtail - (cdy * adxtail + adx * cdytail)) + 2 * (bdx * bdxtail + bdy * bdytail) * (cdx * ady - cdy * adx)) + ((cdx * cdx + cdy * cdy) * (adx * bdytail + bdy * adxtail - (ady * bdxtail + bdx * adytail)) + 2 * (cdx * cdxtail + cdy * cdytail) * (adx * bdy - ady * bdx));\n  if (det >= errbound || -det >= errbound) {\n    return det;\n  }\n  if (bdxtail !== 0 || bdytail !== 0 || cdxtail !== 0 || cdytail !== 0) {\n    s1 = adx * adx;\n    c = splitter * adx;\n    ahi = c - (c - adx);\n    alo = adx - ahi;\n    s0 = alo * alo - (s1 - ahi * ahi - (ahi + ahi) * alo);\n    t1 = ady * ady;\n    c = splitter * ady;\n    ahi = c - (c - ady);\n    alo = ady - ahi;\n    t0 = alo * alo - (t1 - ahi * ahi - (ahi + ahi) * alo);\n    _i = s0 + t0;\n    bvirt = _i - s0;\n    aa[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 + t1;\n    bvirt = _i - _0;\n    aa[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    aa[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    aa[3] = u3;\n  }\n  if (cdxtail !== 0 || cdytail !== 0 || adxtail !== 0 || adytail !== 0) {\n    s1 = bdx * bdx;\n    c = splitter * bdx;\n    ahi = c - (c - bdx);\n    alo = bdx - ahi;\n    s0 = alo * alo - (s1 - ahi * ahi - (ahi + ahi) * alo);\n    t1 = bdy * bdy;\n    c = splitter * bdy;\n    ahi = c - (c - bdy);\n    alo = bdy - ahi;\n    t0 = alo * alo - (t1 - ahi * ahi - (ahi + ahi) * alo);\n    _i = s0 + t0;\n    bvirt = _i - s0;\n    bb[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 + t1;\n    bvirt = _i - _0;\n    bb[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    bb[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    bb[3] = u3;\n  }\n  if (adxtail !== 0 || adytail !== 0 || bdxtail !== 0 || bdytail !== 0) {\n    s1 = cdx * cdx;\n    c = splitter * cdx;\n    ahi = c - (c - cdx);\n    alo = cdx - ahi;\n    s0 = alo * alo - (s1 - ahi * ahi - (ahi + ahi) * alo);\n    t1 = cdy * cdy;\n    c = splitter * cdy;\n    ahi = c - (c - cdy);\n    alo = cdy - ahi;\n    t0 = alo * alo - (t1 - ahi * ahi - (ahi + ahi) * alo);\n    _i = s0 + t0;\n    bvirt = _i - s0;\n    cc[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 + t1;\n    bvirt = _i - _0;\n    cc[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    cc[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    cc[3] = u3;\n  }\n  if (adxtail !== 0) {\n    axtbclen = scale(4, bc, adxtail, axtbc);\n    finlen = finadd(finlen, sum_three(scale(axtbclen, axtbc, 2 * adx, _16), _16, scale(scale(4, cc, adxtail, _8), _8, bdy, _16b), _16b, scale(scale(4, bb, adxtail, _8), _8, -cdy, _16c), _16c, _32, _48), _48);\n  }\n  if (adytail !== 0) {\n    aytbclen = scale(4, bc, adytail, aytbc);\n    finlen = finadd(finlen, sum_three(scale(aytbclen, aytbc, 2 * ady, _16), _16, scale(scale(4, bb, adytail, _8), _8, cdx, _16b), _16b, scale(scale(4, cc, adytail, _8), _8, -bdx, _16c), _16c, _32, _48), _48);\n  }\n  if (bdxtail !== 0) {\n    bxtcalen = scale(4, ca, bdxtail, bxtca);\n    finlen = finadd(finlen, sum_three(scale(bxtcalen, bxtca, 2 * bdx, _16), _16, scale(scale(4, aa, bdxtail, _8), _8, cdy, _16b), _16b, scale(scale(4, cc, bdxtail, _8), _8, -ady, _16c), _16c, _32, _48), _48);\n  }\n  if (bdytail !== 0) {\n    bytcalen = scale(4, ca, bdytail, bytca);\n    finlen = finadd(finlen, sum_three(scale(bytcalen, bytca, 2 * bdy, _16), _16, scale(scale(4, cc, bdytail, _8), _8, adx, _16b), _16b, scale(scale(4, aa, bdytail, _8), _8, -cdx, _16c), _16c, _32, _48), _48);\n  }\n  if (cdxtail !== 0) {\n    cxtablen = scale(4, ab, cdxtail, cxtab);\n    finlen = finadd(finlen, sum_three(scale(cxtablen, cxtab, 2 * cdx, _16), _16, scale(scale(4, bb, cdxtail, _8), _8, ady, _16b), _16b, scale(scale(4, aa, cdxtail, _8), _8, -bdy, _16c), _16c, _32, _48), _48);\n  }\n  if (cdytail !== 0) {\n    cytablen = scale(4, ab, cdytail, cytab);\n    finlen = finadd(finlen, sum_three(scale(cytablen, cytab, 2 * cdy, _16), _16, scale(scale(4, aa, cdytail, _8), _8, bdx, _16b), _16b, scale(scale(4, bb, cdytail, _8), _8, -adx, _16c), _16c, _32, _48), _48);\n  }\n  if (adxtail !== 0 || adytail !== 0) {\n    if (bdxtail !== 0 || bdytail !== 0 || cdxtail !== 0 || cdytail !== 0) {\n      s1 = bdxtail * cdy;\n      c = splitter * bdxtail;\n      ahi = c - (c - bdxtail);\n      alo = bdxtail - ahi;\n      c = splitter * cdy;\n      bhi = c - (c - cdy);\n      blo = cdy - bhi;\n      s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n      t1 = bdx * cdytail;\n      c = splitter * bdx;\n      ahi = c - (c - bdx);\n      alo = bdx - ahi;\n      c = splitter * cdytail;\n      bhi = c - (c - cdytail);\n      blo = cdytail - bhi;\n      t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n      _i = s0 + t0;\n      bvirt = _i - s0;\n      u[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n      _j = s1 + _i;\n      bvirt = _j - s1;\n      _0 = s1 - (_j - bvirt) + (_i - bvirt);\n      _i = _0 + t1;\n      bvirt = _i - _0;\n      u[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n      u3 = _j + _i;\n      bvirt = u3 - _j;\n      u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n      u[3] = u3;\n      s1 = cdxtail * -bdy;\n      c = splitter * cdxtail;\n      ahi = c - (c - cdxtail);\n      alo = cdxtail - ahi;\n      c = splitter * -bdy;\n      bhi = c - (c - -bdy);\n      blo = -bdy - bhi;\n      s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n      t1 = cdx * -bdytail;\n      c = splitter * cdx;\n      ahi = c - (c - cdx);\n      alo = cdx - ahi;\n      c = splitter * -bdytail;\n      bhi = c - (c - -bdytail);\n      blo = -bdytail - bhi;\n      t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n      _i = s0 + t0;\n      bvirt = _i - s0;\n      v[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n      _j = s1 + _i;\n      bvirt = _j - s1;\n      _0 = s1 - (_j - bvirt) + (_i - bvirt);\n      _i = _0 + t1;\n      bvirt = _i - _0;\n      v[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n      u3 = _j + _i;\n      bvirt = u3 - _j;\n      v[2] = _j - (u3 - bvirt) + (_i - bvirt);\n      v[3] = u3;\n      bctlen = sum(4, u, 4, v, bct);\n      s1 = bdxtail * cdytail;\n      c = splitter * bdxtail;\n      ahi = c - (c - bdxtail);\n      alo = bdxtail - ahi;\n      c = splitter * cdytail;\n      bhi = c - (c - cdytail);\n      blo = cdytail - bhi;\n      s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n      t1 = cdxtail * bdytail;\n      c = splitter * cdxtail;\n      ahi = c - (c - cdxtail);\n      alo = cdxtail - ahi;\n      c = splitter * bdytail;\n      bhi = c - (c - bdytail);\n      blo = bdytail - bhi;\n      t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n      _i = s0 - t0;\n      bvirt = s0 - _i;\n      bctt[0] = s0 - (_i + bvirt) + (bvirt - t0);\n      _j = s1 + _i;\n      bvirt = _j - s1;\n      _0 = s1 - (_j - bvirt) + (_i - bvirt);\n      _i = _0 - t1;\n      bvirt = _0 - _i;\n      bctt[1] = _0 - (_i + bvirt) + (bvirt - t1);\n      u3 = _j + _i;\n      bvirt = u3 - _j;\n      bctt[2] = _j - (u3 - bvirt) + (_i - bvirt);\n      bctt[3] = u3;\n      bcttlen = 4;\n    } else {\n      bct[0] = 0;\n      bctlen = 1;\n      bctt[0] = 0;\n      bcttlen = 1;\n    }\n    if (adxtail !== 0) {\n      const len = scale(bctlen, bct, adxtail, _16c);\n      finlen = finadd(finlen, sum(scale(axtbclen, axtbc, adxtail, _16), _16, scale(len, _16c, 2 * adx, _32), _32, _48), _48);\n      const len2 = scale(bcttlen, bctt, adxtail, _8);\n      finlen = finadd(finlen, sum_three(scale(len2, _8, 2 * adx, _16), _16, scale(len2, _8, adxtail, _16b), _16b, scale(len, _16c, adxtail, _32), _32, _32b, _64), _64);\n      if (bdytail !== 0) {\n        finlen = finadd(finlen, scale(scale(4, cc, adxtail, _8), _8, bdytail, _16), _16);\n      }\n      if (cdytail !== 0) {\n        finlen = finadd(finlen, scale(scale(4, bb, -adxtail, _8), _8, cdytail, _16), _16);\n      }\n    }\n    if (adytail !== 0) {\n      const len = scale(bctlen, bct, adytail, _16c);\n      finlen = finadd(finlen, sum(scale(aytbclen, aytbc, adytail, _16), _16, scale(len, _16c, 2 * ady, _32), _32, _48), _48);\n      const len2 = scale(bcttlen, bctt, adytail, _8);\n      finlen = finadd(finlen, sum_three(scale(len2, _8, 2 * ady, _16), _16, scale(len2, _8, adytail, _16b), _16b, scale(len, _16c, adytail, _32), _32, _32b, _64), _64);\n    }\n  }\n  if (bdxtail !== 0 || bdytail !== 0) {\n    if (cdxtail !== 0 || cdytail !== 0 || adxtail !== 0 || adytail !== 0) {\n      s1 = cdxtail * ady;\n      c = splitter * cdxtail;\n      ahi = c - (c - cdxtail);\n      alo = cdxtail - ahi;\n      c = splitter * ady;\n      bhi = c - (c - ady);\n      blo = ady - bhi;\n      s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n      t1 = cdx * adytail;\n      c = splitter * cdx;\n      ahi = c - (c - cdx);\n      alo = cdx - ahi;\n      c = splitter * adytail;\n      bhi = c - (c - adytail);\n      blo = adytail - bhi;\n      t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n      _i = s0 + t0;\n      bvirt = _i - s0;\n      u[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n      _j = s1 + _i;\n      bvirt = _j - s1;\n      _0 = s1 - (_j - bvirt) + (_i - bvirt);\n      _i = _0 + t1;\n      bvirt = _i - _0;\n      u[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n      u3 = _j + _i;\n      bvirt = u3 - _j;\n      u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n      u[3] = u3;\n      n1 = -cdy;\n      n0 = -cdytail;\n      s1 = adxtail * n1;\n      c = splitter * adxtail;\n      ahi = c - (c - adxtail);\n      alo = adxtail - ahi;\n      c = splitter * n1;\n      bhi = c - (c - n1);\n      blo = n1 - bhi;\n      s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n      t1 = adx * n0;\n      c = splitter * adx;\n      ahi = c - (c - adx);\n      alo = adx - ahi;\n      c = splitter * n0;\n      bhi = c - (c - n0);\n      blo = n0 - bhi;\n      t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n      _i = s0 + t0;\n      bvirt = _i - s0;\n      v[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n      _j = s1 + _i;\n      bvirt = _j - s1;\n      _0 = s1 - (_j - bvirt) + (_i - bvirt);\n      _i = _0 + t1;\n      bvirt = _i - _0;\n      v[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n      u3 = _j + _i;\n      bvirt = u3 - _j;\n      v[2] = _j - (u3 - bvirt) + (_i - bvirt);\n      v[3] = u3;\n      catlen = sum(4, u, 4, v, cat);\n      s1 = cdxtail * adytail;\n      c = splitter * cdxtail;\n      ahi = c - (c - cdxtail);\n      alo = cdxtail - ahi;\n      c = splitter * adytail;\n      bhi = c - (c - adytail);\n      blo = adytail - bhi;\n      s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n      t1 = adxtail * cdytail;\n      c = splitter * adxtail;\n      ahi = c - (c - adxtail);\n      alo = adxtail - ahi;\n      c = splitter * cdytail;\n      bhi = c - (c - cdytail);\n      blo = cdytail - bhi;\n      t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n      _i = s0 - t0;\n      bvirt = s0 - _i;\n      catt[0] = s0 - (_i + bvirt) + (bvirt - t0);\n      _j = s1 + _i;\n      bvirt = _j - s1;\n      _0 = s1 - (_j - bvirt) + (_i - bvirt);\n      _i = _0 - t1;\n      bvirt = _0 - _i;\n      catt[1] = _0 - (_i + bvirt) + (bvirt - t1);\n      u3 = _j + _i;\n      bvirt = u3 - _j;\n      catt[2] = _j - (u3 - bvirt) + (_i - bvirt);\n      catt[3] = u3;\n      cattlen = 4;\n    } else {\n      cat[0] = 0;\n      catlen = 1;\n      catt[0] = 0;\n      cattlen = 1;\n    }\n    if (bdxtail !== 0) {\n      const len = scale(catlen, cat, bdxtail, _16c);\n      finlen = finadd(finlen, sum(scale(bxtcalen, bxtca, bdxtail, _16), _16, scale(len, _16c, 2 * bdx, _32), _32, _48), _48);\n      const len2 = scale(cattlen, catt, bdxtail, _8);\n      finlen = finadd(finlen, sum_three(scale(len2, _8, 2 * bdx, _16), _16, scale(len2, _8, bdxtail, _16b), _16b, scale(len, _16c, bdxtail, _32), _32, _32b, _64), _64);\n      if (cdytail !== 0) {\n        finlen = finadd(finlen, scale(scale(4, aa, bdxtail, _8), _8, cdytail, _16), _16);\n      }\n      if (adytail !== 0) {\n        finlen = finadd(finlen, scale(scale(4, cc, -bdxtail, _8), _8, adytail, _16), _16);\n      }\n    }\n    if (bdytail !== 0) {\n      const len = scale(catlen, cat, bdytail, _16c);\n      finlen = finadd(finlen, sum(scale(bytcalen, bytca, bdytail, _16), _16, scale(len, _16c, 2 * bdy, _32), _32, _48), _48);\n      const len2 = scale(cattlen, catt, bdytail, _8);\n      finlen = finadd(finlen, sum_three(scale(len2, _8, 2 * bdy, _16), _16, scale(len2, _8, bdytail, _16b), _16b, scale(len, _16c, bdytail, _32), _32, _32b, _64), _64);\n    }\n  }\n  if (cdxtail !== 0 || cdytail !== 0) {\n    if (adxtail !== 0 || adytail !== 0 || bdxtail !== 0 || bdytail !== 0) {\n      s1 = adxtail * bdy;\n      c = splitter * adxtail;\n      ahi = c - (c - adxtail);\n      alo = adxtail - ahi;\n      c = splitter * bdy;\n      bhi = c - (c - bdy);\n      blo = bdy - bhi;\n      s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n      t1 = adx * bdytail;\n      c = splitter * adx;\n      ahi = c - (c - adx);\n      alo = adx - ahi;\n      c = splitter * bdytail;\n      bhi = c - (c - bdytail);\n      blo = bdytail - bhi;\n      t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n      _i = s0 + t0;\n      bvirt = _i - s0;\n      u[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n      _j = s1 + _i;\n      bvirt = _j - s1;\n      _0 = s1 - (_j - bvirt) + (_i - bvirt);\n      _i = _0 + t1;\n      bvirt = _i - _0;\n      u[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n      u3 = _j + _i;\n      bvirt = u3 - _j;\n      u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n      u[3] = u3;\n      n1 = -ady;\n      n0 = -adytail;\n      s1 = bdxtail * n1;\n      c = splitter * bdxtail;\n      ahi = c - (c - bdxtail);\n      alo = bdxtail - ahi;\n      c = splitter * n1;\n      bhi = c - (c - n1);\n      blo = n1 - bhi;\n      s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n      t1 = bdx * n0;\n      c = splitter * bdx;\n      ahi = c - (c - bdx);\n      alo = bdx - ahi;\n      c = splitter * n0;\n      bhi = c - (c - n0);\n      blo = n0 - bhi;\n      t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n      _i = s0 + t0;\n      bvirt = _i - s0;\n      v[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n      _j = s1 + _i;\n      bvirt = _j - s1;\n      _0 = s1 - (_j - bvirt) + (_i - bvirt);\n      _i = _0 + t1;\n      bvirt = _i - _0;\n      v[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n      u3 = _j + _i;\n      bvirt = u3 - _j;\n      v[2] = _j - (u3 - bvirt) + (_i - bvirt);\n      v[3] = u3;\n      abtlen = sum(4, u, 4, v, abt);\n      s1 = adxtail * bdytail;\n      c = splitter * adxtail;\n      ahi = c - (c - adxtail);\n      alo = adxtail - ahi;\n      c = splitter * bdytail;\n      bhi = c - (c - bdytail);\n      blo = bdytail - bhi;\n      s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n      t1 = bdxtail * adytail;\n      c = splitter * bdxtail;\n      ahi = c - (c - bdxtail);\n      alo = bdxtail - ahi;\n      c = splitter * adytail;\n      bhi = c - (c - adytail);\n      blo = adytail - bhi;\n      t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n      _i = s0 - t0;\n      bvirt = s0 - _i;\n      abtt[0] = s0 - (_i + bvirt) + (bvirt - t0);\n      _j = s1 + _i;\n      bvirt = _j - s1;\n      _0 = s1 - (_j - bvirt) + (_i - bvirt);\n      _i = _0 - t1;\n      bvirt = _0 - _i;\n      abtt[1] = _0 - (_i + bvirt) + (bvirt - t1);\n      u3 = _j + _i;\n      bvirt = u3 - _j;\n      abtt[2] = _j - (u3 - bvirt) + (_i - bvirt);\n      abtt[3] = u3;\n      abttlen = 4;\n    } else {\n      abt[0] = 0;\n      abtlen = 1;\n      abtt[0] = 0;\n      abttlen = 1;\n    }\n    if (cdxtail !== 0) {\n      const len = scale(abtlen, abt, cdxtail, _16c);\n      finlen = finadd(finlen, sum(scale(cxtablen, cxtab, cdxtail, _16), _16, scale(len, _16c, 2 * cdx, _32), _32, _48), _48);\n      const len2 = scale(abttlen, abtt, cdxtail, _8);\n      finlen = finadd(finlen, sum_three(scale(len2, _8, 2 * cdx, _16), _16, scale(len2, _8, cdxtail, _16b), _16b, scale(len, _16c, cdxtail, _32), _32, _32b, _64), _64);\n      if (adytail !== 0) {\n        finlen = finadd(finlen, scale(scale(4, bb, cdxtail, _8), _8, adytail, _16), _16);\n      }\n      if (bdytail !== 0) {\n        finlen = finadd(finlen, scale(scale(4, aa, -cdxtail, _8), _8, bdytail, _16), _16);\n      }\n    }\n    if (cdytail !== 0) {\n      const len = scale(abtlen, abt, cdytail, _16c);\n      finlen = finadd(finlen, sum(scale(cytablen, cytab, cdytail, _16), _16, scale(len, _16c, 2 * cdy, _32), _32, _48), _48);\n      const len2 = scale(abttlen, abtt, cdytail, _8);\n      finlen = finadd(finlen, sum_three(scale(len2, _8, 2 * cdy, _16), _16, scale(len2, _8, cdytail, _16b), _16b, scale(len, _16c, cdytail, _32), _32, _32b, _64), _64);\n    }\n  }\n  return fin[finlen - 1];\n}\nexport function incircle(ax, ay, bx, by, cx, cy, dx, dy) {\n  const adx = ax - dx;\n  const bdx = bx - dx;\n  const cdx = cx - dx;\n  const ady = ay - dy;\n  const bdy = by - dy;\n  const cdy = cy - dy;\n  const bdxcdy = bdx * cdy;\n  const cdxbdy = cdx * bdy;\n  const alift = adx * adx + ady * ady;\n  const cdxady = cdx * ady;\n  const adxcdy = adx * cdy;\n  const blift = bdx * bdx + bdy * bdy;\n  const adxbdy = adx * bdy;\n  const bdxady = bdx * ady;\n  const clift = cdx * cdx + cdy * cdy;\n  const det = alift * (bdxcdy - cdxbdy) + blift * (cdxady - adxcdy) + clift * (adxbdy - bdxady);\n  const permanent = (Math.abs(bdxcdy) + Math.abs(cdxbdy)) * alift + (Math.abs(cdxady) + Math.abs(adxcdy)) * blift + (Math.abs(adxbdy) + Math.abs(bdxady)) * clift;\n  const errbound = iccerrboundA * permanent;\n  if (det > errbound || -det > errbound) {\n    return det;\n  }\n  return incircleadapt(ax, ay, bx, by, cx, cy, dx, dy, permanent);\n}\nexport function incirclefast(ax, ay, bx, by, cx, cy, dx, dy) {\n  const adx = ax - dx;\n  const ady = ay - dy;\n  const bdx = bx - dx;\n  const bdy = by - dy;\n  const cdx = cx - dx;\n  const cdy = cy - dy;\n  const abdet = adx * bdy - bdx * ady;\n  const bcdet = bdx * cdy - cdx * bdy;\n  const cadet = cdx * ady - adx * cdy;\n  const alift = adx * adx + ady * ady;\n  const blift = bdx * bdx + bdy * bdy;\n  const clift = cdx * cdx + cdy * cdy;\n  return alift * bcdet + blift * cadet + clift * abdet;\n}", "map": {"version": 3, "names": ["epsilon", "splitter", "resulterrbound", "estimate", "vec", "sum", "sum_three", "scale", "iccerrboundA", "iccerrboundB", "iccerrboundC", "bc", "ca", "ab", "aa", "bb", "cc", "u", "v", "axtbc", "aytbc", "bxtca", "bytca", "cxtab", "cytab", "abt", "bct", "cat", "abtt", "bctt", "catt", "_8", "_16", "_16b", "_16c", "_32", "_32b", "_48", "_64", "fin", "fin2", "finadd", "finlen", "a", "alen", "tmp", "incircleadapt", "ax", "ay", "bx", "by", "cx", "cy", "dx", "dy", "permanent", "adxtail", "bdxtail", "cdxtail", "adytail", "bdytail", "cdytail", "ax<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bxtcalen", "bytcalen", "cxtablen", "cytablen", "abtlen", "bctlen", "catlen", "abttlen", "bcttlen", "cattlen", "n1", "n0", "bvirt", "c", "ahi", "alo", "bhi", "blo", "_i", "_j", "_0", "s1", "s0", "t1", "t0", "u3", "adx", "bdx", "cdx", "ady", "bdy", "cdy", "det", "errbound", "Math", "abs", "len", "len2", "incircle", "bdxcdy", "cdxbdy", "alift", "cdxady", "adxcdy", "blift", "adxbdy", "bdxady", "clift", "incirclefast", "abdet", "bcdet", "cadet"], "sources": ["C:/console/aava-ui-web/node_modules/robust-predicates/esm/incircle.js"], "sourcesContent": ["import {epsilon, splitter, resulterrbound, estimate, vec, sum, sum_three, scale} from './util.js';\n\nconst iccerrboundA = (10 + 96 * epsilon) * epsilon;\nconst iccerrboundB = (4 + 48 * epsilon) * epsilon;\nconst iccerrboundC = (44 + 576 * epsilon) * epsilon * epsilon;\n\nconst bc = vec(4);\nconst ca = vec(4);\nconst ab = vec(4);\nconst aa = vec(4);\nconst bb = vec(4);\nconst cc = vec(4);\nconst u = vec(4);\nconst v = vec(4);\nconst axtbc = vec(8);\nconst aytbc = vec(8);\nconst bxtca = vec(8);\nconst bytca = vec(8);\nconst cxtab = vec(8);\nconst cytab = vec(8);\nconst abt = vec(8);\nconst bct = vec(8);\nconst cat = vec(8);\nconst abtt = vec(4);\nconst bctt = vec(4);\nconst catt = vec(4);\n\nconst _8 = vec(8);\nconst _16 = vec(16);\nconst _16b = vec(16);\nconst _16c = vec(16);\nconst _32 = vec(32);\nconst _32b = vec(32);\nconst _48 = vec(48);\nconst _64 = vec(64);\n\nlet fin = vec(1152);\nlet fin2 = vec(1152);\n\nfunction finadd(finlen, a, alen) {\n    finlen = sum(finlen, fin, a, alen, fin2);\n    const tmp = fin; fin = fin2; fin2 = tmp;\n    return finlen;\n}\n\nfunction incircleadapt(ax, ay, bx, by, cx, cy, dx, dy, permanent) {\n    let finlen;\n    let adxtail, bdxtail, cdxtail, adytail, bdytail, cdytail;\n    let axtbclen, aytbclen, bxtcalen, bytcalen, cxtablen, cytablen;\n    let abtlen, bctlen, catlen;\n    let abttlen, bcttlen, cattlen;\n    let n1, n0;\n\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _0, s1, s0, t1, t0, u3;\n\n    const adx = ax - dx;\n    const bdx = bx - dx;\n    const cdx = cx - dx;\n    const ady = ay - dy;\n    const bdy = by - dy;\n    const cdy = cy - dy;\n\n    s1 = bdx * cdy;\n    c = splitter * bdx;\n    ahi = c - (c - bdx);\n    alo = bdx - ahi;\n    c = splitter * cdy;\n    bhi = c - (c - cdy);\n    blo = cdy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = cdx * bdy;\n    c = splitter * cdx;\n    ahi = c - (c - cdx);\n    alo = cdx - ahi;\n    c = splitter * bdy;\n    bhi = c - (c - bdy);\n    blo = bdy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    bc[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    bc[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    bc[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    bc[3] = u3;\n    s1 = cdx * ady;\n    c = splitter * cdx;\n    ahi = c - (c - cdx);\n    alo = cdx - ahi;\n    c = splitter * ady;\n    bhi = c - (c - ady);\n    blo = ady - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = adx * cdy;\n    c = splitter * adx;\n    ahi = c - (c - adx);\n    alo = adx - ahi;\n    c = splitter * cdy;\n    bhi = c - (c - cdy);\n    blo = cdy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ca[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ca[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ca[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ca[3] = u3;\n    s1 = adx * bdy;\n    c = splitter * adx;\n    ahi = c - (c - adx);\n    alo = adx - ahi;\n    c = splitter * bdy;\n    bhi = c - (c - bdy);\n    blo = bdy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = bdx * ady;\n    c = splitter * bdx;\n    ahi = c - (c - bdx);\n    alo = bdx - ahi;\n    c = splitter * ady;\n    bhi = c - (c - ady);\n    blo = ady - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ab[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ab[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ab[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ab[3] = u3;\n\n    finlen = sum(\n        sum(\n            sum(\n                scale(scale(4, bc, adx, _8), _8, adx, _16), _16,\n                scale(scale(4, bc, ady, _8), _8, ady, _16b), _16b, _32), _32,\n            sum(\n                scale(scale(4, ca, bdx, _8), _8, bdx, _16), _16,\n                scale(scale(4, ca, bdy, _8), _8, bdy, _16b), _16b, _32b), _32b, _64), _64,\n        sum(\n            scale(scale(4, ab, cdx, _8), _8, cdx, _16), _16,\n            scale(scale(4, ab, cdy, _8), _8, cdy, _16b), _16b, _32), _32, fin);\n\n    let det = estimate(finlen, fin);\n    let errbound = iccerrboundB * permanent;\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n\n    bvirt = ax - adx;\n    adxtail = ax - (adx + bvirt) + (bvirt - dx);\n    bvirt = ay - ady;\n    adytail = ay - (ady + bvirt) + (bvirt - dy);\n    bvirt = bx - bdx;\n    bdxtail = bx - (bdx + bvirt) + (bvirt - dx);\n    bvirt = by - bdy;\n    bdytail = by - (bdy + bvirt) + (bvirt - dy);\n    bvirt = cx - cdx;\n    cdxtail = cx - (cdx + bvirt) + (bvirt - dx);\n    bvirt = cy - cdy;\n    cdytail = cy - (cdy + bvirt) + (bvirt - dy);\n    if (adxtail === 0 && bdxtail === 0 && cdxtail === 0 && adytail === 0 && bdytail === 0 && cdytail === 0) {\n        return det;\n    }\n\n    errbound = iccerrboundC * permanent + resulterrbound * Math.abs(det);\n    det += ((adx * adx + ady * ady) * ((bdx * cdytail + cdy * bdxtail) - (bdy * cdxtail + cdx * bdytail)) +\n        2 * (adx * adxtail + ady * adytail) * (bdx * cdy - bdy * cdx)) +\n        ((bdx * bdx + bdy * bdy) * ((cdx * adytail + ady * cdxtail) - (cdy * adxtail + adx * cdytail)) +\n        2 * (bdx * bdxtail + bdy * bdytail) * (cdx * ady - cdy * adx)) +\n        ((cdx * cdx + cdy * cdy) * ((adx * bdytail + bdy * adxtail) - (ady * bdxtail + bdx * adytail)) +\n        2 * (cdx * cdxtail + cdy * cdytail) * (adx * bdy - ady * bdx));\n\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n\n    if (bdxtail !== 0 || bdytail !== 0 || cdxtail !== 0 || cdytail !== 0) {\n        s1 = adx * adx;\n        c = splitter * adx;\n        ahi = c - (c - adx);\n        alo = adx - ahi;\n        s0 = alo * alo - (s1 - ahi * ahi - (ahi + ahi) * alo);\n        t1 = ady * ady;\n        c = splitter * ady;\n        ahi = c - (c - ady);\n        alo = ady - ahi;\n        t0 = alo * alo - (t1 - ahi * ahi - (ahi + ahi) * alo);\n        _i = s0 + t0;\n        bvirt = _i - s0;\n        aa[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n        _j = s1 + _i;\n        bvirt = _j - s1;\n        _0 = s1 - (_j - bvirt) + (_i - bvirt);\n        _i = _0 + t1;\n        bvirt = _i - _0;\n        aa[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n        u3 = _j + _i;\n        bvirt = u3 - _j;\n        aa[2] = _j - (u3 - bvirt) + (_i - bvirt);\n        aa[3] = u3;\n    }\n    if (cdxtail !== 0 || cdytail !== 0 || adxtail !== 0 || adytail !== 0) {\n        s1 = bdx * bdx;\n        c = splitter * bdx;\n        ahi = c - (c - bdx);\n        alo = bdx - ahi;\n        s0 = alo * alo - (s1 - ahi * ahi - (ahi + ahi) * alo);\n        t1 = bdy * bdy;\n        c = splitter * bdy;\n        ahi = c - (c - bdy);\n        alo = bdy - ahi;\n        t0 = alo * alo - (t1 - ahi * ahi - (ahi + ahi) * alo);\n        _i = s0 + t0;\n        bvirt = _i - s0;\n        bb[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n        _j = s1 + _i;\n        bvirt = _j - s1;\n        _0 = s1 - (_j - bvirt) + (_i - bvirt);\n        _i = _0 + t1;\n        bvirt = _i - _0;\n        bb[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n        u3 = _j + _i;\n        bvirt = u3 - _j;\n        bb[2] = _j - (u3 - bvirt) + (_i - bvirt);\n        bb[3] = u3;\n    }\n    if (adxtail !== 0 || adytail !== 0 || bdxtail !== 0 || bdytail !== 0) {\n        s1 = cdx * cdx;\n        c = splitter * cdx;\n        ahi = c - (c - cdx);\n        alo = cdx - ahi;\n        s0 = alo * alo - (s1 - ahi * ahi - (ahi + ahi) * alo);\n        t1 = cdy * cdy;\n        c = splitter * cdy;\n        ahi = c - (c - cdy);\n        alo = cdy - ahi;\n        t0 = alo * alo - (t1 - ahi * ahi - (ahi + ahi) * alo);\n        _i = s0 + t0;\n        bvirt = _i - s0;\n        cc[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n        _j = s1 + _i;\n        bvirt = _j - s1;\n        _0 = s1 - (_j - bvirt) + (_i - bvirt);\n        _i = _0 + t1;\n        bvirt = _i - _0;\n        cc[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n        u3 = _j + _i;\n        bvirt = u3 - _j;\n        cc[2] = _j - (u3 - bvirt) + (_i - bvirt);\n        cc[3] = u3;\n    }\n\n    if (adxtail !== 0) {\n        axtbclen = scale(4, bc, adxtail, axtbc);\n        finlen = finadd(finlen, sum_three(\n            scale(axtbclen, axtbc, 2 * adx, _16), _16,\n            scale(scale(4, cc, adxtail, _8), _8, bdy, _16b), _16b,\n            scale(scale(4, bb, adxtail, _8), _8, -cdy, _16c), _16c, _32, _48), _48);\n    }\n    if (adytail !== 0) {\n        aytbclen = scale(4, bc, adytail, aytbc);\n        finlen = finadd(finlen, sum_three(\n            scale(aytbclen, aytbc, 2 * ady, _16), _16,\n            scale(scale(4, bb, adytail, _8), _8, cdx, _16b), _16b,\n            scale(scale(4, cc, adytail, _8), _8, -bdx, _16c), _16c, _32, _48), _48);\n    }\n    if (bdxtail !== 0) {\n        bxtcalen = scale(4, ca, bdxtail, bxtca);\n        finlen = finadd(finlen, sum_three(\n            scale(bxtcalen, bxtca, 2 * bdx, _16), _16,\n            scale(scale(4, aa, bdxtail, _8), _8, cdy, _16b), _16b,\n            scale(scale(4, cc, bdxtail, _8), _8, -ady, _16c), _16c, _32, _48), _48);\n    }\n    if (bdytail !== 0) {\n        bytcalen = scale(4, ca, bdytail, bytca);\n        finlen = finadd(finlen, sum_three(\n            scale(bytcalen, bytca, 2 * bdy, _16), _16,\n            scale(scale(4, cc, bdytail, _8), _8, adx, _16b), _16b,\n            scale(scale(4, aa, bdytail, _8), _8, -cdx, _16c), _16c, _32, _48), _48);\n    }\n    if (cdxtail !== 0) {\n        cxtablen = scale(4, ab, cdxtail, cxtab);\n        finlen = finadd(finlen, sum_three(\n            scale(cxtablen, cxtab, 2 * cdx, _16), _16,\n            scale(scale(4, bb, cdxtail, _8), _8, ady, _16b), _16b,\n            scale(scale(4, aa, cdxtail, _8), _8, -bdy, _16c), _16c, _32, _48), _48);\n    }\n    if (cdytail !== 0) {\n        cytablen = scale(4, ab, cdytail, cytab);\n        finlen = finadd(finlen, sum_three(\n            scale(cytablen, cytab, 2 * cdy, _16), _16,\n            scale(scale(4, aa, cdytail, _8), _8, bdx, _16b), _16b,\n            scale(scale(4, bb, cdytail, _8), _8, -adx, _16c), _16c, _32, _48), _48);\n    }\n\n    if (adxtail !== 0 || adytail !== 0) {\n        if (bdxtail !== 0 || bdytail !== 0 || cdxtail !== 0 || cdytail !== 0) {\n            s1 = bdxtail * cdy;\n            c = splitter * bdxtail;\n            ahi = c - (c - bdxtail);\n            alo = bdxtail - ahi;\n            c = splitter * cdy;\n            bhi = c - (c - cdy);\n            blo = cdy - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = bdx * cdytail;\n            c = splitter * bdx;\n            ahi = c - (c - bdx);\n            alo = bdx - ahi;\n            c = splitter * cdytail;\n            bhi = c - (c - cdytail);\n            blo = cdytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 + t0;\n            bvirt = _i - s0;\n            u[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 + t1;\n            bvirt = _i - _0;\n            u[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            u[3] = u3;\n            s1 = cdxtail * -bdy;\n            c = splitter * cdxtail;\n            ahi = c - (c - cdxtail);\n            alo = cdxtail - ahi;\n            c = splitter * -bdy;\n            bhi = c - (c - -bdy);\n            blo = -bdy - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = cdx * -bdytail;\n            c = splitter * cdx;\n            ahi = c - (c - cdx);\n            alo = cdx - ahi;\n            c = splitter * -bdytail;\n            bhi = c - (c - -bdytail);\n            blo = -bdytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 + t0;\n            bvirt = _i - s0;\n            v[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 + t1;\n            bvirt = _i - _0;\n            v[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            v[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            v[3] = u3;\n            bctlen = sum(4, u, 4, v, bct);\n            s1 = bdxtail * cdytail;\n            c = splitter * bdxtail;\n            ahi = c - (c - bdxtail);\n            alo = bdxtail - ahi;\n            c = splitter * cdytail;\n            bhi = c - (c - cdytail);\n            blo = cdytail - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = cdxtail * bdytail;\n            c = splitter * cdxtail;\n            ahi = c - (c - cdxtail);\n            alo = cdxtail - ahi;\n            c = splitter * bdytail;\n            bhi = c - (c - bdytail);\n            blo = bdytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 - t0;\n            bvirt = s0 - _i;\n            bctt[0] = s0 - (_i + bvirt) + (bvirt - t0);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 - t1;\n            bvirt = _0 - _i;\n            bctt[1] = _0 - (_i + bvirt) + (bvirt - t1);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            bctt[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            bctt[3] = u3;\n            bcttlen = 4;\n        } else {\n            bct[0] = 0;\n            bctlen = 1;\n            bctt[0] = 0;\n            bcttlen = 1;\n        }\n        if (adxtail !== 0) {\n            const len = scale(bctlen, bct, adxtail, _16c);\n            finlen = finadd(finlen, sum(\n                scale(axtbclen, axtbc, adxtail, _16), _16,\n                scale(len, _16c, 2 * adx, _32), _32, _48), _48);\n\n            const len2 = scale(bcttlen, bctt, adxtail, _8);\n            finlen = finadd(finlen, sum_three(\n                scale(len2, _8, 2 * adx, _16), _16,\n                scale(len2, _8, adxtail, _16b), _16b,\n                scale(len, _16c, adxtail, _32), _32, _32b, _64), _64);\n\n            if (bdytail !== 0) {\n                finlen = finadd(finlen, scale(scale(4, cc, adxtail, _8), _8, bdytail, _16), _16);\n            }\n            if (cdytail !== 0) {\n                finlen = finadd(finlen, scale(scale(4, bb, -adxtail, _8), _8, cdytail, _16), _16);\n            }\n        }\n        if (adytail !== 0) {\n            const len = scale(bctlen, bct, adytail, _16c);\n            finlen = finadd(finlen, sum(\n                scale(aytbclen, aytbc, adytail, _16), _16,\n                scale(len, _16c, 2 * ady, _32), _32, _48), _48);\n\n            const len2 = scale(bcttlen, bctt, adytail, _8);\n            finlen = finadd(finlen, sum_three(\n                scale(len2, _8, 2 * ady, _16), _16,\n                scale(len2, _8, adytail, _16b), _16b,\n                scale(len, _16c, adytail, _32), _32, _32b, _64), _64);\n        }\n    }\n    if (bdxtail !== 0 || bdytail !== 0) {\n        if (cdxtail !== 0 || cdytail !== 0 || adxtail !== 0 || adytail !== 0) {\n            s1 = cdxtail * ady;\n            c = splitter * cdxtail;\n            ahi = c - (c - cdxtail);\n            alo = cdxtail - ahi;\n            c = splitter * ady;\n            bhi = c - (c - ady);\n            blo = ady - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = cdx * adytail;\n            c = splitter * cdx;\n            ahi = c - (c - cdx);\n            alo = cdx - ahi;\n            c = splitter * adytail;\n            bhi = c - (c - adytail);\n            blo = adytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 + t0;\n            bvirt = _i - s0;\n            u[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 + t1;\n            bvirt = _i - _0;\n            u[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            u[3] = u3;\n            n1 = -cdy;\n            n0 = -cdytail;\n            s1 = adxtail * n1;\n            c = splitter * adxtail;\n            ahi = c - (c - adxtail);\n            alo = adxtail - ahi;\n            c = splitter * n1;\n            bhi = c - (c - n1);\n            blo = n1 - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = adx * n0;\n            c = splitter * adx;\n            ahi = c - (c - adx);\n            alo = adx - ahi;\n            c = splitter * n0;\n            bhi = c - (c - n0);\n            blo = n0 - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 + t0;\n            bvirt = _i - s0;\n            v[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 + t1;\n            bvirt = _i - _0;\n            v[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            v[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            v[3] = u3;\n            catlen = sum(4, u, 4, v, cat);\n            s1 = cdxtail * adytail;\n            c = splitter * cdxtail;\n            ahi = c - (c - cdxtail);\n            alo = cdxtail - ahi;\n            c = splitter * adytail;\n            bhi = c - (c - adytail);\n            blo = adytail - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = adxtail * cdytail;\n            c = splitter * adxtail;\n            ahi = c - (c - adxtail);\n            alo = adxtail - ahi;\n            c = splitter * cdytail;\n            bhi = c - (c - cdytail);\n            blo = cdytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 - t0;\n            bvirt = s0 - _i;\n            catt[0] = s0 - (_i + bvirt) + (bvirt - t0);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 - t1;\n            bvirt = _0 - _i;\n            catt[1] = _0 - (_i + bvirt) + (bvirt - t1);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            catt[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            catt[3] = u3;\n            cattlen = 4;\n        } else {\n            cat[0] = 0;\n            catlen = 1;\n            catt[0] = 0;\n            cattlen = 1;\n        }\n        if (bdxtail !== 0) {\n            const len = scale(catlen, cat, bdxtail, _16c);\n            finlen = finadd(finlen, sum(\n                scale(bxtcalen, bxtca, bdxtail, _16), _16,\n                scale(len, _16c, 2 * bdx, _32), _32, _48), _48);\n\n            const len2 = scale(cattlen, catt, bdxtail, _8);\n            finlen = finadd(finlen, sum_three(\n                scale(len2, _8, 2 * bdx, _16), _16,\n                scale(len2, _8, bdxtail, _16b), _16b,\n                scale(len, _16c, bdxtail, _32), _32, _32b, _64), _64);\n\n            if (cdytail !== 0) {\n                finlen = finadd(finlen, scale(scale(4, aa, bdxtail, _8), _8, cdytail, _16), _16);\n            }\n            if (adytail !== 0) {\n                finlen = finadd(finlen, scale(scale(4, cc, -bdxtail, _8), _8, adytail, _16), _16);\n            }\n        }\n        if (bdytail !== 0) {\n            const len = scale(catlen, cat, bdytail, _16c);\n            finlen = finadd(finlen, sum(\n                scale(bytcalen, bytca, bdytail, _16), _16,\n                scale(len, _16c, 2 * bdy, _32), _32, _48), _48);\n\n            const len2 = scale(cattlen, catt, bdytail, _8);\n            finlen = finadd(finlen, sum_three(\n                scale(len2, _8, 2 * bdy, _16), _16,\n                scale(len2, _8, bdytail, _16b), _16b,\n                scale(len, _16c, bdytail, _32), _32,  _32b, _64), _64);\n        }\n    }\n    if (cdxtail !== 0 || cdytail !== 0) {\n        if (adxtail !== 0 || adytail !== 0 || bdxtail !== 0 || bdytail !== 0) {\n            s1 = adxtail * bdy;\n            c = splitter * adxtail;\n            ahi = c - (c - adxtail);\n            alo = adxtail - ahi;\n            c = splitter * bdy;\n            bhi = c - (c - bdy);\n            blo = bdy - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = adx * bdytail;\n            c = splitter * adx;\n            ahi = c - (c - adx);\n            alo = adx - ahi;\n            c = splitter * bdytail;\n            bhi = c - (c - bdytail);\n            blo = bdytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 + t0;\n            bvirt = _i - s0;\n            u[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 + t1;\n            bvirt = _i - _0;\n            u[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            u[3] = u3;\n            n1 = -ady;\n            n0 = -adytail;\n            s1 = bdxtail * n1;\n            c = splitter * bdxtail;\n            ahi = c - (c - bdxtail);\n            alo = bdxtail - ahi;\n            c = splitter * n1;\n            bhi = c - (c - n1);\n            blo = n1 - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = bdx * n0;\n            c = splitter * bdx;\n            ahi = c - (c - bdx);\n            alo = bdx - ahi;\n            c = splitter * n0;\n            bhi = c - (c - n0);\n            blo = n0 - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 + t0;\n            bvirt = _i - s0;\n            v[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 + t1;\n            bvirt = _i - _0;\n            v[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            v[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            v[3] = u3;\n            abtlen = sum(4, u, 4, v, abt);\n            s1 = adxtail * bdytail;\n            c = splitter * adxtail;\n            ahi = c - (c - adxtail);\n            alo = adxtail - ahi;\n            c = splitter * bdytail;\n            bhi = c - (c - bdytail);\n            blo = bdytail - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = bdxtail * adytail;\n            c = splitter * bdxtail;\n            ahi = c - (c - bdxtail);\n            alo = bdxtail - ahi;\n            c = splitter * adytail;\n            bhi = c - (c - adytail);\n            blo = adytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 - t0;\n            bvirt = s0 - _i;\n            abtt[0] = s0 - (_i + bvirt) + (bvirt - t0);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 - t1;\n            bvirt = _0 - _i;\n            abtt[1] = _0 - (_i + bvirt) + (bvirt - t1);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            abtt[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            abtt[3] = u3;\n            abttlen = 4;\n        } else {\n            abt[0] = 0;\n            abtlen = 1;\n            abtt[0] = 0;\n            abttlen = 1;\n        }\n        if (cdxtail !== 0) {\n            const len = scale(abtlen, abt, cdxtail, _16c);\n            finlen = finadd(finlen, sum(\n                scale(cxtablen, cxtab, cdxtail, _16), _16,\n                scale(len, _16c, 2 * cdx, _32), _32, _48), _48);\n\n            const len2 = scale(abttlen, abtt, cdxtail, _8);\n            finlen = finadd(finlen, sum_three(\n                scale(len2, _8, 2 * cdx, _16), _16,\n                scale(len2, _8, cdxtail, _16b), _16b,\n                scale(len, _16c, cdxtail, _32), _32, _32b, _64), _64);\n\n            if (adytail !== 0) {\n                finlen = finadd(finlen, scale(scale(4, bb, cdxtail, _8), _8, adytail, _16), _16);\n            }\n            if (bdytail !== 0) {\n                finlen = finadd(finlen, scale(scale(4, aa, -cdxtail, _8), _8, bdytail, _16), _16);\n            }\n        }\n        if (cdytail !== 0) {\n            const len = scale(abtlen, abt, cdytail, _16c);\n            finlen = finadd(finlen, sum(\n                scale(cytablen, cytab, cdytail, _16), _16,\n                scale(len, _16c, 2 * cdy, _32), _32, _48), _48);\n\n            const len2 = scale(abttlen, abtt, cdytail, _8);\n            finlen = finadd(finlen, sum_three(\n                scale(len2, _8, 2 * cdy, _16), _16,\n                scale(len2, _8, cdytail, _16b), _16b,\n                scale(len, _16c, cdytail, _32), _32, _32b, _64), _64);\n        }\n    }\n\n    return fin[finlen - 1];\n}\n\nexport function incircle(ax, ay, bx, by, cx, cy, dx, dy) {\n    const adx = ax - dx;\n    const bdx = bx - dx;\n    const cdx = cx - dx;\n    const ady = ay - dy;\n    const bdy = by - dy;\n    const cdy = cy - dy;\n\n    const bdxcdy = bdx * cdy;\n    const cdxbdy = cdx * bdy;\n    const alift = adx * adx + ady * ady;\n\n    const cdxady = cdx * ady;\n    const adxcdy = adx * cdy;\n    const blift = bdx * bdx + bdy * bdy;\n\n    const adxbdy = adx * bdy;\n    const bdxady = bdx * ady;\n    const clift = cdx * cdx + cdy * cdy;\n\n    const det =\n        alift * (bdxcdy - cdxbdy) +\n        blift * (cdxady - adxcdy) +\n        clift * (adxbdy - bdxady);\n\n    const permanent =\n        (Math.abs(bdxcdy) + Math.abs(cdxbdy)) * alift +\n        (Math.abs(cdxady) + Math.abs(adxcdy)) * blift +\n        (Math.abs(adxbdy) + Math.abs(bdxady)) * clift;\n\n    const errbound = iccerrboundA * permanent;\n\n    if (det > errbound || -det > errbound) {\n        return det;\n    }\n    return incircleadapt(ax, ay, bx, by, cx, cy, dx, dy, permanent);\n}\n\nexport function incirclefast(ax, ay, bx, by, cx, cy, dx, dy) {\n    const adx = ax - dx;\n    const ady = ay - dy;\n    const bdx = bx - dx;\n    const bdy = by - dy;\n    const cdx = cx - dx;\n    const cdy = cy - dy;\n\n    const abdet = adx * bdy - bdx * ady;\n    const bcdet = bdx * cdy - cdx * bdy;\n    const cadet = cdx * ady - adx * cdy;\n    const alift = adx * adx + ady * ady;\n    const blift = bdx * bdx + bdy * bdy;\n    const clift = cdx * cdx + cdy * cdy;\n\n    return alift * bcdet + blift * cadet + clift * abdet;\n}\n"], "mappings": "AAAA,SAAQA,OAAO,EAAEC,QAAQ,EAAEC,cAAc,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,GAAG,EAAEC,SAAS,EAAEC,KAAK,QAAO,WAAW;AAEjG,MAAMC,YAAY,GAAG,CAAC,EAAE,GAAG,EAAE,GAAGR,OAAO,IAAIA,OAAO;AAClD,MAAMS,YAAY,GAAG,CAAC,CAAC,GAAG,EAAE,GAAGT,OAAO,IAAIA,OAAO;AACjD,MAAMU,YAAY,GAAG,CAAC,EAAE,GAAG,GAAG,GAAGV,OAAO,IAAIA,OAAO,GAAGA,OAAO;AAE7D,MAAMW,EAAE,GAAGP,GAAG,CAAC,CAAC,CAAC;AACjB,MAAMQ,EAAE,GAAGR,GAAG,CAAC,CAAC,CAAC;AACjB,MAAMS,EAAE,GAAGT,GAAG,CAAC,CAAC,CAAC;AACjB,MAAMU,EAAE,GAAGV,GAAG,CAAC,CAAC,CAAC;AACjB,MAAMW,EAAE,GAAGX,GAAG,CAAC,CAAC,CAAC;AACjB,MAAMY,EAAE,GAAGZ,GAAG,CAAC,CAAC,CAAC;AACjB,MAAMa,CAAC,GAAGb,GAAG,CAAC,CAAC,CAAC;AAChB,MAAMc,CAAC,GAAGd,GAAG,CAAC,CAAC,CAAC;AAChB,MAAMe,KAAK,GAAGf,GAAG,CAAC,CAAC,CAAC;AACpB,MAAMgB,KAAK,GAAGhB,GAAG,CAAC,CAAC,CAAC;AACpB,MAAMiB,KAAK,GAAGjB,GAAG,CAAC,CAAC,CAAC;AACpB,MAAMkB,KAAK,GAAGlB,GAAG,CAAC,CAAC,CAAC;AACpB,MAAMmB,KAAK,GAAGnB,GAAG,CAAC,CAAC,CAAC;AACpB,MAAMoB,KAAK,GAAGpB,GAAG,CAAC,CAAC,CAAC;AACpB,MAAMqB,GAAG,GAAGrB,GAAG,CAAC,CAAC,CAAC;AAClB,MAAMsB,GAAG,GAAGtB,GAAG,CAAC,CAAC,CAAC;AAClB,MAAMuB,GAAG,GAAGvB,GAAG,CAAC,CAAC,CAAC;AAClB,MAAMwB,IAAI,GAAGxB,GAAG,CAAC,CAAC,CAAC;AACnB,MAAMyB,IAAI,GAAGzB,GAAG,CAAC,CAAC,CAAC;AACnB,MAAM0B,IAAI,GAAG1B,GAAG,CAAC,CAAC,CAAC;AAEnB,MAAM2B,EAAE,GAAG3B,GAAG,CAAC,CAAC,CAAC;AACjB,MAAM4B,GAAG,GAAG5B,GAAG,CAAC,EAAE,CAAC;AACnB,MAAM6B,IAAI,GAAG7B,GAAG,CAAC,EAAE,CAAC;AACpB,MAAM8B,IAAI,GAAG9B,GAAG,CAAC,EAAE,CAAC;AACpB,MAAM+B,GAAG,GAAG/B,GAAG,CAAC,EAAE,CAAC;AACnB,MAAMgC,IAAI,GAAGhC,GAAG,CAAC,EAAE,CAAC;AACpB,MAAMiC,GAAG,GAAGjC,GAAG,CAAC,EAAE,CAAC;AACnB,MAAMkC,GAAG,GAAGlC,GAAG,CAAC,EAAE,CAAC;AAEnB,IAAImC,GAAG,GAAGnC,GAAG,CAAC,IAAI,CAAC;AACnB,IAAIoC,IAAI,GAAGpC,GAAG,CAAC,IAAI,CAAC;AAEpB,SAASqC,MAAMA,CAACC,MAAM,EAAEC,CAAC,EAAEC,IAAI,EAAE;EAC7BF,MAAM,GAAGrC,GAAG,CAACqC,MAAM,EAAEH,GAAG,EAAEI,CAAC,EAAEC,IAAI,EAAEJ,IAAI,CAAC;EACxC,MAAMK,GAAG,GAAGN,GAAG;EAAEA,GAAG,GAAGC,IAAI;EAAEA,IAAI,GAAGK,GAAG;EACvC,OAAOH,MAAM;AACjB;AAEA,SAASI,aAAaA,CAACC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,SAAS,EAAE;EAC9D,IAAIb,MAAM;EACV,IAAIc,OAAO,EAAEC,OAAO,EAAEC,OAAO,EAAEC,OAAO,EAAEC,OAAO,EAAEC,OAAO;EACxD,IAAIC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ;EAC9D,IAAIC,MAAM,EAAEC,MAAM,EAAEC,MAAM;EAC1B,IAAIC,OAAO,EAAEC,OAAO,EAAEC,OAAO;EAC7B,IAAIC,EAAE,EAAEC,EAAE;EAEV,IAAIC,KAAK,EAAEC,CAAC,EAAEC,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;EAEhE,MAAMC,GAAG,GAAG3C,EAAE,GAAGM,EAAE;EACnB,MAAMsC,GAAG,GAAG1C,EAAE,GAAGI,EAAE;EACnB,MAAMuC,GAAG,GAAGzC,EAAE,GAAGE,EAAE;EACnB,MAAMwC,GAAG,GAAG7C,EAAE,GAAGM,EAAE;EACnB,MAAMwC,GAAG,GAAG5C,EAAE,GAAGI,EAAE;EACnB,MAAMyC,GAAG,GAAG3C,EAAE,GAAGE,EAAE;EAEnB+B,EAAE,GAAGM,GAAG,GAAGI,GAAG;EACdlB,CAAC,GAAG5E,QAAQ,GAAG0F,GAAG;EAClBb,GAAG,GAAGD,CAAC,IAAIA,CAAC,GAAGc,GAAG,CAAC;EACnBZ,GAAG,GAAGY,GAAG,GAAGb,GAAG;EACfD,CAAC,GAAG5E,QAAQ,GAAG8F,GAAG;EAClBf,GAAG,GAAGH,CAAC,IAAIA,CAAC,GAAGkB,GAAG,CAAC;EACnBd,GAAG,GAAGc,GAAG,GAAGf,GAAG;EACfM,EAAE,GAAGP,GAAG,GAAGE,GAAG,IAAII,EAAE,GAAGP,GAAG,GAAGE,GAAG,GAAGD,GAAG,GAAGC,GAAG,GAAGF,GAAG,GAAGG,GAAG,CAAC;EACzDM,EAAE,GAAGK,GAAG,GAAGE,GAAG;EACdjB,CAAC,GAAG5E,QAAQ,GAAG2F,GAAG;EAClBd,GAAG,GAAGD,CAAC,IAAIA,CAAC,GAAGe,GAAG,CAAC;EACnBb,GAAG,GAAGa,GAAG,GAAGd,GAAG;EACfD,CAAC,GAAG5E,QAAQ,GAAG6F,GAAG;EAClBd,GAAG,GAAGH,CAAC,IAAIA,CAAC,GAAGiB,GAAG,CAAC;EACnBb,GAAG,GAAGa,GAAG,GAAGd,GAAG;EACfQ,EAAE,GAAGT,GAAG,GAAGE,GAAG,IAAIM,EAAE,GAAGT,GAAG,GAAGE,GAAG,GAAGD,GAAG,GAAGC,GAAG,GAAGF,GAAG,GAAGG,GAAG,CAAC;EACzDC,EAAE,GAAGI,EAAE,GAAGE,EAAE;EACZZ,KAAK,GAAGU,EAAE,GAAGJ,EAAE;EACfvE,EAAE,CAAC,CAAC,CAAC,GAAG2E,EAAE,IAAIJ,EAAE,GAAGN,KAAK,CAAC,IAAIA,KAAK,GAAGY,EAAE,CAAC;EACxCL,EAAE,GAAGE,EAAE,GAAGH,EAAE;EACZN,KAAK,GAAGO,EAAE,GAAGE,EAAE;EACfD,EAAE,GAAGC,EAAE,IAAIF,EAAE,GAAGP,KAAK,CAAC,IAAIM,EAAE,GAAGN,KAAK,CAAC;EACrCM,EAAE,GAAGE,EAAE,GAAGG,EAAE;EACZX,KAAK,GAAGQ,EAAE,GAAGF,EAAE;EACfvE,EAAE,CAAC,CAAC,CAAC,GAAGyE,EAAE,IAAIF,EAAE,GAAGN,KAAK,CAAC,IAAIA,KAAK,GAAGW,EAAE,CAAC;EACxCE,EAAE,GAAGN,EAAE,GAAGD,EAAE;EACZN,KAAK,GAAGa,EAAE,GAAGN,EAAE;EACfxE,EAAE,CAAC,CAAC,CAAC,GAAGwE,EAAE,IAAIM,EAAE,GAAGb,KAAK,CAAC,IAAIM,EAAE,GAAGN,KAAK,CAAC;EACxCjE,EAAE,CAAC,CAAC,CAAC,GAAG8E,EAAE;EACVJ,EAAE,GAAGO,GAAG,GAAGC,GAAG;EACdhB,CAAC,GAAG5E,QAAQ,GAAG2F,GAAG;EAClBd,GAAG,GAAGD,CAAC,IAAIA,CAAC,GAAGe,GAAG,CAAC;EACnBb,GAAG,GAAGa,GAAG,GAAGd,GAAG;EACfD,CAAC,GAAG5E,QAAQ,GAAG4F,GAAG;EAClBb,GAAG,GAAGH,CAAC,IAAIA,CAAC,GAAGgB,GAAG,CAAC;EACnBZ,GAAG,GAAGY,GAAG,GAAGb,GAAG;EACfM,EAAE,GAAGP,GAAG,GAAGE,GAAG,IAAII,EAAE,GAAGP,GAAG,GAAGE,GAAG,GAAGD,GAAG,GAAGC,GAAG,GAAGF,GAAG,GAAGG,GAAG,CAAC;EACzDM,EAAE,GAAGG,GAAG,GAAGK,GAAG;EACdlB,CAAC,GAAG5E,QAAQ,GAAGyF,GAAG;EAClBZ,GAAG,GAAGD,CAAC,IAAIA,CAAC,GAAGa,GAAG,CAAC;EACnBX,GAAG,GAAGW,GAAG,GAAGZ,GAAG;EACfD,CAAC,GAAG5E,QAAQ,GAAG8F,GAAG;EAClBf,GAAG,GAAGH,CAAC,IAAIA,CAAC,GAAGkB,GAAG,CAAC;EACnBd,GAAG,GAAGc,GAAG,GAAGf,GAAG;EACfQ,EAAE,GAAGT,GAAG,GAAGE,GAAG,IAAIM,EAAE,GAAGT,GAAG,GAAGE,GAAG,GAAGD,GAAG,GAAGC,GAAG,GAAGF,GAAG,GAAGG,GAAG,CAAC;EACzDC,EAAE,GAAGI,EAAE,GAAGE,EAAE;EACZZ,KAAK,GAAGU,EAAE,GAAGJ,EAAE;EACftE,EAAE,CAAC,CAAC,CAAC,GAAG0E,EAAE,IAAIJ,EAAE,GAAGN,KAAK,CAAC,IAAIA,KAAK,GAAGY,EAAE,CAAC;EACxCL,EAAE,GAAGE,EAAE,GAAGH,EAAE;EACZN,KAAK,GAAGO,EAAE,GAAGE,EAAE;EACfD,EAAE,GAAGC,EAAE,IAAIF,EAAE,GAAGP,KAAK,CAAC,IAAIM,EAAE,GAAGN,KAAK,CAAC;EACrCM,EAAE,GAAGE,EAAE,GAAGG,EAAE;EACZX,KAAK,GAAGQ,EAAE,GAAGF,EAAE;EACftE,EAAE,CAAC,CAAC,CAAC,GAAGwE,EAAE,IAAIF,EAAE,GAAGN,KAAK,CAAC,IAAIA,KAAK,GAAGW,EAAE,CAAC;EACxCE,EAAE,GAAGN,EAAE,GAAGD,EAAE;EACZN,KAAK,GAAGa,EAAE,GAAGN,EAAE;EACfvE,EAAE,CAAC,CAAC,CAAC,GAAGuE,EAAE,IAAIM,EAAE,GAAGb,KAAK,CAAC,IAAIM,EAAE,GAAGN,KAAK,CAAC;EACxChE,EAAE,CAAC,CAAC,CAAC,GAAG6E,EAAE;EACVJ,EAAE,GAAGK,GAAG,GAAGI,GAAG;EACdjB,CAAC,GAAG5E,QAAQ,GAAGyF,GAAG;EAClBZ,GAAG,GAAGD,CAAC,IAAIA,CAAC,GAAGa,GAAG,CAAC;EACnBX,GAAG,GAAGW,GAAG,GAAGZ,GAAG;EACfD,CAAC,GAAG5E,QAAQ,GAAG6F,GAAG;EAClBd,GAAG,GAAGH,CAAC,IAAIA,CAAC,GAAGiB,GAAG,CAAC;EACnBb,GAAG,GAAGa,GAAG,GAAGd,GAAG;EACfM,EAAE,GAAGP,GAAG,GAAGE,GAAG,IAAII,EAAE,GAAGP,GAAG,GAAGE,GAAG,GAAGD,GAAG,GAAGC,GAAG,GAAGF,GAAG,GAAGG,GAAG,CAAC;EACzDM,EAAE,GAAGI,GAAG,GAAGE,GAAG;EACdhB,CAAC,GAAG5E,QAAQ,GAAG0F,GAAG;EAClBb,GAAG,GAAGD,CAAC,IAAIA,CAAC,GAAGc,GAAG,CAAC;EACnBZ,GAAG,GAAGY,GAAG,GAAGb,GAAG;EACfD,CAAC,GAAG5E,QAAQ,GAAG4F,GAAG;EAClBb,GAAG,GAAGH,CAAC,IAAIA,CAAC,GAAGgB,GAAG,CAAC;EACnBZ,GAAG,GAAGY,GAAG,GAAGb,GAAG;EACfQ,EAAE,GAAGT,GAAG,GAAGE,GAAG,IAAIM,EAAE,GAAGT,GAAG,GAAGE,GAAG,GAAGD,GAAG,GAAGC,GAAG,GAAGF,GAAG,GAAGG,GAAG,CAAC;EACzDC,EAAE,GAAGI,EAAE,GAAGE,EAAE;EACZZ,KAAK,GAAGU,EAAE,GAAGJ,EAAE;EACfrE,EAAE,CAAC,CAAC,CAAC,GAAGyE,EAAE,IAAIJ,EAAE,GAAGN,KAAK,CAAC,IAAIA,KAAK,GAAGY,EAAE,CAAC;EACxCL,EAAE,GAAGE,EAAE,GAAGH,EAAE;EACZN,KAAK,GAAGO,EAAE,GAAGE,EAAE;EACfD,EAAE,GAAGC,EAAE,IAAIF,EAAE,GAAGP,KAAK,CAAC,IAAIM,EAAE,GAAGN,KAAK,CAAC;EACrCM,EAAE,GAAGE,EAAE,GAAGG,EAAE;EACZX,KAAK,GAAGQ,EAAE,GAAGF,EAAE;EACfrE,EAAE,CAAC,CAAC,CAAC,GAAGuE,EAAE,IAAIF,EAAE,GAAGN,KAAK,CAAC,IAAIA,KAAK,GAAGW,EAAE,CAAC;EACxCE,EAAE,GAAGN,EAAE,GAAGD,EAAE;EACZN,KAAK,GAAGa,EAAE,GAAGN,EAAE;EACftE,EAAE,CAAC,CAAC,CAAC,GAAGsE,EAAE,IAAIM,EAAE,GAAGb,KAAK,CAAC,IAAIM,EAAE,GAAGN,KAAK,CAAC;EACxC/D,EAAE,CAAC,CAAC,CAAC,GAAG4E,EAAE;EAEV/C,MAAM,GAAGrC,GAAG,CACRA,GAAG,CACCA,GAAG,CACCE,KAAK,CAACA,KAAK,CAAC,CAAC,EAAEI,EAAE,EAAE+E,GAAG,EAAE3D,EAAE,CAAC,EAAEA,EAAE,EAAE2D,GAAG,EAAE1D,GAAG,CAAC,EAAEA,GAAG,EAC/CzB,KAAK,CAACA,KAAK,CAAC,CAAC,EAAEI,EAAE,EAAEkF,GAAG,EAAE9D,EAAE,CAAC,EAAEA,EAAE,EAAE8D,GAAG,EAAE5D,IAAI,CAAC,EAAEA,IAAI,EAAEE,GAAG,CAAC,EAAEA,GAAG,EAChE9B,GAAG,CACCE,KAAK,CAACA,KAAK,CAAC,CAAC,EAAEK,EAAE,EAAE+E,GAAG,EAAE5D,EAAE,CAAC,EAAEA,EAAE,EAAE4D,GAAG,EAAE3D,GAAG,CAAC,EAAEA,GAAG,EAC/CzB,KAAK,CAACA,KAAK,CAAC,CAAC,EAAEK,EAAE,EAAEkF,GAAG,EAAE/D,EAAE,CAAC,EAAEA,EAAE,EAAE+D,GAAG,EAAE7D,IAAI,CAAC,EAAEA,IAAI,EAAEG,IAAI,CAAC,EAAEA,IAAI,EAAEE,GAAG,CAAC,EAAEA,GAAG,EACjFjC,GAAG,CACCE,KAAK,CAACA,KAAK,CAAC,CAAC,EAAEM,EAAE,EAAE+E,GAAG,EAAE7D,EAAE,CAAC,EAAEA,EAAE,EAAE6D,GAAG,EAAE5D,GAAG,CAAC,EAAEA,GAAG,EAC/CzB,KAAK,CAACA,KAAK,CAAC,CAAC,EAAEM,EAAE,EAAEkF,GAAG,EAAEhE,EAAE,CAAC,EAAEA,EAAE,EAAEgE,GAAG,EAAE9D,IAAI,CAAC,EAAEA,IAAI,EAAEE,GAAG,CAAC,EAAEA,GAAG,EAAEI,GAAG,CAAC;EAE1E,IAAIyD,GAAG,GAAG7F,QAAQ,CAACuC,MAAM,EAAEH,GAAG,CAAC;EAC/B,IAAI0D,QAAQ,GAAGxF,YAAY,GAAG8C,SAAS;EACvC,IAAIyC,GAAG,IAAIC,QAAQ,IAAI,CAACD,GAAG,IAAIC,QAAQ,EAAE;IACrC,OAAOD,GAAG;EACd;EAEApB,KAAK,GAAG7B,EAAE,GAAG2C,GAAG;EAChBlC,OAAO,GAAGT,EAAE,IAAI2C,GAAG,GAAGd,KAAK,CAAC,IAAIA,KAAK,GAAGvB,EAAE,CAAC;EAC3CuB,KAAK,GAAG5B,EAAE,GAAG6C,GAAG;EAChBlC,OAAO,GAAGX,EAAE,IAAI6C,GAAG,GAAGjB,KAAK,CAAC,IAAIA,KAAK,GAAGtB,EAAE,CAAC;EAC3CsB,KAAK,GAAG3B,EAAE,GAAG0C,GAAG;EAChBlC,OAAO,GAAGR,EAAE,IAAI0C,GAAG,GAAGf,KAAK,CAAC,IAAIA,KAAK,GAAGvB,EAAE,CAAC;EAC3CuB,KAAK,GAAG1B,EAAE,GAAG4C,GAAG;EAChBlC,OAAO,GAAGV,EAAE,IAAI4C,GAAG,GAAGlB,KAAK,CAAC,IAAIA,KAAK,GAAGtB,EAAE,CAAC;EAC3CsB,KAAK,GAAGzB,EAAE,GAAGyC,GAAG;EAChBlC,OAAO,GAAGP,EAAE,IAAIyC,GAAG,GAAGhB,KAAK,CAAC,IAAIA,KAAK,GAAGvB,EAAE,CAAC;EAC3CuB,KAAK,GAAGxB,EAAE,GAAG2C,GAAG;EAChBlC,OAAO,GAAGT,EAAE,IAAI2C,GAAG,GAAGnB,KAAK,CAAC,IAAIA,KAAK,GAAGtB,EAAE,CAAC;EAC3C,IAAIE,OAAO,KAAK,CAAC,IAAIC,OAAO,KAAK,CAAC,IAAIC,OAAO,KAAK,CAAC,IAAIC,OAAO,KAAK,CAAC,IAAIC,OAAO,KAAK,CAAC,IAAIC,OAAO,KAAK,CAAC,EAAE;IACpG,OAAOmC,GAAG;EACd;EAEAC,QAAQ,GAAGvF,YAAY,GAAG6C,SAAS,GAAGrD,cAAc,GAAGgG,IAAI,CAACC,GAAG,CAACH,GAAG,CAAC;EACpEA,GAAG,IAAK,CAACN,GAAG,GAAGA,GAAG,GAAGG,GAAG,GAAGA,GAAG,KAAMF,GAAG,GAAG9B,OAAO,GAAGkC,GAAG,GAAGtC,OAAO,IAAKqC,GAAG,GAAGpC,OAAO,GAAGkC,GAAG,GAAGhC,OAAO,CAAC,CAAC,GACjG,CAAC,IAAI8B,GAAG,GAAGlC,OAAO,GAAGqC,GAAG,GAAGlC,OAAO,CAAC,IAAIgC,GAAG,GAAGI,GAAG,GAAGD,GAAG,GAAGF,GAAG,CAAC,IAC5D,CAACD,GAAG,GAAGA,GAAG,GAAGG,GAAG,GAAGA,GAAG,KAAMF,GAAG,GAAGjC,OAAO,GAAGkC,GAAG,GAAGnC,OAAO,IAAKqC,GAAG,GAAGvC,OAAO,GAAGkC,GAAG,GAAG7B,OAAO,CAAC,CAAC,GAC9F,CAAC,IAAI8B,GAAG,GAAGlC,OAAO,GAAGqC,GAAG,GAAGlC,OAAO,CAAC,IAAIgC,GAAG,GAAGC,GAAG,GAAGE,GAAG,GAAGL,GAAG,CAAC,CAAC,IAC7D,CAACE,GAAG,GAAGA,GAAG,GAAGG,GAAG,GAAGA,GAAG,KAAML,GAAG,GAAG9B,OAAO,GAAGkC,GAAG,GAAGtC,OAAO,IAAKqC,GAAG,GAAGpC,OAAO,GAAGkC,GAAG,GAAGhC,OAAO,CAAC,CAAC,GAC9F,CAAC,IAAIiC,GAAG,GAAGlC,OAAO,GAAGqC,GAAG,GAAGlC,OAAO,CAAC,IAAI6B,GAAG,GAAGI,GAAG,GAAGD,GAAG,GAAGF,GAAG,CAAC,CAAC;EAElE,IAAIK,GAAG,IAAIC,QAAQ,IAAI,CAACD,GAAG,IAAIC,QAAQ,EAAE;IACrC,OAAOD,GAAG;EACd;EAEA,IAAIvC,OAAO,KAAK,CAAC,IAAIG,OAAO,KAAK,CAAC,IAAIF,OAAO,KAAK,CAAC,IAAIG,OAAO,KAAK,CAAC,EAAE;IAClEwB,EAAE,GAAGK,GAAG,GAAGA,GAAG;IACdb,CAAC,GAAG5E,QAAQ,GAAGyF,GAAG;IAClBZ,GAAG,GAAGD,CAAC,IAAIA,CAAC,GAAGa,GAAG,CAAC;IACnBX,GAAG,GAAGW,GAAG,GAAGZ,GAAG;IACfQ,EAAE,GAAGP,GAAG,GAAGA,GAAG,IAAIM,EAAE,GAAGP,GAAG,GAAGA,GAAG,GAAG,CAACA,GAAG,GAAGA,GAAG,IAAIC,GAAG,CAAC;IACrDQ,EAAE,GAAGM,GAAG,GAAGA,GAAG;IACdhB,CAAC,GAAG5E,QAAQ,GAAG4F,GAAG;IAClBf,GAAG,GAAGD,CAAC,IAAIA,CAAC,GAAGgB,GAAG,CAAC;IACnBd,GAAG,GAAGc,GAAG,GAAGf,GAAG;IACfU,EAAE,GAAGT,GAAG,GAAGA,GAAG,IAAIQ,EAAE,GAAGT,GAAG,GAAGA,GAAG,GAAG,CAACA,GAAG,GAAGA,GAAG,IAAIC,GAAG,CAAC;IACrDG,EAAE,GAAGI,EAAE,GAAGE,EAAE;IACZZ,KAAK,GAAGM,EAAE,GAAGI,EAAE;IACfxE,EAAE,CAAC,CAAC,CAAC,GAAGwE,EAAE,IAAIJ,EAAE,GAAGN,KAAK,CAAC,IAAIY,EAAE,GAAGZ,KAAK,CAAC;IACxCO,EAAE,GAAGE,EAAE,GAAGH,EAAE;IACZN,KAAK,GAAGO,EAAE,GAAGE,EAAE;IACfD,EAAE,GAAGC,EAAE,IAAIF,EAAE,GAAGP,KAAK,CAAC,IAAIM,EAAE,GAAGN,KAAK,CAAC;IACrCM,EAAE,GAAGE,EAAE,GAAGG,EAAE;IACZX,KAAK,GAAGM,EAAE,GAAGE,EAAE;IACftE,EAAE,CAAC,CAAC,CAAC,GAAGsE,EAAE,IAAIF,EAAE,GAAGN,KAAK,CAAC,IAAIW,EAAE,GAAGX,KAAK,CAAC;IACxCa,EAAE,GAAGN,EAAE,GAAGD,EAAE;IACZN,KAAK,GAAGa,EAAE,GAAGN,EAAE;IACfrE,EAAE,CAAC,CAAC,CAAC,GAAGqE,EAAE,IAAIM,EAAE,GAAGb,KAAK,CAAC,IAAIM,EAAE,GAAGN,KAAK,CAAC;IACxC9D,EAAE,CAAC,CAAC,CAAC,GAAG2E,EAAE;EACd;EACA,IAAI/B,OAAO,KAAK,CAAC,IAAIG,OAAO,KAAK,CAAC,IAAIL,OAAO,KAAK,CAAC,IAAIG,OAAO,KAAK,CAAC,EAAE;IAClE0B,EAAE,GAAGM,GAAG,GAAGA,GAAG;IACdd,CAAC,GAAG5E,QAAQ,GAAG0F,GAAG;IAClBb,GAAG,GAAGD,CAAC,IAAIA,CAAC,GAAGc,GAAG,CAAC;IACnBZ,GAAG,GAAGY,GAAG,GAAGb,GAAG;IACfQ,EAAE,GAAGP,GAAG,GAAGA,GAAG,IAAIM,EAAE,GAAGP,GAAG,GAAGA,GAAG,GAAG,CAACA,GAAG,GAAGA,GAAG,IAAIC,GAAG,CAAC;IACrDQ,EAAE,GAAGO,GAAG,GAAGA,GAAG;IACdjB,CAAC,GAAG5E,QAAQ,GAAG6F,GAAG;IAClBhB,GAAG,GAAGD,CAAC,IAAIA,CAAC,GAAGiB,GAAG,CAAC;IACnBf,GAAG,GAAGe,GAAG,GAAGhB,GAAG;IACfU,EAAE,GAAGT,GAAG,GAAGA,GAAG,IAAIQ,EAAE,GAAGT,GAAG,GAAGA,GAAG,GAAG,CAACA,GAAG,GAAGA,GAAG,IAAIC,GAAG,CAAC;IACrDG,EAAE,GAAGI,EAAE,GAAGE,EAAE;IACZZ,KAAK,GAAGM,EAAE,GAAGI,EAAE;IACfvE,EAAE,CAAC,CAAC,CAAC,GAAGuE,EAAE,IAAIJ,EAAE,GAAGN,KAAK,CAAC,IAAIY,EAAE,GAAGZ,KAAK,CAAC;IACxCO,EAAE,GAAGE,EAAE,GAAGH,EAAE;IACZN,KAAK,GAAGO,EAAE,GAAGE,EAAE;IACfD,EAAE,GAAGC,EAAE,IAAIF,EAAE,GAAGP,KAAK,CAAC,IAAIM,EAAE,GAAGN,KAAK,CAAC;IACrCM,EAAE,GAAGE,EAAE,GAAGG,EAAE;IACZX,KAAK,GAAGM,EAAE,GAAGE,EAAE;IACfrE,EAAE,CAAC,CAAC,CAAC,GAAGqE,EAAE,IAAIF,EAAE,GAAGN,KAAK,CAAC,IAAIW,EAAE,GAAGX,KAAK,CAAC;IACxCa,EAAE,GAAGN,EAAE,GAAGD,EAAE;IACZN,KAAK,GAAGa,EAAE,GAAGN,EAAE;IACfpE,EAAE,CAAC,CAAC,CAAC,GAAGoE,EAAE,IAAIM,EAAE,GAAGb,KAAK,CAAC,IAAIM,EAAE,GAAGN,KAAK,CAAC;IACxC7D,EAAE,CAAC,CAAC,CAAC,GAAG0E,EAAE;EACd;EACA,IAAIjC,OAAO,KAAK,CAAC,IAAIG,OAAO,KAAK,CAAC,IAAIF,OAAO,KAAK,CAAC,IAAIG,OAAO,KAAK,CAAC,EAAE;IAClEyB,EAAE,GAAGO,GAAG,GAAGA,GAAG;IACdf,CAAC,GAAG5E,QAAQ,GAAG2F,GAAG;IAClBd,GAAG,GAAGD,CAAC,IAAIA,CAAC,GAAGe,GAAG,CAAC;IACnBb,GAAG,GAAGa,GAAG,GAAGd,GAAG;IACfQ,EAAE,GAAGP,GAAG,GAAGA,GAAG,IAAIM,EAAE,GAAGP,GAAG,GAAGA,GAAG,GAAG,CAACA,GAAG,GAAGA,GAAG,IAAIC,GAAG,CAAC;IACrDQ,EAAE,GAAGQ,GAAG,GAAGA,GAAG;IACdlB,CAAC,GAAG5E,QAAQ,GAAG8F,GAAG;IAClBjB,GAAG,GAAGD,CAAC,IAAIA,CAAC,GAAGkB,GAAG,CAAC;IACnBhB,GAAG,GAAGgB,GAAG,GAAGjB,GAAG;IACfU,EAAE,GAAGT,GAAG,GAAGA,GAAG,IAAIQ,EAAE,GAAGT,GAAG,GAAGA,GAAG,GAAG,CAACA,GAAG,GAAGA,GAAG,IAAIC,GAAG,CAAC;IACrDG,EAAE,GAAGI,EAAE,GAAGE,EAAE;IACZZ,KAAK,GAAGM,EAAE,GAAGI,EAAE;IACftE,EAAE,CAAC,CAAC,CAAC,GAAGsE,EAAE,IAAIJ,EAAE,GAAGN,KAAK,CAAC,IAAIY,EAAE,GAAGZ,KAAK,CAAC;IACxCO,EAAE,GAAGE,EAAE,GAAGH,EAAE;IACZN,KAAK,GAAGO,EAAE,GAAGE,EAAE;IACfD,EAAE,GAAGC,EAAE,IAAIF,EAAE,GAAGP,KAAK,CAAC,IAAIM,EAAE,GAAGN,KAAK,CAAC;IACrCM,EAAE,GAAGE,EAAE,GAAGG,EAAE;IACZX,KAAK,GAAGM,EAAE,GAAGE,EAAE;IACfpE,EAAE,CAAC,CAAC,CAAC,GAAGoE,EAAE,IAAIF,EAAE,GAAGN,KAAK,CAAC,IAAIW,EAAE,GAAGX,KAAK,CAAC;IACxCa,EAAE,GAAGN,EAAE,GAAGD,EAAE;IACZN,KAAK,GAAGa,EAAE,GAAGN,EAAE;IACfnE,EAAE,CAAC,CAAC,CAAC,GAAGmE,EAAE,IAAIM,EAAE,GAAGb,KAAK,CAAC,IAAIM,EAAE,GAAGN,KAAK,CAAC;IACxC5D,EAAE,CAAC,CAAC,CAAC,GAAGyE,EAAE;EACd;EAEA,IAAIjC,OAAO,KAAK,CAAC,EAAE;IACfM,QAAQ,GAAGvD,KAAK,CAAC,CAAC,EAAEI,EAAE,EAAE6C,OAAO,EAAErC,KAAK,CAAC;IACvCuB,MAAM,GAAGD,MAAM,CAACC,MAAM,EAAEpC,SAAS,CAC7BC,KAAK,CAACuD,QAAQ,EAAE3C,KAAK,EAAE,CAAC,GAAGuE,GAAG,EAAE1D,GAAG,CAAC,EAAEA,GAAG,EACzCzB,KAAK,CAACA,KAAK,CAAC,CAAC,EAAES,EAAE,EAAEwC,OAAO,EAAEzB,EAAE,CAAC,EAAEA,EAAE,EAAE+D,GAAG,EAAE7D,IAAI,CAAC,EAAEA,IAAI,EACrD1B,KAAK,CAACA,KAAK,CAAC,CAAC,EAAEQ,EAAE,EAAEyC,OAAO,EAAEzB,EAAE,CAAC,EAAEA,EAAE,EAAE,CAACgE,GAAG,EAAE7D,IAAI,CAAC,EAAEA,IAAI,EAAEC,GAAG,EAAEE,GAAG,CAAC,EAAEA,GAAG,CAAC;EAC/E;EACA,IAAIsB,OAAO,KAAK,CAAC,EAAE;IACfI,QAAQ,GAAGxD,KAAK,CAAC,CAAC,EAAEI,EAAE,EAAEgD,OAAO,EAAEvC,KAAK,CAAC;IACvCsB,MAAM,GAAGD,MAAM,CAACC,MAAM,EAAEpC,SAAS,CAC7BC,KAAK,CAACwD,QAAQ,EAAE3C,KAAK,EAAE,CAAC,GAAGyE,GAAG,EAAE7D,GAAG,CAAC,EAAEA,GAAG,EACzCzB,KAAK,CAACA,KAAK,CAAC,CAAC,EAAEQ,EAAE,EAAE4C,OAAO,EAAE5B,EAAE,CAAC,EAAEA,EAAE,EAAE6D,GAAG,EAAE3D,IAAI,CAAC,EAAEA,IAAI,EACrD1B,KAAK,CAACA,KAAK,CAAC,CAAC,EAAES,EAAE,EAAE2C,OAAO,EAAE5B,EAAE,CAAC,EAAEA,EAAE,EAAE,CAAC4D,GAAG,EAAEzD,IAAI,CAAC,EAAEA,IAAI,EAAEC,GAAG,EAAEE,GAAG,CAAC,EAAEA,GAAG,CAAC;EAC/E;EACA,IAAIoB,OAAO,KAAK,CAAC,EAAE;IACfO,QAAQ,GAAGzD,KAAK,CAAC,CAAC,EAAEK,EAAE,EAAE6C,OAAO,EAAEpC,KAAK,CAAC;IACvCqB,MAAM,GAAGD,MAAM,CAACC,MAAM,EAAEpC,SAAS,CAC7BC,KAAK,CAACyD,QAAQ,EAAE3C,KAAK,EAAE,CAAC,GAAGsE,GAAG,EAAE3D,GAAG,CAAC,EAAEA,GAAG,EACzCzB,KAAK,CAACA,KAAK,CAAC,CAAC,EAAEO,EAAE,EAAE2C,OAAO,EAAE1B,EAAE,CAAC,EAAEA,EAAE,EAAEgE,GAAG,EAAE9D,IAAI,CAAC,EAAEA,IAAI,EACrD1B,KAAK,CAACA,KAAK,CAAC,CAAC,EAAES,EAAE,EAAEyC,OAAO,EAAE1B,EAAE,CAAC,EAAEA,EAAE,EAAE,CAAC8D,GAAG,EAAE3D,IAAI,CAAC,EAAEA,IAAI,EAAEC,GAAG,EAAEE,GAAG,CAAC,EAAEA,GAAG,CAAC;EAC/E;EACA,IAAIuB,OAAO,KAAK,CAAC,EAAE;IACfK,QAAQ,GAAG1D,KAAK,CAAC,CAAC,EAAEK,EAAE,EAAEgD,OAAO,EAAEtC,KAAK,CAAC;IACvCoB,MAAM,GAAGD,MAAM,CAACC,MAAM,EAAEpC,SAAS,CAC7BC,KAAK,CAAC0D,QAAQ,EAAE3C,KAAK,EAAE,CAAC,GAAGwE,GAAG,EAAE9D,GAAG,CAAC,EAAEA,GAAG,EACzCzB,KAAK,CAACA,KAAK,CAAC,CAAC,EAAES,EAAE,EAAE4C,OAAO,EAAE7B,EAAE,CAAC,EAAEA,EAAE,EAAE2D,GAAG,EAAEzD,IAAI,CAAC,EAAEA,IAAI,EACrD1B,KAAK,CAACA,KAAK,CAAC,CAAC,EAAEO,EAAE,EAAE8C,OAAO,EAAE7B,EAAE,CAAC,EAAEA,EAAE,EAAE,CAAC6D,GAAG,EAAE1D,IAAI,CAAC,EAAEA,IAAI,EAAEC,GAAG,EAAEE,GAAG,CAAC,EAAEA,GAAG,CAAC;EAC/E;EACA,IAAIqB,OAAO,KAAK,CAAC,EAAE;IACfQ,QAAQ,GAAG3D,KAAK,CAAC,CAAC,EAAEM,EAAE,EAAE6C,OAAO,EAAEnC,KAAK,CAAC;IACvCmB,MAAM,GAAGD,MAAM,CAACC,MAAM,EAAEpC,SAAS,CAC7BC,KAAK,CAAC2D,QAAQ,EAAE3C,KAAK,EAAE,CAAC,GAAGqE,GAAG,EAAE5D,GAAG,CAAC,EAAEA,GAAG,EACzCzB,KAAK,CAACA,KAAK,CAAC,CAAC,EAAEQ,EAAE,EAAE2C,OAAO,EAAE3B,EAAE,CAAC,EAAEA,EAAE,EAAE8D,GAAG,EAAE5D,IAAI,CAAC,EAAEA,IAAI,EACrD1B,KAAK,CAACA,KAAK,CAAC,CAAC,EAAEO,EAAE,EAAE4C,OAAO,EAAE3B,EAAE,CAAC,EAAEA,EAAE,EAAE,CAAC+D,GAAG,EAAE5D,IAAI,CAAC,EAAEA,IAAI,EAAEC,GAAG,EAAEE,GAAG,CAAC,EAAEA,GAAG,CAAC;EAC/E;EACA,IAAIwB,OAAO,KAAK,CAAC,EAAE;IACfM,QAAQ,GAAG5D,KAAK,CAAC,CAAC,EAAEM,EAAE,EAAEgD,OAAO,EAAErC,KAAK,CAAC;IACvCkB,MAAM,GAAGD,MAAM,CAACC,MAAM,EAAEpC,SAAS,CAC7BC,KAAK,CAAC4D,QAAQ,EAAE3C,KAAK,EAAE,CAAC,GAAGuE,GAAG,EAAE/D,GAAG,CAAC,EAAEA,GAAG,EACzCzB,KAAK,CAACA,KAAK,CAAC,CAAC,EAAEO,EAAE,EAAE+C,OAAO,EAAE9B,EAAE,CAAC,EAAEA,EAAE,EAAE4D,GAAG,EAAE1D,IAAI,CAAC,EAAEA,IAAI,EACrD1B,KAAK,CAACA,KAAK,CAAC,CAAC,EAAEQ,EAAE,EAAE8C,OAAO,EAAE9B,EAAE,CAAC,EAAEA,EAAE,EAAE,CAAC2D,GAAG,EAAExD,IAAI,CAAC,EAAEA,IAAI,EAAEC,GAAG,EAAEE,GAAG,CAAC,EAAEA,GAAG,CAAC;EAC/E;EAEA,IAAImB,OAAO,KAAK,CAAC,IAAIG,OAAO,KAAK,CAAC,EAAE;IAChC,IAAIF,OAAO,KAAK,CAAC,IAAIG,OAAO,KAAK,CAAC,IAAIF,OAAO,KAAK,CAAC,IAAIG,OAAO,KAAK,CAAC,EAAE;MAClEwB,EAAE,GAAG5B,OAAO,GAAGsC,GAAG;MAClBlB,CAAC,GAAG5E,QAAQ,GAAGwD,OAAO;MACtBqB,GAAG,GAAGD,CAAC,IAAIA,CAAC,GAAGpB,OAAO,CAAC;MACvBsB,GAAG,GAAGtB,OAAO,GAAGqB,GAAG;MACnBD,CAAC,GAAG5E,QAAQ,GAAG8F,GAAG;MAClBf,GAAG,GAAGH,CAAC,IAAIA,CAAC,GAAGkB,GAAG,CAAC;MACnBd,GAAG,GAAGc,GAAG,GAAGf,GAAG;MACfM,EAAE,GAAGP,GAAG,GAAGE,GAAG,IAAII,EAAE,GAAGP,GAAG,GAAGE,GAAG,GAAGD,GAAG,GAAGC,GAAG,GAAGF,GAAG,GAAGG,GAAG,CAAC;MACzDM,EAAE,GAAGI,GAAG,GAAG9B,OAAO;MAClBgB,CAAC,GAAG5E,QAAQ,GAAG0F,GAAG;MAClBb,GAAG,GAAGD,CAAC,IAAIA,CAAC,GAAGc,GAAG,CAAC;MACnBZ,GAAG,GAAGY,GAAG,GAAGb,GAAG;MACfD,CAAC,GAAG5E,QAAQ,GAAG4D,OAAO;MACtBmB,GAAG,GAAGH,CAAC,IAAIA,CAAC,GAAGhB,OAAO,CAAC;MACvBoB,GAAG,GAAGpB,OAAO,GAAGmB,GAAG;MACnBQ,EAAE,GAAGT,GAAG,GAAGE,GAAG,IAAIM,EAAE,GAAGT,GAAG,GAAGE,GAAG,GAAGD,GAAG,GAAGC,GAAG,GAAGF,GAAG,GAAGG,GAAG,CAAC;MACzDC,EAAE,GAAGI,EAAE,GAAGE,EAAE;MACZZ,KAAK,GAAGM,EAAE,GAAGI,EAAE;MACfrE,CAAC,CAAC,CAAC,CAAC,GAAGqE,EAAE,IAAIJ,EAAE,GAAGN,KAAK,CAAC,IAAIY,EAAE,GAAGZ,KAAK,CAAC;MACvCO,EAAE,GAAGE,EAAE,GAAGH,EAAE;MACZN,KAAK,GAAGO,EAAE,GAAGE,EAAE;MACfD,EAAE,GAAGC,EAAE,IAAIF,EAAE,GAAGP,KAAK,CAAC,IAAIM,EAAE,GAAGN,KAAK,CAAC;MACrCM,EAAE,GAAGE,EAAE,GAAGG,EAAE;MACZX,KAAK,GAAGM,EAAE,GAAGE,EAAE;MACfnE,CAAC,CAAC,CAAC,CAAC,GAAGmE,EAAE,IAAIF,EAAE,GAAGN,KAAK,CAAC,IAAIW,EAAE,GAAGX,KAAK,CAAC;MACvCa,EAAE,GAAGN,EAAE,GAAGD,EAAE;MACZN,KAAK,GAAGa,EAAE,GAAGN,EAAE;MACflE,CAAC,CAAC,CAAC,CAAC,GAAGkE,EAAE,IAAIM,EAAE,GAAGb,KAAK,CAAC,IAAIM,EAAE,GAAGN,KAAK,CAAC;MACvC3D,CAAC,CAAC,CAAC,CAAC,GAAGwE,EAAE;MACTJ,EAAE,GAAG3B,OAAO,GAAG,CAACoC,GAAG;MACnBjB,CAAC,GAAG5E,QAAQ,GAAGyD,OAAO;MACtBoB,GAAG,GAAGD,CAAC,IAAIA,CAAC,GAAGnB,OAAO,CAAC;MACvBqB,GAAG,GAAGrB,OAAO,GAAGoB,GAAG;MACnBD,CAAC,GAAG5E,QAAQ,GAAG,CAAC6F,GAAG;MACnBd,GAAG,GAAGH,CAAC,IAAIA,CAAC,GAAG,CAACiB,GAAG,CAAC;MACpBb,GAAG,GAAG,CAACa,GAAG,GAAGd,GAAG;MAChBM,EAAE,GAAGP,GAAG,GAAGE,GAAG,IAAII,EAAE,GAAGP,GAAG,GAAGE,GAAG,GAAGD,GAAG,GAAGC,GAAG,GAAGF,GAAG,GAAGG,GAAG,CAAC;MACzDM,EAAE,GAAGK,GAAG,GAAG,CAAChC,OAAO;MACnBiB,CAAC,GAAG5E,QAAQ,GAAG2F,GAAG;MAClBd,GAAG,GAAGD,CAAC,IAAIA,CAAC,GAAGe,GAAG,CAAC;MACnBb,GAAG,GAAGa,GAAG,GAAGd,GAAG;MACfD,CAAC,GAAG5E,QAAQ,GAAG,CAAC2D,OAAO;MACvBoB,GAAG,GAAGH,CAAC,IAAIA,CAAC,GAAG,CAACjB,OAAO,CAAC;MACxBqB,GAAG,GAAG,CAACrB,OAAO,GAAGoB,GAAG;MACpBQ,EAAE,GAAGT,GAAG,GAAGE,GAAG,IAAIM,EAAE,GAAGT,GAAG,GAAGE,GAAG,GAAGD,GAAG,GAAGC,GAAG,GAAGF,GAAG,GAAGG,GAAG,CAAC;MACzDC,EAAE,GAAGI,EAAE,GAAGE,EAAE;MACZZ,KAAK,GAAGM,EAAE,GAAGI,EAAE;MACfpE,CAAC,CAAC,CAAC,CAAC,GAAGoE,EAAE,IAAIJ,EAAE,GAAGN,KAAK,CAAC,IAAIY,EAAE,GAAGZ,KAAK,CAAC;MACvCO,EAAE,GAAGE,EAAE,GAAGH,EAAE;MACZN,KAAK,GAAGO,EAAE,GAAGE,EAAE;MACfD,EAAE,GAAGC,EAAE,IAAIF,EAAE,GAAGP,KAAK,CAAC,IAAIM,EAAE,GAAGN,KAAK,CAAC;MACrCM,EAAE,GAAGE,EAAE,GAAGG,EAAE;MACZX,KAAK,GAAGM,EAAE,GAAGE,EAAE;MACflE,CAAC,CAAC,CAAC,CAAC,GAAGkE,EAAE,IAAIF,EAAE,GAAGN,KAAK,CAAC,IAAIW,EAAE,GAAGX,KAAK,CAAC;MACvCa,EAAE,GAAGN,EAAE,GAAGD,EAAE;MACZN,KAAK,GAAGa,EAAE,GAAGN,EAAE;MACfjE,CAAC,CAAC,CAAC,CAAC,GAAGiE,EAAE,IAAIM,EAAE,GAAGb,KAAK,CAAC,IAAIM,EAAE,GAAGN,KAAK,CAAC;MACvC1D,CAAC,CAAC,CAAC,CAAC,GAAGuE,EAAE;MACTpB,MAAM,GAAGhE,GAAG,CAAC,CAAC,EAAEY,CAAC,EAAE,CAAC,EAAEC,CAAC,EAAEQ,GAAG,CAAC;MAC7B2D,EAAE,GAAG5B,OAAO,GAAGI,OAAO;MACtBgB,CAAC,GAAG5E,QAAQ,GAAGwD,OAAO;MACtBqB,GAAG,GAAGD,CAAC,IAAIA,CAAC,GAAGpB,OAAO,CAAC;MACvBsB,GAAG,GAAGtB,OAAO,GAAGqB,GAAG;MACnBD,CAAC,GAAG5E,QAAQ,GAAG4D,OAAO;MACtBmB,GAAG,GAAGH,CAAC,IAAIA,CAAC,GAAGhB,OAAO,CAAC;MACvBoB,GAAG,GAAGpB,OAAO,GAAGmB,GAAG;MACnBM,EAAE,GAAGP,GAAG,GAAGE,GAAG,IAAII,EAAE,GAAGP,GAAG,GAAGE,GAAG,GAAGD,GAAG,GAAGC,GAAG,GAAGF,GAAG,GAAGG,GAAG,CAAC;MACzDM,EAAE,GAAG7B,OAAO,GAAGE,OAAO;MACtBiB,CAAC,GAAG5E,QAAQ,GAAGyD,OAAO;MACtBoB,GAAG,GAAGD,CAAC,IAAIA,CAAC,GAAGnB,OAAO,CAAC;MACvBqB,GAAG,GAAGrB,OAAO,GAAGoB,GAAG;MACnBD,CAAC,GAAG5E,QAAQ,GAAG2D,OAAO;MACtBoB,GAAG,GAAGH,CAAC,IAAIA,CAAC,GAAGjB,OAAO,CAAC;MACvBqB,GAAG,GAAGrB,OAAO,GAAGoB,GAAG;MACnBQ,EAAE,GAAGT,GAAG,GAAGE,GAAG,IAAIM,EAAE,GAAGT,GAAG,GAAGE,GAAG,GAAGD,GAAG,GAAGC,GAAG,GAAGF,GAAG,GAAGG,GAAG,CAAC;MACzDC,EAAE,GAAGI,EAAE,GAAGE,EAAE;MACZZ,KAAK,GAAGU,EAAE,GAAGJ,EAAE;MACfrD,IAAI,CAAC,CAAC,CAAC,GAAGyD,EAAE,IAAIJ,EAAE,GAAGN,KAAK,CAAC,IAAIA,KAAK,GAAGY,EAAE,CAAC;MAC1CL,EAAE,GAAGE,EAAE,GAAGH,EAAE;MACZN,KAAK,GAAGO,EAAE,GAAGE,EAAE;MACfD,EAAE,GAAGC,EAAE,IAAIF,EAAE,GAAGP,KAAK,CAAC,IAAIM,EAAE,GAAGN,KAAK,CAAC;MACrCM,EAAE,GAAGE,EAAE,GAAGG,EAAE;MACZX,KAAK,GAAGQ,EAAE,GAAGF,EAAE;MACfrD,IAAI,CAAC,CAAC,CAAC,GAAGuD,EAAE,IAAIF,EAAE,GAAGN,KAAK,CAAC,IAAIA,KAAK,GAAGW,EAAE,CAAC;MAC1CE,EAAE,GAAGN,EAAE,GAAGD,EAAE;MACZN,KAAK,GAAGa,EAAE,GAAGN,EAAE;MACftD,IAAI,CAAC,CAAC,CAAC,GAAGsD,EAAE,IAAIM,EAAE,GAAGb,KAAK,CAAC,IAAIM,EAAE,GAAGN,KAAK,CAAC;MAC1C/C,IAAI,CAAC,CAAC,CAAC,GAAG4D,EAAE;MACZjB,OAAO,GAAG,CAAC;IACf,CAAC,MAAM;MACH9C,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;MACV2C,MAAM,GAAG,CAAC;MACVxC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC;MACX2C,OAAO,GAAG,CAAC;IACf;IACA,IAAIhB,OAAO,KAAK,CAAC,EAAE;MACf,MAAM4C,GAAG,GAAG7F,KAAK,CAAC8D,MAAM,EAAE3C,GAAG,EAAE8B,OAAO,EAAEtB,IAAI,CAAC;MAC7CQ,MAAM,GAAGD,MAAM,CAACC,MAAM,EAAErC,GAAG,CACvBE,KAAK,CAACuD,QAAQ,EAAE3C,KAAK,EAAEqC,OAAO,EAAExB,GAAG,CAAC,EAAEA,GAAG,EACzCzB,KAAK,CAAC6F,GAAG,EAAElE,IAAI,EAAE,CAAC,GAAGwD,GAAG,EAAEvD,GAAG,CAAC,EAAEA,GAAG,EAAEE,GAAG,CAAC,EAAEA,GAAG,CAAC;MAEnD,MAAMgE,IAAI,GAAG9F,KAAK,CAACiE,OAAO,EAAE3C,IAAI,EAAE2B,OAAO,EAAEzB,EAAE,CAAC;MAC9CW,MAAM,GAAGD,MAAM,CAACC,MAAM,EAAEpC,SAAS,CAC7BC,KAAK,CAAC8F,IAAI,EAAEtE,EAAE,EAAE,CAAC,GAAG2D,GAAG,EAAE1D,GAAG,CAAC,EAAEA,GAAG,EAClCzB,KAAK,CAAC8F,IAAI,EAAEtE,EAAE,EAAEyB,OAAO,EAAEvB,IAAI,CAAC,EAAEA,IAAI,EACpC1B,KAAK,CAAC6F,GAAG,EAAElE,IAAI,EAAEsB,OAAO,EAAErB,GAAG,CAAC,EAAEA,GAAG,EAAEC,IAAI,EAAEE,GAAG,CAAC,EAAEA,GAAG,CAAC;MAEzD,IAAIsB,OAAO,KAAK,CAAC,EAAE;QACflB,MAAM,GAAGD,MAAM,CAACC,MAAM,EAAEnC,KAAK,CAACA,KAAK,CAAC,CAAC,EAAES,EAAE,EAAEwC,OAAO,EAAEzB,EAAE,CAAC,EAAEA,EAAE,EAAE6B,OAAO,EAAE5B,GAAG,CAAC,EAAEA,GAAG,CAAC;MACpF;MACA,IAAI6B,OAAO,KAAK,CAAC,EAAE;QACfnB,MAAM,GAAGD,MAAM,CAACC,MAAM,EAAEnC,KAAK,CAACA,KAAK,CAAC,CAAC,EAAEQ,EAAE,EAAE,CAACyC,OAAO,EAAEzB,EAAE,CAAC,EAAEA,EAAE,EAAE8B,OAAO,EAAE7B,GAAG,CAAC,EAAEA,GAAG,CAAC;MACrF;IACJ;IACA,IAAI2B,OAAO,KAAK,CAAC,EAAE;MACf,MAAMyC,GAAG,GAAG7F,KAAK,CAAC8D,MAAM,EAAE3C,GAAG,EAAEiC,OAAO,EAAEzB,IAAI,CAAC;MAC7CQ,MAAM,GAAGD,MAAM,CAACC,MAAM,EAAErC,GAAG,CACvBE,KAAK,CAACwD,QAAQ,EAAE3C,KAAK,EAAEuC,OAAO,EAAE3B,GAAG,CAAC,EAAEA,GAAG,EACzCzB,KAAK,CAAC6F,GAAG,EAAElE,IAAI,EAAE,CAAC,GAAG2D,GAAG,EAAE1D,GAAG,CAAC,EAAEA,GAAG,EAAEE,GAAG,CAAC,EAAEA,GAAG,CAAC;MAEnD,MAAMgE,IAAI,GAAG9F,KAAK,CAACiE,OAAO,EAAE3C,IAAI,EAAE8B,OAAO,EAAE5B,EAAE,CAAC;MAC9CW,MAAM,GAAGD,MAAM,CAACC,MAAM,EAAEpC,SAAS,CAC7BC,KAAK,CAAC8F,IAAI,EAAEtE,EAAE,EAAE,CAAC,GAAG8D,GAAG,EAAE7D,GAAG,CAAC,EAAEA,GAAG,EAClCzB,KAAK,CAAC8F,IAAI,EAAEtE,EAAE,EAAE4B,OAAO,EAAE1B,IAAI,CAAC,EAAEA,IAAI,EACpC1B,KAAK,CAAC6F,GAAG,EAAElE,IAAI,EAAEyB,OAAO,EAAExB,GAAG,CAAC,EAAEA,GAAG,EAAEC,IAAI,EAAEE,GAAG,CAAC,EAAEA,GAAG,CAAC;IAC7D;EACJ;EACA,IAAImB,OAAO,KAAK,CAAC,IAAIG,OAAO,KAAK,CAAC,EAAE;IAChC,IAAIF,OAAO,KAAK,CAAC,IAAIG,OAAO,KAAK,CAAC,IAAIL,OAAO,KAAK,CAAC,IAAIG,OAAO,KAAK,CAAC,EAAE;MAClE0B,EAAE,GAAG3B,OAAO,GAAGmC,GAAG;MAClBhB,CAAC,GAAG5E,QAAQ,GAAGyD,OAAO;MACtBoB,GAAG,GAAGD,CAAC,IAAIA,CAAC,GAAGnB,OAAO,CAAC;MACvBqB,GAAG,GAAGrB,OAAO,GAAGoB,GAAG;MACnBD,CAAC,GAAG5E,QAAQ,GAAG4F,GAAG;MAClBb,GAAG,GAAGH,CAAC,IAAIA,CAAC,GAAGgB,GAAG,CAAC;MACnBZ,GAAG,GAAGY,GAAG,GAAGb,GAAG;MACfM,EAAE,GAAGP,GAAG,GAAGE,GAAG,IAAII,EAAE,GAAGP,GAAG,GAAGE,GAAG,GAAGD,GAAG,GAAGC,GAAG,GAAGF,GAAG,GAAGG,GAAG,CAAC;MACzDM,EAAE,GAAGK,GAAG,GAAGjC,OAAO;MAClBkB,CAAC,GAAG5E,QAAQ,GAAG2F,GAAG;MAClBd,GAAG,GAAGD,CAAC,IAAIA,CAAC,GAAGe,GAAG,CAAC;MACnBb,GAAG,GAAGa,GAAG,GAAGd,GAAG;MACfD,CAAC,GAAG5E,QAAQ,GAAG0D,OAAO;MACtBqB,GAAG,GAAGH,CAAC,IAAIA,CAAC,GAAGlB,OAAO,CAAC;MACvBsB,GAAG,GAAGtB,OAAO,GAAGqB,GAAG;MACnBQ,EAAE,GAAGT,GAAG,GAAGE,GAAG,IAAIM,EAAE,GAAGT,GAAG,GAAGE,GAAG,GAAGD,GAAG,GAAGC,GAAG,GAAGF,GAAG,GAAGG,GAAG,CAAC;MACzDC,EAAE,GAAGI,EAAE,GAAGE,EAAE;MACZZ,KAAK,GAAGM,EAAE,GAAGI,EAAE;MACfrE,CAAC,CAAC,CAAC,CAAC,GAAGqE,EAAE,IAAIJ,EAAE,GAAGN,KAAK,CAAC,IAAIY,EAAE,GAAGZ,KAAK,CAAC;MACvCO,EAAE,GAAGE,EAAE,GAAGH,EAAE;MACZN,KAAK,GAAGO,EAAE,GAAGE,EAAE;MACfD,EAAE,GAAGC,EAAE,IAAIF,EAAE,GAAGP,KAAK,CAAC,IAAIM,EAAE,GAAGN,KAAK,CAAC;MACrCM,EAAE,GAAGE,EAAE,GAAGG,EAAE;MACZX,KAAK,GAAGM,EAAE,GAAGE,EAAE;MACfnE,CAAC,CAAC,CAAC,CAAC,GAAGmE,EAAE,IAAIF,EAAE,GAAGN,KAAK,CAAC,IAAIW,EAAE,GAAGX,KAAK,CAAC;MACvCa,EAAE,GAAGN,EAAE,GAAGD,EAAE;MACZN,KAAK,GAAGa,EAAE,GAAGN,EAAE;MACflE,CAAC,CAAC,CAAC,CAAC,GAAGkE,EAAE,IAAIM,EAAE,GAAGb,KAAK,CAAC,IAAIM,EAAE,GAAGN,KAAK,CAAC;MACvC3D,CAAC,CAAC,CAAC,CAAC,GAAGwE,EAAE;MACTf,EAAE,GAAG,CAACqB,GAAG;MACTpB,EAAE,GAAG,CAACd,OAAO;MACbwB,EAAE,GAAG7B,OAAO,GAAGkB,EAAE;MACjBG,CAAC,GAAG5E,QAAQ,GAAGuD,OAAO;MACtBsB,GAAG,GAAGD,CAAC,IAAIA,CAAC,GAAGrB,OAAO,CAAC;MACvBuB,GAAG,GAAGvB,OAAO,GAAGsB,GAAG;MACnBD,CAAC,GAAG5E,QAAQ,GAAGyE,EAAE;MACjBM,GAAG,GAAGH,CAAC,IAAIA,CAAC,GAAGH,EAAE,CAAC;MAClBO,GAAG,GAAGP,EAAE,GAAGM,GAAG;MACdM,EAAE,GAAGP,GAAG,GAAGE,GAAG,IAAII,EAAE,GAAGP,GAAG,GAAGE,GAAG,GAAGD,GAAG,GAAGC,GAAG,GAAGF,GAAG,GAAGG,GAAG,CAAC;MACzDM,EAAE,GAAGG,GAAG,GAAGf,EAAE;MACbE,CAAC,GAAG5E,QAAQ,GAAGyF,GAAG;MAClBZ,GAAG,GAAGD,CAAC,IAAIA,CAAC,GAAGa,GAAG,CAAC;MACnBX,GAAG,GAAGW,GAAG,GAAGZ,GAAG;MACfD,CAAC,GAAG5E,QAAQ,GAAG0E,EAAE;MACjBK,GAAG,GAAGH,CAAC,IAAIA,CAAC,GAAGF,EAAE,CAAC;MAClBM,GAAG,GAAGN,EAAE,GAAGK,GAAG;MACdQ,EAAE,GAAGT,GAAG,GAAGE,GAAG,IAAIM,EAAE,GAAGT,GAAG,GAAGE,GAAG,GAAGD,GAAG,GAAGC,GAAG,GAAGF,GAAG,GAAGG,GAAG,CAAC;MACzDC,EAAE,GAAGI,EAAE,GAAGE,EAAE;MACZZ,KAAK,GAAGM,EAAE,GAAGI,EAAE;MACfpE,CAAC,CAAC,CAAC,CAAC,GAAGoE,EAAE,IAAIJ,EAAE,GAAGN,KAAK,CAAC,IAAIY,EAAE,GAAGZ,KAAK,CAAC;MACvCO,EAAE,GAAGE,EAAE,GAAGH,EAAE;MACZN,KAAK,GAAGO,EAAE,GAAGE,EAAE;MACfD,EAAE,GAAGC,EAAE,IAAIF,EAAE,GAAGP,KAAK,CAAC,IAAIM,EAAE,GAAGN,KAAK,CAAC;MACrCM,EAAE,GAAGE,EAAE,GAAGG,EAAE;MACZX,KAAK,GAAGM,EAAE,GAAGE,EAAE;MACflE,CAAC,CAAC,CAAC,CAAC,GAAGkE,EAAE,IAAIF,EAAE,GAAGN,KAAK,CAAC,IAAIW,EAAE,GAAGX,KAAK,CAAC;MACvCa,EAAE,GAAGN,EAAE,GAAGD,EAAE;MACZN,KAAK,GAAGa,EAAE,GAAGN,EAAE;MACfjE,CAAC,CAAC,CAAC,CAAC,GAAGiE,EAAE,IAAIM,EAAE,GAAGb,KAAK,CAAC,IAAIM,EAAE,GAAGN,KAAK,CAAC;MACvC1D,CAAC,CAAC,CAAC,CAAC,GAAGuE,EAAE;MACTnB,MAAM,GAAGjE,GAAG,CAAC,CAAC,EAAEY,CAAC,EAAE,CAAC,EAAEC,CAAC,EAAES,GAAG,CAAC;MAC7B0D,EAAE,GAAG3B,OAAO,GAAGC,OAAO;MACtBkB,CAAC,GAAG5E,QAAQ,GAAGyD,OAAO;MACtBoB,GAAG,GAAGD,CAAC,IAAIA,CAAC,GAAGnB,OAAO,CAAC;MACvBqB,GAAG,GAAGrB,OAAO,GAAGoB,GAAG;MACnBD,CAAC,GAAG5E,QAAQ,GAAG0D,OAAO;MACtBqB,GAAG,GAAGH,CAAC,IAAIA,CAAC,GAAGlB,OAAO,CAAC;MACvBsB,GAAG,GAAGtB,OAAO,GAAGqB,GAAG;MACnBM,EAAE,GAAGP,GAAG,GAAGE,GAAG,IAAII,EAAE,GAAGP,GAAG,GAAGE,GAAG,GAAGD,GAAG,GAAGC,GAAG,GAAGF,GAAG,GAAGG,GAAG,CAAC;MACzDM,EAAE,GAAG/B,OAAO,GAAGK,OAAO;MACtBgB,CAAC,GAAG5E,QAAQ,GAAGuD,OAAO;MACtBsB,GAAG,GAAGD,CAAC,IAAIA,CAAC,GAAGrB,OAAO,CAAC;MACvBuB,GAAG,GAAGvB,OAAO,GAAGsB,GAAG;MACnBD,CAAC,GAAG5E,QAAQ,GAAG4D,OAAO;MACtBmB,GAAG,GAAGH,CAAC,IAAIA,CAAC,GAAGhB,OAAO,CAAC;MACvBoB,GAAG,GAAGpB,OAAO,GAAGmB,GAAG;MACnBQ,EAAE,GAAGT,GAAG,GAAGE,GAAG,IAAIM,EAAE,GAAGT,GAAG,GAAGE,GAAG,GAAGD,GAAG,GAAGC,GAAG,GAAGF,GAAG,GAAGG,GAAG,CAAC;MACzDC,EAAE,GAAGI,EAAE,GAAGE,EAAE;MACZZ,KAAK,GAAGU,EAAE,GAAGJ,EAAE;MACfpD,IAAI,CAAC,CAAC,CAAC,GAAGwD,EAAE,IAAIJ,EAAE,GAAGN,KAAK,CAAC,IAAIA,KAAK,GAAGY,EAAE,CAAC;MAC1CL,EAAE,GAAGE,EAAE,GAAGH,EAAE;MACZN,KAAK,GAAGO,EAAE,GAAGE,EAAE;MACfD,EAAE,GAAGC,EAAE,IAAIF,EAAE,GAAGP,KAAK,CAAC,IAAIM,EAAE,GAAGN,KAAK,CAAC;MACrCM,EAAE,GAAGE,EAAE,GAAGG,EAAE;MACZX,KAAK,GAAGQ,EAAE,GAAGF,EAAE;MACfpD,IAAI,CAAC,CAAC,CAAC,GAAGsD,EAAE,IAAIF,EAAE,GAAGN,KAAK,CAAC,IAAIA,KAAK,GAAGW,EAAE,CAAC;MAC1CE,EAAE,GAAGN,EAAE,GAAGD,EAAE;MACZN,KAAK,GAAGa,EAAE,GAAGN,EAAE;MACfrD,IAAI,CAAC,CAAC,CAAC,GAAGqD,EAAE,IAAIM,EAAE,GAAGb,KAAK,CAAC,IAAIM,EAAE,GAAGN,KAAK,CAAC;MAC1C9C,IAAI,CAAC,CAAC,CAAC,GAAG2D,EAAE;MACZhB,OAAO,GAAG,CAAC;IACf,CAAC,MAAM;MACH9C,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;MACV2C,MAAM,GAAG,CAAC;MACVxC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC;MACX2C,OAAO,GAAG,CAAC;IACf;IACA,IAAIhB,OAAO,KAAK,CAAC,EAAE;MACf,MAAM2C,GAAG,GAAG7F,KAAK,CAAC+D,MAAM,EAAE3C,GAAG,EAAE8B,OAAO,EAAEvB,IAAI,CAAC;MAC7CQ,MAAM,GAAGD,MAAM,CAACC,MAAM,EAAErC,GAAG,CACvBE,KAAK,CAACyD,QAAQ,EAAE3C,KAAK,EAAEoC,OAAO,EAAEzB,GAAG,CAAC,EAAEA,GAAG,EACzCzB,KAAK,CAAC6F,GAAG,EAAElE,IAAI,EAAE,CAAC,GAAGyD,GAAG,EAAExD,GAAG,CAAC,EAAEA,GAAG,EAAEE,GAAG,CAAC,EAAEA,GAAG,CAAC;MAEnD,MAAMgE,IAAI,GAAG9F,KAAK,CAACkE,OAAO,EAAE3C,IAAI,EAAE2B,OAAO,EAAE1B,EAAE,CAAC;MAC9CW,MAAM,GAAGD,MAAM,CAACC,MAAM,EAAEpC,SAAS,CAC7BC,KAAK,CAAC8F,IAAI,EAAEtE,EAAE,EAAE,CAAC,GAAG4D,GAAG,EAAE3D,GAAG,CAAC,EAAEA,GAAG,EAClCzB,KAAK,CAAC8F,IAAI,EAAEtE,EAAE,EAAE0B,OAAO,EAAExB,IAAI,CAAC,EAAEA,IAAI,EACpC1B,KAAK,CAAC6F,GAAG,EAAElE,IAAI,EAAEuB,OAAO,EAAEtB,GAAG,CAAC,EAAEA,GAAG,EAAEC,IAAI,EAAEE,GAAG,CAAC,EAAEA,GAAG,CAAC;MAEzD,IAAIuB,OAAO,KAAK,CAAC,EAAE;QACfnB,MAAM,GAAGD,MAAM,CAACC,MAAM,EAAEnC,KAAK,CAACA,KAAK,CAAC,CAAC,EAAEO,EAAE,EAAE2C,OAAO,EAAE1B,EAAE,CAAC,EAAEA,EAAE,EAAE8B,OAAO,EAAE7B,GAAG,CAAC,EAAEA,GAAG,CAAC;MACpF;MACA,IAAI2B,OAAO,KAAK,CAAC,EAAE;QACfjB,MAAM,GAAGD,MAAM,CAACC,MAAM,EAAEnC,KAAK,CAACA,KAAK,CAAC,CAAC,EAAES,EAAE,EAAE,CAACyC,OAAO,EAAE1B,EAAE,CAAC,EAAEA,EAAE,EAAE4B,OAAO,EAAE3B,GAAG,CAAC,EAAEA,GAAG,CAAC;MACrF;IACJ;IACA,IAAI4B,OAAO,KAAK,CAAC,EAAE;MACf,MAAMwC,GAAG,GAAG7F,KAAK,CAAC+D,MAAM,EAAE3C,GAAG,EAAEiC,OAAO,EAAE1B,IAAI,CAAC;MAC7CQ,MAAM,GAAGD,MAAM,CAACC,MAAM,EAAErC,GAAG,CACvBE,KAAK,CAAC0D,QAAQ,EAAE3C,KAAK,EAAEsC,OAAO,EAAE5B,GAAG,CAAC,EAAEA,GAAG,EACzCzB,KAAK,CAAC6F,GAAG,EAAElE,IAAI,EAAE,CAAC,GAAG4D,GAAG,EAAE3D,GAAG,CAAC,EAAEA,GAAG,EAAEE,GAAG,CAAC,EAAEA,GAAG,CAAC;MAEnD,MAAMgE,IAAI,GAAG9F,KAAK,CAACkE,OAAO,EAAE3C,IAAI,EAAE8B,OAAO,EAAE7B,EAAE,CAAC;MAC9CW,MAAM,GAAGD,MAAM,CAACC,MAAM,EAAEpC,SAAS,CAC7BC,KAAK,CAAC8F,IAAI,EAAEtE,EAAE,EAAE,CAAC,GAAG+D,GAAG,EAAE9D,GAAG,CAAC,EAAEA,GAAG,EAClCzB,KAAK,CAAC8F,IAAI,EAAEtE,EAAE,EAAE6B,OAAO,EAAE3B,IAAI,CAAC,EAAEA,IAAI,EACpC1B,KAAK,CAAC6F,GAAG,EAAElE,IAAI,EAAE0B,OAAO,EAAEzB,GAAG,CAAC,EAAEA,GAAG,EAAGC,IAAI,EAAEE,GAAG,CAAC,EAAEA,GAAG,CAAC;IAC9D;EACJ;EACA,IAAIoB,OAAO,KAAK,CAAC,IAAIG,OAAO,KAAK,CAAC,EAAE;IAChC,IAAIL,OAAO,KAAK,CAAC,IAAIG,OAAO,KAAK,CAAC,IAAIF,OAAO,KAAK,CAAC,IAAIG,OAAO,KAAK,CAAC,EAAE;MAClEyB,EAAE,GAAG7B,OAAO,GAAGsC,GAAG;MAClBjB,CAAC,GAAG5E,QAAQ,GAAGuD,OAAO;MACtBsB,GAAG,GAAGD,CAAC,IAAIA,CAAC,GAAGrB,OAAO,CAAC;MACvBuB,GAAG,GAAGvB,OAAO,GAAGsB,GAAG;MACnBD,CAAC,GAAG5E,QAAQ,GAAG6F,GAAG;MAClBd,GAAG,GAAGH,CAAC,IAAIA,CAAC,GAAGiB,GAAG,CAAC;MACnBb,GAAG,GAAGa,GAAG,GAAGd,GAAG;MACfM,EAAE,GAAGP,GAAG,GAAGE,GAAG,IAAII,EAAE,GAAGP,GAAG,GAAGE,GAAG,GAAGD,GAAG,GAAGC,GAAG,GAAGF,GAAG,GAAGG,GAAG,CAAC;MACzDM,EAAE,GAAGG,GAAG,GAAG9B,OAAO;MAClBiB,CAAC,GAAG5E,QAAQ,GAAGyF,GAAG;MAClBZ,GAAG,GAAGD,CAAC,IAAIA,CAAC,GAAGa,GAAG,CAAC;MACnBX,GAAG,GAAGW,GAAG,GAAGZ,GAAG;MACfD,CAAC,GAAG5E,QAAQ,GAAG2D,OAAO;MACtBoB,GAAG,GAAGH,CAAC,IAAIA,CAAC,GAAGjB,OAAO,CAAC;MACvBqB,GAAG,GAAGrB,OAAO,GAAGoB,GAAG;MACnBQ,EAAE,GAAGT,GAAG,GAAGE,GAAG,IAAIM,EAAE,GAAGT,GAAG,GAAGE,GAAG,GAAGD,GAAG,GAAGC,GAAG,GAAGF,GAAG,GAAGG,GAAG,CAAC;MACzDC,EAAE,GAAGI,EAAE,GAAGE,EAAE;MACZZ,KAAK,GAAGM,EAAE,GAAGI,EAAE;MACfrE,CAAC,CAAC,CAAC,CAAC,GAAGqE,EAAE,IAAIJ,EAAE,GAAGN,KAAK,CAAC,IAAIY,EAAE,GAAGZ,KAAK,CAAC;MACvCO,EAAE,GAAGE,EAAE,GAAGH,EAAE;MACZN,KAAK,GAAGO,EAAE,GAAGE,EAAE;MACfD,EAAE,GAAGC,EAAE,IAAIF,EAAE,GAAGP,KAAK,CAAC,IAAIM,EAAE,GAAGN,KAAK,CAAC;MACrCM,EAAE,GAAGE,EAAE,GAAGG,EAAE;MACZX,KAAK,GAAGM,EAAE,GAAGE,EAAE;MACfnE,CAAC,CAAC,CAAC,CAAC,GAAGmE,EAAE,IAAIF,EAAE,GAAGN,KAAK,CAAC,IAAIW,EAAE,GAAGX,KAAK,CAAC;MACvCa,EAAE,GAAGN,EAAE,GAAGD,EAAE;MACZN,KAAK,GAAGa,EAAE,GAAGN,EAAE;MACflE,CAAC,CAAC,CAAC,CAAC,GAAGkE,EAAE,IAAIM,EAAE,GAAGb,KAAK,CAAC,IAAIM,EAAE,GAAGN,KAAK,CAAC;MACvC3D,CAAC,CAAC,CAAC,CAAC,GAAGwE,EAAE;MACTf,EAAE,GAAG,CAACmB,GAAG;MACTlB,EAAE,GAAG,CAAChB,OAAO;MACb0B,EAAE,GAAG5B,OAAO,GAAGiB,EAAE;MACjBG,CAAC,GAAG5E,QAAQ,GAAGwD,OAAO;MACtBqB,GAAG,GAAGD,CAAC,IAAIA,CAAC,GAAGpB,OAAO,CAAC;MACvBsB,GAAG,GAAGtB,OAAO,GAAGqB,GAAG;MACnBD,CAAC,GAAG5E,QAAQ,GAAGyE,EAAE;MACjBM,GAAG,GAAGH,CAAC,IAAIA,CAAC,GAAGH,EAAE,CAAC;MAClBO,GAAG,GAAGP,EAAE,GAAGM,GAAG;MACdM,EAAE,GAAGP,GAAG,GAAGE,GAAG,IAAII,EAAE,GAAGP,GAAG,GAAGE,GAAG,GAAGD,GAAG,GAAGC,GAAG,GAAGF,GAAG,GAAGG,GAAG,CAAC;MACzDM,EAAE,GAAGI,GAAG,GAAGhB,EAAE;MACbE,CAAC,GAAG5E,QAAQ,GAAG0F,GAAG;MAClBb,GAAG,GAAGD,CAAC,IAAIA,CAAC,GAAGc,GAAG,CAAC;MACnBZ,GAAG,GAAGY,GAAG,GAAGb,GAAG;MACfD,CAAC,GAAG5E,QAAQ,GAAG0E,EAAE;MACjBK,GAAG,GAAGH,CAAC,IAAIA,CAAC,GAAGF,EAAE,CAAC;MAClBM,GAAG,GAAGN,EAAE,GAAGK,GAAG;MACdQ,EAAE,GAAGT,GAAG,GAAGE,GAAG,IAAIM,EAAE,GAAGT,GAAG,GAAGE,GAAG,GAAGD,GAAG,GAAGC,GAAG,GAAGF,GAAG,GAAGG,GAAG,CAAC;MACzDC,EAAE,GAAGI,EAAE,GAAGE,EAAE;MACZZ,KAAK,GAAGM,EAAE,GAAGI,EAAE;MACfpE,CAAC,CAAC,CAAC,CAAC,GAAGoE,EAAE,IAAIJ,EAAE,GAAGN,KAAK,CAAC,IAAIY,EAAE,GAAGZ,KAAK,CAAC;MACvCO,EAAE,GAAGE,EAAE,GAAGH,EAAE;MACZN,KAAK,GAAGO,EAAE,GAAGE,EAAE;MACfD,EAAE,GAAGC,EAAE,IAAIF,EAAE,GAAGP,KAAK,CAAC,IAAIM,EAAE,GAAGN,KAAK,CAAC;MACrCM,EAAE,GAAGE,EAAE,GAAGG,EAAE;MACZX,KAAK,GAAGM,EAAE,GAAGE,EAAE;MACflE,CAAC,CAAC,CAAC,CAAC,GAAGkE,EAAE,IAAIF,EAAE,GAAGN,KAAK,CAAC,IAAIW,EAAE,GAAGX,KAAK,CAAC;MACvCa,EAAE,GAAGN,EAAE,GAAGD,EAAE;MACZN,KAAK,GAAGa,EAAE,GAAGN,EAAE;MACfjE,CAAC,CAAC,CAAC,CAAC,GAAGiE,EAAE,IAAIM,EAAE,GAAGb,KAAK,CAAC,IAAIM,EAAE,GAAGN,KAAK,CAAC;MACvC1D,CAAC,CAAC,CAAC,CAAC,GAAGuE,EAAE;MACTrB,MAAM,GAAG/D,GAAG,CAAC,CAAC,EAAEY,CAAC,EAAE,CAAC,EAAEC,CAAC,EAAEO,GAAG,CAAC;MAC7B4D,EAAE,GAAG7B,OAAO,GAAGI,OAAO;MACtBiB,CAAC,GAAG5E,QAAQ,GAAGuD,OAAO;MACtBsB,GAAG,GAAGD,CAAC,IAAIA,CAAC,GAAGrB,OAAO,CAAC;MACvBuB,GAAG,GAAGvB,OAAO,GAAGsB,GAAG;MACnBD,CAAC,GAAG5E,QAAQ,GAAG2D,OAAO;MACtBoB,GAAG,GAAGH,CAAC,IAAIA,CAAC,GAAGjB,OAAO,CAAC;MACvBqB,GAAG,GAAGrB,OAAO,GAAGoB,GAAG;MACnBM,EAAE,GAAGP,GAAG,GAAGE,GAAG,IAAII,EAAE,GAAGP,GAAG,GAAGE,GAAG,GAAGD,GAAG,GAAGC,GAAG,GAAGF,GAAG,GAAGG,GAAG,CAAC;MACzDM,EAAE,GAAG9B,OAAO,GAAGE,OAAO;MACtBkB,CAAC,GAAG5E,QAAQ,GAAGwD,OAAO;MACtBqB,GAAG,GAAGD,CAAC,IAAIA,CAAC,GAAGpB,OAAO,CAAC;MACvBsB,GAAG,GAAGtB,OAAO,GAAGqB,GAAG;MACnBD,CAAC,GAAG5E,QAAQ,GAAG0D,OAAO;MACtBqB,GAAG,GAAGH,CAAC,IAAIA,CAAC,GAAGlB,OAAO,CAAC;MACvBsB,GAAG,GAAGtB,OAAO,GAAGqB,GAAG;MACnBQ,EAAE,GAAGT,GAAG,GAAGE,GAAG,IAAIM,EAAE,GAAGT,GAAG,GAAGE,GAAG,GAAGD,GAAG,GAAGC,GAAG,GAAGF,GAAG,GAAGG,GAAG,CAAC;MACzDC,EAAE,GAAGI,EAAE,GAAGE,EAAE;MACZZ,KAAK,GAAGU,EAAE,GAAGJ,EAAE;MACftD,IAAI,CAAC,CAAC,CAAC,GAAG0D,EAAE,IAAIJ,EAAE,GAAGN,KAAK,CAAC,IAAIA,KAAK,GAAGY,EAAE,CAAC;MAC1CL,EAAE,GAAGE,EAAE,GAAGH,EAAE;MACZN,KAAK,GAAGO,EAAE,GAAGE,EAAE;MACfD,EAAE,GAAGC,EAAE,IAAIF,EAAE,GAAGP,KAAK,CAAC,IAAIM,EAAE,GAAGN,KAAK,CAAC;MACrCM,EAAE,GAAGE,EAAE,GAAGG,EAAE;MACZX,KAAK,GAAGQ,EAAE,GAAGF,EAAE;MACftD,IAAI,CAAC,CAAC,CAAC,GAAGwD,EAAE,IAAIF,EAAE,GAAGN,KAAK,CAAC,IAAIA,KAAK,GAAGW,EAAE,CAAC;MAC1CE,EAAE,GAAGN,EAAE,GAAGD,EAAE;MACZN,KAAK,GAAGa,EAAE,GAAGN,EAAE;MACfvD,IAAI,CAAC,CAAC,CAAC,GAAGuD,EAAE,IAAIM,EAAE,GAAGb,KAAK,CAAC,IAAIM,EAAE,GAAGN,KAAK,CAAC;MAC1ChD,IAAI,CAAC,CAAC,CAAC,GAAG6D,EAAE;MACZlB,OAAO,GAAG,CAAC;IACf,CAAC,MAAM;MACH9C,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;MACV2C,MAAM,GAAG,CAAC;MACVxC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC;MACX2C,OAAO,GAAG,CAAC;IACf;IACA,IAAIb,OAAO,KAAK,CAAC,EAAE;MACf,MAAM0C,GAAG,GAAG7F,KAAK,CAAC6D,MAAM,EAAE3C,GAAG,EAAEiC,OAAO,EAAExB,IAAI,CAAC;MAC7CQ,MAAM,GAAGD,MAAM,CAACC,MAAM,EAAErC,GAAG,CACvBE,KAAK,CAAC2D,QAAQ,EAAE3C,KAAK,EAAEmC,OAAO,EAAE1B,GAAG,CAAC,EAAEA,GAAG,EACzCzB,KAAK,CAAC6F,GAAG,EAAElE,IAAI,EAAE,CAAC,GAAG0D,GAAG,EAAEzD,GAAG,CAAC,EAAEA,GAAG,EAAEE,GAAG,CAAC,EAAEA,GAAG,CAAC;MAEnD,MAAMgE,IAAI,GAAG9F,KAAK,CAACgE,OAAO,EAAE3C,IAAI,EAAE8B,OAAO,EAAE3B,EAAE,CAAC;MAC9CW,MAAM,GAAGD,MAAM,CAACC,MAAM,EAAEpC,SAAS,CAC7BC,KAAK,CAAC8F,IAAI,EAAEtE,EAAE,EAAE,CAAC,GAAG6D,GAAG,EAAE5D,GAAG,CAAC,EAAEA,GAAG,EAClCzB,KAAK,CAAC8F,IAAI,EAAEtE,EAAE,EAAE2B,OAAO,EAAEzB,IAAI,CAAC,EAAEA,IAAI,EACpC1B,KAAK,CAAC6F,GAAG,EAAElE,IAAI,EAAEwB,OAAO,EAAEvB,GAAG,CAAC,EAAEA,GAAG,EAAEC,IAAI,EAAEE,GAAG,CAAC,EAAEA,GAAG,CAAC;MAEzD,IAAIqB,OAAO,KAAK,CAAC,EAAE;QACfjB,MAAM,GAAGD,MAAM,CAACC,MAAM,EAAEnC,KAAK,CAACA,KAAK,CAAC,CAAC,EAAEQ,EAAE,EAAE2C,OAAO,EAAE3B,EAAE,CAAC,EAAEA,EAAE,EAAE4B,OAAO,EAAE3B,GAAG,CAAC,EAAEA,GAAG,CAAC;MACpF;MACA,IAAI4B,OAAO,KAAK,CAAC,EAAE;QACflB,MAAM,GAAGD,MAAM,CAACC,MAAM,EAAEnC,KAAK,CAACA,KAAK,CAAC,CAAC,EAAEO,EAAE,EAAE,CAAC4C,OAAO,EAAE3B,EAAE,CAAC,EAAEA,EAAE,EAAE6B,OAAO,EAAE5B,GAAG,CAAC,EAAEA,GAAG,CAAC;MACrF;IACJ;IACA,IAAI6B,OAAO,KAAK,CAAC,EAAE;MACf,MAAMuC,GAAG,GAAG7F,KAAK,CAAC6D,MAAM,EAAE3C,GAAG,EAAEoC,OAAO,EAAE3B,IAAI,CAAC;MAC7CQ,MAAM,GAAGD,MAAM,CAACC,MAAM,EAAErC,GAAG,CACvBE,KAAK,CAAC4D,QAAQ,EAAE3C,KAAK,EAAEqC,OAAO,EAAE7B,GAAG,CAAC,EAAEA,GAAG,EACzCzB,KAAK,CAAC6F,GAAG,EAAElE,IAAI,EAAE,CAAC,GAAG6D,GAAG,EAAE5D,GAAG,CAAC,EAAEA,GAAG,EAAEE,GAAG,CAAC,EAAEA,GAAG,CAAC;MAEnD,MAAMgE,IAAI,GAAG9F,KAAK,CAACgE,OAAO,EAAE3C,IAAI,EAAEiC,OAAO,EAAE9B,EAAE,CAAC;MAC9CW,MAAM,GAAGD,MAAM,CAACC,MAAM,EAAEpC,SAAS,CAC7BC,KAAK,CAAC8F,IAAI,EAAEtE,EAAE,EAAE,CAAC,GAAGgE,GAAG,EAAE/D,GAAG,CAAC,EAAEA,GAAG,EAClCzB,KAAK,CAAC8F,IAAI,EAAEtE,EAAE,EAAE8B,OAAO,EAAE5B,IAAI,CAAC,EAAEA,IAAI,EACpC1B,KAAK,CAAC6F,GAAG,EAAElE,IAAI,EAAE2B,OAAO,EAAE1B,GAAG,CAAC,EAAEA,GAAG,EAAEC,IAAI,EAAEE,GAAG,CAAC,EAAEA,GAAG,CAAC;IAC7D;EACJ;EAEA,OAAOC,GAAG,CAACG,MAAM,GAAG,CAAC,CAAC;AAC1B;AAEA,OAAO,SAAS4D,QAAQA,CAACvD,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;EACrD,MAAMoC,GAAG,GAAG3C,EAAE,GAAGM,EAAE;EACnB,MAAMsC,GAAG,GAAG1C,EAAE,GAAGI,EAAE;EACnB,MAAMuC,GAAG,GAAGzC,EAAE,GAAGE,EAAE;EACnB,MAAMwC,GAAG,GAAG7C,EAAE,GAAGM,EAAE;EACnB,MAAMwC,GAAG,GAAG5C,EAAE,GAAGI,EAAE;EACnB,MAAMyC,GAAG,GAAG3C,EAAE,GAAGE,EAAE;EAEnB,MAAMiD,MAAM,GAAGZ,GAAG,GAAGI,GAAG;EACxB,MAAMS,MAAM,GAAGZ,GAAG,GAAGE,GAAG;EACxB,MAAMW,KAAK,GAAGf,GAAG,GAAGA,GAAG,GAAGG,GAAG,GAAGA,GAAG;EAEnC,MAAMa,MAAM,GAAGd,GAAG,GAAGC,GAAG;EACxB,MAAMc,MAAM,GAAGjB,GAAG,GAAGK,GAAG;EACxB,MAAMa,KAAK,GAAGjB,GAAG,GAAGA,GAAG,GAAGG,GAAG,GAAGA,GAAG;EAEnC,MAAMe,MAAM,GAAGnB,GAAG,GAAGI,GAAG;EACxB,MAAMgB,MAAM,GAAGnB,GAAG,GAAGE,GAAG;EACxB,MAAMkB,KAAK,GAAGnB,GAAG,GAAGA,GAAG,GAAGG,GAAG,GAAGA,GAAG;EAEnC,MAAMC,GAAG,GACLS,KAAK,IAAIF,MAAM,GAAGC,MAAM,CAAC,GACzBI,KAAK,IAAIF,MAAM,GAAGC,MAAM,CAAC,GACzBI,KAAK,IAAIF,MAAM,GAAGC,MAAM,CAAC;EAE7B,MAAMvD,SAAS,GACX,CAAC2C,IAAI,CAACC,GAAG,CAACI,MAAM,CAAC,GAAGL,IAAI,CAACC,GAAG,CAACK,MAAM,CAAC,IAAIC,KAAK,GAC7C,CAACP,IAAI,CAACC,GAAG,CAACO,MAAM,CAAC,GAAGR,IAAI,CAACC,GAAG,CAACQ,MAAM,CAAC,IAAIC,KAAK,GAC7C,CAACV,IAAI,CAACC,GAAG,CAACU,MAAM,CAAC,GAAGX,IAAI,CAACC,GAAG,CAACW,MAAM,CAAC,IAAIC,KAAK;EAEjD,MAAMd,QAAQ,GAAGzF,YAAY,GAAG+C,SAAS;EAEzC,IAAIyC,GAAG,GAAGC,QAAQ,IAAI,CAACD,GAAG,GAAGC,QAAQ,EAAE;IACnC,OAAOD,GAAG;EACd;EACA,OAAOlD,aAAa,CAACC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,SAAS,CAAC;AACnE;AAEA,OAAO,SAASyD,YAAYA,CAACjE,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;EACzD,MAAMoC,GAAG,GAAG3C,EAAE,GAAGM,EAAE;EACnB,MAAMwC,GAAG,GAAG7C,EAAE,GAAGM,EAAE;EACnB,MAAMqC,GAAG,GAAG1C,EAAE,GAAGI,EAAE;EACnB,MAAMyC,GAAG,GAAG5C,EAAE,GAAGI,EAAE;EACnB,MAAMsC,GAAG,GAAGzC,EAAE,GAAGE,EAAE;EACnB,MAAM0C,GAAG,GAAG3C,EAAE,GAAGE,EAAE;EAEnB,MAAM2D,KAAK,GAAGvB,GAAG,GAAGI,GAAG,GAAGH,GAAG,GAAGE,GAAG;EACnC,MAAMqB,KAAK,GAAGvB,GAAG,GAAGI,GAAG,GAAGH,GAAG,GAAGE,GAAG;EACnC,MAAMqB,KAAK,GAAGvB,GAAG,GAAGC,GAAG,GAAGH,GAAG,GAAGK,GAAG;EACnC,MAAMU,KAAK,GAAGf,GAAG,GAAGA,GAAG,GAAGG,GAAG,GAAGA,GAAG;EACnC,MAAMe,KAAK,GAAGjB,GAAG,GAAGA,GAAG,GAAGG,GAAG,GAAGA,GAAG;EACnC,MAAMiB,KAAK,GAAGnB,GAAG,GAAGA,GAAG,GAAGG,GAAG,GAAGA,GAAG;EAEnC,OAAOU,KAAK,GAAGS,KAAK,GAAGN,KAAK,GAAGO,KAAK,GAAGJ,KAAK,GAAGE,KAAK;AACxD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}