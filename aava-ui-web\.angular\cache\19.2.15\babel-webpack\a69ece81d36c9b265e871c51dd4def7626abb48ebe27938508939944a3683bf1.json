{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { compareBy, numberComparator } from '../../../../../base/common/arrays.js';\nimport { findFirstMax } from '../../../../../base/common/arraysFind.js';\nimport { Emitter, Event } from '../../../../../base/common/event.js';\nimport { Disposable } from '../../../../../base/common/lifecycle.js';\nimport { Position } from '../../../../common/core/position.js';\nimport { Range } from '../../../../common/core/range.js';\nimport { SingleTextEdit } from '../../../../common/core/textEdit.js';\nimport { SelectedSuggestionInfo } from '../../../../common/languages.js';\nimport { singleTextEditAugments, singleTextRemoveCommonPrefix } from './singleTextEdit.js';\nimport { SnippetParser } from '../../../snippet/browser/snippetParser.js';\nimport { SnippetSession } from '../../../snippet/browser/snippetSession.js';\nimport { SuggestController } from '../../../suggest/browser/suggestController.js';\nexport class SuggestWidgetAdaptor extends Disposable {\n  get selectedItem() {\n    return this._currentSuggestItemInfo;\n  }\n  constructor(editor, suggestControllerPreselector, onWillAccept) {\n    super();\n    this.editor = editor;\n    this.suggestControllerPreselector = suggestControllerPreselector;\n    this.onWillAccept = onWillAccept;\n    this.isSuggestWidgetVisible = false;\n    this.isShiftKeyPressed = false;\n    this._isActive = false;\n    this._currentSuggestItemInfo = undefined;\n    this._onDidSelectedItemChange = this._register(new Emitter());\n    this.onDidSelectedItemChange = this._onDidSelectedItemChange.event;\n    // See the command acceptAlternativeSelectedSuggestion that is bound to shift+tab\n    this._register(editor.onKeyDown(e => {\n      if (e.shiftKey && !this.isShiftKeyPressed) {\n        this.isShiftKeyPressed = true;\n        this.update(this._isActive);\n      }\n    }));\n    this._register(editor.onKeyUp(e => {\n      if (e.shiftKey && this.isShiftKeyPressed) {\n        this.isShiftKeyPressed = false;\n        this.update(this._isActive);\n      }\n    }));\n    const suggestController = SuggestController.get(this.editor);\n    if (suggestController) {\n      this._register(suggestController.registerSelector({\n        priority: 100,\n        select: (model, pos, suggestItems) => {\n          const textModel = this.editor.getModel();\n          if (!textModel) {\n            // Should not happen\n            return -1;\n          }\n          const i = this.suggestControllerPreselector();\n          const itemToPreselect = i ? singleTextRemoveCommonPrefix(i, textModel) : undefined;\n          if (!itemToPreselect) {\n            return -1;\n          }\n          const position = Position.lift(pos);\n          const candidates = suggestItems.map((suggestItem, index) => {\n            const suggestItemInfo = SuggestItemInfo.fromSuggestion(suggestController, textModel, position, suggestItem, this.isShiftKeyPressed);\n            const suggestItemTextEdit = singleTextRemoveCommonPrefix(suggestItemInfo.toSingleTextEdit(), textModel);\n            const valid = singleTextEditAugments(itemToPreselect, suggestItemTextEdit);\n            return {\n              index,\n              valid,\n              prefixLength: suggestItemTextEdit.text.length,\n              suggestItem\n            };\n          }).filter(item => item && item.valid && item.prefixLength > 0);\n          const result = findFirstMax(candidates, compareBy(s => s.prefixLength, numberComparator));\n          return result ? result.index : -1;\n        }\n      }));\n      let isBoundToSuggestWidget = false;\n      const bindToSuggestWidget = () => {\n        if (isBoundToSuggestWidget) {\n          return;\n        }\n        isBoundToSuggestWidget = true;\n        this._register(suggestController.widget.value.onDidShow(() => {\n          this.isSuggestWidgetVisible = true;\n          this.update(true);\n        }));\n        this._register(suggestController.widget.value.onDidHide(() => {\n          this.isSuggestWidgetVisible = false;\n          this.update(false);\n        }));\n        this._register(suggestController.widget.value.onDidFocus(() => {\n          this.isSuggestWidgetVisible = true;\n          this.update(true);\n        }));\n      };\n      this._register(Event.once(suggestController.model.onDidTrigger)(e => {\n        bindToSuggestWidget();\n      }));\n      this._register(suggestController.onWillInsertSuggestItem(e => {\n        const position = this.editor.getPosition();\n        const model = this.editor.getModel();\n        if (!position || !model) {\n          return undefined;\n        }\n        const suggestItemInfo = SuggestItemInfo.fromSuggestion(suggestController, model, position, e.item, this.isShiftKeyPressed);\n        this.onWillAccept(suggestItemInfo);\n      }));\n    }\n    this.update(this._isActive);\n  }\n  update(newActive) {\n    const newInlineCompletion = this.getSuggestItemInfo();\n    if (this._isActive !== newActive || !suggestItemInfoEquals(this._currentSuggestItemInfo, newInlineCompletion)) {\n      this._isActive = newActive;\n      this._currentSuggestItemInfo = newInlineCompletion;\n      this._onDidSelectedItemChange.fire();\n    }\n  }\n  getSuggestItemInfo() {\n    const suggestController = SuggestController.get(this.editor);\n    if (!suggestController || !this.isSuggestWidgetVisible) {\n      return undefined;\n    }\n    const focusedItem = suggestController.widget.value.getFocusedItem();\n    const position = this.editor.getPosition();\n    const model = this.editor.getModel();\n    if (!focusedItem || !position || !model) {\n      return undefined;\n    }\n    return SuggestItemInfo.fromSuggestion(suggestController, model, position, focusedItem.item, this.isShiftKeyPressed);\n  }\n  stopForceRenderingAbove() {\n    const suggestController = SuggestController.get(this.editor);\n    suggestController?.stopForceRenderingAbove();\n  }\n  forceRenderingAbove() {\n    const suggestController = SuggestController.get(this.editor);\n    suggestController?.forceRenderingAbove();\n  }\n}\nexport class SuggestItemInfo {\n  static fromSuggestion(suggestController, model, position, item, toggleMode) {\n    let {\n      insertText\n    } = item.completion;\n    let isSnippetText = false;\n    if (item.completion.insertTextRules & 4 /* CompletionItemInsertTextRule.InsertAsSnippet */) {\n      const snippet = new SnippetParser().parse(insertText);\n      if (snippet.children.length < 100) {\n        // Adjust whitespace is expensive.\n        SnippetSession.adjustWhitespace(model, position, true, snippet);\n      }\n      insertText = snippet.toString();\n      isSnippetText = true;\n    }\n    const info = suggestController.getOverwriteInfo(item, toggleMode);\n    return new SuggestItemInfo(Range.fromPositions(position.delta(0, -info.overwriteBefore), position.delta(0, Math.max(info.overwriteAfter, 0))), insertText, item.completion.kind, isSnippetText);\n  }\n  constructor(range, insertText, completionItemKind, isSnippetText) {\n    this.range = range;\n    this.insertText = insertText;\n    this.completionItemKind = completionItemKind;\n    this.isSnippetText = isSnippetText;\n  }\n  equals(other) {\n    return this.range.equalsRange(other.range) && this.insertText === other.insertText && this.completionItemKind === other.completionItemKind && this.isSnippetText === other.isSnippetText;\n  }\n  toSelectedSuggestionInfo() {\n    return new SelectedSuggestionInfo(this.range, this.insertText, this.completionItemKind, this.isSnippetText);\n  }\n  toSingleTextEdit() {\n    return new SingleTextEdit(this.range, this.insertText);\n  }\n}\nfunction suggestItemInfoEquals(a, b) {\n  if (a === b) {\n    return true;\n  }\n  if (!a || !b) {\n    return false;\n  }\n  return a.equals(b);\n}", "map": {"version": 3, "names": ["compareBy", "numberComparator", "findFirstMax", "Emitter", "Event", "Disposable", "Position", "Range", "SingleTextEdit", "SelectedSuggestionInfo", "singleTextEditAugments", "singleTextRemoveCommonPrefix", "<PERSON>ni<PERSON><PERSON><PERSON><PERSON><PERSON>", "SnippetSession", "SuggestController", "SuggestWidgetAdaptor", "selectedItem", "_currentSuggestItemInfo", "constructor", "editor", "suggestControllerPreselector", "onWillAccept", "isSuggestWidgetVisible", "isShiftKeyPressed", "_isActive", "undefined", "_onDidSelectedItemChange", "_register", "onDidSelectedItemChange", "event", "onKeyDown", "e", "shift<PERSON>ey", "update", "onKeyUp", "suggestController", "get", "registerSelector", "priority", "select", "model", "pos", "suggestItems", "textModel", "getModel", "i", "itemToPreselect", "position", "lift", "candidates", "map", "suggestItem", "index", "suggestItemInfo", "SuggestItemInfo", "fromSuggestion", "suggestItemTextEdit", "toSingleTextEdit", "valid", "prefixLength", "text", "length", "filter", "item", "result", "s", "isBoundToSuggestWidget", "bindToSuggestWidget", "widget", "value", "onDidShow", "onDidHide", "onDidFocus", "once", "onDidTrigger", "onWillInsertSuggestItem", "getPosition", "newActive", "newInlineCompletion", "getSuggestItemInfo", "suggestItemInfoEquals", "fire", "focusedItem", "getFocusedItem", "stopForceRenderingAbove", "forceRenderingAbove", "toggleMode", "insertText", "completion", "isSnippetText", "insertTextRules", "snippet", "parse", "children", "adjustWhitespace", "toString", "info", "getOverwriteInfo", "fromPositions", "delta", "overwriteBefore", "Math", "max", "overwriteAfter", "kind", "range", "completionItemKind", "equals", "other", "equalsRange", "toSelectedSuggestionInfo", "a", "b"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/contrib/inlineCompletions/browser/model/suggestWidgetAdaptor.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { compareBy, numberComparator } from '../../../../../base/common/arrays.js';\nimport { findFirstMax } from '../../../../../base/common/arraysFind.js';\nimport { Emitter, Event } from '../../../../../base/common/event.js';\nimport { Disposable } from '../../../../../base/common/lifecycle.js';\nimport { Position } from '../../../../common/core/position.js';\nimport { Range } from '../../../../common/core/range.js';\nimport { SingleTextEdit } from '../../../../common/core/textEdit.js';\nimport { SelectedSuggestionInfo } from '../../../../common/languages.js';\nimport { singleTextEditAugments, singleTextRemoveCommonPrefix } from './singleTextEdit.js';\nimport { SnippetParser } from '../../../snippet/browser/snippetParser.js';\nimport { SnippetSession } from '../../../snippet/browser/snippetSession.js';\nimport { SuggestController } from '../../../suggest/browser/suggestController.js';\nexport class SuggestWidgetAdaptor extends Disposable {\n    get selectedItem() {\n        return this._currentSuggestItemInfo;\n    }\n    constructor(editor, suggestControllerPreselector, onWillAccept) {\n        super();\n        this.editor = editor;\n        this.suggestControllerPreselector = suggestControllerPreselector;\n        this.onWillAccept = onWillAccept;\n        this.isSuggestWidgetVisible = false;\n        this.isShiftKeyPressed = false;\n        this._isActive = false;\n        this._currentSuggestItemInfo = undefined;\n        this._onDidSelectedItemChange = this._register(new Emitter());\n        this.onDidSelectedItemChange = this._onDidSelectedItemChange.event;\n        // See the command acceptAlternativeSelectedSuggestion that is bound to shift+tab\n        this._register(editor.onKeyDown(e => {\n            if (e.shiftKey && !this.isShiftKeyPressed) {\n                this.isShiftKeyPressed = true;\n                this.update(this._isActive);\n            }\n        }));\n        this._register(editor.onKeyUp(e => {\n            if (e.shiftKey && this.isShiftKeyPressed) {\n                this.isShiftKeyPressed = false;\n                this.update(this._isActive);\n            }\n        }));\n        const suggestController = SuggestController.get(this.editor);\n        if (suggestController) {\n            this._register(suggestController.registerSelector({\n                priority: 100,\n                select: (model, pos, suggestItems) => {\n                    const textModel = this.editor.getModel();\n                    if (!textModel) {\n                        // Should not happen\n                        return -1;\n                    }\n                    const i = this.suggestControllerPreselector();\n                    const itemToPreselect = i ? singleTextRemoveCommonPrefix(i, textModel) : undefined;\n                    if (!itemToPreselect) {\n                        return -1;\n                    }\n                    const position = Position.lift(pos);\n                    const candidates = suggestItems\n                        .map((suggestItem, index) => {\n                        const suggestItemInfo = SuggestItemInfo.fromSuggestion(suggestController, textModel, position, suggestItem, this.isShiftKeyPressed);\n                        const suggestItemTextEdit = singleTextRemoveCommonPrefix(suggestItemInfo.toSingleTextEdit(), textModel);\n                        const valid = singleTextEditAugments(itemToPreselect, suggestItemTextEdit);\n                        return { index, valid, prefixLength: suggestItemTextEdit.text.length, suggestItem };\n                    })\n                        .filter(item => item && item.valid && item.prefixLength > 0);\n                    const result = findFirstMax(candidates, compareBy(s => s.prefixLength, numberComparator));\n                    return result ? result.index : -1;\n                }\n            }));\n            let isBoundToSuggestWidget = false;\n            const bindToSuggestWidget = () => {\n                if (isBoundToSuggestWidget) {\n                    return;\n                }\n                isBoundToSuggestWidget = true;\n                this._register(suggestController.widget.value.onDidShow(() => {\n                    this.isSuggestWidgetVisible = true;\n                    this.update(true);\n                }));\n                this._register(suggestController.widget.value.onDidHide(() => {\n                    this.isSuggestWidgetVisible = false;\n                    this.update(false);\n                }));\n                this._register(suggestController.widget.value.onDidFocus(() => {\n                    this.isSuggestWidgetVisible = true;\n                    this.update(true);\n                }));\n            };\n            this._register(Event.once(suggestController.model.onDidTrigger)(e => {\n                bindToSuggestWidget();\n            }));\n            this._register(suggestController.onWillInsertSuggestItem(e => {\n                const position = this.editor.getPosition();\n                const model = this.editor.getModel();\n                if (!position || !model) {\n                    return undefined;\n                }\n                const suggestItemInfo = SuggestItemInfo.fromSuggestion(suggestController, model, position, e.item, this.isShiftKeyPressed);\n                this.onWillAccept(suggestItemInfo);\n            }));\n        }\n        this.update(this._isActive);\n    }\n    update(newActive) {\n        const newInlineCompletion = this.getSuggestItemInfo();\n        if (this._isActive !== newActive || !suggestItemInfoEquals(this._currentSuggestItemInfo, newInlineCompletion)) {\n            this._isActive = newActive;\n            this._currentSuggestItemInfo = newInlineCompletion;\n            this._onDidSelectedItemChange.fire();\n        }\n    }\n    getSuggestItemInfo() {\n        const suggestController = SuggestController.get(this.editor);\n        if (!suggestController || !this.isSuggestWidgetVisible) {\n            return undefined;\n        }\n        const focusedItem = suggestController.widget.value.getFocusedItem();\n        const position = this.editor.getPosition();\n        const model = this.editor.getModel();\n        if (!focusedItem || !position || !model) {\n            return undefined;\n        }\n        return SuggestItemInfo.fromSuggestion(suggestController, model, position, focusedItem.item, this.isShiftKeyPressed);\n    }\n    stopForceRenderingAbove() {\n        const suggestController = SuggestController.get(this.editor);\n        suggestController?.stopForceRenderingAbove();\n    }\n    forceRenderingAbove() {\n        const suggestController = SuggestController.get(this.editor);\n        suggestController?.forceRenderingAbove();\n    }\n}\nexport class SuggestItemInfo {\n    static fromSuggestion(suggestController, model, position, item, toggleMode) {\n        let { insertText } = item.completion;\n        let isSnippetText = false;\n        if (item.completion.insertTextRules & 4 /* CompletionItemInsertTextRule.InsertAsSnippet */) {\n            const snippet = new SnippetParser().parse(insertText);\n            if (snippet.children.length < 100) {\n                // Adjust whitespace is expensive.\n                SnippetSession.adjustWhitespace(model, position, true, snippet);\n            }\n            insertText = snippet.toString();\n            isSnippetText = true;\n        }\n        const info = suggestController.getOverwriteInfo(item, toggleMode);\n        return new SuggestItemInfo(Range.fromPositions(position.delta(0, -info.overwriteBefore), position.delta(0, Math.max(info.overwriteAfter, 0))), insertText, item.completion.kind, isSnippetText);\n    }\n    constructor(range, insertText, completionItemKind, isSnippetText) {\n        this.range = range;\n        this.insertText = insertText;\n        this.completionItemKind = completionItemKind;\n        this.isSnippetText = isSnippetText;\n    }\n    equals(other) {\n        return this.range.equalsRange(other.range)\n            && this.insertText === other.insertText\n            && this.completionItemKind === other.completionItemKind\n            && this.isSnippetText === other.isSnippetText;\n    }\n    toSelectedSuggestionInfo() {\n        return new SelectedSuggestionInfo(this.range, this.insertText, this.completionItemKind, this.isSnippetText);\n    }\n    toSingleTextEdit() {\n        return new SingleTextEdit(this.range, this.insertText);\n    }\n}\nfunction suggestItemInfoEquals(a, b) {\n    if (a === b) {\n        return true;\n    }\n    if (!a || !b) {\n        return false;\n    }\n    return a.equals(b);\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,SAAS,EAAEC,gBAAgB,QAAQ,sCAAsC;AAClF,SAASC,YAAY,QAAQ,0CAA0C;AACvE,SAASC,OAAO,EAAEC,KAAK,QAAQ,qCAAqC;AACpE,SAASC,UAAU,QAAQ,yCAAyC;AACpE,SAASC,QAAQ,QAAQ,qCAAqC;AAC9D,SAASC,KAAK,QAAQ,kCAAkC;AACxD,SAASC,cAAc,QAAQ,qCAAqC;AACpE,SAASC,sBAAsB,QAAQ,iCAAiC;AACxE,SAASC,sBAAsB,EAAEC,4BAA4B,QAAQ,qBAAqB;AAC1F,SAASC,aAAa,QAAQ,2CAA2C;AACzE,SAASC,cAAc,QAAQ,4CAA4C;AAC3E,SAASC,iBAAiB,QAAQ,+CAA+C;AACjF,OAAO,MAAMC,oBAAoB,SAASV,UAAU,CAAC;EACjD,IAAIW,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACC,uBAAuB;EACvC;EACAC,WAAWA,CAACC,MAAM,EAAEC,4BAA4B,EAAEC,YAAY,EAAE;IAC5D,KAAK,CAAC,CAAC;IACP,IAAI,CAACF,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,4BAA4B,GAAGA,4BAA4B;IAChE,IAAI,CAACC,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,sBAAsB,GAAG,KAAK;IACnC,IAAI,CAACC,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACP,uBAAuB,GAAGQ,SAAS;IACxC,IAAI,CAACC,wBAAwB,GAAG,IAAI,CAACC,SAAS,CAAC,IAAIxB,OAAO,CAAC,CAAC,CAAC;IAC7D,IAAI,CAACyB,uBAAuB,GAAG,IAAI,CAACF,wBAAwB,CAACG,KAAK;IAClE;IACA,IAAI,CAACF,SAAS,CAACR,MAAM,CAACW,SAAS,CAACC,CAAC,IAAI;MACjC,IAAIA,CAAC,CAACC,QAAQ,IAAI,CAAC,IAAI,CAACT,iBAAiB,EAAE;QACvC,IAAI,CAACA,iBAAiB,GAAG,IAAI;QAC7B,IAAI,CAACU,MAAM,CAAC,IAAI,CAACT,SAAS,CAAC;MAC/B;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,CAACG,SAAS,CAACR,MAAM,CAACe,OAAO,CAACH,CAAC,IAAI;MAC/B,IAAIA,CAAC,CAACC,QAAQ,IAAI,IAAI,CAACT,iBAAiB,EAAE;QACtC,IAAI,CAACA,iBAAiB,GAAG,KAAK;QAC9B,IAAI,CAACU,MAAM,CAAC,IAAI,CAACT,SAAS,CAAC;MAC/B;IACJ,CAAC,CAAC,CAAC;IACH,MAAMW,iBAAiB,GAAGrB,iBAAiB,CAACsB,GAAG,CAAC,IAAI,CAACjB,MAAM,CAAC;IAC5D,IAAIgB,iBAAiB,EAAE;MACnB,IAAI,CAACR,SAAS,CAACQ,iBAAiB,CAACE,gBAAgB,CAAC;QAC9CC,QAAQ,EAAE,GAAG;QACbC,MAAM,EAAEA,CAACC,KAAK,EAAEC,GAAG,EAAEC,YAAY,KAAK;UAClC,MAAMC,SAAS,GAAG,IAAI,CAACxB,MAAM,CAACyB,QAAQ,CAAC,CAAC;UACxC,IAAI,CAACD,SAAS,EAAE;YACZ;YACA,OAAO,CAAC,CAAC;UACb;UACA,MAAME,CAAC,GAAG,IAAI,CAACzB,4BAA4B,CAAC,CAAC;UAC7C,MAAM0B,eAAe,GAAGD,CAAC,GAAGlC,4BAA4B,CAACkC,CAAC,EAAEF,SAAS,CAAC,GAAGlB,SAAS;UAClF,IAAI,CAACqB,eAAe,EAAE;YAClB,OAAO,CAAC,CAAC;UACb;UACA,MAAMC,QAAQ,GAAGzC,QAAQ,CAAC0C,IAAI,CAACP,GAAG,CAAC;UACnC,MAAMQ,UAAU,GAAGP,YAAY,CAC1BQ,GAAG,CAAC,CAACC,WAAW,EAAEC,KAAK,KAAK;YAC7B,MAAMC,eAAe,GAAGC,eAAe,CAACC,cAAc,CAACpB,iBAAiB,EAAEQ,SAAS,EAAEI,QAAQ,EAAEI,WAAW,EAAE,IAAI,CAAC5B,iBAAiB,CAAC;YACnI,MAAMiC,mBAAmB,GAAG7C,4BAA4B,CAAC0C,eAAe,CAACI,gBAAgB,CAAC,CAAC,EAAEd,SAAS,CAAC;YACvG,MAAMe,KAAK,GAAGhD,sBAAsB,CAACoC,eAAe,EAAEU,mBAAmB,CAAC;YAC1E,OAAO;cAAEJ,KAAK;cAAEM,KAAK;cAAEC,YAAY,EAAEH,mBAAmB,CAACI,IAAI,CAACC,MAAM;cAAEV;YAAY,CAAC;UACvF,CAAC,CAAC,CACGW,MAAM,CAACC,IAAI,IAAIA,IAAI,IAAIA,IAAI,CAACL,KAAK,IAAIK,IAAI,CAACJ,YAAY,GAAG,CAAC,CAAC;UAChE,MAAMK,MAAM,GAAG9D,YAAY,CAAC+C,UAAU,EAAEjD,SAAS,CAACiE,CAAC,IAAIA,CAAC,CAACN,YAAY,EAAE1D,gBAAgB,CAAC,CAAC;UACzF,OAAO+D,MAAM,GAAGA,MAAM,CAACZ,KAAK,GAAG,CAAC,CAAC;QACrC;MACJ,CAAC,CAAC,CAAC;MACH,IAAIc,sBAAsB,GAAG,KAAK;MAClC,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;QAC9B,IAAID,sBAAsB,EAAE;UACxB;QACJ;QACAA,sBAAsB,GAAG,IAAI;QAC7B,IAAI,CAACvC,SAAS,CAACQ,iBAAiB,CAACiC,MAAM,CAACC,KAAK,CAACC,SAAS,CAAC,MAAM;UAC1D,IAAI,CAAChD,sBAAsB,GAAG,IAAI;UAClC,IAAI,CAACW,MAAM,CAAC,IAAI,CAAC;QACrB,CAAC,CAAC,CAAC;QACH,IAAI,CAACN,SAAS,CAACQ,iBAAiB,CAACiC,MAAM,CAACC,KAAK,CAACE,SAAS,CAAC,MAAM;UAC1D,IAAI,CAACjD,sBAAsB,GAAG,KAAK;UACnC,IAAI,CAACW,MAAM,CAAC,KAAK,CAAC;QACtB,CAAC,CAAC,CAAC;QACH,IAAI,CAACN,SAAS,CAACQ,iBAAiB,CAACiC,MAAM,CAACC,KAAK,CAACG,UAAU,CAAC,MAAM;UAC3D,IAAI,CAAClD,sBAAsB,GAAG,IAAI;UAClC,IAAI,CAACW,MAAM,CAAC,IAAI,CAAC;QACrB,CAAC,CAAC,CAAC;MACP,CAAC;MACD,IAAI,CAACN,SAAS,CAACvB,KAAK,CAACqE,IAAI,CAACtC,iBAAiB,CAACK,KAAK,CAACkC,YAAY,CAAC,CAAC3C,CAAC,IAAI;QACjEoC,mBAAmB,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC;MACH,IAAI,CAACxC,SAAS,CAACQ,iBAAiB,CAACwC,uBAAuB,CAAC5C,CAAC,IAAI;QAC1D,MAAMgB,QAAQ,GAAG,IAAI,CAAC5B,MAAM,CAACyD,WAAW,CAAC,CAAC;QAC1C,MAAMpC,KAAK,GAAG,IAAI,CAACrB,MAAM,CAACyB,QAAQ,CAAC,CAAC;QACpC,IAAI,CAACG,QAAQ,IAAI,CAACP,KAAK,EAAE;UACrB,OAAOf,SAAS;QACpB;QACA,MAAM4B,eAAe,GAAGC,eAAe,CAACC,cAAc,CAACpB,iBAAiB,EAAEK,KAAK,EAAEO,QAAQ,EAAEhB,CAAC,CAACgC,IAAI,EAAE,IAAI,CAACxC,iBAAiB,CAAC;QAC1H,IAAI,CAACF,YAAY,CAACgC,eAAe,CAAC;MACtC,CAAC,CAAC,CAAC;IACP;IACA,IAAI,CAACpB,MAAM,CAAC,IAAI,CAACT,SAAS,CAAC;EAC/B;EACAS,MAAMA,CAAC4C,SAAS,EAAE;IACd,MAAMC,mBAAmB,GAAG,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACrD,IAAI,IAAI,CAACvD,SAAS,KAAKqD,SAAS,IAAI,CAACG,qBAAqB,CAAC,IAAI,CAAC/D,uBAAuB,EAAE6D,mBAAmB,CAAC,EAAE;MAC3G,IAAI,CAACtD,SAAS,GAAGqD,SAAS;MAC1B,IAAI,CAAC5D,uBAAuB,GAAG6D,mBAAmB;MAClD,IAAI,CAACpD,wBAAwB,CAACuD,IAAI,CAAC,CAAC;IACxC;EACJ;EACAF,kBAAkBA,CAAA,EAAG;IACjB,MAAM5C,iBAAiB,GAAGrB,iBAAiB,CAACsB,GAAG,CAAC,IAAI,CAACjB,MAAM,CAAC;IAC5D,IAAI,CAACgB,iBAAiB,IAAI,CAAC,IAAI,CAACb,sBAAsB,EAAE;MACpD,OAAOG,SAAS;IACpB;IACA,MAAMyD,WAAW,GAAG/C,iBAAiB,CAACiC,MAAM,CAACC,KAAK,CAACc,cAAc,CAAC,CAAC;IACnE,MAAMpC,QAAQ,GAAG,IAAI,CAAC5B,MAAM,CAACyD,WAAW,CAAC,CAAC;IAC1C,MAAMpC,KAAK,GAAG,IAAI,CAACrB,MAAM,CAACyB,QAAQ,CAAC,CAAC;IACpC,IAAI,CAACsC,WAAW,IAAI,CAACnC,QAAQ,IAAI,CAACP,KAAK,EAAE;MACrC,OAAOf,SAAS;IACpB;IACA,OAAO6B,eAAe,CAACC,cAAc,CAACpB,iBAAiB,EAAEK,KAAK,EAAEO,QAAQ,EAAEmC,WAAW,CAACnB,IAAI,EAAE,IAAI,CAACxC,iBAAiB,CAAC;EACvH;EACA6D,uBAAuBA,CAAA,EAAG;IACtB,MAAMjD,iBAAiB,GAAGrB,iBAAiB,CAACsB,GAAG,CAAC,IAAI,CAACjB,MAAM,CAAC;IAC5DgB,iBAAiB,EAAEiD,uBAAuB,CAAC,CAAC;EAChD;EACAC,mBAAmBA,CAAA,EAAG;IAClB,MAAMlD,iBAAiB,GAAGrB,iBAAiB,CAACsB,GAAG,CAAC,IAAI,CAACjB,MAAM,CAAC;IAC5DgB,iBAAiB,EAAEkD,mBAAmB,CAAC,CAAC;EAC5C;AACJ;AACA,OAAO,MAAM/B,eAAe,CAAC;EACzB,OAAOC,cAAcA,CAACpB,iBAAiB,EAAEK,KAAK,EAAEO,QAAQ,EAAEgB,IAAI,EAAEuB,UAAU,EAAE;IACxE,IAAI;MAAEC;IAAW,CAAC,GAAGxB,IAAI,CAACyB,UAAU;IACpC,IAAIC,aAAa,GAAG,KAAK;IACzB,IAAI1B,IAAI,CAACyB,UAAU,CAACE,eAAe,GAAG,CAAC,CAAC,oDAAoD;MACxF,MAAMC,OAAO,GAAG,IAAI/E,aAAa,CAAC,CAAC,CAACgF,KAAK,CAACL,UAAU,CAAC;MACrD,IAAII,OAAO,CAACE,QAAQ,CAAChC,MAAM,GAAG,GAAG,EAAE;QAC/B;QACAhD,cAAc,CAACiF,gBAAgB,CAACtD,KAAK,EAAEO,QAAQ,EAAE,IAAI,EAAE4C,OAAO,CAAC;MACnE;MACAJ,UAAU,GAAGI,OAAO,CAACI,QAAQ,CAAC,CAAC;MAC/BN,aAAa,GAAG,IAAI;IACxB;IACA,MAAMO,IAAI,GAAG7D,iBAAiB,CAAC8D,gBAAgB,CAAClC,IAAI,EAAEuB,UAAU,CAAC;IACjE,OAAO,IAAIhC,eAAe,CAAC/C,KAAK,CAAC2F,aAAa,CAACnD,QAAQ,CAACoD,KAAK,CAAC,CAAC,EAAE,CAACH,IAAI,CAACI,eAAe,CAAC,EAAErD,QAAQ,CAACoD,KAAK,CAAC,CAAC,EAAEE,IAAI,CAACC,GAAG,CAACN,IAAI,CAACO,cAAc,EAAE,CAAC,CAAC,CAAC,CAAC,EAAEhB,UAAU,EAAExB,IAAI,CAACyB,UAAU,CAACgB,IAAI,EAAEf,aAAa,CAAC;EACnM;EACAvE,WAAWA,CAACuF,KAAK,EAAElB,UAAU,EAAEmB,kBAAkB,EAAEjB,aAAa,EAAE;IAC9D,IAAI,CAACgB,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAClB,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACmB,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACjB,aAAa,GAAGA,aAAa;EACtC;EACAkB,MAAMA,CAACC,KAAK,EAAE;IACV,OAAO,IAAI,CAACH,KAAK,CAACI,WAAW,CAACD,KAAK,CAACH,KAAK,CAAC,IACnC,IAAI,CAAClB,UAAU,KAAKqB,KAAK,CAACrB,UAAU,IACpC,IAAI,CAACmB,kBAAkB,KAAKE,KAAK,CAACF,kBAAkB,IACpD,IAAI,CAACjB,aAAa,KAAKmB,KAAK,CAACnB,aAAa;EACrD;EACAqB,wBAAwBA,CAAA,EAAG;IACvB,OAAO,IAAIrG,sBAAsB,CAAC,IAAI,CAACgG,KAAK,EAAE,IAAI,CAAClB,UAAU,EAAE,IAAI,CAACmB,kBAAkB,EAAE,IAAI,CAACjB,aAAa,CAAC;EAC/G;EACAhC,gBAAgBA,CAAA,EAAG;IACf,OAAO,IAAIjD,cAAc,CAAC,IAAI,CAACiG,KAAK,EAAE,IAAI,CAAClB,UAAU,CAAC;EAC1D;AACJ;AACA,SAASP,qBAAqBA,CAAC+B,CAAC,EAAEC,CAAC,EAAE;EACjC,IAAID,CAAC,KAAKC,CAAC,EAAE;IACT,OAAO,IAAI;EACf;EACA,IAAI,CAACD,CAAC,IAAI,CAACC,CAAC,EAAE;IACV,OAAO,KAAK;EAChB;EACA,OAAOD,CAAC,CAACJ,MAAM,CAACK,CAAC,CAAC;AACtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}