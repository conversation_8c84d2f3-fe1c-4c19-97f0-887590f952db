{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { $, append, createStyleSheet, EventHelper, getWindow, isHTMLElement } from '../../dom.js';\nimport { DomEmitter } from '../../event.js';\nimport { EventType, Gesture } from '../../touch.js';\nimport { Delayer } from '../../../common/async.js';\nimport { memoize } from '../../../common/decorators.js';\nimport { Emitter } from '../../../common/event.js';\nimport { Disposable, DisposableStore, toDisposable } from '../../../common/lifecycle.js';\nimport { isMacintosh } from '../../../common/platform.js';\nimport './sash.css';\n/**\n * Allow the sashes to be visible at runtime.\n * @remark Use for development purposes only.\n */\nconst DEBUG = false;\nexport var OrthogonalEdge = /*#__PURE__*/function (OrthogonalEdge) {\n  OrthogonalEdge[\"North\"] = \"north\";\n  OrthogonalEdge[\"South\"] = \"south\";\n  OrthogonalEdge[\"East\"] = \"east\";\n  OrthogonalEdge[\"West\"] = \"west\";\n  return OrthogonalEdge;\n}(OrthogonalEdge || {});\nlet globalSize = 4;\nconst onDidChangeGlobalSize = new Emitter();\nlet globalHoverDelay = 300;\nconst onDidChangeHoverDelay = new Emitter();\nclass MouseEventFactory {\n  constructor(el) {\n    this.el = el;\n    this.disposables = new DisposableStore();\n  }\n  get onPointerMove() {\n    return this.disposables.add(new DomEmitter(getWindow(this.el), 'mousemove')).event;\n  }\n  get onPointerUp() {\n    return this.disposables.add(new DomEmitter(getWindow(this.el), 'mouseup')).event;\n  }\n  dispose() {\n    this.disposables.dispose();\n  }\n}\n__decorate([memoize], MouseEventFactory.prototype, \"onPointerMove\", null);\n__decorate([memoize], MouseEventFactory.prototype, \"onPointerUp\", null);\nclass GestureEventFactory {\n  get onPointerMove() {\n    return this.disposables.add(new DomEmitter(this.el, EventType.Change)).event;\n  }\n  get onPointerUp() {\n    return this.disposables.add(new DomEmitter(this.el, EventType.End)).event;\n  }\n  constructor(el) {\n    this.el = el;\n    this.disposables = new DisposableStore();\n  }\n  dispose() {\n    this.disposables.dispose();\n  }\n}\n__decorate([memoize], GestureEventFactory.prototype, \"onPointerMove\", null);\n__decorate([memoize], GestureEventFactory.prototype, \"onPointerUp\", null);\nclass OrthogonalPointerEventFactory {\n  get onPointerMove() {\n    return this.factory.onPointerMove;\n  }\n  get onPointerUp() {\n    return this.factory.onPointerUp;\n  }\n  constructor(factory) {\n    this.factory = factory;\n  }\n  dispose() {\n    // noop\n  }\n}\n__decorate([memoize], OrthogonalPointerEventFactory.prototype, \"onPointerMove\", null);\n__decorate([memoize], OrthogonalPointerEventFactory.prototype, \"onPointerUp\", null);\nconst PointerEventsDisabledCssClass = 'pointer-events-disabled';\n/**\n * The {@link Sash} is the UI component which allows the user to resize other\n * components. It's usually an invisible horizontal or vertical line which, when\n * hovered, becomes highlighted and can be dragged along the perpendicular dimension\n * to its direction.\n *\n * Features:\n * - Touch event handling\n * - Corner sash support\n * - Hover with different mouse cursor support\n * - Configurable hover size\n * - Linked sash support, for 2x2 corner sashes\n */\nexport class Sash extends Disposable {\n  get state() {\n    return this._state;\n  }\n  get orthogonalStartSash() {\n    return this._orthogonalStartSash;\n  }\n  get orthogonalEndSash() {\n    return this._orthogonalEndSash;\n  }\n  /**\n   * The state of a sash defines whether it can be interacted with by the user\n   * as well as what mouse cursor to use, when hovered.\n   */\n  set state(state) {\n    if (this._state === state) {\n      return;\n    }\n    this.el.classList.toggle('disabled', state === 0 /* SashState.Disabled */);\n    this.el.classList.toggle('minimum', state === 1 /* SashState.AtMinimum */);\n    this.el.classList.toggle('maximum', state === 2 /* SashState.AtMaximum */);\n    this._state = state;\n    this.onDidEnablementChange.fire(state);\n  }\n  /**\n   * A reference to another sash, perpendicular to this one, which\n   * aligns at the start of this one. A corner sash will be created\n   * automatically at that location.\n   *\n   * The start of a horizontal sash is its left-most position.\n   * The start of a vertical sash is its top-most position.\n   */\n  set orthogonalStartSash(sash) {\n    if (this._orthogonalStartSash === sash) {\n      return;\n    }\n    this.orthogonalStartDragHandleDisposables.clear();\n    this.orthogonalStartSashDisposables.clear();\n    if (sash) {\n      const onChange = state => {\n        this.orthogonalStartDragHandleDisposables.clear();\n        if (state !== 0 /* SashState.Disabled */) {\n          this._orthogonalStartDragHandle = append(this.el, $('.orthogonal-drag-handle.start'));\n          this.orthogonalStartDragHandleDisposables.add(toDisposable(() => this._orthogonalStartDragHandle.remove()));\n          this.orthogonalStartDragHandleDisposables.add(new DomEmitter(this._orthogonalStartDragHandle, 'mouseenter')).event(() => Sash.onMouseEnter(sash), undefined, this.orthogonalStartDragHandleDisposables);\n          this.orthogonalStartDragHandleDisposables.add(new DomEmitter(this._orthogonalStartDragHandle, 'mouseleave')).event(() => Sash.onMouseLeave(sash), undefined, this.orthogonalStartDragHandleDisposables);\n        }\n      };\n      this.orthogonalStartSashDisposables.add(sash.onDidEnablementChange.event(onChange, this));\n      onChange(sash.state);\n    }\n    this._orthogonalStartSash = sash;\n  }\n  /**\n   * A reference to another sash, perpendicular to this one, which\n   * aligns at the end of this one. A corner sash will be created\n   * automatically at that location.\n   *\n   * The end of a horizontal sash is its right-most position.\n   * The end of a vertical sash is its bottom-most position.\n   */\n  set orthogonalEndSash(sash) {\n    if (this._orthogonalEndSash === sash) {\n      return;\n    }\n    this.orthogonalEndDragHandleDisposables.clear();\n    this.orthogonalEndSashDisposables.clear();\n    if (sash) {\n      const onChange = state => {\n        this.orthogonalEndDragHandleDisposables.clear();\n        if (state !== 0 /* SashState.Disabled */) {\n          this._orthogonalEndDragHandle = append(this.el, $('.orthogonal-drag-handle.end'));\n          this.orthogonalEndDragHandleDisposables.add(toDisposable(() => this._orthogonalEndDragHandle.remove()));\n          this.orthogonalEndDragHandleDisposables.add(new DomEmitter(this._orthogonalEndDragHandle, 'mouseenter')).event(() => Sash.onMouseEnter(sash), undefined, this.orthogonalEndDragHandleDisposables);\n          this.orthogonalEndDragHandleDisposables.add(new DomEmitter(this._orthogonalEndDragHandle, 'mouseleave')).event(() => Sash.onMouseLeave(sash), undefined, this.orthogonalEndDragHandleDisposables);\n        }\n      };\n      this.orthogonalEndSashDisposables.add(sash.onDidEnablementChange.event(onChange, this));\n      onChange(sash.state);\n    }\n    this._orthogonalEndSash = sash;\n  }\n  constructor(container, layoutProvider, options) {\n    super();\n    this.hoverDelay = globalHoverDelay;\n    this.hoverDelayer = this._register(new Delayer(this.hoverDelay));\n    this._state = 3 /* SashState.Enabled */;\n    this.onDidEnablementChange = this._register(new Emitter());\n    this._onDidStart = this._register(new Emitter());\n    this._onDidChange = this._register(new Emitter());\n    this._onDidReset = this._register(new Emitter());\n    this._onDidEnd = this._register(new Emitter());\n    this.orthogonalStartSashDisposables = this._register(new DisposableStore());\n    this.orthogonalStartDragHandleDisposables = this._register(new DisposableStore());\n    this.orthogonalEndSashDisposables = this._register(new DisposableStore());\n    this.orthogonalEndDragHandleDisposables = this._register(new DisposableStore());\n    /**\n     * An event which fires whenever the user starts dragging this sash.\n     */\n    this.onDidStart = this._onDidStart.event;\n    /**\n     * An event which fires whenever the user moves the mouse while\n     * dragging this sash.\n     */\n    this.onDidChange = this._onDidChange.event;\n    /**\n     * An event which fires whenever the user double clicks this sash.\n     */\n    this.onDidReset = this._onDidReset.event;\n    /**\n     * An event which fires whenever the user stops dragging this sash.\n     */\n    this.onDidEnd = this._onDidEnd.event;\n    /**\n     * A linked sash will be forwarded the same user interactions and events\n     * so it moves exactly the same way as this sash.\n     *\n     * Useful in 2x2 grids. Not meant for widespread usage.\n     */\n    this.linkedSash = undefined;\n    this.el = append(container, $('.monaco-sash'));\n    if (options.orthogonalEdge) {\n      this.el.classList.add(`orthogonal-edge-${options.orthogonalEdge}`);\n    }\n    if (isMacintosh) {\n      this.el.classList.add('mac');\n    }\n    const onMouseDown = this._register(new DomEmitter(this.el, 'mousedown')).event;\n    this._register(onMouseDown(e => this.onPointerStart(e, new MouseEventFactory(container)), this));\n    const onMouseDoubleClick = this._register(new DomEmitter(this.el, 'dblclick')).event;\n    this._register(onMouseDoubleClick(this.onPointerDoublePress, this));\n    const onMouseEnter = this._register(new DomEmitter(this.el, 'mouseenter')).event;\n    this._register(onMouseEnter(() => Sash.onMouseEnter(this)));\n    const onMouseLeave = this._register(new DomEmitter(this.el, 'mouseleave')).event;\n    this._register(onMouseLeave(() => Sash.onMouseLeave(this)));\n    this._register(Gesture.addTarget(this.el));\n    const onTouchStart = this._register(new DomEmitter(this.el, EventType.Start)).event;\n    this._register(onTouchStart(e => this.onPointerStart(e, new GestureEventFactory(this.el)), this));\n    const onTap = this._register(new DomEmitter(this.el, EventType.Tap)).event;\n    let doubleTapTimeout = undefined;\n    this._register(onTap(event => {\n      if (doubleTapTimeout) {\n        clearTimeout(doubleTapTimeout);\n        doubleTapTimeout = undefined;\n        this.onPointerDoublePress(event);\n        return;\n      }\n      clearTimeout(doubleTapTimeout);\n      doubleTapTimeout = setTimeout(() => doubleTapTimeout = undefined, 250);\n    }, this));\n    if (typeof options.size === 'number') {\n      this.size = options.size;\n      if (options.orientation === 0 /* Orientation.VERTICAL */) {\n        this.el.style.width = `${this.size}px`;\n      } else {\n        this.el.style.height = `${this.size}px`;\n      }\n    } else {\n      this.size = globalSize;\n      this._register(onDidChangeGlobalSize.event(size => {\n        this.size = size;\n        this.layout();\n      }));\n    }\n    this._register(onDidChangeHoverDelay.event(delay => this.hoverDelay = delay));\n    this.layoutProvider = layoutProvider;\n    this.orthogonalStartSash = options.orthogonalStartSash;\n    this.orthogonalEndSash = options.orthogonalEndSash;\n    this.orientation = options.orientation || 0 /* Orientation.VERTICAL */;\n    if (this.orientation === 1 /* Orientation.HORIZONTAL */) {\n      this.el.classList.add('horizontal');\n      this.el.classList.remove('vertical');\n    } else {\n      this.el.classList.remove('horizontal');\n      this.el.classList.add('vertical');\n    }\n    this.el.classList.toggle('debug', DEBUG);\n    this.layout();\n  }\n  onPointerStart(event, pointerEventFactory) {\n    EventHelper.stop(event);\n    let isMultisashResize = false;\n    if (!event.__orthogonalSashEvent) {\n      const orthogonalSash = this.getOrthogonalSash(event);\n      if (orthogonalSash) {\n        isMultisashResize = true;\n        event.__orthogonalSashEvent = true;\n        orthogonalSash.onPointerStart(event, new OrthogonalPointerEventFactory(pointerEventFactory));\n      }\n    }\n    if (this.linkedSash && !event.__linkedSashEvent) {\n      event.__linkedSashEvent = true;\n      this.linkedSash.onPointerStart(event, new OrthogonalPointerEventFactory(pointerEventFactory));\n    }\n    if (!this.state) {\n      return;\n    }\n    const iframes = this.el.ownerDocument.getElementsByTagName('iframe');\n    for (const iframe of iframes) {\n      iframe.classList.add(PointerEventsDisabledCssClass); // disable mouse events on iframes as long as we drag the sash\n    }\n    const startX = event.pageX;\n    const startY = event.pageY;\n    const altKey = event.altKey;\n    const startEvent = {\n      startX,\n      currentX: startX,\n      startY,\n      currentY: startY,\n      altKey\n    };\n    this.el.classList.add('active');\n    this._onDidStart.fire(startEvent);\n    // fix https://github.com/microsoft/vscode/issues/21675\n    const style = createStyleSheet(this.el);\n    const updateStyle = () => {\n      let cursor = '';\n      if (isMultisashResize) {\n        cursor = 'all-scroll';\n      } else if (this.orientation === 1 /* Orientation.HORIZONTAL */) {\n        if (this.state === 1 /* SashState.AtMinimum */) {\n          cursor = 's-resize';\n        } else if (this.state === 2 /* SashState.AtMaximum */) {\n          cursor = 'n-resize';\n        } else {\n          cursor = isMacintosh ? 'row-resize' : 'ns-resize';\n        }\n      } else {\n        if (this.state === 1 /* SashState.AtMinimum */) {\n          cursor = 'e-resize';\n        } else if (this.state === 2 /* SashState.AtMaximum */) {\n          cursor = 'w-resize';\n        } else {\n          cursor = isMacintosh ? 'col-resize' : 'ew-resize';\n        }\n      }\n      style.textContent = `* { cursor: ${cursor} !important; }`;\n    };\n    const disposables = new DisposableStore();\n    updateStyle();\n    if (!isMultisashResize) {\n      this.onDidEnablementChange.event(updateStyle, null, disposables);\n    }\n    const onPointerMove = e => {\n      EventHelper.stop(e, false);\n      const event = {\n        startX,\n        currentX: e.pageX,\n        startY,\n        currentY: e.pageY,\n        altKey\n      };\n      this._onDidChange.fire(event);\n    };\n    const onPointerUp = e => {\n      EventHelper.stop(e, false);\n      style.remove();\n      this.el.classList.remove('active');\n      this._onDidEnd.fire();\n      disposables.dispose();\n      for (const iframe of iframes) {\n        iframe.classList.remove(PointerEventsDisabledCssClass);\n      }\n    };\n    pointerEventFactory.onPointerMove(onPointerMove, null, disposables);\n    pointerEventFactory.onPointerUp(onPointerUp, null, disposables);\n    disposables.add(pointerEventFactory);\n  }\n  onPointerDoublePress(e) {\n    const orthogonalSash = this.getOrthogonalSash(e);\n    if (orthogonalSash) {\n      orthogonalSash._onDidReset.fire();\n    }\n    if (this.linkedSash) {\n      this.linkedSash._onDidReset.fire();\n    }\n    this._onDidReset.fire();\n  }\n  static onMouseEnter(sash, fromLinkedSash = false) {\n    if (sash.el.classList.contains('active')) {\n      sash.hoverDelayer.cancel();\n      sash.el.classList.add('hover');\n    } else {\n      sash.hoverDelayer.trigger(() => sash.el.classList.add('hover'), sash.hoverDelay).then(undefined, () => {});\n    }\n    if (!fromLinkedSash && sash.linkedSash) {\n      Sash.onMouseEnter(sash.linkedSash, true);\n    }\n  }\n  static onMouseLeave(sash, fromLinkedSash = false) {\n    sash.hoverDelayer.cancel();\n    sash.el.classList.remove('hover');\n    if (!fromLinkedSash && sash.linkedSash) {\n      Sash.onMouseLeave(sash.linkedSash, true);\n    }\n  }\n  /**\n   * Forcefully stop any user interactions with this sash.\n   * Useful when hiding a parent component, while the user is still\n   * interacting with the sash.\n   */\n  clearSashHoverState() {\n    Sash.onMouseLeave(this);\n  }\n  /**\n   * Layout the sash. The sash will size and position itself\n   * based on its provided {@link ISashLayoutProvider layout provider}.\n   */\n  layout() {\n    if (this.orientation === 0 /* Orientation.VERTICAL */) {\n      const verticalProvider = this.layoutProvider;\n      this.el.style.left = verticalProvider.getVerticalSashLeft(this) - this.size / 2 + 'px';\n      if (verticalProvider.getVerticalSashTop) {\n        this.el.style.top = verticalProvider.getVerticalSashTop(this) + 'px';\n      }\n      if (verticalProvider.getVerticalSashHeight) {\n        this.el.style.height = verticalProvider.getVerticalSashHeight(this) + 'px';\n      }\n    } else {\n      const horizontalProvider = this.layoutProvider;\n      this.el.style.top = horizontalProvider.getHorizontalSashTop(this) - this.size / 2 + 'px';\n      if (horizontalProvider.getHorizontalSashLeft) {\n        this.el.style.left = horizontalProvider.getHorizontalSashLeft(this) + 'px';\n      }\n      if (horizontalProvider.getHorizontalSashWidth) {\n        this.el.style.width = horizontalProvider.getHorizontalSashWidth(this) + 'px';\n      }\n    }\n  }\n  getOrthogonalSash(e) {\n    const target = e.initialTarget ?? e.target;\n    if (!target || !isHTMLElement(target)) {\n      return undefined;\n    }\n    if (target.classList.contains('orthogonal-drag-handle')) {\n      return target.classList.contains('start') ? this.orthogonalStartSash : this.orthogonalEndSash;\n    }\n    return undefined;\n  }\n  dispose() {\n    super.dispose();\n    this.el.remove();\n  }\n}", "map": {"version": 3, "names": ["__decorate", "decorators", "target", "key", "desc", "c", "arguments", "length", "r", "Object", "getOwnPropertyDescriptor", "d", "Reflect", "decorate", "i", "defineProperty", "$", "append", "createStyleSheet", "EventHelper", "getWindow", "isHTMLElement", "DomEmitter", "EventType", "Gesture", "<PERSON><PERSON><PERSON>", "memoize", "Emitter", "Disposable", "DisposableStore", "toDisposable", "isMacintosh", "DEBUG", "OrthogonalEdge", "globalSize", "onDidChangeGlobalSize", "globalHoverDelay", "onDidChangeHoverDelay", "MouseEventFactory", "constructor", "el", "disposables", "onPointerMove", "add", "event", "onPointerUp", "dispose", "prototype", "GestureEventFactory", "Change", "End", "OrthogonalPointerEventFactory", "factory", "PointerEventsDisabledCssClass", "<PERSON>sh", "state", "_state", "orthogonalStartSash", "_orthogonalStartSash", "orthogonalEndSash", "_orthogonalEndSash", "classList", "toggle", "onDidEnablementChange", "fire", "sash", "orthogonalStartDragHandleDisposables", "clear", "orthogonalStartSashDisposables", "onChange", "_orthogonalStartDragHandle", "remove", "onMouseEnter", "undefined", "onMouseLeave", "orthogonalEndDragHandleDisposables", "orthogonalEndSashDisposables", "_orthogonalEndDragHandle", "container", "<PERSON><PERSON><PERSON><PERSON>", "options", "hoverDelay", "hoverDelayer", "_register", "_onDidStart", "_onDidChange", "_onDidReset", "_onDidEnd", "onDidStart", "onDidChange", "onDidReset", "onDidEnd", "linkedSash", "orthogonalEdge", "onMouseDown", "e", "onPointerStart", "onMouseDoubleClick", "onPointerDoublePress", "addTarget", "onTouchStart", "Start", "onTap", "Tap", "doubleTapTimeout", "clearTimeout", "setTimeout", "size", "orientation", "style", "width", "height", "layout", "delay", "pointerEventFactory", "stop", "isMultisashResize", "__orthogonalSashEvent", "orthogonalSash", "getOrthogonalSash", "__linkedSashEvent", "iframes", "ownerDocument", "getElementsByTagName", "iframe", "startX", "pageX", "startY", "pageY", "altKey", "startEvent", "currentX", "currentY", "updateStyle", "cursor", "textContent", "fromLinkedSash", "contains", "cancel", "trigger", "then", "clearSashHoverState", "verticalProvider", "left", "getVerticalSashLeft", "getVerticalSashTop", "top", "getVerticalSashHeight", "horizontalProvider", "getHorizontalSashTop", "getHorizontalSashLeft", "getHorizontalSashWidth", "initialTarget"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/browser/ui/sash/sash.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { $, append, createStyleSheet, EventHelper, getWindow, isHTMLElement } from '../../dom.js';\nimport { DomEmitter } from '../../event.js';\nimport { EventType, Gesture } from '../../touch.js';\nimport { Delayer } from '../../../common/async.js';\nimport { memoize } from '../../../common/decorators.js';\nimport { Emitter } from '../../../common/event.js';\nimport { Disposable, DisposableStore, toDisposable } from '../../../common/lifecycle.js';\nimport { isMacintosh } from '../../../common/platform.js';\nimport './sash.css';\n/**\n * Allow the sashes to be visible at runtime.\n * @remark Use for development purposes only.\n */\nconst DEBUG = false;\nexport var OrthogonalEdge;\n(function (OrthogonalEdge) {\n    OrthogonalEdge[\"North\"] = \"north\";\n    OrthogonalEdge[\"South\"] = \"south\";\n    OrthogonalEdge[\"East\"] = \"east\";\n    OrthogonalEdge[\"West\"] = \"west\";\n})(OrthogonalEdge || (OrthogonalEdge = {}));\nlet globalSize = 4;\nconst onDidChangeGlobalSize = new Emitter();\nlet globalHoverDelay = 300;\nconst onDidChangeHoverDelay = new Emitter();\nclass MouseEventFactory {\n    constructor(el) {\n        this.el = el;\n        this.disposables = new DisposableStore();\n    }\n    get onPointerMove() {\n        return this.disposables.add(new DomEmitter(getWindow(this.el), 'mousemove')).event;\n    }\n    get onPointerUp() {\n        return this.disposables.add(new DomEmitter(getWindow(this.el), 'mouseup')).event;\n    }\n    dispose() {\n        this.disposables.dispose();\n    }\n}\n__decorate([\n    memoize\n], MouseEventFactory.prototype, \"onPointerMove\", null);\n__decorate([\n    memoize\n], MouseEventFactory.prototype, \"onPointerUp\", null);\nclass GestureEventFactory {\n    get onPointerMove() {\n        return this.disposables.add(new DomEmitter(this.el, EventType.Change)).event;\n    }\n    get onPointerUp() {\n        return this.disposables.add(new DomEmitter(this.el, EventType.End)).event;\n    }\n    constructor(el) {\n        this.el = el;\n        this.disposables = new DisposableStore();\n    }\n    dispose() {\n        this.disposables.dispose();\n    }\n}\n__decorate([\n    memoize\n], GestureEventFactory.prototype, \"onPointerMove\", null);\n__decorate([\n    memoize\n], GestureEventFactory.prototype, \"onPointerUp\", null);\nclass OrthogonalPointerEventFactory {\n    get onPointerMove() {\n        return this.factory.onPointerMove;\n    }\n    get onPointerUp() {\n        return this.factory.onPointerUp;\n    }\n    constructor(factory) {\n        this.factory = factory;\n    }\n    dispose() {\n        // noop\n    }\n}\n__decorate([\n    memoize\n], OrthogonalPointerEventFactory.prototype, \"onPointerMove\", null);\n__decorate([\n    memoize\n], OrthogonalPointerEventFactory.prototype, \"onPointerUp\", null);\nconst PointerEventsDisabledCssClass = 'pointer-events-disabled';\n/**\n * The {@link Sash} is the UI component which allows the user to resize other\n * components. It's usually an invisible horizontal or vertical line which, when\n * hovered, becomes highlighted and can be dragged along the perpendicular dimension\n * to its direction.\n *\n * Features:\n * - Touch event handling\n * - Corner sash support\n * - Hover with different mouse cursor support\n * - Configurable hover size\n * - Linked sash support, for 2x2 corner sashes\n */\nexport class Sash extends Disposable {\n    get state() { return this._state; }\n    get orthogonalStartSash() { return this._orthogonalStartSash; }\n    get orthogonalEndSash() { return this._orthogonalEndSash; }\n    /**\n     * The state of a sash defines whether it can be interacted with by the user\n     * as well as what mouse cursor to use, when hovered.\n     */\n    set state(state) {\n        if (this._state === state) {\n            return;\n        }\n        this.el.classList.toggle('disabled', state === 0 /* SashState.Disabled */);\n        this.el.classList.toggle('minimum', state === 1 /* SashState.AtMinimum */);\n        this.el.classList.toggle('maximum', state === 2 /* SashState.AtMaximum */);\n        this._state = state;\n        this.onDidEnablementChange.fire(state);\n    }\n    /**\n     * A reference to another sash, perpendicular to this one, which\n     * aligns at the start of this one. A corner sash will be created\n     * automatically at that location.\n     *\n     * The start of a horizontal sash is its left-most position.\n     * The start of a vertical sash is its top-most position.\n     */\n    set orthogonalStartSash(sash) {\n        if (this._orthogonalStartSash === sash) {\n            return;\n        }\n        this.orthogonalStartDragHandleDisposables.clear();\n        this.orthogonalStartSashDisposables.clear();\n        if (sash) {\n            const onChange = (state) => {\n                this.orthogonalStartDragHandleDisposables.clear();\n                if (state !== 0 /* SashState.Disabled */) {\n                    this._orthogonalStartDragHandle = append(this.el, $('.orthogonal-drag-handle.start'));\n                    this.orthogonalStartDragHandleDisposables.add(toDisposable(() => this._orthogonalStartDragHandle.remove()));\n                    this.orthogonalStartDragHandleDisposables.add(new DomEmitter(this._orthogonalStartDragHandle, 'mouseenter')).event(() => Sash.onMouseEnter(sash), undefined, this.orthogonalStartDragHandleDisposables);\n                    this.orthogonalStartDragHandleDisposables.add(new DomEmitter(this._orthogonalStartDragHandle, 'mouseleave')).event(() => Sash.onMouseLeave(sash), undefined, this.orthogonalStartDragHandleDisposables);\n                }\n            };\n            this.orthogonalStartSashDisposables.add(sash.onDidEnablementChange.event(onChange, this));\n            onChange(sash.state);\n        }\n        this._orthogonalStartSash = sash;\n    }\n    /**\n     * A reference to another sash, perpendicular to this one, which\n     * aligns at the end of this one. A corner sash will be created\n     * automatically at that location.\n     *\n     * The end of a horizontal sash is its right-most position.\n     * The end of a vertical sash is its bottom-most position.\n     */\n    set orthogonalEndSash(sash) {\n        if (this._orthogonalEndSash === sash) {\n            return;\n        }\n        this.orthogonalEndDragHandleDisposables.clear();\n        this.orthogonalEndSashDisposables.clear();\n        if (sash) {\n            const onChange = (state) => {\n                this.orthogonalEndDragHandleDisposables.clear();\n                if (state !== 0 /* SashState.Disabled */) {\n                    this._orthogonalEndDragHandle = append(this.el, $('.orthogonal-drag-handle.end'));\n                    this.orthogonalEndDragHandleDisposables.add(toDisposable(() => this._orthogonalEndDragHandle.remove()));\n                    this.orthogonalEndDragHandleDisposables.add(new DomEmitter(this._orthogonalEndDragHandle, 'mouseenter')).event(() => Sash.onMouseEnter(sash), undefined, this.orthogonalEndDragHandleDisposables);\n                    this.orthogonalEndDragHandleDisposables.add(new DomEmitter(this._orthogonalEndDragHandle, 'mouseleave')).event(() => Sash.onMouseLeave(sash), undefined, this.orthogonalEndDragHandleDisposables);\n                }\n            };\n            this.orthogonalEndSashDisposables.add(sash.onDidEnablementChange.event(onChange, this));\n            onChange(sash.state);\n        }\n        this._orthogonalEndSash = sash;\n    }\n    constructor(container, layoutProvider, options) {\n        super();\n        this.hoverDelay = globalHoverDelay;\n        this.hoverDelayer = this._register(new Delayer(this.hoverDelay));\n        this._state = 3 /* SashState.Enabled */;\n        this.onDidEnablementChange = this._register(new Emitter());\n        this._onDidStart = this._register(new Emitter());\n        this._onDidChange = this._register(new Emitter());\n        this._onDidReset = this._register(new Emitter());\n        this._onDidEnd = this._register(new Emitter());\n        this.orthogonalStartSashDisposables = this._register(new DisposableStore());\n        this.orthogonalStartDragHandleDisposables = this._register(new DisposableStore());\n        this.orthogonalEndSashDisposables = this._register(new DisposableStore());\n        this.orthogonalEndDragHandleDisposables = this._register(new DisposableStore());\n        /**\n         * An event which fires whenever the user starts dragging this sash.\n         */\n        this.onDidStart = this._onDidStart.event;\n        /**\n         * An event which fires whenever the user moves the mouse while\n         * dragging this sash.\n         */\n        this.onDidChange = this._onDidChange.event;\n        /**\n         * An event which fires whenever the user double clicks this sash.\n         */\n        this.onDidReset = this._onDidReset.event;\n        /**\n         * An event which fires whenever the user stops dragging this sash.\n         */\n        this.onDidEnd = this._onDidEnd.event;\n        /**\n         * A linked sash will be forwarded the same user interactions and events\n         * so it moves exactly the same way as this sash.\n         *\n         * Useful in 2x2 grids. Not meant for widespread usage.\n         */\n        this.linkedSash = undefined;\n        this.el = append(container, $('.monaco-sash'));\n        if (options.orthogonalEdge) {\n            this.el.classList.add(`orthogonal-edge-${options.orthogonalEdge}`);\n        }\n        if (isMacintosh) {\n            this.el.classList.add('mac');\n        }\n        const onMouseDown = this._register(new DomEmitter(this.el, 'mousedown')).event;\n        this._register(onMouseDown(e => this.onPointerStart(e, new MouseEventFactory(container)), this));\n        const onMouseDoubleClick = this._register(new DomEmitter(this.el, 'dblclick')).event;\n        this._register(onMouseDoubleClick(this.onPointerDoublePress, this));\n        const onMouseEnter = this._register(new DomEmitter(this.el, 'mouseenter')).event;\n        this._register(onMouseEnter(() => Sash.onMouseEnter(this)));\n        const onMouseLeave = this._register(new DomEmitter(this.el, 'mouseleave')).event;\n        this._register(onMouseLeave(() => Sash.onMouseLeave(this)));\n        this._register(Gesture.addTarget(this.el));\n        const onTouchStart = this._register(new DomEmitter(this.el, EventType.Start)).event;\n        this._register(onTouchStart(e => this.onPointerStart(e, new GestureEventFactory(this.el)), this));\n        const onTap = this._register(new DomEmitter(this.el, EventType.Tap)).event;\n        let doubleTapTimeout = undefined;\n        this._register(onTap(event => {\n            if (doubleTapTimeout) {\n                clearTimeout(doubleTapTimeout);\n                doubleTapTimeout = undefined;\n                this.onPointerDoublePress(event);\n                return;\n            }\n            clearTimeout(doubleTapTimeout);\n            doubleTapTimeout = setTimeout(() => doubleTapTimeout = undefined, 250);\n        }, this));\n        if (typeof options.size === 'number') {\n            this.size = options.size;\n            if (options.orientation === 0 /* Orientation.VERTICAL */) {\n                this.el.style.width = `${this.size}px`;\n            }\n            else {\n                this.el.style.height = `${this.size}px`;\n            }\n        }\n        else {\n            this.size = globalSize;\n            this._register(onDidChangeGlobalSize.event(size => {\n                this.size = size;\n                this.layout();\n            }));\n        }\n        this._register(onDidChangeHoverDelay.event(delay => this.hoverDelay = delay));\n        this.layoutProvider = layoutProvider;\n        this.orthogonalStartSash = options.orthogonalStartSash;\n        this.orthogonalEndSash = options.orthogonalEndSash;\n        this.orientation = options.orientation || 0 /* Orientation.VERTICAL */;\n        if (this.orientation === 1 /* Orientation.HORIZONTAL */) {\n            this.el.classList.add('horizontal');\n            this.el.classList.remove('vertical');\n        }\n        else {\n            this.el.classList.remove('horizontal');\n            this.el.classList.add('vertical');\n        }\n        this.el.classList.toggle('debug', DEBUG);\n        this.layout();\n    }\n    onPointerStart(event, pointerEventFactory) {\n        EventHelper.stop(event);\n        let isMultisashResize = false;\n        if (!event.__orthogonalSashEvent) {\n            const orthogonalSash = this.getOrthogonalSash(event);\n            if (orthogonalSash) {\n                isMultisashResize = true;\n                event.__orthogonalSashEvent = true;\n                orthogonalSash.onPointerStart(event, new OrthogonalPointerEventFactory(pointerEventFactory));\n            }\n        }\n        if (this.linkedSash && !event.__linkedSashEvent) {\n            event.__linkedSashEvent = true;\n            this.linkedSash.onPointerStart(event, new OrthogonalPointerEventFactory(pointerEventFactory));\n        }\n        if (!this.state) {\n            return;\n        }\n        const iframes = this.el.ownerDocument.getElementsByTagName('iframe');\n        for (const iframe of iframes) {\n            iframe.classList.add(PointerEventsDisabledCssClass); // disable mouse events on iframes as long as we drag the sash\n        }\n        const startX = event.pageX;\n        const startY = event.pageY;\n        const altKey = event.altKey;\n        const startEvent = { startX, currentX: startX, startY, currentY: startY, altKey };\n        this.el.classList.add('active');\n        this._onDidStart.fire(startEvent);\n        // fix https://github.com/microsoft/vscode/issues/21675\n        const style = createStyleSheet(this.el);\n        const updateStyle = () => {\n            let cursor = '';\n            if (isMultisashResize) {\n                cursor = 'all-scroll';\n            }\n            else if (this.orientation === 1 /* Orientation.HORIZONTAL */) {\n                if (this.state === 1 /* SashState.AtMinimum */) {\n                    cursor = 's-resize';\n                }\n                else if (this.state === 2 /* SashState.AtMaximum */) {\n                    cursor = 'n-resize';\n                }\n                else {\n                    cursor = isMacintosh ? 'row-resize' : 'ns-resize';\n                }\n            }\n            else {\n                if (this.state === 1 /* SashState.AtMinimum */) {\n                    cursor = 'e-resize';\n                }\n                else if (this.state === 2 /* SashState.AtMaximum */) {\n                    cursor = 'w-resize';\n                }\n                else {\n                    cursor = isMacintosh ? 'col-resize' : 'ew-resize';\n                }\n            }\n            style.textContent = `* { cursor: ${cursor} !important; }`;\n        };\n        const disposables = new DisposableStore();\n        updateStyle();\n        if (!isMultisashResize) {\n            this.onDidEnablementChange.event(updateStyle, null, disposables);\n        }\n        const onPointerMove = (e) => {\n            EventHelper.stop(e, false);\n            const event = { startX, currentX: e.pageX, startY, currentY: e.pageY, altKey };\n            this._onDidChange.fire(event);\n        };\n        const onPointerUp = (e) => {\n            EventHelper.stop(e, false);\n            style.remove();\n            this.el.classList.remove('active');\n            this._onDidEnd.fire();\n            disposables.dispose();\n            for (const iframe of iframes) {\n                iframe.classList.remove(PointerEventsDisabledCssClass);\n            }\n        };\n        pointerEventFactory.onPointerMove(onPointerMove, null, disposables);\n        pointerEventFactory.onPointerUp(onPointerUp, null, disposables);\n        disposables.add(pointerEventFactory);\n    }\n    onPointerDoublePress(e) {\n        const orthogonalSash = this.getOrthogonalSash(e);\n        if (orthogonalSash) {\n            orthogonalSash._onDidReset.fire();\n        }\n        if (this.linkedSash) {\n            this.linkedSash._onDidReset.fire();\n        }\n        this._onDidReset.fire();\n    }\n    static onMouseEnter(sash, fromLinkedSash = false) {\n        if (sash.el.classList.contains('active')) {\n            sash.hoverDelayer.cancel();\n            sash.el.classList.add('hover');\n        }\n        else {\n            sash.hoverDelayer.trigger(() => sash.el.classList.add('hover'), sash.hoverDelay).then(undefined, () => { });\n        }\n        if (!fromLinkedSash && sash.linkedSash) {\n            Sash.onMouseEnter(sash.linkedSash, true);\n        }\n    }\n    static onMouseLeave(sash, fromLinkedSash = false) {\n        sash.hoverDelayer.cancel();\n        sash.el.classList.remove('hover');\n        if (!fromLinkedSash && sash.linkedSash) {\n            Sash.onMouseLeave(sash.linkedSash, true);\n        }\n    }\n    /**\n     * Forcefully stop any user interactions with this sash.\n     * Useful when hiding a parent component, while the user is still\n     * interacting with the sash.\n     */\n    clearSashHoverState() {\n        Sash.onMouseLeave(this);\n    }\n    /**\n     * Layout the sash. The sash will size and position itself\n     * based on its provided {@link ISashLayoutProvider layout provider}.\n     */\n    layout() {\n        if (this.orientation === 0 /* Orientation.VERTICAL */) {\n            const verticalProvider = this.layoutProvider;\n            this.el.style.left = verticalProvider.getVerticalSashLeft(this) - (this.size / 2) + 'px';\n            if (verticalProvider.getVerticalSashTop) {\n                this.el.style.top = verticalProvider.getVerticalSashTop(this) + 'px';\n            }\n            if (verticalProvider.getVerticalSashHeight) {\n                this.el.style.height = verticalProvider.getVerticalSashHeight(this) + 'px';\n            }\n        }\n        else {\n            const horizontalProvider = this.layoutProvider;\n            this.el.style.top = horizontalProvider.getHorizontalSashTop(this) - (this.size / 2) + 'px';\n            if (horizontalProvider.getHorizontalSashLeft) {\n                this.el.style.left = horizontalProvider.getHorizontalSashLeft(this) + 'px';\n            }\n            if (horizontalProvider.getHorizontalSashWidth) {\n                this.el.style.width = horizontalProvider.getHorizontalSashWidth(this) + 'px';\n            }\n        }\n    }\n    getOrthogonalSash(e) {\n        const target = e.initialTarget ?? e.target;\n        if (!target || !(isHTMLElement(target))) {\n            return undefined;\n        }\n        if (target.classList.contains('orthogonal-drag-handle')) {\n            return target.classList.contains('start') ? this.orthogonalStartSash : this.orthogonalEndSash;\n        }\n        return undefined;\n    }\n    dispose() {\n        super.dispose();\n        this.el.remove();\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,IAAIA,UAAU,GAAI,IAAI,IAAI,IAAI,CAACA,UAAU,IAAK,UAAUC,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAE;EACnF,IAAIC,CAAC,GAAGC,SAAS,CAACC,MAAM;IAAEC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGH,MAAM,GAAGE,IAAI,KAAK,IAAI,GAAGA,IAAI,GAAGK,MAAM,CAACC,wBAAwB,CAACR,MAAM,EAAEC,GAAG,CAAC,GAAGC,IAAI;IAAEO,CAAC;EAC5H,IAAI,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,CAACC,QAAQ,KAAK,UAAU,EAAEL,CAAC,GAAGI,OAAO,CAACC,QAAQ,CAACZ,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,CAAC,CAAC,KAC1H,KAAK,IAAIU,CAAC,GAAGb,UAAU,CAACM,MAAM,GAAG,CAAC,EAAEO,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE,IAAIH,CAAC,GAAGV,UAAU,CAACa,CAAC,CAAC,EAAEN,CAAC,GAAG,CAACH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACH,CAAC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACT,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,GAAGG,CAAC,CAACT,MAAM,EAAEC,GAAG,CAAC,KAAKK,CAAC;EACjJ,OAAOH,CAAC,GAAG,CAAC,IAAIG,CAAC,IAAIC,MAAM,CAACM,cAAc,CAACb,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,EAAEA,CAAC;AACjE,CAAC;AACD,SAASQ,CAAC,EAAEC,MAAM,EAAEC,gBAAgB,EAAEC,WAAW,EAAEC,SAAS,EAAEC,aAAa,QAAQ,cAAc;AACjG,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,SAASC,SAAS,EAAEC,OAAO,QAAQ,gBAAgB;AACnD,SAASC,OAAO,QAAQ,0BAA0B;AAClD,SAASC,OAAO,QAAQ,+BAA+B;AACvD,SAASC,OAAO,QAAQ,0BAA0B;AAClD,SAASC,UAAU,EAAEC,eAAe,EAAEC,YAAY,QAAQ,8BAA8B;AACxF,SAASC,WAAW,QAAQ,6BAA6B;AACzD,OAAO,YAAY;AACnB;AACA;AACA;AACA;AACA,MAAMC,KAAK,GAAG,KAAK;AACnB,OAAO,IAAIC,cAAc,gBACxB,UAAUA,cAAc,EAAE;EACvBA,cAAc,CAAC,OAAO,CAAC,GAAG,OAAO;EACjCA,cAAc,CAAC,OAAO,CAAC,GAAG,OAAO;EACjCA,cAAc,CAAC,MAAM,CAAC,GAAG,MAAM;EAC/BA,cAAc,CAAC,MAAM,CAAC,GAAG,MAAM;EAAC,OAJzBA,cAAc;AAKzB,CAAC,CAAEA,cAAc,IAAsB,CAAC,CAAE,CANjB;AAOzB,IAAIC,UAAU,GAAG,CAAC;AAClB,MAAMC,qBAAqB,GAAG,IAAIR,OAAO,CAAC,CAAC;AAC3C,IAAIS,gBAAgB,GAAG,GAAG;AAC1B,MAAMC,qBAAqB,GAAG,IAAIV,OAAO,CAAC,CAAC;AAC3C,MAAMW,iBAAiB,CAAC;EACpBC,WAAWA,CAACC,EAAE,EAAE;IACZ,IAAI,CAACA,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,WAAW,GAAG,IAAIZ,eAAe,CAAC,CAAC;EAC5C;EACA,IAAIa,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACD,WAAW,CAACE,GAAG,CAAC,IAAIrB,UAAU,CAACF,SAAS,CAAC,IAAI,CAACoB,EAAE,CAAC,EAAE,WAAW,CAAC,CAAC,CAACI,KAAK;EACtF;EACA,IAAIC,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACJ,WAAW,CAACE,GAAG,CAAC,IAAIrB,UAAU,CAACF,SAAS,CAAC,IAAI,CAACoB,EAAE,CAAC,EAAE,SAAS,CAAC,CAAC,CAACI,KAAK;EACpF;EACAE,OAAOA,CAAA,EAAG;IACN,IAAI,CAACL,WAAW,CAACK,OAAO,CAAC,CAAC;EAC9B;AACJ;AACA9C,UAAU,CAAC,CACP0B,OAAO,CACV,EAAEY,iBAAiB,CAACS,SAAS,EAAE,eAAe,EAAE,IAAI,CAAC;AACtD/C,UAAU,CAAC,CACP0B,OAAO,CACV,EAAEY,iBAAiB,CAACS,SAAS,EAAE,aAAa,EAAE,IAAI,CAAC;AACpD,MAAMC,mBAAmB,CAAC;EACtB,IAAIN,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACD,WAAW,CAACE,GAAG,CAAC,IAAIrB,UAAU,CAAC,IAAI,CAACkB,EAAE,EAAEjB,SAAS,CAAC0B,MAAM,CAAC,CAAC,CAACL,KAAK;EAChF;EACA,IAAIC,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACJ,WAAW,CAACE,GAAG,CAAC,IAAIrB,UAAU,CAAC,IAAI,CAACkB,EAAE,EAAEjB,SAAS,CAAC2B,GAAG,CAAC,CAAC,CAACN,KAAK;EAC7E;EACAL,WAAWA,CAACC,EAAE,EAAE;IACZ,IAAI,CAACA,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,WAAW,GAAG,IAAIZ,eAAe,CAAC,CAAC;EAC5C;EACAiB,OAAOA,CAAA,EAAG;IACN,IAAI,CAACL,WAAW,CAACK,OAAO,CAAC,CAAC;EAC9B;AACJ;AACA9C,UAAU,CAAC,CACP0B,OAAO,CACV,EAAEsB,mBAAmB,CAACD,SAAS,EAAE,eAAe,EAAE,IAAI,CAAC;AACxD/C,UAAU,CAAC,CACP0B,OAAO,CACV,EAAEsB,mBAAmB,CAACD,SAAS,EAAE,aAAa,EAAE,IAAI,CAAC;AACtD,MAAMI,6BAA6B,CAAC;EAChC,IAAIT,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACU,OAAO,CAACV,aAAa;EACrC;EACA,IAAIG,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACO,OAAO,CAACP,WAAW;EACnC;EACAN,WAAWA,CAACa,OAAO,EAAE;IACjB,IAAI,CAACA,OAAO,GAAGA,OAAO;EAC1B;EACAN,OAAOA,CAAA,EAAG;IACN;EAAA;AAER;AACA9C,UAAU,CAAC,CACP0B,OAAO,CACV,EAAEyB,6BAA6B,CAACJ,SAAS,EAAE,eAAe,EAAE,IAAI,CAAC;AAClE/C,UAAU,CAAC,CACP0B,OAAO,CACV,EAAEyB,6BAA6B,CAACJ,SAAS,EAAE,aAAa,EAAE,IAAI,CAAC;AAChE,MAAMM,6BAA6B,GAAG,yBAAyB;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,IAAI,SAAS1B,UAAU,CAAC;EACjC,IAAI2B,KAAKA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACC,MAAM;EAAE;EAClC,IAAIC,mBAAmBA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACC,oBAAoB;EAAE;EAC9D,IAAIC,iBAAiBA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACC,kBAAkB;EAAE;EAC1D;AACJ;AACA;AACA;EACI,IAAIL,KAAKA,CAACA,KAAK,EAAE;IACb,IAAI,IAAI,CAACC,MAAM,KAAKD,KAAK,EAAE;MACvB;IACJ;IACA,IAAI,CAACf,EAAE,CAACqB,SAAS,CAACC,MAAM,CAAC,UAAU,EAAEP,KAAK,KAAK,CAAC,CAAC,wBAAwB,CAAC;IAC1E,IAAI,CAACf,EAAE,CAACqB,SAAS,CAACC,MAAM,CAAC,SAAS,EAAEP,KAAK,KAAK,CAAC,CAAC,yBAAyB,CAAC;IAC1E,IAAI,CAACf,EAAE,CAACqB,SAAS,CAACC,MAAM,CAAC,SAAS,EAAEP,KAAK,KAAK,CAAC,CAAC,yBAAyB,CAAC;IAC1E,IAAI,CAACC,MAAM,GAAGD,KAAK;IACnB,IAAI,CAACQ,qBAAqB,CAACC,IAAI,CAACT,KAAK,CAAC;EAC1C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAIE,mBAAmBA,CAACQ,IAAI,EAAE;IAC1B,IAAI,IAAI,CAACP,oBAAoB,KAAKO,IAAI,EAAE;MACpC;IACJ;IACA,IAAI,CAACC,oCAAoC,CAACC,KAAK,CAAC,CAAC;IACjD,IAAI,CAACC,8BAA8B,CAACD,KAAK,CAAC,CAAC;IAC3C,IAAIF,IAAI,EAAE;MACN,MAAMI,QAAQ,GAAId,KAAK,IAAK;QACxB,IAAI,CAACW,oCAAoC,CAACC,KAAK,CAAC,CAAC;QACjD,IAAIZ,KAAK,KAAK,CAAC,CAAC,0BAA0B;UACtC,IAAI,CAACe,0BAA0B,GAAGrD,MAAM,CAAC,IAAI,CAACuB,EAAE,EAAExB,CAAC,CAAC,+BAA+B,CAAC,CAAC;UACrF,IAAI,CAACkD,oCAAoC,CAACvB,GAAG,CAACb,YAAY,CAAC,MAAM,IAAI,CAACwC,0BAA0B,CAACC,MAAM,CAAC,CAAC,CAAC,CAAC;UAC3G,IAAI,CAACL,oCAAoC,CAACvB,GAAG,CAAC,IAAIrB,UAAU,CAAC,IAAI,CAACgD,0BAA0B,EAAE,YAAY,CAAC,CAAC,CAAC1B,KAAK,CAAC,MAAMU,IAAI,CAACkB,YAAY,CAACP,IAAI,CAAC,EAAEQ,SAAS,EAAE,IAAI,CAACP,oCAAoC,CAAC;UACvM,IAAI,CAACA,oCAAoC,CAACvB,GAAG,CAAC,IAAIrB,UAAU,CAAC,IAAI,CAACgD,0BAA0B,EAAE,YAAY,CAAC,CAAC,CAAC1B,KAAK,CAAC,MAAMU,IAAI,CAACoB,YAAY,CAACT,IAAI,CAAC,EAAEQ,SAAS,EAAE,IAAI,CAACP,oCAAoC,CAAC;QAC3M;MACJ,CAAC;MACD,IAAI,CAACE,8BAA8B,CAACzB,GAAG,CAACsB,IAAI,CAACF,qBAAqB,CAACnB,KAAK,CAACyB,QAAQ,EAAE,IAAI,CAAC,CAAC;MACzFA,QAAQ,CAACJ,IAAI,CAACV,KAAK,CAAC;IACxB;IACA,IAAI,CAACG,oBAAoB,GAAGO,IAAI;EACpC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAIN,iBAAiBA,CAACM,IAAI,EAAE;IACxB,IAAI,IAAI,CAACL,kBAAkB,KAAKK,IAAI,EAAE;MAClC;IACJ;IACA,IAAI,CAACU,kCAAkC,CAACR,KAAK,CAAC,CAAC;IAC/C,IAAI,CAACS,4BAA4B,CAACT,KAAK,CAAC,CAAC;IACzC,IAAIF,IAAI,EAAE;MACN,MAAMI,QAAQ,GAAId,KAAK,IAAK;QACxB,IAAI,CAACoB,kCAAkC,CAACR,KAAK,CAAC,CAAC;QAC/C,IAAIZ,KAAK,KAAK,CAAC,CAAC,0BAA0B;UACtC,IAAI,CAACsB,wBAAwB,GAAG5D,MAAM,CAAC,IAAI,CAACuB,EAAE,EAAExB,CAAC,CAAC,6BAA6B,CAAC,CAAC;UACjF,IAAI,CAAC2D,kCAAkC,CAAChC,GAAG,CAACb,YAAY,CAAC,MAAM,IAAI,CAAC+C,wBAAwB,CAACN,MAAM,CAAC,CAAC,CAAC,CAAC;UACvG,IAAI,CAACI,kCAAkC,CAAChC,GAAG,CAAC,IAAIrB,UAAU,CAAC,IAAI,CAACuD,wBAAwB,EAAE,YAAY,CAAC,CAAC,CAACjC,KAAK,CAAC,MAAMU,IAAI,CAACkB,YAAY,CAACP,IAAI,CAAC,EAAEQ,SAAS,EAAE,IAAI,CAACE,kCAAkC,CAAC;UACjM,IAAI,CAACA,kCAAkC,CAAChC,GAAG,CAAC,IAAIrB,UAAU,CAAC,IAAI,CAACuD,wBAAwB,EAAE,YAAY,CAAC,CAAC,CAACjC,KAAK,CAAC,MAAMU,IAAI,CAACoB,YAAY,CAACT,IAAI,CAAC,EAAEQ,SAAS,EAAE,IAAI,CAACE,kCAAkC,CAAC;QACrM;MACJ,CAAC;MACD,IAAI,CAACC,4BAA4B,CAACjC,GAAG,CAACsB,IAAI,CAACF,qBAAqB,CAACnB,KAAK,CAACyB,QAAQ,EAAE,IAAI,CAAC,CAAC;MACvFA,QAAQ,CAACJ,IAAI,CAACV,KAAK,CAAC;IACxB;IACA,IAAI,CAACK,kBAAkB,GAAGK,IAAI;EAClC;EACA1B,WAAWA,CAACuC,SAAS,EAAEC,cAAc,EAAEC,OAAO,EAAE;IAC5C,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,UAAU,GAAG7C,gBAAgB;IAClC,IAAI,CAAC8C,YAAY,GAAG,IAAI,CAACC,SAAS,CAAC,IAAI1D,OAAO,CAAC,IAAI,CAACwD,UAAU,CAAC,CAAC;IAChE,IAAI,CAACzB,MAAM,GAAG,CAAC,CAAC;IAChB,IAAI,CAACO,qBAAqB,GAAG,IAAI,CAACoB,SAAS,CAAC,IAAIxD,OAAO,CAAC,CAAC,CAAC;IAC1D,IAAI,CAACyD,WAAW,GAAG,IAAI,CAACD,SAAS,CAAC,IAAIxD,OAAO,CAAC,CAAC,CAAC;IAChD,IAAI,CAAC0D,YAAY,GAAG,IAAI,CAACF,SAAS,CAAC,IAAIxD,OAAO,CAAC,CAAC,CAAC;IACjD,IAAI,CAAC2D,WAAW,GAAG,IAAI,CAACH,SAAS,CAAC,IAAIxD,OAAO,CAAC,CAAC,CAAC;IAChD,IAAI,CAAC4D,SAAS,GAAG,IAAI,CAACJ,SAAS,CAAC,IAAIxD,OAAO,CAAC,CAAC,CAAC;IAC9C,IAAI,CAACyC,8BAA8B,GAAG,IAAI,CAACe,SAAS,CAAC,IAAItD,eAAe,CAAC,CAAC,CAAC;IAC3E,IAAI,CAACqC,oCAAoC,GAAG,IAAI,CAACiB,SAAS,CAAC,IAAItD,eAAe,CAAC,CAAC,CAAC;IACjF,IAAI,CAAC+C,4BAA4B,GAAG,IAAI,CAACO,SAAS,CAAC,IAAItD,eAAe,CAAC,CAAC,CAAC;IACzE,IAAI,CAAC8C,kCAAkC,GAAG,IAAI,CAACQ,SAAS,CAAC,IAAItD,eAAe,CAAC,CAAC,CAAC;IAC/E;AACR;AACA;IACQ,IAAI,CAAC2D,UAAU,GAAG,IAAI,CAACJ,WAAW,CAACxC,KAAK;IACxC;AACR;AACA;AACA;IACQ,IAAI,CAAC6C,WAAW,GAAG,IAAI,CAACJ,YAAY,CAACzC,KAAK;IAC1C;AACR;AACA;IACQ,IAAI,CAAC8C,UAAU,GAAG,IAAI,CAACJ,WAAW,CAAC1C,KAAK;IACxC;AACR;AACA;IACQ,IAAI,CAAC+C,QAAQ,GAAG,IAAI,CAACJ,SAAS,CAAC3C,KAAK;IACpC;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACgD,UAAU,GAAGnB,SAAS;IAC3B,IAAI,CAACjC,EAAE,GAAGvB,MAAM,CAAC6D,SAAS,EAAE9D,CAAC,CAAC,cAAc,CAAC,CAAC;IAC9C,IAAIgE,OAAO,CAACa,cAAc,EAAE;MACxB,IAAI,CAACrD,EAAE,CAACqB,SAAS,CAAClB,GAAG,CAAC,mBAAmBqC,OAAO,CAACa,cAAc,EAAE,CAAC;IACtE;IACA,IAAI9D,WAAW,EAAE;MACb,IAAI,CAACS,EAAE,CAACqB,SAAS,CAAClB,GAAG,CAAC,KAAK,CAAC;IAChC;IACA,MAAMmD,WAAW,GAAG,IAAI,CAACX,SAAS,CAAC,IAAI7D,UAAU,CAAC,IAAI,CAACkB,EAAE,EAAE,WAAW,CAAC,CAAC,CAACI,KAAK;IAC9E,IAAI,CAACuC,SAAS,CAACW,WAAW,CAACC,CAAC,IAAI,IAAI,CAACC,cAAc,CAACD,CAAC,EAAE,IAAIzD,iBAAiB,CAACwC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;IAChG,MAAMmB,kBAAkB,GAAG,IAAI,CAACd,SAAS,CAAC,IAAI7D,UAAU,CAAC,IAAI,CAACkB,EAAE,EAAE,UAAU,CAAC,CAAC,CAACI,KAAK;IACpF,IAAI,CAACuC,SAAS,CAACc,kBAAkB,CAAC,IAAI,CAACC,oBAAoB,EAAE,IAAI,CAAC,CAAC;IACnE,MAAM1B,YAAY,GAAG,IAAI,CAACW,SAAS,CAAC,IAAI7D,UAAU,CAAC,IAAI,CAACkB,EAAE,EAAE,YAAY,CAAC,CAAC,CAACI,KAAK;IAChF,IAAI,CAACuC,SAAS,CAACX,YAAY,CAAC,MAAMlB,IAAI,CAACkB,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;IAC3D,MAAME,YAAY,GAAG,IAAI,CAACS,SAAS,CAAC,IAAI7D,UAAU,CAAC,IAAI,CAACkB,EAAE,EAAE,YAAY,CAAC,CAAC,CAACI,KAAK;IAChF,IAAI,CAACuC,SAAS,CAACT,YAAY,CAAC,MAAMpB,IAAI,CAACoB,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;IAC3D,IAAI,CAACS,SAAS,CAAC3D,OAAO,CAAC2E,SAAS,CAAC,IAAI,CAAC3D,EAAE,CAAC,CAAC;IAC1C,MAAM4D,YAAY,GAAG,IAAI,CAACjB,SAAS,CAAC,IAAI7D,UAAU,CAAC,IAAI,CAACkB,EAAE,EAAEjB,SAAS,CAAC8E,KAAK,CAAC,CAAC,CAACzD,KAAK;IACnF,IAAI,CAACuC,SAAS,CAACiB,YAAY,CAACL,CAAC,IAAI,IAAI,CAACC,cAAc,CAACD,CAAC,EAAE,IAAI/C,mBAAmB,CAAC,IAAI,CAACR,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;IACjG,MAAM8D,KAAK,GAAG,IAAI,CAACnB,SAAS,CAAC,IAAI7D,UAAU,CAAC,IAAI,CAACkB,EAAE,EAAEjB,SAAS,CAACgF,GAAG,CAAC,CAAC,CAAC3D,KAAK;IAC1E,IAAI4D,gBAAgB,GAAG/B,SAAS;IAChC,IAAI,CAACU,SAAS,CAACmB,KAAK,CAAC1D,KAAK,IAAI;MAC1B,IAAI4D,gBAAgB,EAAE;QAClBC,YAAY,CAACD,gBAAgB,CAAC;QAC9BA,gBAAgB,GAAG/B,SAAS;QAC5B,IAAI,CAACyB,oBAAoB,CAACtD,KAAK,CAAC;QAChC;MACJ;MACA6D,YAAY,CAACD,gBAAgB,CAAC;MAC9BA,gBAAgB,GAAGE,UAAU,CAAC,MAAMF,gBAAgB,GAAG/B,SAAS,EAAE,GAAG,CAAC;IAC1E,CAAC,EAAE,IAAI,CAAC,CAAC;IACT,IAAI,OAAOO,OAAO,CAAC2B,IAAI,KAAK,QAAQ,EAAE;MAClC,IAAI,CAACA,IAAI,GAAG3B,OAAO,CAAC2B,IAAI;MACxB,IAAI3B,OAAO,CAAC4B,WAAW,KAAK,CAAC,CAAC,4BAA4B;QACtD,IAAI,CAACpE,EAAE,CAACqE,KAAK,CAACC,KAAK,GAAG,GAAG,IAAI,CAACH,IAAI,IAAI;MAC1C,CAAC,MACI;QACD,IAAI,CAACnE,EAAE,CAACqE,KAAK,CAACE,MAAM,GAAG,GAAG,IAAI,CAACJ,IAAI,IAAI;MAC3C;IACJ,CAAC,MACI;MACD,IAAI,CAACA,IAAI,GAAGzE,UAAU;MACtB,IAAI,CAACiD,SAAS,CAAChD,qBAAqB,CAACS,KAAK,CAAC+D,IAAI,IAAI;QAC/C,IAAI,CAACA,IAAI,GAAGA,IAAI;QAChB,IAAI,CAACK,MAAM,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC;IACP;IACA,IAAI,CAAC7B,SAAS,CAAC9C,qBAAqB,CAACO,KAAK,CAACqE,KAAK,IAAI,IAAI,CAAChC,UAAU,GAAGgC,KAAK,CAAC,CAAC;IAC7E,IAAI,CAAClC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACtB,mBAAmB,GAAGuB,OAAO,CAACvB,mBAAmB;IACtD,IAAI,CAACE,iBAAiB,GAAGqB,OAAO,CAACrB,iBAAiB;IAClD,IAAI,CAACiD,WAAW,GAAG5B,OAAO,CAAC4B,WAAW,IAAI,CAAC,CAAC;IAC5C,IAAI,IAAI,CAACA,WAAW,KAAK,CAAC,CAAC,8BAA8B;MACrD,IAAI,CAACpE,EAAE,CAACqB,SAAS,CAAClB,GAAG,CAAC,YAAY,CAAC;MACnC,IAAI,CAACH,EAAE,CAACqB,SAAS,CAACU,MAAM,CAAC,UAAU,CAAC;IACxC,CAAC,MACI;MACD,IAAI,CAAC/B,EAAE,CAACqB,SAAS,CAACU,MAAM,CAAC,YAAY,CAAC;MACtC,IAAI,CAAC/B,EAAE,CAACqB,SAAS,CAAClB,GAAG,CAAC,UAAU,CAAC;IACrC;IACA,IAAI,CAACH,EAAE,CAACqB,SAAS,CAACC,MAAM,CAAC,OAAO,EAAE9B,KAAK,CAAC;IACxC,IAAI,CAACgF,MAAM,CAAC,CAAC;EACjB;EACAhB,cAAcA,CAACpD,KAAK,EAAEsE,mBAAmB,EAAE;IACvC/F,WAAW,CAACgG,IAAI,CAACvE,KAAK,CAAC;IACvB,IAAIwE,iBAAiB,GAAG,KAAK;IAC7B,IAAI,CAACxE,KAAK,CAACyE,qBAAqB,EAAE;MAC9B,MAAMC,cAAc,GAAG,IAAI,CAACC,iBAAiB,CAAC3E,KAAK,CAAC;MACpD,IAAI0E,cAAc,EAAE;QAChBF,iBAAiB,GAAG,IAAI;QACxBxE,KAAK,CAACyE,qBAAqB,GAAG,IAAI;QAClCC,cAAc,CAACtB,cAAc,CAACpD,KAAK,EAAE,IAAIO,6BAA6B,CAAC+D,mBAAmB,CAAC,CAAC;MAChG;IACJ;IACA,IAAI,IAAI,CAACtB,UAAU,IAAI,CAAChD,KAAK,CAAC4E,iBAAiB,EAAE;MAC7C5E,KAAK,CAAC4E,iBAAiB,GAAG,IAAI;MAC9B,IAAI,CAAC5B,UAAU,CAACI,cAAc,CAACpD,KAAK,EAAE,IAAIO,6BAA6B,CAAC+D,mBAAmB,CAAC,CAAC;IACjG;IACA,IAAI,CAAC,IAAI,CAAC3D,KAAK,EAAE;MACb;IACJ;IACA,MAAMkE,OAAO,GAAG,IAAI,CAACjF,EAAE,CAACkF,aAAa,CAACC,oBAAoB,CAAC,QAAQ,CAAC;IACpE,KAAK,MAAMC,MAAM,IAAIH,OAAO,EAAE;MAC1BG,MAAM,CAAC/D,SAAS,CAAClB,GAAG,CAACU,6BAA6B,CAAC,CAAC,CAAC;IACzD;IACA,MAAMwE,MAAM,GAAGjF,KAAK,CAACkF,KAAK;IAC1B,MAAMC,MAAM,GAAGnF,KAAK,CAACoF,KAAK;IAC1B,MAAMC,MAAM,GAAGrF,KAAK,CAACqF,MAAM;IAC3B,MAAMC,UAAU,GAAG;MAAEL,MAAM;MAAEM,QAAQ,EAAEN,MAAM;MAAEE,MAAM;MAAEK,QAAQ,EAAEL,MAAM;MAAEE;IAAO,CAAC;IACjF,IAAI,CAACzF,EAAE,CAACqB,SAAS,CAAClB,GAAG,CAAC,QAAQ,CAAC;IAC/B,IAAI,CAACyC,WAAW,CAACpB,IAAI,CAACkE,UAAU,CAAC;IACjC;IACA,MAAMrB,KAAK,GAAG3F,gBAAgB,CAAC,IAAI,CAACsB,EAAE,CAAC;IACvC,MAAM6F,WAAW,GAAGA,CAAA,KAAM;MACtB,IAAIC,MAAM,GAAG,EAAE;MACf,IAAIlB,iBAAiB,EAAE;QACnBkB,MAAM,GAAG,YAAY;MACzB,CAAC,MACI,IAAI,IAAI,CAAC1B,WAAW,KAAK,CAAC,CAAC,8BAA8B;QAC1D,IAAI,IAAI,CAACrD,KAAK,KAAK,CAAC,CAAC,2BAA2B;UAC5C+E,MAAM,GAAG,UAAU;QACvB,CAAC,MACI,IAAI,IAAI,CAAC/E,KAAK,KAAK,CAAC,CAAC,2BAA2B;UACjD+E,MAAM,GAAG,UAAU;QACvB,CAAC,MACI;UACDA,MAAM,GAAGvG,WAAW,GAAG,YAAY,GAAG,WAAW;QACrD;MACJ,CAAC,MACI;QACD,IAAI,IAAI,CAACwB,KAAK,KAAK,CAAC,CAAC,2BAA2B;UAC5C+E,MAAM,GAAG,UAAU;QACvB,CAAC,MACI,IAAI,IAAI,CAAC/E,KAAK,KAAK,CAAC,CAAC,2BAA2B;UACjD+E,MAAM,GAAG,UAAU;QACvB,CAAC,MACI;UACDA,MAAM,GAAGvG,WAAW,GAAG,YAAY,GAAG,WAAW;QACrD;MACJ;MACA8E,KAAK,CAAC0B,WAAW,GAAG,eAAeD,MAAM,gBAAgB;IAC7D,CAAC;IACD,MAAM7F,WAAW,GAAG,IAAIZ,eAAe,CAAC,CAAC;IACzCwG,WAAW,CAAC,CAAC;IACb,IAAI,CAACjB,iBAAiB,EAAE;MACpB,IAAI,CAACrD,qBAAqB,CAACnB,KAAK,CAACyF,WAAW,EAAE,IAAI,EAAE5F,WAAW,CAAC;IACpE;IACA,MAAMC,aAAa,GAAIqD,CAAC,IAAK;MACzB5E,WAAW,CAACgG,IAAI,CAACpB,CAAC,EAAE,KAAK,CAAC;MAC1B,MAAMnD,KAAK,GAAG;QAAEiF,MAAM;QAAEM,QAAQ,EAAEpC,CAAC,CAAC+B,KAAK;QAAEC,MAAM;QAAEK,QAAQ,EAAErC,CAAC,CAACiC,KAAK;QAAEC;MAAO,CAAC;MAC9E,IAAI,CAAC5C,YAAY,CAACrB,IAAI,CAACpB,KAAK,CAAC;IACjC,CAAC;IACD,MAAMC,WAAW,GAAIkD,CAAC,IAAK;MACvB5E,WAAW,CAACgG,IAAI,CAACpB,CAAC,EAAE,KAAK,CAAC;MAC1Bc,KAAK,CAACtC,MAAM,CAAC,CAAC;MACd,IAAI,CAAC/B,EAAE,CAACqB,SAAS,CAACU,MAAM,CAAC,QAAQ,CAAC;MAClC,IAAI,CAACgB,SAAS,CAACvB,IAAI,CAAC,CAAC;MACrBvB,WAAW,CAACK,OAAO,CAAC,CAAC;MACrB,KAAK,MAAM8E,MAAM,IAAIH,OAAO,EAAE;QAC1BG,MAAM,CAAC/D,SAAS,CAACU,MAAM,CAAClB,6BAA6B,CAAC;MAC1D;IACJ,CAAC;IACD6D,mBAAmB,CAACxE,aAAa,CAACA,aAAa,EAAE,IAAI,EAAED,WAAW,CAAC;IACnEyE,mBAAmB,CAACrE,WAAW,CAACA,WAAW,EAAE,IAAI,EAAEJ,WAAW,CAAC;IAC/DA,WAAW,CAACE,GAAG,CAACuE,mBAAmB,CAAC;EACxC;EACAhB,oBAAoBA,CAACH,CAAC,EAAE;IACpB,MAAMuB,cAAc,GAAG,IAAI,CAACC,iBAAiB,CAACxB,CAAC,CAAC;IAChD,IAAIuB,cAAc,EAAE;MAChBA,cAAc,CAAChC,WAAW,CAACtB,IAAI,CAAC,CAAC;IACrC;IACA,IAAI,IAAI,CAAC4B,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAACN,WAAW,CAACtB,IAAI,CAAC,CAAC;IACtC;IACA,IAAI,CAACsB,WAAW,CAACtB,IAAI,CAAC,CAAC;EAC3B;EACA,OAAOQ,YAAYA,CAACP,IAAI,EAAEuE,cAAc,GAAG,KAAK,EAAE;IAC9C,IAAIvE,IAAI,CAACzB,EAAE,CAACqB,SAAS,CAAC4E,QAAQ,CAAC,QAAQ,CAAC,EAAE;MACtCxE,IAAI,CAACiB,YAAY,CAACwD,MAAM,CAAC,CAAC;MAC1BzE,IAAI,CAACzB,EAAE,CAACqB,SAAS,CAAClB,GAAG,CAAC,OAAO,CAAC;IAClC,CAAC,MACI;MACDsB,IAAI,CAACiB,YAAY,CAACyD,OAAO,CAAC,MAAM1E,IAAI,CAACzB,EAAE,CAACqB,SAAS,CAAClB,GAAG,CAAC,OAAO,CAAC,EAAEsB,IAAI,CAACgB,UAAU,CAAC,CAAC2D,IAAI,CAACnE,SAAS,EAAE,MAAM,CAAE,CAAC,CAAC;IAC/G;IACA,IAAI,CAAC+D,cAAc,IAAIvE,IAAI,CAAC2B,UAAU,EAAE;MACpCtC,IAAI,CAACkB,YAAY,CAACP,IAAI,CAAC2B,UAAU,EAAE,IAAI,CAAC;IAC5C;EACJ;EACA,OAAOlB,YAAYA,CAACT,IAAI,EAAEuE,cAAc,GAAG,KAAK,EAAE;IAC9CvE,IAAI,CAACiB,YAAY,CAACwD,MAAM,CAAC,CAAC;IAC1BzE,IAAI,CAACzB,EAAE,CAACqB,SAAS,CAACU,MAAM,CAAC,OAAO,CAAC;IACjC,IAAI,CAACiE,cAAc,IAAIvE,IAAI,CAAC2B,UAAU,EAAE;MACpCtC,IAAI,CAACoB,YAAY,CAACT,IAAI,CAAC2B,UAAU,EAAE,IAAI,CAAC;IAC5C;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIiD,mBAAmBA,CAAA,EAAG;IAClBvF,IAAI,CAACoB,YAAY,CAAC,IAAI,CAAC;EAC3B;EACA;AACJ;AACA;AACA;EACIsC,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAACJ,WAAW,KAAK,CAAC,CAAC,4BAA4B;MACnD,MAAMkC,gBAAgB,GAAG,IAAI,CAAC/D,cAAc;MAC5C,IAAI,CAACvC,EAAE,CAACqE,KAAK,CAACkC,IAAI,GAAGD,gBAAgB,CAACE,mBAAmB,CAAC,IAAI,CAAC,GAAI,IAAI,CAACrC,IAAI,GAAG,CAAE,GAAG,IAAI;MACxF,IAAImC,gBAAgB,CAACG,kBAAkB,EAAE;QACrC,IAAI,CAACzG,EAAE,CAACqE,KAAK,CAACqC,GAAG,GAAGJ,gBAAgB,CAACG,kBAAkB,CAAC,IAAI,CAAC,GAAG,IAAI;MACxE;MACA,IAAIH,gBAAgB,CAACK,qBAAqB,EAAE;QACxC,IAAI,CAAC3G,EAAE,CAACqE,KAAK,CAACE,MAAM,GAAG+B,gBAAgB,CAACK,qBAAqB,CAAC,IAAI,CAAC,GAAG,IAAI;MAC9E;IACJ,CAAC,MACI;MACD,MAAMC,kBAAkB,GAAG,IAAI,CAACrE,cAAc;MAC9C,IAAI,CAACvC,EAAE,CAACqE,KAAK,CAACqC,GAAG,GAAGE,kBAAkB,CAACC,oBAAoB,CAAC,IAAI,CAAC,GAAI,IAAI,CAAC1C,IAAI,GAAG,CAAE,GAAG,IAAI;MAC1F,IAAIyC,kBAAkB,CAACE,qBAAqB,EAAE;QAC1C,IAAI,CAAC9G,EAAE,CAACqE,KAAK,CAACkC,IAAI,GAAGK,kBAAkB,CAACE,qBAAqB,CAAC,IAAI,CAAC,GAAG,IAAI;MAC9E;MACA,IAAIF,kBAAkB,CAACG,sBAAsB,EAAE;QAC3C,IAAI,CAAC/G,EAAE,CAACqE,KAAK,CAACC,KAAK,GAAGsC,kBAAkB,CAACG,sBAAsB,CAAC,IAAI,CAAC,GAAG,IAAI;MAChF;IACJ;EACJ;EACAhC,iBAAiBA,CAACxB,CAAC,EAAE;IACjB,MAAM7F,MAAM,GAAG6F,CAAC,CAACyD,aAAa,IAAIzD,CAAC,CAAC7F,MAAM;IAC1C,IAAI,CAACA,MAAM,IAAI,CAAEmB,aAAa,CAACnB,MAAM,CAAE,EAAE;MACrC,OAAOuE,SAAS;IACpB;IACA,IAAIvE,MAAM,CAAC2D,SAAS,CAAC4E,QAAQ,CAAC,wBAAwB,CAAC,EAAE;MACrD,OAAOvI,MAAM,CAAC2D,SAAS,CAAC4E,QAAQ,CAAC,OAAO,CAAC,GAAG,IAAI,CAAChF,mBAAmB,GAAG,IAAI,CAACE,iBAAiB;IACjG;IACA,OAAOc,SAAS;EACpB;EACA3B,OAAOA,CAAA,EAAG;IACN,KAAK,CAACA,OAAO,CAAC,CAAC;IACf,IAAI,CAACN,EAAE,CAAC+B,MAAM,CAAC,CAAC;EACpB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}