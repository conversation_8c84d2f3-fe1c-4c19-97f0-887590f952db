{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nconst emptyArr = [];\n/**\n * Represents an immutable set that works best for a small number of elements (less than 32).\n * It uses bits to encode element membership efficiently.\n*/\nexport class SmallImmutableSet {\n  static {\n    this.cache = new Array(129);\n  }\n  static create(items, additionalItems) {\n    if (items <= 128 && additionalItems.length === 0) {\n      // We create a cache of 128=2^7 elements to cover all sets with up to 7 (dense) elements.\n      let cached = SmallImmutableSet.cache[items];\n      if (!cached) {\n        cached = new SmallImmutableSet(items, additionalItems);\n        SmallImmutableSet.cache[items] = cached;\n      }\n      return cached;\n    }\n    return new SmallImmutableSet(items, additionalItems);\n  }\n  static {\n    this.empty = SmallImmutableSet.create(0, emptyArr);\n  }\n  static getEmpty() {\n    return this.empty;\n  }\n  constructor(items, additionalItems) {\n    this.items = items;\n    this.additionalItems = additionalItems;\n  }\n  add(value, keyProvider) {\n    const key = keyProvider.getKey(value);\n    let idx = key >> 5; // divided by 32\n    if (idx === 0) {\n      // fast path\n      const newItem = 1 << key | this.items;\n      if (newItem === this.items) {\n        return this;\n      }\n      return SmallImmutableSet.create(newItem, this.additionalItems);\n    }\n    idx--;\n    const newItems = this.additionalItems.slice(0);\n    while (newItems.length < idx) {\n      newItems.push(0);\n    }\n    newItems[idx] |= 1 << (key & 31);\n    return SmallImmutableSet.create(this.items, newItems);\n  }\n  merge(other) {\n    const merged = this.items | other.items;\n    if (this.additionalItems === emptyArr && other.additionalItems === emptyArr) {\n      // fast path\n      if (merged === this.items) {\n        return this;\n      }\n      if (merged === other.items) {\n        return other;\n      }\n      return SmallImmutableSet.create(merged, emptyArr);\n    }\n    // This can be optimized, but it's not a common case\n    const newItems = [];\n    for (let i = 0; i < Math.max(this.additionalItems.length, other.additionalItems.length); i++) {\n      const item1 = this.additionalItems[i] || 0;\n      const item2 = other.additionalItems[i] || 0;\n      newItems.push(item1 | item2);\n    }\n    return SmallImmutableSet.create(merged, newItems);\n  }\n  intersects(other) {\n    if ((this.items & other.items) !== 0) {\n      return true;\n    }\n    for (let i = 0; i < Math.min(this.additionalItems.length, other.additionalItems.length); i++) {\n      if ((this.additionalItems[i] & other.additionalItems[i]) !== 0) {\n        return true;\n      }\n    }\n    return false;\n  }\n}\nexport const identityKeyProvider = {\n  getKey(value) {\n    return value;\n  }\n};\n/**\n * Assigns values a unique incrementing key.\n*/\nexport class DenseKeyProvider {\n  constructor() {\n    this.items = new Map();\n  }\n  getKey(value) {\n    let existing = this.items.get(value);\n    if (existing === undefined) {\n      existing = this.items.size;\n      this.items.set(value, existing);\n    }\n    return existing;\n  }\n}", "map": {"version": 3, "names": ["emptyArr", "SmallImmutableSet", "cache", "Array", "create", "items", "additionalItems", "length", "cached", "empty", "getEmpty", "constructor", "add", "value", "<PERSON><PERSON><PERSON><PERSON>", "key", "<PERSON><PERSON><PERSON>", "idx", "newItem", "newItems", "slice", "push", "merge", "other", "merged", "i", "Math", "max", "item1", "item2", "intersects", "min", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Map", "existing", "get", "undefined", "size", "set"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/smallImmutableSet.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nconst emptyArr = [];\n/**\n * Represents an immutable set that works best for a small number of elements (less than 32).\n * It uses bits to encode element membership efficiently.\n*/\nexport class SmallImmutableSet {\n    static { this.cache = new Array(129); }\n    static create(items, additionalItems) {\n        if (items <= 128 && additionalItems.length === 0) {\n            // We create a cache of 128=2^7 elements to cover all sets with up to 7 (dense) elements.\n            let cached = SmallImmutableSet.cache[items];\n            if (!cached) {\n                cached = new SmallImmutableSet(items, additionalItems);\n                SmallImmutableSet.cache[items] = cached;\n            }\n            return cached;\n        }\n        return new SmallImmutableSet(items, additionalItems);\n    }\n    static { this.empty = SmallImmutableSet.create(0, emptyArr); }\n    static getEmpty() {\n        return this.empty;\n    }\n    constructor(items, additionalItems) {\n        this.items = items;\n        this.additionalItems = additionalItems;\n    }\n    add(value, keyProvider) {\n        const key = keyProvider.getKey(value);\n        let idx = key >> 5; // divided by 32\n        if (idx === 0) {\n            // fast path\n            const newItem = (1 << key) | this.items;\n            if (newItem === this.items) {\n                return this;\n            }\n            return SmallImmutableSet.create(newItem, this.additionalItems);\n        }\n        idx--;\n        const newItems = this.additionalItems.slice(0);\n        while (newItems.length < idx) {\n            newItems.push(0);\n        }\n        newItems[idx] |= 1 << (key & 31);\n        return SmallImmutableSet.create(this.items, newItems);\n    }\n    merge(other) {\n        const merged = this.items | other.items;\n        if (this.additionalItems === emptyArr && other.additionalItems === emptyArr) {\n            // fast path\n            if (merged === this.items) {\n                return this;\n            }\n            if (merged === other.items) {\n                return other;\n            }\n            return SmallImmutableSet.create(merged, emptyArr);\n        }\n        // This can be optimized, but it's not a common case\n        const newItems = [];\n        for (let i = 0; i < Math.max(this.additionalItems.length, other.additionalItems.length); i++) {\n            const item1 = this.additionalItems[i] || 0;\n            const item2 = other.additionalItems[i] || 0;\n            newItems.push(item1 | item2);\n        }\n        return SmallImmutableSet.create(merged, newItems);\n    }\n    intersects(other) {\n        if ((this.items & other.items) !== 0) {\n            return true;\n        }\n        for (let i = 0; i < Math.min(this.additionalItems.length, other.additionalItems.length); i++) {\n            if ((this.additionalItems[i] & other.additionalItems[i]) !== 0) {\n                return true;\n            }\n        }\n        return false;\n    }\n}\nexport const identityKeyProvider = {\n    getKey(value) {\n        return value;\n    }\n};\n/**\n * Assigns values a unique incrementing key.\n*/\nexport class DenseKeyProvider {\n    constructor() {\n        this.items = new Map();\n    }\n    getKey(value) {\n        let existing = this.items.get(value);\n        if (existing === undefined) {\n            existing = this.items.size;\n            this.items.set(value, existing);\n        }\n        return existing;\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,MAAMA,QAAQ,GAAG,EAAE;AACnB;AACA;AACA;AACA;AACA,OAAO,MAAMC,iBAAiB,CAAC;EAC3B;IAAS,IAAI,CAACC,KAAK,GAAG,IAAIC,KAAK,CAAC,GAAG,CAAC;EAAE;EACtC,OAAOC,MAAMA,CAACC,KAAK,EAAEC,eAAe,EAAE;IAClC,IAAID,KAAK,IAAI,GAAG,IAAIC,eAAe,CAACC,MAAM,KAAK,CAAC,EAAE;MAC9C;MACA,IAAIC,MAAM,GAAGP,iBAAiB,CAACC,KAAK,CAACG,KAAK,CAAC;MAC3C,IAAI,CAACG,MAAM,EAAE;QACTA,MAAM,GAAG,IAAIP,iBAAiB,CAACI,KAAK,EAAEC,eAAe,CAAC;QACtDL,iBAAiB,CAACC,KAAK,CAACG,KAAK,CAAC,GAAGG,MAAM;MAC3C;MACA,OAAOA,MAAM;IACjB;IACA,OAAO,IAAIP,iBAAiB,CAACI,KAAK,EAAEC,eAAe,CAAC;EACxD;EACA;IAAS,IAAI,CAACG,KAAK,GAAGR,iBAAiB,CAACG,MAAM,CAAC,CAAC,EAAEJ,QAAQ,CAAC;EAAE;EAC7D,OAAOU,QAAQA,CAAA,EAAG;IACd,OAAO,IAAI,CAACD,KAAK;EACrB;EACAE,WAAWA,CAACN,KAAK,EAAEC,eAAe,EAAE;IAChC,IAAI,CAACD,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,eAAe,GAAGA,eAAe;EAC1C;EACAM,GAAGA,CAACC,KAAK,EAAEC,WAAW,EAAE;IACpB,MAAMC,GAAG,GAAGD,WAAW,CAACE,MAAM,CAACH,KAAK,CAAC;IACrC,IAAII,GAAG,GAAGF,GAAG,IAAI,CAAC,CAAC,CAAC;IACpB,IAAIE,GAAG,KAAK,CAAC,EAAE;MACX;MACA,MAAMC,OAAO,GAAI,CAAC,IAAIH,GAAG,GAAI,IAAI,CAACV,KAAK;MACvC,IAAIa,OAAO,KAAK,IAAI,CAACb,KAAK,EAAE;QACxB,OAAO,IAAI;MACf;MACA,OAAOJ,iBAAiB,CAACG,MAAM,CAACc,OAAO,EAAE,IAAI,CAACZ,eAAe,CAAC;IAClE;IACAW,GAAG,EAAE;IACL,MAAME,QAAQ,GAAG,IAAI,CAACb,eAAe,CAACc,KAAK,CAAC,CAAC,CAAC;IAC9C,OAAOD,QAAQ,CAACZ,MAAM,GAAGU,GAAG,EAAE;MAC1BE,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAC;IACpB;IACAF,QAAQ,CAACF,GAAG,CAAC,IAAI,CAAC,KAAKF,GAAG,GAAG,EAAE,CAAC;IAChC,OAAOd,iBAAiB,CAACG,MAAM,CAAC,IAAI,CAACC,KAAK,EAAEc,QAAQ,CAAC;EACzD;EACAG,KAAKA,CAACC,KAAK,EAAE;IACT,MAAMC,MAAM,GAAG,IAAI,CAACnB,KAAK,GAAGkB,KAAK,CAAClB,KAAK;IACvC,IAAI,IAAI,CAACC,eAAe,KAAKN,QAAQ,IAAIuB,KAAK,CAACjB,eAAe,KAAKN,QAAQ,EAAE;MACzE;MACA,IAAIwB,MAAM,KAAK,IAAI,CAACnB,KAAK,EAAE;QACvB,OAAO,IAAI;MACf;MACA,IAAImB,MAAM,KAAKD,KAAK,CAAClB,KAAK,EAAE;QACxB,OAAOkB,KAAK;MAChB;MACA,OAAOtB,iBAAiB,CAACG,MAAM,CAACoB,MAAM,EAAExB,QAAQ,CAAC;IACrD;IACA;IACA,MAAMmB,QAAQ,GAAG,EAAE;IACnB,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,IAAI,CAACC,GAAG,CAAC,IAAI,CAACrB,eAAe,CAACC,MAAM,EAAEgB,KAAK,CAACjB,eAAe,CAACC,MAAM,CAAC,EAAEkB,CAAC,EAAE,EAAE;MAC1F,MAAMG,KAAK,GAAG,IAAI,CAACtB,eAAe,CAACmB,CAAC,CAAC,IAAI,CAAC;MAC1C,MAAMI,KAAK,GAAGN,KAAK,CAACjB,eAAe,CAACmB,CAAC,CAAC,IAAI,CAAC;MAC3CN,QAAQ,CAACE,IAAI,CAACO,KAAK,GAAGC,KAAK,CAAC;IAChC;IACA,OAAO5B,iBAAiB,CAACG,MAAM,CAACoB,MAAM,EAAEL,QAAQ,CAAC;EACrD;EACAW,UAAUA,CAACP,KAAK,EAAE;IACd,IAAI,CAAC,IAAI,CAAClB,KAAK,GAAGkB,KAAK,CAAClB,KAAK,MAAM,CAAC,EAAE;MAClC,OAAO,IAAI;IACf;IACA,KAAK,IAAIoB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,IAAI,CAACK,GAAG,CAAC,IAAI,CAACzB,eAAe,CAACC,MAAM,EAAEgB,KAAK,CAACjB,eAAe,CAACC,MAAM,CAAC,EAAEkB,CAAC,EAAE,EAAE;MAC1F,IAAI,CAAC,IAAI,CAACnB,eAAe,CAACmB,CAAC,CAAC,GAAGF,KAAK,CAACjB,eAAe,CAACmB,CAAC,CAAC,MAAM,CAAC,EAAE;QAC5D,OAAO,IAAI;MACf;IACJ;IACA,OAAO,KAAK;EAChB;AACJ;AACA,OAAO,MAAMO,mBAAmB,GAAG;EAC/BhB,MAAMA,CAACH,KAAK,EAAE;IACV,OAAOA,KAAK;EAChB;AACJ,CAAC;AACD;AACA;AACA;AACA,OAAO,MAAMoB,gBAAgB,CAAC;EAC1BtB,WAAWA,CAAA,EAAG;IACV,IAAI,CAACN,KAAK,GAAG,IAAI6B,GAAG,CAAC,CAAC;EAC1B;EACAlB,MAAMA,CAACH,KAAK,EAAE;IACV,IAAIsB,QAAQ,GAAG,IAAI,CAAC9B,KAAK,CAAC+B,GAAG,CAACvB,KAAK,CAAC;IACpC,IAAIsB,QAAQ,KAAKE,SAAS,EAAE;MACxBF,QAAQ,GAAG,IAAI,CAAC9B,KAAK,CAACiC,IAAI;MAC1B,IAAI,CAACjC,KAAK,CAACkC,GAAG,CAAC1B,KAAK,EAAEsB,QAAQ,CAAC;IACnC;IACA,OAAOA,QAAQ;EACnB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}