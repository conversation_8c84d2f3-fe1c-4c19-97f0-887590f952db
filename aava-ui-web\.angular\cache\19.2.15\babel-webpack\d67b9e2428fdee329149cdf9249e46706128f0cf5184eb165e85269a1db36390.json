{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { MinimapCharRenderer } from './minimapCharRenderer.js';\nimport { allCharCodes } from './minimapCharSheet.js';\nimport { prebakedMiniMaps } from './minimapPreBaked.js';\nimport { toUint8 } from '../../../../base/common/uint.js';\n/**\n * Creates character renderers. It takes a 'scale' that determines how large\n * characters should be drawn. Using this, it draws data into a canvas and\n * then downsamples the characters as necessary for the current display.\n * This makes rendering more efficient, rather than drawing a full (tiny)\n * font, or downsampling in real-time.\n */\nexport class MinimapCharRendererFactory {\n  /**\n   * Creates a new character renderer factory with the given scale.\n   */\n  static create(scale, fontFamily) {\n    // renderers are immutable. By default we'll 'create' a new minimap\n    // character renderer whenever we switch editors, no need to do extra work.\n    if (this.lastCreated && scale === this.lastCreated.scale && fontFamily === this.lastFontFamily) {\n      return this.lastCreated;\n    }\n    let factory;\n    if (prebakedMiniMaps[scale]) {\n      factory = new MinimapCharRenderer(prebakedMiniMaps[scale](), scale);\n    } else {\n      factory = MinimapCharRendererFactory.createFromSampleData(MinimapCharRendererFactory.createSampleData(fontFamily).data, scale);\n    }\n    this.lastFontFamily = fontFamily;\n    this.lastCreated = factory;\n    return factory;\n  }\n  /**\n   * Creates the font sample data, writing to a canvas.\n   */\n  static createSampleData(fontFamily) {\n    const canvas = document.createElement('canvas');\n    const ctx = canvas.getContext('2d');\n    canvas.style.height = `${16 /* Constants.SAMPLED_CHAR_HEIGHT */}px`;\n    canvas.height = 16 /* Constants.SAMPLED_CHAR_HEIGHT */;\n    canvas.width = 96 /* Constants.CHAR_COUNT */ * 10 /* Constants.SAMPLED_CHAR_WIDTH */;\n    canvas.style.width = 96 /* Constants.CHAR_COUNT */ * 10 /* Constants.SAMPLED_CHAR_WIDTH */ + 'px';\n    ctx.fillStyle = '#ffffff';\n    ctx.font = `bold ${16 /* Constants.SAMPLED_CHAR_HEIGHT */}px ${fontFamily}`;\n    ctx.textBaseline = 'middle';\n    let x = 0;\n    for (const code of allCharCodes) {\n      ctx.fillText(String.fromCharCode(code), x, 16 /* Constants.SAMPLED_CHAR_HEIGHT */ / 2);\n      x += 10 /* Constants.SAMPLED_CHAR_WIDTH */;\n    }\n    return ctx.getImageData(0, 0, 96 /* Constants.CHAR_COUNT */ * 10 /* Constants.SAMPLED_CHAR_WIDTH */, 16 /* Constants.SAMPLED_CHAR_HEIGHT */);\n  }\n  /**\n   * Creates a character renderer from the canvas sample data.\n   */\n  static createFromSampleData(source, scale) {\n    const expectedLength = 16 /* Constants.SAMPLED_CHAR_HEIGHT */ * 10 /* Constants.SAMPLED_CHAR_WIDTH */ * 4 /* Constants.RGBA_CHANNELS_CNT */ * 96 /* Constants.CHAR_COUNT */;\n    if (source.length !== expectedLength) {\n      throw new Error('Unexpected source in MinimapCharRenderer');\n    }\n    const charData = MinimapCharRendererFactory._downsample(source, scale);\n    return new MinimapCharRenderer(charData, scale);\n  }\n  static _downsampleChar(source, sourceOffset, dest, destOffset, scale) {\n    const width = 1 /* Constants.BASE_CHAR_WIDTH */ * scale;\n    const height = 2 /* Constants.BASE_CHAR_HEIGHT */ * scale;\n    let targetIndex = destOffset;\n    let brightest = 0;\n    // This is essentially an ad-hoc rescaling algorithm. Standard approaches\n    // like bicubic interpolation are awesome for scaling between image sizes,\n    // but don't work so well when scaling to very small pixel values, we end\n    // up with blurry, indistinct forms.\n    //\n    // The approach taken here is simply mapping each source pixel to the target\n    // pixels, and taking the weighted values for all pixels in each, and then\n    // averaging them out. Finally we apply an intensity boost in _downsample,\n    // since when scaling to the smallest pixel sizes there's more black space\n    // which causes characters to be much less distinct.\n    for (let y = 0; y < height; y++) {\n      // 1. For this destination pixel, get the source pixels we're sampling\n      // from (x1, y1) to the next pixel (x2, y2)\n      const sourceY1 = y / height * 16 /* Constants.SAMPLED_CHAR_HEIGHT */;\n      const sourceY2 = (y + 1) / height * 16 /* Constants.SAMPLED_CHAR_HEIGHT */;\n      for (let x = 0; x < width; x++) {\n        const sourceX1 = x / width * 10 /* Constants.SAMPLED_CHAR_WIDTH */;\n        const sourceX2 = (x + 1) / width * 10 /* Constants.SAMPLED_CHAR_WIDTH */;\n        // 2. Sample all of them, summing them up and weighting them. Similar\n        // to bilinear interpolation.\n        let value = 0;\n        let samples = 0;\n        for (let sy = sourceY1; sy < sourceY2; sy++) {\n          const sourceRow = sourceOffset + Math.floor(sy) * 3840 /* Constants.RGBA_SAMPLED_ROW_WIDTH */;\n          const yBalance = 1 - (sy - Math.floor(sy));\n          for (let sx = sourceX1; sx < sourceX2; sx++) {\n            const xBalance = 1 - (sx - Math.floor(sx));\n            const sourceIndex = sourceRow + Math.floor(sx) * 4 /* Constants.RGBA_CHANNELS_CNT */;\n            const weight = xBalance * yBalance;\n            samples += weight;\n            value += source[sourceIndex] * source[sourceIndex + 3] / 255 * weight;\n          }\n        }\n        const final = value / samples;\n        brightest = Math.max(brightest, final);\n        dest[targetIndex++] = toUint8(final);\n      }\n    }\n    return brightest;\n  }\n  static _downsample(data, scale) {\n    const pixelsPerCharacter = 2 /* Constants.BASE_CHAR_HEIGHT */ * scale * 1 /* Constants.BASE_CHAR_WIDTH */ * scale;\n    const resultLen = pixelsPerCharacter * 96 /* Constants.CHAR_COUNT */;\n    const result = new Uint8ClampedArray(resultLen);\n    let resultOffset = 0;\n    let sourceOffset = 0;\n    let brightest = 0;\n    for (let charIndex = 0; charIndex < 96 /* Constants.CHAR_COUNT */; charIndex++) {\n      brightest = Math.max(brightest, this._downsampleChar(data, sourceOffset, result, resultOffset, scale));\n      resultOffset += pixelsPerCharacter;\n      sourceOffset += 10 /* Constants.SAMPLED_CHAR_WIDTH */ * 4 /* Constants.RGBA_CHANNELS_CNT */;\n    }\n    if (brightest > 0) {\n      const adjust = 255 / brightest;\n      for (let i = 0; i < resultLen; i++) {\n        result[i] *= adjust;\n      }\n    }\n    return result;\n  }\n}", "map": {"version": 3, "names": ["Minimap<PERSON>har<PERSON><PERSON><PERSON>", "allCharCodes", "prebakedMiniMaps", "toUint8", "MinimapCharRendererFactory", "create", "scale", "fontFamily", "lastCreated", "lastFontFamily", "factory", "createFromSampleData", "createSampleData", "data", "canvas", "document", "createElement", "ctx", "getContext", "style", "height", "width", "fillStyle", "font", "textBaseline", "x", "code", "fillText", "String", "fromCharCode", "getImageData", "source", "<PERSON><PERSON><PERSON><PERSON>", "length", "Error", "char<PERSON><PERSON>", "_downsample", "_downsampleChar", "sourceOffset", "dest", "destOffset", "targetIndex", "brightest", "y", "sourceY1", "sourceY2", "sourceX1", "sourceX2", "value", "samples", "sy", "sourceRow", "Math", "floor", "yBalance", "sx", "xBalance", "sourceIndex", "weight", "final", "max", "pixelsPerCharacter", "resultLen", "result", "Uint8ClampedArray", "resultOffset", "charIndex", "adjust", "i"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/browser/viewParts/minimap/minimapCharRendererFactory.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { MinimapCharRenderer } from './minimapCharRenderer.js';\nimport { allCharCodes } from './minimapCharSheet.js';\nimport { prebakedMiniMaps } from './minimapPreBaked.js';\nimport { toUint8 } from '../../../../base/common/uint.js';\n/**\n * Creates character renderers. It takes a 'scale' that determines how large\n * characters should be drawn. Using this, it draws data into a canvas and\n * then downsamples the characters as necessary for the current display.\n * This makes rendering more efficient, rather than drawing a full (tiny)\n * font, or downsampling in real-time.\n */\nexport class MinimapCharRendererFactory {\n    /**\n     * Creates a new character renderer factory with the given scale.\n     */\n    static create(scale, fontFamily) {\n        // renderers are immutable. By default we'll 'create' a new minimap\n        // character renderer whenever we switch editors, no need to do extra work.\n        if (this.lastCreated && scale === this.lastCreated.scale && fontFamily === this.lastFontFamily) {\n            return this.lastCreated;\n        }\n        let factory;\n        if (prebakedMiniMaps[scale]) {\n            factory = new MinimapCharRenderer(prebakedMiniMaps[scale](), scale);\n        }\n        else {\n            factory = MinimapCharRendererFactory.createFromSampleData(MinimapCharRendererFactory.createSampleData(fontFamily).data, scale);\n        }\n        this.lastFontFamily = fontFamily;\n        this.lastCreated = factory;\n        return factory;\n    }\n    /**\n     * Creates the font sample data, writing to a canvas.\n     */\n    static createSampleData(fontFamily) {\n        const canvas = document.createElement('canvas');\n        const ctx = canvas.getContext('2d');\n        canvas.style.height = `${16 /* Constants.SAMPLED_CHAR_HEIGHT */}px`;\n        canvas.height = 16 /* Constants.SAMPLED_CHAR_HEIGHT */;\n        canvas.width = 96 /* Constants.CHAR_COUNT */ * 10 /* Constants.SAMPLED_CHAR_WIDTH */;\n        canvas.style.width = 96 /* Constants.CHAR_COUNT */ * 10 /* Constants.SAMPLED_CHAR_WIDTH */ + 'px';\n        ctx.fillStyle = '#ffffff';\n        ctx.font = `bold ${16 /* Constants.SAMPLED_CHAR_HEIGHT */}px ${fontFamily}`;\n        ctx.textBaseline = 'middle';\n        let x = 0;\n        for (const code of allCharCodes) {\n            ctx.fillText(String.fromCharCode(code), x, 16 /* Constants.SAMPLED_CHAR_HEIGHT */ / 2);\n            x += 10 /* Constants.SAMPLED_CHAR_WIDTH */;\n        }\n        return ctx.getImageData(0, 0, 96 /* Constants.CHAR_COUNT */ * 10 /* Constants.SAMPLED_CHAR_WIDTH */, 16 /* Constants.SAMPLED_CHAR_HEIGHT */);\n    }\n    /**\n     * Creates a character renderer from the canvas sample data.\n     */\n    static createFromSampleData(source, scale) {\n        const expectedLength = 16 /* Constants.SAMPLED_CHAR_HEIGHT */ * 10 /* Constants.SAMPLED_CHAR_WIDTH */ * 4 /* Constants.RGBA_CHANNELS_CNT */ * 96 /* Constants.CHAR_COUNT */;\n        if (source.length !== expectedLength) {\n            throw new Error('Unexpected source in MinimapCharRenderer');\n        }\n        const charData = MinimapCharRendererFactory._downsample(source, scale);\n        return new MinimapCharRenderer(charData, scale);\n    }\n    static _downsampleChar(source, sourceOffset, dest, destOffset, scale) {\n        const width = 1 /* Constants.BASE_CHAR_WIDTH */ * scale;\n        const height = 2 /* Constants.BASE_CHAR_HEIGHT */ * scale;\n        let targetIndex = destOffset;\n        let brightest = 0;\n        // This is essentially an ad-hoc rescaling algorithm. Standard approaches\n        // like bicubic interpolation are awesome for scaling between image sizes,\n        // but don't work so well when scaling to very small pixel values, we end\n        // up with blurry, indistinct forms.\n        //\n        // The approach taken here is simply mapping each source pixel to the target\n        // pixels, and taking the weighted values for all pixels in each, and then\n        // averaging them out. Finally we apply an intensity boost in _downsample,\n        // since when scaling to the smallest pixel sizes there's more black space\n        // which causes characters to be much less distinct.\n        for (let y = 0; y < height; y++) {\n            // 1. For this destination pixel, get the source pixels we're sampling\n            // from (x1, y1) to the next pixel (x2, y2)\n            const sourceY1 = (y / height) * 16 /* Constants.SAMPLED_CHAR_HEIGHT */;\n            const sourceY2 = ((y + 1) / height) * 16 /* Constants.SAMPLED_CHAR_HEIGHT */;\n            for (let x = 0; x < width; x++) {\n                const sourceX1 = (x / width) * 10 /* Constants.SAMPLED_CHAR_WIDTH */;\n                const sourceX2 = ((x + 1) / width) * 10 /* Constants.SAMPLED_CHAR_WIDTH */;\n                // 2. Sample all of them, summing them up and weighting them. Similar\n                // to bilinear interpolation.\n                let value = 0;\n                let samples = 0;\n                for (let sy = sourceY1; sy < sourceY2; sy++) {\n                    const sourceRow = sourceOffset + Math.floor(sy) * 3840 /* Constants.RGBA_SAMPLED_ROW_WIDTH */;\n                    const yBalance = 1 - (sy - Math.floor(sy));\n                    for (let sx = sourceX1; sx < sourceX2; sx++) {\n                        const xBalance = 1 - (sx - Math.floor(sx));\n                        const sourceIndex = sourceRow + Math.floor(sx) * 4 /* Constants.RGBA_CHANNELS_CNT */;\n                        const weight = xBalance * yBalance;\n                        samples += weight;\n                        value += ((source[sourceIndex] * source[sourceIndex + 3]) / 255) * weight;\n                    }\n                }\n                const final = value / samples;\n                brightest = Math.max(brightest, final);\n                dest[targetIndex++] = toUint8(final);\n            }\n        }\n        return brightest;\n    }\n    static _downsample(data, scale) {\n        const pixelsPerCharacter = 2 /* Constants.BASE_CHAR_HEIGHT */ * scale * 1 /* Constants.BASE_CHAR_WIDTH */ * scale;\n        const resultLen = pixelsPerCharacter * 96 /* Constants.CHAR_COUNT */;\n        const result = new Uint8ClampedArray(resultLen);\n        let resultOffset = 0;\n        let sourceOffset = 0;\n        let brightest = 0;\n        for (let charIndex = 0; charIndex < 96 /* Constants.CHAR_COUNT */; charIndex++) {\n            brightest = Math.max(brightest, this._downsampleChar(data, sourceOffset, result, resultOffset, scale));\n            resultOffset += pixelsPerCharacter;\n            sourceOffset += 10 /* Constants.SAMPLED_CHAR_WIDTH */ * 4 /* Constants.RGBA_CHANNELS_CNT */;\n        }\n        if (brightest > 0) {\n            const adjust = 255 / brightest;\n            for (let i = 0; i < resultLen; i++) {\n                result[i] *= adjust;\n            }\n        }\n        return result;\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,mBAAmB,QAAQ,0BAA0B;AAC9D,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,OAAO,QAAQ,iCAAiC;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,0BAA0B,CAAC;EACpC;AACJ;AACA;EACI,OAAOC,MAAMA,CAACC,KAAK,EAAEC,UAAU,EAAE;IAC7B;IACA;IACA,IAAI,IAAI,CAACC,WAAW,IAAIF,KAAK,KAAK,IAAI,CAACE,WAAW,CAACF,KAAK,IAAIC,UAAU,KAAK,IAAI,CAACE,cAAc,EAAE;MAC5F,OAAO,IAAI,CAACD,WAAW;IAC3B;IACA,IAAIE,OAAO;IACX,IAAIR,gBAAgB,CAACI,KAAK,CAAC,EAAE;MACzBI,OAAO,GAAG,IAAIV,mBAAmB,CAACE,gBAAgB,CAACI,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC;IACvE,CAAC,MACI;MACDI,OAAO,GAAGN,0BAA0B,CAACO,oBAAoB,CAACP,0BAA0B,CAACQ,gBAAgB,CAACL,UAAU,CAAC,CAACM,IAAI,EAAEP,KAAK,CAAC;IAClI;IACA,IAAI,CAACG,cAAc,GAAGF,UAAU;IAChC,IAAI,CAACC,WAAW,GAAGE,OAAO;IAC1B,OAAOA,OAAO;EAClB;EACA;AACJ;AACA;EACI,OAAOE,gBAAgBA,CAACL,UAAU,EAAE;IAChC,MAAMO,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IAC/C,MAAMC,GAAG,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;IACnCJ,MAAM,CAACK,KAAK,CAACC,MAAM,GAAG,GAAG,EAAE,CAAC,uCAAuC;IACnEN,MAAM,CAACM,MAAM,GAAG,EAAE,CAAC;IACnBN,MAAM,CAACO,KAAK,GAAG,EAAE,CAAC,6BAA6B,EAAE,CAAC;IAClDP,MAAM,CAACK,KAAK,CAACE,KAAK,GAAG,EAAE,CAAC,6BAA6B,EAAE,CAAC,qCAAqC,IAAI;IACjGJ,GAAG,CAACK,SAAS,GAAG,SAAS;IACzBL,GAAG,CAACM,IAAI,GAAG,QAAQ,EAAE,CAAC,yCAAyChB,UAAU,EAAE;IAC3EU,GAAG,CAACO,YAAY,GAAG,QAAQ;IAC3B,IAAIC,CAAC,GAAG,CAAC;IACT,KAAK,MAAMC,IAAI,IAAIzB,YAAY,EAAE;MAC7BgB,GAAG,CAACU,QAAQ,CAACC,MAAM,CAACC,YAAY,CAACH,IAAI,CAAC,EAAED,CAAC,EAAE,EAAE,CAAC,sCAAsC,CAAC,CAAC;MACtFA,CAAC,IAAI,EAAE,CAAC;IACZ;IACA,OAAOR,GAAG,CAACa,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,6BAA6B,EAAE,CAAC,oCAAoC,EAAE,CAAC,mCAAmC,CAAC;EAChJ;EACA;AACJ;AACA;EACI,OAAOnB,oBAAoBA,CAACoB,MAAM,EAAEzB,KAAK,EAAE;IACvC,MAAM0B,cAAc,GAAG,EAAE,CAAC,sCAAsC,EAAE,CAAC,qCAAqC,CAAC,CAAC,oCAAoC,EAAE,CAAC;IACjJ,IAAID,MAAM,CAACE,MAAM,KAAKD,cAAc,EAAE;MAClC,MAAM,IAAIE,KAAK,CAAC,0CAA0C,CAAC;IAC/D;IACA,MAAMC,QAAQ,GAAG/B,0BAA0B,CAACgC,WAAW,CAACL,MAAM,EAAEzB,KAAK,CAAC;IACtE,OAAO,IAAIN,mBAAmB,CAACmC,QAAQ,EAAE7B,KAAK,CAAC;EACnD;EACA,OAAO+B,eAAeA,CAACN,MAAM,EAAEO,YAAY,EAAEC,IAAI,EAAEC,UAAU,EAAElC,KAAK,EAAE;IAClE,MAAMe,KAAK,GAAG,CAAC,CAAC,kCAAkCf,KAAK;IACvD,MAAMc,MAAM,GAAG,CAAC,CAAC,mCAAmCd,KAAK;IACzD,IAAImC,WAAW,GAAGD,UAAU;IAC5B,IAAIE,SAAS,GAAG,CAAC;IACjB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvB,MAAM,EAAEuB,CAAC,EAAE,EAAE;MAC7B;MACA;MACA,MAAMC,QAAQ,GAAID,CAAC,GAAGvB,MAAM,GAAI,EAAE,CAAC;MACnC,MAAMyB,QAAQ,GAAI,CAACF,CAAC,GAAG,CAAC,IAAIvB,MAAM,GAAI,EAAE,CAAC;MACzC,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,KAAK,EAAEI,CAAC,EAAE,EAAE;QAC5B,MAAMqB,QAAQ,GAAIrB,CAAC,GAAGJ,KAAK,GAAI,EAAE,CAAC;QAClC,MAAM0B,QAAQ,GAAI,CAACtB,CAAC,GAAG,CAAC,IAAIJ,KAAK,GAAI,EAAE,CAAC;QACxC;QACA;QACA,IAAI2B,KAAK,GAAG,CAAC;QACb,IAAIC,OAAO,GAAG,CAAC;QACf,KAAK,IAAIC,EAAE,GAAGN,QAAQ,EAAEM,EAAE,GAAGL,QAAQ,EAAEK,EAAE,EAAE,EAAE;UACzC,MAAMC,SAAS,GAAGb,YAAY,GAAGc,IAAI,CAACC,KAAK,CAACH,EAAE,CAAC,GAAG,IAAI,CAAC;UACvD,MAAMI,QAAQ,GAAG,CAAC,IAAIJ,EAAE,GAAGE,IAAI,CAACC,KAAK,CAACH,EAAE,CAAC,CAAC;UAC1C,KAAK,IAAIK,EAAE,GAAGT,QAAQ,EAAES,EAAE,GAAGR,QAAQ,EAAEQ,EAAE,EAAE,EAAE;YACzC,MAAMC,QAAQ,GAAG,CAAC,IAAID,EAAE,GAAGH,IAAI,CAACC,KAAK,CAACE,EAAE,CAAC,CAAC;YAC1C,MAAME,WAAW,GAAGN,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACE,EAAE,CAAC,GAAG,CAAC,CAAC;YACnD,MAAMG,MAAM,GAAGF,QAAQ,GAAGF,QAAQ;YAClCL,OAAO,IAAIS,MAAM;YACjBV,KAAK,IAAMjB,MAAM,CAAC0B,WAAW,CAAC,GAAG1B,MAAM,CAAC0B,WAAW,GAAG,CAAC,CAAC,GAAI,GAAG,GAAIC,MAAM;UAC7E;QACJ;QACA,MAAMC,KAAK,GAAGX,KAAK,GAAGC,OAAO;QAC7BP,SAAS,GAAGU,IAAI,CAACQ,GAAG,CAAClB,SAAS,EAAEiB,KAAK,CAAC;QACtCpB,IAAI,CAACE,WAAW,EAAE,CAAC,GAAGtC,OAAO,CAACwD,KAAK,CAAC;MACxC;IACJ;IACA,OAAOjB,SAAS;EACpB;EACA,OAAON,WAAWA,CAACvB,IAAI,EAAEP,KAAK,EAAE;IAC5B,MAAMuD,kBAAkB,GAAG,CAAC,CAAC,mCAAmCvD,KAAK,GAAG,CAAC,CAAC,kCAAkCA,KAAK;IACjH,MAAMwD,SAAS,GAAGD,kBAAkB,GAAG,EAAE,CAAC;IAC1C,MAAME,MAAM,GAAG,IAAIC,iBAAiB,CAACF,SAAS,CAAC;IAC/C,IAAIG,YAAY,GAAG,CAAC;IACpB,IAAI3B,YAAY,GAAG,CAAC;IACpB,IAAII,SAAS,GAAG,CAAC;IACjB,KAAK,IAAIwB,SAAS,GAAG,CAAC,EAAEA,SAAS,GAAG,EAAE,CAAC,4BAA4BA,SAAS,EAAE,EAAE;MAC5ExB,SAAS,GAAGU,IAAI,CAACQ,GAAG,CAAClB,SAAS,EAAE,IAAI,CAACL,eAAe,CAACxB,IAAI,EAAEyB,YAAY,EAAEyB,MAAM,EAAEE,YAAY,EAAE3D,KAAK,CAAC,CAAC;MACtG2D,YAAY,IAAIJ,kBAAkB;MAClCvB,YAAY,IAAI,EAAE,CAAC,qCAAqC,CAAC,CAAC;IAC9D;IACA,IAAII,SAAS,GAAG,CAAC,EAAE;MACf,MAAMyB,MAAM,GAAG,GAAG,GAAGzB,SAAS;MAC9B,KAAK,IAAI0B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,SAAS,EAAEM,CAAC,EAAE,EAAE;QAChCL,MAAM,CAACK,CAAC,CAAC,IAAID,MAAM;MACvB;IACJ;IACA,OAAOJ,MAAM;EACjB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}