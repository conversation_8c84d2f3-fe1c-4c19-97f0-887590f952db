import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output, ViewChild, ElementRef } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { IconComponent } from '@ava/play-comp-library';
import { AgentData, AgentInput } from '../workflow-playground/workflow-playground.component';
import { trigger, state, style, transition, animate } from '@angular/animations';

@Component({
  selector: 'app-agent-stepper-card',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IconComponent
  ],
  templateUrl: './agent-stepper-card.component.html',
  styleUrls: ['./agent-stepper-card.component.scss'],
  animations: [
    trigger('slideDown', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateY(-10px)' }),
        animate('300ms ease-out', style({ opacity: 1, transform: 'translateY(0)' }))
      ])
    ])
  ]
})
export class AgentStepperCardComponent {
  @Input() agent!: AgentData;
  @Input() stepNumber: number = 1;
  @Input() isFirst: boolean = false;
  @Input() isLast: boolean = false;
  @Input() isActive: boolean = false;
  @Input() isCompleted: boolean = false;
  
  @Output() inputChanged = new EventEmitter<{inputIndex: number, value: string}>();
  @Output() fileSelected = new EventEmitter<{inputIndex: number, files: File[]}>();
  @Output() messageSent = new EventEmitter<{inputIndex: number, value: string, files?: File[]}>();
  @Output() stepCompleted = new EventEmitter<void>();

  @ViewChild('fileInput') fileInput!: ElementRef;

  isExpanded: boolean = false;
  showAllInputs: boolean = false;
  maxVisibleInputs: number = 2;

  get visibleInputs(): AgentInput[] {
    if (!this.agent.inputs) return [];
    if (this.showAllInputs || this.agent.inputs.length <= this.maxVisibleInputs) {
      return this.agent.inputs;
    }
    return this.agent.inputs.slice(0, this.maxVisibleInputs);
  }

  get hiddenInputsCount(): number {
    if (!this.agent.inputs) return 0;
    return Math.max(0, this.agent.inputs.length - this.maxVisibleInputs);
  }

  get stepperClass(): string {
    const classes = ['stepper-circle'];
    if (this.isCompleted) classes.push('completed');
    else if (this.isActive) classes.push('active');
    else classes.push('inactive');
    return classes.join(' ');
  }

  toggleExpanded(): void {
    if (this.agent.hasInputs) {
      this.isExpanded = !this.isExpanded;
    }
  }

  toggleShowAllInputs(): void {
    this.showAllInputs = !this.showAllInputs;
  }

  onInputChange(inputIndex: number, event: Event): void {
    const target = event.target as HTMLTextAreaElement;
    this.inputChanged.emit({ inputIndex, value: target.value });
  }

  onFileInputClick(inputIndex: number): void {
    // Store the input index for file selection
    this.fileInput.nativeElement.dataset['inputIndex'] = inputIndex.toString();
    this.fileInput.nativeElement.click();
  }

  onFileSelected(event: Event): void {
    const target = event.target as HTMLInputElement;
    const files = target.files;
    const inputIndex = parseInt(target.dataset['inputIndex'] || '0');
    
    if (files && files.length > 0) {
      const fileArray = Array.from(files);
      this.fileSelected.emit({ inputIndex, files: fileArray });
    }
  }

  removeFile(inputIndex: number, fileIndex: number): void {
    if (this.agent.inputs && this.agent.inputs[inputIndex] && this.agent.inputs[inputIndex].files) {
      this.agent.inputs[inputIndex].files!.splice(fileIndex, 1);
      this.fileSelected.emit({ inputIndex, files: this.agent.inputs[inputIndex].files! });
    }
  }

  handleSendMessage(inputIndex: number): void {
    if (!this.agent.inputs || !this.agent.inputs[inputIndex]) return;

    const input = this.agent.inputs[inputIndex];
    const hasValue = input.value && input.value.trim().length > 0;
    const hasFiles = input.files && input.files.length > 0;

    if (!hasValue && !hasFiles) return;

    // Create payload with input value
    const payload = {
      inputIndex,
      value: input.value || '',
      files: input.files
    };

    // Emit the message sent event
    this.messageSent.emit(payload);

    // Emit completion event (parent will handle marking as completed)
    this.stepCompleted.emit();

    // Don't clear the input after sending - keep the value visible
    // input.value = '';
    // if (input.files) {
    //   input.files = [];
    // }
  }

  getAcceptedFileType(input: AgentInput): string {
    return input.inputType === 'image' 
      ? '.png,.jpg,.jpeg,.gif,.bmp,.svg'
      : '.pdf,.doc,.docx,.txt,.csv,.xlsx,.xls,.ppt,.pptx,.png,.jpg,.jpeg,.gif,.bmp,.svg';
  }

  isInputDisabled(input: AgentInput): boolean {
    return input.inputType === 'image';
  }

  showFileUploadButton(input: AgentInput): boolean {
    return input.inputType === 'image' || input.inputType === 'text';
  }

  trackByIndex(index: number, item: any): number {
    return index;
  }
}
