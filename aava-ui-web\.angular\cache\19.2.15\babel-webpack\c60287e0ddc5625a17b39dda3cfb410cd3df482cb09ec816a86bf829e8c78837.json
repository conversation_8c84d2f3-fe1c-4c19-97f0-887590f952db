{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { LcsDiff } from '../../../base/common/diff/diff.js';\nimport { LinesDiff } from './linesDiffComputer.js';\nimport { RangeMapping, DetailedLineRangeMapping } from './rangeMapping.js';\nimport * as strings from '../../../base/common/strings.js';\nimport { Range } from '../core/range.js';\nimport { assertFn, checkAdjacentItems } from '../../../base/common/assert.js';\nimport { LineRange } from '../core/lineRange.js';\nconst MINIMUM_MATCHING_CHARACTER_LENGTH = 3;\nexport class LegacyLinesDiffComputer {\n  computeDiff(originalLines, modifiedLines, options) {\n    const diffComputer = new DiffComputer(originalLines, modifiedLines, {\n      maxComputationTime: options.maxComputationTimeMs,\n      shouldIgnoreTrimWhitespace: options.ignoreTrimWhitespace,\n      shouldComputeCharChanges: true,\n      shouldMakePrettyDiff: true,\n      shouldPostProcessCharChanges: true\n    });\n    const result = diffComputer.computeDiff();\n    const changes = [];\n    let lastChange = null;\n    for (const c of result.changes) {\n      let originalRange;\n      if (c.originalEndLineNumber === 0) {\n        // Insertion\n        originalRange = new LineRange(c.originalStartLineNumber + 1, c.originalStartLineNumber + 1);\n      } else {\n        originalRange = new LineRange(c.originalStartLineNumber, c.originalEndLineNumber + 1);\n      }\n      let modifiedRange;\n      if (c.modifiedEndLineNumber === 0) {\n        // Deletion\n        modifiedRange = new LineRange(c.modifiedStartLineNumber + 1, c.modifiedStartLineNumber + 1);\n      } else {\n        modifiedRange = new LineRange(c.modifiedStartLineNumber, c.modifiedEndLineNumber + 1);\n      }\n      let change = new DetailedLineRangeMapping(originalRange, modifiedRange, c.charChanges?.map(c => new RangeMapping(new Range(c.originalStartLineNumber, c.originalStartColumn, c.originalEndLineNumber, c.originalEndColumn), new Range(c.modifiedStartLineNumber, c.modifiedStartColumn, c.modifiedEndLineNumber, c.modifiedEndColumn))));\n      if (lastChange) {\n        if (lastChange.modified.endLineNumberExclusive === change.modified.startLineNumber || lastChange.original.endLineNumberExclusive === change.original.startLineNumber) {\n          // join touching diffs. Probably moving diffs up/down in the algorithm causes touching diffs.\n          change = new DetailedLineRangeMapping(lastChange.original.join(change.original), lastChange.modified.join(change.modified), lastChange.innerChanges && change.innerChanges ? lastChange.innerChanges.concat(change.innerChanges) : undefined);\n          changes.pop();\n        }\n      }\n      changes.push(change);\n      lastChange = change;\n    }\n    assertFn(() => {\n      return checkAdjacentItems(changes, (m1, m2) => m2.original.startLineNumber - m1.original.endLineNumberExclusive === m2.modified.startLineNumber - m1.modified.endLineNumberExclusive &&\n      // There has to be an unchanged line in between (otherwise both diffs should have been joined)\n      m1.original.endLineNumberExclusive < m2.original.startLineNumber && m1.modified.endLineNumberExclusive < m2.modified.startLineNumber);\n    });\n    return new LinesDiff(changes, [], result.quitEarly);\n  }\n}\nfunction computeDiff(originalSequence, modifiedSequence, continueProcessingPredicate, pretty) {\n  const diffAlgo = new LcsDiff(originalSequence, modifiedSequence, continueProcessingPredicate);\n  return diffAlgo.ComputeDiff(pretty);\n}\nclass LineSequence {\n  constructor(lines) {\n    const startColumns = [];\n    const endColumns = [];\n    for (let i = 0, length = lines.length; i < length; i++) {\n      startColumns[i] = getFirstNonBlankColumn(lines[i], 1);\n      endColumns[i] = getLastNonBlankColumn(lines[i], 1);\n    }\n    this.lines = lines;\n    this._startColumns = startColumns;\n    this._endColumns = endColumns;\n  }\n  getElements() {\n    const elements = [];\n    for (let i = 0, len = this.lines.length; i < len; i++) {\n      elements[i] = this.lines[i].substring(this._startColumns[i] - 1, this._endColumns[i] - 1);\n    }\n    return elements;\n  }\n  getStrictElement(index) {\n    return this.lines[index];\n  }\n  getStartLineNumber(i) {\n    return i + 1;\n  }\n  getEndLineNumber(i) {\n    return i + 1;\n  }\n  createCharSequence(shouldIgnoreTrimWhitespace, startIndex, endIndex) {\n    const charCodes = [];\n    const lineNumbers = [];\n    const columns = [];\n    let len = 0;\n    for (let index = startIndex; index <= endIndex; index++) {\n      const lineContent = this.lines[index];\n      const startColumn = shouldIgnoreTrimWhitespace ? this._startColumns[index] : 1;\n      const endColumn = shouldIgnoreTrimWhitespace ? this._endColumns[index] : lineContent.length + 1;\n      for (let col = startColumn; col < endColumn; col++) {\n        charCodes[len] = lineContent.charCodeAt(col - 1);\n        lineNumbers[len] = index + 1;\n        columns[len] = col;\n        len++;\n      }\n      if (!shouldIgnoreTrimWhitespace && index < endIndex) {\n        // Add \\n if trim whitespace is not ignored\n        charCodes[len] = 10 /* CharCode.LineFeed */;\n        lineNumbers[len] = index + 1;\n        columns[len] = lineContent.length + 1;\n        len++;\n      }\n    }\n    return new CharSequence(charCodes, lineNumbers, columns);\n  }\n}\nclass CharSequence {\n  constructor(charCodes, lineNumbers, columns) {\n    this._charCodes = charCodes;\n    this._lineNumbers = lineNumbers;\n    this._columns = columns;\n  }\n  toString() {\n    return '[' + this._charCodes.map((s, idx) => (s === 10 /* CharCode.LineFeed */ ? '\\\\n' : String.fromCharCode(s)) + `-(${this._lineNumbers[idx]},${this._columns[idx]})`).join(', ') + ']';\n  }\n  _assertIndex(index, arr) {\n    if (index < 0 || index >= arr.length) {\n      throw new Error(`Illegal index`);\n    }\n  }\n  getElements() {\n    return this._charCodes;\n  }\n  getStartLineNumber(i) {\n    if (i > 0 && i === this._lineNumbers.length) {\n      // the start line number of the element after the last element\n      // is the end line number of the last element\n      return this.getEndLineNumber(i - 1);\n    }\n    this._assertIndex(i, this._lineNumbers);\n    return this._lineNumbers[i];\n  }\n  getEndLineNumber(i) {\n    if (i === -1) {\n      // the end line number of the element before the first element\n      // is the start line number of the first element\n      return this.getStartLineNumber(i + 1);\n    }\n    this._assertIndex(i, this._lineNumbers);\n    if (this._charCodes[i] === 10 /* CharCode.LineFeed */) {\n      return this._lineNumbers[i] + 1;\n    }\n    return this._lineNumbers[i];\n  }\n  getStartColumn(i) {\n    if (i > 0 && i === this._columns.length) {\n      // the start column of the element after the last element\n      // is the end column of the last element\n      return this.getEndColumn(i - 1);\n    }\n    this._assertIndex(i, this._columns);\n    return this._columns[i];\n  }\n  getEndColumn(i) {\n    if (i === -1) {\n      // the end column of the element before the first element\n      // is the start column of the first element\n      return this.getStartColumn(i + 1);\n    }\n    this._assertIndex(i, this._columns);\n    if (this._charCodes[i] === 10 /* CharCode.LineFeed */) {\n      return 1;\n    }\n    return this._columns[i] + 1;\n  }\n}\nclass CharChange {\n  constructor(originalStartLineNumber, originalStartColumn, originalEndLineNumber, originalEndColumn, modifiedStartLineNumber, modifiedStartColumn, modifiedEndLineNumber, modifiedEndColumn) {\n    this.originalStartLineNumber = originalStartLineNumber;\n    this.originalStartColumn = originalStartColumn;\n    this.originalEndLineNumber = originalEndLineNumber;\n    this.originalEndColumn = originalEndColumn;\n    this.modifiedStartLineNumber = modifiedStartLineNumber;\n    this.modifiedStartColumn = modifiedStartColumn;\n    this.modifiedEndLineNumber = modifiedEndLineNumber;\n    this.modifiedEndColumn = modifiedEndColumn;\n  }\n  static createFromDiffChange(diffChange, originalCharSequence, modifiedCharSequence) {\n    const originalStartLineNumber = originalCharSequence.getStartLineNumber(diffChange.originalStart);\n    const originalStartColumn = originalCharSequence.getStartColumn(diffChange.originalStart);\n    const originalEndLineNumber = originalCharSequence.getEndLineNumber(diffChange.originalStart + diffChange.originalLength - 1);\n    const originalEndColumn = originalCharSequence.getEndColumn(diffChange.originalStart + diffChange.originalLength - 1);\n    const modifiedStartLineNumber = modifiedCharSequence.getStartLineNumber(diffChange.modifiedStart);\n    const modifiedStartColumn = modifiedCharSequence.getStartColumn(diffChange.modifiedStart);\n    const modifiedEndLineNumber = modifiedCharSequence.getEndLineNumber(diffChange.modifiedStart + diffChange.modifiedLength - 1);\n    const modifiedEndColumn = modifiedCharSequence.getEndColumn(diffChange.modifiedStart + diffChange.modifiedLength - 1);\n    return new CharChange(originalStartLineNumber, originalStartColumn, originalEndLineNumber, originalEndColumn, modifiedStartLineNumber, modifiedStartColumn, modifiedEndLineNumber, modifiedEndColumn);\n  }\n}\nfunction postProcessCharChanges(rawChanges) {\n  if (rawChanges.length <= 1) {\n    return rawChanges;\n  }\n  const result = [rawChanges[0]];\n  let prevChange = result[0];\n  for (let i = 1, len = rawChanges.length; i < len; i++) {\n    const currChange = rawChanges[i];\n    const originalMatchingLength = currChange.originalStart - (prevChange.originalStart + prevChange.originalLength);\n    const modifiedMatchingLength = currChange.modifiedStart - (prevChange.modifiedStart + prevChange.modifiedLength);\n    // Both of the above should be equal, but the continueProcessingPredicate may prevent this from being true\n    const matchingLength = Math.min(originalMatchingLength, modifiedMatchingLength);\n    if (matchingLength < MINIMUM_MATCHING_CHARACTER_LENGTH) {\n      // Merge the current change into the previous one\n      prevChange.originalLength = currChange.originalStart + currChange.originalLength - prevChange.originalStart;\n      prevChange.modifiedLength = currChange.modifiedStart + currChange.modifiedLength - prevChange.modifiedStart;\n    } else {\n      // Add the current change\n      result.push(currChange);\n      prevChange = currChange;\n    }\n  }\n  return result;\n}\nclass LineChange {\n  constructor(originalStartLineNumber, originalEndLineNumber, modifiedStartLineNumber, modifiedEndLineNumber, charChanges) {\n    this.originalStartLineNumber = originalStartLineNumber;\n    this.originalEndLineNumber = originalEndLineNumber;\n    this.modifiedStartLineNumber = modifiedStartLineNumber;\n    this.modifiedEndLineNumber = modifiedEndLineNumber;\n    this.charChanges = charChanges;\n  }\n  static createFromDiffResult(shouldIgnoreTrimWhitespace, diffChange, originalLineSequence, modifiedLineSequence, continueCharDiff, shouldComputeCharChanges, shouldPostProcessCharChanges) {\n    let originalStartLineNumber;\n    let originalEndLineNumber;\n    let modifiedStartLineNumber;\n    let modifiedEndLineNumber;\n    let charChanges = undefined;\n    if (diffChange.originalLength === 0) {\n      originalStartLineNumber = originalLineSequence.getStartLineNumber(diffChange.originalStart) - 1;\n      originalEndLineNumber = 0;\n    } else {\n      originalStartLineNumber = originalLineSequence.getStartLineNumber(diffChange.originalStart);\n      originalEndLineNumber = originalLineSequence.getEndLineNumber(diffChange.originalStart + diffChange.originalLength - 1);\n    }\n    if (diffChange.modifiedLength === 0) {\n      modifiedStartLineNumber = modifiedLineSequence.getStartLineNumber(diffChange.modifiedStart) - 1;\n      modifiedEndLineNumber = 0;\n    } else {\n      modifiedStartLineNumber = modifiedLineSequence.getStartLineNumber(diffChange.modifiedStart);\n      modifiedEndLineNumber = modifiedLineSequence.getEndLineNumber(diffChange.modifiedStart + diffChange.modifiedLength - 1);\n    }\n    if (shouldComputeCharChanges && diffChange.originalLength > 0 && diffChange.originalLength < 20 && diffChange.modifiedLength > 0 && diffChange.modifiedLength < 20 && continueCharDiff()) {\n      // Compute character changes for diff chunks of at most 20 lines...\n      const originalCharSequence = originalLineSequence.createCharSequence(shouldIgnoreTrimWhitespace, diffChange.originalStart, diffChange.originalStart + diffChange.originalLength - 1);\n      const modifiedCharSequence = modifiedLineSequence.createCharSequence(shouldIgnoreTrimWhitespace, diffChange.modifiedStart, diffChange.modifiedStart + diffChange.modifiedLength - 1);\n      if (originalCharSequence.getElements().length > 0 && modifiedCharSequence.getElements().length > 0) {\n        let rawChanges = computeDiff(originalCharSequence, modifiedCharSequence, continueCharDiff, true).changes;\n        if (shouldPostProcessCharChanges) {\n          rawChanges = postProcessCharChanges(rawChanges);\n        }\n        charChanges = [];\n        for (let i = 0, length = rawChanges.length; i < length; i++) {\n          charChanges.push(CharChange.createFromDiffChange(rawChanges[i], originalCharSequence, modifiedCharSequence));\n        }\n      }\n    }\n    return new LineChange(originalStartLineNumber, originalEndLineNumber, modifiedStartLineNumber, modifiedEndLineNumber, charChanges);\n  }\n}\nexport class DiffComputer {\n  constructor(originalLines, modifiedLines, opts) {\n    this.shouldComputeCharChanges = opts.shouldComputeCharChanges;\n    this.shouldPostProcessCharChanges = opts.shouldPostProcessCharChanges;\n    this.shouldIgnoreTrimWhitespace = opts.shouldIgnoreTrimWhitespace;\n    this.shouldMakePrettyDiff = opts.shouldMakePrettyDiff;\n    this.originalLines = originalLines;\n    this.modifiedLines = modifiedLines;\n    this.original = new LineSequence(originalLines);\n    this.modified = new LineSequence(modifiedLines);\n    this.continueLineDiff = createContinueProcessingPredicate(opts.maxComputationTime);\n    this.continueCharDiff = createContinueProcessingPredicate(opts.maxComputationTime === 0 ? 0 : Math.min(opts.maxComputationTime, 5000)); // never run after 5s for character changes...\n  }\n  computeDiff() {\n    if (this.original.lines.length === 1 && this.original.lines[0].length === 0) {\n      // empty original => fast path\n      if (this.modified.lines.length === 1 && this.modified.lines[0].length === 0) {\n        return {\n          quitEarly: false,\n          changes: []\n        };\n      }\n      return {\n        quitEarly: false,\n        changes: [{\n          originalStartLineNumber: 1,\n          originalEndLineNumber: 1,\n          modifiedStartLineNumber: 1,\n          modifiedEndLineNumber: this.modified.lines.length,\n          charChanges: undefined\n        }]\n      };\n    }\n    if (this.modified.lines.length === 1 && this.modified.lines[0].length === 0) {\n      // empty modified => fast path\n      return {\n        quitEarly: false,\n        changes: [{\n          originalStartLineNumber: 1,\n          originalEndLineNumber: this.original.lines.length,\n          modifiedStartLineNumber: 1,\n          modifiedEndLineNumber: 1,\n          charChanges: undefined\n        }]\n      };\n    }\n    const diffResult = computeDiff(this.original, this.modified, this.continueLineDiff, this.shouldMakePrettyDiff);\n    const rawChanges = diffResult.changes;\n    const quitEarly = diffResult.quitEarly;\n    // The diff is always computed with ignoring trim whitespace\n    // This ensures we get the prettiest diff\n    if (this.shouldIgnoreTrimWhitespace) {\n      const lineChanges = [];\n      for (let i = 0, length = rawChanges.length; i < length; i++) {\n        lineChanges.push(LineChange.createFromDiffResult(this.shouldIgnoreTrimWhitespace, rawChanges[i], this.original, this.modified, this.continueCharDiff, this.shouldComputeCharChanges, this.shouldPostProcessCharChanges));\n      }\n      return {\n        quitEarly: quitEarly,\n        changes: lineChanges\n      };\n    }\n    // Need to post-process and introduce changes where the trim whitespace is different\n    // Note that we are looping starting at -1 to also cover the lines before the first change\n    const result = [];\n    let originalLineIndex = 0;\n    let modifiedLineIndex = 0;\n    for (let i = -1 /* !!!! */, len = rawChanges.length; i < len; i++) {\n      const nextChange = i + 1 < len ? rawChanges[i + 1] : null;\n      const originalStop = nextChange ? nextChange.originalStart : this.originalLines.length;\n      const modifiedStop = nextChange ? nextChange.modifiedStart : this.modifiedLines.length;\n      while (originalLineIndex < originalStop && modifiedLineIndex < modifiedStop) {\n        const originalLine = this.originalLines[originalLineIndex];\n        const modifiedLine = this.modifiedLines[modifiedLineIndex];\n        if (originalLine !== modifiedLine) {\n          // These lines differ only in trim whitespace\n          // Check the leading whitespace\n          {\n            let originalStartColumn = getFirstNonBlankColumn(originalLine, 1);\n            let modifiedStartColumn = getFirstNonBlankColumn(modifiedLine, 1);\n            while (originalStartColumn > 1 && modifiedStartColumn > 1) {\n              const originalChar = originalLine.charCodeAt(originalStartColumn - 2);\n              const modifiedChar = modifiedLine.charCodeAt(modifiedStartColumn - 2);\n              if (originalChar !== modifiedChar) {\n                break;\n              }\n              originalStartColumn--;\n              modifiedStartColumn--;\n            }\n            if (originalStartColumn > 1 || modifiedStartColumn > 1) {\n              this._pushTrimWhitespaceCharChange(result, originalLineIndex + 1, 1, originalStartColumn, modifiedLineIndex + 1, 1, modifiedStartColumn);\n            }\n          }\n          // Check the trailing whitespace\n          {\n            let originalEndColumn = getLastNonBlankColumn(originalLine, 1);\n            let modifiedEndColumn = getLastNonBlankColumn(modifiedLine, 1);\n            const originalMaxColumn = originalLine.length + 1;\n            const modifiedMaxColumn = modifiedLine.length + 1;\n            while (originalEndColumn < originalMaxColumn && modifiedEndColumn < modifiedMaxColumn) {\n              const originalChar = originalLine.charCodeAt(originalEndColumn - 1);\n              const modifiedChar = originalLine.charCodeAt(modifiedEndColumn - 1);\n              if (originalChar !== modifiedChar) {\n                break;\n              }\n              originalEndColumn++;\n              modifiedEndColumn++;\n            }\n            if (originalEndColumn < originalMaxColumn || modifiedEndColumn < modifiedMaxColumn) {\n              this._pushTrimWhitespaceCharChange(result, originalLineIndex + 1, originalEndColumn, originalMaxColumn, modifiedLineIndex + 1, modifiedEndColumn, modifiedMaxColumn);\n            }\n          }\n        }\n        originalLineIndex++;\n        modifiedLineIndex++;\n      }\n      if (nextChange) {\n        // Emit the actual change\n        result.push(LineChange.createFromDiffResult(this.shouldIgnoreTrimWhitespace, nextChange, this.original, this.modified, this.continueCharDiff, this.shouldComputeCharChanges, this.shouldPostProcessCharChanges));\n        originalLineIndex += nextChange.originalLength;\n        modifiedLineIndex += nextChange.modifiedLength;\n      }\n    }\n    return {\n      quitEarly: quitEarly,\n      changes: result\n    };\n  }\n  _pushTrimWhitespaceCharChange(result, originalLineNumber, originalStartColumn, originalEndColumn, modifiedLineNumber, modifiedStartColumn, modifiedEndColumn) {\n    if (this._mergeTrimWhitespaceCharChange(result, originalLineNumber, originalStartColumn, originalEndColumn, modifiedLineNumber, modifiedStartColumn, modifiedEndColumn)) {\n      // Merged into previous\n      return;\n    }\n    let charChanges = undefined;\n    if (this.shouldComputeCharChanges) {\n      charChanges = [new CharChange(originalLineNumber, originalStartColumn, originalLineNumber, originalEndColumn, modifiedLineNumber, modifiedStartColumn, modifiedLineNumber, modifiedEndColumn)];\n    }\n    result.push(new LineChange(originalLineNumber, originalLineNumber, modifiedLineNumber, modifiedLineNumber, charChanges));\n  }\n  _mergeTrimWhitespaceCharChange(result, originalLineNumber, originalStartColumn, originalEndColumn, modifiedLineNumber, modifiedStartColumn, modifiedEndColumn) {\n    const len = result.length;\n    if (len === 0) {\n      return false;\n    }\n    const prevChange = result[len - 1];\n    if (prevChange.originalEndLineNumber === 0 || prevChange.modifiedEndLineNumber === 0) {\n      // Don't merge with inserts/deletes\n      return false;\n    }\n    if (prevChange.originalEndLineNumber === originalLineNumber && prevChange.modifiedEndLineNumber === modifiedLineNumber) {\n      if (this.shouldComputeCharChanges && prevChange.charChanges) {\n        prevChange.charChanges.push(new CharChange(originalLineNumber, originalStartColumn, originalLineNumber, originalEndColumn, modifiedLineNumber, modifiedStartColumn, modifiedLineNumber, modifiedEndColumn));\n      }\n      return true;\n    }\n    if (prevChange.originalEndLineNumber + 1 === originalLineNumber && prevChange.modifiedEndLineNumber + 1 === modifiedLineNumber) {\n      prevChange.originalEndLineNumber = originalLineNumber;\n      prevChange.modifiedEndLineNumber = modifiedLineNumber;\n      if (this.shouldComputeCharChanges && prevChange.charChanges) {\n        prevChange.charChanges.push(new CharChange(originalLineNumber, originalStartColumn, originalLineNumber, originalEndColumn, modifiedLineNumber, modifiedStartColumn, modifiedLineNumber, modifiedEndColumn));\n      }\n      return true;\n    }\n    return false;\n  }\n}\nfunction getFirstNonBlankColumn(txt, defaultValue) {\n  const r = strings.firstNonWhitespaceIndex(txt);\n  if (r === -1) {\n    return defaultValue;\n  }\n  return r + 1;\n}\nfunction getLastNonBlankColumn(txt, defaultValue) {\n  const r = strings.lastNonWhitespaceIndex(txt);\n  if (r === -1) {\n    return defaultValue;\n  }\n  return r + 2;\n}\nfunction createContinueProcessingPredicate(maximumRuntime) {\n  if (maximumRuntime === 0) {\n    return () => true;\n  }\n  const startTime = Date.now();\n  return () => {\n    return Date.now() - startTime < maximumRuntime;\n  };\n}", "map": {"version": 3, "names": ["LcsDiff", "LinesDiff", "RangeMapping", "DetailedLineRangeMapping", "strings", "Range", "assertFn", "checkAdjacentItems", "LineRange", "MINIMUM_MATCHING_CHARACTER_LENGTH", "LegacyLinesDiffComputer", "computeDiff", "originalLines", "modifiedLines", "options", "diffComputer", "DiffComputer", "maxComputationTime", "maxComputationTimeMs", "shouldIgnoreTrimWhitespace", "ignoreTrimWhitespace", "shouldComputeCharChanges", "shouldMakePrettyDiff", "shouldPostProcessCharChanges", "result", "changes", "lastChange", "c", "originalRange", "originalEndLineNumber", "originalStartLineNumber", "modifiedRange", "modifiedEndLineNumber", "modifiedStartLineNumber", "change", "char<PERSON><PERSON><PERSON>", "map", "originalStartColumn", "originalEndColumn", "modifiedStartColumn", "modifiedEndColumn", "modified", "endLineNumberExclusive", "startLineNumber", "original", "join", "innerChanges", "concat", "undefined", "pop", "push", "m1", "m2", "quit<PERSON>arly", "originalSequence", "modifiedSequence", "continueProcessingPredicate", "pretty", "diffAlgo", "ComputeDiff", "LineSequence", "constructor", "lines", "startColumns", "endColumns", "i", "length", "getFirstNonBlankColumn", "getLastNonBlankColumn", "_startColumns", "_endColumns", "getElements", "elements", "len", "substring", "getStrictElement", "index", "getStartLineNumber", "getEndLineNumber", "createCharSequence", "startIndex", "endIndex", "charCodes", "lineNumbers", "columns", "lineContent", "startColumn", "endColumn", "col", "charCodeAt", "CharSequence", "_charCodes", "_lineNumbers", "_columns", "toString", "s", "idx", "String", "fromCharCode", "_assertIndex", "arr", "Error", "getStartColumn", "getEndColumn", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createFromDiffChange", "diffChange", "originalCharSequence", "modifiedCharSequence", "originalStart", "original<PERSON>ength", "modifiedStart", "<PERSON><PERSON><PERSON><PERSON>", "postProcessCharChanges", "rawChanges", "prevChange", "curr<PERSON>hange", "originalMatchingLength", "modifiedMatchingLength", "<PERSON><PERSON><PERSON><PERSON>", "Math", "min", "LineChange", "createFromDiffResult", "originalLineSequence", "modifiedLineSequence", "continueCharDiff", "opts", "continueLineDiff", "createContinueProcessingPredicate", "diffResult", "lineChanges", "originalLineIndex", "modifiedLineIndex", "nextChange", "originalStop", "modifiedStop", "originalLine", "modifiedLine", "originalChar", "modifiedChar", "_pushTrimWhitespaceCharChange", "originalMaxColumn", "modifiedMaxColumn", "originalLineNumber", "modifiedLineNumber", "_mergeTrimWhitespaceCharChange", "txt", "defaultValue", "r", "firstNonWhitespaceIndex", "lastNonWhitespaceIndex", "maximumRuntime", "startTime", "Date", "now"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/diff/legacyLinesDiffComputer.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { LcsDiff } from '../../../base/common/diff/diff.js';\nimport { LinesDiff } from './linesDiffComputer.js';\nimport { RangeMapping, DetailedLineRangeMapping } from './rangeMapping.js';\nimport * as strings from '../../../base/common/strings.js';\nimport { Range } from '../core/range.js';\nimport { assertFn, checkAdjacentItems } from '../../../base/common/assert.js';\nimport { LineRange } from '../core/lineRange.js';\nconst MINIMUM_MATCHING_CHARACTER_LENGTH = 3;\nexport class LegacyLinesDiffComputer {\n    computeDiff(originalLines, modifiedLines, options) {\n        const diffComputer = new DiffComputer(originalLines, modifiedLines, {\n            maxComputationTime: options.maxComputationTimeMs,\n            shouldIgnoreTrimWhitespace: options.ignoreTrimWhitespace,\n            shouldComputeCharChanges: true,\n            shouldMakePrettyDiff: true,\n            shouldPostProcessCharChanges: true,\n        });\n        const result = diffComputer.computeDiff();\n        const changes = [];\n        let lastChange = null;\n        for (const c of result.changes) {\n            let originalRange;\n            if (c.originalEndLineNumber === 0) {\n                // Insertion\n                originalRange = new LineRange(c.originalStartLineNumber + 1, c.originalStartLineNumber + 1);\n            }\n            else {\n                originalRange = new LineRange(c.originalStartLineNumber, c.originalEndLineNumber + 1);\n            }\n            let modifiedRange;\n            if (c.modifiedEndLineNumber === 0) {\n                // Deletion\n                modifiedRange = new LineRange(c.modifiedStartLineNumber + 1, c.modifiedStartLineNumber + 1);\n            }\n            else {\n                modifiedRange = new LineRange(c.modifiedStartLineNumber, c.modifiedEndLineNumber + 1);\n            }\n            let change = new DetailedLineRangeMapping(originalRange, modifiedRange, c.charChanges?.map(c => new RangeMapping(new Range(c.originalStartLineNumber, c.originalStartColumn, c.originalEndLineNumber, c.originalEndColumn), new Range(c.modifiedStartLineNumber, c.modifiedStartColumn, c.modifiedEndLineNumber, c.modifiedEndColumn))));\n            if (lastChange) {\n                if (lastChange.modified.endLineNumberExclusive === change.modified.startLineNumber\n                    || lastChange.original.endLineNumberExclusive === change.original.startLineNumber) {\n                    // join touching diffs. Probably moving diffs up/down in the algorithm causes touching diffs.\n                    change = new DetailedLineRangeMapping(lastChange.original.join(change.original), lastChange.modified.join(change.modified), lastChange.innerChanges && change.innerChanges ?\n                        lastChange.innerChanges.concat(change.innerChanges) : undefined);\n                    changes.pop();\n                }\n            }\n            changes.push(change);\n            lastChange = change;\n        }\n        assertFn(() => {\n            return checkAdjacentItems(changes, (m1, m2) => m2.original.startLineNumber - m1.original.endLineNumberExclusive === m2.modified.startLineNumber - m1.modified.endLineNumberExclusive &&\n                // There has to be an unchanged line in between (otherwise both diffs should have been joined)\n                m1.original.endLineNumberExclusive < m2.original.startLineNumber &&\n                m1.modified.endLineNumberExclusive < m2.modified.startLineNumber);\n        });\n        return new LinesDiff(changes, [], result.quitEarly);\n    }\n}\nfunction computeDiff(originalSequence, modifiedSequence, continueProcessingPredicate, pretty) {\n    const diffAlgo = new LcsDiff(originalSequence, modifiedSequence, continueProcessingPredicate);\n    return diffAlgo.ComputeDiff(pretty);\n}\nclass LineSequence {\n    constructor(lines) {\n        const startColumns = [];\n        const endColumns = [];\n        for (let i = 0, length = lines.length; i < length; i++) {\n            startColumns[i] = getFirstNonBlankColumn(lines[i], 1);\n            endColumns[i] = getLastNonBlankColumn(lines[i], 1);\n        }\n        this.lines = lines;\n        this._startColumns = startColumns;\n        this._endColumns = endColumns;\n    }\n    getElements() {\n        const elements = [];\n        for (let i = 0, len = this.lines.length; i < len; i++) {\n            elements[i] = this.lines[i].substring(this._startColumns[i] - 1, this._endColumns[i] - 1);\n        }\n        return elements;\n    }\n    getStrictElement(index) {\n        return this.lines[index];\n    }\n    getStartLineNumber(i) {\n        return i + 1;\n    }\n    getEndLineNumber(i) {\n        return i + 1;\n    }\n    createCharSequence(shouldIgnoreTrimWhitespace, startIndex, endIndex) {\n        const charCodes = [];\n        const lineNumbers = [];\n        const columns = [];\n        let len = 0;\n        for (let index = startIndex; index <= endIndex; index++) {\n            const lineContent = this.lines[index];\n            const startColumn = (shouldIgnoreTrimWhitespace ? this._startColumns[index] : 1);\n            const endColumn = (shouldIgnoreTrimWhitespace ? this._endColumns[index] : lineContent.length + 1);\n            for (let col = startColumn; col < endColumn; col++) {\n                charCodes[len] = lineContent.charCodeAt(col - 1);\n                lineNumbers[len] = index + 1;\n                columns[len] = col;\n                len++;\n            }\n            if (!shouldIgnoreTrimWhitespace && index < endIndex) {\n                // Add \\n if trim whitespace is not ignored\n                charCodes[len] = 10 /* CharCode.LineFeed */;\n                lineNumbers[len] = index + 1;\n                columns[len] = lineContent.length + 1;\n                len++;\n            }\n        }\n        return new CharSequence(charCodes, lineNumbers, columns);\n    }\n}\nclass CharSequence {\n    constructor(charCodes, lineNumbers, columns) {\n        this._charCodes = charCodes;\n        this._lineNumbers = lineNumbers;\n        this._columns = columns;\n    }\n    toString() {\n        return ('[' + this._charCodes.map((s, idx) => (s === 10 /* CharCode.LineFeed */ ? '\\\\n' : String.fromCharCode(s)) + `-(${this._lineNumbers[idx]},${this._columns[idx]})`).join(', ') + ']');\n    }\n    _assertIndex(index, arr) {\n        if (index < 0 || index >= arr.length) {\n            throw new Error(`Illegal index`);\n        }\n    }\n    getElements() {\n        return this._charCodes;\n    }\n    getStartLineNumber(i) {\n        if (i > 0 && i === this._lineNumbers.length) {\n            // the start line number of the element after the last element\n            // is the end line number of the last element\n            return this.getEndLineNumber(i - 1);\n        }\n        this._assertIndex(i, this._lineNumbers);\n        return this._lineNumbers[i];\n    }\n    getEndLineNumber(i) {\n        if (i === -1) {\n            // the end line number of the element before the first element\n            // is the start line number of the first element\n            return this.getStartLineNumber(i + 1);\n        }\n        this._assertIndex(i, this._lineNumbers);\n        if (this._charCodes[i] === 10 /* CharCode.LineFeed */) {\n            return this._lineNumbers[i] + 1;\n        }\n        return this._lineNumbers[i];\n    }\n    getStartColumn(i) {\n        if (i > 0 && i === this._columns.length) {\n            // the start column of the element after the last element\n            // is the end column of the last element\n            return this.getEndColumn(i - 1);\n        }\n        this._assertIndex(i, this._columns);\n        return this._columns[i];\n    }\n    getEndColumn(i) {\n        if (i === -1) {\n            // the end column of the element before the first element\n            // is the start column of the first element\n            return this.getStartColumn(i + 1);\n        }\n        this._assertIndex(i, this._columns);\n        if (this._charCodes[i] === 10 /* CharCode.LineFeed */) {\n            return 1;\n        }\n        return this._columns[i] + 1;\n    }\n}\nclass CharChange {\n    constructor(originalStartLineNumber, originalStartColumn, originalEndLineNumber, originalEndColumn, modifiedStartLineNumber, modifiedStartColumn, modifiedEndLineNumber, modifiedEndColumn) {\n        this.originalStartLineNumber = originalStartLineNumber;\n        this.originalStartColumn = originalStartColumn;\n        this.originalEndLineNumber = originalEndLineNumber;\n        this.originalEndColumn = originalEndColumn;\n        this.modifiedStartLineNumber = modifiedStartLineNumber;\n        this.modifiedStartColumn = modifiedStartColumn;\n        this.modifiedEndLineNumber = modifiedEndLineNumber;\n        this.modifiedEndColumn = modifiedEndColumn;\n    }\n    static createFromDiffChange(diffChange, originalCharSequence, modifiedCharSequence) {\n        const originalStartLineNumber = originalCharSequence.getStartLineNumber(diffChange.originalStart);\n        const originalStartColumn = originalCharSequence.getStartColumn(diffChange.originalStart);\n        const originalEndLineNumber = originalCharSequence.getEndLineNumber(diffChange.originalStart + diffChange.originalLength - 1);\n        const originalEndColumn = originalCharSequence.getEndColumn(diffChange.originalStart + diffChange.originalLength - 1);\n        const modifiedStartLineNumber = modifiedCharSequence.getStartLineNumber(diffChange.modifiedStart);\n        const modifiedStartColumn = modifiedCharSequence.getStartColumn(diffChange.modifiedStart);\n        const modifiedEndLineNumber = modifiedCharSequence.getEndLineNumber(diffChange.modifiedStart + diffChange.modifiedLength - 1);\n        const modifiedEndColumn = modifiedCharSequence.getEndColumn(diffChange.modifiedStart + diffChange.modifiedLength - 1);\n        return new CharChange(originalStartLineNumber, originalStartColumn, originalEndLineNumber, originalEndColumn, modifiedStartLineNumber, modifiedStartColumn, modifiedEndLineNumber, modifiedEndColumn);\n    }\n}\nfunction postProcessCharChanges(rawChanges) {\n    if (rawChanges.length <= 1) {\n        return rawChanges;\n    }\n    const result = [rawChanges[0]];\n    let prevChange = result[0];\n    for (let i = 1, len = rawChanges.length; i < len; i++) {\n        const currChange = rawChanges[i];\n        const originalMatchingLength = currChange.originalStart - (prevChange.originalStart + prevChange.originalLength);\n        const modifiedMatchingLength = currChange.modifiedStart - (prevChange.modifiedStart + prevChange.modifiedLength);\n        // Both of the above should be equal, but the continueProcessingPredicate may prevent this from being true\n        const matchingLength = Math.min(originalMatchingLength, modifiedMatchingLength);\n        if (matchingLength < MINIMUM_MATCHING_CHARACTER_LENGTH) {\n            // Merge the current change into the previous one\n            prevChange.originalLength = (currChange.originalStart + currChange.originalLength) - prevChange.originalStart;\n            prevChange.modifiedLength = (currChange.modifiedStart + currChange.modifiedLength) - prevChange.modifiedStart;\n        }\n        else {\n            // Add the current change\n            result.push(currChange);\n            prevChange = currChange;\n        }\n    }\n    return result;\n}\nclass LineChange {\n    constructor(originalStartLineNumber, originalEndLineNumber, modifiedStartLineNumber, modifiedEndLineNumber, charChanges) {\n        this.originalStartLineNumber = originalStartLineNumber;\n        this.originalEndLineNumber = originalEndLineNumber;\n        this.modifiedStartLineNumber = modifiedStartLineNumber;\n        this.modifiedEndLineNumber = modifiedEndLineNumber;\n        this.charChanges = charChanges;\n    }\n    static createFromDiffResult(shouldIgnoreTrimWhitespace, diffChange, originalLineSequence, modifiedLineSequence, continueCharDiff, shouldComputeCharChanges, shouldPostProcessCharChanges) {\n        let originalStartLineNumber;\n        let originalEndLineNumber;\n        let modifiedStartLineNumber;\n        let modifiedEndLineNumber;\n        let charChanges = undefined;\n        if (diffChange.originalLength === 0) {\n            originalStartLineNumber = originalLineSequence.getStartLineNumber(diffChange.originalStart) - 1;\n            originalEndLineNumber = 0;\n        }\n        else {\n            originalStartLineNumber = originalLineSequence.getStartLineNumber(diffChange.originalStart);\n            originalEndLineNumber = originalLineSequence.getEndLineNumber(diffChange.originalStart + diffChange.originalLength - 1);\n        }\n        if (diffChange.modifiedLength === 0) {\n            modifiedStartLineNumber = modifiedLineSequence.getStartLineNumber(diffChange.modifiedStart) - 1;\n            modifiedEndLineNumber = 0;\n        }\n        else {\n            modifiedStartLineNumber = modifiedLineSequence.getStartLineNumber(diffChange.modifiedStart);\n            modifiedEndLineNumber = modifiedLineSequence.getEndLineNumber(diffChange.modifiedStart + diffChange.modifiedLength - 1);\n        }\n        if (shouldComputeCharChanges && diffChange.originalLength > 0 && diffChange.originalLength < 20 && diffChange.modifiedLength > 0 && diffChange.modifiedLength < 20 && continueCharDiff()) {\n            // Compute character changes for diff chunks of at most 20 lines...\n            const originalCharSequence = originalLineSequence.createCharSequence(shouldIgnoreTrimWhitespace, diffChange.originalStart, diffChange.originalStart + diffChange.originalLength - 1);\n            const modifiedCharSequence = modifiedLineSequence.createCharSequence(shouldIgnoreTrimWhitespace, diffChange.modifiedStart, diffChange.modifiedStart + diffChange.modifiedLength - 1);\n            if (originalCharSequence.getElements().length > 0 && modifiedCharSequence.getElements().length > 0) {\n                let rawChanges = computeDiff(originalCharSequence, modifiedCharSequence, continueCharDiff, true).changes;\n                if (shouldPostProcessCharChanges) {\n                    rawChanges = postProcessCharChanges(rawChanges);\n                }\n                charChanges = [];\n                for (let i = 0, length = rawChanges.length; i < length; i++) {\n                    charChanges.push(CharChange.createFromDiffChange(rawChanges[i], originalCharSequence, modifiedCharSequence));\n                }\n            }\n        }\n        return new LineChange(originalStartLineNumber, originalEndLineNumber, modifiedStartLineNumber, modifiedEndLineNumber, charChanges);\n    }\n}\nexport class DiffComputer {\n    constructor(originalLines, modifiedLines, opts) {\n        this.shouldComputeCharChanges = opts.shouldComputeCharChanges;\n        this.shouldPostProcessCharChanges = opts.shouldPostProcessCharChanges;\n        this.shouldIgnoreTrimWhitespace = opts.shouldIgnoreTrimWhitespace;\n        this.shouldMakePrettyDiff = opts.shouldMakePrettyDiff;\n        this.originalLines = originalLines;\n        this.modifiedLines = modifiedLines;\n        this.original = new LineSequence(originalLines);\n        this.modified = new LineSequence(modifiedLines);\n        this.continueLineDiff = createContinueProcessingPredicate(opts.maxComputationTime);\n        this.continueCharDiff = createContinueProcessingPredicate(opts.maxComputationTime === 0 ? 0 : Math.min(opts.maxComputationTime, 5000)); // never run after 5s for character changes...\n    }\n    computeDiff() {\n        if (this.original.lines.length === 1 && this.original.lines[0].length === 0) {\n            // empty original => fast path\n            if (this.modified.lines.length === 1 && this.modified.lines[0].length === 0) {\n                return {\n                    quitEarly: false,\n                    changes: []\n                };\n            }\n            return {\n                quitEarly: false,\n                changes: [{\n                        originalStartLineNumber: 1,\n                        originalEndLineNumber: 1,\n                        modifiedStartLineNumber: 1,\n                        modifiedEndLineNumber: this.modified.lines.length,\n                        charChanges: undefined\n                    }]\n            };\n        }\n        if (this.modified.lines.length === 1 && this.modified.lines[0].length === 0) {\n            // empty modified => fast path\n            return {\n                quitEarly: false,\n                changes: [{\n                        originalStartLineNumber: 1,\n                        originalEndLineNumber: this.original.lines.length,\n                        modifiedStartLineNumber: 1,\n                        modifiedEndLineNumber: 1,\n                        charChanges: undefined\n                    }]\n            };\n        }\n        const diffResult = computeDiff(this.original, this.modified, this.continueLineDiff, this.shouldMakePrettyDiff);\n        const rawChanges = diffResult.changes;\n        const quitEarly = diffResult.quitEarly;\n        // The diff is always computed with ignoring trim whitespace\n        // This ensures we get the prettiest diff\n        if (this.shouldIgnoreTrimWhitespace) {\n            const lineChanges = [];\n            for (let i = 0, length = rawChanges.length; i < length; i++) {\n                lineChanges.push(LineChange.createFromDiffResult(this.shouldIgnoreTrimWhitespace, rawChanges[i], this.original, this.modified, this.continueCharDiff, this.shouldComputeCharChanges, this.shouldPostProcessCharChanges));\n            }\n            return {\n                quitEarly: quitEarly,\n                changes: lineChanges\n            };\n        }\n        // Need to post-process and introduce changes where the trim whitespace is different\n        // Note that we are looping starting at -1 to also cover the lines before the first change\n        const result = [];\n        let originalLineIndex = 0;\n        let modifiedLineIndex = 0;\n        for (let i = -1 /* !!!! */, len = rawChanges.length; i < len; i++) {\n            const nextChange = (i + 1 < len ? rawChanges[i + 1] : null);\n            const originalStop = (nextChange ? nextChange.originalStart : this.originalLines.length);\n            const modifiedStop = (nextChange ? nextChange.modifiedStart : this.modifiedLines.length);\n            while (originalLineIndex < originalStop && modifiedLineIndex < modifiedStop) {\n                const originalLine = this.originalLines[originalLineIndex];\n                const modifiedLine = this.modifiedLines[modifiedLineIndex];\n                if (originalLine !== modifiedLine) {\n                    // These lines differ only in trim whitespace\n                    // Check the leading whitespace\n                    {\n                        let originalStartColumn = getFirstNonBlankColumn(originalLine, 1);\n                        let modifiedStartColumn = getFirstNonBlankColumn(modifiedLine, 1);\n                        while (originalStartColumn > 1 && modifiedStartColumn > 1) {\n                            const originalChar = originalLine.charCodeAt(originalStartColumn - 2);\n                            const modifiedChar = modifiedLine.charCodeAt(modifiedStartColumn - 2);\n                            if (originalChar !== modifiedChar) {\n                                break;\n                            }\n                            originalStartColumn--;\n                            modifiedStartColumn--;\n                        }\n                        if (originalStartColumn > 1 || modifiedStartColumn > 1) {\n                            this._pushTrimWhitespaceCharChange(result, originalLineIndex + 1, 1, originalStartColumn, modifiedLineIndex + 1, 1, modifiedStartColumn);\n                        }\n                    }\n                    // Check the trailing whitespace\n                    {\n                        let originalEndColumn = getLastNonBlankColumn(originalLine, 1);\n                        let modifiedEndColumn = getLastNonBlankColumn(modifiedLine, 1);\n                        const originalMaxColumn = originalLine.length + 1;\n                        const modifiedMaxColumn = modifiedLine.length + 1;\n                        while (originalEndColumn < originalMaxColumn && modifiedEndColumn < modifiedMaxColumn) {\n                            const originalChar = originalLine.charCodeAt(originalEndColumn - 1);\n                            const modifiedChar = originalLine.charCodeAt(modifiedEndColumn - 1);\n                            if (originalChar !== modifiedChar) {\n                                break;\n                            }\n                            originalEndColumn++;\n                            modifiedEndColumn++;\n                        }\n                        if (originalEndColumn < originalMaxColumn || modifiedEndColumn < modifiedMaxColumn) {\n                            this._pushTrimWhitespaceCharChange(result, originalLineIndex + 1, originalEndColumn, originalMaxColumn, modifiedLineIndex + 1, modifiedEndColumn, modifiedMaxColumn);\n                        }\n                    }\n                }\n                originalLineIndex++;\n                modifiedLineIndex++;\n            }\n            if (nextChange) {\n                // Emit the actual change\n                result.push(LineChange.createFromDiffResult(this.shouldIgnoreTrimWhitespace, nextChange, this.original, this.modified, this.continueCharDiff, this.shouldComputeCharChanges, this.shouldPostProcessCharChanges));\n                originalLineIndex += nextChange.originalLength;\n                modifiedLineIndex += nextChange.modifiedLength;\n            }\n        }\n        return {\n            quitEarly: quitEarly,\n            changes: result\n        };\n    }\n    _pushTrimWhitespaceCharChange(result, originalLineNumber, originalStartColumn, originalEndColumn, modifiedLineNumber, modifiedStartColumn, modifiedEndColumn) {\n        if (this._mergeTrimWhitespaceCharChange(result, originalLineNumber, originalStartColumn, originalEndColumn, modifiedLineNumber, modifiedStartColumn, modifiedEndColumn)) {\n            // Merged into previous\n            return;\n        }\n        let charChanges = undefined;\n        if (this.shouldComputeCharChanges) {\n            charChanges = [new CharChange(originalLineNumber, originalStartColumn, originalLineNumber, originalEndColumn, modifiedLineNumber, modifiedStartColumn, modifiedLineNumber, modifiedEndColumn)];\n        }\n        result.push(new LineChange(originalLineNumber, originalLineNumber, modifiedLineNumber, modifiedLineNumber, charChanges));\n    }\n    _mergeTrimWhitespaceCharChange(result, originalLineNumber, originalStartColumn, originalEndColumn, modifiedLineNumber, modifiedStartColumn, modifiedEndColumn) {\n        const len = result.length;\n        if (len === 0) {\n            return false;\n        }\n        const prevChange = result[len - 1];\n        if (prevChange.originalEndLineNumber === 0 || prevChange.modifiedEndLineNumber === 0) {\n            // Don't merge with inserts/deletes\n            return false;\n        }\n        if (prevChange.originalEndLineNumber === originalLineNumber && prevChange.modifiedEndLineNumber === modifiedLineNumber) {\n            if (this.shouldComputeCharChanges && prevChange.charChanges) {\n                prevChange.charChanges.push(new CharChange(originalLineNumber, originalStartColumn, originalLineNumber, originalEndColumn, modifiedLineNumber, modifiedStartColumn, modifiedLineNumber, modifiedEndColumn));\n            }\n            return true;\n        }\n        if (prevChange.originalEndLineNumber + 1 === originalLineNumber && prevChange.modifiedEndLineNumber + 1 === modifiedLineNumber) {\n            prevChange.originalEndLineNumber = originalLineNumber;\n            prevChange.modifiedEndLineNumber = modifiedLineNumber;\n            if (this.shouldComputeCharChanges && prevChange.charChanges) {\n                prevChange.charChanges.push(new CharChange(originalLineNumber, originalStartColumn, originalLineNumber, originalEndColumn, modifiedLineNumber, modifiedStartColumn, modifiedLineNumber, modifiedEndColumn));\n            }\n            return true;\n        }\n        return false;\n    }\n}\nfunction getFirstNonBlankColumn(txt, defaultValue) {\n    const r = strings.firstNonWhitespaceIndex(txt);\n    if (r === -1) {\n        return defaultValue;\n    }\n    return r + 1;\n}\nfunction getLastNonBlankColumn(txt, defaultValue) {\n    const r = strings.lastNonWhitespaceIndex(txt);\n    if (r === -1) {\n        return defaultValue;\n    }\n    return r + 2;\n}\nfunction createContinueProcessingPredicate(maximumRuntime) {\n    if (maximumRuntime === 0) {\n        return () => true;\n    }\n    const startTime = Date.now();\n    return () => {\n        return Date.now() - startTime < maximumRuntime;\n    };\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,OAAO,QAAQ,mCAAmC;AAC3D,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,YAAY,EAAEC,wBAAwB,QAAQ,mBAAmB;AAC1E,OAAO,KAAKC,OAAO,MAAM,iCAAiC;AAC1D,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,QAAQ,EAAEC,kBAAkB,QAAQ,gCAAgC;AAC7E,SAASC,SAAS,QAAQ,sBAAsB;AAChD,MAAMC,iCAAiC,GAAG,CAAC;AAC3C,OAAO,MAAMC,uBAAuB,CAAC;EACjCC,WAAWA,CAACC,aAAa,EAAEC,aAAa,EAAEC,OAAO,EAAE;IAC/C,MAAMC,YAAY,GAAG,IAAIC,YAAY,CAACJ,aAAa,EAAEC,aAAa,EAAE;MAChEI,kBAAkB,EAAEH,OAAO,CAACI,oBAAoB;MAChDC,0BAA0B,EAAEL,OAAO,CAACM,oBAAoB;MACxDC,wBAAwB,EAAE,IAAI;MAC9BC,oBAAoB,EAAE,IAAI;MAC1BC,4BAA4B,EAAE;IAClC,CAAC,CAAC;IACF,MAAMC,MAAM,GAAGT,YAAY,CAACJ,WAAW,CAAC,CAAC;IACzC,MAAMc,OAAO,GAAG,EAAE;IAClB,IAAIC,UAAU,GAAG,IAAI;IACrB,KAAK,MAAMC,CAAC,IAAIH,MAAM,CAACC,OAAO,EAAE;MAC5B,IAAIG,aAAa;MACjB,IAAID,CAAC,CAACE,qBAAqB,KAAK,CAAC,EAAE;QAC/B;QACAD,aAAa,GAAG,IAAIpB,SAAS,CAACmB,CAAC,CAACG,uBAAuB,GAAG,CAAC,EAAEH,CAAC,CAACG,uBAAuB,GAAG,CAAC,CAAC;MAC/F,CAAC,MACI;QACDF,aAAa,GAAG,IAAIpB,SAAS,CAACmB,CAAC,CAACG,uBAAuB,EAAEH,CAAC,CAACE,qBAAqB,GAAG,CAAC,CAAC;MACzF;MACA,IAAIE,aAAa;MACjB,IAAIJ,CAAC,CAACK,qBAAqB,KAAK,CAAC,EAAE;QAC/B;QACAD,aAAa,GAAG,IAAIvB,SAAS,CAACmB,CAAC,CAACM,uBAAuB,GAAG,CAAC,EAAEN,CAAC,CAACM,uBAAuB,GAAG,CAAC,CAAC;MAC/F,CAAC,MACI;QACDF,aAAa,GAAG,IAAIvB,SAAS,CAACmB,CAAC,CAACM,uBAAuB,EAAEN,CAAC,CAACK,qBAAqB,GAAG,CAAC,CAAC;MACzF;MACA,IAAIE,MAAM,GAAG,IAAI/B,wBAAwB,CAACyB,aAAa,EAAEG,aAAa,EAAEJ,CAAC,CAACQ,WAAW,EAAEC,GAAG,CAACT,CAAC,IAAI,IAAIzB,YAAY,CAAC,IAAIG,KAAK,CAACsB,CAAC,CAACG,uBAAuB,EAAEH,CAAC,CAACU,mBAAmB,EAAEV,CAAC,CAACE,qBAAqB,EAAEF,CAAC,CAACW,iBAAiB,CAAC,EAAE,IAAIjC,KAAK,CAACsB,CAAC,CAACM,uBAAuB,EAAEN,CAAC,CAACY,mBAAmB,EAAEZ,CAAC,CAACK,qBAAqB,EAAEL,CAAC,CAACa,iBAAiB,CAAC,CAAC,CAAC,CAAC;MACxU,IAAId,UAAU,EAAE;QACZ,IAAIA,UAAU,CAACe,QAAQ,CAACC,sBAAsB,KAAKR,MAAM,CAACO,QAAQ,CAACE,eAAe,IAC3EjB,UAAU,CAACkB,QAAQ,CAACF,sBAAsB,KAAKR,MAAM,CAACU,QAAQ,CAACD,eAAe,EAAE;UACnF;UACAT,MAAM,GAAG,IAAI/B,wBAAwB,CAACuB,UAAU,CAACkB,QAAQ,CAACC,IAAI,CAACX,MAAM,CAACU,QAAQ,CAAC,EAAElB,UAAU,CAACe,QAAQ,CAACI,IAAI,CAACX,MAAM,CAACO,QAAQ,CAAC,EAAEf,UAAU,CAACoB,YAAY,IAAIZ,MAAM,CAACY,YAAY,GACtKpB,UAAU,CAACoB,YAAY,CAACC,MAAM,CAACb,MAAM,CAACY,YAAY,CAAC,GAAGE,SAAS,CAAC;UACpEvB,OAAO,CAACwB,GAAG,CAAC,CAAC;QACjB;MACJ;MACAxB,OAAO,CAACyB,IAAI,CAAChB,MAAM,CAAC;MACpBR,UAAU,GAAGQ,MAAM;IACvB;IACA5B,QAAQ,CAAC,MAAM;MACX,OAAOC,kBAAkB,CAACkB,OAAO,EAAE,CAAC0B,EAAE,EAAEC,EAAE,KAAKA,EAAE,CAACR,QAAQ,CAACD,eAAe,GAAGQ,EAAE,CAACP,QAAQ,CAACF,sBAAsB,KAAKU,EAAE,CAACX,QAAQ,CAACE,eAAe,GAAGQ,EAAE,CAACV,QAAQ,CAACC,sBAAsB;MAChL;MACAS,EAAE,CAACP,QAAQ,CAACF,sBAAsB,GAAGU,EAAE,CAACR,QAAQ,CAACD,eAAe,IAChEQ,EAAE,CAACV,QAAQ,CAACC,sBAAsB,GAAGU,EAAE,CAACX,QAAQ,CAACE,eAAe,CAAC;IACzE,CAAC,CAAC;IACF,OAAO,IAAI1C,SAAS,CAACwB,OAAO,EAAE,EAAE,EAAED,MAAM,CAAC6B,SAAS,CAAC;EACvD;AACJ;AACA,SAAS1C,WAAWA,CAAC2C,gBAAgB,EAAEC,gBAAgB,EAAEC,2BAA2B,EAAEC,MAAM,EAAE;EAC1F,MAAMC,QAAQ,GAAG,IAAI1D,OAAO,CAACsD,gBAAgB,EAAEC,gBAAgB,EAAEC,2BAA2B,CAAC;EAC7F,OAAOE,QAAQ,CAACC,WAAW,CAACF,MAAM,CAAC;AACvC;AACA,MAAMG,YAAY,CAAC;EACfC,WAAWA,CAACC,KAAK,EAAE;IACf,MAAMC,YAAY,GAAG,EAAE;IACvB,MAAMC,UAAU,GAAG,EAAE;IACrB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,MAAM,GAAGJ,KAAK,CAACI,MAAM,EAAED,CAAC,GAAGC,MAAM,EAAED,CAAC,EAAE,EAAE;MACpDF,YAAY,CAACE,CAAC,CAAC,GAAGE,sBAAsB,CAACL,KAAK,CAACG,CAAC,CAAC,EAAE,CAAC,CAAC;MACrDD,UAAU,CAACC,CAAC,CAAC,GAAGG,qBAAqB,CAACN,KAAK,CAACG,CAAC,CAAC,EAAE,CAAC,CAAC;IACtD;IACA,IAAI,CAACH,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACO,aAAa,GAAGN,YAAY;IACjC,IAAI,CAACO,WAAW,GAAGN,UAAU;EACjC;EACAO,WAAWA,CAAA,EAAG;IACV,MAAMC,QAAQ,GAAG,EAAE;IACnB,KAAK,IAAIP,CAAC,GAAG,CAAC,EAAEQ,GAAG,GAAG,IAAI,CAACX,KAAK,CAACI,MAAM,EAAED,CAAC,GAAGQ,GAAG,EAAER,CAAC,EAAE,EAAE;MACnDO,QAAQ,CAACP,CAAC,CAAC,GAAG,IAAI,CAACH,KAAK,CAACG,CAAC,CAAC,CAACS,SAAS,CAAC,IAAI,CAACL,aAAa,CAACJ,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAACK,WAAW,CAACL,CAAC,CAAC,GAAG,CAAC,CAAC;IAC7F;IACA,OAAOO,QAAQ;EACnB;EACAG,gBAAgBA,CAACC,KAAK,EAAE;IACpB,OAAO,IAAI,CAACd,KAAK,CAACc,KAAK,CAAC;EAC5B;EACAC,kBAAkBA,CAACZ,CAAC,EAAE;IAClB,OAAOA,CAAC,GAAG,CAAC;EAChB;EACAa,gBAAgBA,CAACb,CAAC,EAAE;IAChB,OAAOA,CAAC,GAAG,CAAC;EAChB;EACAc,kBAAkBA,CAAC5D,0BAA0B,EAAE6D,UAAU,EAAEC,QAAQ,EAAE;IACjE,MAAMC,SAAS,GAAG,EAAE;IACpB,MAAMC,WAAW,GAAG,EAAE;IACtB,MAAMC,OAAO,GAAG,EAAE;IAClB,IAAIX,GAAG,GAAG,CAAC;IACX,KAAK,IAAIG,KAAK,GAAGI,UAAU,EAAEJ,KAAK,IAAIK,QAAQ,EAAEL,KAAK,EAAE,EAAE;MACrD,MAAMS,WAAW,GAAG,IAAI,CAACvB,KAAK,CAACc,KAAK,CAAC;MACrC,MAAMU,WAAW,GAAInE,0BAA0B,GAAG,IAAI,CAACkD,aAAa,CAACO,KAAK,CAAC,GAAG,CAAE;MAChF,MAAMW,SAAS,GAAIpE,0BAA0B,GAAG,IAAI,CAACmD,WAAW,CAACM,KAAK,CAAC,GAAGS,WAAW,CAACnB,MAAM,GAAG,CAAE;MACjG,KAAK,IAAIsB,GAAG,GAAGF,WAAW,EAAEE,GAAG,GAAGD,SAAS,EAAEC,GAAG,EAAE,EAAE;QAChDN,SAAS,CAACT,GAAG,CAAC,GAAGY,WAAW,CAACI,UAAU,CAACD,GAAG,GAAG,CAAC,CAAC;QAChDL,WAAW,CAACV,GAAG,CAAC,GAAGG,KAAK,GAAG,CAAC;QAC5BQ,OAAO,CAACX,GAAG,CAAC,GAAGe,GAAG;QAClBf,GAAG,EAAE;MACT;MACA,IAAI,CAACtD,0BAA0B,IAAIyD,KAAK,GAAGK,QAAQ,EAAE;QACjD;QACAC,SAAS,CAACT,GAAG,CAAC,GAAG,EAAE,CAAC;QACpBU,WAAW,CAACV,GAAG,CAAC,GAAGG,KAAK,GAAG,CAAC;QAC5BQ,OAAO,CAACX,GAAG,CAAC,GAAGY,WAAW,CAACnB,MAAM,GAAG,CAAC;QACrCO,GAAG,EAAE;MACT;IACJ;IACA,OAAO,IAAIiB,YAAY,CAACR,SAAS,EAAEC,WAAW,EAAEC,OAAO,CAAC;EAC5D;AACJ;AACA,MAAMM,YAAY,CAAC;EACf7B,WAAWA,CAACqB,SAAS,EAAEC,WAAW,EAAEC,OAAO,EAAE;IACzC,IAAI,CAACO,UAAU,GAAGT,SAAS;IAC3B,IAAI,CAACU,YAAY,GAAGT,WAAW;IAC/B,IAAI,CAACU,QAAQ,GAAGT,OAAO;EAC3B;EACAU,QAAQA,CAAA,EAAG;IACP,OAAQ,GAAG,GAAG,IAAI,CAACH,UAAU,CAACvD,GAAG,CAAC,CAAC2D,CAAC,EAAEC,GAAG,KAAK,CAACD,CAAC,KAAK,EAAE,CAAC,0BAA0B,KAAK,GAAGE,MAAM,CAACC,YAAY,CAACH,CAAC,CAAC,IAAI,KAAK,IAAI,CAACH,YAAY,CAACI,GAAG,CAAC,IAAI,IAAI,CAACH,QAAQ,CAACG,GAAG,CAAC,GAAG,CAAC,CAACnD,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG;EAC9L;EACAsD,YAAYA,CAACvB,KAAK,EAAEwB,GAAG,EAAE;IACrB,IAAIxB,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAIwB,GAAG,CAAClC,MAAM,EAAE;MAClC,MAAM,IAAImC,KAAK,CAAC,eAAe,CAAC;IACpC;EACJ;EACA9B,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACoB,UAAU;EAC1B;EACAd,kBAAkBA,CAACZ,CAAC,EAAE;IAClB,IAAIA,CAAC,GAAG,CAAC,IAAIA,CAAC,KAAK,IAAI,CAAC2B,YAAY,CAAC1B,MAAM,EAAE;MACzC;MACA;MACA,OAAO,IAAI,CAACY,gBAAgB,CAACb,CAAC,GAAG,CAAC,CAAC;IACvC;IACA,IAAI,CAACkC,YAAY,CAAClC,CAAC,EAAE,IAAI,CAAC2B,YAAY,CAAC;IACvC,OAAO,IAAI,CAACA,YAAY,CAAC3B,CAAC,CAAC;EAC/B;EACAa,gBAAgBA,CAACb,CAAC,EAAE;IAChB,IAAIA,CAAC,KAAK,CAAC,CAAC,EAAE;MACV;MACA;MACA,OAAO,IAAI,CAACY,kBAAkB,CAACZ,CAAC,GAAG,CAAC,CAAC;IACzC;IACA,IAAI,CAACkC,YAAY,CAAClC,CAAC,EAAE,IAAI,CAAC2B,YAAY,CAAC;IACvC,IAAI,IAAI,CAACD,UAAU,CAAC1B,CAAC,CAAC,KAAK,EAAE,CAAC,yBAAyB;MACnD,OAAO,IAAI,CAAC2B,YAAY,CAAC3B,CAAC,CAAC,GAAG,CAAC;IACnC;IACA,OAAO,IAAI,CAAC2B,YAAY,CAAC3B,CAAC,CAAC;EAC/B;EACAqC,cAAcA,CAACrC,CAAC,EAAE;IACd,IAAIA,CAAC,GAAG,CAAC,IAAIA,CAAC,KAAK,IAAI,CAAC4B,QAAQ,CAAC3B,MAAM,EAAE;MACrC;MACA;MACA,OAAO,IAAI,CAACqC,YAAY,CAACtC,CAAC,GAAG,CAAC,CAAC;IACnC;IACA,IAAI,CAACkC,YAAY,CAAClC,CAAC,EAAE,IAAI,CAAC4B,QAAQ,CAAC;IACnC,OAAO,IAAI,CAACA,QAAQ,CAAC5B,CAAC,CAAC;EAC3B;EACAsC,YAAYA,CAACtC,CAAC,EAAE;IACZ,IAAIA,CAAC,KAAK,CAAC,CAAC,EAAE;MACV;MACA;MACA,OAAO,IAAI,CAACqC,cAAc,CAACrC,CAAC,GAAG,CAAC,CAAC;IACrC;IACA,IAAI,CAACkC,YAAY,CAAClC,CAAC,EAAE,IAAI,CAAC4B,QAAQ,CAAC;IACnC,IAAI,IAAI,CAACF,UAAU,CAAC1B,CAAC,CAAC,KAAK,EAAE,CAAC,yBAAyB;MACnD,OAAO,CAAC;IACZ;IACA,OAAO,IAAI,CAAC4B,QAAQ,CAAC5B,CAAC,CAAC,GAAG,CAAC;EAC/B;AACJ;AACA,MAAMuC,UAAU,CAAC;EACb3C,WAAWA,CAAC/B,uBAAuB,EAAEO,mBAAmB,EAAER,qBAAqB,EAAES,iBAAiB,EAAEL,uBAAuB,EAAEM,mBAAmB,EAAEP,qBAAqB,EAAEQ,iBAAiB,EAAE;IACxL,IAAI,CAACV,uBAAuB,GAAGA,uBAAuB;IACtD,IAAI,CAACO,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACR,qBAAqB,GAAGA,qBAAqB;IAClD,IAAI,CAACS,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACL,uBAAuB,GAAGA,uBAAuB;IACtD,IAAI,CAACM,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACP,qBAAqB,GAAGA,qBAAqB;IAClD,IAAI,CAACQ,iBAAiB,GAAGA,iBAAiB;EAC9C;EACA,OAAOiE,oBAAoBA,CAACC,UAAU,EAAEC,oBAAoB,EAAEC,oBAAoB,EAAE;IAChF,MAAM9E,uBAAuB,GAAG6E,oBAAoB,CAAC9B,kBAAkB,CAAC6B,UAAU,CAACG,aAAa,CAAC;IACjG,MAAMxE,mBAAmB,GAAGsE,oBAAoB,CAACL,cAAc,CAACI,UAAU,CAACG,aAAa,CAAC;IACzF,MAAMhF,qBAAqB,GAAG8E,oBAAoB,CAAC7B,gBAAgB,CAAC4B,UAAU,CAACG,aAAa,GAAGH,UAAU,CAACI,cAAc,GAAG,CAAC,CAAC;IAC7H,MAAMxE,iBAAiB,GAAGqE,oBAAoB,CAACJ,YAAY,CAACG,UAAU,CAACG,aAAa,GAAGH,UAAU,CAACI,cAAc,GAAG,CAAC,CAAC;IACrH,MAAM7E,uBAAuB,GAAG2E,oBAAoB,CAAC/B,kBAAkB,CAAC6B,UAAU,CAACK,aAAa,CAAC;IACjG,MAAMxE,mBAAmB,GAAGqE,oBAAoB,CAACN,cAAc,CAACI,UAAU,CAACK,aAAa,CAAC;IACzF,MAAM/E,qBAAqB,GAAG4E,oBAAoB,CAAC9B,gBAAgB,CAAC4B,UAAU,CAACK,aAAa,GAAGL,UAAU,CAACM,cAAc,GAAG,CAAC,CAAC;IAC7H,MAAMxE,iBAAiB,GAAGoE,oBAAoB,CAACL,YAAY,CAACG,UAAU,CAACK,aAAa,GAAGL,UAAU,CAACM,cAAc,GAAG,CAAC,CAAC;IACrH,OAAO,IAAIR,UAAU,CAAC1E,uBAAuB,EAAEO,mBAAmB,EAAER,qBAAqB,EAAES,iBAAiB,EAAEL,uBAAuB,EAAEM,mBAAmB,EAAEP,qBAAqB,EAAEQ,iBAAiB,CAAC;EACzM;AACJ;AACA,SAASyE,sBAAsBA,CAACC,UAAU,EAAE;EACxC,IAAIA,UAAU,CAAChD,MAAM,IAAI,CAAC,EAAE;IACxB,OAAOgD,UAAU;EACrB;EACA,MAAM1F,MAAM,GAAG,CAAC0F,UAAU,CAAC,CAAC,CAAC,CAAC;EAC9B,IAAIC,UAAU,GAAG3F,MAAM,CAAC,CAAC,CAAC;EAC1B,KAAK,IAAIyC,CAAC,GAAG,CAAC,EAAEQ,GAAG,GAAGyC,UAAU,CAAChD,MAAM,EAAED,CAAC,GAAGQ,GAAG,EAAER,CAAC,EAAE,EAAE;IACnD,MAAMmD,UAAU,GAAGF,UAAU,CAACjD,CAAC,CAAC;IAChC,MAAMoD,sBAAsB,GAAGD,UAAU,CAACP,aAAa,IAAIM,UAAU,CAACN,aAAa,GAAGM,UAAU,CAACL,cAAc,CAAC;IAChH,MAAMQ,sBAAsB,GAAGF,UAAU,CAACL,aAAa,IAAII,UAAU,CAACJ,aAAa,GAAGI,UAAU,CAACH,cAAc,CAAC;IAChH;IACA,MAAMO,cAAc,GAAGC,IAAI,CAACC,GAAG,CAACJ,sBAAsB,EAAEC,sBAAsB,CAAC;IAC/E,IAAIC,cAAc,GAAG9G,iCAAiC,EAAE;MACpD;MACA0G,UAAU,CAACL,cAAc,GAAIM,UAAU,CAACP,aAAa,GAAGO,UAAU,CAACN,cAAc,GAAIK,UAAU,CAACN,aAAa;MAC7GM,UAAU,CAACH,cAAc,GAAII,UAAU,CAACL,aAAa,GAAGK,UAAU,CAACJ,cAAc,GAAIG,UAAU,CAACJ,aAAa;IACjH,CAAC,MACI;MACD;MACAvF,MAAM,CAAC0B,IAAI,CAACkE,UAAU,CAAC;MACvBD,UAAU,GAAGC,UAAU;IAC3B;EACJ;EACA,OAAO5F,MAAM;AACjB;AACA,MAAMkG,UAAU,CAAC;EACb7D,WAAWA,CAAC/B,uBAAuB,EAAED,qBAAqB,EAAEI,uBAAuB,EAAED,qBAAqB,EAAEG,WAAW,EAAE;IACrH,IAAI,CAACL,uBAAuB,GAAGA,uBAAuB;IACtD,IAAI,CAACD,qBAAqB,GAAGA,qBAAqB;IAClD,IAAI,CAACI,uBAAuB,GAAGA,uBAAuB;IACtD,IAAI,CAACD,qBAAqB,GAAGA,qBAAqB;IAClD,IAAI,CAACG,WAAW,GAAGA,WAAW;EAClC;EACA,OAAOwF,oBAAoBA,CAACxG,0BAA0B,EAAEuF,UAAU,EAAEkB,oBAAoB,EAAEC,oBAAoB,EAAEC,gBAAgB,EAAEzG,wBAAwB,EAAEE,4BAA4B,EAAE;IACtL,IAAIO,uBAAuB;IAC3B,IAAID,qBAAqB;IACzB,IAAII,uBAAuB;IAC3B,IAAID,qBAAqB;IACzB,IAAIG,WAAW,GAAGa,SAAS;IAC3B,IAAI0D,UAAU,CAACI,cAAc,KAAK,CAAC,EAAE;MACjChF,uBAAuB,GAAG8F,oBAAoB,CAAC/C,kBAAkB,CAAC6B,UAAU,CAACG,aAAa,CAAC,GAAG,CAAC;MAC/FhF,qBAAqB,GAAG,CAAC;IAC7B,CAAC,MACI;MACDC,uBAAuB,GAAG8F,oBAAoB,CAAC/C,kBAAkB,CAAC6B,UAAU,CAACG,aAAa,CAAC;MAC3FhF,qBAAqB,GAAG+F,oBAAoB,CAAC9C,gBAAgB,CAAC4B,UAAU,CAACG,aAAa,GAAGH,UAAU,CAACI,cAAc,GAAG,CAAC,CAAC;IAC3H;IACA,IAAIJ,UAAU,CAACM,cAAc,KAAK,CAAC,EAAE;MACjC/E,uBAAuB,GAAG4F,oBAAoB,CAAChD,kBAAkB,CAAC6B,UAAU,CAACK,aAAa,CAAC,GAAG,CAAC;MAC/F/E,qBAAqB,GAAG,CAAC;IAC7B,CAAC,MACI;MACDC,uBAAuB,GAAG4F,oBAAoB,CAAChD,kBAAkB,CAAC6B,UAAU,CAACK,aAAa,CAAC;MAC3F/E,qBAAqB,GAAG6F,oBAAoB,CAAC/C,gBAAgB,CAAC4B,UAAU,CAACK,aAAa,GAAGL,UAAU,CAACM,cAAc,GAAG,CAAC,CAAC;IAC3H;IACA,IAAI3F,wBAAwB,IAAIqF,UAAU,CAACI,cAAc,GAAG,CAAC,IAAIJ,UAAU,CAACI,cAAc,GAAG,EAAE,IAAIJ,UAAU,CAACM,cAAc,GAAG,CAAC,IAAIN,UAAU,CAACM,cAAc,GAAG,EAAE,IAAIc,gBAAgB,CAAC,CAAC,EAAE;MACtL;MACA,MAAMnB,oBAAoB,GAAGiB,oBAAoB,CAAC7C,kBAAkB,CAAC5D,0BAA0B,EAAEuF,UAAU,CAACG,aAAa,EAAEH,UAAU,CAACG,aAAa,GAAGH,UAAU,CAACI,cAAc,GAAG,CAAC,CAAC;MACpL,MAAMF,oBAAoB,GAAGiB,oBAAoB,CAAC9C,kBAAkB,CAAC5D,0BAA0B,EAAEuF,UAAU,CAACK,aAAa,EAAEL,UAAU,CAACK,aAAa,GAAGL,UAAU,CAACM,cAAc,GAAG,CAAC,CAAC;MACpL,IAAIL,oBAAoB,CAACpC,WAAW,CAAC,CAAC,CAACL,MAAM,GAAG,CAAC,IAAI0C,oBAAoB,CAACrC,WAAW,CAAC,CAAC,CAACL,MAAM,GAAG,CAAC,EAAE;QAChG,IAAIgD,UAAU,GAAGvG,WAAW,CAACgG,oBAAoB,EAAEC,oBAAoB,EAAEkB,gBAAgB,EAAE,IAAI,CAAC,CAACrG,OAAO;QACxG,IAAIF,4BAA4B,EAAE;UAC9B2F,UAAU,GAAGD,sBAAsB,CAACC,UAAU,CAAC;QACnD;QACA/E,WAAW,GAAG,EAAE;QAChB,KAAK,IAAI8B,CAAC,GAAG,CAAC,EAAEC,MAAM,GAAGgD,UAAU,CAAChD,MAAM,EAAED,CAAC,GAAGC,MAAM,EAAED,CAAC,EAAE,EAAE;UACzD9B,WAAW,CAACe,IAAI,CAACsD,UAAU,CAACC,oBAAoB,CAACS,UAAU,CAACjD,CAAC,CAAC,EAAE0C,oBAAoB,EAAEC,oBAAoB,CAAC,CAAC;QAChH;MACJ;IACJ;IACA,OAAO,IAAIc,UAAU,CAAC5F,uBAAuB,EAAED,qBAAqB,EAAEI,uBAAuB,EAAED,qBAAqB,EAAEG,WAAW,CAAC;EACtI;AACJ;AACA,OAAO,MAAMnB,YAAY,CAAC;EACtB6C,WAAWA,CAACjD,aAAa,EAAEC,aAAa,EAAEkH,IAAI,EAAE;IAC5C,IAAI,CAAC1G,wBAAwB,GAAG0G,IAAI,CAAC1G,wBAAwB;IAC7D,IAAI,CAACE,4BAA4B,GAAGwG,IAAI,CAACxG,4BAA4B;IACrE,IAAI,CAACJ,0BAA0B,GAAG4G,IAAI,CAAC5G,0BAA0B;IACjE,IAAI,CAACG,oBAAoB,GAAGyG,IAAI,CAACzG,oBAAoB;IACrD,IAAI,CAACV,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAAC+B,QAAQ,GAAG,IAAIgB,YAAY,CAAChD,aAAa,CAAC;IAC/C,IAAI,CAAC6B,QAAQ,GAAG,IAAImB,YAAY,CAAC/C,aAAa,CAAC;IAC/C,IAAI,CAACmH,gBAAgB,GAAGC,iCAAiC,CAACF,IAAI,CAAC9G,kBAAkB,CAAC;IAClF,IAAI,CAAC6G,gBAAgB,GAAGG,iCAAiC,CAACF,IAAI,CAAC9G,kBAAkB,KAAK,CAAC,GAAG,CAAC,GAAGuG,IAAI,CAACC,GAAG,CAACM,IAAI,CAAC9G,kBAAkB,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;EAC5I;EACAN,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACiC,QAAQ,CAACkB,KAAK,CAACI,MAAM,KAAK,CAAC,IAAI,IAAI,CAACtB,QAAQ,CAACkB,KAAK,CAAC,CAAC,CAAC,CAACI,MAAM,KAAK,CAAC,EAAE;MACzE;MACA,IAAI,IAAI,CAACzB,QAAQ,CAACqB,KAAK,CAACI,MAAM,KAAK,CAAC,IAAI,IAAI,CAACzB,QAAQ,CAACqB,KAAK,CAAC,CAAC,CAAC,CAACI,MAAM,KAAK,CAAC,EAAE;QACzE,OAAO;UACHb,SAAS,EAAE,KAAK;UAChB5B,OAAO,EAAE;QACb,CAAC;MACL;MACA,OAAO;QACH4B,SAAS,EAAE,KAAK;QAChB5B,OAAO,EAAE,CAAC;UACFK,uBAAuB,EAAE,CAAC;UAC1BD,qBAAqB,EAAE,CAAC;UACxBI,uBAAuB,EAAE,CAAC;UAC1BD,qBAAqB,EAAE,IAAI,CAACS,QAAQ,CAACqB,KAAK,CAACI,MAAM;UACjD/B,WAAW,EAAEa;QACjB,CAAC;MACT,CAAC;IACL;IACA,IAAI,IAAI,CAACP,QAAQ,CAACqB,KAAK,CAACI,MAAM,KAAK,CAAC,IAAI,IAAI,CAACzB,QAAQ,CAACqB,KAAK,CAAC,CAAC,CAAC,CAACI,MAAM,KAAK,CAAC,EAAE;MACzE;MACA,OAAO;QACHb,SAAS,EAAE,KAAK;QAChB5B,OAAO,EAAE,CAAC;UACFK,uBAAuB,EAAE,CAAC;UAC1BD,qBAAqB,EAAE,IAAI,CAACe,QAAQ,CAACkB,KAAK,CAACI,MAAM;UACjDjC,uBAAuB,EAAE,CAAC;UAC1BD,qBAAqB,EAAE,CAAC;UACxBG,WAAW,EAAEa;QACjB,CAAC;MACT,CAAC;IACL;IACA,MAAMkF,UAAU,GAAGvH,WAAW,CAAC,IAAI,CAACiC,QAAQ,EAAE,IAAI,CAACH,QAAQ,EAAE,IAAI,CAACuF,gBAAgB,EAAE,IAAI,CAAC1G,oBAAoB,CAAC;IAC9G,MAAM4F,UAAU,GAAGgB,UAAU,CAACzG,OAAO;IACrC,MAAM4B,SAAS,GAAG6E,UAAU,CAAC7E,SAAS;IACtC;IACA;IACA,IAAI,IAAI,CAAClC,0BAA0B,EAAE;MACjC,MAAMgH,WAAW,GAAG,EAAE;MACtB,KAAK,IAAIlE,CAAC,GAAG,CAAC,EAAEC,MAAM,GAAGgD,UAAU,CAAChD,MAAM,EAAED,CAAC,GAAGC,MAAM,EAAED,CAAC,EAAE,EAAE;QACzDkE,WAAW,CAACjF,IAAI,CAACwE,UAAU,CAACC,oBAAoB,CAAC,IAAI,CAACxG,0BAA0B,EAAE+F,UAAU,CAACjD,CAAC,CAAC,EAAE,IAAI,CAACrB,QAAQ,EAAE,IAAI,CAACH,QAAQ,EAAE,IAAI,CAACqF,gBAAgB,EAAE,IAAI,CAACzG,wBAAwB,EAAE,IAAI,CAACE,4BAA4B,CAAC,CAAC;MAC5N;MACA,OAAO;QACH8B,SAAS,EAAEA,SAAS;QACpB5B,OAAO,EAAE0G;MACb,CAAC;IACL;IACA;IACA;IACA,MAAM3G,MAAM,GAAG,EAAE;IACjB,IAAI4G,iBAAiB,GAAG,CAAC;IACzB,IAAIC,iBAAiB,GAAG,CAAC;IACzB,KAAK,IAAIpE,CAAC,GAAG,CAAC,CAAC,CAAC,YAAYQ,GAAG,GAAGyC,UAAU,CAAChD,MAAM,EAAED,CAAC,GAAGQ,GAAG,EAAER,CAAC,EAAE,EAAE;MAC/D,MAAMqE,UAAU,GAAIrE,CAAC,GAAG,CAAC,GAAGQ,GAAG,GAAGyC,UAAU,CAACjD,CAAC,GAAG,CAAC,CAAC,GAAG,IAAK;MAC3D,MAAMsE,YAAY,GAAID,UAAU,GAAGA,UAAU,CAACzB,aAAa,GAAG,IAAI,CAACjG,aAAa,CAACsD,MAAO;MACxF,MAAMsE,YAAY,GAAIF,UAAU,GAAGA,UAAU,CAACvB,aAAa,GAAG,IAAI,CAAClG,aAAa,CAACqD,MAAO;MACxF,OAAOkE,iBAAiB,GAAGG,YAAY,IAAIF,iBAAiB,GAAGG,YAAY,EAAE;QACzE,MAAMC,YAAY,GAAG,IAAI,CAAC7H,aAAa,CAACwH,iBAAiB,CAAC;QAC1D,MAAMM,YAAY,GAAG,IAAI,CAAC7H,aAAa,CAACwH,iBAAiB,CAAC;QAC1D,IAAII,YAAY,KAAKC,YAAY,EAAE;UAC/B;UACA;UACA;YACI,IAAIrG,mBAAmB,GAAG8B,sBAAsB,CAACsE,YAAY,EAAE,CAAC,CAAC;YACjE,IAAIlG,mBAAmB,GAAG4B,sBAAsB,CAACuE,YAAY,EAAE,CAAC,CAAC;YACjE,OAAOrG,mBAAmB,GAAG,CAAC,IAAIE,mBAAmB,GAAG,CAAC,EAAE;cACvD,MAAMoG,YAAY,GAAGF,YAAY,CAAChD,UAAU,CAACpD,mBAAmB,GAAG,CAAC,CAAC;cACrE,MAAMuG,YAAY,GAAGF,YAAY,CAACjD,UAAU,CAAClD,mBAAmB,GAAG,CAAC,CAAC;cACrE,IAAIoG,YAAY,KAAKC,YAAY,EAAE;gBAC/B;cACJ;cACAvG,mBAAmB,EAAE;cACrBE,mBAAmB,EAAE;YACzB;YACA,IAAIF,mBAAmB,GAAG,CAAC,IAAIE,mBAAmB,GAAG,CAAC,EAAE;cACpD,IAAI,CAACsG,6BAA6B,CAACrH,MAAM,EAAE4G,iBAAiB,GAAG,CAAC,EAAE,CAAC,EAAE/F,mBAAmB,EAAEgG,iBAAiB,GAAG,CAAC,EAAE,CAAC,EAAE9F,mBAAmB,CAAC;YAC5I;UACJ;UACA;UACA;YACI,IAAID,iBAAiB,GAAG8B,qBAAqB,CAACqE,YAAY,EAAE,CAAC,CAAC;YAC9D,IAAIjG,iBAAiB,GAAG4B,qBAAqB,CAACsE,YAAY,EAAE,CAAC,CAAC;YAC9D,MAAMI,iBAAiB,GAAGL,YAAY,CAACvE,MAAM,GAAG,CAAC;YACjD,MAAM6E,iBAAiB,GAAGL,YAAY,CAACxE,MAAM,GAAG,CAAC;YACjD,OAAO5B,iBAAiB,GAAGwG,iBAAiB,IAAItG,iBAAiB,GAAGuG,iBAAiB,EAAE;cACnF,MAAMJ,YAAY,GAAGF,YAAY,CAAChD,UAAU,CAACnD,iBAAiB,GAAG,CAAC,CAAC;cACnE,MAAMsG,YAAY,GAAGH,YAAY,CAAChD,UAAU,CAACjD,iBAAiB,GAAG,CAAC,CAAC;cACnE,IAAImG,YAAY,KAAKC,YAAY,EAAE;gBAC/B;cACJ;cACAtG,iBAAiB,EAAE;cACnBE,iBAAiB,EAAE;YACvB;YACA,IAAIF,iBAAiB,GAAGwG,iBAAiB,IAAItG,iBAAiB,GAAGuG,iBAAiB,EAAE;cAChF,IAAI,CAACF,6BAA6B,CAACrH,MAAM,EAAE4G,iBAAiB,GAAG,CAAC,EAAE9F,iBAAiB,EAAEwG,iBAAiB,EAAET,iBAAiB,GAAG,CAAC,EAAE7F,iBAAiB,EAAEuG,iBAAiB,CAAC;YACxK;UACJ;QACJ;QACAX,iBAAiB,EAAE;QACnBC,iBAAiB,EAAE;MACvB;MACA,IAAIC,UAAU,EAAE;QACZ;QACA9G,MAAM,CAAC0B,IAAI,CAACwE,UAAU,CAACC,oBAAoB,CAAC,IAAI,CAACxG,0BAA0B,EAAEmH,UAAU,EAAE,IAAI,CAAC1F,QAAQ,EAAE,IAAI,CAACH,QAAQ,EAAE,IAAI,CAACqF,gBAAgB,EAAE,IAAI,CAACzG,wBAAwB,EAAE,IAAI,CAACE,4BAA4B,CAAC,CAAC;QAChN6G,iBAAiB,IAAIE,UAAU,CAACxB,cAAc;QAC9CuB,iBAAiB,IAAIC,UAAU,CAACtB,cAAc;MAClD;IACJ;IACA,OAAO;MACH3D,SAAS,EAAEA,SAAS;MACpB5B,OAAO,EAAED;IACb,CAAC;EACL;EACAqH,6BAA6BA,CAACrH,MAAM,EAAEwH,kBAAkB,EAAE3G,mBAAmB,EAAEC,iBAAiB,EAAE2G,kBAAkB,EAAE1G,mBAAmB,EAAEC,iBAAiB,EAAE;IAC1J,IAAI,IAAI,CAAC0G,8BAA8B,CAAC1H,MAAM,EAAEwH,kBAAkB,EAAE3G,mBAAmB,EAAEC,iBAAiB,EAAE2G,kBAAkB,EAAE1G,mBAAmB,EAAEC,iBAAiB,CAAC,EAAE;MACrK;MACA;IACJ;IACA,IAAIL,WAAW,GAAGa,SAAS;IAC3B,IAAI,IAAI,CAAC3B,wBAAwB,EAAE;MAC/Bc,WAAW,GAAG,CAAC,IAAIqE,UAAU,CAACwC,kBAAkB,EAAE3G,mBAAmB,EAAE2G,kBAAkB,EAAE1G,iBAAiB,EAAE2G,kBAAkB,EAAE1G,mBAAmB,EAAE0G,kBAAkB,EAAEzG,iBAAiB,CAAC,CAAC;IAClM;IACAhB,MAAM,CAAC0B,IAAI,CAAC,IAAIwE,UAAU,CAACsB,kBAAkB,EAAEA,kBAAkB,EAAEC,kBAAkB,EAAEA,kBAAkB,EAAE9G,WAAW,CAAC,CAAC;EAC5H;EACA+G,8BAA8BA,CAAC1H,MAAM,EAAEwH,kBAAkB,EAAE3G,mBAAmB,EAAEC,iBAAiB,EAAE2G,kBAAkB,EAAE1G,mBAAmB,EAAEC,iBAAiB,EAAE;IAC3J,MAAMiC,GAAG,GAAGjD,MAAM,CAAC0C,MAAM;IACzB,IAAIO,GAAG,KAAK,CAAC,EAAE;MACX,OAAO,KAAK;IAChB;IACA,MAAM0C,UAAU,GAAG3F,MAAM,CAACiD,GAAG,GAAG,CAAC,CAAC;IAClC,IAAI0C,UAAU,CAACtF,qBAAqB,KAAK,CAAC,IAAIsF,UAAU,CAACnF,qBAAqB,KAAK,CAAC,EAAE;MAClF;MACA,OAAO,KAAK;IAChB;IACA,IAAImF,UAAU,CAACtF,qBAAqB,KAAKmH,kBAAkB,IAAI7B,UAAU,CAACnF,qBAAqB,KAAKiH,kBAAkB,EAAE;MACpH,IAAI,IAAI,CAAC5H,wBAAwB,IAAI8F,UAAU,CAAChF,WAAW,EAAE;QACzDgF,UAAU,CAAChF,WAAW,CAACe,IAAI,CAAC,IAAIsD,UAAU,CAACwC,kBAAkB,EAAE3G,mBAAmB,EAAE2G,kBAAkB,EAAE1G,iBAAiB,EAAE2G,kBAAkB,EAAE1G,mBAAmB,EAAE0G,kBAAkB,EAAEzG,iBAAiB,CAAC,CAAC;MAC/M;MACA,OAAO,IAAI;IACf;IACA,IAAI2E,UAAU,CAACtF,qBAAqB,GAAG,CAAC,KAAKmH,kBAAkB,IAAI7B,UAAU,CAACnF,qBAAqB,GAAG,CAAC,KAAKiH,kBAAkB,EAAE;MAC5H9B,UAAU,CAACtF,qBAAqB,GAAGmH,kBAAkB;MACrD7B,UAAU,CAACnF,qBAAqB,GAAGiH,kBAAkB;MACrD,IAAI,IAAI,CAAC5H,wBAAwB,IAAI8F,UAAU,CAAChF,WAAW,EAAE;QACzDgF,UAAU,CAAChF,WAAW,CAACe,IAAI,CAAC,IAAIsD,UAAU,CAACwC,kBAAkB,EAAE3G,mBAAmB,EAAE2G,kBAAkB,EAAE1G,iBAAiB,EAAE2G,kBAAkB,EAAE1G,mBAAmB,EAAE0G,kBAAkB,EAAEzG,iBAAiB,CAAC,CAAC;MAC/M;MACA,OAAO,IAAI;IACf;IACA,OAAO,KAAK;EAChB;AACJ;AACA,SAAS2B,sBAAsBA,CAACgF,GAAG,EAAEC,YAAY,EAAE;EAC/C,MAAMC,CAAC,GAAGjJ,OAAO,CAACkJ,uBAAuB,CAACH,GAAG,CAAC;EAC9C,IAAIE,CAAC,KAAK,CAAC,CAAC,EAAE;IACV,OAAOD,YAAY;EACvB;EACA,OAAOC,CAAC,GAAG,CAAC;AAChB;AACA,SAASjF,qBAAqBA,CAAC+E,GAAG,EAAEC,YAAY,EAAE;EAC9C,MAAMC,CAAC,GAAGjJ,OAAO,CAACmJ,sBAAsB,CAACJ,GAAG,CAAC;EAC7C,IAAIE,CAAC,KAAK,CAAC,CAAC,EAAE;IACV,OAAOD,YAAY;EACvB;EACA,OAAOC,CAAC,GAAG,CAAC;AAChB;AACA,SAASpB,iCAAiCA,CAACuB,cAAc,EAAE;EACvD,IAAIA,cAAc,KAAK,CAAC,EAAE;IACtB,OAAO,MAAM,IAAI;EACrB;EACA,MAAMC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;EAC5B,OAAO,MAAM;IACT,OAAOD,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,SAAS,GAAGD,cAAc;EAClD,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}