{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport class TreeNode {\n  constructor(piece, color) {\n    this.piece = piece;\n    this.color = color;\n    this.size_left = 0;\n    this.lf_left = 0;\n    this.parent = this;\n    this.left = this;\n    this.right = this;\n  }\n  next() {\n    if (this.right !== SENTINEL) {\n      return leftest(this.right);\n    }\n    let node = this;\n    while (node.parent !== SENTINEL) {\n      if (node.parent.left === node) {\n        break;\n      }\n      node = node.parent;\n    }\n    if (node.parent === SENTINEL) {\n      return SENTINEL;\n    } else {\n      return node.parent;\n    }\n  }\n  prev() {\n    if (this.left !== SENTINEL) {\n      return righttest(this.left);\n    }\n    let node = this;\n    while (node.parent !== SENTINEL) {\n      if (node.parent.right === node) {\n        break;\n      }\n      node = node.parent;\n    }\n    if (node.parent === SENTINEL) {\n      return SENTINEL;\n    } else {\n      return node.parent;\n    }\n  }\n  detach() {\n    this.parent = null;\n    this.left = null;\n    this.right = null;\n  }\n}\nexport const SENTINEL = new TreeNode(null, 0 /* NodeColor.Black */);\nSENTINEL.parent = SENTINEL;\nSENTINEL.left = SENTINEL;\nSENTINEL.right = SENTINEL;\nSENTINEL.color = 0 /* NodeColor.Black */;\nexport function leftest(node) {\n  while (node.left !== SENTINEL) {\n    node = node.left;\n  }\n  return node;\n}\nexport function righttest(node) {\n  while (node.right !== SENTINEL) {\n    node = node.right;\n  }\n  return node;\n}\nfunction calculateSize(node) {\n  if (node === SENTINEL) {\n    return 0;\n  }\n  return node.size_left + node.piece.length + calculateSize(node.right);\n}\nfunction calculateLF(node) {\n  if (node === SENTINEL) {\n    return 0;\n  }\n  return node.lf_left + node.piece.lineFeedCnt + calculateLF(node.right);\n}\nfunction resetSentinel() {\n  SENTINEL.parent = SENTINEL;\n}\nexport function leftRotate(tree, x) {\n  const y = x.right;\n  // fix size_left\n  y.size_left += x.size_left + (x.piece ? x.piece.length : 0);\n  y.lf_left += x.lf_left + (x.piece ? x.piece.lineFeedCnt : 0);\n  x.right = y.left;\n  if (y.left !== SENTINEL) {\n    y.left.parent = x;\n  }\n  y.parent = x.parent;\n  if (x.parent === SENTINEL) {\n    tree.root = y;\n  } else if (x.parent.left === x) {\n    x.parent.left = y;\n  } else {\n    x.parent.right = y;\n  }\n  y.left = x;\n  x.parent = y;\n}\nexport function rightRotate(tree, y) {\n  const x = y.left;\n  y.left = x.right;\n  if (x.right !== SENTINEL) {\n    x.right.parent = y;\n  }\n  x.parent = y.parent;\n  // fix size_left\n  y.size_left -= x.size_left + (x.piece ? x.piece.length : 0);\n  y.lf_left -= x.lf_left + (x.piece ? x.piece.lineFeedCnt : 0);\n  if (y.parent === SENTINEL) {\n    tree.root = x;\n  } else if (y === y.parent.right) {\n    y.parent.right = x;\n  } else {\n    y.parent.left = x;\n  }\n  x.right = y;\n  y.parent = x;\n}\nexport function rbDelete(tree, z) {\n  let x;\n  let y;\n  if (z.left === SENTINEL) {\n    y = z;\n    x = y.right;\n  } else if (z.right === SENTINEL) {\n    y = z;\n    x = y.left;\n  } else {\n    y = leftest(z.right);\n    x = y.right;\n  }\n  if (y === tree.root) {\n    tree.root = x;\n    // if x is null, we are removing the only node\n    x.color = 0 /* NodeColor.Black */;\n    z.detach();\n    resetSentinel();\n    tree.root.parent = SENTINEL;\n    return;\n  }\n  const yWasRed = y.color === 1 /* NodeColor.Red */;\n  if (y === y.parent.left) {\n    y.parent.left = x;\n  } else {\n    y.parent.right = x;\n  }\n  if (y === z) {\n    x.parent = y.parent;\n    recomputeTreeMetadata(tree, x);\n  } else {\n    if (y.parent === z) {\n      x.parent = y;\n    } else {\n      x.parent = y.parent;\n    }\n    // as we make changes to x's hierarchy, update size_left of subtree first\n    recomputeTreeMetadata(tree, x);\n    y.left = z.left;\n    y.right = z.right;\n    y.parent = z.parent;\n    y.color = z.color;\n    if (z === tree.root) {\n      tree.root = y;\n    } else {\n      if (z === z.parent.left) {\n        z.parent.left = y;\n      } else {\n        z.parent.right = y;\n      }\n    }\n    if (y.left !== SENTINEL) {\n      y.left.parent = y;\n    }\n    if (y.right !== SENTINEL) {\n      y.right.parent = y;\n    }\n    // update metadata\n    // we replace z with y, so in this sub tree, the length change is z.item.length\n    y.size_left = z.size_left;\n    y.lf_left = z.lf_left;\n    recomputeTreeMetadata(tree, y);\n  }\n  z.detach();\n  if (x.parent.left === x) {\n    const newSizeLeft = calculateSize(x);\n    const newLFLeft = calculateLF(x);\n    if (newSizeLeft !== x.parent.size_left || newLFLeft !== x.parent.lf_left) {\n      const delta = newSizeLeft - x.parent.size_left;\n      const lf_delta = newLFLeft - x.parent.lf_left;\n      x.parent.size_left = newSizeLeft;\n      x.parent.lf_left = newLFLeft;\n      updateTreeMetadata(tree, x.parent, delta, lf_delta);\n    }\n  }\n  recomputeTreeMetadata(tree, x.parent);\n  if (yWasRed) {\n    resetSentinel();\n    return;\n  }\n  // RB-DELETE-FIXUP\n  let w;\n  while (x !== tree.root && x.color === 0 /* NodeColor.Black */) {\n    if (x === x.parent.left) {\n      w = x.parent.right;\n      if (w.color === 1 /* NodeColor.Red */) {\n        w.color = 0 /* NodeColor.Black */;\n        x.parent.color = 1 /* NodeColor.Red */;\n        leftRotate(tree, x.parent);\n        w = x.parent.right;\n      }\n      if (w.left.color === 0 /* NodeColor.Black */ && w.right.color === 0 /* NodeColor.Black */) {\n        w.color = 1 /* NodeColor.Red */;\n        x = x.parent;\n      } else {\n        if (w.right.color === 0 /* NodeColor.Black */) {\n          w.left.color = 0 /* NodeColor.Black */;\n          w.color = 1 /* NodeColor.Red */;\n          rightRotate(tree, w);\n          w = x.parent.right;\n        }\n        w.color = x.parent.color;\n        x.parent.color = 0 /* NodeColor.Black */;\n        w.right.color = 0 /* NodeColor.Black */;\n        leftRotate(tree, x.parent);\n        x = tree.root;\n      }\n    } else {\n      w = x.parent.left;\n      if (w.color === 1 /* NodeColor.Red */) {\n        w.color = 0 /* NodeColor.Black */;\n        x.parent.color = 1 /* NodeColor.Red */;\n        rightRotate(tree, x.parent);\n        w = x.parent.left;\n      }\n      if (w.left.color === 0 /* NodeColor.Black */ && w.right.color === 0 /* NodeColor.Black */) {\n        w.color = 1 /* NodeColor.Red */;\n        x = x.parent;\n      } else {\n        if (w.left.color === 0 /* NodeColor.Black */) {\n          w.right.color = 0 /* NodeColor.Black */;\n          w.color = 1 /* NodeColor.Red */;\n          leftRotate(tree, w);\n          w = x.parent.left;\n        }\n        w.color = x.parent.color;\n        x.parent.color = 0 /* NodeColor.Black */;\n        w.left.color = 0 /* NodeColor.Black */;\n        rightRotate(tree, x.parent);\n        x = tree.root;\n      }\n    }\n  }\n  x.color = 0 /* NodeColor.Black */;\n  resetSentinel();\n}\nexport function fixInsert(tree, x) {\n  recomputeTreeMetadata(tree, x);\n  while (x !== tree.root && x.parent.color === 1 /* NodeColor.Red */) {\n    if (x.parent === x.parent.parent.left) {\n      const y = x.parent.parent.right;\n      if (y.color === 1 /* NodeColor.Red */) {\n        x.parent.color = 0 /* NodeColor.Black */;\n        y.color = 0 /* NodeColor.Black */;\n        x.parent.parent.color = 1 /* NodeColor.Red */;\n        x = x.parent.parent;\n      } else {\n        if (x === x.parent.right) {\n          x = x.parent;\n          leftRotate(tree, x);\n        }\n        x.parent.color = 0 /* NodeColor.Black */;\n        x.parent.parent.color = 1 /* NodeColor.Red */;\n        rightRotate(tree, x.parent.parent);\n      }\n    } else {\n      const y = x.parent.parent.left;\n      if (y.color === 1 /* NodeColor.Red */) {\n        x.parent.color = 0 /* NodeColor.Black */;\n        y.color = 0 /* NodeColor.Black */;\n        x.parent.parent.color = 1 /* NodeColor.Red */;\n        x = x.parent.parent;\n      } else {\n        if (x === x.parent.left) {\n          x = x.parent;\n          rightRotate(tree, x);\n        }\n        x.parent.color = 0 /* NodeColor.Black */;\n        x.parent.parent.color = 1 /* NodeColor.Red */;\n        leftRotate(tree, x.parent.parent);\n      }\n    }\n  }\n  tree.root.color = 0 /* NodeColor.Black */;\n}\nexport function updateTreeMetadata(tree, x, delta, lineFeedCntDelta) {\n  // node length change or line feed count change\n  while (x !== tree.root && x !== SENTINEL) {\n    if (x.parent.left === x) {\n      x.parent.size_left += delta;\n      x.parent.lf_left += lineFeedCntDelta;\n    }\n    x = x.parent;\n  }\n}\nexport function recomputeTreeMetadata(tree, x) {\n  let delta = 0;\n  let lf_delta = 0;\n  if (x === tree.root) {\n    return;\n  }\n  // go upwards till the node whose left subtree is changed.\n  while (x !== tree.root && x === x.parent.right) {\n    x = x.parent;\n  }\n  if (x === tree.root) {\n    // well, it means we add a node to the end (inorder)\n    return;\n  }\n  // x is the node whose right subtree is changed.\n  x = x.parent;\n  delta = calculateSize(x.left) - x.size_left;\n  lf_delta = calculateLF(x.left) - x.lf_left;\n  x.size_left += delta;\n  x.lf_left += lf_delta;\n  // go upwards till root. O(logN)\n  while (x !== tree.root && (delta !== 0 || lf_delta !== 0)) {\n    if (x.parent.left === x) {\n      x.parent.size_left += delta;\n      x.parent.lf_left += lf_delta;\n    }\n    x = x.parent;\n  }\n}", "map": {"version": 3, "names": ["TreeNode", "constructor", "piece", "color", "size_left", "lf_left", "parent", "left", "right", "next", "SENTINEL", "leftest", "node", "prev", "righttest", "detach", "calculateSize", "length", "calculateLF", "lineFeedCnt", "resetSentinel", "leftRotate", "tree", "x", "y", "root", "rightRotate", "rbDelete", "z", "yWasRed", "recomputeTreeMetadata", "newSizeLeft", "newLFLeft", "delta", "lf_delta", "updateTreeMetadata", "w", "fixInsert", "lineFeedCntDelta"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/model/pieceTreeTextBuffer/rbTreeBase.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport class TreeNode {\n    constructor(piece, color) {\n        this.piece = piece;\n        this.color = color;\n        this.size_left = 0;\n        this.lf_left = 0;\n        this.parent = this;\n        this.left = this;\n        this.right = this;\n    }\n    next() {\n        if (this.right !== SENTINEL) {\n            return leftest(this.right);\n        }\n        let node = this;\n        while (node.parent !== SENTINEL) {\n            if (node.parent.left === node) {\n                break;\n            }\n            node = node.parent;\n        }\n        if (node.parent === SENTINEL) {\n            return SENTINEL;\n        }\n        else {\n            return node.parent;\n        }\n    }\n    prev() {\n        if (this.left !== SENTINEL) {\n            return righttest(this.left);\n        }\n        let node = this;\n        while (node.parent !== SENTINEL) {\n            if (node.parent.right === node) {\n                break;\n            }\n            node = node.parent;\n        }\n        if (node.parent === SENTINEL) {\n            return SENTINEL;\n        }\n        else {\n            return node.parent;\n        }\n    }\n    detach() {\n        this.parent = null;\n        this.left = null;\n        this.right = null;\n    }\n}\nexport const SENTINEL = new TreeNode(null, 0 /* NodeColor.Black */);\nSENTINEL.parent = SENTINEL;\nSENTINEL.left = SENTINEL;\nSENTINEL.right = SENTINEL;\nSENTINEL.color = 0 /* NodeColor.Black */;\nexport function leftest(node) {\n    while (node.left !== SENTINEL) {\n        node = node.left;\n    }\n    return node;\n}\nexport function righttest(node) {\n    while (node.right !== SENTINEL) {\n        node = node.right;\n    }\n    return node;\n}\nfunction calculateSize(node) {\n    if (node === SENTINEL) {\n        return 0;\n    }\n    return node.size_left + node.piece.length + calculateSize(node.right);\n}\nfunction calculateLF(node) {\n    if (node === SENTINEL) {\n        return 0;\n    }\n    return node.lf_left + node.piece.lineFeedCnt + calculateLF(node.right);\n}\nfunction resetSentinel() {\n    SENTINEL.parent = SENTINEL;\n}\nexport function leftRotate(tree, x) {\n    const y = x.right;\n    // fix size_left\n    y.size_left += x.size_left + (x.piece ? x.piece.length : 0);\n    y.lf_left += x.lf_left + (x.piece ? x.piece.lineFeedCnt : 0);\n    x.right = y.left;\n    if (y.left !== SENTINEL) {\n        y.left.parent = x;\n    }\n    y.parent = x.parent;\n    if (x.parent === SENTINEL) {\n        tree.root = y;\n    }\n    else if (x.parent.left === x) {\n        x.parent.left = y;\n    }\n    else {\n        x.parent.right = y;\n    }\n    y.left = x;\n    x.parent = y;\n}\nexport function rightRotate(tree, y) {\n    const x = y.left;\n    y.left = x.right;\n    if (x.right !== SENTINEL) {\n        x.right.parent = y;\n    }\n    x.parent = y.parent;\n    // fix size_left\n    y.size_left -= x.size_left + (x.piece ? x.piece.length : 0);\n    y.lf_left -= x.lf_left + (x.piece ? x.piece.lineFeedCnt : 0);\n    if (y.parent === SENTINEL) {\n        tree.root = x;\n    }\n    else if (y === y.parent.right) {\n        y.parent.right = x;\n    }\n    else {\n        y.parent.left = x;\n    }\n    x.right = y;\n    y.parent = x;\n}\nexport function rbDelete(tree, z) {\n    let x;\n    let y;\n    if (z.left === SENTINEL) {\n        y = z;\n        x = y.right;\n    }\n    else if (z.right === SENTINEL) {\n        y = z;\n        x = y.left;\n    }\n    else {\n        y = leftest(z.right);\n        x = y.right;\n    }\n    if (y === tree.root) {\n        tree.root = x;\n        // if x is null, we are removing the only node\n        x.color = 0 /* NodeColor.Black */;\n        z.detach();\n        resetSentinel();\n        tree.root.parent = SENTINEL;\n        return;\n    }\n    const yWasRed = (y.color === 1 /* NodeColor.Red */);\n    if (y === y.parent.left) {\n        y.parent.left = x;\n    }\n    else {\n        y.parent.right = x;\n    }\n    if (y === z) {\n        x.parent = y.parent;\n        recomputeTreeMetadata(tree, x);\n    }\n    else {\n        if (y.parent === z) {\n            x.parent = y;\n        }\n        else {\n            x.parent = y.parent;\n        }\n        // as we make changes to x's hierarchy, update size_left of subtree first\n        recomputeTreeMetadata(tree, x);\n        y.left = z.left;\n        y.right = z.right;\n        y.parent = z.parent;\n        y.color = z.color;\n        if (z === tree.root) {\n            tree.root = y;\n        }\n        else {\n            if (z === z.parent.left) {\n                z.parent.left = y;\n            }\n            else {\n                z.parent.right = y;\n            }\n        }\n        if (y.left !== SENTINEL) {\n            y.left.parent = y;\n        }\n        if (y.right !== SENTINEL) {\n            y.right.parent = y;\n        }\n        // update metadata\n        // we replace z with y, so in this sub tree, the length change is z.item.length\n        y.size_left = z.size_left;\n        y.lf_left = z.lf_left;\n        recomputeTreeMetadata(tree, y);\n    }\n    z.detach();\n    if (x.parent.left === x) {\n        const newSizeLeft = calculateSize(x);\n        const newLFLeft = calculateLF(x);\n        if (newSizeLeft !== x.parent.size_left || newLFLeft !== x.parent.lf_left) {\n            const delta = newSizeLeft - x.parent.size_left;\n            const lf_delta = newLFLeft - x.parent.lf_left;\n            x.parent.size_left = newSizeLeft;\n            x.parent.lf_left = newLFLeft;\n            updateTreeMetadata(tree, x.parent, delta, lf_delta);\n        }\n    }\n    recomputeTreeMetadata(tree, x.parent);\n    if (yWasRed) {\n        resetSentinel();\n        return;\n    }\n    // RB-DELETE-FIXUP\n    let w;\n    while (x !== tree.root && x.color === 0 /* NodeColor.Black */) {\n        if (x === x.parent.left) {\n            w = x.parent.right;\n            if (w.color === 1 /* NodeColor.Red */) {\n                w.color = 0 /* NodeColor.Black */;\n                x.parent.color = 1 /* NodeColor.Red */;\n                leftRotate(tree, x.parent);\n                w = x.parent.right;\n            }\n            if (w.left.color === 0 /* NodeColor.Black */ && w.right.color === 0 /* NodeColor.Black */) {\n                w.color = 1 /* NodeColor.Red */;\n                x = x.parent;\n            }\n            else {\n                if (w.right.color === 0 /* NodeColor.Black */) {\n                    w.left.color = 0 /* NodeColor.Black */;\n                    w.color = 1 /* NodeColor.Red */;\n                    rightRotate(tree, w);\n                    w = x.parent.right;\n                }\n                w.color = x.parent.color;\n                x.parent.color = 0 /* NodeColor.Black */;\n                w.right.color = 0 /* NodeColor.Black */;\n                leftRotate(tree, x.parent);\n                x = tree.root;\n            }\n        }\n        else {\n            w = x.parent.left;\n            if (w.color === 1 /* NodeColor.Red */) {\n                w.color = 0 /* NodeColor.Black */;\n                x.parent.color = 1 /* NodeColor.Red */;\n                rightRotate(tree, x.parent);\n                w = x.parent.left;\n            }\n            if (w.left.color === 0 /* NodeColor.Black */ && w.right.color === 0 /* NodeColor.Black */) {\n                w.color = 1 /* NodeColor.Red */;\n                x = x.parent;\n            }\n            else {\n                if (w.left.color === 0 /* NodeColor.Black */) {\n                    w.right.color = 0 /* NodeColor.Black */;\n                    w.color = 1 /* NodeColor.Red */;\n                    leftRotate(tree, w);\n                    w = x.parent.left;\n                }\n                w.color = x.parent.color;\n                x.parent.color = 0 /* NodeColor.Black */;\n                w.left.color = 0 /* NodeColor.Black */;\n                rightRotate(tree, x.parent);\n                x = tree.root;\n            }\n        }\n    }\n    x.color = 0 /* NodeColor.Black */;\n    resetSentinel();\n}\nexport function fixInsert(tree, x) {\n    recomputeTreeMetadata(tree, x);\n    while (x !== tree.root && x.parent.color === 1 /* NodeColor.Red */) {\n        if (x.parent === x.parent.parent.left) {\n            const y = x.parent.parent.right;\n            if (y.color === 1 /* NodeColor.Red */) {\n                x.parent.color = 0 /* NodeColor.Black */;\n                y.color = 0 /* NodeColor.Black */;\n                x.parent.parent.color = 1 /* NodeColor.Red */;\n                x = x.parent.parent;\n            }\n            else {\n                if (x === x.parent.right) {\n                    x = x.parent;\n                    leftRotate(tree, x);\n                }\n                x.parent.color = 0 /* NodeColor.Black */;\n                x.parent.parent.color = 1 /* NodeColor.Red */;\n                rightRotate(tree, x.parent.parent);\n            }\n        }\n        else {\n            const y = x.parent.parent.left;\n            if (y.color === 1 /* NodeColor.Red */) {\n                x.parent.color = 0 /* NodeColor.Black */;\n                y.color = 0 /* NodeColor.Black */;\n                x.parent.parent.color = 1 /* NodeColor.Red */;\n                x = x.parent.parent;\n            }\n            else {\n                if (x === x.parent.left) {\n                    x = x.parent;\n                    rightRotate(tree, x);\n                }\n                x.parent.color = 0 /* NodeColor.Black */;\n                x.parent.parent.color = 1 /* NodeColor.Red */;\n                leftRotate(tree, x.parent.parent);\n            }\n        }\n    }\n    tree.root.color = 0 /* NodeColor.Black */;\n}\nexport function updateTreeMetadata(tree, x, delta, lineFeedCntDelta) {\n    // node length change or line feed count change\n    while (x !== tree.root && x !== SENTINEL) {\n        if (x.parent.left === x) {\n            x.parent.size_left += delta;\n            x.parent.lf_left += lineFeedCntDelta;\n        }\n        x = x.parent;\n    }\n}\nexport function recomputeTreeMetadata(tree, x) {\n    let delta = 0;\n    let lf_delta = 0;\n    if (x === tree.root) {\n        return;\n    }\n    // go upwards till the node whose left subtree is changed.\n    while (x !== tree.root && x === x.parent.right) {\n        x = x.parent;\n    }\n    if (x === tree.root) {\n        // well, it means we add a node to the end (inorder)\n        return;\n    }\n    // x is the node whose right subtree is changed.\n    x = x.parent;\n    delta = calculateSize(x.left) - x.size_left;\n    lf_delta = calculateLF(x.left) - x.lf_left;\n    x.size_left += delta;\n    x.lf_left += lf_delta;\n    // go upwards till root. O(logN)\n    while (x !== tree.root && (delta !== 0 || lf_delta !== 0)) {\n        if (x.parent.left === x) {\n            x.parent.size_left += delta;\n            x.parent.lf_left += lf_delta;\n        }\n        x = x.parent;\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,MAAMA,QAAQ,CAAC;EAClBC,WAAWA,CAACC,KAAK,EAAEC,KAAK,EAAE;IACtB,IAAI,CAACD,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,SAAS,GAAG,CAAC;IAClB,IAAI,CAACC,OAAO,GAAG,CAAC;IAChB,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,IAAI,GAAG,IAAI;IAChB,IAAI,CAACC,KAAK,GAAG,IAAI;EACrB;EACAC,IAAIA,CAAA,EAAG;IACH,IAAI,IAAI,CAACD,KAAK,KAAKE,QAAQ,EAAE;MACzB,OAAOC,OAAO,CAAC,IAAI,CAACH,KAAK,CAAC;IAC9B;IACA,IAAII,IAAI,GAAG,IAAI;IACf,OAAOA,IAAI,CAACN,MAAM,KAAKI,QAAQ,EAAE;MAC7B,IAAIE,IAAI,CAACN,MAAM,CAACC,IAAI,KAAKK,IAAI,EAAE;QAC3B;MACJ;MACAA,IAAI,GAAGA,IAAI,CAACN,MAAM;IACtB;IACA,IAAIM,IAAI,CAACN,MAAM,KAAKI,QAAQ,EAAE;MAC1B,OAAOA,QAAQ;IACnB,CAAC,MACI;MACD,OAAOE,IAAI,CAACN,MAAM;IACtB;EACJ;EACAO,IAAIA,CAAA,EAAG;IACH,IAAI,IAAI,CAACN,IAAI,KAAKG,QAAQ,EAAE;MACxB,OAAOI,SAAS,CAAC,IAAI,CAACP,IAAI,CAAC;IAC/B;IACA,IAAIK,IAAI,GAAG,IAAI;IACf,OAAOA,IAAI,CAACN,MAAM,KAAKI,QAAQ,EAAE;MAC7B,IAAIE,IAAI,CAACN,MAAM,CAACE,KAAK,KAAKI,IAAI,EAAE;QAC5B;MACJ;MACAA,IAAI,GAAGA,IAAI,CAACN,MAAM;IACtB;IACA,IAAIM,IAAI,CAACN,MAAM,KAAKI,QAAQ,EAAE;MAC1B,OAAOA,QAAQ;IACnB,CAAC,MACI;MACD,OAAOE,IAAI,CAACN,MAAM;IACtB;EACJ;EACAS,MAAMA,CAAA,EAAG;IACL,IAAI,CAACT,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,IAAI,GAAG,IAAI;IAChB,IAAI,CAACC,KAAK,GAAG,IAAI;EACrB;AACJ;AACA,OAAO,MAAME,QAAQ,GAAG,IAAIV,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,qBAAqB,CAAC;AACnEU,QAAQ,CAACJ,MAAM,GAAGI,QAAQ;AAC1BA,QAAQ,CAACH,IAAI,GAAGG,QAAQ;AACxBA,QAAQ,CAACF,KAAK,GAAGE,QAAQ;AACzBA,QAAQ,CAACP,KAAK,GAAG,CAAC,CAAC;AACnB,OAAO,SAASQ,OAAOA,CAACC,IAAI,EAAE;EAC1B,OAAOA,IAAI,CAACL,IAAI,KAAKG,QAAQ,EAAE;IAC3BE,IAAI,GAAGA,IAAI,CAACL,IAAI;EACpB;EACA,OAAOK,IAAI;AACf;AACA,OAAO,SAASE,SAASA,CAACF,IAAI,EAAE;EAC5B,OAAOA,IAAI,CAACJ,KAAK,KAAKE,QAAQ,EAAE;IAC5BE,IAAI,GAAGA,IAAI,CAACJ,KAAK;EACrB;EACA,OAAOI,IAAI;AACf;AACA,SAASI,aAAaA,CAACJ,IAAI,EAAE;EACzB,IAAIA,IAAI,KAAKF,QAAQ,EAAE;IACnB,OAAO,CAAC;EACZ;EACA,OAAOE,IAAI,CAACR,SAAS,GAAGQ,IAAI,CAACV,KAAK,CAACe,MAAM,GAAGD,aAAa,CAACJ,IAAI,CAACJ,KAAK,CAAC;AACzE;AACA,SAASU,WAAWA,CAACN,IAAI,EAAE;EACvB,IAAIA,IAAI,KAAKF,QAAQ,EAAE;IACnB,OAAO,CAAC;EACZ;EACA,OAAOE,IAAI,CAACP,OAAO,GAAGO,IAAI,CAACV,KAAK,CAACiB,WAAW,GAAGD,WAAW,CAACN,IAAI,CAACJ,KAAK,CAAC;AAC1E;AACA,SAASY,aAAaA,CAAA,EAAG;EACrBV,QAAQ,CAACJ,MAAM,GAAGI,QAAQ;AAC9B;AACA,OAAO,SAASW,UAAUA,CAACC,IAAI,EAAEC,CAAC,EAAE;EAChC,MAAMC,CAAC,GAAGD,CAAC,CAACf,KAAK;EACjB;EACAgB,CAAC,CAACpB,SAAS,IAAImB,CAAC,CAACnB,SAAS,IAAImB,CAAC,CAACrB,KAAK,GAAGqB,CAAC,CAACrB,KAAK,CAACe,MAAM,GAAG,CAAC,CAAC;EAC3DO,CAAC,CAACnB,OAAO,IAAIkB,CAAC,CAAClB,OAAO,IAAIkB,CAAC,CAACrB,KAAK,GAAGqB,CAAC,CAACrB,KAAK,CAACiB,WAAW,GAAG,CAAC,CAAC;EAC5DI,CAAC,CAACf,KAAK,GAAGgB,CAAC,CAACjB,IAAI;EAChB,IAAIiB,CAAC,CAACjB,IAAI,KAAKG,QAAQ,EAAE;IACrBc,CAAC,CAACjB,IAAI,CAACD,MAAM,GAAGiB,CAAC;EACrB;EACAC,CAAC,CAAClB,MAAM,GAAGiB,CAAC,CAACjB,MAAM;EACnB,IAAIiB,CAAC,CAACjB,MAAM,KAAKI,QAAQ,EAAE;IACvBY,IAAI,CAACG,IAAI,GAAGD,CAAC;EACjB,CAAC,MACI,IAAID,CAAC,CAACjB,MAAM,CAACC,IAAI,KAAKgB,CAAC,EAAE;IAC1BA,CAAC,CAACjB,MAAM,CAACC,IAAI,GAAGiB,CAAC;EACrB,CAAC,MACI;IACDD,CAAC,CAACjB,MAAM,CAACE,KAAK,GAAGgB,CAAC;EACtB;EACAA,CAAC,CAACjB,IAAI,GAAGgB,CAAC;EACVA,CAAC,CAACjB,MAAM,GAAGkB,CAAC;AAChB;AACA,OAAO,SAASE,WAAWA,CAACJ,IAAI,EAAEE,CAAC,EAAE;EACjC,MAAMD,CAAC,GAAGC,CAAC,CAACjB,IAAI;EAChBiB,CAAC,CAACjB,IAAI,GAAGgB,CAAC,CAACf,KAAK;EAChB,IAAIe,CAAC,CAACf,KAAK,KAAKE,QAAQ,EAAE;IACtBa,CAAC,CAACf,KAAK,CAACF,MAAM,GAAGkB,CAAC;EACtB;EACAD,CAAC,CAACjB,MAAM,GAAGkB,CAAC,CAAClB,MAAM;EACnB;EACAkB,CAAC,CAACpB,SAAS,IAAImB,CAAC,CAACnB,SAAS,IAAImB,CAAC,CAACrB,KAAK,GAAGqB,CAAC,CAACrB,KAAK,CAACe,MAAM,GAAG,CAAC,CAAC;EAC3DO,CAAC,CAACnB,OAAO,IAAIkB,CAAC,CAAClB,OAAO,IAAIkB,CAAC,CAACrB,KAAK,GAAGqB,CAAC,CAACrB,KAAK,CAACiB,WAAW,GAAG,CAAC,CAAC;EAC5D,IAAIK,CAAC,CAAClB,MAAM,KAAKI,QAAQ,EAAE;IACvBY,IAAI,CAACG,IAAI,GAAGF,CAAC;EACjB,CAAC,MACI,IAAIC,CAAC,KAAKA,CAAC,CAAClB,MAAM,CAACE,KAAK,EAAE;IAC3BgB,CAAC,CAAClB,MAAM,CAACE,KAAK,GAAGe,CAAC;EACtB,CAAC,MACI;IACDC,CAAC,CAAClB,MAAM,CAACC,IAAI,GAAGgB,CAAC;EACrB;EACAA,CAAC,CAACf,KAAK,GAAGgB,CAAC;EACXA,CAAC,CAAClB,MAAM,GAAGiB,CAAC;AAChB;AACA,OAAO,SAASI,QAAQA,CAACL,IAAI,EAAEM,CAAC,EAAE;EAC9B,IAAIL,CAAC;EACL,IAAIC,CAAC;EACL,IAAII,CAAC,CAACrB,IAAI,KAAKG,QAAQ,EAAE;IACrBc,CAAC,GAAGI,CAAC;IACLL,CAAC,GAAGC,CAAC,CAAChB,KAAK;EACf,CAAC,MACI,IAAIoB,CAAC,CAACpB,KAAK,KAAKE,QAAQ,EAAE;IAC3Bc,CAAC,GAAGI,CAAC;IACLL,CAAC,GAAGC,CAAC,CAACjB,IAAI;EACd,CAAC,MACI;IACDiB,CAAC,GAAGb,OAAO,CAACiB,CAAC,CAACpB,KAAK,CAAC;IACpBe,CAAC,GAAGC,CAAC,CAAChB,KAAK;EACf;EACA,IAAIgB,CAAC,KAAKF,IAAI,CAACG,IAAI,EAAE;IACjBH,IAAI,CAACG,IAAI,GAAGF,CAAC;IACb;IACAA,CAAC,CAACpB,KAAK,GAAG,CAAC,CAAC;IACZyB,CAAC,CAACb,MAAM,CAAC,CAAC;IACVK,aAAa,CAAC,CAAC;IACfE,IAAI,CAACG,IAAI,CAACnB,MAAM,GAAGI,QAAQ;IAC3B;EACJ;EACA,MAAMmB,OAAO,GAAIL,CAAC,CAACrB,KAAK,KAAK,CAAC,CAAC,mBAAoB;EACnD,IAAIqB,CAAC,KAAKA,CAAC,CAAClB,MAAM,CAACC,IAAI,EAAE;IACrBiB,CAAC,CAAClB,MAAM,CAACC,IAAI,GAAGgB,CAAC;EACrB,CAAC,MACI;IACDC,CAAC,CAAClB,MAAM,CAACE,KAAK,GAAGe,CAAC;EACtB;EACA,IAAIC,CAAC,KAAKI,CAAC,EAAE;IACTL,CAAC,CAACjB,MAAM,GAAGkB,CAAC,CAAClB,MAAM;IACnBwB,qBAAqB,CAACR,IAAI,EAAEC,CAAC,CAAC;EAClC,CAAC,MACI;IACD,IAAIC,CAAC,CAAClB,MAAM,KAAKsB,CAAC,EAAE;MAChBL,CAAC,CAACjB,MAAM,GAAGkB,CAAC;IAChB,CAAC,MACI;MACDD,CAAC,CAACjB,MAAM,GAAGkB,CAAC,CAAClB,MAAM;IACvB;IACA;IACAwB,qBAAqB,CAACR,IAAI,EAAEC,CAAC,CAAC;IAC9BC,CAAC,CAACjB,IAAI,GAAGqB,CAAC,CAACrB,IAAI;IACfiB,CAAC,CAAChB,KAAK,GAAGoB,CAAC,CAACpB,KAAK;IACjBgB,CAAC,CAAClB,MAAM,GAAGsB,CAAC,CAACtB,MAAM;IACnBkB,CAAC,CAACrB,KAAK,GAAGyB,CAAC,CAACzB,KAAK;IACjB,IAAIyB,CAAC,KAAKN,IAAI,CAACG,IAAI,EAAE;MACjBH,IAAI,CAACG,IAAI,GAAGD,CAAC;IACjB,CAAC,MACI;MACD,IAAII,CAAC,KAAKA,CAAC,CAACtB,MAAM,CAACC,IAAI,EAAE;QACrBqB,CAAC,CAACtB,MAAM,CAACC,IAAI,GAAGiB,CAAC;MACrB,CAAC,MACI;QACDI,CAAC,CAACtB,MAAM,CAACE,KAAK,GAAGgB,CAAC;MACtB;IACJ;IACA,IAAIA,CAAC,CAACjB,IAAI,KAAKG,QAAQ,EAAE;MACrBc,CAAC,CAACjB,IAAI,CAACD,MAAM,GAAGkB,CAAC;IACrB;IACA,IAAIA,CAAC,CAAChB,KAAK,KAAKE,QAAQ,EAAE;MACtBc,CAAC,CAAChB,KAAK,CAACF,MAAM,GAAGkB,CAAC;IACtB;IACA;IACA;IACAA,CAAC,CAACpB,SAAS,GAAGwB,CAAC,CAACxB,SAAS;IACzBoB,CAAC,CAACnB,OAAO,GAAGuB,CAAC,CAACvB,OAAO;IACrByB,qBAAqB,CAACR,IAAI,EAAEE,CAAC,CAAC;EAClC;EACAI,CAAC,CAACb,MAAM,CAAC,CAAC;EACV,IAAIQ,CAAC,CAACjB,MAAM,CAACC,IAAI,KAAKgB,CAAC,EAAE;IACrB,MAAMQ,WAAW,GAAGf,aAAa,CAACO,CAAC,CAAC;IACpC,MAAMS,SAAS,GAAGd,WAAW,CAACK,CAAC,CAAC;IAChC,IAAIQ,WAAW,KAAKR,CAAC,CAACjB,MAAM,CAACF,SAAS,IAAI4B,SAAS,KAAKT,CAAC,CAACjB,MAAM,CAACD,OAAO,EAAE;MACtE,MAAM4B,KAAK,GAAGF,WAAW,GAAGR,CAAC,CAACjB,MAAM,CAACF,SAAS;MAC9C,MAAM8B,QAAQ,GAAGF,SAAS,GAAGT,CAAC,CAACjB,MAAM,CAACD,OAAO;MAC7CkB,CAAC,CAACjB,MAAM,CAACF,SAAS,GAAG2B,WAAW;MAChCR,CAAC,CAACjB,MAAM,CAACD,OAAO,GAAG2B,SAAS;MAC5BG,kBAAkB,CAACb,IAAI,EAAEC,CAAC,CAACjB,MAAM,EAAE2B,KAAK,EAAEC,QAAQ,CAAC;IACvD;EACJ;EACAJ,qBAAqB,CAACR,IAAI,EAAEC,CAAC,CAACjB,MAAM,CAAC;EACrC,IAAIuB,OAAO,EAAE;IACTT,aAAa,CAAC,CAAC;IACf;EACJ;EACA;EACA,IAAIgB,CAAC;EACL,OAAOb,CAAC,KAAKD,IAAI,CAACG,IAAI,IAAIF,CAAC,CAACpB,KAAK,KAAK,CAAC,CAAC,uBAAuB;IAC3D,IAAIoB,CAAC,KAAKA,CAAC,CAACjB,MAAM,CAACC,IAAI,EAAE;MACrB6B,CAAC,GAAGb,CAAC,CAACjB,MAAM,CAACE,KAAK;MAClB,IAAI4B,CAAC,CAACjC,KAAK,KAAK,CAAC,CAAC,qBAAqB;QACnCiC,CAAC,CAACjC,KAAK,GAAG,CAAC,CAAC;QACZoB,CAAC,CAACjB,MAAM,CAACH,KAAK,GAAG,CAAC,CAAC;QACnBkB,UAAU,CAACC,IAAI,EAAEC,CAAC,CAACjB,MAAM,CAAC;QAC1B8B,CAAC,GAAGb,CAAC,CAACjB,MAAM,CAACE,KAAK;MACtB;MACA,IAAI4B,CAAC,CAAC7B,IAAI,CAACJ,KAAK,KAAK,CAAC,CAAC,yBAAyBiC,CAAC,CAAC5B,KAAK,CAACL,KAAK,KAAK,CAAC,CAAC,uBAAuB;QACvFiC,CAAC,CAACjC,KAAK,GAAG,CAAC,CAAC;QACZoB,CAAC,GAAGA,CAAC,CAACjB,MAAM;MAChB,CAAC,MACI;QACD,IAAI8B,CAAC,CAAC5B,KAAK,CAACL,KAAK,KAAK,CAAC,CAAC,uBAAuB;UAC3CiC,CAAC,CAAC7B,IAAI,CAACJ,KAAK,GAAG,CAAC,CAAC;UACjBiC,CAAC,CAACjC,KAAK,GAAG,CAAC,CAAC;UACZuB,WAAW,CAACJ,IAAI,EAAEc,CAAC,CAAC;UACpBA,CAAC,GAAGb,CAAC,CAACjB,MAAM,CAACE,KAAK;QACtB;QACA4B,CAAC,CAACjC,KAAK,GAAGoB,CAAC,CAACjB,MAAM,CAACH,KAAK;QACxBoB,CAAC,CAACjB,MAAM,CAACH,KAAK,GAAG,CAAC,CAAC;QACnBiC,CAAC,CAAC5B,KAAK,CAACL,KAAK,GAAG,CAAC,CAAC;QAClBkB,UAAU,CAACC,IAAI,EAAEC,CAAC,CAACjB,MAAM,CAAC;QAC1BiB,CAAC,GAAGD,IAAI,CAACG,IAAI;MACjB;IACJ,CAAC,MACI;MACDW,CAAC,GAAGb,CAAC,CAACjB,MAAM,CAACC,IAAI;MACjB,IAAI6B,CAAC,CAACjC,KAAK,KAAK,CAAC,CAAC,qBAAqB;QACnCiC,CAAC,CAACjC,KAAK,GAAG,CAAC,CAAC;QACZoB,CAAC,CAACjB,MAAM,CAACH,KAAK,GAAG,CAAC,CAAC;QACnBuB,WAAW,CAACJ,IAAI,EAAEC,CAAC,CAACjB,MAAM,CAAC;QAC3B8B,CAAC,GAAGb,CAAC,CAACjB,MAAM,CAACC,IAAI;MACrB;MACA,IAAI6B,CAAC,CAAC7B,IAAI,CAACJ,KAAK,KAAK,CAAC,CAAC,yBAAyBiC,CAAC,CAAC5B,KAAK,CAACL,KAAK,KAAK,CAAC,CAAC,uBAAuB;QACvFiC,CAAC,CAACjC,KAAK,GAAG,CAAC,CAAC;QACZoB,CAAC,GAAGA,CAAC,CAACjB,MAAM;MAChB,CAAC,MACI;QACD,IAAI8B,CAAC,CAAC7B,IAAI,CAACJ,KAAK,KAAK,CAAC,CAAC,uBAAuB;UAC1CiC,CAAC,CAAC5B,KAAK,CAACL,KAAK,GAAG,CAAC,CAAC;UAClBiC,CAAC,CAACjC,KAAK,GAAG,CAAC,CAAC;UACZkB,UAAU,CAACC,IAAI,EAAEc,CAAC,CAAC;UACnBA,CAAC,GAAGb,CAAC,CAACjB,MAAM,CAACC,IAAI;QACrB;QACA6B,CAAC,CAACjC,KAAK,GAAGoB,CAAC,CAACjB,MAAM,CAACH,KAAK;QACxBoB,CAAC,CAACjB,MAAM,CAACH,KAAK,GAAG,CAAC,CAAC;QACnBiC,CAAC,CAAC7B,IAAI,CAACJ,KAAK,GAAG,CAAC,CAAC;QACjBuB,WAAW,CAACJ,IAAI,EAAEC,CAAC,CAACjB,MAAM,CAAC;QAC3BiB,CAAC,GAAGD,IAAI,CAACG,IAAI;MACjB;IACJ;EACJ;EACAF,CAAC,CAACpB,KAAK,GAAG,CAAC,CAAC;EACZiB,aAAa,CAAC,CAAC;AACnB;AACA,OAAO,SAASiB,SAASA,CAACf,IAAI,EAAEC,CAAC,EAAE;EAC/BO,qBAAqB,CAACR,IAAI,EAAEC,CAAC,CAAC;EAC9B,OAAOA,CAAC,KAAKD,IAAI,CAACG,IAAI,IAAIF,CAAC,CAACjB,MAAM,CAACH,KAAK,KAAK,CAAC,CAAC,qBAAqB;IAChE,IAAIoB,CAAC,CAACjB,MAAM,KAAKiB,CAAC,CAACjB,MAAM,CAACA,MAAM,CAACC,IAAI,EAAE;MACnC,MAAMiB,CAAC,GAAGD,CAAC,CAACjB,MAAM,CAACA,MAAM,CAACE,KAAK;MAC/B,IAAIgB,CAAC,CAACrB,KAAK,KAAK,CAAC,CAAC,qBAAqB;QACnCoB,CAAC,CAACjB,MAAM,CAACH,KAAK,GAAG,CAAC,CAAC;QACnBqB,CAAC,CAACrB,KAAK,GAAG,CAAC,CAAC;QACZoB,CAAC,CAACjB,MAAM,CAACA,MAAM,CAACH,KAAK,GAAG,CAAC,CAAC;QAC1BoB,CAAC,GAAGA,CAAC,CAACjB,MAAM,CAACA,MAAM;MACvB,CAAC,MACI;QACD,IAAIiB,CAAC,KAAKA,CAAC,CAACjB,MAAM,CAACE,KAAK,EAAE;UACtBe,CAAC,GAAGA,CAAC,CAACjB,MAAM;UACZe,UAAU,CAACC,IAAI,EAAEC,CAAC,CAAC;QACvB;QACAA,CAAC,CAACjB,MAAM,CAACH,KAAK,GAAG,CAAC,CAAC;QACnBoB,CAAC,CAACjB,MAAM,CAACA,MAAM,CAACH,KAAK,GAAG,CAAC,CAAC;QAC1BuB,WAAW,CAACJ,IAAI,EAAEC,CAAC,CAACjB,MAAM,CAACA,MAAM,CAAC;MACtC;IACJ,CAAC,MACI;MACD,MAAMkB,CAAC,GAAGD,CAAC,CAACjB,MAAM,CAACA,MAAM,CAACC,IAAI;MAC9B,IAAIiB,CAAC,CAACrB,KAAK,KAAK,CAAC,CAAC,qBAAqB;QACnCoB,CAAC,CAACjB,MAAM,CAACH,KAAK,GAAG,CAAC,CAAC;QACnBqB,CAAC,CAACrB,KAAK,GAAG,CAAC,CAAC;QACZoB,CAAC,CAACjB,MAAM,CAACA,MAAM,CAACH,KAAK,GAAG,CAAC,CAAC;QAC1BoB,CAAC,GAAGA,CAAC,CAACjB,MAAM,CAACA,MAAM;MACvB,CAAC,MACI;QACD,IAAIiB,CAAC,KAAKA,CAAC,CAACjB,MAAM,CAACC,IAAI,EAAE;UACrBgB,CAAC,GAAGA,CAAC,CAACjB,MAAM;UACZoB,WAAW,CAACJ,IAAI,EAAEC,CAAC,CAAC;QACxB;QACAA,CAAC,CAACjB,MAAM,CAACH,KAAK,GAAG,CAAC,CAAC;QACnBoB,CAAC,CAACjB,MAAM,CAACA,MAAM,CAACH,KAAK,GAAG,CAAC,CAAC;QAC1BkB,UAAU,CAACC,IAAI,EAAEC,CAAC,CAACjB,MAAM,CAACA,MAAM,CAAC;MACrC;IACJ;EACJ;EACAgB,IAAI,CAACG,IAAI,CAACtB,KAAK,GAAG,CAAC,CAAC;AACxB;AACA,OAAO,SAASgC,kBAAkBA,CAACb,IAAI,EAAEC,CAAC,EAAEU,KAAK,EAAEK,gBAAgB,EAAE;EACjE;EACA,OAAOf,CAAC,KAAKD,IAAI,CAACG,IAAI,IAAIF,CAAC,KAAKb,QAAQ,EAAE;IACtC,IAAIa,CAAC,CAACjB,MAAM,CAACC,IAAI,KAAKgB,CAAC,EAAE;MACrBA,CAAC,CAACjB,MAAM,CAACF,SAAS,IAAI6B,KAAK;MAC3BV,CAAC,CAACjB,MAAM,CAACD,OAAO,IAAIiC,gBAAgB;IACxC;IACAf,CAAC,GAAGA,CAAC,CAACjB,MAAM;EAChB;AACJ;AACA,OAAO,SAASwB,qBAAqBA,CAACR,IAAI,EAAEC,CAAC,EAAE;EAC3C,IAAIU,KAAK,GAAG,CAAC;EACb,IAAIC,QAAQ,GAAG,CAAC;EAChB,IAAIX,CAAC,KAAKD,IAAI,CAACG,IAAI,EAAE;IACjB;EACJ;EACA;EACA,OAAOF,CAAC,KAAKD,IAAI,CAACG,IAAI,IAAIF,CAAC,KAAKA,CAAC,CAACjB,MAAM,CAACE,KAAK,EAAE;IAC5Ce,CAAC,GAAGA,CAAC,CAACjB,MAAM;EAChB;EACA,IAAIiB,CAAC,KAAKD,IAAI,CAACG,IAAI,EAAE;IACjB;IACA;EACJ;EACA;EACAF,CAAC,GAAGA,CAAC,CAACjB,MAAM;EACZ2B,KAAK,GAAGjB,aAAa,CAACO,CAAC,CAAChB,IAAI,CAAC,GAAGgB,CAAC,CAACnB,SAAS;EAC3C8B,QAAQ,GAAGhB,WAAW,CAACK,CAAC,CAAChB,IAAI,CAAC,GAAGgB,CAAC,CAAClB,OAAO;EAC1CkB,CAAC,CAACnB,SAAS,IAAI6B,KAAK;EACpBV,CAAC,CAAClB,OAAO,IAAI6B,QAAQ;EACrB;EACA,OAAOX,CAAC,KAAKD,IAAI,CAACG,IAAI,KAAKQ,KAAK,KAAK,CAAC,IAAIC,QAAQ,KAAK,CAAC,CAAC,EAAE;IACvD,IAAIX,CAAC,CAACjB,MAAM,CAACC,IAAI,KAAKgB,CAAC,EAAE;MACrBA,CAAC,CAACjB,MAAM,CAACF,SAAS,IAAI6B,KAAK;MAC3BV,CAAC,CAACjB,MAAM,CAACD,OAAO,IAAI6B,QAAQ;IAChC;IACAX,CAAC,GAAGA,CAAC,CAACjB,MAAM;EAChB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}