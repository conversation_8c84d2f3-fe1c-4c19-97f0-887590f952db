{"ast": null, "code": "import _asyncToGenerator from \"C:/console/aava-ui-web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = this && this.__param || function (paramIndex, decorator) {\n  return function (target, key) {\n    decorator(target, key, paramIndex);\n  };\n};\nimport * as dom from '../../../base/browser/dom.js';\nimport { StandardKeyboardEvent } from '../../../base/browser/keyboardEvent.js';\nimport { Toggle } from '../../../base/browser/ui/toggle/toggle.js';\nimport { equals } from '../../../base/common/arrays.js';\nimport { TimeoutTimer } from '../../../base/common/async.js';\nimport { Codicon } from '../../../base/common/codicons.js';\nimport { Emitter, EventBufferer } from '../../../base/common/event.js';\nimport { Disposable, DisposableStore } from '../../../base/common/lifecycle.js';\nimport { isIOS } from '../../../base/common/platform.js';\nimport Severity from '../../../base/common/severity.js';\nimport { ThemeIcon } from '../../../base/common/themables.js';\nimport './media/quickInput.css';\nimport { localize } from '../../../nls.js';\nimport { ItemActivation, NO_KEY_MODS, QuickInputButtonLocation, QuickInputHideReason, QuickPickFocus } from '../common/quickInput.js';\nimport { quickInputButtonToAction, renderQuickInputDescription } from './quickInputUtils.js';\nimport { IConfigurationService } from '../../configuration/common/configuration.js';\nimport { IHoverService, WorkbenchHoverDelegate } from '../../hover/browser/hover.js';\nimport { ContextKeyExpr, RawContextKey } from '../../contextkey/common/contextkey.js';\nexport const inQuickInputContextKeyValue = 'inQuickInput';\nexport const InQuickInputContextKey = new RawContextKey(inQuickInputContextKeyValue, false, localize('inQuickInput', \"Whether keyboard focus is inside the quick input control\"));\nexport const inQuickInputContext = ContextKeyExpr.has(inQuickInputContextKeyValue);\nexport const quickInputTypeContextKeyValue = 'quickInputType';\nexport const QuickInputTypeContextKey = new RawContextKey(quickInputTypeContextKeyValue, undefined, localize('quickInputType', \"The type of the currently visible quick input\"));\nexport const endOfQuickInputBoxContextKeyValue = 'cursorAtEndOfQuickInputBox';\nexport const EndOfQuickInputBoxContextKey = new RawContextKey(endOfQuickInputBoxContextKeyValue, false, localize('cursorAtEndOfQuickInputBox', \"Whether the cursor in the quick input is at the end of the input box\"));\nexport const endOfQuickInputBoxContext = ContextKeyExpr.has(endOfQuickInputBoxContextKeyValue);\nexport const backButton = {\n  iconClass: ThemeIcon.asClassName(Codicon.quickInputBack),\n  tooltip: localize('quickInput.back', \"Back\"),\n  handle: -1 // TODO\n};\nclass QuickInput extends Disposable {\n  static {\n    this.noPromptMessage = localize('inputModeEntry', \"Press 'Enter' to confirm your input or 'Escape' to cancel\");\n  }\n  constructor(ui) {\n    super();\n    this.ui = ui;\n    this._widgetUpdated = false;\n    this.visible = false;\n    this._enabled = true;\n    this._busy = false;\n    this._ignoreFocusOut = false;\n    this._leftButtons = [];\n    this._rightButtons = [];\n    this._inlineButtons = [];\n    this.buttonsUpdated = false;\n    this._toggles = [];\n    this.togglesUpdated = false;\n    this.noValidationMessage = QuickInput.noPromptMessage;\n    this._severity = Severity.Ignore;\n    this.onDidTriggerButtonEmitter = this._register(new Emitter());\n    this.onDidHideEmitter = this._register(new Emitter());\n    this.onWillHideEmitter = this._register(new Emitter());\n    this.onDisposeEmitter = this._register(new Emitter());\n    this.visibleDisposables = this._register(new DisposableStore());\n    this.onDidHide = this.onDidHideEmitter.event;\n  }\n  get title() {\n    return this._title;\n  }\n  set title(title) {\n    this._title = title;\n    this.update();\n  }\n  get description() {\n    return this._description;\n  }\n  set description(description) {\n    this._description = description;\n    this.update();\n  }\n  get step() {\n    return this._steps;\n  }\n  set step(step) {\n    this._steps = step;\n    this.update();\n  }\n  get totalSteps() {\n    return this._totalSteps;\n  }\n  set totalSteps(totalSteps) {\n    this._totalSteps = totalSteps;\n    this.update();\n  }\n  get enabled() {\n    return this._enabled;\n  }\n  set enabled(enabled) {\n    this._enabled = enabled;\n    this.update();\n  }\n  get contextKey() {\n    return this._contextKey;\n  }\n  set contextKey(contextKey) {\n    this._contextKey = contextKey;\n    this.update();\n  }\n  get busy() {\n    return this._busy;\n  }\n  set busy(busy) {\n    this._busy = busy;\n    this.update();\n  }\n  get ignoreFocusOut() {\n    return this._ignoreFocusOut;\n  }\n  set ignoreFocusOut(ignoreFocusOut) {\n    const shouldUpdate = this._ignoreFocusOut !== ignoreFocusOut && !isIOS;\n    this._ignoreFocusOut = ignoreFocusOut && !isIOS;\n    if (shouldUpdate) {\n      this.update();\n    }\n  }\n  get titleButtons() {\n    return this._leftButtons.length ? [...this._leftButtons, this._rightButtons] : this._rightButtons;\n  }\n  get buttons() {\n    return [...this._leftButtons, ...this._rightButtons, ...this._inlineButtons];\n  }\n  set buttons(buttons) {\n    this._leftButtons = buttons.filter(b => b === backButton);\n    this._rightButtons = buttons.filter(b => b !== backButton && b.location !== QuickInputButtonLocation.Inline);\n    this._inlineButtons = buttons.filter(b => b.location === QuickInputButtonLocation.Inline);\n    this.buttonsUpdated = true;\n    this.update();\n  }\n  get toggles() {\n    return this._toggles;\n  }\n  set toggles(toggles) {\n    this._toggles = toggles ?? [];\n    this.togglesUpdated = true;\n    this.update();\n  }\n  get validationMessage() {\n    return this._validationMessage;\n  }\n  set validationMessage(validationMessage) {\n    this._validationMessage = validationMessage;\n    this.update();\n  }\n  get severity() {\n    return this._severity;\n  }\n  set severity(severity) {\n    this._severity = severity;\n    this.update();\n  }\n  show() {\n    if (this.visible) {\n      return;\n    }\n    this.visibleDisposables.add(this.ui.onDidTriggerButton(button => {\n      if (this.buttons.indexOf(button) !== -1) {\n        this.onDidTriggerButtonEmitter.fire(button);\n      }\n    }));\n    this.ui.show(this);\n    // update properties in the controller that get reset in the ui.show() call\n    this.visible = true;\n    // This ensures the message/prompt gets rendered\n    this._lastValidationMessage = undefined;\n    // This ensures the input box has the right severity applied\n    this._lastSeverity = undefined;\n    if (this.buttons.length) {\n      // if there are buttons, the ui.show() clears them out of the UI so we should\n      // rerender them.\n      this.buttonsUpdated = true;\n    }\n    if (this.toggles.length) {\n      // if there are toggles, the ui.show() clears them out of the UI so we should\n      // rerender them.\n      this.togglesUpdated = true;\n    }\n    this.update();\n  }\n  hide() {\n    if (!this.visible) {\n      return;\n    }\n    this.ui.hide();\n  }\n  didHide(reason = QuickInputHideReason.Other) {\n    this.visible = false;\n    this.visibleDisposables.clear();\n    this.onDidHideEmitter.fire({\n      reason\n    });\n  }\n  willHide(reason = QuickInputHideReason.Other) {\n    this.onWillHideEmitter.fire({\n      reason\n    });\n  }\n  update() {\n    var _this = this;\n    if (!this.visible) {\n      return;\n    }\n    const title = this.getTitle();\n    if (title && this.ui.title.textContent !== title) {\n      this.ui.title.textContent = title;\n    } else if (!title && this.ui.title.innerHTML !== '&nbsp;') {\n      this.ui.title.innerText = '\\u00a0';\n    }\n    const description = this.getDescription();\n    if (this.ui.description1.textContent !== description) {\n      this.ui.description1.textContent = description;\n    }\n    if (this.ui.description2.textContent !== description) {\n      this.ui.description2.textContent = description;\n    }\n    if (this._widgetUpdated) {\n      this._widgetUpdated = false;\n      if (this._widget) {\n        dom.reset(this.ui.widget, this._widget);\n      } else {\n        dom.reset(this.ui.widget);\n      }\n    }\n    if (this.busy && !this.busyDelay) {\n      this.busyDelay = new TimeoutTimer();\n      this.busyDelay.setIfNotSet(() => {\n        if (this.visible) {\n          this.ui.progressBar.infinite();\n        }\n      }, 800);\n    }\n    if (!this.busy && this.busyDelay) {\n      this.ui.progressBar.stop();\n      this.busyDelay.cancel();\n      this.busyDelay = undefined;\n    }\n    if (this.buttonsUpdated) {\n      this.buttonsUpdated = false;\n      this.ui.leftActionBar.clear();\n      const leftButtons = this._leftButtons.map((button, index) => quickInputButtonToAction(button, `id-${index}`, /*#__PURE__*/_asyncToGenerator(function* () {\n        return _this.onDidTriggerButtonEmitter.fire(button);\n      })));\n      this.ui.leftActionBar.push(leftButtons, {\n        icon: true,\n        label: false\n      });\n      this.ui.rightActionBar.clear();\n      const rightButtons = this._rightButtons.map((button, index) => quickInputButtonToAction(button, `id-${index}`, /*#__PURE__*/_asyncToGenerator(function* () {\n        return _this.onDidTriggerButtonEmitter.fire(button);\n      })));\n      this.ui.rightActionBar.push(rightButtons, {\n        icon: true,\n        label: false\n      });\n      this.ui.inlineActionBar.clear();\n      const inlineButtons = this._inlineButtons.map((button, index) => quickInputButtonToAction(button, `id-${index}`, /*#__PURE__*/_asyncToGenerator(function* () {\n        return _this.onDidTriggerButtonEmitter.fire(button);\n      })));\n      this.ui.inlineActionBar.push(inlineButtons, {\n        icon: true,\n        label: false\n      });\n    }\n    if (this.togglesUpdated) {\n      this.togglesUpdated = false;\n      // HACK: Filter out toggles here that are not concrete Toggle objects. This is to workaround\n      // a layering issue as quick input's interface is in common but Toggle is in browser and\n      // it requires a HTMLElement on its interface\n      const concreteToggles = this.toggles?.filter(opts => opts instanceof Toggle) ?? [];\n      this.ui.inputBox.toggles = concreteToggles;\n    }\n    this.ui.ignoreFocusOut = this.ignoreFocusOut;\n    this.ui.setEnabled(this.enabled);\n    this.ui.setContextKey(this.contextKey);\n    const validationMessage = this.validationMessage || this.noValidationMessage;\n    if (this._lastValidationMessage !== validationMessage) {\n      this._lastValidationMessage = validationMessage;\n      dom.reset(this.ui.message);\n      renderQuickInputDescription(validationMessage, this.ui.message, {\n        callback: content => {\n          this.ui.linkOpenerDelegate(content);\n        },\n        disposables: this.visibleDisposables\n      });\n    }\n    if (this._lastSeverity !== this.severity) {\n      this._lastSeverity = this.severity;\n      this.showMessageDecoration(this.severity);\n    }\n  }\n  getTitle() {\n    if (this.title && this.step) {\n      return `${this.title} (${this.getSteps()})`;\n    }\n    if (this.title) {\n      return this.title;\n    }\n    if (this.step) {\n      return this.getSteps();\n    }\n    return '';\n  }\n  getDescription() {\n    return this.description || '';\n  }\n  getSteps() {\n    if (this.step && this.totalSteps) {\n      return localize('quickInput.steps', \"{0}/{1}\", this.step, this.totalSteps);\n    }\n    if (this.step) {\n      return String(this.step);\n    }\n    return '';\n  }\n  showMessageDecoration(severity) {\n    this.ui.inputBox.showDecoration(severity);\n    if (severity !== Severity.Ignore) {\n      const styles = this.ui.inputBox.stylesForType(severity);\n      this.ui.message.style.color = styles.foreground ? `${styles.foreground}` : '';\n      this.ui.message.style.backgroundColor = styles.background ? `${styles.background}` : '';\n      this.ui.message.style.border = styles.border ? `1px solid ${styles.border}` : '';\n      this.ui.message.style.marginBottom = '-2px';\n    } else {\n      this.ui.message.style.color = '';\n      this.ui.message.style.backgroundColor = '';\n      this.ui.message.style.border = '';\n      this.ui.message.style.marginBottom = '';\n    }\n  }\n  dispose() {\n    this.hide();\n    this.onDisposeEmitter.fire();\n    super.dispose();\n  }\n}\nexport class QuickPick extends QuickInput {\n  constructor() {\n    super(...arguments);\n    this._value = '';\n    this.onDidChangeValueEmitter = this._register(new Emitter());\n    this.onWillAcceptEmitter = this._register(new Emitter());\n    this.onDidAcceptEmitter = this._register(new Emitter());\n    this.onDidCustomEmitter = this._register(new Emitter());\n    this._items = [];\n    this.itemsUpdated = false;\n    this._canSelectMany = false;\n    this._canAcceptInBackground = false;\n    this._matchOnDescription = false;\n    this._matchOnDetail = false;\n    this._matchOnLabel = true;\n    this._matchOnLabelMode = 'fuzzy';\n    this._sortByLabel = true;\n    this._keepScrollPosition = false;\n    this._itemActivation = ItemActivation.FIRST;\n    this._activeItems = [];\n    this.activeItemsUpdated = false;\n    this.activeItemsToConfirm = [];\n    this.onDidChangeActiveEmitter = this._register(new Emitter());\n    this._selectedItems = [];\n    this.selectedItemsUpdated = false;\n    this.selectedItemsToConfirm = [];\n    this.onDidChangeSelectionEmitter = this._register(new Emitter());\n    this.onDidTriggerItemButtonEmitter = this._register(new Emitter());\n    this.onDidTriggerSeparatorButtonEmitter = this._register(new Emitter());\n    this.valueSelectionUpdated = true;\n    this._ok = 'default';\n    this._customButton = false;\n    this._focusEventBufferer = new EventBufferer();\n    this.type = \"quickPick\" /* QuickInputType.QuickPick */;\n    this.filterValue = value => value;\n    this.onDidChangeValue = this.onDidChangeValueEmitter.event;\n    this.onWillAccept = this.onWillAcceptEmitter.event;\n    this.onDidAccept = this.onDidAcceptEmitter.event;\n    this.onDidChangeActive = this.onDidChangeActiveEmitter.event;\n    this.onDidChangeSelection = this.onDidChangeSelectionEmitter.event;\n    this.onDidTriggerItemButton = this.onDidTriggerItemButtonEmitter.event;\n    this.onDidTriggerSeparatorButton = this.onDidTriggerSeparatorButtonEmitter.event;\n  }\n  static {\n    this.DEFAULT_ARIA_LABEL = localize('quickInputBox.ariaLabel', \"Type to narrow down results.\");\n  }\n  get quickNavigate() {\n    return this._quickNavigate;\n  }\n  set quickNavigate(quickNavigate) {\n    this._quickNavigate = quickNavigate;\n    this.update();\n  }\n  get value() {\n    return this._value;\n  }\n  set value(value) {\n    this.doSetValue(value);\n  }\n  doSetValue(value, skipUpdate) {\n    if (this._value !== value) {\n      this._value = value;\n      if (!skipUpdate) {\n        this.update();\n      }\n      if (this.visible) {\n        const didFilter = this.ui.list.filter(this.filterValue(this._value));\n        if (didFilter) {\n          this.trySelectFirst();\n        }\n      }\n      this.onDidChangeValueEmitter.fire(this._value);\n    }\n  }\n  set ariaLabel(ariaLabel) {\n    this._ariaLabel = ariaLabel;\n    this.update();\n  }\n  get ariaLabel() {\n    return this._ariaLabel;\n  }\n  get placeholder() {\n    return this._placeholder;\n  }\n  set placeholder(placeholder) {\n    this._placeholder = placeholder;\n    this.update();\n  }\n  get items() {\n    return this._items;\n  }\n  get scrollTop() {\n    return this.ui.list.scrollTop;\n  }\n  set scrollTop(scrollTop) {\n    this.ui.list.scrollTop = scrollTop;\n  }\n  set items(items) {\n    this._items = items;\n    this.itemsUpdated = true;\n    this.update();\n  }\n  get canSelectMany() {\n    return this._canSelectMany;\n  }\n  set canSelectMany(canSelectMany) {\n    this._canSelectMany = canSelectMany;\n    this.update();\n  }\n  get canAcceptInBackground() {\n    return this._canAcceptInBackground;\n  }\n  set canAcceptInBackground(canAcceptInBackground) {\n    this._canAcceptInBackground = canAcceptInBackground;\n  }\n  get matchOnDescription() {\n    return this._matchOnDescription;\n  }\n  set matchOnDescription(matchOnDescription) {\n    this._matchOnDescription = matchOnDescription;\n    this.update();\n  }\n  get matchOnDetail() {\n    return this._matchOnDetail;\n  }\n  set matchOnDetail(matchOnDetail) {\n    this._matchOnDetail = matchOnDetail;\n    this.update();\n  }\n  get matchOnLabel() {\n    return this._matchOnLabel;\n  }\n  set matchOnLabel(matchOnLabel) {\n    this._matchOnLabel = matchOnLabel;\n    this.update();\n  }\n  get matchOnLabelMode() {\n    return this._matchOnLabelMode;\n  }\n  set matchOnLabelMode(matchOnLabelMode) {\n    this._matchOnLabelMode = matchOnLabelMode;\n    this.update();\n  }\n  get sortByLabel() {\n    return this._sortByLabel;\n  }\n  set sortByLabel(sortByLabel) {\n    this._sortByLabel = sortByLabel;\n    this.update();\n  }\n  get keepScrollPosition() {\n    return this._keepScrollPosition;\n  }\n  set keepScrollPosition(keepScrollPosition) {\n    this._keepScrollPosition = keepScrollPosition;\n  }\n  get itemActivation() {\n    return this._itemActivation;\n  }\n  set itemActivation(itemActivation) {\n    this._itemActivation = itemActivation;\n  }\n  get activeItems() {\n    return this._activeItems;\n  }\n  set activeItems(activeItems) {\n    this._activeItems = activeItems;\n    this.activeItemsUpdated = true;\n    this.update();\n  }\n  get selectedItems() {\n    return this._selectedItems;\n  }\n  set selectedItems(selectedItems) {\n    this._selectedItems = selectedItems;\n    this.selectedItemsUpdated = true;\n    this.update();\n  }\n  get keyMods() {\n    if (this._quickNavigate) {\n      // Disable keyMods when quick navigate is enabled\n      // because in this model the interaction is purely\n      // keyboard driven and Ctrl/Alt are typically\n      // pressed and hold during this interaction.\n      return NO_KEY_MODS;\n    }\n    return this.ui.keyMods;\n  }\n  get valueSelection() {\n    const selection = this.ui.inputBox.getSelection();\n    if (!selection) {\n      return undefined;\n    }\n    return [selection.start, selection.end];\n  }\n  set valueSelection(valueSelection) {\n    this._valueSelection = valueSelection;\n    this.valueSelectionUpdated = true;\n    this.update();\n  }\n  get customButton() {\n    return this._customButton;\n  }\n  set customButton(showCustomButton) {\n    this._customButton = showCustomButton;\n    this.update();\n  }\n  get customLabel() {\n    return this._customButtonLabel;\n  }\n  set customLabel(label) {\n    this._customButtonLabel = label;\n    this.update();\n  }\n  get customHover() {\n    return this._customButtonHover;\n  }\n  set customHover(hover) {\n    this._customButtonHover = hover;\n    this.update();\n  }\n  get ok() {\n    return this._ok;\n  }\n  set ok(showOkButton) {\n    this._ok = showOkButton;\n    this.update();\n  }\n  get hideInput() {\n    return !!this._hideInput;\n  }\n  set hideInput(hideInput) {\n    this._hideInput = hideInput;\n    this.update();\n  }\n  trySelectFirst() {\n    if (!this.canSelectMany) {\n      this.ui.list.focus(QuickPickFocus.First);\n    }\n  }\n  show() {\n    if (!this.visible) {\n      this.visibleDisposables.add(this.ui.inputBox.onDidChange(value => {\n        this.doSetValue(value, true /* skip update since this originates from the UI */);\n      }));\n      this.visibleDisposables.add(this.ui.onDidAccept(() => {\n        if (this.canSelectMany) {\n          // if there are no checked elements, it means that an onDidChangeSelection never fired to overwrite\n          // `_selectedItems`. In that case, we should emit one with an empty array to ensure that\n          // `.selectedItems` is up to date.\n          if (!this.ui.list.getCheckedElements().length) {\n            this._selectedItems = [];\n            this.onDidChangeSelectionEmitter.fire(this.selectedItems);\n          }\n        } else if (this.activeItems[0]) {\n          // For single-select, we set `selectedItems` to the item that was accepted.\n          this._selectedItems = [this.activeItems[0]];\n          this.onDidChangeSelectionEmitter.fire(this.selectedItems);\n        }\n        this.handleAccept(false);\n      }));\n      this.visibleDisposables.add(this.ui.onDidCustom(() => {\n        this.onDidCustomEmitter.fire();\n      }));\n      this.visibleDisposables.add(this._focusEventBufferer.wrapEvent(this.ui.list.onDidChangeFocus,\n      // Only fire the last event\n      (_, e) => e)(focusedItems => {\n        if (this.activeItemsUpdated) {\n          return; // Expect another event.\n        }\n        if (this.activeItemsToConfirm !== this._activeItems && equals(focusedItems, this._activeItems, (a, b) => a === b)) {\n          return;\n        }\n        this._activeItems = focusedItems;\n        this.onDidChangeActiveEmitter.fire(focusedItems);\n      }));\n      this.visibleDisposables.add(this.ui.list.onDidChangeSelection(({\n        items: selectedItems,\n        event\n      }) => {\n        if (this.canSelectMany) {\n          if (selectedItems.length) {\n            this.ui.list.setSelectedElements([]);\n          }\n          return;\n        }\n        if (this.selectedItemsToConfirm !== this._selectedItems && equals(selectedItems, this._selectedItems, (a, b) => a === b)) {\n          return;\n        }\n        this._selectedItems = selectedItems;\n        this.onDidChangeSelectionEmitter.fire(selectedItems);\n        if (selectedItems.length) {\n          this.handleAccept(dom.isMouseEvent(event) && event.button === 1 /* mouse middle click */);\n        }\n      }));\n      this.visibleDisposables.add(this.ui.list.onChangedCheckedElements(checkedItems => {\n        if (!this.canSelectMany || !this.visible) {\n          return;\n        }\n        if (this.selectedItemsToConfirm !== this._selectedItems && equals(checkedItems, this._selectedItems, (a, b) => a === b)) {\n          return;\n        }\n        this._selectedItems = checkedItems;\n        this.onDidChangeSelectionEmitter.fire(checkedItems);\n      }));\n      this.visibleDisposables.add(this.ui.list.onButtonTriggered(event => this.onDidTriggerItemButtonEmitter.fire(event)));\n      this.visibleDisposables.add(this.ui.list.onSeparatorButtonTriggered(event => this.onDidTriggerSeparatorButtonEmitter.fire(event)));\n      this.visibleDisposables.add(this.registerQuickNavigation());\n      this.valueSelectionUpdated = true;\n    }\n    super.show(); // TODO: Why have show() bubble up while update() trickles down?\n  }\n  handleAccept(inBackground) {\n    // Figure out veto via `onWillAccept` event\n    let veto = false;\n    this.onWillAcceptEmitter.fire({\n      veto: () => veto = true\n    });\n    // Continue with `onDidAccept` if no veto\n    if (!veto) {\n      this.onDidAcceptEmitter.fire({\n        inBackground\n      });\n    }\n  }\n  registerQuickNavigation() {\n    return dom.addDisposableListener(this.ui.container, dom.EventType.KEY_UP, e => {\n      if (this.canSelectMany || !this._quickNavigate) {\n        return;\n      }\n      const keyboardEvent = new StandardKeyboardEvent(e);\n      const keyCode = keyboardEvent.keyCode;\n      // Select element when keys are pressed that signal it\n      const quickNavKeys = this._quickNavigate.keybindings;\n      const wasTriggerKeyPressed = quickNavKeys.some(k => {\n        const chords = k.getChords();\n        if (chords.length > 1) {\n          return false;\n        }\n        if (chords[0].shiftKey && keyCode === 4 /* KeyCode.Shift */) {\n          if (keyboardEvent.ctrlKey || keyboardEvent.altKey || keyboardEvent.metaKey) {\n            return false; // this is an optimistic check for the shift key being used to navigate back in quick input\n          }\n          return true;\n        }\n        if (chords[0].altKey && keyCode === 6 /* KeyCode.Alt */) {\n          return true;\n        }\n        if (chords[0].ctrlKey && keyCode === 5 /* KeyCode.Ctrl */) {\n          return true;\n        }\n        if (chords[0].metaKey && keyCode === 57 /* KeyCode.Meta */) {\n          return true;\n        }\n        return false;\n      });\n      if (wasTriggerKeyPressed) {\n        if (this.activeItems[0]) {\n          this._selectedItems = [this.activeItems[0]];\n          this.onDidChangeSelectionEmitter.fire(this.selectedItems);\n          this.handleAccept(false);\n        }\n        // Unset quick navigate after press. It is only valid once\n        // and should not result in any behaviour change afterwards\n        // if the picker remains open because there was no active item\n        this._quickNavigate = undefined;\n      }\n    });\n  }\n  update() {\n    if (!this.visible) {\n      return;\n    }\n    // store the scrollTop before it is reset\n    const scrollTopBefore = this.keepScrollPosition ? this.scrollTop : 0;\n    const hasDescription = !!this.description;\n    const visibilities = {\n      title: !!this.title || !!this.step || !!this.titleButtons.length,\n      description: hasDescription,\n      checkAll: this.canSelectMany && !this._hideCheckAll,\n      checkBox: this.canSelectMany,\n      inputBox: !this._hideInput,\n      progressBar: !this._hideInput || hasDescription,\n      visibleCount: true,\n      count: this.canSelectMany && !this._hideCountBadge,\n      ok: this.ok === 'default' ? this.canSelectMany : this.ok,\n      list: true,\n      message: !!this.validationMessage,\n      customButton: this.customButton\n    };\n    this.ui.setVisibilities(visibilities);\n    super.update();\n    if (this.ui.inputBox.value !== this.value) {\n      this.ui.inputBox.value = this.value;\n    }\n    if (this.valueSelectionUpdated) {\n      this.valueSelectionUpdated = false;\n      this.ui.inputBox.select(this._valueSelection && {\n        start: this._valueSelection[0],\n        end: this._valueSelection[1]\n      });\n    }\n    if (this.ui.inputBox.placeholder !== (this.placeholder || '')) {\n      this.ui.inputBox.placeholder = this.placeholder || '';\n    }\n    let ariaLabel = this.ariaLabel;\n    // Only set aria label to the input box placeholder if we actually have an input box.\n    if (!ariaLabel && visibilities.inputBox) {\n      ariaLabel = this.placeholder || QuickPick.DEFAULT_ARIA_LABEL;\n      // If we have a title, include it in the aria label.\n      if (this.title) {\n        ariaLabel += ` - ${this.title}`;\n      }\n    }\n    if (this.ui.list.ariaLabel !== ariaLabel) {\n      this.ui.list.ariaLabel = ariaLabel ?? null;\n    }\n    this.ui.list.matchOnDescription = this.matchOnDescription;\n    this.ui.list.matchOnDetail = this.matchOnDetail;\n    this.ui.list.matchOnLabel = this.matchOnLabel;\n    this.ui.list.matchOnLabelMode = this.matchOnLabelMode;\n    this.ui.list.sortByLabel = this.sortByLabel;\n    if (this.itemsUpdated) {\n      this.itemsUpdated = false;\n      this._focusEventBufferer.bufferEvents(() => {\n        this.ui.list.setElements(this.items);\n        // We want focus to exist in the list if there are items so that space can be used to toggle\n        this.ui.list.shouldLoop = !this.canSelectMany;\n        this.ui.list.filter(this.filterValue(this.ui.inputBox.value));\n        switch (this._itemActivation) {\n          case ItemActivation.NONE:\n            this._itemActivation = ItemActivation.FIRST; // only valid once, then unset\n            break;\n          case ItemActivation.SECOND:\n            this.ui.list.focus(QuickPickFocus.Second);\n            this._itemActivation = ItemActivation.FIRST; // only valid once, then unset\n            break;\n          case ItemActivation.LAST:\n            this.ui.list.focus(QuickPickFocus.Last);\n            this._itemActivation = ItemActivation.FIRST; // only valid once, then unset\n            break;\n          default:\n            this.trySelectFirst();\n            break;\n        }\n      });\n    }\n    if (this.ui.container.classList.contains('show-checkboxes') !== !!this.canSelectMany) {\n      if (this.canSelectMany) {\n        this.ui.list.clearFocus();\n      } else {\n        this.trySelectFirst();\n      }\n    }\n    if (this.activeItemsUpdated) {\n      this.activeItemsUpdated = false;\n      this.activeItemsToConfirm = this._activeItems;\n      this.ui.list.setFocusedElements(this.activeItems);\n      if (this.activeItemsToConfirm === this._activeItems) {\n        this.activeItemsToConfirm = null;\n      }\n    }\n    if (this.selectedItemsUpdated) {\n      this.selectedItemsUpdated = false;\n      this.selectedItemsToConfirm = this._selectedItems;\n      if (this.canSelectMany) {\n        this.ui.list.setCheckedElements(this.selectedItems);\n      } else {\n        this.ui.list.setSelectedElements(this.selectedItems);\n      }\n      if (this.selectedItemsToConfirm === this._selectedItems) {\n        this.selectedItemsToConfirm = null;\n      }\n    }\n    this.ui.customButton.label = this.customLabel || '';\n    this.ui.customButton.element.title = this.customHover || '';\n    if (!visibilities.inputBox) {\n      // we need to move focus into the tree to detect keybindings\n      // properly when the input box is not visible (quick nav)\n      this.ui.list.domFocus();\n      // Focus the first element in the list if multiselect is enabled\n      if (this.canSelectMany) {\n        this.ui.list.focus(QuickPickFocus.First);\n      }\n    }\n    // Set the scroll position to what it was before updating the items\n    if (this.keepScrollPosition) {\n      this.scrollTop = scrollTopBefore;\n    }\n  }\n  focus(focus) {\n    this.ui.list.focus(focus);\n    // To allow things like space to check/uncheck items\n    if (this.canSelectMany) {\n      this.ui.list.domFocus();\n    }\n  }\n  accept(inBackground) {\n    if (inBackground && !this._canAcceptInBackground) {\n      return; // needs to be enabled\n    }\n    if (this.activeItems[0]) {\n      this._selectedItems = [this.activeItems[0]];\n      this.onDidChangeSelectionEmitter.fire(this.selectedItems);\n      this.handleAccept(inBackground ?? false);\n    }\n  }\n}\nexport class InputBox extends QuickInput {\n  constructor() {\n    super(...arguments);\n    this._value = '';\n    this.valueSelectionUpdated = true;\n    this._password = false;\n    this.onDidValueChangeEmitter = this._register(new Emitter());\n    this.onDidAcceptEmitter = this._register(new Emitter());\n    this.type = \"inputBox\" /* QuickInputType.InputBox */;\n    this.onDidChangeValue = this.onDidValueChangeEmitter.event;\n    this.onDidAccept = this.onDidAcceptEmitter.event;\n  }\n  get value() {\n    return this._value;\n  }\n  set value(value) {\n    this._value = value || '';\n    this.update();\n  }\n  get placeholder() {\n    return this._placeholder;\n  }\n  set placeholder(placeholder) {\n    this._placeholder = placeholder;\n    this.update();\n  }\n  get password() {\n    return this._password;\n  }\n  set password(password) {\n    this._password = password;\n    this.update();\n  }\n  show() {\n    if (!this.visible) {\n      this.visibleDisposables.add(this.ui.inputBox.onDidChange(value => {\n        if (value === this.value) {\n          return;\n        }\n        this._value = value;\n        this.onDidValueChangeEmitter.fire(value);\n      }));\n      this.visibleDisposables.add(this.ui.onDidAccept(() => this.onDidAcceptEmitter.fire()));\n      this.valueSelectionUpdated = true;\n    }\n    super.show();\n  }\n  update() {\n    if (!this.visible) {\n      return;\n    }\n    this.ui.container.classList.remove('hidden-input');\n    const visibilities = {\n      title: !!this.title || !!this.step || !!this.titleButtons.length,\n      description: !!this.description || !!this.step,\n      inputBox: true,\n      message: true,\n      progressBar: true\n    };\n    this.ui.setVisibilities(visibilities);\n    super.update();\n    if (this.ui.inputBox.value !== this.value) {\n      this.ui.inputBox.value = this.value;\n    }\n    if (this.valueSelectionUpdated) {\n      this.valueSelectionUpdated = false;\n      this.ui.inputBox.select(this._valueSelection && {\n        start: this._valueSelection[0],\n        end: this._valueSelection[1]\n      });\n    }\n    if (this.ui.inputBox.placeholder !== (this.placeholder || '')) {\n      this.ui.inputBox.placeholder = this.placeholder || '';\n    }\n    if (this.ui.inputBox.password !== this.password) {\n      this.ui.inputBox.password = this.password;\n    }\n  }\n}\nlet QuickInputHoverDelegate = class QuickInputHoverDelegate extends WorkbenchHoverDelegate {\n  constructor(configurationService, hoverService) {\n    super('element', false, options => this.getOverrideOptions(options), configurationService, hoverService);\n  }\n  getOverrideOptions(options) {\n    // Only show the hover hint if the content is of a decent size\n    const showHoverHint = (dom.isHTMLElement(options.content) ? options.content.textContent ?? '' : typeof options.content === 'string' ? options.content : options.content.value).includes('\\n');\n    return {\n      persistence: {\n        hideOnKeyDown: false\n      },\n      appearance: {\n        showHoverHint,\n        skipFadeInAnimation: true\n      }\n    };\n  }\n};\nQuickInputHoverDelegate = __decorate([__param(0, IConfigurationService), __param(1, IHoverService)], QuickInputHoverDelegate);\nexport { QuickInputHoverDelegate };", "map": {"version": 3, "names": ["__decorate", "decorators", "target", "key", "desc", "c", "arguments", "length", "r", "Object", "getOwnPropertyDescriptor", "d", "Reflect", "decorate", "i", "defineProperty", "__param", "paramIndex", "decorator", "dom", "StandardKeyboardEvent", "Toggle", "equals", "TimeoutTimer", "Codicon", "Emitter", "EventBuff<PERSON>", "Disposable", "DisposableStore", "isIOS", "Severity", "ThemeIcon", "localize", "ItemActivation", "NO_KEY_MODS", "QuickInputButtonLocation", "QuickInputHideReason", "QuickPickFocus", "quickInputButtonToAction", "renderQuickInputDescription", "IConfigurationService", "IHoverService", "WorkbenchHoverDelegate", "ContextKeyExpr", "RawContextKey", "inQuickInputContextKeyValue", "InQuickInputContextKey", "inQuickInputContext", "has", "quickInputTypeContextKeyValue", "QuickInputTypeContextKey", "undefined", "endOfQuickInputBoxContextKeyValue", "EndOfQuickInputBoxContextKey", "endOfQuickInputBoxContext", "backButton", "iconClass", "asClassName", "quickInputBack", "tooltip", "handle", "QuickInput", "noPromptMessage", "constructor", "ui", "_widgetUpdated", "visible", "_enabled", "_busy", "_ignoreFocusOut", "_leftButtons", "_rightButtons", "_inlineButtons", "buttonsUpdated", "_toggles", "togglesUpdated", "noValidationMessage", "_severity", "Ignore", "onDidTriggerButtonEmitter", "_register", "onDidHideEmitter", "onWillHideEmitter", "onDisposeEmitter", "visibleDisposables", "onDidHide", "event", "title", "_title", "update", "description", "_description", "step", "_steps", "totalSteps", "_totalSteps", "enabled", "<PERSON><PERSON>ey", "_context<PERSON>ey", "busy", "ignoreFocusOut", "shouldUpdate", "titleButtons", "buttons", "filter", "b", "location", "Inline", "toggles", "validationMessage", "_validationMessage", "severity", "show", "add", "onDidTriggerButton", "button", "indexOf", "fire", "_lastValidationMessage", "_lastSeverity", "hide", "didHide", "reason", "Other", "clear", "willHide", "_this", "getTitle", "textContent", "innerHTML", "innerText", "getDescription", "description1", "description2", "_widget", "reset", "widget", "busyDelay", "setIfNotSet", "progressBar", "infinite", "stop", "cancel", "leftActionBar", "leftButtons", "map", "index", "_asyncToGenerator", "push", "icon", "label", "rightActionBar", "rightButtons", "inlineActionBar", "inlineButtons", "concreteToggles", "opts", "inputBox", "setEnabled", "setContextKey", "message", "callback", "content", "linkOpenerDelegate", "disposables", "showMessageDecoration", "getSteps", "String", "showDecoration", "styles", "stylesForType", "style", "color", "foreground", "backgroundColor", "background", "border", "marginBottom", "dispose", "QuickPick", "_value", "onDidChangeValueEmitter", "onWillAcceptEmitter", "onDidAcceptEmitter", "onDidCustomEmitter", "_items", "itemsUpdated", "_canSelectMany", "_canAcceptInBackground", "_matchOnDescription", "_matchOnDetail", "_matchOnLabel", "_matchOnLabelMode", "_sortByLabel", "_keepScrollPosition", "_itemActivation", "FIRST", "_activeItems", "activeItemsUpdated", "activeItemsToConfirm", "onDidChangeActiveEmitter", "_selectedItems", "selectedItemsUpdated", "selectedItemsToConfirm", "onDidChangeSelectionEmitter", "onDidTriggerItemButtonEmitter", "onDidTriggerSeparatorButtonEmitter", "valueSelectionUpdated", "_ok", "_customButton", "_focusEventBufferer", "type", "filterValue", "value", "onDidChangeValue", "onWillAccept", "onDidAccept", "onDidChangeActive", "onDidChangeSelection", "onDidTriggerItemButton", "onDidTriggerSeparatorButton", "DEFAULT_ARIA_LABEL", "quickNavigate", "_quickNavigate", "doSetValue", "skipUpdate", "<PERSON><PERSON><PERSON><PERSON>", "list", "trySelectFirst", "aria<PERSON><PERSON><PERSON>", "_a<PERSON><PERSON><PERSON><PERSON>", "placeholder", "_placeholder", "items", "scrollTop", "canSelectMany", "canAcceptInBackground", "matchOnDescription", "matchOnDetail", "matchOnLabel", "matchOnLabelMode", "sortByLabel", "keepScrollPosition", "itemActivation", "activeItems", "selectedItems", "keyMods", "valueSelection", "selection", "getSelection", "start", "end", "_valueSelection", "customButton", "showCustomButton", "customLabel", "_customButtonLabel", "customHover", "_customButtonHover", "hover", "ok", "showOkButton", "hideInput", "_hideInput", "focus", "First", "onDidChange", "getCheckedElements", "handleAccept", "onDidCustom", "wrapEvent", "onDidChangeFocus", "_", "e", "focusedItems", "a", "setSelectedElements", "isMouseEvent", "onChangedCheckedElements", "checkedItems", "onButtonTriggered", "onSeparatorButtonTriggered", "registerQuickNavigation", "inBackground", "veto", "addDisposableListener", "container", "EventType", "KEY_UP", "keyboardEvent", "keyCode", "quickNav<PERSON>eys", "keybindings", "wasTriggerKeyPressed", "some", "k", "chords", "getChords", "shift<PERSON>ey", "ctrl<PERSON>ey", "altKey", "metaKey", "scrollTopBefore", "hasDescription", "visibilities", "checkAll", "_hideCheckAll", "checkBox", "visibleCount", "count", "_hideCountBadge", "setVisibilities", "select", "bufferEvents", "setElements", "shouldLoop", "NONE", "SECOND", "Second", "LAST", "Last", "classList", "contains", "clearFocus", "setFocusedElements", "setCheckedElements", "element", "domFocus", "accept", "InputBox", "_password", "onDidValueChangeEmitter", "password", "remove", "QuickInputHoverDelegate", "configurationService", "hoverService", "options", "getOverrideOptions", "showHoverHint", "isHTMLElement", "includes", "persistence", "hideOnKeyDown", "appearance", "skipFadeInAnimation"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/platform/quickinput/browser/quickInput.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = (this && this.__param) || function (paramIndex, decorator) {\n    return function (target, key) { decorator(target, key, paramIndex); }\n};\nimport * as dom from '../../../base/browser/dom.js';\nimport { StandardKeyboardEvent } from '../../../base/browser/keyboardEvent.js';\nimport { Toggle } from '../../../base/browser/ui/toggle/toggle.js';\nimport { equals } from '../../../base/common/arrays.js';\nimport { TimeoutTimer } from '../../../base/common/async.js';\nimport { Codicon } from '../../../base/common/codicons.js';\nimport { Emitter, EventBufferer } from '../../../base/common/event.js';\nimport { Disposable, DisposableStore } from '../../../base/common/lifecycle.js';\nimport { isIOS } from '../../../base/common/platform.js';\nimport Severity from '../../../base/common/severity.js';\nimport { ThemeIcon } from '../../../base/common/themables.js';\nimport './media/quickInput.css';\nimport { localize } from '../../../nls.js';\nimport { ItemActivation, NO_KEY_MODS, QuickInputButtonLocation, QuickInputHideReason, QuickPickFocus } from '../common/quickInput.js';\nimport { quickInputButtonToAction, renderQuickInputDescription } from './quickInputUtils.js';\nimport { IConfigurationService } from '../../configuration/common/configuration.js';\nimport { IHoverService, WorkbenchHoverDelegate } from '../../hover/browser/hover.js';\nimport { ContextKeyExpr, RawContextKey } from '../../contextkey/common/contextkey.js';\nexport const inQuickInputContextKeyValue = 'inQuickInput';\nexport const InQuickInputContextKey = new RawContextKey(inQuickInputContextKeyValue, false, localize('inQuickInput', \"Whether keyboard focus is inside the quick input control\"));\nexport const inQuickInputContext = ContextKeyExpr.has(inQuickInputContextKeyValue);\nexport const quickInputTypeContextKeyValue = 'quickInputType';\nexport const QuickInputTypeContextKey = new RawContextKey(quickInputTypeContextKeyValue, undefined, localize('quickInputType', \"The type of the currently visible quick input\"));\nexport const endOfQuickInputBoxContextKeyValue = 'cursorAtEndOfQuickInputBox';\nexport const EndOfQuickInputBoxContextKey = new RawContextKey(endOfQuickInputBoxContextKeyValue, false, localize('cursorAtEndOfQuickInputBox', \"Whether the cursor in the quick input is at the end of the input box\"));\nexport const endOfQuickInputBoxContext = ContextKeyExpr.has(endOfQuickInputBoxContextKeyValue);\nexport const backButton = {\n    iconClass: ThemeIcon.asClassName(Codicon.quickInputBack),\n    tooltip: localize('quickInput.back', \"Back\"),\n    handle: -1 // TODO\n};\nclass QuickInput extends Disposable {\n    static { this.noPromptMessage = localize('inputModeEntry', \"Press 'Enter' to confirm your input or 'Escape' to cancel\"); }\n    constructor(ui) {\n        super();\n        this.ui = ui;\n        this._widgetUpdated = false;\n        this.visible = false;\n        this._enabled = true;\n        this._busy = false;\n        this._ignoreFocusOut = false;\n        this._leftButtons = [];\n        this._rightButtons = [];\n        this._inlineButtons = [];\n        this.buttonsUpdated = false;\n        this._toggles = [];\n        this.togglesUpdated = false;\n        this.noValidationMessage = QuickInput.noPromptMessage;\n        this._severity = Severity.Ignore;\n        this.onDidTriggerButtonEmitter = this._register(new Emitter());\n        this.onDidHideEmitter = this._register(new Emitter());\n        this.onWillHideEmitter = this._register(new Emitter());\n        this.onDisposeEmitter = this._register(new Emitter());\n        this.visibleDisposables = this._register(new DisposableStore());\n        this.onDidHide = this.onDidHideEmitter.event;\n    }\n    get title() {\n        return this._title;\n    }\n    set title(title) {\n        this._title = title;\n        this.update();\n    }\n    get description() {\n        return this._description;\n    }\n    set description(description) {\n        this._description = description;\n        this.update();\n    }\n    get step() {\n        return this._steps;\n    }\n    set step(step) {\n        this._steps = step;\n        this.update();\n    }\n    get totalSteps() {\n        return this._totalSteps;\n    }\n    set totalSteps(totalSteps) {\n        this._totalSteps = totalSteps;\n        this.update();\n    }\n    get enabled() {\n        return this._enabled;\n    }\n    set enabled(enabled) {\n        this._enabled = enabled;\n        this.update();\n    }\n    get contextKey() {\n        return this._contextKey;\n    }\n    set contextKey(contextKey) {\n        this._contextKey = contextKey;\n        this.update();\n    }\n    get busy() {\n        return this._busy;\n    }\n    set busy(busy) {\n        this._busy = busy;\n        this.update();\n    }\n    get ignoreFocusOut() {\n        return this._ignoreFocusOut;\n    }\n    set ignoreFocusOut(ignoreFocusOut) {\n        const shouldUpdate = this._ignoreFocusOut !== ignoreFocusOut && !isIOS;\n        this._ignoreFocusOut = ignoreFocusOut && !isIOS;\n        if (shouldUpdate) {\n            this.update();\n        }\n    }\n    get titleButtons() {\n        return this._leftButtons.length\n            ? [...this._leftButtons, this._rightButtons]\n            : this._rightButtons;\n    }\n    get buttons() {\n        return [\n            ...this._leftButtons,\n            ...this._rightButtons,\n            ...this._inlineButtons\n        ];\n    }\n    set buttons(buttons) {\n        this._leftButtons = buttons.filter(b => b === backButton);\n        this._rightButtons = buttons.filter(b => b !== backButton && b.location !== QuickInputButtonLocation.Inline);\n        this._inlineButtons = buttons.filter(b => b.location === QuickInputButtonLocation.Inline);\n        this.buttonsUpdated = true;\n        this.update();\n    }\n    get toggles() {\n        return this._toggles;\n    }\n    set toggles(toggles) {\n        this._toggles = toggles ?? [];\n        this.togglesUpdated = true;\n        this.update();\n    }\n    get validationMessage() {\n        return this._validationMessage;\n    }\n    set validationMessage(validationMessage) {\n        this._validationMessage = validationMessage;\n        this.update();\n    }\n    get severity() {\n        return this._severity;\n    }\n    set severity(severity) {\n        this._severity = severity;\n        this.update();\n    }\n    show() {\n        if (this.visible) {\n            return;\n        }\n        this.visibleDisposables.add(this.ui.onDidTriggerButton(button => {\n            if (this.buttons.indexOf(button) !== -1) {\n                this.onDidTriggerButtonEmitter.fire(button);\n            }\n        }));\n        this.ui.show(this);\n        // update properties in the controller that get reset in the ui.show() call\n        this.visible = true;\n        // This ensures the message/prompt gets rendered\n        this._lastValidationMessage = undefined;\n        // This ensures the input box has the right severity applied\n        this._lastSeverity = undefined;\n        if (this.buttons.length) {\n            // if there are buttons, the ui.show() clears them out of the UI so we should\n            // rerender them.\n            this.buttonsUpdated = true;\n        }\n        if (this.toggles.length) {\n            // if there are toggles, the ui.show() clears them out of the UI so we should\n            // rerender them.\n            this.togglesUpdated = true;\n        }\n        this.update();\n    }\n    hide() {\n        if (!this.visible) {\n            return;\n        }\n        this.ui.hide();\n    }\n    didHide(reason = QuickInputHideReason.Other) {\n        this.visible = false;\n        this.visibleDisposables.clear();\n        this.onDidHideEmitter.fire({ reason });\n    }\n    willHide(reason = QuickInputHideReason.Other) {\n        this.onWillHideEmitter.fire({ reason });\n    }\n    update() {\n        if (!this.visible) {\n            return;\n        }\n        const title = this.getTitle();\n        if (title && this.ui.title.textContent !== title) {\n            this.ui.title.textContent = title;\n        }\n        else if (!title && this.ui.title.innerHTML !== '&nbsp;') {\n            this.ui.title.innerText = '\\u00a0';\n        }\n        const description = this.getDescription();\n        if (this.ui.description1.textContent !== description) {\n            this.ui.description1.textContent = description;\n        }\n        if (this.ui.description2.textContent !== description) {\n            this.ui.description2.textContent = description;\n        }\n        if (this._widgetUpdated) {\n            this._widgetUpdated = false;\n            if (this._widget) {\n                dom.reset(this.ui.widget, this._widget);\n            }\n            else {\n                dom.reset(this.ui.widget);\n            }\n        }\n        if (this.busy && !this.busyDelay) {\n            this.busyDelay = new TimeoutTimer();\n            this.busyDelay.setIfNotSet(() => {\n                if (this.visible) {\n                    this.ui.progressBar.infinite();\n                }\n            }, 800);\n        }\n        if (!this.busy && this.busyDelay) {\n            this.ui.progressBar.stop();\n            this.busyDelay.cancel();\n            this.busyDelay = undefined;\n        }\n        if (this.buttonsUpdated) {\n            this.buttonsUpdated = false;\n            this.ui.leftActionBar.clear();\n            const leftButtons = this._leftButtons\n                .map((button, index) => quickInputButtonToAction(button, `id-${index}`, async () => this.onDidTriggerButtonEmitter.fire(button)));\n            this.ui.leftActionBar.push(leftButtons, { icon: true, label: false });\n            this.ui.rightActionBar.clear();\n            const rightButtons = this._rightButtons\n                .map((button, index) => quickInputButtonToAction(button, `id-${index}`, async () => this.onDidTriggerButtonEmitter.fire(button)));\n            this.ui.rightActionBar.push(rightButtons, { icon: true, label: false });\n            this.ui.inlineActionBar.clear();\n            const inlineButtons = this._inlineButtons\n                .map((button, index) => quickInputButtonToAction(button, `id-${index}`, async () => this.onDidTriggerButtonEmitter.fire(button)));\n            this.ui.inlineActionBar.push(inlineButtons, { icon: true, label: false });\n        }\n        if (this.togglesUpdated) {\n            this.togglesUpdated = false;\n            // HACK: Filter out toggles here that are not concrete Toggle objects. This is to workaround\n            // a layering issue as quick input's interface is in common but Toggle is in browser and\n            // it requires a HTMLElement on its interface\n            const concreteToggles = this.toggles?.filter(opts => opts instanceof Toggle) ?? [];\n            this.ui.inputBox.toggles = concreteToggles;\n        }\n        this.ui.ignoreFocusOut = this.ignoreFocusOut;\n        this.ui.setEnabled(this.enabled);\n        this.ui.setContextKey(this.contextKey);\n        const validationMessage = this.validationMessage || this.noValidationMessage;\n        if (this._lastValidationMessage !== validationMessage) {\n            this._lastValidationMessage = validationMessage;\n            dom.reset(this.ui.message);\n            renderQuickInputDescription(validationMessage, this.ui.message, {\n                callback: (content) => {\n                    this.ui.linkOpenerDelegate(content);\n                },\n                disposables: this.visibleDisposables,\n            });\n        }\n        if (this._lastSeverity !== this.severity) {\n            this._lastSeverity = this.severity;\n            this.showMessageDecoration(this.severity);\n        }\n    }\n    getTitle() {\n        if (this.title && this.step) {\n            return `${this.title} (${this.getSteps()})`;\n        }\n        if (this.title) {\n            return this.title;\n        }\n        if (this.step) {\n            return this.getSteps();\n        }\n        return '';\n    }\n    getDescription() {\n        return this.description || '';\n    }\n    getSteps() {\n        if (this.step && this.totalSteps) {\n            return localize('quickInput.steps', \"{0}/{1}\", this.step, this.totalSteps);\n        }\n        if (this.step) {\n            return String(this.step);\n        }\n        return '';\n    }\n    showMessageDecoration(severity) {\n        this.ui.inputBox.showDecoration(severity);\n        if (severity !== Severity.Ignore) {\n            const styles = this.ui.inputBox.stylesForType(severity);\n            this.ui.message.style.color = styles.foreground ? `${styles.foreground}` : '';\n            this.ui.message.style.backgroundColor = styles.background ? `${styles.background}` : '';\n            this.ui.message.style.border = styles.border ? `1px solid ${styles.border}` : '';\n            this.ui.message.style.marginBottom = '-2px';\n        }\n        else {\n            this.ui.message.style.color = '';\n            this.ui.message.style.backgroundColor = '';\n            this.ui.message.style.border = '';\n            this.ui.message.style.marginBottom = '';\n        }\n    }\n    dispose() {\n        this.hide();\n        this.onDisposeEmitter.fire();\n        super.dispose();\n    }\n}\nexport class QuickPick extends QuickInput {\n    constructor() {\n        super(...arguments);\n        this._value = '';\n        this.onDidChangeValueEmitter = this._register(new Emitter());\n        this.onWillAcceptEmitter = this._register(new Emitter());\n        this.onDidAcceptEmitter = this._register(new Emitter());\n        this.onDidCustomEmitter = this._register(new Emitter());\n        this._items = [];\n        this.itemsUpdated = false;\n        this._canSelectMany = false;\n        this._canAcceptInBackground = false;\n        this._matchOnDescription = false;\n        this._matchOnDetail = false;\n        this._matchOnLabel = true;\n        this._matchOnLabelMode = 'fuzzy';\n        this._sortByLabel = true;\n        this._keepScrollPosition = false;\n        this._itemActivation = ItemActivation.FIRST;\n        this._activeItems = [];\n        this.activeItemsUpdated = false;\n        this.activeItemsToConfirm = [];\n        this.onDidChangeActiveEmitter = this._register(new Emitter());\n        this._selectedItems = [];\n        this.selectedItemsUpdated = false;\n        this.selectedItemsToConfirm = [];\n        this.onDidChangeSelectionEmitter = this._register(new Emitter());\n        this.onDidTriggerItemButtonEmitter = this._register(new Emitter());\n        this.onDidTriggerSeparatorButtonEmitter = this._register(new Emitter());\n        this.valueSelectionUpdated = true;\n        this._ok = 'default';\n        this._customButton = false;\n        this._focusEventBufferer = new EventBufferer();\n        this.type = \"quickPick\" /* QuickInputType.QuickPick */;\n        this.filterValue = (value) => value;\n        this.onDidChangeValue = this.onDidChangeValueEmitter.event;\n        this.onWillAccept = this.onWillAcceptEmitter.event;\n        this.onDidAccept = this.onDidAcceptEmitter.event;\n        this.onDidChangeActive = this.onDidChangeActiveEmitter.event;\n        this.onDidChangeSelection = this.onDidChangeSelectionEmitter.event;\n        this.onDidTriggerItemButton = this.onDidTriggerItemButtonEmitter.event;\n        this.onDidTriggerSeparatorButton = this.onDidTriggerSeparatorButtonEmitter.event;\n    }\n    static { this.DEFAULT_ARIA_LABEL = localize('quickInputBox.ariaLabel', \"Type to narrow down results.\"); }\n    get quickNavigate() {\n        return this._quickNavigate;\n    }\n    set quickNavigate(quickNavigate) {\n        this._quickNavigate = quickNavigate;\n        this.update();\n    }\n    get value() {\n        return this._value;\n    }\n    set value(value) {\n        this.doSetValue(value);\n    }\n    doSetValue(value, skipUpdate) {\n        if (this._value !== value) {\n            this._value = value;\n            if (!skipUpdate) {\n                this.update();\n            }\n            if (this.visible) {\n                const didFilter = this.ui.list.filter(this.filterValue(this._value));\n                if (didFilter) {\n                    this.trySelectFirst();\n                }\n            }\n            this.onDidChangeValueEmitter.fire(this._value);\n        }\n    }\n    set ariaLabel(ariaLabel) {\n        this._ariaLabel = ariaLabel;\n        this.update();\n    }\n    get ariaLabel() {\n        return this._ariaLabel;\n    }\n    get placeholder() {\n        return this._placeholder;\n    }\n    set placeholder(placeholder) {\n        this._placeholder = placeholder;\n        this.update();\n    }\n    get items() {\n        return this._items;\n    }\n    get scrollTop() {\n        return this.ui.list.scrollTop;\n    }\n    set scrollTop(scrollTop) {\n        this.ui.list.scrollTop = scrollTop;\n    }\n    set items(items) {\n        this._items = items;\n        this.itemsUpdated = true;\n        this.update();\n    }\n    get canSelectMany() {\n        return this._canSelectMany;\n    }\n    set canSelectMany(canSelectMany) {\n        this._canSelectMany = canSelectMany;\n        this.update();\n    }\n    get canAcceptInBackground() {\n        return this._canAcceptInBackground;\n    }\n    set canAcceptInBackground(canAcceptInBackground) {\n        this._canAcceptInBackground = canAcceptInBackground;\n    }\n    get matchOnDescription() {\n        return this._matchOnDescription;\n    }\n    set matchOnDescription(matchOnDescription) {\n        this._matchOnDescription = matchOnDescription;\n        this.update();\n    }\n    get matchOnDetail() {\n        return this._matchOnDetail;\n    }\n    set matchOnDetail(matchOnDetail) {\n        this._matchOnDetail = matchOnDetail;\n        this.update();\n    }\n    get matchOnLabel() {\n        return this._matchOnLabel;\n    }\n    set matchOnLabel(matchOnLabel) {\n        this._matchOnLabel = matchOnLabel;\n        this.update();\n    }\n    get matchOnLabelMode() {\n        return this._matchOnLabelMode;\n    }\n    set matchOnLabelMode(matchOnLabelMode) {\n        this._matchOnLabelMode = matchOnLabelMode;\n        this.update();\n    }\n    get sortByLabel() {\n        return this._sortByLabel;\n    }\n    set sortByLabel(sortByLabel) {\n        this._sortByLabel = sortByLabel;\n        this.update();\n    }\n    get keepScrollPosition() {\n        return this._keepScrollPosition;\n    }\n    set keepScrollPosition(keepScrollPosition) {\n        this._keepScrollPosition = keepScrollPosition;\n    }\n    get itemActivation() {\n        return this._itemActivation;\n    }\n    set itemActivation(itemActivation) {\n        this._itemActivation = itemActivation;\n    }\n    get activeItems() {\n        return this._activeItems;\n    }\n    set activeItems(activeItems) {\n        this._activeItems = activeItems;\n        this.activeItemsUpdated = true;\n        this.update();\n    }\n    get selectedItems() {\n        return this._selectedItems;\n    }\n    set selectedItems(selectedItems) {\n        this._selectedItems = selectedItems;\n        this.selectedItemsUpdated = true;\n        this.update();\n    }\n    get keyMods() {\n        if (this._quickNavigate) {\n            // Disable keyMods when quick navigate is enabled\n            // because in this model the interaction is purely\n            // keyboard driven and Ctrl/Alt are typically\n            // pressed and hold during this interaction.\n            return NO_KEY_MODS;\n        }\n        return this.ui.keyMods;\n    }\n    get valueSelection() {\n        const selection = this.ui.inputBox.getSelection();\n        if (!selection) {\n            return undefined;\n        }\n        return [selection.start, selection.end];\n    }\n    set valueSelection(valueSelection) {\n        this._valueSelection = valueSelection;\n        this.valueSelectionUpdated = true;\n        this.update();\n    }\n    get customButton() {\n        return this._customButton;\n    }\n    set customButton(showCustomButton) {\n        this._customButton = showCustomButton;\n        this.update();\n    }\n    get customLabel() {\n        return this._customButtonLabel;\n    }\n    set customLabel(label) {\n        this._customButtonLabel = label;\n        this.update();\n    }\n    get customHover() {\n        return this._customButtonHover;\n    }\n    set customHover(hover) {\n        this._customButtonHover = hover;\n        this.update();\n    }\n    get ok() {\n        return this._ok;\n    }\n    set ok(showOkButton) {\n        this._ok = showOkButton;\n        this.update();\n    }\n    get hideInput() {\n        return !!this._hideInput;\n    }\n    set hideInput(hideInput) {\n        this._hideInput = hideInput;\n        this.update();\n    }\n    trySelectFirst() {\n        if (!this.canSelectMany) {\n            this.ui.list.focus(QuickPickFocus.First);\n        }\n    }\n    show() {\n        if (!this.visible) {\n            this.visibleDisposables.add(this.ui.inputBox.onDidChange(value => {\n                this.doSetValue(value, true /* skip update since this originates from the UI */);\n            }));\n            this.visibleDisposables.add(this.ui.onDidAccept(() => {\n                if (this.canSelectMany) {\n                    // if there are no checked elements, it means that an onDidChangeSelection never fired to overwrite\n                    // `_selectedItems`. In that case, we should emit one with an empty array to ensure that\n                    // `.selectedItems` is up to date.\n                    if (!this.ui.list.getCheckedElements().length) {\n                        this._selectedItems = [];\n                        this.onDidChangeSelectionEmitter.fire(this.selectedItems);\n                    }\n                }\n                else if (this.activeItems[0]) {\n                    // For single-select, we set `selectedItems` to the item that was accepted.\n                    this._selectedItems = [this.activeItems[0]];\n                    this.onDidChangeSelectionEmitter.fire(this.selectedItems);\n                }\n                this.handleAccept(false);\n            }));\n            this.visibleDisposables.add(this.ui.onDidCustom(() => {\n                this.onDidCustomEmitter.fire();\n            }));\n            this.visibleDisposables.add(this._focusEventBufferer.wrapEvent(this.ui.list.onDidChangeFocus, \n            // Only fire the last event\n            (_, e) => e)(focusedItems => {\n                if (this.activeItemsUpdated) {\n                    return; // Expect another event.\n                }\n                if (this.activeItemsToConfirm !== this._activeItems && equals(focusedItems, this._activeItems, (a, b) => a === b)) {\n                    return;\n                }\n                this._activeItems = focusedItems;\n                this.onDidChangeActiveEmitter.fire(focusedItems);\n            }));\n            this.visibleDisposables.add(this.ui.list.onDidChangeSelection(({ items: selectedItems, event }) => {\n                if (this.canSelectMany) {\n                    if (selectedItems.length) {\n                        this.ui.list.setSelectedElements([]);\n                    }\n                    return;\n                }\n                if (this.selectedItemsToConfirm !== this._selectedItems && equals(selectedItems, this._selectedItems, (a, b) => a === b)) {\n                    return;\n                }\n                this._selectedItems = selectedItems;\n                this.onDidChangeSelectionEmitter.fire(selectedItems);\n                if (selectedItems.length) {\n                    this.handleAccept(dom.isMouseEvent(event) && event.button === 1 /* mouse middle click */);\n                }\n            }));\n            this.visibleDisposables.add(this.ui.list.onChangedCheckedElements(checkedItems => {\n                if (!this.canSelectMany || !this.visible) {\n                    return;\n                }\n                if (this.selectedItemsToConfirm !== this._selectedItems && equals(checkedItems, this._selectedItems, (a, b) => a === b)) {\n                    return;\n                }\n                this._selectedItems = checkedItems;\n                this.onDidChangeSelectionEmitter.fire(checkedItems);\n            }));\n            this.visibleDisposables.add(this.ui.list.onButtonTriggered(event => this.onDidTriggerItemButtonEmitter.fire(event)));\n            this.visibleDisposables.add(this.ui.list.onSeparatorButtonTriggered(event => this.onDidTriggerSeparatorButtonEmitter.fire(event)));\n            this.visibleDisposables.add(this.registerQuickNavigation());\n            this.valueSelectionUpdated = true;\n        }\n        super.show(); // TODO: Why have show() bubble up while update() trickles down?\n    }\n    handleAccept(inBackground) {\n        // Figure out veto via `onWillAccept` event\n        let veto = false;\n        this.onWillAcceptEmitter.fire({ veto: () => veto = true });\n        // Continue with `onDidAccept` if no veto\n        if (!veto) {\n            this.onDidAcceptEmitter.fire({ inBackground });\n        }\n    }\n    registerQuickNavigation() {\n        return dom.addDisposableListener(this.ui.container, dom.EventType.KEY_UP, e => {\n            if (this.canSelectMany || !this._quickNavigate) {\n                return;\n            }\n            const keyboardEvent = new StandardKeyboardEvent(e);\n            const keyCode = keyboardEvent.keyCode;\n            // Select element when keys are pressed that signal it\n            const quickNavKeys = this._quickNavigate.keybindings;\n            const wasTriggerKeyPressed = quickNavKeys.some(k => {\n                const chords = k.getChords();\n                if (chords.length > 1) {\n                    return false;\n                }\n                if (chords[0].shiftKey && keyCode === 4 /* KeyCode.Shift */) {\n                    if (keyboardEvent.ctrlKey || keyboardEvent.altKey || keyboardEvent.metaKey) {\n                        return false; // this is an optimistic check for the shift key being used to navigate back in quick input\n                    }\n                    return true;\n                }\n                if (chords[0].altKey && keyCode === 6 /* KeyCode.Alt */) {\n                    return true;\n                }\n                if (chords[0].ctrlKey && keyCode === 5 /* KeyCode.Ctrl */) {\n                    return true;\n                }\n                if (chords[0].metaKey && keyCode === 57 /* KeyCode.Meta */) {\n                    return true;\n                }\n                return false;\n            });\n            if (wasTriggerKeyPressed) {\n                if (this.activeItems[0]) {\n                    this._selectedItems = [this.activeItems[0]];\n                    this.onDidChangeSelectionEmitter.fire(this.selectedItems);\n                    this.handleAccept(false);\n                }\n                // Unset quick navigate after press. It is only valid once\n                // and should not result in any behaviour change afterwards\n                // if the picker remains open because there was no active item\n                this._quickNavigate = undefined;\n            }\n        });\n    }\n    update() {\n        if (!this.visible) {\n            return;\n        }\n        // store the scrollTop before it is reset\n        const scrollTopBefore = this.keepScrollPosition ? this.scrollTop : 0;\n        const hasDescription = !!this.description;\n        const visibilities = {\n            title: !!this.title || !!this.step || !!this.titleButtons.length,\n            description: hasDescription,\n            checkAll: this.canSelectMany && !this._hideCheckAll,\n            checkBox: this.canSelectMany,\n            inputBox: !this._hideInput,\n            progressBar: !this._hideInput || hasDescription,\n            visibleCount: true,\n            count: this.canSelectMany && !this._hideCountBadge,\n            ok: this.ok === 'default' ? this.canSelectMany : this.ok,\n            list: true,\n            message: !!this.validationMessage,\n            customButton: this.customButton\n        };\n        this.ui.setVisibilities(visibilities);\n        super.update();\n        if (this.ui.inputBox.value !== this.value) {\n            this.ui.inputBox.value = this.value;\n        }\n        if (this.valueSelectionUpdated) {\n            this.valueSelectionUpdated = false;\n            this.ui.inputBox.select(this._valueSelection && { start: this._valueSelection[0], end: this._valueSelection[1] });\n        }\n        if (this.ui.inputBox.placeholder !== (this.placeholder || '')) {\n            this.ui.inputBox.placeholder = (this.placeholder || '');\n        }\n        let ariaLabel = this.ariaLabel;\n        // Only set aria label to the input box placeholder if we actually have an input box.\n        if (!ariaLabel && visibilities.inputBox) {\n            ariaLabel = this.placeholder || QuickPick.DEFAULT_ARIA_LABEL;\n            // If we have a title, include it in the aria label.\n            if (this.title) {\n                ariaLabel += ` - ${this.title}`;\n            }\n        }\n        if (this.ui.list.ariaLabel !== ariaLabel) {\n            this.ui.list.ariaLabel = ariaLabel ?? null;\n        }\n        this.ui.list.matchOnDescription = this.matchOnDescription;\n        this.ui.list.matchOnDetail = this.matchOnDetail;\n        this.ui.list.matchOnLabel = this.matchOnLabel;\n        this.ui.list.matchOnLabelMode = this.matchOnLabelMode;\n        this.ui.list.sortByLabel = this.sortByLabel;\n        if (this.itemsUpdated) {\n            this.itemsUpdated = false;\n            this._focusEventBufferer.bufferEvents(() => {\n                this.ui.list.setElements(this.items);\n                // We want focus to exist in the list if there are items so that space can be used to toggle\n                this.ui.list.shouldLoop = !this.canSelectMany;\n                this.ui.list.filter(this.filterValue(this.ui.inputBox.value));\n                switch (this._itemActivation) {\n                    case ItemActivation.NONE:\n                        this._itemActivation = ItemActivation.FIRST; // only valid once, then unset\n                        break;\n                    case ItemActivation.SECOND:\n                        this.ui.list.focus(QuickPickFocus.Second);\n                        this._itemActivation = ItemActivation.FIRST; // only valid once, then unset\n                        break;\n                    case ItemActivation.LAST:\n                        this.ui.list.focus(QuickPickFocus.Last);\n                        this._itemActivation = ItemActivation.FIRST; // only valid once, then unset\n                        break;\n                    default:\n                        this.trySelectFirst();\n                        break;\n                }\n            });\n        }\n        if (this.ui.container.classList.contains('show-checkboxes') !== !!this.canSelectMany) {\n            if (this.canSelectMany) {\n                this.ui.list.clearFocus();\n            }\n            else {\n                this.trySelectFirst();\n            }\n        }\n        if (this.activeItemsUpdated) {\n            this.activeItemsUpdated = false;\n            this.activeItemsToConfirm = this._activeItems;\n            this.ui.list.setFocusedElements(this.activeItems);\n            if (this.activeItemsToConfirm === this._activeItems) {\n                this.activeItemsToConfirm = null;\n            }\n        }\n        if (this.selectedItemsUpdated) {\n            this.selectedItemsUpdated = false;\n            this.selectedItemsToConfirm = this._selectedItems;\n            if (this.canSelectMany) {\n                this.ui.list.setCheckedElements(this.selectedItems);\n            }\n            else {\n                this.ui.list.setSelectedElements(this.selectedItems);\n            }\n            if (this.selectedItemsToConfirm === this._selectedItems) {\n                this.selectedItemsToConfirm = null;\n            }\n        }\n        this.ui.customButton.label = this.customLabel || '';\n        this.ui.customButton.element.title = this.customHover || '';\n        if (!visibilities.inputBox) {\n            // we need to move focus into the tree to detect keybindings\n            // properly when the input box is not visible (quick nav)\n            this.ui.list.domFocus();\n            // Focus the first element in the list if multiselect is enabled\n            if (this.canSelectMany) {\n                this.ui.list.focus(QuickPickFocus.First);\n            }\n        }\n        // Set the scroll position to what it was before updating the items\n        if (this.keepScrollPosition) {\n            this.scrollTop = scrollTopBefore;\n        }\n    }\n    focus(focus) {\n        this.ui.list.focus(focus);\n        // To allow things like space to check/uncheck items\n        if (this.canSelectMany) {\n            this.ui.list.domFocus();\n        }\n    }\n    accept(inBackground) {\n        if (inBackground && !this._canAcceptInBackground) {\n            return; // needs to be enabled\n        }\n        if (this.activeItems[0]) {\n            this._selectedItems = [this.activeItems[0]];\n            this.onDidChangeSelectionEmitter.fire(this.selectedItems);\n            this.handleAccept(inBackground ?? false);\n        }\n    }\n}\nexport class InputBox extends QuickInput {\n    constructor() {\n        super(...arguments);\n        this._value = '';\n        this.valueSelectionUpdated = true;\n        this._password = false;\n        this.onDidValueChangeEmitter = this._register(new Emitter());\n        this.onDidAcceptEmitter = this._register(new Emitter());\n        this.type = \"inputBox\" /* QuickInputType.InputBox */;\n        this.onDidChangeValue = this.onDidValueChangeEmitter.event;\n        this.onDidAccept = this.onDidAcceptEmitter.event;\n    }\n    get value() {\n        return this._value;\n    }\n    set value(value) {\n        this._value = value || '';\n        this.update();\n    }\n    get placeholder() {\n        return this._placeholder;\n    }\n    set placeholder(placeholder) {\n        this._placeholder = placeholder;\n        this.update();\n    }\n    get password() {\n        return this._password;\n    }\n    set password(password) {\n        this._password = password;\n        this.update();\n    }\n    show() {\n        if (!this.visible) {\n            this.visibleDisposables.add(this.ui.inputBox.onDidChange(value => {\n                if (value === this.value) {\n                    return;\n                }\n                this._value = value;\n                this.onDidValueChangeEmitter.fire(value);\n            }));\n            this.visibleDisposables.add(this.ui.onDidAccept(() => this.onDidAcceptEmitter.fire()));\n            this.valueSelectionUpdated = true;\n        }\n        super.show();\n    }\n    update() {\n        if (!this.visible) {\n            return;\n        }\n        this.ui.container.classList.remove('hidden-input');\n        const visibilities = {\n            title: !!this.title || !!this.step || !!this.titleButtons.length,\n            description: !!this.description || !!this.step,\n            inputBox: true,\n            message: true,\n            progressBar: true\n        };\n        this.ui.setVisibilities(visibilities);\n        super.update();\n        if (this.ui.inputBox.value !== this.value) {\n            this.ui.inputBox.value = this.value;\n        }\n        if (this.valueSelectionUpdated) {\n            this.valueSelectionUpdated = false;\n            this.ui.inputBox.select(this._valueSelection && { start: this._valueSelection[0], end: this._valueSelection[1] });\n        }\n        if (this.ui.inputBox.placeholder !== (this.placeholder || '')) {\n            this.ui.inputBox.placeholder = (this.placeholder || '');\n        }\n        if (this.ui.inputBox.password !== this.password) {\n            this.ui.inputBox.password = this.password;\n        }\n    }\n}\nlet QuickInputHoverDelegate = class QuickInputHoverDelegate extends WorkbenchHoverDelegate {\n    constructor(configurationService, hoverService) {\n        super('element', false, (options) => this.getOverrideOptions(options), configurationService, hoverService);\n    }\n    getOverrideOptions(options) {\n        // Only show the hover hint if the content is of a decent size\n        const showHoverHint = (dom.isHTMLElement(options.content)\n            ? options.content.textContent ?? ''\n            : typeof options.content === 'string'\n                ? options.content\n                : options.content.value).includes('\\n');\n        return {\n            persistence: {\n                hideOnKeyDown: false,\n            },\n            appearance: {\n                showHoverHint,\n                skipFadeInAnimation: true,\n            },\n        };\n    }\n};\nQuickInputHoverDelegate = __decorate([\n    __param(0, IConfigurationService),\n    __param(1, IHoverService)\n], QuickInputHoverDelegate);\nexport { QuickInputHoverDelegate };\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA,IAAIA,UAAU,GAAI,IAAI,IAAI,IAAI,CAACA,UAAU,IAAK,UAAUC,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAE;EACnF,IAAIC,CAAC,GAAGC,SAAS,CAACC,MAAM;IAAEC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGH,MAAM,GAAGE,IAAI,KAAK,IAAI,GAAGA,IAAI,GAAGK,MAAM,CAACC,wBAAwB,CAACR,MAAM,EAAEC,GAAG,CAAC,GAAGC,IAAI;IAAEO,CAAC;EAC5H,IAAI,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,CAACC,QAAQ,KAAK,UAAU,EAAEL,CAAC,GAAGI,OAAO,CAACC,QAAQ,CAACZ,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,CAAC,CAAC,KAC1H,KAAK,IAAIU,CAAC,GAAGb,UAAU,CAACM,MAAM,GAAG,CAAC,EAAEO,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE,IAAIH,CAAC,GAAGV,UAAU,CAACa,CAAC,CAAC,EAAEN,CAAC,GAAG,CAACH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACH,CAAC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACT,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,GAAGG,CAAC,CAACT,MAAM,EAAEC,GAAG,CAAC,KAAKK,CAAC;EACjJ,OAAOH,CAAC,GAAG,CAAC,IAAIG,CAAC,IAAIC,MAAM,CAACM,cAAc,CAACb,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,EAAEA,CAAC;AACjE,CAAC;AACD,IAAIQ,OAAO,GAAI,IAAI,IAAI,IAAI,CAACA,OAAO,IAAK,UAAUC,UAAU,EAAEC,SAAS,EAAE;EACrE,OAAO,UAAUhB,MAAM,EAAEC,GAAG,EAAE;IAAEe,SAAS,CAAChB,MAAM,EAAEC,GAAG,EAAEc,UAAU,CAAC;EAAE,CAAC;AACzE,CAAC;AACD,OAAO,KAAKE,GAAG,MAAM,8BAA8B;AACnD,SAASC,qBAAqB,QAAQ,wCAAwC;AAC9E,SAASC,MAAM,QAAQ,2CAA2C;AAClE,SAASC,MAAM,QAAQ,gCAAgC;AACvD,SAASC,YAAY,QAAQ,+BAA+B;AAC5D,SAASC,OAAO,QAAQ,kCAAkC;AAC1D,SAASC,OAAO,EAAEC,aAAa,QAAQ,+BAA+B;AACtE,SAASC,UAAU,EAAEC,eAAe,QAAQ,mCAAmC;AAC/E,SAASC,KAAK,QAAQ,kCAAkC;AACxD,OAAOC,QAAQ,MAAM,kCAAkC;AACvD,SAASC,SAAS,QAAQ,mCAAmC;AAC7D,OAAO,wBAAwB;AAC/B,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,cAAc,EAAEC,WAAW,EAAEC,wBAAwB,EAAEC,oBAAoB,EAAEC,cAAc,QAAQ,yBAAyB;AACrI,SAASC,wBAAwB,EAAEC,2BAA2B,QAAQ,sBAAsB;AAC5F,SAASC,qBAAqB,QAAQ,6CAA6C;AACnF,SAASC,aAAa,EAAEC,sBAAsB,QAAQ,8BAA8B;AACpF,SAASC,cAAc,EAAEC,aAAa,QAAQ,uCAAuC;AACrF,OAAO,MAAMC,2BAA2B,GAAG,cAAc;AACzD,OAAO,MAAMC,sBAAsB,GAAG,IAAIF,aAAa,CAACC,2BAA2B,EAAE,KAAK,EAAEb,QAAQ,CAAC,cAAc,EAAE,0DAA0D,CAAC,CAAC;AACjL,OAAO,MAAMe,mBAAmB,GAAGJ,cAAc,CAACK,GAAG,CAACH,2BAA2B,CAAC;AAClF,OAAO,MAAMI,6BAA6B,GAAG,gBAAgB;AAC7D,OAAO,MAAMC,wBAAwB,GAAG,IAAIN,aAAa,CAACK,6BAA6B,EAAEE,SAAS,EAAEnB,QAAQ,CAAC,gBAAgB,EAAE,+CAA+C,CAAC,CAAC;AAChL,OAAO,MAAMoB,iCAAiC,GAAG,4BAA4B;AAC7E,OAAO,MAAMC,4BAA4B,GAAG,IAAIT,aAAa,CAACQ,iCAAiC,EAAE,KAAK,EAAEpB,QAAQ,CAAC,4BAA4B,EAAE,sEAAsE,CAAC,CAAC;AACvN,OAAO,MAAMsB,yBAAyB,GAAGX,cAAc,CAACK,GAAG,CAACI,iCAAiC,CAAC;AAC9F,OAAO,MAAMG,UAAU,GAAG;EACtBC,SAAS,EAAEzB,SAAS,CAAC0B,WAAW,CAACjC,OAAO,CAACkC,cAAc,CAAC;EACxDC,OAAO,EAAE3B,QAAQ,CAAC,iBAAiB,EAAE,MAAM,CAAC;EAC5C4B,MAAM,EAAE,CAAC,CAAC,CAAC;AACf,CAAC;AACD,MAAMC,UAAU,SAASlC,UAAU,CAAC;EAChC;IAAS,IAAI,CAACmC,eAAe,GAAG9B,QAAQ,CAAC,gBAAgB,EAAE,2DAA2D,CAAC;EAAE;EACzH+B,WAAWA,CAACC,EAAE,EAAE;IACZ,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,KAAK,GAAG,KAAK;IAClB,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,YAAY,GAAG,EAAE;IACtB,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,cAAc,GAAG,EAAE;IACxB,IAAI,CAACC,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACC,mBAAmB,GAAGf,UAAU,CAACC,eAAe;IACrD,IAAI,CAACe,SAAS,GAAG/C,QAAQ,CAACgD,MAAM;IAChC,IAAI,CAACC,yBAAyB,GAAG,IAAI,CAACC,SAAS,CAAC,IAAIvD,OAAO,CAAC,CAAC,CAAC;IAC9D,IAAI,CAACwD,gBAAgB,GAAG,IAAI,CAACD,SAAS,CAAC,IAAIvD,OAAO,CAAC,CAAC,CAAC;IACrD,IAAI,CAACyD,iBAAiB,GAAG,IAAI,CAACF,SAAS,CAAC,IAAIvD,OAAO,CAAC,CAAC,CAAC;IACtD,IAAI,CAAC0D,gBAAgB,GAAG,IAAI,CAACH,SAAS,CAAC,IAAIvD,OAAO,CAAC,CAAC,CAAC;IACrD,IAAI,CAAC2D,kBAAkB,GAAG,IAAI,CAACJ,SAAS,CAAC,IAAIpD,eAAe,CAAC,CAAC,CAAC;IAC/D,IAAI,CAACyD,SAAS,GAAG,IAAI,CAACJ,gBAAgB,CAACK,KAAK;EAChD;EACA,IAAIC,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACC,MAAM;EACtB;EACA,IAAID,KAAKA,CAACA,KAAK,EAAE;IACb,IAAI,CAACC,MAAM,GAAGD,KAAK;IACnB,IAAI,CAACE,MAAM,CAAC,CAAC;EACjB;EACA,IAAIC,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACC,YAAY;EAC5B;EACA,IAAID,WAAWA,CAACA,WAAW,EAAE;IACzB,IAAI,CAACC,YAAY,GAAGD,WAAW;IAC/B,IAAI,CAACD,MAAM,CAAC,CAAC;EACjB;EACA,IAAIG,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACC,MAAM;EACtB;EACA,IAAID,IAAIA,CAACA,IAAI,EAAE;IACX,IAAI,CAACC,MAAM,GAAGD,IAAI;IAClB,IAAI,CAACH,MAAM,CAAC,CAAC;EACjB;EACA,IAAIK,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACC,WAAW;EAC3B;EACA,IAAID,UAAUA,CAACA,UAAU,EAAE;IACvB,IAAI,CAACC,WAAW,GAAGD,UAAU;IAC7B,IAAI,CAACL,MAAM,CAAC,CAAC;EACjB;EACA,IAAIO,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC7B,QAAQ;EACxB;EACA,IAAI6B,OAAOA,CAACA,OAAO,EAAE;IACjB,IAAI,CAAC7B,QAAQ,GAAG6B,OAAO;IACvB,IAAI,CAACP,MAAM,CAAC,CAAC;EACjB;EACA,IAAIQ,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACC,WAAW;EAC3B;EACA,IAAID,UAAUA,CAACA,UAAU,EAAE;IACvB,IAAI,CAACC,WAAW,GAAGD,UAAU;IAC7B,IAAI,CAACR,MAAM,CAAC,CAAC;EACjB;EACA,IAAIU,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAAC/B,KAAK;EACrB;EACA,IAAI+B,IAAIA,CAACA,IAAI,EAAE;IACX,IAAI,CAAC/B,KAAK,GAAG+B,IAAI;IACjB,IAAI,CAACV,MAAM,CAAC,CAAC;EACjB;EACA,IAAIW,cAAcA,CAAA,EAAG;IACjB,OAAO,IAAI,CAAC/B,eAAe;EAC/B;EACA,IAAI+B,cAAcA,CAACA,cAAc,EAAE;IAC/B,MAAMC,YAAY,GAAG,IAAI,CAAChC,eAAe,KAAK+B,cAAc,IAAI,CAACvE,KAAK;IACtE,IAAI,CAACwC,eAAe,GAAG+B,cAAc,IAAI,CAACvE,KAAK;IAC/C,IAAIwE,YAAY,EAAE;MACd,IAAI,CAACZ,MAAM,CAAC,CAAC;IACjB;EACJ;EACA,IAAIa,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAAChC,YAAY,CAAC/D,MAAM,GACzB,CAAC,GAAG,IAAI,CAAC+D,YAAY,EAAE,IAAI,CAACC,aAAa,CAAC,GAC1C,IAAI,CAACA,aAAa;EAC5B;EACA,IAAIgC,OAAOA,CAAA,EAAG;IACV,OAAO,CACH,GAAG,IAAI,CAACjC,YAAY,EACpB,GAAG,IAAI,CAACC,aAAa,EACrB,GAAG,IAAI,CAACC,cAAc,CACzB;EACL;EACA,IAAI+B,OAAOA,CAACA,OAAO,EAAE;IACjB,IAAI,CAACjC,YAAY,GAAGiC,OAAO,CAACC,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAKlD,UAAU,CAAC;IACzD,IAAI,CAACgB,aAAa,GAAGgC,OAAO,CAACC,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAKlD,UAAU,IAAIkD,CAAC,CAACC,QAAQ,KAAKvE,wBAAwB,CAACwE,MAAM,CAAC;IAC5G,IAAI,CAACnC,cAAc,GAAG+B,OAAO,CAACC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,QAAQ,KAAKvE,wBAAwB,CAACwE,MAAM,CAAC;IACzF,IAAI,CAAClC,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACgB,MAAM,CAAC,CAAC;EACjB;EACA,IAAImB,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAAClC,QAAQ;EACxB;EACA,IAAIkC,OAAOA,CAACA,OAAO,EAAE;IACjB,IAAI,CAAClC,QAAQ,GAAGkC,OAAO,IAAI,EAAE;IAC7B,IAAI,CAACjC,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACc,MAAM,CAAC,CAAC;EACjB;EACA,IAAIoB,iBAAiBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACC,kBAAkB;EAClC;EACA,IAAID,iBAAiBA,CAACA,iBAAiB,EAAE;IACrC,IAAI,CAACC,kBAAkB,GAAGD,iBAAiB;IAC3C,IAAI,CAACpB,MAAM,CAAC,CAAC;EACjB;EACA,IAAIsB,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAAClC,SAAS;EACzB;EACA,IAAIkC,QAAQA,CAACA,QAAQ,EAAE;IACnB,IAAI,CAAClC,SAAS,GAAGkC,QAAQ;IACzB,IAAI,CAACtB,MAAM,CAAC,CAAC;EACjB;EACAuB,IAAIA,CAAA,EAAG;IACH,IAAI,IAAI,CAAC9C,OAAO,EAAE;MACd;IACJ;IACA,IAAI,CAACkB,kBAAkB,CAAC6B,GAAG,CAAC,IAAI,CAACjD,EAAE,CAACkD,kBAAkB,CAACC,MAAM,IAAI;MAC7D,IAAI,IAAI,CAACZ,OAAO,CAACa,OAAO,CAACD,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;QACrC,IAAI,CAACpC,yBAAyB,CAACsC,IAAI,CAACF,MAAM,CAAC;MAC/C;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,CAACnD,EAAE,CAACgD,IAAI,CAAC,IAAI,CAAC;IAClB;IACA,IAAI,CAAC9C,OAAO,GAAG,IAAI;IACnB;IACA,IAAI,CAACoD,sBAAsB,GAAGnE,SAAS;IACvC;IACA,IAAI,CAACoE,aAAa,GAAGpE,SAAS;IAC9B,IAAI,IAAI,CAACoD,OAAO,CAAChG,MAAM,EAAE;MACrB;MACA;MACA,IAAI,CAACkE,cAAc,GAAG,IAAI;IAC9B;IACA,IAAI,IAAI,CAACmC,OAAO,CAACrG,MAAM,EAAE;MACrB;MACA;MACA,IAAI,CAACoE,cAAc,GAAG,IAAI;IAC9B;IACA,IAAI,CAACc,MAAM,CAAC,CAAC;EACjB;EACA+B,IAAIA,CAAA,EAAG;IACH,IAAI,CAAC,IAAI,CAACtD,OAAO,EAAE;MACf;IACJ;IACA,IAAI,CAACF,EAAE,CAACwD,IAAI,CAAC,CAAC;EAClB;EACAC,OAAOA,CAACC,MAAM,GAAGtF,oBAAoB,CAACuF,KAAK,EAAE;IACzC,IAAI,CAACzD,OAAO,GAAG,KAAK;IACpB,IAAI,CAACkB,kBAAkB,CAACwC,KAAK,CAAC,CAAC;IAC/B,IAAI,CAAC3C,gBAAgB,CAACoC,IAAI,CAAC;MAAEK;IAAO,CAAC,CAAC;EAC1C;EACAG,QAAQA,CAACH,MAAM,GAAGtF,oBAAoB,CAACuF,KAAK,EAAE;IAC1C,IAAI,CAACzC,iBAAiB,CAACmC,IAAI,CAAC;MAAEK;IAAO,CAAC,CAAC;EAC3C;EACAjC,MAAMA,CAAA,EAAG;IAAA,IAAAqC,KAAA;IACL,IAAI,CAAC,IAAI,CAAC5D,OAAO,EAAE;MACf;IACJ;IACA,MAAMqB,KAAK,GAAG,IAAI,CAACwC,QAAQ,CAAC,CAAC;IAC7B,IAAIxC,KAAK,IAAI,IAAI,CAACvB,EAAE,CAACuB,KAAK,CAACyC,WAAW,KAAKzC,KAAK,EAAE;MAC9C,IAAI,CAACvB,EAAE,CAACuB,KAAK,CAACyC,WAAW,GAAGzC,KAAK;IACrC,CAAC,MACI,IAAI,CAACA,KAAK,IAAI,IAAI,CAACvB,EAAE,CAACuB,KAAK,CAAC0C,SAAS,KAAK,QAAQ,EAAE;MACrD,IAAI,CAACjE,EAAE,CAACuB,KAAK,CAAC2C,SAAS,GAAG,QAAQ;IACtC;IACA,MAAMxC,WAAW,GAAG,IAAI,CAACyC,cAAc,CAAC,CAAC;IACzC,IAAI,IAAI,CAACnE,EAAE,CAACoE,YAAY,CAACJ,WAAW,KAAKtC,WAAW,EAAE;MAClD,IAAI,CAAC1B,EAAE,CAACoE,YAAY,CAACJ,WAAW,GAAGtC,WAAW;IAClD;IACA,IAAI,IAAI,CAAC1B,EAAE,CAACqE,YAAY,CAACL,WAAW,KAAKtC,WAAW,EAAE;MAClD,IAAI,CAAC1B,EAAE,CAACqE,YAAY,CAACL,WAAW,GAAGtC,WAAW;IAClD;IACA,IAAI,IAAI,CAACzB,cAAc,EAAE;MACrB,IAAI,CAACA,cAAc,GAAG,KAAK;MAC3B,IAAI,IAAI,CAACqE,OAAO,EAAE;QACdnH,GAAG,CAACoH,KAAK,CAAC,IAAI,CAACvE,EAAE,CAACwE,MAAM,EAAE,IAAI,CAACF,OAAO,CAAC;MAC3C,CAAC,MACI;QACDnH,GAAG,CAACoH,KAAK,CAAC,IAAI,CAACvE,EAAE,CAACwE,MAAM,CAAC;MAC7B;IACJ;IACA,IAAI,IAAI,CAACrC,IAAI,IAAI,CAAC,IAAI,CAACsC,SAAS,EAAE;MAC9B,IAAI,CAACA,SAAS,GAAG,IAAIlH,YAAY,CAAC,CAAC;MACnC,IAAI,CAACkH,SAAS,CAACC,WAAW,CAAC,MAAM;QAC7B,IAAI,IAAI,CAACxE,OAAO,EAAE;UACd,IAAI,CAACF,EAAE,CAAC2E,WAAW,CAACC,QAAQ,CAAC,CAAC;QAClC;MACJ,CAAC,EAAE,GAAG,CAAC;IACX;IACA,IAAI,CAAC,IAAI,CAACzC,IAAI,IAAI,IAAI,CAACsC,SAAS,EAAE;MAC9B,IAAI,CAACzE,EAAE,CAAC2E,WAAW,CAACE,IAAI,CAAC,CAAC;MAC1B,IAAI,CAACJ,SAAS,CAACK,MAAM,CAAC,CAAC;MACvB,IAAI,CAACL,SAAS,GAAGtF,SAAS;IAC9B;IACA,IAAI,IAAI,CAACsB,cAAc,EAAE;MACrB,IAAI,CAACA,cAAc,GAAG,KAAK;MAC3B,IAAI,CAACT,EAAE,CAAC+E,aAAa,CAACnB,KAAK,CAAC,CAAC;MAC7B,MAAMoB,WAAW,GAAG,IAAI,CAAC1E,YAAY,CAChC2E,GAAG,CAAC,CAAC9B,MAAM,EAAE+B,KAAK,KAAK5G,wBAAwB,CAAC6E,MAAM,EAAE,MAAM+B,KAAK,EAAE,eAAAC,iBAAA,CAAE;QAAA,OAAYrB,KAAI,CAAC/C,yBAAyB,CAACsC,IAAI,CAACF,MAAM,CAAC;MAAA,GAAC,CAAC;MACrI,IAAI,CAACnD,EAAE,CAAC+E,aAAa,CAACK,IAAI,CAACJ,WAAW,EAAE;QAAEK,IAAI,EAAE,IAAI;QAAEC,KAAK,EAAE;MAAM,CAAC,CAAC;MACrE,IAAI,CAACtF,EAAE,CAACuF,cAAc,CAAC3B,KAAK,CAAC,CAAC;MAC9B,MAAM4B,YAAY,GAAG,IAAI,CAACjF,aAAa,CAClC0E,GAAG,CAAC,CAAC9B,MAAM,EAAE+B,KAAK,KAAK5G,wBAAwB,CAAC6E,MAAM,EAAE,MAAM+B,KAAK,EAAE,eAAAC,iBAAA,CAAE;QAAA,OAAYrB,KAAI,CAAC/C,yBAAyB,CAACsC,IAAI,CAACF,MAAM,CAAC;MAAA,GAAC,CAAC;MACrI,IAAI,CAACnD,EAAE,CAACuF,cAAc,CAACH,IAAI,CAACI,YAAY,EAAE;QAAEH,IAAI,EAAE,IAAI;QAAEC,KAAK,EAAE;MAAM,CAAC,CAAC;MACvE,IAAI,CAACtF,EAAE,CAACyF,eAAe,CAAC7B,KAAK,CAAC,CAAC;MAC/B,MAAM8B,aAAa,GAAG,IAAI,CAAClF,cAAc,CACpCyE,GAAG,CAAC,CAAC9B,MAAM,EAAE+B,KAAK,KAAK5G,wBAAwB,CAAC6E,MAAM,EAAE,MAAM+B,KAAK,EAAE,eAAAC,iBAAA,CAAE;QAAA,OAAYrB,KAAI,CAAC/C,yBAAyB,CAACsC,IAAI,CAACF,MAAM,CAAC;MAAA,GAAC,CAAC;MACrI,IAAI,CAACnD,EAAE,CAACyF,eAAe,CAACL,IAAI,CAACM,aAAa,EAAE;QAAEL,IAAI,EAAE,IAAI;QAAEC,KAAK,EAAE;MAAM,CAAC,CAAC;IAC7E;IACA,IAAI,IAAI,CAAC3E,cAAc,EAAE;MACrB,IAAI,CAACA,cAAc,GAAG,KAAK;MAC3B;MACA;MACA;MACA,MAAMgF,eAAe,GAAG,IAAI,CAAC/C,OAAO,EAAEJ,MAAM,CAACoD,IAAI,IAAIA,IAAI,YAAYvI,MAAM,CAAC,IAAI,EAAE;MAClF,IAAI,CAAC2C,EAAE,CAAC6F,QAAQ,CAACjD,OAAO,GAAG+C,eAAe;IAC9C;IACA,IAAI,CAAC3F,EAAE,CAACoC,cAAc,GAAG,IAAI,CAACA,cAAc;IAC5C,IAAI,CAACpC,EAAE,CAAC8F,UAAU,CAAC,IAAI,CAAC9D,OAAO,CAAC;IAChC,IAAI,CAAChC,EAAE,CAAC+F,aAAa,CAAC,IAAI,CAAC9D,UAAU,CAAC;IACtC,MAAMY,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,IAAI,IAAI,CAACjC,mBAAmB;IAC5E,IAAI,IAAI,CAAC0C,sBAAsB,KAAKT,iBAAiB,EAAE;MACnD,IAAI,CAACS,sBAAsB,GAAGT,iBAAiB;MAC/C1F,GAAG,CAACoH,KAAK,CAAC,IAAI,CAACvE,EAAE,CAACgG,OAAO,CAAC;MAC1BzH,2BAA2B,CAACsE,iBAAiB,EAAE,IAAI,CAAC7C,EAAE,CAACgG,OAAO,EAAE;QAC5DC,QAAQ,EAAGC,OAAO,IAAK;UACnB,IAAI,CAAClG,EAAE,CAACmG,kBAAkB,CAACD,OAAO,CAAC;QACvC,CAAC;QACDE,WAAW,EAAE,IAAI,CAAChF;MACtB,CAAC,CAAC;IACN;IACA,IAAI,IAAI,CAACmC,aAAa,KAAK,IAAI,CAACR,QAAQ,EAAE;MACtC,IAAI,CAACQ,aAAa,GAAG,IAAI,CAACR,QAAQ;MAClC,IAAI,CAACsD,qBAAqB,CAAC,IAAI,CAACtD,QAAQ,CAAC;IAC7C;EACJ;EACAgB,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACxC,KAAK,IAAI,IAAI,CAACK,IAAI,EAAE;MACzB,OAAO,GAAG,IAAI,CAACL,KAAK,KAAK,IAAI,CAAC+E,QAAQ,CAAC,CAAC,GAAG;IAC/C;IACA,IAAI,IAAI,CAAC/E,KAAK,EAAE;MACZ,OAAO,IAAI,CAACA,KAAK;IACrB;IACA,IAAI,IAAI,CAACK,IAAI,EAAE;MACX,OAAO,IAAI,CAAC0E,QAAQ,CAAC,CAAC;IAC1B;IACA,OAAO,EAAE;EACb;EACAnC,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAACzC,WAAW,IAAI,EAAE;EACjC;EACA4E,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAAC1E,IAAI,IAAI,IAAI,CAACE,UAAU,EAAE;MAC9B,OAAO9D,QAAQ,CAAC,kBAAkB,EAAE,SAAS,EAAE,IAAI,CAAC4D,IAAI,EAAE,IAAI,CAACE,UAAU,CAAC;IAC9E;IACA,IAAI,IAAI,CAACF,IAAI,EAAE;MACX,OAAO2E,MAAM,CAAC,IAAI,CAAC3E,IAAI,CAAC;IAC5B;IACA,OAAO,EAAE;EACb;EACAyE,qBAAqBA,CAACtD,QAAQ,EAAE;IAC5B,IAAI,CAAC/C,EAAE,CAAC6F,QAAQ,CAACW,cAAc,CAACzD,QAAQ,CAAC;IACzC,IAAIA,QAAQ,KAAKjF,QAAQ,CAACgD,MAAM,EAAE;MAC9B,MAAM2F,MAAM,GAAG,IAAI,CAACzG,EAAE,CAAC6F,QAAQ,CAACa,aAAa,CAAC3D,QAAQ,CAAC;MACvD,IAAI,CAAC/C,EAAE,CAACgG,OAAO,CAACW,KAAK,CAACC,KAAK,GAAGH,MAAM,CAACI,UAAU,GAAG,GAAGJ,MAAM,CAACI,UAAU,EAAE,GAAG,EAAE;MAC7E,IAAI,CAAC7G,EAAE,CAACgG,OAAO,CAACW,KAAK,CAACG,eAAe,GAAGL,MAAM,CAACM,UAAU,GAAG,GAAGN,MAAM,CAACM,UAAU,EAAE,GAAG,EAAE;MACvF,IAAI,CAAC/G,EAAE,CAACgG,OAAO,CAACW,KAAK,CAACK,MAAM,GAAGP,MAAM,CAACO,MAAM,GAAG,aAAaP,MAAM,CAACO,MAAM,EAAE,GAAG,EAAE;MAChF,IAAI,CAAChH,EAAE,CAACgG,OAAO,CAACW,KAAK,CAACM,YAAY,GAAG,MAAM;IAC/C,CAAC,MACI;MACD,IAAI,CAACjH,EAAE,CAACgG,OAAO,CAACW,KAAK,CAACC,KAAK,GAAG,EAAE;MAChC,IAAI,CAAC5G,EAAE,CAACgG,OAAO,CAACW,KAAK,CAACG,eAAe,GAAG,EAAE;MAC1C,IAAI,CAAC9G,EAAE,CAACgG,OAAO,CAACW,KAAK,CAACK,MAAM,GAAG,EAAE;MACjC,IAAI,CAAChH,EAAE,CAACgG,OAAO,CAACW,KAAK,CAACM,YAAY,GAAG,EAAE;IAC3C;EACJ;EACAC,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC1D,IAAI,CAAC,CAAC;IACX,IAAI,CAACrC,gBAAgB,CAACkC,IAAI,CAAC,CAAC;IAC5B,KAAK,CAAC6D,OAAO,CAAC,CAAC;EACnB;AACJ;AACA,OAAO,MAAMC,SAAS,SAAStH,UAAU,CAAC;EACtCE,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGzD,SAAS,CAAC;IACnB,IAAI,CAAC8K,MAAM,GAAG,EAAE;IAChB,IAAI,CAACC,uBAAuB,GAAG,IAAI,CAACrG,SAAS,CAAC,IAAIvD,OAAO,CAAC,CAAC,CAAC;IAC5D,IAAI,CAAC6J,mBAAmB,GAAG,IAAI,CAACtG,SAAS,CAAC,IAAIvD,OAAO,CAAC,CAAC,CAAC;IACxD,IAAI,CAAC8J,kBAAkB,GAAG,IAAI,CAACvG,SAAS,CAAC,IAAIvD,OAAO,CAAC,CAAC,CAAC;IACvD,IAAI,CAAC+J,kBAAkB,GAAG,IAAI,CAACxG,SAAS,CAAC,IAAIvD,OAAO,CAAC,CAAC,CAAC;IACvD,IAAI,CAACgK,MAAM,GAAG,EAAE;IAChB,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB,IAAI,CAACC,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACC,sBAAsB,GAAG,KAAK;IACnC,IAAI,CAACC,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACC,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,iBAAiB,GAAG,OAAO;IAChC,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACC,eAAe,GAAGlK,cAAc,CAACmK,KAAK;IAC3C,IAAI,CAACC,YAAY,GAAG,EAAE;IACtB,IAAI,CAACC,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACC,oBAAoB,GAAG,EAAE;IAC9B,IAAI,CAACC,wBAAwB,GAAG,IAAI,CAACxH,SAAS,CAAC,IAAIvD,OAAO,CAAC,CAAC,CAAC;IAC7D,IAAI,CAACgL,cAAc,GAAG,EAAE;IACxB,IAAI,CAACC,oBAAoB,GAAG,KAAK;IACjC,IAAI,CAACC,sBAAsB,GAAG,EAAE;IAChC,IAAI,CAACC,2BAA2B,GAAG,IAAI,CAAC5H,SAAS,CAAC,IAAIvD,OAAO,CAAC,CAAC,CAAC;IAChE,IAAI,CAACoL,6BAA6B,GAAG,IAAI,CAAC7H,SAAS,CAAC,IAAIvD,OAAO,CAAC,CAAC,CAAC;IAClE,IAAI,CAACqL,kCAAkC,GAAG,IAAI,CAAC9H,SAAS,CAAC,IAAIvD,OAAO,CAAC,CAAC,CAAC;IACvE,IAAI,CAACsL,qBAAqB,GAAG,IAAI;IACjC,IAAI,CAACC,GAAG,GAAG,SAAS;IACpB,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACC,mBAAmB,GAAG,IAAIxL,aAAa,CAAC,CAAC;IAC9C,IAAI,CAACyL,IAAI,GAAG,WAAW,CAAC;IACxB,IAAI,CAACC,WAAW,GAAIC,KAAK,IAAKA,KAAK;IACnC,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACjC,uBAAuB,CAAC/F,KAAK;IAC1D,IAAI,CAACiI,YAAY,GAAG,IAAI,CAACjC,mBAAmB,CAAChG,KAAK;IAClD,IAAI,CAACkI,WAAW,GAAG,IAAI,CAACjC,kBAAkB,CAACjG,KAAK;IAChD,IAAI,CAACmI,iBAAiB,GAAG,IAAI,CAACjB,wBAAwB,CAAClH,KAAK;IAC5D,IAAI,CAACoI,oBAAoB,GAAG,IAAI,CAACd,2BAA2B,CAACtH,KAAK;IAClE,IAAI,CAACqI,sBAAsB,GAAG,IAAI,CAACd,6BAA6B,CAACvH,KAAK;IACtE,IAAI,CAACsI,2BAA2B,GAAG,IAAI,CAACd,kCAAkC,CAACxH,KAAK;EACpF;EACA;IAAS,IAAI,CAACuI,kBAAkB,GAAG7L,QAAQ,CAAC,yBAAyB,EAAE,8BAA8B,CAAC;EAAE;EACxG,IAAI8L,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACC,cAAc;EAC9B;EACA,IAAID,aAAaA,CAACA,aAAa,EAAE;IAC7B,IAAI,CAACC,cAAc,GAAGD,aAAa;IACnC,IAAI,CAACrI,MAAM,CAAC,CAAC;EACjB;EACA,IAAI4H,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACjC,MAAM;EACtB;EACA,IAAIiC,KAAKA,CAACA,KAAK,EAAE;IACb,IAAI,CAACW,UAAU,CAACX,KAAK,CAAC;EAC1B;EACAW,UAAUA,CAACX,KAAK,EAAEY,UAAU,EAAE;IAC1B,IAAI,IAAI,CAAC7C,MAAM,KAAKiC,KAAK,EAAE;MACvB,IAAI,CAACjC,MAAM,GAAGiC,KAAK;MACnB,IAAI,CAACY,UAAU,EAAE;QACb,IAAI,CAACxI,MAAM,CAAC,CAAC;MACjB;MACA,IAAI,IAAI,CAACvB,OAAO,EAAE;QACd,MAAMgK,SAAS,GAAG,IAAI,CAAClK,EAAE,CAACmK,IAAI,CAAC3H,MAAM,CAAC,IAAI,CAAC4G,WAAW,CAAC,IAAI,CAAChC,MAAM,CAAC,CAAC;QACpE,IAAI8C,SAAS,EAAE;UACX,IAAI,CAACE,cAAc,CAAC,CAAC;QACzB;MACJ;MACA,IAAI,CAAC/C,uBAAuB,CAAChE,IAAI,CAAC,IAAI,CAAC+D,MAAM,CAAC;IAClD;EACJ;EACA,IAAIiD,SAASA,CAACA,SAAS,EAAE;IACrB,IAAI,CAACC,UAAU,GAAGD,SAAS;IAC3B,IAAI,CAAC5I,MAAM,CAAC,CAAC;EACjB;EACA,IAAI4I,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACC,UAAU;EAC1B;EACA,IAAIC,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACC,YAAY;EAC5B;EACA,IAAID,WAAWA,CAACA,WAAW,EAAE;IACzB,IAAI,CAACC,YAAY,GAAGD,WAAW;IAC/B,IAAI,CAAC9I,MAAM,CAAC,CAAC;EACjB;EACA,IAAIgJ,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAAChD,MAAM;EACtB;EACA,IAAIiD,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAC1K,EAAE,CAACmK,IAAI,CAACO,SAAS;EACjC;EACA,IAAIA,SAASA,CAACA,SAAS,EAAE;IACrB,IAAI,CAAC1K,EAAE,CAACmK,IAAI,CAACO,SAAS,GAAGA,SAAS;EACtC;EACA,IAAID,KAAKA,CAACA,KAAK,EAAE;IACb,IAAI,CAAChD,MAAM,GAAGgD,KAAK;IACnB,IAAI,CAAC/C,YAAY,GAAG,IAAI;IACxB,IAAI,CAACjG,MAAM,CAAC,CAAC;EACjB;EACA,IAAIkJ,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAAChD,cAAc;EAC9B;EACA,IAAIgD,aAAaA,CAACA,aAAa,EAAE;IAC7B,IAAI,CAAChD,cAAc,GAAGgD,aAAa;IACnC,IAAI,CAAClJ,MAAM,CAAC,CAAC;EACjB;EACA,IAAImJ,qBAAqBA,CAAA,EAAG;IACxB,OAAO,IAAI,CAAChD,sBAAsB;EACtC;EACA,IAAIgD,qBAAqBA,CAACA,qBAAqB,EAAE;IAC7C,IAAI,CAAChD,sBAAsB,GAAGgD,qBAAqB;EACvD;EACA,IAAIC,kBAAkBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAAChD,mBAAmB;EACnC;EACA,IAAIgD,kBAAkBA,CAACA,kBAAkB,EAAE;IACvC,IAAI,CAAChD,mBAAmB,GAAGgD,kBAAkB;IAC7C,IAAI,CAACpJ,MAAM,CAAC,CAAC;EACjB;EACA,IAAIqJ,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAAChD,cAAc;EAC9B;EACA,IAAIgD,aAAaA,CAACA,aAAa,EAAE;IAC7B,IAAI,CAAChD,cAAc,GAAGgD,aAAa;IACnC,IAAI,CAACrJ,MAAM,CAAC,CAAC;EACjB;EACA,IAAIsJ,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAAChD,aAAa;EAC7B;EACA,IAAIgD,YAAYA,CAACA,YAAY,EAAE;IAC3B,IAAI,CAAChD,aAAa,GAAGgD,YAAY;IACjC,IAAI,CAACtJ,MAAM,CAAC,CAAC;EACjB;EACA,IAAIuJ,gBAAgBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAAChD,iBAAiB;EACjC;EACA,IAAIgD,gBAAgBA,CAACA,gBAAgB,EAAE;IACnC,IAAI,CAAChD,iBAAiB,GAAGgD,gBAAgB;IACzC,IAAI,CAACvJ,MAAM,CAAC,CAAC;EACjB;EACA,IAAIwJ,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAAChD,YAAY;EAC5B;EACA,IAAIgD,WAAWA,CAACA,WAAW,EAAE;IACzB,IAAI,CAAChD,YAAY,GAAGgD,WAAW;IAC/B,IAAI,CAACxJ,MAAM,CAAC,CAAC;EACjB;EACA,IAAIyJ,kBAAkBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAAChD,mBAAmB;EACnC;EACA,IAAIgD,kBAAkBA,CAACA,kBAAkB,EAAE;IACvC,IAAI,CAAChD,mBAAmB,GAAGgD,kBAAkB;EACjD;EACA,IAAIC,cAAcA,CAAA,EAAG;IACjB,OAAO,IAAI,CAAChD,eAAe;EAC/B;EACA,IAAIgD,cAAcA,CAACA,cAAc,EAAE;IAC/B,IAAI,CAAChD,eAAe,GAAGgD,cAAc;EACzC;EACA,IAAIC,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAAC/C,YAAY;EAC5B;EACA,IAAI+C,WAAWA,CAACA,WAAW,EAAE;IACzB,IAAI,CAAC/C,YAAY,GAAG+C,WAAW;IAC/B,IAAI,CAAC9C,kBAAkB,GAAG,IAAI;IAC9B,IAAI,CAAC7G,MAAM,CAAC,CAAC;EACjB;EACA,IAAI4J,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAAC5C,cAAc;EAC9B;EACA,IAAI4C,aAAaA,CAACA,aAAa,EAAE;IAC7B,IAAI,CAAC5C,cAAc,GAAG4C,aAAa;IACnC,IAAI,CAAC3C,oBAAoB,GAAG,IAAI;IAChC,IAAI,CAACjH,MAAM,CAAC,CAAC;EACjB;EACA,IAAI6J,OAAOA,CAAA,EAAG;IACV,IAAI,IAAI,CAACvB,cAAc,EAAE;MACrB;MACA;MACA;MACA;MACA,OAAO7L,WAAW;IACtB;IACA,OAAO,IAAI,CAAC8B,EAAE,CAACsL,OAAO;EAC1B;EACA,IAAIC,cAAcA,CAAA,EAAG;IACjB,MAAMC,SAAS,GAAG,IAAI,CAACxL,EAAE,CAAC6F,QAAQ,CAAC4F,YAAY,CAAC,CAAC;IACjD,IAAI,CAACD,SAAS,EAAE;MACZ,OAAOrM,SAAS;IACpB;IACA,OAAO,CAACqM,SAAS,CAACE,KAAK,EAAEF,SAAS,CAACG,GAAG,CAAC;EAC3C;EACA,IAAIJ,cAAcA,CAACA,cAAc,EAAE;IAC/B,IAAI,CAACK,eAAe,GAAGL,cAAc;IACrC,IAAI,CAACxC,qBAAqB,GAAG,IAAI;IACjC,IAAI,CAACtH,MAAM,CAAC,CAAC;EACjB;EACA,IAAIoK,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAAC5C,aAAa;EAC7B;EACA,IAAI4C,YAAYA,CAACC,gBAAgB,EAAE;IAC/B,IAAI,CAAC7C,aAAa,GAAG6C,gBAAgB;IACrC,IAAI,CAACrK,MAAM,CAAC,CAAC;EACjB;EACA,IAAIsK,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACC,kBAAkB;EAClC;EACA,IAAID,WAAWA,CAACzG,KAAK,EAAE;IACnB,IAAI,CAAC0G,kBAAkB,GAAG1G,KAAK;IAC/B,IAAI,CAAC7D,MAAM,CAAC,CAAC;EACjB;EACA,IAAIwK,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACC,kBAAkB;EAClC;EACA,IAAID,WAAWA,CAACE,KAAK,EAAE;IACnB,IAAI,CAACD,kBAAkB,GAAGC,KAAK;IAC/B,IAAI,CAAC1K,MAAM,CAAC,CAAC;EACjB;EACA,IAAI2K,EAAEA,CAAA,EAAG;IACL,OAAO,IAAI,CAACpD,GAAG;EACnB;EACA,IAAIoD,EAAEA,CAACC,YAAY,EAAE;IACjB,IAAI,CAACrD,GAAG,GAAGqD,YAAY;IACvB,IAAI,CAAC5K,MAAM,CAAC,CAAC;EACjB;EACA,IAAI6K,SAASA,CAAA,EAAG;IACZ,OAAO,CAAC,CAAC,IAAI,CAACC,UAAU;EAC5B;EACA,IAAID,SAASA,CAACA,SAAS,EAAE;IACrB,IAAI,CAACC,UAAU,GAAGD,SAAS;IAC3B,IAAI,CAAC7K,MAAM,CAAC,CAAC;EACjB;EACA2I,cAAcA,CAAA,EAAG;IACb,IAAI,CAAC,IAAI,CAACO,aAAa,EAAE;MACrB,IAAI,CAAC3K,EAAE,CAACmK,IAAI,CAACqC,KAAK,CAACnO,cAAc,CAACoO,KAAK,CAAC;IAC5C;EACJ;EACAzJ,IAAIA,CAAA,EAAG;IACH,IAAI,CAAC,IAAI,CAAC9C,OAAO,EAAE;MACf,IAAI,CAACkB,kBAAkB,CAAC6B,GAAG,CAAC,IAAI,CAACjD,EAAE,CAAC6F,QAAQ,CAAC6G,WAAW,CAACrD,KAAK,IAAI;QAC9D,IAAI,CAACW,UAAU,CAACX,KAAK,EAAE,IAAI,CAAC,mDAAmD,CAAC;MACpF,CAAC,CAAC,CAAC;MACH,IAAI,CAACjI,kBAAkB,CAAC6B,GAAG,CAAC,IAAI,CAACjD,EAAE,CAACwJ,WAAW,CAAC,MAAM;QAClD,IAAI,IAAI,CAACmB,aAAa,EAAE;UACpB;UACA;UACA;UACA,IAAI,CAAC,IAAI,CAAC3K,EAAE,CAACmK,IAAI,CAACwC,kBAAkB,CAAC,CAAC,CAACpQ,MAAM,EAAE;YAC3C,IAAI,CAACkM,cAAc,GAAG,EAAE;YACxB,IAAI,CAACG,2BAA2B,CAACvF,IAAI,CAAC,IAAI,CAACgI,aAAa,CAAC;UAC7D;QACJ,CAAC,MACI,IAAI,IAAI,CAACD,WAAW,CAAC,CAAC,CAAC,EAAE;UAC1B;UACA,IAAI,CAAC3C,cAAc,GAAG,CAAC,IAAI,CAAC2C,WAAW,CAAC,CAAC,CAAC,CAAC;UAC3C,IAAI,CAACxC,2BAA2B,CAACvF,IAAI,CAAC,IAAI,CAACgI,aAAa,CAAC;QAC7D;QACA,IAAI,CAACuB,YAAY,CAAC,KAAK,CAAC;MAC5B,CAAC,CAAC,CAAC;MACH,IAAI,CAACxL,kBAAkB,CAAC6B,GAAG,CAAC,IAAI,CAACjD,EAAE,CAAC6M,WAAW,CAAC,MAAM;QAClD,IAAI,CAACrF,kBAAkB,CAACnE,IAAI,CAAC,CAAC;MAClC,CAAC,CAAC,CAAC;MACH,IAAI,CAACjC,kBAAkB,CAAC6B,GAAG,CAAC,IAAI,CAACiG,mBAAmB,CAAC4D,SAAS,CAAC,IAAI,CAAC9M,EAAE,CAACmK,IAAI,CAAC4C,gBAAgB;MAC5F;MACA,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAAC,CAACC,YAAY,IAAI;QACzB,IAAI,IAAI,CAAC5E,kBAAkB,EAAE;UACzB,OAAO,CAAC;QACZ;QACA,IAAI,IAAI,CAACC,oBAAoB,KAAK,IAAI,CAACF,YAAY,IAAI/K,MAAM,CAAC4P,YAAY,EAAE,IAAI,CAAC7E,YAAY,EAAE,CAAC8E,CAAC,EAAE1K,CAAC,KAAK0K,CAAC,KAAK1K,CAAC,CAAC,EAAE;UAC/G;QACJ;QACA,IAAI,CAAC4F,YAAY,GAAG6E,YAAY;QAChC,IAAI,CAAC1E,wBAAwB,CAACnF,IAAI,CAAC6J,YAAY,CAAC;MACpD,CAAC,CAAC,CAAC;MACH,IAAI,CAAC9L,kBAAkB,CAAC6B,GAAG,CAAC,IAAI,CAACjD,EAAE,CAACmK,IAAI,CAACT,oBAAoB,CAAC,CAAC;QAAEe,KAAK,EAAEY,aAAa;QAAE/J;MAAM,CAAC,KAAK;QAC/F,IAAI,IAAI,CAACqJ,aAAa,EAAE;UACpB,IAAIU,aAAa,CAAC9O,MAAM,EAAE;YACtB,IAAI,CAACyD,EAAE,CAACmK,IAAI,CAACiD,mBAAmB,CAAC,EAAE,CAAC;UACxC;UACA;QACJ;QACA,IAAI,IAAI,CAACzE,sBAAsB,KAAK,IAAI,CAACF,cAAc,IAAInL,MAAM,CAAC+N,aAAa,EAAE,IAAI,CAAC5C,cAAc,EAAE,CAAC0E,CAAC,EAAE1K,CAAC,KAAK0K,CAAC,KAAK1K,CAAC,CAAC,EAAE;UACtH;QACJ;QACA,IAAI,CAACgG,cAAc,GAAG4C,aAAa;QACnC,IAAI,CAACzC,2BAA2B,CAACvF,IAAI,CAACgI,aAAa,CAAC;QACpD,IAAIA,aAAa,CAAC9O,MAAM,EAAE;UACtB,IAAI,CAACqQ,YAAY,CAACzP,GAAG,CAACkQ,YAAY,CAAC/L,KAAK,CAAC,IAAIA,KAAK,CAAC6B,MAAM,KAAK,CAAC,CAAC,wBAAwB,CAAC;QAC7F;MACJ,CAAC,CAAC,CAAC;MACH,IAAI,CAAC/B,kBAAkB,CAAC6B,GAAG,CAAC,IAAI,CAACjD,EAAE,CAACmK,IAAI,CAACmD,wBAAwB,CAACC,YAAY,IAAI;QAC9E,IAAI,CAAC,IAAI,CAAC5C,aAAa,IAAI,CAAC,IAAI,CAACzK,OAAO,EAAE;UACtC;QACJ;QACA,IAAI,IAAI,CAACyI,sBAAsB,KAAK,IAAI,CAACF,cAAc,IAAInL,MAAM,CAACiQ,YAAY,EAAE,IAAI,CAAC9E,cAAc,EAAE,CAAC0E,CAAC,EAAE1K,CAAC,KAAK0K,CAAC,KAAK1K,CAAC,CAAC,EAAE;UACrH;QACJ;QACA,IAAI,CAACgG,cAAc,GAAG8E,YAAY;QAClC,IAAI,CAAC3E,2BAA2B,CAACvF,IAAI,CAACkK,YAAY,CAAC;MACvD,CAAC,CAAC,CAAC;MACH,IAAI,CAACnM,kBAAkB,CAAC6B,GAAG,CAAC,IAAI,CAACjD,EAAE,CAACmK,IAAI,CAACqD,iBAAiB,CAAClM,KAAK,IAAI,IAAI,CAACuH,6BAA6B,CAACxF,IAAI,CAAC/B,KAAK,CAAC,CAAC,CAAC;MACpH,IAAI,CAACF,kBAAkB,CAAC6B,GAAG,CAAC,IAAI,CAACjD,EAAE,CAACmK,IAAI,CAACsD,0BAA0B,CAACnM,KAAK,IAAI,IAAI,CAACwH,kCAAkC,CAACzF,IAAI,CAAC/B,KAAK,CAAC,CAAC,CAAC;MAClI,IAAI,CAACF,kBAAkB,CAAC6B,GAAG,CAAC,IAAI,CAACyK,uBAAuB,CAAC,CAAC,CAAC;MAC3D,IAAI,CAAC3E,qBAAqB,GAAG,IAAI;IACrC;IACA,KAAK,CAAC/F,IAAI,CAAC,CAAC,CAAC,CAAC;EAClB;EACA4J,YAAYA,CAACe,YAAY,EAAE;IACvB;IACA,IAAIC,IAAI,GAAG,KAAK;IAChB,IAAI,CAACtG,mBAAmB,CAACjE,IAAI,CAAC;MAAEuK,IAAI,EAAEA,CAAA,KAAMA,IAAI,GAAG;IAAK,CAAC,CAAC;IAC1D;IACA,IAAI,CAACA,IAAI,EAAE;MACP,IAAI,CAACrG,kBAAkB,CAAClE,IAAI,CAAC;QAAEsK;MAAa,CAAC,CAAC;IAClD;EACJ;EACAD,uBAAuBA,CAAA,EAAG;IACtB,OAAOvQ,GAAG,CAAC0Q,qBAAqB,CAAC,IAAI,CAAC7N,EAAE,CAAC8N,SAAS,EAAE3Q,GAAG,CAAC4Q,SAAS,CAACC,MAAM,EAAEf,CAAC,IAAI;MAC3E,IAAI,IAAI,CAACtC,aAAa,IAAI,CAAC,IAAI,CAACZ,cAAc,EAAE;QAC5C;MACJ;MACA,MAAMkE,aAAa,GAAG,IAAI7Q,qBAAqB,CAAC6P,CAAC,CAAC;MAClD,MAAMiB,OAAO,GAAGD,aAAa,CAACC,OAAO;MACrC;MACA,MAAMC,YAAY,GAAG,IAAI,CAACpE,cAAc,CAACqE,WAAW;MACpD,MAAMC,oBAAoB,GAAGF,YAAY,CAACG,IAAI,CAACC,CAAC,IAAI;QAChD,MAAMC,MAAM,GAAGD,CAAC,CAACE,SAAS,CAAC,CAAC;QAC5B,IAAID,MAAM,CAACjS,MAAM,GAAG,CAAC,EAAE;UACnB,OAAO,KAAK;QAChB;QACA,IAAIiS,MAAM,CAAC,CAAC,CAAC,CAACE,QAAQ,IAAIR,OAAO,KAAK,CAAC,CAAC,qBAAqB;UACzD,IAAID,aAAa,CAACU,OAAO,IAAIV,aAAa,CAACW,MAAM,IAAIX,aAAa,CAACY,OAAO,EAAE;YACxE,OAAO,KAAK,CAAC,CAAC;UAClB;UACA,OAAO,IAAI;QACf;QACA,IAAIL,MAAM,CAAC,CAAC,CAAC,CAACI,MAAM,IAAIV,OAAO,KAAK,CAAC,CAAC,mBAAmB;UACrD,OAAO,IAAI;QACf;QACA,IAAIM,MAAM,CAAC,CAAC,CAAC,CAACG,OAAO,IAAIT,OAAO,KAAK,CAAC,CAAC,oBAAoB;UACvD,OAAO,IAAI;QACf;QACA,IAAIM,MAAM,CAAC,CAAC,CAAC,CAACK,OAAO,IAAIX,OAAO,KAAK,EAAE,CAAC,oBAAoB;UACxD,OAAO,IAAI;QACf;QACA,OAAO,KAAK;MAChB,CAAC,CAAC;MACF,IAAIG,oBAAoB,EAAE;QACtB,IAAI,IAAI,CAACjD,WAAW,CAAC,CAAC,CAAC,EAAE;UACrB,IAAI,CAAC3C,cAAc,GAAG,CAAC,IAAI,CAAC2C,WAAW,CAAC,CAAC,CAAC,CAAC;UAC3C,IAAI,CAACxC,2BAA2B,CAACvF,IAAI,CAAC,IAAI,CAACgI,aAAa,CAAC;UACzD,IAAI,CAACuB,YAAY,CAAC,KAAK,CAAC;QAC5B;QACA;QACA;QACA;QACA,IAAI,CAAC7C,cAAc,GAAG5K,SAAS;MACnC;IACJ,CAAC,CAAC;EACN;EACAsC,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC,IAAI,CAACvB,OAAO,EAAE;MACf;IACJ;IACA;IACA,MAAM4O,eAAe,GAAG,IAAI,CAAC5D,kBAAkB,GAAG,IAAI,CAACR,SAAS,GAAG,CAAC;IACpE,MAAMqE,cAAc,GAAG,CAAC,CAAC,IAAI,CAACrN,WAAW;IACzC,MAAMsN,YAAY,GAAG;MACjBzN,KAAK,EAAE,CAAC,CAAC,IAAI,CAACA,KAAK,IAAI,CAAC,CAAC,IAAI,CAACK,IAAI,IAAI,CAAC,CAAC,IAAI,CAACU,YAAY,CAAC/F,MAAM;MAChEmF,WAAW,EAAEqN,cAAc;MAC3BE,QAAQ,EAAE,IAAI,CAACtE,aAAa,IAAI,CAAC,IAAI,CAACuE,aAAa;MACnDC,QAAQ,EAAE,IAAI,CAACxE,aAAa;MAC5B9E,QAAQ,EAAE,CAAC,IAAI,CAAC0G,UAAU;MAC1B5H,WAAW,EAAE,CAAC,IAAI,CAAC4H,UAAU,IAAIwC,cAAc;MAC/CK,YAAY,EAAE,IAAI;MAClBC,KAAK,EAAE,IAAI,CAAC1E,aAAa,IAAI,CAAC,IAAI,CAAC2E,eAAe;MAClDlD,EAAE,EAAE,IAAI,CAACA,EAAE,KAAK,SAAS,GAAG,IAAI,CAACzB,aAAa,GAAG,IAAI,CAACyB,EAAE;MACxDjC,IAAI,EAAE,IAAI;MACVnE,OAAO,EAAE,CAAC,CAAC,IAAI,CAACnD,iBAAiB;MACjCgJ,YAAY,EAAE,IAAI,CAACA;IACvB,CAAC;IACD,IAAI,CAAC7L,EAAE,CAACuP,eAAe,CAACP,YAAY,CAAC;IACrC,KAAK,CAACvN,MAAM,CAAC,CAAC;IACd,IAAI,IAAI,CAACzB,EAAE,CAAC6F,QAAQ,CAACwD,KAAK,KAAK,IAAI,CAACA,KAAK,EAAE;MACvC,IAAI,CAACrJ,EAAE,CAAC6F,QAAQ,CAACwD,KAAK,GAAG,IAAI,CAACA,KAAK;IACvC;IACA,IAAI,IAAI,CAACN,qBAAqB,EAAE;MAC5B,IAAI,CAACA,qBAAqB,GAAG,KAAK;MAClC,IAAI,CAAC/I,EAAE,CAAC6F,QAAQ,CAAC2J,MAAM,CAAC,IAAI,CAAC5D,eAAe,IAAI;QAAEF,KAAK,EAAE,IAAI,CAACE,eAAe,CAAC,CAAC,CAAC;QAAED,GAAG,EAAE,IAAI,CAACC,eAAe,CAAC,CAAC;MAAE,CAAC,CAAC;IACrH;IACA,IAAI,IAAI,CAAC5L,EAAE,CAAC6F,QAAQ,CAAC0E,WAAW,MAAM,IAAI,CAACA,WAAW,IAAI,EAAE,CAAC,EAAE;MAC3D,IAAI,CAACvK,EAAE,CAAC6F,QAAQ,CAAC0E,WAAW,GAAI,IAAI,CAACA,WAAW,IAAI,EAAG;IAC3D;IACA,IAAIF,SAAS,GAAG,IAAI,CAACA,SAAS;IAC9B;IACA,IAAI,CAACA,SAAS,IAAI2E,YAAY,CAACnJ,QAAQ,EAAE;MACrCwE,SAAS,GAAG,IAAI,CAACE,WAAW,IAAIpD,SAAS,CAAC0C,kBAAkB;MAC5D;MACA,IAAI,IAAI,CAACtI,KAAK,EAAE;QACZ8I,SAAS,IAAI,MAAM,IAAI,CAAC9I,KAAK,EAAE;MACnC;IACJ;IACA,IAAI,IAAI,CAACvB,EAAE,CAACmK,IAAI,CAACE,SAAS,KAAKA,SAAS,EAAE;MACtC,IAAI,CAACrK,EAAE,CAACmK,IAAI,CAACE,SAAS,GAAGA,SAAS,IAAI,IAAI;IAC9C;IACA,IAAI,CAACrK,EAAE,CAACmK,IAAI,CAACU,kBAAkB,GAAG,IAAI,CAACA,kBAAkB;IACzD,IAAI,CAAC7K,EAAE,CAACmK,IAAI,CAACW,aAAa,GAAG,IAAI,CAACA,aAAa;IAC/C,IAAI,CAAC9K,EAAE,CAACmK,IAAI,CAACY,YAAY,GAAG,IAAI,CAACA,YAAY;IAC7C,IAAI,CAAC/K,EAAE,CAACmK,IAAI,CAACa,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;IACrD,IAAI,CAAChL,EAAE,CAACmK,IAAI,CAACc,WAAW,GAAG,IAAI,CAACA,WAAW;IAC3C,IAAI,IAAI,CAACvD,YAAY,EAAE;MACnB,IAAI,CAACA,YAAY,GAAG,KAAK;MACzB,IAAI,CAACwB,mBAAmB,CAACuG,YAAY,CAAC,MAAM;QACxC,IAAI,CAACzP,EAAE,CAACmK,IAAI,CAACuF,WAAW,CAAC,IAAI,CAACjF,KAAK,CAAC;QACpC;QACA,IAAI,CAACzK,EAAE,CAACmK,IAAI,CAACwF,UAAU,GAAG,CAAC,IAAI,CAAChF,aAAa;QAC7C,IAAI,CAAC3K,EAAE,CAACmK,IAAI,CAAC3H,MAAM,CAAC,IAAI,CAAC4G,WAAW,CAAC,IAAI,CAACpJ,EAAE,CAAC6F,QAAQ,CAACwD,KAAK,CAAC,CAAC;QAC7D,QAAQ,IAAI,CAAClB,eAAe;UACxB,KAAKlK,cAAc,CAAC2R,IAAI;YACpB,IAAI,CAACzH,eAAe,GAAGlK,cAAc,CAACmK,KAAK,CAAC,CAAC;YAC7C;UACJ,KAAKnK,cAAc,CAAC4R,MAAM;YACtB,IAAI,CAAC7P,EAAE,CAACmK,IAAI,CAACqC,KAAK,CAACnO,cAAc,CAACyR,MAAM,CAAC;YACzC,IAAI,CAAC3H,eAAe,GAAGlK,cAAc,CAACmK,KAAK,CAAC,CAAC;YAC7C;UACJ,KAAKnK,cAAc,CAAC8R,IAAI;YACpB,IAAI,CAAC/P,EAAE,CAACmK,IAAI,CAACqC,KAAK,CAACnO,cAAc,CAAC2R,IAAI,CAAC;YACvC,IAAI,CAAC7H,eAAe,GAAGlK,cAAc,CAACmK,KAAK,CAAC,CAAC;YAC7C;UACJ;YACI,IAAI,CAACgC,cAAc,CAAC,CAAC;YACrB;QACR;MACJ,CAAC,CAAC;IACN;IACA,IAAI,IAAI,CAACpK,EAAE,CAAC8N,SAAS,CAACmC,SAAS,CAACC,QAAQ,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,IAAI,CAACvF,aAAa,EAAE;MAClF,IAAI,IAAI,CAACA,aAAa,EAAE;QACpB,IAAI,CAAC3K,EAAE,CAACmK,IAAI,CAACgG,UAAU,CAAC,CAAC;MAC7B,CAAC,MACI;QACD,IAAI,CAAC/F,cAAc,CAAC,CAAC;MACzB;IACJ;IACA,IAAI,IAAI,CAAC9B,kBAAkB,EAAE;MACzB,IAAI,CAACA,kBAAkB,GAAG,KAAK;MAC/B,IAAI,CAACC,oBAAoB,GAAG,IAAI,CAACF,YAAY;MAC7C,IAAI,CAACrI,EAAE,CAACmK,IAAI,CAACiG,kBAAkB,CAAC,IAAI,CAAChF,WAAW,CAAC;MACjD,IAAI,IAAI,CAAC7C,oBAAoB,KAAK,IAAI,CAACF,YAAY,EAAE;QACjD,IAAI,CAACE,oBAAoB,GAAG,IAAI;MACpC;IACJ;IACA,IAAI,IAAI,CAACG,oBAAoB,EAAE;MAC3B,IAAI,CAACA,oBAAoB,GAAG,KAAK;MACjC,IAAI,CAACC,sBAAsB,GAAG,IAAI,CAACF,cAAc;MACjD,IAAI,IAAI,CAACkC,aAAa,EAAE;QACpB,IAAI,CAAC3K,EAAE,CAACmK,IAAI,CAACkG,kBAAkB,CAAC,IAAI,CAAChF,aAAa,CAAC;MACvD,CAAC,MACI;QACD,IAAI,CAACrL,EAAE,CAACmK,IAAI,CAACiD,mBAAmB,CAAC,IAAI,CAAC/B,aAAa,CAAC;MACxD;MACA,IAAI,IAAI,CAAC1C,sBAAsB,KAAK,IAAI,CAACF,cAAc,EAAE;QACrD,IAAI,CAACE,sBAAsB,GAAG,IAAI;MACtC;IACJ;IACA,IAAI,CAAC3I,EAAE,CAAC6L,YAAY,CAACvG,KAAK,GAAG,IAAI,CAACyG,WAAW,IAAI,EAAE;IACnD,IAAI,CAAC/L,EAAE,CAAC6L,YAAY,CAACyE,OAAO,CAAC/O,KAAK,GAAG,IAAI,CAAC0K,WAAW,IAAI,EAAE;IAC3D,IAAI,CAAC+C,YAAY,CAACnJ,QAAQ,EAAE;MACxB;MACA;MACA,IAAI,CAAC7F,EAAE,CAACmK,IAAI,CAACoG,QAAQ,CAAC,CAAC;MACvB;MACA,IAAI,IAAI,CAAC5F,aAAa,EAAE;QACpB,IAAI,CAAC3K,EAAE,CAACmK,IAAI,CAACqC,KAAK,CAACnO,cAAc,CAACoO,KAAK,CAAC;MAC5C;IACJ;IACA;IACA,IAAI,IAAI,CAACvB,kBAAkB,EAAE;MACzB,IAAI,CAACR,SAAS,GAAGoE,eAAe;IACpC;EACJ;EACAtC,KAAKA,CAACA,KAAK,EAAE;IACT,IAAI,CAACxM,EAAE,CAACmK,IAAI,CAACqC,KAAK,CAACA,KAAK,CAAC;IACzB;IACA,IAAI,IAAI,CAAC7B,aAAa,EAAE;MACpB,IAAI,CAAC3K,EAAE,CAACmK,IAAI,CAACoG,QAAQ,CAAC,CAAC;IAC3B;EACJ;EACAC,MAAMA,CAAC7C,YAAY,EAAE;IACjB,IAAIA,YAAY,IAAI,CAAC,IAAI,CAAC/F,sBAAsB,EAAE;MAC9C,OAAO,CAAC;IACZ;IACA,IAAI,IAAI,CAACwD,WAAW,CAAC,CAAC,CAAC,EAAE;MACrB,IAAI,CAAC3C,cAAc,GAAG,CAAC,IAAI,CAAC2C,WAAW,CAAC,CAAC,CAAC,CAAC;MAC3C,IAAI,CAACxC,2BAA2B,CAACvF,IAAI,CAAC,IAAI,CAACgI,aAAa,CAAC;MACzD,IAAI,CAACuB,YAAY,CAACe,YAAY,IAAI,KAAK,CAAC;IAC5C;EACJ;AACJ;AACA,OAAO,MAAM8C,QAAQ,SAAS5Q,UAAU,CAAC;EACrCE,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGzD,SAAS,CAAC;IACnB,IAAI,CAAC8K,MAAM,GAAG,EAAE;IAChB,IAAI,CAAC2B,qBAAqB,GAAG,IAAI;IACjC,IAAI,CAAC2H,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,uBAAuB,GAAG,IAAI,CAAC3P,SAAS,CAAC,IAAIvD,OAAO,CAAC,CAAC,CAAC;IAC5D,IAAI,CAAC8J,kBAAkB,GAAG,IAAI,CAACvG,SAAS,CAAC,IAAIvD,OAAO,CAAC,CAAC,CAAC;IACvD,IAAI,CAAC0L,IAAI,GAAG,UAAU,CAAC;IACvB,IAAI,CAACG,gBAAgB,GAAG,IAAI,CAACqH,uBAAuB,CAACrP,KAAK;IAC1D,IAAI,CAACkI,WAAW,GAAG,IAAI,CAACjC,kBAAkB,CAACjG,KAAK;EACpD;EACA,IAAI+H,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACjC,MAAM;EACtB;EACA,IAAIiC,KAAKA,CAACA,KAAK,EAAE;IACb,IAAI,CAACjC,MAAM,GAAGiC,KAAK,IAAI,EAAE;IACzB,IAAI,CAAC5H,MAAM,CAAC,CAAC;EACjB;EACA,IAAI8I,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACC,YAAY;EAC5B;EACA,IAAID,WAAWA,CAACA,WAAW,EAAE;IACzB,IAAI,CAACC,YAAY,GAAGD,WAAW;IAC/B,IAAI,CAAC9I,MAAM,CAAC,CAAC;EACjB;EACA,IAAImP,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACF,SAAS;EACzB;EACA,IAAIE,QAAQA,CAACA,QAAQ,EAAE;IACnB,IAAI,CAACF,SAAS,GAAGE,QAAQ;IACzB,IAAI,CAACnP,MAAM,CAAC,CAAC;EACjB;EACAuB,IAAIA,CAAA,EAAG;IACH,IAAI,CAAC,IAAI,CAAC9C,OAAO,EAAE;MACf,IAAI,CAACkB,kBAAkB,CAAC6B,GAAG,CAAC,IAAI,CAACjD,EAAE,CAAC6F,QAAQ,CAAC6G,WAAW,CAACrD,KAAK,IAAI;QAC9D,IAAIA,KAAK,KAAK,IAAI,CAACA,KAAK,EAAE;UACtB;QACJ;QACA,IAAI,CAACjC,MAAM,GAAGiC,KAAK;QACnB,IAAI,CAACsH,uBAAuB,CAACtN,IAAI,CAACgG,KAAK,CAAC;MAC5C,CAAC,CAAC,CAAC;MACH,IAAI,CAACjI,kBAAkB,CAAC6B,GAAG,CAAC,IAAI,CAACjD,EAAE,CAACwJ,WAAW,CAAC,MAAM,IAAI,CAACjC,kBAAkB,CAAClE,IAAI,CAAC,CAAC,CAAC,CAAC;MACtF,IAAI,CAAC0F,qBAAqB,GAAG,IAAI;IACrC;IACA,KAAK,CAAC/F,IAAI,CAAC,CAAC;EAChB;EACAvB,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC,IAAI,CAACvB,OAAO,EAAE;MACf;IACJ;IACA,IAAI,CAACF,EAAE,CAAC8N,SAAS,CAACmC,SAAS,CAACY,MAAM,CAAC,cAAc,CAAC;IAClD,MAAM7B,YAAY,GAAG;MACjBzN,KAAK,EAAE,CAAC,CAAC,IAAI,CAACA,KAAK,IAAI,CAAC,CAAC,IAAI,CAACK,IAAI,IAAI,CAAC,CAAC,IAAI,CAACU,YAAY,CAAC/F,MAAM;MAChEmF,WAAW,EAAE,CAAC,CAAC,IAAI,CAACA,WAAW,IAAI,CAAC,CAAC,IAAI,CAACE,IAAI;MAC9CiE,QAAQ,EAAE,IAAI;MACdG,OAAO,EAAE,IAAI;MACbrB,WAAW,EAAE;IACjB,CAAC;IACD,IAAI,CAAC3E,EAAE,CAACuP,eAAe,CAACP,YAAY,CAAC;IACrC,KAAK,CAACvN,MAAM,CAAC,CAAC;IACd,IAAI,IAAI,CAACzB,EAAE,CAAC6F,QAAQ,CAACwD,KAAK,KAAK,IAAI,CAACA,KAAK,EAAE;MACvC,IAAI,CAACrJ,EAAE,CAAC6F,QAAQ,CAACwD,KAAK,GAAG,IAAI,CAACA,KAAK;IACvC;IACA,IAAI,IAAI,CAACN,qBAAqB,EAAE;MAC5B,IAAI,CAACA,qBAAqB,GAAG,KAAK;MAClC,IAAI,CAAC/I,EAAE,CAAC6F,QAAQ,CAAC2J,MAAM,CAAC,IAAI,CAAC5D,eAAe,IAAI;QAAEF,KAAK,EAAE,IAAI,CAACE,eAAe,CAAC,CAAC,CAAC;QAAED,GAAG,EAAE,IAAI,CAACC,eAAe,CAAC,CAAC;MAAE,CAAC,CAAC;IACrH;IACA,IAAI,IAAI,CAAC5L,EAAE,CAAC6F,QAAQ,CAAC0E,WAAW,MAAM,IAAI,CAACA,WAAW,IAAI,EAAE,CAAC,EAAE;MAC3D,IAAI,CAACvK,EAAE,CAAC6F,QAAQ,CAAC0E,WAAW,GAAI,IAAI,CAACA,WAAW,IAAI,EAAG;IAC3D;IACA,IAAI,IAAI,CAACvK,EAAE,CAAC6F,QAAQ,CAAC+K,QAAQ,KAAK,IAAI,CAACA,QAAQ,EAAE;MAC7C,IAAI,CAAC5Q,EAAE,CAAC6F,QAAQ,CAAC+K,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC7C;EACJ;AACJ;AACA,IAAIE,uBAAuB,GAAG,MAAMA,uBAAuB,SAASpS,sBAAsB,CAAC;EACvFqB,WAAWA,CAACgR,oBAAoB,EAAEC,YAAY,EAAE;IAC5C,KAAK,CAAC,SAAS,EAAE,KAAK,EAAGC,OAAO,IAAK,IAAI,CAACC,kBAAkB,CAACD,OAAO,CAAC,EAAEF,oBAAoB,EAAEC,YAAY,CAAC;EAC9G;EACAE,kBAAkBA,CAACD,OAAO,EAAE;IACxB;IACA,MAAME,aAAa,GAAG,CAAChU,GAAG,CAACiU,aAAa,CAACH,OAAO,CAAC/K,OAAO,CAAC,GACnD+K,OAAO,CAAC/K,OAAO,CAAClC,WAAW,IAAI,EAAE,GACjC,OAAOiN,OAAO,CAAC/K,OAAO,KAAK,QAAQ,GAC/B+K,OAAO,CAAC/K,OAAO,GACf+K,OAAO,CAAC/K,OAAO,CAACmD,KAAK,EAAEgI,QAAQ,CAAC,IAAI,CAAC;IAC/C,OAAO;MACHC,WAAW,EAAE;QACTC,aAAa,EAAE;MACnB,CAAC;MACDC,UAAU,EAAE;QACRL,aAAa;QACbM,mBAAmB,EAAE;MACzB;IACJ,CAAC;EACL;AACJ,CAAC;AACDX,uBAAuB,GAAG9U,UAAU,CAAC,CACjCgB,OAAO,CAAC,CAAC,EAAEwB,qBAAqB,CAAC,EACjCxB,OAAO,CAAC,CAAC,EAAEyB,aAAa,CAAC,CAC5B,EAAEqS,uBAAuB,CAAC;AAC3B,SAASA,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}