{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { TreeError } from './tree.js';\nimport { splice, tail2 } from '../../../common/arrays.js';\nimport { Delayer } from '../../../common/async.js';\nimport { MicrotaskDelay } from '../../../common/symbols.js';\nimport { LcsDiff } from '../../../common/diff/diff.js';\nimport { Emitter, EventBufferer } from '../../../common/event.js';\nimport { Iterable } from '../../../common/iterator.js';\nexport function isFilterResult(obj) {\n  return typeof obj === 'object' && 'visibility' in obj && 'data' in obj;\n}\nexport function getVisibleState(visibility) {\n  switch (visibility) {\n    case true:\n      return 1 /* TreeVisibility.Visible */;\n    case false:\n      return 0 /* TreeVisibility.Hidden */;\n    default:\n      return visibility;\n  }\n}\nfunction isCollapsibleStateUpdate(update) {\n  return typeof update.collapsible === 'boolean';\n}\nexport class IndexTreeModel {\n  constructor(user, list, rootElement, options = {}) {\n    this.user = user;\n    this.list = list;\n    this.rootRef = [];\n    this.eventBufferer = new EventBufferer();\n    this._onDidChangeCollapseState = new Emitter();\n    this.onDidChangeCollapseState = this.eventBufferer.wrapEvent(this._onDidChangeCollapseState.event);\n    this._onDidChangeRenderNodeCount = new Emitter();\n    this.onDidChangeRenderNodeCount = this.eventBufferer.wrapEvent(this._onDidChangeRenderNodeCount.event);\n    this._onDidSplice = new Emitter();\n    this.onDidSplice = this._onDidSplice.event;\n    this.refilterDelayer = new Delayer(MicrotaskDelay);\n    this.collapseByDefault = typeof options.collapseByDefault === 'undefined' ? false : options.collapseByDefault;\n    this.allowNonCollapsibleParents = options.allowNonCollapsibleParents ?? false;\n    this.filter = options.filter;\n    this.autoExpandSingleChildren = typeof options.autoExpandSingleChildren === 'undefined' ? false : options.autoExpandSingleChildren;\n    this.root = {\n      parent: undefined,\n      element: rootElement,\n      children: [],\n      depth: 0,\n      visibleChildrenCount: 0,\n      visibleChildIndex: -1,\n      collapsible: false,\n      collapsed: false,\n      renderNodeCount: 0,\n      visibility: 1 /* TreeVisibility.Visible */,\n      visible: true,\n      filterData: undefined\n    };\n  }\n  splice(location, deleteCount, toInsert = Iterable.empty(), options = {}) {\n    if (location.length === 0) {\n      throw new TreeError(this.user, 'Invalid tree location');\n    }\n    if (options.diffIdentityProvider) {\n      this.spliceSmart(options.diffIdentityProvider, location, deleteCount, toInsert, options);\n    } else {\n      this.spliceSimple(location, deleteCount, toInsert, options);\n    }\n  }\n  spliceSmart(identity, location, deleteCount, toInsertIterable = Iterable.empty(), options, recurseLevels = options.diffDepth ?? 0) {\n    const {\n      parentNode\n    } = this.getParentNodeWithListIndex(location);\n    if (!parentNode.lastDiffIds) {\n      return this.spliceSimple(location, deleteCount, toInsertIterable, options);\n    }\n    const toInsert = [...toInsertIterable];\n    const index = location[location.length - 1];\n    const diff = new LcsDiff({\n      getElements: () => parentNode.lastDiffIds\n    }, {\n      getElements: () => [...parentNode.children.slice(0, index), ...toInsert, ...parentNode.children.slice(index + deleteCount)].map(e => identity.getId(e.element).toString())\n    }).ComputeDiff(false);\n    // if we were given a 'best effort' diff, use default behavior\n    if (diff.quitEarly) {\n      parentNode.lastDiffIds = undefined;\n      return this.spliceSimple(location, deleteCount, toInsert, options);\n    }\n    const locationPrefix = location.slice(0, -1);\n    const recurseSplice = (fromOriginal, fromModified, count) => {\n      if (recurseLevels > 0) {\n        for (let i = 0; i < count; i++) {\n          fromOriginal--;\n          fromModified--;\n          this.spliceSmart(identity, [...locationPrefix, fromOriginal, 0], Number.MAX_SAFE_INTEGER, toInsert[fromModified].children, options, recurseLevels - 1);\n        }\n      }\n    };\n    let lastStartO = Math.min(parentNode.children.length, index + deleteCount);\n    let lastStartM = toInsert.length;\n    for (const change of diff.changes.sort((a, b) => b.originalStart - a.originalStart)) {\n      recurseSplice(lastStartO, lastStartM, lastStartO - (change.originalStart + change.originalLength));\n      lastStartO = change.originalStart;\n      lastStartM = change.modifiedStart - index;\n      this.spliceSimple([...locationPrefix, lastStartO], change.originalLength, Iterable.slice(toInsert, lastStartM, lastStartM + change.modifiedLength), options);\n    }\n    // at this point, startO === startM === count since any remaining prefix should match\n    recurseSplice(lastStartO, lastStartM, lastStartO);\n  }\n  spliceSimple(location, deleteCount, toInsert = Iterable.empty(), {\n    onDidCreateNode,\n    onDidDeleteNode,\n    diffIdentityProvider\n  }) {\n    const {\n      parentNode,\n      listIndex,\n      revealed,\n      visible\n    } = this.getParentNodeWithListIndex(location);\n    const treeListElementsToInsert = [];\n    const nodesToInsertIterator = Iterable.map(toInsert, el => this.createTreeNode(el, parentNode, parentNode.visible ? 1 /* TreeVisibility.Visible */ : 0 /* TreeVisibility.Hidden */, revealed, treeListElementsToInsert, onDidCreateNode));\n    const lastIndex = location[location.length - 1];\n    // figure out what's the visible child start index right before the\n    // splice point\n    let visibleChildStartIndex = 0;\n    for (let i = lastIndex; i >= 0 && i < parentNode.children.length; i--) {\n      const child = parentNode.children[i];\n      if (child.visible) {\n        visibleChildStartIndex = child.visibleChildIndex;\n        break;\n      }\n    }\n    const nodesToInsert = [];\n    let insertedVisibleChildrenCount = 0;\n    let renderNodeCount = 0;\n    for (const child of nodesToInsertIterator) {\n      nodesToInsert.push(child);\n      renderNodeCount += child.renderNodeCount;\n      if (child.visible) {\n        child.visibleChildIndex = visibleChildStartIndex + insertedVisibleChildrenCount++;\n      }\n    }\n    const deletedNodes = splice(parentNode.children, lastIndex, deleteCount, nodesToInsert);\n    if (!diffIdentityProvider) {\n      parentNode.lastDiffIds = undefined;\n    } else if (parentNode.lastDiffIds) {\n      splice(parentNode.lastDiffIds, lastIndex, deleteCount, nodesToInsert.map(n => diffIdentityProvider.getId(n.element).toString()));\n    } else {\n      parentNode.lastDiffIds = parentNode.children.map(n => diffIdentityProvider.getId(n.element).toString());\n    }\n    // figure out what is the count of deleted visible children\n    let deletedVisibleChildrenCount = 0;\n    for (const child of deletedNodes) {\n      if (child.visible) {\n        deletedVisibleChildrenCount++;\n      }\n    }\n    // and adjust for all visible children after the splice point\n    if (deletedVisibleChildrenCount !== 0) {\n      for (let i = lastIndex + nodesToInsert.length; i < parentNode.children.length; i++) {\n        const child = parentNode.children[i];\n        if (child.visible) {\n          child.visibleChildIndex -= deletedVisibleChildrenCount;\n        }\n      }\n    }\n    // update parent's visible children count\n    parentNode.visibleChildrenCount += insertedVisibleChildrenCount - deletedVisibleChildrenCount;\n    if (revealed && visible) {\n      const visibleDeleteCount = deletedNodes.reduce((r, node) => r + (node.visible ? node.renderNodeCount : 0), 0);\n      this._updateAncestorsRenderNodeCount(parentNode, renderNodeCount - visibleDeleteCount);\n      this.list.splice(listIndex, visibleDeleteCount, treeListElementsToInsert);\n    }\n    if (deletedNodes.length > 0 && onDidDeleteNode) {\n      const visit = node => {\n        onDidDeleteNode(node);\n        node.children.forEach(visit);\n      };\n      deletedNodes.forEach(visit);\n    }\n    this._onDidSplice.fire({\n      insertedNodes: nodesToInsert,\n      deletedNodes\n    });\n    let node = parentNode;\n    while (node) {\n      if (node.visibility === 2 /* TreeVisibility.Recurse */) {\n        // delayed to avoid excessive refiltering, see #135941\n        this.refilterDelayer.trigger(() => this.refilter());\n        break;\n      }\n      node = node.parent;\n    }\n  }\n  rerender(location) {\n    if (location.length === 0) {\n      throw new TreeError(this.user, 'Invalid tree location');\n    }\n    const {\n      node,\n      listIndex,\n      revealed\n    } = this.getTreeNodeWithListIndex(location);\n    if (node.visible && revealed) {\n      this.list.splice(listIndex, 1, [node]);\n    }\n  }\n  has(location) {\n    return this.hasTreeNode(location);\n  }\n  getListIndex(location) {\n    const {\n      listIndex,\n      visible,\n      revealed\n    } = this.getTreeNodeWithListIndex(location);\n    return visible && revealed ? listIndex : -1;\n  }\n  getListRenderCount(location) {\n    return this.getTreeNode(location).renderNodeCount;\n  }\n  isCollapsible(location) {\n    return this.getTreeNode(location).collapsible;\n  }\n  setCollapsible(location, collapsible) {\n    const node = this.getTreeNode(location);\n    if (typeof collapsible === 'undefined') {\n      collapsible = !node.collapsible;\n    }\n    const update = {\n      collapsible\n    };\n    return this.eventBufferer.bufferEvents(() => this._setCollapseState(location, update));\n  }\n  isCollapsed(location) {\n    return this.getTreeNode(location).collapsed;\n  }\n  setCollapsed(location, collapsed, recursive) {\n    const node = this.getTreeNode(location);\n    if (typeof collapsed === 'undefined') {\n      collapsed = !node.collapsed;\n    }\n    const update = {\n      collapsed,\n      recursive: recursive || false\n    };\n    return this.eventBufferer.bufferEvents(() => this._setCollapseState(location, update));\n  }\n  _setCollapseState(location, update) {\n    const {\n      node,\n      listIndex,\n      revealed\n    } = this.getTreeNodeWithListIndex(location);\n    const result = this._setListNodeCollapseState(node, listIndex, revealed, update);\n    if (node !== this.root && this.autoExpandSingleChildren && result && !isCollapsibleStateUpdate(update) && node.collapsible && !node.collapsed && !update.recursive) {\n      let onlyVisibleChildIndex = -1;\n      for (let i = 0; i < node.children.length; i++) {\n        const child = node.children[i];\n        if (child.visible) {\n          if (onlyVisibleChildIndex > -1) {\n            onlyVisibleChildIndex = -1;\n            break;\n          } else {\n            onlyVisibleChildIndex = i;\n          }\n        }\n      }\n      if (onlyVisibleChildIndex > -1) {\n        this._setCollapseState([...location, onlyVisibleChildIndex], update);\n      }\n    }\n    return result;\n  }\n  _setListNodeCollapseState(node, listIndex, revealed, update) {\n    const result = this._setNodeCollapseState(node, update, false);\n    if (!revealed || !node.visible || !result) {\n      return result;\n    }\n    const previousRenderNodeCount = node.renderNodeCount;\n    const toInsert = this.updateNodeAfterCollapseChange(node);\n    const deleteCount = previousRenderNodeCount - (listIndex === -1 ? 0 : 1);\n    this.list.splice(listIndex + 1, deleteCount, toInsert.slice(1));\n    return result;\n  }\n  _setNodeCollapseState(node, update, deep) {\n    let result;\n    if (node === this.root) {\n      result = false;\n    } else {\n      if (isCollapsibleStateUpdate(update)) {\n        result = node.collapsible !== update.collapsible;\n        node.collapsible = update.collapsible;\n      } else if (!node.collapsible) {\n        result = false;\n      } else {\n        result = node.collapsed !== update.collapsed;\n        node.collapsed = update.collapsed;\n      }\n      if (result) {\n        this._onDidChangeCollapseState.fire({\n          node,\n          deep\n        });\n      }\n    }\n    if (!isCollapsibleStateUpdate(update) && update.recursive) {\n      for (const child of node.children) {\n        result = this._setNodeCollapseState(child, update, true) || result;\n      }\n    }\n    return result;\n  }\n  expandTo(location) {\n    this.eventBufferer.bufferEvents(() => {\n      let node = this.getTreeNode(location);\n      while (node.parent) {\n        node = node.parent;\n        location = location.slice(0, location.length - 1);\n        if (node.collapsed) {\n          this._setCollapseState(location, {\n            collapsed: false,\n            recursive: false\n          });\n        }\n      }\n    });\n  }\n  refilter() {\n    const previousRenderNodeCount = this.root.renderNodeCount;\n    const toInsert = this.updateNodeAfterFilterChange(this.root);\n    this.list.splice(0, previousRenderNodeCount, toInsert);\n    this.refilterDelayer.cancel();\n  }\n  createTreeNode(treeElement, parent, parentVisibility, revealed, treeListElements, onDidCreateNode) {\n    const node = {\n      parent,\n      element: treeElement.element,\n      children: [],\n      depth: parent.depth + 1,\n      visibleChildrenCount: 0,\n      visibleChildIndex: -1,\n      collapsible: typeof treeElement.collapsible === 'boolean' ? treeElement.collapsible : typeof treeElement.collapsed !== 'undefined',\n      collapsed: typeof treeElement.collapsed === 'undefined' ? this.collapseByDefault : treeElement.collapsed,\n      renderNodeCount: 1,\n      visibility: 1 /* TreeVisibility.Visible */,\n      visible: true,\n      filterData: undefined\n    };\n    const visibility = this._filterNode(node, parentVisibility);\n    node.visibility = visibility;\n    if (revealed) {\n      treeListElements.push(node);\n    }\n    const childElements = treeElement.children || Iterable.empty();\n    const childRevealed = revealed && visibility !== 0 /* TreeVisibility.Hidden */ && !node.collapsed;\n    let visibleChildrenCount = 0;\n    let renderNodeCount = 1;\n    for (const el of childElements) {\n      const child = this.createTreeNode(el, node, visibility, childRevealed, treeListElements, onDidCreateNode);\n      node.children.push(child);\n      renderNodeCount += child.renderNodeCount;\n      if (child.visible) {\n        child.visibleChildIndex = visibleChildrenCount++;\n      }\n    }\n    if (!this.allowNonCollapsibleParents) {\n      node.collapsible = node.collapsible || node.children.length > 0;\n    }\n    node.visibleChildrenCount = visibleChildrenCount;\n    node.visible = visibility === 2 /* TreeVisibility.Recurse */ ? visibleChildrenCount > 0 : visibility === 1 /* TreeVisibility.Visible */;\n    if (!node.visible) {\n      node.renderNodeCount = 0;\n      if (revealed) {\n        treeListElements.pop();\n      }\n    } else if (!node.collapsed) {\n      node.renderNodeCount = renderNodeCount;\n    }\n    onDidCreateNode?.(node);\n    return node;\n  }\n  updateNodeAfterCollapseChange(node) {\n    const previousRenderNodeCount = node.renderNodeCount;\n    const result = [];\n    this._updateNodeAfterCollapseChange(node, result);\n    this._updateAncestorsRenderNodeCount(node.parent, result.length - previousRenderNodeCount);\n    return result;\n  }\n  _updateNodeAfterCollapseChange(node, result) {\n    if (node.visible === false) {\n      return 0;\n    }\n    result.push(node);\n    node.renderNodeCount = 1;\n    if (!node.collapsed) {\n      for (const child of node.children) {\n        node.renderNodeCount += this._updateNodeAfterCollapseChange(child, result);\n      }\n    }\n    this._onDidChangeRenderNodeCount.fire(node);\n    return node.renderNodeCount;\n  }\n  updateNodeAfterFilterChange(node) {\n    const previousRenderNodeCount = node.renderNodeCount;\n    const result = [];\n    this._updateNodeAfterFilterChange(node, node.visible ? 1 /* TreeVisibility.Visible */ : 0 /* TreeVisibility.Hidden */, result);\n    this._updateAncestorsRenderNodeCount(node.parent, result.length - previousRenderNodeCount);\n    return result;\n  }\n  _updateNodeAfterFilterChange(node, parentVisibility, result, revealed = true) {\n    let visibility;\n    if (node !== this.root) {\n      visibility = this._filterNode(node, parentVisibility);\n      if (visibility === 0 /* TreeVisibility.Hidden */) {\n        node.visible = false;\n        node.renderNodeCount = 0;\n        return false;\n      }\n      if (revealed) {\n        result.push(node);\n      }\n    }\n    const resultStartLength = result.length;\n    node.renderNodeCount = node === this.root ? 0 : 1;\n    let hasVisibleDescendants = false;\n    if (!node.collapsed || visibility !== 0 /* TreeVisibility.Hidden */) {\n      let visibleChildIndex = 0;\n      for (const child of node.children) {\n        hasVisibleDescendants = this._updateNodeAfterFilterChange(child, visibility, result, revealed && !node.collapsed) || hasVisibleDescendants;\n        if (child.visible) {\n          child.visibleChildIndex = visibleChildIndex++;\n        }\n      }\n      node.visibleChildrenCount = visibleChildIndex;\n    } else {\n      node.visibleChildrenCount = 0;\n    }\n    if (node !== this.root) {\n      node.visible = visibility === 2 /* TreeVisibility.Recurse */ ? hasVisibleDescendants : visibility === 1 /* TreeVisibility.Visible */;\n      node.visibility = visibility;\n    }\n    if (!node.visible) {\n      node.renderNodeCount = 0;\n      if (revealed) {\n        result.pop();\n      }\n    } else if (!node.collapsed) {\n      node.renderNodeCount += result.length - resultStartLength;\n    }\n    this._onDidChangeRenderNodeCount.fire(node);\n    return node.visible;\n  }\n  _updateAncestorsRenderNodeCount(node, diff) {\n    if (diff === 0) {\n      return;\n    }\n    while (node) {\n      node.renderNodeCount += diff;\n      this._onDidChangeRenderNodeCount.fire(node);\n      node = node.parent;\n    }\n  }\n  _filterNode(node, parentVisibility) {\n    const result = this.filter ? this.filter.filter(node.element, parentVisibility) : 1 /* TreeVisibility.Visible */;\n    if (typeof result === 'boolean') {\n      node.filterData = undefined;\n      return result ? 1 /* TreeVisibility.Visible */ : 0 /* TreeVisibility.Hidden */;\n    } else if (isFilterResult(result)) {\n      node.filterData = result.data;\n      return getVisibleState(result.visibility);\n    } else {\n      node.filterData = undefined;\n      return getVisibleState(result);\n    }\n  }\n  // cheap\n  hasTreeNode(location, node = this.root) {\n    if (!location || location.length === 0) {\n      return true;\n    }\n    const [index, ...rest] = location;\n    if (index < 0 || index > node.children.length) {\n      return false;\n    }\n    return this.hasTreeNode(rest, node.children[index]);\n  }\n  // cheap\n  getTreeNode(location, node = this.root) {\n    if (!location || location.length === 0) {\n      return node;\n    }\n    const [index, ...rest] = location;\n    if (index < 0 || index > node.children.length) {\n      throw new TreeError(this.user, 'Invalid tree location');\n    }\n    return this.getTreeNode(rest, node.children[index]);\n  }\n  // expensive\n  getTreeNodeWithListIndex(location) {\n    if (location.length === 0) {\n      return {\n        node: this.root,\n        listIndex: -1,\n        revealed: true,\n        visible: false\n      };\n    }\n    const {\n      parentNode,\n      listIndex,\n      revealed,\n      visible\n    } = this.getParentNodeWithListIndex(location);\n    const index = location[location.length - 1];\n    if (index < 0 || index > parentNode.children.length) {\n      throw new TreeError(this.user, 'Invalid tree location');\n    }\n    const node = parentNode.children[index];\n    return {\n      node,\n      listIndex,\n      revealed,\n      visible: visible && node.visible\n    };\n  }\n  getParentNodeWithListIndex(location, node = this.root, listIndex = 0, revealed = true, visible = true) {\n    const [index, ...rest] = location;\n    if (index < 0 || index > node.children.length) {\n      throw new TreeError(this.user, 'Invalid tree location');\n    }\n    // TODO@joao perf!\n    for (let i = 0; i < index; i++) {\n      listIndex += node.children[i].renderNodeCount;\n    }\n    revealed = revealed && !node.collapsed;\n    visible = visible && node.visible;\n    if (rest.length === 0) {\n      return {\n        parentNode: node,\n        listIndex,\n        revealed,\n        visible\n      };\n    }\n    return this.getParentNodeWithListIndex(rest, node.children[index], listIndex + 1, revealed, visible);\n  }\n  getNode(location = []) {\n    return this.getTreeNode(location);\n  }\n  // TODO@joao perf!\n  getNodeLocation(node) {\n    const location = [];\n    let indexTreeNode = node; // typing woes\n    while (indexTreeNode.parent) {\n      location.push(indexTreeNode.parent.children.indexOf(indexTreeNode));\n      indexTreeNode = indexTreeNode.parent;\n    }\n    return location.reverse();\n  }\n  getParentNodeLocation(location) {\n    if (location.length === 0) {\n      return undefined;\n    } else if (location.length === 1) {\n      return [];\n    } else {\n      return tail2(location)[0];\n    }\n  }\n  getFirstElementChild(location) {\n    const node = this.getTreeNode(location);\n    if (node.children.length === 0) {\n      return undefined;\n    }\n    return node.children[0].element;\n  }\n}", "map": {"version": 3, "names": ["TreeError", "splice", "tail2", "<PERSON><PERSON><PERSON>", "MicrotaskDelay", "LcsDiff", "Emitter", "EventBuff<PERSON>", "Iterable", "isFilterResult", "obj", "getVisibleState", "visibility", "isCollapsibleStateUpdate", "update", "collapsible", "IndexTreeModel", "constructor", "user", "list", "rootElement", "options", "rootRef", "event<PERSON><PERSON><PERSON>", "_onDidChangeCollapseState", "onDidChangeCollapseState", "wrapEvent", "event", "_onDidChangeRenderNodeCount", "onDidChangeRenderNodeCount", "_onDidSplice", "onDidSplice", "refi<PERSON><PERSON><PERSON><PERSON>", "collapseByDefault", "allowNonCollapsibleParents", "filter", "autoExpandSingleChildren", "root", "parent", "undefined", "element", "children", "depth", "visible<PERSON><PERSON><PERSON>n<PERSON>ount", "visibleChildIndex", "collapsed", "renderNodeCount", "visible", "filterData", "location", "deleteCount", "toInsert", "empty", "length", "diffIdentityProvider", "spliceSmart", "spliceSimple", "identity", "toInsertIterable", "recurseLevels", "diff<PERSON><PERSON>h", "parentNode", "getParentNodeWithListIndex", "lastDiffIds", "index", "diff", "getElements", "slice", "map", "e", "getId", "toString", "ComputeDiff", "quit<PERSON>arly", "locationPrefix", "recurseSplice", "fromOriginal", "fromModified", "count", "i", "Number", "MAX_SAFE_INTEGER", "lastStartO", "Math", "min", "lastStartM", "change", "changes", "sort", "a", "b", "originalStart", "original<PERSON>ength", "modifiedStart", "<PERSON><PERSON><PERSON><PERSON>", "onDidCreateNode", "onDidDeleteNode", "listIndex", "revealed", "treeListElementsToInsert", "nodesToInsertIterator", "el", "createTreeNode", "lastIndex", "visibleChildStartIndex", "child", "nodesToInsert", "insertedVisibleChildrenCount", "push", "deletedNodes", "n", "deletedVisibleChildrenCount", "visibleDeleteCount", "reduce", "r", "node", "_updateAncestorsRenderNodeCount", "visit", "for<PERSON>ach", "fire", "insertedNodes", "trigger", "refilter", "rerender", "getTreeNodeWithListIndex", "has", "hasTreeNode", "getListIndex", "getListRenderCount", "getTreeNode", "isCollapsible", "setCollapsible", "bufferEvents", "_setCollapseState", "isCollapsed", "setCollapsed", "recursive", "result", "_setListNodeCollapseState", "onlyVisibleChildIndex", "_setNodeCollapseState", "previousRenderNodeCount", "updateNodeAfterCollapseChange", "deep", "expandTo", "updateNodeAfterFilterChange", "cancel", "treeElement", "parentVisibility", "treeListElements", "_filterNode", "childElements", "childRevealed", "pop", "_updateNodeAfterCollapseChange", "_updateNodeAfterFilterChange", "resultStartLength", "hasVisibleDescendants", "data", "rest", "getNode", "getNodeLocation", "indexTreeNode", "indexOf", "reverse", "getParentNodeLocation", "getFirstElementChild"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/browser/ui/tree/indexTreeModel.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { TreeError } from './tree.js';\nimport { splice, tail2 } from '../../../common/arrays.js';\nimport { Delayer } from '../../../common/async.js';\nimport { MicrotaskDelay } from '../../../common/symbols.js';\nimport { LcsDiff } from '../../../common/diff/diff.js';\nimport { Emitter, EventBufferer } from '../../../common/event.js';\nimport { Iterable } from '../../../common/iterator.js';\nexport function isFilterResult(obj) {\n    return typeof obj === 'object' && 'visibility' in obj && 'data' in obj;\n}\nexport function getVisibleState(visibility) {\n    switch (visibility) {\n        case true: return 1 /* TreeVisibility.Visible */;\n        case false: return 0 /* TreeVisibility.Hidden */;\n        default: return visibility;\n    }\n}\nfunction isCollapsibleStateUpdate(update) {\n    return typeof update.collapsible === 'boolean';\n}\nexport class IndexTreeModel {\n    constructor(user, list, rootElement, options = {}) {\n        this.user = user;\n        this.list = list;\n        this.rootRef = [];\n        this.eventBufferer = new EventBufferer();\n        this._onDidChangeCollapseState = new Emitter();\n        this.onDidChangeCollapseState = this.eventBufferer.wrapEvent(this._onDidChangeCollapseState.event);\n        this._onDidChangeRenderNodeCount = new Emitter();\n        this.onDidChangeRenderNodeCount = this.eventBufferer.wrapEvent(this._onDidChangeRenderNodeCount.event);\n        this._onDidSplice = new Emitter();\n        this.onDidSplice = this._onDidSplice.event;\n        this.refilterDelayer = new Delayer(MicrotaskDelay);\n        this.collapseByDefault = typeof options.collapseByDefault === 'undefined' ? false : options.collapseByDefault;\n        this.allowNonCollapsibleParents = options.allowNonCollapsibleParents ?? false;\n        this.filter = options.filter;\n        this.autoExpandSingleChildren = typeof options.autoExpandSingleChildren === 'undefined' ? false : options.autoExpandSingleChildren;\n        this.root = {\n            parent: undefined,\n            element: rootElement,\n            children: [],\n            depth: 0,\n            visibleChildrenCount: 0,\n            visibleChildIndex: -1,\n            collapsible: false,\n            collapsed: false,\n            renderNodeCount: 0,\n            visibility: 1 /* TreeVisibility.Visible */,\n            visible: true,\n            filterData: undefined\n        };\n    }\n    splice(location, deleteCount, toInsert = Iterable.empty(), options = {}) {\n        if (location.length === 0) {\n            throw new TreeError(this.user, 'Invalid tree location');\n        }\n        if (options.diffIdentityProvider) {\n            this.spliceSmart(options.diffIdentityProvider, location, deleteCount, toInsert, options);\n        }\n        else {\n            this.spliceSimple(location, deleteCount, toInsert, options);\n        }\n    }\n    spliceSmart(identity, location, deleteCount, toInsertIterable = Iterable.empty(), options, recurseLevels = options.diffDepth ?? 0) {\n        const { parentNode } = this.getParentNodeWithListIndex(location);\n        if (!parentNode.lastDiffIds) {\n            return this.spliceSimple(location, deleteCount, toInsertIterable, options);\n        }\n        const toInsert = [...toInsertIterable];\n        const index = location[location.length - 1];\n        const diff = new LcsDiff({ getElements: () => parentNode.lastDiffIds }, {\n            getElements: () => [\n                ...parentNode.children.slice(0, index),\n                ...toInsert,\n                ...parentNode.children.slice(index + deleteCount),\n            ].map(e => identity.getId(e.element).toString())\n        }).ComputeDiff(false);\n        // if we were given a 'best effort' diff, use default behavior\n        if (diff.quitEarly) {\n            parentNode.lastDiffIds = undefined;\n            return this.spliceSimple(location, deleteCount, toInsert, options);\n        }\n        const locationPrefix = location.slice(0, -1);\n        const recurseSplice = (fromOriginal, fromModified, count) => {\n            if (recurseLevels > 0) {\n                for (let i = 0; i < count; i++) {\n                    fromOriginal--;\n                    fromModified--;\n                    this.spliceSmart(identity, [...locationPrefix, fromOriginal, 0], Number.MAX_SAFE_INTEGER, toInsert[fromModified].children, options, recurseLevels - 1);\n                }\n            }\n        };\n        let lastStartO = Math.min(parentNode.children.length, index + deleteCount);\n        let lastStartM = toInsert.length;\n        for (const change of diff.changes.sort((a, b) => b.originalStart - a.originalStart)) {\n            recurseSplice(lastStartO, lastStartM, lastStartO - (change.originalStart + change.originalLength));\n            lastStartO = change.originalStart;\n            lastStartM = change.modifiedStart - index;\n            this.spliceSimple([...locationPrefix, lastStartO], change.originalLength, Iterable.slice(toInsert, lastStartM, lastStartM + change.modifiedLength), options);\n        }\n        // at this point, startO === startM === count since any remaining prefix should match\n        recurseSplice(lastStartO, lastStartM, lastStartO);\n    }\n    spliceSimple(location, deleteCount, toInsert = Iterable.empty(), { onDidCreateNode, onDidDeleteNode, diffIdentityProvider }) {\n        const { parentNode, listIndex, revealed, visible } = this.getParentNodeWithListIndex(location);\n        const treeListElementsToInsert = [];\n        const nodesToInsertIterator = Iterable.map(toInsert, el => this.createTreeNode(el, parentNode, parentNode.visible ? 1 /* TreeVisibility.Visible */ : 0 /* TreeVisibility.Hidden */, revealed, treeListElementsToInsert, onDidCreateNode));\n        const lastIndex = location[location.length - 1];\n        // figure out what's the visible child start index right before the\n        // splice point\n        let visibleChildStartIndex = 0;\n        for (let i = lastIndex; i >= 0 && i < parentNode.children.length; i--) {\n            const child = parentNode.children[i];\n            if (child.visible) {\n                visibleChildStartIndex = child.visibleChildIndex;\n                break;\n            }\n        }\n        const nodesToInsert = [];\n        let insertedVisibleChildrenCount = 0;\n        let renderNodeCount = 0;\n        for (const child of nodesToInsertIterator) {\n            nodesToInsert.push(child);\n            renderNodeCount += child.renderNodeCount;\n            if (child.visible) {\n                child.visibleChildIndex = visibleChildStartIndex + insertedVisibleChildrenCount++;\n            }\n        }\n        const deletedNodes = splice(parentNode.children, lastIndex, deleteCount, nodesToInsert);\n        if (!diffIdentityProvider) {\n            parentNode.lastDiffIds = undefined;\n        }\n        else if (parentNode.lastDiffIds) {\n            splice(parentNode.lastDiffIds, lastIndex, deleteCount, nodesToInsert.map(n => diffIdentityProvider.getId(n.element).toString()));\n        }\n        else {\n            parentNode.lastDiffIds = parentNode.children.map(n => diffIdentityProvider.getId(n.element).toString());\n        }\n        // figure out what is the count of deleted visible children\n        let deletedVisibleChildrenCount = 0;\n        for (const child of deletedNodes) {\n            if (child.visible) {\n                deletedVisibleChildrenCount++;\n            }\n        }\n        // and adjust for all visible children after the splice point\n        if (deletedVisibleChildrenCount !== 0) {\n            for (let i = lastIndex + nodesToInsert.length; i < parentNode.children.length; i++) {\n                const child = parentNode.children[i];\n                if (child.visible) {\n                    child.visibleChildIndex -= deletedVisibleChildrenCount;\n                }\n            }\n        }\n        // update parent's visible children count\n        parentNode.visibleChildrenCount += insertedVisibleChildrenCount - deletedVisibleChildrenCount;\n        if (revealed && visible) {\n            const visibleDeleteCount = deletedNodes.reduce((r, node) => r + (node.visible ? node.renderNodeCount : 0), 0);\n            this._updateAncestorsRenderNodeCount(parentNode, renderNodeCount - visibleDeleteCount);\n            this.list.splice(listIndex, visibleDeleteCount, treeListElementsToInsert);\n        }\n        if (deletedNodes.length > 0 && onDidDeleteNode) {\n            const visit = (node) => {\n                onDidDeleteNode(node);\n                node.children.forEach(visit);\n            };\n            deletedNodes.forEach(visit);\n        }\n        this._onDidSplice.fire({ insertedNodes: nodesToInsert, deletedNodes });\n        let node = parentNode;\n        while (node) {\n            if (node.visibility === 2 /* TreeVisibility.Recurse */) {\n                // delayed to avoid excessive refiltering, see #135941\n                this.refilterDelayer.trigger(() => this.refilter());\n                break;\n            }\n            node = node.parent;\n        }\n    }\n    rerender(location) {\n        if (location.length === 0) {\n            throw new TreeError(this.user, 'Invalid tree location');\n        }\n        const { node, listIndex, revealed } = this.getTreeNodeWithListIndex(location);\n        if (node.visible && revealed) {\n            this.list.splice(listIndex, 1, [node]);\n        }\n    }\n    has(location) {\n        return this.hasTreeNode(location);\n    }\n    getListIndex(location) {\n        const { listIndex, visible, revealed } = this.getTreeNodeWithListIndex(location);\n        return visible && revealed ? listIndex : -1;\n    }\n    getListRenderCount(location) {\n        return this.getTreeNode(location).renderNodeCount;\n    }\n    isCollapsible(location) {\n        return this.getTreeNode(location).collapsible;\n    }\n    setCollapsible(location, collapsible) {\n        const node = this.getTreeNode(location);\n        if (typeof collapsible === 'undefined') {\n            collapsible = !node.collapsible;\n        }\n        const update = { collapsible };\n        return this.eventBufferer.bufferEvents(() => this._setCollapseState(location, update));\n    }\n    isCollapsed(location) {\n        return this.getTreeNode(location).collapsed;\n    }\n    setCollapsed(location, collapsed, recursive) {\n        const node = this.getTreeNode(location);\n        if (typeof collapsed === 'undefined') {\n            collapsed = !node.collapsed;\n        }\n        const update = { collapsed, recursive: recursive || false };\n        return this.eventBufferer.bufferEvents(() => this._setCollapseState(location, update));\n    }\n    _setCollapseState(location, update) {\n        const { node, listIndex, revealed } = this.getTreeNodeWithListIndex(location);\n        const result = this._setListNodeCollapseState(node, listIndex, revealed, update);\n        if (node !== this.root && this.autoExpandSingleChildren && result && !isCollapsibleStateUpdate(update) && node.collapsible && !node.collapsed && !update.recursive) {\n            let onlyVisibleChildIndex = -1;\n            for (let i = 0; i < node.children.length; i++) {\n                const child = node.children[i];\n                if (child.visible) {\n                    if (onlyVisibleChildIndex > -1) {\n                        onlyVisibleChildIndex = -1;\n                        break;\n                    }\n                    else {\n                        onlyVisibleChildIndex = i;\n                    }\n                }\n            }\n            if (onlyVisibleChildIndex > -1) {\n                this._setCollapseState([...location, onlyVisibleChildIndex], update);\n            }\n        }\n        return result;\n    }\n    _setListNodeCollapseState(node, listIndex, revealed, update) {\n        const result = this._setNodeCollapseState(node, update, false);\n        if (!revealed || !node.visible || !result) {\n            return result;\n        }\n        const previousRenderNodeCount = node.renderNodeCount;\n        const toInsert = this.updateNodeAfterCollapseChange(node);\n        const deleteCount = previousRenderNodeCount - (listIndex === -1 ? 0 : 1);\n        this.list.splice(listIndex + 1, deleteCount, toInsert.slice(1));\n        return result;\n    }\n    _setNodeCollapseState(node, update, deep) {\n        let result;\n        if (node === this.root) {\n            result = false;\n        }\n        else {\n            if (isCollapsibleStateUpdate(update)) {\n                result = node.collapsible !== update.collapsible;\n                node.collapsible = update.collapsible;\n            }\n            else if (!node.collapsible) {\n                result = false;\n            }\n            else {\n                result = node.collapsed !== update.collapsed;\n                node.collapsed = update.collapsed;\n            }\n            if (result) {\n                this._onDidChangeCollapseState.fire({ node, deep });\n            }\n        }\n        if (!isCollapsibleStateUpdate(update) && update.recursive) {\n            for (const child of node.children) {\n                result = this._setNodeCollapseState(child, update, true) || result;\n            }\n        }\n        return result;\n    }\n    expandTo(location) {\n        this.eventBufferer.bufferEvents(() => {\n            let node = this.getTreeNode(location);\n            while (node.parent) {\n                node = node.parent;\n                location = location.slice(0, location.length - 1);\n                if (node.collapsed) {\n                    this._setCollapseState(location, { collapsed: false, recursive: false });\n                }\n            }\n        });\n    }\n    refilter() {\n        const previousRenderNodeCount = this.root.renderNodeCount;\n        const toInsert = this.updateNodeAfterFilterChange(this.root);\n        this.list.splice(0, previousRenderNodeCount, toInsert);\n        this.refilterDelayer.cancel();\n    }\n    createTreeNode(treeElement, parent, parentVisibility, revealed, treeListElements, onDidCreateNode) {\n        const node = {\n            parent,\n            element: treeElement.element,\n            children: [],\n            depth: parent.depth + 1,\n            visibleChildrenCount: 0,\n            visibleChildIndex: -1,\n            collapsible: typeof treeElement.collapsible === 'boolean' ? treeElement.collapsible : (typeof treeElement.collapsed !== 'undefined'),\n            collapsed: typeof treeElement.collapsed === 'undefined' ? this.collapseByDefault : treeElement.collapsed,\n            renderNodeCount: 1,\n            visibility: 1 /* TreeVisibility.Visible */,\n            visible: true,\n            filterData: undefined\n        };\n        const visibility = this._filterNode(node, parentVisibility);\n        node.visibility = visibility;\n        if (revealed) {\n            treeListElements.push(node);\n        }\n        const childElements = treeElement.children || Iterable.empty();\n        const childRevealed = revealed && visibility !== 0 /* TreeVisibility.Hidden */ && !node.collapsed;\n        let visibleChildrenCount = 0;\n        let renderNodeCount = 1;\n        for (const el of childElements) {\n            const child = this.createTreeNode(el, node, visibility, childRevealed, treeListElements, onDidCreateNode);\n            node.children.push(child);\n            renderNodeCount += child.renderNodeCount;\n            if (child.visible) {\n                child.visibleChildIndex = visibleChildrenCount++;\n            }\n        }\n        if (!this.allowNonCollapsibleParents) {\n            node.collapsible = node.collapsible || node.children.length > 0;\n        }\n        node.visibleChildrenCount = visibleChildrenCount;\n        node.visible = visibility === 2 /* TreeVisibility.Recurse */ ? visibleChildrenCount > 0 : (visibility === 1 /* TreeVisibility.Visible */);\n        if (!node.visible) {\n            node.renderNodeCount = 0;\n            if (revealed) {\n                treeListElements.pop();\n            }\n        }\n        else if (!node.collapsed) {\n            node.renderNodeCount = renderNodeCount;\n        }\n        onDidCreateNode?.(node);\n        return node;\n    }\n    updateNodeAfterCollapseChange(node) {\n        const previousRenderNodeCount = node.renderNodeCount;\n        const result = [];\n        this._updateNodeAfterCollapseChange(node, result);\n        this._updateAncestorsRenderNodeCount(node.parent, result.length - previousRenderNodeCount);\n        return result;\n    }\n    _updateNodeAfterCollapseChange(node, result) {\n        if (node.visible === false) {\n            return 0;\n        }\n        result.push(node);\n        node.renderNodeCount = 1;\n        if (!node.collapsed) {\n            for (const child of node.children) {\n                node.renderNodeCount += this._updateNodeAfterCollapseChange(child, result);\n            }\n        }\n        this._onDidChangeRenderNodeCount.fire(node);\n        return node.renderNodeCount;\n    }\n    updateNodeAfterFilterChange(node) {\n        const previousRenderNodeCount = node.renderNodeCount;\n        const result = [];\n        this._updateNodeAfterFilterChange(node, node.visible ? 1 /* TreeVisibility.Visible */ : 0 /* TreeVisibility.Hidden */, result);\n        this._updateAncestorsRenderNodeCount(node.parent, result.length - previousRenderNodeCount);\n        return result;\n    }\n    _updateNodeAfterFilterChange(node, parentVisibility, result, revealed = true) {\n        let visibility;\n        if (node !== this.root) {\n            visibility = this._filterNode(node, parentVisibility);\n            if (visibility === 0 /* TreeVisibility.Hidden */) {\n                node.visible = false;\n                node.renderNodeCount = 0;\n                return false;\n            }\n            if (revealed) {\n                result.push(node);\n            }\n        }\n        const resultStartLength = result.length;\n        node.renderNodeCount = node === this.root ? 0 : 1;\n        let hasVisibleDescendants = false;\n        if (!node.collapsed || visibility !== 0 /* TreeVisibility.Hidden */) {\n            let visibleChildIndex = 0;\n            for (const child of node.children) {\n                hasVisibleDescendants = this._updateNodeAfterFilterChange(child, visibility, result, revealed && !node.collapsed) || hasVisibleDescendants;\n                if (child.visible) {\n                    child.visibleChildIndex = visibleChildIndex++;\n                }\n            }\n            node.visibleChildrenCount = visibleChildIndex;\n        }\n        else {\n            node.visibleChildrenCount = 0;\n        }\n        if (node !== this.root) {\n            node.visible = visibility === 2 /* TreeVisibility.Recurse */ ? hasVisibleDescendants : (visibility === 1 /* TreeVisibility.Visible */);\n            node.visibility = visibility;\n        }\n        if (!node.visible) {\n            node.renderNodeCount = 0;\n            if (revealed) {\n                result.pop();\n            }\n        }\n        else if (!node.collapsed) {\n            node.renderNodeCount += result.length - resultStartLength;\n        }\n        this._onDidChangeRenderNodeCount.fire(node);\n        return node.visible;\n    }\n    _updateAncestorsRenderNodeCount(node, diff) {\n        if (diff === 0) {\n            return;\n        }\n        while (node) {\n            node.renderNodeCount += diff;\n            this._onDidChangeRenderNodeCount.fire(node);\n            node = node.parent;\n        }\n    }\n    _filterNode(node, parentVisibility) {\n        const result = this.filter ? this.filter.filter(node.element, parentVisibility) : 1 /* TreeVisibility.Visible */;\n        if (typeof result === 'boolean') {\n            node.filterData = undefined;\n            return result ? 1 /* TreeVisibility.Visible */ : 0 /* TreeVisibility.Hidden */;\n        }\n        else if (isFilterResult(result)) {\n            node.filterData = result.data;\n            return getVisibleState(result.visibility);\n        }\n        else {\n            node.filterData = undefined;\n            return getVisibleState(result);\n        }\n    }\n    // cheap\n    hasTreeNode(location, node = this.root) {\n        if (!location || location.length === 0) {\n            return true;\n        }\n        const [index, ...rest] = location;\n        if (index < 0 || index > node.children.length) {\n            return false;\n        }\n        return this.hasTreeNode(rest, node.children[index]);\n    }\n    // cheap\n    getTreeNode(location, node = this.root) {\n        if (!location || location.length === 0) {\n            return node;\n        }\n        const [index, ...rest] = location;\n        if (index < 0 || index > node.children.length) {\n            throw new TreeError(this.user, 'Invalid tree location');\n        }\n        return this.getTreeNode(rest, node.children[index]);\n    }\n    // expensive\n    getTreeNodeWithListIndex(location) {\n        if (location.length === 0) {\n            return { node: this.root, listIndex: -1, revealed: true, visible: false };\n        }\n        const { parentNode, listIndex, revealed, visible } = this.getParentNodeWithListIndex(location);\n        const index = location[location.length - 1];\n        if (index < 0 || index > parentNode.children.length) {\n            throw new TreeError(this.user, 'Invalid tree location');\n        }\n        const node = parentNode.children[index];\n        return { node, listIndex, revealed, visible: visible && node.visible };\n    }\n    getParentNodeWithListIndex(location, node = this.root, listIndex = 0, revealed = true, visible = true) {\n        const [index, ...rest] = location;\n        if (index < 0 || index > node.children.length) {\n            throw new TreeError(this.user, 'Invalid tree location');\n        }\n        // TODO@joao perf!\n        for (let i = 0; i < index; i++) {\n            listIndex += node.children[i].renderNodeCount;\n        }\n        revealed = revealed && !node.collapsed;\n        visible = visible && node.visible;\n        if (rest.length === 0) {\n            return { parentNode: node, listIndex, revealed, visible };\n        }\n        return this.getParentNodeWithListIndex(rest, node.children[index], listIndex + 1, revealed, visible);\n    }\n    getNode(location = []) {\n        return this.getTreeNode(location);\n    }\n    // TODO@joao perf!\n    getNodeLocation(node) {\n        const location = [];\n        let indexTreeNode = node; // typing woes\n        while (indexTreeNode.parent) {\n            location.push(indexTreeNode.parent.children.indexOf(indexTreeNode));\n            indexTreeNode = indexTreeNode.parent;\n        }\n        return location.reverse();\n    }\n    getParentNodeLocation(location) {\n        if (location.length === 0) {\n            return undefined;\n        }\n        else if (location.length === 1) {\n            return [];\n        }\n        else {\n            return tail2(location)[0];\n        }\n    }\n    getFirstElementChild(location) {\n        const node = this.getTreeNode(location);\n        if (node.children.length === 0) {\n            return undefined;\n        }\n        return node.children[0].element;\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,WAAW;AACrC,SAASC,MAAM,EAAEC,KAAK,QAAQ,2BAA2B;AACzD,SAASC,OAAO,QAAQ,0BAA0B;AAClD,SAASC,cAAc,QAAQ,4BAA4B;AAC3D,SAASC,OAAO,QAAQ,8BAA8B;AACtD,SAASC,OAAO,EAAEC,aAAa,QAAQ,0BAA0B;AACjE,SAASC,QAAQ,QAAQ,6BAA6B;AACtD,OAAO,SAASC,cAAcA,CAACC,GAAG,EAAE;EAChC,OAAO,OAAOA,GAAG,KAAK,QAAQ,IAAI,YAAY,IAAIA,GAAG,IAAI,MAAM,IAAIA,GAAG;AAC1E;AACA,OAAO,SAASC,eAAeA,CAACC,UAAU,EAAE;EACxC,QAAQA,UAAU;IACd,KAAK,IAAI;MAAE,OAAO,CAAC,CAAC;IACpB,KAAK,KAAK;MAAE,OAAO,CAAC,CAAC;IACrB;MAAS,OAAOA,UAAU;EAC9B;AACJ;AACA,SAASC,wBAAwBA,CAACC,MAAM,EAAE;EACtC,OAAO,OAAOA,MAAM,CAACC,WAAW,KAAK,SAAS;AAClD;AACA,OAAO,MAAMC,cAAc,CAAC;EACxBC,WAAWA,CAACC,IAAI,EAAEC,IAAI,EAAEC,WAAW,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IAC/C,IAAI,CAACH,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACG,OAAO,GAAG,EAAE;IACjB,IAAI,CAACC,aAAa,GAAG,IAAIhB,aAAa,CAAC,CAAC;IACxC,IAAI,CAACiB,yBAAyB,GAAG,IAAIlB,OAAO,CAAC,CAAC;IAC9C,IAAI,CAACmB,wBAAwB,GAAG,IAAI,CAACF,aAAa,CAACG,SAAS,CAAC,IAAI,CAACF,yBAAyB,CAACG,KAAK,CAAC;IAClG,IAAI,CAACC,2BAA2B,GAAG,IAAItB,OAAO,CAAC,CAAC;IAChD,IAAI,CAACuB,0BAA0B,GAAG,IAAI,CAACN,aAAa,CAACG,SAAS,CAAC,IAAI,CAACE,2BAA2B,CAACD,KAAK,CAAC;IACtG,IAAI,CAACG,YAAY,GAAG,IAAIxB,OAAO,CAAC,CAAC;IACjC,IAAI,CAACyB,WAAW,GAAG,IAAI,CAACD,YAAY,CAACH,KAAK;IAC1C,IAAI,CAACK,eAAe,GAAG,IAAI7B,OAAO,CAACC,cAAc,CAAC;IAClD,IAAI,CAAC6B,iBAAiB,GAAG,OAAOZ,OAAO,CAACY,iBAAiB,KAAK,WAAW,GAAG,KAAK,GAAGZ,OAAO,CAACY,iBAAiB;IAC7G,IAAI,CAACC,0BAA0B,GAAGb,OAAO,CAACa,0BAA0B,IAAI,KAAK;IAC7E,IAAI,CAACC,MAAM,GAAGd,OAAO,CAACc,MAAM;IAC5B,IAAI,CAACC,wBAAwB,GAAG,OAAOf,OAAO,CAACe,wBAAwB,KAAK,WAAW,GAAG,KAAK,GAAGf,OAAO,CAACe,wBAAwB;IAClI,IAAI,CAACC,IAAI,GAAG;MACRC,MAAM,EAAEC,SAAS;MACjBC,OAAO,EAAEpB,WAAW;MACpBqB,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE,CAAC;MACRC,oBAAoB,EAAE,CAAC;MACvBC,iBAAiB,EAAE,CAAC,CAAC;MACrB7B,WAAW,EAAE,KAAK;MAClB8B,SAAS,EAAE,KAAK;MAChBC,eAAe,EAAE,CAAC;MAClBlC,UAAU,EAAE,CAAC,CAAC;MACdmC,OAAO,EAAE,IAAI;MACbC,UAAU,EAAET;IAChB,CAAC;EACL;EACAtC,MAAMA,CAACgD,QAAQ,EAAEC,WAAW,EAAEC,QAAQ,GAAG3C,QAAQ,CAAC4C,KAAK,CAAC,CAAC,EAAE/B,OAAO,GAAG,CAAC,CAAC,EAAE;IACrE,IAAI4B,QAAQ,CAACI,MAAM,KAAK,CAAC,EAAE;MACvB,MAAM,IAAIrD,SAAS,CAAC,IAAI,CAACkB,IAAI,EAAE,uBAAuB,CAAC;IAC3D;IACA,IAAIG,OAAO,CAACiC,oBAAoB,EAAE;MAC9B,IAAI,CAACC,WAAW,CAAClC,OAAO,CAACiC,oBAAoB,EAAEL,QAAQ,EAAEC,WAAW,EAAEC,QAAQ,EAAE9B,OAAO,CAAC;IAC5F,CAAC,MACI;MACD,IAAI,CAACmC,YAAY,CAACP,QAAQ,EAAEC,WAAW,EAAEC,QAAQ,EAAE9B,OAAO,CAAC;IAC/D;EACJ;EACAkC,WAAWA,CAACE,QAAQ,EAAER,QAAQ,EAAEC,WAAW,EAAEQ,gBAAgB,GAAGlD,QAAQ,CAAC4C,KAAK,CAAC,CAAC,EAAE/B,OAAO,EAAEsC,aAAa,GAAGtC,OAAO,CAACuC,SAAS,IAAI,CAAC,EAAE;IAC/H,MAAM;MAAEC;IAAW,CAAC,GAAG,IAAI,CAACC,0BAA0B,CAACb,QAAQ,CAAC;IAChE,IAAI,CAACY,UAAU,CAACE,WAAW,EAAE;MACzB,OAAO,IAAI,CAACP,YAAY,CAACP,QAAQ,EAAEC,WAAW,EAAEQ,gBAAgB,EAAErC,OAAO,CAAC;IAC9E;IACA,MAAM8B,QAAQ,GAAG,CAAC,GAAGO,gBAAgB,CAAC;IACtC,MAAMM,KAAK,GAAGf,QAAQ,CAACA,QAAQ,CAACI,MAAM,GAAG,CAAC,CAAC;IAC3C,MAAMY,IAAI,GAAG,IAAI5D,OAAO,CAAC;MAAE6D,WAAW,EAAEA,CAAA,KAAML,UAAU,CAACE;IAAY,CAAC,EAAE;MACpEG,WAAW,EAAEA,CAAA,KAAM,CACf,GAAGL,UAAU,CAACpB,QAAQ,CAAC0B,KAAK,CAAC,CAAC,EAAEH,KAAK,CAAC,EACtC,GAAGb,QAAQ,EACX,GAAGU,UAAU,CAACpB,QAAQ,CAAC0B,KAAK,CAACH,KAAK,GAAGd,WAAW,CAAC,CACpD,CAACkB,GAAG,CAACC,CAAC,IAAIZ,QAAQ,CAACa,KAAK,CAACD,CAAC,CAAC7B,OAAO,CAAC,CAAC+B,QAAQ,CAAC,CAAC;IACnD,CAAC,CAAC,CAACC,WAAW,CAAC,KAAK,CAAC;IACrB;IACA,IAAIP,IAAI,CAACQ,SAAS,EAAE;MAChBZ,UAAU,CAACE,WAAW,GAAGxB,SAAS;MAClC,OAAO,IAAI,CAACiB,YAAY,CAACP,QAAQ,EAAEC,WAAW,EAAEC,QAAQ,EAAE9B,OAAO,CAAC;IACtE;IACA,MAAMqD,cAAc,GAAGzB,QAAQ,CAACkB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC5C,MAAMQ,aAAa,GAAGA,CAACC,YAAY,EAAEC,YAAY,EAAEC,KAAK,KAAK;MACzD,IAAInB,aAAa,GAAG,CAAC,EAAE;QACnB,KAAK,IAAIoB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,EAAEC,CAAC,EAAE,EAAE;UAC5BH,YAAY,EAAE;UACdC,YAAY,EAAE;UACd,IAAI,CAACtB,WAAW,CAACE,QAAQ,EAAE,CAAC,GAAGiB,cAAc,EAAEE,YAAY,EAAE,CAAC,CAAC,EAAEI,MAAM,CAACC,gBAAgB,EAAE9B,QAAQ,CAAC0B,YAAY,CAAC,CAACpC,QAAQ,EAAEpB,OAAO,EAAEsC,aAAa,GAAG,CAAC,CAAC;QAC1J;MACJ;IACJ,CAAC;IACD,IAAIuB,UAAU,GAAGC,IAAI,CAACC,GAAG,CAACvB,UAAU,CAACpB,QAAQ,CAACY,MAAM,EAAEW,KAAK,GAAGd,WAAW,CAAC;IAC1E,IAAImC,UAAU,GAAGlC,QAAQ,CAACE,MAAM;IAChC,KAAK,MAAMiC,MAAM,IAAIrB,IAAI,CAACsB,OAAO,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACC,aAAa,GAAGF,CAAC,CAACE,aAAa,CAAC,EAAE;MACjFhB,aAAa,CAACO,UAAU,EAAEG,UAAU,EAAEH,UAAU,IAAII,MAAM,CAACK,aAAa,GAAGL,MAAM,CAACM,cAAc,CAAC,CAAC;MAClGV,UAAU,GAAGI,MAAM,CAACK,aAAa;MACjCN,UAAU,GAAGC,MAAM,CAACO,aAAa,GAAG7B,KAAK;MACzC,IAAI,CAACR,YAAY,CAAC,CAAC,GAAGkB,cAAc,EAAEQ,UAAU,CAAC,EAAEI,MAAM,CAACM,cAAc,EAAEpF,QAAQ,CAAC2D,KAAK,CAAChB,QAAQ,EAAEkC,UAAU,EAAEA,UAAU,GAAGC,MAAM,CAACQ,cAAc,CAAC,EAAEzE,OAAO,CAAC;IAChK;IACA;IACAsD,aAAa,CAACO,UAAU,EAAEG,UAAU,EAAEH,UAAU,CAAC;EACrD;EACA1B,YAAYA,CAACP,QAAQ,EAAEC,WAAW,EAAEC,QAAQ,GAAG3C,QAAQ,CAAC4C,KAAK,CAAC,CAAC,EAAE;IAAE2C,eAAe;IAAEC,eAAe;IAAE1C;EAAqB,CAAC,EAAE;IACzH,MAAM;MAAEO,UAAU;MAAEoC,SAAS;MAAEC,QAAQ;MAAEnD;IAAQ,CAAC,GAAG,IAAI,CAACe,0BAA0B,CAACb,QAAQ,CAAC;IAC9F,MAAMkD,wBAAwB,GAAG,EAAE;IACnC,MAAMC,qBAAqB,GAAG5F,QAAQ,CAAC4D,GAAG,CAACjB,QAAQ,EAAEkD,EAAE,IAAI,IAAI,CAACC,cAAc,CAACD,EAAE,EAAExC,UAAU,EAAEA,UAAU,CAACd,OAAO,GAAG,CAAC,CAAC,+BAA+B,CAAC,CAAC,6BAA6BmD,QAAQ,EAAEC,wBAAwB,EAAEJ,eAAe,CAAC,CAAC;IACzO,MAAMQ,SAAS,GAAGtD,QAAQ,CAACA,QAAQ,CAACI,MAAM,GAAG,CAAC,CAAC;IAC/C;IACA;IACA,IAAImD,sBAAsB,GAAG,CAAC;IAC9B,KAAK,IAAIzB,CAAC,GAAGwB,SAAS,EAAExB,CAAC,IAAI,CAAC,IAAIA,CAAC,GAAGlB,UAAU,CAACpB,QAAQ,CAACY,MAAM,EAAE0B,CAAC,EAAE,EAAE;MACnE,MAAM0B,KAAK,GAAG5C,UAAU,CAACpB,QAAQ,CAACsC,CAAC,CAAC;MACpC,IAAI0B,KAAK,CAAC1D,OAAO,EAAE;QACfyD,sBAAsB,GAAGC,KAAK,CAAC7D,iBAAiB;QAChD;MACJ;IACJ;IACA,MAAM8D,aAAa,GAAG,EAAE;IACxB,IAAIC,4BAA4B,GAAG,CAAC;IACpC,IAAI7D,eAAe,GAAG,CAAC;IACvB,KAAK,MAAM2D,KAAK,IAAIL,qBAAqB,EAAE;MACvCM,aAAa,CAACE,IAAI,CAACH,KAAK,CAAC;MACzB3D,eAAe,IAAI2D,KAAK,CAAC3D,eAAe;MACxC,IAAI2D,KAAK,CAAC1D,OAAO,EAAE;QACf0D,KAAK,CAAC7D,iBAAiB,GAAG4D,sBAAsB,GAAGG,4BAA4B,EAAE;MACrF;IACJ;IACA,MAAME,YAAY,GAAG5G,MAAM,CAAC4D,UAAU,CAACpB,QAAQ,EAAE8D,SAAS,EAAErD,WAAW,EAAEwD,aAAa,CAAC;IACvF,IAAI,CAACpD,oBAAoB,EAAE;MACvBO,UAAU,CAACE,WAAW,GAAGxB,SAAS;IACtC,CAAC,MACI,IAAIsB,UAAU,CAACE,WAAW,EAAE;MAC7B9D,MAAM,CAAC4D,UAAU,CAACE,WAAW,EAAEwC,SAAS,EAAErD,WAAW,EAAEwD,aAAa,CAACtC,GAAG,CAAC0C,CAAC,IAAIxD,oBAAoB,CAACgB,KAAK,CAACwC,CAAC,CAACtE,OAAO,CAAC,CAAC+B,QAAQ,CAAC,CAAC,CAAC,CAAC;IACpI,CAAC,MACI;MACDV,UAAU,CAACE,WAAW,GAAGF,UAAU,CAACpB,QAAQ,CAAC2B,GAAG,CAAC0C,CAAC,IAAIxD,oBAAoB,CAACgB,KAAK,CAACwC,CAAC,CAACtE,OAAO,CAAC,CAAC+B,QAAQ,CAAC,CAAC,CAAC;IAC3G;IACA;IACA,IAAIwC,2BAA2B,GAAG,CAAC;IACnC,KAAK,MAAMN,KAAK,IAAII,YAAY,EAAE;MAC9B,IAAIJ,KAAK,CAAC1D,OAAO,EAAE;QACfgE,2BAA2B,EAAE;MACjC;IACJ;IACA;IACA,IAAIA,2BAA2B,KAAK,CAAC,EAAE;MACnC,KAAK,IAAIhC,CAAC,GAAGwB,SAAS,GAAGG,aAAa,CAACrD,MAAM,EAAE0B,CAAC,GAAGlB,UAAU,CAACpB,QAAQ,CAACY,MAAM,EAAE0B,CAAC,EAAE,EAAE;QAChF,MAAM0B,KAAK,GAAG5C,UAAU,CAACpB,QAAQ,CAACsC,CAAC,CAAC;QACpC,IAAI0B,KAAK,CAAC1D,OAAO,EAAE;UACf0D,KAAK,CAAC7D,iBAAiB,IAAImE,2BAA2B;QAC1D;MACJ;IACJ;IACA;IACAlD,UAAU,CAAClB,oBAAoB,IAAIgE,4BAA4B,GAAGI,2BAA2B;IAC7F,IAAIb,QAAQ,IAAInD,OAAO,EAAE;MACrB,MAAMiE,kBAAkB,GAAGH,YAAY,CAACI,MAAM,CAAC,CAACC,CAAC,EAAEC,IAAI,KAAKD,CAAC,IAAIC,IAAI,CAACpE,OAAO,GAAGoE,IAAI,CAACrE,eAAe,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;MAC7G,IAAI,CAACsE,+BAA+B,CAACvD,UAAU,EAAEf,eAAe,GAAGkE,kBAAkB,CAAC;MACtF,IAAI,CAAC7F,IAAI,CAAClB,MAAM,CAACgG,SAAS,EAAEe,kBAAkB,EAAEb,wBAAwB,CAAC;IAC7E;IACA,IAAIU,YAAY,CAACxD,MAAM,GAAG,CAAC,IAAI2C,eAAe,EAAE;MAC5C,MAAMqB,KAAK,GAAIF,IAAI,IAAK;QACpBnB,eAAe,CAACmB,IAAI,CAAC;QACrBA,IAAI,CAAC1E,QAAQ,CAAC6E,OAAO,CAACD,KAAK,CAAC;MAChC,CAAC;MACDR,YAAY,CAACS,OAAO,CAACD,KAAK,CAAC;IAC/B;IACA,IAAI,CAACvF,YAAY,CAACyF,IAAI,CAAC;MAAEC,aAAa,EAAEd,aAAa;MAAEG;IAAa,CAAC,CAAC;IACtE,IAAIM,IAAI,GAAGtD,UAAU;IACrB,OAAOsD,IAAI,EAAE;MACT,IAAIA,IAAI,CAACvG,UAAU,KAAK,CAAC,CAAC,8BAA8B;QACpD;QACA,IAAI,CAACoB,eAAe,CAACyF,OAAO,CAAC,MAAM,IAAI,CAACC,QAAQ,CAAC,CAAC,CAAC;QACnD;MACJ;MACAP,IAAI,GAAGA,IAAI,CAAC7E,MAAM;IACtB;EACJ;EACAqF,QAAQA,CAAC1E,QAAQ,EAAE;IACf,IAAIA,QAAQ,CAACI,MAAM,KAAK,CAAC,EAAE;MACvB,MAAM,IAAIrD,SAAS,CAAC,IAAI,CAACkB,IAAI,EAAE,uBAAuB,CAAC;IAC3D;IACA,MAAM;MAAEiG,IAAI;MAAElB,SAAS;MAAEC;IAAS,CAAC,GAAG,IAAI,CAAC0B,wBAAwB,CAAC3E,QAAQ,CAAC;IAC7E,IAAIkE,IAAI,CAACpE,OAAO,IAAImD,QAAQ,EAAE;MAC1B,IAAI,CAAC/E,IAAI,CAAClB,MAAM,CAACgG,SAAS,EAAE,CAAC,EAAE,CAACkB,IAAI,CAAC,CAAC;IAC1C;EACJ;EACAU,GAAGA,CAAC5E,QAAQ,EAAE;IACV,OAAO,IAAI,CAAC6E,WAAW,CAAC7E,QAAQ,CAAC;EACrC;EACA8E,YAAYA,CAAC9E,QAAQ,EAAE;IACnB,MAAM;MAAEgD,SAAS;MAAElD,OAAO;MAAEmD;IAAS,CAAC,GAAG,IAAI,CAAC0B,wBAAwB,CAAC3E,QAAQ,CAAC;IAChF,OAAOF,OAAO,IAAImD,QAAQ,GAAGD,SAAS,GAAG,CAAC,CAAC;EAC/C;EACA+B,kBAAkBA,CAAC/E,QAAQ,EAAE;IACzB,OAAO,IAAI,CAACgF,WAAW,CAAChF,QAAQ,CAAC,CAACH,eAAe;EACrD;EACAoF,aAAaA,CAACjF,QAAQ,EAAE;IACpB,OAAO,IAAI,CAACgF,WAAW,CAAChF,QAAQ,CAAC,CAAClC,WAAW;EACjD;EACAoH,cAAcA,CAAClF,QAAQ,EAAElC,WAAW,EAAE;IAClC,MAAMoG,IAAI,GAAG,IAAI,CAACc,WAAW,CAAChF,QAAQ,CAAC;IACvC,IAAI,OAAOlC,WAAW,KAAK,WAAW,EAAE;MACpCA,WAAW,GAAG,CAACoG,IAAI,CAACpG,WAAW;IACnC;IACA,MAAMD,MAAM,GAAG;MAAEC;IAAY,CAAC;IAC9B,OAAO,IAAI,CAACQ,aAAa,CAAC6G,YAAY,CAAC,MAAM,IAAI,CAACC,iBAAiB,CAACpF,QAAQ,EAAEnC,MAAM,CAAC,CAAC;EAC1F;EACAwH,WAAWA,CAACrF,QAAQ,EAAE;IAClB,OAAO,IAAI,CAACgF,WAAW,CAAChF,QAAQ,CAAC,CAACJ,SAAS;EAC/C;EACA0F,YAAYA,CAACtF,QAAQ,EAAEJ,SAAS,EAAE2F,SAAS,EAAE;IACzC,MAAMrB,IAAI,GAAG,IAAI,CAACc,WAAW,CAAChF,QAAQ,CAAC;IACvC,IAAI,OAAOJ,SAAS,KAAK,WAAW,EAAE;MAClCA,SAAS,GAAG,CAACsE,IAAI,CAACtE,SAAS;IAC/B;IACA,MAAM/B,MAAM,GAAG;MAAE+B,SAAS;MAAE2F,SAAS,EAAEA,SAAS,IAAI;IAAM,CAAC;IAC3D,OAAO,IAAI,CAACjH,aAAa,CAAC6G,YAAY,CAAC,MAAM,IAAI,CAACC,iBAAiB,CAACpF,QAAQ,EAAEnC,MAAM,CAAC,CAAC;EAC1F;EACAuH,iBAAiBA,CAACpF,QAAQ,EAAEnC,MAAM,EAAE;IAChC,MAAM;MAAEqG,IAAI;MAAElB,SAAS;MAAEC;IAAS,CAAC,GAAG,IAAI,CAAC0B,wBAAwB,CAAC3E,QAAQ,CAAC;IAC7E,MAAMwF,MAAM,GAAG,IAAI,CAACC,yBAAyB,CAACvB,IAAI,EAAElB,SAAS,EAAEC,QAAQ,EAAEpF,MAAM,CAAC;IAChF,IAAIqG,IAAI,KAAK,IAAI,CAAC9E,IAAI,IAAI,IAAI,CAACD,wBAAwB,IAAIqG,MAAM,IAAI,CAAC5H,wBAAwB,CAACC,MAAM,CAAC,IAAIqG,IAAI,CAACpG,WAAW,IAAI,CAACoG,IAAI,CAACtE,SAAS,IAAI,CAAC/B,MAAM,CAAC0H,SAAS,EAAE;MAChK,IAAIG,qBAAqB,GAAG,CAAC,CAAC;MAC9B,KAAK,IAAI5D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoC,IAAI,CAAC1E,QAAQ,CAACY,MAAM,EAAE0B,CAAC,EAAE,EAAE;QAC3C,MAAM0B,KAAK,GAAGU,IAAI,CAAC1E,QAAQ,CAACsC,CAAC,CAAC;QAC9B,IAAI0B,KAAK,CAAC1D,OAAO,EAAE;UACf,IAAI4F,qBAAqB,GAAG,CAAC,CAAC,EAAE;YAC5BA,qBAAqB,GAAG,CAAC,CAAC;YAC1B;UACJ,CAAC,MACI;YACDA,qBAAqB,GAAG5D,CAAC;UAC7B;QACJ;MACJ;MACA,IAAI4D,qBAAqB,GAAG,CAAC,CAAC,EAAE;QAC5B,IAAI,CAACN,iBAAiB,CAAC,CAAC,GAAGpF,QAAQ,EAAE0F,qBAAqB,CAAC,EAAE7H,MAAM,CAAC;MACxE;IACJ;IACA,OAAO2H,MAAM;EACjB;EACAC,yBAAyBA,CAACvB,IAAI,EAAElB,SAAS,EAAEC,QAAQ,EAAEpF,MAAM,EAAE;IACzD,MAAM2H,MAAM,GAAG,IAAI,CAACG,qBAAqB,CAACzB,IAAI,EAAErG,MAAM,EAAE,KAAK,CAAC;IAC9D,IAAI,CAACoF,QAAQ,IAAI,CAACiB,IAAI,CAACpE,OAAO,IAAI,CAAC0F,MAAM,EAAE;MACvC,OAAOA,MAAM;IACjB;IACA,MAAMI,uBAAuB,GAAG1B,IAAI,CAACrE,eAAe;IACpD,MAAMK,QAAQ,GAAG,IAAI,CAAC2F,6BAA6B,CAAC3B,IAAI,CAAC;IACzD,MAAMjE,WAAW,GAAG2F,uBAAuB,IAAI5C,SAAS,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACxE,IAAI,CAAC9E,IAAI,CAAClB,MAAM,CAACgG,SAAS,GAAG,CAAC,EAAE/C,WAAW,EAAEC,QAAQ,CAACgB,KAAK,CAAC,CAAC,CAAC,CAAC;IAC/D,OAAOsE,MAAM;EACjB;EACAG,qBAAqBA,CAACzB,IAAI,EAAErG,MAAM,EAAEiI,IAAI,EAAE;IACtC,IAAIN,MAAM;IACV,IAAItB,IAAI,KAAK,IAAI,CAAC9E,IAAI,EAAE;MACpBoG,MAAM,GAAG,KAAK;IAClB,CAAC,MACI;MACD,IAAI5H,wBAAwB,CAACC,MAAM,CAAC,EAAE;QAClC2H,MAAM,GAAGtB,IAAI,CAACpG,WAAW,KAAKD,MAAM,CAACC,WAAW;QAChDoG,IAAI,CAACpG,WAAW,GAAGD,MAAM,CAACC,WAAW;MACzC,CAAC,MACI,IAAI,CAACoG,IAAI,CAACpG,WAAW,EAAE;QACxB0H,MAAM,GAAG,KAAK;MAClB,CAAC,MACI;QACDA,MAAM,GAAGtB,IAAI,CAACtE,SAAS,KAAK/B,MAAM,CAAC+B,SAAS;QAC5CsE,IAAI,CAACtE,SAAS,GAAG/B,MAAM,CAAC+B,SAAS;MACrC;MACA,IAAI4F,MAAM,EAAE;QACR,IAAI,CAACjH,yBAAyB,CAAC+F,IAAI,CAAC;UAAEJ,IAAI;UAAE4B;QAAK,CAAC,CAAC;MACvD;IACJ;IACA,IAAI,CAAClI,wBAAwB,CAACC,MAAM,CAAC,IAAIA,MAAM,CAAC0H,SAAS,EAAE;MACvD,KAAK,MAAM/B,KAAK,IAAIU,IAAI,CAAC1E,QAAQ,EAAE;QAC/BgG,MAAM,GAAG,IAAI,CAACG,qBAAqB,CAACnC,KAAK,EAAE3F,MAAM,EAAE,IAAI,CAAC,IAAI2H,MAAM;MACtE;IACJ;IACA,OAAOA,MAAM;EACjB;EACAO,QAAQA,CAAC/F,QAAQ,EAAE;IACf,IAAI,CAAC1B,aAAa,CAAC6G,YAAY,CAAC,MAAM;MAClC,IAAIjB,IAAI,GAAG,IAAI,CAACc,WAAW,CAAChF,QAAQ,CAAC;MACrC,OAAOkE,IAAI,CAAC7E,MAAM,EAAE;QAChB6E,IAAI,GAAGA,IAAI,CAAC7E,MAAM;QAClBW,QAAQ,GAAGA,QAAQ,CAACkB,KAAK,CAAC,CAAC,EAAElB,QAAQ,CAACI,MAAM,GAAG,CAAC,CAAC;QACjD,IAAI8D,IAAI,CAACtE,SAAS,EAAE;UAChB,IAAI,CAACwF,iBAAiB,CAACpF,QAAQ,EAAE;YAAEJ,SAAS,EAAE,KAAK;YAAE2F,SAAS,EAAE;UAAM,CAAC,CAAC;QAC5E;MACJ;IACJ,CAAC,CAAC;EACN;EACAd,QAAQA,CAAA,EAAG;IACP,MAAMmB,uBAAuB,GAAG,IAAI,CAACxG,IAAI,CAACS,eAAe;IACzD,MAAMK,QAAQ,GAAG,IAAI,CAAC8F,2BAA2B,CAAC,IAAI,CAAC5G,IAAI,CAAC;IAC5D,IAAI,CAAClB,IAAI,CAAClB,MAAM,CAAC,CAAC,EAAE4I,uBAAuB,EAAE1F,QAAQ,CAAC;IACtD,IAAI,CAACnB,eAAe,CAACkH,MAAM,CAAC,CAAC;EACjC;EACA5C,cAAcA,CAAC6C,WAAW,EAAE7G,MAAM,EAAE8G,gBAAgB,EAAElD,QAAQ,EAAEmD,gBAAgB,EAAEtD,eAAe,EAAE;IAC/F,MAAMoB,IAAI,GAAG;MACT7E,MAAM;MACNE,OAAO,EAAE2G,WAAW,CAAC3G,OAAO;MAC5BC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAEJ,MAAM,CAACI,KAAK,GAAG,CAAC;MACvBC,oBAAoB,EAAE,CAAC;MACvBC,iBAAiB,EAAE,CAAC,CAAC;MACrB7B,WAAW,EAAE,OAAOoI,WAAW,CAACpI,WAAW,KAAK,SAAS,GAAGoI,WAAW,CAACpI,WAAW,GAAI,OAAOoI,WAAW,CAACtG,SAAS,KAAK,WAAY;MACpIA,SAAS,EAAE,OAAOsG,WAAW,CAACtG,SAAS,KAAK,WAAW,GAAG,IAAI,CAACZ,iBAAiB,GAAGkH,WAAW,CAACtG,SAAS;MACxGC,eAAe,EAAE,CAAC;MAClBlC,UAAU,EAAE,CAAC,CAAC;MACdmC,OAAO,EAAE,IAAI;MACbC,UAAU,EAAET;IAChB,CAAC;IACD,MAAM3B,UAAU,GAAG,IAAI,CAAC0I,WAAW,CAACnC,IAAI,EAAEiC,gBAAgB,CAAC;IAC3DjC,IAAI,CAACvG,UAAU,GAAGA,UAAU;IAC5B,IAAIsF,QAAQ,EAAE;MACVmD,gBAAgB,CAACzC,IAAI,CAACO,IAAI,CAAC;IAC/B;IACA,MAAMoC,aAAa,GAAGJ,WAAW,CAAC1G,QAAQ,IAAIjC,QAAQ,CAAC4C,KAAK,CAAC,CAAC;IAC9D,MAAMoG,aAAa,GAAGtD,QAAQ,IAAItF,UAAU,KAAK,CAAC,CAAC,+BAA+B,CAACuG,IAAI,CAACtE,SAAS;IACjG,IAAIF,oBAAoB,GAAG,CAAC;IAC5B,IAAIG,eAAe,GAAG,CAAC;IACvB,KAAK,MAAMuD,EAAE,IAAIkD,aAAa,EAAE;MAC5B,MAAM9C,KAAK,GAAG,IAAI,CAACH,cAAc,CAACD,EAAE,EAAEc,IAAI,EAAEvG,UAAU,EAAE4I,aAAa,EAAEH,gBAAgB,EAAEtD,eAAe,CAAC;MACzGoB,IAAI,CAAC1E,QAAQ,CAACmE,IAAI,CAACH,KAAK,CAAC;MACzB3D,eAAe,IAAI2D,KAAK,CAAC3D,eAAe;MACxC,IAAI2D,KAAK,CAAC1D,OAAO,EAAE;QACf0D,KAAK,CAAC7D,iBAAiB,GAAGD,oBAAoB,EAAE;MACpD;IACJ;IACA,IAAI,CAAC,IAAI,CAACT,0BAA0B,EAAE;MAClCiF,IAAI,CAACpG,WAAW,GAAGoG,IAAI,CAACpG,WAAW,IAAIoG,IAAI,CAAC1E,QAAQ,CAACY,MAAM,GAAG,CAAC;IACnE;IACA8D,IAAI,CAACxE,oBAAoB,GAAGA,oBAAoB;IAChDwE,IAAI,CAACpE,OAAO,GAAGnC,UAAU,KAAK,CAAC,CAAC,+BAA+B+B,oBAAoB,GAAG,CAAC,GAAI/B,UAAU,KAAK,CAAC,CAAC,4BAA6B;IACzI,IAAI,CAACuG,IAAI,CAACpE,OAAO,EAAE;MACfoE,IAAI,CAACrE,eAAe,GAAG,CAAC;MACxB,IAAIoD,QAAQ,EAAE;QACVmD,gBAAgB,CAACI,GAAG,CAAC,CAAC;MAC1B;IACJ,CAAC,MACI,IAAI,CAACtC,IAAI,CAACtE,SAAS,EAAE;MACtBsE,IAAI,CAACrE,eAAe,GAAGA,eAAe;IAC1C;IACAiD,eAAe,GAAGoB,IAAI,CAAC;IACvB,OAAOA,IAAI;EACf;EACA2B,6BAA6BA,CAAC3B,IAAI,EAAE;IAChC,MAAM0B,uBAAuB,GAAG1B,IAAI,CAACrE,eAAe;IACpD,MAAM2F,MAAM,GAAG,EAAE;IACjB,IAAI,CAACiB,8BAA8B,CAACvC,IAAI,EAAEsB,MAAM,CAAC;IACjD,IAAI,CAACrB,+BAA+B,CAACD,IAAI,CAAC7E,MAAM,EAAEmG,MAAM,CAACpF,MAAM,GAAGwF,uBAAuB,CAAC;IAC1F,OAAOJ,MAAM;EACjB;EACAiB,8BAA8BA,CAACvC,IAAI,EAAEsB,MAAM,EAAE;IACzC,IAAItB,IAAI,CAACpE,OAAO,KAAK,KAAK,EAAE;MACxB,OAAO,CAAC;IACZ;IACA0F,MAAM,CAAC7B,IAAI,CAACO,IAAI,CAAC;IACjBA,IAAI,CAACrE,eAAe,GAAG,CAAC;IACxB,IAAI,CAACqE,IAAI,CAACtE,SAAS,EAAE;MACjB,KAAK,MAAM4D,KAAK,IAAIU,IAAI,CAAC1E,QAAQ,EAAE;QAC/B0E,IAAI,CAACrE,eAAe,IAAI,IAAI,CAAC4G,8BAA8B,CAACjD,KAAK,EAAEgC,MAAM,CAAC;MAC9E;IACJ;IACA,IAAI,CAAC7G,2BAA2B,CAAC2F,IAAI,CAACJ,IAAI,CAAC;IAC3C,OAAOA,IAAI,CAACrE,eAAe;EAC/B;EACAmG,2BAA2BA,CAAC9B,IAAI,EAAE;IAC9B,MAAM0B,uBAAuB,GAAG1B,IAAI,CAACrE,eAAe;IACpD,MAAM2F,MAAM,GAAG,EAAE;IACjB,IAAI,CAACkB,4BAA4B,CAACxC,IAAI,EAAEA,IAAI,CAACpE,OAAO,GAAG,CAAC,CAAC,+BAA+B,CAAC,CAAC,6BAA6B0F,MAAM,CAAC;IAC9H,IAAI,CAACrB,+BAA+B,CAACD,IAAI,CAAC7E,MAAM,EAAEmG,MAAM,CAACpF,MAAM,GAAGwF,uBAAuB,CAAC;IAC1F,OAAOJ,MAAM;EACjB;EACAkB,4BAA4BA,CAACxC,IAAI,EAAEiC,gBAAgB,EAAEX,MAAM,EAAEvC,QAAQ,GAAG,IAAI,EAAE;IAC1E,IAAItF,UAAU;IACd,IAAIuG,IAAI,KAAK,IAAI,CAAC9E,IAAI,EAAE;MACpBzB,UAAU,GAAG,IAAI,CAAC0I,WAAW,CAACnC,IAAI,EAAEiC,gBAAgB,CAAC;MACrD,IAAIxI,UAAU,KAAK,CAAC,CAAC,6BAA6B;QAC9CuG,IAAI,CAACpE,OAAO,GAAG,KAAK;QACpBoE,IAAI,CAACrE,eAAe,GAAG,CAAC;QACxB,OAAO,KAAK;MAChB;MACA,IAAIoD,QAAQ,EAAE;QACVuC,MAAM,CAAC7B,IAAI,CAACO,IAAI,CAAC;MACrB;IACJ;IACA,MAAMyC,iBAAiB,GAAGnB,MAAM,CAACpF,MAAM;IACvC8D,IAAI,CAACrE,eAAe,GAAGqE,IAAI,KAAK,IAAI,CAAC9E,IAAI,GAAG,CAAC,GAAG,CAAC;IACjD,IAAIwH,qBAAqB,GAAG,KAAK;IACjC,IAAI,CAAC1C,IAAI,CAACtE,SAAS,IAAIjC,UAAU,KAAK,CAAC,CAAC,6BAA6B;MACjE,IAAIgC,iBAAiB,GAAG,CAAC;MACzB,KAAK,MAAM6D,KAAK,IAAIU,IAAI,CAAC1E,QAAQ,EAAE;QAC/BoH,qBAAqB,GAAG,IAAI,CAACF,4BAA4B,CAAClD,KAAK,EAAE7F,UAAU,EAAE6H,MAAM,EAAEvC,QAAQ,IAAI,CAACiB,IAAI,CAACtE,SAAS,CAAC,IAAIgH,qBAAqB;QAC1I,IAAIpD,KAAK,CAAC1D,OAAO,EAAE;UACf0D,KAAK,CAAC7D,iBAAiB,GAAGA,iBAAiB,EAAE;QACjD;MACJ;MACAuE,IAAI,CAACxE,oBAAoB,GAAGC,iBAAiB;IACjD,CAAC,MACI;MACDuE,IAAI,CAACxE,oBAAoB,GAAG,CAAC;IACjC;IACA,IAAIwE,IAAI,KAAK,IAAI,CAAC9E,IAAI,EAAE;MACpB8E,IAAI,CAACpE,OAAO,GAAGnC,UAAU,KAAK,CAAC,CAAC,+BAA+BiJ,qBAAqB,GAAIjJ,UAAU,KAAK,CAAC,CAAC,4BAA6B;MACtIuG,IAAI,CAACvG,UAAU,GAAGA,UAAU;IAChC;IACA,IAAI,CAACuG,IAAI,CAACpE,OAAO,EAAE;MACfoE,IAAI,CAACrE,eAAe,GAAG,CAAC;MACxB,IAAIoD,QAAQ,EAAE;QACVuC,MAAM,CAACgB,GAAG,CAAC,CAAC;MAChB;IACJ,CAAC,MACI,IAAI,CAACtC,IAAI,CAACtE,SAAS,EAAE;MACtBsE,IAAI,CAACrE,eAAe,IAAI2F,MAAM,CAACpF,MAAM,GAAGuG,iBAAiB;IAC7D;IACA,IAAI,CAAChI,2BAA2B,CAAC2F,IAAI,CAACJ,IAAI,CAAC;IAC3C,OAAOA,IAAI,CAACpE,OAAO;EACvB;EACAqE,+BAA+BA,CAACD,IAAI,EAAElD,IAAI,EAAE;IACxC,IAAIA,IAAI,KAAK,CAAC,EAAE;MACZ;IACJ;IACA,OAAOkD,IAAI,EAAE;MACTA,IAAI,CAACrE,eAAe,IAAImB,IAAI;MAC5B,IAAI,CAACrC,2BAA2B,CAAC2F,IAAI,CAACJ,IAAI,CAAC;MAC3CA,IAAI,GAAGA,IAAI,CAAC7E,MAAM;IACtB;EACJ;EACAgH,WAAWA,CAACnC,IAAI,EAAEiC,gBAAgB,EAAE;IAChC,MAAMX,MAAM,GAAG,IAAI,CAACtG,MAAM,GAAG,IAAI,CAACA,MAAM,CAACA,MAAM,CAACgF,IAAI,CAAC3E,OAAO,EAAE4G,gBAAgB,CAAC,GAAG,CAAC,CAAC;IACpF,IAAI,OAAOX,MAAM,KAAK,SAAS,EAAE;MAC7BtB,IAAI,CAACnE,UAAU,GAAGT,SAAS;MAC3B,OAAOkG,MAAM,GAAG,CAAC,CAAC,+BAA+B,CAAC,CAAC;IACvD,CAAC,MACI,IAAIhI,cAAc,CAACgI,MAAM,CAAC,EAAE;MAC7BtB,IAAI,CAACnE,UAAU,GAAGyF,MAAM,CAACqB,IAAI;MAC7B,OAAOnJ,eAAe,CAAC8H,MAAM,CAAC7H,UAAU,CAAC;IAC7C,CAAC,MACI;MACDuG,IAAI,CAACnE,UAAU,GAAGT,SAAS;MAC3B,OAAO5B,eAAe,CAAC8H,MAAM,CAAC;IAClC;EACJ;EACA;EACAX,WAAWA,CAAC7E,QAAQ,EAAEkE,IAAI,GAAG,IAAI,CAAC9E,IAAI,EAAE;IACpC,IAAI,CAACY,QAAQ,IAAIA,QAAQ,CAACI,MAAM,KAAK,CAAC,EAAE;MACpC,OAAO,IAAI;IACf;IACA,MAAM,CAACW,KAAK,EAAE,GAAG+F,IAAI,CAAC,GAAG9G,QAAQ;IACjC,IAAIe,KAAK,GAAG,CAAC,IAAIA,KAAK,GAAGmD,IAAI,CAAC1E,QAAQ,CAACY,MAAM,EAAE;MAC3C,OAAO,KAAK;IAChB;IACA,OAAO,IAAI,CAACyE,WAAW,CAACiC,IAAI,EAAE5C,IAAI,CAAC1E,QAAQ,CAACuB,KAAK,CAAC,CAAC;EACvD;EACA;EACAiE,WAAWA,CAAChF,QAAQ,EAAEkE,IAAI,GAAG,IAAI,CAAC9E,IAAI,EAAE;IACpC,IAAI,CAACY,QAAQ,IAAIA,QAAQ,CAACI,MAAM,KAAK,CAAC,EAAE;MACpC,OAAO8D,IAAI;IACf;IACA,MAAM,CAACnD,KAAK,EAAE,GAAG+F,IAAI,CAAC,GAAG9G,QAAQ;IACjC,IAAIe,KAAK,GAAG,CAAC,IAAIA,KAAK,GAAGmD,IAAI,CAAC1E,QAAQ,CAACY,MAAM,EAAE;MAC3C,MAAM,IAAIrD,SAAS,CAAC,IAAI,CAACkB,IAAI,EAAE,uBAAuB,CAAC;IAC3D;IACA,OAAO,IAAI,CAAC+G,WAAW,CAAC8B,IAAI,EAAE5C,IAAI,CAAC1E,QAAQ,CAACuB,KAAK,CAAC,CAAC;EACvD;EACA;EACA4D,wBAAwBA,CAAC3E,QAAQ,EAAE;IAC/B,IAAIA,QAAQ,CAACI,MAAM,KAAK,CAAC,EAAE;MACvB,OAAO;QAAE8D,IAAI,EAAE,IAAI,CAAC9E,IAAI;QAAE4D,SAAS,EAAE,CAAC,CAAC;QAAEC,QAAQ,EAAE,IAAI;QAAEnD,OAAO,EAAE;MAAM,CAAC;IAC7E;IACA,MAAM;MAAEc,UAAU;MAAEoC,SAAS;MAAEC,QAAQ;MAAEnD;IAAQ,CAAC,GAAG,IAAI,CAACe,0BAA0B,CAACb,QAAQ,CAAC;IAC9F,MAAMe,KAAK,GAAGf,QAAQ,CAACA,QAAQ,CAACI,MAAM,GAAG,CAAC,CAAC;IAC3C,IAAIW,KAAK,GAAG,CAAC,IAAIA,KAAK,GAAGH,UAAU,CAACpB,QAAQ,CAACY,MAAM,EAAE;MACjD,MAAM,IAAIrD,SAAS,CAAC,IAAI,CAACkB,IAAI,EAAE,uBAAuB,CAAC;IAC3D;IACA,MAAMiG,IAAI,GAAGtD,UAAU,CAACpB,QAAQ,CAACuB,KAAK,CAAC;IACvC,OAAO;MAAEmD,IAAI;MAAElB,SAAS;MAAEC,QAAQ;MAAEnD,OAAO,EAAEA,OAAO,IAAIoE,IAAI,CAACpE;IAAQ,CAAC;EAC1E;EACAe,0BAA0BA,CAACb,QAAQ,EAAEkE,IAAI,GAAG,IAAI,CAAC9E,IAAI,EAAE4D,SAAS,GAAG,CAAC,EAAEC,QAAQ,GAAG,IAAI,EAAEnD,OAAO,GAAG,IAAI,EAAE;IACnG,MAAM,CAACiB,KAAK,EAAE,GAAG+F,IAAI,CAAC,GAAG9G,QAAQ;IACjC,IAAIe,KAAK,GAAG,CAAC,IAAIA,KAAK,GAAGmD,IAAI,CAAC1E,QAAQ,CAACY,MAAM,EAAE;MAC3C,MAAM,IAAIrD,SAAS,CAAC,IAAI,CAACkB,IAAI,EAAE,uBAAuB,CAAC;IAC3D;IACA;IACA,KAAK,IAAI6D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGf,KAAK,EAAEe,CAAC,EAAE,EAAE;MAC5BkB,SAAS,IAAIkB,IAAI,CAAC1E,QAAQ,CAACsC,CAAC,CAAC,CAACjC,eAAe;IACjD;IACAoD,QAAQ,GAAGA,QAAQ,IAAI,CAACiB,IAAI,CAACtE,SAAS;IACtCE,OAAO,GAAGA,OAAO,IAAIoE,IAAI,CAACpE,OAAO;IACjC,IAAIgH,IAAI,CAAC1G,MAAM,KAAK,CAAC,EAAE;MACnB,OAAO;QAAEQ,UAAU,EAAEsD,IAAI;QAAElB,SAAS;QAAEC,QAAQ;QAAEnD;MAAQ,CAAC;IAC7D;IACA,OAAO,IAAI,CAACe,0BAA0B,CAACiG,IAAI,EAAE5C,IAAI,CAAC1E,QAAQ,CAACuB,KAAK,CAAC,EAAEiC,SAAS,GAAG,CAAC,EAAEC,QAAQ,EAAEnD,OAAO,CAAC;EACxG;EACAiH,OAAOA,CAAC/G,QAAQ,GAAG,EAAE,EAAE;IACnB,OAAO,IAAI,CAACgF,WAAW,CAAChF,QAAQ,CAAC;EACrC;EACA;EACAgH,eAAeA,CAAC9C,IAAI,EAAE;IAClB,MAAMlE,QAAQ,GAAG,EAAE;IACnB,IAAIiH,aAAa,GAAG/C,IAAI,CAAC,CAAC;IAC1B,OAAO+C,aAAa,CAAC5H,MAAM,EAAE;MACzBW,QAAQ,CAAC2D,IAAI,CAACsD,aAAa,CAAC5H,MAAM,CAACG,QAAQ,CAAC0H,OAAO,CAACD,aAAa,CAAC,CAAC;MACnEA,aAAa,GAAGA,aAAa,CAAC5H,MAAM;IACxC;IACA,OAAOW,QAAQ,CAACmH,OAAO,CAAC,CAAC;EAC7B;EACAC,qBAAqBA,CAACpH,QAAQ,EAAE;IAC5B,IAAIA,QAAQ,CAACI,MAAM,KAAK,CAAC,EAAE;MACvB,OAAOd,SAAS;IACpB,CAAC,MACI,IAAIU,QAAQ,CAACI,MAAM,KAAK,CAAC,EAAE;MAC5B,OAAO,EAAE;IACb,CAAC,MACI;MACD,OAAOnD,KAAK,CAAC+C,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC7B;EACJ;EACAqH,oBAAoBA,CAACrH,QAAQ,EAAE;IAC3B,MAAMkE,IAAI,GAAG,IAAI,CAACc,WAAW,CAAChF,QAAQ,CAAC;IACvC,IAAIkE,IAAI,CAAC1E,QAAQ,CAACY,MAAM,KAAK,CAAC,EAAE;MAC5B,OAAOd,SAAS;IACpB;IACA,OAAO4E,IAAI,CAAC1E,QAAQ,CAAC,CAAC,CAAC,CAACD,OAAO;EACnC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}