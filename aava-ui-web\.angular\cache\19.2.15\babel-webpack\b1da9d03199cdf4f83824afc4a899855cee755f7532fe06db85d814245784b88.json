{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport './iconlabel.css';\nimport * as dom from '../../dom.js';\nimport { HighlightedLabel } from '../highlightedlabel/highlightedLabel.js';\nimport { Disposable } from '../../../common/lifecycle.js';\nimport { equals } from '../../../common/objects.js';\nimport { Range } from '../../../common/range.js';\nimport { getDefaultHoverDelegate } from '../hover/hoverDelegateFactory.js';\nimport { getBaseLayerHoverDelegate } from '../hover/hoverDelegate2.js';\nimport { isString } from '../../../common/types.js';\nimport { stripIcons } from '../../../common/iconLabels.js';\nclass FastLabelNode {\n  constructor(_element) {\n    this._element = _element;\n  }\n  get element() {\n    return this._element;\n  }\n  set textContent(content) {\n    if (this.disposed || content === this._textContent) {\n      return;\n    }\n    this._textContent = content;\n    this._element.textContent = content;\n  }\n  set classNames(classNames) {\n    if (this.disposed || equals(classNames, this._classNames)) {\n      return;\n    }\n    this._classNames = classNames;\n    this._element.classList.value = '';\n    this._element.classList.add(...classNames);\n  }\n  set empty(empty) {\n    if (this.disposed || empty === this._empty) {\n      return;\n    }\n    this._empty = empty;\n    this._element.style.marginLeft = empty ? '0' : '';\n  }\n  dispose() {\n    this.disposed = true;\n  }\n}\nexport class IconLabel extends Disposable {\n  constructor(container, options) {\n    super();\n    this.customHovers = new Map();\n    this.creationOptions = options;\n    this.domNode = this._register(new FastLabelNode(dom.append(container, dom.$('.monaco-icon-label'))));\n    this.labelContainer = dom.append(this.domNode.element, dom.$('.monaco-icon-label-container'));\n    this.nameContainer = dom.append(this.labelContainer, dom.$('span.monaco-icon-name-container'));\n    if (options?.supportHighlights || options?.supportIcons) {\n      this.nameNode = this._register(new LabelWithHighlights(this.nameContainer, !!options.supportIcons));\n    } else {\n      this.nameNode = new Label(this.nameContainer);\n    }\n    this.hoverDelegate = options?.hoverDelegate ?? getDefaultHoverDelegate('mouse');\n  }\n  get element() {\n    return this.domNode.element;\n  }\n  setLabel(label, description, options) {\n    const labelClasses = ['monaco-icon-label'];\n    const containerClasses = ['monaco-icon-label-container'];\n    let ariaLabel = '';\n    if (options) {\n      if (options.extraClasses) {\n        labelClasses.push(...options.extraClasses);\n      }\n      if (options.italic) {\n        labelClasses.push('italic');\n      }\n      if (options.strikethrough) {\n        labelClasses.push('strikethrough');\n      }\n      if (options.disabledCommand) {\n        containerClasses.push('disabled');\n      }\n      if (options.title) {\n        if (typeof options.title === 'string') {\n          ariaLabel += options.title;\n        } else {\n          ariaLabel += label;\n        }\n      }\n    }\n    const existingIconNode = this.domNode.element.querySelector('.monaco-icon-label-iconpath');\n    if (options?.iconPath) {\n      let iconNode;\n      if (!existingIconNode || !dom.isHTMLElement(existingIconNode)) {\n        iconNode = dom.$('.monaco-icon-label-iconpath');\n        this.domNode.element.prepend(iconNode);\n      } else {\n        iconNode = existingIconNode;\n      }\n      iconNode.style.backgroundImage = dom.asCSSUrl(options?.iconPath);\n    } else if (existingIconNode) {\n      existingIconNode.remove();\n    }\n    this.domNode.classNames = labelClasses;\n    this.domNode.element.setAttribute('aria-label', ariaLabel);\n    this.labelContainer.classList.value = '';\n    this.labelContainer.classList.add(...containerClasses);\n    this.setupHover(options?.descriptionTitle ? this.labelContainer : this.element, options?.title);\n    this.nameNode.setLabel(label, options);\n    if (description || this.descriptionNode) {\n      const descriptionNode = this.getOrCreateDescriptionNode();\n      if (descriptionNode instanceof HighlightedLabel) {\n        descriptionNode.set(description || '', options ? options.descriptionMatches : undefined, undefined, options?.labelEscapeNewLines);\n        this.setupHover(descriptionNode.element, options?.descriptionTitle);\n      } else {\n        descriptionNode.textContent = description && options?.labelEscapeNewLines ? HighlightedLabel.escapeNewLines(description, []) : description || '';\n        this.setupHover(descriptionNode.element, options?.descriptionTitle || '');\n        descriptionNode.empty = !description;\n      }\n    }\n    if (options?.suffix || this.suffixNode) {\n      const suffixNode = this.getOrCreateSuffixNode();\n      suffixNode.textContent = options?.suffix ?? '';\n    }\n  }\n  setupHover(htmlElement, tooltip) {\n    const previousCustomHover = this.customHovers.get(htmlElement);\n    if (previousCustomHover) {\n      previousCustomHover.dispose();\n      this.customHovers.delete(htmlElement);\n    }\n    if (!tooltip) {\n      htmlElement.removeAttribute('title');\n      return;\n    }\n    if (this.hoverDelegate.showNativeHover) {\n      function setupNativeHover(htmlElement, tooltip) {\n        if (isString(tooltip)) {\n          // Icons don't render in the native hover so we strip them out\n          htmlElement.title = stripIcons(tooltip);\n        } else if (tooltip?.markdownNotSupportedFallback) {\n          htmlElement.title = tooltip.markdownNotSupportedFallback;\n        } else {\n          htmlElement.removeAttribute('title');\n        }\n      }\n      setupNativeHover(htmlElement, tooltip);\n    } else {\n      const hoverDisposable = getBaseLayerHoverDelegate().setupManagedHover(this.hoverDelegate, htmlElement, tooltip);\n      if (hoverDisposable) {\n        this.customHovers.set(htmlElement, hoverDisposable);\n      }\n    }\n  }\n  dispose() {\n    super.dispose();\n    for (const disposable of this.customHovers.values()) {\n      disposable.dispose();\n    }\n    this.customHovers.clear();\n  }\n  getOrCreateSuffixNode() {\n    if (!this.suffixNode) {\n      const suffixContainer = this._register(new FastLabelNode(dom.after(this.nameContainer, dom.$('span.monaco-icon-suffix-container'))));\n      this.suffixNode = this._register(new FastLabelNode(dom.append(suffixContainer.element, dom.$('span.label-suffix'))));\n    }\n    return this.suffixNode;\n  }\n  getOrCreateDescriptionNode() {\n    if (!this.descriptionNode) {\n      const descriptionContainer = this._register(new FastLabelNode(dom.append(this.labelContainer, dom.$('span.monaco-icon-description-container'))));\n      if (this.creationOptions?.supportDescriptionHighlights) {\n        this.descriptionNode = this._register(new HighlightedLabel(dom.append(descriptionContainer.element, dom.$('span.label-description')), {\n          supportIcons: !!this.creationOptions.supportIcons\n        }));\n      } else {\n        this.descriptionNode = this._register(new FastLabelNode(dom.append(descriptionContainer.element, dom.$('span.label-description'))));\n      }\n    }\n    return this.descriptionNode;\n  }\n}\nclass Label {\n  constructor(container) {\n    this.container = container;\n    this.label = undefined;\n    this.singleLabel = undefined;\n  }\n  setLabel(label, options) {\n    if (this.label === label && equals(this.options, options)) {\n      return;\n    }\n    this.label = label;\n    this.options = options;\n    if (typeof label === 'string') {\n      if (!this.singleLabel) {\n        this.container.innerText = '';\n        this.container.classList.remove('multiple');\n        this.singleLabel = dom.append(this.container, dom.$('a.label-name', {\n          id: options?.domId\n        }));\n      }\n      this.singleLabel.textContent = label;\n    } else {\n      this.container.innerText = '';\n      this.container.classList.add('multiple');\n      this.singleLabel = undefined;\n      for (let i = 0; i < label.length; i++) {\n        const l = label[i];\n        const id = options?.domId && `${options?.domId}_${i}`;\n        dom.append(this.container, dom.$('a.label-name', {\n          id,\n          'data-icon-label-count': label.length,\n          'data-icon-label-index': i,\n          'role': 'treeitem'\n        }, l));\n        if (i < label.length - 1) {\n          dom.append(this.container, dom.$('span.label-separator', undefined, options?.separator || '/'));\n        }\n      }\n    }\n  }\n}\nfunction splitMatches(labels, separator, matches) {\n  if (!matches) {\n    return undefined;\n  }\n  let labelStart = 0;\n  return labels.map(label => {\n    const labelRange = {\n      start: labelStart,\n      end: labelStart + label.length\n    };\n    const result = matches.map(match => Range.intersect(labelRange, match)).filter(range => !Range.isEmpty(range)).map(({\n      start,\n      end\n    }) => ({\n      start: start - labelStart,\n      end: end - labelStart\n    }));\n    labelStart = labelRange.end + separator.length;\n    return result;\n  });\n}\nclass LabelWithHighlights extends Disposable {\n  constructor(container, supportIcons) {\n    super();\n    this.container = container;\n    this.supportIcons = supportIcons;\n    this.label = undefined;\n    this.singleLabel = undefined;\n  }\n  setLabel(label, options) {\n    if (this.label === label && equals(this.options, options)) {\n      return;\n    }\n    this.label = label;\n    this.options = options;\n    if (typeof label === 'string') {\n      if (!this.singleLabel) {\n        this.container.innerText = '';\n        this.container.classList.remove('multiple');\n        this.singleLabel = this._register(new HighlightedLabel(dom.append(this.container, dom.$('a.label-name', {\n          id: options?.domId\n        })), {\n          supportIcons: this.supportIcons\n        }));\n      }\n      this.singleLabel.set(label, options?.matches, undefined, options?.labelEscapeNewLines);\n    } else {\n      this.container.innerText = '';\n      this.container.classList.add('multiple');\n      this.singleLabel = undefined;\n      const separator = options?.separator || '/';\n      const matches = splitMatches(label, separator, options?.matches);\n      for (let i = 0; i < label.length; i++) {\n        const l = label[i];\n        const m = matches ? matches[i] : undefined;\n        const id = options?.domId && `${options?.domId}_${i}`;\n        const name = dom.$('a.label-name', {\n          id,\n          'data-icon-label-count': label.length,\n          'data-icon-label-index': i,\n          'role': 'treeitem'\n        });\n        const highlightedLabel = this._register(new HighlightedLabel(dom.append(this.container, name), {\n          supportIcons: this.supportIcons\n        }));\n        highlightedLabel.set(l, m, undefined, options?.labelEscapeNewLines);\n        if (i < label.length - 1) {\n          dom.append(name, dom.$('span.label-separator', undefined, separator));\n        }\n      }\n    }\n  }\n}", "map": {"version": 3, "names": ["dom", "HighlightedLabel", "Disposable", "equals", "Range", "getDefaultHoverDelegate", "getBaseLayerHoverDelegate", "isString", "stripIcons", "FastLabelNode", "constructor", "_element", "element", "textContent", "content", "disposed", "_textContent", "classNames", "_classNames", "classList", "value", "add", "empty", "_empty", "style", "marginLeft", "dispose", "IconLabel", "container", "options", "customHovers", "Map", "creationOptions", "domNode", "_register", "append", "$", "labelContainer", "nameContainer", "supportHighlights", "supportIcons", "nameNode", "LabelWithHighlights", "Label", "hoverDelegate", "<PERSON><PERSON><PERSON><PERSON>", "label", "description", "labelClasses", "containerClasses", "aria<PERSON><PERSON><PERSON>", "extraClasses", "push", "italic", "strikethrough", "disabledCommand", "title", "existingIconNode", "querySelector", "iconPath", "iconNode", "isHTMLElement", "prepend", "backgroundImage", "asCSSUrl", "remove", "setAttribute", "setupHover", "descriptionTitle", "descriptionNode", "getOrCreateDescriptionNode", "set", "descriptionMatches", "undefined", "labelEscapeNewLines", "escapeNewLines", "suffix", "suffixNode", "getOrCreateSuffixNode", "htmlElement", "tooltip", "previousCustomHover", "get", "delete", "removeAttribute", "showNativeHover", "setupNativeHover", "markdownNotSupportedFallback", "hoverDisposable", "setupManagedHover", "disposable", "values", "clear", "suffixContainer", "after", "description<PERSON><PERSON><PERSON>", "supportDescriptionHighlights", "singleLabel", "innerText", "id", "domId", "i", "length", "l", "separator", "splitMatches", "labels", "matches", "labelStart", "map", "labelRange", "start", "end", "result", "match", "intersect", "filter", "range", "isEmpty", "m", "name", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/browser/ui/iconLabel/iconLabel.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport './iconlabel.css';\nimport * as dom from '../../dom.js';\nimport { HighlightedLabel } from '../highlightedlabel/highlightedLabel.js';\nimport { Disposable } from '../../../common/lifecycle.js';\nimport { equals } from '../../../common/objects.js';\nimport { Range } from '../../../common/range.js';\nimport { getDefaultHoverDelegate } from '../hover/hoverDelegateFactory.js';\nimport { getBaseLayerHoverDelegate } from '../hover/hoverDelegate2.js';\nimport { isString } from '../../../common/types.js';\nimport { stripIcons } from '../../../common/iconLabels.js';\nclass FastLabelNode {\n    constructor(_element) {\n        this._element = _element;\n    }\n    get element() {\n        return this._element;\n    }\n    set textContent(content) {\n        if (this.disposed || content === this._textContent) {\n            return;\n        }\n        this._textContent = content;\n        this._element.textContent = content;\n    }\n    set classNames(classNames) {\n        if (this.disposed || equals(classNames, this._classNames)) {\n            return;\n        }\n        this._classNames = classNames;\n        this._element.classList.value = '';\n        this._element.classList.add(...classNames);\n    }\n    set empty(empty) {\n        if (this.disposed || empty === this._empty) {\n            return;\n        }\n        this._empty = empty;\n        this._element.style.marginLeft = empty ? '0' : '';\n    }\n    dispose() {\n        this.disposed = true;\n    }\n}\nexport class IconLabel extends Disposable {\n    constructor(container, options) {\n        super();\n        this.customHovers = new Map();\n        this.creationOptions = options;\n        this.domNode = this._register(new FastLabelNode(dom.append(container, dom.$('.monaco-icon-label'))));\n        this.labelContainer = dom.append(this.domNode.element, dom.$('.monaco-icon-label-container'));\n        this.nameContainer = dom.append(this.labelContainer, dom.$('span.monaco-icon-name-container'));\n        if (options?.supportHighlights || options?.supportIcons) {\n            this.nameNode = this._register(new LabelWithHighlights(this.nameContainer, !!options.supportIcons));\n        }\n        else {\n            this.nameNode = new Label(this.nameContainer);\n        }\n        this.hoverDelegate = options?.hoverDelegate ?? getDefaultHoverDelegate('mouse');\n    }\n    get element() {\n        return this.domNode.element;\n    }\n    setLabel(label, description, options) {\n        const labelClasses = ['monaco-icon-label'];\n        const containerClasses = ['monaco-icon-label-container'];\n        let ariaLabel = '';\n        if (options) {\n            if (options.extraClasses) {\n                labelClasses.push(...options.extraClasses);\n            }\n            if (options.italic) {\n                labelClasses.push('italic');\n            }\n            if (options.strikethrough) {\n                labelClasses.push('strikethrough');\n            }\n            if (options.disabledCommand) {\n                containerClasses.push('disabled');\n            }\n            if (options.title) {\n                if (typeof options.title === 'string') {\n                    ariaLabel += options.title;\n                }\n                else {\n                    ariaLabel += label;\n                }\n            }\n        }\n        const existingIconNode = this.domNode.element.querySelector('.monaco-icon-label-iconpath');\n        if (options?.iconPath) {\n            let iconNode;\n            if (!existingIconNode || !(dom.isHTMLElement(existingIconNode))) {\n                iconNode = dom.$('.monaco-icon-label-iconpath');\n                this.domNode.element.prepend(iconNode);\n            }\n            else {\n                iconNode = existingIconNode;\n            }\n            iconNode.style.backgroundImage = dom.asCSSUrl(options?.iconPath);\n        }\n        else if (existingIconNode) {\n            existingIconNode.remove();\n        }\n        this.domNode.classNames = labelClasses;\n        this.domNode.element.setAttribute('aria-label', ariaLabel);\n        this.labelContainer.classList.value = '';\n        this.labelContainer.classList.add(...containerClasses);\n        this.setupHover(options?.descriptionTitle ? this.labelContainer : this.element, options?.title);\n        this.nameNode.setLabel(label, options);\n        if (description || this.descriptionNode) {\n            const descriptionNode = this.getOrCreateDescriptionNode();\n            if (descriptionNode instanceof HighlightedLabel) {\n                descriptionNode.set(description || '', options ? options.descriptionMatches : undefined, undefined, options?.labelEscapeNewLines);\n                this.setupHover(descriptionNode.element, options?.descriptionTitle);\n            }\n            else {\n                descriptionNode.textContent = description && options?.labelEscapeNewLines ? HighlightedLabel.escapeNewLines(description, []) : (description || '');\n                this.setupHover(descriptionNode.element, options?.descriptionTitle || '');\n                descriptionNode.empty = !description;\n            }\n        }\n        if (options?.suffix || this.suffixNode) {\n            const suffixNode = this.getOrCreateSuffixNode();\n            suffixNode.textContent = options?.suffix ?? '';\n        }\n    }\n    setupHover(htmlElement, tooltip) {\n        const previousCustomHover = this.customHovers.get(htmlElement);\n        if (previousCustomHover) {\n            previousCustomHover.dispose();\n            this.customHovers.delete(htmlElement);\n        }\n        if (!tooltip) {\n            htmlElement.removeAttribute('title');\n            return;\n        }\n        if (this.hoverDelegate.showNativeHover) {\n            function setupNativeHover(htmlElement, tooltip) {\n                if (isString(tooltip)) {\n                    // Icons don't render in the native hover so we strip them out\n                    htmlElement.title = stripIcons(tooltip);\n                }\n                else if (tooltip?.markdownNotSupportedFallback) {\n                    htmlElement.title = tooltip.markdownNotSupportedFallback;\n                }\n                else {\n                    htmlElement.removeAttribute('title');\n                }\n            }\n            setupNativeHover(htmlElement, tooltip);\n        }\n        else {\n            const hoverDisposable = getBaseLayerHoverDelegate().setupManagedHover(this.hoverDelegate, htmlElement, tooltip);\n            if (hoverDisposable) {\n                this.customHovers.set(htmlElement, hoverDisposable);\n            }\n        }\n    }\n    dispose() {\n        super.dispose();\n        for (const disposable of this.customHovers.values()) {\n            disposable.dispose();\n        }\n        this.customHovers.clear();\n    }\n    getOrCreateSuffixNode() {\n        if (!this.suffixNode) {\n            const suffixContainer = this._register(new FastLabelNode(dom.after(this.nameContainer, dom.$('span.monaco-icon-suffix-container'))));\n            this.suffixNode = this._register(new FastLabelNode(dom.append(suffixContainer.element, dom.$('span.label-suffix'))));\n        }\n        return this.suffixNode;\n    }\n    getOrCreateDescriptionNode() {\n        if (!this.descriptionNode) {\n            const descriptionContainer = this._register(new FastLabelNode(dom.append(this.labelContainer, dom.$('span.monaco-icon-description-container'))));\n            if (this.creationOptions?.supportDescriptionHighlights) {\n                this.descriptionNode = this._register(new HighlightedLabel(dom.append(descriptionContainer.element, dom.$('span.label-description')), { supportIcons: !!this.creationOptions.supportIcons }));\n            }\n            else {\n                this.descriptionNode = this._register(new FastLabelNode(dom.append(descriptionContainer.element, dom.$('span.label-description'))));\n            }\n        }\n        return this.descriptionNode;\n    }\n}\nclass Label {\n    constructor(container) {\n        this.container = container;\n        this.label = undefined;\n        this.singleLabel = undefined;\n    }\n    setLabel(label, options) {\n        if (this.label === label && equals(this.options, options)) {\n            return;\n        }\n        this.label = label;\n        this.options = options;\n        if (typeof label === 'string') {\n            if (!this.singleLabel) {\n                this.container.innerText = '';\n                this.container.classList.remove('multiple');\n                this.singleLabel = dom.append(this.container, dom.$('a.label-name', { id: options?.domId }));\n            }\n            this.singleLabel.textContent = label;\n        }\n        else {\n            this.container.innerText = '';\n            this.container.classList.add('multiple');\n            this.singleLabel = undefined;\n            for (let i = 0; i < label.length; i++) {\n                const l = label[i];\n                const id = options?.domId && `${options?.domId}_${i}`;\n                dom.append(this.container, dom.$('a.label-name', { id, 'data-icon-label-count': label.length, 'data-icon-label-index': i, 'role': 'treeitem' }, l));\n                if (i < label.length - 1) {\n                    dom.append(this.container, dom.$('span.label-separator', undefined, options?.separator || '/'));\n                }\n            }\n        }\n    }\n}\nfunction splitMatches(labels, separator, matches) {\n    if (!matches) {\n        return undefined;\n    }\n    let labelStart = 0;\n    return labels.map(label => {\n        const labelRange = { start: labelStart, end: labelStart + label.length };\n        const result = matches\n            .map(match => Range.intersect(labelRange, match))\n            .filter(range => !Range.isEmpty(range))\n            .map(({ start, end }) => ({ start: start - labelStart, end: end - labelStart }));\n        labelStart = labelRange.end + separator.length;\n        return result;\n    });\n}\nclass LabelWithHighlights extends Disposable {\n    constructor(container, supportIcons) {\n        super();\n        this.container = container;\n        this.supportIcons = supportIcons;\n        this.label = undefined;\n        this.singleLabel = undefined;\n    }\n    setLabel(label, options) {\n        if (this.label === label && equals(this.options, options)) {\n            return;\n        }\n        this.label = label;\n        this.options = options;\n        if (typeof label === 'string') {\n            if (!this.singleLabel) {\n                this.container.innerText = '';\n                this.container.classList.remove('multiple');\n                this.singleLabel = this._register(new HighlightedLabel(dom.append(this.container, dom.$('a.label-name', { id: options?.domId })), { supportIcons: this.supportIcons }));\n            }\n            this.singleLabel.set(label, options?.matches, undefined, options?.labelEscapeNewLines);\n        }\n        else {\n            this.container.innerText = '';\n            this.container.classList.add('multiple');\n            this.singleLabel = undefined;\n            const separator = options?.separator || '/';\n            const matches = splitMatches(label, separator, options?.matches);\n            for (let i = 0; i < label.length; i++) {\n                const l = label[i];\n                const m = matches ? matches[i] : undefined;\n                const id = options?.domId && `${options?.domId}_${i}`;\n                const name = dom.$('a.label-name', { id, 'data-icon-label-count': label.length, 'data-icon-label-index': i, 'role': 'treeitem' });\n                const highlightedLabel = this._register(new HighlightedLabel(dom.append(this.container, name), { supportIcons: this.supportIcons }));\n                highlightedLabel.set(l, m, undefined, options?.labelEscapeNewLines);\n                if (i < label.length - 1) {\n                    dom.append(name, dom.$('span.label-separator', undefined, separator));\n                }\n            }\n        }\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,iBAAiB;AACxB,OAAO,KAAKA,GAAG,MAAM,cAAc;AACnC,SAASC,gBAAgB,QAAQ,yCAAyC;AAC1E,SAASC,UAAU,QAAQ,8BAA8B;AACzD,SAASC,MAAM,QAAQ,4BAA4B;AACnD,SAASC,KAAK,QAAQ,0BAA0B;AAChD,SAASC,uBAAuB,QAAQ,kCAAkC;AAC1E,SAASC,yBAAyB,QAAQ,4BAA4B;AACtE,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,UAAU,QAAQ,+BAA+B;AAC1D,MAAMC,aAAa,CAAC;EAChBC,WAAWA,CAACC,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACA,IAAIC,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACD,QAAQ;EACxB;EACA,IAAIE,WAAWA,CAACC,OAAO,EAAE;IACrB,IAAI,IAAI,CAACC,QAAQ,IAAID,OAAO,KAAK,IAAI,CAACE,YAAY,EAAE;MAChD;IACJ;IACA,IAAI,CAACA,YAAY,GAAGF,OAAO;IAC3B,IAAI,CAACH,QAAQ,CAACE,WAAW,GAAGC,OAAO;EACvC;EACA,IAAIG,UAAUA,CAACA,UAAU,EAAE;IACvB,IAAI,IAAI,CAACF,QAAQ,IAAIZ,MAAM,CAACc,UAAU,EAAE,IAAI,CAACC,WAAW,CAAC,EAAE;MACvD;IACJ;IACA,IAAI,CAACA,WAAW,GAAGD,UAAU;IAC7B,IAAI,CAACN,QAAQ,CAACQ,SAAS,CAACC,KAAK,GAAG,EAAE;IAClC,IAAI,CAACT,QAAQ,CAACQ,SAAS,CAACE,GAAG,CAAC,GAAGJ,UAAU,CAAC;EAC9C;EACA,IAAIK,KAAKA,CAACA,KAAK,EAAE;IACb,IAAI,IAAI,CAACP,QAAQ,IAAIO,KAAK,KAAK,IAAI,CAACC,MAAM,EAAE;MACxC;IACJ;IACA,IAAI,CAACA,MAAM,GAAGD,KAAK;IACnB,IAAI,CAACX,QAAQ,CAACa,KAAK,CAACC,UAAU,GAAGH,KAAK,GAAG,GAAG,GAAG,EAAE;EACrD;EACAI,OAAOA,CAAA,EAAG;IACN,IAAI,CAACX,QAAQ,GAAG,IAAI;EACxB;AACJ;AACA,OAAO,MAAMY,SAAS,SAASzB,UAAU,CAAC;EACtCQ,WAAWA,CAACkB,SAAS,EAAEC,OAAO,EAAE;IAC5B,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,YAAY,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC7B,IAAI,CAACC,eAAe,GAAGH,OAAO;IAC9B,IAAI,CAACI,OAAO,GAAG,IAAI,CAACC,SAAS,CAAC,IAAIzB,aAAa,CAACT,GAAG,CAACmC,MAAM,CAACP,SAAS,EAAE5B,GAAG,CAACoC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC;IACpG,IAAI,CAACC,cAAc,GAAGrC,GAAG,CAACmC,MAAM,CAAC,IAAI,CAACF,OAAO,CAACrB,OAAO,EAAEZ,GAAG,CAACoC,CAAC,CAAC,8BAA8B,CAAC,CAAC;IAC7F,IAAI,CAACE,aAAa,GAAGtC,GAAG,CAACmC,MAAM,CAAC,IAAI,CAACE,cAAc,EAAErC,GAAG,CAACoC,CAAC,CAAC,iCAAiC,CAAC,CAAC;IAC9F,IAAIP,OAAO,EAAEU,iBAAiB,IAAIV,OAAO,EAAEW,YAAY,EAAE;MACrD,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACP,SAAS,CAAC,IAAIQ,mBAAmB,CAAC,IAAI,CAACJ,aAAa,EAAE,CAAC,CAACT,OAAO,CAACW,YAAY,CAAC,CAAC;IACvG,CAAC,MACI;MACD,IAAI,CAACC,QAAQ,GAAG,IAAIE,KAAK,CAAC,IAAI,CAACL,aAAa,CAAC;IACjD;IACA,IAAI,CAACM,aAAa,GAAGf,OAAO,EAAEe,aAAa,IAAIvC,uBAAuB,CAAC,OAAO,CAAC;EACnF;EACA,IAAIO,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACqB,OAAO,CAACrB,OAAO;EAC/B;EACAiC,QAAQA,CAACC,KAAK,EAAEC,WAAW,EAAElB,OAAO,EAAE;IAClC,MAAMmB,YAAY,GAAG,CAAC,mBAAmB,CAAC;IAC1C,MAAMC,gBAAgB,GAAG,CAAC,6BAA6B,CAAC;IACxD,IAAIC,SAAS,GAAG,EAAE;IAClB,IAAIrB,OAAO,EAAE;MACT,IAAIA,OAAO,CAACsB,YAAY,EAAE;QACtBH,YAAY,CAACI,IAAI,CAAC,GAAGvB,OAAO,CAACsB,YAAY,CAAC;MAC9C;MACA,IAAItB,OAAO,CAACwB,MAAM,EAAE;QAChBL,YAAY,CAACI,IAAI,CAAC,QAAQ,CAAC;MAC/B;MACA,IAAIvB,OAAO,CAACyB,aAAa,EAAE;QACvBN,YAAY,CAACI,IAAI,CAAC,eAAe,CAAC;MACtC;MACA,IAAIvB,OAAO,CAAC0B,eAAe,EAAE;QACzBN,gBAAgB,CAACG,IAAI,CAAC,UAAU,CAAC;MACrC;MACA,IAAIvB,OAAO,CAAC2B,KAAK,EAAE;QACf,IAAI,OAAO3B,OAAO,CAAC2B,KAAK,KAAK,QAAQ,EAAE;UACnCN,SAAS,IAAIrB,OAAO,CAAC2B,KAAK;QAC9B,CAAC,MACI;UACDN,SAAS,IAAIJ,KAAK;QACtB;MACJ;IACJ;IACA,MAAMW,gBAAgB,GAAG,IAAI,CAACxB,OAAO,CAACrB,OAAO,CAAC8C,aAAa,CAAC,6BAA6B,CAAC;IAC1F,IAAI7B,OAAO,EAAE8B,QAAQ,EAAE;MACnB,IAAIC,QAAQ;MACZ,IAAI,CAACH,gBAAgB,IAAI,CAAEzD,GAAG,CAAC6D,aAAa,CAACJ,gBAAgB,CAAE,EAAE;QAC7DG,QAAQ,GAAG5D,GAAG,CAACoC,CAAC,CAAC,6BAA6B,CAAC;QAC/C,IAAI,CAACH,OAAO,CAACrB,OAAO,CAACkD,OAAO,CAACF,QAAQ,CAAC;MAC1C,CAAC,MACI;QACDA,QAAQ,GAAGH,gBAAgB;MAC/B;MACAG,QAAQ,CAACpC,KAAK,CAACuC,eAAe,GAAG/D,GAAG,CAACgE,QAAQ,CAACnC,OAAO,EAAE8B,QAAQ,CAAC;IACpE,CAAC,MACI,IAAIF,gBAAgB,EAAE;MACvBA,gBAAgB,CAACQ,MAAM,CAAC,CAAC;IAC7B;IACA,IAAI,CAAChC,OAAO,CAAChB,UAAU,GAAG+B,YAAY;IACtC,IAAI,CAACf,OAAO,CAACrB,OAAO,CAACsD,YAAY,CAAC,YAAY,EAAEhB,SAAS,CAAC;IAC1D,IAAI,CAACb,cAAc,CAAClB,SAAS,CAACC,KAAK,GAAG,EAAE;IACxC,IAAI,CAACiB,cAAc,CAAClB,SAAS,CAACE,GAAG,CAAC,GAAG4B,gBAAgB,CAAC;IACtD,IAAI,CAACkB,UAAU,CAACtC,OAAO,EAAEuC,gBAAgB,GAAG,IAAI,CAAC/B,cAAc,GAAG,IAAI,CAACzB,OAAO,EAAEiB,OAAO,EAAE2B,KAAK,CAAC;IAC/F,IAAI,CAACf,QAAQ,CAACI,QAAQ,CAACC,KAAK,EAAEjB,OAAO,CAAC;IACtC,IAAIkB,WAAW,IAAI,IAAI,CAACsB,eAAe,EAAE;MACrC,MAAMA,eAAe,GAAG,IAAI,CAACC,0BAA0B,CAAC,CAAC;MACzD,IAAID,eAAe,YAAYpE,gBAAgB,EAAE;QAC7CoE,eAAe,CAACE,GAAG,CAACxB,WAAW,IAAI,EAAE,EAAElB,OAAO,GAAGA,OAAO,CAAC2C,kBAAkB,GAAGC,SAAS,EAAEA,SAAS,EAAE5C,OAAO,EAAE6C,mBAAmB,CAAC;QACjI,IAAI,CAACP,UAAU,CAACE,eAAe,CAACzD,OAAO,EAAEiB,OAAO,EAAEuC,gBAAgB,CAAC;MACvE,CAAC,MACI;QACDC,eAAe,CAACxD,WAAW,GAAGkC,WAAW,IAAIlB,OAAO,EAAE6C,mBAAmB,GAAGzE,gBAAgB,CAAC0E,cAAc,CAAC5B,WAAW,EAAE,EAAE,CAAC,GAAIA,WAAW,IAAI,EAAG;QAClJ,IAAI,CAACoB,UAAU,CAACE,eAAe,CAACzD,OAAO,EAAEiB,OAAO,EAAEuC,gBAAgB,IAAI,EAAE,CAAC;QACzEC,eAAe,CAAC/C,KAAK,GAAG,CAACyB,WAAW;MACxC;IACJ;IACA,IAAIlB,OAAO,EAAE+C,MAAM,IAAI,IAAI,CAACC,UAAU,EAAE;MACpC,MAAMA,UAAU,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;MAC/CD,UAAU,CAAChE,WAAW,GAAGgB,OAAO,EAAE+C,MAAM,IAAI,EAAE;IAClD;EACJ;EACAT,UAAUA,CAACY,WAAW,EAAEC,OAAO,EAAE;IAC7B,MAAMC,mBAAmB,GAAG,IAAI,CAACnD,YAAY,CAACoD,GAAG,CAACH,WAAW,CAAC;IAC9D,IAAIE,mBAAmB,EAAE;MACrBA,mBAAmB,CAACvD,OAAO,CAAC,CAAC;MAC7B,IAAI,CAACI,YAAY,CAACqD,MAAM,CAACJ,WAAW,CAAC;IACzC;IACA,IAAI,CAACC,OAAO,EAAE;MACVD,WAAW,CAACK,eAAe,CAAC,OAAO,CAAC;MACpC;IACJ;IACA,IAAI,IAAI,CAACxC,aAAa,CAACyC,eAAe,EAAE;MACpC,SAASC,gBAAgBA,CAACP,WAAW,EAAEC,OAAO,EAAE;QAC5C,IAAIzE,QAAQ,CAACyE,OAAO,CAAC,EAAE;UACnB;UACAD,WAAW,CAACvB,KAAK,GAAGhD,UAAU,CAACwE,OAAO,CAAC;QAC3C,CAAC,MACI,IAAIA,OAAO,EAAEO,4BAA4B,EAAE;UAC5CR,WAAW,CAACvB,KAAK,GAAGwB,OAAO,CAACO,4BAA4B;QAC5D,CAAC,MACI;UACDR,WAAW,CAACK,eAAe,CAAC,OAAO,CAAC;QACxC;MACJ;MACAE,gBAAgB,CAACP,WAAW,EAAEC,OAAO,CAAC;IAC1C,CAAC,MACI;MACD,MAAMQ,eAAe,GAAGlF,yBAAyB,CAAC,CAAC,CAACmF,iBAAiB,CAAC,IAAI,CAAC7C,aAAa,EAAEmC,WAAW,EAAEC,OAAO,CAAC;MAC/G,IAAIQ,eAAe,EAAE;QACjB,IAAI,CAAC1D,YAAY,CAACyC,GAAG,CAACQ,WAAW,EAAES,eAAe,CAAC;MACvD;IACJ;EACJ;EACA9D,OAAOA,CAAA,EAAG;IACN,KAAK,CAACA,OAAO,CAAC,CAAC;IACf,KAAK,MAAMgE,UAAU,IAAI,IAAI,CAAC5D,YAAY,CAAC6D,MAAM,CAAC,CAAC,EAAE;MACjDD,UAAU,CAAChE,OAAO,CAAC,CAAC;IACxB;IACA,IAAI,CAACI,YAAY,CAAC8D,KAAK,CAAC,CAAC;EAC7B;EACAd,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAAC,IAAI,CAACD,UAAU,EAAE;MAClB,MAAMgB,eAAe,GAAG,IAAI,CAAC3D,SAAS,CAAC,IAAIzB,aAAa,CAACT,GAAG,CAAC8F,KAAK,CAAC,IAAI,CAACxD,aAAa,EAAEtC,GAAG,CAACoC,CAAC,CAAC,mCAAmC,CAAC,CAAC,CAAC,CAAC;MACpI,IAAI,CAACyC,UAAU,GAAG,IAAI,CAAC3C,SAAS,CAAC,IAAIzB,aAAa,CAACT,GAAG,CAACmC,MAAM,CAAC0D,eAAe,CAACjF,OAAO,EAAEZ,GAAG,CAACoC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;IACxH;IACA,OAAO,IAAI,CAACyC,UAAU;EAC1B;EACAP,0BAA0BA,CAAA,EAAG;IACzB,IAAI,CAAC,IAAI,CAACD,eAAe,EAAE;MACvB,MAAM0B,oBAAoB,GAAG,IAAI,CAAC7D,SAAS,CAAC,IAAIzB,aAAa,CAACT,GAAG,CAACmC,MAAM,CAAC,IAAI,CAACE,cAAc,EAAErC,GAAG,CAACoC,CAAC,CAAC,wCAAwC,CAAC,CAAC,CAAC,CAAC;MAChJ,IAAI,IAAI,CAACJ,eAAe,EAAEgE,4BAA4B,EAAE;QACpD,IAAI,CAAC3B,eAAe,GAAG,IAAI,CAACnC,SAAS,CAAC,IAAIjC,gBAAgB,CAACD,GAAG,CAACmC,MAAM,CAAC4D,oBAAoB,CAACnF,OAAO,EAAEZ,GAAG,CAACoC,CAAC,CAAC,wBAAwB,CAAC,CAAC,EAAE;UAAEI,YAAY,EAAE,CAAC,CAAC,IAAI,CAACR,eAAe,CAACQ;QAAa,CAAC,CAAC,CAAC;MACjM,CAAC,MACI;QACD,IAAI,CAAC6B,eAAe,GAAG,IAAI,CAACnC,SAAS,CAAC,IAAIzB,aAAa,CAACT,GAAG,CAACmC,MAAM,CAAC4D,oBAAoB,CAACnF,OAAO,EAAEZ,GAAG,CAACoC,CAAC,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC;MACvI;IACJ;IACA,OAAO,IAAI,CAACiC,eAAe;EAC/B;AACJ;AACA,MAAM1B,KAAK,CAAC;EACRjC,WAAWA,CAACkB,SAAS,EAAE;IACnB,IAAI,CAACA,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACkB,KAAK,GAAG2B,SAAS;IACtB,IAAI,CAACwB,WAAW,GAAGxB,SAAS;EAChC;EACA5B,QAAQA,CAACC,KAAK,EAAEjB,OAAO,EAAE;IACrB,IAAI,IAAI,CAACiB,KAAK,KAAKA,KAAK,IAAI3C,MAAM,CAAC,IAAI,CAAC0B,OAAO,EAAEA,OAAO,CAAC,EAAE;MACvD;IACJ;IACA,IAAI,CAACiB,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACjB,OAAO,GAAGA,OAAO;IACtB,IAAI,OAAOiB,KAAK,KAAK,QAAQ,EAAE;MAC3B,IAAI,CAAC,IAAI,CAACmD,WAAW,EAAE;QACnB,IAAI,CAACrE,SAAS,CAACsE,SAAS,GAAG,EAAE;QAC7B,IAAI,CAACtE,SAAS,CAACT,SAAS,CAAC8C,MAAM,CAAC,UAAU,CAAC;QAC3C,IAAI,CAACgC,WAAW,GAAGjG,GAAG,CAACmC,MAAM,CAAC,IAAI,CAACP,SAAS,EAAE5B,GAAG,CAACoC,CAAC,CAAC,cAAc,EAAE;UAAE+D,EAAE,EAAEtE,OAAO,EAAEuE;QAAM,CAAC,CAAC,CAAC;MAChG;MACA,IAAI,CAACH,WAAW,CAACpF,WAAW,GAAGiC,KAAK;IACxC,CAAC,MACI;MACD,IAAI,CAAClB,SAAS,CAACsE,SAAS,GAAG,EAAE;MAC7B,IAAI,CAACtE,SAAS,CAACT,SAAS,CAACE,GAAG,CAAC,UAAU,CAAC;MACxC,IAAI,CAAC4E,WAAW,GAAGxB,SAAS;MAC5B,KAAK,IAAI4B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvD,KAAK,CAACwD,MAAM,EAAED,CAAC,EAAE,EAAE;QACnC,MAAME,CAAC,GAAGzD,KAAK,CAACuD,CAAC,CAAC;QAClB,MAAMF,EAAE,GAAGtE,OAAO,EAAEuE,KAAK,IAAI,GAAGvE,OAAO,EAAEuE,KAAK,IAAIC,CAAC,EAAE;QACrDrG,GAAG,CAACmC,MAAM,CAAC,IAAI,CAACP,SAAS,EAAE5B,GAAG,CAACoC,CAAC,CAAC,cAAc,EAAE;UAAE+D,EAAE;UAAE,uBAAuB,EAAErD,KAAK,CAACwD,MAAM;UAAE,uBAAuB,EAAED,CAAC;UAAE,MAAM,EAAE;QAAW,CAAC,EAAEE,CAAC,CAAC,CAAC;QACnJ,IAAIF,CAAC,GAAGvD,KAAK,CAACwD,MAAM,GAAG,CAAC,EAAE;UACtBtG,GAAG,CAACmC,MAAM,CAAC,IAAI,CAACP,SAAS,EAAE5B,GAAG,CAACoC,CAAC,CAAC,sBAAsB,EAAEqC,SAAS,EAAE5C,OAAO,EAAE2E,SAAS,IAAI,GAAG,CAAC,CAAC;QACnG;MACJ;IACJ;EACJ;AACJ;AACA,SAASC,YAAYA,CAACC,MAAM,EAAEF,SAAS,EAAEG,OAAO,EAAE;EAC9C,IAAI,CAACA,OAAO,EAAE;IACV,OAAOlC,SAAS;EACpB;EACA,IAAImC,UAAU,GAAG,CAAC;EAClB,OAAOF,MAAM,CAACG,GAAG,CAAC/D,KAAK,IAAI;IACvB,MAAMgE,UAAU,GAAG;MAAEC,KAAK,EAAEH,UAAU;MAAEI,GAAG,EAAEJ,UAAU,GAAG9D,KAAK,CAACwD;IAAO,CAAC;IACxE,MAAMW,MAAM,GAAGN,OAAO,CACjBE,GAAG,CAACK,KAAK,IAAI9G,KAAK,CAAC+G,SAAS,CAACL,UAAU,EAAEI,KAAK,CAAC,CAAC,CAChDE,MAAM,CAACC,KAAK,IAAI,CAACjH,KAAK,CAACkH,OAAO,CAACD,KAAK,CAAC,CAAC,CACtCR,GAAG,CAAC,CAAC;MAAEE,KAAK;MAAEC;IAAI,CAAC,MAAM;MAAED,KAAK,EAAEA,KAAK,GAAGH,UAAU;MAAEI,GAAG,EAAEA,GAAG,GAAGJ;IAAW,CAAC,CAAC,CAAC;IACpFA,UAAU,GAAGE,UAAU,CAACE,GAAG,GAAGR,SAAS,CAACF,MAAM;IAC9C,OAAOW,MAAM;EACjB,CAAC,CAAC;AACN;AACA,MAAMvE,mBAAmB,SAASxC,UAAU,CAAC;EACzCQ,WAAWA,CAACkB,SAAS,EAAEY,YAAY,EAAE;IACjC,KAAK,CAAC,CAAC;IACP,IAAI,CAACZ,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACY,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACM,KAAK,GAAG2B,SAAS;IACtB,IAAI,CAACwB,WAAW,GAAGxB,SAAS;EAChC;EACA5B,QAAQA,CAACC,KAAK,EAAEjB,OAAO,EAAE;IACrB,IAAI,IAAI,CAACiB,KAAK,KAAKA,KAAK,IAAI3C,MAAM,CAAC,IAAI,CAAC0B,OAAO,EAAEA,OAAO,CAAC,EAAE;MACvD;IACJ;IACA,IAAI,CAACiB,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACjB,OAAO,GAAGA,OAAO;IACtB,IAAI,OAAOiB,KAAK,KAAK,QAAQ,EAAE;MAC3B,IAAI,CAAC,IAAI,CAACmD,WAAW,EAAE;QACnB,IAAI,CAACrE,SAAS,CAACsE,SAAS,GAAG,EAAE;QAC7B,IAAI,CAACtE,SAAS,CAACT,SAAS,CAAC8C,MAAM,CAAC,UAAU,CAAC;QAC3C,IAAI,CAACgC,WAAW,GAAG,IAAI,CAAC/D,SAAS,CAAC,IAAIjC,gBAAgB,CAACD,GAAG,CAACmC,MAAM,CAAC,IAAI,CAACP,SAAS,EAAE5B,GAAG,CAACoC,CAAC,CAAC,cAAc,EAAE;UAAE+D,EAAE,EAAEtE,OAAO,EAAEuE;QAAM,CAAC,CAAC,CAAC,EAAE;UAAE5D,YAAY,EAAE,IAAI,CAACA;QAAa,CAAC,CAAC,CAAC;MAC3K;MACA,IAAI,CAACyD,WAAW,CAAC1B,GAAG,CAACzB,KAAK,EAAEjB,OAAO,EAAE8E,OAAO,EAAElC,SAAS,EAAE5C,OAAO,EAAE6C,mBAAmB,CAAC;IAC1F,CAAC,MACI;MACD,IAAI,CAAC9C,SAAS,CAACsE,SAAS,GAAG,EAAE;MAC7B,IAAI,CAACtE,SAAS,CAACT,SAAS,CAACE,GAAG,CAAC,UAAU,CAAC;MACxC,IAAI,CAAC4E,WAAW,GAAGxB,SAAS;MAC5B,MAAM+B,SAAS,GAAG3E,OAAO,EAAE2E,SAAS,IAAI,GAAG;MAC3C,MAAMG,OAAO,GAAGF,YAAY,CAAC3D,KAAK,EAAE0D,SAAS,EAAE3E,OAAO,EAAE8E,OAAO,CAAC;MAChE,KAAK,IAAIN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvD,KAAK,CAACwD,MAAM,EAAED,CAAC,EAAE,EAAE;QACnC,MAAME,CAAC,GAAGzD,KAAK,CAACuD,CAAC,CAAC;QAClB,MAAMkB,CAAC,GAAGZ,OAAO,GAAGA,OAAO,CAACN,CAAC,CAAC,GAAG5B,SAAS;QAC1C,MAAM0B,EAAE,GAAGtE,OAAO,EAAEuE,KAAK,IAAI,GAAGvE,OAAO,EAAEuE,KAAK,IAAIC,CAAC,EAAE;QACrD,MAAMmB,IAAI,GAAGxH,GAAG,CAACoC,CAAC,CAAC,cAAc,EAAE;UAAE+D,EAAE;UAAE,uBAAuB,EAAErD,KAAK,CAACwD,MAAM;UAAE,uBAAuB,EAAED,CAAC;UAAE,MAAM,EAAE;QAAW,CAAC,CAAC;QACjI,MAAMoB,gBAAgB,GAAG,IAAI,CAACvF,SAAS,CAAC,IAAIjC,gBAAgB,CAACD,GAAG,CAACmC,MAAM,CAAC,IAAI,CAACP,SAAS,EAAE4F,IAAI,CAAC,EAAE;UAAEhF,YAAY,EAAE,IAAI,CAACA;QAAa,CAAC,CAAC,CAAC;QACpIiF,gBAAgB,CAAClD,GAAG,CAACgC,CAAC,EAAEgB,CAAC,EAAE9C,SAAS,EAAE5C,OAAO,EAAE6C,mBAAmB,CAAC;QACnE,IAAI2B,CAAC,GAAGvD,KAAK,CAACwD,MAAM,GAAG,CAAC,EAAE;UACtBtG,GAAG,CAACmC,MAAM,CAACqF,IAAI,EAAExH,GAAG,CAACoC,CAAC,CAAC,sBAAsB,EAAEqC,SAAS,EAAE+B,SAAS,CAAC,CAAC;QACzE;MACJ;IACJ;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}