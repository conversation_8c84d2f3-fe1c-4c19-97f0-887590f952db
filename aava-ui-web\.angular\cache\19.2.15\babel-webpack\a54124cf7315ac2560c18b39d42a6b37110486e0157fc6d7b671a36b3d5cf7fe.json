{"ast": null, "code": "import _asyncToGenerator from \"C:/console/aava-ui-web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = this && this.__param || function (paramIndex, decorator) {\n  return function (target, key) {\n    decorator(target, key, paramIndex);\n  };\n};\nimport * as dom from '../../../../base/browser/dom.js';\nimport { Disposable } from '../../../../base/common/lifecycle.js';\nimport { TokenizationRegistry } from '../../../common/languages.js';\nimport { HoverOperation } from './hoverOperation.js';\nimport { HoverParticipantRegistry, HoverRangeAnchor } from './hoverTypes.js';\nimport { IInstantiationService } from '../../../../platform/instantiation/common/instantiation.js';\nimport { IKeybindingService } from '../../../../platform/keybinding/common/keybinding.js';\nimport { ContentHoverWidget } from './contentHoverWidget.js';\nimport { ContentHoverComputer } from './contentHoverComputer.js';\nimport { HoverResult } from './contentHoverTypes.js';\nimport { Emitter } from '../../../../base/common/event.js';\nimport { RenderedContentHover } from './contentHoverRendered.js';\nimport { isMousePositionWithinElement } from './hoverUtils.js';\nlet ContentHoverWidgetWrapper = class ContentHoverWidgetWrapper extends Disposable {\n  constructor(_editor, _instantiationService, _keybindingService) {\n    super();\n    this._editor = _editor;\n    this._instantiationService = _instantiationService;\n    this._keybindingService = _keybindingService;\n    this._currentResult = null;\n    this._onContentsChanged = this._register(new Emitter());\n    this.onContentsChanged = this._onContentsChanged.event;\n    this._contentHoverWidget = this._register(this._instantiationService.createInstance(ContentHoverWidget, this._editor));\n    this._participants = this._initializeHoverParticipants();\n    this._computer = new ContentHoverComputer(this._editor, this._participants);\n    this._hoverOperation = this._register(new HoverOperation(this._editor, this._computer));\n    this._registerListeners();\n  }\n  _initializeHoverParticipants() {\n    const participants = [];\n    for (const participant of HoverParticipantRegistry.getAll()) {\n      const participantInstance = this._instantiationService.createInstance(participant, this._editor);\n      participants.push(participantInstance);\n    }\n    participants.sort((p1, p2) => p1.hoverOrdinal - p2.hoverOrdinal);\n    this._register(this._contentHoverWidget.onDidResize(() => {\n      this._participants.forEach(participant => participant.handleResize?.());\n    }));\n    return participants;\n  }\n  _registerListeners() {\n    this._register(this._hoverOperation.onResult(result => {\n      if (!this._computer.anchor) {\n        // invalid state, ignore result\n        return;\n      }\n      const messages = result.hasLoadingMessage ? this._addLoadingMessage(result.value) : result.value;\n      this._withResult(new HoverResult(this._computer.anchor, messages, result.isComplete));\n    }));\n    const contentHoverWidgetNode = this._contentHoverWidget.getDomNode();\n    this._register(dom.addStandardDisposableListener(contentHoverWidgetNode, 'keydown', e => {\n      if (e.equals(9 /* KeyCode.Escape */)) {\n        this.hide();\n      }\n    }));\n    this._register(dom.addStandardDisposableListener(contentHoverWidgetNode, 'mouseleave', e => {\n      this._onMouseLeave(e);\n    }));\n    this._register(TokenizationRegistry.onDidChange(() => {\n      if (this._contentHoverWidget.position && this._currentResult) {\n        this._setCurrentResult(this._currentResult); // render again\n      }\n    }));\n  }\n  /**\n   * Returns true if the hover shows now or will show.\n   */\n  _startShowingOrUpdateHover(anchor, mode, source, focus, mouseEvent) {\n    const contentHoverIsVisible = this._contentHoverWidget.position && this._currentResult;\n    if (!contentHoverIsVisible) {\n      if (anchor) {\n        this._startHoverOperationIfNecessary(anchor, mode, source, focus, false);\n        return true;\n      }\n      return false;\n    }\n    const isHoverSticky = this._editor.getOption(60 /* EditorOption.hover */).sticky;\n    const isMouseGettingCloser = mouseEvent && this._contentHoverWidget.isMouseGettingCloser(mouseEvent.event.posx, mouseEvent.event.posy);\n    const isHoverStickyAndIsMouseGettingCloser = isHoverSticky && isMouseGettingCloser;\n    // The mouse is getting closer to the hover, so we will keep the hover untouched\n    // But we will kick off a hover update at the new anchor, insisting on keeping the hover visible.\n    if (isHoverStickyAndIsMouseGettingCloser) {\n      if (anchor) {\n        this._startHoverOperationIfNecessary(anchor, mode, source, focus, true);\n      }\n      return true;\n    }\n    // If mouse is not getting closer and anchor not defined, hide the hover\n    if (!anchor) {\n      this._setCurrentResult(null);\n      return false;\n    }\n    // If mouse if not getting closer and anchor is defined, and the new anchor is the same as the previous anchor\n    const currentAnchorEqualsPreviousAnchor = this._currentResult.anchor.equals(anchor);\n    if (currentAnchorEqualsPreviousAnchor) {\n      return true;\n    }\n    // If mouse if not getting closer and anchor is defined, and the new anchor is not compatible with the previous anchor\n    const currentAnchorCompatibleWithPreviousAnchor = anchor.canAdoptVisibleHover(this._currentResult.anchor, this._contentHoverWidget.position);\n    if (!currentAnchorCompatibleWithPreviousAnchor) {\n      this._setCurrentResult(null);\n      this._startHoverOperationIfNecessary(anchor, mode, source, focus, false);\n      return true;\n    }\n    // We aren't getting any closer to the hover, so we will filter existing results\n    // and keep those which also apply to the new anchor.\n    this._setCurrentResult(this._currentResult.filter(anchor));\n    this._startHoverOperationIfNecessary(anchor, mode, source, focus, false);\n    return true;\n  }\n  _startHoverOperationIfNecessary(anchor, mode, source, focus, insistOnKeepingHoverVisible) {\n    const currentAnchorEqualToPreviousHover = this._computer.anchor && this._computer.anchor.equals(anchor);\n    if (currentAnchorEqualToPreviousHover) {\n      return;\n    }\n    this._hoverOperation.cancel();\n    this._computer.anchor = anchor;\n    this._computer.shouldFocus = focus;\n    this._computer.source = source;\n    this._computer.insistOnKeepingHoverVisible = insistOnKeepingHoverVisible;\n    this._hoverOperation.start(mode);\n  }\n  _setCurrentResult(hoverResult) {\n    let currentHoverResult = hoverResult;\n    const currentResultEqualToPreviousResult = this._currentResult === currentHoverResult;\n    if (currentResultEqualToPreviousResult) {\n      return;\n    }\n    const currentHoverResultIsEmpty = currentHoverResult && currentHoverResult.hoverParts.length === 0;\n    if (currentHoverResultIsEmpty) {\n      currentHoverResult = null;\n    }\n    this._currentResult = currentHoverResult;\n    if (this._currentResult) {\n      this._showHover(this._currentResult);\n    } else {\n      this._hideHover();\n    }\n  }\n  _addLoadingMessage(result) {\n    if (!this._computer.anchor) {\n      return result;\n    }\n    for (const participant of this._participants) {\n      if (!participant.createLoadingMessage) {\n        continue;\n      }\n      const loadingMessage = participant.createLoadingMessage(this._computer.anchor);\n      if (!loadingMessage) {\n        continue;\n      }\n      return result.slice(0).concat([loadingMessage]);\n    }\n    return result;\n  }\n  _withResult(hoverResult) {\n    const previousHoverIsVisibleWithCompleteResult = this._contentHoverWidget.position && this._currentResult && this._currentResult.isComplete;\n    if (!previousHoverIsVisibleWithCompleteResult) {\n      this._setCurrentResult(hoverResult);\n    }\n    // The hover is visible with a previous complete result.\n    const isCurrentHoverResultComplete = hoverResult.isComplete;\n    if (!isCurrentHoverResultComplete) {\n      // Instead of rendering the new partial result, we wait for the result to be complete.\n      return;\n    }\n    const currentHoverResultIsEmpty = hoverResult.hoverParts.length === 0;\n    const insistOnKeepingPreviousHoverVisible = this._computer.insistOnKeepingHoverVisible;\n    const shouldKeepPreviousHoverVisible = currentHoverResultIsEmpty && insistOnKeepingPreviousHoverVisible;\n    if (shouldKeepPreviousHoverVisible) {\n      // The hover would now hide normally, so we'll keep the previous messages\n      return;\n    }\n    this._setCurrentResult(hoverResult);\n  }\n  _showHover(hoverResult) {\n    const context = this._getHoverContext();\n    this._renderedContentHover = new RenderedContentHover(this._editor, hoverResult, this._participants, this._computer, context, this._keybindingService);\n    if (this._renderedContentHover.domNodeHasChildren) {\n      this._contentHoverWidget.show(this._renderedContentHover);\n    } else {\n      this._renderedContentHover.dispose();\n    }\n  }\n  _hideHover() {\n    this._contentHoverWidget.hide();\n  }\n  _getHoverContext() {\n    const hide = () => {\n      this.hide();\n    };\n    const onContentsChanged = () => {\n      this._onContentsChanged.fire();\n      this._contentHoverWidget.onContentsChanged();\n    };\n    const setMinimumDimensions = dimensions => {\n      this._contentHoverWidget.setMinimumDimensions(dimensions);\n    };\n    return {\n      hide,\n      onContentsChanged,\n      setMinimumDimensions\n    };\n  }\n  showsOrWillShow(mouseEvent) {\n    const isContentWidgetResizing = this._contentHoverWidget.isResizing;\n    if (isContentWidgetResizing) {\n      return true;\n    }\n    const anchorCandidates = this._findHoverAnchorCandidates(mouseEvent);\n    const anchorCandidatesExist = anchorCandidates.length > 0;\n    if (!anchorCandidatesExist) {\n      return this._startShowingOrUpdateHover(null, 0 /* HoverStartMode.Delayed */, 0 /* HoverStartSource.Mouse */, false, mouseEvent);\n    }\n    const anchor = anchorCandidates[0];\n    return this._startShowingOrUpdateHover(anchor, 0 /* HoverStartMode.Delayed */, 0 /* HoverStartSource.Mouse */, false, mouseEvent);\n  }\n  _findHoverAnchorCandidates(mouseEvent) {\n    const anchorCandidates = [];\n    for (const participant of this._participants) {\n      if (!participant.suggestHoverAnchor) {\n        continue;\n      }\n      const anchor = participant.suggestHoverAnchor(mouseEvent);\n      if (!anchor) {\n        continue;\n      }\n      anchorCandidates.push(anchor);\n    }\n    const target = mouseEvent.target;\n    switch (target.type) {\n      case 6 /* MouseTargetType.CONTENT_TEXT */:\n        {\n          anchorCandidates.push(new HoverRangeAnchor(0, target.range, mouseEvent.event.posx, mouseEvent.event.posy));\n          break;\n        }\n      case 7 /* MouseTargetType.CONTENT_EMPTY */:\n        {\n          const epsilon = this._editor.getOption(50 /* EditorOption.fontInfo */).typicalHalfwidthCharacterWidth / 2;\n          // Let hover kick in even when the mouse is technically in the empty area after a line, given the distance is small enough\n          const mouseIsWithinLinesAndCloseToHover = !target.detail.isAfterLines && typeof target.detail.horizontalDistanceToText === 'number' && target.detail.horizontalDistanceToText < epsilon;\n          if (!mouseIsWithinLinesAndCloseToHover) {\n            break;\n          }\n          anchorCandidates.push(new HoverRangeAnchor(0, target.range, mouseEvent.event.posx, mouseEvent.event.posy));\n          break;\n        }\n    }\n    anchorCandidates.sort((a, b) => b.priority - a.priority);\n    return anchorCandidates;\n  }\n  _onMouseLeave(e) {\n    const editorDomNode = this._editor.getDomNode();\n    const isMousePositionOutsideOfEditor = !editorDomNode || !isMousePositionWithinElement(editorDomNode, e.x, e.y);\n    if (isMousePositionOutsideOfEditor) {\n      this.hide();\n    }\n  }\n  startShowingAtRange(range, mode, source, focus) {\n    this._startShowingOrUpdateHover(new HoverRangeAnchor(0, range, undefined, undefined), mode, source, focus, null);\n  }\n  updateHoverVerbosityLevel(action, index, focus) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this._renderedContentHover?.updateHoverVerbosityLevel(action, index, focus);\n    })();\n  }\n  focusedHoverPartIndex() {\n    return this._renderedContentHover?.focusedHoverPartIndex ?? -1;\n  }\n  containsNode(node) {\n    return node ? this._contentHoverWidget.getDomNode().contains(node) : false;\n  }\n  focus() {\n    this._contentHoverWidget.focus();\n  }\n  scrollUp() {\n    this._contentHoverWidget.scrollUp();\n  }\n  scrollDown() {\n    this._contentHoverWidget.scrollDown();\n  }\n  scrollLeft() {\n    this._contentHoverWidget.scrollLeft();\n  }\n  scrollRight() {\n    this._contentHoverWidget.scrollRight();\n  }\n  pageUp() {\n    this._contentHoverWidget.pageUp();\n  }\n  pageDown() {\n    this._contentHoverWidget.pageDown();\n  }\n  goToTop() {\n    this._contentHoverWidget.goToTop();\n  }\n  goToBottom() {\n    this._contentHoverWidget.goToBottom();\n  }\n  hide() {\n    this._computer.anchor = null;\n    this._hoverOperation.cancel();\n    this._setCurrentResult(null);\n  }\n  getDomNode() {\n    return this._contentHoverWidget.getDomNode();\n  }\n  get isColorPickerVisible() {\n    return this._renderedContentHover?.isColorPickerVisible() ?? false;\n  }\n  get isVisibleFromKeyboard() {\n    return this._contentHoverWidget.isVisibleFromKeyboard;\n  }\n  get isVisible() {\n    return this._contentHoverWidget.isVisible;\n  }\n  get isFocused() {\n    return this._contentHoverWidget.isFocused;\n  }\n  get isResizing() {\n    return this._contentHoverWidget.isResizing;\n  }\n  get widget() {\n    return this._contentHoverWidget;\n  }\n};\nContentHoverWidgetWrapper = __decorate([__param(1, IInstantiationService), __param(2, IKeybindingService)], ContentHoverWidgetWrapper);\nexport { ContentHoverWidgetWrapper };", "map": {"version": 3, "names": ["__decorate", "decorators", "target", "key", "desc", "c", "arguments", "length", "r", "Object", "getOwnPropertyDescriptor", "d", "Reflect", "decorate", "i", "defineProperty", "__param", "paramIndex", "decorator", "dom", "Disposable", "TokenizationRegistry", "HoverOperation", "HoverParticipantRegistry", "HoverRangeAnchor", "IInstantiationService", "IKeybindingService", "ContentHoverWidget", "ContentHoverComputer", "HoverResult", "Emitter", "RenderedContentHover", "isMousePositionWithinElement", "ContentHoverWidgetWrapper", "constructor", "_editor", "_instantiationService", "_keybindingService", "_currentResult", "_onContentsChanged", "_register", "onContentsChanged", "event", "_contentHoverWidget", "createInstance", "_participants", "_initializeHoverParticipants", "_computer", "_hoverOperation", "_registerListeners", "participants", "participant", "getAll", "participantInstance", "push", "sort", "p1", "p2", "hoverOrdinal", "onDidResize", "for<PERSON>ach", "handleResize", "onResult", "result", "anchor", "messages", "hasLoadingMessage", "_addLoadingMessage", "value", "_withR<PERSON>ult", "isComplete", "contentHoverWidgetNode", "getDomNode", "addStandardDisposableListener", "e", "equals", "hide", "_onMouseLeave", "onDidChange", "position", "_setCurrentResult", "_startShowingOrUpdateHover", "mode", "source", "focus", "mouseEvent", "contentHoverIsVisible", "_startHoverOperationIfNecessary", "isHoverSticky", "getOption", "sticky", "isMouseGettingCloser", "posx", "posy", "isHoverStickyAndIsMouseGettingCloser", "currentAnchorEqualsPreviousAnchor", "currentAnchorCompatibleWithPreviousAnchor", "canAdoptVisibleHover", "filter", "insistOnKeepingHoverVisible", "currentAnchorEqualToPreviousHover", "cancel", "shouldFocus", "start", "hoverResult", "currentHoverResult", "currentResultEqualToPreviousResult", "currentHoverResultIsEmpty", "hoverParts", "_showHover", "_hideHover", "createLoadingMessage", "loadingMessage", "slice", "concat", "previousHoverIsVisibleWithCompleteResult", "isCurrentHoverResultComplete", "insistOnKeepingPreviousHoverVisible", "shouldKeepPreviousHoverVisible", "context", "_getHoverContext", "_renderedContentHover", "domNodeHasChildren", "show", "dispose", "fire", "setMinimumDimensions", "dimensions", "showsOrWillShow", "isContentWidgetResizing", "isResizing", "anchorCandidates", "_findHoverAnchorCandidates", "anchorCandidatesExist", "suggestHoverAnchor", "type", "range", "epsilon", "typicalHalfwidthCharacterWidth", "mouseIsWithinLinesAndCloseToHover", "detail", "isAfterLines", "horizontalDistanceToText", "a", "b", "priority", "editorDomNode", "isMousePositionOutsideOfEditor", "x", "y", "startShowingAtRange", "undefined", "updateHoverVerbosityLevel", "action", "index", "_this", "_asyncToGenerator", "focusedHoverPartIndex", "containsNode", "node", "contains", "scrollUp", "scrollDown", "scrollLeft", "scrollRight", "pageUp", "pageDown", "goToTop", "goToBottom", "isColorPickerVisible", "isVisibleFromKeyboard", "isVisible", "isFocused", "widget"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/contrib/hover/browser/contentHoverWidgetWrapper.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = (this && this.__param) || function (paramIndex, decorator) {\n    return function (target, key) { decorator(target, key, paramIndex); }\n};\nimport * as dom from '../../../../base/browser/dom.js';\nimport { Disposable } from '../../../../base/common/lifecycle.js';\nimport { TokenizationRegistry } from '../../../common/languages.js';\nimport { HoverOperation } from './hoverOperation.js';\nimport { HoverParticipantRegistry, HoverRangeAnchor } from './hoverTypes.js';\nimport { IInstantiationService } from '../../../../platform/instantiation/common/instantiation.js';\nimport { IKeybindingService } from '../../../../platform/keybinding/common/keybinding.js';\nimport { ContentHoverWidget } from './contentHoverWidget.js';\nimport { ContentHoverComputer } from './contentHoverComputer.js';\nimport { HoverResult } from './contentHoverTypes.js';\nimport { Emitter } from '../../../../base/common/event.js';\nimport { RenderedContentHover } from './contentHoverRendered.js';\nimport { isMousePositionWithinElement } from './hoverUtils.js';\nlet ContentHoverWidgetWrapper = class ContentHoverWidgetWrapper extends Disposable {\n    constructor(_editor, _instantiationService, _keybindingService) {\n        super();\n        this._editor = _editor;\n        this._instantiationService = _instantiationService;\n        this._keybindingService = _keybindingService;\n        this._currentResult = null;\n        this._onContentsChanged = this._register(new Emitter());\n        this.onContentsChanged = this._onContentsChanged.event;\n        this._contentHoverWidget = this._register(this._instantiationService.createInstance(ContentHoverWidget, this._editor));\n        this._participants = this._initializeHoverParticipants();\n        this._computer = new ContentHoverComputer(this._editor, this._participants);\n        this._hoverOperation = this._register(new HoverOperation(this._editor, this._computer));\n        this._registerListeners();\n    }\n    _initializeHoverParticipants() {\n        const participants = [];\n        for (const participant of HoverParticipantRegistry.getAll()) {\n            const participantInstance = this._instantiationService.createInstance(participant, this._editor);\n            participants.push(participantInstance);\n        }\n        participants.sort((p1, p2) => p1.hoverOrdinal - p2.hoverOrdinal);\n        this._register(this._contentHoverWidget.onDidResize(() => {\n            this._participants.forEach(participant => participant.handleResize?.());\n        }));\n        return participants;\n    }\n    _registerListeners() {\n        this._register(this._hoverOperation.onResult((result) => {\n            if (!this._computer.anchor) {\n                // invalid state, ignore result\n                return;\n            }\n            const messages = (result.hasLoadingMessage ? this._addLoadingMessage(result.value) : result.value);\n            this._withResult(new HoverResult(this._computer.anchor, messages, result.isComplete));\n        }));\n        const contentHoverWidgetNode = this._contentHoverWidget.getDomNode();\n        this._register(dom.addStandardDisposableListener(contentHoverWidgetNode, 'keydown', (e) => {\n            if (e.equals(9 /* KeyCode.Escape */)) {\n                this.hide();\n            }\n        }));\n        this._register(dom.addStandardDisposableListener(contentHoverWidgetNode, 'mouseleave', (e) => {\n            this._onMouseLeave(e);\n        }));\n        this._register(TokenizationRegistry.onDidChange(() => {\n            if (this._contentHoverWidget.position && this._currentResult) {\n                this._setCurrentResult(this._currentResult); // render again\n            }\n        }));\n    }\n    /**\n     * Returns true if the hover shows now or will show.\n     */\n    _startShowingOrUpdateHover(anchor, mode, source, focus, mouseEvent) {\n        const contentHoverIsVisible = this._contentHoverWidget.position && this._currentResult;\n        if (!contentHoverIsVisible) {\n            if (anchor) {\n                this._startHoverOperationIfNecessary(anchor, mode, source, focus, false);\n                return true;\n            }\n            return false;\n        }\n        const isHoverSticky = this._editor.getOption(60 /* EditorOption.hover */).sticky;\n        const isMouseGettingCloser = mouseEvent && this._contentHoverWidget.isMouseGettingCloser(mouseEvent.event.posx, mouseEvent.event.posy);\n        const isHoverStickyAndIsMouseGettingCloser = isHoverSticky && isMouseGettingCloser;\n        // The mouse is getting closer to the hover, so we will keep the hover untouched\n        // But we will kick off a hover update at the new anchor, insisting on keeping the hover visible.\n        if (isHoverStickyAndIsMouseGettingCloser) {\n            if (anchor) {\n                this._startHoverOperationIfNecessary(anchor, mode, source, focus, true);\n            }\n            return true;\n        }\n        // If mouse is not getting closer and anchor not defined, hide the hover\n        if (!anchor) {\n            this._setCurrentResult(null);\n            return false;\n        }\n        // If mouse if not getting closer and anchor is defined, and the new anchor is the same as the previous anchor\n        const currentAnchorEqualsPreviousAnchor = this._currentResult.anchor.equals(anchor);\n        if (currentAnchorEqualsPreviousAnchor) {\n            return true;\n        }\n        // If mouse if not getting closer and anchor is defined, and the new anchor is not compatible with the previous anchor\n        const currentAnchorCompatibleWithPreviousAnchor = anchor.canAdoptVisibleHover(this._currentResult.anchor, this._contentHoverWidget.position);\n        if (!currentAnchorCompatibleWithPreviousAnchor) {\n            this._setCurrentResult(null);\n            this._startHoverOperationIfNecessary(anchor, mode, source, focus, false);\n            return true;\n        }\n        // We aren't getting any closer to the hover, so we will filter existing results\n        // and keep those which also apply to the new anchor.\n        this._setCurrentResult(this._currentResult.filter(anchor));\n        this._startHoverOperationIfNecessary(anchor, mode, source, focus, false);\n        return true;\n    }\n    _startHoverOperationIfNecessary(anchor, mode, source, focus, insistOnKeepingHoverVisible) {\n        const currentAnchorEqualToPreviousHover = this._computer.anchor && this._computer.anchor.equals(anchor);\n        if (currentAnchorEqualToPreviousHover) {\n            return;\n        }\n        this._hoverOperation.cancel();\n        this._computer.anchor = anchor;\n        this._computer.shouldFocus = focus;\n        this._computer.source = source;\n        this._computer.insistOnKeepingHoverVisible = insistOnKeepingHoverVisible;\n        this._hoverOperation.start(mode);\n    }\n    _setCurrentResult(hoverResult) {\n        let currentHoverResult = hoverResult;\n        const currentResultEqualToPreviousResult = this._currentResult === currentHoverResult;\n        if (currentResultEqualToPreviousResult) {\n            return;\n        }\n        const currentHoverResultIsEmpty = currentHoverResult && currentHoverResult.hoverParts.length === 0;\n        if (currentHoverResultIsEmpty) {\n            currentHoverResult = null;\n        }\n        this._currentResult = currentHoverResult;\n        if (this._currentResult) {\n            this._showHover(this._currentResult);\n        }\n        else {\n            this._hideHover();\n        }\n    }\n    _addLoadingMessage(result) {\n        if (!this._computer.anchor) {\n            return result;\n        }\n        for (const participant of this._participants) {\n            if (!participant.createLoadingMessage) {\n                continue;\n            }\n            const loadingMessage = participant.createLoadingMessage(this._computer.anchor);\n            if (!loadingMessage) {\n                continue;\n            }\n            return result.slice(0).concat([loadingMessage]);\n        }\n        return result;\n    }\n    _withResult(hoverResult) {\n        const previousHoverIsVisibleWithCompleteResult = this._contentHoverWidget.position && this._currentResult && this._currentResult.isComplete;\n        if (!previousHoverIsVisibleWithCompleteResult) {\n            this._setCurrentResult(hoverResult);\n        }\n        // The hover is visible with a previous complete result.\n        const isCurrentHoverResultComplete = hoverResult.isComplete;\n        if (!isCurrentHoverResultComplete) {\n            // Instead of rendering the new partial result, we wait for the result to be complete.\n            return;\n        }\n        const currentHoverResultIsEmpty = hoverResult.hoverParts.length === 0;\n        const insistOnKeepingPreviousHoverVisible = this._computer.insistOnKeepingHoverVisible;\n        const shouldKeepPreviousHoverVisible = currentHoverResultIsEmpty && insistOnKeepingPreviousHoverVisible;\n        if (shouldKeepPreviousHoverVisible) {\n            // The hover would now hide normally, so we'll keep the previous messages\n            return;\n        }\n        this._setCurrentResult(hoverResult);\n    }\n    _showHover(hoverResult) {\n        const context = this._getHoverContext();\n        this._renderedContentHover = new RenderedContentHover(this._editor, hoverResult, this._participants, this._computer, context, this._keybindingService);\n        if (this._renderedContentHover.domNodeHasChildren) {\n            this._contentHoverWidget.show(this._renderedContentHover);\n        }\n        else {\n            this._renderedContentHover.dispose();\n        }\n    }\n    _hideHover() {\n        this._contentHoverWidget.hide();\n    }\n    _getHoverContext() {\n        const hide = () => {\n            this.hide();\n        };\n        const onContentsChanged = () => {\n            this._onContentsChanged.fire();\n            this._contentHoverWidget.onContentsChanged();\n        };\n        const setMinimumDimensions = (dimensions) => {\n            this._contentHoverWidget.setMinimumDimensions(dimensions);\n        };\n        return { hide, onContentsChanged, setMinimumDimensions };\n    }\n    showsOrWillShow(mouseEvent) {\n        const isContentWidgetResizing = this._contentHoverWidget.isResizing;\n        if (isContentWidgetResizing) {\n            return true;\n        }\n        const anchorCandidates = this._findHoverAnchorCandidates(mouseEvent);\n        const anchorCandidatesExist = anchorCandidates.length > 0;\n        if (!anchorCandidatesExist) {\n            return this._startShowingOrUpdateHover(null, 0 /* HoverStartMode.Delayed */, 0 /* HoverStartSource.Mouse */, false, mouseEvent);\n        }\n        const anchor = anchorCandidates[0];\n        return this._startShowingOrUpdateHover(anchor, 0 /* HoverStartMode.Delayed */, 0 /* HoverStartSource.Mouse */, false, mouseEvent);\n    }\n    _findHoverAnchorCandidates(mouseEvent) {\n        const anchorCandidates = [];\n        for (const participant of this._participants) {\n            if (!participant.suggestHoverAnchor) {\n                continue;\n            }\n            const anchor = participant.suggestHoverAnchor(mouseEvent);\n            if (!anchor) {\n                continue;\n            }\n            anchorCandidates.push(anchor);\n        }\n        const target = mouseEvent.target;\n        switch (target.type) {\n            case 6 /* MouseTargetType.CONTENT_TEXT */: {\n                anchorCandidates.push(new HoverRangeAnchor(0, target.range, mouseEvent.event.posx, mouseEvent.event.posy));\n                break;\n            }\n            case 7 /* MouseTargetType.CONTENT_EMPTY */: {\n                const epsilon = this._editor.getOption(50 /* EditorOption.fontInfo */).typicalHalfwidthCharacterWidth / 2;\n                // Let hover kick in even when the mouse is technically in the empty area after a line, given the distance is small enough\n                const mouseIsWithinLinesAndCloseToHover = !target.detail.isAfterLines\n                    && typeof target.detail.horizontalDistanceToText === 'number'\n                    && target.detail.horizontalDistanceToText < epsilon;\n                if (!mouseIsWithinLinesAndCloseToHover) {\n                    break;\n                }\n                anchorCandidates.push(new HoverRangeAnchor(0, target.range, mouseEvent.event.posx, mouseEvent.event.posy));\n                break;\n            }\n        }\n        anchorCandidates.sort((a, b) => b.priority - a.priority);\n        return anchorCandidates;\n    }\n    _onMouseLeave(e) {\n        const editorDomNode = this._editor.getDomNode();\n        const isMousePositionOutsideOfEditor = !editorDomNode || !isMousePositionWithinElement(editorDomNode, e.x, e.y);\n        if (isMousePositionOutsideOfEditor) {\n            this.hide();\n        }\n    }\n    startShowingAtRange(range, mode, source, focus) {\n        this._startShowingOrUpdateHover(new HoverRangeAnchor(0, range, undefined, undefined), mode, source, focus, null);\n    }\n    async updateHoverVerbosityLevel(action, index, focus) {\n        this._renderedContentHover?.updateHoverVerbosityLevel(action, index, focus);\n    }\n    focusedHoverPartIndex() {\n        return this._renderedContentHover?.focusedHoverPartIndex ?? -1;\n    }\n    containsNode(node) {\n        return (node ? this._contentHoverWidget.getDomNode().contains(node) : false);\n    }\n    focus() {\n        this._contentHoverWidget.focus();\n    }\n    scrollUp() {\n        this._contentHoverWidget.scrollUp();\n    }\n    scrollDown() {\n        this._contentHoverWidget.scrollDown();\n    }\n    scrollLeft() {\n        this._contentHoverWidget.scrollLeft();\n    }\n    scrollRight() {\n        this._contentHoverWidget.scrollRight();\n    }\n    pageUp() {\n        this._contentHoverWidget.pageUp();\n    }\n    pageDown() {\n        this._contentHoverWidget.pageDown();\n    }\n    goToTop() {\n        this._contentHoverWidget.goToTop();\n    }\n    goToBottom() {\n        this._contentHoverWidget.goToBottom();\n    }\n    hide() {\n        this._computer.anchor = null;\n        this._hoverOperation.cancel();\n        this._setCurrentResult(null);\n    }\n    getDomNode() {\n        return this._contentHoverWidget.getDomNode();\n    }\n    get isColorPickerVisible() {\n        return this._renderedContentHover?.isColorPickerVisible() ?? false;\n    }\n    get isVisibleFromKeyboard() {\n        return this._contentHoverWidget.isVisibleFromKeyboard;\n    }\n    get isVisible() {\n        return this._contentHoverWidget.isVisible;\n    }\n    get isFocused() {\n        return this._contentHoverWidget.isFocused;\n    }\n    get isResizing() {\n        return this._contentHoverWidget.isResizing;\n    }\n    get widget() {\n        return this._contentHoverWidget;\n    }\n};\nContentHoverWidgetWrapper = __decorate([\n    __param(1, IInstantiationService),\n    __param(2, IKeybindingService)\n], ContentHoverWidgetWrapper);\nexport { ContentHoverWidgetWrapper };\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA,IAAIA,UAAU,GAAI,IAAI,IAAI,IAAI,CAACA,UAAU,IAAK,UAAUC,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAE;EACnF,IAAIC,CAAC,GAAGC,SAAS,CAACC,MAAM;IAAEC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGH,MAAM,GAAGE,IAAI,KAAK,IAAI,GAAGA,IAAI,GAAGK,MAAM,CAACC,wBAAwB,CAACR,MAAM,EAAEC,GAAG,CAAC,GAAGC,IAAI;IAAEO,CAAC;EAC5H,IAAI,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,CAACC,QAAQ,KAAK,UAAU,EAAEL,CAAC,GAAGI,OAAO,CAACC,QAAQ,CAACZ,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,CAAC,CAAC,KAC1H,KAAK,IAAIU,CAAC,GAAGb,UAAU,CAACM,MAAM,GAAG,CAAC,EAAEO,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE,IAAIH,CAAC,GAAGV,UAAU,CAACa,CAAC,CAAC,EAAEN,CAAC,GAAG,CAACH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACH,CAAC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACT,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,GAAGG,CAAC,CAACT,MAAM,EAAEC,GAAG,CAAC,KAAKK,CAAC;EACjJ,OAAOH,CAAC,GAAG,CAAC,IAAIG,CAAC,IAAIC,MAAM,CAACM,cAAc,CAACb,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,EAAEA,CAAC;AACjE,CAAC;AACD,IAAIQ,OAAO,GAAI,IAAI,IAAI,IAAI,CAACA,OAAO,IAAK,UAAUC,UAAU,EAAEC,SAAS,EAAE;EACrE,OAAO,UAAUhB,MAAM,EAAEC,GAAG,EAAE;IAAEe,SAAS,CAAChB,MAAM,EAAEC,GAAG,EAAEc,UAAU,CAAC;EAAE,CAAC;AACzE,CAAC;AACD,OAAO,KAAKE,GAAG,MAAM,iCAAiC;AACtD,SAASC,UAAU,QAAQ,sCAAsC;AACjE,SAASC,oBAAoB,QAAQ,8BAA8B;AACnE,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,wBAAwB,EAAEC,gBAAgB,QAAQ,iBAAiB;AAC5E,SAASC,qBAAqB,QAAQ,4DAA4D;AAClG,SAASC,kBAAkB,QAAQ,sDAAsD;AACzF,SAASC,kBAAkB,QAAQ,yBAAyB;AAC5D,SAASC,oBAAoB,QAAQ,2BAA2B;AAChE,SAASC,WAAW,QAAQ,wBAAwB;AACpD,SAASC,OAAO,QAAQ,kCAAkC;AAC1D,SAASC,oBAAoB,QAAQ,2BAA2B;AAChE,SAASC,4BAA4B,QAAQ,iBAAiB;AAC9D,IAAIC,yBAAyB,GAAG,MAAMA,yBAAyB,SAASb,UAAU,CAAC;EAC/Ec,WAAWA,CAACC,OAAO,EAAEC,qBAAqB,EAAEC,kBAAkB,EAAE;IAC5D,KAAK,CAAC,CAAC;IACP,IAAI,CAACF,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,qBAAqB,GAAGA,qBAAqB;IAClD,IAAI,CAACC,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACC,kBAAkB,GAAG,IAAI,CAACC,SAAS,CAAC,IAAIV,OAAO,CAAC,CAAC,CAAC;IACvD,IAAI,CAACW,iBAAiB,GAAG,IAAI,CAACF,kBAAkB,CAACG,KAAK;IACtD,IAAI,CAACC,mBAAmB,GAAG,IAAI,CAACH,SAAS,CAAC,IAAI,CAACJ,qBAAqB,CAACQ,cAAc,CAACjB,kBAAkB,EAAE,IAAI,CAACQ,OAAO,CAAC,CAAC;IACtH,IAAI,CAACU,aAAa,GAAG,IAAI,CAACC,4BAA4B,CAAC,CAAC;IACxD,IAAI,CAACC,SAAS,GAAG,IAAInB,oBAAoB,CAAC,IAAI,CAACO,OAAO,EAAE,IAAI,CAACU,aAAa,CAAC;IAC3E,IAAI,CAACG,eAAe,GAAG,IAAI,CAACR,SAAS,CAAC,IAAIlB,cAAc,CAAC,IAAI,CAACa,OAAO,EAAE,IAAI,CAACY,SAAS,CAAC,CAAC;IACvF,IAAI,CAACE,kBAAkB,CAAC,CAAC;EAC7B;EACAH,4BAA4BA,CAAA,EAAG;IAC3B,MAAMI,YAAY,GAAG,EAAE;IACvB,KAAK,MAAMC,WAAW,IAAI5B,wBAAwB,CAAC6B,MAAM,CAAC,CAAC,EAAE;MACzD,MAAMC,mBAAmB,GAAG,IAAI,CAACjB,qBAAqB,CAACQ,cAAc,CAACO,WAAW,EAAE,IAAI,CAAChB,OAAO,CAAC;MAChGe,YAAY,CAACI,IAAI,CAACD,mBAAmB,CAAC;IAC1C;IACAH,YAAY,CAACK,IAAI,CAAC,CAACC,EAAE,EAAEC,EAAE,KAAKD,EAAE,CAACE,YAAY,GAAGD,EAAE,CAACC,YAAY,CAAC;IAChE,IAAI,CAAClB,SAAS,CAAC,IAAI,CAACG,mBAAmB,CAACgB,WAAW,CAAC,MAAM;MACtD,IAAI,CAACd,aAAa,CAACe,OAAO,CAACT,WAAW,IAAIA,WAAW,CAACU,YAAY,GAAG,CAAC,CAAC;IAC3E,CAAC,CAAC,CAAC;IACH,OAAOX,YAAY;EACvB;EACAD,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACT,SAAS,CAAC,IAAI,CAACQ,eAAe,CAACc,QAAQ,CAAEC,MAAM,IAAK;MACrD,IAAI,CAAC,IAAI,CAAChB,SAAS,CAACiB,MAAM,EAAE;QACxB;QACA;MACJ;MACA,MAAMC,QAAQ,GAAIF,MAAM,CAACG,iBAAiB,GAAG,IAAI,CAACC,kBAAkB,CAACJ,MAAM,CAACK,KAAK,CAAC,GAAGL,MAAM,CAACK,KAAM;MAClG,IAAI,CAACC,WAAW,CAAC,IAAIxC,WAAW,CAAC,IAAI,CAACkB,SAAS,CAACiB,MAAM,EAAEC,QAAQ,EAAEF,MAAM,CAACO,UAAU,CAAC,CAAC;IACzF,CAAC,CAAC,CAAC;IACH,MAAMC,sBAAsB,GAAG,IAAI,CAAC5B,mBAAmB,CAAC6B,UAAU,CAAC,CAAC;IACpE,IAAI,CAAChC,SAAS,CAACrB,GAAG,CAACsD,6BAA6B,CAACF,sBAAsB,EAAE,SAAS,EAAGG,CAAC,IAAK;MACvF,IAAIA,CAAC,CAACC,MAAM,CAAC,CAAC,CAAC,oBAAoB,CAAC,EAAE;QAClC,IAAI,CAACC,IAAI,CAAC,CAAC;MACf;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,CAACpC,SAAS,CAACrB,GAAG,CAACsD,6BAA6B,CAACF,sBAAsB,EAAE,YAAY,EAAGG,CAAC,IAAK;MAC1F,IAAI,CAACG,aAAa,CAACH,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC;IACH,IAAI,CAAClC,SAAS,CAACnB,oBAAoB,CAACyD,WAAW,CAAC,MAAM;MAClD,IAAI,IAAI,CAACnC,mBAAmB,CAACoC,QAAQ,IAAI,IAAI,CAACzC,cAAc,EAAE;QAC1D,IAAI,CAAC0C,iBAAiB,CAAC,IAAI,CAAC1C,cAAc,CAAC,CAAC,CAAC;MACjD;IACJ,CAAC,CAAC,CAAC;EACP;EACA;AACJ;AACA;EACI2C,0BAA0BA,CAACjB,MAAM,EAAEkB,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,UAAU,EAAE;IAChE,MAAMC,qBAAqB,GAAG,IAAI,CAAC3C,mBAAmB,CAACoC,QAAQ,IAAI,IAAI,CAACzC,cAAc;IACtF,IAAI,CAACgD,qBAAqB,EAAE;MACxB,IAAItB,MAAM,EAAE;QACR,IAAI,CAACuB,+BAA+B,CAACvB,MAAM,EAAEkB,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAE,KAAK,CAAC;QACxE,OAAO,IAAI;MACf;MACA,OAAO,KAAK;IAChB;IACA,MAAMI,aAAa,GAAG,IAAI,CAACrD,OAAO,CAACsD,SAAS,CAAC,EAAE,CAAC,wBAAwB,CAAC,CAACC,MAAM;IAChF,MAAMC,oBAAoB,GAAGN,UAAU,IAAI,IAAI,CAAC1C,mBAAmB,CAACgD,oBAAoB,CAACN,UAAU,CAAC3C,KAAK,CAACkD,IAAI,EAAEP,UAAU,CAAC3C,KAAK,CAACmD,IAAI,CAAC;IACtI,MAAMC,oCAAoC,GAAGN,aAAa,IAAIG,oBAAoB;IAClF;IACA;IACA,IAAIG,oCAAoC,EAAE;MACtC,IAAI9B,MAAM,EAAE;QACR,IAAI,CAACuB,+BAA+B,CAACvB,MAAM,EAAEkB,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAE,IAAI,CAAC;MAC3E;MACA,OAAO,IAAI;IACf;IACA;IACA,IAAI,CAACpB,MAAM,EAAE;MACT,IAAI,CAACgB,iBAAiB,CAAC,IAAI,CAAC;MAC5B,OAAO,KAAK;IAChB;IACA;IACA,MAAMe,iCAAiC,GAAG,IAAI,CAACzD,cAAc,CAAC0B,MAAM,CAACW,MAAM,CAACX,MAAM,CAAC;IACnF,IAAI+B,iCAAiC,EAAE;MACnC,OAAO,IAAI;IACf;IACA;IACA,MAAMC,yCAAyC,GAAGhC,MAAM,CAACiC,oBAAoB,CAAC,IAAI,CAAC3D,cAAc,CAAC0B,MAAM,EAAE,IAAI,CAACrB,mBAAmB,CAACoC,QAAQ,CAAC;IAC5I,IAAI,CAACiB,yCAAyC,EAAE;MAC5C,IAAI,CAAChB,iBAAiB,CAAC,IAAI,CAAC;MAC5B,IAAI,CAACO,+BAA+B,CAACvB,MAAM,EAAEkB,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAE,KAAK,CAAC;MACxE,OAAO,IAAI;IACf;IACA;IACA;IACA,IAAI,CAACJ,iBAAiB,CAAC,IAAI,CAAC1C,cAAc,CAAC4D,MAAM,CAAClC,MAAM,CAAC,CAAC;IAC1D,IAAI,CAACuB,+BAA+B,CAACvB,MAAM,EAAEkB,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAE,KAAK,CAAC;IACxE,OAAO,IAAI;EACf;EACAG,+BAA+BA,CAACvB,MAAM,EAAEkB,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEe,2BAA2B,EAAE;IACtF,MAAMC,iCAAiC,GAAG,IAAI,CAACrD,SAAS,CAACiB,MAAM,IAAI,IAAI,CAACjB,SAAS,CAACiB,MAAM,CAACW,MAAM,CAACX,MAAM,CAAC;IACvG,IAAIoC,iCAAiC,EAAE;MACnC;IACJ;IACA,IAAI,CAACpD,eAAe,CAACqD,MAAM,CAAC,CAAC;IAC7B,IAAI,CAACtD,SAAS,CAACiB,MAAM,GAAGA,MAAM;IAC9B,IAAI,CAACjB,SAAS,CAACuD,WAAW,GAAGlB,KAAK;IAClC,IAAI,CAACrC,SAAS,CAACoC,MAAM,GAAGA,MAAM;IAC9B,IAAI,CAACpC,SAAS,CAACoD,2BAA2B,GAAGA,2BAA2B;IACxE,IAAI,CAACnD,eAAe,CAACuD,KAAK,CAACrB,IAAI,CAAC;EACpC;EACAF,iBAAiBA,CAACwB,WAAW,EAAE;IAC3B,IAAIC,kBAAkB,GAAGD,WAAW;IACpC,MAAME,kCAAkC,GAAG,IAAI,CAACpE,cAAc,KAAKmE,kBAAkB;IACrF,IAAIC,kCAAkC,EAAE;MACpC;IACJ;IACA,MAAMC,yBAAyB,GAAGF,kBAAkB,IAAIA,kBAAkB,CAACG,UAAU,CAACrG,MAAM,KAAK,CAAC;IAClG,IAAIoG,yBAAyB,EAAE;MAC3BF,kBAAkB,GAAG,IAAI;IAC7B;IACA,IAAI,CAACnE,cAAc,GAAGmE,kBAAkB;IACxC,IAAI,IAAI,CAACnE,cAAc,EAAE;MACrB,IAAI,CAACuE,UAAU,CAAC,IAAI,CAACvE,cAAc,CAAC;IACxC,CAAC,MACI;MACD,IAAI,CAACwE,UAAU,CAAC,CAAC;IACrB;EACJ;EACA3C,kBAAkBA,CAACJ,MAAM,EAAE;IACvB,IAAI,CAAC,IAAI,CAAChB,SAAS,CAACiB,MAAM,EAAE;MACxB,OAAOD,MAAM;IACjB;IACA,KAAK,MAAMZ,WAAW,IAAI,IAAI,CAACN,aAAa,EAAE;MAC1C,IAAI,CAACM,WAAW,CAAC4D,oBAAoB,EAAE;QACnC;MACJ;MACA,MAAMC,cAAc,GAAG7D,WAAW,CAAC4D,oBAAoB,CAAC,IAAI,CAAChE,SAAS,CAACiB,MAAM,CAAC;MAC9E,IAAI,CAACgD,cAAc,EAAE;QACjB;MACJ;MACA,OAAOjD,MAAM,CAACkD,KAAK,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC,CAACF,cAAc,CAAC,CAAC;IACnD;IACA,OAAOjD,MAAM;EACjB;EACAM,WAAWA,CAACmC,WAAW,EAAE;IACrB,MAAMW,wCAAwC,GAAG,IAAI,CAACxE,mBAAmB,CAACoC,QAAQ,IAAI,IAAI,CAACzC,cAAc,IAAI,IAAI,CAACA,cAAc,CAACgC,UAAU;IAC3I,IAAI,CAAC6C,wCAAwC,EAAE;MAC3C,IAAI,CAACnC,iBAAiB,CAACwB,WAAW,CAAC;IACvC;IACA;IACA,MAAMY,4BAA4B,GAAGZ,WAAW,CAAClC,UAAU;IAC3D,IAAI,CAAC8C,4BAA4B,EAAE;MAC/B;MACA;IACJ;IACA,MAAMT,yBAAyB,GAAGH,WAAW,CAACI,UAAU,CAACrG,MAAM,KAAK,CAAC;IACrE,MAAM8G,mCAAmC,GAAG,IAAI,CAACtE,SAAS,CAACoD,2BAA2B;IACtF,MAAMmB,8BAA8B,GAAGX,yBAAyB,IAAIU,mCAAmC;IACvG,IAAIC,8BAA8B,EAAE;MAChC;MACA;IACJ;IACA,IAAI,CAACtC,iBAAiB,CAACwB,WAAW,CAAC;EACvC;EACAK,UAAUA,CAACL,WAAW,EAAE;IACpB,MAAMe,OAAO,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;IACvC,IAAI,CAACC,qBAAqB,GAAG,IAAI1F,oBAAoB,CAAC,IAAI,CAACI,OAAO,EAAEqE,WAAW,EAAE,IAAI,CAAC3D,aAAa,EAAE,IAAI,CAACE,SAAS,EAAEwE,OAAO,EAAE,IAAI,CAAClF,kBAAkB,CAAC;IACtJ,IAAI,IAAI,CAACoF,qBAAqB,CAACC,kBAAkB,EAAE;MAC/C,IAAI,CAAC/E,mBAAmB,CAACgF,IAAI,CAAC,IAAI,CAACF,qBAAqB,CAAC;IAC7D,CAAC,MACI;MACD,IAAI,CAACA,qBAAqB,CAACG,OAAO,CAAC,CAAC;IACxC;EACJ;EACAd,UAAUA,CAAA,EAAG;IACT,IAAI,CAACnE,mBAAmB,CAACiC,IAAI,CAAC,CAAC;EACnC;EACA4C,gBAAgBA,CAAA,EAAG;IACf,MAAM5C,IAAI,GAAGA,CAAA,KAAM;MACf,IAAI,CAACA,IAAI,CAAC,CAAC;IACf,CAAC;IACD,MAAMnC,iBAAiB,GAAGA,CAAA,KAAM;MAC5B,IAAI,CAACF,kBAAkB,CAACsF,IAAI,CAAC,CAAC;MAC9B,IAAI,CAAClF,mBAAmB,CAACF,iBAAiB,CAAC,CAAC;IAChD,CAAC;IACD,MAAMqF,oBAAoB,GAAIC,UAAU,IAAK;MACzC,IAAI,CAACpF,mBAAmB,CAACmF,oBAAoB,CAACC,UAAU,CAAC;IAC7D,CAAC;IACD,OAAO;MAAEnD,IAAI;MAAEnC,iBAAiB;MAAEqF;IAAqB,CAAC;EAC5D;EACAE,eAAeA,CAAC3C,UAAU,EAAE;IACxB,MAAM4C,uBAAuB,GAAG,IAAI,CAACtF,mBAAmB,CAACuF,UAAU;IACnE,IAAID,uBAAuB,EAAE;MACzB,OAAO,IAAI;IACf;IACA,MAAME,gBAAgB,GAAG,IAAI,CAACC,0BAA0B,CAAC/C,UAAU,CAAC;IACpE,MAAMgD,qBAAqB,GAAGF,gBAAgB,CAAC5H,MAAM,GAAG,CAAC;IACzD,IAAI,CAAC8H,qBAAqB,EAAE;MACxB,OAAO,IAAI,CAACpD,0BAA0B,CAAC,IAAI,EAAE,CAAC,CAAC,8BAA8B,CAAC,CAAC,8BAA8B,KAAK,EAAEI,UAAU,CAAC;IACnI;IACA,MAAMrB,MAAM,GAAGmE,gBAAgB,CAAC,CAAC,CAAC;IAClC,OAAO,IAAI,CAAClD,0BAA0B,CAACjB,MAAM,EAAE,CAAC,CAAC,8BAA8B,CAAC,CAAC,8BAA8B,KAAK,EAAEqB,UAAU,CAAC;EACrI;EACA+C,0BAA0BA,CAAC/C,UAAU,EAAE;IACnC,MAAM8C,gBAAgB,GAAG,EAAE;IAC3B,KAAK,MAAMhF,WAAW,IAAI,IAAI,CAACN,aAAa,EAAE;MAC1C,IAAI,CAACM,WAAW,CAACmF,kBAAkB,EAAE;QACjC;MACJ;MACA,MAAMtE,MAAM,GAAGb,WAAW,CAACmF,kBAAkB,CAACjD,UAAU,CAAC;MACzD,IAAI,CAACrB,MAAM,EAAE;QACT;MACJ;MACAmE,gBAAgB,CAAC7E,IAAI,CAACU,MAAM,CAAC;IACjC;IACA,MAAM9D,MAAM,GAAGmF,UAAU,CAACnF,MAAM;IAChC,QAAQA,MAAM,CAACqI,IAAI;MACf,KAAK,CAAC,CAAC;QAAoC;UACvCJ,gBAAgB,CAAC7E,IAAI,CAAC,IAAI9B,gBAAgB,CAAC,CAAC,EAAEtB,MAAM,CAACsI,KAAK,EAAEnD,UAAU,CAAC3C,KAAK,CAACkD,IAAI,EAAEP,UAAU,CAAC3C,KAAK,CAACmD,IAAI,CAAC,CAAC;UAC1G;QACJ;MACA,KAAK,CAAC,CAAC;QAAqC;UACxC,MAAM4C,OAAO,GAAG,IAAI,CAACtG,OAAO,CAACsD,SAAS,CAAC,EAAE,CAAC,2BAA2B,CAAC,CAACiD,8BAA8B,GAAG,CAAC;UACzG;UACA,MAAMC,iCAAiC,GAAG,CAACzI,MAAM,CAAC0I,MAAM,CAACC,YAAY,IAC9D,OAAO3I,MAAM,CAAC0I,MAAM,CAACE,wBAAwB,KAAK,QAAQ,IAC1D5I,MAAM,CAAC0I,MAAM,CAACE,wBAAwB,GAAGL,OAAO;UACvD,IAAI,CAACE,iCAAiC,EAAE;YACpC;UACJ;UACAR,gBAAgB,CAAC7E,IAAI,CAAC,IAAI9B,gBAAgB,CAAC,CAAC,EAAEtB,MAAM,CAACsI,KAAK,EAAEnD,UAAU,CAAC3C,KAAK,CAACkD,IAAI,EAAEP,UAAU,CAAC3C,KAAK,CAACmD,IAAI,CAAC,CAAC;UAC1G;QACJ;IACJ;IACAsC,gBAAgB,CAAC5E,IAAI,CAAC,CAACwF,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACC,QAAQ,GAAGF,CAAC,CAACE,QAAQ,CAAC;IACxD,OAAOd,gBAAgB;EAC3B;EACAtD,aAAaA,CAACH,CAAC,EAAE;IACb,MAAMwE,aAAa,GAAG,IAAI,CAAC/G,OAAO,CAACqC,UAAU,CAAC,CAAC;IAC/C,MAAM2E,8BAA8B,GAAG,CAACD,aAAa,IAAI,CAAClH,4BAA4B,CAACkH,aAAa,EAAExE,CAAC,CAAC0E,CAAC,EAAE1E,CAAC,CAAC2E,CAAC,CAAC;IAC/G,IAAIF,8BAA8B,EAAE;MAChC,IAAI,CAACvE,IAAI,CAAC,CAAC;IACf;EACJ;EACA0E,mBAAmBA,CAACd,KAAK,EAAEtD,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAE;IAC5C,IAAI,CAACH,0BAA0B,CAAC,IAAIzD,gBAAgB,CAAC,CAAC,EAAEgH,KAAK,EAAEe,SAAS,EAAEA,SAAS,CAAC,EAAErE,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAE,IAAI,CAAC;EACpH;EACMoE,yBAAyBA,CAACC,MAAM,EAAEC,KAAK,EAAEtE,KAAK,EAAE;IAAA,IAAAuE,KAAA;IAAA,OAAAC,iBAAA;MAClDD,KAAI,CAAClC,qBAAqB,EAAE+B,yBAAyB,CAACC,MAAM,EAAEC,KAAK,EAAEtE,KAAK,CAAC;IAAC;EAChF;EACAyE,qBAAqBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACpC,qBAAqB,EAAEoC,qBAAqB,IAAI,CAAC,CAAC;EAClE;EACAC,YAAYA,CAACC,IAAI,EAAE;IACf,OAAQA,IAAI,GAAG,IAAI,CAACpH,mBAAmB,CAAC6B,UAAU,CAAC,CAAC,CAACwF,QAAQ,CAACD,IAAI,CAAC,GAAG,KAAK;EAC/E;EACA3E,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACzC,mBAAmB,CAACyC,KAAK,CAAC,CAAC;EACpC;EACA6E,QAAQA,CAAA,EAAG;IACP,IAAI,CAACtH,mBAAmB,CAACsH,QAAQ,CAAC,CAAC;EACvC;EACAC,UAAUA,CAAA,EAAG;IACT,IAAI,CAACvH,mBAAmB,CAACuH,UAAU,CAAC,CAAC;EACzC;EACAC,UAAUA,CAAA,EAAG;IACT,IAAI,CAACxH,mBAAmB,CAACwH,UAAU,CAAC,CAAC;EACzC;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACzH,mBAAmB,CAACyH,WAAW,CAAC,CAAC;EAC1C;EACAC,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC1H,mBAAmB,CAAC0H,MAAM,CAAC,CAAC;EACrC;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC3H,mBAAmB,CAAC2H,QAAQ,CAAC,CAAC;EACvC;EACAC,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC5H,mBAAmB,CAAC4H,OAAO,CAAC,CAAC;EACtC;EACAC,UAAUA,CAAA,EAAG;IACT,IAAI,CAAC7H,mBAAmB,CAAC6H,UAAU,CAAC,CAAC;EACzC;EACA5F,IAAIA,CAAA,EAAG;IACH,IAAI,CAAC7B,SAAS,CAACiB,MAAM,GAAG,IAAI;IAC5B,IAAI,CAAChB,eAAe,CAACqD,MAAM,CAAC,CAAC;IAC7B,IAAI,CAACrB,iBAAiB,CAAC,IAAI,CAAC;EAChC;EACAR,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAAC7B,mBAAmB,CAAC6B,UAAU,CAAC,CAAC;EAChD;EACA,IAAIiG,oBAAoBA,CAAA,EAAG;IACvB,OAAO,IAAI,CAAChD,qBAAqB,EAAEgD,oBAAoB,CAAC,CAAC,IAAI,KAAK;EACtE;EACA,IAAIC,qBAAqBA,CAAA,EAAG;IACxB,OAAO,IAAI,CAAC/H,mBAAmB,CAAC+H,qBAAqB;EACzD;EACA,IAAIC,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAChI,mBAAmB,CAACgI,SAAS;EAC7C;EACA,IAAIC,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACjI,mBAAmB,CAACiI,SAAS;EAC7C;EACA,IAAI1C,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACvF,mBAAmB,CAACuF,UAAU;EAC9C;EACA,IAAI2C,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAAClI,mBAAmB;EACnC;AACJ,CAAC;AACDV,yBAAyB,GAAGjC,UAAU,CAAC,CACnCgB,OAAO,CAAC,CAAC,EAAES,qBAAqB,CAAC,EACjCT,OAAO,CAAC,CAAC,EAAEU,kBAAkB,CAAC,CACjC,EAAEO,yBAAyB,CAAC;AAC7B,SAASA,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}