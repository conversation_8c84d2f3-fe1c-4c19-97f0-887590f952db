{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport class TreeViewsDnDService {\n  constructor() {\n    this._dragOperations = new Map();\n  }\n  removeDragOperationTransfer(uuid) {\n    if (uuid && this._dragOperations.has(uuid)) {\n      const operation = this._dragOperations.get(uuid);\n      this._dragOperations.delete(uuid);\n      return operation;\n    }\n    return undefined;\n  }\n}\nexport class DraggedTreeItemsIdentifier {\n  constructor(identifier) {\n    this.identifier = identifier;\n  }\n}", "map": {"version": 3, "names": ["TreeViewsDnDService", "constructor", "_dragOperations", "Map", "removeDragOperationTransfer", "uuid", "has", "operation", "get", "delete", "undefined", "DraggedTreeItemsIdentifier", "identifier"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/services/treeViewsDnd.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport class TreeViewsDnDService {\n    constructor() {\n        this._dragOperations = new Map();\n    }\n    removeDragOperationTransfer(uuid) {\n        if ((uuid && this._dragOperations.has(uuid))) {\n            const operation = this._dragOperations.get(uuid);\n            this._dragOperations.delete(uuid);\n            return operation;\n        }\n        return undefined;\n    }\n}\nexport class DraggedTreeItemsIdentifier {\n    constructor(identifier) {\n        this.identifier = identifier;\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,MAAMA,mBAAmB,CAAC;EAC7BC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,eAAe,GAAG,IAAIC,GAAG,CAAC,CAAC;EACpC;EACAC,2BAA2BA,CAACC,IAAI,EAAE;IAC9B,IAAKA,IAAI,IAAI,IAAI,CAACH,eAAe,CAACI,GAAG,CAACD,IAAI,CAAC,EAAG;MAC1C,MAAME,SAAS,GAAG,IAAI,CAACL,eAAe,CAACM,GAAG,CAACH,IAAI,CAAC;MAChD,IAAI,CAACH,eAAe,CAACO,MAAM,CAACJ,IAAI,CAAC;MACjC,OAAOE,SAAS;IACpB;IACA,OAAOG,SAAS;EACpB;AACJ;AACA,OAAO,MAAMC,0BAA0B,CAAC;EACpCV,WAAWA,CAACW,UAAU,EAAE;IACpB,IAAI,CAACA,UAAU,GAAGA,UAAU;EAChC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}