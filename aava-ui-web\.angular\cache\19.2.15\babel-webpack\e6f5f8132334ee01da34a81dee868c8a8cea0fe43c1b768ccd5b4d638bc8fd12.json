{"ast": null, "code": "import _asyncToGenerator from \"C:/console/aava-ui-web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nvar __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = this && this.__param || function (paramIndex, decorator) {\n  return function (target, key) {\n    decorator(target, key, paramIndex);\n  };\n};\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as dom from '../../../base/browser/dom.js';\nimport { KeybindingLabel } from '../../../base/browser/ui/keybindingLabel/keybindingLabel.js';\nimport { List } from '../../../base/browser/ui/list/listWidget.js';\nimport { CancellationTokenSource } from '../../../base/common/cancellation.js';\nimport { Codicon } from '../../../base/common/codicons.js';\nimport { Disposable } from '../../../base/common/lifecycle.js';\nimport { OS } from '../../../base/common/platform.js';\nimport { ThemeIcon } from '../../../base/common/themables.js';\nimport './actionWidget.css';\nimport { localize } from '../../../nls.js';\nimport { IContextViewService } from '../../contextview/browser/contextView.js';\nimport { IKeybindingService } from '../../keybinding/common/keybinding.js';\nimport { defaultListStyles } from '../../theme/browser/defaultStyles.js';\nimport { asCssVariable } from '../../theme/common/colorRegistry.js';\nexport const acceptSelectedActionCommand = 'acceptSelectedCodeAction';\nexport const previewSelectedActionCommand = 'previewSelectedCodeAction';\nclass HeaderRenderer {\n  get templateId() {\n    return \"header\" /* ActionListItemKind.Header */;\n  }\n  renderTemplate(container) {\n    container.classList.add('group-header');\n    const text = document.createElement('span');\n    container.append(text);\n    return {\n      container,\n      text\n    };\n  }\n  renderElement(element, _index, templateData) {\n    templateData.text.textContent = element.group?.title ?? '';\n  }\n  disposeTemplate(_templateData) {\n    // noop\n  }\n}\nlet ActionItemRenderer = class ActionItemRenderer {\n  get templateId() {\n    return \"action\" /* ActionListItemKind.Action */;\n  }\n  constructor(_supportsPreview, _keybindingService) {\n    this._supportsPreview = _supportsPreview;\n    this._keybindingService = _keybindingService;\n  }\n  renderTemplate(container) {\n    container.classList.add(this.templateId);\n    const icon = document.createElement('div');\n    icon.className = 'icon';\n    container.append(icon);\n    const text = document.createElement('span');\n    text.className = 'title';\n    container.append(text);\n    const keybinding = new KeybindingLabel(container, OS);\n    return {\n      container,\n      icon,\n      text,\n      keybinding\n    };\n  }\n  renderElement(element, _index, data) {\n    if (element.group?.icon) {\n      data.icon.className = ThemeIcon.asClassName(element.group.icon);\n      if (element.group.icon.color) {\n        data.icon.style.color = asCssVariable(element.group.icon.color.id);\n      }\n    } else {\n      data.icon.className = ThemeIcon.asClassName(Codicon.lightBulb);\n      data.icon.style.color = 'var(--vscode-editorLightBulb-foreground)';\n    }\n    if (!element.item || !element.label) {\n      return;\n    }\n    data.text.textContent = stripNewlines(element.label);\n    data.keybinding.set(element.keybinding);\n    dom.setVisibility(!!element.keybinding, data.keybinding.element);\n    const actionTitle = this._keybindingService.lookupKeybinding(acceptSelectedActionCommand)?.getLabel();\n    const previewTitle = this._keybindingService.lookupKeybinding(previewSelectedActionCommand)?.getLabel();\n    data.container.classList.toggle('option-disabled', element.disabled);\n    if (element.disabled) {\n      data.container.title = element.label;\n    } else if (actionTitle && previewTitle) {\n      if (this._supportsPreview && element.canPreview) {\n        data.container.title = localize({\n          key: 'label-preview',\n          comment: ['placeholders are keybindings, e.g \"F2 to Apply, Shift+F2 to Preview\"']\n        }, \"{0} to Apply, {1} to Preview\", actionTitle, previewTitle);\n      } else {\n        data.container.title = localize({\n          key: 'label',\n          comment: ['placeholder is a keybinding, e.g \"F2 to Apply\"']\n        }, \"{0} to Apply\", actionTitle);\n      }\n    } else {\n      data.container.title = '';\n    }\n  }\n  disposeTemplate(_templateData) {\n    _templateData.keybinding.dispose();\n  }\n};\nActionItemRenderer = __decorate([__param(1, IKeybindingService)], ActionItemRenderer);\nclass AcceptSelectedEvent extends UIEvent {\n  constructor() {\n    super('acceptSelectedAction');\n  }\n}\nclass PreviewSelectedEvent extends UIEvent {\n  constructor() {\n    super('previewSelectedAction');\n  }\n}\nfunction getKeyboardNavigationLabel(item) {\n  // Filter out header vs. action\n  if (item.kind === 'action') {\n    return item.label;\n  }\n  return undefined;\n}\nlet ActionList = class ActionList extends Disposable {\n  constructor(user, preview, items, _delegate, _contextViewService, _keybindingService) {\n    super();\n    this._delegate = _delegate;\n    this._contextViewService = _contextViewService;\n    this._keybindingService = _keybindingService;\n    this._actionLineHeight = 24;\n    this._headerLineHeight = 26;\n    this.cts = this._register(new CancellationTokenSource());\n    this.domNode = document.createElement('div');\n    this.domNode.classList.add('actionList');\n    const virtualDelegate = {\n      getHeight: element => element.kind === \"header\" /* ActionListItemKind.Header */ ? this._headerLineHeight : this._actionLineHeight,\n      getTemplateId: element => element.kind\n    };\n    this._list = this._register(new List(user, this.domNode, virtualDelegate, [new ActionItemRenderer(preview, this._keybindingService), new HeaderRenderer()], {\n      keyboardSupport: false,\n      typeNavigationEnabled: true,\n      keyboardNavigationLabelProvider: {\n        getKeyboardNavigationLabel\n      },\n      accessibilityProvider: {\n        getAriaLabel: element => {\n          if (element.kind === \"action\" /* ActionListItemKind.Action */) {\n            let label = element.label ? stripNewlines(element?.label) : '';\n            if (element.disabled) {\n              label = localize({\n                key: 'customQuickFixWidget.labels',\n                comment: [`Action widget labels for accessibility.`]\n              }, \"{0}, Disabled Reason: {1}\", label, element.disabled);\n            }\n            return label;\n          }\n          return null;\n        },\n        getWidgetAriaLabel: () => localize({\n          key: 'customQuickFixWidget',\n          comment: [`An action widget option`]\n        }, \"Action Widget\"),\n        getRole: e => e.kind === \"action\" /* ActionListItemKind.Action */ ? 'option' : 'separator',\n        getWidgetRole: () => 'listbox'\n      }\n    }));\n    this._list.style(defaultListStyles);\n    this._register(this._list.onMouseClick(e => this.onListClick(e)));\n    this._register(this._list.onMouseOver(e => this.onListHover(e)));\n    this._register(this._list.onDidChangeFocus(() => this.onFocus()));\n    this._register(this._list.onDidChangeSelection(e => this.onListSelection(e)));\n    this._allMenuItems = items;\n    this._list.splice(0, this._list.length, this._allMenuItems);\n    if (this._list.length) {\n      this.focusNext();\n    }\n  }\n  focusCondition(element) {\n    return !element.disabled && element.kind === \"action\" /* ActionListItemKind.Action */;\n  }\n  hide(didCancel) {\n    this._delegate.onHide(didCancel);\n    this.cts.cancel();\n    this._contextViewService.hideContextView();\n  }\n  layout(minWidth) {\n    // Updating list height, depending on how many separators and headers there are.\n    const numHeaders = this._allMenuItems.filter(item => item.kind === 'header').length;\n    const itemsHeight = this._allMenuItems.length * this._actionLineHeight;\n    const heightWithHeaders = itemsHeight + numHeaders * this._headerLineHeight - numHeaders * this._actionLineHeight;\n    this._list.layout(heightWithHeaders);\n    let maxWidth = minWidth;\n    if (this._allMenuItems.length >= 50) {\n      maxWidth = 380;\n    } else {\n      // For finding width dynamically (not using resize observer)\n      const itemWidths = this._allMenuItems.map((_, index) => {\n        const element = this.domNode.ownerDocument.getElementById(this._list.getElementID(index));\n        if (element) {\n          element.style.width = 'auto';\n          const width = element.getBoundingClientRect().width;\n          element.style.width = '';\n          return width;\n        }\n        return 0;\n      });\n      // resize observer - can be used in the future since list widget supports dynamic height but not width\n      maxWidth = Math.max(...itemWidths, minWidth);\n    }\n    const maxVhPrecentage = 0.7;\n    const height = Math.min(heightWithHeaders, this.domNode.ownerDocument.body.clientHeight * maxVhPrecentage);\n    this._list.layout(height, maxWidth);\n    this.domNode.style.height = `${height}px`;\n    this._list.domFocus();\n    return maxWidth;\n  }\n  focusPrevious() {\n    this._list.focusPrevious(1, true, undefined, this.focusCondition);\n  }\n  focusNext() {\n    this._list.focusNext(1, true, undefined, this.focusCondition);\n  }\n  acceptSelected(preview) {\n    const focused = this._list.getFocus();\n    if (focused.length === 0) {\n      return;\n    }\n    const focusIndex = focused[0];\n    const element = this._list.element(focusIndex);\n    if (!this.focusCondition(element)) {\n      return;\n    }\n    const event = preview ? new PreviewSelectedEvent() : new AcceptSelectedEvent();\n    this._list.setSelection([focusIndex], event);\n  }\n  onListSelection(e) {\n    if (!e.elements.length) {\n      return;\n    }\n    const element = e.elements[0];\n    if (element.item && this.focusCondition(element)) {\n      this._delegate.onSelect(element.item, e.browserEvent instanceof PreviewSelectedEvent);\n    } else {\n      this._list.setSelection([]);\n    }\n  }\n  onFocus() {\n    const focused = this._list.getFocus();\n    if (focused.length === 0) {\n      return;\n    }\n    const focusIndex = focused[0];\n    const element = this._list.element(focusIndex);\n    this._delegate.onFocus?.(element.item);\n  }\n  onListHover(e) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const element = e.element;\n      if (element && element.item && _this.focusCondition(element)) {\n        if (_this._delegate.onHover && !element.disabled && element.kind === \"action\" /* ActionListItemKind.Action */) {\n          const result = yield _this._delegate.onHover(element.item, _this.cts.token);\n          element.canPreview = result ? result.canPreview : undefined;\n        }\n        if (e.index) {\n          _this._list.splice(e.index, 1, [element]);\n        }\n      }\n      _this._list.setFocus(typeof e.index === 'number' ? [e.index] : []);\n    })();\n  }\n  onListClick(e) {\n    if (e.element && this.focusCondition(e.element)) {\n      this._list.setFocus([]);\n    }\n  }\n};\nActionList = __decorate([__param(4, IContextViewService), __param(5, IKeybindingService)], ActionList);\nexport { ActionList };\nfunction stripNewlines(str) {\n  return str.replace(/\\r\\n|\\r|\\n/g, ' ');\n}", "map": {"version": 3, "names": ["__decorate", "decorators", "target", "key", "desc", "c", "arguments", "length", "r", "Object", "getOwnPropertyDescriptor", "d", "Reflect", "decorate", "i", "defineProperty", "__param", "paramIndex", "decorator", "dom", "KeybindingLabel", "List", "CancellationTokenSource", "Codicon", "Disposable", "OS", "ThemeIcon", "localize", "IContextViewService", "IKeybindingService", "defaultListStyles", "asCssVariable", "acceptSelectedActionCommand", "previewSelectedActionCommand", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "templateId", "renderTemplate", "container", "classList", "add", "text", "document", "createElement", "append", "renderElement", "element", "_index", "templateData", "textContent", "group", "title", "disposeTemplate", "_templateData", "ActionItemRenderer", "constructor", "_supportsPreview", "_keybindingService", "icon", "className", "keybinding", "data", "asClassName", "color", "style", "id", "lightBulb", "item", "label", "stripNewlines", "set", "setVisibility", "actionTitle", "lookupKeybinding", "get<PERSON><PERSON><PERSON>", "previewTitle", "toggle", "disabled", "canPreview", "comment", "dispose", "AcceptSelectedEvent", "UIEvent", "PreviewSelectedEvent", "getKeyboardNavigationLabel", "kind", "undefined", "ActionList", "user", "preview", "items", "_delegate", "_contextViewService", "_actionLineHeight", "_headerLineHeight", "cts", "_register", "domNode", "virtualDelegate", "getHeight", "getTemplateId", "_list", "keyboardSupport", "typeNavigationEnabled", "keyboardNavigationLabe<PERSON>", "accessibilityProvider", "getAriaLabel", "getWidgetAriaLabel", "getRole", "e", "getWidgetRole", "onMouseClick", "onListClick", "onMouseOver", "onListHover", "onDidChangeFocus", "onFocus", "onDidChangeSelection", "onListSelection", "_allMenuItems", "splice", "focusNext", "focusCondition", "hide", "didCancel", "onHide", "cancel", "hideContextView", "layout", "min<PERSON><PERSON><PERSON>", "numHeaders", "filter", "itemsHeight", "heightWithHeaders", "max<PERSON><PERSON><PERSON>", "itemWidths", "map", "_", "index", "ownerDocument", "getElementById", "getElementID", "width", "getBoundingClientRect", "Math", "max", "maxVhPrecentage", "height", "min", "body", "clientHeight", "domFocus", "focusPrevious", "acceptSelected", "focused", "getFocus", "focusIndex", "event", "setSelection", "elements", "onSelect", "browserEvent", "_this", "_asyncToGenerator", "onHover", "result", "token", "setFocus", "str", "replace"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/platform/actionWidget/browser/actionList.js"], "sourcesContent": ["var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = (this && this.__param) || function (paramIndex, decorator) {\n    return function (target, key) { decorator(target, key, paramIndex); }\n};\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as dom from '../../../base/browser/dom.js';\nimport { KeybindingLabel } from '../../../base/browser/ui/keybindingLabel/keybindingLabel.js';\nimport { List } from '../../../base/browser/ui/list/listWidget.js';\nimport { CancellationTokenSource } from '../../../base/common/cancellation.js';\nimport { Codicon } from '../../../base/common/codicons.js';\nimport { Disposable } from '../../../base/common/lifecycle.js';\nimport { OS } from '../../../base/common/platform.js';\nimport { ThemeIcon } from '../../../base/common/themables.js';\nimport './actionWidget.css';\nimport { localize } from '../../../nls.js';\nimport { IContextViewService } from '../../contextview/browser/contextView.js';\nimport { IKeybindingService } from '../../keybinding/common/keybinding.js';\nimport { defaultListStyles } from '../../theme/browser/defaultStyles.js';\nimport { asCssVariable } from '../../theme/common/colorRegistry.js';\nexport const acceptSelectedActionCommand = 'acceptSelectedCodeAction';\nexport const previewSelectedActionCommand = 'previewSelectedCodeAction';\nclass HeaderRenderer {\n    get templateId() { return \"header\" /* ActionListItemKind.Header */; }\n    renderTemplate(container) {\n        container.classList.add('group-header');\n        const text = document.createElement('span');\n        container.append(text);\n        return { container, text };\n    }\n    renderElement(element, _index, templateData) {\n        templateData.text.textContent = element.group?.title ?? '';\n    }\n    disposeTemplate(_templateData) {\n        // noop\n    }\n}\nlet ActionItemRenderer = class ActionItemRenderer {\n    get templateId() { return \"action\" /* ActionListItemKind.Action */; }\n    constructor(_supportsPreview, _keybindingService) {\n        this._supportsPreview = _supportsPreview;\n        this._keybindingService = _keybindingService;\n    }\n    renderTemplate(container) {\n        container.classList.add(this.templateId);\n        const icon = document.createElement('div');\n        icon.className = 'icon';\n        container.append(icon);\n        const text = document.createElement('span');\n        text.className = 'title';\n        container.append(text);\n        const keybinding = new KeybindingLabel(container, OS);\n        return { container, icon, text, keybinding };\n    }\n    renderElement(element, _index, data) {\n        if (element.group?.icon) {\n            data.icon.className = ThemeIcon.asClassName(element.group.icon);\n            if (element.group.icon.color) {\n                data.icon.style.color = asCssVariable(element.group.icon.color.id);\n            }\n        }\n        else {\n            data.icon.className = ThemeIcon.asClassName(Codicon.lightBulb);\n            data.icon.style.color = 'var(--vscode-editorLightBulb-foreground)';\n        }\n        if (!element.item || !element.label) {\n            return;\n        }\n        data.text.textContent = stripNewlines(element.label);\n        data.keybinding.set(element.keybinding);\n        dom.setVisibility(!!element.keybinding, data.keybinding.element);\n        const actionTitle = this._keybindingService.lookupKeybinding(acceptSelectedActionCommand)?.getLabel();\n        const previewTitle = this._keybindingService.lookupKeybinding(previewSelectedActionCommand)?.getLabel();\n        data.container.classList.toggle('option-disabled', element.disabled);\n        if (element.disabled) {\n            data.container.title = element.label;\n        }\n        else if (actionTitle && previewTitle) {\n            if (this._supportsPreview && element.canPreview) {\n                data.container.title = localize({ key: 'label-preview', comment: ['placeholders are keybindings, e.g \"F2 to Apply, Shift+F2 to Preview\"'] }, \"{0} to Apply, {1} to Preview\", actionTitle, previewTitle);\n            }\n            else {\n                data.container.title = localize({ key: 'label', comment: ['placeholder is a keybinding, e.g \"F2 to Apply\"'] }, \"{0} to Apply\", actionTitle);\n            }\n        }\n        else {\n            data.container.title = '';\n        }\n    }\n    disposeTemplate(_templateData) {\n        _templateData.keybinding.dispose();\n    }\n};\nActionItemRenderer = __decorate([\n    __param(1, IKeybindingService)\n], ActionItemRenderer);\nclass AcceptSelectedEvent extends UIEvent {\n    constructor() { super('acceptSelectedAction'); }\n}\nclass PreviewSelectedEvent extends UIEvent {\n    constructor() { super('previewSelectedAction'); }\n}\nfunction getKeyboardNavigationLabel(item) {\n    // Filter out header vs. action\n    if (item.kind === 'action') {\n        return item.label;\n    }\n    return undefined;\n}\nlet ActionList = class ActionList extends Disposable {\n    constructor(user, preview, items, _delegate, _contextViewService, _keybindingService) {\n        super();\n        this._delegate = _delegate;\n        this._contextViewService = _contextViewService;\n        this._keybindingService = _keybindingService;\n        this._actionLineHeight = 24;\n        this._headerLineHeight = 26;\n        this.cts = this._register(new CancellationTokenSource());\n        this.domNode = document.createElement('div');\n        this.domNode.classList.add('actionList');\n        const virtualDelegate = {\n            getHeight: element => element.kind === \"header\" /* ActionListItemKind.Header */ ? this._headerLineHeight : this._actionLineHeight,\n            getTemplateId: element => element.kind\n        };\n        this._list = this._register(new List(user, this.domNode, virtualDelegate, [\n            new ActionItemRenderer(preview, this._keybindingService),\n            new HeaderRenderer(),\n        ], {\n            keyboardSupport: false,\n            typeNavigationEnabled: true,\n            keyboardNavigationLabelProvider: { getKeyboardNavigationLabel },\n            accessibilityProvider: {\n                getAriaLabel: element => {\n                    if (element.kind === \"action\" /* ActionListItemKind.Action */) {\n                        let label = element.label ? stripNewlines(element?.label) : '';\n                        if (element.disabled) {\n                            label = localize({ key: 'customQuickFixWidget.labels', comment: [`Action widget labels for accessibility.`] }, \"{0}, Disabled Reason: {1}\", label, element.disabled);\n                        }\n                        return label;\n                    }\n                    return null;\n                },\n                getWidgetAriaLabel: () => localize({ key: 'customQuickFixWidget', comment: [`An action widget option`] }, \"Action Widget\"),\n                getRole: (e) => e.kind === \"action\" /* ActionListItemKind.Action */ ? 'option' : 'separator',\n                getWidgetRole: () => 'listbox',\n            },\n        }));\n        this._list.style(defaultListStyles);\n        this._register(this._list.onMouseClick(e => this.onListClick(e)));\n        this._register(this._list.onMouseOver(e => this.onListHover(e)));\n        this._register(this._list.onDidChangeFocus(() => this.onFocus()));\n        this._register(this._list.onDidChangeSelection(e => this.onListSelection(e)));\n        this._allMenuItems = items;\n        this._list.splice(0, this._list.length, this._allMenuItems);\n        if (this._list.length) {\n            this.focusNext();\n        }\n    }\n    focusCondition(element) {\n        return !element.disabled && element.kind === \"action\" /* ActionListItemKind.Action */;\n    }\n    hide(didCancel) {\n        this._delegate.onHide(didCancel);\n        this.cts.cancel();\n        this._contextViewService.hideContextView();\n    }\n    layout(minWidth) {\n        // Updating list height, depending on how many separators and headers there are.\n        const numHeaders = this._allMenuItems.filter(item => item.kind === 'header').length;\n        const itemsHeight = this._allMenuItems.length * this._actionLineHeight;\n        const heightWithHeaders = itemsHeight + numHeaders * this._headerLineHeight - numHeaders * this._actionLineHeight;\n        this._list.layout(heightWithHeaders);\n        let maxWidth = minWidth;\n        if (this._allMenuItems.length >= 50) {\n            maxWidth = 380;\n        }\n        else {\n            // For finding width dynamically (not using resize observer)\n            const itemWidths = this._allMenuItems.map((_, index) => {\n                const element = this.domNode.ownerDocument.getElementById(this._list.getElementID(index));\n                if (element) {\n                    element.style.width = 'auto';\n                    const width = element.getBoundingClientRect().width;\n                    element.style.width = '';\n                    return width;\n                }\n                return 0;\n            });\n            // resize observer - can be used in the future since list widget supports dynamic height but not width\n            maxWidth = Math.max(...itemWidths, minWidth);\n        }\n        const maxVhPrecentage = 0.7;\n        const height = Math.min(heightWithHeaders, this.domNode.ownerDocument.body.clientHeight * maxVhPrecentage);\n        this._list.layout(height, maxWidth);\n        this.domNode.style.height = `${height}px`;\n        this._list.domFocus();\n        return maxWidth;\n    }\n    focusPrevious() {\n        this._list.focusPrevious(1, true, undefined, this.focusCondition);\n    }\n    focusNext() {\n        this._list.focusNext(1, true, undefined, this.focusCondition);\n    }\n    acceptSelected(preview) {\n        const focused = this._list.getFocus();\n        if (focused.length === 0) {\n            return;\n        }\n        const focusIndex = focused[0];\n        const element = this._list.element(focusIndex);\n        if (!this.focusCondition(element)) {\n            return;\n        }\n        const event = preview ? new PreviewSelectedEvent() : new AcceptSelectedEvent();\n        this._list.setSelection([focusIndex], event);\n    }\n    onListSelection(e) {\n        if (!e.elements.length) {\n            return;\n        }\n        const element = e.elements[0];\n        if (element.item && this.focusCondition(element)) {\n            this._delegate.onSelect(element.item, e.browserEvent instanceof PreviewSelectedEvent);\n        }\n        else {\n            this._list.setSelection([]);\n        }\n    }\n    onFocus() {\n        const focused = this._list.getFocus();\n        if (focused.length === 0) {\n            return;\n        }\n        const focusIndex = focused[0];\n        const element = this._list.element(focusIndex);\n        this._delegate.onFocus?.(element.item);\n    }\n    async onListHover(e) {\n        const element = e.element;\n        if (element && element.item && this.focusCondition(element)) {\n            if (this._delegate.onHover && !element.disabled && element.kind === \"action\" /* ActionListItemKind.Action */) {\n                const result = await this._delegate.onHover(element.item, this.cts.token);\n                element.canPreview = result ? result.canPreview : undefined;\n            }\n            if (e.index) {\n                this._list.splice(e.index, 1, [element]);\n            }\n        }\n        this._list.setFocus(typeof e.index === 'number' ? [e.index] : []);\n    }\n    onListClick(e) {\n        if (e.element && this.focusCondition(e.element)) {\n            this._list.setFocus([]);\n        }\n    }\n};\nActionList = __decorate([\n    __param(4, IContextViewService),\n    __param(5, IKeybindingService)\n], ActionList);\nexport { ActionList };\nfunction stripNewlines(str) {\n    return str.replace(/\\r\\n|\\r|\\n/g, ' ');\n}\n"], "mappings": ";AAAA,IAAIA,UAAU,GAAI,IAAI,IAAI,IAAI,CAACA,UAAU,IAAK,UAAUC,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAE;EACnF,IAAIC,CAAC,GAAGC,SAAS,CAACC,MAAM;IAAEC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGH,MAAM,GAAGE,IAAI,KAAK,IAAI,GAAGA,IAAI,GAAGK,MAAM,CAACC,wBAAwB,CAACR,MAAM,EAAEC,GAAG,CAAC,GAAGC,IAAI;IAAEO,CAAC;EAC5H,IAAI,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,CAACC,QAAQ,KAAK,UAAU,EAAEL,CAAC,GAAGI,OAAO,CAACC,QAAQ,CAACZ,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,CAAC,CAAC,KAC1H,KAAK,IAAIU,CAAC,GAAGb,UAAU,CAACM,MAAM,GAAG,CAAC,EAAEO,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE,IAAIH,CAAC,GAAGV,UAAU,CAACa,CAAC,CAAC,EAAEN,CAAC,GAAG,CAACH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACH,CAAC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACT,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,GAAGG,CAAC,CAACT,MAAM,EAAEC,GAAG,CAAC,KAAKK,CAAC;EACjJ,OAAOH,CAAC,GAAG,CAAC,IAAIG,CAAC,IAAIC,MAAM,CAACM,cAAc,CAACb,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,EAAEA,CAAC;AACjE,CAAC;AACD,IAAIQ,OAAO,GAAI,IAAI,IAAI,IAAI,CAACA,OAAO,IAAK,UAAUC,UAAU,EAAEC,SAAS,EAAE;EACrE,OAAO,UAAUhB,MAAM,EAAEC,GAAG,EAAE;IAAEe,SAAS,CAAChB,MAAM,EAAEC,GAAG,EAAEc,UAAU,CAAC;EAAE,CAAC;AACzE,CAAC;AACD;AACA;AACA;AACA;AACA,OAAO,KAAKE,GAAG,MAAM,8BAA8B;AACnD,SAASC,eAAe,QAAQ,6DAA6D;AAC7F,SAASC,IAAI,QAAQ,6CAA6C;AAClE,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,OAAO,QAAQ,kCAAkC;AAC1D,SAASC,UAAU,QAAQ,mCAAmC;AAC9D,SAASC,EAAE,QAAQ,kCAAkC;AACrD,SAASC,SAAS,QAAQ,mCAAmC;AAC7D,OAAO,oBAAoB;AAC3B,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,mBAAmB,QAAQ,0CAA0C;AAC9E,SAASC,kBAAkB,QAAQ,uCAAuC;AAC1E,SAASC,iBAAiB,QAAQ,sCAAsC;AACxE,SAASC,aAAa,QAAQ,qCAAqC;AACnE,OAAO,MAAMC,2BAA2B,GAAG,0BAA0B;AACrE,OAAO,MAAMC,4BAA4B,GAAG,2BAA2B;AACvE,MAAMC,cAAc,CAAC;EACjB,IAAIC,UAAUA,CAAA,EAAG;IAAE,OAAO,QAAQ,CAAC;EAAiC;EACpEC,cAAcA,CAACC,SAAS,EAAE;IACtBA,SAAS,CAACC,SAAS,CAACC,GAAG,CAAC,cAAc,CAAC;IACvC,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;IAC3CL,SAAS,CAACM,MAAM,CAACH,IAAI,CAAC;IACtB,OAAO;MAAEH,SAAS;MAAEG;IAAK,CAAC;EAC9B;EACAI,aAAaA,CAACC,OAAO,EAAEC,MAAM,EAAEC,YAAY,EAAE;IACzCA,YAAY,CAACP,IAAI,CAACQ,WAAW,GAAGH,OAAO,CAACI,KAAK,EAAEC,KAAK,IAAI,EAAE;EAC9D;EACAC,eAAeA,CAACC,aAAa,EAAE;IAC3B;EAAA;AAER;AACA,IAAIC,kBAAkB,GAAG,MAAMA,kBAAkB,CAAC;EAC9C,IAAIlB,UAAUA,CAAA,EAAG;IAAE,OAAO,QAAQ,CAAC;EAAiC;EACpEmB,WAAWA,CAACC,gBAAgB,EAAEC,kBAAkB,EAAE;IAC9C,IAAI,CAACD,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACC,kBAAkB,GAAGA,kBAAkB;EAChD;EACApB,cAAcA,CAACC,SAAS,EAAE;IACtBA,SAAS,CAACC,SAAS,CAACC,GAAG,CAAC,IAAI,CAACJ,UAAU,CAAC;IACxC,MAAMsB,IAAI,GAAGhB,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC1Ce,IAAI,CAACC,SAAS,GAAG,MAAM;IACvBrB,SAAS,CAACM,MAAM,CAACc,IAAI,CAAC;IACtB,MAAMjB,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;IAC3CF,IAAI,CAACkB,SAAS,GAAG,OAAO;IACxBrB,SAAS,CAACM,MAAM,CAACH,IAAI,CAAC;IACtB,MAAMmB,UAAU,GAAG,IAAIvC,eAAe,CAACiB,SAAS,EAAEZ,EAAE,CAAC;IACrD,OAAO;MAAEY,SAAS;MAAEoB,IAAI;MAAEjB,IAAI;MAAEmB;IAAW,CAAC;EAChD;EACAf,aAAaA,CAACC,OAAO,EAAEC,MAAM,EAAEc,IAAI,EAAE;IACjC,IAAIf,OAAO,CAACI,KAAK,EAAEQ,IAAI,EAAE;MACrBG,IAAI,CAACH,IAAI,CAACC,SAAS,GAAGhC,SAAS,CAACmC,WAAW,CAAChB,OAAO,CAACI,KAAK,CAACQ,IAAI,CAAC;MAC/D,IAAIZ,OAAO,CAACI,KAAK,CAACQ,IAAI,CAACK,KAAK,EAAE;QAC1BF,IAAI,CAACH,IAAI,CAACM,KAAK,CAACD,KAAK,GAAG/B,aAAa,CAACc,OAAO,CAACI,KAAK,CAACQ,IAAI,CAACK,KAAK,CAACE,EAAE,CAAC;MACtE;IACJ,CAAC,MACI;MACDJ,IAAI,CAACH,IAAI,CAACC,SAAS,GAAGhC,SAAS,CAACmC,WAAW,CAACtC,OAAO,CAAC0C,SAAS,CAAC;MAC9DL,IAAI,CAACH,IAAI,CAACM,KAAK,CAACD,KAAK,GAAG,0CAA0C;IACtE;IACA,IAAI,CAACjB,OAAO,CAACqB,IAAI,IAAI,CAACrB,OAAO,CAACsB,KAAK,EAAE;MACjC;IACJ;IACAP,IAAI,CAACpB,IAAI,CAACQ,WAAW,GAAGoB,aAAa,CAACvB,OAAO,CAACsB,KAAK,CAAC;IACpDP,IAAI,CAACD,UAAU,CAACU,GAAG,CAACxB,OAAO,CAACc,UAAU,CAAC;IACvCxC,GAAG,CAACmD,aAAa,CAAC,CAAC,CAACzB,OAAO,CAACc,UAAU,EAAEC,IAAI,CAACD,UAAU,CAACd,OAAO,CAAC;IAChE,MAAM0B,WAAW,GAAG,IAAI,CAACf,kBAAkB,CAACgB,gBAAgB,CAACxC,2BAA2B,CAAC,EAAEyC,QAAQ,CAAC,CAAC;IACrG,MAAMC,YAAY,GAAG,IAAI,CAAClB,kBAAkB,CAACgB,gBAAgB,CAACvC,4BAA4B,CAAC,EAAEwC,QAAQ,CAAC,CAAC;IACvGb,IAAI,CAACvB,SAAS,CAACC,SAAS,CAACqC,MAAM,CAAC,iBAAiB,EAAE9B,OAAO,CAAC+B,QAAQ,CAAC;IACpE,IAAI/B,OAAO,CAAC+B,QAAQ,EAAE;MAClBhB,IAAI,CAACvB,SAAS,CAACa,KAAK,GAAGL,OAAO,CAACsB,KAAK;IACxC,CAAC,MACI,IAAII,WAAW,IAAIG,YAAY,EAAE;MAClC,IAAI,IAAI,CAACnB,gBAAgB,IAAIV,OAAO,CAACgC,UAAU,EAAE;QAC7CjB,IAAI,CAACvB,SAAS,CAACa,KAAK,GAAGvB,QAAQ,CAAC;UAAExB,GAAG,EAAE,eAAe;UAAE2E,OAAO,EAAE,CAAC,sEAAsE;QAAE,CAAC,EAAE,8BAA8B,EAAEP,WAAW,EAAEG,YAAY,CAAC;MAC3M,CAAC,MACI;QACDd,IAAI,CAACvB,SAAS,CAACa,KAAK,GAAGvB,QAAQ,CAAC;UAAExB,GAAG,EAAE,OAAO;UAAE2E,OAAO,EAAE,CAAC,gDAAgD;QAAE,CAAC,EAAE,cAAc,EAAEP,WAAW,CAAC;MAC/I;IACJ,CAAC,MACI;MACDX,IAAI,CAACvB,SAAS,CAACa,KAAK,GAAG,EAAE;IAC7B;EACJ;EACAC,eAAeA,CAACC,aAAa,EAAE;IAC3BA,aAAa,CAACO,UAAU,CAACoB,OAAO,CAAC,CAAC;EACtC;AACJ,CAAC;AACD1B,kBAAkB,GAAGrD,UAAU,CAAC,CAC5BgB,OAAO,CAAC,CAAC,EAAEa,kBAAkB,CAAC,CACjC,EAAEwB,kBAAkB,CAAC;AACtB,MAAM2B,mBAAmB,SAASC,OAAO,CAAC;EACtC3B,WAAWA,CAAA,EAAG;IAAE,KAAK,CAAC,sBAAsB,CAAC;EAAE;AACnD;AACA,MAAM4B,oBAAoB,SAASD,OAAO,CAAC;EACvC3B,WAAWA,CAAA,EAAG;IAAE,KAAK,CAAC,uBAAuB,CAAC;EAAE;AACpD;AACA,SAAS6B,0BAA0BA,CAACjB,IAAI,EAAE;EACtC;EACA,IAAIA,IAAI,CAACkB,IAAI,KAAK,QAAQ,EAAE;IACxB,OAAOlB,IAAI,CAACC,KAAK;EACrB;EACA,OAAOkB,SAAS;AACpB;AACA,IAAIC,UAAU,GAAG,MAAMA,UAAU,SAAS9D,UAAU,CAAC;EACjD8B,WAAWA,CAACiC,IAAI,EAAEC,OAAO,EAAEC,KAAK,EAAEC,SAAS,EAAEC,mBAAmB,EAAEnC,kBAAkB,EAAE;IAClF,KAAK,CAAC,CAAC;IACP,IAAI,CAACkC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACnC,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACoC,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACC,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACC,GAAG,GAAG,IAAI,CAACC,SAAS,CAAC,IAAIzE,uBAAuB,CAAC,CAAC,CAAC;IACxD,IAAI,CAAC0E,OAAO,GAAGvD,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC5C,IAAI,CAACsD,OAAO,CAAC1D,SAAS,CAACC,GAAG,CAAC,YAAY,CAAC;IACxC,MAAM0D,eAAe,GAAG;MACpBC,SAAS,EAAErD,OAAO,IAAIA,OAAO,CAACuC,IAAI,KAAK,QAAQ,CAAC,kCAAkC,IAAI,CAACS,iBAAiB,GAAG,IAAI,CAACD,iBAAiB;MACjIO,aAAa,EAAEtD,OAAO,IAAIA,OAAO,CAACuC;IACtC,CAAC;IACD,IAAI,CAACgB,KAAK,GAAG,IAAI,CAACL,SAAS,CAAC,IAAI1E,IAAI,CAACkE,IAAI,EAAE,IAAI,CAACS,OAAO,EAAEC,eAAe,EAAE,CACtE,IAAI5C,kBAAkB,CAACmC,OAAO,EAAE,IAAI,CAAChC,kBAAkB,CAAC,EACxD,IAAItB,cAAc,CAAC,CAAC,CACvB,EAAE;MACCmE,eAAe,EAAE,KAAK;MACtBC,qBAAqB,EAAE,IAAI;MAC3BC,+BAA+B,EAAE;QAAEpB;MAA2B,CAAC;MAC/DqB,qBAAqB,EAAE;QACnBC,YAAY,EAAE5D,OAAO,IAAI;UACrB,IAAIA,OAAO,CAACuC,IAAI,KAAK,QAAQ,CAAC,iCAAiC;YAC3D,IAAIjB,KAAK,GAAGtB,OAAO,CAACsB,KAAK,GAAGC,aAAa,CAACvB,OAAO,EAAEsB,KAAK,CAAC,GAAG,EAAE;YAC9D,IAAItB,OAAO,CAAC+B,QAAQ,EAAE;cAClBT,KAAK,GAAGxC,QAAQ,CAAC;gBAAExB,GAAG,EAAE,6BAA6B;gBAAE2E,OAAO,EAAE,CAAC,yCAAyC;cAAE,CAAC,EAAE,2BAA2B,EAAEX,KAAK,EAAEtB,OAAO,CAAC+B,QAAQ,CAAC;YACxK;YACA,OAAOT,KAAK;UAChB;UACA,OAAO,IAAI;QACf,CAAC;QACDuC,kBAAkB,EAAEA,CAAA,KAAM/E,QAAQ,CAAC;UAAExB,GAAG,EAAE,sBAAsB;UAAE2E,OAAO,EAAE,CAAC,yBAAyB;QAAE,CAAC,EAAE,eAAe,CAAC;QAC1H6B,OAAO,EAAGC,CAAC,IAAKA,CAAC,CAACxB,IAAI,KAAK,QAAQ,CAAC,kCAAkC,QAAQ,GAAG,WAAW;QAC5FyB,aAAa,EAAEA,CAAA,KAAM;MACzB;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,CAACT,KAAK,CAACrC,KAAK,CAACjC,iBAAiB,CAAC;IACnC,IAAI,CAACiE,SAAS,CAAC,IAAI,CAACK,KAAK,CAACU,YAAY,CAACF,CAAC,IAAI,IAAI,CAACG,WAAW,CAACH,CAAC,CAAC,CAAC,CAAC;IACjE,IAAI,CAACb,SAAS,CAAC,IAAI,CAACK,KAAK,CAACY,WAAW,CAACJ,CAAC,IAAI,IAAI,CAACK,WAAW,CAACL,CAAC,CAAC,CAAC,CAAC;IAChE,IAAI,CAACb,SAAS,CAAC,IAAI,CAACK,KAAK,CAACc,gBAAgB,CAAC,MAAM,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC;IACjE,IAAI,CAACpB,SAAS,CAAC,IAAI,CAACK,KAAK,CAACgB,oBAAoB,CAACR,CAAC,IAAI,IAAI,CAACS,eAAe,CAACT,CAAC,CAAC,CAAC,CAAC;IAC7E,IAAI,CAACU,aAAa,GAAG7B,KAAK;IAC1B,IAAI,CAACW,KAAK,CAACmB,MAAM,CAAC,CAAC,EAAE,IAAI,CAACnB,KAAK,CAAC7F,MAAM,EAAE,IAAI,CAAC+G,aAAa,CAAC;IAC3D,IAAI,IAAI,CAAClB,KAAK,CAAC7F,MAAM,EAAE;MACnB,IAAI,CAACiH,SAAS,CAAC,CAAC;IACpB;EACJ;EACAC,cAAcA,CAAC5E,OAAO,EAAE;IACpB,OAAO,CAACA,OAAO,CAAC+B,QAAQ,IAAI/B,OAAO,CAACuC,IAAI,KAAK,QAAQ,CAAC;EAC1D;EACAsC,IAAIA,CAACC,SAAS,EAAE;IACZ,IAAI,CAACjC,SAAS,CAACkC,MAAM,CAACD,SAAS,CAAC;IAChC,IAAI,CAAC7B,GAAG,CAAC+B,MAAM,CAAC,CAAC;IACjB,IAAI,CAAClC,mBAAmB,CAACmC,eAAe,CAAC,CAAC;EAC9C;EACAC,MAAMA,CAACC,QAAQ,EAAE;IACb;IACA,MAAMC,UAAU,GAAG,IAAI,CAACX,aAAa,CAACY,MAAM,CAAChE,IAAI,IAAIA,IAAI,CAACkB,IAAI,KAAK,QAAQ,CAAC,CAAC7E,MAAM;IACnF,MAAM4H,WAAW,GAAG,IAAI,CAACb,aAAa,CAAC/G,MAAM,GAAG,IAAI,CAACqF,iBAAiB;IACtE,MAAMwC,iBAAiB,GAAGD,WAAW,GAAGF,UAAU,GAAG,IAAI,CAACpC,iBAAiB,GAAGoC,UAAU,GAAG,IAAI,CAACrC,iBAAiB;IACjH,IAAI,CAACQ,KAAK,CAAC2B,MAAM,CAACK,iBAAiB,CAAC;IACpC,IAAIC,QAAQ,GAAGL,QAAQ;IACvB,IAAI,IAAI,CAACV,aAAa,CAAC/G,MAAM,IAAI,EAAE,EAAE;MACjC8H,QAAQ,GAAG,GAAG;IAClB,CAAC,MACI;MACD;MACA,MAAMC,UAAU,GAAG,IAAI,CAAChB,aAAa,CAACiB,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,KAAK;QACpD,MAAM5F,OAAO,GAAG,IAAI,CAACmD,OAAO,CAAC0C,aAAa,CAACC,cAAc,CAAC,IAAI,CAACvC,KAAK,CAACwC,YAAY,CAACH,KAAK,CAAC,CAAC;QACzF,IAAI5F,OAAO,EAAE;UACTA,OAAO,CAACkB,KAAK,CAAC8E,KAAK,GAAG,MAAM;UAC5B,MAAMA,KAAK,GAAGhG,OAAO,CAACiG,qBAAqB,CAAC,CAAC,CAACD,KAAK;UACnDhG,OAAO,CAACkB,KAAK,CAAC8E,KAAK,GAAG,EAAE;UACxB,OAAOA,KAAK;QAChB;QACA,OAAO,CAAC;MACZ,CAAC,CAAC;MACF;MACAR,QAAQ,GAAGU,IAAI,CAACC,GAAG,CAAC,GAAGV,UAAU,EAAEN,QAAQ,CAAC;IAChD;IACA,MAAMiB,eAAe,GAAG,GAAG;IAC3B,MAAMC,MAAM,GAAGH,IAAI,CAACI,GAAG,CAACf,iBAAiB,EAAE,IAAI,CAACpC,OAAO,CAAC0C,aAAa,CAACU,IAAI,CAACC,YAAY,GAAGJ,eAAe,CAAC;IAC1G,IAAI,CAAC7C,KAAK,CAAC2B,MAAM,CAACmB,MAAM,EAAEb,QAAQ,CAAC;IACnC,IAAI,CAACrC,OAAO,CAACjC,KAAK,CAACmF,MAAM,GAAG,GAAGA,MAAM,IAAI;IACzC,IAAI,CAAC9C,KAAK,CAACkD,QAAQ,CAAC,CAAC;IACrB,OAAOjB,QAAQ;EACnB;EACAkB,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACnD,KAAK,CAACmD,aAAa,CAAC,CAAC,EAAE,IAAI,EAAElE,SAAS,EAAE,IAAI,CAACoC,cAAc,CAAC;EACrE;EACAD,SAASA,CAAA,EAAG;IACR,IAAI,CAACpB,KAAK,CAACoB,SAAS,CAAC,CAAC,EAAE,IAAI,EAAEnC,SAAS,EAAE,IAAI,CAACoC,cAAc,CAAC;EACjE;EACA+B,cAAcA,CAAChE,OAAO,EAAE;IACpB,MAAMiE,OAAO,GAAG,IAAI,CAACrD,KAAK,CAACsD,QAAQ,CAAC,CAAC;IACrC,IAAID,OAAO,CAAClJ,MAAM,KAAK,CAAC,EAAE;MACtB;IACJ;IACA,MAAMoJ,UAAU,GAAGF,OAAO,CAAC,CAAC,CAAC;IAC7B,MAAM5G,OAAO,GAAG,IAAI,CAACuD,KAAK,CAACvD,OAAO,CAAC8G,UAAU,CAAC;IAC9C,IAAI,CAAC,IAAI,CAAClC,cAAc,CAAC5E,OAAO,CAAC,EAAE;MAC/B;IACJ;IACA,MAAM+G,KAAK,GAAGpE,OAAO,GAAG,IAAIN,oBAAoB,CAAC,CAAC,GAAG,IAAIF,mBAAmB,CAAC,CAAC;IAC9E,IAAI,CAACoB,KAAK,CAACyD,YAAY,CAAC,CAACF,UAAU,CAAC,EAAEC,KAAK,CAAC;EAChD;EACAvC,eAAeA,CAACT,CAAC,EAAE;IACf,IAAI,CAACA,CAAC,CAACkD,QAAQ,CAACvJ,MAAM,EAAE;MACpB;IACJ;IACA,MAAMsC,OAAO,GAAG+D,CAAC,CAACkD,QAAQ,CAAC,CAAC,CAAC;IAC7B,IAAIjH,OAAO,CAACqB,IAAI,IAAI,IAAI,CAACuD,cAAc,CAAC5E,OAAO,CAAC,EAAE;MAC9C,IAAI,CAAC6C,SAAS,CAACqE,QAAQ,CAAClH,OAAO,CAACqB,IAAI,EAAE0C,CAAC,CAACoD,YAAY,YAAY9E,oBAAoB,CAAC;IACzF,CAAC,MACI;MACD,IAAI,CAACkB,KAAK,CAACyD,YAAY,CAAC,EAAE,CAAC;IAC/B;EACJ;EACA1C,OAAOA,CAAA,EAAG;IACN,MAAMsC,OAAO,GAAG,IAAI,CAACrD,KAAK,CAACsD,QAAQ,CAAC,CAAC;IACrC,IAAID,OAAO,CAAClJ,MAAM,KAAK,CAAC,EAAE;MACtB;IACJ;IACA,MAAMoJ,UAAU,GAAGF,OAAO,CAAC,CAAC,CAAC;IAC7B,MAAM5G,OAAO,GAAG,IAAI,CAACuD,KAAK,CAACvD,OAAO,CAAC8G,UAAU,CAAC;IAC9C,IAAI,CAACjE,SAAS,CAACyB,OAAO,GAAGtE,OAAO,CAACqB,IAAI,CAAC;EAC1C;EACM+C,WAAWA,CAACL,CAAC,EAAE;IAAA,IAAAqD,KAAA;IAAA,OAAAC,iBAAA;MACjB,MAAMrH,OAAO,GAAG+D,CAAC,CAAC/D,OAAO;MACzB,IAAIA,OAAO,IAAIA,OAAO,CAACqB,IAAI,IAAI+F,KAAI,CAACxC,cAAc,CAAC5E,OAAO,CAAC,EAAE;QACzD,IAAIoH,KAAI,CAACvE,SAAS,CAACyE,OAAO,IAAI,CAACtH,OAAO,CAAC+B,QAAQ,IAAI/B,OAAO,CAACuC,IAAI,KAAK,QAAQ,CAAC,iCAAiC;UAC1G,MAAMgF,MAAM,SAASH,KAAI,CAACvE,SAAS,CAACyE,OAAO,CAACtH,OAAO,CAACqB,IAAI,EAAE+F,KAAI,CAACnE,GAAG,CAACuE,KAAK,CAAC;UACzExH,OAAO,CAACgC,UAAU,GAAGuF,MAAM,GAAGA,MAAM,CAACvF,UAAU,GAAGQ,SAAS;QAC/D;QACA,IAAIuB,CAAC,CAAC6B,KAAK,EAAE;UACTwB,KAAI,CAAC7D,KAAK,CAACmB,MAAM,CAACX,CAAC,CAAC6B,KAAK,EAAE,CAAC,EAAE,CAAC5F,OAAO,CAAC,CAAC;QAC5C;MACJ;MACAoH,KAAI,CAAC7D,KAAK,CAACkE,QAAQ,CAAC,OAAO1D,CAAC,CAAC6B,KAAK,KAAK,QAAQ,GAAG,CAAC7B,CAAC,CAAC6B,KAAK,CAAC,GAAG,EAAE,CAAC;IAAC;EACtE;EACA1B,WAAWA,CAACH,CAAC,EAAE;IACX,IAAIA,CAAC,CAAC/D,OAAO,IAAI,IAAI,CAAC4E,cAAc,CAACb,CAAC,CAAC/D,OAAO,CAAC,EAAE;MAC7C,IAAI,CAACuD,KAAK,CAACkE,QAAQ,CAAC,EAAE,CAAC;IAC3B;EACJ;AACJ,CAAC;AACDhF,UAAU,GAAGtF,UAAU,CAAC,CACpBgB,OAAO,CAAC,CAAC,EAAEY,mBAAmB,CAAC,EAC/BZ,OAAO,CAAC,CAAC,EAAEa,kBAAkB,CAAC,CACjC,EAAEyD,UAAU,CAAC;AACd,SAASA,UAAU;AACnB,SAASlB,aAAaA,CAACmG,GAAG,EAAE;EACxB,OAAOA,GAAG,CAACC,OAAO,CAAC,aAAa,EAAE,GAAG,CAAC;AAC1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}