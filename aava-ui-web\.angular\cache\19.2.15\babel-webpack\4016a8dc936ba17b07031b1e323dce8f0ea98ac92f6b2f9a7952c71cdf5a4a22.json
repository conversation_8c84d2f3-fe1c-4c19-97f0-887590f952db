{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport class DomReadingContext {\n  get didDomLayout() {\n    return this._didDomLayout;\n  }\n  readClientRect() {\n    if (!this._clientRectRead) {\n      this._clientRectRead = true;\n      const rect = this._domNode.getBoundingClientRect();\n      this.markDidDomLayout();\n      this._clientRectDeltaLeft = rect.left;\n      this._clientRectScale = rect.width / this._domNode.offsetWidth;\n    }\n  }\n  get clientRectDeltaLeft() {\n    if (!this._clientRectRead) {\n      this.readClientRect();\n    }\n    return this._clientRectDeltaLeft;\n  }\n  get clientRectScale() {\n    if (!this._clientRectRead) {\n      this.readClientRect();\n    }\n    return this._clientRectScale;\n  }\n  constructor(_domNode, endNode) {\n    this._domNode = _domNode;\n    this.endNode = endNode;\n    this._didDomLayout = false;\n    this._clientRectDeltaLeft = 0;\n    this._clientRectScale = 1;\n    this._clientRectRead = false;\n  }\n  markDidDomLayout() {\n    this._didDomLayout = true;\n  }\n}", "map": {"version": 3, "names": ["DomReadingContext", "didDomLayout", "_didDomLayout", "readClientRect", "_clientRectRead", "rect", "_domNode", "getBoundingClientRect", "markDidDomLayout", "_clientRectDeltaLeft", "left", "_clientRectScale", "width", "offsetWidth", "clientRectDeltaLeft", "clientRectScale", "constructor", "endNode"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/browser/viewParts/lines/domReadingContext.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport class DomReadingContext {\n    get didDomLayout() {\n        return this._didDomLayout;\n    }\n    readClientRect() {\n        if (!this._clientRectRead) {\n            this._clientRectRead = true;\n            const rect = this._domNode.getBoundingClientRect();\n            this.markDidDomLayout();\n            this._clientRectDeltaLeft = rect.left;\n            this._clientRectScale = rect.width / this._domNode.offsetWidth;\n        }\n    }\n    get clientRectDeltaLeft() {\n        if (!this._clientRectRead) {\n            this.readClientRect();\n        }\n        return this._clientRectDeltaLeft;\n    }\n    get clientRectScale() {\n        if (!this._clientRectRead) {\n            this.readClientRect();\n        }\n        return this._clientRectScale;\n    }\n    constructor(_domNode, endNode) {\n        this._domNode = _domNode;\n        this.endNode = endNode;\n        this._didDomLayout = false;\n        this._clientRectDeltaLeft = 0;\n        this._clientRectScale = 1;\n        this._clientRectRead = false;\n    }\n    markDidDomLayout() {\n        this._didDomLayout = true;\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,MAAMA,iBAAiB,CAAC;EAC3B,IAAIC,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACC,aAAa;EAC7B;EACAC,cAAcA,CAAA,EAAG;IACb,IAAI,CAAC,IAAI,CAACC,eAAe,EAAE;MACvB,IAAI,CAACA,eAAe,GAAG,IAAI;MAC3B,MAAMC,IAAI,GAAG,IAAI,CAACC,QAAQ,CAACC,qBAAqB,CAAC,CAAC;MAClD,IAAI,CAACC,gBAAgB,CAAC,CAAC;MACvB,IAAI,CAACC,oBAAoB,GAAGJ,IAAI,CAACK,IAAI;MACrC,IAAI,CAACC,gBAAgB,GAAGN,IAAI,CAACO,KAAK,GAAG,IAAI,CAACN,QAAQ,CAACO,WAAW;IAClE;EACJ;EACA,IAAIC,mBAAmBA,CAAA,EAAG;IACtB,IAAI,CAAC,IAAI,CAACV,eAAe,EAAE;MACvB,IAAI,CAACD,cAAc,CAAC,CAAC;IACzB;IACA,OAAO,IAAI,CAACM,oBAAoB;EACpC;EACA,IAAIM,eAAeA,CAAA,EAAG;IAClB,IAAI,CAAC,IAAI,CAACX,eAAe,EAAE;MACvB,IAAI,CAACD,cAAc,CAAC,CAAC;IACzB;IACA,OAAO,IAAI,CAACQ,gBAAgB;EAChC;EACAK,WAAWA,CAACV,QAAQ,EAAEW,OAAO,EAAE;IAC3B,IAAI,CAACX,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACW,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACf,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACO,oBAAoB,GAAG,CAAC;IAC7B,IAAI,CAACE,gBAAgB,GAAG,CAAC;IACzB,IAAI,CAACP,eAAe,GAAG,KAAK;EAChC;EACAI,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACN,aAAa,GAAG,IAAI;EAC7B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}