{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { assertFn } from '../assert.js';\nimport { strictEquals } from '../equals.js';\nimport { DisposableStore } from '../lifecycle.js';\nimport { BaseObservable, _setDerivedOpts } from './base.js';\nimport { DebugNameData } from './debugName.js';\nimport { getLogger } from './logging.js';\nexport function derived(computeFnOrOwner, computeFn) {\n  if (computeFn !== undefined) {\n    return new Derived(new DebugNameData(computeFnOrOwner, undefined, computeFn), computeFn, undefined, undefined, undefined, strictEquals);\n  }\n  return new Derived(new DebugNameData(undefined, undefined, computeFnOrOwner), computeFnOrOwner, undefined, undefined, undefined, strictEquals);\n}\nexport function derivedWithSetter(owner, computeFn, setter) {\n  return new DerivedWithSetter(new DebugNameData(owner, undefined, computeFn), computeFn, undefined, undefined, undefined, strictEquals, setter);\n}\nexport function derivedOpts(options, computeFn) {\n  return new Derived(new DebugNameData(options.owner, options.debugName, options.debugReferenceFn), computeFn, undefined, undefined, options.onLastObserverRemoved, options.equalsFn ?? strictEquals);\n}\n_setDerivedOpts(derivedOpts);\n/**\n * Represents an observable that is derived from other observables.\n * The value is only recomputed when absolutely needed.\n *\n * {@link computeFn} should start with a JS Doc using `@description` to name the derived.\n *\n * Use `createEmptyChangeSummary` to create a \"change summary\" that can collect the changes.\n * Use `handleChange` to add a reported change to the change summary.\n * The compute function is given the last change summary.\n * The change summary is discarded after the compute function was called.\n *\n * @see derived\n */\nexport function derivedHandleChanges(options, computeFn) {\n  return new Derived(new DebugNameData(options.owner, options.debugName, undefined), computeFn, options.createEmptyChangeSummary, options.handleChange, undefined, options.equalityComparer ?? strictEquals);\n}\nexport function derivedWithStore(computeFnOrOwner, computeFnOrUndefined) {\n  let computeFn;\n  let owner;\n  if (computeFnOrUndefined === undefined) {\n    computeFn = computeFnOrOwner;\n    owner = undefined;\n  } else {\n    owner = computeFnOrOwner;\n    computeFn = computeFnOrUndefined;\n  }\n  const store = new DisposableStore();\n  return new Derived(new DebugNameData(owner, undefined, computeFn), r => {\n    store.clear();\n    return computeFn(r, store);\n  }, undefined, undefined, () => store.dispose(), strictEquals);\n}\nexport function derivedDisposable(computeFnOrOwner, computeFnOrUndefined) {\n  let computeFn;\n  let owner;\n  if (computeFnOrUndefined === undefined) {\n    computeFn = computeFnOrOwner;\n    owner = undefined;\n  } else {\n    owner = computeFnOrOwner;\n    computeFn = computeFnOrUndefined;\n  }\n  let store = undefined;\n  return new Derived(new DebugNameData(owner, undefined, computeFn), r => {\n    if (!store) {\n      store = new DisposableStore();\n    } else {\n      store.clear();\n    }\n    const result = computeFn(r);\n    if (result) {\n      store.add(result);\n    }\n    return result;\n  }, undefined, undefined, () => {\n    if (store) {\n      store.dispose();\n      store = undefined;\n    }\n  }, strictEquals);\n}\nexport class Derived extends BaseObservable {\n  get debugName() {\n    return this._debugNameData.getDebugName(this) ?? '(anonymous)';\n  }\n  constructor(_debugNameData, _computeFn, createChangeSummary, _handleChange, _handleLastObserverRemoved = undefined, _equalityComparator) {\n    super();\n    this._debugNameData = _debugNameData;\n    this._computeFn = _computeFn;\n    this.createChangeSummary = createChangeSummary;\n    this._handleChange = _handleChange;\n    this._handleLastObserverRemoved = _handleLastObserverRemoved;\n    this._equalityComparator = _equalityComparator;\n    this.state = 0 /* DerivedState.initial */;\n    this.value = undefined;\n    this.updateCount = 0;\n    this.dependencies = new Set();\n    this.dependenciesToBeRemoved = new Set();\n    this.changeSummary = undefined;\n    this.changeSummary = this.createChangeSummary?.();\n    getLogger()?.handleDerivedCreated(this);\n  }\n  onLastObserverRemoved() {\n    /**\n     * We are not tracking changes anymore, thus we have to assume\n     * that our cache is invalid.\n     */\n    this.state = 0 /* DerivedState.initial */;\n    this.value = undefined;\n    for (const d of this.dependencies) {\n      d.removeObserver(this);\n    }\n    this.dependencies.clear();\n    this._handleLastObserverRemoved?.();\n  }\n  get() {\n    if (this.observers.size === 0) {\n      // Without observers, we don't know when to clean up stuff.\n      // Thus, we don't cache anything to prevent memory leaks.\n      const result = this._computeFn(this, this.createChangeSummary?.());\n      // Clear new dependencies\n      this.onLastObserverRemoved();\n      return result;\n    } else {\n      do {\n        // We might not get a notification for a dependency that changed while it is updating,\n        // thus we also have to ask all our depedencies if they changed in this case.\n        if (this.state === 1 /* DerivedState.dependenciesMightHaveChanged */) {\n          for (const d of this.dependencies) {\n            /** might call {@link handleChange} indirectly, which could make us stale */\n            d.reportChanges();\n            if (this.state === 2 /* DerivedState.stale */) {\n              // The other dependencies will refresh on demand, so early break\n              break;\n            }\n          }\n        }\n        // We called report changes of all dependencies.\n        // If we are still not stale, we can assume to be up to date again.\n        if (this.state === 1 /* DerivedState.dependenciesMightHaveChanged */) {\n          this.state = 3 /* DerivedState.upToDate */;\n        }\n        this._recomputeIfNeeded();\n        // In case recomputation changed one of our dependencies, we need to recompute again.\n      } while (this.state !== 3 /* DerivedState.upToDate */);\n      return this.value;\n    }\n  }\n  _recomputeIfNeeded() {\n    if (this.state === 3 /* DerivedState.upToDate */) {\n      return;\n    }\n    const emptySet = this.dependenciesToBeRemoved;\n    this.dependenciesToBeRemoved = this.dependencies;\n    this.dependencies = emptySet;\n    const hadValue = this.state !== 0 /* DerivedState.initial */;\n    const oldValue = this.value;\n    this.state = 3 /* DerivedState.upToDate */;\n    const changeSummary = this.changeSummary;\n    this.changeSummary = this.createChangeSummary?.();\n    try {\n      /** might call {@link handleChange} indirectly, which could invalidate us */\n      this.value = this._computeFn(this, changeSummary);\n    } finally {\n      // We don't want our observed observables to think that they are (not even temporarily) not being observed.\n      // Thus, we only unsubscribe from observables that are definitely not read anymore.\n      for (const o of this.dependenciesToBeRemoved) {\n        o.removeObserver(this);\n      }\n      this.dependenciesToBeRemoved.clear();\n    }\n    const didChange = hadValue && !this._equalityComparator(oldValue, this.value);\n    getLogger()?.handleDerivedRecomputed(this, {\n      oldValue,\n      newValue: this.value,\n      change: undefined,\n      didChange,\n      hadValue\n    });\n    if (didChange) {\n      for (const r of this.observers) {\n        r.handleChange(this, undefined);\n      }\n    }\n  }\n  toString() {\n    return `LazyDerived<${this.debugName}>`;\n  }\n  // IObserver Implementation\n  beginUpdate(_observable) {\n    this.updateCount++;\n    const propagateBeginUpdate = this.updateCount === 1;\n    if (this.state === 3 /* DerivedState.upToDate */) {\n      this.state = 1 /* DerivedState.dependenciesMightHaveChanged */;\n      // If we propagate begin update, that will already signal a possible change.\n      if (!propagateBeginUpdate) {\n        for (const r of this.observers) {\n          r.handlePossibleChange(this);\n        }\n      }\n    }\n    if (propagateBeginUpdate) {\n      for (const r of this.observers) {\n        r.beginUpdate(this); // This signals a possible change\n      }\n    }\n  }\n  endUpdate(_observable) {\n    this.updateCount--;\n    if (this.updateCount === 0) {\n      // End update could change the observer list.\n      const observers = [...this.observers];\n      for (const r of observers) {\n        r.endUpdate(this);\n      }\n    }\n    assertFn(() => this.updateCount >= 0);\n  }\n  handlePossibleChange(observable) {\n    // In all other states, observers already know that we might have changed.\n    if (this.state === 3 /* DerivedState.upToDate */ && this.dependencies.has(observable) && !this.dependenciesToBeRemoved.has(observable)) {\n      this.state = 1 /* DerivedState.dependenciesMightHaveChanged */;\n      for (const r of this.observers) {\n        r.handlePossibleChange(this);\n      }\n    }\n  }\n  handleChange(observable, change) {\n    if (this.dependencies.has(observable) && !this.dependenciesToBeRemoved.has(observable)) {\n      const shouldReact = this._handleChange ? this._handleChange({\n        changedObservable: observable,\n        change,\n        didChange: o => o === observable\n      }, this.changeSummary) : true;\n      const wasUpToDate = this.state === 3 /* DerivedState.upToDate */;\n      if (shouldReact && (this.state === 1 /* DerivedState.dependenciesMightHaveChanged */ || wasUpToDate)) {\n        this.state = 2 /* DerivedState.stale */;\n        if (wasUpToDate) {\n          for (const r of this.observers) {\n            r.handlePossibleChange(this);\n          }\n        }\n      }\n    }\n  }\n  // IReader Implementation\n  readObservable(observable) {\n    // Subscribe before getting the value to enable caching\n    observable.addObserver(this);\n    /** This might call {@link handleChange} indirectly, which could invalidate us */\n    const value = observable.get();\n    // Which is why we only add the observable to the dependencies now.\n    this.dependencies.add(observable);\n    this.dependenciesToBeRemoved.delete(observable);\n    return value;\n  }\n  addObserver(observer) {\n    const shouldCallBeginUpdate = !this.observers.has(observer) && this.updateCount > 0;\n    super.addObserver(observer);\n    if (shouldCallBeginUpdate) {\n      observer.beginUpdate(this);\n    }\n  }\n  removeObserver(observer) {\n    const shouldCallEndUpdate = this.observers.has(observer) && this.updateCount > 0;\n    super.removeObserver(observer);\n    if (shouldCallEndUpdate) {\n      // Calling end update after removing the observer makes sure endUpdate cannot be called twice here.\n      observer.endUpdate(this);\n    }\n  }\n}\nexport class DerivedWithSetter extends Derived {\n  constructor(debugNameData, computeFn, createChangeSummary, handleChange, handleLastObserverRemoved = undefined, equalityComparator, set) {\n    super(debugNameData, computeFn, createChangeSummary, handleChange, handleLastObserverRemoved, equalityComparator);\n    this.set = set;\n  }\n}", "map": {"version": 3, "names": ["assertFn", "strictEquals", "DisposableStore", "BaseObservable", "_setDerivedOpts", "DebugNameData", "<PERSON><PERSON><PERSON><PERSON>", "derived", "computeFnOrOwner", "computeFn", "undefined", "Derived", "derivedWithSetter", "owner", "setter", "DerivedWithSetter", "derivedOpts", "options", "debugName", "debugReferenceFn", "onLastObserverRemoved", "equalsFn", "derivedHandleChanges", "createEmptyChangeSummary", "handleChange", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "derivedWithStore", "computeFnOrUndefined", "store", "r", "clear", "dispose", "derivedDisposable", "result", "add", "_debugNameData", "getDebugName", "constructor", "_computeFn", "createChangeSummary", "_handleChange", "_handleLastObserverRemoved", "_equalityComparator", "state", "value", "updateCount", "dependencies", "Set", "dependenciesToBeRemoved", "changeSummary", "handleDerivedCreated", "d", "removeObserver", "get", "observers", "size", "reportChanges", "_recomputeIfNeeded", "emptySet", "hadValue", "oldValue", "o", "<PERSON><PERSON><PERSON><PERSON>", "handleDerivedRecomputed", "newValue", "change", "toString", "beginUpdate", "_observable", "propagateBeginUpdate", "handlePossibleChange", "endUpdate", "observable", "has", "shouldReact", "changedObservable", "wasUpToDate", "readObservable", "addObserver", "delete", "observer", "shouldCallBeginUpdate", "shouldCallEndUpdate", "debugNameData", "handleLastObserverRemoved", "equalityComparator", "set"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/common/observableInternal/derived.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { assertFn } from '../assert.js';\nimport { strictEquals } from '../equals.js';\nimport { DisposableStore } from '../lifecycle.js';\nimport { BaseObservable, _setDerivedOpts } from './base.js';\nimport { DebugNameData } from './debugName.js';\nimport { getLogger } from './logging.js';\nexport function derived(computeFnOrOwner, computeFn) {\n    if (computeFn !== undefined) {\n        return new Derived(new DebugNameData(computeFnOrOwner, undefined, computeFn), computeFn, undefined, undefined, undefined, strictEquals);\n    }\n    return new Derived(new DebugNameData(undefined, undefined, computeFnOrOwner), computeFnOrOwner, undefined, undefined, undefined, strictEquals);\n}\nexport function derivedWithSetter(owner, computeFn, setter) {\n    return new DerivedWithSetter(new DebugNameData(owner, undefined, computeFn), computeFn, undefined, undefined, undefined, strictEquals, setter);\n}\nexport function derivedOpts(options, computeFn) {\n    return new Derived(new DebugNameData(options.owner, options.debugName, options.debugReferenceFn), computeFn, undefined, undefined, options.onLastObserverRemoved, options.equalsFn ?? strictEquals);\n}\n_setDerivedOpts(derivedOpts);\n/**\n * Represents an observable that is derived from other observables.\n * The value is only recomputed when absolutely needed.\n *\n * {@link computeFn} should start with a JS Doc using `@description` to name the derived.\n *\n * Use `createEmptyChangeSummary` to create a \"change summary\" that can collect the changes.\n * Use `handleChange` to add a reported change to the change summary.\n * The compute function is given the last change summary.\n * The change summary is discarded after the compute function was called.\n *\n * @see derived\n */\nexport function derivedHandleChanges(options, computeFn) {\n    return new Derived(new DebugNameData(options.owner, options.debugName, undefined), computeFn, options.createEmptyChangeSummary, options.handleChange, undefined, options.equalityComparer ?? strictEquals);\n}\nexport function derivedWithStore(computeFnOrOwner, computeFnOrUndefined) {\n    let computeFn;\n    let owner;\n    if (computeFnOrUndefined === undefined) {\n        computeFn = computeFnOrOwner;\n        owner = undefined;\n    }\n    else {\n        owner = computeFnOrOwner;\n        computeFn = computeFnOrUndefined;\n    }\n    const store = new DisposableStore();\n    return new Derived(new DebugNameData(owner, undefined, computeFn), r => {\n        store.clear();\n        return computeFn(r, store);\n    }, undefined, undefined, () => store.dispose(), strictEquals);\n}\nexport function derivedDisposable(computeFnOrOwner, computeFnOrUndefined) {\n    let computeFn;\n    let owner;\n    if (computeFnOrUndefined === undefined) {\n        computeFn = computeFnOrOwner;\n        owner = undefined;\n    }\n    else {\n        owner = computeFnOrOwner;\n        computeFn = computeFnOrUndefined;\n    }\n    let store = undefined;\n    return new Derived(new DebugNameData(owner, undefined, computeFn), r => {\n        if (!store) {\n            store = new DisposableStore();\n        }\n        else {\n            store.clear();\n        }\n        const result = computeFn(r);\n        if (result) {\n            store.add(result);\n        }\n        return result;\n    }, undefined, undefined, () => {\n        if (store) {\n            store.dispose();\n            store = undefined;\n        }\n    }, strictEquals);\n}\nexport class Derived extends BaseObservable {\n    get debugName() {\n        return this._debugNameData.getDebugName(this) ?? '(anonymous)';\n    }\n    constructor(_debugNameData, _computeFn, createChangeSummary, _handleChange, _handleLastObserverRemoved = undefined, _equalityComparator) {\n        super();\n        this._debugNameData = _debugNameData;\n        this._computeFn = _computeFn;\n        this.createChangeSummary = createChangeSummary;\n        this._handleChange = _handleChange;\n        this._handleLastObserverRemoved = _handleLastObserverRemoved;\n        this._equalityComparator = _equalityComparator;\n        this.state = 0 /* DerivedState.initial */;\n        this.value = undefined;\n        this.updateCount = 0;\n        this.dependencies = new Set();\n        this.dependenciesToBeRemoved = new Set();\n        this.changeSummary = undefined;\n        this.changeSummary = this.createChangeSummary?.();\n        getLogger()?.handleDerivedCreated(this);\n    }\n    onLastObserverRemoved() {\n        /**\n         * We are not tracking changes anymore, thus we have to assume\n         * that our cache is invalid.\n         */\n        this.state = 0 /* DerivedState.initial */;\n        this.value = undefined;\n        for (const d of this.dependencies) {\n            d.removeObserver(this);\n        }\n        this.dependencies.clear();\n        this._handleLastObserverRemoved?.();\n    }\n    get() {\n        if (this.observers.size === 0) {\n            // Without observers, we don't know when to clean up stuff.\n            // Thus, we don't cache anything to prevent memory leaks.\n            const result = this._computeFn(this, this.createChangeSummary?.());\n            // Clear new dependencies\n            this.onLastObserverRemoved();\n            return result;\n        }\n        else {\n            do {\n                // We might not get a notification for a dependency that changed while it is updating,\n                // thus we also have to ask all our depedencies if they changed in this case.\n                if (this.state === 1 /* DerivedState.dependenciesMightHaveChanged */) {\n                    for (const d of this.dependencies) {\n                        /** might call {@link handleChange} indirectly, which could make us stale */\n                        d.reportChanges();\n                        if (this.state === 2 /* DerivedState.stale */) {\n                            // The other dependencies will refresh on demand, so early break\n                            break;\n                        }\n                    }\n                }\n                // We called report changes of all dependencies.\n                // If we are still not stale, we can assume to be up to date again.\n                if (this.state === 1 /* DerivedState.dependenciesMightHaveChanged */) {\n                    this.state = 3 /* DerivedState.upToDate */;\n                }\n                this._recomputeIfNeeded();\n                // In case recomputation changed one of our dependencies, we need to recompute again.\n            } while (this.state !== 3 /* DerivedState.upToDate */);\n            return this.value;\n        }\n    }\n    _recomputeIfNeeded() {\n        if (this.state === 3 /* DerivedState.upToDate */) {\n            return;\n        }\n        const emptySet = this.dependenciesToBeRemoved;\n        this.dependenciesToBeRemoved = this.dependencies;\n        this.dependencies = emptySet;\n        const hadValue = this.state !== 0 /* DerivedState.initial */;\n        const oldValue = this.value;\n        this.state = 3 /* DerivedState.upToDate */;\n        const changeSummary = this.changeSummary;\n        this.changeSummary = this.createChangeSummary?.();\n        try {\n            /** might call {@link handleChange} indirectly, which could invalidate us */\n            this.value = this._computeFn(this, changeSummary);\n        }\n        finally {\n            // We don't want our observed observables to think that they are (not even temporarily) not being observed.\n            // Thus, we only unsubscribe from observables that are definitely not read anymore.\n            for (const o of this.dependenciesToBeRemoved) {\n                o.removeObserver(this);\n            }\n            this.dependenciesToBeRemoved.clear();\n        }\n        const didChange = hadValue && !(this._equalityComparator(oldValue, this.value));\n        getLogger()?.handleDerivedRecomputed(this, {\n            oldValue,\n            newValue: this.value,\n            change: undefined,\n            didChange,\n            hadValue,\n        });\n        if (didChange) {\n            for (const r of this.observers) {\n                r.handleChange(this, undefined);\n            }\n        }\n    }\n    toString() {\n        return `LazyDerived<${this.debugName}>`;\n    }\n    // IObserver Implementation\n    beginUpdate(_observable) {\n        this.updateCount++;\n        const propagateBeginUpdate = this.updateCount === 1;\n        if (this.state === 3 /* DerivedState.upToDate */) {\n            this.state = 1 /* DerivedState.dependenciesMightHaveChanged */;\n            // If we propagate begin update, that will already signal a possible change.\n            if (!propagateBeginUpdate) {\n                for (const r of this.observers) {\n                    r.handlePossibleChange(this);\n                }\n            }\n        }\n        if (propagateBeginUpdate) {\n            for (const r of this.observers) {\n                r.beginUpdate(this); // This signals a possible change\n            }\n        }\n    }\n    endUpdate(_observable) {\n        this.updateCount--;\n        if (this.updateCount === 0) {\n            // End update could change the observer list.\n            const observers = [...this.observers];\n            for (const r of observers) {\n                r.endUpdate(this);\n            }\n        }\n        assertFn(() => this.updateCount >= 0);\n    }\n    handlePossibleChange(observable) {\n        // In all other states, observers already know that we might have changed.\n        if (this.state === 3 /* DerivedState.upToDate */ && this.dependencies.has(observable) && !this.dependenciesToBeRemoved.has(observable)) {\n            this.state = 1 /* DerivedState.dependenciesMightHaveChanged */;\n            for (const r of this.observers) {\n                r.handlePossibleChange(this);\n            }\n        }\n    }\n    handleChange(observable, change) {\n        if (this.dependencies.has(observable) && !this.dependenciesToBeRemoved.has(observable)) {\n            const shouldReact = this._handleChange ? this._handleChange({\n                changedObservable: observable,\n                change,\n                didChange: (o) => o === observable,\n            }, this.changeSummary) : true;\n            const wasUpToDate = this.state === 3 /* DerivedState.upToDate */;\n            if (shouldReact && (this.state === 1 /* DerivedState.dependenciesMightHaveChanged */ || wasUpToDate)) {\n                this.state = 2 /* DerivedState.stale */;\n                if (wasUpToDate) {\n                    for (const r of this.observers) {\n                        r.handlePossibleChange(this);\n                    }\n                }\n            }\n        }\n    }\n    // IReader Implementation\n    readObservable(observable) {\n        // Subscribe before getting the value to enable caching\n        observable.addObserver(this);\n        /** This might call {@link handleChange} indirectly, which could invalidate us */\n        const value = observable.get();\n        // Which is why we only add the observable to the dependencies now.\n        this.dependencies.add(observable);\n        this.dependenciesToBeRemoved.delete(observable);\n        return value;\n    }\n    addObserver(observer) {\n        const shouldCallBeginUpdate = !this.observers.has(observer) && this.updateCount > 0;\n        super.addObserver(observer);\n        if (shouldCallBeginUpdate) {\n            observer.beginUpdate(this);\n        }\n    }\n    removeObserver(observer) {\n        const shouldCallEndUpdate = this.observers.has(observer) && this.updateCount > 0;\n        super.removeObserver(observer);\n        if (shouldCallEndUpdate) {\n            // Calling end update after removing the observer makes sure endUpdate cannot be called twice here.\n            observer.endUpdate(this);\n        }\n    }\n}\nexport class DerivedWithSetter extends Derived {\n    constructor(debugNameData, computeFn, createChangeSummary, handleChange, handleLastObserverRemoved = undefined, equalityComparator, set) {\n        super(debugNameData, computeFn, createChangeSummary, handleChange, handleLastObserverRemoved, equalityComparator);\n        this.set = set;\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,QAAQ,QAAQ,cAAc;AACvC,SAASC,YAAY,QAAQ,cAAc;AAC3C,SAASC,eAAe,QAAQ,iBAAiB;AACjD,SAASC,cAAc,EAAEC,eAAe,QAAQ,WAAW;AAC3D,SAASC,aAAa,QAAQ,gBAAgB;AAC9C,SAASC,SAAS,QAAQ,cAAc;AACxC,OAAO,SAASC,OAAOA,CAACC,gBAAgB,EAAEC,SAAS,EAAE;EACjD,IAAIA,SAAS,KAAKC,SAAS,EAAE;IACzB,OAAO,IAAIC,OAAO,CAAC,IAAIN,aAAa,CAACG,gBAAgB,EAAEE,SAAS,EAAED,SAAS,CAAC,EAAEA,SAAS,EAAEC,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAET,YAAY,CAAC;EAC3I;EACA,OAAO,IAAIU,OAAO,CAAC,IAAIN,aAAa,CAACK,SAAS,EAAEA,SAAS,EAAEF,gBAAgB,CAAC,EAAEA,gBAAgB,EAAEE,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAET,YAAY,CAAC;AAClJ;AACA,OAAO,SAASW,iBAAiBA,CAACC,KAAK,EAAEJ,SAAS,EAAEK,MAAM,EAAE;EACxD,OAAO,IAAIC,iBAAiB,CAAC,IAAIV,aAAa,CAACQ,KAAK,EAAEH,SAAS,EAAED,SAAS,CAAC,EAAEA,SAAS,EAAEC,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAET,YAAY,EAAEa,MAAM,CAAC;AAClJ;AACA,OAAO,SAASE,WAAWA,CAACC,OAAO,EAAER,SAAS,EAAE;EAC5C,OAAO,IAAIE,OAAO,CAAC,IAAIN,aAAa,CAACY,OAAO,CAACJ,KAAK,EAAEI,OAAO,CAACC,SAAS,EAAED,OAAO,CAACE,gBAAgB,CAAC,EAAEV,SAAS,EAAEC,SAAS,EAAEA,SAAS,EAAEO,OAAO,CAACG,qBAAqB,EAAEH,OAAO,CAACI,QAAQ,IAAIpB,YAAY,CAAC;AACvM;AACAG,eAAe,CAACY,WAAW,CAAC;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASM,oBAAoBA,CAACL,OAAO,EAAER,SAAS,EAAE;EACrD,OAAO,IAAIE,OAAO,CAAC,IAAIN,aAAa,CAACY,OAAO,CAACJ,KAAK,EAAEI,OAAO,CAACC,SAAS,EAAER,SAAS,CAAC,EAAED,SAAS,EAAEQ,OAAO,CAACM,wBAAwB,EAAEN,OAAO,CAACO,YAAY,EAAEd,SAAS,EAAEO,OAAO,CAACQ,gBAAgB,IAAIxB,YAAY,CAAC;AAC9M;AACA,OAAO,SAASyB,gBAAgBA,CAAClB,gBAAgB,EAAEmB,oBAAoB,EAAE;EACrE,IAAIlB,SAAS;EACb,IAAII,KAAK;EACT,IAAIc,oBAAoB,KAAKjB,SAAS,EAAE;IACpCD,SAAS,GAAGD,gBAAgB;IAC5BK,KAAK,GAAGH,SAAS;EACrB,CAAC,MACI;IACDG,KAAK,GAAGL,gBAAgB;IACxBC,SAAS,GAAGkB,oBAAoB;EACpC;EACA,MAAMC,KAAK,GAAG,IAAI1B,eAAe,CAAC,CAAC;EACnC,OAAO,IAAIS,OAAO,CAAC,IAAIN,aAAa,CAACQ,KAAK,EAAEH,SAAS,EAAED,SAAS,CAAC,EAAEoB,CAAC,IAAI;IACpED,KAAK,CAACE,KAAK,CAAC,CAAC;IACb,OAAOrB,SAAS,CAACoB,CAAC,EAAED,KAAK,CAAC;EAC9B,CAAC,EAAElB,SAAS,EAAEA,SAAS,EAAE,MAAMkB,KAAK,CAACG,OAAO,CAAC,CAAC,EAAE9B,YAAY,CAAC;AACjE;AACA,OAAO,SAAS+B,iBAAiBA,CAACxB,gBAAgB,EAAEmB,oBAAoB,EAAE;EACtE,IAAIlB,SAAS;EACb,IAAII,KAAK;EACT,IAAIc,oBAAoB,KAAKjB,SAAS,EAAE;IACpCD,SAAS,GAAGD,gBAAgB;IAC5BK,KAAK,GAAGH,SAAS;EACrB,CAAC,MACI;IACDG,KAAK,GAAGL,gBAAgB;IACxBC,SAAS,GAAGkB,oBAAoB;EACpC;EACA,IAAIC,KAAK,GAAGlB,SAAS;EACrB,OAAO,IAAIC,OAAO,CAAC,IAAIN,aAAa,CAACQ,KAAK,EAAEH,SAAS,EAAED,SAAS,CAAC,EAAEoB,CAAC,IAAI;IACpE,IAAI,CAACD,KAAK,EAAE;MACRA,KAAK,GAAG,IAAI1B,eAAe,CAAC,CAAC;IACjC,CAAC,MACI;MACD0B,KAAK,CAACE,KAAK,CAAC,CAAC;IACjB;IACA,MAAMG,MAAM,GAAGxB,SAAS,CAACoB,CAAC,CAAC;IAC3B,IAAII,MAAM,EAAE;MACRL,KAAK,CAACM,GAAG,CAACD,MAAM,CAAC;IACrB;IACA,OAAOA,MAAM;EACjB,CAAC,EAAEvB,SAAS,EAAEA,SAAS,EAAE,MAAM;IAC3B,IAAIkB,KAAK,EAAE;MACPA,KAAK,CAACG,OAAO,CAAC,CAAC;MACfH,KAAK,GAAGlB,SAAS;IACrB;EACJ,CAAC,EAAET,YAAY,CAAC;AACpB;AACA,OAAO,MAAMU,OAAO,SAASR,cAAc,CAAC;EACxC,IAAIe,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACiB,cAAc,CAACC,YAAY,CAAC,IAAI,CAAC,IAAI,aAAa;EAClE;EACAC,WAAWA,CAACF,cAAc,EAAEG,UAAU,EAAEC,mBAAmB,EAAEC,aAAa,EAAEC,0BAA0B,GAAG/B,SAAS,EAAEgC,mBAAmB,EAAE;IACrI,KAAK,CAAC,CAAC;IACP,IAAI,CAACP,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACG,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,0BAA0B,GAAGA,0BAA0B;IAC5D,IAAI,CAACC,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;IACf,IAAI,CAACC,KAAK,GAAGlC,SAAS;IACtB,IAAI,CAACmC,WAAW,GAAG,CAAC;IACpB,IAAI,CAACC,YAAY,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC7B,IAAI,CAACC,uBAAuB,GAAG,IAAID,GAAG,CAAC,CAAC;IACxC,IAAI,CAACE,aAAa,GAAGvC,SAAS;IAC9B,IAAI,CAACuC,aAAa,GAAG,IAAI,CAACV,mBAAmB,GAAG,CAAC;IACjDjC,SAAS,CAAC,CAAC,EAAE4C,oBAAoB,CAAC,IAAI,CAAC;EAC3C;EACA9B,qBAAqBA,CAAA,EAAG;IACpB;AACR;AACA;AACA;IACQ,IAAI,CAACuB,KAAK,GAAG,CAAC,CAAC;IACf,IAAI,CAACC,KAAK,GAAGlC,SAAS;IACtB,KAAK,MAAMyC,CAAC,IAAI,IAAI,CAACL,YAAY,EAAE;MAC/BK,CAAC,CAACC,cAAc,CAAC,IAAI,CAAC;IAC1B;IACA,IAAI,CAACN,YAAY,CAAChB,KAAK,CAAC,CAAC;IACzB,IAAI,CAACW,0BAA0B,GAAG,CAAC;EACvC;EACAY,GAAGA,CAAA,EAAG;IACF,IAAI,IAAI,CAACC,SAAS,CAACC,IAAI,KAAK,CAAC,EAAE;MAC3B;MACA;MACA,MAAMtB,MAAM,GAAG,IAAI,CAACK,UAAU,CAAC,IAAI,EAAE,IAAI,CAACC,mBAAmB,GAAG,CAAC,CAAC;MAClE;MACA,IAAI,CAACnB,qBAAqB,CAAC,CAAC;MAC5B,OAAOa,MAAM;IACjB,CAAC,MACI;MACD,GAAG;QACC;QACA;QACA,IAAI,IAAI,CAACU,KAAK,KAAK,CAAC,CAAC,iDAAiD;UAClE,KAAK,MAAMQ,CAAC,IAAI,IAAI,CAACL,YAAY,EAAE;YAC/B;YACAK,CAAC,CAACK,aAAa,CAAC,CAAC;YACjB,IAAI,IAAI,CAACb,KAAK,KAAK,CAAC,CAAC,0BAA0B;cAC3C;cACA;YACJ;UACJ;QACJ;QACA;QACA;QACA,IAAI,IAAI,CAACA,KAAK,KAAK,CAAC,CAAC,iDAAiD;UAClE,IAAI,CAACA,KAAK,GAAG,CAAC,CAAC;QACnB;QACA,IAAI,CAACc,kBAAkB,CAAC,CAAC;QACzB;MACJ,CAAC,QAAQ,IAAI,CAACd,KAAK,KAAK,CAAC,CAAC;MAC1B,OAAO,IAAI,CAACC,KAAK;IACrB;EACJ;EACAa,kBAAkBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAACd,KAAK,KAAK,CAAC,CAAC,6BAA6B;MAC9C;IACJ;IACA,MAAMe,QAAQ,GAAG,IAAI,CAACV,uBAAuB;IAC7C,IAAI,CAACA,uBAAuB,GAAG,IAAI,CAACF,YAAY;IAChD,IAAI,CAACA,YAAY,GAAGY,QAAQ;IAC5B,MAAMC,QAAQ,GAAG,IAAI,CAAChB,KAAK,KAAK,CAAC,CAAC;IAClC,MAAMiB,QAAQ,GAAG,IAAI,CAAChB,KAAK;IAC3B,IAAI,CAACD,KAAK,GAAG,CAAC,CAAC;IACf,MAAMM,aAAa,GAAG,IAAI,CAACA,aAAa;IACxC,IAAI,CAACA,aAAa,GAAG,IAAI,CAACV,mBAAmB,GAAG,CAAC;IACjD,IAAI;MACA;MACA,IAAI,CAACK,KAAK,GAAG,IAAI,CAACN,UAAU,CAAC,IAAI,EAAEW,aAAa,CAAC;IACrD,CAAC,SACO;MACJ;MACA;MACA,KAAK,MAAMY,CAAC,IAAI,IAAI,CAACb,uBAAuB,EAAE;QAC1Ca,CAAC,CAACT,cAAc,CAAC,IAAI,CAAC;MAC1B;MACA,IAAI,CAACJ,uBAAuB,CAAClB,KAAK,CAAC,CAAC;IACxC;IACA,MAAMgC,SAAS,GAAGH,QAAQ,IAAI,CAAE,IAAI,CAACjB,mBAAmB,CAACkB,QAAQ,EAAE,IAAI,CAAChB,KAAK,CAAE;IAC/EtC,SAAS,CAAC,CAAC,EAAEyD,uBAAuB,CAAC,IAAI,EAAE;MACvCH,QAAQ;MACRI,QAAQ,EAAE,IAAI,CAACpB,KAAK;MACpBqB,MAAM,EAAEvD,SAAS;MACjBoD,SAAS;MACTH;IACJ,CAAC,CAAC;IACF,IAAIG,SAAS,EAAE;MACX,KAAK,MAAMjC,CAAC,IAAI,IAAI,CAACyB,SAAS,EAAE;QAC5BzB,CAAC,CAACL,YAAY,CAAC,IAAI,EAAEd,SAAS,CAAC;MACnC;IACJ;EACJ;EACAwD,QAAQA,CAAA,EAAG;IACP,OAAO,eAAe,IAAI,CAAChD,SAAS,GAAG;EAC3C;EACA;EACAiD,WAAWA,CAACC,WAAW,EAAE;IACrB,IAAI,CAACvB,WAAW,EAAE;IAClB,MAAMwB,oBAAoB,GAAG,IAAI,CAACxB,WAAW,KAAK,CAAC;IACnD,IAAI,IAAI,CAACF,KAAK,KAAK,CAAC,CAAC,6BAA6B;MAC9C,IAAI,CAACA,KAAK,GAAG,CAAC,CAAC;MACf;MACA,IAAI,CAAC0B,oBAAoB,EAAE;QACvB,KAAK,MAAMxC,CAAC,IAAI,IAAI,CAACyB,SAAS,EAAE;UAC5BzB,CAAC,CAACyC,oBAAoB,CAAC,IAAI,CAAC;QAChC;MACJ;IACJ;IACA,IAAID,oBAAoB,EAAE;MACtB,KAAK,MAAMxC,CAAC,IAAI,IAAI,CAACyB,SAAS,EAAE;QAC5BzB,CAAC,CAACsC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;MACzB;IACJ;EACJ;EACAI,SAASA,CAACH,WAAW,EAAE;IACnB,IAAI,CAACvB,WAAW,EAAE;IAClB,IAAI,IAAI,CAACA,WAAW,KAAK,CAAC,EAAE;MACxB;MACA,MAAMS,SAAS,GAAG,CAAC,GAAG,IAAI,CAACA,SAAS,CAAC;MACrC,KAAK,MAAMzB,CAAC,IAAIyB,SAAS,EAAE;QACvBzB,CAAC,CAAC0C,SAAS,CAAC,IAAI,CAAC;MACrB;IACJ;IACAvE,QAAQ,CAAC,MAAM,IAAI,CAAC6C,WAAW,IAAI,CAAC,CAAC;EACzC;EACAyB,oBAAoBA,CAACE,UAAU,EAAE;IAC7B;IACA,IAAI,IAAI,CAAC7B,KAAK,KAAK,CAAC,CAAC,+BAA+B,IAAI,CAACG,YAAY,CAAC2B,GAAG,CAACD,UAAU,CAAC,IAAI,CAAC,IAAI,CAACxB,uBAAuB,CAACyB,GAAG,CAACD,UAAU,CAAC,EAAE;MACpI,IAAI,CAAC7B,KAAK,GAAG,CAAC,CAAC;MACf,KAAK,MAAMd,CAAC,IAAI,IAAI,CAACyB,SAAS,EAAE;QAC5BzB,CAAC,CAACyC,oBAAoB,CAAC,IAAI,CAAC;MAChC;IACJ;EACJ;EACA9C,YAAYA,CAACgD,UAAU,EAAEP,MAAM,EAAE;IAC7B,IAAI,IAAI,CAACnB,YAAY,CAAC2B,GAAG,CAACD,UAAU,CAAC,IAAI,CAAC,IAAI,CAACxB,uBAAuB,CAACyB,GAAG,CAACD,UAAU,CAAC,EAAE;MACpF,MAAME,WAAW,GAAG,IAAI,CAAClC,aAAa,GAAG,IAAI,CAACA,aAAa,CAAC;QACxDmC,iBAAiB,EAAEH,UAAU;QAC7BP,MAAM;QACNH,SAAS,EAAGD,CAAC,IAAKA,CAAC,KAAKW;MAC5B,CAAC,EAAE,IAAI,CAACvB,aAAa,CAAC,GAAG,IAAI;MAC7B,MAAM2B,WAAW,GAAG,IAAI,CAACjC,KAAK,KAAK,CAAC,CAAC;MACrC,IAAI+B,WAAW,KAAK,IAAI,CAAC/B,KAAK,KAAK,CAAC,CAAC,mDAAmDiC,WAAW,CAAC,EAAE;QAClG,IAAI,CAACjC,KAAK,GAAG,CAAC,CAAC;QACf,IAAIiC,WAAW,EAAE;UACb,KAAK,MAAM/C,CAAC,IAAI,IAAI,CAACyB,SAAS,EAAE;YAC5BzB,CAAC,CAACyC,oBAAoB,CAAC,IAAI,CAAC;UAChC;QACJ;MACJ;IACJ;EACJ;EACA;EACAO,cAAcA,CAACL,UAAU,EAAE;IACvB;IACAA,UAAU,CAACM,WAAW,CAAC,IAAI,CAAC;IAC5B;IACA,MAAMlC,KAAK,GAAG4B,UAAU,CAACnB,GAAG,CAAC,CAAC;IAC9B;IACA,IAAI,CAACP,YAAY,CAACZ,GAAG,CAACsC,UAAU,CAAC;IACjC,IAAI,CAACxB,uBAAuB,CAAC+B,MAAM,CAACP,UAAU,CAAC;IAC/C,OAAO5B,KAAK;EAChB;EACAkC,WAAWA,CAACE,QAAQ,EAAE;IAClB,MAAMC,qBAAqB,GAAG,CAAC,IAAI,CAAC3B,SAAS,CAACmB,GAAG,CAACO,QAAQ,CAAC,IAAI,IAAI,CAACnC,WAAW,GAAG,CAAC;IACnF,KAAK,CAACiC,WAAW,CAACE,QAAQ,CAAC;IAC3B,IAAIC,qBAAqB,EAAE;MACvBD,QAAQ,CAACb,WAAW,CAAC,IAAI,CAAC;IAC9B;EACJ;EACAf,cAAcA,CAAC4B,QAAQ,EAAE;IACrB,MAAME,mBAAmB,GAAG,IAAI,CAAC5B,SAAS,CAACmB,GAAG,CAACO,QAAQ,CAAC,IAAI,IAAI,CAACnC,WAAW,GAAG,CAAC;IAChF,KAAK,CAACO,cAAc,CAAC4B,QAAQ,CAAC;IAC9B,IAAIE,mBAAmB,EAAE;MACrB;MACAF,QAAQ,CAACT,SAAS,CAAC,IAAI,CAAC;IAC5B;EACJ;AACJ;AACA,OAAO,MAAMxD,iBAAiB,SAASJ,OAAO,CAAC;EAC3C0B,WAAWA,CAAC8C,aAAa,EAAE1E,SAAS,EAAE8B,mBAAmB,EAAEf,YAAY,EAAE4D,yBAAyB,GAAG1E,SAAS,EAAE2E,kBAAkB,EAAEC,GAAG,EAAE;IACrI,KAAK,CAACH,aAAa,EAAE1E,SAAS,EAAE8B,mBAAmB,EAAEf,YAAY,EAAE4D,yBAAyB,EAAEC,kBAAkB,CAAC;IACjH,IAAI,CAACC,GAAG,GAAGA,GAAG;EAClB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}