{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nconst sameOriginWindowChainCache = new WeakMap();\nfunction getParentWindowIfSameOrigin(w) {\n  if (!w.parent || w.parent === w) {\n    return null;\n  }\n  // Cannot really tell if we have access to the parent window unless we try to access something in it\n  try {\n    const location = w.location;\n    const parentLocation = w.parent.location;\n    if (location.origin !== 'null' && parentLocation.origin !== 'null' && location.origin !== parentLocation.origin) {\n      return null;\n    }\n  } catch (e) {\n    return null;\n  }\n  return w.parent;\n}\nexport class IframeUtils {\n  /**\n   * Returns a chain of embedded windows with the same origin (which can be accessed programmatically).\n   * Having a chain of length 1 might mean that the current execution environment is running outside of an iframe or inside an iframe embedded in a window with a different origin.\n   */\n  static getSameOriginWindowChain(targetWindow) {\n    let windowChainCache = sameOriginWindowChainCache.get(targetWindow);\n    if (!windowChainCache) {\n      windowChainCache = [];\n      sameOriginWindowChainCache.set(targetWindow, windowChainCache);\n      let w = targetWindow;\n      let parent;\n      do {\n        parent = getParentWindowIfSameOrigin(w);\n        if (parent) {\n          windowChainCache.push({\n            window: new WeakRef(w),\n            iframeElement: w.frameElement || null\n          });\n        } else {\n          windowChainCache.push({\n            window: new WeakRef(w),\n            iframeElement: null\n          });\n        }\n        w = parent;\n      } while (w);\n    }\n    return windowChainCache.slice(0);\n  }\n  /**\n   * Returns the position of `childWindow` relative to `ancestorWindow`\n   */\n  static getPositionOfChildWindowRelativeToAncestorWindow(childWindow, ancestorWindow) {\n    if (!ancestorWindow || childWindow === ancestorWindow) {\n      return {\n        top: 0,\n        left: 0\n      };\n    }\n    let top = 0,\n      left = 0;\n    const windowChain = this.getSameOriginWindowChain(childWindow);\n    for (const windowChainEl of windowChain) {\n      const windowInChain = windowChainEl.window.deref();\n      top += windowInChain?.scrollY ?? 0;\n      left += windowInChain?.scrollX ?? 0;\n      if (windowInChain === ancestorWindow) {\n        break;\n      }\n      if (!windowChainEl.iframeElement) {\n        break;\n      }\n      const boundingRect = windowChainEl.iframeElement.getBoundingClientRect();\n      top += boundingRect.top;\n      left += boundingRect.left;\n    }\n    return {\n      top: top,\n      left: left\n    };\n  }\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>n<PERSON>ache", "WeakMap", "getParentWindowIfSameOrigin", "w", "parent", "location", "parentLocation", "origin", "e", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getSameOriginWindowChain", "targetWindow", "window<PERSON><PERSON>n<PERSON><PERSON>", "get", "set", "push", "window", "WeakRef", "iframeElement", "frameElement", "slice", "getPositionOfChildWindowRelativeToAncestorWindow", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "top", "left", "window<PERSON><PERSON>n", "windowChainEl", "windowInChain", "deref", "scrollY", "scrollX", "boundingRect", "getBoundingClientRect"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/browser/iframe.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nconst sameOriginWindowChainCache = new WeakMap();\nfunction getParentWindowIfSameOrigin(w) {\n    if (!w.parent || w.parent === w) {\n        return null;\n    }\n    // Cannot really tell if we have access to the parent window unless we try to access something in it\n    try {\n        const location = w.location;\n        const parentLocation = w.parent.location;\n        if (location.origin !== 'null' && parentLocation.origin !== 'null' && location.origin !== parentLocation.origin) {\n            return null;\n        }\n    }\n    catch (e) {\n        return null;\n    }\n    return w.parent;\n}\nexport class IframeUtils {\n    /**\n     * Returns a chain of embedded windows with the same origin (which can be accessed programmatically).\n     * Having a chain of length 1 might mean that the current execution environment is running outside of an iframe or inside an iframe embedded in a window with a different origin.\n     */\n    static getSameOriginWindowChain(targetWindow) {\n        let windowChainCache = sameOriginWindowChainCache.get(targetWindow);\n        if (!windowChainCache) {\n            windowChainCache = [];\n            sameOriginWindowChainCache.set(targetWindow, windowChainCache);\n            let w = targetWindow;\n            let parent;\n            do {\n                parent = getParentWindowIfSameOrigin(w);\n                if (parent) {\n                    windowChainCache.push({\n                        window: new WeakRef(w),\n                        iframeElement: w.frameElement || null\n                    });\n                }\n                else {\n                    windowChainCache.push({\n                        window: new WeakRef(w),\n                        iframeElement: null\n                    });\n                }\n                w = parent;\n            } while (w);\n        }\n        return windowChainCache.slice(0);\n    }\n    /**\n     * Returns the position of `childWindow` relative to `ancestorWindow`\n     */\n    static getPositionOfChildWindowRelativeToAncestorWindow(childWindow, ancestorWindow) {\n        if (!ancestorWindow || childWindow === ancestorWindow) {\n            return {\n                top: 0,\n                left: 0\n            };\n        }\n        let top = 0, left = 0;\n        const windowChain = this.getSameOriginWindowChain(childWindow);\n        for (const windowChainEl of windowChain) {\n            const windowInChain = windowChainEl.window.deref();\n            top += windowInChain?.scrollY ?? 0;\n            left += windowInChain?.scrollX ?? 0;\n            if (windowInChain === ancestorWindow) {\n                break;\n            }\n            if (!windowChainEl.iframeElement) {\n                break;\n            }\n            const boundingRect = windowChainEl.iframeElement.getBoundingClientRect();\n            top += boundingRect.top;\n            left += boundingRect.left;\n        }\n        return {\n            top: top,\n            left: left\n        };\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,MAAMA,0BAA0B,GAAG,IAAIC,OAAO,CAAC,CAAC;AAChD,SAASC,2BAA2BA,CAACC,CAAC,EAAE;EACpC,IAAI,CAACA,CAAC,CAACC,MAAM,IAAID,CAAC,CAACC,MAAM,KAAKD,CAAC,EAAE;IAC7B,OAAO,IAAI;EACf;EACA;EACA,IAAI;IACA,MAAME,QAAQ,GAAGF,CAAC,CAACE,QAAQ;IAC3B,MAAMC,cAAc,GAAGH,CAAC,CAACC,MAAM,CAACC,QAAQ;IACxC,IAAIA,QAAQ,CAACE,MAAM,KAAK,MAAM,IAAID,cAAc,CAACC,MAAM,KAAK,MAAM,IAAIF,QAAQ,CAACE,MAAM,KAAKD,cAAc,CAACC,MAAM,EAAE;MAC7G,OAAO,IAAI;IACf;EACJ,CAAC,CACD,OAAOC,CAAC,EAAE;IACN,OAAO,IAAI;EACf;EACA,OAAOL,CAAC,CAACC,MAAM;AACnB;AACA,OAAO,MAAMK,WAAW,CAAC;EACrB;AACJ;AACA;AACA;EACI,OAAOC,wBAAwBA,CAACC,YAAY,EAAE;IAC1C,IAAIC,gBAAgB,GAAGZ,0BAA0B,CAACa,GAAG,CAACF,YAAY,CAAC;IACnE,IAAI,CAACC,gBAAgB,EAAE;MACnBA,gBAAgB,GAAG,EAAE;MACrBZ,0BAA0B,CAACc,GAAG,CAACH,YAAY,EAAEC,gBAAgB,CAAC;MAC9D,IAAIT,CAAC,GAAGQ,YAAY;MACpB,IAAIP,MAAM;MACV,GAAG;QACCA,MAAM,GAAGF,2BAA2B,CAACC,CAAC,CAAC;QACvC,IAAIC,MAAM,EAAE;UACRQ,gBAAgB,CAACG,IAAI,CAAC;YAClBC,MAAM,EAAE,IAAIC,OAAO,CAACd,CAAC,CAAC;YACtBe,aAAa,EAAEf,CAAC,CAACgB,YAAY,IAAI;UACrC,CAAC,CAAC;QACN,CAAC,MACI;UACDP,gBAAgB,CAACG,IAAI,CAAC;YAClBC,MAAM,EAAE,IAAIC,OAAO,CAACd,CAAC,CAAC;YACtBe,aAAa,EAAE;UACnB,CAAC,CAAC;QACN;QACAf,CAAC,GAAGC,MAAM;MACd,CAAC,QAAQD,CAAC;IACd;IACA,OAAOS,gBAAgB,CAACQ,KAAK,CAAC,CAAC,CAAC;EACpC;EACA;AACJ;AACA;EACI,OAAOC,gDAAgDA,CAACC,WAAW,EAAEC,cAAc,EAAE;IACjF,IAAI,CAACA,cAAc,IAAID,WAAW,KAAKC,cAAc,EAAE;MACnD,OAAO;QACHC,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE;MACV,CAAC;IACL;IACA,IAAID,GAAG,GAAG,CAAC;MAAEC,IAAI,GAAG,CAAC;IACrB,MAAMC,WAAW,GAAG,IAAI,CAAChB,wBAAwB,CAACY,WAAW,CAAC;IAC9D,KAAK,MAAMK,aAAa,IAAID,WAAW,EAAE;MACrC,MAAME,aAAa,GAAGD,aAAa,CAACX,MAAM,CAACa,KAAK,CAAC,CAAC;MAClDL,GAAG,IAAII,aAAa,EAAEE,OAAO,IAAI,CAAC;MAClCL,IAAI,IAAIG,aAAa,EAAEG,OAAO,IAAI,CAAC;MACnC,IAAIH,aAAa,KAAKL,cAAc,EAAE;QAClC;MACJ;MACA,IAAI,CAACI,aAAa,CAACT,aAAa,EAAE;QAC9B;MACJ;MACA,MAAMc,YAAY,GAAGL,aAAa,CAACT,aAAa,CAACe,qBAAqB,CAAC,CAAC;MACxET,GAAG,IAAIQ,YAAY,CAACR,GAAG;MACvBC,IAAI,IAAIO,YAAY,CAACP,IAAI;IAC7B;IACA,OAAO;MACHD,GAAG,EAAEA,GAAG;MACRC,IAAI,EAAEA;IACV,CAAC;EACL;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}