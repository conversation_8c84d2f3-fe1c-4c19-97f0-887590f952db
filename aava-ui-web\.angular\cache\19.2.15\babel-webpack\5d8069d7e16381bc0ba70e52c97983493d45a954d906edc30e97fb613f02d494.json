{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport const allCharCodes = (() => {\n  const v = [];\n  for (let i = 32 /* Constants.START_CH_CODE */; i <= 126 /* Constants.END_CH_CODE */; i++) {\n    v.push(i);\n  }\n  v.push(65533 /* Constants.UNKNOWN_CODE */);\n  return v;\n})();\nexport const getCharIndex = (chCode, fontScale) => {\n  chCode -= 32 /* Constants.START_CH_CODE */;\n  if (chCode < 0 || chCode > 96 /* Constants.CHAR_COUNT */) {\n    if (fontScale <= 2) {\n      // for smaller scales, we can get away with using any ASCII character...\n      return (chCode + 96 /* Constants.CHAR_COUNT */) % 96 /* Constants.CHAR_COUNT */;\n    }\n    return 96 /* Constants.CHAR_COUNT */ - 1; // unknown symbol\n  }\n  return chCode;\n};", "map": {"version": 3, "names": ["allCharCodes", "v", "i", "push", "getCharIndex", "chCode", "fontScale"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/browser/viewParts/minimap/minimapCharSheet.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport const allCharCodes = (() => {\n    const v = [];\n    for (let i = 32 /* Constants.START_CH_CODE */; i <= 126 /* Constants.END_CH_CODE */; i++) {\n        v.push(i);\n    }\n    v.push(65533 /* Constants.UNKNOWN_CODE */);\n    return v;\n})();\nexport const getCharIndex = (chCode, fontScale) => {\n    chCode -= 32 /* Constants.START_CH_CODE */;\n    if (chCode < 0 || chCode > 96 /* Constants.CHAR_COUNT */) {\n        if (fontScale <= 2) {\n            // for smaller scales, we can get away with using any ASCII character...\n            return (chCode + 96 /* Constants.CHAR_COUNT */) % 96 /* Constants.CHAR_COUNT */;\n        }\n        return 96 /* Constants.CHAR_COUNT */ - 1; // unknown symbol\n    }\n    return chCode;\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,MAAMA,YAAY,GAAG,CAAC,MAAM;EAC/B,MAAMC,CAAC,GAAG,EAAE;EACZ,KAAK,IAAIC,CAAC,GAAG,EAAE,CAAC,+BAA+BA,CAAC,IAAI,GAAG,CAAC,6BAA6BA,CAAC,EAAE,EAAE;IACtFD,CAAC,CAACE,IAAI,CAACD,CAAC,CAAC;EACb;EACAD,CAAC,CAACE,IAAI,CAAC,KAAK,CAAC,4BAA4B,CAAC;EAC1C,OAAOF,CAAC;AACZ,CAAC,EAAE,CAAC;AACJ,OAAO,MAAMG,YAAY,GAAGA,CAACC,MAAM,EAAEC,SAAS,KAAK;EAC/CD,MAAM,IAAI,EAAE,CAAC;EACb,IAAIA,MAAM,GAAG,CAAC,IAAIA,MAAM,GAAG,EAAE,CAAC,4BAA4B;IACtD,IAAIC,SAAS,IAAI,CAAC,EAAE;MAChB;MACA,OAAO,CAACD,MAAM,GAAG,EAAE,CAAC,8BAA8B,EAAE,CAAC;IACzD;IACA,OAAO,EAAE,CAAC,6BAA6B,CAAC,CAAC,CAAC;EAC9C;EACA,OAAOA,MAAM;AACjB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}