{"ast": null, "code": "import { keybinding<PERSON>abelBackground, keybindingLabelBorder, keybindingLabelBottomBorder, keybindingLabelForeground, asCssVariable, widgetShadow, buttonForeground, buttonSeparator, buttonBackground, buttonHoverBackground, buttonSecondaryForeground, buttonSecondaryBackground, buttonSecondaryHoverBackground, buttonBorder, progressBarBackground, inputActiveOptionBorder, inputActiveOptionForeground, inputActiveOptionBackground, editorWidgetBackground, editorWidgetForeground, contrastBorder, checkboxBorder, checkboxBackground, checkboxForeground, problemsErrorIconForeground, problemsWarningIconForeground, problemsInfoIconForeground, inputBackground, inputForeground, inputBorder, textLinkForeground, inputValidationInfoBorder, inputValidationInfoBackground, inputValidationInfoForeground, inputValidationWarningBorder, inputValidationWarningBackground, inputValidationWarningForeground, inputValidationErrorBorder, inputValidationErrorBackground, inputValidationErrorForeground, listFilterWidgetBackground, listFilterWidgetNoMatchesOutline, listFilterWidgetOutline, listFilterWidgetShadow, badgeBackground, badgeForeground, breadcrumbsBackground, breadcrumbsForeground, breadcrumbsFocusForeground, breadcrumbsActiveSelectionForeground, activeContrastBorder, listActiveSelectionBackground, listActiveSelectionForeground, listActiveSelectionIconForeground, listDropOverBackground, listFocusAndSelectionOutline, listFocusBackground, listFocusForeground, listFocusOutline, listHoverBackground, listHoverForeground, listInactiveFocusBackground, listInactiveFocusOutline, listInactiveSelectionBackground, listInactiveSelectionForeground, listInactiveSelectionIconForeground, tableColumnsBorder, tableOddRowsBackgroundColor, treeIndentGuidesStroke, asCssVariableWithDefault, editorWidgetBorder, focusBorder, pickerGroupForeground, quickInputListFocusBackground, quickInputListFocusForeground, quickInputListFocusIconForeground, selectBackground, selectBorder, selectForeground, selectListBackground, treeInactiveIndentGuidesStroke, menuBorder, menuForeground, menuBackground, menuSelectionForeground, menuSelectionBackground, menuSelectionBorder, menuSeparatorBackground, scrollbarShadow, scrollbarSliderActiveBackground, scrollbarSliderBackground, scrollbarSliderHoverBackground, listDropBetweenBackground, radioActiveBackground, radioActiveForeground, radioInactiveBackground, radioInactiveForeground, radioInactiveBorder, radioInactiveHoverBackground, radioActiveBorder } from '../common/colorRegistry.js';\nimport { Color } from '../../../base/common/color.js';\nfunction overrideStyles(override, styles) {\n  const result = {\n    ...styles\n  };\n  for (const key in override) {\n    const val = override[key];\n    result[key] = val !== undefined ? asCssVariable(val) : undefined;\n  }\n  return result;\n}\nexport const defaultKeybindingLabelStyles = {\n  keybindingLabelBackground: asCssVariable(keybindingLabelBackground),\n  keybindingLabelForeground: asCssVariable(keybindingLabelForeground),\n  keybindingLabelBorder: asCssVariable(keybindingLabelBorder),\n  keybindingLabelBottomBorder: asCssVariable(keybindingLabelBottomBorder),\n  keybindingLabelShadow: asCssVariable(widgetShadow)\n};\nexport const defaultButtonStyles = {\n  buttonForeground: asCssVariable(buttonForeground),\n  buttonSeparator: asCssVariable(buttonSeparator),\n  buttonBackground: asCssVariable(buttonBackground),\n  buttonHoverBackground: asCssVariable(buttonHoverBackground),\n  buttonSecondaryForeground: asCssVariable(buttonSecondaryForeground),\n  buttonSecondaryBackground: asCssVariable(buttonSecondaryBackground),\n  buttonSecondaryHoverBackground: asCssVariable(buttonSecondaryHoverBackground),\n  buttonBorder: asCssVariable(buttonBorder)\n};\nexport const defaultProgressBarStyles = {\n  progressBarBackground: asCssVariable(progressBarBackground)\n};\nexport const defaultToggleStyles = {\n  inputActiveOptionBorder: asCssVariable(inputActiveOptionBorder),\n  inputActiveOptionForeground: asCssVariable(inputActiveOptionForeground),\n  inputActiveOptionBackground: asCssVariable(inputActiveOptionBackground)\n};\nexport const defaultRadioStyles = {\n  activeForeground: asCssVariable(radioActiveForeground),\n  activeBackground: asCssVariable(radioActiveBackground),\n  activeBorder: asCssVariable(radioActiveBorder),\n  inactiveForeground: asCssVariable(radioInactiveForeground),\n  inactiveBackground: asCssVariable(radioInactiveBackground),\n  inactiveBorder: asCssVariable(radioInactiveBorder),\n  inactiveHoverBackground: asCssVariable(radioInactiveHoverBackground)\n};\nexport const defaultCheckboxStyles = {\n  checkboxBackground: asCssVariable(checkboxBackground),\n  checkboxBorder: asCssVariable(checkboxBorder),\n  checkboxForeground: asCssVariable(checkboxForeground)\n};\nexport const defaultDialogStyles = {\n  dialogBackground: asCssVariable(editorWidgetBackground),\n  dialogForeground: asCssVariable(editorWidgetForeground),\n  dialogShadow: asCssVariable(widgetShadow),\n  dialogBorder: asCssVariable(contrastBorder),\n  errorIconForeground: asCssVariable(problemsErrorIconForeground),\n  warningIconForeground: asCssVariable(problemsWarningIconForeground),\n  infoIconForeground: asCssVariable(problemsInfoIconForeground),\n  textLinkForeground: asCssVariable(textLinkForeground)\n};\nexport const defaultInputBoxStyles = {\n  inputBackground: asCssVariable(inputBackground),\n  inputForeground: asCssVariable(inputForeground),\n  inputBorder: asCssVariable(inputBorder),\n  inputValidationInfoBorder: asCssVariable(inputValidationInfoBorder),\n  inputValidationInfoBackground: asCssVariable(inputValidationInfoBackground),\n  inputValidationInfoForeground: asCssVariable(inputValidationInfoForeground),\n  inputValidationWarningBorder: asCssVariable(inputValidationWarningBorder),\n  inputValidationWarningBackground: asCssVariable(inputValidationWarningBackground),\n  inputValidationWarningForeground: asCssVariable(inputValidationWarningForeground),\n  inputValidationErrorBorder: asCssVariable(inputValidationErrorBorder),\n  inputValidationErrorBackground: asCssVariable(inputValidationErrorBackground),\n  inputValidationErrorForeground: asCssVariable(inputValidationErrorForeground)\n};\nexport const defaultFindWidgetStyles = {\n  listFilterWidgetBackground: asCssVariable(listFilterWidgetBackground),\n  listFilterWidgetOutline: asCssVariable(listFilterWidgetOutline),\n  listFilterWidgetNoMatchesOutline: asCssVariable(listFilterWidgetNoMatchesOutline),\n  listFilterWidgetShadow: asCssVariable(listFilterWidgetShadow),\n  inputBoxStyles: defaultInputBoxStyles,\n  toggleStyles: defaultToggleStyles\n};\nexport const defaultCountBadgeStyles = {\n  badgeBackground: asCssVariable(badgeBackground),\n  badgeForeground: asCssVariable(badgeForeground),\n  badgeBorder: asCssVariable(contrastBorder)\n};\nexport const defaultBreadcrumbsWidgetStyles = {\n  breadcrumbsBackground: asCssVariable(breadcrumbsBackground),\n  breadcrumbsForeground: asCssVariable(breadcrumbsForeground),\n  breadcrumbsHoverForeground: asCssVariable(breadcrumbsFocusForeground),\n  breadcrumbsFocusForeground: asCssVariable(breadcrumbsFocusForeground),\n  breadcrumbsFocusAndSelectionForeground: asCssVariable(breadcrumbsActiveSelectionForeground)\n};\nexport const defaultListStyles = {\n  listBackground: undefined,\n  listInactiveFocusForeground: undefined,\n  listFocusBackground: asCssVariable(listFocusBackground),\n  listFocusForeground: asCssVariable(listFocusForeground),\n  listFocusOutline: asCssVariable(listFocusOutline),\n  listActiveSelectionBackground: asCssVariable(listActiveSelectionBackground),\n  listActiveSelectionForeground: asCssVariable(listActiveSelectionForeground),\n  listActiveSelectionIconForeground: asCssVariable(listActiveSelectionIconForeground),\n  listFocusAndSelectionOutline: asCssVariable(listFocusAndSelectionOutline),\n  listFocusAndSelectionBackground: asCssVariable(listActiveSelectionBackground),\n  listFocusAndSelectionForeground: asCssVariable(listActiveSelectionForeground),\n  listInactiveSelectionBackground: asCssVariable(listInactiveSelectionBackground),\n  listInactiveSelectionIconForeground: asCssVariable(listInactiveSelectionIconForeground),\n  listInactiveSelectionForeground: asCssVariable(listInactiveSelectionForeground),\n  listInactiveFocusBackground: asCssVariable(listInactiveFocusBackground),\n  listInactiveFocusOutline: asCssVariable(listInactiveFocusOutline),\n  listHoverBackground: asCssVariable(listHoverBackground),\n  listHoverForeground: asCssVariable(listHoverForeground),\n  listDropOverBackground: asCssVariable(listDropOverBackground),\n  listDropBetweenBackground: asCssVariable(listDropBetweenBackground),\n  listSelectionOutline: asCssVariable(activeContrastBorder),\n  listHoverOutline: asCssVariable(activeContrastBorder),\n  treeIndentGuidesStroke: asCssVariable(treeIndentGuidesStroke),\n  treeInactiveIndentGuidesStroke: asCssVariable(treeInactiveIndentGuidesStroke),\n  treeStickyScrollBackground: undefined,\n  treeStickyScrollBorder: undefined,\n  treeStickyScrollShadow: asCssVariable(scrollbarShadow),\n  tableColumnsBorder: asCssVariable(tableColumnsBorder),\n  tableOddRowsBackgroundColor: asCssVariable(tableOddRowsBackgroundColor)\n};\nexport function getListStyles(override) {\n  return overrideStyles(override, defaultListStyles);\n}\nexport const defaultSelectBoxStyles = {\n  selectBackground: asCssVariable(selectBackground),\n  selectListBackground: asCssVariable(selectListBackground),\n  selectForeground: asCssVariable(selectForeground),\n  decoratorRightForeground: asCssVariable(pickerGroupForeground),\n  selectBorder: asCssVariable(selectBorder),\n  focusBorder: asCssVariable(focusBorder),\n  listFocusBackground: asCssVariable(quickInputListFocusBackground),\n  listInactiveSelectionIconForeground: asCssVariable(quickInputListFocusIconForeground),\n  listFocusForeground: asCssVariable(quickInputListFocusForeground),\n  listFocusOutline: asCssVariableWithDefault(activeContrastBorder, Color.transparent.toString()),\n  listHoverBackground: asCssVariable(listHoverBackground),\n  listHoverForeground: asCssVariable(listHoverForeground),\n  listHoverOutline: asCssVariable(activeContrastBorder),\n  selectListBorder: asCssVariable(editorWidgetBorder),\n  listBackground: undefined,\n  listActiveSelectionBackground: undefined,\n  listActiveSelectionForeground: undefined,\n  listActiveSelectionIconForeground: undefined,\n  listFocusAndSelectionBackground: undefined,\n  listDropOverBackground: undefined,\n  listDropBetweenBackground: undefined,\n  listInactiveSelectionBackground: undefined,\n  listInactiveSelectionForeground: undefined,\n  listInactiveFocusBackground: undefined,\n  listInactiveFocusOutline: undefined,\n  listSelectionOutline: undefined,\n  listFocusAndSelectionForeground: undefined,\n  listFocusAndSelectionOutline: undefined,\n  listInactiveFocusForeground: undefined,\n  tableColumnsBorder: undefined,\n  tableOddRowsBackgroundColor: undefined,\n  treeIndentGuidesStroke: undefined,\n  treeInactiveIndentGuidesStroke: undefined,\n  treeStickyScrollBackground: undefined,\n  treeStickyScrollBorder: undefined,\n  treeStickyScrollShadow: undefined\n};\nexport const defaultMenuStyles = {\n  shadowColor: asCssVariable(widgetShadow),\n  borderColor: asCssVariable(menuBorder),\n  foregroundColor: asCssVariable(menuForeground),\n  backgroundColor: asCssVariable(menuBackground),\n  selectionForegroundColor: asCssVariable(menuSelectionForeground),\n  selectionBackgroundColor: asCssVariable(menuSelectionBackground),\n  selectionBorderColor: asCssVariable(menuSelectionBorder),\n  separatorColor: asCssVariable(menuSeparatorBackground),\n  scrollbarShadow: asCssVariable(scrollbarShadow),\n  scrollbarSliderBackground: asCssVariable(scrollbarSliderBackground),\n  scrollbarSliderHoverBackground: asCssVariable(scrollbarSliderHoverBackground),\n  scrollbarSliderActiveBackground: asCssVariable(scrollbarSliderActiveBackground)\n};", "map": {"version": 3, "names": ["keybindingLabelBackground", "keybindingLabelBorder", "keybindingLabelBottomBorder", "keybindingLabelForeground", "asCssVariable", "widgetShadow", "buttonForeground", "buttonSeparator", "buttonBackground", "buttonHoverBackground", "buttonSecondaryForeground", "buttonSecondaryBackground", "buttonSecondaryHoverBackground", "buttonBorder", "progressBarBackground", "inputActiveOptionBorder", "inputActiveOptionForeground", "inputActiveOptionBackground", "editorWidgetBackground", "editorWidgetForeground", "contrastBorder", "checkboxBorder", "checkboxBackground", "checkboxForeground", "problemsErrorIconForeground", "problemsWarningIconForeground", "problemsInfoIconForeground", "inputBackground", "inputForeground", "inputBorder", "textLinkForeground", "inputValidationInfoBorder", "inputValidationInfoBackground", "inputValidationInfoForeground", "inputValidationWarningBorder", "inputValidationWarningBackground", "inputValidationWarningForeground", "inputValidationErrorBorder", "inputValidationErrorBackground", "inputValidationErrorForeground", "listFilterWidgetBackground", "listFilterWidgetNoMatchesOutline", "listFilterWidgetOutline", "listFilterWidgetShadow", "badgeBackground", "badgeForeground", "breadcrumbsBackground", "breadcrumbsForeground", "breadcrumbsFocusForeground", "breadcrumbsActiveSelectionForeground", "activeContrastBorder", "listActiveSelectionBackground", "listActiveSelectionForeground", "listActiveSelectionIconForeground", "listDropOverBackground", "listFocusAndSelectionOutline", "listFocusBackground", "listFocusForeground", "listFocusOutline", "listHoverBackground", "listHoverForeground", "listInactiveFocusBackground", "listInactiveFocusOutline", "listInactiveSelectionBackground", "listInactiveSelectionForeground", "listInactiveSelectionIconForeground", "tableColumnsBorder", "tableOddRowsBackgroundColor", "treeIndentGuidesStroke", "asCssVariableWithDefault", "editorW<PERSON>tBorder", "focusBorder", "pickerGroupForeground", "quickInputListFocusBackground", "quickInputListFocusForeground", "quickInputListFocusIconForeground", "selectBackground", "selectBorder", "selectForeground", "selectListBackground", "treeInactiveIndentGuidesStroke", "menuBorder", "menuForeground", "menuBackground", "menuSelectionForeground", "menuSelectionBackground", "menuSelectionBorder", "menuSeparatorBackground", "scrollbarShadow", "scrollbarSliderActiveBackground", "scrollbarSliderBackground", "scrollbarSliderHoverBackground", "listDropBetweenBackground", "radioActiveBackground", "radioActiveForeground", "radioInactiveBackground", "radioInactiveForeground", "radioInactiveBorder", "radioInactiveHoverBackground", "radioActiveBorder", "Color", "overrideStyles", "override", "styles", "result", "key", "val", "undefined", "defaultKeybindingLabelStyles", "keybindingLabelShadow", "defaultButtonStyles", "defaultProgressBarStyles", "defaultToggleStyles", "defaultRadioStyles", "activeForeground", "activeBackground", "activeBorder", "inactiveForeground", "inactiveBackground", "inactiveBorder", "inactiveHoverBackground", "defaultCheckboxStyles", "defaultDialogStyles", "dialogBackground", "dialogForeground", "dialogShadow", "dialogBorder", "errorIconForeground", "warningIconForeground", "infoIconForeground", "defaultInputBoxStyles", "defaultFindWidgetStyles", "inputBoxStyles", "toggleStyles", "defaultCountBadgeStyles", "badgeBorder", "defaultBreadcrumbsWidgetStyles", "breadcrumbsHoverForeground", "breadcrumbsFocusAndSelectionForeground", "defaultListStyles", "listBackground", "listInactiveFocusForeground", "listFocusAndSelectionBackground", "listFocusAndSelectionForeground", "listSelectionOutline", "listHoverOutline", "treeStickyScrollBackground", "treeStickyScrollBorder", "treeStickyScrollShadow", "getListStyles", "defaultSelectBoxStyles", "decoratorRightForeground", "transparent", "toString", "selectListBorder", "defaultMenuStyles", "shadowColor", "borderColor", "foregroundColor", "backgroundColor", "selectionForegroundColor", "selectionBackgroundColor", "selectionBorderColor", "separatorColor"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/platform/theme/browser/defaultStyles.js"], "sourcesContent": ["import { keybinding<PERSON>abelBackground, keybindingLabelBorder, keybindingLabelBottomBorder, keybindingLabelForeground, asCssVariable, widgetShadow, buttonForeground, buttonSeparator, buttonBackground, buttonHoverBackground, buttonSecondaryForeground, buttonSecondaryBackground, buttonSecondaryHoverBackground, buttonBorder, progressBarBackground, inputActiveOptionBorder, inputActiveOptionForeground, inputActiveOptionBackground, editorWidgetBackground, editorWidgetForeground, contrastBorder, checkboxBorder, checkboxBackground, checkboxForeground, problemsErrorIconForeground, problemsWarningIconForeground, problemsInfoIconForeground, inputBackground, inputForeground, inputBorder, textLinkForeground, inputValidationInfoBorder, inputValidationInfoBackground, inputValidationInfoForeground, inputValidationWarningBorder, inputValidationWarningBackground, inputValidationWarningForeground, inputValidationErrorBorder, inputValidationErrorBackground, inputValidationErrorForeground, listFilterWidgetBackground, listFilterWidgetNoMatchesOutline, listFilterWidgetOutline, listFilterWidgetShadow, badgeBackground, badgeForeground, breadcrumbsBackground, breadcrumbsForeground, breadcrumbsFocusForeground, breadcrumbsActiveSelectionForeground, activeContrastBorder, listActiveSelectionBackground, listActiveSelectionForeground, listActiveSelectionIconForeground, listDropOverBackground, listFocusAndSelectionOutline, listFocusBackground, listFocusForeground, listFocusOutline, listHoverBackground, listHoverForeground, listInactiveFocusBackground, listInactiveFocusOutline, listInactiveSelectionBackground, listInactiveSelectionForeground, listInactiveSelectionIconForeground, tableColumnsBorder, tableOddRowsBackgroundColor, treeIndentGuidesStroke, asCssVariableWithDefault, editorWidgetBorder, focusBorder, pickerGroupForeground, quickInputListFocusBackground, quickInputListFocusForeground, quickInputListFocusIconForeground, selectBackground, selectBorder, selectForeground, selectListBackground, treeInactiveIndentGuidesStroke, menuBorder, menuForeground, menuBackground, menuSelectionForeground, menuSelectionBackground, menuSelectionBorder, menuSeparatorBackground, scrollbarShadow, scrollbarSliderActiveBackground, scrollbarSliderBackground, scrollbarSliderHoverBackground, listDropBetweenBackground, radioActiveBackground, radioActiveForeground, radioInactiveBackground, radioInactiveForeground, radioInactiveBorder, radioInactiveHoverBackground, radioActiveBorder } from '../common/colorRegistry.js';\nimport { Color } from '../../../base/common/color.js';\nfunction overrideStyles(override, styles) {\n    const result = { ...styles };\n    for (const key in override) {\n        const val = override[key];\n        result[key] = val !== undefined ? asCssVariable(val) : undefined;\n    }\n    return result;\n}\nexport const defaultKeybindingLabelStyles = {\n    keybindingLabelBackground: asCssVariable(keybindingLabelBackground),\n    keybindingLabelForeground: asCssVariable(keybindingLabelForeground),\n    keybindingLabelBorder: asCssVariable(keybindingLabelBorder),\n    keybindingLabelBottomBorder: asCssVariable(keybindingLabelBottomBorder),\n    keybindingLabelShadow: asCssVariable(widgetShadow)\n};\nexport const defaultButtonStyles = {\n    buttonForeground: asCssVariable(buttonForeground),\n    buttonSeparator: asCssVariable(buttonSeparator),\n    buttonBackground: asCssVariable(buttonBackground),\n    buttonHoverBackground: asCssVariable(buttonHoverBackground),\n    buttonSecondaryForeground: asCssVariable(buttonSecondaryForeground),\n    buttonSecondaryBackground: asCssVariable(buttonSecondaryBackground),\n    buttonSecondaryHoverBackground: asCssVariable(buttonSecondaryHoverBackground),\n    buttonBorder: asCssVariable(buttonBorder),\n};\nexport const defaultProgressBarStyles = {\n    progressBarBackground: asCssVariable(progressBarBackground)\n};\nexport const defaultToggleStyles = {\n    inputActiveOptionBorder: asCssVariable(inputActiveOptionBorder),\n    inputActiveOptionForeground: asCssVariable(inputActiveOptionForeground),\n    inputActiveOptionBackground: asCssVariable(inputActiveOptionBackground)\n};\nexport const defaultRadioStyles = {\n    activeForeground: asCssVariable(radioActiveForeground),\n    activeBackground: asCssVariable(radioActiveBackground),\n    activeBorder: asCssVariable(radioActiveBorder),\n    inactiveForeground: asCssVariable(radioInactiveForeground),\n    inactiveBackground: asCssVariable(radioInactiveBackground),\n    inactiveBorder: asCssVariable(radioInactiveBorder),\n    inactiveHoverBackground: asCssVariable(radioInactiveHoverBackground),\n};\nexport const defaultCheckboxStyles = {\n    checkboxBackground: asCssVariable(checkboxBackground),\n    checkboxBorder: asCssVariable(checkboxBorder),\n    checkboxForeground: asCssVariable(checkboxForeground)\n};\nexport const defaultDialogStyles = {\n    dialogBackground: asCssVariable(editorWidgetBackground),\n    dialogForeground: asCssVariable(editorWidgetForeground),\n    dialogShadow: asCssVariable(widgetShadow),\n    dialogBorder: asCssVariable(contrastBorder),\n    errorIconForeground: asCssVariable(problemsErrorIconForeground),\n    warningIconForeground: asCssVariable(problemsWarningIconForeground),\n    infoIconForeground: asCssVariable(problemsInfoIconForeground),\n    textLinkForeground: asCssVariable(textLinkForeground)\n};\nexport const defaultInputBoxStyles = {\n    inputBackground: asCssVariable(inputBackground),\n    inputForeground: asCssVariable(inputForeground),\n    inputBorder: asCssVariable(inputBorder),\n    inputValidationInfoBorder: asCssVariable(inputValidationInfoBorder),\n    inputValidationInfoBackground: asCssVariable(inputValidationInfoBackground),\n    inputValidationInfoForeground: asCssVariable(inputValidationInfoForeground),\n    inputValidationWarningBorder: asCssVariable(inputValidationWarningBorder),\n    inputValidationWarningBackground: asCssVariable(inputValidationWarningBackground),\n    inputValidationWarningForeground: asCssVariable(inputValidationWarningForeground),\n    inputValidationErrorBorder: asCssVariable(inputValidationErrorBorder),\n    inputValidationErrorBackground: asCssVariable(inputValidationErrorBackground),\n    inputValidationErrorForeground: asCssVariable(inputValidationErrorForeground)\n};\nexport const defaultFindWidgetStyles = {\n    listFilterWidgetBackground: asCssVariable(listFilterWidgetBackground),\n    listFilterWidgetOutline: asCssVariable(listFilterWidgetOutline),\n    listFilterWidgetNoMatchesOutline: asCssVariable(listFilterWidgetNoMatchesOutline),\n    listFilterWidgetShadow: asCssVariable(listFilterWidgetShadow),\n    inputBoxStyles: defaultInputBoxStyles,\n    toggleStyles: defaultToggleStyles\n};\nexport const defaultCountBadgeStyles = {\n    badgeBackground: asCssVariable(badgeBackground),\n    badgeForeground: asCssVariable(badgeForeground),\n    badgeBorder: asCssVariable(contrastBorder)\n};\nexport const defaultBreadcrumbsWidgetStyles = {\n    breadcrumbsBackground: asCssVariable(breadcrumbsBackground),\n    breadcrumbsForeground: asCssVariable(breadcrumbsForeground),\n    breadcrumbsHoverForeground: asCssVariable(breadcrumbsFocusForeground),\n    breadcrumbsFocusForeground: asCssVariable(breadcrumbsFocusForeground),\n    breadcrumbsFocusAndSelectionForeground: asCssVariable(breadcrumbsActiveSelectionForeground)\n};\nexport const defaultListStyles = {\n    listBackground: undefined,\n    listInactiveFocusForeground: undefined,\n    listFocusBackground: asCssVariable(listFocusBackground),\n    listFocusForeground: asCssVariable(listFocusForeground),\n    listFocusOutline: asCssVariable(listFocusOutline),\n    listActiveSelectionBackground: asCssVariable(listActiveSelectionBackground),\n    listActiveSelectionForeground: asCssVariable(listActiveSelectionForeground),\n    listActiveSelectionIconForeground: asCssVariable(listActiveSelectionIconForeground),\n    listFocusAndSelectionOutline: asCssVariable(listFocusAndSelectionOutline),\n    listFocusAndSelectionBackground: asCssVariable(listActiveSelectionBackground),\n    listFocusAndSelectionForeground: asCssVariable(listActiveSelectionForeground),\n    listInactiveSelectionBackground: asCssVariable(listInactiveSelectionBackground),\n    listInactiveSelectionIconForeground: asCssVariable(listInactiveSelectionIconForeground),\n    listInactiveSelectionForeground: asCssVariable(listInactiveSelectionForeground),\n    listInactiveFocusBackground: asCssVariable(listInactiveFocusBackground),\n    listInactiveFocusOutline: asCssVariable(listInactiveFocusOutline),\n    listHoverBackground: asCssVariable(listHoverBackground),\n    listHoverForeground: asCssVariable(listHoverForeground),\n    listDropOverBackground: asCssVariable(listDropOverBackground),\n    listDropBetweenBackground: asCssVariable(listDropBetweenBackground),\n    listSelectionOutline: asCssVariable(activeContrastBorder),\n    listHoverOutline: asCssVariable(activeContrastBorder),\n    treeIndentGuidesStroke: asCssVariable(treeIndentGuidesStroke),\n    treeInactiveIndentGuidesStroke: asCssVariable(treeInactiveIndentGuidesStroke),\n    treeStickyScrollBackground: undefined,\n    treeStickyScrollBorder: undefined,\n    treeStickyScrollShadow: asCssVariable(scrollbarShadow),\n    tableColumnsBorder: asCssVariable(tableColumnsBorder),\n    tableOddRowsBackgroundColor: asCssVariable(tableOddRowsBackgroundColor),\n};\nexport function getListStyles(override) {\n    return overrideStyles(override, defaultListStyles);\n}\nexport const defaultSelectBoxStyles = {\n    selectBackground: asCssVariable(selectBackground),\n    selectListBackground: asCssVariable(selectListBackground),\n    selectForeground: asCssVariable(selectForeground),\n    decoratorRightForeground: asCssVariable(pickerGroupForeground),\n    selectBorder: asCssVariable(selectBorder),\n    focusBorder: asCssVariable(focusBorder),\n    listFocusBackground: asCssVariable(quickInputListFocusBackground),\n    listInactiveSelectionIconForeground: asCssVariable(quickInputListFocusIconForeground),\n    listFocusForeground: asCssVariable(quickInputListFocusForeground),\n    listFocusOutline: asCssVariableWithDefault(activeContrastBorder, Color.transparent.toString()),\n    listHoverBackground: asCssVariable(listHoverBackground),\n    listHoverForeground: asCssVariable(listHoverForeground),\n    listHoverOutline: asCssVariable(activeContrastBorder),\n    selectListBorder: asCssVariable(editorWidgetBorder),\n    listBackground: undefined,\n    listActiveSelectionBackground: undefined,\n    listActiveSelectionForeground: undefined,\n    listActiveSelectionIconForeground: undefined,\n    listFocusAndSelectionBackground: undefined,\n    listDropOverBackground: undefined,\n    listDropBetweenBackground: undefined,\n    listInactiveSelectionBackground: undefined,\n    listInactiveSelectionForeground: undefined,\n    listInactiveFocusBackground: undefined,\n    listInactiveFocusOutline: undefined,\n    listSelectionOutline: undefined,\n    listFocusAndSelectionForeground: undefined,\n    listFocusAndSelectionOutline: undefined,\n    listInactiveFocusForeground: undefined,\n    tableColumnsBorder: undefined,\n    tableOddRowsBackgroundColor: undefined,\n    treeIndentGuidesStroke: undefined,\n    treeInactiveIndentGuidesStroke: undefined,\n    treeStickyScrollBackground: undefined,\n    treeStickyScrollBorder: undefined,\n    treeStickyScrollShadow: undefined\n};\nexport const defaultMenuStyles = {\n    shadowColor: asCssVariable(widgetShadow),\n    borderColor: asCssVariable(menuBorder),\n    foregroundColor: asCssVariable(menuForeground),\n    backgroundColor: asCssVariable(menuBackground),\n    selectionForegroundColor: asCssVariable(menuSelectionForeground),\n    selectionBackgroundColor: asCssVariable(menuSelectionBackground),\n    selectionBorderColor: asCssVariable(menuSelectionBorder),\n    separatorColor: asCssVariable(menuSeparatorBackground),\n    scrollbarShadow: asCssVariable(scrollbarShadow),\n    scrollbarSliderBackground: asCssVariable(scrollbarSliderBackground),\n    scrollbarSliderHoverBackground: asCssVariable(scrollbarSliderHoverBackground),\n    scrollbarSliderActiveBackground: asCssVariable(scrollbarSliderActiveBackground)\n};\n"], "mappings": "AAAA,SAASA,yBAAyB,EAAEC,qBAAqB,EAAEC,2BAA2B,EAAEC,yBAAyB,EAAEC,aAAa,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,gBAAgB,EAAEC,qBAAqB,EAAEC,yBAAyB,EAAEC,yBAAyB,EAAEC,8BAA8B,EAAEC,YAAY,EAAEC,qBAAqB,EAAEC,uBAAuB,EAAEC,2BAA2B,EAAEC,2BAA2B,EAAEC,sBAAsB,EAAEC,sBAAsB,EAAEC,cAAc,EAAEC,cAAc,EAAEC,kBAAkB,EAAEC,kBAAkB,EAAEC,2BAA2B,EAAEC,6BAA6B,EAAEC,0BAA0B,EAAEC,eAAe,EAAEC,eAAe,EAAEC,WAAW,EAAEC,kBAAkB,EAAEC,yBAAyB,EAAEC,6BAA6B,EAAEC,6BAA6B,EAAEC,4BAA4B,EAAEC,gCAAgC,EAAEC,gCAAgC,EAAEC,0BAA0B,EAAEC,8BAA8B,EAAEC,8BAA8B,EAAEC,0BAA0B,EAAEC,gCAAgC,EAAEC,uBAAuB,EAAEC,sBAAsB,EAAEC,eAAe,EAAEC,eAAe,EAAEC,qBAAqB,EAAEC,qBAAqB,EAAEC,0BAA0B,EAAEC,oCAAoC,EAAEC,oBAAoB,EAAEC,6BAA6B,EAAEC,6BAA6B,EAAEC,iCAAiC,EAAEC,sBAAsB,EAAEC,4BAA4B,EAAEC,mBAAmB,EAAEC,mBAAmB,EAAEC,gBAAgB,EAAEC,mBAAmB,EAAEC,mBAAmB,EAAEC,2BAA2B,EAAEC,wBAAwB,EAAEC,+BAA+B,EAAEC,+BAA+B,EAAEC,mCAAmC,EAAEC,kBAAkB,EAAEC,2BAA2B,EAAEC,sBAAsB,EAAEC,wBAAwB,EAAEC,kBAAkB,EAAEC,WAAW,EAAEC,qBAAqB,EAAEC,6BAA6B,EAAEC,6BAA6B,EAAEC,iCAAiC,EAAEC,gBAAgB,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,oBAAoB,EAAEC,8BAA8B,EAAEC,UAAU,EAAEC,cAAc,EAAEC,cAAc,EAAEC,uBAAuB,EAAEC,uBAAuB,EAAEC,mBAAmB,EAAEC,uBAAuB,EAAEC,eAAe,EAAEC,+BAA+B,EAAEC,yBAAyB,EAAEC,8BAA8B,EAAEC,yBAAyB,EAAEC,qBAAqB,EAAEC,qBAAqB,EAAEC,uBAAuB,EAAEC,uBAAuB,EAAEC,mBAAmB,EAAEC,4BAA4B,EAAEC,iBAAiB,QAAQ,4BAA4B;AACt8E,SAASC,KAAK,QAAQ,+BAA+B;AACrD,SAASC,cAAcA,CAACC,QAAQ,EAAEC,MAAM,EAAE;EACtC,MAAMC,MAAM,GAAG;IAAE,GAAGD;EAAO,CAAC;EAC5B,KAAK,MAAME,GAAG,IAAIH,QAAQ,EAAE;IACxB,MAAMI,GAAG,GAAGJ,QAAQ,CAACG,GAAG,CAAC;IACzBD,MAAM,CAACC,GAAG,CAAC,GAAGC,GAAG,KAAKC,SAAS,GAAGvG,aAAa,CAACsG,GAAG,CAAC,GAAGC,SAAS;EACpE;EACA,OAAOH,MAAM;AACjB;AACA,OAAO,MAAMI,4BAA4B,GAAG;EACxC5G,yBAAyB,EAAEI,aAAa,CAACJ,yBAAyB,CAAC;EACnEG,yBAAyB,EAAEC,aAAa,CAACD,yBAAyB,CAAC;EACnEF,qBAAqB,EAAEG,aAAa,CAACH,qBAAqB,CAAC;EAC3DC,2BAA2B,EAAEE,aAAa,CAACF,2BAA2B,CAAC;EACvE2G,qBAAqB,EAAEzG,aAAa,CAACC,YAAY;AACrD,CAAC;AACD,OAAO,MAAMyG,mBAAmB,GAAG;EAC/BxG,gBAAgB,EAAEF,aAAa,CAACE,gBAAgB,CAAC;EACjDC,eAAe,EAAEH,aAAa,CAACG,eAAe,CAAC;EAC/CC,gBAAgB,EAAEJ,aAAa,CAACI,gBAAgB,CAAC;EACjDC,qBAAqB,EAAEL,aAAa,CAACK,qBAAqB,CAAC;EAC3DC,yBAAyB,EAAEN,aAAa,CAACM,yBAAyB,CAAC;EACnEC,yBAAyB,EAAEP,aAAa,CAACO,yBAAyB,CAAC;EACnEC,8BAA8B,EAAER,aAAa,CAACQ,8BAA8B,CAAC;EAC7EC,YAAY,EAAET,aAAa,CAACS,YAAY;AAC5C,CAAC;AACD,OAAO,MAAMkG,wBAAwB,GAAG;EACpCjG,qBAAqB,EAAEV,aAAa,CAACU,qBAAqB;AAC9D,CAAC;AACD,OAAO,MAAMkG,mBAAmB,GAAG;EAC/BjG,uBAAuB,EAAEX,aAAa,CAACW,uBAAuB,CAAC;EAC/DC,2BAA2B,EAAEZ,aAAa,CAACY,2BAA2B,CAAC;EACvEC,2BAA2B,EAAEb,aAAa,CAACa,2BAA2B;AAC1E,CAAC;AACD,OAAO,MAAMgG,kBAAkB,GAAG;EAC9BC,gBAAgB,EAAE9G,aAAa,CAAC0F,qBAAqB,CAAC;EACtDqB,gBAAgB,EAAE/G,aAAa,CAACyF,qBAAqB,CAAC;EACtDuB,YAAY,EAAEhH,aAAa,CAAC+F,iBAAiB,CAAC;EAC9CkB,kBAAkB,EAAEjH,aAAa,CAAC4F,uBAAuB,CAAC;EAC1DsB,kBAAkB,EAAElH,aAAa,CAAC2F,uBAAuB,CAAC;EAC1DwB,cAAc,EAAEnH,aAAa,CAAC6F,mBAAmB,CAAC;EAClDuB,uBAAuB,EAAEpH,aAAa,CAAC8F,4BAA4B;AACvE,CAAC;AACD,OAAO,MAAMuB,qBAAqB,GAAG;EACjCnG,kBAAkB,EAAElB,aAAa,CAACkB,kBAAkB,CAAC;EACrDD,cAAc,EAAEjB,aAAa,CAACiB,cAAc,CAAC;EAC7CE,kBAAkB,EAAEnB,aAAa,CAACmB,kBAAkB;AACxD,CAAC;AACD,OAAO,MAAMmG,mBAAmB,GAAG;EAC/BC,gBAAgB,EAAEvH,aAAa,CAACc,sBAAsB,CAAC;EACvD0G,gBAAgB,EAAExH,aAAa,CAACe,sBAAsB,CAAC;EACvD0G,YAAY,EAAEzH,aAAa,CAACC,YAAY,CAAC;EACzCyH,YAAY,EAAE1H,aAAa,CAACgB,cAAc,CAAC;EAC3C2G,mBAAmB,EAAE3H,aAAa,CAACoB,2BAA2B,CAAC;EAC/DwG,qBAAqB,EAAE5H,aAAa,CAACqB,6BAA6B,CAAC;EACnEwG,kBAAkB,EAAE7H,aAAa,CAACsB,0BAA0B,CAAC;EAC7DI,kBAAkB,EAAE1B,aAAa,CAAC0B,kBAAkB;AACxD,CAAC;AACD,OAAO,MAAMoG,qBAAqB,GAAG;EACjCvG,eAAe,EAAEvB,aAAa,CAACuB,eAAe,CAAC;EAC/CC,eAAe,EAAExB,aAAa,CAACwB,eAAe,CAAC;EAC/CC,WAAW,EAAEzB,aAAa,CAACyB,WAAW,CAAC;EACvCE,yBAAyB,EAAE3B,aAAa,CAAC2B,yBAAyB,CAAC;EACnEC,6BAA6B,EAAE5B,aAAa,CAAC4B,6BAA6B,CAAC;EAC3EC,6BAA6B,EAAE7B,aAAa,CAAC6B,6BAA6B,CAAC;EAC3EC,4BAA4B,EAAE9B,aAAa,CAAC8B,4BAA4B,CAAC;EACzEC,gCAAgC,EAAE/B,aAAa,CAAC+B,gCAAgC,CAAC;EACjFC,gCAAgC,EAAEhC,aAAa,CAACgC,gCAAgC,CAAC;EACjFC,0BAA0B,EAAEjC,aAAa,CAACiC,0BAA0B,CAAC;EACrEC,8BAA8B,EAAElC,aAAa,CAACkC,8BAA8B,CAAC;EAC7EC,8BAA8B,EAAEnC,aAAa,CAACmC,8BAA8B;AAChF,CAAC;AACD,OAAO,MAAM4F,uBAAuB,GAAG;EACnC3F,0BAA0B,EAAEpC,aAAa,CAACoC,0BAA0B,CAAC;EACrEE,uBAAuB,EAAEtC,aAAa,CAACsC,uBAAuB,CAAC;EAC/DD,gCAAgC,EAAErC,aAAa,CAACqC,gCAAgC,CAAC;EACjFE,sBAAsB,EAAEvC,aAAa,CAACuC,sBAAsB,CAAC;EAC7DyF,cAAc,EAAEF,qBAAqB;EACrCG,YAAY,EAAErB;AAClB,CAAC;AACD,OAAO,MAAMsB,uBAAuB,GAAG;EACnC1F,eAAe,EAAExC,aAAa,CAACwC,eAAe,CAAC;EAC/CC,eAAe,EAAEzC,aAAa,CAACyC,eAAe,CAAC;EAC/C0F,WAAW,EAAEnI,aAAa,CAACgB,cAAc;AAC7C,CAAC;AACD,OAAO,MAAMoH,8BAA8B,GAAG;EAC1C1F,qBAAqB,EAAE1C,aAAa,CAAC0C,qBAAqB,CAAC;EAC3DC,qBAAqB,EAAE3C,aAAa,CAAC2C,qBAAqB,CAAC;EAC3D0F,0BAA0B,EAAErI,aAAa,CAAC4C,0BAA0B,CAAC;EACrEA,0BAA0B,EAAE5C,aAAa,CAAC4C,0BAA0B,CAAC;EACrE0F,sCAAsC,EAAEtI,aAAa,CAAC6C,oCAAoC;AAC9F,CAAC;AACD,OAAO,MAAM0F,iBAAiB,GAAG;EAC7BC,cAAc,EAAEjC,SAAS;EACzBkC,2BAA2B,EAAElC,SAAS;EACtCnD,mBAAmB,EAAEpD,aAAa,CAACoD,mBAAmB,CAAC;EACvDC,mBAAmB,EAAErD,aAAa,CAACqD,mBAAmB,CAAC;EACvDC,gBAAgB,EAAEtD,aAAa,CAACsD,gBAAgB,CAAC;EACjDP,6BAA6B,EAAE/C,aAAa,CAAC+C,6BAA6B,CAAC;EAC3EC,6BAA6B,EAAEhD,aAAa,CAACgD,6BAA6B,CAAC;EAC3EC,iCAAiC,EAAEjD,aAAa,CAACiD,iCAAiC,CAAC;EACnFE,4BAA4B,EAAEnD,aAAa,CAACmD,4BAA4B,CAAC;EACzEuF,+BAA+B,EAAE1I,aAAa,CAAC+C,6BAA6B,CAAC;EAC7E4F,+BAA+B,EAAE3I,aAAa,CAACgD,6BAA6B,CAAC;EAC7EW,+BAA+B,EAAE3D,aAAa,CAAC2D,+BAA+B,CAAC;EAC/EE,mCAAmC,EAAE7D,aAAa,CAAC6D,mCAAmC,CAAC;EACvFD,+BAA+B,EAAE5D,aAAa,CAAC4D,+BAA+B,CAAC;EAC/EH,2BAA2B,EAAEzD,aAAa,CAACyD,2BAA2B,CAAC;EACvEC,wBAAwB,EAAE1D,aAAa,CAAC0D,wBAAwB,CAAC;EACjEH,mBAAmB,EAAEvD,aAAa,CAACuD,mBAAmB,CAAC;EACvDC,mBAAmB,EAAExD,aAAa,CAACwD,mBAAmB,CAAC;EACvDN,sBAAsB,EAAElD,aAAa,CAACkD,sBAAsB,CAAC;EAC7DsC,yBAAyB,EAAExF,aAAa,CAACwF,yBAAyB,CAAC;EACnEoD,oBAAoB,EAAE5I,aAAa,CAAC8C,oBAAoB,CAAC;EACzD+F,gBAAgB,EAAE7I,aAAa,CAAC8C,oBAAoB,CAAC;EACrDkB,sBAAsB,EAAEhE,aAAa,CAACgE,sBAAsB,CAAC;EAC7DY,8BAA8B,EAAE5E,aAAa,CAAC4E,8BAA8B,CAAC;EAC7EkE,0BAA0B,EAAEvC,SAAS;EACrCwC,sBAAsB,EAAExC,SAAS;EACjCyC,sBAAsB,EAAEhJ,aAAa,CAACoF,eAAe,CAAC;EACtDtB,kBAAkB,EAAE9D,aAAa,CAAC8D,kBAAkB,CAAC;EACrDC,2BAA2B,EAAE/D,aAAa,CAAC+D,2BAA2B;AAC1E,CAAC;AACD,OAAO,SAASkF,aAAaA,CAAC/C,QAAQ,EAAE;EACpC,OAAOD,cAAc,CAACC,QAAQ,EAAEqC,iBAAiB,CAAC;AACtD;AACA,OAAO,MAAMW,sBAAsB,GAAG;EAClC1E,gBAAgB,EAAExE,aAAa,CAACwE,gBAAgB,CAAC;EACjDG,oBAAoB,EAAE3E,aAAa,CAAC2E,oBAAoB,CAAC;EACzDD,gBAAgB,EAAE1E,aAAa,CAAC0E,gBAAgB,CAAC;EACjDyE,wBAAwB,EAAEnJ,aAAa,CAACoE,qBAAqB,CAAC;EAC9DK,YAAY,EAAEzE,aAAa,CAACyE,YAAY,CAAC;EACzCN,WAAW,EAAEnE,aAAa,CAACmE,WAAW,CAAC;EACvCf,mBAAmB,EAAEpD,aAAa,CAACqE,6BAA6B,CAAC;EACjER,mCAAmC,EAAE7D,aAAa,CAACuE,iCAAiC,CAAC;EACrFlB,mBAAmB,EAAErD,aAAa,CAACsE,6BAA6B,CAAC;EACjEhB,gBAAgB,EAAEW,wBAAwB,CAACnB,oBAAoB,EAAEkD,KAAK,CAACoD,WAAW,CAACC,QAAQ,CAAC,CAAC,CAAC;EAC9F9F,mBAAmB,EAAEvD,aAAa,CAACuD,mBAAmB,CAAC;EACvDC,mBAAmB,EAAExD,aAAa,CAACwD,mBAAmB,CAAC;EACvDqF,gBAAgB,EAAE7I,aAAa,CAAC8C,oBAAoB,CAAC;EACrDwG,gBAAgB,EAAEtJ,aAAa,CAACkE,kBAAkB,CAAC;EACnDsE,cAAc,EAAEjC,SAAS;EACzBxD,6BAA6B,EAAEwD,SAAS;EACxCvD,6BAA6B,EAAEuD,SAAS;EACxCtD,iCAAiC,EAAEsD,SAAS;EAC5CmC,+BAA+B,EAAEnC,SAAS;EAC1CrD,sBAAsB,EAAEqD,SAAS;EACjCf,yBAAyB,EAAEe,SAAS;EACpC5C,+BAA+B,EAAE4C,SAAS;EAC1C3C,+BAA+B,EAAE2C,SAAS;EAC1C9C,2BAA2B,EAAE8C,SAAS;EACtC7C,wBAAwB,EAAE6C,SAAS;EACnCqC,oBAAoB,EAAErC,SAAS;EAC/BoC,+BAA+B,EAAEpC,SAAS;EAC1CpD,4BAA4B,EAAEoD,SAAS;EACvCkC,2BAA2B,EAAElC,SAAS;EACtCzC,kBAAkB,EAAEyC,SAAS;EAC7BxC,2BAA2B,EAAEwC,SAAS;EACtCvC,sBAAsB,EAAEuC,SAAS;EACjC3B,8BAA8B,EAAE2B,SAAS;EACzCuC,0BAA0B,EAAEvC,SAAS;EACrCwC,sBAAsB,EAAExC,SAAS;EACjCyC,sBAAsB,EAAEzC;AAC5B,CAAC;AACD,OAAO,MAAMgD,iBAAiB,GAAG;EAC7BC,WAAW,EAAExJ,aAAa,CAACC,YAAY,CAAC;EACxCwJ,WAAW,EAAEzJ,aAAa,CAAC6E,UAAU,CAAC;EACtC6E,eAAe,EAAE1J,aAAa,CAAC8E,cAAc,CAAC;EAC9C6E,eAAe,EAAE3J,aAAa,CAAC+E,cAAc,CAAC;EAC9C6E,wBAAwB,EAAE5J,aAAa,CAACgF,uBAAuB,CAAC;EAChE6E,wBAAwB,EAAE7J,aAAa,CAACiF,uBAAuB,CAAC;EAChE6E,oBAAoB,EAAE9J,aAAa,CAACkF,mBAAmB,CAAC;EACxD6E,cAAc,EAAE/J,aAAa,CAACmF,uBAAuB,CAAC;EACtDC,eAAe,EAAEpF,aAAa,CAACoF,eAAe,CAAC;EAC/CE,yBAAyB,EAAEtF,aAAa,CAACsF,yBAAyB,CAAC;EACnEC,8BAA8B,EAAEvF,aAAa,CAACuF,8BAA8B,CAAC;EAC7EF,+BAA+B,EAAErF,aAAa,CAACqF,+BAA+B;AAClF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}