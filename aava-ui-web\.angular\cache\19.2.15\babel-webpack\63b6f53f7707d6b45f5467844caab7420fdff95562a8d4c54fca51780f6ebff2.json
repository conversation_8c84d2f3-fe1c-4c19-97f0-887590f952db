{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { equals, groupAdjacentBy } from '../../../../base/common/arrays.js';\nimport { assertFn, checkAdjacentItems } from '../../../../base/common/assert.js';\nimport { LineRange } from '../../core/lineRange.js';\nimport { OffsetRange } from '../../core/offsetRange.js';\nimport { Range } from '../../core/range.js';\nimport { DateTimeout, InfiniteTimeout, SequenceDiff } from './algorithms/diffAlgorithm.js';\nimport { DynamicProgrammingDiffing } from './algorithms/dynamicProgrammingDiffing.js';\nimport { MyersDiffAlgorithm } from './algorithms/myersDiffAlgorithm.js';\nimport { computeMovedLines } from './computeMovedLines.js';\nimport { extendDiffsToEntireWordIfAppropriate, optimizeSequenceDiffs, removeShortMatches, removeVeryShortMatchingLinesBetweenDiffs, removeVeryShortMatchingTextBetweenLongDiffs } from './heuristicSequenceOptimizations.js';\nimport { LineSequence } from './lineSequence.js';\nimport { LinesSliceCharSequence } from './linesSliceCharSequence.js';\nimport { LinesDiff, MovedText } from '../linesDiffComputer.js';\nimport { DetailedLineRangeMapping, LineRangeMapping, RangeMapping } from '../rangeMapping.js';\nexport class DefaultLinesDiffComputer {\n  constructor() {\n    this.dynamicProgrammingDiffing = new DynamicProgrammingDiffing();\n    this.myersDiffingAlgorithm = new MyersDiffAlgorithm();\n  }\n  computeDiff(originalLines, modifiedLines, options) {\n    if (originalLines.length <= 1 && equals(originalLines, modifiedLines, (a, b) => a === b)) {\n      return new LinesDiff([], [], false);\n    }\n    if (originalLines.length === 1 && originalLines[0].length === 0 || modifiedLines.length === 1 && modifiedLines[0].length === 0) {\n      return new LinesDiff([new DetailedLineRangeMapping(new LineRange(1, originalLines.length + 1), new LineRange(1, modifiedLines.length + 1), [new RangeMapping(new Range(1, 1, originalLines.length, originalLines[originalLines.length - 1].length + 1), new Range(1, 1, modifiedLines.length, modifiedLines[modifiedLines.length - 1].length + 1))])], [], false);\n    }\n    const timeout = options.maxComputationTimeMs === 0 ? InfiniteTimeout.instance : new DateTimeout(options.maxComputationTimeMs);\n    const considerWhitespaceChanges = !options.ignoreTrimWhitespace;\n    const perfectHashes = new Map();\n    function getOrCreateHash(text) {\n      let hash = perfectHashes.get(text);\n      if (hash === undefined) {\n        hash = perfectHashes.size;\n        perfectHashes.set(text, hash);\n      }\n      return hash;\n    }\n    const originalLinesHashes = originalLines.map(l => getOrCreateHash(l.trim()));\n    const modifiedLinesHashes = modifiedLines.map(l => getOrCreateHash(l.trim()));\n    const sequence1 = new LineSequence(originalLinesHashes, originalLines);\n    const sequence2 = new LineSequence(modifiedLinesHashes, modifiedLines);\n    const lineAlignmentResult = (() => {\n      if (sequence1.length + sequence2.length < 1700) {\n        // Use the improved algorithm for small files\n        return this.dynamicProgrammingDiffing.compute(sequence1, sequence2, timeout, (offset1, offset2) => originalLines[offset1] === modifiedLines[offset2] ? modifiedLines[offset2].length === 0 ? 0.1 : 1 + Math.log(1 + modifiedLines[offset2].length) : 0.99);\n      }\n      return this.myersDiffingAlgorithm.compute(sequence1, sequence2, timeout);\n    })();\n    let lineAlignments = lineAlignmentResult.diffs;\n    let hitTimeout = lineAlignmentResult.hitTimeout;\n    lineAlignments = optimizeSequenceDiffs(sequence1, sequence2, lineAlignments);\n    lineAlignments = removeVeryShortMatchingLinesBetweenDiffs(sequence1, sequence2, lineAlignments);\n    const alignments = [];\n    const scanForWhitespaceChanges = equalLinesCount => {\n      if (!considerWhitespaceChanges) {\n        return;\n      }\n      for (let i = 0; i < equalLinesCount; i++) {\n        const seq1Offset = seq1LastStart + i;\n        const seq2Offset = seq2LastStart + i;\n        if (originalLines[seq1Offset] !== modifiedLines[seq2Offset]) {\n          // This is because of whitespace changes, diff these lines\n          const characterDiffs = this.refineDiff(originalLines, modifiedLines, new SequenceDiff(new OffsetRange(seq1Offset, seq1Offset + 1), new OffsetRange(seq2Offset, seq2Offset + 1)), timeout, considerWhitespaceChanges);\n          for (const a of characterDiffs.mappings) {\n            alignments.push(a);\n          }\n          if (characterDiffs.hitTimeout) {\n            hitTimeout = true;\n          }\n        }\n      }\n    };\n    let seq1LastStart = 0;\n    let seq2LastStart = 0;\n    for (const diff of lineAlignments) {\n      assertFn(() => diff.seq1Range.start - seq1LastStart === diff.seq2Range.start - seq2LastStart);\n      const equalLinesCount = diff.seq1Range.start - seq1LastStart;\n      scanForWhitespaceChanges(equalLinesCount);\n      seq1LastStart = diff.seq1Range.endExclusive;\n      seq2LastStart = diff.seq2Range.endExclusive;\n      const characterDiffs = this.refineDiff(originalLines, modifiedLines, diff, timeout, considerWhitespaceChanges);\n      if (characterDiffs.hitTimeout) {\n        hitTimeout = true;\n      }\n      for (const a of characterDiffs.mappings) {\n        alignments.push(a);\n      }\n    }\n    scanForWhitespaceChanges(originalLines.length - seq1LastStart);\n    const changes = lineRangeMappingFromRangeMappings(alignments, originalLines, modifiedLines);\n    let moves = [];\n    if (options.computeMoves) {\n      moves = this.computeMoves(changes, originalLines, modifiedLines, originalLinesHashes, modifiedLinesHashes, timeout, considerWhitespaceChanges);\n    }\n    // Make sure all ranges are valid\n    assertFn(() => {\n      function validatePosition(pos, lines) {\n        if (pos.lineNumber < 1 || pos.lineNumber > lines.length) {\n          return false;\n        }\n        const line = lines[pos.lineNumber - 1];\n        if (pos.column < 1 || pos.column > line.length + 1) {\n          return false;\n        }\n        return true;\n      }\n      function validateRange(range, lines) {\n        if (range.startLineNumber < 1 || range.startLineNumber > lines.length + 1) {\n          return false;\n        }\n        if (range.endLineNumberExclusive < 1 || range.endLineNumberExclusive > lines.length + 1) {\n          return false;\n        }\n        return true;\n      }\n      for (const c of changes) {\n        if (!c.innerChanges) {\n          return false;\n        }\n        for (const ic of c.innerChanges) {\n          const valid = validatePosition(ic.modifiedRange.getStartPosition(), modifiedLines) && validatePosition(ic.modifiedRange.getEndPosition(), modifiedLines) && validatePosition(ic.originalRange.getStartPosition(), originalLines) && validatePosition(ic.originalRange.getEndPosition(), originalLines);\n          if (!valid) {\n            return false;\n          }\n        }\n        if (!validateRange(c.modified, modifiedLines) || !validateRange(c.original, originalLines)) {\n          return false;\n        }\n      }\n      return true;\n    });\n    return new LinesDiff(changes, moves, hitTimeout);\n  }\n  computeMoves(changes, originalLines, modifiedLines, hashedOriginalLines, hashedModifiedLines, timeout, considerWhitespaceChanges) {\n    const moves = computeMovedLines(changes, originalLines, modifiedLines, hashedOriginalLines, hashedModifiedLines, timeout);\n    const movesWithDiffs = moves.map(m => {\n      const moveChanges = this.refineDiff(originalLines, modifiedLines, new SequenceDiff(m.original.toOffsetRange(), m.modified.toOffsetRange()), timeout, considerWhitespaceChanges);\n      const mappings = lineRangeMappingFromRangeMappings(moveChanges.mappings, originalLines, modifiedLines, true);\n      return new MovedText(m, mappings);\n    });\n    return movesWithDiffs;\n  }\n  refineDiff(originalLines, modifiedLines, diff, timeout, considerWhitespaceChanges) {\n    const lineRangeMapping = toLineRangeMapping(diff);\n    const rangeMapping = lineRangeMapping.toRangeMapping2(originalLines, modifiedLines);\n    const slice1 = new LinesSliceCharSequence(originalLines, rangeMapping.originalRange, considerWhitespaceChanges);\n    const slice2 = new LinesSliceCharSequence(modifiedLines, rangeMapping.modifiedRange, considerWhitespaceChanges);\n    const diffResult = slice1.length + slice2.length < 500 ? this.dynamicProgrammingDiffing.compute(slice1, slice2, timeout) : this.myersDiffingAlgorithm.compute(slice1, slice2, timeout);\n    const check = false;\n    let diffs = diffResult.diffs;\n    if (check) {\n      SequenceDiff.assertSorted(diffs);\n    }\n    diffs = optimizeSequenceDiffs(slice1, slice2, diffs);\n    if (check) {\n      SequenceDiff.assertSorted(diffs);\n    }\n    diffs = extendDiffsToEntireWordIfAppropriate(slice1, slice2, diffs);\n    if (check) {\n      SequenceDiff.assertSorted(diffs);\n    }\n    diffs = removeShortMatches(slice1, slice2, diffs);\n    if (check) {\n      SequenceDiff.assertSorted(diffs);\n    }\n    diffs = removeVeryShortMatchingTextBetweenLongDiffs(slice1, slice2, diffs);\n    if (check) {\n      SequenceDiff.assertSorted(diffs);\n    }\n    const result = diffs.map(d => new RangeMapping(slice1.translateRange(d.seq1Range), slice2.translateRange(d.seq2Range)));\n    if (check) {\n      RangeMapping.assertSorted(result);\n    }\n    // Assert: result applied on original should be the same as diff applied to original\n    return {\n      mappings: result,\n      hitTimeout: diffResult.hitTimeout\n    };\n  }\n}\nexport function lineRangeMappingFromRangeMappings(alignments, originalLines, modifiedLines, dontAssertStartLine = false) {\n  const changes = [];\n  for (const g of groupAdjacentBy(alignments.map(a => getLineRangeMapping(a, originalLines, modifiedLines)), (a1, a2) => a1.original.overlapOrTouch(a2.original) || a1.modified.overlapOrTouch(a2.modified))) {\n    const first = g[0];\n    const last = g[g.length - 1];\n    changes.push(new DetailedLineRangeMapping(first.original.join(last.original), first.modified.join(last.modified), g.map(a => a.innerChanges[0])));\n  }\n  assertFn(() => {\n    if (!dontAssertStartLine && changes.length > 0) {\n      if (changes[0].modified.startLineNumber !== changes[0].original.startLineNumber) {\n        return false;\n      }\n      if (modifiedLines.length - changes[changes.length - 1].modified.endLineNumberExclusive !== originalLines.length - changes[changes.length - 1].original.endLineNumberExclusive) {\n        return false;\n      }\n    }\n    return checkAdjacentItems(changes, (m1, m2) => m2.original.startLineNumber - m1.original.endLineNumberExclusive === m2.modified.startLineNumber - m1.modified.endLineNumberExclusive &&\n    // There has to be an unchanged line in between (otherwise both diffs should have been joined)\n    m1.original.endLineNumberExclusive < m2.original.startLineNumber && m1.modified.endLineNumberExclusive < m2.modified.startLineNumber);\n  });\n  return changes;\n}\nexport function getLineRangeMapping(rangeMapping, originalLines, modifiedLines) {\n  let lineStartDelta = 0;\n  let lineEndDelta = 0;\n  // rangeMapping describes the edit that replaces `rangeMapping.originalRange` with `newText := getText(modifiedLines, rangeMapping.modifiedRange)`.\n  // original: ]xxx \\n <- this line is not modified\n  // modified: ]xx  \\n\n  if (rangeMapping.modifiedRange.endColumn === 1 && rangeMapping.originalRange.endColumn === 1 && rangeMapping.originalRange.startLineNumber + lineStartDelta <= rangeMapping.originalRange.endLineNumber && rangeMapping.modifiedRange.startLineNumber + lineStartDelta <= rangeMapping.modifiedRange.endLineNumber) {\n    // We can only do this if the range is not empty yet\n    lineEndDelta = -1;\n  }\n  // original: xxx[ \\n <- this line is not modified\n  // modified: xxx[ \\n\n  if (rangeMapping.modifiedRange.startColumn - 1 >= modifiedLines[rangeMapping.modifiedRange.startLineNumber - 1].length && rangeMapping.originalRange.startColumn - 1 >= originalLines[rangeMapping.originalRange.startLineNumber - 1].length && rangeMapping.originalRange.startLineNumber <= rangeMapping.originalRange.endLineNumber + lineEndDelta && rangeMapping.modifiedRange.startLineNumber <= rangeMapping.modifiedRange.endLineNumber + lineEndDelta) {\n    // We can only do this if the range is not empty yet\n    lineStartDelta = 1;\n  }\n  const originalLineRange = new LineRange(rangeMapping.originalRange.startLineNumber + lineStartDelta, rangeMapping.originalRange.endLineNumber + 1 + lineEndDelta);\n  const modifiedLineRange = new LineRange(rangeMapping.modifiedRange.startLineNumber + lineStartDelta, rangeMapping.modifiedRange.endLineNumber + 1 + lineEndDelta);\n  return new DetailedLineRangeMapping(originalLineRange, modifiedLineRange, [rangeMapping]);\n}\nfunction toLineRangeMapping(sequenceDiff) {\n  return new LineRangeMapping(new LineRange(sequenceDiff.seq1Range.start + 1, sequenceDiff.seq1Range.endExclusive + 1), new LineRange(sequenceDiff.seq2Range.start + 1, sequenceDiff.seq2Range.endExclusive + 1));\n}", "map": {"version": 3, "names": ["equals", "groupAdjacentBy", "assertFn", "checkAdjacentItems", "LineRange", "OffsetRange", "Range", "DateTimeout", "InfiniteTimeout", "SequenceDiff", "DynamicProgrammingDiffing", "MyersDiffAlgorithm", "computeMovedLines", "extendDiffsToEntireWordIfAppropriate", "optimizeSequenceDiffs", "removeShortMatches", "removeVeryShortMatchingLinesBetweenDiffs", "removeVeryShortMatchingTextBetweenLongDiffs", "LineSequence", "LinesSliceCharSequence", "LinesDiff", "MovedText", "DetailedLineRangeMapping", "LineRangeMapping", "RangeMapping", "DefaultLinesDiffComputer", "constructor", "dynamicProgrammingDiffing", "myersDiffingAlgorithm", "computeDiff", "originalLines", "modifiedLines", "options", "length", "a", "b", "timeout", "maxComputationTimeMs", "instance", "considerWhitespaceChanges", "ignoreTrimWhitespace", "perfectHashes", "Map", "getOrCreateHash", "text", "hash", "get", "undefined", "size", "set", "originalLinesHashes", "map", "l", "trim", "modifiedLinesHashes", "sequence1", "sequence2", "lineAlignmentResult", "compute", "offset1", "offset2", "Math", "log", "lineAlignments", "diffs", "hitTimeout", "alignments", "scanForWhitespaceChanges", "equalLinesCount", "i", "seq1Offset", "seq1LastStart", "seq2Offset", "seq2LastStart", "characterDiffs", "refineDiff", "mappings", "push", "diff", "seq1Range", "start", "seq2Range", "endExclusive", "changes", "lineRangeMappingFromRangeMappings", "moves", "computeMoves", "validatePosition", "pos", "lines", "lineNumber", "line", "column", "validate<PERSON><PERSON><PERSON>", "range", "startLineNumber", "endLineNumberExclusive", "c", "innerChanges", "ic", "valid", "modifiedRange", "getStartPosition", "getEndPosition", "originalRange", "modified", "original", "hashedOriginalLines", "hashedModifiedLines", "movesWithDiffs", "m", "moveChanges", "toOffsetRange", "lineRangeMapping", "toLineRangeMapping", "rangeMapping", "toRangeMapping2", "slice1", "slice2", "diffResult", "check", "assertSorted", "result", "d", "translateRange", "dontAssertStartLine", "g", "getLineRangeMapping", "a1", "a2", "overlapOrTouch", "first", "last", "join", "m1", "m2", "lineStartDelta", "lineEndDelta", "endColumn", "endLineNumber", "startColumn", "originalLineRange", "modifiedLineRange", "sequenceDiff"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/diff/defaultLinesDiffComputer/defaultLinesDiffComputer.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { equals, groupAdjacentBy } from '../../../../base/common/arrays.js';\nimport { assertFn, checkAdjacentItems } from '../../../../base/common/assert.js';\nimport { LineRange } from '../../core/lineRange.js';\nimport { OffsetRange } from '../../core/offsetRange.js';\nimport { Range } from '../../core/range.js';\nimport { DateTimeout, InfiniteTimeout, SequenceDiff } from './algorithms/diffAlgorithm.js';\nimport { DynamicProgrammingDiffing } from './algorithms/dynamicProgrammingDiffing.js';\nimport { MyersDiffAlgorithm } from './algorithms/myersDiffAlgorithm.js';\nimport { computeMovedLines } from './computeMovedLines.js';\nimport { extendDiffsToEntireWordIfAppropriate, optimizeSequenceDiffs, removeShortMatches, removeVeryShortMatchingLinesBetweenDiffs, removeVeryShortMatchingTextBetweenLongDiffs } from './heuristicSequenceOptimizations.js';\nimport { LineSequence } from './lineSequence.js';\nimport { LinesSliceCharSequence } from './linesSliceCharSequence.js';\nimport { LinesDiff, MovedText } from '../linesDiffComputer.js';\nimport { DetailedLineRangeMapping, LineRangeMapping, RangeMapping } from '../rangeMapping.js';\nexport class DefaultLinesDiffComputer {\n    constructor() {\n        this.dynamicProgrammingDiffing = new DynamicProgrammingDiffing();\n        this.myersDiffingAlgorithm = new MyersDiffAlgorithm();\n    }\n    computeDiff(originalLines, modifiedLines, options) {\n        if (originalLines.length <= 1 && equals(originalLines, modifiedLines, (a, b) => a === b)) {\n            return new LinesDiff([], [], false);\n        }\n        if (originalLines.length === 1 && originalLines[0].length === 0 || modifiedLines.length === 1 && modifiedLines[0].length === 0) {\n            return new LinesDiff([\n                new DetailedLineRangeMapping(new LineRange(1, originalLines.length + 1), new LineRange(1, modifiedLines.length + 1), [\n                    new RangeMapping(new Range(1, 1, originalLines.length, originalLines[originalLines.length - 1].length + 1), new Range(1, 1, modifiedLines.length, modifiedLines[modifiedLines.length - 1].length + 1))\n                ])\n            ], [], false);\n        }\n        const timeout = options.maxComputationTimeMs === 0 ? InfiniteTimeout.instance : new DateTimeout(options.maxComputationTimeMs);\n        const considerWhitespaceChanges = !options.ignoreTrimWhitespace;\n        const perfectHashes = new Map();\n        function getOrCreateHash(text) {\n            let hash = perfectHashes.get(text);\n            if (hash === undefined) {\n                hash = perfectHashes.size;\n                perfectHashes.set(text, hash);\n            }\n            return hash;\n        }\n        const originalLinesHashes = originalLines.map((l) => getOrCreateHash(l.trim()));\n        const modifiedLinesHashes = modifiedLines.map((l) => getOrCreateHash(l.trim()));\n        const sequence1 = new LineSequence(originalLinesHashes, originalLines);\n        const sequence2 = new LineSequence(modifiedLinesHashes, modifiedLines);\n        const lineAlignmentResult = (() => {\n            if (sequence1.length + sequence2.length < 1700) {\n                // Use the improved algorithm for small files\n                return this.dynamicProgrammingDiffing.compute(sequence1, sequence2, timeout, (offset1, offset2) => originalLines[offset1] === modifiedLines[offset2]\n                    ? modifiedLines[offset2].length === 0\n                        ? 0.1\n                        : 1 + Math.log(1 + modifiedLines[offset2].length)\n                    : 0.99);\n            }\n            return this.myersDiffingAlgorithm.compute(sequence1, sequence2, timeout);\n        })();\n        let lineAlignments = lineAlignmentResult.diffs;\n        let hitTimeout = lineAlignmentResult.hitTimeout;\n        lineAlignments = optimizeSequenceDiffs(sequence1, sequence2, lineAlignments);\n        lineAlignments = removeVeryShortMatchingLinesBetweenDiffs(sequence1, sequence2, lineAlignments);\n        const alignments = [];\n        const scanForWhitespaceChanges = (equalLinesCount) => {\n            if (!considerWhitespaceChanges) {\n                return;\n            }\n            for (let i = 0; i < equalLinesCount; i++) {\n                const seq1Offset = seq1LastStart + i;\n                const seq2Offset = seq2LastStart + i;\n                if (originalLines[seq1Offset] !== modifiedLines[seq2Offset]) {\n                    // This is because of whitespace changes, diff these lines\n                    const characterDiffs = this.refineDiff(originalLines, modifiedLines, new SequenceDiff(new OffsetRange(seq1Offset, seq1Offset + 1), new OffsetRange(seq2Offset, seq2Offset + 1)), timeout, considerWhitespaceChanges);\n                    for (const a of characterDiffs.mappings) {\n                        alignments.push(a);\n                    }\n                    if (characterDiffs.hitTimeout) {\n                        hitTimeout = true;\n                    }\n                }\n            }\n        };\n        let seq1LastStart = 0;\n        let seq2LastStart = 0;\n        for (const diff of lineAlignments) {\n            assertFn(() => diff.seq1Range.start - seq1LastStart === diff.seq2Range.start - seq2LastStart);\n            const equalLinesCount = diff.seq1Range.start - seq1LastStart;\n            scanForWhitespaceChanges(equalLinesCount);\n            seq1LastStart = diff.seq1Range.endExclusive;\n            seq2LastStart = diff.seq2Range.endExclusive;\n            const characterDiffs = this.refineDiff(originalLines, modifiedLines, diff, timeout, considerWhitespaceChanges);\n            if (characterDiffs.hitTimeout) {\n                hitTimeout = true;\n            }\n            for (const a of characterDiffs.mappings) {\n                alignments.push(a);\n            }\n        }\n        scanForWhitespaceChanges(originalLines.length - seq1LastStart);\n        const changes = lineRangeMappingFromRangeMappings(alignments, originalLines, modifiedLines);\n        let moves = [];\n        if (options.computeMoves) {\n            moves = this.computeMoves(changes, originalLines, modifiedLines, originalLinesHashes, modifiedLinesHashes, timeout, considerWhitespaceChanges);\n        }\n        // Make sure all ranges are valid\n        assertFn(() => {\n            function validatePosition(pos, lines) {\n                if (pos.lineNumber < 1 || pos.lineNumber > lines.length) {\n                    return false;\n                }\n                const line = lines[pos.lineNumber - 1];\n                if (pos.column < 1 || pos.column > line.length + 1) {\n                    return false;\n                }\n                return true;\n            }\n            function validateRange(range, lines) {\n                if (range.startLineNumber < 1 || range.startLineNumber > lines.length + 1) {\n                    return false;\n                }\n                if (range.endLineNumberExclusive < 1 || range.endLineNumberExclusive > lines.length + 1) {\n                    return false;\n                }\n                return true;\n            }\n            for (const c of changes) {\n                if (!c.innerChanges) {\n                    return false;\n                }\n                for (const ic of c.innerChanges) {\n                    const valid = validatePosition(ic.modifiedRange.getStartPosition(), modifiedLines) && validatePosition(ic.modifiedRange.getEndPosition(), modifiedLines) &&\n                        validatePosition(ic.originalRange.getStartPosition(), originalLines) && validatePosition(ic.originalRange.getEndPosition(), originalLines);\n                    if (!valid) {\n                        return false;\n                    }\n                }\n                if (!validateRange(c.modified, modifiedLines) || !validateRange(c.original, originalLines)) {\n                    return false;\n                }\n            }\n            return true;\n        });\n        return new LinesDiff(changes, moves, hitTimeout);\n    }\n    computeMoves(changes, originalLines, modifiedLines, hashedOriginalLines, hashedModifiedLines, timeout, considerWhitespaceChanges) {\n        const moves = computeMovedLines(changes, originalLines, modifiedLines, hashedOriginalLines, hashedModifiedLines, timeout);\n        const movesWithDiffs = moves.map(m => {\n            const moveChanges = this.refineDiff(originalLines, modifiedLines, new SequenceDiff(m.original.toOffsetRange(), m.modified.toOffsetRange()), timeout, considerWhitespaceChanges);\n            const mappings = lineRangeMappingFromRangeMappings(moveChanges.mappings, originalLines, modifiedLines, true);\n            return new MovedText(m, mappings);\n        });\n        return movesWithDiffs;\n    }\n    refineDiff(originalLines, modifiedLines, diff, timeout, considerWhitespaceChanges) {\n        const lineRangeMapping = toLineRangeMapping(diff);\n        const rangeMapping = lineRangeMapping.toRangeMapping2(originalLines, modifiedLines);\n        const slice1 = new LinesSliceCharSequence(originalLines, rangeMapping.originalRange, considerWhitespaceChanges);\n        const slice2 = new LinesSliceCharSequence(modifiedLines, rangeMapping.modifiedRange, considerWhitespaceChanges);\n        const diffResult = slice1.length + slice2.length < 500\n            ? this.dynamicProgrammingDiffing.compute(slice1, slice2, timeout)\n            : this.myersDiffingAlgorithm.compute(slice1, slice2, timeout);\n        const check = false;\n        let diffs = diffResult.diffs;\n        if (check) {\n            SequenceDiff.assertSorted(diffs);\n        }\n        diffs = optimizeSequenceDiffs(slice1, slice2, diffs);\n        if (check) {\n            SequenceDiff.assertSorted(diffs);\n        }\n        diffs = extendDiffsToEntireWordIfAppropriate(slice1, slice2, diffs);\n        if (check) {\n            SequenceDiff.assertSorted(diffs);\n        }\n        diffs = removeShortMatches(slice1, slice2, diffs);\n        if (check) {\n            SequenceDiff.assertSorted(diffs);\n        }\n        diffs = removeVeryShortMatchingTextBetweenLongDiffs(slice1, slice2, diffs);\n        if (check) {\n            SequenceDiff.assertSorted(diffs);\n        }\n        const result = diffs.map((d) => new RangeMapping(slice1.translateRange(d.seq1Range), slice2.translateRange(d.seq2Range)));\n        if (check) {\n            RangeMapping.assertSorted(result);\n        }\n        // Assert: result applied on original should be the same as diff applied to original\n        return {\n            mappings: result,\n            hitTimeout: diffResult.hitTimeout,\n        };\n    }\n}\nexport function lineRangeMappingFromRangeMappings(alignments, originalLines, modifiedLines, dontAssertStartLine = false) {\n    const changes = [];\n    for (const g of groupAdjacentBy(alignments.map(a => getLineRangeMapping(a, originalLines, modifiedLines)), (a1, a2) => a1.original.overlapOrTouch(a2.original)\n        || a1.modified.overlapOrTouch(a2.modified))) {\n        const first = g[0];\n        const last = g[g.length - 1];\n        changes.push(new DetailedLineRangeMapping(first.original.join(last.original), first.modified.join(last.modified), g.map(a => a.innerChanges[0])));\n    }\n    assertFn(() => {\n        if (!dontAssertStartLine && changes.length > 0) {\n            if (changes[0].modified.startLineNumber !== changes[0].original.startLineNumber) {\n                return false;\n            }\n            if (modifiedLines.length - changes[changes.length - 1].modified.endLineNumberExclusive !== originalLines.length - changes[changes.length - 1].original.endLineNumberExclusive) {\n                return false;\n            }\n        }\n        return checkAdjacentItems(changes, (m1, m2) => m2.original.startLineNumber - m1.original.endLineNumberExclusive === m2.modified.startLineNumber - m1.modified.endLineNumberExclusive &&\n            // There has to be an unchanged line in between (otherwise both diffs should have been joined)\n            m1.original.endLineNumberExclusive < m2.original.startLineNumber &&\n            m1.modified.endLineNumberExclusive < m2.modified.startLineNumber);\n    });\n    return changes;\n}\nexport function getLineRangeMapping(rangeMapping, originalLines, modifiedLines) {\n    let lineStartDelta = 0;\n    let lineEndDelta = 0;\n    // rangeMapping describes the edit that replaces `rangeMapping.originalRange` with `newText := getText(modifiedLines, rangeMapping.modifiedRange)`.\n    // original: ]xxx \\n <- this line is not modified\n    // modified: ]xx  \\n\n    if (rangeMapping.modifiedRange.endColumn === 1 && rangeMapping.originalRange.endColumn === 1\n        && rangeMapping.originalRange.startLineNumber + lineStartDelta <= rangeMapping.originalRange.endLineNumber\n        && rangeMapping.modifiedRange.startLineNumber + lineStartDelta <= rangeMapping.modifiedRange.endLineNumber) {\n        // We can only do this if the range is not empty yet\n        lineEndDelta = -1;\n    }\n    // original: xxx[ \\n <- this line is not modified\n    // modified: xxx[ \\n\n    if (rangeMapping.modifiedRange.startColumn - 1 >= modifiedLines[rangeMapping.modifiedRange.startLineNumber - 1].length\n        && rangeMapping.originalRange.startColumn - 1 >= originalLines[rangeMapping.originalRange.startLineNumber - 1].length\n        && rangeMapping.originalRange.startLineNumber <= rangeMapping.originalRange.endLineNumber + lineEndDelta\n        && rangeMapping.modifiedRange.startLineNumber <= rangeMapping.modifiedRange.endLineNumber + lineEndDelta) {\n        // We can only do this if the range is not empty yet\n        lineStartDelta = 1;\n    }\n    const originalLineRange = new LineRange(rangeMapping.originalRange.startLineNumber + lineStartDelta, rangeMapping.originalRange.endLineNumber + 1 + lineEndDelta);\n    const modifiedLineRange = new LineRange(rangeMapping.modifiedRange.startLineNumber + lineStartDelta, rangeMapping.modifiedRange.endLineNumber + 1 + lineEndDelta);\n    return new DetailedLineRangeMapping(originalLineRange, modifiedLineRange, [rangeMapping]);\n}\nfunction toLineRangeMapping(sequenceDiff) {\n    return new LineRangeMapping(new LineRange(sequenceDiff.seq1Range.start + 1, sequenceDiff.seq1Range.endExclusive + 1), new LineRange(sequenceDiff.seq2Range.start + 1, sequenceDiff.seq2Range.endExclusive + 1));\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,MAAM,EAAEC,eAAe,QAAQ,mCAAmC;AAC3E,SAASC,QAAQ,EAAEC,kBAAkB,QAAQ,mCAAmC;AAChF,SAASC,SAAS,QAAQ,yBAAyB;AACnD,SAASC,WAAW,QAAQ,2BAA2B;AACvD,SAASC,KAAK,QAAQ,qBAAqB;AAC3C,SAASC,WAAW,EAAEC,eAAe,EAAEC,YAAY,QAAQ,+BAA+B;AAC1F,SAASC,yBAAyB,QAAQ,2CAA2C;AACrF,SAASC,kBAAkB,QAAQ,oCAAoC;AACvE,SAASC,iBAAiB,QAAQ,wBAAwB;AAC1D,SAASC,oCAAoC,EAAEC,qBAAqB,EAAEC,kBAAkB,EAAEC,wCAAwC,EAAEC,2CAA2C,QAAQ,qCAAqC;AAC5N,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,sBAAsB,QAAQ,6BAA6B;AACpE,SAASC,SAAS,EAAEC,SAAS,QAAQ,yBAAyB;AAC9D,SAASC,wBAAwB,EAAEC,gBAAgB,EAAEC,YAAY,QAAQ,oBAAoB;AAC7F,OAAO,MAAMC,wBAAwB,CAAC;EAClCC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,yBAAyB,GAAG,IAAIjB,yBAAyB,CAAC,CAAC;IAChE,IAAI,CAACkB,qBAAqB,GAAG,IAAIjB,kBAAkB,CAAC,CAAC;EACzD;EACAkB,WAAWA,CAACC,aAAa,EAAEC,aAAa,EAAEC,OAAO,EAAE;IAC/C,IAAIF,aAAa,CAACG,MAAM,IAAI,CAAC,IAAIjC,MAAM,CAAC8B,aAAa,EAAEC,aAAa,EAAE,CAACG,CAAC,EAAEC,CAAC,KAAKD,CAAC,KAAKC,CAAC,CAAC,EAAE;MACtF,OAAO,IAAIf,SAAS,CAAC,EAAE,EAAE,EAAE,EAAE,KAAK,CAAC;IACvC;IACA,IAAIU,aAAa,CAACG,MAAM,KAAK,CAAC,IAAIH,aAAa,CAAC,CAAC,CAAC,CAACG,MAAM,KAAK,CAAC,IAAIF,aAAa,CAACE,MAAM,KAAK,CAAC,IAAIF,aAAa,CAAC,CAAC,CAAC,CAACE,MAAM,KAAK,CAAC,EAAE;MAC5H,OAAO,IAAIb,SAAS,CAAC,CACjB,IAAIE,wBAAwB,CAAC,IAAIlB,SAAS,CAAC,CAAC,EAAE0B,aAAa,CAACG,MAAM,GAAG,CAAC,CAAC,EAAE,IAAI7B,SAAS,CAAC,CAAC,EAAE2B,aAAa,CAACE,MAAM,GAAG,CAAC,CAAC,EAAE,CACjH,IAAIT,YAAY,CAAC,IAAIlB,KAAK,CAAC,CAAC,EAAE,CAAC,EAAEwB,aAAa,CAACG,MAAM,EAAEH,aAAa,CAACA,aAAa,CAACG,MAAM,GAAG,CAAC,CAAC,CAACA,MAAM,GAAG,CAAC,CAAC,EAAE,IAAI3B,KAAK,CAAC,CAAC,EAAE,CAAC,EAAEyB,aAAa,CAACE,MAAM,EAAEF,aAAa,CAACA,aAAa,CAACE,MAAM,GAAG,CAAC,CAAC,CAACA,MAAM,GAAG,CAAC,CAAC,CAAC,CACzM,CAAC,CACL,EAAE,EAAE,EAAE,KAAK,CAAC;IACjB;IACA,MAAMG,OAAO,GAAGJ,OAAO,CAACK,oBAAoB,KAAK,CAAC,GAAG7B,eAAe,CAAC8B,QAAQ,GAAG,IAAI/B,WAAW,CAACyB,OAAO,CAACK,oBAAoB,CAAC;IAC7H,MAAME,yBAAyB,GAAG,CAACP,OAAO,CAACQ,oBAAoB;IAC/D,MAAMC,aAAa,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC/B,SAASC,eAAeA,CAACC,IAAI,EAAE;MAC3B,IAAIC,IAAI,GAAGJ,aAAa,CAACK,GAAG,CAACF,IAAI,CAAC;MAClC,IAAIC,IAAI,KAAKE,SAAS,EAAE;QACpBF,IAAI,GAAGJ,aAAa,CAACO,IAAI;QACzBP,aAAa,CAACQ,GAAG,CAACL,IAAI,EAAEC,IAAI,CAAC;MACjC;MACA,OAAOA,IAAI;IACf;IACA,MAAMK,mBAAmB,GAAGpB,aAAa,CAACqB,GAAG,CAAEC,CAAC,IAAKT,eAAe,CAACS,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC;IAC/E,MAAMC,mBAAmB,GAAGvB,aAAa,CAACoB,GAAG,CAAEC,CAAC,IAAKT,eAAe,CAACS,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC;IAC/E,MAAME,SAAS,GAAG,IAAIrC,YAAY,CAACgC,mBAAmB,EAAEpB,aAAa,CAAC;IACtE,MAAM0B,SAAS,GAAG,IAAItC,YAAY,CAACoC,mBAAmB,EAAEvB,aAAa,CAAC;IACtE,MAAM0B,mBAAmB,GAAG,CAAC,MAAM;MAC/B,IAAIF,SAAS,CAACtB,MAAM,GAAGuB,SAAS,CAACvB,MAAM,GAAG,IAAI,EAAE;QAC5C;QACA,OAAO,IAAI,CAACN,yBAAyB,CAAC+B,OAAO,CAACH,SAAS,EAAEC,SAAS,EAAEpB,OAAO,EAAE,CAACuB,OAAO,EAAEC,OAAO,KAAK9B,aAAa,CAAC6B,OAAO,CAAC,KAAK5B,aAAa,CAAC6B,OAAO,CAAC,GAC9I7B,aAAa,CAAC6B,OAAO,CAAC,CAAC3B,MAAM,KAAK,CAAC,GAC/B,GAAG,GACH,CAAC,GAAG4B,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG/B,aAAa,CAAC6B,OAAO,CAAC,CAAC3B,MAAM,CAAC,GACnD,IAAI,CAAC;MACf;MACA,OAAO,IAAI,CAACL,qBAAqB,CAAC8B,OAAO,CAACH,SAAS,EAAEC,SAAS,EAAEpB,OAAO,CAAC;IAC5E,CAAC,EAAE,CAAC;IACJ,IAAI2B,cAAc,GAAGN,mBAAmB,CAACO,KAAK;IAC9C,IAAIC,UAAU,GAAGR,mBAAmB,CAACQ,UAAU;IAC/CF,cAAc,GAAGjD,qBAAqB,CAACyC,SAAS,EAAEC,SAAS,EAAEO,cAAc,CAAC;IAC5EA,cAAc,GAAG/C,wCAAwC,CAACuC,SAAS,EAAEC,SAAS,EAAEO,cAAc,CAAC;IAC/F,MAAMG,UAAU,GAAG,EAAE;IACrB,MAAMC,wBAAwB,GAAIC,eAAe,IAAK;MAClD,IAAI,CAAC7B,yBAAyB,EAAE;QAC5B;MACJ;MACA,KAAK,IAAI8B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,eAAe,EAAEC,CAAC,EAAE,EAAE;QACtC,MAAMC,UAAU,GAAGC,aAAa,GAAGF,CAAC;QACpC,MAAMG,UAAU,GAAGC,aAAa,GAAGJ,CAAC;QACpC,IAAIvC,aAAa,CAACwC,UAAU,CAAC,KAAKvC,aAAa,CAACyC,UAAU,CAAC,EAAE;UACzD;UACA,MAAME,cAAc,GAAG,IAAI,CAACC,UAAU,CAAC7C,aAAa,EAAEC,aAAa,EAAE,IAAItB,YAAY,CAAC,IAAIJ,WAAW,CAACiE,UAAU,EAAEA,UAAU,GAAG,CAAC,CAAC,EAAE,IAAIjE,WAAW,CAACmE,UAAU,EAAEA,UAAU,GAAG,CAAC,CAAC,CAAC,EAAEpC,OAAO,EAAEG,yBAAyB,CAAC;UACpN,KAAK,MAAML,CAAC,IAAIwC,cAAc,CAACE,QAAQ,EAAE;YACrCV,UAAU,CAACW,IAAI,CAAC3C,CAAC,CAAC;UACtB;UACA,IAAIwC,cAAc,CAACT,UAAU,EAAE;YAC3BA,UAAU,GAAG,IAAI;UACrB;QACJ;MACJ;IACJ,CAAC;IACD,IAAIM,aAAa,GAAG,CAAC;IACrB,IAAIE,aAAa,GAAG,CAAC;IACrB,KAAK,MAAMK,IAAI,IAAIf,cAAc,EAAE;MAC/B7D,QAAQ,CAAC,MAAM4E,IAAI,CAACC,SAAS,CAACC,KAAK,GAAGT,aAAa,KAAKO,IAAI,CAACG,SAAS,CAACD,KAAK,GAAGP,aAAa,CAAC;MAC7F,MAAML,eAAe,GAAGU,IAAI,CAACC,SAAS,CAACC,KAAK,GAAGT,aAAa;MAC5DJ,wBAAwB,CAACC,eAAe,CAAC;MACzCG,aAAa,GAAGO,IAAI,CAACC,SAAS,CAACG,YAAY;MAC3CT,aAAa,GAAGK,IAAI,CAACG,SAAS,CAACC,YAAY;MAC3C,MAAMR,cAAc,GAAG,IAAI,CAACC,UAAU,CAAC7C,aAAa,EAAEC,aAAa,EAAE+C,IAAI,EAAE1C,OAAO,EAAEG,yBAAyB,CAAC;MAC9G,IAAImC,cAAc,CAACT,UAAU,EAAE;QAC3BA,UAAU,GAAG,IAAI;MACrB;MACA,KAAK,MAAM/B,CAAC,IAAIwC,cAAc,CAACE,QAAQ,EAAE;QACrCV,UAAU,CAACW,IAAI,CAAC3C,CAAC,CAAC;MACtB;IACJ;IACAiC,wBAAwB,CAACrC,aAAa,CAACG,MAAM,GAAGsC,aAAa,CAAC;IAC9D,MAAMY,OAAO,GAAGC,iCAAiC,CAAClB,UAAU,EAAEpC,aAAa,EAAEC,aAAa,CAAC;IAC3F,IAAIsD,KAAK,GAAG,EAAE;IACd,IAAIrD,OAAO,CAACsD,YAAY,EAAE;MACtBD,KAAK,GAAG,IAAI,CAACC,YAAY,CAACH,OAAO,EAAErD,aAAa,EAAEC,aAAa,EAAEmB,mBAAmB,EAAEI,mBAAmB,EAAElB,OAAO,EAAEG,yBAAyB,CAAC;IAClJ;IACA;IACArC,QAAQ,CAAC,MAAM;MACX,SAASqF,gBAAgBA,CAACC,GAAG,EAAEC,KAAK,EAAE;QAClC,IAAID,GAAG,CAACE,UAAU,GAAG,CAAC,IAAIF,GAAG,CAACE,UAAU,GAAGD,KAAK,CAACxD,MAAM,EAAE;UACrD,OAAO,KAAK;QAChB;QACA,MAAM0D,IAAI,GAAGF,KAAK,CAACD,GAAG,CAACE,UAAU,GAAG,CAAC,CAAC;QACtC,IAAIF,GAAG,CAACI,MAAM,GAAG,CAAC,IAAIJ,GAAG,CAACI,MAAM,GAAGD,IAAI,CAAC1D,MAAM,GAAG,CAAC,EAAE;UAChD,OAAO,KAAK;QAChB;QACA,OAAO,IAAI;MACf;MACA,SAAS4D,aAAaA,CAACC,KAAK,EAAEL,KAAK,EAAE;QACjC,IAAIK,KAAK,CAACC,eAAe,GAAG,CAAC,IAAID,KAAK,CAACC,eAAe,GAAGN,KAAK,CAACxD,MAAM,GAAG,CAAC,EAAE;UACvE,OAAO,KAAK;QAChB;QACA,IAAI6D,KAAK,CAACE,sBAAsB,GAAG,CAAC,IAAIF,KAAK,CAACE,sBAAsB,GAAGP,KAAK,CAACxD,MAAM,GAAG,CAAC,EAAE;UACrF,OAAO,KAAK;QAChB;QACA,OAAO,IAAI;MACf;MACA,KAAK,MAAMgE,CAAC,IAAId,OAAO,EAAE;QACrB,IAAI,CAACc,CAAC,CAACC,YAAY,EAAE;UACjB,OAAO,KAAK;QAChB;QACA,KAAK,MAAMC,EAAE,IAAIF,CAAC,CAACC,YAAY,EAAE;UAC7B,MAAME,KAAK,GAAGb,gBAAgB,CAACY,EAAE,CAACE,aAAa,CAACC,gBAAgB,CAAC,CAAC,EAAEvE,aAAa,CAAC,IAAIwD,gBAAgB,CAACY,EAAE,CAACE,aAAa,CAACE,cAAc,CAAC,CAAC,EAAExE,aAAa,CAAC,IACpJwD,gBAAgB,CAACY,EAAE,CAACK,aAAa,CAACF,gBAAgB,CAAC,CAAC,EAAExE,aAAa,CAAC,IAAIyD,gBAAgB,CAACY,EAAE,CAACK,aAAa,CAACD,cAAc,CAAC,CAAC,EAAEzE,aAAa,CAAC;UAC9I,IAAI,CAACsE,KAAK,EAAE;YACR,OAAO,KAAK;UAChB;QACJ;QACA,IAAI,CAACP,aAAa,CAACI,CAAC,CAACQ,QAAQ,EAAE1E,aAAa,CAAC,IAAI,CAAC8D,aAAa,CAACI,CAAC,CAACS,QAAQ,EAAE5E,aAAa,CAAC,EAAE;UACxF,OAAO,KAAK;QAChB;MACJ;MACA,OAAO,IAAI;IACf,CAAC,CAAC;IACF,OAAO,IAAIV,SAAS,CAAC+D,OAAO,EAAEE,KAAK,EAAEpB,UAAU,CAAC;EACpD;EACAqB,YAAYA,CAACH,OAAO,EAAErD,aAAa,EAAEC,aAAa,EAAE4E,mBAAmB,EAAEC,mBAAmB,EAAExE,OAAO,EAAEG,yBAAyB,EAAE;IAC9H,MAAM8C,KAAK,GAAGzE,iBAAiB,CAACuE,OAAO,EAAErD,aAAa,EAAEC,aAAa,EAAE4E,mBAAmB,EAAEC,mBAAmB,EAAExE,OAAO,CAAC;IACzH,MAAMyE,cAAc,GAAGxB,KAAK,CAAClC,GAAG,CAAC2D,CAAC,IAAI;MAClC,MAAMC,WAAW,GAAG,IAAI,CAACpC,UAAU,CAAC7C,aAAa,EAAEC,aAAa,EAAE,IAAItB,YAAY,CAACqG,CAAC,CAACJ,QAAQ,CAACM,aAAa,CAAC,CAAC,EAAEF,CAAC,CAACL,QAAQ,CAACO,aAAa,CAAC,CAAC,CAAC,EAAE5E,OAAO,EAAEG,yBAAyB,CAAC;MAC/K,MAAMqC,QAAQ,GAAGQ,iCAAiC,CAAC2B,WAAW,CAACnC,QAAQ,EAAE9C,aAAa,EAAEC,aAAa,EAAE,IAAI,CAAC;MAC5G,OAAO,IAAIV,SAAS,CAACyF,CAAC,EAAElC,QAAQ,CAAC;IACrC,CAAC,CAAC;IACF,OAAOiC,cAAc;EACzB;EACAlC,UAAUA,CAAC7C,aAAa,EAAEC,aAAa,EAAE+C,IAAI,EAAE1C,OAAO,EAAEG,yBAAyB,EAAE;IAC/E,MAAM0E,gBAAgB,GAAGC,kBAAkB,CAACpC,IAAI,CAAC;IACjD,MAAMqC,YAAY,GAAGF,gBAAgB,CAACG,eAAe,CAACtF,aAAa,EAAEC,aAAa,CAAC;IACnF,MAAMsF,MAAM,GAAG,IAAIlG,sBAAsB,CAACW,aAAa,EAAEqF,YAAY,CAACX,aAAa,EAAEjE,yBAAyB,CAAC;IAC/G,MAAM+E,MAAM,GAAG,IAAInG,sBAAsB,CAACY,aAAa,EAAEoF,YAAY,CAACd,aAAa,EAAE9D,yBAAyB,CAAC;IAC/G,MAAMgF,UAAU,GAAGF,MAAM,CAACpF,MAAM,GAAGqF,MAAM,CAACrF,MAAM,GAAG,GAAG,GAChD,IAAI,CAACN,yBAAyB,CAAC+B,OAAO,CAAC2D,MAAM,EAAEC,MAAM,EAAElF,OAAO,CAAC,GAC/D,IAAI,CAACR,qBAAqB,CAAC8B,OAAO,CAAC2D,MAAM,EAAEC,MAAM,EAAElF,OAAO,CAAC;IACjE,MAAMoF,KAAK,GAAG,KAAK;IACnB,IAAIxD,KAAK,GAAGuD,UAAU,CAACvD,KAAK;IAC5B,IAAIwD,KAAK,EAAE;MACP/G,YAAY,CAACgH,YAAY,CAACzD,KAAK,CAAC;IACpC;IACAA,KAAK,GAAGlD,qBAAqB,CAACuG,MAAM,EAAEC,MAAM,EAAEtD,KAAK,CAAC;IACpD,IAAIwD,KAAK,EAAE;MACP/G,YAAY,CAACgH,YAAY,CAACzD,KAAK,CAAC;IACpC;IACAA,KAAK,GAAGnD,oCAAoC,CAACwG,MAAM,EAAEC,MAAM,EAAEtD,KAAK,CAAC;IACnE,IAAIwD,KAAK,EAAE;MACP/G,YAAY,CAACgH,YAAY,CAACzD,KAAK,CAAC;IACpC;IACAA,KAAK,GAAGjD,kBAAkB,CAACsG,MAAM,EAAEC,MAAM,EAAEtD,KAAK,CAAC;IACjD,IAAIwD,KAAK,EAAE;MACP/G,YAAY,CAACgH,YAAY,CAACzD,KAAK,CAAC;IACpC;IACAA,KAAK,GAAG/C,2CAA2C,CAACoG,MAAM,EAAEC,MAAM,EAAEtD,KAAK,CAAC;IAC1E,IAAIwD,KAAK,EAAE;MACP/G,YAAY,CAACgH,YAAY,CAACzD,KAAK,CAAC;IACpC;IACA,MAAM0D,MAAM,GAAG1D,KAAK,CAACb,GAAG,CAAEwE,CAAC,IAAK,IAAInG,YAAY,CAAC6F,MAAM,CAACO,cAAc,CAACD,CAAC,CAAC5C,SAAS,CAAC,EAAEuC,MAAM,CAACM,cAAc,CAACD,CAAC,CAAC1C,SAAS,CAAC,CAAC,CAAC;IACzH,IAAIuC,KAAK,EAAE;MACPhG,YAAY,CAACiG,YAAY,CAACC,MAAM,CAAC;IACrC;IACA;IACA,OAAO;MACH9C,QAAQ,EAAE8C,MAAM;MAChBzD,UAAU,EAAEsD,UAAU,CAACtD;IAC3B,CAAC;EACL;AACJ;AACA,OAAO,SAASmB,iCAAiCA,CAAClB,UAAU,EAAEpC,aAAa,EAAEC,aAAa,EAAE8F,mBAAmB,GAAG,KAAK,EAAE;EACrH,MAAM1C,OAAO,GAAG,EAAE;EAClB,KAAK,MAAM2C,CAAC,IAAI7H,eAAe,CAACiE,UAAU,CAACf,GAAG,CAACjB,CAAC,IAAI6F,mBAAmB,CAAC7F,CAAC,EAAEJ,aAAa,EAAEC,aAAa,CAAC,CAAC,EAAE,CAACiG,EAAE,EAAEC,EAAE,KAAKD,EAAE,CAACtB,QAAQ,CAACwB,cAAc,CAACD,EAAE,CAACvB,QAAQ,CAAC,IACvJsB,EAAE,CAACvB,QAAQ,CAACyB,cAAc,CAACD,EAAE,CAACxB,QAAQ,CAAC,CAAC,EAAE;IAC7C,MAAM0B,KAAK,GAAGL,CAAC,CAAC,CAAC,CAAC;IAClB,MAAMM,IAAI,GAAGN,CAAC,CAACA,CAAC,CAAC7F,MAAM,GAAG,CAAC,CAAC;IAC5BkD,OAAO,CAACN,IAAI,CAAC,IAAIvD,wBAAwB,CAAC6G,KAAK,CAACzB,QAAQ,CAAC2B,IAAI,CAACD,IAAI,CAAC1B,QAAQ,CAAC,EAAEyB,KAAK,CAAC1B,QAAQ,CAAC4B,IAAI,CAACD,IAAI,CAAC3B,QAAQ,CAAC,EAAEqB,CAAC,CAAC3E,GAAG,CAACjB,CAAC,IAAIA,CAAC,CAACgE,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrJ;EACAhG,QAAQ,CAAC,MAAM;IACX,IAAI,CAAC2H,mBAAmB,IAAI1C,OAAO,CAAClD,MAAM,GAAG,CAAC,EAAE;MAC5C,IAAIkD,OAAO,CAAC,CAAC,CAAC,CAACsB,QAAQ,CAACV,eAAe,KAAKZ,OAAO,CAAC,CAAC,CAAC,CAACuB,QAAQ,CAACX,eAAe,EAAE;QAC7E,OAAO,KAAK;MAChB;MACA,IAAIhE,aAAa,CAACE,MAAM,GAAGkD,OAAO,CAACA,OAAO,CAAClD,MAAM,GAAG,CAAC,CAAC,CAACwE,QAAQ,CAACT,sBAAsB,KAAKlE,aAAa,CAACG,MAAM,GAAGkD,OAAO,CAACA,OAAO,CAAClD,MAAM,GAAG,CAAC,CAAC,CAACyE,QAAQ,CAACV,sBAAsB,EAAE;QAC3K,OAAO,KAAK;MAChB;IACJ;IACA,OAAO7F,kBAAkB,CAACgF,OAAO,EAAE,CAACmD,EAAE,EAAEC,EAAE,KAAKA,EAAE,CAAC7B,QAAQ,CAACX,eAAe,GAAGuC,EAAE,CAAC5B,QAAQ,CAACV,sBAAsB,KAAKuC,EAAE,CAAC9B,QAAQ,CAACV,eAAe,GAAGuC,EAAE,CAAC7B,QAAQ,CAACT,sBAAsB;IAChL;IACAsC,EAAE,CAAC5B,QAAQ,CAACV,sBAAsB,GAAGuC,EAAE,CAAC7B,QAAQ,CAACX,eAAe,IAChEuC,EAAE,CAAC7B,QAAQ,CAACT,sBAAsB,GAAGuC,EAAE,CAAC9B,QAAQ,CAACV,eAAe,CAAC;EACzE,CAAC,CAAC;EACF,OAAOZ,OAAO;AAClB;AACA,OAAO,SAAS4C,mBAAmBA,CAACZ,YAAY,EAAErF,aAAa,EAAEC,aAAa,EAAE;EAC5E,IAAIyG,cAAc,GAAG,CAAC;EACtB,IAAIC,YAAY,GAAG,CAAC;EACpB;EACA;EACA;EACA,IAAItB,YAAY,CAACd,aAAa,CAACqC,SAAS,KAAK,CAAC,IAAIvB,YAAY,CAACX,aAAa,CAACkC,SAAS,KAAK,CAAC,IACrFvB,YAAY,CAACX,aAAa,CAACT,eAAe,GAAGyC,cAAc,IAAIrB,YAAY,CAACX,aAAa,CAACmC,aAAa,IACvGxB,YAAY,CAACd,aAAa,CAACN,eAAe,GAAGyC,cAAc,IAAIrB,YAAY,CAACd,aAAa,CAACsC,aAAa,EAAE;IAC5G;IACAF,YAAY,GAAG,CAAC,CAAC;EACrB;EACA;EACA;EACA,IAAItB,YAAY,CAACd,aAAa,CAACuC,WAAW,GAAG,CAAC,IAAI7G,aAAa,CAACoF,YAAY,CAACd,aAAa,CAACN,eAAe,GAAG,CAAC,CAAC,CAAC9D,MAAM,IAC/GkF,YAAY,CAACX,aAAa,CAACoC,WAAW,GAAG,CAAC,IAAI9G,aAAa,CAACqF,YAAY,CAACX,aAAa,CAACT,eAAe,GAAG,CAAC,CAAC,CAAC9D,MAAM,IAClHkF,YAAY,CAACX,aAAa,CAACT,eAAe,IAAIoB,YAAY,CAACX,aAAa,CAACmC,aAAa,GAAGF,YAAY,IACrGtB,YAAY,CAACd,aAAa,CAACN,eAAe,IAAIoB,YAAY,CAACd,aAAa,CAACsC,aAAa,GAAGF,YAAY,EAAE;IAC1G;IACAD,cAAc,GAAG,CAAC;EACtB;EACA,MAAMK,iBAAiB,GAAG,IAAIzI,SAAS,CAAC+G,YAAY,CAACX,aAAa,CAACT,eAAe,GAAGyC,cAAc,EAAErB,YAAY,CAACX,aAAa,CAACmC,aAAa,GAAG,CAAC,GAAGF,YAAY,CAAC;EACjK,MAAMK,iBAAiB,GAAG,IAAI1I,SAAS,CAAC+G,YAAY,CAACd,aAAa,CAACN,eAAe,GAAGyC,cAAc,EAAErB,YAAY,CAACd,aAAa,CAACsC,aAAa,GAAG,CAAC,GAAGF,YAAY,CAAC;EACjK,OAAO,IAAInH,wBAAwB,CAACuH,iBAAiB,EAAEC,iBAAiB,EAAE,CAAC3B,YAAY,CAAC,CAAC;AAC7F;AACA,SAASD,kBAAkBA,CAAC6B,YAAY,EAAE;EACtC,OAAO,IAAIxH,gBAAgB,CAAC,IAAInB,SAAS,CAAC2I,YAAY,CAAChE,SAAS,CAACC,KAAK,GAAG,CAAC,EAAE+D,YAAY,CAAChE,SAAS,CAACG,YAAY,GAAG,CAAC,CAAC,EAAE,IAAI9E,SAAS,CAAC2I,YAAY,CAAC9D,SAAS,CAACD,KAAK,GAAG,CAAC,EAAE+D,YAAY,CAAC9D,SAAS,CAACC,YAAY,GAAG,CAAC,CAAC,CAAC;AACnN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}