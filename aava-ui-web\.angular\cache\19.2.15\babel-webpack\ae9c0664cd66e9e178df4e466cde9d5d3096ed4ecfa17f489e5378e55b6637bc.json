{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport var Range;\n(function (Range) {\n  /**\n   * Returns the intersection between two ranges as a range itself.\n   * Returns `{ start: 0, end: 0 }` if the intersection is empty.\n   */\n  function intersect(one, other) {\n    if (one.start >= other.end || other.start >= one.end) {\n      return {\n        start: 0,\n        end: 0\n      };\n    }\n    const start = Math.max(one.start, other.start);\n    const end = Math.min(one.end, other.end);\n    if (end - start <= 0) {\n      return {\n        start: 0,\n        end: 0\n      };\n    }\n    return {\n      start,\n      end\n    };\n  }\n  Range.intersect = intersect;\n  function isEmpty(range) {\n    return range.end - range.start <= 0;\n  }\n  Range.isEmpty = isEmpty;\n  function intersects(one, other) {\n    return !isEmpty(intersect(one, other));\n  }\n  Range.intersects = intersects;\n  function relativeComplement(one, other) {\n    const result = [];\n    const first = {\n      start: one.start,\n      end: Math.min(other.start, one.end)\n    };\n    const second = {\n      start: Math.max(other.end, one.start),\n      end: one.end\n    };\n    if (!isEmpty(first)) {\n      result.push(first);\n    }\n    if (!isEmpty(second)) {\n      result.push(second);\n    }\n    return result;\n  }\n  Range.relativeComplement = relativeComplement;\n})(Range || (Range = {}));", "map": {"version": 3, "names": ["Range", "intersect", "one", "other", "start", "end", "Math", "max", "min", "isEmpty", "range", "intersects", "relativeComplement", "result", "first", "second", "push"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/common/range.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport var Range;\n(function (Range) {\n    /**\n     * Returns the intersection between two ranges as a range itself.\n     * Returns `{ start: 0, end: 0 }` if the intersection is empty.\n     */\n    function intersect(one, other) {\n        if (one.start >= other.end || other.start >= one.end) {\n            return { start: 0, end: 0 };\n        }\n        const start = Math.max(one.start, other.start);\n        const end = Math.min(one.end, other.end);\n        if (end - start <= 0) {\n            return { start: 0, end: 0 };\n        }\n        return { start, end };\n    }\n    Range.intersect = intersect;\n    function isEmpty(range) {\n        return range.end - range.start <= 0;\n    }\n    Range.isEmpty = isEmpty;\n    function intersects(one, other) {\n        return !isEmpty(intersect(one, other));\n    }\n    Range.intersects = intersects;\n    function relativeComplement(one, other) {\n        const result = [];\n        const first = { start: one.start, end: Math.min(other.start, one.end) };\n        const second = { start: Math.max(other.end, one.start), end: one.end };\n        if (!isEmpty(first)) {\n            result.push(first);\n        }\n        if (!isEmpty(second)) {\n            result.push(second);\n        }\n        return result;\n    }\n    Range.relativeComplement = relativeComplement;\n})(Range || (Range = {}));\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,IAAIA,KAAK;AAChB,CAAC,UAAUA,KAAK,EAAE;EACd;AACJ;AACA;AACA;EACI,SAASC,SAASA,CAACC,GAAG,EAAEC,KAAK,EAAE;IAC3B,IAAID,GAAG,CAACE,KAAK,IAAID,KAAK,CAACE,GAAG,IAAIF,KAAK,CAACC,KAAK,IAAIF,GAAG,CAACG,GAAG,EAAE;MAClD,OAAO;QAAED,KAAK,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAE,CAAC;IAC/B;IACA,MAAMD,KAAK,GAAGE,IAAI,CAACC,GAAG,CAACL,GAAG,CAACE,KAAK,EAAED,KAAK,CAACC,KAAK,CAAC;IAC9C,MAAMC,GAAG,GAAGC,IAAI,CAACE,GAAG,CAACN,GAAG,CAACG,GAAG,EAAEF,KAAK,CAACE,GAAG,CAAC;IACxC,IAAIA,GAAG,GAAGD,KAAK,IAAI,CAAC,EAAE;MAClB,OAAO;QAAEA,KAAK,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAE,CAAC;IAC/B;IACA,OAAO;MAAED,KAAK;MAAEC;IAAI,CAAC;EACzB;EACAL,KAAK,CAACC,SAAS,GAAGA,SAAS;EAC3B,SAASQ,OAAOA,CAACC,KAAK,EAAE;IACpB,OAAOA,KAAK,CAACL,GAAG,GAAGK,KAAK,CAACN,KAAK,IAAI,CAAC;EACvC;EACAJ,KAAK,CAACS,OAAO,GAAGA,OAAO;EACvB,SAASE,UAAUA,CAACT,GAAG,EAAEC,KAAK,EAAE;IAC5B,OAAO,CAACM,OAAO,CAACR,SAAS,CAACC,GAAG,EAAEC,KAAK,CAAC,CAAC;EAC1C;EACAH,KAAK,CAACW,UAAU,GAAGA,UAAU;EAC7B,SAASC,kBAAkBA,CAACV,GAAG,EAAEC,KAAK,EAAE;IACpC,MAAMU,MAAM,GAAG,EAAE;IACjB,MAAMC,KAAK,GAAG;MAAEV,KAAK,EAAEF,GAAG,CAACE,KAAK;MAAEC,GAAG,EAAEC,IAAI,CAACE,GAAG,CAACL,KAAK,CAACC,KAAK,EAAEF,GAAG,CAACG,GAAG;IAAE,CAAC;IACvE,MAAMU,MAAM,GAAG;MAAEX,KAAK,EAAEE,IAAI,CAACC,GAAG,CAACJ,KAAK,CAACE,GAAG,EAAEH,GAAG,CAACE,KAAK,CAAC;MAAEC,GAAG,EAAEH,GAAG,CAACG;IAAI,CAAC;IACtE,IAAI,CAACI,OAAO,CAACK,KAAK,CAAC,EAAE;MACjBD,MAAM,CAACG,IAAI,CAACF,KAAK,CAAC;IACtB;IACA,IAAI,CAACL,OAAO,CAACM,MAAM,CAAC,EAAE;MAClBF,MAAM,CAACG,IAAI,CAACD,MAAM,CAAC;IACvB;IACA,OAAOF,MAAM;EACjB;EACAb,KAAK,CAACY,kBAAkB,GAAGA,kBAAkB;AACjD,CAAC,EAAEZ,KAAK,KAAKA,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}