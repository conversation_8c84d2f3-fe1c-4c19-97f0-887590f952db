{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport class LineSequence {\n  constructor(trimmedHash, lines) {\n    this.trimmedHash = trimmedHash;\n    this.lines = lines;\n  }\n  getElement(offset) {\n    return this.trimmedHash[offset];\n  }\n  get length() {\n    return this.trimmedHash.length;\n  }\n  getBoundaryScore(length) {\n    const indentationBefore = length === 0 ? 0 : getIndentation(this.lines[length - 1]);\n    const indentationAfter = length === this.lines.length ? 0 : getIndentation(this.lines[length]);\n    return 1000 - (indentationBefore + indentationAfter);\n  }\n  getText(range) {\n    return this.lines.slice(range.start, range.endExclusive).join('\\n');\n  }\n  isStronglyEqual(offset1, offset2) {\n    return this.lines[offset1] === this.lines[offset2];\n  }\n}\nfunction getIndentation(str) {\n  let i = 0;\n  while (i < str.length && (str.charCodeAt(i) === 32 /* CharCode.Space */ || str.charCodeAt(i) === 9 /* CharCode.Tab */)) {\n    i++;\n  }\n  return i;\n}", "map": {"version": 3, "names": ["LineSequence", "constructor", "trimmedHash", "lines", "getElement", "offset", "length", "getBoundaryScore", "indentationBefore", "getIndentation", "indentationAfter", "getText", "range", "slice", "start", "endExclusive", "join", "isStronglyEqual", "offset1", "offset2", "str", "i", "charCodeAt"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/diff/defaultLinesDiffComputer/lineSequence.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport class LineSequence {\n    constructor(trimmedHash, lines) {\n        this.trimmedHash = trimmedHash;\n        this.lines = lines;\n    }\n    getElement(offset) {\n        return this.trimmedHash[offset];\n    }\n    get length() {\n        return this.trimmedHash.length;\n    }\n    getBoundaryScore(length) {\n        const indentationBefore = length === 0 ? 0 : getIndentation(this.lines[length - 1]);\n        const indentationAfter = length === this.lines.length ? 0 : getIndentation(this.lines[length]);\n        return 1000 - (indentationBefore + indentationAfter);\n    }\n    getText(range) {\n        return this.lines.slice(range.start, range.endExclusive).join('\\n');\n    }\n    isStronglyEqual(offset1, offset2) {\n        return this.lines[offset1] === this.lines[offset2];\n    }\n}\nfunction getIndentation(str) {\n    let i = 0;\n    while (i < str.length && (str.charCodeAt(i) === 32 /* CharCode.Space */ || str.charCodeAt(i) === 9 /* CharCode.Tab */)) {\n        i++;\n    }\n    return i;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,MAAMA,YAAY,CAAC;EACtBC,WAAWA,CAACC,WAAW,EAAEC,KAAK,EAAE;IAC5B,IAAI,CAACD,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,KAAK,GAAGA,KAAK;EACtB;EACAC,UAAUA,CAACC,MAAM,EAAE;IACf,OAAO,IAAI,CAACH,WAAW,CAACG,MAAM,CAAC;EACnC;EACA,IAAIC,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACJ,WAAW,CAACI,MAAM;EAClC;EACAC,gBAAgBA,CAACD,MAAM,EAAE;IACrB,MAAME,iBAAiB,GAAGF,MAAM,KAAK,CAAC,GAAG,CAAC,GAAGG,cAAc,CAAC,IAAI,CAACN,KAAK,CAACG,MAAM,GAAG,CAAC,CAAC,CAAC;IACnF,MAAMI,gBAAgB,GAAGJ,MAAM,KAAK,IAAI,CAACH,KAAK,CAACG,MAAM,GAAG,CAAC,GAAGG,cAAc,CAAC,IAAI,CAACN,KAAK,CAACG,MAAM,CAAC,CAAC;IAC9F,OAAO,IAAI,IAAIE,iBAAiB,GAAGE,gBAAgB,CAAC;EACxD;EACAC,OAAOA,CAACC,KAAK,EAAE;IACX,OAAO,IAAI,CAACT,KAAK,CAACU,KAAK,CAACD,KAAK,CAACE,KAAK,EAAEF,KAAK,CAACG,YAAY,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;EACvE;EACAC,eAAeA,CAACC,OAAO,EAAEC,OAAO,EAAE;IAC9B,OAAO,IAAI,CAAChB,KAAK,CAACe,OAAO,CAAC,KAAK,IAAI,CAACf,KAAK,CAACgB,OAAO,CAAC;EACtD;AACJ;AACA,SAASV,cAAcA,CAACW,GAAG,EAAE;EACzB,IAAIC,CAAC,GAAG,CAAC;EACT,OAAOA,CAAC,GAAGD,GAAG,CAACd,MAAM,KAAKc,GAAG,CAACE,UAAU,CAACD,CAAC,CAAC,KAAK,EAAE,CAAC,wBAAwBD,GAAG,CAACE,UAAU,CAACD,CAAC,CAAC,KAAK,CAAC,CAAC,mBAAmB,EAAE;IACpHA,CAAC,EAAE;EACP;EACA,OAAOA,CAAC;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}