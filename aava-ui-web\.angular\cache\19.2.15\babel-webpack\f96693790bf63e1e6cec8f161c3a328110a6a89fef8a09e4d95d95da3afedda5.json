{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = this && this.__param || function (paramIndex, decorator) {\n  return function (target, key) {\n    decorator(target, key, paramIndex);\n  };\n};\nvar MarginHoverWidget_1;\nimport * as dom from '../../../../base/browser/dom.js';\nimport { Disposable, DisposableStore } from '../../../../base/common/lifecycle.js';\nimport { MarkdownRenderer } from '../../../browser/widget/markdownRenderer/browser/markdownRenderer.js';\nimport { ILanguageService } from '../../../common/languages/language.js';\nimport { HoverOperation } from './hoverOperation.js';\nimport { IOpenerService } from '../../../../platform/opener/common/opener.js';\nimport { HoverWidget } from '../../../../base/browser/ui/hover/hoverWidget.js';\nimport { MarginHoverComputer } from './marginHoverComputer.js';\nimport { isMousePositionWithinElement } from './hoverUtils.js';\nconst $ = dom.$;\nlet MarginHoverWidget = /*#__PURE__*/(() => {\n  let MarginHoverWidget = class MarginHoverWidget extends Disposable {\n    static {\n      MarginHoverWidget_1 = this;\n    }\n    static {\n      this.ID = 'editor.contrib.modesGlyphHoverWidget';\n    }\n    constructor(editor, languageService, openerService) {\n      super();\n      this._renderDisposeables = this._register(new DisposableStore());\n      this._editor = editor;\n      this._isVisible = false;\n      this._messages = [];\n      this._hover = this._register(new HoverWidget());\n      this._hover.containerDomNode.classList.toggle('hidden', !this._isVisible);\n      this._markdownRenderer = this._register(new MarkdownRenderer({\n        editor: this._editor\n      }, languageService, openerService));\n      this._computer = new MarginHoverComputer(this._editor);\n      this._hoverOperation = this._register(new HoverOperation(this._editor, this._computer));\n      this._register(this._hoverOperation.onResult(result => {\n        this._withResult(result.value);\n      }));\n      this._register(this._editor.onDidChangeModelDecorations(() => this._onModelDecorationsChanged()));\n      this._register(this._editor.onDidChangeConfiguration(e => {\n        if (e.hasChanged(50 /* EditorOption.fontInfo */)) {\n          this._updateFont();\n        }\n      }));\n      this._register(dom.addStandardDisposableListener(this._hover.containerDomNode, 'mouseleave', e => {\n        this._onMouseLeave(e);\n      }));\n      this._editor.addOverlayWidget(this);\n    }\n    dispose() {\n      this._editor.removeOverlayWidget(this);\n      super.dispose();\n    }\n    getId() {\n      return MarginHoverWidget_1.ID;\n    }\n    getDomNode() {\n      return this._hover.containerDomNode;\n    }\n    getPosition() {\n      return null;\n    }\n    _updateFont() {\n      const codeClasses = Array.prototype.slice.call(this._hover.contentsDomNode.getElementsByClassName('code'));\n      codeClasses.forEach(node => this._editor.applyFontInfo(node));\n    }\n    _onModelDecorationsChanged() {\n      if (this._isVisible) {\n        // The decorations have changed and the hover is visible,\n        // we need to recompute the displayed text\n        this._hoverOperation.cancel();\n        this._hoverOperation.start(0 /* HoverStartMode.Delayed */);\n      }\n    }\n    showsOrWillShow(mouseEvent) {\n      const target = mouseEvent.target;\n      if (target.type === 2 /* MouseTargetType.GUTTER_GLYPH_MARGIN */ && target.detail.glyphMarginLane) {\n        this._startShowingAt(target.position.lineNumber, target.detail.glyphMarginLane);\n        return true;\n      }\n      if (target.type === 3 /* MouseTargetType.GUTTER_LINE_NUMBERS */) {\n        this._startShowingAt(target.position.lineNumber, 'lineNo');\n        return true;\n      }\n      return false;\n    }\n    _startShowingAt(lineNumber, laneOrLine) {\n      if (this._computer.lineNumber === lineNumber && this._computer.lane === laneOrLine) {\n        // We have to show the widget at the exact same line number as before, so no work is needed\n        return;\n      }\n      this._hoverOperation.cancel();\n      this.hide();\n      this._computer.lineNumber = lineNumber;\n      this._computer.lane = laneOrLine;\n      this._hoverOperation.start(0 /* HoverStartMode.Delayed */);\n    }\n    hide() {\n      this._computer.lineNumber = -1;\n      this._hoverOperation.cancel();\n      if (!this._isVisible) {\n        return;\n      }\n      this._isVisible = false;\n      this._hover.containerDomNode.classList.toggle('hidden', !this._isVisible);\n    }\n    _withResult(result) {\n      this._messages = result;\n      if (this._messages.length > 0) {\n        this._renderMessages(this._computer.lineNumber, this._messages);\n      } else {\n        this.hide();\n      }\n    }\n    _renderMessages(lineNumber, messages) {\n      this._renderDisposeables.clear();\n      const fragment = document.createDocumentFragment();\n      for (const msg of messages) {\n        const markdownHoverElement = $('div.hover-row.markdown-hover');\n        const hoverContentsElement = dom.append(markdownHoverElement, $('div.hover-contents'));\n        const renderedContents = this._renderDisposeables.add(this._markdownRenderer.render(msg.value));\n        hoverContentsElement.appendChild(renderedContents.element);\n        fragment.appendChild(markdownHoverElement);\n      }\n      this._updateContents(fragment);\n      this._showAt(lineNumber);\n    }\n    _updateContents(node) {\n      this._hover.contentsDomNode.textContent = '';\n      this._hover.contentsDomNode.appendChild(node);\n      this._updateFont();\n    }\n    _showAt(lineNumber) {\n      if (!this._isVisible) {\n        this._isVisible = true;\n        this._hover.containerDomNode.classList.toggle('hidden', !this._isVisible);\n      }\n      const editorLayout = this._editor.getLayoutInfo();\n      const topForLineNumber = this._editor.getTopForLineNumber(lineNumber);\n      const editorScrollTop = this._editor.getScrollTop();\n      const lineHeight = this._editor.getOption(67 /* EditorOption.lineHeight */);\n      const nodeHeight = this._hover.containerDomNode.clientHeight;\n      const top = topForLineNumber - editorScrollTop - (nodeHeight - lineHeight) / 2;\n      const left = editorLayout.glyphMarginLeft + editorLayout.glyphMarginWidth + (this._computer.lane === 'lineNo' ? editorLayout.lineNumbersWidth : 0);\n      this._hover.containerDomNode.style.left = `${left}px`;\n      this._hover.containerDomNode.style.top = `${Math.max(Math.round(top), 0)}px`;\n    }\n    _onMouseLeave(e) {\n      const editorDomNode = this._editor.getDomNode();\n      const isMousePositionOutsideOfEditor = !editorDomNode || !isMousePositionWithinElement(editorDomNode, e.x, e.y);\n      if (isMousePositionOutsideOfEditor) {\n        this.hide();\n      }\n    }\n  };\n  return MarginHoverWidget;\n})();\nMarginHoverWidget = MarginHoverWidget_1 = __decorate([__param(1, ILanguageService), __param(2, IOpenerService)], MarginHoverWidget);\nexport { MarginHoverWidget };", "map": {"version": 3, "names": ["__decorate", "decorators", "target", "key", "desc", "c", "arguments", "length", "r", "Object", "getOwnPropertyDescriptor", "d", "Reflect", "decorate", "i", "defineProperty", "__param", "paramIndex", "decorator", "MarginHoverWidget_1", "dom", "Disposable", "DisposableStore", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ILanguageService", "HoverOperation", "IOpenerService", "HoverWidget", "MarginHoverComputer", "isMousePositionWithinElement", "$", "MarginHoverWidget", "ID", "constructor", "editor", "languageService", "openerService", "_renderDisposeables", "_register", "_editor", "_isVisible", "_messages", "_hover", "containerDomNode", "classList", "toggle", "_markdown<PERSON><PERSON>er", "_computer", "_hoverOperation", "onResult", "result", "_withR<PERSON>ult", "value", "onDidChangeModelDecorations", "_onModelDecorationsChanged", "onDidChangeConfiguration", "e", "has<PERSON><PERSON>ed", "_updateFont", "addStandardDisposableListener", "_onMouseLeave", "addOverlayWidget", "dispose", "removeOverlayWidget", "getId", "getDomNode", "getPosition", "codeClasses", "Array", "prototype", "slice", "call", "contentsDomNode", "getElementsByClassName", "for<PERSON>ach", "node", "applyFontInfo", "cancel", "start", "showsOrWillShow", "mouseEvent", "type", "detail", "glyphMarginLane", "_startShowingAt", "position", "lineNumber", "laneOrLine", "lane", "hide", "_renderMessages", "messages", "clear", "fragment", "document", "createDocumentFragment", "msg", "markdownHoverElement", "hoverContentsElement", "append", "renderedContents", "add", "render", "append<PERSON><PERSON><PERSON>", "element", "_updateContents", "_showAt", "textContent", "editor<PERSON><PERSON>out", "getLayoutInfo", "topForLineNumber", "getTopForLineNumber", "editorScrollTop", "getScrollTop", "lineHeight", "getOption", "nodeHeight", "clientHeight", "top", "left", "glyphMarginLeft", "glyph<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lineNumbersWidth", "style", "Math", "max", "round", "editorDomNode", "isMousePositionOutsideOfEditor", "x", "y"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/contrib/hover/browser/marginHoverWidget.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = (this && this.__param) || function (paramIndex, decorator) {\n    return function (target, key) { decorator(target, key, paramIndex); }\n};\nvar MarginHoverWidget_1;\nimport * as dom from '../../../../base/browser/dom.js';\nimport { Disposable, DisposableStore } from '../../../../base/common/lifecycle.js';\nimport { MarkdownRenderer } from '../../../browser/widget/markdownRenderer/browser/markdownRenderer.js';\nimport { ILanguageService } from '../../../common/languages/language.js';\nimport { HoverOperation } from './hoverOperation.js';\nimport { IOpenerService } from '../../../../platform/opener/common/opener.js';\nimport { HoverWidget } from '../../../../base/browser/ui/hover/hoverWidget.js';\nimport { MarginHoverComputer } from './marginHoverComputer.js';\nimport { isMousePositionWithinElement } from './hoverUtils.js';\nconst $ = dom.$;\nlet MarginHoverWidget = class MarginHoverWidget extends Disposable {\n    static { MarginHoverWidget_1 = this; }\n    static { this.ID = 'editor.contrib.modesGlyphHoverWidget'; }\n    constructor(editor, languageService, openerService) {\n        super();\n        this._renderDisposeables = this._register(new DisposableStore());\n        this._editor = editor;\n        this._isVisible = false;\n        this._messages = [];\n        this._hover = this._register(new HoverWidget());\n        this._hover.containerDomNode.classList.toggle('hidden', !this._isVisible);\n        this._markdownRenderer = this._register(new MarkdownRenderer({ editor: this._editor }, languageService, openerService));\n        this._computer = new MarginHoverComputer(this._editor);\n        this._hoverOperation = this._register(new HoverOperation(this._editor, this._computer));\n        this._register(this._hoverOperation.onResult((result) => {\n            this._withResult(result.value);\n        }));\n        this._register(this._editor.onDidChangeModelDecorations(() => this._onModelDecorationsChanged()));\n        this._register(this._editor.onDidChangeConfiguration((e) => {\n            if (e.hasChanged(50 /* EditorOption.fontInfo */)) {\n                this._updateFont();\n            }\n        }));\n        this._register(dom.addStandardDisposableListener(this._hover.containerDomNode, 'mouseleave', (e) => {\n            this._onMouseLeave(e);\n        }));\n        this._editor.addOverlayWidget(this);\n    }\n    dispose() {\n        this._editor.removeOverlayWidget(this);\n        super.dispose();\n    }\n    getId() {\n        return MarginHoverWidget_1.ID;\n    }\n    getDomNode() {\n        return this._hover.containerDomNode;\n    }\n    getPosition() {\n        return null;\n    }\n    _updateFont() {\n        const codeClasses = Array.prototype.slice.call(this._hover.contentsDomNode.getElementsByClassName('code'));\n        codeClasses.forEach(node => this._editor.applyFontInfo(node));\n    }\n    _onModelDecorationsChanged() {\n        if (this._isVisible) {\n            // The decorations have changed and the hover is visible,\n            // we need to recompute the displayed text\n            this._hoverOperation.cancel();\n            this._hoverOperation.start(0 /* HoverStartMode.Delayed */);\n        }\n    }\n    showsOrWillShow(mouseEvent) {\n        const target = mouseEvent.target;\n        if (target.type === 2 /* MouseTargetType.GUTTER_GLYPH_MARGIN */ && target.detail.glyphMarginLane) {\n            this._startShowingAt(target.position.lineNumber, target.detail.glyphMarginLane);\n            return true;\n        }\n        if (target.type === 3 /* MouseTargetType.GUTTER_LINE_NUMBERS */) {\n            this._startShowingAt(target.position.lineNumber, 'lineNo');\n            return true;\n        }\n        return false;\n    }\n    _startShowingAt(lineNumber, laneOrLine) {\n        if (this._computer.lineNumber === lineNumber && this._computer.lane === laneOrLine) {\n            // We have to show the widget at the exact same line number as before, so no work is needed\n            return;\n        }\n        this._hoverOperation.cancel();\n        this.hide();\n        this._computer.lineNumber = lineNumber;\n        this._computer.lane = laneOrLine;\n        this._hoverOperation.start(0 /* HoverStartMode.Delayed */);\n    }\n    hide() {\n        this._computer.lineNumber = -1;\n        this._hoverOperation.cancel();\n        if (!this._isVisible) {\n            return;\n        }\n        this._isVisible = false;\n        this._hover.containerDomNode.classList.toggle('hidden', !this._isVisible);\n    }\n    _withResult(result) {\n        this._messages = result;\n        if (this._messages.length > 0) {\n            this._renderMessages(this._computer.lineNumber, this._messages);\n        }\n        else {\n            this.hide();\n        }\n    }\n    _renderMessages(lineNumber, messages) {\n        this._renderDisposeables.clear();\n        const fragment = document.createDocumentFragment();\n        for (const msg of messages) {\n            const markdownHoverElement = $('div.hover-row.markdown-hover');\n            const hoverContentsElement = dom.append(markdownHoverElement, $('div.hover-contents'));\n            const renderedContents = this._renderDisposeables.add(this._markdownRenderer.render(msg.value));\n            hoverContentsElement.appendChild(renderedContents.element);\n            fragment.appendChild(markdownHoverElement);\n        }\n        this._updateContents(fragment);\n        this._showAt(lineNumber);\n    }\n    _updateContents(node) {\n        this._hover.contentsDomNode.textContent = '';\n        this._hover.contentsDomNode.appendChild(node);\n        this._updateFont();\n    }\n    _showAt(lineNumber) {\n        if (!this._isVisible) {\n            this._isVisible = true;\n            this._hover.containerDomNode.classList.toggle('hidden', !this._isVisible);\n        }\n        const editorLayout = this._editor.getLayoutInfo();\n        const topForLineNumber = this._editor.getTopForLineNumber(lineNumber);\n        const editorScrollTop = this._editor.getScrollTop();\n        const lineHeight = this._editor.getOption(67 /* EditorOption.lineHeight */);\n        const nodeHeight = this._hover.containerDomNode.clientHeight;\n        const top = topForLineNumber - editorScrollTop - ((nodeHeight - lineHeight) / 2);\n        const left = editorLayout.glyphMarginLeft + editorLayout.glyphMarginWidth + (this._computer.lane === 'lineNo' ? editorLayout.lineNumbersWidth : 0);\n        this._hover.containerDomNode.style.left = `${left}px`;\n        this._hover.containerDomNode.style.top = `${Math.max(Math.round(top), 0)}px`;\n    }\n    _onMouseLeave(e) {\n        const editorDomNode = this._editor.getDomNode();\n        const isMousePositionOutsideOfEditor = !editorDomNode || !isMousePositionWithinElement(editorDomNode, e.x, e.y);\n        if (isMousePositionOutsideOfEditor) {\n            this.hide();\n        }\n    }\n};\nMarginHoverWidget = MarginHoverWidget_1 = __decorate([\n    __param(1, ILanguageService),\n    __param(2, IOpenerService)\n], MarginHoverWidget);\nexport { MarginHoverWidget };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,IAAIA,UAAU,GAAI,IAAI,IAAI,IAAI,CAACA,UAAU,IAAK,UAAUC,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAE;EACnF,IAAIC,CAAC,GAAGC,SAAS,CAACC,MAAM;IAAEC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGH,MAAM,GAAGE,IAAI,KAAK,IAAI,GAAGA,IAAI,GAAGK,MAAM,CAACC,wBAAwB,CAACR,MAAM,EAAEC,GAAG,CAAC,GAAGC,IAAI;IAAEO,CAAC;EAC5H,IAAI,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,CAACC,QAAQ,KAAK,UAAU,EAAEL,CAAC,GAAGI,OAAO,CAACC,QAAQ,CAACZ,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,CAAC,CAAC,KAC1H,KAAK,IAAIU,CAAC,GAAGb,UAAU,CAACM,MAAM,GAAG,CAAC,EAAEO,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE,IAAIH,CAAC,GAAGV,UAAU,CAACa,CAAC,CAAC,EAAEN,CAAC,GAAG,CAACH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACH,CAAC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACT,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,GAAGG,CAAC,CAACT,MAAM,EAAEC,GAAG,CAAC,KAAKK,CAAC;EACjJ,OAAOH,CAAC,GAAG,CAAC,IAAIG,CAAC,IAAIC,MAAM,CAACM,cAAc,CAACb,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,EAAEA,CAAC;AACjE,CAAC;AACD,IAAIQ,OAAO,GAAI,IAAI,IAAI,IAAI,CAACA,OAAO,IAAK,UAAUC,UAAU,EAAEC,SAAS,EAAE;EACrE,OAAO,UAAUhB,MAAM,EAAEC,GAAG,EAAE;IAAEe,SAAS,CAAChB,MAAM,EAAEC,GAAG,EAAEc,UAAU,CAAC;EAAE,CAAC;AACzE,CAAC;AACD,IAAIE,mBAAmB;AACvB,OAAO,KAAKC,GAAG,MAAM,iCAAiC;AACtD,SAASC,UAAU,EAAEC,eAAe,QAAQ,sCAAsC;AAClF,SAASC,gBAAgB,QAAQ,sEAAsE;AACvG,SAASC,gBAAgB,QAAQ,uCAAuC;AACxE,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,cAAc,QAAQ,8CAA8C;AAC7E,SAASC,WAAW,QAAQ,kDAAkD;AAC9E,SAASC,mBAAmB,QAAQ,0BAA0B;AAC9D,SAASC,4BAA4B,QAAQ,iBAAiB;AAC9D,MAAMC,CAAC,GAAGV,GAAG,CAACU,CAAC;AACf,IAAIC,iBAAiB;EAAA,IAAjBA,iBAAiB,GAAG,MAAMA,iBAAiB,SAASV,UAAU,CAAC;IAC/D;MAASF,mBAAmB,GAAG,IAAI;IAAE;IACrC;MAAS,IAAI,CAACa,EAAE,GAAG,sCAAsC;IAAE;IAC3DC,WAAWA,CAACC,MAAM,EAAEC,eAAe,EAAEC,aAAa,EAAE;MAChD,KAAK,CAAC,CAAC;MACP,IAAI,CAACC,mBAAmB,GAAG,IAAI,CAACC,SAAS,CAAC,IAAIhB,eAAe,CAAC,CAAC,CAAC;MAChE,IAAI,CAACiB,OAAO,GAAGL,MAAM;MACrB,IAAI,CAACM,UAAU,GAAG,KAAK;MACvB,IAAI,CAACC,SAAS,GAAG,EAAE;MACnB,IAAI,CAACC,MAAM,GAAG,IAAI,CAACJ,SAAS,CAAC,IAAIX,WAAW,CAAC,CAAC,CAAC;MAC/C,IAAI,CAACe,MAAM,CAACC,gBAAgB,CAACC,SAAS,CAACC,MAAM,CAAC,QAAQ,EAAE,CAAC,IAAI,CAACL,UAAU,CAAC;MACzE,IAAI,CAACM,iBAAiB,GAAG,IAAI,CAACR,SAAS,CAAC,IAAIf,gBAAgB,CAAC;QAAEW,MAAM,EAAE,IAAI,CAACK;MAAQ,CAAC,EAAEJ,eAAe,EAAEC,aAAa,CAAC,CAAC;MACvH,IAAI,CAACW,SAAS,GAAG,IAAInB,mBAAmB,CAAC,IAAI,CAACW,OAAO,CAAC;MACtD,IAAI,CAACS,eAAe,GAAG,IAAI,CAACV,SAAS,CAAC,IAAIb,cAAc,CAAC,IAAI,CAACc,OAAO,EAAE,IAAI,CAACQ,SAAS,CAAC,CAAC;MACvF,IAAI,CAACT,SAAS,CAAC,IAAI,CAACU,eAAe,CAACC,QAAQ,CAAEC,MAAM,IAAK;QACrD,IAAI,CAACC,WAAW,CAACD,MAAM,CAACE,KAAK,CAAC;MAClC,CAAC,CAAC,CAAC;MACH,IAAI,CAACd,SAAS,CAAC,IAAI,CAACC,OAAO,CAACc,2BAA2B,CAAC,MAAM,IAAI,CAACC,0BAA0B,CAAC,CAAC,CAAC,CAAC;MACjG,IAAI,CAAChB,SAAS,CAAC,IAAI,CAACC,OAAO,CAACgB,wBAAwB,CAAEC,CAAC,IAAK;QACxD,IAAIA,CAAC,CAACC,UAAU,CAAC,EAAE,CAAC,2BAA2B,CAAC,EAAE;UAC9C,IAAI,CAACC,WAAW,CAAC,CAAC;QACtB;MACJ,CAAC,CAAC,CAAC;MACH,IAAI,CAACpB,SAAS,CAAClB,GAAG,CAACuC,6BAA6B,CAAC,IAAI,CAACjB,MAAM,CAACC,gBAAgB,EAAE,YAAY,EAAGa,CAAC,IAAK;QAChG,IAAI,CAACI,aAAa,CAACJ,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC;MACH,IAAI,CAACjB,OAAO,CAACsB,gBAAgB,CAAC,IAAI,CAAC;IACvC;IACAC,OAAOA,CAAA,EAAG;MACN,IAAI,CAACvB,OAAO,CAACwB,mBAAmB,CAAC,IAAI,CAAC;MACtC,KAAK,CAACD,OAAO,CAAC,CAAC;IACnB;IACAE,KAAKA,CAAA,EAAG;MACJ,OAAO7C,mBAAmB,CAACa,EAAE;IACjC;IACAiC,UAAUA,CAAA,EAAG;MACT,OAAO,IAAI,CAACvB,MAAM,CAACC,gBAAgB;IACvC;IACAuB,WAAWA,CAAA,EAAG;MACV,OAAO,IAAI;IACf;IACAR,WAAWA,CAAA,EAAG;MACV,MAAMS,WAAW,GAAGC,KAAK,CAACC,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC7B,MAAM,CAAC8B,eAAe,CAACC,sBAAsB,CAAC,MAAM,CAAC,CAAC;MAC1GN,WAAW,CAACO,OAAO,CAACC,IAAI,IAAI,IAAI,CAACpC,OAAO,CAACqC,aAAa,CAACD,IAAI,CAAC,CAAC;IACjE;IACArB,0BAA0BA,CAAA,EAAG;MACzB,IAAI,IAAI,CAACd,UAAU,EAAE;QACjB;QACA;QACA,IAAI,CAACQ,eAAe,CAAC6B,MAAM,CAAC,CAAC;QAC7B,IAAI,CAAC7B,eAAe,CAAC8B,KAAK,CAAC,CAAC,CAAC,4BAA4B,CAAC;MAC9D;IACJ;IACAC,eAAeA,CAACC,UAAU,EAAE;MACxB,MAAM9E,MAAM,GAAG8E,UAAU,CAAC9E,MAAM;MAChC,IAAIA,MAAM,CAAC+E,IAAI,KAAK,CAAC,CAAC,6CAA6C/E,MAAM,CAACgF,MAAM,CAACC,eAAe,EAAE;QAC9F,IAAI,CAACC,eAAe,CAAClF,MAAM,CAACmF,QAAQ,CAACC,UAAU,EAAEpF,MAAM,CAACgF,MAAM,CAACC,eAAe,CAAC;QAC/E,OAAO,IAAI;MACf;MACA,IAAIjF,MAAM,CAAC+E,IAAI,KAAK,CAAC,CAAC,2CAA2C;QAC7D,IAAI,CAACG,eAAe,CAAClF,MAAM,CAACmF,QAAQ,CAACC,UAAU,EAAE,QAAQ,CAAC;QAC1D,OAAO,IAAI;MACf;MACA,OAAO,KAAK;IAChB;IACAF,eAAeA,CAACE,UAAU,EAAEC,UAAU,EAAE;MACpC,IAAI,IAAI,CAACxC,SAAS,CAACuC,UAAU,KAAKA,UAAU,IAAI,IAAI,CAACvC,SAAS,CAACyC,IAAI,KAAKD,UAAU,EAAE;QAChF;QACA;MACJ;MACA,IAAI,CAACvC,eAAe,CAAC6B,MAAM,CAAC,CAAC;MAC7B,IAAI,CAACY,IAAI,CAAC,CAAC;MACX,IAAI,CAAC1C,SAAS,CAACuC,UAAU,GAAGA,UAAU;MACtC,IAAI,CAACvC,SAAS,CAACyC,IAAI,GAAGD,UAAU;MAChC,IAAI,CAACvC,eAAe,CAAC8B,KAAK,CAAC,CAAC,CAAC,4BAA4B,CAAC;IAC9D;IACAW,IAAIA,CAAA,EAAG;MACH,IAAI,CAAC1C,SAAS,CAACuC,UAAU,GAAG,CAAC,CAAC;MAC9B,IAAI,CAACtC,eAAe,CAAC6B,MAAM,CAAC,CAAC;MAC7B,IAAI,CAAC,IAAI,CAACrC,UAAU,EAAE;QAClB;MACJ;MACA,IAAI,CAACA,UAAU,GAAG,KAAK;MACvB,IAAI,CAACE,MAAM,CAACC,gBAAgB,CAACC,SAAS,CAACC,MAAM,CAAC,QAAQ,EAAE,CAAC,IAAI,CAACL,UAAU,CAAC;IAC7E;IACAW,WAAWA,CAACD,MAAM,EAAE;MAChB,IAAI,CAACT,SAAS,GAAGS,MAAM;MACvB,IAAI,IAAI,CAACT,SAAS,CAAClC,MAAM,GAAG,CAAC,EAAE;QAC3B,IAAI,CAACmF,eAAe,CAAC,IAAI,CAAC3C,SAAS,CAACuC,UAAU,EAAE,IAAI,CAAC7C,SAAS,CAAC;MACnE,CAAC,MACI;QACD,IAAI,CAACgD,IAAI,CAAC,CAAC;MACf;IACJ;IACAC,eAAeA,CAACJ,UAAU,EAAEK,QAAQ,EAAE;MAClC,IAAI,CAACtD,mBAAmB,CAACuD,KAAK,CAAC,CAAC;MAChC,MAAMC,QAAQ,GAAGC,QAAQ,CAACC,sBAAsB,CAAC,CAAC;MAClD,KAAK,MAAMC,GAAG,IAAIL,QAAQ,EAAE;QACxB,MAAMM,oBAAoB,GAAGnE,CAAC,CAAC,8BAA8B,CAAC;QAC9D,MAAMoE,oBAAoB,GAAG9E,GAAG,CAAC+E,MAAM,CAACF,oBAAoB,EAAEnE,CAAC,CAAC,oBAAoB,CAAC,CAAC;QACtF,MAAMsE,gBAAgB,GAAG,IAAI,CAAC/D,mBAAmB,CAACgE,GAAG,CAAC,IAAI,CAACvD,iBAAiB,CAACwD,MAAM,CAACN,GAAG,CAAC5C,KAAK,CAAC,CAAC;QAC/F8C,oBAAoB,CAACK,WAAW,CAACH,gBAAgB,CAACI,OAAO,CAAC;QAC1DX,QAAQ,CAACU,WAAW,CAACN,oBAAoB,CAAC;MAC9C;MACA,IAAI,CAACQ,eAAe,CAACZ,QAAQ,CAAC;MAC9B,IAAI,CAACa,OAAO,CAACpB,UAAU,CAAC;IAC5B;IACAmB,eAAeA,CAAC9B,IAAI,EAAE;MAClB,IAAI,CAACjC,MAAM,CAAC8B,eAAe,CAACmC,WAAW,GAAG,EAAE;MAC5C,IAAI,CAACjE,MAAM,CAAC8B,eAAe,CAAC+B,WAAW,CAAC5B,IAAI,CAAC;MAC7C,IAAI,CAACjB,WAAW,CAAC,CAAC;IACtB;IACAgD,OAAOA,CAACpB,UAAU,EAAE;MAChB,IAAI,CAAC,IAAI,CAAC9C,UAAU,EAAE;QAClB,IAAI,CAACA,UAAU,GAAG,IAAI;QACtB,IAAI,CAACE,MAAM,CAACC,gBAAgB,CAACC,SAAS,CAACC,MAAM,CAAC,QAAQ,EAAE,CAAC,IAAI,CAACL,UAAU,CAAC;MAC7E;MACA,MAAMoE,YAAY,GAAG,IAAI,CAACrE,OAAO,CAACsE,aAAa,CAAC,CAAC;MACjD,MAAMC,gBAAgB,GAAG,IAAI,CAACvE,OAAO,CAACwE,mBAAmB,CAACzB,UAAU,CAAC;MACrE,MAAM0B,eAAe,GAAG,IAAI,CAACzE,OAAO,CAAC0E,YAAY,CAAC,CAAC;MACnD,MAAMC,UAAU,GAAG,IAAI,CAAC3E,OAAO,CAAC4E,SAAS,CAAC,EAAE,CAAC,6BAA6B,CAAC;MAC3E,MAAMC,UAAU,GAAG,IAAI,CAAC1E,MAAM,CAACC,gBAAgB,CAAC0E,YAAY;MAC5D,MAAMC,GAAG,GAAGR,gBAAgB,GAAGE,eAAe,GAAI,CAACI,UAAU,GAAGF,UAAU,IAAI,CAAE;MAChF,MAAMK,IAAI,GAAGX,YAAY,CAACY,eAAe,GAAGZ,YAAY,CAACa,gBAAgB,IAAI,IAAI,CAAC1E,SAAS,CAACyC,IAAI,KAAK,QAAQ,GAAGoB,YAAY,CAACc,gBAAgB,GAAG,CAAC,CAAC;MAClJ,IAAI,CAAChF,MAAM,CAACC,gBAAgB,CAACgF,KAAK,CAACJ,IAAI,GAAG,GAAGA,IAAI,IAAI;MACrD,IAAI,CAAC7E,MAAM,CAACC,gBAAgB,CAACgF,KAAK,CAACL,GAAG,GAAG,GAAGM,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,KAAK,CAACR,GAAG,CAAC,EAAE,CAAC,CAAC,IAAI;IAChF;IACA1D,aAAaA,CAACJ,CAAC,EAAE;MACb,MAAMuE,aAAa,GAAG,IAAI,CAACxF,OAAO,CAAC0B,UAAU,CAAC,CAAC;MAC/C,MAAM+D,8BAA8B,GAAG,CAACD,aAAa,IAAI,CAAClG,4BAA4B,CAACkG,aAAa,EAAEvE,CAAC,CAACyE,CAAC,EAAEzE,CAAC,CAAC0E,CAAC,CAAC;MAC/G,IAAIF,8BAA8B,EAAE;QAChC,IAAI,CAACvC,IAAI,CAAC,CAAC;MACf;IACJ;EACJ,CAAC;EAAA,OAtIG1D,iBAAiB;AAAA,IAsIpB;AACDA,iBAAiB,GAAGZ,mBAAmB,GAAGnB,UAAU,CAAC,CACjDgB,OAAO,CAAC,CAAC,EAAEQ,gBAAgB,CAAC,EAC5BR,OAAO,CAAC,CAAC,EAAEU,cAAc,CAAC,CAC7B,EAAEK,iBAAiB,CAAC;AACrB,SAASA,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}