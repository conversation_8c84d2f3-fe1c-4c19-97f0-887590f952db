{"ast": null, "code": "import { CommonModule, DatePipe } from '@angular/common';\nimport { PageFooterComponent } from '@shared/components/page-footer/page-footer.component';\nimport { AvaTextboxComponent, DropdownComponent, IconComponent, TextCardComponent } from '@ava/play-comp-library';\nimport { LucideAngularModule } from 'lucide-angular';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { startWith, debounceTime, distinctUntilChanged, map } from 'rxjs';\nimport guardrailsLabels from './constants/guardrails-base.json';\nimport { ConsoleCardComponent } from \"@shared/components/console-card/console-card.component\";\nimport { TimeAgoPipe } from '@shared/pipes/time-ago.pipe';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@shared/services/pagination.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@shared/services/guardrails.service\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"@ava/play-comp-library\";\nfunction GuardrailsComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"div\", 14)(2, \"h5\", 15);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.grLabels.text);\n  }\n}\nfunction GuardrailsComponent_ng_container_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"ava-console-card\", 16);\n    i0.ɵɵpipe(2, \"timeAgo\");\n    i0.ɵɵlistener(\"actionClick\", function GuardrailsComponent_ng_container_11_Template_ava_console_card_actionClick_1_listener($event) {\n      const guardrail_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onActionClick($event, guardrail_r3.id));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const guardrail_r3 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"title\", guardrail_r3 == null ? null : guardrail_r3.title)(\"description\", guardrail_r3 == null ? null : guardrail_r3.description)(\"author\", (guardrail_r3 == null ? null : guardrail_r3.owner) || \"AAVA\")(\"date\", i0.ɵɵpipeBind1(2, 6, guardrail_r3 == null ? null : guardrail_r3.createdDate))(\"actions\", ctx_r0.defaultActions)(\"skeleton\", ctx_r0.isLoading);\n  }\n}\nfunction GuardrailsComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"div\", 18)(2, \"app-page-footer\", 19);\n    i0.ɵɵlistener(\"pageChange\", function GuardrailsComponent_div_12_Template_app_page_footer_pageChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onPageChange($event));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"totalItems\", ctx_r0.filteredGuardrails.length + 1)(\"currentPage\", ctx_r0.currentPage)(\"itemsPerPage\", ctx_r0.itemsPerPage);\n  }\n}\nexport let GuardrailsComponent = /*#__PURE__*/(() => {\n  class GuardrailsComponent {\n    paginationService;\n    router;\n    route;\n    datePipe;\n    guardrailsService;\n    fb;\n    cdr;\n    dialogService;\n    // Labels from constants file\n    grLabels = guardrailsLabels.labels;\n    searchForm;\n    search;\n    onSearchClick() {\n      throw new Error('Method not implemented.');\n    }\n    allGuardrails = [];\n    filteredGuardrails = [];\n    displayedGuardrails = [];\n    isLoading = false;\n    // Delete popup properties\n    showDeletePopup = false;\n    guardrailToDelete = null;\n    error = null;\n    currentPage = 1;\n    itemsPerPage = 12;\n    totalPages = 1;\n    guardrailsOptions = [{\n      name: 'All',\n      value: 'all'\n    }, {\n      name: 'Type A',\n      value: 'typeA'\n    }, {\n      name: 'Type B',\n      value: 'typeB'\n    }];\n    selectedData = null;\n    defaultActions = [{\n      id: 'edit',\n      icon: 'edit',\n      label: 'Edit item',\n      tooltip: 'Edit'\n    }, {\n      id: 'delete',\n      icon: 'trash',\n      label: 'Delete item',\n      tooltip: 'Delete'\n    }, {\n      id: 'run',\n      icon: 'play',\n      label: 'Run application',\n      tooltip: 'Run',\n      isPrimary: true\n    }];\n    showInfoPopup = false;\n    infoMessage = '';\n    message = \"\";\n    cardSkeletonPlaceholders = Array(11);\n    constructor(paginationService, router, route, datePipe, guardrailsService, fb, cdr, dialogService) {\n      this.paginationService = paginationService;\n      this.router = router;\n      this.route = route;\n      this.datePipe = datePipe;\n      this.guardrailsService = guardrailsService;\n      this.fb = fb;\n      this.cdr = cdr;\n      this.dialogService = dialogService;\n    }\n    ngOnInit() {\n      this.searchForm = this.fb.group({\n        search: ['']\n      });\n      this.filteredGuardrails = [...this.allGuardrails];\n      const pageParam = this.route.snapshot.queryParamMap.get('page');\n      if (pageParam) {\n        this.currentPage = parseInt(pageParam, 10);\n      }\n      this.filteredGuardrails = this.allGuardrails.map(item => {\n        const formattedDate = this.datePipe.transform(item.createdDate, 'MM/dd/yyyy') || '';\n        return {\n          ...item,\n          createdDate: formattedDate\n        };\n      });\n      // initialize the search listener ONCE\n      this.searchForm.get('search').valueChanges.pipe(startWith(''), debounceTime(300), distinctUntilChanged(), map(value => value?.toLowerCase() ?? '')).subscribe(searchText => {\n        this.filterGuardrails(searchText);\n      });\n      this.loadGuardrails();\n    }\n    loadGuardrails() {\n      this.isLoading = true;\n      this.guardrailsService.fetchAllGuardrails().subscribe({\n        next: data => {\n          this.allGuardrails = data.map(item => ({\n            id: String(item.id),\n            title: item.name,\n            description: item.description || 'No description',\n            tags: [{\n              label: item.configKey,\n              type: 'primary'\n            }, {\n              label: `ChatBot: ${item.chatBot ? 'Yes' : 'No'}`,\n              type: 'secondary'\n            }],\n            actions: [{\n              action: 'execute',\n              icon: 'play_circle',\n              tooltip: 'Run guardrail'\n            }, {\n              action: 'clone',\n              icon: 'content_copy',\n              tooltip: 'Clone guardrail'\n            }, {\n              action: 'delete',\n              icon: 'delete',\n              tooltip: 'Delete guardrail'\n            }],\n            createdDate: new Date().toLocaleDateString() // Optional: Use actual created date if available\n          }));\n          // after data is loaded, filter based on current search text\n          const currentSearch = this.searchForm.get('search')?.value?.toLowerCase() || '';\n          this.filterGuardrails(currentSearch);\n          this.filteredGuardrails = [...this.allGuardrails];\n          this.updateDisplayedGuardrails();\n          this.isLoading = false;\n        },\n        error: error => {\n          console.error('Error loading guardrails:', error);\n          this.isLoading = false;\n          this.dialogService.error({\n            title: 'Loading Failed',\n            message: 'Failed to load guardrails. Please try again.',\n            showRetryButton: true,\n            retryButtonText: 'Retry'\n          }).then(result => {\n            if (result.action === 'retry') {\n              this.loadGuardrails();\n            }\n          });\n        }\n      });\n    }\n    filterGuardrails(searchText) {\n      this.filteredGuardrails = this.allGuardrails.filter(gr => {\n        const inTitle = gr.title?.toLowerCase().includes(searchText);\n        const inDescription = gr.description?.toLowerCase().includes(searchText);\n        const inTags = Array.isArray(gr.tags) && gr.tags?.some(tag => tag.label?.toLowerCase().includes(searchText));\n        return inTitle || inDescription || inTags;\n      });\n      this.updateDisplayedGuardrails();\n    }\n    updateDisplayedGuardrails() {\n      const result = this.paginationService.getPaginatedItems(this.filteredGuardrails, this.currentPage, this.itemsPerPage);\n      this.displayedGuardrails = result.displayedItems;\n      this.totalPages = result.totalPages;\n    }\n    onCreateGuardrail() {\n      this.router.navigate(['/libraries/guardrails/create']);\n    }\n    // onCardClicked(guardrailId: string): void {\n    //   this.router.navigate(['/libraries/guardrails/edit', guardrailId], {\n    //     queryParams: { returnPage: this.currentPage },\n    //   });\n    // }\n    getHeaderIcons(guardrail) {\n      return [{\n        iconName: 'swords',\n        title: guardrail.toolType || 'Guardrails'\n      }, {\n        iconName: 'users',\n        title: `${guardrail.userCount || 10}`\n      }];\n    }\n    getFooterIcons(guardrail) {\n      return [{\n        iconName: 'user',\n        title: guardrail.owner || 'AAVA'\n      }, {\n        iconName: 'calendar-days',\n        title: guardrail.createdDate\n      }];\n    }\n    onActionClick(event, guardrailId) {\n      switch (event.actionId) {\n        case 'edit':\n          this.router.navigate([`/libraries/guardrails/edit/${guardrailId}`]);\n          break;\n        case 'delete':\n          this.confirmDeleteGuardrail(guardrailId);\n          break;\n        case 'run':\n          this.router.navigate([`/libraries/guardrails/edit/${guardrailId}`]);\n          break;\n        case 'copy':\n          this.duplicateGuardrail(guardrailId);\n          break;\n        default:\n          break;\n      }\n    }\n    confirmDeleteGuardrail(guardrailId) {\n      const guardrail = this.allGuardrails.find(item => item.id === guardrailId);\n      if (!guardrail) return;\n      this.dialogService.confirmation({\n        title: 'Delete Guardrail',\n        message: `Are you sure you want to delete \"${guardrail.title}\"? This action cannot be undone.`,\n        confirmButtonText: 'Delete',\n        cancelButtonText: 'Cancel',\n        confirmButtonVariant: 'danger',\n        icon: 'trash'\n      }).then(result => {\n        if (result.confirmed) {\n          this.deleteGuardrail(guardrailId);\n        }\n      });\n    }\n    deleteGuardrail(guardrailId) {\n      // Show loading dialog\n      this.dialogService.loading({\n        title: 'Deleting Guardrail...',\n        message: 'Please wait while we delete the guardrail.',\n        showProgress: false,\n        showCancelButton: false\n      });\n      this.guardrailsService.deleteGuardrail(Number(guardrailId)).subscribe({\n        next: response => {\n          this.dialogService.close(); // Close loading dialog\n          this.dialogService.success({\n            title: 'Guardrail Deleted',\n            message: 'Guardrail has been deleted successfully.'\n          });\n          // Remove the guardrail from local arrays\n          this.allGuardrails = this.allGuardrails.filter(gr => gr.id !== guardrailId);\n          this.filteredGuardrails = this.filteredGuardrails.filter(gr => gr.id !== guardrailId);\n          this.updateDisplayedGuardrails();\n        },\n        error: err => {\n          this.dialogService.close(); // Close loading dialog\n          const errorMessage = err?.error?.message || err?.message || 'Failed to delete guardrail. Please try again.';\n          this.dialogService.error({\n            title: 'Delete Failed',\n            message: errorMessage,\n            showRetryButton: true,\n            retryButtonText: 'Retry'\n          }).then(result => {\n            if (result.action === 'retry') {\n              this.deleteGuardrail(guardrailId);\n            }\n          });\n        }\n      });\n    }\n    // Remove old popup methods\n    // closeDeletePopup(): void { ... }\n    duplicateGuardrail(guardrailId) {\n      // Show loading dialog\n      this.dialogService.loading({\n        title: 'Duplicating Guardrail...',\n        message: 'Please wait while we duplicate the guardrail.',\n        showProgress: false,\n        showCancelButton: false\n      });\n      // Navigate to create page with clone parameter\n      setTimeout(() => {\n        this.dialogService.close();\n        this.router.navigate(['/libraries/guardrails/create'], {\n          queryParams: {\n            clone: guardrailId\n          }\n        });\n      }, 1000);\n    }\n    getTagsLine(guardrail) {\n      if (!guardrail.tags || !Array.isArray(guardrail.tags)) return '';\n      return guardrail.tags.map(tag => tag.label?.trim()).filter(label => !!label).join(' | ');\n    }\n    onSelectionChange(data) {\n      this.selectedData = data;\n      // Implement filter logic if needed\n    }\n    onPageChange(page) {\n      this.currentPage = page;\n      this.updateDisplayedGuardrails();\n    }\n    get showCreateCard() {\n      return this.currentPage === 1 && !this.isLoading && !this.error;\n    }\n    static ɵfac = function GuardrailsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || GuardrailsComponent)(i0.ɵɵdirectiveInject(i1.PaginationService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.DatePipe), i0.ɵɵdirectiveInject(i4.GuardrailsService), i0.ɵɵdirectiveInject(i5.FormBuilder), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i6.DialogService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: GuardrailsComponent,\n      selectors: [[\"app-guardrails\"]],\n      features: [i0.ɵɵProvidersFeature([DatePipe])],\n      decls: 13,\n      vars: 12,\n      consts: [[\"id\", \"guardrails-container\", 1, \"container-fluid\"], [\"id\", \"search-filter-container\", 1, \"row\", \"g-3\"], [1, \"col-12\", \"col-md-8\", \"col-lg-9\", \"col-xl-10\", \"search-section\"], [3, \"formGroup\"], [\"hoverEffect\", \"glow\", \"pressedEffect\", \"solid\", \"formControlName\", \"search\", 3, \"placeholder\"], [\"slot\", \"icon-start\", \"iconName\", \"search\", \"iconColor\", \"var(--color-brand-primary)\", 3, \"iconSize\"], [1, \"col-12\", \"col-md-4\", \"col-lg-3\", \"col-xl-2\", \"action-buttons\"], [3, \"selectionChange\", \"dropdownTitle\", \"options\"], [\"id\", \"prompts-card-container\", 1, \"row\", \"g-3\"], [\"iconColor\", \"#144692\", 1, \"col-12\", \"col-sm-6\", \"col-md-4\", \"col-lg-3\", \"col-xl-3\", \"col-xxl-2\", \"mt-5\", 3, \"cardClick\", \"type\", \"iconName\", \"title\", \"isLoading\"], [\"class\", \"col-12 d-flex justify-content-center align-items-center py-5\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"row\", 4, \"ngIf\"], [1, \"col-12\", \"d-flex\", \"justify-content-center\", \"align-items-center\", \"py-5\"], [1, \"text-center\"], [1, \"text-muted\"], [\"categoryIcon\", \"swords\", \"categoryTitle\", \"Guardrail\", \"categoryValue\", \"42\", 1, \"col-12\", \"col-sm-6\", \"col-md-4\", \"col-lg-3\", \"col-xl-3\", \"col-xxl-2\", \"mt-5\", 3, \"actionClick\", \"title\", \"description\", \"author\", \"date\", \"actions\", \"skeleton\"], [1, \"row\"], [1, \"col-12\", \"d-flex\", \"justify-content-center\", \"mt-4\"], [3, \"pageChange\", \"totalItems\", \"currentPage\", \"itemsPerPage\"]],\n      template: function GuardrailsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"form\", 3)(4, \"ava-textbox\", 4);\n          i0.ɵɵelement(5, \"ava-icon\", 5);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(6, \"div\", 6)(7, \"ava-dropdown\", 7);\n          i0.ɵɵlistener(\"selectionChange\", function GuardrailsComponent_Template_ava_dropdown_selectionChange_7_listener($event) {\n            return ctx.onSelectionChange($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(8, \"div\", 8)(9, \"ava-text-card\", 9);\n          i0.ɵɵlistener(\"cardClick\", function GuardrailsComponent_Template_ava_text_card_cardClick_9_listener() {\n            return ctx.onCreateGuardrail();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(10, GuardrailsComponent_div_10_Template, 4, 1, \"div\", 10)(11, GuardrailsComponent_ng_container_11_Template, 3, 8, \"ng-container\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(12, GuardrailsComponent_div_12_Template, 3, 3, \"div\", 12);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"formGroup\", ctx.searchForm);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"placeholder\", ctx.grLabels.searchPlaceholder);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"iconSize\", 16);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"dropdownTitle\", ctx.grLabels.dropdownTitle)(\"options\", ctx.guardrailsOptions);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"type\", \"create\")(\"iconName\", \"plus\")(\"title\", ctx.grLabels.title)(\"isLoading\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.filteredGuardrails.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.isLoading && ctx.displayedGuardrails.length === 0 ? ctx.cardSkeletonPlaceholders : ctx.displayedGuardrails);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.filteredGuardrails.length > 0);\n        }\n      },\n      dependencies: [CommonModule, i3.NgForOf, i3.NgIf, ReactiveFormsModule, i5.ɵNgNoValidate, i5.NgControlStatus, i5.NgControlStatusGroup, i5.FormGroupDirective, i5.FormControlName, PageFooterComponent, TextCardComponent, AvaTextboxComponent, IconComponent, DropdownComponent, LucideAngularModule, ConsoleCardComponent, TimeAgoPipe],\n      styles: [\".ava-dropdown {\\n  width: 100% !important;\\n}\\n\\n.mt-5[_ngcontent-%COMP%] {\\n  margin-top: 2rem;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3Byb2plY3RzL3NoYXJlZC9wYWdlcy9saWJyYXJpZXMvZ3VhcmRyYWlscy9ndWFyZHJhaWxzLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0Usc0JBQUE7QUFDRjs7QUFFQTtFQUNFLGdCQUFBO0FBQ0YiLCJzb3VyY2VzQ29udGVudCI6WyI6Om5nLWRlZXAgLmF2YS1kcm9wZG93biB7XHJcbiAgd2lkdGg6IDEwMCUgIWltcG9ydGFudDtcclxufVxyXG5cclxuLm10LTUge1xyXG4gIG1hcmdpbi10b3A6IDJyZW07XHJcbn1cclxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n  return GuardrailsComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "DatePipe", "PageFooterComponent", "AvaTextboxComponent", "DropdownComponent", "IconComponent", "TextCardComponent", "LucideAngularModule", "ReactiveFormsModule", "startWith", "debounceTime", "distinctUntilChanged", "map", "guardrailsLabels", "ConsoleCardComponent", "TimeAgoPipe", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "g<PERSON><PERSON><PERSON><PERSON>", "text", "ɵɵelementContainerStart", "ɵɵlistener", "GuardrailsComponent_ng_container_11_Template_ava_console_card_actionClick_1_listener", "$event", "guardrail_r3", "ɵɵrestoreView", "_r2", "$implicit", "ɵɵnextContext", "ɵɵresetView", "onActionClick", "id", "ɵɵproperty", "title", "description", "owner", "ɵɵpipeBind1", "createdDate", "defaultActions", "isLoading", "GuardrailsComponent_div_12_Template_app_page_footer_pageChange_2_listener", "_r4", "onPageChange", "filteredGuardrails", "length", "currentPage", "itemsPerPage", "GuardrailsComponent", "paginationService", "router", "route", "datePipe", "guardrailsService", "fb", "cdr", "dialogService", "labels", "searchForm", "search", "onSearchClick", "Error", "allGuardrails", "displayedGuardrails", "showDeletePopup", "guardrailToDelete", "error", "totalPages", "guardrailsOptions", "name", "value", "selectedData", "icon", "label", "tooltip", "isPrimary", "showInfoPopup", "infoMessage", "message", "cardSkeletonPlaceholders", "Array", "constructor", "ngOnInit", "group", "pageParam", "snapshot", "queryParamMap", "get", "parseInt", "item", "formattedDate", "transform", "valueChanges", "pipe", "toLowerCase", "subscribe", "searchText", "filterGuardrails", "loadGuardrails", "fetchAllGuardrails", "next", "data", "String", "tags", "config<PERSON><PERSON>", "type", "chatBot", "actions", "action", "Date", "toLocaleDateString", "currentSearch", "updateDisplayedGuardrails", "console", "showRetryButton", "retryButtonText", "then", "result", "filter", "gr", "inTitle", "includes", "inDescription", "inTags", "isArray", "some", "tag", "getPaginatedItems", "displayedItems", "onCreateGuardrail", "navigate", "getHeaderIcons", "guardrail", "iconName", "toolType", "userCount", "getFooterIcons", "event", "guardrailId", "actionId", "confirmDeleteGuardrail", "duplicateGuardrail", "find", "confirmation", "confirmButtonText", "cancelButtonText", "confirmButtonVariant", "confirmed", "deleteGuardrail", "loading", "showProgress", "showCancelButton", "Number", "response", "close", "success", "err", "errorMessage", "setTimeout", "queryParams", "clone", "getTagsLine", "trim", "join", "onSelectionChange", "page", "showCreateCard", "ɵɵdirectiveInject", "i1", "PaginationService", "i2", "Router", "ActivatedRoute", "i3", "i4", "GuardrailsService", "i5", "FormBuilder", "ChangeDetectorRef", "i6", "DialogService", "selectors", "features", "ɵɵProvidersFeature", "decls", "vars", "consts", "template", "GuardrailsComponent_Template", "rf", "ctx", "ɵɵelement", "GuardrailsComponent_Template_ava_dropdown_selectionChange_7_listener", "GuardrailsComponent_Template_ava_text_card_cardClick_9_listener", "ɵɵtemplate", "GuardrailsComponent_div_10_Template", "GuardrailsComponent_ng_container_11_Template", "GuardrailsComponent_div_12_Template", "searchPlaceholder", "dropdownTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "ɵNgNoValidate", "NgControlStatus", "NgControlStatusGroup", "FormGroupDirective", "FormControlName", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\pages\\libraries\\guardrails\\guardrails.component.ts", "C:\\console\\aava-ui-web\\projects\\shared\\pages\\libraries\\guardrails\\guardrails.component.html"], "sourcesContent": ["import { ChangeDetectorRef, Component, OnInit } from '@angular/core';\r\nimport { CommonModule, DatePipe } from '@angular/common';\r\nimport { Router, ActivatedRoute } from '@angular/router';\r\nimport { PageFooterComponent } from '@shared/components/page-footer/page-footer.component';\r\nimport { PaginationService } from '@shared/services/pagination.service';\r\nimport {\r\n  AvaTextboxComponent,\r\n  DropdownComponent,\r\n  DropdownOption,\r\n  IconComponent,\r\n  TextCardComponent,\r\n  PopupComponent,\r\n  DialogService\r\n} from '@ava/play-comp-library';\r\nimport { LucideAngularModule } from 'lucide-angular';\r\nimport { Guardrail } from '@shared/models/card.model';\r\nimport { GuardrailsService } from '@shared/services/guardrails.service';\r\nimport { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';\r\nimport { startWith, debounceTime, distinctUntilChanged, map } from 'rxjs';\r\nimport guardrailsLabels from './constants/guardrails-base.json';\r\nimport { ConsoleCardAction, ConsoleCardComponent } from \"@shared/components/console-card/console-card.component\";\r\nimport { TimeAgoPipe } from '@shared/pipes/time-ago.pipe';\r\n\r\n@Component({\r\n  selector: 'app-guardrails',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    ReactiveFormsModule,\r\n    PageFooterComponent,\r\n    TextCardComponent,\r\n    AvaTextboxComponent,\r\n    IconComponent,\r\n    DropdownComponent,\r\n    PopupComponent,\r\n    LucideAngularModule,\r\n    ConsoleCardComponent,\r\n    TimeAgoPipe,\r\n],\r\n  providers: [DatePipe],\r\n  templateUrl: './guardrails.component.html',\r\n  styleUrl: './guardrails.component.scss',\r\n})\r\nexport class GuardrailsComponent implements OnInit {\r\n  // Labels from constants file\r\n  grLabels = guardrailsLabels.labels;\r\n\r\n  searchForm!: FormGroup;\r\n  search: any;\r\n  onSearchClick() {\r\n    throw new Error('Method not implemented.');\r\n  }\r\n  allGuardrails: any = [];\r\n  filteredGuardrails: any[] = [];\r\n  displayedGuardrails: any[] = [];\r\n  isLoading: boolean = false;\r\n\r\n  // Delete popup properties\r\n  showDeletePopup: boolean = false;\r\n  guardrailToDelete: any = null;\r\n  error: string | null = null;\r\n  currentPage: number = 1;\r\n  itemsPerPage: number = 12;\r\n  totalPages: number = 1;\r\n  guardrailsOptions: DropdownOption[] = [\r\n    { name: 'All', value: 'all' },\r\n    { name: 'Type A', value: 'typeA' },\r\n    { name: 'Type B', value: 'typeB' },\r\n  ];\r\n  selectedData: any = null;\r\ndefaultActions: ConsoleCardAction[] = [\r\n    {\r\n      id: 'edit',\r\n      icon: 'edit',\r\n      label: 'Edit item',\r\n      tooltip: 'Edit',\r\n    },\r\n    {\r\n      id: 'delete',\r\n      icon: 'trash',\r\n      label: 'Delete item',\r\n      tooltip: 'Delete',\r\n    },\r\n    {\r\n      id: 'run',\r\n      icon: 'play',\r\n      label: 'Run application',\r\n      tooltip: 'Run',\r\n      isPrimary: true,\r\n    },\r\n  ];\r\n  showInfoPopup: boolean = false;\r\n  infoMessage: string = '';\r\n  message: string =\"\";\r\n  cardSkeletonPlaceholders = Array(11);\r\n\r\n  constructor(\r\n    private paginationService: PaginationService,\r\n    private router: Router,\r\n    private route: ActivatedRoute,\r\n    private datePipe: DatePipe,\r\n    private guardrailsService: GuardrailsService,\r\n    private fb: FormBuilder,\r\n    private cdr: ChangeDetectorRef,\r\n    private dialogService: DialogService,\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.searchForm = this.fb.group({\r\n      search: [''],\r\n    });\r\n    this.filteredGuardrails = [...this.allGuardrails];\r\n    const pageParam = this.route.snapshot.queryParamMap.get('page');\r\n    if (pageParam) {\r\n      this.currentPage = parseInt(pageParam, 10);\r\n    }\r\n    this.filteredGuardrails = this.allGuardrails.map((item: any) => {\r\n      const formattedDate =\r\n        this.datePipe.transform(item.createdDate, 'MM/dd/yyyy') || '';\r\n      return {\r\n        ...item,\r\n        createdDate: formattedDate,\r\n      };\r\n    });\r\n\r\n    // initialize the search listener ONCE\r\n    this.searchForm\r\n      .get('search')!\r\n      .valueChanges.pipe(\r\n        startWith(''),\r\n        debounceTime(300),\r\n        distinctUntilChanged(),\r\n        map((value) => value?.toLowerCase() ?? ''),\r\n      )\r\n      .subscribe((searchText) => {\r\n        this.filterGuardrails(searchText);\r\n      });\r\n    \r\n    this.loadGuardrails();\r\n  }\r\n\r\n  loadGuardrails(): void {\r\n    this.isLoading = true;\r\n    this.guardrailsService\r\n      .fetchAllGuardrails()\r\n      .subscribe({\r\n        next: (data: Guardrail[]) => {\r\n          this.allGuardrails = data.map((item: Guardrail) => ({\r\n            id: String(item.id),\r\n            title: item.name,\r\n            description: item.description || 'No description',\r\n            tags: [\r\n              { label: item.configKey, type: 'primary' },\r\n              {\r\n                label: `ChatBot: ${item.chatBot ? 'Yes' : 'No'}`,\r\n                type: 'secondary',\r\n              },\r\n            ],\r\n            actions: [\r\n              {\r\n                action: 'execute',\r\n                icon: 'play_circle',\r\n                tooltip: 'Run guardrail',\r\n              },\r\n              {\r\n                action: 'clone',\r\n                icon: 'content_copy',\r\n                tooltip: 'Clone guardrail',\r\n              },\r\n              { action: 'delete', icon: 'delete', tooltip: 'Delete guardrail' },\r\n            ],\r\n            createdDate: new Date().toLocaleDateString(), // Optional: Use actual created date if available\r\n          }));\r\n\r\n          // after data is loaded, filter based on current search text\r\n          const currentSearch =\r\n            this.searchForm.get('search')?.value?.toLowerCase() || '';\r\n          this.filterGuardrails(currentSearch);\r\n\r\n          this.filteredGuardrails = [...this.allGuardrails];\r\n          this.updateDisplayedGuardrails();\r\n          this.isLoading = false;\r\n        },\r\n        error: (error) => {\r\n          console.error('Error loading guardrails:', error);\r\n          this.isLoading = false;\r\n          this.dialogService.error({\r\n            title: 'Loading Failed',\r\n            message: 'Failed to load guardrails. Please try again.',\r\n            showRetryButton: true,\r\n            retryButtonText: 'Retry'\r\n          }).then(result => {\r\n            if (result.action === 'retry') {\r\n              this.loadGuardrails();\r\n            }\r\n          });\r\n        }\r\n      });\r\n  }\r\n\r\n  filterGuardrails(searchText: string): void {\r\n    this.filteredGuardrails = this.allGuardrails.filter((gr: any) => {\r\n      const inTitle = gr.title?.toLowerCase().includes(searchText);\r\n      const inDescription = gr.description?.toLowerCase().includes(searchText);\r\n      const inTags =\r\n        Array.isArray(gr.tags) &&\r\n        gr.tags?.some((tag: any) =>\r\n          tag.label?.toLowerCase().includes(searchText),\r\n        );\r\n\r\n      return inTitle || inDescription || inTags;\r\n    });\r\n\r\n    this.updateDisplayedGuardrails();\r\n  }\r\n\r\n  updateDisplayedGuardrails(): void {\r\n    const result = this.paginationService.getPaginatedItems(\r\n      this.filteredGuardrails,\r\n      this.currentPage,\r\n      this.itemsPerPage,\r\n    );\r\n    this.displayedGuardrails = result.displayedItems;\r\n    this.totalPages = result.totalPages;\r\n  }\r\n\r\n  onCreateGuardrail(): void {\r\n    this.router.navigate(['/libraries/guardrails/create']);\r\n  }\r\n\r\n  // onCardClicked(guardrailId: string): void {\r\n  //   this.router.navigate(['/libraries/guardrails/edit', guardrailId], {\r\n  //     queryParams: { returnPage: this.currentPage },\r\n  //   });\r\n  // }\r\n\r\n  getHeaderIcons(guardrail: any): { iconName: string; title: string }[] {\r\n    return [\r\n      { iconName: 'swords', title: guardrail.toolType || 'Guardrails' },\r\n      { iconName: 'users', title: `${guardrail.userCount || 10}` },\r\n    ];\r\n  }\r\n\r\n  getFooterIcons(guardrail: any): { iconName: string; title: string }[] {\r\n    return [\r\n      { iconName: 'user', title: guardrail.owner || 'AAVA' },\r\n      { iconName: 'calendar-days', title: guardrail.createdDate },\r\n    ];\r\n  }\r\n\r\n  onActionClick(\r\n    event: { actionId: string; action: ConsoleCardAction },\r\n    guardrailId: string,\r\n  ): void {\r\n    switch (event.actionId) {\r\n      case 'edit':\r\n        this.router.navigate([`/libraries/guardrails/edit/${guardrailId}`]);\r\n        break;\r\n      case 'delete':\r\n        this.confirmDeleteGuardrail(guardrailId);\r\n        break;\r\n      case 'run':\r\n        this.router.navigate([`/libraries/guardrails/edit/${guardrailId}`]);\r\n        break;\r\n      case 'copy':\r\n        this.duplicateGuardrail(guardrailId);\r\n        break;\r\n      default:\r\n        break;\r\n    }\r\n  }\r\n\r\n  confirmDeleteGuardrail(guardrailId: string): void {\r\n    const guardrail = this.allGuardrails.find((item :any)=> item.id === guardrailId);\r\n    if (!guardrail) return;\r\n\r\n    this.dialogService.confirmation({\r\n      title: 'Delete Guardrail',\r\n      message: `Are you sure you want to delete \"${guardrail.title}\"? This action cannot be undone.`,\r\n      confirmButtonText: 'Delete',\r\n      cancelButtonText: 'Cancel',\r\n      confirmButtonVariant: 'danger',\r\n      icon:'trash',\r\n    }).then(result => {\r\n      if (result.confirmed) {\r\n        this.deleteGuardrail(guardrailId);\r\n      }\r\n    });\r\n  }\r\n\r\n  deleteGuardrail(guardrailId: string): void {\r\n    // Show loading dialog\r\n    this.dialogService.loading({\r\n      title: 'Deleting Guardrail...',\r\n      message: 'Please wait while we delete the guardrail.',\r\n      showProgress: false,\r\n      showCancelButton: false\r\n    });\r\n\r\n    this.guardrailsService.deleteGuardrail(Number(guardrailId)).subscribe({\r\n      next: (response) => {\r\n        this.dialogService.close(); // Close loading dialog\r\n        \r\n        this.dialogService.success({\r\n          title: 'Guardrail Deleted',\r\n          message: 'Guardrail has been deleted successfully.'\r\n        });\r\n\r\n        // Remove the guardrail from local arrays\r\n        this.allGuardrails = this.allGuardrails.filter((gr:any) => gr.id !== guardrailId);\r\n        this.filteredGuardrails = this.filteredGuardrails.filter(gr => gr.id !== guardrailId);\r\n        this.updateDisplayedGuardrails();\r\n      },\r\n      error: (err) => {\r\n        this.dialogService.close(); // Close loading dialog\r\n        \r\n        const errorMessage = err?.error?.message || err?.message || 'Failed to delete guardrail. Please try again.';\r\n        this.dialogService.error({\r\n          title: 'Delete Failed',\r\n          message: errorMessage,\r\n          showRetryButton: true,\r\n          retryButtonText: 'Retry'\r\n        }).then(result => {\r\n          if (result.action === 'retry') {\r\n            this.deleteGuardrail(guardrailId);\r\n          }\r\n        });\r\n      },\r\n    });\r\n  }\r\n\r\n  // Remove old popup methods\r\n  // closeDeletePopup(): void { ... }\r\n\r\n  duplicateGuardrail(guardrailId: string): void {\r\n    // Show loading dialog\r\n    this.dialogService.loading({\r\n      title: 'Duplicating Guardrail...',\r\n      message: 'Please wait while we duplicate the guardrail.',\r\n      showProgress: false,\r\n      showCancelButton: false\r\n    });\r\n\r\n    // Navigate to create page with clone parameter\r\n    setTimeout(() => {\r\n      this.dialogService.close();\r\n      this.router.navigate(['/libraries/guardrails/create'], {\r\n        queryParams: { clone: guardrailId }\r\n      });\r\n    }, 1000);\r\n  }\r\n\r\n  getTagsLine(guardrail: any): string {\r\n    if (!guardrail.tags || !Array.isArray(guardrail.tags)) return '';\r\n    return guardrail.tags\r\n      .map((tag: any) => tag.label?.trim())\r\n      .filter((label: string | undefined) => !!label)\r\n      .join(' | ');\r\n  }\r\n\r\n  onSelectionChange(data: any) {\r\n    this.selectedData = data;\r\n    // Implement filter logic if needed\r\n  }\r\n\r\n  onPageChange(page: number): void {\r\n    this.currentPage = page;\r\n    this.updateDisplayedGuardrails();\r\n  }\r\n\r\n  get showCreateCard(): boolean {\r\n    return this.currentPage === 1 && !this.isLoading && !this.error;\r\n  }\r\n}\r\n", "<div id=\"guardrails-container\" class=\"container-fluid\">\r\n  <div id=\"search-filter-container\" class=\"row g-3\">\r\n    <div class=\"col-12 col-md-8 col-lg-9 col-xl-10 search-section\">\r\n      <form [formGroup]=\"searchForm\">\r\n        <ava-textbox\r\n          [placeholder]=\"grLabels.searchPlaceholder\"\r\n          hoverEffect=\"glow\"\r\n          pressedEffect=\"solid\"\r\n          formControlName=\"search\"\r\n        >\r\n          <ava-icon\r\n            slot=\"icon-start\"\r\n            iconName=\"search\"\r\n            [iconSize]=\"16\"\r\n            iconColor=\"var(--color-brand-primary)\"\r\n          >\r\n          </ava-icon>\r\n        </ava-textbox>\r\n      </form>\r\n    </div>\r\n    <div class=\"col-12 col-md-4 col-lg-3 col-xl-2 action-buttons\">\r\n      <ava-dropdown\r\n        [dropdownTitle]=\"grLabels.dropdownTitle\"\r\n        [options]=\"guardrailsOptions\"\r\n        (selectionChange)=\"onSelectionChange($event)\"\r\n      >\r\n      </ava-dropdown>\r\n    </div>\r\n  </div>\r\n\r\n  <div id=\"prompts-card-container\" class=\"row g-3\">\r\n    <ava-text-card\r\n      class=\"col-12 col-sm-6 col-md-4 col-lg-3 col-xl-3 col-xxl-2 mt-5\"\r\n      [type]=\"'create'\"\r\n      [iconName]=\"'plus'\"\r\n      iconColor=\"#144692\"\r\n      [title]=\"grLabels.title\"\r\n      (cardClick)=\"onCreateGuardrail()\"\r\n      [isLoading]=\"isLoading\"\r\n    >\r\n    </ava-text-card>\r\n\r\n    <!-- No Results Message -->\r\n    <div\r\n      class=\"col-12 d-flex justify-content-center align-items-center py-5\"\r\n      *ngIf=\"!isLoading && filteredGuardrails.length === 0\"\r\n    >\r\n      <div class=\"text-center\">\r\n        <h5 class=\"text-muted\">{{grLabels.text}}</h5>\r\n      </div>\r\n    </div>\r\n\r\n    <ng-container *ngFor=\"let guardrail of isLoading && displayedGuardrails.length === 0 ? cardSkeletonPlaceholders : displayedGuardrails\">\r\n      <ava-console-card\r\n        class=\"col-12 col-sm-6 col-md-4 col-lg-3 col-xl-3 col-xxl-2 mt-5\"\r\n        [title]=\"guardrail?.title\"\r\n        [description]=\"guardrail?.description\"\r\n        categoryIcon=\"swords\"\r\n        categoryTitle=\"Guardrail\"\r\n        categoryValue=\"42\"\r\n        [author]=\"guardrail?.owner || 'AAVA'\"\r\n        [date]=\"guardrail?.createdDate | timeAgo\"\r\n        [actions]=\"defaultActions\"\r\n        (actionClick)=\"onActionClick($event, guardrail.id)\"\r\n        [skeleton]=\"isLoading\"\r\n      >\r\n      </ava-console-card>\r\n    </ng-container>\r\n  </div>\r\n\r\n  <!-- Pagination Footer -->\r\n  <div class=\"row\" *ngIf=\"filteredGuardrails.length > 0\">\r\n    <div class=\"col-12 d-flex justify-content-center mt-4\">\r\n      <app-page-footer\r\n        [totalItems]=\"filteredGuardrails.length + 1\"\r\n        [currentPage]=\"currentPage\"\r\n        [itemsPerPage]=\"itemsPerPage\"\r\n        (pageChange)=\"onPageChange($event)\"\r\n      ></app-page-footer>\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n"], "mappings": "AACA,SAASA,YAAY,EAAEC,QAAQ,QAAQ,iBAAiB;AAExD,SAASC,mBAAmB,QAAQ,sDAAsD;AAE1F,SACEC,mBAAmB,EACnBC,iBAAiB,EAEjBC,aAAa,EACbC,iBAAiB,QAGZ,wBAAwB;AAC/B,SAASC,mBAAmB,QAAQ,gBAAgB;AAGpD,SAAiCC,mBAAmB,QAAQ,gBAAgB;AAC5E,SAASC,SAAS,EAAEC,YAAY,EAAEC,oBAAoB,EAAEC,GAAG,QAAQ,MAAM;AACzE,OAAOC,gBAAgB,MAAM,kCAAkC;AAC/D,SAA4BC,oBAAoB,QAAQ,wDAAwD;AAChH,SAASC,WAAW,QAAQ,6BAA6B;;;;;;;;;;IC2BjDC,EALJ,CAAAC,cAAA,cAGC,cAC0B,aACA;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAE5CF,EAF4C,CAAAG,YAAA,EAAK,EACzC,EACF;;;;IAFqBH,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,QAAA,CAAAC,IAAA,CAAiB;;;;;;IAI5CR,EAAA,CAAAS,uBAAA,GAAuI;IACrIT,EAAA,CAAAC,cAAA,2BAYC;;IAFCD,EAAA,CAAAU,UAAA,yBAAAC,qFAAAC,MAAA;MAAA,MAAAC,YAAA,GAAAb,EAAA,CAAAc,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAV,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAeZ,MAAA,CAAAa,aAAA,CAAAP,MAAA,EAAAC,YAAA,CAAAO,EAAA,CAAmC;IAAA,EAAC;IAGrDpB,EAAA,CAAAG,YAAA,EAAmB;;;;;;IAXjBH,EAAA,CAAAI,SAAA,EAA0B;IAS1BJ,EATA,CAAAqB,UAAA,UAAAR,YAAA,kBAAAA,YAAA,CAAAS,KAAA,CAA0B,gBAAAT,YAAA,kBAAAA,YAAA,CAAAU,WAAA,CACY,YAAAV,YAAA,kBAAAA,YAAA,CAAAW,KAAA,YAID,SAAAxB,EAAA,CAAAyB,WAAA,OAAAZ,YAAA,kBAAAA,YAAA,CAAAa,WAAA,EACI,YAAApB,MAAA,CAAAqB,cAAA,CACf,aAAArB,MAAA,CAAAsB,SAAA,CAEJ;;;;;;IASxB5B,EAFJ,CAAAC,cAAA,cAAuD,cACE,0BAMpD;IADCD,EAAA,CAAAU,UAAA,wBAAAmB,0EAAAjB,MAAA;MAAAZ,EAAA,CAAAc,aAAA,CAAAgB,GAAA;MAAA,MAAAxB,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAcZ,MAAA,CAAAyB,YAAA,CAAAnB,MAAA,CAAoB;IAAA,EAAC;IAGzCZ,EAFK,CAAAG,YAAA,EAAkB,EACf,EACF;;;;IANAH,EAAA,CAAAI,SAAA,GAA4C;IAE5CJ,EAFA,CAAAqB,UAAA,eAAAf,MAAA,CAAA0B,kBAAA,CAAAC,MAAA,KAA4C,gBAAA3B,MAAA,CAAA4B,WAAA,CACjB,iBAAA5B,MAAA,CAAA6B,YAAA,CACE;;;ADjCrC,WAAaC,mBAAmB;EAA1B,MAAOA,mBAAmB;IAsDpBC,iBAAA;IACAC,MAAA;IACAC,KAAA;IACAC,QAAA;IACAC,iBAAA;IACAC,EAAA;IACAC,GAAA;IACAC,aAAA;IA5DV;IACArC,QAAQ,GAAGV,gBAAgB,CAACgD,MAAM;IAElCC,UAAU;IACVC,MAAM;IACNC,aAAaA,CAAA;MACX,MAAM,IAAIC,KAAK,CAAC,yBAAyB,CAAC;IAC5C;IACAC,aAAa,GAAQ,EAAE;IACvBlB,kBAAkB,GAAU,EAAE;IAC9BmB,mBAAmB,GAAU,EAAE;IAC/BvB,SAAS,GAAY,KAAK;IAE1B;IACAwB,eAAe,GAAY,KAAK;IAChCC,iBAAiB,GAAQ,IAAI;IAC7BC,KAAK,GAAkB,IAAI;IAC3BpB,WAAW,GAAW,CAAC;IACvBC,YAAY,GAAW,EAAE;IACzBoB,UAAU,GAAW,CAAC;IACtBC,iBAAiB,GAAqB,CACpC;MAAEC,IAAI,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAK,CAAE,EAC7B;MAAED,IAAI,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAO,CAAE,EAClC;MAAED,IAAI,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAO,CAAE,CACnC;IACDC,YAAY,GAAQ,IAAI;IAC1BhC,cAAc,GAAwB,CAClC;MACEP,EAAE,EAAE,MAAM;MACVwC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,WAAW;MAClBC,OAAO,EAAE;KACV,EACD;MACE1C,EAAE,EAAE,QAAQ;MACZwC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,aAAa;MACpBC,OAAO,EAAE;KACV,EACD;MACE1C,EAAE,EAAE,KAAK;MACTwC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,iBAAiB;MACxBC,OAAO,EAAE,KAAK;MACdC,SAAS,EAAE;KACZ,CACF;IACDC,aAAa,GAAY,KAAK;IAC9BC,WAAW,GAAW,EAAE;IACxBC,OAAO,GAAU,EAAE;IACnBC,wBAAwB,GAAGC,KAAK,CAAC,EAAE,CAAC;IAEpCC,YACUhC,iBAAoC,EACpCC,MAAc,EACdC,KAAqB,EACrBC,QAAkB,EAClBC,iBAAoC,EACpCC,EAAe,EACfC,GAAsB,EACtBC,aAA4B;MAP5B,KAAAP,iBAAiB,GAAjBA,iBAAiB;MACjB,KAAAC,MAAM,GAANA,MAAM;MACN,KAAAC,KAAK,GAALA,KAAK;MACL,KAAAC,QAAQ,GAARA,QAAQ;MACR,KAAAC,iBAAiB,GAAjBA,iBAAiB;MACjB,KAAAC,EAAE,GAAFA,EAAE;MACF,KAAAC,GAAG,GAAHA,GAAG;MACH,KAAAC,aAAa,GAAbA,aAAa;IACpB;IAEH0B,QAAQA,CAAA;MACN,IAAI,CAACxB,UAAU,GAAG,IAAI,CAACJ,EAAE,CAAC6B,KAAK,CAAC;QAC9BxB,MAAM,EAAE,CAAC,EAAE;OACZ,CAAC;MACF,IAAI,CAACf,kBAAkB,GAAG,CAAC,GAAG,IAAI,CAACkB,aAAa,CAAC;MACjD,MAAMsB,SAAS,GAAG,IAAI,CAACjC,KAAK,CAACkC,QAAQ,CAACC,aAAa,CAACC,GAAG,CAAC,MAAM,CAAC;MAC/D,IAAIH,SAAS,EAAE;QACb,IAAI,CAACtC,WAAW,GAAG0C,QAAQ,CAACJ,SAAS,EAAE,EAAE,CAAC;MAC5C;MACA,IAAI,CAACxC,kBAAkB,GAAG,IAAI,CAACkB,aAAa,CAACtD,GAAG,CAAEiF,IAAS,IAAI;QAC7D,MAAMC,aAAa,GACjB,IAAI,CAACtC,QAAQ,CAACuC,SAAS,CAACF,IAAI,CAACnD,WAAW,EAAE,YAAY,CAAC,IAAI,EAAE;QAC/D,OAAO;UACL,GAAGmD,IAAI;UACPnD,WAAW,EAAEoD;SACd;MACH,CAAC,CAAC;MAEF;MACA,IAAI,CAAChC,UAAU,CACZ6B,GAAG,CAAC,QAAQ,CAAE,CACdK,YAAY,CAACC,IAAI,CAChBxF,SAAS,CAAC,EAAE,CAAC,EACbC,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBC,GAAG,CAAE8D,KAAK,IAAKA,KAAK,EAAEwB,WAAW,EAAE,IAAI,EAAE,CAAC,CAC3C,CACAC,SAAS,CAAEC,UAAU,IAAI;QACxB,IAAI,CAACC,gBAAgB,CAACD,UAAU,CAAC;MACnC,CAAC,CAAC;MAEJ,IAAI,CAACE,cAAc,EAAE;IACvB;IAEAA,cAAcA,CAAA;MACZ,IAAI,CAAC1D,SAAS,GAAG,IAAI;MACrB,IAAI,CAACa,iBAAiB,CACnB8C,kBAAkB,EAAE,CACpBJ,SAAS,CAAC;QACTK,IAAI,EAAGC,IAAiB,IAAI;UAC1B,IAAI,CAACvC,aAAa,GAAGuC,IAAI,CAAC7F,GAAG,CAAEiF,IAAe,KAAM;YAClDzD,EAAE,EAAEsE,MAAM,CAACb,IAAI,CAACzD,EAAE,CAAC;YACnBE,KAAK,EAAEuD,IAAI,CAACpB,IAAI;YAChBlC,WAAW,EAAEsD,IAAI,CAACtD,WAAW,IAAI,gBAAgB;YACjDoE,IAAI,EAAE,CACJ;cAAE9B,KAAK,EAAEgB,IAAI,CAACe,SAAS;cAAEC,IAAI,EAAE;YAAS,CAAE,EAC1C;cACEhC,KAAK,EAAE,YAAYgB,IAAI,CAACiB,OAAO,GAAG,KAAK,GAAG,IAAI,EAAE;cAChDD,IAAI,EAAE;aACP,CACF;YACDE,OAAO,EAAE,CACP;cACEC,MAAM,EAAE,SAAS;cACjBpC,IAAI,EAAE,aAAa;cACnBE,OAAO,EAAE;aACV,EACD;cACEkC,MAAM,EAAE,OAAO;cACfpC,IAAI,EAAE,cAAc;cACpBE,OAAO,EAAE;aACV,EACD;cAAEkC,MAAM,EAAE,QAAQ;cAAEpC,IAAI,EAAE,QAAQ;cAAEE,OAAO,EAAE;YAAkB,CAAE,CAClE;YACDpC,WAAW,EAAE,IAAIuE,IAAI,EAAE,CAACC,kBAAkB,EAAE,CAAE;WAC/C,CAAC,CAAC;UAEH;UACA,MAAMC,aAAa,GACjB,IAAI,CAACrD,UAAU,CAAC6B,GAAG,CAAC,QAAQ,CAAC,EAAEjB,KAAK,EAAEwB,WAAW,EAAE,IAAI,EAAE;UAC3D,IAAI,CAACG,gBAAgB,CAACc,aAAa,CAAC;UAEpC,IAAI,CAACnE,kBAAkB,GAAG,CAAC,GAAG,IAAI,CAACkB,aAAa,CAAC;UACjD,IAAI,CAACkD,yBAAyB,EAAE;UAChC,IAAI,CAACxE,SAAS,GAAG,KAAK;QACxB,CAAC;QACD0B,KAAK,EAAGA,KAAK,IAAI;UACf+C,OAAO,CAAC/C,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;UACjD,IAAI,CAAC1B,SAAS,GAAG,KAAK;UACtB,IAAI,CAACgB,aAAa,CAACU,KAAK,CAAC;YACvBhC,KAAK,EAAE,gBAAgB;YACvB4C,OAAO,EAAE,8CAA8C;YACvDoC,eAAe,EAAE,IAAI;YACrBC,eAAe,EAAE;WAClB,CAAC,CAACC,IAAI,CAACC,MAAM,IAAG;YACf,IAAIA,MAAM,CAACT,MAAM,KAAK,OAAO,EAAE;cAC7B,IAAI,CAACV,cAAc,EAAE;YACvB;UACF,CAAC,CAAC;QACJ;OACD,CAAC;IACN;IAEAD,gBAAgBA,CAACD,UAAkB;MACjC,IAAI,CAACpD,kBAAkB,GAAG,IAAI,CAACkB,aAAa,CAACwD,MAAM,CAAEC,EAAO,IAAI;QAC9D,MAAMC,OAAO,GAAGD,EAAE,CAACrF,KAAK,EAAE4D,WAAW,EAAE,CAAC2B,QAAQ,CAACzB,UAAU,CAAC;QAC5D,MAAM0B,aAAa,GAAGH,EAAE,CAACpF,WAAW,EAAE2D,WAAW,EAAE,CAAC2B,QAAQ,CAACzB,UAAU,CAAC;QACxE,MAAM2B,MAAM,GACV3C,KAAK,CAAC4C,OAAO,CAACL,EAAE,CAAChB,IAAI,CAAC,IACtBgB,EAAE,CAAChB,IAAI,EAAEsB,IAAI,CAAEC,GAAQ,IACrBA,GAAG,CAACrD,KAAK,EAAEqB,WAAW,EAAE,CAAC2B,QAAQ,CAACzB,UAAU,CAAC,CAC9C;QAEH,OAAOwB,OAAO,IAAIE,aAAa,IAAIC,MAAM;MAC3C,CAAC,CAAC;MAEF,IAAI,CAACX,yBAAyB,EAAE;IAClC;IAEAA,yBAAyBA,CAAA;MACvB,MAAMK,MAAM,GAAG,IAAI,CAACpE,iBAAiB,CAAC8E,iBAAiB,CACrD,IAAI,CAACnF,kBAAkB,EACvB,IAAI,CAACE,WAAW,EAChB,IAAI,CAACC,YAAY,CAClB;MACD,IAAI,CAACgB,mBAAmB,GAAGsD,MAAM,CAACW,cAAc;MAChD,IAAI,CAAC7D,UAAU,GAAGkD,MAAM,CAAClD,UAAU;IACrC;IAEA8D,iBAAiBA,CAAA;MACf,IAAI,CAAC/E,MAAM,CAACgF,QAAQ,CAAC,CAAC,8BAA8B,CAAC,CAAC;IACxD;IAEA;IACA;IACA;IACA;IACA;IAEAC,cAAcA,CAACC,SAAc;MAC3B,OAAO,CACL;QAAEC,QAAQ,EAAE,QAAQ;QAAEnG,KAAK,EAAEkG,SAAS,CAACE,QAAQ,IAAI;MAAY,CAAE,EACjE;QAAED,QAAQ,EAAE,OAAO;QAAEnG,KAAK,EAAE,GAAGkG,SAAS,CAACG,SAAS,IAAI,EAAE;MAAE,CAAE,CAC7D;IACH;IAEAC,cAAcA,CAACJ,SAAc;MAC3B,OAAO,CACL;QAAEC,QAAQ,EAAE,MAAM;QAAEnG,KAAK,EAAEkG,SAAS,CAAChG,KAAK,IAAI;MAAM,CAAE,EACtD;QAAEiG,QAAQ,EAAE,eAAe;QAAEnG,KAAK,EAAEkG,SAAS,CAAC9F;MAAW,CAAE,CAC5D;IACH;IAEAP,aAAaA,CACX0G,KAAsD,EACtDC,WAAmB;MAEnB,QAAQD,KAAK,CAACE,QAAQ;QACpB,KAAK,MAAM;UACT,IAAI,CAACzF,MAAM,CAACgF,QAAQ,CAAC,CAAC,8BAA8BQ,WAAW,EAAE,CAAC,CAAC;UACnE;QACF,KAAK,QAAQ;UACX,IAAI,CAACE,sBAAsB,CAACF,WAAW,CAAC;UACxC;QACF,KAAK,KAAK;UACR,IAAI,CAACxF,MAAM,CAACgF,QAAQ,CAAC,CAAC,8BAA8BQ,WAAW,EAAE,CAAC,CAAC;UACnE;QACF,KAAK,MAAM;UACT,IAAI,CAACG,kBAAkB,CAACH,WAAW,CAAC;UACpC;QACF;UACE;MACJ;IACF;IAEAE,sBAAsBA,CAACF,WAAmB;MACxC,MAAMN,SAAS,GAAG,IAAI,CAACtE,aAAa,CAACgF,IAAI,CAAErD,IAAS,IAAIA,IAAI,CAACzD,EAAE,KAAK0G,WAAW,CAAC;MAChF,IAAI,CAACN,SAAS,EAAE;MAEhB,IAAI,CAAC5E,aAAa,CAACuF,YAAY,CAAC;QAC9B7G,KAAK,EAAE,kBAAkB;QACzB4C,OAAO,EAAE,oCAAoCsD,SAAS,CAAClG,KAAK,kCAAkC;QAC9F8G,iBAAiB,EAAE,QAAQ;QAC3BC,gBAAgB,EAAE,QAAQ;QAC1BC,oBAAoB,EAAE,QAAQ;QAC9B1E,IAAI,EAAC;OACN,CAAC,CAAC4C,IAAI,CAACC,MAAM,IAAG;QACf,IAAIA,MAAM,CAAC8B,SAAS,EAAE;UACpB,IAAI,CAACC,eAAe,CAACV,WAAW,CAAC;QACnC;MACF,CAAC,CAAC;IACJ;IAEAU,eAAeA,CAACV,WAAmB;MACjC;MACA,IAAI,CAAClF,aAAa,CAAC6F,OAAO,CAAC;QACzBnH,KAAK,EAAE,uBAAuB;QAC9B4C,OAAO,EAAE,4CAA4C;QACrDwE,YAAY,EAAE,KAAK;QACnBC,gBAAgB,EAAE;OACnB,CAAC;MAEF,IAAI,CAAClG,iBAAiB,CAAC+F,eAAe,CAACI,MAAM,CAACd,WAAW,CAAC,CAAC,CAAC3C,SAAS,CAAC;QACpEK,IAAI,EAAGqD,QAAQ,IAAI;UACjB,IAAI,CAACjG,aAAa,CAACkG,KAAK,EAAE,CAAC,CAAC;UAE5B,IAAI,CAAClG,aAAa,CAACmG,OAAO,CAAC;YACzBzH,KAAK,EAAE,mBAAmB;YAC1B4C,OAAO,EAAE;WACV,CAAC;UAEF;UACA,IAAI,CAAChB,aAAa,GAAG,IAAI,CAACA,aAAa,CAACwD,MAAM,CAAEC,EAAM,IAAKA,EAAE,CAACvF,EAAE,KAAK0G,WAAW,CAAC;UACjF,IAAI,CAAC9F,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAAC0E,MAAM,CAACC,EAAE,IAAIA,EAAE,CAACvF,EAAE,KAAK0G,WAAW,CAAC;UACrF,IAAI,CAAC1B,yBAAyB,EAAE;QAClC,CAAC;QACD9C,KAAK,EAAG0F,GAAG,IAAI;UACb,IAAI,CAACpG,aAAa,CAACkG,KAAK,EAAE,CAAC,CAAC;UAE5B,MAAMG,YAAY,GAAGD,GAAG,EAAE1F,KAAK,EAAEY,OAAO,IAAI8E,GAAG,EAAE9E,OAAO,IAAI,+CAA+C;UAC3G,IAAI,CAACtB,aAAa,CAACU,KAAK,CAAC;YACvBhC,KAAK,EAAE,eAAe;YACtB4C,OAAO,EAAE+E,YAAY;YACrB3C,eAAe,EAAE,IAAI;YACrBC,eAAe,EAAE;WAClB,CAAC,CAACC,IAAI,CAACC,MAAM,IAAG;YACf,IAAIA,MAAM,CAACT,MAAM,KAAK,OAAO,EAAE;cAC7B,IAAI,CAACwC,eAAe,CAACV,WAAW,CAAC;YACnC;UACF,CAAC,CAAC;QACJ;OACD,CAAC;IACJ;IAEA;IACA;IAEAG,kBAAkBA,CAACH,WAAmB;MACpC;MACA,IAAI,CAAClF,aAAa,CAAC6F,OAAO,CAAC;QACzBnH,KAAK,EAAE,0BAA0B;QACjC4C,OAAO,EAAE,+CAA+C;QACxDwE,YAAY,EAAE,KAAK;QACnBC,gBAAgB,EAAE;OACnB,CAAC;MAEF;MACAO,UAAU,CAAC,MAAK;QACd,IAAI,CAACtG,aAAa,CAACkG,KAAK,EAAE;QAC1B,IAAI,CAACxG,MAAM,CAACgF,QAAQ,CAAC,CAAC,8BAA8B,CAAC,EAAE;UACrD6B,WAAW,EAAE;YAAEC,KAAK,EAAEtB;UAAW;SAClC,CAAC;MACJ,CAAC,EAAE,IAAI,CAAC;IACV;IAEAuB,WAAWA,CAAC7B,SAAc;MACxB,IAAI,CAACA,SAAS,CAAC7B,IAAI,IAAI,CAACvB,KAAK,CAAC4C,OAAO,CAACQ,SAAS,CAAC7B,IAAI,CAAC,EAAE,OAAO,EAAE;MAChE,OAAO6B,SAAS,CAAC7B,IAAI,CAClB/F,GAAG,CAAEsH,GAAQ,IAAKA,GAAG,CAACrD,KAAK,EAAEyF,IAAI,EAAE,CAAC,CACpC5C,MAAM,CAAE7C,KAAyB,IAAK,CAAC,CAACA,KAAK,CAAC,CAC9C0F,IAAI,CAAC,KAAK,CAAC;IAChB;IAEAC,iBAAiBA,CAAC/D,IAAS;MACzB,IAAI,CAAC9B,YAAY,GAAG8B,IAAI;MACxB;IACF;IAEA1D,YAAYA,CAAC0H,IAAY;MACvB,IAAI,CAACvH,WAAW,GAAGuH,IAAI;MACvB,IAAI,CAACrD,yBAAyB,EAAE;IAClC;IAEA,IAAIsD,cAAcA,CAAA;MAChB,OAAO,IAAI,CAACxH,WAAW,KAAK,CAAC,IAAI,CAAC,IAAI,CAACN,SAAS,IAAI,CAAC,IAAI,CAAC0B,KAAK;IACjE;;uCAzUWlB,mBAAmB,EAAApC,EAAA,CAAA2J,iBAAA,CAAAC,EAAA,CAAAC,iBAAA,GAAA7J,EAAA,CAAA2J,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAA/J,EAAA,CAAA2J,iBAAA,CAAAG,EAAA,CAAAE,cAAA,GAAAhK,EAAA,CAAA2J,iBAAA,CAAAM,EAAA,CAAAhL,QAAA,GAAAe,EAAA,CAAA2J,iBAAA,CAAAO,EAAA,CAAAC,iBAAA,GAAAnK,EAAA,CAAA2J,iBAAA,CAAAS,EAAA,CAAAC,WAAA,GAAArK,EAAA,CAAA2J,iBAAA,CAAA3J,EAAA,CAAAsK,iBAAA,GAAAtK,EAAA,CAAA2J,iBAAA,CAAAY,EAAA,CAAAC,aAAA;IAAA;;YAAnBpI,mBAAmB;MAAAqI,SAAA;MAAAC,QAAA,GAAA1K,EAAA,CAAA2K,kBAAA,CAJnB,CAAC1L,QAAQ,CAAC;MAAA2L,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCnCfjL,EAJR,CAAAC,cAAA,aAAuD,aACH,aACe,cAC9B,qBAM5B;UACCD,EAAA,CAAAmL,SAAA,kBAMW;UAGjBnL,EAFI,CAAAG,YAAA,EAAc,EACT,EACH;UAEJH,EADF,CAAAC,cAAA,aAA8D,sBAK3D;UADCD,EAAA,CAAAU,UAAA,6BAAA0K,qEAAAxK,MAAA;YAAA,OAAmBsK,GAAA,CAAA1B,iBAAA,CAAA5I,MAAA,CAAyB;UAAA,EAAC;UAInDZ,EAFI,CAAAG,YAAA,EAAe,EACX,EACF;UAGJH,EADF,CAAAC,cAAA,aAAiD,uBAS9C;UAFCD,EAAA,CAAAU,UAAA,uBAAA2K,gEAAA;YAAA,OAAaH,GAAA,CAAA7D,iBAAA,EAAmB;UAAA,EAAC;UAGnCrH,EAAA,CAAAG,YAAA,EAAgB;UAYhBH,EATA,CAAAsL,UAAA,KAAAC,mCAAA,kBAGC,KAAAC,4CAAA,2BAMsI;UAgBzIxL,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAsL,UAAA,KAAAG,mCAAA,kBAAuD;UAUzDzL,EAAA,CAAAG,YAAA,EAAM;;;UA9EMH,EAAA,CAAAI,SAAA,GAAwB;UAAxBJ,EAAA,CAAAqB,UAAA,cAAA6J,GAAA,CAAApI,UAAA,CAAwB;UAE1B9C,EAAA,CAAAI,SAAA,EAA0C;UAA1CJ,EAAA,CAAAqB,UAAA,gBAAA6J,GAAA,CAAA3K,QAAA,CAAAmL,iBAAA,CAA0C;UAQxC1L,EAAA,CAAAI,SAAA,EAAe;UAAfJ,EAAA,CAAAqB,UAAA,gBAAe;UASnBrB,EAAA,CAAAI,SAAA,GAAwC;UACxCJ,EADA,CAAAqB,UAAA,kBAAA6J,GAAA,CAAA3K,QAAA,CAAAoL,aAAA,CAAwC,YAAAT,GAAA,CAAA1H,iBAAA,CACX;UAU/BxD,EAAA,CAAAI,SAAA,GAAiB;UAKjBJ,EALA,CAAAqB,UAAA,kBAAiB,oBACE,UAAA6J,GAAA,CAAA3K,QAAA,CAAAe,KAAA,CAEK,cAAA4J,GAAA,CAAAtJ,SAAA,CAED;UAOtB5B,EAAA,CAAAI,SAAA,EAAmD;UAAnDJ,EAAA,CAAAqB,UAAA,UAAA6J,GAAA,CAAAtJ,SAAA,IAAAsJ,GAAA,CAAAlJ,kBAAA,CAAAC,MAAA,OAAmD;UAOlBjC,EAAA,CAAAI,SAAA,EAAiG;UAAjGJ,EAAA,CAAAqB,UAAA,YAAA6J,GAAA,CAAAtJ,SAAA,IAAAsJ,GAAA,CAAA/H,mBAAA,CAAAlB,MAAA,SAAAiJ,GAAA,CAAA/G,wBAAA,GAAA+G,GAAA,CAAA/H,mBAAA,CAAiG;UAmBrHnD,EAAA,CAAAI,SAAA,EAAmC;UAAnCJ,EAAA,CAAAqB,UAAA,SAAA6J,GAAA,CAAAlJ,kBAAA,CAAAC,MAAA,KAAmC;;;qBD5CnDjD,YAAY,EAAAiL,EAAA,CAAA2B,OAAA,EAAA3B,EAAA,CAAA4B,IAAA,EACZrM,mBAAmB,EAAA4K,EAAA,CAAA0B,aAAA,EAAA1B,EAAA,CAAA2B,eAAA,EAAA3B,EAAA,CAAA4B,oBAAA,EAAA5B,EAAA,CAAA6B,kBAAA,EAAA7B,EAAA,CAAA8B,eAAA,EACnBhN,mBAAmB,EACnBI,iBAAiB,EACjBH,mBAAmB,EACnBE,aAAa,EACbD,iBAAiB,EAEjBG,mBAAmB,EACnBO,oBAAoB,EACpBC,WAAW;MAAAoM,MAAA;IAAA;;SAMF/J,mBAAmB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}