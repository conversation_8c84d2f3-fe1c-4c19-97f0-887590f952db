{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as arrays from '../../../base/common/arrays.js';\nimport { LineTokens } from './lineTokens.js';\n/**\n * Represents sparse tokens in a text model.\n */\nexport class SparseTokensStore {\n  constructor(languageIdCodec) {\n    this._pieces = [];\n    this._isComplete = false;\n    this._languageIdCodec = languageIdCodec;\n  }\n  flush() {\n    this._pieces = [];\n    this._isComplete = false;\n  }\n  isEmpty() {\n    return this._pieces.length === 0;\n  }\n  set(pieces, isComplete) {\n    this._pieces = pieces || [];\n    this._isComplete = isComplete;\n  }\n  setPartial(_range, pieces) {\n    // console.log(`setPartial ${_range} ${pieces.map(p => p.toString()).join(', ')}`);\n    let range = _range;\n    if (pieces.length > 0) {\n      const _firstRange = pieces[0].getRange();\n      const _lastRange = pieces[pieces.length - 1].getRange();\n      if (!_firstRange || !_lastRange) {\n        return _range;\n      }\n      range = _range.plusRange(_firstRange).plusRange(_lastRange);\n    }\n    let insertPosition = null;\n    for (let i = 0, len = this._pieces.length; i < len; i++) {\n      const piece = this._pieces[i];\n      if (piece.endLineNumber < range.startLineNumber) {\n        // this piece is before the range\n        continue;\n      }\n      if (piece.startLineNumber > range.endLineNumber) {\n        // this piece is after the range, so mark the spot before this piece\n        // as a good insertion position and stop looping\n        insertPosition = insertPosition || {\n          index: i\n        };\n        break;\n      }\n      // this piece might intersect with the range\n      piece.removeTokens(range);\n      if (piece.isEmpty()) {\n        // remove the piece if it became empty\n        this._pieces.splice(i, 1);\n        i--;\n        len--;\n        continue;\n      }\n      if (piece.endLineNumber < range.startLineNumber) {\n        // after removal, this piece is before the range\n        continue;\n      }\n      if (piece.startLineNumber > range.endLineNumber) {\n        // after removal, this piece is after the range\n        insertPosition = insertPosition || {\n          index: i\n        };\n        continue;\n      }\n      // after removal, this piece contains the range\n      const [a, b] = piece.split(range);\n      if (a.isEmpty()) {\n        // this piece is actually after the range\n        insertPosition = insertPosition || {\n          index: i\n        };\n        continue;\n      }\n      if (b.isEmpty()) {\n        // this piece is actually before the range\n        continue;\n      }\n      this._pieces.splice(i, 1, a, b);\n      i++;\n      len++;\n      insertPosition = insertPosition || {\n        index: i\n      };\n    }\n    insertPosition = insertPosition || {\n      index: this._pieces.length\n    };\n    if (pieces.length > 0) {\n      this._pieces = arrays.arrayInsert(this._pieces, insertPosition.index, pieces);\n    }\n    // console.log(`I HAVE ${this._pieces.length} pieces`);\n    // console.log(`${this._pieces.map(p => p.toString()).join('\\n')}`);\n    return range;\n  }\n  isComplete() {\n    return this._isComplete;\n  }\n  addSparseTokens(lineNumber, aTokens) {\n    if (aTokens.getLineContent().length === 0) {\n      // Don't do anything for empty lines\n      return aTokens;\n    }\n    const pieces = this._pieces;\n    if (pieces.length === 0) {\n      return aTokens;\n    }\n    const pieceIndex = SparseTokensStore._findFirstPieceWithLine(pieces, lineNumber);\n    const bTokens = pieces[pieceIndex].getLineTokens(lineNumber);\n    if (!bTokens) {\n      return aTokens;\n    }\n    const aLen = aTokens.getCount();\n    const bLen = bTokens.getCount();\n    let aIndex = 0;\n    const result = [];\n    let resultLen = 0;\n    let lastEndOffset = 0;\n    const emitToken = (endOffset, metadata) => {\n      if (endOffset === lastEndOffset) {\n        return;\n      }\n      lastEndOffset = endOffset;\n      result[resultLen++] = endOffset;\n      result[resultLen++] = metadata;\n    };\n    for (let bIndex = 0; bIndex < bLen; bIndex++) {\n      const bStartCharacter = bTokens.getStartCharacter(bIndex);\n      const bEndCharacter = bTokens.getEndCharacter(bIndex);\n      const bMetadata = bTokens.getMetadata(bIndex);\n      const bMask = ((bMetadata & 1 /* MetadataConsts.SEMANTIC_USE_ITALIC */ ? 2048 /* MetadataConsts.ITALIC_MASK */ : 0) | (bMetadata & 2 /* MetadataConsts.SEMANTIC_USE_BOLD */ ? 4096 /* MetadataConsts.BOLD_MASK */ : 0) | (bMetadata & 4 /* MetadataConsts.SEMANTIC_USE_UNDERLINE */ ? 8192 /* MetadataConsts.UNDERLINE_MASK */ : 0) | (bMetadata & 8 /* MetadataConsts.SEMANTIC_USE_STRIKETHROUGH */ ? 16384 /* MetadataConsts.STRIKETHROUGH_MASK */ : 0) | (bMetadata & 16 /* MetadataConsts.SEMANTIC_USE_FOREGROUND */ ? 16744448 /* MetadataConsts.FOREGROUND_MASK */ : 0) | (bMetadata & 32 /* MetadataConsts.SEMANTIC_USE_BACKGROUND */ ? 4278190080 /* MetadataConsts.BACKGROUND_MASK */ : 0)) >>> 0;\n      const aMask = ~bMask >>> 0;\n      // push any token from `a` that is before `b`\n      while (aIndex < aLen && aTokens.getEndOffset(aIndex) <= bStartCharacter) {\n        emitToken(aTokens.getEndOffset(aIndex), aTokens.getMetadata(aIndex));\n        aIndex++;\n      }\n      // push the token from `a` if it intersects the token from `b`\n      if (aIndex < aLen && aTokens.getStartOffset(aIndex) < bStartCharacter) {\n        emitToken(bStartCharacter, aTokens.getMetadata(aIndex));\n      }\n      // skip any tokens from `a` that are contained inside `b`\n      while (aIndex < aLen && aTokens.getEndOffset(aIndex) < bEndCharacter) {\n        emitToken(aTokens.getEndOffset(aIndex), aTokens.getMetadata(aIndex) & aMask | bMetadata & bMask);\n        aIndex++;\n      }\n      if (aIndex < aLen) {\n        emitToken(bEndCharacter, aTokens.getMetadata(aIndex) & aMask | bMetadata & bMask);\n        if (aTokens.getEndOffset(aIndex) === bEndCharacter) {\n          // `a` ends exactly at the same spot as `b`!\n          aIndex++;\n        }\n      } else {\n        const aMergeIndex = Math.min(Math.max(0, aIndex - 1), aLen - 1);\n        // push the token from `b`\n        emitToken(bEndCharacter, aTokens.getMetadata(aMergeIndex) & aMask | bMetadata & bMask);\n      }\n    }\n    // push the remaining tokens from `a`\n    while (aIndex < aLen) {\n      emitToken(aTokens.getEndOffset(aIndex), aTokens.getMetadata(aIndex));\n      aIndex++;\n    }\n    return new LineTokens(new Uint32Array(result), aTokens.getLineContent(), this._languageIdCodec);\n  }\n  static _findFirstPieceWithLine(pieces, lineNumber) {\n    let low = 0;\n    let high = pieces.length - 1;\n    while (low < high) {\n      let mid = low + Math.floor((high - low) / 2);\n      if (pieces[mid].endLineNumber < lineNumber) {\n        low = mid + 1;\n      } else if (pieces[mid].startLineNumber > lineNumber) {\n        high = mid - 1;\n      } else {\n        while (mid > low && pieces[mid - 1].startLineNumber <= lineNumber && lineNumber <= pieces[mid - 1].endLineNumber) {\n          mid--;\n        }\n        return mid;\n      }\n    }\n    return low;\n  }\n  acceptEdit(range, eolCount, firstLineLength, lastLineLength, firstCharCode) {\n    for (const piece of this._pieces) {\n      piece.acceptEdit(range, eolCount, firstLineLength, lastLineLength, firstCharCode);\n    }\n  }\n}", "map": {"version": 3, "names": ["arrays", "LineTokens", "SparseTokensStore", "constructor", "languageIdCodec", "_pieces", "_isComplete", "_languageIdCodec", "flush", "isEmpty", "length", "set", "pieces", "isComplete", "setPartial", "_range", "range", "_first<PERSON><PERSON>e", "getRange", "_last<PERSON><PERSON>e", "plusRange", "insertPosition", "i", "len", "piece", "endLineNumber", "startLineNumber", "index", "removeTokens", "splice", "a", "b", "split", "arrayInsert", "addSparseTokens", "lineNumber", "aTokens", "get<PERSON>ineC<PERSON>nt", "pieceIndex", "_findFirstPieceWithLine", "bTokens", "getLineTokens", "aLen", "getCount", "bLen", "aIndex", "result", "resultLen", "lastEndOffset", "emitToken", "endOffset", "metadata", "bIndex", "bStartCharacter", "getStartCharacter", "bEndCharacter", "getEndCharacter", "bMetadata", "getMetadata", "bMask", "aMask", "getEndOffset", "getStartOffset", "aMergeIndex", "Math", "min", "max", "Uint32Array", "low", "high", "mid", "floor", "acceptEdit", "eolCount", "firstLine<PERSON>ength", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "firstCharCode"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/tokens/sparseTokensStore.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as arrays from '../../../base/common/arrays.js';\nimport { LineTokens } from './lineTokens.js';\n/**\n * Represents sparse tokens in a text model.\n */\nexport class SparseTokensStore {\n    constructor(languageIdCodec) {\n        this._pieces = [];\n        this._isComplete = false;\n        this._languageIdCodec = languageIdCodec;\n    }\n    flush() {\n        this._pieces = [];\n        this._isComplete = false;\n    }\n    isEmpty() {\n        return (this._pieces.length === 0);\n    }\n    set(pieces, isComplete) {\n        this._pieces = pieces || [];\n        this._isComplete = isComplete;\n    }\n    setPartial(_range, pieces) {\n        // console.log(`setPartial ${_range} ${pieces.map(p => p.toString()).join(', ')}`);\n        let range = _range;\n        if (pieces.length > 0) {\n            const _firstRange = pieces[0].getRange();\n            const _lastRange = pieces[pieces.length - 1].getRange();\n            if (!_firstRange || !_lastRange) {\n                return _range;\n            }\n            range = _range.plusRange(_firstRange).plusRange(_lastRange);\n        }\n        let insertPosition = null;\n        for (let i = 0, len = this._pieces.length; i < len; i++) {\n            const piece = this._pieces[i];\n            if (piece.endLineNumber < range.startLineNumber) {\n                // this piece is before the range\n                continue;\n            }\n            if (piece.startLineNumber > range.endLineNumber) {\n                // this piece is after the range, so mark the spot before this piece\n                // as a good insertion position and stop looping\n                insertPosition = insertPosition || { index: i };\n                break;\n            }\n            // this piece might intersect with the range\n            piece.removeTokens(range);\n            if (piece.isEmpty()) {\n                // remove the piece if it became empty\n                this._pieces.splice(i, 1);\n                i--;\n                len--;\n                continue;\n            }\n            if (piece.endLineNumber < range.startLineNumber) {\n                // after removal, this piece is before the range\n                continue;\n            }\n            if (piece.startLineNumber > range.endLineNumber) {\n                // after removal, this piece is after the range\n                insertPosition = insertPosition || { index: i };\n                continue;\n            }\n            // after removal, this piece contains the range\n            const [a, b] = piece.split(range);\n            if (a.isEmpty()) {\n                // this piece is actually after the range\n                insertPosition = insertPosition || { index: i };\n                continue;\n            }\n            if (b.isEmpty()) {\n                // this piece is actually before the range\n                continue;\n            }\n            this._pieces.splice(i, 1, a, b);\n            i++;\n            len++;\n            insertPosition = insertPosition || { index: i };\n        }\n        insertPosition = insertPosition || { index: this._pieces.length };\n        if (pieces.length > 0) {\n            this._pieces = arrays.arrayInsert(this._pieces, insertPosition.index, pieces);\n        }\n        // console.log(`I HAVE ${this._pieces.length} pieces`);\n        // console.log(`${this._pieces.map(p => p.toString()).join('\\n')}`);\n        return range;\n    }\n    isComplete() {\n        return this._isComplete;\n    }\n    addSparseTokens(lineNumber, aTokens) {\n        if (aTokens.getLineContent().length === 0) {\n            // Don't do anything for empty lines\n            return aTokens;\n        }\n        const pieces = this._pieces;\n        if (pieces.length === 0) {\n            return aTokens;\n        }\n        const pieceIndex = SparseTokensStore._findFirstPieceWithLine(pieces, lineNumber);\n        const bTokens = pieces[pieceIndex].getLineTokens(lineNumber);\n        if (!bTokens) {\n            return aTokens;\n        }\n        const aLen = aTokens.getCount();\n        const bLen = bTokens.getCount();\n        let aIndex = 0;\n        const result = [];\n        let resultLen = 0;\n        let lastEndOffset = 0;\n        const emitToken = (endOffset, metadata) => {\n            if (endOffset === lastEndOffset) {\n                return;\n            }\n            lastEndOffset = endOffset;\n            result[resultLen++] = endOffset;\n            result[resultLen++] = metadata;\n        };\n        for (let bIndex = 0; bIndex < bLen; bIndex++) {\n            const bStartCharacter = bTokens.getStartCharacter(bIndex);\n            const bEndCharacter = bTokens.getEndCharacter(bIndex);\n            const bMetadata = bTokens.getMetadata(bIndex);\n            const bMask = (((bMetadata & 1 /* MetadataConsts.SEMANTIC_USE_ITALIC */) ? 2048 /* MetadataConsts.ITALIC_MASK */ : 0)\n                | ((bMetadata & 2 /* MetadataConsts.SEMANTIC_USE_BOLD */) ? 4096 /* MetadataConsts.BOLD_MASK */ : 0)\n                | ((bMetadata & 4 /* MetadataConsts.SEMANTIC_USE_UNDERLINE */) ? 8192 /* MetadataConsts.UNDERLINE_MASK */ : 0)\n                | ((bMetadata & 8 /* MetadataConsts.SEMANTIC_USE_STRIKETHROUGH */) ? 16384 /* MetadataConsts.STRIKETHROUGH_MASK */ : 0)\n                | ((bMetadata & 16 /* MetadataConsts.SEMANTIC_USE_FOREGROUND */) ? 16744448 /* MetadataConsts.FOREGROUND_MASK */ : 0)\n                | ((bMetadata & 32 /* MetadataConsts.SEMANTIC_USE_BACKGROUND */) ? 4278190080 /* MetadataConsts.BACKGROUND_MASK */ : 0)) >>> 0;\n            const aMask = (~bMask) >>> 0;\n            // push any token from `a` that is before `b`\n            while (aIndex < aLen && aTokens.getEndOffset(aIndex) <= bStartCharacter) {\n                emitToken(aTokens.getEndOffset(aIndex), aTokens.getMetadata(aIndex));\n                aIndex++;\n            }\n            // push the token from `a` if it intersects the token from `b`\n            if (aIndex < aLen && aTokens.getStartOffset(aIndex) < bStartCharacter) {\n                emitToken(bStartCharacter, aTokens.getMetadata(aIndex));\n            }\n            // skip any tokens from `a` that are contained inside `b`\n            while (aIndex < aLen && aTokens.getEndOffset(aIndex) < bEndCharacter) {\n                emitToken(aTokens.getEndOffset(aIndex), (aTokens.getMetadata(aIndex) & aMask) | (bMetadata & bMask));\n                aIndex++;\n            }\n            if (aIndex < aLen) {\n                emitToken(bEndCharacter, (aTokens.getMetadata(aIndex) & aMask) | (bMetadata & bMask));\n                if (aTokens.getEndOffset(aIndex) === bEndCharacter) {\n                    // `a` ends exactly at the same spot as `b`!\n                    aIndex++;\n                }\n            }\n            else {\n                const aMergeIndex = Math.min(Math.max(0, aIndex - 1), aLen - 1);\n                // push the token from `b`\n                emitToken(bEndCharacter, (aTokens.getMetadata(aMergeIndex) & aMask) | (bMetadata & bMask));\n            }\n        }\n        // push the remaining tokens from `a`\n        while (aIndex < aLen) {\n            emitToken(aTokens.getEndOffset(aIndex), aTokens.getMetadata(aIndex));\n            aIndex++;\n        }\n        return new LineTokens(new Uint32Array(result), aTokens.getLineContent(), this._languageIdCodec);\n    }\n    static _findFirstPieceWithLine(pieces, lineNumber) {\n        let low = 0;\n        let high = pieces.length - 1;\n        while (low < high) {\n            let mid = low + Math.floor((high - low) / 2);\n            if (pieces[mid].endLineNumber < lineNumber) {\n                low = mid + 1;\n            }\n            else if (pieces[mid].startLineNumber > lineNumber) {\n                high = mid - 1;\n            }\n            else {\n                while (mid > low && pieces[mid - 1].startLineNumber <= lineNumber && lineNumber <= pieces[mid - 1].endLineNumber) {\n                    mid--;\n                }\n                return mid;\n            }\n        }\n        return low;\n    }\n    acceptEdit(range, eolCount, firstLineLength, lastLineLength, firstCharCode) {\n        for (const piece of this._pieces) {\n            piece.acceptEdit(range, eolCount, firstLineLength, lastLineLength, firstCharCode);\n        }\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,gCAAgC;AACxD,SAASC,UAAU,QAAQ,iBAAiB;AAC5C;AACA;AACA;AACA,OAAO,MAAMC,iBAAiB,CAAC;EAC3BC,WAAWA,CAACC,eAAe,EAAE;IACzB,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,gBAAgB,GAAGH,eAAe;EAC3C;EACAI,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACH,OAAO,GAAG,EAAE;IACjB,IAAI,CAACC,WAAW,GAAG,KAAK;EAC5B;EACAG,OAAOA,CAAA,EAAG;IACN,OAAQ,IAAI,CAACJ,OAAO,CAACK,MAAM,KAAK,CAAC;EACrC;EACAC,GAAGA,CAACC,MAAM,EAAEC,UAAU,EAAE;IACpB,IAAI,CAACR,OAAO,GAAGO,MAAM,IAAI,EAAE;IAC3B,IAAI,CAACN,WAAW,GAAGO,UAAU;EACjC;EACAC,UAAUA,CAACC,MAAM,EAAEH,MAAM,EAAE;IACvB;IACA,IAAII,KAAK,GAAGD,MAAM;IAClB,IAAIH,MAAM,CAACF,MAAM,GAAG,CAAC,EAAE;MACnB,MAAMO,WAAW,GAAGL,MAAM,CAAC,CAAC,CAAC,CAACM,QAAQ,CAAC,CAAC;MACxC,MAAMC,UAAU,GAAGP,MAAM,CAACA,MAAM,CAACF,MAAM,GAAG,CAAC,CAAC,CAACQ,QAAQ,CAAC,CAAC;MACvD,IAAI,CAACD,WAAW,IAAI,CAACE,UAAU,EAAE;QAC7B,OAAOJ,MAAM;MACjB;MACAC,KAAK,GAAGD,MAAM,CAACK,SAAS,CAACH,WAAW,CAAC,CAACG,SAAS,CAACD,UAAU,CAAC;IAC/D;IACA,IAAIE,cAAc,GAAG,IAAI;IACzB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAG,IAAI,CAAClB,OAAO,CAACK,MAAM,EAAEY,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;MACrD,MAAME,KAAK,GAAG,IAAI,CAACnB,OAAO,CAACiB,CAAC,CAAC;MAC7B,IAAIE,KAAK,CAACC,aAAa,GAAGT,KAAK,CAACU,eAAe,EAAE;QAC7C;QACA;MACJ;MACA,IAAIF,KAAK,CAACE,eAAe,GAAGV,KAAK,CAACS,aAAa,EAAE;QAC7C;QACA;QACAJ,cAAc,GAAGA,cAAc,IAAI;UAAEM,KAAK,EAAEL;QAAE,CAAC;QAC/C;MACJ;MACA;MACAE,KAAK,CAACI,YAAY,CAACZ,KAAK,CAAC;MACzB,IAAIQ,KAAK,CAACf,OAAO,CAAC,CAAC,EAAE;QACjB;QACA,IAAI,CAACJ,OAAO,CAACwB,MAAM,CAACP,CAAC,EAAE,CAAC,CAAC;QACzBA,CAAC,EAAE;QACHC,GAAG,EAAE;QACL;MACJ;MACA,IAAIC,KAAK,CAACC,aAAa,GAAGT,KAAK,CAACU,eAAe,EAAE;QAC7C;QACA;MACJ;MACA,IAAIF,KAAK,CAACE,eAAe,GAAGV,KAAK,CAACS,aAAa,EAAE;QAC7C;QACAJ,cAAc,GAAGA,cAAc,IAAI;UAAEM,KAAK,EAAEL;QAAE,CAAC;QAC/C;MACJ;MACA;MACA,MAAM,CAACQ,CAAC,EAAEC,CAAC,CAAC,GAAGP,KAAK,CAACQ,KAAK,CAAChB,KAAK,CAAC;MACjC,IAAIc,CAAC,CAACrB,OAAO,CAAC,CAAC,EAAE;QACb;QACAY,cAAc,GAAGA,cAAc,IAAI;UAAEM,KAAK,EAAEL;QAAE,CAAC;QAC/C;MACJ;MACA,IAAIS,CAAC,CAACtB,OAAO,CAAC,CAAC,EAAE;QACb;QACA;MACJ;MACA,IAAI,CAACJ,OAAO,CAACwB,MAAM,CAACP,CAAC,EAAE,CAAC,EAAEQ,CAAC,EAAEC,CAAC,CAAC;MAC/BT,CAAC,EAAE;MACHC,GAAG,EAAE;MACLF,cAAc,GAAGA,cAAc,IAAI;QAAEM,KAAK,EAAEL;MAAE,CAAC;IACnD;IACAD,cAAc,GAAGA,cAAc,IAAI;MAAEM,KAAK,EAAE,IAAI,CAACtB,OAAO,CAACK;IAAO,CAAC;IACjE,IAAIE,MAAM,CAACF,MAAM,GAAG,CAAC,EAAE;MACnB,IAAI,CAACL,OAAO,GAAGL,MAAM,CAACiC,WAAW,CAAC,IAAI,CAAC5B,OAAO,EAAEgB,cAAc,CAACM,KAAK,EAAEf,MAAM,CAAC;IACjF;IACA;IACA;IACA,OAAOI,KAAK;EAChB;EACAH,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAACP,WAAW;EAC3B;EACA4B,eAAeA,CAACC,UAAU,EAAEC,OAAO,EAAE;IACjC,IAAIA,OAAO,CAACC,cAAc,CAAC,CAAC,CAAC3B,MAAM,KAAK,CAAC,EAAE;MACvC;MACA,OAAO0B,OAAO;IAClB;IACA,MAAMxB,MAAM,GAAG,IAAI,CAACP,OAAO;IAC3B,IAAIO,MAAM,CAACF,MAAM,KAAK,CAAC,EAAE;MACrB,OAAO0B,OAAO;IAClB;IACA,MAAME,UAAU,GAAGpC,iBAAiB,CAACqC,uBAAuB,CAAC3B,MAAM,EAAEuB,UAAU,CAAC;IAChF,MAAMK,OAAO,GAAG5B,MAAM,CAAC0B,UAAU,CAAC,CAACG,aAAa,CAACN,UAAU,CAAC;IAC5D,IAAI,CAACK,OAAO,EAAE;MACV,OAAOJ,OAAO;IAClB;IACA,MAAMM,IAAI,GAAGN,OAAO,CAACO,QAAQ,CAAC,CAAC;IAC/B,MAAMC,IAAI,GAAGJ,OAAO,CAACG,QAAQ,CAAC,CAAC;IAC/B,IAAIE,MAAM,GAAG,CAAC;IACd,MAAMC,MAAM,GAAG,EAAE;IACjB,IAAIC,SAAS,GAAG,CAAC;IACjB,IAAIC,aAAa,GAAG,CAAC;IACrB,MAAMC,SAAS,GAAGA,CAACC,SAAS,EAAEC,QAAQ,KAAK;MACvC,IAAID,SAAS,KAAKF,aAAa,EAAE;QAC7B;MACJ;MACAA,aAAa,GAAGE,SAAS;MACzBJ,MAAM,CAACC,SAAS,EAAE,CAAC,GAAGG,SAAS;MAC/BJ,MAAM,CAACC,SAAS,EAAE,CAAC,GAAGI,QAAQ;IAClC,CAAC;IACD,KAAK,IAAIC,MAAM,GAAG,CAAC,EAAEA,MAAM,GAAGR,IAAI,EAAEQ,MAAM,EAAE,EAAE;MAC1C,MAAMC,eAAe,GAAGb,OAAO,CAACc,iBAAiB,CAACF,MAAM,CAAC;MACzD,MAAMG,aAAa,GAAGf,OAAO,CAACgB,eAAe,CAACJ,MAAM,CAAC;MACrD,MAAMK,SAAS,GAAGjB,OAAO,CAACkB,WAAW,CAACN,MAAM,CAAC;MAC7C,MAAMO,KAAK,GAAG,CAAC,CAAEF,SAAS,GAAG,CAAC,CAAC,2CAA4C,IAAI,CAAC,mCAAmC,CAAC,KAC5GA,SAAS,GAAG,CAAC,CAAC,yCAA0C,IAAI,CAAC,iCAAiC,CAAC,CAAC,IAChGA,SAAS,GAAG,CAAC,CAAC,8CAA+C,IAAI,CAAC,sCAAsC,CAAC,CAAC,IAC1GA,SAAS,GAAG,CAAC,CAAC,kDAAmD,KAAK,CAAC,0CAA0C,CAAC,CAAC,IACnHA,SAAS,GAAG,EAAE,CAAC,+CAAgD,QAAQ,CAAC,uCAAuC,CAAC,CAAC,IACjHA,SAAS,GAAG,EAAE,CAAC,+CAAgD,UAAU,CAAC,uCAAuC,CAAC,CAAC,MAAM,CAAC;MAClI,MAAMG,KAAK,GAAI,CAACD,KAAK,KAAM,CAAC;MAC5B;MACA,OAAOd,MAAM,GAAGH,IAAI,IAAIN,OAAO,CAACyB,YAAY,CAAChB,MAAM,CAAC,IAAIQ,eAAe,EAAE;QACrEJ,SAAS,CAACb,OAAO,CAACyB,YAAY,CAAChB,MAAM,CAAC,EAAET,OAAO,CAACsB,WAAW,CAACb,MAAM,CAAC,CAAC;QACpEA,MAAM,EAAE;MACZ;MACA;MACA,IAAIA,MAAM,GAAGH,IAAI,IAAIN,OAAO,CAAC0B,cAAc,CAACjB,MAAM,CAAC,GAAGQ,eAAe,EAAE;QACnEJ,SAAS,CAACI,eAAe,EAAEjB,OAAO,CAACsB,WAAW,CAACb,MAAM,CAAC,CAAC;MAC3D;MACA;MACA,OAAOA,MAAM,GAAGH,IAAI,IAAIN,OAAO,CAACyB,YAAY,CAAChB,MAAM,CAAC,GAAGU,aAAa,EAAE;QAClEN,SAAS,CAACb,OAAO,CAACyB,YAAY,CAAChB,MAAM,CAAC,EAAGT,OAAO,CAACsB,WAAW,CAACb,MAAM,CAAC,GAAGe,KAAK,GAAKH,SAAS,GAAGE,KAAM,CAAC;QACpGd,MAAM,EAAE;MACZ;MACA,IAAIA,MAAM,GAAGH,IAAI,EAAE;QACfO,SAAS,CAACM,aAAa,EAAGnB,OAAO,CAACsB,WAAW,CAACb,MAAM,CAAC,GAAGe,KAAK,GAAKH,SAAS,GAAGE,KAAM,CAAC;QACrF,IAAIvB,OAAO,CAACyB,YAAY,CAAChB,MAAM,CAAC,KAAKU,aAAa,EAAE;UAChD;UACAV,MAAM,EAAE;QACZ;MACJ,CAAC,MACI;QACD,MAAMkB,WAAW,GAAGC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAAC,CAAC,EAAErB,MAAM,GAAG,CAAC,CAAC,EAAEH,IAAI,GAAG,CAAC,CAAC;QAC/D;QACAO,SAAS,CAACM,aAAa,EAAGnB,OAAO,CAACsB,WAAW,CAACK,WAAW,CAAC,GAAGH,KAAK,GAAKH,SAAS,GAAGE,KAAM,CAAC;MAC9F;IACJ;IACA;IACA,OAAOd,MAAM,GAAGH,IAAI,EAAE;MAClBO,SAAS,CAACb,OAAO,CAACyB,YAAY,CAAChB,MAAM,CAAC,EAAET,OAAO,CAACsB,WAAW,CAACb,MAAM,CAAC,CAAC;MACpEA,MAAM,EAAE;IACZ;IACA,OAAO,IAAI5C,UAAU,CAAC,IAAIkE,WAAW,CAACrB,MAAM,CAAC,EAAEV,OAAO,CAACC,cAAc,CAAC,CAAC,EAAE,IAAI,CAAC9B,gBAAgB,CAAC;EACnG;EACA,OAAOgC,uBAAuBA,CAAC3B,MAAM,EAAEuB,UAAU,EAAE;IAC/C,IAAIiC,GAAG,GAAG,CAAC;IACX,IAAIC,IAAI,GAAGzD,MAAM,CAACF,MAAM,GAAG,CAAC;IAC5B,OAAO0D,GAAG,GAAGC,IAAI,EAAE;MACf,IAAIC,GAAG,GAAGF,GAAG,GAAGJ,IAAI,CAACO,KAAK,CAAC,CAACF,IAAI,GAAGD,GAAG,IAAI,CAAC,CAAC;MAC5C,IAAIxD,MAAM,CAAC0D,GAAG,CAAC,CAAC7C,aAAa,GAAGU,UAAU,EAAE;QACxCiC,GAAG,GAAGE,GAAG,GAAG,CAAC;MACjB,CAAC,MACI,IAAI1D,MAAM,CAAC0D,GAAG,CAAC,CAAC5C,eAAe,GAAGS,UAAU,EAAE;QAC/CkC,IAAI,GAAGC,GAAG,GAAG,CAAC;MAClB,CAAC,MACI;QACD,OAAOA,GAAG,GAAGF,GAAG,IAAIxD,MAAM,CAAC0D,GAAG,GAAG,CAAC,CAAC,CAAC5C,eAAe,IAAIS,UAAU,IAAIA,UAAU,IAAIvB,MAAM,CAAC0D,GAAG,GAAG,CAAC,CAAC,CAAC7C,aAAa,EAAE;UAC9G6C,GAAG,EAAE;QACT;QACA,OAAOA,GAAG;MACd;IACJ;IACA,OAAOF,GAAG;EACd;EACAI,UAAUA,CAACxD,KAAK,EAAEyD,QAAQ,EAAEC,eAAe,EAAEC,cAAc,EAAEC,aAAa,EAAE;IACxE,KAAK,MAAMpD,KAAK,IAAI,IAAI,CAACnB,OAAO,EAAE;MAC9BmB,KAAK,CAACgD,UAAU,CAACxD,KAAK,EAAEyD,QAAQ,EAAEC,eAAe,EAAEC,cAAc,EAAEC,aAAa,CAAC;IACrF;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}