{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = this && this.__param || function (paramIndex, decorator) {\n  return function (target, key) {\n    decorator(target, key, paramIndex);\n  };\n};\nimport * as dom from '../../../../base/browser/dom.js';\nimport { DomScrollableElement } from '../../../../base/browser/ui/scrollbar/scrollableElement.js';\nimport { Codicon } from '../../../../base/common/codicons.js';\nimport { ThemeIcon } from '../../../../base/common/themables.js';\nimport { Emitter } from '../../../../base/common/event.js';\nimport { MarkdownString } from '../../../../base/common/htmlContent.js';\nimport { DisposableStore } from '../../../../base/common/lifecycle.js';\nimport { MarkdownRenderer } from '../../../browser/widget/markdownRenderer/browser/markdownRenderer.js';\nimport { ResizableHTMLElement } from '../../../../base/browser/ui/resizable/resizable.js';\nimport * as nls from '../../../../nls.js';\nimport { IInstantiationService } from '../../../../platform/instantiation/common/instantiation.js';\nexport function canExpandCompletionItem(item) {\n  return !!item && Boolean(item.completion.documentation || item.completion.detail && item.completion.detail !== item.completion.label);\n}\nlet SuggestDetailsWidget = class SuggestDetailsWidget {\n  constructor(_editor, instaService) {\n    this._editor = _editor;\n    this._onDidClose = new Emitter();\n    this.onDidClose = this._onDidClose.event;\n    this._onDidChangeContents = new Emitter();\n    this.onDidChangeContents = this._onDidChangeContents.event;\n    this._disposables = new DisposableStore();\n    this._renderDisposeable = new DisposableStore();\n    this._borderWidth = 1;\n    this._size = new dom.Dimension(330, 0);\n    this.domNode = dom.$('.suggest-details');\n    this.domNode.classList.add('no-docs');\n    this._markdownRenderer = instaService.createInstance(MarkdownRenderer, {\n      editor: _editor\n    });\n    this._body = dom.$('.body');\n    this._scrollbar = new DomScrollableElement(this._body, {\n      alwaysConsumeMouseWheel: true\n    });\n    dom.append(this.domNode, this._scrollbar.getDomNode());\n    this._disposables.add(this._scrollbar);\n    this._header = dom.append(this._body, dom.$('.header'));\n    this._close = dom.append(this._header, dom.$('span' + ThemeIcon.asCSSSelector(Codicon.close)));\n    this._close.title = nls.localize('details.close', \"Close\");\n    this._type = dom.append(this._header, dom.$('p.type'));\n    this._docs = dom.append(this._body, dom.$('p.docs'));\n    this._configureFont();\n    this._disposables.add(this._editor.onDidChangeConfiguration(e => {\n      if (e.hasChanged(50 /* EditorOption.fontInfo */)) {\n        this._configureFont();\n      }\n    }));\n  }\n  dispose() {\n    this._disposables.dispose();\n    this._renderDisposeable.dispose();\n  }\n  _configureFont() {\n    const options = this._editor.getOptions();\n    const fontInfo = options.get(50 /* EditorOption.fontInfo */);\n    const fontFamily = fontInfo.getMassagedFontFamily();\n    const fontSize = options.get(120 /* EditorOption.suggestFontSize */) || fontInfo.fontSize;\n    const lineHeight = options.get(121 /* EditorOption.suggestLineHeight */) || fontInfo.lineHeight;\n    const fontWeight = fontInfo.fontWeight;\n    const fontSizePx = `${fontSize}px`;\n    const lineHeightPx = `${lineHeight}px`;\n    this.domNode.style.fontSize = fontSizePx;\n    this.domNode.style.lineHeight = `${lineHeight / fontSize}`;\n    this.domNode.style.fontWeight = fontWeight;\n    this.domNode.style.fontFeatureSettings = fontInfo.fontFeatureSettings;\n    this._type.style.fontFamily = fontFamily;\n    this._close.style.height = lineHeightPx;\n    this._close.style.width = lineHeightPx;\n  }\n  getLayoutInfo() {\n    const lineHeight = this._editor.getOption(121 /* EditorOption.suggestLineHeight */) || this._editor.getOption(50 /* EditorOption.fontInfo */).lineHeight;\n    const borderWidth = this._borderWidth;\n    const borderHeight = borderWidth * 2;\n    return {\n      lineHeight,\n      borderWidth,\n      borderHeight,\n      verticalPadding: 22,\n      horizontalPadding: 14\n    };\n  }\n  renderLoading() {\n    this._type.textContent = nls.localize('loading', \"Loading...\");\n    this._docs.textContent = '';\n    this.domNode.classList.remove('no-docs', 'no-type');\n    this.layout(this.size.width, this.getLayoutInfo().lineHeight * 2);\n    this._onDidChangeContents.fire(this);\n  }\n  renderItem(item, explainMode) {\n    this._renderDisposeable.clear();\n    let {\n      detail,\n      documentation\n    } = item.completion;\n    if (explainMode) {\n      let md = '';\n      md += `score: ${item.score[0]}\\n`;\n      md += `prefix: ${item.word ?? '(no prefix)'}\\n`;\n      md += `word: ${item.completion.filterText ? item.completion.filterText + ' (filterText)' : item.textLabel}\\n`;\n      md += `distance: ${item.distance} (localityBonus-setting)\\n`;\n      md += `index: ${item.idx}, based on ${item.completion.sortText && `sortText: \"${item.completion.sortText}\"` || 'label'}\\n`;\n      md += `commit_chars: ${item.completion.commitCharacters?.join('')}\\n`;\n      documentation = new MarkdownString().appendCodeblock('empty', md);\n      detail = `Provider: ${item.provider._debugDisplayName}`;\n    }\n    if (!explainMode && !canExpandCompletionItem(item)) {\n      this.clearContents();\n      return;\n    }\n    this.domNode.classList.remove('no-docs', 'no-type');\n    // --- details\n    if (detail) {\n      const cappedDetail = detail.length > 100000 ? `${detail.substr(0, 100000)}…` : detail;\n      this._type.textContent = cappedDetail;\n      this._type.title = cappedDetail;\n      dom.show(this._type);\n      this._type.classList.toggle('auto-wrap', !/\\r?\\n^\\s+/gmi.test(cappedDetail));\n    } else {\n      dom.clearNode(this._type);\n      this._type.title = '';\n      dom.hide(this._type);\n      this.domNode.classList.add('no-type');\n    }\n    // --- documentation\n    dom.clearNode(this._docs);\n    if (typeof documentation === 'string') {\n      this._docs.classList.remove('markdown-docs');\n      this._docs.textContent = documentation;\n    } else if (documentation) {\n      this._docs.classList.add('markdown-docs');\n      dom.clearNode(this._docs);\n      const renderedContents = this._markdownRenderer.render(documentation);\n      this._docs.appendChild(renderedContents.element);\n      this._renderDisposeable.add(renderedContents);\n      this._renderDisposeable.add(this._markdownRenderer.onDidRenderAsync(() => {\n        this.layout(this._size.width, this._type.clientHeight + this._docs.clientHeight);\n        this._onDidChangeContents.fire(this);\n      }));\n    }\n    this.domNode.style.userSelect = 'text';\n    this.domNode.tabIndex = -1;\n    this._close.onmousedown = e => {\n      e.preventDefault();\n      e.stopPropagation();\n    };\n    this._close.onclick = e => {\n      e.preventDefault();\n      e.stopPropagation();\n      this._onDidClose.fire();\n    };\n    this._body.scrollTop = 0;\n    this.layout(this._size.width, this._type.clientHeight + this._docs.clientHeight);\n    this._onDidChangeContents.fire(this);\n  }\n  clearContents() {\n    this.domNode.classList.add('no-docs');\n    this._type.textContent = '';\n    this._docs.textContent = '';\n  }\n  get isEmpty() {\n    return this.domNode.classList.contains('no-docs');\n  }\n  get size() {\n    return this._size;\n  }\n  layout(width, height) {\n    const newSize = new dom.Dimension(width, height);\n    if (!dom.Dimension.equals(newSize, this._size)) {\n      this._size = newSize;\n      dom.size(this.domNode, width, height);\n    }\n    this._scrollbar.scanDomNode();\n  }\n  scrollDown(much = 8) {\n    this._body.scrollTop += much;\n  }\n  scrollUp(much = 8) {\n    this._body.scrollTop -= much;\n  }\n  scrollTop() {\n    this._body.scrollTop = 0;\n  }\n  scrollBottom() {\n    this._body.scrollTop = this._body.scrollHeight;\n  }\n  pageDown() {\n    this.scrollDown(80);\n  }\n  pageUp() {\n    this.scrollUp(80);\n  }\n  set borderWidth(width) {\n    this._borderWidth = width;\n  }\n  get borderWidth() {\n    return this._borderWidth;\n  }\n};\nSuggestDetailsWidget = __decorate([__param(1, IInstantiationService)], SuggestDetailsWidget);\nexport { SuggestDetailsWidget };\nexport class SuggestDetailsOverlay {\n  constructor(widget, _editor) {\n    this.widget = widget;\n    this._editor = _editor;\n    this.allowEditorOverflow = true;\n    this._disposables = new DisposableStore();\n    this._added = false;\n    this._preferAlignAtTop = true;\n    this._resizable = new ResizableHTMLElement();\n    this._resizable.domNode.classList.add('suggest-details-container');\n    this._resizable.domNode.appendChild(widget.domNode);\n    this._resizable.enableSashes(false, true, true, false);\n    let topLeftNow;\n    let sizeNow;\n    let deltaTop = 0;\n    let deltaLeft = 0;\n    this._disposables.add(this._resizable.onDidWillResize(() => {\n      topLeftNow = this._topLeft;\n      sizeNow = this._resizable.size;\n    }));\n    this._disposables.add(this._resizable.onDidResize(e => {\n      if (topLeftNow && sizeNow) {\n        this.widget.layout(e.dimension.width, e.dimension.height);\n        let updateTopLeft = false;\n        if (e.west) {\n          deltaLeft = sizeNow.width - e.dimension.width;\n          updateTopLeft = true;\n        }\n        if (e.north) {\n          deltaTop = sizeNow.height - e.dimension.height;\n          updateTopLeft = true;\n        }\n        if (updateTopLeft) {\n          this._applyTopLeft({\n            top: topLeftNow.top + deltaTop,\n            left: topLeftNow.left + deltaLeft\n          });\n        }\n      }\n      if (e.done) {\n        topLeftNow = undefined;\n        sizeNow = undefined;\n        deltaTop = 0;\n        deltaLeft = 0;\n        this._userSize = e.dimension;\n      }\n    }));\n    this._disposables.add(this.widget.onDidChangeContents(() => {\n      if (this._anchorBox) {\n        this._placeAtAnchor(this._anchorBox, this._userSize ?? this.widget.size, this._preferAlignAtTop);\n      }\n    }));\n  }\n  dispose() {\n    this._resizable.dispose();\n    this._disposables.dispose();\n    this.hide();\n  }\n  getId() {\n    return 'suggest.details';\n  }\n  getDomNode() {\n    return this._resizable.domNode;\n  }\n  getPosition() {\n    return this._topLeft ? {\n      preference: this._topLeft\n    } : null;\n  }\n  show() {\n    if (!this._added) {\n      this._editor.addOverlayWidget(this);\n      this._added = true;\n    }\n  }\n  hide(sessionEnded = false) {\n    this._resizable.clearSashHoverState();\n    if (this._added) {\n      this._editor.removeOverlayWidget(this);\n      this._added = false;\n      this._anchorBox = undefined;\n      this._topLeft = undefined;\n    }\n    if (sessionEnded) {\n      this._userSize = undefined;\n      this.widget.clearContents();\n    }\n  }\n  placeAtAnchor(anchor, preferAlignAtTop) {\n    const anchorBox = anchor.getBoundingClientRect();\n    this._anchorBox = anchorBox;\n    this._preferAlignAtTop = preferAlignAtTop;\n    this._placeAtAnchor(this._anchorBox, this._userSize ?? this.widget.size, preferAlignAtTop);\n  }\n  _placeAtAnchor(anchorBox, size, preferAlignAtTop) {\n    const bodyBox = dom.getClientArea(this.getDomNode().ownerDocument.body);\n    const info = this.widget.getLayoutInfo();\n    const defaultMinSize = new dom.Dimension(220, 2 * info.lineHeight);\n    const defaultTop = anchorBox.top;\n    // EAST\n    const eastPlacement = function () {\n      const width = bodyBox.width - (anchorBox.left + anchorBox.width + info.borderWidth + info.horizontalPadding);\n      const left = -info.borderWidth + anchorBox.left + anchorBox.width;\n      const maxSizeTop = new dom.Dimension(width, bodyBox.height - anchorBox.top - info.borderHeight - info.verticalPadding);\n      const maxSizeBottom = maxSizeTop.with(undefined, anchorBox.top + anchorBox.height - info.borderHeight - info.verticalPadding);\n      return {\n        top: defaultTop,\n        left,\n        fit: width - size.width,\n        maxSizeTop,\n        maxSizeBottom,\n        minSize: defaultMinSize.with(Math.min(width, defaultMinSize.width))\n      };\n    }();\n    // WEST\n    const westPlacement = function () {\n      const width = anchorBox.left - info.borderWidth - info.horizontalPadding;\n      const left = Math.max(info.horizontalPadding, anchorBox.left - size.width - info.borderWidth);\n      const maxSizeTop = new dom.Dimension(width, bodyBox.height - anchorBox.top - info.borderHeight - info.verticalPadding);\n      const maxSizeBottom = maxSizeTop.with(undefined, anchorBox.top + anchorBox.height - info.borderHeight - info.verticalPadding);\n      return {\n        top: defaultTop,\n        left,\n        fit: width - size.width,\n        maxSizeTop,\n        maxSizeBottom,\n        minSize: defaultMinSize.with(Math.min(width, defaultMinSize.width))\n      };\n    }();\n    // SOUTH\n    const southPacement = function () {\n      const left = anchorBox.left;\n      const top = -info.borderWidth + anchorBox.top + anchorBox.height;\n      const maxSizeBottom = new dom.Dimension(anchorBox.width - info.borderHeight, bodyBox.height - anchorBox.top - anchorBox.height - info.verticalPadding);\n      return {\n        top,\n        left,\n        fit: maxSizeBottom.height - size.height,\n        maxSizeBottom,\n        maxSizeTop: maxSizeBottom,\n        minSize: defaultMinSize.with(maxSizeBottom.width)\n      };\n    }();\n    // take first placement that fits or the first with \"least bad\" fit\n    const placements = [eastPlacement, westPlacement, southPacement];\n    const placement = placements.find(p => p.fit >= 0) ?? placements.sort((a, b) => b.fit - a.fit)[0];\n    // top/bottom placement\n    const bottom = anchorBox.top + anchorBox.height - info.borderHeight;\n    let alignAtTop;\n    let height = size.height;\n    const maxHeight = Math.max(placement.maxSizeTop.height, placement.maxSizeBottom.height);\n    if (height > maxHeight) {\n      height = maxHeight;\n    }\n    let maxSize;\n    if (preferAlignAtTop) {\n      if (height <= placement.maxSizeTop.height) {\n        alignAtTop = true;\n        maxSize = placement.maxSizeTop;\n      } else {\n        alignAtTop = false;\n        maxSize = placement.maxSizeBottom;\n      }\n    } else {\n      if (height <= placement.maxSizeBottom.height) {\n        alignAtTop = false;\n        maxSize = placement.maxSizeBottom;\n      } else {\n        alignAtTop = true;\n        maxSize = placement.maxSizeTop;\n      }\n    }\n    let {\n      top,\n      left\n    } = placement;\n    if (!alignAtTop && height > anchorBox.height) {\n      top = bottom - height;\n    }\n    const editorDomNode = this._editor.getDomNode();\n    if (editorDomNode) {\n      // get bounding rectangle of the suggest widget relative to the editor\n      const editorBoundingBox = editorDomNode.getBoundingClientRect();\n      top -= editorBoundingBox.top;\n      left -= editorBoundingBox.left;\n    }\n    this._applyTopLeft({\n      left,\n      top\n    });\n    this._resizable.enableSashes(!alignAtTop, placement === eastPlacement, alignAtTop, placement !== eastPlacement);\n    this._resizable.minSize = placement.minSize;\n    this._resizable.maxSize = maxSize;\n    this._resizable.layout(height, Math.min(maxSize.width, size.width));\n    this.widget.layout(this._resizable.size.width, this._resizable.size.height);\n  }\n  _applyTopLeft(topLeft) {\n    this._topLeft = topLeft;\n    this._editor.layoutOverlayWidget(this);\n  }\n}", "map": {"version": 3, "names": ["__decorate", "decorators", "target", "key", "desc", "c", "arguments", "length", "r", "Object", "getOwnPropertyDescriptor", "d", "Reflect", "decorate", "i", "defineProperty", "__param", "paramIndex", "decorator", "dom", "DomScrollableElement", "Codicon", "ThemeIcon", "Emitter", "MarkdownString", "DisposableStore", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ResizableHTMLElement", "nls", "IInstantiationService", "canExpandCompletionItem", "item", "Boolean", "completion", "documentation", "detail", "label", "SuggestDetailsWidget", "constructor", "_editor", "instaService", "_onDidClose", "onDidClose", "event", "_onDidChangeContents", "onDidChangeContents", "_disposables", "_renderDisposeable", "_borderWidth", "_size", "Dimension", "domNode", "$", "classList", "add", "_markdown<PERSON><PERSON>er", "createInstance", "editor", "_body", "_scrollbar", "alwaysConsumeMouseWheel", "append", "getDomNode", "_header", "_close", "asCSSSelector", "close", "title", "localize", "_type", "_docs", "_configureFont", "onDidChangeConfiguration", "e", "has<PERSON><PERSON>ed", "dispose", "options", "getOptions", "fontInfo", "get", "fontFamily", "getMassagedFontFamily", "fontSize", "lineHeight", "fontWeight", "fontSizePx", "lineHeightPx", "style", "fontFeatureSettings", "height", "width", "getLayoutInfo", "getOption", "borderWidth", "borderHeight", "verticalPadding", "horizontalPadding", "renderLoading", "textContent", "remove", "layout", "size", "fire", "renderItem", "explainMode", "clear", "md", "score", "word", "filterText", "textLabel", "distance", "idx", "sortText", "commitCharacters", "join", "appendCodeblock", "provider", "_debugDisplayName", "clearContents", "cappedDetail", "substr", "show", "toggle", "test", "clearNode", "hide", "renderedContents", "render", "append<PERSON><PERSON><PERSON>", "element", "onDidRenderAsync", "clientHeight", "userSelect", "tabIndex", "onmousedown", "preventDefault", "stopPropagation", "onclick", "scrollTop", "isEmpty", "contains", "newSize", "equals", "scanDomNode", "scrollDown", "much", "scrollUp", "scrollBottom", "scrollHeight", "pageDown", "pageUp", "SuggestDetailsOverlay", "widget", "allowEditorOverflow", "_added", "_preferAlignAtTop", "_resizable", "enableSashes", "topLeftNow", "sizeNow", "deltaTop", "deltaLeft", "onDidWillResize", "_topLeft", "onDidResize", "dimension", "updateTopLeft", "west", "north", "_applyTopLeft", "top", "left", "done", "undefined", "_userSize", "_anchorBox", "_placeAtAnchor", "getId", "getPosition", "preference", "addOverlayWidget", "sessionEnded", "clearSashHoverState", "removeOverlayWidget", "placeAtAnchor", "anchor", "preferAlignAtTop", "anchorBox", "getBoundingClientRect", "bodyBox", "getClientArea", "ownerDocument", "body", "info", "defaultMinSize", "defaultTop", "eastPlacement", "maxSizeTop", "maxSizeBottom", "with", "fit", "minSize", "Math", "min", "westPlacement", "max", "southPacement", "placements", "placement", "find", "p", "sort", "a", "b", "bottom", "alignAtTop", "maxHeight", "maxSize", "editorDomNode", "editorBoundingBox", "topLeft", "layoutOverlayWidget"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/contrib/suggest/browser/suggestWidgetDetails.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = (this && this.__param) || function (paramIndex, decorator) {\n    return function (target, key) { decorator(target, key, paramIndex); }\n};\nimport * as dom from '../../../../base/browser/dom.js';\nimport { DomScrollableElement } from '../../../../base/browser/ui/scrollbar/scrollableElement.js';\nimport { Codicon } from '../../../../base/common/codicons.js';\nimport { ThemeIcon } from '../../../../base/common/themables.js';\nimport { Emitter } from '../../../../base/common/event.js';\nimport { MarkdownString } from '../../../../base/common/htmlContent.js';\nimport { DisposableStore } from '../../../../base/common/lifecycle.js';\nimport { MarkdownRenderer } from '../../../browser/widget/markdownRenderer/browser/markdownRenderer.js';\nimport { ResizableHTMLElement } from '../../../../base/browser/ui/resizable/resizable.js';\nimport * as nls from '../../../../nls.js';\nimport { IInstantiationService } from '../../../../platform/instantiation/common/instantiation.js';\nexport function canExpandCompletionItem(item) {\n    return !!item && Boolean(item.completion.documentation || item.completion.detail && item.completion.detail !== item.completion.label);\n}\nlet SuggestDetailsWidget = class SuggestDetailsWidget {\n    constructor(_editor, instaService) {\n        this._editor = _editor;\n        this._onDidClose = new Emitter();\n        this.onDidClose = this._onDidClose.event;\n        this._onDidChangeContents = new Emitter();\n        this.onDidChangeContents = this._onDidChangeContents.event;\n        this._disposables = new DisposableStore();\n        this._renderDisposeable = new DisposableStore();\n        this._borderWidth = 1;\n        this._size = new dom.Dimension(330, 0);\n        this.domNode = dom.$('.suggest-details');\n        this.domNode.classList.add('no-docs');\n        this._markdownRenderer = instaService.createInstance(MarkdownRenderer, { editor: _editor });\n        this._body = dom.$('.body');\n        this._scrollbar = new DomScrollableElement(this._body, {\n            alwaysConsumeMouseWheel: true,\n        });\n        dom.append(this.domNode, this._scrollbar.getDomNode());\n        this._disposables.add(this._scrollbar);\n        this._header = dom.append(this._body, dom.$('.header'));\n        this._close = dom.append(this._header, dom.$('span' + ThemeIcon.asCSSSelector(Codicon.close)));\n        this._close.title = nls.localize('details.close', \"Close\");\n        this._type = dom.append(this._header, dom.$('p.type'));\n        this._docs = dom.append(this._body, dom.$('p.docs'));\n        this._configureFont();\n        this._disposables.add(this._editor.onDidChangeConfiguration(e => {\n            if (e.hasChanged(50 /* EditorOption.fontInfo */)) {\n                this._configureFont();\n            }\n        }));\n    }\n    dispose() {\n        this._disposables.dispose();\n        this._renderDisposeable.dispose();\n    }\n    _configureFont() {\n        const options = this._editor.getOptions();\n        const fontInfo = options.get(50 /* EditorOption.fontInfo */);\n        const fontFamily = fontInfo.getMassagedFontFamily();\n        const fontSize = options.get(120 /* EditorOption.suggestFontSize */) || fontInfo.fontSize;\n        const lineHeight = options.get(121 /* EditorOption.suggestLineHeight */) || fontInfo.lineHeight;\n        const fontWeight = fontInfo.fontWeight;\n        const fontSizePx = `${fontSize}px`;\n        const lineHeightPx = `${lineHeight}px`;\n        this.domNode.style.fontSize = fontSizePx;\n        this.domNode.style.lineHeight = `${lineHeight / fontSize}`;\n        this.domNode.style.fontWeight = fontWeight;\n        this.domNode.style.fontFeatureSettings = fontInfo.fontFeatureSettings;\n        this._type.style.fontFamily = fontFamily;\n        this._close.style.height = lineHeightPx;\n        this._close.style.width = lineHeightPx;\n    }\n    getLayoutInfo() {\n        const lineHeight = this._editor.getOption(121 /* EditorOption.suggestLineHeight */) || this._editor.getOption(50 /* EditorOption.fontInfo */).lineHeight;\n        const borderWidth = this._borderWidth;\n        const borderHeight = borderWidth * 2;\n        return {\n            lineHeight,\n            borderWidth,\n            borderHeight,\n            verticalPadding: 22,\n            horizontalPadding: 14\n        };\n    }\n    renderLoading() {\n        this._type.textContent = nls.localize('loading', \"Loading...\");\n        this._docs.textContent = '';\n        this.domNode.classList.remove('no-docs', 'no-type');\n        this.layout(this.size.width, this.getLayoutInfo().lineHeight * 2);\n        this._onDidChangeContents.fire(this);\n    }\n    renderItem(item, explainMode) {\n        this._renderDisposeable.clear();\n        let { detail, documentation } = item.completion;\n        if (explainMode) {\n            let md = '';\n            md += `score: ${item.score[0]}\\n`;\n            md += `prefix: ${item.word ?? '(no prefix)'}\\n`;\n            md += `word: ${item.completion.filterText ? item.completion.filterText + ' (filterText)' : item.textLabel}\\n`;\n            md += `distance: ${item.distance} (localityBonus-setting)\\n`;\n            md += `index: ${item.idx}, based on ${item.completion.sortText && `sortText: \"${item.completion.sortText}\"` || 'label'}\\n`;\n            md += `commit_chars: ${item.completion.commitCharacters?.join('')}\\n`;\n            documentation = new MarkdownString().appendCodeblock('empty', md);\n            detail = `Provider: ${item.provider._debugDisplayName}`;\n        }\n        if (!explainMode && !canExpandCompletionItem(item)) {\n            this.clearContents();\n            return;\n        }\n        this.domNode.classList.remove('no-docs', 'no-type');\n        // --- details\n        if (detail) {\n            const cappedDetail = detail.length > 100000 ? `${detail.substr(0, 100000)}…` : detail;\n            this._type.textContent = cappedDetail;\n            this._type.title = cappedDetail;\n            dom.show(this._type);\n            this._type.classList.toggle('auto-wrap', !/\\r?\\n^\\s+/gmi.test(cappedDetail));\n        }\n        else {\n            dom.clearNode(this._type);\n            this._type.title = '';\n            dom.hide(this._type);\n            this.domNode.classList.add('no-type');\n        }\n        // --- documentation\n        dom.clearNode(this._docs);\n        if (typeof documentation === 'string') {\n            this._docs.classList.remove('markdown-docs');\n            this._docs.textContent = documentation;\n        }\n        else if (documentation) {\n            this._docs.classList.add('markdown-docs');\n            dom.clearNode(this._docs);\n            const renderedContents = this._markdownRenderer.render(documentation);\n            this._docs.appendChild(renderedContents.element);\n            this._renderDisposeable.add(renderedContents);\n            this._renderDisposeable.add(this._markdownRenderer.onDidRenderAsync(() => {\n                this.layout(this._size.width, this._type.clientHeight + this._docs.clientHeight);\n                this._onDidChangeContents.fire(this);\n            }));\n        }\n        this.domNode.style.userSelect = 'text';\n        this.domNode.tabIndex = -1;\n        this._close.onmousedown = e => {\n            e.preventDefault();\n            e.stopPropagation();\n        };\n        this._close.onclick = e => {\n            e.preventDefault();\n            e.stopPropagation();\n            this._onDidClose.fire();\n        };\n        this._body.scrollTop = 0;\n        this.layout(this._size.width, this._type.clientHeight + this._docs.clientHeight);\n        this._onDidChangeContents.fire(this);\n    }\n    clearContents() {\n        this.domNode.classList.add('no-docs');\n        this._type.textContent = '';\n        this._docs.textContent = '';\n    }\n    get isEmpty() {\n        return this.domNode.classList.contains('no-docs');\n    }\n    get size() {\n        return this._size;\n    }\n    layout(width, height) {\n        const newSize = new dom.Dimension(width, height);\n        if (!dom.Dimension.equals(newSize, this._size)) {\n            this._size = newSize;\n            dom.size(this.domNode, width, height);\n        }\n        this._scrollbar.scanDomNode();\n    }\n    scrollDown(much = 8) {\n        this._body.scrollTop += much;\n    }\n    scrollUp(much = 8) {\n        this._body.scrollTop -= much;\n    }\n    scrollTop() {\n        this._body.scrollTop = 0;\n    }\n    scrollBottom() {\n        this._body.scrollTop = this._body.scrollHeight;\n    }\n    pageDown() {\n        this.scrollDown(80);\n    }\n    pageUp() {\n        this.scrollUp(80);\n    }\n    set borderWidth(width) {\n        this._borderWidth = width;\n    }\n    get borderWidth() {\n        return this._borderWidth;\n    }\n};\nSuggestDetailsWidget = __decorate([\n    __param(1, IInstantiationService)\n], SuggestDetailsWidget);\nexport { SuggestDetailsWidget };\nexport class SuggestDetailsOverlay {\n    constructor(widget, _editor) {\n        this.widget = widget;\n        this._editor = _editor;\n        this.allowEditorOverflow = true;\n        this._disposables = new DisposableStore();\n        this._added = false;\n        this._preferAlignAtTop = true;\n        this._resizable = new ResizableHTMLElement();\n        this._resizable.domNode.classList.add('suggest-details-container');\n        this._resizable.domNode.appendChild(widget.domNode);\n        this._resizable.enableSashes(false, true, true, false);\n        let topLeftNow;\n        let sizeNow;\n        let deltaTop = 0;\n        let deltaLeft = 0;\n        this._disposables.add(this._resizable.onDidWillResize(() => {\n            topLeftNow = this._topLeft;\n            sizeNow = this._resizable.size;\n        }));\n        this._disposables.add(this._resizable.onDidResize(e => {\n            if (topLeftNow && sizeNow) {\n                this.widget.layout(e.dimension.width, e.dimension.height);\n                let updateTopLeft = false;\n                if (e.west) {\n                    deltaLeft = sizeNow.width - e.dimension.width;\n                    updateTopLeft = true;\n                }\n                if (e.north) {\n                    deltaTop = sizeNow.height - e.dimension.height;\n                    updateTopLeft = true;\n                }\n                if (updateTopLeft) {\n                    this._applyTopLeft({\n                        top: topLeftNow.top + deltaTop,\n                        left: topLeftNow.left + deltaLeft,\n                    });\n                }\n            }\n            if (e.done) {\n                topLeftNow = undefined;\n                sizeNow = undefined;\n                deltaTop = 0;\n                deltaLeft = 0;\n                this._userSize = e.dimension;\n            }\n        }));\n        this._disposables.add(this.widget.onDidChangeContents(() => {\n            if (this._anchorBox) {\n                this._placeAtAnchor(this._anchorBox, this._userSize ?? this.widget.size, this._preferAlignAtTop);\n            }\n        }));\n    }\n    dispose() {\n        this._resizable.dispose();\n        this._disposables.dispose();\n        this.hide();\n    }\n    getId() {\n        return 'suggest.details';\n    }\n    getDomNode() {\n        return this._resizable.domNode;\n    }\n    getPosition() {\n        return this._topLeft ? { preference: this._topLeft } : null;\n    }\n    show() {\n        if (!this._added) {\n            this._editor.addOverlayWidget(this);\n            this._added = true;\n        }\n    }\n    hide(sessionEnded = false) {\n        this._resizable.clearSashHoverState();\n        if (this._added) {\n            this._editor.removeOverlayWidget(this);\n            this._added = false;\n            this._anchorBox = undefined;\n            this._topLeft = undefined;\n        }\n        if (sessionEnded) {\n            this._userSize = undefined;\n            this.widget.clearContents();\n        }\n    }\n    placeAtAnchor(anchor, preferAlignAtTop) {\n        const anchorBox = anchor.getBoundingClientRect();\n        this._anchorBox = anchorBox;\n        this._preferAlignAtTop = preferAlignAtTop;\n        this._placeAtAnchor(this._anchorBox, this._userSize ?? this.widget.size, preferAlignAtTop);\n    }\n    _placeAtAnchor(anchorBox, size, preferAlignAtTop) {\n        const bodyBox = dom.getClientArea(this.getDomNode().ownerDocument.body);\n        const info = this.widget.getLayoutInfo();\n        const defaultMinSize = new dom.Dimension(220, 2 * info.lineHeight);\n        const defaultTop = anchorBox.top;\n        // EAST\n        const eastPlacement = (function () {\n            const width = bodyBox.width - (anchorBox.left + anchorBox.width + info.borderWidth + info.horizontalPadding);\n            const left = -info.borderWidth + anchorBox.left + anchorBox.width;\n            const maxSizeTop = new dom.Dimension(width, bodyBox.height - anchorBox.top - info.borderHeight - info.verticalPadding);\n            const maxSizeBottom = maxSizeTop.with(undefined, anchorBox.top + anchorBox.height - info.borderHeight - info.verticalPadding);\n            return { top: defaultTop, left, fit: width - size.width, maxSizeTop, maxSizeBottom, minSize: defaultMinSize.with(Math.min(width, defaultMinSize.width)) };\n        })();\n        // WEST\n        const westPlacement = (function () {\n            const width = anchorBox.left - info.borderWidth - info.horizontalPadding;\n            const left = Math.max(info.horizontalPadding, anchorBox.left - size.width - info.borderWidth);\n            const maxSizeTop = new dom.Dimension(width, bodyBox.height - anchorBox.top - info.borderHeight - info.verticalPadding);\n            const maxSizeBottom = maxSizeTop.with(undefined, anchorBox.top + anchorBox.height - info.borderHeight - info.verticalPadding);\n            return { top: defaultTop, left, fit: width - size.width, maxSizeTop, maxSizeBottom, minSize: defaultMinSize.with(Math.min(width, defaultMinSize.width)) };\n        })();\n        // SOUTH\n        const southPacement = (function () {\n            const left = anchorBox.left;\n            const top = -info.borderWidth + anchorBox.top + anchorBox.height;\n            const maxSizeBottom = new dom.Dimension(anchorBox.width - info.borderHeight, bodyBox.height - anchorBox.top - anchorBox.height - info.verticalPadding);\n            return { top, left, fit: maxSizeBottom.height - size.height, maxSizeBottom, maxSizeTop: maxSizeBottom, minSize: defaultMinSize.with(maxSizeBottom.width) };\n        })();\n        // take first placement that fits or the first with \"least bad\" fit\n        const placements = [eastPlacement, westPlacement, southPacement];\n        const placement = placements.find(p => p.fit >= 0) ?? placements.sort((a, b) => b.fit - a.fit)[0];\n        // top/bottom placement\n        const bottom = anchorBox.top + anchorBox.height - info.borderHeight;\n        let alignAtTop;\n        let height = size.height;\n        const maxHeight = Math.max(placement.maxSizeTop.height, placement.maxSizeBottom.height);\n        if (height > maxHeight) {\n            height = maxHeight;\n        }\n        let maxSize;\n        if (preferAlignAtTop) {\n            if (height <= placement.maxSizeTop.height) {\n                alignAtTop = true;\n                maxSize = placement.maxSizeTop;\n            }\n            else {\n                alignAtTop = false;\n                maxSize = placement.maxSizeBottom;\n            }\n        }\n        else {\n            if (height <= placement.maxSizeBottom.height) {\n                alignAtTop = false;\n                maxSize = placement.maxSizeBottom;\n            }\n            else {\n                alignAtTop = true;\n                maxSize = placement.maxSizeTop;\n            }\n        }\n        let { top, left } = placement;\n        if (!alignAtTop && height > anchorBox.height) {\n            top = bottom - height;\n        }\n        const editorDomNode = this._editor.getDomNode();\n        if (editorDomNode) {\n            // get bounding rectangle of the suggest widget relative to the editor\n            const editorBoundingBox = editorDomNode.getBoundingClientRect();\n            top -= editorBoundingBox.top;\n            left -= editorBoundingBox.left;\n        }\n        this._applyTopLeft({ left, top });\n        this._resizable.enableSashes(!alignAtTop, placement === eastPlacement, alignAtTop, placement !== eastPlacement);\n        this._resizable.minSize = placement.minSize;\n        this._resizable.maxSize = maxSize;\n        this._resizable.layout(height, Math.min(maxSize.width, size.width));\n        this.widget.layout(this._resizable.size.width, this._resizable.size.height);\n    }\n    _applyTopLeft(topLeft) {\n        this._topLeft = topLeft;\n        this._editor.layoutOverlayWidget(this);\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,IAAIA,UAAU,GAAI,IAAI,IAAI,IAAI,CAACA,UAAU,IAAK,UAAUC,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAE;EACnF,IAAIC,CAAC,GAAGC,SAAS,CAACC,MAAM;IAAEC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGH,MAAM,GAAGE,IAAI,KAAK,IAAI,GAAGA,IAAI,GAAGK,MAAM,CAACC,wBAAwB,CAACR,MAAM,EAAEC,GAAG,CAAC,GAAGC,IAAI;IAAEO,CAAC;EAC5H,IAAI,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,CAACC,QAAQ,KAAK,UAAU,EAAEL,CAAC,GAAGI,OAAO,CAACC,QAAQ,CAACZ,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,CAAC,CAAC,KAC1H,KAAK,IAAIU,CAAC,GAAGb,UAAU,CAACM,MAAM,GAAG,CAAC,EAAEO,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE,IAAIH,CAAC,GAAGV,UAAU,CAACa,CAAC,CAAC,EAAEN,CAAC,GAAG,CAACH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACH,CAAC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACT,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,GAAGG,CAAC,CAACT,MAAM,EAAEC,GAAG,CAAC,KAAKK,CAAC;EACjJ,OAAOH,CAAC,GAAG,CAAC,IAAIG,CAAC,IAAIC,MAAM,CAACM,cAAc,CAACb,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,EAAEA,CAAC;AACjE,CAAC;AACD,IAAIQ,OAAO,GAAI,IAAI,IAAI,IAAI,CAACA,OAAO,IAAK,UAAUC,UAAU,EAAEC,SAAS,EAAE;EACrE,OAAO,UAAUhB,MAAM,EAAEC,GAAG,EAAE;IAAEe,SAAS,CAAChB,MAAM,EAAEC,GAAG,EAAEc,UAAU,CAAC;EAAE,CAAC;AACzE,CAAC;AACD,OAAO,KAAKE,GAAG,MAAM,iCAAiC;AACtD,SAASC,oBAAoB,QAAQ,4DAA4D;AACjG,SAASC,OAAO,QAAQ,qCAAqC;AAC7D,SAASC,SAAS,QAAQ,sCAAsC;AAChE,SAASC,OAAO,QAAQ,kCAAkC;AAC1D,SAASC,cAAc,QAAQ,wCAAwC;AACvE,SAASC,eAAe,QAAQ,sCAAsC;AACtE,SAASC,gBAAgB,QAAQ,sEAAsE;AACvG,SAASC,oBAAoB,QAAQ,oDAAoD;AACzF,OAAO,KAAKC,GAAG,MAAM,oBAAoB;AACzC,SAASC,qBAAqB,QAAQ,4DAA4D;AAClG,OAAO,SAASC,uBAAuBA,CAACC,IAAI,EAAE;EAC1C,OAAO,CAAC,CAACA,IAAI,IAAIC,OAAO,CAACD,IAAI,CAACE,UAAU,CAACC,aAAa,IAAIH,IAAI,CAACE,UAAU,CAACE,MAAM,IAAIJ,IAAI,CAACE,UAAU,CAACE,MAAM,KAAKJ,IAAI,CAACE,UAAU,CAACG,KAAK,CAAC;AACzI;AACA,IAAIC,oBAAoB,GAAG,MAAMA,oBAAoB,CAAC;EAClDC,WAAWA,CAACC,OAAO,EAAEC,YAAY,EAAE;IAC/B,IAAI,CAACD,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACE,WAAW,GAAG,IAAIlB,OAAO,CAAC,CAAC;IAChC,IAAI,CAACmB,UAAU,GAAG,IAAI,CAACD,WAAW,CAACE,KAAK;IACxC,IAAI,CAACC,oBAAoB,GAAG,IAAIrB,OAAO,CAAC,CAAC;IACzC,IAAI,CAACsB,mBAAmB,GAAG,IAAI,CAACD,oBAAoB,CAACD,KAAK;IAC1D,IAAI,CAACG,YAAY,GAAG,IAAIrB,eAAe,CAAC,CAAC;IACzC,IAAI,CAACsB,kBAAkB,GAAG,IAAItB,eAAe,CAAC,CAAC;IAC/C,IAAI,CAACuB,YAAY,GAAG,CAAC;IACrB,IAAI,CAACC,KAAK,GAAG,IAAI9B,GAAG,CAAC+B,SAAS,CAAC,GAAG,EAAE,CAAC,CAAC;IACtC,IAAI,CAACC,OAAO,GAAGhC,GAAG,CAACiC,CAAC,CAAC,kBAAkB,CAAC;IACxC,IAAI,CAACD,OAAO,CAACE,SAAS,CAACC,GAAG,CAAC,SAAS,CAAC;IACrC,IAAI,CAACC,iBAAiB,GAAGf,YAAY,CAACgB,cAAc,CAAC9B,gBAAgB,EAAE;MAAE+B,MAAM,EAAElB;IAAQ,CAAC,CAAC;IAC3F,IAAI,CAACmB,KAAK,GAAGvC,GAAG,CAACiC,CAAC,CAAC,OAAO,CAAC;IAC3B,IAAI,CAACO,UAAU,GAAG,IAAIvC,oBAAoB,CAAC,IAAI,CAACsC,KAAK,EAAE;MACnDE,uBAAuB,EAAE;IAC7B,CAAC,CAAC;IACFzC,GAAG,CAAC0C,MAAM,CAAC,IAAI,CAACV,OAAO,EAAE,IAAI,CAACQ,UAAU,CAACG,UAAU,CAAC,CAAC,CAAC;IACtD,IAAI,CAAChB,YAAY,CAACQ,GAAG,CAAC,IAAI,CAACK,UAAU,CAAC;IACtC,IAAI,CAACI,OAAO,GAAG5C,GAAG,CAAC0C,MAAM,CAAC,IAAI,CAACH,KAAK,EAAEvC,GAAG,CAACiC,CAAC,CAAC,SAAS,CAAC,CAAC;IACvD,IAAI,CAACY,MAAM,GAAG7C,GAAG,CAAC0C,MAAM,CAAC,IAAI,CAACE,OAAO,EAAE5C,GAAG,CAACiC,CAAC,CAAC,MAAM,GAAG9B,SAAS,CAAC2C,aAAa,CAAC5C,OAAO,CAAC6C,KAAK,CAAC,CAAC,CAAC;IAC9F,IAAI,CAACF,MAAM,CAACG,KAAK,GAAGvC,GAAG,CAACwC,QAAQ,CAAC,eAAe,EAAE,OAAO,CAAC;IAC1D,IAAI,CAACC,KAAK,GAAGlD,GAAG,CAAC0C,MAAM,CAAC,IAAI,CAACE,OAAO,EAAE5C,GAAG,CAACiC,CAAC,CAAC,QAAQ,CAAC,CAAC;IACtD,IAAI,CAACkB,KAAK,GAAGnD,GAAG,CAAC0C,MAAM,CAAC,IAAI,CAACH,KAAK,EAAEvC,GAAG,CAACiC,CAAC,CAAC,QAAQ,CAAC,CAAC;IACpD,IAAI,CAACmB,cAAc,CAAC,CAAC;IACrB,IAAI,CAACzB,YAAY,CAACQ,GAAG,CAAC,IAAI,CAACf,OAAO,CAACiC,wBAAwB,CAACC,CAAC,IAAI;MAC7D,IAAIA,CAAC,CAACC,UAAU,CAAC,EAAE,CAAC,2BAA2B,CAAC,EAAE;QAC9C,IAAI,CAACH,cAAc,CAAC,CAAC;MACzB;IACJ,CAAC,CAAC,CAAC;EACP;EACAI,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC7B,YAAY,CAAC6B,OAAO,CAAC,CAAC;IAC3B,IAAI,CAAC5B,kBAAkB,CAAC4B,OAAO,CAAC,CAAC;EACrC;EACAJ,cAAcA,CAAA,EAAG;IACb,MAAMK,OAAO,GAAG,IAAI,CAACrC,OAAO,CAACsC,UAAU,CAAC,CAAC;IACzC,MAAMC,QAAQ,GAAGF,OAAO,CAACG,GAAG,CAAC,EAAE,CAAC,2BAA2B,CAAC;IAC5D,MAAMC,UAAU,GAAGF,QAAQ,CAACG,qBAAqB,CAAC,CAAC;IACnD,MAAMC,QAAQ,GAAGN,OAAO,CAACG,GAAG,CAAC,GAAG,CAAC,kCAAkC,CAAC,IAAID,QAAQ,CAACI,QAAQ;IACzF,MAAMC,UAAU,GAAGP,OAAO,CAACG,GAAG,CAAC,GAAG,CAAC,oCAAoC,CAAC,IAAID,QAAQ,CAACK,UAAU;IAC/F,MAAMC,UAAU,GAAGN,QAAQ,CAACM,UAAU;IACtC,MAAMC,UAAU,GAAG,GAAGH,QAAQ,IAAI;IAClC,MAAMI,YAAY,GAAG,GAAGH,UAAU,IAAI;IACtC,IAAI,CAAChC,OAAO,CAACoC,KAAK,CAACL,QAAQ,GAAGG,UAAU;IACxC,IAAI,CAAClC,OAAO,CAACoC,KAAK,CAACJ,UAAU,GAAG,GAAGA,UAAU,GAAGD,QAAQ,EAAE;IAC1D,IAAI,CAAC/B,OAAO,CAACoC,KAAK,CAACH,UAAU,GAAGA,UAAU;IAC1C,IAAI,CAACjC,OAAO,CAACoC,KAAK,CAACC,mBAAmB,GAAGV,QAAQ,CAACU,mBAAmB;IACrE,IAAI,CAACnB,KAAK,CAACkB,KAAK,CAACP,UAAU,GAAGA,UAAU;IACxC,IAAI,CAAChB,MAAM,CAACuB,KAAK,CAACE,MAAM,GAAGH,YAAY;IACvC,IAAI,CAACtB,MAAM,CAACuB,KAAK,CAACG,KAAK,GAAGJ,YAAY;EAC1C;EACAK,aAAaA,CAAA,EAAG;IACZ,MAAMR,UAAU,GAAG,IAAI,CAAC5C,OAAO,CAACqD,SAAS,CAAC,GAAG,CAAC,oCAAoC,CAAC,IAAI,IAAI,CAACrD,OAAO,CAACqD,SAAS,CAAC,EAAE,CAAC,2BAA2B,CAAC,CAACT,UAAU;IACxJ,MAAMU,WAAW,GAAG,IAAI,CAAC7C,YAAY;IACrC,MAAM8C,YAAY,GAAGD,WAAW,GAAG,CAAC;IACpC,OAAO;MACHV,UAAU;MACVU,WAAW;MACXC,YAAY;MACZC,eAAe,EAAE,EAAE;MACnBC,iBAAiB,EAAE;IACvB,CAAC;EACL;EACAC,aAAaA,CAAA,EAAG;IACZ,IAAI,CAAC5B,KAAK,CAAC6B,WAAW,GAAGtE,GAAG,CAACwC,QAAQ,CAAC,SAAS,EAAE,YAAY,CAAC;IAC9D,IAAI,CAACE,KAAK,CAAC4B,WAAW,GAAG,EAAE;IAC3B,IAAI,CAAC/C,OAAO,CAACE,SAAS,CAAC8C,MAAM,CAAC,SAAS,EAAE,SAAS,CAAC;IACnD,IAAI,CAACC,MAAM,CAAC,IAAI,CAACC,IAAI,CAACX,KAAK,EAAE,IAAI,CAACC,aAAa,CAAC,CAAC,CAACR,UAAU,GAAG,CAAC,CAAC;IACjE,IAAI,CAACvC,oBAAoB,CAAC0D,IAAI,CAAC,IAAI,CAAC;EACxC;EACAC,UAAUA,CAACxE,IAAI,EAAEyE,WAAW,EAAE;IAC1B,IAAI,CAACzD,kBAAkB,CAAC0D,KAAK,CAAC,CAAC;IAC/B,IAAI;MAAEtE,MAAM;MAAED;IAAc,CAAC,GAAGH,IAAI,CAACE,UAAU;IAC/C,IAAIuE,WAAW,EAAE;MACb,IAAIE,EAAE,GAAG,EAAE;MACXA,EAAE,IAAI,UAAU3E,IAAI,CAAC4E,KAAK,CAAC,CAAC,CAAC,IAAI;MACjCD,EAAE,IAAI,WAAW3E,IAAI,CAAC6E,IAAI,IAAI,aAAa,IAAI;MAC/CF,EAAE,IAAI,SAAS3E,IAAI,CAACE,UAAU,CAAC4E,UAAU,GAAG9E,IAAI,CAACE,UAAU,CAAC4E,UAAU,GAAG,eAAe,GAAG9E,IAAI,CAAC+E,SAAS,IAAI;MAC7GJ,EAAE,IAAI,aAAa3E,IAAI,CAACgF,QAAQ,4BAA4B;MAC5DL,EAAE,IAAI,UAAU3E,IAAI,CAACiF,GAAG,cAAcjF,IAAI,CAACE,UAAU,CAACgF,QAAQ,IAAI,cAAclF,IAAI,CAACE,UAAU,CAACgF,QAAQ,GAAG,IAAI,OAAO,IAAI;MAC1HP,EAAE,IAAI,iBAAiB3E,IAAI,CAACE,UAAU,CAACiF,gBAAgB,EAAEC,IAAI,CAAC,EAAE,CAAC,IAAI;MACrEjF,aAAa,GAAG,IAAIV,cAAc,CAAC,CAAC,CAAC4F,eAAe,CAAC,OAAO,EAAEV,EAAE,CAAC;MACjEvE,MAAM,GAAG,aAAaJ,IAAI,CAACsF,QAAQ,CAACC,iBAAiB,EAAE;IAC3D;IACA,IAAI,CAACd,WAAW,IAAI,CAAC1E,uBAAuB,CAACC,IAAI,CAAC,EAAE;MAChD,IAAI,CAACwF,aAAa,CAAC,CAAC;MACpB;IACJ;IACA,IAAI,CAACpE,OAAO,CAACE,SAAS,CAAC8C,MAAM,CAAC,SAAS,EAAE,SAAS,CAAC;IACnD;IACA,IAAIhE,MAAM,EAAE;MACR,MAAMqF,YAAY,GAAGrF,MAAM,CAAC5B,MAAM,GAAG,MAAM,GAAG,GAAG4B,MAAM,CAACsF,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,GAAG,GAAGtF,MAAM;MACrF,IAAI,CAACkC,KAAK,CAAC6B,WAAW,GAAGsB,YAAY;MACrC,IAAI,CAACnD,KAAK,CAACF,KAAK,GAAGqD,YAAY;MAC/BrG,GAAG,CAACuG,IAAI,CAAC,IAAI,CAACrD,KAAK,CAAC;MACpB,IAAI,CAACA,KAAK,CAAChB,SAAS,CAACsE,MAAM,CAAC,WAAW,EAAE,CAAC,cAAc,CAACC,IAAI,CAACJ,YAAY,CAAC,CAAC;IAChF,CAAC,MACI;MACDrG,GAAG,CAAC0G,SAAS,CAAC,IAAI,CAACxD,KAAK,CAAC;MACzB,IAAI,CAACA,KAAK,CAACF,KAAK,GAAG,EAAE;MACrBhD,GAAG,CAAC2G,IAAI,CAAC,IAAI,CAACzD,KAAK,CAAC;MACpB,IAAI,CAAClB,OAAO,CAACE,SAAS,CAACC,GAAG,CAAC,SAAS,CAAC;IACzC;IACA;IACAnC,GAAG,CAAC0G,SAAS,CAAC,IAAI,CAACvD,KAAK,CAAC;IACzB,IAAI,OAAOpC,aAAa,KAAK,QAAQ,EAAE;MACnC,IAAI,CAACoC,KAAK,CAACjB,SAAS,CAAC8C,MAAM,CAAC,eAAe,CAAC;MAC5C,IAAI,CAAC7B,KAAK,CAAC4B,WAAW,GAAGhE,aAAa;IAC1C,CAAC,MACI,IAAIA,aAAa,EAAE;MACpB,IAAI,CAACoC,KAAK,CAACjB,SAAS,CAACC,GAAG,CAAC,eAAe,CAAC;MACzCnC,GAAG,CAAC0G,SAAS,CAAC,IAAI,CAACvD,KAAK,CAAC;MACzB,MAAMyD,gBAAgB,GAAG,IAAI,CAACxE,iBAAiB,CAACyE,MAAM,CAAC9F,aAAa,CAAC;MACrE,IAAI,CAACoC,KAAK,CAAC2D,WAAW,CAACF,gBAAgB,CAACG,OAAO,CAAC;MAChD,IAAI,CAACnF,kBAAkB,CAACO,GAAG,CAACyE,gBAAgB,CAAC;MAC7C,IAAI,CAAChF,kBAAkB,CAACO,GAAG,CAAC,IAAI,CAACC,iBAAiB,CAAC4E,gBAAgB,CAAC,MAAM;QACtE,IAAI,CAAC/B,MAAM,CAAC,IAAI,CAACnD,KAAK,CAACyC,KAAK,EAAE,IAAI,CAACrB,KAAK,CAAC+D,YAAY,GAAG,IAAI,CAAC9D,KAAK,CAAC8D,YAAY,CAAC;QAChF,IAAI,CAACxF,oBAAoB,CAAC0D,IAAI,CAAC,IAAI,CAAC;MACxC,CAAC,CAAC,CAAC;IACP;IACA,IAAI,CAACnD,OAAO,CAACoC,KAAK,CAAC8C,UAAU,GAAG,MAAM;IACtC,IAAI,CAAClF,OAAO,CAACmF,QAAQ,GAAG,CAAC,CAAC;IAC1B,IAAI,CAACtE,MAAM,CAACuE,WAAW,GAAG9D,CAAC,IAAI;MAC3BA,CAAC,CAAC+D,cAAc,CAAC,CAAC;MAClB/D,CAAC,CAACgE,eAAe,CAAC,CAAC;IACvB,CAAC;IACD,IAAI,CAACzE,MAAM,CAAC0E,OAAO,GAAGjE,CAAC,IAAI;MACvBA,CAAC,CAAC+D,cAAc,CAAC,CAAC;MAClB/D,CAAC,CAACgE,eAAe,CAAC,CAAC;MACnB,IAAI,CAAChG,WAAW,CAAC6D,IAAI,CAAC,CAAC;IAC3B,CAAC;IACD,IAAI,CAAC5C,KAAK,CAACiF,SAAS,GAAG,CAAC;IACxB,IAAI,CAACvC,MAAM,CAAC,IAAI,CAACnD,KAAK,CAACyC,KAAK,EAAE,IAAI,CAACrB,KAAK,CAAC+D,YAAY,GAAG,IAAI,CAAC9D,KAAK,CAAC8D,YAAY,CAAC;IAChF,IAAI,CAACxF,oBAAoB,CAAC0D,IAAI,CAAC,IAAI,CAAC;EACxC;EACAiB,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACpE,OAAO,CAACE,SAAS,CAACC,GAAG,CAAC,SAAS,CAAC;IACrC,IAAI,CAACe,KAAK,CAAC6B,WAAW,GAAG,EAAE;IAC3B,IAAI,CAAC5B,KAAK,CAAC4B,WAAW,GAAG,EAAE;EAC/B;EACA,IAAI0C,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACzF,OAAO,CAACE,SAAS,CAACwF,QAAQ,CAAC,SAAS,CAAC;EACrD;EACA,IAAIxC,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACpD,KAAK;EACrB;EACAmD,MAAMA,CAACV,KAAK,EAAED,MAAM,EAAE;IAClB,MAAMqD,OAAO,GAAG,IAAI3H,GAAG,CAAC+B,SAAS,CAACwC,KAAK,EAAED,MAAM,CAAC;IAChD,IAAI,CAACtE,GAAG,CAAC+B,SAAS,CAAC6F,MAAM,CAACD,OAAO,EAAE,IAAI,CAAC7F,KAAK,CAAC,EAAE;MAC5C,IAAI,CAACA,KAAK,GAAG6F,OAAO;MACpB3H,GAAG,CAACkF,IAAI,CAAC,IAAI,CAAClD,OAAO,EAAEuC,KAAK,EAAED,MAAM,CAAC;IACzC;IACA,IAAI,CAAC9B,UAAU,CAACqF,WAAW,CAAC,CAAC;EACjC;EACAC,UAAUA,CAACC,IAAI,GAAG,CAAC,EAAE;IACjB,IAAI,CAACxF,KAAK,CAACiF,SAAS,IAAIO,IAAI;EAChC;EACAC,QAAQA,CAACD,IAAI,GAAG,CAAC,EAAE;IACf,IAAI,CAACxF,KAAK,CAACiF,SAAS,IAAIO,IAAI;EAChC;EACAP,SAASA,CAAA,EAAG;IACR,IAAI,CAACjF,KAAK,CAACiF,SAAS,GAAG,CAAC;EAC5B;EACAS,YAAYA,CAAA,EAAG;IACX,IAAI,CAAC1F,KAAK,CAACiF,SAAS,GAAG,IAAI,CAACjF,KAAK,CAAC2F,YAAY;EAClD;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACL,UAAU,CAAC,EAAE,CAAC;EACvB;EACAM,MAAMA,CAAA,EAAG;IACL,IAAI,CAACJ,QAAQ,CAAC,EAAE,CAAC;EACrB;EACA,IAAItD,WAAWA,CAACH,KAAK,EAAE;IACnB,IAAI,CAAC1C,YAAY,GAAG0C,KAAK;EAC7B;EACA,IAAIG,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAAC7C,YAAY;EAC5B;AACJ,CAAC;AACDX,oBAAoB,GAAGrC,UAAU,CAAC,CAC9BgB,OAAO,CAAC,CAAC,EAAEa,qBAAqB,CAAC,CACpC,EAAEQ,oBAAoB,CAAC;AACxB,SAASA,oBAAoB;AAC7B,OAAO,MAAMmH,qBAAqB,CAAC;EAC/BlH,WAAWA,CAACmH,MAAM,EAAElH,OAAO,EAAE;IACzB,IAAI,CAACkH,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAClH,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACmH,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAAC5G,YAAY,GAAG,IAAIrB,eAAe,CAAC,CAAC;IACzC,IAAI,CAACkI,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACC,UAAU,GAAG,IAAIlI,oBAAoB,CAAC,CAAC;IAC5C,IAAI,CAACkI,UAAU,CAAC1G,OAAO,CAACE,SAAS,CAACC,GAAG,CAAC,2BAA2B,CAAC;IAClE,IAAI,CAACuG,UAAU,CAAC1G,OAAO,CAAC8E,WAAW,CAACwB,MAAM,CAACtG,OAAO,CAAC;IACnD,IAAI,CAAC0G,UAAU,CAACC,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC;IACtD,IAAIC,UAAU;IACd,IAAIC,OAAO;IACX,IAAIC,QAAQ,GAAG,CAAC;IAChB,IAAIC,SAAS,GAAG,CAAC;IACjB,IAAI,CAACpH,YAAY,CAACQ,GAAG,CAAC,IAAI,CAACuG,UAAU,CAACM,eAAe,CAAC,MAAM;MACxDJ,UAAU,GAAG,IAAI,CAACK,QAAQ;MAC1BJ,OAAO,GAAG,IAAI,CAACH,UAAU,CAACxD,IAAI;IAClC,CAAC,CAAC,CAAC;IACH,IAAI,CAACvD,YAAY,CAACQ,GAAG,CAAC,IAAI,CAACuG,UAAU,CAACQ,WAAW,CAAC5F,CAAC,IAAI;MACnD,IAAIsF,UAAU,IAAIC,OAAO,EAAE;QACvB,IAAI,CAACP,MAAM,CAACrD,MAAM,CAAC3B,CAAC,CAAC6F,SAAS,CAAC5E,KAAK,EAAEjB,CAAC,CAAC6F,SAAS,CAAC7E,MAAM,CAAC;QACzD,IAAI8E,aAAa,GAAG,KAAK;QACzB,IAAI9F,CAAC,CAAC+F,IAAI,EAAE;UACRN,SAAS,GAAGF,OAAO,CAACtE,KAAK,GAAGjB,CAAC,CAAC6F,SAAS,CAAC5E,KAAK;UAC7C6E,aAAa,GAAG,IAAI;QACxB;QACA,IAAI9F,CAAC,CAACgG,KAAK,EAAE;UACTR,QAAQ,GAAGD,OAAO,CAACvE,MAAM,GAAGhB,CAAC,CAAC6F,SAAS,CAAC7E,MAAM;UAC9C8E,aAAa,GAAG,IAAI;QACxB;QACA,IAAIA,aAAa,EAAE;UACf,IAAI,CAACG,aAAa,CAAC;YACfC,GAAG,EAAEZ,UAAU,CAACY,GAAG,GAAGV,QAAQ;YAC9BW,IAAI,EAAEb,UAAU,CAACa,IAAI,GAAGV;UAC5B,CAAC,CAAC;QACN;MACJ;MACA,IAAIzF,CAAC,CAACoG,IAAI,EAAE;QACRd,UAAU,GAAGe,SAAS;QACtBd,OAAO,GAAGc,SAAS;QACnBb,QAAQ,GAAG,CAAC;QACZC,SAAS,GAAG,CAAC;QACb,IAAI,CAACa,SAAS,GAAGtG,CAAC,CAAC6F,SAAS;MAChC;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,CAACxH,YAAY,CAACQ,GAAG,CAAC,IAAI,CAACmG,MAAM,CAAC5G,mBAAmB,CAAC,MAAM;MACxD,IAAI,IAAI,CAACmI,UAAU,EAAE;QACjB,IAAI,CAACC,cAAc,CAAC,IAAI,CAACD,UAAU,EAAE,IAAI,CAACD,SAAS,IAAI,IAAI,CAACtB,MAAM,CAACpD,IAAI,EAAE,IAAI,CAACuD,iBAAiB,CAAC;MACpG;IACJ,CAAC,CAAC,CAAC;EACP;EACAjF,OAAOA,CAAA,EAAG;IACN,IAAI,CAACkF,UAAU,CAAClF,OAAO,CAAC,CAAC;IACzB,IAAI,CAAC7B,YAAY,CAAC6B,OAAO,CAAC,CAAC;IAC3B,IAAI,CAACmD,IAAI,CAAC,CAAC;EACf;EACAoD,KAAKA,CAAA,EAAG;IACJ,OAAO,iBAAiB;EAC5B;EACApH,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAAC+F,UAAU,CAAC1G,OAAO;EAClC;EACAgI,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACf,QAAQ,GAAG;MAAEgB,UAAU,EAAE,IAAI,CAAChB;IAAS,CAAC,GAAG,IAAI;EAC/D;EACA1C,IAAIA,CAAA,EAAG;IACH,IAAI,CAAC,IAAI,CAACiC,MAAM,EAAE;MACd,IAAI,CAACpH,OAAO,CAAC8I,gBAAgB,CAAC,IAAI,CAAC;MACnC,IAAI,CAAC1B,MAAM,GAAG,IAAI;IACtB;EACJ;EACA7B,IAAIA,CAACwD,YAAY,GAAG,KAAK,EAAE;IACvB,IAAI,CAACzB,UAAU,CAAC0B,mBAAmB,CAAC,CAAC;IACrC,IAAI,IAAI,CAAC5B,MAAM,EAAE;MACb,IAAI,CAACpH,OAAO,CAACiJ,mBAAmB,CAAC,IAAI,CAAC;MACtC,IAAI,CAAC7B,MAAM,GAAG,KAAK;MACnB,IAAI,CAACqB,UAAU,GAAGF,SAAS;MAC3B,IAAI,CAACV,QAAQ,GAAGU,SAAS;IAC7B;IACA,IAAIQ,YAAY,EAAE;MACd,IAAI,CAACP,SAAS,GAAGD,SAAS;MAC1B,IAAI,CAACrB,MAAM,CAAClC,aAAa,CAAC,CAAC;IAC/B;EACJ;EACAkE,aAAaA,CAACC,MAAM,EAAEC,gBAAgB,EAAE;IACpC,MAAMC,SAAS,GAAGF,MAAM,CAACG,qBAAqB,CAAC,CAAC;IAChD,IAAI,CAACb,UAAU,GAAGY,SAAS;IAC3B,IAAI,CAAChC,iBAAiB,GAAG+B,gBAAgB;IACzC,IAAI,CAACV,cAAc,CAAC,IAAI,CAACD,UAAU,EAAE,IAAI,CAACD,SAAS,IAAI,IAAI,CAACtB,MAAM,CAACpD,IAAI,EAAEsF,gBAAgB,CAAC;EAC9F;EACAV,cAAcA,CAACW,SAAS,EAAEvF,IAAI,EAAEsF,gBAAgB,EAAE;IAC9C,MAAMG,OAAO,GAAG3K,GAAG,CAAC4K,aAAa,CAAC,IAAI,CAACjI,UAAU,CAAC,CAAC,CAACkI,aAAa,CAACC,IAAI,CAAC;IACvE,MAAMC,IAAI,GAAG,IAAI,CAACzC,MAAM,CAAC9D,aAAa,CAAC,CAAC;IACxC,MAAMwG,cAAc,GAAG,IAAIhL,GAAG,CAAC+B,SAAS,CAAC,GAAG,EAAE,CAAC,GAAGgJ,IAAI,CAAC/G,UAAU,CAAC;IAClE,MAAMiH,UAAU,GAAGR,SAAS,CAACjB,GAAG;IAChC;IACA,MAAM0B,aAAa,GAAI,YAAY;MAC/B,MAAM3G,KAAK,GAAGoG,OAAO,CAACpG,KAAK,IAAIkG,SAAS,CAAChB,IAAI,GAAGgB,SAAS,CAAClG,KAAK,GAAGwG,IAAI,CAACrG,WAAW,GAAGqG,IAAI,CAAClG,iBAAiB,CAAC;MAC5G,MAAM4E,IAAI,GAAG,CAACsB,IAAI,CAACrG,WAAW,GAAG+F,SAAS,CAAChB,IAAI,GAAGgB,SAAS,CAAClG,KAAK;MACjE,MAAM4G,UAAU,GAAG,IAAInL,GAAG,CAAC+B,SAAS,CAACwC,KAAK,EAAEoG,OAAO,CAACrG,MAAM,GAAGmG,SAAS,CAACjB,GAAG,GAAGuB,IAAI,CAACpG,YAAY,GAAGoG,IAAI,CAACnG,eAAe,CAAC;MACtH,MAAMwG,aAAa,GAAGD,UAAU,CAACE,IAAI,CAAC1B,SAAS,EAAEc,SAAS,CAACjB,GAAG,GAAGiB,SAAS,CAACnG,MAAM,GAAGyG,IAAI,CAACpG,YAAY,GAAGoG,IAAI,CAACnG,eAAe,CAAC;MAC7H,OAAO;QAAE4E,GAAG,EAAEyB,UAAU;QAAExB,IAAI;QAAE6B,GAAG,EAAE/G,KAAK,GAAGW,IAAI,CAACX,KAAK;QAAE4G,UAAU;QAAEC,aAAa;QAAEG,OAAO,EAAEP,cAAc,CAACK,IAAI,CAACG,IAAI,CAACC,GAAG,CAAClH,KAAK,EAAEyG,cAAc,CAACzG,KAAK,CAAC;MAAE,CAAC;IAC7J,CAAC,CAAE,CAAC;IACJ;IACA,MAAMmH,aAAa,GAAI,YAAY;MAC/B,MAAMnH,KAAK,GAAGkG,SAAS,CAAChB,IAAI,GAAGsB,IAAI,CAACrG,WAAW,GAAGqG,IAAI,CAAClG,iBAAiB;MACxE,MAAM4E,IAAI,GAAG+B,IAAI,CAACG,GAAG,CAACZ,IAAI,CAAClG,iBAAiB,EAAE4F,SAAS,CAAChB,IAAI,GAAGvE,IAAI,CAACX,KAAK,GAAGwG,IAAI,CAACrG,WAAW,CAAC;MAC7F,MAAMyG,UAAU,GAAG,IAAInL,GAAG,CAAC+B,SAAS,CAACwC,KAAK,EAAEoG,OAAO,CAACrG,MAAM,GAAGmG,SAAS,CAACjB,GAAG,GAAGuB,IAAI,CAACpG,YAAY,GAAGoG,IAAI,CAACnG,eAAe,CAAC;MACtH,MAAMwG,aAAa,GAAGD,UAAU,CAACE,IAAI,CAAC1B,SAAS,EAAEc,SAAS,CAACjB,GAAG,GAAGiB,SAAS,CAACnG,MAAM,GAAGyG,IAAI,CAACpG,YAAY,GAAGoG,IAAI,CAACnG,eAAe,CAAC;MAC7H,OAAO;QAAE4E,GAAG,EAAEyB,UAAU;QAAExB,IAAI;QAAE6B,GAAG,EAAE/G,KAAK,GAAGW,IAAI,CAACX,KAAK;QAAE4G,UAAU;QAAEC,aAAa;QAAEG,OAAO,EAAEP,cAAc,CAACK,IAAI,CAACG,IAAI,CAACC,GAAG,CAAClH,KAAK,EAAEyG,cAAc,CAACzG,KAAK,CAAC;MAAE,CAAC;IAC7J,CAAC,CAAE,CAAC;IACJ;IACA,MAAMqH,aAAa,GAAI,YAAY;MAC/B,MAAMnC,IAAI,GAAGgB,SAAS,CAAChB,IAAI;MAC3B,MAAMD,GAAG,GAAG,CAACuB,IAAI,CAACrG,WAAW,GAAG+F,SAAS,CAACjB,GAAG,GAAGiB,SAAS,CAACnG,MAAM;MAChE,MAAM8G,aAAa,GAAG,IAAIpL,GAAG,CAAC+B,SAAS,CAAC0I,SAAS,CAAClG,KAAK,GAAGwG,IAAI,CAACpG,YAAY,EAAEgG,OAAO,CAACrG,MAAM,GAAGmG,SAAS,CAACjB,GAAG,GAAGiB,SAAS,CAACnG,MAAM,GAAGyG,IAAI,CAACnG,eAAe,CAAC;MACtJ,OAAO;QAAE4E,GAAG;QAAEC,IAAI;QAAE6B,GAAG,EAAEF,aAAa,CAAC9G,MAAM,GAAGY,IAAI,CAACZ,MAAM;QAAE8G,aAAa;QAAED,UAAU,EAAEC,aAAa;QAAEG,OAAO,EAAEP,cAAc,CAACK,IAAI,CAACD,aAAa,CAAC7G,KAAK;MAAE,CAAC;IAC9J,CAAC,CAAE,CAAC;IACJ;IACA,MAAMsH,UAAU,GAAG,CAACX,aAAa,EAAEQ,aAAa,EAAEE,aAAa,CAAC;IAChE,MAAME,SAAS,GAAGD,UAAU,CAACE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACV,GAAG,IAAI,CAAC,CAAC,IAAIO,UAAU,CAACI,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACb,GAAG,GAAGY,CAAC,CAACZ,GAAG,CAAC,CAAC,CAAC,CAAC;IACjG;IACA,MAAMc,MAAM,GAAG3B,SAAS,CAACjB,GAAG,GAAGiB,SAAS,CAACnG,MAAM,GAAGyG,IAAI,CAACpG,YAAY;IACnE,IAAI0H,UAAU;IACd,IAAI/H,MAAM,GAAGY,IAAI,CAACZ,MAAM;IACxB,MAAMgI,SAAS,GAAGd,IAAI,CAACG,GAAG,CAACG,SAAS,CAACX,UAAU,CAAC7G,MAAM,EAAEwH,SAAS,CAACV,aAAa,CAAC9G,MAAM,CAAC;IACvF,IAAIA,MAAM,GAAGgI,SAAS,EAAE;MACpBhI,MAAM,GAAGgI,SAAS;IACtB;IACA,IAAIC,OAAO;IACX,IAAI/B,gBAAgB,EAAE;MAClB,IAAIlG,MAAM,IAAIwH,SAAS,CAACX,UAAU,CAAC7G,MAAM,EAAE;QACvC+H,UAAU,GAAG,IAAI;QACjBE,OAAO,GAAGT,SAAS,CAACX,UAAU;MAClC,CAAC,MACI;QACDkB,UAAU,GAAG,KAAK;QAClBE,OAAO,GAAGT,SAAS,CAACV,aAAa;MACrC;IACJ,CAAC,MACI;MACD,IAAI9G,MAAM,IAAIwH,SAAS,CAACV,aAAa,CAAC9G,MAAM,EAAE;QAC1C+H,UAAU,GAAG,KAAK;QAClBE,OAAO,GAAGT,SAAS,CAACV,aAAa;MACrC,CAAC,MACI;QACDiB,UAAU,GAAG,IAAI;QACjBE,OAAO,GAAGT,SAAS,CAACX,UAAU;MAClC;IACJ;IACA,IAAI;MAAE3B,GAAG;MAAEC;IAAK,CAAC,GAAGqC,SAAS;IAC7B,IAAI,CAACO,UAAU,IAAI/H,MAAM,GAAGmG,SAAS,CAACnG,MAAM,EAAE;MAC1CkF,GAAG,GAAG4C,MAAM,GAAG9H,MAAM;IACzB;IACA,MAAMkI,aAAa,GAAG,IAAI,CAACpL,OAAO,CAACuB,UAAU,CAAC,CAAC;IAC/C,IAAI6J,aAAa,EAAE;MACf;MACA,MAAMC,iBAAiB,GAAGD,aAAa,CAAC9B,qBAAqB,CAAC,CAAC;MAC/DlB,GAAG,IAAIiD,iBAAiB,CAACjD,GAAG;MAC5BC,IAAI,IAAIgD,iBAAiB,CAAChD,IAAI;IAClC;IACA,IAAI,CAACF,aAAa,CAAC;MAAEE,IAAI;MAAED;IAAI,CAAC,CAAC;IACjC,IAAI,CAACd,UAAU,CAACC,YAAY,CAAC,CAAC0D,UAAU,EAAEP,SAAS,KAAKZ,aAAa,EAAEmB,UAAU,EAAEP,SAAS,KAAKZ,aAAa,CAAC;IAC/G,IAAI,CAACxC,UAAU,CAAC6C,OAAO,GAAGO,SAAS,CAACP,OAAO;IAC3C,IAAI,CAAC7C,UAAU,CAAC6D,OAAO,GAAGA,OAAO;IACjC,IAAI,CAAC7D,UAAU,CAACzD,MAAM,CAACX,MAAM,EAAEkH,IAAI,CAACC,GAAG,CAACc,OAAO,CAAChI,KAAK,EAAEW,IAAI,CAACX,KAAK,CAAC,CAAC;IACnE,IAAI,CAAC+D,MAAM,CAACrD,MAAM,CAAC,IAAI,CAACyD,UAAU,CAACxD,IAAI,CAACX,KAAK,EAAE,IAAI,CAACmE,UAAU,CAACxD,IAAI,CAACZ,MAAM,CAAC;EAC/E;EACAiF,aAAaA,CAACmD,OAAO,EAAE;IACnB,IAAI,CAACzD,QAAQ,GAAGyD,OAAO;IACvB,IAAI,CAACtL,OAAO,CAACuL,mBAAmB,CAAC,IAAI,CAAC;EAC1C;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}