{"ast": null, "code": "import _asyncToGenerator from \"C:/console/aava-ui-web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as dom from '../../../../base/browser/dom.js';\nimport { createTrustedTypesPolicy } from '../../../../base/browser/trustedTypes.js';\nimport { equals } from '../../../../base/common/arrays.js';\nimport { Disposable, DisposableStore } from '../../../../base/common/lifecycle.js';\nimport { ThemeIcon } from '../../../../base/common/themables.js';\nimport './stickyScroll.css';\nimport { getColumnOfNodeOffset } from '../../../browser/viewParts/lines/viewLine.js';\nimport { EmbeddedCodeEditorWidget } from '../../../browser/widget/codeEditor/embeddedCodeEditorWidget.js';\nimport { Position } from '../../../common/core/position.js';\nimport { StringBuilder } from '../../../common/core/stringBuilder.js';\nimport { LineDecoration } from '../../../common/viewLayout/lineDecorations.js';\nimport { RenderLineInput, renderViewLine } from '../../../common/viewLayout/viewLineRenderer.js';\nimport { foldingCollapsedIcon, foldingExpandedIcon } from '../../folding/browser/foldingDecorations.js';\nexport class StickyScrollWidgetState {\n  constructor(startLineNumbers, endLineNumbers, lastLineRelativePosition, showEndForLine = null) {\n    this.startLineNumbers = startLineNumbers;\n    this.endLineNumbers = endLineNumbers;\n    this.lastLineRelativePosition = lastLineRelativePosition;\n    this.showEndForLine = showEndForLine;\n  }\n  equals(other) {\n    return !!other && this.lastLineRelativePosition === other.lastLineRelativePosition && this.showEndForLine === other.showEndForLine && equals(this.startLineNumbers, other.startLineNumbers) && equals(this.endLineNumbers, other.endLineNumbers);\n  }\n  static get Empty() {\n    return new StickyScrollWidgetState([], [], 0);\n  }\n}\nconst _ttPolicy = createTrustedTypesPolicy('stickyScrollViewLayer', {\n  createHTML: value => value\n});\nconst STICKY_INDEX_ATTR = 'data-sticky-line-index';\nconst STICKY_IS_LINE_ATTR = 'data-sticky-is-line';\nconst STICKY_IS_LINE_NUMBER_ATTR = 'data-sticky-is-line-number';\nconst STICKY_IS_FOLDING_ICON_ATTR = 'data-sticky-is-folding-icon';\nexport class StickyScrollWidget extends Disposable {\n  constructor(_editor) {\n    super();\n    this._editor = _editor;\n    this._foldingIconStore = new DisposableStore();\n    this._rootDomNode = document.createElement('div');\n    this._lineNumbersDomNode = document.createElement('div');\n    this._linesDomNodeScrollable = document.createElement('div');\n    this._linesDomNode = document.createElement('div');\n    this._lineHeight = this._editor.getOption(67 /* EditorOption.lineHeight */);\n    this._renderedStickyLines = [];\n    this._lineNumbers = [];\n    this._lastLineRelativePosition = 0;\n    this._minContentWidthInPx = 0;\n    this._isOnGlyphMargin = false;\n    this._lineNumbersDomNode.className = 'sticky-widget-line-numbers';\n    this._lineNumbersDomNode.setAttribute('role', 'none');\n    this._linesDomNode.className = 'sticky-widget-lines';\n    this._linesDomNode.setAttribute('role', 'list');\n    this._linesDomNodeScrollable.className = 'sticky-widget-lines-scrollable';\n    this._linesDomNodeScrollable.appendChild(this._linesDomNode);\n    this._rootDomNode.className = 'sticky-widget';\n    this._rootDomNode.classList.toggle('peek', _editor instanceof EmbeddedCodeEditorWidget);\n    this._rootDomNode.appendChild(this._lineNumbersDomNode);\n    this._rootDomNode.appendChild(this._linesDomNodeScrollable);\n    const updateScrollLeftPosition = () => {\n      this._linesDomNode.style.left = this._editor.getOption(116 /* EditorOption.stickyScroll */).scrollWithEditor ? `-${this._editor.getScrollLeft()}px` : '0px';\n    };\n    this._register(this._editor.onDidChangeConfiguration(e => {\n      if (e.hasChanged(116 /* EditorOption.stickyScroll */)) {\n        updateScrollLeftPosition();\n      }\n      if (e.hasChanged(67 /* EditorOption.lineHeight */)) {\n        this._lineHeight = this._editor.getOption(67 /* EditorOption.lineHeight */);\n      }\n    }));\n    this._register(this._editor.onDidScrollChange(e => {\n      if (e.scrollLeftChanged) {\n        updateScrollLeftPosition();\n      }\n      if (e.scrollWidthChanged) {\n        this._updateWidgetWidth();\n      }\n    }));\n    this._register(this._editor.onDidChangeModel(() => {\n      updateScrollLeftPosition();\n      this._updateWidgetWidth();\n    }));\n    this._register(this._foldingIconStore);\n    updateScrollLeftPosition();\n    this._register(this._editor.onDidLayoutChange(e => {\n      this._updateWidgetWidth();\n    }));\n    this._updateWidgetWidth();\n  }\n  get lineNumbers() {\n    return this._lineNumbers;\n  }\n  get lineNumberCount() {\n    return this._lineNumbers.length;\n  }\n  getRenderedStickyLine(lineNumber) {\n    return this._renderedStickyLines.find(stickyLine => stickyLine.lineNumber === lineNumber);\n  }\n  getCurrentLines() {\n    return this._lineNumbers;\n  }\n  setState(_state, foldingModel, _rebuildFromLine) {\n    if (_rebuildFromLine === undefined && (!this._previousState && !_state || this._previousState && this._previousState.equals(_state))) {\n      return;\n    }\n    const isWidgetHeightZero = this._isWidgetHeightZero(_state);\n    const state = isWidgetHeightZero ? undefined : _state;\n    const rebuildFromLine = isWidgetHeightZero ? 0 : this._findLineToRebuildWidgetFrom(_state, _rebuildFromLine);\n    this._renderRootNode(state, foldingModel, rebuildFromLine);\n    this._previousState = _state;\n  }\n  _isWidgetHeightZero(state) {\n    if (!state) {\n      return true;\n    }\n    const futureWidgetHeight = state.startLineNumbers.length * this._lineHeight + state.lastLineRelativePosition;\n    if (futureWidgetHeight > 0) {\n      this._lastLineRelativePosition = state.lastLineRelativePosition;\n      const lineNumbers = [...state.startLineNumbers];\n      if (state.showEndForLine !== null) {\n        lineNumbers[state.showEndForLine] = state.endLineNumbers[state.showEndForLine];\n      }\n      this._lineNumbers = lineNumbers;\n    } else {\n      this._lastLineRelativePosition = 0;\n      this._lineNumbers = [];\n    }\n    return futureWidgetHeight === 0;\n  }\n  _findLineToRebuildWidgetFrom(state, _rebuildFromLine) {\n    if (!state || !this._previousState) {\n      return 0;\n    }\n    if (_rebuildFromLine !== undefined) {\n      return _rebuildFromLine;\n    }\n    const previousState = this._previousState;\n    const indexOfLinesAlreadyRendered = state.startLineNumbers.findIndex(startLineNumber => !previousState.startLineNumbers.includes(startLineNumber));\n    return indexOfLinesAlreadyRendered === -1 ? 0 : indexOfLinesAlreadyRendered;\n  }\n  _updateWidgetWidth() {\n    const layoutInfo = this._editor.getLayoutInfo();\n    const lineNumbersWidth = layoutInfo.contentLeft;\n    this._lineNumbersDomNode.style.width = `${lineNumbersWidth}px`;\n    this._linesDomNodeScrollable.style.setProperty('--vscode-editorStickyScroll-scrollableWidth', `${this._editor.getScrollWidth() - layoutInfo.verticalScrollbarWidth}px`);\n    this._rootDomNode.style.width = `${layoutInfo.width - layoutInfo.verticalScrollbarWidth}px`;\n  }\n  _clearStickyLinesFromLine(clearFromLine) {\n    this._foldingIconStore.clear();\n    // Removing only the lines that need to be rerendered\n    for (let i = clearFromLine; i < this._renderedStickyLines.length; i++) {\n      const stickyLine = this._renderedStickyLines[i];\n      stickyLine.lineNumberDomNode.remove();\n      stickyLine.lineDomNode.remove();\n    }\n    // Keep the lines that need to be updated\n    this._renderedStickyLines = this._renderedStickyLines.slice(0, clearFromLine);\n    this._rootDomNode.style.display = 'none';\n  }\n  _useFoldingOpacityTransition(requireTransitions) {\n    this._lineNumbersDomNode.style.setProperty('--vscode-editorStickyScroll-foldingOpacityTransition', `opacity ${requireTransitions ? 0.5 : 0}s`);\n  }\n  _setFoldingIconsVisibility(allVisible) {\n    for (const line of this._renderedStickyLines) {\n      const foldingIcon = line.foldingIcon;\n      if (!foldingIcon) {\n        continue;\n      }\n      foldingIcon.setVisible(allVisible ? true : foldingIcon.isCollapsed);\n    }\n  }\n  _renderRootNode(state, foldingModel, rebuildFromLine) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this._clearStickyLinesFromLine(rebuildFromLine);\n      if (!state) {\n        return;\n      }\n      // For existing sticky lines update the top and z-index\n      for (const stickyLine of _this._renderedStickyLines) {\n        _this._updateTopAndZIndexOfStickyLine(stickyLine);\n      }\n      // For new sticky lines\n      const layoutInfo = _this._editor.getLayoutInfo();\n      const linesToRender = _this._lineNumbers.slice(rebuildFromLine);\n      for (const [index, line] of linesToRender.entries()) {\n        const stickyLine = _this._renderChildNode(index + rebuildFromLine, line, foldingModel, layoutInfo);\n        if (!stickyLine) {\n          continue;\n        }\n        _this._linesDomNode.appendChild(stickyLine.lineDomNode);\n        _this._lineNumbersDomNode.appendChild(stickyLine.lineNumberDomNode);\n        _this._renderedStickyLines.push(stickyLine);\n      }\n      if (foldingModel) {\n        _this._setFoldingHoverListeners();\n        _this._useFoldingOpacityTransition(!_this._isOnGlyphMargin);\n      }\n      const widgetHeight = _this._lineNumbers.length * _this._lineHeight + _this._lastLineRelativePosition;\n      _this._rootDomNode.style.display = 'block';\n      _this._lineNumbersDomNode.style.height = `${widgetHeight}px`;\n      _this._linesDomNodeScrollable.style.height = `${widgetHeight}px`;\n      _this._rootDomNode.style.height = `${widgetHeight}px`;\n      _this._rootDomNode.style.marginLeft = '0px';\n      _this._minContentWidthInPx = Math.max(..._this._renderedStickyLines.map(l => l.scrollWidth)) + layoutInfo.verticalScrollbarWidth;\n      _this._editor.layoutOverlayWidget(_this);\n    })();\n  }\n  _setFoldingHoverListeners() {\n    const showFoldingControls = this._editor.getOption(111 /* EditorOption.showFoldingControls */);\n    if (showFoldingControls !== 'mouseover') {\n      return;\n    }\n    this._foldingIconStore.add(dom.addDisposableListener(this._lineNumbersDomNode, dom.EventType.MOUSE_ENTER, () => {\n      this._isOnGlyphMargin = true;\n      this._setFoldingIconsVisibility(true);\n    }));\n    this._foldingIconStore.add(dom.addDisposableListener(this._lineNumbersDomNode, dom.EventType.MOUSE_LEAVE, () => {\n      this._isOnGlyphMargin = false;\n      this._useFoldingOpacityTransition(true);\n      this._setFoldingIconsVisibility(false);\n    }));\n  }\n  _renderChildNode(index, line, foldingModel, layoutInfo) {\n    const viewModel = this._editor._getViewModel();\n    if (!viewModel) {\n      return;\n    }\n    const viewLineNumber = viewModel.coordinatesConverter.convertModelPositionToViewPosition(new Position(line, 1)).lineNumber;\n    const lineRenderingData = viewModel.getViewLineRenderingData(viewLineNumber);\n    const lineNumberOption = this._editor.getOption(68 /* EditorOption.lineNumbers */);\n    let actualInlineDecorations;\n    try {\n      actualInlineDecorations = LineDecoration.filter(lineRenderingData.inlineDecorations, viewLineNumber, lineRenderingData.minColumn, lineRenderingData.maxColumn);\n    } catch (err) {\n      actualInlineDecorations = [];\n    }\n    const renderLineInput = new RenderLineInput(true, true, lineRenderingData.content, lineRenderingData.continuesWithWrappedLine, lineRenderingData.isBasicASCII, lineRenderingData.containsRTL, 0, lineRenderingData.tokens, actualInlineDecorations, lineRenderingData.tabSize, lineRenderingData.startVisibleColumn, 1, 1, 1, 500, 'none', true, true, null);\n    const sb = new StringBuilder(2000);\n    const renderOutput = renderViewLine(renderLineInput, sb);\n    let newLine;\n    if (_ttPolicy) {\n      newLine = _ttPolicy.createHTML(sb.build());\n    } else {\n      newLine = sb.build();\n    }\n    const lineHTMLNode = document.createElement('span');\n    lineHTMLNode.setAttribute(STICKY_INDEX_ATTR, String(index));\n    lineHTMLNode.setAttribute(STICKY_IS_LINE_ATTR, '');\n    lineHTMLNode.setAttribute('role', 'listitem');\n    lineHTMLNode.tabIndex = 0;\n    lineHTMLNode.className = 'sticky-line-content';\n    lineHTMLNode.classList.add(`stickyLine${line}`);\n    lineHTMLNode.style.lineHeight = `${this._lineHeight}px`;\n    lineHTMLNode.innerHTML = newLine;\n    const lineNumberHTMLNode = document.createElement('span');\n    lineNumberHTMLNode.setAttribute(STICKY_INDEX_ATTR, String(index));\n    lineNumberHTMLNode.setAttribute(STICKY_IS_LINE_NUMBER_ATTR, '');\n    lineNumberHTMLNode.className = 'sticky-line-number';\n    lineNumberHTMLNode.style.lineHeight = `${this._lineHeight}px`;\n    const lineNumbersWidth = layoutInfo.contentLeft;\n    lineNumberHTMLNode.style.width = `${lineNumbersWidth}px`;\n    const innerLineNumberHTML = document.createElement('span');\n    if (lineNumberOption.renderType === 1 /* RenderLineNumbersType.On */ || lineNumberOption.renderType === 3 /* RenderLineNumbersType.Interval */ && line % 10 === 0) {\n      innerLineNumberHTML.innerText = line.toString();\n    } else if (lineNumberOption.renderType === 2 /* RenderLineNumbersType.Relative */) {\n      innerLineNumberHTML.innerText = Math.abs(line - this._editor.getPosition().lineNumber).toString();\n    }\n    innerLineNumberHTML.className = 'sticky-line-number-inner';\n    innerLineNumberHTML.style.lineHeight = `${this._lineHeight}px`;\n    innerLineNumberHTML.style.width = `${layoutInfo.lineNumbersWidth}px`;\n    innerLineNumberHTML.style.paddingLeft = `${layoutInfo.lineNumbersLeft}px`;\n    lineNumberHTMLNode.appendChild(innerLineNumberHTML);\n    const foldingIcon = this._renderFoldingIconForLine(foldingModel, line);\n    if (foldingIcon) {\n      lineNumberHTMLNode.appendChild(foldingIcon.domNode);\n    }\n    this._editor.applyFontInfo(lineHTMLNode);\n    this._editor.applyFontInfo(innerLineNumberHTML);\n    lineNumberHTMLNode.style.lineHeight = `${this._lineHeight}px`;\n    lineHTMLNode.style.lineHeight = `${this._lineHeight}px`;\n    lineNumberHTMLNode.style.height = `${this._lineHeight}px`;\n    lineHTMLNode.style.height = `${this._lineHeight}px`;\n    const renderedLine = new RenderedStickyLine(index, line, lineHTMLNode, lineNumberHTMLNode, foldingIcon, renderOutput.characterMapping, lineHTMLNode.scrollWidth);\n    return this._updateTopAndZIndexOfStickyLine(renderedLine);\n  }\n  _updateTopAndZIndexOfStickyLine(stickyLine) {\n    const index = stickyLine.index;\n    const lineHTMLNode = stickyLine.lineDomNode;\n    const lineNumberHTMLNode = stickyLine.lineNumberDomNode;\n    const isLastLine = index === this._lineNumbers.length - 1;\n    const lastLineZIndex = '0';\n    const intermediateLineZIndex = '1';\n    lineHTMLNode.style.zIndex = isLastLine ? lastLineZIndex : intermediateLineZIndex;\n    lineNumberHTMLNode.style.zIndex = isLastLine ? lastLineZIndex : intermediateLineZIndex;\n    const lastLineTop = `${index * this._lineHeight + this._lastLineRelativePosition + (stickyLine.foldingIcon?.isCollapsed ? 1 : 0)}px`;\n    const intermediateLineTop = `${index * this._lineHeight}px`;\n    lineHTMLNode.style.top = isLastLine ? lastLineTop : intermediateLineTop;\n    lineNumberHTMLNode.style.top = isLastLine ? lastLineTop : intermediateLineTop;\n    return stickyLine;\n  }\n  _renderFoldingIconForLine(foldingModel, line) {\n    const showFoldingControls = this._editor.getOption(111 /* EditorOption.showFoldingControls */);\n    if (!foldingModel || showFoldingControls === 'never') {\n      return;\n    }\n    const foldingRegions = foldingModel.regions;\n    const indexOfFoldingRegion = foldingRegions.findRange(line);\n    const startLineNumber = foldingRegions.getStartLineNumber(indexOfFoldingRegion);\n    const isFoldingScope = line === startLineNumber;\n    if (!isFoldingScope) {\n      return;\n    }\n    const isCollapsed = foldingRegions.isCollapsed(indexOfFoldingRegion);\n    const foldingIcon = new StickyFoldingIcon(isCollapsed, startLineNumber, foldingRegions.getEndLineNumber(indexOfFoldingRegion), this._lineHeight);\n    foldingIcon.setVisible(this._isOnGlyphMargin ? true : isCollapsed || showFoldingControls === 'always');\n    foldingIcon.domNode.setAttribute(STICKY_IS_FOLDING_ICON_ATTR, '');\n    return foldingIcon;\n  }\n  getId() {\n    return 'editor.contrib.stickyScrollWidget';\n  }\n  getDomNode() {\n    return this._rootDomNode;\n  }\n  getPosition() {\n    return {\n      preference: 2 /* OverlayWidgetPositionPreference.TOP_CENTER */,\n      stackOridinal: 10\n    };\n  }\n  getMinContentWidthInPx() {\n    return this._minContentWidthInPx;\n  }\n  focusLineWithIndex(index) {\n    if (0 <= index && index < this._renderedStickyLines.length) {\n      this._renderedStickyLines[index].lineDomNode.focus();\n    }\n  }\n  /**\n   * Given a leaf dom node, tries to find the editor position.\n   */\n  getEditorPositionFromNode(spanDomNode) {\n    if (!spanDomNode || spanDomNode.children.length > 0) {\n      // This is not a leaf node\n      return null;\n    }\n    const renderedStickyLine = this._getRenderedStickyLineFromChildDomNode(spanDomNode);\n    if (!renderedStickyLine) {\n      return null;\n    }\n    const column = getColumnOfNodeOffset(renderedStickyLine.characterMapping, spanDomNode, 0);\n    return new Position(renderedStickyLine.lineNumber, column);\n  }\n  getLineNumberFromChildDomNode(domNode) {\n    return this._getRenderedStickyLineFromChildDomNode(domNode)?.lineNumber ?? null;\n  }\n  _getRenderedStickyLineFromChildDomNode(domNode) {\n    const index = this.getLineIndexFromChildDomNode(domNode);\n    if (index === null || index < 0 || index >= this._renderedStickyLines.length) {\n      return null;\n    }\n    return this._renderedStickyLines[index];\n  }\n  /**\n   * Given a child dom node, tries to find the line number attribute that was stored in the node.\n   * @returns the attribute value or null if none is found.\n   */\n  getLineIndexFromChildDomNode(domNode) {\n    const lineIndex = this._getAttributeValue(domNode, STICKY_INDEX_ATTR);\n    return lineIndex ? parseInt(lineIndex, 10) : null;\n  }\n  /**\n   * Given a child dom node, tries to find if it is (contained in) a sticky line.\n   * @returns a boolean.\n   */\n  isInStickyLine(domNode) {\n    const isInLine = this._getAttributeValue(domNode, STICKY_IS_LINE_ATTR);\n    return isInLine !== undefined;\n  }\n  /**\n   * Given a child dom node, tries to find if this dom node is (contained in) a sticky folding icon.\n   * @returns a boolean.\n   */\n  isInFoldingIconDomNode(domNode) {\n    const isInFoldingIcon = this._getAttributeValue(domNode, STICKY_IS_FOLDING_ICON_ATTR);\n    return isInFoldingIcon !== undefined;\n  }\n  /**\n   * Given the dom node, finds if it or its parent sequence contains the given attribute.\n   * @returns the attribute value or undefined.\n   */\n  _getAttributeValue(domNode, attribute) {\n    while (domNode && domNode !== this._rootDomNode) {\n      const line = domNode.getAttribute(attribute);\n      if (line !== null) {\n        return line;\n      }\n      domNode = domNode.parentElement;\n    }\n    return;\n  }\n}\nclass RenderedStickyLine {\n  constructor(index, lineNumber, lineDomNode, lineNumberDomNode, foldingIcon, characterMapping, scrollWidth) {\n    this.index = index;\n    this.lineNumber = lineNumber;\n    this.lineDomNode = lineDomNode;\n    this.lineNumberDomNode = lineNumberDomNode;\n    this.foldingIcon = foldingIcon;\n    this.characterMapping = characterMapping;\n    this.scrollWidth = scrollWidth;\n  }\n}\nclass StickyFoldingIcon {\n  constructor(isCollapsed, foldingStartLine, foldingEndLine, dimension) {\n    this.isCollapsed = isCollapsed;\n    this.foldingStartLine = foldingStartLine;\n    this.foldingEndLine = foldingEndLine;\n    this.dimension = dimension;\n    this.domNode = document.createElement('div');\n    this.domNode.style.width = `${dimension}px`;\n    this.domNode.style.height = `${dimension}px`;\n    this.domNode.className = ThemeIcon.asClassName(isCollapsed ? foldingCollapsedIcon : foldingExpandedIcon);\n  }\n  setVisible(visible) {\n    this.domNode.style.cursor = visible ? 'pointer' : 'default';\n    this.domNode.style.opacity = visible ? '1' : '0';\n  }\n}", "map": {"version": 3, "names": ["dom", "createTrustedTypesPolicy", "equals", "Disposable", "DisposableStore", "ThemeIcon", "getColumnOfNodeOffset", "EmbeddedCodeEditorWidget", "Position", "StringBuilder", "LineDecoration", "RenderLineInput", "renderViewLine", "foldingCollapsedIcon", "foldingExpandedIcon", "StickyScrollWidgetState", "constructor", "startLineNumbers", "endLineNumbers", "lastLineRelativePosition", "showEndForLine", "other", "Empty", "_ttPolicy", "createHTML", "value", "STICKY_INDEX_ATTR", "STICKY_IS_LINE_ATTR", "STICKY_IS_LINE_NUMBER_ATTR", "STICKY_IS_FOLDING_ICON_ATTR", "StickyScrollWidget", "_editor", "_foldingIconStore", "_rootDomNode", "document", "createElement", "_lineNumbersDomNode", "_linesDomNodeScrollable", "_linesDomNode", "_lineHeight", "getOption", "_renderedStickyLines", "_lineNumbers", "_lastLineRelativePosition", "_minContentWidthInPx", "_isOnGlyphMargin", "className", "setAttribute", "append<PERSON><PERSON><PERSON>", "classList", "toggle", "updateScrollLeftPosition", "style", "left", "scrollWithEditor", "getScrollLeft", "_register", "onDidChangeConfiguration", "e", "has<PERSON><PERSON>ed", "onDidScrollChange", "scrollLeftChanged", "scrollWidthChanged", "_updateWidgetWidth", "onDidChangeModel", "onDidLayoutChange", "lineNumbers", "lineNumberCount", "length", "getRenderedStickyLine", "lineNumber", "find", "stickyLine", "getCurrentLines", "setState", "_state", "foldingModel", "_rebuildFromLine", "undefined", "_previousState", "isWidgetHeightZero", "_isWidgetHeightZero", "state", "rebuildFromLine", "_findLineToRebuildWidgetFrom", "_renderRootNode", "futureWidgetHeight", "previousState", "indexOfLinesAlreadyRendered", "findIndex", "startLineNumber", "includes", "layoutInfo", "getLayoutInfo", "lineNumbersWidth", "contentLeft", "width", "setProperty", "getScrollWidth", "verticalScrollbarWidth", "_clearStickyLinesFromLine", "clearFromLine", "clear", "i", "lineNumberDomNode", "remove", "lineDomNode", "slice", "display", "_useFoldingOpacityTransition", "requireTransitions", "_setFoldingIconsVisibility", "allVisible", "line", "foldingIcon", "setVisible", "isCollapsed", "_this", "_asyncToGenerator", "_updateTopAndZIndexOfStickyLine", "linesToRender", "index", "entries", "_renderChildNode", "push", "_setFoldingHoverListeners", "widgetHeight", "height", "marginLeft", "Math", "max", "map", "l", "scrollWidth", "layoutOverlayWidget", "showFoldingControls", "add", "addDisposableListener", "EventType", "MOUSE_ENTER", "MOUSE_LEAVE", "viewModel", "_getViewModel", "viewLineNumber", "coordinatesConverter", "convertModelPositionToViewPosition", "lineRenderingData", "getViewLineRenderingData", "lineNumberOption", "actualInlineDecorations", "filter", "inlineDecorations", "minColumn", "maxColumn", "err", "renderLineInput", "content", "continuesWithWrappedLine", "isBasicASCII", "containsRTL", "tokens", "tabSize", "startVisibleColumn", "sb", "renderOutput", "newLine", "build", "lineHTMLNode", "String", "tabIndex", "lineHeight", "innerHTML", "lineNumberHTMLNode", "innerLineNumberHTML", "renderType", "innerText", "toString", "abs", "getPosition", "paddingLeft", "lineNumbersLeft", "_renderFoldingIconForLine", "domNode", "applyFontInfo", "renderedLine", "RenderedStickyLine", "characterMapping", "isLastLine", "lastLineZIndex", "intermediateLineZIndex", "zIndex", "lastLineTop", "intermediateLineTop", "top", "foldingRegions", "regions", "indexOfFoldingRegion", "find<PERSON><PERSON><PERSON>", "getStartLineNumber", "isFoldingScope", "StickyFoldingIcon", "getEndLineNumber", "getId", "getDomNode", "preference", "stackOridinal", "getMinContentWidthInPx", "focusLineWithIndex", "focus", "getEditorPositionFromNode", "spanDomNode", "children", "renderedStickyLine", "_getRenderedStickyLineFromChildDomNode", "column", "getLineNumberFromChildDomNode", "getLineIndexFromChildDomNode", "lineIndex", "_getAttributeValue", "parseInt", "isInStickyLine", "isInLine", "isInFoldingIconDomNode", "isInFoldingIcon", "attribute", "getAttribute", "parentElement", "foldingStartLine", "foldingEndLine", "dimension", "asClassName", "visible", "cursor", "opacity"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/contrib/stickyScroll/browser/stickyScrollWidget.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as dom from '../../../../base/browser/dom.js';\nimport { createTrustedTypesPolicy } from '../../../../base/browser/trustedTypes.js';\nimport { equals } from '../../../../base/common/arrays.js';\nimport { Disposable, DisposableStore } from '../../../../base/common/lifecycle.js';\nimport { ThemeIcon } from '../../../../base/common/themables.js';\nimport './stickyScroll.css';\nimport { getColumnOfNodeOffset } from '../../../browser/viewParts/lines/viewLine.js';\nimport { EmbeddedCodeEditorWidget } from '../../../browser/widget/codeEditor/embeddedCodeEditorWidget.js';\nimport { Position } from '../../../common/core/position.js';\nimport { StringBuilder } from '../../../common/core/stringBuilder.js';\nimport { LineDecoration } from '../../../common/viewLayout/lineDecorations.js';\nimport { RenderLineInput, renderViewLine } from '../../../common/viewLayout/viewLineRenderer.js';\nimport { foldingCollapsedIcon, foldingExpandedIcon } from '../../folding/browser/foldingDecorations.js';\nexport class StickyScrollWidgetState {\n    constructor(startLineNumbers, endLineNumbers, lastLineRelativePosition, showEndForLine = null) {\n        this.startLineNumbers = startLineNumbers;\n        this.endLineNumbers = endLineNumbers;\n        this.lastLineRelativePosition = lastLineRelativePosition;\n        this.showEndForLine = showEndForLine;\n    }\n    equals(other) {\n        return !!other\n            && this.lastLineRelativePosition === other.lastLineRelativePosition\n            && this.showEndForLine === other.showEndForLine\n            && equals(this.startLineNumbers, other.startLineNumbers)\n            && equals(this.endLineNumbers, other.endLineNumbers);\n    }\n    static get Empty() {\n        return new StickyScrollWidgetState([], [], 0);\n    }\n}\nconst _ttPolicy = createTrustedTypesPolicy('stickyScrollViewLayer', { createHTML: value => value });\nconst STICKY_INDEX_ATTR = 'data-sticky-line-index';\nconst STICKY_IS_LINE_ATTR = 'data-sticky-is-line';\nconst STICKY_IS_LINE_NUMBER_ATTR = 'data-sticky-is-line-number';\nconst STICKY_IS_FOLDING_ICON_ATTR = 'data-sticky-is-folding-icon';\nexport class StickyScrollWidget extends Disposable {\n    constructor(_editor) {\n        super();\n        this._editor = _editor;\n        this._foldingIconStore = new DisposableStore();\n        this._rootDomNode = document.createElement('div');\n        this._lineNumbersDomNode = document.createElement('div');\n        this._linesDomNodeScrollable = document.createElement('div');\n        this._linesDomNode = document.createElement('div');\n        this._lineHeight = this._editor.getOption(67 /* EditorOption.lineHeight */);\n        this._renderedStickyLines = [];\n        this._lineNumbers = [];\n        this._lastLineRelativePosition = 0;\n        this._minContentWidthInPx = 0;\n        this._isOnGlyphMargin = false;\n        this._lineNumbersDomNode.className = 'sticky-widget-line-numbers';\n        this._lineNumbersDomNode.setAttribute('role', 'none');\n        this._linesDomNode.className = 'sticky-widget-lines';\n        this._linesDomNode.setAttribute('role', 'list');\n        this._linesDomNodeScrollable.className = 'sticky-widget-lines-scrollable';\n        this._linesDomNodeScrollable.appendChild(this._linesDomNode);\n        this._rootDomNode.className = 'sticky-widget';\n        this._rootDomNode.classList.toggle('peek', _editor instanceof EmbeddedCodeEditorWidget);\n        this._rootDomNode.appendChild(this._lineNumbersDomNode);\n        this._rootDomNode.appendChild(this._linesDomNodeScrollable);\n        const updateScrollLeftPosition = () => {\n            this._linesDomNode.style.left = this._editor.getOption(116 /* EditorOption.stickyScroll */).scrollWithEditor ? `-${this._editor.getScrollLeft()}px` : '0px';\n        };\n        this._register(this._editor.onDidChangeConfiguration((e) => {\n            if (e.hasChanged(116 /* EditorOption.stickyScroll */)) {\n                updateScrollLeftPosition();\n            }\n            if (e.hasChanged(67 /* EditorOption.lineHeight */)) {\n                this._lineHeight = this._editor.getOption(67 /* EditorOption.lineHeight */);\n            }\n        }));\n        this._register(this._editor.onDidScrollChange((e) => {\n            if (e.scrollLeftChanged) {\n                updateScrollLeftPosition();\n            }\n            if (e.scrollWidthChanged) {\n                this._updateWidgetWidth();\n            }\n        }));\n        this._register(this._editor.onDidChangeModel(() => {\n            updateScrollLeftPosition();\n            this._updateWidgetWidth();\n        }));\n        this._register(this._foldingIconStore);\n        updateScrollLeftPosition();\n        this._register(this._editor.onDidLayoutChange((e) => {\n            this._updateWidgetWidth();\n        }));\n        this._updateWidgetWidth();\n    }\n    get lineNumbers() {\n        return this._lineNumbers;\n    }\n    get lineNumberCount() {\n        return this._lineNumbers.length;\n    }\n    getRenderedStickyLine(lineNumber) {\n        return this._renderedStickyLines.find(stickyLine => stickyLine.lineNumber === lineNumber);\n    }\n    getCurrentLines() {\n        return this._lineNumbers;\n    }\n    setState(_state, foldingModel, _rebuildFromLine) {\n        if (_rebuildFromLine === undefined &&\n            ((!this._previousState && !_state) || (this._previousState && this._previousState.equals(_state)))) {\n            return;\n        }\n        const isWidgetHeightZero = this._isWidgetHeightZero(_state);\n        const state = isWidgetHeightZero ? undefined : _state;\n        const rebuildFromLine = isWidgetHeightZero ? 0 : this._findLineToRebuildWidgetFrom(_state, _rebuildFromLine);\n        this._renderRootNode(state, foldingModel, rebuildFromLine);\n        this._previousState = _state;\n    }\n    _isWidgetHeightZero(state) {\n        if (!state) {\n            return true;\n        }\n        const futureWidgetHeight = state.startLineNumbers.length * this._lineHeight + state.lastLineRelativePosition;\n        if (futureWidgetHeight > 0) {\n            this._lastLineRelativePosition = state.lastLineRelativePosition;\n            const lineNumbers = [...state.startLineNumbers];\n            if (state.showEndForLine !== null) {\n                lineNumbers[state.showEndForLine] = state.endLineNumbers[state.showEndForLine];\n            }\n            this._lineNumbers = lineNumbers;\n        }\n        else {\n            this._lastLineRelativePosition = 0;\n            this._lineNumbers = [];\n        }\n        return futureWidgetHeight === 0;\n    }\n    _findLineToRebuildWidgetFrom(state, _rebuildFromLine) {\n        if (!state || !this._previousState) {\n            return 0;\n        }\n        if (_rebuildFromLine !== undefined) {\n            return _rebuildFromLine;\n        }\n        const previousState = this._previousState;\n        const indexOfLinesAlreadyRendered = state.startLineNumbers.findIndex(startLineNumber => !previousState.startLineNumbers.includes(startLineNumber));\n        return (indexOfLinesAlreadyRendered === -1) ? 0 : indexOfLinesAlreadyRendered;\n    }\n    _updateWidgetWidth() {\n        const layoutInfo = this._editor.getLayoutInfo();\n        const lineNumbersWidth = layoutInfo.contentLeft;\n        this._lineNumbersDomNode.style.width = `${lineNumbersWidth}px`;\n        this._linesDomNodeScrollable.style.setProperty('--vscode-editorStickyScroll-scrollableWidth', `${this._editor.getScrollWidth() - layoutInfo.verticalScrollbarWidth}px`);\n        this._rootDomNode.style.width = `${layoutInfo.width - layoutInfo.verticalScrollbarWidth}px`;\n    }\n    _clearStickyLinesFromLine(clearFromLine) {\n        this._foldingIconStore.clear();\n        // Removing only the lines that need to be rerendered\n        for (let i = clearFromLine; i < this._renderedStickyLines.length; i++) {\n            const stickyLine = this._renderedStickyLines[i];\n            stickyLine.lineNumberDomNode.remove();\n            stickyLine.lineDomNode.remove();\n        }\n        // Keep the lines that need to be updated\n        this._renderedStickyLines = this._renderedStickyLines.slice(0, clearFromLine);\n        this._rootDomNode.style.display = 'none';\n    }\n    _useFoldingOpacityTransition(requireTransitions) {\n        this._lineNumbersDomNode.style.setProperty('--vscode-editorStickyScroll-foldingOpacityTransition', `opacity ${requireTransitions ? 0.5 : 0}s`);\n    }\n    _setFoldingIconsVisibility(allVisible) {\n        for (const line of this._renderedStickyLines) {\n            const foldingIcon = line.foldingIcon;\n            if (!foldingIcon) {\n                continue;\n            }\n            foldingIcon.setVisible(allVisible ? true : foldingIcon.isCollapsed);\n        }\n    }\n    async _renderRootNode(state, foldingModel, rebuildFromLine) {\n        this._clearStickyLinesFromLine(rebuildFromLine);\n        if (!state) {\n            return;\n        }\n        // For existing sticky lines update the top and z-index\n        for (const stickyLine of this._renderedStickyLines) {\n            this._updateTopAndZIndexOfStickyLine(stickyLine);\n        }\n        // For new sticky lines\n        const layoutInfo = this._editor.getLayoutInfo();\n        const linesToRender = this._lineNumbers.slice(rebuildFromLine);\n        for (const [index, line] of linesToRender.entries()) {\n            const stickyLine = this._renderChildNode(index + rebuildFromLine, line, foldingModel, layoutInfo);\n            if (!stickyLine) {\n                continue;\n            }\n            this._linesDomNode.appendChild(stickyLine.lineDomNode);\n            this._lineNumbersDomNode.appendChild(stickyLine.lineNumberDomNode);\n            this._renderedStickyLines.push(stickyLine);\n        }\n        if (foldingModel) {\n            this._setFoldingHoverListeners();\n            this._useFoldingOpacityTransition(!this._isOnGlyphMargin);\n        }\n        const widgetHeight = this._lineNumbers.length * this._lineHeight + this._lastLineRelativePosition;\n        this._rootDomNode.style.display = 'block';\n        this._lineNumbersDomNode.style.height = `${widgetHeight}px`;\n        this._linesDomNodeScrollable.style.height = `${widgetHeight}px`;\n        this._rootDomNode.style.height = `${widgetHeight}px`;\n        this._rootDomNode.style.marginLeft = '0px';\n        this._minContentWidthInPx = Math.max(...this._renderedStickyLines.map(l => l.scrollWidth)) + layoutInfo.verticalScrollbarWidth;\n        this._editor.layoutOverlayWidget(this);\n    }\n    _setFoldingHoverListeners() {\n        const showFoldingControls = this._editor.getOption(111 /* EditorOption.showFoldingControls */);\n        if (showFoldingControls !== 'mouseover') {\n            return;\n        }\n        this._foldingIconStore.add(dom.addDisposableListener(this._lineNumbersDomNode, dom.EventType.MOUSE_ENTER, () => {\n            this._isOnGlyphMargin = true;\n            this._setFoldingIconsVisibility(true);\n        }));\n        this._foldingIconStore.add(dom.addDisposableListener(this._lineNumbersDomNode, dom.EventType.MOUSE_LEAVE, () => {\n            this._isOnGlyphMargin = false;\n            this._useFoldingOpacityTransition(true);\n            this._setFoldingIconsVisibility(false);\n        }));\n    }\n    _renderChildNode(index, line, foldingModel, layoutInfo) {\n        const viewModel = this._editor._getViewModel();\n        if (!viewModel) {\n            return;\n        }\n        const viewLineNumber = viewModel.coordinatesConverter.convertModelPositionToViewPosition(new Position(line, 1)).lineNumber;\n        const lineRenderingData = viewModel.getViewLineRenderingData(viewLineNumber);\n        const lineNumberOption = this._editor.getOption(68 /* EditorOption.lineNumbers */);\n        let actualInlineDecorations;\n        try {\n            actualInlineDecorations = LineDecoration.filter(lineRenderingData.inlineDecorations, viewLineNumber, lineRenderingData.minColumn, lineRenderingData.maxColumn);\n        }\n        catch (err) {\n            actualInlineDecorations = [];\n        }\n        const renderLineInput = new RenderLineInput(true, true, lineRenderingData.content, lineRenderingData.continuesWithWrappedLine, lineRenderingData.isBasicASCII, lineRenderingData.containsRTL, 0, lineRenderingData.tokens, actualInlineDecorations, lineRenderingData.tabSize, lineRenderingData.startVisibleColumn, 1, 1, 1, 500, 'none', true, true, null);\n        const sb = new StringBuilder(2000);\n        const renderOutput = renderViewLine(renderLineInput, sb);\n        let newLine;\n        if (_ttPolicy) {\n            newLine = _ttPolicy.createHTML(sb.build());\n        }\n        else {\n            newLine = sb.build();\n        }\n        const lineHTMLNode = document.createElement('span');\n        lineHTMLNode.setAttribute(STICKY_INDEX_ATTR, String(index));\n        lineHTMLNode.setAttribute(STICKY_IS_LINE_ATTR, '');\n        lineHTMLNode.setAttribute('role', 'listitem');\n        lineHTMLNode.tabIndex = 0;\n        lineHTMLNode.className = 'sticky-line-content';\n        lineHTMLNode.classList.add(`stickyLine${line}`);\n        lineHTMLNode.style.lineHeight = `${this._lineHeight}px`;\n        lineHTMLNode.innerHTML = newLine;\n        const lineNumberHTMLNode = document.createElement('span');\n        lineNumberHTMLNode.setAttribute(STICKY_INDEX_ATTR, String(index));\n        lineNumberHTMLNode.setAttribute(STICKY_IS_LINE_NUMBER_ATTR, '');\n        lineNumberHTMLNode.className = 'sticky-line-number';\n        lineNumberHTMLNode.style.lineHeight = `${this._lineHeight}px`;\n        const lineNumbersWidth = layoutInfo.contentLeft;\n        lineNumberHTMLNode.style.width = `${lineNumbersWidth}px`;\n        const innerLineNumberHTML = document.createElement('span');\n        if (lineNumberOption.renderType === 1 /* RenderLineNumbersType.On */ || lineNumberOption.renderType === 3 /* RenderLineNumbersType.Interval */ && line % 10 === 0) {\n            innerLineNumberHTML.innerText = line.toString();\n        }\n        else if (lineNumberOption.renderType === 2 /* RenderLineNumbersType.Relative */) {\n            innerLineNumberHTML.innerText = Math.abs(line - this._editor.getPosition().lineNumber).toString();\n        }\n        innerLineNumberHTML.className = 'sticky-line-number-inner';\n        innerLineNumberHTML.style.lineHeight = `${this._lineHeight}px`;\n        innerLineNumberHTML.style.width = `${layoutInfo.lineNumbersWidth}px`;\n        innerLineNumberHTML.style.paddingLeft = `${layoutInfo.lineNumbersLeft}px`;\n        lineNumberHTMLNode.appendChild(innerLineNumberHTML);\n        const foldingIcon = this._renderFoldingIconForLine(foldingModel, line);\n        if (foldingIcon) {\n            lineNumberHTMLNode.appendChild(foldingIcon.domNode);\n        }\n        this._editor.applyFontInfo(lineHTMLNode);\n        this._editor.applyFontInfo(innerLineNumberHTML);\n        lineNumberHTMLNode.style.lineHeight = `${this._lineHeight}px`;\n        lineHTMLNode.style.lineHeight = `${this._lineHeight}px`;\n        lineNumberHTMLNode.style.height = `${this._lineHeight}px`;\n        lineHTMLNode.style.height = `${this._lineHeight}px`;\n        const renderedLine = new RenderedStickyLine(index, line, lineHTMLNode, lineNumberHTMLNode, foldingIcon, renderOutput.characterMapping, lineHTMLNode.scrollWidth);\n        return this._updateTopAndZIndexOfStickyLine(renderedLine);\n    }\n    _updateTopAndZIndexOfStickyLine(stickyLine) {\n        const index = stickyLine.index;\n        const lineHTMLNode = stickyLine.lineDomNode;\n        const lineNumberHTMLNode = stickyLine.lineNumberDomNode;\n        const isLastLine = index === this._lineNumbers.length - 1;\n        const lastLineZIndex = '0';\n        const intermediateLineZIndex = '1';\n        lineHTMLNode.style.zIndex = isLastLine ? lastLineZIndex : intermediateLineZIndex;\n        lineNumberHTMLNode.style.zIndex = isLastLine ? lastLineZIndex : intermediateLineZIndex;\n        const lastLineTop = `${index * this._lineHeight + this._lastLineRelativePosition + (stickyLine.foldingIcon?.isCollapsed ? 1 : 0)}px`;\n        const intermediateLineTop = `${index * this._lineHeight}px`;\n        lineHTMLNode.style.top = isLastLine ? lastLineTop : intermediateLineTop;\n        lineNumberHTMLNode.style.top = isLastLine ? lastLineTop : intermediateLineTop;\n        return stickyLine;\n    }\n    _renderFoldingIconForLine(foldingModel, line) {\n        const showFoldingControls = this._editor.getOption(111 /* EditorOption.showFoldingControls */);\n        if (!foldingModel || showFoldingControls === 'never') {\n            return;\n        }\n        const foldingRegions = foldingModel.regions;\n        const indexOfFoldingRegion = foldingRegions.findRange(line);\n        const startLineNumber = foldingRegions.getStartLineNumber(indexOfFoldingRegion);\n        const isFoldingScope = line === startLineNumber;\n        if (!isFoldingScope) {\n            return;\n        }\n        const isCollapsed = foldingRegions.isCollapsed(indexOfFoldingRegion);\n        const foldingIcon = new StickyFoldingIcon(isCollapsed, startLineNumber, foldingRegions.getEndLineNumber(indexOfFoldingRegion), this._lineHeight);\n        foldingIcon.setVisible(this._isOnGlyphMargin ? true : (isCollapsed || showFoldingControls === 'always'));\n        foldingIcon.domNode.setAttribute(STICKY_IS_FOLDING_ICON_ATTR, '');\n        return foldingIcon;\n    }\n    getId() {\n        return 'editor.contrib.stickyScrollWidget';\n    }\n    getDomNode() {\n        return this._rootDomNode;\n    }\n    getPosition() {\n        return {\n            preference: 2 /* OverlayWidgetPositionPreference.TOP_CENTER */,\n            stackOridinal: 10,\n        };\n    }\n    getMinContentWidthInPx() {\n        return this._minContentWidthInPx;\n    }\n    focusLineWithIndex(index) {\n        if (0 <= index && index < this._renderedStickyLines.length) {\n            this._renderedStickyLines[index].lineDomNode.focus();\n        }\n    }\n    /**\n     * Given a leaf dom node, tries to find the editor position.\n     */\n    getEditorPositionFromNode(spanDomNode) {\n        if (!spanDomNode || spanDomNode.children.length > 0) {\n            // This is not a leaf node\n            return null;\n        }\n        const renderedStickyLine = this._getRenderedStickyLineFromChildDomNode(spanDomNode);\n        if (!renderedStickyLine) {\n            return null;\n        }\n        const column = getColumnOfNodeOffset(renderedStickyLine.characterMapping, spanDomNode, 0);\n        return new Position(renderedStickyLine.lineNumber, column);\n    }\n    getLineNumberFromChildDomNode(domNode) {\n        return this._getRenderedStickyLineFromChildDomNode(domNode)?.lineNumber ?? null;\n    }\n    _getRenderedStickyLineFromChildDomNode(domNode) {\n        const index = this.getLineIndexFromChildDomNode(domNode);\n        if (index === null || index < 0 || index >= this._renderedStickyLines.length) {\n            return null;\n        }\n        return this._renderedStickyLines[index];\n    }\n    /**\n     * Given a child dom node, tries to find the line number attribute that was stored in the node.\n     * @returns the attribute value or null if none is found.\n     */\n    getLineIndexFromChildDomNode(domNode) {\n        const lineIndex = this._getAttributeValue(domNode, STICKY_INDEX_ATTR);\n        return lineIndex ? parseInt(lineIndex, 10) : null;\n    }\n    /**\n     * Given a child dom node, tries to find if it is (contained in) a sticky line.\n     * @returns a boolean.\n     */\n    isInStickyLine(domNode) {\n        const isInLine = this._getAttributeValue(domNode, STICKY_IS_LINE_ATTR);\n        return isInLine !== undefined;\n    }\n    /**\n     * Given a child dom node, tries to find if this dom node is (contained in) a sticky folding icon.\n     * @returns a boolean.\n     */\n    isInFoldingIconDomNode(domNode) {\n        const isInFoldingIcon = this._getAttributeValue(domNode, STICKY_IS_FOLDING_ICON_ATTR);\n        return isInFoldingIcon !== undefined;\n    }\n    /**\n     * Given the dom node, finds if it or its parent sequence contains the given attribute.\n     * @returns the attribute value or undefined.\n     */\n    _getAttributeValue(domNode, attribute) {\n        while (domNode && domNode !== this._rootDomNode) {\n            const line = domNode.getAttribute(attribute);\n            if (line !== null) {\n                return line;\n            }\n            domNode = domNode.parentElement;\n        }\n        return;\n    }\n}\nclass RenderedStickyLine {\n    constructor(index, lineNumber, lineDomNode, lineNumberDomNode, foldingIcon, characterMapping, scrollWidth) {\n        this.index = index;\n        this.lineNumber = lineNumber;\n        this.lineDomNode = lineDomNode;\n        this.lineNumberDomNode = lineNumberDomNode;\n        this.foldingIcon = foldingIcon;\n        this.characterMapping = characterMapping;\n        this.scrollWidth = scrollWidth;\n    }\n}\nclass StickyFoldingIcon {\n    constructor(isCollapsed, foldingStartLine, foldingEndLine, dimension) {\n        this.isCollapsed = isCollapsed;\n        this.foldingStartLine = foldingStartLine;\n        this.foldingEndLine = foldingEndLine;\n        this.dimension = dimension;\n        this.domNode = document.createElement('div');\n        this.domNode.style.width = `${dimension}px`;\n        this.domNode.style.height = `${dimension}px`;\n        this.domNode.className = ThemeIcon.asClassName(isCollapsed ? foldingCollapsedIcon : foldingExpandedIcon);\n    }\n    setVisible(visible) {\n        this.domNode.style.cursor = visible ? 'pointer' : 'default';\n        this.domNode.style.opacity = visible ? '1' : '0';\n    }\n}\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA,OAAO,KAAKA,GAAG,MAAM,iCAAiC;AACtD,SAASC,wBAAwB,QAAQ,0CAA0C;AACnF,SAASC,MAAM,QAAQ,mCAAmC;AAC1D,SAASC,UAAU,EAAEC,eAAe,QAAQ,sCAAsC;AAClF,SAASC,SAAS,QAAQ,sCAAsC;AAChE,OAAO,oBAAoB;AAC3B,SAASC,qBAAqB,QAAQ,8CAA8C;AACpF,SAASC,wBAAwB,QAAQ,gEAAgE;AACzG,SAASC,QAAQ,QAAQ,kCAAkC;AAC3D,SAASC,aAAa,QAAQ,uCAAuC;AACrE,SAASC,cAAc,QAAQ,+CAA+C;AAC9E,SAASC,eAAe,EAAEC,cAAc,QAAQ,gDAAgD;AAChG,SAASC,oBAAoB,EAAEC,mBAAmB,QAAQ,6CAA6C;AACvG,OAAO,MAAMC,uBAAuB,CAAC;EACjCC,WAAWA,CAACC,gBAAgB,EAAEC,cAAc,EAAEC,wBAAwB,EAAEC,cAAc,GAAG,IAAI,EAAE;IAC3F,IAAI,CAACH,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,wBAAwB,GAAGA,wBAAwB;IACxD,IAAI,CAACC,cAAc,GAAGA,cAAc;EACxC;EACAlB,MAAMA,CAACmB,KAAK,EAAE;IACV,OAAO,CAAC,CAACA,KAAK,IACP,IAAI,CAACF,wBAAwB,KAAKE,KAAK,CAACF,wBAAwB,IAChE,IAAI,CAACC,cAAc,KAAKC,KAAK,CAACD,cAAc,IAC5ClB,MAAM,CAAC,IAAI,CAACe,gBAAgB,EAAEI,KAAK,CAACJ,gBAAgB,CAAC,IACrDf,MAAM,CAAC,IAAI,CAACgB,cAAc,EAAEG,KAAK,CAACH,cAAc,CAAC;EAC5D;EACA,WAAWI,KAAKA,CAAA,EAAG;IACf,OAAO,IAAIP,uBAAuB,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;EACjD;AACJ;AACA,MAAMQ,SAAS,GAAGtB,wBAAwB,CAAC,uBAAuB,EAAE;EAAEuB,UAAU,EAAEC,KAAK,IAAIA;AAAM,CAAC,CAAC;AACnG,MAAMC,iBAAiB,GAAG,wBAAwB;AAClD,MAAMC,mBAAmB,GAAG,qBAAqB;AACjD,MAAMC,0BAA0B,GAAG,4BAA4B;AAC/D,MAAMC,2BAA2B,GAAG,6BAA6B;AACjE,OAAO,MAAMC,kBAAkB,SAAS3B,UAAU,CAAC;EAC/Ca,WAAWA,CAACe,OAAO,EAAE;IACjB,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,iBAAiB,GAAG,IAAI5B,eAAe,CAAC,CAAC;IAC9C,IAAI,CAAC6B,YAAY,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IACjD,IAAI,CAACC,mBAAmB,GAAGF,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IACxD,IAAI,CAACE,uBAAuB,GAAGH,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC5D,IAAI,CAACG,aAAa,GAAGJ,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAClD,IAAI,CAACI,WAAW,GAAG,IAAI,CAACR,OAAO,CAACS,SAAS,CAAC,EAAE,CAAC,6BAA6B,CAAC;IAC3E,IAAI,CAACC,oBAAoB,GAAG,EAAE;IAC9B,IAAI,CAACC,YAAY,GAAG,EAAE;IACtB,IAAI,CAACC,yBAAyB,GAAG,CAAC;IAClC,IAAI,CAACC,oBAAoB,GAAG,CAAC;IAC7B,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACT,mBAAmB,CAACU,SAAS,GAAG,4BAA4B;IACjE,IAAI,CAACV,mBAAmB,CAACW,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC;IACrD,IAAI,CAACT,aAAa,CAACQ,SAAS,GAAG,qBAAqB;IACpD,IAAI,CAACR,aAAa,CAACS,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC;IAC/C,IAAI,CAACV,uBAAuB,CAACS,SAAS,GAAG,gCAAgC;IACzE,IAAI,CAACT,uBAAuB,CAACW,WAAW,CAAC,IAAI,CAACV,aAAa,CAAC;IAC5D,IAAI,CAACL,YAAY,CAACa,SAAS,GAAG,eAAe;IAC7C,IAAI,CAACb,YAAY,CAACgB,SAAS,CAACC,MAAM,CAAC,MAAM,EAAEnB,OAAO,YAAYxB,wBAAwB,CAAC;IACvF,IAAI,CAAC0B,YAAY,CAACe,WAAW,CAAC,IAAI,CAACZ,mBAAmB,CAAC;IACvD,IAAI,CAACH,YAAY,CAACe,WAAW,CAAC,IAAI,CAACX,uBAAuB,CAAC;IAC3D,MAAMc,wBAAwB,GAAGA,CAAA,KAAM;MACnC,IAAI,CAACb,aAAa,CAACc,KAAK,CAACC,IAAI,GAAG,IAAI,CAACtB,OAAO,CAACS,SAAS,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAACc,gBAAgB,GAAG,IAAI,IAAI,CAACvB,OAAO,CAACwB,aAAa,CAAC,CAAC,IAAI,GAAG,KAAK;IAC/J,CAAC;IACD,IAAI,CAACC,SAAS,CAAC,IAAI,CAACzB,OAAO,CAAC0B,wBAAwB,CAAEC,CAAC,IAAK;MACxD,IAAIA,CAAC,CAACC,UAAU,CAAC,GAAG,CAAC,+BAA+B,CAAC,EAAE;QACnDR,wBAAwB,CAAC,CAAC;MAC9B;MACA,IAAIO,CAAC,CAACC,UAAU,CAAC,EAAE,CAAC,6BAA6B,CAAC,EAAE;QAChD,IAAI,CAACpB,WAAW,GAAG,IAAI,CAACR,OAAO,CAACS,SAAS,CAAC,EAAE,CAAC,6BAA6B,CAAC;MAC/E;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,CAACgB,SAAS,CAAC,IAAI,CAACzB,OAAO,CAAC6B,iBAAiB,CAAEF,CAAC,IAAK;MACjD,IAAIA,CAAC,CAACG,iBAAiB,EAAE;QACrBV,wBAAwB,CAAC,CAAC;MAC9B;MACA,IAAIO,CAAC,CAACI,kBAAkB,EAAE;QACtB,IAAI,CAACC,kBAAkB,CAAC,CAAC;MAC7B;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,CAACP,SAAS,CAAC,IAAI,CAACzB,OAAO,CAACiC,gBAAgB,CAAC,MAAM;MAC/Cb,wBAAwB,CAAC,CAAC;MAC1B,IAAI,CAACY,kBAAkB,CAAC,CAAC;IAC7B,CAAC,CAAC,CAAC;IACH,IAAI,CAACP,SAAS,CAAC,IAAI,CAACxB,iBAAiB,CAAC;IACtCmB,wBAAwB,CAAC,CAAC;IAC1B,IAAI,CAACK,SAAS,CAAC,IAAI,CAACzB,OAAO,CAACkC,iBAAiB,CAAEP,CAAC,IAAK;MACjD,IAAI,CAACK,kBAAkB,CAAC,CAAC;IAC7B,CAAC,CAAC,CAAC;IACH,IAAI,CAACA,kBAAkB,CAAC,CAAC;EAC7B;EACA,IAAIG,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACxB,YAAY;EAC5B;EACA,IAAIyB,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACzB,YAAY,CAAC0B,MAAM;EACnC;EACAC,qBAAqBA,CAACC,UAAU,EAAE;IAC9B,OAAO,IAAI,CAAC7B,oBAAoB,CAAC8B,IAAI,CAACC,UAAU,IAAIA,UAAU,CAACF,UAAU,KAAKA,UAAU,CAAC;EAC7F;EACAG,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAAC/B,YAAY;EAC5B;EACAgC,QAAQA,CAACC,MAAM,EAAEC,YAAY,EAAEC,gBAAgB,EAAE;IAC7C,IAAIA,gBAAgB,KAAKC,SAAS,KAC5B,CAAC,IAAI,CAACC,cAAc,IAAI,CAACJ,MAAM,IAAM,IAAI,CAACI,cAAc,IAAI,IAAI,CAACA,cAAc,CAAC7E,MAAM,CAACyE,MAAM,CAAE,CAAC,EAAE;MACpG;IACJ;IACA,MAAMK,kBAAkB,GAAG,IAAI,CAACC,mBAAmB,CAACN,MAAM,CAAC;IAC3D,MAAMO,KAAK,GAAGF,kBAAkB,GAAGF,SAAS,GAAGH,MAAM;IACrD,MAAMQ,eAAe,GAAGH,kBAAkB,GAAG,CAAC,GAAG,IAAI,CAACI,4BAA4B,CAACT,MAAM,EAAEE,gBAAgB,CAAC;IAC5G,IAAI,CAACQ,eAAe,CAACH,KAAK,EAAEN,YAAY,EAAEO,eAAe,CAAC;IAC1D,IAAI,CAACJ,cAAc,GAAGJ,MAAM;EAChC;EACAM,mBAAmBA,CAACC,KAAK,EAAE;IACvB,IAAI,CAACA,KAAK,EAAE;MACR,OAAO,IAAI;IACf;IACA,MAAMI,kBAAkB,GAAGJ,KAAK,CAACjE,gBAAgB,CAACmD,MAAM,GAAG,IAAI,CAAC7B,WAAW,GAAG2C,KAAK,CAAC/D,wBAAwB;IAC5G,IAAImE,kBAAkB,GAAG,CAAC,EAAE;MACxB,IAAI,CAAC3C,yBAAyB,GAAGuC,KAAK,CAAC/D,wBAAwB;MAC/D,MAAM+C,WAAW,GAAG,CAAC,GAAGgB,KAAK,CAACjE,gBAAgB,CAAC;MAC/C,IAAIiE,KAAK,CAAC9D,cAAc,KAAK,IAAI,EAAE;QAC/B8C,WAAW,CAACgB,KAAK,CAAC9D,cAAc,CAAC,GAAG8D,KAAK,CAAChE,cAAc,CAACgE,KAAK,CAAC9D,cAAc,CAAC;MAClF;MACA,IAAI,CAACsB,YAAY,GAAGwB,WAAW;IACnC,CAAC,MACI;MACD,IAAI,CAACvB,yBAAyB,GAAG,CAAC;MAClC,IAAI,CAACD,YAAY,GAAG,EAAE;IAC1B;IACA,OAAO4C,kBAAkB,KAAK,CAAC;EACnC;EACAF,4BAA4BA,CAACF,KAAK,EAAEL,gBAAgB,EAAE;IAClD,IAAI,CAACK,KAAK,IAAI,CAAC,IAAI,CAACH,cAAc,EAAE;MAChC,OAAO,CAAC;IACZ;IACA,IAAIF,gBAAgB,KAAKC,SAAS,EAAE;MAChC,OAAOD,gBAAgB;IAC3B;IACA,MAAMU,aAAa,GAAG,IAAI,CAACR,cAAc;IACzC,MAAMS,2BAA2B,GAAGN,KAAK,CAACjE,gBAAgB,CAACwE,SAAS,CAACC,eAAe,IAAI,CAACH,aAAa,CAACtE,gBAAgB,CAAC0E,QAAQ,CAACD,eAAe,CAAC,CAAC;IAClJ,OAAQF,2BAA2B,KAAK,CAAC,CAAC,GAAI,CAAC,GAAGA,2BAA2B;EACjF;EACAzB,kBAAkBA,CAAA,EAAG;IACjB,MAAM6B,UAAU,GAAG,IAAI,CAAC7D,OAAO,CAAC8D,aAAa,CAAC,CAAC;IAC/C,MAAMC,gBAAgB,GAAGF,UAAU,CAACG,WAAW;IAC/C,IAAI,CAAC3D,mBAAmB,CAACgB,KAAK,CAAC4C,KAAK,GAAG,GAAGF,gBAAgB,IAAI;IAC9D,IAAI,CAACzD,uBAAuB,CAACe,KAAK,CAAC6C,WAAW,CAAC,6CAA6C,EAAE,GAAG,IAAI,CAAClE,OAAO,CAACmE,cAAc,CAAC,CAAC,GAAGN,UAAU,CAACO,sBAAsB,IAAI,CAAC;IACvK,IAAI,CAAClE,YAAY,CAACmB,KAAK,CAAC4C,KAAK,GAAG,GAAGJ,UAAU,CAACI,KAAK,GAAGJ,UAAU,CAACO,sBAAsB,IAAI;EAC/F;EACAC,yBAAyBA,CAACC,aAAa,EAAE;IACrC,IAAI,CAACrE,iBAAiB,CAACsE,KAAK,CAAC,CAAC;IAC9B;IACA,KAAK,IAAIC,CAAC,GAAGF,aAAa,EAAEE,CAAC,GAAG,IAAI,CAAC9D,oBAAoB,CAAC2B,MAAM,EAAEmC,CAAC,EAAE,EAAE;MACnE,MAAM/B,UAAU,GAAG,IAAI,CAAC/B,oBAAoB,CAAC8D,CAAC,CAAC;MAC/C/B,UAAU,CAACgC,iBAAiB,CAACC,MAAM,CAAC,CAAC;MACrCjC,UAAU,CAACkC,WAAW,CAACD,MAAM,CAAC,CAAC;IACnC;IACA;IACA,IAAI,CAAChE,oBAAoB,GAAG,IAAI,CAACA,oBAAoB,CAACkE,KAAK,CAAC,CAAC,EAAEN,aAAa,CAAC;IAC7E,IAAI,CAACpE,YAAY,CAACmB,KAAK,CAACwD,OAAO,GAAG,MAAM;EAC5C;EACAC,4BAA4BA,CAACC,kBAAkB,EAAE;IAC7C,IAAI,CAAC1E,mBAAmB,CAACgB,KAAK,CAAC6C,WAAW,CAAC,sDAAsD,EAAE,WAAWa,kBAAkB,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC;EAClJ;EACAC,0BAA0BA,CAACC,UAAU,EAAE;IACnC,KAAK,MAAMC,IAAI,IAAI,IAAI,CAACxE,oBAAoB,EAAE;MAC1C,MAAMyE,WAAW,GAAGD,IAAI,CAACC,WAAW;MACpC,IAAI,CAACA,WAAW,EAAE;QACd;MACJ;MACAA,WAAW,CAACC,UAAU,CAACH,UAAU,GAAG,IAAI,GAAGE,WAAW,CAACE,WAAW,CAAC;IACvE;EACJ;EACM/B,eAAeA,CAACH,KAAK,EAAEN,YAAY,EAAEO,eAAe,EAAE;IAAA,IAAAkC,KAAA;IAAA,OAAAC,iBAAA;MACxDD,KAAI,CAACjB,yBAAyB,CAACjB,eAAe,CAAC;MAC/C,IAAI,CAACD,KAAK,EAAE;QACR;MACJ;MACA;MACA,KAAK,MAAMV,UAAU,IAAI6C,KAAI,CAAC5E,oBAAoB,EAAE;QAChD4E,KAAI,CAACE,+BAA+B,CAAC/C,UAAU,CAAC;MACpD;MACA;MACA,MAAMoB,UAAU,GAAGyB,KAAI,CAACtF,OAAO,CAAC8D,aAAa,CAAC,CAAC;MAC/C,MAAM2B,aAAa,GAAGH,KAAI,CAAC3E,YAAY,CAACiE,KAAK,CAACxB,eAAe,CAAC;MAC9D,KAAK,MAAM,CAACsC,KAAK,EAAER,IAAI,CAAC,IAAIO,aAAa,CAACE,OAAO,CAAC,CAAC,EAAE;QACjD,MAAMlD,UAAU,GAAG6C,KAAI,CAACM,gBAAgB,CAACF,KAAK,GAAGtC,eAAe,EAAE8B,IAAI,EAAErC,YAAY,EAAEgB,UAAU,CAAC;QACjG,IAAI,CAACpB,UAAU,EAAE;UACb;QACJ;QACA6C,KAAI,CAAC/E,aAAa,CAACU,WAAW,CAACwB,UAAU,CAACkC,WAAW,CAAC;QACtDW,KAAI,CAACjF,mBAAmB,CAACY,WAAW,CAACwB,UAAU,CAACgC,iBAAiB,CAAC;QAClEa,KAAI,CAAC5E,oBAAoB,CAACmF,IAAI,CAACpD,UAAU,CAAC;MAC9C;MACA,IAAII,YAAY,EAAE;QACdyC,KAAI,CAACQ,yBAAyB,CAAC,CAAC;QAChCR,KAAI,CAACR,4BAA4B,CAAC,CAACQ,KAAI,CAACxE,gBAAgB,CAAC;MAC7D;MACA,MAAMiF,YAAY,GAAGT,KAAI,CAAC3E,YAAY,CAAC0B,MAAM,GAAGiD,KAAI,CAAC9E,WAAW,GAAG8E,KAAI,CAAC1E,yBAAyB;MACjG0E,KAAI,CAACpF,YAAY,CAACmB,KAAK,CAACwD,OAAO,GAAG,OAAO;MACzCS,KAAI,CAACjF,mBAAmB,CAACgB,KAAK,CAAC2E,MAAM,GAAG,GAAGD,YAAY,IAAI;MAC3DT,KAAI,CAAChF,uBAAuB,CAACe,KAAK,CAAC2E,MAAM,GAAG,GAAGD,YAAY,IAAI;MAC/DT,KAAI,CAACpF,YAAY,CAACmB,KAAK,CAAC2E,MAAM,GAAG,GAAGD,YAAY,IAAI;MACpDT,KAAI,CAACpF,YAAY,CAACmB,KAAK,CAAC4E,UAAU,GAAG,KAAK;MAC1CX,KAAI,CAACzE,oBAAoB,GAAGqF,IAAI,CAACC,GAAG,CAAC,GAAGb,KAAI,CAAC5E,oBAAoB,CAAC0F,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGzC,UAAU,CAACO,sBAAsB;MAC9HkB,KAAI,CAACtF,OAAO,CAACuG,mBAAmB,CAACjB,KAAI,CAAC;IAAC;EAC3C;EACAQ,yBAAyBA,CAAA,EAAG;IACxB,MAAMU,mBAAmB,GAAG,IAAI,CAACxG,OAAO,CAACS,SAAS,CAAC,GAAG,CAAC,sCAAsC,CAAC;IAC9F,IAAI+F,mBAAmB,KAAK,WAAW,EAAE;MACrC;IACJ;IACA,IAAI,CAACvG,iBAAiB,CAACwG,GAAG,CAACxI,GAAG,CAACyI,qBAAqB,CAAC,IAAI,CAACrG,mBAAmB,EAAEpC,GAAG,CAAC0I,SAAS,CAACC,WAAW,EAAE,MAAM;MAC5G,IAAI,CAAC9F,gBAAgB,GAAG,IAAI;MAC5B,IAAI,CAACkE,0BAA0B,CAAC,IAAI,CAAC;IACzC,CAAC,CAAC,CAAC;IACH,IAAI,CAAC/E,iBAAiB,CAACwG,GAAG,CAACxI,GAAG,CAACyI,qBAAqB,CAAC,IAAI,CAACrG,mBAAmB,EAAEpC,GAAG,CAAC0I,SAAS,CAACE,WAAW,EAAE,MAAM;MAC5G,IAAI,CAAC/F,gBAAgB,GAAG,KAAK;MAC7B,IAAI,CAACgE,4BAA4B,CAAC,IAAI,CAAC;MACvC,IAAI,CAACE,0BAA0B,CAAC,KAAK,CAAC;IAC1C,CAAC,CAAC,CAAC;EACP;EACAY,gBAAgBA,CAACF,KAAK,EAAER,IAAI,EAAErC,YAAY,EAAEgB,UAAU,EAAE;IACpD,MAAMiD,SAAS,GAAG,IAAI,CAAC9G,OAAO,CAAC+G,aAAa,CAAC,CAAC;IAC9C,IAAI,CAACD,SAAS,EAAE;MACZ;IACJ;IACA,MAAME,cAAc,GAAGF,SAAS,CAACG,oBAAoB,CAACC,kCAAkC,CAAC,IAAIzI,QAAQ,CAACyG,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC3C,UAAU;IAC1H,MAAM4E,iBAAiB,GAAGL,SAAS,CAACM,wBAAwB,CAACJ,cAAc,CAAC;IAC5E,MAAMK,gBAAgB,GAAG,IAAI,CAACrH,OAAO,CAACS,SAAS,CAAC,EAAE,CAAC,8BAA8B,CAAC;IAClF,IAAI6G,uBAAuB;IAC3B,IAAI;MACAA,uBAAuB,GAAG3I,cAAc,CAAC4I,MAAM,CAACJ,iBAAiB,CAACK,iBAAiB,EAAER,cAAc,EAAEG,iBAAiB,CAACM,SAAS,EAAEN,iBAAiB,CAACO,SAAS,CAAC;IAClK,CAAC,CACD,OAAOC,GAAG,EAAE;MACRL,uBAAuB,GAAG,EAAE;IAChC;IACA,MAAMM,eAAe,GAAG,IAAIhJ,eAAe,CAAC,IAAI,EAAE,IAAI,EAAEuI,iBAAiB,CAACU,OAAO,EAAEV,iBAAiB,CAACW,wBAAwB,EAAEX,iBAAiB,CAACY,YAAY,EAAEZ,iBAAiB,CAACa,WAAW,EAAE,CAAC,EAAEb,iBAAiB,CAACc,MAAM,EAAEX,uBAAuB,EAAEH,iBAAiB,CAACe,OAAO,EAAEf,iBAAiB,CAACgB,kBAAkB,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC5V,MAAMC,EAAE,GAAG,IAAI1J,aAAa,CAAC,IAAI,CAAC;IAClC,MAAM2J,YAAY,GAAGxJ,cAAc,CAAC+I,eAAe,EAAEQ,EAAE,CAAC;IACxD,IAAIE,OAAO;IACX,IAAI9I,SAAS,EAAE;MACX8I,OAAO,GAAG9I,SAAS,CAACC,UAAU,CAAC2I,EAAE,CAACG,KAAK,CAAC,CAAC,CAAC;IAC9C,CAAC,MACI;MACDD,OAAO,GAAGF,EAAE,CAACG,KAAK,CAAC,CAAC;IACxB;IACA,MAAMC,YAAY,GAAGrI,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;IACnDoI,YAAY,CAACxH,YAAY,CAACrB,iBAAiB,EAAE8I,MAAM,CAAC/C,KAAK,CAAC,CAAC;IAC3D8C,YAAY,CAACxH,YAAY,CAACpB,mBAAmB,EAAE,EAAE,CAAC;IAClD4I,YAAY,CAACxH,YAAY,CAAC,MAAM,EAAE,UAAU,CAAC;IAC7CwH,YAAY,CAACE,QAAQ,GAAG,CAAC;IACzBF,YAAY,CAACzH,SAAS,GAAG,qBAAqB;IAC9CyH,YAAY,CAACtH,SAAS,CAACuF,GAAG,CAAC,aAAavB,IAAI,EAAE,CAAC;IAC/CsD,YAAY,CAACnH,KAAK,CAACsH,UAAU,GAAG,GAAG,IAAI,CAACnI,WAAW,IAAI;IACvDgI,YAAY,CAACI,SAAS,GAAGN,OAAO;IAChC,MAAMO,kBAAkB,GAAG1I,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;IACzDyI,kBAAkB,CAAC7H,YAAY,CAACrB,iBAAiB,EAAE8I,MAAM,CAAC/C,KAAK,CAAC,CAAC;IACjEmD,kBAAkB,CAAC7H,YAAY,CAACnB,0BAA0B,EAAE,EAAE,CAAC;IAC/DgJ,kBAAkB,CAAC9H,SAAS,GAAG,oBAAoB;IACnD8H,kBAAkB,CAACxH,KAAK,CAACsH,UAAU,GAAG,GAAG,IAAI,CAACnI,WAAW,IAAI;IAC7D,MAAMuD,gBAAgB,GAAGF,UAAU,CAACG,WAAW;IAC/C6E,kBAAkB,CAACxH,KAAK,CAAC4C,KAAK,GAAG,GAAGF,gBAAgB,IAAI;IACxD,MAAM+E,mBAAmB,GAAG3I,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;IAC1D,IAAIiH,gBAAgB,CAAC0B,UAAU,KAAK,CAAC,CAAC,kCAAkC1B,gBAAgB,CAAC0B,UAAU,KAAK,CAAC,CAAC,wCAAwC7D,IAAI,GAAG,EAAE,KAAK,CAAC,EAAE;MAC/J4D,mBAAmB,CAACE,SAAS,GAAG9D,IAAI,CAAC+D,QAAQ,CAAC,CAAC;IACnD,CAAC,MACI,IAAI5B,gBAAgB,CAAC0B,UAAU,KAAK,CAAC,CAAC,sCAAsC;MAC7ED,mBAAmB,CAACE,SAAS,GAAG9C,IAAI,CAACgD,GAAG,CAAChE,IAAI,GAAG,IAAI,CAAClF,OAAO,CAACmJ,WAAW,CAAC,CAAC,CAAC5G,UAAU,CAAC,CAAC0G,QAAQ,CAAC,CAAC;IACrG;IACAH,mBAAmB,CAAC/H,SAAS,GAAG,0BAA0B;IAC1D+H,mBAAmB,CAACzH,KAAK,CAACsH,UAAU,GAAG,GAAG,IAAI,CAACnI,WAAW,IAAI;IAC9DsI,mBAAmB,CAACzH,KAAK,CAAC4C,KAAK,GAAG,GAAGJ,UAAU,CAACE,gBAAgB,IAAI;IACpE+E,mBAAmB,CAACzH,KAAK,CAAC+H,WAAW,GAAG,GAAGvF,UAAU,CAACwF,eAAe,IAAI;IACzER,kBAAkB,CAAC5H,WAAW,CAAC6H,mBAAmB,CAAC;IACnD,MAAM3D,WAAW,GAAG,IAAI,CAACmE,yBAAyB,CAACzG,YAAY,EAAEqC,IAAI,CAAC;IACtE,IAAIC,WAAW,EAAE;MACb0D,kBAAkB,CAAC5H,WAAW,CAACkE,WAAW,CAACoE,OAAO,CAAC;IACvD;IACA,IAAI,CAACvJ,OAAO,CAACwJ,aAAa,CAAChB,YAAY,CAAC;IACxC,IAAI,CAACxI,OAAO,CAACwJ,aAAa,CAACV,mBAAmB,CAAC;IAC/CD,kBAAkB,CAACxH,KAAK,CAACsH,UAAU,GAAG,GAAG,IAAI,CAACnI,WAAW,IAAI;IAC7DgI,YAAY,CAACnH,KAAK,CAACsH,UAAU,GAAG,GAAG,IAAI,CAACnI,WAAW,IAAI;IACvDqI,kBAAkB,CAACxH,KAAK,CAAC2E,MAAM,GAAG,GAAG,IAAI,CAACxF,WAAW,IAAI;IACzDgI,YAAY,CAACnH,KAAK,CAAC2E,MAAM,GAAG,GAAG,IAAI,CAACxF,WAAW,IAAI;IACnD,MAAMiJ,YAAY,GAAG,IAAIC,kBAAkB,CAAChE,KAAK,EAAER,IAAI,EAAEsD,YAAY,EAAEK,kBAAkB,EAAE1D,WAAW,EAAEkD,YAAY,CAACsB,gBAAgB,EAAEnB,YAAY,CAAClC,WAAW,CAAC;IAChK,OAAO,IAAI,CAACd,+BAA+B,CAACiE,YAAY,CAAC;EAC7D;EACAjE,+BAA+BA,CAAC/C,UAAU,EAAE;IACxC,MAAMiD,KAAK,GAAGjD,UAAU,CAACiD,KAAK;IAC9B,MAAM8C,YAAY,GAAG/F,UAAU,CAACkC,WAAW;IAC3C,MAAMkE,kBAAkB,GAAGpG,UAAU,CAACgC,iBAAiB;IACvD,MAAMmF,UAAU,GAAGlE,KAAK,KAAK,IAAI,CAAC/E,YAAY,CAAC0B,MAAM,GAAG,CAAC;IACzD,MAAMwH,cAAc,GAAG,GAAG;IAC1B,MAAMC,sBAAsB,GAAG,GAAG;IAClCtB,YAAY,CAACnH,KAAK,CAAC0I,MAAM,GAAGH,UAAU,GAAGC,cAAc,GAAGC,sBAAsB;IAChFjB,kBAAkB,CAACxH,KAAK,CAAC0I,MAAM,GAAGH,UAAU,GAAGC,cAAc,GAAGC,sBAAsB;IACtF,MAAME,WAAW,GAAG,GAAGtE,KAAK,GAAG,IAAI,CAAClF,WAAW,GAAG,IAAI,CAACI,yBAAyB,IAAI6B,UAAU,CAAC0C,WAAW,EAAEE,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI;IACpI,MAAM4E,mBAAmB,GAAG,GAAGvE,KAAK,GAAG,IAAI,CAAClF,WAAW,IAAI;IAC3DgI,YAAY,CAACnH,KAAK,CAAC6I,GAAG,GAAGN,UAAU,GAAGI,WAAW,GAAGC,mBAAmB;IACvEpB,kBAAkB,CAACxH,KAAK,CAAC6I,GAAG,GAAGN,UAAU,GAAGI,WAAW,GAAGC,mBAAmB;IAC7E,OAAOxH,UAAU;EACrB;EACA6G,yBAAyBA,CAACzG,YAAY,EAAEqC,IAAI,EAAE;IAC1C,MAAMsB,mBAAmB,GAAG,IAAI,CAACxG,OAAO,CAACS,SAAS,CAAC,GAAG,CAAC,sCAAsC,CAAC;IAC9F,IAAI,CAACoC,YAAY,IAAI2D,mBAAmB,KAAK,OAAO,EAAE;MAClD;IACJ;IACA,MAAM2D,cAAc,GAAGtH,YAAY,CAACuH,OAAO;IAC3C,MAAMC,oBAAoB,GAAGF,cAAc,CAACG,SAAS,CAACpF,IAAI,CAAC;IAC3D,MAAMvB,eAAe,GAAGwG,cAAc,CAACI,kBAAkB,CAACF,oBAAoB,CAAC;IAC/E,MAAMG,cAAc,GAAGtF,IAAI,KAAKvB,eAAe;IAC/C,IAAI,CAAC6G,cAAc,EAAE;MACjB;IACJ;IACA,MAAMnF,WAAW,GAAG8E,cAAc,CAAC9E,WAAW,CAACgF,oBAAoB,CAAC;IACpE,MAAMlF,WAAW,GAAG,IAAIsF,iBAAiB,CAACpF,WAAW,EAAE1B,eAAe,EAAEwG,cAAc,CAACO,gBAAgB,CAACL,oBAAoB,CAAC,EAAE,IAAI,CAAC7J,WAAW,CAAC;IAChJ2E,WAAW,CAACC,UAAU,CAAC,IAAI,CAACtE,gBAAgB,GAAG,IAAI,GAAIuE,WAAW,IAAImB,mBAAmB,KAAK,QAAS,CAAC;IACxGrB,WAAW,CAACoE,OAAO,CAACvI,YAAY,CAAClB,2BAA2B,EAAE,EAAE,CAAC;IACjE,OAAOqF,WAAW;EACtB;EACAwF,KAAKA,CAAA,EAAG;IACJ,OAAO,mCAAmC;EAC9C;EACAC,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAAC1K,YAAY;EAC5B;EACAiJ,WAAWA,CAAA,EAAG;IACV,OAAO;MACH0B,UAAU,EAAE,CAAC,CAAC;MACdC,aAAa,EAAE;IACnB,CAAC;EACL;EACAC,sBAAsBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAAClK,oBAAoB;EACpC;EACAmK,kBAAkBA,CAACtF,KAAK,EAAE;IACtB,IAAI,CAAC,IAAIA,KAAK,IAAIA,KAAK,GAAG,IAAI,CAAChF,oBAAoB,CAAC2B,MAAM,EAAE;MACxD,IAAI,CAAC3B,oBAAoB,CAACgF,KAAK,CAAC,CAACf,WAAW,CAACsG,KAAK,CAAC,CAAC;IACxD;EACJ;EACA;AACJ;AACA;EACIC,yBAAyBA,CAACC,WAAW,EAAE;IACnC,IAAI,CAACA,WAAW,IAAIA,WAAW,CAACC,QAAQ,CAAC/I,MAAM,GAAG,CAAC,EAAE;MACjD;MACA,OAAO,IAAI;IACf;IACA,MAAMgJ,kBAAkB,GAAG,IAAI,CAACC,sCAAsC,CAACH,WAAW,CAAC;IACnF,IAAI,CAACE,kBAAkB,EAAE;MACrB,OAAO,IAAI;IACf;IACA,MAAME,MAAM,GAAGhN,qBAAqB,CAAC8M,kBAAkB,CAAC1B,gBAAgB,EAAEwB,WAAW,EAAE,CAAC,CAAC;IACzF,OAAO,IAAI1M,QAAQ,CAAC4M,kBAAkB,CAAC9I,UAAU,EAAEgJ,MAAM,CAAC;EAC9D;EACAC,6BAA6BA,CAACjC,OAAO,EAAE;IACnC,OAAO,IAAI,CAAC+B,sCAAsC,CAAC/B,OAAO,CAAC,EAAEhH,UAAU,IAAI,IAAI;EACnF;EACA+I,sCAAsCA,CAAC/B,OAAO,EAAE;IAC5C,MAAM7D,KAAK,GAAG,IAAI,CAAC+F,4BAA4B,CAAClC,OAAO,CAAC;IACxD,IAAI7D,KAAK,KAAK,IAAI,IAAIA,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAI,IAAI,CAAChF,oBAAoB,CAAC2B,MAAM,EAAE;MAC1E,OAAO,IAAI;IACf;IACA,OAAO,IAAI,CAAC3B,oBAAoB,CAACgF,KAAK,CAAC;EAC3C;EACA;AACJ;AACA;AACA;EACI+F,4BAA4BA,CAAClC,OAAO,EAAE;IAClC,MAAMmC,SAAS,GAAG,IAAI,CAACC,kBAAkB,CAACpC,OAAO,EAAE5J,iBAAiB,CAAC;IACrE,OAAO+L,SAAS,GAAGE,QAAQ,CAACF,SAAS,EAAE,EAAE,CAAC,GAAG,IAAI;EACrD;EACA;AACJ;AACA;AACA;EACIG,cAAcA,CAACtC,OAAO,EAAE;IACpB,MAAMuC,QAAQ,GAAG,IAAI,CAACH,kBAAkB,CAACpC,OAAO,EAAE3J,mBAAmB,CAAC;IACtE,OAAOkM,QAAQ,KAAK/I,SAAS;EACjC;EACA;AACJ;AACA;AACA;EACIgJ,sBAAsBA,CAACxC,OAAO,EAAE;IAC5B,MAAMyC,eAAe,GAAG,IAAI,CAACL,kBAAkB,CAACpC,OAAO,EAAEzJ,2BAA2B,CAAC;IACrF,OAAOkM,eAAe,KAAKjJ,SAAS;EACxC;EACA;AACJ;AACA;AACA;EACI4I,kBAAkBA,CAACpC,OAAO,EAAE0C,SAAS,EAAE;IACnC,OAAO1C,OAAO,IAAIA,OAAO,KAAK,IAAI,CAACrJ,YAAY,EAAE;MAC7C,MAAMgF,IAAI,GAAGqE,OAAO,CAAC2C,YAAY,CAACD,SAAS,CAAC;MAC5C,IAAI/G,IAAI,KAAK,IAAI,EAAE;QACf,OAAOA,IAAI;MACf;MACAqE,OAAO,GAAGA,OAAO,CAAC4C,aAAa;IACnC;IACA;EACJ;AACJ;AACA,MAAMzC,kBAAkB,CAAC;EACrBzK,WAAWA,CAACyG,KAAK,EAAEnD,UAAU,EAAEoC,WAAW,EAAEF,iBAAiB,EAAEU,WAAW,EAAEwE,gBAAgB,EAAErD,WAAW,EAAE;IACvG,IAAI,CAACZ,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACnD,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACoC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACF,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACU,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACwE,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACrD,WAAW,GAAGA,WAAW;EAClC;AACJ;AACA,MAAMmE,iBAAiB,CAAC;EACpBxL,WAAWA,CAACoG,WAAW,EAAE+G,gBAAgB,EAAEC,cAAc,EAAEC,SAAS,EAAE;IAClE,IAAI,CAACjH,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAAC+G,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAAC/C,OAAO,GAAGpJ,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC5C,IAAI,CAACmJ,OAAO,CAAClI,KAAK,CAAC4C,KAAK,GAAG,GAAGqI,SAAS,IAAI;IAC3C,IAAI,CAAC/C,OAAO,CAAClI,KAAK,CAAC2E,MAAM,GAAG,GAAGsG,SAAS,IAAI;IAC5C,IAAI,CAAC/C,OAAO,CAACxI,SAAS,GAAGzC,SAAS,CAACiO,WAAW,CAAClH,WAAW,GAAGvG,oBAAoB,GAAGC,mBAAmB,CAAC;EAC5G;EACAqG,UAAUA,CAACoH,OAAO,EAAE;IAChB,IAAI,CAACjD,OAAO,CAAClI,KAAK,CAACoL,MAAM,GAAGD,OAAO,GAAG,SAAS,GAAG,SAAS;IAC3D,IAAI,CAACjD,OAAO,CAAClI,KAAK,CAACqL,OAAO,GAAGF,OAAO,GAAG,GAAG,GAAG,GAAG;EACpD;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}