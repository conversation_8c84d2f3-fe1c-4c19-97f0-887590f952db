{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { ApprovalCardComponent, AvaTagComponent, IconComponent, ButtonComponent } from \"@ava/play-comp-library\";\nimport * as i0 from \"@angular/core\";\nexport let DashboardApprovalCardComponent = /*#__PURE__*/(() => {\n  class DashboardApprovalCardComponent {\n    title = \"\";\n    avatarUrl = \"assets/images/avatar.png\";\n    date = \"12 May 2025\";\n    email = '<EMAIL>';\n    type = '';\n    id = '';\n    rowData;\n    testClick = new EventEmitter();\n    sendBackClick = new EventEmitter();\n    approveClick = new EventEmitter();\n    approvalBtnCustomStyles = {\n      'background': 'linear-gradient(130.87deg, #0084FF 33.91%, #03BDD4 100%)'\n    };\n    onApproveClick(rowData) {\n      this.approveClick.emit(rowData);\n    }\n    onSendBackClick(rowData) {\n      this.sendBackClick.emit(rowData);\n    }\n    onTestClick() {\n      this.testClick.emit(this.rowData);\n    }\n    static ɵfac = function DashboardApprovalCardComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DashboardApprovalCardComponent)();\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DashboardApprovalCardComponent,\n      selectors: [[\"app-dashboard-approval-card\"]],\n      inputs: {\n        title: \"title\",\n        avatarUrl: \"avatarUrl\",\n        date: \"date\",\n        email: \"email\",\n        type: \"type\",\n        id: \"id\",\n        rowData: \"rowData\"\n      },\n      outputs: {\n        testClick: \"testClick\",\n        sendBackClick: \"sendBackClick\",\n        approveClick: \"approveClick\"\n      },\n      decls: 32,\n      vars: 7,\n      consts: [[\"id\", \"dashboard-approval-card\"], [\"height\", \"300\"], [\"header\", \"\"], [1, \"header\"], [\"iconSize\", \"25\", \"iconColor\", \"#3B3F46\", \"iconName\", \"ellipsis-vertical\"], [\"content\", \"\", 1, \"content\"], [1, \"box\", \"tag-wrapper\"], [1, \"avtar-tag-wrapper\"], [\"alt\", \"Avatar\", 3, \"src\"], [\"label\", \"Ascendion\", \"size\", \"sm\"], [\"label\", \"Individual Agent\", \"size\", \"sm\"], [\"size\", \"sm\", 1, \"green-tag\", 3, \"label\"], [1, \"box\", \"info-wrapper\"], [1, \"email\"], [\"iconSize\", \"20\", \"iconName\", \"user\", \"iconColor\", \"#3B3F46\"], [1, \"date\"], [\"iconSize\", \"20\", \"iconName\", \"calendar-days\", \"iconColor\", \"#3B3F46\"], [\"footer\", \"\"], [1, \"footer\"], [1, \"grandient-border-btn\"], [1, \"grandient-border\"], [\"label\", \"Test\", \"size\", \"medium\", \"state\", \"default\", \"iconName\", \"play\", \"iconPosition\", \"left\"], [\"label\", \"Sendback\", \"size\", \"medium\", \"state\", \"default\", \"iconName\", \"move-left\", \"iconPosition\", \"left\"], [\"label\", \"Approve\", \"variant\", \"primary\", \"size\", \"medium\", \"state\", \"default\", \"iconName\", \"Check\", \"iconPosition\", \"left\", 1, \"approval-btn\", 3, \"customStyles\", \"iconSize\"]],\n      template: function DashboardApprovalCardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"ava-approval-card\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"h2\");\n          i0.ɵɵtext(5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\");\n          i0.ɵɵelement(7, \"ava-icon\", 4);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(8, \"div\", 5)(9, \"div\", 6)(10, \"span\", 7);\n          i0.ɵɵelement(11, \"img\", 8)(12, \"ava-tag\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(13, \"ava-tag\", 10)(14, \"ava-tag\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"div\", 12)(16, \"div\", 13);\n          i0.ɵɵelement(17, \"ava-icon\", 14);\n          i0.ɵɵelementStart(18, \"span\");\n          i0.ɵɵtext(19);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"div\", 15);\n          i0.ɵɵelement(21, \"ava-icon\", 16);\n          i0.ɵɵelementStart(22, \"span\");\n          i0.ɵɵtext(23);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(24, \"div\", 17)(25, \"div\", 18)(26, \"div\", 19)(27, \"span\", 20);\n          i0.ɵɵelement(28, \"ava-button\", 21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"span\", 20);\n          i0.ɵɵelement(30, \"ava-button\", 22);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(31, \"ava-button\", 23);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(ctx.title);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"src\", ctx.avatarUrl, i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"label\", ctx.type);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(ctx.email);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.date);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"customStyles\", ctx.approvalBtnCustomStyles)(\"iconSize\", 15);\n        }\n      },\n      dependencies: [ApprovalCardComponent, AvaTagComponent, IconComponent, ButtonComponent],\n      styles: [\"#dashboard-approval-card .ava-console-approval-card-container {\\n  display: flex;\\n  flex-direction: column;\\n  row-gap: 24px;\\n}\\n  #dashboard-approval-card .ava-console-approval-card-container .ava-default-card-container .ava-default-card {\\n  box-shadow: none;\\n}\\n  #dashboard-approval-card .ava-console-approval-card-container .ava-default-card-container .header {\\n  display: flex;\\n  column-gap: 16px;\\n  align-items: flex-start;\\n  margin-bottom: 24px;\\n  justify-content: space-between;\\n}\\n  #dashboard-approval-card .ava-console-approval-card-container .ava-default-card-container .header div {\\n  display: flex;\\n  column-gap: 10px;\\n  align-items: center;\\n}\\n  #dashboard-approval-card .ava-console-approval-card-container .ava-default-card-container .header h2 {\\n  font-family: Mulish;\\n  font-weight: 600;\\n  font-style: SemiBold;\\n  font-size: 20px;\\n  color: #3B3F46;\\n  width: inherit;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  display: inline-block;\\n}\\n  #dashboard-approval-card .ava-console-approval-card-container .ava-default-card-container .header div .ava-tag {\\n  width: 56px;\\n  height: 32px;\\n  border-radius: 20px;\\n  background-color: #DC2626;\\n  display: flex;\\n  justify-content: center;\\n}\\n  #dashboard-approval-card .ava-console-approval-card-container .ava-default-card-container .header div .ava-tag .ava-tag__label {\\n  font-family: Mulish;\\n  font-weight: 600;\\n  font-style: SemiBold;\\n  font-size: 14px;\\n  color: #FFFFFF;\\n}\\n  #dashboard-approval-card .ava-console-approval-card-container .ava-default-card-container .content {\\n  margin-bottom: 24px;\\n}\\n  #dashboard-approval-card .ava-console-approval-card-container .ava-default-card-container .content .tag-wrapper {\\n  margin-bottom: 24px;\\n  display: flex;\\n  gap: 10px;\\n}\\n  #dashboard-approval-card .ava-console-approval-card-container .ava-default-card-container .content .tag-wrapper .avtar-tag-wrapper {\\n  background: #F0F1F2;\\n  border: none;\\n  border-radius: 4px;\\n  height: 32px;\\n  display: inline-flex;\\n  width: fit-content;\\n  align-items: center;\\n  padding: 4px 8px;\\n}\\n  #dashboard-approval-card .ava-console-approval-card-container .ava-default-card-container .content .tag-wrapper .avtar-tag-wrapper img {\\n  height: 24px;\\n  width: 24px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n}\\n  #dashboard-approval-card .ava-console-approval-card-container .ava-default-card-container .content .tag-wrapper .avtar-tag-wrapper .ava-tag {\\n  border: none;\\n  background: transparent;\\n}\\n  #dashboard-approval-card .ava-console-approval-card-container .ava-default-card-container .content .tag-wrapper .ava-tag {\\n  background: #F0F1F2;\\n  border: none;\\n  border-radius: 4px;\\n  height: 32px;\\n}\\n  #dashboard-approval-card .ava-console-approval-card-container .ava-default-card-container .content .tag-wrapper .ava-tag .ava-tag__label {\\n  font-family: Mulish;\\n  font-weight: 400;\\n  font-style: Regular;\\n  font-size: 14px;\\n  color: #616874;\\n}\\n  #dashboard-approval-card .ava-console-approval-card-container .ava-default-card-container .content .tag-wrapper .green-tag .ava-tag {\\n  background: #C5EBDD;\\n  border: none;\\n  border-radius: 4px;\\n  height: 32px;\\n}\\n  #dashboard-approval-card .ava-console-approval-card-container .ava-default-card-container .content .tag-wrapper .green-tag .ava-tag .ava-tag__label {\\n  font-family: Mulish;\\n  font-weight: 700;\\n  font-style: Bold;\\n  font-size: 12px;\\n  color: #33364D;\\n  text-transform: capitalize;\\n}\\n  #dashboard-approval-card .ava-console-approval-card-container .ava-default-card-container .content .info-wrapper {\\n  display: flex;\\n  justify-content: space-between;\\n}\\n  #dashboard-approval-card .ava-console-approval-card-container .ava-default-card-container .content .info-wrapper .email, \\n  #dashboard-approval-card .ava-console-approval-card-container .ava-default-card-container .content .info-wrapper .date {\\n  font-family: Mulish;\\n  font-weight: 400;\\n  font-style: Regular;\\n  font-size: 14px;\\n  color: #3B3F46;\\n  display: flex;\\n  gap: 6px;\\n}\\n  #dashboard-approval-card .ava-console-approval-card-container .ava-default-card-container .footer {\\n  display: flex;\\n  justify-content: flex-end;\\n  gap: 8px;\\n}\\n  #dashboard-approval-card .ava-console-approval-card-container .ava-default-card-container .footer .grandient-border-btn {\\n  display: flex;\\n  gap: 8px;\\n}\\n  #dashboard-approval-card .ava-console-approval-card-container .ava-default-card-container .footer .grandient-border-btn .grandient-border {\\n  background: linear-gradient(130.87deg, #0084FF 33.91%, #03BDD4 100%);\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  border-radius: 15px;\\n}\\n  #dashboard-approval-card .ava-console-approval-card-container .ava-default-card-container .footer .grandient-border-btn .ava-button {\\n  border: none;\\n  background: #FFFFFF;\\n  color: transparent;\\n  box-shadow: none;\\n}\\n  #dashboard-approval-card .ava-console-approval-card-container .ava-default-card-container .footer .grandient-border-btn .ava-button svg {\\n  stroke: #0084FF;\\n}\\n  #dashboard-approval-card .ava-console-approval-card-container .ava-default-card-container .footer .grandient-border-btn .ava-button .button-label {\\n  background: linear-gradient(130.87deg, #0084FF 33.91%, #03BDD4 100%);\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n  color: transparent;\\n  font-family: Mulish;\\n  font-weight: 500;\\n  font-size: 14px;\\n}\\n  #dashboard-approval-card .ava-console-approval-card-container .ava-default-card-container .footer .approval-btn .button-label {\\n  font-family: Mulish;\\n  font-weight: 500;\\n  font-size: 14px;\\n  color: #FFFFFF;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return DashboardApprovalCardComponent;\n})();", "map": {"version": 3, "names": ["EventEmitter", "ApprovalCardComponent", "AvaTagComponent", "IconComponent", "ButtonComponent", "DashboardApprovalCardComponent", "title", "avatarUrl", "date", "email", "type", "id", "rowData", "testClick", "sendBackClick", "approveClick", "approvalBtnCustomStyles", "onApproveClick", "emit", "onSendBackClick", "onTestClick", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "DashboardApprovalCardComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵproperty", "ɵɵsanitizeUrl", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\console\\src\\app\\shared\\components\\dashboard-approval-card\\dashboard-approval-card.component.ts", "C:\\console\\aava-ui-web\\projects\\console\\src\\app\\shared\\components\\dashboard-approval-card\\dashboard-approval-card.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, Output } from '@angular/core';\r\nimport { ApprovalCardComponent, AvaTagComponent, IconComponent, ButtonComponent } from \"@ava/play-comp-library\";\r\n\r\n@Component({\r\n  selector: 'app-dashboard-approval-card',\r\n  imports: [ApprovalCardComponent, AvaTagComponent, IconComponent, ButtonComponent],\r\n  templateUrl: './dashboard-approval-card.component.html',\r\n  styleUrl: './dashboard-approval-card.component.scss'\r\n})\r\nexport class DashboardApprovalCardComponent {\r\n  @Input() title = \"\"\r\n  @Input() avatarUrl = \"assets/images/avatar.png\"\r\n  @Input() date = \"12 May 2025\"\r\n  @Input() email = '<EMAIL>'\r\n  @Input() type = ''\r\n  @Input() id = ''\r\n  @Input() rowData: any\r\n  @Output() testClick = new EventEmitter<number>();\r\n  @Output() sendBackClick = new EventEmitter<number>();\r\n  @Output() approveClick = new EventEmitter<number>();\r\n\r\n\r\n\r\n\r\n  approvalBtnCustomStyles: Record<string, string> = {\r\n    'background': 'linear-gradient(130.87deg, #0084FF 33.91%, #03BDD4 100%)',\r\n  };\r\n\r\n  onApproveClick(rowData: any) {\r\n    this.approveClick.emit(rowData);\r\n  }\r\n\r\n  onSendBackClick(rowData: any) {\r\n    this.sendBackClick.emit(rowData);\r\n  }\r\n\r\n  onTestClick() {\r\n    this.testClick.emit(this.rowData);\r\n  }\r\n\r\n}\r\n", "<div id=\"dashboard-approval-card\">\r\n\r\n    <ava-approval-card height=\"300\">\r\n        <div header>\r\n            <div class=\"header\">\r\n                <h2>{{title}}</h2>\r\n                <div>\r\n                    <!-- <ava-tag label=\"High\" color=\"error\" size=\"sm\"></ava-tag> -->\r\n                    <ava-icon iconSize=\"25\" iconColor=\"#3B3F46\" iconName=\"ellipsis-vertical\"></ava-icon>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div content class=\"content\">\r\n            <div class=\"box tag-wrapper\">\r\n                <span class=\"avtar-tag-wrapper\">\r\n                    <img [src]=\"avatarUrl\" alt=\"Avatar\"> \r\n                    <ava-tag label=\"Ascendion\" size=\"sm\"></ava-tag>\r\n                </span>\r\n\r\n                <ava-tag label=\"Individual Agent\" size=\"sm\"></ava-tag>\r\n                <ava-tag [label]=\"type\" size=\"sm\" class=\"green-tag\"></ava-tag>\r\n\r\n            </div>\r\n\r\n            <div class=\"box info-wrapper\">\r\n                <div class=\"email\">\r\n                    <ava-icon iconSize=\"20\" iconName=\"user\" iconColor=\"#3B3F46\"></ava-icon>\r\n                    <span>{{email}}</span>\r\n                </div>\r\n                <div class=\"date\">\r\n                    <ava-icon iconSize=\"20\" iconName=\"calendar-days\" iconColor=\"#3B3F46\"></ava-icon>\r\n                    <span>{{date}}</span>\r\n                </div>\r\n\r\n            </div>\r\n        </div>\r\n        <div footer>\r\n            <div class=\"footer\">\r\n                <div class=\"grandient-border-btn\">\r\n                    <span class=\"grandient-border\">\r\n                        <ava-button label=\"Test\" size=\"medium\" state=\"default\"\r\n                            iconName=\"play\" iconPosition=\"left\">\r\n                        </ava-button>\r\n                    </span>\r\n                    <span class=\"grandient-border\">\r\n                        <ava-button label=\"Sendback\" size=\"medium\" state=\"default\"\r\n                            iconName=\"move-left\" iconPosition=\"left\">\r\n                        </ava-button>\r\n                    </span>\r\n                </div>\r\n                <ava-button label=\"Approve\" variant=\"primary\" size=\"medium\"\r\n                    class=\"approval-btn\" [customStyles]=\"approvalBtnCustomStyles\" state=\"default\" iconName=\"Check\"\r\n                    iconPosition=\"left\" [iconSize]=15>\r\n                </ava-button>\r\n            </div>\r\n        </div>\r\n    </ava-approval-card>\r\n</div>"], "mappings": "AAAA,SAAoBA,YAAY,QAAuB,eAAe;AACtE,SAASC,qBAAqB,EAAEC,eAAe,EAAEC,aAAa,EAAEC,eAAe,QAAQ,wBAAwB;;AAQ/G,WAAaC,8BAA8B;EAArC,MAAOA,8BAA8B;IAChCC,KAAK,GAAG,EAAE;IACVC,SAAS,GAAG,0BAA0B;IACtCC,IAAI,GAAG,aAAa;IACpBC,KAAK,GAAG,uBAAuB;IAC/BC,IAAI,GAAG,EAAE;IACTC,EAAE,GAAG,EAAE;IACPC,OAAO;IACNC,SAAS,GAAG,IAAIb,YAAY,EAAU;IACtCc,aAAa,GAAG,IAAId,YAAY,EAAU;IAC1Ce,YAAY,GAAG,IAAIf,YAAY,EAAU;IAKnDgB,uBAAuB,GAA2B;MAChD,YAAY,EAAE;KACf;IAEDC,cAAcA,CAACL,OAAY;MACzB,IAAI,CAACG,YAAY,CAACG,IAAI,CAACN,OAAO,CAAC;IACjC;IAEAO,eAAeA,CAACP,OAAY;MAC1B,IAAI,CAACE,aAAa,CAACI,IAAI,CAACN,OAAO,CAAC;IAClC;IAEAQ,WAAWA,CAAA;MACT,IAAI,CAACP,SAAS,CAACK,IAAI,CAAC,IAAI,CAACN,OAAO,CAAC;IACnC;;uCA7BWP,8BAA8B;IAAA;;YAA9BA,8BAA8B;MAAAgB,SAAA;MAAAC,MAAA;QAAAhB,KAAA;QAAAC,SAAA;QAAAC,IAAA;QAAAC,KAAA;QAAAC,IAAA;QAAAC,EAAA;QAAAC,OAAA;MAAA;MAAAW,OAAA;QAAAV,SAAA;QAAAC,aAAA;QAAAC,YAAA;MAAA;MAAAS,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCJ3BE,EALhB,CAAAC,cAAA,aAAkC,2BAEE,aAChB,aACY,SACZ;UAAAD,EAAA,CAAAE,MAAA,GAAS;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAClBH,EAAA,CAAAC,cAAA,UAAK;UAEDD,EAAA,CAAAI,SAAA,kBAAoF;UAGhGJ,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;UAGEH,EAFR,CAAAC,cAAA,aAA6B,aACI,eACO;UAE5BD,EADA,CAAAI,SAAA,cAAoC,kBACW;UACnDJ,EAAA,CAAAG,YAAA,EAAO;UAGPH,EADA,CAAAI,SAAA,mBAAsD,mBACQ;UAElEJ,EAAA,CAAAG,YAAA,EAAM;UAGFH,EADJ,CAAAC,cAAA,eAA8B,eACP;UACfD,EAAA,CAAAI,SAAA,oBAAuE;UACvEJ,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,IAAS;UACnBF,EADmB,CAAAG,YAAA,EAAO,EACpB;UACNH,EAAA,CAAAC,cAAA,eAAkB;UACdD,EAAA,CAAAI,SAAA,oBAAgF;UAChFJ,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,IAAQ;UAI1BF,EAJ0B,CAAAG,YAAA,EAAO,EACnB,EAEJ,EACJ;UAIMH,EAHZ,CAAAC,cAAA,eAAY,eACY,eACkB,gBACC;UAC3BD,EAAA,CAAAI,SAAA,sBAEa;UACjBJ,EAAA,CAAAG,YAAA,EAAO;UACPH,EAAA,CAAAC,cAAA,gBAA+B;UAC3BD,EAAA,CAAAI,SAAA,sBAEa;UAErBJ,EADI,CAAAG,YAAA,EAAO,EACL;UACNH,EAAA,CAAAI,SAAA,sBAGa;UAI7BJ,EAHY,CAAAG,YAAA,EAAM,EACJ,EACU,EAClB;;;UApDcH,EAAA,CAAAK,SAAA,GAAS;UAATL,EAAA,CAAAM,iBAAA,CAAAP,GAAA,CAAAxB,KAAA,CAAS;UAUJyB,EAAA,CAAAK,SAAA,GAAiB;UAAjBL,EAAA,CAAAO,UAAA,QAAAR,GAAA,CAAAvB,SAAA,EAAAwB,EAAA,CAAAQ,aAAA,CAAiB;UAKjBR,EAAA,CAAAK,SAAA,GAAc;UAAdL,EAAA,CAAAO,UAAA,UAAAR,GAAA,CAAApB,IAAA,CAAc;UAObqB,EAAA,CAAAK,SAAA,GAAS;UAATL,EAAA,CAAAM,iBAAA,CAAAP,GAAA,CAAArB,KAAA,CAAS;UAITsB,EAAA,CAAAK,SAAA,GAAQ;UAARL,EAAA,CAAAM,iBAAA,CAAAP,GAAA,CAAAtB,IAAA,CAAQ;UAoBOuB,EAAA,CAAAK,SAAA,GAAwC;UACzCL,EADC,CAAAO,UAAA,iBAAAR,GAAA,CAAAd,uBAAA,CAAwC,gBAC5B;;;qBD/CzCf,qBAAqB,EAAEC,eAAe,EAAEC,aAAa,EAAEC,eAAe;MAAAoC,MAAA;IAAA;;SAIrEnC,8BAA8B;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}