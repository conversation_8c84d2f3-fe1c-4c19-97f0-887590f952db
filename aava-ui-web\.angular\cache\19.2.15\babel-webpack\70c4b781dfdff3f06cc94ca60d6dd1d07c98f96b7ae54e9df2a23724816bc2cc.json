{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { $, addDisposableListener, EventType, getActiveElement, getWindow, isAncestor, isHTMLElement } from '../../../base/browser/dom.js';\nimport { StandardMouseEvent } from '../../../base/browser/mouseEvent.js';\nimport { Menu } from '../../../base/browser/ui/menu/menu.js';\nimport { ActionRunner } from '../../../base/common/actions.js';\nimport { isCancellationError } from '../../../base/common/errors.js';\nimport { combinedDisposable, DisposableStore } from '../../../base/common/lifecycle.js';\nimport { defaultMenuStyles } from '../../theme/browser/defaultStyles.js';\nexport class ContextMenuHandler {\n  constructor(contextViewService, telemetryService, notificationService, keybindingService) {\n    this.contextViewService = contextViewService;\n    this.telemetryService = telemetryService;\n    this.notificationService = notificationService;\n    this.keybindingService = keybindingService;\n    this.focusToReturn = null;\n    this.lastContainer = null;\n    this.block = null;\n    this.blockDisposable = null;\n    this.options = {\n      blockMouse: true\n    };\n  }\n  configure(options) {\n    this.options = options;\n  }\n  showContextMenu(delegate) {\n    const actions = delegate.getActions();\n    if (!actions.length) {\n      return; // Don't render an empty context menu\n    }\n    this.focusToReturn = getActiveElement();\n    let menu;\n    const shadowRootElement = isHTMLElement(delegate.domForShadowRoot) ? delegate.domForShadowRoot : undefined;\n    this.contextViewService.showContextView({\n      getAnchor: () => delegate.getAnchor(),\n      canRelayout: false,\n      anchorAlignment: delegate.anchorAlignment,\n      anchorAxisAlignment: delegate.anchorAxisAlignment,\n      render: container => {\n        this.lastContainer = container;\n        const className = delegate.getMenuClassName ? delegate.getMenuClassName() : '';\n        if (className) {\n          container.className += ' ' + className;\n        }\n        // Render invisible div to block mouse interaction in the rest of the UI\n        if (this.options.blockMouse) {\n          this.block = container.appendChild($('.context-view-block'));\n          this.block.style.position = 'fixed';\n          this.block.style.cursor = 'initial';\n          this.block.style.left = '0';\n          this.block.style.top = '0';\n          this.block.style.width = '100%';\n          this.block.style.height = '100%';\n          this.block.style.zIndex = '-1';\n          this.blockDisposable?.dispose();\n          this.blockDisposable = addDisposableListener(this.block, EventType.MOUSE_DOWN, e => e.stopPropagation());\n        }\n        const menuDisposables = new DisposableStore();\n        const actionRunner = delegate.actionRunner || new ActionRunner();\n        actionRunner.onWillRun(evt => this.onActionRun(evt, !delegate.skipTelemetry), this, menuDisposables);\n        actionRunner.onDidRun(this.onDidActionRun, this, menuDisposables);\n        menu = new Menu(container, actions, {\n          actionViewItemProvider: delegate.getActionViewItem,\n          context: delegate.getActionsContext ? delegate.getActionsContext() : null,\n          actionRunner,\n          getKeyBinding: delegate.getKeyBinding ? delegate.getKeyBinding : action => this.keybindingService.lookupKeybinding(action.id)\n        }, defaultMenuStyles);\n        menu.onDidCancel(() => this.contextViewService.hideContextView(true), null, menuDisposables);\n        menu.onDidBlur(() => this.contextViewService.hideContextView(true), null, menuDisposables);\n        const targetWindow = getWindow(container);\n        menuDisposables.add(addDisposableListener(targetWindow, EventType.BLUR, () => this.contextViewService.hideContextView(true)));\n        menuDisposables.add(addDisposableListener(targetWindow, EventType.MOUSE_DOWN, e => {\n          if (e.defaultPrevented) {\n            return;\n          }\n          const event = new StandardMouseEvent(targetWindow, e);\n          let element = event.target;\n          // Don't do anything as we are likely creating a context menu\n          if (event.rightButton) {\n            return;\n          }\n          while (element) {\n            if (element === container) {\n              return;\n            }\n            element = element.parentElement;\n          }\n          this.contextViewService.hideContextView(true);\n        }));\n        return combinedDisposable(menuDisposables, menu);\n      },\n      focus: () => {\n        menu?.focus(!!delegate.autoSelectFirstItem);\n      },\n      onHide: didCancel => {\n        delegate.onHide?.(!!didCancel);\n        if (this.block) {\n          this.block.remove();\n          this.block = null;\n        }\n        this.blockDisposable?.dispose();\n        this.blockDisposable = null;\n        if (!!this.lastContainer && (getActiveElement() === this.lastContainer || isAncestor(getActiveElement(), this.lastContainer))) {\n          this.focusToReturn?.focus();\n        }\n        this.lastContainer = null;\n      }\n    }, shadowRootElement, !!shadowRootElement);\n  }\n  onActionRun(e, logTelemetry) {\n    if (logTelemetry) {\n      this.telemetryService.publicLog2('workbenchActionExecuted', {\n        id: e.action.id,\n        from: 'contextMenu'\n      });\n    }\n    this.contextViewService.hideContextView(false);\n  }\n  onDidActionRun(e) {\n    if (e.error && !isCancellationError(e.error)) {\n      this.notificationService.error(e.error);\n    }\n  }\n}", "map": {"version": 3, "names": ["$", "addDisposableListener", "EventType", "getActiveElement", "getWindow", "isAncestor", "isHTMLElement", "StandardMouseEvent", "<PERSON><PERSON>", "ActionRunner", "isCancellationError", "combinedDisposable", "DisposableStore", "defaultMenuStyles", "ContextMenuHandler", "constructor", "contextViewService", "telemetryService", "notificationService", "keybindingService", "focusToReturn", "lastC<PERSON>r", "block", "blockDisposable", "options", "blockMouse", "configure", "showContextMenu", "delegate", "actions", "getActions", "length", "menu", "shadowRootElement", "domForShadowRoot", "undefined", "showContextView", "getAnchor", "canRelayout", "anchorAlignment", "anchorAxisAlignment", "render", "container", "className", "getMenuClassName", "append<PERSON><PERSON><PERSON>", "style", "position", "cursor", "left", "top", "width", "height", "zIndex", "dispose", "MOUSE_DOWN", "e", "stopPropagation", "menuDisposables", "actionRunner", "onWillRun", "evt", "onActionRun", "skipTelemetry", "onDidRun", "onDidActionRun", "actionViewItemProvider", "getActionViewItem", "context", "getActionsContext", "getKeyBinding", "action", "lookupKeybinding", "id", "onDidCancel", "hideContextView", "onDidBlur", "targetWindow", "add", "BLUR", "defaultPrevented", "event", "element", "target", "rightB<PERSON>on", "parentElement", "focus", "autoSelectFirstItem", "onHide", "didCancel", "remove", "logTelemetry", "publicLog2", "from", "error"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/platform/contextview/browser/contextMenuHandler.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { $, addDisposableListener, EventType, getActiveElement, getWindow, isAncestor, isHTMLElement } from '../../../base/browser/dom.js';\nimport { StandardMouseEvent } from '../../../base/browser/mouseEvent.js';\nimport { Menu } from '../../../base/browser/ui/menu/menu.js';\nimport { ActionRunner } from '../../../base/common/actions.js';\nimport { isCancellationError } from '../../../base/common/errors.js';\nimport { combinedDisposable, DisposableStore } from '../../../base/common/lifecycle.js';\nimport { defaultMenuStyles } from '../../theme/browser/defaultStyles.js';\nexport class ContextMenuHandler {\n    constructor(contextViewService, telemetryService, notificationService, keybindingService) {\n        this.contextViewService = contextViewService;\n        this.telemetryService = telemetryService;\n        this.notificationService = notificationService;\n        this.keybindingService = keybindingService;\n        this.focusToReturn = null;\n        this.lastContainer = null;\n        this.block = null;\n        this.blockDisposable = null;\n        this.options = { blockMouse: true };\n    }\n    configure(options) {\n        this.options = options;\n    }\n    showContextMenu(delegate) {\n        const actions = delegate.getActions();\n        if (!actions.length) {\n            return; // Don't render an empty context menu\n        }\n        this.focusToReturn = getActiveElement();\n        let menu;\n        const shadowRootElement = isHTMLElement(delegate.domForShadowRoot) ? delegate.domForShadowRoot : undefined;\n        this.contextViewService.showContextView({\n            getAnchor: () => delegate.getAnchor(),\n            canRelayout: false,\n            anchorAlignment: delegate.anchorAlignment,\n            anchorAxisAlignment: delegate.anchorAxisAlignment,\n            render: (container) => {\n                this.lastContainer = container;\n                const className = delegate.getMenuClassName ? delegate.getMenuClassName() : '';\n                if (className) {\n                    container.className += ' ' + className;\n                }\n                // Render invisible div to block mouse interaction in the rest of the UI\n                if (this.options.blockMouse) {\n                    this.block = container.appendChild($('.context-view-block'));\n                    this.block.style.position = 'fixed';\n                    this.block.style.cursor = 'initial';\n                    this.block.style.left = '0';\n                    this.block.style.top = '0';\n                    this.block.style.width = '100%';\n                    this.block.style.height = '100%';\n                    this.block.style.zIndex = '-1';\n                    this.blockDisposable?.dispose();\n                    this.blockDisposable = addDisposableListener(this.block, EventType.MOUSE_DOWN, e => e.stopPropagation());\n                }\n                const menuDisposables = new DisposableStore();\n                const actionRunner = delegate.actionRunner || new ActionRunner();\n                actionRunner.onWillRun(evt => this.onActionRun(evt, !delegate.skipTelemetry), this, menuDisposables);\n                actionRunner.onDidRun(this.onDidActionRun, this, menuDisposables);\n                menu = new Menu(container, actions, {\n                    actionViewItemProvider: delegate.getActionViewItem,\n                    context: delegate.getActionsContext ? delegate.getActionsContext() : null,\n                    actionRunner,\n                    getKeyBinding: delegate.getKeyBinding ? delegate.getKeyBinding : action => this.keybindingService.lookupKeybinding(action.id)\n                }, defaultMenuStyles);\n                menu.onDidCancel(() => this.contextViewService.hideContextView(true), null, menuDisposables);\n                menu.onDidBlur(() => this.contextViewService.hideContextView(true), null, menuDisposables);\n                const targetWindow = getWindow(container);\n                menuDisposables.add(addDisposableListener(targetWindow, EventType.BLUR, () => this.contextViewService.hideContextView(true)));\n                menuDisposables.add(addDisposableListener(targetWindow, EventType.MOUSE_DOWN, (e) => {\n                    if (e.defaultPrevented) {\n                        return;\n                    }\n                    const event = new StandardMouseEvent(targetWindow, e);\n                    let element = event.target;\n                    // Don't do anything as we are likely creating a context menu\n                    if (event.rightButton) {\n                        return;\n                    }\n                    while (element) {\n                        if (element === container) {\n                            return;\n                        }\n                        element = element.parentElement;\n                    }\n                    this.contextViewService.hideContextView(true);\n                }));\n                return combinedDisposable(menuDisposables, menu);\n            },\n            focus: () => {\n                menu?.focus(!!delegate.autoSelectFirstItem);\n            },\n            onHide: (didCancel) => {\n                delegate.onHide?.(!!didCancel);\n                if (this.block) {\n                    this.block.remove();\n                    this.block = null;\n                }\n                this.blockDisposable?.dispose();\n                this.blockDisposable = null;\n                if (!!this.lastContainer && (getActiveElement() === this.lastContainer || isAncestor(getActiveElement(), this.lastContainer))) {\n                    this.focusToReturn?.focus();\n                }\n                this.lastContainer = null;\n            }\n        }, shadowRootElement, !!shadowRootElement);\n    }\n    onActionRun(e, logTelemetry) {\n        if (logTelemetry) {\n            this.telemetryService.publicLog2('workbenchActionExecuted', { id: e.action.id, from: 'contextMenu' });\n        }\n        this.contextViewService.hideContextView(false);\n    }\n    onDidActionRun(e) {\n        if (e.error && !isCancellationError(e.error)) {\n            this.notificationService.error(e.error);\n        }\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,CAAC,EAAEC,qBAAqB,EAAEC,SAAS,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,UAAU,EAAEC,aAAa,QAAQ,8BAA8B;AAC1I,SAASC,kBAAkB,QAAQ,qCAAqC;AACxE,SAASC,IAAI,QAAQ,uCAAuC;AAC5D,SAASC,YAAY,QAAQ,iCAAiC;AAC9D,SAASC,mBAAmB,QAAQ,gCAAgC;AACpE,SAASC,kBAAkB,EAAEC,eAAe,QAAQ,mCAAmC;AACvF,SAASC,iBAAiB,QAAQ,sCAAsC;AACxE,OAAO,MAAMC,kBAAkB,CAAC;EAC5BC,WAAWA,CAACC,kBAAkB,EAAEC,gBAAgB,EAAEC,mBAAmB,EAAEC,iBAAiB,EAAE;IACtF,IAAI,CAACH,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACC,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,KAAK,GAAG,IAAI;IACjB,IAAI,CAACC,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACC,OAAO,GAAG;MAAEC,UAAU,EAAE;IAAK,CAAC;EACvC;EACAC,SAASA,CAACF,OAAO,EAAE;IACf,IAAI,CAACA,OAAO,GAAGA,OAAO;EAC1B;EACAG,eAAeA,CAACC,QAAQ,EAAE;IACtB,MAAMC,OAAO,GAAGD,QAAQ,CAACE,UAAU,CAAC,CAAC;IACrC,IAAI,CAACD,OAAO,CAACE,MAAM,EAAE;MACjB,OAAO,CAAC;IACZ;IACA,IAAI,CAACX,aAAa,GAAGjB,gBAAgB,CAAC,CAAC;IACvC,IAAI6B,IAAI;IACR,MAAMC,iBAAiB,GAAG3B,aAAa,CAACsB,QAAQ,CAACM,gBAAgB,CAAC,GAAGN,QAAQ,CAACM,gBAAgB,GAAGC,SAAS;IAC1G,IAAI,CAACnB,kBAAkB,CAACoB,eAAe,CAAC;MACpCC,SAAS,EAAEA,CAAA,KAAMT,QAAQ,CAACS,SAAS,CAAC,CAAC;MACrCC,WAAW,EAAE,KAAK;MAClBC,eAAe,EAAEX,QAAQ,CAACW,eAAe;MACzCC,mBAAmB,EAAEZ,QAAQ,CAACY,mBAAmB;MACjDC,MAAM,EAAGC,SAAS,IAAK;QACnB,IAAI,CAACrB,aAAa,GAAGqB,SAAS;QAC9B,MAAMC,SAAS,GAAGf,QAAQ,CAACgB,gBAAgB,GAAGhB,QAAQ,CAACgB,gBAAgB,CAAC,CAAC,GAAG,EAAE;QAC9E,IAAID,SAAS,EAAE;UACXD,SAAS,CAACC,SAAS,IAAI,GAAG,GAAGA,SAAS;QAC1C;QACA;QACA,IAAI,IAAI,CAACnB,OAAO,CAACC,UAAU,EAAE;UACzB,IAAI,CAACH,KAAK,GAAGoB,SAAS,CAACG,WAAW,CAAC7C,CAAC,CAAC,qBAAqB,CAAC,CAAC;UAC5D,IAAI,CAACsB,KAAK,CAACwB,KAAK,CAACC,QAAQ,GAAG,OAAO;UACnC,IAAI,CAACzB,KAAK,CAACwB,KAAK,CAACE,MAAM,GAAG,SAAS;UACnC,IAAI,CAAC1B,KAAK,CAACwB,KAAK,CAACG,IAAI,GAAG,GAAG;UAC3B,IAAI,CAAC3B,KAAK,CAACwB,KAAK,CAACI,GAAG,GAAG,GAAG;UAC1B,IAAI,CAAC5B,KAAK,CAACwB,KAAK,CAACK,KAAK,GAAG,MAAM;UAC/B,IAAI,CAAC7B,KAAK,CAACwB,KAAK,CAACM,MAAM,GAAG,MAAM;UAChC,IAAI,CAAC9B,KAAK,CAACwB,KAAK,CAACO,MAAM,GAAG,IAAI;UAC9B,IAAI,CAAC9B,eAAe,EAAE+B,OAAO,CAAC,CAAC;UAC/B,IAAI,CAAC/B,eAAe,GAAGtB,qBAAqB,CAAC,IAAI,CAACqB,KAAK,EAAEpB,SAAS,CAACqD,UAAU,EAAEC,CAAC,IAAIA,CAAC,CAACC,eAAe,CAAC,CAAC,CAAC;QAC5G;QACA,MAAMC,eAAe,GAAG,IAAI9C,eAAe,CAAC,CAAC;QAC7C,MAAM+C,YAAY,GAAG/B,QAAQ,CAAC+B,YAAY,IAAI,IAAIlD,YAAY,CAAC,CAAC;QAChEkD,YAAY,CAACC,SAAS,CAACC,GAAG,IAAI,IAAI,CAACC,WAAW,CAACD,GAAG,EAAE,CAACjC,QAAQ,CAACmC,aAAa,CAAC,EAAE,IAAI,EAAEL,eAAe,CAAC;QACpGC,YAAY,CAACK,QAAQ,CAAC,IAAI,CAACC,cAAc,EAAE,IAAI,EAAEP,eAAe,CAAC;QACjE1B,IAAI,GAAG,IAAIxB,IAAI,CAACkC,SAAS,EAAEb,OAAO,EAAE;UAChCqC,sBAAsB,EAAEtC,QAAQ,CAACuC,iBAAiB;UAClDC,OAAO,EAAExC,QAAQ,CAACyC,iBAAiB,GAAGzC,QAAQ,CAACyC,iBAAiB,CAAC,CAAC,GAAG,IAAI;UACzEV,YAAY;UACZW,aAAa,EAAE1C,QAAQ,CAAC0C,aAAa,GAAG1C,QAAQ,CAAC0C,aAAa,GAAGC,MAAM,IAAI,IAAI,CAACpD,iBAAiB,CAACqD,gBAAgB,CAACD,MAAM,CAACE,EAAE;QAChI,CAAC,EAAE5D,iBAAiB,CAAC;QACrBmB,IAAI,CAAC0C,WAAW,CAAC,MAAM,IAAI,CAAC1D,kBAAkB,CAAC2D,eAAe,CAAC,IAAI,CAAC,EAAE,IAAI,EAAEjB,eAAe,CAAC;QAC5F1B,IAAI,CAAC4C,SAAS,CAAC,MAAM,IAAI,CAAC5D,kBAAkB,CAAC2D,eAAe,CAAC,IAAI,CAAC,EAAE,IAAI,EAAEjB,eAAe,CAAC;QAC1F,MAAMmB,YAAY,GAAGzE,SAAS,CAACsC,SAAS,CAAC;QACzCgB,eAAe,CAACoB,GAAG,CAAC7E,qBAAqB,CAAC4E,YAAY,EAAE3E,SAAS,CAAC6E,IAAI,EAAE,MAAM,IAAI,CAAC/D,kBAAkB,CAAC2D,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC;QAC7HjB,eAAe,CAACoB,GAAG,CAAC7E,qBAAqB,CAAC4E,YAAY,EAAE3E,SAAS,CAACqD,UAAU,EAAGC,CAAC,IAAK;UACjF,IAAIA,CAAC,CAACwB,gBAAgB,EAAE;YACpB;UACJ;UACA,MAAMC,KAAK,GAAG,IAAI1E,kBAAkB,CAACsE,YAAY,EAAErB,CAAC,CAAC;UACrD,IAAI0B,OAAO,GAAGD,KAAK,CAACE,MAAM;UAC1B;UACA,IAAIF,KAAK,CAACG,WAAW,EAAE;YACnB;UACJ;UACA,OAAOF,OAAO,EAAE;YACZ,IAAIA,OAAO,KAAKxC,SAAS,EAAE;cACvB;YACJ;YACAwC,OAAO,GAAGA,OAAO,CAACG,aAAa;UACnC;UACA,IAAI,CAACrE,kBAAkB,CAAC2D,eAAe,CAAC,IAAI,CAAC;QACjD,CAAC,CAAC,CAAC;QACH,OAAOhE,kBAAkB,CAAC+C,eAAe,EAAE1B,IAAI,CAAC;MACpD,CAAC;MACDsD,KAAK,EAAEA,CAAA,KAAM;QACTtD,IAAI,EAAEsD,KAAK,CAAC,CAAC,CAAC1D,QAAQ,CAAC2D,mBAAmB,CAAC;MAC/C,CAAC;MACDC,MAAM,EAAGC,SAAS,IAAK;QACnB7D,QAAQ,CAAC4D,MAAM,GAAG,CAAC,CAACC,SAAS,CAAC;QAC9B,IAAI,IAAI,CAACnE,KAAK,EAAE;UACZ,IAAI,CAACA,KAAK,CAACoE,MAAM,CAAC,CAAC;UACnB,IAAI,CAACpE,KAAK,GAAG,IAAI;QACrB;QACA,IAAI,CAACC,eAAe,EAAE+B,OAAO,CAAC,CAAC;QAC/B,IAAI,CAAC/B,eAAe,GAAG,IAAI;QAC3B,IAAI,CAAC,CAAC,IAAI,CAACF,aAAa,KAAKlB,gBAAgB,CAAC,CAAC,KAAK,IAAI,CAACkB,aAAa,IAAIhB,UAAU,CAACF,gBAAgB,CAAC,CAAC,EAAE,IAAI,CAACkB,aAAa,CAAC,CAAC,EAAE;UAC3H,IAAI,CAACD,aAAa,EAAEkE,KAAK,CAAC,CAAC;QAC/B;QACA,IAAI,CAACjE,aAAa,GAAG,IAAI;MAC7B;IACJ,CAAC,EAAEY,iBAAiB,EAAE,CAAC,CAACA,iBAAiB,CAAC;EAC9C;EACA6B,WAAWA,CAACN,CAAC,EAAEmC,YAAY,EAAE;IACzB,IAAIA,YAAY,EAAE;MACd,IAAI,CAAC1E,gBAAgB,CAAC2E,UAAU,CAAC,yBAAyB,EAAE;QAAEnB,EAAE,EAAEjB,CAAC,CAACe,MAAM,CAACE,EAAE;QAAEoB,IAAI,EAAE;MAAc,CAAC,CAAC;IACzG;IACA,IAAI,CAAC7E,kBAAkB,CAAC2D,eAAe,CAAC,KAAK,CAAC;EAClD;EACAV,cAAcA,CAACT,CAAC,EAAE;IACd,IAAIA,CAAC,CAACsC,KAAK,IAAI,CAACpF,mBAAmB,CAAC8C,CAAC,CAACsC,KAAK,CAAC,EAAE;MAC1C,IAAI,CAAC5E,mBAAmB,CAAC4E,KAAK,CAACtC,CAAC,CAACsC,KAAK,CAAC;IAC3C;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}