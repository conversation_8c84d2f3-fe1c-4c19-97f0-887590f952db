{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as dom from '../../dom.js';\nimport { DomEmitter } from '../../event.js';\nimport { StandardKeyboardEvent } from '../../keyboardEvent.js';\nimport { renderMarkdown } from '../../markdownRenderer.js';\nimport { getBaseLayerHoverDelegate } from '../hover/hoverDelegate2.js';\nimport { getDefaultHoverDelegate } from '../hover/hoverDelegateFactory.js';\nimport { List } from '../list/listWidget.js';\nimport * as arrays from '../../../common/arrays.js';\nimport { Emitter, Event } from '../../../common/event.js';\nimport { KeyCodeUtils } from '../../../common/keyCodes.js';\nimport { Disposable } from '../../../common/lifecycle.js';\nimport { isMacintosh } from '../../../common/platform.js';\nimport './selectBoxCustom.css';\nimport { localize } from '../../../../nls.js';\nconst $ = dom.$;\nconst SELECT_OPTION_ENTRY_TEMPLATE_ID = 'selectOption.entry.template';\nclass SelectListRenderer {\n  get templateId() {\n    return SELECT_OPTION_ENTRY_TEMPLATE_ID;\n  }\n  renderTemplate(container) {\n    const data = Object.create(null);\n    data.root = container;\n    data.text = dom.append(container, $('.option-text'));\n    data.detail = dom.append(container, $('.option-detail'));\n    data.decoratorRight = dom.append(container, $('.option-decorator-right'));\n    return data;\n  }\n  renderElement(element, index, templateData) {\n    const data = templateData;\n    const text = element.text;\n    const detail = element.detail;\n    const decoratorRight = element.decoratorRight;\n    const isDisabled = element.isDisabled;\n    data.text.textContent = text;\n    data.detail.textContent = !!detail ? detail : '';\n    data.decoratorRight.innerText = !!decoratorRight ? decoratorRight : '';\n    // pseudo-select disabled option\n    if (isDisabled) {\n      data.root.classList.add('option-disabled');\n    } else {\n      // Make sure we do class removal from prior template rendering\n      data.root.classList.remove('option-disabled');\n    }\n  }\n  disposeTemplate(_templateData) {\n    // noop\n  }\n}\nexport let SelectBoxList = /*#__PURE__*/(() => {\n  class SelectBoxList extends Disposable {\n    static {\n      this.DEFAULT_DROPDOWN_MINIMUM_BOTTOM_MARGIN = 32;\n    }\n    static {\n      this.DEFAULT_DROPDOWN_MINIMUM_TOP_MARGIN = 2;\n    }\n    static {\n      this.DEFAULT_MINIMUM_VISIBLE_OPTIONS = 3;\n    }\n    constructor(options, selected, contextViewProvider, styles, selectBoxOptions) {\n      super();\n      this.options = [];\n      this._currentSelection = 0;\n      this._hasDetails = false;\n      this._skipLayout = false;\n      this._sticky = false; // for dev purposes only\n      this._isVisible = false;\n      this.styles = styles;\n      this.selectBoxOptions = selectBoxOptions || Object.create(null);\n      if (typeof this.selectBoxOptions.minBottomMargin !== 'number') {\n        this.selectBoxOptions.minBottomMargin = SelectBoxList.DEFAULT_DROPDOWN_MINIMUM_BOTTOM_MARGIN;\n      } else if (this.selectBoxOptions.minBottomMargin < 0) {\n        this.selectBoxOptions.minBottomMargin = 0;\n      }\n      this.selectElement = document.createElement('select');\n      // Use custom CSS vars for padding calculation\n      this.selectElement.className = 'monaco-select-box monaco-select-box-dropdown-padding';\n      if (typeof this.selectBoxOptions.ariaLabel === 'string') {\n        this.selectElement.setAttribute('aria-label', this.selectBoxOptions.ariaLabel);\n      }\n      if (typeof this.selectBoxOptions.ariaDescription === 'string') {\n        this.selectElement.setAttribute('aria-description', this.selectBoxOptions.ariaDescription);\n      }\n      this._onDidSelect = new Emitter();\n      this._register(this._onDidSelect);\n      this.registerListeners();\n      this.constructSelectDropDown(contextViewProvider);\n      this.selected = selected || 0;\n      if (options) {\n        this.setOptions(options, selected);\n      }\n      this.initStyleSheet();\n    }\n    setTitle(title) {\n      if (!this._hover && title) {\n        this._hover = this._register(getBaseLayerHoverDelegate().setupManagedHover(getDefaultHoverDelegate('mouse'), this.selectElement, title));\n      } else if (this._hover) {\n        this._hover.update(title);\n      }\n    }\n    // IDelegate - List renderer\n    getHeight() {\n      return 22;\n    }\n    getTemplateId() {\n      return SELECT_OPTION_ENTRY_TEMPLATE_ID;\n    }\n    constructSelectDropDown(contextViewProvider) {\n      // SetUp ContextView container to hold select Dropdown\n      this.contextViewProvider = contextViewProvider;\n      this.selectDropDownContainer = dom.$('.monaco-select-box-dropdown-container');\n      // Use custom CSS vars for padding calculation (shared with parent select)\n      this.selectDropDownContainer.classList.add('monaco-select-box-dropdown-padding');\n      // Setup container for select option details\n      this.selectionDetailsPane = dom.append(this.selectDropDownContainer, $('.select-box-details-pane'));\n      // Create span flex box item/div we can measure and control\n      const widthControlOuterDiv = dom.append(this.selectDropDownContainer, $('.select-box-dropdown-container-width-control'));\n      const widthControlInnerDiv = dom.append(widthControlOuterDiv, $('.width-control-div'));\n      this.widthControlElement = document.createElement('span');\n      this.widthControlElement.className = 'option-text-width-control';\n      dom.append(widthControlInnerDiv, this.widthControlElement);\n      // Always default to below position\n      this._dropDownPosition = 0 /* AnchorPosition.BELOW */;\n      // Inline stylesheet for themes\n      this.styleElement = dom.createStyleSheet(this.selectDropDownContainer);\n      // Prevent dragging of dropdown #114329\n      this.selectDropDownContainer.setAttribute('draggable', 'true');\n      this._register(dom.addDisposableListener(this.selectDropDownContainer, dom.EventType.DRAG_START, e => {\n        dom.EventHelper.stop(e, true);\n      }));\n    }\n    registerListeners() {\n      // Parent native select keyboard listeners\n      this._register(dom.addStandardDisposableListener(this.selectElement, 'change', e => {\n        this.selected = e.target.selectedIndex;\n        this._onDidSelect.fire({\n          index: e.target.selectedIndex,\n          selected: e.target.value\n        });\n        if (!!this.options[this.selected] && !!this.options[this.selected].text) {\n          this.setTitle(this.options[this.selected].text);\n        }\n      }));\n      // Have to implement both keyboard and mouse controllers to handle disabled options\n      // Intercept mouse events to override normal select actions on parents\n      this._register(dom.addDisposableListener(this.selectElement, dom.EventType.CLICK, e => {\n        dom.EventHelper.stop(e);\n        if (this._isVisible) {\n          this.hideSelectDropDown(true);\n        } else {\n          this.showSelectDropDown();\n        }\n      }));\n      this._register(dom.addDisposableListener(this.selectElement, dom.EventType.MOUSE_DOWN, e => {\n        dom.EventHelper.stop(e);\n      }));\n      // Intercept touch events\n      // The following implementation is slightly different from the mouse event handlers above.\n      // Use the following helper variable, otherwise the list flickers.\n      let listIsVisibleOnTouchStart;\n      this._register(dom.addDisposableListener(this.selectElement, 'touchstart', e => {\n        listIsVisibleOnTouchStart = this._isVisible;\n      }));\n      this._register(dom.addDisposableListener(this.selectElement, 'touchend', e => {\n        dom.EventHelper.stop(e);\n        if (listIsVisibleOnTouchStart) {\n          this.hideSelectDropDown(true);\n        } else {\n          this.showSelectDropDown();\n        }\n      }));\n      // Intercept keyboard handling\n      this._register(dom.addDisposableListener(this.selectElement, dom.EventType.KEY_DOWN, e => {\n        const event = new StandardKeyboardEvent(e);\n        let showDropDown = false;\n        // Create and drop down select list on keyboard select\n        if (isMacintosh) {\n          if (event.keyCode === 18 /* KeyCode.DownArrow */ || event.keyCode === 16 /* KeyCode.UpArrow */ || event.keyCode === 10 /* KeyCode.Space */ || event.keyCode === 3 /* KeyCode.Enter */) {\n            showDropDown = true;\n          }\n        } else {\n          if (event.keyCode === 18 /* KeyCode.DownArrow */ && event.altKey || event.keyCode === 16 /* KeyCode.UpArrow */ && event.altKey || event.keyCode === 10 /* KeyCode.Space */ || event.keyCode === 3 /* KeyCode.Enter */) {\n            showDropDown = true;\n          }\n        }\n        if (showDropDown) {\n          this.showSelectDropDown();\n          dom.EventHelper.stop(e, true);\n        }\n      }));\n    }\n    get onDidSelect() {\n      return this._onDidSelect.event;\n    }\n    setOptions(options, selected) {\n      if (!arrays.equals(this.options, options)) {\n        this.options = options;\n        this.selectElement.options.length = 0;\n        this._hasDetails = false;\n        this._cachedMaxDetailsHeight = undefined;\n        this.options.forEach((option, index) => {\n          this.selectElement.add(this.createOption(option.text, index, option.isDisabled));\n          if (typeof option.description === 'string') {\n            this._hasDetails = true;\n          }\n        });\n      }\n      if (selected !== undefined) {\n        this.select(selected);\n        // Set current = selected since this is not necessarily a user exit\n        this._currentSelection = this.selected;\n      }\n    }\n    setOptionsList() {\n      // Mirror options in drop-down\n      // Populate select list for non-native select mode\n      this.selectList?.splice(0, this.selectList.length, this.options);\n    }\n    select(index) {\n      if (index >= 0 && index < this.options.length) {\n        this.selected = index;\n      } else if (index > this.options.length - 1) {\n        // Adjust index to end of list\n        // This could make client out of sync with the select\n        this.select(this.options.length - 1);\n      } else if (this.selected < 0) {\n        this.selected = 0;\n      }\n      this.selectElement.selectedIndex = this.selected;\n      if (!!this.options[this.selected] && !!this.options[this.selected].text) {\n        this.setTitle(this.options[this.selected].text);\n      }\n    }\n    focus() {\n      if (this.selectElement) {\n        this.selectElement.tabIndex = 0;\n        this.selectElement.focus();\n      }\n    }\n    blur() {\n      if (this.selectElement) {\n        this.selectElement.tabIndex = -1;\n        this.selectElement.blur();\n      }\n    }\n    setFocusable(focusable) {\n      this.selectElement.tabIndex = focusable ? 0 : -1;\n    }\n    render(container) {\n      this.container = container;\n      container.classList.add('select-container');\n      container.appendChild(this.selectElement);\n      this.styleSelectElement();\n    }\n    initStyleSheet() {\n      const content = [];\n      // Style non-native select mode\n      if (this.styles.listFocusBackground) {\n        content.push(`.monaco-select-box-dropdown-container > .select-box-dropdown-list-container .monaco-list .monaco-list-row.focused { background-color: ${this.styles.listFocusBackground} !important; }`);\n      }\n      if (this.styles.listFocusForeground) {\n        content.push(`.monaco-select-box-dropdown-container > .select-box-dropdown-list-container .monaco-list .monaco-list-row.focused { color: ${this.styles.listFocusForeground} !important; }`);\n      }\n      if (this.styles.decoratorRightForeground) {\n        content.push(`.monaco-select-box-dropdown-container > .select-box-dropdown-list-container .monaco-list .monaco-list-row:not(.focused) .option-decorator-right { color: ${this.styles.decoratorRightForeground}; }`);\n      }\n      if (this.styles.selectBackground && this.styles.selectBorder && this.styles.selectBorder !== this.styles.selectBackground) {\n        content.push(`.monaco-select-box-dropdown-container { border: 1px solid ${this.styles.selectBorder} } `);\n        content.push(`.monaco-select-box-dropdown-container > .select-box-details-pane.border-top { border-top: 1px solid ${this.styles.selectBorder} } `);\n        content.push(`.monaco-select-box-dropdown-container > .select-box-details-pane.border-bottom { border-bottom: 1px solid ${this.styles.selectBorder} } `);\n      } else if (this.styles.selectListBorder) {\n        content.push(`.monaco-select-box-dropdown-container > .select-box-details-pane.border-top { border-top: 1px solid ${this.styles.selectListBorder} } `);\n        content.push(`.monaco-select-box-dropdown-container > .select-box-details-pane.border-bottom { border-bottom: 1px solid ${this.styles.selectListBorder} } `);\n      }\n      // Hover foreground - ignore for disabled options\n      if (this.styles.listHoverForeground) {\n        content.push(`.monaco-select-box-dropdown-container > .select-box-dropdown-list-container .monaco-list .monaco-list-row:not(.option-disabled):not(.focused):hover { color: ${this.styles.listHoverForeground} !important; }`);\n      }\n      // Hover background - ignore for disabled options\n      if (this.styles.listHoverBackground) {\n        content.push(`.monaco-select-box-dropdown-container > .select-box-dropdown-list-container .monaco-list .monaco-list-row:not(.option-disabled):not(.focused):hover { background-color: ${this.styles.listHoverBackground} !important; }`);\n      }\n      // Match quick input outline styles - ignore for disabled options\n      if (this.styles.listFocusOutline) {\n        content.push(`.monaco-select-box-dropdown-container > .select-box-dropdown-list-container .monaco-list .monaco-list-row.focused { outline: 1.6px dotted ${this.styles.listFocusOutline} !important; outline-offset: -1.6px !important; }`);\n      }\n      if (this.styles.listHoverOutline) {\n        content.push(`.monaco-select-box-dropdown-container > .select-box-dropdown-list-container .monaco-list .monaco-list-row:not(.option-disabled):not(.focused):hover { outline: 1.6px dashed ${this.styles.listHoverOutline} !important; outline-offset: -1.6px !important; }`);\n      }\n      // Clear list styles on focus and on hover for disabled options\n      content.push(`.monaco-select-box-dropdown-container > .select-box-dropdown-list-container .monaco-list .monaco-list-row.option-disabled.focused { background-color: transparent !important; color: inherit !important; outline: none !important; }`);\n      content.push(`.monaco-select-box-dropdown-container > .select-box-dropdown-list-container .monaco-list .monaco-list-row.option-disabled:hover { background-color: transparent !important; color: inherit !important; outline: none !important; }`);\n      this.styleElement.textContent = content.join('\\n');\n    }\n    styleSelectElement() {\n      const background = this.styles.selectBackground ?? '';\n      const foreground = this.styles.selectForeground ?? '';\n      const border = this.styles.selectBorder ?? '';\n      this.selectElement.style.backgroundColor = background;\n      this.selectElement.style.color = foreground;\n      this.selectElement.style.borderColor = border;\n    }\n    styleList() {\n      const background = this.styles.selectBackground ?? '';\n      const listBackground = dom.asCssValueWithDefault(this.styles.selectListBackground, background);\n      this.selectDropDownListContainer.style.backgroundColor = listBackground;\n      this.selectionDetailsPane.style.backgroundColor = listBackground;\n      const optionsBorder = this.styles.focusBorder ?? '';\n      this.selectDropDownContainer.style.outlineColor = optionsBorder;\n      this.selectDropDownContainer.style.outlineOffset = '-1px';\n      this.selectList.style(this.styles);\n    }\n    createOption(value, index, disabled) {\n      const option = document.createElement('option');\n      option.value = value;\n      option.text = value;\n      option.disabled = !!disabled;\n      return option;\n    }\n    // ContextView dropdown methods\n    showSelectDropDown() {\n      this.selectionDetailsPane.innerText = '';\n      if (!this.contextViewProvider || this._isVisible) {\n        return;\n      }\n      // Lazily create and populate list only at open, moved from constructor\n      this.createSelectList(this.selectDropDownContainer);\n      this.setOptionsList();\n      // This allows us to flip the position based on measurement\n      // Set drop-down position above/below from required height and margins\n      // If pre-layout cannot fit at least one option do not show drop-down\n      this.contextViewProvider.showContextView({\n        getAnchor: () => this.selectElement,\n        render: container => this.renderSelectDropDown(container, true),\n        layout: () => {\n          this.layoutSelectDropDown();\n        },\n        onHide: () => {\n          this.selectDropDownContainer.classList.remove('visible');\n          this.selectElement.classList.remove('synthetic-focus');\n        },\n        anchorPosition: this._dropDownPosition\n      }, this.selectBoxOptions.optionsAsChildren ? this.container : undefined);\n      // Hide so we can relay out\n      this._isVisible = true;\n      this.hideSelectDropDown(false);\n      this.contextViewProvider.showContextView({\n        getAnchor: () => this.selectElement,\n        render: container => this.renderSelectDropDown(container),\n        layout: () => this.layoutSelectDropDown(),\n        onHide: () => {\n          this.selectDropDownContainer.classList.remove('visible');\n          this.selectElement.classList.remove('synthetic-focus');\n        },\n        anchorPosition: this._dropDownPosition\n      }, this.selectBoxOptions.optionsAsChildren ? this.container : undefined);\n      // Track initial selection the case user escape, blur\n      this._currentSelection = this.selected;\n      this._isVisible = true;\n      this.selectElement.setAttribute('aria-expanded', 'true');\n    }\n    hideSelectDropDown(focusSelect) {\n      if (!this.contextViewProvider || !this._isVisible) {\n        return;\n      }\n      this._isVisible = false;\n      this.selectElement.setAttribute('aria-expanded', 'false');\n      if (focusSelect) {\n        this.selectElement.focus();\n      }\n      this.contextViewProvider.hideContextView();\n    }\n    renderSelectDropDown(container, preLayoutPosition) {\n      container.appendChild(this.selectDropDownContainer);\n      // Pre-Layout allows us to change position\n      this.layoutSelectDropDown(preLayoutPosition);\n      return {\n        dispose: () => {\n          // contextView will dispose itself if moving from one View to another\n          this.selectDropDownContainer.remove(); // remove to take out the CSS rules we add\n        }\n      };\n    }\n    // Iterate over detailed descriptions, find max height\n    measureMaxDetailsHeight() {\n      let maxDetailsPaneHeight = 0;\n      this.options.forEach((_option, index) => {\n        this.updateDetail(index);\n        if (this.selectionDetailsPane.offsetHeight > maxDetailsPaneHeight) {\n          maxDetailsPaneHeight = this.selectionDetailsPane.offsetHeight;\n        }\n      });\n      return maxDetailsPaneHeight;\n    }\n    layoutSelectDropDown(preLayoutPosition) {\n      // Avoid recursion from layout called in onListFocus\n      if (this._skipLayout) {\n        return false;\n      }\n      // Layout ContextView drop down select list and container\n      // Have to manage our vertical overflow, sizing, position below or above\n      // Position has to be determined and set prior to contextView instantiation\n      if (this.selectList) {\n        // Make visible to enable measurements\n        this.selectDropDownContainer.classList.add('visible');\n        const window = dom.getWindow(this.selectElement);\n        const selectPosition = dom.getDomNodePagePosition(this.selectElement);\n        const styles = dom.getWindow(this.selectElement).getComputedStyle(this.selectElement);\n        const verticalPadding = parseFloat(styles.getPropertyValue('--dropdown-padding-top')) + parseFloat(styles.getPropertyValue('--dropdown-padding-bottom'));\n        const maxSelectDropDownHeightBelow = window.innerHeight - selectPosition.top - selectPosition.height - (this.selectBoxOptions.minBottomMargin || 0);\n        const maxSelectDropDownHeightAbove = selectPosition.top - SelectBoxList.DEFAULT_DROPDOWN_MINIMUM_TOP_MARGIN;\n        // Determine optimal width - min(longest option), opt(parent select, excluding margins), max(ContextView controlled)\n        const selectWidth = this.selectElement.offsetWidth;\n        const selectMinWidth = this.setWidthControlElement(this.widthControlElement);\n        const selectOptimalWidth = Math.max(selectMinWidth, Math.round(selectWidth)).toString() + 'px';\n        this.selectDropDownContainer.style.width = selectOptimalWidth;\n        // Get initial list height and determine space above and below\n        this.selectList.getHTMLElement().style.height = '';\n        this.selectList.layout();\n        let listHeight = this.selectList.contentHeight;\n        if (this._hasDetails && this._cachedMaxDetailsHeight === undefined) {\n          this._cachedMaxDetailsHeight = this.measureMaxDetailsHeight();\n        }\n        const maxDetailsPaneHeight = this._hasDetails ? this._cachedMaxDetailsHeight : 0;\n        const minRequiredDropDownHeight = listHeight + verticalPadding + maxDetailsPaneHeight;\n        const maxVisibleOptionsBelow = Math.floor((maxSelectDropDownHeightBelow - verticalPadding - maxDetailsPaneHeight) / this.getHeight());\n        const maxVisibleOptionsAbove = Math.floor((maxSelectDropDownHeightAbove - verticalPadding - maxDetailsPaneHeight) / this.getHeight());\n        // If we are only doing pre-layout check/adjust position only\n        // Calculate vertical space available, flip up if insufficient\n        // Use reflected padding on parent select, ContextView style\n        // properties not available before DOM attachment\n        if (preLayoutPosition) {\n          // Check if select moved out of viewport , do not open\n          // If at least one option cannot be shown, don't open the drop-down or hide/remove if open\n          if (selectPosition.top + selectPosition.height > window.innerHeight - 22 || selectPosition.top < SelectBoxList.DEFAULT_DROPDOWN_MINIMUM_TOP_MARGIN || maxVisibleOptionsBelow < 1 && maxVisibleOptionsAbove < 1) {\n            // Indicate we cannot open\n            return false;\n          }\n          // Determine if we have to flip up\n          // Always show complete list items - never more than Max available vertical height\n          if (maxVisibleOptionsBelow < SelectBoxList.DEFAULT_MINIMUM_VISIBLE_OPTIONS && maxVisibleOptionsAbove > maxVisibleOptionsBelow && this.options.length > maxVisibleOptionsBelow) {\n            this._dropDownPosition = 1 /* AnchorPosition.ABOVE */;\n            this.selectDropDownListContainer.remove();\n            this.selectionDetailsPane.remove();\n            this.selectDropDownContainer.appendChild(this.selectionDetailsPane);\n            this.selectDropDownContainer.appendChild(this.selectDropDownListContainer);\n            this.selectionDetailsPane.classList.remove('border-top');\n            this.selectionDetailsPane.classList.add('border-bottom');\n          } else {\n            this._dropDownPosition = 0 /* AnchorPosition.BELOW */;\n            this.selectDropDownListContainer.remove();\n            this.selectionDetailsPane.remove();\n            this.selectDropDownContainer.appendChild(this.selectDropDownListContainer);\n            this.selectDropDownContainer.appendChild(this.selectionDetailsPane);\n            this.selectionDetailsPane.classList.remove('border-bottom');\n            this.selectionDetailsPane.classList.add('border-top');\n          }\n          // Do full layout on showSelectDropDown only\n          return true;\n        }\n        // Check if select out of viewport or cutting into status bar\n        if (selectPosition.top + selectPosition.height > window.innerHeight - 22 || selectPosition.top < SelectBoxList.DEFAULT_DROPDOWN_MINIMUM_TOP_MARGIN || this._dropDownPosition === 0 /* AnchorPosition.BELOW */ && maxVisibleOptionsBelow < 1 || this._dropDownPosition === 1 /* AnchorPosition.ABOVE */ && maxVisibleOptionsAbove < 1) {\n          // Cannot properly layout, close and hide\n          this.hideSelectDropDown(true);\n          return false;\n        }\n        // SetUp list dimensions and layout - account for container padding\n        // Use position to check above or below available space\n        if (this._dropDownPosition === 0 /* AnchorPosition.BELOW */) {\n          if (this._isVisible && maxVisibleOptionsBelow + maxVisibleOptionsAbove < 1) {\n            // If drop-down is visible, must be doing a DOM re-layout, hide since we don't fit\n            // Hide drop-down, hide contextview, focus on parent select\n            this.hideSelectDropDown(true);\n            return false;\n          }\n          // Adjust list height to max from select bottom to margin (default/minBottomMargin)\n          if (minRequiredDropDownHeight > maxSelectDropDownHeightBelow) {\n            listHeight = maxVisibleOptionsBelow * this.getHeight();\n          }\n        } else {\n          if (minRequiredDropDownHeight > maxSelectDropDownHeightAbove) {\n            listHeight = maxVisibleOptionsAbove * this.getHeight();\n          }\n        }\n        // Set adjusted list height and relayout\n        this.selectList.layout(listHeight);\n        this.selectList.domFocus();\n        // Finally set focus on selected item\n        if (this.selectList.length > 0) {\n          this.selectList.setFocus([this.selected || 0]);\n          this.selectList.reveal(this.selectList.getFocus()[0] || 0);\n        }\n        if (this._hasDetails) {\n          // Leave the selectDropDownContainer to size itself according to children (list + details) - #57447\n          this.selectList.getHTMLElement().style.height = listHeight + verticalPadding + 'px';\n          this.selectDropDownContainer.style.height = '';\n        } else {\n          this.selectDropDownContainer.style.height = listHeight + verticalPadding + 'px';\n        }\n        this.updateDetail(this.selected);\n        this.selectDropDownContainer.style.width = selectOptimalWidth;\n        // Maintain focus outline on parent select as well as list container - tabindex for focus\n        this.selectDropDownListContainer.setAttribute('tabindex', '0');\n        this.selectElement.classList.add('synthetic-focus');\n        this.selectDropDownContainer.classList.add('synthetic-focus');\n        return true;\n      } else {\n        return false;\n      }\n    }\n    setWidthControlElement(container) {\n      let elementWidth = 0;\n      if (container) {\n        let longest = 0;\n        let longestLength = 0;\n        this.options.forEach((option, index) => {\n          const detailLength = !!option.detail ? option.detail.length : 0;\n          const rightDecoratorLength = !!option.decoratorRight ? option.decoratorRight.length : 0;\n          const len = option.text.length + detailLength + rightDecoratorLength;\n          if (len > longestLength) {\n            longest = index;\n            longestLength = len;\n          }\n        });\n        container.textContent = this.options[longest].text + (!!this.options[longest].decoratorRight ? this.options[longest].decoratorRight + ' ' : '');\n        elementWidth = dom.getTotalWidth(container);\n      }\n      return elementWidth;\n    }\n    createSelectList(parent) {\n      // If we have already constructive list on open, skip\n      if (this.selectList) {\n        return;\n      }\n      // SetUp container for list\n      this.selectDropDownListContainer = dom.append(parent, $('.select-box-dropdown-list-container'));\n      this.listRenderer = new SelectListRenderer();\n      this.selectList = this._register(new List('SelectBoxCustom', this.selectDropDownListContainer, this, [this.listRenderer], {\n        useShadows: false,\n        verticalScrollMode: 3 /* ScrollbarVisibility.Visible */,\n        keyboardSupport: false,\n        mouseSupport: false,\n        accessibilityProvider: {\n          getAriaLabel: element => {\n            let label = element.text;\n            if (element.detail) {\n              label += `. ${element.detail}`;\n            }\n            if (element.decoratorRight) {\n              label += `. ${element.decoratorRight}`;\n            }\n            if (element.description) {\n              label += `. ${element.description}`;\n            }\n            return label;\n          },\n          getWidgetAriaLabel: () => localize({\n            key: 'selectBox',\n            comment: ['Behave like native select dropdown element.']\n          }, \"Select Box\"),\n          getRole: () => isMacintosh ? '' : 'option',\n          getWidgetRole: () => 'listbox'\n        }\n      }));\n      if (this.selectBoxOptions.ariaLabel) {\n        this.selectList.ariaLabel = this.selectBoxOptions.ariaLabel;\n      }\n      // SetUp list keyboard controller - control navigation, disabled items, focus\n      const onKeyDown = this._register(new DomEmitter(this.selectDropDownListContainer, 'keydown'));\n      const onSelectDropDownKeyDown = Event.chain(onKeyDown.event, $ => $.filter(() => this.selectList.length > 0).map(e => new StandardKeyboardEvent(e)));\n      this._register(Event.chain(onSelectDropDownKeyDown, $ => $.filter(e => e.keyCode === 3 /* KeyCode.Enter */))(this.onEnter, this));\n      this._register(Event.chain(onSelectDropDownKeyDown, $ => $.filter(e => e.keyCode === 2 /* KeyCode.Tab */))(this.onEnter, this)); // Tab should behave the same as enter, #79339\n      this._register(Event.chain(onSelectDropDownKeyDown, $ => $.filter(e => e.keyCode === 9 /* KeyCode.Escape */))(this.onEscape, this));\n      this._register(Event.chain(onSelectDropDownKeyDown, $ => $.filter(e => e.keyCode === 16 /* KeyCode.UpArrow */))(this.onUpArrow, this));\n      this._register(Event.chain(onSelectDropDownKeyDown, $ => $.filter(e => e.keyCode === 18 /* KeyCode.DownArrow */))(this.onDownArrow, this));\n      this._register(Event.chain(onSelectDropDownKeyDown, $ => $.filter(e => e.keyCode === 12 /* KeyCode.PageDown */))(this.onPageDown, this));\n      this._register(Event.chain(onSelectDropDownKeyDown, $ => $.filter(e => e.keyCode === 11 /* KeyCode.PageUp */))(this.onPageUp, this));\n      this._register(Event.chain(onSelectDropDownKeyDown, $ => $.filter(e => e.keyCode === 14 /* KeyCode.Home */))(this.onHome, this));\n      this._register(Event.chain(onSelectDropDownKeyDown, $ => $.filter(e => e.keyCode === 13 /* KeyCode.End */))(this.onEnd, this));\n      this._register(Event.chain(onSelectDropDownKeyDown, $ => $.filter(e => e.keyCode >= 21 /* KeyCode.Digit0 */ && e.keyCode <= 56 /* KeyCode.KeyZ */ || e.keyCode >= 85 /* KeyCode.Semicolon */ && e.keyCode <= 113 /* KeyCode.NumpadDivide */))(this.onCharacter, this));\n      // SetUp list mouse controller - control navigation, disabled items, focus\n      this._register(dom.addDisposableListener(this.selectList.getHTMLElement(), dom.EventType.POINTER_UP, e => this.onPointerUp(e)));\n      this._register(this.selectList.onMouseOver(e => typeof e.index !== 'undefined' && this.selectList.setFocus([e.index])));\n      this._register(this.selectList.onDidChangeFocus(e => this.onListFocus(e)));\n      this._register(dom.addDisposableListener(this.selectDropDownContainer, dom.EventType.FOCUS_OUT, e => {\n        if (!this._isVisible || dom.isAncestor(e.relatedTarget, this.selectDropDownContainer)) {\n          return;\n        }\n        this.onListBlur();\n      }));\n      this.selectList.getHTMLElement().setAttribute('aria-label', this.selectBoxOptions.ariaLabel || '');\n      this.selectList.getHTMLElement().setAttribute('aria-expanded', 'true');\n      this.styleList();\n    }\n    // List methods\n    // List mouse controller - active exit, select option, fire onDidSelect if change, return focus to parent select\n    // Also takes in touchend events\n    onPointerUp(e) {\n      if (!this.selectList.length) {\n        return;\n      }\n      dom.EventHelper.stop(e);\n      const target = e.target;\n      if (!target) {\n        return;\n      }\n      // Check our mouse event is on an option (not scrollbar)\n      if (target.classList.contains('slider')) {\n        return;\n      }\n      const listRowElement = target.closest('.monaco-list-row');\n      if (!listRowElement) {\n        return;\n      }\n      const index = Number(listRowElement.getAttribute('data-index'));\n      const disabled = listRowElement.classList.contains('option-disabled');\n      // Ignore mouse selection of disabled options\n      if (index >= 0 && index < this.options.length && !disabled) {\n        this.selected = index;\n        this.select(this.selected);\n        this.selectList.setFocus([this.selected]);\n        this.selectList.reveal(this.selectList.getFocus()[0]);\n        // Only fire if selection change\n        if (this.selected !== this._currentSelection) {\n          // Set current = selected\n          this._currentSelection = this.selected;\n          this._onDidSelect.fire({\n            index: this.selectElement.selectedIndex,\n            selected: this.options[this.selected].text\n          });\n          if (!!this.options[this.selected] && !!this.options[this.selected].text) {\n            this.setTitle(this.options[this.selected].text);\n          }\n        }\n        this.hideSelectDropDown(true);\n      }\n    }\n    // List Exit - passive - implicit no selection change, hide drop-down\n    onListBlur() {\n      if (this._sticky) {\n        return;\n      }\n      if (this.selected !== this._currentSelection) {\n        // Reset selected to current if no change\n        this.select(this._currentSelection);\n      }\n      this.hideSelectDropDown(false);\n    }\n    renderDescriptionMarkdown(text, actionHandler) {\n      const cleanRenderedMarkdown = element => {\n        for (let i = 0; i < element.childNodes.length; i++) {\n          const child = element.childNodes.item(i);\n          const tagName = child.tagName && child.tagName.toLowerCase();\n          if (tagName === 'img') {\n            child.remove();\n          } else {\n            cleanRenderedMarkdown(child);\n          }\n        }\n      };\n      const rendered = renderMarkdown({\n        value: text,\n        supportThemeIcons: true\n      }, {\n        actionHandler\n      });\n      rendered.element.classList.add('select-box-description-markdown');\n      cleanRenderedMarkdown(rendered.element);\n      return rendered.element;\n    }\n    // List Focus Change - passive - update details pane with newly focused element's data\n    onListFocus(e) {\n      // Skip during initial layout\n      if (!this._isVisible || !this._hasDetails) {\n        return;\n      }\n      this.updateDetail(e.indexes[0]);\n    }\n    updateDetail(selectedIndex) {\n      this.selectionDetailsPane.innerText = '';\n      const option = this.options[selectedIndex];\n      const description = option?.description ?? '';\n      const descriptionIsMarkdown = option?.descriptionIsMarkdown ?? false;\n      if (description) {\n        if (descriptionIsMarkdown) {\n          const actionHandler = option.descriptionMarkdownActionHandler;\n          this.selectionDetailsPane.appendChild(this.renderDescriptionMarkdown(description, actionHandler));\n        } else {\n          this.selectionDetailsPane.innerText = description;\n        }\n        this.selectionDetailsPane.style.display = 'block';\n      } else {\n        this.selectionDetailsPane.style.display = 'none';\n      }\n      // Avoid recursion\n      this._skipLayout = true;\n      this.contextViewProvider.layout();\n      this._skipLayout = false;\n    }\n    // List keyboard controller\n    // List exit - active - hide ContextView dropdown, reset selection, return focus to parent select\n    onEscape(e) {\n      dom.EventHelper.stop(e);\n      // Reset selection to value when opened\n      this.select(this._currentSelection);\n      this.hideSelectDropDown(true);\n    }\n    // List exit - active - hide ContextView dropdown, return focus to parent select, fire onDidSelect if change\n    onEnter(e) {\n      dom.EventHelper.stop(e);\n      // Only fire if selection change\n      if (this.selected !== this._currentSelection) {\n        this._currentSelection = this.selected;\n        this._onDidSelect.fire({\n          index: this.selectElement.selectedIndex,\n          selected: this.options[this.selected].text\n        });\n        if (!!this.options[this.selected] && !!this.options[this.selected].text) {\n          this.setTitle(this.options[this.selected].text);\n        }\n      }\n      this.hideSelectDropDown(true);\n    }\n    // List navigation - have to handle a disabled option (jump over)\n    onDownArrow(e) {\n      if (this.selected < this.options.length - 1) {\n        dom.EventHelper.stop(e, true);\n        // Skip disabled options\n        const nextOptionDisabled = this.options[this.selected + 1].isDisabled;\n        if (nextOptionDisabled && this.options.length > this.selected + 2) {\n          this.selected += 2;\n        } else if (nextOptionDisabled) {\n          return;\n        } else {\n          this.selected++;\n        }\n        // Set focus/selection - only fire event when closing drop-down or on blur\n        this.select(this.selected);\n        this.selectList.setFocus([this.selected]);\n        this.selectList.reveal(this.selectList.getFocus()[0]);\n      }\n    }\n    onUpArrow(e) {\n      if (this.selected > 0) {\n        dom.EventHelper.stop(e, true);\n        // Skip disabled options\n        const previousOptionDisabled = this.options[this.selected - 1].isDisabled;\n        if (previousOptionDisabled && this.selected > 1) {\n          this.selected -= 2;\n        } else {\n          this.selected--;\n        }\n        // Set focus/selection - only fire event when closing drop-down or on blur\n        this.select(this.selected);\n        this.selectList.setFocus([this.selected]);\n        this.selectList.reveal(this.selectList.getFocus()[0]);\n      }\n    }\n    onPageUp(e) {\n      dom.EventHelper.stop(e);\n      this.selectList.focusPreviousPage();\n      // Allow scrolling to settle\n      setTimeout(() => {\n        this.selected = this.selectList.getFocus()[0];\n        // Shift selection down if we land on a disabled option\n        if (this.options[this.selected].isDisabled && this.selected < this.options.length - 1) {\n          this.selected++;\n          this.selectList.setFocus([this.selected]);\n        }\n        this.selectList.reveal(this.selected);\n        this.select(this.selected);\n      }, 1);\n    }\n    onPageDown(e) {\n      dom.EventHelper.stop(e);\n      this.selectList.focusNextPage();\n      // Allow scrolling to settle\n      setTimeout(() => {\n        this.selected = this.selectList.getFocus()[0];\n        // Shift selection up if we land on a disabled option\n        if (this.options[this.selected].isDisabled && this.selected > 0) {\n          this.selected--;\n          this.selectList.setFocus([this.selected]);\n        }\n        this.selectList.reveal(this.selected);\n        this.select(this.selected);\n      }, 1);\n    }\n    onHome(e) {\n      dom.EventHelper.stop(e);\n      if (this.options.length < 2) {\n        return;\n      }\n      this.selected = 0;\n      if (this.options[this.selected].isDisabled && this.selected > 1) {\n        this.selected++;\n      }\n      this.selectList.setFocus([this.selected]);\n      this.selectList.reveal(this.selected);\n      this.select(this.selected);\n    }\n    onEnd(e) {\n      dom.EventHelper.stop(e);\n      if (this.options.length < 2) {\n        return;\n      }\n      this.selected = this.options.length - 1;\n      if (this.options[this.selected].isDisabled && this.selected > 1) {\n        this.selected--;\n      }\n      this.selectList.setFocus([this.selected]);\n      this.selectList.reveal(this.selected);\n      this.select(this.selected);\n    }\n    // Mimic option first character navigation of native select\n    onCharacter(e) {\n      const ch = KeyCodeUtils.toString(e.keyCode);\n      let optionIndex = -1;\n      for (let i = 0; i < this.options.length - 1; i++) {\n        optionIndex = (i + this.selected + 1) % this.options.length;\n        if (this.options[optionIndex].text.charAt(0).toUpperCase() === ch && !this.options[optionIndex].isDisabled) {\n          this.select(optionIndex);\n          this.selectList.setFocus([optionIndex]);\n          this.selectList.reveal(this.selectList.getFocus()[0]);\n          dom.EventHelper.stop(e);\n          break;\n        }\n      }\n    }\n    dispose() {\n      this.hideSelectDropDown(false);\n      super.dispose();\n    }\n  }\n  return SelectBoxList;\n})();", "map": {"version": 3, "names": ["dom", "DomEmitter", "StandardKeyboardEvent", "renderMarkdown", "getBaseLayerHoverDelegate", "getDefaultHoverDelegate", "List", "arrays", "Emitter", "Event", "KeyCodeUtils", "Disposable", "isMacintosh", "localize", "$", "SELECT_OPTION_ENTRY_TEMPLATE_ID", "SelectList<PERSON><PERSON><PERSON>", "templateId", "renderTemplate", "container", "data", "Object", "create", "root", "text", "append", "detail", "decoratorRight", "renderElement", "element", "index", "templateData", "isDisabled", "textContent", "innerText", "classList", "add", "remove", "disposeTemplate", "_templateData", "SelectBoxList", "DEFAULT_DROPDOWN_MINIMUM_BOTTOM_MARGIN", "DEFAULT_DROPDOWN_MINIMUM_TOP_MARGIN", "DEFAULT_MINIMUM_VISIBLE_OPTIONS", "constructor", "options", "selected", "contextView<PERSON>rovider", "styles", "selectBoxOptions", "_currentSelection", "_hasDetails", "_skipLayout", "_sticky", "_isVisible", "minBottomMargin", "selectElement", "document", "createElement", "className", "aria<PERSON><PERSON><PERSON>", "setAttribute", "ariaDescription", "_onDidSelect", "_register", "registerListeners", "constructSelectDropDown", "setOptions", "initStyleSheet", "setTitle", "title", "_hover", "setupManagedHover", "update", "getHeight", "getTemplateId", "selectDropDownContainer", "selectionDetailsPane", "widthControlOuterDiv", "widthControlInnerDiv", "widthControlElement", "_dropDownPosition", "styleElement", "createStyleSheet", "addDisposableListener", "EventType", "DRAG_START", "e", "EventHelper", "stop", "addStandardDisposableListener", "target", "selectedIndex", "fire", "value", "CLICK", "hideSelectDropDown", "showSelectDropDown", "MOUSE_DOWN", "listIsVisibleOnTouchStart", "KEY_DOWN", "event", "showDropDown", "keyCode", "altKey", "onDidSelect", "equals", "length", "_cachedMaxDetailsHeight", "undefined", "for<PERSON>ach", "option", "createOption", "description", "select", "setOptionsList", "selectList", "splice", "focus", "tabIndex", "blur", "setFocusable", "focusable", "render", "append<PERSON><PERSON><PERSON>", "styleSelectElement", "content", "listFocusBackground", "push", "listFocusForeground", "decoratorRightForeground", "selectBackground", "selectBorder", "selectListBorder", "listHoverForeground", "listHoverBackground", "listFocusOutline", "listHoverOutline", "join", "background", "foreground", "selectForeground", "border", "style", "backgroundColor", "color", "borderColor", "styleList", "listBackground", "asCssValueWithDefault", "selectListBackground", "selectDropDownListContainer", "optionsBorder", "focusBorder", "outlineColor", "outlineOffset", "disabled", "createSelectList", "showContextView", "getAnchor", "renderSelectDropDown", "layout", "layoutSelectDropDown", "onHide", "anchorPosition", "optionsAsChildren", "focusSelect", "hideContextView", "preLayoutPosition", "dispose", "measureMaxDetailsHeight", "maxDetailsPaneHeight", "_option", "updateDetail", "offsetHeight", "window", "getWindow", "selectPosition", "getDomNodePagePosition", "getComputedStyle", "verticalPadding", "parseFloat", "getPropertyValue", "maxSelectDropDownHeightBelow", "innerHeight", "top", "height", "maxSelectDropDownHeightAbove", "selectWidth", "offsetWidth", "select<PERSON><PERSON><PERSON><PERSON><PERSON>", "setWidthControlElement", "selectOptimalWidth", "Math", "max", "round", "toString", "width", "getHTMLElement", "listHeight", "contentHeight", "minRequiredDropDownHeight", "maxVisibleOptionsBelow", "floor", "maxVisibleOptionsAbove", "domFocus", "setFocus", "reveal", "getFocus", "elementWidth", "longest", "longestLength", "<PERSON><PERSON><PERSON><PERSON>", "rightDecoratorLength", "len", "getTotalWidth", "parent", "<PERSON><PERSON><PERSON><PERSON>", "useShadows", "verticalScrollMode", "keyboardSupport", "mouseSupport", "accessibilityProvider", "getAriaLabel", "label", "getWidgetAriaLabel", "key", "comment", "getRole", "getWidgetRole", "onKeyDown", "onSelectDropDownKeyDown", "chain", "filter", "map", "onEnter", "onEscape", "onUpArrow", "onDownArrow", "onPageDown", "onPageUp", "onHome", "onEnd", "onCharacter", "POINTER_UP", "onPointerUp", "onMouseOver", "onDidChangeFocus", "onListFocus", "FOCUS_OUT", "isAncestor", "relatedTarget", "onListBlur", "contains", "listRowElement", "closest", "Number", "getAttribute", "renderDescriptionMarkdown", "actionHandler", "cleanRenderedMarkdown", "i", "childNodes", "child", "item", "tagName", "toLowerCase", "rendered", "supportThemeIcons", "indexes", "descriptionIsMarkdown", "descriptionMarkdownActionHandler", "display", "nextOptionDisabled", "previousOptionDisabled", "focusPreviousPage", "setTimeout", "focusNextPage", "ch", "optionIndex", "char<PERSON>t", "toUpperCase"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/browser/ui/selectBox/selectBoxCustom.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as dom from '../../dom.js';\nimport { DomEmitter } from '../../event.js';\nimport { StandardKeyboardEvent } from '../../keyboardEvent.js';\nimport { renderMarkdown } from '../../markdownRenderer.js';\nimport { getBaseLayerHoverDelegate } from '../hover/hoverDelegate2.js';\nimport { getDefaultHoverDelegate } from '../hover/hoverDelegateFactory.js';\nimport { List } from '../list/listWidget.js';\nimport * as arrays from '../../../common/arrays.js';\nimport { Emitter, Event } from '../../../common/event.js';\nimport { KeyCodeUtils } from '../../../common/keyCodes.js';\nimport { Disposable } from '../../../common/lifecycle.js';\nimport { isMacintosh } from '../../../common/platform.js';\nimport './selectBoxCustom.css';\nimport { localize } from '../../../../nls.js';\nconst $ = dom.$;\nconst SELECT_OPTION_ENTRY_TEMPLATE_ID = 'selectOption.entry.template';\nclass SelectListRenderer {\n    get templateId() { return SELECT_OPTION_ENTRY_TEMPLATE_ID; }\n    renderTemplate(container) {\n        const data = Object.create(null);\n        data.root = container;\n        data.text = dom.append(container, $('.option-text'));\n        data.detail = dom.append(container, $('.option-detail'));\n        data.decoratorRight = dom.append(container, $('.option-decorator-right'));\n        return data;\n    }\n    renderElement(element, index, templateData) {\n        const data = templateData;\n        const text = element.text;\n        const detail = element.detail;\n        const decoratorRight = element.decoratorRight;\n        const isDisabled = element.isDisabled;\n        data.text.textContent = text;\n        data.detail.textContent = !!detail ? detail : '';\n        data.decoratorRight.innerText = !!decoratorRight ? decoratorRight : '';\n        // pseudo-select disabled option\n        if (isDisabled) {\n            data.root.classList.add('option-disabled');\n        }\n        else {\n            // Make sure we do class removal from prior template rendering\n            data.root.classList.remove('option-disabled');\n        }\n    }\n    disposeTemplate(_templateData) {\n        // noop\n    }\n}\nexport class SelectBoxList extends Disposable {\n    static { this.DEFAULT_DROPDOWN_MINIMUM_BOTTOM_MARGIN = 32; }\n    static { this.DEFAULT_DROPDOWN_MINIMUM_TOP_MARGIN = 2; }\n    static { this.DEFAULT_MINIMUM_VISIBLE_OPTIONS = 3; }\n    constructor(options, selected, contextViewProvider, styles, selectBoxOptions) {\n        super();\n        this.options = [];\n        this._currentSelection = 0;\n        this._hasDetails = false;\n        this._skipLayout = false;\n        this._sticky = false; // for dev purposes only\n        this._isVisible = false;\n        this.styles = styles;\n        this.selectBoxOptions = selectBoxOptions || Object.create(null);\n        if (typeof this.selectBoxOptions.minBottomMargin !== 'number') {\n            this.selectBoxOptions.minBottomMargin = SelectBoxList.DEFAULT_DROPDOWN_MINIMUM_BOTTOM_MARGIN;\n        }\n        else if (this.selectBoxOptions.minBottomMargin < 0) {\n            this.selectBoxOptions.minBottomMargin = 0;\n        }\n        this.selectElement = document.createElement('select');\n        // Use custom CSS vars for padding calculation\n        this.selectElement.className = 'monaco-select-box monaco-select-box-dropdown-padding';\n        if (typeof this.selectBoxOptions.ariaLabel === 'string') {\n            this.selectElement.setAttribute('aria-label', this.selectBoxOptions.ariaLabel);\n        }\n        if (typeof this.selectBoxOptions.ariaDescription === 'string') {\n            this.selectElement.setAttribute('aria-description', this.selectBoxOptions.ariaDescription);\n        }\n        this._onDidSelect = new Emitter();\n        this._register(this._onDidSelect);\n        this.registerListeners();\n        this.constructSelectDropDown(contextViewProvider);\n        this.selected = selected || 0;\n        if (options) {\n            this.setOptions(options, selected);\n        }\n        this.initStyleSheet();\n    }\n    setTitle(title) {\n        if (!this._hover && title) {\n            this._hover = this._register(getBaseLayerHoverDelegate().setupManagedHover(getDefaultHoverDelegate('mouse'), this.selectElement, title));\n        }\n        else if (this._hover) {\n            this._hover.update(title);\n        }\n    }\n    // IDelegate - List renderer\n    getHeight() {\n        return 22;\n    }\n    getTemplateId() {\n        return SELECT_OPTION_ENTRY_TEMPLATE_ID;\n    }\n    constructSelectDropDown(contextViewProvider) {\n        // SetUp ContextView container to hold select Dropdown\n        this.contextViewProvider = contextViewProvider;\n        this.selectDropDownContainer = dom.$('.monaco-select-box-dropdown-container');\n        // Use custom CSS vars for padding calculation (shared with parent select)\n        this.selectDropDownContainer.classList.add('monaco-select-box-dropdown-padding');\n        // Setup container for select option details\n        this.selectionDetailsPane = dom.append(this.selectDropDownContainer, $('.select-box-details-pane'));\n        // Create span flex box item/div we can measure and control\n        const widthControlOuterDiv = dom.append(this.selectDropDownContainer, $('.select-box-dropdown-container-width-control'));\n        const widthControlInnerDiv = dom.append(widthControlOuterDiv, $('.width-control-div'));\n        this.widthControlElement = document.createElement('span');\n        this.widthControlElement.className = 'option-text-width-control';\n        dom.append(widthControlInnerDiv, this.widthControlElement);\n        // Always default to below position\n        this._dropDownPosition = 0 /* AnchorPosition.BELOW */;\n        // Inline stylesheet for themes\n        this.styleElement = dom.createStyleSheet(this.selectDropDownContainer);\n        // Prevent dragging of dropdown #114329\n        this.selectDropDownContainer.setAttribute('draggable', 'true');\n        this._register(dom.addDisposableListener(this.selectDropDownContainer, dom.EventType.DRAG_START, (e) => {\n            dom.EventHelper.stop(e, true);\n        }));\n    }\n    registerListeners() {\n        // Parent native select keyboard listeners\n        this._register(dom.addStandardDisposableListener(this.selectElement, 'change', (e) => {\n            this.selected = e.target.selectedIndex;\n            this._onDidSelect.fire({\n                index: e.target.selectedIndex,\n                selected: e.target.value\n            });\n            if (!!this.options[this.selected] && !!this.options[this.selected].text) {\n                this.setTitle(this.options[this.selected].text);\n            }\n        }));\n        // Have to implement both keyboard and mouse controllers to handle disabled options\n        // Intercept mouse events to override normal select actions on parents\n        this._register(dom.addDisposableListener(this.selectElement, dom.EventType.CLICK, (e) => {\n            dom.EventHelper.stop(e);\n            if (this._isVisible) {\n                this.hideSelectDropDown(true);\n            }\n            else {\n                this.showSelectDropDown();\n            }\n        }));\n        this._register(dom.addDisposableListener(this.selectElement, dom.EventType.MOUSE_DOWN, (e) => {\n            dom.EventHelper.stop(e);\n        }));\n        // Intercept touch events\n        // The following implementation is slightly different from the mouse event handlers above.\n        // Use the following helper variable, otherwise the list flickers.\n        let listIsVisibleOnTouchStart;\n        this._register(dom.addDisposableListener(this.selectElement, 'touchstart', (e) => {\n            listIsVisibleOnTouchStart = this._isVisible;\n        }));\n        this._register(dom.addDisposableListener(this.selectElement, 'touchend', (e) => {\n            dom.EventHelper.stop(e);\n            if (listIsVisibleOnTouchStart) {\n                this.hideSelectDropDown(true);\n            }\n            else {\n                this.showSelectDropDown();\n            }\n        }));\n        // Intercept keyboard handling\n        this._register(dom.addDisposableListener(this.selectElement, dom.EventType.KEY_DOWN, (e) => {\n            const event = new StandardKeyboardEvent(e);\n            let showDropDown = false;\n            // Create and drop down select list on keyboard select\n            if (isMacintosh) {\n                if (event.keyCode === 18 /* KeyCode.DownArrow */ || event.keyCode === 16 /* KeyCode.UpArrow */ || event.keyCode === 10 /* KeyCode.Space */ || event.keyCode === 3 /* KeyCode.Enter */) {\n                    showDropDown = true;\n                }\n            }\n            else {\n                if (event.keyCode === 18 /* KeyCode.DownArrow */ && event.altKey || event.keyCode === 16 /* KeyCode.UpArrow */ && event.altKey || event.keyCode === 10 /* KeyCode.Space */ || event.keyCode === 3 /* KeyCode.Enter */) {\n                    showDropDown = true;\n                }\n            }\n            if (showDropDown) {\n                this.showSelectDropDown();\n                dom.EventHelper.stop(e, true);\n            }\n        }));\n    }\n    get onDidSelect() {\n        return this._onDidSelect.event;\n    }\n    setOptions(options, selected) {\n        if (!arrays.equals(this.options, options)) {\n            this.options = options;\n            this.selectElement.options.length = 0;\n            this._hasDetails = false;\n            this._cachedMaxDetailsHeight = undefined;\n            this.options.forEach((option, index) => {\n                this.selectElement.add(this.createOption(option.text, index, option.isDisabled));\n                if (typeof option.description === 'string') {\n                    this._hasDetails = true;\n                }\n            });\n        }\n        if (selected !== undefined) {\n            this.select(selected);\n            // Set current = selected since this is not necessarily a user exit\n            this._currentSelection = this.selected;\n        }\n    }\n    setOptionsList() {\n        // Mirror options in drop-down\n        // Populate select list for non-native select mode\n        this.selectList?.splice(0, this.selectList.length, this.options);\n    }\n    select(index) {\n        if (index >= 0 && index < this.options.length) {\n            this.selected = index;\n        }\n        else if (index > this.options.length - 1) {\n            // Adjust index to end of list\n            // This could make client out of sync with the select\n            this.select(this.options.length - 1);\n        }\n        else if (this.selected < 0) {\n            this.selected = 0;\n        }\n        this.selectElement.selectedIndex = this.selected;\n        if (!!this.options[this.selected] && !!this.options[this.selected].text) {\n            this.setTitle(this.options[this.selected].text);\n        }\n    }\n    focus() {\n        if (this.selectElement) {\n            this.selectElement.tabIndex = 0;\n            this.selectElement.focus();\n        }\n    }\n    blur() {\n        if (this.selectElement) {\n            this.selectElement.tabIndex = -1;\n            this.selectElement.blur();\n        }\n    }\n    setFocusable(focusable) {\n        this.selectElement.tabIndex = focusable ? 0 : -1;\n    }\n    render(container) {\n        this.container = container;\n        container.classList.add('select-container');\n        container.appendChild(this.selectElement);\n        this.styleSelectElement();\n    }\n    initStyleSheet() {\n        const content = [];\n        // Style non-native select mode\n        if (this.styles.listFocusBackground) {\n            content.push(`.monaco-select-box-dropdown-container > .select-box-dropdown-list-container .monaco-list .monaco-list-row.focused { background-color: ${this.styles.listFocusBackground} !important; }`);\n        }\n        if (this.styles.listFocusForeground) {\n            content.push(`.monaco-select-box-dropdown-container > .select-box-dropdown-list-container .monaco-list .monaco-list-row.focused { color: ${this.styles.listFocusForeground} !important; }`);\n        }\n        if (this.styles.decoratorRightForeground) {\n            content.push(`.monaco-select-box-dropdown-container > .select-box-dropdown-list-container .monaco-list .monaco-list-row:not(.focused) .option-decorator-right { color: ${this.styles.decoratorRightForeground}; }`);\n        }\n        if (this.styles.selectBackground && this.styles.selectBorder && this.styles.selectBorder !== this.styles.selectBackground) {\n            content.push(`.monaco-select-box-dropdown-container { border: 1px solid ${this.styles.selectBorder} } `);\n            content.push(`.monaco-select-box-dropdown-container > .select-box-details-pane.border-top { border-top: 1px solid ${this.styles.selectBorder} } `);\n            content.push(`.monaco-select-box-dropdown-container > .select-box-details-pane.border-bottom { border-bottom: 1px solid ${this.styles.selectBorder} } `);\n        }\n        else if (this.styles.selectListBorder) {\n            content.push(`.monaco-select-box-dropdown-container > .select-box-details-pane.border-top { border-top: 1px solid ${this.styles.selectListBorder} } `);\n            content.push(`.monaco-select-box-dropdown-container > .select-box-details-pane.border-bottom { border-bottom: 1px solid ${this.styles.selectListBorder} } `);\n        }\n        // Hover foreground - ignore for disabled options\n        if (this.styles.listHoverForeground) {\n            content.push(`.monaco-select-box-dropdown-container > .select-box-dropdown-list-container .monaco-list .monaco-list-row:not(.option-disabled):not(.focused):hover { color: ${this.styles.listHoverForeground} !important; }`);\n        }\n        // Hover background - ignore for disabled options\n        if (this.styles.listHoverBackground) {\n            content.push(`.monaco-select-box-dropdown-container > .select-box-dropdown-list-container .monaco-list .monaco-list-row:not(.option-disabled):not(.focused):hover { background-color: ${this.styles.listHoverBackground} !important; }`);\n        }\n        // Match quick input outline styles - ignore for disabled options\n        if (this.styles.listFocusOutline) {\n            content.push(`.monaco-select-box-dropdown-container > .select-box-dropdown-list-container .monaco-list .monaco-list-row.focused { outline: 1.6px dotted ${this.styles.listFocusOutline} !important; outline-offset: -1.6px !important; }`);\n        }\n        if (this.styles.listHoverOutline) {\n            content.push(`.monaco-select-box-dropdown-container > .select-box-dropdown-list-container .monaco-list .monaco-list-row:not(.option-disabled):not(.focused):hover { outline: 1.6px dashed ${this.styles.listHoverOutline} !important; outline-offset: -1.6px !important; }`);\n        }\n        // Clear list styles on focus and on hover for disabled options\n        content.push(`.monaco-select-box-dropdown-container > .select-box-dropdown-list-container .monaco-list .monaco-list-row.option-disabled.focused { background-color: transparent !important; color: inherit !important; outline: none !important; }`);\n        content.push(`.monaco-select-box-dropdown-container > .select-box-dropdown-list-container .monaco-list .monaco-list-row.option-disabled:hover { background-color: transparent !important; color: inherit !important; outline: none !important; }`);\n        this.styleElement.textContent = content.join('\\n');\n    }\n    styleSelectElement() {\n        const background = this.styles.selectBackground ?? '';\n        const foreground = this.styles.selectForeground ?? '';\n        const border = this.styles.selectBorder ?? '';\n        this.selectElement.style.backgroundColor = background;\n        this.selectElement.style.color = foreground;\n        this.selectElement.style.borderColor = border;\n    }\n    styleList() {\n        const background = this.styles.selectBackground ?? '';\n        const listBackground = dom.asCssValueWithDefault(this.styles.selectListBackground, background);\n        this.selectDropDownListContainer.style.backgroundColor = listBackground;\n        this.selectionDetailsPane.style.backgroundColor = listBackground;\n        const optionsBorder = this.styles.focusBorder ?? '';\n        this.selectDropDownContainer.style.outlineColor = optionsBorder;\n        this.selectDropDownContainer.style.outlineOffset = '-1px';\n        this.selectList.style(this.styles);\n    }\n    createOption(value, index, disabled) {\n        const option = document.createElement('option');\n        option.value = value;\n        option.text = value;\n        option.disabled = !!disabled;\n        return option;\n    }\n    // ContextView dropdown methods\n    showSelectDropDown() {\n        this.selectionDetailsPane.innerText = '';\n        if (!this.contextViewProvider || this._isVisible) {\n            return;\n        }\n        // Lazily create and populate list only at open, moved from constructor\n        this.createSelectList(this.selectDropDownContainer);\n        this.setOptionsList();\n        // This allows us to flip the position based on measurement\n        // Set drop-down position above/below from required height and margins\n        // If pre-layout cannot fit at least one option do not show drop-down\n        this.contextViewProvider.showContextView({\n            getAnchor: () => this.selectElement,\n            render: (container) => this.renderSelectDropDown(container, true),\n            layout: () => {\n                this.layoutSelectDropDown();\n            },\n            onHide: () => {\n                this.selectDropDownContainer.classList.remove('visible');\n                this.selectElement.classList.remove('synthetic-focus');\n            },\n            anchorPosition: this._dropDownPosition\n        }, this.selectBoxOptions.optionsAsChildren ? this.container : undefined);\n        // Hide so we can relay out\n        this._isVisible = true;\n        this.hideSelectDropDown(false);\n        this.contextViewProvider.showContextView({\n            getAnchor: () => this.selectElement,\n            render: (container) => this.renderSelectDropDown(container),\n            layout: () => this.layoutSelectDropDown(),\n            onHide: () => {\n                this.selectDropDownContainer.classList.remove('visible');\n                this.selectElement.classList.remove('synthetic-focus');\n            },\n            anchorPosition: this._dropDownPosition\n        }, this.selectBoxOptions.optionsAsChildren ? this.container : undefined);\n        // Track initial selection the case user escape, blur\n        this._currentSelection = this.selected;\n        this._isVisible = true;\n        this.selectElement.setAttribute('aria-expanded', 'true');\n    }\n    hideSelectDropDown(focusSelect) {\n        if (!this.contextViewProvider || !this._isVisible) {\n            return;\n        }\n        this._isVisible = false;\n        this.selectElement.setAttribute('aria-expanded', 'false');\n        if (focusSelect) {\n            this.selectElement.focus();\n        }\n        this.contextViewProvider.hideContextView();\n    }\n    renderSelectDropDown(container, preLayoutPosition) {\n        container.appendChild(this.selectDropDownContainer);\n        // Pre-Layout allows us to change position\n        this.layoutSelectDropDown(preLayoutPosition);\n        return {\n            dispose: () => {\n                // contextView will dispose itself if moving from one View to another\n                this.selectDropDownContainer.remove(); // remove to take out the CSS rules we add\n            }\n        };\n    }\n    // Iterate over detailed descriptions, find max height\n    measureMaxDetailsHeight() {\n        let maxDetailsPaneHeight = 0;\n        this.options.forEach((_option, index) => {\n            this.updateDetail(index);\n            if (this.selectionDetailsPane.offsetHeight > maxDetailsPaneHeight) {\n                maxDetailsPaneHeight = this.selectionDetailsPane.offsetHeight;\n            }\n        });\n        return maxDetailsPaneHeight;\n    }\n    layoutSelectDropDown(preLayoutPosition) {\n        // Avoid recursion from layout called in onListFocus\n        if (this._skipLayout) {\n            return false;\n        }\n        // Layout ContextView drop down select list and container\n        // Have to manage our vertical overflow, sizing, position below or above\n        // Position has to be determined and set prior to contextView instantiation\n        if (this.selectList) {\n            // Make visible to enable measurements\n            this.selectDropDownContainer.classList.add('visible');\n            const window = dom.getWindow(this.selectElement);\n            const selectPosition = dom.getDomNodePagePosition(this.selectElement);\n            const styles = dom.getWindow(this.selectElement).getComputedStyle(this.selectElement);\n            const verticalPadding = parseFloat(styles.getPropertyValue('--dropdown-padding-top')) + parseFloat(styles.getPropertyValue('--dropdown-padding-bottom'));\n            const maxSelectDropDownHeightBelow = (window.innerHeight - selectPosition.top - selectPosition.height - (this.selectBoxOptions.minBottomMargin || 0));\n            const maxSelectDropDownHeightAbove = (selectPosition.top - SelectBoxList.DEFAULT_DROPDOWN_MINIMUM_TOP_MARGIN);\n            // Determine optimal width - min(longest option), opt(parent select, excluding margins), max(ContextView controlled)\n            const selectWidth = this.selectElement.offsetWidth;\n            const selectMinWidth = this.setWidthControlElement(this.widthControlElement);\n            const selectOptimalWidth = Math.max(selectMinWidth, Math.round(selectWidth)).toString() + 'px';\n            this.selectDropDownContainer.style.width = selectOptimalWidth;\n            // Get initial list height and determine space above and below\n            this.selectList.getHTMLElement().style.height = '';\n            this.selectList.layout();\n            let listHeight = this.selectList.contentHeight;\n            if (this._hasDetails && this._cachedMaxDetailsHeight === undefined) {\n                this._cachedMaxDetailsHeight = this.measureMaxDetailsHeight();\n            }\n            const maxDetailsPaneHeight = this._hasDetails ? this._cachedMaxDetailsHeight : 0;\n            const minRequiredDropDownHeight = listHeight + verticalPadding + maxDetailsPaneHeight;\n            const maxVisibleOptionsBelow = ((Math.floor((maxSelectDropDownHeightBelow - verticalPadding - maxDetailsPaneHeight) / this.getHeight())));\n            const maxVisibleOptionsAbove = ((Math.floor((maxSelectDropDownHeightAbove - verticalPadding - maxDetailsPaneHeight) / this.getHeight())));\n            // If we are only doing pre-layout check/adjust position only\n            // Calculate vertical space available, flip up if insufficient\n            // Use reflected padding on parent select, ContextView style\n            // properties not available before DOM attachment\n            if (preLayoutPosition) {\n                // Check if select moved out of viewport , do not open\n                // If at least one option cannot be shown, don't open the drop-down or hide/remove if open\n                if ((selectPosition.top + selectPosition.height) > (window.innerHeight - 22)\n                    || selectPosition.top < SelectBoxList.DEFAULT_DROPDOWN_MINIMUM_TOP_MARGIN\n                    || ((maxVisibleOptionsBelow < 1) && (maxVisibleOptionsAbove < 1))) {\n                    // Indicate we cannot open\n                    return false;\n                }\n                // Determine if we have to flip up\n                // Always show complete list items - never more than Max available vertical height\n                if (maxVisibleOptionsBelow < SelectBoxList.DEFAULT_MINIMUM_VISIBLE_OPTIONS\n                    && maxVisibleOptionsAbove > maxVisibleOptionsBelow\n                    && this.options.length > maxVisibleOptionsBelow) {\n                    this._dropDownPosition = 1 /* AnchorPosition.ABOVE */;\n                    this.selectDropDownListContainer.remove();\n                    this.selectionDetailsPane.remove();\n                    this.selectDropDownContainer.appendChild(this.selectionDetailsPane);\n                    this.selectDropDownContainer.appendChild(this.selectDropDownListContainer);\n                    this.selectionDetailsPane.classList.remove('border-top');\n                    this.selectionDetailsPane.classList.add('border-bottom');\n                }\n                else {\n                    this._dropDownPosition = 0 /* AnchorPosition.BELOW */;\n                    this.selectDropDownListContainer.remove();\n                    this.selectionDetailsPane.remove();\n                    this.selectDropDownContainer.appendChild(this.selectDropDownListContainer);\n                    this.selectDropDownContainer.appendChild(this.selectionDetailsPane);\n                    this.selectionDetailsPane.classList.remove('border-bottom');\n                    this.selectionDetailsPane.classList.add('border-top');\n                }\n                // Do full layout on showSelectDropDown only\n                return true;\n            }\n            // Check if select out of viewport or cutting into status bar\n            if ((selectPosition.top + selectPosition.height) > (window.innerHeight - 22)\n                || selectPosition.top < SelectBoxList.DEFAULT_DROPDOWN_MINIMUM_TOP_MARGIN\n                || (this._dropDownPosition === 0 /* AnchorPosition.BELOW */ && maxVisibleOptionsBelow < 1)\n                || (this._dropDownPosition === 1 /* AnchorPosition.ABOVE */ && maxVisibleOptionsAbove < 1)) {\n                // Cannot properly layout, close and hide\n                this.hideSelectDropDown(true);\n                return false;\n            }\n            // SetUp list dimensions and layout - account for container padding\n            // Use position to check above or below available space\n            if (this._dropDownPosition === 0 /* AnchorPosition.BELOW */) {\n                if (this._isVisible && maxVisibleOptionsBelow + maxVisibleOptionsAbove < 1) {\n                    // If drop-down is visible, must be doing a DOM re-layout, hide since we don't fit\n                    // Hide drop-down, hide contextview, focus on parent select\n                    this.hideSelectDropDown(true);\n                    return false;\n                }\n                // Adjust list height to max from select bottom to margin (default/minBottomMargin)\n                if (minRequiredDropDownHeight > maxSelectDropDownHeightBelow) {\n                    listHeight = (maxVisibleOptionsBelow * this.getHeight());\n                }\n            }\n            else {\n                if (minRequiredDropDownHeight > maxSelectDropDownHeightAbove) {\n                    listHeight = (maxVisibleOptionsAbove * this.getHeight());\n                }\n            }\n            // Set adjusted list height and relayout\n            this.selectList.layout(listHeight);\n            this.selectList.domFocus();\n            // Finally set focus on selected item\n            if (this.selectList.length > 0) {\n                this.selectList.setFocus([this.selected || 0]);\n                this.selectList.reveal(this.selectList.getFocus()[0] || 0);\n            }\n            if (this._hasDetails) {\n                // Leave the selectDropDownContainer to size itself according to children (list + details) - #57447\n                this.selectList.getHTMLElement().style.height = (listHeight + verticalPadding) + 'px';\n                this.selectDropDownContainer.style.height = '';\n            }\n            else {\n                this.selectDropDownContainer.style.height = (listHeight + verticalPadding) + 'px';\n            }\n            this.updateDetail(this.selected);\n            this.selectDropDownContainer.style.width = selectOptimalWidth;\n            // Maintain focus outline on parent select as well as list container - tabindex for focus\n            this.selectDropDownListContainer.setAttribute('tabindex', '0');\n            this.selectElement.classList.add('synthetic-focus');\n            this.selectDropDownContainer.classList.add('synthetic-focus');\n            return true;\n        }\n        else {\n            return false;\n        }\n    }\n    setWidthControlElement(container) {\n        let elementWidth = 0;\n        if (container) {\n            let longest = 0;\n            let longestLength = 0;\n            this.options.forEach((option, index) => {\n                const detailLength = !!option.detail ? option.detail.length : 0;\n                const rightDecoratorLength = !!option.decoratorRight ? option.decoratorRight.length : 0;\n                const len = option.text.length + detailLength + rightDecoratorLength;\n                if (len > longestLength) {\n                    longest = index;\n                    longestLength = len;\n                }\n            });\n            container.textContent = this.options[longest].text + (!!this.options[longest].decoratorRight ? (this.options[longest].decoratorRight + ' ') : '');\n            elementWidth = dom.getTotalWidth(container);\n        }\n        return elementWidth;\n    }\n    createSelectList(parent) {\n        // If we have already constructive list on open, skip\n        if (this.selectList) {\n            return;\n        }\n        // SetUp container for list\n        this.selectDropDownListContainer = dom.append(parent, $('.select-box-dropdown-list-container'));\n        this.listRenderer = new SelectListRenderer();\n        this.selectList = this._register(new List('SelectBoxCustom', this.selectDropDownListContainer, this, [this.listRenderer], {\n            useShadows: false,\n            verticalScrollMode: 3 /* ScrollbarVisibility.Visible */,\n            keyboardSupport: false,\n            mouseSupport: false,\n            accessibilityProvider: {\n                getAriaLabel: element => {\n                    let label = element.text;\n                    if (element.detail) {\n                        label += `. ${element.detail}`;\n                    }\n                    if (element.decoratorRight) {\n                        label += `. ${element.decoratorRight}`;\n                    }\n                    if (element.description) {\n                        label += `. ${element.description}`;\n                    }\n                    return label;\n                },\n                getWidgetAriaLabel: () => localize({ key: 'selectBox', comment: ['Behave like native select dropdown element.'] }, \"Select Box\"),\n                getRole: () => isMacintosh ? '' : 'option',\n                getWidgetRole: () => 'listbox'\n            }\n        }));\n        if (this.selectBoxOptions.ariaLabel) {\n            this.selectList.ariaLabel = this.selectBoxOptions.ariaLabel;\n        }\n        // SetUp list keyboard controller - control navigation, disabled items, focus\n        const onKeyDown = this._register(new DomEmitter(this.selectDropDownListContainer, 'keydown'));\n        const onSelectDropDownKeyDown = Event.chain(onKeyDown.event, $ => $.filter(() => this.selectList.length > 0)\n            .map(e => new StandardKeyboardEvent(e)));\n        this._register(Event.chain(onSelectDropDownKeyDown, $ => $.filter(e => e.keyCode === 3 /* KeyCode.Enter */))(this.onEnter, this));\n        this._register(Event.chain(onSelectDropDownKeyDown, $ => $.filter(e => e.keyCode === 2 /* KeyCode.Tab */))(this.onEnter, this)); // Tab should behave the same as enter, #79339\n        this._register(Event.chain(onSelectDropDownKeyDown, $ => $.filter(e => e.keyCode === 9 /* KeyCode.Escape */))(this.onEscape, this));\n        this._register(Event.chain(onSelectDropDownKeyDown, $ => $.filter(e => e.keyCode === 16 /* KeyCode.UpArrow */))(this.onUpArrow, this));\n        this._register(Event.chain(onSelectDropDownKeyDown, $ => $.filter(e => e.keyCode === 18 /* KeyCode.DownArrow */))(this.onDownArrow, this));\n        this._register(Event.chain(onSelectDropDownKeyDown, $ => $.filter(e => e.keyCode === 12 /* KeyCode.PageDown */))(this.onPageDown, this));\n        this._register(Event.chain(onSelectDropDownKeyDown, $ => $.filter(e => e.keyCode === 11 /* KeyCode.PageUp */))(this.onPageUp, this));\n        this._register(Event.chain(onSelectDropDownKeyDown, $ => $.filter(e => e.keyCode === 14 /* KeyCode.Home */))(this.onHome, this));\n        this._register(Event.chain(onSelectDropDownKeyDown, $ => $.filter(e => e.keyCode === 13 /* KeyCode.End */))(this.onEnd, this));\n        this._register(Event.chain(onSelectDropDownKeyDown, $ => $.filter(e => (e.keyCode >= 21 /* KeyCode.Digit0 */ && e.keyCode <= 56 /* KeyCode.KeyZ */) || (e.keyCode >= 85 /* KeyCode.Semicolon */ && e.keyCode <= 113 /* KeyCode.NumpadDivide */)))(this.onCharacter, this));\n        // SetUp list mouse controller - control navigation, disabled items, focus\n        this._register(dom.addDisposableListener(this.selectList.getHTMLElement(), dom.EventType.POINTER_UP, e => this.onPointerUp(e)));\n        this._register(this.selectList.onMouseOver(e => typeof e.index !== 'undefined' && this.selectList.setFocus([e.index])));\n        this._register(this.selectList.onDidChangeFocus(e => this.onListFocus(e)));\n        this._register(dom.addDisposableListener(this.selectDropDownContainer, dom.EventType.FOCUS_OUT, e => {\n            if (!this._isVisible || dom.isAncestor(e.relatedTarget, this.selectDropDownContainer)) {\n                return;\n            }\n            this.onListBlur();\n        }));\n        this.selectList.getHTMLElement().setAttribute('aria-label', this.selectBoxOptions.ariaLabel || '');\n        this.selectList.getHTMLElement().setAttribute('aria-expanded', 'true');\n        this.styleList();\n    }\n    // List methods\n    // List mouse controller - active exit, select option, fire onDidSelect if change, return focus to parent select\n    // Also takes in touchend events\n    onPointerUp(e) {\n        if (!this.selectList.length) {\n            return;\n        }\n        dom.EventHelper.stop(e);\n        const target = e.target;\n        if (!target) {\n            return;\n        }\n        // Check our mouse event is on an option (not scrollbar)\n        if (target.classList.contains('slider')) {\n            return;\n        }\n        const listRowElement = target.closest('.monaco-list-row');\n        if (!listRowElement) {\n            return;\n        }\n        const index = Number(listRowElement.getAttribute('data-index'));\n        const disabled = listRowElement.classList.contains('option-disabled');\n        // Ignore mouse selection of disabled options\n        if (index >= 0 && index < this.options.length && !disabled) {\n            this.selected = index;\n            this.select(this.selected);\n            this.selectList.setFocus([this.selected]);\n            this.selectList.reveal(this.selectList.getFocus()[0]);\n            // Only fire if selection change\n            if (this.selected !== this._currentSelection) {\n                // Set current = selected\n                this._currentSelection = this.selected;\n                this._onDidSelect.fire({\n                    index: this.selectElement.selectedIndex,\n                    selected: this.options[this.selected].text\n                });\n                if (!!this.options[this.selected] && !!this.options[this.selected].text) {\n                    this.setTitle(this.options[this.selected].text);\n                }\n            }\n            this.hideSelectDropDown(true);\n        }\n    }\n    // List Exit - passive - implicit no selection change, hide drop-down\n    onListBlur() {\n        if (this._sticky) {\n            return;\n        }\n        if (this.selected !== this._currentSelection) {\n            // Reset selected to current if no change\n            this.select(this._currentSelection);\n        }\n        this.hideSelectDropDown(false);\n    }\n    renderDescriptionMarkdown(text, actionHandler) {\n        const cleanRenderedMarkdown = (element) => {\n            for (let i = 0; i < element.childNodes.length; i++) {\n                const child = element.childNodes.item(i);\n                const tagName = child.tagName && child.tagName.toLowerCase();\n                if (tagName === 'img') {\n                    child.remove();\n                }\n                else {\n                    cleanRenderedMarkdown(child);\n                }\n            }\n        };\n        const rendered = renderMarkdown({ value: text, supportThemeIcons: true }, { actionHandler });\n        rendered.element.classList.add('select-box-description-markdown');\n        cleanRenderedMarkdown(rendered.element);\n        return rendered.element;\n    }\n    // List Focus Change - passive - update details pane with newly focused element's data\n    onListFocus(e) {\n        // Skip during initial layout\n        if (!this._isVisible || !this._hasDetails) {\n            return;\n        }\n        this.updateDetail(e.indexes[0]);\n    }\n    updateDetail(selectedIndex) {\n        this.selectionDetailsPane.innerText = '';\n        const option = this.options[selectedIndex];\n        const description = option?.description ?? '';\n        const descriptionIsMarkdown = option?.descriptionIsMarkdown ?? false;\n        if (description) {\n            if (descriptionIsMarkdown) {\n                const actionHandler = option.descriptionMarkdownActionHandler;\n                this.selectionDetailsPane.appendChild(this.renderDescriptionMarkdown(description, actionHandler));\n            }\n            else {\n                this.selectionDetailsPane.innerText = description;\n            }\n            this.selectionDetailsPane.style.display = 'block';\n        }\n        else {\n            this.selectionDetailsPane.style.display = 'none';\n        }\n        // Avoid recursion\n        this._skipLayout = true;\n        this.contextViewProvider.layout();\n        this._skipLayout = false;\n    }\n    // List keyboard controller\n    // List exit - active - hide ContextView dropdown, reset selection, return focus to parent select\n    onEscape(e) {\n        dom.EventHelper.stop(e);\n        // Reset selection to value when opened\n        this.select(this._currentSelection);\n        this.hideSelectDropDown(true);\n    }\n    // List exit - active - hide ContextView dropdown, return focus to parent select, fire onDidSelect if change\n    onEnter(e) {\n        dom.EventHelper.stop(e);\n        // Only fire if selection change\n        if (this.selected !== this._currentSelection) {\n            this._currentSelection = this.selected;\n            this._onDidSelect.fire({\n                index: this.selectElement.selectedIndex,\n                selected: this.options[this.selected].text\n            });\n            if (!!this.options[this.selected] && !!this.options[this.selected].text) {\n                this.setTitle(this.options[this.selected].text);\n            }\n        }\n        this.hideSelectDropDown(true);\n    }\n    // List navigation - have to handle a disabled option (jump over)\n    onDownArrow(e) {\n        if (this.selected < this.options.length - 1) {\n            dom.EventHelper.stop(e, true);\n            // Skip disabled options\n            const nextOptionDisabled = this.options[this.selected + 1].isDisabled;\n            if (nextOptionDisabled && this.options.length > this.selected + 2) {\n                this.selected += 2;\n            }\n            else if (nextOptionDisabled) {\n                return;\n            }\n            else {\n                this.selected++;\n            }\n            // Set focus/selection - only fire event when closing drop-down or on blur\n            this.select(this.selected);\n            this.selectList.setFocus([this.selected]);\n            this.selectList.reveal(this.selectList.getFocus()[0]);\n        }\n    }\n    onUpArrow(e) {\n        if (this.selected > 0) {\n            dom.EventHelper.stop(e, true);\n            // Skip disabled options\n            const previousOptionDisabled = this.options[this.selected - 1].isDisabled;\n            if (previousOptionDisabled && this.selected > 1) {\n                this.selected -= 2;\n            }\n            else {\n                this.selected--;\n            }\n            // Set focus/selection - only fire event when closing drop-down or on blur\n            this.select(this.selected);\n            this.selectList.setFocus([this.selected]);\n            this.selectList.reveal(this.selectList.getFocus()[0]);\n        }\n    }\n    onPageUp(e) {\n        dom.EventHelper.stop(e);\n        this.selectList.focusPreviousPage();\n        // Allow scrolling to settle\n        setTimeout(() => {\n            this.selected = this.selectList.getFocus()[0];\n            // Shift selection down if we land on a disabled option\n            if (this.options[this.selected].isDisabled && this.selected < this.options.length - 1) {\n                this.selected++;\n                this.selectList.setFocus([this.selected]);\n            }\n            this.selectList.reveal(this.selected);\n            this.select(this.selected);\n        }, 1);\n    }\n    onPageDown(e) {\n        dom.EventHelper.stop(e);\n        this.selectList.focusNextPage();\n        // Allow scrolling to settle\n        setTimeout(() => {\n            this.selected = this.selectList.getFocus()[0];\n            // Shift selection up if we land on a disabled option\n            if (this.options[this.selected].isDisabled && this.selected > 0) {\n                this.selected--;\n                this.selectList.setFocus([this.selected]);\n            }\n            this.selectList.reveal(this.selected);\n            this.select(this.selected);\n        }, 1);\n    }\n    onHome(e) {\n        dom.EventHelper.stop(e);\n        if (this.options.length < 2) {\n            return;\n        }\n        this.selected = 0;\n        if (this.options[this.selected].isDisabled && this.selected > 1) {\n            this.selected++;\n        }\n        this.selectList.setFocus([this.selected]);\n        this.selectList.reveal(this.selected);\n        this.select(this.selected);\n    }\n    onEnd(e) {\n        dom.EventHelper.stop(e);\n        if (this.options.length < 2) {\n            return;\n        }\n        this.selected = this.options.length - 1;\n        if (this.options[this.selected].isDisabled && this.selected > 1) {\n            this.selected--;\n        }\n        this.selectList.setFocus([this.selected]);\n        this.selectList.reveal(this.selected);\n        this.select(this.selected);\n    }\n    // Mimic option first character navigation of native select\n    onCharacter(e) {\n        const ch = KeyCodeUtils.toString(e.keyCode);\n        let optionIndex = -1;\n        for (let i = 0; i < this.options.length - 1; i++) {\n            optionIndex = (i + this.selected + 1) % this.options.length;\n            if (this.options[optionIndex].text.charAt(0).toUpperCase() === ch && !this.options[optionIndex].isDisabled) {\n                this.select(optionIndex);\n                this.selectList.setFocus([optionIndex]);\n                this.selectList.reveal(this.selectList.getFocus()[0]);\n                dom.EventHelper.stop(e);\n                break;\n            }\n        }\n    }\n    dispose() {\n        this.hideSelectDropDown(false);\n        super.dispose();\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,KAAKA,GAAG,MAAM,cAAc;AACnC,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,SAASC,qBAAqB,QAAQ,wBAAwB;AAC9D,SAASC,cAAc,QAAQ,2BAA2B;AAC1D,SAASC,yBAAyB,QAAQ,4BAA4B;AACtE,SAASC,uBAAuB,QAAQ,kCAAkC;AAC1E,SAASC,IAAI,QAAQ,uBAAuB;AAC5C,OAAO,KAAKC,MAAM,MAAM,2BAA2B;AACnD,SAASC,OAAO,EAAEC,KAAK,QAAQ,0BAA0B;AACzD,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,UAAU,QAAQ,8BAA8B;AACzD,SAASC,WAAW,QAAQ,6BAA6B;AACzD,OAAO,uBAAuB;AAC9B,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,MAAMC,CAAC,GAAGd,GAAG,CAACc,CAAC;AACf,MAAMC,+BAA+B,GAAG,6BAA6B;AACrE,MAAMC,kBAAkB,CAAC;EACrB,IAAIC,UAAUA,CAAA,EAAG;IAAE,OAAOF,+BAA+B;EAAE;EAC3DG,cAAcA,CAACC,SAAS,EAAE;IACtB,MAAMC,IAAI,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAChCF,IAAI,CAACG,IAAI,GAAGJ,SAAS;IACrBC,IAAI,CAACI,IAAI,GAAGxB,GAAG,CAACyB,MAAM,CAACN,SAAS,EAAEL,CAAC,CAAC,cAAc,CAAC,CAAC;IACpDM,IAAI,CAACM,MAAM,GAAG1B,GAAG,CAACyB,MAAM,CAACN,SAAS,EAAEL,CAAC,CAAC,gBAAgB,CAAC,CAAC;IACxDM,IAAI,CAACO,cAAc,GAAG3B,GAAG,CAACyB,MAAM,CAACN,SAAS,EAAEL,CAAC,CAAC,yBAAyB,CAAC,CAAC;IACzE,OAAOM,IAAI;EACf;EACAQ,aAAaA,CAACC,OAAO,EAAEC,KAAK,EAAEC,YAAY,EAAE;IACxC,MAAMX,IAAI,GAAGW,YAAY;IACzB,MAAMP,IAAI,GAAGK,OAAO,CAACL,IAAI;IACzB,MAAME,MAAM,GAAGG,OAAO,CAACH,MAAM;IAC7B,MAAMC,cAAc,GAAGE,OAAO,CAACF,cAAc;IAC7C,MAAMK,UAAU,GAAGH,OAAO,CAACG,UAAU;IACrCZ,IAAI,CAACI,IAAI,CAACS,WAAW,GAAGT,IAAI;IAC5BJ,IAAI,CAACM,MAAM,CAACO,WAAW,GAAG,CAAC,CAACP,MAAM,GAAGA,MAAM,GAAG,EAAE;IAChDN,IAAI,CAACO,cAAc,CAACO,SAAS,GAAG,CAAC,CAACP,cAAc,GAAGA,cAAc,GAAG,EAAE;IACtE;IACA,IAAIK,UAAU,EAAE;MACZZ,IAAI,CAACG,IAAI,CAACY,SAAS,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAC9C,CAAC,MACI;MACD;MACAhB,IAAI,CAACG,IAAI,CAACY,SAAS,CAACE,MAAM,CAAC,iBAAiB,CAAC;IACjD;EACJ;EACAC,eAAeA,CAACC,aAAa,EAAE;IAC3B;EAAA;AAER;AACA,WAAaC,aAAa;EAAnB,MAAMA,aAAa,SAAS7B,UAAU,CAAC;IAC1C;MAAS,IAAI,CAAC8B,sCAAsC,GAAG,EAAE;IAAE;IAC3D;MAAS,IAAI,CAACC,mCAAmC,GAAG,CAAC;IAAE;IACvD;MAAS,IAAI,CAACC,+BAA+B,GAAG,CAAC;IAAE;IACnDC,WAAWA,CAACC,OAAO,EAAEC,QAAQ,EAAEC,mBAAmB,EAAEC,MAAM,EAAEC,gBAAgB,EAAE;MAC1E,KAAK,CAAC,CAAC;MACP,IAAI,CAACJ,OAAO,GAAG,EAAE;MACjB,IAAI,CAACK,iBAAiB,GAAG,CAAC;MAC1B,IAAI,CAACC,WAAW,GAAG,KAAK;MACxB,IAAI,CAACC,WAAW,GAAG,KAAK;MACxB,IAAI,CAACC,OAAO,GAAG,KAAK,CAAC,CAAC;MACtB,IAAI,CAACC,UAAU,GAAG,KAAK;MACvB,IAAI,CAACN,MAAM,GAAGA,MAAM;MACpB,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB,IAAI5B,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;MAC/D,IAAI,OAAO,IAAI,CAAC2B,gBAAgB,CAACM,eAAe,KAAK,QAAQ,EAAE;QAC3D,IAAI,CAACN,gBAAgB,CAACM,eAAe,GAAGf,aAAa,CAACC,sCAAsC;MAChG,CAAC,MACI,IAAI,IAAI,CAACQ,gBAAgB,CAACM,eAAe,GAAG,CAAC,EAAE;QAChD,IAAI,CAACN,gBAAgB,CAACM,eAAe,GAAG,CAAC;MAC7C;MACA,IAAI,CAACC,aAAa,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;MACrD;MACA,IAAI,CAACF,aAAa,CAACG,SAAS,GAAG,sDAAsD;MACrF,IAAI,OAAO,IAAI,CAACV,gBAAgB,CAACW,SAAS,KAAK,QAAQ,EAAE;QACrD,IAAI,CAACJ,aAAa,CAACK,YAAY,CAAC,YAAY,EAAE,IAAI,CAACZ,gBAAgB,CAACW,SAAS,CAAC;MAClF;MACA,IAAI,OAAO,IAAI,CAACX,gBAAgB,CAACa,eAAe,KAAK,QAAQ,EAAE;QAC3D,IAAI,CAACN,aAAa,CAACK,YAAY,CAAC,kBAAkB,EAAE,IAAI,CAACZ,gBAAgB,CAACa,eAAe,CAAC;MAC9F;MACA,IAAI,CAACC,YAAY,GAAG,IAAIvD,OAAO,CAAC,CAAC;MACjC,IAAI,CAACwD,SAAS,CAAC,IAAI,CAACD,YAAY,CAAC;MACjC,IAAI,CAACE,iBAAiB,CAAC,CAAC;MACxB,IAAI,CAACC,uBAAuB,CAACnB,mBAAmB,CAAC;MACjD,IAAI,CAACD,QAAQ,GAAGA,QAAQ,IAAI,CAAC;MAC7B,IAAID,OAAO,EAAE;QACT,IAAI,CAACsB,UAAU,CAACtB,OAAO,EAAEC,QAAQ,CAAC;MACtC;MACA,IAAI,CAACsB,cAAc,CAAC,CAAC;IACzB;IACAC,QAAQA,CAACC,KAAK,EAAE;MACZ,IAAI,CAAC,IAAI,CAACC,MAAM,IAAID,KAAK,EAAE;QACvB,IAAI,CAACC,MAAM,GAAG,IAAI,CAACP,SAAS,CAAC5D,yBAAyB,CAAC,CAAC,CAACoE,iBAAiB,CAACnE,uBAAuB,CAAC,OAAO,CAAC,EAAE,IAAI,CAACmD,aAAa,EAAEc,KAAK,CAAC,CAAC;MAC5I,CAAC,MACI,IAAI,IAAI,CAACC,MAAM,EAAE;QAClB,IAAI,CAACA,MAAM,CAACE,MAAM,CAACH,KAAK,CAAC;MAC7B;IACJ;IACA;IACAI,SAASA,CAAA,EAAG;MACR,OAAO,EAAE;IACb;IACAC,aAAaA,CAAA,EAAG;MACZ,OAAO5D,+BAA+B;IAC1C;IACAmD,uBAAuBA,CAACnB,mBAAmB,EAAE;MACzC;MACA,IAAI,CAACA,mBAAmB,GAAGA,mBAAmB;MAC9C,IAAI,CAAC6B,uBAAuB,GAAG5E,GAAG,CAACc,CAAC,CAAC,uCAAuC,CAAC;MAC7E;MACA,IAAI,CAAC8D,uBAAuB,CAACzC,SAAS,CAACC,GAAG,CAAC,oCAAoC,CAAC;MAChF;MACA,IAAI,CAACyC,oBAAoB,GAAG7E,GAAG,CAACyB,MAAM,CAAC,IAAI,CAACmD,uBAAuB,EAAE9D,CAAC,CAAC,0BAA0B,CAAC,CAAC;MACnG;MACA,MAAMgE,oBAAoB,GAAG9E,GAAG,CAACyB,MAAM,CAAC,IAAI,CAACmD,uBAAuB,EAAE9D,CAAC,CAAC,8CAA8C,CAAC,CAAC;MACxH,MAAMiE,oBAAoB,GAAG/E,GAAG,CAACyB,MAAM,CAACqD,oBAAoB,EAAEhE,CAAC,CAAC,oBAAoB,CAAC,CAAC;MACtF,IAAI,CAACkE,mBAAmB,GAAGvB,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;MACzD,IAAI,CAACsB,mBAAmB,CAACrB,SAAS,GAAG,2BAA2B;MAChE3D,GAAG,CAACyB,MAAM,CAACsD,oBAAoB,EAAE,IAAI,CAACC,mBAAmB,CAAC;MAC1D;MACA,IAAI,CAACC,iBAAiB,GAAG,CAAC,CAAC;MAC3B;MACA,IAAI,CAACC,YAAY,GAAGlF,GAAG,CAACmF,gBAAgB,CAAC,IAAI,CAACP,uBAAuB,CAAC;MACtE;MACA,IAAI,CAACA,uBAAuB,CAACf,YAAY,CAAC,WAAW,EAAE,MAAM,CAAC;MAC9D,IAAI,CAACG,SAAS,CAAChE,GAAG,CAACoF,qBAAqB,CAAC,IAAI,CAACR,uBAAuB,EAAE5E,GAAG,CAACqF,SAAS,CAACC,UAAU,EAAGC,CAAC,IAAK;QACpGvF,GAAG,CAACwF,WAAW,CAACC,IAAI,CAACF,CAAC,EAAE,IAAI,CAAC;MACjC,CAAC,CAAC,CAAC;IACP;IACAtB,iBAAiBA,CAAA,EAAG;MAChB;MACA,IAAI,CAACD,SAAS,CAAChE,GAAG,CAAC0F,6BAA6B,CAAC,IAAI,CAAClC,aAAa,EAAE,QAAQ,EAAG+B,CAAC,IAAK;QAClF,IAAI,CAACzC,QAAQ,GAAGyC,CAAC,CAACI,MAAM,CAACC,aAAa;QACtC,IAAI,CAAC7B,YAAY,CAAC8B,IAAI,CAAC;UACnB/D,KAAK,EAAEyD,CAAC,CAACI,MAAM,CAACC,aAAa;UAC7B9C,QAAQ,EAAEyC,CAAC,CAACI,MAAM,CAACG;QACvB,CAAC,CAAC;QACF,IAAI,CAAC,CAAC,IAAI,CAACjD,OAAO,CAAC,IAAI,CAACC,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,CAACD,OAAO,CAAC,IAAI,CAACC,QAAQ,CAAC,CAACtB,IAAI,EAAE;UACrE,IAAI,CAAC6C,QAAQ,CAAC,IAAI,CAACxB,OAAO,CAAC,IAAI,CAACC,QAAQ,CAAC,CAACtB,IAAI,CAAC;QACnD;MACJ,CAAC,CAAC,CAAC;MACH;MACA;MACA,IAAI,CAACwC,SAAS,CAAChE,GAAG,CAACoF,qBAAqB,CAAC,IAAI,CAAC5B,aAAa,EAAExD,GAAG,CAACqF,SAAS,CAACU,KAAK,EAAGR,CAAC,IAAK;QACrFvF,GAAG,CAACwF,WAAW,CAACC,IAAI,CAACF,CAAC,CAAC;QACvB,IAAI,IAAI,CAACjC,UAAU,EAAE;UACjB,IAAI,CAAC0C,kBAAkB,CAAC,IAAI,CAAC;QACjC,CAAC,MACI;UACD,IAAI,CAACC,kBAAkB,CAAC,CAAC;QAC7B;MACJ,CAAC,CAAC,CAAC;MACH,IAAI,CAACjC,SAAS,CAAChE,GAAG,CAACoF,qBAAqB,CAAC,IAAI,CAAC5B,aAAa,EAAExD,GAAG,CAACqF,SAAS,CAACa,UAAU,EAAGX,CAAC,IAAK;QAC1FvF,GAAG,CAACwF,WAAW,CAACC,IAAI,CAACF,CAAC,CAAC;MAC3B,CAAC,CAAC,CAAC;MACH;MACA;MACA;MACA,IAAIY,yBAAyB;MAC7B,IAAI,CAACnC,SAAS,CAAChE,GAAG,CAACoF,qBAAqB,CAAC,IAAI,CAAC5B,aAAa,EAAE,YAAY,EAAG+B,CAAC,IAAK;QAC9EY,yBAAyB,GAAG,IAAI,CAAC7C,UAAU;MAC/C,CAAC,CAAC,CAAC;MACH,IAAI,CAACU,SAAS,CAAChE,GAAG,CAACoF,qBAAqB,CAAC,IAAI,CAAC5B,aAAa,EAAE,UAAU,EAAG+B,CAAC,IAAK;QAC5EvF,GAAG,CAACwF,WAAW,CAACC,IAAI,CAACF,CAAC,CAAC;QACvB,IAAIY,yBAAyB,EAAE;UAC3B,IAAI,CAACH,kBAAkB,CAAC,IAAI,CAAC;QACjC,CAAC,MACI;UACD,IAAI,CAACC,kBAAkB,CAAC,CAAC;QAC7B;MACJ,CAAC,CAAC,CAAC;MACH;MACA,IAAI,CAACjC,SAAS,CAAChE,GAAG,CAACoF,qBAAqB,CAAC,IAAI,CAAC5B,aAAa,EAAExD,GAAG,CAACqF,SAAS,CAACe,QAAQ,EAAGb,CAAC,IAAK;QACxF,MAAMc,KAAK,GAAG,IAAInG,qBAAqB,CAACqF,CAAC,CAAC;QAC1C,IAAIe,YAAY,GAAG,KAAK;QACxB;QACA,IAAI1F,WAAW,EAAE;UACb,IAAIyF,KAAK,CAACE,OAAO,KAAK,EAAE,CAAC,2BAA2BF,KAAK,CAACE,OAAO,KAAK,EAAE,CAAC,yBAAyBF,KAAK,CAACE,OAAO,KAAK,EAAE,CAAC,uBAAuBF,KAAK,CAACE,OAAO,KAAK,CAAC,CAAC,qBAAqB;YACnLD,YAAY,GAAG,IAAI;UACvB;QACJ,CAAC,MACI;UACD,IAAID,KAAK,CAACE,OAAO,KAAK,EAAE,CAAC,2BAA2BF,KAAK,CAACG,MAAM,IAAIH,KAAK,CAACE,OAAO,KAAK,EAAE,CAAC,yBAAyBF,KAAK,CAACG,MAAM,IAAIH,KAAK,CAACE,OAAO,KAAK,EAAE,CAAC,uBAAuBF,KAAK,CAACE,OAAO,KAAK,CAAC,CAAC,qBAAqB;YACnND,YAAY,GAAG,IAAI;UACvB;QACJ;QACA,IAAIA,YAAY,EAAE;UACd,IAAI,CAACL,kBAAkB,CAAC,CAAC;UACzBjG,GAAG,CAACwF,WAAW,CAACC,IAAI,CAACF,CAAC,EAAE,IAAI,CAAC;QACjC;MACJ,CAAC,CAAC,CAAC;IACP;IACA,IAAIkB,WAAWA,CAAA,EAAG;MACd,OAAO,IAAI,CAAC1C,YAAY,CAACsC,KAAK;IAClC;IACAlC,UAAUA,CAACtB,OAAO,EAAEC,QAAQ,EAAE;MAC1B,IAAI,CAACvC,MAAM,CAACmG,MAAM,CAAC,IAAI,CAAC7D,OAAO,EAAEA,OAAO,CAAC,EAAE;QACvC,IAAI,CAACA,OAAO,GAAGA,OAAO;QACtB,IAAI,CAACW,aAAa,CAACX,OAAO,CAAC8D,MAAM,GAAG,CAAC;QACrC,IAAI,CAACxD,WAAW,GAAG,KAAK;QACxB,IAAI,CAACyD,uBAAuB,GAAGC,SAAS;QACxC,IAAI,CAAChE,OAAO,CAACiE,OAAO,CAAC,CAACC,MAAM,EAAEjF,KAAK,KAAK;UACpC,IAAI,CAAC0B,aAAa,CAACpB,GAAG,CAAC,IAAI,CAAC4E,YAAY,CAACD,MAAM,CAACvF,IAAI,EAAEM,KAAK,EAAEiF,MAAM,CAAC/E,UAAU,CAAC,CAAC;UAChF,IAAI,OAAO+E,MAAM,CAACE,WAAW,KAAK,QAAQ,EAAE;YACxC,IAAI,CAAC9D,WAAW,GAAG,IAAI;UAC3B;QACJ,CAAC,CAAC;MACN;MACA,IAAIL,QAAQ,KAAK+D,SAAS,EAAE;QACxB,IAAI,CAACK,MAAM,CAACpE,QAAQ,CAAC;QACrB;QACA,IAAI,CAACI,iBAAiB,GAAG,IAAI,CAACJ,QAAQ;MAC1C;IACJ;IACAqE,cAAcA,CAAA,EAAG;MACb;MACA;MACA,IAAI,CAACC,UAAU,EAAEC,MAAM,CAAC,CAAC,EAAE,IAAI,CAACD,UAAU,CAACT,MAAM,EAAE,IAAI,CAAC9D,OAAO,CAAC;IACpE;IACAqE,MAAMA,CAACpF,KAAK,EAAE;MACV,IAAIA,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAG,IAAI,CAACe,OAAO,CAAC8D,MAAM,EAAE;QAC3C,IAAI,CAAC7D,QAAQ,GAAGhB,KAAK;MACzB,CAAC,MACI,IAAIA,KAAK,GAAG,IAAI,CAACe,OAAO,CAAC8D,MAAM,GAAG,CAAC,EAAE;QACtC;QACA;QACA,IAAI,CAACO,MAAM,CAAC,IAAI,CAACrE,OAAO,CAAC8D,MAAM,GAAG,CAAC,CAAC;MACxC,CAAC,MACI,IAAI,IAAI,CAAC7D,QAAQ,GAAG,CAAC,EAAE;QACxB,IAAI,CAACA,QAAQ,GAAG,CAAC;MACrB;MACA,IAAI,CAACU,aAAa,CAACoC,aAAa,GAAG,IAAI,CAAC9C,QAAQ;MAChD,IAAI,CAAC,CAAC,IAAI,CAACD,OAAO,CAAC,IAAI,CAACC,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,CAACD,OAAO,CAAC,IAAI,CAACC,QAAQ,CAAC,CAACtB,IAAI,EAAE;QACrE,IAAI,CAAC6C,QAAQ,CAAC,IAAI,CAACxB,OAAO,CAAC,IAAI,CAACC,QAAQ,CAAC,CAACtB,IAAI,CAAC;MACnD;IACJ;IACA8F,KAAKA,CAAA,EAAG;MACJ,IAAI,IAAI,CAAC9D,aAAa,EAAE;QACpB,IAAI,CAACA,aAAa,CAAC+D,QAAQ,GAAG,CAAC;QAC/B,IAAI,CAAC/D,aAAa,CAAC8D,KAAK,CAAC,CAAC;MAC9B;IACJ;IACAE,IAAIA,CAAA,EAAG;MACH,IAAI,IAAI,CAAChE,aAAa,EAAE;QACpB,IAAI,CAACA,aAAa,CAAC+D,QAAQ,GAAG,CAAC,CAAC;QAChC,IAAI,CAAC/D,aAAa,CAACgE,IAAI,CAAC,CAAC;MAC7B;IACJ;IACAC,YAAYA,CAACC,SAAS,EAAE;MACpB,IAAI,CAAClE,aAAa,CAAC+D,QAAQ,GAAGG,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC;IACpD;IACAC,MAAMA,CAACxG,SAAS,EAAE;MACd,IAAI,CAACA,SAAS,GAAGA,SAAS;MAC1BA,SAAS,CAACgB,SAAS,CAACC,GAAG,CAAC,kBAAkB,CAAC;MAC3CjB,SAAS,CAACyG,WAAW,CAAC,IAAI,CAACpE,aAAa,CAAC;MACzC,IAAI,CAACqE,kBAAkB,CAAC,CAAC;IAC7B;IACAzD,cAAcA,CAAA,EAAG;MACb,MAAM0D,OAAO,GAAG,EAAE;MAClB;MACA,IAAI,IAAI,CAAC9E,MAAM,CAAC+E,mBAAmB,EAAE;QACjCD,OAAO,CAACE,IAAI,CAAC,yIAAyI,IAAI,CAAChF,MAAM,CAAC+E,mBAAmB,gBAAgB,CAAC;MAC1M;MACA,IAAI,IAAI,CAAC/E,MAAM,CAACiF,mBAAmB,EAAE;QACjCH,OAAO,CAACE,IAAI,CAAC,8HAA8H,IAAI,CAAChF,MAAM,CAACiF,mBAAmB,gBAAgB,CAAC;MAC/L;MACA,IAAI,IAAI,CAACjF,MAAM,CAACkF,wBAAwB,EAAE;QACtCJ,OAAO,CAACE,IAAI,CAAC,4JAA4J,IAAI,CAAChF,MAAM,CAACkF,wBAAwB,KAAK,CAAC;MACvN;MACA,IAAI,IAAI,CAAClF,MAAM,CAACmF,gBAAgB,IAAI,IAAI,CAACnF,MAAM,CAACoF,YAAY,IAAI,IAAI,CAACpF,MAAM,CAACoF,YAAY,KAAK,IAAI,CAACpF,MAAM,CAACmF,gBAAgB,EAAE;QACvHL,OAAO,CAACE,IAAI,CAAC,6DAA6D,IAAI,CAAChF,MAAM,CAACoF,YAAY,KAAK,CAAC;QACxGN,OAAO,CAACE,IAAI,CAAC,uGAAuG,IAAI,CAAChF,MAAM,CAACoF,YAAY,KAAK,CAAC;QAClJN,OAAO,CAACE,IAAI,CAAC,6GAA6G,IAAI,CAAChF,MAAM,CAACoF,YAAY,KAAK,CAAC;MAC5J,CAAC,MACI,IAAI,IAAI,CAACpF,MAAM,CAACqF,gBAAgB,EAAE;QACnCP,OAAO,CAACE,IAAI,CAAC,uGAAuG,IAAI,CAAChF,MAAM,CAACqF,gBAAgB,KAAK,CAAC;QACtJP,OAAO,CAACE,IAAI,CAAC,6GAA6G,IAAI,CAAChF,MAAM,CAACqF,gBAAgB,KAAK,CAAC;MAChK;MACA;MACA,IAAI,IAAI,CAACrF,MAAM,CAACsF,mBAAmB,EAAE;QACjCR,OAAO,CAACE,IAAI,CAAC,gKAAgK,IAAI,CAAChF,MAAM,CAACsF,mBAAmB,gBAAgB,CAAC;MACjO;MACA;MACA,IAAI,IAAI,CAACtF,MAAM,CAACuF,mBAAmB,EAAE;QACjCT,OAAO,CAACE,IAAI,CAAC,2KAA2K,IAAI,CAAChF,MAAM,CAACuF,mBAAmB,gBAAgB,CAAC;MAC5O;MACA;MACA,IAAI,IAAI,CAACvF,MAAM,CAACwF,gBAAgB,EAAE;QAC9BV,OAAO,CAACE,IAAI,CAAC,6IAA6I,IAAI,CAAChF,MAAM,CAACwF,gBAAgB,mDAAmD,CAAC;MAC9O;MACA,IAAI,IAAI,CAACxF,MAAM,CAACyF,gBAAgB,EAAE;QAC9BX,OAAO,CAACE,IAAI,CAAC,+KAA+K,IAAI,CAAChF,MAAM,CAACyF,gBAAgB,mDAAmD,CAAC;MAChR;MACA;MACAX,OAAO,CAACE,IAAI,CAAC,sOAAsO,CAAC;MACpPF,OAAO,CAACE,IAAI,CAAC,oOAAoO,CAAC;MAClP,IAAI,CAAC9C,YAAY,CAACjD,WAAW,GAAG6F,OAAO,CAACY,IAAI,CAAC,IAAI,CAAC;IACtD;IACAb,kBAAkBA,CAAA,EAAG;MACjB,MAAMc,UAAU,GAAG,IAAI,CAAC3F,MAAM,CAACmF,gBAAgB,IAAI,EAAE;MACrD,MAAMS,UAAU,GAAG,IAAI,CAAC5F,MAAM,CAAC6F,gBAAgB,IAAI,EAAE;MACrD,MAAMC,MAAM,GAAG,IAAI,CAAC9F,MAAM,CAACoF,YAAY,IAAI,EAAE;MAC7C,IAAI,CAAC5E,aAAa,CAACuF,KAAK,CAACC,eAAe,GAAGL,UAAU;MACrD,IAAI,CAACnF,aAAa,CAACuF,KAAK,CAACE,KAAK,GAAGL,UAAU;MAC3C,IAAI,CAACpF,aAAa,CAACuF,KAAK,CAACG,WAAW,GAAGJ,MAAM;IACjD;IACAK,SAASA,CAAA,EAAG;MACR,MAAMR,UAAU,GAAG,IAAI,CAAC3F,MAAM,CAACmF,gBAAgB,IAAI,EAAE;MACrD,MAAMiB,cAAc,GAAGpJ,GAAG,CAACqJ,qBAAqB,CAAC,IAAI,CAACrG,MAAM,CAACsG,oBAAoB,EAAEX,UAAU,CAAC;MAC9F,IAAI,CAACY,2BAA2B,CAACR,KAAK,CAACC,eAAe,GAAGI,cAAc;MACvE,IAAI,CAACvE,oBAAoB,CAACkE,KAAK,CAACC,eAAe,GAAGI,cAAc;MAChE,MAAMI,aAAa,GAAG,IAAI,CAACxG,MAAM,CAACyG,WAAW,IAAI,EAAE;MACnD,IAAI,CAAC7E,uBAAuB,CAACmE,KAAK,CAACW,YAAY,GAAGF,aAAa;MAC/D,IAAI,CAAC5E,uBAAuB,CAACmE,KAAK,CAACY,aAAa,GAAG,MAAM;MACzD,IAAI,CAACvC,UAAU,CAAC2B,KAAK,CAAC,IAAI,CAAC/F,MAAM,CAAC;IACtC;IACAgE,YAAYA,CAAClB,KAAK,EAAEhE,KAAK,EAAE8H,QAAQ,EAAE;MACjC,MAAM7C,MAAM,GAAGtD,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;MAC/CqD,MAAM,CAACjB,KAAK,GAAGA,KAAK;MACpBiB,MAAM,CAACvF,IAAI,GAAGsE,KAAK;MACnBiB,MAAM,CAAC6C,QAAQ,GAAG,CAAC,CAACA,QAAQ;MAC5B,OAAO7C,MAAM;IACjB;IACA;IACAd,kBAAkBA,CAAA,EAAG;MACjB,IAAI,CAACpB,oBAAoB,CAAC3C,SAAS,GAAG,EAAE;MACxC,IAAI,CAAC,IAAI,CAACa,mBAAmB,IAAI,IAAI,CAACO,UAAU,EAAE;QAC9C;MACJ;MACA;MACA,IAAI,CAACuG,gBAAgB,CAAC,IAAI,CAACjF,uBAAuB,CAAC;MACnD,IAAI,CAACuC,cAAc,CAAC,CAAC;MACrB;MACA;MACA;MACA,IAAI,CAACpE,mBAAmB,CAAC+G,eAAe,CAAC;QACrCC,SAAS,EAAEA,CAAA,KAAM,IAAI,CAACvG,aAAa;QACnCmE,MAAM,EAAGxG,SAAS,IAAK,IAAI,CAAC6I,oBAAoB,CAAC7I,SAAS,EAAE,IAAI,CAAC;QACjE8I,MAAM,EAAEA,CAAA,KAAM;UACV,IAAI,CAACC,oBAAoB,CAAC,CAAC;QAC/B,CAAC;QACDC,MAAM,EAAEA,CAAA,KAAM;UACV,IAAI,CAACvF,uBAAuB,CAACzC,SAAS,CAACE,MAAM,CAAC,SAAS,CAAC;UACxD,IAAI,CAACmB,aAAa,CAACrB,SAAS,CAACE,MAAM,CAAC,iBAAiB,CAAC;QAC1D,CAAC;QACD+H,cAAc,EAAE,IAAI,CAACnF;MACzB,CAAC,EAAE,IAAI,CAAChC,gBAAgB,CAACoH,iBAAiB,GAAG,IAAI,CAAClJ,SAAS,GAAG0F,SAAS,CAAC;MACxE;MACA,IAAI,CAACvD,UAAU,GAAG,IAAI;MACtB,IAAI,CAAC0C,kBAAkB,CAAC,KAAK,CAAC;MAC9B,IAAI,CAACjD,mBAAmB,CAAC+G,eAAe,CAAC;QACrCC,SAAS,EAAEA,CAAA,KAAM,IAAI,CAACvG,aAAa;QACnCmE,MAAM,EAAGxG,SAAS,IAAK,IAAI,CAAC6I,oBAAoB,CAAC7I,SAAS,CAAC;QAC3D8I,MAAM,EAAEA,CAAA,KAAM,IAAI,CAACC,oBAAoB,CAAC,CAAC;QACzCC,MAAM,EAAEA,CAAA,KAAM;UACV,IAAI,CAACvF,uBAAuB,CAACzC,SAAS,CAACE,MAAM,CAAC,SAAS,CAAC;UACxD,IAAI,CAACmB,aAAa,CAACrB,SAAS,CAACE,MAAM,CAAC,iBAAiB,CAAC;QAC1D,CAAC;QACD+H,cAAc,EAAE,IAAI,CAACnF;MACzB,CAAC,EAAE,IAAI,CAAChC,gBAAgB,CAACoH,iBAAiB,GAAG,IAAI,CAAClJ,SAAS,GAAG0F,SAAS,CAAC;MACxE;MACA,IAAI,CAAC3D,iBAAiB,GAAG,IAAI,CAACJ,QAAQ;MACtC,IAAI,CAACQ,UAAU,GAAG,IAAI;MACtB,IAAI,CAACE,aAAa,CAACK,YAAY,CAAC,eAAe,EAAE,MAAM,CAAC;IAC5D;IACAmC,kBAAkBA,CAACsE,WAAW,EAAE;MAC5B,IAAI,CAAC,IAAI,CAACvH,mBAAmB,IAAI,CAAC,IAAI,CAACO,UAAU,EAAE;QAC/C;MACJ;MACA,IAAI,CAACA,UAAU,GAAG,KAAK;MACvB,IAAI,CAACE,aAAa,CAACK,YAAY,CAAC,eAAe,EAAE,OAAO,CAAC;MACzD,IAAIyG,WAAW,EAAE;QACb,IAAI,CAAC9G,aAAa,CAAC8D,KAAK,CAAC,CAAC;MAC9B;MACA,IAAI,CAACvE,mBAAmB,CAACwH,eAAe,CAAC,CAAC;IAC9C;IACAP,oBAAoBA,CAAC7I,SAAS,EAAEqJ,iBAAiB,EAAE;MAC/CrJ,SAAS,CAACyG,WAAW,CAAC,IAAI,CAAChD,uBAAuB,CAAC;MACnD;MACA,IAAI,CAACsF,oBAAoB,CAACM,iBAAiB,CAAC;MAC5C,OAAO;QACHC,OAAO,EAAEA,CAAA,KAAM;UACX;UACA,IAAI,CAAC7F,uBAAuB,CAACvC,MAAM,CAAC,CAAC,CAAC,CAAC;QAC3C;MACJ,CAAC;IACL;IACA;IACAqI,uBAAuBA,CAAA,EAAG;MACtB,IAAIC,oBAAoB,GAAG,CAAC;MAC5B,IAAI,CAAC9H,OAAO,CAACiE,OAAO,CAAC,CAAC8D,OAAO,EAAE9I,KAAK,KAAK;QACrC,IAAI,CAAC+I,YAAY,CAAC/I,KAAK,CAAC;QACxB,IAAI,IAAI,CAAC+C,oBAAoB,CAACiG,YAAY,GAAGH,oBAAoB,EAAE;UAC/DA,oBAAoB,GAAG,IAAI,CAAC9F,oBAAoB,CAACiG,YAAY;QACjE;MACJ,CAAC,CAAC;MACF,OAAOH,oBAAoB;IAC/B;IACAT,oBAAoBA,CAACM,iBAAiB,EAAE;MACpC;MACA,IAAI,IAAI,CAACpH,WAAW,EAAE;QAClB,OAAO,KAAK;MAChB;MACA;MACA;MACA;MACA,IAAI,IAAI,CAACgE,UAAU,EAAE;QACjB;QACA,IAAI,CAACxC,uBAAuB,CAACzC,SAAS,CAACC,GAAG,CAAC,SAAS,CAAC;QACrD,MAAM2I,MAAM,GAAG/K,GAAG,CAACgL,SAAS,CAAC,IAAI,CAACxH,aAAa,CAAC;QAChD,MAAMyH,cAAc,GAAGjL,GAAG,CAACkL,sBAAsB,CAAC,IAAI,CAAC1H,aAAa,CAAC;QACrE,MAAMR,MAAM,GAAGhD,GAAG,CAACgL,SAAS,CAAC,IAAI,CAACxH,aAAa,CAAC,CAAC2H,gBAAgB,CAAC,IAAI,CAAC3H,aAAa,CAAC;QACrF,MAAM4H,eAAe,GAAGC,UAAU,CAACrI,MAAM,CAACsI,gBAAgB,CAAC,wBAAwB,CAAC,CAAC,GAAGD,UAAU,CAACrI,MAAM,CAACsI,gBAAgB,CAAC,2BAA2B,CAAC,CAAC;QACxJ,MAAMC,4BAA4B,GAAIR,MAAM,CAACS,WAAW,GAAGP,cAAc,CAACQ,GAAG,GAAGR,cAAc,CAACS,MAAM,IAAI,IAAI,CAACzI,gBAAgB,CAACM,eAAe,IAAI,CAAC,CAAE;QACrJ,MAAMoI,4BAA4B,GAAIV,cAAc,CAACQ,GAAG,GAAGjJ,aAAa,CAACE,mCAAoC;QAC7G;QACA,MAAMkJ,WAAW,GAAG,IAAI,CAACpI,aAAa,CAACqI,WAAW;QAClD,MAAMC,cAAc,GAAG,IAAI,CAACC,sBAAsB,CAAC,IAAI,CAAC/G,mBAAmB,CAAC;QAC5E,MAAMgH,kBAAkB,GAAGC,IAAI,CAACC,GAAG,CAACJ,cAAc,EAAEG,IAAI,CAACE,KAAK,CAACP,WAAW,CAAC,CAAC,CAACQ,QAAQ,CAAC,CAAC,GAAG,IAAI;QAC9F,IAAI,CAACxH,uBAAuB,CAACmE,KAAK,CAACsD,KAAK,GAAGL,kBAAkB;QAC7D;QACA,IAAI,CAAC5E,UAAU,CAACkF,cAAc,CAAC,CAAC,CAACvD,KAAK,CAAC2C,MAAM,GAAG,EAAE;QAClD,IAAI,CAACtE,UAAU,CAAC6C,MAAM,CAAC,CAAC;QACxB,IAAIsC,UAAU,GAAG,IAAI,CAACnF,UAAU,CAACoF,aAAa;QAC9C,IAAI,IAAI,CAACrJ,WAAW,IAAI,IAAI,CAACyD,uBAAuB,KAAKC,SAAS,EAAE;UAChE,IAAI,CAACD,uBAAuB,GAAG,IAAI,CAAC8D,uBAAuB,CAAC,CAAC;QACjE;QACA,MAAMC,oBAAoB,GAAG,IAAI,CAACxH,WAAW,GAAG,IAAI,CAACyD,uBAAuB,GAAG,CAAC;QAChF,MAAM6F,yBAAyB,GAAGF,UAAU,GAAGnB,eAAe,GAAGT,oBAAoB;QACrF,MAAM+B,sBAAsB,GAAKT,IAAI,CAACU,KAAK,CAAC,CAACpB,4BAA4B,GAAGH,eAAe,GAAGT,oBAAoB,IAAI,IAAI,CAACjG,SAAS,CAAC,CAAC,CAAG;QACzI,MAAMkI,sBAAsB,GAAKX,IAAI,CAACU,KAAK,CAAC,CAAChB,4BAA4B,GAAGP,eAAe,GAAGT,oBAAoB,IAAI,IAAI,CAACjG,SAAS,CAAC,CAAC,CAAG;QACzI;QACA;QACA;QACA;QACA,IAAI8F,iBAAiB,EAAE;UACnB;UACA;UACA,IAAKS,cAAc,CAACQ,GAAG,GAAGR,cAAc,CAACS,MAAM,GAAKX,MAAM,CAACS,WAAW,GAAG,EAAG,IACrEP,cAAc,CAACQ,GAAG,GAAGjJ,aAAa,CAACE,mCAAmC,IACpEgK,sBAAsB,GAAG,CAAC,IAAME,sBAAsB,GAAG,CAAG,EAAE;YACnE;YACA,OAAO,KAAK;UAChB;UACA;UACA;UACA,IAAIF,sBAAsB,GAAGlK,aAAa,CAACG,+BAA+B,IACnEiK,sBAAsB,GAAGF,sBAAsB,IAC/C,IAAI,CAAC7J,OAAO,CAAC8D,MAAM,GAAG+F,sBAAsB,EAAE;YACjD,IAAI,CAACzH,iBAAiB,GAAG,CAAC,CAAC;YAC3B,IAAI,CAACsE,2BAA2B,CAAClH,MAAM,CAAC,CAAC;YACzC,IAAI,CAACwC,oBAAoB,CAACxC,MAAM,CAAC,CAAC;YAClC,IAAI,CAACuC,uBAAuB,CAACgD,WAAW,CAAC,IAAI,CAAC/C,oBAAoB,CAAC;YACnE,IAAI,CAACD,uBAAuB,CAACgD,WAAW,CAAC,IAAI,CAAC2B,2BAA2B,CAAC;YAC1E,IAAI,CAAC1E,oBAAoB,CAAC1C,SAAS,CAACE,MAAM,CAAC,YAAY,CAAC;YACxD,IAAI,CAACwC,oBAAoB,CAAC1C,SAAS,CAACC,GAAG,CAAC,eAAe,CAAC;UAC5D,CAAC,MACI;YACD,IAAI,CAAC6C,iBAAiB,GAAG,CAAC,CAAC;YAC3B,IAAI,CAACsE,2BAA2B,CAAClH,MAAM,CAAC,CAAC;YACzC,IAAI,CAACwC,oBAAoB,CAACxC,MAAM,CAAC,CAAC;YAClC,IAAI,CAACuC,uBAAuB,CAACgD,WAAW,CAAC,IAAI,CAAC2B,2BAA2B,CAAC;YAC1E,IAAI,CAAC3E,uBAAuB,CAACgD,WAAW,CAAC,IAAI,CAAC/C,oBAAoB,CAAC;YACnE,IAAI,CAACA,oBAAoB,CAAC1C,SAAS,CAACE,MAAM,CAAC,eAAe,CAAC;YAC3D,IAAI,CAACwC,oBAAoB,CAAC1C,SAAS,CAACC,GAAG,CAAC,YAAY,CAAC;UACzD;UACA;UACA,OAAO,IAAI;QACf;QACA;QACA,IAAK6I,cAAc,CAACQ,GAAG,GAAGR,cAAc,CAACS,MAAM,GAAKX,MAAM,CAACS,WAAW,GAAG,EAAG,IACrEP,cAAc,CAACQ,GAAG,GAAGjJ,aAAa,CAACE,mCAAmC,IACrE,IAAI,CAACuC,iBAAiB,KAAK,CAAC,CAAC,8BAA8ByH,sBAAsB,GAAG,CAAE,IACtF,IAAI,CAACzH,iBAAiB,KAAK,CAAC,CAAC,8BAA8B2H,sBAAsB,GAAG,CAAE,EAAE;UAC5F;UACA,IAAI,CAAC5G,kBAAkB,CAAC,IAAI,CAAC;UAC7B,OAAO,KAAK;QAChB;QACA;QACA;QACA,IAAI,IAAI,CAACf,iBAAiB,KAAK,CAAC,CAAC,4BAA4B;UACzD,IAAI,IAAI,CAAC3B,UAAU,IAAIoJ,sBAAsB,GAAGE,sBAAsB,GAAG,CAAC,EAAE;YACxE;YACA;YACA,IAAI,CAAC5G,kBAAkB,CAAC,IAAI,CAAC;YAC7B,OAAO,KAAK;UAChB;UACA;UACA,IAAIyG,yBAAyB,GAAGlB,4BAA4B,EAAE;YAC1DgB,UAAU,GAAIG,sBAAsB,GAAG,IAAI,CAAChI,SAAS,CAAC,CAAE;UAC5D;QACJ,CAAC,MACI;UACD,IAAI+H,yBAAyB,GAAGd,4BAA4B,EAAE;YAC1DY,UAAU,GAAIK,sBAAsB,GAAG,IAAI,CAAClI,SAAS,CAAC,CAAE;UAC5D;QACJ;QACA;QACA,IAAI,CAAC0C,UAAU,CAAC6C,MAAM,CAACsC,UAAU,CAAC;QAClC,IAAI,CAACnF,UAAU,CAACyF,QAAQ,CAAC,CAAC;QAC1B;QACA,IAAI,IAAI,CAACzF,UAAU,CAACT,MAAM,GAAG,CAAC,EAAE;UAC5B,IAAI,CAACS,UAAU,CAAC0F,QAAQ,CAAC,CAAC,IAAI,CAAChK,QAAQ,IAAI,CAAC,CAAC,CAAC;UAC9C,IAAI,CAACsE,UAAU,CAAC2F,MAAM,CAAC,IAAI,CAAC3F,UAAU,CAAC4F,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QAC9D;QACA,IAAI,IAAI,CAAC7J,WAAW,EAAE;UAClB;UACA,IAAI,CAACiE,UAAU,CAACkF,cAAc,CAAC,CAAC,CAACvD,KAAK,CAAC2C,MAAM,GAAIa,UAAU,GAAGnB,eAAe,GAAI,IAAI;UACrF,IAAI,CAACxG,uBAAuB,CAACmE,KAAK,CAAC2C,MAAM,GAAG,EAAE;QAClD,CAAC,MACI;UACD,IAAI,CAAC9G,uBAAuB,CAACmE,KAAK,CAAC2C,MAAM,GAAIa,UAAU,GAAGnB,eAAe,GAAI,IAAI;QACrF;QACA,IAAI,CAACP,YAAY,CAAC,IAAI,CAAC/H,QAAQ,CAAC;QAChC,IAAI,CAAC8B,uBAAuB,CAACmE,KAAK,CAACsD,KAAK,GAAGL,kBAAkB;QAC7D;QACA,IAAI,CAACzC,2BAA2B,CAAC1F,YAAY,CAAC,UAAU,EAAE,GAAG,CAAC;QAC9D,IAAI,CAACL,aAAa,CAACrB,SAAS,CAACC,GAAG,CAAC,iBAAiB,CAAC;QACnD,IAAI,CAACwC,uBAAuB,CAACzC,SAAS,CAACC,GAAG,CAAC,iBAAiB,CAAC;QAC7D,OAAO,IAAI;MACf,CAAC,MACI;QACD,OAAO,KAAK;MAChB;IACJ;IACA2J,sBAAsBA,CAAC5K,SAAS,EAAE;MAC9B,IAAI8L,YAAY,GAAG,CAAC;MACpB,IAAI9L,SAAS,EAAE;QACX,IAAI+L,OAAO,GAAG,CAAC;QACf,IAAIC,aAAa,GAAG,CAAC;QACrB,IAAI,CAACtK,OAAO,CAACiE,OAAO,CAAC,CAACC,MAAM,EAAEjF,KAAK,KAAK;UACpC,MAAMsL,YAAY,GAAG,CAAC,CAACrG,MAAM,CAACrF,MAAM,GAAGqF,MAAM,CAACrF,MAAM,CAACiF,MAAM,GAAG,CAAC;UAC/D,MAAM0G,oBAAoB,GAAG,CAAC,CAACtG,MAAM,CAACpF,cAAc,GAAGoF,MAAM,CAACpF,cAAc,CAACgF,MAAM,GAAG,CAAC;UACvF,MAAM2G,GAAG,GAAGvG,MAAM,CAACvF,IAAI,CAACmF,MAAM,GAAGyG,YAAY,GAAGC,oBAAoB;UACpE,IAAIC,GAAG,GAAGH,aAAa,EAAE;YACrBD,OAAO,GAAGpL,KAAK;YACfqL,aAAa,GAAGG,GAAG;UACvB;QACJ,CAAC,CAAC;QACFnM,SAAS,CAACc,WAAW,GAAG,IAAI,CAACY,OAAO,CAACqK,OAAO,CAAC,CAAC1L,IAAI,IAAI,CAAC,CAAC,IAAI,CAACqB,OAAO,CAACqK,OAAO,CAAC,CAACvL,cAAc,GAAI,IAAI,CAACkB,OAAO,CAACqK,OAAO,CAAC,CAACvL,cAAc,GAAG,GAAG,GAAI,EAAE,CAAC;QACjJsL,YAAY,GAAGjN,GAAG,CAACuN,aAAa,CAACpM,SAAS,CAAC;MAC/C;MACA,OAAO8L,YAAY;IACvB;IACApD,gBAAgBA,CAAC2D,MAAM,EAAE;MACrB;MACA,IAAI,IAAI,CAACpG,UAAU,EAAE;QACjB;MACJ;MACA;MACA,IAAI,CAACmC,2BAA2B,GAAGvJ,GAAG,CAACyB,MAAM,CAAC+L,MAAM,EAAE1M,CAAC,CAAC,qCAAqC,CAAC,CAAC;MAC/F,IAAI,CAAC2M,YAAY,GAAG,IAAIzM,kBAAkB,CAAC,CAAC;MAC5C,IAAI,CAACoG,UAAU,GAAG,IAAI,CAACpD,SAAS,CAAC,IAAI1D,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAACiJ,2BAA2B,EAAE,IAAI,EAAE,CAAC,IAAI,CAACkE,YAAY,CAAC,EAAE;QACtHC,UAAU,EAAE,KAAK;QACjBC,kBAAkB,EAAE,CAAC,CAAC;QACtBC,eAAe,EAAE,KAAK;QACtBC,YAAY,EAAE,KAAK;QACnBC,qBAAqB,EAAE;UACnBC,YAAY,EAAElM,OAAO,IAAI;YACrB,IAAImM,KAAK,GAAGnM,OAAO,CAACL,IAAI;YACxB,IAAIK,OAAO,CAACH,MAAM,EAAE;cAChBsM,KAAK,IAAI,KAAKnM,OAAO,CAACH,MAAM,EAAE;YAClC;YACA,IAAIG,OAAO,CAACF,cAAc,EAAE;cACxBqM,KAAK,IAAI,KAAKnM,OAAO,CAACF,cAAc,EAAE;YAC1C;YACA,IAAIE,OAAO,CAACoF,WAAW,EAAE;cACrB+G,KAAK,IAAI,KAAKnM,OAAO,CAACoF,WAAW,EAAE;YACvC;YACA,OAAO+G,KAAK;UAChB,CAAC;UACDC,kBAAkB,EAAEA,CAAA,KAAMpN,QAAQ,CAAC;YAAEqN,GAAG,EAAE,WAAW;YAAEC,OAAO,EAAE,CAAC,6CAA6C;UAAE,CAAC,EAAE,YAAY,CAAC;UAChIC,OAAO,EAAEA,CAAA,KAAMxN,WAAW,GAAG,EAAE,GAAG,QAAQ;UAC1CyN,aAAa,EAAEA,CAAA,KAAM;QACzB;MACJ,CAAC,CAAC,CAAC;MACH,IAAI,IAAI,CAACpL,gBAAgB,CAACW,SAAS,EAAE;QACjC,IAAI,CAACwD,UAAU,CAACxD,SAAS,GAAG,IAAI,CAACX,gBAAgB,CAACW,SAAS;MAC/D;MACA;MACA,MAAM0K,SAAS,GAAG,IAAI,CAACtK,SAAS,CAAC,IAAI/D,UAAU,CAAC,IAAI,CAACsJ,2BAA2B,EAAE,SAAS,CAAC,CAAC;MAC7F,MAAMgF,uBAAuB,GAAG9N,KAAK,CAAC+N,KAAK,CAACF,SAAS,CAACjI,KAAK,EAAEvF,CAAC,IAAIA,CAAC,CAAC2N,MAAM,CAAC,MAAM,IAAI,CAACrH,UAAU,CAACT,MAAM,GAAG,CAAC,CAAC,CACvG+H,GAAG,CAACnJ,CAAC,IAAI,IAAIrF,qBAAqB,CAACqF,CAAC,CAAC,CAAC,CAAC;MAC5C,IAAI,CAACvB,SAAS,CAACvD,KAAK,CAAC+N,KAAK,CAACD,uBAAuB,EAAEzN,CAAC,IAAIA,CAAC,CAAC2N,MAAM,CAAClJ,CAAC,IAAIA,CAAC,CAACgB,OAAO,KAAK,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAACoI,OAAO,EAAE,IAAI,CAAC,CAAC;MACjI,IAAI,CAAC3K,SAAS,CAACvD,KAAK,CAAC+N,KAAK,CAACD,uBAAuB,EAAEzN,CAAC,IAAIA,CAAC,CAAC2N,MAAM,CAAClJ,CAAC,IAAIA,CAAC,CAACgB,OAAO,KAAK,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAACoI,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;MACjI,IAAI,CAAC3K,SAAS,CAACvD,KAAK,CAAC+N,KAAK,CAACD,uBAAuB,EAAEzN,CAAC,IAAIA,CAAC,CAAC2N,MAAM,CAAClJ,CAAC,IAAIA,CAAC,CAACgB,OAAO,KAAK,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,IAAI,CAACqI,QAAQ,EAAE,IAAI,CAAC,CAAC;MACnI,IAAI,CAAC5K,SAAS,CAACvD,KAAK,CAAC+N,KAAK,CAACD,uBAAuB,EAAEzN,CAAC,IAAIA,CAAC,CAAC2N,MAAM,CAAClJ,CAAC,IAAIA,CAAC,CAACgB,OAAO,KAAK,EAAE,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAACsI,SAAS,EAAE,IAAI,CAAC,CAAC;MACtI,IAAI,CAAC7K,SAAS,CAACvD,KAAK,CAAC+N,KAAK,CAACD,uBAAuB,EAAEzN,CAAC,IAAIA,CAAC,CAAC2N,MAAM,CAAClJ,CAAC,IAAIA,CAAC,CAACgB,OAAO,KAAK,EAAE,CAAC,uBAAuB,CAAC,CAAC,CAAC,IAAI,CAACuI,WAAW,EAAE,IAAI,CAAC,CAAC;MAC1I,IAAI,CAAC9K,SAAS,CAACvD,KAAK,CAAC+N,KAAK,CAACD,uBAAuB,EAAEzN,CAAC,IAAIA,CAAC,CAAC2N,MAAM,CAAClJ,CAAC,IAAIA,CAAC,CAACgB,OAAO,KAAK,EAAE,CAAC,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAACwI,UAAU,EAAE,IAAI,CAAC,CAAC;MACxI,IAAI,CAAC/K,SAAS,CAACvD,KAAK,CAAC+N,KAAK,CAACD,uBAAuB,EAAEzN,CAAC,IAAIA,CAAC,CAAC2N,MAAM,CAAClJ,CAAC,IAAIA,CAAC,CAACgB,OAAO,KAAK,EAAE,CAAC,oBAAoB,CAAC,CAAC,CAAC,IAAI,CAACyI,QAAQ,EAAE,IAAI,CAAC,CAAC;MACpI,IAAI,CAAChL,SAAS,CAACvD,KAAK,CAAC+N,KAAK,CAACD,uBAAuB,EAAEzN,CAAC,IAAIA,CAAC,CAAC2N,MAAM,CAAClJ,CAAC,IAAIA,CAAC,CAACgB,OAAO,KAAK,EAAE,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC0I,MAAM,EAAE,IAAI,CAAC,CAAC;MAChI,IAAI,CAACjL,SAAS,CAACvD,KAAK,CAAC+N,KAAK,CAACD,uBAAuB,EAAEzN,CAAC,IAAIA,CAAC,CAAC2N,MAAM,CAAClJ,CAAC,IAAIA,CAAC,CAACgB,OAAO,KAAK,EAAE,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC2I,KAAK,EAAE,IAAI,CAAC,CAAC;MAC9H,IAAI,CAAClL,SAAS,CAACvD,KAAK,CAAC+N,KAAK,CAACD,uBAAuB,EAAEzN,CAAC,IAAIA,CAAC,CAAC2N,MAAM,CAAClJ,CAAC,IAAKA,CAAC,CAACgB,OAAO,IAAI,EAAE,CAAC,wBAAwBhB,CAAC,CAACgB,OAAO,IAAI,EAAE,CAAC,sBAAwBhB,CAAC,CAACgB,OAAO,IAAI,EAAE,CAAC,2BAA2BhB,CAAC,CAACgB,OAAO,IAAI,GAAG,CAAC,0BAA2B,CAAC,CAAC,CAAC,IAAI,CAAC4I,WAAW,EAAE,IAAI,CAAC,CAAC;MAC1Q;MACA,IAAI,CAACnL,SAAS,CAAChE,GAAG,CAACoF,qBAAqB,CAAC,IAAI,CAACgC,UAAU,CAACkF,cAAc,CAAC,CAAC,EAAEtM,GAAG,CAACqF,SAAS,CAAC+J,UAAU,EAAE7J,CAAC,IAAI,IAAI,CAAC8J,WAAW,CAAC9J,CAAC,CAAC,CAAC,CAAC;MAC/H,IAAI,CAACvB,SAAS,CAAC,IAAI,CAACoD,UAAU,CAACkI,WAAW,CAAC/J,CAAC,IAAI,OAAOA,CAAC,CAACzD,KAAK,KAAK,WAAW,IAAI,IAAI,CAACsF,UAAU,CAAC0F,QAAQ,CAAC,CAACvH,CAAC,CAACzD,KAAK,CAAC,CAAC,CAAC,CAAC;MACvH,IAAI,CAACkC,SAAS,CAAC,IAAI,CAACoD,UAAU,CAACmI,gBAAgB,CAAChK,CAAC,IAAI,IAAI,CAACiK,WAAW,CAACjK,CAAC,CAAC,CAAC,CAAC;MAC1E,IAAI,CAACvB,SAAS,CAAChE,GAAG,CAACoF,qBAAqB,CAAC,IAAI,CAACR,uBAAuB,EAAE5E,GAAG,CAACqF,SAAS,CAACoK,SAAS,EAAElK,CAAC,IAAI;QACjG,IAAI,CAAC,IAAI,CAACjC,UAAU,IAAItD,GAAG,CAAC0P,UAAU,CAACnK,CAAC,CAACoK,aAAa,EAAE,IAAI,CAAC/K,uBAAuB,CAAC,EAAE;UACnF;QACJ;QACA,IAAI,CAACgL,UAAU,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC;MACH,IAAI,CAACxI,UAAU,CAACkF,cAAc,CAAC,CAAC,CAACzI,YAAY,CAAC,YAAY,EAAE,IAAI,CAACZ,gBAAgB,CAACW,SAAS,IAAI,EAAE,CAAC;MAClG,IAAI,CAACwD,UAAU,CAACkF,cAAc,CAAC,CAAC,CAACzI,YAAY,CAAC,eAAe,EAAE,MAAM,CAAC;MACtE,IAAI,CAACsF,SAAS,CAAC,CAAC;IACpB;IACA;IACA;IACA;IACAkG,WAAWA,CAAC9J,CAAC,EAAE;MACX,IAAI,CAAC,IAAI,CAAC6B,UAAU,CAACT,MAAM,EAAE;QACzB;MACJ;MACA3G,GAAG,CAACwF,WAAW,CAACC,IAAI,CAACF,CAAC,CAAC;MACvB,MAAMI,MAAM,GAAGJ,CAAC,CAACI,MAAM;MACvB,IAAI,CAACA,MAAM,EAAE;QACT;MACJ;MACA;MACA,IAAIA,MAAM,CAACxD,SAAS,CAAC0N,QAAQ,CAAC,QAAQ,CAAC,EAAE;QACrC;MACJ;MACA,MAAMC,cAAc,GAAGnK,MAAM,CAACoK,OAAO,CAAC,kBAAkB,CAAC;MACzD,IAAI,CAACD,cAAc,EAAE;QACjB;MACJ;MACA,MAAMhO,KAAK,GAAGkO,MAAM,CAACF,cAAc,CAACG,YAAY,CAAC,YAAY,CAAC,CAAC;MAC/D,MAAMrG,QAAQ,GAAGkG,cAAc,CAAC3N,SAAS,CAAC0N,QAAQ,CAAC,iBAAiB,CAAC;MACrE;MACA,IAAI/N,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAG,IAAI,CAACe,OAAO,CAAC8D,MAAM,IAAI,CAACiD,QAAQ,EAAE;QACxD,IAAI,CAAC9G,QAAQ,GAAGhB,KAAK;QACrB,IAAI,CAACoF,MAAM,CAAC,IAAI,CAACpE,QAAQ,CAAC;QAC1B,IAAI,CAACsE,UAAU,CAAC0F,QAAQ,CAAC,CAAC,IAAI,CAAChK,QAAQ,CAAC,CAAC;QACzC,IAAI,CAACsE,UAAU,CAAC2F,MAAM,CAAC,IAAI,CAAC3F,UAAU,CAAC4F,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrD;QACA,IAAI,IAAI,CAAClK,QAAQ,KAAK,IAAI,CAACI,iBAAiB,EAAE;UAC1C;UACA,IAAI,CAACA,iBAAiB,GAAG,IAAI,CAACJ,QAAQ;UACtC,IAAI,CAACiB,YAAY,CAAC8B,IAAI,CAAC;YACnB/D,KAAK,EAAE,IAAI,CAAC0B,aAAa,CAACoC,aAAa;YACvC9C,QAAQ,EAAE,IAAI,CAACD,OAAO,CAAC,IAAI,CAACC,QAAQ,CAAC,CAACtB;UAC1C,CAAC,CAAC;UACF,IAAI,CAAC,CAAC,IAAI,CAACqB,OAAO,CAAC,IAAI,CAACC,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,CAACD,OAAO,CAAC,IAAI,CAACC,QAAQ,CAAC,CAACtB,IAAI,EAAE;YACrE,IAAI,CAAC6C,QAAQ,CAAC,IAAI,CAACxB,OAAO,CAAC,IAAI,CAACC,QAAQ,CAAC,CAACtB,IAAI,CAAC;UACnD;QACJ;QACA,IAAI,CAACwE,kBAAkB,CAAC,IAAI,CAAC;MACjC;IACJ;IACA;IACA4J,UAAUA,CAAA,EAAG;MACT,IAAI,IAAI,CAACvM,OAAO,EAAE;QACd;MACJ;MACA,IAAI,IAAI,CAACP,QAAQ,KAAK,IAAI,CAACI,iBAAiB,EAAE;QAC1C;QACA,IAAI,CAACgE,MAAM,CAAC,IAAI,CAAChE,iBAAiB,CAAC;MACvC;MACA,IAAI,CAAC8C,kBAAkB,CAAC,KAAK,CAAC;IAClC;IACAkK,yBAAyBA,CAAC1O,IAAI,EAAE2O,aAAa,EAAE;MAC3C,MAAMC,qBAAqB,GAAIvO,OAAO,IAAK;QACvC,KAAK,IAAIwO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGxO,OAAO,CAACyO,UAAU,CAAC3J,MAAM,EAAE0J,CAAC,EAAE,EAAE;UAChD,MAAME,KAAK,GAAG1O,OAAO,CAACyO,UAAU,CAACE,IAAI,CAACH,CAAC,CAAC;UACxC,MAAMI,OAAO,GAAGF,KAAK,CAACE,OAAO,IAAIF,KAAK,CAACE,OAAO,CAACC,WAAW,CAAC,CAAC;UAC5D,IAAID,OAAO,KAAK,KAAK,EAAE;YACnBF,KAAK,CAAClO,MAAM,CAAC,CAAC;UAClB,CAAC,MACI;YACD+N,qBAAqB,CAACG,KAAK,CAAC;UAChC;QACJ;MACJ,CAAC;MACD,MAAMI,QAAQ,GAAGxQ,cAAc,CAAC;QAAE2F,KAAK,EAAEtE,IAAI;QAAEoP,iBAAiB,EAAE;MAAK,CAAC,EAAE;QAAET;MAAc,CAAC,CAAC;MAC5FQ,QAAQ,CAAC9O,OAAO,CAACM,SAAS,CAACC,GAAG,CAAC,iCAAiC,CAAC;MACjEgO,qBAAqB,CAACO,QAAQ,CAAC9O,OAAO,CAAC;MACvC,OAAO8O,QAAQ,CAAC9O,OAAO;IAC3B;IACA;IACA2N,WAAWA,CAACjK,CAAC,EAAE;MACX;MACA,IAAI,CAAC,IAAI,CAACjC,UAAU,IAAI,CAAC,IAAI,CAACH,WAAW,EAAE;QACvC;MACJ;MACA,IAAI,CAAC0H,YAAY,CAACtF,CAAC,CAACsL,OAAO,CAAC,CAAC,CAAC,CAAC;IACnC;IACAhG,YAAYA,CAACjF,aAAa,EAAE;MACxB,IAAI,CAACf,oBAAoB,CAAC3C,SAAS,GAAG,EAAE;MACxC,MAAM6E,MAAM,GAAG,IAAI,CAAClE,OAAO,CAAC+C,aAAa,CAAC;MAC1C,MAAMqB,WAAW,GAAGF,MAAM,EAAEE,WAAW,IAAI,EAAE;MAC7C,MAAM6J,qBAAqB,GAAG/J,MAAM,EAAE+J,qBAAqB,IAAI,KAAK;MACpE,IAAI7J,WAAW,EAAE;QACb,IAAI6J,qBAAqB,EAAE;UACvB,MAAMX,aAAa,GAAGpJ,MAAM,CAACgK,gCAAgC;UAC7D,IAAI,CAAClM,oBAAoB,CAAC+C,WAAW,CAAC,IAAI,CAACsI,yBAAyB,CAACjJ,WAAW,EAAEkJ,aAAa,CAAC,CAAC;QACrG,CAAC,MACI;UACD,IAAI,CAACtL,oBAAoB,CAAC3C,SAAS,GAAG+E,WAAW;QACrD;QACA,IAAI,CAACpC,oBAAoB,CAACkE,KAAK,CAACiI,OAAO,GAAG,OAAO;MACrD,CAAC,MACI;QACD,IAAI,CAACnM,oBAAoB,CAACkE,KAAK,CAACiI,OAAO,GAAG,MAAM;MACpD;MACA;MACA,IAAI,CAAC5N,WAAW,GAAG,IAAI;MACvB,IAAI,CAACL,mBAAmB,CAACkH,MAAM,CAAC,CAAC;MACjC,IAAI,CAAC7G,WAAW,GAAG,KAAK;IAC5B;IACA;IACA;IACAwL,QAAQA,CAACrJ,CAAC,EAAE;MACRvF,GAAG,CAACwF,WAAW,CAACC,IAAI,CAACF,CAAC,CAAC;MACvB;MACA,IAAI,CAAC2B,MAAM,CAAC,IAAI,CAAChE,iBAAiB,CAAC;MACnC,IAAI,CAAC8C,kBAAkB,CAAC,IAAI,CAAC;IACjC;IACA;IACA2I,OAAOA,CAACpJ,CAAC,EAAE;MACPvF,GAAG,CAACwF,WAAW,CAACC,IAAI,CAACF,CAAC,CAAC;MACvB;MACA,IAAI,IAAI,CAACzC,QAAQ,KAAK,IAAI,CAACI,iBAAiB,EAAE;QAC1C,IAAI,CAACA,iBAAiB,GAAG,IAAI,CAACJ,QAAQ;QACtC,IAAI,CAACiB,YAAY,CAAC8B,IAAI,CAAC;UACnB/D,KAAK,EAAE,IAAI,CAAC0B,aAAa,CAACoC,aAAa;UACvC9C,QAAQ,EAAE,IAAI,CAACD,OAAO,CAAC,IAAI,CAACC,QAAQ,CAAC,CAACtB;QAC1C,CAAC,CAAC;QACF,IAAI,CAAC,CAAC,IAAI,CAACqB,OAAO,CAAC,IAAI,CAACC,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,CAACD,OAAO,CAAC,IAAI,CAACC,QAAQ,CAAC,CAACtB,IAAI,EAAE;UACrE,IAAI,CAAC6C,QAAQ,CAAC,IAAI,CAACxB,OAAO,CAAC,IAAI,CAACC,QAAQ,CAAC,CAACtB,IAAI,CAAC;QACnD;MACJ;MACA,IAAI,CAACwE,kBAAkB,CAAC,IAAI,CAAC;IACjC;IACA;IACA8I,WAAWA,CAACvJ,CAAC,EAAE;MACX,IAAI,IAAI,CAACzC,QAAQ,GAAG,IAAI,CAACD,OAAO,CAAC8D,MAAM,GAAG,CAAC,EAAE;QACzC3G,GAAG,CAACwF,WAAW,CAACC,IAAI,CAACF,CAAC,EAAE,IAAI,CAAC;QAC7B;QACA,MAAM0L,kBAAkB,GAAG,IAAI,CAACpO,OAAO,CAAC,IAAI,CAACC,QAAQ,GAAG,CAAC,CAAC,CAACd,UAAU;QACrE,IAAIiP,kBAAkB,IAAI,IAAI,CAACpO,OAAO,CAAC8D,MAAM,GAAG,IAAI,CAAC7D,QAAQ,GAAG,CAAC,EAAE;UAC/D,IAAI,CAACA,QAAQ,IAAI,CAAC;QACtB,CAAC,MACI,IAAImO,kBAAkB,EAAE;UACzB;QACJ,CAAC,MACI;UACD,IAAI,CAACnO,QAAQ,EAAE;QACnB;QACA;QACA,IAAI,CAACoE,MAAM,CAAC,IAAI,CAACpE,QAAQ,CAAC;QAC1B,IAAI,CAACsE,UAAU,CAAC0F,QAAQ,CAAC,CAAC,IAAI,CAAChK,QAAQ,CAAC,CAAC;QACzC,IAAI,CAACsE,UAAU,CAAC2F,MAAM,CAAC,IAAI,CAAC3F,UAAU,CAAC4F,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzD;IACJ;IACA6B,SAASA,CAACtJ,CAAC,EAAE;MACT,IAAI,IAAI,CAACzC,QAAQ,GAAG,CAAC,EAAE;QACnB9C,GAAG,CAACwF,WAAW,CAACC,IAAI,CAACF,CAAC,EAAE,IAAI,CAAC;QAC7B;QACA,MAAM2L,sBAAsB,GAAG,IAAI,CAACrO,OAAO,CAAC,IAAI,CAACC,QAAQ,GAAG,CAAC,CAAC,CAACd,UAAU;QACzE,IAAIkP,sBAAsB,IAAI,IAAI,CAACpO,QAAQ,GAAG,CAAC,EAAE;UAC7C,IAAI,CAACA,QAAQ,IAAI,CAAC;QACtB,CAAC,MACI;UACD,IAAI,CAACA,QAAQ,EAAE;QACnB;QACA;QACA,IAAI,CAACoE,MAAM,CAAC,IAAI,CAACpE,QAAQ,CAAC;QAC1B,IAAI,CAACsE,UAAU,CAAC0F,QAAQ,CAAC,CAAC,IAAI,CAAChK,QAAQ,CAAC,CAAC;QACzC,IAAI,CAACsE,UAAU,CAAC2F,MAAM,CAAC,IAAI,CAAC3F,UAAU,CAAC4F,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzD;IACJ;IACAgC,QAAQA,CAACzJ,CAAC,EAAE;MACRvF,GAAG,CAACwF,WAAW,CAACC,IAAI,CAACF,CAAC,CAAC;MACvB,IAAI,CAAC6B,UAAU,CAAC+J,iBAAiB,CAAC,CAAC;MACnC;MACAC,UAAU,CAAC,MAAM;QACb,IAAI,CAACtO,QAAQ,GAAG,IAAI,CAACsE,UAAU,CAAC4F,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7C;QACA,IAAI,IAAI,CAACnK,OAAO,CAAC,IAAI,CAACC,QAAQ,CAAC,CAACd,UAAU,IAAI,IAAI,CAACc,QAAQ,GAAG,IAAI,CAACD,OAAO,CAAC8D,MAAM,GAAG,CAAC,EAAE;UACnF,IAAI,CAAC7D,QAAQ,EAAE;UACf,IAAI,CAACsE,UAAU,CAAC0F,QAAQ,CAAC,CAAC,IAAI,CAAChK,QAAQ,CAAC,CAAC;QAC7C;QACA,IAAI,CAACsE,UAAU,CAAC2F,MAAM,CAAC,IAAI,CAACjK,QAAQ,CAAC;QACrC,IAAI,CAACoE,MAAM,CAAC,IAAI,CAACpE,QAAQ,CAAC;MAC9B,CAAC,EAAE,CAAC,CAAC;IACT;IACAiM,UAAUA,CAACxJ,CAAC,EAAE;MACVvF,GAAG,CAACwF,WAAW,CAACC,IAAI,CAACF,CAAC,CAAC;MACvB,IAAI,CAAC6B,UAAU,CAACiK,aAAa,CAAC,CAAC;MAC/B;MACAD,UAAU,CAAC,MAAM;QACb,IAAI,CAACtO,QAAQ,GAAG,IAAI,CAACsE,UAAU,CAAC4F,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7C;QACA,IAAI,IAAI,CAACnK,OAAO,CAAC,IAAI,CAACC,QAAQ,CAAC,CAACd,UAAU,IAAI,IAAI,CAACc,QAAQ,GAAG,CAAC,EAAE;UAC7D,IAAI,CAACA,QAAQ,EAAE;UACf,IAAI,CAACsE,UAAU,CAAC0F,QAAQ,CAAC,CAAC,IAAI,CAAChK,QAAQ,CAAC,CAAC;QAC7C;QACA,IAAI,CAACsE,UAAU,CAAC2F,MAAM,CAAC,IAAI,CAACjK,QAAQ,CAAC;QACrC,IAAI,CAACoE,MAAM,CAAC,IAAI,CAACpE,QAAQ,CAAC;MAC9B,CAAC,EAAE,CAAC,CAAC;IACT;IACAmM,MAAMA,CAAC1J,CAAC,EAAE;MACNvF,GAAG,CAACwF,WAAW,CAACC,IAAI,CAACF,CAAC,CAAC;MACvB,IAAI,IAAI,CAAC1C,OAAO,CAAC8D,MAAM,GAAG,CAAC,EAAE;QACzB;MACJ;MACA,IAAI,CAAC7D,QAAQ,GAAG,CAAC;MACjB,IAAI,IAAI,CAACD,OAAO,CAAC,IAAI,CAACC,QAAQ,CAAC,CAACd,UAAU,IAAI,IAAI,CAACc,QAAQ,GAAG,CAAC,EAAE;QAC7D,IAAI,CAACA,QAAQ,EAAE;MACnB;MACA,IAAI,CAACsE,UAAU,CAAC0F,QAAQ,CAAC,CAAC,IAAI,CAAChK,QAAQ,CAAC,CAAC;MACzC,IAAI,CAACsE,UAAU,CAAC2F,MAAM,CAAC,IAAI,CAACjK,QAAQ,CAAC;MACrC,IAAI,CAACoE,MAAM,CAAC,IAAI,CAACpE,QAAQ,CAAC;IAC9B;IACAoM,KAAKA,CAAC3J,CAAC,EAAE;MACLvF,GAAG,CAACwF,WAAW,CAACC,IAAI,CAACF,CAAC,CAAC;MACvB,IAAI,IAAI,CAAC1C,OAAO,CAAC8D,MAAM,GAAG,CAAC,EAAE;QACzB;MACJ;MACA,IAAI,CAAC7D,QAAQ,GAAG,IAAI,CAACD,OAAO,CAAC8D,MAAM,GAAG,CAAC;MACvC,IAAI,IAAI,CAAC9D,OAAO,CAAC,IAAI,CAACC,QAAQ,CAAC,CAACd,UAAU,IAAI,IAAI,CAACc,QAAQ,GAAG,CAAC,EAAE;QAC7D,IAAI,CAACA,QAAQ,EAAE;MACnB;MACA,IAAI,CAACsE,UAAU,CAAC0F,QAAQ,CAAC,CAAC,IAAI,CAAChK,QAAQ,CAAC,CAAC;MACzC,IAAI,CAACsE,UAAU,CAAC2F,MAAM,CAAC,IAAI,CAACjK,QAAQ,CAAC;MACrC,IAAI,CAACoE,MAAM,CAAC,IAAI,CAACpE,QAAQ,CAAC;IAC9B;IACA;IACAqM,WAAWA,CAAC5J,CAAC,EAAE;MACX,MAAM+L,EAAE,GAAG5Q,YAAY,CAAC0L,QAAQ,CAAC7G,CAAC,CAACgB,OAAO,CAAC;MAC3C,IAAIgL,WAAW,GAAG,CAAC,CAAC;MACpB,KAAK,IAAIlB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACxN,OAAO,CAAC8D,MAAM,GAAG,CAAC,EAAE0J,CAAC,EAAE,EAAE;QAC9CkB,WAAW,GAAG,CAAClB,CAAC,GAAG,IAAI,CAACvN,QAAQ,GAAG,CAAC,IAAI,IAAI,CAACD,OAAO,CAAC8D,MAAM;QAC3D,IAAI,IAAI,CAAC9D,OAAO,CAAC0O,WAAW,CAAC,CAAC/P,IAAI,CAACgQ,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,KAAKH,EAAE,IAAI,CAAC,IAAI,CAACzO,OAAO,CAAC0O,WAAW,CAAC,CAACvP,UAAU,EAAE;UACxG,IAAI,CAACkF,MAAM,CAACqK,WAAW,CAAC;UACxB,IAAI,CAACnK,UAAU,CAAC0F,QAAQ,CAAC,CAACyE,WAAW,CAAC,CAAC;UACvC,IAAI,CAACnK,UAAU,CAAC2F,MAAM,CAAC,IAAI,CAAC3F,UAAU,CAAC4F,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrDhN,GAAG,CAACwF,WAAW,CAACC,IAAI,CAACF,CAAC,CAAC;UACvB;QACJ;MACJ;IACJ;IACAkF,OAAOA,CAAA,EAAG;MACN,IAAI,CAACzE,kBAAkB,CAAC,KAAK,CAAC;MAC9B,KAAK,CAACyE,OAAO,CAAC,CAAC;IACnB;EACJ;EAAC,OA5xBYjI,aAAa;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}