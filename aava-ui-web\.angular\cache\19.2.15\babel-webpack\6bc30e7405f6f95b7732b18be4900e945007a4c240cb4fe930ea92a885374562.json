{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Lazy } from './lazy.js';\n// When comparing large numbers of strings it's better for performance to create an\n// Intl.Collator object and use the function provided by its compare property\n// than it is to use String.prototype.localeCompare()\n// A collator with numeric sorting enabled, and no sensitivity to case, accents or diacritics.\nconst intlFileNameCollatorBaseNumeric = new Lazy(() => {\n  const collator = new Intl.Collator(undefined, {\n    numeric: true,\n    sensitivity: 'base'\n  });\n  return {\n    collator,\n    collatorIsNumeric: collator.resolvedOptions().numeric\n  };\n});\n// A collator with numeric sorting enabled.\nconst intlFileNameCollatorNumeric = new Lazy(() => {\n  const collator = new Intl.Collator(undefined, {\n    numeric: true\n  });\n  return {\n    collator\n  };\n});\n// A collator with numeric sorting enabled, and sensitivity to accents and diacritics but not case.\nconst intlFileNameCollatorNumericCaseInsensitive = new Lazy(() => {\n  const collator = new Intl.Collator(undefined, {\n    numeric: true,\n    sensitivity: 'accent'\n  });\n  return {\n    collator\n  };\n});\n/** Compares filenames without distinguishing the name from the extension. Disambiguates by unicode comparison. */\nexport function compareFileNames(one, other, caseSensitive = false) {\n  const a = one || '';\n  const b = other || '';\n  const result = intlFileNameCollatorBaseNumeric.value.collator.compare(a, b);\n  // Using the numeric option will make compare(`foo1`, `foo01`) === 0. Disambiguate.\n  if (intlFileNameCollatorBaseNumeric.value.collatorIsNumeric && result === 0 && a !== b) {\n    return a < b ? -1 : 1;\n  }\n  return result;\n}\nexport function compareAnything(one, other, lookFor) {\n  const elementAName = one.toLowerCase();\n  const elementBName = other.toLowerCase();\n  // Sort prefix matches over non prefix matches\n  const prefixCompare = compareByPrefix(one, other, lookFor);\n  if (prefixCompare) {\n    return prefixCompare;\n  }\n  // Sort suffix matches over non suffix matches\n  const elementASuffixMatch = elementAName.endsWith(lookFor);\n  const elementBSuffixMatch = elementBName.endsWith(lookFor);\n  if (elementASuffixMatch !== elementBSuffixMatch) {\n    return elementASuffixMatch ? -1 : 1;\n  }\n  // Understand file names\n  const r = compareFileNames(elementAName, elementBName);\n  if (r !== 0) {\n    return r;\n  }\n  // Compare by name\n  return elementAName.localeCompare(elementBName);\n}\nexport function compareByPrefix(one, other, lookFor) {\n  const elementAName = one.toLowerCase();\n  const elementBName = other.toLowerCase();\n  // Sort prefix matches over non prefix matches\n  const elementAPrefixMatch = elementAName.startsWith(lookFor);\n  const elementBPrefixMatch = elementBName.startsWith(lookFor);\n  if (elementAPrefixMatch !== elementBPrefixMatch) {\n    return elementAPrefixMatch ? -1 : 1;\n  }\n  // Same prefix: Sort shorter matches to the top to have those on top that match more precisely\n  else if (elementAPrefixMatch && elementBPrefixMatch) {\n    if (elementAName.length < elementBName.length) {\n      return -1;\n    }\n    if (elementAName.length > elementBName.length) {\n      return 1;\n    }\n  }\n  return 0;\n}", "map": {"version": 3, "names": ["Lazy", "intlFileNameCollatorBaseNumeric", "collator", "Intl", "Collator", "undefined", "numeric", "sensitivity", "collatorIsNumeric", "resolvedOptions", "intlFileNameCollatorNumeric", "intlFileNameCollatorNumericCaseInsensitive", "compareFileNames", "one", "other", "caseSensitive", "a", "b", "result", "value", "compare", "compareAnything", "lookFor", "elementAName", "toLowerCase", "elementBName", "prefixCompare", "compareByPrefix", "elementASuffixMatch", "endsWith", "elementBSuffixMatch", "r", "localeCompare", "elementAPrefixMatch", "startsWith", "elementBPrefixMatch", "length"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/common/comparers.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Lazy } from './lazy.js';\n// When comparing large numbers of strings it's better for performance to create an\n// Intl.Collator object and use the function provided by its compare property\n// than it is to use String.prototype.localeCompare()\n// A collator with numeric sorting enabled, and no sensitivity to case, accents or diacritics.\nconst intlFileNameCollatorBaseNumeric = new Lazy(() => {\n    const collator = new Intl.Collator(undefined, { numeric: true, sensitivity: 'base' });\n    return {\n        collator,\n        collatorIsNumeric: collator.resolvedOptions().numeric\n    };\n});\n// A collator with numeric sorting enabled.\nconst intlFileNameCollatorNumeric = new Lazy(() => {\n    const collator = new Intl.Collator(undefined, { numeric: true });\n    return {\n        collator\n    };\n});\n// A collator with numeric sorting enabled, and sensitivity to accents and diacritics but not case.\nconst intlFileNameCollatorNumericCaseInsensitive = new Lazy(() => {\n    const collator = new Intl.Collator(undefined, { numeric: true, sensitivity: 'accent' });\n    return {\n        collator\n    };\n});\n/** Compares filenames without distinguishing the name from the extension. Disambiguates by unicode comparison. */\nexport function compareFileNames(one, other, caseSensitive = false) {\n    const a = one || '';\n    const b = other || '';\n    const result = intlFileNameCollatorBaseNumeric.value.collator.compare(a, b);\n    // Using the numeric option will make compare(`foo1`, `foo01`) === 0. Disambiguate.\n    if (intlFileNameCollatorBaseNumeric.value.collatorIsNumeric && result === 0 && a !== b) {\n        return a < b ? -1 : 1;\n    }\n    return result;\n}\nexport function compareAnything(one, other, lookFor) {\n    const elementAName = one.toLowerCase();\n    const elementBName = other.toLowerCase();\n    // Sort prefix matches over non prefix matches\n    const prefixCompare = compareByPrefix(one, other, lookFor);\n    if (prefixCompare) {\n        return prefixCompare;\n    }\n    // Sort suffix matches over non suffix matches\n    const elementASuffixMatch = elementAName.endsWith(lookFor);\n    const elementBSuffixMatch = elementBName.endsWith(lookFor);\n    if (elementASuffixMatch !== elementBSuffixMatch) {\n        return elementASuffixMatch ? -1 : 1;\n    }\n    // Understand file names\n    const r = compareFileNames(elementAName, elementBName);\n    if (r !== 0) {\n        return r;\n    }\n    // Compare by name\n    return elementAName.localeCompare(elementBName);\n}\nexport function compareByPrefix(one, other, lookFor) {\n    const elementAName = one.toLowerCase();\n    const elementBName = other.toLowerCase();\n    // Sort prefix matches over non prefix matches\n    const elementAPrefixMatch = elementAName.startsWith(lookFor);\n    const elementBPrefixMatch = elementBName.startsWith(lookFor);\n    if (elementAPrefixMatch !== elementBPrefixMatch) {\n        return elementAPrefixMatch ? -1 : 1;\n    }\n    // Same prefix: Sort shorter matches to the top to have those on top that match more precisely\n    else if (elementAPrefixMatch && elementBPrefixMatch) {\n        if (elementAName.length < elementBName.length) {\n            return -1;\n        }\n        if (elementAName.length > elementBName.length) {\n            return 1;\n        }\n    }\n    return 0;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,IAAI,QAAQ,WAAW;AAChC;AACA;AACA;AACA;AACA,MAAMC,+BAA+B,GAAG,IAAID,IAAI,CAAC,MAAM;EACnD,MAAME,QAAQ,GAAG,IAAIC,IAAI,CAACC,QAAQ,CAACC,SAAS,EAAE;IAAEC,OAAO,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAO,CAAC,CAAC;EACrF,OAAO;IACHL,QAAQ;IACRM,iBAAiB,EAAEN,QAAQ,CAACO,eAAe,CAAC,CAAC,CAACH;EAClD,CAAC;AACL,CAAC,CAAC;AACF;AACA,MAAMI,2BAA2B,GAAG,IAAIV,IAAI,CAAC,MAAM;EAC/C,MAAME,QAAQ,GAAG,IAAIC,IAAI,CAACC,QAAQ,CAACC,SAAS,EAAE;IAAEC,OAAO,EAAE;EAAK,CAAC,CAAC;EAChE,OAAO;IACHJ;EACJ,CAAC;AACL,CAAC,CAAC;AACF;AACA,MAAMS,0CAA0C,GAAG,IAAIX,IAAI,CAAC,MAAM;EAC9D,MAAME,QAAQ,GAAG,IAAIC,IAAI,CAACC,QAAQ,CAACC,SAAS,EAAE;IAAEC,OAAO,EAAE,IAAI;IAAEC,WAAW,EAAE;EAAS,CAAC,CAAC;EACvF,OAAO;IACHL;EACJ,CAAC;AACL,CAAC,CAAC;AACF;AACA,OAAO,SAASU,gBAAgBA,CAACC,GAAG,EAAEC,KAAK,EAAEC,aAAa,GAAG,KAAK,EAAE;EAChE,MAAMC,CAAC,GAAGH,GAAG,IAAI,EAAE;EACnB,MAAMI,CAAC,GAAGH,KAAK,IAAI,EAAE;EACrB,MAAMI,MAAM,GAAGjB,+BAA+B,CAACkB,KAAK,CAACjB,QAAQ,CAACkB,OAAO,CAACJ,CAAC,EAAEC,CAAC,CAAC;EAC3E;EACA,IAAIhB,+BAA+B,CAACkB,KAAK,CAACX,iBAAiB,IAAIU,MAAM,KAAK,CAAC,IAAIF,CAAC,KAAKC,CAAC,EAAE;IACpF,OAAOD,CAAC,GAAGC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;EACzB;EACA,OAAOC,MAAM;AACjB;AACA,OAAO,SAASG,eAAeA,CAACR,GAAG,EAAEC,KAAK,EAAEQ,OAAO,EAAE;EACjD,MAAMC,YAAY,GAAGV,GAAG,CAACW,WAAW,CAAC,CAAC;EACtC,MAAMC,YAAY,GAAGX,KAAK,CAACU,WAAW,CAAC,CAAC;EACxC;EACA,MAAME,aAAa,GAAGC,eAAe,CAACd,GAAG,EAAEC,KAAK,EAAEQ,OAAO,CAAC;EAC1D,IAAII,aAAa,EAAE;IACf,OAAOA,aAAa;EACxB;EACA;EACA,MAAME,mBAAmB,GAAGL,YAAY,CAACM,QAAQ,CAACP,OAAO,CAAC;EAC1D,MAAMQ,mBAAmB,GAAGL,YAAY,CAACI,QAAQ,CAACP,OAAO,CAAC;EAC1D,IAAIM,mBAAmB,KAAKE,mBAAmB,EAAE;IAC7C,OAAOF,mBAAmB,GAAG,CAAC,CAAC,GAAG,CAAC;EACvC;EACA;EACA,MAAMG,CAAC,GAAGnB,gBAAgB,CAACW,YAAY,EAAEE,YAAY,CAAC;EACtD,IAAIM,CAAC,KAAK,CAAC,EAAE;IACT,OAAOA,CAAC;EACZ;EACA;EACA,OAAOR,YAAY,CAACS,aAAa,CAACP,YAAY,CAAC;AACnD;AACA,OAAO,SAASE,eAAeA,CAACd,GAAG,EAAEC,KAAK,EAAEQ,OAAO,EAAE;EACjD,MAAMC,YAAY,GAAGV,GAAG,CAACW,WAAW,CAAC,CAAC;EACtC,MAAMC,YAAY,GAAGX,KAAK,CAACU,WAAW,CAAC,CAAC;EACxC;EACA,MAAMS,mBAAmB,GAAGV,YAAY,CAACW,UAAU,CAACZ,OAAO,CAAC;EAC5D,MAAMa,mBAAmB,GAAGV,YAAY,CAACS,UAAU,CAACZ,OAAO,CAAC;EAC5D,IAAIW,mBAAmB,KAAKE,mBAAmB,EAAE;IAC7C,OAAOF,mBAAmB,GAAG,CAAC,CAAC,GAAG,CAAC;EACvC;EACA;EAAA,KACK,IAAIA,mBAAmB,IAAIE,mBAAmB,EAAE;IACjD,IAAIZ,YAAY,CAACa,MAAM,GAAGX,YAAY,CAACW,MAAM,EAAE;MAC3C,OAAO,CAAC,CAAC;IACb;IACA,IAAIb,YAAY,CAACa,MAAM,GAAGX,YAAY,CAACW,MAAM,EAAE;MAC3C,OAAO,CAAC;IACZ;EACJ;EACA,OAAO,CAAC;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}