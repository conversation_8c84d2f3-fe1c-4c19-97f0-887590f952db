import { inject, Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { EnvironmentService } from '@shared/services/environment.service';
import { catchError, map, Observable, of } from 'rxjs';
import { CardData } from '../../shared/models/card.model';
import { webSocket, WebSocketSubject } from 'rxjs/webSocket';
import { TokenStorageService } from '@shared/auth/services/token-storage.service';

@Injectable({
  providedIn: 'root',
})
export class WorkflowService {
  private environmentService = inject(EnvironmentService);
  private apiServiceUrl = this.environmentService.consoleApi;
  private baseUrl = this.environmentService.consoleApiV2;
  private pipelineAPI = this.environmentService.consoleInstructionApi;
  private headers = {
    headers: new HttpHeaders({
      'Content-Type': 'application/json',
    }),
  };
  private http = inject(HttpClient); //  Injecting HttpClient

  public socket$!: WebSocketSubject<any>;

  // ::: Hardcoded Access key, Should be removed once API issue is resolved
  private ACCESS_KEY = ``;

  constructor(private tokenStorageService: TokenStorageService) {}

  /**
   * Fetches all prompt data from the API
   *
   * @returns Observable emitting an array of CardData items
   * - On success: returns the fetched data
   * - On error: logs the error and returns an empty array
   */
  fetchAllWorkflows(): Observable<CardData[]> {
    const url = `${this.apiServiceUrl}/ava/force/workflow`;
    return this.http
      .get<CardData[]>(
        url,
        this.headers, //  Pass headers correctly
      )
      .pipe(
        map((response: CardData[]) => {
          return response; //  Return the response data
        }),
        catchError((error: any) => {
          console.error('API error:', error); //  Log the API error
          return of([]); //  Fallback: return an empty array on error
        }),
      );
  }

  fetchAllV2Workflows(page: number, records: number, isDeleted: boolean): Observable<CardData[]> {
    const url = `${this.baseUrl}/ava/force/da/workflow/approved`;
    const params = new HttpParams()
      .set('page', page.toString())
      .set('records', records.toString())
      .set('isDeleted', isDeleted.toString());
    
    return this.http
      .get<CardData[]>(
        url,
        {params}
      )
      .pipe(
        map((response: CardData[]) => {
          return response; //  Return the response data
        }),
        catchError((error: any) => {
          console.error('API error:', error); //  Log the API error
          return of([]); //  Fallback: return an empty array on error
        }),
      );
  }

  /**
   * Deletes a workflow by its ID.
   * @param workflowId The ID of the workflow to delete.
   * @returns Observable of the delete operation result.
   */
  deleteWorkflowById(workflowId: string): Observable<any> {
    console.log(workflowId);
    const url = `${this.apiServiceUrl}/ava/force/workflow?workflowId=${workflowId}`;
    return this.http.delete(url, this.headers);
  }

  deleteWorkflow(workflowId: string, modifiedBy: string): Observable<any> {
    console.log(workflowId);
    const url = `${this.baseUrl}/ava/force/da/workflow/change_request?workflowId=${workflowId}&modifiedBy=${modifiedBy}`;
    return this.http.delete(url, this.headers);
  }

  /**
   * Fetches the dropdown list options based on the level number and parent level ID.
   * @param levelNum The hierarchical level number to retrieve options for.
   * @param parentLvlId The ID of the parent level to filter the options.
   * @returns Observable of an array of dropdown options with `value` and `label`.
   */

  getDropdownList(levelNum: number, parentLvlId: number) {
    const url = `${this.apiServiceUrl}/ava/force/level?levelNum=${levelNum}&parentLvlId=${parentLvlId}`;
    return this.http.get(url, this.headers).pipe(
      map((res: any) => {
        const optionsArray = (res?.levels || []).map((item: any) => ({
          value: item.levelId,
          label: item.name,
        }));
        return optionsArray;
      }),
    );
  }

  getAllGenerativeModels(modelType = 'Generative') {
    const url = `${this.apiServiceUrl}/ava/force/model`;
    return this.http
      .get(url, { ...this.headers, params: { modelType } })
      .pipe(map((response: any) => response?.models || []));
  }

  private workflowSavePutUrl = `${this.baseUrl}/ava/force/da/workflow`;

  saveWorkFlow(payload: any) {
    return this.http.post(this.workflowSavePutUrl, payload, this.headers);
  }

  updateWorkFlow(payload: any) {
    const url = `${this.baseUrl}/ava/force/da/workflow/change_request`
    return this.http.put(url, payload, this.headers);
  }

  // Initialize WebSocket connection
  public workflowLogConnect(executionId: string): Observable<any> {
    this.ACCESS_KEY = this.tokenStorageService.getAccessToken() || '';
    const config = this.environmentService.getConfig();
    const wsUrl = config['logStreamingApiUrl'] || 'wss://aava-dev.avateam.io/ws-pipeline-log-stream';
    const fullWsUrl = `${wsUrl}?executionId=${executionId}&access-key=${this.ACCESS_KEY}`;
    console.log('Full WebSocket URL:', fullWsUrl);

    this.socket$ = webSocket(fullWsUrl);
    return this.socket$;
  }

  // Send a message to the WebSocket server
  public sendMessage(message: any): void {
    if (this.socket$) {
      this.socket$.next(message);
    }
  }

  // Disconnect from the WebSocket server
  public workflowLogDisconnect(): void {
    this.socket$.complete();
  }

  public executeWorkflow(
    payload: FormData | Record<string, any>,
    queryString: string,
  ) {
    // const pipelineBaseUrl = `${this.pipelineAPI}/workflow`;
    const url = `${this.pipelineAPI}/ava/force/workflow/execute${queryString}`;

    return this.http.post(url, payload).pipe(
      map((response: any) => {
        return response;
      }),
    );
  }

  getOneWorkflow(workflowId: string) {
    const url = `${this.apiServiceUrl}/ava/force/workflow`;
    return this.http.get(url, { ...this.headers, params: { workflowId } }).pipe(
      map((response: any) => {
        return response?.pipeline || {};
      }),
    );
  }

  getWorkflowById(workflowId: string){
    const url = `${this.baseUrl}/ava/force/da/workflow`;
    return this.http.get(url, { ...this.headers, params: { workflowId } }).pipe(
      map((response: any) => {
        return response?.workflowDetail || {};
      }),
    );
  }
}
