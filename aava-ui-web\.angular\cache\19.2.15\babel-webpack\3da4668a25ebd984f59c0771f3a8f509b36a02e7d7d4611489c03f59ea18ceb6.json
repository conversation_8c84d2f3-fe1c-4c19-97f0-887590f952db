{"ast": null, "code": "import { Mimes } from '../common/mime.js';\n// Common data transfers\nexport const DataTransfers = {\n  /**\n   * Application specific resource transfer type\n   */\n  RESOURCES: 'ResourceURLs',\n  /**\n   * Browser specific transfer type to download\n   */\n  DOWNLOAD_URL: 'DownloadURL',\n  /**\n   * Browser specific transfer type for files\n   */\n  FILES: 'Files',\n  /**\n   * Typically transfer type for copy/paste transfers.\n   */\n  TEXT: Mimes.text,\n  /**\n   * Internal type used to pass around text/uri-list data.\n   *\n   * This is needed to work around https://bugs.chromium.org/p/chromium/issues/detail?id=239745.\n   */\n  INTERNAL_URI_LIST: 'application/vnd.code.uri-list'\n};", "map": {"version": 3, "names": ["<PERSON><PERSON>", "DataTransfers", "RESOURCES", "DOWNLOAD_URL", "FILES", "TEXT", "text", "INTERNAL_URI_LIST"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/browser/dnd.js"], "sourcesContent": ["import { Mimes } from '../common/mime.js';\n// Common data transfers\nexport const DataTransfers = {\n    /**\n     * Application specific resource transfer type\n     */\n    RESOURCES: 'ResourceURLs',\n    /**\n     * Browser specific transfer type to download\n     */\n    DOWNLOAD_URL: 'DownloadURL',\n    /**\n     * Browser specific transfer type for files\n     */\n    FILES: 'Files',\n    /**\n     * Typically transfer type for copy/paste transfers.\n     */\n    TEXT: Mimes.text,\n    /**\n     * Internal type used to pass around text/uri-list data.\n     *\n     * This is needed to work around https://bugs.chromium.org/p/chromium/issues/detail?id=239745.\n     */\n    INTERNAL_URI_LIST: 'application/vnd.code.uri-list',\n};\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,mBAAmB;AACzC;AACA,OAAO,MAAMC,aAAa,GAAG;EACzB;AACJ;AACA;EACIC,SAAS,EAAE,cAAc;EACzB;AACJ;AACA;EACIC,YAAY,EAAE,aAAa;EAC3B;AACJ;AACA;EACIC,KAAK,EAAE,OAAO;EACd;AACJ;AACA;EACIC,IAAI,EAAEL,KAAK,CAACM,IAAI;EAChB;AACJ;AACA;AACA;AACA;EACIC,iBAAiB,EAAE;AACvB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}