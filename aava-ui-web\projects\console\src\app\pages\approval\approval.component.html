<div class="approval">
    <div class="approval-left-screen" [class.quick-actions-expanded]="quickActionsExpanded">
        <div class="quick-actions-wrapper" [class.expanded]="quickActionsExpanded">
            <div class="quick-actions-toggle">
                <span class="left-title" *ngIf="quickActionsExpanded">Category</span>
                <lucide-icon name="panel-left" class="collapse-icon" (click)="toggleQuickActions()"></lucide-icon>
            </div>

            <!-- Expanded view with text labels -->
            <div class="quick-actions-content" *ngIf="quickActionsExpanded">
                <div class="action-buttons">
                    <button class="action-button" 
                            [class.active-action]="currentTab === labels.agents"
                            (click)="redirectToListOfApproval(labels.agents)">
                        <div class="action-icon">
                            <img [src]="'svgs/icons/' + 'awe_agents' + '.svg'" [alt]="labels.agents" width="24" height="24" title="{{labels.agents}}" />
                        </div>
                        <span class="action-label">{{labels.agents}}</span>
                    </button>
                    
                    <button class="action-button" 
                            [class.active-action]="currentTab === labels.workflows"
                            (click)="redirectToListOfApproval(labels.workflows)">
                        <div class="action-icon">
                            <img [src]="'svgs/icons/' + 'awe_workflows' + '.svg'" [alt]="labels.workflows" width="24" height="24" title="{{labels.workflows}}" />
                        </div>
                        <span class="action-label">{{labels.workflows}}</span>
                    </button>
                    
                    <button class="action-button" 
                            [class.active-action]="currentTab === labels.tools"
                            (click)="redirectToListOfApproval(labels.tools)">
                        <div class="action-icon">
                            <img [src]="'svgs/icons/' + 'awe_tools' + '.svg'" [alt]="labels.tools" width="24" height="24" title="{{labels.tools}}" />
                        </div>
                        <span class="action-label">{{labels.tools}}</span>
                    </button>
                </div>
            </div>

            <!-- Collapsed view with icons only -->
            <div class="quick-actions-icons" *ngIf="!quickActionsExpanded">
                <button class="icon-button" 
                        [class.active-action]="currentTab === labels.agents"
                        (click)="redirectToListOfApproval(labels.agents)" 
                        [title]="labels.agents" 
                        title="{{labels.agents}}">
                    <div class="icon-wrapper">
                        <img [src]="'svgs/icons/' + 'awe_agents' + '.svg'" [alt]="labels.agents" width="24" height="24" title="{{labels.agents}}" />
                    </div>
                </button>
                 <button class="icon-button" 
                         [class.active-action]="currentTab === labels.workflows"
                         (click)="redirectToListOfApproval(labels.workflows)" 
                         [title]="labels.workflows" 
                         title="{{labels.workflows}}">
                    <div class="icon-wrapper">
                        <img [src]="'svgs/icons/' + 'awe_workflows' + '.svg'" [alt]="labels.workflows" width="24" height="24" title="{{labels.workflows}}" />
                    </div>
                </button>
                <button class="icon-button" 
                        [class.active-action]="currentTab === labels.tools"
                        (click)="redirectToListOfApproval(labels.tools)" 
                        [title]="labels.tools" 
                        title="{{labels.tools}}">
                    <div class="icon-wrapper">
                        <img [src]="'svgs/icons/' + 'awe_tools' + '.svg'" [alt]="labels.tools" width="24" height="24" title="{{labels.tools}}" />
                    </div>
                </button>
            </div>
        </div>
    </div>
    
    <div class="approval-right-screen">
        <router-outlet></router-outlet>
    </div>
</div>

