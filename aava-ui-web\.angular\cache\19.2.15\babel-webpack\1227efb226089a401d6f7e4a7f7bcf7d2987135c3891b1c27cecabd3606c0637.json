{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport class ArrayNavigator {\n  constructor(items, start = 0, end = items.length, index = start - 1) {\n    this.items = items;\n    this.start = start;\n    this.end = end;\n    this.index = index;\n  }\n  current() {\n    if (this.index === this.start - 1 || this.index === this.end) {\n      return null;\n    }\n    return this.items[this.index];\n  }\n  next() {\n    this.index = Math.min(this.index + 1, this.end);\n    return this.current();\n  }\n  previous() {\n    this.index = Math.max(this.index - 1, this.start - 1);\n    return this.current();\n  }\n  first() {\n    this.index = this.start;\n    return this.current();\n  }\n  last() {\n    this.index = this.end - 1;\n    return this.current();\n  }\n}", "map": {"version": 3, "names": ["ArrayNavigator", "constructor", "items", "start", "end", "length", "index", "current", "next", "Math", "min", "previous", "max", "first", "last"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/common/navigator.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport class ArrayNavigator {\n    constructor(items, start = 0, end = items.length, index = start - 1) {\n        this.items = items;\n        this.start = start;\n        this.end = end;\n        this.index = index;\n    }\n    current() {\n        if (this.index === this.start - 1 || this.index === this.end) {\n            return null;\n        }\n        return this.items[this.index];\n    }\n    next() {\n        this.index = Math.min(this.index + 1, this.end);\n        return this.current();\n    }\n    previous() {\n        this.index = Math.max(this.index - 1, this.start - 1);\n        return this.current();\n    }\n    first() {\n        this.index = this.start;\n        return this.current();\n    }\n    last() {\n        this.index = this.end - 1;\n        return this.current();\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,MAAMA,cAAc,CAAC;EACxBC,WAAWA,CAACC,KAAK,EAAEC,KAAK,GAAG,CAAC,EAAEC,GAAG,GAAGF,KAAK,CAACG,MAAM,EAAEC,KAAK,GAAGH,KAAK,GAAG,CAAC,EAAE;IACjE,IAAI,CAACD,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,GAAG,GAAGA,GAAG;IACd,IAAI,CAACE,KAAK,GAAGA,KAAK;EACtB;EACAC,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACD,KAAK,KAAK,IAAI,CAACH,KAAK,GAAG,CAAC,IAAI,IAAI,CAACG,KAAK,KAAK,IAAI,CAACF,GAAG,EAAE;MAC1D,OAAO,IAAI;IACf;IACA,OAAO,IAAI,CAACF,KAAK,CAAC,IAAI,CAACI,KAAK,CAAC;EACjC;EACAE,IAAIA,CAAA,EAAG;IACH,IAAI,CAACF,KAAK,GAAGG,IAAI,CAACC,GAAG,CAAC,IAAI,CAACJ,KAAK,GAAG,CAAC,EAAE,IAAI,CAACF,GAAG,CAAC;IAC/C,OAAO,IAAI,CAACG,OAAO,CAAC,CAAC;EACzB;EACAI,QAAQA,CAAA,EAAG;IACP,IAAI,CAACL,KAAK,GAAGG,IAAI,CAACG,GAAG,CAAC,IAAI,CAACN,KAAK,GAAG,CAAC,EAAE,IAAI,CAACH,KAAK,GAAG,CAAC,CAAC;IACrD,OAAO,IAAI,CAACI,OAAO,CAAC,CAAC;EACzB;EACAM,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACP,KAAK,GAAG,IAAI,CAACH,KAAK;IACvB,OAAO,IAAI,CAACI,OAAO,CAAC,CAAC;EACzB;EACAO,IAAIA,CAAA,EAAG;IACH,IAAI,CAACR,KAAK,GAAG,IAAI,CAACF,GAAG,GAAG,CAAC;IACzB,OAAO,IAAI,CAACG,OAAO,CAAC,CAAC;EACzB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}