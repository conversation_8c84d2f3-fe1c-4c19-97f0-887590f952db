{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport class Array2D {\n  constructor(width, height) {\n    this.width = width;\n    this.height = height;\n    this.array = [];\n    this.array = new Array(width * height);\n  }\n  get(x, y) {\n    return this.array[x + y * this.width];\n  }\n  set(x, y, value) {\n    this.array[x + y * this.width] = value;\n  }\n}\nexport function isSpace(charCode) {\n  return charCode === 32 /* CharCode.Space */ || charCode === 9 /* CharCode.Tab */;\n}\nexport class LineRangeFragment {\n  static {\n    this.chrKeys = new Map();\n  }\n  static getKey(chr) {\n    let key = this.chrKeys.get(chr);\n    if (key === undefined) {\n      key = this.chrKeys.size;\n      this.chrKeys.set(chr, key);\n    }\n    return key;\n  }\n  constructor(range, lines, source) {\n    this.range = range;\n    this.lines = lines;\n    this.source = source;\n    this.histogram = [];\n    let counter = 0;\n    for (let i = range.startLineNumber - 1; i < range.endLineNumberExclusive - 1; i++) {\n      const line = lines[i];\n      for (let j = 0; j < line.length; j++) {\n        counter++;\n        const chr = line[j];\n        const key = LineRangeFragment.getKey(chr);\n        this.histogram[key] = (this.histogram[key] || 0) + 1;\n      }\n      counter++;\n      const key = LineRangeFragment.getKey('\\n');\n      this.histogram[key] = (this.histogram[key] || 0) + 1;\n    }\n    this.totalCount = counter;\n  }\n  computeSimilarity(other) {\n    let sumDifferences = 0;\n    const maxLength = Math.max(this.histogram.length, other.histogram.length);\n    for (let i = 0; i < maxLength; i++) {\n      sumDifferences += Math.abs((this.histogram[i] ?? 0) - (other.histogram[i] ?? 0));\n    }\n    return 1 - sumDifferences / (this.totalCount + other.totalCount);\n  }\n}", "map": {"version": 3, "names": ["Array2D", "constructor", "width", "height", "array", "Array", "get", "x", "y", "set", "value", "isSpace", "charCode", "LineRangeFragment", "chr<PERSON><PERSON><PERSON>", "Map", "<PERSON><PERSON><PERSON>", "chr", "key", "undefined", "size", "range", "lines", "source", "histogram", "counter", "i", "startLineNumber", "endLineNumberExclusive", "line", "j", "length", "totalCount", "computeSimilarity", "other", "sumDifferences", "max<PERSON><PERSON><PERSON>", "Math", "max", "abs"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/diff/defaultLinesDiffComputer/utils.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport class Array2D {\n    constructor(width, height) {\n        this.width = width;\n        this.height = height;\n        this.array = [];\n        this.array = new Array(width * height);\n    }\n    get(x, y) {\n        return this.array[x + y * this.width];\n    }\n    set(x, y, value) {\n        this.array[x + y * this.width] = value;\n    }\n}\nexport function isSpace(charCode) {\n    return charCode === 32 /* CharCode.Space */ || charCode === 9 /* CharCode.Tab */;\n}\nexport class LineRangeFragment {\n    static { this.chrKeys = new Map(); }\n    static getKey(chr) {\n        let key = this.chrKeys.get(chr);\n        if (key === undefined) {\n            key = this.chrKeys.size;\n            this.chrKeys.set(chr, key);\n        }\n        return key;\n    }\n    constructor(range, lines, source) {\n        this.range = range;\n        this.lines = lines;\n        this.source = source;\n        this.histogram = [];\n        let counter = 0;\n        for (let i = range.startLineNumber - 1; i < range.endLineNumberExclusive - 1; i++) {\n            const line = lines[i];\n            for (let j = 0; j < line.length; j++) {\n                counter++;\n                const chr = line[j];\n                const key = LineRangeFragment.getKey(chr);\n                this.histogram[key] = (this.histogram[key] || 0) + 1;\n            }\n            counter++;\n            const key = LineRangeFragment.getKey('\\n');\n            this.histogram[key] = (this.histogram[key] || 0) + 1;\n        }\n        this.totalCount = counter;\n    }\n    computeSimilarity(other) {\n        let sumDifferences = 0;\n        const maxLength = Math.max(this.histogram.length, other.histogram.length);\n        for (let i = 0; i < maxLength; i++) {\n            sumDifferences += Math.abs((this.histogram[i] ?? 0) - (other.histogram[i] ?? 0));\n        }\n        return 1 - (sumDifferences / (this.totalCount + other.totalCount));\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,MAAMA,OAAO,CAAC;EACjBC,WAAWA,CAACC,KAAK,EAAEC,MAAM,EAAE;IACvB,IAAI,CAACD,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,KAAK,GAAG,EAAE;IACf,IAAI,CAACA,KAAK,GAAG,IAAIC,KAAK,CAACH,KAAK,GAAGC,MAAM,CAAC;EAC1C;EACAG,GAAGA,CAACC,CAAC,EAAEC,CAAC,EAAE;IACN,OAAO,IAAI,CAACJ,KAAK,CAACG,CAAC,GAAGC,CAAC,GAAG,IAAI,CAACN,KAAK,CAAC;EACzC;EACAO,GAAGA,CAACF,CAAC,EAAEC,CAAC,EAAEE,KAAK,EAAE;IACb,IAAI,CAACN,KAAK,CAACG,CAAC,GAAGC,CAAC,GAAG,IAAI,CAACN,KAAK,CAAC,GAAGQ,KAAK;EAC1C;AACJ;AACA,OAAO,SAASC,OAAOA,CAACC,QAAQ,EAAE;EAC9B,OAAOA,QAAQ,KAAK,EAAE,CAAC,wBAAwBA,QAAQ,KAAK,CAAC,CAAC;AAClE;AACA,OAAO,MAAMC,iBAAiB,CAAC;EAC3B;IAAS,IAAI,CAACC,OAAO,GAAG,IAAIC,GAAG,CAAC,CAAC;EAAE;EACnC,OAAOC,MAAMA,CAACC,GAAG,EAAE;IACf,IAAIC,GAAG,GAAG,IAAI,CAACJ,OAAO,CAACR,GAAG,CAACW,GAAG,CAAC;IAC/B,IAAIC,GAAG,KAAKC,SAAS,EAAE;MACnBD,GAAG,GAAG,IAAI,CAACJ,OAAO,CAACM,IAAI;MACvB,IAAI,CAACN,OAAO,CAACL,GAAG,CAACQ,GAAG,EAAEC,GAAG,CAAC;IAC9B;IACA,OAAOA,GAAG;EACd;EACAjB,WAAWA,CAACoB,KAAK,EAAEC,KAAK,EAAEC,MAAM,EAAE;IAC9B,IAAI,CAACF,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAIC,OAAO,GAAG,CAAC;IACf,KAAK,IAAIC,CAAC,GAAGL,KAAK,CAACM,eAAe,GAAG,CAAC,EAAED,CAAC,GAAGL,KAAK,CAACO,sBAAsB,GAAG,CAAC,EAAEF,CAAC,EAAE,EAAE;MAC/E,MAAMG,IAAI,GAAGP,KAAK,CAACI,CAAC,CAAC;MACrB,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,IAAI,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;QAClCL,OAAO,EAAE;QACT,MAAMR,GAAG,GAAGY,IAAI,CAACC,CAAC,CAAC;QACnB,MAAMZ,GAAG,GAAGL,iBAAiB,CAACG,MAAM,CAACC,GAAG,CAAC;QACzC,IAAI,CAACO,SAAS,CAACN,GAAG,CAAC,GAAG,CAAC,IAAI,CAACM,SAAS,CAACN,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;MACxD;MACAO,OAAO,EAAE;MACT,MAAMP,GAAG,GAAGL,iBAAiB,CAACG,MAAM,CAAC,IAAI,CAAC;MAC1C,IAAI,CAACQ,SAAS,CAACN,GAAG,CAAC,GAAG,CAAC,IAAI,CAACM,SAAS,CAACN,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;IACxD;IACA,IAAI,CAACc,UAAU,GAAGP,OAAO;EAC7B;EACAQ,iBAAiBA,CAACC,KAAK,EAAE;IACrB,IAAIC,cAAc,GAAG,CAAC;IACtB,MAAMC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,IAAI,CAACd,SAAS,CAACO,MAAM,EAAEG,KAAK,CAACV,SAAS,CAACO,MAAM,CAAC;IACzE,KAAK,IAAIL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGU,SAAS,EAAEV,CAAC,EAAE,EAAE;MAChCS,cAAc,IAAIE,IAAI,CAACE,GAAG,CAAC,CAAC,IAAI,CAACf,SAAS,CAACE,CAAC,CAAC,IAAI,CAAC,KAAKQ,KAAK,CAACV,SAAS,CAACE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;IACpF;IACA,OAAO,CAAC,GAAIS,cAAc,IAAI,IAAI,CAACH,UAAU,GAAGE,KAAK,CAACF,UAAU,CAAE;EACtE;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}