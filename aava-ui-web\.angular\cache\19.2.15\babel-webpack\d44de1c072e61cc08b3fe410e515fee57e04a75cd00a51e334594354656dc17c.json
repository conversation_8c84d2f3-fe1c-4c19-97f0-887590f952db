{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { asArray } from '../../../../base/common/arrays.js';\nimport { isEmptyMarkdownString } from '../../../../base/common/htmlContent.js';\nimport { GlyphMarginLane } from '../../../common/model.js';\nexport class MarginHoverComputer {\n  get lineNumber() {\n    return this._lineNumber;\n  }\n  set lineNumber(value) {\n    this._lineNumber = value;\n  }\n  get lane() {\n    return this._laneOrLine;\n  }\n  set lane(value) {\n    this._laneOrLine = value;\n  }\n  constructor(_editor) {\n    this._editor = _editor;\n    this._lineNumber = -1;\n    this._laneOrLine = GlyphMarginLane.Center;\n  }\n  computeSync() {\n    const toHoverMessage = contents => {\n      return {\n        value: contents\n      };\n    };\n    const lineDecorations = this._editor.getLineDecorations(this._lineNumber);\n    const result = [];\n    const isLineHover = this._laneOrLine === 'lineNo';\n    if (!lineDecorations) {\n      return result;\n    }\n    for (const d of lineDecorations) {\n      const lane = d.options.glyphMargin?.position ?? GlyphMarginLane.Center;\n      if (!isLineHover && lane !== this._laneOrLine) {\n        continue;\n      }\n      const hoverMessage = isLineHover ? d.options.lineNumberHoverMessage : d.options.glyphMarginHoverMessage;\n      if (!hoverMessage || isEmptyMarkdownString(hoverMessage)) {\n        continue;\n      }\n      result.push(...asArray(hoverMessage).map(toHoverMessage));\n    }\n    return result;\n  }\n}", "map": {"version": 3, "names": ["asArray", "isEmptyMarkdownString", "GlyphMarginLane", "MarginHoverComputer", "lineNumber", "_lineNumber", "value", "lane", "_laneOrLine", "constructor", "_editor", "Center", "computeSync", "toHoverMessage", "contents", "lineDecorations", "getLineDecorations", "result", "isLineHover", "d", "options", "glyphMargin", "position", "hoverMessage", "lineNumberHoverMessage", "glyphMarginHoverMessage", "push", "map"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/contrib/hover/browser/marginHoverComputer.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { asArray } from '../../../../base/common/arrays.js';\nimport { isEmptyMarkdownString } from '../../../../base/common/htmlContent.js';\nimport { GlyphMarginLane } from '../../../common/model.js';\nexport class MarginHoverComputer {\n    get lineNumber() {\n        return this._lineNumber;\n    }\n    set lineNumber(value) {\n        this._lineNumber = value;\n    }\n    get lane() {\n        return this._laneOrLine;\n    }\n    set lane(value) {\n        this._laneOrLine = value;\n    }\n    constructor(_editor) {\n        this._editor = _editor;\n        this._lineNumber = -1;\n        this._laneOrLine = GlyphMarginLane.Center;\n    }\n    computeSync() {\n        const toHoverMessage = (contents) => {\n            return {\n                value: contents\n            };\n        };\n        const lineDecorations = this._editor.getLineDecorations(this._lineNumber);\n        const result = [];\n        const isLineHover = this._laneOrLine === 'lineNo';\n        if (!lineDecorations) {\n            return result;\n        }\n        for (const d of lineDecorations) {\n            const lane = d.options.glyphMargin?.position ?? GlyphMarginLane.Center;\n            if (!isLineHover && lane !== this._laneOrLine) {\n                continue;\n            }\n            const hoverMessage = isLineHover ? d.options.lineNumberHoverMessage : d.options.glyphMarginHoverMessage;\n            if (!hoverMessage || isEmptyMarkdownString(hoverMessage)) {\n                continue;\n            }\n            result.push(...asArray(hoverMessage).map(toHoverMessage));\n        }\n        return result;\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,OAAO,QAAQ,mCAAmC;AAC3D,SAASC,qBAAqB,QAAQ,wCAAwC;AAC9E,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,OAAO,MAAMC,mBAAmB,CAAC;EAC7B,IAAIC,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACC,WAAW;EAC3B;EACA,IAAID,UAAUA,CAACE,KAAK,EAAE;IAClB,IAAI,CAACD,WAAW,GAAGC,KAAK;EAC5B;EACA,IAAIC,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACC,WAAW;EAC3B;EACA,IAAID,IAAIA,CAACD,KAAK,EAAE;IACZ,IAAI,CAACE,WAAW,GAAGF,KAAK;EAC5B;EACAG,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACL,WAAW,GAAG,CAAC,CAAC;IACrB,IAAI,CAACG,WAAW,GAAGN,eAAe,CAACS,MAAM;EAC7C;EACAC,WAAWA,CAAA,EAAG;IACV,MAAMC,cAAc,GAAIC,QAAQ,IAAK;MACjC,OAAO;QACHR,KAAK,EAAEQ;MACX,CAAC;IACL,CAAC;IACD,MAAMC,eAAe,GAAG,IAAI,CAACL,OAAO,CAACM,kBAAkB,CAAC,IAAI,CAACX,WAAW,CAAC;IACzE,MAAMY,MAAM,GAAG,EAAE;IACjB,MAAMC,WAAW,GAAG,IAAI,CAACV,WAAW,KAAK,QAAQ;IACjD,IAAI,CAACO,eAAe,EAAE;MAClB,OAAOE,MAAM;IACjB;IACA,KAAK,MAAME,CAAC,IAAIJ,eAAe,EAAE;MAC7B,MAAMR,IAAI,GAAGY,CAAC,CAACC,OAAO,CAACC,WAAW,EAAEC,QAAQ,IAAIpB,eAAe,CAACS,MAAM;MACtE,IAAI,CAACO,WAAW,IAAIX,IAAI,KAAK,IAAI,CAACC,WAAW,EAAE;QAC3C;MACJ;MACA,MAAMe,YAAY,GAAGL,WAAW,GAAGC,CAAC,CAACC,OAAO,CAACI,sBAAsB,GAAGL,CAAC,CAACC,OAAO,CAACK,uBAAuB;MACvG,IAAI,CAACF,YAAY,IAAItB,qBAAqB,CAACsB,YAAY,CAAC,EAAE;QACtD;MACJ;MACAN,MAAM,CAACS,IAAI,CAAC,GAAG1B,OAAO,CAACuB,YAAY,CAAC,CAACI,GAAG,CAACd,cAAc,CAAC,CAAC;IAC7D;IACA,OAAOI,MAAM;EACjB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}