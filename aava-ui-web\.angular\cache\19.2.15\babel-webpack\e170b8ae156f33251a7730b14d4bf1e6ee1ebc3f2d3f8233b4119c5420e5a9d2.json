{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport class BracketInfo {\n  constructor(range, /** 0-based level */\n  nestingLevel, nestingLevelOfEqualBracketType, isInvalid) {\n    this.range = range;\n    this.nestingLevel = nestingLevel;\n    this.nestingLevelOfEqualBracketType = nestingLevelOfEqualBracketType;\n    this.isInvalid = isInvalid;\n  }\n}\nexport class BracketPairInfo {\n  constructor(range, openingBracketRange, closingBracketRange, /** 0-based */\n  nestingLevel, nestingLevelOfEqualBracketType, bracketPairNode) {\n    this.range = range;\n    this.openingBracketRange = openingBracketRange;\n    this.closingBracketRange = closingBracketRange;\n    this.nestingLevel = nestingLevel;\n    this.nestingLevelOfEqualBracketType = nestingLevelOfEqualBracketType;\n    this.bracketPairNode = bracketPairNode;\n  }\n  get openingBracketInfo() {\n    return this.bracketPairNode.openingBracket.bracketInfo;\n  }\n}\nexport class BracketPairWithMinIndentationInfo extends BracketPairInfo {\n  constructor(range, openingBracketRange, closingBracketRange,\n  /**\n   * 0-based\n  */\n  nestingLevel, nestingLevelOfEqualBracketType, bracketPairNode,\n  /**\n   * -1 if not requested, otherwise the size of the minimum indentation in the bracket pair in terms of visible columns.\n  */\n  minVisibleColumnIndentation) {\n    super(range, openingBracketRange, closingBracketRange, nestingLevel, nestingLevelOfEqualBracketType, bracketPairNode);\n    this.minVisibleColumnIndentation = minVisibleColumnIndentation;\n  }\n}", "map": {"version": 3, "names": ["BracketInfo", "constructor", "range", "nestingLevel", "nestingLevelOfEqualBracketType", "isInvalid", "BracketPairInfo", "openingBracketRange", "closingBracketRange", "bracketPairNode", "openingBracketInfo", "openingBracket", "bracketInfo", "BracketPairWithMinIndentationInfo", "minVisibleColumnIndentation"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/textModelBracketPairs.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport class BracketInfo {\n    constructor(range, \n    /** 0-based level */\n    nestingLevel, nestingLevelOfEqualBracketType, isInvalid) {\n        this.range = range;\n        this.nestingLevel = nestingLevel;\n        this.nestingLevelOfEqualBracketType = nestingLevelOfEqualBracketType;\n        this.isInvalid = isInvalid;\n    }\n}\nexport class BracketPairInfo {\n    constructor(range, openingBracketRange, closingBracketRange, \n    /** 0-based */\n    nestingLevel, nestingLevelOfEqualBracketType, bracketPairNode) {\n        this.range = range;\n        this.openingBracketRange = openingBracketRange;\n        this.closingBracketRange = closingBracketRange;\n        this.nestingLevel = nestingLevel;\n        this.nestingLevelOfEqualBracketType = nestingLevelOfEqualBracketType;\n        this.bracketPairNode = bracketPairNode;\n    }\n    get openingBracketInfo() {\n        return this.bracketPairNode.openingBracket.bracketInfo;\n    }\n}\nexport class BracketPairWithMinIndentationInfo extends BracketPairInfo {\n    constructor(range, openingBracketRange, closingBracketRange, \n    /**\n     * 0-based\n    */\n    nestingLevel, nestingLevelOfEqualBracketType, bracketPairNode, \n    /**\n     * -1 if not requested, otherwise the size of the minimum indentation in the bracket pair in terms of visible columns.\n    */\n    minVisibleColumnIndentation) {\n        super(range, openingBracketRange, closingBracketRange, nestingLevel, nestingLevelOfEqualBracketType, bracketPairNode);\n        this.minVisibleColumnIndentation = minVisibleColumnIndentation;\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,MAAMA,WAAW,CAAC;EACrBC,WAAWA,CAACC,KAAK,EACjB;EACAC,YAAY,EAAEC,8BAA8B,EAAEC,SAAS,EAAE;IACrD,IAAI,CAACH,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,8BAA8B,GAAGA,8BAA8B;IACpE,IAAI,CAACC,SAAS,GAAGA,SAAS;EAC9B;AACJ;AACA,OAAO,MAAMC,eAAe,CAAC;EACzBL,WAAWA,CAACC,KAAK,EAAEK,mBAAmB,EAAEC,mBAAmB,EAC3D;EACAL,YAAY,EAAEC,8BAA8B,EAAEK,eAAe,EAAE;IAC3D,IAAI,CAACP,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACK,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACC,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACL,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,8BAA8B,GAAGA,8BAA8B;IACpE,IAAI,CAACK,eAAe,GAAGA,eAAe;EAC1C;EACA,IAAIC,kBAAkBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAACD,eAAe,CAACE,cAAc,CAACC,WAAW;EAC1D;AACJ;AACA,OAAO,MAAMC,iCAAiC,SAASP,eAAe,CAAC;EACnEL,WAAWA,CAACC,KAAK,EAAEK,mBAAmB,EAAEC,mBAAmB;EAC3D;AACJ;AACA;EACIL,YAAY,EAAEC,8BAA8B,EAAEK,eAAe;EAC7D;AACJ;AACA;EACIK,2BAA2B,EAAE;IACzB,KAAK,CAACZ,KAAK,EAAEK,mBAAmB,EAAEC,mBAAmB,EAAEL,YAAY,EAAEC,8BAA8B,EAAEK,eAAe,CAAC;IACrH,IAAI,CAACK,2BAA2B,GAAGA,2BAA2B;EAClE;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}