{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { h } from '../../../../../base/browser/dom.js';\nimport { ActionBar } from '../../../../../base/browser/ui/actionbar/actionbar.js';\nimport { Action } from '../../../../../base/common/actions.js';\nimport { booleanComparator, compareBy, numberComparator, tieBreakComparators } from '../../../../../base/common/arrays.js';\nimport { findMaxIdx } from '../../../../../base/common/arraysFind.js';\nimport { Codicon } from '../../../../../base/common/codicons.js';\nimport { Disposable, toDisposable } from '../../../../../base/common/lifecycle.js';\nimport { autorun, autorunHandleChanges, autorunWithStore, constObservable, derived, derivedWithStore, observableFromEvent, observableSignalFromEvent, observableValue, recomputeInitiallyAndOnChange } from '../../../../../base/common/observable.js';\nimport { ThemeIcon } from '../../../../../base/common/themables.js';\nimport { PlaceholderViewZone, ViewZoneOverlayWidget, applyStyle, applyViewZones } from '../utils.js';\nimport { OffsetRange, OffsetRangeSet } from '../../../../common/core/offsetRange.js';\nimport { localize } from '../../../../../nls.js';\nexport let MovedBlocksLinesFeature = /*#__PURE__*/(() => {\n  class MovedBlocksLinesFeature extends Disposable {\n    static {\n      this.movedCodeBlockPadding = 4;\n    }\n    constructor(_rootElement, _diffModel, _originalEditorLayoutInfo, _modifiedEditorLayoutInfo, _editors) {\n      super();\n      this._rootElement = _rootElement;\n      this._diffModel = _diffModel;\n      this._originalEditorLayoutInfo = _originalEditorLayoutInfo;\n      this._modifiedEditorLayoutInfo = _modifiedEditorLayoutInfo;\n      this._editors = _editors;\n      this._originalScrollTop = observableFromEvent(this, this._editors.original.onDidScrollChange, () => this._editors.original.getScrollTop());\n      this._modifiedScrollTop = observableFromEvent(this, this._editors.modified.onDidScrollChange, () => this._editors.modified.getScrollTop());\n      this._viewZonesChanged = observableSignalFromEvent('onDidChangeViewZones', this._editors.modified.onDidChangeViewZones);\n      this.width = observableValue(this, 0);\n      this._modifiedViewZonesChangedSignal = observableSignalFromEvent('modified.onDidChangeViewZones', this._editors.modified.onDidChangeViewZones);\n      this._originalViewZonesChangedSignal = observableSignalFromEvent('original.onDidChangeViewZones', this._editors.original.onDidChangeViewZones);\n      this._state = derivedWithStore(this, (reader, store) => {\n        /** @description state */\n        this._element.replaceChildren();\n        const model = this._diffModel.read(reader);\n        const moves = model?.diff.read(reader)?.movedTexts;\n        if (!moves || moves.length === 0) {\n          this.width.set(0, undefined);\n          return;\n        }\n        this._viewZonesChanged.read(reader);\n        const infoOrig = this._originalEditorLayoutInfo.read(reader);\n        const infoMod = this._modifiedEditorLayoutInfo.read(reader);\n        if (!infoOrig || !infoMod) {\n          this.width.set(0, undefined);\n          return;\n        }\n        this._modifiedViewZonesChangedSignal.read(reader);\n        this._originalViewZonesChangedSignal.read(reader);\n        const lines = moves.map(move => {\n          function computeLineStart(range, editor) {\n            const t1 = editor.getTopForLineNumber(range.startLineNumber, true);\n            const t2 = editor.getTopForLineNumber(range.endLineNumberExclusive, true);\n            return (t1 + t2) / 2;\n          }\n          const start = computeLineStart(move.lineRangeMapping.original, this._editors.original);\n          const startOffset = this._originalScrollTop.read(reader);\n          const end = computeLineStart(move.lineRangeMapping.modified, this._editors.modified);\n          const endOffset = this._modifiedScrollTop.read(reader);\n          const from = start - startOffset;\n          const to = end - endOffset;\n          const top = Math.min(start, end);\n          const bottom = Math.max(start, end);\n          return {\n            range: new OffsetRange(top, bottom),\n            from,\n            to,\n            fromWithoutScroll: start,\n            toWithoutScroll: end,\n            move\n          };\n        });\n        lines.sort(tieBreakComparators(compareBy(l => l.fromWithoutScroll > l.toWithoutScroll, booleanComparator), compareBy(l => l.fromWithoutScroll > l.toWithoutScroll ? l.fromWithoutScroll : -l.toWithoutScroll, numberComparator)));\n        const layout = LinesLayout.compute(lines.map(l => l.range));\n        const padding = 10;\n        const lineAreaLeft = infoOrig.verticalScrollbarWidth;\n        const lineAreaWidth = (layout.getTrackCount() - 1) * 10 + padding * 2;\n        const width = lineAreaLeft + lineAreaWidth + (infoMod.contentLeft - MovedBlocksLinesFeature.movedCodeBlockPadding);\n        let idx = 0;\n        for (const line of lines) {\n          const track = layout.getTrack(idx);\n          const verticalY = lineAreaLeft + padding + track * 10;\n          const arrowHeight = 15;\n          const arrowWidth = 15;\n          const right = width;\n          const rectWidth = infoMod.glyphMarginWidth + infoMod.lineNumbersWidth;\n          const rectHeight = 18;\n          const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');\n          rect.classList.add('arrow-rectangle');\n          rect.setAttribute('x', `${right - rectWidth}`);\n          rect.setAttribute('y', `${line.to - rectHeight / 2}`);\n          rect.setAttribute('width', `${rectWidth}`);\n          rect.setAttribute('height', `${rectHeight}`);\n          this._element.appendChild(rect);\n          const g = document.createElementNS('http://www.w3.org/2000/svg', 'g');\n          const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');\n          path.setAttribute('d', `M ${0} ${line.from} L ${verticalY} ${line.from} L ${verticalY} ${line.to} L ${right - arrowWidth} ${line.to}`);\n          path.setAttribute('fill', 'none');\n          g.appendChild(path);\n          const arrowRight = document.createElementNS('http://www.w3.org/2000/svg', 'polygon');\n          arrowRight.classList.add('arrow');\n          store.add(autorun(reader => {\n            path.classList.toggle('currentMove', line.move === model.activeMovedText.read(reader));\n            arrowRight.classList.toggle('currentMove', line.move === model.activeMovedText.read(reader));\n          }));\n          arrowRight.setAttribute('points', `${right - arrowWidth},${line.to - arrowHeight / 2} ${right},${line.to} ${right - arrowWidth},${line.to + arrowHeight / 2}`);\n          g.appendChild(arrowRight);\n          this._element.appendChild(g);\n          /*\n          TODO@hediet\n          path.addEventListener('mouseenter', () => {\n              model.setHoveredMovedText(line.move);\n          });\n          path.addEventListener('mouseleave', () => {\n              model.setHoveredMovedText(undefined);\n          });*/\n          idx++;\n        }\n        this.width.set(lineAreaWidth, undefined);\n      });\n      this._element = document.createElementNS('http://www.w3.org/2000/svg', 'svg');\n      this._element.setAttribute('class', 'moved-blocks-lines');\n      this._rootElement.appendChild(this._element);\n      this._register(toDisposable(() => this._element.remove()));\n      this._register(autorun(reader => {\n        /** @description update moved blocks lines positioning */\n        const info = this._originalEditorLayoutInfo.read(reader);\n        const info2 = this._modifiedEditorLayoutInfo.read(reader);\n        if (!info || !info2) {\n          return;\n        }\n        this._element.style.left = `${info.width - info.verticalScrollbarWidth}px`;\n        this._element.style.height = `${info.height}px`;\n        this._element.style.width = `${info.verticalScrollbarWidth + info.contentLeft - MovedBlocksLinesFeature.movedCodeBlockPadding + this.width.read(reader)}px`;\n      }));\n      this._register(recomputeInitiallyAndOnChange(this._state));\n      const movedBlockViewZones = derived(reader => {\n        const model = this._diffModel.read(reader);\n        const d = model?.diff.read(reader);\n        if (!d) {\n          return [];\n        }\n        return d.movedTexts.map(move => ({\n          move,\n          original: new PlaceholderViewZone(constObservable(move.lineRangeMapping.original.startLineNumber - 1), 18),\n          modified: new PlaceholderViewZone(constObservable(move.lineRangeMapping.modified.startLineNumber - 1), 18)\n        }));\n      });\n      this._register(applyViewZones(this._editors.original, movedBlockViewZones.map(zones => /** @description movedBlockViewZones.original */zones.map(z => z.original))));\n      this._register(applyViewZones(this._editors.modified, movedBlockViewZones.map(zones => /** @description movedBlockViewZones.modified */zones.map(z => z.modified))));\n      this._register(autorunWithStore((reader, store) => {\n        const blocks = movedBlockViewZones.read(reader);\n        for (const b of blocks) {\n          store.add(new MovedBlockOverlayWidget(this._editors.original, b.original, b.move, 'original', this._diffModel.get()));\n          store.add(new MovedBlockOverlayWidget(this._editors.modified, b.modified, b.move, 'modified', this._diffModel.get()));\n        }\n      }));\n      const originalHasFocus = observableSignalFromEvent('original.onDidFocusEditorWidget', e => this._editors.original.onDidFocusEditorWidget(() => setTimeout(() => e(undefined), 0)));\n      const modifiedHasFocus = observableSignalFromEvent('modified.onDidFocusEditorWidget', e => this._editors.modified.onDidFocusEditorWidget(() => setTimeout(() => e(undefined), 0)));\n      let lastChangedEditor = 'modified';\n      this._register(autorunHandleChanges({\n        createEmptyChangeSummary: () => undefined,\n        handleChange: (ctx, summary) => {\n          if (ctx.didChange(originalHasFocus)) {\n            lastChangedEditor = 'original';\n          }\n          if (ctx.didChange(modifiedHasFocus)) {\n            lastChangedEditor = 'modified';\n          }\n          return true;\n        }\n      }, reader => {\n        /** @description MovedBlocksLines.setActiveMovedTextFromCursor */\n        originalHasFocus.read(reader);\n        modifiedHasFocus.read(reader);\n        const m = this._diffModel.read(reader);\n        if (!m) {\n          return;\n        }\n        const diff = m.diff.read(reader);\n        let movedText = undefined;\n        if (diff && lastChangedEditor === 'original') {\n          const originalPos = this._editors.originalCursor.read(reader);\n          if (originalPos) {\n            movedText = diff.movedTexts.find(m => m.lineRangeMapping.original.contains(originalPos.lineNumber));\n          }\n        }\n        if (diff && lastChangedEditor === 'modified') {\n          const modifiedPos = this._editors.modifiedCursor.read(reader);\n          if (modifiedPos) {\n            movedText = diff.movedTexts.find(m => m.lineRangeMapping.modified.contains(modifiedPos.lineNumber));\n          }\n        }\n        if (movedText !== m.movedTextToCompare.get()) {\n          m.movedTextToCompare.set(undefined, undefined);\n        }\n        m.setActiveMovedText(movedText);\n      }));\n    }\n  }\n  return MovedBlocksLinesFeature;\n})();\nclass LinesLayout {\n  static compute(lines) {\n    const setsPerTrack = [];\n    const trackPerLineIdx = [];\n    for (const line of lines) {\n      let trackIdx = setsPerTrack.findIndex(set => !set.intersectsStrict(line));\n      if (trackIdx === -1) {\n        const maxTrackCount = 6;\n        if (setsPerTrack.length >= maxTrackCount) {\n          trackIdx = findMaxIdx(setsPerTrack, compareBy(set => set.intersectWithRangeLength(line), numberComparator));\n        } else {\n          trackIdx = setsPerTrack.length;\n          setsPerTrack.push(new OffsetRangeSet());\n        }\n      }\n      setsPerTrack[trackIdx].addRange(line);\n      trackPerLineIdx.push(trackIdx);\n    }\n    return new LinesLayout(setsPerTrack.length, trackPerLineIdx);\n  }\n  constructor(_trackCount, trackPerLineIdx) {\n    this._trackCount = _trackCount;\n    this.trackPerLineIdx = trackPerLineIdx;\n  }\n  getTrack(lineIdx) {\n    return this.trackPerLineIdx[lineIdx];\n  }\n  getTrackCount() {\n    return this._trackCount;\n  }\n}\nclass MovedBlockOverlayWidget extends ViewZoneOverlayWidget {\n  constructor(_editor, _viewZone, _move, _kind, _diffModel) {\n    const root = h('div.diff-hidden-lines-widget');\n    super(_editor, _viewZone, root.root);\n    this._editor = _editor;\n    this._move = _move;\n    this._kind = _kind;\n    this._diffModel = _diffModel;\n    this._nodes = h('div.diff-moved-code-block', {\n      style: {\n        marginRight: '4px'\n      }\n    }, [h('div.text-content@textContent'), h('div.action-bar@actionBar')]);\n    root.root.appendChild(this._nodes.root);\n    const editorLayout = observableFromEvent(this._editor.onDidLayoutChange, () => this._editor.getLayoutInfo());\n    this._register(applyStyle(this._nodes.root, {\n      paddingRight: editorLayout.map(l => l.verticalScrollbarWidth)\n    }));\n    let text;\n    if (_move.changes.length > 0) {\n      text = this._kind === 'original' ? localize('codeMovedToWithChanges', 'Code moved with changes to line {0}-{1}', this._move.lineRangeMapping.modified.startLineNumber, this._move.lineRangeMapping.modified.endLineNumberExclusive - 1) : localize('codeMovedFromWithChanges', 'Code moved with changes from line {0}-{1}', this._move.lineRangeMapping.original.startLineNumber, this._move.lineRangeMapping.original.endLineNumberExclusive - 1);\n    } else {\n      text = this._kind === 'original' ? localize('codeMovedTo', 'Code moved to line {0}-{1}', this._move.lineRangeMapping.modified.startLineNumber, this._move.lineRangeMapping.modified.endLineNumberExclusive - 1) : localize('codeMovedFrom', 'Code moved from line {0}-{1}', this._move.lineRangeMapping.original.startLineNumber, this._move.lineRangeMapping.original.endLineNumberExclusive - 1);\n    }\n    const actionBar = this._register(new ActionBar(this._nodes.actionBar, {\n      highlightToggledItems: true\n    }));\n    const caption = new Action('', text, '', false);\n    actionBar.push(caption, {\n      icon: false,\n      label: true\n    });\n    const actionCompare = new Action('', 'Compare', ThemeIcon.asClassName(Codicon.compareChanges), true, () => {\n      this._editor.focus();\n      this._diffModel.movedTextToCompare.set(this._diffModel.movedTextToCompare.get() === _move ? undefined : this._move, undefined);\n    });\n    this._register(autorun(reader => {\n      const isActive = this._diffModel.movedTextToCompare.read(reader) === _move;\n      actionCompare.checked = isActive;\n    }));\n    actionBar.push(actionCompare, {\n      icon: false,\n      label: true\n    });\n  }\n}", "map": {"version": 3, "names": ["h", "ActionBar", "Action", "booleanComparator", "compareBy", "numberComparator", "tieBreakComparators", "findMaxIdx", "Codicon", "Disposable", "toDisposable", "autorun", "autorunHandleChanges", "autorunWithStore", "constObservable", "derived", "derivedWithStore", "observableFromEvent", "observableSignalFromEvent", "observableValue", "recomputeInitiallyAndOnChange", "ThemeIcon", "PlaceholderViewZone", "ViewZoneOverlayWidget", "applyStyle", "applyViewZones", "OffsetRange", "OffsetRangeSet", "localize", "MovedBlocksLinesFeature", "movedCodeBlockPadding", "constructor", "_rootElement", "_diffModel", "_originalEditorLayoutInfo", "_modifiedEditorLayoutInfo", "_editors", "_originalScrollTop", "original", "onDidScrollChange", "getScrollTop", "_modifiedScrollTop", "modified", "_viewZonesChanged", "onDidChangeViewZones", "width", "_modifiedViewZonesChangedSignal", "_originalViewZonesChangedSignal", "_state", "reader", "store", "_element", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "model", "read", "moves", "diff", "movedTexts", "length", "set", "undefined", "infoOrig", "infoMod", "lines", "map", "move", "computeLineStart", "range", "editor", "t1", "getTopForLineNumber", "startLineNumber", "t2", "endLineNumberExclusive", "start", "lineRangeMapping", "startOffset", "end", "endOffset", "from", "to", "top", "Math", "min", "bottom", "max", "fromWithoutScroll", "toWithoutScroll", "sort", "l", "layout", "LinesLayout", "compute", "padding", "lineAreaLeft", "verticalScrollbarWidth", "lineAreaWidth", "getTrackCount", "contentLeft", "idx", "line", "track", "getTrack", "verticalY", "arrowHeight", "arrow<PERSON>idth", "right", "rectWidth", "glyph<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lineNumbersWidth", "rectHeight", "rect", "document", "createElementNS", "classList", "add", "setAttribute", "append<PERSON><PERSON><PERSON>", "g", "path", "arrowRight", "toggle", "activeMovedText", "_register", "remove", "info", "info2", "style", "left", "height", "movedBlockViewZones", "d", "zones", "z", "blocks", "b", "MovedBlockOverlayWidget", "get", "originalHasFocus", "e", "onDidFocusEditorWidget", "setTimeout", "modifiedHasFocus", "lastChangedEditor", "createEmptyChangeSummary", "handleChange", "ctx", "summary", "<PERSON><PERSON><PERSON><PERSON>", "m", "movedText", "originalPos", "originalCursor", "find", "contains", "lineNumber", "modifiedPos", "modifiedCursor", "movedTextToCompare", "setActiveMovedText", "setsPerTrack", "trackPerLineIdx", "trackIdx", "findIndex", "intersectsStrict", "maxTrackCount", "intersectWithRangeLength", "push", "addRange", "_trackCount", "lineIdx", "_editor", "_viewZone", "_move", "_kind", "root", "_nodes", "marginRight", "editor<PERSON><PERSON>out", "onDidLayoutChange", "getLayoutInfo", "paddingRight", "text", "changes", "actionBar", "highlightToggledItems", "caption", "icon", "label", "actionCompare", "asClassName", "compareChanges", "focus", "isActive", "checked"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/browser/widget/diffEditor/features/movedBlocksLinesFeature.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { h } from '../../../../../base/browser/dom.js';\nimport { ActionBar } from '../../../../../base/browser/ui/actionbar/actionbar.js';\nimport { Action } from '../../../../../base/common/actions.js';\nimport { booleanComparator, compareBy, numberComparator, tieBreakComparators } from '../../../../../base/common/arrays.js';\nimport { findMaxIdx } from '../../../../../base/common/arraysFind.js';\nimport { Codicon } from '../../../../../base/common/codicons.js';\nimport { Disposable, toDisposable } from '../../../../../base/common/lifecycle.js';\nimport { autorun, autorunHandleChanges, autorunWithStore, constObservable, derived, derivedWithStore, observableFromEvent, observableSignalFromEvent, observableValue, recomputeInitiallyAndOnChange } from '../../../../../base/common/observable.js';\nimport { ThemeIcon } from '../../../../../base/common/themables.js';\nimport { PlaceholderViewZone, ViewZoneOverlayWidget, applyStyle, applyViewZones } from '../utils.js';\nimport { OffsetRange, OffsetRangeSet } from '../../../../common/core/offsetRange.js';\nimport { localize } from '../../../../../nls.js';\nexport class MovedBlocksLinesFeature extends Disposable {\n    static { this.movedCodeBlockPadding = 4; }\n    constructor(_rootElement, _diffModel, _originalEditorLayoutInfo, _modifiedEditorLayoutInfo, _editors) {\n        super();\n        this._rootElement = _rootElement;\n        this._diffModel = _diffModel;\n        this._originalEditorLayoutInfo = _originalEditorLayoutInfo;\n        this._modifiedEditorLayoutInfo = _modifiedEditorLayoutInfo;\n        this._editors = _editors;\n        this._originalScrollTop = observableFromEvent(this, this._editors.original.onDidScrollChange, () => this._editors.original.getScrollTop());\n        this._modifiedScrollTop = observableFromEvent(this, this._editors.modified.onDidScrollChange, () => this._editors.modified.getScrollTop());\n        this._viewZonesChanged = observableSignalFromEvent('onDidChangeViewZones', this._editors.modified.onDidChangeViewZones);\n        this.width = observableValue(this, 0);\n        this._modifiedViewZonesChangedSignal = observableSignalFromEvent('modified.onDidChangeViewZones', this._editors.modified.onDidChangeViewZones);\n        this._originalViewZonesChangedSignal = observableSignalFromEvent('original.onDidChangeViewZones', this._editors.original.onDidChangeViewZones);\n        this._state = derivedWithStore(this, (reader, store) => {\n            /** @description state */\n            this._element.replaceChildren();\n            const model = this._diffModel.read(reader);\n            const moves = model?.diff.read(reader)?.movedTexts;\n            if (!moves || moves.length === 0) {\n                this.width.set(0, undefined);\n                return;\n            }\n            this._viewZonesChanged.read(reader);\n            const infoOrig = this._originalEditorLayoutInfo.read(reader);\n            const infoMod = this._modifiedEditorLayoutInfo.read(reader);\n            if (!infoOrig || !infoMod) {\n                this.width.set(0, undefined);\n                return;\n            }\n            this._modifiedViewZonesChangedSignal.read(reader);\n            this._originalViewZonesChangedSignal.read(reader);\n            const lines = moves.map((move) => {\n                function computeLineStart(range, editor) {\n                    const t1 = editor.getTopForLineNumber(range.startLineNumber, true);\n                    const t2 = editor.getTopForLineNumber(range.endLineNumberExclusive, true);\n                    return (t1 + t2) / 2;\n                }\n                const start = computeLineStart(move.lineRangeMapping.original, this._editors.original);\n                const startOffset = this._originalScrollTop.read(reader);\n                const end = computeLineStart(move.lineRangeMapping.modified, this._editors.modified);\n                const endOffset = this._modifiedScrollTop.read(reader);\n                const from = start - startOffset;\n                const to = end - endOffset;\n                const top = Math.min(start, end);\n                const bottom = Math.max(start, end);\n                return { range: new OffsetRange(top, bottom), from, to, fromWithoutScroll: start, toWithoutScroll: end, move };\n            });\n            lines.sort(tieBreakComparators(compareBy(l => l.fromWithoutScroll > l.toWithoutScroll, booleanComparator), compareBy(l => l.fromWithoutScroll > l.toWithoutScroll ? l.fromWithoutScroll : -l.toWithoutScroll, numberComparator)));\n            const layout = LinesLayout.compute(lines.map(l => l.range));\n            const padding = 10;\n            const lineAreaLeft = infoOrig.verticalScrollbarWidth;\n            const lineAreaWidth = (layout.getTrackCount() - 1) * 10 + padding * 2;\n            const width = lineAreaLeft + lineAreaWidth + (infoMod.contentLeft - MovedBlocksLinesFeature.movedCodeBlockPadding);\n            let idx = 0;\n            for (const line of lines) {\n                const track = layout.getTrack(idx);\n                const verticalY = lineAreaLeft + padding + track * 10;\n                const arrowHeight = 15;\n                const arrowWidth = 15;\n                const right = width;\n                const rectWidth = infoMod.glyphMarginWidth + infoMod.lineNumbersWidth;\n                const rectHeight = 18;\n                const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');\n                rect.classList.add('arrow-rectangle');\n                rect.setAttribute('x', `${right - rectWidth}`);\n                rect.setAttribute('y', `${line.to - rectHeight / 2}`);\n                rect.setAttribute('width', `${rectWidth}`);\n                rect.setAttribute('height', `${rectHeight}`);\n                this._element.appendChild(rect);\n                const g = document.createElementNS('http://www.w3.org/2000/svg', 'g');\n                const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');\n                path.setAttribute('d', `M ${0} ${line.from} L ${verticalY} ${line.from} L ${verticalY} ${line.to} L ${right - arrowWidth} ${line.to}`);\n                path.setAttribute('fill', 'none');\n                g.appendChild(path);\n                const arrowRight = document.createElementNS('http://www.w3.org/2000/svg', 'polygon');\n                arrowRight.classList.add('arrow');\n                store.add(autorun(reader => {\n                    path.classList.toggle('currentMove', line.move === model.activeMovedText.read(reader));\n                    arrowRight.classList.toggle('currentMove', line.move === model.activeMovedText.read(reader));\n                }));\n                arrowRight.setAttribute('points', `${right - arrowWidth},${line.to - arrowHeight / 2} ${right},${line.to} ${right - arrowWidth},${line.to + arrowHeight / 2}`);\n                g.appendChild(arrowRight);\n                this._element.appendChild(g);\n                /*\n                TODO@hediet\n                path.addEventListener('mouseenter', () => {\n                    model.setHoveredMovedText(line.move);\n                });\n                path.addEventListener('mouseleave', () => {\n                    model.setHoveredMovedText(undefined);\n                });*/\n                idx++;\n            }\n            this.width.set(lineAreaWidth, undefined);\n        });\n        this._element = document.createElementNS('http://www.w3.org/2000/svg', 'svg');\n        this._element.setAttribute('class', 'moved-blocks-lines');\n        this._rootElement.appendChild(this._element);\n        this._register(toDisposable(() => this._element.remove()));\n        this._register(autorun(reader => {\n            /** @description update moved blocks lines positioning */\n            const info = this._originalEditorLayoutInfo.read(reader);\n            const info2 = this._modifiedEditorLayoutInfo.read(reader);\n            if (!info || !info2) {\n                return;\n            }\n            this._element.style.left = `${info.width - info.verticalScrollbarWidth}px`;\n            this._element.style.height = `${info.height}px`;\n            this._element.style.width = `${info.verticalScrollbarWidth + info.contentLeft - MovedBlocksLinesFeature.movedCodeBlockPadding + this.width.read(reader)}px`;\n        }));\n        this._register(recomputeInitiallyAndOnChange(this._state));\n        const movedBlockViewZones = derived(reader => {\n            const model = this._diffModel.read(reader);\n            const d = model?.diff.read(reader);\n            if (!d) {\n                return [];\n            }\n            return d.movedTexts.map(move => ({\n                move,\n                original: new PlaceholderViewZone(constObservable(move.lineRangeMapping.original.startLineNumber - 1), 18),\n                modified: new PlaceholderViewZone(constObservable(move.lineRangeMapping.modified.startLineNumber - 1), 18),\n            }));\n        });\n        this._register(applyViewZones(this._editors.original, movedBlockViewZones.map(zones => /** @description movedBlockViewZones.original */ zones.map(z => z.original))));\n        this._register(applyViewZones(this._editors.modified, movedBlockViewZones.map(zones => /** @description movedBlockViewZones.modified */ zones.map(z => z.modified))));\n        this._register(autorunWithStore((reader, store) => {\n            const blocks = movedBlockViewZones.read(reader);\n            for (const b of blocks) {\n                store.add(new MovedBlockOverlayWidget(this._editors.original, b.original, b.move, 'original', this._diffModel.get()));\n                store.add(new MovedBlockOverlayWidget(this._editors.modified, b.modified, b.move, 'modified', this._diffModel.get()));\n            }\n        }));\n        const originalHasFocus = observableSignalFromEvent('original.onDidFocusEditorWidget', e => this._editors.original.onDidFocusEditorWidget(() => setTimeout(() => e(undefined), 0)));\n        const modifiedHasFocus = observableSignalFromEvent('modified.onDidFocusEditorWidget', e => this._editors.modified.onDidFocusEditorWidget(() => setTimeout(() => e(undefined), 0)));\n        let lastChangedEditor = 'modified';\n        this._register(autorunHandleChanges({\n            createEmptyChangeSummary: () => undefined,\n            handleChange: (ctx, summary) => {\n                if (ctx.didChange(originalHasFocus)) {\n                    lastChangedEditor = 'original';\n                }\n                if (ctx.didChange(modifiedHasFocus)) {\n                    lastChangedEditor = 'modified';\n                }\n                return true;\n            }\n        }, reader => {\n            /** @description MovedBlocksLines.setActiveMovedTextFromCursor */\n            originalHasFocus.read(reader);\n            modifiedHasFocus.read(reader);\n            const m = this._diffModel.read(reader);\n            if (!m) {\n                return;\n            }\n            const diff = m.diff.read(reader);\n            let movedText = undefined;\n            if (diff && lastChangedEditor === 'original') {\n                const originalPos = this._editors.originalCursor.read(reader);\n                if (originalPos) {\n                    movedText = diff.movedTexts.find(m => m.lineRangeMapping.original.contains(originalPos.lineNumber));\n                }\n            }\n            if (diff && lastChangedEditor === 'modified') {\n                const modifiedPos = this._editors.modifiedCursor.read(reader);\n                if (modifiedPos) {\n                    movedText = diff.movedTexts.find(m => m.lineRangeMapping.modified.contains(modifiedPos.lineNumber));\n                }\n            }\n            if (movedText !== m.movedTextToCompare.get()) {\n                m.movedTextToCompare.set(undefined, undefined);\n            }\n            m.setActiveMovedText(movedText);\n        }));\n    }\n}\nclass LinesLayout {\n    static compute(lines) {\n        const setsPerTrack = [];\n        const trackPerLineIdx = [];\n        for (const line of lines) {\n            let trackIdx = setsPerTrack.findIndex(set => !set.intersectsStrict(line));\n            if (trackIdx === -1) {\n                const maxTrackCount = 6;\n                if (setsPerTrack.length >= maxTrackCount) {\n                    trackIdx = findMaxIdx(setsPerTrack, compareBy(set => set.intersectWithRangeLength(line), numberComparator));\n                }\n                else {\n                    trackIdx = setsPerTrack.length;\n                    setsPerTrack.push(new OffsetRangeSet());\n                }\n            }\n            setsPerTrack[trackIdx].addRange(line);\n            trackPerLineIdx.push(trackIdx);\n        }\n        return new LinesLayout(setsPerTrack.length, trackPerLineIdx);\n    }\n    constructor(_trackCount, trackPerLineIdx) {\n        this._trackCount = _trackCount;\n        this.trackPerLineIdx = trackPerLineIdx;\n    }\n    getTrack(lineIdx) {\n        return this.trackPerLineIdx[lineIdx];\n    }\n    getTrackCount() {\n        return this._trackCount;\n    }\n}\nclass MovedBlockOverlayWidget extends ViewZoneOverlayWidget {\n    constructor(_editor, _viewZone, _move, _kind, _diffModel) {\n        const root = h('div.diff-hidden-lines-widget');\n        super(_editor, _viewZone, root.root);\n        this._editor = _editor;\n        this._move = _move;\n        this._kind = _kind;\n        this._diffModel = _diffModel;\n        this._nodes = h('div.diff-moved-code-block', { style: { marginRight: '4px' } }, [\n            h('div.text-content@textContent'),\n            h('div.action-bar@actionBar'),\n        ]);\n        root.root.appendChild(this._nodes.root);\n        const editorLayout = observableFromEvent(this._editor.onDidLayoutChange, () => this._editor.getLayoutInfo());\n        this._register(applyStyle(this._nodes.root, {\n            paddingRight: editorLayout.map(l => l.verticalScrollbarWidth)\n        }));\n        let text;\n        if (_move.changes.length > 0) {\n            text = this._kind === 'original' ? localize('codeMovedToWithChanges', 'Code moved with changes to line {0}-{1}', this._move.lineRangeMapping.modified.startLineNumber, this._move.lineRangeMapping.modified.endLineNumberExclusive - 1) : localize('codeMovedFromWithChanges', 'Code moved with changes from line {0}-{1}', this._move.lineRangeMapping.original.startLineNumber, this._move.lineRangeMapping.original.endLineNumberExclusive - 1);\n        }\n        else {\n            text = this._kind === 'original' ? localize('codeMovedTo', 'Code moved to line {0}-{1}', this._move.lineRangeMapping.modified.startLineNumber, this._move.lineRangeMapping.modified.endLineNumberExclusive - 1) : localize('codeMovedFrom', 'Code moved from line {0}-{1}', this._move.lineRangeMapping.original.startLineNumber, this._move.lineRangeMapping.original.endLineNumberExclusive - 1);\n        }\n        const actionBar = this._register(new ActionBar(this._nodes.actionBar, {\n            highlightToggledItems: true,\n        }));\n        const caption = new Action('', text, '', false);\n        actionBar.push(caption, { icon: false, label: true });\n        const actionCompare = new Action('', 'Compare', ThemeIcon.asClassName(Codicon.compareChanges), true, () => {\n            this._editor.focus();\n            this._diffModel.movedTextToCompare.set(this._diffModel.movedTextToCompare.get() === _move ? undefined : this._move, undefined);\n        });\n        this._register(autorun(reader => {\n            const isActive = this._diffModel.movedTextToCompare.read(reader) === _move;\n            actionCompare.checked = isActive;\n        }));\n        actionBar.push(actionCompare, { icon: false, label: true });\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,CAAC,QAAQ,oCAAoC;AACtD,SAASC,SAAS,QAAQ,uDAAuD;AACjF,SAASC,MAAM,QAAQ,uCAAuC;AAC9D,SAASC,iBAAiB,EAAEC,SAAS,EAAEC,gBAAgB,EAAEC,mBAAmB,QAAQ,sCAAsC;AAC1H,SAASC,UAAU,QAAQ,0CAA0C;AACrE,SAASC,OAAO,QAAQ,wCAAwC;AAChE,SAASC,UAAU,EAAEC,YAAY,QAAQ,yCAAyC;AAClF,SAASC,OAAO,EAAEC,oBAAoB,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,OAAO,EAAEC,gBAAgB,EAAEC,mBAAmB,EAAEC,yBAAyB,EAAEC,eAAe,EAAEC,6BAA6B,QAAQ,0CAA0C;AACtP,SAASC,SAAS,QAAQ,yCAAyC;AACnE,SAASC,mBAAmB,EAAEC,qBAAqB,EAAEC,UAAU,EAAEC,cAAc,QAAQ,aAAa;AACpG,SAASC,WAAW,EAAEC,cAAc,QAAQ,wCAAwC;AACpF,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,WAAaC,uBAAuB;EAA7B,MAAMA,uBAAuB,SAASpB,UAAU,CAAC;IACpD;MAAS,IAAI,CAACqB,qBAAqB,GAAG,CAAC;IAAE;IACzCC,WAAWA,CAACC,YAAY,EAAEC,UAAU,EAAEC,yBAAyB,EAAEC,yBAAyB,EAAEC,QAAQ,EAAE;MAClG,KAAK,CAAC,CAAC;MACP,IAAI,CAACJ,YAAY,GAAGA,YAAY;MAChC,IAAI,CAACC,UAAU,GAAGA,UAAU;MAC5B,IAAI,CAACC,yBAAyB,GAAGA,yBAAyB;MAC1D,IAAI,CAACC,yBAAyB,GAAGA,yBAAyB;MAC1D,IAAI,CAACC,QAAQ,GAAGA,QAAQ;MACxB,IAAI,CAACC,kBAAkB,GAAGpB,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAACmB,QAAQ,CAACE,QAAQ,CAACC,iBAAiB,EAAE,MAAM,IAAI,CAACH,QAAQ,CAACE,QAAQ,CAACE,YAAY,CAAC,CAAC,CAAC;MAC1I,IAAI,CAACC,kBAAkB,GAAGxB,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAACmB,QAAQ,CAACM,QAAQ,CAACH,iBAAiB,EAAE,MAAM,IAAI,CAACH,QAAQ,CAACM,QAAQ,CAACF,YAAY,CAAC,CAAC,CAAC;MAC1I,IAAI,CAACG,iBAAiB,GAAGzB,yBAAyB,CAAC,sBAAsB,EAAE,IAAI,CAACkB,QAAQ,CAACM,QAAQ,CAACE,oBAAoB,CAAC;MACvH,IAAI,CAACC,KAAK,GAAG1B,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC;MACrC,IAAI,CAAC2B,+BAA+B,GAAG5B,yBAAyB,CAAC,+BAA+B,EAAE,IAAI,CAACkB,QAAQ,CAACM,QAAQ,CAACE,oBAAoB,CAAC;MAC9I,IAAI,CAACG,+BAA+B,GAAG7B,yBAAyB,CAAC,+BAA+B,EAAE,IAAI,CAACkB,QAAQ,CAACE,QAAQ,CAACM,oBAAoB,CAAC;MAC9I,IAAI,CAACI,MAAM,GAAGhC,gBAAgB,CAAC,IAAI,EAAE,CAACiC,MAAM,EAAEC,KAAK,KAAK;QACpD;QACA,IAAI,CAACC,QAAQ,CAACC,eAAe,CAAC,CAAC;QAC/B,MAAMC,KAAK,GAAG,IAAI,CAACpB,UAAU,CAACqB,IAAI,CAACL,MAAM,CAAC;QAC1C,MAAMM,KAAK,GAAGF,KAAK,EAAEG,IAAI,CAACF,IAAI,CAACL,MAAM,CAAC,EAAEQ,UAAU;QAClD,IAAI,CAACF,KAAK,IAAIA,KAAK,CAACG,MAAM,KAAK,CAAC,EAAE;UAC9B,IAAI,CAACb,KAAK,CAACc,GAAG,CAAC,CAAC,EAAEC,SAAS,CAAC;UAC5B;QACJ;QACA,IAAI,CAACjB,iBAAiB,CAACW,IAAI,CAACL,MAAM,CAAC;QACnC,MAAMY,QAAQ,GAAG,IAAI,CAAC3B,yBAAyB,CAACoB,IAAI,CAACL,MAAM,CAAC;QAC5D,MAAMa,OAAO,GAAG,IAAI,CAAC3B,yBAAyB,CAACmB,IAAI,CAACL,MAAM,CAAC;QAC3D,IAAI,CAACY,QAAQ,IAAI,CAACC,OAAO,EAAE;UACvB,IAAI,CAACjB,KAAK,CAACc,GAAG,CAAC,CAAC,EAAEC,SAAS,CAAC;UAC5B;QACJ;QACA,IAAI,CAACd,+BAA+B,CAACQ,IAAI,CAACL,MAAM,CAAC;QACjD,IAAI,CAACF,+BAA+B,CAACO,IAAI,CAACL,MAAM,CAAC;QACjD,MAAMc,KAAK,GAAGR,KAAK,CAACS,GAAG,CAAEC,IAAI,IAAK;UAC9B,SAASC,gBAAgBA,CAACC,KAAK,EAAEC,MAAM,EAAE;YACrC,MAAMC,EAAE,GAAGD,MAAM,CAACE,mBAAmB,CAACH,KAAK,CAACI,eAAe,EAAE,IAAI,CAAC;YAClE,MAAMC,EAAE,GAAGJ,MAAM,CAACE,mBAAmB,CAACH,KAAK,CAACM,sBAAsB,EAAE,IAAI,CAAC;YACzE,OAAO,CAACJ,EAAE,GAAGG,EAAE,IAAI,CAAC;UACxB;UACA,MAAME,KAAK,GAAGR,gBAAgB,CAACD,IAAI,CAACU,gBAAgB,CAACrC,QAAQ,EAAE,IAAI,CAACF,QAAQ,CAACE,QAAQ,CAAC;UACtF,MAAMsC,WAAW,GAAG,IAAI,CAACvC,kBAAkB,CAACiB,IAAI,CAACL,MAAM,CAAC;UACxD,MAAM4B,GAAG,GAAGX,gBAAgB,CAACD,IAAI,CAACU,gBAAgB,CAACjC,QAAQ,EAAE,IAAI,CAACN,QAAQ,CAACM,QAAQ,CAAC;UACpF,MAAMoC,SAAS,GAAG,IAAI,CAACrC,kBAAkB,CAACa,IAAI,CAACL,MAAM,CAAC;UACtD,MAAM8B,IAAI,GAAGL,KAAK,GAAGE,WAAW;UAChC,MAAMI,EAAE,GAAGH,GAAG,GAAGC,SAAS;UAC1B,MAAMG,GAAG,GAAGC,IAAI,CAACC,GAAG,CAACT,KAAK,EAAEG,GAAG,CAAC;UAChC,MAAMO,MAAM,GAAGF,IAAI,CAACG,GAAG,CAACX,KAAK,EAAEG,GAAG,CAAC;UACnC,OAAO;YAAEV,KAAK,EAAE,IAAIzC,WAAW,CAACuD,GAAG,EAAEG,MAAM,CAAC;YAAEL,IAAI;YAAEC,EAAE;YAAEM,iBAAiB,EAAEZ,KAAK;YAAEa,eAAe,EAAEV,GAAG;YAAEZ;UAAK,CAAC;QAClH,CAAC,CAAC;QACFF,KAAK,CAACyB,IAAI,CAAClF,mBAAmB,CAACF,SAAS,CAACqF,CAAC,IAAIA,CAAC,CAACH,iBAAiB,GAAGG,CAAC,CAACF,eAAe,EAAEpF,iBAAiB,CAAC,EAAEC,SAAS,CAACqF,CAAC,IAAIA,CAAC,CAACH,iBAAiB,GAAGG,CAAC,CAACF,eAAe,GAAGE,CAAC,CAACH,iBAAiB,GAAG,CAACG,CAAC,CAACF,eAAe,EAAElF,gBAAgB,CAAC,CAAC,CAAC;QACjO,MAAMqF,MAAM,GAAGC,WAAW,CAACC,OAAO,CAAC7B,KAAK,CAACC,GAAG,CAACyB,CAAC,IAAIA,CAAC,CAACtB,KAAK,CAAC,CAAC;QAC3D,MAAM0B,OAAO,GAAG,EAAE;QAClB,MAAMC,YAAY,GAAGjC,QAAQ,CAACkC,sBAAsB;QACpD,MAAMC,aAAa,GAAG,CAACN,MAAM,CAACO,aAAa,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,GAAGJ,OAAO,GAAG,CAAC;QACrE,MAAMhD,KAAK,GAAGiD,YAAY,GAAGE,aAAa,IAAIlC,OAAO,CAACoC,WAAW,GAAGrE,uBAAuB,CAACC,qBAAqB,CAAC;QAClH,IAAIqE,GAAG,GAAG,CAAC;QACX,KAAK,MAAMC,IAAI,IAAIrC,KAAK,EAAE;UACtB,MAAMsC,KAAK,GAAGX,MAAM,CAACY,QAAQ,CAACH,GAAG,CAAC;UAClC,MAAMI,SAAS,GAAGT,YAAY,GAAGD,OAAO,GAAGQ,KAAK,GAAG,EAAE;UACrD,MAAMG,WAAW,GAAG,EAAE;UACtB,MAAMC,UAAU,GAAG,EAAE;UACrB,MAAMC,KAAK,GAAG7D,KAAK;UACnB,MAAM8D,SAAS,GAAG7C,OAAO,CAAC8C,gBAAgB,GAAG9C,OAAO,CAAC+C,gBAAgB;UACrE,MAAMC,UAAU,GAAG,EAAE;UACrB,MAAMC,IAAI,GAAGC,QAAQ,CAACC,eAAe,CAAC,4BAA4B,EAAE,MAAM,CAAC;UAC3EF,IAAI,CAACG,SAAS,CAACC,GAAG,CAAC,iBAAiB,CAAC;UACrCJ,IAAI,CAACK,YAAY,CAAC,GAAG,EAAE,GAAGV,KAAK,GAAGC,SAAS,EAAE,CAAC;UAC9CI,IAAI,CAACK,YAAY,CAAC,GAAG,EAAE,GAAGhB,IAAI,CAACpB,EAAE,GAAG8B,UAAU,GAAG,CAAC,EAAE,CAAC;UACrDC,IAAI,CAACK,YAAY,CAAC,OAAO,EAAE,GAAGT,SAAS,EAAE,CAAC;UAC1CI,IAAI,CAACK,YAAY,CAAC,QAAQ,EAAE,GAAGN,UAAU,EAAE,CAAC;UAC5C,IAAI,CAAC3D,QAAQ,CAACkE,WAAW,CAACN,IAAI,CAAC;UAC/B,MAAMO,CAAC,GAAGN,QAAQ,CAACC,eAAe,CAAC,4BAA4B,EAAE,GAAG,CAAC;UACrE,MAAMM,IAAI,GAAGP,QAAQ,CAACC,eAAe,CAAC,4BAA4B,EAAE,MAAM,CAAC;UAC3EM,IAAI,CAACH,YAAY,CAAC,GAAG,EAAE,KAAK,CAAC,IAAIhB,IAAI,CAACrB,IAAI,MAAMwB,SAAS,IAAIH,IAAI,CAACrB,IAAI,MAAMwB,SAAS,IAAIH,IAAI,CAACpB,EAAE,MAAM0B,KAAK,GAAGD,UAAU,IAAIL,IAAI,CAACpB,EAAE,EAAE,CAAC;UACtIuC,IAAI,CAACH,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC;UACjCE,CAAC,CAACD,WAAW,CAACE,IAAI,CAAC;UACnB,MAAMC,UAAU,GAAGR,QAAQ,CAACC,eAAe,CAAC,4BAA4B,EAAE,SAAS,CAAC;UACpFO,UAAU,CAACN,SAAS,CAACC,GAAG,CAAC,OAAO,CAAC;UACjCjE,KAAK,CAACiE,GAAG,CAACxG,OAAO,CAACsC,MAAM,IAAI;YACxBsE,IAAI,CAACL,SAAS,CAACO,MAAM,CAAC,aAAa,EAAErB,IAAI,CAACnC,IAAI,KAAKZ,KAAK,CAACqE,eAAe,CAACpE,IAAI,CAACL,MAAM,CAAC,CAAC;YACtFuE,UAAU,CAACN,SAAS,CAACO,MAAM,CAAC,aAAa,EAAErB,IAAI,CAACnC,IAAI,KAAKZ,KAAK,CAACqE,eAAe,CAACpE,IAAI,CAACL,MAAM,CAAC,CAAC;UAChG,CAAC,CAAC,CAAC;UACHuE,UAAU,CAACJ,YAAY,CAAC,QAAQ,EAAE,GAAGV,KAAK,GAAGD,UAAU,IAAIL,IAAI,CAACpB,EAAE,GAAGwB,WAAW,GAAG,CAAC,IAAIE,KAAK,IAAIN,IAAI,CAACpB,EAAE,IAAI0B,KAAK,GAAGD,UAAU,IAAIL,IAAI,CAACpB,EAAE,GAAGwB,WAAW,GAAG,CAAC,EAAE,CAAC;UAC9Jc,CAAC,CAACD,WAAW,CAACG,UAAU,CAAC;UACzB,IAAI,CAACrE,QAAQ,CAACkE,WAAW,CAACC,CAAC,CAAC;UAC5B;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;UACgBnB,GAAG,EAAE;QACT;QACA,IAAI,CAACtD,KAAK,CAACc,GAAG,CAACqC,aAAa,EAAEpC,SAAS,CAAC;MAC5C,CAAC,CAAC;MACF,IAAI,CAACT,QAAQ,GAAG6D,QAAQ,CAACC,eAAe,CAAC,4BAA4B,EAAE,KAAK,CAAC;MAC7E,IAAI,CAAC9D,QAAQ,CAACiE,YAAY,CAAC,OAAO,EAAE,oBAAoB,CAAC;MACzD,IAAI,CAACpF,YAAY,CAACqF,WAAW,CAAC,IAAI,CAAClE,QAAQ,CAAC;MAC5C,IAAI,CAACwE,SAAS,CAACjH,YAAY,CAAC,MAAM,IAAI,CAACyC,QAAQ,CAACyE,MAAM,CAAC,CAAC,CAAC,CAAC;MAC1D,IAAI,CAACD,SAAS,CAAChH,OAAO,CAACsC,MAAM,IAAI;QAC7B;QACA,MAAM4E,IAAI,GAAG,IAAI,CAAC3F,yBAAyB,CAACoB,IAAI,CAACL,MAAM,CAAC;QACxD,MAAM6E,KAAK,GAAG,IAAI,CAAC3F,yBAAyB,CAACmB,IAAI,CAACL,MAAM,CAAC;QACzD,IAAI,CAAC4E,IAAI,IAAI,CAACC,KAAK,EAAE;UACjB;QACJ;QACA,IAAI,CAAC3E,QAAQ,CAAC4E,KAAK,CAACC,IAAI,GAAG,GAAGH,IAAI,CAAChF,KAAK,GAAGgF,IAAI,CAAC9B,sBAAsB,IAAI;QAC1E,IAAI,CAAC5C,QAAQ,CAAC4E,KAAK,CAACE,MAAM,GAAG,GAAGJ,IAAI,CAACI,MAAM,IAAI;QAC/C,IAAI,CAAC9E,QAAQ,CAAC4E,KAAK,CAAClF,KAAK,GAAG,GAAGgF,IAAI,CAAC9B,sBAAsB,GAAG8B,IAAI,CAAC3B,WAAW,GAAGrE,uBAAuB,CAACC,qBAAqB,GAAG,IAAI,CAACe,KAAK,CAACS,IAAI,CAACL,MAAM,CAAC,IAAI;MAC/J,CAAC,CAAC,CAAC;MACH,IAAI,CAAC0E,SAAS,CAACvG,6BAA6B,CAAC,IAAI,CAAC4B,MAAM,CAAC,CAAC;MAC1D,MAAMkF,mBAAmB,GAAGnH,OAAO,CAACkC,MAAM,IAAI;QAC1C,MAAMI,KAAK,GAAG,IAAI,CAACpB,UAAU,CAACqB,IAAI,CAACL,MAAM,CAAC;QAC1C,MAAMkF,CAAC,GAAG9E,KAAK,EAAEG,IAAI,CAACF,IAAI,CAACL,MAAM,CAAC;QAClC,IAAI,CAACkF,CAAC,EAAE;UACJ,OAAO,EAAE;QACb;QACA,OAAOA,CAAC,CAAC1E,UAAU,CAACO,GAAG,CAACC,IAAI,KAAK;UAC7BA,IAAI;UACJ3B,QAAQ,EAAE,IAAIhB,mBAAmB,CAACR,eAAe,CAACmD,IAAI,CAACU,gBAAgB,CAACrC,QAAQ,CAACiC,eAAe,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC;UAC1G7B,QAAQ,EAAE,IAAIpB,mBAAmB,CAACR,eAAe,CAACmD,IAAI,CAACU,gBAAgB,CAACjC,QAAQ,CAAC6B,eAAe,GAAG,CAAC,CAAC,EAAE,EAAE;QAC7G,CAAC,CAAC,CAAC;MACP,CAAC,CAAC;MACF,IAAI,CAACoD,SAAS,CAAClG,cAAc,CAAC,IAAI,CAACW,QAAQ,CAACE,QAAQ,EAAE4F,mBAAmB,CAAClE,GAAG,CAACoE,KAAK,IAAI,gDAAiDA,KAAK,CAACpE,GAAG,CAACqE,CAAC,IAAIA,CAAC,CAAC/F,QAAQ,CAAC,CAAC,CAAC,CAAC;MACrK,IAAI,CAACqF,SAAS,CAAClG,cAAc,CAAC,IAAI,CAACW,QAAQ,CAACM,QAAQ,EAAEwF,mBAAmB,CAAClE,GAAG,CAACoE,KAAK,IAAI,gDAAiDA,KAAK,CAACpE,GAAG,CAACqE,CAAC,IAAIA,CAAC,CAAC3F,QAAQ,CAAC,CAAC,CAAC,CAAC;MACrK,IAAI,CAACiF,SAAS,CAAC9G,gBAAgB,CAAC,CAACoC,MAAM,EAAEC,KAAK,KAAK;QAC/C,MAAMoF,MAAM,GAAGJ,mBAAmB,CAAC5E,IAAI,CAACL,MAAM,CAAC;QAC/C,KAAK,MAAMsF,CAAC,IAAID,MAAM,EAAE;UACpBpF,KAAK,CAACiE,GAAG,CAAC,IAAIqB,uBAAuB,CAAC,IAAI,CAACpG,QAAQ,CAACE,QAAQ,EAAEiG,CAAC,CAACjG,QAAQ,EAAEiG,CAAC,CAACtE,IAAI,EAAE,UAAU,EAAE,IAAI,CAAChC,UAAU,CAACwG,GAAG,CAAC,CAAC,CAAC,CAAC;UACrHvF,KAAK,CAACiE,GAAG,CAAC,IAAIqB,uBAAuB,CAAC,IAAI,CAACpG,QAAQ,CAACM,QAAQ,EAAE6F,CAAC,CAAC7F,QAAQ,EAAE6F,CAAC,CAACtE,IAAI,EAAE,UAAU,EAAE,IAAI,CAAChC,UAAU,CAACwG,GAAG,CAAC,CAAC,CAAC,CAAC;QACzH;MACJ,CAAC,CAAC,CAAC;MACH,MAAMC,gBAAgB,GAAGxH,yBAAyB,CAAC,iCAAiC,EAAEyH,CAAC,IAAI,IAAI,CAACvG,QAAQ,CAACE,QAAQ,CAACsG,sBAAsB,CAAC,MAAMC,UAAU,CAAC,MAAMF,CAAC,CAAC/E,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAClL,MAAMkF,gBAAgB,GAAG5H,yBAAyB,CAAC,iCAAiC,EAAEyH,CAAC,IAAI,IAAI,CAACvG,QAAQ,CAACM,QAAQ,CAACkG,sBAAsB,CAAC,MAAMC,UAAU,CAAC,MAAMF,CAAC,CAAC/E,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAClL,IAAImF,iBAAiB,GAAG,UAAU;MAClC,IAAI,CAACpB,SAAS,CAAC/G,oBAAoB,CAAC;QAChCoI,wBAAwB,EAAEA,CAAA,KAAMpF,SAAS;QACzCqF,YAAY,EAAEA,CAACC,GAAG,EAAEC,OAAO,KAAK;UAC5B,IAAID,GAAG,CAACE,SAAS,CAACV,gBAAgB,CAAC,EAAE;YACjCK,iBAAiB,GAAG,UAAU;UAClC;UACA,IAAIG,GAAG,CAACE,SAAS,CAACN,gBAAgB,CAAC,EAAE;YACjCC,iBAAiB,GAAG,UAAU;UAClC;UACA,OAAO,IAAI;QACf;MACJ,CAAC,EAAE9F,MAAM,IAAI;QACT;QACAyF,gBAAgB,CAACpF,IAAI,CAACL,MAAM,CAAC;QAC7B6F,gBAAgB,CAACxF,IAAI,CAACL,MAAM,CAAC;QAC7B,MAAMoG,CAAC,GAAG,IAAI,CAACpH,UAAU,CAACqB,IAAI,CAACL,MAAM,CAAC;QACtC,IAAI,CAACoG,CAAC,EAAE;UACJ;QACJ;QACA,MAAM7F,IAAI,GAAG6F,CAAC,CAAC7F,IAAI,CAACF,IAAI,CAACL,MAAM,CAAC;QAChC,IAAIqG,SAAS,GAAG1F,SAAS;QACzB,IAAIJ,IAAI,IAAIuF,iBAAiB,KAAK,UAAU,EAAE;UAC1C,MAAMQ,WAAW,GAAG,IAAI,CAACnH,QAAQ,CAACoH,cAAc,CAAClG,IAAI,CAACL,MAAM,CAAC;UAC7D,IAAIsG,WAAW,EAAE;YACbD,SAAS,GAAG9F,IAAI,CAACC,UAAU,CAACgG,IAAI,CAACJ,CAAC,IAAIA,CAAC,CAAC1E,gBAAgB,CAACrC,QAAQ,CAACoH,QAAQ,CAACH,WAAW,CAACI,UAAU,CAAC,CAAC;UACvG;QACJ;QACA,IAAInG,IAAI,IAAIuF,iBAAiB,KAAK,UAAU,EAAE;UAC1C,MAAMa,WAAW,GAAG,IAAI,CAACxH,QAAQ,CAACyH,cAAc,CAACvG,IAAI,CAACL,MAAM,CAAC;UAC7D,IAAI2G,WAAW,EAAE;YACbN,SAAS,GAAG9F,IAAI,CAACC,UAAU,CAACgG,IAAI,CAACJ,CAAC,IAAIA,CAAC,CAAC1E,gBAAgB,CAACjC,QAAQ,CAACgH,QAAQ,CAACE,WAAW,CAACD,UAAU,CAAC,CAAC;UACvG;QACJ;QACA,IAAIL,SAAS,KAAKD,CAAC,CAACS,kBAAkB,CAACrB,GAAG,CAAC,CAAC,EAAE;UAC1CY,CAAC,CAACS,kBAAkB,CAACnG,GAAG,CAACC,SAAS,EAAEA,SAAS,CAAC;QAClD;QACAyF,CAAC,CAACU,kBAAkB,CAACT,SAAS,CAAC;MACnC,CAAC,CAAC,CAAC;IACP;EACJ;EAAC,OAhLYzH,uBAAuB;AAAA;AAiLpC,MAAM8D,WAAW,CAAC;EACd,OAAOC,OAAOA,CAAC7B,KAAK,EAAE;IAClB,MAAMiG,YAAY,GAAG,EAAE;IACvB,MAAMC,eAAe,GAAG,EAAE;IAC1B,KAAK,MAAM7D,IAAI,IAAIrC,KAAK,EAAE;MACtB,IAAImG,QAAQ,GAAGF,YAAY,CAACG,SAAS,CAACxG,GAAG,IAAI,CAACA,GAAG,CAACyG,gBAAgB,CAAChE,IAAI,CAAC,CAAC;MACzE,IAAI8D,QAAQ,KAAK,CAAC,CAAC,EAAE;QACjB,MAAMG,aAAa,GAAG,CAAC;QACvB,IAAIL,YAAY,CAACtG,MAAM,IAAI2G,aAAa,EAAE;UACtCH,QAAQ,GAAG3J,UAAU,CAACyJ,YAAY,EAAE5J,SAAS,CAACuD,GAAG,IAAIA,GAAG,CAAC2G,wBAAwB,CAAClE,IAAI,CAAC,EAAE/F,gBAAgB,CAAC,CAAC;QAC/G,CAAC,MACI;UACD6J,QAAQ,GAAGF,YAAY,CAACtG,MAAM;UAC9BsG,YAAY,CAACO,IAAI,CAAC,IAAI5I,cAAc,CAAC,CAAC,CAAC;QAC3C;MACJ;MACAqI,YAAY,CAACE,QAAQ,CAAC,CAACM,QAAQ,CAACpE,IAAI,CAAC;MACrC6D,eAAe,CAACM,IAAI,CAACL,QAAQ,CAAC;IAClC;IACA,OAAO,IAAIvE,WAAW,CAACqE,YAAY,CAACtG,MAAM,EAAEuG,eAAe,CAAC;EAChE;EACAlI,WAAWA,CAAC0I,WAAW,EAAER,eAAe,EAAE;IACtC,IAAI,CAACQ,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACR,eAAe,GAAGA,eAAe;EAC1C;EACA3D,QAAQA,CAACoE,OAAO,EAAE;IACd,OAAO,IAAI,CAACT,eAAe,CAACS,OAAO,CAAC;EACxC;EACAzE,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACwE,WAAW;EAC3B;AACJ;AACA,MAAMjC,uBAAuB,SAASjH,qBAAqB,CAAC;EACxDQ,WAAWA,CAAC4I,OAAO,EAAEC,SAAS,EAAEC,KAAK,EAAEC,KAAK,EAAE7I,UAAU,EAAE;IACtD,MAAM8I,IAAI,GAAG/K,CAAC,CAAC,8BAA8B,CAAC;IAC9C,KAAK,CAAC2K,OAAO,EAAEC,SAAS,EAAEG,IAAI,CAACA,IAAI,CAAC;IACpC,IAAI,CAACJ,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACE,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC7I,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAAC+I,MAAM,GAAGhL,CAAC,CAAC,2BAA2B,EAAE;MAAE+H,KAAK,EAAE;QAAEkD,WAAW,EAAE;MAAM;IAAE,CAAC,EAAE,CAC5EjL,CAAC,CAAC,8BAA8B,CAAC,EACjCA,CAAC,CAAC,0BAA0B,CAAC,CAChC,CAAC;IACF+K,IAAI,CAACA,IAAI,CAAC1D,WAAW,CAAC,IAAI,CAAC2D,MAAM,CAACD,IAAI,CAAC;IACvC,MAAMG,YAAY,GAAGjK,mBAAmB,CAAC,IAAI,CAAC0J,OAAO,CAACQ,iBAAiB,EAAE,MAAM,IAAI,CAACR,OAAO,CAACS,aAAa,CAAC,CAAC,CAAC;IAC5G,IAAI,CAACzD,SAAS,CAACnG,UAAU,CAAC,IAAI,CAACwJ,MAAM,CAACD,IAAI,EAAE;MACxCM,YAAY,EAAEH,YAAY,CAAClH,GAAG,CAACyB,CAAC,IAAIA,CAAC,CAACM,sBAAsB;IAChE,CAAC,CAAC,CAAC;IACH,IAAIuF,IAAI;IACR,IAAIT,KAAK,CAACU,OAAO,CAAC7H,MAAM,GAAG,CAAC,EAAE;MAC1B4H,IAAI,GAAG,IAAI,CAACR,KAAK,KAAK,UAAU,GAAGlJ,QAAQ,CAAC,wBAAwB,EAAE,yCAAyC,EAAE,IAAI,CAACiJ,KAAK,CAAClG,gBAAgB,CAACjC,QAAQ,CAAC6B,eAAe,EAAE,IAAI,CAACsG,KAAK,CAAClG,gBAAgB,CAACjC,QAAQ,CAAC+B,sBAAsB,GAAG,CAAC,CAAC,GAAG7C,QAAQ,CAAC,0BAA0B,EAAE,2CAA2C,EAAE,IAAI,CAACiJ,KAAK,CAAClG,gBAAgB,CAACrC,QAAQ,CAACiC,eAAe,EAAE,IAAI,CAACsG,KAAK,CAAClG,gBAAgB,CAACrC,QAAQ,CAACmC,sBAAsB,GAAG,CAAC,CAAC;IACtb,CAAC,MACI;MACD6G,IAAI,GAAG,IAAI,CAACR,KAAK,KAAK,UAAU,GAAGlJ,QAAQ,CAAC,aAAa,EAAE,4BAA4B,EAAE,IAAI,CAACiJ,KAAK,CAAClG,gBAAgB,CAACjC,QAAQ,CAAC6B,eAAe,EAAE,IAAI,CAACsG,KAAK,CAAClG,gBAAgB,CAACjC,QAAQ,CAAC+B,sBAAsB,GAAG,CAAC,CAAC,GAAG7C,QAAQ,CAAC,eAAe,EAAE,8BAA8B,EAAE,IAAI,CAACiJ,KAAK,CAAClG,gBAAgB,CAACrC,QAAQ,CAACiC,eAAe,EAAE,IAAI,CAACsG,KAAK,CAAClG,gBAAgB,CAACrC,QAAQ,CAACmC,sBAAsB,GAAG,CAAC,CAAC;IACtY;IACA,MAAM+G,SAAS,GAAG,IAAI,CAAC7D,SAAS,CAAC,IAAI1H,SAAS,CAAC,IAAI,CAAC+K,MAAM,CAACQ,SAAS,EAAE;MAClEC,qBAAqB,EAAE;IAC3B,CAAC,CAAC,CAAC;IACH,MAAMC,OAAO,GAAG,IAAIxL,MAAM,CAAC,EAAE,EAAEoL,IAAI,EAAE,EAAE,EAAE,KAAK,CAAC;IAC/CE,SAAS,CAACjB,IAAI,CAACmB,OAAO,EAAE;MAAEC,IAAI,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAK,CAAC,CAAC;IACrD,MAAMC,aAAa,GAAG,IAAI3L,MAAM,CAAC,EAAE,EAAE,SAAS,EAAEmB,SAAS,CAACyK,WAAW,CAACtL,OAAO,CAACuL,cAAc,CAAC,EAAE,IAAI,EAAE,MAAM;MACvG,IAAI,CAACpB,OAAO,CAACqB,KAAK,CAAC,CAAC;MACpB,IAAI,CAAC/J,UAAU,CAAC6H,kBAAkB,CAACnG,GAAG,CAAC,IAAI,CAAC1B,UAAU,CAAC6H,kBAAkB,CAACrB,GAAG,CAAC,CAAC,KAAKoC,KAAK,GAAGjH,SAAS,GAAG,IAAI,CAACiH,KAAK,EAAEjH,SAAS,CAAC;IAClI,CAAC,CAAC;IACF,IAAI,CAAC+D,SAAS,CAAChH,OAAO,CAACsC,MAAM,IAAI;MAC7B,MAAMgJ,QAAQ,GAAG,IAAI,CAAChK,UAAU,CAAC6H,kBAAkB,CAACxG,IAAI,CAACL,MAAM,CAAC,KAAK4H,KAAK;MAC1EgB,aAAa,CAACK,OAAO,GAAGD,QAAQ;IACpC,CAAC,CAAC,CAAC;IACHT,SAAS,CAACjB,IAAI,CAACsB,aAAa,EAAE;MAAEF,IAAI,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAK,CAAC,CAAC;EAC/D;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}