{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = this && this.__param || function (paramIndex, decorator) {\n  return function (target, key) {\n    decorator(target, key, paramIndex);\n  };\n};\nvar CodeActionKeybindingResolver_1;\nimport { HierarchicalKind } from '../../../../base/common/hierarchicalKind.js';\nimport { Lazy } from '../../../../base/common/lazy.js';\nimport { codeActionCommandId, fixAllCommandId, organizeImportsCommandId, refactorCommandId, sourceActionCommandId } from './codeAction.js';\nimport { CodeActionCommandArgs, CodeActionKind } from '../common/types.js';\nimport { IKeybindingService } from '../../../../platform/keybinding/common/keybinding.js';\nlet CodeActionKeybindingResolver = /*#__PURE__*/(() => {\n  let CodeActionKeybindingResolver = class CodeActionKeybindingResolver {\n    static {\n      CodeActionKeybindingResolver_1 = this;\n    }\n    static {\n      this.codeActionCommands = [refactorCommandId, codeActionCommandId, sourceActionCommandId, organizeImportsCommandId, fixAllCommandId];\n    }\n    constructor(keybindingService) {\n      this.keybindingService = keybindingService;\n    }\n    getResolver() {\n      // Lazy since we may not actually ever read the value\n      const allCodeActionBindings = new Lazy(() => this.keybindingService.getKeybindings().filter(item => CodeActionKeybindingResolver_1.codeActionCommands.indexOf(item.command) >= 0).filter(item => item.resolvedKeybinding).map(item => {\n        // Special case these commands since they come built-in with VS Code and don't use 'commandArgs'\n        let commandArgs = item.commandArgs;\n        if (item.command === organizeImportsCommandId) {\n          commandArgs = {\n            kind: CodeActionKind.SourceOrganizeImports.value\n          };\n        } else if (item.command === fixAllCommandId) {\n          commandArgs = {\n            kind: CodeActionKind.SourceFixAll.value\n          };\n        }\n        return {\n          resolvedKeybinding: item.resolvedKeybinding,\n          ...CodeActionCommandArgs.fromUser(commandArgs, {\n            kind: HierarchicalKind.None,\n            apply: \"never\" /* CodeActionAutoApply.Never */\n          })\n        };\n      }));\n      return action => {\n        if (action.kind) {\n          const binding = this.bestKeybindingForCodeAction(action, allCodeActionBindings.value);\n          return binding?.resolvedKeybinding;\n        }\n        return undefined;\n      };\n    }\n    bestKeybindingForCodeAction(action, candidates) {\n      if (!action.kind) {\n        return undefined;\n      }\n      const kind = new HierarchicalKind(action.kind);\n      return candidates.filter(candidate => candidate.kind.contains(kind)).filter(candidate => {\n        if (candidate.preferred) {\n          // If the candidate keybinding only applies to preferred actions, the this action must also be preferred\n          return action.isPreferred;\n        }\n        return true;\n      }).reduceRight((currentBest, candidate) => {\n        if (!currentBest) {\n          return candidate;\n        }\n        // Select the more specific binding\n        return currentBest.kind.contains(candidate.kind) ? candidate : currentBest;\n      }, undefined);\n    }\n  };\n  return CodeActionKeybindingResolver;\n})();\nCodeActionKeybindingResolver = CodeActionKeybindingResolver_1 = __decorate([__param(0, IKeybindingService)], CodeActionKeybindingResolver);\nexport { CodeActionKeybindingResolver };", "map": {"version": 3, "names": ["__decorate", "decorators", "target", "key", "desc", "c", "arguments", "length", "r", "Object", "getOwnPropertyDescriptor", "d", "Reflect", "decorate", "i", "defineProperty", "__param", "paramIndex", "decorator", "CodeActionKeybindingResolver_1", "HierarchicalKind", "Lazy", "codeActionCommandId", "fixAllCommandId", "organizeImportsCommandId", "refactorCommandId", "sourceActionCommandId", "CodeActionCommandArgs", "CodeActionKind", "IKeybindingService", "CodeActionKeybindingResolver", "codeActionCommands", "constructor", "keybindingService", "getResolver", "allCodeActionBindings", "getKeybindings", "filter", "item", "indexOf", "command", "resolvedKeybinding", "map", "commandArgs", "kind", "SourceOrganizeImports", "value", "SourceFixAll", "fromUser", "None", "apply", "action", "binding", "bestKeybindingForCodeAction", "undefined", "candidates", "candidate", "contains", "preferred", "isPreferred", "reduceRight", "currentBest"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/contrib/codeAction/browser/codeActionKeybindingResolver.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = (this && this.__param) || function (paramIndex, decorator) {\n    return function (target, key) { decorator(target, key, paramIndex); }\n};\nvar CodeActionKeybindingResolver_1;\nimport { HierarchicalKind } from '../../../../base/common/hierarchicalKind.js';\nimport { Lazy } from '../../../../base/common/lazy.js';\nimport { codeActionCommandId, fixAllCommandId, organizeImportsCommandId, refactorCommandId, sourceActionCommandId } from './codeAction.js';\nimport { CodeActionCommandArgs, CodeActionKind } from '../common/types.js';\nimport { IKeybindingService } from '../../../../platform/keybinding/common/keybinding.js';\nlet CodeActionKeybindingResolver = class CodeActionKeybindingResolver {\n    static { CodeActionKeybindingResolver_1 = this; }\n    static { this.codeActionCommands = [\n        refactorCommandId,\n        codeActionCommandId,\n        sourceActionCommandId,\n        organizeImportsCommandId,\n        fixAllCommandId\n    ]; }\n    constructor(keybindingService) {\n        this.keybindingService = keybindingService;\n    }\n    getResolver() {\n        // Lazy since we may not actually ever read the value\n        const allCodeActionBindings = new Lazy(() => this.keybindingService.getKeybindings()\n            .filter(item => CodeActionKeybindingResolver_1.codeActionCommands.indexOf(item.command) >= 0)\n            .filter(item => item.resolvedKeybinding)\n            .map((item) => {\n            // Special case these commands since they come built-in with VS Code and don't use 'commandArgs'\n            let commandArgs = item.commandArgs;\n            if (item.command === organizeImportsCommandId) {\n                commandArgs = { kind: CodeActionKind.SourceOrganizeImports.value };\n            }\n            else if (item.command === fixAllCommandId) {\n                commandArgs = { kind: CodeActionKind.SourceFixAll.value };\n            }\n            return {\n                resolvedKeybinding: item.resolvedKeybinding,\n                ...CodeActionCommandArgs.fromUser(commandArgs, {\n                    kind: HierarchicalKind.None,\n                    apply: \"never\" /* CodeActionAutoApply.Never */\n                })\n            };\n        }));\n        return (action) => {\n            if (action.kind) {\n                const binding = this.bestKeybindingForCodeAction(action, allCodeActionBindings.value);\n                return binding?.resolvedKeybinding;\n            }\n            return undefined;\n        };\n    }\n    bestKeybindingForCodeAction(action, candidates) {\n        if (!action.kind) {\n            return undefined;\n        }\n        const kind = new HierarchicalKind(action.kind);\n        return candidates\n            .filter(candidate => candidate.kind.contains(kind))\n            .filter(candidate => {\n            if (candidate.preferred) {\n                // If the candidate keybinding only applies to preferred actions, the this action must also be preferred\n                return action.isPreferred;\n            }\n            return true;\n        })\n            .reduceRight((currentBest, candidate) => {\n            if (!currentBest) {\n                return candidate;\n            }\n            // Select the more specific binding\n            return currentBest.kind.contains(candidate.kind) ? candidate : currentBest;\n        }, undefined);\n    }\n};\nCodeActionKeybindingResolver = CodeActionKeybindingResolver_1 = __decorate([\n    __param(0, IKeybindingService)\n], CodeActionKeybindingResolver);\nexport { CodeActionKeybindingResolver };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,IAAIA,UAAU,GAAI,IAAI,IAAI,IAAI,CAACA,UAAU,IAAK,UAAUC,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAE;EACnF,IAAIC,CAAC,GAAGC,SAAS,CAACC,MAAM;IAAEC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGH,MAAM,GAAGE,IAAI,KAAK,IAAI,GAAGA,IAAI,GAAGK,MAAM,CAACC,wBAAwB,CAACR,MAAM,EAAEC,GAAG,CAAC,GAAGC,IAAI;IAAEO,CAAC;EAC5H,IAAI,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,CAACC,QAAQ,KAAK,UAAU,EAAEL,CAAC,GAAGI,OAAO,CAACC,QAAQ,CAACZ,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,CAAC,CAAC,KAC1H,KAAK,IAAIU,CAAC,GAAGb,UAAU,CAACM,MAAM,GAAG,CAAC,EAAEO,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE,IAAIH,CAAC,GAAGV,UAAU,CAACa,CAAC,CAAC,EAAEN,CAAC,GAAG,CAACH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACH,CAAC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACT,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,GAAGG,CAAC,CAACT,MAAM,EAAEC,GAAG,CAAC,KAAKK,CAAC;EACjJ,OAAOH,CAAC,GAAG,CAAC,IAAIG,CAAC,IAAIC,MAAM,CAACM,cAAc,CAACb,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,EAAEA,CAAC;AACjE,CAAC;AACD,IAAIQ,OAAO,GAAI,IAAI,IAAI,IAAI,CAACA,OAAO,IAAK,UAAUC,UAAU,EAAEC,SAAS,EAAE;EACrE,OAAO,UAAUhB,MAAM,EAAEC,GAAG,EAAE;IAAEe,SAAS,CAAChB,MAAM,EAAEC,GAAG,EAAEc,UAAU,CAAC;EAAE,CAAC;AACzE,CAAC;AACD,IAAIE,8BAA8B;AAClC,SAASC,gBAAgB,QAAQ,6CAA6C;AAC9E,SAASC,IAAI,QAAQ,iCAAiC;AACtD,SAASC,mBAAmB,EAAEC,eAAe,EAAEC,wBAAwB,EAAEC,iBAAiB,EAAEC,qBAAqB,QAAQ,iBAAiB;AAC1I,SAASC,qBAAqB,EAAEC,cAAc,QAAQ,oBAAoB;AAC1E,SAASC,kBAAkB,QAAQ,sDAAsD;AACzF,IAAIC,4BAA4B;EAAA,IAA5BA,4BAA4B,GAAG,MAAMA,4BAA4B,CAAC;IAClE;MAASX,8BAA8B,GAAG,IAAI;IAAE;IAChD;MAAS,IAAI,CAACY,kBAAkB,GAAG,CAC/BN,iBAAiB,EACjBH,mBAAmB,EACnBI,qBAAqB,EACrBF,wBAAwB,EACxBD,eAAe,CAClB;IAAE;IACHS,WAAWA,CAACC,iBAAiB,EAAE;MAC3B,IAAI,CAACA,iBAAiB,GAAGA,iBAAiB;IAC9C;IACAC,WAAWA,CAAA,EAAG;MACV;MACA,MAAMC,qBAAqB,GAAG,IAAId,IAAI,CAAC,MAAM,IAAI,CAACY,iBAAiB,CAACG,cAAc,CAAC,CAAC,CAC/EC,MAAM,CAACC,IAAI,IAAInB,8BAA8B,CAACY,kBAAkB,CAACQ,OAAO,CAACD,IAAI,CAACE,OAAO,CAAC,IAAI,CAAC,CAAC,CAC5FH,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACG,kBAAkB,CAAC,CACvCC,GAAG,CAAEJ,IAAI,IAAK;QACf;QACA,IAAIK,WAAW,GAAGL,IAAI,CAACK,WAAW;QAClC,IAAIL,IAAI,CAACE,OAAO,KAAKhB,wBAAwB,EAAE;UAC3CmB,WAAW,GAAG;YAAEC,IAAI,EAAEhB,cAAc,CAACiB,qBAAqB,CAACC;UAAM,CAAC;QACtE,CAAC,MACI,IAAIR,IAAI,CAACE,OAAO,KAAKjB,eAAe,EAAE;UACvCoB,WAAW,GAAG;YAAEC,IAAI,EAAEhB,cAAc,CAACmB,YAAY,CAACD;UAAM,CAAC;QAC7D;QACA,OAAO;UACHL,kBAAkB,EAAEH,IAAI,CAACG,kBAAkB;UAC3C,GAAGd,qBAAqB,CAACqB,QAAQ,CAACL,WAAW,EAAE;YAC3CC,IAAI,EAAExB,gBAAgB,CAAC6B,IAAI;YAC3BC,KAAK,EAAE,OAAO,CAAC;UACnB,CAAC;QACL,CAAC;MACL,CAAC,CAAC,CAAC;MACH,OAAQC,MAAM,IAAK;QACf,IAAIA,MAAM,CAACP,IAAI,EAAE;UACb,MAAMQ,OAAO,GAAG,IAAI,CAACC,2BAA2B,CAACF,MAAM,EAAEhB,qBAAqB,CAACW,KAAK,CAAC;UACrF,OAAOM,OAAO,EAAEX,kBAAkB;QACtC;QACA,OAAOa,SAAS;MACpB,CAAC;IACL;IACAD,2BAA2BA,CAACF,MAAM,EAAEI,UAAU,EAAE;MAC5C,IAAI,CAACJ,MAAM,CAACP,IAAI,EAAE;QACd,OAAOU,SAAS;MACpB;MACA,MAAMV,IAAI,GAAG,IAAIxB,gBAAgB,CAAC+B,MAAM,CAACP,IAAI,CAAC;MAC9C,OAAOW,UAAU,CACZlB,MAAM,CAACmB,SAAS,IAAIA,SAAS,CAACZ,IAAI,CAACa,QAAQ,CAACb,IAAI,CAAC,CAAC,CAClDP,MAAM,CAACmB,SAAS,IAAI;QACrB,IAAIA,SAAS,CAACE,SAAS,EAAE;UACrB;UACA,OAAOP,MAAM,CAACQ,WAAW;QAC7B;QACA,OAAO,IAAI;MACf,CAAC,CAAC,CACGC,WAAW,CAAC,CAACC,WAAW,EAAEL,SAAS,KAAK;QACzC,IAAI,CAACK,WAAW,EAAE;UACd,OAAOL,SAAS;QACpB;QACA;QACA,OAAOK,WAAW,CAACjB,IAAI,CAACa,QAAQ,CAACD,SAAS,CAACZ,IAAI,CAAC,GAAGY,SAAS,GAAGK,WAAW;MAC9E,CAAC,EAAEP,SAAS,CAAC;IACjB;EACJ,CAAC;EAAA,OAhEGxB,4BAA4B;AAAA,IAgE/B;AACDA,4BAA4B,GAAGX,8BAA8B,GAAGnB,UAAU,CAAC,CACvEgB,OAAO,CAAC,CAAC,EAAEa,kBAAkB,CAAC,CACjC,EAAEC,4BAA4B,CAAC;AAChC,SAASA,4BAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}