{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as strings from './strings.js';\nexport function buildReplaceStringWithCasePreserved(matches, pattern) {\n  if (matches && matches[0] !== '') {\n    const containsHyphens = validateSpecificSpecialCharacter(matches, pattern, '-');\n    const containsUnderscores = validateSpecificSpecialCharacter(matches, pattern, '_');\n    if (containsHyphens && !containsUnderscores) {\n      return buildReplaceStringForSpecificSpecialCharacter(matches, pattern, '-');\n    } else if (!containsHyphens && containsUnderscores) {\n      return buildReplaceStringForSpecificSpecialCharacter(matches, pattern, '_');\n    }\n    if (matches[0].toUpperCase() === matches[0]) {\n      return pattern.toUpperCase();\n    } else if (matches[0].toLowerCase() === matches[0]) {\n      return pattern.toLowerCase();\n    } else if (strings.containsUppercaseCharacter(matches[0][0]) && pattern.length > 0) {\n      return pattern[0].toUpperCase() + pattern.substr(1);\n    } else if (matches[0][0].toUpperCase() !== matches[0][0] && pattern.length > 0) {\n      return pattern[0].toLowerCase() + pattern.substr(1);\n    } else {\n      // we don't understand its pattern yet.\n      return pattern;\n    }\n  } else {\n    return pattern;\n  }\n}\nfunction validateSpecificSpecialCharacter(matches, pattern, specialCharacter) {\n  const doesContainSpecialCharacter = matches[0].indexOf(specialCharacter) !== -1 && pattern.indexOf(specialCharacter) !== -1;\n  return doesContainSpecialCharacter && matches[0].split(specialCharacter).length === pattern.split(specialCharacter).length;\n}\nfunction buildReplaceStringForSpecificSpecialCharacter(matches, pattern, specialCharacter) {\n  const splitPatternAtSpecialCharacter = pattern.split(specialCharacter);\n  const splitMatchAtSpecialCharacter = matches[0].split(specialCharacter);\n  let replaceString = '';\n  splitPatternAtSpecialCharacter.forEach((splitValue, index) => {\n    replaceString += buildReplaceStringWithCasePreserved([splitMatchAtSpecialCharacter[index]], splitValue) + specialCharacter;\n  });\n  return replaceString.slice(0, -1);\n}", "map": {"version": 3, "names": ["strings", "buildReplaceStringWithCasePreserved", "matches", "pattern", "containsHyphens", "validateSpecificSpecialCharacter", "containsUnderscores", "buildReplaceStringForSpecificSpecialCharacter", "toUpperCase", "toLowerCase", "containsUppercaseCharacter", "length", "substr", "specialCharacter", "doesContainSpecialCharacter", "indexOf", "split", "splitPatternAtSpecialCharacter", "splitMatchAtSpecialCharacter", "replaceString", "for<PERSON>ach", "splitValue", "index", "slice"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/common/search.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as strings from './strings.js';\nexport function buildReplaceStringWithCasePreserved(matches, pattern) {\n    if (matches && (matches[0] !== '')) {\n        const containsHyphens = validateSpecificSpecialCharacter(matches, pattern, '-');\n        const containsUnderscores = validateSpecificSpecialCharacter(matches, pattern, '_');\n        if (containsHyphens && !containsUnderscores) {\n            return buildReplaceStringForSpecificSpecialCharacter(matches, pattern, '-');\n        }\n        else if (!containsHyphens && containsUnderscores) {\n            return buildReplaceStringForSpecificSpecialCharacter(matches, pattern, '_');\n        }\n        if (matches[0].toUpperCase() === matches[0]) {\n            return pattern.toUpperCase();\n        }\n        else if (matches[0].toLowerCase() === matches[0]) {\n            return pattern.toLowerCase();\n        }\n        else if (strings.containsUppercaseCharacter(matches[0][0]) && pattern.length > 0) {\n            return pattern[0].toUpperCase() + pattern.substr(1);\n        }\n        else if (matches[0][0].toUpperCase() !== matches[0][0] && pattern.length > 0) {\n            return pattern[0].toLowerCase() + pattern.substr(1);\n        }\n        else {\n            // we don't understand its pattern yet.\n            return pattern;\n        }\n    }\n    else {\n        return pattern;\n    }\n}\nfunction validateSpecificSpecialCharacter(matches, pattern, specialCharacter) {\n    const doesContainSpecialCharacter = matches[0].indexOf(specialCharacter) !== -1 && pattern.indexOf(specialCharacter) !== -1;\n    return doesContainSpecialCharacter && matches[0].split(specialCharacter).length === pattern.split(specialCharacter).length;\n}\nfunction buildReplaceStringForSpecificSpecialCharacter(matches, pattern, specialCharacter) {\n    const splitPatternAtSpecialCharacter = pattern.split(specialCharacter);\n    const splitMatchAtSpecialCharacter = matches[0].split(specialCharacter);\n    let replaceString = '';\n    splitPatternAtSpecialCharacter.forEach((splitValue, index) => {\n        replaceString += buildReplaceStringWithCasePreserved([splitMatchAtSpecialCharacter[index]], splitValue) + specialCharacter;\n    });\n    return replaceString.slice(0, -1);\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,KAAKA,OAAO,MAAM,cAAc;AACvC,OAAO,SAASC,mCAAmCA,CAACC,OAAO,EAAEC,OAAO,EAAE;EAClE,IAAID,OAAO,IAAKA,OAAO,CAAC,CAAC,CAAC,KAAK,EAAG,EAAE;IAChC,MAAME,eAAe,GAAGC,gCAAgC,CAACH,OAAO,EAAEC,OAAO,EAAE,GAAG,CAAC;IAC/E,MAAMG,mBAAmB,GAAGD,gCAAgC,CAACH,OAAO,EAAEC,OAAO,EAAE,GAAG,CAAC;IACnF,IAAIC,eAAe,IAAI,CAACE,mBAAmB,EAAE;MACzC,OAAOC,6CAA6C,CAACL,OAAO,EAAEC,OAAO,EAAE,GAAG,CAAC;IAC/E,CAAC,MACI,IAAI,CAACC,eAAe,IAAIE,mBAAmB,EAAE;MAC9C,OAAOC,6CAA6C,CAACL,OAAO,EAAEC,OAAO,EAAE,GAAG,CAAC;IAC/E;IACA,IAAID,OAAO,CAAC,CAAC,CAAC,CAACM,WAAW,CAAC,CAAC,KAAKN,OAAO,CAAC,CAAC,CAAC,EAAE;MACzC,OAAOC,OAAO,CAACK,WAAW,CAAC,CAAC;IAChC,CAAC,MACI,IAAIN,OAAO,CAAC,CAAC,CAAC,CAACO,WAAW,CAAC,CAAC,KAAKP,OAAO,CAAC,CAAC,CAAC,EAAE;MAC9C,OAAOC,OAAO,CAACM,WAAW,CAAC,CAAC;IAChC,CAAC,MACI,IAAIT,OAAO,CAACU,0BAA0B,CAACR,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIC,OAAO,CAACQ,MAAM,GAAG,CAAC,EAAE;MAC9E,OAAOR,OAAO,CAAC,CAAC,CAAC,CAACK,WAAW,CAAC,CAAC,GAAGL,OAAO,CAACS,MAAM,CAAC,CAAC,CAAC;IACvD,CAAC,MACI,IAAIV,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACM,WAAW,CAAC,CAAC,KAAKN,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIC,OAAO,CAACQ,MAAM,GAAG,CAAC,EAAE;MAC1E,OAAOR,OAAO,CAAC,CAAC,CAAC,CAACM,WAAW,CAAC,CAAC,GAAGN,OAAO,CAACS,MAAM,CAAC,CAAC,CAAC;IACvD,CAAC,MACI;MACD;MACA,OAAOT,OAAO;IAClB;EACJ,CAAC,MACI;IACD,OAAOA,OAAO;EAClB;AACJ;AACA,SAASE,gCAAgCA,CAACH,OAAO,EAAEC,OAAO,EAAEU,gBAAgB,EAAE;EAC1E,MAAMC,2BAA2B,GAAGZ,OAAO,CAAC,CAAC,CAAC,CAACa,OAAO,CAACF,gBAAgB,CAAC,KAAK,CAAC,CAAC,IAAIV,OAAO,CAACY,OAAO,CAACF,gBAAgB,CAAC,KAAK,CAAC,CAAC;EAC3H,OAAOC,2BAA2B,IAAIZ,OAAO,CAAC,CAAC,CAAC,CAACc,KAAK,CAACH,gBAAgB,CAAC,CAACF,MAAM,KAAKR,OAAO,CAACa,KAAK,CAACH,gBAAgB,CAAC,CAACF,MAAM;AAC9H;AACA,SAASJ,6CAA6CA,CAACL,OAAO,EAAEC,OAAO,EAAEU,gBAAgB,EAAE;EACvF,MAAMI,8BAA8B,GAAGd,OAAO,CAACa,KAAK,CAACH,gBAAgB,CAAC;EACtE,MAAMK,4BAA4B,GAAGhB,OAAO,CAAC,CAAC,CAAC,CAACc,KAAK,CAACH,gBAAgB,CAAC;EACvE,IAAIM,aAAa,GAAG,EAAE;EACtBF,8BAA8B,CAACG,OAAO,CAAC,CAACC,UAAU,EAAEC,KAAK,KAAK;IAC1DH,aAAa,IAAIlB,mCAAmC,CAAC,CAACiB,4BAA4B,CAACI,KAAK,CAAC,CAAC,EAAED,UAAU,CAAC,GAAGR,gBAAgB;EAC9H,CAAC,CAAC;EACF,OAAOM,aAAa,CAACI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}