<div id="dasboard-container" class="container-fluid">
  <div id="dashboard-cards-container" class="row">
    <div class="col-md-8 d-flex flex-column gap-2">
      <div class="d-flex flex-column">
        <app-dashboard-img-card></app-dashboard-img-card>
        <div class="d-flex flex-wrap txt-card-wrapper">
          <div *ngFor="let details of dashboardDetails" class="col-3">
            <app-dashboard-txt-card
              [title]="details.title"
              [value]="details.value"
              [subtitle]="details.subtitle"
            >
            </app-dashboard-txt-card>
          </div>
        </div>
      </div>
    </div>
    <div id="activity-monitoring-container" class="col-4">
      <div class="active-monitoring-wrapper">
        <div class="row active-monitoring">
          <div class="col-5 active-monitoring-text">Active Monitoring</div>
          <div class="col-5">
            <ava-dropdown
              class="a-m-dropdown"
              dropdownTitle="Agents"
              [options]="options"
              [search]="false"
              (selectionChange)="onSelectionChange($event)"
            >
            </ava-dropdown>
          </div>
          <div class="col-2">
            <ava-link label="View All" color="#3B3F46"></ava-link>
          </div>
        </div>
        <div class="d-flex flex-column gap-4 active-monitoring-list">
          <div *ngFor="let activity of activityMonitoring">
            <app-dashboard-agent-monitoring-card
              [activity]="activity"
            ></app-dashboard-agent-monitoring-card>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div id="dasborad-bottom-container" class="row">
    <div id="high-prioirty-container" class="col-12">
      <div class="box-wrapper high-priority-wrapper p-4">
        <div class="col-12 agent-approvals-cards-header header-section mb-4">
          <span> Approvals </span>
          <div class="d-flex gap-2">
            <ava-tag
              *ngFor="let tab of approvalTabs"
              [label]="tab"
              [pill]="true"
              color="custom"
              (clicked)="updatedSelectedApprovalTab(tab)"
              [customStyle]="
                selectedApprovalTab === tab
                  ? agentApprovalsSelectedPillStyle
                  : agentApprovalsPillStyle
              "
            >
            </ava-tag>
          </div>
        </div>
        <div class="row g-2 overflow-y-scroll">
          @for (item of approvalData; track $index) {
            <div class="col-12 col-md-4">
              <app-dashboard-approval-card
                [title]="item.title"
                [email]="item.requestedBy"
                [date]="item.requestedAt"
                [type]="item.type"
                [id]="item.id"
                [rowData]="item.rawData"
                (testClick)="onTestClick($event)"
                (sendBackClick)="onSendBackClick($event)"
                (approveClick)="approveItem($event)"
              >
              </app-dashboard-approval-card>
            </div>
          }
        </div>
        <div class="agent-approvals-cards-footer d-flex justify-content-end">
          <ava-link label="View All" color="#3B3F46"></ava-link>
        </div>
      </div>
    </div>
  </div>
</div>
