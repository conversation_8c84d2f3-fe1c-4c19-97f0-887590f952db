{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport class ListError extends Error {\n  constructor(user, message) {\n    super(`ListError [${user}] ${message}`);\n  }\n}", "map": {"version": 3, "names": ["ListError", "Error", "constructor", "user", "message"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/browser/ui/list/list.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport class ListError extends Error {\n    constructor(user, message) {\n        super(`ListError [${user}] ${message}`);\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,MAAMA,SAAS,SAASC,KAAK,CAAC;EACjCC,WAAWA,CAACC,IAAI,EAAEC,OAAO,EAAE;IACvB,KAAK,CAAC,cAAcD,IAAI,KAAKC,OAAO,EAAE,CAAC;EAC3C;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}