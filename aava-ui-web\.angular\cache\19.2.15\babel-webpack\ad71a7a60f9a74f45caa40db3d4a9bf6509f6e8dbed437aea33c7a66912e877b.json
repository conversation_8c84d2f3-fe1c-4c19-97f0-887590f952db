{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport class Node {\n  constructor(key, data) {\n    this.key = key;\n    this.data = data;\n    this.incoming = new Map();\n    this.outgoing = new Map();\n  }\n}\nexport class Graph {\n  constructor(_hashFn) {\n    this._hashFn = _hashFn;\n    this._nodes = new Map();\n    // empty\n  }\n  roots() {\n    const ret = [];\n    for (const node of this._nodes.values()) {\n      if (node.outgoing.size === 0) {\n        ret.push(node);\n      }\n    }\n    return ret;\n  }\n  insertEdge(from, to) {\n    const fromNode = this.lookupOrInsertNode(from);\n    const toNode = this.lookupOrInsertNode(to);\n    fromNode.outgoing.set(toNode.key, toNode);\n    toNode.incoming.set(fromNode.key, fromNode);\n  }\n  removeNode(data) {\n    const key = this._hashFn(data);\n    this._nodes.delete(key);\n    for (const node of this._nodes.values()) {\n      node.outgoing.delete(key);\n      node.incoming.delete(key);\n    }\n  }\n  lookupOrInsertNode(data) {\n    const key = this._hashFn(data);\n    let node = this._nodes.get(key);\n    if (!node) {\n      node = new Node(key, data);\n      this._nodes.set(key, node);\n    }\n    return node;\n  }\n  isEmpty() {\n    return this._nodes.size === 0;\n  }\n  toString() {\n    const data = [];\n    for (const [key, value] of this._nodes) {\n      data.push(`${key}\\n\\t(-> incoming)[${[...value.incoming.keys()].join(', ')}]\\n\\t(outgoing ->)[${[...value.outgoing.keys()].join(',')}]\\n`);\n    }\n    return data.join('\\n');\n  }\n  /**\n   * This is brute force and slow and **only** be used\n   * to trouble shoot.\n   */\n  findCycleSlow() {\n    for (const [id, node] of this._nodes) {\n      const seen = new Set([id]);\n      const res = this._findCycle(node, seen);\n      if (res) {\n        return res;\n      }\n    }\n    return undefined;\n  }\n  _findCycle(node, seen) {\n    for (const [id, outgoing] of node.outgoing) {\n      if (seen.has(id)) {\n        return [...seen, id].join(' -> ');\n      }\n      seen.add(id);\n      const value = this._findCycle(outgoing, seen);\n      if (value) {\n        return value;\n      }\n      seen.delete(id);\n    }\n    return undefined;\n  }\n}", "map": {"version": 3, "names": ["Node", "constructor", "key", "data", "incoming", "Map", "outgoing", "Graph", "_hashFn", "_nodes", "roots", "ret", "node", "values", "size", "push", "insertEdge", "from", "to", "fromNode", "lookupOrInsertNode", "toNode", "set", "removeNode", "delete", "get", "isEmpty", "toString", "value", "keys", "join", "findCycleSlow", "id", "seen", "Set", "res", "_findCycle", "undefined", "has", "add"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/platform/instantiation/common/graph.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport class Node {\n    constructor(key, data) {\n        this.key = key;\n        this.data = data;\n        this.incoming = new Map();\n        this.outgoing = new Map();\n    }\n}\nexport class Graph {\n    constructor(_hashFn) {\n        this._hashFn = _hashFn;\n        this._nodes = new Map();\n        // empty\n    }\n    roots() {\n        const ret = [];\n        for (const node of this._nodes.values()) {\n            if (node.outgoing.size === 0) {\n                ret.push(node);\n            }\n        }\n        return ret;\n    }\n    insertEdge(from, to) {\n        const fromNode = this.lookupOrInsertNode(from);\n        const toNode = this.lookupOrInsertNode(to);\n        fromNode.outgoing.set(toNode.key, toNode);\n        toNode.incoming.set(fromNode.key, fromNode);\n    }\n    removeNode(data) {\n        const key = this._hashFn(data);\n        this._nodes.delete(key);\n        for (const node of this._nodes.values()) {\n            node.outgoing.delete(key);\n            node.incoming.delete(key);\n        }\n    }\n    lookupOrInsertNode(data) {\n        const key = this._hashFn(data);\n        let node = this._nodes.get(key);\n        if (!node) {\n            node = new Node(key, data);\n            this._nodes.set(key, node);\n        }\n        return node;\n    }\n    isEmpty() {\n        return this._nodes.size === 0;\n    }\n    toString() {\n        const data = [];\n        for (const [key, value] of this._nodes) {\n            data.push(`${key}\\n\\t(-> incoming)[${[...value.incoming.keys()].join(', ')}]\\n\\t(outgoing ->)[${[...value.outgoing.keys()].join(',')}]\\n`);\n        }\n        return data.join('\\n');\n    }\n    /**\n     * This is brute force and slow and **only** be used\n     * to trouble shoot.\n     */\n    findCycleSlow() {\n        for (const [id, node] of this._nodes) {\n            const seen = new Set([id]);\n            const res = this._findCycle(node, seen);\n            if (res) {\n                return res;\n            }\n        }\n        return undefined;\n    }\n    _findCycle(node, seen) {\n        for (const [id, outgoing] of node.outgoing) {\n            if (seen.has(id)) {\n                return [...seen, id].join(' -> ');\n            }\n            seen.add(id);\n            const value = this._findCycle(outgoing, seen);\n            if (value) {\n                return value;\n            }\n            seen.delete(id);\n        }\n        return undefined;\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,MAAMA,IAAI,CAAC;EACdC,WAAWA,CAACC,GAAG,EAAEC,IAAI,EAAE;IACnB,IAAI,CAACD,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC;IACzB,IAAI,CAACC,QAAQ,GAAG,IAAID,GAAG,CAAC,CAAC;EAC7B;AACJ;AACA,OAAO,MAAME,KAAK,CAAC;EACfN,WAAWA,CAACO,OAAO,EAAE;IACjB,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,MAAM,GAAG,IAAIJ,GAAG,CAAC,CAAC;IACvB;EACJ;EACAK,KAAKA,CAAA,EAAG;IACJ,MAAMC,GAAG,GAAG,EAAE;IACd,KAAK,MAAMC,IAAI,IAAI,IAAI,CAACH,MAAM,CAACI,MAAM,CAAC,CAAC,EAAE;MACrC,IAAID,IAAI,CAACN,QAAQ,CAACQ,IAAI,KAAK,CAAC,EAAE;QAC1BH,GAAG,CAACI,IAAI,CAACH,IAAI,CAAC;MAClB;IACJ;IACA,OAAOD,GAAG;EACd;EACAK,UAAUA,CAACC,IAAI,EAAEC,EAAE,EAAE;IACjB,MAAMC,QAAQ,GAAG,IAAI,CAACC,kBAAkB,CAACH,IAAI,CAAC;IAC9C,MAAMI,MAAM,GAAG,IAAI,CAACD,kBAAkB,CAACF,EAAE,CAAC;IAC1CC,QAAQ,CAACb,QAAQ,CAACgB,GAAG,CAACD,MAAM,CAACnB,GAAG,EAAEmB,MAAM,CAAC;IACzCA,MAAM,CAACjB,QAAQ,CAACkB,GAAG,CAACH,QAAQ,CAACjB,GAAG,EAAEiB,QAAQ,CAAC;EAC/C;EACAI,UAAUA,CAACpB,IAAI,EAAE;IACb,MAAMD,GAAG,GAAG,IAAI,CAACM,OAAO,CAACL,IAAI,CAAC;IAC9B,IAAI,CAACM,MAAM,CAACe,MAAM,CAACtB,GAAG,CAAC;IACvB,KAAK,MAAMU,IAAI,IAAI,IAAI,CAACH,MAAM,CAACI,MAAM,CAAC,CAAC,EAAE;MACrCD,IAAI,CAACN,QAAQ,CAACkB,MAAM,CAACtB,GAAG,CAAC;MACzBU,IAAI,CAACR,QAAQ,CAACoB,MAAM,CAACtB,GAAG,CAAC;IAC7B;EACJ;EACAkB,kBAAkBA,CAACjB,IAAI,EAAE;IACrB,MAAMD,GAAG,GAAG,IAAI,CAACM,OAAO,CAACL,IAAI,CAAC;IAC9B,IAAIS,IAAI,GAAG,IAAI,CAACH,MAAM,CAACgB,GAAG,CAACvB,GAAG,CAAC;IAC/B,IAAI,CAACU,IAAI,EAAE;MACPA,IAAI,GAAG,IAAIZ,IAAI,CAACE,GAAG,EAAEC,IAAI,CAAC;MAC1B,IAAI,CAACM,MAAM,CAACa,GAAG,CAACpB,GAAG,EAAEU,IAAI,CAAC;IAC9B;IACA,OAAOA,IAAI;EACf;EACAc,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACjB,MAAM,CAACK,IAAI,KAAK,CAAC;EACjC;EACAa,QAAQA,CAAA,EAAG;IACP,MAAMxB,IAAI,GAAG,EAAE;IACf,KAAK,MAAM,CAACD,GAAG,EAAE0B,KAAK,CAAC,IAAI,IAAI,CAACnB,MAAM,EAAE;MACpCN,IAAI,CAACY,IAAI,CAAC,GAAGb,GAAG,qBAAqB,CAAC,GAAG0B,KAAK,CAACxB,QAAQ,CAACyB,IAAI,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,GAAGF,KAAK,CAACtB,QAAQ,CAACuB,IAAI,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;IAC9I;IACA,OAAO3B,IAAI,CAAC2B,IAAI,CAAC,IAAI,CAAC;EAC1B;EACA;AACJ;AACA;AACA;EACIC,aAAaA,CAAA,EAAG;IACZ,KAAK,MAAM,CAACC,EAAE,EAAEpB,IAAI,CAAC,IAAI,IAAI,CAACH,MAAM,EAAE;MAClC,MAAMwB,IAAI,GAAG,IAAIC,GAAG,CAAC,CAACF,EAAE,CAAC,CAAC;MAC1B,MAAMG,GAAG,GAAG,IAAI,CAACC,UAAU,CAACxB,IAAI,EAAEqB,IAAI,CAAC;MACvC,IAAIE,GAAG,EAAE;QACL,OAAOA,GAAG;MACd;IACJ;IACA,OAAOE,SAAS;EACpB;EACAD,UAAUA,CAACxB,IAAI,EAAEqB,IAAI,EAAE;IACnB,KAAK,MAAM,CAACD,EAAE,EAAE1B,QAAQ,CAAC,IAAIM,IAAI,CAACN,QAAQ,EAAE;MACxC,IAAI2B,IAAI,CAACK,GAAG,CAACN,EAAE,CAAC,EAAE;QACd,OAAO,CAAC,GAAGC,IAAI,EAAED,EAAE,CAAC,CAACF,IAAI,CAAC,MAAM,CAAC;MACrC;MACAG,IAAI,CAACM,GAAG,CAACP,EAAE,CAAC;MACZ,MAAMJ,KAAK,GAAG,IAAI,CAACQ,UAAU,CAAC9B,QAAQ,EAAE2B,IAAI,CAAC;MAC7C,IAAIL,KAAK,EAAE;QACP,OAAOA,KAAK;MAChB;MACAK,IAAI,CAACT,MAAM,CAACQ,EAAE,CAAC;IACnB;IACA,OAAOK,SAAS;EACpB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}