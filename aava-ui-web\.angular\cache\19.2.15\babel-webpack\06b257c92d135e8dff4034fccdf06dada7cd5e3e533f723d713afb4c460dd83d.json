{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as dom from '../../dom.js';\nimport { Toggle } from '../toggle/toggle.js';\nimport { HistoryInputBox } from '../inputbox/inputBox.js';\nimport { Widget } from '../widget.js';\nimport { Codicon } from '../../../common/codicons.js';\nimport { Emitter } from '../../../common/event.js';\nimport './findInput.css';\nimport * as nls from '../../../../nls.js';\nimport { getDefaultHoverDelegate } from '../hover/hoverDelegateFactory.js';\nconst NLS_DEFAULT_LABEL = nls.localize('defaultLabel', \"input\");\nconst NLS_PRESERVE_CASE_LABEL = nls.localize('label.preserveCaseToggle', \"Preserve Case\");\nclass PreserveCaseToggle extends Toggle {\n  constructor(opts) {\n    super({\n      // TODO: does this need its own icon?\n      icon: Codicon.preserveCase,\n      title: NLS_PRESERVE_CASE_LABEL + opts.appendTitle,\n      isChecked: opts.isChecked,\n      hoverDelegate: opts.hoverDelegate ?? getDefaultHoverDelegate('element'),\n      inputActiveOptionBorder: opts.inputActiveOptionBorder,\n      inputActiveOptionForeground: opts.inputActiveOptionForeground,\n      inputActiveOptionBackground: opts.inputActiveOptionBackground\n    });\n  }\n}\nexport class ReplaceInput extends Widget {\n  constructor(parent, contextViewProvider, _showOptionButtons, options) {\n    super();\n    this._showOptionButtons = _showOptionButtons;\n    this.fixFocusOnOptionClickEnabled = true;\n    this.cachedOptionsWidth = 0;\n    this._onDidOptionChange = this._register(new Emitter());\n    this.onDidOptionChange = this._onDidOptionChange.event;\n    this._onKeyDown = this._register(new Emitter());\n    this.onKeyDown = this._onKeyDown.event;\n    this._onMouseDown = this._register(new Emitter());\n    this._onInput = this._register(new Emitter());\n    this._onKeyUp = this._register(new Emitter());\n    this._onPreserveCaseKeyDown = this._register(new Emitter());\n    this.onPreserveCaseKeyDown = this._onPreserveCaseKeyDown.event;\n    this.contextViewProvider = contextViewProvider;\n    this.placeholder = options.placeholder || '';\n    this.validation = options.validation;\n    this.label = options.label || NLS_DEFAULT_LABEL;\n    const appendPreserveCaseLabel = options.appendPreserveCaseLabel || '';\n    const history = options.history || [];\n    const flexibleHeight = !!options.flexibleHeight;\n    const flexibleWidth = !!options.flexibleWidth;\n    const flexibleMaxHeight = options.flexibleMaxHeight;\n    this.domNode = document.createElement('div');\n    this.domNode.classList.add('monaco-findInput');\n    this.inputBox = this._register(new HistoryInputBox(this.domNode, this.contextViewProvider, {\n      ariaLabel: this.label || '',\n      placeholder: this.placeholder || '',\n      validationOptions: {\n        validation: this.validation\n      },\n      history,\n      showHistoryHint: options.showHistoryHint,\n      flexibleHeight,\n      flexibleWidth,\n      flexibleMaxHeight,\n      inputBoxStyles: options.inputBoxStyles\n    }));\n    this.preserveCase = this._register(new PreserveCaseToggle({\n      appendTitle: appendPreserveCaseLabel,\n      isChecked: false,\n      ...options.toggleStyles\n    }));\n    this._register(this.preserveCase.onChange(viaKeyboard => {\n      this._onDidOptionChange.fire(viaKeyboard);\n      if (!viaKeyboard && this.fixFocusOnOptionClickEnabled) {\n        this.inputBox.focus();\n      }\n      this.validate();\n    }));\n    this._register(this.preserveCase.onKeyDown(e => {\n      this._onPreserveCaseKeyDown.fire(e);\n    }));\n    if (this._showOptionButtons) {\n      this.cachedOptionsWidth = this.preserveCase.width();\n    } else {\n      this.cachedOptionsWidth = 0;\n    }\n    // Arrow-Key support to navigate between options\n    const indexes = [this.preserveCase.domNode];\n    this.onkeydown(this.domNode, event => {\n      if (event.equals(15 /* KeyCode.LeftArrow */) || event.equals(17 /* KeyCode.RightArrow */) || event.equals(9 /* KeyCode.Escape */)) {\n        const index = indexes.indexOf(this.domNode.ownerDocument.activeElement);\n        if (index >= 0) {\n          let newIndex = -1;\n          if (event.equals(17 /* KeyCode.RightArrow */)) {\n            newIndex = (index + 1) % indexes.length;\n          } else if (event.equals(15 /* KeyCode.LeftArrow */)) {\n            if (index === 0) {\n              newIndex = indexes.length - 1;\n            } else {\n              newIndex = index - 1;\n            }\n          }\n          if (event.equals(9 /* KeyCode.Escape */)) {\n            indexes[index].blur();\n            this.inputBox.focus();\n          } else if (newIndex >= 0) {\n            indexes[newIndex].focus();\n          }\n          dom.EventHelper.stop(event, true);\n        }\n      }\n    });\n    const controls = document.createElement('div');\n    controls.className = 'controls';\n    controls.style.display = this._showOptionButtons ? 'block' : 'none';\n    controls.appendChild(this.preserveCase.domNode);\n    this.domNode.appendChild(controls);\n    parent?.appendChild(this.domNode);\n    this.onkeydown(this.inputBox.inputElement, e => this._onKeyDown.fire(e));\n    this.onkeyup(this.inputBox.inputElement, e => this._onKeyUp.fire(e));\n    this.oninput(this.inputBox.inputElement, e => this._onInput.fire());\n    this.onmousedown(this.inputBox.inputElement, e => this._onMouseDown.fire(e));\n  }\n  enable() {\n    this.domNode.classList.remove('disabled');\n    this.inputBox.enable();\n    this.preserveCase.enable();\n  }\n  disable() {\n    this.domNode.classList.add('disabled');\n    this.inputBox.disable();\n    this.preserveCase.disable();\n  }\n  setEnabled(enabled) {\n    if (enabled) {\n      this.enable();\n    } else {\n      this.disable();\n    }\n  }\n  select() {\n    this.inputBox.select();\n  }\n  focus() {\n    this.inputBox.focus();\n  }\n  getPreserveCase() {\n    return this.preserveCase.checked;\n  }\n  setPreserveCase(value) {\n    this.preserveCase.checked = value;\n  }\n  focusOnPreserve() {\n    this.preserveCase.focus();\n  }\n  validate() {\n    this.inputBox?.validate();\n  }\n  set width(newWidth) {\n    this.inputBox.paddingRight = this.cachedOptionsWidth;\n    this.domNode.style.width = newWidth + 'px';\n  }\n  dispose() {\n    super.dispose();\n  }\n}", "map": {"version": 3, "names": ["dom", "Toggle", "HistoryInputBox", "Widget", "Codicon", "Emitter", "nls", "getDefaultHoverDelegate", "NLS_DEFAULT_LABEL", "localize", "NLS_PRESERVE_CASE_LABEL", "PreserveCaseToggle", "constructor", "opts", "icon", "preserveCase", "title", "appendTitle", "isChecked", "hoverDelegate", "inputActiveOptionBorder", "inputActiveOptionForeground", "inputActiveOptionBackground", "ReplaceInput", "parent", "contextView<PERSON>rovider", "_showOptionButtons", "options", "fixFocusOnOptionClickEnabled", "cachedOptionsWidth", "_onDidOptionChange", "_register", "onDidOptionChange", "event", "_onKeyDown", "onKeyDown", "_onMouseDown", "_onInput", "_onKeyUp", "_onPreserveCaseKeyDown", "onPreserveCaseKeyDown", "placeholder", "validation", "label", "appendPreserveCaseLabel", "history", "flexibleHeight", "flexibleWidth", "flexibleMaxHeight", "domNode", "document", "createElement", "classList", "add", "inputBox", "aria<PERSON><PERSON><PERSON>", "validationOptions", "showHistoryHint", "inputBoxStyles", "toggleStyles", "onChange", "viaKeyboard", "fire", "focus", "validate", "e", "width", "indexes", "onkeydown", "equals", "index", "indexOf", "ownerDocument", "activeElement", "newIndex", "length", "blur", "EventHelper", "stop", "controls", "className", "style", "display", "append<PERSON><PERSON><PERSON>", "inputElement", "onkeyup", "oninput", "onmousedown", "enable", "remove", "disable", "setEnabled", "enabled", "select", "getPreserveCase", "checked", "setPreserveCase", "value", "focusOnPreserve", "newWidth", "paddingRight", "dispose"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/browser/ui/findinput/replaceInput.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as dom from '../../dom.js';\nimport { Toggle } from '../toggle/toggle.js';\nimport { HistoryInputBox } from '../inputbox/inputBox.js';\nimport { Widget } from '../widget.js';\nimport { Codicon } from '../../../common/codicons.js';\nimport { Emitter } from '../../../common/event.js';\nimport './findInput.css';\nimport * as nls from '../../../../nls.js';\nimport { getDefaultHoverDelegate } from '../hover/hoverDelegateFactory.js';\nconst NLS_DEFAULT_LABEL = nls.localize('defaultLabel', \"input\");\nconst NLS_PRESERVE_CASE_LABEL = nls.localize('label.preserveCaseToggle', \"Preserve Case\");\nclass PreserveCaseToggle extends Toggle {\n    constructor(opts) {\n        super({\n            // TODO: does this need its own icon?\n            icon: Codicon.preserveCase,\n            title: NLS_PRESERVE_CASE_LABEL + opts.appendTitle,\n            isChecked: opts.isChecked,\n            hoverDelegate: opts.hoverDelegate ?? getDefaultHoverDelegate('element'),\n            inputActiveOptionBorder: opts.inputActiveOptionBorder,\n            inputActiveOptionForeground: opts.inputActiveOptionForeground,\n            inputActiveOptionBackground: opts.inputActiveOptionBackground,\n        });\n    }\n}\nexport class ReplaceInput extends Widget {\n    constructor(parent, contextViewProvider, _showOptionButtons, options) {\n        super();\n        this._showOptionButtons = _showOptionButtons;\n        this.fixFocusOnOptionClickEnabled = true;\n        this.cachedOptionsWidth = 0;\n        this._onDidOptionChange = this._register(new Emitter());\n        this.onDidOptionChange = this._onDidOptionChange.event;\n        this._onKeyDown = this._register(new Emitter());\n        this.onKeyDown = this._onKeyDown.event;\n        this._onMouseDown = this._register(new Emitter());\n        this._onInput = this._register(new Emitter());\n        this._onKeyUp = this._register(new Emitter());\n        this._onPreserveCaseKeyDown = this._register(new Emitter());\n        this.onPreserveCaseKeyDown = this._onPreserveCaseKeyDown.event;\n        this.contextViewProvider = contextViewProvider;\n        this.placeholder = options.placeholder || '';\n        this.validation = options.validation;\n        this.label = options.label || NLS_DEFAULT_LABEL;\n        const appendPreserveCaseLabel = options.appendPreserveCaseLabel || '';\n        const history = options.history || [];\n        const flexibleHeight = !!options.flexibleHeight;\n        const flexibleWidth = !!options.flexibleWidth;\n        const flexibleMaxHeight = options.flexibleMaxHeight;\n        this.domNode = document.createElement('div');\n        this.domNode.classList.add('monaco-findInput');\n        this.inputBox = this._register(new HistoryInputBox(this.domNode, this.contextViewProvider, {\n            ariaLabel: this.label || '',\n            placeholder: this.placeholder || '',\n            validationOptions: {\n                validation: this.validation\n            },\n            history,\n            showHistoryHint: options.showHistoryHint,\n            flexibleHeight,\n            flexibleWidth,\n            flexibleMaxHeight,\n            inputBoxStyles: options.inputBoxStyles\n        }));\n        this.preserveCase = this._register(new PreserveCaseToggle({\n            appendTitle: appendPreserveCaseLabel,\n            isChecked: false,\n            ...options.toggleStyles\n        }));\n        this._register(this.preserveCase.onChange(viaKeyboard => {\n            this._onDidOptionChange.fire(viaKeyboard);\n            if (!viaKeyboard && this.fixFocusOnOptionClickEnabled) {\n                this.inputBox.focus();\n            }\n            this.validate();\n        }));\n        this._register(this.preserveCase.onKeyDown(e => {\n            this._onPreserveCaseKeyDown.fire(e);\n        }));\n        if (this._showOptionButtons) {\n            this.cachedOptionsWidth = this.preserveCase.width();\n        }\n        else {\n            this.cachedOptionsWidth = 0;\n        }\n        // Arrow-Key support to navigate between options\n        const indexes = [this.preserveCase.domNode];\n        this.onkeydown(this.domNode, (event) => {\n            if (event.equals(15 /* KeyCode.LeftArrow */) || event.equals(17 /* KeyCode.RightArrow */) || event.equals(9 /* KeyCode.Escape */)) {\n                const index = indexes.indexOf(this.domNode.ownerDocument.activeElement);\n                if (index >= 0) {\n                    let newIndex = -1;\n                    if (event.equals(17 /* KeyCode.RightArrow */)) {\n                        newIndex = (index + 1) % indexes.length;\n                    }\n                    else if (event.equals(15 /* KeyCode.LeftArrow */)) {\n                        if (index === 0) {\n                            newIndex = indexes.length - 1;\n                        }\n                        else {\n                            newIndex = index - 1;\n                        }\n                    }\n                    if (event.equals(9 /* KeyCode.Escape */)) {\n                        indexes[index].blur();\n                        this.inputBox.focus();\n                    }\n                    else if (newIndex >= 0) {\n                        indexes[newIndex].focus();\n                    }\n                    dom.EventHelper.stop(event, true);\n                }\n            }\n        });\n        const controls = document.createElement('div');\n        controls.className = 'controls';\n        controls.style.display = this._showOptionButtons ? 'block' : 'none';\n        controls.appendChild(this.preserveCase.domNode);\n        this.domNode.appendChild(controls);\n        parent?.appendChild(this.domNode);\n        this.onkeydown(this.inputBox.inputElement, (e) => this._onKeyDown.fire(e));\n        this.onkeyup(this.inputBox.inputElement, (e) => this._onKeyUp.fire(e));\n        this.oninput(this.inputBox.inputElement, (e) => this._onInput.fire());\n        this.onmousedown(this.inputBox.inputElement, (e) => this._onMouseDown.fire(e));\n    }\n    enable() {\n        this.domNode.classList.remove('disabled');\n        this.inputBox.enable();\n        this.preserveCase.enable();\n    }\n    disable() {\n        this.domNode.classList.add('disabled');\n        this.inputBox.disable();\n        this.preserveCase.disable();\n    }\n    setEnabled(enabled) {\n        if (enabled) {\n            this.enable();\n        }\n        else {\n            this.disable();\n        }\n    }\n    select() {\n        this.inputBox.select();\n    }\n    focus() {\n        this.inputBox.focus();\n    }\n    getPreserveCase() {\n        return this.preserveCase.checked;\n    }\n    setPreserveCase(value) {\n        this.preserveCase.checked = value;\n    }\n    focusOnPreserve() {\n        this.preserveCase.focus();\n    }\n    validate() {\n        this.inputBox?.validate();\n    }\n    set width(newWidth) {\n        this.inputBox.paddingRight = this.cachedOptionsWidth;\n        this.domNode.style.width = newWidth + 'px';\n    }\n    dispose() {\n        super.dispose();\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,KAAKA,GAAG,MAAM,cAAc;AACnC,SAASC,MAAM,QAAQ,qBAAqB;AAC5C,SAASC,eAAe,QAAQ,yBAAyB;AACzD,SAASC,MAAM,QAAQ,cAAc;AACrC,SAASC,OAAO,QAAQ,6BAA6B;AACrD,SAASC,OAAO,QAAQ,0BAA0B;AAClD,OAAO,iBAAiB;AACxB,OAAO,KAAKC,GAAG,MAAM,oBAAoB;AACzC,SAASC,uBAAuB,QAAQ,kCAAkC;AAC1E,MAAMC,iBAAiB,GAAGF,GAAG,CAACG,QAAQ,CAAC,cAAc,EAAE,OAAO,CAAC;AAC/D,MAAMC,uBAAuB,GAAGJ,GAAG,CAACG,QAAQ,CAAC,0BAA0B,EAAE,eAAe,CAAC;AACzF,MAAME,kBAAkB,SAASV,MAAM,CAAC;EACpCW,WAAWA,CAACC,IAAI,EAAE;IACd,KAAK,CAAC;MACF;MACAC,IAAI,EAAEV,OAAO,CAACW,YAAY;MAC1BC,KAAK,EAAEN,uBAAuB,GAAGG,IAAI,CAACI,WAAW;MACjDC,SAAS,EAAEL,IAAI,CAACK,SAAS;MACzBC,aAAa,EAAEN,IAAI,CAACM,aAAa,IAAIZ,uBAAuB,CAAC,SAAS,CAAC;MACvEa,uBAAuB,EAAEP,IAAI,CAACO,uBAAuB;MACrDC,2BAA2B,EAAER,IAAI,CAACQ,2BAA2B;MAC7DC,2BAA2B,EAAET,IAAI,CAACS;IACtC,CAAC,CAAC;EACN;AACJ;AACA,OAAO,MAAMC,YAAY,SAASpB,MAAM,CAAC;EACrCS,WAAWA,CAACY,MAAM,EAAEC,mBAAmB,EAAEC,kBAAkB,EAAEC,OAAO,EAAE;IAClE,KAAK,CAAC,CAAC;IACP,IAAI,CAACD,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACE,4BAA4B,GAAG,IAAI;IACxC,IAAI,CAACC,kBAAkB,GAAG,CAAC;IAC3B,IAAI,CAACC,kBAAkB,GAAG,IAAI,CAACC,SAAS,CAAC,IAAI1B,OAAO,CAAC,CAAC,CAAC;IACvD,IAAI,CAAC2B,iBAAiB,GAAG,IAAI,CAACF,kBAAkB,CAACG,KAAK;IACtD,IAAI,CAACC,UAAU,GAAG,IAAI,CAACH,SAAS,CAAC,IAAI1B,OAAO,CAAC,CAAC,CAAC;IAC/C,IAAI,CAAC8B,SAAS,GAAG,IAAI,CAACD,UAAU,CAACD,KAAK;IACtC,IAAI,CAACG,YAAY,GAAG,IAAI,CAACL,SAAS,CAAC,IAAI1B,OAAO,CAAC,CAAC,CAAC;IACjD,IAAI,CAACgC,QAAQ,GAAG,IAAI,CAACN,SAAS,CAAC,IAAI1B,OAAO,CAAC,CAAC,CAAC;IAC7C,IAAI,CAACiC,QAAQ,GAAG,IAAI,CAACP,SAAS,CAAC,IAAI1B,OAAO,CAAC,CAAC,CAAC;IAC7C,IAAI,CAACkC,sBAAsB,GAAG,IAAI,CAACR,SAAS,CAAC,IAAI1B,OAAO,CAAC,CAAC,CAAC;IAC3D,IAAI,CAACmC,qBAAqB,GAAG,IAAI,CAACD,sBAAsB,CAACN,KAAK;IAC9D,IAAI,CAACR,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACgB,WAAW,GAAGd,OAAO,CAACc,WAAW,IAAI,EAAE;IAC5C,IAAI,CAACC,UAAU,GAAGf,OAAO,CAACe,UAAU;IACpC,IAAI,CAACC,KAAK,GAAGhB,OAAO,CAACgB,KAAK,IAAInC,iBAAiB;IAC/C,MAAMoC,uBAAuB,GAAGjB,OAAO,CAACiB,uBAAuB,IAAI,EAAE;IACrE,MAAMC,OAAO,GAAGlB,OAAO,CAACkB,OAAO,IAAI,EAAE;IACrC,MAAMC,cAAc,GAAG,CAAC,CAACnB,OAAO,CAACmB,cAAc;IAC/C,MAAMC,aAAa,GAAG,CAAC,CAACpB,OAAO,CAACoB,aAAa;IAC7C,MAAMC,iBAAiB,GAAGrB,OAAO,CAACqB,iBAAiB;IACnD,IAAI,CAACC,OAAO,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC5C,IAAI,CAACF,OAAO,CAACG,SAAS,CAACC,GAAG,CAAC,kBAAkB,CAAC;IAC9C,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACvB,SAAS,CAAC,IAAI7B,eAAe,CAAC,IAAI,CAAC+C,OAAO,EAAE,IAAI,CAACxB,mBAAmB,EAAE;MACvF8B,SAAS,EAAE,IAAI,CAACZ,KAAK,IAAI,EAAE;MAC3BF,WAAW,EAAE,IAAI,CAACA,WAAW,IAAI,EAAE;MACnCe,iBAAiB,EAAE;QACfd,UAAU,EAAE,IAAI,CAACA;MACrB,CAAC;MACDG,OAAO;MACPY,eAAe,EAAE9B,OAAO,CAAC8B,eAAe;MACxCX,cAAc;MACdC,aAAa;MACbC,iBAAiB;MACjBU,cAAc,EAAE/B,OAAO,CAAC+B;IAC5B,CAAC,CAAC,CAAC;IACH,IAAI,CAAC3C,YAAY,GAAG,IAAI,CAACgB,SAAS,CAAC,IAAIpB,kBAAkB,CAAC;MACtDM,WAAW,EAAE2B,uBAAuB;MACpC1B,SAAS,EAAE,KAAK;MAChB,GAAGS,OAAO,CAACgC;IACf,CAAC,CAAC,CAAC;IACH,IAAI,CAAC5B,SAAS,CAAC,IAAI,CAAChB,YAAY,CAAC6C,QAAQ,CAACC,WAAW,IAAI;MACrD,IAAI,CAAC/B,kBAAkB,CAACgC,IAAI,CAACD,WAAW,CAAC;MACzC,IAAI,CAACA,WAAW,IAAI,IAAI,CAACjC,4BAA4B,EAAE;QACnD,IAAI,CAAC0B,QAAQ,CAACS,KAAK,CAAC,CAAC;MACzB;MACA,IAAI,CAACC,QAAQ,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC;IACH,IAAI,CAACjC,SAAS,CAAC,IAAI,CAAChB,YAAY,CAACoB,SAAS,CAAC8B,CAAC,IAAI;MAC5C,IAAI,CAAC1B,sBAAsB,CAACuB,IAAI,CAACG,CAAC,CAAC;IACvC,CAAC,CAAC,CAAC;IACH,IAAI,IAAI,CAACvC,kBAAkB,EAAE;MACzB,IAAI,CAACG,kBAAkB,GAAG,IAAI,CAACd,YAAY,CAACmD,KAAK,CAAC,CAAC;IACvD,CAAC,MACI;MACD,IAAI,CAACrC,kBAAkB,GAAG,CAAC;IAC/B;IACA;IACA,MAAMsC,OAAO,GAAG,CAAC,IAAI,CAACpD,YAAY,CAACkC,OAAO,CAAC;IAC3C,IAAI,CAACmB,SAAS,CAAC,IAAI,CAACnB,OAAO,EAAGhB,KAAK,IAAK;MACpC,IAAIA,KAAK,CAACoC,MAAM,CAAC,EAAE,CAAC,uBAAuB,CAAC,IAAIpC,KAAK,CAACoC,MAAM,CAAC,EAAE,CAAC,wBAAwB,CAAC,IAAIpC,KAAK,CAACoC,MAAM,CAAC,CAAC,CAAC,oBAAoB,CAAC,EAAE;QAC/H,MAAMC,KAAK,GAAGH,OAAO,CAACI,OAAO,CAAC,IAAI,CAACtB,OAAO,CAACuB,aAAa,CAACC,aAAa,CAAC;QACvE,IAAIH,KAAK,IAAI,CAAC,EAAE;UACZ,IAAII,QAAQ,GAAG,CAAC,CAAC;UACjB,IAAIzC,KAAK,CAACoC,MAAM,CAAC,EAAE,CAAC,wBAAwB,CAAC,EAAE;YAC3CK,QAAQ,GAAG,CAACJ,KAAK,GAAG,CAAC,IAAIH,OAAO,CAACQ,MAAM;UAC3C,CAAC,MACI,IAAI1C,KAAK,CAACoC,MAAM,CAAC,EAAE,CAAC,uBAAuB,CAAC,EAAE;YAC/C,IAAIC,KAAK,KAAK,CAAC,EAAE;cACbI,QAAQ,GAAGP,OAAO,CAACQ,MAAM,GAAG,CAAC;YACjC,CAAC,MACI;cACDD,QAAQ,GAAGJ,KAAK,GAAG,CAAC;YACxB;UACJ;UACA,IAAIrC,KAAK,CAACoC,MAAM,CAAC,CAAC,CAAC,oBAAoB,CAAC,EAAE;YACtCF,OAAO,CAACG,KAAK,CAAC,CAACM,IAAI,CAAC,CAAC;YACrB,IAAI,CAACtB,QAAQ,CAACS,KAAK,CAAC,CAAC;UACzB,CAAC,MACI,IAAIW,QAAQ,IAAI,CAAC,EAAE;YACpBP,OAAO,CAACO,QAAQ,CAAC,CAACX,KAAK,CAAC,CAAC;UAC7B;UACA/D,GAAG,CAAC6E,WAAW,CAACC,IAAI,CAAC7C,KAAK,EAAE,IAAI,CAAC;QACrC;MACJ;IACJ,CAAC,CAAC;IACF,MAAM8C,QAAQ,GAAG7B,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC9C4B,QAAQ,CAACC,SAAS,GAAG,UAAU;IAC/BD,QAAQ,CAACE,KAAK,CAACC,OAAO,GAAG,IAAI,CAACxD,kBAAkB,GAAG,OAAO,GAAG,MAAM;IACnEqD,QAAQ,CAACI,WAAW,CAAC,IAAI,CAACpE,YAAY,CAACkC,OAAO,CAAC;IAC/C,IAAI,CAACA,OAAO,CAACkC,WAAW,CAACJ,QAAQ,CAAC;IAClCvD,MAAM,EAAE2D,WAAW,CAAC,IAAI,CAAClC,OAAO,CAAC;IACjC,IAAI,CAACmB,SAAS,CAAC,IAAI,CAACd,QAAQ,CAAC8B,YAAY,EAAGnB,CAAC,IAAK,IAAI,CAAC/B,UAAU,CAAC4B,IAAI,CAACG,CAAC,CAAC,CAAC;IAC1E,IAAI,CAACoB,OAAO,CAAC,IAAI,CAAC/B,QAAQ,CAAC8B,YAAY,EAAGnB,CAAC,IAAK,IAAI,CAAC3B,QAAQ,CAACwB,IAAI,CAACG,CAAC,CAAC,CAAC;IACtE,IAAI,CAACqB,OAAO,CAAC,IAAI,CAAChC,QAAQ,CAAC8B,YAAY,EAAGnB,CAAC,IAAK,IAAI,CAAC5B,QAAQ,CAACyB,IAAI,CAAC,CAAC,CAAC;IACrE,IAAI,CAACyB,WAAW,CAAC,IAAI,CAACjC,QAAQ,CAAC8B,YAAY,EAAGnB,CAAC,IAAK,IAAI,CAAC7B,YAAY,CAAC0B,IAAI,CAACG,CAAC,CAAC,CAAC;EAClF;EACAuB,MAAMA,CAAA,EAAG;IACL,IAAI,CAACvC,OAAO,CAACG,SAAS,CAACqC,MAAM,CAAC,UAAU,CAAC;IACzC,IAAI,CAACnC,QAAQ,CAACkC,MAAM,CAAC,CAAC;IACtB,IAAI,CAACzE,YAAY,CAACyE,MAAM,CAAC,CAAC;EAC9B;EACAE,OAAOA,CAAA,EAAG;IACN,IAAI,CAACzC,OAAO,CAACG,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC;IACtC,IAAI,CAACC,QAAQ,CAACoC,OAAO,CAAC,CAAC;IACvB,IAAI,CAAC3E,YAAY,CAAC2E,OAAO,CAAC,CAAC;EAC/B;EACAC,UAAUA,CAACC,OAAO,EAAE;IAChB,IAAIA,OAAO,EAAE;MACT,IAAI,CAACJ,MAAM,CAAC,CAAC;IACjB,CAAC,MACI;MACD,IAAI,CAACE,OAAO,CAAC,CAAC;IAClB;EACJ;EACAG,MAAMA,CAAA,EAAG;IACL,IAAI,CAACvC,QAAQ,CAACuC,MAAM,CAAC,CAAC;EAC1B;EACA9B,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACT,QAAQ,CAACS,KAAK,CAAC,CAAC;EACzB;EACA+B,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAAC/E,YAAY,CAACgF,OAAO;EACpC;EACAC,eAAeA,CAACC,KAAK,EAAE;IACnB,IAAI,CAAClF,YAAY,CAACgF,OAAO,GAAGE,KAAK;EACrC;EACAC,eAAeA,CAAA,EAAG;IACd,IAAI,CAACnF,YAAY,CAACgD,KAAK,CAAC,CAAC;EAC7B;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACV,QAAQ,EAAEU,QAAQ,CAAC,CAAC;EAC7B;EACA,IAAIE,KAAKA,CAACiC,QAAQ,EAAE;IAChB,IAAI,CAAC7C,QAAQ,CAAC8C,YAAY,GAAG,IAAI,CAACvE,kBAAkB;IACpD,IAAI,CAACoB,OAAO,CAACgC,KAAK,CAACf,KAAK,GAAGiC,QAAQ,GAAG,IAAI;EAC9C;EACAE,OAAOA,CAAA,EAAG;IACN,KAAK,CAACA,OAAO,CAAC,CAAC;EACnB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}