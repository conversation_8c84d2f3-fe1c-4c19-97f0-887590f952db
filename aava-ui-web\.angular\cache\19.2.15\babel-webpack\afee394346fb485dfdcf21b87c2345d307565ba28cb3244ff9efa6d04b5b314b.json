{"ast": null, "code": "import _asyncToGenerator from \"C:/console/aava-ui-web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport _asyncIterator from \"C:/console/aava-ui-web/node_modules/@babel/runtime/helpers/esm/asyncIterator.js\";\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { createCancelableAsyncIterable, RunOnceScheduler } from '../../../../base/common/async.js';\nimport { onUnexpectedError } from '../../../../base/common/errors.js';\nimport { Emitter } from '../../../../base/common/event.js';\nimport { Disposable } from '../../../../base/common/lifecycle.js';\nexport class HoverResult {\n  constructor(value, isComplete, hasLoadingMessage) {\n    this.value = value;\n    this.isComplete = isComplete;\n    this.hasLoadingMessage = hasLoadingMessage;\n  }\n}\n/**\n * Computing the hover is very fine tuned.\n *\n * Suppose the hover delay is 300ms (the default). Then, when resting the mouse at an anchor:\n * - at 150ms, the async computation is triggered (i.e. semantic hover)\n *   - if async results already come in, they are not rendered yet.\n * - at 300ms, the sync computation is triggered (i.e. decorations, markers)\n *   - if there are sync or async results, they are rendered.\n * - at 900ms, if the async computation hasn't finished, a \"Loading...\" result is added.\n */\nexport class HoverOperation extends Disposable {\n  constructor(_editor, _computer) {\n    super();\n    this._editor = _editor;\n    this._computer = _computer;\n    this._onResult = this._register(new Emitter());\n    this.onResult = this._onResult.event;\n    this._firstWaitScheduler = this._register(new RunOnceScheduler(() => this._triggerAsyncComputation(), 0));\n    this._secondWaitScheduler = this._register(new RunOnceScheduler(() => this._triggerSyncComputation(), 0));\n    this._loadingMessageScheduler = this._register(new RunOnceScheduler(() => this._triggerLoadingMessage(), 0));\n    this._state = 0 /* HoverOperationState.Idle */;\n    this._asyncIterable = null;\n    this._asyncIterableDone = false;\n    this._result = [];\n  }\n  dispose() {\n    if (this._asyncIterable) {\n      this._asyncIterable.cancel();\n      this._asyncIterable = null;\n    }\n    super.dispose();\n  }\n  get _hoverTime() {\n    return this._editor.getOption(60 /* EditorOption.hover */).delay;\n  }\n  get _firstWaitTime() {\n    return this._hoverTime / 2;\n  }\n  get _secondWaitTime() {\n    return this._hoverTime - this._firstWaitTime;\n  }\n  get _loadingMessageTime() {\n    return 3 * this._hoverTime;\n  }\n  _setState(state, fireResult = true) {\n    this._state = state;\n    if (fireResult) {\n      this._fireResult();\n    }\n  }\n  _triggerAsyncComputation() {\n    var _this = this;\n    this._setState(2 /* HoverOperationState.SecondWait */);\n    this._secondWaitScheduler.schedule(this._secondWaitTime);\n    if (this._computer.computeAsync) {\n      this._asyncIterableDone = false;\n      this._asyncIterable = createCancelableAsyncIterable(token => this._computer.computeAsync(token));\n      _asyncToGenerator(function* () {\n        try {\n          var _iteratorAbruptCompletion = false;\n          var _didIteratorError = false;\n          var _iteratorError;\n          try {\n            for (var _iterator = _asyncIterator(_this._asyncIterable), _step; _iteratorAbruptCompletion = !(_step = yield _iterator.next()).done; _iteratorAbruptCompletion = false) {\n              const item = _step.value;\n              {\n                if (item) {\n                  _this._result.push(item);\n                  _this._fireResult();\n                }\n              }\n            }\n          } catch (err) {\n            _didIteratorError = true;\n            _iteratorError = err;\n          } finally {\n            try {\n              if (_iteratorAbruptCompletion && _iterator.return != null) {\n                yield _iterator.return();\n              }\n            } finally {\n              if (_didIteratorError) {\n                throw _iteratorError;\n              }\n            }\n          }\n          _this._asyncIterableDone = true;\n          if (_this._state === 3 /* HoverOperationState.WaitingForAsync */ || _this._state === 4 /* HoverOperationState.WaitingForAsyncShowingLoading */) {\n            _this._setState(0 /* HoverOperationState.Idle */);\n          }\n        } catch (e) {\n          onUnexpectedError(e);\n        }\n      })();\n    } else {\n      this._asyncIterableDone = true;\n    }\n  }\n  _triggerSyncComputation() {\n    if (this._computer.computeSync) {\n      this._result = this._result.concat(this._computer.computeSync());\n    }\n    this._setState(this._asyncIterableDone ? 0 /* HoverOperationState.Idle */ : 3 /* HoverOperationState.WaitingForAsync */);\n  }\n  _triggerLoadingMessage() {\n    if (this._state === 3 /* HoverOperationState.WaitingForAsync */) {\n      this._setState(4 /* HoverOperationState.WaitingForAsyncShowingLoading */);\n    }\n  }\n  _fireResult() {\n    if (this._state === 1 /* HoverOperationState.FirstWait */ || this._state === 2 /* HoverOperationState.SecondWait */) {\n      // Do not send out results before the hover time\n      return;\n    }\n    const isComplete = this._state === 0 /* HoverOperationState.Idle */;\n    const hasLoadingMessage = this._state === 4 /* HoverOperationState.WaitingForAsyncShowingLoading */;\n    this._onResult.fire(new HoverResult(this._result.slice(0), isComplete, hasLoadingMessage));\n  }\n  start(mode) {\n    if (mode === 0 /* HoverStartMode.Delayed */) {\n      if (this._state === 0 /* HoverOperationState.Idle */) {\n        this._setState(1 /* HoverOperationState.FirstWait */);\n        this._firstWaitScheduler.schedule(this._firstWaitTime);\n        this._loadingMessageScheduler.schedule(this._loadingMessageTime);\n      }\n    } else {\n      switch (this._state) {\n        case 0 /* HoverOperationState.Idle */:\n          this._triggerAsyncComputation();\n          this._secondWaitScheduler.cancel();\n          this._triggerSyncComputation();\n          break;\n        case 2 /* HoverOperationState.SecondWait */:\n          this._secondWaitScheduler.cancel();\n          this._triggerSyncComputation();\n          break;\n      }\n    }\n  }\n  cancel() {\n    this._firstWaitScheduler.cancel();\n    this._secondWaitScheduler.cancel();\n    this._loadingMessageScheduler.cancel();\n    if (this._asyncIterable) {\n      this._asyncIterable.cancel();\n      this._asyncIterable = null;\n    }\n    this._result = [];\n    this._setState(0 /* HoverOperationState.Idle */, false);\n  }\n}", "map": {"version": 3, "names": ["createCancelableAsyncIterable", "RunOnceScheduler", "onUnexpectedError", "Emitter", "Disposable", "HoverResult", "constructor", "value", "isComplete", "hasLoadingMessage", "HoverOperation", "_editor", "_computer", "_onResult", "_register", "onResult", "event", "_firstWaitScheduler", "_triggerAsyncComputation", "_secondWaitScheduler", "_triggerSyncComputation", "_loadingMessageScheduler", "_triggerLoadingMessage", "_state", "_asyncIterable", "_asyncIterableDone", "_result", "dispose", "cancel", "_hoverTime", "getOption", "delay", "_firstWaitTime", "_secondWaitTime", "_loadingMessageTime", "_setState", "state", "fireResult", "_fireResult", "_this", "schedule", "computeAsync", "token", "_asyncToGenerator", "_iteratorAbruptCompletion", "_didIteratorError", "_iteratorError", "_iterator", "_asyncIterator", "_step", "next", "done", "item", "push", "err", "return", "e", "computeSync", "concat", "fire", "slice", "start", "mode"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/contrib/hover/browser/hoverOperation.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { createCancelableAsyncIterable, RunOnceScheduler } from '../../../../base/common/async.js';\nimport { onUnexpectedError } from '../../../../base/common/errors.js';\nimport { Emitter } from '../../../../base/common/event.js';\nimport { Disposable } from '../../../../base/common/lifecycle.js';\nexport class HoverResult {\n    constructor(value, isComplete, hasLoadingMessage) {\n        this.value = value;\n        this.isComplete = isComplete;\n        this.hasLoadingMessage = hasLoadingMessage;\n    }\n}\n/**\n * Computing the hover is very fine tuned.\n *\n * Suppose the hover delay is 300ms (the default). Then, when resting the mouse at an anchor:\n * - at 150ms, the async computation is triggered (i.e. semantic hover)\n *   - if async results already come in, they are not rendered yet.\n * - at 300ms, the sync computation is triggered (i.e. decorations, markers)\n *   - if there are sync or async results, they are rendered.\n * - at 900ms, if the async computation hasn't finished, a \"Loading...\" result is added.\n */\nexport class HoverOperation extends Disposable {\n    constructor(_editor, _computer) {\n        super();\n        this._editor = _editor;\n        this._computer = _computer;\n        this._onResult = this._register(new Emitter());\n        this.onResult = this._onResult.event;\n        this._firstWaitScheduler = this._register(new RunOnceScheduler(() => this._triggerAsyncComputation(), 0));\n        this._secondWaitScheduler = this._register(new RunOnceScheduler(() => this._triggerSyncComputation(), 0));\n        this._loadingMessageScheduler = this._register(new RunOnceScheduler(() => this._triggerLoadingMessage(), 0));\n        this._state = 0 /* HoverOperationState.Idle */;\n        this._asyncIterable = null;\n        this._asyncIterableDone = false;\n        this._result = [];\n    }\n    dispose() {\n        if (this._asyncIterable) {\n            this._asyncIterable.cancel();\n            this._asyncIterable = null;\n        }\n        super.dispose();\n    }\n    get _hoverTime() {\n        return this._editor.getOption(60 /* EditorOption.hover */).delay;\n    }\n    get _firstWaitTime() {\n        return this._hoverTime / 2;\n    }\n    get _secondWaitTime() {\n        return this._hoverTime - this._firstWaitTime;\n    }\n    get _loadingMessageTime() {\n        return 3 * this._hoverTime;\n    }\n    _setState(state, fireResult = true) {\n        this._state = state;\n        if (fireResult) {\n            this._fireResult();\n        }\n    }\n    _triggerAsyncComputation() {\n        this._setState(2 /* HoverOperationState.SecondWait */);\n        this._secondWaitScheduler.schedule(this._secondWaitTime);\n        if (this._computer.computeAsync) {\n            this._asyncIterableDone = false;\n            this._asyncIterable = createCancelableAsyncIterable(token => this._computer.computeAsync(token));\n            (async () => {\n                try {\n                    for await (const item of this._asyncIterable) {\n                        if (item) {\n                            this._result.push(item);\n                            this._fireResult();\n                        }\n                    }\n                    this._asyncIterableDone = true;\n                    if (this._state === 3 /* HoverOperationState.WaitingForAsync */ || this._state === 4 /* HoverOperationState.WaitingForAsyncShowingLoading */) {\n                        this._setState(0 /* HoverOperationState.Idle */);\n                    }\n                }\n                catch (e) {\n                    onUnexpectedError(e);\n                }\n            })();\n        }\n        else {\n            this._asyncIterableDone = true;\n        }\n    }\n    _triggerSyncComputation() {\n        if (this._computer.computeSync) {\n            this._result = this._result.concat(this._computer.computeSync());\n        }\n        this._setState(this._asyncIterableDone ? 0 /* HoverOperationState.Idle */ : 3 /* HoverOperationState.WaitingForAsync */);\n    }\n    _triggerLoadingMessage() {\n        if (this._state === 3 /* HoverOperationState.WaitingForAsync */) {\n            this._setState(4 /* HoverOperationState.WaitingForAsyncShowingLoading */);\n        }\n    }\n    _fireResult() {\n        if (this._state === 1 /* HoverOperationState.FirstWait */ || this._state === 2 /* HoverOperationState.SecondWait */) {\n            // Do not send out results before the hover time\n            return;\n        }\n        const isComplete = (this._state === 0 /* HoverOperationState.Idle */);\n        const hasLoadingMessage = (this._state === 4 /* HoverOperationState.WaitingForAsyncShowingLoading */);\n        this._onResult.fire(new HoverResult(this._result.slice(0), isComplete, hasLoadingMessage));\n    }\n    start(mode) {\n        if (mode === 0 /* HoverStartMode.Delayed */) {\n            if (this._state === 0 /* HoverOperationState.Idle */) {\n                this._setState(1 /* HoverOperationState.FirstWait */);\n                this._firstWaitScheduler.schedule(this._firstWaitTime);\n                this._loadingMessageScheduler.schedule(this._loadingMessageTime);\n            }\n        }\n        else {\n            switch (this._state) {\n                case 0 /* HoverOperationState.Idle */:\n                    this._triggerAsyncComputation();\n                    this._secondWaitScheduler.cancel();\n                    this._triggerSyncComputation();\n                    break;\n                case 2 /* HoverOperationState.SecondWait */:\n                    this._secondWaitScheduler.cancel();\n                    this._triggerSyncComputation();\n                    break;\n            }\n        }\n    }\n    cancel() {\n        this._firstWaitScheduler.cancel();\n        this._secondWaitScheduler.cancel();\n        this._loadingMessageScheduler.cancel();\n        if (this._asyncIterable) {\n            this._asyncIterable.cancel();\n            this._asyncIterable = null;\n        }\n        this._result = [];\n        this._setState(0 /* HoverOperationState.Idle */, false);\n    }\n}\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA,SAASA,6BAA6B,EAAEC,gBAAgB,QAAQ,kCAAkC;AAClG,SAASC,iBAAiB,QAAQ,mCAAmC;AACrE,SAASC,OAAO,QAAQ,kCAAkC;AAC1D,SAASC,UAAU,QAAQ,sCAAsC;AACjE,OAAO,MAAMC,WAAW,CAAC;EACrBC,WAAWA,CAACC,KAAK,EAAEC,UAAU,EAAEC,iBAAiB,EAAE;IAC9C,IAAI,CAACF,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;EAC9C;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,cAAc,SAASN,UAAU,CAAC;EAC3CE,WAAWA,CAACK,OAAO,EAAEC,SAAS,EAAE;IAC5B,KAAK,CAAC,CAAC;IACP,IAAI,CAACD,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,SAAS,GAAG,IAAI,CAACC,SAAS,CAAC,IAAIX,OAAO,CAAC,CAAC,CAAC;IAC9C,IAAI,CAACY,QAAQ,GAAG,IAAI,CAACF,SAAS,CAACG,KAAK;IACpC,IAAI,CAACC,mBAAmB,GAAG,IAAI,CAACH,SAAS,CAAC,IAAIb,gBAAgB,CAAC,MAAM,IAAI,CAACiB,wBAAwB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACzG,IAAI,CAACC,oBAAoB,GAAG,IAAI,CAACL,SAAS,CAAC,IAAIb,gBAAgB,CAAC,MAAM,IAAI,CAACmB,uBAAuB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACzG,IAAI,CAACC,wBAAwB,GAAG,IAAI,CAACP,SAAS,CAAC,IAAIb,gBAAgB,CAAC,MAAM,IAAI,CAACqB,sBAAsB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC5G,IAAI,CAACC,MAAM,GAAG,CAAC,CAAC;IAChB,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACC,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACC,OAAO,GAAG,EAAE;EACrB;EACAC,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACH,cAAc,EAAE;MACrB,IAAI,CAACA,cAAc,CAACI,MAAM,CAAC,CAAC;MAC5B,IAAI,CAACJ,cAAc,GAAG,IAAI;IAC9B;IACA,KAAK,CAACG,OAAO,CAAC,CAAC;EACnB;EACA,IAAIE,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAAClB,OAAO,CAACmB,SAAS,CAAC,EAAE,CAAC,wBAAwB,CAAC,CAACC,KAAK;EACpE;EACA,IAAIC,cAAcA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACH,UAAU,GAAG,CAAC;EAC9B;EACA,IAAII,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACJ,UAAU,GAAG,IAAI,CAACG,cAAc;EAChD;EACA,IAAIE,mBAAmBA,CAAA,EAAG;IACtB,OAAO,CAAC,GAAG,IAAI,CAACL,UAAU;EAC9B;EACAM,SAASA,CAACC,KAAK,EAAEC,UAAU,GAAG,IAAI,EAAE;IAChC,IAAI,CAACd,MAAM,GAAGa,KAAK;IACnB,IAAIC,UAAU,EAAE;MACZ,IAAI,CAACC,WAAW,CAAC,CAAC;IACtB;EACJ;EACApB,wBAAwBA,CAAA,EAAG;IAAA,IAAAqB,KAAA;IACvB,IAAI,CAACJ,SAAS,CAAC,CAAC,CAAC,oCAAoC,CAAC;IACtD,IAAI,CAAChB,oBAAoB,CAACqB,QAAQ,CAAC,IAAI,CAACP,eAAe,CAAC;IACxD,IAAI,IAAI,CAACrB,SAAS,CAAC6B,YAAY,EAAE;MAC7B,IAAI,CAAChB,kBAAkB,GAAG,KAAK;MAC/B,IAAI,CAACD,cAAc,GAAGxB,6BAA6B,CAAC0C,KAAK,IAAI,IAAI,CAAC9B,SAAS,CAAC6B,YAAY,CAACC,KAAK,CAAC,CAAC;MAChGC,iBAAA,CAAC,aAAY;QACT,IAAI;UAAA,IAAAC,yBAAA;UAAA,IAAAC,iBAAA;UAAA,IAAAC,cAAA;UAAA;YACA,SAAAC,SAAA,GAAAC,cAAA,CAAyBT,KAAI,CAACf,cAAc,GAAAyB,KAAA,EAAAL,yBAAA,KAAAK,KAAA,SAAAF,SAAA,CAAAG,IAAA,IAAAC,IAAA,EAAAP,yBAAA,UAAE;cAAA,MAA7BQ,IAAI,GAAAH,KAAA,CAAA1C,KAAA;cAAA;gBACjB,IAAI6C,IAAI,EAAE;kBACNb,KAAI,CAACb,OAAO,CAAC2B,IAAI,CAACD,IAAI,CAAC;kBACvBb,KAAI,CAACD,WAAW,CAAC,CAAC;gBACtB;cAAC;YACL;UAAC,SAAAgB,GAAA;YAAAT,iBAAA;YAAAC,cAAA,GAAAQ,GAAA;UAAA;YAAA;cAAA,IAAAV,yBAAA,IAAAG,SAAA,CAAAQ,MAAA;gBAAA,MAAAR,SAAA,CAAAQ,MAAA;cAAA;YAAA;cAAA,IAAAV,iBAAA;gBAAA,MAAAC,cAAA;cAAA;YAAA;UAAA;UACDP,KAAI,CAACd,kBAAkB,GAAG,IAAI;UAC9B,IAAIc,KAAI,CAAChB,MAAM,KAAK,CAAC,CAAC,6CAA6CgB,KAAI,CAAChB,MAAM,KAAK,CAAC,CAAC,yDAAyD;YAC1IgB,KAAI,CAACJ,SAAS,CAAC,CAAC,CAAC,8BAA8B,CAAC;UACpD;QACJ,CAAC,CACD,OAAOqB,CAAC,EAAE;UACNtD,iBAAiB,CAACsD,CAAC,CAAC;QACxB;MACJ,CAAC,EAAE,CAAC;IACR,CAAC,MACI;MACD,IAAI,CAAC/B,kBAAkB,GAAG,IAAI;IAClC;EACJ;EACAL,uBAAuBA,CAAA,EAAG;IACtB,IAAI,IAAI,CAACR,SAAS,CAAC6C,WAAW,EAAE;MAC5B,IAAI,CAAC/B,OAAO,GAAG,IAAI,CAACA,OAAO,CAACgC,MAAM,CAAC,IAAI,CAAC9C,SAAS,CAAC6C,WAAW,CAAC,CAAC,CAAC;IACpE;IACA,IAAI,CAACtB,SAAS,CAAC,IAAI,CAACV,kBAAkB,GAAG,CAAC,CAAC,iCAAiC,CAAC,CAAC,yCAAyC,CAAC;EAC5H;EACAH,sBAAsBA,CAAA,EAAG;IACrB,IAAI,IAAI,CAACC,MAAM,KAAK,CAAC,CAAC,2CAA2C;MAC7D,IAAI,CAACY,SAAS,CAAC,CAAC,CAAC,uDAAuD,CAAC;IAC7E;EACJ;EACAG,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACf,MAAM,KAAK,CAAC,CAAC,uCAAuC,IAAI,CAACA,MAAM,KAAK,CAAC,CAAC,sCAAsC;MACjH;MACA;IACJ;IACA,MAAMf,UAAU,GAAI,IAAI,CAACe,MAAM,KAAK,CAAC,CAAC,8BAA+B;IACrE,MAAMd,iBAAiB,GAAI,IAAI,CAACc,MAAM,KAAK,CAAC,CAAC,uDAAwD;IACrG,IAAI,CAACV,SAAS,CAAC8C,IAAI,CAAC,IAAItD,WAAW,CAAC,IAAI,CAACqB,OAAO,CAACkC,KAAK,CAAC,CAAC,CAAC,EAAEpD,UAAU,EAAEC,iBAAiB,CAAC,CAAC;EAC9F;EACAoD,KAAKA,CAACC,IAAI,EAAE;IACR,IAAIA,IAAI,KAAK,CAAC,CAAC,8BAA8B;MACzC,IAAI,IAAI,CAACvC,MAAM,KAAK,CAAC,CAAC,gCAAgC;QAClD,IAAI,CAACY,SAAS,CAAC,CAAC,CAAC,mCAAmC,CAAC;QACrD,IAAI,CAAClB,mBAAmB,CAACuB,QAAQ,CAAC,IAAI,CAACR,cAAc,CAAC;QACtD,IAAI,CAACX,wBAAwB,CAACmB,QAAQ,CAAC,IAAI,CAACN,mBAAmB,CAAC;MACpE;IACJ,CAAC,MACI;MACD,QAAQ,IAAI,CAACX,MAAM;QACf,KAAK,CAAC,CAAC;UACH,IAAI,CAACL,wBAAwB,CAAC,CAAC;UAC/B,IAAI,CAACC,oBAAoB,CAACS,MAAM,CAAC,CAAC;UAClC,IAAI,CAACR,uBAAuB,CAAC,CAAC;UAC9B;QACJ,KAAK,CAAC,CAAC;UACH,IAAI,CAACD,oBAAoB,CAACS,MAAM,CAAC,CAAC;UAClC,IAAI,CAACR,uBAAuB,CAAC,CAAC;UAC9B;MACR;IACJ;EACJ;EACAQ,MAAMA,CAAA,EAAG;IACL,IAAI,CAACX,mBAAmB,CAACW,MAAM,CAAC,CAAC;IACjC,IAAI,CAACT,oBAAoB,CAACS,MAAM,CAAC,CAAC;IAClC,IAAI,CAACP,wBAAwB,CAACO,MAAM,CAAC,CAAC;IACtC,IAAI,IAAI,CAACJ,cAAc,EAAE;MACrB,IAAI,CAACA,cAAc,CAACI,MAAM,CAAC,CAAC;MAC5B,IAAI,CAACJ,cAAc,GAAG,IAAI;IAC9B;IACA,IAAI,CAACE,OAAO,GAAG,EAAE;IACjB,IAAI,CAACS,SAAS,CAAC,CAAC,CAAC,gCAAgC,KAAK,CAAC;EAC3D;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}