{"ast": null, "code": "import _asyncToGenerator from \"C:/console/aava-ui-web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { $, append, clearNode, createStyleSheet, getWindow, h, hasParentWithClass, asCssValueWithDefault, is<PERSON>eyboardEvent, addDisposableListener } from '../../dom.js';\nimport { DomEmitter } from '../../event.js';\nimport { StandardKeyboardEvent } from '../../keyboardEvent.js';\nimport { ActionBar } from '../actionbar/actionbar.js';\nimport { FindInput } from '../findinput/findInput.js';\nimport { unthemedInboxStyles } from '../inputbox/inputBox.js';\nimport { ElementsDragAndDropData } from '../list/listView.js';\nimport { isActionItem, isButton, isInputElement, isMonacoCustomToggle, isMonacoEditor, isStickyScrollContainer, isStickyScrollElement, List, MouseController } from '../list/listWidget.js';\nimport { Toggle, unthemedToggleStyles } from '../toggle/toggle.js';\nimport { getVisibleState, isFilterResult } from './indexTreeModel.js';\nimport { TreeMouseEventTarget } from './tree.js';\nimport { Action } from '../../../common/actions.js';\nimport { distinct, equals, range } from '../../../common/arrays.js';\nimport { Delayer, disposableTimeout, timeout } from '../../../common/async.js';\nimport { Codicon } from '../../../common/codicons.js';\nimport { ThemeIcon } from '../../../common/themables.js';\nimport { SetMap } from '../../../common/map.js';\nimport { Emitter, Event, EventBufferer, Relay } from '../../../common/event.js';\nimport { fuzzyScore, FuzzyScore } from '../../../common/filters.js';\nimport { Disposable, DisposableStore, dispose, toDisposable } from '../../../common/lifecycle.js';\nimport { clamp } from '../../../common/numbers.js';\nimport { isNumber } from '../../../common/types.js';\nimport './media/tree.css';\nimport { localize } from '../../../../nls.js';\nimport { createInstantHoverDelegate, getDefaultHoverDelegate } from '../hover/hoverDelegateFactory.js';\nimport { autorun, constObservable } from '../../../common/observable.js';\nimport { alert } from '../aria/aria.js';\nclass TreeElementsDragAndDropData extends ElementsDragAndDropData {\n  constructor(data) {\n    super(data.elements.map(node => node.element));\n    this.data = data;\n  }\n}\nfunction asTreeDragAndDropData(data) {\n  if (data instanceof ElementsDragAndDropData) {\n    return new TreeElementsDragAndDropData(data);\n  }\n  return data;\n}\nclass TreeNodeListDragAndDrop {\n  constructor(modelProvider, dnd) {\n    this.modelProvider = modelProvider;\n    this.dnd = dnd;\n    this.autoExpandDisposable = Disposable.None;\n    this.disposables = new DisposableStore();\n  }\n  getDragURI(node) {\n    return this.dnd.getDragURI(node.element);\n  }\n  getDragLabel(nodes, originalEvent) {\n    if (this.dnd.getDragLabel) {\n      return this.dnd.getDragLabel(nodes.map(node => node.element), originalEvent);\n    }\n    return undefined;\n  }\n  onDragStart(data, originalEvent) {\n    this.dnd.onDragStart?.(asTreeDragAndDropData(data), originalEvent);\n  }\n  onDragOver(data, targetNode, targetIndex, targetSector, originalEvent, raw = true) {\n    const result = this.dnd.onDragOver(asTreeDragAndDropData(data), targetNode && targetNode.element, targetIndex, targetSector, originalEvent);\n    const didChangeAutoExpandNode = this.autoExpandNode !== targetNode;\n    if (didChangeAutoExpandNode) {\n      this.autoExpandDisposable.dispose();\n      this.autoExpandNode = targetNode;\n    }\n    if (typeof targetNode === 'undefined') {\n      return result;\n    }\n    if (didChangeAutoExpandNode && typeof result !== 'boolean' && result.autoExpand) {\n      this.autoExpandDisposable = disposableTimeout(() => {\n        const model = this.modelProvider();\n        const ref = model.getNodeLocation(targetNode);\n        if (model.isCollapsed(ref)) {\n          model.setCollapsed(ref, false);\n        }\n        this.autoExpandNode = undefined;\n      }, 500, this.disposables);\n    }\n    if (typeof result === 'boolean' || !result.accept || typeof result.bubble === 'undefined' || result.feedback) {\n      if (!raw) {\n        const accept = typeof result === 'boolean' ? result : result.accept;\n        const effect = typeof result === 'boolean' ? undefined : result.effect;\n        return {\n          accept,\n          effect,\n          feedback: [targetIndex]\n        };\n      }\n      return result;\n    }\n    if (result.bubble === 1 /* TreeDragOverBubble.Up */) {\n      const model = this.modelProvider();\n      const ref = model.getNodeLocation(targetNode);\n      const parentRef = model.getParentNodeLocation(ref);\n      const parentNode = model.getNode(parentRef);\n      const parentIndex = parentRef && model.getListIndex(parentRef);\n      return this.onDragOver(data, parentNode, parentIndex, targetSector, originalEvent, false);\n    }\n    const model = this.modelProvider();\n    const ref = model.getNodeLocation(targetNode);\n    const start = model.getListIndex(ref);\n    const length = model.getListRenderCount(ref);\n    return {\n      ...result,\n      feedback: range(start, start + length)\n    };\n  }\n  drop(data, targetNode, targetIndex, targetSector, originalEvent) {\n    this.autoExpandDisposable.dispose();\n    this.autoExpandNode = undefined;\n    this.dnd.drop(asTreeDragAndDropData(data), targetNode && targetNode.element, targetIndex, targetSector, originalEvent);\n  }\n  onDragEnd(originalEvent) {\n    this.dnd.onDragEnd?.(originalEvent);\n  }\n  dispose() {\n    this.disposables.dispose();\n    this.dnd.dispose();\n  }\n}\nfunction asListOptions(modelProvider, options) {\n  return options && {\n    ...options,\n    identityProvider: options.identityProvider && {\n      getId(el) {\n        return options.identityProvider.getId(el.element);\n      }\n    },\n    dnd: options.dnd && new TreeNodeListDragAndDrop(modelProvider, options.dnd),\n    multipleSelectionController: options.multipleSelectionController && {\n      isSelectionSingleChangeEvent(e) {\n        return options.multipleSelectionController.isSelectionSingleChangeEvent({\n          ...e,\n          element: e.element\n        });\n      },\n      isSelectionRangeChangeEvent(e) {\n        return options.multipleSelectionController.isSelectionRangeChangeEvent({\n          ...e,\n          element: e.element\n        });\n      }\n    },\n    accessibilityProvider: options.accessibilityProvider && {\n      ...options.accessibilityProvider,\n      getSetSize(node) {\n        const model = modelProvider();\n        const ref = model.getNodeLocation(node);\n        const parentRef = model.getParentNodeLocation(ref);\n        const parentNode = model.getNode(parentRef);\n        return parentNode.visibleChildrenCount;\n      },\n      getPosInSet(node) {\n        return node.visibleChildIndex + 1;\n      },\n      isChecked: options.accessibilityProvider && options.accessibilityProvider.isChecked ? node => {\n        return options.accessibilityProvider.isChecked(node.element);\n      } : undefined,\n      getRole: options.accessibilityProvider && options.accessibilityProvider.getRole ? node => {\n        return options.accessibilityProvider.getRole(node.element);\n      } : () => 'treeitem',\n      getAriaLabel(e) {\n        return options.accessibilityProvider.getAriaLabel(e.element);\n      },\n      getWidgetAriaLabel() {\n        return options.accessibilityProvider.getWidgetAriaLabel();\n      },\n      getWidgetRole: options.accessibilityProvider && options.accessibilityProvider.getWidgetRole ? () => options.accessibilityProvider.getWidgetRole() : () => 'tree',\n      getAriaLevel: options.accessibilityProvider && options.accessibilityProvider.getAriaLevel ? node => options.accessibilityProvider.getAriaLevel(node.element) : node => {\n        return node.depth;\n      },\n      getActiveDescendantId: options.accessibilityProvider.getActiveDescendantId && (node => {\n        return options.accessibilityProvider.getActiveDescendantId(node.element);\n      })\n    },\n    keyboardNavigationLabelProvider: options.keyboardNavigationLabelProvider && {\n      ...options.keyboardNavigationLabelProvider,\n      getKeyboardNavigationLabel(node) {\n        return options.keyboardNavigationLabelProvider.getKeyboardNavigationLabel(node.element);\n      }\n    }\n  };\n}\nexport class ComposedTreeDelegate {\n  constructor(delegate) {\n    this.delegate = delegate;\n  }\n  getHeight(element) {\n    return this.delegate.getHeight(element.element);\n  }\n  getTemplateId(element) {\n    return this.delegate.getTemplateId(element.element);\n  }\n  hasDynamicHeight(element) {\n    return !!this.delegate.hasDynamicHeight && this.delegate.hasDynamicHeight(element.element);\n  }\n  setDynamicHeight(element, height) {\n    this.delegate.setDynamicHeight?.(element.element, height);\n  }\n}\nexport var RenderIndentGuides = /*#__PURE__*/function (RenderIndentGuides) {\n  RenderIndentGuides[\"None\"] = \"none\";\n  RenderIndentGuides[\"OnHover\"] = \"onHover\";\n  RenderIndentGuides[\"Always\"] = \"always\";\n  return RenderIndentGuides;\n}(RenderIndentGuides || {});\nclass EventCollection {\n  get elements() {\n    return this._elements;\n  }\n  constructor(onDidChange, _elements = []) {\n    this._elements = _elements;\n    this.disposables = new DisposableStore();\n    this.onDidChange = Event.forEach(onDidChange, elements => this._elements = elements, this.disposables);\n  }\n  dispose() {\n    this.disposables.dispose();\n  }\n}\nexport let TreeRenderer = /*#__PURE__*/(() => {\n  class TreeRenderer {\n    static {\n      this.DefaultIndent = 8;\n    }\n    constructor(renderer, modelProvider, onDidChangeCollapseState, activeNodes, renderedIndentGuides, options = {}) {\n      this.renderer = renderer;\n      this.modelProvider = modelProvider;\n      this.activeNodes = activeNodes;\n      this.renderedIndentGuides = renderedIndentGuides;\n      this.renderedElements = new Map();\n      this.renderedNodes = new Map();\n      this.indent = TreeRenderer.DefaultIndent;\n      this.hideTwistiesOfChildlessElements = false;\n      this.shouldRenderIndentGuides = false;\n      this.activeIndentNodes = new Set();\n      this.indentGuidesDisposable = Disposable.None;\n      this.disposables = new DisposableStore();\n      this.templateId = renderer.templateId;\n      this.updateOptions(options);\n      Event.map(onDidChangeCollapseState, e => e.node)(this.onDidChangeNodeTwistieState, this, this.disposables);\n      renderer.onDidChangeTwistieState?.(this.onDidChangeTwistieState, this, this.disposables);\n    }\n    updateOptions(options = {}) {\n      if (typeof options.indent !== 'undefined') {\n        const indent = clamp(options.indent, 0, 40);\n        if (indent !== this.indent) {\n          this.indent = indent;\n          for (const [node, templateData] of this.renderedNodes) {\n            this.renderTreeElement(node, templateData);\n          }\n        }\n      }\n      if (typeof options.renderIndentGuides !== 'undefined') {\n        const shouldRenderIndentGuides = options.renderIndentGuides !== RenderIndentGuides.None;\n        if (shouldRenderIndentGuides !== this.shouldRenderIndentGuides) {\n          this.shouldRenderIndentGuides = shouldRenderIndentGuides;\n          for (const [node, templateData] of this.renderedNodes) {\n            this._renderIndentGuides(node, templateData);\n          }\n          this.indentGuidesDisposable.dispose();\n          if (shouldRenderIndentGuides) {\n            const disposables = new DisposableStore();\n            this.activeNodes.onDidChange(this._onDidChangeActiveNodes, this, disposables);\n            this.indentGuidesDisposable = disposables;\n            this._onDidChangeActiveNodes(this.activeNodes.elements);\n          }\n        }\n      }\n      if (typeof options.hideTwistiesOfChildlessElements !== 'undefined') {\n        this.hideTwistiesOfChildlessElements = options.hideTwistiesOfChildlessElements;\n      }\n    }\n    renderTemplate(container) {\n      const el = append(container, $('.monaco-tl-row'));\n      const indent = append(el, $('.monaco-tl-indent'));\n      const twistie = append(el, $('.monaco-tl-twistie'));\n      const contents = append(el, $('.monaco-tl-contents'));\n      const templateData = this.renderer.renderTemplate(contents);\n      return {\n        container,\n        indent,\n        twistie,\n        indentGuidesDisposable: Disposable.None,\n        templateData\n      };\n    }\n    renderElement(node, index, templateData, height) {\n      this.renderedNodes.set(node, templateData);\n      this.renderedElements.set(node.element, node);\n      this.renderTreeElement(node, templateData);\n      this.renderer.renderElement(node, index, templateData.templateData, height);\n    }\n    disposeElement(node, index, templateData, height) {\n      templateData.indentGuidesDisposable.dispose();\n      this.renderer.disposeElement?.(node, index, templateData.templateData, height);\n      if (typeof height === 'number') {\n        this.renderedNodes.delete(node);\n        this.renderedElements.delete(node.element);\n      }\n    }\n    disposeTemplate(templateData) {\n      this.renderer.disposeTemplate(templateData.templateData);\n    }\n    onDidChangeTwistieState(element) {\n      const node = this.renderedElements.get(element);\n      if (!node) {\n        return;\n      }\n      this.onDidChangeNodeTwistieState(node);\n    }\n    onDidChangeNodeTwistieState(node) {\n      const templateData = this.renderedNodes.get(node);\n      if (!templateData) {\n        return;\n      }\n      this._onDidChangeActiveNodes(this.activeNodes.elements);\n      this.renderTreeElement(node, templateData);\n    }\n    renderTreeElement(node, templateData) {\n      const indent = TreeRenderer.DefaultIndent + (node.depth - 1) * this.indent;\n      templateData.twistie.style.paddingLeft = `${indent}px`;\n      templateData.indent.style.width = `${indent + this.indent - 16}px`;\n      if (node.collapsible) {\n        templateData.container.setAttribute('aria-expanded', String(!node.collapsed));\n      } else {\n        templateData.container.removeAttribute('aria-expanded');\n      }\n      templateData.twistie.classList.remove(...ThemeIcon.asClassNameArray(Codicon.treeItemExpanded));\n      let twistieRendered = false;\n      if (this.renderer.renderTwistie) {\n        twistieRendered = this.renderer.renderTwistie(node.element, templateData.twistie);\n      }\n      if (node.collapsible && (!this.hideTwistiesOfChildlessElements || node.visibleChildrenCount > 0)) {\n        if (!twistieRendered) {\n          templateData.twistie.classList.add(...ThemeIcon.asClassNameArray(Codicon.treeItemExpanded));\n        }\n        templateData.twistie.classList.add('collapsible');\n        templateData.twistie.classList.toggle('collapsed', node.collapsed);\n      } else {\n        templateData.twistie.classList.remove('collapsible', 'collapsed');\n      }\n      this._renderIndentGuides(node, templateData);\n    }\n    _renderIndentGuides(node, templateData) {\n      clearNode(templateData.indent);\n      templateData.indentGuidesDisposable.dispose();\n      if (!this.shouldRenderIndentGuides) {\n        return;\n      }\n      const disposableStore = new DisposableStore();\n      const model = this.modelProvider();\n      while (true) {\n        const ref = model.getNodeLocation(node);\n        const parentRef = model.getParentNodeLocation(ref);\n        if (!parentRef) {\n          break;\n        }\n        const parent = model.getNode(parentRef);\n        const guide = $('.indent-guide', {\n          style: `width: ${this.indent}px`\n        });\n        if (this.activeIndentNodes.has(parent)) {\n          guide.classList.add('active');\n        }\n        if (templateData.indent.childElementCount === 0) {\n          templateData.indent.appendChild(guide);\n        } else {\n          templateData.indent.insertBefore(guide, templateData.indent.firstElementChild);\n        }\n        this.renderedIndentGuides.add(parent, guide);\n        disposableStore.add(toDisposable(() => this.renderedIndentGuides.delete(parent, guide)));\n        node = parent;\n      }\n      templateData.indentGuidesDisposable = disposableStore;\n    }\n    _onDidChangeActiveNodes(nodes) {\n      if (!this.shouldRenderIndentGuides) {\n        return;\n      }\n      const set = new Set();\n      const model = this.modelProvider();\n      nodes.forEach(node => {\n        const ref = model.getNodeLocation(node);\n        try {\n          const parentRef = model.getParentNodeLocation(ref);\n          if (node.collapsible && node.children.length > 0 && !node.collapsed) {\n            set.add(node);\n          } else if (parentRef) {\n            set.add(model.getNode(parentRef));\n          }\n        } catch {\n          // noop\n        }\n      });\n      this.activeIndentNodes.forEach(node => {\n        if (!set.has(node)) {\n          this.renderedIndentGuides.forEach(node, line => line.classList.remove('active'));\n        }\n      });\n      set.forEach(node => {\n        if (!this.activeIndentNodes.has(node)) {\n          this.renderedIndentGuides.forEach(node, line => line.classList.add('active'));\n        }\n      });\n      this.activeIndentNodes = set;\n    }\n    dispose() {\n      this.renderedNodes.clear();\n      this.renderedElements.clear();\n      this.indentGuidesDisposable.dispose();\n      dispose(this.disposables);\n    }\n  }\n  return TreeRenderer;\n})();\nclass FindFilter {\n  get totalCount() {\n    return this._totalCount;\n  }\n  get matchCount() {\n    return this._matchCount;\n  }\n  constructor(tree, keyboardNavigationLabelProvider, _filter) {\n    this.tree = tree;\n    this.keyboardNavigationLabelProvider = keyboardNavigationLabelProvider;\n    this._filter = _filter;\n    this._totalCount = 0;\n    this._matchCount = 0;\n    this._pattern = '';\n    this._lowercasePattern = '';\n    this.disposables = new DisposableStore();\n    tree.onWillRefilter(this.reset, this, this.disposables);\n  }\n  filter(element, parentVisibility) {\n    let visibility = 1 /* TreeVisibility.Visible */;\n    if (this._filter) {\n      const result = this._filter.filter(element, parentVisibility);\n      if (typeof result === 'boolean') {\n        visibility = result ? 1 /* TreeVisibility.Visible */ : 0 /* TreeVisibility.Hidden */;\n      } else if (isFilterResult(result)) {\n        visibility = getVisibleState(result.visibility);\n      } else {\n        visibility = result;\n      }\n      if (visibility === 0 /* TreeVisibility.Hidden */) {\n        return false;\n      }\n    }\n    this._totalCount++;\n    if (!this._pattern) {\n      this._matchCount++;\n      return {\n        data: FuzzyScore.Default,\n        visibility\n      };\n    }\n    const label = this.keyboardNavigationLabelProvider.getKeyboardNavigationLabel(element);\n    const labels = Array.isArray(label) ? label : [label];\n    for (const l of labels) {\n      const labelStr = l && l.toString();\n      if (typeof labelStr === 'undefined') {\n        return {\n          data: FuzzyScore.Default,\n          visibility\n        };\n      }\n      let score;\n      if (this.tree.findMatchType === TreeFindMatchType.Contiguous) {\n        const index = labelStr.toLowerCase().indexOf(this._lowercasePattern);\n        if (index > -1) {\n          score = [Number.MAX_SAFE_INTEGER, 0];\n          for (let i = this._lowercasePattern.length; i > 0; i--) {\n            score.push(index + i - 1);\n          }\n        }\n      } else {\n        score = fuzzyScore(this._pattern, this._lowercasePattern, 0, labelStr, labelStr.toLowerCase(), 0, {\n          firstMatchCanBeWeak: true,\n          boostFullMatch: true\n        });\n      }\n      if (score) {\n        this._matchCount++;\n        return labels.length === 1 ? {\n          data: score,\n          visibility\n        } : {\n          data: {\n            label: labelStr,\n            score: score\n          },\n          visibility\n        };\n      }\n    }\n    if (this.tree.findMode === TreeFindMode.Filter) {\n      if (typeof this.tree.options.defaultFindVisibility === 'number') {\n        return this.tree.options.defaultFindVisibility;\n      } else if (this.tree.options.defaultFindVisibility) {\n        return this.tree.options.defaultFindVisibility(element);\n      } else {\n        return 2 /* TreeVisibility.Recurse */;\n      }\n    } else {\n      return {\n        data: FuzzyScore.Default,\n        visibility\n      };\n    }\n  }\n  reset() {\n    this._totalCount = 0;\n    this._matchCount = 0;\n  }\n  dispose() {\n    dispose(this.disposables);\n  }\n}\nexport class ModeToggle extends Toggle {\n  constructor(opts) {\n    super({\n      icon: Codicon.listFilter,\n      title: localize('filter', \"Filter\"),\n      isChecked: opts.isChecked ?? false,\n      hoverDelegate: opts.hoverDelegate ?? getDefaultHoverDelegate('element'),\n      inputActiveOptionBorder: opts.inputActiveOptionBorder,\n      inputActiveOptionForeground: opts.inputActiveOptionForeground,\n      inputActiveOptionBackground: opts.inputActiveOptionBackground\n    });\n  }\n}\nexport class FuzzyToggle extends Toggle {\n  constructor(opts) {\n    super({\n      icon: Codicon.searchFuzzy,\n      title: localize('fuzzySearch', \"Fuzzy Match\"),\n      isChecked: opts.isChecked ?? false,\n      hoverDelegate: opts.hoverDelegate ?? getDefaultHoverDelegate('element'),\n      inputActiveOptionBorder: opts.inputActiveOptionBorder,\n      inputActiveOptionForeground: opts.inputActiveOptionForeground,\n      inputActiveOptionBackground: opts.inputActiveOptionBackground\n    });\n  }\n}\nconst unthemedFindWidgetStyles = {\n  inputBoxStyles: unthemedInboxStyles,\n  toggleStyles: unthemedToggleStyles,\n  listFilterWidgetBackground: undefined,\n  listFilterWidgetNoMatchesOutline: undefined,\n  listFilterWidgetOutline: undefined,\n  listFilterWidgetShadow: undefined\n};\nexport var TreeFindMode = /*#__PURE__*/function (TreeFindMode) {\n  TreeFindMode[TreeFindMode[\"Highlight\"] = 0] = \"Highlight\";\n  TreeFindMode[TreeFindMode[\"Filter\"] = 1] = \"Filter\";\n  return TreeFindMode;\n}(TreeFindMode || {});\nexport var TreeFindMatchType = /*#__PURE__*/function (TreeFindMatchType) {\n  TreeFindMatchType[TreeFindMatchType[\"Fuzzy\"] = 0] = \"Fuzzy\";\n  TreeFindMatchType[TreeFindMatchType[\"Contiguous\"] = 1] = \"Contiguous\";\n  return TreeFindMatchType;\n}(TreeFindMatchType || {});\nclass FindWidget extends Disposable {\n  set mode(mode) {\n    this.modeToggle.checked = mode === TreeFindMode.Filter;\n    this.findInput.inputBox.setPlaceHolder(mode === TreeFindMode.Filter ? localize('type to filter', \"Type to filter\") : localize('type to search', \"Type to search\"));\n  }\n  set matchType(matchType) {\n    this.matchTypeToggle.checked = matchType === TreeFindMatchType.Fuzzy;\n  }\n  constructor(container, tree, contextViewProvider, mode, matchType, options) {\n    super();\n    this.tree = tree;\n    this.elements = h('.monaco-tree-type-filter', [h('.monaco-tree-type-filter-grab.codicon.codicon-debug-gripper@grab', {\n      tabIndex: 0\n    }), h('.monaco-tree-type-filter-input@findInput'), h('.monaco-tree-type-filter-actionbar@actionbar')]);\n    this.width = 0;\n    this.right = 0;\n    this.top = 0;\n    this._onDidDisable = new Emitter();\n    container.appendChild(this.elements.root);\n    this._register(toDisposable(() => this.elements.root.remove()));\n    const styles = options?.styles ?? unthemedFindWidgetStyles;\n    if (styles.listFilterWidgetBackground) {\n      this.elements.root.style.backgroundColor = styles.listFilterWidgetBackground;\n    }\n    if (styles.listFilterWidgetShadow) {\n      this.elements.root.style.boxShadow = `0 0 8px 2px ${styles.listFilterWidgetShadow}`;\n    }\n    const toggleHoverDelegate = this._register(createInstantHoverDelegate());\n    this.modeToggle = this._register(new ModeToggle({\n      ...styles.toggleStyles,\n      isChecked: mode === TreeFindMode.Filter,\n      hoverDelegate: toggleHoverDelegate\n    }));\n    this.matchTypeToggle = this._register(new FuzzyToggle({\n      ...styles.toggleStyles,\n      isChecked: matchType === TreeFindMatchType.Fuzzy,\n      hoverDelegate: toggleHoverDelegate\n    }));\n    this.onDidChangeMode = Event.map(this.modeToggle.onChange, () => this.modeToggle.checked ? TreeFindMode.Filter : TreeFindMode.Highlight, this._store);\n    this.onDidChangeMatchType = Event.map(this.matchTypeToggle.onChange, () => this.matchTypeToggle.checked ? TreeFindMatchType.Fuzzy : TreeFindMatchType.Contiguous, this._store);\n    this.findInput = this._register(new FindInput(this.elements.findInput, contextViewProvider, {\n      label: localize('type to search', \"Type to search\"),\n      additionalToggles: [this.modeToggle, this.matchTypeToggle],\n      showCommonFindToggles: false,\n      inputBoxStyles: styles.inputBoxStyles,\n      toggleStyles: styles.toggleStyles,\n      history: options?.history\n    }));\n    this.actionbar = this._register(new ActionBar(this.elements.actionbar));\n    this.mode = mode;\n    const emitter = this._register(new DomEmitter(this.findInput.inputBox.inputElement, 'keydown'));\n    const onKeyDown = Event.chain(emitter.event, $ => $.map(e => new StandardKeyboardEvent(e)));\n    this._register(onKeyDown(e => {\n      // Using equals() so we reserve modified keys for future use\n      if (e.equals(3 /* KeyCode.Enter */)) {\n        // This is the only keyboard way to return to the tree from a history item that isn't the last one\n        e.preventDefault();\n        e.stopPropagation();\n        this.findInput.inputBox.addToHistory();\n        this.tree.domFocus();\n        return;\n      }\n      if (e.equals(18 /* KeyCode.DownArrow */)) {\n        e.preventDefault();\n        e.stopPropagation();\n        if (this.findInput.inputBox.isAtLastInHistory() || this.findInput.inputBox.isNowhereInHistory()) {\n          // Retain original pre-history DownArrow behavior\n          this.findInput.inputBox.addToHistory();\n          this.tree.domFocus();\n        } else {\n          // Downward through history\n          this.findInput.inputBox.showNextValue();\n        }\n        return;\n      }\n      if (e.equals(16 /* KeyCode.UpArrow */)) {\n        e.preventDefault();\n        e.stopPropagation();\n        // Upward through history\n        this.findInput.inputBox.showPreviousValue();\n        return;\n      }\n    }));\n    const closeAction = this._register(new Action('close', localize('close', \"Close\"), 'codicon codicon-close', true, () => this.dispose()));\n    this.actionbar.push(closeAction, {\n      icon: true,\n      label: false\n    });\n    const onGrabMouseDown = this._register(new DomEmitter(this.elements.grab, 'mousedown'));\n    this._register(onGrabMouseDown.event(e => {\n      const disposables = new DisposableStore();\n      const onWindowMouseMove = disposables.add(new DomEmitter(getWindow(e), 'mousemove'));\n      const onWindowMouseUp = disposables.add(new DomEmitter(getWindow(e), 'mouseup'));\n      const startRight = this.right;\n      const startX = e.pageX;\n      const startTop = this.top;\n      const startY = e.pageY;\n      this.elements.grab.classList.add('grabbing');\n      const transition = this.elements.root.style.transition;\n      this.elements.root.style.transition = 'unset';\n      const update = e => {\n        const deltaX = e.pageX - startX;\n        this.right = startRight - deltaX;\n        const deltaY = e.pageY - startY;\n        this.top = startTop + deltaY;\n        this.layout();\n      };\n      disposables.add(onWindowMouseMove.event(update));\n      disposables.add(onWindowMouseUp.event(e => {\n        update(e);\n        this.elements.grab.classList.remove('grabbing');\n        this.elements.root.style.transition = transition;\n        disposables.dispose();\n      }));\n    }));\n    const onGrabKeyDown = Event.chain(this._register(new DomEmitter(this.elements.grab, 'keydown')).event, $ => $.map(e => new StandardKeyboardEvent(e)));\n    this._register(onGrabKeyDown(e => {\n      let right;\n      let top;\n      if (e.keyCode === 15 /* KeyCode.LeftArrow */) {\n        right = Number.POSITIVE_INFINITY;\n      } else if (e.keyCode === 17 /* KeyCode.RightArrow */) {\n        right = 0;\n      } else if (e.keyCode === 10 /* KeyCode.Space */) {\n        right = this.right === 0 ? Number.POSITIVE_INFINITY : 0;\n      }\n      if (e.keyCode === 16 /* KeyCode.UpArrow */) {\n        top = 0;\n      } else if (e.keyCode === 18 /* KeyCode.DownArrow */) {\n        top = Number.POSITIVE_INFINITY;\n      }\n      if (right !== undefined) {\n        e.preventDefault();\n        e.stopPropagation();\n        this.right = right;\n        this.layout();\n      }\n      if (top !== undefined) {\n        e.preventDefault();\n        e.stopPropagation();\n        this.top = top;\n        const transition = this.elements.root.style.transition;\n        this.elements.root.style.transition = 'unset';\n        this.layout();\n        setTimeout(() => {\n          this.elements.root.style.transition = transition;\n        }, 0);\n      }\n    }));\n    this.onDidChangeValue = this.findInput.onDidChange;\n  }\n  layout(width = this.width) {\n    this.width = width;\n    this.right = clamp(this.right, 0, Math.max(0, width - 212));\n    this.elements.root.style.right = `${this.right}px`;\n    this.top = clamp(this.top, 0, 24);\n    this.elements.root.style.top = `${this.top}px`;\n  }\n  showMessage(message) {\n    this.findInput.showMessage(message);\n  }\n  clearMessage() {\n    this.findInput.clearMessage();\n  }\n  dispose() {\n    var _superprop_getDispose = () => super.dispose,\n      _this = this;\n    return _asyncToGenerator(function* () {\n      _this._onDidDisable.fire();\n      _this.elements.root.classList.add('disabled');\n      yield timeout(300);\n      _superprop_getDispose().call(_this);\n    })();\n  }\n}\nclass FindController {\n  get pattern() {\n    return this._pattern;\n  }\n  get mode() {\n    return this._mode;\n  }\n  set mode(mode) {\n    if (mode === this._mode) {\n      return;\n    }\n    this._mode = mode;\n    if (this.widget) {\n      this.widget.mode = this._mode;\n    }\n    this.tree.refilter();\n    this.render();\n    this._onDidChangeMode.fire(mode);\n  }\n  get matchType() {\n    return this._matchType;\n  }\n  set matchType(matchType) {\n    if (matchType === this._matchType) {\n      return;\n    }\n    this._matchType = matchType;\n    if (this.widget) {\n      this.widget.matchType = this._matchType;\n    }\n    this.tree.refilter();\n    this.render();\n    this._onDidChangeMatchType.fire(matchType);\n  }\n  constructor(tree, model, view, filter, contextViewProvider, options = {}) {\n    this.tree = tree;\n    this.view = view;\n    this.filter = filter;\n    this.contextViewProvider = contextViewProvider;\n    this.options = options;\n    this._pattern = '';\n    this.width = 0;\n    this._onDidChangeMode = new Emitter();\n    this.onDidChangeMode = this._onDidChangeMode.event;\n    this._onDidChangeMatchType = new Emitter();\n    this.onDidChangeMatchType = this._onDidChangeMatchType.event;\n    this._onDidChangePattern = new Emitter();\n    this._onDidChangeOpenState = new Emitter();\n    this.onDidChangeOpenState = this._onDidChangeOpenState.event;\n    this.enabledDisposables = new DisposableStore();\n    this.disposables = new DisposableStore();\n    this._mode = tree.options.defaultFindMode ?? TreeFindMode.Highlight;\n    this._matchType = tree.options.defaultFindMatchType ?? TreeFindMatchType.Fuzzy;\n    model.onDidSplice(this.onDidSpliceModel, this, this.disposables);\n  }\n  updateOptions(optionsUpdate = {}) {\n    if (optionsUpdate.defaultFindMode !== undefined) {\n      this.mode = optionsUpdate.defaultFindMode;\n    }\n    if (optionsUpdate.defaultFindMatchType !== undefined) {\n      this.matchType = optionsUpdate.defaultFindMatchType;\n    }\n  }\n  onDidSpliceModel() {\n    if (!this.widget || this.pattern.length === 0) {\n      return;\n    }\n    this.tree.refilter();\n    this.render();\n  }\n  render() {\n    const noMatches = this.filter.totalCount > 0 && this.filter.matchCount === 0;\n    if (this.pattern && noMatches) {\n      alert(localize('replFindNoResults', \"No results\"));\n      if (this.tree.options.showNotFoundMessage ?? true) {\n        this.widget?.showMessage({\n          type: 2 /* MessageType.WARNING */,\n          content: localize('not found', \"No elements found.\")\n        });\n      } else {\n        this.widget?.showMessage({\n          type: 2 /* MessageType.WARNING */\n        });\n      }\n    } else {\n      this.widget?.clearMessage();\n      if (this.pattern) {\n        alert(localize('replFindResults', \"{0} results\", this.filter.matchCount));\n      }\n    }\n  }\n  shouldAllowFocus(node) {\n    if (!this.widget || !this.pattern) {\n      return true;\n    }\n    if (this.filter.totalCount > 0 && this.filter.matchCount <= 1) {\n      return true;\n    }\n    return !FuzzyScore.isDefault(node.filterData);\n  }\n  layout(width) {\n    this.width = width;\n    this.widget?.layout(width);\n  }\n  dispose() {\n    this._history = undefined;\n    this._onDidChangePattern.dispose();\n    this.enabledDisposables.dispose();\n    this.disposables.dispose();\n  }\n}\nfunction stickyScrollNodeStateEquals(node1, node2) {\n  return node1.position === node2.position && stickyScrollNodeEquals(node1, node2);\n}\nfunction stickyScrollNodeEquals(node1, node2) {\n  return node1.node.element === node2.node.element && node1.startIndex === node2.startIndex && node1.height === node2.height && node1.endIndex === node2.endIndex;\n}\nclass StickyScrollState {\n  constructor(stickyNodes = []) {\n    this.stickyNodes = stickyNodes;\n  }\n  get count() {\n    return this.stickyNodes.length;\n  }\n  equal(state) {\n    return equals(this.stickyNodes, state.stickyNodes, stickyScrollNodeStateEquals);\n  }\n  lastNodePartiallyVisible() {\n    if (this.count === 0) {\n      return false;\n    }\n    const lastStickyNode = this.stickyNodes[this.count - 1];\n    if (this.count === 1) {\n      return lastStickyNode.position !== 0;\n    }\n    const secondLastStickyNode = this.stickyNodes[this.count - 2];\n    return secondLastStickyNode.position + secondLastStickyNode.height !== lastStickyNode.position;\n  }\n  animationStateChanged(previousState) {\n    if (!equals(this.stickyNodes, previousState.stickyNodes, stickyScrollNodeEquals)) {\n      return false;\n    }\n    if (this.count === 0) {\n      return false;\n    }\n    const lastStickyNode = this.stickyNodes[this.count - 1];\n    const previousLastStickyNode = previousState.stickyNodes[previousState.count - 1];\n    return lastStickyNode.position !== previousLastStickyNode.position;\n  }\n}\nclass DefaultStickyScrollDelegate {\n  constrainStickyScrollNodes(stickyNodes, stickyScrollMaxItemCount, maxWidgetHeight) {\n    for (let i = 0; i < stickyNodes.length; i++) {\n      const stickyNode = stickyNodes[i];\n      const stickyNodeBottom = stickyNode.position + stickyNode.height;\n      if (stickyNodeBottom > maxWidgetHeight || i >= stickyScrollMaxItemCount) {\n        return stickyNodes.slice(0, i);\n      }\n    }\n    return stickyNodes;\n  }\n}\nclass StickyScrollController extends Disposable {\n  constructor(tree, model, view, renderers, treeDelegate, options = {}) {\n    super();\n    this.tree = tree;\n    this.model = model;\n    this.view = view;\n    this.treeDelegate = treeDelegate;\n    this.maxWidgetViewRatio = 0.4;\n    const stickyScrollOptions = this.validateStickySettings(options);\n    this.stickyScrollMaxItemCount = stickyScrollOptions.stickyScrollMaxItemCount;\n    this.stickyScrollDelegate = options.stickyScrollDelegate ?? new DefaultStickyScrollDelegate();\n    this._widget = this._register(new StickyScrollWidget(view.getScrollableElement(), view, tree, renderers, treeDelegate, options.accessibilityProvider));\n    this.onDidChangeHasFocus = this._widget.onDidChangeHasFocus;\n    this.onContextMenu = this._widget.onContextMenu;\n    this._register(view.onDidScroll(() => this.update()));\n    this._register(view.onDidChangeContentHeight(() => this.update()));\n    this._register(tree.onDidChangeCollapseState(() => this.update()));\n    this.update();\n  }\n  get height() {\n    return this._widget.height;\n  }\n  getNodeAtHeight(height) {\n    let index;\n    if (height === 0) {\n      index = this.view.firstVisibleIndex;\n    } else {\n      index = this.view.indexAt(height + this.view.scrollTop);\n    }\n    if (index < 0 || index >= this.view.length) {\n      return undefined;\n    }\n    return this.view.element(index);\n  }\n  update() {\n    const firstVisibleNode = this.getNodeAtHeight(0);\n    // Don't render anything if there are no elements\n    if (!firstVisibleNode || this.tree.scrollTop === 0) {\n      this._widget.setState(undefined);\n      return;\n    }\n    const stickyState = this.findStickyState(firstVisibleNode);\n    this._widget.setState(stickyState);\n  }\n  findStickyState(firstVisibleNode) {\n    const stickyNodes = [];\n    let firstVisibleNodeUnderWidget = firstVisibleNode;\n    let stickyNodesHeight = 0;\n    let nextStickyNode = this.getNextStickyNode(firstVisibleNodeUnderWidget, undefined, stickyNodesHeight);\n    while (nextStickyNode) {\n      stickyNodes.push(nextStickyNode);\n      stickyNodesHeight += nextStickyNode.height;\n      if (stickyNodes.length <= this.stickyScrollMaxItemCount) {\n        firstVisibleNodeUnderWidget = this.getNextVisibleNode(nextStickyNode);\n        if (!firstVisibleNodeUnderWidget) {\n          break;\n        }\n      }\n      nextStickyNode = this.getNextStickyNode(firstVisibleNodeUnderWidget, nextStickyNode.node, stickyNodesHeight);\n    }\n    const contrainedStickyNodes = this.constrainStickyNodes(stickyNodes);\n    return contrainedStickyNodes.length ? new StickyScrollState(contrainedStickyNodes) : undefined;\n  }\n  getNextVisibleNode(previousStickyNode) {\n    return this.getNodeAtHeight(previousStickyNode.position + previousStickyNode.height);\n  }\n  getNextStickyNode(firstVisibleNodeUnderWidget, previousStickyNode, stickyNodesHeight) {\n    const nextStickyNode = this.getAncestorUnderPrevious(firstVisibleNodeUnderWidget, previousStickyNode);\n    if (!nextStickyNode) {\n      return undefined;\n    }\n    if (nextStickyNode === firstVisibleNodeUnderWidget) {\n      if (!this.nodeIsUncollapsedParent(firstVisibleNodeUnderWidget)) {\n        return undefined;\n      }\n      if (this.nodeTopAlignsWithStickyNodesBottom(firstVisibleNodeUnderWidget, stickyNodesHeight)) {\n        return undefined;\n      }\n    }\n    return this.createStickyScrollNode(nextStickyNode, stickyNodesHeight);\n  }\n  nodeTopAlignsWithStickyNodesBottom(node, stickyNodesHeight) {\n    const nodeIndex = this.getNodeIndex(node);\n    const elementTop = this.view.getElementTop(nodeIndex);\n    const stickyPosition = stickyNodesHeight;\n    return this.view.scrollTop === elementTop - stickyPosition;\n  }\n  createStickyScrollNode(node, currentStickyNodesHeight) {\n    const height = this.treeDelegate.getHeight(node);\n    const {\n      startIndex,\n      endIndex\n    } = this.getNodeRange(node);\n    const position = this.calculateStickyNodePosition(endIndex, currentStickyNodesHeight, height);\n    return {\n      node,\n      position,\n      height,\n      startIndex,\n      endIndex\n    };\n  }\n  getAncestorUnderPrevious(node, previousAncestor = undefined) {\n    let currentAncestor = node;\n    let parentOfcurrentAncestor = this.getParentNode(currentAncestor);\n    while (parentOfcurrentAncestor) {\n      if (parentOfcurrentAncestor === previousAncestor) {\n        return currentAncestor;\n      }\n      currentAncestor = parentOfcurrentAncestor;\n      parentOfcurrentAncestor = this.getParentNode(currentAncestor);\n    }\n    if (previousAncestor === undefined) {\n      return currentAncestor;\n    }\n    return undefined;\n  }\n  calculateStickyNodePosition(lastDescendantIndex, stickyRowPositionTop, stickyNodeHeight) {\n    let lastChildRelativeTop = this.view.getRelativeTop(lastDescendantIndex);\n    // If the last descendant is only partially visible at the top of the view, getRelativeTop() returns null\n    // In that case, utilize the next node's relative top to calculate the sticky node's position\n    if (lastChildRelativeTop === null && this.view.firstVisibleIndex === lastDescendantIndex && lastDescendantIndex + 1 < this.view.length) {\n      const nodeHeight = this.treeDelegate.getHeight(this.view.element(lastDescendantIndex));\n      const nextNodeRelativeTop = this.view.getRelativeTop(lastDescendantIndex + 1);\n      lastChildRelativeTop = nextNodeRelativeTop ? nextNodeRelativeTop - nodeHeight / this.view.renderHeight : null;\n    }\n    if (lastChildRelativeTop === null) {\n      return stickyRowPositionTop;\n    }\n    const lastChildNode = this.view.element(lastDescendantIndex);\n    const lastChildHeight = this.treeDelegate.getHeight(lastChildNode);\n    const topOfLastChild = lastChildRelativeTop * this.view.renderHeight;\n    const bottomOfLastChild = topOfLastChild + lastChildHeight;\n    if (stickyRowPositionTop + stickyNodeHeight > bottomOfLastChild && stickyRowPositionTop <= bottomOfLastChild) {\n      return bottomOfLastChild - stickyNodeHeight;\n    }\n    return stickyRowPositionTop;\n  }\n  constrainStickyNodes(stickyNodes) {\n    if (stickyNodes.length === 0) {\n      return [];\n    }\n    // Check if sticky nodes need to be constrained\n    const maximumStickyWidgetHeight = this.view.renderHeight * this.maxWidgetViewRatio;\n    const lastStickyNode = stickyNodes[stickyNodes.length - 1];\n    if (stickyNodes.length <= this.stickyScrollMaxItemCount && lastStickyNode.position + lastStickyNode.height <= maximumStickyWidgetHeight) {\n      return stickyNodes;\n    }\n    // constrain sticky nodes\n    const constrainedStickyNodes = this.stickyScrollDelegate.constrainStickyScrollNodes(stickyNodes, this.stickyScrollMaxItemCount, maximumStickyWidgetHeight);\n    if (!constrainedStickyNodes.length) {\n      return [];\n    }\n    // Validate constraints\n    const lastConstrainedStickyNode = constrainedStickyNodes[constrainedStickyNodes.length - 1];\n    if (constrainedStickyNodes.length > this.stickyScrollMaxItemCount || lastConstrainedStickyNode.position + lastConstrainedStickyNode.height > maximumStickyWidgetHeight) {\n      throw new Error('stickyScrollDelegate violates constraints');\n    }\n    return constrainedStickyNodes;\n  }\n  getParentNode(node) {\n    const nodeLocation = this.model.getNodeLocation(node);\n    const parentLocation = this.model.getParentNodeLocation(nodeLocation);\n    return parentLocation ? this.model.getNode(parentLocation) : undefined;\n  }\n  nodeIsUncollapsedParent(node) {\n    const nodeLocation = this.model.getNodeLocation(node);\n    return this.model.getListRenderCount(nodeLocation) > 1;\n  }\n  getNodeIndex(node) {\n    const nodeLocation = this.model.getNodeLocation(node);\n    const nodeIndex = this.model.getListIndex(nodeLocation);\n    return nodeIndex;\n  }\n  getNodeRange(node) {\n    const nodeLocation = this.model.getNodeLocation(node);\n    const startIndex = this.model.getListIndex(nodeLocation);\n    if (startIndex < 0) {\n      throw new Error('Node not found in tree');\n    }\n    const renderCount = this.model.getListRenderCount(nodeLocation);\n    const endIndex = startIndex + renderCount - 1;\n    return {\n      startIndex,\n      endIndex\n    };\n  }\n  nodePositionTopBelowWidget(node) {\n    const ancestors = [];\n    let currentAncestor = this.getParentNode(node);\n    while (currentAncestor) {\n      ancestors.push(currentAncestor);\n      currentAncestor = this.getParentNode(currentAncestor);\n    }\n    let widgetHeight = 0;\n    for (let i = 0; i < ancestors.length && i < this.stickyScrollMaxItemCount; i++) {\n      widgetHeight += this.treeDelegate.getHeight(ancestors[i]);\n    }\n    return widgetHeight;\n  }\n  domFocus() {\n    this._widget.domFocus();\n  }\n  // Whether sticky scroll was the last focused part in the tree or not\n  focusedLast() {\n    return this._widget.focusedLast();\n  }\n  updateOptions(optionsUpdate = {}) {\n    if (!optionsUpdate.stickyScrollMaxItemCount) {\n      return;\n    }\n    const validatedOptions = this.validateStickySettings(optionsUpdate);\n    if (this.stickyScrollMaxItemCount !== validatedOptions.stickyScrollMaxItemCount) {\n      this.stickyScrollMaxItemCount = validatedOptions.stickyScrollMaxItemCount;\n      this.update();\n    }\n  }\n  validateStickySettings(options) {\n    let stickyScrollMaxItemCount = 7;\n    if (typeof options.stickyScrollMaxItemCount === 'number') {\n      stickyScrollMaxItemCount = Math.max(options.stickyScrollMaxItemCount, 1);\n    }\n    return {\n      stickyScrollMaxItemCount\n    };\n  }\n}\nclass StickyScrollWidget {\n  constructor(container, view, tree, treeRenderers, treeDelegate, accessibilityProvider) {\n    this.view = view;\n    this.tree = tree;\n    this.treeRenderers = treeRenderers;\n    this.treeDelegate = treeDelegate;\n    this.accessibilityProvider = accessibilityProvider;\n    this._previousElements = [];\n    this._previousStateDisposables = new DisposableStore();\n    this._rootDomNode = $('.monaco-tree-sticky-container.empty');\n    container.appendChild(this._rootDomNode);\n    const shadow = $('.monaco-tree-sticky-container-shadow');\n    this._rootDomNode.appendChild(shadow);\n    this.stickyScrollFocus = new StickyScrollFocus(this._rootDomNode, view);\n    this.onDidChangeHasFocus = this.stickyScrollFocus.onDidChangeHasFocus;\n    this.onContextMenu = this.stickyScrollFocus.onContextMenu;\n  }\n  get height() {\n    if (!this._previousState) {\n      return 0;\n    }\n    const lastElement = this._previousState.stickyNodes[this._previousState.count - 1];\n    return lastElement.position + lastElement.height;\n  }\n  setState(state) {\n    const wasVisible = !!this._previousState && this._previousState.count > 0;\n    const isVisible = !!state && state.count > 0;\n    // If state has not changed, do nothing\n    if (!wasVisible && !isVisible || wasVisible && isVisible && this._previousState.equal(state)) {\n      return;\n    }\n    // Update visibility of the widget if changed\n    if (wasVisible !== isVisible) {\n      this.setVisible(isVisible);\n    }\n    if (!isVisible) {\n      this._previousState = undefined;\n      this._previousElements = [];\n      this._previousStateDisposables.clear();\n      return;\n    }\n    const lastStickyNode = state.stickyNodes[state.count - 1];\n    // If the new state is only a change in the last node's position, update the position of the last element\n    if (this._previousState && state.animationStateChanged(this._previousState)) {\n      this._previousElements[this._previousState.count - 1].style.top = `${lastStickyNode.position}px`;\n    }\n    // create new dom elements\n    else {\n      this._previousStateDisposables.clear();\n      const elements = Array(state.count);\n      for (let stickyIndex = state.count - 1; stickyIndex >= 0; stickyIndex--) {\n        const stickyNode = state.stickyNodes[stickyIndex];\n        const {\n          element,\n          disposable\n        } = this.createElement(stickyNode, stickyIndex, state.count);\n        elements[stickyIndex] = element;\n        this._rootDomNode.appendChild(element);\n        this._previousStateDisposables.add(disposable);\n      }\n      this.stickyScrollFocus.updateElements(elements, state);\n      this._previousElements = elements;\n    }\n    this._previousState = state;\n    // Set the height of the widget to the bottom of the last sticky node\n    this._rootDomNode.style.height = `${lastStickyNode.position + lastStickyNode.height}px`;\n  }\n  createElement(stickyNode, stickyIndex, stickyNodesTotal) {\n    const nodeIndex = stickyNode.startIndex;\n    // Sticky element container\n    const stickyElement = document.createElement('div');\n    stickyElement.style.top = `${stickyNode.position}px`;\n    if (this.tree.options.setRowHeight !== false) {\n      stickyElement.style.height = `${stickyNode.height}px`;\n    }\n    if (this.tree.options.setRowLineHeight !== false) {\n      stickyElement.style.lineHeight = `${stickyNode.height}px`;\n    }\n    stickyElement.classList.add('monaco-tree-sticky-row');\n    stickyElement.classList.add('monaco-list-row');\n    stickyElement.setAttribute('data-index', `${nodeIndex}`);\n    stickyElement.setAttribute('data-parity', nodeIndex % 2 === 0 ? 'even' : 'odd');\n    stickyElement.setAttribute('id', this.view.getElementID(nodeIndex));\n    const accessibilityDisposable = this.setAccessibilityAttributes(stickyElement, stickyNode.node.element, stickyIndex, stickyNodesTotal);\n    // Get the renderer for the node\n    const nodeTemplateId = this.treeDelegate.getTemplateId(stickyNode.node);\n    const renderer = this.treeRenderers.find(renderer => renderer.templateId === nodeTemplateId);\n    if (!renderer) {\n      throw new Error(`No renderer found for template id ${nodeTemplateId}`);\n    }\n    // To make sure we do not influence the original node, we create a copy of the node\n    // We need to check if it is already a unique instance of the node by the delegate\n    let nodeCopy = stickyNode.node;\n    if (nodeCopy === this.tree.getNode(this.tree.getNodeLocation(stickyNode.node))) {\n      nodeCopy = new Proxy(stickyNode.node, {});\n    }\n    // Render the element\n    const templateData = renderer.renderTemplate(stickyElement);\n    renderer.renderElement(nodeCopy, stickyNode.startIndex, templateData, stickyNode.height);\n    // Remove the element from the DOM when state is disposed\n    const disposable = toDisposable(() => {\n      accessibilityDisposable.dispose();\n      renderer.disposeElement(nodeCopy, stickyNode.startIndex, templateData, stickyNode.height);\n      renderer.disposeTemplate(templateData);\n      stickyElement.remove();\n    });\n    return {\n      element: stickyElement,\n      disposable\n    };\n  }\n  setAccessibilityAttributes(container, element, stickyIndex, stickyNodesTotal) {\n    if (!this.accessibilityProvider) {\n      return Disposable.None;\n    }\n    if (this.accessibilityProvider.getSetSize) {\n      container.setAttribute('aria-setsize', String(this.accessibilityProvider.getSetSize(element, stickyIndex, stickyNodesTotal)));\n    }\n    if (this.accessibilityProvider.getPosInSet) {\n      container.setAttribute('aria-posinset', String(this.accessibilityProvider.getPosInSet(element, stickyIndex)));\n    }\n    if (this.accessibilityProvider.getRole) {\n      container.setAttribute('role', this.accessibilityProvider.getRole(element) ?? 'treeitem');\n    }\n    const ariaLabel = this.accessibilityProvider.getAriaLabel(element);\n    const observable = ariaLabel && typeof ariaLabel !== 'string' ? ariaLabel : constObservable(ariaLabel);\n    const result = autorun(reader => {\n      const value = reader.readObservable(observable);\n      if (value) {\n        container.setAttribute('aria-label', value);\n      } else {\n        container.removeAttribute('aria-label');\n      }\n    });\n    if (typeof ariaLabel === 'string') {} else if (ariaLabel) {\n      container.setAttribute('aria-label', ariaLabel.get());\n    }\n    const ariaLevel = this.accessibilityProvider.getAriaLevel && this.accessibilityProvider.getAriaLevel(element);\n    if (typeof ariaLevel === 'number') {\n      container.setAttribute('aria-level', `${ariaLevel}`);\n    }\n    // Sticky Scroll elements can not be selected\n    container.setAttribute('aria-selected', String(false));\n    return result;\n  }\n  setVisible(visible) {\n    this._rootDomNode.classList.toggle('empty', !visible);\n    if (!visible) {\n      this.stickyScrollFocus.updateElements([], undefined);\n    }\n  }\n  domFocus() {\n    this.stickyScrollFocus.domFocus();\n  }\n  focusedLast() {\n    return this.stickyScrollFocus.focusedLast();\n  }\n  dispose() {\n    this.stickyScrollFocus.dispose();\n    this._previousStateDisposables.dispose();\n    this._rootDomNode.remove();\n  }\n}\nclass StickyScrollFocus extends Disposable {\n  get domHasFocus() {\n    return this._domHasFocus;\n  }\n  set domHasFocus(hasFocus) {\n    if (hasFocus !== this._domHasFocus) {\n      this._onDidChangeHasFocus.fire(hasFocus);\n      this._domHasFocus = hasFocus;\n    }\n  }\n  constructor(container, view) {\n    super();\n    this.container = container;\n    this.view = view;\n    this.focusedIndex = -1;\n    this.elements = [];\n    this._onDidChangeHasFocus = new Emitter();\n    this.onDidChangeHasFocus = this._onDidChangeHasFocus.event;\n    this._onContextMenu = new Emitter();\n    this.onContextMenu = this._onContextMenu.event;\n    this._domHasFocus = false;\n    this._register(addDisposableListener(this.container, 'focus', () => this.onFocus()));\n    this._register(addDisposableListener(this.container, 'blur', () => this.onBlur()));\n    this._register(this.view.onDidFocus(() => this.toggleStickyScrollFocused(false)));\n    this._register(this.view.onKeyDown(e => this.onKeyDown(e)));\n    this._register(this.view.onMouseDown(e => this.onMouseDown(e)));\n    this._register(this.view.onContextMenu(e => this.handleContextMenu(e)));\n  }\n  handleContextMenu(e) {\n    const target = e.browserEvent.target;\n    if (!isStickyScrollContainer(target) && !isStickyScrollElement(target)) {\n      if (this.focusedLast()) {\n        this.view.domFocus();\n      }\n      return;\n    }\n    // The list handles the context menu triggered by a mouse event\n    // In that case only set the focus of the element clicked and leave the rest to the list to handle\n    if (!isKeyboardEvent(e.browserEvent)) {\n      if (!this.state) {\n        throw new Error('Context menu should not be triggered when state is undefined');\n      }\n      const stickyIndex = this.state.stickyNodes.findIndex(stickyNode => stickyNode.node.element === e.element?.element);\n      if (stickyIndex === -1) {\n        throw new Error('Context menu should not be triggered when element is not in sticky scroll widget');\n      }\n      this.container.focus();\n      this.setFocus(stickyIndex);\n      return;\n    }\n    if (!this.state || this.focusedIndex < 0) {\n      throw new Error('Context menu key should not be triggered when focus is not in sticky scroll widget');\n    }\n    const stickyNode = this.state.stickyNodes[this.focusedIndex];\n    const element = stickyNode.node.element;\n    const anchor = this.elements[this.focusedIndex];\n    this._onContextMenu.fire({\n      element,\n      anchor,\n      browserEvent: e.browserEvent,\n      isStickyScroll: true\n    });\n  }\n  onKeyDown(e) {\n    // Sticky Scroll Navigation\n    if (this.domHasFocus && this.state) {\n      // Move up\n      if (e.key === 'ArrowUp') {\n        this.setFocusedElement(Math.max(0, this.focusedIndex - 1));\n        e.preventDefault();\n        e.stopPropagation();\n      }\n      // Move down, if last sticky node is focused, move focus into first child of last sticky node\n      else if (e.key === 'ArrowDown' || e.key === 'ArrowRight') {\n        if (this.focusedIndex >= this.state.count - 1) {\n          const nodeIndexToFocus = this.state.stickyNodes[this.state.count - 1].startIndex + 1;\n          this.view.domFocus();\n          this.view.setFocus([nodeIndexToFocus]);\n          this.scrollNodeUnderWidget(nodeIndexToFocus, this.state);\n        } else {\n          this.setFocusedElement(this.focusedIndex + 1);\n        }\n        e.preventDefault();\n        e.stopPropagation();\n      }\n    }\n  }\n  onMouseDown(e) {\n    const target = e.browserEvent.target;\n    if (!isStickyScrollContainer(target) && !isStickyScrollElement(target)) {\n      return;\n    }\n    e.browserEvent.preventDefault();\n    e.browserEvent.stopPropagation();\n  }\n  updateElements(elements, state) {\n    if (state && state.count === 0) {\n      throw new Error('Sticky scroll state must be undefined when there are no sticky nodes');\n    }\n    if (state && state.count !== elements.length) {\n      throw new Error('Sticky scroll focus received illigel state');\n    }\n    const previousIndex = this.focusedIndex;\n    this.removeFocus();\n    this.elements = elements;\n    this.state = state;\n    if (state) {\n      const newFocusedIndex = clamp(previousIndex, 0, state.count - 1);\n      this.setFocus(newFocusedIndex);\n    } else {\n      if (this.domHasFocus) {\n        this.view.domFocus();\n      }\n    }\n    // must come last as it calls blur()\n    this.container.tabIndex = state ? 0 : -1;\n  }\n  setFocusedElement(stickyIndex) {\n    // doesn't imply that the widget has (or will have) focus\n    const state = this.state;\n    if (!state) {\n      throw new Error('Cannot set focus when state is undefined');\n    }\n    this.setFocus(stickyIndex);\n    if (stickyIndex < state.count - 1) {\n      return;\n    }\n    // If the last sticky node is not fully visible, scroll it into view\n    if (state.lastNodePartiallyVisible()) {\n      const lastStickyNode = state.stickyNodes[stickyIndex];\n      this.scrollNodeUnderWidget(lastStickyNode.endIndex + 1, state);\n    }\n  }\n  scrollNodeUnderWidget(nodeIndex, state) {\n    const lastStickyNode = state.stickyNodes[state.count - 1];\n    const secondLastStickyNode = state.count > 1 ? state.stickyNodes[state.count - 2] : undefined;\n    const elementScrollTop = this.view.getElementTop(nodeIndex);\n    const elementTargetViewTop = secondLastStickyNode ? secondLastStickyNode.position + secondLastStickyNode.height + lastStickyNode.height : lastStickyNode.height;\n    this.view.scrollTop = elementScrollTop - elementTargetViewTop;\n  }\n  domFocus() {\n    if (!this.state) {\n      throw new Error('Cannot focus when state is undefined');\n    }\n    this.container.focus();\n  }\n  focusedLast() {\n    if (!this.state) {\n      return false;\n    }\n    return this.view.getHTMLElement().classList.contains('sticky-scroll-focused');\n  }\n  removeFocus() {\n    if (this.focusedIndex === -1) {\n      return;\n    }\n    this.toggleElementFocus(this.elements[this.focusedIndex], false);\n    this.focusedIndex = -1;\n  }\n  setFocus(newFocusIndex) {\n    if (0 > newFocusIndex) {\n      throw new Error('addFocus() can not remove focus');\n    }\n    if (!this.state && newFocusIndex >= 0) {\n      throw new Error('Cannot set focus index when state is undefined');\n    }\n    if (this.state && newFocusIndex >= this.state.count) {\n      throw new Error('Cannot set focus index to an index that does not exist');\n    }\n    const oldIndex = this.focusedIndex;\n    if (oldIndex >= 0) {\n      this.toggleElementFocus(this.elements[oldIndex], false);\n    }\n    if (newFocusIndex >= 0) {\n      this.toggleElementFocus(this.elements[newFocusIndex], true);\n    }\n    this.focusedIndex = newFocusIndex;\n  }\n  toggleElementFocus(element, focused) {\n    this.toggleElementActiveFocus(element, focused && this.domHasFocus);\n    this.toggleElementPassiveFocus(element, focused);\n  }\n  toggleCurrentElementActiveFocus(focused) {\n    if (this.focusedIndex === -1) {\n      return;\n    }\n    this.toggleElementActiveFocus(this.elements[this.focusedIndex], focused);\n  }\n  toggleElementActiveFocus(element, focused) {\n    // active focus is set when sticky scroll has focus\n    element.classList.toggle('focused', focused);\n  }\n  toggleElementPassiveFocus(element, focused) {\n    // passive focus allows to show focus when sticky scroll does not have focus\n    // for example when the context menu has focus\n    element.classList.toggle('passive-focused', focused);\n  }\n  toggleStickyScrollFocused(focused) {\n    // Weather the last focus in the view was sticky scroll and not the list\n    // Is only removed when the focus is back in the tree an no longer in sticky scroll\n    this.view.getHTMLElement().classList.toggle('sticky-scroll-focused', focused);\n  }\n  onFocus() {\n    if (!this.state || this.elements.length === 0) {\n      throw new Error('Cannot focus when state is undefined or elements are empty');\n    }\n    this.domHasFocus = true;\n    this.toggleStickyScrollFocused(true);\n    this.toggleCurrentElementActiveFocus(true);\n    if (this.focusedIndex === -1) {\n      this.setFocus(0);\n    }\n  }\n  onBlur() {\n    this.domHasFocus = false;\n    this.toggleCurrentElementActiveFocus(false);\n  }\n  dispose() {\n    this.toggleStickyScrollFocused(false);\n    this._onDidChangeHasFocus.fire(false);\n    super.dispose();\n  }\n}\nfunction asTreeMouseEvent(event) {\n  let target = TreeMouseEventTarget.Unknown;\n  if (hasParentWithClass(event.browserEvent.target, 'monaco-tl-twistie', 'monaco-tl-row')) {\n    target = TreeMouseEventTarget.Twistie;\n  } else if (hasParentWithClass(event.browserEvent.target, 'monaco-tl-contents', 'monaco-tl-row')) {\n    target = TreeMouseEventTarget.Element;\n  } else if (hasParentWithClass(event.browserEvent.target, 'monaco-tree-type-filter', 'monaco-list')) {\n    target = TreeMouseEventTarget.Filter;\n  }\n  return {\n    browserEvent: event.browserEvent,\n    element: event.element ? event.element.element : null,\n    target\n  };\n}\nfunction asTreeContextMenuEvent(event) {\n  const isStickyScroll = isStickyScrollContainer(event.browserEvent.target);\n  return {\n    element: event.element ? event.element.element : null,\n    browserEvent: event.browserEvent,\n    anchor: event.anchor,\n    isStickyScroll\n  };\n}\nfunction dfs(node, fn) {\n  fn(node);\n  node.children.forEach(child => dfs(child, fn));\n}\n/**\n * The trait concept needs to exist at the tree level, because collapsed\n * tree nodes will not be known by the list.\n */\nclass Trait {\n  get nodeSet() {\n    if (!this._nodeSet) {\n      this._nodeSet = this.createNodeSet();\n    }\n    return this._nodeSet;\n  }\n  constructor(getFirstViewElementWithTrait, identityProvider) {\n    this.getFirstViewElementWithTrait = getFirstViewElementWithTrait;\n    this.identityProvider = identityProvider;\n    this.nodes = [];\n    this._onDidChange = new Emitter();\n    this.onDidChange = this._onDidChange.event;\n  }\n  set(nodes, browserEvent) {\n    if (!browserEvent?.__forceEvent && equals(this.nodes, nodes)) {\n      return;\n    }\n    this._set(nodes, false, browserEvent);\n  }\n  _set(nodes, silent, browserEvent) {\n    this.nodes = [...nodes];\n    this.elements = undefined;\n    this._nodeSet = undefined;\n    if (!silent) {\n      const that = this;\n      this._onDidChange.fire({\n        get elements() {\n          return that.get();\n        },\n        browserEvent\n      });\n    }\n  }\n  get() {\n    if (!this.elements) {\n      this.elements = this.nodes.map(node => node.element);\n    }\n    return [...this.elements];\n  }\n  getNodes() {\n    return this.nodes;\n  }\n  has(node) {\n    return this.nodeSet.has(node);\n  }\n  onDidModelSplice({\n    insertedNodes,\n    deletedNodes\n  }) {\n    if (!this.identityProvider) {\n      const set = this.createNodeSet();\n      const visit = node => set.delete(node);\n      deletedNodes.forEach(node => dfs(node, visit));\n      this.set([...set.values()]);\n      return;\n    }\n    const deletedNodesIdSet = new Set();\n    const deletedNodesVisitor = node => deletedNodesIdSet.add(this.identityProvider.getId(node.element).toString());\n    deletedNodes.forEach(node => dfs(node, deletedNodesVisitor));\n    const insertedNodesMap = new Map();\n    const insertedNodesVisitor = node => insertedNodesMap.set(this.identityProvider.getId(node.element).toString(), node);\n    insertedNodes.forEach(node => dfs(node, insertedNodesVisitor));\n    const nodes = [];\n    for (const node of this.nodes) {\n      const id = this.identityProvider.getId(node.element).toString();\n      const wasDeleted = deletedNodesIdSet.has(id);\n      if (!wasDeleted) {\n        nodes.push(node);\n      } else {\n        const insertedNode = insertedNodesMap.get(id);\n        if (insertedNode && insertedNode.visible) {\n          nodes.push(insertedNode);\n        }\n      }\n    }\n    if (this.nodes.length > 0 && nodes.length === 0) {\n      const node = this.getFirstViewElementWithTrait();\n      if (node) {\n        nodes.push(node);\n      }\n    }\n    this._set(nodes, true);\n  }\n  createNodeSet() {\n    const set = new Set();\n    for (const node of this.nodes) {\n      set.add(node);\n    }\n    return set;\n  }\n}\nclass TreeNodeListMouseController extends MouseController {\n  constructor(list, tree, stickyScrollProvider) {\n    super(list);\n    this.tree = tree;\n    this.stickyScrollProvider = stickyScrollProvider;\n  }\n  onViewPointer(e) {\n    if (isButton(e.browserEvent.target) || isInputElement(e.browserEvent.target) || isMonacoEditor(e.browserEvent.target)) {\n      return;\n    }\n    if (e.browserEvent.isHandledByList) {\n      return;\n    }\n    const node = e.element;\n    if (!node) {\n      return super.onViewPointer(e);\n    }\n    if (this.isSelectionRangeChangeEvent(e) || this.isSelectionSingleChangeEvent(e)) {\n      return super.onViewPointer(e);\n    }\n    const target = e.browserEvent.target;\n    const onTwistie = target.classList.contains('monaco-tl-twistie') || target.classList.contains('monaco-icon-label') && target.classList.contains('folder-icon') && e.browserEvent.offsetX < 16;\n    const isStickyElement = isStickyScrollElement(e.browserEvent.target);\n    let expandOnlyOnTwistieClick = false;\n    if (isStickyElement) {\n      expandOnlyOnTwistieClick = true;\n    } else if (typeof this.tree.expandOnlyOnTwistieClick === 'function') {\n      expandOnlyOnTwistieClick = this.tree.expandOnlyOnTwistieClick(node.element);\n    } else {\n      expandOnlyOnTwistieClick = !!this.tree.expandOnlyOnTwistieClick;\n    }\n    if (!isStickyElement) {\n      if (expandOnlyOnTwistieClick && !onTwistie && e.browserEvent.detail !== 2) {\n        return super.onViewPointer(e);\n      }\n      if (!this.tree.expandOnDoubleClick && e.browserEvent.detail === 2) {\n        return super.onViewPointer(e);\n      }\n    } else {\n      this.handleStickyScrollMouseEvent(e, node);\n    }\n    if (node.collapsible && (!isStickyElement || onTwistie)) {\n      const location = this.tree.getNodeLocation(node);\n      const recursive = e.browserEvent.altKey;\n      this.tree.setFocus([location]);\n      this.tree.toggleCollapsed(location, recursive);\n      if (onTwistie) {\n        // Do not set this before calling a handler on the super class, because it will reject it as handled\n        e.browserEvent.isHandledByList = true;\n        return;\n      }\n    }\n    if (!isStickyElement) {\n      super.onViewPointer(e);\n    }\n  }\n  handleStickyScrollMouseEvent(e, node) {\n    if (isMonacoCustomToggle(e.browserEvent.target) || isActionItem(e.browserEvent.target)) {\n      return;\n    }\n    const stickyScrollController = this.stickyScrollProvider();\n    if (!stickyScrollController) {\n      throw new Error('Sticky scroll controller not found');\n    }\n    const nodeIndex = this.list.indexOf(node);\n    const elementScrollTop = this.list.getElementTop(nodeIndex);\n    const elementTargetViewTop = stickyScrollController.nodePositionTopBelowWidget(node);\n    this.tree.scrollTop = elementScrollTop - elementTargetViewTop;\n    this.list.domFocus();\n    this.list.setFocus([nodeIndex]);\n    this.list.setSelection([nodeIndex]);\n  }\n  onDoubleClick(e) {\n    const onTwistie = e.browserEvent.target.classList.contains('monaco-tl-twistie');\n    if (onTwistie || !this.tree.expandOnDoubleClick) {\n      return;\n    }\n    if (e.browserEvent.isHandledByList) {\n      return;\n    }\n    super.onDoubleClick(e);\n  }\n  // to make sure dom focus is not stolen (for example with context menu)\n  onMouseDown(e) {\n    const target = e.browserEvent.target;\n    if (!isStickyScrollContainer(target) && !isStickyScrollElement(target)) {\n      super.onMouseDown(e);\n      return;\n    }\n  }\n  onContextMenu(e) {\n    const target = e.browserEvent.target;\n    if (!isStickyScrollContainer(target) && !isStickyScrollElement(target)) {\n      super.onContextMenu(e);\n      return;\n    }\n  }\n}\n/**\n * We use this List subclass to restore selection and focus as nodes\n * get rendered in the list, possibly due to a node expand() call.\n */\nclass TreeNodeList extends List {\n  constructor(user, container, virtualDelegate, renderers, focusTrait, selectionTrait, anchorTrait, options) {\n    super(user, container, virtualDelegate, renderers, options);\n    this.focusTrait = focusTrait;\n    this.selectionTrait = selectionTrait;\n    this.anchorTrait = anchorTrait;\n  }\n  createMouseController(options) {\n    return new TreeNodeListMouseController(this, options.tree, options.stickyScrollProvider);\n  }\n  splice(start, deleteCount, elements = []) {\n    super.splice(start, deleteCount, elements);\n    if (elements.length === 0) {\n      return;\n    }\n    const additionalFocus = [];\n    const additionalSelection = [];\n    let anchor;\n    elements.forEach((node, index) => {\n      if (this.focusTrait.has(node)) {\n        additionalFocus.push(start + index);\n      }\n      if (this.selectionTrait.has(node)) {\n        additionalSelection.push(start + index);\n      }\n      if (this.anchorTrait.has(node)) {\n        anchor = start + index;\n      }\n    });\n    if (additionalFocus.length > 0) {\n      super.setFocus(distinct([...super.getFocus(), ...additionalFocus]));\n    }\n    if (additionalSelection.length > 0) {\n      super.setSelection(distinct([...super.getSelection(), ...additionalSelection]));\n    }\n    if (typeof anchor === 'number') {\n      super.setAnchor(anchor);\n    }\n  }\n  setFocus(indexes, browserEvent, fromAPI = false) {\n    super.setFocus(indexes, browserEvent);\n    if (!fromAPI) {\n      this.focusTrait.set(indexes.map(i => this.element(i)), browserEvent);\n    }\n  }\n  setSelection(indexes, browserEvent, fromAPI = false) {\n    super.setSelection(indexes, browserEvent);\n    if (!fromAPI) {\n      this.selectionTrait.set(indexes.map(i => this.element(i)), browserEvent);\n    }\n  }\n  setAnchor(index, fromAPI = false) {\n    super.setAnchor(index);\n    if (!fromAPI) {\n      if (typeof index === 'undefined') {\n        this.anchorTrait.set([]);\n      } else {\n        this.anchorTrait.set([this.element(index)]);\n      }\n    }\n  }\n}\nexport class AbstractTree {\n  get onDidScroll() {\n    return this.view.onDidScroll;\n  }\n  get onDidChangeFocus() {\n    return this.eventBufferer.wrapEvent(this.focus.onDidChange);\n  }\n  get onDidChangeSelection() {\n    return this.eventBufferer.wrapEvent(this.selection.onDidChange);\n  }\n  get onMouseDblClick() {\n    return Event.filter(Event.map(this.view.onMouseDblClick, asTreeMouseEvent), e => e.target !== TreeMouseEventTarget.Filter);\n  }\n  get onMouseOver() {\n    return Event.map(this.view.onMouseOver, asTreeMouseEvent);\n  }\n  get onMouseOut() {\n    return Event.map(this.view.onMouseOut, asTreeMouseEvent);\n  }\n  get onContextMenu() {\n    return Event.any(Event.filter(Event.map(this.view.onContextMenu, asTreeContextMenuEvent), e => !e.isStickyScroll), this.stickyScrollController?.onContextMenu ?? Event.None);\n  }\n  get onPointer() {\n    return Event.map(this.view.onPointer, asTreeMouseEvent);\n  }\n  get onKeyDown() {\n    return this.view.onKeyDown;\n  }\n  get onDidFocus() {\n    return this.view.onDidFocus;\n  }\n  get onDidChangeModel() {\n    return Event.signal(this.model.onDidSplice);\n  }\n  get onDidChangeCollapseState() {\n    return this.model.onDidChangeCollapseState;\n  }\n  get findMode() {\n    return this.findController?.mode ?? TreeFindMode.Highlight;\n  }\n  set findMode(findMode) {\n    if (this.findController) {\n      this.findController.mode = findMode;\n    }\n  }\n  get findMatchType() {\n    return this.findController?.matchType ?? TreeFindMatchType.Fuzzy;\n  }\n  set findMatchType(findFuzzy) {\n    if (this.findController) {\n      this.findController.matchType = findFuzzy;\n    }\n  }\n  get expandOnDoubleClick() {\n    return typeof this._options.expandOnDoubleClick === 'undefined' ? true : this._options.expandOnDoubleClick;\n  }\n  get expandOnlyOnTwistieClick() {\n    return typeof this._options.expandOnlyOnTwistieClick === 'undefined' ? true : this._options.expandOnlyOnTwistieClick;\n  }\n  get onDidDispose() {\n    return this.view.onDidDispose;\n  }\n  constructor(_user, container, delegate, renderers, _options = {}) {\n    this._user = _user;\n    this._options = _options;\n    this.eventBufferer = new EventBufferer();\n    this.onDidChangeFindOpenState = Event.None;\n    this.onDidChangeStickyScrollFocused = Event.None;\n    this.disposables = new DisposableStore();\n    this._onWillRefilter = new Emitter();\n    this.onWillRefilter = this._onWillRefilter.event;\n    this._onDidUpdateOptions = new Emitter();\n    this.treeDelegate = new ComposedTreeDelegate(delegate);\n    const onDidChangeCollapseStateRelay = new Relay();\n    const onDidChangeActiveNodes = new Relay();\n    const activeNodes = this.disposables.add(new EventCollection(onDidChangeActiveNodes.event));\n    const renderedIndentGuides = new SetMap();\n    this.renderers = renderers.map(r => new TreeRenderer(r, () => this.model, onDidChangeCollapseStateRelay.event, activeNodes, renderedIndentGuides, _options));\n    for (const r of this.renderers) {\n      this.disposables.add(r);\n    }\n    let filter;\n    if (_options.keyboardNavigationLabelProvider) {\n      filter = new FindFilter(this, _options.keyboardNavigationLabelProvider, _options.filter);\n      _options = {\n        ..._options,\n        filter: filter\n      }; // TODO need typescript help here\n      this.disposables.add(filter);\n    }\n    this.focus = new Trait(() => this.view.getFocusedElements()[0], _options.identityProvider);\n    this.selection = new Trait(() => this.view.getSelectedElements()[0], _options.identityProvider);\n    this.anchor = new Trait(() => this.view.getAnchorElement(), _options.identityProvider);\n    this.view = new TreeNodeList(_user, container, this.treeDelegate, this.renderers, this.focus, this.selection, this.anchor, {\n      ...asListOptions(() => this.model, _options),\n      tree: this,\n      stickyScrollProvider: () => this.stickyScrollController\n    });\n    this.model = this.createModel(_user, this.view, _options);\n    onDidChangeCollapseStateRelay.input = this.model.onDidChangeCollapseState;\n    const onDidModelSplice = Event.forEach(this.model.onDidSplice, e => {\n      this.eventBufferer.bufferEvents(() => {\n        this.focus.onDidModelSplice(e);\n        this.selection.onDidModelSplice(e);\n      });\n    }, this.disposables);\n    // Make sure the `forEach` always runs\n    onDidModelSplice(() => null, null, this.disposables);\n    // Active nodes can change when the model changes or when focus or selection change.\n    // We debounce it with 0 delay since these events may fire in the same stack and we only\n    // want to run this once. It also doesn't matter if it runs on the next tick since it's only\n    // a nice to have UI feature.\n    const activeNodesEmitter = this.disposables.add(new Emitter());\n    const activeNodesDebounce = this.disposables.add(new Delayer(0));\n    this.disposables.add(Event.any(onDidModelSplice, this.focus.onDidChange, this.selection.onDidChange)(() => {\n      activeNodesDebounce.trigger(() => {\n        const set = new Set();\n        for (const node of this.focus.getNodes()) {\n          set.add(node);\n        }\n        for (const node of this.selection.getNodes()) {\n          set.add(node);\n        }\n        activeNodesEmitter.fire([...set.values()]);\n      });\n    }));\n    onDidChangeActiveNodes.input = activeNodesEmitter.event;\n    if (_options.keyboardSupport !== false) {\n      const onKeyDown = Event.chain(this.view.onKeyDown, $ => $.filter(e => !isInputElement(e.target)).map(e => new StandardKeyboardEvent(e)));\n      Event.chain(onKeyDown, $ => $.filter(e => e.keyCode === 15 /* KeyCode.LeftArrow */))(this.onLeftArrow, this, this.disposables);\n      Event.chain(onKeyDown, $ => $.filter(e => e.keyCode === 17 /* KeyCode.RightArrow */))(this.onRightArrow, this, this.disposables);\n      Event.chain(onKeyDown, $ => $.filter(e => e.keyCode === 10 /* KeyCode.Space */))(this.onSpace, this, this.disposables);\n    }\n    if ((_options.findWidgetEnabled ?? true) && _options.keyboardNavigationLabelProvider && _options.contextViewProvider) {\n      const opts = this.options.findWidgetStyles ? {\n        styles: this.options.findWidgetStyles\n      } : undefined;\n      this.findController = new FindController(this, this.model, this.view, filter, _options.contextViewProvider, opts);\n      this.focusNavigationFilter = node => this.findController.shouldAllowFocus(node);\n      this.onDidChangeFindOpenState = this.findController.onDidChangeOpenState;\n      this.disposables.add(this.findController);\n      this.onDidChangeFindMode = this.findController.onDidChangeMode;\n      this.onDidChangeFindMatchType = this.findController.onDidChangeMatchType;\n    } else {\n      this.onDidChangeFindMode = Event.None;\n      this.onDidChangeFindMatchType = Event.None;\n    }\n    if (_options.enableStickyScroll) {\n      this.stickyScrollController = new StickyScrollController(this, this.model, this.view, this.renderers, this.treeDelegate, _options);\n      this.onDidChangeStickyScrollFocused = this.stickyScrollController.onDidChangeHasFocus;\n    }\n    this.styleElement = createStyleSheet(this.view.getHTMLElement());\n    this.getHTMLElement().classList.toggle('always', this._options.renderIndentGuides === RenderIndentGuides.Always);\n  }\n  updateOptions(optionsUpdate = {}) {\n    this._options = {\n      ...this._options,\n      ...optionsUpdate\n    };\n    for (const renderer of this.renderers) {\n      renderer.updateOptions(optionsUpdate);\n    }\n    this.view.updateOptions(this._options);\n    this.findController?.updateOptions(optionsUpdate);\n    this.updateStickyScroll(optionsUpdate);\n    this._onDidUpdateOptions.fire(this._options);\n    this.getHTMLElement().classList.toggle('always', this._options.renderIndentGuides === RenderIndentGuides.Always);\n  }\n  get options() {\n    return this._options;\n  }\n  updateStickyScroll(optionsUpdate) {\n    if (!this.stickyScrollController && this._options.enableStickyScroll) {\n      this.stickyScrollController = new StickyScrollController(this, this.model, this.view, this.renderers, this.treeDelegate, this._options);\n      this.onDidChangeStickyScrollFocused = this.stickyScrollController.onDidChangeHasFocus;\n    } else if (this.stickyScrollController && !this._options.enableStickyScroll) {\n      this.onDidChangeStickyScrollFocused = Event.None;\n      this.stickyScrollController.dispose();\n      this.stickyScrollController = undefined;\n    }\n    this.stickyScrollController?.updateOptions(optionsUpdate);\n  }\n  // Widget\n  getHTMLElement() {\n    return this.view.getHTMLElement();\n  }\n  get scrollTop() {\n    return this.view.scrollTop;\n  }\n  set scrollTop(scrollTop) {\n    this.view.scrollTop = scrollTop;\n  }\n  get scrollHeight() {\n    return this.view.scrollHeight;\n  }\n  get renderHeight() {\n    return this.view.renderHeight;\n  }\n  get ariaLabel() {\n    return this.view.ariaLabel;\n  }\n  set ariaLabel(value) {\n    this.view.ariaLabel = value;\n  }\n  domFocus() {\n    if (this.stickyScrollController?.focusedLast()) {\n      this.stickyScrollController.domFocus();\n    } else {\n      this.view.domFocus();\n    }\n  }\n  layout(height, width) {\n    this.view.layout(height, width);\n    if (isNumber(width)) {\n      this.findController?.layout(width);\n    }\n  }\n  style(styles) {\n    const suffix = `.${this.view.domId}`;\n    const content = [];\n    if (styles.treeIndentGuidesStroke) {\n      content.push(`.monaco-list${suffix}:hover .monaco-tl-indent > .indent-guide, .monaco-list${suffix}.always .monaco-tl-indent > .indent-guide  { border-color: ${styles.treeInactiveIndentGuidesStroke}; }`);\n      content.push(`.monaco-list${suffix} .monaco-tl-indent > .indent-guide.active { border-color: ${styles.treeIndentGuidesStroke}; }`);\n    }\n    // Sticky Scroll Background\n    const stickyScrollBackground = styles.treeStickyScrollBackground ?? styles.listBackground;\n    if (stickyScrollBackground) {\n      content.push(`.monaco-list${suffix} .monaco-scrollable-element .monaco-tree-sticky-container { background-color: ${stickyScrollBackground}; }`);\n      content.push(`.monaco-list${suffix} .monaco-scrollable-element .monaco-tree-sticky-container .monaco-tree-sticky-row { background-color: ${stickyScrollBackground}; }`);\n    }\n    // Sticky Scroll Border\n    if (styles.treeStickyScrollBorder) {\n      content.push(`.monaco-list${suffix} .monaco-scrollable-element .monaco-tree-sticky-container { border-bottom: 1px solid ${styles.treeStickyScrollBorder}; }`);\n    }\n    // Sticky Scroll Shadow\n    if (styles.treeStickyScrollShadow) {\n      content.push(`.monaco-list${suffix} .monaco-scrollable-element .monaco-tree-sticky-container .monaco-tree-sticky-container-shadow { box-shadow: ${styles.treeStickyScrollShadow} 0 6px 6px -6px inset; height: 3px; }`);\n    }\n    // Sticky Scroll Focus\n    if (styles.listFocusForeground) {\n      content.push(`.monaco-list${suffix}.sticky-scroll-focused .monaco-scrollable-element .monaco-tree-sticky-container:focus .monaco-list-row.focused { color: ${styles.listFocusForeground}; }`);\n      content.push(`.monaco-list${suffix}:not(.sticky-scroll-focused) .monaco-scrollable-element .monaco-tree-sticky-container .monaco-list-row.focused { color: inherit; }`);\n    }\n    // Sticky Scroll Focus Outlines\n    const focusAndSelectionOutline = asCssValueWithDefault(styles.listFocusAndSelectionOutline, asCssValueWithDefault(styles.listSelectionOutline, styles.listFocusOutline ?? ''));\n    if (focusAndSelectionOutline) {\n      // default: listFocusOutline\n      content.push(`.monaco-list${suffix}.sticky-scroll-focused .monaco-scrollable-element .monaco-tree-sticky-container:focus .monaco-list-row.focused.selected { outline: 1px solid ${focusAndSelectionOutline}; outline-offset: -1px;}`);\n      content.push(`.monaco-list${suffix}:not(.sticky-scroll-focused) .monaco-scrollable-element .monaco-tree-sticky-container .monaco-list-row.focused.selected { outline: inherit;}`);\n    }\n    if (styles.listFocusOutline) {\n      // default: set\n      content.push(`.monaco-list${suffix}.sticky-scroll-focused .monaco-scrollable-element .monaco-tree-sticky-container:focus .monaco-list-row.focused { outline: 1px solid ${styles.listFocusOutline}; outline-offset: -1px; }`);\n      content.push(`.monaco-list${suffix}:not(.sticky-scroll-focused) .monaco-scrollable-element .monaco-tree-sticky-container .monaco-list-row.focused { outline: inherit; }`);\n      content.push(`.monaco-workbench.context-menu-visible .monaco-list${suffix}.last-focused.sticky-scroll-focused .monaco-scrollable-element .monaco-tree-sticky-container .monaco-list-row.passive-focused { outline: 1px solid ${styles.listFocusOutline}; outline-offset: -1px; }`);\n      content.push(`.monaco-workbench.context-menu-visible .monaco-list${suffix}.last-focused.sticky-scroll-focused .monaco-list-rows .monaco-list-row.focused { outline: inherit; }`);\n      content.push(`.monaco-workbench.context-menu-visible .monaco-list${suffix}.last-focused:not(.sticky-scroll-focused) .monaco-tree-sticky-container .monaco-list-rows .monaco-list-row.focused { outline: inherit; }`);\n    }\n    this.styleElement.textContent = content.join('\\n');\n    this.view.style(styles);\n  }\n  // Tree navigation\n  getParentElement(location) {\n    const parentRef = this.model.getParentNodeLocation(location);\n    const parentNode = this.model.getNode(parentRef);\n    return parentNode.element;\n  }\n  getFirstElementChild(location) {\n    return this.model.getFirstElementChild(location);\n  }\n  // Tree\n  getNode(location) {\n    return this.model.getNode(location);\n  }\n  getNodeLocation(node) {\n    return this.model.getNodeLocation(node);\n  }\n  collapse(location, recursive = false) {\n    return this.model.setCollapsed(location, true, recursive);\n  }\n  expand(location, recursive = false) {\n    return this.model.setCollapsed(location, false, recursive);\n  }\n  toggleCollapsed(location, recursive = false) {\n    return this.model.setCollapsed(location, undefined, recursive);\n  }\n  isCollapsible(location) {\n    return this.model.isCollapsible(location);\n  }\n  setCollapsible(location, collapsible) {\n    return this.model.setCollapsible(location, collapsible);\n  }\n  isCollapsed(location) {\n    return this.model.isCollapsed(location);\n  }\n  refilter() {\n    this._onWillRefilter.fire(undefined);\n    this.model.refilter();\n  }\n  setSelection(elements, browserEvent) {\n    this.eventBufferer.bufferEvents(() => {\n      const nodes = elements.map(e => this.model.getNode(e));\n      this.selection.set(nodes, browserEvent);\n      const indexes = elements.map(e => this.model.getListIndex(e)).filter(i => i > -1);\n      this.view.setSelection(indexes, browserEvent, true);\n    });\n  }\n  getSelection() {\n    return this.selection.get();\n  }\n  setFocus(elements, browserEvent) {\n    this.eventBufferer.bufferEvents(() => {\n      const nodes = elements.map(e => this.model.getNode(e));\n      this.focus.set(nodes, browserEvent);\n      const indexes = elements.map(e => this.model.getListIndex(e)).filter(i => i > -1);\n      this.view.setFocus(indexes, browserEvent, true);\n    });\n  }\n  focusNext(n = 1, loop = false, browserEvent, filter = isKeyboardEvent(browserEvent) && browserEvent.altKey ? undefined : this.focusNavigationFilter) {\n    this.view.focusNext(n, loop, browserEvent, filter);\n  }\n  focusPrevious(n = 1, loop = false, browserEvent, filter = isKeyboardEvent(browserEvent) && browserEvent.altKey ? undefined : this.focusNavigationFilter) {\n    this.view.focusPrevious(n, loop, browserEvent, filter);\n  }\n  focusNextPage(browserEvent, filter = isKeyboardEvent(browserEvent) && browserEvent.altKey ? undefined : this.focusNavigationFilter) {\n    return this.view.focusNextPage(browserEvent, filter);\n  }\n  focusPreviousPage(browserEvent, filter = isKeyboardEvent(browserEvent) && browserEvent.altKey ? undefined : this.focusNavigationFilter) {\n    return this.view.focusPreviousPage(browserEvent, filter, () => this.stickyScrollController?.height ?? 0);\n  }\n  focusLast(browserEvent, filter = isKeyboardEvent(browserEvent) && browserEvent.altKey ? undefined : this.focusNavigationFilter) {\n    this.view.focusLast(browserEvent, filter);\n  }\n  focusFirst(browserEvent, filter = isKeyboardEvent(browserEvent) && browserEvent.altKey ? undefined : this.focusNavigationFilter) {\n    this.view.focusFirst(browserEvent, filter);\n  }\n  getFocus() {\n    return this.focus.get();\n  }\n  reveal(location, relativeTop) {\n    this.model.expandTo(location);\n    const index = this.model.getListIndex(location);\n    if (index === -1) {\n      return;\n    }\n    if (!this.stickyScrollController) {\n      this.view.reveal(index, relativeTop);\n    } else {\n      const paddingTop = this.stickyScrollController.nodePositionTopBelowWidget(this.getNode(location));\n      this.view.reveal(index, relativeTop, paddingTop);\n    }\n  }\n  // List\n  onLeftArrow(e) {\n    e.preventDefault();\n    e.stopPropagation();\n    const nodes = this.view.getFocusedElements();\n    if (nodes.length === 0) {\n      return;\n    }\n    const node = nodes[0];\n    const location = this.model.getNodeLocation(node);\n    const didChange = this.model.setCollapsed(location, true);\n    if (!didChange) {\n      const parentLocation = this.model.getParentNodeLocation(location);\n      if (!parentLocation) {\n        return;\n      }\n      const parentListIndex = this.model.getListIndex(parentLocation);\n      this.view.reveal(parentListIndex);\n      this.view.setFocus([parentListIndex]);\n    }\n  }\n  onRightArrow(e) {\n    e.preventDefault();\n    e.stopPropagation();\n    const nodes = this.view.getFocusedElements();\n    if (nodes.length === 0) {\n      return;\n    }\n    const node = nodes[0];\n    const location = this.model.getNodeLocation(node);\n    const didChange = this.model.setCollapsed(location, false);\n    if (!didChange) {\n      if (!node.children.some(child => child.visible)) {\n        return;\n      }\n      const [focusedIndex] = this.view.getFocus();\n      const firstChildIndex = focusedIndex + 1;\n      this.view.reveal(firstChildIndex);\n      this.view.setFocus([firstChildIndex]);\n    }\n  }\n  onSpace(e) {\n    e.preventDefault();\n    e.stopPropagation();\n    const nodes = this.view.getFocusedElements();\n    if (nodes.length === 0) {\n      return;\n    }\n    const node = nodes[0];\n    const location = this.model.getNodeLocation(node);\n    const recursive = e.browserEvent.altKey;\n    this.model.setCollapsed(location, undefined, recursive);\n  }\n  dispose() {\n    dispose(this.disposables);\n    this.stickyScrollController?.dispose();\n    this.view.dispose();\n  }\n}", "map": {"version": 3, "names": ["$", "append", "clearNode", "createStyleSheet", "getWindow", "h", "hasParentWithClass", "asCssValueWithDefault", "isKeyboardEvent", "addDisposableListener", "DomEmitter", "StandardKeyboardEvent", "ActionBar", "FindInput", "unthemedInboxStyles", "ElementsDragAndDropData", "isActionItem", "isButton", "isInputElement", "isMonacoCustomToggle", "isMonacoEditor", "isStickyScrollContainer", "isStickyScrollElement", "List", "MouseController", "Toggle", "unthemedToggleStyles", "getVisibleState", "isFilterResult", "TreeMouseEventTarget", "Action", "distinct", "equals", "range", "<PERSON><PERSON><PERSON>", "disposableTimeout", "timeout", "Codicon", "ThemeIcon", "SetMap", "Emitter", "Event", "EventBuff<PERSON>", "<PERSON><PERSON>", "fuzzyScore", "FuzzyScore", "Disposable", "DisposableStore", "dispose", "toDisposable", "clamp", "isNumber", "localize", "createInstantHoverDelegate", "getDefaultHoverDelegate", "autorun", "constObservable", "alert", "TreeElementsDragAndDropData", "constructor", "data", "elements", "map", "node", "element", "asTreeDragAndDropData", "TreeNodeListDragAndDrop", "modelProvider", "dnd", "autoExpandDisposable", "None", "disposables", "getDragURI", "getDragLabel", "nodes", "originalEvent", "undefined", "onDragStart", "onDragOver", "targetNode", "targetIndex", "targetSector", "raw", "result", "didChangeAutoExpandNode", "autoExpandNode", "autoExpand", "model", "ref", "getNodeLocation", "isCollapsed", "setCollapsed", "accept", "bubble", "feedback", "effect", "parentRef", "getParentNodeLocation", "parentNode", "getNode", "parentIndex", "getListIndex", "start", "length", "getListRenderCount", "drop", "onDragEnd", "asListOptions", "options", "identity<PERSON><PERSON><PERSON>", "getId", "el", "multipleSelectionController", "isSelectionSingleChangeEvent", "e", "isSelectionRangeChangeEvent", "accessibilityProvider", "getSetSize", "visible<PERSON><PERSON><PERSON>n<PERSON>ount", "getPosInSet", "visibleChildIndex", "isChecked", "getRole", "getAriaLabel", "getWidgetAriaLabel", "getWidgetRole", "getAriaLevel", "depth", "getActiveDescendantId", "keyboardNavigationLabe<PERSON>", "getKeyboardNavigationLabel", "ComposedTreeDelegate", "delegate", "getHeight", "getTemplateId", "hasDynamicHeight", "setDynamicHeight", "height", "RenderIndentGuides", "EventCollection", "_elements", "onDidChange", "for<PERSON>ach", "<PERSON><PERSON><PERSON><PERSON>", "DefaultIndent", "renderer", "onDidChangeCollapseState", "activeNodes", "renderedIndentGuides", "renderedElements", "Map", "renderedNodes", "indent", "hideTwistiesOfChildlessElements", "shouldRenderIndentGuides", "activeIndentNodes", "Set", "indentGuidesDisposable", "templateId", "updateOptions", "onDidChangeNodeTwistieState", "onDidChangeTwistieState", "templateData", "renderTreeElement", "renderIndentGuides", "_renderIndentGuides", "_onDidChangeActiveNodes", "renderTemplate", "container", "twistie", "contents", "renderElement", "index", "set", "disposeElement", "delete", "disposeTemplate", "get", "style", "paddingLeft", "width", "collapsible", "setAttribute", "String", "collapsed", "removeAttribute", "classList", "remove", "asClassNameArray", "treeItemExpanded", "<PERSON><PERSON><PERSON><PERSON>ed", "<PERSON><PERSON><PERSON><PERSON>ie", "add", "toggle", "disposableStore", "parent", "guide", "has", "childElementCount", "append<PERSON><PERSON><PERSON>", "insertBefore", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "line", "clear", "<PERSON><PERSON><PERSON><PERSON>", "totalCount", "_totalCount", "matchCount", "_matchCount", "tree", "_filter", "_pattern", "_lowercasePattern", "onWillRefilter", "reset", "filter", "parentVisibility", "visibility", "<PERSON><PERSON><PERSON>", "label", "labels", "Array", "isArray", "l", "labelStr", "toString", "score", "findMatchType", "TreeFindMatchType", "Contiguous", "toLowerCase", "indexOf", "Number", "MAX_SAFE_INTEGER", "i", "push", "firstMatchCanBeWeak", "boostFullMatch", "findMode", "TreeFindMode", "Filter", "defaultFindVisibility", "ModeToggle", "opts", "icon", "listFilter", "title", "hoverDelegate", "inputActiveOptionBorder", "inputActiveOptionForeground", "inputActiveOptionBackground", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "searchFuzzy", "unthemedFindWidgetStyles", "inputBoxStyles", "toggleStyles", "listFilterWidgetBackground", "listFilterWidgetNoMatchesOutline", "listFilterWidgetOutline", "listFilterWidgetShadow", "FindWidget", "mode", "modeToggle", "checked", "findInput", "inputBox", "setPlaceHolder", "matchType", "matchTypeToggle", "Fuzzy", "contextView<PERSON>rovider", "tabIndex", "right", "top", "_onDidDisable", "root", "_register", "styles", "backgroundColor", "boxShadow", "toggleHoverDelegate", "onDidChangeMode", "onChange", "Highlight", "_store", "onDidChangeMatchType", "additionalToggles", "showCommonFindToggles", "history", "actionbar", "emitter", "inputElement", "onKeyDown", "chain", "event", "preventDefault", "stopPropagation", "addToHistory", "domFocus", "isAtLastInHistory", "isNowhereInHistory", "showNextValue", "showPreviousValue", "closeAction", "onGrabMouseDown", "grab", "onWindowMouseMove", "onWindowMouseUp", "startRight", "startX", "pageX", "startTop", "startY", "pageY", "transition", "update", "deltaX", "deltaY", "layout", "onGrabKeyDown", "keyCode", "POSITIVE_INFINITY", "setTimeout", "onDidChangeValue", "Math", "max", "showMessage", "message", "clearMessage", "_superprop_getDispose", "_this", "_asyncToGenerator", "fire", "call", "FindController", "pattern", "_mode", "widget", "refilter", "render", "_onDidChangeMode", "_matchType", "_onDidChangeMatchType", "view", "_onDidChangePattern", "_onDidChangeOpenState", "onDidChangeOpenState", "enabledDisposables", "defaultFindMode", "defaultFindMatchType", "onDidSplice", "onDidSpliceModel", "optionsUpdate", "noMatches", "showNotFoundMessage", "type", "content", "shouldAllowFocus", "isDefault", "filterData", "_history", "stickyScrollNodeStateEquals", "node1", "node2", "position", "stickyScrollNodeEquals", "startIndex", "endIndex", "StickyScrollState", "stickyNodes", "count", "equal", "state", "lastNodePartiallyVisible", "lastStickyNode", "secondLastStickyNode", "animationStateChanged", "previousState", "previousLastStickyNode", "DefaultStickyScrollDelegate", "constrainStickyScrollNodes", "stickyScrollMaxItemCount", "maxWidgetHeight", "stickyNode", "stickyNodeBottom", "slice", "StickyScrollController", "renderers", "treeDelegate", "maxWidgetViewRatio", "stickyScrollOptions", "validateStickySettings", "stickyScrollDelegate", "_widget", "StickyScrollWidget", "getScrollableElement", "onDidChangeHasFocus", "onContextMenu", "onDidScroll", "onDidChangeContentHeight", "getNodeAtHeight", "firstVisibleIndex", "indexAt", "scrollTop", "firstVisibleNode", "setState", "stickyState", "findStickyState", "firstVisibleNodeUnderWidget", "stickyNodesHeight", "nextStickyNode", "getNextStickyNode", "getNextVisibleNode", "contrainedStickyNodes", "constrainStickyNodes", "previousStickyNode", "getAncestorUnderPrevious", "nodeIsUncollapsedParent", "nodeTopAlignsWithStickyNodesBottom", "createStickyScrollNode", "nodeIndex", "getNodeIndex", "elementTop", "getElementTop", "stickyPosition", "currentStickyNodesHeight", "getNodeRange", "calculateStickyNodePosition", "previousA<PERSON><PERSON>", "currentAncestor", "parentOfcurrentAncestor", "getParentNode", "lastDescendantIndex", "stickyRowPositionTop", "stickyNodeHeight", "lastChildRelativeTop", "getRelativeTop", "nodeHeight", "nextNodeRelativeTop", "renderHeight", "lastChildNode", "<PERSON><PERSON><PERSON><PERSON><PERSON>eight", "topOfLastChild", "bottomOfLastChild", "maximumStickyWidgetHeight", "constrainedStickyNodes", "lastConstrainedStickyNode", "Error", "nodeLocation", "parentLocation", "renderCount", "nodePositionTopBelowWidget", "ancestors", "widgetHeight", "focusedLast", "validatedOptions", "treeRenderers", "_previousElements", "_previousStateDisposables", "_rootDomNode", "shadow", "stickyScrollFocus", "StickyScrollFocus", "_previousState", "lastElement", "wasVisible", "isVisible", "setVisible", "stickyIndex", "disposable", "createElement", "updateElements", "stickyNodesTotal", "stickyElement", "document", "setRowHeight", "setRowLineHeight", "lineHeight", "getElementID", "accessibilityDisposable", "setAccessibilityAttributes", "nodeTemplateId", "find", "nodeCopy", "Proxy", "aria<PERSON><PERSON><PERSON>", "observable", "reader", "value", "readObservable", "ariaLevel", "visible", "domHasFocus", "_domHasFocus", "hasFocus", "_onDidChangeHasFocus", "focusedIndex", "_onContextMenu", "onFocus", "onBlur", "onDidFocus", "toggleStickyScrollFocused", "onMouseDown", "handleContextMenu", "target", "browserEvent", "findIndex", "focus", "setFocus", "anchor", "isStickyScroll", "key", "setFocusedElement", "nodeIndexToFocus", "scrollNodeUnderWidget", "previousIndex", "removeFocus", "newFocusedIndex", "elementScrollTop", "elementTargetViewTop", "getHTMLElement", "contains", "toggleElementFocus", "newFocusIndex", "oldIndex", "focused", "toggleElementActiveFocus", "toggleElementPassiveFocus", "toggleCurrentElementActiveFocus", "asTreeMouseEvent", "Unknown", "<PERSON><PERSON><PERSON><PERSON>", "Element", "asTreeContextMenuEvent", "dfs", "fn", "child", "<PERSON><PERSON><PERSON>", "nodeSet", "_nodeSet", "createNodeSet", "getFirstViewElementWithTrait", "_onDidChange", "__forceEvent", "_set", "silent", "that", "getNodes", "onDidModelSplice", "insertedNodes", "deletedNodes", "visit", "values", "deletedNodesIdSet", "deletedNodesVisitor", "insertedNodesMap", "insertedNodesVisitor", "id", "wasDeleted", "insertedNode", "TreeNodeListMouseController", "list", "stickyScrollProvider", "on<PERSON>iewPointer", "isHandledByList", "onTwistie", "offsetX", "isStickyElement", "expandOnlyOnTwistieClick", "detail", "expandOnDoubleClick", "handleStickyScrollMouseEvent", "location", "recursive", "altKey", "toggleCollapsed", "stickyScrollController", "setSelection", "onDoubleClick", "TreeNodeList", "user", "virtualDelegate", "focusTrait", "selectionTrait", "anchorTrait", "createMouseController", "splice", "deleteCount", "additionalFocus", "additionalSelection", "getFocus", "getSelection", "setAnchor", "indexes", "fromAPI", "AbstractTree", "onDidChangeFocus", "event<PERSON><PERSON><PERSON>", "wrapEvent", "onDidChangeSelection", "selection", "onMouseDblClick", "onMouseOver", "onMouseOut", "any", "onPointer", "onDidChangeModel", "signal", "findController", "findFuzzy", "_options", "onDidDispose", "_user", "onDidChangeFindOpenState", "onDidChangeStickyScrollFocused", "_onWillR<PERSON><PERSON>er", "_onDidUpdateOptions", "onDidChangeCollapseStateRelay", "onDidChangeActiveNodes", "r", "getFocusedElements", "getSelectedElements", "getAnchorElement", "createModel", "input", "bufferEvents", "activeNodesEmitter", "activeNodesDebounce", "trigger", "keyboardSupport", "onLeftArrow", "onRightArrow", "onSpace", "findWidgetEnabled", "findWidgetStyles", "focusNavigationFilter", "onDidChangeFindMode", "onDidChangeFindMatchType", "enableStickyScroll", "styleElement", "Always", "updateStickyScroll", "scrollHeight", "suffix", "domId", "treeIndentGuidesStroke", "treeInactiveIndentGuidesStroke", "stickyScrollBackground", "treeStickyScrollBackground", "listBackground", "treeStickyScrollBorder", "treeStickyScrollShadow", "listFocusForeground", "focusAndSelectionOutline", "listFocusAndSelectionOutline", "listSelectionOutline", "listFocusOutline", "textContent", "join", "getParentElement", "getFirstElementChild", "collapse", "expand", "isCollapsible", "setCollapsible", "focusNext", "n", "loop", "focusPrevious", "focusNextPage", "focusPreviousPage", "focusLast", "focusFirst", "reveal", "relativeTop", "expandTo", "paddingTop", "<PERSON><PERSON><PERSON><PERSON>", "parentListIndex", "some", "firstChildIndex"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/browser/ui/tree/abstractTree.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { $, append, clearNode, createStyleSheet, getWindow, h, hasParentWithClass, asCssValueWithDefault, isKeyboardEvent, addDisposableListener } from '../../dom.js';\nimport { DomEmitter } from '../../event.js';\nimport { StandardKeyboardEvent } from '../../keyboardEvent.js';\nimport { ActionBar } from '../actionbar/actionbar.js';\nimport { FindInput } from '../findinput/findInput.js';\nimport { unthemedInboxStyles } from '../inputbox/inputBox.js';\nimport { ElementsDragAndDropData } from '../list/listView.js';\nimport { isActionItem, isButton, isInputElement, isMonacoCustomToggle, isMonacoEditor, isStickyScrollContainer, isStickyScrollElement, List, MouseController } from '../list/listWidget.js';\nimport { Toggle, unthemedToggleStyles } from '../toggle/toggle.js';\nimport { getVisibleState, isFilterResult } from './indexTreeModel.js';\nimport { TreeMouseEventTarget } from './tree.js';\nimport { Action } from '../../../common/actions.js';\nimport { distinct, equals, range } from '../../../common/arrays.js';\nimport { Delayer, disposableTimeout, timeout } from '../../../common/async.js';\nimport { Codicon } from '../../../common/codicons.js';\nimport { ThemeIcon } from '../../../common/themables.js';\nimport { SetMap } from '../../../common/map.js';\nimport { Emitter, Event, EventBufferer, Relay } from '../../../common/event.js';\nimport { fuzzyScore, FuzzyScore } from '../../../common/filters.js';\nimport { Disposable, DisposableStore, dispose, toDisposable } from '../../../common/lifecycle.js';\nimport { clamp } from '../../../common/numbers.js';\nimport { isNumber } from '../../../common/types.js';\nimport './media/tree.css';\nimport { localize } from '../../../../nls.js';\nimport { createInstantHoverDelegate, getDefaultHoverDelegate } from '../hover/hoverDelegateFactory.js';\nimport { autorun, constObservable } from '../../../common/observable.js';\nimport { alert } from '../aria/aria.js';\nclass TreeElementsDragAndDropData extends ElementsDragAndDropData {\n    constructor(data) {\n        super(data.elements.map(node => node.element));\n        this.data = data;\n    }\n}\nfunction asTreeDragAndDropData(data) {\n    if (data instanceof ElementsDragAndDropData) {\n        return new TreeElementsDragAndDropData(data);\n    }\n    return data;\n}\nclass TreeNodeListDragAndDrop {\n    constructor(modelProvider, dnd) {\n        this.modelProvider = modelProvider;\n        this.dnd = dnd;\n        this.autoExpandDisposable = Disposable.None;\n        this.disposables = new DisposableStore();\n    }\n    getDragURI(node) {\n        return this.dnd.getDragURI(node.element);\n    }\n    getDragLabel(nodes, originalEvent) {\n        if (this.dnd.getDragLabel) {\n            return this.dnd.getDragLabel(nodes.map(node => node.element), originalEvent);\n        }\n        return undefined;\n    }\n    onDragStart(data, originalEvent) {\n        this.dnd.onDragStart?.(asTreeDragAndDropData(data), originalEvent);\n    }\n    onDragOver(data, targetNode, targetIndex, targetSector, originalEvent, raw = true) {\n        const result = this.dnd.onDragOver(asTreeDragAndDropData(data), targetNode && targetNode.element, targetIndex, targetSector, originalEvent);\n        const didChangeAutoExpandNode = this.autoExpandNode !== targetNode;\n        if (didChangeAutoExpandNode) {\n            this.autoExpandDisposable.dispose();\n            this.autoExpandNode = targetNode;\n        }\n        if (typeof targetNode === 'undefined') {\n            return result;\n        }\n        if (didChangeAutoExpandNode && typeof result !== 'boolean' && result.autoExpand) {\n            this.autoExpandDisposable = disposableTimeout(() => {\n                const model = this.modelProvider();\n                const ref = model.getNodeLocation(targetNode);\n                if (model.isCollapsed(ref)) {\n                    model.setCollapsed(ref, false);\n                }\n                this.autoExpandNode = undefined;\n            }, 500, this.disposables);\n        }\n        if (typeof result === 'boolean' || !result.accept || typeof result.bubble === 'undefined' || result.feedback) {\n            if (!raw) {\n                const accept = typeof result === 'boolean' ? result : result.accept;\n                const effect = typeof result === 'boolean' ? undefined : result.effect;\n                return { accept, effect, feedback: [targetIndex] };\n            }\n            return result;\n        }\n        if (result.bubble === 1 /* TreeDragOverBubble.Up */) {\n            const model = this.modelProvider();\n            const ref = model.getNodeLocation(targetNode);\n            const parentRef = model.getParentNodeLocation(ref);\n            const parentNode = model.getNode(parentRef);\n            const parentIndex = parentRef && model.getListIndex(parentRef);\n            return this.onDragOver(data, parentNode, parentIndex, targetSector, originalEvent, false);\n        }\n        const model = this.modelProvider();\n        const ref = model.getNodeLocation(targetNode);\n        const start = model.getListIndex(ref);\n        const length = model.getListRenderCount(ref);\n        return { ...result, feedback: range(start, start + length) };\n    }\n    drop(data, targetNode, targetIndex, targetSector, originalEvent) {\n        this.autoExpandDisposable.dispose();\n        this.autoExpandNode = undefined;\n        this.dnd.drop(asTreeDragAndDropData(data), targetNode && targetNode.element, targetIndex, targetSector, originalEvent);\n    }\n    onDragEnd(originalEvent) {\n        this.dnd.onDragEnd?.(originalEvent);\n    }\n    dispose() {\n        this.disposables.dispose();\n        this.dnd.dispose();\n    }\n}\nfunction asListOptions(modelProvider, options) {\n    return options && {\n        ...options,\n        identityProvider: options.identityProvider && {\n            getId(el) {\n                return options.identityProvider.getId(el.element);\n            }\n        },\n        dnd: options.dnd && new TreeNodeListDragAndDrop(modelProvider, options.dnd),\n        multipleSelectionController: options.multipleSelectionController && {\n            isSelectionSingleChangeEvent(e) {\n                return options.multipleSelectionController.isSelectionSingleChangeEvent({ ...e, element: e.element });\n            },\n            isSelectionRangeChangeEvent(e) {\n                return options.multipleSelectionController.isSelectionRangeChangeEvent({ ...e, element: e.element });\n            }\n        },\n        accessibilityProvider: options.accessibilityProvider && {\n            ...options.accessibilityProvider,\n            getSetSize(node) {\n                const model = modelProvider();\n                const ref = model.getNodeLocation(node);\n                const parentRef = model.getParentNodeLocation(ref);\n                const parentNode = model.getNode(parentRef);\n                return parentNode.visibleChildrenCount;\n            },\n            getPosInSet(node) {\n                return node.visibleChildIndex + 1;\n            },\n            isChecked: options.accessibilityProvider && options.accessibilityProvider.isChecked ? (node) => {\n                return options.accessibilityProvider.isChecked(node.element);\n            } : undefined,\n            getRole: options.accessibilityProvider && options.accessibilityProvider.getRole ? (node) => {\n                return options.accessibilityProvider.getRole(node.element);\n            } : () => 'treeitem',\n            getAriaLabel(e) {\n                return options.accessibilityProvider.getAriaLabel(e.element);\n            },\n            getWidgetAriaLabel() {\n                return options.accessibilityProvider.getWidgetAriaLabel();\n            },\n            getWidgetRole: options.accessibilityProvider && options.accessibilityProvider.getWidgetRole ? () => options.accessibilityProvider.getWidgetRole() : () => 'tree',\n            getAriaLevel: options.accessibilityProvider && options.accessibilityProvider.getAriaLevel ? (node) => options.accessibilityProvider.getAriaLevel(node.element) : (node) => {\n                return node.depth;\n            },\n            getActiveDescendantId: options.accessibilityProvider.getActiveDescendantId && (node => {\n                return options.accessibilityProvider.getActiveDescendantId(node.element);\n            })\n        },\n        keyboardNavigationLabelProvider: options.keyboardNavigationLabelProvider && {\n            ...options.keyboardNavigationLabelProvider,\n            getKeyboardNavigationLabel(node) {\n                return options.keyboardNavigationLabelProvider.getKeyboardNavigationLabel(node.element);\n            }\n        }\n    };\n}\nexport class ComposedTreeDelegate {\n    constructor(delegate) {\n        this.delegate = delegate;\n    }\n    getHeight(element) {\n        return this.delegate.getHeight(element.element);\n    }\n    getTemplateId(element) {\n        return this.delegate.getTemplateId(element.element);\n    }\n    hasDynamicHeight(element) {\n        return !!this.delegate.hasDynamicHeight && this.delegate.hasDynamicHeight(element.element);\n    }\n    setDynamicHeight(element, height) {\n        this.delegate.setDynamicHeight?.(element.element, height);\n    }\n}\nexport var RenderIndentGuides;\n(function (RenderIndentGuides) {\n    RenderIndentGuides[\"None\"] = \"none\";\n    RenderIndentGuides[\"OnHover\"] = \"onHover\";\n    RenderIndentGuides[\"Always\"] = \"always\";\n})(RenderIndentGuides || (RenderIndentGuides = {}));\nclass EventCollection {\n    get elements() {\n        return this._elements;\n    }\n    constructor(onDidChange, _elements = []) {\n        this._elements = _elements;\n        this.disposables = new DisposableStore();\n        this.onDidChange = Event.forEach(onDidChange, elements => this._elements = elements, this.disposables);\n    }\n    dispose() {\n        this.disposables.dispose();\n    }\n}\nexport class TreeRenderer {\n    static { this.DefaultIndent = 8; }\n    constructor(renderer, modelProvider, onDidChangeCollapseState, activeNodes, renderedIndentGuides, options = {}) {\n        this.renderer = renderer;\n        this.modelProvider = modelProvider;\n        this.activeNodes = activeNodes;\n        this.renderedIndentGuides = renderedIndentGuides;\n        this.renderedElements = new Map();\n        this.renderedNodes = new Map();\n        this.indent = TreeRenderer.DefaultIndent;\n        this.hideTwistiesOfChildlessElements = false;\n        this.shouldRenderIndentGuides = false;\n        this.activeIndentNodes = new Set();\n        this.indentGuidesDisposable = Disposable.None;\n        this.disposables = new DisposableStore();\n        this.templateId = renderer.templateId;\n        this.updateOptions(options);\n        Event.map(onDidChangeCollapseState, e => e.node)(this.onDidChangeNodeTwistieState, this, this.disposables);\n        renderer.onDidChangeTwistieState?.(this.onDidChangeTwistieState, this, this.disposables);\n    }\n    updateOptions(options = {}) {\n        if (typeof options.indent !== 'undefined') {\n            const indent = clamp(options.indent, 0, 40);\n            if (indent !== this.indent) {\n                this.indent = indent;\n                for (const [node, templateData] of this.renderedNodes) {\n                    this.renderTreeElement(node, templateData);\n                }\n            }\n        }\n        if (typeof options.renderIndentGuides !== 'undefined') {\n            const shouldRenderIndentGuides = options.renderIndentGuides !== RenderIndentGuides.None;\n            if (shouldRenderIndentGuides !== this.shouldRenderIndentGuides) {\n                this.shouldRenderIndentGuides = shouldRenderIndentGuides;\n                for (const [node, templateData] of this.renderedNodes) {\n                    this._renderIndentGuides(node, templateData);\n                }\n                this.indentGuidesDisposable.dispose();\n                if (shouldRenderIndentGuides) {\n                    const disposables = new DisposableStore();\n                    this.activeNodes.onDidChange(this._onDidChangeActiveNodes, this, disposables);\n                    this.indentGuidesDisposable = disposables;\n                    this._onDidChangeActiveNodes(this.activeNodes.elements);\n                }\n            }\n        }\n        if (typeof options.hideTwistiesOfChildlessElements !== 'undefined') {\n            this.hideTwistiesOfChildlessElements = options.hideTwistiesOfChildlessElements;\n        }\n    }\n    renderTemplate(container) {\n        const el = append(container, $('.monaco-tl-row'));\n        const indent = append(el, $('.monaco-tl-indent'));\n        const twistie = append(el, $('.monaco-tl-twistie'));\n        const contents = append(el, $('.monaco-tl-contents'));\n        const templateData = this.renderer.renderTemplate(contents);\n        return { container, indent, twistie, indentGuidesDisposable: Disposable.None, templateData };\n    }\n    renderElement(node, index, templateData, height) {\n        this.renderedNodes.set(node, templateData);\n        this.renderedElements.set(node.element, node);\n        this.renderTreeElement(node, templateData);\n        this.renderer.renderElement(node, index, templateData.templateData, height);\n    }\n    disposeElement(node, index, templateData, height) {\n        templateData.indentGuidesDisposable.dispose();\n        this.renderer.disposeElement?.(node, index, templateData.templateData, height);\n        if (typeof height === 'number') {\n            this.renderedNodes.delete(node);\n            this.renderedElements.delete(node.element);\n        }\n    }\n    disposeTemplate(templateData) {\n        this.renderer.disposeTemplate(templateData.templateData);\n    }\n    onDidChangeTwistieState(element) {\n        const node = this.renderedElements.get(element);\n        if (!node) {\n            return;\n        }\n        this.onDidChangeNodeTwistieState(node);\n    }\n    onDidChangeNodeTwistieState(node) {\n        const templateData = this.renderedNodes.get(node);\n        if (!templateData) {\n            return;\n        }\n        this._onDidChangeActiveNodes(this.activeNodes.elements);\n        this.renderTreeElement(node, templateData);\n    }\n    renderTreeElement(node, templateData) {\n        const indent = TreeRenderer.DefaultIndent + (node.depth - 1) * this.indent;\n        templateData.twistie.style.paddingLeft = `${indent}px`;\n        templateData.indent.style.width = `${indent + this.indent - 16}px`;\n        if (node.collapsible) {\n            templateData.container.setAttribute('aria-expanded', String(!node.collapsed));\n        }\n        else {\n            templateData.container.removeAttribute('aria-expanded');\n        }\n        templateData.twistie.classList.remove(...ThemeIcon.asClassNameArray(Codicon.treeItemExpanded));\n        let twistieRendered = false;\n        if (this.renderer.renderTwistie) {\n            twistieRendered = this.renderer.renderTwistie(node.element, templateData.twistie);\n        }\n        if (node.collapsible && (!this.hideTwistiesOfChildlessElements || node.visibleChildrenCount > 0)) {\n            if (!twistieRendered) {\n                templateData.twistie.classList.add(...ThemeIcon.asClassNameArray(Codicon.treeItemExpanded));\n            }\n            templateData.twistie.classList.add('collapsible');\n            templateData.twistie.classList.toggle('collapsed', node.collapsed);\n        }\n        else {\n            templateData.twistie.classList.remove('collapsible', 'collapsed');\n        }\n        this._renderIndentGuides(node, templateData);\n    }\n    _renderIndentGuides(node, templateData) {\n        clearNode(templateData.indent);\n        templateData.indentGuidesDisposable.dispose();\n        if (!this.shouldRenderIndentGuides) {\n            return;\n        }\n        const disposableStore = new DisposableStore();\n        const model = this.modelProvider();\n        while (true) {\n            const ref = model.getNodeLocation(node);\n            const parentRef = model.getParentNodeLocation(ref);\n            if (!parentRef) {\n                break;\n            }\n            const parent = model.getNode(parentRef);\n            const guide = $('.indent-guide', { style: `width: ${this.indent}px` });\n            if (this.activeIndentNodes.has(parent)) {\n                guide.classList.add('active');\n            }\n            if (templateData.indent.childElementCount === 0) {\n                templateData.indent.appendChild(guide);\n            }\n            else {\n                templateData.indent.insertBefore(guide, templateData.indent.firstElementChild);\n            }\n            this.renderedIndentGuides.add(parent, guide);\n            disposableStore.add(toDisposable(() => this.renderedIndentGuides.delete(parent, guide)));\n            node = parent;\n        }\n        templateData.indentGuidesDisposable = disposableStore;\n    }\n    _onDidChangeActiveNodes(nodes) {\n        if (!this.shouldRenderIndentGuides) {\n            return;\n        }\n        const set = new Set();\n        const model = this.modelProvider();\n        nodes.forEach(node => {\n            const ref = model.getNodeLocation(node);\n            try {\n                const parentRef = model.getParentNodeLocation(ref);\n                if (node.collapsible && node.children.length > 0 && !node.collapsed) {\n                    set.add(node);\n                }\n                else if (parentRef) {\n                    set.add(model.getNode(parentRef));\n                }\n            }\n            catch {\n                // noop\n            }\n        });\n        this.activeIndentNodes.forEach(node => {\n            if (!set.has(node)) {\n                this.renderedIndentGuides.forEach(node, line => line.classList.remove('active'));\n            }\n        });\n        set.forEach(node => {\n            if (!this.activeIndentNodes.has(node)) {\n                this.renderedIndentGuides.forEach(node, line => line.classList.add('active'));\n            }\n        });\n        this.activeIndentNodes = set;\n    }\n    dispose() {\n        this.renderedNodes.clear();\n        this.renderedElements.clear();\n        this.indentGuidesDisposable.dispose();\n        dispose(this.disposables);\n    }\n}\nclass FindFilter {\n    get totalCount() { return this._totalCount; }\n    get matchCount() { return this._matchCount; }\n    constructor(tree, keyboardNavigationLabelProvider, _filter) {\n        this.tree = tree;\n        this.keyboardNavigationLabelProvider = keyboardNavigationLabelProvider;\n        this._filter = _filter;\n        this._totalCount = 0;\n        this._matchCount = 0;\n        this._pattern = '';\n        this._lowercasePattern = '';\n        this.disposables = new DisposableStore();\n        tree.onWillRefilter(this.reset, this, this.disposables);\n    }\n    filter(element, parentVisibility) {\n        let visibility = 1 /* TreeVisibility.Visible */;\n        if (this._filter) {\n            const result = this._filter.filter(element, parentVisibility);\n            if (typeof result === 'boolean') {\n                visibility = result ? 1 /* TreeVisibility.Visible */ : 0 /* TreeVisibility.Hidden */;\n            }\n            else if (isFilterResult(result)) {\n                visibility = getVisibleState(result.visibility);\n            }\n            else {\n                visibility = result;\n            }\n            if (visibility === 0 /* TreeVisibility.Hidden */) {\n                return false;\n            }\n        }\n        this._totalCount++;\n        if (!this._pattern) {\n            this._matchCount++;\n            return { data: FuzzyScore.Default, visibility };\n        }\n        const label = this.keyboardNavigationLabelProvider.getKeyboardNavigationLabel(element);\n        const labels = Array.isArray(label) ? label : [label];\n        for (const l of labels) {\n            const labelStr = l && l.toString();\n            if (typeof labelStr === 'undefined') {\n                return { data: FuzzyScore.Default, visibility };\n            }\n            let score;\n            if (this.tree.findMatchType === TreeFindMatchType.Contiguous) {\n                const index = labelStr.toLowerCase().indexOf(this._lowercasePattern);\n                if (index > -1) {\n                    score = [Number.MAX_SAFE_INTEGER, 0];\n                    for (let i = this._lowercasePattern.length; i > 0; i--) {\n                        score.push(index + i - 1);\n                    }\n                }\n            }\n            else {\n                score = fuzzyScore(this._pattern, this._lowercasePattern, 0, labelStr, labelStr.toLowerCase(), 0, { firstMatchCanBeWeak: true, boostFullMatch: true });\n            }\n            if (score) {\n                this._matchCount++;\n                return labels.length === 1 ?\n                    { data: score, visibility } :\n                    { data: { label: labelStr, score: score }, visibility };\n            }\n        }\n        if (this.tree.findMode === TreeFindMode.Filter) {\n            if (typeof this.tree.options.defaultFindVisibility === 'number') {\n                return this.tree.options.defaultFindVisibility;\n            }\n            else if (this.tree.options.defaultFindVisibility) {\n                return this.tree.options.defaultFindVisibility(element);\n            }\n            else {\n                return 2 /* TreeVisibility.Recurse */;\n            }\n        }\n        else {\n            return { data: FuzzyScore.Default, visibility };\n        }\n    }\n    reset() {\n        this._totalCount = 0;\n        this._matchCount = 0;\n    }\n    dispose() {\n        dispose(this.disposables);\n    }\n}\nexport class ModeToggle extends Toggle {\n    constructor(opts) {\n        super({\n            icon: Codicon.listFilter,\n            title: localize('filter', \"Filter\"),\n            isChecked: opts.isChecked ?? false,\n            hoverDelegate: opts.hoverDelegate ?? getDefaultHoverDelegate('element'),\n            inputActiveOptionBorder: opts.inputActiveOptionBorder,\n            inputActiveOptionForeground: opts.inputActiveOptionForeground,\n            inputActiveOptionBackground: opts.inputActiveOptionBackground\n        });\n    }\n}\nexport class FuzzyToggle extends Toggle {\n    constructor(opts) {\n        super({\n            icon: Codicon.searchFuzzy,\n            title: localize('fuzzySearch', \"Fuzzy Match\"),\n            isChecked: opts.isChecked ?? false,\n            hoverDelegate: opts.hoverDelegate ?? getDefaultHoverDelegate('element'),\n            inputActiveOptionBorder: opts.inputActiveOptionBorder,\n            inputActiveOptionForeground: opts.inputActiveOptionForeground,\n            inputActiveOptionBackground: opts.inputActiveOptionBackground\n        });\n    }\n}\nconst unthemedFindWidgetStyles = {\n    inputBoxStyles: unthemedInboxStyles,\n    toggleStyles: unthemedToggleStyles,\n    listFilterWidgetBackground: undefined,\n    listFilterWidgetNoMatchesOutline: undefined,\n    listFilterWidgetOutline: undefined,\n    listFilterWidgetShadow: undefined\n};\nexport var TreeFindMode;\n(function (TreeFindMode) {\n    TreeFindMode[TreeFindMode[\"Highlight\"] = 0] = \"Highlight\";\n    TreeFindMode[TreeFindMode[\"Filter\"] = 1] = \"Filter\";\n})(TreeFindMode || (TreeFindMode = {}));\nexport var TreeFindMatchType;\n(function (TreeFindMatchType) {\n    TreeFindMatchType[TreeFindMatchType[\"Fuzzy\"] = 0] = \"Fuzzy\";\n    TreeFindMatchType[TreeFindMatchType[\"Contiguous\"] = 1] = \"Contiguous\";\n})(TreeFindMatchType || (TreeFindMatchType = {}));\nclass FindWidget extends Disposable {\n    set mode(mode) {\n        this.modeToggle.checked = mode === TreeFindMode.Filter;\n        this.findInput.inputBox.setPlaceHolder(mode === TreeFindMode.Filter ? localize('type to filter', \"Type to filter\") : localize('type to search', \"Type to search\"));\n    }\n    set matchType(matchType) {\n        this.matchTypeToggle.checked = matchType === TreeFindMatchType.Fuzzy;\n    }\n    constructor(container, tree, contextViewProvider, mode, matchType, options) {\n        super();\n        this.tree = tree;\n        this.elements = h('.monaco-tree-type-filter', [\n            h('.monaco-tree-type-filter-grab.codicon.codicon-debug-gripper@grab', { tabIndex: 0 }),\n            h('.monaco-tree-type-filter-input@findInput'),\n            h('.monaco-tree-type-filter-actionbar@actionbar'),\n        ]);\n        this.width = 0;\n        this.right = 0;\n        this.top = 0;\n        this._onDidDisable = new Emitter();\n        container.appendChild(this.elements.root);\n        this._register(toDisposable(() => this.elements.root.remove()));\n        const styles = options?.styles ?? unthemedFindWidgetStyles;\n        if (styles.listFilterWidgetBackground) {\n            this.elements.root.style.backgroundColor = styles.listFilterWidgetBackground;\n        }\n        if (styles.listFilterWidgetShadow) {\n            this.elements.root.style.boxShadow = `0 0 8px 2px ${styles.listFilterWidgetShadow}`;\n        }\n        const toggleHoverDelegate = this._register(createInstantHoverDelegate());\n        this.modeToggle = this._register(new ModeToggle({ ...styles.toggleStyles, isChecked: mode === TreeFindMode.Filter, hoverDelegate: toggleHoverDelegate }));\n        this.matchTypeToggle = this._register(new FuzzyToggle({ ...styles.toggleStyles, isChecked: matchType === TreeFindMatchType.Fuzzy, hoverDelegate: toggleHoverDelegate }));\n        this.onDidChangeMode = Event.map(this.modeToggle.onChange, () => this.modeToggle.checked ? TreeFindMode.Filter : TreeFindMode.Highlight, this._store);\n        this.onDidChangeMatchType = Event.map(this.matchTypeToggle.onChange, () => this.matchTypeToggle.checked ? TreeFindMatchType.Fuzzy : TreeFindMatchType.Contiguous, this._store);\n        this.findInput = this._register(new FindInput(this.elements.findInput, contextViewProvider, {\n            label: localize('type to search', \"Type to search\"),\n            additionalToggles: [this.modeToggle, this.matchTypeToggle],\n            showCommonFindToggles: false,\n            inputBoxStyles: styles.inputBoxStyles,\n            toggleStyles: styles.toggleStyles,\n            history: options?.history\n        }));\n        this.actionbar = this._register(new ActionBar(this.elements.actionbar));\n        this.mode = mode;\n        const emitter = this._register(new DomEmitter(this.findInput.inputBox.inputElement, 'keydown'));\n        const onKeyDown = Event.chain(emitter.event, $ => $.map(e => new StandardKeyboardEvent(e)));\n        this._register(onKeyDown((e) => {\n            // Using equals() so we reserve modified keys for future use\n            if (e.equals(3 /* KeyCode.Enter */)) {\n                // This is the only keyboard way to return to the tree from a history item that isn't the last one\n                e.preventDefault();\n                e.stopPropagation();\n                this.findInput.inputBox.addToHistory();\n                this.tree.domFocus();\n                return;\n            }\n            if (e.equals(18 /* KeyCode.DownArrow */)) {\n                e.preventDefault();\n                e.stopPropagation();\n                if (this.findInput.inputBox.isAtLastInHistory() || this.findInput.inputBox.isNowhereInHistory()) {\n                    // Retain original pre-history DownArrow behavior\n                    this.findInput.inputBox.addToHistory();\n                    this.tree.domFocus();\n                }\n                else {\n                    // Downward through history\n                    this.findInput.inputBox.showNextValue();\n                }\n                return;\n            }\n            if (e.equals(16 /* KeyCode.UpArrow */)) {\n                e.preventDefault();\n                e.stopPropagation();\n                // Upward through history\n                this.findInput.inputBox.showPreviousValue();\n                return;\n            }\n        }));\n        const closeAction = this._register(new Action('close', localize('close', \"Close\"), 'codicon codicon-close', true, () => this.dispose()));\n        this.actionbar.push(closeAction, { icon: true, label: false });\n        const onGrabMouseDown = this._register(new DomEmitter(this.elements.grab, 'mousedown'));\n        this._register(onGrabMouseDown.event(e => {\n            const disposables = new DisposableStore();\n            const onWindowMouseMove = disposables.add(new DomEmitter(getWindow(e), 'mousemove'));\n            const onWindowMouseUp = disposables.add(new DomEmitter(getWindow(e), 'mouseup'));\n            const startRight = this.right;\n            const startX = e.pageX;\n            const startTop = this.top;\n            const startY = e.pageY;\n            this.elements.grab.classList.add('grabbing');\n            const transition = this.elements.root.style.transition;\n            this.elements.root.style.transition = 'unset';\n            const update = (e) => {\n                const deltaX = e.pageX - startX;\n                this.right = startRight - deltaX;\n                const deltaY = e.pageY - startY;\n                this.top = startTop + deltaY;\n                this.layout();\n            };\n            disposables.add(onWindowMouseMove.event(update));\n            disposables.add(onWindowMouseUp.event(e => {\n                update(e);\n                this.elements.grab.classList.remove('grabbing');\n                this.elements.root.style.transition = transition;\n                disposables.dispose();\n            }));\n        }));\n        const onGrabKeyDown = Event.chain(this._register(new DomEmitter(this.elements.grab, 'keydown')).event, $ => $.map(e => new StandardKeyboardEvent(e)));\n        this._register(onGrabKeyDown((e) => {\n            let right;\n            let top;\n            if (e.keyCode === 15 /* KeyCode.LeftArrow */) {\n                right = Number.POSITIVE_INFINITY;\n            }\n            else if (e.keyCode === 17 /* KeyCode.RightArrow */) {\n                right = 0;\n            }\n            else if (e.keyCode === 10 /* KeyCode.Space */) {\n                right = this.right === 0 ? Number.POSITIVE_INFINITY : 0;\n            }\n            if (e.keyCode === 16 /* KeyCode.UpArrow */) {\n                top = 0;\n            }\n            else if (e.keyCode === 18 /* KeyCode.DownArrow */) {\n                top = Number.POSITIVE_INFINITY;\n            }\n            if (right !== undefined) {\n                e.preventDefault();\n                e.stopPropagation();\n                this.right = right;\n                this.layout();\n            }\n            if (top !== undefined) {\n                e.preventDefault();\n                e.stopPropagation();\n                this.top = top;\n                const transition = this.elements.root.style.transition;\n                this.elements.root.style.transition = 'unset';\n                this.layout();\n                setTimeout(() => {\n                    this.elements.root.style.transition = transition;\n                }, 0);\n            }\n        }));\n        this.onDidChangeValue = this.findInput.onDidChange;\n    }\n    layout(width = this.width) {\n        this.width = width;\n        this.right = clamp(this.right, 0, Math.max(0, width - 212));\n        this.elements.root.style.right = `${this.right}px`;\n        this.top = clamp(this.top, 0, 24);\n        this.elements.root.style.top = `${this.top}px`;\n    }\n    showMessage(message) {\n        this.findInput.showMessage(message);\n    }\n    clearMessage() {\n        this.findInput.clearMessage();\n    }\n    async dispose() {\n        this._onDidDisable.fire();\n        this.elements.root.classList.add('disabled');\n        await timeout(300);\n        super.dispose();\n    }\n}\nclass FindController {\n    get pattern() { return this._pattern; }\n    get mode() { return this._mode; }\n    set mode(mode) {\n        if (mode === this._mode) {\n            return;\n        }\n        this._mode = mode;\n        if (this.widget) {\n            this.widget.mode = this._mode;\n        }\n        this.tree.refilter();\n        this.render();\n        this._onDidChangeMode.fire(mode);\n    }\n    get matchType() { return this._matchType; }\n    set matchType(matchType) {\n        if (matchType === this._matchType) {\n            return;\n        }\n        this._matchType = matchType;\n        if (this.widget) {\n            this.widget.matchType = this._matchType;\n        }\n        this.tree.refilter();\n        this.render();\n        this._onDidChangeMatchType.fire(matchType);\n    }\n    constructor(tree, model, view, filter, contextViewProvider, options = {}) {\n        this.tree = tree;\n        this.view = view;\n        this.filter = filter;\n        this.contextViewProvider = contextViewProvider;\n        this.options = options;\n        this._pattern = '';\n        this.width = 0;\n        this._onDidChangeMode = new Emitter();\n        this.onDidChangeMode = this._onDidChangeMode.event;\n        this._onDidChangeMatchType = new Emitter();\n        this.onDidChangeMatchType = this._onDidChangeMatchType.event;\n        this._onDidChangePattern = new Emitter();\n        this._onDidChangeOpenState = new Emitter();\n        this.onDidChangeOpenState = this._onDidChangeOpenState.event;\n        this.enabledDisposables = new DisposableStore();\n        this.disposables = new DisposableStore();\n        this._mode = tree.options.defaultFindMode ?? TreeFindMode.Highlight;\n        this._matchType = tree.options.defaultFindMatchType ?? TreeFindMatchType.Fuzzy;\n        model.onDidSplice(this.onDidSpliceModel, this, this.disposables);\n    }\n    updateOptions(optionsUpdate = {}) {\n        if (optionsUpdate.defaultFindMode !== undefined) {\n            this.mode = optionsUpdate.defaultFindMode;\n        }\n        if (optionsUpdate.defaultFindMatchType !== undefined) {\n            this.matchType = optionsUpdate.defaultFindMatchType;\n        }\n    }\n    onDidSpliceModel() {\n        if (!this.widget || this.pattern.length === 0) {\n            return;\n        }\n        this.tree.refilter();\n        this.render();\n    }\n    render() {\n        const noMatches = this.filter.totalCount > 0 && this.filter.matchCount === 0;\n        if (this.pattern && noMatches) {\n            alert(localize('replFindNoResults', \"No results\"));\n            if (this.tree.options.showNotFoundMessage ?? true) {\n                this.widget?.showMessage({ type: 2 /* MessageType.WARNING */, content: localize('not found', \"No elements found.\") });\n            }\n            else {\n                this.widget?.showMessage({ type: 2 /* MessageType.WARNING */ });\n            }\n        }\n        else {\n            this.widget?.clearMessage();\n            if (this.pattern) {\n                alert(localize('replFindResults', \"{0} results\", this.filter.matchCount));\n            }\n        }\n    }\n    shouldAllowFocus(node) {\n        if (!this.widget || !this.pattern) {\n            return true;\n        }\n        if (this.filter.totalCount > 0 && this.filter.matchCount <= 1) {\n            return true;\n        }\n        return !FuzzyScore.isDefault(node.filterData);\n    }\n    layout(width) {\n        this.width = width;\n        this.widget?.layout(width);\n    }\n    dispose() {\n        this._history = undefined;\n        this._onDidChangePattern.dispose();\n        this.enabledDisposables.dispose();\n        this.disposables.dispose();\n    }\n}\nfunction stickyScrollNodeStateEquals(node1, node2) {\n    return node1.position === node2.position && stickyScrollNodeEquals(node1, node2);\n}\nfunction stickyScrollNodeEquals(node1, node2) {\n    return node1.node.element === node2.node.element &&\n        node1.startIndex === node2.startIndex &&\n        node1.height === node2.height &&\n        node1.endIndex === node2.endIndex;\n}\nclass StickyScrollState {\n    constructor(stickyNodes = []) {\n        this.stickyNodes = stickyNodes;\n    }\n    get count() { return this.stickyNodes.length; }\n    equal(state) {\n        return equals(this.stickyNodes, state.stickyNodes, stickyScrollNodeStateEquals);\n    }\n    lastNodePartiallyVisible() {\n        if (this.count === 0) {\n            return false;\n        }\n        const lastStickyNode = this.stickyNodes[this.count - 1];\n        if (this.count === 1) {\n            return lastStickyNode.position !== 0;\n        }\n        const secondLastStickyNode = this.stickyNodes[this.count - 2];\n        return secondLastStickyNode.position + secondLastStickyNode.height !== lastStickyNode.position;\n    }\n    animationStateChanged(previousState) {\n        if (!equals(this.stickyNodes, previousState.stickyNodes, stickyScrollNodeEquals)) {\n            return false;\n        }\n        if (this.count === 0) {\n            return false;\n        }\n        const lastStickyNode = this.stickyNodes[this.count - 1];\n        const previousLastStickyNode = previousState.stickyNodes[previousState.count - 1];\n        return lastStickyNode.position !== previousLastStickyNode.position;\n    }\n}\nclass DefaultStickyScrollDelegate {\n    constrainStickyScrollNodes(stickyNodes, stickyScrollMaxItemCount, maxWidgetHeight) {\n        for (let i = 0; i < stickyNodes.length; i++) {\n            const stickyNode = stickyNodes[i];\n            const stickyNodeBottom = stickyNode.position + stickyNode.height;\n            if (stickyNodeBottom > maxWidgetHeight || i >= stickyScrollMaxItemCount) {\n                return stickyNodes.slice(0, i);\n            }\n        }\n        return stickyNodes;\n    }\n}\nclass StickyScrollController extends Disposable {\n    constructor(tree, model, view, renderers, treeDelegate, options = {}) {\n        super();\n        this.tree = tree;\n        this.model = model;\n        this.view = view;\n        this.treeDelegate = treeDelegate;\n        this.maxWidgetViewRatio = 0.4;\n        const stickyScrollOptions = this.validateStickySettings(options);\n        this.stickyScrollMaxItemCount = stickyScrollOptions.stickyScrollMaxItemCount;\n        this.stickyScrollDelegate = options.stickyScrollDelegate ?? new DefaultStickyScrollDelegate();\n        this._widget = this._register(new StickyScrollWidget(view.getScrollableElement(), view, tree, renderers, treeDelegate, options.accessibilityProvider));\n        this.onDidChangeHasFocus = this._widget.onDidChangeHasFocus;\n        this.onContextMenu = this._widget.onContextMenu;\n        this._register(view.onDidScroll(() => this.update()));\n        this._register(view.onDidChangeContentHeight(() => this.update()));\n        this._register(tree.onDidChangeCollapseState(() => this.update()));\n        this.update();\n    }\n    get height() {\n        return this._widget.height;\n    }\n    getNodeAtHeight(height) {\n        let index;\n        if (height === 0) {\n            index = this.view.firstVisibleIndex;\n        }\n        else {\n            index = this.view.indexAt(height + this.view.scrollTop);\n        }\n        if (index < 0 || index >= this.view.length) {\n            return undefined;\n        }\n        return this.view.element(index);\n    }\n    update() {\n        const firstVisibleNode = this.getNodeAtHeight(0);\n        // Don't render anything if there are no elements\n        if (!firstVisibleNode || this.tree.scrollTop === 0) {\n            this._widget.setState(undefined);\n            return;\n        }\n        const stickyState = this.findStickyState(firstVisibleNode);\n        this._widget.setState(stickyState);\n    }\n    findStickyState(firstVisibleNode) {\n        const stickyNodes = [];\n        let firstVisibleNodeUnderWidget = firstVisibleNode;\n        let stickyNodesHeight = 0;\n        let nextStickyNode = this.getNextStickyNode(firstVisibleNodeUnderWidget, undefined, stickyNodesHeight);\n        while (nextStickyNode) {\n            stickyNodes.push(nextStickyNode);\n            stickyNodesHeight += nextStickyNode.height;\n            if (stickyNodes.length <= this.stickyScrollMaxItemCount) {\n                firstVisibleNodeUnderWidget = this.getNextVisibleNode(nextStickyNode);\n                if (!firstVisibleNodeUnderWidget) {\n                    break;\n                }\n            }\n            nextStickyNode = this.getNextStickyNode(firstVisibleNodeUnderWidget, nextStickyNode.node, stickyNodesHeight);\n        }\n        const contrainedStickyNodes = this.constrainStickyNodes(stickyNodes);\n        return contrainedStickyNodes.length ? new StickyScrollState(contrainedStickyNodes) : undefined;\n    }\n    getNextVisibleNode(previousStickyNode) {\n        return this.getNodeAtHeight(previousStickyNode.position + previousStickyNode.height);\n    }\n    getNextStickyNode(firstVisibleNodeUnderWidget, previousStickyNode, stickyNodesHeight) {\n        const nextStickyNode = this.getAncestorUnderPrevious(firstVisibleNodeUnderWidget, previousStickyNode);\n        if (!nextStickyNode) {\n            return undefined;\n        }\n        if (nextStickyNode === firstVisibleNodeUnderWidget) {\n            if (!this.nodeIsUncollapsedParent(firstVisibleNodeUnderWidget)) {\n                return undefined;\n            }\n            if (this.nodeTopAlignsWithStickyNodesBottom(firstVisibleNodeUnderWidget, stickyNodesHeight)) {\n                return undefined;\n            }\n        }\n        return this.createStickyScrollNode(nextStickyNode, stickyNodesHeight);\n    }\n    nodeTopAlignsWithStickyNodesBottom(node, stickyNodesHeight) {\n        const nodeIndex = this.getNodeIndex(node);\n        const elementTop = this.view.getElementTop(nodeIndex);\n        const stickyPosition = stickyNodesHeight;\n        return this.view.scrollTop === elementTop - stickyPosition;\n    }\n    createStickyScrollNode(node, currentStickyNodesHeight) {\n        const height = this.treeDelegate.getHeight(node);\n        const { startIndex, endIndex } = this.getNodeRange(node);\n        const position = this.calculateStickyNodePosition(endIndex, currentStickyNodesHeight, height);\n        return { node, position, height, startIndex, endIndex };\n    }\n    getAncestorUnderPrevious(node, previousAncestor = undefined) {\n        let currentAncestor = node;\n        let parentOfcurrentAncestor = this.getParentNode(currentAncestor);\n        while (parentOfcurrentAncestor) {\n            if (parentOfcurrentAncestor === previousAncestor) {\n                return currentAncestor;\n            }\n            currentAncestor = parentOfcurrentAncestor;\n            parentOfcurrentAncestor = this.getParentNode(currentAncestor);\n        }\n        if (previousAncestor === undefined) {\n            return currentAncestor;\n        }\n        return undefined;\n    }\n    calculateStickyNodePosition(lastDescendantIndex, stickyRowPositionTop, stickyNodeHeight) {\n        let lastChildRelativeTop = this.view.getRelativeTop(lastDescendantIndex);\n        // If the last descendant is only partially visible at the top of the view, getRelativeTop() returns null\n        // In that case, utilize the next node's relative top to calculate the sticky node's position\n        if (lastChildRelativeTop === null && this.view.firstVisibleIndex === lastDescendantIndex && lastDescendantIndex + 1 < this.view.length) {\n            const nodeHeight = this.treeDelegate.getHeight(this.view.element(lastDescendantIndex));\n            const nextNodeRelativeTop = this.view.getRelativeTop(lastDescendantIndex + 1);\n            lastChildRelativeTop = nextNodeRelativeTop ? nextNodeRelativeTop - nodeHeight / this.view.renderHeight : null;\n        }\n        if (lastChildRelativeTop === null) {\n            return stickyRowPositionTop;\n        }\n        const lastChildNode = this.view.element(lastDescendantIndex);\n        const lastChildHeight = this.treeDelegate.getHeight(lastChildNode);\n        const topOfLastChild = lastChildRelativeTop * this.view.renderHeight;\n        const bottomOfLastChild = topOfLastChild + lastChildHeight;\n        if (stickyRowPositionTop + stickyNodeHeight > bottomOfLastChild && stickyRowPositionTop <= bottomOfLastChild) {\n            return bottomOfLastChild - stickyNodeHeight;\n        }\n        return stickyRowPositionTop;\n    }\n    constrainStickyNodes(stickyNodes) {\n        if (stickyNodes.length === 0) {\n            return [];\n        }\n        // Check if sticky nodes need to be constrained\n        const maximumStickyWidgetHeight = this.view.renderHeight * this.maxWidgetViewRatio;\n        const lastStickyNode = stickyNodes[stickyNodes.length - 1];\n        if (stickyNodes.length <= this.stickyScrollMaxItemCount && lastStickyNode.position + lastStickyNode.height <= maximumStickyWidgetHeight) {\n            return stickyNodes;\n        }\n        // constrain sticky nodes\n        const constrainedStickyNodes = this.stickyScrollDelegate.constrainStickyScrollNodes(stickyNodes, this.stickyScrollMaxItemCount, maximumStickyWidgetHeight);\n        if (!constrainedStickyNodes.length) {\n            return [];\n        }\n        // Validate constraints\n        const lastConstrainedStickyNode = constrainedStickyNodes[constrainedStickyNodes.length - 1];\n        if (constrainedStickyNodes.length > this.stickyScrollMaxItemCount || lastConstrainedStickyNode.position + lastConstrainedStickyNode.height > maximumStickyWidgetHeight) {\n            throw new Error('stickyScrollDelegate violates constraints');\n        }\n        return constrainedStickyNodes;\n    }\n    getParentNode(node) {\n        const nodeLocation = this.model.getNodeLocation(node);\n        const parentLocation = this.model.getParentNodeLocation(nodeLocation);\n        return parentLocation ? this.model.getNode(parentLocation) : undefined;\n    }\n    nodeIsUncollapsedParent(node) {\n        const nodeLocation = this.model.getNodeLocation(node);\n        return this.model.getListRenderCount(nodeLocation) > 1;\n    }\n    getNodeIndex(node) {\n        const nodeLocation = this.model.getNodeLocation(node);\n        const nodeIndex = this.model.getListIndex(nodeLocation);\n        return nodeIndex;\n    }\n    getNodeRange(node) {\n        const nodeLocation = this.model.getNodeLocation(node);\n        const startIndex = this.model.getListIndex(nodeLocation);\n        if (startIndex < 0) {\n            throw new Error('Node not found in tree');\n        }\n        const renderCount = this.model.getListRenderCount(nodeLocation);\n        const endIndex = startIndex + renderCount - 1;\n        return { startIndex, endIndex };\n    }\n    nodePositionTopBelowWidget(node) {\n        const ancestors = [];\n        let currentAncestor = this.getParentNode(node);\n        while (currentAncestor) {\n            ancestors.push(currentAncestor);\n            currentAncestor = this.getParentNode(currentAncestor);\n        }\n        let widgetHeight = 0;\n        for (let i = 0; i < ancestors.length && i < this.stickyScrollMaxItemCount; i++) {\n            widgetHeight += this.treeDelegate.getHeight(ancestors[i]);\n        }\n        return widgetHeight;\n    }\n    domFocus() {\n        this._widget.domFocus();\n    }\n    // Whether sticky scroll was the last focused part in the tree or not\n    focusedLast() {\n        return this._widget.focusedLast();\n    }\n    updateOptions(optionsUpdate = {}) {\n        if (!optionsUpdate.stickyScrollMaxItemCount) {\n            return;\n        }\n        const validatedOptions = this.validateStickySettings(optionsUpdate);\n        if (this.stickyScrollMaxItemCount !== validatedOptions.stickyScrollMaxItemCount) {\n            this.stickyScrollMaxItemCount = validatedOptions.stickyScrollMaxItemCount;\n            this.update();\n        }\n    }\n    validateStickySettings(options) {\n        let stickyScrollMaxItemCount = 7;\n        if (typeof options.stickyScrollMaxItemCount === 'number') {\n            stickyScrollMaxItemCount = Math.max(options.stickyScrollMaxItemCount, 1);\n        }\n        return { stickyScrollMaxItemCount };\n    }\n}\nclass StickyScrollWidget {\n    constructor(container, view, tree, treeRenderers, treeDelegate, accessibilityProvider) {\n        this.view = view;\n        this.tree = tree;\n        this.treeRenderers = treeRenderers;\n        this.treeDelegate = treeDelegate;\n        this.accessibilityProvider = accessibilityProvider;\n        this._previousElements = [];\n        this._previousStateDisposables = new DisposableStore();\n        this._rootDomNode = $('.monaco-tree-sticky-container.empty');\n        container.appendChild(this._rootDomNode);\n        const shadow = $('.monaco-tree-sticky-container-shadow');\n        this._rootDomNode.appendChild(shadow);\n        this.stickyScrollFocus = new StickyScrollFocus(this._rootDomNode, view);\n        this.onDidChangeHasFocus = this.stickyScrollFocus.onDidChangeHasFocus;\n        this.onContextMenu = this.stickyScrollFocus.onContextMenu;\n    }\n    get height() {\n        if (!this._previousState) {\n            return 0;\n        }\n        const lastElement = this._previousState.stickyNodes[this._previousState.count - 1];\n        return lastElement.position + lastElement.height;\n    }\n    setState(state) {\n        const wasVisible = !!this._previousState && this._previousState.count > 0;\n        const isVisible = !!state && state.count > 0;\n        // If state has not changed, do nothing\n        if ((!wasVisible && !isVisible) || (wasVisible && isVisible && this._previousState.equal(state))) {\n            return;\n        }\n        // Update visibility of the widget if changed\n        if (wasVisible !== isVisible) {\n            this.setVisible(isVisible);\n        }\n        if (!isVisible) {\n            this._previousState = undefined;\n            this._previousElements = [];\n            this._previousStateDisposables.clear();\n            return;\n        }\n        const lastStickyNode = state.stickyNodes[state.count - 1];\n        // If the new state is only a change in the last node's position, update the position of the last element\n        if (this._previousState && state.animationStateChanged(this._previousState)) {\n            this._previousElements[this._previousState.count - 1].style.top = `${lastStickyNode.position}px`;\n        }\n        // create new dom elements\n        else {\n            this._previousStateDisposables.clear();\n            const elements = Array(state.count);\n            for (let stickyIndex = state.count - 1; stickyIndex >= 0; stickyIndex--) {\n                const stickyNode = state.stickyNodes[stickyIndex];\n                const { element, disposable } = this.createElement(stickyNode, stickyIndex, state.count);\n                elements[stickyIndex] = element;\n                this._rootDomNode.appendChild(element);\n                this._previousStateDisposables.add(disposable);\n            }\n            this.stickyScrollFocus.updateElements(elements, state);\n            this._previousElements = elements;\n        }\n        this._previousState = state;\n        // Set the height of the widget to the bottom of the last sticky node\n        this._rootDomNode.style.height = `${lastStickyNode.position + lastStickyNode.height}px`;\n    }\n    createElement(stickyNode, stickyIndex, stickyNodesTotal) {\n        const nodeIndex = stickyNode.startIndex;\n        // Sticky element container\n        const stickyElement = document.createElement('div');\n        stickyElement.style.top = `${stickyNode.position}px`;\n        if (this.tree.options.setRowHeight !== false) {\n            stickyElement.style.height = `${stickyNode.height}px`;\n        }\n        if (this.tree.options.setRowLineHeight !== false) {\n            stickyElement.style.lineHeight = `${stickyNode.height}px`;\n        }\n        stickyElement.classList.add('monaco-tree-sticky-row');\n        stickyElement.classList.add('monaco-list-row');\n        stickyElement.setAttribute('data-index', `${nodeIndex}`);\n        stickyElement.setAttribute('data-parity', nodeIndex % 2 === 0 ? 'even' : 'odd');\n        stickyElement.setAttribute('id', this.view.getElementID(nodeIndex));\n        const accessibilityDisposable = this.setAccessibilityAttributes(stickyElement, stickyNode.node.element, stickyIndex, stickyNodesTotal);\n        // Get the renderer for the node\n        const nodeTemplateId = this.treeDelegate.getTemplateId(stickyNode.node);\n        const renderer = this.treeRenderers.find((renderer) => renderer.templateId === nodeTemplateId);\n        if (!renderer) {\n            throw new Error(`No renderer found for template id ${nodeTemplateId}`);\n        }\n        // To make sure we do not influence the original node, we create a copy of the node\n        // We need to check if it is already a unique instance of the node by the delegate\n        let nodeCopy = stickyNode.node;\n        if (nodeCopy === this.tree.getNode(this.tree.getNodeLocation(stickyNode.node))) {\n            nodeCopy = new Proxy(stickyNode.node, {});\n        }\n        // Render the element\n        const templateData = renderer.renderTemplate(stickyElement);\n        renderer.renderElement(nodeCopy, stickyNode.startIndex, templateData, stickyNode.height);\n        // Remove the element from the DOM when state is disposed\n        const disposable = toDisposable(() => {\n            accessibilityDisposable.dispose();\n            renderer.disposeElement(nodeCopy, stickyNode.startIndex, templateData, stickyNode.height);\n            renderer.disposeTemplate(templateData);\n            stickyElement.remove();\n        });\n        return { element: stickyElement, disposable };\n    }\n    setAccessibilityAttributes(container, element, stickyIndex, stickyNodesTotal) {\n        if (!this.accessibilityProvider) {\n            return Disposable.None;\n        }\n        if (this.accessibilityProvider.getSetSize) {\n            container.setAttribute('aria-setsize', String(this.accessibilityProvider.getSetSize(element, stickyIndex, stickyNodesTotal)));\n        }\n        if (this.accessibilityProvider.getPosInSet) {\n            container.setAttribute('aria-posinset', String(this.accessibilityProvider.getPosInSet(element, stickyIndex)));\n        }\n        if (this.accessibilityProvider.getRole) {\n            container.setAttribute('role', this.accessibilityProvider.getRole(element) ?? 'treeitem');\n        }\n        const ariaLabel = this.accessibilityProvider.getAriaLabel(element);\n        const observable = (ariaLabel && typeof ariaLabel !== 'string') ? ariaLabel : constObservable(ariaLabel);\n        const result = autorun(reader => {\n            const value = reader.readObservable(observable);\n            if (value) {\n                container.setAttribute('aria-label', value);\n            }\n            else {\n                container.removeAttribute('aria-label');\n            }\n        });\n        if (typeof ariaLabel === 'string') {\n        }\n        else if (ariaLabel) {\n            container.setAttribute('aria-label', ariaLabel.get());\n        }\n        const ariaLevel = this.accessibilityProvider.getAriaLevel && this.accessibilityProvider.getAriaLevel(element);\n        if (typeof ariaLevel === 'number') {\n            container.setAttribute('aria-level', `${ariaLevel}`);\n        }\n        // Sticky Scroll elements can not be selected\n        container.setAttribute('aria-selected', String(false));\n        return result;\n    }\n    setVisible(visible) {\n        this._rootDomNode.classList.toggle('empty', !visible);\n        if (!visible) {\n            this.stickyScrollFocus.updateElements([], undefined);\n        }\n    }\n    domFocus() {\n        this.stickyScrollFocus.domFocus();\n    }\n    focusedLast() {\n        return this.stickyScrollFocus.focusedLast();\n    }\n    dispose() {\n        this.stickyScrollFocus.dispose();\n        this._previousStateDisposables.dispose();\n        this._rootDomNode.remove();\n    }\n}\nclass StickyScrollFocus extends Disposable {\n    get domHasFocus() { return this._domHasFocus; }\n    set domHasFocus(hasFocus) {\n        if (hasFocus !== this._domHasFocus) {\n            this._onDidChangeHasFocus.fire(hasFocus);\n            this._domHasFocus = hasFocus;\n        }\n    }\n    constructor(container, view) {\n        super();\n        this.container = container;\n        this.view = view;\n        this.focusedIndex = -1;\n        this.elements = [];\n        this._onDidChangeHasFocus = new Emitter();\n        this.onDidChangeHasFocus = this._onDidChangeHasFocus.event;\n        this._onContextMenu = new Emitter();\n        this.onContextMenu = this._onContextMenu.event;\n        this._domHasFocus = false;\n        this._register(addDisposableListener(this.container, 'focus', () => this.onFocus()));\n        this._register(addDisposableListener(this.container, 'blur', () => this.onBlur()));\n        this._register(this.view.onDidFocus(() => this.toggleStickyScrollFocused(false)));\n        this._register(this.view.onKeyDown((e) => this.onKeyDown(e)));\n        this._register(this.view.onMouseDown((e) => this.onMouseDown(e)));\n        this._register(this.view.onContextMenu((e) => this.handleContextMenu(e)));\n    }\n    handleContextMenu(e) {\n        const target = e.browserEvent.target;\n        if (!isStickyScrollContainer(target) && !isStickyScrollElement(target)) {\n            if (this.focusedLast()) {\n                this.view.domFocus();\n            }\n            return;\n        }\n        // The list handles the context menu triggered by a mouse event\n        // In that case only set the focus of the element clicked and leave the rest to the list to handle\n        if (!isKeyboardEvent(e.browserEvent)) {\n            if (!this.state) {\n                throw new Error('Context menu should not be triggered when state is undefined');\n            }\n            const stickyIndex = this.state.stickyNodes.findIndex(stickyNode => stickyNode.node.element === e.element?.element);\n            if (stickyIndex === -1) {\n                throw new Error('Context menu should not be triggered when element is not in sticky scroll widget');\n            }\n            this.container.focus();\n            this.setFocus(stickyIndex);\n            return;\n        }\n        if (!this.state || this.focusedIndex < 0) {\n            throw new Error('Context menu key should not be triggered when focus is not in sticky scroll widget');\n        }\n        const stickyNode = this.state.stickyNodes[this.focusedIndex];\n        const element = stickyNode.node.element;\n        const anchor = this.elements[this.focusedIndex];\n        this._onContextMenu.fire({ element, anchor, browserEvent: e.browserEvent, isStickyScroll: true });\n    }\n    onKeyDown(e) {\n        // Sticky Scroll Navigation\n        if (this.domHasFocus && this.state) {\n            // Move up\n            if (e.key === 'ArrowUp') {\n                this.setFocusedElement(Math.max(0, this.focusedIndex - 1));\n                e.preventDefault();\n                e.stopPropagation();\n            }\n            // Move down, if last sticky node is focused, move focus into first child of last sticky node\n            else if (e.key === 'ArrowDown' || e.key === 'ArrowRight') {\n                if (this.focusedIndex >= this.state.count - 1) {\n                    const nodeIndexToFocus = this.state.stickyNodes[this.state.count - 1].startIndex + 1;\n                    this.view.domFocus();\n                    this.view.setFocus([nodeIndexToFocus]);\n                    this.scrollNodeUnderWidget(nodeIndexToFocus, this.state);\n                }\n                else {\n                    this.setFocusedElement(this.focusedIndex + 1);\n                }\n                e.preventDefault();\n                e.stopPropagation();\n            }\n        }\n    }\n    onMouseDown(e) {\n        const target = e.browserEvent.target;\n        if (!isStickyScrollContainer(target) && !isStickyScrollElement(target)) {\n            return;\n        }\n        e.browserEvent.preventDefault();\n        e.browserEvent.stopPropagation();\n    }\n    updateElements(elements, state) {\n        if (state && state.count === 0) {\n            throw new Error('Sticky scroll state must be undefined when there are no sticky nodes');\n        }\n        if (state && state.count !== elements.length) {\n            throw new Error('Sticky scroll focus received illigel state');\n        }\n        const previousIndex = this.focusedIndex;\n        this.removeFocus();\n        this.elements = elements;\n        this.state = state;\n        if (state) {\n            const newFocusedIndex = clamp(previousIndex, 0, state.count - 1);\n            this.setFocus(newFocusedIndex);\n        }\n        else {\n            if (this.domHasFocus) {\n                this.view.domFocus();\n            }\n        }\n        // must come last as it calls blur()\n        this.container.tabIndex = state ? 0 : -1;\n    }\n    setFocusedElement(stickyIndex) {\n        // doesn't imply that the widget has (or will have) focus\n        const state = this.state;\n        if (!state) {\n            throw new Error('Cannot set focus when state is undefined');\n        }\n        this.setFocus(stickyIndex);\n        if (stickyIndex < state.count - 1) {\n            return;\n        }\n        // If the last sticky node is not fully visible, scroll it into view\n        if (state.lastNodePartiallyVisible()) {\n            const lastStickyNode = state.stickyNodes[stickyIndex];\n            this.scrollNodeUnderWidget(lastStickyNode.endIndex + 1, state);\n        }\n    }\n    scrollNodeUnderWidget(nodeIndex, state) {\n        const lastStickyNode = state.stickyNodes[state.count - 1];\n        const secondLastStickyNode = state.count > 1 ? state.stickyNodes[state.count - 2] : undefined;\n        const elementScrollTop = this.view.getElementTop(nodeIndex);\n        const elementTargetViewTop = secondLastStickyNode ? secondLastStickyNode.position + secondLastStickyNode.height + lastStickyNode.height : lastStickyNode.height;\n        this.view.scrollTop = elementScrollTop - elementTargetViewTop;\n    }\n    domFocus() {\n        if (!this.state) {\n            throw new Error('Cannot focus when state is undefined');\n        }\n        this.container.focus();\n    }\n    focusedLast() {\n        if (!this.state) {\n            return false;\n        }\n        return this.view.getHTMLElement().classList.contains('sticky-scroll-focused');\n    }\n    removeFocus() {\n        if (this.focusedIndex === -1) {\n            return;\n        }\n        this.toggleElementFocus(this.elements[this.focusedIndex], false);\n        this.focusedIndex = -1;\n    }\n    setFocus(newFocusIndex) {\n        if (0 > newFocusIndex) {\n            throw new Error('addFocus() can not remove focus');\n        }\n        if (!this.state && newFocusIndex >= 0) {\n            throw new Error('Cannot set focus index when state is undefined');\n        }\n        if (this.state && newFocusIndex >= this.state.count) {\n            throw new Error('Cannot set focus index to an index that does not exist');\n        }\n        const oldIndex = this.focusedIndex;\n        if (oldIndex >= 0) {\n            this.toggleElementFocus(this.elements[oldIndex], false);\n        }\n        if (newFocusIndex >= 0) {\n            this.toggleElementFocus(this.elements[newFocusIndex], true);\n        }\n        this.focusedIndex = newFocusIndex;\n    }\n    toggleElementFocus(element, focused) {\n        this.toggleElementActiveFocus(element, focused && this.domHasFocus);\n        this.toggleElementPassiveFocus(element, focused);\n    }\n    toggleCurrentElementActiveFocus(focused) {\n        if (this.focusedIndex === -1) {\n            return;\n        }\n        this.toggleElementActiveFocus(this.elements[this.focusedIndex], focused);\n    }\n    toggleElementActiveFocus(element, focused) {\n        // active focus is set when sticky scroll has focus\n        element.classList.toggle('focused', focused);\n    }\n    toggleElementPassiveFocus(element, focused) {\n        // passive focus allows to show focus when sticky scroll does not have focus\n        // for example when the context menu has focus\n        element.classList.toggle('passive-focused', focused);\n    }\n    toggleStickyScrollFocused(focused) {\n        // Weather the last focus in the view was sticky scroll and not the list\n        // Is only removed when the focus is back in the tree an no longer in sticky scroll\n        this.view.getHTMLElement().classList.toggle('sticky-scroll-focused', focused);\n    }\n    onFocus() {\n        if (!this.state || this.elements.length === 0) {\n            throw new Error('Cannot focus when state is undefined or elements are empty');\n        }\n        this.domHasFocus = true;\n        this.toggleStickyScrollFocused(true);\n        this.toggleCurrentElementActiveFocus(true);\n        if (this.focusedIndex === -1) {\n            this.setFocus(0);\n        }\n    }\n    onBlur() {\n        this.domHasFocus = false;\n        this.toggleCurrentElementActiveFocus(false);\n    }\n    dispose() {\n        this.toggleStickyScrollFocused(false);\n        this._onDidChangeHasFocus.fire(false);\n        super.dispose();\n    }\n}\nfunction asTreeMouseEvent(event) {\n    let target = TreeMouseEventTarget.Unknown;\n    if (hasParentWithClass(event.browserEvent.target, 'monaco-tl-twistie', 'monaco-tl-row')) {\n        target = TreeMouseEventTarget.Twistie;\n    }\n    else if (hasParentWithClass(event.browserEvent.target, 'monaco-tl-contents', 'monaco-tl-row')) {\n        target = TreeMouseEventTarget.Element;\n    }\n    else if (hasParentWithClass(event.browserEvent.target, 'monaco-tree-type-filter', 'monaco-list')) {\n        target = TreeMouseEventTarget.Filter;\n    }\n    return {\n        browserEvent: event.browserEvent,\n        element: event.element ? event.element.element : null,\n        target\n    };\n}\nfunction asTreeContextMenuEvent(event) {\n    const isStickyScroll = isStickyScrollContainer(event.browserEvent.target);\n    return {\n        element: event.element ? event.element.element : null,\n        browserEvent: event.browserEvent,\n        anchor: event.anchor,\n        isStickyScroll\n    };\n}\nfunction dfs(node, fn) {\n    fn(node);\n    node.children.forEach(child => dfs(child, fn));\n}\n/**\n * The trait concept needs to exist at the tree level, because collapsed\n * tree nodes will not be known by the list.\n */\nclass Trait {\n    get nodeSet() {\n        if (!this._nodeSet) {\n            this._nodeSet = this.createNodeSet();\n        }\n        return this._nodeSet;\n    }\n    constructor(getFirstViewElementWithTrait, identityProvider) {\n        this.getFirstViewElementWithTrait = getFirstViewElementWithTrait;\n        this.identityProvider = identityProvider;\n        this.nodes = [];\n        this._onDidChange = new Emitter();\n        this.onDidChange = this._onDidChange.event;\n    }\n    set(nodes, browserEvent) {\n        if (!browserEvent?.__forceEvent && equals(this.nodes, nodes)) {\n            return;\n        }\n        this._set(nodes, false, browserEvent);\n    }\n    _set(nodes, silent, browserEvent) {\n        this.nodes = [...nodes];\n        this.elements = undefined;\n        this._nodeSet = undefined;\n        if (!silent) {\n            const that = this;\n            this._onDidChange.fire({ get elements() { return that.get(); }, browserEvent });\n        }\n    }\n    get() {\n        if (!this.elements) {\n            this.elements = this.nodes.map(node => node.element);\n        }\n        return [...this.elements];\n    }\n    getNodes() {\n        return this.nodes;\n    }\n    has(node) {\n        return this.nodeSet.has(node);\n    }\n    onDidModelSplice({ insertedNodes, deletedNodes }) {\n        if (!this.identityProvider) {\n            const set = this.createNodeSet();\n            const visit = (node) => set.delete(node);\n            deletedNodes.forEach(node => dfs(node, visit));\n            this.set([...set.values()]);\n            return;\n        }\n        const deletedNodesIdSet = new Set();\n        const deletedNodesVisitor = (node) => deletedNodesIdSet.add(this.identityProvider.getId(node.element).toString());\n        deletedNodes.forEach(node => dfs(node, deletedNodesVisitor));\n        const insertedNodesMap = new Map();\n        const insertedNodesVisitor = (node) => insertedNodesMap.set(this.identityProvider.getId(node.element).toString(), node);\n        insertedNodes.forEach(node => dfs(node, insertedNodesVisitor));\n        const nodes = [];\n        for (const node of this.nodes) {\n            const id = this.identityProvider.getId(node.element).toString();\n            const wasDeleted = deletedNodesIdSet.has(id);\n            if (!wasDeleted) {\n                nodes.push(node);\n            }\n            else {\n                const insertedNode = insertedNodesMap.get(id);\n                if (insertedNode && insertedNode.visible) {\n                    nodes.push(insertedNode);\n                }\n            }\n        }\n        if (this.nodes.length > 0 && nodes.length === 0) {\n            const node = this.getFirstViewElementWithTrait();\n            if (node) {\n                nodes.push(node);\n            }\n        }\n        this._set(nodes, true);\n    }\n    createNodeSet() {\n        const set = new Set();\n        for (const node of this.nodes) {\n            set.add(node);\n        }\n        return set;\n    }\n}\nclass TreeNodeListMouseController extends MouseController {\n    constructor(list, tree, stickyScrollProvider) {\n        super(list);\n        this.tree = tree;\n        this.stickyScrollProvider = stickyScrollProvider;\n    }\n    onViewPointer(e) {\n        if (isButton(e.browserEvent.target) ||\n            isInputElement(e.browserEvent.target) ||\n            isMonacoEditor(e.browserEvent.target)) {\n            return;\n        }\n        if (e.browserEvent.isHandledByList) {\n            return;\n        }\n        const node = e.element;\n        if (!node) {\n            return super.onViewPointer(e);\n        }\n        if (this.isSelectionRangeChangeEvent(e) || this.isSelectionSingleChangeEvent(e)) {\n            return super.onViewPointer(e);\n        }\n        const target = e.browserEvent.target;\n        const onTwistie = target.classList.contains('monaco-tl-twistie')\n            || (target.classList.contains('monaco-icon-label') && target.classList.contains('folder-icon') && e.browserEvent.offsetX < 16);\n        const isStickyElement = isStickyScrollElement(e.browserEvent.target);\n        let expandOnlyOnTwistieClick = false;\n        if (isStickyElement) {\n            expandOnlyOnTwistieClick = true;\n        }\n        else if (typeof this.tree.expandOnlyOnTwistieClick === 'function') {\n            expandOnlyOnTwistieClick = this.tree.expandOnlyOnTwistieClick(node.element);\n        }\n        else {\n            expandOnlyOnTwistieClick = !!this.tree.expandOnlyOnTwistieClick;\n        }\n        if (!isStickyElement) {\n            if (expandOnlyOnTwistieClick && !onTwistie && e.browserEvent.detail !== 2) {\n                return super.onViewPointer(e);\n            }\n            if (!this.tree.expandOnDoubleClick && e.browserEvent.detail === 2) {\n                return super.onViewPointer(e);\n            }\n        }\n        else {\n            this.handleStickyScrollMouseEvent(e, node);\n        }\n        if (node.collapsible && (!isStickyElement || onTwistie)) {\n            const location = this.tree.getNodeLocation(node);\n            const recursive = e.browserEvent.altKey;\n            this.tree.setFocus([location]);\n            this.tree.toggleCollapsed(location, recursive);\n            if (onTwistie) {\n                // Do not set this before calling a handler on the super class, because it will reject it as handled\n                e.browserEvent.isHandledByList = true;\n                return;\n            }\n        }\n        if (!isStickyElement) {\n            super.onViewPointer(e);\n        }\n    }\n    handleStickyScrollMouseEvent(e, node) {\n        if (isMonacoCustomToggle(e.browserEvent.target) || isActionItem(e.browserEvent.target)) {\n            return;\n        }\n        const stickyScrollController = this.stickyScrollProvider();\n        if (!stickyScrollController) {\n            throw new Error('Sticky scroll controller not found');\n        }\n        const nodeIndex = this.list.indexOf(node);\n        const elementScrollTop = this.list.getElementTop(nodeIndex);\n        const elementTargetViewTop = stickyScrollController.nodePositionTopBelowWidget(node);\n        this.tree.scrollTop = elementScrollTop - elementTargetViewTop;\n        this.list.domFocus();\n        this.list.setFocus([nodeIndex]);\n        this.list.setSelection([nodeIndex]);\n    }\n    onDoubleClick(e) {\n        const onTwistie = e.browserEvent.target.classList.contains('monaco-tl-twistie');\n        if (onTwistie || !this.tree.expandOnDoubleClick) {\n            return;\n        }\n        if (e.browserEvent.isHandledByList) {\n            return;\n        }\n        super.onDoubleClick(e);\n    }\n    // to make sure dom focus is not stolen (for example with context menu)\n    onMouseDown(e) {\n        const target = e.browserEvent.target;\n        if (!isStickyScrollContainer(target) && !isStickyScrollElement(target)) {\n            super.onMouseDown(e);\n            return;\n        }\n    }\n    onContextMenu(e) {\n        const target = e.browserEvent.target;\n        if (!isStickyScrollContainer(target) && !isStickyScrollElement(target)) {\n            super.onContextMenu(e);\n            return;\n        }\n    }\n}\n/**\n * We use this List subclass to restore selection and focus as nodes\n * get rendered in the list, possibly due to a node expand() call.\n */\nclass TreeNodeList extends List {\n    constructor(user, container, virtualDelegate, renderers, focusTrait, selectionTrait, anchorTrait, options) {\n        super(user, container, virtualDelegate, renderers, options);\n        this.focusTrait = focusTrait;\n        this.selectionTrait = selectionTrait;\n        this.anchorTrait = anchorTrait;\n    }\n    createMouseController(options) {\n        return new TreeNodeListMouseController(this, options.tree, options.stickyScrollProvider);\n    }\n    splice(start, deleteCount, elements = []) {\n        super.splice(start, deleteCount, elements);\n        if (elements.length === 0) {\n            return;\n        }\n        const additionalFocus = [];\n        const additionalSelection = [];\n        let anchor;\n        elements.forEach((node, index) => {\n            if (this.focusTrait.has(node)) {\n                additionalFocus.push(start + index);\n            }\n            if (this.selectionTrait.has(node)) {\n                additionalSelection.push(start + index);\n            }\n            if (this.anchorTrait.has(node)) {\n                anchor = start + index;\n            }\n        });\n        if (additionalFocus.length > 0) {\n            super.setFocus(distinct([...super.getFocus(), ...additionalFocus]));\n        }\n        if (additionalSelection.length > 0) {\n            super.setSelection(distinct([...super.getSelection(), ...additionalSelection]));\n        }\n        if (typeof anchor === 'number') {\n            super.setAnchor(anchor);\n        }\n    }\n    setFocus(indexes, browserEvent, fromAPI = false) {\n        super.setFocus(indexes, browserEvent);\n        if (!fromAPI) {\n            this.focusTrait.set(indexes.map(i => this.element(i)), browserEvent);\n        }\n    }\n    setSelection(indexes, browserEvent, fromAPI = false) {\n        super.setSelection(indexes, browserEvent);\n        if (!fromAPI) {\n            this.selectionTrait.set(indexes.map(i => this.element(i)), browserEvent);\n        }\n    }\n    setAnchor(index, fromAPI = false) {\n        super.setAnchor(index);\n        if (!fromAPI) {\n            if (typeof index === 'undefined') {\n                this.anchorTrait.set([]);\n            }\n            else {\n                this.anchorTrait.set([this.element(index)]);\n            }\n        }\n    }\n}\nexport class AbstractTree {\n    get onDidScroll() { return this.view.onDidScroll; }\n    get onDidChangeFocus() { return this.eventBufferer.wrapEvent(this.focus.onDidChange); }\n    get onDidChangeSelection() { return this.eventBufferer.wrapEvent(this.selection.onDidChange); }\n    get onMouseDblClick() { return Event.filter(Event.map(this.view.onMouseDblClick, asTreeMouseEvent), e => e.target !== TreeMouseEventTarget.Filter); }\n    get onMouseOver() { return Event.map(this.view.onMouseOver, asTreeMouseEvent); }\n    get onMouseOut() { return Event.map(this.view.onMouseOut, asTreeMouseEvent); }\n    get onContextMenu() { return Event.any(Event.filter(Event.map(this.view.onContextMenu, asTreeContextMenuEvent), e => !e.isStickyScroll), this.stickyScrollController?.onContextMenu ?? Event.None); }\n    get onPointer() { return Event.map(this.view.onPointer, asTreeMouseEvent); }\n    get onKeyDown() { return this.view.onKeyDown; }\n    get onDidFocus() { return this.view.onDidFocus; }\n    get onDidChangeModel() { return Event.signal(this.model.onDidSplice); }\n    get onDidChangeCollapseState() { return this.model.onDidChangeCollapseState; }\n    get findMode() { return this.findController?.mode ?? TreeFindMode.Highlight; }\n    set findMode(findMode) { if (this.findController) {\n        this.findController.mode = findMode;\n    } }\n    get findMatchType() { return this.findController?.matchType ?? TreeFindMatchType.Fuzzy; }\n    set findMatchType(findFuzzy) { if (this.findController) {\n        this.findController.matchType = findFuzzy;\n    } }\n    get expandOnDoubleClick() { return typeof this._options.expandOnDoubleClick === 'undefined' ? true : this._options.expandOnDoubleClick; }\n    get expandOnlyOnTwistieClick() { return typeof this._options.expandOnlyOnTwistieClick === 'undefined' ? true : this._options.expandOnlyOnTwistieClick; }\n    get onDidDispose() { return this.view.onDidDispose; }\n    constructor(_user, container, delegate, renderers, _options = {}) {\n        this._user = _user;\n        this._options = _options;\n        this.eventBufferer = new EventBufferer();\n        this.onDidChangeFindOpenState = Event.None;\n        this.onDidChangeStickyScrollFocused = Event.None;\n        this.disposables = new DisposableStore();\n        this._onWillRefilter = new Emitter();\n        this.onWillRefilter = this._onWillRefilter.event;\n        this._onDidUpdateOptions = new Emitter();\n        this.treeDelegate = new ComposedTreeDelegate(delegate);\n        const onDidChangeCollapseStateRelay = new Relay();\n        const onDidChangeActiveNodes = new Relay();\n        const activeNodes = this.disposables.add(new EventCollection(onDidChangeActiveNodes.event));\n        const renderedIndentGuides = new SetMap();\n        this.renderers = renderers.map(r => new TreeRenderer(r, () => this.model, onDidChangeCollapseStateRelay.event, activeNodes, renderedIndentGuides, _options));\n        for (const r of this.renderers) {\n            this.disposables.add(r);\n        }\n        let filter;\n        if (_options.keyboardNavigationLabelProvider) {\n            filter = new FindFilter(this, _options.keyboardNavigationLabelProvider, _options.filter);\n            _options = { ..._options, filter: filter }; // TODO need typescript help here\n            this.disposables.add(filter);\n        }\n        this.focus = new Trait(() => this.view.getFocusedElements()[0], _options.identityProvider);\n        this.selection = new Trait(() => this.view.getSelectedElements()[0], _options.identityProvider);\n        this.anchor = new Trait(() => this.view.getAnchorElement(), _options.identityProvider);\n        this.view = new TreeNodeList(_user, container, this.treeDelegate, this.renderers, this.focus, this.selection, this.anchor, { ...asListOptions(() => this.model, _options), tree: this, stickyScrollProvider: () => this.stickyScrollController });\n        this.model = this.createModel(_user, this.view, _options);\n        onDidChangeCollapseStateRelay.input = this.model.onDidChangeCollapseState;\n        const onDidModelSplice = Event.forEach(this.model.onDidSplice, e => {\n            this.eventBufferer.bufferEvents(() => {\n                this.focus.onDidModelSplice(e);\n                this.selection.onDidModelSplice(e);\n            });\n        }, this.disposables);\n        // Make sure the `forEach` always runs\n        onDidModelSplice(() => null, null, this.disposables);\n        // Active nodes can change when the model changes or when focus or selection change.\n        // We debounce it with 0 delay since these events may fire in the same stack and we only\n        // want to run this once. It also doesn't matter if it runs on the next tick since it's only\n        // a nice to have UI feature.\n        const activeNodesEmitter = this.disposables.add(new Emitter());\n        const activeNodesDebounce = this.disposables.add(new Delayer(0));\n        this.disposables.add(Event.any(onDidModelSplice, this.focus.onDidChange, this.selection.onDidChange)(() => {\n            activeNodesDebounce.trigger(() => {\n                const set = new Set();\n                for (const node of this.focus.getNodes()) {\n                    set.add(node);\n                }\n                for (const node of this.selection.getNodes()) {\n                    set.add(node);\n                }\n                activeNodesEmitter.fire([...set.values()]);\n            });\n        }));\n        onDidChangeActiveNodes.input = activeNodesEmitter.event;\n        if (_options.keyboardSupport !== false) {\n            const onKeyDown = Event.chain(this.view.onKeyDown, $ => $.filter(e => !isInputElement(e.target))\n                .map(e => new StandardKeyboardEvent(e)));\n            Event.chain(onKeyDown, $ => $.filter(e => e.keyCode === 15 /* KeyCode.LeftArrow */))(this.onLeftArrow, this, this.disposables);\n            Event.chain(onKeyDown, $ => $.filter(e => e.keyCode === 17 /* KeyCode.RightArrow */))(this.onRightArrow, this, this.disposables);\n            Event.chain(onKeyDown, $ => $.filter(e => e.keyCode === 10 /* KeyCode.Space */))(this.onSpace, this, this.disposables);\n        }\n        if ((_options.findWidgetEnabled ?? true) && _options.keyboardNavigationLabelProvider && _options.contextViewProvider) {\n            const opts = this.options.findWidgetStyles ? { styles: this.options.findWidgetStyles } : undefined;\n            this.findController = new FindController(this, this.model, this.view, filter, _options.contextViewProvider, opts);\n            this.focusNavigationFilter = node => this.findController.shouldAllowFocus(node);\n            this.onDidChangeFindOpenState = this.findController.onDidChangeOpenState;\n            this.disposables.add(this.findController);\n            this.onDidChangeFindMode = this.findController.onDidChangeMode;\n            this.onDidChangeFindMatchType = this.findController.onDidChangeMatchType;\n        }\n        else {\n            this.onDidChangeFindMode = Event.None;\n            this.onDidChangeFindMatchType = Event.None;\n        }\n        if (_options.enableStickyScroll) {\n            this.stickyScrollController = new StickyScrollController(this, this.model, this.view, this.renderers, this.treeDelegate, _options);\n            this.onDidChangeStickyScrollFocused = this.stickyScrollController.onDidChangeHasFocus;\n        }\n        this.styleElement = createStyleSheet(this.view.getHTMLElement());\n        this.getHTMLElement().classList.toggle('always', this._options.renderIndentGuides === RenderIndentGuides.Always);\n    }\n    updateOptions(optionsUpdate = {}) {\n        this._options = { ...this._options, ...optionsUpdate };\n        for (const renderer of this.renderers) {\n            renderer.updateOptions(optionsUpdate);\n        }\n        this.view.updateOptions(this._options);\n        this.findController?.updateOptions(optionsUpdate);\n        this.updateStickyScroll(optionsUpdate);\n        this._onDidUpdateOptions.fire(this._options);\n        this.getHTMLElement().classList.toggle('always', this._options.renderIndentGuides === RenderIndentGuides.Always);\n    }\n    get options() {\n        return this._options;\n    }\n    updateStickyScroll(optionsUpdate) {\n        if (!this.stickyScrollController && this._options.enableStickyScroll) {\n            this.stickyScrollController = new StickyScrollController(this, this.model, this.view, this.renderers, this.treeDelegate, this._options);\n            this.onDidChangeStickyScrollFocused = this.stickyScrollController.onDidChangeHasFocus;\n        }\n        else if (this.stickyScrollController && !this._options.enableStickyScroll) {\n            this.onDidChangeStickyScrollFocused = Event.None;\n            this.stickyScrollController.dispose();\n            this.stickyScrollController = undefined;\n        }\n        this.stickyScrollController?.updateOptions(optionsUpdate);\n    }\n    // Widget\n    getHTMLElement() {\n        return this.view.getHTMLElement();\n    }\n    get scrollTop() {\n        return this.view.scrollTop;\n    }\n    set scrollTop(scrollTop) {\n        this.view.scrollTop = scrollTop;\n    }\n    get scrollHeight() {\n        return this.view.scrollHeight;\n    }\n    get renderHeight() {\n        return this.view.renderHeight;\n    }\n    get ariaLabel() {\n        return this.view.ariaLabel;\n    }\n    set ariaLabel(value) {\n        this.view.ariaLabel = value;\n    }\n    domFocus() {\n        if (this.stickyScrollController?.focusedLast()) {\n            this.stickyScrollController.domFocus();\n        }\n        else {\n            this.view.domFocus();\n        }\n    }\n    layout(height, width) {\n        this.view.layout(height, width);\n        if (isNumber(width)) {\n            this.findController?.layout(width);\n        }\n    }\n    style(styles) {\n        const suffix = `.${this.view.domId}`;\n        const content = [];\n        if (styles.treeIndentGuidesStroke) {\n            content.push(`.monaco-list${suffix}:hover .monaco-tl-indent > .indent-guide, .monaco-list${suffix}.always .monaco-tl-indent > .indent-guide  { border-color: ${styles.treeInactiveIndentGuidesStroke}; }`);\n            content.push(`.monaco-list${suffix} .monaco-tl-indent > .indent-guide.active { border-color: ${styles.treeIndentGuidesStroke}; }`);\n        }\n        // Sticky Scroll Background\n        const stickyScrollBackground = styles.treeStickyScrollBackground ?? styles.listBackground;\n        if (stickyScrollBackground) {\n            content.push(`.monaco-list${suffix} .monaco-scrollable-element .monaco-tree-sticky-container { background-color: ${stickyScrollBackground}; }`);\n            content.push(`.monaco-list${suffix} .monaco-scrollable-element .monaco-tree-sticky-container .monaco-tree-sticky-row { background-color: ${stickyScrollBackground}; }`);\n        }\n        // Sticky Scroll Border\n        if (styles.treeStickyScrollBorder) {\n            content.push(`.monaco-list${suffix} .monaco-scrollable-element .monaco-tree-sticky-container { border-bottom: 1px solid ${styles.treeStickyScrollBorder}; }`);\n        }\n        // Sticky Scroll Shadow\n        if (styles.treeStickyScrollShadow) {\n            content.push(`.monaco-list${suffix} .monaco-scrollable-element .monaco-tree-sticky-container .monaco-tree-sticky-container-shadow { box-shadow: ${styles.treeStickyScrollShadow} 0 6px 6px -6px inset; height: 3px; }`);\n        }\n        // Sticky Scroll Focus\n        if (styles.listFocusForeground) {\n            content.push(`.monaco-list${suffix}.sticky-scroll-focused .monaco-scrollable-element .monaco-tree-sticky-container:focus .monaco-list-row.focused { color: ${styles.listFocusForeground}; }`);\n            content.push(`.monaco-list${suffix}:not(.sticky-scroll-focused) .monaco-scrollable-element .monaco-tree-sticky-container .monaco-list-row.focused { color: inherit; }`);\n        }\n        // Sticky Scroll Focus Outlines\n        const focusAndSelectionOutline = asCssValueWithDefault(styles.listFocusAndSelectionOutline, asCssValueWithDefault(styles.listSelectionOutline, styles.listFocusOutline ?? ''));\n        if (focusAndSelectionOutline) { // default: listFocusOutline\n            content.push(`.monaco-list${suffix}.sticky-scroll-focused .monaco-scrollable-element .monaco-tree-sticky-container:focus .monaco-list-row.focused.selected { outline: 1px solid ${focusAndSelectionOutline}; outline-offset: -1px;}`);\n            content.push(`.monaco-list${suffix}:not(.sticky-scroll-focused) .monaco-scrollable-element .monaco-tree-sticky-container .monaco-list-row.focused.selected { outline: inherit;}`);\n        }\n        if (styles.listFocusOutline) { // default: set\n            content.push(`.monaco-list${suffix}.sticky-scroll-focused .monaco-scrollable-element .monaco-tree-sticky-container:focus .monaco-list-row.focused { outline: 1px solid ${styles.listFocusOutline}; outline-offset: -1px; }`);\n            content.push(`.monaco-list${suffix}:not(.sticky-scroll-focused) .monaco-scrollable-element .monaco-tree-sticky-container .monaco-list-row.focused { outline: inherit; }`);\n            content.push(`.monaco-workbench.context-menu-visible .monaco-list${suffix}.last-focused.sticky-scroll-focused .monaco-scrollable-element .monaco-tree-sticky-container .monaco-list-row.passive-focused { outline: 1px solid ${styles.listFocusOutline}; outline-offset: -1px; }`);\n            content.push(`.monaco-workbench.context-menu-visible .monaco-list${suffix}.last-focused.sticky-scroll-focused .monaco-list-rows .monaco-list-row.focused { outline: inherit; }`);\n            content.push(`.monaco-workbench.context-menu-visible .monaco-list${suffix}.last-focused:not(.sticky-scroll-focused) .monaco-tree-sticky-container .monaco-list-rows .monaco-list-row.focused { outline: inherit; }`);\n        }\n        this.styleElement.textContent = content.join('\\n');\n        this.view.style(styles);\n    }\n    // Tree navigation\n    getParentElement(location) {\n        const parentRef = this.model.getParentNodeLocation(location);\n        const parentNode = this.model.getNode(parentRef);\n        return parentNode.element;\n    }\n    getFirstElementChild(location) {\n        return this.model.getFirstElementChild(location);\n    }\n    // Tree\n    getNode(location) {\n        return this.model.getNode(location);\n    }\n    getNodeLocation(node) {\n        return this.model.getNodeLocation(node);\n    }\n    collapse(location, recursive = false) {\n        return this.model.setCollapsed(location, true, recursive);\n    }\n    expand(location, recursive = false) {\n        return this.model.setCollapsed(location, false, recursive);\n    }\n    toggleCollapsed(location, recursive = false) {\n        return this.model.setCollapsed(location, undefined, recursive);\n    }\n    isCollapsible(location) {\n        return this.model.isCollapsible(location);\n    }\n    setCollapsible(location, collapsible) {\n        return this.model.setCollapsible(location, collapsible);\n    }\n    isCollapsed(location) {\n        return this.model.isCollapsed(location);\n    }\n    refilter() {\n        this._onWillRefilter.fire(undefined);\n        this.model.refilter();\n    }\n    setSelection(elements, browserEvent) {\n        this.eventBufferer.bufferEvents(() => {\n            const nodes = elements.map(e => this.model.getNode(e));\n            this.selection.set(nodes, browserEvent);\n            const indexes = elements.map(e => this.model.getListIndex(e)).filter(i => i > -1);\n            this.view.setSelection(indexes, browserEvent, true);\n        });\n    }\n    getSelection() {\n        return this.selection.get();\n    }\n    setFocus(elements, browserEvent) {\n        this.eventBufferer.bufferEvents(() => {\n            const nodes = elements.map(e => this.model.getNode(e));\n            this.focus.set(nodes, browserEvent);\n            const indexes = elements.map(e => this.model.getListIndex(e)).filter(i => i > -1);\n            this.view.setFocus(indexes, browserEvent, true);\n        });\n    }\n    focusNext(n = 1, loop = false, browserEvent, filter = (isKeyboardEvent(browserEvent) && browserEvent.altKey) ? undefined : this.focusNavigationFilter) {\n        this.view.focusNext(n, loop, browserEvent, filter);\n    }\n    focusPrevious(n = 1, loop = false, browserEvent, filter = (isKeyboardEvent(browserEvent) && browserEvent.altKey) ? undefined : this.focusNavigationFilter) {\n        this.view.focusPrevious(n, loop, browserEvent, filter);\n    }\n    focusNextPage(browserEvent, filter = (isKeyboardEvent(browserEvent) && browserEvent.altKey) ? undefined : this.focusNavigationFilter) {\n        return this.view.focusNextPage(browserEvent, filter);\n    }\n    focusPreviousPage(browserEvent, filter = (isKeyboardEvent(browserEvent) && browserEvent.altKey) ? undefined : this.focusNavigationFilter) {\n        return this.view.focusPreviousPage(browserEvent, filter, () => this.stickyScrollController?.height ?? 0);\n    }\n    focusLast(browserEvent, filter = (isKeyboardEvent(browserEvent) && browserEvent.altKey) ? undefined : this.focusNavigationFilter) {\n        this.view.focusLast(browserEvent, filter);\n    }\n    focusFirst(browserEvent, filter = (isKeyboardEvent(browserEvent) && browserEvent.altKey) ? undefined : this.focusNavigationFilter) {\n        this.view.focusFirst(browserEvent, filter);\n    }\n    getFocus() {\n        return this.focus.get();\n    }\n    reveal(location, relativeTop) {\n        this.model.expandTo(location);\n        const index = this.model.getListIndex(location);\n        if (index === -1) {\n            return;\n        }\n        if (!this.stickyScrollController) {\n            this.view.reveal(index, relativeTop);\n        }\n        else {\n            const paddingTop = this.stickyScrollController.nodePositionTopBelowWidget(this.getNode(location));\n            this.view.reveal(index, relativeTop, paddingTop);\n        }\n    }\n    // List\n    onLeftArrow(e) {\n        e.preventDefault();\n        e.stopPropagation();\n        const nodes = this.view.getFocusedElements();\n        if (nodes.length === 0) {\n            return;\n        }\n        const node = nodes[0];\n        const location = this.model.getNodeLocation(node);\n        const didChange = this.model.setCollapsed(location, true);\n        if (!didChange) {\n            const parentLocation = this.model.getParentNodeLocation(location);\n            if (!parentLocation) {\n                return;\n            }\n            const parentListIndex = this.model.getListIndex(parentLocation);\n            this.view.reveal(parentListIndex);\n            this.view.setFocus([parentListIndex]);\n        }\n    }\n    onRightArrow(e) {\n        e.preventDefault();\n        e.stopPropagation();\n        const nodes = this.view.getFocusedElements();\n        if (nodes.length === 0) {\n            return;\n        }\n        const node = nodes[0];\n        const location = this.model.getNodeLocation(node);\n        const didChange = this.model.setCollapsed(location, false);\n        if (!didChange) {\n            if (!node.children.some(child => child.visible)) {\n                return;\n            }\n            const [focusedIndex] = this.view.getFocus();\n            const firstChildIndex = focusedIndex + 1;\n            this.view.reveal(firstChildIndex);\n            this.view.setFocus([firstChildIndex]);\n        }\n    }\n    onSpace(e) {\n        e.preventDefault();\n        e.stopPropagation();\n        const nodes = this.view.getFocusedElements();\n        if (nodes.length === 0) {\n            return;\n        }\n        const node = nodes[0];\n        const location = this.model.getNodeLocation(node);\n        const recursive = e.browserEvent.altKey;\n        this.model.setCollapsed(location, undefined, recursive);\n    }\n    dispose() {\n        dispose(this.disposables);\n        this.stickyScrollController?.dispose();\n        this.view.dispose();\n    }\n}\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA,SAASA,CAAC,EAAEC,MAAM,EAAEC,SAAS,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,CAAC,EAAEC,kBAAkB,EAAEC,qBAAqB,EAAEC,eAAe,EAAEC,qBAAqB,QAAQ,cAAc;AACtK,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,SAASC,qBAAqB,QAAQ,wBAAwB;AAC9D,SAASC,SAAS,QAAQ,2BAA2B;AACrD,SAASC,SAAS,QAAQ,2BAA2B;AACrD,SAASC,mBAAmB,QAAQ,yBAAyB;AAC7D,SAASC,uBAAuB,QAAQ,qBAAqB;AAC7D,SAASC,YAAY,EAAEC,QAAQ,EAAEC,cAAc,EAAEC,oBAAoB,EAAEC,cAAc,EAAEC,uBAAuB,EAAEC,qBAAqB,EAAEC,IAAI,EAAEC,eAAe,QAAQ,uBAAuB;AAC3L,SAASC,MAAM,EAAEC,oBAAoB,QAAQ,qBAAqB;AAClE,SAASC,eAAe,EAAEC,cAAc,QAAQ,qBAAqB;AACrE,SAASC,oBAAoB,QAAQ,WAAW;AAChD,SAASC,MAAM,QAAQ,4BAA4B;AACnD,SAASC,QAAQ,EAAEC,MAAM,EAAEC,KAAK,QAAQ,2BAA2B;AACnE,SAASC,OAAO,EAAEC,iBAAiB,EAAEC,OAAO,QAAQ,0BAA0B;AAC9E,SAASC,OAAO,QAAQ,6BAA6B;AACrD,SAASC,SAAS,QAAQ,8BAA8B;AACxD,SAASC,MAAM,QAAQ,wBAAwB;AAC/C,SAASC,OAAO,EAAEC,KAAK,EAAEC,aAAa,EAAEC,KAAK,QAAQ,0BAA0B;AAC/E,SAASC,UAAU,EAAEC,UAAU,QAAQ,4BAA4B;AACnE,SAASC,UAAU,EAAEC,eAAe,EAAEC,OAAO,EAAEC,YAAY,QAAQ,8BAA8B;AACjG,SAASC,KAAK,QAAQ,4BAA4B;AAClD,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,OAAO,kBAAkB;AACzB,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,SAASC,0BAA0B,EAAEC,uBAAuB,QAAQ,kCAAkC;AACtG,SAASC,OAAO,EAAEC,eAAe,QAAQ,+BAA+B;AACxE,SAASC,KAAK,QAAQ,iBAAiB;AACvC,MAAMC,2BAA2B,SAAS3C,uBAAuB,CAAC;EAC9D4C,WAAWA,CAACC,IAAI,EAAE;IACd,KAAK,CAACA,IAAI,CAACC,QAAQ,CAACC,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACC,OAAO,CAAC,CAAC;IAC9C,IAAI,CAACJ,IAAI,GAAGA,IAAI;EACpB;AACJ;AACA,SAASK,qBAAqBA,CAACL,IAAI,EAAE;EACjC,IAAIA,IAAI,YAAY7C,uBAAuB,EAAE;IACzC,OAAO,IAAI2C,2BAA2B,CAACE,IAAI,CAAC;EAChD;EACA,OAAOA,IAAI;AACf;AACA,MAAMM,uBAAuB,CAAC;EAC1BP,WAAWA,CAACQ,aAAa,EAAEC,GAAG,EAAE;IAC5B,IAAI,CAACD,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,oBAAoB,GAAGvB,UAAU,CAACwB,IAAI;IAC3C,IAAI,CAACC,WAAW,GAAG,IAAIxB,eAAe,CAAC,CAAC;EAC5C;EACAyB,UAAUA,CAACT,IAAI,EAAE;IACb,OAAO,IAAI,CAACK,GAAG,CAACI,UAAU,CAACT,IAAI,CAACC,OAAO,CAAC;EAC5C;EACAS,YAAYA,CAACC,KAAK,EAAEC,aAAa,EAAE;IAC/B,IAAI,IAAI,CAACP,GAAG,CAACK,YAAY,EAAE;MACvB,OAAO,IAAI,CAACL,GAAG,CAACK,YAAY,CAACC,KAAK,CAACZ,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACC,OAAO,CAAC,EAAEW,aAAa,CAAC;IAChF;IACA,OAAOC,SAAS;EACpB;EACAC,WAAWA,CAACjB,IAAI,EAAEe,aAAa,EAAE;IAC7B,IAAI,CAACP,GAAG,CAACS,WAAW,GAAGZ,qBAAqB,CAACL,IAAI,CAAC,EAAEe,aAAa,CAAC;EACtE;EACAG,UAAUA,CAAClB,IAAI,EAAEmB,UAAU,EAAEC,WAAW,EAAEC,YAAY,EAAEN,aAAa,EAAEO,GAAG,GAAG,IAAI,EAAE;IAC/E,MAAMC,MAAM,GAAG,IAAI,CAACf,GAAG,CAACU,UAAU,CAACb,qBAAqB,CAACL,IAAI,CAAC,EAAEmB,UAAU,IAAIA,UAAU,CAACf,OAAO,EAAEgB,WAAW,EAAEC,YAAY,EAAEN,aAAa,CAAC;IAC3I,MAAMS,uBAAuB,GAAG,IAAI,CAACC,cAAc,KAAKN,UAAU;IAClE,IAAIK,uBAAuB,EAAE;MACzB,IAAI,CAACf,oBAAoB,CAACrB,OAAO,CAAC,CAAC;MACnC,IAAI,CAACqC,cAAc,GAAGN,UAAU;IACpC;IACA,IAAI,OAAOA,UAAU,KAAK,WAAW,EAAE;MACnC,OAAOI,MAAM;IACjB;IACA,IAAIC,uBAAuB,IAAI,OAAOD,MAAM,KAAK,SAAS,IAAIA,MAAM,CAACG,UAAU,EAAE;MAC7E,IAAI,CAACjB,oBAAoB,GAAGlC,iBAAiB,CAAC,MAAM;QAChD,MAAMoD,KAAK,GAAG,IAAI,CAACpB,aAAa,CAAC,CAAC;QAClC,MAAMqB,GAAG,GAAGD,KAAK,CAACE,eAAe,CAACV,UAAU,CAAC;QAC7C,IAAIQ,KAAK,CAACG,WAAW,CAACF,GAAG,CAAC,EAAE;UACxBD,KAAK,CAACI,YAAY,CAACH,GAAG,EAAE,KAAK,CAAC;QAClC;QACA,IAAI,CAACH,cAAc,GAAGT,SAAS;MACnC,CAAC,EAAE,GAAG,EAAE,IAAI,CAACL,WAAW,CAAC;IAC7B;IACA,IAAI,OAAOY,MAAM,KAAK,SAAS,IAAI,CAACA,MAAM,CAACS,MAAM,IAAI,OAAOT,MAAM,CAACU,MAAM,KAAK,WAAW,IAAIV,MAAM,CAACW,QAAQ,EAAE;MAC1G,IAAI,CAACZ,GAAG,EAAE;QACN,MAAMU,MAAM,GAAG,OAAOT,MAAM,KAAK,SAAS,GAAGA,MAAM,GAAGA,MAAM,CAACS,MAAM;QACnE,MAAMG,MAAM,GAAG,OAAOZ,MAAM,KAAK,SAAS,GAAGP,SAAS,GAAGO,MAAM,CAACY,MAAM;QACtE,OAAO;UAAEH,MAAM;UAAEG,MAAM;UAAED,QAAQ,EAAE,CAACd,WAAW;QAAE,CAAC;MACtD;MACA,OAAOG,MAAM;IACjB;IACA,IAAIA,MAAM,CAACU,MAAM,KAAK,CAAC,CAAC,6BAA6B;MACjD,MAAMN,KAAK,GAAG,IAAI,CAACpB,aAAa,CAAC,CAAC;MAClC,MAAMqB,GAAG,GAAGD,KAAK,CAACE,eAAe,CAACV,UAAU,CAAC;MAC7C,MAAMiB,SAAS,GAAGT,KAAK,CAACU,qBAAqB,CAACT,GAAG,CAAC;MAClD,MAAMU,UAAU,GAAGX,KAAK,CAACY,OAAO,CAACH,SAAS,CAAC;MAC3C,MAAMI,WAAW,GAAGJ,SAAS,IAAIT,KAAK,CAACc,YAAY,CAACL,SAAS,CAAC;MAC9D,OAAO,IAAI,CAAClB,UAAU,CAAClB,IAAI,EAAEsC,UAAU,EAAEE,WAAW,EAAEnB,YAAY,EAAEN,aAAa,EAAE,KAAK,CAAC;IAC7F;IACA,MAAMY,KAAK,GAAG,IAAI,CAACpB,aAAa,CAAC,CAAC;IAClC,MAAMqB,GAAG,GAAGD,KAAK,CAACE,eAAe,CAACV,UAAU,CAAC;IAC7C,MAAMuB,KAAK,GAAGf,KAAK,CAACc,YAAY,CAACb,GAAG,CAAC;IACrC,MAAMe,MAAM,GAAGhB,KAAK,CAACiB,kBAAkB,CAAChB,GAAG,CAAC;IAC5C,OAAO;MAAE,GAAGL,MAAM;MAAEW,QAAQ,EAAE7D,KAAK,CAACqE,KAAK,EAAEA,KAAK,GAAGC,MAAM;IAAE,CAAC;EAChE;EACAE,IAAIA,CAAC7C,IAAI,EAAEmB,UAAU,EAAEC,WAAW,EAAEC,YAAY,EAAEN,aAAa,EAAE;IAC7D,IAAI,CAACN,oBAAoB,CAACrB,OAAO,CAAC,CAAC;IACnC,IAAI,CAACqC,cAAc,GAAGT,SAAS;IAC/B,IAAI,CAACR,GAAG,CAACqC,IAAI,CAACxC,qBAAqB,CAACL,IAAI,CAAC,EAAEmB,UAAU,IAAIA,UAAU,CAACf,OAAO,EAAEgB,WAAW,EAAEC,YAAY,EAAEN,aAAa,CAAC;EAC1H;EACA+B,SAASA,CAAC/B,aAAa,EAAE;IACrB,IAAI,CAACP,GAAG,CAACsC,SAAS,GAAG/B,aAAa,CAAC;EACvC;EACA3B,OAAOA,CAAA,EAAG;IACN,IAAI,CAACuB,WAAW,CAACvB,OAAO,CAAC,CAAC;IAC1B,IAAI,CAACoB,GAAG,CAACpB,OAAO,CAAC,CAAC;EACtB;AACJ;AACA,SAAS2D,aAAaA,CAACxC,aAAa,EAAEyC,OAAO,EAAE;EAC3C,OAAOA,OAAO,IAAI;IACd,GAAGA,OAAO;IACVC,gBAAgB,EAAED,OAAO,CAACC,gBAAgB,IAAI;MAC1CC,KAAKA,CAACC,EAAE,EAAE;QACN,OAAOH,OAAO,CAACC,gBAAgB,CAACC,KAAK,CAACC,EAAE,CAAC/C,OAAO,CAAC;MACrD;IACJ,CAAC;IACDI,GAAG,EAAEwC,OAAO,CAACxC,GAAG,IAAI,IAAIF,uBAAuB,CAACC,aAAa,EAAEyC,OAAO,CAACxC,GAAG,CAAC;IAC3E4C,2BAA2B,EAAEJ,OAAO,CAACI,2BAA2B,IAAI;MAChEC,4BAA4BA,CAACC,CAAC,EAAE;QAC5B,OAAON,OAAO,CAACI,2BAA2B,CAACC,4BAA4B,CAAC;UAAE,GAAGC,CAAC;UAAElD,OAAO,EAAEkD,CAAC,CAAClD;QAAQ,CAAC,CAAC;MACzG,CAAC;MACDmD,2BAA2BA,CAACD,CAAC,EAAE;QAC3B,OAAON,OAAO,CAACI,2BAA2B,CAACG,2BAA2B,CAAC;UAAE,GAAGD,CAAC;UAAElD,OAAO,EAAEkD,CAAC,CAAClD;QAAQ,CAAC,CAAC;MACxG;IACJ,CAAC;IACDoD,qBAAqB,EAAER,OAAO,CAACQ,qBAAqB,IAAI;MACpD,GAAGR,OAAO,CAACQ,qBAAqB;MAChCC,UAAUA,CAACtD,IAAI,EAAE;QACb,MAAMwB,KAAK,GAAGpB,aAAa,CAAC,CAAC;QAC7B,MAAMqB,GAAG,GAAGD,KAAK,CAACE,eAAe,CAAC1B,IAAI,CAAC;QACvC,MAAMiC,SAAS,GAAGT,KAAK,CAACU,qBAAqB,CAACT,GAAG,CAAC;QAClD,MAAMU,UAAU,GAAGX,KAAK,CAACY,OAAO,CAACH,SAAS,CAAC;QAC3C,OAAOE,UAAU,CAACoB,oBAAoB;MAC1C,CAAC;MACDC,WAAWA,CAACxD,IAAI,EAAE;QACd,OAAOA,IAAI,CAACyD,iBAAiB,GAAG,CAAC;MACrC,CAAC;MACDC,SAAS,EAAEb,OAAO,CAACQ,qBAAqB,IAAIR,OAAO,CAACQ,qBAAqB,CAACK,SAAS,GAAI1D,IAAI,IAAK;QAC5F,OAAO6C,OAAO,CAACQ,qBAAqB,CAACK,SAAS,CAAC1D,IAAI,CAACC,OAAO,CAAC;MAChE,CAAC,GAAGY,SAAS;MACb8C,OAAO,EAAEd,OAAO,CAACQ,qBAAqB,IAAIR,OAAO,CAACQ,qBAAqB,CAACM,OAAO,GAAI3D,IAAI,IAAK;QACxF,OAAO6C,OAAO,CAACQ,qBAAqB,CAACM,OAAO,CAAC3D,IAAI,CAACC,OAAO,CAAC;MAC9D,CAAC,GAAG,MAAM,UAAU;MACpB2D,YAAYA,CAACT,CAAC,EAAE;QACZ,OAAON,OAAO,CAACQ,qBAAqB,CAACO,YAAY,CAACT,CAAC,CAAClD,OAAO,CAAC;MAChE,CAAC;MACD4D,kBAAkBA,CAAA,EAAG;QACjB,OAAOhB,OAAO,CAACQ,qBAAqB,CAACQ,kBAAkB,CAAC,CAAC;MAC7D,CAAC;MACDC,aAAa,EAAEjB,OAAO,CAACQ,qBAAqB,IAAIR,OAAO,CAACQ,qBAAqB,CAACS,aAAa,GAAG,MAAMjB,OAAO,CAACQ,qBAAqB,CAACS,aAAa,CAAC,CAAC,GAAG,MAAM,MAAM;MAChKC,YAAY,EAAElB,OAAO,CAACQ,qBAAqB,IAAIR,OAAO,CAACQ,qBAAqB,CAACU,YAAY,GAAI/D,IAAI,IAAK6C,OAAO,CAACQ,qBAAqB,CAACU,YAAY,CAAC/D,IAAI,CAACC,OAAO,CAAC,GAAID,IAAI,IAAK;QACvK,OAAOA,IAAI,CAACgE,KAAK;MACrB,CAAC;MACDC,qBAAqB,EAAEpB,OAAO,CAACQ,qBAAqB,CAACY,qBAAqB,KAAKjE,IAAI,IAAI;QACnF,OAAO6C,OAAO,CAACQ,qBAAqB,CAACY,qBAAqB,CAACjE,IAAI,CAACC,OAAO,CAAC;MAC5E,CAAC;IACL,CAAC;IACDiE,+BAA+B,EAAErB,OAAO,CAACqB,+BAA+B,IAAI;MACxE,GAAGrB,OAAO,CAACqB,+BAA+B;MAC1CC,0BAA0BA,CAACnE,IAAI,EAAE;QAC7B,OAAO6C,OAAO,CAACqB,+BAA+B,CAACC,0BAA0B,CAACnE,IAAI,CAACC,OAAO,CAAC;MAC3F;IACJ;EACJ,CAAC;AACL;AACA,OAAO,MAAMmE,oBAAoB,CAAC;EAC9BxE,WAAWA,CAACyE,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACAC,SAASA,CAACrE,OAAO,EAAE;IACf,OAAO,IAAI,CAACoE,QAAQ,CAACC,SAAS,CAACrE,OAAO,CAACA,OAAO,CAAC;EACnD;EACAsE,aAAaA,CAACtE,OAAO,EAAE;IACnB,OAAO,IAAI,CAACoE,QAAQ,CAACE,aAAa,CAACtE,OAAO,CAACA,OAAO,CAAC;EACvD;EACAuE,gBAAgBA,CAACvE,OAAO,EAAE;IACtB,OAAO,CAAC,CAAC,IAAI,CAACoE,QAAQ,CAACG,gBAAgB,IAAI,IAAI,CAACH,QAAQ,CAACG,gBAAgB,CAACvE,OAAO,CAACA,OAAO,CAAC;EAC9F;EACAwE,gBAAgBA,CAACxE,OAAO,EAAEyE,MAAM,EAAE;IAC9B,IAAI,CAACL,QAAQ,CAACI,gBAAgB,GAAGxE,OAAO,CAACA,OAAO,EAAEyE,MAAM,CAAC;EAC7D;AACJ;AACA,OAAO,IAAIC,kBAAkB,gBAC5B,UAAUA,kBAAkB,EAAE;EAC3BA,kBAAkB,CAAC,MAAM,CAAC,GAAG,MAAM;EACnCA,kBAAkB,CAAC,SAAS,CAAC,GAAG,SAAS;EACzCA,kBAAkB,CAAC,QAAQ,CAAC,GAAG,QAAQ;EAAC,OAHjCA,kBAAkB;AAI7B,CAAC,CAAEA,kBAAkB,IAA0B,CAAC,CAAE,CALrB;AAM7B,MAAMC,eAAe,CAAC;EAClB,IAAI9E,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC+E,SAAS;EACzB;EACAjF,WAAWA,CAACkF,WAAW,EAAED,SAAS,GAAG,EAAE,EAAE;IACrC,IAAI,CAACA,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACrE,WAAW,GAAG,IAAIxB,eAAe,CAAC,CAAC;IACxC,IAAI,CAAC8F,WAAW,GAAGpG,KAAK,CAACqG,OAAO,CAACD,WAAW,EAAEhF,QAAQ,IAAI,IAAI,CAAC+E,SAAS,GAAG/E,QAAQ,EAAE,IAAI,CAACU,WAAW,CAAC;EAC1G;EACAvB,OAAOA,CAAA,EAAG;IACN,IAAI,CAACuB,WAAW,CAACvB,OAAO,CAAC,CAAC;EAC9B;AACJ;AACA,WAAa+F,YAAY;EAAlB,MAAMA,YAAY,CAAC;IACtB;MAAS,IAAI,CAACC,aAAa,GAAG,CAAC;IAAE;IACjCrF,WAAWA,CAACsF,QAAQ,EAAE9E,aAAa,EAAE+E,wBAAwB,EAAEC,WAAW,EAAEC,oBAAoB,EAAExC,OAAO,GAAG,CAAC,CAAC,EAAE;MAC5G,IAAI,CAACqC,QAAQ,GAAGA,QAAQ;MACxB,IAAI,CAAC9E,aAAa,GAAGA,aAAa;MAClC,IAAI,CAACgF,WAAW,GAAGA,WAAW;MAC9B,IAAI,CAACC,oBAAoB,GAAGA,oBAAoB;MAChD,IAAI,CAACC,gBAAgB,GAAG,IAAIC,GAAG,CAAC,CAAC;MACjC,IAAI,CAACC,aAAa,GAAG,IAAID,GAAG,CAAC,CAAC;MAC9B,IAAI,CAACE,MAAM,GAAGT,YAAY,CAACC,aAAa;MACxC,IAAI,CAACS,+BAA+B,GAAG,KAAK;MAC5C,IAAI,CAACC,wBAAwB,GAAG,KAAK;MACrC,IAAI,CAACC,iBAAiB,GAAG,IAAIC,GAAG,CAAC,CAAC;MAClC,IAAI,CAACC,sBAAsB,GAAG/G,UAAU,CAACwB,IAAI;MAC7C,IAAI,CAACC,WAAW,GAAG,IAAIxB,eAAe,CAAC,CAAC;MACxC,IAAI,CAAC+G,UAAU,GAAGb,QAAQ,CAACa,UAAU;MACrC,IAAI,CAACC,aAAa,CAACnD,OAAO,CAAC;MAC3BnE,KAAK,CAACqB,GAAG,CAACoF,wBAAwB,EAAEhC,CAAC,IAAIA,CAAC,CAACnD,IAAI,CAAC,CAAC,IAAI,CAACiG,2BAA2B,EAAE,IAAI,EAAE,IAAI,CAACzF,WAAW,CAAC;MAC1G0E,QAAQ,CAACgB,uBAAuB,GAAG,IAAI,CAACA,uBAAuB,EAAE,IAAI,EAAE,IAAI,CAAC1F,WAAW,CAAC;IAC5F;IACAwF,aAAaA,CAACnD,OAAO,GAAG,CAAC,CAAC,EAAE;MACxB,IAAI,OAAOA,OAAO,CAAC4C,MAAM,KAAK,WAAW,EAAE;QACvC,MAAMA,MAAM,GAAGtG,KAAK,CAAC0D,OAAO,CAAC4C,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QAC3C,IAAIA,MAAM,KAAK,IAAI,CAACA,MAAM,EAAE;UACxB,IAAI,CAACA,MAAM,GAAGA,MAAM;UACpB,KAAK,MAAM,CAACzF,IAAI,EAAEmG,YAAY,CAAC,IAAI,IAAI,CAACX,aAAa,EAAE;YACnD,IAAI,CAACY,iBAAiB,CAACpG,IAAI,EAAEmG,YAAY,CAAC;UAC9C;QACJ;MACJ;MACA,IAAI,OAAOtD,OAAO,CAACwD,kBAAkB,KAAK,WAAW,EAAE;QACnD,MAAMV,wBAAwB,GAAG9C,OAAO,CAACwD,kBAAkB,KAAK1B,kBAAkB,CAACpE,IAAI;QACvF,IAAIoF,wBAAwB,KAAK,IAAI,CAACA,wBAAwB,EAAE;UAC5D,IAAI,CAACA,wBAAwB,GAAGA,wBAAwB;UACxD,KAAK,MAAM,CAAC3F,IAAI,EAAEmG,YAAY,CAAC,IAAI,IAAI,CAACX,aAAa,EAAE;YACnD,IAAI,CAACc,mBAAmB,CAACtG,IAAI,EAAEmG,YAAY,CAAC;UAChD;UACA,IAAI,CAACL,sBAAsB,CAAC7G,OAAO,CAAC,CAAC;UACrC,IAAI0G,wBAAwB,EAAE;YAC1B,MAAMnF,WAAW,GAAG,IAAIxB,eAAe,CAAC,CAAC;YACzC,IAAI,CAACoG,WAAW,CAACN,WAAW,CAAC,IAAI,CAACyB,uBAAuB,EAAE,IAAI,EAAE/F,WAAW,CAAC;YAC7E,IAAI,CAACsF,sBAAsB,GAAGtF,WAAW;YACzC,IAAI,CAAC+F,uBAAuB,CAAC,IAAI,CAACnB,WAAW,CAACtF,QAAQ,CAAC;UAC3D;QACJ;MACJ;MACA,IAAI,OAAO+C,OAAO,CAAC6C,+BAA+B,KAAK,WAAW,EAAE;QAChE,IAAI,CAACA,+BAA+B,GAAG7C,OAAO,CAAC6C,+BAA+B;MAClF;IACJ;IACAc,cAAcA,CAACC,SAAS,EAAE;MACtB,MAAMzD,EAAE,GAAG9G,MAAM,CAACuK,SAAS,EAAExK,CAAC,CAAC,gBAAgB,CAAC,CAAC;MACjD,MAAMwJ,MAAM,GAAGvJ,MAAM,CAAC8G,EAAE,EAAE/G,CAAC,CAAC,mBAAmB,CAAC,CAAC;MACjD,MAAMyK,OAAO,GAAGxK,MAAM,CAAC8G,EAAE,EAAE/G,CAAC,CAAC,oBAAoB,CAAC,CAAC;MACnD,MAAM0K,QAAQ,GAAGzK,MAAM,CAAC8G,EAAE,EAAE/G,CAAC,CAAC,qBAAqB,CAAC,CAAC;MACrD,MAAMkK,YAAY,GAAG,IAAI,CAACjB,QAAQ,CAACsB,cAAc,CAACG,QAAQ,CAAC;MAC3D,OAAO;QAAEF,SAAS;QAAEhB,MAAM;QAAEiB,OAAO;QAAEZ,sBAAsB,EAAE/G,UAAU,CAACwB,IAAI;QAAE4F;MAAa,CAAC;IAChG;IACAS,aAAaA,CAAC5G,IAAI,EAAE6G,KAAK,EAAEV,YAAY,EAAEzB,MAAM,EAAE;MAC7C,IAAI,CAACc,aAAa,CAACsB,GAAG,CAAC9G,IAAI,EAAEmG,YAAY,CAAC;MAC1C,IAAI,CAACb,gBAAgB,CAACwB,GAAG,CAAC9G,IAAI,CAACC,OAAO,EAAED,IAAI,CAAC;MAC7C,IAAI,CAACoG,iBAAiB,CAACpG,IAAI,EAAEmG,YAAY,CAAC;MAC1C,IAAI,CAACjB,QAAQ,CAAC0B,aAAa,CAAC5G,IAAI,EAAE6G,KAAK,EAAEV,YAAY,CAACA,YAAY,EAAEzB,MAAM,CAAC;IAC/E;IACAqC,cAAcA,CAAC/G,IAAI,EAAE6G,KAAK,EAAEV,YAAY,EAAEzB,MAAM,EAAE;MAC9CyB,YAAY,CAACL,sBAAsB,CAAC7G,OAAO,CAAC,CAAC;MAC7C,IAAI,CAACiG,QAAQ,CAAC6B,cAAc,GAAG/G,IAAI,EAAE6G,KAAK,EAAEV,YAAY,CAACA,YAAY,EAAEzB,MAAM,CAAC;MAC9E,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;QAC5B,IAAI,CAACc,aAAa,CAACwB,MAAM,CAAChH,IAAI,CAAC;QAC/B,IAAI,CAACsF,gBAAgB,CAAC0B,MAAM,CAAChH,IAAI,CAACC,OAAO,CAAC;MAC9C;IACJ;IACAgH,eAAeA,CAACd,YAAY,EAAE;MAC1B,IAAI,CAACjB,QAAQ,CAAC+B,eAAe,CAACd,YAAY,CAACA,YAAY,CAAC;IAC5D;IACAD,uBAAuBA,CAACjG,OAAO,EAAE;MAC7B,MAAMD,IAAI,GAAG,IAAI,CAACsF,gBAAgB,CAAC4B,GAAG,CAACjH,OAAO,CAAC;MAC/C,IAAI,CAACD,IAAI,EAAE;QACP;MACJ;MACA,IAAI,CAACiG,2BAA2B,CAACjG,IAAI,CAAC;IAC1C;IACAiG,2BAA2BA,CAACjG,IAAI,EAAE;MAC9B,MAAMmG,YAAY,GAAG,IAAI,CAACX,aAAa,CAAC0B,GAAG,CAAClH,IAAI,CAAC;MACjD,IAAI,CAACmG,YAAY,EAAE;QACf;MACJ;MACA,IAAI,CAACI,uBAAuB,CAAC,IAAI,CAACnB,WAAW,CAACtF,QAAQ,CAAC;MACvD,IAAI,CAACsG,iBAAiB,CAACpG,IAAI,EAAEmG,YAAY,CAAC;IAC9C;IACAC,iBAAiBA,CAACpG,IAAI,EAAEmG,YAAY,EAAE;MAClC,MAAMV,MAAM,GAAGT,YAAY,CAACC,aAAa,GAAG,CAACjF,IAAI,CAACgE,KAAK,GAAG,CAAC,IAAI,IAAI,CAACyB,MAAM;MAC1EU,YAAY,CAACO,OAAO,CAACS,KAAK,CAACC,WAAW,GAAG,GAAG3B,MAAM,IAAI;MACtDU,YAAY,CAACV,MAAM,CAAC0B,KAAK,CAACE,KAAK,GAAG,GAAG5B,MAAM,GAAG,IAAI,CAACA,MAAM,GAAG,EAAE,IAAI;MAClE,IAAIzF,IAAI,CAACsH,WAAW,EAAE;QAClBnB,YAAY,CAACM,SAAS,CAACc,YAAY,CAAC,eAAe,EAAEC,MAAM,CAAC,CAACxH,IAAI,CAACyH,SAAS,CAAC,CAAC;MACjF,CAAC,MACI;QACDtB,YAAY,CAACM,SAAS,CAACiB,eAAe,CAAC,eAAe,CAAC;MAC3D;MACAvB,YAAY,CAACO,OAAO,CAACiB,SAAS,CAACC,MAAM,CAAC,GAAGrJ,SAAS,CAACsJ,gBAAgB,CAACvJ,OAAO,CAACwJ,gBAAgB,CAAC,CAAC;MAC9F,IAAIC,eAAe,GAAG,KAAK;MAC3B,IAAI,IAAI,CAAC7C,QAAQ,CAAC8C,aAAa,EAAE;QAC7BD,eAAe,GAAG,IAAI,CAAC7C,QAAQ,CAAC8C,aAAa,CAAChI,IAAI,CAACC,OAAO,EAAEkG,YAAY,CAACO,OAAO,CAAC;MACrF;MACA,IAAI1G,IAAI,CAACsH,WAAW,KAAK,CAAC,IAAI,CAAC5B,+BAA+B,IAAI1F,IAAI,CAACuD,oBAAoB,GAAG,CAAC,CAAC,EAAE;QAC9F,IAAI,CAACwE,eAAe,EAAE;UAClB5B,YAAY,CAACO,OAAO,CAACiB,SAAS,CAACM,GAAG,CAAC,GAAG1J,SAAS,CAACsJ,gBAAgB,CAACvJ,OAAO,CAACwJ,gBAAgB,CAAC,CAAC;QAC/F;QACA3B,YAAY,CAACO,OAAO,CAACiB,SAAS,CAACM,GAAG,CAAC,aAAa,CAAC;QACjD9B,YAAY,CAACO,OAAO,CAACiB,SAAS,CAACO,MAAM,CAAC,WAAW,EAAElI,IAAI,CAACyH,SAAS,CAAC;MACtE,CAAC,MACI;QACDtB,YAAY,CAACO,OAAO,CAACiB,SAAS,CAACC,MAAM,CAAC,aAAa,EAAE,WAAW,CAAC;MACrE;MACA,IAAI,CAACtB,mBAAmB,CAACtG,IAAI,EAAEmG,YAAY,CAAC;IAChD;IACAG,mBAAmBA,CAACtG,IAAI,EAAEmG,YAAY,EAAE;MACpChK,SAAS,CAACgK,YAAY,CAACV,MAAM,CAAC;MAC9BU,YAAY,CAACL,sBAAsB,CAAC7G,OAAO,CAAC,CAAC;MAC7C,IAAI,CAAC,IAAI,CAAC0G,wBAAwB,EAAE;QAChC;MACJ;MACA,MAAMwC,eAAe,GAAG,IAAInJ,eAAe,CAAC,CAAC;MAC7C,MAAMwC,KAAK,GAAG,IAAI,CAACpB,aAAa,CAAC,CAAC;MAClC,OAAO,IAAI,EAAE;QACT,MAAMqB,GAAG,GAAGD,KAAK,CAACE,eAAe,CAAC1B,IAAI,CAAC;QACvC,MAAMiC,SAAS,GAAGT,KAAK,CAACU,qBAAqB,CAACT,GAAG,CAAC;QAClD,IAAI,CAACQ,SAAS,EAAE;UACZ;QACJ;QACA,MAAMmG,MAAM,GAAG5G,KAAK,CAACY,OAAO,CAACH,SAAS,CAAC;QACvC,MAAMoG,KAAK,GAAGpM,CAAC,CAAC,eAAe,EAAE;UAAEkL,KAAK,EAAE,UAAU,IAAI,CAAC1B,MAAM;QAAK,CAAC,CAAC;QACtE,IAAI,IAAI,CAACG,iBAAiB,CAAC0C,GAAG,CAACF,MAAM,CAAC,EAAE;UACpCC,KAAK,CAACV,SAAS,CAACM,GAAG,CAAC,QAAQ,CAAC;QACjC;QACA,IAAI9B,YAAY,CAACV,MAAM,CAAC8C,iBAAiB,KAAK,CAAC,EAAE;UAC7CpC,YAAY,CAACV,MAAM,CAAC+C,WAAW,CAACH,KAAK,CAAC;QAC1C,CAAC,MACI;UACDlC,YAAY,CAACV,MAAM,CAACgD,YAAY,CAACJ,KAAK,EAAElC,YAAY,CAACV,MAAM,CAACiD,iBAAiB,CAAC;QAClF;QACA,IAAI,CAACrD,oBAAoB,CAAC4C,GAAG,CAACG,MAAM,EAAEC,KAAK,CAAC;QAC5CF,eAAe,CAACF,GAAG,CAAC/I,YAAY,CAAC,MAAM,IAAI,CAACmG,oBAAoB,CAAC2B,MAAM,CAACoB,MAAM,EAAEC,KAAK,CAAC,CAAC,CAAC;QACxFrI,IAAI,GAAGoI,MAAM;MACjB;MACAjC,YAAY,CAACL,sBAAsB,GAAGqC,eAAe;IACzD;IACA5B,uBAAuBA,CAAC5F,KAAK,EAAE;MAC3B,IAAI,CAAC,IAAI,CAACgF,wBAAwB,EAAE;QAChC;MACJ;MACA,MAAMmB,GAAG,GAAG,IAAIjB,GAAG,CAAC,CAAC;MACrB,MAAMrE,KAAK,GAAG,IAAI,CAACpB,aAAa,CAAC,CAAC;MAClCO,KAAK,CAACoE,OAAO,CAAC/E,IAAI,IAAI;QAClB,MAAMyB,GAAG,GAAGD,KAAK,CAACE,eAAe,CAAC1B,IAAI,CAAC;QACvC,IAAI;UACA,MAAMiC,SAAS,GAAGT,KAAK,CAACU,qBAAqB,CAACT,GAAG,CAAC;UAClD,IAAIzB,IAAI,CAACsH,WAAW,IAAItH,IAAI,CAAC2I,QAAQ,CAACnG,MAAM,GAAG,CAAC,IAAI,CAACxC,IAAI,CAACyH,SAAS,EAAE;YACjEX,GAAG,CAACmB,GAAG,CAACjI,IAAI,CAAC;UACjB,CAAC,MACI,IAAIiC,SAAS,EAAE;YAChB6E,GAAG,CAACmB,GAAG,CAACzG,KAAK,CAACY,OAAO,CAACH,SAAS,CAAC,CAAC;UACrC;QACJ,CAAC,CACD,MAAM;UACF;QAAA;MAER,CAAC,CAAC;MACF,IAAI,CAAC2D,iBAAiB,CAACb,OAAO,CAAC/E,IAAI,IAAI;QACnC,IAAI,CAAC8G,GAAG,CAACwB,GAAG,CAACtI,IAAI,CAAC,EAAE;UAChB,IAAI,CAACqF,oBAAoB,CAACN,OAAO,CAAC/E,IAAI,EAAE4I,IAAI,IAAIA,IAAI,CAACjB,SAAS,CAACC,MAAM,CAAC,QAAQ,CAAC,CAAC;QACpF;MACJ,CAAC,CAAC;MACFd,GAAG,CAAC/B,OAAO,CAAC/E,IAAI,IAAI;QAChB,IAAI,CAAC,IAAI,CAAC4F,iBAAiB,CAAC0C,GAAG,CAACtI,IAAI,CAAC,EAAE;UACnC,IAAI,CAACqF,oBAAoB,CAACN,OAAO,CAAC/E,IAAI,EAAE4I,IAAI,IAAIA,IAAI,CAACjB,SAAS,CAACM,GAAG,CAAC,QAAQ,CAAC,CAAC;QACjF;MACJ,CAAC,CAAC;MACF,IAAI,CAACrC,iBAAiB,GAAGkB,GAAG;IAChC;IACA7H,OAAOA,CAAA,EAAG;MACN,IAAI,CAACuG,aAAa,CAACqD,KAAK,CAAC,CAAC;MAC1B,IAAI,CAACvD,gBAAgB,CAACuD,KAAK,CAAC,CAAC;MAC7B,IAAI,CAAC/C,sBAAsB,CAAC7G,OAAO,CAAC,CAAC;MACrCA,OAAO,CAAC,IAAI,CAACuB,WAAW,CAAC;IAC7B;EACJ;EAAC,OA3LYwE,YAAY;AAAA;AA4LzB,MAAM8D,UAAU,CAAC;EACb,IAAIC,UAAUA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACC,WAAW;EAAE;EAC5C,IAAIC,UAAUA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACC,WAAW;EAAE;EAC5CtJ,WAAWA,CAACuJ,IAAI,EAAEjF,+BAA+B,EAAEkF,OAAO,EAAE;IACxD,IAAI,CAACD,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACjF,+BAA+B,GAAGA,+BAA+B;IACtE,IAAI,CAACkF,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACJ,WAAW,GAAG,CAAC;IACpB,IAAI,CAACE,WAAW,GAAG,CAAC;IACpB,IAAI,CAACG,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAAC9I,WAAW,GAAG,IAAIxB,eAAe,CAAC,CAAC;IACxCmK,IAAI,CAACI,cAAc,CAAC,IAAI,CAACC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAChJ,WAAW,CAAC;EAC3D;EACAiJ,MAAMA,CAACxJ,OAAO,EAAEyJ,gBAAgB,EAAE;IAC9B,IAAIC,UAAU,GAAG,CAAC,CAAC;IACnB,IAAI,IAAI,CAACP,OAAO,EAAE;MACd,MAAMhI,MAAM,GAAG,IAAI,CAACgI,OAAO,CAACK,MAAM,CAACxJ,OAAO,EAAEyJ,gBAAgB,CAAC;MAC7D,IAAI,OAAOtI,MAAM,KAAK,SAAS,EAAE;QAC7BuI,UAAU,GAAGvI,MAAM,GAAG,CAAC,CAAC,+BAA+B,CAAC,CAAC;MAC7D,CAAC,MACI,IAAIvD,cAAc,CAACuD,MAAM,CAAC,EAAE;QAC7BuI,UAAU,GAAG/L,eAAe,CAACwD,MAAM,CAACuI,UAAU,CAAC;MACnD,CAAC,MACI;QACDA,UAAU,GAAGvI,MAAM;MACvB;MACA,IAAIuI,UAAU,KAAK,CAAC,CAAC,6BAA6B;QAC9C,OAAO,KAAK;MAChB;IACJ;IACA,IAAI,CAACX,WAAW,EAAE;IAClB,IAAI,CAAC,IAAI,CAACK,QAAQ,EAAE;MAChB,IAAI,CAACH,WAAW,EAAE;MAClB,OAAO;QAAErJ,IAAI,EAAEf,UAAU,CAAC8K,OAAO;QAAED;MAAW,CAAC;IACnD;IACA,MAAME,KAAK,GAAG,IAAI,CAAC3F,+BAA+B,CAACC,0BAA0B,CAAClE,OAAO,CAAC;IACtF,MAAM6J,MAAM,GAAGC,KAAK,CAACC,OAAO,CAACH,KAAK,CAAC,GAAGA,KAAK,GAAG,CAACA,KAAK,CAAC;IACrD,KAAK,MAAMI,CAAC,IAAIH,MAAM,EAAE;MACpB,MAAMI,QAAQ,GAAGD,CAAC,IAAIA,CAAC,CAACE,QAAQ,CAAC,CAAC;MAClC,IAAI,OAAOD,QAAQ,KAAK,WAAW,EAAE;QACjC,OAAO;UAAErK,IAAI,EAAEf,UAAU,CAAC8K,OAAO;UAAED;QAAW,CAAC;MACnD;MACA,IAAIS,KAAK;MACT,IAAI,IAAI,CAACjB,IAAI,CAACkB,aAAa,KAAKC,iBAAiB,CAACC,UAAU,EAAE;QAC1D,MAAM1D,KAAK,GAAGqD,QAAQ,CAACM,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,IAAI,CAACnB,iBAAiB,CAAC;QACpE,IAAIzC,KAAK,GAAG,CAAC,CAAC,EAAE;UACZuD,KAAK,GAAG,CAACM,MAAM,CAACC,gBAAgB,EAAE,CAAC,CAAC;UACpC,KAAK,IAAIC,CAAC,GAAG,IAAI,CAACtB,iBAAiB,CAAC9G,MAAM,EAAEoI,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;YACpDR,KAAK,CAACS,IAAI,CAAChE,KAAK,GAAG+D,CAAC,GAAG,CAAC,CAAC;UAC7B;QACJ;MACJ,CAAC,MACI;QACDR,KAAK,GAAGvL,UAAU,CAAC,IAAI,CAACwK,QAAQ,EAAE,IAAI,CAACC,iBAAiB,EAAE,CAAC,EAAEY,QAAQ,EAAEA,QAAQ,CAACM,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE;UAAEM,mBAAmB,EAAE,IAAI;UAAEC,cAAc,EAAE;QAAK,CAAC,CAAC;MAC1J;MACA,IAAIX,KAAK,EAAE;QACP,IAAI,CAAClB,WAAW,EAAE;QAClB,OAAOY,MAAM,CAACtH,MAAM,KAAK,CAAC,GACtB;UAAE3C,IAAI,EAAEuK,KAAK;UAAET;QAAW,CAAC,GAC3B;UAAE9J,IAAI,EAAE;YAAEgK,KAAK,EAAEK,QAAQ;YAAEE,KAAK,EAAEA;UAAM,CAAC;UAAET;QAAW,CAAC;MAC/D;IACJ;IACA,IAAI,IAAI,CAACR,IAAI,CAAC6B,QAAQ,KAAKC,YAAY,CAACC,MAAM,EAAE;MAC5C,IAAI,OAAO,IAAI,CAAC/B,IAAI,CAACtG,OAAO,CAACsI,qBAAqB,KAAK,QAAQ,EAAE;QAC7D,OAAO,IAAI,CAAChC,IAAI,CAACtG,OAAO,CAACsI,qBAAqB;MAClD,CAAC,MACI,IAAI,IAAI,CAAChC,IAAI,CAACtG,OAAO,CAACsI,qBAAqB,EAAE;QAC9C,OAAO,IAAI,CAAChC,IAAI,CAACtG,OAAO,CAACsI,qBAAqB,CAAClL,OAAO,CAAC;MAC3D,CAAC,MACI;QACD,OAAO,CAAC,CAAC;MACb;IACJ,CAAC,MACI;MACD,OAAO;QAAEJ,IAAI,EAAEf,UAAU,CAAC8K,OAAO;QAAED;MAAW,CAAC;IACnD;EACJ;EACAH,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACR,WAAW,GAAG,CAAC;IACpB,IAAI,CAACE,WAAW,GAAG,CAAC;EACxB;EACAjK,OAAOA,CAAA,EAAG;IACNA,OAAO,CAAC,IAAI,CAACuB,WAAW,CAAC;EAC7B;AACJ;AACA,OAAO,MAAM4K,UAAU,SAAS1N,MAAM,CAAC;EACnCkC,WAAWA,CAACyL,IAAI,EAAE;IACd,KAAK,CAAC;MACFC,IAAI,EAAEhN,OAAO,CAACiN,UAAU;MACxBC,KAAK,EAAEnM,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC;MACnCqE,SAAS,EAAE2H,IAAI,CAAC3H,SAAS,IAAI,KAAK;MAClC+H,aAAa,EAAEJ,IAAI,CAACI,aAAa,IAAIlM,uBAAuB,CAAC,SAAS,CAAC;MACvEmM,uBAAuB,EAAEL,IAAI,CAACK,uBAAuB;MACrDC,2BAA2B,EAAEN,IAAI,CAACM,2BAA2B;MAC7DC,2BAA2B,EAAEP,IAAI,CAACO;IACtC,CAAC,CAAC;EACN;AACJ;AACA,OAAO,MAAMC,WAAW,SAASnO,MAAM,CAAC;EACpCkC,WAAWA,CAACyL,IAAI,EAAE;IACd,KAAK,CAAC;MACFC,IAAI,EAAEhN,OAAO,CAACwN,WAAW;MACzBN,KAAK,EAAEnM,QAAQ,CAAC,aAAa,EAAE,aAAa,CAAC;MAC7CqE,SAAS,EAAE2H,IAAI,CAAC3H,SAAS,IAAI,KAAK;MAClC+H,aAAa,EAAEJ,IAAI,CAACI,aAAa,IAAIlM,uBAAuB,CAAC,SAAS,CAAC;MACvEmM,uBAAuB,EAAEL,IAAI,CAACK,uBAAuB;MACrDC,2BAA2B,EAAEN,IAAI,CAACM,2BAA2B;MAC7DC,2BAA2B,EAAEP,IAAI,CAACO;IACtC,CAAC,CAAC;EACN;AACJ;AACA,MAAMG,wBAAwB,GAAG;EAC7BC,cAAc,EAAEjP,mBAAmB;EACnCkP,YAAY,EAAEtO,oBAAoB;EAClCuO,0BAA0B,EAAErL,SAAS;EACrCsL,gCAAgC,EAAEtL,SAAS;EAC3CuL,uBAAuB,EAAEvL,SAAS;EAClCwL,sBAAsB,EAAExL;AAC5B,CAAC;AACD,OAAO,IAAIoK,YAAY,gBACtB,UAAUA,YAAY,EAAE;EACrBA,YAAY,CAACA,YAAY,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW;EACzDA,YAAY,CAACA,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ;EAAC,OAF7CA,YAAY;AAGvB,CAAC,CAAEA,YAAY,IAAoB,CAAC,CAAE,CAJf;AAKvB,OAAO,IAAIX,iBAAiB,gBAC3B,UAAUA,iBAAiB,EAAE;EAC1BA,iBAAiB,CAACA,iBAAiB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;EAC3DA,iBAAiB,CAACA,iBAAiB,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,YAAY;EAAC,OAF/DA,iBAAiB;AAG5B,CAAC,CAAEA,iBAAiB,IAAyB,CAAC,CAAE,CAJpB;AAK5B,MAAMgC,UAAU,SAASvN,UAAU,CAAC;EAChC,IAAIwN,IAAIA,CAACA,IAAI,EAAE;IACX,IAAI,CAACC,UAAU,CAACC,OAAO,GAAGF,IAAI,KAAKtB,YAAY,CAACC,MAAM;IACtD,IAAI,CAACwB,SAAS,CAACC,QAAQ,CAACC,cAAc,CAACL,IAAI,KAAKtB,YAAY,CAACC,MAAM,GAAG7L,QAAQ,CAAC,gBAAgB,EAAE,gBAAgB,CAAC,GAAGA,QAAQ,CAAC,gBAAgB,EAAE,gBAAgB,CAAC,CAAC;EACtK;EACA,IAAIwN,SAASA,CAACA,SAAS,EAAE;IACrB,IAAI,CAACC,eAAe,CAACL,OAAO,GAAGI,SAAS,KAAKvC,iBAAiB,CAACyC,KAAK;EACxE;EACAnN,WAAWA,CAAC6G,SAAS,EAAE0C,IAAI,EAAE6D,mBAAmB,EAAET,IAAI,EAAEM,SAAS,EAAEhK,OAAO,EAAE;IACxE,KAAK,CAAC,CAAC;IACP,IAAI,CAACsG,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACrJ,QAAQ,GAAGxD,CAAC,CAAC,0BAA0B,EAAE,CAC1CA,CAAC,CAAC,kEAAkE,EAAE;MAAE2Q,QAAQ,EAAE;IAAE,CAAC,CAAC,EACtF3Q,CAAC,CAAC,0CAA0C,CAAC,EAC7CA,CAAC,CAAC,8CAA8C,CAAC,CACpD,CAAC;IACF,IAAI,CAAC+K,KAAK,GAAG,CAAC;IACd,IAAI,CAAC6F,KAAK,GAAG,CAAC;IACd,IAAI,CAACC,GAAG,GAAG,CAAC;IACZ,IAAI,CAACC,aAAa,GAAG,IAAI3O,OAAO,CAAC,CAAC;IAClCgI,SAAS,CAAC+B,WAAW,CAAC,IAAI,CAAC1I,QAAQ,CAACuN,IAAI,CAAC;IACzC,IAAI,CAACC,SAAS,CAACpO,YAAY,CAAC,MAAM,IAAI,CAACY,QAAQ,CAACuN,IAAI,CAACzF,MAAM,CAAC,CAAC,CAAC,CAAC;IAC/D,MAAM2F,MAAM,GAAG1K,OAAO,EAAE0K,MAAM,IAAIxB,wBAAwB;IAC1D,IAAIwB,MAAM,CAACrB,0BAA0B,EAAE;MACnC,IAAI,CAACpM,QAAQ,CAACuN,IAAI,CAAClG,KAAK,CAACqG,eAAe,GAAGD,MAAM,CAACrB,0BAA0B;IAChF;IACA,IAAIqB,MAAM,CAAClB,sBAAsB,EAAE;MAC/B,IAAI,CAACvM,QAAQ,CAACuN,IAAI,CAAClG,KAAK,CAACsG,SAAS,GAAG,eAAeF,MAAM,CAAClB,sBAAsB,EAAE;IACvF;IACA,MAAMqB,mBAAmB,GAAG,IAAI,CAACJ,SAAS,CAAChO,0BAA0B,CAAC,CAAC,CAAC;IACxE,IAAI,CAACkN,UAAU,GAAG,IAAI,CAACc,SAAS,CAAC,IAAIlC,UAAU,CAAC;MAAE,GAAGmC,MAAM,CAACtB,YAAY;MAAEvI,SAAS,EAAE6I,IAAI,KAAKtB,YAAY,CAACC,MAAM;MAAEO,aAAa,EAAEiC;IAAoB,CAAC,CAAC,CAAC;IACzJ,IAAI,CAACZ,eAAe,GAAG,IAAI,CAACQ,SAAS,CAAC,IAAIzB,WAAW,CAAC;MAAE,GAAG0B,MAAM,CAACtB,YAAY;MAAEvI,SAAS,EAAEmJ,SAAS,KAAKvC,iBAAiB,CAACyC,KAAK;MAAEtB,aAAa,EAAEiC;IAAoB,CAAC,CAAC,CAAC;IACxK,IAAI,CAACC,eAAe,GAAGjP,KAAK,CAACqB,GAAG,CAAC,IAAI,CAACyM,UAAU,CAACoB,QAAQ,EAAE,MAAM,IAAI,CAACpB,UAAU,CAACC,OAAO,GAAGxB,YAAY,CAACC,MAAM,GAAGD,YAAY,CAAC4C,SAAS,EAAE,IAAI,CAACC,MAAM,CAAC;IACrJ,IAAI,CAACC,oBAAoB,GAAGrP,KAAK,CAACqB,GAAG,CAAC,IAAI,CAAC+M,eAAe,CAACc,QAAQ,EAAE,MAAM,IAAI,CAACd,eAAe,CAACL,OAAO,GAAGnC,iBAAiB,CAACyC,KAAK,GAAGzC,iBAAiB,CAACC,UAAU,EAAE,IAAI,CAACuD,MAAM,CAAC;IAC9K,IAAI,CAACpB,SAAS,GAAG,IAAI,CAACY,SAAS,CAAC,IAAIxQ,SAAS,CAAC,IAAI,CAACgD,QAAQ,CAAC4M,SAAS,EAAEM,mBAAmB,EAAE;MACxFnD,KAAK,EAAExK,QAAQ,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;MACnD2O,iBAAiB,EAAE,CAAC,IAAI,CAACxB,UAAU,EAAE,IAAI,CAACM,eAAe,CAAC;MAC1DmB,qBAAqB,EAAE,KAAK;MAC5BjC,cAAc,EAAEuB,MAAM,CAACvB,cAAc;MACrCC,YAAY,EAAEsB,MAAM,CAACtB,YAAY;MACjCiC,OAAO,EAAErL,OAAO,EAAEqL;IACtB,CAAC,CAAC,CAAC;IACH,IAAI,CAACC,SAAS,GAAG,IAAI,CAACb,SAAS,CAAC,IAAIzQ,SAAS,CAAC,IAAI,CAACiD,QAAQ,CAACqO,SAAS,CAAC,CAAC;IACvE,IAAI,CAAC5B,IAAI,GAAGA,IAAI;IAChB,MAAM6B,OAAO,GAAG,IAAI,CAACd,SAAS,CAAC,IAAI3Q,UAAU,CAAC,IAAI,CAAC+P,SAAS,CAACC,QAAQ,CAAC0B,YAAY,EAAE,SAAS,CAAC,CAAC;IAC/F,MAAMC,SAAS,GAAG5P,KAAK,CAAC6P,KAAK,CAACH,OAAO,CAACI,KAAK,EAAEvS,CAAC,IAAIA,CAAC,CAAC8D,GAAG,CAACoD,CAAC,IAAI,IAAIvG,qBAAqB,CAACuG,CAAC,CAAC,CAAC,CAAC;IAC3F,IAAI,CAACmK,SAAS,CAACgB,SAAS,CAAEnL,CAAC,IAAK;MAC5B;MACA,IAAIA,CAAC,CAAClF,MAAM,CAAC,CAAC,CAAC,mBAAmB,CAAC,EAAE;QACjC;QACAkF,CAAC,CAACsL,cAAc,CAAC,CAAC;QAClBtL,CAAC,CAACuL,eAAe,CAAC,CAAC;QACnB,IAAI,CAAChC,SAAS,CAACC,QAAQ,CAACgC,YAAY,CAAC,CAAC;QACtC,IAAI,CAACxF,IAAI,CAACyF,QAAQ,CAAC,CAAC;QACpB;MACJ;MACA,IAAIzL,CAAC,CAAClF,MAAM,CAAC,EAAE,CAAC,uBAAuB,CAAC,EAAE;QACtCkF,CAAC,CAACsL,cAAc,CAAC,CAAC;QAClBtL,CAAC,CAACuL,eAAe,CAAC,CAAC;QACnB,IAAI,IAAI,CAAChC,SAAS,CAACC,QAAQ,CAACkC,iBAAiB,CAAC,CAAC,IAAI,IAAI,CAACnC,SAAS,CAACC,QAAQ,CAACmC,kBAAkB,CAAC,CAAC,EAAE;UAC7F;UACA,IAAI,CAACpC,SAAS,CAACC,QAAQ,CAACgC,YAAY,CAAC,CAAC;UACtC,IAAI,CAACxF,IAAI,CAACyF,QAAQ,CAAC,CAAC;QACxB,CAAC,MACI;UACD;UACA,IAAI,CAAClC,SAAS,CAACC,QAAQ,CAACoC,aAAa,CAAC,CAAC;QAC3C;QACA;MACJ;MACA,IAAI5L,CAAC,CAAClF,MAAM,CAAC,EAAE,CAAC,qBAAqB,CAAC,EAAE;QACpCkF,CAAC,CAACsL,cAAc,CAAC,CAAC;QAClBtL,CAAC,CAACuL,eAAe,CAAC,CAAC;QACnB;QACA,IAAI,CAAChC,SAAS,CAACC,QAAQ,CAACqC,iBAAiB,CAAC,CAAC;QAC3C;MACJ;IACJ,CAAC,CAAC,CAAC;IACH,MAAMC,WAAW,GAAG,IAAI,CAAC3B,SAAS,CAAC,IAAIvP,MAAM,CAAC,OAAO,EAAEsB,QAAQ,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,uBAAuB,EAAE,IAAI,EAAE,MAAM,IAAI,CAACJ,OAAO,CAAC,CAAC,CAAC,CAAC;IACxI,IAAI,CAACkP,SAAS,CAACtD,IAAI,CAACoE,WAAW,EAAE;MAAE3D,IAAI,EAAE,IAAI;MAAEzB,KAAK,EAAE;IAAM,CAAC,CAAC;IAC9D,MAAMqF,eAAe,GAAG,IAAI,CAAC5B,SAAS,CAAC,IAAI3Q,UAAU,CAAC,IAAI,CAACmD,QAAQ,CAACqP,IAAI,EAAE,WAAW,CAAC,CAAC;IACvF,IAAI,CAAC7B,SAAS,CAAC4B,eAAe,CAACV,KAAK,CAACrL,CAAC,IAAI;MACtC,MAAM3C,WAAW,GAAG,IAAIxB,eAAe,CAAC,CAAC;MACzC,MAAMoQ,iBAAiB,GAAG5O,WAAW,CAACyH,GAAG,CAAC,IAAItL,UAAU,CAACN,SAAS,CAAC8G,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;MACpF,MAAMkM,eAAe,GAAG7O,WAAW,CAACyH,GAAG,CAAC,IAAItL,UAAU,CAACN,SAAS,CAAC8G,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;MAChF,MAAMmM,UAAU,GAAG,IAAI,CAACpC,KAAK;MAC7B,MAAMqC,MAAM,GAAGpM,CAAC,CAACqM,KAAK;MACtB,MAAMC,QAAQ,GAAG,IAAI,CAACtC,GAAG;MACzB,MAAMuC,MAAM,GAAGvM,CAAC,CAACwM,KAAK;MACtB,IAAI,CAAC7P,QAAQ,CAACqP,IAAI,CAACxH,SAAS,CAACM,GAAG,CAAC,UAAU,CAAC;MAC5C,MAAM2H,UAAU,GAAG,IAAI,CAAC9P,QAAQ,CAACuN,IAAI,CAAClG,KAAK,CAACyI,UAAU;MACtD,IAAI,CAAC9P,QAAQ,CAACuN,IAAI,CAAClG,KAAK,CAACyI,UAAU,GAAG,OAAO;MAC7C,MAAMC,MAAM,GAAI1M,CAAC,IAAK;QAClB,MAAM2M,MAAM,GAAG3M,CAAC,CAACqM,KAAK,GAAGD,MAAM;QAC/B,IAAI,CAACrC,KAAK,GAAGoC,UAAU,GAAGQ,MAAM;QAChC,MAAMC,MAAM,GAAG5M,CAAC,CAACwM,KAAK,GAAGD,MAAM;QAC/B,IAAI,CAACvC,GAAG,GAAGsC,QAAQ,GAAGM,MAAM;QAC5B,IAAI,CAACC,MAAM,CAAC,CAAC;MACjB,CAAC;MACDxP,WAAW,CAACyH,GAAG,CAACmH,iBAAiB,CAACZ,KAAK,CAACqB,MAAM,CAAC,CAAC;MAChDrP,WAAW,CAACyH,GAAG,CAACoH,eAAe,CAACb,KAAK,CAACrL,CAAC,IAAI;QACvC0M,MAAM,CAAC1M,CAAC,CAAC;QACT,IAAI,CAACrD,QAAQ,CAACqP,IAAI,CAACxH,SAAS,CAACC,MAAM,CAAC,UAAU,CAAC;QAC/C,IAAI,CAAC9H,QAAQ,CAACuN,IAAI,CAAClG,KAAK,CAACyI,UAAU,GAAGA,UAAU;QAChDpP,WAAW,CAACvB,OAAO,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC;IACH,MAAMgR,aAAa,GAAGvR,KAAK,CAAC6P,KAAK,CAAC,IAAI,CAACjB,SAAS,CAAC,IAAI3Q,UAAU,CAAC,IAAI,CAACmD,QAAQ,CAACqP,IAAI,EAAE,SAAS,CAAC,CAAC,CAACX,KAAK,EAAEvS,CAAC,IAAIA,CAAC,CAAC8D,GAAG,CAACoD,CAAC,IAAI,IAAIvG,qBAAqB,CAACuG,CAAC,CAAC,CAAC,CAAC;IACrJ,IAAI,CAACmK,SAAS,CAAC2C,aAAa,CAAE9M,CAAC,IAAK;MAChC,IAAI+J,KAAK;MACT,IAAIC,GAAG;MACP,IAAIhK,CAAC,CAAC+M,OAAO,KAAK,EAAE,CAAC,yBAAyB;QAC1ChD,KAAK,GAAGxC,MAAM,CAACyF,iBAAiB;MACpC,CAAC,MACI,IAAIhN,CAAC,CAAC+M,OAAO,KAAK,EAAE,CAAC,0BAA0B;QAChDhD,KAAK,GAAG,CAAC;MACb,CAAC,MACI,IAAI/J,CAAC,CAAC+M,OAAO,KAAK,EAAE,CAAC,qBAAqB;QAC3ChD,KAAK,GAAG,IAAI,CAACA,KAAK,KAAK,CAAC,GAAGxC,MAAM,CAACyF,iBAAiB,GAAG,CAAC;MAC3D;MACA,IAAIhN,CAAC,CAAC+M,OAAO,KAAK,EAAE,CAAC,uBAAuB;QACxC/C,GAAG,GAAG,CAAC;MACX,CAAC,MACI,IAAIhK,CAAC,CAAC+M,OAAO,KAAK,EAAE,CAAC,yBAAyB;QAC/C/C,GAAG,GAAGzC,MAAM,CAACyF,iBAAiB;MAClC;MACA,IAAIjD,KAAK,KAAKrM,SAAS,EAAE;QACrBsC,CAAC,CAACsL,cAAc,CAAC,CAAC;QAClBtL,CAAC,CAACuL,eAAe,CAAC,CAAC;QACnB,IAAI,CAACxB,KAAK,GAAGA,KAAK;QAClB,IAAI,CAAC8C,MAAM,CAAC,CAAC;MACjB;MACA,IAAI7C,GAAG,KAAKtM,SAAS,EAAE;QACnBsC,CAAC,CAACsL,cAAc,CAAC,CAAC;QAClBtL,CAAC,CAACuL,eAAe,CAAC,CAAC;QACnB,IAAI,CAACvB,GAAG,GAAGA,GAAG;QACd,MAAMyC,UAAU,GAAG,IAAI,CAAC9P,QAAQ,CAACuN,IAAI,CAAClG,KAAK,CAACyI,UAAU;QACtD,IAAI,CAAC9P,QAAQ,CAACuN,IAAI,CAAClG,KAAK,CAACyI,UAAU,GAAG,OAAO;QAC7C,IAAI,CAACI,MAAM,CAAC,CAAC;QACbI,UAAU,CAAC,MAAM;UACb,IAAI,CAACtQ,QAAQ,CAACuN,IAAI,CAAClG,KAAK,CAACyI,UAAU,GAAGA,UAAU;QACpD,CAAC,EAAE,CAAC,CAAC;MACT;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,CAACS,gBAAgB,GAAG,IAAI,CAAC3D,SAAS,CAAC5H,WAAW;EACtD;EACAkL,MAAMA,CAAC3I,KAAK,GAAG,IAAI,CAACA,KAAK,EAAE;IACvB,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC6F,KAAK,GAAG/N,KAAK,CAAC,IAAI,CAAC+N,KAAK,EAAE,CAAC,EAAEoD,IAAI,CAACC,GAAG,CAAC,CAAC,EAAElJ,KAAK,GAAG,GAAG,CAAC,CAAC;IAC3D,IAAI,CAACvH,QAAQ,CAACuN,IAAI,CAAClG,KAAK,CAAC+F,KAAK,GAAG,GAAG,IAAI,CAACA,KAAK,IAAI;IAClD,IAAI,CAACC,GAAG,GAAGhO,KAAK,CAAC,IAAI,CAACgO,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;IACjC,IAAI,CAACrN,QAAQ,CAACuN,IAAI,CAAClG,KAAK,CAACgG,GAAG,GAAG,GAAG,IAAI,CAACA,GAAG,IAAI;EAClD;EACAqD,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAI,CAAC/D,SAAS,CAAC8D,WAAW,CAACC,OAAO,CAAC;EACvC;EACAC,YAAYA,CAAA,EAAG;IACX,IAAI,CAAChE,SAAS,CAACgE,YAAY,CAAC,CAAC;EACjC;EACMzR,OAAOA,CAAA,EAAG;IAAA,IAAA0R,qBAAA,GAAAA,CAAA,WAAA1R,OAAA;MAAA2R,KAAA;IAAA,OAAAC,iBAAA;MACZD,KAAI,CAACxD,aAAa,CAAC0D,IAAI,CAAC,CAAC;MACzBF,KAAI,CAAC9Q,QAAQ,CAACuN,IAAI,CAAC1F,SAAS,CAACM,GAAG,CAAC,UAAU,CAAC;MAC5C,MAAM5J,OAAO,CAAC,GAAG,CAAC;MAClBsS,qBAAA,GAAAI,IAAA,CAAAH,KAAc,CAAC;IAAC;EACpB;AACJ;AACA,MAAMI,cAAc,CAAC;EACjB,IAAIC,OAAOA,CAAA,EAAG;IAAE,OAAO,IAAI,CAAC5H,QAAQ;EAAE;EACtC,IAAIkD,IAAIA,CAAA,EAAG;IAAE,OAAO,IAAI,CAAC2E,KAAK;EAAE;EAChC,IAAI3E,IAAIA,CAACA,IAAI,EAAE;IACX,IAAIA,IAAI,KAAK,IAAI,CAAC2E,KAAK,EAAE;MACrB;IACJ;IACA,IAAI,CAACA,KAAK,GAAG3E,IAAI;IACjB,IAAI,IAAI,CAAC4E,MAAM,EAAE;MACb,IAAI,CAACA,MAAM,CAAC5E,IAAI,GAAG,IAAI,CAAC2E,KAAK;IACjC;IACA,IAAI,CAAC/H,IAAI,CAACiI,QAAQ,CAAC,CAAC;IACpB,IAAI,CAACC,MAAM,CAAC,CAAC;IACb,IAAI,CAACC,gBAAgB,CAACR,IAAI,CAACvE,IAAI,CAAC;EACpC;EACA,IAAIM,SAASA,CAAA,EAAG;IAAE,OAAO,IAAI,CAAC0E,UAAU;EAAE;EAC1C,IAAI1E,SAASA,CAACA,SAAS,EAAE;IACrB,IAAIA,SAAS,KAAK,IAAI,CAAC0E,UAAU,EAAE;MAC/B;IACJ;IACA,IAAI,CAACA,UAAU,GAAG1E,SAAS;IAC3B,IAAI,IAAI,CAACsE,MAAM,EAAE;MACb,IAAI,CAACA,MAAM,CAACtE,SAAS,GAAG,IAAI,CAAC0E,UAAU;IAC3C;IACA,IAAI,CAACpI,IAAI,CAACiI,QAAQ,CAAC,CAAC;IACpB,IAAI,CAACC,MAAM,CAAC,CAAC;IACb,IAAI,CAACG,qBAAqB,CAACV,IAAI,CAACjE,SAAS,CAAC;EAC9C;EACAjN,WAAWA,CAACuJ,IAAI,EAAE3H,KAAK,EAAEiQ,IAAI,EAAEhI,MAAM,EAAEuD,mBAAmB,EAAEnK,OAAO,GAAG,CAAC,CAAC,EAAE;IACtE,IAAI,CAACsG,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACsI,IAAI,GAAGA,IAAI;IAChB,IAAI,CAAChI,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACuD,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACnK,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACwG,QAAQ,GAAG,EAAE;IAClB,IAAI,CAAChC,KAAK,GAAG,CAAC;IACd,IAAI,CAACiK,gBAAgB,GAAG,IAAI7S,OAAO,CAAC,CAAC;IACrC,IAAI,CAACkP,eAAe,GAAG,IAAI,CAAC2D,gBAAgB,CAAC9C,KAAK;IAClD,IAAI,CAACgD,qBAAqB,GAAG,IAAI/S,OAAO,CAAC,CAAC;IAC1C,IAAI,CAACsP,oBAAoB,GAAG,IAAI,CAACyD,qBAAqB,CAAChD,KAAK;IAC5D,IAAI,CAACkD,mBAAmB,GAAG,IAAIjT,OAAO,CAAC,CAAC;IACxC,IAAI,CAACkT,qBAAqB,GAAG,IAAIlT,OAAO,CAAC,CAAC;IAC1C,IAAI,CAACmT,oBAAoB,GAAG,IAAI,CAACD,qBAAqB,CAACnD,KAAK;IAC5D,IAAI,CAACqD,kBAAkB,GAAG,IAAI7S,eAAe,CAAC,CAAC;IAC/C,IAAI,CAACwB,WAAW,GAAG,IAAIxB,eAAe,CAAC,CAAC;IACxC,IAAI,CAACkS,KAAK,GAAG/H,IAAI,CAACtG,OAAO,CAACiP,eAAe,IAAI7G,YAAY,CAAC4C,SAAS;IACnE,IAAI,CAAC0D,UAAU,GAAGpI,IAAI,CAACtG,OAAO,CAACkP,oBAAoB,IAAIzH,iBAAiB,CAACyC,KAAK;IAC9EvL,KAAK,CAACwQ,WAAW,CAAC,IAAI,CAACC,gBAAgB,EAAE,IAAI,EAAE,IAAI,CAACzR,WAAW,CAAC;EACpE;EACAwF,aAAaA,CAACkM,aAAa,GAAG,CAAC,CAAC,EAAE;IAC9B,IAAIA,aAAa,CAACJ,eAAe,KAAKjR,SAAS,EAAE;MAC7C,IAAI,CAAC0L,IAAI,GAAG2F,aAAa,CAACJ,eAAe;IAC7C;IACA,IAAII,aAAa,CAACH,oBAAoB,KAAKlR,SAAS,EAAE;MAClD,IAAI,CAACgM,SAAS,GAAGqF,aAAa,CAACH,oBAAoB;IACvD;EACJ;EACAE,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAAC,IAAI,CAACd,MAAM,IAAI,IAAI,CAACF,OAAO,CAACzO,MAAM,KAAK,CAAC,EAAE;MAC3C;IACJ;IACA,IAAI,CAAC2G,IAAI,CAACiI,QAAQ,CAAC,CAAC;IACpB,IAAI,CAACC,MAAM,CAAC,CAAC;EACjB;EACAA,MAAMA,CAAA,EAAG;IACL,MAAMc,SAAS,GAAG,IAAI,CAAC1I,MAAM,CAACV,UAAU,GAAG,CAAC,IAAI,IAAI,CAACU,MAAM,CAACR,UAAU,KAAK,CAAC;IAC5E,IAAI,IAAI,CAACgI,OAAO,IAAIkB,SAAS,EAAE;MAC3BzS,KAAK,CAACL,QAAQ,CAAC,mBAAmB,EAAE,YAAY,CAAC,CAAC;MAClD,IAAI,IAAI,CAAC8J,IAAI,CAACtG,OAAO,CAACuP,mBAAmB,IAAI,IAAI,EAAE;QAC/C,IAAI,CAACjB,MAAM,EAAEX,WAAW,CAAC;UAAE6B,IAAI,EAAE,CAAC,CAAC;UAA2BC,OAAO,EAAEjT,QAAQ,CAAC,WAAW,EAAE,oBAAoB;QAAE,CAAC,CAAC;MACzH,CAAC,MACI;QACD,IAAI,CAAC8R,MAAM,EAAEX,WAAW,CAAC;UAAE6B,IAAI,EAAE,CAAC,CAAC;QAA0B,CAAC,CAAC;MACnE;IACJ,CAAC,MACI;MACD,IAAI,CAAClB,MAAM,EAAET,YAAY,CAAC,CAAC;MAC3B,IAAI,IAAI,CAACO,OAAO,EAAE;QACdvR,KAAK,CAACL,QAAQ,CAAC,iBAAiB,EAAE,aAAa,EAAE,IAAI,CAACoK,MAAM,CAACR,UAAU,CAAC,CAAC;MAC7E;IACJ;EACJ;EACAsJ,gBAAgBA,CAACvS,IAAI,EAAE;IACnB,IAAI,CAAC,IAAI,CAACmR,MAAM,IAAI,CAAC,IAAI,CAACF,OAAO,EAAE;MAC/B,OAAO,IAAI;IACf;IACA,IAAI,IAAI,CAACxH,MAAM,CAACV,UAAU,GAAG,CAAC,IAAI,IAAI,CAACU,MAAM,CAACR,UAAU,IAAI,CAAC,EAAE;MAC3D,OAAO,IAAI;IACf;IACA,OAAO,CAACnK,UAAU,CAAC0T,SAAS,CAACxS,IAAI,CAACyS,UAAU,CAAC;EACjD;EACAzC,MAAMA,CAAC3I,KAAK,EAAE;IACV,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC8J,MAAM,EAAEnB,MAAM,CAAC3I,KAAK,CAAC;EAC9B;EACApI,OAAOA,CAAA,EAAG;IACN,IAAI,CAACyT,QAAQ,GAAG7R,SAAS;IACzB,IAAI,CAAC6Q,mBAAmB,CAACzS,OAAO,CAAC,CAAC;IAClC,IAAI,CAAC4S,kBAAkB,CAAC5S,OAAO,CAAC,CAAC;IACjC,IAAI,CAACuB,WAAW,CAACvB,OAAO,CAAC,CAAC;EAC9B;AACJ;AACA,SAAS0T,2BAA2BA,CAACC,KAAK,EAAEC,KAAK,EAAE;EAC/C,OAAOD,KAAK,CAACE,QAAQ,KAAKD,KAAK,CAACC,QAAQ,IAAIC,sBAAsB,CAACH,KAAK,EAAEC,KAAK,CAAC;AACpF;AACA,SAASE,sBAAsBA,CAACH,KAAK,EAAEC,KAAK,EAAE;EAC1C,OAAOD,KAAK,CAAC5S,IAAI,CAACC,OAAO,KAAK4S,KAAK,CAAC7S,IAAI,CAACC,OAAO,IAC5C2S,KAAK,CAACI,UAAU,KAAKH,KAAK,CAACG,UAAU,IACrCJ,KAAK,CAAClO,MAAM,KAAKmO,KAAK,CAACnO,MAAM,IAC7BkO,KAAK,CAACK,QAAQ,KAAKJ,KAAK,CAACI,QAAQ;AACzC;AACA,MAAMC,iBAAiB,CAAC;EACpBtT,WAAWA,CAACuT,WAAW,GAAG,EAAE,EAAE;IAC1B,IAAI,CAACA,WAAW,GAAGA,WAAW;EAClC;EACA,IAAIC,KAAKA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACD,WAAW,CAAC3Q,MAAM;EAAE;EAC9C6Q,KAAKA,CAACC,KAAK,EAAE;IACT,OAAOrV,MAAM,CAAC,IAAI,CAACkV,WAAW,EAAEG,KAAK,CAACH,WAAW,EAAER,2BAA2B,CAAC;EACnF;EACAY,wBAAwBA,CAAA,EAAG;IACvB,IAAI,IAAI,CAACH,KAAK,KAAK,CAAC,EAAE;MAClB,OAAO,KAAK;IAChB;IACA,MAAMI,cAAc,GAAG,IAAI,CAACL,WAAW,CAAC,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;IACvD,IAAI,IAAI,CAACA,KAAK,KAAK,CAAC,EAAE;MAClB,OAAOI,cAAc,CAACV,QAAQ,KAAK,CAAC;IACxC;IACA,MAAMW,oBAAoB,GAAG,IAAI,CAACN,WAAW,CAAC,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;IAC7D,OAAOK,oBAAoB,CAACX,QAAQ,GAAGW,oBAAoB,CAAC/O,MAAM,KAAK8O,cAAc,CAACV,QAAQ;EAClG;EACAY,qBAAqBA,CAACC,aAAa,EAAE;IACjC,IAAI,CAAC1V,MAAM,CAAC,IAAI,CAACkV,WAAW,EAAEQ,aAAa,CAACR,WAAW,EAAEJ,sBAAsB,CAAC,EAAE;MAC9E,OAAO,KAAK;IAChB;IACA,IAAI,IAAI,CAACK,KAAK,KAAK,CAAC,EAAE;MAClB,OAAO,KAAK;IAChB;IACA,MAAMI,cAAc,GAAG,IAAI,CAACL,WAAW,CAAC,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;IACvD,MAAMQ,sBAAsB,GAAGD,aAAa,CAACR,WAAW,CAACQ,aAAa,CAACP,KAAK,GAAG,CAAC,CAAC;IACjF,OAAOI,cAAc,CAACV,QAAQ,KAAKc,sBAAsB,CAACd,QAAQ;EACtE;AACJ;AACA,MAAMe,2BAA2B,CAAC;EAC9BC,0BAA0BA,CAACX,WAAW,EAAEY,wBAAwB,EAAEC,eAAe,EAAE;IAC/E,KAAK,IAAIpJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuI,WAAW,CAAC3Q,MAAM,EAAEoI,CAAC,EAAE,EAAE;MACzC,MAAMqJ,UAAU,GAAGd,WAAW,CAACvI,CAAC,CAAC;MACjC,MAAMsJ,gBAAgB,GAAGD,UAAU,CAACnB,QAAQ,GAAGmB,UAAU,CAACvP,MAAM;MAChE,IAAIwP,gBAAgB,GAAGF,eAAe,IAAIpJ,CAAC,IAAImJ,wBAAwB,EAAE;QACrE,OAAOZ,WAAW,CAACgB,KAAK,CAAC,CAAC,EAAEvJ,CAAC,CAAC;MAClC;IACJ;IACA,OAAOuI,WAAW;EACtB;AACJ;AACA,MAAMiB,sBAAsB,SAASrV,UAAU,CAAC;EAC5Ca,WAAWA,CAACuJ,IAAI,EAAE3H,KAAK,EAAEiQ,IAAI,EAAE4C,SAAS,EAAEC,YAAY,EAAEzR,OAAO,GAAG,CAAC,CAAC,EAAE;IAClE,KAAK,CAAC,CAAC;IACP,IAAI,CAACsG,IAAI,GAAGA,IAAI;IAChB,IAAI,CAAC3H,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACiQ,IAAI,GAAGA,IAAI;IAChB,IAAI,CAAC6C,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,kBAAkB,GAAG,GAAG;IAC7B,MAAMC,mBAAmB,GAAG,IAAI,CAACC,sBAAsB,CAAC5R,OAAO,CAAC;IAChE,IAAI,CAACkR,wBAAwB,GAAGS,mBAAmB,CAACT,wBAAwB;IAC5E,IAAI,CAACW,oBAAoB,GAAG7R,OAAO,CAAC6R,oBAAoB,IAAI,IAAIb,2BAA2B,CAAC,CAAC;IAC7F,IAAI,CAACc,OAAO,GAAG,IAAI,CAACrH,SAAS,CAAC,IAAIsH,kBAAkB,CAACnD,IAAI,CAACoD,oBAAoB,CAAC,CAAC,EAAEpD,IAAI,EAAEtI,IAAI,EAAEkL,SAAS,EAAEC,YAAY,EAAEzR,OAAO,CAACQ,qBAAqB,CAAC,CAAC;IACtJ,IAAI,CAACyR,mBAAmB,GAAG,IAAI,CAACH,OAAO,CAACG,mBAAmB;IAC3D,IAAI,CAACC,aAAa,GAAG,IAAI,CAACJ,OAAO,CAACI,aAAa;IAC/C,IAAI,CAACzH,SAAS,CAACmE,IAAI,CAACuD,WAAW,CAAC,MAAM,IAAI,CAACnF,MAAM,CAAC,CAAC,CAAC,CAAC;IACrD,IAAI,CAACvC,SAAS,CAACmE,IAAI,CAACwD,wBAAwB,CAAC,MAAM,IAAI,CAACpF,MAAM,CAAC,CAAC,CAAC,CAAC;IAClE,IAAI,CAACvC,SAAS,CAACnE,IAAI,CAAChE,wBAAwB,CAAC,MAAM,IAAI,CAAC0K,MAAM,CAAC,CAAC,CAAC,CAAC;IAClE,IAAI,CAACA,MAAM,CAAC,CAAC;EACjB;EACA,IAAInL,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACiQ,OAAO,CAACjQ,MAAM;EAC9B;EACAwQ,eAAeA,CAACxQ,MAAM,EAAE;IACpB,IAAImC,KAAK;IACT,IAAInC,MAAM,KAAK,CAAC,EAAE;MACdmC,KAAK,GAAG,IAAI,CAAC4K,IAAI,CAAC0D,iBAAiB;IACvC,CAAC,MACI;MACDtO,KAAK,GAAG,IAAI,CAAC4K,IAAI,CAAC2D,OAAO,CAAC1Q,MAAM,GAAG,IAAI,CAAC+M,IAAI,CAAC4D,SAAS,CAAC;IAC3D;IACA,IAAIxO,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAI,IAAI,CAAC4K,IAAI,CAACjP,MAAM,EAAE;MACxC,OAAO3B,SAAS;IACpB;IACA,OAAO,IAAI,CAAC4Q,IAAI,CAACxR,OAAO,CAAC4G,KAAK,CAAC;EACnC;EACAgJ,MAAMA,CAAA,EAAG;IACL,MAAMyF,gBAAgB,GAAG,IAAI,CAACJ,eAAe,CAAC,CAAC,CAAC;IAChD;IACA,IAAI,CAACI,gBAAgB,IAAI,IAAI,CAACnM,IAAI,CAACkM,SAAS,KAAK,CAAC,EAAE;MAChD,IAAI,CAACV,OAAO,CAACY,QAAQ,CAAC1U,SAAS,CAAC;MAChC;IACJ;IACA,MAAM2U,WAAW,GAAG,IAAI,CAACC,eAAe,CAACH,gBAAgB,CAAC;IAC1D,IAAI,CAACX,OAAO,CAACY,QAAQ,CAACC,WAAW,CAAC;EACtC;EACAC,eAAeA,CAACH,gBAAgB,EAAE;IAC9B,MAAMnC,WAAW,GAAG,EAAE;IACtB,IAAIuC,2BAA2B,GAAGJ,gBAAgB;IAClD,IAAIK,iBAAiB,GAAG,CAAC;IACzB,IAAIC,cAAc,GAAG,IAAI,CAACC,iBAAiB,CAACH,2BAA2B,EAAE7U,SAAS,EAAE8U,iBAAiB,CAAC;IACtG,OAAOC,cAAc,EAAE;MACnBzC,WAAW,CAACtI,IAAI,CAAC+K,cAAc,CAAC;MAChCD,iBAAiB,IAAIC,cAAc,CAAClR,MAAM;MAC1C,IAAIyO,WAAW,CAAC3Q,MAAM,IAAI,IAAI,CAACuR,wBAAwB,EAAE;QACrD2B,2BAA2B,GAAG,IAAI,CAACI,kBAAkB,CAACF,cAAc,CAAC;QACrE,IAAI,CAACF,2BAA2B,EAAE;UAC9B;QACJ;MACJ;MACAE,cAAc,GAAG,IAAI,CAACC,iBAAiB,CAACH,2BAA2B,EAAEE,cAAc,CAAC5V,IAAI,EAAE2V,iBAAiB,CAAC;IAChH;IACA,MAAMI,qBAAqB,GAAG,IAAI,CAACC,oBAAoB,CAAC7C,WAAW,CAAC;IACpE,OAAO4C,qBAAqB,CAACvT,MAAM,GAAG,IAAI0Q,iBAAiB,CAAC6C,qBAAqB,CAAC,GAAGlV,SAAS;EAClG;EACAiV,kBAAkBA,CAACG,kBAAkB,EAAE;IACnC,OAAO,IAAI,CAACf,eAAe,CAACe,kBAAkB,CAACnD,QAAQ,GAAGmD,kBAAkB,CAACvR,MAAM,CAAC;EACxF;EACAmR,iBAAiBA,CAACH,2BAA2B,EAAEO,kBAAkB,EAAEN,iBAAiB,EAAE;IAClF,MAAMC,cAAc,GAAG,IAAI,CAACM,wBAAwB,CAACR,2BAA2B,EAAEO,kBAAkB,CAAC;IACrG,IAAI,CAACL,cAAc,EAAE;MACjB,OAAO/U,SAAS;IACpB;IACA,IAAI+U,cAAc,KAAKF,2BAA2B,EAAE;MAChD,IAAI,CAAC,IAAI,CAACS,uBAAuB,CAACT,2BAA2B,CAAC,EAAE;QAC5D,OAAO7U,SAAS;MACpB;MACA,IAAI,IAAI,CAACuV,kCAAkC,CAACV,2BAA2B,EAAEC,iBAAiB,CAAC,EAAE;QACzF,OAAO9U,SAAS;MACpB;IACJ;IACA,OAAO,IAAI,CAACwV,sBAAsB,CAACT,cAAc,EAAED,iBAAiB,CAAC;EACzE;EACAS,kCAAkCA,CAACpW,IAAI,EAAE2V,iBAAiB,EAAE;IACxD,MAAMW,SAAS,GAAG,IAAI,CAACC,YAAY,CAACvW,IAAI,CAAC;IACzC,MAAMwW,UAAU,GAAG,IAAI,CAAC/E,IAAI,CAACgF,aAAa,CAACH,SAAS,CAAC;IACrD,MAAMI,cAAc,GAAGf,iBAAiB;IACxC,OAAO,IAAI,CAAClE,IAAI,CAAC4D,SAAS,KAAKmB,UAAU,GAAGE,cAAc;EAC9D;EACAL,sBAAsBA,CAACrW,IAAI,EAAE2W,wBAAwB,EAAE;IACnD,MAAMjS,MAAM,GAAG,IAAI,CAAC4P,YAAY,CAAChQ,SAAS,CAACtE,IAAI,CAAC;IAChD,MAAM;MAAEgT,UAAU;MAAEC;IAAS,CAAC,GAAG,IAAI,CAAC2D,YAAY,CAAC5W,IAAI,CAAC;IACxD,MAAM8S,QAAQ,GAAG,IAAI,CAAC+D,2BAA2B,CAAC5D,QAAQ,EAAE0D,wBAAwB,EAAEjS,MAAM,CAAC;IAC7F,OAAO;MAAE1E,IAAI;MAAE8S,QAAQ;MAAEpO,MAAM;MAAEsO,UAAU;MAAEC;IAAS,CAAC;EAC3D;EACAiD,wBAAwBA,CAAClW,IAAI,EAAE8W,gBAAgB,GAAGjW,SAAS,EAAE;IACzD,IAAIkW,eAAe,GAAG/W,IAAI;IAC1B,IAAIgX,uBAAuB,GAAG,IAAI,CAACC,aAAa,CAACF,eAAe,CAAC;IACjE,OAAOC,uBAAuB,EAAE;MAC5B,IAAIA,uBAAuB,KAAKF,gBAAgB,EAAE;QAC9C,OAAOC,eAAe;MAC1B;MACAA,eAAe,GAAGC,uBAAuB;MACzCA,uBAAuB,GAAG,IAAI,CAACC,aAAa,CAACF,eAAe,CAAC;IACjE;IACA,IAAID,gBAAgB,KAAKjW,SAAS,EAAE;MAChC,OAAOkW,eAAe;IAC1B;IACA,OAAOlW,SAAS;EACpB;EACAgW,2BAA2BA,CAACK,mBAAmB,EAAEC,oBAAoB,EAAEC,gBAAgB,EAAE;IACrF,IAAIC,oBAAoB,GAAG,IAAI,CAAC5F,IAAI,CAAC6F,cAAc,CAACJ,mBAAmB,CAAC;IACxE;IACA;IACA,IAAIG,oBAAoB,KAAK,IAAI,IAAI,IAAI,CAAC5F,IAAI,CAAC0D,iBAAiB,KAAK+B,mBAAmB,IAAIA,mBAAmB,GAAG,CAAC,GAAG,IAAI,CAACzF,IAAI,CAACjP,MAAM,EAAE;MACpI,MAAM+U,UAAU,GAAG,IAAI,CAACjD,YAAY,CAAChQ,SAAS,CAAC,IAAI,CAACmN,IAAI,CAACxR,OAAO,CAACiX,mBAAmB,CAAC,CAAC;MACtF,MAAMM,mBAAmB,GAAG,IAAI,CAAC/F,IAAI,CAAC6F,cAAc,CAACJ,mBAAmB,GAAG,CAAC,CAAC;MAC7EG,oBAAoB,GAAGG,mBAAmB,GAAGA,mBAAmB,GAAGD,UAAU,GAAG,IAAI,CAAC9F,IAAI,CAACgG,YAAY,GAAG,IAAI;IACjH;IACA,IAAIJ,oBAAoB,KAAK,IAAI,EAAE;MAC/B,OAAOF,oBAAoB;IAC/B;IACA,MAAMO,aAAa,GAAG,IAAI,CAACjG,IAAI,CAACxR,OAAO,CAACiX,mBAAmB,CAAC;IAC5D,MAAMS,eAAe,GAAG,IAAI,CAACrD,YAAY,CAAChQ,SAAS,CAACoT,aAAa,CAAC;IAClE,MAAME,cAAc,GAAGP,oBAAoB,GAAG,IAAI,CAAC5F,IAAI,CAACgG,YAAY;IACpE,MAAMI,iBAAiB,GAAGD,cAAc,GAAGD,eAAe;IAC1D,IAAIR,oBAAoB,GAAGC,gBAAgB,GAAGS,iBAAiB,IAAIV,oBAAoB,IAAIU,iBAAiB,EAAE;MAC1G,OAAOA,iBAAiB,GAAGT,gBAAgB;IAC/C;IACA,OAAOD,oBAAoB;EAC/B;EACAnB,oBAAoBA,CAAC7C,WAAW,EAAE;IAC9B,IAAIA,WAAW,CAAC3Q,MAAM,KAAK,CAAC,EAAE;MAC1B,OAAO,EAAE;IACb;IACA;IACA,MAAMsV,yBAAyB,GAAG,IAAI,CAACrG,IAAI,CAACgG,YAAY,GAAG,IAAI,CAAClD,kBAAkB;IAClF,MAAMf,cAAc,GAAGL,WAAW,CAACA,WAAW,CAAC3Q,MAAM,GAAG,CAAC,CAAC;IAC1D,IAAI2Q,WAAW,CAAC3Q,MAAM,IAAI,IAAI,CAACuR,wBAAwB,IAAIP,cAAc,CAACV,QAAQ,GAAGU,cAAc,CAAC9O,MAAM,IAAIoT,yBAAyB,EAAE;MACrI,OAAO3E,WAAW;IACtB;IACA;IACA,MAAM4E,sBAAsB,GAAG,IAAI,CAACrD,oBAAoB,CAACZ,0BAA0B,CAACX,WAAW,EAAE,IAAI,CAACY,wBAAwB,EAAE+D,yBAAyB,CAAC;IAC1J,IAAI,CAACC,sBAAsB,CAACvV,MAAM,EAAE;MAChC,OAAO,EAAE;IACb;IACA;IACA,MAAMwV,yBAAyB,GAAGD,sBAAsB,CAACA,sBAAsB,CAACvV,MAAM,GAAG,CAAC,CAAC;IAC3F,IAAIuV,sBAAsB,CAACvV,MAAM,GAAG,IAAI,CAACuR,wBAAwB,IAAIiE,yBAAyB,CAAClF,QAAQ,GAAGkF,yBAAyB,CAACtT,MAAM,GAAGoT,yBAAyB,EAAE;MACpK,MAAM,IAAIG,KAAK,CAAC,2CAA2C,CAAC;IAChE;IACA,OAAOF,sBAAsB;EACjC;EACAd,aAAaA,CAACjX,IAAI,EAAE;IAChB,MAAMkY,YAAY,GAAG,IAAI,CAAC1W,KAAK,CAACE,eAAe,CAAC1B,IAAI,CAAC;IACrD,MAAMmY,cAAc,GAAG,IAAI,CAAC3W,KAAK,CAACU,qBAAqB,CAACgW,YAAY,CAAC;IACrE,OAAOC,cAAc,GAAG,IAAI,CAAC3W,KAAK,CAACY,OAAO,CAAC+V,cAAc,CAAC,GAAGtX,SAAS;EAC1E;EACAsV,uBAAuBA,CAACnW,IAAI,EAAE;IAC1B,MAAMkY,YAAY,GAAG,IAAI,CAAC1W,KAAK,CAACE,eAAe,CAAC1B,IAAI,CAAC;IACrD,OAAO,IAAI,CAACwB,KAAK,CAACiB,kBAAkB,CAACyV,YAAY,CAAC,GAAG,CAAC;EAC1D;EACA3B,YAAYA,CAACvW,IAAI,EAAE;IACf,MAAMkY,YAAY,GAAG,IAAI,CAAC1W,KAAK,CAACE,eAAe,CAAC1B,IAAI,CAAC;IACrD,MAAMsW,SAAS,GAAG,IAAI,CAAC9U,KAAK,CAACc,YAAY,CAAC4V,YAAY,CAAC;IACvD,OAAO5B,SAAS;EACpB;EACAM,YAAYA,CAAC5W,IAAI,EAAE;IACf,MAAMkY,YAAY,GAAG,IAAI,CAAC1W,KAAK,CAACE,eAAe,CAAC1B,IAAI,CAAC;IACrD,MAAMgT,UAAU,GAAG,IAAI,CAACxR,KAAK,CAACc,YAAY,CAAC4V,YAAY,CAAC;IACxD,IAAIlF,UAAU,GAAG,CAAC,EAAE;MAChB,MAAM,IAAIiF,KAAK,CAAC,wBAAwB,CAAC;IAC7C;IACA,MAAMG,WAAW,GAAG,IAAI,CAAC5W,KAAK,CAACiB,kBAAkB,CAACyV,YAAY,CAAC;IAC/D,MAAMjF,QAAQ,GAAGD,UAAU,GAAGoF,WAAW,GAAG,CAAC;IAC7C,OAAO;MAAEpF,UAAU;MAAEC;IAAS,CAAC;EACnC;EACAoF,0BAA0BA,CAACrY,IAAI,EAAE;IAC7B,MAAMsY,SAAS,GAAG,EAAE;IACpB,IAAIvB,eAAe,GAAG,IAAI,CAACE,aAAa,CAACjX,IAAI,CAAC;IAC9C,OAAO+W,eAAe,EAAE;MACpBuB,SAAS,CAACzN,IAAI,CAACkM,eAAe,CAAC;MAC/BA,eAAe,GAAG,IAAI,CAACE,aAAa,CAACF,eAAe,CAAC;IACzD;IACA,IAAIwB,YAAY,GAAG,CAAC;IACpB,KAAK,IAAI3N,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0N,SAAS,CAAC9V,MAAM,IAAIoI,CAAC,GAAG,IAAI,CAACmJ,wBAAwB,EAAEnJ,CAAC,EAAE,EAAE;MAC5E2N,YAAY,IAAI,IAAI,CAACjE,YAAY,CAAChQ,SAAS,CAACgU,SAAS,CAAC1N,CAAC,CAAC,CAAC;IAC7D;IACA,OAAO2N,YAAY;EACvB;EACA3J,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC+F,OAAO,CAAC/F,QAAQ,CAAC,CAAC;EAC3B;EACA;EACA4J,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC7D,OAAO,CAAC6D,WAAW,CAAC,CAAC;EACrC;EACAxS,aAAaA,CAACkM,aAAa,GAAG,CAAC,CAAC,EAAE;IAC9B,IAAI,CAACA,aAAa,CAAC6B,wBAAwB,EAAE;MACzC;IACJ;IACA,MAAM0E,gBAAgB,GAAG,IAAI,CAAChE,sBAAsB,CAACvC,aAAa,CAAC;IACnE,IAAI,IAAI,CAAC6B,wBAAwB,KAAK0E,gBAAgB,CAAC1E,wBAAwB,EAAE;MAC7E,IAAI,CAACA,wBAAwB,GAAG0E,gBAAgB,CAAC1E,wBAAwB;MACzE,IAAI,CAAClE,MAAM,CAAC,CAAC;IACjB;EACJ;EACA4E,sBAAsBA,CAAC5R,OAAO,EAAE;IAC5B,IAAIkR,wBAAwB,GAAG,CAAC;IAChC,IAAI,OAAOlR,OAAO,CAACkR,wBAAwB,KAAK,QAAQ,EAAE;MACtDA,wBAAwB,GAAGzD,IAAI,CAACC,GAAG,CAAC1N,OAAO,CAACkR,wBAAwB,EAAE,CAAC,CAAC;IAC5E;IACA,OAAO;MAAEA;IAAyB,CAAC;EACvC;AACJ;AACA,MAAMa,kBAAkB,CAAC;EACrBhV,WAAWA,CAAC6G,SAAS,EAAEgL,IAAI,EAAEtI,IAAI,EAAEuP,aAAa,EAAEpE,YAAY,EAAEjR,qBAAqB,EAAE;IACnF,IAAI,CAACoO,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACtI,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACuP,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACpE,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACjR,qBAAqB,GAAGA,qBAAqB;IAClD,IAAI,CAACsV,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACC,yBAAyB,GAAG,IAAI5Z,eAAe,CAAC,CAAC;IACtD,IAAI,CAAC6Z,YAAY,GAAG5c,CAAC,CAAC,qCAAqC,CAAC;IAC5DwK,SAAS,CAAC+B,WAAW,CAAC,IAAI,CAACqQ,YAAY,CAAC;IACxC,MAAMC,MAAM,GAAG7c,CAAC,CAAC,sCAAsC,CAAC;IACxD,IAAI,CAAC4c,YAAY,CAACrQ,WAAW,CAACsQ,MAAM,CAAC;IACrC,IAAI,CAACC,iBAAiB,GAAG,IAAIC,iBAAiB,CAAC,IAAI,CAACH,YAAY,EAAEpH,IAAI,CAAC;IACvE,IAAI,CAACqD,mBAAmB,GAAG,IAAI,CAACiE,iBAAiB,CAACjE,mBAAmB;IACrE,IAAI,CAACC,aAAa,GAAG,IAAI,CAACgE,iBAAiB,CAAChE,aAAa;EAC7D;EACA,IAAIrQ,MAAMA,CAAA,EAAG;IACT,IAAI,CAAC,IAAI,CAACuU,cAAc,EAAE;MACtB,OAAO,CAAC;IACZ;IACA,MAAMC,WAAW,GAAG,IAAI,CAACD,cAAc,CAAC9F,WAAW,CAAC,IAAI,CAAC8F,cAAc,CAAC7F,KAAK,GAAG,CAAC,CAAC;IAClF,OAAO8F,WAAW,CAACpG,QAAQ,GAAGoG,WAAW,CAACxU,MAAM;EACpD;EACA6Q,QAAQA,CAACjC,KAAK,EAAE;IACZ,MAAM6F,UAAU,GAAG,CAAC,CAAC,IAAI,CAACF,cAAc,IAAI,IAAI,CAACA,cAAc,CAAC7F,KAAK,GAAG,CAAC;IACzE,MAAMgG,SAAS,GAAG,CAAC,CAAC9F,KAAK,IAAIA,KAAK,CAACF,KAAK,GAAG,CAAC;IAC5C;IACA,IAAK,CAAC+F,UAAU,IAAI,CAACC,SAAS,IAAMD,UAAU,IAAIC,SAAS,IAAI,IAAI,CAACH,cAAc,CAAC5F,KAAK,CAACC,KAAK,CAAE,EAAE;MAC9F;IACJ;IACA;IACA,IAAI6F,UAAU,KAAKC,SAAS,EAAE;MAC1B,IAAI,CAACC,UAAU,CAACD,SAAS,CAAC;IAC9B;IACA,IAAI,CAACA,SAAS,EAAE;MACZ,IAAI,CAACH,cAAc,GAAGpY,SAAS;MAC/B,IAAI,CAAC8X,iBAAiB,GAAG,EAAE;MAC3B,IAAI,CAACC,yBAAyB,CAAC/P,KAAK,CAAC,CAAC;MACtC;IACJ;IACA,MAAM2K,cAAc,GAAGF,KAAK,CAACH,WAAW,CAACG,KAAK,CAACF,KAAK,GAAG,CAAC,CAAC;IACzD;IACA,IAAI,IAAI,CAAC6F,cAAc,IAAI3F,KAAK,CAACI,qBAAqB,CAAC,IAAI,CAACuF,cAAc,CAAC,EAAE;MACzE,IAAI,CAACN,iBAAiB,CAAC,IAAI,CAACM,cAAc,CAAC7F,KAAK,GAAG,CAAC,CAAC,CAACjM,KAAK,CAACgG,GAAG,GAAG,GAAGqG,cAAc,CAACV,QAAQ,IAAI;IACpG;IACA;IAAA,KACK;MACD,IAAI,CAAC8F,yBAAyB,CAAC/P,KAAK,CAAC,CAAC;MACtC,MAAM/I,QAAQ,GAAGiK,KAAK,CAACuJ,KAAK,CAACF,KAAK,CAAC;MACnC,KAAK,IAAIkG,WAAW,GAAGhG,KAAK,CAACF,KAAK,GAAG,CAAC,EAAEkG,WAAW,IAAI,CAAC,EAAEA,WAAW,EAAE,EAAE;QACrE,MAAMrF,UAAU,GAAGX,KAAK,CAACH,WAAW,CAACmG,WAAW,CAAC;QACjD,MAAM;UAAErZ,OAAO;UAAEsZ;QAAW,CAAC,GAAG,IAAI,CAACC,aAAa,CAACvF,UAAU,EAAEqF,WAAW,EAAEhG,KAAK,CAACF,KAAK,CAAC;QACxFtT,QAAQ,CAACwZ,WAAW,CAAC,GAAGrZ,OAAO;QAC/B,IAAI,CAAC4Y,YAAY,CAACrQ,WAAW,CAACvI,OAAO,CAAC;QACtC,IAAI,CAAC2Y,yBAAyB,CAAC3Q,GAAG,CAACsR,UAAU,CAAC;MAClD;MACA,IAAI,CAACR,iBAAiB,CAACU,cAAc,CAAC3Z,QAAQ,EAAEwT,KAAK,CAAC;MACtD,IAAI,CAACqF,iBAAiB,GAAG7Y,QAAQ;IACrC;IACA,IAAI,CAACmZ,cAAc,GAAG3F,KAAK;IAC3B;IACA,IAAI,CAACuF,YAAY,CAAC1R,KAAK,CAACzC,MAAM,GAAG,GAAG8O,cAAc,CAACV,QAAQ,GAAGU,cAAc,CAAC9O,MAAM,IAAI;EAC3F;EACA8U,aAAaA,CAACvF,UAAU,EAAEqF,WAAW,EAAEI,gBAAgB,EAAE;IACrD,MAAMpD,SAAS,GAAGrC,UAAU,CAACjB,UAAU;IACvC;IACA,MAAM2G,aAAa,GAAGC,QAAQ,CAACJ,aAAa,CAAC,KAAK,CAAC;IACnDG,aAAa,CAACxS,KAAK,CAACgG,GAAG,GAAG,GAAG8G,UAAU,CAACnB,QAAQ,IAAI;IACpD,IAAI,IAAI,CAAC3J,IAAI,CAACtG,OAAO,CAACgX,YAAY,KAAK,KAAK,EAAE;MAC1CF,aAAa,CAACxS,KAAK,CAACzC,MAAM,GAAG,GAAGuP,UAAU,CAACvP,MAAM,IAAI;IACzD;IACA,IAAI,IAAI,CAACyE,IAAI,CAACtG,OAAO,CAACiX,gBAAgB,KAAK,KAAK,EAAE;MAC9CH,aAAa,CAACxS,KAAK,CAAC4S,UAAU,GAAG,GAAG9F,UAAU,CAACvP,MAAM,IAAI;IAC7D;IACAiV,aAAa,CAAChS,SAAS,CAACM,GAAG,CAAC,wBAAwB,CAAC;IACrD0R,aAAa,CAAChS,SAAS,CAACM,GAAG,CAAC,iBAAiB,CAAC;IAC9C0R,aAAa,CAACpS,YAAY,CAAC,YAAY,EAAE,GAAG+O,SAAS,EAAE,CAAC;IACxDqD,aAAa,CAACpS,YAAY,CAAC,aAAa,EAAE+O,SAAS,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM,GAAG,KAAK,CAAC;IAC/EqD,aAAa,CAACpS,YAAY,CAAC,IAAI,EAAE,IAAI,CAACkK,IAAI,CAACuI,YAAY,CAAC1D,SAAS,CAAC,CAAC;IACnE,MAAM2D,uBAAuB,GAAG,IAAI,CAACC,0BAA0B,CAACP,aAAa,EAAE1F,UAAU,CAACjU,IAAI,CAACC,OAAO,EAAEqZ,WAAW,EAAEI,gBAAgB,CAAC;IACtI;IACA,MAAMS,cAAc,GAAG,IAAI,CAAC7F,YAAY,CAAC/P,aAAa,CAAC0P,UAAU,CAACjU,IAAI,CAAC;IACvE,MAAMkF,QAAQ,GAAG,IAAI,CAACwT,aAAa,CAAC0B,IAAI,CAAElV,QAAQ,IAAKA,QAAQ,CAACa,UAAU,KAAKoU,cAAc,CAAC;IAC9F,IAAI,CAACjV,QAAQ,EAAE;MACX,MAAM,IAAI+S,KAAK,CAAC,qCAAqCkC,cAAc,EAAE,CAAC;IAC1E;IACA;IACA;IACA,IAAIE,QAAQ,GAAGpG,UAAU,CAACjU,IAAI;IAC9B,IAAIqa,QAAQ,KAAK,IAAI,CAAClR,IAAI,CAAC/G,OAAO,CAAC,IAAI,CAAC+G,IAAI,CAACzH,eAAe,CAACuS,UAAU,CAACjU,IAAI,CAAC,CAAC,EAAE;MAC5Eqa,QAAQ,GAAG,IAAIC,KAAK,CAACrG,UAAU,CAACjU,IAAI,EAAE,CAAC,CAAC,CAAC;IAC7C;IACA;IACA,MAAMmG,YAAY,GAAGjB,QAAQ,CAACsB,cAAc,CAACmT,aAAa,CAAC;IAC3DzU,QAAQ,CAAC0B,aAAa,CAACyT,QAAQ,EAAEpG,UAAU,CAACjB,UAAU,EAAE7M,YAAY,EAAE8N,UAAU,CAACvP,MAAM,CAAC;IACxF;IACA,MAAM6U,UAAU,GAAGra,YAAY,CAAC,MAAM;MAClC+a,uBAAuB,CAAChb,OAAO,CAAC,CAAC;MACjCiG,QAAQ,CAAC6B,cAAc,CAACsT,QAAQ,EAAEpG,UAAU,CAACjB,UAAU,EAAE7M,YAAY,EAAE8N,UAAU,CAACvP,MAAM,CAAC;MACzFQ,QAAQ,CAAC+B,eAAe,CAACd,YAAY,CAAC;MACtCwT,aAAa,CAAC/R,MAAM,CAAC,CAAC;IAC1B,CAAC,CAAC;IACF,OAAO;MAAE3H,OAAO,EAAE0Z,aAAa;MAAEJ;IAAW,CAAC;EACjD;EACAW,0BAA0BA,CAACzT,SAAS,EAAExG,OAAO,EAAEqZ,WAAW,EAAEI,gBAAgB,EAAE;IAC1E,IAAI,CAAC,IAAI,CAACrW,qBAAqB,EAAE;MAC7B,OAAOtE,UAAU,CAACwB,IAAI;IAC1B;IACA,IAAI,IAAI,CAAC8C,qBAAqB,CAACC,UAAU,EAAE;MACvCmD,SAAS,CAACc,YAAY,CAAC,cAAc,EAAEC,MAAM,CAAC,IAAI,CAACnE,qBAAqB,CAACC,UAAU,CAACrD,OAAO,EAAEqZ,WAAW,EAAEI,gBAAgB,CAAC,CAAC,CAAC;IACjI;IACA,IAAI,IAAI,CAACrW,qBAAqB,CAACG,WAAW,EAAE;MACxCiD,SAAS,CAACc,YAAY,CAAC,eAAe,EAAEC,MAAM,CAAC,IAAI,CAACnE,qBAAqB,CAACG,WAAW,CAACvD,OAAO,EAAEqZ,WAAW,CAAC,CAAC,CAAC;IACjH;IACA,IAAI,IAAI,CAACjW,qBAAqB,CAACM,OAAO,EAAE;MACpC8C,SAAS,CAACc,YAAY,CAAC,MAAM,EAAE,IAAI,CAAClE,qBAAqB,CAACM,OAAO,CAAC1D,OAAO,CAAC,IAAI,UAAU,CAAC;IAC7F;IACA,MAAMsa,SAAS,GAAG,IAAI,CAAClX,qBAAqB,CAACO,YAAY,CAAC3D,OAAO,CAAC;IAClE,MAAMua,UAAU,GAAID,SAAS,IAAI,OAAOA,SAAS,KAAK,QAAQ,GAAIA,SAAS,GAAG9a,eAAe,CAAC8a,SAAS,CAAC;IACxG,MAAMnZ,MAAM,GAAG5B,OAAO,CAACib,MAAM,IAAI;MAC7B,MAAMC,KAAK,GAAGD,MAAM,CAACE,cAAc,CAACH,UAAU,CAAC;MAC/C,IAAIE,KAAK,EAAE;QACPjU,SAAS,CAACc,YAAY,CAAC,YAAY,EAAEmT,KAAK,CAAC;MAC/C,CAAC,MACI;QACDjU,SAAS,CAACiB,eAAe,CAAC,YAAY,CAAC;MAC3C;IACJ,CAAC,CAAC;IACF,IAAI,OAAO6S,SAAS,KAAK,QAAQ,EAAE,CACnC,CAAC,MACI,IAAIA,SAAS,EAAE;MAChB9T,SAAS,CAACc,YAAY,CAAC,YAAY,EAAEgT,SAAS,CAACrT,GAAG,CAAC,CAAC,CAAC;IACzD;IACA,MAAM0T,SAAS,GAAG,IAAI,CAACvX,qBAAqB,CAACU,YAAY,IAAI,IAAI,CAACV,qBAAqB,CAACU,YAAY,CAAC9D,OAAO,CAAC;IAC7G,IAAI,OAAO2a,SAAS,KAAK,QAAQ,EAAE;MAC/BnU,SAAS,CAACc,YAAY,CAAC,YAAY,EAAE,GAAGqT,SAAS,EAAE,CAAC;IACxD;IACA;IACAnU,SAAS,CAACc,YAAY,CAAC,eAAe,EAAEC,MAAM,CAAC,KAAK,CAAC,CAAC;IACtD,OAAOpG,MAAM;EACjB;EACAiY,UAAUA,CAACwB,OAAO,EAAE;IAChB,IAAI,CAAChC,YAAY,CAAClR,SAAS,CAACO,MAAM,CAAC,OAAO,EAAE,CAAC2S,OAAO,CAAC;IACrD,IAAI,CAACA,OAAO,EAAE;MACV,IAAI,CAAC9B,iBAAiB,CAACU,cAAc,CAAC,EAAE,EAAE5Y,SAAS,CAAC;IACxD;EACJ;EACA+N,QAAQA,CAAA,EAAG;IACP,IAAI,CAACmK,iBAAiB,CAACnK,QAAQ,CAAC,CAAC;EACrC;EACA4J,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACO,iBAAiB,CAACP,WAAW,CAAC,CAAC;EAC/C;EACAvZ,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC8Z,iBAAiB,CAAC9Z,OAAO,CAAC,CAAC;IAChC,IAAI,CAAC2Z,yBAAyB,CAAC3Z,OAAO,CAAC,CAAC;IACxC,IAAI,CAAC4Z,YAAY,CAACjR,MAAM,CAAC,CAAC;EAC9B;AACJ;AACA,MAAMoR,iBAAiB,SAASja,UAAU,CAAC;EACvC,IAAI+b,WAAWA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACC,YAAY;EAAE;EAC9C,IAAID,WAAWA,CAACE,QAAQ,EAAE;IACtB,IAAIA,QAAQ,KAAK,IAAI,CAACD,YAAY,EAAE;MAChC,IAAI,CAACE,oBAAoB,CAACnK,IAAI,CAACkK,QAAQ,CAAC;MACxC,IAAI,CAACD,YAAY,GAAGC,QAAQ;IAChC;EACJ;EACApb,WAAWA,CAAC6G,SAAS,EAAEgL,IAAI,EAAE;IACzB,KAAK,CAAC,CAAC;IACP,IAAI,CAAChL,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACgL,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACyJ,YAAY,GAAG,CAAC,CAAC;IACtB,IAAI,CAACpb,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACmb,oBAAoB,GAAG,IAAIxc,OAAO,CAAC,CAAC;IACzC,IAAI,CAACqW,mBAAmB,GAAG,IAAI,CAACmG,oBAAoB,CAACzM,KAAK;IAC1D,IAAI,CAAC2M,cAAc,GAAG,IAAI1c,OAAO,CAAC,CAAC;IACnC,IAAI,CAACsW,aAAa,GAAG,IAAI,CAACoG,cAAc,CAAC3M,KAAK;IAC9C,IAAI,CAACuM,YAAY,GAAG,KAAK;IACzB,IAAI,CAACzN,SAAS,CAAC5Q,qBAAqB,CAAC,IAAI,CAAC+J,SAAS,EAAE,OAAO,EAAE,MAAM,IAAI,CAAC2U,OAAO,CAAC,CAAC,CAAC,CAAC;IACpF,IAAI,CAAC9N,SAAS,CAAC5Q,qBAAqB,CAAC,IAAI,CAAC+J,SAAS,EAAE,MAAM,EAAE,MAAM,IAAI,CAAC4U,MAAM,CAAC,CAAC,CAAC,CAAC;IAClF,IAAI,CAAC/N,SAAS,CAAC,IAAI,CAACmE,IAAI,CAAC6J,UAAU,CAAC,MAAM,IAAI,CAACC,yBAAyB,CAAC,KAAK,CAAC,CAAC,CAAC;IACjF,IAAI,CAACjO,SAAS,CAAC,IAAI,CAACmE,IAAI,CAACnD,SAAS,CAAEnL,CAAC,IAAK,IAAI,CAACmL,SAAS,CAACnL,CAAC,CAAC,CAAC,CAAC;IAC7D,IAAI,CAACmK,SAAS,CAAC,IAAI,CAACmE,IAAI,CAAC+J,WAAW,CAAErY,CAAC,IAAK,IAAI,CAACqY,WAAW,CAACrY,CAAC,CAAC,CAAC,CAAC;IACjE,IAAI,CAACmK,SAAS,CAAC,IAAI,CAACmE,IAAI,CAACsD,aAAa,CAAE5R,CAAC,IAAK,IAAI,CAACsY,iBAAiB,CAACtY,CAAC,CAAC,CAAC,CAAC;EAC7E;EACAsY,iBAAiBA,CAACtY,CAAC,EAAE;IACjB,MAAMuY,MAAM,GAAGvY,CAAC,CAACwY,YAAY,CAACD,MAAM;IACpC,IAAI,CAACpe,uBAAuB,CAACoe,MAAM,CAAC,IAAI,CAACne,qBAAqB,CAACme,MAAM,CAAC,EAAE;MACpE,IAAI,IAAI,CAAClD,WAAW,CAAC,CAAC,EAAE;QACpB,IAAI,CAAC/G,IAAI,CAAC7C,QAAQ,CAAC,CAAC;MACxB;MACA;IACJ;IACA;IACA;IACA,IAAI,CAACnS,eAAe,CAAC0G,CAAC,CAACwY,YAAY,CAAC,EAAE;MAClC,IAAI,CAAC,IAAI,CAACrI,KAAK,EAAE;QACb,MAAM,IAAI2E,KAAK,CAAC,8DAA8D,CAAC;MACnF;MACA,MAAMqB,WAAW,GAAG,IAAI,CAAChG,KAAK,CAACH,WAAW,CAACyI,SAAS,CAAC3H,UAAU,IAAIA,UAAU,CAACjU,IAAI,CAACC,OAAO,KAAKkD,CAAC,CAAClD,OAAO,EAAEA,OAAO,CAAC;MAClH,IAAIqZ,WAAW,KAAK,CAAC,CAAC,EAAE;QACpB,MAAM,IAAIrB,KAAK,CAAC,kFAAkF,CAAC;MACvG;MACA,IAAI,CAACxR,SAAS,CAACoV,KAAK,CAAC,CAAC;MACtB,IAAI,CAACC,QAAQ,CAACxC,WAAW,CAAC;MAC1B;IACJ;IACA,IAAI,CAAC,IAAI,CAAChG,KAAK,IAAI,IAAI,CAAC4H,YAAY,GAAG,CAAC,EAAE;MACtC,MAAM,IAAIjD,KAAK,CAAC,oFAAoF,CAAC;IACzG;IACA,MAAMhE,UAAU,GAAG,IAAI,CAACX,KAAK,CAACH,WAAW,CAAC,IAAI,CAAC+H,YAAY,CAAC;IAC5D,MAAMjb,OAAO,GAAGgU,UAAU,CAACjU,IAAI,CAACC,OAAO;IACvC,MAAM8b,MAAM,GAAG,IAAI,CAACjc,QAAQ,CAAC,IAAI,CAACob,YAAY,CAAC;IAC/C,IAAI,CAACC,cAAc,CAACrK,IAAI,CAAC;MAAE7Q,OAAO;MAAE8b,MAAM;MAAEJ,YAAY,EAAExY,CAAC,CAACwY,YAAY;MAAEK,cAAc,EAAE;IAAK,CAAC,CAAC;EACrG;EACA1N,SAASA,CAACnL,CAAC,EAAE;IACT;IACA,IAAI,IAAI,CAAC2X,WAAW,IAAI,IAAI,CAACxH,KAAK,EAAE;MAChC;MACA,IAAInQ,CAAC,CAAC8Y,GAAG,KAAK,SAAS,EAAE;QACrB,IAAI,CAACC,iBAAiB,CAAC5L,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC2K,YAAY,GAAG,CAAC,CAAC,CAAC;QAC1D/X,CAAC,CAACsL,cAAc,CAAC,CAAC;QAClBtL,CAAC,CAACuL,eAAe,CAAC,CAAC;MACvB;MACA;MAAA,KACK,IAAIvL,CAAC,CAAC8Y,GAAG,KAAK,WAAW,IAAI9Y,CAAC,CAAC8Y,GAAG,KAAK,YAAY,EAAE;QACtD,IAAI,IAAI,CAACf,YAAY,IAAI,IAAI,CAAC5H,KAAK,CAACF,KAAK,GAAG,CAAC,EAAE;UAC3C,MAAM+I,gBAAgB,GAAG,IAAI,CAAC7I,KAAK,CAACH,WAAW,CAAC,IAAI,CAACG,KAAK,CAACF,KAAK,GAAG,CAAC,CAAC,CAACJ,UAAU,GAAG,CAAC;UACpF,IAAI,CAACvB,IAAI,CAAC7C,QAAQ,CAAC,CAAC;UACpB,IAAI,CAAC6C,IAAI,CAACqK,QAAQ,CAAC,CAACK,gBAAgB,CAAC,CAAC;UACtC,IAAI,CAACC,qBAAqB,CAACD,gBAAgB,EAAE,IAAI,CAAC7I,KAAK,CAAC;QAC5D,CAAC,MACI;UACD,IAAI,CAAC4I,iBAAiB,CAAC,IAAI,CAAChB,YAAY,GAAG,CAAC,CAAC;QACjD;QACA/X,CAAC,CAACsL,cAAc,CAAC,CAAC;QAClBtL,CAAC,CAACuL,eAAe,CAAC,CAAC;MACvB;IACJ;EACJ;EACA8M,WAAWA,CAACrY,CAAC,EAAE;IACX,MAAMuY,MAAM,GAAGvY,CAAC,CAACwY,YAAY,CAACD,MAAM;IACpC,IAAI,CAACpe,uBAAuB,CAACoe,MAAM,CAAC,IAAI,CAACne,qBAAqB,CAACme,MAAM,CAAC,EAAE;MACpE;IACJ;IACAvY,CAAC,CAACwY,YAAY,CAAClN,cAAc,CAAC,CAAC;IAC/BtL,CAAC,CAACwY,YAAY,CAACjN,eAAe,CAAC,CAAC;EACpC;EACA+K,cAAcA,CAAC3Z,QAAQ,EAAEwT,KAAK,EAAE;IAC5B,IAAIA,KAAK,IAAIA,KAAK,CAACF,KAAK,KAAK,CAAC,EAAE;MAC5B,MAAM,IAAI6E,KAAK,CAAC,sEAAsE,CAAC;IAC3F;IACA,IAAI3E,KAAK,IAAIA,KAAK,CAACF,KAAK,KAAKtT,QAAQ,CAAC0C,MAAM,EAAE;MAC1C,MAAM,IAAIyV,KAAK,CAAC,4CAA4C,CAAC;IACjE;IACA,MAAMoE,aAAa,GAAG,IAAI,CAACnB,YAAY;IACvC,IAAI,CAACoB,WAAW,CAAC,CAAC;IAClB,IAAI,CAACxc,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACwT,KAAK,GAAGA,KAAK;IAClB,IAAIA,KAAK,EAAE;MACP,MAAMiJ,eAAe,GAAGpd,KAAK,CAACkd,aAAa,EAAE,CAAC,EAAE/I,KAAK,CAACF,KAAK,GAAG,CAAC,CAAC;MAChE,IAAI,CAAC0I,QAAQ,CAACS,eAAe,CAAC;IAClC,CAAC,MACI;MACD,IAAI,IAAI,CAACzB,WAAW,EAAE;QAClB,IAAI,CAACrJ,IAAI,CAAC7C,QAAQ,CAAC,CAAC;MACxB;IACJ;IACA;IACA,IAAI,CAACnI,SAAS,CAACwG,QAAQ,GAAGqG,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;EAC5C;EACA4I,iBAAiBA,CAAC5C,WAAW,EAAE;IAC3B;IACA,MAAMhG,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,IAAI,CAACA,KAAK,EAAE;MACR,MAAM,IAAI2E,KAAK,CAAC,0CAA0C,CAAC;IAC/D;IACA,IAAI,CAAC6D,QAAQ,CAACxC,WAAW,CAAC;IAC1B,IAAIA,WAAW,GAAGhG,KAAK,CAACF,KAAK,GAAG,CAAC,EAAE;MAC/B;IACJ;IACA;IACA,IAAIE,KAAK,CAACC,wBAAwB,CAAC,CAAC,EAAE;MAClC,MAAMC,cAAc,GAAGF,KAAK,CAACH,WAAW,CAACmG,WAAW,CAAC;MACrD,IAAI,CAAC8C,qBAAqB,CAAC5I,cAAc,CAACP,QAAQ,GAAG,CAAC,EAAEK,KAAK,CAAC;IAClE;EACJ;EACA8I,qBAAqBA,CAAC9F,SAAS,EAAEhD,KAAK,EAAE;IACpC,MAAME,cAAc,GAAGF,KAAK,CAACH,WAAW,CAACG,KAAK,CAACF,KAAK,GAAG,CAAC,CAAC;IACzD,MAAMK,oBAAoB,GAAGH,KAAK,CAACF,KAAK,GAAG,CAAC,GAAGE,KAAK,CAACH,WAAW,CAACG,KAAK,CAACF,KAAK,GAAG,CAAC,CAAC,GAAGvS,SAAS;IAC7F,MAAM2b,gBAAgB,GAAG,IAAI,CAAC/K,IAAI,CAACgF,aAAa,CAACH,SAAS,CAAC;IAC3D,MAAMmG,oBAAoB,GAAGhJ,oBAAoB,GAAGA,oBAAoB,CAACX,QAAQ,GAAGW,oBAAoB,CAAC/O,MAAM,GAAG8O,cAAc,CAAC9O,MAAM,GAAG8O,cAAc,CAAC9O,MAAM;IAC/J,IAAI,CAAC+M,IAAI,CAAC4D,SAAS,GAAGmH,gBAAgB,GAAGC,oBAAoB;EACjE;EACA7N,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC,IAAI,CAAC0E,KAAK,EAAE;MACb,MAAM,IAAI2E,KAAK,CAAC,sCAAsC,CAAC;IAC3D;IACA,IAAI,CAACxR,SAAS,CAACoV,KAAK,CAAC,CAAC;EAC1B;EACArD,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC,IAAI,CAAClF,KAAK,EAAE;MACb,OAAO,KAAK;IAChB;IACA,OAAO,IAAI,CAAC7B,IAAI,CAACiL,cAAc,CAAC,CAAC,CAAC/U,SAAS,CAACgV,QAAQ,CAAC,uBAAuB,CAAC;EACjF;EACAL,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACpB,YAAY,KAAK,CAAC,CAAC,EAAE;MAC1B;IACJ;IACA,IAAI,CAAC0B,kBAAkB,CAAC,IAAI,CAAC9c,QAAQ,CAAC,IAAI,CAACob,YAAY,CAAC,EAAE,KAAK,CAAC;IAChE,IAAI,CAACA,YAAY,GAAG,CAAC,CAAC;EAC1B;EACAY,QAAQA,CAACe,aAAa,EAAE;IACpB,IAAI,CAAC,GAAGA,aAAa,EAAE;MACnB,MAAM,IAAI5E,KAAK,CAAC,iCAAiC,CAAC;IACtD;IACA,IAAI,CAAC,IAAI,CAAC3E,KAAK,IAAIuJ,aAAa,IAAI,CAAC,EAAE;MACnC,MAAM,IAAI5E,KAAK,CAAC,gDAAgD,CAAC;IACrE;IACA,IAAI,IAAI,CAAC3E,KAAK,IAAIuJ,aAAa,IAAI,IAAI,CAACvJ,KAAK,CAACF,KAAK,EAAE;MACjD,MAAM,IAAI6E,KAAK,CAAC,wDAAwD,CAAC;IAC7E;IACA,MAAM6E,QAAQ,GAAG,IAAI,CAAC5B,YAAY;IAClC,IAAI4B,QAAQ,IAAI,CAAC,EAAE;MACf,IAAI,CAACF,kBAAkB,CAAC,IAAI,CAAC9c,QAAQ,CAACgd,QAAQ,CAAC,EAAE,KAAK,CAAC;IAC3D;IACA,IAAID,aAAa,IAAI,CAAC,EAAE;MACpB,IAAI,CAACD,kBAAkB,CAAC,IAAI,CAAC9c,QAAQ,CAAC+c,aAAa,CAAC,EAAE,IAAI,CAAC;IAC/D;IACA,IAAI,CAAC3B,YAAY,GAAG2B,aAAa;EACrC;EACAD,kBAAkBA,CAAC3c,OAAO,EAAE8c,OAAO,EAAE;IACjC,IAAI,CAACC,wBAAwB,CAAC/c,OAAO,EAAE8c,OAAO,IAAI,IAAI,CAACjC,WAAW,CAAC;IACnE,IAAI,CAACmC,yBAAyB,CAAChd,OAAO,EAAE8c,OAAO,CAAC;EACpD;EACAG,+BAA+BA,CAACH,OAAO,EAAE;IACrC,IAAI,IAAI,CAAC7B,YAAY,KAAK,CAAC,CAAC,EAAE;MAC1B;IACJ;IACA,IAAI,CAAC8B,wBAAwB,CAAC,IAAI,CAACld,QAAQ,CAAC,IAAI,CAACob,YAAY,CAAC,EAAE6B,OAAO,CAAC;EAC5E;EACAC,wBAAwBA,CAAC/c,OAAO,EAAE8c,OAAO,EAAE;IACvC;IACA9c,OAAO,CAAC0H,SAAS,CAACO,MAAM,CAAC,SAAS,EAAE6U,OAAO,CAAC;EAChD;EACAE,yBAAyBA,CAAChd,OAAO,EAAE8c,OAAO,EAAE;IACxC;IACA;IACA9c,OAAO,CAAC0H,SAAS,CAACO,MAAM,CAAC,iBAAiB,EAAE6U,OAAO,CAAC;EACxD;EACAxB,yBAAyBA,CAACwB,OAAO,EAAE;IAC/B;IACA;IACA,IAAI,CAACtL,IAAI,CAACiL,cAAc,CAAC,CAAC,CAAC/U,SAAS,CAACO,MAAM,CAAC,uBAAuB,EAAE6U,OAAO,CAAC;EACjF;EACA3B,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC,IAAI,CAAC9H,KAAK,IAAI,IAAI,CAACxT,QAAQ,CAAC0C,MAAM,KAAK,CAAC,EAAE;MAC3C,MAAM,IAAIyV,KAAK,CAAC,4DAA4D,CAAC;IACjF;IACA,IAAI,CAAC6C,WAAW,GAAG,IAAI;IACvB,IAAI,CAACS,yBAAyB,CAAC,IAAI,CAAC;IACpC,IAAI,CAAC2B,+BAA+B,CAAC,IAAI,CAAC;IAC1C,IAAI,IAAI,CAAChC,YAAY,KAAK,CAAC,CAAC,EAAE;MAC1B,IAAI,CAACY,QAAQ,CAAC,CAAC,CAAC;IACpB;EACJ;EACAT,MAAMA,CAAA,EAAG;IACL,IAAI,CAACP,WAAW,GAAG,KAAK;IACxB,IAAI,CAACoC,+BAA+B,CAAC,KAAK,CAAC;EAC/C;EACAje,OAAOA,CAAA,EAAG;IACN,IAAI,CAACsc,yBAAyB,CAAC,KAAK,CAAC;IACrC,IAAI,CAACN,oBAAoB,CAACnK,IAAI,CAAC,KAAK,CAAC;IACrC,KAAK,CAAC7R,OAAO,CAAC,CAAC;EACnB;AACJ;AACA,SAASke,gBAAgBA,CAAC3O,KAAK,EAAE;EAC7B,IAAIkN,MAAM,GAAG5d,oBAAoB,CAACsf,OAAO;EACzC,IAAI7gB,kBAAkB,CAACiS,KAAK,CAACmN,YAAY,CAACD,MAAM,EAAE,mBAAmB,EAAE,eAAe,CAAC,EAAE;IACrFA,MAAM,GAAG5d,oBAAoB,CAACuf,OAAO;EACzC,CAAC,MACI,IAAI9gB,kBAAkB,CAACiS,KAAK,CAACmN,YAAY,CAACD,MAAM,EAAE,oBAAoB,EAAE,eAAe,CAAC,EAAE;IAC3FA,MAAM,GAAG5d,oBAAoB,CAACwf,OAAO;EACzC,CAAC,MACI,IAAI/gB,kBAAkB,CAACiS,KAAK,CAACmN,YAAY,CAACD,MAAM,EAAE,yBAAyB,EAAE,aAAa,CAAC,EAAE;IAC9FA,MAAM,GAAG5d,oBAAoB,CAACoN,MAAM;EACxC;EACA,OAAO;IACHyQ,YAAY,EAAEnN,KAAK,CAACmN,YAAY;IAChC1b,OAAO,EAAEuO,KAAK,CAACvO,OAAO,GAAGuO,KAAK,CAACvO,OAAO,CAACA,OAAO,GAAG,IAAI;IACrDyb;EACJ,CAAC;AACL;AACA,SAAS6B,sBAAsBA,CAAC/O,KAAK,EAAE;EACnC,MAAMwN,cAAc,GAAG1e,uBAAuB,CAACkR,KAAK,CAACmN,YAAY,CAACD,MAAM,CAAC;EACzE,OAAO;IACHzb,OAAO,EAAEuO,KAAK,CAACvO,OAAO,GAAGuO,KAAK,CAACvO,OAAO,CAACA,OAAO,GAAG,IAAI;IACrD0b,YAAY,EAAEnN,KAAK,CAACmN,YAAY;IAChCI,MAAM,EAAEvN,KAAK,CAACuN,MAAM;IACpBC;EACJ,CAAC;AACL;AACA,SAASwB,GAAGA,CAACxd,IAAI,EAAEyd,EAAE,EAAE;EACnBA,EAAE,CAACzd,IAAI,CAAC;EACRA,IAAI,CAAC2I,QAAQ,CAAC5D,OAAO,CAAC2Y,KAAK,IAAIF,GAAG,CAACE,KAAK,EAAED,EAAE,CAAC,CAAC;AAClD;AACA;AACA;AACA;AACA;AACA,MAAME,KAAK,CAAC;EACR,IAAIC,OAAOA,CAAA,EAAG;IACV,IAAI,CAAC,IAAI,CAACC,QAAQ,EAAE;MAChB,IAAI,CAACA,QAAQ,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC;IACxC;IACA,OAAO,IAAI,CAACD,QAAQ;EACxB;EACAje,WAAWA,CAACme,4BAA4B,EAAEjb,gBAAgB,EAAE;IACxD,IAAI,CAACib,4BAA4B,GAAGA,4BAA4B;IAChE,IAAI,CAACjb,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACnC,KAAK,GAAG,EAAE;IACf,IAAI,CAACqd,YAAY,GAAG,IAAIvf,OAAO,CAAC,CAAC;IACjC,IAAI,CAACqG,WAAW,GAAG,IAAI,CAACkZ,YAAY,CAACxP,KAAK;EAC9C;EACA1H,GAAGA,CAACnG,KAAK,EAAEgb,YAAY,EAAE;IACrB,IAAI,CAACA,YAAY,EAAEsC,YAAY,IAAIhgB,MAAM,CAAC,IAAI,CAAC0C,KAAK,EAAEA,KAAK,CAAC,EAAE;MAC1D;IACJ;IACA,IAAI,CAACud,IAAI,CAACvd,KAAK,EAAE,KAAK,EAAEgb,YAAY,CAAC;EACzC;EACAuC,IAAIA,CAACvd,KAAK,EAAEwd,MAAM,EAAExC,YAAY,EAAE;IAC9B,IAAI,CAAChb,KAAK,GAAG,CAAC,GAAGA,KAAK,CAAC;IACvB,IAAI,CAACb,QAAQ,GAAGe,SAAS;IACzB,IAAI,CAACgd,QAAQ,GAAGhd,SAAS;IACzB,IAAI,CAACsd,MAAM,EAAE;MACT,MAAMC,IAAI,GAAG,IAAI;MACjB,IAAI,CAACJ,YAAY,CAAClN,IAAI,CAAC;QAAE,IAAIhR,QAAQA,CAAA,EAAG;UAAE,OAAOse,IAAI,CAAClX,GAAG,CAAC,CAAC;QAAE,CAAC;QAAEyU;MAAa,CAAC,CAAC;IACnF;EACJ;EACAzU,GAAGA,CAAA,EAAG;IACF,IAAI,CAAC,IAAI,CAACpH,QAAQ,EAAE;MAChB,IAAI,CAACA,QAAQ,GAAG,IAAI,CAACa,KAAK,CAACZ,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACC,OAAO,CAAC;IACxD;IACA,OAAO,CAAC,GAAG,IAAI,CAACH,QAAQ,CAAC;EAC7B;EACAue,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAAC1d,KAAK;EACrB;EACA2H,GAAGA,CAACtI,IAAI,EAAE;IACN,OAAO,IAAI,CAAC4d,OAAO,CAACtV,GAAG,CAACtI,IAAI,CAAC;EACjC;EACAse,gBAAgBA,CAAC;IAAEC,aAAa;IAAEC;EAAa,CAAC,EAAE;IAC9C,IAAI,CAAC,IAAI,CAAC1b,gBAAgB,EAAE;MACxB,MAAMgE,GAAG,GAAG,IAAI,CAACgX,aAAa,CAAC,CAAC;MAChC,MAAMW,KAAK,GAAIze,IAAI,IAAK8G,GAAG,CAACE,MAAM,CAAChH,IAAI,CAAC;MACxCwe,YAAY,CAACzZ,OAAO,CAAC/E,IAAI,IAAIwd,GAAG,CAACxd,IAAI,EAAEye,KAAK,CAAC,CAAC;MAC9C,IAAI,CAAC3X,GAAG,CAAC,CAAC,GAAGA,GAAG,CAAC4X,MAAM,CAAC,CAAC,CAAC,CAAC;MAC3B;IACJ;IACA,MAAMC,iBAAiB,GAAG,IAAI9Y,GAAG,CAAC,CAAC;IACnC,MAAM+Y,mBAAmB,GAAI5e,IAAI,IAAK2e,iBAAiB,CAAC1W,GAAG,CAAC,IAAI,CAACnF,gBAAgB,CAACC,KAAK,CAAC/C,IAAI,CAACC,OAAO,CAAC,CAACkK,QAAQ,CAAC,CAAC,CAAC;IACjHqU,YAAY,CAACzZ,OAAO,CAAC/E,IAAI,IAAIwd,GAAG,CAACxd,IAAI,EAAE4e,mBAAmB,CAAC,CAAC;IAC5D,MAAMC,gBAAgB,GAAG,IAAItZ,GAAG,CAAC,CAAC;IAClC,MAAMuZ,oBAAoB,GAAI9e,IAAI,IAAK6e,gBAAgB,CAAC/X,GAAG,CAAC,IAAI,CAAChE,gBAAgB,CAACC,KAAK,CAAC/C,IAAI,CAACC,OAAO,CAAC,CAACkK,QAAQ,CAAC,CAAC,EAAEnK,IAAI,CAAC;IACvHue,aAAa,CAACxZ,OAAO,CAAC/E,IAAI,IAAIwd,GAAG,CAACxd,IAAI,EAAE8e,oBAAoB,CAAC,CAAC;IAC9D,MAAMne,KAAK,GAAG,EAAE;IAChB,KAAK,MAAMX,IAAI,IAAI,IAAI,CAACW,KAAK,EAAE;MAC3B,MAAMoe,EAAE,GAAG,IAAI,CAACjc,gBAAgB,CAACC,KAAK,CAAC/C,IAAI,CAACC,OAAO,CAAC,CAACkK,QAAQ,CAAC,CAAC;MAC/D,MAAM6U,UAAU,GAAGL,iBAAiB,CAACrW,GAAG,CAACyW,EAAE,CAAC;MAC5C,IAAI,CAACC,UAAU,EAAE;QACbre,KAAK,CAACkK,IAAI,CAAC7K,IAAI,CAAC;MACpB,CAAC,MACI;QACD,MAAMif,YAAY,GAAGJ,gBAAgB,CAAC3X,GAAG,CAAC6X,EAAE,CAAC;QAC7C,IAAIE,YAAY,IAAIA,YAAY,CAACpE,OAAO,EAAE;UACtCla,KAAK,CAACkK,IAAI,CAACoU,YAAY,CAAC;QAC5B;MACJ;IACJ;IACA,IAAI,IAAI,CAACte,KAAK,CAAC6B,MAAM,GAAG,CAAC,IAAI7B,KAAK,CAAC6B,MAAM,KAAK,CAAC,EAAE;MAC7C,MAAMxC,IAAI,GAAG,IAAI,CAAC+d,4BAA4B,CAAC,CAAC;MAChD,IAAI/d,IAAI,EAAE;QACNW,KAAK,CAACkK,IAAI,CAAC7K,IAAI,CAAC;MACpB;IACJ;IACA,IAAI,CAACke,IAAI,CAACvd,KAAK,EAAE,IAAI,CAAC;EAC1B;EACAmd,aAAaA,CAAA,EAAG;IACZ,MAAMhX,GAAG,GAAG,IAAIjB,GAAG,CAAC,CAAC;IACrB,KAAK,MAAM7F,IAAI,IAAI,IAAI,CAACW,KAAK,EAAE;MAC3BmG,GAAG,CAACmB,GAAG,CAACjI,IAAI,CAAC;IACjB;IACA,OAAO8G,GAAG;EACd;AACJ;AACA,MAAMoY,2BAA2B,SAASzhB,eAAe,CAAC;EACtDmC,WAAWA,CAACuf,IAAI,EAAEhW,IAAI,EAAEiW,oBAAoB,EAAE;IAC1C,KAAK,CAACD,IAAI,CAAC;IACX,IAAI,CAAChW,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACiW,oBAAoB,GAAGA,oBAAoB;EACpD;EACAC,aAAaA,CAAClc,CAAC,EAAE;IACb,IAAIjG,QAAQ,CAACiG,CAAC,CAACwY,YAAY,CAACD,MAAM,CAAC,IAC/Bve,cAAc,CAACgG,CAAC,CAACwY,YAAY,CAACD,MAAM,CAAC,IACrCre,cAAc,CAAC8F,CAAC,CAACwY,YAAY,CAACD,MAAM,CAAC,EAAE;MACvC;IACJ;IACA,IAAIvY,CAAC,CAACwY,YAAY,CAAC2D,eAAe,EAAE;MAChC;IACJ;IACA,MAAMtf,IAAI,GAAGmD,CAAC,CAAClD,OAAO;IACtB,IAAI,CAACD,IAAI,EAAE;MACP,OAAO,KAAK,CAACqf,aAAa,CAAClc,CAAC,CAAC;IACjC;IACA,IAAI,IAAI,CAACC,2BAA2B,CAACD,CAAC,CAAC,IAAI,IAAI,CAACD,4BAA4B,CAACC,CAAC,CAAC,EAAE;MAC7E,OAAO,KAAK,CAACkc,aAAa,CAAClc,CAAC,CAAC;IACjC;IACA,MAAMuY,MAAM,GAAGvY,CAAC,CAACwY,YAAY,CAACD,MAAM;IACpC,MAAM6D,SAAS,GAAG7D,MAAM,CAAC/T,SAAS,CAACgV,QAAQ,CAAC,mBAAmB,CAAC,IACxDjB,MAAM,CAAC/T,SAAS,CAACgV,QAAQ,CAAC,mBAAmB,CAAC,IAAIjB,MAAM,CAAC/T,SAAS,CAACgV,QAAQ,CAAC,aAAa,CAAC,IAAIxZ,CAAC,CAACwY,YAAY,CAAC6D,OAAO,GAAG,EAAG;IAClI,MAAMC,eAAe,GAAGliB,qBAAqB,CAAC4F,CAAC,CAACwY,YAAY,CAACD,MAAM,CAAC;IACpE,IAAIgE,wBAAwB,GAAG,KAAK;IACpC,IAAID,eAAe,EAAE;MACjBC,wBAAwB,GAAG,IAAI;IACnC,CAAC,MACI,IAAI,OAAO,IAAI,CAACvW,IAAI,CAACuW,wBAAwB,KAAK,UAAU,EAAE;MAC/DA,wBAAwB,GAAG,IAAI,CAACvW,IAAI,CAACuW,wBAAwB,CAAC1f,IAAI,CAACC,OAAO,CAAC;IAC/E,CAAC,MACI;MACDyf,wBAAwB,GAAG,CAAC,CAAC,IAAI,CAACvW,IAAI,CAACuW,wBAAwB;IACnE;IACA,IAAI,CAACD,eAAe,EAAE;MAClB,IAAIC,wBAAwB,IAAI,CAACH,SAAS,IAAIpc,CAAC,CAACwY,YAAY,CAACgE,MAAM,KAAK,CAAC,EAAE;QACvE,OAAO,KAAK,CAACN,aAAa,CAAClc,CAAC,CAAC;MACjC;MACA,IAAI,CAAC,IAAI,CAACgG,IAAI,CAACyW,mBAAmB,IAAIzc,CAAC,CAACwY,YAAY,CAACgE,MAAM,KAAK,CAAC,EAAE;QAC/D,OAAO,KAAK,CAACN,aAAa,CAAClc,CAAC,CAAC;MACjC;IACJ,CAAC,MACI;MACD,IAAI,CAAC0c,4BAA4B,CAAC1c,CAAC,EAAEnD,IAAI,CAAC;IAC9C;IACA,IAAIA,IAAI,CAACsH,WAAW,KAAK,CAACmY,eAAe,IAAIF,SAAS,CAAC,EAAE;MACrD,MAAMO,QAAQ,GAAG,IAAI,CAAC3W,IAAI,CAACzH,eAAe,CAAC1B,IAAI,CAAC;MAChD,MAAM+f,SAAS,GAAG5c,CAAC,CAACwY,YAAY,CAACqE,MAAM;MACvC,IAAI,CAAC7W,IAAI,CAAC2S,QAAQ,CAAC,CAACgE,QAAQ,CAAC,CAAC;MAC9B,IAAI,CAAC3W,IAAI,CAAC8W,eAAe,CAACH,QAAQ,EAAEC,SAAS,CAAC;MAC9C,IAAIR,SAAS,EAAE;QACX;QACApc,CAAC,CAACwY,YAAY,CAAC2D,eAAe,GAAG,IAAI;QACrC;MACJ;IACJ;IACA,IAAI,CAACG,eAAe,EAAE;MAClB,KAAK,CAACJ,aAAa,CAAClc,CAAC,CAAC;IAC1B;EACJ;EACA0c,4BAA4BA,CAAC1c,CAAC,EAAEnD,IAAI,EAAE;IAClC,IAAI5C,oBAAoB,CAAC+F,CAAC,CAACwY,YAAY,CAACD,MAAM,CAAC,IAAIze,YAAY,CAACkG,CAAC,CAACwY,YAAY,CAACD,MAAM,CAAC,EAAE;MACpF;IACJ;IACA,MAAMwE,sBAAsB,GAAG,IAAI,CAACd,oBAAoB,CAAC,CAAC;IAC1D,IAAI,CAACc,sBAAsB,EAAE;MACzB,MAAM,IAAIjI,KAAK,CAAC,oCAAoC,CAAC;IACzD;IACA,MAAM3B,SAAS,GAAG,IAAI,CAAC6I,IAAI,CAAC1U,OAAO,CAACzK,IAAI,CAAC;IACzC,MAAMwc,gBAAgB,GAAG,IAAI,CAAC2C,IAAI,CAAC1I,aAAa,CAACH,SAAS,CAAC;IAC3D,MAAMmG,oBAAoB,GAAGyD,sBAAsB,CAAC7H,0BAA0B,CAACrY,IAAI,CAAC;IACpF,IAAI,CAACmJ,IAAI,CAACkM,SAAS,GAAGmH,gBAAgB,GAAGC,oBAAoB;IAC7D,IAAI,CAAC0C,IAAI,CAACvQ,QAAQ,CAAC,CAAC;IACpB,IAAI,CAACuQ,IAAI,CAACrD,QAAQ,CAAC,CAACxF,SAAS,CAAC,CAAC;IAC/B,IAAI,CAAC6I,IAAI,CAACgB,YAAY,CAAC,CAAC7J,SAAS,CAAC,CAAC;EACvC;EACA8J,aAAaA,CAACjd,CAAC,EAAE;IACb,MAAMoc,SAAS,GAAGpc,CAAC,CAACwY,YAAY,CAACD,MAAM,CAAC/T,SAAS,CAACgV,QAAQ,CAAC,mBAAmB,CAAC;IAC/E,IAAI4C,SAAS,IAAI,CAAC,IAAI,CAACpW,IAAI,CAACyW,mBAAmB,EAAE;MAC7C;IACJ;IACA,IAAIzc,CAAC,CAACwY,YAAY,CAAC2D,eAAe,EAAE;MAChC;IACJ;IACA,KAAK,CAACc,aAAa,CAACjd,CAAC,CAAC;EAC1B;EACA;EACAqY,WAAWA,CAACrY,CAAC,EAAE;IACX,MAAMuY,MAAM,GAAGvY,CAAC,CAACwY,YAAY,CAACD,MAAM;IACpC,IAAI,CAACpe,uBAAuB,CAACoe,MAAM,CAAC,IAAI,CAACne,qBAAqB,CAACme,MAAM,CAAC,EAAE;MACpE,KAAK,CAACF,WAAW,CAACrY,CAAC,CAAC;MACpB;IACJ;EACJ;EACA4R,aAAaA,CAAC5R,CAAC,EAAE;IACb,MAAMuY,MAAM,GAAGvY,CAAC,CAACwY,YAAY,CAACD,MAAM;IACpC,IAAI,CAACpe,uBAAuB,CAACoe,MAAM,CAAC,IAAI,CAACne,qBAAqB,CAACme,MAAM,CAAC,EAAE;MACpE,KAAK,CAAC3G,aAAa,CAAC5R,CAAC,CAAC;MACtB;IACJ;EACJ;AACJ;AACA;AACA;AACA;AACA;AACA,MAAMkd,YAAY,SAAS7iB,IAAI,CAAC;EAC5BoC,WAAWA,CAAC0gB,IAAI,EAAE7Z,SAAS,EAAE8Z,eAAe,EAAElM,SAAS,EAAEmM,UAAU,EAAEC,cAAc,EAAEC,WAAW,EAAE7d,OAAO,EAAE;IACvG,KAAK,CAACyd,IAAI,EAAE7Z,SAAS,EAAE8Z,eAAe,EAAElM,SAAS,EAAExR,OAAO,CAAC;IAC3D,IAAI,CAAC2d,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,WAAW,GAAGA,WAAW;EAClC;EACAC,qBAAqBA,CAAC9d,OAAO,EAAE;IAC3B,OAAO,IAAIqc,2BAA2B,CAAC,IAAI,EAAErc,OAAO,CAACsG,IAAI,EAAEtG,OAAO,CAACuc,oBAAoB,CAAC;EAC5F;EACAwB,MAAMA,CAACre,KAAK,EAAEse,WAAW,EAAE/gB,QAAQ,GAAG,EAAE,EAAE;IACtC,KAAK,CAAC8gB,MAAM,CAACre,KAAK,EAAEse,WAAW,EAAE/gB,QAAQ,CAAC;IAC1C,IAAIA,QAAQ,CAAC0C,MAAM,KAAK,CAAC,EAAE;MACvB;IACJ;IACA,MAAMse,eAAe,GAAG,EAAE;IAC1B,MAAMC,mBAAmB,GAAG,EAAE;IAC9B,IAAIhF,MAAM;IACVjc,QAAQ,CAACiF,OAAO,CAAC,CAAC/E,IAAI,EAAE6G,KAAK,KAAK;MAC9B,IAAI,IAAI,CAAC2Z,UAAU,CAAClY,GAAG,CAACtI,IAAI,CAAC,EAAE;QAC3B8gB,eAAe,CAACjW,IAAI,CAACtI,KAAK,GAAGsE,KAAK,CAAC;MACvC;MACA,IAAI,IAAI,CAAC4Z,cAAc,CAACnY,GAAG,CAACtI,IAAI,CAAC,EAAE;QAC/B+gB,mBAAmB,CAAClW,IAAI,CAACtI,KAAK,GAAGsE,KAAK,CAAC;MAC3C;MACA,IAAI,IAAI,CAAC6Z,WAAW,CAACpY,GAAG,CAACtI,IAAI,CAAC,EAAE;QAC5B+b,MAAM,GAAGxZ,KAAK,GAAGsE,KAAK;MAC1B;IACJ,CAAC,CAAC;IACF,IAAIia,eAAe,CAACte,MAAM,GAAG,CAAC,EAAE;MAC5B,KAAK,CAACsZ,QAAQ,CAAC9d,QAAQ,CAAC,CAAC,GAAG,KAAK,CAACgjB,QAAQ,CAAC,CAAC,EAAE,GAAGF,eAAe,CAAC,CAAC,CAAC;IACvE;IACA,IAAIC,mBAAmB,CAACve,MAAM,GAAG,CAAC,EAAE;MAChC,KAAK,CAAC2d,YAAY,CAACniB,QAAQ,CAAC,CAAC,GAAG,KAAK,CAACijB,YAAY,CAAC,CAAC,EAAE,GAAGF,mBAAmB,CAAC,CAAC,CAAC;IACnF;IACA,IAAI,OAAOhF,MAAM,KAAK,QAAQ,EAAE;MAC5B,KAAK,CAACmF,SAAS,CAACnF,MAAM,CAAC;IAC3B;EACJ;EACAD,QAAQA,CAACqF,OAAO,EAAExF,YAAY,EAAEyF,OAAO,GAAG,KAAK,EAAE;IAC7C,KAAK,CAACtF,QAAQ,CAACqF,OAAO,EAAExF,YAAY,CAAC;IACrC,IAAI,CAACyF,OAAO,EAAE;MACV,IAAI,CAACZ,UAAU,CAAC1Z,GAAG,CAACqa,OAAO,CAACphB,GAAG,CAAC6K,CAAC,IAAI,IAAI,CAAC3K,OAAO,CAAC2K,CAAC,CAAC,CAAC,EAAE+Q,YAAY,CAAC;IACxE;EACJ;EACAwE,YAAYA,CAACgB,OAAO,EAAExF,YAAY,EAAEyF,OAAO,GAAG,KAAK,EAAE;IACjD,KAAK,CAACjB,YAAY,CAACgB,OAAO,EAAExF,YAAY,CAAC;IACzC,IAAI,CAACyF,OAAO,EAAE;MACV,IAAI,CAACX,cAAc,CAAC3Z,GAAG,CAACqa,OAAO,CAACphB,GAAG,CAAC6K,CAAC,IAAI,IAAI,CAAC3K,OAAO,CAAC2K,CAAC,CAAC,CAAC,EAAE+Q,YAAY,CAAC;IAC5E;EACJ;EACAuF,SAASA,CAACra,KAAK,EAAEua,OAAO,GAAG,KAAK,EAAE;IAC9B,KAAK,CAACF,SAAS,CAACra,KAAK,CAAC;IACtB,IAAI,CAACua,OAAO,EAAE;MACV,IAAI,OAAOva,KAAK,KAAK,WAAW,EAAE;QAC9B,IAAI,CAAC6Z,WAAW,CAAC5Z,GAAG,CAAC,EAAE,CAAC;MAC5B,CAAC,MACI;QACD,IAAI,CAAC4Z,WAAW,CAAC5Z,GAAG,CAAC,CAAC,IAAI,CAAC7G,OAAO,CAAC4G,KAAK,CAAC,CAAC,CAAC;MAC/C;IACJ;EACJ;AACJ;AACA,OAAO,MAAMwa,YAAY,CAAC;EACtB,IAAIrM,WAAWA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACvD,IAAI,CAACuD,WAAW;EAAE;EAClD,IAAIsM,gBAAgBA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACC,aAAa,CAACC,SAAS,CAAC,IAAI,CAAC3F,KAAK,CAAC/W,WAAW,CAAC;EAAE;EACtF,IAAI2c,oBAAoBA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACF,aAAa,CAACC,SAAS,CAAC,IAAI,CAACE,SAAS,CAAC5c,WAAW,CAAC;EAAE;EAC9F,IAAI6c,eAAeA,CAAA,EAAG;IAAE,OAAOjjB,KAAK,CAAC+K,MAAM,CAAC/K,KAAK,CAACqB,GAAG,CAAC,IAAI,CAAC0R,IAAI,CAACkQ,eAAe,EAAExE,gBAAgB,CAAC,EAAEha,CAAC,IAAIA,CAAC,CAACuY,MAAM,KAAK5d,oBAAoB,CAACoN,MAAM,CAAC;EAAE;EACpJ,IAAI0W,WAAWA,CAAA,EAAG;IAAE,OAAOljB,KAAK,CAACqB,GAAG,CAAC,IAAI,CAAC0R,IAAI,CAACmQ,WAAW,EAAEzE,gBAAgB,CAAC;EAAE;EAC/E,IAAI0E,UAAUA,CAAA,EAAG;IAAE,OAAOnjB,KAAK,CAACqB,GAAG,CAAC,IAAI,CAAC0R,IAAI,CAACoQ,UAAU,EAAE1E,gBAAgB,CAAC;EAAE;EAC7E,IAAIpI,aAAaA,CAAA,EAAG;IAAE,OAAOrW,KAAK,CAACojB,GAAG,CAACpjB,KAAK,CAAC+K,MAAM,CAAC/K,KAAK,CAACqB,GAAG,CAAC,IAAI,CAAC0R,IAAI,CAACsD,aAAa,EAAEwI,sBAAsB,CAAC,EAAEpa,CAAC,IAAI,CAACA,CAAC,CAAC6Y,cAAc,CAAC,EAAE,IAAI,CAACkE,sBAAsB,EAAEnL,aAAa,IAAIrW,KAAK,CAAC6B,IAAI,CAAC;EAAE;EACpM,IAAIwhB,SAASA,CAAA,EAAG;IAAE,OAAOrjB,KAAK,CAACqB,GAAG,CAAC,IAAI,CAAC0R,IAAI,CAACsQ,SAAS,EAAE5E,gBAAgB,CAAC;EAAE;EAC3E,IAAI7O,SAASA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACmD,IAAI,CAACnD,SAAS;EAAE;EAC9C,IAAIgN,UAAUA,CAAA,EAAG;IAAE,OAAO,IAAI,CAAC7J,IAAI,CAAC6J,UAAU;EAAE;EAChD,IAAI0G,gBAAgBA,CAAA,EAAG;IAAE,OAAOtjB,KAAK,CAACujB,MAAM,CAAC,IAAI,CAACzgB,KAAK,CAACwQ,WAAW,CAAC;EAAE;EACtE,IAAI7M,wBAAwBA,CAAA,EAAG;IAAE,OAAO,IAAI,CAAC3D,KAAK,CAAC2D,wBAAwB;EAAE;EAC7E,IAAI6F,QAAQA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACkX,cAAc,EAAE3V,IAAI,IAAItB,YAAY,CAAC4C,SAAS;EAAE;EAC7E,IAAI7C,QAAQA,CAACA,QAAQ,EAAE;IAAE,IAAI,IAAI,CAACkX,cAAc,EAAE;MAC9C,IAAI,CAACA,cAAc,CAAC3V,IAAI,GAAGvB,QAAQ;IACvC;EAAE;EACF,IAAIX,aAAaA,CAAA,EAAG;IAAE,OAAO,IAAI,CAAC6X,cAAc,EAAErV,SAAS,IAAIvC,iBAAiB,CAACyC,KAAK;EAAE;EACxF,IAAI1C,aAAaA,CAAC8X,SAAS,EAAE;IAAE,IAAI,IAAI,CAACD,cAAc,EAAE;MACpD,IAAI,CAACA,cAAc,CAACrV,SAAS,GAAGsV,SAAS;IAC7C;EAAE;EACF,IAAIvC,mBAAmBA,CAAA,EAAG;IAAE,OAAO,OAAO,IAAI,CAACwC,QAAQ,CAACxC,mBAAmB,KAAK,WAAW,GAAG,IAAI,GAAG,IAAI,CAACwC,QAAQ,CAACxC,mBAAmB;EAAE;EACxI,IAAIF,wBAAwBA,CAAA,EAAG;IAAE,OAAO,OAAO,IAAI,CAAC0C,QAAQ,CAAC1C,wBAAwB,KAAK,WAAW,GAAG,IAAI,GAAG,IAAI,CAAC0C,QAAQ,CAAC1C,wBAAwB;EAAE;EACvJ,IAAI2C,YAAYA,CAAA,EAAG;IAAE,OAAO,IAAI,CAAC5Q,IAAI,CAAC4Q,YAAY;EAAE;EACpDziB,WAAWA,CAAC0iB,KAAK,EAAE7b,SAAS,EAAEpC,QAAQ,EAAEgQ,SAAS,EAAE+N,QAAQ,GAAG,CAAC,CAAC,EAAE;IAC9D,IAAI,CAACE,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACF,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACb,aAAa,GAAG,IAAI5iB,aAAa,CAAC,CAAC;IACxC,IAAI,CAAC4jB,wBAAwB,GAAG7jB,KAAK,CAAC6B,IAAI;IAC1C,IAAI,CAACiiB,8BAA8B,GAAG9jB,KAAK,CAAC6B,IAAI;IAChD,IAAI,CAACC,WAAW,GAAG,IAAIxB,eAAe,CAAC,CAAC;IACxC,IAAI,CAACyjB,eAAe,GAAG,IAAIhkB,OAAO,CAAC,CAAC;IACpC,IAAI,CAAC8K,cAAc,GAAG,IAAI,CAACkZ,eAAe,CAACjU,KAAK;IAChD,IAAI,CAACkU,mBAAmB,GAAG,IAAIjkB,OAAO,CAAC,CAAC;IACxC,IAAI,CAAC6V,YAAY,GAAG,IAAIlQ,oBAAoB,CAACC,QAAQ,CAAC;IACtD,MAAMse,6BAA6B,GAAG,IAAI/jB,KAAK,CAAC,CAAC;IACjD,MAAMgkB,sBAAsB,GAAG,IAAIhkB,KAAK,CAAC,CAAC;IAC1C,MAAMwG,WAAW,GAAG,IAAI,CAAC5E,WAAW,CAACyH,GAAG,CAAC,IAAIrD,eAAe,CAACge,sBAAsB,CAACpU,KAAK,CAAC,CAAC;IAC3F,MAAMnJ,oBAAoB,GAAG,IAAI7G,MAAM,CAAC,CAAC;IACzC,IAAI,CAAC6V,SAAS,GAAGA,SAAS,CAACtU,GAAG,CAAC8iB,CAAC,IAAI,IAAI7d,YAAY,CAAC6d,CAAC,EAAE,MAAM,IAAI,CAACrhB,KAAK,EAAEmhB,6BAA6B,CAACnU,KAAK,EAAEpJ,WAAW,EAAEC,oBAAoB,EAAE+c,QAAQ,CAAC,CAAC;IAC5J,KAAK,MAAMS,CAAC,IAAI,IAAI,CAACxO,SAAS,EAAE;MAC5B,IAAI,CAAC7T,WAAW,CAACyH,GAAG,CAAC4a,CAAC,CAAC;IAC3B;IACA,IAAIpZ,MAAM;IACV,IAAI2Y,QAAQ,CAACle,+BAA+B,EAAE;MAC1CuF,MAAM,GAAG,IAAIX,UAAU,CAAC,IAAI,EAAEsZ,QAAQ,CAACle,+BAA+B,EAAEke,QAAQ,CAAC3Y,MAAM,CAAC;MACxF2Y,QAAQ,GAAG;QAAE,GAAGA,QAAQ;QAAE3Y,MAAM,EAAEA;MAAO,CAAC,CAAC,CAAC;MAC5C,IAAI,CAACjJ,WAAW,CAACyH,GAAG,CAACwB,MAAM,CAAC;IAChC;IACA,IAAI,CAACoS,KAAK,GAAG,IAAI8B,KAAK,CAAC,MAAM,IAAI,CAAClM,IAAI,CAACqR,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEV,QAAQ,CAACtf,gBAAgB,CAAC;IAC1F,IAAI,CAAC4e,SAAS,GAAG,IAAI/D,KAAK,CAAC,MAAM,IAAI,CAAClM,IAAI,CAACsR,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEX,QAAQ,CAACtf,gBAAgB,CAAC;IAC/F,IAAI,CAACiZ,MAAM,GAAG,IAAI4B,KAAK,CAAC,MAAM,IAAI,CAAClM,IAAI,CAACuR,gBAAgB,CAAC,CAAC,EAAEZ,QAAQ,CAACtf,gBAAgB,CAAC;IACtF,IAAI,CAAC2O,IAAI,GAAG,IAAI4O,YAAY,CAACiC,KAAK,EAAE7b,SAAS,EAAE,IAAI,CAAC6N,YAAY,EAAE,IAAI,CAACD,SAAS,EAAE,IAAI,CAACwH,KAAK,EAAE,IAAI,CAAC6F,SAAS,EAAE,IAAI,CAAC3F,MAAM,EAAE;MAAE,GAAGnZ,aAAa,CAAC,MAAM,IAAI,CAACpB,KAAK,EAAE4gB,QAAQ,CAAC;MAAEjZ,IAAI,EAAE,IAAI;MAAEiW,oBAAoB,EAAEA,CAAA,KAAM,IAAI,CAACc;IAAuB,CAAC,CAAC;IACjP,IAAI,CAAC1e,KAAK,GAAG,IAAI,CAACyhB,WAAW,CAACX,KAAK,EAAE,IAAI,CAAC7Q,IAAI,EAAE2Q,QAAQ,CAAC;IACzDO,6BAA6B,CAACO,KAAK,GAAG,IAAI,CAAC1hB,KAAK,CAAC2D,wBAAwB;IACzE,MAAMmZ,gBAAgB,GAAG5f,KAAK,CAACqG,OAAO,CAAC,IAAI,CAACvD,KAAK,CAACwQ,WAAW,EAAE7O,CAAC,IAAI;MAChE,IAAI,CAACoe,aAAa,CAAC4B,YAAY,CAAC,MAAM;QAClC,IAAI,CAACtH,KAAK,CAACyC,gBAAgB,CAACnb,CAAC,CAAC;QAC9B,IAAI,CAACue,SAAS,CAACpD,gBAAgB,CAACnb,CAAC,CAAC;MACtC,CAAC,CAAC;IACN,CAAC,EAAE,IAAI,CAAC3C,WAAW,CAAC;IACpB;IACA8d,gBAAgB,CAAC,MAAM,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC9d,WAAW,CAAC;IACpD;IACA;IACA;IACA;IACA,MAAM4iB,kBAAkB,GAAG,IAAI,CAAC5iB,WAAW,CAACyH,GAAG,CAAC,IAAIxJ,OAAO,CAAC,CAAC,CAAC;IAC9D,MAAM4kB,mBAAmB,GAAG,IAAI,CAAC7iB,WAAW,CAACyH,GAAG,CAAC,IAAI9J,OAAO,CAAC,CAAC,CAAC,CAAC;IAChE,IAAI,CAACqC,WAAW,CAACyH,GAAG,CAACvJ,KAAK,CAACojB,GAAG,CAACxD,gBAAgB,EAAE,IAAI,CAACzC,KAAK,CAAC/W,WAAW,EAAE,IAAI,CAAC4c,SAAS,CAAC5c,WAAW,CAAC,CAAC,MAAM;MACvGue,mBAAmB,CAACC,OAAO,CAAC,MAAM;QAC9B,MAAMxc,GAAG,GAAG,IAAIjB,GAAG,CAAC,CAAC;QACrB,KAAK,MAAM7F,IAAI,IAAI,IAAI,CAAC6b,KAAK,CAACwC,QAAQ,CAAC,CAAC,EAAE;UACtCvX,GAAG,CAACmB,GAAG,CAACjI,IAAI,CAAC;QACjB;QACA,KAAK,MAAMA,IAAI,IAAI,IAAI,CAAC0hB,SAAS,CAACrD,QAAQ,CAAC,CAAC,EAAE;UAC1CvX,GAAG,CAACmB,GAAG,CAACjI,IAAI,CAAC;QACjB;QACAojB,kBAAkB,CAACtS,IAAI,CAAC,CAAC,GAAGhK,GAAG,CAAC4X,MAAM,CAAC,CAAC,CAAC,CAAC;MAC9C,CAAC,CAAC;IACN,CAAC,CAAC,CAAC;IACHkE,sBAAsB,CAACM,KAAK,GAAGE,kBAAkB,CAAC5U,KAAK;IACvD,IAAI4T,QAAQ,CAACmB,eAAe,KAAK,KAAK,EAAE;MACpC,MAAMjV,SAAS,GAAG5P,KAAK,CAAC6P,KAAK,CAAC,IAAI,CAACkD,IAAI,CAACnD,SAAS,EAAErS,CAAC,IAAIA,CAAC,CAACwN,MAAM,CAACtG,CAAC,IAAI,CAAChG,cAAc,CAACgG,CAAC,CAACuY,MAAM,CAAC,CAAC,CAC3F3b,GAAG,CAACoD,CAAC,IAAI,IAAIvG,qBAAqB,CAACuG,CAAC,CAAC,CAAC,CAAC;MAC5CzE,KAAK,CAAC6P,KAAK,CAACD,SAAS,EAAErS,CAAC,IAAIA,CAAC,CAACwN,MAAM,CAACtG,CAAC,IAAIA,CAAC,CAAC+M,OAAO,KAAK,EAAE,CAAC,uBAAuB,CAAC,CAAC,CAAC,IAAI,CAACsT,WAAW,EAAE,IAAI,EAAE,IAAI,CAAChjB,WAAW,CAAC;MAC9H9B,KAAK,CAAC6P,KAAK,CAACD,SAAS,EAAErS,CAAC,IAAIA,CAAC,CAACwN,MAAM,CAACtG,CAAC,IAAIA,CAAC,CAAC+M,OAAO,KAAK,EAAE,CAAC,wBAAwB,CAAC,CAAC,CAAC,IAAI,CAACuT,YAAY,EAAE,IAAI,EAAE,IAAI,CAACjjB,WAAW,CAAC;MAChI9B,KAAK,CAAC6P,KAAK,CAACD,SAAS,EAAErS,CAAC,IAAIA,CAAC,CAACwN,MAAM,CAACtG,CAAC,IAAIA,CAAC,CAAC+M,OAAO,KAAK,EAAE,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAACwT,OAAO,EAAE,IAAI,EAAE,IAAI,CAACljB,WAAW,CAAC;IAC1H;IACA,IAAI,CAAC4hB,QAAQ,CAACuB,iBAAiB,IAAI,IAAI,KAAKvB,QAAQ,CAACle,+BAA+B,IAAIke,QAAQ,CAACpV,mBAAmB,EAAE;MAClH,MAAM3B,IAAI,GAAG,IAAI,CAACxI,OAAO,CAAC+gB,gBAAgB,GAAG;QAAErW,MAAM,EAAE,IAAI,CAAC1K,OAAO,CAAC+gB;MAAiB,CAAC,GAAG/iB,SAAS;MAClG,IAAI,CAACqhB,cAAc,GAAG,IAAIlR,cAAc,CAAC,IAAI,EAAE,IAAI,CAACxP,KAAK,EAAE,IAAI,CAACiQ,IAAI,EAAEhI,MAAM,EAAE2Y,QAAQ,CAACpV,mBAAmB,EAAE3B,IAAI,CAAC;MACjH,IAAI,CAACwY,qBAAqB,GAAG7jB,IAAI,IAAI,IAAI,CAACkiB,cAAc,CAAC3P,gBAAgB,CAACvS,IAAI,CAAC;MAC/E,IAAI,CAACuiB,wBAAwB,GAAG,IAAI,CAACL,cAAc,CAACtQ,oBAAoB;MACxE,IAAI,CAACpR,WAAW,CAACyH,GAAG,CAAC,IAAI,CAACia,cAAc,CAAC;MACzC,IAAI,CAAC4B,mBAAmB,GAAG,IAAI,CAAC5B,cAAc,CAACvU,eAAe;MAC9D,IAAI,CAACoW,wBAAwB,GAAG,IAAI,CAAC7B,cAAc,CAACnU,oBAAoB;IAC5E,CAAC,MACI;MACD,IAAI,CAAC+V,mBAAmB,GAAGplB,KAAK,CAAC6B,IAAI;MACrC,IAAI,CAACwjB,wBAAwB,GAAGrlB,KAAK,CAAC6B,IAAI;IAC9C;IACA,IAAI6hB,QAAQ,CAAC4B,kBAAkB,EAAE;MAC7B,IAAI,CAAC9D,sBAAsB,GAAG,IAAI9L,sBAAsB,CAAC,IAAI,EAAE,IAAI,CAAC5S,KAAK,EAAE,IAAI,CAACiQ,IAAI,EAAE,IAAI,CAAC4C,SAAS,EAAE,IAAI,CAACC,YAAY,EAAE8N,QAAQ,CAAC;MAClI,IAAI,CAACI,8BAA8B,GAAG,IAAI,CAACtC,sBAAsB,CAACpL,mBAAmB;IACzF;IACA,IAAI,CAACmP,YAAY,GAAG7nB,gBAAgB,CAAC,IAAI,CAACqV,IAAI,CAACiL,cAAc,CAAC,CAAC,CAAC;IAChE,IAAI,CAACA,cAAc,CAAC,CAAC,CAAC/U,SAAS,CAACO,MAAM,CAAC,QAAQ,EAAE,IAAI,CAACka,QAAQ,CAAC/b,kBAAkB,KAAK1B,kBAAkB,CAACuf,MAAM,CAAC;EACpH;EACAle,aAAaA,CAACkM,aAAa,GAAG,CAAC,CAAC,EAAE;IAC9B,IAAI,CAACkQ,QAAQ,GAAG;MAAE,GAAG,IAAI,CAACA,QAAQ;MAAE,GAAGlQ;IAAc,CAAC;IACtD,KAAK,MAAMhN,QAAQ,IAAI,IAAI,CAACmP,SAAS,EAAE;MACnCnP,QAAQ,CAACc,aAAa,CAACkM,aAAa,CAAC;IACzC;IACA,IAAI,CAACT,IAAI,CAACzL,aAAa,CAAC,IAAI,CAACoc,QAAQ,CAAC;IACtC,IAAI,CAACF,cAAc,EAAElc,aAAa,CAACkM,aAAa,CAAC;IACjD,IAAI,CAACiS,kBAAkB,CAACjS,aAAa,CAAC;IACtC,IAAI,CAACwQ,mBAAmB,CAAC5R,IAAI,CAAC,IAAI,CAACsR,QAAQ,CAAC;IAC5C,IAAI,CAAC1F,cAAc,CAAC,CAAC,CAAC/U,SAAS,CAACO,MAAM,CAAC,QAAQ,EAAE,IAAI,CAACka,QAAQ,CAAC/b,kBAAkB,KAAK1B,kBAAkB,CAACuf,MAAM,CAAC;EACpH;EACA,IAAIrhB,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACuf,QAAQ;EACxB;EACA+B,kBAAkBA,CAACjS,aAAa,EAAE;IAC9B,IAAI,CAAC,IAAI,CAACgO,sBAAsB,IAAI,IAAI,CAACkC,QAAQ,CAAC4B,kBAAkB,EAAE;MAClE,IAAI,CAAC9D,sBAAsB,GAAG,IAAI9L,sBAAsB,CAAC,IAAI,EAAE,IAAI,CAAC5S,KAAK,EAAE,IAAI,CAACiQ,IAAI,EAAE,IAAI,CAAC4C,SAAS,EAAE,IAAI,CAACC,YAAY,EAAE,IAAI,CAAC8N,QAAQ,CAAC;MACvI,IAAI,CAACI,8BAA8B,GAAG,IAAI,CAACtC,sBAAsB,CAACpL,mBAAmB;IACzF,CAAC,MACI,IAAI,IAAI,CAACoL,sBAAsB,IAAI,CAAC,IAAI,CAACkC,QAAQ,CAAC4B,kBAAkB,EAAE;MACvE,IAAI,CAACxB,8BAA8B,GAAG9jB,KAAK,CAAC6B,IAAI;MAChD,IAAI,CAAC2f,sBAAsB,CAACjhB,OAAO,CAAC,CAAC;MACrC,IAAI,CAACihB,sBAAsB,GAAGrf,SAAS;IAC3C;IACA,IAAI,CAACqf,sBAAsB,EAAEla,aAAa,CAACkM,aAAa,CAAC;EAC7D;EACA;EACAwK,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAACjL,IAAI,CAACiL,cAAc,CAAC,CAAC;EACrC;EACA,IAAIrH,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAC5D,IAAI,CAAC4D,SAAS;EAC9B;EACA,IAAIA,SAASA,CAACA,SAAS,EAAE;IACrB,IAAI,CAAC5D,IAAI,CAAC4D,SAAS,GAAGA,SAAS;EACnC;EACA,IAAI+O,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAAC3S,IAAI,CAAC2S,YAAY;EACjC;EACA,IAAI3M,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAAChG,IAAI,CAACgG,YAAY;EACjC;EACA,IAAI8C,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAC9I,IAAI,CAAC8I,SAAS;EAC9B;EACA,IAAIA,SAASA,CAACG,KAAK,EAAE;IACjB,IAAI,CAACjJ,IAAI,CAAC8I,SAAS,GAAGG,KAAK;EAC/B;EACA9L,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACsR,sBAAsB,EAAE1H,WAAW,CAAC,CAAC,EAAE;MAC5C,IAAI,CAAC0H,sBAAsB,CAACtR,QAAQ,CAAC,CAAC;IAC1C,CAAC,MACI;MACD,IAAI,CAAC6C,IAAI,CAAC7C,QAAQ,CAAC,CAAC;IACxB;EACJ;EACAoB,MAAMA,CAACtL,MAAM,EAAE2C,KAAK,EAAE;IAClB,IAAI,CAACoK,IAAI,CAACzB,MAAM,CAACtL,MAAM,EAAE2C,KAAK,CAAC;IAC/B,IAAIjI,QAAQ,CAACiI,KAAK,CAAC,EAAE;MACjB,IAAI,CAAC6a,cAAc,EAAElS,MAAM,CAAC3I,KAAK,CAAC;IACtC;EACJ;EACAF,KAAKA,CAACoG,MAAM,EAAE;IACV,MAAM8W,MAAM,GAAG,IAAI,IAAI,CAAC5S,IAAI,CAAC6S,KAAK,EAAE;IACpC,MAAMhS,OAAO,GAAG,EAAE;IAClB,IAAI/E,MAAM,CAACgX,sBAAsB,EAAE;MAC/BjS,OAAO,CAACzH,IAAI,CAAC,eAAewZ,MAAM,yDAAyDA,MAAM,8DAA8D9W,MAAM,CAACiX,8BAA8B,KAAK,CAAC;MAC1MlS,OAAO,CAACzH,IAAI,CAAC,eAAewZ,MAAM,6DAA6D9W,MAAM,CAACgX,sBAAsB,KAAK,CAAC;IACtI;IACA;IACA,MAAME,sBAAsB,GAAGlX,MAAM,CAACmX,0BAA0B,IAAInX,MAAM,CAACoX,cAAc;IACzF,IAAIF,sBAAsB,EAAE;MACxBnS,OAAO,CAACzH,IAAI,CAAC,eAAewZ,MAAM,iFAAiFI,sBAAsB,KAAK,CAAC;MAC/InS,OAAO,CAACzH,IAAI,CAAC,eAAewZ,MAAM,yGAAyGI,sBAAsB,KAAK,CAAC;IAC3K;IACA;IACA,IAAIlX,MAAM,CAACqX,sBAAsB,EAAE;MAC/BtS,OAAO,CAACzH,IAAI,CAAC,eAAewZ,MAAM,wFAAwF9W,MAAM,CAACqX,sBAAsB,KAAK,CAAC;IACjK;IACA;IACA,IAAIrX,MAAM,CAACsX,sBAAsB,EAAE;MAC/BvS,OAAO,CAACzH,IAAI,CAAC,eAAewZ,MAAM,gHAAgH9W,MAAM,CAACsX,sBAAsB,uCAAuC,CAAC;IAC3N;IACA;IACA,IAAItX,MAAM,CAACuX,mBAAmB,EAAE;MAC5BxS,OAAO,CAACzH,IAAI,CAAC,eAAewZ,MAAM,2HAA2H9W,MAAM,CAACuX,mBAAmB,KAAK,CAAC;MAC7LxS,OAAO,CAACzH,IAAI,CAAC,eAAewZ,MAAM,oIAAoI,CAAC;IAC3K;IACA;IACA,MAAMU,wBAAwB,GAAGvoB,qBAAqB,CAAC+Q,MAAM,CAACyX,4BAA4B,EAAExoB,qBAAqB,CAAC+Q,MAAM,CAAC0X,oBAAoB,EAAE1X,MAAM,CAAC2X,gBAAgB,IAAI,EAAE,CAAC,CAAC;IAC9K,IAAIH,wBAAwB,EAAE;MAAE;MAC5BzS,OAAO,CAACzH,IAAI,CAAC,eAAewZ,MAAM,gJAAgJU,wBAAwB,0BAA0B,CAAC;MACrOzS,OAAO,CAACzH,IAAI,CAAC,eAAewZ,MAAM,8IAA8I,CAAC;IACrL;IACA,IAAI9W,MAAM,CAAC2X,gBAAgB,EAAE;MAAE;MAC3B5S,OAAO,CAACzH,IAAI,CAAC,eAAewZ,MAAM,uIAAuI9W,MAAM,CAAC2X,gBAAgB,2BAA2B,CAAC;MAC5N5S,OAAO,CAACzH,IAAI,CAAC,eAAewZ,MAAM,sIAAsI,CAAC;MACzK/R,OAAO,CAACzH,IAAI,CAAC,sDAAsDwZ,MAAM,sJAAsJ9W,MAAM,CAAC2X,gBAAgB,2BAA2B,CAAC;MAClR5S,OAAO,CAACzH,IAAI,CAAC,sDAAsDwZ,MAAM,sGAAsG,CAAC;MAChL/R,OAAO,CAACzH,IAAI,CAAC,sDAAsDwZ,MAAM,0IAA0I,CAAC;IACxN;IACA,IAAI,CAACJ,YAAY,CAACkB,WAAW,GAAG7S,OAAO,CAAC8S,IAAI,CAAC,IAAI,CAAC;IAClD,IAAI,CAAC3T,IAAI,CAACtK,KAAK,CAACoG,MAAM,CAAC;EAC3B;EACA;EACA8X,gBAAgBA,CAACvF,QAAQ,EAAE;IACvB,MAAM7d,SAAS,GAAG,IAAI,CAACT,KAAK,CAACU,qBAAqB,CAAC4d,QAAQ,CAAC;IAC5D,MAAM3d,UAAU,GAAG,IAAI,CAACX,KAAK,CAACY,OAAO,CAACH,SAAS,CAAC;IAChD,OAAOE,UAAU,CAAClC,OAAO;EAC7B;EACAqlB,oBAAoBA,CAACxF,QAAQ,EAAE;IAC3B,OAAO,IAAI,CAACte,KAAK,CAAC8jB,oBAAoB,CAACxF,QAAQ,CAAC;EACpD;EACA;EACA1d,OAAOA,CAAC0d,QAAQ,EAAE;IACd,OAAO,IAAI,CAACte,KAAK,CAACY,OAAO,CAAC0d,QAAQ,CAAC;EACvC;EACApe,eAAeA,CAAC1B,IAAI,EAAE;IAClB,OAAO,IAAI,CAACwB,KAAK,CAACE,eAAe,CAAC1B,IAAI,CAAC;EAC3C;EACAulB,QAAQA,CAACzF,QAAQ,EAAEC,SAAS,GAAG,KAAK,EAAE;IAClC,OAAO,IAAI,CAACve,KAAK,CAACI,YAAY,CAACke,QAAQ,EAAE,IAAI,EAAEC,SAAS,CAAC;EAC7D;EACAyF,MAAMA,CAAC1F,QAAQ,EAAEC,SAAS,GAAG,KAAK,EAAE;IAChC,OAAO,IAAI,CAACve,KAAK,CAACI,YAAY,CAACke,QAAQ,EAAE,KAAK,EAAEC,SAAS,CAAC;EAC9D;EACAE,eAAeA,CAACH,QAAQ,EAAEC,SAAS,GAAG,KAAK,EAAE;IACzC,OAAO,IAAI,CAACve,KAAK,CAACI,YAAY,CAACke,QAAQ,EAAEjf,SAAS,EAAEkf,SAAS,CAAC;EAClE;EACA0F,aAAaA,CAAC3F,QAAQ,EAAE;IACpB,OAAO,IAAI,CAACte,KAAK,CAACikB,aAAa,CAAC3F,QAAQ,CAAC;EAC7C;EACA4F,cAAcA,CAAC5F,QAAQ,EAAExY,WAAW,EAAE;IAClC,OAAO,IAAI,CAAC9F,KAAK,CAACkkB,cAAc,CAAC5F,QAAQ,EAAExY,WAAW,CAAC;EAC3D;EACA3F,WAAWA,CAACme,QAAQ,EAAE;IAClB,OAAO,IAAI,CAACte,KAAK,CAACG,WAAW,CAACme,QAAQ,CAAC;EAC3C;EACA1O,QAAQA,CAAA,EAAG;IACP,IAAI,CAACqR,eAAe,CAAC3R,IAAI,CAACjQ,SAAS,CAAC;IACpC,IAAI,CAACW,KAAK,CAAC4P,QAAQ,CAAC,CAAC;EACzB;EACA+O,YAAYA,CAACrgB,QAAQ,EAAE6b,YAAY,EAAE;IACjC,IAAI,CAAC4F,aAAa,CAAC4B,YAAY,CAAC,MAAM;MAClC,MAAMxiB,KAAK,GAAGb,QAAQ,CAACC,GAAG,CAACoD,CAAC,IAAI,IAAI,CAAC3B,KAAK,CAACY,OAAO,CAACe,CAAC,CAAC,CAAC;MACtD,IAAI,CAACue,SAAS,CAAC5a,GAAG,CAACnG,KAAK,EAAEgb,YAAY,CAAC;MACvC,MAAMwF,OAAO,GAAGrhB,QAAQ,CAACC,GAAG,CAACoD,CAAC,IAAI,IAAI,CAAC3B,KAAK,CAACc,YAAY,CAACa,CAAC,CAAC,CAAC,CAACsG,MAAM,CAACmB,CAAC,IAAIA,CAAC,GAAG,CAAC,CAAC,CAAC;MACjF,IAAI,CAAC6G,IAAI,CAAC0O,YAAY,CAACgB,OAAO,EAAExF,YAAY,EAAE,IAAI,CAAC;IACvD,CAAC,CAAC;EACN;EACAsF,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAACS,SAAS,CAACxa,GAAG,CAAC,CAAC;EAC/B;EACA4U,QAAQA,CAAChc,QAAQ,EAAE6b,YAAY,EAAE;IAC7B,IAAI,CAAC4F,aAAa,CAAC4B,YAAY,CAAC,MAAM;MAClC,MAAMxiB,KAAK,GAAGb,QAAQ,CAACC,GAAG,CAACoD,CAAC,IAAI,IAAI,CAAC3B,KAAK,CAACY,OAAO,CAACe,CAAC,CAAC,CAAC;MACtD,IAAI,CAAC0Y,KAAK,CAAC/U,GAAG,CAACnG,KAAK,EAAEgb,YAAY,CAAC;MACnC,MAAMwF,OAAO,GAAGrhB,QAAQ,CAACC,GAAG,CAACoD,CAAC,IAAI,IAAI,CAAC3B,KAAK,CAACc,YAAY,CAACa,CAAC,CAAC,CAAC,CAACsG,MAAM,CAACmB,CAAC,IAAIA,CAAC,GAAG,CAAC,CAAC,CAAC;MACjF,IAAI,CAAC6G,IAAI,CAACqK,QAAQ,CAACqF,OAAO,EAAExF,YAAY,EAAE,IAAI,CAAC;IACnD,CAAC,CAAC;EACN;EACAgK,SAASA,CAACC,CAAC,GAAG,CAAC,EAAEC,IAAI,GAAG,KAAK,EAAElK,YAAY,EAAElS,MAAM,GAAIhN,eAAe,CAACkf,YAAY,CAAC,IAAIA,YAAY,CAACqE,MAAM,GAAInf,SAAS,GAAG,IAAI,CAACgjB,qBAAqB,EAAE;IACnJ,IAAI,CAACpS,IAAI,CAACkU,SAAS,CAACC,CAAC,EAAEC,IAAI,EAAElK,YAAY,EAAElS,MAAM,CAAC;EACtD;EACAqc,aAAaA,CAACF,CAAC,GAAG,CAAC,EAAEC,IAAI,GAAG,KAAK,EAAElK,YAAY,EAAElS,MAAM,GAAIhN,eAAe,CAACkf,YAAY,CAAC,IAAIA,YAAY,CAACqE,MAAM,GAAInf,SAAS,GAAG,IAAI,CAACgjB,qBAAqB,EAAE;IACvJ,IAAI,CAACpS,IAAI,CAACqU,aAAa,CAACF,CAAC,EAAEC,IAAI,EAAElK,YAAY,EAAElS,MAAM,CAAC;EAC1D;EACAsc,aAAaA,CAACpK,YAAY,EAAElS,MAAM,GAAIhN,eAAe,CAACkf,YAAY,CAAC,IAAIA,YAAY,CAACqE,MAAM,GAAInf,SAAS,GAAG,IAAI,CAACgjB,qBAAqB,EAAE;IAClI,OAAO,IAAI,CAACpS,IAAI,CAACsU,aAAa,CAACpK,YAAY,EAAElS,MAAM,CAAC;EACxD;EACAuc,iBAAiBA,CAACrK,YAAY,EAAElS,MAAM,GAAIhN,eAAe,CAACkf,YAAY,CAAC,IAAIA,YAAY,CAACqE,MAAM,GAAInf,SAAS,GAAG,IAAI,CAACgjB,qBAAqB,EAAE;IACtI,OAAO,IAAI,CAACpS,IAAI,CAACuU,iBAAiB,CAACrK,YAAY,EAAElS,MAAM,EAAE,MAAM,IAAI,CAACyW,sBAAsB,EAAExb,MAAM,IAAI,CAAC,CAAC;EAC5G;EACAuhB,SAASA,CAACtK,YAAY,EAAElS,MAAM,GAAIhN,eAAe,CAACkf,YAAY,CAAC,IAAIA,YAAY,CAACqE,MAAM,GAAInf,SAAS,GAAG,IAAI,CAACgjB,qBAAqB,EAAE;IAC9H,IAAI,CAACpS,IAAI,CAACwU,SAAS,CAACtK,YAAY,EAAElS,MAAM,CAAC;EAC7C;EACAyc,UAAUA,CAACvK,YAAY,EAAElS,MAAM,GAAIhN,eAAe,CAACkf,YAAY,CAAC,IAAIA,YAAY,CAACqE,MAAM,GAAInf,SAAS,GAAG,IAAI,CAACgjB,qBAAqB,EAAE;IAC/H,IAAI,CAACpS,IAAI,CAACyU,UAAU,CAACvK,YAAY,EAAElS,MAAM,CAAC;EAC9C;EACAuX,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACnF,KAAK,CAAC3U,GAAG,CAAC,CAAC;EAC3B;EACAif,MAAMA,CAACrG,QAAQ,EAAEsG,WAAW,EAAE;IAC1B,IAAI,CAAC5kB,KAAK,CAAC6kB,QAAQ,CAACvG,QAAQ,CAAC;IAC7B,MAAMjZ,KAAK,GAAG,IAAI,CAACrF,KAAK,CAACc,YAAY,CAACwd,QAAQ,CAAC;IAC/C,IAAIjZ,KAAK,KAAK,CAAC,CAAC,EAAE;MACd;IACJ;IACA,IAAI,CAAC,IAAI,CAACqZ,sBAAsB,EAAE;MAC9B,IAAI,CAACzO,IAAI,CAAC0U,MAAM,CAACtf,KAAK,EAAEuf,WAAW,CAAC;IACxC,CAAC,MACI;MACD,MAAME,UAAU,GAAG,IAAI,CAACpG,sBAAsB,CAAC7H,0BAA0B,CAAC,IAAI,CAACjW,OAAO,CAAC0d,QAAQ,CAAC,CAAC;MACjG,IAAI,CAACrO,IAAI,CAAC0U,MAAM,CAACtf,KAAK,EAAEuf,WAAW,EAAEE,UAAU,CAAC;IACpD;EACJ;EACA;EACA9C,WAAWA,CAACrgB,CAAC,EAAE;IACXA,CAAC,CAACsL,cAAc,CAAC,CAAC;IAClBtL,CAAC,CAACuL,eAAe,CAAC,CAAC;IACnB,MAAM/N,KAAK,GAAG,IAAI,CAAC8Q,IAAI,CAACqR,kBAAkB,CAAC,CAAC;IAC5C,IAAIniB,KAAK,CAAC6B,MAAM,KAAK,CAAC,EAAE;MACpB;IACJ;IACA,MAAMxC,IAAI,GAAGW,KAAK,CAAC,CAAC,CAAC;IACrB,MAAMmf,QAAQ,GAAG,IAAI,CAACte,KAAK,CAACE,eAAe,CAAC1B,IAAI,CAAC;IACjD,MAAMumB,SAAS,GAAG,IAAI,CAAC/kB,KAAK,CAACI,YAAY,CAACke,QAAQ,EAAE,IAAI,CAAC;IACzD,IAAI,CAACyG,SAAS,EAAE;MACZ,MAAMpO,cAAc,GAAG,IAAI,CAAC3W,KAAK,CAACU,qBAAqB,CAAC4d,QAAQ,CAAC;MACjE,IAAI,CAAC3H,cAAc,EAAE;QACjB;MACJ;MACA,MAAMqO,eAAe,GAAG,IAAI,CAAChlB,KAAK,CAACc,YAAY,CAAC6V,cAAc,CAAC;MAC/D,IAAI,CAAC1G,IAAI,CAAC0U,MAAM,CAACK,eAAe,CAAC;MACjC,IAAI,CAAC/U,IAAI,CAACqK,QAAQ,CAAC,CAAC0K,eAAe,CAAC,CAAC;IACzC;EACJ;EACA/C,YAAYA,CAACtgB,CAAC,EAAE;IACZA,CAAC,CAACsL,cAAc,CAAC,CAAC;IAClBtL,CAAC,CAACuL,eAAe,CAAC,CAAC;IACnB,MAAM/N,KAAK,GAAG,IAAI,CAAC8Q,IAAI,CAACqR,kBAAkB,CAAC,CAAC;IAC5C,IAAIniB,KAAK,CAAC6B,MAAM,KAAK,CAAC,EAAE;MACpB;IACJ;IACA,MAAMxC,IAAI,GAAGW,KAAK,CAAC,CAAC,CAAC;IACrB,MAAMmf,QAAQ,GAAG,IAAI,CAACte,KAAK,CAACE,eAAe,CAAC1B,IAAI,CAAC;IACjD,MAAMumB,SAAS,GAAG,IAAI,CAAC/kB,KAAK,CAACI,YAAY,CAACke,QAAQ,EAAE,KAAK,CAAC;IAC1D,IAAI,CAACyG,SAAS,EAAE;MACZ,IAAI,CAACvmB,IAAI,CAAC2I,QAAQ,CAAC8d,IAAI,CAAC/I,KAAK,IAAIA,KAAK,CAAC7C,OAAO,CAAC,EAAE;QAC7C;MACJ;MACA,MAAM,CAACK,YAAY,CAAC,GAAG,IAAI,CAACzJ,IAAI,CAACuP,QAAQ,CAAC,CAAC;MAC3C,MAAM0F,eAAe,GAAGxL,YAAY,GAAG,CAAC;MACxC,IAAI,CAACzJ,IAAI,CAAC0U,MAAM,CAACO,eAAe,CAAC;MACjC,IAAI,CAACjV,IAAI,CAACqK,QAAQ,CAAC,CAAC4K,eAAe,CAAC,CAAC;IACzC;EACJ;EACAhD,OAAOA,CAACvgB,CAAC,EAAE;IACPA,CAAC,CAACsL,cAAc,CAAC,CAAC;IAClBtL,CAAC,CAACuL,eAAe,CAAC,CAAC;IACnB,MAAM/N,KAAK,GAAG,IAAI,CAAC8Q,IAAI,CAACqR,kBAAkB,CAAC,CAAC;IAC5C,IAAIniB,KAAK,CAAC6B,MAAM,KAAK,CAAC,EAAE;MACpB;IACJ;IACA,MAAMxC,IAAI,GAAGW,KAAK,CAAC,CAAC,CAAC;IACrB,MAAMmf,QAAQ,GAAG,IAAI,CAACte,KAAK,CAACE,eAAe,CAAC1B,IAAI,CAAC;IACjD,MAAM+f,SAAS,GAAG5c,CAAC,CAACwY,YAAY,CAACqE,MAAM;IACvC,IAAI,CAACxe,KAAK,CAACI,YAAY,CAACke,QAAQ,EAAEjf,SAAS,EAAEkf,SAAS,CAAC;EAC3D;EACA9gB,OAAOA,CAAA,EAAG;IACNA,OAAO,CAAC,IAAI,CAACuB,WAAW,CAAC;IACzB,IAAI,CAAC0f,sBAAsB,EAAEjhB,OAAO,CAAC,CAAC;IACtC,IAAI,CAACwS,IAAI,CAACxS,OAAO,CAAC,CAAC;EACvB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}