{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n/**\n * The minimal size of the slider (such that it can still be clickable) -- it is artificially enlarged.\n */\nconst MINIMUM_SLIDER_SIZE = 20;\nexport class ScrollbarState {\n  constructor(arrowSize, scrollbarSize, oppositeScrollbarSize, visibleSize, scrollSize, scrollPosition) {\n    this._scrollbarSize = Math.round(scrollbarSize);\n    this._oppositeScrollbarSize = Math.round(oppositeScrollbarSize);\n    this._arrowSize = Math.round(arrowSize);\n    this._visibleSize = visibleSize;\n    this._scrollSize = scrollSize;\n    this._scrollPosition = scrollPosition;\n    this._computedAvailableSize = 0;\n    this._computedIsNeeded = false;\n    this._computedSliderSize = 0;\n    this._computedSliderRatio = 0;\n    this._computedSliderPosition = 0;\n    this._refreshComputedValues();\n  }\n  clone() {\n    return new ScrollbarState(this._arrowSize, this._scrollbarSize, this._oppositeScrollbarSize, this._visibleSize, this._scrollSize, this._scrollPosition);\n  }\n  setVisibleSize(visibleSize) {\n    const iVisibleSize = Math.round(visibleSize);\n    if (this._visibleSize !== iVisibleSize) {\n      this._visibleSize = iVisibleSize;\n      this._refreshComputedValues();\n      return true;\n    }\n    return false;\n  }\n  setScrollSize(scrollSize) {\n    const iScrollSize = Math.round(scrollSize);\n    if (this._scrollSize !== iScrollSize) {\n      this._scrollSize = iScrollSize;\n      this._refreshComputedValues();\n      return true;\n    }\n    return false;\n  }\n  setScrollPosition(scrollPosition) {\n    const iScrollPosition = Math.round(scrollPosition);\n    if (this._scrollPosition !== iScrollPosition) {\n      this._scrollPosition = iScrollPosition;\n      this._refreshComputedValues();\n      return true;\n    }\n    return false;\n  }\n  setScrollbarSize(scrollbarSize) {\n    this._scrollbarSize = Math.round(scrollbarSize);\n  }\n  setOppositeScrollbarSize(oppositeScrollbarSize) {\n    this._oppositeScrollbarSize = Math.round(oppositeScrollbarSize);\n  }\n  static _computeValues(oppositeScrollbarSize, arrowSize, visibleSize, scrollSize, scrollPosition) {\n    const computedAvailableSize = Math.max(0, visibleSize - oppositeScrollbarSize);\n    const computedRepresentableSize = Math.max(0, computedAvailableSize - 2 * arrowSize);\n    const computedIsNeeded = scrollSize > 0 && scrollSize > visibleSize;\n    if (!computedIsNeeded) {\n      // There is no need for a slider\n      return {\n        computedAvailableSize: Math.round(computedAvailableSize),\n        computedIsNeeded: computedIsNeeded,\n        computedSliderSize: Math.round(computedRepresentableSize),\n        computedSliderRatio: 0,\n        computedSliderPosition: 0\n      };\n    }\n    // We must artificially increase the size of the slider if needed, since the slider would be too small to grab with the mouse otherwise\n    const computedSliderSize = Math.round(Math.max(MINIMUM_SLIDER_SIZE, Math.floor(visibleSize * computedRepresentableSize / scrollSize)));\n    // The slider can move from 0 to `computedRepresentableSize` - `computedSliderSize`\n    // in the same way `scrollPosition` can move from 0 to `scrollSize` - `visibleSize`.\n    const computedSliderRatio = (computedRepresentableSize - computedSliderSize) / (scrollSize - visibleSize);\n    const computedSliderPosition = scrollPosition * computedSliderRatio;\n    return {\n      computedAvailableSize: Math.round(computedAvailableSize),\n      computedIsNeeded: computedIsNeeded,\n      computedSliderSize: Math.round(computedSliderSize),\n      computedSliderRatio: computedSliderRatio,\n      computedSliderPosition: Math.round(computedSliderPosition)\n    };\n  }\n  _refreshComputedValues() {\n    const r = ScrollbarState._computeValues(this._oppositeScrollbarSize, this._arrowSize, this._visibleSize, this._scrollSize, this._scrollPosition);\n    this._computedAvailableSize = r.computedAvailableSize;\n    this._computedIsNeeded = r.computedIsNeeded;\n    this._computedSliderSize = r.computedSliderSize;\n    this._computedSliderRatio = r.computedSliderRatio;\n    this._computedSliderPosition = r.computedSliderPosition;\n  }\n  getArrowSize() {\n    return this._arrowSize;\n  }\n  getScrollPosition() {\n    return this._scrollPosition;\n  }\n  getRectangleLargeSize() {\n    return this._computedAvailableSize;\n  }\n  getRectangleSmallSize() {\n    return this._scrollbarSize;\n  }\n  isNeeded() {\n    return this._computedIsNeeded;\n  }\n  getSliderSize() {\n    return this._computedSliderSize;\n  }\n  getSliderPosition() {\n    return this._computedSliderPosition;\n  }\n  /**\n   * Compute a desired `scrollPosition` such that `offset` ends up in the center of the slider.\n   * `offset` is based on the same coordinate system as the `sliderPosition`.\n   */\n  getDesiredScrollPositionFromOffset(offset) {\n    if (!this._computedIsNeeded) {\n      // no need for a slider\n      return 0;\n    }\n    const desiredSliderPosition = offset - this._arrowSize - this._computedSliderSize / 2;\n    return Math.round(desiredSliderPosition / this._computedSliderRatio);\n  }\n  /**\n   * Compute a desired `scrollPosition` from if offset is before or after the slider position.\n   * If offset is before slider, treat as a page up (or left).  If after, page down (or right).\n   * `offset` and `_computedSliderPosition` are based on the same coordinate system.\n   * `_visibleSize` corresponds to a \"page\" of lines in the returned coordinate system.\n   */\n  getDesiredScrollPositionFromOffsetPaged(offset) {\n    if (!this._computedIsNeeded) {\n      // no need for a slider\n      return 0;\n    }\n    const correctedOffset = offset - this._arrowSize; // compensate if has arrows\n    let desiredScrollPosition = this._scrollPosition;\n    if (correctedOffset < this._computedSliderPosition) {\n      desiredScrollPosition -= this._visibleSize; // page up/left\n    } else {\n      desiredScrollPosition += this._visibleSize; // page down/right\n    }\n    return desiredScrollPosition;\n  }\n  /**\n   * Compute a desired `scrollPosition` such that the slider moves by `delta`.\n   */\n  getDesiredScrollPositionFromDelta(delta) {\n    if (!this._computedIsNeeded) {\n      // no need for a slider\n      return 0;\n    }\n    const desiredSliderPosition = this._computedSliderPosition + delta;\n    return Math.round(desiredSliderPosition / this._computedSliderRatio);\n  }\n}", "map": {"version": 3, "names": ["MINIMUM_SLIDER_SIZE", "ScrollbarState", "constructor", "arrowSize", "scrollbarSize", "oppositeScrollbarSize", "visibleSize", "scrollSize", "scrollPosition", "_scrollbarSize", "Math", "round", "_oppositeScrollbarSize", "_arrowSize", "_visibleSize", "_scrollSize", "_scrollPosition", "_computedAvailableSize", "_computedIsNeeded", "_computedSliderSize", "_computedSliderRatio", "_computedSliderPosition", "_refreshComputedValues", "clone", "setVisibleSize", "iVisibleSize", "setScrollSize", "iScrollSize", "setScrollPosition", "iScrollPosition", "setScrollbarSize", "setOppositeScrollbarSize", "_computeValues", "computedAvailableSize", "max", "computedRepresentableSize", "computedIsNeeded", "computedSliderSize", "computedSliderRatio", "computedSliderPosition", "floor", "r", "getArrowSize", "getScrollPosition", "getRectangleLargeSize", "getRectangleSmallSize", "isNeeded", "getSliderSize", "getSliderPosition", "getDesiredScrollPositionFromOffset", "offset", "desiredSliderPosition", "getDesiredScrollPositionFromOffsetPaged", "correctedOffset", "desiredScrollPosition", "getDesiredScrollPositionFromDelta", "delta"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/browser/ui/scrollbar/scrollbarState.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n/**\n * The minimal size of the slider (such that it can still be clickable) -- it is artificially enlarged.\n */\nconst MINIMUM_SLIDER_SIZE = 20;\nexport class ScrollbarState {\n    constructor(arrowSize, scrollbarSize, oppositeScrollbarSize, visibleSize, scrollSize, scrollPosition) {\n        this._scrollbarSize = Math.round(scrollbarSize);\n        this._oppositeScrollbarSize = Math.round(oppositeScrollbarSize);\n        this._arrowSize = Math.round(arrowSize);\n        this._visibleSize = visibleSize;\n        this._scrollSize = scrollSize;\n        this._scrollPosition = scrollPosition;\n        this._computedAvailableSize = 0;\n        this._computedIsNeeded = false;\n        this._computedSliderSize = 0;\n        this._computedSliderRatio = 0;\n        this._computedSliderPosition = 0;\n        this._refreshComputedValues();\n    }\n    clone() {\n        return new ScrollbarState(this._arrowSize, this._scrollbarSize, this._oppositeScrollbarSize, this._visibleSize, this._scrollSize, this._scrollPosition);\n    }\n    setVisibleSize(visibleSize) {\n        const iVisibleSize = Math.round(visibleSize);\n        if (this._visibleSize !== iVisibleSize) {\n            this._visibleSize = iVisibleSize;\n            this._refreshComputedValues();\n            return true;\n        }\n        return false;\n    }\n    setScrollSize(scrollSize) {\n        const iScrollSize = Math.round(scrollSize);\n        if (this._scrollSize !== iScrollSize) {\n            this._scrollSize = iScrollSize;\n            this._refreshComputedValues();\n            return true;\n        }\n        return false;\n    }\n    setScrollPosition(scrollPosition) {\n        const iScrollPosition = Math.round(scrollPosition);\n        if (this._scrollPosition !== iScrollPosition) {\n            this._scrollPosition = iScrollPosition;\n            this._refreshComputedValues();\n            return true;\n        }\n        return false;\n    }\n    setScrollbarSize(scrollbarSize) {\n        this._scrollbarSize = Math.round(scrollbarSize);\n    }\n    setOppositeScrollbarSize(oppositeScrollbarSize) {\n        this._oppositeScrollbarSize = Math.round(oppositeScrollbarSize);\n    }\n    static _computeValues(oppositeScrollbarSize, arrowSize, visibleSize, scrollSize, scrollPosition) {\n        const computedAvailableSize = Math.max(0, visibleSize - oppositeScrollbarSize);\n        const computedRepresentableSize = Math.max(0, computedAvailableSize - 2 * arrowSize);\n        const computedIsNeeded = (scrollSize > 0 && scrollSize > visibleSize);\n        if (!computedIsNeeded) {\n            // There is no need for a slider\n            return {\n                computedAvailableSize: Math.round(computedAvailableSize),\n                computedIsNeeded: computedIsNeeded,\n                computedSliderSize: Math.round(computedRepresentableSize),\n                computedSliderRatio: 0,\n                computedSliderPosition: 0,\n            };\n        }\n        // We must artificially increase the size of the slider if needed, since the slider would be too small to grab with the mouse otherwise\n        const computedSliderSize = Math.round(Math.max(MINIMUM_SLIDER_SIZE, Math.floor(visibleSize * computedRepresentableSize / scrollSize)));\n        // The slider can move from 0 to `computedRepresentableSize` - `computedSliderSize`\n        // in the same way `scrollPosition` can move from 0 to `scrollSize` - `visibleSize`.\n        const computedSliderRatio = (computedRepresentableSize - computedSliderSize) / (scrollSize - visibleSize);\n        const computedSliderPosition = (scrollPosition * computedSliderRatio);\n        return {\n            computedAvailableSize: Math.round(computedAvailableSize),\n            computedIsNeeded: computedIsNeeded,\n            computedSliderSize: Math.round(computedSliderSize),\n            computedSliderRatio: computedSliderRatio,\n            computedSliderPosition: Math.round(computedSliderPosition),\n        };\n    }\n    _refreshComputedValues() {\n        const r = ScrollbarState._computeValues(this._oppositeScrollbarSize, this._arrowSize, this._visibleSize, this._scrollSize, this._scrollPosition);\n        this._computedAvailableSize = r.computedAvailableSize;\n        this._computedIsNeeded = r.computedIsNeeded;\n        this._computedSliderSize = r.computedSliderSize;\n        this._computedSliderRatio = r.computedSliderRatio;\n        this._computedSliderPosition = r.computedSliderPosition;\n    }\n    getArrowSize() {\n        return this._arrowSize;\n    }\n    getScrollPosition() {\n        return this._scrollPosition;\n    }\n    getRectangleLargeSize() {\n        return this._computedAvailableSize;\n    }\n    getRectangleSmallSize() {\n        return this._scrollbarSize;\n    }\n    isNeeded() {\n        return this._computedIsNeeded;\n    }\n    getSliderSize() {\n        return this._computedSliderSize;\n    }\n    getSliderPosition() {\n        return this._computedSliderPosition;\n    }\n    /**\n     * Compute a desired `scrollPosition` such that `offset` ends up in the center of the slider.\n     * `offset` is based on the same coordinate system as the `sliderPosition`.\n     */\n    getDesiredScrollPositionFromOffset(offset) {\n        if (!this._computedIsNeeded) {\n            // no need for a slider\n            return 0;\n        }\n        const desiredSliderPosition = offset - this._arrowSize - this._computedSliderSize / 2;\n        return Math.round(desiredSliderPosition / this._computedSliderRatio);\n    }\n    /**\n     * Compute a desired `scrollPosition` from if offset is before or after the slider position.\n     * If offset is before slider, treat as a page up (or left).  If after, page down (or right).\n     * `offset` and `_computedSliderPosition` are based on the same coordinate system.\n     * `_visibleSize` corresponds to a \"page\" of lines in the returned coordinate system.\n     */\n    getDesiredScrollPositionFromOffsetPaged(offset) {\n        if (!this._computedIsNeeded) {\n            // no need for a slider\n            return 0;\n        }\n        const correctedOffset = offset - this._arrowSize; // compensate if has arrows\n        let desiredScrollPosition = this._scrollPosition;\n        if (correctedOffset < this._computedSliderPosition) {\n            desiredScrollPosition -= this._visibleSize; // page up/left\n        }\n        else {\n            desiredScrollPosition += this._visibleSize; // page down/right\n        }\n        return desiredScrollPosition;\n    }\n    /**\n     * Compute a desired `scrollPosition` such that the slider moves by `delta`.\n     */\n    getDesiredScrollPositionFromDelta(delta) {\n        if (!this._computedIsNeeded) {\n            // no need for a slider\n            return 0;\n        }\n        const desiredSliderPosition = this._computedSliderPosition + delta;\n        return Math.round(desiredSliderPosition / this._computedSliderRatio);\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,mBAAmB,GAAG,EAAE;AAC9B,OAAO,MAAMC,cAAc,CAAC;EACxBC,WAAWA,CAACC,SAAS,EAAEC,aAAa,EAAEC,qBAAqB,EAAEC,WAAW,EAAEC,UAAU,EAAEC,cAAc,EAAE;IAClG,IAAI,CAACC,cAAc,GAAGC,IAAI,CAACC,KAAK,CAACP,aAAa,CAAC;IAC/C,IAAI,CAACQ,sBAAsB,GAAGF,IAAI,CAACC,KAAK,CAACN,qBAAqB,CAAC;IAC/D,IAAI,CAACQ,UAAU,GAAGH,IAAI,CAACC,KAAK,CAACR,SAAS,CAAC;IACvC,IAAI,CAACW,YAAY,GAAGR,WAAW;IAC/B,IAAI,CAACS,WAAW,GAAGR,UAAU;IAC7B,IAAI,CAACS,eAAe,GAAGR,cAAc;IACrC,IAAI,CAACS,sBAAsB,GAAG,CAAC;IAC/B,IAAI,CAACC,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACC,mBAAmB,GAAG,CAAC;IAC5B,IAAI,CAACC,oBAAoB,GAAG,CAAC;IAC7B,IAAI,CAACC,uBAAuB,GAAG,CAAC;IAChC,IAAI,CAACC,sBAAsB,CAAC,CAAC;EACjC;EACAC,KAAKA,CAAA,EAAG;IACJ,OAAO,IAAItB,cAAc,CAAC,IAAI,CAACY,UAAU,EAAE,IAAI,CAACJ,cAAc,EAAE,IAAI,CAACG,sBAAsB,EAAE,IAAI,CAACE,YAAY,EAAE,IAAI,CAACC,WAAW,EAAE,IAAI,CAACC,eAAe,CAAC;EAC3J;EACAQ,cAAcA,CAAClB,WAAW,EAAE;IACxB,MAAMmB,YAAY,GAAGf,IAAI,CAACC,KAAK,CAACL,WAAW,CAAC;IAC5C,IAAI,IAAI,CAACQ,YAAY,KAAKW,YAAY,EAAE;MACpC,IAAI,CAACX,YAAY,GAAGW,YAAY;MAChC,IAAI,CAACH,sBAAsB,CAAC,CAAC;MAC7B,OAAO,IAAI;IACf;IACA,OAAO,KAAK;EAChB;EACAI,aAAaA,CAACnB,UAAU,EAAE;IACtB,MAAMoB,WAAW,GAAGjB,IAAI,CAACC,KAAK,CAACJ,UAAU,CAAC;IAC1C,IAAI,IAAI,CAACQ,WAAW,KAAKY,WAAW,EAAE;MAClC,IAAI,CAACZ,WAAW,GAAGY,WAAW;MAC9B,IAAI,CAACL,sBAAsB,CAAC,CAAC;MAC7B,OAAO,IAAI;IACf;IACA,OAAO,KAAK;EAChB;EACAM,iBAAiBA,CAACpB,cAAc,EAAE;IAC9B,MAAMqB,eAAe,GAAGnB,IAAI,CAACC,KAAK,CAACH,cAAc,CAAC;IAClD,IAAI,IAAI,CAACQ,eAAe,KAAKa,eAAe,EAAE;MAC1C,IAAI,CAACb,eAAe,GAAGa,eAAe;MACtC,IAAI,CAACP,sBAAsB,CAAC,CAAC;MAC7B,OAAO,IAAI;IACf;IACA,OAAO,KAAK;EAChB;EACAQ,gBAAgBA,CAAC1B,aAAa,EAAE;IAC5B,IAAI,CAACK,cAAc,GAAGC,IAAI,CAACC,KAAK,CAACP,aAAa,CAAC;EACnD;EACA2B,wBAAwBA,CAAC1B,qBAAqB,EAAE;IAC5C,IAAI,CAACO,sBAAsB,GAAGF,IAAI,CAACC,KAAK,CAACN,qBAAqB,CAAC;EACnE;EACA,OAAO2B,cAAcA,CAAC3B,qBAAqB,EAAEF,SAAS,EAAEG,WAAW,EAAEC,UAAU,EAAEC,cAAc,EAAE;IAC7F,MAAMyB,qBAAqB,GAAGvB,IAAI,CAACwB,GAAG,CAAC,CAAC,EAAE5B,WAAW,GAAGD,qBAAqB,CAAC;IAC9E,MAAM8B,yBAAyB,GAAGzB,IAAI,CAACwB,GAAG,CAAC,CAAC,EAAED,qBAAqB,GAAG,CAAC,GAAG9B,SAAS,CAAC;IACpF,MAAMiC,gBAAgB,GAAI7B,UAAU,GAAG,CAAC,IAAIA,UAAU,GAAGD,WAAY;IACrE,IAAI,CAAC8B,gBAAgB,EAAE;MACnB;MACA,OAAO;QACHH,qBAAqB,EAAEvB,IAAI,CAACC,KAAK,CAACsB,qBAAqB,CAAC;QACxDG,gBAAgB,EAAEA,gBAAgB;QAClCC,kBAAkB,EAAE3B,IAAI,CAACC,KAAK,CAACwB,yBAAyB,CAAC;QACzDG,mBAAmB,EAAE,CAAC;QACtBC,sBAAsB,EAAE;MAC5B,CAAC;IACL;IACA;IACA,MAAMF,kBAAkB,GAAG3B,IAAI,CAACC,KAAK,CAACD,IAAI,CAACwB,GAAG,CAAClC,mBAAmB,EAAEU,IAAI,CAAC8B,KAAK,CAAClC,WAAW,GAAG6B,yBAAyB,GAAG5B,UAAU,CAAC,CAAC,CAAC;IACtI;IACA;IACA,MAAM+B,mBAAmB,GAAG,CAACH,yBAAyB,GAAGE,kBAAkB,KAAK9B,UAAU,GAAGD,WAAW,CAAC;IACzG,MAAMiC,sBAAsB,GAAI/B,cAAc,GAAG8B,mBAAoB;IACrE,OAAO;MACHL,qBAAqB,EAAEvB,IAAI,CAACC,KAAK,CAACsB,qBAAqB,CAAC;MACxDG,gBAAgB,EAAEA,gBAAgB;MAClCC,kBAAkB,EAAE3B,IAAI,CAACC,KAAK,CAAC0B,kBAAkB,CAAC;MAClDC,mBAAmB,EAAEA,mBAAmB;MACxCC,sBAAsB,EAAE7B,IAAI,CAACC,KAAK,CAAC4B,sBAAsB;IAC7D,CAAC;EACL;EACAjB,sBAAsBA,CAAA,EAAG;IACrB,MAAMmB,CAAC,GAAGxC,cAAc,CAAC+B,cAAc,CAAC,IAAI,CAACpB,sBAAsB,EAAE,IAAI,CAACC,UAAU,EAAE,IAAI,CAACC,YAAY,EAAE,IAAI,CAACC,WAAW,EAAE,IAAI,CAACC,eAAe,CAAC;IAChJ,IAAI,CAACC,sBAAsB,GAAGwB,CAAC,CAACR,qBAAqB;IACrD,IAAI,CAACf,iBAAiB,GAAGuB,CAAC,CAACL,gBAAgB;IAC3C,IAAI,CAACjB,mBAAmB,GAAGsB,CAAC,CAACJ,kBAAkB;IAC/C,IAAI,CAACjB,oBAAoB,GAAGqB,CAAC,CAACH,mBAAmB;IACjD,IAAI,CAACjB,uBAAuB,GAAGoB,CAAC,CAACF,sBAAsB;EAC3D;EACAG,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC7B,UAAU;EAC1B;EACA8B,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAAC3B,eAAe;EAC/B;EACA4B,qBAAqBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAAC3B,sBAAsB;EACtC;EACA4B,qBAAqBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACpC,cAAc;EAC9B;EACAqC,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAAC5B,iBAAiB;EACjC;EACA6B,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAC5B,mBAAmB;EACnC;EACA6B,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAAC3B,uBAAuB;EACvC;EACA;AACJ;AACA;AACA;EACI4B,kCAAkCA,CAACC,MAAM,EAAE;IACvC,IAAI,CAAC,IAAI,CAAChC,iBAAiB,EAAE;MACzB;MACA,OAAO,CAAC;IACZ;IACA,MAAMiC,qBAAqB,GAAGD,MAAM,GAAG,IAAI,CAACrC,UAAU,GAAG,IAAI,CAACM,mBAAmB,GAAG,CAAC;IACrF,OAAOT,IAAI,CAACC,KAAK,CAACwC,qBAAqB,GAAG,IAAI,CAAC/B,oBAAoB,CAAC;EACxE;EACA;AACJ;AACA;AACA;AACA;AACA;EACIgC,uCAAuCA,CAACF,MAAM,EAAE;IAC5C,IAAI,CAAC,IAAI,CAAChC,iBAAiB,EAAE;MACzB;MACA,OAAO,CAAC;IACZ;IACA,MAAMmC,eAAe,GAAGH,MAAM,GAAG,IAAI,CAACrC,UAAU,CAAC,CAAC;IAClD,IAAIyC,qBAAqB,GAAG,IAAI,CAACtC,eAAe;IAChD,IAAIqC,eAAe,GAAG,IAAI,CAAChC,uBAAuB,EAAE;MAChDiC,qBAAqB,IAAI,IAAI,CAACxC,YAAY,CAAC,CAAC;IAChD,CAAC,MACI;MACDwC,qBAAqB,IAAI,IAAI,CAACxC,YAAY,CAAC,CAAC;IAChD;IACA,OAAOwC,qBAAqB;EAChC;EACA;AACJ;AACA;EACIC,iCAAiCA,CAACC,KAAK,EAAE;IACrC,IAAI,CAAC,IAAI,CAACtC,iBAAiB,EAAE;MACzB;MACA,OAAO,CAAC;IACZ;IACA,MAAMiC,qBAAqB,GAAG,IAAI,CAAC9B,uBAAuB,GAAGmC,KAAK;IAClE,OAAO9C,IAAI,CAACC,KAAK,CAACwC,qBAAqB,GAAG,IAAI,CAAC/B,oBAAoB,CAAC;EACxE;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}