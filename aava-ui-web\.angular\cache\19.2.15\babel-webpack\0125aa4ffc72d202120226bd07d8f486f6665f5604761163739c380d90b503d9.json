{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = this && this.__param || function (paramIndex, decorator) {\n  return function (target, key) {\n    decorator(target, key, paramIndex);\n  };\n};\nimport { $, addDisposableListener } from '../../../../../../base/browser/dom.js';\nimport { ArrayQueue } from '../../../../../../base/common/arrays.js';\nimport { RunOnceScheduler } from '../../../../../../base/common/async.js';\nimport { Codicon } from '../../../../../../base/common/codicons.js';\nimport { Disposable, DisposableStore } from '../../../../../../base/common/lifecycle.js';\nimport { autorun, derived, derivedWithStore, observableFromEvent, observableValue } from '../../../../../../base/common/observable.js';\nimport { ThemeIcon } from '../../../../../../base/common/themables.js';\nimport { assertIsDefined } from '../../../../../../base/common/types.js';\nimport { applyFontInfo } from '../../../../config/domFontInfo.js';\nimport { diffDeleteDecoration, diffRemoveIcon } from '../../registrations.contribution.js';\nimport { DiffMapping } from '../../diffEditorViewModel.js';\nimport { InlineDiffDeletedCodeMargin } from './inlineDiffDeletedCodeMargin.js';\nimport { LineSource, RenderOptions, renderLines } from './renderLines.js';\nimport { animatedObservable, joinCombine } from '../../utils.js';\nimport { LineRange } from '../../../../../common/core/lineRange.js';\nimport { Position } from '../../../../../common/core/position.js';\nimport { InlineDecoration } from '../../../../../common/viewModel.js';\nimport { IClipboardService } from '../../../../../../platform/clipboard/common/clipboardService.js';\nimport { IContextMenuService } from '../../../../../../platform/contextview/browser/contextView.js';\nimport { Range } from '../../../../../common/core/range.js';\n/**\n * Ensures both editors have the same height by aligning unchanged lines.\n * In inline view mode, inserts viewzones to show deleted code from the original text model in the modified code editor.\n * Synchronizes scrolling.\n *\n * Make sure to add the view zones!\n */\nlet DiffEditorViewZones = class DiffEditorViewZones extends Disposable {\n  constructor(_targetWindow, _editors, _diffModel, _options, _diffEditorWidget, _canIgnoreViewZoneUpdateEvent, _origViewZonesToIgnore, _modViewZonesToIgnore, _clipboardService, _contextMenuService) {\n    super();\n    this._targetWindow = _targetWindow;\n    this._editors = _editors;\n    this._diffModel = _diffModel;\n    this._options = _options;\n    this._diffEditorWidget = _diffEditorWidget;\n    this._canIgnoreViewZoneUpdateEvent = _canIgnoreViewZoneUpdateEvent;\n    this._origViewZonesToIgnore = _origViewZonesToIgnore;\n    this._modViewZonesToIgnore = _modViewZonesToIgnore;\n    this._clipboardService = _clipboardService;\n    this._contextMenuService = _contextMenuService;\n    this._originalTopPadding = observableValue(this, 0);\n    this._originalScrollOffset = observableValue(this, 0);\n    this._originalScrollOffsetAnimated = animatedObservable(this._targetWindow, this._originalScrollOffset, this._store);\n    this._modifiedTopPadding = observableValue(this, 0);\n    this._modifiedScrollOffset = observableValue(this, 0);\n    this._modifiedScrollOffsetAnimated = animatedObservable(this._targetWindow, this._modifiedScrollOffset, this._store);\n    const state = observableValue('invalidateAlignmentsState', 0);\n    const updateImmediately = this._register(new RunOnceScheduler(() => {\n      state.set(state.get() + 1, undefined);\n    }, 0));\n    this._register(this._editors.original.onDidChangeViewZones(_args => {\n      if (!this._canIgnoreViewZoneUpdateEvent()) {\n        updateImmediately.schedule();\n      }\n    }));\n    this._register(this._editors.modified.onDidChangeViewZones(_args => {\n      if (!this._canIgnoreViewZoneUpdateEvent()) {\n        updateImmediately.schedule();\n      }\n    }));\n    this._register(this._editors.original.onDidChangeConfiguration(args => {\n      if (args.hasChanged(147 /* EditorOption.wrappingInfo */) || args.hasChanged(67 /* EditorOption.lineHeight */)) {\n        updateImmediately.schedule();\n      }\n    }));\n    this._register(this._editors.modified.onDidChangeConfiguration(args => {\n      if (args.hasChanged(147 /* EditorOption.wrappingInfo */) || args.hasChanged(67 /* EditorOption.lineHeight */)) {\n        updateImmediately.schedule();\n      }\n    }));\n    const originalModelTokenizationCompleted = this._diffModel.map(m => m ? observableFromEvent(this, m.model.original.onDidChangeTokens, () => m.model.original.tokenization.backgroundTokenizationState === 2 /* BackgroundTokenizationState.Completed */) : undefined).map((m, reader) => m?.read(reader));\n    const alignments = derived(reader => {\n      /** @description alignments */\n      const diffModel = this._diffModel.read(reader);\n      const diff = diffModel?.diff.read(reader);\n      if (!diffModel || !diff) {\n        return null;\n      }\n      state.read(reader);\n      const renderSideBySide = this._options.renderSideBySide.read(reader);\n      const innerHunkAlignment = renderSideBySide;\n      return computeRangeAlignment(this._editors.original, this._editors.modified, diff.mappings, this._origViewZonesToIgnore, this._modViewZonesToIgnore, innerHunkAlignment);\n    });\n    const alignmentsSyncedMovedText = derived(reader => {\n      /** @description alignmentsSyncedMovedText */\n      const syncedMovedText = this._diffModel.read(reader)?.movedTextToCompare.read(reader);\n      if (!syncedMovedText) {\n        return null;\n      }\n      state.read(reader);\n      const mappings = syncedMovedText.changes.map(c => new DiffMapping(c));\n      // TODO dont include alignments outside syncedMovedText\n      return computeRangeAlignment(this._editors.original, this._editors.modified, mappings, this._origViewZonesToIgnore, this._modViewZonesToIgnore, true);\n    });\n    function createFakeLinesDiv() {\n      const r = document.createElement('div');\n      r.className = 'diagonal-fill';\n      return r;\n    }\n    const alignmentViewZonesDisposables = this._register(new DisposableStore());\n    this.viewZones = derivedWithStore(this, (reader, store) => {\n      alignmentViewZonesDisposables.clear();\n      const alignmentsVal = alignments.read(reader) || [];\n      const origViewZones = [];\n      const modViewZones = [];\n      const modifiedTopPaddingVal = this._modifiedTopPadding.read(reader);\n      if (modifiedTopPaddingVal > 0) {\n        modViewZones.push({\n          afterLineNumber: 0,\n          domNode: document.createElement('div'),\n          heightInPx: modifiedTopPaddingVal,\n          showInHiddenAreas: true,\n          suppressMouseDown: true\n        });\n      }\n      const originalTopPaddingVal = this._originalTopPadding.read(reader);\n      if (originalTopPaddingVal > 0) {\n        origViewZones.push({\n          afterLineNumber: 0,\n          domNode: document.createElement('div'),\n          heightInPx: originalTopPaddingVal,\n          showInHiddenAreas: true,\n          suppressMouseDown: true\n        });\n      }\n      const renderSideBySide = this._options.renderSideBySide.read(reader);\n      const deletedCodeLineBreaksComputer = !renderSideBySide ? this._editors.modified._getViewModel()?.createLineBreaksComputer() : undefined;\n      if (deletedCodeLineBreaksComputer) {\n        const originalModel = this._editors.original.getModel();\n        for (const a of alignmentsVal) {\n          if (a.diff) {\n            for (let i = a.originalRange.startLineNumber; i < a.originalRange.endLineNumberExclusive; i++) {\n              // `i` can be out of bound when the diff has not been updated yet.\n              // In this case, we do an early return.\n              // TODO@hediet: Fix this by applying the edit directly to the diff model, so that the diff is always valid.\n              if (i > originalModel.getLineCount()) {\n                return {\n                  orig: origViewZones,\n                  mod: modViewZones\n                };\n              }\n              deletedCodeLineBreaksComputer?.addRequest(originalModel.getLineContent(i), null, null);\n            }\n          }\n        }\n      }\n      const lineBreakData = deletedCodeLineBreaksComputer?.finalize() ?? [];\n      let lineBreakDataIdx = 0;\n      const modLineHeight = this._editors.modified.getOption(67 /* EditorOption.lineHeight */);\n      const syncedMovedText = this._diffModel.read(reader)?.movedTextToCompare.read(reader);\n      const mightContainNonBasicASCII = this._editors.original.getModel()?.mightContainNonBasicASCII() ?? false;\n      const mightContainRTL = this._editors.original.getModel()?.mightContainRTL() ?? false;\n      const renderOptions = RenderOptions.fromEditor(this._editors.modified);\n      for (const a of alignmentsVal) {\n        if (a.diff && !renderSideBySide && (!this._options.useTrueInlineDiffRendering.read(reader) || !allowsTrueInlineDiffRendering(a.diff))) {\n          if (!a.originalRange.isEmpty) {\n            originalModelTokenizationCompleted.read(reader); // Update view-zones once tokenization completes\n            const deletedCodeDomNode = document.createElement('div');\n            deletedCodeDomNode.classList.add('view-lines', 'line-delete', 'monaco-mouse-cursor-text');\n            const originalModel = this._editors.original.getModel();\n            // `a.originalRange` can be out of bound when the diff has not been updated yet.\n            // In this case, we do an early return.\n            // TODO@hediet: Fix this by applying the edit directly to the diff model, so that the diff is always valid.\n            if (a.originalRange.endLineNumberExclusive - 1 > originalModel.getLineCount()) {\n              return {\n                orig: origViewZones,\n                mod: modViewZones\n              };\n            }\n            const source = new LineSource(a.originalRange.mapToLineArray(l => originalModel.tokenization.getLineTokens(l)), a.originalRange.mapToLineArray(_ => lineBreakData[lineBreakDataIdx++]), mightContainNonBasicASCII, mightContainRTL);\n            const decorations = [];\n            for (const i of a.diff.innerChanges || []) {\n              decorations.push(new InlineDecoration(i.originalRange.delta(-(a.diff.original.startLineNumber - 1)), diffDeleteDecoration.className, 0 /* InlineDecorationType.Regular */));\n            }\n            const result = renderLines(source, renderOptions, decorations, deletedCodeDomNode);\n            const marginDomNode = document.createElement('div');\n            marginDomNode.className = 'inline-deleted-margin-view-zone';\n            applyFontInfo(marginDomNode, renderOptions.fontInfo);\n            if (this._options.renderIndicators.read(reader)) {\n              for (let i = 0; i < result.heightInLines; i++) {\n                const marginElement = document.createElement('div');\n                marginElement.className = `delete-sign ${ThemeIcon.asClassName(diffRemoveIcon)}`;\n                marginElement.setAttribute('style', `position:absolute;top:${i * modLineHeight}px;width:${renderOptions.lineDecorationsWidth}px;height:${modLineHeight}px;right:0;`);\n                marginDomNode.appendChild(marginElement);\n              }\n            }\n            let zoneId = undefined;\n            alignmentViewZonesDisposables.add(new InlineDiffDeletedCodeMargin(() => assertIsDefined(zoneId), marginDomNode, this._editors.modified, a.diff, this._diffEditorWidget, result.viewLineCounts, this._editors.original.getModel(), this._contextMenuService, this._clipboardService));\n            for (let i = 0; i < result.viewLineCounts.length; i++) {\n              const count = result.viewLineCounts[i];\n              // Account for wrapped lines in the (collapsed) original editor (which doesn't wrap lines).\n              if (count > 1) {\n                origViewZones.push({\n                  afterLineNumber: a.originalRange.startLineNumber + i,\n                  domNode: createFakeLinesDiv(),\n                  heightInPx: (count - 1) * modLineHeight,\n                  showInHiddenAreas: true,\n                  suppressMouseDown: true\n                });\n              }\n            }\n            modViewZones.push({\n              afterLineNumber: a.modifiedRange.startLineNumber - 1,\n              domNode: deletedCodeDomNode,\n              heightInPx: result.heightInLines * modLineHeight,\n              minWidthInPx: result.minWidthInPx,\n              marginDomNode,\n              setZoneId(id) {\n                zoneId = id;\n              },\n              showInHiddenAreas: true,\n              suppressMouseDown: true\n            });\n          }\n          const marginDomNode = document.createElement('div');\n          marginDomNode.className = 'gutter-delete';\n          origViewZones.push({\n            afterLineNumber: a.originalRange.endLineNumberExclusive - 1,\n            domNode: createFakeLinesDiv(),\n            heightInPx: a.modifiedHeightInPx,\n            marginDomNode,\n            showInHiddenAreas: true,\n            suppressMouseDown: true\n          });\n        } else {\n          const delta = a.modifiedHeightInPx - a.originalHeightInPx;\n          if (delta > 0) {\n            if (syncedMovedText?.lineRangeMapping.original.delta(-1).deltaLength(2).contains(a.originalRange.endLineNumberExclusive - 1)) {\n              continue;\n            }\n            origViewZones.push({\n              afterLineNumber: a.originalRange.endLineNumberExclusive - 1,\n              domNode: createFakeLinesDiv(),\n              heightInPx: delta,\n              showInHiddenAreas: true,\n              suppressMouseDown: true\n            });\n          } else {\n            if (syncedMovedText?.lineRangeMapping.modified.delta(-1).deltaLength(2).contains(a.modifiedRange.endLineNumberExclusive - 1)) {\n              continue;\n            }\n            function createViewZoneMarginArrow() {\n              const arrow = document.createElement('div');\n              arrow.className = 'arrow-revert-change ' + ThemeIcon.asClassName(Codicon.arrowRight);\n              store.add(addDisposableListener(arrow, 'mousedown', e => e.stopPropagation()));\n              store.add(addDisposableListener(arrow, 'click', e => {\n                e.stopPropagation();\n                _diffEditorWidget.revert(a.diff);\n              }));\n              return $('div', {}, arrow);\n            }\n            let marginDomNode = undefined;\n            if (a.diff && a.diff.modified.isEmpty && this._options.shouldRenderOldRevertArrows.read(reader)) {\n              marginDomNode = createViewZoneMarginArrow();\n            }\n            modViewZones.push({\n              afterLineNumber: a.modifiedRange.endLineNumberExclusive - 1,\n              domNode: createFakeLinesDiv(),\n              heightInPx: -delta,\n              marginDomNode,\n              showInHiddenAreas: true,\n              suppressMouseDown: true\n            });\n          }\n        }\n      }\n      for (const a of alignmentsSyncedMovedText.read(reader) ?? []) {\n        if (!syncedMovedText?.lineRangeMapping.original.intersect(a.originalRange) || !syncedMovedText?.lineRangeMapping.modified.intersect(a.modifiedRange)) {\n          // ignore unrelated alignments outside the synced moved text\n          continue;\n        }\n        const delta = a.modifiedHeightInPx - a.originalHeightInPx;\n        if (delta > 0) {\n          origViewZones.push({\n            afterLineNumber: a.originalRange.endLineNumberExclusive - 1,\n            domNode: createFakeLinesDiv(),\n            heightInPx: delta,\n            showInHiddenAreas: true,\n            suppressMouseDown: true\n          });\n        } else {\n          modViewZones.push({\n            afterLineNumber: a.modifiedRange.endLineNumberExclusive - 1,\n            domNode: createFakeLinesDiv(),\n            heightInPx: -delta,\n            showInHiddenAreas: true,\n            suppressMouseDown: true\n          });\n        }\n      }\n      return {\n        orig: origViewZones,\n        mod: modViewZones\n      };\n    });\n    let ignoreChange = false;\n    this._register(this._editors.original.onDidScrollChange(e => {\n      if (e.scrollLeftChanged && !ignoreChange) {\n        ignoreChange = true;\n        this._editors.modified.setScrollLeft(e.scrollLeft);\n        ignoreChange = false;\n      }\n    }));\n    this._register(this._editors.modified.onDidScrollChange(e => {\n      if (e.scrollLeftChanged && !ignoreChange) {\n        ignoreChange = true;\n        this._editors.original.setScrollLeft(e.scrollLeft);\n        ignoreChange = false;\n      }\n    }));\n    this._originalScrollTop = observableFromEvent(this._editors.original.onDidScrollChange, () => /** @description original.getScrollTop */this._editors.original.getScrollTop());\n    this._modifiedScrollTop = observableFromEvent(this._editors.modified.onDidScrollChange, () => /** @description modified.getScrollTop */this._editors.modified.getScrollTop());\n    // origExtraHeight + origOffset - origScrollTop = modExtraHeight + modOffset - modScrollTop\n    // origScrollTop = origExtraHeight + origOffset - modExtraHeight - modOffset + modScrollTop\n    // modScrollTop = modExtraHeight + modOffset - origExtraHeight - origOffset + origScrollTop\n    // origOffset - modOffset = heightOfLines(1..Y) - heightOfLines(1..X)\n    // origScrollTop >= 0, modScrollTop >= 0\n    this._register(autorun(reader => {\n      /** @description update scroll modified */\n      const newScrollTopModified = this._originalScrollTop.read(reader) - (this._originalScrollOffsetAnimated.get() - this._modifiedScrollOffsetAnimated.read(reader)) - (this._originalTopPadding.get() - this._modifiedTopPadding.read(reader));\n      if (newScrollTopModified !== this._editors.modified.getScrollTop()) {\n        this._editors.modified.setScrollTop(newScrollTopModified, 1 /* ScrollType.Immediate */);\n      }\n    }));\n    this._register(autorun(reader => {\n      /** @description update scroll original */\n      const newScrollTopOriginal = this._modifiedScrollTop.read(reader) - (this._modifiedScrollOffsetAnimated.get() - this._originalScrollOffsetAnimated.read(reader)) - (this._modifiedTopPadding.get() - this._originalTopPadding.read(reader));\n      if (newScrollTopOriginal !== this._editors.original.getScrollTop()) {\n        this._editors.original.setScrollTop(newScrollTopOriginal, 1 /* ScrollType.Immediate */);\n      }\n    }));\n    this._register(autorun(reader => {\n      /** @description update editor top offsets */\n      const m = this._diffModel.read(reader)?.movedTextToCompare.read(reader);\n      let deltaOrigToMod = 0;\n      if (m) {\n        const trueTopOriginal = this._editors.original.getTopForLineNumber(m.lineRangeMapping.original.startLineNumber, true) - this._originalTopPadding.get();\n        const trueTopModified = this._editors.modified.getTopForLineNumber(m.lineRangeMapping.modified.startLineNumber, true) - this._modifiedTopPadding.get();\n        deltaOrigToMod = trueTopModified - trueTopOriginal;\n      }\n      if (deltaOrigToMod > 0) {\n        this._modifiedTopPadding.set(0, undefined);\n        this._originalTopPadding.set(deltaOrigToMod, undefined);\n      } else if (deltaOrigToMod < 0) {\n        this._modifiedTopPadding.set(-deltaOrigToMod, undefined);\n        this._originalTopPadding.set(0, undefined);\n      } else {\n        setTimeout(() => {\n          this._modifiedTopPadding.set(0, undefined);\n          this._originalTopPadding.set(0, undefined);\n        }, 400);\n      }\n      if (this._editors.modified.hasTextFocus()) {\n        this._originalScrollOffset.set(this._modifiedScrollOffset.get() - deltaOrigToMod, undefined, true);\n      } else {\n        this._modifiedScrollOffset.set(this._originalScrollOffset.get() + deltaOrigToMod, undefined, true);\n      }\n    }));\n  }\n};\nDiffEditorViewZones = __decorate([__param(8, IClipboardService), __param(9, IContextMenuService)], DiffEditorViewZones);\nexport { DiffEditorViewZones };\nfunction computeRangeAlignment(originalEditor, modifiedEditor, diffs, originalEditorAlignmentViewZones, modifiedEditorAlignmentViewZones, innerHunkAlignment) {\n  const originalLineHeightOverrides = new ArrayQueue(getAdditionalLineHeights(originalEditor, originalEditorAlignmentViewZones));\n  const modifiedLineHeightOverrides = new ArrayQueue(getAdditionalLineHeights(modifiedEditor, modifiedEditorAlignmentViewZones));\n  const origLineHeight = originalEditor.getOption(67 /* EditorOption.lineHeight */);\n  const modLineHeight = modifiedEditor.getOption(67 /* EditorOption.lineHeight */);\n  const result = [];\n  let lastOriginalLineNumber = 0;\n  let lastModifiedLineNumber = 0;\n  function handleAlignmentsOutsideOfDiffs(untilOriginalLineNumberExclusive, untilModifiedLineNumberExclusive) {\n    while (true) {\n      let origNext = originalLineHeightOverrides.peek();\n      let modNext = modifiedLineHeightOverrides.peek();\n      if (origNext && origNext.lineNumber >= untilOriginalLineNumberExclusive) {\n        origNext = undefined;\n      }\n      if (modNext && modNext.lineNumber >= untilModifiedLineNumberExclusive) {\n        modNext = undefined;\n      }\n      if (!origNext && !modNext) {\n        break;\n      }\n      const distOrig = origNext ? origNext.lineNumber - lastOriginalLineNumber : Number.MAX_VALUE;\n      const distNext = modNext ? modNext.lineNumber - lastModifiedLineNumber : Number.MAX_VALUE;\n      if (distOrig < distNext) {\n        originalLineHeightOverrides.dequeue();\n        modNext = {\n          lineNumber: origNext.lineNumber - lastOriginalLineNumber + lastModifiedLineNumber,\n          heightInPx: 0\n        };\n      } else if (distOrig > distNext) {\n        modifiedLineHeightOverrides.dequeue();\n        origNext = {\n          lineNumber: modNext.lineNumber - lastModifiedLineNumber + lastOriginalLineNumber,\n          heightInPx: 0\n        };\n      } else {\n        originalLineHeightOverrides.dequeue();\n        modifiedLineHeightOverrides.dequeue();\n      }\n      result.push({\n        originalRange: LineRange.ofLength(origNext.lineNumber, 1),\n        modifiedRange: LineRange.ofLength(modNext.lineNumber, 1),\n        originalHeightInPx: origLineHeight + origNext.heightInPx,\n        modifiedHeightInPx: modLineHeight + modNext.heightInPx,\n        diff: undefined\n      });\n    }\n  }\n  for (const m of diffs) {\n    const c = m.lineRangeMapping;\n    handleAlignmentsOutsideOfDiffs(c.original.startLineNumber, c.modified.startLineNumber);\n    let first = true;\n    let lastModLineNumber = c.modified.startLineNumber;\n    let lastOrigLineNumber = c.original.startLineNumber;\n    function emitAlignment(origLineNumberExclusive, modLineNumberExclusive, forceAlignment = false) {\n      if (origLineNumberExclusive < lastOrigLineNumber || modLineNumberExclusive < lastModLineNumber) {\n        return;\n      }\n      if (first) {\n        first = false;\n      } else if (!forceAlignment && (origLineNumberExclusive === lastOrigLineNumber || modLineNumberExclusive === lastModLineNumber)) {\n        // This causes a re-alignment of an already aligned line.\n        // However, we don't care for the final alignment.\n        return;\n      }\n      const originalRange = new LineRange(lastOrigLineNumber, origLineNumberExclusive);\n      const modifiedRange = new LineRange(lastModLineNumber, modLineNumberExclusive);\n      if (originalRange.isEmpty && modifiedRange.isEmpty) {\n        return;\n      }\n      const originalAdditionalHeight = originalLineHeightOverrides.takeWhile(v => v.lineNumber < origLineNumberExclusive)?.reduce((p, c) => p + c.heightInPx, 0) ?? 0;\n      const modifiedAdditionalHeight = modifiedLineHeightOverrides.takeWhile(v => v.lineNumber < modLineNumberExclusive)?.reduce((p, c) => p + c.heightInPx, 0) ?? 0;\n      result.push({\n        originalRange,\n        modifiedRange,\n        originalHeightInPx: originalRange.length * origLineHeight + originalAdditionalHeight,\n        modifiedHeightInPx: modifiedRange.length * modLineHeight + modifiedAdditionalHeight,\n        diff: m.lineRangeMapping\n      });\n      lastOrigLineNumber = origLineNumberExclusive;\n      lastModLineNumber = modLineNumberExclusive;\n    }\n    if (innerHunkAlignment) {\n      for (const i of c.innerChanges || []) {\n        if (i.originalRange.startColumn > 1 && i.modifiedRange.startColumn > 1) {\n          // There is some unmodified text on this line before the diff\n          emitAlignment(i.originalRange.startLineNumber, i.modifiedRange.startLineNumber);\n        }\n        const originalModel = originalEditor.getModel();\n        // When the diff is invalid, the ranges might be out of bounds (this should be fixed in the diff model by applying edits directly).\n        const maxColumn = i.originalRange.endLineNumber <= originalModel.getLineCount() ? originalModel.getLineMaxColumn(i.originalRange.endLineNumber) : Number.MAX_SAFE_INTEGER;\n        if (i.originalRange.endColumn < maxColumn) {\n          // // There is some unmodified text on this line after the diff\n          emitAlignment(i.originalRange.endLineNumber, i.modifiedRange.endLineNumber);\n        }\n      }\n    }\n    emitAlignment(c.original.endLineNumberExclusive, c.modified.endLineNumberExclusive, true);\n    lastOriginalLineNumber = c.original.endLineNumberExclusive;\n    lastModifiedLineNumber = c.modified.endLineNumberExclusive;\n  }\n  handleAlignmentsOutsideOfDiffs(Number.MAX_VALUE, Number.MAX_VALUE);\n  return result;\n}\nfunction getAdditionalLineHeights(editor, viewZonesToIgnore) {\n  const viewZoneHeights = [];\n  const wrappingZoneHeights = [];\n  const hasWrapping = editor.getOption(147 /* EditorOption.wrappingInfo */).wrappingColumn !== -1;\n  const coordinatesConverter = editor._getViewModel().coordinatesConverter;\n  const editorLineHeight = editor.getOption(67 /* EditorOption.lineHeight */);\n  if (hasWrapping) {\n    for (let i = 1; i <= editor.getModel().getLineCount(); i++) {\n      const lineCount = coordinatesConverter.getModelLineViewLineCount(i);\n      if (lineCount > 1) {\n        wrappingZoneHeights.push({\n          lineNumber: i,\n          heightInPx: editorLineHeight * (lineCount - 1)\n        });\n      }\n    }\n  }\n  for (const w of editor.getWhitespaces()) {\n    if (viewZonesToIgnore.has(w.id)) {\n      continue;\n    }\n    const modelLineNumber = w.afterLineNumber === 0 ? 0 : coordinatesConverter.convertViewPositionToModelPosition(new Position(w.afterLineNumber, 1)).lineNumber;\n    viewZoneHeights.push({\n      lineNumber: modelLineNumber,\n      heightInPx: w.height\n    });\n  }\n  const result = joinCombine(viewZoneHeights, wrappingZoneHeights, v => v.lineNumber, (v1, v2) => ({\n    lineNumber: v1.lineNumber,\n    heightInPx: v1.heightInPx + v2.heightInPx\n  }));\n  return result;\n}\nexport function allowsTrueInlineDiffRendering(mapping) {\n  if (!mapping.innerChanges) {\n    return false;\n  }\n  return mapping.innerChanges.every(c => rangeIsSingleLine(c.modifiedRange) && rangeIsSingleLine(c.originalRange) || c.originalRange.equalsRange(new Range(1, 1, 1, 1)));\n}\nfunction rangeIsSingleLine(range) {\n  return range.startLineNumber === range.endLineNumber;\n}", "map": {"version": 3, "names": ["__decorate", "decorators", "target", "key", "desc", "c", "arguments", "length", "r", "Object", "getOwnPropertyDescriptor", "d", "Reflect", "decorate", "i", "defineProperty", "__param", "paramIndex", "decorator", "$", "addDisposableListener", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "RunOnceScheduler", "Codicon", "Disposable", "DisposableStore", "autorun", "derived", "derivedWithStore", "observableFromEvent", "observableValue", "ThemeIcon", "assertIsDefined", "applyFontInfo", "diffDeleteDecoration", "diffRemoveIcon", "DiffMapping", "InlineDiffDeletedCodeMargin", "LineSource", "RenderOptions", "renderLines", "animatedObservable", "joinCombine", "LineRange", "Position", "InlineDecoration", "IClipboardService", "IContextMenuService", "Range", "DiffEditorViewZones", "constructor", "_targetWindow", "_editors", "_diffModel", "_options", "_diffEditorWidget", "_canIgnoreViewZoneUpdateEvent", "_origViewZonesToIgnore", "_modViewZonesToIgnore", "_clipboardService", "_contextMenuService", "_originalTopPadding", "_originalScrollOffset", "_originalScrollOffsetAnimated", "_store", "_modifiedTopPadding", "_modifiedScrollOffset", "_modifiedScrollOffsetAnimated", "state", "updateImmediately", "_register", "set", "get", "undefined", "original", "onDidChangeViewZones", "_args", "schedule", "modified", "onDidChangeConfiguration", "args", "has<PERSON><PERSON>ed", "originalModelTokenizationCompleted", "map", "m", "model", "onDidChangeTokens", "tokenization", "backgroundTokenizationState", "reader", "read", "alignments", "diffModel", "diff", "renderSideBySide", "innerHunkAlignment", "computeRangeAlignment", "mappings", "alignmentsSyncedMovedText", "syncedMovedText", "movedTextToCompare", "changes", "createFakeLinesDiv", "document", "createElement", "className", "alignmentViewZonesDisposables", "viewZones", "store", "clear", "alignmentsVal", "origViewZones", "modViewZones", "modifiedTopPaddingVal", "push", "afterLineNumber", "domNode", "heightInPx", "showInHiddenAreas", "suppressMouseDown", "originalTopPaddingVal", "deletedCodeLineBreaksComputer", "_getViewModel", "createLineBreaksComputer", "originalModel", "getModel", "a", "originalRange", "startLineNumber", "endLineNumberExclusive", "getLineCount", "orig", "mod", "addRequest", "get<PERSON>ineC<PERSON>nt", "lineBreakData", "finalize", "lineBreakDataIdx", "modLineHeight", "getOption", "mightContainNonBasicASCII", "mightContainRTL", "renderOptions", "fromEditor", "useTrueInlineDiffRendering", "allowsTrueInlineDiffRendering", "isEmpty", "deletedCodeDomNode", "classList", "add", "source", "mapToLineArray", "l", "getLineTokens", "_", "decorations", "innerChanges", "delta", "result", "marginDomNode", "fontInfo", "renderIndicators", "heightInLines", "marginElement", "asClassName", "setAttribute", "lineDecorationsWidth", "append<PERSON><PERSON><PERSON>", "zoneId", "viewLineCounts", "count", "modifiedRange", "minWidthInPx", "setZoneId", "id", "modifiedHeightInPx", "originalHeightInPx", "lineRangeMapping", "deltaLength", "contains", "createViewZoneMarginArrow", "arrow", "arrowRight", "e", "stopPropagation", "revert", "shouldRenderOldRevertArrows", "intersect", "ignoreChange", "onDidScrollChange", "scrollLeftChanged", "setScrollLeft", "scrollLeft", "_originalScrollTop", "getScrollTop", "_modifiedScrollTop", "newScrollTopModified", "setScrollTop", "newScrollTopOriginal", "deltaOrigToMod", "trueTopOriginal", "getTopForLineNumber", "trueTopModified", "setTimeout", "hasTextFocus", "originalEditor", "modifiedEditor", "diffs", "originalEditorAlignmentViewZones", "modifiedEditorAlignmentViewZones", "originalLineHeightOverrides", "getAdditionalLineHeights", "modifiedLineHeightOverrides", "origLineHeight", "lastOriginalLineNumber", "lastModifiedLineNumber", "handleAlignmentsOutsideOfDiffs", "untilOriginalLineNumberExclusive", "untilModifiedLineNumberExclusive", "origNext", "peek", "modNext", "lineNumber", "distOrig", "Number", "MAX_VALUE", "distNext", "dequeue", "of<PERSON>ength", "first", "lastModLineNumber", "lastOrigLineNumber", "emitAlignment", "origLineNumberExclusive", "modLineNumberExclusive", "forceAlignment", "originalAdditionalHeight", "<PERSON><PERSON><PERSON><PERSON>", "v", "reduce", "p", "modifiedAdditionalHeight", "startColumn", "maxColumn", "endLineNumber", "getLineMaxColumn", "MAX_SAFE_INTEGER", "endColumn", "editor", "viewZonesToIgnore", "viewZoneHeights", "wrappingZoneHeights", "hasWrapping", "wrappingColumn", "coordinatesConverter", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lineCount", "getModelLineViewLineCount", "w", "getWhitespaces", "has", "modelLineNumber", "convertViewPositionToModelPosition", "height", "v1", "v2", "mapping", "every", "rangeIsSingleLine", "equalsRange", "range"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/browser/widget/diffEditor/components/diffEditorViewZones/diffEditorViewZones.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = (this && this.__param) || function (paramIndex, decorator) {\n    return function (target, key) { decorator(target, key, paramIndex); }\n};\nimport { $, addDisposableListener } from '../../../../../../base/browser/dom.js';\nimport { ArrayQueue } from '../../../../../../base/common/arrays.js';\nimport { RunOnceScheduler } from '../../../../../../base/common/async.js';\nimport { Codicon } from '../../../../../../base/common/codicons.js';\nimport { Disposable, DisposableStore } from '../../../../../../base/common/lifecycle.js';\nimport { autorun, derived, derivedWithStore, observableFromEvent, observableValue } from '../../../../../../base/common/observable.js';\nimport { ThemeIcon } from '../../../../../../base/common/themables.js';\nimport { assertIsDefined } from '../../../../../../base/common/types.js';\nimport { applyFontInfo } from '../../../../config/domFontInfo.js';\nimport { diffDeleteDecoration, diffRemoveIcon } from '../../registrations.contribution.js';\nimport { DiffMapping } from '../../diffEditorViewModel.js';\nimport { InlineDiffDeletedCodeMargin } from './inlineDiffDeletedCodeMargin.js';\nimport { LineSource, RenderOptions, renderLines } from './renderLines.js';\nimport { animatedObservable, joinCombine } from '../../utils.js';\nimport { LineRange } from '../../../../../common/core/lineRange.js';\nimport { Position } from '../../../../../common/core/position.js';\nimport { InlineDecoration } from '../../../../../common/viewModel.js';\nimport { IClipboardService } from '../../../../../../platform/clipboard/common/clipboardService.js';\nimport { IContextMenuService } from '../../../../../../platform/contextview/browser/contextView.js';\nimport { Range } from '../../../../../common/core/range.js';\n/**\n * Ensures both editors have the same height by aligning unchanged lines.\n * In inline view mode, inserts viewzones to show deleted code from the original text model in the modified code editor.\n * Synchronizes scrolling.\n *\n * Make sure to add the view zones!\n */\nlet DiffEditorViewZones = class DiffEditorViewZones extends Disposable {\n    constructor(_targetWindow, _editors, _diffModel, _options, _diffEditorWidget, _canIgnoreViewZoneUpdateEvent, _origViewZonesToIgnore, _modViewZonesToIgnore, _clipboardService, _contextMenuService) {\n        super();\n        this._targetWindow = _targetWindow;\n        this._editors = _editors;\n        this._diffModel = _diffModel;\n        this._options = _options;\n        this._diffEditorWidget = _diffEditorWidget;\n        this._canIgnoreViewZoneUpdateEvent = _canIgnoreViewZoneUpdateEvent;\n        this._origViewZonesToIgnore = _origViewZonesToIgnore;\n        this._modViewZonesToIgnore = _modViewZonesToIgnore;\n        this._clipboardService = _clipboardService;\n        this._contextMenuService = _contextMenuService;\n        this._originalTopPadding = observableValue(this, 0);\n        this._originalScrollOffset = observableValue(this, 0);\n        this._originalScrollOffsetAnimated = animatedObservable(this._targetWindow, this._originalScrollOffset, this._store);\n        this._modifiedTopPadding = observableValue(this, 0);\n        this._modifiedScrollOffset = observableValue(this, 0);\n        this._modifiedScrollOffsetAnimated = animatedObservable(this._targetWindow, this._modifiedScrollOffset, this._store);\n        const state = observableValue('invalidateAlignmentsState', 0);\n        const updateImmediately = this._register(new RunOnceScheduler(() => {\n            state.set(state.get() + 1, undefined);\n        }, 0));\n        this._register(this._editors.original.onDidChangeViewZones((_args) => { if (!this._canIgnoreViewZoneUpdateEvent()) {\n            updateImmediately.schedule();\n        } }));\n        this._register(this._editors.modified.onDidChangeViewZones((_args) => { if (!this._canIgnoreViewZoneUpdateEvent()) {\n            updateImmediately.schedule();\n        } }));\n        this._register(this._editors.original.onDidChangeConfiguration((args) => {\n            if (args.hasChanged(147 /* EditorOption.wrappingInfo */) || args.hasChanged(67 /* EditorOption.lineHeight */)) {\n                updateImmediately.schedule();\n            }\n        }));\n        this._register(this._editors.modified.onDidChangeConfiguration((args) => {\n            if (args.hasChanged(147 /* EditorOption.wrappingInfo */) || args.hasChanged(67 /* EditorOption.lineHeight */)) {\n                updateImmediately.schedule();\n            }\n        }));\n        const originalModelTokenizationCompleted = this._diffModel.map(m => m ? observableFromEvent(this, m.model.original.onDidChangeTokens, () => m.model.original.tokenization.backgroundTokenizationState === 2 /* BackgroundTokenizationState.Completed */) : undefined).map((m, reader) => m?.read(reader));\n        const alignments = derived((reader) => {\n            /** @description alignments */\n            const diffModel = this._diffModel.read(reader);\n            const diff = diffModel?.diff.read(reader);\n            if (!diffModel || !diff) {\n                return null;\n            }\n            state.read(reader);\n            const renderSideBySide = this._options.renderSideBySide.read(reader);\n            const innerHunkAlignment = renderSideBySide;\n            return computeRangeAlignment(this._editors.original, this._editors.modified, diff.mappings, this._origViewZonesToIgnore, this._modViewZonesToIgnore, innerHunkAlignment);\n        });\n        const alignmentsSyncedMovedText = derived((reader) => {\n            /** @description alignmentsSyncedMovedText */\n            const syncedMovedText = this._diffModel.read(reader)?.movedTextToCompare.read(reader);\n            if (!syncedMovedText) {\n                return null;\n            }\n            state.read(reader);\n            const mappings = syncedMovedText.changes.map(c => new DiffMapping(c));\n            // TODO dont include alignments outside syncedMovedText\n            return computeRangeAlignment(this._editors.original, this._editors.modified, mappings, this._origViewZonesToIgnore, this._modViewZonesToIgnore, true);\n        });\n        function createFakeLinesDiv() {\n            const r = document.createElement('div');\n            r.className = 'diagonal-fill';\n            return r;\n        }\n        const alignmentViewZonesDisposables = this._register(new DisposableStore());\n        this.viewZones = derivedWithStore(this, (reader, store) => {\n            alignmentViewZonesDisposables.clear();\n            const alignmentsVal = alignments.read(reader) || [];\n            const origViewZones = [];\n            const modViewZones = [];\n            const modifiedTopPaddingVal = this._modifiedTopPadding.read(reader);\n            if (modifiedTopPaddingVal > 0) {\n                modViewZones.push({\n                    afterLineNumber: 0,\n                    domNode: document.createElement('div'),\n                    heightInPx: modifiedTopPaddingVal,\n                    showInHiddenAreas: true,\n                    suppressMouseDown: true,\n                });\n            }\n            const originalTopPaddingVal = this._originalTopPadding.read(reader);\n            if (originalTopPaddingVal > 0) {\n                origViewZones.push({\n                    afterLineNumber: 0,\n                    domNode: document.createElement('div'),\n                    heightInPx: originalTopPaddingVal,\n                    showInHiddenAreas: true,\n                    suppressMouseDown: true,\n                });\n            }\n            const renderSideBySide = this._options.renderSideBySide.read(reader);\n            const deletedCodeLineBreaksComputer = !renderSideBySide ? this._editors.modified._getViewModel()?.createLineBreaksComputer() : undefined;\n            if (deletedCodeLineBreaksComputer) {\n                const originalModel = this._editors.original.getModel();\n                for (const a of alignmentsVal) {\n                    if (a.diff) {\n                        for (let i = a.originalRange.startLineNumber; i < a.originalRange.endLineNumberExclusive; i++) {\n                            // `i` can be out of bound when the diff has not been updated yet.\n                            // In this case, we do an early return.\n                            // TODO@hediet: Fix this by applying the edit directly to the diff model, so that the diff is always valid.\n                            if (i > originalModel.getLineCount()) {\n                                return { orig: origViewZones, mod: modViewZones };\n                            }\n                            deletedCodeLineBreaksComputer?.addRequest(originalModel.getLineContent(i), null, null);\n                        }\n                    }\n                }\n            }\n            const lineBreakData = deletedCodeLineBreaksComputer?.finalize() ?? [];\n            let lineBreakDataIdx = 0;\n            const modLineHeight = this._editors.modified.getOption(67 /* EditorOption.lineHeight */);\n            const syncedMovedText = this._diffModel.read(reader)?.movedTextToCompare.read(reader);\n            const mightContainNonBasicASCII = this._editors.original.getModel()?.mightContainNonBasicASCII() ?? false;\n            const mightContainRTL = this._editors.original.getModel()?.mightContainRTL() ?? false;\n            const renderOptions = RenderOptions.fromEditor(this._editors.modified);\n            for (const a of alignmentsVal) {\n                if (a.diff && !renderSideBySide && (!this._options.useTrueInlineDiffRendering.read(reader) || !allowsTrueInlineDiffRendering(a.diff))) {\n                    if (!a.originalRange.isEmpty) {\n                        originalModelTokenizationCompleted.read(reader); // Update view-zones once tokenization completes\n                        const deletedCodeDomNode = document.createElement('div');\n                        deletedCodeDomNode.classList.add('view-lines', 'line-delete', 'monaco-mouse-cursor-text');\n                        const originalModel = this._editors.original.getModel();\n                        // `a.originalRange` can be out of bound when the diff has not been updated yet.\n                        // In this case, we do an early return.\n                        // TODO@hediet: Fix this by applying the edit directly to the diff model, so that the diff is always valid.\n                        if (a.originalRange.endLineNumberExclusive - 1 > originalModel.getLineCount()) {\n                            return { orig: origViewZones, mod: modViewZones };\n                        }\n                        const source = new LineSource(a.originalRange.mapToLineArray(l => originalModel.tokenization.getLineTokens(l)), a.originalRange.mapToLineArray(_ => lineBreakData[lineBreakDataIdx++]), mightContainNonBasicASCII, mightContainRTL);\n                        const decorations = [];\n                        for (const i of a.diff.innerChanges || []) {\n                            decorations.push(new InlineDecoration(i.originalRange.delta(-(a.diff.original.startLineNumber - 1)), diffDeleteDecoration.className, 0 /* InlineDecorationType.Regular */));\n                        }\n                        const result = renderLines(source, renderOptions, decorations, deletedCodeDomNode);\n                        const marginDomNode = document.createElement('div');\n                        marginDomNode.className = 'inline-deleted-margin-view-zone';\n                        applyFontInfo(marginDomNode, renderOptions.fontInfo);\n                        if (this._options.renderIndicators.read(reader)) {\n                            for (let i = 0; i < result.heightInLines; i++) {\n                                const marginElement = document.createElement('div');\n                                marginElement.className = `delete-sign ${ThemeIcon.asClassName(diffRemoveIcon)}`;\n                                marginElement.setAttribute('style', `position:absolute;top:${i * modLineHeight}px;width:${renderOptions.lineDecorationsWidth}px;height:${modLineHeight}px;right:0;`);\n                                marginDomNode.appendChild(marginElement);\n                            }\n                        }\n                        let zoneId = undefined;\n                        alignmentViewZonesDisposables.add(new InlineDiffDeletedCodeMargin(() => assertIsDefined(zoneId), marginDomNode, this._editors.modified, a.diff, this._diffEditorWidget, result.viewLineCounts, this._editors.original.getModel(), this._contextMenuService, this._clipboardService));\n                        for (let i = 0; i < result.viewLineCounts.length; i++) {\n                            const count = result.viewLineCounts[i];\n                            // Account for wrapped lines in the (collapsed) original editor (which doesn't wrap lines).\n                            if (count > 1) {\n                                origViewZones.push({\n                                    afterLineNumber: a.originalRange.startLineNumber + i,\n                                    domNode: createFakeLinesDiv(),\n                                    heightInPx: (count - 1) * modLineHeight,\n                                    showInHiddenAreas: true,\n                                    suppressMouseDown: true,\n                                });\n                            }\n                        }\n                        modViewZones.push({\n                            afterLineNumber: a.modifiedRange.startLineNumber - 1,\n                            domNode: deletedCodeDomNode,\n                            heightInPx: result.heightInLines * modLineHeight,\n                            minWidthInPx: result.minWidthInPx,\n                            marginDomNode,\n                            setZoneId(id) { zoneId = id; },\n                            showInHiddenAreas: true,\n                            suppressMouseDown: true,\n                        });\n                    }\n                    const marginDomNode = document.createElement('div');\n                    marginDomNode.className = 'gutter-delete';\n                    origViewZones.push({\n                        afterLineNumber: a.originalRange.endLineNumberExclusive - 1,\n                        domNode: createFakeLinesDiv(),\n                        heightInPx: a.modifiedHeightInPx,\n                        marginDomNode,\n                        showInHiddenAreas: true,\n                        suppressMouseDown: true,\n                    });\n                }\n                else {\n                    const delta = a.modifiedHeightInPx - a.originalHeightInPx;\n                    if (delta > 0) {\n                        if (syncedMovedText?.lineRangeMapping.original.delta(-1).deltaLength(2).contains(a.originalRange.endLineNumberExclusive - 1)) {\n                            continue;\n                        }\n                        origViewZones.push({\n                            afterLineNumber: a.originalRange.endLineNumberExclusive - 1,\n                            domNode: createFakeLinesDiv(),\n                            heightInPx: delta,\n                            showInHiddenAreas: true,\n                            suppressMouseDown: true,\n                        });\n                    }\n                    else {\n                        if (syncedMovedText?.lineRangeMapping.modified.delta(-1).deltaLength(2).contains(a.modifiedRange.endLineNumberExclusive - 1)) {\n                            continue;\n                        }\n                        function createViewZoneMarginArrow() {\n                            const arrow = document.createElement('div');\n                            arrow.className = 'arrow-revert-change ' + ThemeIcon.asClassName(Codicon.arrowRight);\n                            store.add(addDisposableListener(arrow, 'mousedown', e => e.stopPropagation()));\n                            store.add(addDisposableListener(arrow, 'click', e => {\n                                e.stopPropagation();\n                                _diffEditorWidget.revert(a.diff);\n                            }));\n                            return $('div', {}, arrow);\n                        }\n                        let marginDomNode = undefined;\n                        if (a.diff && a.diff.modified.isEmpty && this._options.shouldRenderOldRevertArrows.read(reader)) {\n                            marginDomNode = createViewZoneMarginArrow();\n                        }\n                        modViewZones.push({\n                            afterLineNumber: a.modifiedRange.endLineNumberExclusive - 1,\n                            domNode: createFakeLinesDiv(),\n                            heightInPx: -delta,\n                            marginDomNode,\n                            showInHiddenAreas: true,\n                            suppressMouseDown: true,\n                        });\n                    }\n                }\n            }\n            for (const a of alignmentsSyncedMovedText.read(reader) ?? []) {\n                if (!syncedMovedText?.lineRangeMapping.original.intersect(a.originalRange)\n                    || !syncedMovedText?.lineRangeMapping.modified.intersect(a.modifiedRange)) {\n                    // ignore unrelated alignments outside the synced moved text\n                    continue;\n                }\n                const delta = a.modifiedHeightInPx - a.originalHeightInPx;\n                if (delta > 0) {\n                    origViewZones.push({\n                        afterLineNumber: a.originalRange.endLineNumberExclusive - 1,\n                        domNode: createFakeLinesDiv(),\n                        heightInPx: delta,\n                        showInHiddenAreas: true,\n                        suppressMouseDown: true,\n                    });\n                }\n                else {\n                    modViewZones.push({\n                        afterLineNumber: a.modifiedRange.endLineNumberExclusive - 1,\n                        domNode: createFakeLinesDiv(),\n                        heightInPx: -delta,\n                        showInHiddenAreas: true,\n                        suppressMouseDown: true,\n                    });\n                }\n            }\n            return { orig: origViewZones, mod: modViewZones };\n        });\n        let ignoreChange = false;\n        this._register(this._editors.original.onDidScrollChange(e => {\n            if (e.scrollLeftChanged && !ignoreChange) {\n                ignoreChange = true;\n                this._editors.modified.setScrollLeft(e.scrollLeft);\n                ignoreChange = false;\n            }\n        }));\n        this._register(this._editors.modified.onDidScrollChange(e => {\n            if (e.scrollLeftChanged && !ignoreChange) {\n                ignoreChange = true;\n                this._editors.original.setScrollLeft(e.scrollLeft);\n                ignoreChange = false;\n            }\n        }));\n        this._originalScrollTop = observableFromEvent(this._editors.original.onDidScrollChange, () => /** @description original.getScrollTop */ this._editors.original.getScrollTop());\n        this._modifiedScrollTop = observableFromEvent(this._editors.modified.onDidScrollChange, () => /** @description modified.getScrollTop */ this._editors.modified.getScrollTop());\n        // origExtraHeight + origOffset - origScrollTop = modExtraHeight + modOffset - modScrollTop\n        // origScrollTop = origExtraHeight + origOffset - modExtraHeight - modOffset + modScrollTop\n        // modScrollTop = modExtraHeight + modOffset - origExtraHeight - origOffset + origScrollTop\n        // origOffset - modOffset = heightOfLines(1..Y) - heightOfLines(1..X)\n        // origScrollTop >= 0, modScrollTop >= 0\n        this._register(autorun(reader => {\n            /** @description update scroll modified */\n            const newScrollTopModified = this._originalScrollTop.read(reader)\n                - (this._originalScrollOffsetAnimated.get() - this._modifiedScrollOffsetAnimated.read(reader))\n                - (this._originalTopPadding.get() - this._modifiedTopPadding.read(reader));\n            if (newScrollTopModified !== this._editors.modified.getScrollTop()) {\n                this._editors.modified.setScrollTop(newScrollTopModified, 1 /* ScrollType.Immediate */);\n            }\n        }));\n        this._register(autorun(reader => {\n            /** @description update scroll original */\n            const newScrollTopOriginal = this._modifiedScrollTop.read(reader)\n                - (this._modifiedScrollOffsetAnimated.get() - this._originalScrollOffsetAnimated.read(reader))\n                - (this._modifiedTopPadding.get() - this._originalTopPadding.read(reader));\n            if (newScrollTopOriginal !== this._editors.original.getScrollTop()) {\n                this._editors.original.setScrollTop(newScrollTopOriginal, 1 /* ScrollType.Immediate */);\n            }\n        }));\n        this._register(autorun(reader => {\n            /** @description update editor top offsets */\n            const m = this._diffModel.read(reader)?.movedTextToCompare.read(reader);\n            let deltaOrigToMod = 0;\n            if (m) {\n                const trueTopOriginal = this._editors.original.getTopForLineNumber(m.lineRangeMapping.original.startLineNumber, true) - this._originalTopPadding.get();\n                const trueTopModified = this._editors.modified.getTopForLineNumber(m.lineRangeMapping.modified.startLineNumber, true) - this._modifiedTopPadding.get();\n                deltaOrigToMod = trueTopModified - trueTopOriginal;\n            }\n            if (deltaOrigToMod > 0) {\n                this._modifiedTopPadding.set(0, undefined);\n                this._originalTopPadding.set(deltaOrigToMod, undefined);\n            }\n            else if (deltaOrigToMod < 0) {\n                this._modifiedTopPadding.set(-deltaOrigToMod, undefined);\n                this._originalTopPadding.set(0, undefined);\n            }\n            else {\n                setTimeout(() => {\n                    this._modifiedTopPadding.set(0, undefined);\n                    this._originalTopPadding.set(0, undefined);\n                }, 400);\n            }\n            if (this._editors.modified.hasTextFocus()) {\n                this._originalScrollOffset.set(this._modifiedScrollOffset.get() - deltaOrigToMod, undefined, true);\n            }\n            else {\n                this._modifiedScrollOffset.set(this._originalScrollOffset.get() + deltaOrigToMod, undefined, true);\n            }\n        }));\n    }\n};\nDiffEditorViewZones = __decorate([\n    __param(8, IClipboardService),\n    __param(9, IContextMenuService)\n], DiffEditorViewZones);\nexport { DiffEditorViewZones };\nfunction computeRangeAlignment(originalEditor, modifiedEditor, diffs, originalEditorAlignmentViewZones, modifiedEditorAlignmentViewZones, innerHunkAlignment) {\n    const originalLineHeightOverrides = new ArrayQueue(getAdditionalLineHeights(originalEditor, originalEditorAlignmentViewZones));\n    const modifiedLineHeightOverrides = new ArrayQueue(getAdditionalLineHeights(modifiedEditor, modifiedEditorAlignmentViewZones));\n    const origLineHeight = originalEditor.getOption(67 /* EditorOption.lineHeight */);\n    const modLineHeight = modifiedEditor.getOption(67 /* EditorOption.lineHeight */);\n    const result = [];\n    let lastOriginalLineNumber = 0;\n    let lastModifiedLineNumber = 0;\n    function handleAlignmentsOutsideOfDiffs(untilOriginalLineNumberExclusive, untilModifiedLineNumberExclusive) {\n        while (true) {\n            let origNext = originalLineHeightOverrides.peek();\n            let modNext = modifiedLineHeightOverrides.peek();\n            if (origNext && origNext.lineNumber >= untilOriginalLineNumberExclusive) {\n                origNext = undefined;\n            }\n            if (modNext && modNext.lineNumber >= untilModifiedLineNumberExclusive) {\n                modNext = undefined;\n            }\n            if (!origNext && !modNext) {\n                break;\n            }\n            const distOrig = origNext ? origNext.lineNumber - lastOriginalLineNumber : Number.MAX_VALUE;\n            const distNext = modNext ? modNext.lineNumber - lastModifiedLineNumber : Number.MAX_VALUE;\n            if (distOrig < distNext) {\n                originalLineHeightOverrides.dequeue();\n                modNext = {\n                    lineNumber: origNext.lineNumber - lastOriginalLineNumber + lastModifiedLineNumber,\n                    heightInPx: 0,\n                };\n            }\n            else if (distOrig > distNext) {\n                modifiedLineHeightOverrides.dequeue();\n                origNext = {\n                    lineNumber: modNext.lineNumber - lastModifiedLineNumber + lastOriginalLineNumber,\n                    heightInPx: 0,\n                };\n            }\n            else {\n                originalLineHeightOverrides.dequeue();\n                modifiedLineHeightOverrides.dequeue();\n            }\n            result.push({\n                originalRange: LineRange.ofLength(origNext.lineNumber, 1),\n                modifiedRange: LineRange.ofLength(modNext.lineNumber, 1),\n                originalHeightInPx: origLineHeight + origNext.heightInPx,\n                modifiedHeightInPx: modLineHeight + modNext.heightInPx,\n                diff: undefined,\n            });\n        }\n    }\n    for (const m of diffs) {\n        const c = m.lineRangeMapping;\n        handleAlignmentsOutsideOfDiffs(c.original.startLineNumber, c.modified.startLineNumber);\n        let first = true;\n        let lastModLineNumber = c.modified.startLineNumber;\n        let lastOrigLineNumber = c.original.startLineNumber;\n        function emitAlignment(origLineNumberExclusive, modLineNumberExclusive, forceAlignment = false) {\n            if (origLineNumberExclusive < lastOrigLineNumber || modLineNumberExclusive < lastModLineNumber) {\n                return;\n            }\n            if (first) {\n                first = false;\n            }\n            else if (!forceAlignment && (origLineNumberExclusive === lastOrigLineNumber || modLineNumberExclusive === lastModLineNumber)) {\n                // This causes a re-alignment of an already aligned line.\n                // However, we don't care for the final alignment.\n                return;\n            }\n            const originalRange = new LineRange(lastOrigLineNumber, origLineNumberExclusive);\n            const modifiedRange = new LineRange(lastModLineNumber, modLineNumberExclusive);\n            if (originalRange.isEmpty && modifiedRange.isEmpty) {\n                return;\n            }\n            const originalAdditionalHeight = originalLineHeightOverrides\n                .takeWhile(v => v.lineNumber < origLineNumberExclusive)\n                ?.reduce((p, c) => p + c.heightInPx, 0) ?? 0;\n            const modifiedAdditionalHeight = modifiedLineHeightOverrides\n                .takeWhile(v => v.lineNumber < modLineNumberExclusive)\n                ?.reduce((p, c) => p + c.heightInPx, 0) ?? 0;\n            result.push({\n                originalRange,\n                modifiedRange,\n                originalHeightInPx: originalRange.length * origLineHeight + originalAdditionalHeight,\n                modifiedHeightInPx: modifiedRange.length * modLineHeight + modifiedAdditionalHeight,\n                diff: m.lineRangeMapping,\n            });\n            lastOrigLineNumber = origLineNumberExclusive;\n            lastModLineNumber = modLineNumberExclusive;\n        }\n        if (innerHunkAlignment) {\n            for (const i of c.innerChanges || []) {\n                if (i.originalRange.startColumn > 1 && i.modifiedRange.startColumn > 1) {\n                    // There is some unmodified text on this line before the diff\n                    emitAlignment(i.originalRange.startLineNumber, i.modifiedRange.startLineNumber);\n                }\n                const originalModel = originalEditor.getModel();\n                // When the diff is invalid, the ranges might be out of bounds (this should be fixed in the diff model by applying edits directly).\n                const maxColumn = i.originalRange.endLineNumber <= originalModel.getLineCount() ? originalModel.getLineMaxColumn(i.originalRange.endLineNumber) : Number.MAX_SAFE_INTEGER;\n                if (i.originalRange.endColumn < maxColumn) {\n                    // // There is some unmodified text on this line after the diff\n                    emitAlignment(i.originalRange.endLineNumber, i.modifiedRange.endLineNumber);\n                }\n            }\n        }\n        emitAlignment(c.original.endLineNumberExclusive, c.modified.endLineNumberExclusive, true);\n        lastOriginalLineNumber = c.original.endLineNumberExclusive;\n        lastModifiedLineNumber = c.modified.endLineNumberExclusive;\n    }\n    handleAlignmentsOutsideOfDiffs(Number.MAX_VALUE, Number.MAX_VALUE);\n    return result;\n}\nfunction getAdditionalLineHeights(editor, viewZonesToIgnore) {\n    const viewZoneHeights = [];\n    const wrappingZoneHeights = [];\n    const hasWrapping = editor.getOption(147 /* EditorOption.wrappingInfo */).wrappingColumn !== -1;\n    const coordinatesConverter = editor._getViewModel().coordinatesConverter;\n    const editorLineHeight = editor.getOption(67 /* EditorOption.lineHeight */);\n    if (hasWrapping) {\n        for (let i = 1; i <= editor.getModel().getLineCount(); i++) {\n            const lineCount = coordinatesConverter.getModelLineViewLineCount(i);\n            if (lineCount > 1) {\n                wrappingZoneHeights.push({ lineNumber: i, heightInPx: editorLineHeight * (lineCount - 1) });\n            }\n        }\n    }\n    for (const w of editor.getWhitespaces()) {\n        if (viewZonesToIgnore.has(w.id)) {\n            continue;\n        }\n        const modelLineNumber = w.afterLineNumber === 0 ? 0 : coordinatesConverter.convertViewPositionToModelPosition(new Position(w.afterLineNumber, 1)).lineNumber;\n        viewZoneHeights.push({ lineNumber: modelLineNumber, heightInPx: w.height });\n    }\n    const result = joinCombine(viewZoneHeights, wrappingZoneHeights, v => v.lineNumber, (v1, v2) => ({ lineNumber: v1.lineNumber, heightInPx: v1.heightInPx + v2.heightInPx }));\n    return result;\n}\nexport function allowsTrueInlineDiffRendering(mapping) {\n    if (!mapping.innerChanges) {\n        return false;\n    }\n    return mapping.innerChanges.every(c => (rangeIsSingleLine(c.modifiedRange) && rangeIsSingleLine(c.originalRange))\n        || c.originalRange.equalsRange(new Range(1, 1, 1, 1)));\n}\nfunction rangeIsSingleLine(range) {\n    return range.startLineNumber === range.endLineNumber;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,IAAIA,UAAU,GAAI,IAAI,IAAI,IAAI,CAACA,UAAU,IAAK,UAAUC,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAE;EACnF,IAAIC,CAAC,GAAGC,SAAS,CAACC,MAAM;IAAEC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGH,MAAM,GAAGE,IAAI,KAAK,IAAI,GAAGA,IAAI,GAAGK,MAAM,CAACC,wBAAwB,CAACR,MAAM,EAAEC,GAAG,CAAC,GAAGC,IAAI;IAAEO,CAAC;EAC5H,IAAI,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,CAACC,QAAQ,KAAK,UAAU,EAAEL,CAAC,GAAGI,OAAO,CAACC,QAAQ,CAACZ,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,CAAC,CAAC,KAC1H,KAAK,IAAIU,CAAC,GAAGb,UAAU,CAACM,MAAM,GAAG,CAAC,EAAEO,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE,IAAIH,CAAC,GAAGV,UAAU,CAACa,CAAC,CAAC,EAAEN,CAAC,GAAG,CAACH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACH,CAAC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACT,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,GAAGG,CAAC,CAACT,MAAM,EAAEC,GAAG,CAAC,KAAKK,CAAC;EACjJ,OAAOH,CAAC,GAAG,CAAC,IAAIG,CAAC,IAAIC,MAAM,CAACM,cAAc,CAACb,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,EAAEA,CAAC;AACjE,CAAC;AACD,IAAIQ,OAAO,GAAI,IAAI,IAAI,IAAI,CAACA,OAAO,IAAK,UAAUC,UAAU,EAAEC,SAAS,EAAE;EACrE,OAAO,UAAUhB,MAAM,EAAEC,GAAG,EAAE;IAAEe,SAAS,CAAChB,MAAM,EAAEC,GAAG,EAAEc,UAAU,CAAC;EAAE,CAAC;AACzE,CAAC;AACD,SAASE,CAAC,EAAEC,qBAAqB,QAAQ,uCAAuC;AAChF,SAASC,UAAU,QAAQ,yCAAyC;AACpE,SAASC,gBAAgB,QAAQ,wCAAwC;AACzE,SAASC,OAAO,QAAQ,2CAA2C;AACnE,SAASC,UAAU,EAAEC,eAAe,QAAQ,4CAA4C;AACxF,SAASC,OAAO,EAAEC,OAAO,EAAEC,gBAAgB,EAAEC,mBAAmB,EAAEC,eAAe,QAAQ,6CAA6C;AACtI,SAASC,SAAS,QAAQ,4CAA4C;AACtE,SAASC,eAAe,QAAQ,wCAAwC;AACxE,SAASC,aAAa,QAAQ,mCAAmC;AACjE,SAASC,oBAAoB,EAAEC,cAAc,QAAQ,qCAAqC;AAC1F,SAASC,WAAW,QAAQ,8BAA8B;AAC1D,SAASC,2BAA2B,QAAQ,kCAAkC;AAC9E,SAASC,UAAU,EAAEC,aAAa,EAAEC,WAAW,QAAQ,kBAAkB;AACzE,SAASC,kBAAkB,EAAEC,WAAW,QAAQ,gBAAgB;AAChE,SAASC,SAAS,QAAQ,yCAAyC;AACnE,SAASC,QAAQ,QAAQ,wCAAwC;AACjE,SAASC,gBAAgB,QAAQ,oCAAoC;AACrE,SAASC,iBAAiB,QAAQ,iEAAiE;AACnG,SAASC,mBAAmB,QAAQ,+DAA+D;AACnG,SAASC,KAAK,QAAQ,qCAAqC;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,mBAAmB,GAAG,MAAMA,mBAAmB,SAASzB,UAAU,CAAC;EACnE0B,WAAWA,CAACC,aAAa,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,iBAAiB,EAAEC,6BAA6B,EAAEC,sBAAsB,EAAEC,qBAAqB,EAAEC,iBAAiB,EAAEC,mBAAmB,EAAE;IAChM,KAAK,CAAC,CAAC;IACP,IAAI,CAACT,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACC,6BAA6B,GAAGA,6BAA6B;IAClE,IAAI,CAACC,sBAAsB,GAAGA,sBAAsB;IACpD,IAAI,CAACC,qBAAqB,GAAGA,qBAAqB;IAClD,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACC,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACC,mBAAmB,GAAG/B,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC;IACnD,IAAI,CAACgC,qBAAqB,GAAGhC,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC;IACrD,IAAI,CAACiC,6BAA6B,GAAGtB,kBAAkB,CAAC,IAAI,CAACU,aAAa,EAAE,IAAI,CAACW,qBAAqB,EAAE,IAAI,CAACE,MAAM,CAAC;IACpH,IAAI,CAACC,mBAAmB,GAAGnC,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC;IACnD,IAAI,CAACoC,qBAAqB,GAAGpC,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC;IACrD,IAAI,CAACqC,6BAA6B,GAAG1B,kBAAkB,CAAC,IAAI,CAACU,aAAa,EAAE,IAAI,CAACe,qBAAqB,EAAE,IAAI,CAACF,MAAM,CAAC;IACpH,MAAMI,KAAK,GAAGtC,eAAe,CAAC,2BAA2B,EAAE,CAAC,CAAC;IAC7D,MAAMuC,iBAAiB,GAAG,IAAI,CAACC,SAAS,CAAC,IAAIhD,gBAAgB,CAAC,MAAM;MAChE8C,KAAK,CAACG,GAAG,CAACH,KAAK,CAACI,GAAG,CAAC,CAAC,GAAG,CAAC,EAAEC,SAAS,CAAC;IACzC,CAAC,EAAE,CAAC,CAAC,CAAC;IACN,IAAI,CAACH,SAAS,CAAC,IAAI,CAAClB,QAAQ,CAACsB,QAAQ,CAACC,oBAAoB,CAAEC,KAAK,IAAK;MAAE,IAAI,CAAC,IAAI,CAACpB,6BAA6B,CAAC,CAAC,EAAE;QAC/Ga,iBAAiB,CAACQ,QAAQ,CAAC,CAAC;MAChC;IAAE,CAAC,CAAC,CAAC;IACL,IAAI,CAACP,SAAS,CAAC,IAAI,CAAClB,QAAQ,CAAC0B,QAAQ,CAACH,oBAAoB,CAAEC,KAAK,IAAK;MAAE,IAAI,CAAC,IAAI,CAACpB,6BAA6B,CAAC,CAAC,EAAE;QAC/Ga,iBAAiB,CAACQ,QAAQ,CAAC,CAAC;MAChC;IAAE,CAAC,CAAC,CAAC;IACL,IAAI,CAACP,SAAS,CAAC,IAAI,CAAClB,QAAQ,CAACsB,QAAQ,CAACK,wBAAwB,CAAEC,IAAI,IAAK;MACrE,IAAIA,IAAI,CAACC,UAAU,CAAC,GAAG,CAAC,+BAA+B,CAAC,IAAID,IAAI,CAACC,UAAU,CAAC,EAAE,CAAC,6BAA6B,CAAC,EAAE;QAC3GZ,iBAAiB,CAACQ,QAAQ,CAAC,CAAC;MAChC;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,CAACP,SAAS,CAAC,IAAI,CAAClB,QAAQ,CAAC0B,QAAQ,CAACC,wBAAwB,CAAEC,IAAI,IAAK;MACrE,IAAIA,IAAI,CAACC,UAAU,CAAC,GAAG,CAAC,+BAA+B,CAAC,IAAID,IAAI,CAACC,UAAU,CAAC,EAAE,CAAC,6BAA6B,CAAC,EAAE;QAC3GZ,iBAAiB,CAACQ,QAAQ,CAAC,CAAC;MAChC;IACJ,CAAC,CAAC,CAAC;IACH,MAAMK,kCAAkC,GAAG,IAAI,CAAC7B,UAAU,CAAC8B,GAAG,CAACC,CAAC,IAAIA,CAAC,GAAGvD,mBAAmB,CAAC,IAAI,EAAEuD,CAAC,CAACC,KAAK,CAACX,QAAQ,CAACY,iBAAiB,EAAE,MAAMF,CAAC,CAACC,KAAK,CAACX,QAAQ,CAACa,YAAY,CAACC,2BAA2B,KAAK,CAAC,CAAC,2CAA2C,CAAC,GAAGf,SAAS,CAAC,CAACU,GAAG,CAAC,CAACC,CAAC,EAAEK,MAAM,KAAKL,CAAC,EAAEM,IAAI,CAACD,MAAM,CAAC,CAAC;IACzS,MAAME,UAAU,GAAGhE,OAAO,CAAE8D,MAAM,IAAK;MACnC;MACA,MAAMG,SAAS,GAAG,IAAI,CAACvC,UAAU,CAACqC,IAAI,CAACD,MAAM,CAAC;MAC9C,MAAMI,IAAI,GAAGD,SAAS,EAAEC,IAAI,CAACH,IAAI,CAACD,MAAM,CAAC;MACzC,IAAI,CAACG,SAAS,IAAI,CAACC,IAAI,EAAE;QACrB,OAAO,IAAI;MACf;MACAzB,KAAK,CAACsB,IAAI,CAACD,MAAM,CAAC;MAClB,MAAMK,gBAAgB,GAAG,IAAI,CAACxC,QAAQ,CAACwC,gBAAgB,CAACJ,IAAI,CAACD,MAAM,CAAC;MACpE,MAAMM,kBAAkB,GAAGD,gBAAgB;MAC3C,OAAOE,qBAAqB,CAAC,IAAI,CAAC5C,QAAQ,CAACsB,QAAQ,EAAE,IAAI,CAACtB,QAAQ,CAAC0B,QAAQ,EAAEe,IAAI,CAACI,QAAQ,EAAE,IAAI,CAACxC,sBAAsB,EAAE,IAAI,CAACC,qBAAqB,EAAEqC,kBAAkB,CAAC;IAC5K,CAAC,CAAC;IACF,MAAMG,yBAAyB,GAAGvE,OAAO,CAAE8D,MAAM,IAAK;MAClD;MACA,MAAMU,eAAe,GAAG,IAAI,CAAC9C,UAAU,CAACqC,IAAI,CAACD,MAAM,CAAC,EAAEW,kBAAkB,CAACV,IAAI,CAACD,MAAM,CAAC;MACrF,IAAI,CAACU,eAAe,EAAE;QAClB,OAAO,IAAI;MACf;MACA/B,KAAK,CAACsB,IAAI,CAACD,MAAM,CAAC;MAClB,MAAMQ,QAAQ,GAAGE,eAAe,CAACE,OAAO,CAAClB,GAAG,CAAC9E,CAAC,IAAI,IAAI+B,WAAW,CAAC/B,CAAC,CAAC,CAAC;MACrE;MACA,OAAO2F,qBAAqB,CAAC,IAAI,CAAC5C,QAAQ,CAACsB,QAAQ,EAAE,IAAI,CAACtB,QAAQ,CAAC0B,QAAQ,EAAEmB,QAAQ,EAAE,IAAI,CAACxC,sBAAsB,EAAE,IAAI,CAACC,qBAAqB,EAAE,IAAI,CAAC;IACzJ,CAAC,CAAC;IACF,SAAS4C,kBAAkBA,CAAA,EAAG;MAC1B,MAAM9F,CAAC,GAAG+F,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MACvChG,CAAC,CAACiG,SAAS,GAAG,eAAe;MAC7B,OAAOjG,CAAC;IACZ;IACA,MAAMkG,6BAA6B,GAAG,IAAI,CAACpC,SAAS,CAAC,IAAI7C,eAAe,CAAC,CAAC,CAAC;IAC3E,IAAI,CAACkF,SAAS,GAAG/E,gBAAgB,CAAC,IAAI,EAAE,CAAC6D,MAAM,EAAEmB,KAAK,KAAK;MACvDF,6BAA6B,CAACG,KAAK,CAAC,CAAC;MACrC,MAAMC,aAAa,GAAGnB,UAAU,CAACD,IAAI,CAACD,MAAM,CAAC,IAAI,EAAE;MACnD,MAAMsB,aAAa,GAAG,EAAE;MACxB,MAAMC,YAAY,GAAG,EAAE;MACvB,MAAMC,qBAAqB,GAAG,IAAI,CAAChD,mBAAmB,CAACyB,IAAI,CAACD,MAAM,CAAC;MACnE,IAAIwB,qBAAqB,GAAG,CAAC,EAAE;QAC3BD,YAAY,CAACE,IAAI,CAAC;UACdC,eAAe,EAAE,CAAC;UAClBC,OAAO,EAAEb,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UACtCa,UAAU,EAAEJ,qBAAqB;UACjCK,iBAAiB,EAAE,IAAI;UACvBC,iBAAiB,EAAE;QACvB,CAAC,CAAC;MACN;MACA,MAAMC,qBAAqB,GAAG,IAAI,CAAC3D,mBAAmB,CAAC6B,IAAI,CAACD,MAAM,CAAC;MACnE,IAAI+B,qBAAqB,GAAG,CAAC,EAAE;QAC3BT,aAAa,CAACG,IAAI,CAAC;UACfC,eAAe,EAAE,CAAC;UAClBC,OAAO,EAAEb,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UACtCa,UAAU,EAAEG,qBAAqB;UACjCF,iBAAiB,EAAE,IAAI;UACvBC,iBAAiB,EAAE;QACvB,CAAC,CAAC;MACN;MACA,MAAMzB,gBAAgB,GAAG,IAAI,CAACxC,QAAQ,CAACwC,gBAAgB,CAACJ,IAAI,CAACD,MAAM,CAAC;MACpE,MAAMgC,6BAA6B,GAAG,CAAC3B,gBAAgB,GAAG,IAAI,CAAC1C,QAAQ,CAAC0B,QAAQ,CAAC4C,aAAa,CAAC,CAAC,EAAEC,wBAAwB,CAAC,CAAC,GAAGlD,SAAS;MACxI,IAAIgD,6BAA6B,EAAE;QAC/B,MAAMG,aAAa,GAAG,IAAI,CAACxE,QAAQ,CAACsB,QAAQ,CAACmD,QAAQ,CAAC,CAAC;QACvD,KAAK,MAAMC,CAAC,IAAIhB,aAAa,EAAE;UAC3B,IAAIgB,CAAC,CAACjC,IAAI,EAAE;YACR,KAAK,IAAI/E,CAAC,GAAGgH,CAAC,CAACC,aAAa,CAACC,eAAe,EAAElH,CAAC,GAAGgH,CAAC,CAACC,aAAa,CAACE,sBAAsB,EAAEnH,CAAC,EAAE,EAAE;cAC3F;cACA;cACA;cACA,IAAIA,CAAC,GAAG8G,aAAa,CAACM,YAAY,CAAC,CAAC,EAAE;gBAClC,OAAO;kBAAEC,IAAI,EAAEpB,aAAa;kBAAEqB,GAAG,EAAEpB;gBAAa,CAAC;cACrD;cACAS,6BAA6B,EAAEY,UAAU,CAACT,aAAa,CAACU,cAAc,CAACxH,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC;YAC1F;UACJ;QACJ;MACJ;MACA,MAAMyH,aAAa,GAAGd,6BAA6B,EAAEe,QAAQ,CAAC,CAAC,IAAI,EAAE;MACrE,IAAIC,gBAAgB,GAAG,CAAC;MACxB,MAAMC,aAAa,GAAG,IAAI,CAACtF,QAAQ,CAAC0B,QAAQ,CAAC6D,SAAS,CAAC,EAAE,CAAC,6BAA6B,CAAC;MACxF,MAAMxC,eAAe,GAAG,IAAI,CAAC9C,UAAU,CAACqC,IAAI,CAACD,MAAM,CAAC,EAAEW,kBAAkB,CAACV,IAAI,CAACD,MAAM,CAAC;MACrF,MAAMmD,yBAAyB,GAAG,IAAI,CAACxF,QAAQ,CAACsB,QAAQ,CAACmD,QAAQ,CAAC,CAAC,EAAEe,yBAAyB,CAAC,CAAC,IAAI,KAAK;MACzG,MAAMC,eAAe,GAAG,IAAI,CAACzF,QAAQ,CAACsB,QAAQ,CAACmD,QAAQ,CAAC,CAAC,EAAEgB,eAAe,CAAC,CAAC,IAAI,KAAK;MACrF,MAAMC,aAAa,GAAGvG,aAAa,CAACwG,UAAU,CAAC,IAAI,CAAC3F,QAAQ,CAAC0B,QAAQ,CAAC;MACtE,KAAK,MAAMgD,CAAC,IAAIhB,aAAa,EAAE;QAC3B,IAAIgB,CAAC,CAACjC,IAAI,IAAI,CAACC,gBAAgB,KAAK,CAAC,IAAI,CAACxC,QAAQ,CAAC0F,0BAA0B,CAACtD,IAAI,CAACD,MAAM,CAAC,IAAI,CAACwD,6BAA6B,CAACnB,CAAC,CAACjC,IAAI,CAAC,CAAC,EAAE;UACnI,IAAI,CAACiC,CAAC,CAACC,aAAa,CAACmB,OAAO,EAAE;YAC1BhE,kCAAkC,CAACQ,IAAI,CAACD,MAAM,CAAC,CAAC,CAAC;YACjD,MAAM0D,kBAAkB,GAAG5C,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;YACxD2C,kBAAkB,CAACC,SAAS,CAACC,GAAG,CAAC,YAAY,EAAE,aAAa,EAAE,0BAA0B,CAAC;YACzF,MAAMzB,aAAa,GAAG,IAAI,CAACxE,QAAQ,CAACsB,QAAQ,CAACmD,QAAQ,CAAC,CAAC;YACvD;YACA;YACA;YACA,IAAIC,CAAC,CAACC,aAAa,CAACE,sBAAsB,GAAG,CAAC,GAAGL,aAAa,CAACM,YAAY,CAAC,CAAC,EAAE;cAC3E,OAAO;gBAAEC,IAAI,EAAEpB,aAAa;gBAAEqB,GAAG,EAAEpB;cAAa,CAAC;YACrD;YACA,MAAMsC,MAAM,GAAG,IAAIhH,UAAU,CAACwF,CAAC,CAACC,aAAa,CAACwB,cAAc,CAACC,CAAC,IAAI5B,aAAa,CAACrC,YAAY,CAACkE,aAAa,CAACD,CAAC,CAAC,CAAC,EAAE1B,CAAC,CAACC,aAAa,CAACwB,cAAc,CAACG,CAAC,IAAInB,aAAa,CAACE,gBAAgB,EAAE,CAAC,CAAC,EAAEG,yBAAyB,EAAEC,eAAe,CAAC;YACnO,MAAMc,WAAW,GAAG,EAAE;YACtB,KAAK,MAAM7I,CAAC,IAAIgH,CAAC,CAACjC,IAAI,CAAC+D,YAAY,IAAI,EAAE,EAAE;cACvCD,WAAW,CAACzC,IAAI,CAAC,IAAIrE,gBAAgB,CAAC/B,CAAC,CAACiH,aAAa,CAAC8B,KAAK,CAAC,EAAE/B,CAAC,CAACjC,IAAI,CAACnB,QAAQ,CAACsD,eAAe,GAAG,CAAC,CAAC,CAAC,EAAE9F,oBAAoB,CAACuE,SAAS,EAAE,CAAC,CAAC,kCAAkC,CAAC,CAAC;YAC/K;YACA,MAAMqD,MAAM,GAAGtH,WAAW,CAAC8G,MAAM,EAAER,aAAa,EAAEa,WAAW,EAAER,kBAAkB,CAAC;YAClF,MAAMY,aAAa,GAAGxD,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;YACnDuD,aAAa,CAACtD,SAAS,GAAG,iCAAiC;YAC3DxE,aAAa,CAAC8H,aAAa,EAAEjB,aAAa,CAACkB,QAAQ,CAAC;YACpD,IAAI,IAAI,CAAC1G,QAAQ,CAAC2G,gBAAgB,CAACvE,IAAI,CAACD,MAAM,CAAC,EAAE;cAC7C,KAAK,IAAI3E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgJ,MAAM,CAACI,aAAa,EAAEpJ,CAAC,EAAE,EAAE;gBAC3C,MAAMqJ,aAAa,GAAG5D,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;gBACnD2D,aAAa,CAAC1D,SAAS,GAAG,eAAe1E,SAAS,CAACqI,WAAW,CAACjI,cAAc,CAAC,EAAE;gBAChFgI,aAAa,CAACE,YAAY,CAAC,OAAO,EAAE,yBAAyBvJ,CAAC,GAAG4H,aAAa,YAAYI,aAAa,CAACwB,oBAAoB,aAAa5B,aAAa,aAAa,CAAC;gBACpKqB,aAAa,CAACQ,WAAW,CAACJ,aAAa,CAAC;cAC5C;YACJ;YACA,IAAIK,MAAM,GAAG/F,SAAS;YACtBiC,6BAA6B,CAAC2C,GAAG,CAAC,IAAIhH,2BAA2B,CAAC,MAAML,eAAe,CAACwI,MAAM,CAAC,EAAET,aAAa,EAAE,IAAI,CAAC3G,QAAQ,CAAC0B,QAAQ,EAAEgD,CAAC,CAACjC,IAAI,EAAE,IAAI,CAACtC,iBAAiB,EAAEuG,MAAM,CAACW,cAAc,EAAE,IAAI,CAACrH,QAAQ,CAACsB,QAAQ,CAACmD,QAAQ,CAAC,CAAC,EAAE,IAAI,CAACjE,mBAAmB,EAAE,IAAI,CAACD,iBAAiB,CAAC,CAAC;YACpR,KAAK,IAAI7C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgJ,MAAM,CAACW,cAAc,CAAClK,MAAM,EAAEO,CAAC,EAAE,EAAE;cACnD,MAAM4J,KAAK,GAAGZ,MAAM,CAACW,cAAc,CAAC3J,CAAC,CAAC;cACtC;cACA,IAAI4J,KAAK,GAAG,CAAC,EAAE;gBACX3D,aAAa,CAACG,IAAI,CAAC;kBACfC,eAAe,EAAEW,CAAC,CAACC,aAAa,CAACC,eAAe,GAAGlH,CAAC;kBACpDsG,OAAO,EAAEd,kBAAkB,CAAC,CAAC;kBAC7Be,UAAU,EAAE,CAACqD,KAAK,GAAG,CAAC,IAAIhC,aAAa;kBACvCpB,iBAAiB,EAAE,IAAI;kBACvBC,iBAAiB,EAAE;gBACvB,CAAC,CAAC;cACN;YACJ;YACAP,YAAY,CAACE,IAAI,CAAC;cACdC,eAAe,EAAEW,CAAC,CAAC6C,aAAa,CAAC3C,eAAe,GAAG,CAAC;cACpDZ,OAAO,EAAE+B,kBAAkB;cAC3B9B,UAAU,EAAEyC,MAAM,CAACI,aAAa,GAAGxB,aAAa;cAChDkC,YAAY,EAAEd,MAAM,CAACc,YAAY;cACjCb,aAAa;cACbc,SAASA,CAACC,EAAE,EAAE;gBAAEN,MAAM,GAAGM,EAAE;cAAE,CAAC;cAC9BxD,iBAAiB,EAAE,IAAI;cACvBC,iBAAiB,EAAE;YACvB,CAAC,CAAC;UACN;UACA,MAAMwC,aAAa,GAAGxD,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UACnDuD,aAAa,CAACtD,SAAS,GAAG,eAAe;UACzCM,aAAa,CAACG,IAAI,CAAC;YACfC,eAAe,EAAEW,CAAC,CAACC,aAAa,CAACE,sBAAsB,GAAG,CAAC;YAC3Db,OAAO,EAAEd,kBAAkB,CAAC,CAAC;YAC7Be,UAAU,EAAES,CAAC,CAACiD,kBAAkB;YAChChB,aAAa;YACbzC,iBAAiB,EAAE,IAAI;YACvBC,iBAAiB,EAAE;UACvB,CAAC,CAAC;QACN,CAAC,MACI;UACD,MAAMsC,KAAK,GAAG/B,CAAC,CAACiD,kBAAkB,GAAGjD,CAAC,CAACkD,kBAAkB;UACzD,IAAInB,KAAK,GAAG,CAAC,EAAE;YACX,IAAI1D,eAAe,EAAE8E,gBAAgB,CAACvG,QAAQ,CAACmF,KAAK,CAAC,CAAC,CAAC,CAAC,CAACqB,WAAW,CAAC,CAAC,CAAC,CAACC,QAAQ,CAACrD,CAAC,CAACC,aAAa,CAACE,sBAAsB,GAAG,CAAC,CAAC,EAAE;cAC1H;YACJ;YACAlB,aAAa,CAACG,IAAI,CAAC;cACfC,eAAe,EAAEW,CAAC,CAACC,aAAa,CAACE,sBAAsB,GAAG,CAAC;cAC3Db,OAAO,EAAEd,kBAAkB,CAAC,CAAC;cAC7Be,UAAU,EAAEwC,KAAK;cACjBvC,iBAAiB,EAAE,IAAI;cACvBC,iBAAiB,EAAE;YACvB,CAAC,CAAC;UACN,CAAC,MACI;YACD,IAAIpB,eAAe,EAAE8E,gBAAgB,CAACnG,QAAQ,CAAC+E,KAAK,CAAC,CAAC,CAAC,CAAC,CAACqB,WAAW,CAAC,CAAC,CAAC,CAACC,QAAQ,CAACrD,CAAC,CAAC6C,aAAa,CAAC1C,sBAAsB,GAAG,CAAC,CAAC,EAAE;cAC1H;YACJ;YACA,SAASmD,yBAAyBA,CAAA,EAAG;cACjC,MAAMC,KAAK,GAAG9E,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;cAC3C6E,KAAK,CAAC5E,SAAS,GAAG,sBAAsB,GAAG1E,SAAS,CAACqI,WAAW,CAAC7I,OAAO,CAAC+J,UAAU,CAAC;cACpF1E,KAAK,CAACyC,GAAG,CAACjI,qBAAqB,CAACiK,KAAK,EAAE,WAAW,EAAEE,CAAC,IAAIA,CAAC,CAACC,eAAe,CAAC,CAAC,CAAC,CAAC;cAC9E5E,KAAK,CAACyC,GAAG,CAACjI,qBAAqB,CAACiK,KAAK,EAAE,OAAO,EAAEE,CAAC,IAAI;gBACjDA,CAAC,CAACC,eAAe,CAAC,CAAC;gBACnBjI,iBAAiB,CAACkI,MAAM,CAAC3D,CAAC,CAACjC,IAAI,CAAC;cACpC,CAAC,CAAC,CAAC;cACH,OAAO1E,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,EAAEkK,KAAK,CAAC;YAC9B;YACA,IAAItB,aAAa,GAAGtF,SAAS;YAC7B,IAAIqD,CAAC,CAACjC,IAAI,IAAIiC,CAAC,CAACjC,IAAI,CAACf,QAAQ,CAACoE,OAAO,IAAI,IAAI,CAAC5F,QAAQ,CAACoI,2BAA2B,CAAChG,IAAI,CAACD,MAAM,CAAC,EAAE;cAC7FsE,aAAa,GAAGqB,yBAAyB,CAAC,CAAC;YAC/C;YACApE,YAAY,CAACE,IAAI,CAAC;cACdC,eAAe,EAAEW,CAAC,CAAC6C,aAAa,CAAC1C,sBAAsB,GAAG,CAAC;cAC3Db,OAAO,EAAEd,kBAAkB,CAAC,CAAC;cAC7Be,UAAU,EAAE,CAACwC,KAAK;cAClBE,aAAa;cACbzC,iBAAiB,EAAE,IAAI;cACvBC,iBAAiB,EAAE;YACvB,CAAC,CAAC;UACN;QACJ;MACJ;MACA,KAAK,MAAMO,CAAC,IAAI5B,yBAAyB,CAACR,IAAI,CAACD,MAAM,CAAC,IAAI,EAAE,EAAE;QAC1D,IAAI,CAACU,eAAe,EAAE8E,gBAAgB,CAACvG,QAAQ,CAACiH,SAAS,CAAC7D,CAAC,CAACC,aAAa,CAAC,IACnE,CAAC5B,eAAe,EAAE8E,gBAAgB,CAACnG,QAAQ,CAAC6G,SAAS,CAAC7D,CAAC,CAAC6C,aAAa,CAAC,EAAE;UAC3E;UACA;QACJ;QACA,MAAMd,KAAK,GAAG/B,CAAC,CAACiD,kBAAkB,GAAGjD,CAAC,CAACkD,kBAAkB;QACzD,IAAInB,KAAK,GAAG,CAAC,EAAE;UACX9C,aAAa,CAACG,IAAI,CAAC;YACfC,eAAe,EAAEW,CAAC,CAACC,aAAa,CAACE,sBAAsB,GAAG,CAAC;YAC3Db,OAAO,EAAEd,kBAAkB,CAAC,CAAC;YAC7Be,UAAU,EAAEwC,KAAK;YACjBvC,iBAAiB,EAAE,IAAI;YACvBC,iBAAiB,EAAE;UACvB,CAAC,CAAC;QACN,CAAC,MACI;UACDP,YAAY,CAACE,IAAI,CAAC;YACdC,eAAe,EAAEW,CAAC,CAAC6C,aAAa,CAAC1C,sBAAsB,GAAG,CAAC;YAC3Db,OAAO,EAAEd,kBAAkB,CAAC,CAAC;YAC7Be,UAAU,EAAE,CAACwC,KAAK;YAClBvC,iBAAiB,EAAE,IAAI;YACvBC,iBAAiB,EAAE;UACvB,CAAC,CAAC;QACN;MACJ;MACA,OAAO;QAAEY,IAAI,EAAEpB,aAAa;QAAEqB,GAAG,EAAEpB;MAAa,CAAC;IACrD,CAAC,CAAC;IACF,IAAI4E,YAAY,GAAG,KAAK;IACxB,IAAI,CAACtH,SAAS,CAAC,IAAI,CAAClB,QAAQ,CAACsB,QAAQ,CAACmH,iBAAiB,CAACN,CAAC,IAAI;MACzD,IAAIA,CAAC,CAACO,iBAAiB,IAAI,CAACF,YAAY,EAAE;QACtCA,YAAY,GAAG,IAAI;QACnB,IAAI,CAACxI,QAAQ,CAAC0B,QAAQ,CAACiH,aAAa,CAACR,CAAC,CAACS,UAAU,CAAC;QAClDJ,YAAY,GAAG,KAAK;MACxB;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,CAACtH,SAAS,CAAC,IAAI,CAAClB,QAAQ,CAAC0B,QAAQ,CAAC+G,iBAAiB,CAACN,CAAC,IAAI;MACzD,IAAIA,CAAC,CAACO,iBAAiB,IAAI,CAACF,YAAY,EAAE;QACtCA,YAAY,GAAG,IAAI;QACnB,IAAI,CAACxI,QAAQ,CAACsB,QAAQ,CAACqH,aAAa,CAACR,CAAC,CAACS,UAAU,CAAC;QAClDJ,YAAY,GAAG,KAAK;MACxB;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,CAACK,kBAAkB,GAAGpK,mBAAmB,CAAC,IAAI,CAACuB,QAAQ,CAACsB,QAAQ,CAACmH,iBAAiB,EAAE,MAAM,yCAA0C,IAAI,CAACzI,QAAQ,CAACsB,QAAQ,CAACwH,YAAY,CAAC,CAAC,CAAC;IAC9K,IAAI,CAACC,kBAAkB,GAAGtK,mBAAmB,CAAC,IAAI,CAACuB,QAAQ,CAAC0B,QAAQ,CAAC+G,iBAAiB,EAAE,MAAM,yCAA0C,IAAI,CAACzI,QAAQ,CAAC0B,QAAQ,CAACoH,YAAY,CAAC,CAAC,CAAC;IAC9K;IACA;IACA;IACA;IACA;IACA,IAAI,CAAC5H,SAAS,CAAC5C,OAAO,CAAC+D,MAAM,IAAI;MAC7B;MACA,MAAM2G,oBAAoB,GAAG,IAAI,CAACH,kBAAkB,CAACvG,IAAI,CAACD,MAAM,CAAC,IAC1D,IAAI,CAAC1B,6BAA6B,CAACS,GAAG,CAAC,CAAC,GAAG,IAAI,CAACL,6BAA6B,CAACuB,IAAI,CAACD,MAAM,CAAC,CAAC,IAC3F,IAAI,CAAC5B,mBAAmB,CAACW,GAAG,CAAC,CAAC,GAAG,IAAI,CAACP,mBAAmB,CAACyB,IAAI,CAACD,MAAM,CAAC,CAAC;MAC9E,IAAI2G,oBAAoB,KAAK,IAAI,CAAChJ,QAAQ,CAAC0B,QAAQ,CAACoH,YAAY,CAAC,CAAC,EAAE;QAChE,IAAI,CAAC9I,QAAQ,CAAC0B,QAAQ,CAACuH,YAAY,CAACD,oBAAoB,EAAE,CAAC,CAAC,0BAA0B,CAAC;MAC3F;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,CAAC9H,SAAS,CAAC5C,OAAO,CAAC+D,MAAM,IAAI;MAC7B;MACA,MAAM6G,oBAAoB,GAAG,IAAI,CAACH,kBAAkB,CAACzG,IAAI,CAACD,MAAM,CAAC,IAC1D,IAAI,CAACtB,6BAA6B,CAACK,GAAG,CAAC,CAAC,GAAG,IAAI,CAACT,6BAA6B,CAAC2B,IAAI,CAACD,MAAM,CAAC,CAAC,IAC3F,IAAI,CAACxB,mBAAmB,CAACO,GAAG,CAAC,CAAC,GAAG,IAAI,CAACX,mBAAmB,CAAC6B,IAAI,CAACD,MAAM,CAAC,CAAC;MAC9E,IAAI6G,oBAAoB,KAAK,IAAI,CAAClJ,QAAQ,CAACsB,QAAQ,CAACwH,YAAY,CAAC,CAAC,EAAE;QAChE,IAAI,CAAC9I,QAAQ,CAACsB,QAAQ,CAAC2H,YAAY,CAACC,oBAAoB,EAAE,CAAC,CAAC,0BAA0B,CAAC;MAC3F;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,CAAChI,SAAS,CAAC5C,OAAO,CAAC+D,MAAM,IAAI;MAC7B;MACA,MAAML,CAAC,GAAG,IAAI,CAAC/B,UAAU,CAACqC,IAAI,CAACD,MAAM,CAAC,EAAEW,kBAAkB,CAACV,IAAI,CAACD,MAAM,CAAC;MACvE,IAAI8G,cAAc,GAAG,CAAC;MACtB,IAAInH,CAAC,EAAE;QACH,MAAMoH,eAAe,GAAG,IAAI,CAACpJ,QAAQ,CAACsB,QAAQ,CAAC+H,mBAAmB,CAACrH,CAAC,CAAC6F,gBAAgB,CAACvG,QAAQ,CAACsD,eAAe,EAAE,IAAI,CAAC,GAAG,IAAI,CAACnE,mBAAmB,CAACW,GAAG,CAAC,CAAC;QACtJ,MAAMkI,eAAe,GAAG,IAAI,CAACtJ,QAAQ,CAAC0B,QAAQ,CAAC2H,mBAAmB,CAACrH,CAAC,CAAC6F,gBAAgB,CAACnG,QAAQ,CAACkD,eAAe,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC/D,mBAAmB,CAACO,GAAG,CAAC,CAAC;QACtJ+H,cAAc,GAAGG,eAAe,GAAGF,eAAe;MACtD;MACA,IAAID,cAAc,GAAG,CAAC,EAAE;QACpB,IAAI,CAACtI,mBAAmB,CAACM,GAAG,CAAC,CAAC,EAAEE,SAAS,CAAC;QAC1C,IAAI,CAACZ,mBAAmB,CAACU,GAAG,CAACgI,cAAc,EAAE9H,SAAS,CAAC;MAC3D,CAAC,MACI,IAAI8H,cAAc,GAAG,CAAC,EAAE;QACzB,IAAI,CAACtI,mBAAmB,CAACM,GAAG,CAAC,CAACgI,cAAc,EAAE9H,SAAS,CAAC;QACxD,IAAI,CAACZ,mBAAmB,CAACU,GAAG,CAAC,CAAC,EAAEE,SAAS,CAAC;MAC9C,CAAC,MACI;QACDkI,UAAU,CAAC,MAAM;UACb,IAAI,CAAC1I,mBAAmB,CAACM,GAAG,CAAC,CAAC,EAAEE,SAAS,CAAC;UAC1C,IAAI,CAACZ,mBAAmB,CAACU,GAAG,CAAC,CAAC,EAAEE,SAAS,CAAC;QAC9C,CAAC,EAAE,GAAG,CAAC;MACX;MACA,IAAI,IAAI,CAACrB,QAAQ,CAAC0B,QAAQ,CAAC8H,YAAY,CAAC,CAAC,EAAE;QACvC,IAAI,CAAC9I,qBAAqB,CAACS,GAAG,CAAC,IAAI,CAACL,qBAAqB,CAACM,GAAG,CAAC,CAAC,GAAG+H,cAAc,EAAE9H,SAAS,EAAE,IAAI,CAAC;MACtG,CAAC,MACI;QACD,IAAI,CAACP,qBAAqB,CAACK,GAAG,CAAC,IAAI,CAACT,qBAAqB,CAACU,GAAG,CAAC,CAAC,GAAG+H,cAAc,EAAE9H,SAAS,EAAE,IAAI,CAAC;MACtG;IACJ,CAAC,CAAC,CAAC;EACP;AACJ,CAAC;AACDxB,mBAAmB,GAAGjD,UAAU,CAAC,CAC7BgB,OAAO,CAAC,CAAC,EAAE8B,iBAAiB,CAAC,EAC7B9B,OAAO,CAAC,CAAC,EAAE+B,mBAAmB,CAAC,CAClC,EAAEE,mBAAmB,CAAC;AACvB,SAASA,mBAAmB;AAC5B,SAAS+C,qBAAqBA,CAAC6G,cAAc,EAAEC,cAAc,EAAEC,KAAK,EAAEC,gCAAgC,EAAEC,gCAAgC,EAAElH,kBAAkB,EAAE;EAC1J,MAAMmH,2BAA2B,GAAG,IAAI7L,UAAU,CAAC8L,wBAAwB,CAACN,cAAc,EAAEG,gCAAgC,CAAC,CAAC;EAC9H,MAAMI,2BAA2B,GAAG,IAAI/L,UAAU,CAAC8L,wBAAwB,CAACL,cAAc,EAAEG,gCAAgC,CAAC,CAAC;EAC9H,MAAMI,cAAc,GAAGR,cAAc,CAAClE,SAAS,CAAC,EAAE,CAAC,6BAA6B,CAAC;EACjF,MAAMD,aAAa,GAAGoE,cAAc,CAACnE,SAAS,CAAC,EAAE,CAAC,6BAA6B,CAAC;EAChF,MAAMmB,MAAM,GAAG,EAAE;EACjB,IAAIwD,sBAAsB,GAAG,CAAC;EAC9B,IAAIC,sBAAsB,GAAG,CAAC;EAC9B,SAASC,8BAA8BA,CAACC,gCAAgC,EAAEC,gCAAgC,EAAE;IACxG,OAAO,IAAI,EAAE;MACT,IAAIC,QAAQ,GAAGT,2BAA2B,CAACU,IAAI,CAAC,CAAC;MACjD,IAAIC,OAAO,GAAGT,2BAA2B,CAACQ,IAAI,CAAC,CAAC;MAChD,IAAID,QAAQ,IAAIA,QAAQ,CAACG,UAAU,IAAIL,gCAAgC,EAAE;QACrEE,QAAQ,GAAGlJ,SAAS;MACxB;MACA,IAAIoJ,OAAO,IAAIA,OAAO,CAACC,UAAU,IAAIJ,gCAAgC,EAAE;QACnEG,OAAO,GAAGpJ,SAAS;MACvB;MACA,IAAI,CAACkJ,QAAQ,IAAI,CAACE,OAAO,EAAE;QACvB;MACJ;MACA,MAAME,QAAQ,GAAGJ,QAAQ,GAAGA,QAAQ,CAACG,UAAU,GAAGR,sBAAsB,GAAGU,MAAM,CAACC,SAAS;MAC3F,MAAMC,QAAQ,GAAGL,OAAO,GAAGA,OAAO,CAACC,UAAU,GAAGP,sBAAsB,GAAGS,MAAM,CAACC,SAAS;MACzF,IAAIF,QAAQ,GAAGG,QAAQ,EAAE;QACrBhB,2BAA2B,CAACiB,OAAO,CAAC,CAAC;QACrCN,OAAO,GAAG;UACNC,UAAU,EAAEH,QAAQ,CAACG,UAAU,GAAGR,sBAAsB,GAAGC,sBAAsB;UACjFlG,UAAU,EAAE;QAChB,CAAC;MACL,CAAC,MACI,IAAI0G,QAAQ,GAAGG,QAAQ,EAAE;QAC1Bd,2BAA2B,CAACe,OAAO,CAAC,CAAC;QACrCR,QAAQ,GAAG;UACPG,UAAU,EAAED,OAAO,CAACC,UAAU,GAAGP,sBAAsB,GAAGD,sBAAsB;UAChFjG,UAAU,EAAE;QAChB,CAAC;MACL,CAAC,MACI;QACD6F,2BAA2B,CAACiB,OAAO,CAAC,CAAC;QACrCf,2BAA2B,CAACe,OAAO,CAAC,CAAC;MACzC;MACArE,MAAM,CAAC5C,IAAI,CAAC;QACRa,aAAa,EAAEpF,SAAS,CAACyL,QAAQ,CAACT,QAAQ,CAACG,UAAU,EAAE,CAAC,CAAC;QACzDnD,aAAa,EAAEhI,SAAS,CAACyL,QAAQ,CAACP,OAAO,CAACC,UAAU,EAAE,CAAC,CAAC;QACxD9C,kBAAkB,EAAEqC,cAAc,GAAGM,QAAQ,CAACtG,UAAU;QACxD0D,kBAAkB,EAAErC,aAAa,GAAGmF,OAAO,CAACxG,UAAU;QACtDxB,IAAI,EAAEpB;MACV,CAAC,CAAC;IACN;EACJ;EACA,KAAK,MAAMW,CAAC,IAAI2H,KAAK,EAAE;IACnB,MAAM1M,CAAC,GAAG+E,CAAC,CAAC6F,gBAAgB;IAC5BuC,8BAA8B,CAACnN,CAAC,CAACqE,QAAQ,CAACsD,eAAe,EAAE3H,CAAC,CAACyE,QAAQ,CAACkD,eAAe,CAAC;IACtF,IAAIqG,KAAK,GAAG,IAAI;IAChB,IAAIC,iBAAiB,GAAGjO,CAAC,CAACyE,QAAQ,CAACkD,eAAe;IAClD,IAAIuG,kBAAkB,GAAGlO,CAAC,CAACqE,QAAQ,CAACsD,eAAe;IACnD,SAASwG,aAAaA,CAACC,uBAAuB,EAAEC,sBAAsB,EAAEC,cAAc,GAAG,KAAK,EAAE;MAC5F,IAAIF,uBAAuB,GAAGF,kBAAkB,IAAIG,sBAAsB,GAAGJ,iBAAiB,EAAE;QAC5F;MACJ;MACA,IAAID,KAAK,EAAE;QACPA,KAAK,GAAG,KAAK;MACjB,CAAC,MACI,IAAI,CAACM,cAAc,KAAKF,uBAAuB,KAAKF,kBAAkB,IAAIG,sBAAsB,KAAKJ,iBAAiB,CAAC,EAAE;QAC1H;QACA;QACA;MACJ;MACA,MAAMvG,aAAa,GAAG,IAAIpF,SAAS,CAAC4L,kBAAkB,EAAEE,uBAAuB,CAAC;MAChF,MAAM9D,aAAa,GAAG,IAAIhI,SAAS,CAAC2L,iBAAiB,EAAEI,sBAAsB,CAAC;MAC9E,IAAI3G,aAAa,CAACmB,OAAO,IAAIyB,aAAa,CAACzB,OAAO,EAAE;QAChD;MACJ;MACA,MAAM0F,wBAAwB,GAAG1B,2BAA2B,CACvD2B,SAAS,CAACC,CAAC,IAAIA,CAAC,CAAChB,UAAU,GAAGW,uBAAuB,CAAC,EACrDM,MAAM,CAAC,CAACC,CAAC,EAAE3O,CAAC,KAAK2O,CAAC,GAAG3O,CAAC,CAACgH,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC;MAChD,MAAM4H,wBAAwB,GAAG7B,2BAA2B,CACvDyB,SAAS,CAACC,CAAC,IAAIA,CAAC,CAAChB,UAAU,GAAGY,sBAAsB,CAAC,EACpDK,MAAM,CAAC,CAACC,CAAC,EAAE3O,CAAC,KAAK2O,CAAC,GAAG3O,CAAC,CAACgH,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC;MAChDyC,MAAM,CAAC5C,IAAI,CAAC;QACRa,aAAa;QACb4C,aAAa;QACbK,kBAAkB,EAAEjD,aAAa,CAACxH,MAAM,GAAG8M,cAAc,GAAGuB,wBAAwB;QACpF7D,kBAAkB,EAAEJ,aAAa,CAACpK,MAAM,GAAGmI,aAAa,GAAGuG,wBAAwB;QACnFpJ,IAAI,EAAET,CAAC,CAAC6F;MACZ,CAAC,CAAC;MACFsD,kBAAkB,GAAGE,uBAAuB;MAC5CH,iBAAiB,GAAGI,sBAAsB;IAC9C;IACA,IAAI3I,kBAAkB,EAAE;MACpB,KAAK,MAAMjF,CAAC,IAAIT,CAAC,CAACuJ,YAAY,IAAI,EAAE,EAAE;QAClC,IAAI9I,CAAC,CAACiH,aAAa,CAACmH,WAAW,GAAG,CAAC,IAAIpO,CAAC,CAAC6J,aAAa,CAACuE,WAAW,GAAG,CAAC,EAAE;UACpE;UACAV,aAAa,CAAC1N,CAAC,CAACiH,aAAa,CAACC,eAAe,EAAElH,CAAC,CAAC6J,aAAa,CAAC3C,eAAe,CAAC;QACnF;QACA,MAAMJ,aAAa,GAAGiF,cAAc,CAAChF,QAAQ,CAAC,CAAC;QAC/C;QACA,MAAMsH,SAAS,GAAGrO,CAAC,CAACiH,aAAa,CAACqH,aAAa,IAAIxH,aAAa,CAACM,YAAY,CAAC,CAAC,GAAGN,aAAa,CAACyH,gBAAgB,CAACvO,CAAC,CAACiH,aAAa,CAACqH,aAAa,CAAC,GAAGpB,MAAM,CAACsB,gBAAgB;QACzK,IAAIxO,CAAC,CAACiH,aAAa,CAACwH,SAAS,GAAGJ,SAAS,EAAE;UACvC;UACAX,aAAa,CAAC1N,CAAC,CAACiH,aAAa,CAACqH,aAAa,EAAEtO,CAAC,CAAC6J,aAAa,CAACyE,aAAa,CAAC;QAC/E;MACJ;IACJ;IACAZ,aAAa,CAACnO,CAAC,CAACqE,QAAQ,CAACuD,sBAAsB,EAAE5H,CAAC,CAACyE,QAAQ,CAACmD,sBAAsB,EAAE,IAAI,CAAC;IACzFqF,sBAAsB,GAAGjN,CAAC,CAACqE,QAAQ,CAACuD,sBAAsB;IAC1DsF,sBAAsB,GAAGlN,CAAC,CAACyE,QAAQ,CAACmD,sBAAsB;EAC9D;EACAuF,8BAA8B,CAACQ,MAAM,CAACC,SAAS,EAAED,MAAM,CAACC,SAAS,CAAC;EAClE,OAAOnE,MAAM;AACjB;AACA,SAASqD,wBAAwBA,CAACqC,MAAM,EAAEC,iBAAiB,EAAE;EACzD,MAAMC,eAAe,GAAG,EAAE;EAC1B,MAAMC,mBAAmB,GAAG,EAAE;EAC9B,MAAMC,WAAW,GAAGJ,MAAM,CAAC7G,SAAS,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAACkH,cAAc,KAAK,CAAC,CAAC;EAC/F,MAAMC,oBAAoB,GAAGN,MAAM,CAAC9H,aAAa,CAAC,CAAC,CAACoI,oBAAoB;EACxE,MAAMC,gBAAgB,GAAGP,MAAM,CAAC7G,SAAS,CAAC,EAAE,CAAC,6BAA6B,CAAC;EAC3E,IAAIiH,WAAW,EAAE;IACb,KAAK,IAAI9O,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI0O,MAAM,CAAC3H,QAAQ,CAAC,CAAC,CAACK,YAAY,CAAC,CAAC,EAAEpH,CAAC,EAAE,EAAE;MACxD,MAAMkP,SAAS,GAAGF,oBAAoB,CAACG,yBAAyB,CAACnP,CAAC,CAAC;MACnE,IAAIkP,SAAS,GAAG,CAAC,EAAE;QACfL,mBAAmB,CAACzI,IAAI,CAAC;UAAE4G,UAAU,EAAEhN,CAAC;UAAEuG,UAAU,EAAE0I,gBAAgB,IAAIC,SAAS,GAAG,CAAC;QAAE,CAAC,CAAC;MAC/F;IACJ;EACJ;EACA,KAAK,MAAME,CAAC,IAAIV,MAAM,CAACW,cAAc,CAAC,CAAC,EAAE;IACrC,IAAIV,iBAAiB,CAACW,GAAG,CAACF,CAAC,CAACpF,EAAE,CAAC,EAAE;MAC7B;IACJ;IACA,MAAMuF,eAAe,GAAGH,CAAC,CAAC/I,eAAe,KAAK,CAAC,GAAG,CAAC,GAAG2I,oBAAoB,CAACQ,kCAAkC,CAAC,IAAI1N,QAAQ,CAACsN,CAAC,CAAC/I,eAAe,EAAE,CAAC,CAAC,CAAC,CAAC2G,UAAU;IAC5J4B,eAAe,CAACxI,IAAI,CAAC;MAAE4G,UAAU,EAAEuC,eAAe;MAAEhJ,UAAU,EAAE6I,CAAC,CAACK;IAAO,CAAC,CAAC;EAC/E;EACA,MAAMzG,MAAM,GAAGpH,WAAW,CAACgN,eAAe,EAAEC,mBAAmB,EAAEb,CAAC,IAAIA,CAAC,CAAChB,UAAU,EAAE,CAAC0C,EAAE,EAAEC,EAAE,MAAM;IAAE3C,UAAU,EAAE0C,EAAE,CAAC1C,UAAU;IAAEzG,UAAU,EAAEmJ,EAAE,CAACnJ,UAAU,GAAGoJ,EAAE,CAACpJ;EAAW,CAAC,CAAC,CAAC;EAC3K,OAAOyC,MAAM;AACjB;AACA,OAAO,SAASb,6BAA6BA,CAACyH,OAAO,EAAE;EACnD,IAAI,CAACA,OAAO,CAAC9G,YAAY,EAAE;IACvB,OAAO,KAAK;EAChB;EACA,OAAO8G,OAAO,CAAC9G,YAAY,CAAC+G,KAAK,CAACtQ,CAAC,IAAKuQ,iBAAiB,CAACvQ,CAAC,CAACsK,aAAa,CAAC,IAAIiG,iBAAiB,CAACvQ,CAAC,CAAC0H,aAAa,CAAC,IACzG1H,CAAC,CAAC0H,aAAa,CAAC8I,WAAW,CAAC,IAAI7N,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAC9D;AACA,SAAS4N,iBAAiBA,CAACE,KAAK,EAAE;EAC9B,OAAOA,KAAK,CAAC9I,eAAe,KAAK8I,KAAK,CAAC1B,aAAa;AACxD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}