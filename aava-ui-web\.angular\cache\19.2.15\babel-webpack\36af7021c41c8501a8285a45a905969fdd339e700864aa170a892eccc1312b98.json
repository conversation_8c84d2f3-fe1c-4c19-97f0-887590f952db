{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = this && this.__param || function (paramIndex, decorator) {\n  return function (target, key) {\n    decorator(target, key, paramIndex);\n  };\n};\nimport { normalizeDriveLetter } from '../../../../base/common/labels.js';\nimport * as path from '../../../../base/common/path.js';\nimport { dirname } from '../../../../base/common/resources.js';\nimport { commonPrefixLength, getLeadingWhitespace, isFalsyOrWhitespace, splitLines } from '../../../../base/common/strings.js';\nimport { generateUuid } from '../../../../base/common/uuid.js';\nimport { ILanguageConfigurationService } from '../../../common/languages/languageConfigurationRegistry.js';\nimport { Text } from './snippetParser.js';\nimport * as nls from '../../../../nls.js';\nimport { WORKSPACE_EXTENSION, isSingleFolderWorkspaceIdentifier, toWorkspaceIdentifier, isEmptyWorkspaceIdentifier } from '../../../../platform/workspace/common/workspace.js';\nexport const KnownSnippetVariableNames = Object.freeze({\n  'CURRENT_YEAR': true,\n  'CURRENT_YEAR_SHORT': true,\n  'CURRENT_MONTH': true,\n  'CURRENT_DATE': true,\n  'CURRENT_HOUR': true,\n  'CURRENT_MINUTE': true,\n  'CURRENT_SECOND': true,\n  'CURRENT_DAY_NAME': true,\n  'CURRENT_DAY_NAME_SHORT': true,\n  'CURRENT_MONTH_NAME': true,\n  'CURRENT_MONTH_NAME_SHORT': true,\n  'CURRENT_SECONDS_UNIX': true,\n  'CURRENT_TIMEZONE_OFFSET': true,\n  'SELECTION': true,\n  'CLIPBOARD': true,\n  'TM_SELECTED_TEXT': true,\n  'TM_CURRENT_LINE': true,\n  'TM_CURRENT_WORD': true,\n  'TM_LINE_INDEX': true,\n  'TM_LINE_NUMBER': true,\n  'TM_FILENAME': true,\n  'TM_FILENAME_BASE': true,\n  'TM_DIRECTORY': true,\n  'TM_FILEPATH': true,\n  'CURSOR_INDEX': true,\n  // 0-offset\n  'CURSOR_NUMBER': true,\n  // 1-offset\n  'RELATIVE_FILEPATH': true,\n  'BLOCK_COMMENT_START': true,\n  'BLOCK_COMMENT_END': true,\n  'LINE_COMMENT': true,\n  'WORKSPACE_NAME': true,\n  'WORKSPACE_FOLDER': true,\n  'RANDOM': true,\n  'RANDOM_HEX': true,\n  'UUID': true\n});\nexport class CompositeSnippetVariableResolver {\n  constructor(_delegates) {\n    this._delegates = _delegates;\n    //\n  }\n  resolve(variable) {\n    for (const delegate of this._delegates) {\n      const value = delegate.resolve(variable);\n      if (value !== undefined) {\n        return value;\n      }\n    }\n    return undefined;\n  }\n}\nexport class SelectionBasedVariableResolver {\n  constructor(_model, _selection, _selectionIdx, _overtypingCapturer) {\n    this._model = _model;\n    this._selection = _selection;\n    this._selectionIdx = _selectionIdx;\n    this._overtypingCapturer = _overtypingCapturer;\n    //\n  }\n  resolve(variable) {\n    const {\n      name\n    } = variable;\n    if (name === 'SELECTION' || name === 'TM_SELECTED_TEXT') {\n      let value = this._model.getValueInRange(this._selection) || undefined;\n      let isMultiline = this._selection.startLineNumber !== this._selection.endLineNumber;\n      // If there was no selected text, try to get last overtyped text\n      if (!value && this._overtypingCapturer) {\n        const info = this._overtypingCapturer.getLastOvertypedInfo(this._selectionIdx);\n        if (info) {\n          value = info.value;\n          isMultiline = info.multiline;\n        }\n      }\n      if (value && isMultiline && variable.snippet) {\n        // Selection is a multiline string which we indentation we now\n        // need to adjust. We compare the indentation of this variable\n        // with the indentation at the editor position and add potential\n        // extra indentation to the value\n        const line = this._model.getLineContent(this._selection.startLineNumber);\n        const lineLeadingWhitespace = getLeadingWhitespace(line, 0, this._selection.startColumn - 1);\n        let varLeadingWhitespace = lineLeadingWhitespace;\n        variable.snippet.walk(marker => {\n          if (marker === variable) {\n            return false;\n          }\n          if (marker instanceof Text) {\n            varLeadingWhitespace = getLeadingWhitespace(splitLines(marker.value).pop());\n          }\n          return true;\n        });\n        const whitespaceCommonLength = commonPrefixLength(varLeadingWhitespace, lineLeadingWhitespace);\n        value = value.replace(/(\\r\\n|\\r|\\n)(.*)/g, (m, newline, rest) => `${newline}${varLeadingWhitespace.substr(whitespaceCommonLength)}${rest}`);\n      }\n      return value;\n    } else if (name === 'TM_CURRENT_LINE') {\n      return this._model.getLineContent(this._selection.positionLineNumber);\n    } else if (name === 'TM_CURRENT_WORD') {\n      const info = this._model.getWordAtPosition({\n        lineNumber: this._selection.positionLineNumber,\n        column: this._selection.positionColumn\n      });\n      return info && info.word || undefined;\n    } else if (name === 'TM_LINE_INDEX') {\n      return String(this._selection.positionLineNumber - 1);\n    } else if (name === 'TM_LINE_NUMBER') {\n      return String(this._selection.positionLineNumber);\n    } else if (name === 'CURSOR_INDEX') {\n      return String(this._selectionIdx);\n    } else if (name === 'CURSOR_NUMBER') {\n      return String(this._selectionIdx + 1);\n    }\n    return undefined;\n  }\n}\nexport class ModelBasedVariableResolver {\n  constructor(_labelService, _model) {\n    this._labelService = _labelService;\n    this._model = _model;\n    //\n  }\n  resolve(variable) {\n    const {\n      name\n    } = variable;\n    if (name === 'TM_FILENAME') {\n      return path.basename(this._model.uri.fsPath);\n    } else if (name === 'TM_FILENAME_BASE') {\n      const name = path.basename(this._model.uri.fsPath);\n      const idx = name.lastIndexOf('.');\n      if (idx <= 0) {\n        return name;\n      } else {\n        return name.slice(0, idx);\n      }\n    } else if (name === 'TM_DIRECTORY') {\n      if (path.dirname(this._model.uri.fsPath) === '.') {\n        return '';\n      }\n      return this._labelService.getUriLabel(dirname(this._model.uri));\n    } else if (name === 'TM_FILEPATH') {\n      return this._labelService.getUriLabel(this._model.uri);\n    } else if (name === 'RELATIVE_FILEPATH') {\n      return this._labelService.getUriLabel(this._model.uri, {\n        relative: true,\n        noPrefix: true\n      });\n    }\n    return undefined;\n  }\n}\nexport class ClipboardBasedVariableResolver {\n  constructor(_readClipboardText, _selectionIdx, _selectionCount, _spread) {\n    this._readClipboardText = _readClipboardText;\n    this._selectionIdx = _selectionIdx;\n    this._selectionCount = _selectionCount;\n    this._spread = _spread;\n    //\n  }\n  resolve(variable) {\n    if (variable.name !== 'CLIPBOARD') {\n      return undefined;\n    }\n    const clipboardText = this._readClipboardText();\n    if (!clipboardText) {\n      return undefined;\n    }\n    // `spread` is assigning each cursor a line of the clipboard\n    // text whenever there the line count equals the cursor count\n    // and when enabled\n    if (this._spread) {\n      const lines = clipboardText.split(/\\r\\n|\\n|\\r/).filter(s => !isFalsyOrWhitespace(s));\n      if (lines.length === this._selectionCount) {\n        return lines[this._selectionIdx];\n      }\n    }\n    return clipboardText;\n  }\n}\nlet CommentBasedVariableResolver = class CommentBasedVariableResolver {\n  constructor(_model, _selection, _languageConfigurationService) {\n    this._model = _model;\n    this._selection = _selection;\n    this._languageConfigurationService = _languageConfigurationService;\n    //\n  }\n  resolve(variable) {\n    const {\n      name\n    } = variable;\n    const langId = this._model.getLanguageIdAtPosition(this._selection.selectionStartLineNumber, this._selection.selectionStartColumn);\n    const config = this._languageConfigurationService.getLanguageConfiguration(langId).comments;\n    if (!config) {\n      return undefined;\n    }\n    if (name === 'LINE_COMMENT') {\n      return config.lineCommentToken || undefined;\n    } else if (name === 'BLOCK_COMMENT_START') {\n      return config.blockCommentStartToken || undefined;\n    } else if (name === 'BLOCK_COMMENT_END') {\n      return config.blockCommentEndToken || undefined;\n    }\n    return undefined;\n  }\n};\nCommentBasedVariableResolver = __decorate([__param(2, ILanguageConfigurationService)], CommentBasedVariableResolver);\nexport { CommentBasedVariableResolver };\nexport class TimeBasedVariableResolver {\n  constructor() {\n    this._date = new Date();\n  }\n  static {\n    this.dayNames = [nls.localize('Sunday', \"Sunday\"), nls.localize('Monday', \"Monday\"), nls.localize('Tuesday', \"Tuesday\"), nls.localize('Wednesday', \"Wednesday\"), nls.localize('Thursday', \"Thursday\"), nls.localize('Friday', \"Friday\"), nls.localize('Saturday', \"Saturday\")];\n  }\n  static {\n    this.dayNamesShort = [nls.localize('SundayShort', \"Sun\"), nls.localize('MondayShort', \"Mon\"), nls.localize('TuesdayShort', \"Tue\"), nls.localize('WednesdayShort', \"Wed\"), nls.localize('ThursdayShort', \"Thu\"), nls.localize('FridayShort', \"Fri\"), nls.localize('SaturdayShort', \"Sat\")];\n  }\n  static {\n    this.monthNames = [nls.localize('January', \"January\"), nls.localize('February', \"February\"), nls.localize('March', \"March\"), nls.localize('April', \"April\"), nls.localize('May', \"May\"), nls.localize('June', \"June\"), nls.localize('July', \"July\"), nls.localize('August', \"August\"), nls.localize('September', \"September\"), nls.localize('October', \"October\"), nls.localize('November', \"November\"), nls.localize('December', \"December\")];\n  }\n  static {\n    this.monthNamesShort = [nls.localize('JanuaryShort', \"Jan\"), nls.localize('FebruaryShort', \"Feb\"), nls.localize('MarchShort', \"Mar\"), nls.localize('AprilShort', \"Apr\"), nls.localize('MayShort', \"May\"), nls.localize('JuneShort', \"Jun\"), nls.localize('JulyShort', \"Jul\"), nls.localize('AugustShort', \"Aug\"), nls.localize('SeptemberShort', \"Sep\"), nls.localize('OctoberShort', \"Oct\"), nls.localize('NovemberShort', \"Nov\"), nls.localize('DecemberShort', \"Dec\")];\n  }\n  resolve(variable) {\n    const {\n      name\n    } = variable;\n    if (name === 'CURRENT_YEAR') {\n      return String(this._date.getFullYear());\n    } else if (name === 'CURRENT_YEAR_SHORT') {\n      return String(this._date.getFullYear()).slice(-2);\n    } else if (name === 'CURRENT_MONTH') {\n      return String(this._date.getMonth().valueOf() + 1).padStart(2, '0');\n    } else if (name === 'CURRENT_DATE') {\n      return String(this._date.getDate().valueOf()).padStart(2, '0');\n    } else if (name === 'CURRENT_HOUR') {\n      return String(this._date.getHours().valueOf()).padStart(2, '0');\n    } else if (name === 'CURRENT_MINUTE') {\n      return String(this._date.getMinutes().valueOf()).padStart(2, '0');\n    } else if (name === 'CURRENT_SECOND') {\n      return String(this._date.getSeconds().valueOf()).padStart(2, '0');\n    } else if (name === 'CURRENT_DAY_NAME') {\n      return TimeBasedVariableResolver.dayNames[this._date.getDay()];\n    } else if (name === 'CURRENT_DAY_NAME_SHORT') {\n      return TimeBasedVariableResolver.dayNamesShort[this._date.getDay()];\n    } else if (name === 'CURRENT_MONTH_NAME') {\n      return TimeBasedVariableResolver.monthNames[this._date.getMonth()];\n    } else if (name === 'CURRENT_MONTH_NAME_SHORT') {\n      return TimeBasedVariableResolver.monthNamesShort[this._date.getMonth()];\n    } else if (name === 'CURRENT_SECONDS_UNIX') {\n      return String(Math.floor(this._date.getTime() / 1000));\n    } else if (name === 'CURRENT_TIMEZONE_OFFSET') {\n      const rawTimeOffset = this._date.getTimezoneOffset();\n      const sign = rawTimeOffset > 0 ? '-' : '+';\n      const hours = Math.trunc(Math.abs(rawTimeOffset / 60));\n      const hoursString = hours < 10 ? '0' + hours : hours;\n      const minutes = Math.abs(rawTimeOffset) - hours * 60;\n      const minutesString = minutes < 10 ? '0' + minutes : minutes;\n      return sign + hoursString + ':' + minutesString;\n    }\n    return undefined;\n  }\n}\nexport class WorkspaceBasedVariableResolver {\n  constructor(_workspaceService) {\n    this._workspaceService = _workspaceService;\n    //\n  }\n  resolve(variable) {\n    if (!this._workspaceService) {\n      return undefined;\n    }\n    const workspaceIdentifier = toWorkspaceIdentifier(this._workspaceService.getWorkspace());\n    if (isEmptyWorkspaceIdentifier(workspaceIdentifier)) {\n      return undefined;\n    }\n    if (variable.name === 'WORKSPACE_NAME') {\n      return this._resolveWorkspaceName(workspaceIdentifier);\n    } else if (variable.name === 'WORKSPACE_FOLDER') {\n      return this._resoveWorkspacePath(workspaceIdentifier);\n    }\n    return undefined;\n  }\n  _resolveWorkspaceName(workspaceIdentifier) {\n    if (isSingleFolderWorkspaceIdentifier(workspaceIdentifier)) {\n      return path.basename(workspaceIdentifier.uri.path);\n    }\n    let filename = path.basename(workspaceIdentifier.configPath.path);\n    if (filename.endsWith(WORKSPACE_EXTENSION)) {\n      filename = filename.substr(0, filename.length - WORKSPACE_EXTENSION.length - 1);\n    }\n    return filename;\n  }\n  _resoveWorkspacePath(workspaceIdentifier) {\n    if (isSingleFolderWorkspaceIdentifier(workspaceIdentifier)) {\n      return normalizeDriveLetter(workspaceIdentifier.uri.fsPath);\n    }\n    const filename = path.basename(workspaceIdentifier.configPath.path);\n    let folderpath = workspaceIdentifier.configPath.fsPath;\n    if (folderpath.endsWith(filename)) {\n      folderpath = folderpath.substr(0, folderpath.length - filename.length - 1);\n    }\n    return folderpath ? normalizeDriveLetter(folderpath) : '/';\n  }\n}\nexport class RandomBasedVariableResolver {\n  resolve(variable) {\n    const {\n      name\n    } = variable;\n    if (name === 'RANDOM') {\n      return Math.random().toString().slice(-6);\n    } else if (name === 'RANDOM_HEX') {\n      return Math.random().toString(16).slice(-6);\n    } else if (name === 'UUID') {\n      return generateUuid();\n    }\n    return undefined;\n  }\n}", "map": {"version": 3, "names": ["__decorate", "decorators", "target", "key", "desc", "c", "arguments", "length", "r", "Object", "getOwnPropertyDescriptor", "d", "Reflect", "decorate", "i", "defineProperty", "__param", "paramIndex", "decorator", "normalizeDriveLetter", "path", "dirname", "commonPrefixLength", "getLeadingWhitespace", "isFalsyOrWhitespace", "splitLines", "generateUuid", "ILanguageConfigurationService", "Text", "nls", "WORKSPACE_EXTENSION", "isSingleFolderWorkspaceIdentifier", "toWorkspaceIdentifier", "isEmptyWorkspaceIdentifier", "KnownSnippetVariableNames", "freeze", "CompositeSnippetVariableResolver", "constructor", "_delegates", "resolve", "variable", "delegate", "value", "undefined", "SelectionBasedVariableResolver", "_model", "_selection", "_selectionIdx", "_overtypingCapturer", "name", "getValueInRange", "isMultiline", "startLineNumber", "endLineNumber", "info", "getLastOvertypedInfo", "multiline", "snippet", "line", "get<PERSON>ineC<PERSON>nt", "lineLeadingWhitespace", "startColumn", "varLeadingWhitespace", "walk", "marker", "pop", "whitespaceCommonLength", "replace", "m", "newline", "rest", "substr", "positionLineNumber", "getWordAtPosition", "lineNumber", "column", "positionColumn", "word", "String", "ModelBasedVariableResolver", "_labelService", "basename", "uri", "fsPath", "idx", "lastIndexOf", "slice", "getUriLabel", "relative", "noPrefix", "ClipboardBasedVariableResolver", "_readClipboardText", "_selectionCount", "_spread", "clipboardText", "lines", "split", "filter", "s", "CommentBasedVariableResolver", "_languageConfigurationService", "langId", "getLanguageIdAtPosition", "selectionStartLineNumber", "selectionStartColumn", "config", "getLanguageConfiguration", "comments", "lineCommentToken", "blockCommentStartToken", "blockCommentEndToken", "TimeBasedVariableResolver", "_date", "Date", "dayNames", "localize", "dayNamesShort", "monthNames", "monthNamesShort", "getFullYear", "getMonth", "valueOf", "padStart", "getDate", "getHours", "getMinutes", "getSeconds", "getDay", "Math", "floor", "getTime", "rawTimeOffset", "getTimezoneOffset", "sign", "hours", "trunc", "abs", "hoursString", "minutes", "minutesString", "WorkspaceBasedVariableResolver", "_workspaceService", "workspaceIdentifier", "getWorkspace", "_resolveWorkspaceName", "_resoveWorkspacePath", "filename", "config<PERSON><PERSON>", "endsWith", "folderpath", "RandomBasedVariableResolver", "random", "toString"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/contrib/snippet/browser/snippetVariables.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = (this && this.__param) || function (paramIndex, decorator) {\n    return function (target, key) { decorator(target, key, paramIndex); }\n};\nimport { normalizeDriveLetter } from '../../../../base/common/labels.js';\nimport * as path from '../../../../base/common/path.js';\nimport { dirname } from '../../../../base/common/resources.js';\nimport { commonPrefixLength, getLeadingWhitespace, isFalsyOrWhitespace, splitLines } from '../../../../base/common/strings.js';\nimport { generateUuid } from '../../../../base/common/uuid.js';\nimport { ILanguageConfigurationService } from '../../../common/languages/languageConfigurationRegistry.js';\nimport { Text } from './snippetParser.js';\nimport * as nls from '../../../../nls.js';\nimport { WORKSPACE_EXTENSION, isSingleFolderWorkspaceIdentifier, toWorkspaceIdentifier, isEmptyWorkspaceIdentifier } from '../../../../platform/workspace/common/workspace.js';\nexport const KnownSnippetVariableNames = Object.freeze({\n    'CURRENT_YEAR': true,\n    'CURRENT_YEAR_SHORT': true,\n    'CURRENT_MONTH': true,\n    'CURRENT_DATE': true,\n    'CURRENT_HOUR': true,\n    'CURRENT_MINUTE': true,\n    'CURRENT_SECOND': true,\n    'CURRENT_DAY_NAME': true,\n    'CURRENT_DAY_NAME_SHORT': true,\n    'CURRENT_MONTH_NAME': true,\n    'CURRENT_MONTH_NAME_SHORT': true,\n    'CURRENT_SECONDS_UNIX': true,\n    'CURRENT_TIMEZONE_OFFSET': true,\n    'SELECTION': true,\n    'CLIPBOARD': true,\n    'TM_SELECTED_TEXT': true,\n    'TM_CURRENT_LINE': true,\n    'TM_CURRENT_WORD': true,\n    'TM_LINE_INDEX': true,\n    'TM_LINE_NUMBER': true,\n    'TM_FILENAME': true,\n    'TM_FILENAME_BASE': true,\n    'TM_DIRECTORY': true,\n    'TM_FILEPATH': true,\n    'CURSOR_INDEX': true, // 0-offset\n    'CURSOR_NUMBER': true, // 1-offset\n    'RELATIVE_FILEPATH': true,\n    'BLOCK_COMMENT_START': true,\n    'BLOCK_COMMENT_END': true,\n    'LINE_COMMENT': true,\n    'WORKSPACE_NAME': true,\n    'WORKSPACE_FOLDER': true,\n    'RANDOM': true,\n    'RANDOM_HEX': true,\n    'UUID': true\n});\nexport class CompositeSnippetVariableResolver {\n    constructor(_delegates) {\n        this._delegates = _delegates;\n        //\n    }\n    resolve(variable) {\n        for (const delegate of this._delegates) {\n            const value = delegate.resolve(variable);\n            if (value !== undefined) {\n                return value;\n            }\n        }\n        return undefined;\n    }\n}\nexport class SelectionBasedVariableResolver {\n    constructor(_model, _selection, _selectionIdx, _overtypingCapturer) {\n        this._model = _model;\n        this._selection = _selection;\n        this._selectionIdx = _selectionIdx;\n        this._overtypingCapturer = _overtypingCapturer;\n        //\n    }\n    resolve(variable) {\n        const { name } = variable;\n        if (name === 'SELECTION' || name === 'TM_SELECTED_TEXT') {\n            let value = this._model.getValueInRange(this._selection) || undefined;\n            let isMultiline = this._selection.startLineNumber !== this._selection.endLineNumber;\n            // If there was no selected text, try to get last overtyped text\n            if (!value && this._overtypingCapturer) {\n                const info = this._overtypingCapturer.getLastOvertypedInfo(this._selectionIdx);\n                if (info) {\n                    value = info.value;\n                    isMultiline = info.multiline;\n                }\n            }\n            if (value && isMultiline && variable.snippet) {\n                // Selection is a multiline string which we indentation we now\n                // need to adjust. We compare the indentation of this variable\n                // with the indentation at the editor position and add potential\n                // extra indentation to the value\n                const line = this._model.getLineContent(this._selection.startLineNumber);\n                const lineLeadingWhitespace = getLeadingWhitespace(line, 0, this._selection.startColumn - 1);\n                let varLeadingWhitespace = lineLeadingWhitespace;\n                variable.snippet.walk(marker => {\n                    if (marker === variable) {\n                        return false;\n                    }\n                    if (marker instanceof Text) {\n                        varLeadingWhitespace = getLeadingWhitespace(splitLines(marker.value).pop());\n                    }\n                    return true;\n                });\n                const whitespaceCommonLength = commonPrefixLength(varLeadingWhitespace, lineLeadingWhitespace);\n                value = value.replace(/(\\r\\n|\\r|\\n)(.*)/g, (m, newline, rest) => `${newline}${varLeadingWhitespace.substr(whitespaceCommonLength)}${rest}`);\n            }\n            return value;\n        }\n        else if (name === 'TM_CURRENT_LINE') {\n            return this._model.getLineContent(this._selection.positionLineNumber);\n        }\n        else if (name === 'TM_CURRENT_WORD') {\n            const info = this._model.getWordAtPosition({\n                lineNumber: this._selection.positionLineNumber,\n                column: this._selection.positionColumn\n            });\n            return info && info.word || undefined;\n        }\n        else if (name === 'TM_LINE_INDEX') {\n            return String(this._selection.positionLineNumber - 1);\n        }\n        else if (name === 'TM_LINE_NUMBER') {\n            return String(this._selection.positionLineNumber);\n        }\n        else if (name === 'CURSOR_INDEX') {\n            return String(this._selectionIdx);\n        }\n        else if (name === 'CURSOR_NUMBER') {\n            return String(this._selectionIdx + 1);\n        }\n        return undefined;\n    }\n}\nexport class ModelBasedVariableResolver {\n    constructor(_labelService, _model) {\n        this._labelService = _labelService;\n        this._model = _model;\n        //\n    }\n    resolve(variable) {\n        const { name } = variable;\n        if (name === 'TM_FILENAME') {\n            return path.basename(this._model.uri.fsPath);\n        }\n        else if (name === 'TM_FILENAME_BASE') {\n            const name = path.basename(this._model.uri.fsPath);\n            const idx = name.lastIndexOf('.');\n            if (idx <= 0) {\n                return name;\n            }\n            else {\n                return name.slice(0, idx);\n            }\n        }\n        else if (name === 'TM_DIRECTORY') {\n            if (path.dirname(this._model.uri.fsPath) === '.') {\n                return '';\n            }\n            return this._labelService.getUriLabel(dirname(this._model.uri));\n        }\n        else if (name === 'TM_FILEPATH') {\n            return this._labelService.getUriLabel(this._model.uri);\n        }\n        else if (name === 'RELATIVE_FILEPATH') {\n            return this._labelService.getUriLabel(this._model.uri, { relative: true, noPrefix: true });\n        }\n        return undefined;\n    }\n}\nexport class ClipboardBasedVariableResolver {\n    constructor(_readClipboardText, _selectionIdx, _selectionCount, _spread) {\n        this._readClipboardText = _readClipboardText;\n        this._selectionIdx = _selectionIdx;\n        this._selectionCount = _selectionCount;\n        this._spread = _spread;\n        //\n    }\n    resolve(variable) {\n        if (variable.name !== 'CLIPBOARD') {\n            return undefined;\n        }\n        const clipboardText = this._readClipboardText();\n        if (!clipboardText) {\n            return undefined;\n        }\n        // `spread` is assigning each cursor a line of the clipboard\n        // text whenever there the line count equals the cursor count\n        // and when enabled\n        if (this._spread) {\n            const lines = clipboardText.split(/\\r\\n|\\n|\\r/).filter(s => !isFalsyOrWhitespace(s));\n            if (lines.length === this._selectionCount) {\n                return lines[this._selectionIdx];\n            }\n        }\n        return clipboardText;\n    }\n}\nlet CommentBasedVariableResolver = class CommentBasedVariableResolver {\n    constructor(_model, _selection, _languageConfigurationService) {\n        this._model = _model;\n        this._selection = _selection;\n        this._languageConfigurationService = _languageConfigurationService;\n        //\n    }\n    resolve(variable) {\n        const { name } = variable;\n        const langId = this._model.getLanguageIdAtPosition(this._selection.selectionStartLineNumber, this._selection.selectionStartColumn);\n        const config = this._languageConfigurationService.getLanguageConfiguration(langId).comments;\n        if (!config) {\n            return undefined;\n        }\n        if (name === 'LINE_COMMENT') {\n            return config.lineCommentToken || undefined;\n        }\n        else if (name === 'BLOCK_COMMENT_START') {\n            return config.blockCommentStartToken || undefined;\n        }\n        else if (name === 'BLOCK_COMMENT_END') {\n            return config.blockCommentEndToken || undefined;\n        }\n        return undefined;\n    }\n};\nCommentBasedVariableResolver = __decorate([\n    __param(2, ILanguageConfigurationService)\n], CommentBasedVariableResolver);\nexport { CommentBasedVariableResolver };\nexport class TimeBasedVariableResolver {\n    constructor() {\n        this._date = new Date();\n    }\n    static { this.dayNames = [nls.localize('Sunday', \"Sunday\"), nls.localize('Monday', \"Monday\"), nls.localize('Tuesday', \"Tuesday\"), nls.localize('Wednesday', \"Wednesday\"), nls.localize('Thursday', \"Thursday\"), nls.localize('Friday', \"Friday\"), nls.localize('Saturday', \"Saturday\")]; }\n    static { this.dayNamesShort = [nls.localize('SundayShort', \"Sun\"), nls.localize('MondayShort', \"Mon\"), nls.localize('TuesdayShort', \"Tue\"), nls.localize('WednesdayShort', \"Wed\"), nls.localize('ThursdayShort', \"Thu\"), nls.localize('FridayShort', \"Fri\"), nls.localize('SaturdayShort', \"Sat\")]; }\n    static { this.monthNames = [nls.localize('January', \"January\"), nls.localize('February', \"February\"), nls.localize('March', \"March\"), nls.localize('April', \"April\"), nls.localize('May', \"May\"), nls.localize('June', \"June\"), nls.localize('July', \"July\"), nls.localize('August', \"August\"), nls.localize('September', \"September\"), nls.localize('October', \"October\"), nls.localize('November', \"November\"), nls.localize('December', \"December\")]; }\n    static { this.monthNamesShort = [nls.localize('JanuaryShort', \"Jan\"), nls.localize('FebruaryShort', \"Feb\"), nls.localize('MarchShort', \"Mar\"), nls.localize('AprilShort', \"Apr\"), nls.localize('MayShort', \"May\"), nls.localize('JuneShort', \"Jun\"), nls.localize('JulyShort', \"Jul\"), nls.localize('AugustShort', \"Aug\"), nls.localize('SeptemberShort', \"Sep\"), nls.localize('OctoberShort', \"Oct\"), nls.localize('NovemberShort', \"Nov\"), nls.localize('DecemberShort', \"Dec\")]; }\n    resolve(variable) {\n        const { name } = variable;\n        if (name === 'CURRENT_YEAR') {\n            return String(this._date.getFullYear());\n        }\n        else if (name === 'CURRENT_YEAR_SHORT') {\n            return String(this._date.getFullYear()).slice(-2);\n        }\n        else if (name === 'CURRENT_MONTH') {\n            return String(this._date.getMonth().valueOf() + 1).padStart(2, '0');\n        }\n        else if (name === 'CURRENT_DATE') {\n            return String(this._date.getDate().valueOf()).padStart(2, '0');\n        }\n        else if (name === 'CURRENT_HOUR') {\n            return String(this._date.getHours().valueOf()).padStart(2, '0');\n        }\n        else if (name === 'CURRENT_MINUTE') {\n            return String(this._date.getMinutes().valueOf()).padStart(2, '0');\n        }\n        else if (name === 'CURRENT_SECOND') {\n            return String(this._date.getSeconds().valueOf()).padStart(2, '0');\n        }\n        else if (name === 'CURRENT_DAY_NAME') {\n            return TimeBasedVariableResolver.dayNames[this._date.getDay()];\n        }\n        else if (name === 'CURRENT_DAY_NAME_SHORT') {\n            return TimeBasedVariableResolver.dayNamesShort[this._date.getDay()];\n        }\n        else if (name === 'CURRENT_MONTH_NAME') {\n            return TimeBasedVariableResolver.monthNames[this._date.getMonth()];\n        }\n        else if (name === 'CURRENT_MONTH_NAME_SHORT') {\n            return TimeBasedVariableResolver.monthNamesShort[this._date.getMonth()];\n        }\n        else if (name === 'CURRENT_SECONDS_UNIX') {\n            return String(Math.floor(this._date.getTime() / 1000));\n        }\n        else if (name === 'CURRENT_TIMEZONE_OFFSET') {\n            const rawTimeOffset = this._date.getTimezoneOffset();\n            const sign = rawTimeOffset > 0 ? '-' : '+';\n            const hours = Math.trunc(Math.abs(rawTimeOffset / 60));\n            const hoursString = (hours < 10 ? '0' + hours : hours);\n            const minutes = Math.abs(rawTimeOffset) - hours * 60;\n            const minutesString = (minutes < 10 ? '0' + minutes : minutes);\n            return sign + hoursString + ':' + minutesString;\n        }\n        return undefined;\n    }\n}\nexport class WorkspaceBasedVariableResolver {\n    constructor(_workspaceService) {\n        this._workspaceService = _workspaceService;\n        //\n    }\n    resolve(variable) {\n        if (!this._workspaceService) {\n            return undefined;\n        }\n        const workspaceIdentifier = toWorkspaceIdentifier(this._workspaceService.getWorkspace());\n        if (isEmptyWorkspaceIdentifier(workspaceIdentifier)) {\n            return undefined;\n        }\n        if (variable.name === 'WORKSPACE_NAME') {\n            return this._resolveWorkspaceName(workspaceIdentifier);\n        }\n        else if (variable.name === 'WORKSPACE_FOLDER') {\n            return this._resoveWorkspacePath(workspaceIdentifier);\n        }\n        return undefined;\n    }\n    _resolveWorkspaceName(workspaceIdentifier) {\n        if (isSingleFolderWorkspaceIdentifier(workspaceIdentifier)) {\n            return path.basename(workspaceIdentifier.uri.path);\n        }\n        let filename = path.basename(workspaceIdentifier.configPath.path);\n        if (filename.endsWith(WORKSPACE_EXTENSION)) {\n            filename = filename.substr(0, filename.length - WORKSPACE_EXTENSION.length - 1);\n        }\n        return filename;\n    }\n    _resoveWorkspacePath(workspaceIdentifier) {\n        if (isSingleFolderWorkspaceIdentifier(workspaceIdentifier)) {\n            return normalizeDriveLetter(workspaceIdentifier.uri.fsPath);\n        }\n        const filename = path.basename(workspaceIdentifier.configPath.path);\n        let folderpath = workspaceIdentifier.configPath.fsPath;\n        if (folderpath.endsWith(filename)) {\n            folderpath = folderpath.substr(0, folderpath.length - filename.length - 1);\n        }\n        return (folderpath ? normalizeDriveLetter(folderpath) : '/');\n    }\n}\nexport class RandomBasedVariableResolver {\n    resolve(variable) {\n        const { name } = variable;\n        if (name === 'RANDOM') {\n            return Math.random().toString().slice(-6);\n        }\n        else if (name === 'RANDOM_HEX') {\n            return Math.random().toString(16).slice(-6);\n        }\n        else if (name === 'UUID') {\n            return generateUuid();\n        }\n        return undefined;\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,IAAIA,UAAU,GAAI,IAAI,IAAI,IAAI,CAACA,UAAU,IAAK,UAAUC,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAE;EACnF,IAAIC,CAAC,GAAGC,SAAS,CAACC,MAAM;IAAEC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGH,MAAM,GAAGE,IAAI,KAAK,IAAI,GAAGA,IAAI,GAAGK,MAAM,CAACC,wBAAwB,CAACR,MAAM,EAAEC,GAAG,CAAC,GAAGC,IAAI;IAAEO,CAAC;EAC5H,IAAI,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,CAACC,QAAQ,KAAK,UAAU,EAAEL,CAAC,GAAGI,OAAO,CAACC,QAAQ,CAACZ,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,CAAC,CAAC,KAC1H,KAAK,IAAIU,CAAC,GAAGb,UAAU,CAACM,MAAM,GAAG,CAAC,EAAEO,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE,IAAIH,CAAC,GAAGV,UAAU,CAACa,CAAC,CAAC,EAAEN,CAAC,GAAG,CAACH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACH,CAAC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACT,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,GAAGG,CAAC,CAACT,MAAM,EAAEC,GAAG,CAAC,KAAKK,CAAC;EACjJ,OAAOH,CAAC,GAAG,CAAC,IAAIG,CAAC,IAAIC,MAAM,CAACM,cAAc,CAACb,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,EAAEA,CAAC;AACjE,CAAC;AACD,IAAIQ,OAAO,GAAI,IAAI,IAAI,IAAI,CAACA,OAAO,IAAK,UAAUC,UAAU,EAAEC,SAAS,EAAE;EACrE,OAAO,UAAUhB,MAAM,EAAEC,GAAG,EAAE;IAAEe,SAAS,CAAChB,MAAM,EAAEC,GAAG,EAAEc,UAAU,CAAC;EAAE,CAAC;AACzE,CAAC;AACD,SAASE,oBAAoB,QAAQ,mCAAmC;AACxE,OAAO,KAAKC,IAAI,MAAM,iCAAiC;AACvD,SAASC,OAAO,QAAQ,sCAAsC;AAC9D,SAASC,kBAAkB,EAAEC,oBAAoB,EAAEC,mBAAmB,EAAEC,UAAU,QAAQ,oCAAoC;AAC9H,SAASC,YAAY,QAAQ,iCAAiC;AAC9D,SAASC,6BAA6B,QAAQ,4DAA4D;AAC1G,SAASC,IAAI,QAAQ,oBAAoB;AACzC,OAAO,KAAKC,GAAG,MAAM,oBAAoB;AACzC,SAASC,mBAAmB,EAAEC,iCAAiC,EAAEC,qBAAqB,EAAEC,0BAA0B,QAAQ,oDAAoD;AAC9K,OAAO,MAAMC,yBAAyB,GAAGzB,MAAM,CAAC0B,MAAM,CAAC;EACnD,cAAc,EAAE,IAAI;EACpB,oBAAoB,EAAE,IAAI;EAC1B,eAAe,EAAE,IAAI;EACrB,cAAc,EAAE,IAAI;EACpB,cAAc,EAAE,IAAI;EACpB,gBAAgB,EAAE,IAAI;EACtB,gBAAgB,EAAE,IAAI;EACtB,kBAAkB,EAAE,IAAI;EACxB,wBAAwB,EAAE,IAAI;EAC9B,oBAAoB,EAAE,IAAI;EAC1B,0BAA0B,EAAE,IAAI;EAChC,sBAAsB,EAAE,IAAI;EAC5B,yBAAyB,EAAE,IAAI;EAC/B,WAAW,EAAE,IAAI;EACjB,WAAW,EAAE,IAAI;EACjB,kBAAkB,EAAE,IAAI;EACxB,iBAAiB,EAAE,IAAI;EACvB,iBAAiB,EAAE,IAAI;EACvB,eAAe,EAAE,IAAI;EACrB,gBAAgB,EAAE,IAAI;EACtB,aAAa,EAAE,IAAI;EACnB,kBAAkB,EAAE,IAAI;EACxB,cAAc,EAAE,IAAI;EACpB,aAAa,EAAE,IAAI;EACnB,cAAc,EAAE,IAAI;EAAE;EACtB,eAAe,EAAE,IAAI;EAAE;EACvB,mBAAmB,EAAE,IAAI;EACzB,qBAAqB,EAAE,IAAI;EAC3B,mBAAmB,EAAE,IAAI;EACzB,cAAc,EAAE,IAAI;EACpB,gBAAgB,EAAE,IAAI;EACtB,kBAAkB,EAAE,IAAI;EACxB,QAAQ,EAAE,IAAI;EACd,YAAY,EAAE,IAAI;EAClB,MAAM,EAAE;AACZ,CAAC,CAAC;AACF,OAAO,MAAMC,gCAAgC,CAAC;EAC1CC,WAAWA,CAACC,UAAU,EAAE;IACpB,IAAI,CAACA,UAAU,GAAGA,UAAU;IAC5B;EACJ;EACAC,OAAOA,CAACC,QAAQ,EAAE;IACd,KAAK,MAAMC,QAAQ,IAAI,IAAI,CAACH,UAAU,EAAE;MACpC,MAAMI,KAAK,GAAGD,QAAQ,CAACF,OAAO,CAACC,QAAQ,CAAC;MACxC,IAAIE,KAAK,KAAKC,SAAS,EAAE;QACrB,OAAOD,KAAK;MAChB;IACJ;IACA,OAAOC,SAAS;EACpB;AACJ;AACA,OAAO,MAAMC,8BAA8B,CAAC;EACxCP,WAAWA,CAACQ,MAAM,EAAEC,UAAU,EAAEC,aAAa,EAAEC,mBAAmB,EAAE;IAChE,IAAI,CAACH,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,mBAAmB,GAAGA,mBAAmB;IAC9C;EACJ;EACAT,OAAOA,CAACC,QAAQ,EAAE;IACd,MAAM;MAAES;IAAK,CAAC,GAAGT,QAAQ;IACzB,IAAIS,IAAI,KAAK,WAAW,IAAIA,IAAI,KAAK,kBAAkB,EAAE;MACrD,IAAIP,KAAK,GAAG,IAAI,CAACG,MAAM,CAACK,eAAe,CAAC,IAAI,CAACJ,UAAU,CAAC,IAAIH,SAAS;MACrE,IAAIQ,WAAW,GAAG,IAAI,CAACL,UAAU,CAACM,eAAe,KAAK,IAAI,CAACN,UAAU,CAACO,aAAa;MACnF;MACA,IAAI,CAACX,KAAK,IAAI,IAAI,CAACM,mBAAmB,EAAE;QACpC,MAAMM,IAAI,GAAG,IAAI,CAACN,mBAAmB,CAACO,oBAAoB,CAAC,IAAI,CAACR,aAAa,CAAC;QAC9E,IAAIO,IAAI,EAAE;UACNZ,KAAK,GAAGY,IAAI,CAACZ,KAAK;UAClBS,WAAW,GAAGG,IAAI,CAACE,SAAS;QAChC;MACJ;MACA,IAAId,KAAK,IAAIS,WAAW,IAAIX,QAAQ,CAACiB,OAAO,EAAE;QAC1C;QACA;QACA;QACA;QACA,MAAMC,IAAI,GAAG,IAAI,CAACb,MAAM,CAACc,cAAc,CAAC,IAAI,CAACb,UAAU,CAACM,eAAe,CAAC;QACxE,MAAMQ,qBAAqB,GAAGrC,oBAAoB,CAACmC,IAAI,EAAE,CAAC,EAAE,IAAI,CAACZ,UAAU,CAACe,WAAW,GAAG,CAAC,CAAC;QAC5F,IAAIC,oBAAoB,GAAGF,qBAAqB;QAChDpB,QAAQ,CAACiB,OAAO,CAACM,IAAI,CAACC,MAAM,IAAI;UAC5B,IAAIA,MAAM,KAAKxB,QAAQ,EAAE;YACrB,OAAO,KAAK;UAChB;UACA,IAAIwB,MAAM,YAAYpC,IAAI,EAAE;YACxBkC,oBAAoB,GAAGvC,oBAAoB,CAACE,UAAU,CAACuC,MAAM,CAACtB,KAAK,CAAC,CAACuB,GAAG,CAAC,CAAC,CAAC;UAC/E;UACA,OAAO,IAAI;QACf,CAAC,CAAC;QACF,MAAMC,sBAAsB,GAAG5C,kBAAkB,CAACwC,oBAAoB,EAAEF,qBAAqB,CAAC;QAC9FlB,KAAK,GAAGA,KAAK,CAACyB,OAAO,CAAC,mBAAmB,EAAE,CAACC,CAAC,EAAEC,OAAO,EAAEC,IAAI,KAAK,GAAGD,OAAO,GAAGP,oBAAoB,CAACS,MAAM,CAACL,sBAAsB,CAAC,GAAGI,IAAI,EAAE,CAAC;MAC/I;MACA,OAAO5B,KAAK;IAChB,CAAC,MACI,IAAIO,IAAI,KAAK,iBAAiB,EAAE;MACjC,OAAO,IAAI,CAACJ,MAAM,CAACc,cAAc,CAAC,IAAI,CAACb,UAAU,CAAC0B,kBAAkB,CAAC;IACzE,CAAC,MACI,IAAIvB,IAAI,KAAK,iBAAiB,EAAE;MACjC,MAAMK,IAAI,GAAG,IAAI,CAACT,MAAM,CAAC4B,iBAAiB,CAAC;QACvCC,UAAU,EAAE,IAAI,CAAC5B,UAAU,CAAC0B,kBAAkB;QAC9CG,MAAM,EAAE,IAAI,CAAC7B,UAAU,CAAC8B;MAC5B,CAAC,CAAC;MACF,OAAOtB,IAAI,IAAIA,IAAI,CAACuB,IAAI,IAAIlC,SAAS;IACzC,CAAC,MACI,IAAIM,IAAI,KAAK,eAAe,EAAE;MAC/B,OAAO6B,MAAM,CAAC,IAAI,CAAChC,UAAU,CAAC0B,kBAAkB,GAAG,CAAC,CAAC;IACzD,CAAC,MACI,IAAIvB,IAAI,KAAK,gBAAgB,EAAE;MAChC,OAAO6B,MAAM,CAAC,IAAI,CAAChC,UAAU,CAAC0B,kBAAkB,CAAC;IACrD,CAAC,MACI,IAAIvB,IAAI,KAAK,cAAc,EAAE;MAC9B,OAAO6B,MAAM,CAAC,IAAI,CAAC/B,aAAa,CAAC;IACrC,CAAC,MACI,IAAIE,IAAI,KAAK,eAAe,EAAE;MAC/B,OAAO6B,MAAM,CAAC,IAAI,CAAC/B,aAAa,GAAG,CAAC,CAAC;IACzC;IACA,OAAOJ,SAAS;EACpB;AACJ;AACA,OAAO,MAAMoC,0BAA0B,CAAC;EACpC1C,WAAWA,CAAC2C,aAAa,EAAEnC,MAAM,EAAE;IAC/B,IAAI,CAACmC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACnC,MAAM,GAAGA,MAAM;IACpB;EACJ;EACAN,OAAOA,CAACC,QAAQ,EAAE;IACd,MAAM;MAAES;IAAK,CAAC,GAAGT,QAAQ;IACzB,IAAIS,IAAI,KAAK,aAAa,EAAE;MACxB,OAAO7B,IAAI,CAAC6D,QAAQ,CAAC,IAAI,CAACpC,MAAM,CAACqC,GAAG,CAACC,MAAM,CAAC;IAChD,CAAC,MACI,IAAIlC,IAAI,KAAK,kBAAkB,EAAE;MAClC,MAAMA,IAAI,GAAG7B,IAAI,CAAC6D,QAAQ,CAAC,IAAI,CAACpC,MAAM,CAACqC,GAAG,CAACC,MAAM,CAAC;MAClD,MAAMC,GAAG,GAAGnC,IAAI,CAACoC,WAAW,CAAC,GAAG,CAAC;MACjC,IAAID,GAAG,IAAI,CAAC,EAAE;QACV,OAAOnC,IAAI;MACf,CAAC,MACI;QACD,OAAOA,IAAI,CAACqC,KAAK,CAAC,CAAC,EAAEF,GAAG,CAAC;MAC7B;IACJ,CAAC,MACI,IAAInC,IAAI,KAAK,cAAc,EAAE;MAC9B,IAAI7B,IAAI,CAACC,OAAO,CAAC,IAAI,CAACwB,MAAM,CAACqC,GAAG,CAACC,MAAM,CAAC,KAAK,GAAG,EAAE;QAC9C,OAAO,EAAE;MACb;MACA,OAAO,IAAI,CAACH,aAAa,CAACO,WAAW,CAAClE,OAAO,CAAC,IAAI,CAACwB,MAAM,CAACqC,GAAG,CAAC,CAAC;IACnE,CAAC,MACI,IAAIjC,IAAI,KAAK,aAAa,EAAE;MAC7B,OAAO,IAAI,CAAC+B,aAAa,CAACO,WAAW,CAAC,IAAI,CAAC1C,MAAM,CAACqC,GAAG,CAAC;IAC1D,CAAC,MACI,IAAIjC,IAAI,KAAK,mBAAmB,EAAE;MACnC,OAAO,IAAI,CAAC+B,aAAa,CAACO,WAAW,CAAC,IAAI,CAAC1C,MAAM,CAACqC,GAAG,EAAE;QAAEM,QAAQ,EAAE,IAAI;QAAEC,QAAQ,EAAE;MAAK,CAAC,CAAC;IAC9F;IACA,OAAO9C,SAAS;EACpB;AACJ;AACA,OAAO,MAAM+C,8BAA8B,CAAC;EACxCrD,WAAWA,CAACsD,kBAAkB,EAAE5C,aAAa,EAAE6C,eAAe,EAAEC,OAAO,EAAE;IACrE,IAAI,CAACF,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAAC5C,aAAa,GAAGA,aAAa;IAClC,IAAI,CAAC6C,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB;EACJ;EACAtD,OAAOA,CAACC,QAAQ,EAAE;IACd,IAAIA,QAAQ,CAACS,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAON,SAAS;IACpB;IACA,MAAMmD,aAAa,GAAG,IAAI,CAACH,kBAAkB,CAAC,CAAC;IAC/C,IAAI,CAACG,aAAa,EAAE;MAChB,OAAOnD,SAAS;IACpB;IACA;IACA;IACA;IACA,IAAI,IAAI,CAACkD,OAAO,EAAE;MACd,MAAME,KAAK,GAAGD,aAAa,CAACE,KAAK,CAAC,YAAY,CAAC,CAACC,MAAM,CAACC,CAAC,IAAI,CAAC1E,mBAAmB,CAAC0E,CAAC,CAAC,CAAC;MACpF,IAAIH,KAAK,CAACxF,MAAM,KAAK,IAAI,CAACqF,eAAe,EAAE;QACvC,OAAOG,KAAK,CAAC,IAAI,CAAChD,aAAa,CAAC;MACpC;IACJ;IACA,OAAO+C,aAAa;EACxB;AACJ;AACA,IAAIK,4BAA4B,GAAG,MAAMA,4BAA4B,CAAC;EAClE9D,WAAWA,CAACQ,MAAM,EAAEC,UAAU,EAAEsD,6BAA6B,EAAE;IAC3D,IAAI,CAACvD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACsD,6BAA6B,GAAGA,6BAA6B;IAClE;EACJ;EACA7D,OAAOA,CAACC,QAAQ,EAAE;IACd,MAAM;MAAES;IAAK,CAAC,GAAGT,QAAQ;IACzB,MAAM6D,MAAM,GAAG,IAAI,CAACxD,MAAM,CAACyD,uBAAuB,CAAC,IAAI,CAACxD,UAAU,CAACyD,wBAAwB,EAAE,IAAI,CAACzD,UAAU,CAAC0D,oBAAoB,CAAC;IAClI,MAAMC,MAAM,GAAG,IAAI,CAACL,6BAA6B,CAACM,wBAAwB,CAACL,MAAM,CAAC,CAACM,QAAQ;IAC3F,IAAI,CAACF,MAAM,EAAE;MACT,OAAO9D,SAAS;IACpB;IACA,IAAIM,IAAI,KAAK,cAAc,EAAE;MACzB,OAAOwD,MAAM,CAACG,gBAAgB,IAAIjE,SAAS;IAC/C,CAAC,MACI,IAAIM,IAAI,KAAK,qBAAqB,EAAE;MACrC,OAAOwD,MAAM,CAACI,sBAAsB,IAAIlE,SAAS;IACrD,CAAC,MACI,IAAIM,IAAI,KAAK,mBAAmB,EAAE;MACnC,OAAOwD,MAAM,CAACK,oBAAoB,IAAInE,SAAS;IACnD;IACA,OAAOA,SAAS;EACpB;AACJ,CAAC;AACDwD,4BAA4B,GAAGnG,UAAU,CAAC,CACtCgB,OAAO,CAAC,CAAC,EAAEW,6BAA6B,CAAC,CAC5C,EAAEwE,4BAA4B,CAAC;AAChC,SAASA,4BAA4B;AACrC,OAAO,MAAMY,yBAAyB,CAAC;EACnC1E,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC2E,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;EAC3B;EACA;IAAS,IAAI,CAACC,QAAQ,GAAG,CAACrF,GAAG,CAACsF,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAEtF,GAAG,CAACsF,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAEtF,GAAG,CAACsF,QAAQ,CAAC,SAAS,EAAE,SAAS,CAAC,EAAEtF,GAAG,CAACsF,QAAQ,CAAC,WAAW,EAAE,WAAW,CAAC,EAAEtF,GAAG,CAACsF,QAAQ,CAAC,UAAU,EAAE,UAAU,CAAC,EAAEtF,GAAG,CAACsF,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAEtF,GAAG,CAACsF,QAAQ,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;EAAE;EACzR;IAAS,IAAI,CAACC,aAAa,GAAG,CAACvF,GAAG,CAACsF,QAAQ,CAAC,aAAa,EAAE,KAAK,CAAC,EAAEtF,GAAG,CAACsF,QAAQ,CAAC,aAAa,EAAE,KAAK,CAAC,EAAEtF,GAAG,CAACsF,QAAQ,CAAC,cAAc,EAAE,KAAK,CAAC,EAAEtF,GAAG,CAACsF,QAAQ,CAAC,gBAAgB,EAAE,KAAK,CAAC,EAAEtF,GAAG,CAACsF,QAAQ,CAAC,eAAe,EAAE,KAAK,CAAC,EAAEtF,GAAG,CAACsF,QAAQ,CAAC,aAAa,EAAE,KAAK,CAAC,EAAEtF,GAAG,CAACsF,QAAQ,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;EAAE;EACpS;IAAS,IAAI,CAACE,UAAU,GAAG,CAACxF,GAAG,CAACsF,QAAQ,CAAC,SAAS,EAAE,SAAS,CAAC,EAAEtF,GAAG,CAACsF,QAAQ,CAAC,UAAU,EAAE,UAAU,CAAC,EAAEtF,GAAG,CAACsF,QAAQ,CAAC,OAAO,EAAE,OAAO,CAAC,EAAEtF,GAAG,CAACsF,QAAQ,CAAC,OAAO,EAAE,OAAO,CAAC,EAAEtF,GAAG,CAACsF,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC,EAAEtF,GAAG,CAACsF,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC,EAAEtF,GAAG,CAACsF,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC,EAAEtF,GAAG,CAACsF,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAEtF,GAAG,CAACsF,QAAQ,CAAC,WAAW,EAAE,WAAW,CAAC,EAAEtF,GAAG,CAACsF,QAAQ,CAAC,SAAS,EAAE,SAAS,CAAC,EAAEtF,GAAG,CAACsF,QAAQ,CAAC,UAAU,EAAE,UAAU,CAAC,EAAEtF,GAAG,CAACsF,QAAQ,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;EAAE;EACzb;IAAS,IAAI,CAACG,eAAe,GAAG,CAACzF,GAAG,CAACsF,QAAQ,CAAC,cAAc,EAAE,KAAK,CAAC,EAAEtF,GAAG,CAACsF,QAAQ,CAAC,eAAe,EAAE,KAAK,CAAC,EAAEtF,GAAG,CAACsF,QAAQ,CAAC,YAAY,EAAE,KAAK,CAAC,EAAEtF,GAAG,CAACsF,QAAQ,CAAC,YAAY,EAAE,KAAK,CAAC,EAAEtF,GAAG,CAACsF,QAAQ,CAAC,UAAU,EAAE,KAAK,CAAC,EAAEtF,GAAG,CAACsF,QAAQ,CAAC,WAAW,EAAE,KAAK,CAAC,EAAEtF,GAAG,CAACsF,QAAQ,CAAC,WAAW,EAAE,KAAK,CAAC,EAAEtF,GAAG,CAACsF,QAAQ,CAAC,aAAa,EAAE,KAAK,CAAC,EAAEtF,GAAG,CAACsF,QAAQ,CAAC,gBAAgB,EAAE,KAAK,CAAC,EAAEtF,GAAG,CAACsF,QAAQ,CAAC,cAAc,EAAE,KAAK,CAAC,EAAEtF,GAAG,CAACsF,QAAQ,CAAC,eAAe,EAAE,KAAK,CAAC,EAAEtF,GAAG,CAACsF,QAAQ,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;EAAE;EACpd5E,OAAOA,CAACC,QAAQ,EAAE;IACd,MAAM;MAAES;IAAK,CAAC,GAAGT,QAAQ;IACzB,IAAIS,IAAI,KAAK,cAAc,EAAE;MACzB,OAAO6B,MAAM,CAAC,IAAI,CAACkC,KAAK,CAACO,WAAW,CAAC,CAAC,CAAC;IAC3C,CAAC,MACI,IAAItE,IAAI,KAAK,oBAAoB,EAAE;MACpC,OAAO6B,MAAM,CAAC,IAAI,CAACkC,KAAK,CAACO,WAAW,CAAC,CAAC,CAAC,CAACjC,KAAK,CAAC,CAAC,CAAC,CAAC;IACrD,CAAC,MACI,IAAIrC,IAAI,KAAK,eAAe,EAAE;MAC/B,OAAO6B,MAAM,CAAC,IAAI,CAACkC,KAAK,CAACQ,QAAQ,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACvE,CAAC,MACI,IAAIzE,IAAI,KAAK,cAAc,EAAE;MAC9B,OAAO6B,MAAM,CAAC,IAAI,CAACkC,KAAK,CAACW,OAAO,CAAC,CAAC,CAACF,OAAO,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAClE,CAAC,MACI,IAAIzE,IAAI,KAAK,cAAc,EAAE;MAC9B,OAAO6B,MAAM,CAAC,IAAI,CAACkC,KAAK,CAACY,QAAQ,CAAC,CAAC,CAACH,OAAO,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACnE,CAAC,MACI,IAAIzE,IAAI,KAAK,gBAAgB,EAAE;MAChC,OAAO6B,MAAM,CAAC,IAAI,CAACkC,KAAK,CAACa,UAAU,CAAC,CAAC,CAACJ,OAAO,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACrE,CAAC,MACI,IAAIzE,IAAI,KAAK,gBAAgB,EAAE;MAChC,OAAO6B,MAAM,CAAC,IAAI,CAACkC,KAAK,CAACc,UAAU,CAAC,CAAC,CAACL,OAAO,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACrE,CAAC,MACI,IAAIzE,IAAI,KAAK,kBAAkB,EAAE;MAClC,OAAO8D,yBAAyB,CAACG,QAAQ,CAAC,IAAI,CAACF,KAAK,CAACe,MAAM,CAAC,CAAC,CAAC;IAClE,CAAC,MACI,IAAI9E,IAAI,KAAK,wBAAwB,EAAE;MACxC,OAAO8D,yBAAyB,CAACK,aAAa,CAAC,IAAI,CAACJ,KAAK,CAACe,MAAM,CAAC,CAAC,CAAC;IACvE,CAAC,MACI,IAAI9E,IAAI,KAAK,oBAAoB,EAAE;MACpC,OAAO8D,yBAAyB,CAACM,UAAU,CAAC,IAAI,CAACL,KAAK,CAACQ,QAAQ,CAAC,CAAC,CAAC;IACtE,CAAC,MACI,IAAIvE,IAAI,KAAK,0BAA0B,EAAE;MAC1C,OAAO8D,yBAAyB,CAACO,eAAe,CAAC,IAAI,CAACN,KAAK,CAACQ,QAAQ,CAAC,CAAC,CAAC;IAC3E,CAAC,MACI,IAAIvE,IAAI,KAAK,sBAAsB,EAAE;MACtC,OAAO6B,MAAM,CAACkD,IAAI,CAACC,KAAK,CAAC,IAAI,CAACjB,KAAK,CAACkB,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;IAC1D,CAAC,MACI,IAAIjF,IAAI,KAAK,yBAAyB,EAAE;MACzC,MAAMkF,aAAa,GAAG,IAAI,CAACnB,KAAK,CAACoB,iBAAiB,CAAC,CAAC;MACpD,MAAMC,IAAI,GAAGF,aAAa,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG;MAC1C,MAAMG,KAAK,GAAGN,IAAI,CAACO,KAAK,CAACP,IAAI,CAACQ,GAAG,CAACL,aAAa,GAAG,EAAE,CAAC,CAAC;MACtD,MAAMM,WAAW,GAAIH,KAAK,GAAG,EAAE,GAAG,GAAG,GAAGA,KAAK,GAAGA,KAAM;MACtD,MAAMI,OAAO,GAAGV,IAAI,CAACQ,GAAG,CAACL,aAAa,CAAC,GAAGG,KAAK,GAAG,EAAE;MACpD,MAAMK,aAAa,GAAID,OAAO,GAAG,EAAE,GAAG,GAAG,GAAGA,OAAO,GAAGA,OAAQ;MAC9D,OAAOL,IAAI,GAAGI,WAAW,GAAG,GAAG,GAAGE,aAAa;IACnD;IACA,OAAOhG,SAAS;EACpB;AACJ;AACA,OAAO,MAAMiG,8BAA8B,CAAC;EACxCvG,WAAWA,CAACwG,iBAAiB,EAAE;IAC3B,IAAI,CAACA,iBAAiB,GAAGA,iBAAiB;IAC1C;EACJ;EACAtG,OAAOA,CAACC,QAAQ,EAAE;IACd,IAAI,CAAC,IAAI,CAACqG,iBAAiB,EAAE;MACzB,OAAOlG,SAAS;IACpB;IACA,MAAMmG,mBAAmB,GAAG9G,qBAAqB,CAAC,IAAI,CAAC6G,iBAAiB,CAACE,YAAY,CAAC,CAAC,CAAC;IACxF,IAAI9G,0BAA0B,CAAC6G,mBAAmB,CAAC,EAAE;MACjD,OAAOnG,SAAS;IACpB;IACA,IAAIH,QAAQ,CAACS,IAAI,KAAK,gBAAgB,EAAE;MACpC,OAAO,IAAI,CAAC+F,qBAAqB,CAACF,mBAAmB,CAAC;IAC1D,CAAC,MACI,IAAItG,QAAQ,CAACS,IAAI,KAAK,kBAAkB,EAAE;MAC3C,OAAO,IAAI,CAACgG,oBAAoB,CAACH,mBAAmB,CAAC;IACzD;IACA,OAAOnG,SAAS;EACpB;EACAqG,qBAAqBA,CAACF,mBAAmB,EAAE;IACvC,IAAI/G,iCAAiC,CAAC+G,mBAAmB,CAAC,EAAE;MACxD,OAAO1H,IAAI,CAAC6D,QAAQ,CAAC6D,mBAAmB,CAAC5D,GAAG,CAAC9D,IAAI,CAAC;IACtD;IACA,IAAI8H,QAAQ,GAAG9H,IAAI,CAAC6D,QAAQ,CAAC6D,mBAAmB,CAACK,UAAU,CAAC/H,IAAI,CAAC;IACjE,IAAI8H,QAAQ,CAACE,QAAQ,CAACtH,mBAAmB,CAAC,EAAE;MACxCoH,QAAQ,GAAGA,QAAQ,CAAC3E,MAAM,CAAC,CAAC,EAAE2E,QAAQ,CAAC3I,MAAM,GAAGuB,mBAAmB,CAACvB,MAAM,GAAG,CAAC,CAAC;IACnF;IACA,OAAO2I,QAAQ;EACnB;EACAD,oBAAoBA,CAACH,mBAAmB,EAAE;IACtC,IAAI/G,iCAAiC,CAAC+G,mBAAmB,CAAC,EAAE;MACxD,OAAO3H,oBAAoB,CAAC2H,mBAAmB,CAAC5D,GAAG,CAACC,MAAM,CAAC;IAC/D;IACA,MAAM+D,QAAQ,GAAG9H,IAAI,CAAC6D,QAAQ,CAAC6D,mBAAmB,CAACK,UAAU,CAAC/H,IAAI,CAAC;IACnE,IAAIiI,UAAU,GAAGP,mBAAmB,CAACK,UAAU,CAAChE,MAAM;IACtD,IAAIkE,UAAU,CAACD,QAAQ,CAACF,QAAQ,CAAC,EAAE;MAC/BG,UAAU,GAAGA,UAAU,CAAC9E,MAAM,CAAC,CAAC,EAAE8E,UAAU,CAAC9I,MAAM,GAAG2I,QAAQ,CAAC3I,MAAM,GAAG,CAAC,CAAC;IAC9E;IACA,OAAQ8I,UAAU,GAAGlI,oBAAoB,CAACkI,UAAU,CAAC,GAAG,GAAG;EAC/D;AACJ;AACA,OAAO,MAAMC,2BAA2B,CAAC;EACrC/G,OAAOA,CAACC,QAAQ,EAAE;IACd,MAAM;MAAES;IAAK,CAAC,GAAGT,QAAQ;IACzB,IAAIS,IAAI,KAAK,QAAQ,EAAE;MACnB,OAAO+E,IAAI,CAACuB,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAClE,KAAK,CAAC,CAAC,CAAC,CAAC;IAC7C,CAAC,MACI,IAAIrC,IAAI,KAAK,YAAY,EAAE;MAC5B,OAAO+E,IAAI,CAACuB,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAAClE,KAAK,CAAC,CAAC,CAAC,CAAC;IAC/C,CAAC,MACI,IAAIrC,IAAI,KAAK,MAAM,EAAE;MACtB,OAAOvB,YAAY,CAAC,CAAC;IACzB;IACA,OAAOiB,SAAS;EACpB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}