{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = this && this.__param || function (paramIndex, decorator) {\n  return function (target, key) {\n    decorator(target, key, paramIndex);\n  };\n};\nimport { Disposable, toDisposable } from '../../../../base/common/lifecycle.js';\nimport { derived, observableFromEvent, observableValue } from '../../../../base/common/observable.js';\nimport './inlineEdit.css';\nimport { Position } from '../../../common/core/position.js';\nimport { Range } from '../../../common/core/range.js';\nimport { ILanguageService } from '../../../common/languages/language.js';\nimport { InjectedTextCursorStops } from '../../../common/model.js';\nimport { LineDecoration } from '../../../common/viewLayout/lineDecorations.js';\nimport { ColumnRange, applyObservableDecorations } from '../../inlineCompletions/browser/utils.js';\nimport { diffDeleteDecoration, diffLineDeleteDecorationBackgroundWithIndicator } from '../../../browser/widget/diffEditor/registrations.contribution.js';\nexport const INLINE_EDIT_DESCRIPTION = 'inline-edit';\nlet GhostTextWidget = class GhostTextWidget extends Disposable {\n  constructor(editor, model, languageService) {\n    super();\n    this.editor = editor;\n    this.model = model;\n    this.languageService = languageService;\n    this.isDisposed = observableValue(this, false);\n    this.currentTextModel = observableFromEvent(this, this.editor.onDidChangeModel, () => /** @description editor.model */this.editor.getModel());\n    this.uiState = derived(this, reader => {\n      if (this.isDisposed.read(reader)) {\n        return undefined;\n      }\n      const textModel = this.currentTextModel.read(reader);\n      if (textModel !== this.model.targetTextModel.read(reader)) {\n        return undefined;\n      }\n      const ghostText = this.model.ghostText.read(reader);\n      if (!ghostText) {\n        return undefined;\n      }\n      let range = this.model.range?.read(reader);\n      //if range is empty, we want to remove it\n      if (range && range.startLineNumber === range.endLineNumber && range.startColumn === range.endColumn) {\n        range = undefined;\n      }\n      //check if both range and text are single line - in this case we want to do inline replacement\n      //rather than replacing whole lines\n      const isSingleLine = (range ? range.startLineNumber === range.endLineNumber : true) && ghostText.parts.length === 1 && ghostText.parts[0].lines.length === 1;\n      //check if we're just removing code\n      const isPureRemove = ghostText.parts.length === 1 && ghostText.parts[0].lines.every(l => l.length === 0);\n      const inlineTexts = [];\n      const additionalLines = [];\n      function addToAdditionalLines(lines, className) {\n        if (additionalLines.length > 0) {\n          const lastLine = additionalLines[additionalLines.length - 1];\n          if (className) {\n            lastLine.decorations.push(new LineDecoration(lastLine.content.length + 1, lastLine.content.length + 1 + lines[0].length, className, 0 /* InlineDecorationType.Regular */));\n          }\n          lastLine.content += lines[0];\n          lines = lines.slice(1);\n        }\n        for (const line of lines) {\n          additionalLines.push({\n            content: line,\n            decorations: className ? [new LineDecoration(1, line.length + 1, className, 0 /* InlineDecorationType.Regular */)] : []\n          });\n        }\n      }\n      const textBufferLine = textModel.getLineContent(ghostText.lineNumber);\n      let hiddenTextStartColumn = undefined;\n      let lastIdx = 0;\n      if (!isPureRemove && (isSingleLine || !range)) {\n        for (const part of ghostText.parts) {\n          let lines = part.lines;\n          //If remove range is set, we want to push all new liens to virtual area\n          if (range && !isSingleLine) {\n            addToAdditionalLines(lines, INLINE_EDIT_DESCRIPTION);\n            lines = [];\n          }\n          if (hiddenTextStartColumn === undefined) {\n            inlineTexts.push({\n              column: part.column,\n              text: lines[0],\n              preview: part.preview\n            });\n            lines = lines.slice(1);\n          } else {\n            addToAdditionalLines([textBufferLine.substring(lastIdx, part.column - 1)], undefined);\n          }\n          if (lines.length > 0) {\n            addToAdditionalLines(lines, INLINE_EDIT_DESCRIPTION);\n            if (hiddenTextStartColumn === undefined && part.column <= textBufferLine.length) {\n              hiddenTextStartColumn = part.column;\n            }\n          }\n          lastIdx = part.column - 1;\n        }\n        if (hiddenTextStartColumn !== undefined) {\n          addToAdditionalLines([textBufferLine.substring(lastIdx)], undefined);\n        }\n      }\n      const hiddenRange = hiddenTextStartColumn !== undefined ? new ColumnRange(hiddenTextStartColumn, textBufferLine.length + 1) : undefined;\n      const lineNumber = isSingleLine || !range ? ghostText.lineNumber : range.endLineNumber - 1;\n      return {\n        inlineTexts,\n        additionalLines,\n        hiddenRange,\n        lineNumber,\n        additionalReservedLineCount: this.model.minReservedLineCount.read(reader),\n        targetTextModel: textModel,\n        range,\n        isSingleLine,\n        isPureRemove\n      };\n    });\n    this.decorations = derived(this, reader => {\n      const uiState = this.uiState.read(reader);\n      if (!uiState) {\n        return [];\n      }\n      const decorations = [];\n      if (uiState.hiddenRange) {\n        decorations.push({\n          range: uiState.hiddenRange.toRange(uiState.lineNumber),\n          options: {\n            inlineClassName: 'inline-edit-hidden',\n            description: 'inline-edit-hidden'\n          }\n        });\n      }\n      if (uiState.range) {\n        const ranges = [];\n        if (uiState.isSingleLine) {\n          ranges.push(uiState.range);\n        } else if (!uiState.isPureRemove) {\n          const lines = uiState.range.endLineNumber - uiState.range.startLineNumber;\n          for (let i = 0; i < lines; i++) {\n            const line = uiState.range.startLineNumber + i;\n            const firstNonWhitespace = uiState.targetTextModel.getLineFirstNonWhitespaceColumn(line);\n            const lastNonWhitespace = uiState.targetTextModel.getLineLastNonWhitespaceColumn(line);\n            const range = new Range(line, firstNonWhitespace, line, lastNonWhitespace);\n            ranges.push(range);\n          }\n        }\n        for (const range of ranges) {\n          decorations.push({\n            range,\n            options: diffDeleteDecoration\n          });\n        }\n      }\n      if (uiState.range && !uiState.isSingleLine && uiState.isPureRemove) {\n        const r = new Range(uiState.range.startLineNumber, 1, uiState.range.endLineNumber - 1, 1);\n        decorations.push({\n          range: r,\n          options: diffLineDeleteDecorationBackgroundWithIndicator\n        });\n      }\n      for (const p of uiState.inlineTexts) {\n        decorations.push({\n          range: Range.fromPositions(new Position(uiState.lineNumber, p.column)),\n          options: {\n            description: INLINE_EDIT_DESCRIPTION,\n            after: {\n              content: p.text,\n              inlineClassName: p.preview ? 'inline-edit-decoration-preview' : 'inline-edit-decoration',\n              cursorStops: InjectedTextCursorStops.Left\n            },\n            showIfCollapsed: true\n          }\n        });\n      }\n      return decorations;\n    });\n    this._register(toDisposable(() => {\n      this.isDisposed.set(true, undefined);\n    }));\n    this._register(applyObservableDecorations(this.editor, this.decorations));\n  }\n};\nGhostTextWidget = __decorate([__param(2, ILanguageService)], GhostTextWidget);\nexport { GhostTextWidget };", "map": {"version": 3, "names": ["__decorate", "decorators", "target", "key", "desc", "c", "arguments", "length", "r", "Object", "getOwnPropertyDescriptor", "d", "Reflect", "decorate", "i", "defineProperty", "__param", "paramIndex", "decorator", "Disposable", "toDisposable", "derived", "observableFromEvent", "observableValue", "Position", "Range", "ILanguageService", "InjectedTextCursorStops", "LineDecoration", "ColumnRange", "applyObservableDecorations", "diffDeleteDecoration", "diffLineDeleteDecorationBackgroundWithIndicator", "INLINE_EDIT_DESCRIPTION", "GhostTextWidget", "constructor", "editor", "model", "languageService", "isDisposed", "currentTextModel", "onDidChangeModel", "getModel", "uiState", "reader", "read", "undefined", "textModel", "targetTextModel", "ghostText", "range", "startLineNumber", "endLineNumber", "startColumn", "endColumn", "isSingleLine", "parts", "lines", "isPureRemove", "every", "l", "inlineTexts", "additionalLines", "addToAdditionalLines", "className", "lastLine", "decorations", "push", "content", "slice", "line", "textBufferLine", "get<PERSON>ineC<PERSON>nt", "lineNumber", "hiddenTextStartColumn", "lastIdx", "part", "column", "text", "preview", "substring", "hiddenRange", "additionalReservedLineCount", "minReservedLineCount", "to<PERSON><PERSON><PERSON>", "options", "inlineClassName", "description", "ranges", "firstNonWhitespace", "getLineFirstNonWhitespaceColumn", "lastNonWhitespace", "getLineLastNonWhitespaceColumn", "p", "fromPositions", "after", "cursorStops", "Left", "showIfCollapsed", "_register", "set"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/contrib/inlineEdit/browser/ghostTextWidget.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = (this && this.__param) || function (paramIndex, decorator) {\n    return function (target, key) { decorator(target, key, paramIndex); }\n};\nimport { Disposable, toDisposable } from '../../../../base/common/lifecycle.js';\nimport { derived, observableFromEvent, observableValue } from '../../../../base/common/observable.js';\nimport './inlineEdit.css';\nimport { Position } from '../../../common/core/position.js';\nimport { Range } from '../../../common/core/range.js';\nimport { ILanguageService } from '../../../common/languages/language.js';\nimport { InjectedTextCursorStops } from '../../../common/model.js';\nimport { LineDecoration } from '../../../common/viewLayout/lineDecorations.js';\nimport { ColumnRange, applyObservableDecorations } from '../../inlineCompletions/browser/utils.js';\nimport { diffDeleteDecoration, diffLineDeleteDecorationBackgroundWithIndicator } from '../../../browser/widget/diffEditor/registrations.contribution.js';\nexport const INLINE_EDIT_DESCRIPTION = 'inline-edit';\nlet GhostTextWidget = class GhostTextWidget extends Disposable {\n    constructor(editor, model, languageService) {\n        super();\n        this.editor = editor;\n        this.model = model;\n        this.languageService = languageService;\n        this.isDisposed = observableValue(this, false);\n        this.currentTextModel = observableFromEvent(this, this.editor.onDidChangeModel, () => /** @description editor.model */ this.editor.getModel());\n        this.uiState = derived(this, reader => {\n            if (this.isDisposed.read(reader)) {\n                return undefined;\n            }\n            const textModel = this.currentTextModel.read(reader);\n            if (textModel !== this.model.targetTextModel.read(reader)) {\n                return undefined;\n            }\n            const ghostText = this.model.ghostText.read(reader);\n            if (!ghostText) {\n                return undefined;\n            }\n            let range = this.model.range?.read(reader);\n            //if range is empty, we want to remove it\n            if (range && range.startLineNumber === range.endLineNumber && range.startColumn === range.endColumn) {\n                range = undefined;\n            }\n            //check if both range and text are single line - in this case we want to do inline replacement\n            //rather than replacing whole lines\n            const isSingleLine = (range ? range.startLineNumber === range.endLineNumber : true) && ghostText.parts.length === 1 && ghostText.parts[0].lines.length === 1;\n            //check if we're just removing code\n            const isPureRemove = ghostText.parts.length === 1 && ghostText.parts[0].lines.every(l => l.length === 0);\n            const inlineTexts = [];\n            const additionalLines = [];\n            function addToAdditionalLines(lines, className) {\n                if (additionalLines.length > 0) {\n                    const lastLine = additionalLines[additionalLines.length - 1];\n                    if (className) {\n                        lastLine.decorations.push(new LineDecoration(lastLine.content.length + 1, lastLine.content.length + 1 + lines[0].length, className, 0 /* InlineDecorationType.Regular */));\n                    }\n                    lastLine.content += lines[0];\n                    lines = lines.slice(1);\n                }\n                for (const line of lines) {\n                    additionalLines.push({\n                        content: line,\n                        decorations: className ? [new LineDecoration(1, line.length + 1, className, 0 /* InlineDecorationType.Regular */)] : []\n                    });\n                }\n            }\n            const textBufferLine = textModel.getLineContent(ghostText.lineNumber);\n            let hiddenTextStartColumn = undefined;\n            let lastIdx = 0;\n            if (!isPureRemove && (isSingleLine || !range)) {\n                for (const part of ghostText.parts) {\n                    let lines = part.lines;\n                    //If remove range is set, we want to push all new liens to virtual area\n                    if (range && !isSingleLine) {\n                        addToAdditionalLines(lines, INLINE_EDIT_DESCRIPTION);\n                        lines = [];\n                    }\n                    if (hiddenTextStartColumn === undefined) {\n                        inlineTexts.push({\n                            column: part.column,\n                            text: lines[0],\n                            preview: part.preview,\n                        });\n                        lines = lines.slice(1);\n                    }\n                    else {\n                        addToAdditionalLines([textBufferLine.substring(lastIdx, part.column - 1)], undefined);\n                    }\n                    if (lines.length > 0) {\n                        addToAdditionalLines(lines, INLINE_EDIT_DESCRIPTION);\n                        if (hiddenTextStartColumn === undefined && part.column <= textBufferLine.length) {\n                            hiddenTextStartColumn = part.column;\n                        }\n                    }\n                    lastIdx = part.column - 1;\n                }\n                if (hiddenTextStartColumn !== undefined) {\n                    addToAdditionalLines([textBufferLine.substring(lastIdx)], undefined);\n                }\n            }\n            const hiddenRange = hiddenTextStartColumn !== undefined ? new ColumnRange(hiddenTextStartColumn, textBufferLine.length + 1) : undefined;\n            const lineNumber = (isSingleLine || !range) ? ghostText.lineNumber : range.endLineNumber - 1;\n            return {\n                inlineTexts,\n                additionalLines,\n                hiddenRange,\n                lineNumber,\n                additionalReservedLineCount: this.model.minReservedLineCount.read(reader),\n                targetTextModel: textModel,\n                range,\n                isSingleLine,\n                isPureRemove,\n            };\n        });\n        this.decorations = derived(this, reader => {\n            const uiState = this.uiState.read(reader);\n            if (!uiState) {\n                return [];\n            }\n            const decorations = [];\n            if (uiState.hiddenRange) {\n                decorations.push({\n                    range: uiState.hiddenRange.toRange(uiState.lineNumber),\n                    options: { inlineClassName: 'inline-edit-hidden', description: 'inline-edit-hidden', }\n                });\n            }\n            if (uiState.range) {\n                const ranges = [];\n                if (uiState.isSingleLine) {\n                    ranges.push(uiState.range);\n                }\n                else if (!uiState.isPureRemove) {\n                    const lines = uiState.range.endLineNumber - uiState.range.startLineNumber;\n                    for (let i = 0; i < lines; i++) {\n                        const line = uiState.range.startLineNumber + i;\n                        const firstNonWhitespace = uiState.targetTextModel.getLineFirstNonWhitespaceColumn(line);\n                        const lastNonWhitespace = uiState.targetTextModel.getLineLastNonWhitespaceColumn(line);\n                        const range = new Range(line, firstNonWhitespace, line, lastNonWhitespace);\n                        ranges.push(range);\n                    }\n                }\n                for (const range of ranges) {\n                    decorations.push({\n                        range,\n                        options: diffDeleteDecoration\n                    });\n                }\n            }\n            if (uiState.range && !uiState.isSingleLine && uiState.isPureRemove) {\n                const r = new Range(uiState.range.startLineNumber, 1, uiState.range.endLineNumber - 1, 1);\n                decorations.push({\n                    range: r,\n                    options: diffLineDeleteDecorationBackgroundWithIndicator\n                });\n            }\n            for (const p of uiState.inlineTexts) {\n                decorations.push({\n                    range: Range.fromPositions(new Position(uiState.lineNumber, p.column)),\n                    options: {\n                        description: INLINE_EDIT_DESCRIPTION,\n                        after: { content: p.text, inlineClassName: p.preview ? 'inline-edit-decoration-preview' : 'inline-edit-decoration', cursorStops: InjectedTextCursorStops.Left },\n                        showIfCollapsed: true,\n                    }\n                });\n            }\n            return decorations;\n        });\n        this._register(toDisposable(() => { this.isDisposed.set(true, undefined); }));\n        this._register(applyObservableDecorations(this.editor, this.decorations));\n    }\n};\nGhostTextWidget = __decorate([\n    __param(2, ILanguageService)\n], GhostTextWidget);\nexport { GhostTextWidget };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,IAAIA,UAAU,GAAI,IAAI,IAAI,IAAI,CAACA,UAAU,IAAK,UAAUC,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAE;EACnF,IAAIC,CAAC,GAAGC,SAAS,CAACC,MAAM;IAAEC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGH,MAAM,GAAGE,IAAI,KAAK,IAAI,GAAGA,IAAI,GAAGK,MAAM,CAACC,wBAAwB,CAACR,MAAM,EAAEC,GAAG,CAAC,GAAGC,IAAI;IAAEO,CAAC;EAC5H,IAAI,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,CAACC,QAAQ,KAAK,UAAU,EAAEL,CAAC,GAAGI,OAAO,CAACC,QAAQ,CAACZ,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,CAAC,CAAC,KAC1H,KAAK,IAAIU,CAAC,GAAGb,UAAU,CAACM,MAAM,GAAG,CAAC,EAAEO,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE,IAAIH,CAAC,GAAGV,UAAU,CAACa,CAAC,CAAC,EAAEN,CAAC,GAAG,CAACH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACH,CAAC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACT,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,GAAGG,CAAC,CAACT,MAAM,EAAEC,GAAG,CAAC,KAAKK,CAAC;EACjJ,OAAOH,CAAC,GAAG,CAAC,IAAIG,CAAC,IAAIC,MAAM,CAACM,cAAc,CAACb,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,EAAEA,CAAC;AACjE,CAAC;AACD,IAAIQ,OAAO,GAAI,IAAI,IAAI,IAAI,CAACA,OAAO,IAAK,UAAUC,UAAU,EAAEC,SAAS,EAAE;EACrE,OAAO,UAAUhB,MAAM,EAAEC,GAAG,EAAE;IAAEe,SAAS,CAAChB,MAAM,EAAEC,GAAG,EAAEc,UAAU,CAAC;EAAE,CAAC;AACzE,CAAC;AACD,SAASE,UAAU,EAAEC,YAAY,QAAQ,sCAAsC;AAC/E,SAASC,OAAO,EAAEC,mBAAmB,EAAEC,eAAe,QAAQ,uCAAuC;AACrG,OAAO,kBAAkB;AACzB,SAASC,QAAQ,QAAQ,kCAAkC;AAC3D,SAASC,KAAK,QAAQ,+BAA+B;AACrD,SAASC,gBAAgB,QAAQ,uCAAuC;AACxE,SAASC,uBAAuB,QAAQ,0BAA0B;AAClE,SAASC,cAAc,QAAQ,+CAA+C;AAC9E,SAASC,WAAW,EAAEC,0BAA0B,QAAQ,0CAA0C;AAClG,SAASC,oBAAoB,EAAEC,+CAA+C,QAAQ,kEAAkE;AACxJ,OAAO,MAAMC,uBAAuB,GAAG,aAAa;AACpD,IAAIC,eAAe,GAAG,MAAMA,eAAe,SAASf,UAAU,CAAC;EAC3DgB,WAAWA,CAACC,MAAM,EAAEC,KAAK,EAAEC,eAAe,EAAE;IACxC,KAAK,CAAC,CAAC;IACP,IAAI,CAACF,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,UAAU,GAAGhB,eAAe,CAAC,IAAI,EAAE,KAAK,CAAC;IAC9C,IAAI,CAACiB,gBAAgB,GAAGlB,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAACc,MAAM,CAACK,gBAAgB,EAAE,MAAM,gCAAiC,IAAI,CAACL,MAAM,CAACM,QAAQ,CAAC,CAAC,CAAC;IAC9I,IAAI,CAACC,OAAO,GAAGtB,OAAO,CAAC,IAAI,EAAEuB,MAAM,IAAI;MACnC,IAAI,IAAI,CAACL,UAAU,CAACM,IAAI,CAACD,MAAM,CAAC,EAAE;QAC9B,OAAOE,SAAS;MACpB;MACA,MAAMC,SAAS,GAAG,IAAI,CAACP,gBAAgB,CAACK,IAAI,CAACD,MAAM,CAAC;MACpD,IAAIG,SAAS,KAAK,IAAI,CAACV,KAAK,CAACW,eAAe,CAACH,IAAI,CAACD,MAAM,CAAC,EAAE;QACvD,OAAOE,SAAS;MACpB;MACA,MAAMG,SAAS,GAAG,IAAI,CAACZ,KAAK,CAACY,SAAS,CAACJ,IAAI,CAACD,MAAM,CAAC;MACnD,IAAI,CAACK,SAAS,EAAE;QACZ,OAAOH,SAAS;MACpB;MACA,IAAII,KAAK,GAAG,IAAI,CAACb,KAAK,CAACa,KAAK,EAAEL,IAAI,CAACD,MAAM,CAAC;MAC1C;MACA,IAAIM,KAAK,IAAIA,KAAK,CAACC,eAAe,KAAKD,KAAK,CAACE,aAAa,IAAIF,KAAK,CAACG,WAAW,KAAKH,KAAK,CAACI,SAAS,EAAE;QACjGJ,KAAK,GAAGJ,SAAS;MACrB;MACA;MACA;MACA,MAAMS,YAAY,GAAG,CAACL,KAAK,GAAGA,KAAK,CAACC,eAAe,KAAKD,KAAK,CAACE,aAAa,GAAG,IAAI,KAAKH,SAAS,CAACO,KAAK,CAACjD,MAAM,KAAK,CAAC,IAAI0C,SAAS,CAACO,KAAK,CAAC,CAAC,CAAC,CAACC,KAAK,CAAClD,MAAM,KAAK,CAAC;MAC5J;MACA,MAAMmD,YAAY,GAAGT,SAAS,CAACO,KAAK,CAACjD,MAAM,KAAK,CAAC,IAAI0C,SAAS,CAACO,KAAK,CAAC,CAAC,CAAC,CAACC,KAAK,CAACE,KAAK,CAACC,CAAC,IAAIA,CAAC,CAACrD,MAAM,KAAK,CAAC,CAAC;MACxG,MAAMsD,WAAW,GAAG,EAAE;MACtB,MAAMC,eAAe,GAAG,EAAE;MAC1B,SAASC,oBAAoBA,CAACN,KAAK,EAAEO,SAAS,EAAE;QAC5C,IAAIF,eAAe,CAACvD,MAAM,GAAG,CAAC,EAAE;UAC5B,MAAM0D,QAAQ,GAAGH,eAAe,CAACA,eAAe,CAACvD,MAAM,GAAG,CAAC,CAAC;UAC5D,IAAIyD,SAAS,EAAE;YACXC,QAAQ,CAACC,WAAW,CAACC,IAAI,CAAC,IAAIvC,cAAc,CAACqC,QAAQ,CAACG,OAAO,CAAC7D,MAAM,GAAG,CAAC,EAAE0D,QAAQ,CAACG,OAAO,CAAC7D,MAAM,GAAG,CAAC,GAAGkD,KAAK,CAAC,CAAC,CAAC,CAAClD,MAAM,EAAEyD,SAAS,EAAE,CAAC,CAAC,kCAAkC,CAAC,CAAC;UAC9K;UACAC,QAAQ,CAACG,OAAO,IAAIX,KAAK,CAAC,CAAC,CAAC;UAC5BA,KAAK,GAAGA,KAAK,CAACY,KAAK,CAAC,CAAC,CAAC;QAC1B;QACA,KAAK,MAAMC,IAAI,IAAIb,KAAK,EAAE;UACtBK,eAAe,CAACK,IAAI,CAAC;YACjBC,OAAO,EAAEE,IAAI;YACbJ,WAAW,EAAEF,SAAS,GAAG,CAAC,IAAIpC,cAAc,CAAC,CAAC,EAAE0C,IAAI,CAAC/D,MAAM,GAAG,CAAC,EAAEyD,SAAS,EAAE,CAAC,CAAC,kCAAkC,CAAC,CAAC,GAAG;UACzH,CAAC,CAAC;QACN;MACJ;MACA,MAAMO,cAAc,GAAGxB,SAAS,CAACyB,cAAc,CAACvB,SAAS,CAACwB,UAAU,CAAC;MACrE,IAAIC,qBAAqB,GAAG5B,SAAS;MACrC,IAAI6B,OAAO,GAAG,CAAC;MACf,IAAI,CAACjB,YAAY,KAAKH,YAAY,IAAI,CAACL,KAAK,CAAC,EAAE;QAC3C,KAAK,MAAM0B,IAAI,IAAI3B,SAAS,CAACO,KAAK,EAAE;UAChC,IAAIC,KAAK,GAAGmB,IAAI,CAACnB,KAAK;UACtB;UACA,IAAIP,KAAK,IAAI,CAACK,YAAY,EAAE;YACxBQ,oBAAoB,CAACN,KAAK,EAAExB,uBAAuB,CAAC;YACpDwB,KAAK,GAAG,EAAE;UACd;UACA,IAAIiB,qBAAqB,KAAK5B,SAAS,EAAE;YACrCe,WAAW,CAACM,IAAI,CAAC;cACbU,MAAM,EAAED,IAAI,CAACC,MAAM;cACnBC,IAAI,EAAErB,KAAK,CAAC,CAAC,CAAC;cACdsB,OAAO,EAAEH,IAAI,CAACG;YAClB,CAAC,CAAC;YACFtB,KAAK,GAAGA,KAAK,CAACY,KAAK,CAAC,CAAC,CAAC;UAC1B,CAAC,MACI;YACDN,oBAAoB,CAAC,CAACQ,cAAc,CAACS,SAAS,CAACL,OAAO,EAAEC,IAAI,CAACC,MAAM,GAAG,CAAC,CAAC,CAAC,EAAE/B,SAAS,CAAC;UACzF;UACA,IAAIW,KAAK,CAAClD,MAAM,GAAG,CAAC,EAAE;YAClBwD,oBAAoB,CAACN,KAAK,EAAExB,uBAAuB,CAAC;YACpD,IAAIyC,qBAAqB,KAAK5B,SAAS,IAAI8B,IAAI,CAACC,MAAM,IAAIN,cAAc,CAAChE,MAAM,EAAE;cAC7EmE,qBAAqB,GAAGE,IAAI,CAACC,MAAM;YACvC;UACJ;UACAF,OAAO,GAAGC,IAAI,CAACC,MAAM,GAAG,CAAC;QAC7B;QACA,IAAIH,qBAAqB,KAAK5B,SAAS,EAAE;UACrCiB,oBAAoB,CAAC,CAACQ,cAAc,CAACS,SAAS,CAACL,OAAO,CAAC,CAAC,EAAE7B,SAAS,CAAC;QACxE;MACJ;MACA,MAAMmC,WAAW,GAAGP,qBAAqB,KAAK5B,SAAS,GAAG,IAAIjB,WAAW,CAAC6C,qBAAqB,EAAEH,cAAc,CAAChE,MAAM,GAAG,CAAC,CAAC,GAAGuC,SAAS;MACvI,MAAM2B,UAAU,GAAIlB,YAAY,IAAI,CAACL,KAAK,GAAID,SAAS,CAACwB,UAAU,GAAGvB,KAAK,CAACE,aAAa,GAAG,CAAC;MAC5F,OAAO;QACHS,WAAW;QACXC,eAAe;QACfmB,WAAW;QACXR,UAAU;QACVS,2BAA2B,EAAE,IAAI,CAAC7C,KAAK,CAAC8C,oBAAoB,CAACtC,IAAI,CAACD,MAAM,CAAC;QACzEI,eAAe,EAAED,SAAS;QAC1BG,KAAK;QACLK,YAAY;QACZG;MACJ,CAAC;IACL,CAAC,CAAC;IACF,IAAI,CAACQ,WAAW,GAAG7C,OAAO,CAAC,IAAI,EAAEuB,MAAM,IAAI;MACvC,MAAMD,OAAO,GAAG,IAAI,CAACA,OAAO,CAACE,IAAI,CAACD,MAAM,CAAC;MACzC,IAAI,CAACD,OAAO,EAAE;QACV,OAAO,EAAE;MACb;MACA,MAAMuB,WAAW,GAAG,EAAE;MACtB,IAAIvB,OAAO,CAACsC,WAAW,EAAE;QACrBf,WAAW,CAACC,IAAI,CAAC;UACbjB,KAAK,EAAEP,OAAO,CAACsC,WAAW,CAACG,OAAO,CAACzC,OAAO,CAAC8B,UAAU,CAAC;UACtDY,OAAO,EAAE;YAAEC,eAAe,EAAE,oBAAoB;YAAEC,WAAW,EAAE;UAAsB;QACzF,CAAC,CAAC;MACN;MACA,IAAI5C,OAAO,CAACO,KAAK,EAAE;QACf,MAAMsC,MAAM,GAAG,EAAE;QACjB,IAAI7C,OAAO,CAACY,YAAY,EAAE;UACtBiC,MAAM,CAACrB,IAAI,CAACxB,OAAO,CAACO,KAAK,CAAC;QAC9B,CAAC,MACI,IAAI,CAACP,OAAO,CAACe,YAAY,EAAE;UAC5B,MAAMD,KAAK,GAAGd,OAAO,CAACO,KAAK,CAACE,aAAa,GAAGT,OAAO,CAACO,KAAK,CAACC,eAAe;UACzE,KAAK,IAAIrC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2C,KAAK,EAAE3C,CAAC,EAAE,EAAE;YAC5B,MAAMwD,IAAI,GAAG3B,OAAO,CAACO,KAAK,CAACC,eAAe,GAAGrC,CAAC;YAC9C,MAAM2E,kBAAkB,GAAG9C,OAAO,CAACK,eAAe,CAAC0C,+BAA+B,CAACpB,IAAI,CAAC;YACxF,MAAMqB,iBAAiB,GAAGhD,OAAO,CAACK,eAAe,CAAC4C,8BAA8B,CAACtB,IAAI,CAAC;YACtF,MAAMpB,KAAK,GAAG,IAAIzB,KAAK,CAAC6C,IAAI,EAAEmB,kBAAkB,EAAEnB,IAAI,EAAEqB,iBAAiB,CAAC;YAC1EH,MAAM,CAACrB,IAAI,CAACjB,KAAK,CAAC;UACtB;QACJ;QACA,KAAK,MAAMA,KAAK,IAAIsC,MAAM,EAAE;UACxBtB,WAAW,CAACC,IAAI,CAAC;YACbjB,KAAK;YACLmC,OAAO,EAAEtD;UACb,CAAC,CAAC;QACN;MACJ;MACA,IAAIY,OAAO,CAACO,KAAK,IAAI,CAACP,OAAO,CAACY,YAAY,IAAIZ,OAAO,CAACe,YAAY,EAAE;QAChE,MAAMlD,CAAC,GAAG,IAAIiB,KAAK,CAACkB,OAAO,CAACO,KAAK,CAACC,eAAe,EAAE,CAAC,EAAER,OAAO,CAACO,KAAK,CAACE,aAAa,GAAG,CAAC,EAAE,CAAC,CAAC;QACzFc,WAAW,CAACC,IAAI,CAAC;UACbjB,KAAK,EAAE1C,CAAC;UACR6E,OAAO,EAAErD;QACb,CAAC,CAAC;MACN;MACA,KAAK,MAAM6D,CAAC,IAAIlD,OAAO,CAACkB,WAAW,EAAE;QACjCK,WAAW,CAACC,IAAI,CAAC;UACbjB,KAAK,EAAEzB,KAAK,CAACqE,aAAa,CAAC,IAAItE,QAAQ,CAACmB,OAAO,CAAC8B,UAAU,EAAEoB,CAAC,CAAChB,MAAM,CAAC,CAAC;UACtEQ,OAAO,EAAE;YACLE,WAAW,EAAEtD,uBAAuB;YACpC8D,KAAK,EAAE;cAAE3B,OAAO,EAAEyB,CAAC,CAACf,IAAI;cAAEQ,eAAe,EAAEO,CAAC,CAACd,OAAO,GAAG,gCAAgC,GAAG,wBAAwB;cAAEiB,WAAW,EAAErE,uBAAuB,CAACsE;YAAK,CAAC;YAC/JC,eAAe,EAAE;UACrB;QACJ,CAAC,CAAC;MACN;MACA,OAAOhC,WAAW;IACtB,CAAC,CAAC;IACF,IAAI,CAACiC,SAAS,CAAC/E,YAAY,CAAC,MAAM;MAAE,IAAI,CAACmB,UAAU,CAAC6D,GAAG,CAAC,IAAI,EAAEtD,SAAS,CAAC;IAAE,CAAC,CAAC,CAAC;IAC7E,IAAI,CAACqD,SAAS,CAACrE,0BAA0B,CAAC,IAAI,CAACM,MAAM,EAAE,IAAI,CAAC8B,WAAW,CAAC,CAAC;EAC7E;AACJ,CAAC;AACDhC,eAAe,GAAGlC,UAAU,CAAC,CACzBgB,OAAO,CAAC,CAAC,EAAEU,gBAAgB,CAAC,CAC/B,EAAEQ,eAAe,CAAC;AACnB,SAASA,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}