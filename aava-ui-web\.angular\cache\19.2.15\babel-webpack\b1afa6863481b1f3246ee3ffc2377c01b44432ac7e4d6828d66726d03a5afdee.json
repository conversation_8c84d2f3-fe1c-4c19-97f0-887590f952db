{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport class EditorTheme {\n  get type() {\n    return this._theme.type;\n  }\n  get value() {\n    return this._theme;\n  }\n  constructor(theme) {\n    this._theme = theme;\n  }\n  update(theme) {\n    this._theme = theme;\n  }\n  getColor(color) {\n    return this._theme.getColor(color);\n  }\n}", "map": {"version": 3, "names": ["EditorTheme", "type", "_theme", "value", "constructor", "theme", "update", "getColor", "color"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/editorTheme.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport class EditorTheme {\n    get type() {\n        return this._theme.type;\n    }\n    get value() {\n        return this._theme;\n    }\n    constructor(theme) {\n        this._theme = theme;\n    }\n    update(theme) {\n        this._theme = theme;\n    }\n    getColor(color) {\n        return this._theme.getColor(color);\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,MAAMA,WAAW,CAAC;EACrB,IAAIC,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACC,MAAM,CAACD,IAAI;EAC3B;EACA,IAAIE,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACD,MAAM;EACtB;EACAE,WAAWA,CAACC,KAAK,EAAE;IACf,IAAI,CAACH,MAAM,GAAGG,KAAK;EACvB;EACAC,MAAMA,CAACD,KAAK,EAAE;IACV,IAAI,CAACH,MAAM,GAAGG,KAAK;EACvB;EACAE,QAAQA,CAACC,KAAK,EAAE;IACZ,OAAO,IAAI,CAACN,MAAM,CAACK,QAAQ,CAACC,KAAK,CAAC;EACtC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}