{"ast": null, "code": "import { <PERSON>der } from \"d3-array\";\nimport { cartesian, cartesianCross, cartesianNormalizeInPlace } from \"./cartesian.js\";\nimport { abs, asin, atan2, cos, epsilon, epsilon2, halfPi, pi, quarterPi, sign, sin, tau } from \"./math.js\";\nfunction longitude(point) {\n  return abs(point[0]) <= pi ? point[0] : sign(point[0]) * ((abs(point[0]) + pi) % tau - pi);\n}\nexport default function (polygon, point) {\n  var lambda = longitude(point),\n    phi = point[1],\n    sinPhi = sin(phi),\n    normal = [sin(lambda), -cos(lambda), 0],\n    angle = 0,\n    winding = 0;\n  var sum = new Adder();\n  if (sinPhi === 1) phi = halfPi + epsilon;else if (sinPhi === -1) phi = -halfPi - epsilon;\n  for (var i = 0, n = polygon.length; i < n; ++i) {\n    if (!(m = (ring = polygon[i]).length)) continue;\n    var ring,\n      m,\n      point0 = ring[m - 1],\n      lambda0 = longitude(point0),\n      phi0 = point0[1] / 2 + quarterPi,\n      sinPhi0 = sin(phi0),\n      cosPhi0 = cos(phi0);\n    for (var j = 0; j < m; ++j, lambda0 = lambda1, sinPhi0 = sinPhi1, cosPhi0 = cosPhi1, point0 = point1) {\n      var point1 = ring[j],\n        lambda1 = longitude(point1),\n        phi1 = point1[1] / 2 + quarterPi,\n        sinPhi1 = sin(phi1),\n        cosPhi1 = cos(phi1),\n        delta = lambda1 - lambda0,\n        sign = delta >= 0 ? 1 : -1,\n        absDelta = sign * delta,\n        antimeridian = absDelta > pi,\n        k = sinPhi0 * sinPhi1;\n      sum.add(atan2(k * sign * sin(absDelta), cosPhi0 * cosPhi1 + k * cos(absDelta)));\n      angle += antimeridian ? delta + sign * tau : delta;\n\n      // Are the longitudes either side of the point’s meridian (lambda),\n      // and are the latitudes smaller than the parallel (phi)?\n      if (antimeridian ^ lambda0 >= lambda ^ lambda1 >= lambda) {\n        var arc = cartesianCross(cartesian(point0), cartesian(point1));\n        cartesianNormalizeInPlace(arc);\n        var intersection = cartesianCross(normal, arc);\n        cartesianNormalizeInPlace(intersection);\n        var phiArc = (antimeridian ^ delta >= 0 ? -1 : 1) * asin(intersection[2]);\n        if (phi > phiArc || phi === phiArc && (arc[0] || arc[1])) {\n          winding += antimeridian ^ delta >= 0 ? 1 : -1;\n        }\n      }\n    }\n  }\n\n  // First, determine whether the South pole is inside or outside:\n  //\n  // It is inside if:\n  // * the polygon winds around it in a clockwise direction.\n  // * the polygon does not (cumulatively) wind around it, but has a negative\n  //   (counter-clockwise) area.\n  //\n  // Second, count the (signed) number of times a segment crosses a lambda\n  // from the point to the South pole.  If it is zero, then the point is the\n  // same side as the South pole.\n\n  return (angle < -epsilon || angle < epsilon && sum < -epsilon2) ^ winding & 1;\n}", "map": {"version": 3, "names": ["<PERSON><PERSON>", "cartesian", "cartesianCross", "cartesianNormalizeInPlace", "abs", "asin", "atan2", "cos", "epsilon", "epsilon2", "halfPi", "pi", "quarterPi", "sign", "sin", "tau", "longitude", "point", "polygon", "lambda", "phi", "sinPhi", "normal", "angle", "winding", "sum", "i", "n", "length", "m", "ring", "point0", "lambda0", "phi0", "sinPhi0", "cosPhi0", "j", "lambda1", "sinPhi1", "cosPhi1", "point1", "phi1", "delta", "absD<PERSON><PERSON>", "antimeridian", "k", "add", "arc", "intersection", "phiArc"], "sources": ["C:/console/aava-ui-web/node_modules/d3-geo/src/polygonContains.js"], "sourcesContent": ["import {<PERSON>der} from \"d3-array\";\nimport {cartesian, cartesianCross, cartesianNormalizeInPlace} from \"./cartesian.js\";\nimport {abs, asin, atan2, cos, epsilon, epsilon2, halfPi, pi, quarterPi, sign, sin, tau} from \"./math.js\";\n\nfunction longitude(point) {\n  return abs(point[0]) <= pi ? point[0] : sign(point[0]) * ((abs(point[0]) + pi) % tau - pi);\n}\n\nexport default function(polygon, point) {\n  var lambda = longitude(point),\n      phi = point[1],\n      sinPhi = sin(phi),\n      normal = [sin(lambda), -cos(lambda), 0],\n      angle = 0,\n      winding = 0;\n\n  var sum = new Adder();\n\n  if (sinPhi === 1) phi = halfPi + epsilon;\n  else if (sinPhi === -1) phi = -halfPi - epsilon;\n\n  for (var i = 0, n = polygon.length; i < n; ++i) {\n    if (!(m = (ring = polygon[i]).length)) continue;\n    var ring,\n        m,\n        point0 = ring[m - 1],\n        lambda0 = longitude(point0),\n        phi0 = point0[1] / 2 + quarterPi,\n        sinPhi0 = sin(phi0),\n        cosPhi0 = cos(phi0);\n\n    for (var j = 0; j < m; ++j, lambda0 = lambda1, sinPhi0 = sinPhi1, cosPhi0 = cosPhi1, point0 = point1) {\n      var point1 = ring[j],\n          lambda1 = longitude(point1),\n          phi1 = point1[1] / 2 + quarterPi,\n          sinPhi1 = sin(phi1),\n          cosPhi1 = cos(phi1),\n          delta = lambda1 - lambda0,\n          sign = delta >= 0 ? 1 : -1,\n          absDelta = sign * delta,\n          antimeridian = absDelta > pi,\n          k = sinPhi0 * sinPhi1;\n\n      sum.add(atan2(k * sign * sin(absDelta), cosPhi0 * cosPhi1 + k * cos(absDelta)));\n      angle += antimeridian ? delta + sign * tau : delta;\n\n      // Are the longitudes either side of the point’s meridian (lambda),\n      // and are the latitudes smaller than the parallel (phi)?\n      if (antimeridian ^ lambda0 >= lambda ^ lambda1 >= lambda) {\n        var arc = cartesianCross(cartesian(point0), cartesian(point1));\n        cartesianNormalizeInPlace(arc);\n        var intersection = cartesianCross(normal, arc);\n        cartesianNormalizeInPlace(intersection);\n        var phiArc = (antimeridian ^ delta >= 0 ? -1 : 1) * asin(intersection[2]);\n        if (phi > phiArc || phi === phiArc && (arc[0] || arc[1])) {\n          winding += antimeridian ^ delta >= 0 ? 1 : -1;\n        }\n      }\n    }\n  }\n\n  // First, determine whether the South pole is inside or outside:\n  //\n  // It is inside if:\n  // * the polygon winds around it in a clockwise direction.\n  // * the polygon does not (cumulatively) wind around it, but has a negative\n  //   (counter-clockwise) area.\n  //\n  // Second, count the (signed) number of times a segment crosses a lambda\n  // from the point to the South pole.  If it is zero, then the point is the\n  // same side as the South pole.\n\n  return (angle < -epsilon || angle < epsilon && sum < -epsilon2) ^ (winding & 1);\n}\n"], "mappings": "AAAA,SAAQA,KAAK,QAAO,UAAU;AAC9B,SAAQC,SAAS,EAAEC,cAAc,EAAEC,yBAAyB,QAAO,gBAAgB;AACnF,SAAQC,GAAG,EAAEC,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,EAAE,EAAEC,SAAS,EAAEC,IAAI,EAAEC,GAAG,EAAEC,GAAG,QAAO,WAAW;AAEzG,SAASC,SAASA,CAACC,KAAK,EAAE;EACxB,OAAOb,GAAG,CAACa,KAAK,CAAC,CAAC,CAAC,CAAC,IAAIN,EAAE,GAAGM,KAAK,CAAC,CAAC,CAAC,GAAGJ,IAAI,CAACI,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAACb,GAAG,CAACa,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGN,EAAE,IAAII,GAAG,GAAGJ,EAAE,CAAC;AAC5F;AAEA,eAAe,UAASO,OAAO,EAAED,KAAK,EAAE;EACtC,IAAIE,MAAM,GAAGH,SAAS,CAACC,KAAK,CAAC;IACzBG,GAAG,GAAGH,KAAK,CAAC,CAAC,CAAC;IACdI,MAAM,GAAGP,GAAG,CAACM,GAAG,CAAC;IACjBE,MAAM,GAAG,CAACR,GAAG,CAACK,MAAM,CAAC,EAAE,CAACZ,GAAG,CAACY,MAAM,CAAC,EAAE,CAAC,CAAC;IACvCI,KAAK,GAAG,CAAC;IACTC,OAAO,GAAG,CAAC;EAEf,IAAIC,GAAG,GAAG,IAAIzB,KAAK,CAAC,CAAC;EAErB,IAAIqB,MAAM,KAAK,CAAC,EAAED,GAAG,GAAGV,MAAM,GAAGF,OAAO,CAAC,KACpC,IAAIa,MAAM,KAAK,CAAC,CAAC,EAAED,GAAG,GAAG,CAACV,MAAM,GAAGF,OAAO;EAE/C,KAAK,IAAIkB,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGT,OAAO,CAACU,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;IAC9C,IAAI,EAAEG,CAAC,GAAG,CAACC,IAAI,GAAGZ,OAAO,CAACQ,CAAC,CAAC,EAAEE,MAAM,CAAC,EAAE;IACvC,IAAIE,IAAI;MACJD,CAAC;MACDE,MAAM,GAAGD,IAAI,CAACD,CAAC,GAAG,CAAC,CAAC;MACpBG,OAAO,GAAGhB,SAAS,CAACe,MAAM,CAAC;MAC3BE,IAAI,GAAGF,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,GAAGnB,SAAS;MAChCsB,OAAO,GAAGpB,GAAG,CAACmB,IAAI,CAAC;MACnBE,OAAO,GAAG5B,GAAG,CAAC0B,IAAI,CAAC;IAEvB,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,CAAC,EAAE,EAAEO,CAAC,EAAEJ,OAAO,GAAGK,OAAO,EAAEH,OAAO,GAAGI,OAAO,EAAEH,OAAO,GAAGI,OAAO,EAAER,MAAM,GAAGS,MAAM,EAAE;MACpG,IAAIA,MAAM,GAAGV,IAAI,CAACM,CAAC,CAAC;QAChBC,OAAO,GAAGrB,SAAS,CAACwB,MAAM,CAAC;QAC3BC,IAAI,GAAGD,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG5B,SAAS;QAChC0B,OAAO,GAAGxB,GAAG,CAAC2B,IAAI,CAAC;QACnBF,OAAO,GAAGhC,GAAG,CAACkC,IAAI,CAAC;QACnBC,KAAK,GAAGL,OAAO,GAAGL,OAAO;QACzBnB,IAAI,GAAG6B,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC1BC,QAAQ,GAAG9B,IAAI,GAAG6B,KAAK;QACvBE,YAAY,GAAGD,QAAQ,GAAGhC,EAAE;QAC5BkC,CAAC,GAAGX,OAAO,GAAGI,OAAO;MAEzBb,GAAG,CAACqB,GAAG,CAACxC,KAAK,CAACuC,CAAC,GAAGhC,IAAI,GAAGC,GAAG,CAAC6B,QAAQ,CAAC,EAAER,OAAO,GAAGI,OAAO,GAAGM,CAAC,GAAGtC,GAAG,CAACoC,QAAQ,CAAC,CAAC,CAAC;MAC/EpB,KAAK,IAAIqB,YAAY,GAAGF,KAAK,GAAG7B,IAAI,GAAGE,GAAG,GAAG2B,KAAK;;MAElD;MACA;MACA,IAAIE,YAAY,GAAGZ,OAAO,IAAIb,MAAM,GAAGkB,OAAO,IAAIlB,MAAM,EAAE;QACxD,IAAI4B,GAAG,GAAG7C,cAAc,CAACD,SAAS,CAAC8B,MAAM,CAAC,EAAE9B,SAAS,CAACuC,MAAM,CAAC,CAAC;QAC9DrC,yBAAyB,CAAC4C,GAAG,CAAC;QAC9B,IAAIC,YAAY,GAAG9C,cAAc,CAACoB,MAAM,EAAEyB,GAAG,CAAC;QAC9C5C,yBAAyB,CAAC6C,YAAY,CAAC;QACvC,IAAIC,MAAM,GAAG,CAACL,YAAY,GAAGF,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAIrC,IAAI,CAAC2C,YAAY,CAAC,CAAC,CAAC,CAAC;QACzE,IAAI5B,GAAG,GAAG6B,MAAM,IAAI7B,GAAG,KAAK6B,MAAM,KAAKF,GAAG,CAAC,CAAC,CAAC,IAAIA,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;UACxDvB,OAAO,IAAIoB,YAAY,GAAGF,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC/C;MACF;IACF;EACF;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA,OAAO,CAACnB,KAAK,GAAG,CAACf,OAAO,IAAIe,KAAK,GAAGf,OAAO,IAAIiB,GAAG,GAAG,CAAChB,QAAQ,IAAKe,OAAO,GAAG,CAAE;AACjF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}