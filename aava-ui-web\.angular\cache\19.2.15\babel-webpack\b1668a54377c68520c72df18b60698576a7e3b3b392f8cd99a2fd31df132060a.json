{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { AbstractTree } from './abstractTree.js';\nimport { CompressibleObjectTreeModel } from './compressedObjectTreeModel.js';\nimport { ObjectTreeModel } from './objectTreeModel.js';\nimport { memoize } from '../../../common/decorators.js';\nimport { Iterable } from '../../../common/iterator.js';\nexport class ObjectTree extends AbstractTree {\n  get onDidChangeCollapseState() {\n    return this.model.onDidChangeCollapseState;\n  }\n  constructor(user, container, delegate, renderers, options = {}) {\n    super(user, container, delegate, renderers, options);\n    this.user = user;\n  }\n  setChildren(element, children = Iterable.empty(), options) {\n    this.model.setChildren(element, children, options);\n  }\n  rerender(element) {\n    if (element === undefined) {\n      this.view.rerender();\n      return;\n    }\n    this.model.rerender(element);\n  }\n  hasElement(element) {\n    return this.model.has(element);\n  }\n  createModel(user, view, options) {\n    return new ObjectTreeModel(user, view, options);\n  }\n}\nclass CompressibleRenderer {\n  get compressedTreeNodeProvider() {\n    return this._compressedTreeNodeProvider();\n  }\n  constructor(_compressedTreeNodeProvider, stickyScrollDelegate, renderer) {\n    this._compressedTreeNodeProvider = _compressedTreeNodeProvider;\n    this.stickyScrollDelegate = stickyScrollDelegate;\n    this.renderer = renderer;\n    this.templateId = renderer.templateId;\n    if (renderer.onDidChangeTwistieState) {\n      this.onDidChangeTwistieState = renderer.onDidChangeTwistieState;\n    }\n  }\n  renderTemplate(container) {\n    const data = this.renderer.renderTemplate(container);\n    return {\n      compressedTreeNode: undefined,\n      data\n    };\n  }\n  renderElement(node, index, templateData, height) {\n    let compressedTreeNode = this.stickyScrollDelegate.getCompressedNode(node);\n    if (!compressedTreeNode) {\n      compressedTreeNode = this.compressedTreeNodeProvider.getCompressedTreeNode(node.element);\n    }\n    if (compressedTreeNode.element.elements.length === 1) {\n      templateData.compressedTreeNode = undefined;\n      this.renderer.renderElement(node, index, templateData.data, height);\n    } else {\n      templateData.compressedTreeNode = compressedTreeNode;\n      this.renderer.renderCompressedElements(compressedTreeNode, index, templateData.data, height);\n    }\n  }\n  disposeElement(node, index, templateData, height) {\n    if (templateData.compressedTreeNode) {\n      this.renderer.disposeCompressedElements?.(templateData.compressedTreeNode, index, templateData.data, height);\n    } else {\n      this.renderer.disposeElement?.(node, index, templateData.data, height);\n    }\n  }\n  disposeTemplate(templateData) {\n    this.renderer.disposeTemplate(templateData.data);\n  }\n  renderTwistie(element, twistieElement) {\n    if (this.renderer.renderTwistie) {\n      return this.renderer.renderTwistie(element, twistieElement);\n    }\n    return false;\n  }\n}\n__decorate([memoize], CompressibleRenderer.prototype, \"compressedTreeNodeProvider\", null);\nclass CompressibleStickyScrollDelegate {\n  constructor(modelProvider) {\n    this.modelProvider = modelProvider;\n    this.compressedStickyNodes = new Map();\n  }\n  getCompressedNode(node) {\n    return this.compressedStickyNodes.get(node);\n  }\n  constrainStickyScrollNodes(stickyNodes, stickyScrollMaxItemCount, maxWidgetHeight) {\n    this.compressedStickyNodes.clear();\n    if (stickyNodes.length === 0) {\n      return [];\n    }\n    for (let i = 0; i < stickyNodes.length; i++) {\n      const stickyNode = stickyNodes[i];\n      const stickyNodeBottom = stickyNode.position + stickyNode.height;\n      const followingReachesMaxHeight = i + 1 < stickyNodes.length && stickyNodeBottom + stickyNodes[i + 1].height > maxWidgetHeight;\n      if (followingReachesMaxHeight || i >= stickyScrollMaxItemCount - 1 && stickyScrollMaxItemCount < stickyNodes.length) {\n        const uncompressedStickyNodes = stickyNodes.slice(0, i);\n        const overflowingStickyNodes = stickyNodes.slice(i);\n        const compressedStickyNode = this.compressStickyNodes(overflowingStickyNodes);\n        return [...uncompressedStickyNodes, compressedStickyNode];\n      }\n    }\n    return stickyNodes;\n  }\n  compressStickyNodes(stickyNodes) {\n    if (stickyNodes.length === 0) {\n      throw new Error('Can\\'t compress empty sticky nodes');\n    }\n    const compressionModel = this.modelProvider();\n    if (!compressionModel.isCompressionEnabled()) {\n      return stickyNodes[0];\n    }\n    // Collect all elements to be compressed\n    const elements = [];\n    for (let i = 0; i < stickyNodes.length; i++) {\n      const stickyNode = stickyNodes[i];\n      const compressedNode = compressionModel.getCompressedTreeNode(stickyNode.node.element);\n      if (compressedNode.element) {\n        // if an element is incompressible, it can't be compressed with it's parent element\n        if (i !== 0 && compressedNode.element.incompressible) {\n          break;\n        }\n        elements.push(...compressedNode.element.elements);\n      }\n    }\n    if (elements.length < 2) {\n      return stickyNodes[0];\n    }\n    // Compress the elements\n    const lastStickyNode = stickyNodes[stickyNodes.length - 1];\n    const compressedElement = {\n      elements,\n      incompressible: false\n    };\n    const compressedNode = {\n      ...lastStickyNode.node,\n      children: [],\n      element: compressedElement\n    };\n    const stickyTreeNode = new Proxy(stickyNodes[0].node, {});\n    const compressedStickyNode = {\n      node: stickyTreeNode,\n      startIndex: stickyNodes[0].startIndex,\n      endIndex: lastStickyNode.endIndex,\n      position: stickyNodes[0].position,\n      height: stickyNodes[0].height\n    };\n    this.compressedStickyNodes.set(stickyTreeNode, compressedNode);\n    return compressedStickyNode;\n  }\n}\nfunction asObjectTreeOptions(compressedTreeNodeProvider, options) {\n  return options && {\n    ...options,\n    keyboardNavigationLabelProvider: options.keyboardNavigationLabelProvider && {\n      getKeyboardNavigationLabel(e) {\n        let compressedTreeNode;\n        try {\n          compressedTreeNode = compressedTreeNodeProvider().getCompressedTreeNode(e);\n        } catch {\n          return options.keyboardNavigationLabelProvider.getKeyboardNavigationLabel(e);\n        }\n        if (compressedTreeNode.element.elements.length === 1) {\n          return options.keyboardNavigationLabelProvider.getKeyboardNavigationLabel(e);\n        } else {\n          return options.keyboardNavigationLabelProvider.getCompressedNodeKeyboardNavigationLabel(compressedTreeNode.element.elements);\n        }\n      }\n    }\n  };\n}\nexport class CompressibleObjectTree extends ObjectTree {\n  constructor(user, container, delegate, renderers, options = {}) {\n    const compressedTreeNodeProvider = () => this;\n    const stickyScrollDelegate = new CompressibleStickyScrollDelegate(() => this.model);\n    const compressibleRenderers = renderers.map(r => new CompressibleRenderer(compressedTreeNodeProvider, stickyScrollDelegate, r));\n    super(user, container, delegate, compressibleRenderers, {\n      ...asObjectTreeOptions(compressedTreeNodeProvider, options),\n      stickyScrollDelegate\n    });\n  }\n  setChildren(element, children = Iterable.empty(), options) {\n    this.model.setChildren(element, children, options);\n  }\n  createModel(user, view, options) {\n    return new CompressibleObjectTreeModel(user, view, options);\n  }\n  updateOptions(optionsUpdate = {}) {\n    super.updateOptions(optionsUpdate);\n    if (typeof optionsUpdate.compressionEnabled !== 'undefined') {\n      this.model.setCompressionEnabled(optionsUpdate.compressionEnabled);\n    }\n  }\n  getCompressedTreeNode(element = null) {\n    return this.model.getCompressedTreeNode(element);\n  }\n}", "map": {"version": 3, "names": ["__decorate", "decorators", "target", "key", "desc", "c", "arguments", "length", "r", "Object", "getOwnPropertyDescriptor", "d", "Reflect", "decorate", "i", "defineProperty", "AbstractTree", "CompressibleObjectTreeModel", "ObjectTreeModel", "memoize", "Iterable", "ObjectTree", "onDidChangeCollapseState", "model", "constructor", "user", "container", "delegate", "renderers", "options", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "element", "children", "empty", "rerender", "undefined", "view", "hasElement", "has", "createModel", "Compress<PERSON><PERSON><PERSON><PERSON>", "compressedTreeNodeProvider", "_compressedTreeNodeProvider", "stickyScrollDelegate", "renderer", "templateId", "onDidChangeTwistieState", "renderTemplate", "data", "compressedTreeNode", "renderElement", "node", "index", "templateData", "height", "getCompressedNode", "getCompressedTreeNode", "elements", "renderCompressedElements", "disposeElement", "disposeCompressedElements", "disposeTemplate", "<PERSON><PERSON><PERSON><PERSON>ie", "<PERSON><PERSON><PERSON>", "prototype", "CompressibleStickyScrollDelegate", "modelProvider", "compressedStickyNodes", "Map", "get", "constrainStickyScrollNodes", "stickyNodes", "stickyScrollMaxItemCount", "maxWidgetHeight", "clear", "stickyNode", "stickyNodeBottom", "position", "followingReachesMaxHeight", "uncompressedStickyNodes", "slice", "overflowingStickyNodes", "compressedStickyNode", "compressStickyNodes", "Error", "compressionModel", "isCompressionEnabled", "compressedNode", "incompressible", "push", "lastStickyNode", "compressedElement", "stickyTreeNode", "Proxy", "startIndex", "endIndex", "set", "asObjectTreeOptions", "keyboardNavigationLabe<PERSON>", "getKeyboardNavigationLabel", "e", "getCompressedNodeKeyboardNavigationLabel", "CompressibleObjectTree", "compressibleRenderers", "map", "updateOptions", "optionsUpdate", "compressionEnabled", "setCompressionEnabled"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/browser/ui/tree/objectTree.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { AbstractTree } from './abstractTree.js';\nimport { CompressibleObjectTreeModel } from './compressedObjectTreeModel.js';\nimport { ObjectTreeModel } from './objectTreeModel.js';\nimport { memoize } from '../../../common/decorators.js';\nimport { Iterable } from '../../../common/iterator.js';\nexport class ObjectTree extends AbstractTree {\n    get onDidChangeCollapseState() { return this.model.onDidChangeCollapseState; }\n    constructor(user, container, delegate, renderers, options = {}) {\n        super(user, container, delegate, renderers, options);\n        this.user = user;\n    }\n    setChildren(element, children = Iterable.empty(), options) {\n        this.model.setChildren(element, children, options);\n    }\n    rerender(element) {\n        if (element === undefined) {\n            this.view.rerender();\n            return;\n        }\n        this.model.rerender(element);\n    }\n    hasElement(element) {\n        return this.model.has(element);\n    }\n    createModel(user, view, options) {\n        return new ObjectTreeModel(user, view, options);\n    }\n}\nclass CompressibleRenderer {\n    get compressedTreeNodeProvider() {\n        return this._compressedTreeNodeProvider();\n    }\n    constructor(_compressedTreeNodeProvider, stickyScrollDelegate, renderer) {\n        this._compressedTreeNodeProvider = _compressedTreeNodeProvider;\n        this.stickyScrollDelegate = stickyScrollDelegate;\n        this.renderer = renderer;\n        this.templateId = renderer.templateId;\n        if (renderer.onDidChangeTwistieState) {\n            this.onDidChangeTwistieState = renderer.onDidChangeTwistieState;\n        }\n    }\n    renderTemplate(container) {\n        const data = this.renderer.renderTemplate(container);\n        return { compressedTreeNode: undefined, data };\n    }\n    renderElement(node, index, templateData, height) {\n        let compressedTreeNode = this.stickyScrollDelegate.getCompressedNode(node);\n        if (!compressedTreeNode) {\n            compressedTreeNode = this.compressedTreeNodeProvider.getCompressedTreeNode(node.element);\n        }\n        if (compressedTreeNode.element.elements.length === 1) {\n            templateData.compressedTreeNode = undefined;\n            this.renderer.renderElement(node, index, templateData.data, height);\n        }\n        else {\n            templateData.compressedTreeNode = compressedTreeNode;\n            this.renderer.renderCompressedElements(compressedTreeNode, index, templateData.data, height);\n        }\n    }\n    disposeElement(node, index, templateData, height) {\n        if (templateData.compressedTreeNode) {\n            this.renderer.disposeCompressedElements?.(templateData.compressedTreeNode, index, templateData.data, height);\n        }\n        else {\n            this.renderer.disposeElement?.(node, index, templateData.data, height);\n        }\n    }\n    disposeTemplate(templateData) {\n        this.renderer.disposeTemplate(templateData.data);\n    }\n    renderTwistie(element, twistieElement) {\n        if (this.renderer.renderTwistie) {\n            return this.renderer.renderTwistie(element, twistieElement);\n        }\n        return false;\n    }\n}\n__decorate([\n    memoize\n], CompressibleRenderer.prototype, \"compressedTreeNodeProvider\", null);\nclass CompressibleStickyScrollDelegate {\n    constructor(modelProvider) {\n        this.modelProvider = modelProvider;\n        this.compressedStickyNodes = new Map();\n    }\n    getCompressedNode(node) {\n        return this.compressedStickyNodes.get(node);\n    }\n    constrainStickyScrollNodes(stickyNodes, stickyScrollMaxItemCount, maxWidgetHeight) {\n        this.compressedStickyNodes.clear();\n        if (stickyNodes.length === 0) {\n            return [];\n        }\n        for (let i = 0; i < stickyNodes.length; i++) {\n            const stickyNode = stickyNodes[i];\n            const stickyNodeBottom = stickyNode.position + stickyNode.height;\n            const followingReachesMaxHeight = i + 1 < stickyNodes.length && stickyNodeBottom + stickyNodes[i + 1].height > maxWidgetHeight;\n            if (followingReachesMaxHeight || i >= stickyScrollMaxItemCount - 1 && stickyScrollMaxItemCount < stickyNodes.length) {\n                const uncompressedStickyNodes = stickyNodes.slice(0, i);\n                const overflowingStickyNodes = stickyNodes.slice(i);\n                const compressedStickyNode = this.compressStickyNodes(overflowingStickyNodes);\n                return [...uncompressedStickyNodes, compressedStickyNode];\n            }\n        }\n        return stickyNodes;\n    }\n    compressStickyNodes(stickyNodes) {\n        if (stickyNodes.length === 0) {\n            throw new Error('Can\\'t compress empty sticky nodes');\n        }\n        const compressionModel = this.modelProvider();\n        if (!compressionModel.isCompressionEnabled()) {\n            return stickyNodes[0];\n        }\n        // Collect all elements to be compressed\n        const elements = [];\n        for (let i = 0; i < stickyNodes.length; i++) {\n            const stickyNode = stickyNodes[i];\n            const compressedNode = compressionModel.getCompressedTreeNode(stickyNode.node.element);\n            if (compressedNode.element) {\n                // if an element is incompressible, it can't be compressed with it's parent element\n                if (i !== 0 && compressedNode.element.incompressible) {\n                    break;\n                }\n                elements.push(...compressedNode.element.elements);\n            }\n        }\n        if (elements.length < 2) {\n            return stickyNodes[0];\n        }\n        // Compress the elements\n        const lastStickyNode = stickyNodes[stickyNodes.length - 1];\n        const compressedElement = { elements, incompressible: false };\n        const compressedNode = { ...lastStickyNode.node, children: [], element: compressedElement };\n        const stickyTreeNode = new Proxy(stickyNodes[0].node, {});\n        const compressedStickyNode = {\n            node: stickyTreeNode,\n            startIndex: stickyNodes[0].startIndex,\n            endIndex: lastStickyNode.endIndex,\n            position: stickyNodes[0].position,\n            height: stickyNodes[0].height,\n        };\n        this.compressedStickyNodes.set(stickyTreeNode, compressedNode);\n        return compressedStickyNode;\n    }\n}\nfunction asObjectTreeOptions(compressedTreeNodeProvider, options) {\n    return options && {\n        ...options,\n        keyboardNavigationLabelProvider: options.keyboardNavigationLabelProvider && {\n            getKeyboardNavigationLabel(e) {\n                let compressedTreeNode;\n                try {\n                    compressedTreeNode = compressedTreeNodeProvider().getCompressedTreeNode(e);\n                }\n                catch {\n                    return options.keyboardNavigationLabelProvider.getKeyboardNavigationLabel(e);\n                }\n                if (compressedTreeNode.element.elements.length === 1) {\n                    return options.keyboardNavigationLabelProvider.getKeyboardNavigationLabel(e);\n                }\n                else {\n                    return options.keyboardNavigationLabelProvider.getCompressedNodeKeyboardNavigationLabel(compressedTreeNode.element.elements);\n                }\n            }\n        }\n    };\n}\nexport class CompressibleObjectTree extends ObjectTree {\n    constructor(user, container, delegate, renderers, options = {}) {\n        const compressedTreeNodeProvider = () => this;\n        const stickyScrollDelegate = new CompressibleStickyScrollDelegate(() => this.model);\n        const compressibleRenderers = renderers.map(r => new CompressibleRenderer(compressedTreeNodeProvider, stickyScrollDelegate, r));\n        super(user, container, delegate, compressibleRenderers, { ...asObjectTreeOptions(compressedTreeNodeProvider, options), stickyScrollDelegate });\n    }\n    setChildren(element, children = Iterable.empty(), options) {\n        this.model.setChildren(element, children, options);\n    }\n    createModel(user, view, options) {\n        return new CompressibleObjectTreeModel(user, view, options);\n    }\n    updateOptions(optionsUpdate = {}) {\n        super.updateOptions(optionsUpdate);\n        if (typeof optionsUpdate.compressionEnabled !== 'undefined') {\n            this.model.setCompressionEnabled(optionsUpdate.compressionEnabled);\n        }\n    }\n    getCompressedTreeNode(element = null) {\n        return this.model.getCompressedTreeNode(element);\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,IAAIA,UAAU,GAAI,IAAI,IAAI,IAAI,CAACA,UAAU,IAAK,UAAUC,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAE;EACnF,IAAIC,CAAC,GAAGC,SAAS,CAACC,MAAM;IAAEC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGH,MAAM,GAAGE,IAAI,KAAK,IAAI,GAAGA,IAAI,GAAGK,MAAM,CAACC,wBAAwB,CAACR,MAAM,EAAEC,GAAG,CAAC,GAAGC,IAAI;IAAEO,CAAC;EAC5H,IAAI,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,CAACC,QAAQ,KAAK,UAAU,EAAEL,CAAC,GAAGI,OAAO,CAACC,QAAQ,CAACZ,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,CAAC,CAAC,KAC1H,KAAK,IAAIU,CAAC,GAAGb,UAAU,CAACM,MAAM,GAAG,CAAC,EAAEO,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE,IAAIH,CAAC,GAAGV,UAAU,CAACa,CAAC,CAAC,EAAEN,CAAC,GAAG,CAACH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACH,CAAC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACT,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,GAAGG,CAAC,CAACT,MAAM,EAAEC,GAAG,CAAC,KAAKK,CAAC;EACjJ,OAAOH,CAAC,GAAG,CAAC,IAAIG,CAAC,IAAIC,MAAM,CAACM,cAAc,CAACb,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,EAAEA,CAAC;AACjE,CAAC;AACD,SAASQ,YAAY,QAAQ,mBAAmB;AAChD,SAASC,2BAA2B,QAAQ,gCAAgC;AAC5E,SAASC,eAAe,QAAQ,sBAAsB;AACtD,SAASC,OAAO,QAAQ,+BAA+B;AACvD,SAASC,QAAQ,QAAQ,6BAA6B;AACtD,OAAO,MAAMC,UAAU,SAASL,YAAY,CAAC;EACzC,IAAIM,wBAAwBA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACC,KAAK,CAACD,wBAAwB;EAAE;EAC7EE,WAAWA,CAACC,IAAI,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IAC5D,KAAK,CAACJ,IAAI,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,OAAO,CAAC;IACpD,IAAI,CAACJ,IAAI,GAAGA,IAAI;EACpB;EACAK,WAAWA,CAACC,OAAO,EAAEC,QAAQ,GAAGZ,QAAQ,CAACa,KAAK,CAAC,CAAC,EAAEJ,OAAO,EAAE;IACvD,IAAI,CAACN,KAAK,CAACO,WAAW,CAACC,OAAO,EAAEC,QAAQ,EAAEH,OAAO,CAAC;EACtD;EACAK,QAAQA,CAACH,OAAO,EAAE;IACd,IAAIA,OAAO,KAAKI,SAAS,EAAE;MACvB,IAAI,CAACC,IAAI,CAACF,QAAQ,CAAC,CAAC;MACpB;IACJ;IACA,IAAI,CAACX,KAAK,CAACW,QAAQ,CAACH,OAAO,CAAC;EAChC;EACAM,UAAUA,CAACN,OAAO,EAAE;IAChB,OAAO,IAAI,CAACR,KAAK,CAACe,GAAG,CAACP,OAAO,CAAC;EAClC;EACAQ,WAAWA,CAACd,IAAI,EAAEW,IAAI,EAAEP,OAAO,EAAE;IAC7B,OAAO,IAAIX,eAAe,CAACO,IAAI,EAAEW,IAAI,EAAEP,OAAO,CAAC;EACnD;AACJ;AACA,MAAMW,oBAAoB,CAAC;EACvB,IAAIC,0BAA0BA,CAAA,EAAG;IAC7B,OAAO,IAAI,CAACC,2BAA2B,CAAC,CAAC;EAC7C;EACAlB,WAAWA,CAACkB,2BAA2B,EAAEC,oBAAoB,EAAEC,QAAQ,EAAE;IACrE,IAAI,CAACF,2BAA2B,GAAGA,2BAA2B;IAC9D,IAAI,CAACC,oBAAoB,GAAGA,oBAAoB;IAChD,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,UAAU,GAAGD,QAAQ,CAACC,UAAU;IACrC,IAAID,QAAQ,CAACE,uBAAuB,EAAE;MAClC,IAAI,CAACA,uBAAuB,GAAGF,QAAQ,CAACE,uBAAuB;IACnE;EACJ;EACAC,cAAcA,CAACrB,SAAS,EAAE;IACtB,MAAMsB,IAAI,GAAG,IAAI,CAACJ,QAAQ,CAACG,cAAc,CAACrB,SAAS,CAAC;IACpD,OAAO;MAAEuB,kBAAkB,EAAEd,SAAS;MAAEa;IAAK,CAAC;EAClD;EACAE,aAAaA,CAACC,IAAI,EAAEC,KAAK,EAAEC,YAAY,EAAEC,MAAM,EAAE;IAC7C,IAAIL,kBAAkB,GAAG,IAAI,CAACN,oBAAoB,CAACY,iBAAiB,CAACJ,IAAI,CAAC;IAC1E,IAAI,CAACF,kBAAkB,EAAE;MACrBA,kBAAkB,GAAG,IAAI,CAACR,0BAA0B,CAACe,qBAAqB,CAACL,IAAI,CAACpB,OAAO,CAAC;IAC5F;IACA,IAAIkB,kBAAkB,CAAClB,OAAO,CAAC0B,QAAQ,CAAClD,MAAM,KAAK,CAAC,EAAE;MAClD8C,YAAY,CAACJ,kBAAkB,GAAGd,SAAS;MAC3C,IAAI,CAACS,QAAQ,CAACM,aAAa,CAACC,IAAI,EAAEC,KAAK,EAAEC,YAAY,CAACL,IAAI,EAAEM,MAAM,CAAC;IACvE,CAAC,MACI;MACDD,YAAY,CAACJ,kBAAkB,GAAGA,kBAAkB;MACpD,IAAI,CAACL,QAAQ,CAACc,wBAAwB,CAACT,kBAAkB,EAAEG,KAAK,EAAEC,YAAY,CAACL,IAAI,EAAEM,MAAM,CAAC;IAChG;EACJ;EACAK,cAAcA,CAACR,IAAI,EAAEC,KAAK,EAAEC,YAAY,EAAEC,MAAM,EAAE;IAC9C,IAAID,YAAY,CAACJ,kBAAkB,EAAE;MACjC,IAAI,CAACL,QAAQ,CAACgB,yBAAyB,GAAGP,YAAY,CAACJ,kBAAkB,EAAEG,KAAK,EAAEC,YAAY,CAACL,IAAI,EAAEM,MAAM,CAAC;IAChH,CAAC,MACI;MACD,IAAI,CAACV,QAAQ,CAACe,cAAc,GAAGR,IAAI,EAAEC,KAAK,EAAEC,YAAY,CAACL,IAAI,EAAEM,MAAM,CAAC;IAC1E;EACJ;EACAO,eAAeA,CAACR,YAAY,EAAE;IAC1B,IAAI,CAACT,QAAQ,CAACiB,eAAe,CAACR,YAAY,CAACL,IAAI,CAAC;EACpD;EACAc,aAAaA,CAAC/B,OAAO,EAAEgC,cAAc,EAAE;IACnC,IAAI,IAAI,CAACnB,QAAQ,CAACkB,aAAa,EAAE;MAC7B,OAAO,IAAI,CAAClB,QAAQ,CAACkB,aAAa,CAAC/B,OAAO,EAAEgC,cAAc,CAAC;IAC/D;IACA,OAAO,KAAK;EAChB;AACJ;AACA/D,UAAU,CAAC,CACPmB,OAAO,CACV,EAAEqB,oBAAoB,CAACwB,SAAS,EAAE,4BAA4B,EAAE,IAAI,CAAC;AACtE,MAAMC,gCAAgC,CAAC;EACnCzC,WAAWA,CAAC0C,aAAa,EAAE;IACvB,IAAI,CAACA,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,qBAAqB,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC1C;EACAb,iBAAiBA,CAACJ,IAAI,EAAE;IACpB,OAAO,IAAI,CAACgB,qBAAqB,CAACE,GAAG,CAAClB,IAAI,CAAC;EAC/C;EACAmB,0BAA0BA,CAACC,WAAW,EAAEC,wBAAwB,EAAEC,eAAe,EAAE;IAC/E,IAAI,CAACN,qBAAqB,CAACO,KAAK,CAAC,CAAC;IAClC,IAAIH,WAAW,CAAChE,MAAM,KAAK,CAAC,EAAE;MAC1B,OAAO,EAAE;IACb;IACA,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyD,WAAW,CAAChE,MAAM,EAAEO,CAAC,EAAE,EAAE;MACzC,MAAM6D,UAAU,GAAGJ,WAAW,CAACzD,CAAC,CAAC;MACjC,MAAM8D,gBAAgB,GAAGD,UAAU,CAACE,QAAQ,GAAGF,UAAU,CAACrB,MAAM;MAChE,MAAMwB,yBAAyB,GAAGhE,CAAC,GAAG,CAAC,GAAGyD,WAAW,CAAChE,MAAM,IAAIqE,gBAAgB,GAAGL,WAAW,CAACzD,CAAC,GAAG,CAAC,CAAC,CAACwC,MAAM,GAAGmB,eAAe;MAC9H,IAAIK,yBAAyB,IAAIhE,CAAC,IAAI0D,wBAAwB,GAAG,CAAC,IAAIA,wBAAwB,GAAGD,WAAW,CAAChE,MAAM,EAAE;QACjH,MAAMwE,uBAAuB,GAAGR,WAAW,CAACS,KAAK,CAAC,CAAC,EAAElE,CAAC,CAAC;QACvD,MAAMmE,sBAAsB,GAAGV,WAAW,CAACS,KAAK,CAAClE,CAAC,CAAC;QACnD,MAAMoE,oBAAoB,GAAG,IAAI,CAACC,mBAAmB,CAACF,sBAAsB,CAAC;QAC7E,OAAO,CAAC,GAAGF,uBAAuB,EAAEG,oBAAoB,CAAC;MAC7D;IACJ;IACA,OAAOX,WAAW;EACtB;EACAY,mBAAmBA,CAACZ,WAAW,EAAE;IAC7B,IAAIA,WAAW,CAAChE,MAAM,KAAK,CAAC,EAAE;MAC1B,MAAM,IAAI6E,KAAK,CAAC,oCAAoC,CAAC;IACzD;IACA,MAAMC,gBAAgB,GAAG,IAAI,CAACnB,aAAa,CAAC,CAAC;IAC7C,IAAI,CAACmB,gBAAgB,CAACC,oBAAoB,CAAC,CAAC,EAAE;MAC1C,OAAOf,WAAW,CAAC,CAAC,CAAC;IACzB;IACA;IACA,MAAMd,QAAQ,GAAG,EAAE;IACnB,KAAK,IAAI3C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyD,WAAW,CAAChE,MAAM,EAAEO,CAAC,EAAE,EAAE;MACzC,MAAM6D,UAAU,GAAGJ,WAAW,CAACzD,CAAC,CAAC;MACjC,MAAMyE,cAAc,GAAGF,gBAAgB,CAAC7B,qBAAqB,CAACmB,UAAU,CAACxB,IAAI,CAACpB,OAAO,CAAC;MACtF,IAAIwD,cAAc,CAACxD,OAAO,EAAE;QACxB;QACA,IAAIjB,CAAC,KAAK,CAAC,IAAIyE,cAAc,CAACxD,OAAO,CAACyD,cAAc,EAAE;UAClD;QACJ;QACA/B,QAAQ,CAACgC,IAAI,CAAC,GAAGF,cAAc,CAACxD,OAAO,CAAC0B,QAAQ,CAAC;MACrD;IACJ;IACA,IAAIA,QAAQ,CAAClD,MAAM,GAAG,CAAC,EAAE;MACrB,OAAOgE,WAAW,CAAC,CAAC,CAAC;IACzB;IACA;IACA,MAAMmB,cAAc,GAAGnB,WAAW,CAACA,WAAW,CAAChE,MAAM,GAAG,CAAC,CAAC;IAC1D,MAAMoF,iBAAiB,GAAG;MAAElC,QAAQ;MAAE+B,cAAc,EAAE;IAAM,CAAC;IAC7D,MAAMD,cAAc,GAAG;MAAE,GAAGG,cAAc,CAACvC,IAAI;MAAEnB,QAAQ,EAAE,EAAE;MAAED,OAAO,EAAE4D;IAAkB,CAAC;IAC3F,MAAMC,cAAc,GAAG,IAAIC,KAAK,CAACtB,WAAW,CAAC,CAAC,CAAC,CAACpB,IAAI,EAAE,CAAC,CAAC,CAAC;IACzD,MAAM+B,oBAAoB,GAAG;MACzB/B,IAAI,EAAEyC,cAAc;MACpBE,UAAU,EAAEvB,WAAW,CAAC,CAAC,CAAC,CAACuB,UAAU;MACrCC,QAAQ,EAAEL,cAAc,CAACK,QAAQ;MACjClB,QAAQ,EAAEN,WAAW,CAAC,CAAC,CAAC,CAACM,QAAQ;MACjCvB,MAAM,EAAEiB,WAAW,CAAC,CAAC,CAAC,CAACjB;IAC3B,CAAC;IACD,IAAI,CAACa,qBAAqB,CAAC6B,GAAG,CAACJ,cAAc,EAAEL,cAAc,CAAC;IAC9D,OAAOL,oBAAoB;EAC/B;AACJ;AACA,SAASe,mBAAmBA,CAACxD,0BAA0B,EAAEZ,OAAO,EAAE;EAC9D,OAAOA,OAAO,IAAI;IACd,GAAGA,OAAO;IACVqE,+BAA+B,EAAErE,OAAO,CAACqE,+BAA+B,IAAI;MACxEC,0BAA0BA,CAACC,CAAC,EAAE;QAC1B,IAAInD,kBAAkB;QACtB,IAAI;UACAA,kBAAkB,GAAGR,0BAA0B,CAAC,CAAC,CAACe,qBAAqB,CAAC4C,CAAC,CAAC;QAC9E,CAAC,CACD,MAAM;UACF,OAAOvE,OAAO,CAACqE,+BAA+B,CAACC,0BAA0B,CAACC,CAAC,CAAC;QAChF;QACA,IAAInD,kBAAkB,CAAClB,OAAO,CAAC0B,QAAQ,CAAClD,MAAM,KAAK,CAAC,EAAE;UAClD,OAAOsB,OAAO,CAACqE,+BAA+B,CAACC,0BAA0B,CAACC,CAAC,CAAC;QAChF,CAAC,MACI;UACD,OAAOvE,OAAO,CAACqE,+BAA+B,CAACG,wCAAwC,CAACpD,kBAAkB,CAAClB,OAAO,CAAC0B,QAAQ,CAAC;QAChI;MACJ;IACJ;EACJ,CAAC;AACL;AACA,OAAO,MAAM6C,sBAAsB,SAASjF,UAAU,CAAC;EACnDG,WAAWA,CAACC,IAAI,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IAC5D,MAAMY,0BAA0B,GAAGA,CAAA,KAAM,IAAI;IAC7C,MAAME,oBAAoB,GAAG,IAAIsB,gCAAgC,CAAC,MAAM,IAAI,CAAC1C,KAAK,CAAC;IACnF,MAAMgF,qBAAqB,GAAG3E,SAAS,CAAC4E,GAAG,CAAChG,CAAC,IAAI,IAAIgC,oBAAoB,CAACC,0BAA0B,EAAEE,oBAAoB,EAAEnC,CAAC,CAAC,CAAC;IAC/H,KAAK,CAACiB,IAAI,EAAEC,SAAS,EAAEC,QAAQ,EAAE4E,qBAAqB,EAAE;MAAE,GAAGN,mBAAmB,CAACxD,0BAA0B,EAAEZ,OAAO,CAAC;MAAEc;IAAqB,CAAC,CAAC;EAClJ;EACAb,WAAWA,CAACC,OAAO,EAAEC,QAAQ,GAAGZ,QAAQ,CAACa,KAAK,CAAC,CAAC,EAAEJ,OAAO,EAAE;IACvD,IAAI,CAACN,KAAK,CAACO,WAAW,CAACC,OAAO,EAAEC,QAAQ,EAAEH,OAAO,CAAC;EACtD;EACAU,WAAWA,CAACd,IAAI,EAAEW,IAAI,EAAEP,OAAO,EAAE;IAC7B,OAAO,IAAIZ,2BAA2B,CAACQ,IAAI,EAAEW,IAAI,EAAEP,OAAO,CAAC;EAC/D;EACA4E,aAAaA,CAACC,aAAa,GAAG,CAAC,CAAC,EAAE;IAC9B,KAAK,CAACD,aAAa,CAACC,aAAa,CAAC;IAClC,IAAI,OAAOA,aAAa,CAACC,kBAAkB,KAAK,WAAW,EAAE;MACzD,IAAI,CAACpF,KAAK,CAACqF,qBAAqB,CAACF,aAAa,CAACC,kBAAkB,CAAC;IACtE;EACJ;EACAnD,qBAAqBA,CAACzB,OAAO,GAAG,IAAI,EAAE;IAClC,OAAO,IAAI,CAACR,KAAK,CAACiC,qBAAqB,CAACzB,OAAO,CAAC;EACpD;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}