{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as dom from '../../dom.js';\nimport { getBaseLayerHoverDelegate } from '../hover/hoverDelegate2.js';\nimport { getDefaultHoverDelegate } from '../hover/hoverDelegateFactory.js';\nimport { UILabelProvider } from '../../../common/keybindingLabels.js';\nimport { Disposable } from '../../../common/lifecycle.js';\nimport { equals } from '../../../common/objects.js';\nimport './keybindingLabel.css';\nimport { localize } from '../../../../nls.js';\nconst $ = dom.$;\nexport const unthemedKeybindingLabelOptions = {\n  keybindingLabelBackground: undefined,\n  keybindingLabelForeground: undefined,\n  keybindingLabelBorder: undefined,\n  keybindingLabelBottomBorder: undefined,\n  keybindingLabelShadow: undefined\n};\nexport class KeybindingLabel extends Disposable {\n  constructor(container, os, options) {\n    super();\n    this.os = os;\n    this.keyElements = new Set();\n    this.options = options || Object.create(null);\n    const labelForeground = this.options.keybindingLabelForeground;\n    this.domNode = dom.append(container, $('.monaco-keybinding'));\n    if (labelForeground) {\n      this.domNode.style.color = labelForeground;\n    }\n    this.hover = this._register(getBaseLayerHoverDelegate().setupManagedHover(getDefaultHoverDelegate('mouse'), this.domNode, ''));\n    this.didEverRender = false;\n    container.appendChild(this.domNode);\n  }\n  get element() {\n    return this.domNode;\n  }\n  set(keybinding, matches) {\n    if (this.didEverRender && this.keybinding === keybinding && KeybindingLabel.areSame(this.matches, matches)) {\n      return;\n    }\n    this.keybinding = keybinding;\n    this.matches = matches;\n    this.render();\n  }\n  render() {\n    this.clear();\n    if (this.keybinding) {\n      const chords = this.keybinding.getChords();\n      if (chords[0]) {\n        this.renderChord(this.domNode, chords[0], this.matches ? this.matches.firstPart : null);\n      }\n      for (let i = 1; i < chords.length; i++) {\n        dom.append(this.domNode, $('span.monaco-keybinding-key-chord-separator', undefined, ' '));\n        this.renderChord(this.domNode, chords[i], this.matches ? this.matches.chordPart : null);\n      }\n      const title = this.options.disableTitle ?? false ? undefined : this.keybinding.getAriaLabel() || undefined;\n      this.hover.update(title);\n      this.domNode.setAttribute('aria-label', title || '');\n    } else if (this.options && this.options.renderUnboundKeybindings) {\n      this.renderUnbound(this.domNode);\n    }\n    this.didEverRender = true;\n  }\n  clear() {\n    dom.clearNode(this.domNode);\n    this.keyElements.clear();\n  }\n  renderChord(parent, chord, match) {\n    const modifierLabels = UILabelProvider.modifierLabels[this.os];\n    if (chord.ctrlKey) {\n      this.renderKey(parent, modifierLabels.ctrlKey, Boolean(match?.ctrlKey), modifierLabels.separator);\n    }\n    if (chord.shiftKey) {\n      this.renderKey(parent, modifierLabels.shiftKey, Boolean(match?.shiftKey), modifierLabels.separator);\n    }\n    if (chord.altKey) {\n      this.renderKey(parent, modifierLabels.altKey, Boolean(match?.altKey), modifierLabels.separator);\n    }\n    if (chord.metaKey) {\n      this.renderKey(parent, modifierLabels.metaKey, Boolean(match?.metaKey), modifierLabels.separator);\n    }\n    const keyLabel = chord.keyLabel;\n    if (keyLabel) {\n      this.renderKey(parent, keyLabel, Boolean(match?.keyCode), '');\n    }\n  }\n  renderKey(parent, label, highlight, separator) {\n    dom.append(parent, this.createKeyElement(label, highlight ? '.highlight' : ''));\n    if (separator) {\n      dom.append(parent, $('span.monaco-keybinding-key-separator', undefined, separator));\n    }\n  }\n  renderUnbound(parent) {\n    dom.append(parent, this.createKeyElement(localize('unbound', \"Unbound\")));\n  }\n  createKeyElement(label, extraClass = '') {\n    const keyElement = $('span.monaco-keybinding-key' + extraClass, undefined, label);\n    this.keyElements.add(keyElement);\n    if (this.options.keybindingLabelBackground) {\n      keyElement.style.backgroundColor = this.options.keybindingLabelBackground;\n    }\n    if (this.options.keybindingLabelBorder) {\n      keyElement.style.borderColor = this.options.keybindingLabelBorder;\n    }\n    if (this.options.keybindingLabelBottomBorder) {\n      keyElement.style.borderBottomColor = this.options.keybindingLabelBottomBorder;\n    }\n    if (this.options.keybindingLabelShadow) {\n      keyElement.style.boxShadow = `inset 0 -1px 0 ${this.options.keybindingLabelShadow}`;\n    }\n    return keyElement;\n  }\n  static areSame(a, b) {\n    if (a === b || !a && !b) {\n      return true;\n    }\n    return !!a && !!b && equals(a.firstPart, b.firstPart) && equals(a.chordPart, b.chordPart);\n  }\n}", "map": {"version": 3, "names": ["dom", "getBaseLayerHoverDelegate", "getDefaultHoverDelegate", "UILabe<PERSON>", "Disposable", "equals", "localize", "$", "unthemedKeybindingLabelOptions", "keybindingLabelBackground", "undefined", "keybindingLabelForeground", "keybindingLabelBorder", "keybindingLabelBottomBorder", "keybindingLabelShadow", "KeybindingLabel", "constructor", "container", "os", "options", "keyElements", "Set", "Object", "create", "labelForeground", "domNode", "append", "style", "color", "hover", "_register", "setupManagedHover", "did<PERSON><PERSON><PERSON><PERSON>", "append<PERSON><PERSON><PERSON>", "element", "set", "keybinding", "matches", "areSame", "render", "clear", "chords", "getChords", "renderChord", "firstPart", "i", "length", "chordPart", "title", "disable<PERSON><PERSON>le", "getAriaLabel", "update", "setAttribute", "renderUnboundKeybindings", "renderUnbound", "clearNode", "parent", "chord", "match", "modifierLabels", "ctrl<PERSON>ey", "<PERSON><PERSON><PERSON>", "Boolean", "separator", "shift<PERSON>ey", "altKey", "metaKey", "<PERSON><PERSON><PERSON><PERSON>", "keyCode", "label", "highlight", "createKeyElement", "extraClass", "keyElement", "add", "backgroundColor", "borderColor", "borderBottomColor", "boxShadow", "a", "b"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/browser/ui/keybindingLabel/keybindingLabel.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as dom from '../../dom.js';\nimport { getBaseLayerHoverDelegate } from '../hover/hoverDelegate2.js';\nimport { getDefaultHoverDelegate } from '../hover/hoverDelegateFactory.js';\nimport { UILabelProvider } from '../../../common/keybindingLabels.js';\nimport { Disposable } from '../../../common/lifecycle.js';\nimport { equals } from '../../../common/objects.js';\nimport './keybindingLabel.css';\nimport { localize } from '../../../../nls.js';\nconst $ = dom.$;\nexport const unthemedKeybindingLabelOptions = {\n    keybindingLabelBackground: undefined,\n    keybindingLabelForeground: undefined,\n    keybindingLabelBorder: undefined,\n    keybindingLabelBottomBorder: undefined,\n    keybindingLabelShadow: undefined\n};\nexport class KeybindingLabel extends Disposable {\n    constructor(container, os, options) {\n        super();\n        this.os = os;\n        this.keyElements = new Set();\n        this.options = options || Object.create(null);\n        const labelForeground = this.options.keybindingLabelForeground;\n        this.domNode = dom.append(container, $('.monaco-keybinding'));\n        if (labelForeground) {\n            this.domNode.style.color = labelForeground;\n        }\n        this.hover = this._register(getBaseLayerHoverDelegate().setupManagedHover(getDefaultHoverDelegate('mouse'), this.domNode, ''));\n        this.didEverRender = false;\n        container.appendChild(this.domNode);\n    }\n    get element() {\n        return this.domNode;\n    }\n    set(keybinding, matches) {\n        if (this.didEverRender && this.keybinding === keybinding && KeybindingLabel.areSame(this.matches, matches)) {\n            return;\n        }\n        this.keybinding = keybinding;\n        this.matches = matches;\n        this.render();\n    }\n    render() {\n        this.clear();\n        if (this.keybinding) {\n            const chords = this.keybinding.getChords();\n            if (chords[0]) {\n                this.renderChord(this.domNode, chords[0], this.matches ? this.matches.firstPart : null);\n            }\n            for (let i = 1; i < chords.length; i++) {\n                dom.append(this.domNode, $('span.monaco-keybinding-key-chord-separator', undefined, ' '));\n                this.renderChord(this.domNode, chords[i], this.matches ? this.matches.chordPart : null);\n            }\n            const title = (this.options.disableTitle ?? false) ? undefined : this.keybinding.getAriaLabel() || undefined;\n            this.hover.update(title);\n            this.domNode.setAttribute('aria-label', title || '');\n        }\n        else if (this.options && this.options.renderUnboundKeybindings) {\n            this.renderUnbound(this.domNode);\n        }\n        this.didEverRender = true;\n    }\n    clear() {\n        dom.clearNode(this.domNode);\n        this.keyElements.clear();\n    }\n    renderChord(parent, chord, match) {\n        const modifierLabels = UILabelProvider.modifierLabels[this.os];\n        if (chord.ctrlKey) {\n            this.renderKey(parent, modifierLabels.ctrlKey, Boolean(match?.ctrlKey), modifierLabels.separator);\n        }\n        if (chord.shiftKey) {\n            this.renderKey(parent, modifierLabels.shiftKey, Boolean(match?.shiftKey), modifierLabels.separator);\n        }\n        if (chord.altKey) {\n            this.renderKey(parent, modifierLabels.altKey, Boolean(match?.altKey), modifierLabels.separator);\n        }\n        if (chord.metaKey) {\n            this.renderKey(parent, modifierLabels.metaKey, Boolean(match?.metaKey), modifierLabels.separator);\n        }\n        const keyLabel = chord.keyLabel;\n        if (keyLabel) {\n            this.renderKey(parent, keyLabel, Boolean(match?.keyCode), '');\n        }\n    }\n    renderKey(parent, label, highlight, separator) {\n        dom.append(parent, this.createKeyElement(label, highlight ? '.highlight' : ''));\n        if (separator) {\n            dom.append(parent, $('span.monaco-keybinding-key-separator', undefined, separator));\n        }\n    }\n    renderUnbound(parent) {\n        dom.append(parent, this.createKeyElement(localize('unbound', \"Unbound\")));\n    }\n    createKeyElement(label, extraClass = '') {\n        const keyElement = $('span.monaco-keybinding-key' + extraClass, undefined, label);\n        this.keyElements.add(keyElement);\n        if (this.options.keybindingLabelBackground) {\n            keyElement.style.backgroundColor = this.options.keybindingLabelBackground;\n        }\n        if (this.options.keybindingLabelBorder) {\n            keyElement.style.borderColor = this.options.keybindingLabelBorder;\n        }\n        if (this.options.keybindingLabelBottomBorder) {\n            keyElement.style.borderBottomColor = this.options.keybindingLabelBottomBorder;\n        }\n        if (this.options.keybindingLabelShadow) {\n            keyElement.style.boxShadow = `inset 0 -1px 0 ${this.options.keybindingLabelShadow}`;\n        }\n        return keyElement;\n    }\n    static areSame(a, b) {\n        if (a === b || (!a && !b)) {\n            return true;\n        }\n        return !!a && !!b && equals(a.firstPart, b.firstPart) && equals(a.chordPart, b.chordPart);\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,KAAKA,GAAG,MAAM,cAAc;AACnC,SAASC,yBAAyB,QAAQ,4BAA4B;AACtE,SAASC,uBAAuB,QAAQ,kCAAkC;AAC1E,SAASC,eAAe,QAAQ,qCAAqC;AACrE,SAASC,UAAU,QAAQ,8BAA8B;AACzD,SAASC,MAAM,QAAQ,4BAA4B;AACnD,OAAO,uBAAuB;AAC9B,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,MAAMC,CAAC,GAAGP,GAAG,CAACO,CAAC;AACf,OAAO,MAAMC,8BAA8B,GAAG;EAC1CC,yBAAyB,EAAEC,SAAS;EACpCC,yBAAyB,EAAED,SAAS;EACpCE,qBAAqB,EAAEF,SAAS;EAChCG,2BAA2B,EAAEH,SAAS;EACtCI,qBAAqB,EAAEJ;AAC3B,CAAC;AACD,OAAO,MAAMK,eAAe,SAASX,UAAU,CAAC;EAC5CY,WAAWA,CAACC,SAAS,EAAEC,EAAE,EAAEC,OAAO,EAAE;IAChC,KAAK,CAAC,CAAC;IACP,IAAI,CAACD,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACE,WAAW,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC5B,IAAI,CAACF,OAAO,GAAGA,OAAO,IAAIG,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAC7C,MAAMC,eAAe,GAAG,IAAI,CAACL,OAAO,CAACR,yBAAyB;IAC9D,IAAI,CAACc,OAAO,GAAGzB,GAAG,CAAC0B,MAAM,CAACT,SAAS,EAAEV,CAAC,CAAC,oBAAoB,CAAC,CAAC;IAC7D,IAAIiB,eAAe,EAAE;MACjB,IAAI,CAACC,OAAO,CAACE,KAAK,CAACC,KAAK,GAAGJ,eAAe;IAC9C;IACA,IAAI,CAACK,KAAK,GAAG,IAAI,CAACC,SAAS,CAAC7B,yBAAyB,CAAC,CAAC,CAAC8B,iBAAiB,CAAC7B,uBAAuB,CAAC,OAAO,CAAC,EAAE,IAAI,CAACuB,OAAO,EAAE,EAAE,CAAC,CAAC;IAC9H,IAAI,CAACO,aAAa,GAAG,KAAK;IAC1Bf,SAAS,CAACgB,WAAW,CAAC,IAAI,CAACR,OAAO,CAAC;EACvC;EACA,IAAIS,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACT,OAAO;EACvB;EACAU,GAAGA,CAACC,UAAU,EAAEC,OAAO,EAAE;IACrB,IAAI,IAAI,CAACL,aAAa,IAAI,IAAI,CAACI,UAAU,KAAKA,UAAU,IAAIrB,eAAe,CAACuB,OAAO,CAAC,IAAI,CAACD,OAAO,EAAEA,OAAO,CAAC,EAAE;MACxG;IACJ;IACA,IAAI,CAACD,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACE,MAAM,CAAC,CAAC;EACjB;EACAA,MAAMA,CAAA,EAAG;IACL,IAAI,CAACC,KAAK,CAAC,CAAC;IACZ,IAAI,IAAI,CAACJ,UAAU,EAAE;MACjB,MAAMK,MAAM,GAAG,IAAI,CAACL,UAAU,CAACM,SAAS,CAAC,CAAC;MAC1C,IAAID,MAAM,CAAC,CAAC,CAAC,EAAE;QACX,IAAI,CAACE,WAAW,CAAC,IAAI,CAAClB,OAAO,EAAEgB,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACJ,OAAO,GAAG,IAAI,CAACA,OAAO,CAACO,SAAS,GAAG,IAAI,CAAC;MAC3F;MACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,MAAM,CAACK,MAAM,EAAED,CAAC,EAAE,EAAE;QACpC7C,GAAG,CAAC0B,MAAM,CAAC,IAAI,CAACD,OAAO,EAAElB,CAAC,CAAC,4CAA4C,EAAEG,SAAS,EAAE,GAAG,CAAC,CAAC;QACzF,IAAI,CAACiC,WAAW,CAAC,IAAI,CAAClB,OAAO,EAAEgB,MAAM,CAACI,CAAC,CAAC,EAAE,IAAI,CAACR,OAAO,GAAG,IAAI,CAACA,OAAO,CAACU,SAAS,GAAG,IAAI,CAAC;MAC3F;MACA,MAAMC,KAAK,GAAI,IAAI,CAAC7B,OAAO,CAAC8B,YAAY,IAAI,KAAK,GAAIvC,SAAS,GAAG,IAAI,CAAC0B,UAAU,CAACc,YAAY,CAAC,CAAC,IAAIxC,SAAS;MAC5G,IAAI,CAACmB,KAAK,CAACsB,MAAM,CAACH,KAAK,CAAC;MACxB,IAAI,CAACvB,OAAO,CAAC2B,YAAY,CAAC,YAAY,EAAEJ,KAAK,IAAI,EAAE,CAAC;IACxD,CAAC,MACI,IAAI,IAAI,CAAC7B,OAAO,IAAI,IAAI,CAACA,OAAO,CAACkC,wBAAwB,EAAE;MAC5D,IAAI,CAACC,aAAa,CAAC,IAAI,CAAC7B,OAAO,CAAC;IACpC;IACA,IAAI,CAACO,aAAa,GAAG,IAAI;EAC7B;EACAQ,KAAKA,CAAA,EAAG;IACJxC,GAAG,CAACuD,SAAS,CAAC,IAAI,CAAC9B,OAAO,CAAC;IAC3B,IAAI,CAACL,WAAW,CAACoB,KAAK,CAAC,CAAC;EAC5B;EACAG,WAAWA,CAACa,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAE;IAC9B,MAAMC,cAAc,GAAGxD,eAAe,CAACwD,cAAc,CAAC,IAAI,CAACzC,EAAE,CAAC;IAC9D,IAAIuC,KAAK,CAACG,OAAO,EAAE;MACf,IAAI,CAACC,SAAS,CAACL,MAAM,EAAEG,cAAc,CAACC,OAAO,EAAEE,OAAO,CAACJ,KAAK,EAAEE,OAAO,CAAC,EAAED,cAAc,CAACI,SAAS,CAAC;IACrG;IACA,IAAIN,KAAK,CAACO,QAAQ,EAAE;MAChB,IAAI,CAACH,SAAS,CAACL,MAAM,EAAEG,cAAc,CAACK,QAAQ,EAAEF,OAAO,CAACJ,KAAK,EAAEM,QAAQ,CAAC,EAAEL,cAAc,CAACI,SAAS,CAAC;IACvG;IACA,IAAIN,KAAK,CAACQ,MAAM,EAAE;MACd,IAAI,CAACJ,SAAS,CAACL,MAAM,EAAEG,cAAc,CAACM,MAAM,EAAEH,OAAO,CAACJ,KAAK,EAAEO,MAAM,CAAC,EAAEN,cAAc,CAACI,SAAS,CAAC;IACnG;IACA,IAAIN,KAAK,CAACS,OAAO,EAAE;MACf,IAAI,CAACL,SAAS,CAACL,MAAM,EAAEG,cAAc,CAACO,OAAO,EAAEJ,OAAO,CAACJ,KAAK,EAAEQ,OAAO,CAAC,EAAEP,cAAc,CAACI,SAAS,CAAC;IACrG;IACA,MAAMI,QAAQ,GAAGV,KAAK,CAACU,QAAQ;IAC/B,IAAIA,QAAQ,EAAE;MACV,IAAI,CAACN,SAAS,CAACL,MAAM,EAAEW,QAAQ,EAAEL,OAAO,CAACJ,KAAK,EAAEU,OAAO,CAAC,EAAE,EAAE,CAAC;IACjE;EACJ;EACAP,SAASA,CAACL,MAAM,EAAEa,KAAK,EAAEC,SAAS,EAAEP,SAAS,EAAE;IAC3C/D,GAAG,CAAC0B,MAAM,CAAC8B,MAAM,EAAE,IAAI,CAACe,gBAAgB,CAACF,KAAK,EAAEC,SAAS,GAAG,YAAY,GAAG,EAAE,CAAC,CAAC;IAC/E,IAAIP,SAAS,EAAE;MACX/D,GAAG,CAAC0B,MAAM,CAAC8B,MAAM,EAAEjD,CAAC,CAAC,sCAAsC,EAAEG,SAAS,EAAEqD,SAAS,CAAC,CAAC;IACvF;EACJ;EACAT,aAAaA,CAACE,MAAM,EAAE;IAClBxD,GAAG,CAAC0B,MAAM,CAAC8B,MAAM,EAAE,IAAI,CAACe,gBAAgB,CAACjE,QAAQ,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC;EAC7E;EACAiE,gBAAgBA,CAACF,KAAK,EAAEG,UAAU,GAAG,EAAE,EAAE;IACrC,MAAMC,UAAU,GAAGlE,CAAC,CAAC,4BAA4B,GAAGiE,UAAU,EAAE9D,SAAS,EAAE2D,KAAK,CAAC;IACjF,IAAI,CAACjD,WAAW,CAACsD,GAAG,CAACD,UAAU,CAAC;IAChC,IAAI,IAAI,CAACtD,OAAO,CAACV,yBAAyB,EAAE;MACxCgE,UAAU,CAAC9C,KAAK,CAACgD,eAAe,GAAG,IAAI,CAACxD,OAAO,CAACV,yBAAyB;IAC7E;IACA,IAAI,IAAI,CAACU,OAAO,CAACP,qBAAqB,EAAE;MACpC6D,UAAU,CAAC9C,KAAK,CAACiD,WAAW,GAAG,IAAI,CAACzD,OAAO,CAACP,qBAAqB;IACrE;IACA,IAAI,IAAI,CAACO,OAAO,CAACN,2BAA2B,EAAE;MAC1C4D,UAAU,CAAC9C,KAAK,CAACkD,iBAAiB,GAAG,IAAI,CAAC1D,OAAO,CAACN,2BAA2B;IACjF;IACA,IAAI,IAAI,CAACM,OAAO,CAACL,qBAAqB,EAAE;MACpC2D,UAAU,CAAC9C,KAAK,CAACmD,SAAS,GAAG,kBAAkB,IAAI,CAAC3D,OAAO,CAACL,qBAAqB,EAAE;IACvF;IACA,OAAO2D,UAAU;EACrB;EACA,OAAOnC,OAAOA,CAACyC,CAAC,EAAEC,CAAC,EAAE;IACjB,IAAID,CAAC,KAAKC,CAAC,IAAK,CAACD,CAAC,IAAI,CAACC,CAAE,EAAE;MACvB,OAAO,IAAI;IACf;IACA,OAAO,CAAC,CAACD,CAAC,IAAI,CAAC,CAACC,CAAC,IAAI3E,MAAM,CAAC0E,CAAC,CAACnC,SAAS,EAAEoC,CAAC,CAACpC,SAAS,CAAC,IAAIvC,MAAM,CAAC0E,CAAC,CAAChC,SAAS,EAAEiC,CAAC,CAACjC,SAAS,CAAC;EAC7F;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}