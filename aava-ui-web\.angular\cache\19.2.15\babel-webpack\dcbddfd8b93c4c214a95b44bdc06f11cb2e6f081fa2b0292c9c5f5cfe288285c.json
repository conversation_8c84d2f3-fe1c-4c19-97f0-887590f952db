{"ast": null, "code": "//#endregion\n//#region Utilities\nexport var FileKind = /*#__PURE__*/function (FileKind) {\n  FileKind[FileKind[\"FILE\"] = 0] = \"FILE\";\n  FileKind[FileKind[\"FOLDER\"] = 1] = \"FOLDER\";\n  FileKind[FileKind[\"ROOT_FOLDER\"] = 2] = \"ROOT_FOLDER\";\n  return FileKind;\n}(FileKind || {});\n\n//#endregion", "map": {"version": 3, "names": ["FileKind"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/platform/files/common/files.js"], "sourcesContent": ["//#endregion\n//#region Utilities\nexport var FileKind;\n(function (FileKind) {\n    FileKind[FileKind[\"FILE\"] = 0] = \"FILE\";\n    FileKind[FileKind[\"FOLDER\"] = 1] = \"FOLDER\";\n    FileKind[FileKind[\"ROOT_FOLDER\"] = 2] = \"ROOT_FOLDER\";\n})(FileKind || (FileKind = {}));\n//#endregion\n"], "mappings": "AAAA;AACA;AACA,OAAO,IAAIA,QAAQ,gBAClB,UAAUA,QAAQ,EAAE;EACjBA,QAAQ,CAACA,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EACvCA,QAAQ,CAACA,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ;EAC3CA,QAAQ,CAACA,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,GAAG,aAAa;EAAC,OAH/CA,QAAQ;AAInB,CAAC,CAAEA,QAAQ,IAAgB,CAAC,CAAE,CALX;;AAMnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}