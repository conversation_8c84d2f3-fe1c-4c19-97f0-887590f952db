{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { EventEmitter } from '@angular/core';\nimport { IconComponent } from '@ava/play-comp-library';\nimport { AgentStepperCardComponent } from '../agent-stepper-card/agent-stepper-card.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nfunction WorkflowPlaygroundComponent_div_9_app_agent_stepper_card_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-agent-stepper-card\", 13);\n    i0.ɵɵlistener(\"inputChanged\", function WorkflowPlaygroundComponent_div_9_app_agent_stepper_card_2_Template_app_agent_stepper_card_inputChanged_0_listener($event) {\n      const agent_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onAgentInputChange(agent_r2.id, $event.inputIndex, $event.value));\n    })(\"fileSelected\", function WorkflowPlaygroundComponent_div_9_app_agent_stepper_card_2_Template_app_agent_stepper_card_fileSelected_0_listener($event) {\n      const agent_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onAgentFileSelect(agent_r2.id, $event.inputIndex, $event.files));\n    })(\"messageSent\", function WorkflowPlaygroundComponent_div_9_app_agent_stepper_card_2_Template_app_agent_stepper_card_messageSent_0_listener($event) {\n      const i_r4 = i0.ɵɵrestoreView(_r1).index;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onMessageSent(i_r4, $event));\n    })(\"stepCompleted\", function WorkflowPlaygroundComponent_div_9_app_agent_stepper_card_2_Template_app_agent_stepper_card_stepCompleted_0_listener() {\n      const i_r4 = i0.ɵɵrestoreView(_r1).index;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onStepCompleted(i_r4));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const agent_r2 = ctx.$implicit;\n    const i_r4 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"agent\", agent_r2)(\"stepNumber\", i_r4 + 1)(\"isFirst\", i_r4 === 0)(\"isLast\", i_r4 === ctx_r2.agents.length - 1)(\"isActive\", ctx_r2.isStepActive(i_r4))(\"isCompleted\", ctx_r2.isStepCompleted(i_r4));\n  }\n}\nfunction WorkflowPlaygroundComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"div\", 11);\n    i0.ɵɵtemplate(2, WorkflowPlaygroundComponent_div_9_app_agent_stepper_card_2_Template, 1, 6, \"app-agent-stepper-card\", 12);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.agents)(\"ngForTrackBy\", ctx_r2.trackByAgentId);\n  }\n}\nfunction WorkflowPlaygroundComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"div\", 15);\n    i0.ɵɵelement(2, \"ava-icon\", 16);\n    i0.ɵɵelementStart(3, \"span\", 17);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"iconSize\", 24);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r2.agents.length, \" Agents\");\n  }\n}\nexport let WorkflowPlaygroundComponent = /*#__PURE__*/(() => {\n  class WorkflowPlaygroundComponent {\n    agents = [];\n    isCollapsed = false;\n    workflowName = 'Workflow';\n    backClicked = new EventEmitter();\n    collapseToggled = new EventEmitter();\n    agentInputChanged = new EventEmitter();\n    agentFileSelected = new EventEmitter();\n    messageSent = new EventEmitter();\n    currentActiveStep = 0;\n    completedSteps = new Set();\n    onBackClick() {\n      this.backClicked.emit();\n    }\n    onCollapseToggle() {\n      this.isCollapsed = !this.isCollapsed;\n      this.collapseToggled.emit(this.isCollapsed);\n    }\n    onAgentInputChange(agentId, inputIndex, value) {\n      this.agentInputChanged.emit({\n        agentId,\n        inputIndex,\n        value\n      });\n    }\n    onAgentFileSelect(agentId, inputIndex, files) {\n      this.agentFileSelected.emit({\n        agentId,\n        inputIndex,\n        files\n      });\n    }\n    onMessageSent(agentIndex, event) {\n      const agent = this.agents[agentIndex];\n      if (agent) {\n        this.messageSent.emit({\n          agentId: agent.id,\n          inputIndex: event.inputIndex,\n          value: event.value,\n          files: event.files\n        });\n      }\n    }\n    onStepCompleted(agentIndex) {\n      this.completedSteps.add(agentIndex);\n      // Move to next step if available\n      if (agentIndex === this.currentActiveStep && agentIndex < this.agents.length - 1) {\n        this.currentActiveStep = agentIndex + 1;\n      }\n    }\n    isStepActive(index) {\n      return index === this.currentActiveStep;\n    }\n    isStepCompleted(index) {\n      return this.completedSteps.has(index);\n    }\n    trackByAgentId(index, agent) {\n      return agent.id;\n    }\n    static ɵfac = function WorkflowPlaygroundComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || WorkflowPlaygroundComponent)();\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: WorkflowPlaygroundComponent,\n      selectors: [[\"app-workflow-playground\"]],\n      inputs: {\n        agents: \"agents\",\n        isCollapsed: \"isCollapsed\",\n        workflowName: \"workflowName\"\n      },\n      outputs: {\n        backClicked: \"backClicked\",\n        collapseToggled: \"collapseToggled\",\n        agentInputChanged: \"agentInputChanged\",\n        agentFileSelected: \"agentFileSelected\",\n        messageSent: \"messageSent\"\n      },\n      decls: 11,\n      vars: 9,\n      consts: [[1, \"workflow-playground-container\"], [1, \"playground-header\"], [1, \"header-left\"], [\"title\", \"Go Back\", 1, \"back-btn\", 3, \"click\"], [\"iconName\", \"arrow-left\", \"iconColor\", \"var(--color-brand-primary)\", 3, \"iconSize\"], [1, \"workflow-title\"], [1, \"collapse-btn\", 3, \"click\", \"title\"], [\"iconColor\", \"var(--color-brand-primary)\", 3, \"iconName\", \"iconSize\"], [\"class\", \"playground-content\", 4, \"ngIf\"], [\"class\", \"collapsed-content\", 4, \"ngIf\"], [1, \"playground-content\"], [1, \"agents-container\"], [3, \"agent\", \"stepNumber\", \"isFirst\", \"isLast\", \"isActive\", \"isCompleted\", \"inputChanged\", \"fileSelected\", \"messageSent\", \"stepCompleted\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [3, \"inputChanged\", \"fileSelected\", \"messageSent\", \"stepCompleted\", \"agent\", \"stepNumber\", \"isFirst\", \"isLast\", \"isActive\", \"isCompleted\"], [1, \"collapsed-content\"], [1, \"collapsed-info\"], [\"iconName\", \"workflow\", \"iconColor\", \"var(--color-brand-primary)\", 3, \"iconSize\"], [1, \"collapsed-text\"]],\n      template: function WorkflowPlaygroundComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"button\", 3);\n          i0.ɵɵlistener(\"click\", function WorkflowPlaygroundComponent_Template_button_click_3_listener() {\n            return ctx.onBackClick();\n          });\n          i0.ɵɵelement(4, \"ava-icon\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"h3\", 5);\n          i0.ɵɵtext(6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function WorkflowPlaygroundComponent_Template_button_click_7_listener() {\n            return ctx.onCollapseToggle();\n          });\n          i0.ɵɵelement(8, \"ava-icon\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(9, WorkflowPlaygroundComponent_div_9_Template, 3, 2, \"div\", 8)(10, WorkflowPlaygroundComponent_div_10_Template, 5, 2, \"div\", 9);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"collapsed\", ctx.isCollapsed);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"iconSize\", 20);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.workflowName);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"title\", ctx.isCollapsed ? \"Expand Panel\" : \"Collapse Panel\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"iconName\", ctx.isCollapsed ? \"panel-right\" : \"panel-left\")(\"iconSize\", 20);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isCollapsed);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isCollapsed);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, i1.NgIf, IconComponent, AgentStepperCardComponent],\n      styles: [\".workflow-playground-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%;\\n  background: var(--card-bg);\\n  border-radius: 8px;\\n  box-shadow: 0 4px 15px var(--card-shadow, rgba(0, 0, 0, 0.05));\\n  transition: all 0.3s ease;\\n  overflow: hidden;\\n}\\n.workflow-playground-container.collapsed[_ngcontent-%COMP%] {\\n  width: 60px;\\n  min-width: 60px;\\n}\\n\\n.playground-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 16px 20px;\\n  background: #E6F3FF;\\n  color: #000000;\\n  min-height: 64px;\\n  box-sizing: border-box;\\n}\\n.playground-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  flex: 1;\\n  min-width: 0;\\n}\\n.playground-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 36px;\\n  height: 36px;\\n  border: none;\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 6px;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  flex-shrink: 0;\\n}\\n.playground-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.2);\\n  transform: translateX(-2px);\\n}\\n.playground-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%]:active {\\n  transform: translateX(-1px);\\n}\\n.playground-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .workflow-title[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: white;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  min-width: 0;\\n}\\n.playground-header[_ngcontent-%COMP%]   .collapse-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 36px;\\n  height: 36px;\\n  border: none;\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 6px;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  flex-shrink: 0;\\n}\\n.playground-header[_ngcontent-%COMP%]   .collapse-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.2);\\n}\\n\\n.playground-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow: hidden;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.agents-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: 20px;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0;\\n}\\n.agents-container[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n.agents-container[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: var(--scrollbar-track, #f1f1f1);\\n  border-radius: 3px;\\n}\\n.agents-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: var(--scrollbar-thumb, #c1c1c1);\\n  border-radius: 3px;\\n}\\n.agents-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: var(--scrollbar-thumb-hover, #a8a8a8);\\n}\\n\\n.collapsed-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 20px 10px;\\n}\\n.collapsed-content[_ngcontent-%COMP%]   .collapsed-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 8px;\\n  text-align: center;\\n}\\n.collapsed-content[_ngcontent-%COMP%]   .collapsed-info[_ngcontent-%COMP%]   .collapsed-text[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: var(--text-secondary);\\n  writing-mode: vertical-rl;\\n  text-orientation: mixed;\\n}\\n\\n@media (max-width: 1024px) {\\n  .workflow-playground-container.collapsed[_ngcontent-%COMP%] {\\n    width: 55px;\\n    min-width: 55px;\\n  }\\n  .playground-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .workflow-title[_ngcontent-%COMP%] {\\n    font-size: 17px;\\n  }\\n  .agents-container[_ngcontent-%COMP%] {\\n    padding: 18px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .workflow-playground-container.collapsed[_ngcontent-%COMP%] {\\n    width: 50px;\\n    min-width: 50px;\\n  }\\n  .playground-header[_ngcontent-%COMP%] {\\n    padding: 12px 16px;\\n    min-height: 56px;\\n  }\\n  .playground-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%] {\\n    gap: 8px;\\n  }\\n  .playground-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%], \\n   .playground-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .collapse-btn[_ngcontent-%COMP%] {\\n    width: 32px;\\n    height: 32px;\\n  }\\n  .playground-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .workflow-title[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n  .agents-container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .workflow-playground-container.collapsed[_ngcontent-%COMP%] {\\n    width: 45px;\\n    min-width: 45px;\\n  }\\n  .playground-header[_ngcontent-%COMP%] {\\n    padding: 10px 12px;\\n    min-height: 52px;\\n  }\\n  .playground-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%] {\\n    gap: 6px;\\n  }\\n  .playground-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%], \\n   .playground-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .collapse-btn[_ngcontent-%COMP%] {\\n    width: 28px;\\n    height: 28px;\\n  }\\n  .playground-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .workflow-title[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n  .agents-container[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n  .collapsed-content[_ngcontent-%COMP%] {\\n    padding: 16px 8px;\\n  }\\n  .collapsed-content[_ngcontent-%COMP%]   .collapsed-info[_ngcontent-%COMP%] {\\n    gap: 6px;\\n  }\\n  .collapsed-content[_ngcontent-%COMP%]   .collapsed-info[_ngcontent-%COMP%]   .collapsed-text[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return WorkflowPlaygroundComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "EventEmitter", "IconComponent", "AgentStepperCardComponent", "i0", "ɵɵelementStart", "ɵɵlistener", "WorkflowPlaygroundComponent_div_9_app_agent_stepper_card_2_Template_app_agent_stepper_card_inputChanged_0_listener", "$event", "agent_r2", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "onAgentInputChange", "id", "inputIndex", "value", "WorkflowPlaygroundComponent_div_9_app_agent_stepper_card_2_Template_app_agent_stepper_card_fileSelected_0_listener", "onAgentFileSelect", "files", "WorkflowPlaygroundComponent_div_9_app_agent_stepper_card_2_Template_app_agent_stepper_card_messageSent_0_listener", "i_r4", "index", "onMessageSent", "WorkflowPlaygroundComponent_div_9_app_agent_stepper_card_2_Template_app_agent_stepper_card_stepCompleted_0_listener", "onStepCompleted", "ɵɵelementEnd", "ɵɵproperty", "agents", "length", "isStepActive", "isStepCompleted", "ɵɵtemplate", "WorkflowPlaygroundComponent_div_9_app_agent_stepper_card_2_Template", "ɵɵadvance", "trackByAgentId", "ɵɵelement", "ɵɵtext", "ɵɵtextInterpolate1", "WorkflowPlaygroundComponent", "isCollapsed", "workflowName", "backClicked", "collapseToggled", "agentInputChanged", "agentFileSelected", "messageSent", "currentActiveStep", "completedSteps", "Set", "onBackClick", "emit", "onCollapseToggle", "agentId", "agentIndex", "event", "agent", "add", "has", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "WorkflowPlaygroundComponent_Template", "rf", "ctx", "WorkflowPlaygroundComponent_Template_button_click_3_listener", "WorkflowPlaygroundComponent_Template_button_click_7_listener", "WorkflowPlaygroundComponent_div_9_Template", "WorkflowPlaygroundComponent_div_10_Template", "ɵɵclassProp", "ɵɵtextInterpolate", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\pages\\workflows\\workflow-execution\\components\\workflow-playground\\workflow-playground.component.ts", "C:\\console\\aava-ui-web\\projects\\shared\\pages\\workflows\\workflow-execution\\components\\workflow-playground\\workflow-playground.component.html"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { Component, EventEmitter, Input, Output } from '@angular/core';\nimport { IconComponent } from '@ava/play-comp-library';\nimport { AgentStepperCardComponent } from '../agent-stepper-card/agent-stepper-card.component';\n\nexport interface AgentData {\n  id: number;\n  name: string;\n  role: string;\n  goal: string;\n  backstory: string;\n  task: {\n    description: string;\n    expectedOutput: string;\n  };\n  serial: number;\n  inputs?: AgentInput[];\n  hasInputs?: boolean;\n}\n\nexport interface AgentInput {\n  placeholder: string;\n  inputName: string;\n  inputType: 'text' | 'image';\n  value?: string;\n  files?: File[];\n}\n\n@Component({\n  selector: 'app-workflow-playground',\n  standalone: true,\n  imports: [\n    CommonModule,\n    IconComponent,\n    AgentStepperCardComponent\n  ],\n  templateUrl: './workflow-playground.component.html',\n  styleUrls: ['./workflow-playground.component.scss']\n})\nexport class WorkflowPlaygroundComponent {\n  @Input() agents: AgentData[] = [];\n  @Input() isCollapsed: boolean = false;\n  @Input() workflowName: string = 'Workflow';\n\n  @Output() backClicked = new EventEmitter<void>();\n  @Output() collapseToggled = new EventEmitter<boolean>();\n  @Output() agentInputChanged = new EventEmitter<{agentId: number, inputIndex: number, value: string}>();\n  @Output() agentFileSelected = new EventEmitter<{agentId: number, inputIndex: number, files: File[]}>();\n  @Output() messageSent = new EventEmitter<{agentId: number, inputIndex: number, value: string, files?: File[]}>();\n\n  currentActiveStep: number = 0;\n  completedSteps: Set<number> = new Set();\n\n  onBackClick(): void {\n    this.backClicked.emit();\n  }\n\n  onCollapseToggle(): void {\n    this.isCollapsed = !this.isCollapsed;\n    this.collapseToggled.emit(this.isCollapsed);\n  }\n\n  onAgentInputChange(agentId: number, inputIndex: number, value: string): void {\n    this.agentInputChanged.emit({ agentId, inputIndex, value });\n  }\n\n  onAgentFileSelect(agentId: number, inputIndex: number, files: File[]): void {\n    this.agentFileSelected.emit({ agentId, inputIndex, files });\n  }\n\n  onMessageSent(agentIndex: number, event: {inputIndex: number, value: string, files?: File[]}): void {\n    const agent = this.agents[agentIndex];\n    if (agent) {\n      this.messageSent.emit({\n        agentId: agent.id,\n        inputIndex: event.inputIndex,\n        value: event.value,\n        files: event.files\n      });\n    }\n  }\n\n  onStepCompleted(agentIndex: number): void {\n    this.completedSteps.add(agentIndex);\n\n    // Move to next step if available\n    if (agentIndex === this.currentActiveStep && agentIndex < this.agents.length - 1) {\n      this.currentActiveStep = agentIndex + 1;\n    }\n  }\n\n  isStepActive(index: number): boolean {\n    return index === this.currentActiveStep;\n  }\n\n  isStepCompleted(index: number): boolean {\n    return this.completedSteps.has(index);\n  }\n\n  trackByAgentId(index: number, agent: AgentData): number {\n    return agent.id;\n  }\n}\n", "<div class=\"workflow-playground-container\" [class.collapsed]=\"isCollapsed\">\n  <!-- Header Section -->\n  <div class=\"playground-header\">\n    <div class=\"header-left\">\n      <button class=\"back-btn\" (click)=\"onBackClick()\" title=\"Go Back\">\n        <ava-icon iconName=\"arrow-left\" [iconSize]=\"20\" iconColor=\"var(--color-brand-primary)\"></ava-icon>\n      </button>\n      <h3 class=\"workflow-title\">{{ workflowName }}</h3>\n    </div>\n    \n    <button class=\"collapse-btn\" (click)=\"onCollapseToggle()\" [title]=\"isCollapsed ? 'Expand Panel' : 'Collapse Panel'\">\n      <ava-icon \n        [iconName]=\"isCollapsed ? 'panel-right' : 'panel-left'\" \n        [iconSize]=\"20\" \n        iconColor=\"var(--color-brand-primary)\">\n      </ava-icon>\n    </button>\n  </div>\n\n  <!-- Content Section -->\n  <div class=\"playground-content\" *ngIf=\"!isCollapsed\">\n    <div class=\"agents-container\">\n      <app-agent-stepper-card\n        *ngFor=\"let agent of agents; let i = index; trackBy: trackByAgentId\"\n        [agent]=\"agent\"\n        [stepNumber]=\"i + 1\"\n        [isFirst]=\"i === 0\"\n        [isLast]=\"i === agents.length - 1\"\n        [isActive]=\"isStepActive(i)\"\n        [isCompleted]=\"isStepCompleted(i)\"\n        (inputChanged)=\"onAgentInputChange(agent.id, $event.inputIndex, $event.value)\"\n        (fileSelected)=\"onAgentFileSelect(agent.id, $event.inputIndex, $event.files)\"\n        (messageSent)=\"onMessageSent(i, $event)\"\n        (stepCompleted)=\"onStepCompleted(i)\">\n      </app-agent-stepper-card>\n    </div>\n  </div>\n\n  <!-- Collapsed State -->\n  <div class=\"collapsed-content\" *ngIf=\"isCollapsed\">\n    <div class=\"collapsed-info\">\n      <ava-icon iconName=\"workflow\" [iconSize]=\"24\" iconColor=\"var(--color-brand-primary)\"></ava-icon>\n      <span class=\"collapsed-text\">{{ agents.length }} Agents</span>\n    </div>\n  </div>\n</div>\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAAoBC,YAAY,QAAuB,eAAe;AACtE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,yBAAyB,QAAQ,oDAAoD;;;;;;ICmBxFC,EAAA,CAAAC,cAAA,iCAWuC;IAArCD,EAHA,CAAAE,UAAA,0BAAAC,mHAAAC,MAAA;MAAA,MAAAC,QAAA,GAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAgBF,MAAA,CAAAG,kBAAA,CAAAP,QAAA,CAAAQ,EAAA,EAAAT,MAAA,CAAAU,UAAA,EAAAV,MAAA,CAAAW,KAAA,CAA6D;IAAA,EAAC,0BAAAC,mHAAAZ,MAAA;MAAA,MAAAC,QAAA,GAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAC9DF,MAAA,CAAAQ,iBAAA,CAAAZ,QAAA,CAAAQ,EAAA,EAAAT,MAAA,CAAAU,UAAA,EAAAV,MAAA,CAAAc,KAAA,CAA4D;IAAA,EAAC,yBAAAC,kHAAAf,MAAA;MAAA,MAAAgB,IAAA,GAAApB,EAAA,CAAAM,aAAA,CAAAC,GAAA,EAAAc,KAAA;MAAA,MAAAZ,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAC9DF,MAAA,CAAAa,aAAA,CAAAF,IAAA,EAAAhB,MAAA,CAAwB;IAAA,EAAC,2BAAAmB,oHAAA;MAAA,MAAAH,IAAA,GAAApB,EAAA,CAAAM,aAAA,CAAAC,GAAA,EAAAc,KAAA;MAAA,MAAAZ,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CACvBF,MAAA,CAAAe,eAAA,CAAAJ,IAAA,CAAkB;IAAA,EAAC;IACtCpB,EAAA,CAAAyB,YAAA,EAAyB;;;;;;IALvBzB,EALA,CAAA0B,UAAA,UAAArB,QAAA,CAAe,eAAAe,IAAA,KACK,YAAAA,IAAA,OACD,WAAAA,IAAA,KAAAX,MAAA,CAAAkB,MAAA,CAAAC,MAAA,KACe,aAAAnB,MAAA,CAAAoB,YAAA,CAAAT,IAAA,EACN,gBAAAX,MAAA,CAAAqB,eAAA,CAAAV,IAAA,EACM;;;;;IARtCpB,EADF,CAAAC,cAAA,cAAqD,cACrB;IAC5BD,EAAA,CAAA+B,UAAA,IAAAC,mEAAA,qCAWuC;IAG3ChC,EADE,CAAAyB,YAAA,EAAM,EACF;;;;IAbkBzB,EAAA,CAAAiC,SAAA,GAAW;IAAejC,EAA1B,CAAA0B,UAAA,YAAAjB,MAAA,CAAAkB,MAAA,CAAW,iBAAAlB,MAAA,CAAAyB,cAAA,CAAsC;;;;;IAiBvElC,EADF,CAAAC,cAAA,cAAmD,cACrB;IAC1BD,EAAA,CAAAmC,SAAA,mBAAgG;IAChGnC,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAoC,MAAA,GAA0B;IAE3DpC,EAF2D,CAAAyB,YAAA,EAAO,EAC1D,EACF;;;;IAH4BzB,EAAA,CAAAiC,SAAA,GAAe;IAAfjC,EAAA,CAAA0B,UAAA,gBAAe;IAChB1B,EAAA,CAAAiC,SAAA,GAA0B;IAA1BjC,EAAA,CAAAqC,kBAAA,KAAA5B,MAAA,CAAAkB,MAAA,CAAAC,MAAA,YAA0B;;;ADH7D,WAAaU,2BAA2B;EAAlC,MAAOA,2BAA2B;IAC7BX,MAAM,GAAgB,EAAE;IACxBY,WAAW,GAAY,KAAK;IAC5BC,YAAY,GAAW,UAAU;IAEhCC,WAAW,GAAG,IAAI5C,YAAY,EAAQ;IACtC6C,eAAe,GAAG,IAAI7C,YAAY,EAAW;IAC7C8C,iBAAiB,GAAG,IAAI9C,YAAY,EAAwD;IAC5F+C,iBAAiB,GAAG,IAAI/C,YAAY,EAAwD;IAC5FgD,WAAW,GAAG,IAAIhD,YAAY,EAAwE;IAEhHiD,iBAAiB,GAAW,CAAC;IAC7BC,cAAc,GAAgB,IAAIC,GAAG,EAAE;IAEvCC,WAAWA,CAAA;MACT,IAAI,CAACR,WAAW,CAACS,IAAI,EAAE;IACzB;IAEAC,gBAAgBA,CAAA;MACd,IAAI,CAACZ,WAAW,GAAG,CAAC,IAAI,CAACA,WAAW;MACpC,IAAI,CAACG,eAAe,CAACQ,IAAI,CAAC,IAAI,CAACX,WAAW,CAAC;IAC7C;IAEA3B,kBAAkBA,CAACwC,OAAe,EAAEtC,UAAkB,EAAEC,KAAa;MACnE,IAAI,CAAC4B,iBAAiB,CAACO,IAAI,CAAC;QAAEE,OAAO;QAAEtC,UAAU;QAAEC;MAAK,CAAE,CAAC;IAC7D;IAEAE,iBAAiBA,CAACmC,OAAe,EAAEtC,UAAkB,EAAEI,KAAa;MAClE,IAAI,CAAC0B,iBAAiB,CAACM,IAAI,CAAC;QAAEE,OAAO;QAAEtC,UAAU;QAAEI;MAAK,CAAE,CAAC;IAC7D;IAEAI,aAAaA,CAAC+B,UAAkB,EAAEC,KAA0D;MAC1F,MAAMC,KAAK,GAAG,IAAI,CAAC5B,MAAM,CAAC0B,UAAU,CAAC;MACrC,IAAIE,KAAK,EAAE;QACT,IAAI,CAACV,WAAW,CAACK,IAAI,CAAC;UACpBE,OAAO,EAAEG,KAAK,CAAC1C,EAAE;UACjBC,UAAU,EAAEwC,KAAK,CAACxC,UAAU;UAC5BC,KAAK,EAAEuC,KAAK,CAACvC,KAAK;UAClBG,KAAK,EAAEoC,KAAK,CAACpC;SACd,CAAC;MACJ;IACF;IAEAM,eAAeA,CAAC6B,UAAkB;MAChC,IAAI,CAACN,cAAc,CAACS,GAAG,CAACH,UAAU,CAAC;MAEnC;MACA,IAAIA,UAAU,KAAK,IAAI,CAACP,iBAAiB,IAAIO,UAAU,GAAG,IAAI,CAAC1B,MAAM,CAACC,MAAM,GAAG,CAAC,EAAE;QAChF,IAAI,CAACkB,iBAAiB,GAAGO,UAAU,GAAG,CAAC;MACzC;IACF;IAEAxB,YAAYA,CAACR,KAAa;MACxB,OAAOA,KAAK,KAAK,IAAI,CAACyB,iBAAiB;IACzC;IAEAhB,eAAeA,CAACT,KAAa;MAC3B,OAAO,IAAI,CAAC0B,cAAc,CAACU,GAAG,CAACpC,KAAK,CAAC;IACvC;IAEAa,cAAcA,CAACb,KAAa,EAAEkC,KAAgB;MAC5C,OAAOA,KAAK,CAAC1C,EAAE;IACjB;;uCA9DWyB,2BAA2B;IAAA;;YAA3BA,2BAA2B;MAAAoB,SAAA;MAAAC,MAAA;QAAAhC,MAAA;QAAAY,WAAA;QAAAC,YAAA;MAAA;MAAAoB,OAAA;QAAAnB,WAAA;QAAAC,eAAA;QAAAC,iBAAA;QAAAC,iBAAA;QAAAC,WAAA;MAAA;MAAAgB,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCnClClE,EAJN,CAAAC,cAAA,aAA2E,aAE1C,aACJ,gBAC0C;UAAxCD,EAAA,CAAAE,UAAA,mBAAAkE,6DAAA;YAAA,OAASD,GAAA,CAAAlB,WAAA,EAAa;UAAA,EAAC;UAC9CjD,EAAA,CAAAmC,SAAA,kBAAkG;UACpGnC,EAAA,CAAAyB,YAAA,EAAS;UACTzB,EAAA,CAAAC,cAAA,YAA2B;UAAAD,EAAA,CAAAoC,MAAA,GAAkB;UAC/CpC,EAD+C,CAAAyB,YAAA,EAAK,EAC9C;UAENzB,EAAA,CAAAC,cAAA,gBAAoH;UAAvFD,EAAA,CAAAE,UAAA,mBAAAmE,6DAAA;YAAA,OAASF,GAAA,CAAAhB,gBAAA,EAAkB;UAAA,EAAC;UACvDnD,EAAA,CAAAmC,SAAA,kBAIW;UAEfnC,EADE,CAAAyB,YAAA,EAAS,EACL;UAsBNzB,EAnBA,CAAA+B,UAAA,IAAAuC,0CAAA,iBAAqD,KAAAC,2CAAA,iBAmBF;UAMrDvE,EAAA,CAAAyB,YAAA,EAAM;;;UA7CqCzB,EAAA,CAAAwE,WAAA,cAAAL,GAAA,CAAA5B,WAAA,CAA+B;UAKlCvC,EAAA,CAAAiC,SAAA,GAAe;UAAfjC,EAAA,CAAA0B,UAAA,gBAAe;UAEtB1B,EAAA,CAAAiC,SAAA,GAAkB;UAAlBjC,EAAA,CAAAyE,iBAAA,CAAAN,GAAA,CAAA3B,YAAA,CAAkB;UAGWxC,EAAA,CAAAiC,SAAA,EAAyD;UAAzDjC,EAAA,CAAA0B,UAAA,UAAAyC,GAAA,CAAA5B,WAAA,qCAAyD;UAE/GvC,EAAA,CAAAiC,SAAA,EAAuD;UACvDjC,EADA,CAAA0B,UAAA,aAAAyC,GAAA,CAAA5B,WAAA,gCAAuD,gBACxC;UAOYvC,EAAA,CAAAiC,SAAA,EAAkB;UAAlBjC,EAAA,CAAA0B,UAAA,UAAAyC,GAAA,CAAA5B,WAAA,CAAkB;UAmBnBvC,EAAA,CAAAiC,SAAA,EAAiB;UAAjBjC,EAAA,CAAA0B,UAAA,SAAAyC,GAAA,CAAA5B,WAAA,CAAiB;;;qBDP/C3C,YAAY,EAAA8E,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZ9E,aAAa,EACbC,yBAAyB;MAAA8E,MAAA;IAAA;;SAKhBvC,2BAA2B;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}