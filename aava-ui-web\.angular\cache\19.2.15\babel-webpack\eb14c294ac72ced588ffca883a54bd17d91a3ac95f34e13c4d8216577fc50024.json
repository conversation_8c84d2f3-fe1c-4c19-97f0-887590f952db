{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n/**\n * Returns:\n *  - -1 => the line consists of whitespace\n *  - otherwise => the indent level is returned value\n */\nexport function computeIndentLevel(line, tabSize) {\n  let indent = 0;\n  let i = 0;\n  const len = line.length;\n  while (i < len) {\n    const chCode = line.charCodeAt(i);\n    if (chCode === 32 /* CharCode.Space */) {\n      indent++;\n    } else if (chCode === 9 /* CharCode.Tab */) {\n      indent = indent - indent % tabSize + tabSize;\n    } else {\n      break;\n    }\n    i++;\n  }\n  if (i === len) {\n    return -1; // line only consists of whitespace\n  }\n  return indent;\n}", "map": {"version": 3, "names": ["computeIndentLevel", "line", "tabSize", "indent", "i", "len", "length", "chCode", "charCodeAt"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/model/utils.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n/**\n * Returns:\n *  - -1 => the line consists of whitespace\n *  - otherwise => the indent level is returned value\n */\nexport function computeIndentLevel(line, tabSize) {\n    let indent = 0;\n    let i = 0;\n    const len = line.length;\n    while (i < len) {\n        const chCode = line.charCodeAt(i);\n        if (chCode === 32 /* CharCode.Space */) {\n            indent++;\n        }\n        else if (chCode === 9 /* CharCode.Tab */) {\n            indent = indent - indent % tabSize + tabSize;\n        }\n        else {\n            break;\n        }\n        i++;\n    }\n    if (i === len) {\n        return -1; // line only consists of whitespace\n    }\n    return indent;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,kBAAkBA,CAACC,IAAI,EAAEC,OAAO,EAAE;EAC9C,IAAIC,MAAM,GAAG,CAAC;EACd,IAAIC,CAAC,GAAG,CAAC;EACT,MAAMC,GAAG,GAAGJ,IAAI,CAACK,MAAM;EACvB,OAAOF,CAAC,GAAGC,GAAG,EAAE;IACZ,MAAME,MAAM,GAAGN,IAAI,CAACO,UAAU,CAACJ,CAAC,CAAC;IACjC,IAAIG,MAAM,KAAK,EAAE,CAAC,sBAAsB;MACpCJ,MAAM,EAAE;IACZ,CAAC,MACI,IAAII,MAAM,KAAK,CAAC,CAAC,oBAAoB;MACtCJ,MAAM,GAAGA,MAAM,GAAGA,MAAM,GAAGD,OAAO,GAAGA,OAAO;IAChD,CAAC,MACI;MACD;IACJ;IACAE,CAAC,EAAE;EACP;EACA,IAAIA,CAAC,KAAKC,GAAG,EAAE;IACX,OAAO,CAAC,CAAC,CAAC,CAAC;EACf;EACA,OAAOF,MAAM;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}