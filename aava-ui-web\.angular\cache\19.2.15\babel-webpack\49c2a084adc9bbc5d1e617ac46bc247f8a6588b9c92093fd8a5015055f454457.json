{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as arrays from './arrays.js';\nimport * as types from './types.js';\nimport * as nls from '../../nls.js';\nfunction exceptionToErrorMessage(exception, verbose) {\n  if (verbose && (exception.stack || exception.stacktrace)) {\n    return nls.localize('stackTrace.format', \"{0}: {1}\", detectSystemErrorMessage(exception), stackToString(exception.stack) || stackToString(exception.stacktrace));\n  }\n  return detectSystemErrorMessage(exception);\n}\nfunction stackToString(stack) {\n  if (Array.isArray(stack)) {\n    return stack.join('\\n');\n  }\n  return stack;\n}\nfunction detectSystemErrorMessage(exception) {\n  // Custom node.js error from us\n  if (exception.code === 'ERR_UNC_HOST_NOT_ALLOWED') {\n    return `${exception.message}. Please update the 'security.allowedUNCHosts' setting if you want to allow this host.`;\n  }\n  // See https://nodejs.org/api/errors.html#errors_class_system_error\n  if (typeof exception.code === 'string' && typeof exception.errno === 'number' && typeof exception.syscall === 'string') {\n    return nls.localize('nodeExceptionMessage', \"A system error occurred ({0})\", exception.message);\n  }\n  return exception.message || nls.localize('error.defaultMessage', \"An unknown error occurred. Please consult the log for more details.\");\n}\n/**\n * Tries to generate a human readable error message out of the error. If the verbose parameter\n * is set to true, the error message will include stacktrace details if provided.\n *\n * @returns A string containing the error message.\n */\nexport function toErrorMessage(error = null, verbose = false) {\n  if (!error) {\n    return nls.localize('error.defaultMessage', \"An unknown error occurred. Please consult the log for more details.\");\n  }\n  if (Array.isArray(error)) {\n    const errors = arrays.coalesce(error);\n    const msg = toErrorMessage(errors[0], verbose);\n    if (errors.length > 1) {\n      return nls.localize('error.moreErrors', \"{0} ({1} errors in total)\", msg, errors.length);\n    }\n    return msg;\n  }\n  if (types.isString(error)) {\n    return error;\n  }\n  if (error.detail) {\n    const detail = error.detail;\n    if (detail.error) {\n      return exceptionToErrorMessage(detail.error, verbose);\n    }\n    if (detail.exception) {\n      return exceptionToErrorMessage(detail.exception, verbose);\n    }\n  }\n  if (error.stack) {\n    return exceptionToErrorMessage(error, verbose);\n  }\n  if (error.message) {\n    return error.message;\n  }\n  return nls.localize('error.defaultMessage', \"An unknown error occurred. Please consult the log for more details.\");\n}", "map": {"version": 3, "names": ["arrays", "types", "nls", "exceptionToErrorMessage", "exception", "verbose", "stack", "stacktrace", "localize", "detectSystemErrorMessage", "stackToString", "Array", "isArray", "join", "code", "message", "errno", "syscall", "toErrorMessage", "error", "errors", "coalesce", "msg", "length", "isString", "detail"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/common/errorMessage.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as arrays from './arrays.js';\nimport * as types from './types.js';\nimport * as nls from '../../nls.js';\nfunction exceptionToErrorMessage(exception, verbose) {\n    if (verbose && (exception.stack || exception.stacktrace)) {\n        return nls.localize('stackTrace.format', \"{0}: {1}\", detectSystemErrorMessage(exception), stackToString(exception.stack) || stackToString(exception.stacktrace));\n    }\n    return detectSystemErrorMessage(exception);\n}\nfunction stackToString(stack) {\n    if (Array.isArray(stack)) {\n        return stack.join('\\n');\n    }\n    return stack;\n}\nfunction detectSystemErrorMessage(exception) {\n    // Custom node.js error from us\n    if (exception.code === 'ERR_UNC_HOST_NOT_ALLOWED') {\n        return `${exception.message}. Please update the 'security.allowedUNCHosts' setting if you want to allow this host.`;\n    }\n    // See https://nodejs.org/api/errors.html#errors_class_system_error\n    if (typeof exception.code === 'string' && typeof exception.errno === 'number' && typeof exception.syscall === 'string') {\n        return nls.localize('nodeExceptionMessage', \"A system error occurred ({0})\", exception.message);\n    }\n    return exception.message || nls.localize('error.defaultMessage', \"An unknown error occurred. Please consult the log for more details.\");\n}\n/**\n * Tries to generate a human readable error message out of the error. If the verbose parameter\n * is set to true, the error message will include stacktrace details if provided.\n *\n * @returns A string containing the error message.\n */\nexport function toErrorMessage(error = null, verbose = false) {\n    if (!error) {\n        return nls.localize('error.defaultMessage', \"An unknown error occurred. Please consult the log for more details.\");\n    }\n    if (Array.isArray(error)) {\n        const errors = arrays.coalesce(error);\n        const msg = toErrorMessage(errors[0], verbose);\n        if (errors.length > 1) {\n            return nls.localize('error.moreErrors', \"{0} ({1} errors in total)\", msg, errors.length);\n        }\n        return msg;\n    }\n    if (types.isString(error)) {\n        return error;\n    }\n    if (error.detail) {\n        const detail = error.detail;\n        if (detail.error) {\n            return exceptionToErrorMessage(detail.error, verbose);\n        }\n        if (detail.exception) {\n            return exceptionToErrorMessage(detail.exception, verbose);\n        }\n    }\n    if (error.stack) {\n        return exceptionToErrorMessage(error, verbose);\n    }\n    if (error.message) {\n        return error.message;\n    }\n    return nls.localize('error.defaultMessage', \"An unknown error occurred. Please consult the log for more details.\");\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,aAAa;AACrC,OAAO,KAAKC,KAAK,MAAM,YAAY;AACnC,OAAO,KAAKC,GAAG,MAAM,cAAc;AACnC,SAASC,uBAAuBA,CAACC,SAAS,EAAEC,OAAO,EAAE;EACjD,IAAIA,OAAO,KAAKD,SAAS,CAACE,KAAK,IAAIF,SAAS,CAACG,UAAU,CAAC,EAAE;IACtD,OAAOL,GAAG,CAACM,QAAQ,CAAC,mBAAmB,EAAE,UAAU,EAAEC,wBAAwB,CAACL,SAAS,CAAC,EAAEM,aAAa,CAACN,SAAS,CAACE,KAAK,CAAC,IAAII,aAAa,CAACN,SAAS,CAACG,UAAU,CAAC,CAAC;EACpK;EACA,OAAOE,wBAAwB,CAACL,SAAS,CAAC;AAC9C;AACA,SAASM,aAAaA,CAACJ,KAAK,EAAE;EAC1B,IAAIK,KAAK,CAACC,OAAO,CAACN,KAAK,CAAC,EAAE;IACtB,OAAOA,KAAK,CAACO,IAAI,CAAC,IAAI,CAAC;EAC3B;EACA,OAAOP,KAAK;AAChB;AACA,SAASG,wBAAwBA,CAACL,SAAS,EAAE;EACzC;EACA,IAAIA,SAAS,CAACU,IAAI,KAAK,0BAA0B,EAAE;IAC/C,OAAO,GAAGV,SAAS,CAACW,OAAO,wFAAwF;EACvH;EACA;EACA,IAAI,OAAOX,SAAS,CAACU,IAAI,KAAK,QAAQ,IAAI,OAAOV,SAAS,CAACY,KAAK,KAAK,QAAQ,IAAI,OAAOZ,SAAS,CAACa,OAAO,KAAK,QAAQ,EAAE;IACpH,OAAOf,GAAG,CAACM,QAAQ,CAAC,sBAAsB,EAAE,+BAA+B,EAAEJ,SAAS,CAACW,OAAO,CAAC;EACnG;EACA,OAAOX,SAAS,CAACW,OAAO,IAAIb,GAAG,CAACM,QAAQ,CAAC,sBAAsB,EAAE,qEAAqE,CAAC;AAC3I;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASU,cAAcA,CAACC,KAAK,GAAG,IAAI,EAAEd,OAAO,GAAG,KAAK,EAAE;EAC1D,IAAI,CAACc,KAAK,EAAE;IACR,OAAOjB,GAAG,CAACM,QAAQ,CAAC,sBAAsB,EAAE,qEAAqE,CAAC;EACtH;EACA,IAAIG,KAAK,CAACC,OAAO,CAACO,KAAK,CAAC,EAAE;IACtB,MAAMC,MAAM,GAAGpB,MAAM,CAACqB,QAAQ,CAACF,KAAK,CAAC;IACrC,MAAMG,GAAG,GAAGJ,cAAc,CAACE,MAAM,CAAC,CAAC,CAAC,EAAEf,OAAO,CAAC;IAC9C,IAAIe,MAAM,CAACG,MAAM,GAAG,CAAC,EAAE;MACnB,OAAOrB,GAAG,CAACM,QAAQ,CAAC,kBAAkB,EAAE,2BAA2B,EAAEc,GAAG,EAAEF,MAAM,CAACG,MAAM,CAAC;IAC5F;IACA,OAAOD,GAAG;EACd;EACA,IAAIrB,KAAK,CAACuB,QAAQ,CAACL,KAAK,CAAC,EAAE;IACvB,OAAOA,KAAK;EAChB;EACA,IAAIA,KAAK,CAACM,MAAM,EAAE;IACd,MAAMA,MAAM,GAAGN,KAAK,CAACM,MAAM;IAC3B,IAAIA,MAAM,CAACN,KAAK,EAAE;MACd,OAAOhB,uBAAuB,CAACsB,MAAM,CAACN,KAAK,EAAEd,OAAO,CAAC;IACzD;IACA,IAAIoB,MAAM,CAACrB,SAAS,EAAE;MAClB,OAAOD,uBAAuB,CAACsB,MAAM,CAACrB,SAAS,EAAEC,OAAO,CAAC;IAC7D;EACJ;EACA,IAAIc,KAAK,CAACb,KAAK,EAAE;IACb,OAAOH,uBAAuB,CAACgB,KAAK,EAAEd,OAAO,CAAC;EAClD;EACA,IAAIc,KAAK,CAACJ,OAAO,EAAE;IACf,OAAOI,KAAK,CAACJ,OAAO;EACxB;EACA,OAAOb,GAAG,CAACM,QAAQ,CAAC,sBAAsB,EAAE,qEAAqE,CAAC;AACtH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}