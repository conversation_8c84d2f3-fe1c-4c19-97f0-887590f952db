import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { Router } from '@angular/router';
import {
  of,
  map,
  catchError,
  Subject,
  takeUntil,
  debounceTime,
  distinctUntilChanged,
  startWith,
} from 'rxjs';
import { PageFooterComponent } from '@shared/components/page-footer/page-footer.component';
import { PaginationService } from '@shared/services/pagination.service';
import { KnowledgeBaseService } from '@shared/services/knowledge-base.service';
import {
  AvaTextboxComponent,
  DropdownComponent,
  DropdownOption,
  IconComponent,
  TextCardComponent,
  PopupComponent,
  DialogService
} from '@ava/play-comp-library';
import { LucideAngularModule } from 'lucide-angular';
import knowledgeBaseLabels from '../knowledge-base/constants/knowledge-base.json';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { ConsoleCardAction, ConsoleCardComponent } from "@shared/components/console-card/console-card.component";
import { TimeAgoPipe } from '@shared/pipes/time-ago.pipe';

@Component({
  selector: 'app-knowledge-base',
  standalone: true,
  imports: [
    CommonModule,
    PageFooterComponent,
    TextCardComponent,
    AvaTextboxComponent,
    DropdownComponent,
    LucideAngularModule,
    IconComponent,
    ReactiveFormsModule,
    PopupComponent,
    ConsoleCardComponent,
    TimeAgoPipe,
  ],
  providers: [DatePipe],
  templateUrl: './knowledge-base.component.html',
  styleUrls: ['./knowledge-base.component.scss'],
})
export class KnowledgeBaseComponent implements OnInit, OnDestroy {
  searchForm!: FormGroup;
  search: any;
  public kbLabels = knowledgeBaseLabels.labels;
  allKnowledgeBase: any[] = [];
  filteredKnowledgeBase: any[] = [];
  displayedKnowledgeBase: any[] = [];
  isLoading: boolean = false;
  error: string | null = null;
  currentPage: number = 1;
  itemsPerPage: number = 11;
  totalPages: number = 1;
  showInfoPopup: boolean = false;
  iconName = '';
  infoMessage = '';
  private destroy$ = new Subject<void>();

  knowledgeBaseOptions: DropdownOption[] = [
    { name: 'All', value: 'all' },
    { name: 'Type A', value: 'typeA' },
    { name: 'Type B', value: 'typeB' },
  ];
  selectedData: any = null;

  defaultActions: ConsoleCardAction[] = [
    {
      id: 'delete',
      icon: 'trash',
      label: 'Delete item',
      tooltip: 'Delete',
    },
    {
      id: 'edit',
      icon: 'edit',
      label: 'Edit item',
      tooltip: 'Edit',
    },
  ];

  showDeletePopup = false;
  knowledgeBaseToDelete: any = null;
  cardSkeletonPlaceholders = Array(11);

  constructor(
    private knowledgeBaseService: KnowledgeBaseService,
    private paginationService: PaginationService,
    private router: Router,
    private datePipe: DatePipe,
    private fb: FormBuilder,
    private dialogService: DialogService,
  ) {
    this.searchForm = this.fb.group({
      search: [''],
    });
  }

  ngOnInit(): void {
    this.searchForm
      .get('search')!
      .valueChanges.pipe(
        startWith(''),
        debounceTime(300),
        distinctUntilChanged(),
        map((value) => value?.toLowerCase() ?? ''),
      )
      .subscribe((searchText) => {
        this.filterKnowledgeBase(searchText);
      });

    this.fetchAllKnowledge();
  }

  fetchAllKnowledge() {
    this.isLoading = true;
    this.knowledgeBaseService
      .fetchAllKnowledge()
      .pipe(
        map((response: any[]) => {
          return response.map((item: any) => {
            const formattedDate =
              this.datePipe.transform(item.createdDate, 'MM/dd/yyyy') || '';
            return {
              ...item,
              title: item.collectionName,
              createdDate: formattedDate,
              userCount: item.userCount || 0,
            };
          });
        }),
        catchError((error) => {
          this.isLoading = false;
          this.handleFetchError();
          return of([]);
        }),
        takeUntil(this.destroy$),
      )
      .subscribe({
        next: (res) => {
          this.allKnowledgeBase = res;
          this.filteredKnowledgeBase = [...this.allKnowledgeBase];
          this.updateDisplayedKnowledgeBase();
          this.isLoading = false;
        },
        error: (err) => {
          this.isLoading = false;
        },
      });
  }

  private handleFetchError(): void {
    this.dialogService.error({
      title: 'Loading Failed',
      message: 'Failed to load knowledge bases. Please try again.',
      showRetryButton: true,
      retryButtonText: 'Retry'
    }).then(result => {
      if (result.action === 'retry') {
        this.fetchAllKnowledge();
      }
    });
  }

  updateDisplayedKnowledgeBase(): void {
    this.itemsPerPage = this.currentPage === 1 ? 12 : 11;
    const paginationResult = this.paginationService.getPaginatedItems(
      this.filteredKnowledgeBase,
      this.currentPage,
      this.itemsPerPage,
    );
    this.displayedKnowledgeBase = paginationResult.displayedItems;
    this.totalPages = paginationResult.totalPages;
  }

  filterKnowledgeBase(searchText: string): void {
    this.filteredKnowledgeBase = this.allKnowledgeBase.filter((know) => {
      const inTitle = know.title?.toLowerCase().includes(searchText);
      const inDescription = know.description
        ?.toLowerCase()
        .includes(searchText);
      const inTags =
        Array.isArray(know.tags) &&
        know.tags?.some((tag: any) =>
          tag.label?.toLowerCase().includes(searchText),
        );

      return inTitle || inDescription || inTags;
    });

    this.updateDisplayedKnowledgeBase();
  }

  onCreateKnowledgeBase(): void {
    this.router.navigate(['/libraries/knowledge-base/create']);
  }

  onCardClicked(knowledgeBaseId: string): void {
    // Navigate to knowledge base details page
    this.router.navigate([`/libraries/knowledge-base/edit/${knowledgeBaseId}`]);
  }

  onActionClick(
    event: { actionId: string; action: ConsoleCardAction },
    knowledgeBaseId: string,
  ): void {
    switch (event.actionId) {
      case 'delete':
        this.confirmDeleteKnowledgeBase(knowledgeBaseId);
        break;
      case 'edit':
        this.editKnowledgeBase(knowledgeBaseId);
        break;
      default:
        break;
    }
  }

  editKnowledgeBase(knowledgeBaseId: string): void {
    // Implement duplicate logic
    this.router.navigate([`/libraries/knowledge-base/edit/${knowledgeBaseId}`]);
  }

  deleteKnowledgeBase(knowledgeBaseId: string): void {
    const collectionObj = this.allKnowledgeBase.find(
      (item) => item.id === knowledgeBaseId,
    );
    const collectionName = collectionObj?.title;

    if (collectionName) {
      // Show loading dialog
      this.dialogService.loading({
        title: 'Deleting Knowledge Base...',
        message: 'Please wait while we delete the knowledge base.',
        showProgress: false,
        showCancelButton: false
      });

      this.knowledgeBaseService.deleteByCollection(collectionName).subscribe({
        next: (res) => {
          this.dialogService.close(); // Close loading dialog
          console.log('Knowledge base deleted:', res);
          
          this.dialogService.success({
            title: 'Success!',
            message: 'Knowledge Base deleted successfully.'
          }).then(() => {
            this.fetchAllKnowledge(); // Refresh the list
          });
        },
        error: (err) => {
          this.dialogService.close(); // Close loading dialog
          console.error('Failed to delete knowledge base:', err);
          
          this.dialogService.error({
            title: 'Deletion Failed',
            message: err?.error?.message || err.message || 'Failed to delete knowledge base. Please try again.',
            showRetryButton: true,
            retryButtonText: 'Retry'
          }).then(result => {
            if (result.action === 'retry') {
              this.deleteKnowledgeBase(knowledgeBaseId);
            }
          });
        },
      });
    } else {
      this.dialogService.error({
        title: 'Error',
        message: `Knowledge base with ID ${knowledgeBaseId} not found.`
      });
      console.warn(`Knowledge base with ID ${knowledgeBaseId} not found.`);
    }
  }

  confirmDeleteKnowledgeBase(knowledgeBaseId: string): void {
    const knowledgeBaseToDelete = this.allKnowledgeBase.find(
      (item) => item.id === knowledgeBaseId,
    );

    if (!knowledgeBaseToDelete) {
      this.dialogService.error({
        title: 'Error',
        message: 'Knowledge base not found.'
      });
      return;
    }

    this.dialogService.confirmation({
      title: 'Delete Knowledge Base?',
      message: `Are you sure you want to delete "${knowledgeBaseToDelete.title}"? This action cannot be undone.`,
      confirmButtonText: 'Delete',
      cancelButtonText: 'Cancel',
      confirmButtonVariant: 'danger',
      icon:'trash'
    }).then(result => {
      if (result.action === 'proceed') {
        this.deleteKnowledgeBase(knowledgeBaseId);
      }
    });
  }

  onConfirmDelete(): void {
    const collectionName = this.knowledgeBaseToDelete?.title;
    if (collectionName) {
      this.knowledgeBaseService.deleteByCollection(collectionName).subscribe({
        next: (res) => {
          console.log('Knowledge base deleted:', res);
          // Show success popup
          this.iconName = 'check-circle';
          this.infoMessage = 'Knowledge Base deleted successfully.';
          this.showInfoPopup = true;
          this.fetchAllKnowledge();
          this.closeDeletePopup();
        },
        error: (err) => {
          console.error('Failed to delete knowledge base:', err);
          this.closeDeletePopup();
        },
      });
    }
  }

  closeDeletePopup(): void {
    this.showDeletePopup = false;
    this.knowledgeBaseToDelete = null;
  }

  duplicateKnowledgeBase(knowledgeBaseId: string): void {
    // Implement duplicate logic
  }

  onSelectionChange(data: any) {
    this.selectedData = data;
    // Implement filter logic if needed
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    this.updateDisplayedKnowledgeBase();
  }

  get showCreateCard(): boolean {
    return !this.isLoading && !this.error;
  }

  handleInfoPopupClose(): void {
    this.showInfoPopup = false;
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
