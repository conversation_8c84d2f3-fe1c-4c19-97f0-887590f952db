{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as dom from './dom.js';\nimport { DisposableStore, toDisposable } from '../common/lifecycle.js';\nexport class GlobalPointerMoveMonitor {\n  constructor() {\n    this._hooks = new DisposableStore();\n    this._pointerMoveCallback = null;\n    this._onStopCallback = null;\n  }\n  dispose() {\n    this.stopMonitoring(false);\n    this._hooks.dispose();\n  }\n  stopMonitoring(invokeStopCallback, browserEvent) {\n    if (!this.isMonitoring()) {\n      // Not monitoring\n      return;\n    }\n    // Unhook\n    this._hooks.clear();\n    this._pointerMoveCallback = null;\n    const onStopCallback = this._onStopCallback;\n    this._onStopCallback = null;\n    if (invokeStopCallback && onStopCallback) {\n      onStopCallback(browserEvent);\n    }\n  }\n  isMonitoring() {\n    return !!this._pointerMoveCallback;\n  }\n  startMonitoring(initialElement, pointerId, initialButtons, pointerMoveCallback, onStopCallback) {\n    if (this.isMonitoring()) {\n      this.stopMonitoring(false);\n    }\n    this._pointerMoveCallback = pointerMoveCallback;\n    this._onStopCallback = onStopCallback;\n    let eventSource = initialElement;\n    try {\n      initialElement.setPointerCapture(pointerId);\n      this._hooks.add(toDisposable(() => {\n        try {\n          initialElement.releasePointerCapture(pointerId);\n        } catch (err) {\n          // See https://github.com/microsoft/vscode/issues/161731\n          //\n          // `releasePointerCapture` sometimes fails when being invoked with the exception:\n          //     DOMException: Failed to execute 'releasePointerCapture' on 'Element':\n          //     No active pointer with the given id is found.\n          //\n          // There's no need to do anything in case of failure\n        }\n      }));\n    } catch (err) {\n      // See https://github.com/microsoft/vscode/issues/144584\n      // See https://github.com/microsoft/vscode/issues/146947\n      // `setPointerCapture` sometimes fails when being invoked\n      // from a `mousedown` listener on macOS and Windows\n      // and it always fails on Linux with the exception:\n      //     DOMException: Failed to execute 'setPointerCapture' on 'Element':\n      //     No active pointer with the given id is found.\n      // In case of failure, we bind the listeners on the window\n      eventSource = dom.getWindow(initialElement);\n    }\n    this._hooks.add(dom.addDisposableListener(eventSource, dom.EventType.POINTER_MOVE, e => {\n      if (e.buttons !== initialButtons) {\n        // Buttons state has changed in the meantime\n        this.stopMonitoring(true);\n        return;\n      }\n      e.preventDefault();\n      this._pointerMoveCallback(e);\n    }));\n    this._hooks.add(dom.addDisposableListener(eventSource, dom.EventType.POINTER_UP, e => this.stopMonitoring(true)));\n  }\n}", "map": {"version": 3, "names": ["dom", "DisposableStore", "toDisposable", "GlobalPointerMoveMonitor", "constructor", "_hooks", "_pointerMoveCallback", "_onStopCallback", "dispose", "stopMonitoring", "invokeStopCallback", "browserEvent", "isMonitoring", "clear", "onStopCallback", "startMonitoring", "initialElement", "pointerId", "initialButtons", "pointer<PERSON><PERSON><PERSON><PERSON><PERSON>", "eventSource", "setPointerCapture", "add", "releasePointerCapture", "err", "getWindow", "addDisposableListener", "EventType", "POINTER_MOVE", "e", "buttons", "preventDefault", "POINTER_UP"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/browser/globalPointerMoveMonitor.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as dom from './dom.js';\nimport { DisposableStore, toDisposable } from '../common/lifecycle.js';\nexport class GlobalPointerMoveMonitor {\n    constructor() {\n        this._hooks = new DisposableStore();\n        this._pointerMoveCallback = null;\n        this._onStopCallback = null;\n    }\n    dispose() {\n        this.stopMonitoring(false);\n        this._hooks.dispose();\n    }\n    stopMonitoring(invokeStopCallback, browserEvent) {\n        if (!this.isMonitoring()) {\n            // Not monitoring\n            return;\n        }\n        // Unhook\n        this._hooks.clear();\n        this._pointerMoveCallback = null;\n        const onStopCallback = this._onStopCallback;\n        this._onStopCallback = null;\n        if (invokeStopCallback && onStopCallback) {\n            onStopCallback(browserEvent);\n        }\n    }\n    isMonitoring() {\n        return !!this._pointerMoveCallback;\n    }\n    startMonitoring(initialElement, pointerId, initialButtons, pointerMoveCallback, onStopCallback) {\n        if (this.isMonitoring()) {\n            this.stopMonitoring(false);\n        }\n        this._pointerMoveCallback = pointerMoveCallback;\n        this._onStopCallback = onStopCallback;\n        let eventSource = initialElement;\n        try {\n            initialElement.setPointerCapture(pointerId);\n            this._hooks.add(toDisposable(() => {\n                try {\n                    initialElement.releasePointerCapture(pointerId);\n                }\n                catch (err) {\n                    // See https://github.com/microsoft/vscode/issues/161731\n                    //\n                    // `releasePointerCapture` sometimes fails when being invoked with the exception:\n                    //     DOMException: Failed to execute 'releasePointerCapture' on 'Element':\n                    //     No active pointer with the given id is found.\n                    //\n                    // There's no need to do anything in case of failure\n                }\n            }));\n        }\n        catch (err) {\n            // See https://github.com/microsoft/vscode/issues/144584\n            // See https://github.com/microsoft/vscode/issues/146947\n            // `setPointerCapture` sometimes fails when being invoked\n            // from a `mousedown` listener on macOS and Windows\n            // and it always fails on Linux with the exception:\n            //     DOMException: Failed to execute 'setPointerCapture' on 'Element':\n            //     No active pointer with the given id is found.\n            // In case of failure, we bind the listeners on the window\n            eventSource = dom.getWindow(initialElement);\n        }\n        this._hooks.add(dom.addDisposableListener(eventSource, dom.EventType.POINTER_MOVE, (e) => {\n            if (e.buttons !== initialButtons) {\n                // Buttons state has changed in the meantime\n                this.stopMonitoring(true);\n                return;\n            }\n            e.preventDefault();\n            this._pointerMoveCallback(e);\n        }));\n        this._hooks.add(dom.addDisposableListener(eventSource, dom.EventType.POINTER_UP, (e) => this.stopMonitoring(true)));\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,KAAKA,GAAG,MAAM,UAAU;AAC/B,SAASC,eAAe,EAAEC,YAAY,QAAQ,wBAAwB;AACtE,OAAO,MAAMC,wBAAwB,CAAC;EAClCC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,MAAM,GAAG,IAAIJ,eAAe,CAAC,CAAC;IACnC,IAAI,CAACK,oBAAoB,GAAG,IAAI;IAChC,IAAI,CAACC,eAAe,GAAG,IAAI;EAC/B;EACAC,OAAOA,CAAA,EAAG;IACN,IAAI,CAACC,cAAc,CAAC,KAAK,CAAC;IAC1B,IAAI,CAACJ,MAAM,CAACG,OAAO,CAAC,CAAC;EACzB;EACAC,cAAcA,CAACC,kBAAkB,EAAEC,YAAY,EAAE;IAC7C,IAAI,CAAC,IAAI,CAACC,YAAY,CAAC,CAAC,EAAE;MACtB;MACA;IACJ;IACA;IACA,IAAI,CAACP,MAAM,CAACQ,KAAK,CAAC,CAAC;IACnB,IAAI,CAACP,oBAAoB,GAAG,IAAI;IAChC,MAAMQ,cAAc,GAAG,IAAI,CAACP,eAAe;IAC3C,IAAI,CAACA,eAAe,GAAG,IAAI;IAC3B,IAAIG,kBAAkB,IAAII,cAAc,EAAE;MACtCA,cAAc,CAACH,YAAY,CAAC;IAChC;EACJ;EACAC,YAAYA,CAAA,EAAG;IACX,OAAO,CAAC,CAAC,IAAI,CAACN,oBAAoB;EACtC;EACAS,eAAeA,CAACC,cAAc,EAAEC,SAAS,EAAEC,cAAc,EAAEC,mBAAmB,EAAEL,cAAc,EAAE;IAC5F,IAAI,IAAI,CAACF,YAAY,CAAC,CAAC,EAAE;MACrB,IAAI,CAACH,cAAc,CAAC,KAAK,CAAC;IAC9B;IACA,IAAI,CAACH,oBAAoB,GAAGa,mBAAmB;IAC/C,IAAI,CAACZ,eAAe,GAAGO,cAAc;IACrC,IAAIM,WAAW,GAAGJ,cAAc;IAChC,IAAI;MACAA,cAAc,CAACK,iBAAiB,CAACJ,SAAS,CAAC;MAC3C,IAAI,CAACZ,MAAM,CAACiB,GAAG,CAACpB,YAAY,CAAC,MAAM;QAC/B,IAAI;UACAc,cAAc,CAACO,qBAAqB,CAACN,SAAS,CAAC;QACnD,CAAC,CACD,OAAOO,GAAG,EAAE;UACR;UACA;UACA;UACA;UACA;UACA;UACA;QAAA;MAER,CAAC,CAAC,CAAC;IACP,CAAC,CACD,OAAOA,GAAG,EAAE;MACR;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAJ,WAAW,GAAGpB,GAAG,CAACyB,SAAS,CAACT,cAAc,CAAC;IAC/C;IACA,IAAI,CAACX,MAAM,CAACiB,GAAG,CAACtB,GAAG,CAAC0B,qBAAqB,CAACN,WAAW,EAAEpB,GAAG,CAAC2B,SAAS,CAACC,YAAY,EAAGC,CAAC,IAAK;MACtF,IAAIA,CAAC,CAACC,OAAO,KAAKZ,cAAc,EAAE;QAC9B;QACA,IAAI,CAACT,cAAc,CAAC,IAAI,CAAC;QACzB;MACJ;MACAoB,CAAC,CAACE,cAAc,CAAC,CAAC;MAClB,IAAI,CAACzB,oBAAoB,CAACuB,CAAC,CAAC;IAChC,CAAC,CAAC,CAAC;IACH,IAAI,CAACxB,MAAM,CAACiB,GAAG,CAACtB,GAAG,CAAC0B,qBAAqB,CAACN,WAAW,EAAEpB,GAAG,CAAC2B,SAAS,CAACK,UAAU,EAAGH,CAAC,IAAK,IAAI,CAACpB,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;EACvH;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}