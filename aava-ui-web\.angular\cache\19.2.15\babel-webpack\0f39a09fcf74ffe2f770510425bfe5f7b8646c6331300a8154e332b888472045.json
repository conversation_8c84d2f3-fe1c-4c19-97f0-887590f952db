{"ast": null, "code": "import { ContiguousMultilineTokens } from './contiguousMultilineTokens.js';\nexport class ContiguousMultilineTokensBuilder {\n  constructor() {\n    this._tokens = [];\n  }\n  add(lineNumber, lineTokens) {\n    if (this._tokens.length > 0) {\n      const last = this._tokens[this._tokens.length - 1];\n      if (last.endLineNumber + 1 === lineNumber) {\n        // append\n        last.appendLineTokens(lineTokens);\n        return;\n      }\n    }\n    this._tokens.push(new ContiguousMultilineTokens(lineNumber, [lineTokens]));\n  }\n  finalize() {\n    return this._tokens;\n  }\n}", "map": {"version": 3, "names": ["ContiguousMultilineTokens", "ContiguousMultilineTokensBuilder", "constructor", "_tokens", "add", "lineNumber", "lineTokens", "length", "last", "endLineNumber", "appendLineTokens", "push", "finalize"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/tokens/contiguousMultilineTokensBuilder.js"], "sourcesContent": ["import { ContiguousMultilineTokens } from './contiguousMultilineTokens.js';\nexport class ContiguousMultilineTokensBuilder {\n    constructor() {\n        this._tokens = [];\n    }\n    add(lineNumber, lineTokens) {\n        if (this._tokens.length > 0) {\n            const last = this._tokens[this._tokens.length - 1];\n            if (last.endLineNumber + 1 === lineNumber) {\n                // append\n                last.appendLineTokens(lineTokens);\n                return;\n            }\n        }\n        this._tokens.push(new ContiguousMultilineTokens(lineNumber, [lineTokens]));\n    }\n    finalize() {\n        return this._tokens;\n    }\n}\n"], "mappings": "AAAA,SAASA,yBAAyB,QAAQ,gCAAgC;AAC1E,OAAO,MAAMC,gCAAgC,CAAC;EAC1CC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,OAAO,GAAG,EAAE;EACrB;EACAC,GAAGA,CAACC,UAAU,EAAEC,UAAU,EAAE;IACxB,IAAI,IAAI,CAACH,OAAO,CAACI,MAAM,GAAG,CAAC,EAAE;MACzB,MAAMC,IAAI,GAAG,IAAI,CAACL,OAAO,CAAC,IAAI,CAACA,OAAO,CAACI,MAAM,GAAG,CAAC,CAAC;MAClD,IAAIC,IAAI,CAACC,aAAa,GAAG,CAAC,KAAKJ,UAAU,EAAE;QACvC;QACAG,IAAI,CAACE,gBAAgB,CAACJ,UAAU,CAAC;QACjC;MACJ;IACJ;IACA,IAAI,CAACH,OAAO,CAACQ,IAAI,CAAC,IAAIX,yBAAyB,CAACK,UAAU,EAAE,CAACC,UAAU,CAAC,CAAC,CAAC;EAC9E;EACAM,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACT,OAAO;EACvB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}