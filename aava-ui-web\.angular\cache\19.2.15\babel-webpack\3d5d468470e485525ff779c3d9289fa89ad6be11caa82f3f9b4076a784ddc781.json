{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { forEachAdjacent } from '../../../../../base/common/arrays.js';\nimport { BugIndicatingError } from '../../../../../base/common/errors.js';\nimport { OffsetRange } from '../../../core/offsetRange.js';\nexport class DiffAlgorithmResult {\n  static trivial(seq1, seq2) {\n    return new DiffAlgorithmResult([new SequenceDiff(OffsetRange.ofLength(seq1.length), OffsetRange.ofLength(seq2.length))], false);\n  }\n  static trivialTimedOut(seq1, seq2) {\n    return new DiffAlgorithmResult([new SequenceDiff(OffsetRange.ofLength(seq1.length), OffsetRange.ofLength(seq2.length))], true);\n  }\n  constructor(diffs,\n  /**\n   * Indicates if the time out was reached.\n   * In that case, the diffs might be an approximation and the user should be asked to rerun the diff with more time.\n   */\n  hitTimeout) {\n    this.diffs = diffs;\n    this.hitTimeout = hitTimeout;\n  }\n}\nexport class SequenceDiff {\n  static invert(sequenceDiffs, doc1Length) {\n    const result = [];\n    forEachAdjacent(sequenceDiffs, (a, b) => {\n      result.push(SequenceDiff.fromOffsetPairs(a ? a.getEndExclusives() : OffsetPair.zero, b ? b.getStarts() : new OffsetPair(doc1Length, (a ? a.seq2Range.endExclusive - a.seq1Range.endExclusive : 0) + doc1Length)));\n    });\n    return result;\n  }\n  static fromOffsetPairs(start, endExclusive) {\n    return new SequenceDiff(new OffsetRange(start.offset1, endExclusive.offset1), new OffsetRange(start.offset2, endExclusive.offset2));\n  }\n  static assertSorted(sequenceDiffs) {\n    let last = undefined;\n    for (const cur of sequenceDiffs) {\n      if (last) {\n        if (!(last.seq1Range.endExclusive <= cur.seq1Range.start && last.seq2Range.endExclusive <= cur.seq2Range.start)) {\n          throw new BugIndicatingError('Sequence diffs must be sorted');\n        }\n      }\n      last = cur;\n    }\n  }\n  constructor(seq1Range, seq2Range) {\n    this.seq1Range = seq1Range;\n    this.seq2Range = seq2Range;\n  }\n  swap() {\n    return new SequenceDiff(this.seq2Range, this.seq1Range);\n  }\n  toString() {\n    return `${this.seq1Range} <-> ${this.seq2Range}`;\n  }\n  join(other) {\n    return new SequenceDiff(this.seq1Range.join(other.seq1Range), this.seq2Range.join(other.seq2Range));\n  }\n  delta(offset) {\n    if (offset === 0) {\n      return this;\n    }\n    return new SequenceDiff(this.seq1Range.delta(offset), this.seq2Range.delta(offset));\n  }\n  deltaStart(offset) {\n    if (offset === 0) {\n      return this;\n    }\n    return new SequenceDiff(this.seq1Range.deltaStart(offset), this.seq2Range.deltaStart(offset));\n  }\n  deltaEnd(offset) {\n    if (offset === 0) {\n      return this;\n    }\n    return new SequenceDiff(this.seq1Range.deltaEnd(offset), this.seq2Range.deltaEnd(offset));\n  }\n  intersect(other) {\n    const i1 = this.seq1Range.intersect(other.seq1Range);\n    const i2 = this.seq2Range.intersect(other.seq2Range);\n    if (!i1 || !i2) {\n      return undefined;\n    }\n    return new SequenceDiff(i1, i2);\n  }\n  getStarts() {\n    return new OffsetPair(this.seq1Range.start, this.seq2Range.start);\n  }\n  getEndExclusives() {\n    return new OffsetPair(this.seq1Range.endExclusive, this.seq2Range.endExclusive);\n  }\n}\nexport class OffsetPair {\n  static {\n    this.zero = new OffsetPair(0, 0);\n  }\n  static {\n    this.max = new OffsetPair(Number.MAX_SAFE_INTEGER, Number.MAX_SAFE_INTEGER);\n  }\n  constructor(offset1, offset2) {\n    this.offset1 = offset1;\n    this.offset2 = offset2;\n  }\n  toString() {\n    return `${this.offset1} <-> ${this.offset2}`;\n  }\n  delta(offset) {\n    if (offset === 0) {\n      return this;\n    }\n    return new OffsetPair(this.offset1 + offset, this.offset2 + offset);\n  }\n  equals(other) {\n    return this.offset1 === other.offset1 && this.offset2 === other.offset2;\n  }\n}\nexport class InfiniteTimeout {\n  static {\n    this.instance = new InfiniteTimeout();\n  }\n  isValid() {\n    return true;\n  }\n}\nexport class DateTimeout {\n  constructor(timeout) {\n    this.timeout = timeout;\n    this.startTime = Date.now();\n    this.valid = true;\n    if (timeout <= 0) {\n      throw new BugIndicatingError('timeout must be positive');\n    }\n  }\n  // Recommendation: Set a log-point `{this.disable()}` in the body\n  isValid() {\n    const valid = Date.now() - this.startTime < this.timeout;\n    if (!valid && this.valid) {\n      this.valid = false; // timeout reached\n      // eslint-disable-next-line no-debugger\n      debugger; // WARNING: Most likely debugging caused the timeout. Call `this.disable()` to continue without timing out.\n    }\n    return this.valid;\n  }\n}", "map": {"version": 3, "names": ["forEachAdjacent", "BugIndicatingError", "OffsetRange", "DiffAlgorithmResult", "trivial", "seq1", "seq2", "SequenceDiff", "of<PERSON>ength", "length", "trivialTimedOut", "constructor", "diffs", "hitTimeout", "invert", "sequenceDiffs", "doc1<PERSON><PERSON>th", "result", "a", "b", "push", "fromOffsetPairs", "getEndExclusives", "OffsetPair", "zero", "getStarts", "seq2Range", "endExclusive", "seq1Range", "start", "offset1", "offset2", "assertSorted", "last", "undefined", "cur", "swap", "toString", "join", "other", "delta", "offset", "deltaStart", "deltaEnd", "intersect", "i1", "i2", "max", "Number", "MAX_SAFE_INTEGER", "equals", "InfiniteTimeout", "instance", "<PERSON><PERSON><PERSON><PERSON>", "DateTimeout", "timeout", "startTime", "Date", "now", "valid"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/diff/defaultLinesDiffComputer/algorithms/diffAlgorithm.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { forEachAdjacent } from '../../../../../base/common/arrays.js';\nimport { BugIndicatingError } from '../../../../../base/common/errors.js';\nimport { OffsetRange } from '../../../core/offsetRange.js';\nexport class DiffAlgorithmResult {\n    static trivial(seq1, seq2) {\n        return new DiffAlgorithmResult([new SequenceDiff(OffsetRange.ofLength(seq1.length), OffsetRange.ofLength(seq2.length))], false);\n    }\n    static trivialTimedOut(seq1, seq2) {\n        return new DiffAlgorithmResult([new SequenceDiff(OffsetRange.ofLength(seq1.length), OffsetRange.ofLength(seq2.length))], true);\n    }\n    constructor(diffs, \n    /**\n     * Indicates if the time out was reached.\n     * In that case, the diffs might be an approximation and the user should be asked to rerun the diff with more time.\n     */\n    hitTimeout) {\n        this.diffs = diffs;\n        this.hitTimeout = hitTimeout;\n    }\n}\nexport class SequenceDiff {\n    static invert(sequenceDiffs, doc1Length) {\n        const result = [];\n        forEachAdjacent(sequenceDiffs, (a, b) => {\n            result.push(SequenceDiff.fromOffsetPairs(a ? a.getEndExclusives() : OffsetPair.zero, b ? b.getStarts() : new OffsetPair(doc1Length, (a ? a.seq2Range.endExclusive - a.seq1Range.endExclusive : 0) + doc1Length)));\n        });\n        return result;\n    }\n    static fromOffsetPairs(start, endExclusive) {\n        return new SequenceDiff(new OffsetRange(start.offset1, endExclusive.offset1), new OffsetRange(start.offset2, endExclusive.offset2));\n    }\n    static assertSorted(sequenceDiffs) {\n        let last = undefined;\n        for (const cur of sequenceDiffs) {\n            if (last) {\n                if (!(last.seq1Range.endExclusive <= cur.seq1Range.start && last.seq2Range.endExclusive <= cur.seq2Range.start)) {\n                    throw new BugIndicatingError('Sequence diffs must be sorted');\n                }\n            }\n            last = cur;\n        }\n    }\n    constructor(seq1Range, seq2Range) {\n        this.seq1Range = seq1Range;\n        this.seq2Range = seq2Range;\n    }\n    swap() {\n        return new SequenceDiff(this.seq2Range, this.seq1Range);\n    }\n    toString() {\n        return `${this.seq1Range} <-> ${this.seq2Range}`;\n    }\n    join(other) {\n        return new SequenceDiff(this.seq1Range.join(other.seq1Range), this.seq2Range.join(other.seq2Range));\n    }\n    delta(offset) {\n        if (offset === 0) {\n            return this;\n        }\n        return new SequenceDiff(this.seq1Range.delta(offset), this.seq2Range.delta(offset));\n    }\n    deltaStart(offset) {\n        if (offset === 0) {\n            return this;\n        }\n        return new SequenceDiff(this.seq1Range.deltaStart(offset), this.seq2Range.deltaStart(offset));\n    }\n    deltaEnd(offset) {\n        if (offset === 0) {\n            return this;\n        }\n        return new SequenceDiff(this.seq1Range.deltaEnd(offset), this.seq2Range.deltaEnd(offset));\n    }\n    intersect(other) {\n        const i1 = this.seq1Range.intersect(other.seq1Range);\n        const i2 = this.seq2Range.intersect(other.seq2Range);\n        if (!i1 || !i2) {\n            return undefined;\n        }\n        return new SequenceDiff(i1, i2);\n    }\n    getStarts() {\n        return new OffsetPair(this.seq1Range.start, this.seq2Range.start);\n    }\n    getEndExclusives() {\n        return new OffsetPair(this.seq1Range.endExclusive, this.seq2Range.endExclusive);\n    }\n}\nexport class OffsetPair {\n    static { this.zero = new OffsetPair(0, 0); }\n    static { this.max = new OffsetPair(Number.MAX_SAFE_INTEGER, Number.MAX_SAFE_INTEGER); }\n    constructor(offset1, offset2) {\n        this.offset1 = offset1;\n        this.offset2 = offset2;\n    }\n    toString() {\n        return `${this.offset1} <-> ${this.offset2}`;\n    }\n    delta(offset) {\n        if (offset === 0) {\n            return this;\n        }\n        return new OffsetPair(this.offset1 + offset, this.offset2 + offset);\n    }\n    equals(other) {\n        return this.offset1 === other.offset1 && this.offset2 === other.offset2;\n    }\n}\nexport class InfiniteTimeout {\n    static { this.instance = new InfiniteTimeout(); }\n    isValid() {\n        return true;\n    }\n}\nexport class DateTimeout {\n    constructor(timeout) {\n        this.timeout = timeout;\n        this.startTime = Date.now();\n        this.valid = true;\n        if (timeout <= 0) {\n            throw new BugIndicatingError('timeout must be positive');\n        }\n    }\n    // Recommendation: Set a log-point `{this.disable()}` in the body\n    isValid() {\n        const valid = Date.now() - this.startTime < this.timeout;\n        if (!valid && this.valid) {\n            this.valid = false; // timeout reached\n            // eslint-disable-next-line no-debugger\n            debugger; // WARNING: Most likely debugging caused the timeout. Call `this.disable()` to continue without timing out.\n        }\n        return this.valid;\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,eAAe,QAAQ,sCAAsC;AACtE,SAASC,kBAAkB,QAAQ,sCAAsC;AACzE,SAASC,WAAW,QAAQ,8BAA8B;AAC1D,OAAO,MAAMC,mBAAmB,CAAC;EAC7B,OAAOC,OAAOA,CAACC,IAAI,EAAEC,IAAI,EAAE;IACvB,OAAO,IAAIH,mBAAmB,CAAC,CAAC,IAAII,YAAY,CAACL,WAAW,CAACM,QAAQ,CAACH,IAAI,CAACI,MAAM,CAAC,EAAEP,WAAW,CAACM,QAAQ,CAACF,IAAI,CAACG,MAAM,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC;EACnI;EACA,OAAOC,eAAeA,CAACL,IAAI,EAAEC,IAAI,EAAE;IAC/B,OAAO,IAAIH,mBAAmB,CAAC,CAAC,IAAII,YAAY,CAACL,WAAW,CAACM,QAAQ,CAACH,IAAI,CAACI,MAAM,CAAC,EAAEP,WAAW,CAACM,QAAQ,CAACF,IAAI,CAACG,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC;EAClI;EACAE,WAAWA,CAACC,KAAK;EACjB;AACJ;AACA;AACA;EACIC,UAAU,EAAE;IACR,IAAI,CAACD,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,UAAU,GAAGA,UAAU;EAChC;AACJ;AACA,OAAO,MAAMN,YAAY,CAAC;EACtB,OAAOO,MAAMA,CAACC,aAAa,EAAEC,UAAU,EAAE;IACrC,MAAMC,MAAM,GAAG,EAAE;IACjBjB,eAAe,CAACe,aAAa,EAAE,CAACG,CAAC,EAAEC,CAAC,KAAK;MACrCF,MAAM,CAACG,IAAI,CAACb,YAAY,CAACc,eAAe,CAACH,CAAC,GAAGA,CAAC,CAACI,gBAAgB,CAAC,CAAC,GAAGC,UAAU,CAACC,IAAI,EAAEL,CAAC,GAAGA,CAAC,CAACM,SAAS,CAAC,CAAC,GAAG,IAAIF,UAAU,CAACP,UAAU,EAAE,CAACE,CAAC,GAAGA,CAAC,CAACQ,SAAS,CAACC,YAAY,GAAGT,CAAC,CAACU,SAAS,CAACD,YAAY,GAAG,CAAC,IAAIX,UAAU,CAAC,CAAC,CAAC;IACrN,CAAC,CAAC;IACF,OAAOC,MAAM;EACjB;EACA,OAAOI,eAAeA,CAACQ,KAAK,EAAEF,YAAY,EAAE;IACxC,OAAO,IAAIpB,YAAY,CAAC,IAAIL,WAAW,CAAC2B,KAAK,CAACC,OAAO,EAAEH,YAAY,CAACG,OAAO,CAAC,EAAE,IAAI5B,WAAW,CAAC2B,KAAK,CAACE,OAAO,EAAEJ,YAAY,CAACI,OAAO,CAAC,CAAC;EACvI;EACA,OAAOC,YAAYA,CAACjB,aAAa,EAAE;IAC/B,IAAIkB,IAAI,GAAGC,SAAS;IACpB,KAAK,MAAMC,GAAG,IAAIpB,aAAa,EAAE;MAC7B,IAAIkB,IAAI,EAAE;QACN,IAAI,EAAEA,IAAI,CAACL,SAAS,CAACD,YAAY,IAAIQ,GAAG,CAACP,SAAS,CAACC,KAAK,IAAII,IAAI,CAACP,SAAS,CAACC,YAAY,IAAIQ,GAAG,CAACT,SAAS,CAACG,KAAK,CAAC,EAAE;UAC7G,MAAM,IAAI5B,kBAAkB,CAAC,+BAA+B,CAAC;QACjE;MACJ;MACAgC,IAAI,GAAGE,GAAG;IACd;EACJ;EACAxB,WAAWA,CAACiB,SAAS,EAAEF,SAAS,EAAE;IAC9B,IAAI,CAACE,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACF,SAAS,GAAGA,SAAS;EAC9B;EACAU,IAAIA,CAAA,EAAG;IACH,OAAO,IAAI7B,YAAY,CAAC,IAAI,CAACmB,SAAS,EAAE,IAAI,CAACE,SAAS,CAAC;EAC3D;EACAS,QAAQA,CAAA,EAAG;IACP,OAAO,GAAG,IAAI,CAACT,SAAS,QAAQ,IAAI,CAACF,SAAS,EAAE;EACpD;EACAY,IAAIA,CAACC,KAAK,EAAE;IACR,OAAO,IAAIhC,YAAY,CAAC,IAAI,CAACqB,SAAS,CAACU,IAAI,CAACC,KAAK,CAACX,SAAS,CAAC,EAAE,IAAI,CAACF,SAAS,CAACY,IAAI,CAACC,KAAK,CAACb,SAAS,CAAC,CAAC;EACvG;EACAc,KAAKA,CAACC,MAAM,EAAE;IACV,IAAIA,MAAM,KAAK,CAAC,EAAE;MACd,OAAO,IAAI;IACf;IACA,OAAO,IAAIlC,YAAY,CAAC,IAAI,CAACqB,SAAS,CAACY,KAAK,CAACC,MAAM,CAAC,EAAE,IAAI,CAACf,SAAS,CAACc,KAAK,CAACC,MAAM,CAAC,CAAC;EACvF;EACAC,UAAUA,CAACD,MAAM,EAAE;IACf,IAAIA,MAAM,KAAK,CAAC,EAAE;MACd,OAAO,IAAI;IACf;IACA,OAAO,IAAIlC,YAAY,CAAC,IAAI,CAACqB,SAAS,CAACc,UAAU,CAACD,MAAM,CAAC,EAAE,IAAI,CAACf,SAAS,CAACgB,UAAU,CAACD,MAAM,CAAC,CAAC;EACjG;EACAE,QAAQA,CAACF,MAAM,EAAE;IACb,IAAIA,MAAM,KAAK,CAAC,EAAE;MACd,OAAO,IAAI;IACf;IACA,OAAO,IAAIlC,YAAY,CAAC,IAAI,CAACqB,SAAS,CAACe,QAAQ,CAACF,MAAM,CAAC,EAAE,IAAI,CAACf,SAAS,CAACiB,QAAQ,CAACF,MAAM,CAAC,CAAC;EAC7F;EACAG,SAASA,CAACL,KAAK,EAAE;IACb,MAAMM,EAAE,GAAG,IAAI,CAACjB,SAAS,CAACgB,SAAS,CAACL,KAAK,CAACX,SAAS,CAAC;IACpD,MAAMkB,EAAE,GAAG,IAAI,CAACpB,SAAS,CAACkB,SAAS,CAACL,KAAK,CAACb,SAAS,CAAC;IACpD,IAAI,CAACmB,EAAE,IAAI,CAACC,EAAE,EAAE;MACZ,OAAOZ,SAAS;IACpB;IACA,OAAO,IAAI3B,YAAY,CAACsC,EAAE,EAAEC,EAAE,CAAC;EACnC;EACArB,SAASA,CAAA,EAAG;IACR,OAAO,IAAIF,UAAU,CAAC,IAAI,CAACK,SAAS,CAACC,KAAK,EAAE,IAAI,CAACH,SAAS,CAACG,KAAK,CAAC;EACrE;EACAP,gBAAgBA,CAAA,EAAG;IACf,OAAO,IAAIC,UAAU,CAAC,IAAI,CAACK,SAAS,CAACD,YAAY,EAAE,IAAI,CAACD,SAAS,CAACC,YAAY,CAAC;EACnF;AACJ;AACA,OAAO,MAAMJ,UAAU,CAAC;EACpB;IAAS,IAAI,CAACC,IAAI,GAAG,IAAID,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC;EAAE;EAC3C;IAAS,IAAI,CAACwB,GAAG,GAAG,IAAIxB,UAAU,CAACyB,MAAM,CAACC,gBAAgB,EAAED,MAAM,CAACC,gBAAgB,CAAC;EAAE;EACtFtC,WAAWA,CAACmB,OAAO,EAAEC,OAAO,EAAE;IAC1B,IAAI,CAACD,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,OAAO,GAAGA,OAAO;EAC1B;EACAM,QAAQA,CAAA,EAAG;IACP,OAAO,GAAG,IAAI,CAACP,OAAO,QAAQ,IAAI,CAACC,OAAO,EAAE;EAChD;EACAS,KAAKA,CAACC,MAAM,EAAE;IACV,IAAIA,MAAM,KAAK,CAAC,EAAE;MACd,OAAO,IAAI;IACf;IACA,OAAO,IAAIlB,UAAU,CAAC,IAAI,CAACO,OAAO,GAAGW,MAAM,EAAE,IAAI,CAACV,OAAO,GAAGU,MAAM,CAAC;EACvE;EACAS,MAAMA,CAACX,KAAK,EAAE;IACV,OAAO,IAAI,CAACT,OAAO,KAAKS,KAAK,CAACT,OAAO,IAAI,IAAI,CAACC,OAAO,KAAKQ,KAAK,CAACR,OAAO;EAC3E;AACJ;AACA,OAAO,MAAMoB,eAAe,CAAC;EACzB;IAAS,IAAI,CAACC,QAAQ,GAAG,IAAID,eAAe,CAAC,CAAC;EAAE;EAChDE,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI;EACf;AACJ;AACA,OAAO,MAAMC,WAAW,CAAC;EACrB3C,WAAWA,CAAC4C,OAAO,EAAE;IACjB,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;IAC3B,IAAI,CAACC,KAAK,GAAG,IAAI;IACjB,IAAIJ,OAAO,IAAI,CAAC,EAAE;MACd,MAAM,IAAItD,kBAAkB,CAAC,0BAA0B,CAAC;IAC5D;EACJ;EACA;EACAoD,OAAOA,CAAA,EAAG;IACN,MAAMM,KAAK,GAAGF,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,CAACF,SAAS,GAAG,IAAI,CAACD,OAAO;IACxD,IAAI,CAACI,KAAK,IAAI,IAAI,CAACA,KAAK,EAAE;MACtB,IAAI,CAACA,KAAK,GAAG,KAAK,CAAC,CAAC;MACpB;MACA,SAAS,CAAC;IACd;IACA,OAAO,IAAI,CAACA,KAAK;EACrB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}