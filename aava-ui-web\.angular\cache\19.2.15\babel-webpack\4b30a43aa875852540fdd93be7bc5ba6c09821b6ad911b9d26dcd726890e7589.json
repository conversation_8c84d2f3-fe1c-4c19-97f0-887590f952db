{"ast": null, "code": "import _asyncToGenerator from \"C:/console/aava-ui-web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { coalesce, equals, isNonEmptyArray } from '../../../../base/common/arrays.js';\nimport { CancellationToken } from '../../../../base/common/cancellation.js';\nimport { illegalArgument, isCancellationError, onUnexpectedExternalError } from '../../../../base/common/errors.js';\nimport { Disposable, DisposableStore } from '../../../../base/common/lifecycle.js';\nimport { URI } from '../../../../base/common/uri.js';\nimport { IBulkEditService } from '../../../browser/services/bulkEditService.js';\nimport { Range } from '../../../common/core/range.js';\nimport { Selection } from '../../../common/core/selection.js';\nimport { ILanguageFeaturesService } from '../../../common/services/languageFeatures.js';\nimport { IModelService } from '../../../common/services/model.js';\nimport { TextModelCancellationTokenSource } from '../../editorState/browser/editorState.js';\nimport * as nls from '../../../../nls.js';\nimport { CommandsRegistry, ICommandService } from '../../../../platform/commands/common/commands.js';\nimport { INotificationService } from '../../../../platform/notification/common/notification.js';\nimport { Progress } from '../../../../platform/progress/common/progress.js';\nimport { ITelemetryService } from '../../../../platform/telemetry/common/telemetry.js';\nimport { CodeActionItem, CodeActionKind, CodeActionTriggerSource, filtersAction, mayIncludeActionsOfKind } from '../common/types.js';\nimport { HierarchicalKind } from '../../../../base/common/hierarchicalKind.js';\nexport const codeActionCommandId = 'editor.action.codeAction';\nexport const quickFixCommandId = 'editor.action.quickFix';\nexport const autoFixCommandId = 'editor.action.autoFix';\nexport const refactorCommandId = 'editor.action.refactor';\nexport const sourceActionCommandId = 'editor.action.sourceAction';\nexport const organizeImportsCommandId = 'editor.action.organizeImports';\nexport const fixAllCommandId = 'editor.action.fixAll';\nclass ManagedCodeActionSet extends Disposable {\n  static codeActionsPreferredComparator(a, b) {\n    if (a.isPreferred && !b.isPreferred) {\n      return -1;\n    } else if (!a.isPreferred && b.isPreferred) {\n      return 1;\n    } else {\n      return 0;\n    }\n  }\n  static codeActionsComparator({\n    action: a\n  }, {\n    action: b\n  }) {\n    if (a.isAI && !b.isAI) {\n      return 1;\n    } else if (!a.isAI && b.isAI) {\n      return -1;\n    }\n    if (isNonEmptyArray(a.diagnostics)) {\n      return isNonEmptyArray(b.diagnostics) ? ManagedCodeActionSet.codeActionsPreferredComparator(a, b) : -1;\n    } else if (isNonEmptyArray(b.diagnostics)) {\n      return 1;\n    } else {\n      return ManagedCodeActionSet.codeActionsPreferredComparator(a, b); // both have no diagnostics\n    }\n  }\n  constructor(actions, documentation, disposables) {\n    super();\n    this.documentation = documentation;\n    this._register(disposables);\n    this.allActions = [...actions].sort(ManagedCodeActionSet.codeActionsComparator);\n    this.validActions = this.allActions.filter(({\n      action\n    }) => !action.disabled);\n  }\n  get hasAutoFix() {\n    return this.validActions.some(({\n      action: fix\n    }) => !!fix.kind && CodeActionKind.QuickFix.contains(new HierarchicalKind(fix.kind)) && !!fix.isPreferred);\n  }\n  get hasAIFix() {\n    return this.validActions.some(({\n      action: fix\n    }) => !!fix.isAI);\n  }\n  get allAIFixes() {\n    return this.validActions.every(({\n      action: fix\n    }) => !!fix.isAI);\n  }\n}\nconst emptyCodeActionsResponse = {\n  actions: [],\n  documentation: undefined\n};\nexport function getCodeActions(_x, _x2, _x3, _x4, _x5, _x6) {\n  return _getCodeActions.apply(this, arguments);\n}\nfunction _getCodeActions() {\n  _getCodeActions = _asyncToGenerator(function* (registry, model, rangeOrSelection, trigger, progress, token) {\n    const filter = trigger.filter || {};\n    const notebookFilter = {\n      ...filter,\n      excludes: [...(filter.excludes || []), CodeActionKind.Notebook]\n    };\n    const codeActionContext = {\n      only: filter.include?.value,\n      trigger: trigger.type\n    };\n    const cts = new TextModelCancellationTokenSource(model, token);\n    // if the trigger is auto (autosave, lightbulb, etc), we should exclude notebook codeActions\n    const excludeNotebookCodeActions = trigger.type === 2 /* languages.CodeActionTriggerType.Auto */;\n    const providers = getCodeActionProviders(registry, model, excludeNotebookCodeActions ? notebookFilter : filter);\n    const disposables = new DisposableStore();\n    const promises = providers.map(/*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(function* (provider) {\n        try {\n          progress.report(provider);\n          const providedCodeActions = yield provider.provideCodeActions(model, rangeOrSelection, codeActionContext, cts.token);\n          if (providedCodeActions) {\n            disposables.add(providedCodeActions);\n          }\n          if (cts.token.isCancellationRequested) {\n            return emptyCodeActionsResponse;\n          }\n          const filteredActions = (providedCodeActions?.actions || []).filter(action => action && filtersAction(filter, action));\n          const documentation = getDocumentationFromProvider(provider, filteredActions, filter.include);\n          return {\n            actions: filteredActions.map(action => new CodeActionItem(action, provider)),\n            documentation\n          };\n        } catch (err) {\n          if (isCancellationError(err)) {\n            throw err;\n          }\n          onUnexpectedExternalError(err);\n          return emptyCodeActionsResponse;\n        }\n      });\n      return function (_x14) {\n        return _ref2.apply(this, arguments);\n      };\n    }());\n    const listener = registry.onDidChange(() => {\n      const newProviders = registry.all(model);\n      if (!equals(newProviders, providers)) {\n        cts.cancel();\n      }\n    });\n    try {\n      const actions = yield Promise.all(promises);\n      const allActions = actions.map(x => x.actions).flat();\n      const allDocumentation = [...coalesce(actions.map(x => x.documentation)), ...getAdditionalDocumentationForShowingActions(registry, model, trigger, allActions)];\n      return new ManagedCodeActionSet(allActions, allDocumentation, disposables);\n    } finally {\n      listener.dispose();\n      cts.dispose();\n    }\n  });\n  return _getCodeActions.apply(this, arguments);\n}\nfunction getCodeActionProviders(registry, model, filter) {\n  return registry.all(model)\n  // Don't include providers that we know will not return code actions of interest\n  .filter(provider => {\n    if (!provider.providedCodeActionKinds) {\n      // We don't know what type of actions this provider will return.\n      return true;\n    }\n    return provider.providedCodeActionKinds.some(kind => mayIncludeActionsOfKind(filter, new HierarchicalKind(kind)));\n  });\n}\nfunction* getAdditionalDocumentationForShowingActions(registry, model, trigger, actionsToShow) {\n  if (model && actionsToShow.length) {\n    for (const provider of registry.all(model)) {\n      if (provider._getAdditionalMenuItems) {\n        yield* provider._getAdditionalMenuItems?.({\n          trigger: trigger.type,\n          only: trigger.filter?.include?.value\n        }, actionsToShow.map(item => item.action));\n      }\n    }\n  }\n}\nfunction getDocumentationFromProvider(provider, providedCodeActions, only) {\n  if (!provider.documentation) {\n    return undefined;\n  }\n  const documentation = provider.documentation.map(entry => ({\n    kind: new HierarchicalKind(entry.kind),\n    command: entry.command\n  }));\n  if (only) {\n    let currentBest;\n    for (const entry of documentation) {\n      if (entry.kind.contains(only)) {\n        if (!currentBest) {\n          currentBest = entry;\n        } else {\n          // Take best match\n          if (currentBest.kind.contains(entry.kind)) {\n            currentBest = entry;\n          }\n        }\n      }\n    }\n    if (currentBest) {\n      return currentBest?.command;\n    }\n  }\n  // Otherwise, check to see if any of the provided actions match.\n  for (const action of providedCodeActions) {\n    if (!action.kind) {\n      continue;\n    }\n    for (const entry of documentation) {\n      if (entry.kind.contains(new HierarchicalKind(action.kind))) {\n        return entry.command;\n      }\n    }\n  }\n  return undefined;\n}\nexport var ApplyCodeActionReason = /*#__PURE__*/function (ApplyCodeActionReason) {\n  ApplyCodeActionReason[\"OnSave\"] = \"onSave\";\n  ApplyCodeActionReason[\"FromProblemsView\"] = \"fromProblemsView\";\n  ApplyCodeActionReason[\"FromCodeActions\"] = \"fromCodeActions\";\n  ApplyCodeActionReason[\"FromAILightbulb\"] = \"fromAILightbulb\"; // direct invocation when clicking on the AI lightbulb\n  return ApplyCodeActionReason;\n}(ApplyCodeActionReason || {});\nexport function applyCodeAction(_x7, _x8, _x9, _x0) {\n  return _applyCodeAction.apply(this, arguments);\n}\nfunction _applyCodeAction() {\n  _applyCodeAction = _asyncToGenerator(function* (accessor, item, codeActionReason, options, token = CancellationToken.None) {\n    const bulkEditService = accessor.get(IBulkEditService);\n    const commandService = accessor.get(ICommandService);\n    const telemetryService = accessor.get(ITelemetryService);\n    const notificationService = accessor.get(INotificationService);\n    telemetryService.publicLog2('codeAction.applyCodeAction', {\n      codeActionTitle: item.action.title,\n      codeActionKind: item.action.kind,\n      codeActionIsPreferred: !!item.action.isPreferred,\n      reason: codeActionReason\n    });\n    yield item.resolve(token);\n    if (token.isCancellationRequested) {\n      return;\n    }\n    if (item.action.edit?.edits.length) {\n      const result = yield bulkEditService.apply(item.action.edit, {\n        editor: options?.editor,\n        label: item.action.title,\n        quotableLabel: item.action.title,\n        code: 'undoredo.codeAction',\n        respectAutoSaveConfig: codeActionReason !== ApplyCodeActionReason.OnSave,\n        showPreview: options?.preview\n      });\n      if (!result.isApplied) {\n        return;\n      }\n    }\n    if (item.action.command) {\n      try {\n        yield commandService.executeCommand(item.action.command.id, ...(item.action.command.arguments || []));\n      } catch (err) {\n        const message = asMessage(err);\n        notificationService.error(typeof message === 'string' ? message : nls.localize('applyCodeActionFailed', \"An unknown error occurred while applying the code action\"));\n      }\n    }\n  });\n  return _applyCodeAction.apply(this, arguments);\n}\nfunction asMessage(err) {\n  if (typeof err === 'string') {\n    return err;\n  } else if (err instanceof Error && typeof err.message === 'string') {\n    return err.message;\n  } else {\n    return undefined;\n  }\n}\nCommandsRegistry.registerCommand('_executeCodeActionProvider', /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* (accessor, resource, rangeOrSelection, kind, itemResolveCount) {\n    if (!(resource instanceof URI)) {\n      throw illegalArgument();\n    }\n    const {\n      codeActionProvider\n    } = accessor.get(ILanguageFeaturesService);\n    const model = accessor.get(IModelService).getModel(resource);\n    if (!model) {\n      throw illegalArgument();\n    }\n    const validatedRangeOrSelection = Selection.isISelection(rangeOrSelection) ? Selection.liftSelection(rangeOrSelection) : Range.isIRange(rangeOrSelection) ? model.validateRange(rangeOrSelection) : undefined;\n    if (!validatedRangeOrSelection) {\n      throw illegalArgument();\n    }\n    const include = typeof kind === 'string' ? new HierarchicalKind(kind) : undefined;\n    const codeActionSet = yield getCodeActions(codeActionProvider, model, validatedRangeOrSelection, {\n      type: 1 /* languages.CodeActionTriggerType.Invoke */,\n      triggerAction: CodeActionTriggerSource.Default,\n      filter: {\n        includeSourceActions: true,\n        include\n      }\n    }, Progress.None, CancellationToken.None);\n    const resolving = [];\n    const resolveCount = Math.min(codeActionSet.validActions.length, typeof itemResolveCount === 'number' ? itemResolveCount : 0);\n    for (let i = 0; i < resolveCount; i++) {\n      resolving.push(codeActionSet.validActions[i].resolve(CancellationToken.None));\n    }\n    try {\n      yield Promise.all(resolving);\n      return codeActionSet.validActions.map(item => item.action);\n    } finally {\n      setTimeout(() => codeActionSet.dispose(), 100);\n    }\n  });\n  return function (_x1, _x10, _x11, _x12, _x13) {\n    return _ref.apply(this, arguments);\n  };\n}());", "map": {"version": 3, "names": ["coalesce", "equals", "isNonEmptyArray", "CancellationToken", "illegalArgument", "isCancellationError", "onUnexpectedExternalError", "Disposable", "DisposableStore", "URI", "IBulkEditService", "Range", "Selection", "ILanguageFeaturesService", "IModelService", "TextModelCancellationTokenSource", "nls", "CommandsRegistry", "ICommandService", "INotificationService", "Progress", "ITelemetryService", "CodeActionItem", "CodeActionKind", "CodeActionTriggerSource", "filtersAction", "mayIncludeActionsOfKind", "HierarchicalKind", "codeActionCommandId", "quickFixCommandId", "autoFixCommandId", "refactorCommandId", "sourceActionCommandId", "organizeImportsCommandId", "fixAllCommandId", "ManagedCodeActionSet", "codeActionsPreferredComparator", "a", "b", "isPreferred", "codeActionsComparator", "action", "isAI", "diagnostics", "constructor", "actions", "documentation", "disposables", "_register", "allActions", "sort", "validActions", "filter", "disabled", "hasAutoFix", "some", "fix", "kind", "QuickFix", "contains", "hasAIFix", "allAIFixes", "every", "emptyCodeActionsResponse", "undefined", "getCodeActions", "_x", "_x2", "_x3", "_x4", "_x5", "_x6", "_getCodeActions", "apply", "arguments", "_asyncToGenerator", "registry", "model", "rangeOrSelection", "trigger", "progress", "token", "notebookFilter", "excludes", "Notebook", "codeActionContext", "only", "include", "value", "type", "cts", "excludeNotebookCodeActions", "providers", "getCodeActionProviders", "promises", "map", "_ref2", "provider", "report", "providedCodeActions", "provideCodeActions", "add", "isCancellationRequested", "filteredActions", "getDocumentationFromProvider", "err", "_x14", "listener", "onDidChange", "newProviders", "all", "cancel", "Promise", "x", "flat", "allDocumentation", "getAdditionalDocumentationForShowingActions", "dispose", "providedCodeActionKinds", "actionsToShow", "length", "_getAdditionalMenuItems", "item", "entry", "command", "currentBest", "ApplyCodeActionReason", "applyCodeAction", "_x7", "_x8", "_x9", "_x0", "_applyCodeAction", "accessor", "codeActionReason", "options", "None", "bulkEditService", "get", "commandService", "telemetryService", "notificationService", "publicLog2", "codeActionTitle", "title", "codeActionKind", "codeActionIsPreferred", "reason", "resolve", "edit", "edits", "result", "editor", "label", "quotable<PERSON><PERSON>l", "code", "respectAutoSaveConfig", "OnSave", "showPreview", "preview", "isApplied", "executeCommand", "id", "message", "asMessage", "error", "localize", "Error", "registerCommand", "_ref", "resource", "itemResolveCount", "codeActionProvider", "getModel", "validatedRangeOrSelection", "isISelection", "liftSelection", "isIRange", "validate<PERSON><PERSON><PERSON>", "codeActionSet", "triggerAction", "<PERSON><PERSON><PERSON>", "includeSourceActions", "resolving", "resolveCount", "Math", "min", "i", "push", "setTimeout", "_x1", "_x10", "_x11", "_x12", "_x13"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/contrib/codeAction/browser/codeAction.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { coalesce, equals, isNonEmptyArray } from '../../../../base/common/arrays.js';\nimport { CancellationToken } from '../../../../base/common/cancellation.js';\nimport { illegalArgument, isCancellationError, onUnexpectedExternalError } from '../../../../base/common/errors.js';\nimport { Disposable, DisposableStore } from '../../../../base/common/lifecycle.js';\nimport { URI } from '../../../../base/common/uri.js';\nimport { IBulkEditService } from '../../../browser/services/bulkEditService.js';\nimport { Range } from '../../../common/core/range.js';\nimport { Selection } from '../../../common/core/selection.js';\nimport { ILanguageFeaturesService } from '../../../common/services/languageFeatures.js';\nimport { IModelService } from '../../../common/services/model.js';\nimport { TextModelCancellationTokenSource } from '../../editorState/browser/editorState.js';\nimport * as nls from '../../../../nls.js';\nimport { CommandsRegistry, ICommandService } from '../../../../platform/commands/common/commands.js';\nimport { INotificationService } from '../../../../platform/notification/common/notification.js';\nimport { Progress } from '../../../../platform/progress/common/progress.js';\nimport { ITelemetryService } from '../../../../platform/telemetry/common/telemetry.js';\nimport { CodeActionItem, CodeActionKind, CodeActionTriggerSource, filtersAction, mayIncludeActionsOfKind } from '../common/types.js';\nimport { HierarchicalKind } from '../../../../base/common/hierarchicalKind.js';\nexport const codeActionCommandId = 'editor.action.codeAction';\nexport const quickFixCommandId = 'editor.action.quickFix';\nexport const autoFixCommandId = 'editor.action.autoFix';\nexport const refactorCommandId = 'editor.action.refactor';\nexport const sourceActionCommandId = 'editor.action.sourceAction';\nexport const organizeImportsCommandId = 'editor.action.organizeImports';\nexport const fixAllCommandId = 'editor.action.fixAll';\nclass ManagedCodeActionSet extends Disposable {\n    static codeActionsPreferredComparator(a, b) {\n        if (a.isPreferred && !b.isPreferred) {\n            return -1;\n        }\n        else if (!a.isPreferred && b.isPreferred) {\n            return 1;\n        }\n        else {\n            return 0;\n        }\n    }\n    static codeActionsComparator({ action: a }, { action: b }) {\n        if (a.isAI && !b.isAI) {\n            return 1;\n        }\n        else if (!a.isAI && b.isAI) {\n            return -1;\n        }\n        if (isNonEmptyArray(a.diagnostics)) {\n            return isNonEmptyArray(b.diagnostics) ? ManagedCodeActionSet.codeActionsPreferredComparator(a, b) : -1;\n        }\n        else if (isNonEmptyArray(b.diagnostics)) {\n            return 1;\n        }\n        else {\n            return ManagedCodeActionSet.codeActionsPreferredComparator(a, b); // both have no diagnostics\n        }\n    }\n    constructor(actions, documentation, disposables) {\n        super();\n        this.documentation = documentation;\n        this._register(disposables);\n        this.allActions = [...actions].sort(ManagedCodeActionSet.codeActionsComparator);\n        this.validActions = this.allActions.filter(({ action }) => !action.disabled);\n    }\n    get hasAutoFix() {\n        return this.validActions.some(({ action: fix }) => !!fix.kind && CodeActionKind.QuickFix.contains(new HierarchicalKind(fix.kind)) && !!fix.isPreferred);\n    }\n    get hasAIFix() {\n        return this.validActions.some(({ action: fix }) => !!fix.isAI);\n    }\n    get allAIFixes() {\n        return this.validActions.every(({ action: fix }) => !!fix.isAI);\n    }\n}\nconst emptyCodeActionsResponse = { actions: [], documentation: undefined };\nexport async function getCodeActions(registry, model, rangeOrSelection, trigger, progress, token) {\n    const filter = trigger.filter || {};\n    const notebookFilter = {\n        ...filter,\n        excludes: [...(filter.excludes || []), CodeActionKind.Notebook],\n    };\n    const codeActionContext = {\n        only: filter.include?.value,\n        trigger: trigger.type,\n    };\n    const cts = new TextModelCancellationTokenSource(model, token);\n    // if the trigger is auto (autosave, lightbulb, etc), we should exclude notebook codeActions\n    const excludeNotebookCodeActions = (trigger.type === 2 /* languages.CodeActionTriggerType.Auto */);\n    const providers = getCodeActionProviders(registry, model, (excludeNotebookCodeActions) ? notebookFilter : filter);\n    const disposables = new DisposableStore();\n    const promises = providers.map(async (provider) => {\n        try {\n            progress.report(provider);\n            const providedCodeActions = await provider.provideCodeActions(model, rangeOrSelection, codeActionContext, cts.token);\n            if (providedCodeActions) {\n                disposables.add(providedCodeActions);\n            }\n            if (cts.token.isCancellationRequested) {\n                return emptyCodeActionsResponse;\n            }\n            const filteredActions = (providedCodeActions?.actions || []).filter(action => action && filtersAction(filter, action));\n            const documentation = getDocumentationFromProvider(provider, filteredActions, filter.include);\n            return {\n                actions: filteredActions.map(action => new CodeActionItem(action, provider)),\n                documentation\n            };\n        }\n        catch (err) {\n            if (isCancellationError(err)) {\n                throw err;\n            }\n            onUnexpectedExternalError(err);\n            return emptyCodeActionsResponse;\n        }\n    });\n    const listener = registry.onDidChange(() => {\n        const newProviders = registry.all(model);\n        if (!equals(newProviders, providers)) {\n            cts.cancel();\n        }\n    });\n    try {\n        const actions = await Promise.all(promises);\n        const allActions = actions.map(x => x.actions).flat();\n        const allDocumentation = [\n            ...coalesce(actions.map(x => x.documentation)),\n            ...getAdditionalDocumentationForShowingActions(registry, model, trigger, allActions)\n        ];\n        return new ManagedCodeActionSet(allActions, allDocumentation, disposables);\n    }\n    finally {\n        listener.dispose();\n        cts.dispose();\n    }\n}\nfunction getCodeActionProviders(registry, model, filter) {\n    return registry.all(model)\n        // Don't include providers that we know will not return code actions of interest\n        .filter(provider => {\n        if (!provider.providedCodeActionKinds) {\n            // We don't know what type of actions this provider will return.\n            return true;\n        }\n        return provider.providedCodeActionKinds.some(kind => mayIncludeActionsOfKind(filter, new HierarchicalKind(kind)));\n    });\n}\nfunction* getAdditionalDocumentationForShowingActions(registry, model, trigger, actionsToShow) {\n    if (model && actionsToShow.length) {\n        for (const provider of registry.all(model)) {\n            if (provider._getAdditionalMenuItems) {\n                yield* provider._getAdditionalMenuItems?.({ trigger: trigger.type, only: trigger.filter?.include?.value }, actionsToShow.map(item => item.action));\n            }\n        }\n    }\n}\nfunction getDocumentationFromProvider(provider, providedCodeActions, only) {\n    if (!provider.documentation) {\n        return undefined;\n    }\n    const documentation = provider.documentation.map(entry => ({ kind: new HierarchicalKind(entry.kind), command: entry.command }));\n    if (only) {\n        let currentBest;\n        for (const entry of documentation) {\n            if (entry.kind.contains(only)) {\n                if (!currentBest) {\n                    currentBest = entry;\n                }\n                else {\n                    // Take best match\n                    if (currentBest.kind.contains(entry.kind)) {\n                        currentBest = entry;\n                    }\n                }\n            }\n        }\n        if (currentBest) {\n            return currentBest?.command;\n        }\n    }\n    // Otherwise, check to see if any of the provided actions match.\n    for (const action of providedCodeActions) {\n        if (!action.kind) {\n            continue;\n        }\n        for (const entry of documentation) {\n            if (entry.kind.contains(new HierarchicalKind(action.kind))) {\n                return entry.command;\n            }\n        }\n    }\n    return undefined;\n}\nexport var ApplyCodeActionReason;\n(function (ApplyCodeActionReason) {\n    ApplyCodeActionReason[\"OnSave\"] = \"onSave\";\n    ApplyCodeActionReason[\"FromProblemsView\"] = \"fromProblemsView\";\n    ApplyCodeActionReason[\"FromCodeActions\"] = \"fromCodeActions\";\n    ApplyCodeActionReason[\"FromAILightbulb\"] = \"fromAILightbulb\"; // direct invocation when clicking on the AI lightbulb\n})(ApplyCodeActionReason || (ApplyCodeActionReason = {}));\nexport async function applyCodeAction(accessor, item, codeActionReason, options, token = CancellationToken.None) {\n    const bulkEditService = accessor.get(IBulkEditService);\n    const commandService = accessor.get(ICommandService);\n    const telemetryService = accessor.get(ITelemetryService);\n    const notificationService = accessor.get(INotificationService);\n    telemetryService.publicLog2('codeAction.applyCodeAction', {\n        codeActionTitle: item.action.title,\n        codeActionKind: item.action.kind,\n        codeActionIsPreferred: !!item.action.isPreferred,\n        reason: codeActionReason,\n    });\n    await item.resolve(token);\n    if (token.isCancellationRequested) {\n        return;\n    }\n    if (item.action.edit?.edits.length) {\n        const result = await bulkEditService.apply(item.action.edit, {\n            editor: options?.editor,\n            label: item.action.title,\n            quotableLabel: item.action.title,\n            code: 'undoredo.codeAction',\n            respectAutoSaveConfig: codeActionReason !== ApplyCodeActionReason.OnSave,\n            showPreview: options?.preview,\n        });\n        if (!result.isApplied) {\n            return;\n        }\n    }\n    if (item.action.command) {\n        try {\n            await commandService.executeCommand(item.action.command.id, ...(item.action.command.arguments || []));\n        }\n        catch (err) {\n            const message = asMessage(err);\n            notificationService.error(typeof message === 'string'\n                ? message\n                : nls.localize('applyCodeActionFailed', \"An unknown error occurred while applying the code action\"));\n        }\n    }\n}\nfunction asMessage(err) {\n    if (typeof err === 'string') {\n        return err;\n    }\n    else if (err instanceof Error && typeof err.message === 'string') {\n        return err.message;\n    }\n    else {\n        return undefined;\n    }\n}\nCommandsRegistry.registerCommand('_executeCodeActionProvider', async function (accessor, resource, rangeOrSelection, kind, itemResolveCount) {\n    if (!(resource instanceof URI)) {\n        throw illegalArgument();\n    }\n    const { codeActionProvider } = accessor.get(ILanguageFeaturesService);\n    const model = accessor.get(IModelService).getModel(resource);\n    if (!model) {\n        throw illegalArgument();\n    }\n    const validatedRangeOrSelection = Selection.isISelection(rangeOrSelection)\n        ? Selection.liftSelection(rangeOrSelection)\n        : Range.isIRange(rangeOrSelection)\n            ? model.validateRange(rangeOrSelection)\n            : undefined;\n    if (!validatedRangeOrSelection) {\n        throw illegalArgument();\n    }\n    const include = typeof kind === 'string' ? new HierarchicalKind(kind) : undefined;\n    const codeActionSet = await getCodeActions(codeActionProvider, model, validatedRangeOrSelection, { type: 1 /* languages.CodeActionTriggerType.Invoke */, triggerAction: CodeActionTriggerSource.Default, filter: { includeSourceActions: true, include } }, Progress.None, CancellationToken.None);\n    const resolving = [];\n    const resolveCount = Math.min(codeActionSet.validActions.length, typeof itemResolveCount === 'number' ? itemResolveCount : 0);\n    for (let i = 0; i < resolveCount; i++) {\n        resolving.push(codeActionSet.validActions[i].resolve(CancellationToken.None));\n    }\n    try {\n        await Promise.all(resolving);\n        return codeActionSet.validActions.map(item => item.action);\n    }\n    finally {\n        setTimeout(() => codeActionSet.dispose(), 100);\n    }\n});\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA,SAASA,QAAQ,EAAEC,MAAM,EAAEC,eAAe,QAAQ,mCAAmC;AACrF,SAASC,iBAAiB,QAAQ,yCAAyC;AAC3E,SAASC,eAAe,EAAEC,mBAAmB,EAAEC,yBAAyB,QAAQ,mCAAmC;AACnH,SAASC,UAAU,EAAEC,eAAe,QAAQ,sCAAsC;AAClF,SAASC,GAAG,QAAQ,gCAAgC;AACpD,SAASC,gBAAgB,QAAQ,8CAA8C;AAC/E,SAASC,KAAK,QAAQ,+BAA+B;AACrD,SAASC,SAAS,QAAQ,mCAAmC;AAC7D,SAASC,wBAAwB,QAAQ,8CAA8C;AACvF,SAASC,aAAa,QAAQ,mCAAmC;AACjE,SAASC,gCAAgC,QAAQ,0CAA0C;AAC3F,OAAO,KAAKC,GAAG,MAAM,oBAAoB;AACzC,SAASC,gBAAgB,EAAEC,eAAe,QAAQ,kDAAkD;AACpG,SAASC,oBAAoB,QAAQ,0DAA0D;AAC/F,SAASC,QAAQ,QAAQ,kDAAkD;AAC3E,SAASC,iBAAiB,QAAQ,oDAAoD;AACtF,SAASC,cAAc,EAAEC,cAAc,EAAEC,uBAAuB,EAAEC,aAAa,EAAEC,uBAAuB,QAAQ,oBAAoB;AACpI,SAASC,gBAAgB,QAAQ,6CAA6C;AAC9E,OAAO,MAAMC,mBAAmB,GAAG,0BAA0B;AAC7D,OAAO,MAAMC,iBAAiB,GAAG,wBAAwB;AACzD,OAAO,MAAMC,gBAAgB,GAAG,uBAAuB;AACvD,OAAO,MAAMC,iBAAiB,GAAG,wBAAwB;AACzD,OAAO,MAAMC,qBAAqB,GAAG,4BAA4B;AACjE,OAAO,MAAMC,wBAAwB,GAAG,+BAA+B;AACvE,OAAO,MAAMC,eAAe,GAAG,sBAAsB;AACrD,MAAMC,oBAAoB,SAAS5B,UAAU,CAAC;EAC1C,OAAO6B,8BAA8BA,CAACC,CAAC,EAAEC,CAAC,EAAE;IACxC,IAAID,CAAC,CAACE,WAAW,IAAI,CAACD,CAAC,CAACC,WAAW,EAAE;MACjC,OAAO,CAAC,CAAC;IACb,CAAC,MACI,IAAI,CAACF,CAAC,CAACE,WAAW,IAAID,CAAC,CAACC,WAAW,EAAE;MACtC,OAAO,CAAC;IACZ,CAAC,MACI;MACD,OAAO,CAAC;IACZ;EACJ;EACA,OAAOC,qBAAqBA,CAAC;IAAEC,MAAM,EAAEJ;EAAE,CAAC,EAAE;IAAEI,MAAM,EAAEH;EAAE,CAAC,EAAE;IACvD,IAAID,CAAC,CAACK,IAAI,IAAI,CAACJ,CAAC,CAACI,IAAI,EAAE;MACnB,OAAO,CAAC;IACZ,CAAC,MACI,IAAI,CAACL,CAAC,CAACK,IAAI,IAAIJ,CAAC,CAACI,IAAI,EAAE;MACxB,OAAO,CAAC,CAAC;IACb;IACA,IAAIxC,eAAe,CAACmC,CAAC,CAACM,WAAW,CAAC,EAAE;MAChC,OAAOzC,eAAe,CAACoC,CAAC,CAACK,WAAW,CAAC,GAAGR,oBAAoB,CAACC,8BAA8B,CAACC,CAAC,EAAEC,CAAC,CAAC,GAAG,CAAC,CAAC;IAC1G,CAAC,MACI,IAAIpC,eAAe,CAACoC,CAAC,CAACK,WAAW,CAAC,EAAE;MACrC,OAAO,CAAC;IACZ,CAAC,MACI;MACD,OAAOR,oBAAoB,CAACC,8BAA8B,CAACC,CAAC,EAAEC,CAAC,CAAC,CAAC,CAAC;IACtE;EACJ;EACAM,WAAWA,CAACC,OAAO,EAAEC,aAAa,EAAEC,WAAW,EAAE;IAC7C,KAAK,CAAC,CAAC;IACP,IAAI,CAACD,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACE,SAAS,CAACD,WAAW,CAAC;IAC3B,IAAI,CAACE,UAAU,GAAG,CAAC,GAAGJ,OAAO,CAAC,CAACK,IAAI,CAACf,oBAAoB,CAACK,qBAAqB,CAAC;IAC/E,IAAI,CAACW,YAAY,GAAG,IAAI,CAACF,UAAU,CAACG,MAAM,CAAC,CAAC;MAAEX;IAAO,CAAC,KAAK,CAACA,MAAM,CAACY,QAAQ,CAAC;EAChF;EACA,IAAIC,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACH,YAAY,CAACI,IAAI,CAAC,CAAC;MAAEd,MAAM,EAAEe;IAAI,CAAC,KAAK,CAAC,CAACA,GAAG,CAACC,IAAI,IAAIlC,cAAc,CAACmC,QAAQ,CAACC,QAAQ,CAAC,IAAIhC,gBAAgB,CAAC6B,GAAG,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAACD,GAAG,CAACjB,WAAW,CAAC;EAC3J;EACA,IAAIqB,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACT,YAAY,CAACI,IAAI,CAAC,CAAC;MAAEd,MAAM,EAAEe;IAAI,CAAC,KAAK,CAAC,CAACA,GAAG,CAACd,IAAI,CAAC;EAClE;EACA,IAAImB,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACV,YAAY,CAACW,KAAK,CAAC,CAAC;MAAErB,MAAM,EAAEe;IAAI,CAAC,KAAK,CAAC,CAACA,GAAG,CAACd,IAAI,CAAC;EACnE;AACJ;AACA,MAAMqB,wBAAwB,GAAG;EAAElB,OAAO,EAAE,EAAE;EAAEC,aAAa,EAAEkB;AAAU,CAAC;AAC1E,gBAAsBC,cAAcA,CAAAC,EAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;EAAA,OAAAC,eAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AA2DnC,SAAAF,gBAAA;EAAAA,eAAA,GAAAG,iBAAA,CA3DM,WAA8BC,QAAQ,EAAEC,KAAK,EAAEC,gBAAgB,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,KAAK,EAAE;IAC9F,MAAM7B,MAAM,GAAG2B,OAAO,CAAC3B,MAAM,IAAI,CAAC,CAAC;IACnC,MAAM8B,cAAc,GAAG;MACnB,GAAG9B,MAAM;MACT+B,QAAQ,EAAE,CAAC,IAAI/B,MAAM,CAAC+B,QAAQ,IAAI,EAAE,CAAC,EAAE5D,cAAc,CAAC6D,QAAQ;IAClE,CAAC;IACD,MAAMC,iBAAiB,GAAG;MACtBC,IAAI,EAAElC,MAAM,CAACmC,OAAO,EAAEC,KAAK;MAC3BT,OAAO,EAAEA,OAAO,CAACU;IACrB,CAAC;IACD,MAAMC,GAAG,GAAG,IAAI3E,gCAAgC,CAAC8D,KAAK,EAAEI,KAAK,CAAC;IAC9D;IACA,MAAMU,0BAA0B,GAAIZ,OAAO,CAACU,IAAI,KAAK,CAAC,CAAC,0CAA2C;IAClG,MAAMG,SAAS,GAAGC,sBAAsB,CAACjB,QAAQ,EAAEC,KAAK,EAAGc,0BAA0B,GAAIT,cAAc,GAAG9B,MAAM,CAAC;IACjH,MAAML,WAAW,GAAG,IAAIvC,eAAe,CAAC,CAAC;IACzC,MAAMsF,QAAQ,GAAGF,SAAS,CAACG,GAAG;MAAA,IAAAC,KAAA,GAAArB,iBAAA,CAAC,WAAOsB,QAAQ,EAAK;QAC/C,IAAI;UACAjB,QAAQ,CAACkB,MAAM,CAACD,QAAQ,CAAC;UACzB,MAAME,mBAAmB,SAASF,QAAQ,CAACG,kBAAkB,CAACvB,KAAK,EAAEC,gBAAgB,EAAEO,iBAAiB,EAAEK,GAAG,CAACT,KAAK,CAAC;UACpH,IAAIkB,mBAAmB,EAAE;YACrBpD,WAAW,CAACsD,GAAG,CAACF,mBAAmB,CAAC;UACxC;UACA,IAAIT,GAAG,CAACT,KAAK,CAACqB,uBAAuB,EAAE;YACnC,OAAOvC,wBAAwB;UACnC;UACA,MAAMwC,eAAe,GAAG,CAACJ,mBAAmB,EAAEtD,OAAO,IAAI,EAAE,EAAEO,MAAM,CAACX,MAAM,IAAIA,MAAM,IAAIhB,aAAa,CAAC2B,MAAM,EAAEX,MAAM,CAAC,CAAC;UACtH,MAAMK,aAAa,GAAG0D,4BAA4B,CAACP,QAAQ,EAAEM,eAAe,EAAEnD,MAAM,CAACmC,OAAO,CAAC;UAC7F,OAAO;YACH1C,OAAO,EAAE0D,eAAe,CAACR,GAAG,CAACtD,MAAM,IAAI,IAAInB,cAAc,CAACmB,MAAM,EAAEwD,QAAQ,CAAC,CAAC;YAC5EnD;UACJ,CAAC;QACL,CAAC,CACD,OAAO2D,GAAG,EAAE;UACR,IAAIpG,mBAAmB,CAACoG,GAAG,CAAC,EAAE;YAC1B,MAAMA,GAAG;UACb;UACAnG,yBAAyB,CAACmG,GAAG,CAAC;UAC9B,OAAO1C,wBAAwB;QACnC;MACJ,CAAC;MAAA,iBAAA2C,IAAA;QAAA,OAAAV,KAAA,CAAAvB,KAAA,OAAAC,SAAA;MAAA;IAAA,IAAC;IACF,MAAMiC,QAAQ,GAAG/B,QAAQ,CAACgC,WAAW,CAAC,MAAM;MACxC,MAAMC,YAAY,GAAGjC,QAAQ,CAACkC,GAAG,CAACjC,KAAK,CAAC;MACxC,IAAI,CAAC5E,MAAM,CAAC4G,YAAY,EAAEjB,SAAS,CAAC,EAAE;QAClCF,GAAG,CAACqB,MAAM,CAAC,CAAC;MAChB;IACJ,CAAC,CAAC;IACF,IAAI;MACA,MAAMlE,OAAO,SAASmE,OAAO,CAACF,GAAG,CAAChB,QAAQ,CAAC;MAC3C,MAAM7C,UAAU,GAAGJ,OAAO,CAACkD,GAAG,CAACkB,CAAC,IAAIA,CAAC,CAACpE,OAAO,CAAC,CAACqE,IAAI,CAAC,CAAC;MACrD,MAAMC,gBAAgB,GAAG,CACrB,GAAGnH,QAAQ,CAAC6C,OAAO,CAACkD,GAAG,CAACkB,CAAC,IAAIA,CAAC,CAACnE,aAAa,CAAC,CAAC,EAC9C,GAAGsE,2CAA2C,CAACxC,QAAQ,EAAEC,KAAK,EAAEE,OAAO,EAAE9B,UAAU,CAAC,CACvF;MACD,OAAO,IAAId,oBAAoB,CAACc,UAAU,EAAEkE,gBAAgB,EAAEpE,WAAW,CAAC;IAC9E,CAAC,SACO;MACJ4D,QAAQ,CAACU,OAAO,CAAC,CAAC;MAClB3B,GAAG,CAAC2B,OAAO,CAAC,CAAC;IACjB;EACJ,CAAC;EAAA,OAAA7C,eAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AACD,SAASmB,sBAAsBA,CAACjB,QAAQ,EAAEC,KAAK,EAAEzB,MAAM,EAAE;EACrD,OAAOwB,QAAQ,CAACkC,GAAG,CAACjC,KAAK;EACrB;EAAA,CACCzB,MAAM,CAAC6C,QAAQ,IAAI;IACpB,IAAI,CAACA,QAAQ,CAACqB,uBAAuB,EAAE;MACnC;MACA,OAAO,IAAI;IACf;IACA,OAAOrB,QAAQ,CAACqB,uBAAuB,CAAC/D,IAAI,CAACE,IAAI,IAAI/B,uBAAuB,CAAC0B,MAAM,EAAE,IAAIzB,gBAAgB,CAAC8B,IAAI,CAAC,CAAC,CAAC;EACrH,CAAC,CAAC;AACN;AACA,UAAU2D,2CAA2CA,CAACxC,QAAQ,EAAEC,KAAK,EAAEE,OAAO,EAAEwC,aAAa,EAAE;EAC3F,IAAI1C,KAAK,IAAI0C,aAAa,CAACC,MAAM,EAAE;IAC/B,KAAK,MAAMvB,QAAQ,IAAIrB,QAAQ,CAACkC,GAAG,CAACjC,KAAK,CAAC,EAAE;MACxC,IAAIoB,QAAQ,CAACwB,uBAAuB,EAAE;QAClC,OAAOxB,QAAQ,CAACwB,uBAAuB,GAAG;UAAE1C,OAAO,EAAEA,OAAO,CAACU,IAAI;UAAEH,IAAI,EAAEP,OAAO,CAAC3B,MAAM,EAAEmC,OAAO,EAAEC;QAAM,CAAC,EAAE+B,aAAa,CAACxB,GAAG,CAAC2B,IAAI,IAAIA,IAAI,CAACjF,MAAM,CAAC,CAAC;MACtJ;IACJ;EACJ;AACJ;AACA,SAAS+D,4BAA4BA,CAACP,QAAQ,EAAEE,mBAAmB,EAAEb,IAAI,EAAE;EACvE,IAAI,CAACW,QAAQ,CAACnD,aAAa,EAAE;IACzB,OAAOkB,SAAS;EACpB;EACA,MAAMlB,aAAa,GAAGmD,QAAQ,CAACnD,aAAa,CAACiD,GAAG,CAAC4B,KAAK,KAAK;IAAElE,IAAI,EAAE,IAAI9B,gBAAgB,CAACgG,KAAK,CAAClE,IAAI,CAAC;IAAEmE,OAAO,EAAED,KAAK,CAACC;EAAQ,CAAC,CAAC,CAAC;EAC/H,IAAItC,IAAI,EAAE;IACN,IAAIuC,WAAW;IACf,KAAK,MAAMF,KAAK,IAAI7E,aAAa,EAAE;MAC/B,IAAI6E,KAAK,CAAClE,IAAI,CAACE,QAAQ,CAAC2B,IAAI,CAAC,EAAE;QAC3B,IAAI,CAACuC,WAAW,EAAE;UACdA,WAAW,GAAGF,KAAK;QACvB,CAAC,MACI;UACD;UACA,IAAIE,WAAW,CAACpE,IAAI,CAACE,QAAQ,CAACgE,KAAK,CAAClE,IAAI,CAAC,EAAE;YACvCoE,WAAW,GAAGF,KAAK;UACvB;QACJ;MACJ;IACJ;IACA,IAAIE,WAAW,EAAE;MACb,OAAOA,WAAW,EAAED,OAAO;IAC/B;EACJ;EACA;EACA,KAAK,MAAMnF,MAAM,IAAI0D,mBAAmB,EAAE;IACtC,IAAI,CAAC1D,MAAM,CAACgB,IAAI,EAAE;MACd;IACJ;IACA,KAAK,MAAMkE,KAAK,IAAI7E,aAAa,EAAE;MAC/B,IAAI6E,KAAK,CAAClE,IAAI,CAACE,QAAQ,CAAC,IAAIhC,gBAAgB,CAACc,MAAM,CAACgB,IAAI,CAAC,CAAC,EAAE;QACxD,OAAOkE,KAAK,CAACC,OAAO;MACxB;IACJ;EACJ;EACA,OAAO5D,SAAS;AACpB;AACA,OAAO,IAAI8D,qBAAqB,gBAC/B,UAAUA,qBAAqB,EAAE;EAC9BA,qBAAqB,CAAC,QAAQ,CAAC,GAAG,QAAQ;EAC1CA,qBAAqB,CAAC,kBAAkB,CAAC,GAAG,kBAAkB;EAC9DA,qBAAqB,CAAC,iBAAiB,CAAC,GAAG,iBAAiB;EAC5DA,qBAAqB,CAAC,iBAAiB,CAAC,GAAG,iBAAiB,CAAC,CAAC;EAAA,OAJvDA,qBAAqB;AAKhC,CAAC,CAAEA,qBAAqB,IAA6B,CAAC,CAAE,CANxB;AAOhC,gBAAsBC,eAAeA,CAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;EAAA,OAAAC,gBAAA,CAAA3D,KAAA,OAAAC,SAAA;AAAA;AAuCpC,SAAA0D,iBAAA;EAAAA,gBAAA,GAAAzD,iBAAA,CAvCM,WAA+B0D,QAAQ,EAAEX,IAAI,EAAEY,gBAAgB,EAAEC,OAAO,EAAEtD,KAAK,GAAG9E,iBAAiB,CAACqI,IAAI,EAAE;IAC7G,MAAMC,eAAe,GAAGJ,QAAQ,CAACK,GAAG,CAAChI,gBAAgB,CAAC;IACtD,MAAMiI,cAAc,GAAGN,QAAQ,CAACK,GAAG,CAACxH,eAAe,CAAC;IACpD,MAAM0H,gBAAgB,GAAGP,QAAQ,CAACK,GAAG,CAACrH,iBAAiB,CAAC;IACxD,MAAMwH,mBAAmB,GAAGR,QAAQ,CAACK,GAAG,CAACvH,oBAAoB,CAAC;IAC9DyH,gBAAgB,CAACE,UAAU,CAAC,4BAA4B,EAAE;MACtDC,eAAe,EAAErB,IAAI,CAACjF,MAAM,CAACuG,KAAK;MAClCC,cAAc,EAAEvB,IAAI,CAACjF,MAAM,CAACgB,IAAI;MAChCyF,qBAAqB,EAAE,CAAC,CAACxB,IAAI,CAACjF,MAAM,CAACF,WAAW;MAChD4G,MAAM,EAAEb;IACZ,CAAC,CAAC;IACF,MAAMZ,IAAI,CAAC0B,OAAO,CAACnE,KAAK,CAAC;IACzB,IAAIA,KAAK,CAACqB,uBAAuB,EAAE;MAC/B;IACJ;IACA,IAAIoB,IAAI,CAACjF,MAAM,CAAC4G,IAAI,EAAEC,KAAK,CAAC9B,MAAM,EAAE;MAChC,MAAM+B,MAAM,SAASd,eAAe,CAAChE,KAAK,CAACiD,IAAI,CAACjF,MAAM,CAAC4G,IAAI,EAAE;QACzDG,MAAM,EAAEjB,OAAO,EAAEiB,MAAM;QACvBC,KAAK,EAAE/B,IAAI,CAACjF,MAAM,CAACuG,KAAK;QACxBU,aAAa,EAAEhC,IAAI,CAACjF,MAAM,CAACuG,KAAK;QAChCW,IAAI,EAAE,qBAAqB;QAC3BC,qBAAqB,EAAEtB,gBAAgB,KAAKR,qBAAqB,CAAC+B,MAAM;QACxEC,WAAW,EAAEvB,OAAO,EAAEwB;MAC1B,CAAC,CAAC;MACF,IAAI,CAACR,MAAM,CAACS,SAAS,EAAE;QACnB;MACJ;IACJ;IACA,IAAItC,IAAI,CAACjF,MAAM,CAACmF,OAAO,EAAE;MACrB,IAAI;QACA,MAAMe,cAAc,CAACsB,cAAc,CAACvC,IAAI,CAACjF,MAAM,CAACmF,OAAO,CAACsC,EAAE,EAAE,IAAIxC,IAAI,CAACjF,MAAM,CAACmF,OAAO,CAAClD,SAAS,IAAI,EAAE,CAAC,CAAC;MACzG,CAAC,CACD,OAAO+B,GAAG,EAAE;QACR,MAAM0D,OAAO,GAAGC,SAAS,CAAC3D,GAAG,CAAC;QAC9BoC,mBAAmB,CAACwB,KAAK,CAAC,OAAOF,OAAO,KAAK,QAAQ,GAC/CA,OAAO,GACPnJ,GAAG,CAACsJ,QAAQ,CAAC,uBAAuB,EAAE,0DAA0D,CAAC,CAAC;MAC5G;IACJ;EACJ,CAAC;EAAA,OAAAlC,gBAAA,CAAA3D,KAAA,OAAAC,SAAA;AAAA;AACD,SAAS0F,SAASA,CAAC3D,GAAG,EAAE;EACpB,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;IACzB,OAAOA,GAAG;EACd,CAAC,MACI,IAAIA,GAAG,YAAY8D,KAAK,IAAI,OAAO9D,GAAG,CAAC0D,OAAO,KAAK,QAAQ,EAAE;IAC9D,OAAO1D,GAAG,CAAC0D,OAAO;EACtB,CAAC,MACI;IACD,OAAOnG,SAAS;EACpB;AACJ;AACA/C,gBAAgB,CAACuJ,eAAe,CAAC,4BAA4B;EAAA,IAAAC,IAAA,GAAA9F,iBAAA,CAAE,WAAgB0D,QAAQ,EAAEqC,QAAQ,EAAE5F,gBAAgB,EAAErB,IAAI,EAAEkH,gBAAgB,EAAE;IACzI,IAAI,EAAED,QAAQ,YAAYjK,GAAG,CAAC,EAAE;MAC5B,MAAML,eAAe,CAAC,CAAC;IAC3B;IACA,MAAM;MAAEwK;IAAmB,CAAC,GAAGvC,QAAQ,CAACK,GAAG,CAAC7H,wBAAwB,CAAC;IACrE,MAAMgE,KAAK,GAAGwD,QAAQ,CAACK,GAAG,CAAC5H,aAAa,CAAC,CAAC+J,QAAQ,CAACH,QAAQ,CAAC;IAC5D,IAAI,CAAC7F,KAAK,EAAE;MACR,MAAMzE,eAAe,CAAC,CAAC;IAC3B;IACA,MAAM0K,yBAAyB,GAAGlK,SAAS,CAACmK,YAAY,CAACjG,gBAAgB,CAAC,GACpElE,SAAS,CAACoK,aAAa,CAAClG,gBAAgB,CAAC,GACzCnE,KAAK,CAACsK,QAAQ,CAACnG,gBAAgB,CAAC,GAC5BD,KAAK,CAACqG,aAAa,CAACpG,gBAAgB,CAAC,GACrCd,SAAS;IACnB,IAAI,CAAC8G,yBAAyB,EAAE;MAC5B,MAAM1K,eAAe,CAAC,CAAC;IAC3B;IACA,MAAMmF,OAAO,GAAG,OAAO9B,IAAI,KAAK,QAAQ,GAAG,IAAI9B,gBAAgB,CAAC8B,IAAI,CAAC,GAAGO,SAAS;IACjF,MAAMmH,aAAa,SAASlH,cAAc,CAAC2G,kBAAkB,EAAE/F,KAAK,EAAEiG,yBAAyB,EAAE;MAAErF,IAAI,EAAE,CAAC,CAAC;MAA8C2F,aAAa,EAAE5J,uBAAuB,CAAC6J,OAAO;MAAEjI,MAAM,EAAE;QAAEkI,oBAAoB,EAAE,IAAI;QAAE/F;MAAQ;IAAE,CAAC,EAAEnE,QAAQ,CAACoH,IAAI,EAAErI,iBAAiB,CAACqI,IAAI,CAAC;IAClS,MAAM+C,SAAS,GAAG,EAAE;IACpB,MAAMC,YAAY,GAAGC,IAAI,CAACC,GAAG,CAACP,aAAa,CAAChI,YAAY,CAACqE,MAAM,EAAE,OAAOmD,gBAAgB,KAAK,QAAQ,GAAGA,gBAAgB,GAAG,CAAC,CAAC;IAC7H,KAAK,IAAIgB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,YAAY,EAAEG,CAAC,EAAE,EAAE;MACnCJ,SAAS,CAACK,IAAI,CAACT,aAAa,CAAChI,YAAY,CAACwI,CAAC,CAAC,CAACvC,OAAO,CAACjJ,iBAAiB,CAACqI,IAAI,CAAC,CAAC;IACjF;IACA,IAAI;MACA,MAAMxB,OAAO,CAACF,GAAG,CAACyE,SAAS,CAAC;MAC5B,OAAOJ,aAAa,CAAChI,YAAY,CAAC4C,GAAG,CAAC2B,IAAI,IAAIA,IAAI,CAACjF,MAAM,CAAC;IAC9D,CAAC,SACO;MACJoJ,UAAU,CAAC,MAAMV,aAAa,CAAC9D,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC;IAClD;EACJ,CAAC;EAAA,iBAAAyE,GAAA,EAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA;IAAA,OAAAzB,IAAA,CAAAhG,KAAA,OAAAC,SAAA;EAAA;AAAA,IAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}