{"ast": null, "code": "export const routes = [{\n  path: '',\n  loadComponent: () => import('./pages/marketplace/marketplace.component').then(m => m.MarketplaceComponent)\n}];", "map": {"version": 3, "names": ["routes", "path", "loadComponent", "then", "m", "MarketplaceComponent"], "sources": ["C:\\console\\aava-ui-web\\projects\\marketing\\src\\app\\app.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\r\n\r\nexport const routes: Routes = [\r\n  {\r\n    path: '',\r\n    loadComponent: () =>\r\n      import('./pages/marketplace/marketplace.component').then(\r\n        (m) => m.MarketplaceComponent,\r\n      ),\r\n  },\r\n];\r\n"], "mappings": "AAEA,OAAO,MAAMA,MAAM,GAAW,CAC5B;EACEC,IAAI,EAAE,EAAE;EACRC,aAAa,EAAEA,CAAA,KACb,MAAM,CAAC,2CAA2C,CAAC,CAACC,IAAI,CACrDC,CAAC,IAAKA,CAAC,CAACC,oBAAoB;CAElC,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}