{"ast": null, "code": "/**\n * Represents contiguous tokens over a contiguous range of lines.\n */\nexport class ContiguousMultilineTokens {\n  /**\n   * (Inclusive) start line number for these tokens.\n   */\n  get startLineNumber() {\n    return this._startLineNumber;\n  }\n  /**\n   * (Inclusive) end line number for these tokens.\n   */\n  get endLineNumber() {\n    return this._startLineNumber + this._tokens.length - 1;\n  }\n  constructor(startLineNumber, tokens) {\n    this._startLineNumber = startLineNumber;\n    this._tokens = tokens;\n  }\n  /**\n   * @see {@link _tokens}\n   */\n  getLineTokens(lineNumber) {\n    return this._tokens[lineNumber - this._startLineNumber];\n  }\n  appendLineTokens(lineTokens) {\n    this._tokens.push(lineTokens);\n  }\n}", "map": {"version": 3, "names": ["ContiguousMultilineTokens", "startLineNumber", "_startLineNumber", "endLineNumber", "_tokens", "length", "constructor", "tokens", "getLineTokens", "lineNumber", "appendLineTokens", "lineTokens", "push"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/tokens/contiguousMultilineTokens.js"], "sourcesContent": ["/**\n * Represents contiguous tokens over a contiguous range of lines.\n */\nexport class ContiguousMultilineTokens {\n    /**\n     * (Inclusive) start line number for these tokens.\n     */\n    get startLineNumber() {\n        return this._startLineNumber;\n    }\n    /**\n     * (Inclusive) end line number for these tokens.\n     */\n    get endLineNumber() {\n        return this._startLineNumber + this._tokens.length - 1;\n    }\n    constructor(startLineNumber, tokens) {\n        this._startLineNumber = startLineNumber;\n        this._tokens = tokens;\n    }\n    /**\n     * @see {@link _tokens}\n     */\n    getLineTokens(lineNumber) {\n        return this._tokens[lineNumber - this._startLineNumber];\n    }\n    appendLineTokens(lineTokens) {\n        this._tokens.push(lineTokens);\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA,OAAO,MAAMA,yBAAyB,CAAC;EACnC;AACJ;AACA;EACI,IAAIC,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACC,gBAAgB;EAChC;EACA;AACJ;AACA;EACI,IAAIC,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACD,gBAAgB,GAAG,IAAI,CAACE,OAAO,CAACC,MAAM,GAAG,CAAC;EAC1D;EACAC,WAAWA,CAACL,eAAe,EAAEM,MAAM,EAAE;IACjC,IAAI,CAACL,gBAAgB,GAAGD,eAAe;IACvC,IAAI,CAACG,OAAO,GAAGG,MAAM;EACzB;EACA;AACJ;AACA;EACIC,aAAaA,CAACC,UAAU,EAAE;IACtB,OAAO,IAAI,CAACL,OAAO,CAACK,UAAU,GAAG,IAAI,CAACP,gBAAgB,CAAC;EAC3D;EACAQ,gBAAgBA,CAACC,UAAU,EAAE;IACzB,IAAI,CAACP,OAAO,CAACQ,IAAI,CAACD,UAAU,CAAC;EACjC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}