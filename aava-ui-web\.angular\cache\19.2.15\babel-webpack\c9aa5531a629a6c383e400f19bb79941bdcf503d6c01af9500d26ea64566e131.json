{"ast": null, "code": "import _asyncToGenerator from \"C:/console/aava-ui-web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = this && this.__param || function (paramIndex, decorator) {\n  return function (target, key) {\n    decorator(target, key, paramIndex);\n  };\n};\nvar WorkerBasedDocumentDiffProvider_1;\nimport { registerSingleton } from '../../../../platform/instantiation/common/extensions.js';\nimport { IInstantiationService, createDecorator } from '../../../../platform/instantiation/common/instantiation.js';\nimport { Emitter } from '../../../../base/common/event.js';\nimport { StopWatch } from '../../../../base/common/stopwatch.js';\nimport { LineRange } from '../../../common/core/lineRange.js';\nimport { DetailedLineRangeMapping, RangeMapping } from '../../../common/diff/rangeMapping.js';\nimport { IEditorWorkerService } from '../../../common/services/editorWorker.js';\nimport { ITelemetryService } from '../../../../platform/telemetry/common/telemetry.js';\nexport const IDiffProviderFactoryService = createDecorator('diffProviderFactoryService');\nlet WorkerBasedDiffProviderFactoryService = class WorkerBasedDiffProviderFactoryService {\n  constructor(instantiationService) {\n    this.instantiationService = instantiationService;\n  }\n  createDiffProvider(options) {\n    return this.instantiationService.createInstance(WorkerBasedDocumentDiffProvider, options);\n  }\n};\nWorkerBasedDiffProviderFactoryService = __decorate([__param(0, IInstantiationService)], WorkerBasedDiffProviderFactoryService);\nexport { WorkerBasedDiffProviderFactoryService };\nregisterSingleton(IDiffProviderFactoryService, WorkerBasedDiffProviderFactoryService, 1 /* InstantiationType.Delayed */);\nlet WorkerBasedDocumentDiffProvider = class WorkerBasedDocumentDiffProvider {\n  static {\n    WorkerBasedDocumentDiffProvider_1 = this;\n  }\n  static {\n    this.diffCache = new Map();\n  }\n  constructor(options, editorWorkerService, telemetryService) {\n    this.editorWorkerService = editorWorkerService;\n    this.telemetryService = telemetryService;\n    this.onDidChangeEventEmitter = new Emitter();\n    this.onDidChange = this.onDidChangeEventEmitter.event;\n    this.diffAlgorithm = 'advanced';\n    this.diffAlgorithmOnDidChangeSubscription = undefined;\n    this.setOptions(options);\n  }\n  dispose() {\n    this.diffAlgorithmOnDidChangeSubscription?.dispose();\n  }\n  computeDiff(original, modified, options, cancellationToken) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (typeof _this.diffAlgorithm !== 'string') {\n        return _this.diffAlgorithm.computeDiff(original, modified, options, cancellationToken);\n      }\n      if (original.isDisposed() || modified.isDisposed()) {\n        // TODO@hediet\n        return {\n          changes: [],\n          identical: true,\n          quitEarly: false,\n          moves: []\n        };\n      }\n      // This significantly speeds up the case when the original file is empty\n      if (original.getLineCount() === 1 && original.getLineMaxColumn(1) === 1) {\n        if (modified.getLineCount() === 1 && modified.getLineMaxColumn(1) === 1) {\n          return {\n            changes: [],\n            identical: true,\n            quitEarly: false,\n            moves: []\n          };\n        }\n        return {\n          changes: [new DetailedLineRangeMapping(new LineRange(1, 2), new LineRange(1, modified.getLineCount() + 1), [new RangeMapping(original.getFullModelRange(), modified.getFullModelRange())])],\n          identical: false,\n          quitEarly: false,\n          moves: []\n        };\n      }\n      const uriKey = JSON.stringify([original.uri.toString(), modified.uri.toString()]);\n      const context = JSON.stringify([original.id, modified.id, original.getAlternativeVersionId(), modified.getAlternativeVersionId(), JSON.stringify(options)]);\n      const c = WorkerBasedDocumentDiffProvider_1.diffCache.get(uriKey);\n      if (c && c.context === context) {\n        return c.result;\n      }\n      const sw = StopWatch.create();\n      const result = yield _this.editorWorkerService.computeDiff(original.uri, modified.uri, options, _this.diffAlgorithm);\n      const timeMs = sw.elapsed();\n      _this.telemetryService.publicLog2('diffEditor.computeDiff', {\n        timeMs,\n        timedOut: result?.quitEarly ?? true,\n        detectedMoves: options.computeMoves ? result?.moves.length ?? 0 : -1\n      });\n      if (cancellationToken.isCancellationRequested) {\n        // Text models might be disposed!\n        return {\n          changes: [],\n          identical: false,\n          quitEarly: true,\n          moves: []\n        };\n      }\n      if (!result) {\n        throw new Error('no diff result available');\n      }\n      // max 10 items in cache\n      if (WorkerBasedDocumentDiffProvider_1.diffCache.size > 10) {\n        WorkerBasedDocumentDiffProvider_1.diffCache.delete(WorkerBasedDocumentDiffProvider_1.diffCache.keys().next().value);\n      }\n      WorkerBasedDocumentDiffProvider_1.diffCache.set(uriKey, {\n        result,\n        context\n      });\n      return result;\n    })();\n  }\n  setOptions(newOptions) {\n    let didChange = false;\n    if (newOptions.diffAlgorithm) {\n      if (this.diffAlgorithm !== newOptions.diffAlgorithm) {\n        this.diffAlgorithmOnDidChangeSubscription?.dispose();\n        this.diffAlgorithmOnDidChangeSubscription = undefined;\n        this.diffAlgorithm = newOptions.diffAlgorithm;\n        if (typeof newOptions.diffAlgorithm !== 'string') {\n          this.diffAlgorithmOnDidChangeSubscription = newOptions.diffAlgorithm.onDidChange(() => this.onDidChangeEventEmitter.fire());\n        }\n        didChange = true;\n      }\n    }\n    if (didChange) {\n      this.onDidChangeEventEmitter.fire();\n    }\n  }\n};\nWorkerBasedDocumentDiffProvider = WorkerBasedDocumentDiffProvider_1 = __decorate([__param(1, IEditorWorkerService), __param(2, ITelemetryService)], WorkerBasedDocumentDiffProvider);\nexport { WorkerBasedDocumentDiffProvider };", "map": {"version": 3, "names": ["__decorate", "decorators", "target", "key", "desc", "c", "arguments", "length", "r", "Object", "getOwnPropertyDescriptor", "d", "Reflect", "decorate", "i", "defineProperty", "__param", "paramIndex", "decorator", "WorkerBasedDocumentDiffProvider_1", "registerSingleton", "IInstantiationService", "createDecorator", "Emitter", "StopWatch", "LineRange", "DetailedLineRangeMapping", "RangeMapping", "IEditorWorkerService", "ITelemetryService", "IDiffProviderFactoryService", "WorkerBasedDiffProviderFactoryService", "constructor", "instantiationService", "createDiffProvider", "options", "createInstance", "WorkerBasedDocumentDiffProvider", "diffCache", "Map", "editorWorkerService", "telemetryService", "onDidChangeEventEmitter", "onDidChange", "event", "diffAlgorithm", "diffAlgorithmOnDidChangeSubscription", "undefined", "setOptions", "dispose", "computeDiff", "original", "modified", "cancellationToken", "_this", "_asyncToGenerator", "isDisposed", "changes", "identical", "quit<PERSON>arly", "moves", "getLineCount", "getLineMaxColumn", "getFullModelRange", "<PERSON><PERSON><PERSON><PERSON>", "JSON", "stringify", "uri", "toString", "context", "id", "getAlternativeVersionId", "get", "result", "sw", "create", "timeMs", "elapsed", "publicLog2", "timedOut", "detectedMoves", "computeMoves", "isCancellationRequested", "Error", "size", "delete", "keys", "next", "value", "set", "newOptions", "<PERSON><PERSON><PERSON><PERSON>", "fire"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/browser/widget/diffEditor/diffProviderFactoryService.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = (this && this.__param) || function (paramIndex, decorator) {\n    return function (target, key) { decorator(target, key, paramIndex); }\n};\nvar WorkerBasedDocumentDiffProvider_1;\nimport { registerSingleton } from '../../../../platform/instantiation/common/extensions.js';\nimport { IInstantiationService, createDecorator } from '../../../../platform/instantiation/common/instantiation.js';\nimport { Emitter } from '../../../../base/common/event.js';\nimport { StopWatch } from '../../../../base/common/stopwatch.js';\nimport { LineRange } from '../../../common/core/lineRange.js';\nimport { DetailedLineRangeMapping, RangeMapping } from '../../../common/diff/rangeMapping.js';\nimport { IEditorWorkerService } from '../../../common/services/editorWorker.js';\nimport { ITelemetryService } from '../../../../platform/telemetry/common/telemetry.js';\nexport const IDiffProviderFactoryService = createDecorator('diffProviderFactoryService');\nlet WorkerBasedDiffProviderFactoryService = class WorkerBasedDiffProviderFactoryService {\n    constructor(instantiationService) {\n        this.instantiationService = instantiationService;\n    }\n    createDiffProvider(options) {\n        return this.instantiationService.createInstance(WorkerBasedDocumentDiffProvider, options);\n    }\n};\nWorkerBasedDiffProviderFactoryService = __decorate([\n    __param(0, IInstantiationService)\n], WorkerBasedDiffProviderFactoryService);\nexport { WorkerBasedDiffProviderFactoryService };\nregisterSingleton(IDiffProviderFactoryService, WorkerBasedDiffProviderFactoryService, 1 /* InstantiationType.Delayed */);\nlet WorkerBasedDocumentDiffProvider = class WorkerBasedDocumentDiffProvider {\n    static { WorkerBasedDocumentDiffProvider_1 = this; }\n    static { this.diffCache = new Map(); }\n    constructor(options, editorWorkerService, telemetryService) {\n        this.editorWorkerService = editorWorkerService;\n        this.telemetryService = telemetryService;\n        this.onDidChangeEventEmitter = new Emitter();\n        this.onDidChange = this.onDidChangeEventEmitter.event;\n        this.diffAlgorithm = 'advanced';\n        this.diffAlgorithmOnDidChangeSubscription = undefined;\n        this.setOptions(options);\n    }\n    dispose() {\n        this.diffAlgorithmOnDidChangeSubscription?.dispose();\n    }\n    async computeDiff(original, modified, options, cancellationToken) {\n        if (typeof this.diffAlgorithm !== 'string') {\n            return this.diffAlgorithm.computeDiff(original, modified, options, cancellationToken);\n        }\n        if (original.isDisposed() || modified.isDisposed()) {\n            // TODO@hediet\n            return {\n                changes: [],\n                identical: true,\n                quitEarly: false,\n                moves: [],\n            };\n        }\n        // This significantly speeds up the case when the original file is empty\n        if (original.getLineCount() === 1 && original.getLineMaxColumn(1) === 1) {\n            if (modified.getLineCount() === 1 && modified.getLineMaxColumn(1) === 1) {\n                return {\n                    changes: [],\n                    identical: true,\n                    quitEarly: false,\n                    moves: [],\n                };\n            }\n            return {\n                changes: [\n                    new DetailedLineRangeMapping(new LineRange(1, 2), new LineRange(1, modified.getLineCount() + 1), [\n                        new RangeMapping(original.getFullModelRange(), modified.getFullModelRange())\n                    ])\n                ],\n                identical: false,\n                quitEarly: false,\n                moves: [],\n            };\n        }\n        const uriKey = JSON.stringify([original.uri.toString(), modified.uri.toString()]);\n        const context = JSON.stringify([original.id, modified.id, original.getAlternativeVersionId(), modified.getAlternativeVersionId(), JSON.stringify(options)]);\n        const c = WorkerBasedDocumentDiffProvider_1.diffCache.get(uriKey);\n        if (c && c.context === context) {\n            return c.result;\n        }\n        const sw = StopWatch.create();\n        const result = await this.editorWorkerService.computeDiff(original.uri, modified.uri, options, this.diffAlgorithm);\n        const timeMs = sw.elapsed();\n        this.telemetryService.publicLog2('diffEditor.computeDiff', {\n            timeMs,\n            timedOut: result?.quitEarly ?? true,\n            detectedMoves: options.computeMoves ? (result?.moves.length ?? 0) : -1,\n        });\n        if (cancellationToken.isCancellationRequested) {\n            // Text models might be disposed!\n            return {\n                changes: [],\n                identical: false,\n                quitEarly: true,\n                moves: [],\n            };\n        }\n        if (!result) {\n            throw new Error('no diff result available');\n        }\n        // max 10 items in cache\n        if (WorkerBasedDocumentDiffProvider_1.diffCache.size > 10) {\n            WorkerBasedDocumentDiffProvider_1.diffCache.delete(WorkerBasedDocumentDiffProvider_1.diffCache.keys().next().value);\n        }\n        WorkerBasedDocumentDiffProvider_1.diffCache.set(uriKey, { result, context });\n        return result;\n    }\n    setOptions(newOptions) {\n        let didChange = false;\n        if (newOptions.diffAlgorithm) {\n            if (this.diffAlgorithm !== newOptions.diffAlgorithm) {\n                this.diffAlgorithmOnDidChangeSubscription?.dispose();\n                this.diffAlgorithmOnDidChangeSubscription = undefined;\n                this.diffAlgorithm = newOptions.diffAlgorithm;\n                if (typeof newOptions.diffAlgorithm !== 'string') {\n                    this.diffAlgorithmOnDidChangeSubscription = newOptions.diffAlgorithm.onDidChange(() => this.onDidChangeEventEmitter.fire());\n                }\n                didChange = true;\n            }\n        }\n        if (didChange) {\n            this.onDidChangeEventEmitter.fire();\n        }\n    }\n};\nWorkerBasedDocumentDiffProvider = WorkerBasedDocumentDiffProvider_1 = __decorate([\n    __param(1, IEditorWorkerService),\n    __param(2, ITelemetryService)\n], WorkerBasedDocumentDiffProvider);\nexport { WorkerBasedDocumentDiffProvider };\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA,IAAIA,UAAU,GAAI,IAAI,IAAI,IAAI,CAACA,UAAU,IAAK,UAAUC,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAE;EACnF,IAAIC,CAAC,GAAGC,SAAS,CAACC,MAAM;IAAEC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGH,MAAM,GAAGE,IAAI,KAAK,IAAI,GAAGA,IAAI,GAAGK,MAAM,CAACC,wBAAwB,CAACR,MAAM,EAAEC,GAAG,CAAC,GAAGC,IAAI;IAAEO,CAAC;EAC5H,IAAI,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,CAACC,QAAQ,KAAK,UAAU,EAAEL,CAAC,GAAGI,OAAO,CAACC,QAAQ,CAACZ,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,CAAC,CAAC,KAC1H,KAAK,IAAIU,CAAC,GAAGb,UAAU,CAACM,MAAM,GAAG,CAAC,EAAEO,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE,IAAIH,CAAC,GAAGV,UAAU,CAACa,CAAC,CAAC,EAAEN,CAAC,GAAG,CAACH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACH,CAAC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACT,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,GAAGG,CAAC,CAACT,MAAM,EAAEC,GAAG,CAAC,KAAKK,CAAC;EACjJ,OAAOH,CAAC,GAAG,CAAC,IAAIG,CAAC,IAAIC,MAAM,CAACM,cAAc,CAACb,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,EAAEA,CAAC;AACjE,CAAC;AACD,IAAIQ,OAAO,GAAI,IAAI,IAAI,IAAI,CAACA,OAAO,IAAK,UAAUC,UAAU,EAAEC,SAAS,EAAE;EACrE,OAAO,UAAUhB,MAAM,EAAEC,GAAG,EAAE;IAAEe,SAAS,CAAChB,MAAM,EAAEC,GAAG,EAAEc,UAAU,CAAC;EAAE,CAAC;AACzE,CAAC;AACD,IAAIE,iCAAiC;AACrC,SAASC,iBAAiB,QAAQ,yDAAyD;AAC3F,SAASC,qBAAqB,EAAEC,eAAe,QAAQ,4DAA4D;AACnH,SAASC,OAAO,QAAQ,kCAAkC;AAC1D,SAASC,SAAS,QAAQ,sCAAsC;AAChE,SAASC,SAAS,QAAQ,mCAAmC;AAC7D,SAASC,wBAAwB,EAAEC,YAAY,QAAQ,sCAAsC;AAC7F,SAASC,oBAAoB,QAAQ,0CAA0C;AAC/E,SAASC,iBAAiB,QAAQ,oDAAoD;AACtF,OAAO,MAAMC,2BAA2B,GAAGR,eAAe,CAAC,4BAA4B,CAAC;AACxF,IAAIS,qCAAqC,GAAG,MAAMA,qCAAqC,CAAC;EACpFC,WAAWA,CAACC,oBAAoB,EAAE;IAC9B,IAAI,CAACA,oBAAoB,GAAGA,oBAAoB;EACpD;EACAC,kBAAkBA,CAACC,OAAO,EAAE;IACxB,OAAO,IAAI,CAACF,oBAAoB,CAACG,cAAc,CAACC,+BAA+B,EAAEF,OAAO,CAAC;EAC7F;AACJ,CAAC;AACDJ,qCAAqC,GAAG/B,UAAU,CAAC,CAC/CgB,OAAO,CAAC,CAAC,EAAEK,qBAAqB,CAAC,CACpC,EAAEU,qCAAqC,CAAC;AACzC,SAASA,qCAAqC;AAC9CX,iBAAiB,CAACU,2BAA2B,EAAEC,qCAAqC,EAAE,CAAC,CAAC,+BAA+B,CAAC;AACxH,IAAIM,+BAA+B,GAAG,MAAMA,+BAA+B,CAAC;EACxE;IAASlB,iCAAiC,GAAG,IAAI;EAAE;EACnD;IAAS,IAAI,CAACmB,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;EAAE;EACrCP,WAAWA,CAACG,OAAO,EAAEK,mBAAmB,EAAEC,gBAAgB,EAAE;IACxD,IAAI,CAACD,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACC,uBAAuB,GAAG,IAAInB,OAAO,CAAC,CAAC;IAC5C,IAAI,CAACoB,WAAW,GAAG,IAAI,CAACD,uBAAuB,CAACE,KAAK;IACrD,IAAI,CAACC,aAAa,GAAG,UAAU;IAC/B,IAAI,CAACC,oCAAoC,GAAGC,SAAS;IACrD,IAAI,CAACC,UAAU,CAACb,OAAO,CAAC;EAC5B;EACAc,OAAOA,CAAA,EAAG;IACN,IAAI,CAACH,oCAAoC,EAAEG,OAAO,CAAC,CAAC;EACxD;EACMC,WAAWA,CAACC,QAAQ,EAAEC,QAAQ,EAAEjB,OAAO,EAAEkB,iBAAiB,EAAE;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAC9D,IAAI,OAAOD,KAAI,CAACT,aAAa,KAAK,QAAQ,EAAE;QACxC,OAAOS,KAAI,CAACT,aAAa,CAACK,WAAW,CAACC,QAAQ,EAAEC,QAAQ,EAAEjB,OAAO,EAAEkB,iBAAiB,CAAC;MACzF;MACA,IAAIF,QAAQ,CAACK,UAAU,CAAC,CAAC,IAAIJ,QAAQ,CAACI,UAAU,CAAC,CAAC,EAAE;QAChD;QACA,OAAO;UACHC,OAAO,EAAE,EAAE;UACXC,SAAS,EAAE,IAAI;UACfC,SAAS,EAAE,KAAK;UAChBC,KAAK,EAAE;QACX,CAAC;MACL;MACA;MACA,IAAIT,QAAQ,CAACU,YAAY,CAAC,CAAC,KAAK,CAAC,IAAIV,QAAQ,CAACW,gBAAgB,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;QACrE,IAAIV,QAAQ,CAACS,YAAY,CAAC,CAAC,KAAK,CAAC,IAAIT,QAAQ,CAACU,gBAAgB,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;UACrE,OAAO;YACHL,OAAO,EAAE,EAAE;YACXC,SAAS,EAAE,IAAI;YACfC,SAAS,EAAE,KAAK;YAChBC,KAAK,EAAE;UACX,CAAC;QACL;QACA,OAAO;UACHH,OAAO,EAAE,CACL,IAAI/B,wBAAwB,CAAC,IAAID,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,IAAIA,SAAS,CAAC,CAAC,EAAE2B,QAAQ,CAACS,YAAY,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAC7F,IAAIlC,YAAY,CAACwB,QAAQ,CAACY,iBAAiB,CAAC,CAAC,EAAEX,QAAQ,CAACW,iBAAiB,CAAC,CAAC,CAAC,CAC/E,CAAC,CACL;UACDL,SAAS,EAAE,KAAK;UAChBC,SAAS,EAAE,KAAK;UAChBC,KAAK,EAAE;QACX,CAAC;MACL;MACA,MAAMI,MAAM,GAAGC,IAAI,CAACC,SAAS,CAAC,CAACf,QAAQ,CAACgB,GAAG,CAACC,QAAQ,CAAC,CAAC,EAAEhB,QAAQ,CAACe,GAAG,CAACC,QAAQ,CAAC,CAAC,CAAC,CAAC;MACjF,MAAMC,OAAO,GAAGJ,IAAI,CAACC,SAAS,CAAC,CAACf,QAAQ,CAACmB,EAAE,EAAElB,QAAQ,CAACkB,EAAE,EAAEnB,QAAQ,CAACoB,uBAAuB,CAAC,CAAC,EAAEnB,QAAQ,CAACmB,uBAAuB,CAAC,CAAC,EAAEN,IAAI,CAACC,SAAS,CAAC/B,OAAO,CAAC,CAAC,CAAC;MAC3J,MAAM9B,CAAC,GAAGc,iCAAiC,CAACmB,SAAS,CAACkC,GAAG,CAACR,MAAM,CAAC;MACjE,IAAI3D,CAAC,IAAIA,CAAC,CAACgE,OAAO,KAAKA,OAAO,EAAE;QAC5B,OAAOhE,CAAC,CAACoE,MAAM;MACnB;MACA,MAAMC,EAAE,GAAGlD,SAAS,CAACmD,MAAM,CAAC,CAAC;MAC7B,MAAMF,MAAM,SAASnB,KAAI,CAACd,mBAAmB,CAACU,WAAW,CAACC,QAAQ,CAACgB,GAAG,EAAEf,QAAQ,CAACe,GAAG,EAAEhC,OAAO,EAAEmB,KAAI,CAACT,aAAa,CAAC;MAClH,MAAM+B,MAAM,GAAGF,EAAE,CAACG,OAAO,CAAC,CAAC;MAC3BvB,KAAI,CAACb,gBAAgB,CAACqC,UAAU,CAAC,wBAAwB,EAAE;QACvDF,MAAM;QACNG,QAAQ,EAAEN,MAAM,EAAEd,SAAS,IAAI,IAAI;QACnCqB,aAAa,EAAE7C,OAAO,CAAC8C,YAAY,GAAIR,MAAM,EAAEb,KAAK,CAACrD,MAAM,IAAI,CAAC,GAAI,CAAC;MACzE,CAAC,CAAC;MACF,IAAI8C,iBAAiB,CAAC6B,uBAAuB,EAAE;QAC3C;QACA,OAAO;UACHzB,OAAO,EAAE,EAAE;UACXC,SAAS,EAAE,KAAK;UAChBC,SAAS,EAAE,IAAI;UACfC,KAAK,EAAE;QACX,CAAC;MACL;MACA,IAAI,CAACa,MAAM,EAAE;QACT,MAAM,IAAIU,KAAK,CAAC,0BAA0B,CAAC;MAC/C;MACA;MACA,IAAIhE,iCAAiC,CAACmB,SAAS,CAAC8C,IAAI,GAAG,EAAE,EAAE;QACvDjE,iCAAiC,CAACmB,SAAS,CAAC+C,MAAM,CAAClE,iCAAiC,CAACmB,SAAS,CAACgD,IAAI,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC;MACvH;MACArE,iCAAiC,CAACmB,SAAS,CAACmD,GAAG,CAACzB,MAAM,EAAE;QAAES,MAAM;QAAEJ;MAAQ,CAAC,CAAC;MAC5E,OAAOI,MAAM;IAAC;EAClB;EACAzB,UAAUA,CAAC0C,UAAU,EAAE;IACnB,IAAIC,SAAS,GAAG,KAAK;IACrB,IAAID,UAAU,CAAC7C,aAAa,EAAE;MAC1B,IAAI,IAAI,CAACA,aAAa,KAAK6C,UAAU,CAAC7C,aAAa,EAAE;QACjD,IAAI,CAACC,oCAAoC,EAAEG,OAAO,CAAC,CAAC;QACpD,IAAI,CAACH,oCAAoC,GAAGC,SAAS;QACrD,IAAI,CAACF,aAAa,GAAG6C,UAAU,CAAC7C,aAAa;QAC7C,IAAI,OAAO6C,UAAU,CAAC7C,aAAa,KAAK,QAAQ,EAAE;UAC9C,IAAI,CAACC,oCAAoC,GAAG4C,UAAU,CAAC7C,aAAa,CAACF,WAAW,CAAC,MAAM,IAAI,CAACD,uBAAuB,CAACkD,IAAI,CAAC,CAAC,CAAC;QAC/H;QACAD,SAAS,GAAG,IAAI;MACpB;IACJ;IACA,IAAIA,SAAS,EAAE;MACX,IAAI,CAACjD,uBAAuB,CAACkD,IAAI,CAAC,CAAC;IACvC;EACJ;AACJ,CAAC;AACDvD,+BAA+B,GAAGlB,iCAAiC,GAAGnB,UAAU,CAAC,CAC7EgB,OAAO,CAAC,CAAC,EAAEY,oBAAoB,CAAC,EAChCZ,OAAO,CAAC,CAAC,EAAEa,iBAAiB,CAAC,CAChC,EAAEQ,+BAA+B,CAAC;AACnC,SAASA,+BAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}