{"ast": null, "code": "import _asyncToGenerator from \"C:/console/aava-ui-web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { strictEquals } from '../equals.js';\nimport { DebugNameData, getFunctionName } from './debugName.js';\nimport { getLogger } from './logging.js';\nlet _recomputeInitiallyAndOnChange;\nexport function _setRecomputeInitiallyAndOnChange(recomputeInitiallyAndOnChange) {\n  _recomputeInitiallyAndOnChange = recomputeInitiallyAndOnChange;\n}\nlet _keepObserved;\nexport function _setKeepObserved(keepObserved) {\n  _keepObserved = keepObserved;\n}\nlet _derived;\n/**\n * @internal\n * This is to allow splitting files.\n*/\nexport function _setDerivedOpts(derived) {\n  _derived = derived;\n}\nexport class ConvenientObservable {\n  get TChange() {\n    return null;\n  }\n  reportChanges() {\n    this.get();\n  }\n  /** @sealed */\n  read(reader) {\n    if (reader) {\n      return reader.readObservable(this);\n    } else {\n      return this.get();\n    }\n  }\n  map(fnOrOwner, fnOrUndefined) {\n    const owner = fnOrUndefined === undefined ? undefined : fnOrOwner;\n    const fn = fnOrUndefined === undefined ? fnOrOwner : fnOrUndefined;\n    return _derived({\n      owner,\n      debugName: () => {\n        const name = getFunctionName(fn);\n        if (name !== undefined) {\n          return name;\n        }\n        // regexp to match `x => x.y` or `x => x?.y` where x and y can be arbitrary identifiers (uses backref):\n        const regexp = /^\\s*\\(?\\s*([a-zA-Z_$][a-zA-Z_$0-9]*)\\s*\\)?\\s*=>\\s*\\1(?:\\??)\\.([a-zA-Z_$][a-zA-Z_$0-9]*)\\s*$/;\n        const match = regexp.exec(fn.toString());\n        if (match) {\n          return `${this.debugName}.${match[2]}`;\n        }\n        if (!owner) {\n          return `${this.debugName} (mapped)`;\n        }\n        return undefined;\n      },\n      debugReferenceFn: fn\n    }, reader => fn(this.read(reader), reader));\n  }\n  /**\n   * @sealed\n   * Converts an observable of an observable value into a direct observable of the value.\n  */\n  flatten() {\n    return _derived({\n      owner: undefined,\n      debugName: () => `${this.debugName} (flattened)`\n    }, reader => this.read(reader).read(reader));\n  }\n  recomputeInitiallyAndOnChange(store, handleValue) {\n    store.add(_recomputeInitiallyAndOnChange(this, handleValue));\n    return this;\n  }\n  /**\n   * Ensures that this observable is observed. This keeps the cache alive.\n   * However, in case of deriveds, it does not force eager evaluation (only when the value is read/get).\n   * Use `recomputeInitiallyAndOnChange` for eager evaluation.\n   */\n  keepObserved(store) {\n    store.add(_keepObserved(this));\n    return this;\n  }\n}\nexport class BaseObservable extends ConvenientObservable {\n  constructor() {\n    super(...arguments);\n    this.observers = new Set();\n  }\n  addObserver(observer) {\n    const len = this.observers.size;\n    this.observers.add(observer);\n    if (len === 0) {\n      this.onFirstObserverAdded();\n    }\n  }\n  removeObserver(observer) {\n    const deleted = this.observers.delete(observer);\n    if (deleted && this.observers.size === 0) {\n      this.onLastObserverRemoved();\n    }\n  }\n  onFirstObserverAdded() {}\n  onLastObserverRemoved() {}\n}\n/**\n * Starts a transaction in which many observables can be changed at once.\n * {@link fn} should start with a JS Doc using `@description` to give the transaction a debug name.\n * Reaction run on demand or when the transaction ends.\n */\nexport function transaction(fn, getDebugName) {\n  const tx = new TransactionImpl(fn, getDebugName);\n  try {\n    fn(tx);\n  } finally {\n    tx.finish();\n  }\n}\nlet _globalTransaction = undefined;\nexport function globalTransaction(fn) {\n  if (_globalTransaction) {\n    fn(_globalTransaction);\n  } else {\n    const tx = new TransactionImpl(fn, undefined);\n    _globalTransaction = tx;\n    try {\n      fn(tx);\n    } finally {\n      tx.finish(); // During finish, more actions might be added to the transaction.\n      // Which is why we only clear the global transaction after finish.\n      _globalTransaction = undefined;\n    }\n  }\n}\nexport function asyncTransaction(_x, _x2) {\n  return _asyncTransaction.apply(this, arguments);\n}\n/**\n * Allows to chain transactions.\n */\nfunction _asyncTransaction() {\n  _asyncTransaction = _asyncToGenerator(function* (fn, getDebugName) {\n    const tx = new TransactionImpl(fn, getDebugName);\n    try {\n      yield fn(tx);\n    } finally {\n      tx.finish();\n    }\n  });\n  return _asyncTransaction.apply(this, arguments);\n}\nexport function subtransaction(tx, fn, getDebugName) {\n  if (!tx) {\n    transaction(fn, getDebugName);\n  } else {\n    fn(tx);\n  }\n}\nexport class TransactionImpl {\n  constructor(_fn, _getDebugName) {\n    this._fn = _fn;\n    this._getDebugName = _getDebugName;\n    this.updatingObservers = [];\n    getLogger()?.handleBeginTransaction(this);\n  }\n  getDebugName() {\n    if (this._getDebugName) {\n      return this._getDebugName();\n    }\n    return getFunctionName(this._fn);\n  }\n  updateObserver(observer, observable) {\n    // When this gets called while finish is active, they will still get considered\n    this.updatingObservers.push({\n      observer,\n      observable\n    });\n    observer.beginUpdate(observable);\n  }\n  finish() {\n    const updatingObservers = this.updatingObservers;\n    for (let i = 0; i < updatingObservers.length; i++) {\n      const {\n        observer,\n        observable\n      } = updatingObservers[i];\n      observer.endUpdate(observable);\n    }\n    // Prevent anyone from updating observers from now on.\n    this.updatingObservers = null;\n    getLogger()?.handleEndTransaction();\n  }\n}\nexport function observableValue(nameOrOwner, initialValue) {\n  let debugNameData;\n  if (typeof nameOrOwner === 'string') {\n    debugNameData = new DebugNameData(undefined, nameOrOwner, undefined);\n  } else {\n    debugNameData = new DebugNameData(nameOrOwner, undefined, undefined);\n  }\n  return new ObservableValue(debugNameData, initialValue, strictEquals);\n}\nexport class ObservableValue extends BaseObservable {\n  get debugName() {\n    return this._debugNameData.getDebugName(this) ?? 'ObservableValue';\n  }\n  constructor(_debugNameData, initialValue, _equalityComparator) {\n    super();\n    this._debugNameData = _debugNameData;\n    this._equalityComparator = _equalityComparator;\n    this._value = initialValue;\n  }\n  get() {\n    return this._value;\n  }\n  set(value, tx, change) {\n    if (change === undefined && this._equalityComparator(this._value, value)) {\n      return;\n    }\n    let _tx;\n    if (!tx) {\n      tx = _tx = new TransactionImpl(() => {}, () => `Setting ${this.debugName}`);\n    }\n    try {\n      const oldValue = this._value;\n      this._setValue(value);\n      getLogger()?.handleObservableChanged(this, {\n        oldValue,\n        newValue: value,\n        change,\n        didChange: true,\n        hadValue: true\n      });\n      for (const observer of this.observers) {\n        tx.updateObserver(observer, this);\n        observer.handleChange(this, change);\n      }\n    } finally {\n      if (_tx) {\n        _tx.finish();\n      }\n    }\n  }\n  toString() {\n    return `${this.debugName}: ${this._value}`;\n  }\n  _setValue(newValue) {\n    this._value = newValue;\n  }\n}\n/**\n * A disposable observable. When disposed, its value is also disposed.\n * When a new value is set, the previous value is disposed.\n */\nexport function disposableObservableValue(nameOrOwner, initialValue) {\n  let debugNameData;\n  if (typeof nameOrOwner === 'string') {\n    debugNameData = new DebugNameData(undefined, nameOrOwner, undefined);\n  } else {\n    debugNameData = new DebugNameData(nameOrOwner, undefined, undefined);\n  }\n  return new DisposableObservableValue(debugNameData, initialValue, strictEquals);\n}\nexport class DisposableObservableValue extends ObservableValue {\n  _setValue(newValue) {\n    if (this._value === newValue) {\n      return;\n    }\n    if (this._value) {\n      this._value.dispose();\n    }\n    this._value = newValue;\n  }\n  dispose() {\n    this._value?.dispose();\n  }\n}", "map": {"version": 3, "names": ["strictEquals", "DebugNameData", "getFunctionName", "<PERSON><PERSON><PERSON><PERSON>", "_recomputeInitiallyAndOnChange", "_setRecomputeInitiallyAndOnChange", "recomputeInitiallyAndOnChange", "_keepObserved", "_setKeepObserved", "keepObserved", "_derived", "_setDerivedOpts", "derived", "ConvenientObservable", "TChange", "reportChanges", "get", "read", "reader", "readObservable", "map", "fnOrOwner", "fnOrUndefined", "owner", "undefined", "fn", "debugName", "name", "regexp", "match", "exec", "toString", "debugReferenceFn", "flatten", "store", "handleValue", "add", "BaseObservable", "constructor", "arguments", "observers", "Set", "addObserver", "observer", "len", "size", "onFirstObserverAdded", "removeObserver", "deleted", "delete", "onLastObserverRemoved", "transaction", "getDebugName", "tx", "TransactionImpl", "finish", "_globalTransaction", "globalTransaction", "asyncTransaction", "_x", "_x2", "_asyncTransaction", "apply", "_asyncToGenerator", "subtransaction", "_fn", "_getDebugName", "updatingObservers", "handleBeginTransaction", "updateObserver", "observable", "push", "beginUpdate", "i", "length", "endUpdate", "handleEndTransaction", "observableValue", "name<PERSON>r<PERSON><PERSON><PERSON>", "initialValue", "debugNameData", "ObservableValue", "_debugNameData", "_equalityComparator", "_value", "set", "value", "change", "_tx", "oldValue", "_setValue", "handleObservableChanged", "newValue", "<PERSON><PERSON><PERSON><PERSON>", "hadValue", "handleChange", "disposableObservableValue", "DisposableObservableValue", "dispose"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/common/observableInternal/base.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { strictEquals } from '../equals.js';\nimport { DebugNameData, getFunctionName } from './debugName.js';\nimport { getLogger } from './logging.js';\nlet _recomputeInitiallyAndOnChange;\nexport function _setRecomputeInitiallyAndOnChange(recomputeInitiallyAndOnChange) {\n    _recomputeInitiallyAndOnChange = recomputeInitiallyAndOnChange;\n}\nlet _keepObserved;\nexport function _setKeepObserved(keepObserved) {\n    _keepObserved = keepObserved;\n}\nlet _derived;\n/**\n * @internal\n * This is to allow splitting files.\n*/\nexport function _setDerivedOpts(derived) {\n    _derived = derived;\n}\nexport class ConvenientObservable {\n    get TChange() { return null; }\n    reportChanges() {\n        this.get();\n    }\n    /** @sealed */\n    read(reader) {\n        if (reader) {\n            return reader.readObservable(this);\n        }\n        else {\n            return this.get();\n        }\n    }\n    map(fnOrOwner, fnOrUndefined) {\n        const owner = fnOrUndefined === undefined ? undefined : fnOrOwner;\n        const fn = fnOrUndefined === undefined ? fnOrOwner : fnOrUndefined;\n        return _derived({\n            owner,\n            debugName: () => {\n                const name = getFunctionName(fn);\n                if (name !== undefined) {\n                    return name;\n                }\n                // regexp to match `x => x.y` or `x => x?.y` where x and y can be arbitrary identifiers (uses backref):\n                const regexp = /^\\s*\\(?\\s*([a-zA-Z_$][a-zA-Z_$0-9]*)\\s*\\)?\\s*=>\\s*\\1(?:\\??)\\.([a-zA-Z_$][a-zA-Z_$0-9]*)\\s*$/;\n                const match = regexp.exec(fn.toString());\n                if (match) {\n                    return `${this.debugName}.${match[2]}`;\n                }\n                if (!owner) {\n                    return `${this.debugName} (mapped)`;\n                }\n                return undefined;\n            },\n            debugReferenceFn: fn,\n        }, (reader) => fn(this.read(reader), reader));\n    }\n    /**\n     * @sealed\n     * Converts an observable of an observable value into a direct observable of the value.\n    */\n    flatten() {\n        return _derived({\n            owner: undefined,\n            debugName: () => `${this.debugName} (flattened)`,\n        }, (reader) => this.read(reader).read(reader));\n    }\n    recomputeInitiallyAndOnChange(store, handleValue) {\n        store.add(_recomputeInitiallyAndOnChange(this, handleValue));\n        return this;\n    }\n    /**\n     * Ensures that this observable is observed. This keeps the cache alive.\n     * However, in case of deriveds, it does not force eager evaluation (only when the value is read/get).\n     * Use `recomputeInitiallyAndOnChange` for eager evaluation.\n     */\n    keepObserved(store) {\n        store.add(_keepObserved(this));\n        return this;\n    }\n}\nexport class BaseObservable extends ConvenientObservable {\n    constructor() {\n        super(...arguments);\n        this.observers = new Set();\n    }\n    addObserver(observer) {\n        const len = this.observers.size;\n        this.observers.add(observer);\n        if (len === 0) {\n            this.onFirstObserverAdded();\n        }\n    }\n    removeObserver(observer) {\n        const deleted = this.observers.delete(observer);\n        if (deleted && this.observers.size === 0) {\n            this.onLastObserverRemoved();\n        }\n    }\n    onFirstObserverAdded() { }\n    onLastObserverRemoved() { }\n}\n/**\n * Starts a transaction in which many observables can be changed at once.\n * {@link fn} should start with a JS Doc using `@description` to give the transaction a debug name.\n * Reaction run on demand or when the transaction ends.\n */\nexport function transaction(fn, getDebugName) {\n    const tx = new TransactionImpl(fn, getDebugName);\n    try {\n        fn(tx);\n    }\n    finally {\n        tx.finish();\n    }\n}\nlet _globalTransaction = undefined;\nexport function globalTransaction(fn) {\n    if (_globalTransaction) {\n        fn(_globalTransaction);\n    }\n    else {\n        const tx = new TransactionImpl(fn, undefined);\n        _globalTransaction = tx;\n        try {\n            fn(tx);\n        }\n        finally {\n            tx.finish(); // During finish, more actions might be added to the transaction.\n            // Which is why we only clear the global transaction after finish.\n            _globalTransaction = undefined;\n        }\n    }\n}\nexport async function asyncTransaction(fn, getDebugName) {\n    const tx = new TransactionImpl(fn, getDebugName);\n    try {\n        await fn(tx);\n    }\n    finally {\n        tx.finish();\n    }\n}\n/**\n * Allows to chain transactions.\n */\nexport function subtransaction(tx, fn, getDebugName) {\n    if (!tx) {\n        transaction(fn, getDebugName);\n    }\n    else {\n        fn(tx);\n    }\n}\nexport class TransactionImpl {\n    constructor(_fn, _getDebugName) {\n        this._fn = _fn;\n        this._getDebugName = _getDebugName;\n        this.updatingObservers = [];\n        getLogger()?.handleBeginTransaction(this);\n    }\n    getDebugName() {\n        if (this._getDebugName) {\n            return this._getDebugName();\n        }\n        return getFunctionName(this._fn);\n    }\n    updateObserver(observer, observable) {\n        // When this gets called while finish is active, they will still get considered\n        this.updatingObservers.push({ observer, observable });\n        observer.beginUpdate(observable);\n    }\n    finish() {\n        const updatingObservers = this.updatingObservers;\n        for (let i = 0; i < updatingObservers.length; i++) {\n            const { observer, observable } = updatingObservers[i];\n            observer.endUpdate(observable);\n        }\n        // Prevent anyone from updating observers from now on.\n        this.updatingObservers = null;\n        getLogger()?.handleEndTransaction();\n    }\n}\nexport function observableValue(nameOrOwner, initialValue) {\n    let debugNameData;\n    if (typeof nameOrOwner === 'string') {\n        debugNameData = new DebugNameData(undefined, nameOrOwner, undefined);\n    }\n    else {\n        debugNameData = new DebugNameData(nameOrOwner, undefined, undefined);\n    }\n    return new ObservableValue(debugNameData, initialValue, strictEquals);\n}\nexport class ObservableValue extends BaseObservable {\n    get debugName() {\n        return this._debugNameData.getDebugName(this) ?? 'ObservableValue';\n    }\n    constructor(_debugNameData, initialValue, _equalityComparator) {\n        super();\n        this._debugNameData = _debugNameData;\n        this._equalityComparator = _equalityComparator;\n        this._value = initialValue;\n    }\n    get() {\n        return this._value;\n    }\n    set(value, tx, change) {\n        if (change === undefined && this._equalityComparator(this._value, value)) {\n            return;\n        }\n        let _tx;\n        if (!tx) {\n            tx = _tx = new TransactionImpl(() => { }, () => `Setting ${this.debugName}`);\n        }\n        try {\n            const oldValue = this._value;\n            this._setValue(value);\n            getLogger()?.handleObservableChanged(this, { oldValue, newValue: value, change, didChange: true, hadValue: true });\n            for (const observer of this.observers) {\n                tx.updateObserver(observer, this);\n                observer.handleChange(this, change);\n            }\n        }\n        finally {\n            if (_tx) {\n                _tx.finish();\n            }\n        }\n    }\n    toString() {\n        return `${this.debugName}: ${this._value}`;\n    }\n    _setValue(newValue) {\n        this._value = newValue;\n    }\n}\n/**\n * A disposable observable. When disposed, its value is also disposed.\n * When a new value is set, the previous value is disposed.\n */\nexport function disposableObservableValue(nameOrOwner, initialValue) {\n    let debugNameData;\n    if (typeof nameOrOwner === 'string') {\n        debugNameData = new DebugNameData(undefined, nameOrOwner, undefined);\n    }\n    else {\n        debugNameData = new DebugNameData(nameOrOwner, undefined, undefined);\n    }\n    return new DisposableObservableValue(debugNameData, initialValue, strictEquals);\n}\nexport class DisposableObservableValue extends ObservableValue {\n    _setValue(newValue) {\n        if (this._value === newValue) {\n            return;\n        }\n        if (this._value) {\n            this._value.dispose();\n        }\n        this._value = newValue;\n    }\n    dispose() {\n        this._value?.dispose();\n    }\n}\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA,SAASA,YAAY,QAAQ,cAAc;AAC3C,SAASC,aAAa,EAAEC,eAAe,QAAQ,gBAAgB;AAC/D,SAASC,SAAS,QAAQ,cAAc;AACxC,IAAIC,8BAA8B;AAClC,OAAO,SAASC,iCAAiCA,CAACC,6BAA6B,EAAE;EAC7EF,8BAA8B,GAAGE,6BAA6B;AAClE;AACA,IAAIC,aAAa;AACjB,OAAO,SAASC,gBAAgBA,CAACC,YAAY,EAAE;EAC3CF,aAAa,GAAGE,YAAY;AAChC;AACA,IAAIC,QAAQ;AACZ;AACA;AACA;AACA;AACA,OAAO,SAASC,eAAeA,CAACC,OAAO,EAAE;EACrCF,QAAQ,GAAGE,OAAO;AACtB;AACA,OAAO,MAAMC,oBAAoB,CAAC;EAC9B,IAAIC,OAAOA,CAAA,EAAG;IAAE,OAAO,IAAI;EAAE;EAC7BC,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACC,GAAG,CAAC,CAAC;EACd;EACA;EACAC,IAAIA,CAACC,MAAM,EAAE;IACT,IAAIA,MAAM,EAAE;MACR,OAAOA,MAAM,CAACC,cAAc,CAAC,IAAI,CAAC;IACtC,CAAC,MACI;MACD,OAAO,IAAI,CAACH,GAAG,CAAC,CAAC;IACrB;EACJ;EACAI,GAAGA,CAACC,SAAS,EAAEC,aAAa,EAAE;IAC1B,MAAMC,KAAK,GAAGD,aAAa,KAAKE,SAAS,GAAGA,SAAS,GAAGH,SAAS;IACjE,MAAMI,EAAE,GAAGH,aAAa,KAAKE,SAAS,GAAGH,SAAS,GAAGC,aAAa;IAClE,OAAOZ,QAAQ,CAAC;MACZa,KAAK;MACLG,SAAS,EAAEA,CAAA,KAAM;QACb,MAAMC,IAAI,GAAGzB,eAAe,CAACuB,EAAE,CAAC;QAChC,IAAIE,IAAI,KAAKH,SAAS,EAAE;UACpB,OAAOG,IAAI;QACf;QACA;QACA,MAAMC,MAAM,GAAG,6FAA6F;QAC5G,MAAMC,KAAK,GAAGD,MAAM,CAACE,IAAI,CAACL,EAAE,CAACM,QAAQ,CAAC,CAAC,CAAC;QACxC,IAAIF,KAAK,EAAE;UACP,OAAO,GAAG,IAAI,CAACH,SAAS,IAAIG,KAAK,CAAC,CAAC,CAAC,EAAE;QAC1C;QACA,IAAI,CAACN,KAAK,EAAE;UACR,OAAO,GAAG,IAAI,CAACG,SAAS,WAAW;QACvC;QACA,OAAOF,SAAS;MACpB,CAAC;MACDQ,gBAAgB,EAAEP;IACtB,CAAC,EAAGP,MAAM,IAAKO,EAAE,CAAC,IAAI,CAACR,IAAI,CAACC,MAAM,CAAC,EAAEA,MAAM,CAAC,CAAC;EACjD;EACA;AACJ;AACA;AACA;EACIe,OAAOA,CAAA,EAAG;IACN,OAAOvB,QAAQ,CAAC;MACZa,KAAK,EAAEC,SAAS;MAChBE,SAAS,EAAEA,CAAA,KAAM,GAAG,IAAI,CAACA,SAAS;IACtC,CAAC,EAAGR,MAAM,IAAK,IAAI,CAACD,IAAI,CAACC,MAAM,CAAC,CAACD,IAAI,CAACC,MAAM,CAAC,CAAC;EAClD;EACAZ,6BAA6BA,CAAC4B,KAAK,EAAEC,WAAW,EAAE;IAC9CD,KAAK,CAACE,GAAG,CAAChC,8BAA8B,CAAC,IAAI,EAAE+B,WAAW,CAAC,CAAC;IAC5D,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACI1B,YAAYA,CAACyB,KAAK,EAAE;IAChBA,KAAK,CAACE,GAAG,CAAC7B,aAAa,CAAC,IAAI,CAAC,CAAC;IAC9B,OAAO,IAAI;EACf;AACJ;AACA,OAAO,MAAM8B,cAAc,SAASxB,oBAAoB,CAAC;EACrDyB,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACC,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC9B;EACAC,WAAWA,CAACC,QAAQ,EAAE;IAClB,MAAMC,GAAG,GAAG,IAAI,CAACJ,SAAS,CAACK,IAAI;IAC/B,IAAI,CAACL,SAAS,CAACJ,GAAG,CAACO,QAAQ,CAAC;IAC5B,IAAIC,GAAG,KAAK,CAAC,EAAE;MACX,IAAI,CAACE,oBAAoB,CAAC,CAAC;IAC/B;EACJ;EACAC,cAAcA,CAACJ,QAAQ,EAAE;IACrB,MAAMK,OAAO,GAAG,IAAI,CAACR,SAAS,CAACS,MAAM,CAACN,QAAQ,CAAC;IAC/C,IAAIK,OAAO,IAAI,IAAI,CAACR,SAAS,CAACK,IAAI,KAAK,CAAC,EAAE;MACtC,IAAI,CAACK,qBAAqB,CAAC,CAAC;IAChC;EACJ;EACAJ,oBAAoBA,CAAA,EAAG,CAAE;EACzBI,qBAAqBA,CAAA,EAAG,CAAE;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAAC1B,EAAE,EAAE2B,YAAY,EAAE;EAC1C,MAAMC,EAAE,GAAG,IAAIC,eAAe,CAAC7B,EAAE,EAAE2B,YAAY,CAAC;EAChD,IAAI;IACA3B,EAAE,CAAC4B,EAAE,CAAC;EACV,CAAC,SACO;IACJA,EAAE,CAACE,MAAM,CAAC,CAAC;EACf;AACJ;AACA,IAAIC,kBAAkB,GAAGhC,SAAS;AAClC,OAAO,SAASiC,iBAAiBA,CAAChC,EAAE,EAAE;EAClC,IAAI+B,kBAAkB,EAAE;IACpB/B,EAAE,CAAC+B,kBAAkB,CAAC;EAC1B,CAAC,MACI;IACD,MAAMH,EAAE,GAAG,IAAIC,eAAe,CAAC7B,EAAE,EAAED,SAAS,CAAC;IAC7CgC,kBAAkB,GAAGH,EAAE;IACvB,IAAI;MACA5B,EAAE,CAAC4B,EAAE,CAAC;IACV,CAAC,SACO;MACJA,EAAE,CAACE,MAAM,CAAC,CAAC,CAAC,CAAC;MACb;MACAC,kBAAkB,GAAGhC,SAAS;IAClC;EACJ;AACJ;AACA,gBAAsBkC,gBAAgBA,CAAAC,EAAA,EAAAC,GAAA;EAAA,OAAAC,iBAAA,CAAAC,KAAA,OAAAvB,SAAA;AAAA;AAStC;AACA;AACA;AAFA,SAAAsB,kBAAA;EAAAA,iBAAA,GAAAE,iBAAA,CATO,WAAgCtC,EAAE,EAAE2B,YAAY,EAAE;IACrD,MAAMC,EAAE,GAAG,IAAIC,eAAe,CAAC7B,EAAE,EAAE2B,YAAY,CAAC;IAChD,IAAI;MACA,MAAM3B,EAAE,CAAC4B,EAAE,CAAC;IAChB,CAAC,SACO;MACJA,EAAE,CAACE,MAAM,CAAC,CAAC;IACf;EACJ,CAAC;EAAA,OAAAM,iBAAA,CAAAC,KAAA,OAAAvB,SAAA;AAAA;AAID,OAAO,SAASyB,cAAcA,CAACX,EAAE,EAAE5B,EAAE,EAAE2B,YAAY,EAAE;EACjD,IAAI,CAACC,EAAE,EAAE;IACLF,WAAW,CAAC1B,EAAE,EAAE2B,YAAY,CAAC;EACjC,CAAC,MACI;IACD3B,EAAE,CAAC4B,EAAE,CAAC;EACV;AACJ;AACA,OAAO,MAAMC,eAAe,CAAC;EACzBhB,WAAWA,CAAC2B,GAAG,EAAEC,aAAa,EAAE;IAC5B,IAAI,CAACD,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,iBAAiB,GAAG,EAAE;IAC3BhE,SAAS,CAAC,CAAC,EAAEiE,sBAAsB,CAAC,IAAI,CAAC;EAC7C;EACAhB,YAAYA,CAAA,EAAG;IACX,IAAI,IAAI,CAACc,aAAa,EAAE;MACpB,OAAO,IAAI,CAACA,aAAa,CAAC,CAAC;IAC/B;IACA,OAAOhE,eAAe,CAAC,IAAI,CAAC+D,GAAG,CAAC;EACpC;EACAI,cAAcA,CAAC1B,QAAQ,EAAE2B,UAAU,EAAE;IACjC;IACA,IAAI,CAACH,iBAAiB,CAACI,IAAI,CAAC;MAAE5B,QAAQ;MAAE2B;IAAW,CAAC,CAAC;IACrD3B,QAAQ,CAAC6B,WAAW,CAACF,UAAU,CAAC;EACpC;EACAf,MAAMA,CAAA,EAAG;IACL,MAAMY,iBAAiB,GAAG,IAAI,CAACA,iBAAiB;IAChD,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,iBAAiB,CAACO,MAAM,EAAED,CAAC,EAAE,EAAE;MAC/C,MAAM;QAAE9B,QAAQ;QAAE2B;MAAW,CAAC,GAAGH,iBAAiB,CAACM,CAAC,CAAC;MACrD9B,QAAQ,CAACgC,SAAS,CAACL,UAAU,CAAC;IAClC;IACA;IACA,IAAI,CAACH,iBAAiB,GAAG,IAAI;IAC7BhE,SAAS,CAAC,CAAC,EAAEyE,oBAAoB,CAAC,CAAC;EACvC;AACJ;AACA,OAAO,SAASC,eAAeA,CAACC,WAAW,EAAEC,YAAY,EAAE;EACvD,IAAIC,aAAa;EACjB,IAAI,OAAOF,WAAW,KAAK,QAAQ,EAAE;IACjCE,aAAa,GAAG,IAAI/E,aAAa,CAACuB,SAAS,EAAEsD,WAAW,EAAEtD,SAAS,CAAC;EACxE,CAAC,MACI;IACDwD,aAAa,GAAG,IAAI/E,aAAa,CAAC6E,WAAW,EAAEtD,SAAS,EAAEA,SAAS,CAAC;EACxE;EACA,OAAO,IAAIyD,eAAe,CAACD,aAAa,EAAED,YAAY,EAAE/E,YAAY,CAAC;AACzE;AACA,OAAO,MAAMiF,eAAe,SAAS5C,cAAc,CAAC;EAChD,IAAIX,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACwD,cAAc,CAAC9B,YAAY,CAAC,IAAI,CAAC,IAAI,iBAAiB;EACtE;EACAd,WAAWA,CAAC4C,cAAc,EAAEH,YAAY,EAAEI,mBAAmB,EAAE;IAC3D,KAAK,CAAC,CAAC;IACP,IAAI,CAACD,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACC,MAAM,GAAGL,YAAY;EAC9B;EACA/D,GAAGA,CAAA,EAAG;IACF,OAAO,IAAI,CAACoE,MAAM;EACtB;EACAC,GAAGA,CAACC,KAAK,EAAEjC,EAAE,EAAEkC,MAAM,EAAE;IACnB,IAAIA,MAAM,KAAK/D,SAAS,IAAI,IAAI,CAAC2D,mBAAmB,CAAC,IAAI,CAACC,MAAM,EAAEE,KAAK,CAAC,EAAE;MACtE;IACJ;IACA,IAAIE,GAAG;IACP,IAAI,CAACnC,EAAE,EAAE;MACLA,EAAE,GAAGmC,GAAG,GAAG,IAAIlC,eAAe,CAAC,MAAM,CAAE,CAAC,EAAE,MAAM,WAAW,IAAI,CAAC5B,SAAS,EAAE,CAAC;IAChF;IACA,IAAI;MACA,MAAM+D,QAAQ,GAAG,IAAI,CAACL,MAAM;MAC5B,IAAI,CAACM,SAAS,CAACJ,KAAK,CAAC;MACrBnF,SAAS,CAAC,CAAC,EAAEwF,uBAAuB,CAAC,IAAI,EAAE;QAAEF,QAAQ;QAAEG,QAAQ,EAAEN,KAAK;QAAEC,MAAM;QAAEM,SAAS,EAAE,IAAI;QAAEC,QAAQ,EAAE;MAAK,CAAC,CAAC;MAClH,KAAK,MAAMnD,QAAQ,IAAI,IAAI,CAACH,SAAS,EAAE;QACnCa,EAAE,CAACgB,cAAc,CAAC1B,QAAQ,EAAE,IAAI,CAAC;QACjCA,QAAQ,CAACoD,YAAY,CAAC,IAAI,EAAER,MAAM,CAAC;MACvC;IACJ,CAAC,SACO;MACJ,IAAIC,GAAG,EAAE;QACLA,GAAG,CAACjC,MAAM,CAAC,CAAC;MAChB;IACJ;EACJ;EACAxB,QAAQA,CAAA,EAAG;IACP,OAAO,GAAG,IAAI,CAACL,SAAS,KAAK,IAAI,CAAC0D,MAAM,EAAE;EAC9C;EACAM,SAASA,CAACE,QAAQ,EAAE;IAChB,IAAI,CAACR,MAAM,GAAGQ,QAAQ;EAC1B;AACJ;AACA;AACA;AACA;AACA;AACA,OAAO,SAASI,yBAAyBA,CAAClB,WAAW,EAAEC,YAAY,EAAE;EACjE,IAAIC,aAAa;EACjB,IAAI,OAAOF,WAAW,KAAK,QAAQ,EAAE;IACjCE,aAAa,GAAG,IAAI/E,aAAa,CAACuB,SAAS,EAAEsD,WAAW,EAAEtD,SAAS,CAAC;EACxE,CAAC,MACI;IACDwD,aAAa,GAAG,IAAI/E,aAAa,CAAC6E,WAAW,EAAEtD,SAAS,EAAEA,SAAS,CAAC;EACxE;EACA,OAAO,IAAIyE,yBAAyB,CAACjB,aAAa,EAAED,YAAY,EAAE/E,YAAY,CAAC;AACnF;AACA,OAAO,MAAMiG,yBAAyB,SAAShB,eAAe,CAAC;EAC3DS,SAASA,CAACE,QAAQ,EAAE;IAChB,IAAI,IAAI,CAACR,MAAM,KAAKQ,QAAQ,EAAE;MAC1B;IACJ;IACA,IAAI,IAAI,CAACR,MAAM,EAAE;MACb,IAAI,CAACA,MAAM,CAACc,OAAO,CAAC,CAAC;IACzB;IACA,IAAI,CAACd,MAAM,GAAGQ,QAAQ;EAC1B;EACAM,OAAOA,CAAA,EAAG;IACN,IAAI,CAACd,MAAM,EAAEc,OAAO,CAAC,CAAC;EAC1B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}