{"ast": null, "code": "/**\n * marked v14.0.0 - a markdown parser\n * Copyright (c) 2011-2024, <PERSON>. (MIT Licensed)\n * https://github.com/markedjs/marked\n */\n\n/**\n * DO NOT EDIT THIS FILE\n * The code in this file is generated from files in ./src/\n */\n\n// ESM-uncomment-begin\nlet __marked_exports = {};\n(function () {\n  function define(deps, factory) {\n    factory(__marked_exports);\n  }\n  define.amd = true;\n  // ESM-uncomment-end\n\n  (function (global, factory) {\n    typeof define === 'function' && define.amd ? define(['exports'], factory) : typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) : (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global.marked = {}));\n  })(this, function (exports) {\n    'use strict';\n\n    /**\n     * Gets the original marked default options.\n     */\n    function _getDefaults() {\n      return {\n        async: false,\n        breaks: false,\n        extensions: null,\n        gfm: true,\n        hooks: null,\n        pedantic: false,\n        renderer: null,\n        silent: false,\n        tokenizer: null,\n        walkTokens: null\n      };\n    }\n    exports.defaults = _getDefaults();\n    function changeDefaults(newDefaults) {\n      exports.defaults = newDefaults;\n    }\n\n    /**\n     * Helpers\n     */\n    const escapeTest = /[&<>\"']/;\n    const escapeReplace = new RegExp(escapeTest.source, 'g');\n    const escapeTestNoEncode = /[<>\"']|&(?!(#\\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\\w+);)/;\n    const escapeReplaceNoEncode = new RegExp(escapeTestNoEncode.source, 'g');\n    const escapeReplacements = {\n      '&': '&amp;',\n      '<': '&lt;',\n      '>': '&gt;',\n      '\"': '&quot;',\n      \"'\": '&#39;'\n    };\n    const getEscapeReplacement = ch => escapeReplacements[ch];\n    function escape$1(html, encode) {\n      if (encode) {\n        if (escapeTest.test(html)) {\n          return html.replace(escapeReplace, getEscapeReplacement);\n        }\n      } else {\n        if (escapeTestNoEncode.test(html)) {\n          return html.replace(escapeReplaceNoEncode, getEscapeReplacement);\n        }\n      }\n      return html;\n    }\n    const caret = /(^|[^\\[])\\^/g;\n    function edit(regex, opt) {\n      let source = typeof regex === 'string' ? regex : regex.source;\n      opt = opt || '';\n      const obj = {\n        replace: (name, val) => {\n          let valSource = typeof val === 'string' ? val : val.source;\n          valSource = valSource.replace(caret, '$1');\n          source = source.replace(name, valSource);\n          return obj;\n        },\n        getRegex: () => {\n          return new RegExp(source, opt);\n        }\n      };\n      return obj;\n    }\n    function cleanUrl(href) {\n      try {\n        href = encodeURI(href).replace(/%25/g, '%');\n      } catch {\n        return null;\n      }\n      return href;\n    }\n    const noopTest = {\n      exec: () => null\n    };\n    function splitCells(tableRow, count) {\n      // ensure that every cell-delimiting pipe has a space\n      // before it to distinguish it from an escaped pipe\n      const row = tableRow.replace(/\\|/g, (match, offset, str) => {\n          let escaped = false;\n          let curr = offset;\n          while (--curr >= 0 && str[curr] === '\\\\') escaped = !escaped;\n          if (escaped) {\n            // odd number of slashes means | is escaped\n            // so we leave it alone\n            return '|';\n          } else {\n            // add space before unescaped |\n            return ' |';\n          }\n        }),\n        cells = row.split(/ \\|/);\n      let i = 0;\n      // First/last cell in a row cannot be empty if it has no leading/trailing pipe\n      if (!cells[0].trim()) {\n        cells.shift();\n      }\n      if (cells.length > 0 && !cells[cells.length - 1].trim()) {\n        cells.pop();\n      }\n      if (count) {\n        if (cells.length > count) {\n          cells.splice(count);\n        } else {\n          while (cells.length < count) cells.push('');\n        }\n      }\n      for (; i < cells.length; i++) {\n        // leading or trailing whitespace is ignored per the gfm spec\n        cells[i] = cells[i].trim().replace(/\\\\\\|/g, '|');\n      }\n      return cells;\n    }\n    /**\n     * Remove trailing 'c's. Equivalent to str.replace(/c*$/, '').\n     * /c*$/ is vulnerable to REDOS.\n     *\n     * @param str\n     * @param c\n     * @param invert Remove suffix of non-c chars instead. Default falsey.\n     */\n    function rtrim(str, c, invert) {\n      const l = str.length;\n      if (l === 0) {\n        return '';\n      }\n      // Length of suffix matching the invert condition.\n      let suffLen = 0;\n      // Step left until we fail to match the invert condition.\n      while (suffLen < l) {\n        const currChar = str.charAt(l - suffLen - 1);\n        if (currChar === c && !invert) {\n          suffLen++;\n        } else if (currChar !== c && invert) {\n          suffLen++;\n        } else {\n          break;\n        }\n      }\n      return str.slice(0, l - suffLen);\n    }\n    function findClosingBracket(str, b) {\n      if (str.indexOf(b[1]) === -1) {\n        return -1;\n      }\n      let level = 0;\n      for (let i = 0; i < str.length; i++) {\n        if (str[i] === '\\\\') {\n          i++;\n        } else if (str[i] === b[0]) {\n          level++;\n        } else if (str[i] === b[1]) {\n          level--;\n          if (level < 0) {\n            return i;\n          }\n        }\n      }\n      return -1;\n    }\n    function outputLink(cap, link, raw, lexer) {\n      const href = link.href;\n      const title = link.title ? escape$1(link.title) : null;\n      const text = cap[1].replace(/\\\\([\\[\\]])/g, '$1');\n      if (cap[0].charAt(0) !== '!') {\n        lexer.state.inLink = true;\n        const token = {\n          type: 'link',\n          raw,\n          href,\n          title,\n          text,\n          tokens: lexer.inlineTokens(text)\n        };\n        lexer.state.inLink = false;\n        return token;\n      }\n      return {\n        type: 'image',\n        raw,\n        href,\n        title,\n        text: escape$1(text)\n      };\n    }\n    function indentCodeCompensation(raw, text) {\n      const matchIndentToCode = raw.match(/^(\\s+)(?:```)/);\n      if (matchIndentToCode === null) {\n        return text;\n      }\n      const indentToCode = matchIndentToCode[1];\n      return text.split('\\n').map(node => {\n        const matchIndentInNode = node.match(/^\\s+/);\n        if (matchIndentInNode === null) {\n          return node;\n        }\n        const [indentInNode] = matchIndentInNode;\n        if (indentInNode.length >= indentToCode.length) {\n          return node.slice(indentToCode.length);\n        }\n        return node;\n      }).join('\\n');\n    }\n    /**\n     * Tokenizer\n     */\n    class _Tokenizer {\n      options;\n      rules; // set by the lexer\n      lexer; // set by the lexer\n      constructor(options) {\n        this.options = options || exports.defaults;\n      }\n      space(src) {\n        const cap = this.rules.block.newline.exec(src);\n        if (cap && cap[0].length > 0) {\n          return {\n            type: 'space',\n            raw: cap[0]\n          };\n        }\n      }\n      code(src) {\n        const cap = this.rules.block.code.exec(src);\n        if (cap) {\n          const text = cap[0].replace(/^ {1,4}/gm, '');\n          return {\n            type: 'code',\n            raw: cap[0],\n            codeBlockStyle: 'indented',\n            text: !this.options.pedantic ? rtrim(text, '\\n') : text\n          };\n        }\n      }\n      fences(src) {\n        const cap = this.rules.block.fences.exec(src);\n        if (cap) {\n          const raw = cap[0];\n          const text = indentCodeCompensation(raw, cap[3] || '');\n          return {\n            type: 'code',\n            raw,\n            lang: cap[2] ? cap[2].trim().replace(this.rules.inline.anyPunctuation, '$1') : cap[2],\n            text\n          };\n        }\n      }\n      heading(src) {\n        const cap = this.rules.block.heading.exec(src);\n        if (cap) {\n          let text = cap[2].trim();\n          // remove trailing #s\n          if (/#$/.test(text)) {\n            const trimmed = rtrim(text, '#');\n            if (this.options.pedantic) {\n              text = trimmed.trim();\n            } else if (!trimmed || / $/.test(trimmed)) {\n              // CommonMark requires space before trailing #s\n              text = trimmed.trim();\n            }\n          }\n          return {\n            type: 'heading',\n            raw: cap[0],\n            depth: cap[1].length,\n            text,\n            tokens: this.lexer.inline(text)\n          };\n        }\n      }\n      hr(src) {\n        const cap = this.rules.block.hr.exec(src);\n        if (cap) {\n          return {\n            type: 'hr',\n            raw: rtrim(cap[0], '\\n')\n          };\n        }\n      }\n      blockquote(src) {\n        const cap = this.rules.block.blockquote.exec(src);\n        if (cap) {\n          let lines = rtrim(cap[0], '\\n').split('\\n');\n          let raw = '';\n          let text = '';\n          const tokens = [];\n          while (lines.length > 0) {\n            let inBlockquote = false;\n            const currentLines = [];\n            let i;\n            for (i = 0; i < lines.length; i++) {\n              // get lines up to a continuation\n              if (/^ {0,3}>/.test(lines[i])) {\n                currentLines.push(lines[i]);\n                inBlockquote = true;\n              } else if (!inBlockquote) {\n                currentLines.push(lines[i]);\n              } else {\n                break;\n              }\n            }\n            lines = lines.slice(i);\n            const currentRaw = currentLines.join('\\n');\n            const currentText = currentRaw\n            // precede setext continuation with 4 spaces so it isn't a setext\n            .replace(/\\n {0,3}((?:=+|-+) *)(?=\\n|$)/g, '\\n    $1').replace(/^ {0,3}>[ \\t]?/gm, '');\n            raw = raw ? `${raw}\\n${currentRaw}` : currentRaw;\n            text = text ? `${text}\\n${currentText}` : currentText;\n            // parse blockquote lines as top level tokens\n            // merge paragraphs if this is a continuation\n            const top = this.lexer.state.top;\n            this.lexer.state.top = true;\n            this.lexer.blockTokens(currentText, tokens, true);\n            this.lexer.state.top = top;\n            // if there is no continuation then we are done\n            if (lines.length === 0) {\n              break;\n            }\n            const lastToken = tokens[tokens.length - 1];\n            if (lastToken?.type === 'code') {\n              // blockquote continuation cannot be preceded by a code block\n              break;\n            } else if (lastToken?.type === 'blockquote') {\n              // include continuation in nested blockquote\n              const oldToken = lastToken;\n              const newText = oldToken.raw + '\\n' + lines.join('\\n');\n              const newToken = this.blockquote(newText);\n              tokens[tokens.length - 1] = newToken;\n              raw = raw.substring(0, raw.length - oldToken.raw.length) + newToken.raw;\n              text = text.substring(0, text.length - oldToken.text.length) + newToken.text;\n              break;\n            } else if (lastToken?.type === 'list') {\n              // include continuation in nested list\n              const oldToken = lastToken;\n              const newText = oldToken.raw + '\\n' + lines.join('\\n');\n              const newToken = this.list(newText);\n              tokens[tokens.length - 1] = newToken;\n              raw = raw.substring(0, raw.length - lastToken.raw.length) + newToken.raw;\n              text = text.substring(0, text.length - oldToken.raw.length) + newToken.raw;\n              lines = newText.substring(tokens[tokens.length - 1].raw.length).split('\\n');\n              continue;\n            }\n          }\n          return {\n            type: 'blockquote',\n            raw,\n            tokens,\n            text\n          };\n        }\n      }\n      list(src) {\n        let cap = this.rules.block.list.exec(src);\n        if (cap) {\n          let bull = cap[1].trim();\n          const isordered = bull.length > 1;\n          const list = {\n            type: 'list',\n            raw: '',\n            ordered: isordered,\n            start: isordered ? +bull.slice(0, -1) : '',\n            loose: false,\n            items: []\n          };\n          bull = isordered ? `\\\\d{1,9}\\\\${bull.slice(-1)}` : `\\\\${bull}`;\n          if (this.options.pedantic) {\n            bull = isordered ? bull : '[*+-]';\n          }\n          // Get next list item\n          const itemRegex = new RegExp(`^( {0,3}${bull})((?:[\\t ][^\\\\n]*)?(?:\\\\n|$))`);\n          let endsWithBlankLine = false;\n          // Check if current bullet point can start a new List Item\n          while (src) {\n            let endEarly = false;\n            let raw = '';\n            let itemContents = '';\n            if (!(cap = itemRegex.exec(src))) {\n              break;\n            }\n            if (this.rules.block.hr.test(src)) {\n              // End list if bullet was actually HR (possibly move into itemRegex?)\n              break;\n            }\n            raw = cap[0];\n            src = src.substring(raw.length);\n            let line = cap[2].split('\\n', 1)[0].replace(/^\\t+/, t => ' '.repeat(3 * t.length));\n            let nextLine = src.split('\\n', 1)[0];\n            let blankLine = !line.trim();\n            let indent = 0;\n            if (this.options.pedantic) {\n              indent = 2;\n              itemContents = line.trimStart();\n            } else if (blankLine) {\n              indent = cap[1].length + 1;\n            } else {\n              indent = cap[2].search(/[^ ]/); // Find first non-space char\n              indent = indent > 4 ? 1 : indent; // Treat indented code blocks (> 4 spaces) as having only 1 indent\n              itemContents = line.slice(indent);\n              indent += cap[1].length;\n            }\n            if (blankLine && /^ *$/.test(nextLine)) {\n              // Items begin with at most one blank line\n              raw += nextLine + '\\n';\n              src = src.substring(nextLine.length + 1);\n              endEarly = true;\n            }\n            if (!endEarly) {\n              const nextBulletRegex = new RegExp(`^ {0,${Math.min(3, indent - 1)}}(?:[*+-]|\\\\d{1,9}[.)])((?:[ \\t][^\\\\n]*)?(?:\\\\n|$))`);\n              const hrRegex = new RegExp(`^ {0,${Math.min(3, indent - 1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\\\* *){3,})(?:\\\\n+|$)`);\n              const fencesBeginRegex = new RegExp(`^ {0,${Math.min(3, indent - 1)}}(?:\\`\\`\\`|~~~)`);\n              const headingBeginRegex = new RegExp(`^ {0,${Math.min(3, indent - 1)}}#`);\n              // Check if following lines should be included in List Item\n              while (src) {\n                const rawLine = src.split('\\n', 1)[0];\n                nextLine = rawLine;\n                // Re-align to follow commonmark nesting rules\n                if (this.options.pedantic) {\n                  nextLine = nextLine.replace(/^ {1,4}(?=( {4})*[^ ])/g, '  ');\n                }\n                // End list item if found code fences\n                if (fencesBeginRegex.test(nextLine)) {\n                  break;\n                }\n                // End list item if found start of new heading\n                if (headingBeginRegex.test(nextLine)) {\n                  break;\n                }\n                // End list item if found start of new bullet\n                if (nextBulletRegex.test(nextLine)) {\n                  break;\n                }\n                // Horizontal rule found\n                if (hrRegex.test(src)) {\n                  break;\n                }\n                if (nextLine.search(/[^ ]/) >= indent || !nextLine.trim()) {\n                  // Dedent if possible\n                  itemContents += '\\n' + nextLine.slice(indent);\n                } else {\n                  // not enough indentation\n                  if (blankLine) {\n                    break;\n                  }\n                  // paragraph continuation unless last line was a different block level element\n                  if (line.search(/[^ ]/) >= 4) {\n                    // indented code block\n                    break;\n                  }\n                  if (fencesBeginRegex.test(line)) {\n                    break;\n                  }\n                  if (headingBeginRegex.test(line)) {\n                    break;\n                  }\n                  if (hrRegex.test(line)) {\n                    break;\n                  }\n                  itemContents += '\\n' + nextLine;\n                }\n                if (!blankLine && !nextLine.trim()) {\n                  // Check if current line is blank\n                  blankLine = true;\n                }\n                raw += rawLine + '\\n';\n                src = src.substring(rawLine.length + 1);\n                line = nextLine.slice(indent);\n              }\n            }\n            if (!list.loose) {\n              // If the previous item ended with a blank line, the list is loose\n              if (endsWithBlankLine) {\n                list.loose = true;\n              } else if (/\\n *\\n *$/.test(raw)) {\n                endsWithBlankLine = true;\n              }\n            }\n            let istask = null;\n            let ischecked;\n            // Check for task list items\n            if (this.options.gfm) {\n              istask = /^\\[[ xX]\\] /.exec(itemContents);\n              if (istask) {\n                ischecked = istask[0] !== '[ ] ';\n                itemContents = itemContents.replace(/^\\[[ xX]\\] +/, '');\n              }\n            }\n            list.items.push({\n              type: 'list_item',\n              raw,\n              task: !!istask,\n              checked: ischecked,\n              loose: false,\n              text: itemContents,\n              tokens: []\n            });\n            list.raw += raw;\n          }\n          // Do not consume newlines at end of final item. Alternatively, make itemRegex *start* with any newlines to simplify/speed up endsWithBlankLine logic\n          list.items[list.items.length - 1].raw = list.items[list.items.length - 1].raw.trimEnd();\n          list.items[list.items.length - 1].text = list.items[list.items.length - 1].text.trimEnd();\n          list.raw = list.raw.trimEnd();\n          // Item child tokens handled here at end because we needed to have the final item to trim it first\n          for (let i = 0; i < list.items.length; i++) {\n            this.lexer.state.top = false;\n            list.items[i].tokens = this.lexer.blockTokens(list.items[i].text, []);\n            if (!list.loose) {\n              // Check if list should be loose\n              const spacers = list.items[i].tokens.filter(t => t.type === 'space');\n              const hasMultipleLineBreaks = spacers.length > 0 && spacers.some(t => /\\n.*\\n/.test(t.raw));\n              list.loose = hasMultipleLineBreaks;\n            }\n          }\n          // Set all items to loose if list is loose\n          if (list.loose) {\n            for (let i = 0; i < list.items.length; i++) {\n              list.items[i].loose = true;\n            }\n          }\n          return list;\n        }\n      }\n      html(src) {\n        const cap = this.rules.block.html.exec(src);\n        if (cap) {\n          const token = {\n            type: 'html',\n            block: true,\n            raw: cap[0],\n            pre: cap[1] === 'pre' || cap[1] === 'script' || cap[1] === 'style',\n            text: cap[0]\n          };\n          return token;\n        }\n      }\n      def(src) {\n        const cap = this.rules.block.def.exec(src);\n        if (cap) {\n          const tag = cap[1].toLowerCase().replace(/\\s+/g, ' ');\n          const href = cap[2] ? cap[2].replace(/^<(.*)>$/, '$1').replace(this.rules.inline.anyPunctuation, '$1') : '';\n          const title = cap[3] ? cap[3].substring(1, cap[3].length - 1).replace(this.rules.inline.anyPunctuation, '$1') : cap[3];\n          return {\n            type: 'def',\n            tag,\n            raw: cap[0],\n            href,\n            title\n          };\n        }\n      }\n      table(src) {\n        const cap = this.rules.block.table.exec(src);\n        if (!cap) {\n          return;\n        }\n        if (!/[:|]/.test(cap[2])) {\n          // delimiter row must have a pipe (|) or colon (:) otherwise it is a setext heading\n          return;\n        }\n        const headers = splitCells(cap[1]);\n        const aligns = cap[2].replace(/^\\||\\| *$/g, '').split('|');\n        const rows = cap[3] && cap[3].trim() ? cap[3].replace(/\\n[ \\t]*$/, '').split('\\n') : [];\n        const item = {\n          type: 'table',\n          raw: cap[0],\n          header: [],\n          align: [],\n          rows: []\n        };\n        if (headers.length !== aligns.length) {\n          // header and align columns must be equal, rows can be different.\n          return;\n        }\n        for (const align of aligns) {\n          if (/^ *-+: *$/.test(align)) {\n            item.align.push('right');\n          } else if (/^ *:-+: *$/.test(align)) {\n            item.align.push('center');\n          } else if (/^ *:-+ *$/.test(align)) {\n            item.align.push('left');\n          } else {\n            item.align.push(null);\n          }\n        }\n        for (let i = 0; i < headers.length; i++) {\n          item.header.push({\n            text: headers[i],\n            tokens: this.lexer.inline(headers[i]),\n            header: true,\n            align: item.align[i]\n          });\n        }\n        for (const row of rows) {\n          item.rows.push(splitCells(row, item.header.length).map((cell, i) => {\n            return {\n              text: cell,\n              tokens: this.lexer.inline(cell),\n              header: false,\n              align: item.align[i]\n            };\n          }));\n        }\n        return item;\n      }\n      lheading(src) {\n        const cap = this.rules.block.lheading.exec(src);\n        if (cap) {\n          return {\n            type: 'heading',\n            raw: cap[0],\n            depth: cap[2].charAt(0) === '=' ? 1 : 2,\n            text: cap[1],\n            tokens: this.lexer.inline(cap[1])\n          };\n        }\n      }\n      paragraph(src) {\n        const cap = this.rules.block.paragraph.exec(src);\n        if (cap) {\n          const text = cap[1].charAt(cap[1].length - 1) === '\\n' ? cap[1].slice(0, -1) : cap[1];\n          return {\n            type: 'paragraph',\n            raw: cap[0],\n            text,\n            tokens: this.lexer.inline(text)\n          };\n        }\n      }\n      text(src) {\n        const cap = this.rules.block.text.exec(src);\n        if (cap) {\n          return {\n            type: 'text',\n            raw: cap[0],\n            text: cap[0],\n            tokens: this.lexer.inline(cap[0])\n          };\n        }\n      }\n      escape(src) {\n        const cap = this.rules.inline.escape.exec(src);\n        if (cap) {\n          return {\n            type: 'escape',\n            raw: cap[0],\n            text: escape$1(cap[1])\n          };\n        }\n      }\n      tag(src) {\n        const cap = this.rules.inline.tag.exec(src);\n        if (cap) {\n          if (!this.lexer.state.inLink && /^<a /i.test(cap[0])) {\n            this.lexer.state.inLink = true;\n          } else if (this.lexer.state.inLink && /^<\\/a>/i.test(cap[0])) {\n            this.lexer.state.inLink = false;\n          }\n          if (!this.lexer.state.inRawBlock && /^<(pre|code|kbd|script)(\\s|>)/i.test(cap[0])) {\n            this.lexer.state.inRawBlock = true;\n          } else if (this.lexer.state.inRawBlock && /^<\\/(pre|code|kbd|script)(\\s|>)/i.test(cap[0])) {\n            this.lexer.state.inRawBlock = false;\n          }\n          return {\n            type: 'html',\n            raw: cap[0],\n            inLink: this.lexer.state.inLink,\n            inRawBlock: this.lexer.state.inRawBlock,\n            block: false,\n            text: cap[0]\n          };\n        }\n      }\n      link(src) {\n        const cap = this.rules.inline.link.exec(src);\n        if (cap) {\n          const trimmedUrl = cap[2].trim();\n          if (!this.options.pedantic && /^</.test(trimmedUrl)) {\n            // commonmark requires matching angle brackets\n            if (!/>$/.test(trimmedUrl)) {\n              return;\n            }\n            // ending angle bracket cannot be escaped\n            const rtrimSlash = rtrim(trimmedUrl.slice(0, -1), '\\\\');\n            if ((trimmedUrl.length - rtrimSlash.length) % 2 === 0) {\n              return;\n            }\n          } else {\n            // find closing parenthesis\n            const lastParenIndex = findClosingBracket(cap[2], '()');\n            if (lastParenIndex > -1) {\n              const start = cap[0].indexOf('!') === 0 ? 5 : 4;\n              const linkLen = start + cap[1].length + lastParenIndex;\n              cap[2] = cap[2].substring(0, lastParenIndex);\n              cap[0] = cap[0].substring(0, linkLen).trim();\n              cap[3] = '';\n            }\n          }\n          let href = cap[2];\n          let title = '';\n          if (this.options.pedantic) {\n            // split pedantic href and title\n            const link = /^([^'\"]*[^\\s])\\s+(['\"])(.*)\\2/.exec(href);\n            if (link) {\n              href = link[1];\n              title = link[3];\n            }\n          } else {\n            title = cap[3] ? cap[3].slice(1, -1) : '';\n          }\n          href = href.trim();\n          if (/^</.test(href)) {\n            if (this.options.pedantic && !/>$/.test(trimmedUrl)) {\n              // pedantic allows starting angle bracket without ending angle bracket\n              href = href.slice(1);\n            } else {\n              href = href.slice(1, -1);\n            }\n          }\n          return outputLink(cap, {\n            href: href ? href.replace(this.rules.inline.anyPunctuation, '$1') : href,\n            title: title ? title.replace(this.rules.inline.anyPunctuation, '$1') : title\n          }, cap[0], this.lexer);\n        }\n      }\n      reflink(src, links) {\n        let cap;\n        if ((cap = this.rules.inline.reflink.exec(src)) || (cap = this.rules.inline.nolink.exec(src))) {\n          const linkString = (cap[2] || cap[1]).replace(/\\s+/g, ' ');\n          const link = links[linkString.toLowerCase()];\n          if (!link) {\n            const text = cap[0].charAt(0);\n            return {\n              type: 'text',\n              raw: text,\n              text\n            };\n          }\n          return outputLink(cap, link, cap[0], this.lexer);\n        }\n      }\n      emStrong(src, maskedSrc, prevChar = '') {\n        let match = this.rules.inline.emStrongLDelim.exec(src);\n        if (!match) return;\n        // _ can't be between two alphanumerics. \\p{L}\\p{N} includes non-english alphabet/numbers as well\n        if (match[3] && prevChar.match(/[\\p{L}\\p{N}]/u)) return;\n        const nextChar = match[1] || match[2] || '';\n        if (!nextChar || !prevChar || this.rules.inline.punctuation.exec(prevChar)) {\n          // unicode Regex counts emoji as 1 char; spread into array for proper count (used multiple times below)\n          const lLength = [...match[0]].length - 1;\n          let rDelim,\n            rLength,\n            delimTotal = lLength,\n            midDelimTotal = 0;\n          const endReg = match[0][0] === '*' ? this.rules.inline.emStrongRDelimAst : this.rules.inline.emStrongRDelimUnd;\n          endReg.lastIndex = 0;\n          // Clip maskedSrc to same section of string as src (move to lexer?)\n          maskedSrc = maskedSrc.slice(-1 * src.length + lLength);\n          while ((match = endReg.exec(maskedSrc)) != null) {\n            rDelim = match[1] || match[2] || match[3] || match[4] || match[5] || match[6];\n            if (!rDelim) continue; // skip single * in __abc*abc__\n            rLength = [...rDelim].length;\n            if (match[3] || match[4]) {\n              // found another Left Delim\n              delimTotal += rLength;\n              continue;\n            } else if (match[5] || match[6]) {\n              // either Left or Right Delim\n              if (lLength % 3 && !((lLength + rLength) % 3)) {\n                midDelimTotal += rLength;\n                continue; // CommonMark Emphasis Rules 9-10\n              }\n            }\n            delimTotal -= rLength;\n            if (delimTotal > 0) continue; // Haven't found enough closing delimiters\n            // Remove extra characters. *a*** -> *a*\n            rLength = Math.min(rLength, rLength + delimTotal + midDelimTotal);\n            // char length can be >1 for unicode characters;\n            const lastCharLength = [...match[0]][0].length;\n            const raw = src.slice(0, lLength + match.index + lastCharLength + rLength);\n            // Create `em` if smallest delimiter has odd char count. *a***\n            if (Math.min(lLength, rLength) % 2) {\n              const text = raw.slice(1, -1);\n              return {\n                type: 'em',\n                raw,\n                text,\n                tokens: this.lexer.inlineTokens(text)\n              };\n            }\n            // Create 'strong' if smallest delimiter has even char count. **a***\n            const text = raw.slice(2, -2);\n            return {\n              type: 'strong',\n              raw,\n              text,\n              tokens: this.lexer.inlineTokens(text)\n            };\n          }\n        }\n      }\n      codespan(src) {\n        const cap = this.rules.inline.code.exec(src);\n        if (cap) {\n          let text = cap[2].replace(/\\n/g, ' ');\n          const hasNonSpaceChars = /[^ ]/.test(text);\n          const hasSpaceCharsOnBothEnds = /^ /.test(text) && / $/.test(text);\n          if (hasNonSpaceChars && hasSpaceCharsOnBothEnds) {\n            text = text.substring(1, text.length - 1);\n          }\n          text = escape$1(text, true);\n          return {\n            type: 'codespan',\n            raw: cap[0],\n            text\n          };\n        }\n      }\n      br(src) {\n        const cap = this.rules.inline.br.exec(src);\n        if (cap) {\n          return {\n            type: 'br',\n            raw: cap[0]\n          };\n        }\n      }\n      del(src) {\n        const cap = this.rules.inline.del.exec(src);\n        if (cap) {\n          return {\n            type: 'del',\n            raw: cap[0],\n            text: cap[2],\n            tokens: this.lexer.inlineTokens(cap[2])\n          };\n        }\n      }\n      autolink(src) {\n        const cap = this.rules.inline.autolink.exec(src);\n        if (cap) {\n          let text, href;\n          if (cap[2] === '@') {\n            text = escape$1(cap[1]);\n            href = 'mailto:' + text;\n          } else {\n            text = escape$1(cap[1]);\n            href = text;\n          }\n          return {\n            type: 'link',\n            raw: cap[0],\n            text,\n            href,\n            tokens: [{\n              type: 'text',\n              raw: text,\n              text\n            }]\n          };\n        }\n      }\n      url(src) {\n        let cap;\n        if (cap = this.rules.inline.url.exec(src)) {\n          let text, href;\n          if (cap[2] === '@') {\n            text = escape$1(cap[0]);\n            href = 'mailto:' + text;\n          } else {\n            // do extended autolink path validation\n            let prevCapZero;\n            do {\n              prevCapZero = cap[0];\n              cap[0] = this.rules.inline._backpedal.exec(cap[0])?.[0] ?? '';\n            } while (prevCapZero !== cap[0]);\n            text = escape$1(cap[0]);\n            if (cap[1] === 'www.') {\n              href = 'http://' + cap[0];\n            } else {\n              href = cap[0];\n            }\n          }\n          return {\n            type: 'link',\n            raw: cap[0],\n            text,\n            href,\n            tokens: [{\n              type: 'text',\n              raw: text,\n              text\n            }]\n          };\n        }\n      }\n      inlineText(src) {\n        const cap = this.rules.inline.text.exec(src);\n        if (cap) {\n          let text;\n          if (this.lexer.state.inRawBlock) {\n            text = cap[0];\n          } else {\n            text = escape$1(cap[0]);\n          }\n          return {\n            type: 'text',\n            raw: cap[0],\n            text\n          };\n        }\n      }\n    }\n\n    /**\n     * Block-Level Grammar\n     */\n    const newline = /^(?: *(?:\\n|$))+/;\n    const blockCode = /^( {4}[^\\n]+(?:\\n(?: *(?:\\n|$))*)?)+/;\n    const fences = /^ {0,3}(`{3,}(?=[^`\\n]*(?:\\n|$))|~{3,})([^\\n]*)(?:\\n|$)(?:|([\\s\\S]*?)(?:\\n|$))(?: {0,3}\\1[~`]* *(?=\\n|$)|$)/;\n    const hr = /^ {0,3}((?:-[\\t ]*){3,}|(?:_[ \\t]*){3,}|(?:\\*[ \\t]*){3,})(?:\\n+|$)/;\n    const heading = /^ {0,3}(#{1,6})(?=\\s|$)(.*)(?:\\n+|$)/;\n    const bullet = /(?:[*+-]|\\d{1,9}[.)])/;\n    const lheading = edit(/^(?!bull |blockCode|fences|blockquote|heading|html)((?:.|\\n(?!\\s*?\\n|bull |blockCode|fences|blockquote|heading|html))+?)\\n {0,3}(=+|-+) *(?:\\n+|$)/).replace(/bull/g, bullet) // lists can interrupt\n    .replace(/blockCode/g, / {4}/) // indented code blocks can interrupt\n    .replace(/fences/g, / {0,3}(?:`{3,}|~{3,})/) // fenced code blocks can interrupt\n    .replace(/blockquote/g, / {0,3}>/) // blockquote can interrupt\n    .replace(/heading/g, / {0,3}#{1,6}/) // ATX heading can interrupt\n    .replace(/html/g, / {0,3}<[^\\n>]+>\\n/) // block html can interrupt\n    .getRegex();\n    const _paragraph = /^([^\\n]+(?:\\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\\n)[^\\n]+)*)/;\n    const blockText = /^[^\\n]+/;\n    const _blockLabel = /(?!\\s*\\])(?:\\\\.|[^\\[\\]\\\\])+/;\n    const def = edit(/^ {0,3}\\[(label)\\]: *(?:\\n *)?([^<\\s][^\\s]*|<.*?>)(?:(?: +(?:\\n *)?| *\\n *)(title))? *(?:\\n+|$)/).replace('label', _blockLabel).replace('title', /(?:\"(?:\\\\\"?|[^\"\\\\])*\"|'[^'\\n]*(?:\\n[^'\\n]+)*\\n?'|\\([^()]*\\))/).getRegex();\n    const list = edit(/^( {0,3}bull)([ \\t][^\\n]+?)?(?:\\n|$)/).replace(/bull/g, bullet).getRegex();\n    const _tag = 'address|article|aside|base|basefont|blockquote|body|caption' + '|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption' + '|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe' + '|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option' + '|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title' + '|tr|track|ul';\n    const _comment = /<!--(?:-?>|[\\s\\S]*?(?:-->|$))/;\n    const html = edit('^ {0,3}(?:' // optional indentation\n    + '<(script|pre|style|textarea)[\\\\s>][\\\\s\\\\S]*?(?:</\\\\1>[^\\\\n]*\\\\n+|$)' // (1)\n    + '|comment[^\\\\n]*(\\\\n+|$)' // (2)\n    + '|<\\\\?[\\\\s\\\\S]*?(?:\\\\?>\\\\n*|$)' // (3)\n    + '|<![A-Z][\\\\s\\\\S]*?(?:>\\\\n*|$)' // (4)\n    + '|<!\\\\[CDATA\\\\[[\\\\s\\\\S]*?(?:\\\\]\\\\]>\\\\n*|$)' // (5)\n    + '|</?(tag)(?: +|\\\\n|/?>)[\\\\s\\\\S]*?(?:(?:\\\\n *)+\\\\n|$)' // (6)\n    + '|<(?!script|pre|style|textarea)([a-z][\\\\w-]*)(?:attribute)*? */?>(?=[ \\\\t]*(?:\\\\n|$))[\\\\s\\\\S]*?(?:(?:\\\\n *)+\\\\n|$)' // (7) open tag\n    + '|</(?!script|pre|style|textarea)[a-z][\\\\w-]*\\\\s*>(?=[ \\\\t]*(?:\\\\n|$))[\\\\s\\\\S]*?(?:(?:\\\\n *)+\\\\n|$)' // (7) closing tag\n    + ')', 'i').replace('comment', _comment).replace('tag', _tag).replace('attribute', / +[a-zA-Z:_][\\w.:-]*(?: *= *\"[^\"\\n]*\"| *= *'[^'\\n]*'| *= *[^\\s\"'=<>`]+)?/).getRegex();\n    const paragraph = edit(_paragraph).replace('hr', hr).replace('heading', ' {0,3}#{1,6}(?:\\\\s|$)').replace('|lheading', '') // setext headings don't interrupt commonmark paragraphs\n    .replace('|table', '').replace('blockquote', ' {0,3}>').replace('fences', ' {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n').replace('list', ' {0,3}(?:[*+-]|1[.)]) ') // only lists starting from 1 can interrupt\n    .replace('html', '</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)').replace('tag', _tag) // pars can be interrupted by type (6) html blocks\n    .getRegex();\n    const blockquote = edit(/^( {0,3}> ?(paragraph|[^\\n]*)(?:\\n|$))+/).replace('paragraph', paragraph).getRegex();\n    /**\n     * Normal Block Grammar\n     */\n    const blockNormal = {\n      blockquote,\n      code: blockCode,\n      def,\n      fences,\n      heading,\n      hr,\n      html,\n      lheading,\n      list,\n      newline,\n      paragraph,\n      table: noopTest,\n      text: blockText\n    };\n    /**\n     * GFM Block Grammar\n     */\n    const gfmTable = edit('^ *([^\\\\n ].*)\\\\n' // Header\n    + ' {0,3}((?:\\\\| *)?:?-+:? *(?:\\\\| *:?-+:? *)*(?:\\\\| *)?)' // Align\n    + '(?:\\\\n((?:(?! *\\\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\\\n|$))*)\\\\n*|$)') // Cells\n    .replace('hr', hr).replace('heading', ' {0,3}#{1,6}(?:\\\\s|$)').replace('blockquote', ' {0,3}>').replace('code', ' {4}[^\\\\n]').replace('fences', ' {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n').replace('list', ' {0,3}(?:[*+-]|1[.)]) ') // only lists starting from 1 can interrupt\n    .replace('html', '</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)').replace('tag', _tag) // tables can be interrupted by type (6) html blocks\n    .getRegex();\n    const blockGfm = {\n      ...blockNormal,\n      table: gfmTable,\n      paragraph: edit(_paragraph).replace('hr', hr).replace('heading', ' {0,3}#{1,6}(?:\\\\s|$)').replace('|lheading', '') // setext headings don't interrupt commonmark paragraphs\n      .replace('table', gfmTable) // interrupt paragraphs with table\n      .replace('blockquote', ' {0,3}>').replace('fences', ' {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n').replace('list', ' {0,3}(?:[*+-]|1[.)]) ') // only lists starting from 1 can interrupt\n      .replace('html', '</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)').replace('tag', _tag) // pars can be interrupted by type (6) html blocks\n      .getRegex()\n    };\n    /**\n     * Pedantic grammar (original John Gruber's loose markdown specification)\n     */\n    const blockPedantic = {\n      ...blockNormal,\n      html: edit('^ *(?:comment *(?:\\\\n|\\\\s*$)' + '|<(tag)[\\\\s\\\\S]+?</\\\\1> *(?:\\\\n{2,}|\\\\s*$)' // closed tag\n      + '|<tag(?:\"[^\"]*\"|\\'[^\\']*\\'|\\\\s[^\\'\"/>\\\\s]*)*?/?> *(?:\\\\n{2,}|\\\\s*$))').replace('comment', _comment).replace(/tag/g, '(?!(?:' + 'a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub' + '|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)' + '\\\\b)\\\\w+(?!:|[^\\\\w\\\\s@]*@)\\\\b').getRegex(),\n      def: /^ *\\[([^\\]]+)\\]: *<?([^\\s>]+)>?(?: +([\"(][^\\n]+[\")]))? *(?:\\n+|$)/,\n      heading: /^(#{1,6})(.*)(?:\\n+|$)/,\n      fences: noopTest,\n      // fences not supported\n      lheading: /^(.+?)\\n {0,3}(=+|-+) *(?:\\n+|$)/,\n      paragraph: edit(_paragraph).replace('hr', hr).replace('heading', ' *#{1,6} *[^\\n]').replace('lheading', lheading).replace('|table', '').replace('blockquote', ' {0,3}>').replace('|fences', '').replace('|list', '').replace('|html', '').replace('|tag', '').getRegex()\n    };\n    /**\n     * Inline-Level Grammar\n     */\n    const escape = /^\\\\([!\"#$%&'()*+,\\-./:;<=>?@\\[\\]\\\\^_`{|}~])/;\n    const inlineCode = /^(`+)([^`]|[^`][\\s\\S]*?[^`])\\1(?!`)/;\n    const br = /^( {2,}|\\\\)\\n(?!\\s*$)/;\n    const inlineText = /^(`+|[^`])(?:(?= {2,}\\n)|[\\s\\S]*?(?:(?=[\\\\<!\\[`*_]|\\b_|$)|[^ ](?= {2,}\\n)))/;\n    // list of unicode punctuation marks, plus any missing characters from CommonMark spec\n    const _punctuation = '\\\\p{P}\\\\p{S}';\n    const punctuation = edit(/^((?![*_])[\\spunctuation])/, 'u').replace(/punctuation/g, _punctuation).getRegex();\n    // sequences em should skip over [title](link), `code`, <html>\n    const blockSkip = /\\[[^[\\]]*?\\]\\([^\\(\\)]*?\\)|`[^`]*?`|<[^<>]*?>/g;\n    const emStrongLDelim = edit(/^(?:\\*+(?:((?!\\*)[punct])|[^\\s*]))|^_+(?:((?!_)[punct])|([^\\s_]))/, 'u').replace(/punct/g, _punctuation).getRegex();\n    const emStrongRDelimAst = edit('^[^_*]*?__[^_*]*?\\\\*[^_*]*?(?=__)' // Skip orphan inside strong\n    + '|[^*]+(?=[^*])' // Consume to delim\n    + '|(?!\\\\*)[punct](\\\\*+)(?=[\\\\s]|$)' // (1) #*** can only be a Right Delimiter\n    + '|[^punct\\\\s](\\\\*+)(?!\\\\*)(?=[punct\\\\s]|$)' // (2) a***#, a*** can only be a Right Delimiter\n    + '|(?!\\\\*)[punct\\\\s](\\\\*+)(?=[^punct\\\\s])' // (3) #***a, ***a can only be Left Delimiter\n    + '|[\\\\s](\\\\*+)(?!\\\\*)(?=[punct])' // (4) ***# can only be Left Delimiter\n    + '|(?!\\\\*)[punct](\\\\*+)(?!\\\\*)(?=[punct])' // (5) #***# can be either Left or Right Delimiter\n    + '|[^punct\\\\s](\\\\*+)(?=[^punct\\\\s])', 'gu') // (6) a***a can be either Left or Right Delimiter\n    .replace(/punct/g, _punctuation).getRegex();\n    // (6) Not allowed for _\n    const emStrongRDelimUnd = edit('^[^_*]*?\\\\*\\\\*[^_*]*?_[^_*]*?(?=\\\\*\\\\*)' // Skip orphan inside strong\n    + '|[^_]+(?=[^_])' // Consume to delim\n    + '|(?!_)[punct](_+)(?=[\\\\s]|$)' // (1) #___ can only be a Right Delimiter\n    + '|[^punct\\\\s](_+)(?!_)(?=[punct\\\\s]|$)' // (2) a___#, a___ can only be a Right Delimiter\n    + '|(?!_)[punct\\\\s](_+)(?=[^punct\\\\s])' // (3) #___a, ___a can only be Left Delimiter\n    + '|[\\\\s](_+)(?!_)(?=[punct])' // (4) ___# can only be Left Delimiter\n    + '|(?!_)[punct](_+)(?!_)(?=[punct])', 'gu') // (5) #___# can be either Left or Right Delimiter\n    .replace(/punct/g, _punctuation).getRegex();\n    const anyPunctuation = edit(/\\\\([punct])/, 'gu').replace(/punct/g, _punctuation).getRegex();\n    const autolink = edit(/^<(scheme:[^\\s\\x00-\\x1f<>]*|email)>/).replace('scheme', /[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace('email', /[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex();\n    const _inlineComment = edit(_comment).replace('(?:-->|$)', '-->').getRegex();\n    const tag = edit('^comment' + '|^</[a-zA-Z][\\\\w:-]*\\\\s*>' // self-closing tag\n    + '|^<[a-zA-Z][\\\\w-]*(?:attribute)*?\\\\s*/?>' // open tag\n    + '|^<\\\\?[\\\\s\\\\S]*?\\\\?>' // processing instruction, e.g. <?php ?>\n    + '|^<![a-zA-Z]+\\\\s[\\\\s\\\\S]*?>' // declaration, e.g. <!DOCTYPE html>\n    + '|^<!\\\\[CDATA\\\\[[\\\\s\\\\S]*?\\\\]\\\\]>') // CDATA section\n    .replace('comment', _inlineComment).replace('attribute', /\\s+[a-zA-Z:_][\\w.:-]*(?:\\s*=\\s*\"[^\"]*\"|\\s*=\\s*'[^']*'|\\s*=\\s*[^\\s\"'=<>`]+)?/).getRegex();\n    const _inlineLabel = /(?:\\[(?:\\\\.|[^\\[\\]\\\\])*\\]|\\\\.|`[^`]*`|[^\\[\\]\\\\`])*?/;\n    const link = edit(/^!?\\[(label)\\]\\(\\s*(href)(?:\\s+(title))?\\s*\\)/).replace('label', _inlineLabel).replace('href', /<(?:\\\\.|[^\\n<>\\\\])+>|[^\\s\\x00-\\x1f]*/).replace('title', /\"(?:\\\\\"?|[^\"\\\\])*\"|'(?:\\\\'?|[^'\\\\])*'|\\((?:\\\\\\)?|[^)\\\\])*\\)/).getRegex();\n    const reflink = edit(/^!?\\[(label)\\]\\[(ref)\\]/).replace('label', _inlineLabel).replace('ref', _blockLabel).getRegex();\n    const nolink = edit(/^!?\\[(ref)\\](?:\\[\\])?/).replace('ref', _blockLabel).getRegex();\n    const reflinkSearch = edit('reflink|nolink(?!\\\\()', 'g').replace('reflink', reflink).replace('nolink', nolink).getRegex();\n    /**\n     * Normal Inline Grammar\n     */\n    const inlineNormal = {\n      _backpedal: noopTest,\n      // only used for GFM url\n      anyPunctuation,\n      autolink,\n      blockSkip,\n      br,\n      code: inlineCode,\n      del: noopTest,\n      emStrongLDelim,\n      emStrongRDelimAst,\n      emStrongRDelimUnd,\n      escape,\n      link,\n      nolink,\n      punctuation,\n      reflink,\n      reflinkSearch,\n      tag,\n      text: inlineText,\n      url: noopTest\n    };\n    /**\n     * Pedantic Inline Grammar\n     */\n    const inlinePedantic = {\n      ...inlineNormal,\n      link: edit(/^!?\\[(label)\\]\\((.*?)\\)/).replace('label', _inlineLabel).getRegex(),\n      reflink: edit(/^!?\\[(label)\\]\\s*\\[([^\\]]*)\\]/).replace('label', _inlineLabel).getRegex()\n    };\n    /**\n     * GFM Inline Grammar\n     */\n    const inlineGfm = {\n      ...inlineNormal,\n      escape: edit(escape).replace('])', '~|])').getRegex(),\n      url: edit(/^((?:ftp|https?):\\/\\/|www\\.)(?:[a-zA-Z0-9\\-]+\\.?)+[^\\s<]*|^email/, 'i').replace('email', /[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),\n      _backpedal: /(?:[^?!.,:;*_'\"~()&]+|\\([^)]*\\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'\"~)]+(?!$))+/,\n      del: /^(~~?)(?=[^\\s~])([\\s\\S]*?[^\\s~])\\1(?=[^~]|$)/,\n      text: /^([`~]+|[^`~])(?:(?= {2,}\\n)|(?=[a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-]+@)|[\\s\\S]*?(?:(?=[\\\\<!\\[`*~_]|\\b_|https?:\\/\\/|ftp:\\/\\/|www\\.|$)|[^ ](?= {2,}\\n)|[^a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-](?=[a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-]+@)))/\n    };\n    /**\n     * GFM + Line Breaks Inline Grammar\n     */\n    const inlineBreaks = {\n      ...inlineGfm,\n      br: edit(br).replace('{2,}', '*').getRegex(),\n      text: edit(inlineGfm.text).replace('\\\\b_', '\\\\b_| {2,}\\\\n').replace(/\\{2,\\}/g, '*').getRegex()\n    };\n    /**\n     * exports\n     */\n    const block = {\n      normal: blockNormal,\n      gfm: blockGfm,\n      pedantic: blockPedantic\n    };\n    const inline = {\n      normal: inlineNormal,\n      gfm: inlineGfm,\n      breaks: inlineBreaks,\n      pedantic: inlinePedantic\n    };\n\n    /**\n     * Block Lexer\n     */\n    class _Lexer {\n      tokens;\n      options;\n      state;\n      tokenizer;\n      inlineQueue;\n      constructor(options) {\n        // TokenList cannot be created in one go\n        this.tokens = [];\n        this.tokens.links = Object.create(null);\n        this.options = options || exports.defaults;\n        this.options.tokenizer = this.options.tokenizer || new _Tokenizer();\n        this.tokenizer = this.options.tokenizer;\n        this.tokenizer.options = this.options;\n        this.tokenizer.lexer = this;\n        this.inlineQueue = [];\n        this.state = {\n          inLink: false,\n          inRawBlock: false,\n          top: true\n        };\n        const rules = {\n          block: block.normal,\n          inline: inline.normal\n        };\n        if (this.options.pedantic) {\n          rules.block = block.pedantic;\n          rules.inline = inline.pedantic;\n        } else if (this.options.gfm) {\n          rules.block = block.gfm;\n          if (this.options.breaks) {\n            rules.inline = inline.breaks;\n          } else {\n            rules.inline = inline.gfm;\n          }\n        }\n        this.tokenizer.rules = rules;\n      }\n      /**\n       * Expose Rules\n       */\n      static get rules() {\n        return {\n          block,\n          inline\n        };\n      }\n      /**\n       * Static Lex Method\n       */\n      static lex(src, options) {\n        const lexer = new _Lexer(options);\n        return lexer.lex(src);\n      }\n      /**\n       * Static Lex Inline Method\n       */\n      static lexInline(src, options) {\n        const lexer = new _Lexer(options);\n        return lexer.inlineTokens(src);\n      }\n      /**\n       * Preprocessing\n       */\n      lex(src) {\n        src = src.replace(/\\r\\n|\\r/g, '\\n');\n        this.blockTokens(src, this.tokens);\n        for (let i = 0; i < this.inlineQueue.length; i++) {\n          const next = this.inlineQueue[i];\n          this.inlineTokens(next.src, next.tokens);\n        }\n        this.inlineQueue = [];\n        return this.tokens;\n      }\n      blockTokens(src, tokens = [], lastParagraphClipped = false) {\n        if (this.options.pedantic) {\n          src = src.replace(/\\t/g, '    ').replace(/^ +$/gm, '');\n        } else {\n          src = src.replace(/^( *)(\\t+)/gm, (_, leading, tabs) => {\n            return leading + '    '.repeat(tabs.length);\n          });\n        }\n        let token;\n        let lastToken;\n        let cutSrc;\n        while (src) {\n          if (this.options.extensions && this.options.extensions.block && this.options.extensions.block.some(extTokenizer => {\n            if (token = extTokenizer.call({\n              lexer: this\n            }, src, tokens)) {\n              src = src.substring(token.raw.length);\n              tokens.push(token);\n              return true;\n            }\n            return false;\n          })) {\n            continue;\n          }\n          // newline\n          if (token = this.tokenizer.space(src)) {\n            src = src.substring(token.raw.length);\n            if (token.raw.length === 1 && tokens.length > 0) {\n              // if there's a single \\n as a spacer, it's terminating the last line,\n              // so move it there so that we don't get unnecessary paragraph tags\n              tokens[tokens.length - 1].raw += '\\n';\n            } else {\n              tokens.push(token);\n            }\n            continue;\n          }\n          // code\n          if (token = this.tokenizer.code(src)) {\n            src = src.substring(token.raw.length);\n            lastToken = tokens[tokens.length - 1];\n            // An indented code block cannot interrupt a paragraph.\n            if (lastToken && (lastToken.type === 'paragraph' || lastToken.type === 'text')) {\n              lastToken.raw += '\\n' + token.raw;\n              lastToken.text += '\\n' + token.text;\n              this.inlineQueue[this.inlineQueue.length - 1].src = lastToken.text;\n            } else {\n              tokens.push(token);\n            }\n            continue;\n          }\n          // fences\n          if (token = this.tokenizer.fences(src)) {\n            src = src.substring(token.raw.length);\n            tokens.push(token);\n            continue;\n          }\n          // heading\n          if (token = this.tokenizer.heading(src)) {\n            src = src.substring(token.raw.length);\n            tokens.push(token);\n            continue;\n          }\n          // hr\n          if (token = this.tokenizer.hr(src)) {\n            src = src.substring(token.raw.length);\n            tokens.push(token);\n            continue;\n          }\n          // blockquote\n          if (token = this.tokenizer.blockquote(src)) {\n            src = src.substring(token.raw.length);\n            tokens.push(token);\n            continue;\n          }\n          // list\n          if (token = this.tokenizer.list(src)) {\n            src = src.substring(token.raw.length);\n            tokens.push(token);\n            continue;\n          }\n          // html\n          if (token = this.tokenizer.html(src)) {\n            src = src.substring(token.raw.length);\n            tokens.push(token);\n            continue;\n          }\n          // def\n          if (token = this.tokenizer.def(src)) {\n            src = src.substring(token.raw.length);\n            lastToken = tokens[tokens.length - 1];\n            if (lastToken && (lastToken.type === 'paragraph' || lastToken.type === 'text')) {\n              lastToken.raw += '\\n' + token.raw;\n              lastToken.text += '\\n' + token.raw;\n              this.inlineQueue[this.inlineQueue.length - 1].src = lastToken.text;\n            } else if (!this.tokens.links[token.tag]) {\n              this.tokens.links[token.tag] = {\n                href: token.href,\n                title: token.title\n              };\n            }\n            continue;\n          }\n          // table (gfm)\n          if (token = this.tokenizer.table(src)) {\n            src = src.substring(token.raw.length);\n            tokens.push(token);\n            continue;\n          }\n          // lheading\n          if (token = this.tokenizer.lheading(src)) {\n            src = src.substring(token.raw.length);\n            tokens.push(token);\n            continue;\n          }\n          // top-level paragraph\n          // prevent paragraph consuming extensions by clipping 'src' to extension start\n          cutSrc = src;\n          if (this.options.extensions && this.options.extensions.startBlock) {\n            let startIndex = Infinity;\n            const tempSrc = src.slice(1);\n            let tempStart;\n            this.options.extensions.startBlock.forEach(getStartIndex => {\n              tempStart = getStartIndex.call({\n                lexer: this\n              }, tempSrc);\n              if (typeof tempStart === 'number' && tempStart >= 0) {\n                startIndex = Math.min(startIndex, tempStart);\n              }\n            });\n            if (startIndex < Infinity && startIndex >= 0) {\n              cutSrc = src.substring(0, startIndex + 1);\n            }\n          }\n          if (this.state.top && (token = this.tokenizer.paragraph(cutSrc))) {\n            lastToken = tokens[tokens.length - 1];\n            if (lastParagraphClipped && lastToken?.type === 'paragraph') {\n              lastToken.raw += '\\n' + token.raw;\n              lastToken.text += '\\n' + token.text;\n              this.inlineQueue.pop();\n              this.inlineQueue[this.inlineQueue.length - 1].src = lastToken.text;\n            } else {\n              tokens.push(token);\n            }\n            lastParagraphClipped = cutSrc.length !== src.length;\n            src = src.substring(token.raw.length);\n            continue;\n          }\n          // text\n          if (token = this.tokenizer.text(src)) {\n            src = src.substring(token.raw.length);\n            lastToken = tokens[tokens.length - 1];\n            if (lastToken && lastToken.type === 'text') {\n              lastToken.raw += '\\n' + token.raw;\n              lastToken.text += '\\n' + token.text;\n              this.inlineQueue.pop();\n              this.inlineQueue[this.inlineQueue.length - 1].src = lastToken.text;\n            } else {\n              tokens.push(token);\n            }\n            continue;\n          }\n          if (src) {\n            const errMsg = 'Infinite loop on byte: ' + src.charCodeAt(0);\n            if (this.options.silent) {\n              console.error(errMsg);\n              break;\n            } else {\n              throw new Error(errMsg);\n            }\n          }\n        }\n        this.state.top = true;\n        return tokens;\n      }\n      inline(src, tokens = []) {\n        this.inlineQueue.push({\n          src,\n          tokens\n        });\n        return tokens;\n      }\n      /**\n       * Lexing/Compiling\n       */\n      inlineTokens(src, tokens = []) {\n        let token, lastToken, cutSrc;\n        // String with links masked to avoid interference with em and strong\n        let maskedSrc = src;\n        let match;\n        let keepPrevChar, prevChar;\n        // Mask out reflinks\n        if (this.tokens.links) {\n          const links = Object.keys(this.tokens.links);\n          if (links.length > 0) {\n            while ((match = this.tokenizer.rules.inline.reflinkSearch.exec(maskedSrc)) != null) {\n              if (links.includes(match[0].slice(match[0].lastIndexOf('[') + 1, -1))) {\n                maskedSrc = maskedSrc.slice(0, match.index) + '[' + 'a'.repeat(match[0].length - 2) + ']' + maskedSrc.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex);\n              }\n            }\n          }\n        }\n        // Mask out other blocks\n        while ((match = this.tokenizer.rules.inline.blockSkip.exec(maskedSrc)) != null) {\n          maskedSrc = maskedSrc.slice(0, match.index) + '[' + 'a'.repeat(match[0].length - 2) + ']' + maskedSrc.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);\n        }\n        // Mask out escaped characters\n        while ((match = this.tokenizer.rules.inline.anyPunctuation.exec(maskedSrc)) != null) {\n          maskedSrc = maskedSrc.slice(0, match.index) + '++' + maskedSrc.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);\n        }\n        while (src) {\n          if (!keepPrevChar) {\n            prevChar = '';\n          }\n          keepPrevChar = false;\n          // extensions\n          if (this.options.extensions && this.options.extensions.inline && this.options.extensions.inline.some(extTokenizer => {\n            if (token = extTokenizer.call({\n              lexer: this\n            }, src, tokens)) {\n              src = src.substring(token.raw.length);\n              tokens.push(token);\n              return true;\n            }\n            return false;\n          })) {\n            continue;\n          }\n          // escape\n          if (token = this.tokenizer.escape(src)) {\n            src = src.substring(token.raw.length);\n            tokens.push(token);\n            continue;\n          }\n          // tag\n          if (token = this.tokenizer.tag(src)) {\n            src = src.substring(token.raw.length);\n            lastToken = tokens[tokens.length - 1];\n            if (lastToken && token.type === 'text' && lastToken.type === 'text') {\n              lastToken.raw += token.raw;\n              lastToken.text += token.text;\n            } else {\n              tokens.push(token);\n            }\n            continue;\n          }\n          // link\n          if (token = this.tokenizer.link(src)) {\n            src = src.substring(token.raw.length);\n            tokens.push(token);\n            continue;\n          }\n          // reflink, nolink\n          if (token = this.tokenizer.reflink(src, this.tokens.links)) {\n            src = src.substring(token.raw.length);\n            lastToken = tokens[tokens.length - 1];\n            if (lastToken && token.type === 'text' && lastToken.type === 'text') {\n              lastToken.raw += token.raw;\n              lastToken.text += token.text;\n            } else {\n              tokens.push(token);\n            }\n            continue;\n          }\n          // em & strong\n          if (token = this.tokenizer.emStrong(src, maskedSrc, prevChar)) {\n            src = src.substring(token.raw.length);\n            tokens.push(token);\n            continue;\n          }\n          // code\n          if (token = this.tokenizer.codespan(src)) {\n            src = src.substring(token.raw.length);\n            tokens.push(token);\n            continue;\n          }\n          // br\n          if (token = this.tokenizer.br(src)) {\n            src = src.substring(token.raw.length);\n            tokens.push(token);\n            continue;\n          }\n          // del (gfm)\n          if (token = this.tokenizer.del(src)) {\n            src = src.substring(token.raw.length);\n            tokens.push(token);\n            continue;\n          }\n          // autolink\n          if (token = this.tokenizer.autolink(src)) {\n            src = src.substring(token.raw.length);\n            tokens.push(token);\n            continue;\n          }\n          // url (gfm)\n          if (!this.state.inLink && (token = this.tokenizer.url(src))) {\n            src = src.substring(token.raw.length);\n            tokens.push(token);\n            continue;\n          }\n          // text\n          // prevent inlineText consuming extensions by clipping 'src' to extension start\n          cutSrc = src;\n          if (this.options.extensions && this.options.extensions.startInline) {\n            let startIndex = Infinity;\n            const tempSrc = src.slice(1);\n            let tempStart;\n            this.options.extensions.startInline.forEach(getStartIndex => {\n              tempStart = getStartIndex.call({\n                lexer: this\n              }, tempSrc);\n              if (typeof tempStart === 'number' && tempStart >= 0) {\n                startIndex = Math.min(startIndex, tempStart);\n              }\n            });\n            if (startIndex < Infinity && startIndex >= 0) {\n              cutSrc = src.substring(0, startIndex + 1);\n            }\n          }\n          if (token = this.tokenizer.inlineText(cutSrc)) {\n            src = src.substring(token.raw.length);\n            if (token.raw.slice(-1) !== '_') {\n              // Track prevChar before string of ____ started\n              prevChar = token.raw.slice(-1);\n            }\n            keepPrevChar = true;\n            lastToken = tokens[tokens.length - 1];\n            if (lastToken && lastToken.type === 'text') {\n              lastToken.raw += token.raw;\n              lastToken.text += token.text;\n            } else {\n              tokens.push(token);\n            }\n            continue;\n          }\n          if (src) {\n            const errMsg = 'Infinite loop on byte: ' + src.charCodeAt(0);\n            if (this.options.silent) {\n              console.error(errMsg);\n              break;\n            } else {\n              throw new Error(errMsg);\n            }\n          }\n        }\n        return tokens;\n      }\n    }\n\n    /**\n     * Renderer\n     */\n    class _Renderer {\n      options;\n      parser; // set by the parser\n      constructor(options) {\n        this.options = options || exports.defaults;\n      }\n      space(token) {\n        return '';\n      }\n      code({\n        text,\n        lang,\n        escaped\n      }) {\n        const langString = (lang || '').match(/^\\S*/)?.[0];\n        const code = text.replace(/\\n$/, '') + '\\n';\n        if (!langString) {\n          return '<pre><code>' + (escaped ? code : escape$1(code, true)) + '</code></pre>\\n';\n        }\n        return '<pre><code class=\"language-' + escape$1(langString) + '\">' + (escaped ? code : escape$1(code, true)) + '</code></pre>\\n';\n      }\n      blockquote({\n        tokens\n      }) {\n        const body = this.parser.parse(tokens);\n        return `<blockquote>\\n${body}</blockquote>\\n`;\n      }\n      html({\n        text\n      }) {\n        return text;\n      }\n      heading({\n        tokens,\n        depth\n      }) {\n        return `<h${depth}>${this.parser.parseInline(tokens)}</h${depth}>\\n`;\n      }\n      hr(token) {\n        return '<hr>\\n';\n      }\n      list(token) {\n        const ordered = token.ordered;\n        const start = token.start;\n        let body = '';\n        for (let j = 0; j < token.items.length; j++) {\n          const item = token.items[j];\n          body += this.listitem(item);\n        }\n        const type = ordered ? 'ol' : 'ul';\n        const startAttr = ordered && start !== 1 ? ' start=\"' + start + '\"' : '';\n        return '<' + type + startAttr + '>\\n' + body + '</' + type + '>\\n';\n      }\n      listitem(item) {\n        let itemBody = '';\n        if (item.task) {\n          const checkbox = this.checkbox({\n            checked: !!item.checked\n          });\n          if (item.loose) {\n            if (item.tokens.length > 0 && item.tokens[0].type === 'paragraph') {\n              item.tokens[0].text = checkbox + ' ' + item.tokens[0].text;\n              if (item.tokens[0].tokens && item.tokens[0].tokens.length > 0 && item.tokens[0].tokens[0].type === 'text') {\n                item.tokens[0].tokens[0].text = checkbox + ' ' + item.tokens[0].tokens[0].text;\n              }\n            } else {\n              item.tokens.unshift({\n                type: 'text',\n                raw: checkbox + ' ',\n                text: checkbox + ' '\n              });\n            }\n          } else {\n            itemBody += checkbox + ' ';\n          }\n        }\n        itemBody += this.parser.parse(item.tokens, !!item.loose);\n        return `<li>${itemBody}</li>\\n`;\n      }\n      checkbox({\n        checked\n      }) {\n        return '<input ' + (checked ? 'checked=\"\" ' : '') + 'disabled=\"\" type=\"checkbox\">';\n      }\n      paragraph({\n        tokens\n      }) {\n        return `<p>${this.parser.parseInline(tokens)}</p>\\n`;\n      }\n      table(token) {\n        let header = '';\n        // header\n        let cell = '';\n        for (let j = 0; j < token.header.length; j++) {\n          cell += this.tablecell(token.header[j]);\n        }\n        header += this.tablerow({\n          text: cell\n        });\n        let body = '';\n        for (let j = 0; j < token.rows.length; j++) {\n          const row = token.rows[j];\n          cell = '';\n          for (let k = 0; k < row.length; k++) {\n            cell += this.tablecell(row[k]);\n          }\n          body += this.tablerow({\n            text: cell\n          });\n        }\n        if (body) body = `<tbody>${body}</tbody>`;\n        return '<table>\\n' + '<thead>\\n' + header + '</thead>\\n' + body + '</table>\\n';\n      }\n      tablerow({\n        text\n      }) {\n        return `<tr>\\n${text}</tr>\\n`;\n      }\n      tablecell(token) {\n        const content = this.parser.parseInline(token.tokens);\n        const type = token.header ? 'th' : 'td';\n        const tag = token.align ? `<${type} align=\"${token.align}\">` : `<${type}>`;\n        return tag + content + `</${type}>\\n`;\n      }\n      /**\n       * span level renderer\n       */\n      strong({\n        tokens\n      }) {\n        return `<strong>${this.parser.parseInline(tokens)}</strong>`;\n      }\n      em({\n        tokens\n      }) {\n        return `<em>${this.parser.parseInline(tokens)}</em>`;\n      }\n      codespan({\n        text\n      }) {\n        return `<code>${text}</code>`;\n      }\n      br(token) {\n        return '<br>';\n      }\n      del({\n        tokens\n      }) {\n        return `<del>${this.parser.parseInline(tokens)}</del>`;\n      }\n      link({\n        href,\n        title,\n        tokens\n      }) {\n        const text = this.parser.parseInline(tokens);\n        const cleanHref = cleanUrl(href);\n        if (cleanHref === null) {\n          return text;\n        }\n        href = cleanHref;\n        let out = '<a href=\"' + href + '\"';\n        if (title) {\n          out += ' title=\"' + title + '\"';\n        }\n        out += '>' + text + '</a>';\n        return out;\n      }\n      image({\n        href,\n        title,\n        text\n      }) {\n        const cleanHref = cleanUrl(href);\n        if (cleanHref === null) {\n          return text;\n        }\n        href = cleanHref;\n        let out = `<img src=\"${href}\" alt=\"${text}\"`;\n        if (title) {\n          out += ` title=\"${title}\"`;\n        }\n        out += '>';\n        return out;\n      }\n      text(token) {\n        return 'tokens' in token && token.tokens ? this.parser.parseInline(token.tokens) : token.text;\n      }\n    }\n\n    /**\n     * TextRenderer\n     * returns only the textual part of the token\n     */\n    class _TextRenderer {\n      // no need for block level renderers\n      strong({\n        text\n      }) {\n        return text;\n      }\n      em({\n        text\n      }) {\n        return text;\n      }\n      codespan({\n        text\n      }) {\n        return text;\n      }\n      del({\n        text\n      }) {\n        return text;\n      }\n      html({\n        text\n      }) {\n        return text;\n      }\n      text({\n        text\n      }) {\n        return text;\n      }\n      link({\n        text\n      }) {\n        return '' + text;\n      }\n      image({\n        text\n      }) {\n        return '' + text;\n      }\n      br() {\n        return '';\n      }\n    }\n\n    /**\n     * Parsing & Compiling\n     */\n    class _Parser {\n      options;\n      renderer;\n      textRenderer;\n      constructor(options) {\n        this.options = options || exports.defaults;\n        this.options.renderer = this.options.renderer || new _Renderer();\n        this.renderer = this.options.renderer;\n        this.renderer.options = this.options;\n        this.renderer.parser = this;\n        this.textRenderer = new _TextRenderer();\n      }\n      /**\n       * Static Parse Method\n       */\n      static parse(tokens, options) {\n        const parser = new _Parser(options);\n        return parser.parse(tokens);\n      }\n      /**\n       * Static Parse Inline Method\n       */\n      static parseInline(tokens, options) {\n        const parser = new _Parser(options);\n        return parser.parseInline(tokens);\n      }\n      /**\n       * Parse Loop\n       */\n      parse(tokens, top = true) {\n        let out = '';\n        for (let i = 0; i < tokens.length; i++) {\n          const anyToken = tokens[i];\n          // Run any renderer extensions\n          if (this.options.extensions && this.options.extensions.renderers && this.options.extensions.renderers[anyToken.type]) {\n            const genericToken = anyToken;\n            const ret = this.options.extensions.renderers[genericToken.type].call({\n              parser: this\n            }, genericToken);\n            if (ret !== false || !['space', 'hr', 'heading', 'code', 'table', 'blockquote', 'list', 'html', 'paragraph', 'text'].includes(genericToken.type)) {\n              out += ret || '';\n              continue;\n            }\n          }\n          const token = anyToken;\n          switch (token.type) {\n            case 'space':\n              {\n                out += this.renderer.space(token);\n                continue;\n              }\n            case 'hr':\n              {\n                out += this.renderer.hr(token);\n                continue;\n              }\n            case 'heading':\n              {\n                out += this.renderer.heading(token);\n                continue;\n              }\n            case 'code':\n              {\n                out += this.renderer.code(token);\n                continue;\n              }\n            case 'table':\n              {\n                out += this.renderer.table(token);\n                continue;\n              }\n            case 'blockquote':\n              {\n                out += this.renderer.blockquote(token);\n                continue;\n              }\n            case 'list':\n              {\n                out += this.renderer.list(token);\n                continue;\n              }\n            case 'html':\n              {\n                out += this.renderer.html(token);\n                continue;\n              }\n            case 'paragraph':\n              {\n                out += this.renderer.paragraph(token);\n                continue;\n              }\n            case 'text':\n              {\n                let textToken = token;\n                let body = this.renderer.text(textToken);\n                while (i + 1 < tokens.length && tokens[i + 1].type === 'text') {\n                  textToken = tokens[++i];\n                  body += '\\n' + this.renderer.text(textToken);\n                }\n                if (top) {\n                  out += this.renderer.paragraph({\n                    type: 'paragraph',\n                    raw: body,\n                    text: body,\n                    tokens: [{\n                      type: 'text',\n                      raw: body,\n                      text: body\n                    }]\n                  });\n                } else {\n                  out += body;\n                }\n                continue;\n              }\n            default:\n              {\n                const errMsg = 'Token with \"' + token.type + '\" type was not found.';\n                if (this.options.silent) {\n                  console.error(errMsg);\n                  return '';\n                } else {\n                  throw new Error(errMsg);\n                }\n              }\n          }\n        }\n        return out;\n      }\n      /**\n       * Parse Inline Tokens\n       */\n      parseInline(tokens, renderer) {\n        renderer = renderer || this.renderer;\n        let out = '';\n        for (let i = 0; i < tokens.length; i++) {\n          const anyToken = tokens[i];\n          // Run any renderer extensions\n          if (this.options.extensions && this.options.extensions.renderers && this.options.extensions.renderers[anyToken.type]) {\n            const ret = this.options.extensions.renderers[anyToken.type].call({\n              parser: this\n            }, anyToken);\n            if (ret !== false || !['escape', 'html', 'link', 'image', 'strong', 'em', 'codespan', 'br', 'del', 'text'].includes(anyToken.type)) {\n              out += ret || '';\n              continue;\n            }\n          }\n          const token = anyToken;\n          switch (token.type) {\n            case 'escape':\n              {\n                out += renderer.text(token);\n                break;\n              }\n            case 'html':\n              {\n                out += renderer.html(token);\n                break;\n              }\n            case 'link':\n              {\n                out += renderer.link(token);\n                break;\n              }\n            case 'image':\n              {\n                out += renderer.image(token);\n                break;\n              }\n            case 'strong':\n              {\n                out += renderer.strong(token);\n                break;\n              }\n            case 'em':\n              {\n                out += renderer.em(token);\n                break;\n              }\n            case 'codespan':\n              {\n                out += renderer.codespan(token);\n                break;\n              }\n            case 'br':\n              {\n                out += renderer.br(token);\n                break;\n              }\n            case 'del':\n              {\n                out += renderer.del(token);\n                break;\n              }\n            case 'text':\n              {\n                out += renderer.text(token);\n                break;\n              }\n            default:\n              {\n                const errMsg = 'Token with \"' + token.type + '\" type was not found.';\n                if (this.options.silent) {\n                  console.error(errMsg);\n                  return '';\n                } else {\n                  throw new Error(errMsg);\n                }\n              }\n          }\n        }\n        return out;\n      }\n    }\n    class _Hooks {\n      options;\n      constructor(options) {\n        this.options = options || exports.defaults;\n      }\n      static passThroughHooks = new Set(['preprocess', 'postprocess', 'processAllTokens']);\n      /**\n       * Process markdown before marked\n       */\n      preprocess(markdown) {\n        return markdown;\n      }\n      /**\n       * Process HTML after marked is finished\n       */\n      postprocess(html) {\n        return html;\n      }\n      /**\n       * Process all tokens before walk tokens\n       */\n      processAllTokens(tokens) {\n        return tokens;\n      }\n    }\n    class Marked {\n      defaults = _getDefaults();\n      options = this.setOptions;\n      parse = this.parseMarkdown(_Lexer.lex, _Parser.parse);\n      parseInline = this.parseMarkdown(_Lexer.lexInline, _Parser.parseInline);\n      Parser = _Parser;\n      Renderer = _Renderer;\n      TextRenderer = _TextRenderer;\n      Lexer = _Lexer;\n      Tokenizer = _Tokenizer;\n      Hooks = _Hooks;\n      constructor(...args) {\n        this.use(...args);\n      }\n      /**\n       * Run callback for every token\n       */\n      walkTokens(tokens, callback) {\n        let values = [];\n        for (const token of tokens) {\n          values = values.concat(callback.call(this, token));\n          switch (token.type) {\n            case 'table':\n              {\n                const tableToken = token;\n                for (const cell of tableToken.header) {\n                  values = values.concat(this.walkTokens(cell.tokens, callback));\n                }\n                for (const row of tableToken.rows) {\n                  for (const cell of row) {\n                    values = values.concat(this.walkTokens(cell.tokens, callback));\n                  }\n                }\n                break;\n              }\n            case 'list':\n              {\n                const listToken = token;\n                values = values.concat(this.walkTokens(listToken.items, callback));\n                break;\n              }\n            default:\n              {\n                const genericToken = token;\n                if (this.defaults.extensions?.childTokens?.[genericToken.type]) {\n                  this.defaults.extensions.childTokens[genericToken.type].forEach(childTokens => {\n                    const tokens = genericToken[childTokens].flat(Infinity);\n                    values = values.concat(this.walkTokens(tokens, callback));\n                  });\n                } else if (genericToken.tokens) {\n                  values = values.concat(this.walkTokens(genericToken.tokens, callback));\n                }\n              }\n          }\n        }\n        return values;\n      }\n      use(...args) {\n        const extensions = this.defaults.extensions || {\n          renderers: {},\n          childTokens: {}\n        };\n        args.forEach(pack => {\n          // copy options to new object\n          const opts = {\n            ...pack\n          };\n          // set async to true if it was set to true before\n          opts.async = this.defaults.async || opts.async || false;\n          // ==-- Parse \"addon\" extensions --== //\n          if (pack.extensions) {\n            pack.extensions.forEach(ext => {\n              if (!ext.name) {\n                throw new Error('extension name required');\n              }\n              if ('renderer' in ext) {\n                // Renderer extensions\n                const prevRenderer = extensions.renderers[ext.name];\n                if (prevRenderer) {\n                  // Replace extension with func to run new extension but fall back if false\n                  extensions.renderers[ext.name] = function (...args) {\n                    let ret = ext.renderer.apply(this, args);\n                    if (ret === false) {\n                      ret = prevRenderer.apply(this, args);\n                    }\n                    return ret;\n                  };\n                } else {\n                  extensions.renderers[ext.name] = ext.renderer;\n                }\n              }\n              if ('tokenizer' in ext) {\n                // Tokenizer Extensions\n                if (!ext.level || ext.level !== 'block' && ext.level !== 'inline') {\n                  throw new Error(\"extension level must be 'block' or 'inline'\");\n                }\n                const extLevel = extensions[ext.level];\n                if (extLevel) {\n                  extLevel.unshift(ext.tokenizer);\n                } else {\n                  extensions[ext.level] = [ext.tokenizer];\n                }\n                if (ext.start) {\n                  // Function to check for start of token\n                  if (ext.level === 'block') {\n                    if (extensions.startBlock) {\n                      extensions.startBlock.push(ext.start);\n                    } else {\n                      extensions.startBlock = [ext.start];\n                    }\n                  } else if (ext.level === 'inline') {\n                    if (extensions.startInline) {\n                      extensions.startInline.push(ext.start);\n                    } else {\n                      extensions.startInline = [ext.start];\n                    }\n                  }\n                }\n              }\n              if ('childTokens' in ext && ext.childTokens) {\n                // Child tokens to be visited by walkTokens\n                extensions.childTokens[ext.name] = ext.childTokens;\n              }\n            });\n            opts.extensions = extensions;\n          }\n          // ==-- Parse \"overwrite\" extensions --== //\n          if (pack.renderer) {\n            const renderer = this.defaults.renderer || new _Renderer(this.defaults);\n            for (const prop in pack.renderer) {\n              if (!(prop in renderer)) {\n                throw new Error(`renderer '${prop}' does not exist`);\n              }\n              if (['options', 'parser'].includes(prop)) {\n                // ignore options property\n                continue;\n              }\n              const rendererProp = prop;\n              const rendererFunc = pack.renderer[rendererProp];\n              const prevRenderer = renderer[rendererProp];\n              // Replace renderer with func to run extension, but fall back if false\n              renderer[rendererProp] = (...args) => {\n                let ret = rendererFunc.apply(renderer, args);\n                if (ret === false) {\n                  ret = prevRenderer.apply(renderer, args);\n                }\n                return ret || '';\n              };\n            }\n            opts.renderer = renderer;\n          }\n          if (pack.tokenizer) {\n            const tokenizer = this.defaults.tokenizer || new _Tokenizer(this.defaults);\n            for (const prop in pack.tokenizer) {\n              if (!(prop in tokenizer)) {\n                throw new Error(`tokenizer '${prop}' does not exist`);\n              }\n              if (['options', 'rules', 'lexer'].includes(prop)) {\n                // ignore options, rules, and lexer properties\n                continue;\n              }\n              const tokenizerProp = prop;\n              const tokenizerFunc = pack.tokenizer[tokenizerProp];\n              const prevTokenizer = tokenizer[tokenizerProp];\n              // Replace tokenizer with func to run extension, but fall back if false\n              // @ts-expect-error cannot type tokenizer function dynamically\n              tokenizer[tokenizerProp] = (...args) => {\n                let ret = tokenizerFunc.apply(tokenizer, args);\n                if (ret === false) {\n                  ret = prevTokenizer.apply(tokenizer, args);\n                }\n                return ret;\n              };\n            }\n            opts.tokenizer = tokenizer;\n          }\n          // ==-- Parse Hooks extensions --== //\n          if (pack.hooks) {\n            const hooks = this.defaults.hooks || new _Hooks();\n            for (const prop in pack.hooks) {\n              if (!(prop in hooks)) {\n                throw new Error(`hook '${prop}' does not exist`);\n              }\n              if (prop === 'options') {\n                // ignore options property\n                continue;\n              }\n              const hooksProp = prop;\n              const hooksFunc = pack.hooks[hooksProp];\n              const prevHook = hooks[hooksProp];\n              if (_Hooks.passThroughHooks.has(prop)) {\n                // @ts-expect-error cannot type hook function dynamically\n                hooks[hooksProp] = arg => {\n                  if (this.defaults.async) {\n                    return Promise.resolve(hooksFunc.call(hooks, arg)).then(ret => {\n                      return prevHook.call(hooks, ret);\n                    });\n                  }\n                  const ret = hooksFunc.call(hooks, arg);\n                  return prevHook.call(hooks, ret);\n                };\n              } else {\n                // @ts-expect-error cannot type hook function dynamically\n                hooks[hooksProp] = (...args) => {\n                  let ret = hooksFunc.apply(hooks, args);\n                  if (ret === false) {\n                    ret = prevHook.apply(hooks, args);\n                  }\n                  return ret;\n                };\n              }\n            }\n            opts.hooks = hooks;\n          }\n          // ==-- Parse WalkTokens extensions --== //\n          if (pack.walkTokens) {\n            const walkTokens = this.defaults.walkTokens;\n            const packWalktokens = pack.walkTokens;\n            opts.walkTokens = function (token) {\n              let values = [];\n              values.push(packWalktokens.call(this, token));\n              if (walkTokens) {\n                values = values.concat(walkTokens.call(this, token));\n              }\n              return values;\n            };\n          }\n          this.defaults = {\n            ...this.defaults,\n            ...opts\n          };\n        });\n        return this;\n      }\n      setOptions(opt) {\n        this.defaults = {\n          ...this.defaults,\n          ...opt\n        };\n        return this;\n      }\n      lexer(src, options) {\n        return _Lexer.lex(src, options ?? this.defaults);\n      }\n      parser(tokens, options) {\n        return _Parser.parse(tokens, options ?? this.defaults);\n      }\n      parseMarkdown(lexer, parser) {\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        const parse = (src, options) => {\n          const origOpt = {\n            ...options\n          };\n          const opt = {\n            ...this.defaults,\n            ...origOpt\n          };\n          const throwError = this.onError(!!opt.silent, !!opt.async);\n          // throw error if an extension set async to true but parse was called with async: false\n          if (this.defaults.async === true && origOpt.async === false) {\n            return throwError(new Error('marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise.'));\n          }\n          // throw error in case of non string input\n          if (typeof src === 'undefined' || src === null) {\n            return throwError(new Error('marked(): input parameter is undefined or null'));\n          }\n          if (typeof src !== 'string') {\n            return throwError(new Error('marked(): input parameter is of type ' + Object.prototype.toString.call(src) + ', string expected'));\n          }\n          if (opt.hooks) {\n            opt.hooks.options = opt;\n          }\n          if (opt.async) {\n            return Promise.resolve(opt.hooks ? opt.hooks.preprocess(src) : src).then(src => lexer(src, opt)).then(tokens => opt.hooks ? opt.hooks.processAllTokens(tokens) : tokens).then(tokens => opt.walkTokens ? Promise.all(this.walkTokens(tokens, opt.walkTokens)).then(() => tokens) : tokens).then(tokens => parser(tokens, opt)).then(html => opt.hooks ? opt.hooks.postprocess(html) : html).catch(throwError);\n          }\n          try {\n            if (opt.hooks) {\n              src = opt.hooks.preprocess(src);\n            }\n            let tokens = lexer(src, opt);\n            if (opt.hooks) {\n              tokens = opt.hooks.processAllTokens(tokens);\n            }\n            if (opt.walkTokens) {\n              this.walkTokens(tokens, opt.walkTokens);\n            }\n            let html = parser(tokens, opt);\n            if (opt.hooks) {\n              html = opt.hooks.postprocess(html);\n            }\n            return html;\n          } catch (e) {\n            return throwError(e);\n          }\n        };\n        return parse;\n      }\n      onError(silent, async) {\n        return e => {\n          e.message += '\\nPlease report this to https://github.com/markedjs/marked.';\n          if (silent) {\n            const msg = '<p>An error occurred:</p><pre>' + escape$1(e.message + '', true) + '</pre>';\n            if (async) {\n              return Promise.resolve(msg);\n            }\n            return msg;\n          }\n          if (async) {\n            return Promise.reject(e);\n          }\n          throw e;\n        };\n      }\n    }\n    const markedInstance = new Marked();\n    function marked(src, opt) {\n      return markedInstance.parse(src, opt);\n    }\n    /**\n     * Sets the default options.\n     *\n     * @param options Hash of options\n     */\n    marked.options = marked.setOptions = function (options) {\n      markedInstance.setOptions(options);\n      marked.defaults = markedInstance.defaults;\n      changeDefaults(marked.defaults);\n      return marked;\n    };\n    /**\n     * Gets the original marked default options.\n     */\n    marked.getDefaults = _getDefaults;\n    marked.defaults = exports.defaults;\n    /**\n     * Use Extension\n     */\n    marked.use = function (...args) {\n      markedInstance.use(...args);\n      marked.defaults = markedInstance.defaults;\n      changeDefaults(marked.defaults);\n      return marked;\n    };\n    /**\n     * Run callback for every token\n     */\n    marked.walkTokens = function (tokens, callback) {\n      return markedInstance.walkTokens(tokens, callback);\n    };\n    /**\n     * Compiles markdown to HTML without enclosing `p` tag.\n     *\n     * @param src String of markdown source to be compiled\n     * @param options Hash of options\n     * @return String of compiled HTML\n     */\n    marked.parseInline = markedInstance.parseInline;\n    /**\n     * Expose\n     */\n    marked.Parser = _Parser;\n    marked.parser = _Parser.parse;\n    marked.Renderer = _Renderer;\n    marked.TextRenderer = _TextRenderer;\n    marked.Lexer = _Lexer;\n    marked.lexer = _Lexer.lex;\n    marked.Tokenizer = _Tokenizer;\n    marked.Hooks = _Hooks;\n    marked.parse = marked;\n    const options = marked.options;\n    const setOptions = marked.setOptions;\n    const use = marked.use;\n    const walkTokens = marked.walkTokens;\n    const parseInline = marked.parseInline;\n    const parse = marked;\n    const parser = _Parser.parse;\n    const lexer = _Lexer.lex;\n    exports.Hooks = _Hooks;\n    exports.Lexer = _Lexer;\n    exports.Marked = Marked;\n    exports.Parser = _Parser;\n    exports.Renderer = _Renderer;\n    exports.TextRenderer = _TextRenderer;\n    exports.Tokenizer = _Tokenizer;\n    exports.getDefaults = _getDefaults;\n    exports.lexer = lexer;\n    exports.marked = marked;\n    exports.options = options;\n    exports.parse = parse;\n    exports.parseInline = parseInline;\n    exports.parser = parser;\n    exports.setOptions = setOptions;\n    exports.use = use;\n    exports.walkTokens = walkTokens;\n  });\n\n  // ESM-uncomment-begin\n})();\nexport var Hooks = __marked_exports.Hooks || exports.Hooks;\nexport var Lexer = __marked_exports.Lexer || exports.Lexer;\nexport var Marked = __marked_exports.Marked || exports.Marked;\nexport var Parser = __marked_exports.Parser || exports.Parser;\nexport var Renderer = __marked_exports.Renderer || exports.Renderer;\nexport var TextRenderer = __marked_exports.TextRenderer || exports.TextRenderer;\nexport var Tokenizer = __marked_exports.Tokenizer || exports.Tokenizer;\nexport var defaults = __marked_exports.defaults || exports.defaults;\nexport var getDefaults = __marked_exports.getDefaults || exports.getDefaults;\nexport var lexer = __marked_exports.lexer || exports.lexer;\nexport var marked = __marked_exports.marked || exports.marked;\nexport var options = __marked_exports.options || exports.options;\nexport var parse = __marked_exports.parse || exports.parse;\nexport var parseInline = __marked_exports.parseInline || exports.parseInline;\nexport var parser = __marked_exports.parser || exports.parser;\nexport var setOptions = __marked_exports.setOptions || exports.setOptions;\nexport var use = __marked_exports.use || exports.use;\nexport var walkTokens = __marked_exports.walkTokens || exports.walkTokens;\n// ESM-uncomment-end", "map": {"version": 3, "names": ["__marked_exports", "define", "deps", "factory", "amd", "global", "exports", "module", "globalThis", "self", "marked", "_getDefaults", "async", "breaks", "extensions", "gfm", "hooks", "pedantic", "renderer", "silent", "tokenizer", "walkTokens", "defaults", "changeDefaults", "newDefaults", "escapeTest", "escapeReplace", "RegExp", "source", "escapeTestNoEncode", "escapeReplaceNoEncode", "escapeReplacements", "getEscapeReplacement", "ch", "escape$1", "html", "encode", "test", "replace", "caret", "edit", "regex", "opt", "obj", "name", "val", "valSource", "getRegex", "cleanUrl", "href", "encodeURI", "noopTest", "exec", "splitCells", "tableRow", "count", "row", "match", "offset", "str", "escaped", "curr", "cells", "split", "i", "trim", "shift", "length", "pop", "splice", "push", "rtrim", "c", "invert", "l", "suffLen", "curr<PERSON>har", "char<PERSON>t", "slice", "findClosingBracket", "b", "indexOf", "level", "outputLink", "cap", "link", "raw", "lexer", "title", "text", "state", "inLink", "token", "type", "tokens", "inlineTokens", "indentCodeCompensation", "matchIndentToCode", "indentToCode", "map", "node", "matchIndentInNode", "indentInNode", "join", "_Tokenizer", "options", "rules", "constructor", "space", "src", "block", "newline", "code", "codeBlockStyle", "fences", "lang", "inline", "anyPunctuation", "heading", "trimmed", "depth", "hr", "blockquote", "lines", "inBlockquote", "currentLines", "currentRaw", "currentText", "top", "blockTokens", "lastToken", "oldToken", "newText", "newToken", "substring", "list", "bull", "isordered", "ordered", "start", "loose", "items", "itemRegex", "endsWithBlankLine", "endEarly", "itemContents", "line", "t", "repeat", "nextLine", "blankLine", "indent", "trimStart", "search", "nextBulletRegex", "Math", "min", "hrRegex", "fencesBeginRegex", "headingBeginRegex", "rawLine", "istask", "ischecked", "task", "checked", "trimEnd", "spacers", "filter", "hasMultipleLineBreaks", "some", "pre", "def", "tag", "toLowerCase", "table", "headers", "aligns", "rows", "item", "header", "align", "cell", "lheading", "paragraph", "escape", "inRawBlock", "trimmedUrl", "rtrimSlash", "lastParenIndex", "linkLen", "reflink", "links", "nolink", "linkString", "emStrong", "maskedSrc", "prevChar", "em<PERSON><PERSON>g<PERSON><PERSON><PERSON>", "nextChar", "punctuation", "l<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "r<PERSON><PERSON><PERSON>", "delimTotal", "midDelimTotal", "endReg", "emStrongRDelim<PERSON>t", "emStrongRDelimUnd", "lastIndex", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "index", "codespan", "hasNonSpaceChars", "hasSpaceCharsOnBothEnds", "br", "del", "autolink", "url", "prevCapZero", "_backpedal", "inlineText", "blockCode", "bullet", "_paragraph", "blockText", "_blockLabel", "_tag", "_comment", "blockNormal", "gfmTable", "blockGfm", "blockPedantic", "inlineCode", "_punctuation", "blockSkip", "_inlineComment", "_inlineLabel", "reflinkSearch", "inlineNormal", "inlinePedantic", "inlineGfm", "inlineBreaks", "normal", "_<PERSON>er", "inlineQueue", "Object", "create", "lex", "lexInline", "next", "lastParagraphClipped", "_", "leading", "tabs", "cutSrc", "extTokenizer", "call", "startBlock", "startIndex", "Infinity", "tempSrc", "tempStart", "for<PERSON>ach", "getStartIndex", "errMsg", "charCodeAt", "console", "error", "Error", "keepPrevChar", "keys", "includes", "lastIndexOf", "startInline", "_Renderer", "parser", "langString", "body", "parse", "parseInline", "j", "listitem", "startAttr", "itemBody", "checkbox", "unshift", "tablecell", "tablerow", "k", "content", "strong", "em", "cleanHref", "out", "image", "_<PERSON><PERSON><PERSON><PERSON>", "_<PERSON><PERSON>r", "<PERSON><PERSON><PERSON><PERSON>", "anyToken", "renderers", "genericToken", "ret", "textToken", "_Hooks", "passThroughHooks", "Set", "preprocess", "markdown", "postprocess", "processAllTokens", "Marked", "setOptions", "parseMarkdown", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Tokenizer", "<PERSON>s", "args", "use", "callback", "values", "concat", "tableToken", "listToken", "childTokens", "flat", "pack", "opts", "ext", "prev<PERSON><PERSON><PERSON>", "apply", "extLevel", "prop", "rendererProp", "rendererFunc", "tokenizerProp", "tokenizerFunc", "prevTokenizer", "hooksProp", "hooksFunc", "prevHook", "has", "arg", "Promise", "resolve", "then", "packWalktokens", "origOpt", "throwError", "onError", "prototype", "toString", "all", "catch", "e", "message", "msg", "reject", "markedInstance", "getDefaults"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/common/marked/marked.js"], "sourcesContent": ["/**\n * marked v14.0.0 - a markdown parser\n * Copyright (c) 2011-2024, <PERSON>. (MIT Licensed)\n * https://github.com/markedjs/marked\n */\n\n/**\n * DO NOT EDIT THIS FILE\n * The code in this file is generated from files in ./src/\n */\n\n// ESM-uncomment-begin\nlet __marked_exports = {};\n(function() {\n  function define(deps, factory) {\n    factory(__marked_exports);\n  }\n  define.amd = true;\n// ESM-uncomment-end\n\n(function (global, factory) {\n\ttypeof define === 'function' && define.amd ? define(['exports'], factory) :\n\ttypeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :\n\t(global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global.marked = {}));\n  })(this, (function (exports) {\n\t'use strict';\n\n\t/**\n\t * Gets the original marked default options.\n\t */\n\tfunction _getDefaults() {\n\t\treturn {\n\t\t\tasync: false,\n\t\t\tbreaks: false,\n\t\t\textensions: null,\n\t\t\tgfm: true,\n\t\t\thooks: null,\n\t\t\tpedantic: false,\n\t\t\trenderer: null,\n\t\t\tsilent: false,\n\t\t\ttokenizer: null,\n\t\t\twalkTokens: null,\n\t\t};\n\t}\n\texports.defaults = _getDefaults();\n\tfunction changeDefaults(newDefaults) {\n\t\texports.defaults = newDefaults;\n\t}\n\n\t/**\n\t * Helpers\n\t */\n\tconst escapeTest = /[&<>\"']/;\n\tconst escapeReplace = new RegExp(escapeTest.source, 'g');\n\tconst escapeTestNoEncode = /[<>\"']|&(?!(#\\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\\w+);)/;\n\tconst escapeReplaceNoEncode = new RegExp(escapeTestNoEncode.source, 'g');\n\tconst escapeReplacements = {\n\t\t'&': '&amp;',\n\t\t'<': '&lt;',\n\t\t'>': '&gt;',\n\t\t'\"': '&quot;',\n\t\t\"'\": '&#39;',\n\t};\n\tconst getEscapeReplacement = (ch) => escapeReplacements[ch];\n\tfunction escape$1(html, encode) {\n\t\tif (encode) {\n\t\t\tif (escapeTest.test(html)) {\n\t\t\t\treturn html.replace(escapeReplace, getEscapeReplacement);\n\t\t\t}\n\t\t}\n\t\telse {\n\t\t\tif (escapeTestNoEncode.test(html)) {\n\t\t\t\treturn html.replace(escapeReplaceNoEncode, getEscapeReplacement);\n\t\t\t}\n\t\t}\n\t\treturn html;\n\t}\n\tconst caret = /(^|[^\\[])\\^/g;\n\tfunction edit(regex, opt) {\n\t\tlet source = typeof regex === 'string' ? regex : regex.source;\n\t\topt = opt || '';\n\t\tconst obj = {\n\t\t\treplace: (name, val) => {\n\t\t\t\tlet valSource = typeof val === 'string' ? val : val.source;\n\t\t\t\tvalSource = valSource.replace(caret, '$1');\n\t\t\t\tsource = source.replace(name, valSource);\n\t\t\t\treturn obj;\n\t\t\t},\n\t\t\tgetRegex: () => {\n\t\t\t\treturn new RegExp(source, opt);\n\t\t\t},\n\t\t};\n\t\treturn obj;\n\t}\n\tfunction cleanUrl(href) {\n\t\ttry {\n\t\t\thref = encodeURI(href).replace(/%25/g, '%');\n\t\t}\n\t\tcatch {\n\t\t\treturn null;\n\t\t}\n\t\treturn href;\n\t}\n\tconst noopTest = { exec: () => null };\n\tfunction splitCells(tableRow, count) {\n\t\t// ensure that every cell-delimiting pipe has a space\n\t\t// before it to distinguish it from an escaped pipe\n\t\tconst row = tableRow.replace(/\\|/g, (match, offset, str) => {\n\t\t\tlet escaped = false;\n\t\t\tlet curr = offset;\n\t\t\twhile (--curr >= 0 && str[curr] === '\\\\')\n\t\t\t\tescaped = !escaped;\n\t\t\tif (escaped) {\n\t\t\t\t// odd number of slashes means | is escaped\n\t\t\t\t// so we leave it alone\n\t\t\t\treturn '|';\n\t\t\t}\n\t\t\telse {\n\t\t\t\t// add space before unescaped |\n\t\t\t\treturn ' |';\n\t\t\t}\n\t\t}), cells = row.split(/ \\|/);\n\t\tlet i = 0;\n\t\t// First/last cell in a row cannot be empty if it has no leading/trailing pipe\n\t\tif (!cells[0].trim()) {\n\t\t\tcells.shift();\n\t\t}\n\t\tif (cells.length > 0 && !cells[cells.length - 1].trim()) {\n\t\t\tcells.pop();\n\t\t}\n\t\tif (count) {\n\t\t\tif (cells.length > count) {\n\t\t\t\tcells.splice(count);\n\t\t\t}\n\t\t\telse {\n\t\t\t\twhile (cells.length < count)\n\t\t\t\t\tcells.push('');\n\t\t\t}\n\t\t}\n\t\tfor (; i < cells.length; i++) {\n\t\t\t// leading or trailing whitespace is ignored per the gfm spec\n\t\t\tcells[i] = cells[i].trim().replace(/\\\\\\|/g, '|');\n\t\t}\n\t\treturn cells;\n\t}\n\t/**\n\t * Remove trailing 'c's. Equivalent to str.replace(/c*$/, '').\n\t * /c*$/ is vulnerable to REDOS.\n\t *\n\t * @param str\n\t * @param c\n\t * @param invert Remove suffix of non-c chars instead. Default falsey.\n\t */\n\tfunction rtrim(str, c, invert) {\n\t\tconst l = str.length;\n\t\tif (l === 0) {\n\t\t\treturn '';\n\t\t}\n\t\t// Length of suffix matching the invert condition.\n\t\tlet suffLen = 0;\n\t\t// Step left until we fail to match the invert condition.\n\t\twhile (suffLen < l) {\n\t\t\tconst currChar = str.charAt(l - suffLen - 1);\n\t\t\tif (currChar === c && !invert) {\n\t\t\t\tsuffLen++;\n\t\t\t}\n\t\t\telse if (currChar !== c && invert) {\n\t\t\t\tsuffLen++;\n\t\t\t}\n\t\t\telse {\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t\treturn str.slice(0, l - suffLen);\n\t}\n\tfunction findClosingBracket(str, b) {\n\t\tif (str.indexOf(b[1]) === -1) {\n\t\t\treturn -1;\n\t\t}\n\t\tlet level = 0;\n\t\tfor (let i = 0; i < str.length; i++) {\n\t\t\tif (str[i] === '\\\\') {\n\t\t\t\ti++;\n\t\t\t}\n\t\t\telse if (str[i] === b[0]) {\n\t\t\t\tlevel++;\n\t\t\t}\n\t\t\telse if (str[i] === b[1]) {\n\t\t\t\tlevel--;\n\t\t\t\tif (level < 0) {\n\t\t\t\t\treturn i;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\treturn -1;\n\t}\n\n\tfunction outputLink(cap, link, raw, lexer) {\n\t\tconst href = link.href;\n\t\tconst title = link.title ? escape$1(link.title) : null;\n\t\tconst text = cap[1].replace(/\\\\([\\[\\]])/g, '$1');\n\t\tif (cap[0].charAt(0) !== '!') {\n\t\t\tlexer.state.inLink = true;\n\t\t\tconst token = {\n\t\t\t\ttype: 'link',\n\t\t\t\traw,\n\t\t\t\thref,\n\t\t\t\ttitle,\n\t\t\t\ttext,\n\t\t\t\ttokens: lexer.inlineTokens(text),\n\t\t\t};\n\t\t\tlexer.state.inLink = false;\n\t\t\treturn token;\n\t\t}\n\t\treturn {\n\t\t\ttype: 'image',\n\t\t\traw,\n\t\t\thref,\n\t\t\ttitle,\n\t\t\ttext: escape$1(text),\n\t\t};\n\t}\n\tfunction indentCodeCompensation(raw, text) {\n\t\tconst matchIndentToCode = raw.match(/^(\\s+)(?:```)/);\n\t\tif (matchIndentToCode === null) {\n\t\t\treturn text;\n\t\t}\n\t\tconst indentToCode = matchIndentToCode[1];\n\t\treturn text\n\t\t\t.split('\\n')\n\t\t\t.map(node => {\n\t\t\t\tconst matchIndentInNode = node.match(/^\\s+/);\n\t\t\t\tif (matchIndentInNode === null) {\n\t\t\t\t\treturn node;\n\t\t\t\t}\n\t\t\t\tconst [indentInNode] = matchIndentInNode;\n\t\t\t\tif (indentInNode.length >= indentToCode.length) {\n\t\t\t\t\treturn node.slice(indentToCode.length);\n\t\t\t\t}\n\t\t\t\treturn node;\n\t\t\t})\n\t\t\t.join('\\n');\n\t}\n\t/**\n\t * Tokenizer\n\t */\n\tclass _Tokenizer {\n\t\toptions;\n\t\trules; // set by the lexer\n\t\tlexer; // set by the lexer\n\t\tconstructor(options) {\n\t\t\tthis.options = options || exports.defaults;\n\t\t}\n\t\tspace(src) {\n\t\t\tconst cap = this.rules.block.newline.exec(src);\n\t\t\tif (cap && cap[0].length > 0) {\n\t\t\t\treturn {\n\t\t\t\t\ttype: 'space',\n\t\t\t\t\traw: cap[0],\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\t\tcode(src) {\n\t\t\tconst cap = this.rules.block.code.exec(src);\n\t\t\tif (cap) {\n\t\t\t\tconst text = cap[0].replace(/^ {1,4}/gm, '');\n\t\t\t\treturn {\n\t\t\t\t\ttype: 'code',\n\t\t\t\t\traw: cap[0],\n\t\t\t\t\tcodeBlockStyle: 'indented',\n\t\t\t\t\ttext: !this.options.pedantic\n\t\t\t\t\t\t? rtrim(text, '\\n')\n\t\t\t\t\t\t: text,\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\t\tfences(src) {\n\t\t\tconst cap = this.rules.block.fences.exec(src);\n\t\t\tif (cap) {\n\t\t\t\tconst raw = cap[0];\n\t\t\t\tconst text = indentCodeCompensation(raw, cap[3] || '');\n\t\t\t\treturn {\n\t\t\t\t\ttype: 'code',\n\t\t\t\t\traw,\n\t\t\t\t\tlang: cap[2] ? cap[2].trim().replace(this.rules.inline.anyPunctuation, '$1') : cap[2],\n\t\t\t\t\ttext,\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\t\theading(src) {\n\t\t\tconst cap = this.rules.block.heading.exec(src);\n\t\t\tif (cap) {\n\t\t\t\tlet text = cap[2].trim();\n\t\t\t\t// remove trailing #s\n\t\t\t\tif (/#$/.test(text)) {\n\t\t\t\t\tconst trimmed = rtrim(text, '#');\n\t\t\t\t\tif (this.options.pedantic) {\n\t\t\t\t\t\ttext = trimmed.trim();\n\t\t\t\t\t}\n\t\t\t\t\telse if (!trimmed || / $/.test(trimmed)) {\n\t\t\t\t\t\t// CommonMark requires space before trailing #s\n\t\t\t\t\t\ttext = trimmed.trim();\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn {\n\t\t\t\t\ttype: 'heading',\n\t\t\t\t\traw: cap[0],\n\t\t\t\t\tdepth: cap[1].length,\n\t\t\t\t\ttext,\n\t\t\t\t\ttokens: this.lexer.inline(text),\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\t\thr(src) {\n\t\t\tconst cap = this.rules.block.hr.exec(src);\n\t\t\tif (cap) {\n\t\t\t\treturn {\n\t\t\t\t\ttype: 'hr',\n\t\t\t\t\traw: rtrim(cap[0], '\\n'),\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\t\tblockquote(src) {\n\t\t\tconst cap = this.rules.block.blockquote.exec(src);\n\t\t\tif (cap) {\n\t\t\t\tlet lines = rtrim(cap[0], '\\n').split('\\n');\n\t\t\t\tlet raw = '';\n\t\t\t\tlet text = '';\n\t\t\t\tconst tokens = [];\n\t\t\t\twhile (lines.length > 0) {\n\t\t\t\t\tlet inBlockquote = false;\n\t\t\t\t\tconst currentLines = [];\n\t\t\t\t\tlet i;\n\t\t\t\t\tfor (i = 0; i < lines.length; i++) {\n\t\t\t\t\t\t// get lines up to a continuation\n\t\t\t\t\t\tif (/^ {0,3}>/.test(lines[i])) {\n\t\t\t\t\t\t\tcurrentLines.push(lines[i]);\n\t\t\t\t\t\t\tinBlockquote = true;\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse if (!inBlockquote) {\n\t\t\t\t\t\t\tcurrentLines.push(lines[i]);\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse {\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tlines = lines.slice(i);\n\t\t\t\t\tconst currentRaw = currentLines.join('\\n');\n\t\t\t\t\tconst currentText = currentRaw\n\t\t\t\t\t\t// precede setext continuation with 4 spaces so it isn't a setext\n\t\t\t\t\t\t.replace(/\\n {0,3}((?:=+|-+) *)(?=\\n|$)/g, '\\n    $1')\n\t\t\t\t\t\t.replace(/^ {0,3}>[ \\t]?/gm, '');\n\t\t\t\t\traw = raw ? `${raw}\\n${currentRaw}` : currentRaw;\n\t\t\t\t\ttext = text ? `${text}\\n${currentText}` : currentText;\n\t\t\t\t\t// parse blockquote lines as top level tokens\n\t\t\t\t\t// merge paragraphs if this is a continuation\n\t\t\t\t\tconst top = this.lexer.state.top;\n\t\t\t\t\tthis.lexer.state.top = true;\n\t\t\t\t\tthis.lexer.blockTokens(currentText, tokens, true);\n\t\t\t\t\tthis.lexer.state.top = top;\n\t\t\t\t\t// if there is no continuation then we are done\n\t\t\t\t\tif (lines.length === 0) {\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\tconst lastToken = tokens[tokens.length - 1];\n\t\t\t\t\tif (lastToken?.type === 'code') {\n\t\t\t\t\t\t// blockquote continuation cannot be preceded by a code block\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\telse if (lastToken?.type === 'blockquote') {\n\t\t\t\t\t\t// include continuation in nested blockquote\n\t\t\t\t\t\tconst oldToken = lastToken;\n\t\t\t\t\t\tconst newText = oldToken.raw + '\\n' + lines.join('\\n');\n\t\t\t\t\t\tconst newToken = this.blockquote(newText);\n\t\t\t\t\t\ttokens[tokens.length - 1] = newToken;\n\t\t\t\t\t\traw = raw.substring(0, raw.length - oldToken.raw.length) + newToken.raw;\n\t\t\t\t\t\ttext = text.substring(0, text.length - oldToken.text.length) + newToken.text;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\telse if (lastToken?.type === 'list') {\n\t\t\t\t\t\t// include continuation in nested list\n\t\t\t\t\t\tconst oldToken = lastToken;\n\t\t\t\t\t\tconst newText = oldToken.raw + '\\n' + lines.join('\\n');\n\t\t\t\t\t\tconst newToken = this.list(newText);\n\t\t\t\t\t\ttokens[tokens.length - 1] = newToken;\n\t\t\t\t\t\traw = raw.substring(0, raw.length - lastToken.raw.length) + newToken.raw;\n\t\t\t\t\t\ttext = text.substring(0, text.length - oldToken.raw.length) + newToken.raw;\n\t\t\t\t\t\tlines = newText.substring(tokens[tokens.length - 1].raw.length).split('\\n');\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn {\n\t\t\t\t\ttype: 'blockquote',\n\t\t\t\t\traw,\n\t\t\t\t\ttokens,\n\t\t\t\t\ttext,\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\t\tlist(src) {\n\t\t\tlet cap = this.rules.block.list.exec(src);\n\t\t\tif (cap) {\n\t\t\t\tlet bull = cap[1].trim();\n\t\t\t\tconst isordered = bull.length > 1;\n\t\t\t\tconst list = {\n\t\t\t\t\ttype: 'list',\n\t\t\t\t\traw: '',\n\t\t\t\t\tordered: isordered,\n\t\t\t\t\tstart: isordered ? +bull.slice(0, -1) : '',\n\t\t\t\t\tloose: false,\n\t\t\t\t\titems: [],\n\t\t\t\t};\n\t\t\t\tbull = isordered ? `\\\\d{1,9}\\\\${bull.slice(-1)}` : `\\\\${bull}`;\n\t\t\t\tif (this.options.pedantic) {\n\t\t\t\t\tbull = isordered ? bull : '[*+-]';\n\t\t\t\t}\n\t\t\t\t// Get next list item\n\t\t\t\tconst itemRegex = new RegExp(`^( {0,3}${bull})((?:[\\t ][^\\\\n]*)?(?:\\\\n|$))`);\n\t\t\t\tlet endsWithBlankLine = false;\n\t\t\t\t// Check if current bullet point can start a new List Item\n\t\t\t\twhile (src) {\n\t\t\t\t\tlet endEarly = false;\n\t\t\t\t\tlet raw = '';\n\t\t\t\t\tlet itemContents = '';\n\t\t\t\t\tif (!(cap = itemRegex.exec(src))) {\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\tif (this.rules.block.hr.test(src)) { // End list if bullet was actually HR (possibly move into itemRegex?)\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\traw = cap[0];\n\t\t\t\t\tsrc = src.substring(raw.length);\n\t\t\t\t\tlet line = cap[2].split('\\n', 1)[0].replace(/^\\t+/, (t) => ' '.repeat(3 * t.length));\n\t\t\t\t\tlet nextLine = src.split('\\n', 1)[0];\n\t\t\t\t\tlet blankLine = !line.trim();\n\t\t\t\t\tlet indent = 0;\n\t\t\t\t\tif (this.options.pedantic) {\n\t\t\t\t\t\tindent = 2;\n\t\t\t\t\t\titemContents = line.trimStart();\n\t\t\t\t\t}\n\t\t\t\t\telse if (blankLine) {\n\t\t\t\t\t\tindent = cap[1].length + 1;\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tindent = cap[2].search(/[^ ]/); // Find first non-space char\n\t\t\t\t\t\tindent = indent > 4 ? 1 : indent; // Treat indented code blocks (> 4 spaces) as having only 1 indent\n\t\t\t\t\t\titemContents = line.slice(indent);\n\t\t\t\t\t\tindent += cap[1].length;\n\t\t\t\t\t}\n\t\t\t\t\tif (blankLine && /^ *$/.test(nextLine)) { // Items begin with at most one blank line\n\t\t\t\t\t\traw += nextLine + '\\n';\n\t\t\t\t\t\tsrc = src.substring(nextLine.length + 1);\n\t\t\t\t\t\tendEarly = true;\n\t\t\t\t\t}\n\t\t\t\t\tif (!endEarly) {\n\t\t\t\t\t\tconst nextBulletRegex = new RegExp(`^ {0,${Math.min(3, indent - 1)}}(?:[*+-]|\\\\d{1,9}[.)])((?:[ \\t][^\\\\n]*)?(?:\\\\n|$))`);\n\t\t\t\t\t\tconst hrRegex = new RegExp(`^ {0,${Math.min(3, indent - 1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\\\* *){3,})(?:\\\\n+|$)`);\n\t\t\t\t\t\tconst fencesBeginRegex = new RegExp(`^ {0,${Math.min(3, indent - 1)}}(?:\\`\\`\\`|~~~)`);\n\t\t\t\t\t\tconst headingBeginRegex = new RegExp(`^ {0,${Math.min(3, indent - 1)}}#`);\n\t\t\t\t\t\t// Check if following lines should be included in List Item\n\t\t\t\t\t\twhile (src) {\n\t\t\t\t\t\t\tconst rawLine = src.split('\\n', 1)[0];\n\t\t\t\t\t\t\tnextLine = rawLine;\n\t\t\t\t\t\t\t// Re-align to follow commonmark nesting rules\n\t\t\t\t\t\t\tif (this.options.pedantic) {\n\t\t\t\t\t\t\t\tnextLine = nextLine.replace(/^ {1,4}(?=( {4})*[^ ])/g, '  ');\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t// End list item if found code fences\n\t\t\t\t\t\t\tif (fencesBeginRegex.test(nextLine)) {\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t// End list item if found start of new heading\n\t\t\t\t\t\t\tif (headingBeginRegex.test(nextLine)) {\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t// End list item if found start of new bullet\n\t\t\t\t\t\t\tif (nextBulletRegex.test(nextLine)) {\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t// Horizontal rule found\n\t\t\t\t\t\t\tif (hrRegex.test(src)) {\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (nextLine.search(/[^ ]/) >= indent || !nextLine.trim()) { // Dedent if possible\n\t\t\t\t\t\t\t\titemContents += '\\n' + nextLine.slice(indent);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\t// not enough indentation\n\t\t\t\t\t\t\t\tif (blankLine) {\n\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t// paragraph continuation unless last line was a different block level element\n\t\t\t\t\t\t\t\tif (line.search(/[^ ]/) >= 4) { // indented code block\n\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tif (fencesBeginRegex.test(line)) {\n\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tif (headingBeginRegex.test(line)) {\n\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tif (hrRegex.test(line)) {\n\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\titemContents += '\\n' + nextLine;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (!blankLine && !nextLine.trim()) { // Check if current line is blank\n\t\t\t\t\t\t\t\tblankLine = true;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\traw += rawLine + '\\n';\n\t\t\t\t\t\t\tsrc = src.substring(rawLine.length + 1);\n\t\t\t\t\t\t\tline = nextLine.slice(indent);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tif (!list.loose) {\n\t\t\t\t\t\t// If the previous item ended with a blank line, the list is loose\n\t\t\t\t\t\tif (endsWithBlankLine) {\n\t\t\t\t\t\t\tlist.loose = true;\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse if (/\\n *\\n *$/.test(raw)) {\n\t\t\t\t\t\t\tendsWithBlankLine = true;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tlet istask = null;\n\t\t\t\t\tlet ischecked;\n\t\t\t\t\t// Check for task list items\n\t\t\t\t\tif (this.options.gfm) {\n\t\t\t\t\t\tistask = /^\\[[ xX]\\] /.exec(itemContents);\n\t\t\t\t\t\tif (istask) {\n\t\t\t\t\t\t\tischecked = istask[0] !== '[ ] ';\n\t\t\t\t\t\t\titemContents = itemContents.replace(/^\\[[ xX]\\] +/, '');\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tlist.items.push({\n\t\t\t\t\t\ttype: 'list_item',\n\t\t\t\t\t\traw,\n\t\t\t\t\t\ttask: !!istask,\n\t\t\t\t\t\tchecked: ischecked,\n\t\t\t\t\t\tloose: false,\n\t\t\t\t\t\ttext: itemContents,\n\t\t\t\t\t\ttokens: [],\n\t\t\t\t\t});\n\t\t\t\t\tlist.raw += raw;\n\t\t\t\t}\n\t\t\t\t// Do not consume newlines at end of final item. Alternatively, make itemRegex *start* with any newlines to simplify/speed up endsWithBlankLine logic\n\t\t\t\tlist.items[list.items.length - 1].raw = list.items[list.items.length - 1].raw.trimEnd();\n\t\t\t\tlist.items[list.items.length - 1].text = list.items[list.items.length - 1].text.trimEnd();\n\t\t\t\tlist.raw = list.raw.trimEnd();\n\t\t\t\t// Item child tokens handled here at end because we needed to have the final item to trim it first\n\t\t\t\tfor (let i = 0; i < list.items.length; i++) {\n\t\t\t\t\tthis.lexer.state.top = false;\n\t\t\t\t\tlist.items[i].tokens = this.lexer.blockTokens(list.items[i].text, []);\n\t\t\t\t\tif (!list.loose) {\n\t\t\t\t\t\t// Check if list should be loose\n\t\t\t\t\t\tconst spacers = list.items[i].tokens.filter(t => t.type === 'space');\n\t\t\t\t\t\tconst hasMultipleLineBreaks = spacers.length > 0 && spacers.some(t => /\\n.*\\n/.test(t.raw));\n\t\t\t\t\t\tlist.loose = hasMultipleLineBreaks;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t// Set all items to loose if list is loose\n\t\t\t\tif (list.loose) {\n\t\t\t\t\tfor (let i = 0; i < list.items.length; i++) {\n\t\t\t\t\t\tlist.items[i].loose = true;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn list;\n\t\t\t}\n\t\t}\n\t\thtml(src) {\n\t\t\tconst cap = this.rules.block.html.exec(src);\n\t\t\tif (cap) {\n\t\t\t\tconst token = {\n\t\t\t\t\ttype: 'html',\n\t\t\t\t\tblock: true,\n\t\t\t\t\traw: cap[0],\n\t\t\t\t\tpre: cap[1] === 'pre' || cap[1] === 'script' || cap[1] === 'style',\n\t\t\t\t\ttext: cap[0],\n\t\t\t\t};\n\t\t\t\treturn token;\n\t\t\t}\n\t\t}\n\t\tdef(src) {\n\t\t\tconst cap = this.rules.block.def.exec(src);\n\t\t\tif (cap) {\n\t\t\t\tconst tag = cap[1].toLowerCase().replace(/\\s+/g, ' ');\n\t\t\t\tconst href = cap[2] ? cap[2].replace(/^<(.*)>$/, '$1').replace(this.rules.inline.anyPunctuation, '$1') : '';\n\t\t\t\tconst title = cap[3] ? cap[3].substring(1, cap[3].length - 1).replace(this.rules.inline.anyPunctuation, '$1') : cap[3];\n\t\t\t\treturn {\n\t\t\t\t\ttype: 'def',\n\t\t\t\t\ttag,\n\t\t\t\t\traw: cap[0],\n\t\t\t\t\thref,\n\t\t\t\t\ttitle,\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\t\ttable(src) {\n\t\t\tconst cap = this.rules.block.table.exec(src);\n\t\t\tif (!cap) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif (!/[:|]/.test(cap[2])) {\n\t\t\t\t// delimiter row must have a pipe (|) or colon (:) otherwise it is a setext heading\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tconst headers = splitCells(cap[1]);\n\t\t\tconst aligns = cap[2].replace(/^\\||\\| *$/g, '').split('|');\n\t\t\tconst rows = cap[3] && cap[3].trim() ? cap[3].replace(/\\n[ \\t]*$/, '').split('\\n') : [];\n\t\t\tconst item = {\n\t\t\t\ttype: 'table',\n\t\t\t\traw: cap[0],\n\t\t\t\theader: [],\n\t\t\t\talign: [],\n\t\t\t\trows: [],\n\t\t\t};\n\t\t\tif (headers.length !== aligns.length) {\n\t\t\t\t// header and align columns must be equal, rows can be different.\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tfor (const align of aligns) {\n\t\t\t\tif (/^ *-+: *$/.test(align)) {\n\t\t\t\t\titem.align.push('right');\n\t\t\t\t}\n\t\t\t\telse if (/^ *:-+: *$/.test(align)) {\n\t\t\t\t\titem.align.push('center');\n\t\t\t\t}\n\t\t\t\telse if (/^ *:-+ *$/.test(align)) {\n\t\t\t\t\titem.align.push('left');\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\titem.align.push(null);\n\t\t\t\t}\n\t\t\t}\n\t\t\tfor (let i = 0; i < headers.length; i++) {\n\t\t\t\titem.header.push({\n\t\t\t\t\ttext: headers[i],\n\t\t\t\t\ttokens: this.lexer.inline(headers[i]),\n\t\t\t\t\theader: true,\n\t\t\t\t\talign: item.align[i],\n\t\t\t\t});\n\t\t\t}\n\t\t\tfor (const row of rows) {\n\t\t\t\titem.rows.push(splitCells(row, item.header.length).map((cell, i) => {\n\t\t\t\t\treturn {\n\t\t\t\t\t\ttext: cell,\n\t\t\t\t\t\ttokens: this.lexer.inline(cell),\n\t\t\t\t\t\theader: false,\n\t\t\t\t\t\talign: item.align[i],\n\t\t\t\t\t};\n\t\t\t\t}));\n\t\t\t}\n\t\t\treturn item;\n\t\t}\n\t\tlheading(src) {\n\t\t\tconst cap = this.rules.block.lheading.exec(src);\n\t\t\tif (cap) {\n\t\t\t\treturn {\n\t\t\t\t\ttype: 'heading',\n\t\t\t\t\traw: cap[0],\n\t\t\t\t\tdepth: cap[2].charAt(0) === '=' ? 1 : 2,\n\t\t\t\t\ttext: cap[1],\n\t\t\t\t\ttokens: this.lexer.inline(cap[1]),\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\t\tparagraph(src) {\n\t\t\tconst cap = this.rules.block.paragraph.exec(src);\n\t\t\tif (cap) {\n\t\t\t\tconst text = cap[1].charAt(cap[1].length - 1) === '\\n'\n\t\t\t\t\t? cap[1].slice(0, -1)\n\t\t\t\t\t: cap[1];\n\t\t\t\treturn {\n\t\t\t\t\ttype: 'paragraph',\n\t\t\t\t\traw: cap[0],\n\t\t\t\t\ttext,\n\t\t\t\t\ttokens: this.lexer.inline(text),\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\t\ttext(src) {\n\t\t\tconst cap = this.rules.block.text.exec(src);\n\t\t\tif (cap) {\n\t\t\t\treturn {\n\t\t\t\t\ttype: 'text',\n\t\t\t\t\traw: cap[0],\n\t\t\t\t\ttext: cap[0],\n\t\t\t\t\ttokens: this.lexer.inline(cap[0]),\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\t\tescape(src) {\n\t\t\tconst cap = this.rules.inline.escape.exec(src);\n\t\t\tif (cap) {\n\t\t\t\treturn {\n\t\t\t\t\ttype: 'escape',\n\t\t\t\t\traw: cap[0],\n\t\t\t\t\ttext: escape$1(cap[1]),\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\t\ttag(src) {\n\t\t\tconst cap = this.rules.inline.tag.exec(src);\n\t\t\tif (cap) {\n\t\t\t\tif (!this.lexer.state.inLink && /^<a /i.test(cap[0])) {\n\t\t\t\t\tthis.lexer.state.inLink = true;\n\t\t\t\t}\n\t\t\t\telse if (this.lexer.state.inLink && /^<\\/a>/i.test(cap[0])) {\n\t\t\t\t\tthis.lexer.state.inLink = false;\n\t\t\t\t}\n\t\t\t\tif (!this.lexer.state.inRawBlock && /^<(pre|code|kbd|script)(\\s|>)/i.test(cap[0])) {\n\t\t\t\t\tthis.lexer.state.inRawBlock = true;\n\t\t\t\t}\n\t\t\t\telse if (this.lexer.state.inRawBlock && /^<\\/(pre|code|kbd|script)(\\s|>)/i.test(cap[0])) {\n\t\t\t\t\tthis.lexer.state.inRawBlock = false;\n\t\t\t\t}\n\t\t\t\treturn {\n\t\t\t\t\ttype: 'html',\n\t\t\t\t\traw: cap[0],\n\t\t\t\t\tinLink: this.lexer.state.inLink,\n\t\t\t\t\tinRawBlock: this.lexer.state.inRawBlock,\n\t\t\t\t\tblock: false,\n\t\t\t\t\ttext: cap[0],\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\t\tlink(src) {\n\t\t\tconst cap = this.rules.inline.link.exec(src);\n\t\t\tif (cap) {\n\t\t\t\tconst trimmedUrl = cap[2].trim();\n\t\t\t\tif (!this.options.pedantic && /^</.test(trimmedUrl)) {\n\t\t\t\t\t// commonmark requires matching angle brackets\n\t\t\t\t\tif (!(/>$/.test(trimmedUrl))) {\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\t// ending angle bracket cannot be escaped\n\t\t\t\t\tconst rtrimSlash = rtrim(trimmedUrl.slice(0, -1), '\\\\');\n\t\t\t\t\tif ((trimmedUrl.length - rtrimSlash.length) % 2 === 0) {\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\t// find closing parenthesis\n\t\t\t\t\tconst lastParenIndex = findClosingBracket(cap[2], '()');\n\t\t\t\t\tif (lastParenIndex > -1) {\n\t\t\t\t\t\tconst start = cap[0].indexOf('!') === 0 ? 5 : 4;\n\t\t\t\t\t\tconst linkLen = start + cap[1].length + lastParenIndex;\n\t\t\t\t\t\tcap[2] = cap[2].substring(0, lastParenIndex);\n\t\t\t\t\t\tcap[0] = cap[0].substring(0, linkLen).trim();\n\t\t\t\t\t\tcap[3] = '';\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tlet href = cap[2];\n\t\t\t\tlet title = '';\n\t\t\t\tif (this.options.pedantic) {\n\t\t\t\t\t// split pedantic href and title\n\t\t\t\t\tconst link = /^([^'\"]*[^\\s])\\s+(['\"])(.*)\\2/.exec(href);\n\t\t\t\t\tif (link) {\n\t\t\t\t\t\thref = link[1];\n\t\t\t\t\t\ttitle = link[3];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\ttitle = cap[3] ? cap[3].slice(1, -1) : '';\n\t\t\t\t}\n\t\t\t\thref = href.trim();\n\t\t\t\tif (/^</.test(href)) {\n\t\t\t\t\tif (this.options.pedantic && !(/>$/.test(trimmedUrl))) {\n\t\t\t\t\t\t// pedantic allows starting angle bracket without ending angle bracket\n\t\t\t\t\t\thref = href.slice(1);\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\thref = href.slice(1, -1);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn outputLink(cap, {\n\t\t\t\t\thref: href ? href.replace(this.rules.inline.anyPunctuation, '$1') : href,\n\t\t\t\t\ttitle: title ? title.replace(this.rules.inline.anyPunctuation, '$1') : title,\n\t\t\t\t}, cap[0], this.lexer);\n\t\t\t}\n\t\t}\n\t\treflink(src, links) {\n\t\t\tlet cap;\n\t\t\tif ((cap = this.rules.inline.reflink.exec(src))\n\t\t\t\t|| (cap = this.rules.inline.nolink.exec(src))) {\n\t\t\t\tconst linkString = (cap[2] || cap[1]).replace(/\\s+/g, ' ');\n\t\t\t\tconst link = links[linkString.toLowerCase()];\n\t\t\t\tif (!link) {\n\t\t\t\t\tconst text = cap[0].charAt(0);\n\t\t\t\t\treturn {\n\t\t\t\t\t\ttype: 'text',\n\t\t\t\t\t\traw: text,\n\t\t\t\t\t\ttext,\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t\treturn outputLink(cap, link, cap[0], this.lexer);\n\t\t\t}\n\t\t}\n\t\temStrong(src, maskedSrc, prevChar = '') {\n\t\t\tlet match = this.rules.inline.emStrongLDelim.exec(src);\n\t\t\tif (!match)\n\t\t\t\treturn;\n\t\t\t// _ can't be between two alphanumerics. \\p{L}\\p{N} includes non-english alphabet/numbers as well\n\t\t\tif (match[3] && prevChar.match(/[\\p{L}\\p{N}]/u))\n\t\t\t\treturn;\n\t\t\tconst nextChar = match[1] || match[2] || '';\n\t\t\tif (!nextChar || !prevChar || this.rules.inline.punctuation.exec(prevChar)) {\n\t\t\t\t// unicode Regex counts emoji as 1 char; spread into array for proper count (used multiple times below)\n\t\t\t\tconst lLength = [...match[0]].length - 1;\n\t\t\t\tlet rDelim, rLength, delimTotal = lLength, midDelimTotal = 0;\n\t\t\t\tconst endReg = match[0][0] === '*' ? this.rules.inline.emStrongRDelimAst : this.rules.inline.emStrongRDelimUnd;\n\t\t\t\tendReg.lastIndex = 0;\n\t\t\t\t// Clip maskedSrc to same section of string as src (move to lexer?)\n\t\t\t\tmaskedSrc = maskedSrc.slice(-1 * src.length + lLength);\n\t\t\t\twhile ((match = endReg.exec(maskedSrc)) != null) {\n\t\t\t\t\trDelim = match[1] || match[2] || match[3] || match[4] || match[5] || match[6];\n\t\t\t\t\tif (!rDelim)\n\t\t\t\t\t\tcontinue; // skip single * in __abc*abc__\n\t\t\t\t\trLength = [...rDelim].length;\n\t\t\t\t\tif (match[3] || match[4]) { // found another Left Delim\n\t\t\t\t\t\tdelimTotal += rLength;\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t\t\telse if (match[5] || match[6]) { // either Left or Right Delim\n\t\t\t\t\t\tif (lLength % 3 && !((lLength + rLength) % 3)) {\n\t\t\t\t\t\t\tmidDelimTotal += rLength;\n\t\t\t\t\t\t\tcontinue; // CommonMark Emphasis Rules 9-10\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tdelimTotal -= rLength;\n\t\t\t\t\tif (delimTotal > 0)\n\t\t\t\t\t\tcontinue; // Haven't found enough closing delimiters\n\t\t\t\t\t// Remove extra characters. *a*** -> *a*\n\t\t\t\t\trLength = Math.min(rLength, rLength + delimTotal + midDelimTotal);\n\t\t\t\t\t// char length can be >1 for unicode characters;\n\t\t\t\t\tconst lastCharLength = [...match[0]][0].length;\n\t\t\t\t\tconst raw = src.slice(0, lLength + match.index + lastCharLength + rLength);\n\t\t\t\t\t// Create `em` if smallest delimiter has odd char count. *a***\n\t\t\t\t\tif (Math.min(lLength, rLength) % 2) {\n\t\t\t\t\t\tconst text = raw.slice(1, -1);\n\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\ttype: 'em',\n\t\t\t\t\t\t\traw,\n\t\t\t\t\t\t\ttext,\n\t\t\t\t\t\t\ttokens: this.lexer.inlineTokens(text),\n\t\t\t\t\t\t};\n\t\t\t\t\t}\n\t\t\t\t\t// Create 'strong' if smallest delimiter has even char count. **a***\n\t\t\t\t\tconst text = raw.slice(2, -2);\n\t\t\t\t\treturn {\n\t\t\t\t\t\ttype: 'strong',\n\t\t\t\t\t\traw,\n\t\t\t\t\t\ttext,\n\t\t\t\t\t\ttokens: this.lexer.inlineTokens(text),\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\tcodespan(src) {\n\t\t\tconst cap = this.rules.inline.code.exec(src);\n\t\t\tif (cap) {\n\t\t\t\tlet text = cap[2].replace(/\\n/g, ' ');\n\t\t\t\tconst hasNonSpaceChars = /[^ ]/.test(text);\n\t\t\t\tconst hasSpaceCharsOnBothEnds = /^ /.test(text) && / $/.test(text);\n\t\t\t\tif (hasNonSpaceChars && hasSpaceCharsOnBothEnds) {\n\t\t\t\t\ttext = text.substring(1, text.length - 1);\n\t\t\t\t}\n\t\t\t\ttext = escape$1(text, true);\n\t\t\t\treturn {\n\t\t\t\t\ttype: 'codespan',\n\t\t\t\t\traw: cap[0],\n\t\t\t\t\ttext,\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\t\tbr(src) {\n\t\t\tconst cap = this.rules.inline.br.exec(src);\n\t\t\tif (cap) {\n\t\t\t\treturn {\n\t\t\t\t\ttype: 'br',\n\t\t\t\t\traw: cap[0],\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\t\tdel(src) {\n\t\t\tconst cap = this.rules.inline.del.exec(src);\n\t\t\tif (cap) {\n\t\t\t\treturn {\n\t\t\t\t\ttype: 'del',\n\t\t\t\t\traw: cap[0],\n\t\t\t\t\ttext: cap[2],\n\t\t\t\t\ttokens: this.lexer.inlineTokens(cap[2]),\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\t\tautolink(src) {\n\t\t\tconst cap = this.rules.inline.autolink.exec(src);\n\t\t\tif (cap) {\n\t\t\t\tlet text, href;\n\t\t\t\tif (cap[2] === '@') {\n\t\t\t\t\ttext = escape$1(cap[1]);\n\t\t\t\t\thref = 'mailto:' + text;\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\ttext = escape$1(cap[1]);\n\t\t\t\t\thref = text;\n\t\t\t\t}\n\t\t\t\treturn {\n\t\t\t\t\ttype: 'link',\n\t\t\t\t\traw: cap[0],\n\t\t\t\t\ttext,\n\t\t\t\t\thref,\n\t\t\t\t\ttokens: [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\ttype: 'text',\n\t\t\t\t\t\t\traw: text,\n\t\t\t\t\t\t\ttext,\n\t\t\t\t\t\t},\n\t\t\t\t\t],\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\t\turl(src) {\n\t\t\tlet cap;\n\t\t\tif (cap = this.rules.inline.url.exec(src)) {\n\t\t\t\tlet text, href;\n\t\t\t\tif (cap[2] === '@') {\n\t\t\t\t\ttext = escape$1(cap[0]);\n\t\t\t\t\thref = 'mailto:' + text;\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\t// do extended autolink path validation\n\t\t\t\t\tlet prevCapZero;\n\t\t\t\t\tdo {\n\t\t\t\t\t\tprevCapZero = cap[0];\n\t\t\t\t\t\tcap[0] = this.rules.inline._backpedal.exec(cap[0])?.[0] ?? '';\n\t\t\t\t\t} while (prevCapZero !== cap[0]);\n\t\t\t\t\ttext = escape$1(cap[0]);\n\t\t\t\t\tif (cap[1] === 'www.') {\n\t\t\t\t\t\thref = 'http://' + cap[0];\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\thref = cap[0];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn {\n\t\t\t\t\ttype: 'link',\n\t\t\t\t\traw: cap[0],\n\t\t\t\t\ttext,\n\t\t\t\t\thref,\n\t\t\t\t\ttokens: [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\ttype: 'text',\n\t\t\t\t\t\t\traw: text,\n\t\t\t\t\t\t\ttext,\n\t\t\t\t\t\t},\n\t\t\t\t\t],\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\t\tinlineText(src) {\n\t\t\tconst cap = this.rules.inline.text.exec(src);\n\t\t\tif (cap) {\n\t\t\t\tlet text;\n\t\t\t\tif (this.lexer.state.inRawBlock) {\n\t\t\t\t\ttext = cap[0];\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\ttext = escape$1(cap[0]);\n\t\t\t\t}\n\t\t\t\treturn {\n\t\t\t\t\ttype: 'text',\n\t\t\t\t\traw: cap[0],\n\t\t\t\t\ttext,\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * Block-Level Grammar\n\t */\n\tconst newline = /^(?: *(?:\\n|$))+/;\n\tconst blockCode = /^( {4}[^\\n]+(?:\\n(?: *(?:\\n|$))*)?)+/;\n\tconst fences = /^ {0,3}(`{3,}(?=[^`\\n]*(?:\\n|$))|~{3,})([^\\n]*)(?:\\n|$)(?:|([\\s\\S]*?)(?:\\n|$))(?: {0,3}\\1[~`]* *(?=\\n|$)|$)/;\n\tconst hr = /^ {0,3}((?:-[\\t ]*){3,}|(?:_[ \\t]*){3,}|(?:\\*[ \\t]*){3,})(?:\\n+|$)/;\n\tconst heading = /^ {0,3}(#{1,6})(?=\\s|$)(.*)(?:\\n+|$)/;\n\tconst bullet = /(?:[*+-]|\\d{1,9}[.)])/;\n\tconst lheading = edit(/^(?!bull |blockCode|fences|blockquote|heading|html)((?:.|\\n(?!\\s*?\\n|bull |blockCode|fences|blockquote|heading|html))+?)\\n {0,3}(=+|-+) *(?:\\n+|$)/)\n\t\t.replace(/bull/g, bullet) // lists can interrupt\n\t\t.replace(/blockCode/g, / {4}/) // indented code blocks can interrupt\n\t\t.replace(/fences/g, / {0,3}(?:`{3,}|~{3,})/) // fenced code blocks can interrupt\n\t\t.replace(/blockquote/g, / {0,3}>/) // blockquote can interrupt\n\t\t.replace(/heading/g, / {0,3}#{1,6}/) // ATX heading can interrupt\n\t\t.replace(/html/g, / {0,3}<[^\\n>]+>\\n/) // block html can interrupt\n\t\t.getRegex();\n\tconst _paragraph = /^([^\\n]+(?:\\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\\n)[^\\n]+)*)/;\n\tconst blockText = /^[^\\n]+/;\n\tconst _blockLabel = /(?!\\s*\\])(?:\\\\.|[^\\[\\]\\\\])+/;\n\tconst def = edit(/^ {0,3}\\[(label)\\]: *(?:\\n *)?([^<\\s][^\\s]*|<.*?>)(?:(?: +(?:\\n *)?| *\\n *)(title))? *(?:\\n+|$)/)\n\t\t.replace('label', _blockLabel)\n\t\t.replace('title', /(?:\"(?:\\\\\"?|[^\"\\\\])*\"|'[^'\\n]*(?:\\n[^'\\n]+)*\\n?'|\\([^()]*\\))/)\n\t\t.getRegex();\n\tconst list = edit(/^( {0,3}bull)([ \\t][^\\n]+?)?(?:\\n|$)/)\n\t\t.replace(/bull/g, bullet)\n\t\t.getRegex();\n\tconst _tag = 'address|article|aside|base|basefont|blockquote|body|caption'\n\t\t+ '|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption'\n\t\t+ '|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe'\n\t\t+ '|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option'\n\t\t+ '|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title'\n\t\t+ '|tr|track|ul';\n\tconst _comment = /<!--(?:-?>|[\\s\\S]*?(?:-->|$))/;\n\tconst html = edit('^ {0,3}(?:' // optional indentation\n\t\t+ '<(script|pre|style|textarea)[\\\\s>][\\\\s\\\\S]*?(?:</\\\\1>[^\\\\n]*\\\\n+|$)' // (1)\n\t\t+ '|comment[^\\\\n]*(\\\\n+|$)' // (2)\n\t\t+ '|<\\\\?[\\\\s\\\\S]*?(?:\\\\?>\\\\n*|$)' // (3)\n\t\t+ '|<![A-Z][\\\\s\\\\S]*?(?:>\\\\n*|$)' // (4)\n\t\t+ '|<!\\\\[CDATA\\\\[[\\\\s\\\\S]*?(?:\\\\]\\\\]>\\\\n*|$)' // (5)\n\t\t+ '|</?(tag)(?: +|\\\\n|/?>)[\\\\s\\\\S]*?(?:(?:\\\\n *)+\\\\n|$)' // (6)\n\t\t+ '|<(?!script|pre|style|textarea)([a-z][\\\\w-]*)(?:attribute)*? */?>(?=[ \\\\t]*(?:\\\\n|$))[\\\\s\\\\S]*?(?:(?:\\\\n *)+\\\\n|$)' // (7) open tag\n\t\t+ '|</(?!script|pre|style|textarea)[a-z][\\\\w-]*\\\\s*>(?=[ \\\\t]*(?:\\\\n|$))[\\\\s\\\\S]*?(?:(?:\\\\n *)+\\\\n|$)' // (7) closing tag\n\t\t+ ')', 'i')\n\t\t.replace('comment', _comment)\n\t\t.replace('tag', _tag)\n\t\t.replace('attribute', / +[a-zA-Z:_][\\w.:-]*(?: *= *\"[^\"\\n]*\"| *= *'[^'\\n]*'| *= *[^\\s\"'=<>`]+)?/)\n\t\t.getRegex();\n\tconst paragraph = edit(_paragraph)\n\t\t.replace('hr', hr)\n\t\t.replace('heading', ' {0,3}#{1,6}(?:\\\\s|$)')\n\t\t.replace('|lheading', '') // setext headings don't interrupt commonmark paragraphs\n\t\t.replace('|table', '')\n\t\t.replace('blockquote', ' {0,3}>')\n\t\t.replace('fences', ' {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n')\n\t\t.replace('list', ' {0,3}(?:[*+-]|1[.)]) ') // only lists starting from 1 can interrupt\n\t\t.replace('html', '</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)')\n\t\t.replace('tag', _tag) // pars can be interrupted by type (6) html blocks\n\t\t.getRegex();\n\tconst blockquote = edit(/^( {0,3}> ?(paragraph|[^\\n]*)(?:\\n|$))+/)\n\t\t.replace('paragraph', paragraph)\n\t\t.getRegex();\n\t/**\n\t * Normal Block Grammar\n\t */\n\tconst blockNormal = {\n\t\tblockquote,\n\t\tcode: blockCode,\n\t\tdef,\n\t\tfences,\n\t\theading,\n\t\thr,\n\t\thtml,\n\t\tlheading,\n\t\tlist,\n\t\tnewline,\n\t\tparagraph,\n\t\ttable: noopTest,\n\t\ttext: blockText,\n\t};\n\t/**\n\t * GFM Block Grammar\n\t */\n\tconst gfmTable = edit('^ *([^\\\\n ].*)\\\\n' // Header\n\t\t+ ' {0,3}((?:\\\\| *)?:?-+:? *(?:\\\\| *:?-+:? *)*(?:\\\\| *)?)' // Align\n\t\t+ '(?:\\\\n((?:(?! *\\\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\\\n|$))*)\\\\n*|$)') // Cells\n\t\t.replace('hr', hr)\n\t\t.replace('heading', ' {0,3}#{1,6}(?:\\\\s|$)')\n\t\t.replace('blockquote', ' {0,3}>')\n\t\t.replace('code', ' {4}[^\\\\n]')\n\t\t.replace('fences', ' {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n')\n\t\t.replace('list', ' {0,3}(?:[*+-]|1[.)]) ') // only lists starting from 1 can interrupt\n\t\t.replace('html', '</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)')\n\t\t.replace('tag', _tag) // tables can be interrupted by type (6) html blocks\n\t\t.getRegex();\n\tconst blockGfm = {\n\t\t...blockNormal,\n\t\ttable: gfmTable,\n\t\tparagraph: edit(_paragraph)\n\t\t\t.replace('hr', hr)\n\t\t\t.replace('heading', ' {0,3}#{1,6}(?:\\\\s|$)')\n\t\t\t.replace('|lheading', '') // setext headings don't interrupt commonmark paragraphs\n\t\t\t.replace('table', gfmTable) // interrupt paragraphs with table\n\t\t\t.replace('blockquote', ' {0,3}>')\n\t\t\t.replace('fences', ' {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n')\n\t\t\t.replace('list', ' {0,3}(?:[*+-]|1[.)]) ') // only lists starting from 1 can interrupt\n\t\t\t.replace('html', '</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)')\n\t\t\t.replace('tag', _tag) // pars can be interrupted by type (6) html blocks\n\t\t\t.getRegex(),\n\t};\n\t/**\n\t * Pedantic grammar (original John Gruber's loose markdown specification)\n\t */\n\tconst blockPedantic = {\n\t\t...blockNormal,\n\t\thtml: edit('^ *(?:comment *(?:\\\\n|\\\\s*$)'\n\t\t\t+ '|<(tag)[\\\\s\\\\S]+?</\\\\1> *(?:\\\\n{2,}|\\\\s*$)' // closed tag\n\t\t\t+ '|<tag(?:\"[^\"]*\"|\\'[^\\']*\\'|\\\\s[^\\'\"/>\\\\s]*)*?/?> *(?:\\\\n{2,}|\\\\s*$))')\n\t\t\t.replace('comment', _comment)\n\t\t\t.replace(/tag/g, '(?!(?:'\n\t\t\t\t+ 'a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub'\n\t\t\t\t+ '|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)'\n\t\t\t\t+ '\\\\b)\\\\w+(?!:|[^\\\\w\\\\s@]*@)\\\\b')\n\t\t\t.getRegex(),\n\t\tdef: /^ *\\[([^\\]]+)\\]: *<?([^\\s>]+)>?(?: +([\"(][^\\n]+[\")]))? *(?:\\n+|$)/,\n\t\theading: /^(#{1,6})(.*)(?:\\n+|$)/,\n\t\tfences: noopTest, // fences not supported\n\t\tlheading: /^(.+?)\\n {0,3}(=+|-+) *(?:\\n+|$)/,\n\t\tparagraph: edit(_paragraph)\n\t\t\t.replace('hr', hr)\n\t\t\t.replace('heading', ' *#{1,6} *[^\\n]')\n\t\t\t.replace('lheading', lheading)\n\t\t\t.replace('|table', '')\n\t\t\t.replace('blockquote', ' {0,3}>')\n\t\t\t.replace('|fences', '')\n\t\t\t.replace('|list', '')\n\t\t\t.replace('|html', '')\n\t\t\t.replace('|tag', '')\n\t\t\t.getRegex(),\n\t};\n\t/**\n\t * Inline-Level Grammar\n\t */\n\tconst escape = /^\\\\([!\"#$%&'()*+,\\-./:;<=>?@\\[\\]\\\\^_`{|}~])/;\n\tconst inlineCode = /^(`+)([^`]|[^`][\\s\\S]*?[^`])\\1(?!`)/;\n\tconst br = /^( {2,}|\\\\)\\n(?!\\s*$)/;\n\tconst inlineText = /^(`+|[^`])(?:(?= {2,}\\n)|[\\s\\S]*?(?:(?=[\\\\<!\\[`*_]|\\b_|$)|[^ ](?= {2,}\\n)))/;\n\t// list of unicode punctuation marks, plus any missing characters from CommonMark spec\n\tconst _punctuation = '\\\\p{P}\\\\p{S}';\n\tconst punctuation = edit(/^((?![*_])[\\spunctuation])/, 'u')\n\t\t.replace(/punctuation/g, _punctuation).getRegex();\n\t// sequences em should skip over [title](link), `code`, <html>\n\tconst blockSkip = /\\[[^[\\]]*?\\]\\([^\\(\\)]*?\\)|`[^`]*?`|<[^<>]*?>/g;\n\tconst emStrongLDelim = edit(/^(?:\\*+(?:((?!\\*)[punct])|[^\\s*]))|^_+(?:((?!_)[punct])|([^\\s_]))/, 'u')\n\t\t.replace(/punct/g, _punctuation)\n\t\t.getRegex();\n\tconst emStrongRDelimAst = edit('^[^_*]*?__[^_*]*?\\\\*[^_*]*?(?=__)' // Skip orphan inside strong\n\t\t+ '|[^*]+(?=[^*])' // Consume to delim\n\t\t+ '|(?!\\\\*)[punct](\\\\*+)(?=[\\\\s]|$)' // (1) #*** can only be a Right Delimiter\n\t\t+ '|[^punct\\\\s](\\\\*+)(?!\\\\*)(?=[punct\\\\s]|$)' // (2) a***#, a*** can only be a Right Delimiter\n\t\t+ '|(?!\\\\*)[punct\\\\s](\\\\*+)(?=[^punct\\\\s])' // (3) #***a, ***a can only be Left Delimiter\n\t\t+ '|[\\\\s](\\\\*+)(?!\\\\*)(?=[punct])' // (4) ***# can only be Left Delimiter\n\t\t+ '|(?!\\\\*)[punct](\\\\*+)(?!\\\\*)(?=[punct])' // (5) #***# can be either Left or Right Delimiter\n\t\t+ '|[^punct\\\\s](\\\\*+)(?=[^punct\\\\s])', 'gu') // (6) a***a can be either Left or Right Delimiter\n\t\t.replace(/punct/g, _punctuation)\n\t\t.getRegex();\n\t// (6) Not allowed for _\n\tconst emStrongRDelimUnd = edit('^[^_*]*?\\\\*\\\\*[^_*]*?_[^_*]*?(?=\\\\*\\\\*)' // Skip orphan inside strong\n\t\t+ '|[^_]+(?=[^_])' // Consume to delim\n\t\t+ '|(?!_)[punct](_+)(?=[\\\\s]|$)' // (1) #___ can only be a Right Delimiter\n\t\t+ '|[^punct\\\\s](_+)(?!_)(?=[punct\\\\s]|$)' // (2) a___#, a___ can only be a Right Delimiter\n\t\t+ '|(?!_)[punct\\\\s](_+)(?=[^punct\\\\s])' // (3) #___a, ___a can only be Left Delimiter\n\t\t+ '|[\\\\s](_+)(?!_)(?=[punct])' // (4) ___# can only be Left Delimiter\n\t\t+ '|(?!_)[punct](_+)(?!_)(?=[punct])', 'gu') // (5) #___# can be either Left or Right Delimiter\n\t\t.replace(/punct/g, _punctuation)\n\t\t.getRegex();\n\tconst anyPunctuation = edit(/\\\\([punct])/, 'gu')\n\t\t.replace(/punct/g, _punctuation)\n\t\t.getRegex();\n\tconst autolink = edit(/^<(scheme:[^\\s\\x00-\\x1f<>]*|email)>/)\n\t\t.replace('scheme', /[a-zA-Z][a-zA-Z0-9+.-]{1,31}/)\n\t\t.replace('email', /[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/)\n\t\t.getRegex();\n\tconst _inlineComment = edit(_comment).replace('(?:-->|$)', '-->').getRegex();\n\tconst tag = edit('^comment'\n\t\t+ '|^</[a-zA-Z][\\\\w:-]*\\\\s*>' // self-closing tag\n\t\t+ '|^<[a-zA-Z][\\\\w-]*(?:attribute)*?\\\\s*/?>' // open tag\n\t\t+ '|^<\\\\?[\\\\s\\\\S]*?\\\\?>' // processing instruction, e.g. <?php ?>\n\t\t+ '|^<![a-zA-Z]+\\\\s[\\\\s\\\\S]*?>' // declaration, e.g. <!DOCTYPE html>\n\t\t+ '|^<!\\\\[CDATA\\\\[[\\\\s\\\\S]*?\\\\]\\\\]>') // CDATA section\n\t\t.replace('comment', _inlineComment)\n\t\t.replace('attribute', /\\s+[a-zA-Z:_][\\w.:-]*(?:\\s*=\\s*\"[^\"]*\"|\\s*=\\s*'[^']*'|\\s*=\\s*[^\\s\"'=<>`]+)?/)\n\t\t.getRegex();\n\tconst _inlineLabel = /(?:\\[(?:\\\\.|[^\\[\\]\\\\])*\\]|\\\\.|`[^`]*`|[^\\[\\]\\\\`])*?/;\n\tconst link = edit(/^!?\\[(label)\\]\\(\\s*(href)(?:\\s+(title))?\\s*\\)/)\n\t\t.replace('label', _inlineLabel)\n\t\t.replace('href', /<(?:\\\\.|[^\\n<>\\\\])+>|[^\\s\\x00-\\x1f]*/)\n\t\t.replace('title', /\"(?:\\\\\"?|[^\"\\\\])*\"|'(?:\\\\'?|[^'\\\\])*'|\\((?:\\\\\\)?|[^)\\\\])*\\)/)\n\t\t.getRegex();\n\tconst reflink = edit(/^!?\\[(label)\\]\\[(ref)\\]/)\n\t\t.replace('label', _inlineLabel)\n\t\t.replace('ref', _blockLabel)\n\t\t.getRegex();\n\tconst nolink = edit(/^!?\\[(ref)\\](?:\\[\\])?/)\n\t\t.replace('ref', _blockLabel)\n\t\t.getRegex();\n\tconst reflinkSearch = edit('reflink|nolink(?!\\\\()', 'g')\n\t\t.replace('reflink', reflink)\n\t\t.replace('nolink', nolink)\n\t\t.getRegex();\n\t/**\n\t * Normal Inline Grammar\n\t */\n\tconst inlineNormal = {\n\t\t_backpedal: noopTest, // only used for GFM url\n\t\tanyPunctuation,\n\t\tautolink,\n\t\tblockSkip,\n\t\tbr,\n\t\tcode: inlineCode,\n\t\tdel: noopTest,\n\t\temStrongLDelim,\n\t\temStrongRDelimAst,\n\t\temStrongRDelimUnd,\n\t\tescape,\n\t\tlink,\n\t\tnolink,\n\t\tpunctuation,\n\t\treflink,\n\t\treflinkSearch,\n\t\ttag,\n\t\ttext: inlineText,\n\t\turl: noopTest,\n\t};\n\t/**\n\t * Pedantic Inline Grammar\n\t */\n\tconst inlinePedantic = {\n\t\t...inlineNormal,\n\t\tlink: edit(/^!?\\[(label)\\]\\((.*?)\\)/)\n\t\t\t.replace('label', _inlineLabel)\n\t\t\t.getRegex(),\n\t\treflink: edit(/^!?\\[(label)\\]\\s*\\[([^\\]]*)\\]/)\n\t\t\t.replace('label', _inlineLabel)\n\t\t\t.getRegex(),\n\t};\n\t/**\n\t * GFM Inline Grammar\n\t */\n\tconst inlineGfm = {\n\t\t...inlineNormal,\n\t\tescape: edit(escape).replace('])', '~|])').getRegex(),\n\t\turl: edit(/^((?:ftp|https?):\\/\\/|www\\.)(?:[a-zA-Z0-9\\-]+\\.?)+[^\\s<]*|^email/, 'i')\n\t\t\t.replace('email', /[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/)\n\t\t\t.getRegex(),\n\t\t_backpedal: /(?:[^?!.,:;*_'\"~()&]+|\\([^)]*\\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'\"~)]+(?!$))+/,\n\t\tdel: /^(~~?)(?=[^\\s~])([\\s\\S]*?[^\\s~])\\1(?=[^~]|$)/,\n\t\ttext: /^([`~]+|[^`~])(?:(?= {2,}\\n)|(?=[a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-]+@)|[\\s\\S]*?(?:(?=[\\\\<!\\[`*~_]|\\b_|https?:\\/\\/|ftp:\\/\\/|www\\.|$)|[^ ](?= {2,}\\n)|[^a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-](?=[a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-]+@)))/,\n\t};\n\t/**\n\t * GFM + Line Breaks Inline Grammar\n\t */\n\tconst inlineBreaks = {\n\t\t...inlineGfm,\n\t\tbr: edit(br).replace('{2,}', '*').getRegex(),\n\t\ttext: edit(inlineGfm.text)\n\t\t\t.replace('\\\\b_', '\\\\b_| {2,}\\\\n')\n\t\t\t.replace(/\\{2,\\}/g, '*')\n\t\t\t.getRegex(),\n\t};\n\t/**\n\t * exports\n\t */\n\tconst block = {\n\t\tnormal: blockNormal,\n\t\tgfm: blockGfm,\n\t\tpedantic: blockPedantic,\n\t};\n\tconst inline = {\n\t\tnormal: inlineNormal,\n\t\tgfm: inlineGfm,\n\t\tbreaks: inlineBreaks,\n\t\tpedantic: inlinePedantic,\n\t};\n\n\t/**\n\t * Block Lexer\n\t */\n\tclass _Lexer {\n\t\ttokens;\n\t\toptions;\n\t\tstate;\n\t\ttokenizer;\n\t\tinlineQueue;\n\t\tconstructor(options) {\n\t\t\t// TokenList cannot be created in one go\n\t\t\tthis.tokens = [];\n\t\t\tthis.tokens.links = Object.create(null);\n\t\t\tthis.options = options || exports.defaults;\n\t\t\tthis.options.tokenizer = this.options.tokenizer || new _Tokenizer();\n\t\t\tthis.tokenizer = this.options.tokenizer;\n\t\t\tthis.tokenizer.options = this.options;\n\t\t\tthis.tokenizer.lexer = this;\n\t\t\tthis.inlineQueue = [];\n\t\t\tthis.state = {\n\t\t\t\tinLink: false,\n\t\t\t\tinRawBlock: false,\n\t\t\t\ttop: true,\n\t\t\t};\n\t\t\tconst rules = {\n\t\t\t\tblock: block.normal,\n\t\t\t\tinline: inline.normal,\n\t\t\t};\n\t\t\tif (this.options.pedantic) {\n\t\t\t\trules.block = block.pedantic;\n\t\t\t\trules.inline = inline.pedantic;\n\t\t\t}\n\t\t\telse if (this.options.gfm) {\n\t\t\t\trules.block = block.gfm;\n\t\t\t\tif (this.options.breaks) {\n\t\t\t\t\trules.inline = inline.breaks;\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\trules.inline = inline.gfm;\n\t\t\t\t}\n\t\t\t}\n\t\t\tthis.tokenizer.rules = rules;\n\t\t}\n\t\t/**\n\t\t * Expose Rules\n\t\t */\n\t\tstatic get rules() {\n\t\t\treturn {\n\t\t\t\tblock,\n\t\t\t\tinline,\n\t\t\t};\n\t\t}\n\t\t/**\n\t\t * Static Lex Method\n\t\t */\n\t\tstatic lex(src, options) {\n\t\t\tconst lexer = new _Lexer(options);\n\t\t\treturn lexer.lex(src);\n\t\t}\n\t\t/**\n\t\t * Static Lex Inline Method\n\t\t */\n\t\tstatic lexInline(src, options) {\n\t\t\tconst lexer = new _Lexer(options);\n\t\t\treturn lexer.inlineTokens(src);\n\t\t}\n\t\t/**\n\t\t * Preprocessing\n\t\t */\n\t\tlex(src) {\n\t\t\tsrc = src\n\t\t\t\t.replace(/\\r\\n|\\r/g, '\\n');\n\t\t\tthis.blockTokens(src, this.tokens);\n\t\t\tfor (let i = 0; i < this.inlineQueue.length; i++) {\n\t\t\t\tconst next = this.inlineQueue[i];\n\t\t\t\tthis.inlineTokens(next.src, next.tokens);\n\t\t\t}\n\t\t\tthis.inlineQueue = [];\n\t\t\treturn this.tokens;\n\t\t}\n\t\tblockTokens(src, tokens = [], lastParagraphClipped = false) {\n\t\t\tif (this.options.pedantic) {\n\t\t\t\tsrc = src.replace(/\\t/g, '    ').replace(/^ +$/gm, '');\n\t\t\t}\n\t\t\telse {\n\t\t\t\tsrc = src.replace(/^( *)(\\t+)/gm, (_, leading, tabs) => {\n\t\t\t\t\treturn leading + '    '.repeat(tabs.length);\n\t\t\t\t});\n\t\t\t}\n\t\t\tlet token;\n\t\t\tlet lastToken;\n\t\t\tlet cutSrc;\n\t\t\twhile (src) {\n\t\t\t\tif (this.options.extensions\n\t\t\t\t\t&& this.options.extensions.block\n\t\t\t\t\t&& this.options.extensions.block.some((extTokenizer) => {\n\t\t\t\t\t\tif (token = extTokenizer.call({ lexer: this }, src, tokens)) {\n\t\t\t\t\t\t\tsrc = src.substring(token.raw.length);\n\t\t\t\t\t\t\ttokens.push(token);\n\t\t\t\t\t\t\treturn true;\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t})) {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\t// newline\n\t\t\t\tif (token = this.tokenizer.space(src)) {\n\t\t\t\t\tsrc = src.substring(token.raw.length);\n\t\t\t\t\tif (token.raw.length === 1 && tokens.length > 0) {\n\t\t\t\t\t\t// if there's a single \\n as a spacer, it's terminating the last line,\n\t\t\t\t\t\t// so move it there so that we don't get unnecessary paragraph tags\n\t\t\t\t\t\ttokens[tokens.length - 1].raw += '\\n';\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\ttokens.push(token);\n\t\t\t\t\t}\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\t// code\n\t\t\t\tif (token = this.tokenizer.code(src)) {\n\t\t\t\t\tsrc = src.substring(token.raw.length);\n\t\t\t\t\tlastToken = tokens[tokens.length - 1];\n\t\t\t\t\t// An indented code block cannot interrupt a paragraph.\n\t\t\t\t\tif (lastToken && (lastToken.type === 'paragraph' || lastToken.type === 'text')) {\n\t\t\t\t\t\tlastToken.raw += '\\n' + token.raw;\n\t\t\t\t\t\tlastToken.text += '\\n' + token.text;\n\t\t\t\t\t\tthis.inlineQueue[this.inlineQueue.length - 1].src = lastToken.text;\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\ttokens.push(token);\n\t\t\t\t\t}\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\t// fences\n\t\t\t\tif (token = this.tokenizer.fences(src)) {\n\t\t\t\t\tsrc = src.substring(token.raw.length);\n\t\t\t\t\ttokens.push(token);\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\t// heading\n\t\t\t\tif (token = this.tokenizer.heading(src)) {\n\t\t\t\t\tsrc = src.substring(token.raw.length);\n\t\t\t\t\ttokens.push(token);\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\t// hr\n\t\t\t\tif (token = this.tokenizer.hr(src)) {\n\t\t\t\t\tsrc = src.substring(token.raw.length);\n\t\t\t\t\ttokens.push(token);\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\t// blockquote\n\t\t\t\tif (token = this.tokenizer.blockquote(src)) {\n\t\t\t\t\tsrc = src.substring(token.raw.length);\n\t\t\t\t\ttokens.push(token);\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\t// list\n\t\t\t\tif (token = this.tokenizer.list(src)) {\n\t\t\t\t\tsrc = src.substring(token.raw.length);\n\t\t\t\t\ttokens.push(token);\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\t// html\n\t\t\t\tif (token = this.tokenizer.html(src)) {\n\t\t\t\t\tsrc = src.substring(token.raw.length);\n\t\t\t\t\ttokens.push(token);\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\t// def\n\t\t\t\tif (token = this.tokenizer.def(src)) {\n\t\t\t\t\tsrc = src.substring(token.raw.length);\n\t\t\t\t\tlastToken = tokens[tokens.length - 1];\n\t\t\t\t\tif (lastToken && (lastToken.type === 'paragraph' || lastToken.type === 'text')) {\n\t\t\t\t\t\tlastToken.raw += '\\n' + token.raw;\n\t\t\t\t\t\tlastToken.text += '\\n' + token.raw;\n\t\t\t\t\t\tthis.inlineQueue[this.inlineQueue.length - 1].src = lastToken.text;\n\t\t\t\t\t}\n\t\t\t\t\telse if (!this.tokens.links[token.tag]) {\n\t\t\t\t\t\tthis.tokens.links[token.tag] = {\n\t\t\t\t\t\t\thref: token.href,\n\t\t\t\t\t\t\ttitle: token.title,\n\t\t\t\t\t\t};\n\t\t\t\t\t}\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\t// table (gfm)\n\t\t\t\tif (token = this.tokenizer.table(src)) {\n\t\t\t\t\tsrc = src.substring(token.raw.length);\n\t\t\t\t\ttokens.push(token);\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\t// lheading\n\t\t\t\tif (token = this.tokenizer.lheading(src)) {\n\t\t\t\t\tsrc = src.substring(token.raw.length);\n\t\t\t\t\ttokens.push(token);\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\t// top-level paragraph\n\t\t\t\t// prevent paragraph consuming extensions by clipping 'src' to extension start\n\t\t\t\tcutSrc = src;\n\t\t\t\tif (this.options.extensions && this.options.extensions.startBlock) {\n\t\t\t\t\tlet startIndex = Infinity;\n\t\t\t\t\tconst tempSrc = src.slice(1);\n\t\t\t\t\tlet tempStart;\n\t\t\t\t\tthis.options.extensions.startBlock.forEach((getStartIndex) => {\n\t\t\t\t\t\ttempStart = getStartIndex.call({ lexer: this }, tempSrc);\n\t\t\t\t\t\tif (typeof tempStart === 'number' && tempStart >= 0) {\n\t\t\t\t\t\t\tstartIndex = Math.min(startIndex, tempStart);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\tif (startIndex < Infinity && startIndex >= 0) {\n\t\t\t\t\t\tcutSrc = src.substring(0, startIndex + 1);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (this.state.top && (token = this.tokenizer.paragraph(cutSrc))) {\n\t\t\t\t\tlastToken = tokens[tokens.length - 1];\n\t\t\t\t\tif (lastParagraphClipped && lastToken?.type === 'paragraph') {\n\t\t\t\t\t\tlastToken.raw += '\\n' + token.raw;\n\t\t\t\t\t\tlastToken.text += '\\n' + token.text;\n\t\t\t\t\t\tthis.inlineQueue.pop();\n\t\t\t\t\t\tthis.inlineQueue[this.inlineQueue.length - 1].src = lastToken.text;\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\ttokens.push(token);\n\t\t\t\t\t}\n\t\t\t\t\tlastParagraphClipped = (cutSrc.length !== src.length);\n\t\t\t\t\tsrc = src.substring(token.raw.length);\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\t// text\n\t\t\t\tif (token = this.tokenizer.text(src)) {\n\t\t\t\t\tsrc = src.substring(token.raw.length);\n\t\t\t\t\tlastToken = tokens[tokens.length - 1];\n\t\t\t\t\tif (lastToken && lastToken.type === 'text') {\n\t\t\t\t\t\tlastToken.raw += '\\n' + token.raw;\n\t\t\t\t\t\tlastToken.text += '\\n' + token.text;\n\t\t\t\t\t\tthis.inlineQueue.pop();\n\t\t\t\t\t\tthis.inlineQueue[this.inlineQueue.length - 1].src = lastToken.text;\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\ttokens.push(token);\n\t\t\t\t\t}\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\tif (src) {\n\t\t\t\t\tconst errMsg = 'Infinite loop on byte: ' + src.charCodeAt(0);\n\t\t\t\t\tif (this.options.silent) {\n\t\t\t\t\t\tconsole.error(errMsg);\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tthrow new Error(errMsg);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tthis.state.top = true;\n\t\t\treturn tokens;\n\t\t}\n\t\tinline(src, tokens = []) {\n\t\t\tthis.inlineQueue.push({ src, tokens });\n\t\t\treturn tokens;\n\t\t}\n\t\t/**\n\t\t * Lexing/Compiling\n\t\t */\n\t\tinlineTokens(src, tokens = []) {\n\t\t\tlet token, lastToken, cutSrc;\n\t\t\t// String with links masked to avoid interference with em and strong\n\t\t\tlet maskedSrc = src;\n\t\t\tlet match;\n\t\t\tlet keepPrevChar, prevChar;\n\t\t\t// Mask out reflinks\n\t\t\tif (this.tokens.links) {\n\t\t\t\tconst links = Object.keys(this.tokens.links);\n\t\t\t\tif (links.length > 0) {\n\t\t\t\t\twhile ((match = this.tokenizer.rules.inline.reflinkSearch.exec(maskedSrc)) != null) {\n\t\t\t\t\t\tif (links.includes(match[0].slice(match[0].lastIndexOf('[') + 1, -1))) {\n\t\t\t\t\t\t\tmaskedSrc = maskedSrc.slice(0, match.index) + '[' + 'a'.repeat(match[0].length - 2) + ']' + maskedSrc.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\t// Mask out other blocks\n\t\t\twhile ((match = this.tokenizer.rules.inline.blockSkip.exec(maskedSrc)) != null) {\n\t\t\t\tmaskedSrc = maskedSrc.slice(0, match.index) + '[' + 'a'.repeat(match[0].length - 2) + ']' + maskedSrc.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);\n\t\t\t}\n\t\t\t// Mask out escaped characters\n\t\t\twhile ((match = this.tokenizer.rules.inline.anyPunctuation.exec(maskedSrc)) != null) {\n\t\t\t\tmaskedSrc = maskedSrc.slice(0, match.index) + '++' + maskedSrc.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);\n\t\t\t}\n\t\t\twhile (src) {\n\t\t\t\tif (!keepPrevChar) {\n\t\t\t\t\tprevChar = '';\n\t\t\t\t}\n\t\t\t\tkeepPrevChar = false;\n\t\t\t\t// extensions\n\t\t\t\tif (this.options.extensions\n\t\t\t\t\t&& this.options.extensions.inline\n\t\t\t\t\t&& this.options.extensions.inline.some((extTokenizer) => {\n\t\t\t\t\t\tif (token = extTokenizer.call({ lexer: this }, src, tokens)) {\n\t\t\t\t\t\t\tsrc = src.substring(token.raw.length);\n\t\t\t\t\t\t\ttokens.push(token);\n\t\t\t\t\t\t\treturn true;\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t})) {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\t// escape\n\t\t\t\tif (token = this.tokenizer.escape(src)) {\n\t\t\t\t\tsrc = src.substring(token.raw.length);\n\t\t\t\t\ttokens.push(token);\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\t// tag\n\t\t\t\tif (token = this.tokenizer.tag(src)) {\n\t\t\t\t\tsrc = src.substring(token.raw.length);\n\t\t\t\t\tlastToken = tokens[tokens.length - 1];\n\t\t\t\t\tif (lastToken && token.type === 'text' && lastToken.type === 'text') {\n\t\t\t\t\t\tlastToken.raw += token.raw;\n\t\t\t\t\t\tlastToken.text += token.text;\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\ttokens.push(token);\n\t\t\t\t\t}\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\t// link\n\t\t\t\tif (token = this.tokenizer.link(src)) {\n\t\t\t\t\tsrc = src.substring(token.raw.length);\n\t\t\t\t\ttokens.push(token);\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\t// reflink, nolink\n\t\t\t\tif (token = this.tokenizer.reflink(src, this.tokens.links)) {\n\t\t\t\t\tsrc = src.substring(token.raw.length);\n\t\t\t\t\tlastToken = tokens[tokens.length - 1];\n\t\t\t\t\tif (lastToken && token.type === 'text' && lastToken.type === 'text') {\n\t\t\t\t\t\tlastToken.raw += token.raw;\n\t\t\t\t\t\tlastToken.text += token.text;\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\ttokens.push(token);\n\t\t\t\t\t}\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\t// em & strong\n\t\t\t\tif (token = this.tokenizer.emStrong(src, maskedSrc, prevChar)) {\n\t\t\t\t\tsrc = src.substring(token.raw.length);\n\t\t\t\t\ttokens.push(token);\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\t// code\n\t\t\t\tif (token = this.tokenizer.codespan(src)) {\n\t\t\t\t\tsrc = src.substring(token.raw.length);\n\t\t\t\t\ttokens.push(token);\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\t// br\n\t\t\t\tif (token = this.tokenizer.br(src)) {\n\t\t\t\t\tsrc = src.substring(token.raw.length);\n\t\t\t\t\ttokens.push(token);\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\t// del (gfm)\n\t\t\t\tif (token = this.tokenizer.del(src)) {\n\t\t\t\t\tsrc = src.substring(token.raw.length);\n\t\t\t\t\ttokens.push(token);\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\t// autolink\n\t\t\t\tif (token = this.tokenizer.autolink(src)) {\n\t\t\t\t\tsrc = src.substring(token.raw.length);\n\t\t\t\t\ttokens.push(token);\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\t// url (gfm)\n\t\t\t\tif (!this.state.inLink && (token = this.tokenizer.url(src))) {\n\t\t\t\t\tsrc = src.substring(token.raw.length);\n\t\t\t\t\ttokens.push(token);\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\t// text\n\t\t\t\t// prevent inlineText consuming extensions by clipping 'src' to extension start\n\t\t\t\tcutSrc = src;\n\t\t\t\tif (this.options.extensions && this.options.extensions.startInline) {\n\t\t\t\t\tlet startIndex = Infinity;\n\t\t\t\t\tconst tempSrc = src.slice(1);\n\t\t\t\t\tlet tempStart;\n\t\t\t\t\tthis.options.extensions.startInline.forEach((getStartIndex) => {\n\t\t\t\t\t\ttempStart = getStartIndex.call({ lexer: this }, tempSrc);\n\t\t\t\t\t\tif (typeof tempStart === 'number' && tempStart >= 0) {\n\t\t\t\t\t\t\tstartIndex = Math.min(startIndex, tempStart);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\tif (startIndex < Infinity && startIndex >= 0) {\n\t\t\t\t\t\tcutSrc = src.substring(0, startIndex + 1);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (token = this.tokenizer.inlineText(cutSrc)) {\n\t\t\t\t\tsrc = src.substring(token.raw.length);\n\t\t\t\t\tif (token.raw.slice(-1) !== '_') { // Track prevChar before string of ____ started\n\t\t\t\t\t\tprevChar = token.raw.slice(-1);\n\t\t\t\t\t}\n\t\t\t\t\tkeepPrevChar = true;\n\t\t\t\t\tlastToken = tokens[tokens.length - 1];\n\t\t\t\t\tif (lastToken && lastToken.type === 'text') {\n\t\t\t\t\t\tlastToken.raw += token.raw;\n\t\t\t\t\t\tlastToken.text += token.text;\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\ttokens.push(token);\n\t\t\t\t\t}\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\tif (src) {\n\t\t\t\t\tconst errMsg = 'Infinite loop on byte: ' + src.charCodeAt(0);\n\t\t\t\t\tif (this.options.silent) {\n\t\t\t\t\t\tconsole.error(errMsg);\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tthrow new Error(errMsg);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn tokens;\n\t\t}\n\t}\n\n\t/**\n\t * Renderer\n\t */\n\tclass _Renderer {\n\t\toptions;\n\t\tparser; // set by the parser\n\t\tconstructor(options) {\n\t\t\tthis.options = options || exports.defaults;\n\t\t}\n\t\tspace(token) {\n\t\t\treturn '';\n\t\t}\n\t\tcode({ text, lang, escaped }) {\n\t\t\tconst langString = (lang || '').match(/^\\S*/)?.[0];\n\t\t\tconst code = text.replace(/\\n$/, '') + '\\n';\n\t\t\tif (!langString) {\n\t\t\t\treturn '<pre><code>'\n\t\t\t\t\t+ (escaped ? code : escape$1(code, true))\n\t\t\t\t\t+ '</code></pre>\\n';\n\t\t\t}\n\t\t\treturn '<pre><code class=\"language-'\n\t\t\t\t+ escape$1(langString)\n\t\t\t\t+ '\">'\n\t\t\t\t+ (escaped ? code : escape$1(code, true))\n\t\t\t\t+ '</code></pre>\\n';\n\t\t}\n\t\tblockquote({ tokens }) {\n\t\t\tconst body = this.parser.parse(tokens);\n\t\t\treturn `<blockquote>\\n${body}</blockquote>\\n`;\n\t\t}\n\t\thtml({ text }) {\n\t\t\treturn text;\n\t\t}\n\t\theading({ tokens, depth }) {\n\t\t\treturn `<h${depth}>${this.parser.parseInline(tokens)}</h${depth}>\\n`;\n\t\t}\n\t\thr(token) {\n\t\t\treturn '<hr>\\n';\n\t\t}\n\t\tlist(token) {\n\t\t\tconst ordered = token.ordered;\n\t\t\tconst start = token.start;\n\t\t\tlet body = '';\n\t\t\tfor (let j = 0; j < token.items.length; j++) {\n\t\t\t\tconst item = token.items[j];\n\t\t\t\tbody += this.listitem(item);\n\t\t\t}\n\t\t\tconst type = ordered ? 'ol' : 'ul';\n\t\t\tconst startAttr = (ordered && start !== 1) ? (' start=\"' + start + '\"') : '';\n\t\t\treturn '<' + type + startAttr + '>\\n' + body + '</' + type + '>\\n';\n\t\t}\n\t\tlistitem(item) {\n\t\t\tlet itemBody = '';\n\t\t\tif (item.task) {\n\t\t\t\tconst checkbox = this.checkbox({ checked: !!item.checked });\n\t\t\t\tif (item.loose) {\n\t\t\t\t\tif (item.tokens.length > 0 && item.tokens[0].type === 'paragraph') {\n\t\t\t\t\t\titem.tokens[0].text = checkbox + ' ' + item.tokens[0].text;\n\t\t\t\t\t\tif (item.tokens[0].tokens && item.tokens[0].tokens.length > 0 && item.tokens[0].tokens[0].type === 'text') {\n\t\t\t\t\t\t\titem.tokens[0].tokens[0].text = checkbox + ' ' + item.tokens[0].tokens[0].text;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\titem.tokens.unshift({\n\t\t\t\t\t\t\ttype: 'text',\n\t\t\t\t\t\t\traw: checkbox + ' ',\n\t\t\t\t\t\t\ttext: checkbox + ' ',\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\titemBody += checkbox + ' ';\n\t\t\t\t}\n\t\t\t}\n\t\t\titemBody += this.parser.parse(item.tokens, !!item.loose);\n\t\t\treturn `<li>${itemBody}</li>\\n`;\n\t\t}\n\t\tcheckbox({ checked }) {\n\t\t\treturn '<input '\n\t\t\t\t+ (checked ? 'checked=\"\" ' : '')\n\t\t\t\t+ 'disabled=\"\" type=\"checkbox\">';\n\t\t}\n\t\tparagraph({ tokens }) {\n\t\t\treturn `<p>${this.parser.parseInline(tokens)}</p>\\n`;\n\t\t}\n\t\ttable(token) {\n\t\t\tlet header = '';\n\t\t\t// header\n\t\t\tlet cell = '';\n\t\t\tfor (let j = 0; j < token.header.length; j++) {\n\t\t\t\tcell += this.tablecell(token.header[j]);\n\t\t\t}\n\t\t\theader += this.tablerow({ text: cell });\n\t\t\tlet body = '';\n\t\t\tfor (let j = 0; j < token.rows.length; j++) {\n\t\t\t\tconst row = token.rows[j];\n\t\t\t\tcell = '';\n\t\t\t\tfor (let k = 0; k < row.length; k++) {\n\t\t\t\t\tcell += this.tablecell(row[k]);\n\t\t\t\t}\n\t\t\t\tbody += this.tablerow({ text: cell });\n\t\t\t}\n\t\t\tif (body)\n\t\t\t\tbody = `<tbody>${body}</tbody>`;\n\t\t\treturn '<table>\\n'\n\t\t\t\t+ '<thead>\\n'\n\t\t\t\t+ header\n\t\t\t\t+ '</thead>\\n'\n\t\t\t\t+ body\n\t\t\t\t+ '</table>\\n';\n\t\t}\n\t\ttablerow({ text }) {\n\t\t\treturn `<tr>\\n${text}</tr>\\n`;\n\t\t}\n\t\ttablecell(token) {\n\t\t\tconst content = this.parser.parseInline(token.tokens);\n\t\t\tconst type = token.header ? 'th' : 'td';\n\t\t\tconst tag = token.align\n\t\t\t\t? `<${type} align=\"${token.align}\">`\n\t\t\t\t: `<${type}>`;\n\t\t\treturn tag + content + `</${type}>\\n`;\n\t\t}\n\t\t/**\n\t\t * span level renderer\n\t\t */\n\t\tstrong({ tokens }) {\n\t\t\treturn `<strong>${this.parser.parseInline(tokens)}</strong>`;\n\t\t}\n\t\tem({ tokens }) {\n\t\t\treturn `<em>${this.parser.parseInline(tokens)}</em>`;\n\t\t}\n\t\tcodespan({ text }) {\n\t\t\treturn `<code>${text}</code>`;\n\t\t}\n\t\tbr(token) {\n\t\t\treturn '<br>';\n\t\t}\n\t\tdel({ tokens }) {\n\t\t\treturn `<del>${this.parser.parseInline(tokens)}</del>`;\n\t\t}\n\t\tlink({ href, title, tokens }) {\n\t\t\tconst text = this.parser.parseInline(tokens);\n\t\t\tconst cleanHref = cleanUrl(href);\n\t\t\tif (cleanHref === null) {\n\t\t\t\treturn text;\n\t\t\t}\n\t\t\thref = cleanHref;\n\t\t\tlet out = '<a href=\"' + href + '\"';\n\t\t\tif (title) {\n\t\t\t\tout += ' title=\"' + title + '\"';\n\t\t\t}\n\t\t\tout += '>' + text + '</a>';\n\t\t\treturn out;\n\t\t}\n\t\timage({ href, title, text }) {\n\t\t\tconst cleanHref = cleanUrl(href);\n\t\t\tif (cleanHref === null) {\n\t\t\t\treturn text;\n\t\t\t}\n\t\t\thref = cleanHref;\n\t\t\tlet out = `<img src=\"${href}\" alt=\"${text}\"`;\n\t\t\tif (title) {\n\t\t\t\tout += ` title=\"${title}\"`;\n\t\t\t}\n\t\t\tout += '>';\n\t\t\treturn out;\n\t\t}\n\t\ttext(token) {\n\t\t\treturn 'tokens' in token && token.tokens ? this.parser.parseInline(token.tokens) : token.text;\n\t\t}\n\t}\n\n\t/**\n\t * TextRenderer\n\t * returns only the textual part of the token\n\t */\n\tclass _TextRenderer {\n\t\t// no need for block level renderers\n\t\tstrong({ text }) {\n\t\t\treturn text;\n\t\t}\n\t\tem({ text }) {\n\t\t\treturn text;\n\t\t}\n\t\tcodespan({ text }) {\n\t\t\treturn text;\n\t\t}\n\t\tdel({ text }) {\n\t\t\treturn text;\n\t\t}\n\t\thtml({ text }) {\n\t\t\treturn text;\n\t\t}\n\t\ttext({ text }) {\n\t\t\treturn text;\n\t\t}\n\t\tlink({ text }) {\n\t\t\treturn '' + text;\n\t\t}\n\t\timage({ text }) {\n\t\t\treturn '' + text;\n\t\t}\n\t\tbr() {\n\t\t\treturn '';\n\t\t}\n\t}\n\n\t/**\n\t * Parsing & Compiling\n\t */\n\tclass _Parser {\n\t\toptions;\n\t\trenderer;\n\t\ttextRenderer;\n\t\tconstructor(options) {\n\t\t\tthis.options = options || exports.defaults;\n\t\t\tthis.options.renderer = this.options.renderer || new _Renderer();\n\t\t\tthis.renderer = this.options.renderer;\n\t\t\tthis.renderer.options = this.options;\n\t\t\tthis.renderer.parser = this;\n\t\t\tthis.textRenderer = new _TextRenderer();\n\t\t}\n\t\t/**\n\t\t * Static Parse Method\n\t\t */\n\t\tstatic parse(tokens, options) {\n\t\t\tconst parser = new _Parser(options);\n\t\t\treturn parser.parse(tokens);\n\t\t}\n\t\t/**\n\t\t * Static Parse Inline Method\n\t\t */\n\t\tstatic parseInline(tokens, options) {\n\t\t\tconst parser = new _Parser(options);\n\t\t\treturn parser.parseInline(tokens);\n\t\t}\n\t\t/**\n\t\t * Parse Loop\n\t\t */\n\t\tparse(tokens, top = true) {\n\t\t\tlet out = '';\n\t\t\tfor (let i = 0; i < tokens.length; i++) {\n\t\t\t\tconst anyToken = tokens[i];\n\t\t\t\t// Run any renderer extensions\n\t\t\t\tif (this.options.extensions && this.options.extensions.renderers && this.options.extensions.renderers[anyToken.type]) {\n\t\t\t\t\tconst genericToken = anyToken;\n\t\t\t\t\tconst ret = this.options.extensions.renderers[genericToken.type].call({ parser: this }, genericToken);\n\t\t\t\t\tif (ret !== false || !['space', 'hr', 'heading', 'code', 'table', 'blockquote', 'list', 'html', 'paragraph', 'text'].includes(genericToken.type)) {\n\t\t\t\t\t\tout += ret || '';\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tconst token = anyToken;\n\t\t\t\tswitch (token.type) {\n\t\t\t\t\tcase 'space': {\n\t\t\t\t\t\tout += this.renderer.space(token);\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t\t\tcase 'hr': {\n\t\t\t\t\t\tout += this.renderer.hr(token);\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t\t\tcase 'heading': {\n\t\t\t\t\t\tout += this.renderer.heading(token);\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t\t\tcase 'code': {\n\t\t\t\t\t\tout += this.renderer.code(token);\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t\t\tcase 'table': {\n\t\t\t\t\t\tout += this.renderer.table(token);\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t\t\tcase 'blockquote': {\n\t\t\t\t\t\tout += this.renderer.blockquote(token);\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t\t\tcase 'list': {\n\t\t\t\t\t\tout += this.renderer.list(token);\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t\t\tcase 'html': {\n\t\t\t\t\t\tout += this.renderer.html(token);\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t\t\tcase 'paragraph': {\n\t\t\t\t\t\tout += this.renderer.paragraph(token);\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t\t\tcase 'text': {\n\t\t\t\t\t\tlet textToken = token;\n\t\t\t\t\t\tlet body = this.renderer.text(textToken);\n\t\t\t\t\t\twhile (i + 1 < tokens.length && tokens[i + 1].type === 'text') {\n\t\t\t\t\t\t\ttextToken = tokens[++i];\n\t\t\t\t\t\t\tbody += '\\n' + this.renderer.text(textToken);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (top) {\n\t\t\t\t\t\t\tout += this.renderer.paragraph({\n\t\t\t\t\t\t\t\ttype: 'paragraph',\n\t\t\t\t\t\t\t\traw: body,\n\t\t\t\t\t\t\t\ttext: body,\n\t\t\t\t\t\t\t\ttokens: [{ type: 'text', raw: body, text: body }],\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse {\n\t\t\t\t\t\t\tout += body;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t\t\tdefault: {\n\t\t\t\t\t\tconst errMsg = 'Token with \"' + token.type + '\" type was not found.';\n\t\t\t\t\t\tif (this.options.silent) {\n\t\t\t\t\t\t\tconsole.error(errMsg);\n\t\t\t\t\t\t\treturn '';\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse {\n\t\t\t\t\t\t\tthrow new Error(errMsg);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn out;\n\t\t}\n\t\t/**\n\t\t * Parse Inline Tokens\n\t\t */\n\t\tparseInline(tokens, renderer) {\n\t\t\trenderer = renderer || this.renderer;\n\t\t\tlet out = '';\n\t\t\tfor (let i = 0; i < tokens.length; i++) {\n\t\t\t\tconst anyToken = tokens[i];\n\t\t\t\t// Run any renderer extensions\n\t\t\t\tif (this.options.extensions && this.options.extensions.renderers && this.options.extensions.renderers[anyToken.type]) {\n\t\t\t\t\tconst ret = this.options.extensions.renderers[anyToken.type].call({ parser: this }, anyToken);\n\t\t\t\t\tif (ret !== false || !['escape', 'html', 'link', 'image', 'strong', 'em', 'codespan', 'br', 'del', 'text'].includes(anyToken.type)) {\n\t\t\t\t\t\tout += ret || '';\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tconst token = anyToken;\n\t\t\t\tswitch (token.type) {\n\t\t\t\t\tcase 'escape': {\n\t\t\t\t\t\tout += renderer.text(token);\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\tcase 'html': {\n\t\t\t\t\t\tout += renderer.html(token);\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\tcase 'link': {\n\t\t\t\t\t\tout += renderer.link(token);\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\tcase 'image': {\n\t\t\t\t\t\tout += renderer.image(token);\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\tcase 'strong': {\n\t\t\t\t\t\tout += renderer.strong(token);\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\tcase 'em': {\n\t\t\t\t\t\tout += renderer.em(token);\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\tcase 'codespan': {\n\t\t\t\t\t\tout += renderer.codespan(token);\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\tcase 'br': {\n\t\t\t\t\t\tout += renderer.br(token);\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\tcase 'del': {\n\t\t\t\t\t\tout += renderer.del(token);\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\tcase 'text': {\n\t\t\t\t\t\tout += renderer.text(token);\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\tdefault: {\n\t\t\t\t\t\tconst errMsg = 'Token with \"' + token.type + '\" type was not found.';\n\t\t\t\t\t\tif (this.options.silent) {\n\t\t\t\t\t\t\tconsole.error(errMsg);\n\t\t\t\t\t\t\treturn '';\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse {\n\t\t\t\t\t\t\tthrow new Error(errMsg);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn out;\n\t\t}\n\t}\n\n\tclass _Hooks {\n\t\toptions;\n\t\tconstructor(options) {\n\t\t\tthis.options = options || exports.defaults;\n\t\t}\n\t\tstatic passThroughHooks = new Set([\n\t\t\t'preprocess',\n\t\t\t'postprocess',\n\t\t\t'processAllTokens',\n\t\t]);\n\t\t/**\n\t\t * Process markdown before marked\n\t\t */\n\t\tpreprocess(markdown) {\n\t\t\treturn markdown;\n\t\t}\n\t\t/**\n\t\t * Process HTML after marked is finished\n\t\t */\n\t\tpostprocess(html) {\n\t\t\treturn html;\n\t\t}\n\t\t/**\n\t\t * Process all tokens before walk tokens\n\t\t */\n\t\tprocessAllTokens(tokens) {\n\t\t\treturn tokens;\n\t\t}\n\t}\n\n\tclass Marked {\n\t\tdefaults = _getDefaults();\n\t\toptions = this.setOptions;\n\t\tparse = this.parseMarkdown(_Lexer.lex, _Parser.parse);\n\t\tparseInline = this.parseMarkdown(_Lexer.lexInline, _Parser.parseInline);\n\t\tParser = _Parser;\n\t\tRenderer = _Renderer;\n\t\tTextRenderer = _TextRenderer;\n\t\tLexer = _Lexer;\n\t\tTokenizer = _Tokenizer;\n\t\tHooks = _Hooks;\n\t\tconstructor(...args) {\n\t\t\tthis.use(...args);\n\t\t}\n\t\t/**\n\t\t * Run callback for every token\n\t\t */\n\t\twalkTokens(tokens, callback) {\n\t\t\tlet values = [];\n\t\t\tfor (const token of tokens) {\n\t\t\t\tvalues = values.concat(callback.call(this, token));\n\t\t\t\tswitch (token.type) {\n\t\t\t\t\tcase 'table': {\n\t\t\t\t\t\tconst tableToken = token;\n\t\t\t\t\t\tfor (const cell of tableToken.header) {\n\t\t\t\t\t\t\tvalues = values.concat(this.walkTokens(cell.tokens, callback));\n\t\t\t\t\t\t}\n\t\t\t\t\t\tfor (const row of tableToken.rows) {\n\t\t\t\t\t\t\tfor (const cell of row) {\n\t\t\t\t\t\t\t\tvalues = values.concat(this.walkTokens(cell.tokens, callback));\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\tcase 'list': {\n\t\t\t\t\t\tconst listToken = token;\n\t\t\t\t\t\tvalues = values.concat(this.walkTokens(listToken.items, callback));\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\tdefault: {\n\t\t\t\t\t\tconst genericToken = token;\n\t\t\t\t\t\tif (this.defaults.extensions?.childTokens?.[genericToken.type]) {\n\t\t\t\t\t\t\tthis.defaults.extensions.childTokens[genericToken.type].forEach((childTokens) => {\n\t\t\t\t\t\t\t\tconst tokens = genericToken[childTokens].flat(Infinity);\n\t\t\t\t\t\t\t\tvalues = values.concat(this.walkTokens(tokens, callback));\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse if (genericToken.tokens) {\n\t\t\t\t\t\t\tvalues = values.concat(this.walkTokens(genericToken.tokens, callback));\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn values;\n\t\t}\n\t\tuse(...args) {\n\t\t\tconst extensions = this.defaults.extensions || { renderers: {}, childTokens: {} };\n\t\t\targs.forEach((pack) => {\n\t\t\t\t// copy options to new object\n\t\t\t\tconst opts = { ...pack };\n\t\t\t\t// set async to true if it was set to true before\n\t\t\t\topts.async = this.defaults.async || opts.async || false;\n\t\t\t\t// ==-- Parse \"addon\" extensions --== //\n\t\t\t\tif (pack.extensions) {\n\t\t\t\t\tpack.extensions.forEach((ext) => {\n\t\t\t\t\t\tif (!ext.name) {\n\t\t\t\t\t\t\tthrow new Error('extension name required');\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif ('renderer' in ext) { // Renderer extensions\n\t\t\t\t\t\t\tconst prevRenderer = extensions.renderers[ext.name];\n\t\t\t\t\t\t\tif (prevRenderer) {\n\t\t\t\t\t\t\t\t// Replace extension with func to run new extension but fall back if false\n\t\t\t\t\t\t\t\textensions.renderers[ext.name] = function (...args) {\n\t\t\t\t\t\t\t\t\tlet ret = ext.renderer.apply(this, args);\n\t\t\t\t\t\t\t\t\tif (ret === false) {\n\t\t\t\t\t\t\t\t\t\tret = prevRenderer.apply(this, args);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\treturn ret;\n\t\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\textensions.renderers[ext.name] = ext.renderer;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif ('tokenizer' in ext) { // Tokenizer Extensions\n\t\t\t\t\t\t\tif (!ext.level || (ext.level !== 'block' && ext.level !== 'inline')) {\n\t\t\t\t\t\t\t\tthrow new Error(\"extension level must be 'block' or 'inline'\");\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tconst extLevel = extensions[ext.level];\n\t\t\t\t\t\t\tif (extLevel) {\n\t\t\t\t\t\t\t\textLevel.unshift(ext.tokenizer);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\textensions[ext.level] = [ext.tokenizer];\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (ext.start) { // Function to check for start of token\n\t\t\t\t\t\t\t\tif (ext.level === 'block') {\n\t\t\t\t\t\t\t\t\tif (extensions.startBlock) {\n\t\t\t\t\t\t\t\t\t\textensions.startBlock.push(ext.start);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\t\t\textensions.startBlock = [ext.start];\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\telse if (ext.level === 'inline') {\n\t\t\t\t\t\t\t\t\tif (extensions.startInline) {\n\t\t\t\t\t\t\t\t\t\textensions.startInline.push(ext.start);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\t\t\textensions.startInline = [ext.start];\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif ('childTokens' in ext && ext.childTokens) { // Child tokens to be visited by walkTokens\n\t\t\t\t\t\t\textensions.childTokens[ext.name] = ext.childTokens;\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\topts.extensions = extensions;\n\t\t\t\t}\n\t\t\t\t// ==-- Parse \"overwrite\" extensions --== //\n\t\t\t\tif (pack.renderer) {\n\t\t\t\t\tconst renderer = this.defaults.renderer || new _Renderer(this.defaults);\n\t\t\t\t\tfor (const prop in pack.renderer) {\n\t\t\t\t\t\tif (!(prop in renderer)) {\n\t\t\t\t\t\t\tthrow new Error(`renderer '${prop}' does not exist`);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (['options', 'parser'].includes(prop)) {\n\t\t\t\t\t\t\t// ignore options property\n\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tconst rendererProp = prop;\n\t\t\t\t\t\tconst rendererFunc = pack.renderer[rendererProp];\n\t\t\t\t\t\tconst prevRenderer = renderer[rendererProp];\n\t\t\t\t\t\t// Replace renderer with func to run extension, but fall back if false\n\t\t\t\t\t\trenderer[rendererProp] = (...args) => {\n\t\t\t\t\t\t\tlet ret = rendererFunc.apply(renderer, args);\n\t\t\t\t\t\t\tif (ret === false) {\n\t\t\t\t\t\t\t\tret = prevRenderer.apply(renderer, args);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\treturn ret || '';\n\t\t\t\t\t\t};\n\t\t\t\t\t}\n\t\t\t\t\topts.renderer = renderer;\n\t\t\t\t}\n\t\t\t\tif (pack.tokenizer) {\n\t\t\t\t\tconst tokenizer = this.defaults.tokenizer || new _Tokenizer(this.defaults);\n\t\t\t\t\tfor (const prop in pack.tokenizer) {\n\t\t\t\t\t\tif (!(prop in tokenizer)) {\n\t\t\t\t\t\t\tthrow new Error(`tokenizer '${prop}' does not exist`);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (['options', 'rules', 'lexer'].includes(prop)) {\n\t\t\t\t\t\t\t// ignore options, rules, and lexer properties\n\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tconst tokenizerProp = prop;\n\t\t\t\t\t\tconst tokenizerFunc = pack.tokenizer[tokenizerProp];\n\t\t\t\t\t\tconst prevTokenizer = tokenizer[tokenizerProp];\n\t\t\t\t\t\t// Replace tokenizer with func to run extension, but fall back if false\n\t\t\t\t\t\t// @ts-expect-error cannot type tokenizer function dynamically\n\t\t\t\t\t\ttokenizer[tokenizerProp] = (...args) => {\n\t\t\t\t\t\t\tlet ret = tokenizerFunc.apply(tokenizer, args);\n\t\t\t\t\t\t\tif (ret === false) {\n\t\t\t\t\t\t\t\tret = prevTokenizer.apply(tokenizer, args);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\treturn ret;\n\t\t\t\t\t\t};\n\t\t\t\t\t}\n\t\t\t\t\topts.tokenizer = tokenizer;\n\t\t\t\t}\n\t\t\t\t// ==-- Parse Hooks extensions --== //\n\t\t\t\tif (pack.hooks) {\n\t\t\t\t\tconst hooks = this.defaults.hooks || new _Hooks();\n\t\t\t\t\tfor (const prop in pack.hooks) {\n\t\t\t\t\t\tif (!(prop in hooks)) {\n\t\t\t\t\t\t\tthrow new Error(`hook '${prop}' does not exist`);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (prop === 'options') {\n\t\t\t\t\t\t\t// ignore options property\n\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tconst hooksProp = prop;\n\t\t\t\t\t\tconst hooksFunc = pack.hooks[hooksProp];\n\t\t\t\t\t\tconst prevHook = hooks[hooksProp];\n\t\t\t\t\t\tif (_Hooks.passThroughHooks.has(prop)) {\n\t\t\t\t\t\t\t// @ts-expect-error cannot type hook function dynamically\n\t\t\t\t\t\t\thooks[hooksProp] = (arg) => {\n\t\t\t\t\t\t\t\tif (this.defaults.async) {\n\t\t\t\t\t\t\t\t\treturn Promise.resolve(hooksFunc.call(hooks, arg)).then(ret => {\n\t\t\t\t\t\t\t\t\t\treturn prevHook.call(hooks, ret);\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tconst ret = hooksFunc.call(hooks, arg);\n\t\t\t\t\t\t\t\treturn prevHook.call(hooks, ret);\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t// @ts-expect-error cannot type hook function dynamically\n\t\t\t\t\t\t\thooks[hooksProp] = (...args) => {\n\t\t\t\t\t\t\t\tlet ret = hooksFunc.apply(hooks, args);\n\t\t\t\t\t\t\t\tif (ret === false) {\n\t\t\t\t\t\t\t\t\tret = prevHook.apply(hooks, args);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\treturn ret;\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\topts.hooks = hooks;\n\t\t\t\t}\n\t\t\t\t// ==-- Parse WalkTokens extensions --== //\n\t\t\t\tif (pack.walkTokens) {\n\t\t\t\t\tconst walkTokens = this.defaults.walkTokens;\n\t\t\t\t\tconst packWalktokens = pack.walkTokens;\n\t\t\t\t\topts.walkTokens = function (token) {\n\t\t\t\t\t\tlet values = [];\n\t\t\t\t\t\tvalues.push(packWalktokens.call(this, token));\n\t\t\t\t\t\tif (walkTokens) {\n\t\t\t\t\t\t\tvalues = values.concat(walkTokens.call(this, token));\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn values;\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t\tthis.defaults = { ...this.defaults, ...opts };\n\t\t\t});\n\t\t\treturn this;\n\t\t}\n\t\tsetOptions(opt) {\n\t\t\tthis.defaults = { ...this.defaults, ...opt };\n\t\t\treturn this;\n\t\t}\n\t\tlexer(src, options) {\n\t\t\treturn _Lexer.lex(src, options ?? this.defaults);\n\t\t}\n\t\tparser(tokens, options) {\n\t\t\treturn _Parser.parse(tokens, options ?? this.defaults);\n\t\t}\n\t\tparseMarkdown(lexer, parser) {\n\t\t\t// eslint-disable-next-line @typescript-eslint/no-explicit-any\n\t\t\tconst parse = (src, options) => {\n\t\t\t\tconst origOpt = { ...options };\n\t\t\t\tconst opt = { ...this.defaults, ...origOpt };\n\t\t\t\tconst throwError = this.onError(!!opt.silent, !!opt.async);\n\t\t\t\t// throw error if an extension set async to true but parse was called with async: false\n\t\t\t\tif (this.defaults.async === true && origOpt.async === false) {\n\t\t\t\t\treturn throwError(new Error('marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise.'));\n\t\t\t\t}\n\t\t\t\t// throw error in case of non string input\n\t\t\t\tif (typeof src === 'undefined' || src === null) {\n\t\t\t\t\treturn throwError(new Error('marked(): input parameter is undefined or null'));\n\t\t\t\t}\n\t\t\t\tif (typeof src !== 'string') {\n\t\t\t\t\treturn throwError(new Error('marked(): input parameter is of type '\n\t\t\t\t\t\t+ Object.prototype.toString.call(src) + ', string expected'));\n\t\t\t\t}\n\t\t\t\tif (opt.hooks) {\n\t\t\t\t\topt.hooks.options = opt;\n\t\t\t\t}\n\t\t\t\tif (opt.async) {\n\t\t\t\t\treturn Promise.resolve(opt.hooks ? opt.hooks.preprocess(src) : src)\n\t\t\t\t\t\t.then(src => lexer(src, opt))\n\t\t\t\t\t\t.then(tokens => opt.hooks ? opt.hooks.processAllTokens(tokens) : tokens)\n\t\t\t\t\t\t.then(tokens => opt.walkTokens ? Promise.all(this.walkTokens(tokens, opt.walkTokens)).then(() => tokens) : tokens)\n\t\t\t\t\t\t.then(tokens => parser(tokens, opt))\n\t\t\t\t\t\t.then(html => opt.hooks ? opt.hooks.postprocess(html) : html)\n\t\t\t\t\t\t.catch(throwError);\n\t\t\t\t}\n\t\t\t\ttry {\n\t\t\t\t\tif (opt.hooks) {\n\t\t\t\t\t\tsrc = opt.hooks.preprocess(src);\n\t\t\t\t\t}\n\t\t\t\t\tlet tokens = lexer(src, opt);\n\t\t\t\t\tif (opt.hooks) {\n\t\t\t\t\t\ttokens = opt.hooks.processAllTokens(tokens);\n\t\t\t\t\t}\n\t\t\t\t\tif (opt.walkTokens) {\n\t\t\t\t\t\tthis.walkTokens(tokens, opt.walkTokens);\n\t\t\t\t\t}\n\t\t\t\t\tlet html = parser(tokens, opt);\n\t\t\t\t\tif (opt.hooks) {\n\t\t\t\t\t\thtml = opt.hooks.postprocess(html);\n\t\t\t\t\t}\n\t\t\t\t\treturn html;\n\t\t\t\t}\n\t\t\t\tcatch (e) {\n\t\t\t\t\treturn throwError(e);\n\t\t\t\t}\n\t\t\t};\n\t\t\treturn parse;\n\t\t}\n\t\tonError(silent, async) {\n\t\t\treturn (e) => {\n\t\t\t\te.message += '\\nPlease report this to https://github.com/markedjs/marked.';\n\t\t\t\tif (silent) {\n\t\t\t\t\tconst msg = '<p>An error occurred:</p><pre>'\n\t\t\t\t\t\t+ escape$1(e.message + '', true)\n\t\t\t\t\t\t+ '</pre>';\n\t\t\t\t\tif (async) {\n\t\t\t\t\t\treturn Promise.resolve(msg);\n\t\t\t\t\t}\n\t\t\t\t\treturn msg;\n\t\t\t\t}\n\t\t\t\tif (async) {\n\t\t\t\t\treturn Promise.reject(e);\n\t\t\t\t}\n\t\t\t\tthrow e;\n\t\t\t};\n\t\t}\n\t}\n\n\tconst markedInstance = new Marked();\n\tfunction marked(src, opt) {\n\t\treturn markedInstance.parse(src, opt);\n\t}\n\t/**\n\t * Sets the default options.\n\t *\n\t * @param options Hash of options\n\t */\n\tmarked.options =\n\t\tmarked.setOptions = function (options) {\n\t\t\tmarkedInstance.setOptions(options);\n\t\t\tmarked.defaults = markedInstance.defaults;\n\t\t\tchangeDefaults(marked.defaults);\n\t\t\treturn marked;\n\t\t};\n\t/**\n\t * Gets the original marked default options.\n\t */\n\tmarked.getDefaults = _getDefaults;\n\tmarked.defaults = exports.defaults;\n\t/**\n\t * Use Extension\n\t */\n\tmarked.use = function (...args) {\n\t\tmarkedInstance.use(...args);\n\t\tmarked.defaults = markedInstance.defaults;\n\t\tchangeDefaults(marked.defaults);\n\t\treturn marked;\n\t};\n\t/**\n\t * Run callback for every token\n\t */\n\tmarked.walkTokens = function (tokens, callback) {\n\t\treturn markedInstance.walkTokens(tokens, callback);\n\t};\n\t/**\n\t * Compiles markdown to HTML without enclosing `p` tag.\n\t *\n\t * @param src String of markdown source to be compiled\n\t * @param options Hash of options\n\t * @return String of compiled HTML\n\t */\n\tmarked.parseInline = markedInstance.parseInline;\n\t/**\n\t * Expose\n\t */\n\tmarked.Parser = _Parser;\n\tmarked.parser = _Parser.parse;\n\tmarked.Renderer = _Renderer;\n\tmarked.TextRenderer = _TextRenderer;\n\tmarked.Lexer = _Lexer;\n\tmarked.lexer = _Lexer.lex;\n\tmarked.Tokenizer = _Tokenizer;\n\tmarked.Hooks = _Hooks;\n\tmarked.parse = marked;\n\tconst options = marked.options;\n\tconst setOptions = marked.setOptions;\n\tconst use = marked.use;\n\tconst walkTokens = marked.walkTokens;\n\tconst parseInline = marked.parseInline;\n\tconst parse = marked;\n\tconst parser = _Parser.parse;\n\tconst lexer = _Lexer.lex;\n\n\texports.Hooks = _Hooks;\n\texports.Lexer = _Lexer;\n\texports.Marked = Marked;\n\texports.Parser = _Parser;\n\texports.Renderer = _Renderer;\n\texports.TextRenderer = _TextRenderer;\n\texports.Tokenizer = _Tokenizer;\n\texports.getDefaults = _getDefaults;\n\texports.lexer = lexer;\n\texports.marked = marked;\n\texports.options = options;\n\texports.parse = parse;\n\texports.parseInline = parseInline;\n\texports.parser = parser;\n\texports.setOptions = setOptions;\n\texports.use = use;\n\texports.walkTokens = walkTokens;\n}));\n\n// ESM-uncomment-begin\n})();\nexport var Hooks = (__marked_exports.Hooks || exports.Hooks);\nexport var Lexer = (__marked_exports.Lexer || exports.Lexer);\nexport var Marked = (__marked_exports.Marked || exports.Marked);\nexport var Parser = (__marked_exports.Parser || exports.Parser);\nexport var Renderer = (__marked_exports.Renderer || exports.Renderer);\nexport var TextRenderer = (__marked_exports.TextRenderer || exports.TextRenderer);\nexport var Tokenizer = (__marked_exports.Tokenizer || exports.Tokenizer);\nexport var defaults = (__marked_exports.defaults || exports.defaults);\nexport var getDefaults = (__marked_exports.getDefaults || exports.getDefaults);\nexport var lexer = (__marked_exports.lexer || exports.lexer);\nexport var marked = (__marked_exports.marked || exports.marked);\nexport var options = (__marked_exports.options || exports.options);\nexport var parse = (__marked_exports.parse || exports.parse);\nexport var parseInline = (__marked_exports.parseInline || exports.parseInline);\nexport var parser = (__marked_exports.parser || exports.parser);\nexport var setOptions = (__marked_exports.setOptions || exports.setOptions);\nexport var use = (__marked_exports.use || exports.use);\nexport var walkTokens = (__marked_exports.walkTokens || exports.walkTokens);\n// ESM-uncomment-end\n\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,IAAIA,gBAAgB,GAAG,CAAC,CAAC;AACzB,CAAC,YAAW;EACV,SAASC,MAAMA,CAACC,IAAI,EAAEC,OAAO,EAAE;IAC7BA,OAAO,CAACH,gBAAgB,CAAC;EAC3B;EACAC,MAAM,CAACG,GAAG,GAAG,IAAI;EACnB;;EAEA,CAAC,UAAUC,MAAM,EAAEF,OAAO,EAAE;IAC3B,OAAOF,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACG,GAAG,GAAGH,MAAM,CAAC,CAAC,SAAS,CAAC,EAAEE,OAAO,CAAC,GACzE,OAAOG,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,GAAGJ,OAAO,CAACG,OAAO,CAAC,IAC9ED,MAAM,GAAG,OAAOG,UAAU,KAAK,WAAW,GAAGA,UAAU,GAAGH,MAAM,IAAII,IAAI,EAAEN,OAAO,CAACE,MAAM,CAACK,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;EACtG,CAAC,EAAE,IAAI,EAAG,UAAUJ,OAAO,EAAE;IAC9B,YAAY;;IAEZ;AACD;AACA;IACC,SAASK,YAAYA,CAAA,EAAG;MACvB,OAAO;QACNC,KAAK,EAAE,KAAK;QACZC,MAAM,EAAE,KAAK;QACbC,UAAU,EAAE,IAAI;QAChBC,GAAG,EAAE,IAAI;QACTC,KAAK,EAAE,IAAI;QACXC,QAAQ,EAAE,KAAK;QACfC,QAAQ,EAAE,IAAI;QACdC,MAAM,EAAE,KAAK;QACbC,SAAS,EAAE,IAAI;QACfC,UAAU,EAAE;MACb,CAAC;IACF;IACAf,OAAO,CAACgB,QAAQ,GAAGX,YAAY,CAAC,CAAC;IACjC,SAASY,cAAcA,CAACC,WAAW,EAAE;MACpClB,OAAO,CAACgB,QAAQ,GAAGE,WAAW;IAC/B;;IAEA;AACD;AACA;IACC,MAAMC,UAAU,GAAG,SAAS;IAC5B,MAAMC,aAAa,GAAG,IAAIC,MAAM,CAACF,UAAU,CAACG,MAAM,EAAE,GAAG,CAAC;IACxD,MAAMC,kBAAkB,GAAG,mDAAmD;IAC9E,MAAMC,qBAAqB,GAAG,IAAIH,MAAM,CAACE,kBAAkB,CAACD,MAAM,EAAE,GAAG,CAAC;IACxE,MAAMG,kBAAkB,GAAG;MAC1B,GAAG,EAAE,OAAO;MACZ,GAAG,EAAE,MAAM;MACX,GAAG,EAAE,MAAM;MACX,GAAG,EAAE,QAAQ;MACb,GAAG,EAAE;IACN,CAAC;IACD,MAAMC,oBAAoB,GAAIC,EAAE,IAAKF,kBAAkB,CAACE,EAAE,CAAC;IAC3D,SAASC,QAAQA,CAACC,IAAI,EAAEC,MAAM,EAAE;MAC/B,IAAIA,MAAM,EAAE;QACX,IAAIX,UAAU,CAACY,IAAI,CAACF,IAAI,CAAC,EAAE;UAC1B,OAAOA,IAAI,CAACG,OAAO,CAACZ,aAAa,EAAEM,oBAAoB,CAAC;QACzD;MACD,CAAC,MACI;QACJ,IAAIH,kBAAkB,CAACQ,IAAI,CAACF,IAAI,CAAC,EAAE;UAClC,OAAOA,IAAI,CAACG,OAAO,CAACR,qBAAqB,EAAEE,oBAAoB,CAAC;QACjE;MACD;MACA,OAAOG,IAAI;IACZ;IACA,MAAMI,KAAK,GAAG,cAAc;IAC5B,SAASC,IAAIA,CAACC,KAAK,EAAEC,GAAG,EAAE;MACzB,IAAId,MAAM,GAAG,OAAOa,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAGA,KAAK,CAACb,MAAM;MAC7Dc,GAAG,GAAGA,GAAG,IAAI,EAAE;MACf,MAAMC,GAAG,GAAG;QACXL,OAAO,EAAEA,CAACM,IAAI,EAAEC,GAAG,KAAK;UACvB,IAAIC,SAAS,GAAG,OAAOD,GAAG,KAAK,QAAQ,GAAGA,GAAG,GAAGA,GAAG,CAACjB,MAAM;UAC1DkB,SAAS,GAAGA,SAAS,CAACR,OAAO,CAACC,KAAK,EAAE,IAAI,CAAC;UAC1CX,MAAM,GAAGA,MAAM,CAACU,OAAO,CAACM,IAAI,EAAEE,SAAS,CAAC;UACxC,OAAOH,GAAG;QACX,CAAC;QACDI,QAAQ,EAAEA,CAAA,KAAM;UACf,OAAO,IAAIpB,MAAM,CAACC,MAAM,EAAEc,GAAG,CAAC;QAC/B;MACD,CAAC;MACD,OAAOC,GAAG;IACX;IACA,SAASK,QAAQA,CAACC,IAAI,EAAE;MACvB,IAAI;QACHA,IAAI,GAAGC,SAAS,CAACD,IAAI,CAAC,CAACX,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;MAC5C,CAAC,CACD,MAAM;QACL,OAAO,IAAI;MACZ;MACA,OAAOW,IAAI;IACZ;IACA,MAAME,QAAQ,GAAG;MAAEC,IAAI,EAAEA,CAAA,KAAM;IAAK,CAAC;IACrC,SAASC,UAAUA,CAACC,QAAQ,EAAEC,KAAK,EAAE;MACpC;MACA;MACA,MAAMC,GAAG,GAAGF,QAAQ,CAAChB,OAAO,CAAC,KAAK,EAAE,CAACmB,KAAK,EAAEC,MAAM,EAAEC,GAAG,KAAK;UAC3D,IAAIC,OAAO,GAAG,KAAK;UACnB,IAAIC,IAAI,GAAGH,MAAM;UACjB,OAAO,EAAEG,IAAI,IAAI,CAAC,IAAIF,GAAG,CAACE,IAAI,CAAC,KAAK,IAAI,EACvCD,OAAO,GAAG,CAACA,OAAO;UACnB,IAAIA,OAAO,EAAE;YACZ;YACA;YACA,OAAO,GAAG;UACX,CAAC,MACI;YACJ;YACA,OAAO,IAAI;UACZ;QACD,CAAC,CAAC;QAAEE,KAAK,GAAGN,GAAG,CAACO,KAAK,CAAC,KAAK,CAAC;MAC5B,IAAIC,CAAC,GAAG,CAAC;MACT;MACA,IAAI,CAACF,KAAK,CAAC,CAAC,CAAC,CAACG,IAAI,CAAC,CAAC,EAAE;QACrBH,KAAK,CAACI,KAAK,CAAC,CAAC;MACd;MACA,IAAIJ,KAAK,CAACK,MAAM,GAAG,CAAC,IAAI,CAACL,KAAK,CAACA,KAAK,CAACK,MAAM,GAAG,CAAC,CAAC,CAACF,IAAI,CAAC,CAAC,EAAE;QACxDH,KAAK,CAACM,GAAG,CAAC,CAAC;MACZ;MACA,IAAIb,KAAK,EAAE;QACV,IAAIO,KAAK,CAACK,MAAM,GAAGZ,KAAK,EAAE;UACzBO,KAAK,CAACO,MAAM,CAACd,KAAK,CAAC;QACpB,CAAC,MACI;UACJ,OAAOO,KAAK,CAACK,MAAM,GAAGZ,KAAK,EAC1BO,KAAK,CAACQ,IAAI,CAAC,EAAE,CAAC;QAChB;MACD;MACA,OAAON,CAAC,GAAGF,KAAK,CAACK,MAAM,EAAEH,CAAC,EAAE,EAAE;QAC7B;QACAF,KAAK,CAACE,CAAC,CAAC,GAAGF,KAAK,CAACE,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC3B,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC;MACjD;MACA,OAAOwB,KAAK;IACb;IACA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;IACC,SAASS,KAAKA,CAACZ,GAAG,EAAEa,CAAC,EAAEC,MAAM,EAAE;MAC9B,MAAMC,CAAC,GAAGf,GAAG,CAACQ,MAAM;MACpB,IAAIO,CAAC,KAAK,CAAC,EAAE;QACZ,OAAO,EAAE;MACV;MACA;MACA,IAAIC,OAAO,GAAG,CAAC;MACf;MACA,OAAOA,OAAO,GAAGD,CAAC,EAAE;QACnB,MAAME,QAAQ,GAAGjB,GAAG,CAACkB,MAAM,CAACH,CAAC,GAAGC,OAAO,GAAG,CAAC,CAAC;QAC5C,IAAIC,QAAQ,KAAKJ,CAAC,IAAI,CAACC,MAAM,EAAE;UAC9BE,OAAO,EAAE;QACV,CAAC,MACI,IAAIC,QAAQ,KAAKJ,CAAC,IAAIC,MAAM,EAAE;UAClCE,OAAO,EAAE;QACV,CAAC,MACI;UACJ;QACD;MACD;MACA,OAAOhB,GAAG,CAACmB,KAAK,CAAC,CAAC,EAAEJ,CAAC,GAAGC,OAAO,CAAC;IACjC;IACA,SAASI,kBAAkBA,CAACpB,GAAG,EAAEqB,CAAC,EAAE;MACnC,IAAIrB,GAAG,CAACsB,OAAO,CAACD,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;QAC7B,OAAO,CAAC,CAAC;MACV;MACA,IAAIE,KAAK,GAAG,CAAC;MACb,KAAK,IAAIlB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,GAAG,CAACQ,MAAM,EAAEH,CAAC,EAAE,EAAE;QACpC,IAAIL,GAAG,CAACK,CAAC,CAAC,KAAK,IAAI,EAAE;UACpBA,CAAC,EAAE;QACJ,CAAC,MACI,IAAIL,GAAG,CAACK,CAAC,CAAC,KAAKgB,CAAC,CAAC,CAAC,CAAC,EAAE;UACzBE,KAAK,EAAE;QACR,CAAC,MACI,IAAIvB,GAAG,CAACK,CAAC,CAAC,KAAKgB,CAAC,CAAC,CAAC,CAAC,EAAE;UACzBE,KAAK,EAAE;UACP,IAAIA,KAAK,GAAG,CAAC,EAAE;YACd,OAAOlB,CAAC;UACT;QACD;MACD;MACA,OAAO,CAAC,CAAC;IACV;IAEA,SAASmB,UAAUA,CAACC,GAAG,EAAEC,IAAI,EAAEC,GAAG,EAAEC,KAAK,EAAE;MAC1C,MAAMtC,IAAI,GAAGoC,IAAI,CAACpC,IAAI;MACtB,MAAMuC,KAAK,GAAGH,IAAI,CAACG,KAAK,GAAGtD,QAAQ,CAACmD,IAAI,CAACG,KAAK,CAAC,GAAG,IAAI;MACtD,MAAMC,IAAI,GAAGL,GAAG,CAAC,CAAC,CAAC,CAAC9C,OAAO,CAAC,aAAa,EAAE,IAAI,CAAC;MAChD,IAAI8C,GAAG,CAAC,CAAC,CAAC,CAACP,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QAC7BU,KAAK,CAACG,KAAK,CAACC,MAAM,GAAG,IAAI;QACzB,MAAMC,KAAK,GAAG;UACbC,IAAI,EAAE,MAAM;UACZP,GAAG;UACHrC,IAAI;UACJuC,KAAK;UACLC,IAAI;UACJK,MAAM,EAAEP,KAAK,CAACQ,YAAY,CAACN,IAAI;QAChC,CAAC;QACDF,KAAK,CAACG,KAAK,CAACC,MAAM,GAAG,KAAK;QAC1B,OAAOC,KAAK;MACb;MACA,OAAO;QACNC,IAAI,EAAE,OAAO;QACbP,GAAG;QACHrC,IAAI;QACJuC,KAAK;QACLC,IAAI,EAAEvD,QAAQ,CAACuD,IAAI;MACpB,CAAC;IACF;IACA,SAASO,sBAAsBA,CAACV,GAAG,EAAEG,IAAI,EAAE;MAC1C,MAAMQ,iBAAiB,GAAGX,GAAG,CAAC7B,KAAK,CAAC,eAAe,CAAC;MACpD,IAAIwC,iBAAiB,KAAK,IAAI,EAAE;QAC/B,OAAOR,IAAI;MACZ;MACA,MAAMS,YAAY,GAAGD,iBAAiB,CAAC,CAAC,CAAC;MACzC,OAAOR,IAAI,CACT1B,KAAK,CAAC,IAAI,CAAC,CACXoC,GAAG,CAACC,IAAI,IAAI;QACZ,MAAMC,iBAAiB,GAAGD,IAAI,CAAC3C,KAAK,CAAC,MAAM,CAAC;QAC5C,IAAI4C,iBAAiB,KAAK,IAAI,EAAE;UAC/B,OAAOD,IAAI;QACZ;QACA,MAAM,CAACE,YAAY,CAAC,GAAGD,iBAAiB;QACxC,IAAIC,YAAY,CAACnC,MAAM,IAAI+B,YAAY,CAAC/B,MAAM,EAAE;UAC/C,OAAOiC,IAAI,CAACtB,KAAK,CAACoB,YAAY,CAAC/B,MAAM,CAAC;QACvC;QACA,OAAOiC,IAAI;MACZ,CAAC,CAAC,CACDG,IAAI,CAAC,IAAI,CAAC;IACb;IACA;AACD;AACA;IACC,MAAMC,UAAU,CAAC;MAChBC,OAAO;MACPC,KAAK,CAAC,CAAC;MACPnB,KAAK,CAAC,CAAC;MACPoB,WAAWA,CAACF,OAAO,EAAE;QACpB,IAAI,CAACA,OAAO,GAAGA,OAAO,IAAInG,OAAO,CAACgB,QAAQ;MAC3C;MACAsF,KAAKA,CAACC,GAAG,EAAE;QACV,MAAMzB,GAAG,GAAG,IAAI,CAACsB,KAAK,CAACI,KAAK,CAACC,OAAO,CAAC3D,IAAI,CAACyD,GAAG,CAAC;QAC9C,IAAIzB,GAAG,IAAIA,GAAG,CAAC,CAAC,CAAC,CAACjB,MAAM,GAAG,CAAC,EAAE;UAC7B,OAAO;YACN0B,IAAI,EAAE,OAAO;YACbP,GAAG,EAAEF,GAAG,CAAC,CAAC;UACX,CAAC;QACF;MACD;MACA4B,IAAIA,CAACH,GAAG,EAAE;QACT,MAAMzB,GAAG,GAAG,IAAI,CAACsB,KAAK,CAACI,KAAK,CAACE,IAAI,CAAC5D,IAAI,CAACyD,GAAG,CAAC;QAC3C,IAAIzB,GAAG,EAAE;UACR,MAAMK,IAAI,GAAGL,GAAG,CAAC,CAAC,CAAC,CAAC9C,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;UAC5C,OAAO;YACNuD,IAAI,EAAE,MAAM;YACZP,GAAG,EAAEF,GAAG,CAAC,CAAC,CAAC;YACX6B,cAAc,EAAE,UAAU;YAC1BxB,IAAI,EAAE,CAAC,IAAI,CAACgB,OAAO,CAACxF,QAAQ,GACzBsD,KAAK,CAACkB,IAAI,EAAE,IAAI,CAAC,GACjBA;UACJ,CAAC;QACF;MACD;MACAyB,MAAMA,CAACL,GAAG,EAAE;QACX,MAAMzB,GAAG,GAAG,IAAI,CAACsB,KAAK,CAACI,KAAK,CAACI,MAAM,CAAC9D,IAAI,CAACyD,GAAG,CAAC;QAC7C,IAAIzB,GAAG,EAAE;UACR,MAAME,GAAG,GAAGF,GAAG,CAAC,CAAC,CAAC;UAClB,MAAMK,IAAI,GAAGO,sBAAsB,CAACV,GAAG,EAAEF,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;UACtD,OAAO;YACNS,IAAI,EAAE,MAAM;YACZP,GAAG;YACH6B,IAAI,EAAE/B,GAAG,CAAC,CAAC,CAAC,GAAGA,GAAG,CAAC,CAAC,CAAC,CAACnB,IAAI,CAAC,CAAC,CAAC3B,OAAO,CAAC,IAAI,CAACoE,KAAK,CAACU,MAAM,CAACC,cAAc,EAAE,IAAI,CAAC,GAAGjC,GAAG,CAAC,CAAC,CAAC;YACrFK;UACD,CAAC;QACF;MACD;MACA6B,OAAOA,CAACT,GAAG,EAAE;QACZ,MAAMzB,GAAG,GAAG,IAAI,CAACsB,KAAK,CAACI,KAAK,CAACQ,OAAO,CAAClE,IAAI,CAACyD,GAAG,CAAC;QAC9C,IAAIzB,GAAG,EAAE;UACR,IAAIK,IAAI,GAAGL,GAAG,CAAC,CAAC,CAAC,CAACnB,IAAI,CAAC,CAAC;UACxB;UACA,IAAI,IAAI,CAAC5B,IAAI,CAACoD,IAAI,CAAC,EAAE;YACpB,MAAM8B,OAAO,GAAGhD,KAAK,CAACkB,IAAI,EAAE,GAAG,CAAC;YAChC,IAAI,IAAI,CAACgB,OAAO,CAACxF,QAAQ,EAAE;cAC1BwE,IAAI,GAAG8B,OAAO,CAACtD,IAAI,CAAC,CAAC;YACtB,CAAC,MACI,IAAI,CAACsD,OAAO,IAAI,IAAI,CAAClF,IAAI,CAACkF,OAAO,CAAC,EAAE;cACxC;cACA9B,IAAI,GAAG8B,OAAO,CAACtD,IAAI,CAAC,CAAC;YACtB;UACD;UACA,OAAO;YACN4B,IAAI,EAAE,SAAS;YACfP,GAAG,EAAEF,GAAG,CAAC,CAAC,CAAC;YACXoC,KAAK,EAAEpC,GAAG,CAAC,CAAC,CAAC,CAACjB,MAAM;YACpBsB,IAAI;YACJK,MAAM,EAAE,IAAI,CAACP,KAAK,CAAC6B,MAAM,CAAC3B,IAAI;UAC/B,CAAC;QACF;MACD;MACAgC,EAAEA,CAACZ,GAAG,EAAE;QACP,MAAMzB,GAAG,GAAG,IAAI,CAACsB,KAAK,CAACI,KAAK,CAACW,EAAE,CAACrE,IAAI,CAACyD,GAAG,CAAC;QACzC,IAAIzB,GAAG,EAAE;UACR,OAAO;YACNS,IAAI,EAAE,IAAI;YACVP,GAAG,EAAEf,KAAK,CAACa,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI;UACxB,CAAC;QACF;MACD;MACAsC,UAAUA,CAACb,GAAG,EAAE;QACf,MAAMzB,GAAG,GAAG,IAAI,CAACsB,KAAK,CAACI,KAAK,CAACY,UAAU,CAACtE,IAAI,CAACyD,GAAG,CAAC;QACjD,IAAIzB,GAAG,EAAE;UACR,IAAIuC,KAAK,GAAGpD,KAAK,CAACa,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAACrB,KAAK,CAAC,IAAI,CAAC;UAC3C,IAAIuB,GAAG,GAAG,EAAE;UACZ,IAAIG,IAAI,GAAG,EAAE;UACb,MAAMK,MAAM,GAAG,EAAE;UACjB,OAAO6B,KAAK,CAACxD,MAAM,GAAG,CAAC,EAAE;YACxB,IAAIyD,YAAY,GAAG,KAAK;YACxB,MAAMC,YAAY,GAAG,EAAE;YACvB,IAAI7D,CAAC;YACL,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2D,KAAK,CAACxD,MAAM,EAAEH,CAAC,EAAE,EAAE;cAClC;cACA,IAAI,UAAU,CAAC3B,IAAI,CAACsF,KAAK,CAAC3D,CAAC,CAAC,CAAC,EAAE;gBAC9B6D,YAAY,CAACvD,IAAI,CAACqD,KAAK,CAAC3D,CAAC,CAAC,CAAC;gBAC3B4D,YAAY,GAAG,IAAI;cACpB,CAAC,MACI,IAAI,CAACA,YAAY,EAAE;gBACvBC,YAAY,CAACvD,IAAI,CAACqD,KAAK,CAAC3D,CAAC,CAAC,CAAC;cAC5B,CAAC,MACI;gBACJ;cACD;YACD;YACA2D,KAAK,GAAGA,KAAK,CAAC7C,KAAK,CAACd,CAAC,CAAC;YACtB,MAAM8D,UAAU,GAAGD,YAAY,CAACtB,IAAI,CAAC,IAAI,CAAC;YAC1C,MAAMwB,WAAW,GAAGD;YACnB;YAAA,CACCxF,OAAO,CAAC,gCAAgC,EAAE,UAAU,CAAC,CACrDA,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC;YACjCgD,GAAG,GAAGA,GAAG,GAAG,GAAGA,GAAG,KAAKwC,UAAU,EAAE,GAAGA,UAAU;YAChDrC,IAAI,GAAGA,IAAI,GAAG,GAAGA,IAAI,KAAKsC,WAAW,EAAE,GAAGA,WAAW;YACrD;YACA;YACA,MAAMC,GAAG,GAAG,IAAI,CAACzC,KAAK,CAACG,KAAK,CAACsC,GAAG;YAChC,IAAI,CAACzC,KAAK,CAACG,KAAK,CAACsC,GAAG,GAAG,IAAI;YAC3B,IAAI,CAACzC,KAAK,CAAC0C,WAAW,CAACF,WAAW,EAAEjC,MAAM,EAAE,IAAI,CAAC;YACjD,IAAI,CAACP,KAAK,CAACG,KAAK,CAACsC,GAAG,GAAGA,GAAG;YAC1B;YACA,IAAIL,KAAK,CAACxD,MAAM,KAAK,CAAC,EAAE;cACvB;YACD;YACA,MAAM+D,SAAS,GAAGpC,MAAM,CAACA,MAAM,CAAC3B,MAAM,GAAG,CAAC,CAAC;YAC3C,IAAI+D,SAAS,EAAErC,IAAI,KAAK,MAAM,EAAE;cAC/B;cACA;YACD,CAAC,MACI,IAAIqC,SAAS,EAAErC,IAAI,KAAK,YAAY,EAAE;cAC1C;cACA,MAAMsC,QAAQ,GAAGD,SAAS;cAC1B,MAAME,OAAO,GAAGD,QAAQ,CAAC7C,GAAG,GAAG,IAAI,GAAGqC,KAAK,CAACpB,IAAI,CAAC,IAAI,CAAC;cACtD,MAAM8B,QAAQ,GAAG,IAAI,CAACX,UAAU,CAACU,OAAO,CAAC;cACzCtC,MAAM,CAACA,MAAM,CAAC3B,MAAM,GAAG,CAAC,CAAC,GAAGkE,QAAQ;cACpC/C,GAAG,GAAGA,GAAG,CAACgD,SAAS,CAAC,CAAC,EAAEhD,GAAG,CAACnB,MAAM,GAAGgE,QAAQ,CAAC7C,GAAG,CAACnB,MAAM,CAAC,GAAGkE,QAAQ,CAAC/C,GAAG;cACvEG,IAAI,GAAGA,IAAI,CAAC6C,SAAS,CAAC,CAAC,EAAE7C,IAAI,CAACtB,MAAM,GAAGgE,QAAQ,CAAC1C,IAAI,CAACtB,MAAM,CAAC,GAAGkE,QAAQ,CAAC5C,IAAI;cAC5E;YACD,CAAC,MACI,IAAIyC,SAAS,EAAErC,IAAI,KAAK,MAAM,EAAE;cACpC;cACA,MAAMsC,QAAQ,GAAGD,SAAS;cAC1B,MAAME,OAAO,GAAGD,QAAQ,CAAC7C,GAAG,GAAG,IAAI,GAAGqC,KAAK,CAACpB,IAAI,CAAC,IAAI,CAAC;cACtD,MAAM8B,QAAQ,GAAG,IAAI,CAACE,IAAI,CAACH,OAAO,CAAC;cACnCtC,MAAM,CAACA,MAAM,CAAC3B,MAAM,GAAG,CAAC,CAAC,GAAGkE,QAAQ;cACpC/C,GAAG,GAAGA,GAAG,CAACgD,SAAS,CAAC,CAAC,EAAEhD,GAAG,CAACnB,MAAM,GAAG+D,SAAS,CAAC5C,GAAG,CAACnB,MAAM,CAAC,GAAGkE,QAAQ,CAAC/C,GAAG;cACxEG,IAAI,GAAGA,IAAI,CAAC6C,SAAS,CAAC,CAAC,EAAE7C,IAAI,CAACtB,MAAM,GAAGgE,QAAQ,CAAC7C,GAAG,CAACnB,MAAM,CAAC,GAAGkE,QAAQ,CAAC/C,GAAG;cAC1EqC,KAAK,GAAGS,OAAO,CAACE,SAAS,CAACxC,MAAM,CAACA,MAAM,CAAC3B,MAAM,GAAG,CAAC,CAAC,CAACmB,GAAG,CAACnB,MAAM,CAAC,CAACJ,KAAK,CAAC,IAAI,CAAC;cAC3E;YACD;UACD;UACA,OAAO;YACN8B,IAAI,EAAE,YAAY;YAClBP,GAAG;YACHQ,MAAM;YACNL;UACD,CAAC;QACF;MACD;MACA8C,IAAIA,CAAC1B,GAAG,EAAE;QACT,IAAIzB,GAAG,GAAG,IAAI,CAACsB,KAAK,CAACI,KAAK,CAACyB,IAAI,CAACnF,IAAI,CAACyD,GAAG,CAAC;QACzC,IAAIzB,GAAG,EAAE;UACR,IAAIoD,IAAI,GAAGpD,GAAG,CAAC,CAAC,CAAC,CAACnB,IAAI,CAAC,CAAC;UACxB,MAAMwE,SAAS,GAAGD,IAAI,CAACrE,MAAM,GAAG,CAAC;UACjC,MAAMoE,IAAI,GAAG;YACZ1C,IAAI,EAAE,MAAM;YACZP,GAAG,EAAE,EAAE;YACPoD,OAAO,EAAED,SAAS;YAClBE,KAAK,EAAEF,SAAS,GAAG,CAACD,IAAI,CAAC1D,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE;YAC1C8D,KAAK,EAAE,KAAK;YACZC,KAAK,EAAE;UACR,CAAC;UACDL,IAAI,GAAGC,SAAS,GAAG,aAAaD,IAAI,CAAC1D,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,KAAK0D,IAAI,EAAE;UAC9D,IAAI,IAAI,CAAC/B,OAAO,CAACxF,QAAQ,EAAE;YAC1BuH,IAAI,GAAGC,SAAS,GAAGD,IAAI,GAAG,OAAO;UAClC;UACA;UACA,MAAMM,SAAS,GAAG,IAAInH,MAAM,CAAC,WAAW6G,IAAI,+BAA+B,CAAC;UAC5E,IAAIO,iBAAiB,GAAG,KAAK;UAC7B;UACA,OAAOlC,GAAG,EAAE;YACX,IAAImC,QAAQ,GAAG,KAAK;YACpB,IAAI1D,GAAG,GAAG,EAAE;YACZ,IAAI2D,YAAY,GAAG,EAAE;YACrB,IAAI,EAAE7D,GAAG,GAAG0D,SAAS,CAAC1F,IAAI,CAACyD,GAAG,CAAC,CAAC,EAAE;cACjC;YACD;YACA,IAAI,IAAI,CAACH,KAAK,CAACI,KAAK,CAACW,EAAE,CAACpF,IAAI,CAACwE,GAAG,CAAC,EAAE;cAAE;cACpC;YACD;YACAvB,GAAG,GAAGF,GAAG,CAAC,CAAC,CAAC;YACZyB,GAAG,GAAGA,GAAG,CAACyB,SAAS,CAAChD,GAAG,CAACnB,MAAM,CAAC;YAC/B,IAAI+E,IAAI,GAAG9D,GAAG,CAAC,CAAC,CAAC,CAACrB,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAACzB,OAAO,CAAC,MAAM,EAAG6G,CAAC,IAAK,GAAG,CAACC,MAAM,CAAC,CAAC,GAAGD,CAAC,CAAChF,MAAM,CAAC,CAAC;YACpF,IAAIkF,QAAQ,GAAGxC,GAAG,CAAC9C,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACpC,IAAIuF,SAAS,GAAG,CAACJ,IAAI,CAACjF,IAAI,CAAC,CAAC;YAC5B,IAAIsF,MAAM,GAAG,CAAC;YACd,IAAI,IAAI,CAAC9C,OAAO,CAACxF,QAAQ,EAAE;cAC1BsI,MAAM,GAAG,CAAC;cACVN,YAAY,GAAGC,IAAI,CAACM,SAAS,CAAC,CAAC;YAChC,CAAC,MACI,IAAIF,SAAS,EAAE;cACnBC,MAAM,GAAGnE,GAAG,CAAC,CAAC,CAAC,CAACjB,MAAM,GAAG,CAAC;YAC3B,CAAC,MACI;cACJoF,MAAM,GAAGnE,GAAG,CAAC,CAAC,CAAC,CAACqE,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;cAChCF,MAAM,GAAGA,MAAM,GAAG,CAAC,GAAG,CAAC,GAAGA,MAAM,CAAC,CAAC;cAClCN,YAAY,GAAGC,IAAI,CAACpE,KAAK,CAACyE,MAAM,CAAC;cACjCA,MAAM,IAAInE,GAAG,CAAC,CAAC,CAAC,CAACjB,MAAM;YACxB;YACA,IAAImF,SAAS,IAAI,MAAM,CAACjH,IAAI,CAACgH,QAAQ,CAAC,EAAE;cAAE;cACzC/D,GAAG,IAAI+D,QAAQ,GAAG,IAAI;cACtBxC,GAAG,GAAGA,GAAG,CAACyB,SAAS,CAACe,QAAQ,CAAClF,MAAM,GAAG,CAAC,CAAC;cACxC6E,QAAQ,GAAG,IAAI;YAChB;YACA,IAAI,CAACA,QAAQ,EAAE;cACd,MAAMU,eAAe,GAAG,IAAI/H,MAAM,CAAC,QAAQgI,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEL,MAAM,GAAG,CAAC,CAAC,qDAAqD,CAAC;cACxH,MAAMM,OAAO,GAAG,IAAIlI,MAAM,CAAC,QAAQgI,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEL,MAAM,GAAG,CAAC,CAAC,oDAAoD,CAAC;cAC/G,MAAMO,gBAAgB,GAAG,IAAInI,MAAM,CAAC,QAAQgI,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEL,MAAM,GAAG,CAAC,CAAC,iBAAiB,CAAC;cACrF,MAAMQ,iBAAiB,GAAG,IAAIpI,MAAM,CAAC,QAAQgI,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEL,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC;cACzE;cACA,OAAO1C,GAAG,EAAE;gBACX,MAAMmD,OAAO,GAAGnD,GAAG,CAAC9C,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrCsF,QAAQ,GAAGW,OAAO;gBAClB;gBACA,IAAI,IAAI,CAACvD,OAAO,CAACxF,QAAQ,EAAE;kBAC1BoI,QAAQ,GAAGA,QAAQ,CAAC/G,OAAO,CAAC,yBAAyB,EAAE,IAAI,CAAC;gBAC7D;gBACA;gBACA,IAAIwH,gBAAgB,CAACzH,IAAI,CAACgH,QAAQ,CAAC,EAAE;kBACpC;gBACD;gBACA;gBACA,IAAIU,iBAAiB,CAAC1H,IAAI,CAACgH,QAAQ,CAAC,EAAE;kBACrC;gBACD;gBACA;gBACA,IAAIK,eAAe,CAACrH,IAAI,CAACgH,QAAQ,CAAC,EAAE;kBACnC;gBACD;gBACA;gBACA,IAAIQ,OAAO,CAACxH,IAAI,CAACwE,GAAG,CAAC,EAAE;kBACtB;gBACD;gBACA,IAAIwC,QAAQ,CAACI,MAAM,CAAC,MAAM,CAAC,IAAIF,MAAM,IAAI,CAACF,QAAQ,CAACpF,IAAI,CAAC,CAAC,EAAE;kBAAE;kBAC5DgF,YAAY,IAAI,IAAI,GAAGI,QAAQ,CAACvE,KAAK,CAACyE,MAAM,CAAC;gBAC9C,CAAC,MACI;kBACJ;kBACA,IAAID,SAAS,EAAE;oBACd;kBACD;kBACA;kBACA,IAAIJ,IAAI,CAACO,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;oBAAE;oBAC/B;kBACD;kBACA,IAAIK,gBAAgB,CAACzH,IAAI,CAAC6G,IAAI,CAAC,EAAE;oBAChC;kBACD;kBACA,IAAIa,iBAAiB,CAAC1H,IAAI,CAAC6G,IAAI,CAAC,EAAE;oBACjC;kBACD;kBACA,IAAIW,OAAO,CAACxH,IAAI,CAAC6G,IAAI,CAAC,EAAE;oBACvB;kBACD;kBACAD,YAAY,IAAI,IAAI,GAAGI,QAAQ;gBAChC;gBACA,IAAI,CAACC,SAAS,IAAI,CAACD,QAAQ,CAACpF,IAAI,CAAC,CAAC,EAAE;kBAAE;kBACrCqF,SAAS,GAAG,IAAI;gBACjB;gBACAhE,GAAG,IAAI0E,OAAO,GAAG,IAAI;gBACrBnD,GAAG,GAAGA,GAAG,CAACyB,SAAS,CAAC0B,OAAO,CAAC7F,MAAM,GAAG,CAAC,CAAC;gBACvC+E,IAAI,GAAGG,QAAQ,CAACvE,KAAK,CAACyE,MAAM,CAAC;cAC9B;YACD;YACA,IAAI,CAAChB,IAAI,CAACK,KAAK,EAAE;cAChB;cACA,IAAIG,iBAAiB,EAAE;gBACtBR,IAAI,CAACK,KAAK,GAAG,IAAI;cAClB,CAAC,MACI,IAAI,WAAW,CAACvG,IAAI,CAACiD,GAAG,CAAC,EAAE;gBAC/ByD,iBAAiB,GAAG,IAAI;cACzB;YACD;YACA,IAAIkB,MAAM,GAAG,IAAI;YACjB,IAAIC,SAAS;YACb;YACA,IAAI,IAAI,CAACzD,OAAO,CAAC1F,GAAG,EAAE;cACrBkJ,MAAM,GAAG,aAAa,CAAC7G,IAAI,CAAC6F,YAAY,CAAC;cACzC,IAAIgB,MAAM,EAAE;gBACXC,SAAS,GAAGD,MAAM,CAAC,CAAC,CAAC,KAAK,MAAM;gBAChChB,YAAY,GAAGA,YAAY,CAAC3G,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC;cACxD;YACD;YACAiG,IAAI,CAACM,KAAK,CAACvE,IAAI,CAAC;cACfuB,IAAI,EAAE,WAAW;cACjBP,GAAG;cACH6E,IAAI,EAAE,CAAC,CAACF,MAAM;cACdG,OAAO,EAAEF,SAAS;cAClBtB,KAAK,EAAE,KAAK;cACZnD,IAAI,EAAEwD,YAAY;cAClBnD,MAAM,EAAE;YACT,CAAC,CAAC;YACFyC,IAAI,CAACjD,GAAG,IAAIA,GAAG;UAChB;UACA;UACAiD,IAAI,CAACM,KAAK,CAACN,IAAI,CAACM,KAAK,CAAC1E,MAAM,GAAG,CAAC,CAAC,CAACmB,GAAG,GAAGiD,IAAI,CAACM,KAAK,CAACN,IAAI,CAACM,KAAK,CAAC1E,MAAM,GAAG,CAAC,CAAC,CAACmB,GAAG,CAAC+E,OAAO,CAAC,CAAC;UACvF9B,IAAI,CAACM,KAAK,CAACN,IAAI,CAACM,KAAK,CAAC1E,MAAM,GAAG,CAAC,CAAC,CAACsB,IAAI,GAAG8C,IAAI,CAACM,KAAK,CAACN,IAAI,CAACM,KAAK,CAAC1E,MAAM,GAAG,CAAC,CAAC,CAACsB,IAAI,CAAC4E,OAAO,CAAC,CAAC;UACzF9B,IAAI,CAACjD,GAAG,GAAGiD,IAAI,CAACjD,GAAG,CAAC+E,OAAO,CAAC,CAAC;UAC7B;UACA,KAAK,IAAIrG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuE,IAAI,CAACM,KAAK,CAAC1E,MAAM,EAAEH,CAAC,EAAE,EAAE;YAC3C,IAAI,CAACuB,KAAK,CAACG,KAAK,CAACsC,GAAG,GAAG,KAAK;YAC5BO,IAAI,CAACM,KAAK,CAAC7E,CAAC,CAAC,CAAC8B,MAAM,GAAG,IAAI,CAACP,KAAK,CAAC0C,WAAW,CAACM,IAAI,CAACM,KAAK,CAAC7E,CAAC,CAAC,CAACyB,IAAI,EAAE,EAAE,CAAC;YACrE,IAAI,CAAC8C,IAAI,CAACK,KAAK,EAAE;cAChB;cACA,MAAM0B,OAAO,GAAG/B,IAAI,CAACM,KAAK,CAAC7E,CAAC,CAAC,CAAC8B,MAAM,CAACyE,MAAM,CAACpB,CAAC,IAAIA,CAAC,CAACtD,IAAI,KAAK,OAAO,CAAC;cACpE,MAAM2E,qBAAqB,GAAGF,OAAO,CAACnG,MAAM,GAAG,CAAC,IAAImG,OAAO,CAACG,IAAI,CAACtB,CAAC,IAAI,QAAQ,CAAC9G,IAAI,CAAC8G,CAAC,CAAC7D,GAAG,CAAC,CAAC;cAC3FiD,IAAI,CAACK,KAAK,GAAG4B,qBAAqB;YACnC;UACD;UACA;UACA,IAAIjC,IAAI,CAACK,KAAK,EAAE;YACf,KAAK,IAAI5E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuE,IAAI,CAACM,KAAK,CAAC1E,MAAM,EAAEH,CAAC,EAAE,EAAE;cAC3CuE,IAAI,CAACM,KAAK,CAAC7E,CAAC,CAAC,CAAC4E,KAAK,GAAG,IAAI;YAC3B;UACD;UACA,OAAOL,IAAI;QACZ;MACD;MACApG,IAAIA,CAAC0E,GAAG,EAAE;QACT,MAAMzB,GAAG,GAAG,IAAI,CAACsB,KAAK,CAACI,KAAK,CAAC3E,IAAI,CAACiB,IAAI,CAACyD,GAAG,CAAC;QAC3C,IAAIzB,GAAG,EAAE;UACR,MAAMQ,KAAK,GAAG;YACbC,IAAI,EAAE,MAAM;YACZiB,KAAK,EAAE,IAAI;YACXxB,GAAG,EAAEF,GAAG,CAAC,CAAC,CAAC;YACXsF,GAAG,EAAEtF,GAAG,CAAC,CAAC,CAAC,KAAK,KAAK,IAAIA,GAAG,CAAC,CAAC,CAAC,KAAK,QAAQ,IAAIA,GAAG,CAAC,CAAC,CAAC,KAAK,OAAO;YAClEK,IAAI,EAAEL,GAAG,CAAC,CAAC;UACZ,CAAC;UACD,OAAOQ,KAAK;QACb;MACD;MACA+E,GAAGA,CAAC9D,GAAG,EAAE;QACR,MAAMzB,GAAG,GAAG,IAAI,CAACsB,KAAK,CAACI,KAAK,CAAC6D,GAAG,CAACvH,IAAI,CAACyD,GAAG,CAAC;QAC1C,IAAIzB,GAAG,EAAE;UACR,MAAMwF,GAAG,GAAGxF,GAAG,CAAC,CAAC,CAAC,CAACyF,WAAW,CAAC,CAAC,CAACvI,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;UACrD,MAAMW,IAAI,GAAGmC,GAAG,CAAC,CAAC,CAAC,GAAGA,GAAG,CAAC,CAAC,CAAC,CAAC9C,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,CAACA,OAAO,CAAC,IAAI,CAACoE,KAAK,CAACU,MAAM,CAACC,cAAc,EAAE,IAAI,CAAC,GAAG,EAAE;UAC3G,MAAM7B,KAAK,GAAGJ,GAAG,CAAC,CAAC,CAAC,GAAGA,GAAG,CAAC,CAAC,CAAC,CAACkD,SAAS,CAAC,CAAC,EAAElD,GAAG,CAAC,CAAC,CAAC,CAACjB,MAAM,GAAG,CAAC,CAAC,CAAC7B,OAAO,CAAC,IAAI,CAACoE,KAAK,CAACU,MAAM,CAACC,cAAc,EAAE,IAAI,CAAC,GAAGjC,GAAG,CAAC,CAAC,CAAC;UACtH,OAAO;YACNS,IAAI,EAAE,KAAK;YACX+E,GAAG;YACHtF,GAAG,EAAEF,GAAG,CAAC,CAAC,CAAC;YACXnC,IAAI;YACJuC;UACD,CAAC;QACF;MACD;MACAsF,KAAKA,CAACjE,GAAG,EAAE;QACV,MAAMzB,GAAG,GAAG,IAAI,CAACsB,KAAK,CAACI,KAAK,CAACgE,KAAK,CAAC1H,IAAI,CAACyD,GAAG,CAAC;QAC5C,IAAI,CAACzB,GAAG,EAAE;UACT;QACD;QACA,IAAI,CAAC,MAAM,CAAC/C,IAAI,CAAC+C,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;UACzB;UACA;QACD;QACA,MAAM2F,OAAO,GAAG1H,UAAU,CAAC+B,GAAG,CAAC,CAAC,CAAC,CAAC;QAClC,MAAM4F,MAAM,GAAG5F,GAAG,CAAC,CAAC,CAAC,CAAC9C,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAACyB,KAAK,CAAC,GAAG,CAAC;QAC1D,MAAMkH,IAAI,GAAG7F,GAAG,CAAC,CAAC,CAAC,IAAIA,GAAG,CAAC,CAAC,CAAC,CAACnB,IAAI,CAAC,CAAC,GAAGmB,GAAG,CAAC,CAAC,CAAC,CAAC9C,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAACyB,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE;QACvF,MAAMmH,IAAI,GAAG;UACZrF,IAAI,EAAE,OAAO;UACbP,GAAG,EAAEF,GAAG,CAAC,CAAC,CAAC;UACX+F,MAAM,EAAE,EAAE;UACVC,KAAK,EAAE,EAAE;UACTH,IAAI,EAAE;QACP,CAAC;QACD,IAAIF,OAAO,CAAC5G,MAAM,KAAK6G,MAAM,CAAC7G,MAAM,EAAE;UACrC;UACA;QACD;QACA,KAAK,MAAMiH,KAAK,IAAIJ,MAAM,EAAE;UAC3B,IAAI,WAAW,CAAC3I,IAAI,CAAC+I,KAAK,CAAC,EAAE;YAC5BF,IAAI,CAACE,KAAK,CAAC9G,IAAI,CAAC,OAAO,CAAC;UACzB,CAAC,MACI,IAAI,YAAY,CAACjC,IAAI,CAAC+I,KAAK,CAAC,EAAE;YAClCF,IAAI,CAACE,KAAK,CAAC9G,IAAI,CAAC,QAAQ,CAAC;UAC1B,CAAC,MACI,IAAI,WAAW,CAACjC,IAAI,CAAC+I,KAAK,CAAC,EAAE;YACjCF,IAAI,CAACE,KAAK,CAAC9G,IAAI,CAAC,MAAM,CAAC;UACxB,CAAC,MACI;YACJ4G,IAAI,CAACE,KAAK,CAAC9G,IAAI,CAAC,IAAI,CAAC;UACtB;QACD;QACA,KAAK,IAAIN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+G,OAAO,CAAC5G,MAAM,EAAEH,CAAC,EAAE,EAAE;UACxCkH,IAAI,CAACC,MAAM,CAAC7G,IAAI,CAAC;YAChBmB,IAAI,EAAEsF,OAAO,CAAC/G,CAAC,CAAC;YAChB8B,MAAM,EAAE,IAAI,CAACP,KAAK,CAAC6B,MAAM,CAAC2D,OAAO,CAAC/G,CAAC,CAAC,CAAC;YACrCmH,MAAM,EAAE,IAAI;YACZC,KAAK,EAAEF,IAAI,CAACE,KAAK,CAACpH,CAAC;UACpB,CAAC,CAAC;QACH;QACA,KAAK,MAAMR,GAAG,IAAIyH,IAAI,EAAE;UACvBC,IAAI,CAACD,IAAI,CAAC3G,IAAI,CAACjB,UAAU,CAACG,GAAG,EAAE0H,IAAI,CAACC,MAAM,CAAChH,MAAM,CAAC,CAACgC,GAAG,CAAC,CAACkF,IAAI,EAAErH,CAAC,KAAK;YACnE,OAAO;cACNyB,IAAI,EAAE4F,IAAI;cACVvF,MAAM,EAAE,IAAI,CAACP,KAAK,CAAC6B,MAAM,CAACiE,IAAI,CAAC;cAC/BF,MAAM,EAAE,KAAK;cACbC,KAAK,EAAEF,IAAI,CAACE,KAAK,CAACpH,CAAC;YACpB,CAAC;UACF,CAAC,CAAC,CAAC;QACJ;QACA,OAAOkH,IAAI;MACZ;MACAI,QAAQA,CAACzE,GAAG,EAAE;QACb,MAAMzB,GAAG,GAAG,IAAI,CAACsB,KAAK,CAACI,KAAK,CAACwE,QAAQ,CAAClI,IAAI,CAACyD,GAAG,CAAC;QAC/C,IAAIzB,GAAG,EAAE;UACR,OAAO;YACNS,IAAI,EAAE,SAAS;YACfP,GAAG,EAAEF,GAAG,CAAC,CAAC,CAAC;YACXoC,KAAK,EAAEpC,GAAG,CAAC,CAAC,CAAC,CAACP,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC;YACvCY,IAAI,EAAEL,GAAG,CAAC,CAAC,CAAC;YACZU,MAAM,EAAE,IAAI,CAACP,KAAK,CAAC6B,MAAM,CAAChC,GAAG,CAAC,CAAC,CAAC;UACjC,CAAC;QACF;MACD;MACAmG,SAASA,CAAC1E,GAAG,EAAE;QACd,MAAMzB,GAAG,GAAG,IAAI,CAACsB,KAAK,CAACI,KAAK,CAACyE,SAAS,CAACnI,IAAI,CAACyD,GAAG,CAAC;QAChD,IAAIzB,GAAG,EAAE;UACR,MAAMK,IAAI,GAAGL,GAAG,CAAC,CAAC,CAAC,CAACP,MAAM,CAACO,GAAG,CAAC,CAAC,CAAC,CAACjB,MAAM,GAAG,CAAC,CAAC,KAAK,IAAI,GACnDiB,GAAG,CAAC,CAAC,CAAC,CAACN,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GACnBM,GAAG,CAAC,CAAC,CAAC;UACT,OAAO;YACNS,IAAI,EAAE,WAAW;YACjBP,GAAG,EAAEF,GAAG,CAAC,CAAC,CAAC;YACXK,IAAI;YACJK,MAAM,EAAE,IAAI,CAACP,KAAK,CAAC6B,MAAM,CAAC3B,IAAI;UAC/B,CAAC;QACF;MACD;MACAA,IAAIA,CAACoB,GAAG,EAAE;QACT,MAAMzB,GAAG,GAAG,IAAI,CAACsB,KAAK,CAACI,KAAK,CAACrB,IAAI,CAACrC,IAAI,CAACyD,GAAG,CAAC;QAC3C,IAAIzB,GAAG,EAAE;UACR,OAAO;YACNS,IAAI,EAAE,MAAM;YACZP,GAAG,EAAEF,GAAG,CAAC,CAAC,CAAC;YACXK,IAAI,EAAEL,GAAG,CAAC,CAAC,CAAC;YACZU,MAAM,EAAE,IAAI,CAACP,KAAK,CAAC6B,MAAM,CAAChC,GAAG,CAAC,CAAC,CAAC;UACjC,CAAC;QACF;MACD;MACAoG,MAAMA,CAAC3E,GAAG,EAAE;QACX,MAAMzB,GAAG,GAAG,IAAI,CAACsB,KAAK,CAACU,MAAM,CAACoE,MAAM,CAACpI,IAAI,CAACyD,GAAG,CAAC;QAC9C,IAAIzB,GAAG,EAAE;UACR,OAAO;YACNS,IAAI,EAAE,QAAQ;YACdP,GAAG,EAAEF,GAAG,CAAC,CAAC,CAAC;YACXK,IAAI,EAAEvD,QAAQ,CAACkD,GAAG,CAAC,CAAC,CAAC;UACtB,CAAC;QACF;MACD;MACAwF,GAAGA,CAAC/D,GAAG,EAAE;QACR,MAAMzB,GAAG,GAAG,IAAI,CAACsB,KAAK,CAACU,MAAM,CAACwD,GAAG,CAACxH,IAAI,CAACyD,GAAG,CAAC;QAC3C,IAAIzB,GAAG,EAAE;UACR,IAAI,CAAC,IAAI,CAACG,KAAK,CAACG,KAAK,CAACC,MAAM,IAAI,OAAO,CAACtD,IAAI,CAAC+C,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;YACrD,IAAI,CAACG,KAAK,CAACG,KAAK,CAACC,MAAM,GAAG,IAAI;UAC/B,CAAC,MACI,IAAI,IAAI,CAACJ,KAAK,CAACG,KAAK,CAACC,MAAM,IAAI,SAAS,CAACtD,IAAI,CAAC+C,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;YAC3D,IAAI,CAACG,KAAK,CAACG,KAAK,CAACC,MAAM,GAAG,KAAK;UAChC;UACA,IAAI,CAAC,IAAI,CAACJ,KAAK,CAACG,KAAK,CAAC+F,UAAU,IAAI,gCAAgC,CAACpJ,IAAI,CAAC+C,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;YAClF,IAAI,CAACG,KAAK,CAACG,KAAK,CAAC+F,UAAU,GAAG,IAAI;UACnC,CAAC,MACI,IAAI,IAAI,CAAClG,KAAK,CAACG,KAAK,CAAC+F,UAAU,IAAI,kCAAkC,CAACpJ,IAAI,CAAC+C,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;YACxF,IAAI,CAACG,KAAK,CAACG,KAAK,CAAC+F,UAAU,GAAG,KAAK;UACpC;UACA,OAAO;YACN5F,IAAI,EAAE,MAAM;YACZP,GAAG,EAAEF,GAAG,CAAC,CAAC,CAAC;YACXO,MAAM,EAAE,IAAI,CAACJ,KAAK,CAACG,KAAK,CAACC,MAAM;YAC/B8F,UAAU,EAAE,IAAI,CAAClG,KAAK,CAACG,KAAK,CAAC+F,UAAU;YACvC3E,KAAK,EAAE,KAAK;YACZrB,IAAI,EAAEL,GAAG,CAAC,CAAC;UACZ,CAAC;QACF;MACD;MACAC,IAAIA,CAACwB,GAAG,EAAE;QACT,MAAMzB,GAAG,GAAG,IAAI,CAACsB,KAAK,CAACU,MAAM,CAAC/B,IAAI,CAACjC,IAAI,CAACyD,GAAG,CAAC;QAC5C,IAAIzB,GAAG,EAAE;UACR,MAAMsG,UAAU,GAAGtG,GAAG,CAAC,CAAC,CAAC,CAACnB,IAAI,CAAC,CAAC;UAChC,IAAI,CAAC,IAAI,CAACwC,OAAO,CAACxF,QAAQ,IAAI,IAAI,CAACoB,IAAI,CAACqJ,UAAU,CAAC,EAAE;YACpD;YACA,IAAI,CAAE,IAAI,CAACrJ,IAAI,CAACqJ,UAAU,CAAE,EAAE;cAC7B;YACD;YACA;YACA,MAAMC,UAAU,GAAGpH,KAAK,CAACmH,UAAU,CAAC5G,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC;YACvD,IAAI,CAAC4G,UAAU,CAACvH,MAAM,GAAGwH,UAAU,CAACxH,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;cACtD;YACD;UACD,CAAC,MACI;YACJ;YACA,MAAMyH,cAAc,GAAG7G,kBAAkB,CAACK,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC;YACvD,IAAIwG,cAAc,GAAG,CAAC,CAAC,EAAE;cACxB,MAAMjD,KAAK,GAAGvD,GAAG,CAAC,CAAC,CAAC,CAACH,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC;cAC/C,MAAM4G,OAAO,GAAGlD,KAAK,GAAGvD,GAAG,CAAC,CAAC,CAAC,CAACjB,MAAM,GAAGyH,cAAc;cACtDxG,GAAG,CAAC,CAAC,CAAC,GAAGA,GAAG,CAAC,CAAC,CAAC,CAACkD,SAAS,CAAC,CAAC,EAAEsD,cAAc,CAAC;cAC5CxG,GAAG,CAAC,CAAC,CAAC,GAAGA,GAAG,CAAC,CAAC,CAAC,CAACkD,SAAS,CAAC,CAAC,EAAEuD,OAAO,CAAC,CAAC5H,IAAI,CAAC,CAAC;cAC5CmB,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE;YACZ;UACD;UACA,IAAInC,IAAI,GAAGmC,GAAG,CAAC,CAAC,CAAC;UACjB,IAAII,KAAK,GAAG,EAAE;UACd,IAAI,IAAI,CAACiB,OAAO,CAACxF,QAAQ,EAAE;YAC1B;YACA,MAAMoE,IAAI,GAAG,+BAA+B,CAACjC,IAAI,CAACH,IAAI,CAAC;YACvD,IAAIoC,IAAI,EAAE;cACTpC,IAAI,GAAGoC,IAAI,CAAC,CAAC,CAAC;cACdG,KAAK,GAAGH,IAAI,CAAC,CAAC,CAAC;YAChB;UACD,CAAC,MACI;YACJG,KAAK,GAAGJ,GAAG,CAAC,CAAC,CAAC,GAAGA,GAAG,CAAC,CAAC,CAAC,CAACN,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE;UAC1C;UACA7B,IAAI,GAAGA,IAAI,CAACgB,IAAI,CAAC,CAAC;UAClB,IAAI,IAAI,CAAC5B,IAAI,CAACY,IAAI,CAAC,EAAE;YACpB,IAAI,IAAI,CAACwD,OAAO,CAACxF,QAAQ,IAAI,CAAE,IAAI,CAACoB,IAAI,CAACqJ,UAAU,CAAE,EAAE;cACtD;cACAzI,IAAI,GAAGA,IAAI,CAAC6B,KAAK,CAAC,CAAC,CAAC;YACrB,CAAC,MACI;cACJ7B,IAAI,GAAGA,IAAI,CAAC6B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACzB;UACD;UACA,OAAOK,UAAU,CAACC,GAAG,EAAE;YACtBnC,IAAI,EAAEA,IAAI,GAAGA,IAAI,CAACX,OAAO,CAAC,IAAI,CAACoE,KAAK,CAACU,MAAM,CAACC,cAAc,EAAE,IAAI,CAAC,GAAGpE,IAAI;YACxEuC,KAAK,EAAEA,KAAK,GAAGA,KAAK,CAAClD,OAAO,CAAC,IAAI,CAACoE,KAAK,CAACU,MAAM,CAACC,cAAc,EAAE,IAAI,CAAC,GAAG7B;UACxE,CAAC,EAAEJ,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,CAACG,KAAK,CAAC;QACvB;MACD;MACAuG,OAAOA,CAACjF,GAAG,EAAEkF,KAAK,EAAE;QACnB,IAAI3G,GAAG;QACP,IAAI,CAACA,GAAG,GAAG,IAAI,CAACsB,KAAK,CAACU,MAAM,CAAC0E,OAAO,CAAC1I,IAAI,CAACyD,GAAG,CAAC,MACzCzB,GAAG,GAAG,IAAI,CAACsB,KAAK,CAACU,MAAM,CAAC4E,MAAM,CAAC5I,IAAI,CAACyD,GAAG,CAAC,CAAC,EAAE;UAC/C,MAAMoF,UAAU,GAAG,CAAC7G,GAAG,CAAC,CAAC,CAAC,IAAIA,GAAG,CAAC,CAAC,CAAC,EAAE9C,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;UAC1D,MAAM+C,IAAI,GAAG0G,KAAK,CAACE,UAAU,CAACpB,WAAW,CAAC,CAAC,CAAC;UAC5C,IAAI,CAACxF,IAAI,EAAE;YACV,MAAMI,IAAI,GAAGL,GAAG,CAAC,CAAC,CAAC,CAACP,MAAM,CAAC,CAAC,CAAC;YAC7B,OAAO;cACNgB,IAAI,EAAE,MAAM;cACZP,GAAG,EAAEG,IAAI;cACTA;YACD,CAAC;UACF;UACA,OAAON,UAAU,CAACC,GAAG,EAAEC,IAAI,EAAED,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,CAACG,KAAK,CAAC;QACjD;MACD;MACA2G,QAAQA,CAACrF,GAAG,EAAEsF,SAAS,EAAEC,QAAQ,GAAG,EAAE,EAAE;QACvC,IAAI3I,KAAK,GAAG,IAAI,CAACiD,KAAK,CAACU,MAAM,CAACiF,cAAc,CAACjJ,IAAI,CAACyD,GAAG,CAAC;QACtD,IAAI,CAACpD,KAAK,EACT;QACD;QACA,IAAIA,KAAK,CAAC,CAAC,CAAC,IAAI2I,QAAQ,CAAC3I,KAAK,CAAC,eAAe,CAAC,EAC9C;QACD,MAAM6I,QAAQ,GAAG7I,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE;QAC3C,IAAI,CAAC6I,QAAQ,IAAI,CAACF,QAAQ,IAAI,IAAI,CAAC1F,KAAK,CAACU,MAAM,CAACmF,WAAW,CAACnJ,IAAI,CAACgJ,QAAQ,CAAC,EAAE;UAC3E;UACA,MAAMI,OAAO,GAAG,CAAC,GAAG/I,KAAK,CAAC,CAAC,CAAC,CAAC,CAACU,MAAM,GAAG,CAAC;UACxC,IAAIsI,MAAM;YAAEC,OAAO;YAAEC,UAAU,GAAGH,OAAO;YAAEI,aAAa,GAAG,CAAC;UAC5D,MAAMC,MAAM,GAAGpJ,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,GAAG,IAAI,CAACiD,KAAK,CAACU,MAAM,CAAC0F,iBAAiB,GAAG,IAAI,CAACpG,KAAK,CAACU,MAAM,CAAC2F,iBAAiB;UAC9GF,MAAM,CAACG,SAAS,GAAG,CAAC;UACpB;UACAb,SAAS,GAAGA,SAAS,CAACrH,KAAK,CAAC,CAAC,CAAC,GAAG+B,GAAG,CAAC1C,MAAM,GAAGqI,OAAO,CAAC;UACtD,OAAO,CAAC/I,KAAK,GAAGoJ,MAAM,CAACzJ,IAAI,CAAC+I,SAAS,CAAC,KAAK,IAAI,EAAE;YAChDM,MAAM,GAAGhJ,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC;YAC7E,IAAI,CAACgJ,MAAM,EACV,SAAS,CAAC;YACXC,OAAO,GAAG,CAAC,GAAGD,MAAM,CAAC,CAACtI,MAAM;YAC5B,IAAIV,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,EAAE;cAAE;cAC3BkJ,UAAU,IAAID,OAAO;cACrB;YACD,CAAC,MACI,IAAIjJ,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,EAAE;cAAE;cAChC,IAAI+I,OAAO,GAAG,CAAC,IAAI,EAAE,CAACA,OAAO,GAAGE,OAAO,IAAI,CAAC,CAAC,EAAE;gBAC9CE,aAAa,IAAIF,OAAO;gBACxB,SAAS,CAAC;cACX;YACD;YACAC,UAAU,IAAID,OAAO;YACrB,IAAIC,UAAU,GAAG,CAAC,EACjB,SAAS,CAAC;YACX;YACAD,OAAO,GAAG/C,IAAI,CAACC,GAAG,CAAC8C,OAAO,EAAEA,OAAO,GAAGC,UAAU,GAAGC,aAAa,CAAC;YACjE;YACA,MAAMK,cAAc,GAAG,CAAC,GAAGxJ,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACU,MAAM;YAC9C,MAAMmB,GAAG,GAAGuB,GAAG,CAAC/B,KAAK,CAAC,CAAC,EAAE0H,OAAO,GAAG/I,KAAK,CAACyJ,KAAK,GAAGD,cAAc,GAAGP,OAAO,CAAC;YAC1E;YACA,IAAI/C,IAAI,CAACC,GAAG,CAAC4C,OAAO,EAAEE,OAAO,CAAC,GAAG,CAAC,EAAE;cACnC,MAAMjH,IAAI,GAAGH,GAAG,CAACR,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;cAC7B,OAAO;gBACNe,IAAI,EAAE,IAAI;gBACVP,GAAG;gBACHG,IAAI;gBACJK,MAAM,EAAE,IAAI,CAACP,KAAK,CAACQ,YAAY,CAACN,IAAI;cACrC,CAAC;YACF;YACA;YACA,MAAMA,IAAI,GAAGH,GAAG,CAACR,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC7B,OAAO;cACNe,IAAI,EAAE,QAAQ;cACdP,GAAG;cACHG,IAAI;cACJK,MAAM,EAAE,IAAI,CAACP,KAAK,CAACQ,YAAY,CAACN,IAAI;YACrC,CAAC;UACF;QACD;MACD;MACA0H,QAAQA,CAACtG,GAAG,EAAE;QACb,MAAMzB,GAAG,GAAG,IAAI,CAACsB,KAAK,CAACU,MAAM,CAACJ,IAAI,CAAC5D,IAAI,CAACyD,GAAG,CAAC;QAC5C,IAAIzB,GAAG,EAAE;UACR,IAAIK,IAAI,GAAGL,GAAG,CAAC,CAAC,CAAC,CAAC9C,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;UACrC,MAAM8K,gBAAgB,GAAG,MAAM,CAAC/K,IAAI,CAACoD,IAAI,CAAC;UAC1C,MAAM4H,uBAAuB,GAAG,IAAI,CAAChL,IAAI,CAACoD,IAAI,CAAC,IAAI,IAAI,CAACpD,IAAI,CAACoD,IAAI,CAAC;UAClE,IAAI2H,gBAAgB,IAAIC,uBAAuB,EAAE;YAChD5H,IAAI,GAAGA,IAAI,CAAC6C,SAAS,CAAC,CAAC,EAAE7C,IAAI,CAACtB,MAAM,GAAG,CAAC,CAAC;UAC1C;UACAsB,IAAI,GAAGvD,QAAQ,CAACuD,IAAI,EAAE,IAAI,CAAC;UAC3B,OAAO;YACNI,IAAI,EAAE,UAAU;YAChBP,GAAG,EAAEF,GAAG,CAAC,CAAC,CAAC;YACXK;UACD,CAAC;QACF;MACD;MACA6H,EAAEA,CAACzG,GAAG,EAAE;QACP,MAAMzB,GAAG,GAAG,IAAI,CAACsB,KAAK,CAACU,MAAM,CAACkG,EAAE,CAAClK,IAAI,CAACyD,GAAG,CAAC;QAC1C,IAAIzB,GAAG,EAAE;UACR,OAAO;YACNS,IAAI,EAAE,IAAI;YACVP,GAAG,EAAEF,GAAG,CAAC,CAAC;UACX,CAAC;QACF;MACD;MACAmI,GAAGA,CAAC1G,GAAG,EAAE;QACR,MAAMzB,GAAG,GAAG,IAAI,CAACsB,KAAK,CAACU,MAAM,CAACmG,GAAG,CAACnK,IAAI,CAACyD,GAAG,CAAC;QAC3C,IAAIzB,GAAG,EAAE;UACR,OAAO;YACNS,IAAI,EAAE,KAAK;YACXP,GAAG,EAAEF,GAAG,CAAC,CAAC,CAAC;YACXK,IAAI,EAAEL,GAAG,CAAC,CAAC,CAAC;YACZU,MAAM,EAAE,IAAI,CAACP,KAAK,CAACQ,YAAY,CAACX,GAAG,CAAC,CAAC,CAAC;UACvC,CAAC;QACF;MACD;MACAoI,QAAQA,CAAC3G,GAAG,EAAE;QACb,MAAMzB,GAAG,GAAG,IAAI,CAACsB,KAAK,CAACU,MAAM,CAACoG,QAAQ,CAACpK,IAAI,CAACyD,GAAG,CAAC;QAChD,IAAIzB,GAAG,EAAE;UACR,IAAIK,IAAI,EAAExC,IAAI;UACd,IAAImC,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;YACnBK,IAAI,GAAGvD,QAAQ,CAACkD,GAAG,CAAC,CAAC,CAAC,CAAC;YACvBnC,IAAI,GAAG,SAAS,GAAGwC,IAAI;UACxB,CAAC,MACI;YACJA,IAAI,GAAGvD,QAAQ,CAACkD,GAAG,CAAC,CAAC,CAAC,CAAC;YACvBnC,IAAI,GAAGwC,IAAI;UACZ;UACA,OAAO;YACNI,IAAI,EAAE,MAAM;YACZP,GAAG,EAAEF,GAAG,CAAC,CAAC,CAAC;YACXK,IAAI;YACJxC,IAAI;YACJ6C,MAAM,EAAE,CACP;cACCD,IAAI,EAAE,MAAM;cACZP,GAAG,EAAEG,IAAI;cACTA;YACD,CAAC;UAEH,CAAC;QACF;MACD;MACAgI,GAAGA,CAAC5G,GAAG,EAAE;QACR,IAAIzB,GAAG;QACP,IAAIA,GAAG,GAAG,IAAI,CAACsB,KAAK,CAACU,MAAM,CAACqG,GAAG,CAACrK,IAAI,CAACyD,GAAG,CAAC,EAAE;UAC1C,IAAIpB,IAAI,EAAExC,IAAI;UACd,IAAImC,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;YACnBK,IAAI,GAAGvD,QAAQ,CAACkD,GAAG,CAAC,CAAC,CAAC,CAAC;YACvBnC,IAAI,GAAG,SAAS,GAAGwC,IAAI;UACxB,CAAC,MACI;YACJ;YACA,IAAIiI,WAAW;YACf,GAAG;cACFA,WAAW,GAAGtI,GAAG,CAAC,CAAC,CAAC;cACpBA,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAACsB,KAAK,CAACU,MAAM,CAACuG,UAAU,CAACvK,IAAI,CAACgC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE;YAC9D,CAAC,QAAQsI,WAAW,KAAKtI,GAAG,CAAC,CAAC,CAAC;YAC/BK,IAAI,GAAGvD,QAAQ,CAACkD,GAAG,CAAC,CAAC,CAAC,CAAC;YACvB,IAAIA,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,EAAE;cACtBnC,IAAI,GAAG,SAAS,GAAGmC,GAAG,CAAC,CAAC,CAAC;YAC1B,CAAC,MACI;cACJnC,IAAI,GAAGmC,GAAG,CAAC,CAAC,CAAC;YACd;UACD;UACA,OAAO;YACNS,IAAI,EAAE,MAAM;YACZP,GAAG,EAAEF,GAAG,CAAC,CAAC,CAAC;YACXK,IAAI;YACJxC,IAAI;YACJ6C,MAAM,EAAE,CACP;cACCD,IAAI,EAAE,MAAM;cACZP,GAAG,EAAEG,IAAI;cACTA;YACD,CAAC;UAEH,CAAC;QACF;MACD;MACAmI,UAAUA,CAAC/G,GAAG,EAAE;QACf,MAAMzB,GAAG,GAAG,IAAI,CAACsB,KAAK,CAACU,MAAM,CAAC3B,IAAI,CAACrC,IAAI,CAACyD,GAAG,CAAC;QAC5C,IAAIzB,GAAG,EAAE;UACR,IAAIK,IAAI;UACR,IAAI,IAAI,CAACF,KAAK,CAACG,KAAK,CAAC+F,UAAU,EAAE;YAChChG,IAAI,GAAGL,GAAG,CAAC,CAAC,CAAC;UACd,CAAC,MACI;YACJK,IAAI,GAAGvD,QAAQ,CAACkD,GAAG,CAAC,CAAC,CAAC,CAAC;UACxB;UACA,OAAO;YACNS,IAAI,EAAE,MAAM;YACZP,GAAG,EAAEF,GAAG,CAAC,CAAC,CAAC;YACXK;UACD,CAAC;QACF;MACD;IACD;;IAEA;AACD;AACA;IACC,MAAMsB,OAAO,GAAG,kBAAkB;IAClC,MAAM8G,SAAS,GAAG,sCAAsC;IACxD,MAAM3G,MAAM,GAAG,6GAA6G;IAC5H,MAAMO,EAAE,GAAG,oEAAoE;IAC/E,MAAMH,OAAO,GAAG,sCAAsC;IACtD,MAAMwG,MAAM,GAAG,uBAAuB;IACtC,MAAMxC,QAAQ,GAAG9I,IAAI,CAAC,oJAAoJ,CAAC,CACzKF,OAAO,CAAC,OAAO,EAAEwL,MAAM,CAAC,CAAC;IAAA,CACzBxL,OAAO,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;IAAA,CAC9BA,OAAO,CAAC,SAAS,EAAE,uBAAuB,CAAC,CAAC;IAAA,CAC5CA,OAAO,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;IAAA,CAClCA,OAAO,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;IAAA,CACpCA,OAAO,CAAC,OAAO,EAAE,mBAAmB,CAAC,CAAC;IAAA,CACtCS,QAAQ,CAAC,CAAC;IACZ,MAAMgL,UAAU,GAAG,sFAAsF;IACzG,MAAMC,SAAS,GAAG,SAAS;IAC3B,MAAMC,WAAW,GAAG,6BAA6B;IACjD,MAAMtD,GAAG,GAAGnI,IAAI,CAAC,iGAAiG,CAAC,CACjHF,OAAO,CAAC,OAAO,EAAE2L,WAAW,CAAC,CAC7B3L,OAAO,CAAC,OAAO,EAAE,8DAA8D,CAAC,CAChFS,QAAQ,CAAC,CAAC;IACZ,MAAMwF,IAAI,GAAG/F,IAAI,CAAC,sCAAsC,CAAC,CACvDF,OAAO,CAAC,OAAO,EAAEwL,MAAM,CAAC,CACxB/K,QAAQ,CAAC,CAAC;IACZ,MAAMmL,IAAI,GAAG,6DAA6D,GACvE,0EAA0E,GAC1E,sEAAsE,GACtE,yEAAyE,GACzE,qEAAqE,GACrE,cAAc;IACjB,MAAMC,QAAQ,GAAG,+BAA+B;IAChD,MAAMhM,IAAI,GAAGK,IAAI,CAAC,YAAY,CAAC;IAAA,EAC5B,qEAAqE,CAAC;IAAA,EACtE,yBAAyB,CAAC;IAAA,EAC1B,+BAA+B,CAAC;IAAA,EAChC,+BAA+B,CAAC;IAAA,EAChC,2CAA2C,CAAC;IAAA,EAC5C,sDAAsD,CAAC;IAAA,EACvD,oHAAoH,CAAC;IAAA,EACrH,oGAAoG,CAAC;IAAA,EACrG,GAAG,EAAE,GAAG,CAAC,CACVF,OAAO,CAAC,SAAS,EAAE6L,QAAQ,CAAC,CAC5B7L,OAAO,CAAC,KAAK,EAAE4L,IAAI,CAAC,CACpB5L,OAAO,CAAC,WAAW,EAAE,0EAA0E,CAAC,CAChGS,QAAQ,CAAC,CAAC;IACZ,MAAMwI,SAAS,GAAG/I,IAAI,CAACuL,UAAU,CAAC,CAChCzL,OAAO,CAAC,IAAI,EAAEmF,EAAE,CAAC,CACjBnF,OAAO,CAAC,SAAS,EAAE,uBAAuB,CAAC,CAC3CA,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;IAAA,CACzBA,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CACrBA,OAAO,CAAC,YAAY,EAAE,SAAS,CAAC,CAChCA,OAAO,CAAC,QAAQ,EAAE,gDAAgD,CAAC,CACnEA,OAAO,CAAC,MAAM,EAAE,wBAAwB,CAAC,CAAC;IAAA,CAC1CA,OAAO,CAAC,MAAM,EAAE,6DAA6D,CAAC,CAC9EA,OAAO,CAAC,KAAK,EAAE4L,IAAI,CAAC,CAAC;IAAA,CACrBnL,QAAQ,CAAC,CAAC;IACZ,MAAM2E,UAAU,GAAGlF,IAAI,CAAC,yCAAyC,CAAC,CAChEF,OAAO,CAAC,WAAW,EAAEiJ,SAAS,CAAC,CAC/BxI,QAAQ,CAAC,CAAC;IACZ;AACD;AACA;IACC,MAAMqL,WAAW,GAAG;MACnB1G,UAAU;MACVV,IAAI,EAAE6G,SAAS;MACflD,GAAG;MACHzD,MAAM;MACNI,OAAO;MACPG,EAAE;MACFtF,IAAI;MACJmJ,QAAQ;MACR/C,IAAI;MACJxB,OAAO;MACPwE,SAAS;MACTT,KAAK,EAAE3H,QAAQ;MACfsC,IAAI,EAAEuI;IACP,CAAC;IACD;AACD;AACA;IACC,MAAMK,QAAQ,GAAG7L,IAAI,CAAC,mBAAmB,CAAC;IAAA,EACvC,wDAAwD,CAAC;IAAA,EACzD,sFAAsF,CAAC,CAAC;IAAA,CACzFF,OAAO,CAAC,IAAI,EAAEmF,EAAE,CAAC,CACjBnF,OAAO,CAAC,SAAS,EAAE,uBAAuB,CAAC,CAC3CA,OAAO,CAAC,YAAY,EAAE,SAAS,CAAC,CAChCA,OAAO,CAAC,MAAM,EAAE,YAAY,CAAC,CAC7BA,OAAO,CAAC,QAAQ,EAAE,gDAAgD,CAAC,CACnEA,OAAO,CAAC,MAAM,EAAE,wBAAwB,CAAC,CAAC;IAAA,CAC1CA,OAAO,CAAC,MAAM,EAAE,6DAA6D,CAAC,CAC9EA,OAAO,CAAC,KAAK,EAAE4L,IAAI,CAAC,CAAC;IAAA,CACrBnL,QAAQ,CAAC,CAAC;IACZ,MAAMuL,QAAQ,GAAG;MAChB,GAAGF,WAAW;MACdtD,KAAK,EAAEuD,QAAQ;MACf9C,SAAS,EAAE/I,IAAI,CAACuL,UAAU,CAAC,CACzBzL,OAAO,CAAC,IAAI,EAAEmF,EAAE,CAAC,CACjBnF,OAAO,CAAC,SAAS,EAAE,uBAAuB,CAAC,CAC3CA,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;MAAA,CACzBA,OAAO,CAAC,OAAO,EAAE+L,QAAQ,CAAC,CAAC;MAAA,CAC3B/L,OAAO,CAAC,YAAY,EAAE,SAAS,CAAC,CAChCA,OAAO,CAAC,QAAQ,EAAE,gDAAgD,CAAC,CACnEA,OAAO,CAAC,MAAM,EAAE,wBAAwB,CAAC,CAAC;MAAA,CAC1CA,OAAO,CAAC,MAAM,EAAE,6DAA6D,CAAC,CAC9EA,OAAO,CAAC,KAAK,EAAE4L,IAAI,CAAC,CAAC;MAAA,CACrBnL,QAAQ,CAAC;IACZ,CAAC;IACD;AACD;AACA;IACC,MAAMwL,aAAa,GAAG;MACrB,GAAGH,WAAW;MACdjM,IAAI,EAAEK,IAAI,CAAC,8BAA8B,GACtC,4CAA4C,CAAC;MAAA,EAC7C,sEAAsE,CAAC,CACxEF,OAAO,CAAC,SAAS,EAAE6L,QAAQ,CAAC,CAC5B7L,OAAO,CAAC,MAAM,EAAE,QAAQ,GACtB,qEAAqE,GACrE,6DAA6D,GAC7D,+BAA+B,CAAC,CAClCS,QAAQ,CAAC,CAAC;MACZ4H,GAAG,EAAE,mEAAmE;MACxErD,OAAO,EAAE,wBAAwB;MACjCJ,MAAM,EAAE/D,QAAQ;MAAE;MAClBmI,QAAQ,EAAE,kCAAkC;MAC5CC,SAAS,EAAE/I,IAAI,CAACuL,UAAU,CAAC,CACzBzL,OAAO,CAAC,IAAI,EAAEmF,EAAE,CAAC,CACjBnF,OAAO,CAAC,SAAS,EAAE,iBAAiB,CAAC,CACrCA,OAAO,CAAC,UAAU,EAAEgJ,QAAQ,CAAC,CAC7BhJ,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CACrBA,OAAO,CAAC,YAAY,EAAE,SAAS,CAAC,CAChCA,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CACtBA,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CACpBA,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CACpBA,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CACnBS,QAAQ,CAAC;IACZ,CAAC;IACD;AACD;AACA;IACC,MAAMyI,MAAM,GAAG,6CAA6C;IAC5D,MAAMgD,UAAU,GAAG,qCAAqC;IACxD,MAAMlB,EAAE,GAAG,uBAAuB;IAClC,MAAMM,UAAU,GAAG,6EAA6E;IAChG;IACA,MAAMa,YAAY,GAAG,cAAc;IACnC,MAAMlC,WAAW,GAAG/J,IAAI,CAAC,4BAA4B,EAAE,GAAG,CAAC,CACzDF,OAAO,CAAC,cAAc,EAAEmM,YAAY,CAAC,CAAC1L,QAAQ,CAAC,CAAC;IAClD;IACA,MAAM2L,SAAS,GAAG,+CAA+C;IACjE,MAAMrC,cAAc,GAAG7J,IAAI,CAAC,mEAAmE,EAAE,GAAG,CAAC,CACnGF,OAAO,CAAC,QAAQ,EAAEmM,YAAY,CAAC,CAC/B1L,QAAQ,CAAC,CAAC;IACZ,MAAM+J,iBAAiB,GAAGtK,IAAI,CAAC,mCAAmC,CAAC;IAAA,EAChE,gBAAgB,CAAC;IAAA,EACjB,kCAAkC,CAAC;IAAA,EACnC,2CAA2C,CAAC;IAAA,EAC5C,yCAAyC,CAAC;IAAA,EAC1C,gCAAgC,CAAC;IAAA,EACjC,yCAAyC,CAAC;IAAA,EAC1C,mCAAmC,EAAE,IAAI,CAAC,CAAC;IAAA,CAC5CF,OAAO,CAAC,QAAQ,EAAEmM,YAAY,CAAC,CAC/B1L,QAAQ,CAAC,CAAC;IACZ;IACA,MAAMgK,iBAAiB,GAAGvK,IAAI,CAAC,yCAAyC,CAAC;IAAA,EACtE,gBAAgB,CAAC;IAAA,EACjB,8BAA8B,CAAC;IAAA,EAC/B,uCAAuC,CAAC;IAAA,EACxC,qCAAqC,CAAC;IAAA,EACtC,4BAA4B,CAAC;IAAA,EAC7B,mCAAmC,EAAE,IAAI,CAAC,CAAC;IAAA,CAC5CF,OAAO,CAAC,QAAQ,EAAEmM,YAAY,CAAC,CAC/B1L,QAAQ,CAAC,CAAC;IACZ,MAAMsE,cAAc,GAAG7E,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,CAC9CF,OAAO,CAAC,QAAQ,EAAEmM,YAAY,CAAC,CAC/B1L,QAAQ,CAAC,CAAC;IACZ,MAAMyK,QAAQ,GAAGhL,IAAI,CAAC,qCAAqC,CAAC,CAC1DF,OAAO,CAAC,QAAQ,EAAE,8BAA8B,CAAC,CACjDA,OAAO,CAAC,OAAO,EAAE,8IAA8I,CAAC,CAChKS,QAAQ,CAAC,CAAC;IACZ,MAAM4L,cAAc,GAAGnM,IAAI,CAAC2L,QAAQ,CAAC,CAAC7L,OAAO,CAAC,WAAW,EAAE,KAAK,CAAC,CAACS,QAAQ,CAAC,CAAC;IAC5E,MAAM6H,GAAG,GAAGpI,IAAI,CAAC,UAAU,GACxB,2BAA2B,CAAC;IAAA,EAC5B,0CAA0C,CAAC;IAAA,EAC3C,sBAAsB,CAAC;IAAA,EACvB,6BAA6B,CAAC;IAAA,EAC9B,kCAAkC,CAAC,CAAC;IAAA,CACrCF,OAAO,CAAC,SAAS,EAAEqM,cAAc,CAAC,CAClCrM,OAAO,CAAC,WAAW,EAAE,6EAA6E,CAAC,CACnGS,QAAQ,CAAC,CAAC;IACZ,MAAM6L,YAAY,GAAG,qDAAqD;IAC1E,MAAMvJ,IAAI,GAAG7C,IAAI,CAAC,+CAA+C,CAAC,CAChEF,OAAO,CAAC,OAAO,EAAEsM,YAAY,CAAC,CAC9BtM,OAAO,CAAC,MAAM,EAAE,sCAAsC,CAAC,CACvDA,OAAO,CAAC,OAAO,EAAE,6DAA6D,CAAC,CAC/ES,QAAQ,CAAC,CAAC;IACZ,MAAM+I,OAAO,GAAGtJ,IAAI,CAAC,yBAAyB,CAAC,CAC7CF,OAAO,CAAC,OAAO,EAAEsM,YAAY,CAAC,CAC9BtM,OAAO,CAAC,KAAK,EAAE2L,WAAW,CAAC,CAC3BlL,QAAQ,CAAC,CAAC;IACZ,MAAMiJ,MAAM,GAAGxJ,IAAI,CAAC,uBAAuB,CAAC,CAC1CF,OAAO,CAAC,KAAK,EAAE2L,WAAW,CAAC,CAC3BlL,QAAQ,CAAC,CAAC;IACZ,MAAM8L,aAAa,GAAGrM,IAAI,CAAC,uBAAuB,EAAE,GAAG,CAAC,CACtDF,OAAO,CAAC,SAAS,EAAEwJ,OAAO,CAAC,CAC3BxJ,OAAO,CAAC,QAAQ,EAAE0J,MAAM,CAAC,CACzBjJ,QAAQ,CAAC,CAAC;IACZ;AACD;AACA;IACC,MAAM+L,YAAY,GAAG;MACpBnB,UAAU,EAAExK,QAAQ;MAAE;MACtBkE,cAAc;MACdmG,QAAQ;MACRkB,SAAS;MACTpB,EAAE;MACFtG,IAAI,EAAEwH,UAAU;MAChBjB,GAAG,EAAEpK,QAAQ;MACbkJ,cAAc;MACdS,iBAAiB;MACjBC,iBAAiB;MACjBvB,MAAM;MACNnG,IAAI;MACJ2G,MAAM;MACNO,WAAW;MACXT,OAAO;MACP+C,aAAa;MACbjE,GAAG;MACHnF,IAAI,EAAEmI,UAAU;MAChBH,GAAG,EAAEtK;IACN,CAAC;IACD;AACD;AACA;IACC,MAAM4L,cAAc,GAAG;MACtB,GAAGD,YAAY;MACfzJ,IAAI,EAAE7C,IAAI,CAAC,yBAAyB,CAAC,CACnCF,OAAO,CAAC,OAAO,EAAEsM,YAAY,CAAC,CAC9B7L,QAAQ,CAAC,CAAC;MACZ+I,OAAO,EAAEtJ,IAAI,CAAC,+BAA+B,CAAC,CAC5CF,OAAO,CAAC,OAAO,EAAEsM,YAAY,CAAC,CAC9B7L,QAAQ,CAAC;IACZ,CAAC;IACD;AACD;AACA;IACC,MAAMiM,SAAS,GAAG;MACjB,GAAGF,YAAY;MACftD,MAAM,EAAEhJ,IAAI,CAACgJ,MAAM,CAAC,CAAClJ,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CAACS,QAAQ,CAAC,CAAC;MACrD0K,GAAG,EAAEjL,IAAI,CAAC,kEAAkE,EAAE,GAAG,CAAC,CAChFF,OAAO,CAAC,OAAO,EAAE,2EAA2E,CAAC,CAC7FS,QAAQ,CAAC,CAAC;MACZ4K,UAAU,EAAE,4EAA4E;MACxFJ,GAAG,EAAE,8CAA8C;MACnD9H,IAAI,EAAE;IACP,CAAC;IACD;AACD;AACA;IACC,MAAMwJ,YAAY,GAAG;MACpB,GAAGD,SAAS;MACZ1B,EAAE,EAAE9K,IAAI,CAAC8K,EAAE,CAAC,CAAChL,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAACS,QAAQ,CAAC,CAAC;MAC5C0C,IAAI,EAAEjD,IAAI,CAACwM,SAAS,CAACvJ,IAAI,CAAC,CACxBnD,OAAO,CAAC,MAAM,EAAE,eAAe,CAAC,CAChCA,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,CACvBS,QAAQ,CAAC;IACZ,CAAC;IACD;AACD;AACA;IACC,MAAM+D,KAAK,GAAG;MACboI,MAAM,EAAEd,WAAW;MACnBrN,GAAG,EAAEuN,QAAQ;MACbrN,QAAQ,EAAEsN;IACX,CAAC;IACD,MAAMnH,MAAM,GAAG;MACd8H,MAAM,EAAEJ,YAAY;MACpB/N,GAAG,EAAEiO,SAAS;MACdnO,MAAM,EAAEoO,YAAY;MACpBhO,QAAQ,EAAE8N;IACX,CAAC;;IAED;AACD;AACA;IACC,MAAMI,MAAM,CAAC;MACZrJ,MAAM;MACNW,OAAO;MACPf,KAAK;MACLtE,SAAS;MACTgO,WAAW;MACXzI,WAAWA,CAACF,OAAO,EAAE;QACpB;QACA,IAAI,CAACX,MAAM,GAAG,EAAE;QAChB,IAAI,CAACA,MAAM,CAACiG,KAAK,GAAGsD,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;QACvC,IAAI,CAAC7I,OAAO,GAAGA,OAAO,IAAInG,OAAO,CAACgB,QAAQ;QAC1C,IAAI,CAACmF,OAAO,CAACrF,SAAS,GAAG,IAAI,CAACqF,OAAO,CAACrF,SAAS,IAAI,IAAIoF,UAAU,CAAC,CAAC;QACnE,IAAI,CAACpF,SAAS,GAAG,IAAI,CAACqF,OAAO,CAACrF,SAAS;QACvC,IAAI,CAACA,SAAS,CAACqF,OAAO,GAAG,IAAI,CAACA,OAAO;QACrC,IAAI,CAACrF,SAAS,CAACmE,KAAK,GAAG,IAAI;QAC3B,IAAI,CAAC6J,WAAW,GAAG,EAAE;QACrB,IAAI,CAAC1J,KAAK,GAAG;UACZC,MAAM,EAAE,KAAK;UACb8F,UAAU,EAAE,KAAK;UACjBzD,GAAG,EAAE;QACN,CAAC;QACD,MAAMtB,KAAK,GAAG;UACbI,KAAK,EAAEA,KAAK,CAACoI,MAAM;UACnB9H,MAAM,EAAEA,MAAM,CAAC8H;QAChB,CAAC;QACD,IAAI,IAAI,CAACzI,OAAO,CAACxF,QAAQ,EAAE;UAC1ByF,KAAK,CAACI,KAAK,GAAGA,KAAK,CAAC7F,QAAQ;UAC5ByF,KAAK,CAACU,MAAM,GAAGA,MAAM,CAACnG,QAAQ;QAC/B,CAAC,MACI,IAAI,IAAI,CAACwF,OAAO,CAAC1F,GAAG,EAAE;UAC1B2F,KAAK,CAACI,KAAK,GAAGA,KAAK,CAAC/F,GAAG;UACvB,IAAI,IAAI,CAAC0F,OAAO,CAAC5F,MAAM,EAAE;YACxB6F,KAAK,CAACU,MAAM,GAAGA,MAAM,CAACvG,MAAM;UAC7B,CAAC,MACI;YACJ6F,KAAK,CAACU,MAAM,GAAGA,MAAM,CAACrG,GAAG;UAC1B;QACD;QACA,IAAI,CAACK,SAAS,CAACsF,KAAK,GAAGA,KAAK;MAC7B;MACA;AACF;AACA;MACE,WAAWA,KAAKA,CAAA,EAAG;QAClB,OAAO;UACNI,KAAK;UACLM;QACD,CAAC;MACF;MACA;AACF;AACA;MACE,OAAOmI,GAAGA,CAAC1I,GAAG,EAAEJ,OAAO,EAAE;QACxB,MAAMlB,KAAK,GAAG,IAAI4J,MAAM,CAAC1I,OAAO,CAAC;QACjC,OAAOlB,KAAK,CAACgK,GAAG,CAAC1I,GAAG,CAAC;MACtB;MACA;AACF;AACA;MACE,OAAO2I,SAASA,CAAC3I,GAAG,EAAEJ,OAAO,EAAE;QAC9B,MAAMlB,KAAK,GAAG,IAAI4J,MAAM,CAAC1I,OAAO,CAAC;QACjC,OAAOlB,KAAK,CAACQ,YAAY,CAACc,GAAG,CAAC;MAC/B;MACA;AACF;AACA;MACE0I,GAAGA,CAAC1I,GAAG,EAAE;QACRA,GAAG,GAAGA,GAAG,CACPvE,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC;QAC3B,IAAI,CAAC2F,WAAW,CAACpB,GAAG,EAAE,IAAI,CAACf,MAAM,CAAC;QAClC,KAAK,IAAI9B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACoL,WAAW,CAACjL,MAAM,EAAEH,CAAC,EAAE,EAAE;UACjD,MAAMyL,IAAI,GAAG,IAAI,CAACL,WAAW,CAACpL,CAAC,CAAC;UAChC,IAAI,CAAC+B,YAAY,CAAC0J,IAAI,CAAC5I,GAAG,EAAE4I,IAAI,CAAC3J,MAAM,CAAC;QACzC;QACA,IAAI,CAACsJ,WAAW,GAAG,EAAE;QACrB,OAAO,IAAI,CAACtJ,MAAM;MACnB;MACAmC,WAAWA,CAACpB,GAAG,EAAEf,MAAM,GAAG,EAAE,EAAE4J,oBAAoB,GAAG,KAAK,EAAE;QAC3D,IAAI,IAAI,CAACjJ,OAAO,CAACxF,QAAQ,EAAE;UAC1B4F,GAAG,GAAGA,GAAG,CAACvE,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,CAACA,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC;QACvD,CAAC,MACI;UACJuE,GAAG,GAAGA,GAAG,CAACvE,OAAO,CAAC,cAAc,EAAE,CAACqN,CAAC,EAAEC,OAAO,EAAEC,IAAI,KAAK;YACvD,OAAOD,OAAO,GAAG,MAAM,CAACxG,MAAM,CAACyG,IAAI,CAAC1L,MAAM,CAAC;UAC5C,CAAC,CAAC;QACH;QACA,IAAIyB,KAAK;QACT,IAAIsC,SAAS;QACb,IAAI4H,MAAM;QACV,OAAOjJ,GAAG,EAAE;UACX,IAAI,IAAI,CAACJ,OAAO,CAAC3F,UAAU,IACvB,IAAI,CAAC2F,OAAO,CAAC3F,UAAU,CAACgG,KAAK,IAC7B,IAAI,CAACL,OAAO,CAAC3F,UAAU,CAACgG,KAAK,CAAC2D,IAAI,CAAEsF,YAAY,IAAK;YACvD,IAAInK,KAAK,GAAGmK,YAAY,CAACC,IAAI,CAAC;cAAEzK,KAAK,EAAE;YAAK,CAAC,EAAEsB,GAAG,EAAEf,MAAM,CAAC,EAAE;cAC5De,GAAG,GAAGA,GAAG,CAACyB,SAAS,CAAC1C,KAAK,CAACN,GAAG,CAACnB,MAAM,CAAC;cACrC2B,MAAM,CAACxB,IAAI,CAACsB,KAAK,CAAC;cAClB,OAAO,IAAI;YACZ;YACA,OAAO,KAAK;UACb,CAAC,CAAC,EAAE;YACJ;UACD;UACA;UACA,IAAIA,KAAK,GAAG,IAAI,CAACxE,SAAS,CAACwF,KAAK,CAACC,GAAG,CAAC,EAAE;YACtCA,GAAG,GAAGA,GAAG,CAACyB,SAAS,CAAC1C,KAAK,CAACN,GAAG,CAACnB,MAAM,CAAC;YACrC,IAAIyB,KAAK,CAACN,GAAG,CAACnB,MAAM,KAAK,CAAC,IAAI2B,MAAM,CAAC3B,MAAM,GAAG,CAAC,EAAE;cAChD;cACA;cACA2B,MAAM,CAACA,MAAM,CAAC3B,MAAM,GAAG,CAAC,CAAC,CAACmB,GAAG,IAAI,IAAI;YACtC,CAAC,MACI;cACJQ,MAAM,CAACxB,IAAI,CAACsB,KAAK,CAAC;YACnB;YACA;UACD;UACA;UACA,IAAIA,KAAK,GAAG,IAAI,CAACxE,SAAS,CAAC4F,IAAI,CAACH,GAAG,CAAC,EAAE;YACrCA,GAAG,GAAGA,GAAG,CAACyB,SAAS,CAAC1C,KAAK,CAACN,GAAG,CAACnB,MAAM,CAAC;YACrC+D,SAAS,GAAGpC,MAAM,CAACA,MAAM,CAAC3B,MAAM,GAAG,CAAC,CAAC;YACrC;YACA,IAAI+D,SAAS,KAAKA,SAAS,CAACrC,IAAI,KAAK,WAAW,IAAIqC,SAAS,CAACrC,IAAI,KAAK,MAAM,CAAC,EAAE;cAC/EqC,SAAS,CAAC5C,GAAG,IAAI,IAAI,GAAGM,KAAK,CAACN,GAAG;cACjC4C,SAAS,CAACzC,IAAI,IAAI,IAAI,GAAGG,KAAK,CAACH,IAAI;cACnC,IAAI,CAAC2J,WAAW,CAAC,IAAI,CAACA,WAAW,CAACjL,MAAM,GAAG,CAAC,CAAC,CAAC0C,GAAG,GAAGqB,SAAS,CAACzC,IAAI;YACnE,CAAC,MACI;cACJK,MAAM,CAACxB,IAAI,CAACsB,KAAK,CAAC;YACnB;YACA;UACD;UACA;UACA,IAAIA,KAAK,GAAG,IAAI,CAACxE,SAAS,CAAC8F,MAAM,CAACL,GAAG,CAAC,EAAE;YACvCA,GAAG,GAAGA,GAAG,CAACyB,SAAS,CAAC1C,KAAK,CAACN,GAAG,CAACnB,MAAM,CAAC;YACrC2B,MAAM,CAACxB,IAAI,CAACsB,KAAK,CAAC;YAClB;UACD;UACA;UACA,IAAIA,KAAK,GAAG,IAAI,CAACxE,SAAS,CAACkG,OAAO,CAACT,GAAG,CAAC,EAAE;YACxCA,GAAG,GAAGA,GAAG,CAACyB,SAAS,CAAC1C,KAAK,CAACN,GAAG,CAACnB,MAAM,CAAC;YACrC2B,MAAM,CAACxB,IAAI,CAACsB,KAAK,CAAC;YAClB;UACD;UACA;UACA,IAAIA,KAAK,GAAG,IAAI,CAACxE,SAAS,CAACqG,EAAE,CAACZ,GAAG,CAAC,EAAE;YACnCA,GAAG,GAAGA,GAAG,CAACyB,SAAS,CAAC1C,KAAK,CAACN,GAAG,CAACnB,MAAM,CAAC;YACrC2B,MAAM,CAACxB,IAAI,CAACsB,KAAK,CAAC;YAClB;UACD;UACA;UACA,IAAIA,KAAK,GAAG,IAAI,CAACxE,SAAS,CAACsG,UAAU,CAACb,GAAG,CAAC,EAAE;YAC3CA,GAAG,GAAGA,GAAG,CAACyB,SAAS,CAAC1C,KAAK,CAACN,GAAG,CAACnB,MAAM,CAAC;YACrC2B,MAAM,CAACxB,IAAI,CAACsB,KAAK,CAAC;YAClB;UACD;UACA;UACA,IAAIA,KAAK,GAAG,IAAI,CAACxE,SAAS,CAACmH,IAAI,CAAC1B,GAAG,CAAC,EAAE;YACrCA,GAAG,GAAGA,GAAG,CAACyB,SAAS,CAAC1C,KAAK,CAACN,GAAG,CAACnB,MAAM,CAAC;YACrC2B,MAAM,CAACxB,IAAI,CAACsB,KAAK,CAAC;YAClB;UACD;UACA;UACA,IAAIA,KAAK,GAAG,IAAI,CAACxE,SAAS,CAACe,IAAI,CAAC0E,GAAG,CAAC,EAAE;YACrCA,GAAG,GAAGA,GAAG,CAACyB,SAAS,CAAC1C,KAAK,CAACN,GAAG,CAACnB,MAAM,CAAC;YACrC2B,MAAM,CAACxB,IAAI,CAACsB,KAAK,CAAC;YAClB;UACD;UACA;UACA,IAAIA,KAAK,GAAG,IAAI,CAACxE,SAAS,CAACuJ,GAAG,CAAC9D,GAAG,CAAC,EAAE;YACpCA,GAAG,GAAGA,GAAG,CAACyB,SAAS,CAAC1C,KAAK,CAACN,GAAG,CAACnB,MAAM,CAAC;YACrC+D,SAAS,GAAGpC,MAAM,CAACA,MAAM,CAAC3B,MAAM,GAAG,CAAC,CAAC;YACrC,IAAI+D,SAAS,KAAKA,SAAS,CAACrC,IAAI,KAAK,WAAW,IAAIqC,SAAS,CAACrC,IAAI,KAAK,MAAM,CAAC,EAAE;cAC/EqC,SAAS,CAAC5C,GAAG,IAAI,IAAI,GAAGM,KAAK,CAACN,GAAG;cACjC4C,SAAS,CAACzC,IAAI,IAAI,IAAI,GAAGG,KAAK,CAACN,GAAG;cAClC,IAAI,CAAC8J,WAAW,CAAC,IAAI,CAACA,WAAW,CAACjL,MAAM,GAAG,CAAC,CAAC,CAAC0C,GAAG,GAAGqB,SAAS,CAACzC,IAAI;YACnE,CAAC,MACI,IAAI,CAAC,IAAI,CAACK,MAAM,CAACiG,KAAK,CAACnG,KAAK,CAACgF,GAAG,CAAC,EAAE;cACvC,IAAI,CAAC9E,MAAM,CAACiG,KAAK,CAACnG,KAAK,CAACgF,GAAG,CAAC,GAAG;gBAC9B3H,IAAI,EAAE2C,KAAK,CAAC3C,IAAI;gBAChBuC,KAAK,EAAEI,KAAK,CAACJ;cACd,CAAC;YACF;YACA;UACD;UACA;UACA,IAAII,KAAK,GAAG,IAAI,CAACxE,SAAS,CAAC0J,KAAK,CAACjE,GAAG,CAAC,EAAE;YACtCA,GAAG,GAAGA,GAAG,CAACyB,SAAS,CAAC1C,KAAK,CAACN,GAAG,CAACnB,MAAM,CAAC;YACrC2B,MAAM,CAACxB,IAAI,CAACsB,KAAK,CAAC;YAClB;UACD;UACA;UACA,IAAIA,KAAK,GAAG,IAAI,CAACxE,SAAS,CAACkK,QAAQ,CAACzE,GAAG,CAAC,EAAE;YACzCA,GAAG,GAAGA,GAAG,CAACyB,SAAS,CAAC1C,KAAK,CAACN,GAAG,CAACnB,MAAM,CAAC;YACrC2B,MAAM,CAACxB,IAAI,CAACsB,KAAK,CAAC;YAClB;UACD;UACA;UACA;UACAkK,MAAM,GAAGjJ,GAAG;UACZ,IAAI,IAAI,CAACJ,OAAO,CAAC3F,UAAU,IAAI,IAAI,CAAC2F,OAAO,CAAC3F,UAAU,CAACmP,UAAU,EAAE;YAClE,IAAIC,UAAU,GAAGC,QAAQ;YACzB,MAAMC,OAAO,GAAGvJ,GAAG,CAAC/B,KAAK,CAAC,CAAC,CAAC;YAC5B,IAAIuL,SAAS;YACb,IAAI,CAAC5J,OAAO,CAAC3F,UAAU,CAACmP,UAAU,CAACK,OAAO,CAAEC,aAAa,IAAK;cAC7DF,SAAS,GAAGE,aAAa,CAACP,IAAI,CAAC;gBAAEzK,KAAK,EAAE;cAAK,CAAC,EAAE6K,OAAO,CAAC;cACxD,IAAI,OAAOC,SAAS,KAAK,QAAQ,IAAIA,SAAS,IAAI,CAAC,EAAE;gBACpDH,UAAU,GAAGvG,IAAI,CAACC,GAAG,CAACsG,UAAU,EAAEG,SAAS,CAAC;cAC7C;YACD,CAAC,CAAC;YACF,IAAIH,UAAU,GAAGC,QAAQ,IAAID,UAAU,IAAI,CAAC,EAAE;cAC7CJ,MAAM,GAAGjJ,GAAG,CAACyB,SAAS,CAAC,CAAC,EAAE4H,UAAU,GAAG,CAAC,CAAC;YAC1C;UACD;UACA,IAAI,IAAI,CAACxK,KAAK,CAACsC,GAAG,KAAKpC,KAAK,GAAG,IAAI,CAACxE,SAAS,CAACmK,SAAS,CAACuE,MAAM,CAAC,CAAC,EAAE;YACjE5H,SAAS,GAAGpC,MAAM,CAACA,MAAM,CAAC3B,MAAM,GAAG,CAAC,CAAC;YACrC,IAAIuL,oBAAoB,IAAIxH,SAAS,EAAErC,IAAI,KAAK,WAAW,EAAE;cAC5DqC,SAAS,CAAC5C,GAAG,IAAI,IAAI,GAAGM,KAAK,CAACN,GAAG;cACjC4C,SAAS,CAACzC,IAAI,IAAI,IAAI,GAAGG,KAAK,CAACH,IAAI;cACnC,IAAI,CAAC2J,WAAW,CAAChL,GAAG,CAAC,CAAC;cACtB,IAAI,CAACgL,WAAW,CAAC,IAAI,CAACA,WAAW,CAACjL,MAAM,GAAG,CAAC,CAAC,CAAC0C,GAAG,GAAGqB,SAAS,CAACzC,IAAI;YACnE,CAAC,MACI;cACJK,MAAM,CAACxB,IAAI,CAACsB,KAAK,CAAC;YACnB;YACA8J,oBAAoB,GAAII,MAAM,CAAC3L,MAAM,KAAK0C,GAAG,CAAC1C,MAAO;YACrD0C,GAAG,GAAGA,GAAG,CAACyB,SAAS,CAAC1C,KAAK,CAACN,GAAG,CAACnB,MAAM,CAAC;YACrC;UACD;UACA;UACA,IAAIyB,KAAK,GAAG,IAAI,CAACxE,SAAS,CAACqE,IAAI,CAACoB,GAAG,CAAC,EAAE;YACrCA,GAAG,GAAGA,GAAG,CAACyB,SAAS,CAAC1C,KAAK,CAACN,GAAG,CAACnB,MAAM,CAAC;YACrC+D,SAAS,GAAGpC,MAAM,CAACA,MAAM,CAAC3B,MAAM,GAAG,CAAC,CAAC;YACrC,IAAI+D,SAAS,IAAIA,SAAS,CAACrC,IAAI,KAAK,MAAM,EAAE;cAC3CqC,SAAS,CAAC5C,GAAG,IAAI,IAAI,GAAGM,KAAK,CAACN,GAAG;cACjC4C,SAAS,CAACzC,IAAI,IAAI,IAAI,GAAGG,KAAK,CAACH,IAAI;cACnC,IAAI,CAAC2J,WAAW,CAAChL,GAAG,CAAC,CAAC;cACtB,IAAI,CAACgL,WAAW,CAAC,IAAI,CAACA,WAAW,CAACjL,MAAM,GAAG,CAAC,CAAC,CAAC0C,GAAG,GAAGqB,SAAS,CAACzC,IAAI;YACnE,CAAC,MACI;cACJK,MAAM,CAACxB,IAAI,CAACsB,KAAK,CAAC;YACnB;YACA;UACD;UACA,IAAIiB,GAAG,EAAE;YACR,MAAM2J,MAAM,GAAG,yBAAyB,GAAG3J,GAAG,CAAC4J,UAAU,CAAC,CAAC,CAAC;YAC5D,IAAI,IAAI,CAAChK,OAAO,CAACtF,MAAM,EAAE;cACxBuP,OAAO,CAACC,KAAK,CAACH,MAAM,CAAC;cACrB;YACD,CAAC,MACI;cACJ,MAAM,IAAII,KAAK,CAACJ,MAAM,CAAC;YACxB;UACD;QACD;QACA,IAAI,CAAC9K,KAAK,CAACsC,GAAG,GAAG,IAAI;QACrB,OAAOlC,MAAM;MACd;MACAsB,MAAMA,CAACP,GAAG,EAAEf,MAAM,GAAG,EAAE,EAAE;QACxB,IAAI,CAACsJ,WAAW,CAAC9K,IAAI,CAAC;UAAEuC,GAAG;UAAEf;QAAO,CAAC,CAAC;QACtC,OAAOA,MAAM;MACd;MACA;AACF;AACA;MACEC,YAAYA,CAACc,GAAG,EAAEf,MAAM,GAAG,EAAE,EAAE;QAC9B,IAAIF,KAAK,EAAEsC,SAAS,EAAE4H,MAAM;QAC5B;QACA,IAAI3D,SAAS,GAAGtF,GAAG;QACnB,IAAIpD,KAAK;QACT,IAAIoN,YAAY,EAAEzE,QAAQ;QAC1B;QACA,IAAI,IAAI,CAACtG,MAAM,CAACiG,KAAK,EAAE;UACtB,MAAMA,KAAK,GAAGsD,MAAM,CAACyB,IAAI,CAAC,IAAI,CAAChL,MAAM,CAACiG,KAAK,CAAC;UAC5C,IAAIA,KAAK,CAAC5H,MAAM,GAAG,CAAC,EAAE;YACrB,OAAO,CAACV,KAAK,GAAG,IAAI,CAACrC,SAAS,CAACsF,KAAK,CAACU,MAAM,CAACyH,aAAa,CAACzL,IAAI,CAAC+I,SAAS,CAAC,KAAK,IAAI,EAAE;cACnF,IAAIJ,KAAK,CAACgF,QAAQ,CAACtN,KAAK,CAAC,CAAC,CAAC,CAACqB,KAAK,CAACrB,KAAK,CAAC,CAAC,CAAC,CAACuN,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;gBACtE7E,SAAS,GAAGA,SAAS,CAACrH,KAAK,CAAC,CAAC,EAAErB,KAAK,CAACyJ,KAAK,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC9D,MAAM,CAAC3F,KAAK,CAAC,CAAC,CAAC,CAACU,MAAM,GAAG,CAAC,CAAC,GAAG,GAAG,GAAGgI,SAAS,CAACrH,KAAK,CAAC,IAAI,CAAC1D,SAAS,CAACsF,KAAK,CAACU,MAAM,CAACyH,aAAa,CAAC7B,SAAS,CAAC;cACjK;YACD;UACD;QACD;QACA;QACA,OAAO,CAACvJ,KAAK,GAAG,IAAI,CAACrC,SAAS,CAACsF,KAAK,CAACU,MAAM,CAACsH,SAAS,CAACtL,IAAI,CAAC+I,SAAS,CAAC,KAAK,IAAI,EAAE;UAC/EA,SAAS,GAAGA,SAAS,CAACrH,KAAK,CAAC,CAAC,EAAErB,KAAK,CAACyJ,KAAK,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC9D,MAAM,CAAC3F,KAAK,CAAC,CAAC,CAAC,CAACU,MAAM,GAAG,CAAC,CAAC,GAAG,GAAG,GAAGgI,SAAS,CAACrH,KAAK,CAAC,IAAI,CAAC1D,SAAS,CAACsF,KAAK,CAACU,MAAM,CAACsH,SAAS,CAAC1B,SAAS,CAAC;QAC7J;QACA;QACA,OAAO,CAACvJ,KAAK,GAAG,IAAI,CAACrC,SAAS,CAACsF,KAAK,CAACU,MAAM,CAACC,cAAc,CAACjE,IAAI,CAAC+I,SAAS,CAAC,KAAK,IAAI,EAAE;UACpFA,SAAS,GAAGA,SAAS,CAACrH,KAAK,CAAC,CAAC,EAAErB,KAAK,CAACyJ,KAAK,CAAC,GAAG,IAAI,GAAGf,SAAS,CAACrH,KAAK,CAAC,IAAI,CAAC1D,SAAS,CAACsF,KAAK,CAACU,MAAM,CAACC,cAAc,CAAC2F,SAAS,CAAC;QAC3H;QACA,OAAOnG,GAAG,EAAE;UACX,IAAI,CAACgK,YAAY,EAAE;YAClBzE,QAAQ,GAAG,EAAE;UACd;UACAyE,YAAY,GAAG,KAAK;UACpB;UACA,IAAI,IAAI,CAACpK,OAAO,CAAC3F,UAAU,IACvB,IAAI,CAAC2F,OAAO,CAAC3F,UAAU,CAACsG,MAAM,IAC9B,IAAI,CAACX,OAAO,CAAC3F,UAAU,CAACsG,MAAM,CAACqD,IAAI,CAAEsF,YAAY,IAAK;YACxD,IAAInK,KAAK,GAAGmK,YAAY,CAACC,IAAI,CAAC;cAAEzK,KAAK,EAAE;YAAK,CAAC,EAAEsB,GAAG,EAAEf,MAAM,CAAC,EAAE;cAC5De,GAAG,GAAGA,GAAG,CAACyB,SAAS,CAAC1C,KAAK,CAACN,GAAG,CAACnB,MAAM,CAAC;cACrC2B,MAAM,CAACxB,IAAI,CAACsB,KAAK,CAAC;cAClB,OAAO,IAAI;YACZ;YACA,OAAO,KAAK;UACb,CAAC,CAAC,EAAE;YACJ;UACD;UACA;UACA,IAAIA,KAAK,GAAG,IAAI,CAACxE,SAAS,CAACoK,MAAM,CAAC3E,GAAG,CAAC,EAAE;YACvCA,GAAG,GAAGA,GAAG,CAACyB,SAAS,CAAC1C,KAAK,CAACN,GAAG,CAACnB,MAAM,CAAC;YACrC2B,MAAM,CAACxB,IAAI,CAACsB,KAAK,CAAC;YAClB;UACD;UACA;UACA,IAAIA,KAAK,GAAG,IAAI,CAACxE,SAAS,CAACwJ,GAAG,CAAC/D,GAAG,CAAC,EAAE;YACpCA,GAAG,GAAGA,GAAG,CAACyB,SAAS,CAAC1C,KAAK,CAACN,GAAG,CAACnB,MAAM,CAAC;YACrC+D,SAAS,GAAGpC,MAAM,CAACA,MAAM,CAAC3B,MAAM,GAAG,CAAC,CAAC;YACrC,IAAI+D,SAAS,IAAItC,KAAK,CAACC,IAAI,KAAK,MAAM,IAAIqC,SAAS,CAACrC,IAAI,KAAK,MAAM,EAAE;cACpEqC,SAAS,CAAC5C,GAAG,IAAIM,KAAK,CAACN,GAAG;cAC1B4C,SAAS,CAACzC,IAAI,IAAIG,KAAK,CAACH,IAAI;YAC7B,CAAC,MACI;cACJK,MAAM,CAACxB,IAAI,CAACsB,KAAK,CAAC;YACnB;YACA;UACD;UACA;UACA,IAAIA,KAAK,GAAG,IAAI,CAACxE,SAAS,CAACiE,IAAI,CAACwB,GAAG,CAAC,EAAE;YACrCA,GAAG,GAAGA,GAAG,CAACyB,SAAS,CAAC1C,KAAK,CAACN,GAAG,CAACnB,MAAM,CAAC;YACrC2B,MAAM,CAACxB,IAAI,CAACsB,KAAK,CAAC;YAClB;UACD;UACA;UACA,IAAIA,KAAK,GAAG,IAAI,CAACxE,SAAS,CAAC0K,OAAO,CAACjF,GAAG,EAAE,IAAI,CAACf,MAAM,CAACiG,KAAK,CAAC,EAAE;YAC3DlF,GAAG,GAAGA,GAAG,CAACyB,SAAS,CAAC1C,KAAK,CAACN,GAAG,CAACnB,MAAM,CAAC;YACrC+D,SAAS,GAAGpC,MAAM,CAACA,MAAM,CAAC3B,MAAM,GAAG,CAAC,CAAC;YACrC,IAAI+D,SAAS,IAAItC,KAAK,CAACC,IAAI,KAAK,MAAM,IAAIqC,SAAS,CAACrC,IAAI,KAAK,MAAM,EAAE;cACpEqC,SAAS,CAAC5C,GAAG,IAAIM,KAAK,CAACN,GAAG;cAC1B4C,SAAS,CAACzC,IAAI,IAAIG,KAAK,CAACH,IAAI;YAC7B,CAAC,MACI;cACJK,MAAM,CAACxB,IAAI,CAACsB,KAAK,CAAC;YACnB;YACA;UACD;UACA;UACA,IAAIA,KAAK,GAAG,IAAI,CAACxE,SAAS,CAAC8K,QAAQ,CAACrF,GAAG,EAAEsF,SAAS,EAAEC,QAAQ,CAAC,EAAE;YAC9DvF,GAAG,GAAGA,GAAG,CAACyB,SAAS,CAAC1C,KAAK,CAACN,GAAG,CAACnB,MAAM,CAAC;YACrC2B,MAAM,CAACxB,IAAI,CAACsB,KAAK,CAAC;YAClB;UACD;UACA;UACA,IAAIA,KAAK,GAAG,IAAI,CAACxE,SAAS,CAAC+L,QAAQ,CAACtG,GAAG,CAAC,EAAE;YACzCA,GAAG,GAAGA,GAAG,CAACyB,SAAS,CAAC1C,KAAK,CAACN,GAAG,CAACnB,MAAM,CAAC;YACrC2B,MAAM,CAACxB,IAAI,CAACsB,KAAK,CAAC;YAClB;UACD;UACA;UACA,IAAIA,KAAK,GAAG,IAAI,CAACxE,SAAS,CAACkM,EAAE,CAACzG,GAAG,CAAC,EAAE;YACnCA,GAAG,GAAGA,GAAG,CAACyB,SAAS,CAAC1C,KAAK,CAACN,GAAG,CAACnB,MAAM,CAAC;YACrC2B,MAAM,CAACxB,IAAI,CAACsB,KAAK,CAAC;YAClB;UACD;UACA;UACA,IAAIA,KAAK,GAAG,IAAI,CAACxE,SAAS,CAACmM,GAAG,CAAC1G,GAAG,CAAC,EAAE;YACpCA,GAAG,GAAGA,GAAG,CAACyB,SAAS,CAAC1C,KAAK,CAACN,GAAG,CAACnB,MAAM,CAAC;YACrC2B,MAAM,CAACxB,IAAI,CAACsB,KAAK,CAAC;YAClB;UACD;UACA;UACA,IAAIA,KAAK,GAAG,IAAI,CAACxE,SAAS,CAACoM,QAAQ,CAAC3G,GAAG,CAAC,EAAE;YACzCA,GAAG,GAAGA,GAAG,CAACyB,SAAS,CAAC1C,KAAK,CAACN,GAAG,CAACnB,MAAM,CAAC;YACrC2B,MAAM,CAACxB,IAAI,CAACsB,KAAK,CAAC;YAClB;UACD;UACA;UACA,IAAI,CAAC,IAAI,CAACF,KAAK,CAACC,MAAM,KAAKC,KAAK,GAAG,IAAI,CAACxE,SAAS,CAACqM,GAAG,CAAC5G,GAAG,CAAC,CAAC,EAAE;YAC5DA,GAAG,GAAGA,GAAG,CAACyB,SAAS,CAAC1C,KAAK,CAACN,GAAG,CAACnB,MAAM,CAAC;YACrC2B,MAAM,CAACxB,IAAI,CAACsB,KAAK,CAAC;YAClB;UACD;UACA;UACA;UACAkK,MAAM,GAAGjJ,GAAG;UACZ,IAAI,IAAI,CAACJ,OAAO,CAAC3F,UAAU,IAAI,IAAI,CAAC2F,OAAO,CAAC3F,UAAU,CAACmQ,WAAW,EAAE;YACnE,IAAIf,UAAU,GAAGC,QAAQ;YACzB,MAAMC,OAAO,GAAGvJ,GAAG,CAAC/B,KAAK,CAAC,CAAC,CAAC;YAC5B,IAAIuL,SAAS;YACb,IAAI,CAAC5J,OAAO,CAAC3F,UAAU,CAACmQ,WAAW,CAACX,OAAO,CAAEC,aAAa,IAAK;cAC9DF,SAAS,GAAGE,aAAa,CAACP,IAAI,CAAC;gBAAEzK,KAAK,EAAE;cAAK,CAAC,EAAE6K,OAAO,CAAC;cACxD,IAAI,OAAOC,SAAS,KAAK,QAAQ,IAAIA,SAAS,IAAI,CAAC,EAAE;gBACpDH,UAAU,GAAGvG,IAAI,CAACC,GAAG,CAACsG,UAAU,EAAEG,SAAS,CAAC;cAC7C;YACD,CAAC,CAAC;YACF,IAAIH,UAAU,GAAGC,QAAQ,IAAID,UAAU,IAAI,CAAC,EAAE;cAC7CJ,MAAM,GAAGjJ,GAAG,CAACyB,SAAS,CAAC,CAAC,EAAE4H,UAAU,GAAG,CAAC,CAAC;YAC1C;UACD;UACA,IAAItK,KAAK,GAAG,IAAI,CAACxE,SAAS,CAACwM,UAAU,CAACkC,MAAM,CAAC,EAAE;YAC9CjJ,GAAG,GAAGA,GAAG,CAACyB,SAAS,CAAC1C,KAAK,CAACN,GAAG,CAACnB,MAAM,CAAC;YACrC,IAAIyB,KAAK,CAACN,GAAG,CAACR,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;cAAE;cAClCsH,QAAQ,GAAGxG,KAAK,CAACN,GAAG,CAACR,KAAK,CAAC,CAAC,CAAC,CAAC;YAC/B;YACA+L,YAAY,GAAG,IAAI;YACnB3I,SAAS,GAAGpC,MAAM,CAACA,MAAM,CAAC3B,MAAM,GAAG,CAAC,CAAC;YACrC,IAAI+D,SAAS,IAAIA,SAAS,CAACrC,IAAI,KAAK,MAAM,EAAE;cAC3CqC,SAAS,CAAC5C,GAAG,IAAIM,KAAK,CAACN,GAAG;cAC1B4C,SAAS,CAACzC,IAAI,IAAIG,KAAK,CAACH,IAAI;YAC7B,CAAC,MACI;cACJK,MAAM,CAACxB,IAAI,CAACsB,KAAK,CAAC;YACnB;YACA;UACD;UACA,IAAIiB,GAAG,EAAE;YACR,MAAM2J,MAAM,GAAG,yBAAyB,GAAG3J,GAAG,CAAC4J,UAAU,CAAC,CAAC,CAAC;YAC5D,IAAI,IAAI,CAAChK,OAAO,CAACtF,MAAM,EAAE;cACxBuP,OAAO,CAACC,KAAK,CAACH,MAAM,CAAC;cACrB;YACD,CAAC,MACI;cACJ,MAAM,IAAII,KAAK,CAACJ,MAAM,CAAC;YACxB;UACD;QACD;QACA,OAAO1K,MAAM;MACd;IACD;;IAEA;AACD;AACA;IACC,MAAMoL,SAAS,CAAC;MACfzK,OAAO;MACP0K,MAAM,CAAC,CAAC;MACRxK,WAAWA,CAACF,OAAO,EAAE;QACpB,IAAI,CAACA,OAAO,GAAGA,OAAO,IAAInG,OAAO,CAACgB,QAAQ;MAC3C;MACAsF,KAAKA,CAAChB,KAAK,EAAE;QACZ,OAAO,EAAE;MACV;MACAoB,IAAIA,CAAC;QAAEvB,IAAI;QAAE0B,IAAI;QAAEvD;MAAQ,CAAC,EAAE;QAC7B,MAAMwN,UAAU,GAAG,CAACjK,IAAI,IAAI,EAAE,EAAE1D,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAClD,MAAMuD,IAAI,GAAGvB,IAAI,CAACnD,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,IAAI;QAC3C,IAAI,CAAC8O,UAAU,EAAE;UAChB,OAAO,aAAa,IAChBxN,OAAO,GAAGoD,IAAI,GAAG9E,QAAQ,CAAC8E,IAAI,EAAE,IAAI,CAAC,CAAC,GACvC,iBAAiB;QACrB;QACA,OAAO,6BAA6B,GACjC9E,QAAQ,CAACkP,UAAU,CAAC,GACpB,IAAI,IACHxN,OAAO,GAAGoD,IAAI,GAAG9E,QAAQ,CAAC8E,IAAI,EAAE,IAAI,CAAC,CAAC,GACvC,iBAAiB;MACrB;MACAU,UAAUA,CAAC;QAAE5B;MAAO,CAAC,EAAE;QACtB,MAAMuL,IAAI,GAAG,IAAI,CAACF,MAAM,CAACG,KAAK,CAACxL,MAAM,CAAC;QACtC,OAAO,iBAAiBuL,IAAI,iBAAiB;MAC9C;MACAlP,IAAIA,CAAC;QAAEsD;MAAK,CAAC,EAAE;QACd,OAAOA,IAAI;MACZ;MACA6B,OAAOA,CAAC;QAAExB,MAAM;QAAE0B;MAAM,CAAC,EAAE;QAC1B,OAAO,KAAKA,KAAK,IAAI,IAAI,CAAC2J,MAAM,CAACI,WAAW,CAACzL,MAAM,CAAC,MAAM0B,KAAK,KAAK;MACrE;MACAC,EAAEA,CAAC7B,KAAK,EAAE;QACT,OAAO,QAAQ;MAChB;MACA2C,IAAIA,CAAC3C,KAAK,EAAE;QACX,MAAM8C,OAAO,GAAG9C,KAAK,CAAC8C,OAAO;QAC7B,MAAMC,KAAK,GAAG/C,KAAK,CAAC+C,KAAK;QACzB,IAAI0I,IAAI,GAAG,EAAE;QACb,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG5L,KAAK,CAACiD,KAAK,CAAC1E,MAAM,EAAEqN,CAAC,EAAE,EAAE;UAC5C,MAAMtG,IAAI,GAAGtF,KAAK,CAACiD,KAAK,CAAC2I,CAAC,CAAC;UAC3BH,IAAI,IAAI,IAAI,CAACI,QAAQ,CAACvG,IAAI,CAAC;QAC5B;QACA,MAAMrF,IAAI,GAAG6C,OAAO,GAAG,IAAI,GAAG,IAAI;QAClC,MAAMgJ,SAAS,GAAIhJ,OAAO,IAAIC,KAAK,KAAK,CAAC,GAAK,UAAU,GAAGA,KAAK,GAAG,GAAG,GAAI,EAAE;QAC5E,OAAO,GAAG,GAAG9C,IAAI,GAAG6L,SAAS,GAAG,KAAK,GAAGL,IAAI,GAAG,IAAI,GAAGxL,IAAI,GAAG,KAAK;MACnE;MACA4L,QAAQA,CAACvG,IAAI,EAAE;QACd,IAAIyG,QAAQ,GAAG,EAAE;QACjB,IAAIzG,IAAI,CAACf,IAAI,EAAE;UACd,MAAMyH,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC;YAAExH,OAAO,EAAE,CAAC,CAACc,IAAI,CAACd;UAAQ,CAAC,CAAC;UAC3D,IAAIc,IAAI,CAACtC,KAAK,EAAE;YACf,IAAIsC,IAAI,CAACpF,MAAM,CAAC3B,MAAM,GAAG,CAAC,IAAI+G,IAAI,CAACpF,MAAM,CAAC,CAAC,CAAC,CAACD,IAAI,KAAK,WAAW,EAAE;cAClEqF,IAAI,CAACpF,MAAM,CAAC,CAAC,CAAC,CAACL,IAAI,GAAGmM,QAAQ,GAAG,GAAG,GAAG1G,IAAI,CAACpF,MAAM,CAAC,CAAC,CAAC,CAACL,IAAI;cAC1D,IAAIyF,IAAI,CAACpF,MAAM,CAAC,CAAC,CAAC,CAACA,MAAM,IAAIoF,IAAI,CAACpF,MAAM,CAAC,CAAC,CAAC,CAACA,MAAM,CAAC3B,MAAM,GAAG,CAAC,IAAI+G,IAAI,CAACpF,MAAM,CAAC,CAAC,CAAC,CAACA,MAAM,CAAC,CAAC,CAAC,CAACD,IAAI,KAAK,MAAM,EAAE;gBAC1GqF,IAAI,CAACpF,MAAM,CAAC,CAAC,CAAC,CAACA,MAAM,CAAC,CAAC,CAAC,CAACL,IAAI,GAAGmM,QAAQ,GAAG,GAAG,GAAG1G,IAAI,CAACpF,MAAM,CAAC,CAAC,CAAC,CAACA,MAAM,CAAC,CAAC,CAAC,CAACL,IAAI;cAC/E;YACD,CAAC,MACI;cACJyF,IAAI,CAACpF,MAAM,CAAC+L,OAAO,CAAC;gBACnBhM,IAAI,EAAE,MAAM;gBACZP,GAAG,EAAEsM,QAAQ,GAAG,GAAG;gBACnBnM,IAAI,EAAEmM,QAAQ,GAAG;cAClB,CAAC,CAAC;YACH;UACD,CAAC,MACI;YACJD,QAAQ,IAAIC,QAAQ,GAAG,GAAG;UAC3B;QACD;QACAD,QAAQ,IAAI,IAAI,CAACR,MAAM,CAACG,KAAK,CAACpG,IAAI,CAACpF,MAAM,EAAE,CAAC,CAACoF,IAAI,CAACtC,KAAK,CAAC;QACxD,OAAO,OAAO+I,QAAQ,SAAS;MAChC;MACAC,QAAQA,CAAC;QAAExH;MAAQ,CAAC,EAAE;QACrB,OAAO,SAAS,IACZA,OAAO,GAAG,aAAa,GAAG,EAAE,CAAC,GAC9B,8BAA8B;MAClC;MACAmB,SAASA,CAAC;QAAEzF;MAAO,CAAC,EAAE;QACrB,OAAO,MAAM,IAAI,CAACqL,MAAM,CAACI,WAAW,CAACzL,MAAM,CAAC,QAAQ;MACrD;MACAgF,KAAKA,CAAClF,KAAK,EAAE;QACZ,IAAIuF,MAAM,GAAG,EAAE;QACf;QACA,IAAIE,IAAI,GAAG,EAAE;QACb,KAAK,IAAImG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG5L,KAAK,CAACuF,MAAM,CAAChH,MAAM,EAAEqN,CAAC,EAAE,EAAE;UAC7CnG,IAAI,IAAI,IAAI,CAACyG,SAAS,CAAClM,KAAK,CAACuF,MAAM,CAACqG,CAAC,CAAC,CAAC;QACxC;QACArG,MAAM,IAAI,IAAI,CAAC4G,QAAQ,CAAC;UAAEtM,IAAI,EAAE4F;QAAK,CAAC,CAAC;QACvC,IAAIgG,IAAI,GAAG,EAAE;QACb,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG5L,KAAK,CAACqF,IAAI,CAAC9G,MAAM,EAAEqN,CAAC,EAAE,EAAE;UAC3C,MAAMhO,GAAG,GAAGoC,KAAK,CAACqF,IAAI,CAACuG,CAAC,CAAC;UACzBnG,IAAI,GAAG,EAAE;UACT,KAAK,IAAI2G,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGxO,GAAG,CAACW,MAAM,EAAE6N,CAAC,EAAE,EAAE;YACpC3G,IAAI,IAAI,IAAI,CAACyG,SAAS,CAACtO,GAAG,CAACwO,CAAC,CAAC,CAAC;UAC/B;UACAX,IAAI,IAAI,IAAI,CAACU,QAAQ,CAAC;YAAEtM,IAAI,EAAE4F;UAAK,CAAC,CAAC;QACtC;QACA,IAAIgG,IAAI,EACPA,IAAI,GAAG,UAAUA,IAAI,UAAU;QAChC,OAAO,WAAW,GACf,WAAW,GACXlG,MAAM,GACN,YAAY,GACZkG,IAAI,GACJ,YAAY;MAChB;MACAU,QAAQA,CAAC;QAAEtM;MAAK,CAAC,EAAE;QAClB,OAAO,SAASA,IAAI,SAAS;MAC9B;MACAqM,SAASA,CAAClM,KAAK,EAAE;QAChB,MAAMqM,OAAO,GAAG,IAAI,CAACd,MAAM,CAACI,WAAW,CAAC3L,KAAK,CAACE,MAAM,CAAC;QACrD,MAAMD,IAAI,GAAGD,KAAK,CAACuF,MAAM,GAAG,IAAI,GAAG,IAAI;QACvC,MAAMP,GAAG,GAAGhF,KAAK,CAACwF,KAAK,GACpB,IAAIvF,IAAI,WAAWD,KAAK,CAACwF,KAAK,IAAI,GAClC,IAAIvF,IAAI,GAAG;QACd,OAAO+E,GAAG,GAAGqH,OAAO,GAAG,KAAKpM,IAAI,KAAK;MACtC;MACA;AACF;AACA;MACEqM,MAAMA,CAAC;QAAEpM;MAAO,CAAC,EAAE;QAClB,OAAO,WAAW,IAAI,CAACqL,MAAM,CAACI,WAAW,CAACzL,MAAM,CAAC,WAAW;MAC7D;MACAqM,EAAEA,CAAC;QAAErM;MAAO,CAAC,EAAE;QACd,OAAO,OAAO,IAAI,CAACqL,MAAM,CAACI,WAAW,CAACzL,MAAM,CAAC,OAAO;MACrD;MACAqH,QAAQA,CAAC;QAAE1H;MAAK,CAAC,EAAE;QAClB,OAAO,SAASA,IAAI,SAAS;MAC9B;MACA6H,EAAEA,CAAC1H,KAAK,EAAE;QACT,OAAO,MAAM;MACd;MACA2H,GAAGA,CAAC;QAAEzH;MAAO,CAAC,EAAE;QACf,OAAO,QAAQ,IAAI,CAACqL,MAAM,CAACI,WAAW,CAACzL,MAAM,CAAC,QAAQ;MACvD;MACAT,IAAIA,CAAC;QAAEpC,IAAI;QAAEuC,KAAK;QAAEM;MAAO,CAAC,EAAE;QAC7B,MAAML,IAAI,GAAG,IAAI,CAAC0L,MAAM,CAACI,WAAW,CAACzL,MAAM,CAAC;QAC5C,MAAMsM,SAAS,GAAGpP,QAAQ,CAACC,IAAI,CAAC;QAChC,IAAImP,SAAS,KAAK,IAAI,EAAE;UACvB,OAAO3M,IAAI;QACZ;QACAxC,IAAI,GAAGmP,SAAS;QAChB,IAAIC,GAAG,GAAG,WAAW,GAAGpP,IAAI,GAAG,GAAG;QAClC,IAAIuC,KAAK,EAAE;UACV6M,GAAG,IAAI,UAAU,GAAG7M,KAAK,GAAG,GAAG;QAChC;QACA6M,GAAG,IAAI,GAAG,GAAG5M,IAAI,GAAG,MAAM;QAC1B,OAAO4M,GAAG;MACX;MACAC,KAAKA,CAAC;QAAErP,IAAI;QAAEuC,KAAK;QAAEC;MAAK,CAAC,EAAE;QAC5B,MAAM2M,SAAS,GAAGpP,QAAQ,CAACC,IAAI,CAAC;QAChC,IAAImP,SAAS,KAAK,IAAI,EAAE;UACvB,OAAO3M,IAAI;QACZ;QACAxC,IAAI,GAAGmP,SAAS;QAChB,IAAIC,GAAG,GAAG,aAAapP,IAAI,UAAUwC,IAAI,GAAG;QAC5C,IAAID,KAAK,EAAE;UACV6M,GAAG,IAAI,WAAW7M,KAAK,GAAG;QAC3B;QACA6M,GAAG,IAAI,GAAG;QACV,OAAOA,GAAG;MACX;MACA5M,IAAIA,CAACG,KAAK,EAAE;QACX,OAAO,QAAQ,IAAIA,KAAK,IAAIA,KAAK,CAACE,MAAM,GAAG,IAAI,CAACqL,MAAM,CAACI,WAAW,CAAC3L,KAAK,CAACE,MAAM,CAAC,GAAGF,KAAK,CAACH,IAAI;MAC9F;IACD;;IAEA;AACD;AACA;AACA;IACC,MAAM8M,aAAa,CAAC;MACnB;MACAL,MAAMA,CAAC;QAAEzM;MAAK,CAAC,EAAE;QAChB,OAAOA,IAAI;MACZ;MACA0M,EAAEA,CAAC;QAAE1M;MAAK,CAAC,EAAE;QACZ,OAAOA,IAAI;MACZ;MACA0H,QAAQA,CAAC;QAAE1H;MAAK,CAAC,EAAE;QAClB,OAAOA,IAAI;MACZ;MACA8H,GAAGA,CAAC;QAAE9H;MAAK,CAAC,EAAE;QACb,OAAOA,IAAI;MACZ;MACAtD,IAAIA,CAAC;QAAEsD;MAAK,CAAC,EAAE;QACd,OAAOA,IAAI;MACZ;MACAA,IAAIA,CAAC;QAAEA;MAAK,CAAC,EAAE;QACd,OAAOA,IAAI;MACZ;MACAJ,IAAIA,CAAC;QAAEI;MAAK,CAAC,EAAE;QACd,OAAO,EAAE,GAAGA,IAAI;MACjB;MACA6M,KAAKA,CAAC;QAAE7M;MAAK,CAAC,EAAE;QACf,OAAO,EAAE,GAAGA,IAAI;MACjB;MACA6H,EAAEA,CAAA,EAAG;QACJ,OAAO,EAAE;MACV;IACD;;IAEA;AACD;AACA;IACC,MAAMkF,OAAO,CAAC;MACb/L,OAAO;MACPvF,QAAQ;MACRuR,YAAY;MACZ9L,WAAWA,CAACF,OAAO,EAAE;QACpB,IAAI,CAACA,OAAO,GAAGA,OAAO,IAAInG,OAAO,CAACgB,QAAQ;QAC1C,IAAI,CAACmF,OAAO,CAACvF,QAAQ,GAAG,IAAI,CAACuF,OAAO,CAACvF,QAAQ,IAAI,IAAIgQ,SAAS,CAAC,CAAC;QAChE,IAAI,CAAChQ,QAAQ,GAAG,IAAI,CAACuF,OAAO,CAACvF,QAAQ;QACrC,IAAI,CAACA,QAAQ,CAACuF,OAAO,GAAG,IAAI,CAACA,OAAO;QACpC,IAAI,CAACvF,QAAQ,CAACiQ,MAAM,GAAG,IAAI;QAC3B,IAAI,CAACsB,YAAY,GAAG,IAAIF,aAAa,CAAC,CAAC;MACxC;MACA;AACF;AACA;MACE,OAAOjB,KAAKA,CAACxL,MAAM,EAAEW,OAAO,EAAE;QAC7B,MAAM0K,MAAM,GAAG,IAAIqB,OAAO,CAAC/L,OAAO,CAAC;QACnC,OAAO0K,MAAM,CAACG,KAAK,CAACxL,MAAM,CAAC;MAC5B;MACA;AACF;AACA;MACE,OAAOyL,WAAWA,CAACzL,MAAM,EAAEW,OAAO,EAAE;QACnC,MAAM0K,MAAM,GAAG,IAAIqB,OAAO,CAAC/L,OAAO,CAAC;QACnC,OAAO0K,MAAM,CAACI,WAAW,CAACzL,MAAM,CAAC;MAClC;MACA;AACF;AACA;MACEwL,KAAKA,CAACxL,MAAM,EAAEkC,GAAG,GAAG,IAAI,EAAE;QACzB,IAAIqK,GAAG,GAAG,EAAE;QACZ,KAAK,IAAIrO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8B,MAAM,CAAC3B,MAAM,EAAEH,CAAC,EAAE,EAAE;UACvC,MAAM0O,QAAQ,GAAG5M,MAAM,CAAC9B,CAAC,CAAC;UAC1B;UACA,IAAI,IAAI,CAACyC,OAAO,CAAC3F,UAAU,IAAI,IAAI,CAAC2F,OAAO,CAAC3F,UAAU,CAAC6R,SAAS,IAAI,IAAI,CAAClM,OAAO,CAAC3F,UAAU,CAAC6R,SAAS,CAACD,QAAQ,CAAC7M,IAAI,CAAC,EAAE;YACrH,MAAM+M,YAAY,GAAGF,QAAQ;YAC7B,MAAMG,GAAG,GAAG,IAAI,CAACpM,OAAO,CAAC3F,UAAU,CAAC6R,SAAS,CAACC,YAAY,CAAC/M,IAAI,CAAC,CAACmK,IAAI,CAAC;cAAEmB,MAAM,EAAE;YAAK,CAAC,EAAEyB,YAAY,CAAC;YACrG,IAAIC,GAAG,KAAK,KAAK,IAAI,CAAC,CAAC,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC9B,QAAQ,CAAC6B,YAAY,CAAC/M,IAAI,CAAC,EAAE;cACjJwM,GAAG,IAAIQ,GAAG,IAAI,EAAE;cAChB;YACD;UACD;UACA,MAAMjN,KAAK,GAAG8M,QAAQ;UACtB,QAAQ9M,KAAK,CAACC,IAAI;YACjB,KAAK,OAAO;cAAE;gBACbwM,GAAG,IAAI,IAAI,CAACnR,QAAQ,CAAC0F,KAAK,CAAChB,KAAK,CAAC;gBACjC;cACD;YACA,KAAK,IAAI;cAAE;gBACVyM,GAAG,IAAI,IAAI,CAACnR,QAAQ,CAACuG,EAAE,CAAC7B,KAAK,CAAC;gBAC9B;cACD;YACA,KAAK,SAAS;cAAE;gBACfyM,GAAG,IAAI,IAAI,CAACnR,QAAQ,CAACoG,OAAO,CAAC1B,KAAK,CAAC;gBACnC;cACD;YACA,KAAK,MAAM;cAAE;gBACZyM,GAAG,IAAI,IAAI,CAACnR,QAAQ,CAAC8F,IAAI,CAACpB,KAAK,CAAC;gBAChC;cACD;YACA,KAAK,OAAO;cAAE;gBACbyM,GAAG,IAAI,IAAI,CAACnR,QAAQ,CAAC4J,KAAK,CAAClF,KAAK,CAAC;gBACjC;cACD;YACA,KAAK,YAAY;cAAE;gBAClByM,GAAG,IAAI,IAAI,CAACnR,QAAQ,CAACwG,UAAU,CAAC9B,KAAK,CAAC;gBACtC;cACD;YACA,KAAK,MAAM;cAAE;gBACZyM,GAAG,IAAI,IAAI,CAACnR,QAAQ,CAACqH,IAAI,CAAC3C,KAAK,CAAC;gBAChC;cACD;YACA,KAAK,MAAM;cAAE;gBACZyM,GAAG,IAAI,IAAI,CAACnR,QAAQ,CAACiB,IAAI,CAACyD,KAAK,CAAC;gBAChC;cACD;YACA,KAAK,WAAW;cAAE;gBACjByM,GAAG,IAAI,IAAI,CAACnR,QAAQ,CAACqK,SAAS,CAAC3F,KAAK,CAAC;gBACrC;cACD;YACA,KAAK,MAAM;cAAE;gBACZ,IAAIkN,SAAS,GAAGlN,KAAK;gBACrB,IAAIyL,IAAI,GAAG,IAAI,CAACnQ,QAAQ,CAACuE,IAAI,CAACqN,SAAS,CAAC;gBACxC,OAAO9O,CAAC,GAAG,CAAC,GAAG8B,MAAM,CAAC3B,MAAM,IAAI2B,MAAM,CAAC9B,CAAC,GAAG,CAAC,CAAC,CAAC6B,IAAI,KAAK,MAAM,EAAE;kBAC9DiN,SAAS,GAAGhN,MAAM,CAAC,EAAE9B,CAAC,CAAC;kBACvBqN,IAAI,IAAI,IAAI,GAAG,IAAI,CAACnQ,QAAQ,CAACuE,IAAI,CAACqN,SAAS,CAAC;gBAC7C;gBACA,IAAI9K,GAAG,EAAE;kBACRqK,GAAG,IAAI,IAAI,CAACnR,QAAQ,CAACqK,SAAS,CAAC;oBAC9B1F,IAAI,EAAE,WAAW;oBACjBP,GAAG,EAAE+L,IAAI;oBACT5L,IAAI,EAAE4L,IAAI;oBACVvL,MAAM,EAAE,CAAC;sBAAED,IAAI,EAAE,MAAM;sBAAEP,GAAG,EAAE+L,IAAI;sBAAE5L,IAAI,EAAE4L;oBAAK,CAAC;kBACjD,CAAC,CAAC;gBACH,CAAC,MACI;kBACJgB,GAAG,IAAIhB,IAAI;gBACZ;gBACA;cACD;YACA;cAAS;gBACR,MAAMb,MAAM,GAAG,cAAc,GAAG5K,KAAK,CAACC,IAAI,GAAG,uBAAuB;gBACpE,IAAI,IAAI,CAACY,OAAO,CAACtF,MAAM,EAAE;kBACxBuP,OAAO,CAACC,KAAK,CAACH,MAAM,CAAC;kBACrB,OAAO,EAAE;gBACV,CAAC,MACI;kBACJ,MAAM,IAAII,KAAK,CAACJ,MAAM,CAAC;gBACxB;cACD;UACD;QACD;QACA,OAAO6B,GAAG;MACX;MACA;AACF;AACA;MACEd,WAAWA,CAACzL,MAAM,EAAE5E,QAAQ,EAAE;QAC7BA,QAAQ,GAAGA,QAAQ,IAAI,IAAI,CAACA,QAAQ;QACpC,IAAImR,GAAG,GAAG,EAAE;QACZ,KAAK,IAAIrO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8B,MAAM,CAAC3B,MAAM,EAAEH,CAAC,EAAE,EAAE;UACvC,MAAM0O,QAAQ,GAAG5M,MAAM,CAAC9B,CAAC,CAAC;UAC1B;UACA,IAAI,IAAI,CAACyC,OAAO,CAAC3F,UAAU,IAAI,IAAI,CAAC2F,OAAO,CAAC3F,UAAU,CAAC6R,SAAS,IAAI,IAAI,CAAClM,OAAO,CAAC3F,UAAU,CAAC6R,SAAS,CAACD,QAAQ,CAAC7M,IAAI,CAAC,EAAE;YACrH,MAAMgN,GAAG,GAAG,IAAI,CAACpM,OAAO,CAAC3F,UAAU,CAAC6R,SAAS,CAACD,QAAQ,CAAC7M,IAAI,CAAC,CAACmK,IAAI,CAAC;cAAEmB,MAAM,EAAE;YAAK,CAAC,EAAEuB,QAAQ,CAAC;YAC7F,IAAIG,GAAG,KAAK,KAAK,IAAI,CAAC,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC9B,QAAQ,CAAC2B,QAAQ,CAAC7M,IAAI,CAAC,EAAE;cACnIwM,GAAG,IAAIQ,GAAG,IAAI,EAAE;cAChB;YACD;UACD;UACA,MAAMjN,KAAK,GAAG8M,QAAQ;UACtB,QAAQ9M,KAAK,CAACC,IAAI;YACjB,KAAK,QAAQ;cAAE;gBACdwM,GAAG,IAAInR,QAAQ,CAACuE,IAAI,CAACG,KAAK,CAAC;gBAC3B;cACD;YACA,KAAK,MAAM;cAAE;gBACZyM,GAAG,IAAInR,QAAQ,CAACiB,IAAI,CAACyD,KAAK,CAAC;gBAC3B;cACD;YACA,KAAK,MAAM;cAAE;gBACZyM,GAAG,IAAInR,QAAQ,CAACmE,IAAI,CAACO,KAAK,CAAC;gBAC3B;cACD;YACA,KAAK,OAAO;cAAE;gBACbyM,GAAG,IAAInR,QAAQ,CAACoR,KAAK,CAAC1M,KAAK,CAAC;gBAC5B;cACD;YACA,KAAK,QAAQ;cAAE;gBACdyM,GAAG,IAAInR,QAAQ,CAACgR,MAAM,CAACtM,KAAK,CAAC;gBAC7B;cACD;YACA,KAAK,IAAI;cAAE;gBACVyM,GAAG,IAAInR,QAAQ,CAACiR,EAAE,CAACvM,KAAK,CAAC;gBACzB;cACD;YACA,KAAK,UAAU;cAAE;gBAChByM,GAAG,IAAInR,QAAQ,CAACiM,QAAQ,CAACvH,KAAK,CAAC;gBAC/B;cACD;YACA,KAAK,IAAI;cAAE;gBACVyM,GAAG,IAAInR,QAAQ,CAACoM,EAAE,CAAC1H,KAAK,CAAC;gBACzB;cACD;YACA,KAAK,KAAK;cAAE;gBACXyM,GAAG,IAAInR,QAAQ,CAACqM,GAAG,CAAC3H,KAAK,CAAC;gBAC1B;cACD;YACA,KAAK,MAAM;cAAE;gBACZyM,GAAG,IAAInR,QAAQ,CAACuE,IAAI,CAACG,KAAK,CAAC;gBAC3B;cACD;YACA;cAAS;gBACR,MAAM4K,MAAM,GAAG,cAAc,GAAG5K,KAAK,CAACC,IAAI,GAAG,uBAAuB;gBACpE,IAAI,IAAI,CAACY,OAAO,CAACtF,MAAM,EAAE;kBACxBuP,OAAO,CAACC,KAAK,CAACH,MAAM,CAAC;kBACrB,OAAO,EAAE;gBACV,CAAC,MACI;kBACJ,MAAM,IAAII,KAAK,CAACJ,MAAM,CAAC;gBACxB;cACD;UACD;QACD;QACA,OAAO6B,GAAG;MACX;IACD;IAEA,MAAMU,MAAM,CAAC;MACZtM,OAAO;MACPE,WAAWA,CAACF,OAAO,EAAE;QACpB,IAAI,CAACA,OAAO,GAAGA,OAAO,IAAInG,OAAO,CAACgB,QAAQ;MAC3C;MACA,OAAO0R,gBAAgB,GAAG,IAAIC,GAAG,CAAC,CACjC,YAAY,EACZ,aAAa,EACb,kBAAkB,CAClB,CAAC;MACF;AACF;AACA;MACEC,UAAUA,CAACC,QAAQ,EAAE;QACpB,OAAOA,QAAQ;MAChB;MACA;AACF;AACA;MACEC,WAAWA,CAACjR,IAAI,EAAE;QACjB,OAAOA,IAAI;MACZ;MACA;AACF;AACA;MACEkR,gBAAgBA,CAACvN,MAAM,EAAE;QACxB,OAAOA,MAAM;MACd;IACD;IAEA,MAAMwN,MAAM,CAAC;MACZhS,QAAQ,GAAGX,YAAY,CAAC,CAAC;MACzB8F,OAAO,GAAG,IAAI,CAAC8M,UAAU;MACzBjC,KAAK,GAAG,IAAI,CAACkC,aAAa,CAACrE,MAAM,CAACI,GAAG,EAAEiD,OAAO,CAAClB,KAAK,CAAC;MACrDC,WAAW,GAAG,IAAI,CAACiC,aAAa,CAACrE,MAAM,CAACK,SAAS,EAAEgD,OAAO,CAACjB,WAAW,CAAC;MACvEkC,MAAM,GAAGjB,OAAO;MAChBkB,QAAQ,GAAGxC,SAAS;MACpByC,YAAY,GAAGpB,aAAa;MAC5BqB,KAAK,GAAGzE,MAAM;MACd0E,SAAS,GAAGrN,UAAU;MACtBsN,KAAK,GAAGf,MAAM;MACdpM,WAAWA,CAAC,GAAGoN,IAAI,EAAE;QACpB,IAAI,CAACC,GAAG,CAAC,GAAGD,IAAI,CAAC;MAClB;MACA;AACF;AACA;MACE1S,UAAUA,CAACyE,MAAM,EAAEmO,QAAQ,EAAE;QAC5B,IAAIC,MAAM,GAAG,EAAE;QACf,KAAK,MAAMtO,KAAK,IAAIE,MAAM,EAAE;UAC3BoO,MAAM,GAAGA,MAAM,CAACC,MAAM,CAACF,QAAQ,CAACjE,IAAI,CAAC,IAAI,EAAEpK,KAAK,CAAC,CAAC;UAClD,QAAQA,KAAK,CAACC,IAAI;YACjB,KAAK,OAAO;cAAE;gBACb,MAAMuO,UAAU,GAAGxO,KAAK;gBACxB,KAAK,MAAMyF,IAAI,IAAI+I,UAAU,CAACjJ,MAAM,EAAE;kBACrC+I,MAAM,GAAGA,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC9S,UAAU,CAACgK,IAAI,CAACvF,MAAM,EAAEmO,QAAQ,CAAC,CAAC;gBAC/D;gBACA,KAAK,MAAMzQ,GAAG,IAAI4Q,UAAU,CAACnJ,IAAI,EAAE;kBAClC,KAAK,MAAMI,IAAI,IAAI7H,GAAG,EAAE;oBACvB0Q,MAAM,GAAGA,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC9S,UAAU,CAACgK,IAAI,CAACvF,MAAM,EAAEmO,QAAQ,CAAC,CAAC;kBAC/D;gBACD;gBACA;cACD;YACA,KAAK,MAAM;cAAE;gBACZ,MAAMI,SAAS,GAAGzO,KAAK;gBACvBsO,MAAM,GAAGA,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC9S,UAAU,CAACgT,SAAS,CAACxL,KAAK,EAAEoL,QAAQ,CAAC,CAAC;gBAClE;cACD;YACA;cAAS;gBACR,MAAMrB,YAAY,GAAGhN,KAAK;gBAC1B,IAAI,IAAI,CAACtE,QAAQ,CAACR,UAAU,EAAEwT,WAAW,GAAG1B,YAAY,CAAC/M,IAAI,CAAC,EAAE;kBAC/D,IAAI,CAACvE,QAAQ,CAACR,UAAU,CAACwT,WAAW,CAAC1B,YAAY,CAAC/M,IAAI,CAAC,CAACyK,OAAO,CAAEgE,WAAW,IAAK;oBAChF,MAAMxO,MAAM,GAAG8M,YAAY,CAAC0B,WAAW,CAAC,CAACC,IAAI,CAACpE,QAAQ,CAAC;oBACvD+D,MAAM,GAAGA,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC9S,UAAU,CAACyE,MAAM,EAAEmO,QAAQ,CAAC,CAAC;kBAC1D,CAAC,CAAC;gBACH,CAAC,MACI,IAAIrB,YAAY,CAAC9M,MAAM,EAAE;kBAC7BoO,MAAM,GAAGA,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC9S,UAAU,CAACuR,YAAY,CAAC9M,MAAM,EAAEmO,QAAQ,CAAC,CAAC;gBACvE;cACD;UACD;QACD;QACA,OAAOC,MAAM;MACd;MACAF,GAAGA,CAAC,GAAGD,IAAI,EAAE;QACZ,MAAMjT,UAAU,GAAG,IAAI,CAACQ,QAAQ,CAACR,UAAU,IAAI;UAAE6R,SAAS,EAAE,CAAC,CAAC;UAAE2B,WAAW,EAAE,CAAC;QAAE,CAAC;QACjFP,IAAI,CAACzD,OAAO,CAAEkE,IAAI,IAAK;UACtB;UACA,MAAMC,IAAI,GAAG;YAAE,GAAGD;UAAK,CAAC;UACxB;UACAC,IAAI,CAAC7T,KAAK,GAAG,IAAI,CAACU,QAAQ,CAACV,KAAK,IAAI6T,IAAI,CAAC7T,KAAK,IAAI,KAAK;UACvD;UACA,IAAI4T,IAAI,CAAC1T,UAAU,EAAE;YACpB0T,IAAI,CAAC1T,UAAU,CAACwP,OAAO,CAAEoE,GAAG,IAAK;cAChC,IAAI,CAACA,GAAG,CAAC9R,IAAI,EAAE;gBACd,MAAM,IAAIgO,KAAK,CAAC,yBAAyB,CAAC;cAC3C;cACA,IAAI,UAAU,IAAI8D,GAAG,EAAE;gBAAE;gBACxB,MAAMC,YAAY,GAAG7T,UAAU,CAAC6R,SAAS,CAAC+B,GAAG,CAAC9R,IAAI,CAAC;gBACnD,IAAI+R,YAAY,EAAE;kBACjB;kBACA7T,UAAU,CAAC6R,SAAS,CAAC+B,GAAG,CAAC9R,IAAI,CAAC,GAAG,UAAU,GAAGmR,IAAI,EAAE;oBACnD,IAAIlB,GAAG,GAAG6B,GAAG,CAACxT,QAAQ,CAAC0T,KAAK,CAAC,IAAI,EAAEb,IAAI,CAAC;oBACxC,IAAIlB,GAAG,KAAK,KAAK,EAAE;sBAClBA,GAAG,GAAG8B,YAAY,CAACC,KAAK,CAAC,IAAI,EAAEb,IAAI,CAAC;oBACrC;oBACA,OAAOlB,GAAG;kBACX,CAAC;gBACF,CAAC,MACI;kBACJ/R,UAAU,CAAC6R,SAAS,CAAC+B,GAAG,CAAC9R,IAAI,CAAC,GAAG8R,GAAG,CAACxT,QAAQ;gBAC9C;cACD;cACA,IAAI,WAAW,IAAIwT,GAAG,EAAE;gBAAE;gBACzB,IAAI,CAACA,GAAG,CAACxP,KAAK,IAAKwP,GAAG,CAACxP,KAAK,KAAK,OAAO,IAAIwP,GAAG,CAACxP,KAAK,KAAK,QAAS,EAAE;kBACpE,MAAM,IAAI0L,KAAK,CAAC,6CAA6C,CAAC;gBAC/D;gBACA,MAAMiE,QAAQ,GAAG/T,UAAU,CAAC4T,GAAG,CAACxP,KAAK,CAAC;gBACtC,IAAI2P,QAAQ,EAAE;kBACbA,QAAQ,CAAChD,OAAO,CAAC6C,GAAG,CAACtT,SAAS,CAAC;gBAChC,CAAC,MACI;kBACJN,UAAU,CAAC4T,GAAG,CAACxP,KAAK,CAAC,GAAG,CAACwP,GAAG,CAACtT,SAAS,CAAC;gBACxC;gBACA,IAAIsT,GAAG,CAAC/L,KAAK,EAAE;kBAAE;kBAChB,IAAI+L,GAAG,CAACxP,KAAK,KAAK,OAAO,EAAE;oBAC1B,IAAIpE,UAAU,CAACmP,UAAU,EAAE;sBAC1BnP,UAAU,CAACmP,UAAU,CAAC3L,IAAI,CAACoQ,GAAG,CAAC/L,KAAK,CAAC;oBACtC,CAAC,MACI;sBACJ7H,UAAU,CAACmP,UAAU,GAAG,CAACyE,GAAG,CAAC/L,KAAK,CAAC;oBACpC;kBACD,CAAC,MACI,IAAI+L,GAAG,CAACxP,KAAK,KAAK,QAAQ,EAAE;oBAChC,IAAIpE,UAAU,CAACmQ,WAAW,EAAE;sBAC3BnQ,UAAU,CAACmQ,WAAW,CAAC3M,IAAI,CAACoQ,GAAG,CAAC/L,KAAK,CAAC;oBACvC,CAAC,MACI;sBACJ7H,UAAU,CAACmQ,WAAW,GAAG,CAACyD,GAAG,CAAC/L,KAAK,CAAC;oBACrC;kBACD;gBACD;cACD;cACA,IAAI,aAAa,IAAI+L,GAAG,IAAIA,GAAG,CAACJ,WAAW,EAAE;gBAAE;gBAC9CxT,UAAU,CAACwT,WAAW,CAACI,GAAG,CAAC9R,IAAI,CAAC,GAAG8R,GAAG,CAACJ,WAAW;cACnD;YACD,CAAC,CAAC;YACFG,IAAI,CAAC3T,UAAU,GAAGA,UAAU;UAC7B;UACA;UACA,IAAI0T,IAAI,CAACtT,QAAQ,EAAE;YAClB,MAAMA,QAAQ,GAAG,IAAI,CAACI,QAAQ,CAACJ,QAAQ,IAAI,IAAIgQ,SAAS,CAAC,IAAI,CAAC5P,QAAQ,CAAC;YACvE,KAAK,MAAMwT,IAAI,IAAIN,IAAI,CAACtT,QAAQ,EAAE;cACjC,IAAI,EAAE4T,IAAI,IAAI5T,QAAQ,CAAC,EAAE;gBACxB,MAAM,IAAI0P,KAAK,CAAC,aAAakE,IAAI,kBAAkB,CAAC;cACrD;cACA,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC/D,QAAQ,CAAC+D,IAAI,CAAC,EAAE;gBACzC;gBACA;cACD;cACA,MAAMC,YAAY,GAAGD,IAAI;cACzB,MAAME,YAAY,GAAGR,IAAI,CAACtT,QAAQ,CAAC6T,YAAY,CAAC;cAChD,MAAMJ,YAAY,GAAGzT,QAAQ,CAAC6T,YAAY,CAAC;cAC3C;cACA7T,QAAQ,CAAC6T,YAAY,CAAC,GAAG,CAAC,GAAGhB,IAAI,KAAK;gBACrC,IAAIlB,GAAG,GAAGmC,YAAY,CAACJ,KAAK,CAAC1T,QAAQ,EAAE6S,IAAI,CAAC;gBAC5C,IAAIlB,GAAG,KAAK,KAAK,EAAE;kBAClBA,GAAG,GAAG8B,YAAY,CAACC,KAAK,CAAC1T,QAAQ,EAAE6S,IAAI,CAAC;gBACzC;gBACA,OAAOlB,GAAG,IAAI,EAAE;cACjB,CAAC;YACF;YACA4B,IAAI,CAACvT,QAAQ,GAAGA,QAAQ;UACzB;UACA,IAAIsT,IAAI,CAACpT,SAAS,EAAE;YACnB,MAAMA,SAAS,GAAG,IAAI,CAACE,QAAQ,CAACF,SAAS,IAAI,IAAIoF,UAAU,CAAC,IAAI,CAAClF,QAAQ,CAAC;YAC1E,KAAK,MAAMwT,IAAI,IAAIN,IAAI,CAACpT,SAAS,EAAE;cAClC,IAAI,EAAE0T,IAAI,IAAI1T,SAAS,CAAC,EAAE;gBACzB,MAAM,IAAIwP,KAAK,CAAC,cAAckE,IAAI,kBAAkB,CAAC;cACtD;cACA,IAAI,CAAC,SAAS,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC/D,QAAQ,CAAC+D,IAAI,CAAC,EAAE;gBACjD;gBACA;cACD;cACA,MAAMG,aAAa,GAAGH,IAAI;cAC1B,MAAMI,aAAa,GAAGV,IAAI,CAACpT,SAAS,CAAC6T,aAAa,CAAC;cACnD,MAAME,aAAa,GAAG/T,SAAS,CAAC6T,aAAa,CAAC;cAC9C;cACA;cACA7T,SAAS,CAAC6T,aAAa,CAAC,GAAG,CAAC,GAAGlB,IAAI,KAAK;gBACvC,IAAIlB,GAAG,GAAGqC,aAAa,CAACN,KAAK,CAACxT,SAAS,EAAE2S,IAAI,CAAC;gBAC9C,IAAIlB,GAAG,KAAK,KAAK,EAAE;kBAClBA,GAAG,GAAGsC,aAAa,CAACP,KAAK,CAACxT,SAAS,EAAE2S,IAAI,CAAC;gBAC3C;gBACA,OAAOlB,GAAG;cACX,CAAC;YACF;YACA4B,IAAI,CAACrT,SAAS,GAAGA,SAAS;UAC3B;UACA;UACA,IAAIoT,IAAI,CAACxT,KAAK,EAAE;YACf,MAAMA,KAAK,GAAG,IAAI,CAACM,QAAQ,CAACN,KAAK,IAAI,IAAI+R,MAAM,CAAC,CAAC;YACjD,KAAK,MAAM+B,IAAI,IAAIN,IAAI,CAACxT,KAAK,EAAE;cAC9B,IAAI,EAAE8T,IAAI,IAAI9T,KAAK,CAAC,EAAE;gBACrB,MAAM,IAAI4P,KAAK,CAAC,SAASkE,IAAI,kBAAkB,CAAC;cACjD;cACA,IAAIA,IAAI,KAAK,SAAS,EAAE;gBACvB;gBACA;cACD;cACA,MAAMM,SAAS,GAAGN,IAAI;cACtB,MAAMO,SAAS,GAAGb,IAAI,CAACxT,KAAK,CAACoU,SAAS,CAAC;cACvC,MAAME,QAAQ,GAAGtU,KAAK,CAACoU,SAAS,CAAC;cACjC,IAAIrC,MAAM,CAACC,gBAAgB,CAACuC,GAAG,CAACT,IAAI,CAAC,EAAE;gBACtC;gBACA9T,KAAK,CAACoU,SAAS,CAAC,GAAII,GAAG,IAAK;kBAC3B,IAAI,IAAI,CAAClU,QAAQ,CAACV,KAAK,EAAE;oBACxB,OAAO6U,OAAO,CAACC,OAAO,CAACL,SAAS,CAACrF,IAAI,CAAChP,KAAK,EAAEwU,GAAG,CAAC,CAAC,CAACG,IAAI,CAAC9C,GAAG,IAAI;sBAC9D,OAAOyC,QAAQ,CAACtF,IAAI,CAAChP,KAAK,EAAE6R,GAAG,CAAC;oBACjC,CAAC,CAAC;kBACH;kBACA,MAAMA,GAAG,GAAGwC,SAAS,CAACrF,IAAI,CAAChP,KAAK,EAAEwU,GAAG,CAAC;kBACtC,OAAOF,QAAQ,CAACtF,IAAI,CAAChP,KAAK,EAAE6R,GAAG,CAAC;gBACjC,CAAC;cACF,CAAC,MACI;gBACJ;gBACA7R,KAAK,CAACoU,SAAS,CAAC,GAAG,CAAC,GAAGrB,IAAI,KAAK;kBAC/B,IAAIlB,GAAG,GAAGwC,SAAS,CAACT,KAAK,CAAC5T,KAAK,EAAE+S,IAAI,CAAC;kBACtC,IAAIlB,GAAG,KAAK,KAAK,EAAE;oBAClBA,GAAG,GAAGyC,QAAQ,CAACV,KAAK,CAAC5T,KAAK,EAAE+S,IAAI,CAAC;kBAClC;kBACA,OAAOlB,GAAG;gBACX,CAAC;cACF;YACD;YACA4B,IAAI,CAACzT,KAAK,GAAGA,KAAK;UACnB;UACA;UACA,IAAIwT,IAAI,CAACnT,UAAU,EAAE;YACpB,MAAMA,UAAU,GAAG,IAAI,CAACC,QAAQ,CAACD,UAAU;YAC3C,MAAMuU,cAAc,GAAGpB,IAAI,CAACnT,UAAU;YACtCoT,IAAI,CAACpT,UAAU,GAAG,UAAUuE,KAAK,EAAE;cAClC,IAAIsO,MAAM,GAAG,EAAE;cACfA,MAAM,CAAC5P,IAAI,CAACsR,cAAc,CAAC5F,IAAI,CAAC,IAAI,EAAEpK,KAAK,CAAC,CAAC;cAC7C,IAAIvE,UAAU,EAAE;gBACf6S,MAAM,GAAGA,MAAM,CAACC,MAAM,CAAC9S,UAAU,CAAC2O,IAAI,CAAC,IAAI,EAAEpK,KAAK,CAAC,CAAC;cACrD;cACA,OAAOsO,MAAM;YACd,CAAC;UACF;UACA,IAAI,CAAC5S,QAAQ,GAAG;YAAE,GAAG,IAAI,CAACA,QAAQ;YAAE,GAAGmT;UAAK,CAAC;QAC9C,CAAC,CAAC;QACF,OAAO,IAAI;MACZ;MACAlB,UAAUA,CAAC7Q,GAAG,EAAE;QACf,IAAI,CAACpB,QAAQ,GAAG;UAAE,GAAG,IAAI,CAACA,QAAQ;UAAE,GAAGoB;QAAI,CAAC;QAC5C,OAAO,IAAI;MACZ;MACA6C,KAAKA,CAACsB,GAAG,EAAEJ,OAAO,EAAE;QACnB,OAAO0I,MAAM,CAACI,GAAG,CAAC1I,GAAG,EAAEJ,OAAO,IAAI,IAAI,CAACnF,QAAQ,CAAC;MACjD;MACA6P,MAAMA,CAACrL,MAAM,EAAEW,OAAO,EAAE;QACvB,OAAO+L,OAAO,CAAClB,KAAK,CAACxL,MAAM,EAAEW,OAAO,IAAI,IAAI,CAACnF,QAAQ,CAAC;MACvD;MACAkS,aAAaA,CAACjO,KAAK,EAAE4L,MAAM,EAAE;QAC5B;QACA,MAAMG,KAAK,GAAGA,CAACzK,GAAG,EAAEJ,OAAO,KAAK;UAC/B,MAAMoP,OAAO,GAAG;YAAE,GAAGpP;UAAQ,CAAC;UAC9B,MAAM/D,GAAG,GAAG;YAAE,GAAG,IAAI,CAACpB,QAAQ;YAAE,GAAGuU;UAAQ,CAAC;UAC5C,MAAMC,UAAU,GAAG,IAAI,CAACC,OAAO,CAAC,CAAC,CAACrT,GAAG,CAACvB,MAAM,EAAE,CAAC,CAACuB,GAAG,CAAC9B,KAAK,CAAC;UAC1D;UACA,IAAI,IAAI,CAACU,QAAQ,CAACV,KAAK,KAAK,IAAI,IAAIiV,OAAO,CAACjV,KAAK,KAAK,KAAK,EAAE;YAC5D,OAAOkV,UAAU,CAAC,IAAIlF,KAAK,CAAC,oIAAoI,CAAC,CAAC;UACnK;UACA;UACA,IAAI,OAAO/J,GAAG,KAAK,WAAW,IAAIA,GAAG,KAAK,IAAI,EAAE;YAC/C,OAAOiP,UAAU,CAAC,IAAIlF,KAAK,CAAC,gDAAgD,CAAC,CAAC;UAC/E;UACA,IAAI,OAAO/J,GAAG,KAAK,QAAQ,EAAE;YAC5B,OAAOiP,UAAU,CAAC,IAAIlF,KAAK,CAAC,uCAAuC,GAChEvB,MAAM,CAAC2G,SAAS,CAACC,QAAQ,CAACjG,IAAI,CAACnJ,GAAG,CAAC,GAAG,mBAAmB,CAAC,CAAC;UAC/D;UACA,IAAInE,GAAG,CAAC1B,KAAK,EAAE;YACd0B,GAAG,CAAC1B,KAAK,CAACyF,OAAO,GAAG/D,GAAG;UACxB;UACA,IAAIA,GAAG,CAAC9B,KAAK,EAAE;YACd,OAAO6U,OAAO,CAACC,OAAO,CAAChT,GAAG,CAAC1B,KAAK,GAAG0B,GAAG,CAAC1B,KAAK,CAACkS,UAAU,CAACrM,GAAG,CAAC,GAAGA,GAAG,CAAC,CACjE8O,IAAI,CAAC9O,GAAG,IAAItB,KAAK,CAACsB,GAAG,EAAEnE,GAAG,CAAC,CAAC,CAC5BiT,IAAI,CAAC7P,MAAM,IAAIpD,GAAG,CAAC1B,KAAK,GAAG0B,GAAG,CAAC1B,KAAK,CAACqS,gBAAgB,CAACvN,MAAM,CAAC,GAAGA,MAAM,CAAC,CACvE6P,IAAI,CAAC7P,MAAM,IAAIpD,GAAG,CAACrB,UAAU,GAAGoU,OAAO,CAACS,GAAG,CAAC,IAAI,CAAC7U,UAAU,CAACyE,MAAM,EAAEpD,GAAG,CAACrB,UAAU,CAAC,CAAC,CAACsU,IAAI,CAAC,MAAM7P,MAAM,CAAC,GAAGA,MAAM,CAAC,CACjH6P,IAAI,CAAC7P,MAAM,IAAIqL,MAAM,CAACrL,MAAM,EAAEpD,GAAG,CAAC,CAAC,CACnCiT,IAAI,CAACxT,IAAI,IAAIO,GAAG,CAAC1B,KAAK,GAAG0B,GAAG,CAAC1B,KAAK,CAACoS,WAAW,CAACjR,IAAI,CAAC,GAAGA,IAAI,CAAC,CAC5DgU,KAAK,CAACL,UAAU,CAAC;UACpB;UACA,IAAI;YACH,IAAIpT,GAAG,CAAC1B,KAAK,EAAE;cACd6F,GAAG,GAAGnE,GAAG,CAAC1B,KAAK,CAACkS,UAAU,CAACrM,GAAG,CAAC;YAChC;YACA,IAAIf,MAAM,GAAGP,KAAK,CAACsB,GAAG,EAAEnE,GAAG,CAAC;YAC5B,IAAIA,GAAG,CAAC1B,KAAK,EAAE;cACd8E,MAAM,GAAGpD,GAAG,CAAC1B,KAAK,CAACqS,gBAAgB,CAACvN,MAAM,CAAC;YAC5C;YACA,IAAIpD,GAAG,CAACrB,UAAU,EAAE;cACnB,IAAI,CAACA,UAAU,CAACyE,MAAM,EAAEpD,GAAG,CAACrB,UAAU,CAAC;YACxC;YACA,IAAIc,IAAI,GAAGgP,MAAM,CAACrL,MAAM,EAAEpD,GAAG,CAAC;YAC9B,IAAIA,GAAG,CAAC1B,KAAK,EAAE;cACdmB,IAAI,GAAGO,GAAG,CAAC1B,KAAK,CAACoS,WAAW,CAACjR,IAAI,CAAC;YACnC;YACA,OAAOA,IAAI;UACZ,CAAC,CACD,OAAOiU,CAAC,EAAE;YACT,OAAON,UAAU,CAACM,CAAC,CAAC;UACrB;QACD,CAAC;QACD,OAAO9E,KAAK;MACb;MACAyE,OAAOA,CAAC5U,MAAM,EAAEP,KAAK,EAAE;QACtB,OAAQwV,CAAC,IAAK;UACbA,CAAC,CAACC,OAAO,IAAI,6DAA6D;UAC1E,IAAIlV,MAAM,EAAE;YACX,MAAMmV,GAAG,GAAG,gCAAgC,GACzCpU,QAAQ,CAACkU,CAAC,CAACC,OAAO,GAAG,EAAE,EAAE,IAAI,CAAC,GAC9B,QAAQ;YACX,IAAIzV,KAAK,EAAE;cACV,OAAO6U,OAAO,CAACC,OAAO,CAACY,GAAG,CAAC;YAC5B;YACA,OAAOA,GAAG;UACX;UACA,IAAI1V,KAAK,EAAE;YACV,OAAO6U,OAAO,CAACc,MAAM,CAACH,CAAC,CAAC;UACzB;UACA,MAAMA,CAAC;QACR,CAAC;MACF;IACD;IAEA,MAAMI,cAAc,GAAG,IAAIlD,MAAM,CAAC,CAAC;IACnC,SAAS5S,MAAMA,CAACmG,GAAG,EAAEnE,GAAG,EAAE;MACzB,OAAO8T,cAAc,CAAClF,KAAK,CAACzK,GAAG,EAAEnE,GAAG,CAAC;IACtC;IACA;AACD;AACA;AACA;AACA;IACChC,MAAM,CAAC+F,OAAO,GACb/F,MAAM,CAAC6S,UAAU,GAAG,UAAU9M,OAAO,EAAE;MACtC+P,cAAc,CAACjD,UAAU,CAAC9M,OAAO,CAAC;MAClC/F,MAAM,CAACY,QAAQ,GAAGkV,cAAc,CAAClV,QAAQ;MACzCC,cAAc,CAACb,MAAM,CAACY,QAAQ,CAAC;MAC/B,OAAOZ,MAAM;IACd,CAAC;IACF;AACD;AACA;IACCA,MAAM,CAAC+V,WAAW,GAAG9V,YAAY;IACjCD,MAAM,CAACY,QAAQ,GAAGhB,OAAO,CAACgB,QAAQ;IAClC;AACD;AACA;IACCZ,MAAM,CAACsT,GAAG,GAAG,UAAU,GAAGD,IAAI,EAAE;MAC/ByC,cAAc,CAACxC,GAAG,CAAC,GAAGD,IAAI,CAAC;MAC3BrT,MAAM,CAACY,QAAQ,GAAGkV,cAAc,CAAClV,QAAQ;MACzCC,cAAc,CAACb,MAAM,CAACY,QAAQ,CAAC;MAC/B,OAAOZ,MAAM;IACd,CAAC;IACD;AACD;AACA;IACCA,MAAM,CAACW,UAAU,GAAG,UAAUyE,MAAM,EAAEmO,QAAQ,EAAE;MAC/C,OAAOuC,cAAc,CAACnV,UAAU,CAACyE,MAAM,EAAEmO,QAAQ,CAAC;IACnD,CAAC;IACD;AACD;AACA;AACA;AACA;AACA;AACA;IACCvT,MAAM,CAAC6Q,WAAW,GAAGiF,cAAc,CAACjF,WAAW;IAC/C;AACD;AACA;IACC7Q,MAAM,CAAC+S,MAAM,GAAGjB,OAAO;IACvB9R,MAAM,CAACyQ,MAAM,GAAGqB,OAAO,CAAClB,KAAK;IAC7B5Q,MAAM,CAACgT,QAAQ,GAAGxC,SAAS;IAC3BxQ,MAAM,CAACiT,YAAY,GAAGpB,aAAa;IACnC7R,MAAM,CAACkT,KAAK,GAAGzE,MAAM;IACrBzO,MAAM,CAAC6E,KAAK,GAAG4J,MAAM,CAACI,GAAG;IACzB7O,MAAM,CAACmT,SAAS,GAAGrN,UAAU;IAC7B9F,MAAM,CAACoT,KAAK,GAAGf,MAAM;IACrBrS,MAAM,CAAC4Q,KAAK,GAAG5Q,MAAM;IACrB,MAAM+F,OAAO,GAAG/F,MAAM,CAAC+F,OAAO;IAC9B,MAAM8M,UAAU,GAAG7S,MAAM,CAAC6S,UAAU;IACpC,MAAMS,GAAG,GAAGtT,MAAM,CAACsT,GAAG;IACtB,MAAM3S,UAAU,GAAGX,MAAM,CAACW,UAAU;IACpC,MAAMkQ,WAAW,GAAG7Q,MAAM,CAAC6Q,WAAW;IACtC,MAAMD,KAAK,GAAG5Q,MAAM;IACpB,MAAMyQ,MAAM,GAAGqB,OAAO,CAAClB,KAAK;IAC5B,MAAM/L,KAAK,GAAG4J,MAAM,CAACI,GAAG;IAExBjP,OAAO,CAACwT,KAAK,GAAGf,MAAM;IACtBzS,OAAO,CAACsT,KAAK,GAAGzE,MAAM;IACtB7O,OAAO,CAACgT,MAAM,GAAGA,MAAM;IACvBhT,OAAO,CAACmT,MAAM,GAAGjB,OAAO;IACxBlS,OAAO,CAACoT,QAAQ,GAAGxC,SAAS;IAC5B5Q,OAAO,CAACqT,YAAY,GAAGpB,aAAa;IACpCjS,OAAO,CAACuT,SAAS,GAAGrN,UAAU;IAC9BlG,OAAO,CAACmW,WAAW,GAAG9V,YAAY;IAClCL,OAAO,CAACiF,KAAK,GAAGA,KAAK;IACrBjF,OAAO,CAACI,MAAM,GAAGA,MAAM;IACvBJ,OAAO,CAACmG,OAAO,GAAGA,OAAO;IACzBnG,OAAO,CAACgR,KAAK,GAAGA,KAAK;IACrBhR,OAAO,CAACiR,WAAW,GAAGA,WAAW;IACjCjR,OAAO,CAAC6Q,MAAM,GAAGA,MAAM;IACvB7Q,OAAO,CAACiT,UAAU,GAAGA,UAAU;IAC/BjT,OAAO,CAAC0T,GAAG,GAAGA,GAAG;IACjB1T,OAAO,CAACe,UAAU,GAAGA,UAAU;EAChC,CAAE,CAAC;;EAEH;AACA,CAAC,EAAE,CAAC;AACJ,OAAO,IAAIyS,KAAK,GAAI9T,gBAAgB,CAAC8T,KAAK,IAAIxT,OAAO,CAACwT,KAAM;AAC5D,OAAO,IAAIF,KAAK,GAAI5T,gBAAgB,CAAC4T,KAAK,IAAItT,OAAO,CAACsT,KAAM;AAC5D,OAAO,IAAIN,MAAM,GAAItT,gBAAgB,CAACsT,MAAM,IAAIhT,OAAO,CAACgT,MAAO;AAC/D,OAAO,IAAIG,MAAM,GAAIzT,gBAAgB,CAACyT,MAAM,IAAInT,OAAO,CAACmT,MAAO;AAC/D,OAAO,IAAIC,QAAQ,GAAI1T,gBAAgB,CAAC0T,QAAQ,IAAIpT,OAAO,CAACoT,QAAS;AACrE,OAAO,IAAIC,YAAY,GAAI3T,gBAAgB,CAAC2T,YAAY,IAAIrT,OAAO,CAACqT,YAAa;AACjF,OAAO,IAAIE,SAAS,GAAI7T,gBAAgB,CAAC6T,SAAS,IAAIvT,OAAO,CAACuT,SAAU;AACxE,OAAO,IAAIvS,QAAQ,GAAItB,gBAAgB,CAACsB,QAAQ,IAAIhB,OAAO,CAACgB,QAAS;AACrE,OAAO,IAAImV,WAAW,GAAIzW,gBAAgB,CAACyW,WAAW,IAAInW,OAAO,CAACmW,WAAY;AAC9E,OAAO,IAAIlR,KAAK,GAAIvF,gBAAgB,CAACuF,KAAK,IAAIjF,OAAO,CAACiF,KAAM;AAC5D,OAAO,IAAI7E,MAAM,GAAIV,gBAAgB,CAACU,MAAM,IAAIJ,OAAO,CAACI,MAAO;AAC/D,OAAO,IAAI+F,OAAO,GAAIzG,gBAAgB,CAACyG,OAAO,IAAInG,OAAO,CAACmG,OAAQ;AAClE,OAAO,IAAI6K,KAAK,GAAItR,gBAAgB,CAACsR,KAAK,IAAIhR,OAAO,CAACgR,KAAM;AAC5D,OAAO,IAAIC,WAAW,GAAIvR,gBAAgB,CAACuR,WAAW,IAAIjR,OAAO,CAACiR,WAAY;AAC9E,OAAO,IAAIJ,MAAM,GAAInR,gBAAgB,CAACmR,MAAM,IAAI7Q,OAAO,CAAC6Q,MAAO;AAC/D,OAAO,IAAIoC,UAAU,GAAIvT,gBAAgB,CAACuT,UAAU,IAAIjT,OAAO,CAACiT,UAAW;AAC3E,OAAO,IAAIS,GAAG,GAAIhU,gBAAgB,CAACgU,GAAG,IAAI1T,OAAO,CAAC0T,GAAI;AACtD,OAAO,IAAI3S,UAAU,GAAIrB,gBAAgB,CAACqB,UAAU,IAAIf,OAAO,CAACe,UAAW;AAC3E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}