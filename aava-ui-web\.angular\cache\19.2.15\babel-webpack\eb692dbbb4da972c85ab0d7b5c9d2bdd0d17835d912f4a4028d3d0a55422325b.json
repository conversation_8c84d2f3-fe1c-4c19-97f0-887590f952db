{"ast": null, "code": "import _asyncToGenerator from \"C:/console/aava-ui-web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = this && this.__param || function (paramIndex, decorator) {\n  return function (target, key) {\n    decorator(target, key, paramIndex);\n  };\n};\nimport { RunOnceScheduler } from '../../../../base/common/async.js';\nimport { CancellationTokenSource } from '../../../../base/common/cancellation.js';\nimport { Disposable, toDisposable } from '../../../../base/common/lifecycle.js';\nimport { autorun, autorunWithStore, derived, observableSignal, observableSignalFromEvent, observableValue, transaction, waitForState } from '../../../../base/common/observable.js';\nimport { IDiffProviderFactoryService } from './diffProviderFactoryService.js';\nimport { filterWithPrevious } from './utils.js';\nimport { readHotReloadableExport } from '../../../../base/common/hotReloadHelpers.js';\nimport { LineRange, LineRangeSet } from '../../../common/core/lineRange.js';\nimport { DefaultLinesDiffComputer } from '../../../common/diff/defaultLinesDiffComputer/defaultLinesDiffComputer.js';\nimport { DetailedLineRangeMapping, LineRangeMapping, RangeMapping } from '../../../common/diff/rangeMapping.js';\nimport { TextEditInfo } from '../../../common/model/bracketPairsTextModelPart/bracketPairsTree/beforeEditPositionMapper.js';\nimport { combineTextEditInfos } from '../../../common/model/bracketPairsTextModelPart/bracketPairsTree/combineTextEditInfos.js';\nimport { optimizeSequenceDiffs } from '../../../common/diff/defaultLinesDiffComputer/heuristicSequenceOptimizations.js';\nimport { isDefined } from '../../../../base/common/types.js';\nimport { groupAdjacentBy } from '../../../../base/common/arrays.js';\nimport { softAssert } from '../../../../base/common/assert.js';\nlet DiffEditorViewModel = class DiffEditorViewModel extends Disposable {\n  setActiveMovedText(movedText) {\n    this._activeMovedText.set(movedText, undefined);\n  }\n  constructor(model, _options, _diffProviderFactoryService) {\n    var _this;\n    super();\n    _this = this;\n    this.model = model;\n    this._options = _options;\n    this._diffProviderFactoryService = _diffProviderFactoryService;\n    this._isDiffUpToDate = observableValue(this, false);\n    this.isDiffUpToDate = this._isDiffUpToDate;\n    this._diff = observableValue(this, undefined);\n    this.diff = this._diff;\n    this._unchangedRegions = observableValue(this, undefined);\n    this.unchangedRegions = derived(this, r => {\n      if (this._options.hideUnchangedRegions.read(r)) {\n        return this._unchangedRegions.read(r)?.regions ?? [];\n      } else {\n        // Reset state\n        transaction(tx => {\n          for (const r of this._unchangedRegions.get()?.regions || []) {\n            r.collapseAll(tx);\n          }\n        });\n        return [];\n      }\n    });\n    this.movedTextToCompare = observableValue(this, undefined);\n    this._activeMovedText = observableValue(this, undefined);\n    this._hoveredMovedText = observableValue(this, undefined);\n    this.activeMovedText = derived(this, r => this.movedTextToCompare.read(r) ?? this._hoveredMovedText.read(r) ?? this._activeMovedText.read(r));\n    this._cancellationTokenSource = new CancellationTokenSource();\n    this._diffProvider = derived(this, reader => {\n      const diffProvider = this._diffProviderFactoryService.createDiffProvider({\n        diffAlgorithm: this._options.diffAlgorithm.read(reader)\n      });\n      const onChangeSignal = observableSignalFromEvent('onDidChange', diffProvider.onDidChange);\n      return {\n        diffProvider,\n        onChangeSignal\n      };\n    });\n    this._register(toDisposable(() => this._cancellationTokenSource.cancel()));\n    const contentChangedSignal = observableSignal('contentChangedSignal');\n    const debouncer = this._register(new RunOnceScheduler(() => contentChangedSignal.trigger(undefined), 200));\n    this._register(autorun(reader => {\n      /** @description collapse touching unchanged ranges */\n      const lastUnchangedRegions = this._unchangedRegions.read(reader);\n      if (!lastUnchangedRegions || lastUnchangedRegions.regions.some(r => r.isDragged.read(reader))) {\n        return;\n      }\n      const lastUnchangedRegionsOrigRanges = lastUnchangedRegions.originalDecorationIds.map(id => model.original.getDecorationRange(id)).map(r => r ? LineRange.fromRangeInclusive(r) : undefined);\n      const lastUnchangedRegionsModRanges = lastUnchangedRegions.modifiedDecorationIds.map(id => model.modified.getDecorationRange(id)).map(r => r ? LineRange.fromRangeInclusive(r) : undefined);\n      const updatedLastUnchangedRegions = lastUnchangedRegions.regions.map((r, idx) => !lastUnchangedRegionsOrigRanges[idx] || !lastUnchangedRegionsModRanges[idx] ? undefined : new UnchangedRegion(lastUnchangedRegionsOrigRanges[idx].startLineNumber, lastUnchangedRegionsModRanges[idx].startLineNumber, lastUnchangedRegionsOrigRanges[idx].length, r.visibleLineCountTop.read(reader), r.visibleLineCountBottom.read(reader))).filter(isDefined);\n      const newRanges = [];\n      let didChange = false;\n      for (const touching of groupAdjacentBy(updatedLastUnchangedRegions, (a, b) => a.getHiddenModifiedRange(reader).endLineNumberExclusive === b.getHiddenModifiedRange(reader).startLineNumber)) {\n        if (touching.length > 1) {\n          didChange = true;\n          const sumLineCount = touching.reduce((sum, r) => sum + r.lineCount, 0);\n          const r = new UnchangedRegion(touching[0].originalLineNumber, touching[0].modifiedLineNumber, sumLineCount, touching[0].visibleLineCountTop.get(), touching[touching.length - 1].visibleLineCountBottom.get());\n          newRanges.push(r);\n        } else {\n          newRanges.push(touching[0]);\n        }\n      }\n      if (didChange) {\n        const originalDecorationIds = model.original.deltaDecorations(lastUnchangedRegions.originalDecorationIds, newRanges.map(r => ({\n          range: r.originalUnchangedRange.toInclusiveRange(),\n          options: {\n            description: 'unchanged'\n          }\n        })));\n        const modifiedDecorationIds = model.modified.deltaDecorations(lastUnchangedRegions.modifiedDecorationIds, newRanges.map(r => ({\n          range: r.modifiedUnchangedRange.toInclusiveRange(),\n          options: {\n            description: 'unchanged'\n          }\n        })));\n        transaction(tx => {\n          this._unchangedRegions.set({\n            regions: newRanges,\n            originalDecorationIds,\n            modifiedDecorationIds\n          }, tx);\n        });\n      }\n    }));\n    const updateUnchangedRegions = (result, tx, reader) => {\n      const newUnchangedRegions = UnchangedRegion.fromDiffs(result.changes, model.original.getLineCount(), model.modified.getLineCount(), this._options.hideUnchangedRegionsMinimumLineCount.read(reader), this._options.hideUnchangedRegionsContextLineCount.read(reader));\n      // Transfer state from cur state\n      let visibleRegions = undefined;\n      const lastUnchangedRegions = this._unchangedRegions.get();\n      if (lastUnchangedRegions) {\n        const lastUnchangedRegionsOrigRanges = lastUnchangedRegions.originalDecorationIds.map(id => model.original.getDecorationRange(id)).map(r => r ? LineRange.fromRangeInclusive(r) : undefined);\n        const lastUnchangedRegionsModRanges = lastUnchangedRegions.modifiedDecorationIds.map(id => model.modified.getDecorationRange(id)).map(r => r ? LineRange.fromRangeInclusive(r) : undefined);\n        const updatedLastUnchangedRegions = filterWithPrevious(lastUnchangedRegions.regions.map((r, idx) => {\n          if (!lastUnchangedRegionsOrigRanges[idx] || !lastUnchangedRegionsModRanges[idx]) {\n            return undefined;\n          }\n          const length = lastUnchangedRegionsOrigRanges[idx].length;\n          return new UnchangedRegion(lastUnchangedRegionsOrigRanges[idx].startLineNumber, lastUnchangedRegionsModRanges[idx].startLineNumber, length,\n          // The visible area can shrink by edits -> we have to account for this\n          Math.min(r.visibleLineCountTop.get(), length), Math.min(r.visibleLineCountBottom.get(), length - r.visibleLineCountTop.get()));\n        }).filter(isDefined), (cur, prev) => !prev || cur.modifiedLineNumber >= prev.modifiedLineNumber + prev.lineCount && cur.originalLineNumber >= prev.originalLineNumber + prev.lineCount);\n        let hiddenRegions = updatedLastUnchangedRegions.map(r => new LineRangeMapping(r.getHiddenOriginalRange(reader), r.getHiddenModifiedRange(reader)));\n        hiddenRegions = LineRangeMapping.clip(hiddenRegions, LineRange.ofLength(1, model.original.getLineCount()), LineRange.ofLength(1, model.modified.getLineCount()));\n        visibleRegions = LineRangeMapping.inverse(hiddenRegions, model.original.getLineCount(), model.modified.getLineCount());\n      }\n      const newUnchangedRegions2 = [];\n      if (visibleRegions) {\n        for (const r of newUnchangedRegions) {\n          const intersecting = visibleRegions.filter(f => f.original.intersectsStrict(r.originalUnchangedRange) && f.modified.intersectsStrict(r.modifiedUnchangedRange));\n          newUnchangedRegions2.push(...r.setVisibleRanges(intersecting, tx));\n        }\n      } else {\n        newUnchangedRegions2.push(...newUnchangedRegions);\n      }\n      const originalDecorationIds = model.original.deltaDecorations(lastUnchangedRegions?.originalDecorationIds || [], newUnchangedRegions2.map(r => ({\n        range: r.originalUnchangedRange.toInclusiveRange(),\n        options: {\n          description: 'unchanged'\n        }\n      })));\n      const modifiedDecorationIds = model.modified.deltaDecorations(lastUnchangedRegions?.modifiedDecorationIds || [], newUnchangedRegions2.map(r => ({\n        range: r.modifiedUnchangedRange.toInclusiveRange(),\n        options: {\n          description: 'unchanged'\n        }\n      })));\n      this._unchangedRegions.set({\n        regions: newUnchangedRegions2,\n        originalDecorationIds,\n        modifiedDecorationIds\n      }, tx);\n    };\n    this._register(model.modified.onDidChangeContent(e => {\n      const diff = this._diff.get();\n      if (diff) {\n        const textEdits = TextEditInfo.fromModelContentChanges(e.changes);\n        const result = applyModifiedEdits(this._lastDiff, textEdits, model.original, model.modified);\n        if (result) {\n          this._lastDiff = result;\n          transaction(tx => {\n            this._diff.set(DiffState.fromDiffResult(this._lastDiff), tx);\n            updateUnchangedRegions(result, tx);\n            const currentSyncedMovedText = this.movedTextToCompare.get();\n            this.movedTextToCompare.set(currentSyncedMovedText ? this._lastDiff.moves.find(m => m.lineRangeMapping.modified.intersect(currentSyncedMovedText.lineRangeMapping.modified)) : undefined, tx);\n          });\n        }\n      }\n      this._isDiffUpToDate.set(false, undefined);\n      debouncer.schedule();\n    }));\n    this._register(model.original.onDidChangeContent(e => {\n      const diff = this._diff.get();\n      if (diff) {\n        const textEdits = TextEditInfo.fromModelContentChanges(e.changes);\n        const result = applyOriginalEdits(this._lastDiff, textEdits, model.original, model.modified);\n        if (result) {\n          this._lastDiff = result;\n          transaction(tx => {\n            this._diff.set(DiffState.fromDiffResult(this._lastDiff), tx);\n            updateUnchangedRegions(result, tx);\n            const currentSyncedMovedText = this.movedTextToCompare.get();\n            this.movedTextToCompare.set(currentSyncedMovedText ? this._lastDiff.moves.find(m => m.lineRangeMapping.modified.intersect(currentSyncedMovedText.lineRangeMapping.modified)) : undefined, tx);\n          });\n        }\n      }\n      this._isDiffUpToDate.set(false, undefined);\n      debouncer.schedule();\n    }));\n    this._register(autorunWithStore(/*#__PURE__*/function () {\n      var _ref = _asyncToGenerator(function* (reader, store) {\n        /** @description compute diff */\n        // So that they get recomputed when these settings change\n        _this._options.hideUnchangedRegionsMinimumLineCount.read(reader);\n        _this._options.hideUnchangedRegionsContextLineCount.read(reader);\n        debouncer.cancel();\n        contentChangedSignal.read(reader);\n        const documentDiffProvider = _this._diffProvider.read(reader);\n        documentDiffProvider.onChangeSignal.read(reader);\n        readHotReloadableExport(DefaultLinesDiffComputer, reader);\n        readHotReloadableExport(optimizeSequenceDiffs, reader);\n        _this._isDiffUpToDate.set(false, undefined);\n        let originalTextEditInfos = [];\n        store.add(model.original.onDidChangeContent(e => {\n          const edits = TextEditInfo.fromModelContentChanges(e.changes);\n          originalTextEditInfos = combineTextEditInfos(originalTextEditInfos, edits);\n        }));\n        let modifiedTextEditInfos = [];\n        store.add(model.modified.onDidChangeContent(e => {\n          const edits = TextEditInfo.fromModelContentChanges(e.changes);\n          modifiedTextEditInfos = combineTextEditInfos(modifiedTextEditInfos, edits);\n        }));\n        let result = yield documentDiffProvider.diffProvider.computeDiff(model.original, model.modified, {\n          ignoreTrimWhitespace: _this._options.ignoreTrimWhitespace.read(reader),\n          maxComputationTimeMs: _this._options.maxComputationTimeMs.read(reader),\n          computeMoves: _this._options.showMoves.read(reader)\n        }, _this._cancellationTokenSource.token);\n        if (_this._cancellationTokenSource.token.isCancellationRequested) {\n          return;\n        }\n        if (model.original.isDisposed() || model.modified.isDisposed()) {\n          // TODO@hediet fishy?\n          return;\n        }\n        result = normalizeDocumentDiff(result, model.original, model.modified);\n        result = applyOriginalEdits(result, originalTextEditInfos, model.original, model.modified) ?? result;\n        result = applyModifiedEdits(result, modifiedTextEditInfos, model.original, model.modified) ?? result;\n        transaction(tx => {\n          /** @description write diff result */\n          updateUnchangedRegions(result, tx);\n          _this._lastDiff = result;\n          const state = DiffState.fromDiffResult(result);\n          _this._diff.set(state, tx);\n          _this._isDiffUpToDate.set(true, tx);\n          const currentSyncedMovedText = _this.movedTextToCompare.get();\n          _this.movedTextToCompare.set(currentSyncedMovedText ? _this._lastDiff.moves.find(m => m.lineRangeMapping.modified.intersect(currentSyncedMovedText.lineRangeMapping.modified)) : undefined, tx);\n        });\n      });\n      return function (_x, _x2) {\n        return _ref.apply(this, arguments);\n      };\n    }()));\n  }\n  ensureModifiedLineIsVisible(lineNumber, preference, tx) {\n    if (this.diff.get()?.mappings.length === 0) {\n      return;\n    }\n    const unchangedRegions = this._unchangedRegions.get()?.regions || [];\n    for (const r of unchangedRegions) {\n      if (r.getHiddenModifiedRange(undefined).contains(lineNumber)) {\n        r.showModifiedLine(lineNumber, preference, tx);\n        return;\n      }\n    }\n  }\n  ensureOriginalLineIsVisible(lineNumber, preference, tx) {\n    if (this.diff.get()?.mappings.length === 0) {\n      return;\n    }\n    const unchangedRegions = this._unchangedRegions.get()?.regions || [];\n    for (const r of unchangedRegions) {\n      if (r.getHiddenOriginalRange(undefined).contains(lineNumber)) {\n        r.showOriginalLine(lineNumber, preference, tx);\n        return;\n      }\n    }\n  }\n  waitForDiff() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      yield waitForState(_this2.isDiffUpToDate, s => s);\n    })();\n  }\n  serializeState() {\n    const regions = this._unchangedRegions.get();\n    return {\n      collapsedRegions: regions?.regions.map(r => ({\n        range: r.getHiddenModifiedRange(undefined).serialize()\n      }))\n    };\n  }\n  restoreSerializedState(state) {\n    const ranges = state.collapsedRegions?.map(r => LineRange.deserialize(r.range));\n    const regions = this._unchangedRegions.get();\n    if (!regions || !ranges) {\n      return;\n    }\n    transaction(tx => {\n      for (const r of regions.regions) {\n        for (const range of ranges) {\n          if (r.modifiedUnchangedRange.intersect(range)) {\n            r.setHiddenModifiedRange(range, tx);\n            break;\n          }\n        }\n      }\n    });\n  }\n};\nDiffEditorViewModel = __decorate([__param(2, IDiffProviderFactoryService)], DiffEditorViewModel);\nexport { DiffEditorViewModel };\nfunction normalizeDocumentDiff(diff, original, modified) {\n  return {\n    changes: diff.changes.map(c => new DetailedLineRangeMapping(c.original, c.modified, c.innerChanges ? c.innerChanges.map(i => normalizeRangeMapping(i, original, modified)) : undefined)),\n    moves: diff.moves,\n    identical: diff.identical,\n    quitEarly: diff.quitEarly\n  };\n}\nfunction normalizeRangeMapping(rangeMapping, original, modified) {\n  let originalRange = rangeMapping.originalRange;\n  let modifiedRange = rangeMapping.modifiedRange;\n  if (originalRange.startColumn === 1 && modifiedRange.startColumn === 1 && (originalRange.endColumn !== 1 || modifiedRange.endColumn !== 1) && originalRange.endColumn === original.getLineMaxColumn(originalRange.endLineNumber) && modifiedRange.endColumn === modified.getLineMaxColumn(modifiedRange.endLineNumber) && originalRange.endLineNumber < original.getLineCount() && modifiedRange.endLineNumber < modified.getLineCount()) {\n    originalRange = originalRange.setEndPosition(originalRange.endLineNumber + 1, 1);\n    modifiedRange = modifiedRange.setEndPosition(modifiedRange.endLineNumber + 1, 1);\n  }\n  return new RangeMapping(originalRange, modifiedRange);\n}\nexport class DiffState {\n  static fromDiffResult(result) {\n    return new DiffState(result.changes.map(c => new DiffMapping(c)), result.moves || [], result.identical, result.quitEarly);\n  }\n  constructor(mappings, movedTexts, identical, quitEarly) {\n    this.mappings = mappings;\n    this.movedTexts = movedTexts;\n    this.identical = identical;\n    this.quitEarly = quitEarly;\n  }\n}\nexport class DiffMapping {\n  constructor(lineRangeMapping) {\n    this.lineRangeMapping = lineRangeMapping;\n    /*\n    readonly movedTo: MovedText | undefined,\n    readonly movedFrom: MovedText | undefined,\n     if (movedTo) {\n        assertFn(() =>\n            movedTo.lineRangeMapping.modifiedRange.equals(lineRangeMapping.modifiedRange)\n            && lineRangeMapping.originalRange.isEmpty\n            && !movedFrom\n        );\n    } else if (movedFrom) {\n        assertFn(() =>\n            movedFrom.lineRangeMapping.originalRange.equals(lineRangeMapping.originalRange)\n            && lineRangeMapping.modifiedRange.isEmpty\n            && !movedTo\n        );\n    }\n    */\n  }\n}\nexport class UnchangedRegion {\n  static fromDiffs(changes, originalLineCount, modifiedLineCount, minHiddenLineCount, minContext) {\n    const inversedMappings = DetailedLineRangeMapping.inverse(changes, originalLineCount, modifiedLineCount);\n    const result = [];\n    for (const mapping of inversedMappings) {\n      let origStart = mapping.original.startLineNumber;\n      let modStart = mapping.modified.startLineNumber;\n      let length = mapping.original.length;\n      const atStart = origStart === 1 && modStart === 1;\n      const atEnd = origStart + length === originalLineCount + 1 && modStart + length === modifiedLineCount + 1;\n      if ((atStart || atEnd) && length >= minContext + minHiddenLineCount) {\n        if (atStart && !atEnd) {\n          length -= minContext;\n        }\n        if (atEnd && !atStart) {\n          origStart += minContext;\n          modStart += minContext;\n          length -= minContext;\n        }\n        result.push(new UnchangedRegion(origStart, modStart, length, 0, 0));\n      } else if (length >= minContext * 2 + minHiddenLineCount) {\n        origStart += minContext;\n        modStart += minContext;\n        length -= minContext * 2;\n        result.push(new UnchangedRegion(origStart, modStart, length, 0, 0));\n      }\n    }\n    return result;\n  }\n  get originalUnchangedRange() {\n    return LineRange.ofLength(this.originalLineNumber, this.lineCount);\n  }\n  get modifiedUnchangedRange() {\n    return LineRange.ofLength(this.modifiedLineNumber, this.lineCount);\n  }\n  constructor(originalLineNumber, modifiedLineNumber, lineCount, visibleLineCountTop, visibleLineCountBottom) {\n    this.originalLineNumber = originalLineNumber;\n    this.modifiedLineNumber = modifiedLineNumber;\n    this.lineCount = lineCount;\n    this._visibleLineCountTop = observableValue(this, 0);\n    this.visibleLineCountTop = this._visibleLineCountTop;\n    this._visibleLineCountBottom = observableValue(this, 0);\n    this.visibleLineCountBottom = this._visibleLineCountBottom;\n    this._shouldHideControls = derived(this, reader => /** @description isVisible */this.visibleLineCountTop.read(reader) + this.visibleLineCountBottom.read(reader) === this.lineCount && !this.isDragged.read(reader));\n    this.isDragged = observableValue(this, undefined);\n    const visibleLineCountTop2 = Math.max(Math.min(visibleLineCountTop, this.lineCount), 0);\n    const visibleLineCountBottom2 = Math.max(Math.min(visibleLineCountBottom, this.lineCount - visibleLineCountTop), 0);\n    softAssert(visibleLineCountTop === visibleLineCountTop2);\n    softAssert(visibleLineCountBottom === visibleLineCountBottom2);\n    this._visibleLineCountTop.set(visibleLineCountTop2, undefined);\n    this._visibleLineCountBottom.set(visibleLineCountBottom2, undefined);\n  }\n  setVisibleRanges(visibleRanges, tx) {\n    const result = [];\n    const hiddenModified = new LineRangeSet(visibleRanges.map(r => r.modified)).subtractFrom(this.modifiedUnchangedRange);\n    let originalStartLineNumber = this.originalLineNumber;\n    let modifiedStartLineNumber = this.modifiedLineNumber;\n    const modifiedEndLineNumberEx = this.modifiedLineNumber + this.lineCount;\n    if (hiddenModified.ranges.length === 0) {\n      this.showAll(tx);\n      result.push(this);\n    } else {\n      let i = 0;\n      for (const r of hiddenModified.ranges) {\n        const isLast = i === hiddenModified.ranges.length - 1;\n        i++;\n        const length = (isLast ? modifiedEndLineNumberEx : r.endLineNumberExclusive) - modifiedStartLineNumber;\n        const newR = new UnchangedRegion(originalStartLineNumber, modifiedStartLineNumber, length, 0, 0);\n        newR.setHiddenModifiedRange(r, tx);\n        result.push(newR);\n        originalStartLineNumber = newR.originalUnchangedRange.endLineNumberExclusive;\n        modifiedStartLineNumber = newR.modifiedUnchangedRange.endLineNumberExclusive;\n      }\n    }\n    return result;\n  }\n  shouldHideControls(reader) {\n    return this._shouldHideControls.read(reader);\n  }\n  getHiddenOriginalRange(reader) {\n    return LineRange.ofLength(this.originalLineNumber + this._visibleLineCountTop.read(reader), this.lineCount - this._visibleLineCountTop.read(reader) - this._visibleLineCountBottom.read(reader));\n  }\n  getHiddenModifiedRange(reader) {\n    return LineRange.ofLength(this.modifiedLineNumber + this._visibleLineCountTop.read(reader), this.lineCount - this._visibleLineCountTop.read(reader) - this._visibleLineCountBottom.read(reader));\n  }\n  setHiddenModifiedRange(range, tx) {\n    const visibleLineCountTop = range.startLineNumber - this.modifiedLineNumber;\n    const visibleLineCountBottom = this.modifiedLineNumber + this.lineCount - range.endLineNumberExclusive;\n    this.setState(visibleLineCountTop, visibleLineCountBottom, tx);\n  }\n  getMaxVisibleLineCountTop() {\n    return this.lineCount - this._visibleLineCountBottom.get();\n  }\n  getMaxVisibleLineCountBottom() {\n    return this.lineCount - this._visibleLineCountTop.get();\n  }\n  showMoreAbove(count = 10, tx) {\n    const maxVisibleLineCountTop = this.getMaxVisibleLineCountTop();\n    this._visibleLineCountTop.set(Math.min(this._visibleLineCountTop.get() + count, maxVisibleLineCountTop), tx);\n  }\n  showMoreBelow(count = 10, tx) {\n    const maxVisibleLineCountBottom = this.lineCount - this._visibleLineCountTop.get();\n    this._visibleLineCountBottom.set(Math.min(this._visibleLineCountBottom.get() + count, maxVisibleLineCountBottom), tx);\n  }\n  showAll(tx) {\n    this._visibleLineCountBottom.set(this.lineCount - this._visibleLineCountTop.get(), tx);\n  }\n  showModifiedLine(lineNumber, preference, tx) {\n    const top = lineNumber + 1 - (this.modifiedLineNumber + this._visibleLineCountTop.get());\n    const bottom = this.modifiedLineNumber - this._visibleLineCountBottom.get() + this.lineCount - lineNumber;\n    if (preference === 0 /* RevealPreference.FromCloserSide */ && top < bottom || preference === 1 /* RevealPreference.FromTop */) {\n      this._visibleLineCountTop.set(this._visibleLineCountTop.get() + top, tx);\n    } else {\n      this._visibleLineCountBottom.set(this._visibleLineCountBottom.get() + bottom, tx);\n    }\n  }\n  showOriginalLine(lineNumber, preference, tx) {\n    const top = lineNumber - this.originalLineNumber;\n    const bottom = this.originalLineNumber + this.lineCount - lineNumber;\n    if (preference === 0 /* RevealPreference.FromCloserSide */ && top < bottom || preference === 1 /* RevealPreference.FromTop */) {\n      this._visibleLineCountTop.set(Math.min(this._visibleLineCountTop.get() + bottom - top, this.getMaxVisibleLineCountTop()), tx);\n    } else {\n      this._visibleLineCountBottom.set(Math.min(this._visibleLineCountBottom.get() + top - bottom, this.getMaxVisibleLineCountBottom()), tx);\n    }\n  }\n  collapseAll(tx) {\n    this._visibleLineCountTop.set(0, tx);\n    this._visibleLineCountBottom.set(0, tx);\n  }\n  setState(visibleLineCountTop, visibleLineCountBottom, tx) {\n    visibleLineCountTop = Math.max(Math.min(visibleLineCountTop, this.lineCount), 0);\n    visibleLineCountBottom = Math.max(Math.min(visibleLineCountBottom, this.lineCount - visibleLineCountTop), 0);\n    this._visibleLineCountTop.set(visibleLineCountTop, tx);\n    this._visibleLineCountBottom.set(visibleLineCountBottom, tx);\n  }\n}\nfunction applyOriginalEdits(diff, textEdits, originalTextModel, modifiedTextModel) {\n  return undefined;\n  /*\n  TODO@hediet\n  if (textEdits.length === 0) {\n      return diff;\n  }\n   const diff2 = flip(diff);\n  const diff3 = applyModifiedEdits(diff2, textEdits, modifiedTextModel, originalTextModel);\n  if (!diff3) {\n      return undefined;\n  }\n  return flip(diff3);*/\n}\n/*\nfunction flip(diff: IDocumentDiff): IDocumentDiff {\n    return {\n        changes: diff.changes.map(c => c.flip()),\n        moves: diff.moves.map(m => m.flip()),\n        identical: diff.identical,\n        quitEarly: diff.quitEarly,\n    };\n}\n*/\nfunction applyModifiedEdits(diff, textEdits, originalTextModel, modifiedTextModel) {\n  return undefined;\n  /*\n  TODO@hediet\n  if (textEdits.length === 0) {\n      return diff;\n  }\n  if (diff.changes.some(c => !c.innerChanges) || diff.moves.length > 0) {\n      // TODO support these cases\n      return undefined;\n  }\n   const changes = applyModifiedEditsToLineRangeMappings(diff.changes, textEdits, originalTextModel, modifiedTextModel);\n   const moves = diff.moves.map(m => {\n      const newModifiedRange = applyEditToLineRange(m.lineRangeMapping.modified, textEdits);\n      return newModifiedRange ? new MovedText(\n          new SimpleLineRangeMapping(m.lineRangeMapping.original, newModifiedRange),\n          applyModifiedEditsToLineRangeMappings(m.changes, textEdits, originalTextModel, modifiedTextModel),\n      ) : undefined;\n  }).filter(isDefined);\n   return {\n      identical: false,\n      quitEarly: false,\n      changes,\n      moves,\n  };*/\n}\n/*\nfunction applyEditToLineRange(range: LineRange, textEdits: TextEditInfo[]): LineRange | undefined {\n    let rangeStartLineNumber = range.startLineNumber;\n    let rangeEndLineNumberEx = range.endLineNumberExclusive;\n\n    for (let i = textEdits.length - 1; i >= 0; i--) {\n        const textEdit = textEdits[i];\n        const textEditStartLineNumber = lengthGetLineCount(textEdit.startOffset) + 1;\n        const textEditEndLineNumber = lengthGetLineCount(textEdit.endOffset) + 1;\n        const newLengthLineCount = lengthGetLineCount(textEdit.newLength);\n        const delta = newLengthLineCount - (textEditEndLineNumber - textEditStartLineNumber);\n\n        if (textEditEndLineNumber < rangeStartLineNumber) {\n            // the text edit is before us\n            rangeStartLineNumber += delta;\n            rangeEndLineNumberEx += delta;\n        } else if (textEditStartLineNumber > rangeEndLineNumberEx) {\n            // the text edit is after us\n            // NOOP\n        } else if (textEditStartLineNumber < rangeStartLineNumber && rangeEndLineNumberEx < textEditEndLineNumber) {\n            // the range is fully contained in the text edit\n            return undefined;\n        } else if (textEditStartLineNumber < rangeStartLineNumber && textEditEndLineNumber <= rangeEndLineNumberEx) {\n            // the text edit ends inside our range\n            rangeStartLineNumber = textEditEndLineNumber + 1;\n            rangeStartLineNumber += delta;\n            rangeEndLineNumberEx += delta;\n        } else if (rangeStartLineNumber <= textEditStartLineNumber && textEditEndLineNumber < rangeStartLineNumber) {\n            // the text edit starts inside our range\n            rangeEndLineNumberEx = textEditStartLineNumber;\n        } else {\n            rangeEndLineNumberEx += delta;\n        }\n    }\n\n    return new LineRange(rangeStartLineNumber, rangeEndLineNumberEx);\n}\n\nfunction applyModifiedEditsToLineRangeMappings(changes: readonly LineRangeMapping[], textEdits: TextEditInfo[], originalTextModel: ITextModel, modifiedTextModel: ITextModel): LineRangeMapping[] {\n    const diffTextEdits = changes.flatMap(c => c.innerChanges!.map(c => new TextEditInfo(\n        positionToLength(c.originalRange.getStartPosition()),\n        positionToLength(c.originalRange.getEndPosition()),\n        lengthOfRange(c.modifiedRange).toLength(),\n    )));\n\n    const combined = combineTextEditInfos(diffTextEdits, textEdits);\n\n    let lastOriginalEndOffset = lengthZero;\n    let lastModifiedEndOffset = lengthZero;\n    const rangeMappings = combined.map(c => {\n        const modifiedStartOffset = lengthAdd(lastModifiedEndOffset, lengthDiffNonNegative(lastOriginalEndOffset, c.startOffset));\n        lastOriginalEndOffset = c.endOffset;\n        lastModifiedEndOffset = lengthAdd(modifiedStartOffset, c.newLength);\n\n        return new RangeMapping(\n            Range.fromPositions(lengthToPosition(c.startOffset), lengthToPosition(c.endOffset)),\n            Range.fromPositions(lengthToPosition(modifiedStartOffset), lengthToPosition(lastModifiedEndOffset)),\n        );\n    });\n\n    const newChanges = lineRangeMappingFromRangeMappings(\n        rangeMappings,\n        originalTextModel.getLinesContent(),\n        modifiedTextModel.getLinesContent(),\n    );\n    return newChanges;\n}\n*/", "map": {"version": 3, "names": ["__decorate", "decorators", "target", "key", "desc", "c", "arguments", "length", "r", "Object", "getOwnPropertyDescriptor", "d", "Reflect", "decorate", "i", "defineProperty", "__param", "paramIndex", "decorator", "RunOnceScheduler", "CancellationTokenSource", "Disposable", "toDisposable", "autorun", "autorunWithStore", "derived", "observableSignal", "observableSignalFromEvent", "observableValue", "transaction", "waitForState", "IDiffProviderFactoryService", "filterWithPrevious", "readHotReloadableExport", "LineRange", "LineRangeSet", "DefaultLinesDiffComputer", "DetailedLineRangeMapping", "LineRangeMapping", "RangeMapping", "TextEditInfo", "combineTextEditInfos", "optimizeSequenceDiffs", "isDefined", "groupAdjacentBy", "softAssert", "DiffEditorViewModel", "setActiveMovedText", "movedText", "_activeMovedText", "set", "undefined", "constructor", "model", "_options", "_diffProviderFactoryService", "_this", "this", "_isDiffUpToDate", "isDiffUpToDate", "_diff", "diff", "_unchangedRegions", "unchangedRegions", "hideUnchangedRegions", "read", "regions", "tx", "get", "collapseAll", "movedTextToCompare", "_hoveredMovedText", "activeMovedText", "_cancellationTokenSource", "_diffProvider", "reader", "diffProvider", "createDiffProvider", "diffAlgorithm", "onChangeSignal", "onDidChange", "_register", "cancel", "contentChangedSignal", "debouncer", "trigger", "lastUnchangedRegions", "some", "isDragged", "lastUnchangedRegionsOrigRanges", "originalDecorationIds", "map", "id", "original", "getDecorationRange", "fromRangeInclusive", "lastUnchangedRegionsModRanges", "modifiedDecorationIds", "modified", "updatedLastUnchangedRegions", "idx", "UnchangedRegion", "startLineNumber", "visibleLineCountTop", "visibleLineCountBottom", "filter", "newRang<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "touching", "a", "b", "getHiddenModifiedRange", "endLineNumberExclusive", "sumLineCount", "reduce", "sum", "lineCount", "originalLineNumber", "modifiedLineNumber", "push", "deltaDecorations", "range", "originalUnchangedRange", "toInclusiveRange", "options", "description", "modifiedUnchangedRange", "updateUnchangedRegions", "result", "newUnchangedRegions", "fromDiffs", "changes", "getLineCount", "hideUnchangedRegionsMinimumLineCount", "hideUnchangedRegionsContextLineCount", "visibleRegions", "Math", "min", "cur", "prev", "hiddenRegions", "getHiddenOriginalRange", "clip", "of<PERSON>ength", "inverse", "newUnchangedRegions2", "intersecting", "f", "intersectsStrict", "setVisibleRanges", "onDidChangeContent", "e", "textEdits", "fromModelContentChanges", "applyModifiedEdits", "_lastDiff", "DiffState", "fromDiffResult", "currentSyncedMovedText", "moves", "find", "m", "lineRangeMapping", "intersect", "schedule", "applyOriginalEdits", "_ref", "_asyncToGenerator", "store", "documentDiffProvider", "originalTextEditInfos", "add", "edits", "modifiedTextEditInfos", "computeDiff", "ignoreTrimWhitespace", "maxComputationTimeMs", "computeMoves", "showMoves", "token", "isCancellationRequested", "isDisposed", "normalizeDocumentDiff", "state", "_x", "_x2", "apply", "ensureModifiedLineIsVisible", "lineNumber", "preference", "mappings", "contains", "showModifiedLine", "ensureOriginalLineIsVisible", "showOriginalLine", "waitForDiff", "_this2", "s", "serializeState", "collapsedRegions", "serialize", "restoreSerializedState", "ranges", "deserialize", "setHiddenModifiedRange", "innerChanges", "normalizeRangeMapping", "identical", "quit<PERSON>arly", "rangeMapping", "originalRange", "modifiedRange", "startColumn", "endColumn", "getLineMaxColumn", "endLineNumber", "setEndPosition", "DiffMapping", "movedTexts", "originalLineCount", "modifiedLineCount", "minHiddenLineCount", "minContext", "inversedMappings", "mapping", "origStart", "modStart", "atStart", "atEnd", "_visibleLineCountTop", "_visibleLineCountBottom", "_shouldHideControls", "visibleLineCountTop2", "max", "visibleLineCountBottom2", "visibleRanges", "hiddenModified", "subtractFrom", "originalStartLineNumber", "modifiedStartLineNumber", "modifiedEndLineNumberEx", "showAll", "isLast", "newR", "shouldHideControls", "setState", "getMaxVisibleLineCountTop", "getMaxVisibleLineCountBottom", "showMoreAbove", "count", "maxVisibleLineCountTop", "showMoreBelow", "maxVisibleLineCountBottom", "top", "bottom", "originalTextModel", "modifiedTextModel"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/browser/widget/diffEditor/diffEditorViewModel.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = (this && this.__param) || function (paramIndex, decorator) {\n    return function (target, key) { decorator(target, key, paramIndex); }\n};\nimport { RunOnceScheduler } from '../../../../base/common/async.js';\nimport { CancellationTokenSource } from '../../../../base/common/cancellation.js';\nimport { Disposable, toDisposable } from '../../../../base/common/lifecycle.js';\nimport { autorun, autorunWithStore, derived, observableSignal, observableSignalFromEvent, observableValue, transaction, waitForState } from '../../../../base/common/observable.js';\nimport { IDiffProviderFactoryService } from './diffProviderFactoryService.js';\nimport { filterWithPrevious } from './utils.js';\nimport { readHotReloadableExport } from '../../../../base/common/hotReloadHelpers.js';\nimport { LineRange, LineRangeSet } from '../../../common/core/lineRange.js';\nimport { DefaultLinesDiffComputer } from '../../../common/diff/defaultLinesDiffComputer/defaultLinesDiffComputer.js';\nimport { DetailedLineRangeMapping, LineRangeMapping, RangeMapping } from '../../../common/diff/rangeMapping.js';\nimport { TextEditInfo } from '../../../common/model/bracketPairsTextModelPart/bracketPairsTree/beforeEditPositionMapper.js';\nimport { combineTextEditInfos } from '../../../common/model/bracketPairsTextModelPart/bracketPairsTree/combineTextEditInfos.js';\nimport { optimizeSequenceDiffs } from '../../../common/diff/defaultLinesDiffComputer/heuristicSequenceOptimizations.js';\nimport { isDefined } from '../../../../base/common/types.js';\nimport { groupAdjacentBy } from '../../../../base/common/arrays.js';\nimport { softAssert } from '../../../../base/common/assert.js';\nlet DiffEditorViewModel = class DiffEditorViewModel extends Disposable {\n    setActiveMovedText(movedText) {\n        this._activeMovedText.set(movedText, undefined);\n    }\n    constructor(model, _options, _diffProviderFactoryService) {\n        super();\n        this.model = model;\n        this._options = _options;\n        this._diffProviderFactoryService = _diffProviderFactoryService;\n        this._isDiffUpToDate = observableValue(this, false);\n        this.isDiffUpToDate = this._isDiffUpToDate;\n        this._diff = observableValue(this, undefined);\n        this.diff = this._diff;\n        this._unchangedRegions = observableValue(this, undefined);\n        this.unchangedRegions = derived(this, r => {\n            if (this._options.hideUnchangedRegions.read(r)) {\n                return this._unchangedRegions.read(r)?.regions ?? [];\n            }\n            else {\n                // Reset state\n                transaction(tx => {\n                    for (const r of this._unchangedRegions.get()?.regions || []) {\n                        r.collapseAll(tx);\n                    }\n                });\n                return [];\n            }\n        });\n        this.movedTextToCompare = observableValue(this, undefined);\n        this._activeMovedText = observableValue(this, undefined);\n        this._hoveredMovedText = observableValue(this, undefined);\n        this.activeMovedText = derived(this, r => this.movedTextToCompare.read(r) ?? this._hoveredMovedText.read(r) ?? this._activeMovedText.read(r));\n        this._cancellationTokenSource = new CancellationTokenSource();\n        this._diffProvider = derived(this, reader => {\n            const diffProvider = this._diffProviderFactoryService.createDiffProvider({\n                diffAlgorithm: this._options.diffAlgorithm.read(reader)\n            });\n            const onChangeSignal = observableSignalFromEvent('onDidChange', diffProvider.onDidChange);\n            return {\n                diffProvider,\n                onChangeSignal,\n            };\n        });\n        this._register(toDisposable(() => this._cancellationTokenSource.cancel()));\n        const contentChangedSignal = observableSignal('contentChangedSignal');\n        const debouncer = this._register(new RunOnceScheduler(() => contentChangedSignal.trigger(undefined), 200));\n        this._register(autorun(reader => {\n            /** @description collapse touching unchanged ranges */\n            const lastUnchangedRegions = this._unchangedRegions.read(reader);\n            if (!lastUnchangedRegions || lastUnchangedRegions.regions.some(r => r.isDragged.read(reader))) {\n                return;\n            }\n            const lastUnchangedRegionsOrigRanges = lastUnchangedRegions.originalDecorationIds\n                .map(id => model.original.getDecorationRange(id))\n                .map(r => r ? LineRange.fromRangeInclusive(r) : undefined);\n            const lastUnchangedRegionsModRanges = lastUnchangedRegions.modifiedDecorationIds\n                .map(id => model.modified.getDecorationRange(id))\n                .map(r => r ? LineRange.fromRangeInclusive(r) : undefined);\n            const updatedLastUnchangedRegions = lastUnchangedRegions.regions.map((r, idx) => (!lastUnchangedRegionsOrigRanges[idx] || !lastUnchangedRegionsModRanges[idx]) ? undefined :\n                new UnchangedRegion(lastUnchangedRegionsOrigRanges[idx].startLineNumber, lastUnchangedRegionsModRanges[idx].startLineNumber, lastUnchangedRegionsOrigRanges[idx].length, r.visibleLineCountTop.read(reader), r.visibleLineCountBottom.read(reader))).filter(isDefined);\n            const newRanges = [];\n            let didChange = false;\n            for (const touching of groupAdjacentBy(updatedLastUnchangedRegions, (a, b) => a.getHiddenModifiedRange(reader).endLineNumberExclusive === b.getHiddenModifiedRange(reader).startLineNumber)) {\n                if (touching.length > 1) {\n                    didChange = true;\n                    const sumLineCount = touching.reduce((sum, r) => sum + r.lineCount, 0);\n                    const r = new UnchangedRegion(touching[0].originalLineNumber, touching[0].modifiedLineNumber, sumLineCount, touching[0].visibleLineCountTop.get(), touching[touching.length - 1].visibleLineCountBottom.get());\n                    newRanges.push(r);\n                }\n                else {\n                    newRanges.push(touching[0]);\n                }\n            }\n            if (didChange) {\n                const originalDecorationIds = model.original.deltaDecorations(lastUnchangedRegions.originalDecorationIds, newRanges.map(r => ({ range: r.originalUnchangedRange.toInclusiveRange(), options: { description: 'unchanged' } })));\n                const modifiedDecorationIds = model.modified.deltaDecorations(lastUnchangedRegions.modifiedDecorationIds, newRanges.map(r => ({ range: r.modifiedUnchangedRange.toInclusiveRange(), options: { description: 'unchanged' } })));\n                transaction(tx => {\n                    this._unchangedRegions.set({\n                        regions: newRanges,\n                        originalDecorationIds,\n                        modifiedDecorationIds\n                    }, tx);\n                });\n            }\n        }));\n        const updateUnchangedRegions = (result, tx, reader) => {\n            const newUnchangedRegions = UnchangedRegion.fromDiffs(result.changes, model.original.getLineCount(), model.modified.getLineCount(), this._options.hideUnchangedRegionsMinimumLineCount.read(reader), this._options.hideUnchangedRegionsContextLineCount.read(reader));\n            // Transfer state from cur state\n            let visibleRegions = undefined;\n            const lastUnchangedRegions = this._unchangedRegions.get();\n            if (lastUnchangedRegions) {\n                const lastUnchangedRegionsOrigRanges = lastUnchangedRegions.originalDecorationIds\n                    .map(id => model.original.getDecorationRange(id))\n                    .map(r => r ? LineRange.fromRangeInclusive(r) : undefined);\n                const lastUnchangedRegionsModRanges = lastUnchangedRegions.modifiedDecorationIds\n                    .map(id => model.modified.getDecorationRange(id))\n                    .map(r => r ? LineRange.fromRangeInclusive(r) : undefined);\n                const updatedLastUnchangedRegions = filterWithPrevious(lastUnchangedRegions.regions\n                    .map((r, idx) => {\n                    if (!lastUnchangedRegionsOrigRanges[idx] || !lastUnchangedRegionsModRanges[idx]) {\n                        return undefined;\n                    }\n                    const length = lastUnchangedRegionsOrigRanges[idx].length;\n                    return new UnchangedRegion(lastUnchangedRegionsOrigRanges[idx].startLineNumber, lastUnchangedRegionsModRanges[idx].startLineNumber, length, \n                    // The visible area can shrink by edits -> we have to account for this\n                    Math.min(r.visibleLineCountTop.get(), length), Math.min(r.visibleLineCountBottom.get(), length - r.visibleLineCountTop.get()));\n                }).filter(isDefined), (cur, prev) => !prev || (cur.modifiedLineNumber >= prev.modifiedLineNumber + prev.lineCount && cur.originalLineNumber >= prev.originalLineNumber + prev.lineCount));\n                let hiddenRegions = updatedLastUnchangedRegions.map(r => new LineRangeMapping(r.getHiddenOriginalRange(reader), r.getHiddenModifiedRange(reader)));\n                hiddenRegions = LineRangeMapping.clip(hiddenRegions, LineRange.ofLength(1, model.original.getLineCount()), LineRange.ofLength(1, model.modified.getLineCount()));\n                visibleRegions = LineRangeMapping.inverse(hiddenRegions, model.original.getLineCount(), model.modified.getLineCount());\n            }\n            const newUnchangedRegions2 = [];\n            if (visibleRegions) {\n                for (const r of newUnchangedRegions) {\n                    const intersecting = visibleRegions.filter(f => f.original.intersectsStrict(r.originalUnchangedRange) && f.modified.intersectsStrict(r.modifiedUnchangedRange));\n                    newUnchangedRegions2.push(...r.setVisibleRanges(intersecting, tx));\n                }\n            }\n            else {\n                newUnchangedRegions2.push(...newUnchangedRegions);\n            }\n            const originalDecorationIds = model.original.deltaDecorations(lastUnchangedRegions?.originalDecorationIds || [], newUnchangedRegions2.map(r => ({ range: r.originalUnchangedRange.toInclusiveRange(), options: { description: 'unchanged' } })));\n            const modifiedDecorationIds = model.modified.deltaDecorations(lastUnchangedRegions?.modifiedDecorationIds || [], newUnchangedRegions2.map(r => ({ range: r.modifiedUnchangedRange.toInclusiveRange(), options: { description: 'unchanged' } })));\n            this._unchangedRegions.set({\n                regions: newUnchangedRegions2,\n                originalDecorationIds,\n                modifiedDecorationIds\n            }, tx);\n        };\n        this._register(model.modified.onDidChangeContent((e) => {\n            const diff = this._diff.get();\n            if (diff) {\n                const textEdits = TextEditInfo.fromModelContentChanges(e.changes);\n                const result = applyModifiedEdits(this._lastDiff, textEdits, model.original, model.modified);\n                if (result) {\n                    this._lastDiff = result;\n                    transaction(tx => {\n                        this._diff.set(DiffState.fromDiffResult(this._lastDiff), tx);\n                        updateUnchangedRegions(result, tx);\n                        const currentSyncedMovedText = this.movedTextToCompare.get();\n                        this.movedTextToCompare.set(currentSyncedMovedText ? this._lastDiff.moves.find(m => m.lineRangeMapping.modified.intersect(currentSyncedMovedText.lineRangeMapping.modified)) : undefined, tx);\n                    });\n                }\n            }\n            this._isDiffUpToDate.set(false, undefined);\n            debouncer.schedule();\n        }));\n        this._register(model.original.onDidChangeContent((e) => {\n            const diff = this._diff.get();\n            if (diff) {\n                const textEdits = TextEditInfo.fromModelContentChanges(e.changes);\n                const result = applyOriginalEdits(this._lastDiff, textEdits, model.original, model.modified);\n                if (result) {\n                    this._lastDiff = result;\n                    transaction(tx => {\n                        this._diff.set(DiffState.fromDiffResult(this._lastDiff), tx);\n                        updateUnchangedRegions(result, tx);\n                        const currentSyncedMovedText = this.movedTextToCompare.get();\n                        this.movedTextToCompare.set(currentSyncedMovedText ? this._lastDiff.moves.find(m => m.lineRangeMapping.modified.intersect(currentSyncedMovedText.lineRangeMapping.modified)) : undefined, tx);\n                    });\n                }\n            }\n            this._isDiffUpToDate.set(false, undefined);\n            debouncer.schedule();\n        }));\n        this._register(autorunWithStore(async (reader, store) => {\n            /** @description compute diff */\n            // So that they get recomputed when these settings change\n            this._options.hideUnchangedRegionsMinimumLineCount.read(reader);\n            this._options.hideUnchangedRegionsContextLineCount.read(reader);\n            debouncer.cancel();\n            contentChangedSignal.read(reader);\n            const documentDiffProvider = this._diffProvider.read(reader);\n            documentDiffProvider.onChangeSignal.read(reader);\n            readHotReloadableExport(DefaultLinesDiffComputer, reader);\n            readHotReloadableExport(optimizeSequenceDiffs, reader);\n            this._isDiffUpToDate.set(false, undefined);\n            let originalTextEditInfos = [];\n            store.add(model.original.onDidChangeContent((e) => {\n                const edits = TextEditInfo.fromModelContentChanges(e.changes);\n                originalTextEditInfos = combineTextEditInfos(originalTextEditInfos, edits);\n            }));\n            let modifiedTextEditInfos = [];\n            store.add(model.modified.onDidChangeContent((e) => {\n                const edits = TextEditInfo.fromModelContentChanges(e.changes);\n                modifiedTextEditInfos = combineTextEditInfos(modifiedTextEditInfos, edits);\n            }));\n            let result = await documentDiffProvider.diffProvider.computeDiff(model.original, model.modified, {\n                ignoreTrimWhitespace: this._options.ignoreTrimWhitespace.read(reader),\n                maxComputationTimeMs: this._options.maxComputationTimeMs.read(reader),\n                computeMoves: this._options.showMoves.read(reader),\n            }, this._cancellationTokenSource.token);\n            if (this._cancellationTokenSource.token.isCancellationRequested) {\n                return;\n            }\n            if (model.original.isDisposed() || model.modified.isDisposed()) {\n                // TODO@hediet fishy?\n                return;\n            }\n            result = normalizeDocumentDiff(result, model.original, model.modified);\n            result = applyOriginalEdits(result, originalTextEditInfos, model.original, model.modified) ?? result;\n            result = applyModifiedEdits(result, modifiedTextEditInfos, model.original, model.modified) ?? result;\n            transaction(tx => {\n                /** @description write diff result */\n                updateUnchangedRegions(result, tx);\n                this._lastDiff = result;\n                const state = DiffState.fromDiffResult(result);\n                this._diff.set(state, tx);\n                this._isDiffUpToDate.set(true, tx);\n                const currentSyncedMovedText = this.movedTextToCompare.get();\n                this.movedTextToCompare.set(currentSyncedMovedText ? this._lastDiff.moves.find(m => m.lineRangeMapping.modified.intersect(currentSyncedMovedText.lineRangeMapping.modified)) : undefined, tx);\n            });\n        }));\n    }\n    ensureModifiedLineIsVisible(lineNumber, preference, tx) {\n        if (this.diff.get()?.mappings.length === 0) {\n            return;\n        }\n        const unchangedRegions = this._unchangedRegions.get()?.regions || [];\n        for (const r of unchangedRegions) {\n            if (r.getHiddenModifiedRange(undefined).contains(lineNumber)) {\n                r.showModifiedLine(lineNumber, preference, tx);\n                return;\n            }\n        }\n    }\n    ensureOriginalLineIsVisible(lineNumber, preference, tx) {\n        if (this.diff.get()?.mappings.length === 0) {\n            return;\n        }\n        const unchangedRegions = this._unchangedRegions.get()?.regions || [];\n        for (const r of unchangedRegions) {\n            if (r.getHiddenOriginalRange(undefined).contains(lineNumber)) {\n                r.showOriginalLine(lineNumber, preference, tx);\n                return;\n            }\n        }\n    }\n    async waitForDiff() {\n        await waitForState(this.isDiffUpToDate, s => s);\n    }\n    serializeState() {\n        const regions = this._unchangedRegions.get();\n        return {\n            collapsedRegions: regions?.regions.map(r => ({ range: r.getHiddenModifiedRange(undefined).serialize() }))\n        };\n    }\n    restoreSerializedState(state) {\n        const ranges = state.collapsedRegions?.map(r => LineRange.deserialize(r.range));\n        const regions = this._unchangedRegions.get();\n        if (!regions || !ranges) {\n            return;\n        }\n        transaction(tx => {\n            for (const r of regions.regions) {\n                for (const range of ranges) {\n                    if (r.modifiedUnchangedRange.intersect(range)) {\n                        r.setHiddenModifiedRange(range, tx);\n                        break;\n                    }\n                }\n            }\n        });\n    }\n};\nDiffEditorViewModel = __decorate([\n    __param(2, IDiffProviderFactoryService)\n], DiffEditorViewModel);\nexport { DiffEditorViewModel };\nfunction normalizeDocumentDiff(diff, original, modified) {\n    return {\n        changes: diff.changes.map(c => new DetailedLineRangeMapping(c.original, c.modified, c.innerChanges ? c.innerChanges.map(i => normalizeRangeMapping(i, original, modified)) : undefined)),\n        moves: diff.moves,\n        identical: diff.identical,\n        quitEarly: diff.quitEarly,\n    };\n}\nfunction normalizeRangeMapping(rangeMapping, original, modified) {\n    let originalRange = rangeMapping.originalRange;\n    let modifiedRange = rangeMapping.modifiedRange;\n    if (originalRange.startColumn === 1 && modifiedRange.startColumn === 1 &&\n        (originalRange.endColumn !== 1 || modifiedRange.endColumn !== 1) &&\n        originalRange.endColumn === original.getLineMaxColumn(originalRange.endLineNumber)\n        && modifiedRange.endColumn === modified.getLineMaxColumn(modifiedRange.endLineNumber)\n        && originalRange.endLineNumber < original.getLineCount()\n        && modifiedRange.endLineNumber < modified.getLineCount()) {\n        originalRange = originalRange.setEndPosition(originalRange.endLineNumber + 1, 1);\n        modifiedRange = modifiedRange.setEndPosition(modifiedRange.endLineNumber + 1, 1);\n    }\n    return new RangeMapping(originalRange, modifiedRange);\n}\nexport class DiffState {\n    static fromDiffResult(result) {\n        return new DiffState(result.changes.map(c => new DiffMapping(c)), result.moves || [], result.identical, result.quitEarly);\n    }\n    constructor(mappings, movedTexts, identical, quitEarly) {\n        this.mappings = mappings;\n        this.movedTexts = movedTexts;\n        this.identical = identical;\n        this.quitEarly = quitEarly;\n    }\n}\nexport class DiffMapping {\n    constructor(lineRangeMapping) {\n        this.lineRangeMapping = lineRangeMapping;\n        /*\n        readonly movedTo: MovedText | undefined,\n        readonly movedFrom: MovedText | undefined,\n\n        if (movedTo) {\n            assertFn(() =>\n                movedTo.lineRangeMapping.modifiedRange.equals(lineRangeMapping.modifiedRange)\n                && lineRangeMapping.originalRange.isEmpty\n                && !movedFrom\n            );\n        } else if (movedFrom) {\n            assertFn(() =>\n                movedFrom.lineRangeMapping.originalRange.equals(lineRangeMapping.originalRange)\n                && lineRangeMapping.modifiedRange.isEmpty\n                && !movedTo\n            );\n        }\n        */\n    }\n}\nexport class UnchangedRegion {\n    static fromDiffs(changes, originalLineCount, modifiedLineCount, minHiddenLineCount, minContext) {\n        const inversedMappings = DetailedLineRangeMapping.inverse(changes, originalLineCount, modifiedLineCount);\n        const result = [];\n        for (const mapping of inversedMappings) {\n            let origStart = mapping.original.startLineNumber;\n            let modStart = mapping.modified.startLineNumber;\n            let length = mapping.original.length;\n            const atStart = origStart === 1 && modStart === 1;\n            const atEnd = origStart + length === originalLineCount + 1 && modStart + length === modifiedLineCount + 1;\n            if ((atStart || atEnd) && length >= minContext + minHiddenLineCount) {\n                if (atStart && !atEnd) {\n                    length -= minContext;\n                }\n                if (atEnd && !atStart) {\n                    origStart += minContext;\n                    modStart += minContext;\n                    length -= minContext;\n                }\n                result.push(new UnchangedRegion(origStart, modStart, length, 0, 0));\n            }\n            else if (length >= minContext * 2 + minHiddenLineCount) {\n                origStart += minContext;\n                modStart += minContext;\n                length -= minContext * 2;\n                result.push(new UnchangedRegion(origStart, modStart, length, 0, 0));\n            }\n        }\n        return result;\n    }\n    get originalUnchangedRange() {\n        return LineRange.ofLength(this.originalLineNumber, this.lineCount);\n    }\n    get modifiedUnchangedRange() {\n        return LineRange.ofLength(this.modifiedLineNumber, this.lineCount);\n    }\n    constructor(originalLineNumber, modifiedLineNumber, lineCount, visibleLineCountTop, visibleLineCountBottom) {\n        this.originalLineNumber = originalLineNumber;\n        this.modifiedLineNumber = modifiedLineNumber;\n        this.lineCount = lineCount;\n        this._visibleLineCountTop = observableValue(this, 0);\n        this.visibleLineCountTop = this._visibleLineCountTop;\n        this._visibleLineCountBottom = observableValue(this, 0);\n        this.visibleLineCountBottom = this._visibleLineCountBottom;\n        this._shouldHideControls = derived(this, reader => /** @description isVisible */ this.visibleLineCountTop.read(reader) + this.visibleLineCountBottom.read(reader) === this.lineCount && !this.isDragged.read(reader));\n        this.isDragged = observableValue(this, undefined);\n        const visibleLineCountTop2 = Math.max(Math.min(visibleLineCountTop, this.lineCount), 0);\n        const visibleLineCountBottom2 = Math.max(Math.min(visibleLineCountBottom, this.lineCount - visibleLineCountTop), 0);\n        softAssert(visibleLineCountTop === visibleLineCountTop2);\n        softAssert(visibleLineCountBottom === visibleLineCountBottom2);\n        this._visibleLineCountTop.set(visibleLineCountTop2, undefined);\n        this._visibleLineCountBottom.set(visibleLineCountBottom2, undefined);\n    }\n    setVisibleRanges(visibleRanges, tx) {\n        const result = [];\n        const hiddenModified = new LineRangeSet(visibleRanges.map(r => r.modified)).subtractFrom(this.modifiedUnchangedRange);\n        let originalStartLineNumber = this.originalLineNumber;\n        let modifiedStartLineNumber = this.modifiedLineNumber;\n        const modifiedEndLineNumberEx = this.modifiedLineNumber + this.lineCount;\n        if (hiddenModified.ranges.length === 0) {\n            this.showAll(tx);\n            result.push(this);\n        }\n        else {\n            let i = 0;\n            for (const r of hiddenModified.ranges) {\n                const isLast = i === hiddenModified.ranges.length - 1;\n                i++;\n                const length = (isLast ? modifiedEndLineNumberEx : r.endLineNumberExclusive) - modifiedStartLineNumber;\n                const newR = new UnchangedRegion(originalStartLineNumber, modifiedStartLineNumber, length, 0, 0);\n                newR.setHiddenModifiedRange(r, tx);\n                result.push(newR);\n                originalStartLineNumber = newR.originalUnchangedRange.endLineNumberExclusive;\n                modifiedStartLineNumber = newR.modifiedUnchangedRange.endLineNumberExclusive;\n            }\n        }\n        return result;\n    }\n    shouldHideControls(reader) {\n        return this._shouldHideControls.read(reader);\n    }\n    getHiddenOriginalRange(reader) {\n        return LineRange.ofLength(this.originalLineNumber + this._visibleLineCountTop.read(reader), this.lineCount - this._visibleLineCountTop.read(reader) - this._visibleLineCountBottom.read(reader));\n    }\n    getHiddenModifiedRange(reader) {\n        return LineRange.ofLength(this.modifiedLineNumber + this._visibleLineCountTop.read(reader), this.lineCount - this._visibleLineCountTop.read(reader) - this._visibleLineCountBottom.read(reader));\n    }\n    setHiddenModifiedRange(range, tx) {\n        const visibleLineCountTop = range.startLineNumber - this.modifiedLineNumber;\n        const visibleLineCountBottom = (this.modifiedLineNumber + this.lineCount) - range.endLineNumberExclusive;\n        this.setState(visibleLineCountTop, visibleLineCountBottom, tx);\n    }\n    getMaxVisibleLineCountTop() {\n        return this.lineCount - this._visibleLineCountBottom.get();\n    }\n    getMaxVisibleLineCountBottom() {\n        return this.lineCount - this._visibleLineCountTop.get();\n    }\n    showMoreAbove(count = 10, tx) {\n        const maxVisibleLineCountTop = this.getMaxVisibleLineCountTop();\n        this._visibleLineCountTop.set(Math.min(this._visibleLineCountTop.get() + count, maxVisibleLineCountTop), tx);\n    }\n    showMoreBelow(count = 10, tx) {\n        const maxVisibleLineCountBottom = this.lineCount - this._visibleLineCountTop.get();\n        this._visibleLineCountBottom.set(Math.min(this._visibleLineCountBottom.get() + count, maxVisibleLineCountBottom), tx);\n    }\n    showAll(tx) {\n        this._visibleLineCountBottom.set(this.lineCount - this._visibleLineCountTop.get(), tx);\n    }\n    showModifiedLine(lineNumber, preference, tx) {\n        const top = lineNumber + 1 - (this.modifiedLineNumber + this._visibleLineCountTop.get());\n        const bottom = (this.modifiedLineNumber - this._visibleLineCountBottom.get() + this.lineCount) - lineNumber;\n        if (preference === 0 /* RevealPreference.FromCloserSide */ && top < bottom || preference === 1 /* RevealPreference.FromTop */) {\n            this._visibleLineCountTop.set(this._visibleLineCountTop.get() + top, tx);\n        }\n        else {\n            this._visibleLineCountBottom.set(this._visibleLineCountBottom.get() + bottom, tx);\n        }\n    }\n    showOriginalLine(lineNumber, preference, tx) {\n        const top = lineNumber - this.originalLineNumber;\n        const bottom = (this.originalLineNumber + this.lineCount) - lineNumber;\n        if (preference === 0 /* RevealPreference.FromCloserSide */ && top < bottom || preference === 1 /* RevealPreference.FromTop */) {\n            this._visibleLineCountTop.set(Math.min(this._visibleLineCountTop.get() + bottom - top, this.getMaxVisibleLineCountTop()), tx);\n        }\n        else {\n            this._visibleLineCountBottom.set(Math.min(this._visibleLineCountBottom.get() + top - bottom, this.getMaxVisibleLineCountBottom()), tx);\n        }\n    }\n    collapseAll(tx) {\n        this._visibleLineCountTop.set(0, tx);\n        this._visibleLineCountBottom.set(0, tx);\n    }\n    setState(visibleLineCountTop, visibleLineCountBottom, tx) {\n        visibleLineCountTop = Math.max(Math.min(visibleLineCountTop, this.lineCount), 0);\n        visibleLineCountBottom = Math.max(Math.min(visibleLineCountBottom, this.lineCount - visibleLineCountTop), 0);\n        this._visibleLineCountTop.set(visibleLineCountTop, tx);\n        this._visibleLineCountBottom.set(visibleLineCountBottom, tx);\n    }\n}\nfunction applyOriginalEdits(diff, textEdits, originalTextModel, modifiedTextModel) {\n    return undefined;\n    /*\n    TODO@hediet\n    if (textEdits.length === 0) {\n        return diff;\n    }\n\n    const diff2 = flip(diff);\n    const diff3 = applyModifiedEdits(diff2, textEdits, modifiedTextModel, originalTextModel);\n    if (!diff3) {\n        return undefined;\n    }\n    return flip(diff3);*/\n}\n/*\nfunction flip(diff: IDocumentDiff): IDocumentDiff {\n    return {\n        changes: diff.changes.map(c => c.flip()),\n        moves: diff.moves.map(m => m.flip()),\n        identical: diff.identical,\n        quitEarly: diff.quitEarly,\n    };\n}\n*/\nfunction applyModifiedEdits(diff, textEdits, originalTextModel, modifiedTextModel) {\n    return undefined;\n    /*\n    TODO@hediet\n    if (textEdits.length === 0) {\n        return diff;\n    }\n    if (diff.changes.some(c => !c.innerChanges) || diff.moves.length > 0) {\n        // TODO support these cases\n        return undefined;\n    }\n\n    const changes = applyModifiedEditsToLineRangeMappings(diff.changes, textEdits, originalTextModel, modifiedTextModel);\n\n    const moves = diff.moves.map(m => {\n        const newModifiedRange = applyEditToLineRange(m.lineRangeMapping.modified, textEdits);\n        return newModifiedRange ? new MovedText(\n            new SimpleLineRangeMapping(m.lineRangeMapping.original, newModifiedRange),\n            applyModifiedEditsToLineRangeMappings(m.changes, textEdits, originalTextModel, modifiedTextModel),\n        ) : undefined;\n    }).filter(isDefined);\n\n    return {\n        identical: false,\n        quitEarly: false,\n        changes,\n        moves,\n    };*/\n}\n/*\nfunction applyEditToLineRange(range: LineRange, textEdits: TextEditInfo[]): LineRange | undefined {\n    let rangeStartLineNumber = range.startLineNumber;\n    let rangeEndLineNumberEx = range.endLineNumberExclusive;\n\n    for (let i = textEdits.length - 1; i >= 0; i--) {\n        const textEdit = textEdits[i];\n        const textEditStartLineNumber = lengthGetLineCount(textEdit.startOffset) + 1;\n        const textEditEndLineNumber = lengthGetLineCount(textEdit.endOffset) + 1;\n        const newLengthLineCount = lengthGetLineCount(textEdit.newLength);\n        const delta = newLengthLineCount - (textEditEndLineNumber - textEditStartLineNumber);\n\n        if (textEditEndLineNumber < rangeStartLineNumber) {\n            // the text edit is before us\n            rangeStartLineNumber += delta;\n            rangeEndLineNumberEx += delta;\n        } else if (textEditStartLineNumber > rangeEndLineNumberEx) {\n            // the text edit is after us\n            // NOOP\n        } else if (textEditStartLineNumber < rangeStartLineNumber && rangeEndLineNumberEx < textEditEndLineNumber) {\n            // the range is fully contained in the text edit\n            return undefined;\n        } else if (textEditStartLineNumber < rangeStartLineNumber && textEditEndLineNumber <= rangeEndLineNumberEx) {\n            // the text edit ends inside our range\n            rangeStartLineNumber = textEditEndLineNumber + 1;\n            rangeStartLineNumber += delta;\n            rangeEndLineNumberEx += delta;\n        } else if (rangeStartLineNumber <= textEditStartLineNumber && textEditEndLineNumber < rangeStartLineNumber) {\n            // the text edit starts inside our range\n            rangeEndLineNumberEx = textEditStartLineNumber;\n        } else {\n            rangeEndLineNumberEx += delta;\n        }\n    }\n\n    return new LineRange(rangeStartLineNumber, rangeEndLineNumberEx);\n}\n\nfunction applyModifiedEditsToLineRangeMappings(changes: readonly LineRangeMapping[], textEdits: TextEditInfo[], originalTextModel: ITextModel, modifiedTextModel: ITextModel): LineRangeMapping[] {\n    const diffTextEdits = changes.flatMap(c => c.innerChanges!.map(c => new TextEditInfo(\n        positionToLength(c.originalRange.getStartPosition()),\n        positionToLength(c.originalRange.getEndPosition()),\n        lengthOfRange(c.modifiedRange).toLength(),\n    )));\n\n    const combined = combineTextEditInfos(diffTextEdits, textEdits);\n\n    let lastOriginalEndOffset = lengthZero;\n    let lastModifiedEndOffset = lengthZero;\n    const rangeMappings = combined.map(c => {\n        const modifiedStartOffset = lengthAdd(lastModifiedEndOffset, lengthDiffNonNegative(lastOriginalEndOffset, c.startOffset));\n        lastOriginalEndOffset = c.endOffset;\n        lastModifiedEndOffset = lengthAdd(modifiedStartOffset, c.newLength);\n\n        return new RangeMapping(\n            Range.fromPositions(lengthToPosition(c.startOffset), lengthToPosition(c.endOffset)),\n            Range.fromPositions(lengthToPosition(modifiedStartOffset), lengthToPosition(lastModifiedEndOffset)),\n        );\n    });\n\n    const newChanges = lineRangeMappingFromRangeMappings(\n        rangeMappings,\n        originalTextModel.getLinesContent(),\n        modifiedTextModel.getLinesContent(),\n    );\n    return newChanges;\n}\n*/\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA,IAAIA,UAAU,GAAI,IAAI,IAAI,IAAI,CAACA,UAAU,IAAK,UAAUC,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAE;EACnF,IAAIC,CAAC,GAAGC,SAAS,CAACC,MAAM;IAAEC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGH,MAAM,GAAGE,IAAI,KAAK,IAAI,GAAGA,IAAI,GAAGK,MAAM,CAACC,wBAAwB,CAACR,MAAM,EAAEC,GAAG,CAAC,GAAGC,IAAI;IAAEO,CAAC;EAC5H,IAAI,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,CAACC,QAAQ,KAAK,UAAU,EAAEL,CAAC,GAAGI,OAAO,CAACC,QAAQ,CAACZ,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,CAAC,CAAC,KAC1H,KAAK,IAAIU,CAAC,GAAGb,UAAU,CAACM,MAAM,GAAG,CAAC,EAAEO,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE,IAAIH,CAAC,GAAGV,UAAU,CAACa,CAAC,CAAC,EAAEN,CAAC,GAAG,CAACH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACH,CAAC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACT,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,GAAGG,CAAC,CAACT,MAAM,EAAEC,GAAG,CAAC,KAAKK,CAAC;EACjJ,OAAOH,CAAC,GAAG,CAAC,IAAIG,CAAC,IAAIC,MAAM,CAACM,cAAc,CAACb,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,EAAEA,CAAC;AACjE,CAAC;AACD,IAAIQ,OAAO,GAAI,IAAI,IAAI,IAAI,CAACA,OAAO,IAAK,UAAUC,UAAU,EAAEC,SAAS,EAAE;EACrE,OAAO,UAAUhB,MAAM,EAAEC,GAAG,EAAE;IAAEe,SAAS,CAAChB,MAAM,EAAEC,GAAG,EAAEc,UAAU,CAAC;EAAE,CAAC;AACzE,CAAC;AACD,SAASE,gBAAgB,QAAQ,kCAAkC;AACnE,SAASC,uBAAuB,QAAQ,yCAAyC;AACjF,SAASC,UAAU,EAAEC,YAAY,QAAQ,sCAAsC;AAC/E,SAASC,OAAO,EAAEC,gBAAgB,EAAEC,OAAO,EAAEC,gBAAgB,EAAEC,yBAAyB,EAAEC,eAAe,EAAEC,WAAW,EAAEC,YAAY,QAAQ,uCAAuC;AACnL,SAASC,2BAA2B,QAAQ,iCAAiC;AAC7E,SAASC,kBAAkB,QAAQ,YAAY;AAC/C,SAASC,uBAAuB,QAAQ,6CAA6C;AACrF,SAASC,SAAS,EAAEC,YAAY,QAAQ,mCAAmC;AAC3E,SAASC,wBAAwB,QAAQ,2EAA2E;AACpH,SAASC,wBAAwB,EAAEC,gBAAgB,EAAEC,YAAY,QAAQ,sCAAsC;AAC/G,SAASC,YAAY,QAAQ,8FAA8F;AAC3H,SAASC,oBAAoB,QAAQ,0FAA0F;AAC/H,SAASC,qBAAqB,QAAQ,iFAAiF;AACvH,SAASC,SAAS,QAAQ,kCAAkC;AAC5D,SAASC,eAAe,QAAQ,mCAAmC;AACnE,SAASC,UAAU,QAAQ,mCAAmC;AAC9D,IAAIC,mBAAmB,GAAG,MAAMA,mBAAmB,SAASzB,UAAU,CAAC;EACnE0B,kBAAkBA,CAACC,SAAS,EAAE;IAC1B,IAAI,CAACC,gBAAgB,CAACC,GAAG,CAACF,SAAS,EAAEG,SAAS,CAAC;EACnD;EACAC,WAAWA,CAACC,KAAK,EAAEC,QAAQ,EAAEC,2BAA2B,EAAE;IAAA,IAAAC,KAAA;IACtD,KAAK,CAAC,CAAC;IAAAA,KAAA,GAAAC,IAAA;IACP,IAAI,CAACJ,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,2BAA2B,GAAGA,2BAA2B;IAC9D,IAAI,CAACG,eAAe,GAAG9B,eAAe,CAAC,IAAI,EAAE,KAAK,CAAC;IACnD,IAAI,CAAC+B,cAAc,GAAG,IAAI,CAACD,eAAe;IAC1C,IAAI,CAACE,KAAK,GAAGhC,eAAe,CAAC,IAAI,EAAEuB,SAAS,CAAC;IAC7C,IAAI,CAACU,IAAI,GAAG,IAAI,CAACD,KAAK;IACtB,IAAI,CAACE,iBAAiB,GAAGlC,eAAe,CAAC,IAAI,EAAEuB,SAAS,CAAC;IACzD,IAAI,CAACY,gBAAgB,GAAGtC,OAAO,CAAC,IAAI,EAAEjB,CAAC,IAAI;MACvC,IAAI,IAAI,CAAC8C,QAAQ,CAACU,oBAAoB,CAACC,IAAI,CAACzD,CAAC,CAAC,EAAE;QAC5C,OAAO,IAAI,CAACsD,iBAAiB,CAACG,IAAI,CAACzD,CAAC,CAAC,EAAE0D,OAAO,IAAI,EAAE;MACxD,CAAC,MACI;QACD;QACArC,WAAW,CAACsC,EAAE,IAAI;UACd,KAAK,MAAM3D,CAAC,IAAI,IAAI,CAACsD,iBAAiB,CAACM,GAAG,CAAC,CAAC,EAAEF,OAAO,IAAI,EAAE,EAAE;YACzD1D,CAAC,CAAC6D,WAAW,CAACF,EAAE,CAAC;UACrB;QACJ,CAAC,CAAC;QACF,OAAO,EAAE;MACb;IACJ,CAAC,CAAC;IACF,IAAI,CAACG,kBAAkB,GAAG1C,eAAe,CAAC,IAAI,EAAEuB,SAAS,CAAC;IAC1D,IAAI,CAACF,gBAAgB,GAAGrB,eAAe,CAAC,IAAI,EAAEuB,SAAS,CAAC;IACxD,IAAI,CAACoB,iBAAiB,GAAG3C,eAAe,CAAC,IAAI,EAAEuB,SAAS,CAAC;IACzD,IAAI,CAACqB,eAAe,GAAG/C,OAAO,CAAC,IAAI,EAAEjB,CAAC,IAAI,IAAI,CAAC8D,kBAAkB,CAACL,IAAI,CAACzD,CAAC,CAAC,IAAI,IAAI,CAAC+D,iBAAiB,CAACN,IAAI,CAACzD,CAAC,CAAC,IAAI,IAAI,CAACyC,gBAAgB,CAACgB,IAAI,CAACzD,CAAC,CAAC,CAAC;IAC7I,IAAI,CAACiE,wBAAwB,GAAG,IAAIrD,uBAAuB,CAAC,CAAC;IAC7D,IAAI,CAACsD,aAAa,GAAGjD,OAAO,CAAC,IAAI,EAAEkD,MAAM,IAAI;MACzC,MAAMC,YAAY,GAAG,IAAI,CAACrB,2BAA2B,CAACsB,kBAAkB,CAAC;QACrEC,aAAa,EAAE,IAAI,CAACxB,QAAQ,CAACwB,aAAa,CAACb,IAAI,CAACU,MAAM;MAC1D,CAAC,CAAC;MACF,MAAMI,cAAc,GAAGpD,yBAAyB,CAAC,aAAa,EAAEiD,YAAY,CAACI,WAAW,CAAC;MACzF,OAAO;QACHJ,YAAY;QACZG;MACJ,CAAC;IACL,CAAC,CAAC;IACF,IAAI,CAACE,SAAS,CAAC3D,YAAY,CAAC,MAAM,IAAI,CAACmD,wBAAwB,CAACS,MAAM,CAAC,CAAC,CAAC,CAAC;IAC1E,MAAMC,oBAAoB,GAAGzD,gBAAgB,CAAC,sBAAsB,CAAC;IACrE,MAAM0D,SAAS,GAAG,IAAI,CAACH,SAAS,CAAC,IAAI9D,gBAAgB,CAAC,MAAMgE,oBAAoB,CAACE,OAAO,CAAClC,SAAS,CAAC,EAAE,GAAG,CAAC,CAAC;IAC1G,IAAI,CAAC8B,SAAS,CAAC1D,OAAO,CAACoD,MAAM,IAAI;MAC7B;MACA,MAAMW,oBAAoB,GAAG,IAAI,CAACxB,iBAAiB,CAACG,IAAI,CAACU,MAAM,CAAC;MAChE,IAAI,CAACW,oBAAoB,IAAIA,oBAAoB,CAACpB,OAAO,CAACqB,IAAI,CAAC/E,CAAC,IAAIA,CAAC,CAACgF,SAAS,CAACvB,IAAI,CAACU,MAAM,CAAC,CAAC,EAAE;QAC3F;MACJ;MACA,MAAMc,8BAA8B,GAAGH,oBAAoB,CAACI,qBAAqB,CAC5EC,GAAG,CAACC,EAAE,IAAIvC,KAAK,CAACwC,QAAQ,CAACC,kBAAkB,CAACF,EAAE,CAAC,CAAC,CAChDD,GAAG,CAACnF,CAAC,IAAIA,CAAC,GAAG0B,SAAS,CAAC6D,kBAAkB,CAACvF,CAAC,CAAC,GAAG2C,SAAS,CAAC;MAC9D,MAAM6C,6BAA6B,GAAGV,oBAAoB,CAACW,qBAAqB,CAC3EN,GAAG,CAACC,EAAE,IAAIvC,KAAK,CAAC6C,QAAQ,CAACJ,kBAAkB,CAACF,EAAE,CAAC,CAAC,CAChDD,GAAG,CAACnF,CAAC,IAAIA,CAAC,GAAG0B,SAAS,CAAC6D,kBAAkB,CAACvF,CAAC,CAAC,GAAG2C,SAAS,CAAC;MAC9D,MAAMgD,2BAA2B,GAAGb,oBAAoB,CAACpB,OAAO,CAACyB,GAAG,CAAC,CAACnF,CAAC,EAAE4F,GAAG,KAAM,CAACX,8BAA8B,CAACW,GAAG,CAAC,IAAI,CAACJ,6BAA6B,CAACI,GAAG,CAAC,GAAIjD,SAAS,GACtK,IAAIkD,eAAe,CAACZ,8BAA8B,CAACW,GAAG,CAAC,CAACE,eAAe,EAAEN,6BAA6B,CAACI,GAAG,CAAC,CAACE,eAAe,EAAEb,8BAA8B,CAACW,GAAG,CAAC,CAAC7F,MAAM,EAAEC,CAAC,CAAC+F,mBAAmB,CAACtC,IAAI,CAACU,MAAM,CAAC,EAAEnE,CAAC,CAACgG,sBAAsB,CAACvC,IAAI,CAACU,MAAM,CAAC,CAAC,CAAC,CAAC8B,MAAM,CAAC9D,SAAS,CAAC;MAC1Q,MAAM+D,SAAS,GAAG,EAAE;MACpB,IAAIC,SAAS,GAAG,KAAK;MACrB,KAAK,MAAMC,QAAQ,IAAIhE,eAAe,CAACuD,2BAA2B,EAAE,CAACU,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACE,sBAAsB,CAACpC,MAAM,CAAC,CAACqC,sBAAsB,KAAKF,CAAC,CAACC,sBAAsB,CAACpC,MAAM,CAAC,CAAC2B,eAAe,CAAC,EAAE;QACzL,IAAIM,QAAQ,CAACrG,MAAM,GAAG,CAAC,EAAE;UACrBoG,SAAS,GAAG,IAAI;UAChB,MAAMM,YAAY,GAAGL,QAAQ,CAACM,MAAM,CAAC,CAACC,GAAG,EAAE3G,CAAC,KAAK2G,GAAG,GAAG3G,CAAC,CAAC4G,SAAS,EAAE,CAAC,CAAC;UACtE,MAAM5G,CAAC,GAAG,IAAI6F,eAAe,CAACO,QAAQ,CAAC,CAAC,CAAC,CAACS,kBAAkB,EAAET,QAAQ,CAAC,CAAC,CAAC,CAACU,kBAAkB,EAAEL,YAAY,EAAEL,QAAQ,CAAC,CAAC,CAAC,CAACL,mBAAmB,CAACnC,GAAG,CAAC,CAAC,EAAEwC,QAAQ,CAACA,QAAQ,CAACrG,MAAM,GAAG,CAAC,CAAC,CAACiG,sBAAsB,CAACpC,GAAG,CAAC,CAAC,CAAC;UAC9MsC,SAAS,CAACa,IAAI,CAAC/G,CAAC,CAAC;QACrB,CAAC,MACI;UACDkG,SAAS,CAACa,IAAI,CAACX,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC/B;MACJ;MACA,IAAID,SAAS,EAAE;QACX,MAAMjB,qBAAqB,GAAGrC,KAAK,CAACwC,QAAQ,CAAC2B,gBAAgB,CAAClC,oBAAoB,CAACI,qBAAqB,EAAEgB,SAAS,CAACf,GAAG,CAACnF,CAAC,KAAK;UAAEiH,KAAK,EAAEjH,CAAC,CAACkH,sBAAsB,CAACC,gBAAgB,CAAC,CAAC;UAAEC,OAAO,EAAE;YAAEC,WAAW,EAAE;UAAY;QAAE,CAAC,CAAC,CAAC,CAAC;QAC9N,MAAM5B,qBAAqB,GAAG5C,KAAK,CAAC6C,QAAQ,CAACsB,gBAAgB,CAAClC,oBAAoB,CAACW,qBAAqB,EAAES,SAAS,CAACf,GAAG,CAACnF,CAAC,KAAK;UAAEiH,KAAK,EAAEjH,CAAC,CAACsH,sBAAsB,CAACH,gBAAgB,CAAC,CAAC;UAAEC,OAAO,EAAE;YAAEC,WAAW,EAAE;UAAY;QAAE,CAAC,CAAC,CAAC,CAAC;QAC9NhG,WAAW,CAACsC,EAAE,IAAI;UACd,IAAI,CAACL,iBAAiB,CAACZ,GAAG,CAAC;YACvBgB,OAAO,EAAEwC,SAAS;YAClBhB,qBAAqB;YACrBO;UACJ,CAAC,EAAE9B,EAAE,CAAC;QACV,CAAC,CAAC;MACN;IACJ,CAAC,CAAC,CAAC;IACH,MAAM4D,sBAAsB,GAAGA,CAACC,MAAM,EAAE7D,EAAE,EAAEQ,MAAM,KAAK;MACnD,MAAMsD,mBAAmB,GAAG5B,eAAe,CAAC6B,SAAS,CAACF,MAAM,CAACG,OAAO,EAAE9E,KAAK,CAACwC,QAAQ,CAACuC,YAAY,CAAC,CAAC,EAAE/E,KAAK,CAAC6C,QAAQ,CAACkC,YAAY,CAAC,CAAC,EAAE,IAAI,CAAC9E,QAAQ,CAAC+E,oCAAoC,CAACpE,IAAI,CAACU,MAAM,CAAC,EAAE,IAAI,CAACrB,QAAQ,CAACgF,oCAAoC,CAACrE,IAAI,CAACU,MAAM,CAAC,CAAC;MACrQ;MACA,IAAI4D,cAAc,GAAGpF,SAAS;MAC9B,MAAMmC,oBAAoB,GAAG,IAAI,CAACxB,iBAAiB,CAACM,GAAG,CAAC,CAAC;MACzD,IAAIkB,oBAAoB,EAAE;QACtB,MAAMG,8BAA8B,GAAGH,oBAAoB,CAACI,qBAAqB,CAC5EC,GAAG,CAACC,EAAE,IAAIvC,KAAK,CAACwC,QAAQ,CAACC,kBAAkB,CAACF,EAAE,CAAC,CAAC,CAChDD,GAAG,CAACnF,CAAC,IAAIA,CAAC,GAAG0B,SAAS,CAAC6D,kBAAkB,CAACvF,CAAC,CAAC,GAAG2C,SAAS,CAAC;QAC9D,MAAM6C,6BAA6B,GAAGV,oBAAoB,CAACW,qBAAqB,CAC3EN,GAAG,CAACC,EAAE,IAAIvC,KAAK,CAAC6C,QAAQ,CAACJ,kBAAkB,CAACF,EAAE,CAAC,CAAC,CAChDD,GAAG,CAACnF,CAAC,IAAIA,CAAC,GAAG0B,SAAS,CAAC6D,kBAAkB,CAACvF,CAAC,CAAC,GAAG2C,SAAS,CAAC;QAC9D,MAAMgD,2BAA2B,GAAGnE,kBAAkB,CAACsD,oBAAoB,CAACpB,OAAO,CAC9EyB,GAAG,CAAC,CAACnF,CAAC,EAAE4F,GAAG,KAAK;UACjB,IAAI,CAACX,8BAA8B,CAACW,GAAG,CAAC,IAAI,CAACJ,6BAA6B,CAACI,GAAG,CAAC,EAAE;YAC7E,OAAOjD,SAAS;UACpB;UACA,MAAM5C,MAAM,GAAGkF,8BAA8B,CAACW,GAAG,CAAC,CAAC7F,MAAM;UACzD,OAAO,IAAI8F,eAAe,CAACZ,8BAA8B,CAACW,GAAG,CAAC,CAACE,eAAe,EAAEN,6BAA6B,CAACI,GAAG,CAAC,CAACE,eAAe,EAAE/F,MAAM;UAC1I;UACAiI,IAAI,CAACC,GAAG,CAACjI,CAAC,CAAC+F,mBAAmB,CAACnC,GAAG,CAAC,CAAC,EAAE7D,MAAM,CAAC,EAAEiI,IAAI,CAACC,GAAG,CAACjI,CAAC,CAACgG,sBAAsB,CAACpC,GAAG,CAAC,CAAC,EAAE7D,MAAM,GAAGC,CAAC,CAAC+F,mBAAmB,CAACnC,GAAG,CAAC,CAAC,CAAC,CAAC;QAClI,CAAC,CAAC,CAACqC,MAAM,CAAC9D,SAAS,CAAC,EAAE,CAAC+F,GAAG,EAAEC,IAAI,KAAK,CAACA,IAAI,IAAKD,GAAG,CAACpB,kBAAkB,IAAIqB,IAAI,CAACrB,kBAAkB,GAAGqB,IAAI,CAACvB,SAAS,IAAIsB,GAAG,CAACrB,kBAAkB,IAAIsB,IAAI,CAACtB,kBAAkB,GAAGsB,IAAI,CAACvB,SAAU,CAAC;QACzL,IAAIwB,aAAa,GAAGzC,2BAA2B,CAACR,GAAG,CAACnF,CAAC,IAAI,IAAI8B,gBAAgB,CAAC9B,CAAC,CAACqI,sBAAsB,CAAClE,MAAM,CAAC,EAAEnE,CAAC,CAACuG,sBAAsB,CAACpC,MAAM,CAAC,CAAC,CAAC;QAClJiE,aAAa,GAAGtG,gBAAgB,CAACwG,IAAI,CAACF,aAAa,EAAE1G,SAAS,CAAC6G,QAAQ,CAAC,CAAC,EAAE1F,KAAK,CAACwC,QAAQ,CAACuC,YAAY,CAAC,CAAC,CAAC,EAAElG,SAAS,CAAC6G,QAAQ,CAAC,CAAC,EAAE1F,KAAK,CAAC6C,QAAQ,CAACkC,YAAY,CAAC,CAAC,CAAC,CAAC;QAChKG,cAAc,GAAGjG,gBAAgB,CAAC0G,OAAO,CAACJ,aAAa,EAAEvF,KAAK,CAACwC,QAAQ,CAACuC,YAAY,CAAC,CAAC,EAAE/E,KAAK,CAAC6C,QAAQ,CAACkC,YAAY,CAAC,CAAC,CAAC;MAC1H;MACA,MAAMa,oBAAoB,GAAG,EAAE;MAC/B,IAAIV,cAAc,EAAE;QAChB,KAAK,MAAM/H,CAAC,IAAIyH,mBAAmB,EAAE;UACjC,MAAMiB,YAAY,GAAGX,cAAc,CAAC9B,MAAM,CAAC0C,CAAC,IAAIA,CAAC,CAACtD,QAAQ,CAACuD,gBAAgB,CAAC5I,CAAC,CAACkH,sBAAsB,CAAC,IAAIyB,CAAC,CAACjD,QAAQ,CAACkD,gBAAgB,CAAC5I,CAAC,CAACsH,sBAAsB,CAAC,CAAC;UAC/JmB,oBAAoB,CAAC1B,IAAI,CAAC,GAAG/G,CAAC,CAAC6I,gBAAgB,CAACH,YAAY,EAAE/E,EAAE,CAAC,CAAC;QACtE;MACJ,CAAC,MACI;QACD8E,oBAAoB,CAAC1B,IAAI,CAAC,GAAGU,mBAAmB,CAAC;MACrD;MACA,MAAMvC,qBAAqB,GAAGrC,KAAK,CAACwC,QAAQ,CAAC2B,gBAAgB,CAAClC,oBAAoB,EAAEI,qBAAqB,IAAI,EAAE,EAAEuD,oBAAoB,CAACtD,GAAG,CAACnF,CAAC,KAAK;QAAEiH,KAAK,EAAEjH,CAAC,CAACkH,sBAAsB,CAACC,gBAAgB,CAAC,CAAC;QAAEC,OAAO,EAAE;UAAEC,WAAW,EAAE;QAAY;MAAE,CAAC,CAAC,CAAC,CAAC;MAChP,MAAM5B,qBAAqB,GAAG5C,KAAK,CAAC6C,QAAQ,CAACsB,gBAAgB,CAAClC,oBAAoB,EAAEW,qBAAqB,IAAI,EAAE,EAAEgD,oBAAoB,CAACtD,GAAG,CAACnF,CAAC,KAAK;QAAEiH,KAAK,EAAEjH,CAAC,CAACsH,sBAAsB,CAACH,gBAAgB,CAAC,CAAC;QAAEC,OAAO,EAAE;UAAEC,WAAW,EAAE;QAAY;MAAE,CAAC,CAAC,CAAC,CAAC;MAChP,IAAI,CAAC/D,iBAAiB,CAACZ,GAAG,CAAC;QACvBgB,OAAO,EAAE+E,oBAAoB;QAC7BvD,qBAAqB;QACrBO;MACJ,CAAC,EAAE9B,EAAE,CAAC;IACV,CAAC;IACD,IAAI,CAACc,SAAS,CAAC5B,KAAK,CAAC6C,QAAQ,CAACoD,kBAAkB,CAAEC,CAAC,IAAK;MACpD,MAAM1F,IAAI,GAAG,IAAI,CAACD,KAAK,CAACQ,GAAG,CAAC,CAAC;MAC7B,IAAIP,IAAI,EAAE;QACN,MAAM2F,SAAS,GAAGhH,YAAY,CAACiH,uBAAuB,CAACF,CAAC,CAACpB,OAAO,CAAC;QACjE,MAAMH,MAAM,GAAG0B,kBAAkB,CAAC,IAAI,CAACC,SAAS,EAAEH,SAAS,EAAEnG,KAAK,CAACwC,QAAQ,EAAExC,KAAK,CAAC6C,QAAQ,CAAC;QAC5F,IAAI8B,MAAM,EAAE;UACR,IAAI,CAAC2B,SAAS,GAAG3B,MAAM;UACvBnG,WAAW,CAACsC,EAAE,IAAI;YACd,IAAI,CAACP,KAAK,CAACV,GAAG,CAAC0G,SAAS,CAACC,cAAc,CAAC,IAAI,CAACF,SAAS,CAAC,EAAExF,EAAE,CAAC;YAC5D4D,sBAAsB,CAACC,MAAM,EAAE7D,EAAE,CAAC;YAClC,MAAM2F,sBAAsB,GAAG,IAAI,CAACxF,kBAAkB,CAACF,GAAG,CAAC,CAAC;YAC5D,IAAI,CAACE,kBAAkB,CAACpB,GAAG,CAAC4G,sBAAsB,GAAG,IAAI,CAACH,SAAS,CAACI,KAAK,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,gBAAgB,CAAChE,QAAQ,CAACiE,SAAS,CAACL,sBAAsB,CAACI,gBAAgB,CAAChE,QAAQ,CAAC,CAAC,GAAG/C,SAAS,EAAEgB,EAAE,CAAC;UACjM,CAAC,CAAC;QACN;MACJ;MACA,IAAI,CAACT,eAAe,CAACR,GAAG,CAAC,KAAK,EAAEC,SAAS,CAAC;MAC1CiC,SAAS,CAACgF,QAAQ,CAAC,CAAC;IACxB,CAAC,CAAC,CAAC;IACH,IAAI,CAACnF,SAAS,CAAC5B,KAAK,CAACwC,QAAQ,CAACyD,kBAAkB,CAAEC,CAAC,IAAK;MACpD,MAAM1F,IAAI,GAAG,IAAI,CAACD,KAAK,CAACQ,GAAG,CAAC,CAAC;MAC7B,IAAIP,IAAI,EAAE;QACN,MAAM2F,SAAS,GAAGhH,YAAY,CAACiH,uBAAuB,CAACF,CAAC,CAACpB,OAAO,CAAC;QACjE,MAAMH,MAAM,GAAGqC,kBAAkB,CAAC,IAAI,CAACV,SAAS,EAAEH,SAAS,EAAEnG,KAAK,CAACwC,QAAQ,EAAExC,KAAK,CAAC6C,QAAQ,CAAC;QAC5F,IAAI8B,MAAM,EAAE;UACR,IAAI,CAAC2B,SAAS,GAAG3B,MAAM;UACvBnG,WAAW,CAACsC,EAAE,IAAI;YACd,IAAI,CAACP,KAAK,CAACV,GAAG,CAAC0G,SAAS,CAACC,cAAc,CAAC,IAAI,CAACF,SAAS,CAAC,EAAExF,EAAE,CAAC;YAC5D4D,sBAAsB,CAACC,MAAM,EAAE7D,EAAE,CAAC;YAClC,MAAM2F,sBAAsB,GAAG,IAAI,CAACxF,kBAAkB,CAACF,GAAG,CAAC,CAAC;YAC5D,IAAI,CAACE,kBAAkB,CAACpB,GAAG,CAAC4G,sBAAsB,GAAG,IAAI,CAACH,SAAS,CAACI,KAAK,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,gBAAgB,CAAChE,QAAQ,CAACiE,SAAS,CAACL,sBAAsB,CAACI,gBAAgB,CAAChE,QAAQ,CAAC,CAAC,GAAG/C,SAAS,EAAEgB,EAAE,CAAC;UACjM,CAAC,CAAC;QACN;MACJ;MACA,IAAI,CAACT,eAAe,CAACR,GAAG,CAAC,KAAK,EAAEC,SAAS,CAAC;MAC1CiC,SAAS,CAACgF,QAAQ,CAAC,CAAC;IACxB,CAAC,CAAC,CAAC;IACH,IAAI,CAACnF,SAAS,CAACzD,gBAAgB;MAAA,IAAA8I,IAAA,GAAAC,iBAAA,CAAC,WAAO5F,MAAM,EAAE6F,KAAK,EAAK;QACrD;QACA;QACAhH,KAAI,CAACF,QAAQ,CAAC+E,oCAAoC,CAACpE,IAAI,CAACU,MAAM,CAAC;QAC/DnB,KAAI,CAACF,QAAQ,CAACgF,oCAAoC,CAACrE,IAAI,CAACU,MAAM,CAAC;QAC/DS,SAAS,CAACF,MAAM,CAAC,CAAC;QAClBC,oBAAoB,CAAClB,IAAI,CAACU,MAAM,CAAC;QACjC,MAAM8F,oBAAoB,GAAGjH,KAAI,CAACkB,aAAa,CAACT,IAAI,CAACU,MAAM,CAAC;QAC5D8F,oBAAoB,CAAC1F,cAAc,CAACd,IAAI,CAACU,MAAM,CAAC;QAChD1C,uBAAuB,CAACG,wBAAwB,EAAEuC,MAAM,CAAC;QACzD1C,uBAAuB,CAACS,qBAAqB,EAAEiC,MAAM,CAAC;QACtDnB,KAAI,CAACE,eAAe,CAACR,GAAG,CAAC,KAAK,EAAEC,SAAS,CAAC;QAC1C,IAAIuH,qBAAqB,GAAG,EAAE;QAC9BF,KAAK,CAACG,GAAG,CAACtH,KAAK,CAACwC,QAAQ,CAACyD,kBAAkB,CAAEC,CAAC,IAAK;UAC/C,MAAMqB,KAAK,GAAGpI,YAAY,CAACiH,uBAAuB,CAACF,CAAC,CAACpB,OAAO,CAAC;UAC7DuC,qBAAqB,GAAGjI,oBAAoB,CAACiI,qBAAqB,EAAEE,KAAK,CAAC;QAC9E,CAAC,CAAC,CAAC;QACH,IAAIC,qBAAqB,GAAG,EAAE;QAC9BL,KAAK,CAACG,GAAG,CAACtH,KAAK,CAAC6C,QAAQ,CAACoD,kBAAkB,CAAEC,CAAC,IAAK;UAC/C,MAAMqB,KAAK,GAAGpI,YAAY,CAACiH,uBAAuB,CAACF,CAAC,CAACpB,OAAO,CAAC;UAC7D0C,qBAAqB,GAAGpI,oBAAoB,CAACoI,qBAAqB,EAAED,KAAK,CAAC;QAC9E,CAAC,CAAC,CAAC;QACH,IAAI5C,MAAM,SAASyC,oBAAoB,CAAC7F,YAAY,CAACkG,WAAW,CAACzH,KAAK,CAACwC,QAAQ,EAAExC,KAAK,CAAC6C,QAAQ,EAAE;UAC7F6E,oBAAoB,EAAEvH,KAAI,CAACF,QAAQ,CAACyH,oBAAoB,CAAC9G,IAAI,CAACU,MAAM,CAAC;UACrEqG,oBAAoB,EAAExH,KAAI,CAACF,QAAQ,CAAC0H,oBAAoB,CAAC/G,IAAI,CAACU,MAAM,CAAC;UACrEsG,YAAY,EAAEzH,KAAI,CAACF,QAAQ,CAAC4H,SAAS,CAACjH,IAAI,CAACU,MAAM;QACrD,CAAC,EAAEnB,KAAI,CAACiB,wBAAwB,CAAC0G,KAAK,CAAC;QACvC,IAAI3H,KAAI,CAACiB,wBAAwB,CAAC0G,KAAK,CAACC,uBAAuB,EAAE;UAC7D;QACJ;QACA,IAAI/H,KAAK,CAACwC,QAAQ,CAACwF,UAAU,CAAC,CAAC,IAAIhI,KAAK,CAAC6C,QAAQ,CAACmF,UAAU,CAAC,CAAC,EAAE;UAC5D;UACA;QACJ;QACArD,MAAM,GAAGsD,qBAAqB,CAACtD,MAAM,EAAE3E,KAAK,CAACwC,QAAQ,EAAExC,KAAK,CAAC6C,QAAQ,CAAC;QACtE8B,MAAM,GAAGqC,kBAAkB,CAACrC,MAAM,EAAE0C,qBAAqB,EAAErH,KAAK,CAACwC,QAAQ,EAAExC,KAAK,CAAC6C,QAAQ,CAAC,IAAI8B,MAAM;QACpGA,MAAM,GAAG0B,kBAAkB,CAAC1B,MAAM,EAAE6C,qBAAqB,EAAExH,KAAK,CAACwC,QAAQ,EAAExC,KAAK,CAAC6C,QAAQ,CAAC,IAAI8B,MAAM;QACpGnG,WAAW,CAACsC,EAAE,IAAI;UACd;UACA4D,sBAAsB,CAACC,MAAM,EAAE7D,EAAE,CAAC;UAClCX,KAAI,CAACmG,SAAS,GAAG3B,MAAM;UACvB,MAAMuD,KAAK,GAAG3B,SAAS,CAACC,cAAc,CAAC7B,MAAM,CAAC;UAC9CxE,KAAI,CAACI,KAAK,CAACV,GAAG,CAACqI,KAAK,EAAEpH,EAAE,CAAC;UACzBX,KAAI,CAACE,eAAe,CAACR,GAAG,CAAC,IAAI,EAAEiB,EAAE,CAAC;UAClC,MAAM2F,sBAAsB,GAAGtG,KAAI,CAACc,kBAAkB,CAACF,GAAG,CAAC,CAAC;UAC5DZ,KAAI,CAACc,kBAAkB,CAACpB,GAAG,CAAC4G,sBAAsB,GAAGtG,KAAI,CAACmG,SAAS,CAACI,KAAK,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,gBAAgB,CAAChE,QAAQ,CAACiE,SAAS,CAACL,sBAAsB,CAACI,gBAAgB,CAAChE,QAAQ,CAAC,CAAC,GAAG/C,SAAS,EAAEgB,EAAE,CAAC;QACjM,CAAC,CAAC;MACN,CAAC;MAAA,iBAAAqH,EAAA,EAAAC,GAAA;QAAA,OAAAnB,IAAA,CAAAoB,KAAA,OAAApL,SAAA;MAAA;IAAA,IAAC,CAAC;EACP;EACAqL,2BAA2BA,CAACC,UAAU,EAAEC,UAAU,EAAE1H,EAAE,EAAE;IACpD,IAAI,IAAI,CAACN,IAAI,CAACO,GAAG,CAAC,CAAC,EAAE0H,QAAQ,CAACvL,MAAM,KAAK,CAAC,EAAE;MACxC;IACJ;IACA,MAAMwD,gBAAgB,GAAG,IAAI,CAACD,iBAAiB,CAACM,GAAG,CAAC,CAAC,EAAEF,OAAO,IAAI,EAAE;IACpE,KAAK,MAAM1D,CAAC,IAAIuD,gBAAgB,EAAE;MAC9B,IAAIvD,CAAC,CAACuG,sBAAsB,CAAC5D,SAAS,CAAC,CAAC4I,QAAQ,CAACH,UAAU,CAAC,EAAE;QAC1DpL,CAAC,CAACwL,gBAAgB,CAACJ,UAAU,EAAEC,UAAU,EAAE1H,EAAE,CAAC;QAC9C;MACJ;IACJ;EACJ;EACA8H,2BAA2BA,CAACL,UAAU,EAAEC,UAAU,EAAE1H,EAAE,EAAE;IACpD,IAAI,IAAI,CAACN,IAAI,CAACO,GAAG,CAAC,CAAC,EAAE0H,QAAQ,CAACvL,MAAM,KAAK,CAAC,EAAE;MACxC;IACJ;IACA,MAAMwD,gBAAgB,GAAG,IAAI,CAACD,iBAAiB,CAACM,GAAG,CAAC,CAAC,EAAEF,OAAO,IAAI,EAAE;IACpE,KAAK,MAAM1D,CAAC,IAAIuD,gBAAgB,EAAE;MAC9B,IAAIvD,CAAC,CAACqI,sBAAsB,CAAC1F,SAAS,CAAC,CAAC4I,QAAQ,CAACH,UAAU,CAAC,EAAE;QAC1DpL,CAAC,CAAC0L,gBAAgB,CAACN,UAAU,EAAEC,UAAU,EAAE1H,EAAE,CAAC;QAC9C;MACJ;IACJ;EACJ;EACMgI,WAAWA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAA7B,iBAAA;MAChB,MAAMzI,YAAY,CAACsK,MAAI,CAACzI,cAAc,EAAE0I,CAAC,IAAIA,CAAC,CAAC;IAAC;EACpD;EACAC,cAAcA,CAAA,EAAG;IACb,MAAMpI,OAAO,GAAG,IAAI,CAACJ,iBAAiB,CAACM,GAAG,CAAC,CAAC;IAC5C,OAAO;MACHmI,gBAAgB,EAAErI,OAAO,EAAEA,OAAO,CAACyB,GAAG,CAACnF,CAAC,KAAK;QAAEiH,KAAK,EAAEjH,CAAC,CAACuG,sBAAsB,CAAC5D,SAAS,CAAC,CAACqJ,SAAS,CAAC;MAAE,CAAC,CAAC;IAC5G,CAAC;EACL;EACAC,sBAAsBA,CAAClB,KAAK,EAAE;IAC1B,MAAMmB,MAAM,GAAGnB,KAAK,CAACgB,gBAAgB,EAAE5G,GAAG,CAACnF,CAAC,IAAI0B,SAAS,CAACyK,WAAW,CAACnM,CAAC,CAACiH,KAAK,CAAC,CAAC;IAC/E,MAAMvD,OAAO,GAAG,IAAI,CAACJ,iBAAiB,CAACM,GAAG,CAAC,CAAC;IAC5C,IAAI,CAACF,OAAO,IAAI,CAACwI,MAAM,EAAE;MACrB;IACJ;IACA7K,WAAW,CAACsC,EAAE,IAAI;MACd,KAAK,MAAM3D,CAAC,IAAI0D,OAAO,CAACA,OAAO,EAAE;QAC7B,KAAK,MAAMuD,KAAK,IAAIiF,MAAM,EAAE;UACxB,IAAIlM,CAAC,CAACsH,sBAAsB,CAACqC,SAAS,CAAC1C,KAAK,CAAC,EAAE;YAC3CjH,CAAC,CAACoM,sBAAsB,CAACnF,KAAK,EAAEtD,EAAE,CAAC;YACnC;UACJ;QACJ;MACJ;IACJ,CAAC,CAAC;EACN;AACJ,CAAC;AACDrB,mBAAmB,GAAG9C,UAAU,CAAC,CAC7BgB,OAAO,CAAC,CAAC,EAAEe,2BAA2B,CAAC,CAC1C,EAAEe,mBAAmB,CAAC;AACvB,SAASA,mBAAmB;AAC5B,SAASwI,qBAAqBA,CAACzH,IAAI,EAAEgC,QAAQ,EAAEK,QAAQ,EAAE;EACrD,OAAO;IACHiC,OAAO,EAAEtE,IAAI,CAACsE,OAAO,CAACxC,GAAG,CAACtF,CAAC,IAAI,IAAIgC,wBAAwB,CAAChC,CAAC,CAACwF,QAAQ,EAAExF,CAAC,CAAC6F,QAAQ,EAAE7F,CAAC,CAACwM,YAAY,GAAGxM,CAAC,CAACwM,YAAY,CAAClH,GAAG,CAAC7E,CAAC,IAAIgM,qBAAqB,CAAChM,CAAC,EAAE+E,QAAQ,EAAEK,QAAQ,CAAC,CAAC,GAAG/C,SAAS,CAAC,CAAC;IACxL4G,KAAK,EAAElG,IAAI,CAACkG,KAAK;IACjBgD,SAAS,EAAElJ,IAAI,CAACkJ,SAAS;IACzBC,SAAS,EAAEnJ,IAAI,CAACmJ;EACpB,CAAC;AACL;AACA,SAASF,qBAAqBA,CAACG,YAAY,EAAEpH,QAAQ,EAAEK,QAAQ,EAAE;EAC7D,IAAIgH,aAAa,GAAGD,YAAY,CAACC,aAAa;EAC9C,IAAIC,aAAa,GAAGF,YAAY,CAACE,aAAa;EAC9C,IAAID,aAAa,CAACE,WAAW,KAAK,CAAC,IAAID,aAAa,CAACC,WAAW,KAAK,CAAC,KACjEF,aAAa,CAACG,SAAS,KAAK,CAAC,IAAIF,aAAa,CAACE,SAAS,KAAK,CAAC,CAAC,IAChEH,aAAa,CAACG,SAAS,KAAKxH,QAAQ,CAACyH,gBAAgB,CAACJ,aAAa,CAACK,aAAa,CAAC,IAC/EJ,aAAa,CAACE,SAAS,KAAKnH,QAAQ,CAACoH,gBAAgB,CAACH,aAAa,CAACI,aAAa,CAAC,IAClFL,aAAa,CAACK,aAAa,GAAG1H,QAAQ,CAACuC,YAAY,CAAC,CAAC,IACrD+E,aAAa,CAACI,aAAa,GAAGrH,QAAQ,CAACkC,YAAY,CAAC,CAAC,EAAE;IAC1D8E,aAAa,GAAGA,aAAa,CAACM,cAAc,CAACN,aAAa,CAACK,aAAa,GAAG,CAAC,EAAE,CAAC,CAAC;IAChFJ,aAAa,GAAGA,aAAa,CAACK,cAAc,CAACL,aAAa,CAACI,aAAa,GAAG,CAAC,EAAE,CAAC,CAAC;EACpF;EACA,OAAO,IAAIhL,YAAY,CAAC2K,aAAa,EAAEC,aAAa,CAAC;AACzD;AACA,OAAO,MAAMvD,SAAS,CAAC;EACnB,OAAOC,cAAcA,CAAC7B,MAAM,EAAE;IAC1B,OAAO,IAAI4B,SAAS,CAAC5B,MAAM,CAACG,OAAO,CAACxC,GAAG,CAACtF,CAAC,IAAI,IAAIoN,WAAW,CAACpN,CAAC,CAAC,CAAC,EAAE2H,MAAM,CAAC+B,KAAK,IAAI,EAAE,EAAE/B,MAAM,CAAC+E,SAAS,EAAE/E,MAAM,CAACgF,SAAS,CAAC;EAC7H;EACA5J,WAAWA,CAAC0I,QAAQ,EAAE4B,UAAU,EAAEX,SAAS,EAAEC,SAAS,EAAE;IACpD,IAAI,CAAClB,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAAC4B,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACX,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,SAAS,GAAGA,SAAS;EAC9B;AACJ;AACA,OAAO,MAAMS,WAAW,CAAC;EACrBrK,WAAWA,CAAC8G,gBAAgB,EAAE;IAC1B,IAAI,CAACA,gBAAgB,GAAGA,gBAAgB;IACxC;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAEI;AACJ;AACA,OAAO,MAAM7D,eAAe,CAAC;EACzB,OAAO6B,SAASA,CAACC,OAAO,EAAEwF,iBAAiB,EAAEC,iBAAiB,EAAEC,kBAAkB,EAAEC,UAAU,EAAE;IAC5F,MAAMC,gBAAgB,GAAG1L,wBAAwB,CAAC2G,OAAO,CAACb,OAAO,EAAEwF,iBAAiB,EAAEC,iBAAiB,CAAC;IACxG,MAAM5F,MAAM,GAAG,EAAE;IACjB,KAAK,MAAMgG,OAAO,IAAID,gBAAgB,EAAE;MACpC,IAAIE,SAAS,GAAGD,OAAO,CAACnI,QAAQ,CAACS,eAAe;MAChD,IAAI4H,QAAQ,GAAGF,OAAO,CAAC9H,QAAQ,CAACI,eAAe;MAC/C,IAAI/F,MAAM,GAAGyN,OAAO,CAACnI,QAAQ,CAACtF,MAAM;MACpC,MAAM4N,OAAO,GAAGF,SAAS,KAAK,CAAC,IAAIC,QAAQ,KAAK,CAAC;MACjD,MAAME,KAAK,GAAGH,SAAS,GAAG1N,MAAM,KAAKoN,iBAAiB,GAAG,CAAC,IAAIO,QAAQ,GAAG3N,MAAM,KAAKqN,iBAAiB,GAAG,CAAC;MACzG,IAAI,CAACO,OAAO,IAAIC,KAAK,KAAK7N,MAAM,IAAIuN,UAAU,GAAGD,kBAAkB,EAAE;QACjE,IAAIM,OAAO,IAAI,CAACC,KAAK,EAAE;UACnB7N,MAAM,IAAIuN,UAAU;QACxB;QACA,IAAIM,KAAK,IAAI,CAACD,OAAO,EAAE;UACnBF,SAAS,IAAIH,UAAU;UACvBI,QAAQ,IAAIJ,UAAU;UACtBvN,MAAM,IAAIuN,UAAU;QACxB;QACA9F,MAAM,CAACT,IAAI,CAAC,IAAIlB,eAAe,CAAC4H,SAAS,EAAEC,QAAQ,EAAE3N,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;MACvE,CAAC,MACI,IAAIA,MAAM,IAAIuN,UAAU,GAAG,CAAC,GAAGD,kBAAkB,EAAE;QACpDI,SAAS,IAAIH,UAAU;QACvBI,QAAQ,IAAIJ,UAAU;QACtBvN,MAAM,IAAIuN,UAAU,GAAG,CAAC;QACxB9F,MAAM,CAACT,IAAI,CAAC,IAAIlB,eAAe,CAAC4H,SAAS,EAAEC,QAAQ,EAAE3N,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;MACvE;IACJ;IACA,OAAOyH,MAAM;EACjB;EACA,IAAIN,sBAAsBA,CAAA,EAAG;IACzB,OAAOxF,SAAS,CAAC6G,QAAQ,CAAC,IAAI,CAAC1B,kBAAkB,EAAE,IAAI,CAACD,SAAS,CAAC;EACtE;EACA,IAAIU,sBAAsBA,CAAA,EAAG;IACzB,OAAO5F,SAAS,CAAC6G,QAAQ,CAAC,IAAI,CAACzB,kBAAkB,EAAE,IAAI,CAACF,SAAS,CAAC;EACtE;EACAhE,WAAWA,CAACiE,kBAAkB,EAAEC,kBAAkB,EAAEF,SAAS,EAAEb,mBAAmB,EAAEC,sBAAsB,EAAE;IACxG,IAAI,CAACa,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACF,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACiH,oBAAoB,GAAGzM,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC;IACpD,IAAI,CAAC2E,mBAAmB,GAAG,IAAI,CAAC8H,oBAAoB;IACpD,IAAI,CAACC,uBAAuB,GAAG1M,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC;IACvD,IAAI,CAAC4E,sBAAsB,GAAG,IAAI,CAAC8H,uBAAuB;IAC1D,IAAI,CAACC,mBAAmB,GAAG9M,OAAO,CAAC,IAAI,EAAEkD,MAAM,IAAI,6BAA8B,IAAI,CAAC4B,mBAAmB,CAACtC,IAAI,CAACU,MAAM,CAAC,GAAG,IAAI,CAAC6B,sBAAsB,CAACvC,IAAI,CAACU,MAAM,CAAC,KAAK,IAAI,CAACyC,SAAS,IAAI,CAAC,IAAI,CAAC5B,SAAS,CAACvB,IAAI,CAACU,MAAM,CAAC,CAAC;IACrN,IAAI,CAACa,SAAS,GAAG5D,eAAe,CAAC,IAAI,EAAEuB,SAAS,CAAC;IACjD,MAAMqL,oBAAoB,GAAGhG,IAAI,CAACiG,GAAG,CAACjG,IAAI,CAACC,GAAG,CAAClC,mBAAmB,EAAE,IAAI,CAACa,SAAS,CAAC,EAAE,CAAC,CAAC;IACvF,MAAMsH,uBAAuB,GAAGlG,IAAI,CAACiG,GAAG,CAACjG,IAAI,CAACC,GAAG,CAACjC,sBAAsB,EAAE,IAAI,CAACY,SAAS,GAAGb,mBAAmB,CAAC,EAAE,CAAC,CAAC;IACnH1D,UAAU,CAAC0D,mBAAmB,KAAKiI,oBAAoB,CAAC;IACxD3L,UAAU,CAAC2D,sBAAsB,KAAKkI,uBAAuB,CAAC;IAC9D,IAAI,CAACL,oBAAoB,CAACnL,GAAG,CAACsL,oBAAoB,EAAErL,SAAS,CAAC;IAC9D,IAAI,CAACmL,uBAAuB,CAACpL,GAAG,CAACwL,uBAAuB,EAAEvL,SAAS,CAAC;EACxE;EACAkG,gBAAgBA,CAACsF,aAAa,EAAExK,EAAE,EAAE;IAChC,MAAM6D,MAAM,GAAG,EAAE;IACjB,MAAM4G,cAAc,GAAG,IAAIzM,YAAY,CAACwM,aAAa,CAAChJ,GAAG,CAACnF,CAAC,IAAIA,CAAC,CAAC0F,QAAQ,CAAC,CAAC,CAAC2I,YAAY,CAAC,IAAI,CAAC/G,sBAAsB,CAAC;IACrH,IAAIgH,uBAAuB,GAAG,IAAI,CAACzH,kBAAkB;IACrD,IAAI0H,uBAAuB,GAAG,IAAI,CAACzH,kBAAkB;IACrD,MAAM0H,uBAAuB,GAAG,IAAI,CAAC1H,kBAAkB,GAAG,IAAI,CAACF,SAAS;IACxE,IAAIwH,cAAc,CAAClC,MAAM,CAACnM,MAAM,KAAK,CAAC,EAAE;MACpC,IAAI,CAAC0O,OAAO,CAAC9K,EAAE,CAAC;MAChB6D,MAAM,CAACT,IAAI,CAAC,IAAI,CAAC;IACrB,CAAC,MACI;MACD,IAAIzG,CAAC,GAAG,CAAC;MACT,KAAK,MAAMN,CAAC,IAAIoO,cAAc,CAAClC,MAAM,EAAE;QACnC,MAAMwC,MAAM,GAAGpO,CAAC,KAAK8N,cAAc,CAAClC,MAAM,CAACnM,MAAM,GAAG,CAAC;QACrDO,CAAC,EAAE;QACH,MAAMP,MAAM,GAAG,CAAC2O,MAAM,GAAGF,uBAAuB,GAAGxO,CAAC,CAACwG,sBAAsB,IAAI+H,uBAAuB;QACtG,MAAMI,IAAI,GAAG,IAAI9I,eAAe,CAACyI,uBAAuB,EAAEC,uBAAuB,EAAExO,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;QAChG4O,IAAI,CAACvC,sBAAsB,CAACpM,CAAC,EAAE2D,EAAE,CAAC;QAClC6D,MAAM,CAACT,IAAI,CAAC4H,IAAI,CAAC;QACjBL,uBAAuB,GAAGK,IAAI,CAACzH,sBAAsB,CAACV,sBAAsB;QAC5E+H,uBAAuB,GAAGI,IAAI,CAACrH,sBAAsB,CAACd,sBAAsB;MAChF;IACJ;IACA,OAAOgB,MAAM;EACjB;EACAoH,kBAAkBA,CAACzK,MAAM,EAAE;IACvB,OAAO,IAAI,CAAC4J,mBAAmB,CAACtK,IAAI,CAACU,MAAM,CAAC;EAChD;EACAkE,sBAAsBA,CAAClE,MAAM,EAAE;IAC3B,OAAOzC,SAAS,CAAC6G,QAAQ,CAAC,IAAI,CAAC1B,kBAAkB,GAAG,IAAI,CAACgH,oBAAoB,CAACpK,IAAI,CAACU,MAAM,CAAC,EAAE,IAAI,CAACyC,SAAS,GAAG,IAAI,CAACiH,oBAAoB,CAACpK,IAAI,CAACU,MAAM,CAAC,GAAG,IAAI,CAAC2J,uBAAuB,CAACrK,IAAI,CAACU,MAAM,CAAC,CAAC;EACpM;EACAoC,sBAAsBA,CAACpC,MAAM,EAAE;IAC3B,OAAOzC,SAAS,CAAC6G,QAAQ,CAAC,IAAI,CAACzB,kBAAkB,GAAG,IAAI,CAAC+G,oBAAoB,CAACpK,IAAI,CAACU,MAAM,CAAC,EAAE,IAAI,CAACyC,SAAS,GAAG,IAAI,CAACiH,oBAAoB,CAACpK,IAAI,CAACU,MAAM,CAAC,GAAG,IAAI,CAAC2J,uBAAuB,CAACrK,IAAI,CAACU,MAAM,CAAC,CAAC;EACpM;EACAiI,sBAAsBA,CAACnF,KAAK,EAAEtD,EAAE,EAAE;IAC9B,MAAMoC,mBAAmB,GAAGkB,KAAK,CAACnB,eAAe,GAAG,IAAI,CAACgB,kBAAkB;IAC3E,MAAMd,sBAAsB,GAAI,IAAI,CAACc,kBAAkB,GAAG,IAAI,CAACF,SAAS,GAAIK,KAAK,CAACT,sBAAsB;IACxG,IAAI,CAACqI,QAAQ,CAAC9I,mBAAmB,EAAEC,sBAAsB,EAAErC,EAAE,CAAC;EAClE;EACAmL,yBAAyBA,CAAA,EAAG;IACxB,OAAO,IAAI,CAAClI,SAAS,GAAG,IAAI,CAACkH,uBAAuB,CAAClK,GAAG,CAAC,CAAC;EAC9D;EACAmL,4BAA4BA,CAAA,EAAG;IAC3B,OAAO,IAAI,CAACnI,SAAS,GAAG,IAAI,CAACiH,oBAAoB,CAACjK,GAAG,CAAC,CAAC;EAC3D;EACAoL,aAAaA,CAACC,KAAK,GAAG,EAAE,EAAEtL,EAAE,EAAE;IAC1B,MAAMuL,sBAAsB,GAAG,IAAI,CAACJ,yBAAyB,CAAC,CAAC;IAC/D,IAAI,CAACjB,oBAAoB,CAACnL,GAAG,CAACsF,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC4F,oBAAoB,CAACjK,GAAG,CAAC,CAAC,GAAGqL,KAAK,EAAEC,sBAAsB,CAAC,EAAEvL,EAAE,CAAC;EAChH;EACAwL,aAAaA,CAACF,KAAK,GAAG,EAAE,EAAEtL,EAAE,EAAE;IAC1B,MAAMyL,yBAAyB,GAAG,IAAI,CAACxI,SAAS,GAAG,IAAI,CAACiH,oBAAoB,CAACjK,GAAG,CAAC,CAAC;IAClF,IAAI,CAACkK,uBAAuB,CAACpL,GAAG,CAACsF,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC6F,uBAAuB,CAAClK,GAAG,CAAC,CAAC,GAAGqL,KAAK,EAAEG,yBAAyB,CAAC,EAAEzL,EAAE,CAAC;EACzH;EACA8K,OAAOA,CAAC9K,EAAE,EAAE;IACR,IAAI,CAACmK,uBAAuB,CAACpL,GAAG,CAAC,IAAI,CAACkE,SAAS,GAAG,IAAI,CAACiH,oBAAoB,CAACjK,GAAG,CAAC,CAAC,EAAED,EAAE,CAAC;EAC1F;EACA6H,gBAAgBA,CAACJ,UAAU,EAAEC,UAAU,EAAE1H,EAAE,EAAE;IACzC,MAAM0L,GAAG,GAAGjE,UAAU,GAAG,CAAC,IAAI,IAAI,CAACtE,kBAAkB,GAAG,IAAI,CAAC+G,oBAAoB,CAACjK,GAAG,CAAC,CAAC,CAAC;IACxF,MAAM0L,MAAM,GAAI,IAAI,CAACxI,kBAAkB,GAAG,IAAI,CAACgH,uBAAuB,CAAClK,GAAG,CAAC,CAAC,GAAG,IAAI,CAACgD,SAAS,GAAIwE,UAAU;IAC3G,IAAIC,UAAU,KAAK,CAAC,CAAC,yCAAyCgE,GAAG,GAAGC,MAAM,IAAIjE,UAAU,KAAK,CAAC,CAAC,gCAAgC;MAC3H,IAAI,CAACwC,oBAAoB,CAACnL,GAAG,CAAC,IAAI,CAACmL,oBAAoB,CAACjK,GAAG,CAAC,CAAC,GAAGyL,GAAG,EAAE1L,EAAE,CAAC;IAC5E,CAAC,MACI;MACD,IAAI,CAACmK,uBAAuB,CAACpL,GAAG,CAAC,IAAI,CAACoL,uBAAuB,CAAClK,GAAG,CAAC,CAAC,GAAG0L,MAAM,EAAE3L,EAAE,CAAC;IACrF;EACJ;EACA+H,gBAAgBA,CAACN,UAAU,EAAEC,UAAU,EAAE1H,EAAE,EAAE;IACzC,MAAM0L,GAAG,GAAGjE,UAAU,GAAG,IAAI,CAACvE,kBAAkB;IAChD,MAAMyI,MAAM,GAAI,IAAI,CAACzI,kBAAkB,GAAG,IAAI,CAACD,SAAS,GAAIwE,UAAU;IACtE,IAAIC,UAAU,KAAK,CAAC,CAAC,yCAAyCgE,GAAG,GAAGC,MAAM,IAAIjE,UAAU,KAAK,CAAC,CAAC,gCAAgC;MAC3H,IAAI,CAACwC,oBAAoB,CAACnL,GAAG,CAACsF,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC4F,oBAAoB,CAACjK,GAAG,CAAC,CAAC,GAAG0L,MAAM,GAAGD,GAAG,EAAE,IAAI,CAACP,yBAAyB,CAAC,CAAC,CAAC,EAAEnL,EAAE,CAAC;IACjI,CAAC,MACI;MACD,IAAI,CAACmK,uBAAuB,CAACpL,GAAG,CAACsF,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC6F,uBAAuB,CAAClK,GAAG,CAAC,CAAC,GAAGyL,GAAG,GAAGC,MAAM,EAAE,IAAI,CAACP,4BAA4B,CAAC,CAAC,CAAC,EAAEpL,EAAE,CAAC;IAC1I;EACJ;EACAE,WAAWA,CAACF,EAAE,EAAE;IACZ,IAAI,CAACkK,oBAAoB,CAACnL,GAAG,CAAC,CAAC,EAAEiB,EAAE,CAAC;IACpC,IAAI,CAACmK,uBAAuB,CAACpL,GAAG,CAAC,CAAC,EAAEiB,EAAE,CAAC;EAC3C;EACAkL,QAAQA,CAAC9I,mBAAmB,EAAEC,sBAAsB,EAAErC,EAAE,EAAE;IACtDoC,mBAAmB,GAAGiC,IAAI,CAACiG,GAAG,CAACjG,IAAI,CAACC,GAAG,CAAClC,mBAAmB,EAAE,IAAI,CAACa,SAAS,CAAC,EAAE,CAAC,CAAC;IAChFZ,sBAAsB,GAAGgC,IAAI,CAACiG,GAAG,CAACjG,IAAI,CAACC,GAAG,CAACjC,sBAAsB,EAAE,IAAI,CAACY,SAAS,GAAGb,mBAAmB,CAAC,EAAE,CAAC,CAAC;IAC5G,IAAI,CAAC8H,oBAAoB,CAACnL,GAAG,CAACqD,mBAAmB,EAAEpC,EAAE,CAAC;IACtD,IAAI,CAACmK,uBAAuB,CAACpL,GAAG,CAACsD,sBAAsB,EAAErC,EAAE,CAAC;EAChE;AACJ;AACA,SAASkG,kBAAkBA,CAACxG,IAAI,EAAE2F,SAAS,EAAEuG,iBAAiB,EAAEC,iBAAiB,EAAE;EAC/E,OAAO7M,SAAS;EAChB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASuG,kBAAkBA,CAAC7F,IAAI,EAAE2F,SAAS,EAAEuG,iBAAiB,EAAEC,iBAAiB,EAAE;EAC/E,OAAO7M,SAAS;EAChB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}