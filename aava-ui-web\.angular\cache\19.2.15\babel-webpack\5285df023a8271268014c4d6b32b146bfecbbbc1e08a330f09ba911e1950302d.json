{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { StandardWheelEvent } from '../../mouseEvent.js';\nimport { AbstractScrollbar } from './abstractScrollbar.js';\nimport { ARROW_IMG_SIZE } from './scrollbarArrow.js';\nimport { ScrollbarState } from './scrollbarState.js';\nimport { Codicon } from '../../../common/codicons.js';\nexport class HorizontalScrollbar extends AbstractScrollbar {\n  constructor(scrollable, options, host) {\n    const scrollDimensions = scrollable.getScrollDimensions();\n    const scrollPosition = scrollable.getCurrentScrollPosition();\n    super({\n      lazyRender: options.lazyRender,\n      host: host,\n      scrollbarState: new ScrollbarState(options.horizontalHasArrows ? options.arrowSize : 0, options.horizontal === 2 /* ScrollbarVisibility.Hidden */ ? 0 : options.horizontalScrollbarSize, options.vertical === 2 /* ScrollbarVisibility.Hidden */ ? 0 : options.verticalScrollbarSize, scrollDimensions.width, scrollDimensions.scrollWidth, scrollPosition.scrollLeft),\n      visibility: options.horizontal,\n      extraScrollbarClassName: 'horizontal',\n      scrollable: scrollable,\n      scrollByPage: options.scrollByPage\n    });\n    if (options.horizontalHasArrows) {\n      const arrowDelta = (options.arrowSize - ARROW_IMG_SIZE) / 2;\n      const scrollbarDelta = (options.horizontalScrollbarSize - ARROW_IMG_SIZE) / 2;\n      this._createArrow({\n        className: 'scra',\n        icon: Codicon.scrollbarButtonLeft,\n        top: scrollbarDelta,\n        left: arrowDelta,\n        bottom: undefined,\n        right: undefined,\n        bgWidth: options.arrowSize,\n        bgHeight: options.horizontalScrollbarSize,\n        onActivate: () => this._host.onMouseWheel(new StandardWheelEvent(null, 1, 0))\n      });\n      this._createArrow({\n        className: 'scra',\n        icon: Codicon.scrollbarButtonRight,\n        top: scrollbarDelta,\n        left: undefined,\n        bottom: undefined,\n        right: arrowDelta,\n        bgWidth: options.arrowSize,\n        bgHeight: options.horizontalScrollbarSize,\n        onActivate: () => this._host.onMouseWheel(new StandardWheelEvent(null, -1, 0))\n      });\n    }\n    this._createSlider(Math.floor((options.horizontalScrollbarSize - options.horizontalSliderSize) / 2), 0, undefined, options.horizontalSliderSize);\n  }\n  _updateSlider(sliderSize, sliderPosition) {\n    this.slider.setWidth(sliderSize);\n    this.slider.setLeft(sliderPosition);\n  }\n  _renderDomNode(largeSize, smallSize) {\n    this.domNode.setWidth(largeSize);\n    this.domNode.setHeight(smallSize);\n    this.domNode.setLeft(0);\n    this.domNode.setBottom(0);\n  }\n  onDidScroll(e) {\n    this._shouldRender = this._onElementScrollSize(e.scrollWidth) || this._shouldRender;\n    this._shouldRender = this._onElementScrollPosition(e.scrollLeft) || this._shouldRender;\n    this._shouldRender = this._onElementSize(e.width) || this._shouldRender;\n    return this._shouldRender;\n  }\n  _pointerDownRelativePosition(offsetX, offsetY) {\n    return offsetX;\n  }\n  _sliderPointerPosition(e) {\n    return e.pageX;\n  }\n  _sliderOrthogonalPointerPosition(e) {\n    return e.pageY;\n  }\n  _updateScrollbarSize(size) {\n    this.slider.setHeight(size);\n  }\n  writeScrollPosition(target, scrollPosition) {\n    target.scrollLeft = scrollPosition;\n  }\n  updateOptions(options) {\n    this.updateScrollbarSize(options.horizontal === 2 /* ScrollbarVisibility.Hidden */ ? 0 : options.horizontalScrollbarSize);\n    this._scrollbarState.setOppositeScrollbarSize(options.vertical === 2 /* ScrollbarVisibility.Hidden */ ? 0 : options.verticalScrollbarSize);\n    this._visibilityController.setVisibility(options.horizontal);\n    this._scrollByPage = options.scrollByPage;\n  }\n}", "map": {"version": 3, "names": ["StandardWheelEvent", "AbstractScrollbar", "ARROW_IMG_SIZE", "ScrollbarState", "Codicon", "HorizontalScrollbar", "constructor", "scrollable", "options", "host", "scrollDimensions", "getScrollDimensions", "scrollPosition", "getCurrentScrollPosition", "lazy<PERSON>ender", "scrollbarState", "horizontalHasArrows", "arrowSize", "horizontal", "horizontalScrollbarSize", "vertical", "verticalScrollbarSize", "width", "scrollWidth", "scrollLeft", "visibility", "extraScrollbarClassName", "scrollByPage", "arrow<PERSON><PERSON><PERSON>", "scrollbarDel<PERSON>", "_createArrow", "className", "icon", "scrollbarButtonLeft", "top", "left", "bottom", "undefined", "right", "bgWidth", "bgHeight", "onActivate", "_host", "onMouseWheel", "scrollbarButtonRight", "_createSlider", "Math", "floor", "horizontalSliderSize", "_updateSlider", "sliderSize", "sliderPosition", "slider", "<PERSON><PERSON><PERSON><PERSON>", "setLeft", "_renderDomNode", "largeSize", "smallSize", "domNode", "setHeight", "setBottom", "onDidScroll", "e", "_shouldRender", "_onElementScrollSize", "_onElementScrollPosition", "_onElementSize", "_pointerDownRelativePosition", "offsetX", "offsetY", "_sliderPointerPosition", "pageX", "_sliderOrthogonalPointerPosition", "pageY", "_updateScrollbarSize", "size", "writeScrollPosition", "target", "updateOptions", "updateScrollbarSize", "_scrollbarState", "setOppositeScrollbarSize", "_visibilityController", "setVisibility", "_scrollByPage"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/browser/ui/scrollbar/horizontalScrollbar.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { StandardWheelEvent } from '../../mouseEvent.js';\nimport { AbstractScrollbar } from './abstractScrollbar.js';\nimport { ARROW_IMG_SIZE } from './scrollbarArrow.js';\nimport { ScrollbarState } from './scrollbarState.js';\nimport { Codicon } from '../../../common/codicons.js';\nexport class HorizontalScrollbar extends AbstractScrollbar {\n    constructor(scrollable, options, host) {\n        const scrollDimensions = scrollable.getScrollDimensions();\n        const scrollPosition = scrollable.getCurrentScrollPosition();\n        super({\n            lazyRender: options.lazyRender,\n            host: host,\n            scrollbarState: new ScrollbarState((options.horizontalHasArrows ? options.arrowSize : 0), (options.horizontal === 2 /* ScrollbarVisibility.Hidden */ ? 0 : options.horizontalScrollbarSize), (options.vertical === 2 /* ScrollbarVisibility.Hidden */ ? 0 : options.verticalScrollbarSize), scrollDimensions.width, scrollDimensions.scrollWidth, scrollPosition.scrollLeft),\n            visibility: options.horizontal,\n            extraScrollbarClassName: 'horizontal',\n            scrollable: scrollable,\n            scrollByPage: options.scrollByPage\n        });\n        if (options.horizontalHasArrows) {\n            const arrowDelta = (options.arrowSize - ARROW_IMG_SIZE) / 2;\n            const scrollbarDelta = (options.horizontalScrollbarSize - ARROW_IMG_SIZE) / 2;\n            this._createArrow({\n                className: 'scra',\n                icon: Codicon.scrollbarButtonLeft,\n                top: scrollbarDelta,\n                left: arrowDelta,\n                bottom: undefined,\n                right: undefined,\n                bgWidth: options.arrowSize,\n                bgHeight: options.horizontalScrollbarSize,\n                onActivate: () => this._host.onMouseWheel(new StandardWheelEvent(null, 1, 0)),\n            });\n            this._createArrow({\n                className: 'scra',\n                icon: Codicon.scrollbarButtonRight,\n                top: scrollbarDelta,\n                left: undefined,\n                bottom: undefined,\n                right: arrowDelta,\n                bgWidth: options.arrowSize,\n                bgHeight: options.horizontalScrollbarSize,\n                onActivate: () => this._host.onMouseWheel(new StandardWheelEvent(null, -1, 0)),\n            });\n        }\n        this._createSlider(Math.floor((options.horizontalScrollbarSize - options.horizontalSliderSize) / 2), 0, undefined, options.horizontalSliderSize);\n    }\n    _updateSlider(sliderSize, sliderPosition) {\n        this.slider.setWidth(sliderSize);\n        this.slider.setLeft(sliderPosition);\n    }\n    _renderDomNode(largeSize, smallSize) {\n        this.domNode.setWidth(largeSize);\n        this.domNode.setHeight(smallSize);\n        this.domNode.setLeft(0);\n        this.domNode.setBottom(0);\n    }\n    onDidScroll(e) {\n        this._shouldRender = this._onElementScrollSize(e.scrollWidth) || this._shouldRender;\n        this._shouldRender = this._onElementScrollPosition(e.scrollLeft) || this._shouldRender;\n        this._shouldRender = this._onElementSize(e.width) || this._shouldRender;\n        return this._shouldRender;\n    }\n    _pointerDownRelativePosition(offsetX, offsetY) {\n        return offsetX;\n    }\n    _sliderPointerPosition(e) {\n        return e.pageX;\n    }\n    _sliderOrthogonalPointerPosition(e) {\n        return e.pageY;\n    }\n    _updateScrollbarSize(size) {\n        this.slider.setHeight(size);\n    }\n    writeScrollPosition(target, scrollPosition) {\n        target.scrollLeft = scrollPosition;\n    }\n    updateOptions(options) {\n        this.updateScrollbarSize(options.horizontal === 2 /* ScrollbarVisibility.Hidden */ ? 0 : options.horizontalScrollbarSize);\n        this._scrollbarState.setOppositeScrollbarSize(options.vertical === 2 /* ScrollbarVisibility.Hidden */ ? 0 : options.verticalScrollbarSize);\n        this._visibilityController.setVisibility(options.horizontal);\n        this._scrollByPage = options.scrollByPage;\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,kBAAkB,QAAQ,qBAAqB;AACxD,SAASC,iBAAiB,QAAQ,wBAAwB;AAC1D,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,OAAO,QAAQ,6BAA6B;AACrD,OAAO,MAAMC,mBAAmB,SAASJ,iBAAiB,CAAC;EACvDK,WAAWA,CAACC,UAAU,EAAEC,OAAO,EAAEC,IAAI,EAAE;IACnC,MAAMC,gBAAgB,GAAGH,UAAU,CAACI,mBAAmB,CAAC,CAAC;IACzD,MAAMC,cAAc,GAAGL,UAAU,CAACM,wBAAwB,CAAC,CAAC;IAC5D,KAAK,CAAC;MACFC,UAAU,EAAEN,OAAO,CAACM,UAAU;MAC9BL,IAAI,EAAEA,IAAI;MACVM,cAAc,EAAE,IAAIZ,cAAc,CAAEK,OAAO,CAACQ,mBAAmB,GAAGR,OAAO,CAACS,SAAS,GAAG,CAAC,EAAIT,OAAO,CAACU,UAAU,KAAK,CAAC,CAAC,mCAAmC,CAAC,GAAGV,OAAO,CAACW,uBAAuB,EAAIX,OAAO,CAACY,QAAQ,KAAK,CAAC,CAAC,mCAAmC,CAAC,GAAGZ,OAAO,CAACa,qBAAqB,EAAGX,gBAAgB,CAACY,KAAK,EAAEZ,gBAAgB,CAACa,WAAW,EAAEX,cAAc,CAACY,UAAU,CAAC;MAC5WC,UAAU,EAAEjB,OAAO,CAACU,UAAU;MAC9BQ,uBAAuB,EAAE,YAAY;MACrCnB,UAAU,EAAEA,UAAU;MACtBoB,YAAY,EAAEnB,OAAO,CAACmB;IAC1B,CAAC,CAAC;IACF,IAAInB,OAAO,CAACQ,mBAAmB,EAAE;MAC7B,MAAMY,UAAU,GAAG,CAACpB,OAAO,CAACS,SAAS,GAAGf,cAAc,IAAI,CAAC;MAC3D,MAAM2B,cAAc,GAAG,CAACrB,OAAO,CAACW,uBAAuB,GAAGjB,cAAc,IAAI,CAAC;MAC7E,IAAI,CAAC4B,YAAY,CAAC;QACdC,SAAS,EAAE,MAAM;QACjBC,IAAI,EAAE5B,OAAO,CAAC6B,mBAAmB;QACjCC,GAAG,EAAEL,cAAc;QACnBM,IAAI,EAAEP,UAAU;QAChBQ,MAAM,EAAEC,SAAS;QACjBC,KAAK,EAAED,SAAS;QAChBE,OAAO,EAAE/B,OAAO,CAACS,SAAS;QAC1BuB,QAAQ,EAAEhC,OAAO,CAACW,uBAAuB;QACzCsB,UAAU,EAAEA,CAAA,KAAM,IAAI,CAACC,KAAK,CAACC,YAAY,CAAC,IAAI3C,kBAAkB,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;MAChF,CAAC,CAAC;MACF,IAAI,CAAC8B,YAAY,CAAC;QACdC,SAAS,EAAE,MAAM;QACjBC,IAAI,EAAE5B,OAAO,CAACwC,oBAAoB;QAClCV,GAAG,EAAEL,cAAc;QACnBM,IAAI,EAAEE,SAAS;QACfD,MAAM,EAAEC,SAAS;QACjBC,KAAK,EAAEV,UAAU;QACjBW,OAAO,EAAE/B,OAAO,CAACS,SAAS;QAC1BuB,QAAQ,EAAEhC,OAAO,CAACW,uBAAuB;QACzCsB,UAAU,EAAEA,CAAA,KAAM,IAAI,CAACC,KAAK,CAACC,YAAY,CAAC,IAAI3C,kBAAkB,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;MACjF,CAAC,CAAC;IACN;IACA,IAAI,CAAC6C,aAAa,CAACC,IAAI,CAACC,KAAK,CAAC,CAACvC,OAAO,CAACW,uBAAuB,GAAGX,OAAO,CAACwC,oBAAoB,IAAI,CAAC,CAAC,EAAE,CAAC,EAAEX,SAAS,EAAE7B,OAAO,CAACwC,oBAAoB,CAAC;EACpJ;EACAC,aAAaA,CAACC,UAAU,EAAEC,cAAc,EAAE;IACtC,IAAI,CAACC,MAAM,CAACC,QAAQ,CAACH,UAAU,CAAC;IAChC,IAAI,CAACE,MAAM,CAACE,OAAO,CAACH,cAAc,CAAC;EACvC;EACAI,cAAcA,CAACC,SAAS,EAAEC,SAAS,EAAE;IACjC,IAAI,CAACC,OAAO,CAACL,QAAQ,CAACG,SAAS,CAAC;IAChC,IAAI,CAACE,OAAO,CAACC,SAAS,CAACF,SAAS,CAAC;IACjC,IAAI,CAACC,OAAO,CAACJ,OAAO,CAAC,CAAC,CAAC;IACvB,IAAI,CAACI,OAAO,CAACE,SAAS,CAAC,CAAC,CAAC;EAC7B;EACAC,WAAWA,CAACC,CAAC,EAAE;IACX,IAAI,CAACC,aAAa,GAAG,IAAI,CAACC,oBAAoB,CAACF,CAAC,CAACvC,WAAW,CAAC,IAAI,IAAI,CAACwC,aAAa;IACnF,IAAI,CAACA,aAAa,GAAG,IAAI,CAACE,wBAAwB,CAACH,CAAC,CAACtC,UAAU,CAAC,IAAI,IAAI,CAACuC,aAAa;IACtF,IAAI,CAACA,aAAa,GAAG,IAAI,CAACG,cAAc,CAACJ,CAAC,CAACxC,KAAK,CAAC,IAAI,IAAI,CAACyC,aAAa;IACvE,OAAO,IAAI,CAACA,aAAa;EAC7B;EACAI,4BAA4BA,CAACC,OAAO,EAAEC,OAAO,EAAE;IAC3C,OAAOD,OAAO;EAClB;EACAE,sBAAsBA,CAACR,CAAC,EAAE;IACtB,OAAOA,CAAC,CAACS,KAAK;EAClB;EACAC,gCAAgCA,CAACV,CAAC,EAAE;IAChC,OAAOA,CAAC,CAACW,KAAK;EAClB;EACAC,oBAAoBA,CAACC,IAAI,EAAE;IACvB,IAAI,CAACvB,MAAM,CAACO,SAAS,CAACgB,IAAI,CAAC;EAC/B;EACAC,mBAAmBA,CAACC,MAAM,EAAEjE,cAAc,EAAE;IACxCiE,MAAM,CAACrD,UAAU,GAAGZ,cAAc;EACtC;EACAkE,aAAaA,CAACtE,OAAO,EAAE;IACnB,IAAI,CAACuE,mBAAmB,CAACvE,OAAO,CAACU,UAAU,KAAK,CAAC,CAAC,mCAAmC,CAAC,GAAGV,OAAO,CAACW,uBAAuB,CAAC;IACzH,IAAI,CAAC6D,eAAe,CAACC,wBAAwB,CAACzE,OAAO,CAACY,QAAQ,KAAK,CAAC,CAAC,mCAAmC,CAAC,GAAGZ,OAAO,CAACa,qBAAqB,CAAC;IAC1I,IAAI,CAAC6D,qBAAqB,CAACC,aAAa,CAAC3E,OAAO,CAACU,UAAU,CAAC;IAC5D,IAAI,CAACkE,aAAa,GAAG5E,OAAO,CAACmB,YAAY;EAC7C;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}