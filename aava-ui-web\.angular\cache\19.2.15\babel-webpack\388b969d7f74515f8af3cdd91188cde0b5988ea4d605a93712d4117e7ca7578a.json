{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { BugIndicatingError } from '../../../base/common/errors.js';\nimport { LineRange } from '../core/lineRange.js';\nimport { Position } from '../core/position.js';\nimport { Range } from '../core/range.js';\nimport { SingleTextEdit } from '../core/textEdit.js';\n/**\n * Maps a line range in the original text model to a line range in the modified text model.\n */\nexport class LineRangeMapping {\n  static inverse(mapping, originalLineCount, modifiedLineCount) {\n    const result = [];\n    let lastOriginalEndLineNumber = 1;\n    let lastModifiedEndLineNumber = 1;\n    for (const m of mapping) {\n      const r = new LineRangeMapping(new LineRange(lastOriginalEndLineNumber, m.original.startLineNumber), new LineRange(lastModifiedEndLineNumber, m.modified.startLineNumber));\n      if (!r.modified.isEmpty) {\n        result.push(r);\n      }\n      lastOriginalEndLineNumber = m.original.endLineNumberExclusive;\n      lastModifiedEndLineNumber = m.modified.endLineNumberExclusive;\n    }\n    const r = new LineRangeMapping(new LineRange(lastOriginalEndLineNumber, originalLineCount + 1), new LineRange(lastModifiedEndLineNumber, modifiedLineCount + 1));\n    if (!r.modified.isEmpty) {\n      result.push(r);\n    }\n    return result;\n  }\n  static clip(mapping, originalRange, modifiedRange) {\n    const result = [];\n    for (const m of mapping) {\n      const original = m.original.intersect(originalRange);\n      const modified = m.modified.intersect(modifiedRange);\n      if (original && !original.isEmpty && modified && !modified.isEmpty) {\n        result.push(new LineRangeMapping(original, modified));\n      }\n    }\n    return result;\n  }\n  constructor(originalRange, modifiedRange) {\n    this.original = originalRange;\n    this.modified = modifiedRange;\n  }\n  toString() {\n    return `{${this.original.toString()}->${this.modified.toString()}}`;\n  }\n  flip() {\n    return new LineRangeMapping(this.modified, this.original);\n  }\n  join(other) {\n    return new LineRangeMapping(this.original.join(other.original), this.modified.join(other.modified));\n  }\n  /**\n   * This method assumes that the LineRangeMapping describes a valid diff!\n   * I.e. if one range is empty, the other range cannot be the entire document.\n   * It avoids various problems when the line range points to non-existing line-numbers.\n  */\n  toRangeMapping() {\n    const origInclusiveRange = this.original.toInclusiveRange();\n    const modInclusiveRange = this.modified.toInclusiveRange();\n    if (origInclusiveRange && modInclusiveRange) {\n      return new RangeMapping(origInclusiveRange, modInclusiveRange);\n    } else if (this.original.startLineNumber === 1 || this.modified.startLineNumber === 1) {\n      if (!(this.modified.startLineNumber === 1 && this.original.startLineNumber === 1)) {\n        // If one line range starts at 1, the other one must start at 1 as well.\n        throw new BugIndicatingError('not a valid diff');\n      }\n      // Because one range is empty and both ranges start at line 1, none of the ranges can cover all lines.\n      // Thus, `endLineNumberExclusive` is a valid line number.\n      return new RangeMapping(new Range(this.original.startLineNumber, 1, this.original.endLineNumberExclusive, 1), new Range(this.modified.startLineNumber, 1, this.modified.endLineNumberExclusive, 1));\n    } else {\n      // We can assume here that both startLineNumbers are greater than 1.\n      return new RangeMapping(new Range(this.original.startLineNumber - 1, Number.MAX_SAFE_INTEGER, this.original.endLineNumberExclusive - 1, Number.MAX_SAFE_INTEGER), new Range(this.modified.startLineNumber - 1, Number.MAX_SAFE_INTEGER, this.modified.endLineNumberExclusive - 1, Number.MAX_SAFE_INTEGER));\n    }\n  }\n  /**\n   * This method assumes that the LineRangeMapping describes a valid diff!\n   * I.e. if one range is empty, the other range cannot be the entire document.\n   * It avoids various problems when the line range points to non-existing line-numbers.\n  */\n  toRangeMapping2(original, modified) {\n    if (isValidLineNumber(this.original.endLineNumberExclusive, original) && isValidLineNumber(this.modified.endLineNumberExclusive, modified)) {\n      return new RangeMapping(new Range(this.original.startLineNumber, 1, this.original.endLineNumberExclusive, 1), new Range(this.modified.startLineNumber, 1, this.modified.endLineNumberExclusive, 1));\n    }\n    if (!this.original.isEmpty && !this.modified.isEmpty) {\n      return new RangeMapping(Range.fromPositions(new Position(this.original.startLineNumber, 1), normalizePosition(new Position(this.original.endLineNumberExclusive - 1, Number.MAX_SAFE_INTEGER), original)), Range.fromPositions(new Position(this.modified.startLineNumber, 1), normalizePosition(new Position(this.modified.endLineNumberExclusive - 1, Number.MAX_SAFE_INTEGER), modified)));\n    }\n    if (this.original.startLineNumber > 1 && this.modified.startLineNumber > 1) {\n      return new RangeMapping(Range.fromPositions(normalizePosition(new Position(this.original.startLineNumber - 1, Number.MAX_SAFE_INTEGER), original), normalizePosition(new Position(this.original.endLineNumberExclusive - 1, Number.MAX_SAFE_INTEGER), original)), Range.fromPositions(normalizePosition(new Position(this.modified.startLineNumber - 1, Number.MAX_SAFE_INTEGER), modified), normalizePosition(new Position(this.modified.endLineNumberExclusive - 1, Number.MAX_SAFE_INTEGER), modified)));\n    }\n    // Situation now: one range is empty and one range touches the last line and one range starts at line 1.\n    // I don't think this can happen.\n    throw new BugIndicatingError();\n  }\n}\nfunction normalizePosition(position, content) {\n  if (position.lineNumber < 1) {\n    return new Position(1, 1);\n  }\n  if (position.lineNumber > content.length) {\n    return new Position(content.length, content[content.length - 1].length + 1);\n  }\n  const line = content[position.lineNumber - 1];\n  if (position.column > line.length + 1) {\n    return new Position(position.lineNumber, line.length + 1);\n  }\n  return position;\n}\nfunction isValidLineNumber(lineNumber, lines) {\n  return lineNumber >= 1 && lineNumber <= lines.length;\n}\n/**\n * Maps a line range in the original text model to a line range in the modified text model.\n * Also contains inner range mappings.\n */\nexport class DetailedLineRangeMapping extends LineRangeMapping {\n  static fromRangeMappings(rangeMappings) {\n    const originalRange = LineRange.join(rangeMappings.map(r => LineRange.fromRangeInclusive(r.originalRange)));\n    const modifiedRange = LineRange.join(rangeMappings.map(r => LineRange.fromRangeInclusive(r.modifiedRange)));\n    return new DetailedLineRangeMapping(originalRange, modifiedRange, rangeMappings);\n  }\n  constructor(originalRange, modifiedRange, innerChanges) {\n    super(originalRange, modifiedRange);\n    this.innerChanges = innerChanges;\n  }\n  flip() {\n    return new DetailedLineRangeMapping(this.modified, this.original, this.innerChanges?.map(c => c.flip()));\n  }\n  withInnerChangesFromLineRanges() {\n    return new DetailedLineRangeMapping(this.original, this.modified, [this.toRangeMapping()]);\n  }\n}\n/**\n * Maps a range in the original text model to a range in the modified text model.\n */\nexport class RangeMapping {\n  static assertSorted(rangeMappings) {\n    for (let i = 1; i < rangeMappings.length; i++) {\n      const previous = rangeMappings[i - 1];\n      const current = rangeMappings[i];\n      if (!(previous.originalRange.getEndPosition().isBeforeOrEqual(current.originalRange.getStartPosition()) && previous.modifiedRange.getEndPosition().isBeforeOrEqual(current.modifiedRange.getStartPosition()))) {\n        throw new BugIndicatingError('Range mappings must be sorted');\n      }\n    }\n  }\n  constructor(originalRange, modifiedRange) {\n    this.originalRange = originalRange;\n    this.modifiedRange = modifiedRange;\n  }\n  toString() {\n    return `{${this.originalRange.toString()}->${this.modifiedRange.toString()}}`;\n  }\n  flip() {\n    return new RangeMapping(this.modifiedRange, this.originalRange);\n  }\n  /**\n   * Creates a single text edit that describes the change from the original to the modified text.\n  */\n  toTextEdit(modified) {\n    const newText = modified.getValueOfRange(this.modifiedRange);\n    return new SingleTextEdit(this.originalRange, newText);\n  }\n}", "map": {"version": 3, "names": ["BugIndicatingError", "LineRange", "Position", "Range", "SingleTextEdit", "LineRangeMapping", "inverse", "mapping", "originalLineCount", "modifiedLineCount", "result", "lastOriginalEndLineNumber", "lastModifiedEndLineNumber", "m", "r", "original", "startLineNumber", "modified", "isEmpty", "push", "endLineNumberExclusive", "clip", "originalRange", "modifiedRange", "intersect", "constructor", "toString", "flip", "join", "other", "toRangeMapping", "origInclusiveRange", "toInclusiveRange", "modInclusiveRange", "RangeMapping", "Number", "MAX_SAFE_INTEGER", "toRangeMapping2", "isValidLineNumber", "fromPositions", "normalizePosition", "position", "content", "lineNumber", "length", "line", "column", "lines", "DetailedLineRangeMapping", "fromRangeMappings", "rangeMappings", "map", "fromRangeInclusive", "innerChanges", "c", "withInnerChangesFromLineRanges", "assertSorted", "i", "previous", "current", "getEndPosition", "isBeforeOrEqual", "getStartPosition", "toTextEdit", "newText", "getValueOfRange"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/diff/rangeMapping.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { BugIndicatingError } from '../../../base/common/errors.js';\nimport { LineRange } from '../core/lineRange.js';\nimport { Position } from '../core/position.js';\nimport { Range } from '../core/range.js';\nimport { SingleTextEdit } from '../core/textEdit.js';\n/**\n * Maps a line range in the original text model to a line range in the modified text model.\n */\nexport class LineRangeMapping {\n    static inverse(mapping, originalLineCount, modifiedLineCount) {\n        const result = [];\n        let lastOriginalEndLineNumber = 1;\n        let lastModifiedEndLineNumber = 1;\n        for (const m of mapping) {\n            const r = new LineRangeMapping(new LineRange(lastOriginalEndLineNumber, m.original.startLineNumber), new LineRange(lastModifiedEndLineNumber, m.modified.startLineNumber));\n            if (!r.modified.isEmpty) {\n                result.push(r);\n            }\n            lastOriginalEndLineNumber = m.original.endLineNumberExclusive;\n            lastModifiedEndLineNumber = m.modified.endLineNumberExclusive;\n        }\n        const r = new LineRangeMapping(new LineRange(lastOriginalEndLineNumber, originalLineCount + 1), new LineRange(lastModifiedEndLineNumber, modifiedLineCount + 1));\n        if (!r.modified.isEmpty) {\n            result.push(r);\n        }\n        return result;\n    }\n    static clip(mapping, originalRange, modifiedRange) {\n        const result = [];\n        for (const m of mapping) {\n            const original = m.original.intersect(originalRange);\n            const modified = m.modified.intersect(modifiedRange);\n            if (original && !original.isEmpty && modified && !modified.isEmpty) {\n                result.push(new LineRangeMapping(original, modified));\n            }\n        }\n        return result;\n    }\n    constructor(originalRange, modifiedRange) {\n        this.original = originalRange;\n        this.modified = modifiedRange;\n    }\n    toString() {\n        return `{${this.original.toString()}->${this.modified.toString()}}`;\n    }\n    flip() {\n        return new LineRangeMapping(this.modified, this.original);\n    }\n    join(other) {\n        return new LineRangeMapping(this.original.join(other.original), this.modified.join(other.modified));\n    }\n    /**\n     * This method assumes that the LineRangeMapping describes a valid diff!\n     * I.e. if one range is empty, the other range cannot be the entire document.\n     * It avoids various problems when the line range points to non-existing line-numbers.\n    */\n    toRangeMapping() {\n        const origInclusiveRange = this.original.toInclusiveRange();\n        const modInclusiveRange = this.modified.toInclusiveRange();\n        if (origInclusiveRange && modInclusiveRange) {\n            return new RangeMapping(origInclusiveRange, modInclusiveRange);\n        }\n        else if (this.original.startLineNumber === 1 || this.modified.startLineNumber === 1) {\n            if (!(this.modified.startLineNumber === 1 && this.original.startLineNumber === 1)) {\n                // If one line range starts at 1, the other one must start at 1 as well.\n                throw new BugIndicatingError('not a valid diff');\n            }\n            // Because one range is empty and both ranges start at line 1, none of the ranges can cover all lines.\n            // Thus, `endLineNumberExclusive` is a valid line number.\n            return new RangeMapping(new Range(this.original.startLineNumber, 1, this.original.endLineNumberExclusive, 1), new Range(this.modified.startLineNumber, 1, this.modified.endLineNumberExclusive, 1));\n        }\n        else {\n            // We can assume here that both startLineNumbers are greater than 1.\n            return new RangeMapping(new Range(this.original.startLineNumber - 1, Number.MAX_SAFE_INTEGER, this.original.endLineNumberExclusive - 1, Number.MAX_SAFE_INTEGER), new Range(this.modified.startLineNumber - 1, Number.MAX_SAFE_INTEGER, this.modified.endLineNumberExclusive - 1, Number.MAX_SAFE_INTEGER));\n        }\n    }\n    /**\n     * This method assumes that the LineRangeMapping describes a valid diff!\n     * I.e. if one range is empty, the other range cannot be the entire document.\n     * It avoids various problems when the line range points to non-existing line-numbers.\n    */\n    toRangeMapping2(original, modified) {\n        if (isValidLineNumber(this.original.endLineNumberExclusive, original)\n            && isValidLineNumber(this.modified.endLineNumberExclusive, modified)) {\n            return new RangeMapping(new Range(this.original.startLineNumber, 1, this.original.endLineNumberExclusive, 1), new Range(this.modified.startLineNumber, 1, this.modified.endLineNumberExclusive, 1));\n        }\n        if (!this.original.isEmpty && !this.modified.isEmpty) {\n            return new RangeMapping(Range.fromPositions(new Position(this.original.startLineNumber, 1), normalizePosition(new Position(this.original.endLineNumberExclusive - 1, Number.MAX_SAFE_INTEGER), original)), Range.fromPositions(new Position(this.modified.startLineNumber, 1), normalizePosition(new Position(this.modified.endLineNumberExclusive - 1, Number.MAX_SAFE_INTEGER), modified)));\n        }\n        if (this.original.startLineNumber > 1 && this.modified.startLineNumber > 1) {\n            return new RangeMapping(Range.fromPositions(normalizePosition(new Position(this.original.startLineNumber - 1, Number.MAX_SAFE_INTEGER), original), normalizePosition(new Position(this.original.endLineNumberExclusive - 1, Number.MAX_SAFE_INTEGER), original)), Range.fromPositions(normalizePosition(new Position(this.modified.startLineNumber - 1, Number.MAX_SAFE_INTEGER), modified), normalizePosition(new Position(this.modified.endLineNumberExclusive - 1, Number.MAX_SAFE_INTEGER), modified)));\n        }\n        // Situation now: one range is empty and one range touches the last line and one range starts at line 1.\n        // I don't think this can happen.\n        throw new BugIndicatingError();\n    }\n}\nfunction normalizePosition(position, content) {\n    if (position.lineNumber < 1) {\n        return new Position(1, 1);\n    }\n    if (position.lineNumber > content.length) {\n        return new Position(content.length, content[content.length - 1].length + 1);\n    }\n    const line = content[position.lineNumber - 1];\n    if (position.column > line.length + 1) {\n        return new Position(position.lineNumber, line.length + 1);\n    }\n    return position;\n}\nfunction isValidLineNumber(lineNumber, lines) {\n    return lineNumber >= 1 && lineNumber <= lines.length;\n}\n/**\n * Maps a line range in the original text model to a line range in the modified text model.\n * Also contains inner range mappings.\n */\nexport class DetailedLineRangeMapping extends LineRangeMapping {\n    static fromRangeMappings(rangeMappings) {\n        const originalRange = LineRange.join(rangeMappings.map(r => LineRange.fromRangeInclusive(r.originalRange)));\n        const modifiedRange = LineRange.join(rangeMappings.map(r => LineRange.fromRangeInclusive(r.modifiedRange)));\n        return new DetailedLineRangeMapping(originalRange, modifiedRange, rangeMappings);\n    }\n    constructor(originalRange, modifiedRange, innerChanges) {\n        super(originalRange, modifiedRange);\n        this.innerChanges = innerChanges;\n    }\n    flip() {\n        return new DetailedLineRangeMapping(this.modified, this.original, this.innerChanges?.map(c => c.flip()));\n    }\n    withInnerChangesFromLineRanges() {\n        return new DetailedLineRangeMapping(this.original, this.modified, [this.toRangeMapping()]);\n    }\n}\n/**\n * Maps a range in the original text model to a range in the modified text model.\n */\nexport class RangeMapping {\n    static assertSorted(rangeMappings) {\n        for (let i = 1; i < rangeMappings.length; i++) {\n            const previous = rangeMappings[i - 1];\n            const current = rangeMappings[i];\n            if (!(previous.originalRange.getEndPosition().isBeforeOrEqual(current.originalRange.getStartPosition())\n                && previous.modifiedRange.getEndPosition().isBeforeOrEqual(current.modifiedRange.getStartPosition()))) {\n                throw new BugIndicatingError('Range mappings must be sorted');\n            }\n        }\n    }\n    constructor(originalRange, modifiedRange) {\n        this.originalRange = originalRange;\n        this.modifiedRange = modifiedRange;\n    }\n    toString() {\n        return `{${this.originalRange.toString()}->${this.modifiedRange.toString()}}`;\n    }\n    flip() {\n        return new RangeMapping(this.modifiedRange, this.originalRange);\n    }\n    /**\n     * Creates a single text edit that describes the change from the original to the modified text.\n    */\n    toTextEdit(modified) {\n        const newText = modified.getValueOfRange(this.modifiedRange);\n        return new SingleTextEdit(this.originalRange, newText);\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,kBAAkB,QAAQ,gCAAgC;AACnE,SAASC,SAAS,QAAQ,sBAAsB;AAChD,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,cAAc,QAAQ,qBAAqB;AACpD;AACA;AACA;AACA,OAAO,MAAMC,gBAAgB,CAAC;EAC1B,OAAOC,OAAOA,CAACC,OAAO,EAAEC,iBAAiB,EAAEC,iBAAiB,EAAE;IAC1D,MAAMC,MAAM,GAAG,EAAE;IACjB,IAAIC,yBAAyB,GAAG,CAAC;IACjC,IAAIC,yBAAyB,GAAG,CAAC;IACjC,KAAK,MAAMC,CAAC,IAAIN,OAAO,EAAE;MACrB,MAAMO,CAAC,GAAG,IAAIT,gBAAgB,CAAC,IAAIJ,SAAS,CAACU,yBAAyB,EAAEE,CAAC,CAACE,QAAQ,CAACC,eAAe,CAAC,EAAE,IAAIf,SAAS,CAACW,yBAAyB,EAAEC,CAAC,CAACI,QAAQ,CAACD,eAAe,CAAC,CAAC;MAC1K,IAAI,CAACF,CAAC,CAACG,QAAQ,CAACC,OAAO,EAAE;QACrBR,MAAM,CAACS,IAAI,CAACL,CAAC,CAAC;MAClB;MACAH,yBAAyB,GAAGE,CAAC,CAACE,QAAQ,CAACK,sBAAsB;MAC7DR,yBAAyB,GAAGC,CAAC,CAACI,QAAQ,CAACG,sBAAsB;IACjE;IACA,MAAMN,CAAC,GAAG,IAAIT,gBAAgB,CAAC,IAAIJ,SAAS,CAACU,yBAAyB,EAAEH,iBAAiB,GAAG,CAAC,CAAC,EAAE,IAAIP,SAAS,CAACW,yBAAyB,EAAEH,iBAAiB,GAAG,CAAC,CAAC,CAAC;IAChK,IAAI,CAACK,CAAC,CAACG,QAAQ,CAACC,OAAO,EAAE;MACrBR,MAAM,CAACS,IAAI,CAACL,CAAC,CAAC;IAClB;IACA,OAAOJ,MAAM;EACjB;EACA,OAAOW,IAAIA,CAACd,OAAO,EAAEe,aAAa,EAAEC,aAAa,EAAE;IAC/C,MAAMb,MAAM,GAAG,EAAE;IACjB,KAAK,MAAMG,CAAC,IAAIN,OAAO,EAAE;MACrB,MAAMQ,QAAQ,GAAGF,CAAC,CAACE,QAAQ,CAACS,SAAS,CAACF,aAAa,CAAC;MACpD,MAAML,QAAQ,GAAGJ,CAAC,CAACI,QAAQ,CAACO,SAAS,CAACD,aAAa,CAAC;MACpD,IAAIR,QAAQ,IAAI,CAACA,QAAQ,CAACG,OAAO,IAAID,QAAQ,IAAI,CAACA,QAAQ,CAACC,OAAO,EAAE;QAChER,MAAM,CAACS,IAAI,CAAC,IAAId,gBAAgB,CAACU,QAAQ,EAAEE,QAAQ,CAAC,CAAC;MACzD;IACJ;IACA,OAAOP,MAAM;EACjB;EACAe,WAAWA,CAACH,aAAa,EAAEC,aAAa,EAAE;IACtC,IAAI,CAACR,QAAQ,GAAGO,aAAa;IAC7B,IAAI,CAACL,QAAQ,GAAGM,aAAa;EACjC;EACAG,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,IAAI,CAACX,QAAQ,CAACW,QAAQ,CAAC,CAAC,KAAK,IAAI,CAACT,QAAQ,CAACS,QAAQ,CAAC,CAAC,GAAG;EACvE;EACAC,IAAIA,CAAA,EAAG;IACH,OAAO,IAAItB,gBAAgB,CAAC,IAAI,CAACY,QAAQ,EAAE,IAAI,CAACF,QAAQ,CAAC;EAC7D;EACAa,IAAIA,CAACC,KAAK,EAAE;IACR,OAAO,IAAIxB,gBAAgB,CAAC,IAAI,CAACU,QAAQ,CAACa,IAAI,CAACC,KAAK,CAACd,QAAQ,CAAC,EAAE,IAAI,CAACE,QAAQ,CAACW,IAAI,CAACC,KAAK,CAACZ,QAAQ,CAAC,CAAC;EACvG;EACA;AACJ;AACA;AACA;AACA;EACIa,cAAcA,CAAA,EAAG;IACb,MAAMC,kBAAkB,GAAG,IAAI,CAAChB,QAAQ,CAACiB,gBAAgB,CAAC,CAAC;IAC3D,MAAMC,iBAAiB,GAAG,IAAI,CAAChB,QAAQ,CAACe,gBAAgB,CAAC,CAAC;IAC1D,IAAID,kBAAkB,IAAIE,iBAAiB,EAAE;MACzC,OAAO,IAAIC,YAAY,CAACH,kBAAkB,EAAEE,iBAAiB,CAAC;IAClE,CAAC,MACI,IAAI,IAAI,CAAClB,QAAQ,CAACC,eAAe,KAAK,CAAC,IAAI,IAAI,CAACC,QAAQ,CAACD,eAAe,KAAK,CAAC,EAAE;MACjF,IAAI,EAAE,IAAI,CAACC,QAAQ,CAACD,eAAe,KAAK,CAAC,IAAI,IAAI,CAACD,QAAQ,CAACC,eAAe,KAAK,CAAC,CAAC,EAAE;QAC/E;QACA,MAAM,IAAIhB,kBAAkB,CAAC,kBAAkB,CAAC;MACpD;MACA;MACA;MACA,OAAO,IAAIkC,YAAY,CAAC,IAAI/B,KAAK,CAAC,IAAI,CAACY,QAAQ,CAACC,eAAe,EAAE,CAAC,EAAE,IAAI,CAACD,QAAQ,CAACK,sBAAsB,EAAE,CAAC,CAAC,EAAE,IAAIjB,KAAK,CAAC,IAAI,CAACc,QAAQ,CAACD,eAAe,EAAE,CAAC,EAAE,IAAI,CAACC,QAAQ,CAACG,sBAAsB,EAAE,CAAC,CAAC,CAAC;IACvM,CAAC,MACI;MACD;MACA,OAAO,IAAIc,YAAY,CAAC,IAAI/B,KAAK,CAAC,IAAI,CAACY,QAAQ,CAACC,eAAe,GAAG,CAAC,EAAEmB,MAAM,CAACC,gBAAgB,EAAE,IAAI,CAACrB,QAAQ,CAACK,sBAAsB,GAAG,CAAC,EAAEe,MAAM,CAACC,gBAAgB,CAAC,EAAE,IAAIjC,KAAK,CAAC,IAAI,CAACc,QAAQ,CAACD,eAAe,GAAG,CAAC,EAAEmB,MAAM,CAACC,gBAAgB,EAAE,IAAI,CAACnB,QAAQ,CAACG,sBAAsB,GAAG,CAAC,EAAEe,MAAM,CAACC,gBAAgB,CAAC,CAAC;IAC/S;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIC,eAAeA,CAACtB,QAAQ,EAAEE,QAAQ,EAAE;IAChC,IAAIqB,iBAAiB,CAAC,IAAI,CAACvB,QAAQ,CAACK,sBAAsB,EAAEL,QAAQ,CAAC,IAC9DuB,iBAAiB,CAAC,IAAI,CAACrB,QAAQ,CAACG,sBAAsB,EAAEH,QAAQ,CAAC,EAAE;MACtE,OAAO,IAAIiB,YAAY,CAAC,IAAI/B,KAAK,CAAC,IAAI,CAACY,QAAQ,CAACC,eAAe,EAAE,CAAC,EAAE,IAAI,CAACD,QAAQ,CAACK,sBAAsB,EAAE,CAAC,CAAC,EAAE,IAAIjB,KAAK,CAAC,IAAI,CAACc,QAAQ,CAACD,eAAe,EAAE,CAAC,EAAE,IAAI,CAACC,QAAQ,CAACG,sBAAsB,EAAE,CAAC,CAAC,CAAC;IACvM;IACA,IAAI,CAAC,IAAI,CAACL,QAAQ,CAACG,OAAO,IAAI,CAAC,IAAI,CAACD,QAAQ,CAACC,OAAO,EAAE;MAClD,OAAO,IAAIgB,YAAY,CAAC/B,KAAK,CAACoC,aAAa,CAAC,IAAIrC,QAAQ,CAAC,IAAI,CAACa,QAAQ,CAACC,eAAe,EAAE,CAAC,CAAC,EAAEwB,iBAAiB,CAAC,IAAItC,QAAQ,CAAC,IAAI,CAACa,QAAQ,CAACK,sBAAsB,GAAG,CAAC,EAAEe,MAAM,CAACC,gBAAgB,CAAC,EAAErB,QAAQ,CAAC,CAAC,EAAEZ,KAAK,CAACoC,aAAa,CAAC,IAAIrC,QAAQ,CAAC,IAAI,CAACe,QAAQ,CAACD,eAAe,EAAE,CAAC,CAAC,EAAEwB,iBAAiB,CAAC,IAAItC,QAAQ,CAAC,IAAI,CAACe,QAAQ,CAACG,sBAAsB,GAAG,CAAC,EAAEe,MAAM,CAACC,gBAAgB,CAAC,EAAEnB,QAAQ,CAAC,CAAC,CAAC;IACjY;IACA,IAAI,IAAI,CAACF,QAAQ,CAACC,eAAe,GAAG,CAAC,IAAI,IAAI,CAACC,QAAQ,CAACD,eAAe,GAAG,CAAC,EAAE;MACxE,OAAO,IAAIkB,YAAY,CAAC/B,KAAK,CAACoC,aAAa,CAACC,iBAAiB,CAAC,IAAItC,QAAQ,CAAC,IAAI,CAACa,QAAQ,CAACC,eAAe,GAAG,CAAC,EAAEmB,MAAM,CAACC,gBAAgB,CAAC,EAAErB,QAAQ,CAAC,EAAEyB,iBAAiB,CAAC,IAAItC,QAAQ,CAAC,IAAI,CAACa,QAAQ,CAACK,sBAAsB,GAAG,CAAC,EAAEe,MAAM,CAACC,gBAAgB,CAAC,EAAErB,QAAQ,CAAC,CAAC,EAAEZ,KAAK,CAACoC,aAAa,CAACC,iBAAiB,CAAC,IAAItC,QAAQ,CAAC,IAAI,CAACe,QAAQ,CAACD,eAAe,GAAG,CAAC,EAAEmB,MAAM,CAACC,gBAAgB,CAAC,EAAEnB,QAAQ,CAAC,EAAEuB,iBAAiB,CAAC,IAAItC,QAAQ,CAAC,IAAI,CAACe,QAAQ,CAACG,sBAAsB,GAAG,CAAC,EAAEe,MAAM,CAACC,gBAAgB,CAAC,EAAEnB,QAAQ,CAAC,CAAC,CAAC;IAC/e;IACA;IACA;IACA,MAAM,IAAIjB,kBAAkB,CAAC,CAAC;EAClC;AACJ;AACA,SAASwC,iBAAiBA,CAACC,QAAQ,EAAEC,OAAO,EAAE;EAC1C,IAAID,QAAQ,CAACE,UAAU,GAAG,CAAC,EAAE;IACzB,OAAO,IAAIzC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;EAC7B;EACA,IAAIuC,QAAQ,CAACE,UAAU,GAAGD,OAAO,CAACE,MAAM,EAAE;IACtC,OAAO,IAAI1C,QAAQ,CAACwC,OAAO,CAACE,MAAM,EAAEF,OAAO,CAACA,OAAO,CAACE,MAAM,GAAG,CAAC,CAAC,CAACA,MAAM,GAAG,CAAC,CAAC;EAC/E;EACA,MAAMC,IAAI,GAAGH,OAAO,CAACD,QAAQ,CAACE,UAAU,GAAG,CAAC,CAAC;EAC7C,IAAIF,QAAQ,CAACK,MAAM,GAAGD,IAAI,CAACD,MAAM,GAAG,CAAC,EAAE;IACnC,OAAO,IAAI1C,QAAQ,CAACuC,QAAQ,CAACE,UAAU,EAAEE,IAAI,CAACD,MAAM,GAAG,CAAC,CAAC;EAC7D;EACA,OAAOH,QAAQ;AACnB;AACA,SAASH,iBAAiBA,CAACK,UAAU,EAAEI,KAAK,EAAE;EAC1C,OAAOJ,UAAU,IAAI,CAAC,IAAIA,UAAU,IAAII,KAAK,CAACH,MAAM;AACxD;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMI,wBAAwB,SAAS3C,gBAAgB,CAAC;EAC3D,OAAO4C,iBAAiBA,CAACC,aAAa,EAAE;IACpC,MAAM5B,aAAa,GAAGrB,SAAS,CAAC2B,IAAI,CAACsB,aAAa,CAACC,GAAG,CAACrC,CAAC,IAAIb,SAAS,CAACmD,kBAAkB,CAACtC,CAAC,CAACQ,aAAa,CAAC,CAAC,CAAC;IAC3G,MAAMC,aAAa,GAAGtB,SAAS,CAAC2B,IAAI,CAACsB,aAAa,CAACC,GAAG,CAACrC,CAAC,IAAIb,SAAS,CAACmD,kBAAkB,CAACtC,CAAC,CAACS,aAAa,CAAC,CAAC,CAAC;IAC3G,OAAO,IAAIyB,wBAAwB,CAAC1B,aAAa,EAAEC,aAAa,EAAE2B,aAAa,CAAC;EACpF;EACAzB,WAAWA,CAACH,aAAa,EAAEC,aAAa,EAAE8B,YAAY,EAAE;IACpD,KAAK,CAAC/B,aAAa,EAAEC,aAAa,CAAC;IACnC,IAAI,CAAC8B,YAAY,GAAGA,YAAY;EACpC;EACA1B,IAAIA,CAAA,EAAG;IACH,OAAO,IAAIqB,wBAAwB,CAAC,IAAI,CAAC/B,QAAQ,EAAE,IAAI,CAACF,QAAQ,EAAE,IAAI,CAACsC,YAAY,EAAEF,GAAG,CAACG,CAAC,IAAIA,CAAC,CAAC3B,IAAI,CAAC,CAAC,CAAC,CAAC;EAC5G;EACA4B,8BAA8BA,CAAA,EAAG;IAC7B,OAAO,IAAIP,wBAAwB,CAAC,IAAI,CAACjC,QAAQ,EAAE,IAAI,CAACE,QAAQ,EAAE,CAAC,IAAI,CAACa,cAAc,CAAC,CAAC,CAAC,CAAC;EAC9F;AACJ;AACA;AACA;AACA;AACA,OAAO,MAAMI,YAAY,CAAC;EACtB,OAAOsB,YAAYA,CAACN,aAAa,EAAE;IAC/B,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,aAAa,CAACN,MAAM,EAAEa,CAAC,EAAE,EAAE;MAC3C,MAAMC,QAAQ,GAAGR,aAAa,CAACO,CAAC,GAAG,CAAC,CAAC;MACrC,MAAME,OAAO,GAAGT,aAAa,CAACO,CAAC,CAAC;MAChC,IAAI,EAAEC,QAAQ,CAACpC,aAAa,CAACsC,cAAc,CAAC,CAAC,CAACC,eAAe,CAACF,OAAO,CAACrC,aAAa,CAACwC,gBAAgB,CAAC,CAAC,CAAC,IAChGJ,QAAQ,CAACnC,aAAa,CAACqC,cAAc,CAAC,CAAC,CAACC,eAAe,CAACF,OAAO,CAACpC,aAAa,CAACuC,gBAAgB,CAAC,CAAC,CAAC,CAAC,EAAE;QACvG,MAAM,IAAI9D,kBAAkB,CAAC,+BAA+B,CAAC;MACjE;IACJ;EACJ;EACAyB,WAAWA,CAACH,aAAa,EAAEC,aAAa,EAAE;IACtC,IAAI,CAACD,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,aAAa,GAAGA,aAAa;EACtC;EACAG,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,IAAI,CAACJ,aAAa,CAACI,QAAQ,CAAC,CAAC,KAAK,IAAI,CAACH,aAAa,CAACG,QAAQ,CAAC,CAAC,GAAG;EACjF;EACAC,IAAIA,CAAA,EAAG;IACH,OAAO,IAAIO,YAAY,CAAC,IAAI,CAACX,aAAa,EAAE,IAAI,CAACD,aAAa,CAAC;EACnE;EACA;AACJ;AACA;EACIyC,UAAUA,CAAC9C,QAAQ,EAAE;IACjB,MAAM+C,OAAO,GAAG/C,QAAQ,CAACgD,eAAe,CAAC,IAAI,CAAC1C,aAAa,CAAC;IAC5D,OAAO,IAAInB,cAAc,CAAC,IAAI,CAACkB,aAAa,EAAE0C,OAAO,CAAC;EAC1D;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}