{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport var EditorOpenSource = /*#__PURE__*/function (EditorOpenSource) {\n  /**\n   * Default: the editor is opening via a programmatic call\n   * to the editor service API.\n   */\n  EditorOpenSource[EditorOpenSource[\"API\"] = 0] = \"API\";\n  /**\n   * Indicates that a user action triggered the opening, e.g.\n   * via mouse or keyboard use.\n   */\n  EditorOpenSource[EditorOpenSource[\"USER\"] = 1] = \"USER\";\n  return EditorOpenSource;\n}(EditorOpenSource || {});", "map": {"version": 3, "names": ["EditorOpenSource"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/platform/editor/common/editor.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport var EditorOpenSource;\n(function (EditorOpenSource) {\n    /**\n     * Default: the editor is opening via a programmatic call\n     * to the editor service API.\n     */\n    EditorOpenSource[EditorOpenSource[\"API\"] = 0] = \"API\";\n    /**\n     * Indicates that a user action triggered the opening, e.g.\n     * via mouse or keyboard use.\n     */\n    EditorOpenSource[EditorOpenSource[\"USER\"] = 1] = \"USER\";\n})(EditorOpenSource || (EditorOpenSource = {}));\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,IAAIA,gBAAgB,gBAC1B,UAAUA,gBAAgB,EAAE;EACzB;AACJ;AACA;AACA;EACIA,gBAAgB,CAACA,gBAAgB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK;EACrD;AACJ;AACA;AACA;EACIA,gBAAgB,CAACA,gBAAgB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EAAC,OAVjDA,gBAAgB;AAW3B,CAAC,CAAEA,gBAAgB,IAAwB,CAAC,CAAE,CAZnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}