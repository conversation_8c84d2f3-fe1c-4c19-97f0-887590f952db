{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { splitLines } from '../../../base/common/strings.js';\nimport { Position } from '../core/position.js';\nimport { PrefixSumComputer } from './prefixSumComputer.js';\nexport class MirrorTextModel {\n  constructor(uri, lines, eol, versionId) {\n    this._uri = uri;\n    this._lines = lines;\n    this._eol = eol;\n    this._versionId = versionId;\n    this._lineStarts = null;\n    this._cachedTextValue = null;\n  }\n  dispose() {\n    this._lines.length = 0;\n  }\n  get version() {\n    return this._versionId;\n  }\n  getText() {\n    if (this._cachedTextValue === null) {\n      this._cachedTextValue = this._lines.join(this._eol);\n    }\n    return this._cachedTextValue;\n  }\n  onEvents(e) {\n    if (e.eol && e.eol !== this._eol) {\n      this._eol = e.eol;\n      this._lineStarts = null;\n    }\n    // Update my lines\n    const changes = e.changes;\n    for (const change of changes) {\n      this._acceptDeleteRange(change.range);\n      this._acceptInsertText(new Position(change.range.startLineNumber, change.range.startColumn), change.text);\n    }\n    this._versionId = e.versionId;\n    this._cachedTextValue = null;\n  }\n  _ensureLineStarts() {\n    if (!this._lineStarts) {\n      const eolLength = this._eol.length;\n      const linesLength = this._lines.length;\n      const lineStartValues = new Uint32Array(linesLength);\n      for (let i = 0; i < linesLength; i++) {\n        lineStartValues[i] = this._lines[i].length + eolLength;\n      }\n      this._lineStarts = new PrefixSumComputer(lineStartValues);\n    }\n  }\n  /**\n   * All changes to a line's text go through this method\n   */\n  _setLineText(lineIndex, newValue) {\n    this._lines[lineIndex] = newValue;\n    if (this._lineStarts) {\n      // update prefix sum\n      this._lineStarts.setValue(lineIndex, this._lines[lineIndex].length + this._eol.length);\n    }\n  }\n  _acceptDeleteRange(range) {\n    if (range.startLineNumber === range.endLineNumber) {\n      if (range.startColumn === range.endColumn) {\n        // Nothing to delete\n        return;\n      }\n      // Delete text on the affected line\n      this._setLineText(range.startLineNumber - 1, this._lines[range.startLineNumber - 1].substring(0, range.startColumn - 1) + this._lines[range.startLineNumber - 1].substring(range.endColumn - 1));\n      return;\n    }\n    // Take remaining text on last line and append it to remaining text on first line\n    this._setLineText(range.startLineNumber - 1, this._lines[range.startLineNumber - 1].substring(0, range.startColumn - 1) + this._lines[range.endLineNumber - 1].substring(range.endColumn - 1));\n    // Delete middle lines\n    this._lines.splice(range.startLineNumber, range.endLineNumber - range.startLineNumber);\n    if (this._lineStarts) {\n      // update prefix sum\n      this._lineStarts.removeValues(range.startLineNumber, range.endLineNumber - range.startLineNumber);\n    }\n  }\n  _acceptInsertText(position, insertText) {\n    if (insertText.length === 0) {\n      // Nothing to insert\n      return;\n    }\n    const insertLines = splitLines(insertText);\n    if (insertLines.length === 1) {\n      // Inserting text on one line\n      this._setLineText(position.lineNumber - 1, this._lines[position.lineNumber - 1].substring(0, position.column - 1) + insertLines[0] + this._lines[position.lineNumber - 1].substring(position.column - 1));\n      return;\n    }\n    // Append overflowing text from first line to the end of text to insert\n    insertLines[insertLines.length - 1] += this._lines[position.lineNumber - 1].substring(position.column - 1);\n    // Delete overflowing text from first line and insert text on first line\n    this._setLineText(position.lineNumber - 1, this._lines[position.lineNumber - 1].substring(0, position.column - 1) + insertLines[0]);\n    // Insert new lines & store lengths\n    const newLengths = new Uint32Array(insertLines.length - 1);\n    for (let i = 1; i < insertLines.length; i++) {\n      this._lines.splice(position.lineNumber + i - 1, 0, insertLines[i]);\n      newLengths[i - 1] = insertLines[i].length + this._eol.length;\n    }\n    if (this._lineStarts) {\n      // update prefix sum\n      this._lineStarts.insertValues(position.lineNumber, newLengths);\n    }\n  }\n}", "map": {"version": 3, "names": ["splitLines", "Position", "PrefixSumComputer", "MirrorTextModel", "constructor", "uri", "lines", "eol", "versionId", "_uri", "_lines", "_eol", "_versionId", "_lineStarts", "_cachedTextValue", "dispose", "length", "version", "getText", "join", "onEvents", "e", "changes", "change", "_acceptDeleteRange", "range", "_acceptInsertText", "startLineNumber", "startColumn", "text", "_ensureLineStarts", "e<PERSON><PERSON><PERSON><PERSON>", "linesLength", "lineStartValues", "Uint32Array", "i", "_setLineText", "lineIndex", "newValue", "setValue", "endLineNumber", "endColumn", "substring", "splice", "removeValues", "position", "insertText", "insertLines", "lineNumber", "column", "newLengths", "insertValues"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/model/mirrorTextModel.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { splitLines } from '../../../base/common/strings.js';\nimport { Position } from '../core/position.js';\nimport { PrefixSumComputer } from './prefixSumComputer.js';\nexport class MirrorTextModel {\n    constructor(uri, lines, eol, versionId) {\n        this._uri = uri;\n        this._lines = lines;\n        this._eol = eol;\n        this._versionId = versionId;\n        this._lineStarts = null;\n        this._cachedTextValue = null;\n    }\n    dispose() {\n        this._lines.length = 0;\n    }\n    get version() {\n        return this._versionId;\n    }\n    getText() {\n        if (this._cachedTextValue === null) {\n            this._cachedTextValue = this._lines.join(this._eol);\n        }\n        return this._cachedTextValue;\n    }\n    onEvents(e) {\n        if (e.eol && e.eol !== this._eol) {\n            this._eol = e.eol;\n            this._lineStarts = null;\n        }\n        // Update my lines\n        const changes = e.changes;\n        for (const change of changes) {\n            this._acceptDeleteRange(change.range);\n            this._acceptInsertText(new Position(change.range.startLineNumber, change.range.startColumn), change.text);\n        }\n        this._versionId = e.versionId;\n        this._cachedTextValue = null;\n    }\n    _ensureLineStarts() {\n        if (!this._lineStarts) {\n            const eolLength = this._eol.length;\n            const linesLength = this._lines.length;\n            const lineStartValues = new Uint32Array(linesLength);\n            for (let i = 0; i < linesLength; i++) {\n                lineStartValues[i] = this._lines[i].length + eolLength;\n            }\n            this._lineStarts = new PrefixSumComputer(lineStartValues);\n        }\n    }\n    /**\n     * All changes to a line's text go through this method\n     */\n    _setLineText(lineIndex, newValue) {\n        this._lines[lineIndex] = newValue;\n        if (this._lineStarts) {\n            // update prefix sum\n            this._lineStarts.setValue(lineIndex, this._lines[lineIndex].length + this._eol.length);\n        }\n    }\n    _acceptDeleteRange(range) {\n        if (range.startLineNumber === range.endLineNumber) {\n            if (range.startColumn === range.endColumn) {\n                // Nothing to delete\n                return;\n            }\n            // Delete text on the affected line\n            this._setLineText(range.startLineNumber - 1, this._lines[range.startLineNumber - 1].substring(0, range.startColumn - 1)\n                + this._lines[range.startLineNumber - 1].substring(range.endColumn - 1));\n            return;\n        }\n        // Take remaining text on last line and append it to remaining text on first line\n        this._setLineText(range.startLineNumber - 1, this._lines[range.startLineNumber - 1].substring(0, range.startColumn - 1)\n            + this._lines[range.endLineNumber - 1].substring(range.endColumn - 1));\n        // Delete middle lines\n        this._lines.splice(range.startLineNumber, range.endLineNumber - range.startLineNumber);\n        if (this._lineStarts) {\n            // update prefix sum\n            this._lineStarts.removeValues(range.startLineNumber, range.endLineNumber - range.startLineNumber);\n        }\n    }\n    _acceptInsertText(position, insertText) {\n        if (insertText.length === 0) {\n            // Nothing to insert\n            return;\n        }\n        const insertLines = splitLines(insertText);\n        if (insertLines.length === 1) {\n            // Inserting text on one line\n            this._setLineText(position.lineNumber - 1, this._lines[position.lineNumber - 1].substring(0, position.column - 1)\n                + insertLines[0]\n                + this._lines[position.lineNumber - 1].substring(position.column - 1));\n            return;\n        }\n        // Append overflowing text from first line to the end of text to insert\n        insertLines[insertLines.length - 1] += this._lines[position.lineNumber - 1].substring(position.column - 1);\n        // Delete overflowing text from first line and insert text on first line\n        this._setLineText(position.lineNumber - 1, this._lines[position.lineNumber - 1].substring(0, position.column - 1)\n            + insertLines[0]);\n        // Insert new lines & store lengths\n        const newLengths = new Uint32Array(insertLines.length - 1);\n        for (let i = 1; i < insertLines.length; i++) {\n            this._lines.splice(position.lineNumber + i - 1, 0, insertLines[i]);\n            newLengths[i - 1] = insertLines[i].length + this._eol.length;\n        }\n        if (this._lineStarts) {\n            // update prefix sum\n            this._lineStarts.insertValues(position.lineNumber, newLengths);\n        }\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,UAAU,QAAQ,iCAAiC;AAC5D,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,iBAAiB,QAAQ,wBAAwB;AAC1D,OAAO,MAAMC,eAAe,CAAC;EACzBC,WAAWA,CAACC,GAAG,EAAEC,KAAK,EAAEC,GAAG,EAAEC,SAAS,EAAE;IACpC,IAAI,CAACC,IAAI,GAAGJ,GAAG;IACf,IAAI,CAACK,MAAM,GAAGJ,KAAK;IACnB,IAAI,CAACK,IAAI,GAAGJ,GAAG;IACf,IAAI,CAACK,UAAU,GAAGJ,SAAS;IAC3B,IAAI,CAACK,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,gBAAgB,GAAG,IAAI;EAChC;EACAC,OAAOA,CAAA,EAAG;IACN,IAAI,CAACL,MAAM,CAACM,MAAM,GAAG,CAAC;EAC1B;EACA,IAAIC,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACL,UAAU;EAC1B;EACAM,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACJ,gBAAgB,KAAK,IAAI,EAAE;MAChC,IAAI,CAACA,gBAAgB,GAAG,IAAI,CAACJ,MAAM,CAACS,IAAI,CAAC,IAAI,CAACR,IAAI,CAAC;IACvD;IACA,OAAO,IAAI,CAACG,gBAAgB;EAChC;EACAM,QAAQA,CAACC,CAAC,EAAE;IACR,IAAIA,CAAC,CAACd,GAAG,IAAIc,CAAC,CAACd,GAAG,KAAK,IAAI,CAACI,IAAI,EAAE;MAC9B,IAAI,CAACA,IAAI,GAAGU,CAAC,CAACd,GAAG;MACjB,IAAI,CAACM,WAAW,GAAG,IAAI;IAC3B;IACA;IACA,MAAMS,OAAO,GAAGD,CAAC,CAACC,OAAO;IACzB,KAAK,MAAMC,MAAM,IAAID,OAAO,EAAE;MAC1B,IAAI,CAACE,kBAAkB,CAACD,MAAM,CAACE,KAAK,CAAC;MACrC,IAAI,CAACC,iBAAiB,CAAC,IAAIzB,QAAQ,CAACsB,MAAM,CAACE,KAAK,CAACE,eAAe,EAAEJ,MAAM,CAACE,KAAK,CAACG,WAAW,CAAC,EAAEL,MAAM,CAACM,IAAI,CAAC;IAC7G;IACA,IAAI,CAACjB,UAAU,GAAGS,CAAC,CAACb,SAAS;IAC7B,IAAI,CAACM,gBAAgB,GAAG,IAAI;EAChC;EACAgB,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC,IAAI,CAACjB,WAAW,EAAE;MACnB,MAAMkB,SAAS,GAAG,IAAI,CAACpB,IAAI,CAACK,MAAM;MAClC,MAAMgB,WAAW,GAAG,IAAI,CAACtB,MAAM,CAACM,MAAM;MACtC,MAAMiB,eAAe,GAAG,IAAIC,WAAW,CAACF,WAAW,CAAC;MACpD,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,WAAW,EAAEG,CAAC,EAAE,EAAE;QAClCF,eAAe,CAACE,CAAC,CAAC,GAAG,IAAI,CAACzB,MAAM,CAACyB,CAAC,CAAC,CAACnB,MAAM,GAAGe,SAAS;MAC1D;MACA,IAAI,CAAClB,WAAW,GAAG,IAAIX,iBAAiB,CAAC+B,eAAe,CAAC;IAC7D;EACJ;EACA;AACJ;AACA;EACIG,YAAYA,CAACC,SAAS,EAAEC,QAAQ,EAAE;IAC9B,IAAI,CAAC5B,MAAM,CAAC2B,SAAS,CAAC,GAAGC,QAAQ;IACjC,IAAI,IAAI,CAACzB,WAAW,EAAE;MAClB;MACA,IAAI,CAACA,WAAW,CAAC0B,QAAQ,CAACF,SAAS,EAAE,IAAI,CAAC3B,MAAM,CAAC2B,SAAS,CAAC,CAACrB,MAAM,GAAG,IAAI,CAACL,IAAI,CAACK,MAAM,CAAC;IAC1F;EACJ;EACAQ,kBAAkBA,CAACC,KAAK,EAAE;IACtB,IAAIA,KAAK,CAACE,eAAe,KAAKF,KAAK,CAACe,aAAa,EAAE;MAC/C,IAAIf,KAAK,CAACG,WAAW,KAAKH,KAAK,CAACgB,SAAS,EAAE;QACvC;QACA;MACJ;MACA;MACA,IAAI,CAACL,YAAY,CAACX,KAAK,CAACE,eAAe,GAAG,CAAC,EAAE,IAAI,CAACjB,MAAM,CAACe,KAAK,CAACE,eAAe,GAAG,CAAC,CAAC,CAACe,SAAS,CAAC,CAAC,EAAEjB,KAAK,CAACG,WAAW,GAAG,CAAC,CAAC,GACjH,IAAI,CAAClB,MAAM,CAACe,KAAK,CAACE,eAAe,GAAG,CAAC,CAAC,CAACe,SAAS,CAACjB,KAAK,CAACgB,SAAS,GAAG,CAAC,CAAC,CAAC;MAC5E;IACJ;IACA;IACA,IAAI,CAACL,YAAY,CAACX,KAAK,CAACE,eAAe,GAAG,CAAC,EAAE,IAAI,CAACjB,MAAM,CAACe,KAAK,CAACE,eAAe,GAAG,CAAC,CAAC,CAACe,SAAS,CAAC,CAAC,EAAEjB,KAAK,CAACG,WAAW,GAAG,CAAC,CAAC,GACjH,IAAI,CAAClB,MAAM,CAACe,KAAK,CAACe,aAAa,GAAG,CAAC,CAAC,CAACE,SAAS,CAACjB,KAAK,CAACgB,SAAS,GAAG,CAAC,CAAC,CAAC;IAC1E;IACA,IAAI,CAAC/B,MAAM,CAACiC,MAAM,CAAClB,KAAK,CAACE,eAAe,EAAEF,KAAK,CAACe,aAAa,GAAGf,KAAK,CAACE,eAAe,CAAC;IACtF,IAAI,IAAI,CAACd,WAAW,EAAE;MAClB;MACA,IAAI,CAACA,WAAW,CAAC+B,YAAY,CAACnB,KAAK,CAACE,eAAe,EAAEF,KAAK,CAACe,aAAa,GAAGf,KAAK,CAACE,eAAe,CAAC;IACrG;EACJ;EACAD,iBAAiBA,CAACmB,QAAQ,EAAEC,UAAU,EAAE;IACpC,IAAIA,UAAU,CAAC9B,MAAM,KAAK,CAAC,EAAE;MACzB;MACA;IACJ;IACA,MAAM+B,WAAW,GAAG/C,UAAU,CAAC8C,UAAU,CAAC;IAC1C,IAAIC,WAAW,CAAC/B,MAAM,KAAK,CAAC,EAAE;MAC1B;MACA,IAAI,CAACoB,YAAY,CAACS,QAAQ,CAACG,UAAU,GAAG,CAAC,EAAE,IAAI,CAACtC,MAAM,CAACmC,QAAQ,CAACG,UAAU,GAAG,CAAC,CAAC,CAACN,SAAS,CAAC,CAAC,EAAEG,QAAQ,CAACI,MAAM,GAAG,CAAC,CAAC,GAC3GF,WAAW,CAAC,CAAC,CAAC,GACd,IAAI,CAACrC,MAAM,CAACmC,QAAQ,CAACG,UAAU,GAAG,CAAC,CAAC,CAACN,SAAS,CAACG,QAAQ,CAACI,MAAM,GAAG,CAAC,CAAC,CAAC;MAC1E;IACJ;IACA;IACAF,WAAW,CAACA,WAAW,CAAC/B,MAAM,GAAG,CAAC,CAAC,IAAI,IAAI,CAACN,MAAM,CAACmC,QAAQ,CAACG,UAAU,GAAG,CAAC,CAAC,CAACN,SAAS,CAACG,QAAQ,CAACI,MAAM,GAAG,CAAC,CAAC;IAC1G;IACA,IAAI,CAACb,YAAY,CAACS,QAAQ,CAACG,UAAU,GAAG,CAAC,EAAE,IAAI,CAACtC,MAAM,CAACmC,QAAQ,CAACG,UAAU,GAAG,CAAC,CAAC,CAACN,SAAS,CAAC,CAAC,EAAEG,QAAQ,CAACI,MAAM,GAAG,CAAC,CAAC,GAC3GF,WAAW,CAAC,CAAC,CAAC,CAAC;IACrB;IACA,MAAMG,UAAU,GAAG,IAAIhB,WAAW,CAACa,WAAW,CAAC/B,MAAM,GAAG,CAAC,CAAC;IAC1D,KAAK,IAAImB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,WAAW,CAAC/B,MAAM,EAAEmB,CAAC,EAAE,EAAE;MACzC,IAAI,CAACzB,MAAM,CAACiC,MAAM,CAACE,QAAQ,CAACG,UAAU,GAAGb,CAAC,GAAG,CAAC,EAAE,CAAC,EAAEY,WAAW,CAACZ,CAAC,CAAC,CAAC;MAClEe,UAAU,CAACf,CAAC,GAAG,CAAC,CAAC,GAAGY,WAAW,CAACZ,CAAC,CAAC,CAACnB,MAAM,GAAG,IAAI,CAACL,IAAI,CAACK,MAAM;IAChE;IACA,IAAI,IAAI,CAACH,WAAW,EAAE;MAClB;MACA,IAAI,CAACA,WAAW,CAACsC,YAAY,CAACN,QAAQ,CAACG,UAAU,EAAEE,UAAU,CAAC;IAClE;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}