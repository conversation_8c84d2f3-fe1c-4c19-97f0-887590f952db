{"ast": null, "code": "import { inject } from '@angular/core';\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\nimport { Router } from '@angular/router';\nimport { tap, BehaviorSubject, catchError, throwError, map, of } from 'rxjs';\nimport { TokenStorageService } from './token-storage.service';\nimport * as i0 from \"@angular/core\";\nexport let AuthService = /*#__PURE__*/(() => {\n  class AuthService {\n    http = inject(HttpClient);\n    router = inject(Router);\n    tokenStorage = inject(TokenStorageService);\n    authConfig = null;\n    authStateSubject = new BehaviorSubject(this.isAuthenticated());\n    authState$ = this.authStateSubject.asObservable();\n    refreshSubscription = null;\n    setAuthConfig(config) {\n      this.authConfig = config;\n    }\n    getAuthConfig() {\n      return this.authConfig;\n    }\n    getPostLoginRedirectUrl() {\n      return this.authConfig?.postLoginRedirectUrl || '/dashboard';\n    }\n    getApiAuthUrl() {\n      if (!this.authConfig?.apiAuthUrl) {\n        throw new Error('Auth configuration not set. Call setAuthConfig() first.');\n      }\n      return this.authConfig.apiAuthUrl;\n    }\n    getRedirectUrl() {\n      if (!this.authConfig?.redirectUrl) {\n        throw new Error('Auth configuration not set. Call setAuthConfig() first.');\n      }\n      return this.authConfig.redirectUrl;\n    }\n    getAuthHeaders() {\n      return new HttpHeaders({\n        Authorization: `Basic YWRtaW46YWRtaW4xMjM=`\n      });\n    }\n    getLoginFlow() {\n      return this.tokenStorage.getLoginType() || 'sso';\n    }\n    loginSSO(redirectUrl) {\n      const finalRedirectUrl = redirectUrl || this.getRedirectUrl();\n      const url = `${this.getApiAuthUrl()}/login-url?redirectUrl=${finalRedirectUrl}`;\n      return this.http.get(url, {\n        headers: this.getAuthHeaders()\n      }).pipe(tap(({\n        loginUrl\n      }) => {\n        window.location.href = loginUrl;\n      }));\n    }\n    exchangeCodeForToken(code, redirectUrl) {\n      const finalRedirectUrl = redirectUrl || this.getRedirectUrl();\n      const encodedRedirectUrl = encodeURIComponent(finalRedirectUrl);\n      const url = `${this.getApiAuthUrl()}/token?redirectUrl=${encodedRedirectUrl}`;\n      return this.http.post(url, {\n        code: code\n      }, {\n        headers: this.getAuthHeaders()\n      }).pipe(tap(response => {\n        this.tokenStorage.storeLoginType('sso');\n        const {\n          access_token: accessToken,\n          refresh_token: refreshToken,\n          expires_in: expiresIn,\n          user_name: da_name,\n          email: da_username,\n          id_token: idToken\n        } = response;\n        this.tokenStorage.storeTokens(accessToken, refreshToken, expiresIn);\n        this.tokenStorage.storeDaInfo(da_name, da_username, idToken);\n        this.authStateSubject.next(true);\n      }), map(response => ({\n        accessToken: response.access_token,\n        refreshToken: response.refresh_token\n      })), catchError(error => {\n        console.error('Token exchange failed:', error);\n        return throwError(() => error);\n      }));\n    }\n    refreshToken(refreshToken) {\n      const refreshTokenFromStorage = this.tokenStorage.getRefreshToken();\n      const url = `${this.getApiAuthUrl()}/refresh-token`;\n      return this.http.post(url, {\n        refreshToken: refreshToken ? refreshToken : refreshTokenFromStorage\n      }, {\n        headers: this.getAuthHeaders()\n      }).pipe(tap(response => {\n        const {\n          access_token: accessToken,\n          refresh_token: refreshToken,\n          expires_in: expiresIn,\n          user_name: da_name,\n          email: da_username,\n          id_token: idToken\n        } = response;\n        this.tokenStorage.storeTokens(accessToken, refreshToken, expiresIn);\n        this.tokenStorage.storeDaInfo(da_name, da_username, idToken);\n        this.authStateSubject.next(true);\n      }), map(response => ({\n        accessToken: response.access_token,\n        refreshToken: response.refresh_token\n      })), catchError(error => {\n        console.error('Token refresh failed:', error);\n        return throwError(() => error);\n      }));\n    }\n    logout(redirectUrl) {\n      const idToken = this.tokenStorage.getIdToken();\n      const finalRedirectUrl = redirectUrl || this.getRedirectUrl();\n      const url = `${this.getApiAuthUrl()}/logout-url?redirectUrl=${finalRedirectUrl}`;\n      const headers = this.getAuthHeaders().set('X-ID-TOKEN', idToken || '');\n      return this.http.get(url, {\n        headers\n      }).pipe(tap(({\n        logoutUrl\n      }) => {\n        this.tokenStorage.clearTokens();\n        this.authStateSubject.next(false);\n        window.location.href = logoutUrl;\n      }), catchError(error => {\n        console.error('Logout failed:', error);\n        return throwError(() => error);\n      }));\n    }\n    isAuthenticated() {\n      return !!this.tokenStorage.getAccessToken();\n    }\n    basicLoginWithCredentials(username, password) {\n      const url = `${this.getApiAuthUrl()}/basic/login`;\n      const loginBody = {\n        userName: username,\n        password: password\n      };\n      return this.http.post(url, loginBody, {\n        headers: this.getAuthHeaders()\n      }).pipe(tap(response => {\n        const {\n          access_token: accessToken,\n          refresh_token: refreshToken,\n          expires_in: expiresIn,\n          user_name: da_name,\n          email: da_username,\n          id_token: idToken\n        } = response;\n        this.tokenStorage.storeTokens(accessToken, refreshToken, expiresIn);\n        this.tokenStorage.storeDaInfo(da_name, da_username, idToken);\n        this.authStateSubject.next(true);\n      }), catchError(error => {\n        console.error('Basic login failed:', error);\n        return throwError(() => error);\n      }));\n    }\n    basicRefreshToken() {\n      const refreshToken = this.tokenStorage.getRefreshToken();\n      const url = `${this.getApiAuthUrl()}/basic/refresh/token`;\n      return this.http.post(url, {\n        refreshToken\n      }, {\n        headers: this.getAuthHeaders()\n      }).pipe(tap(response => {\n        const {\n          access_token: accessToken,\n          refresh_token: refreshToken,\n          expires_in: expiresIn,\n          user_name: da_name,\n          email: da_username,\n          id_token: idToken\n        } = response;\n        this.tokenStorage.storeTokens(accessToken, refreshToken, expiresIn);\n        this.tokenStorage.storeDaInfo(da_name, da_username, idToken);\n        this.authStateSubject.next(true);\n      }), catchError(error => {\n        console.error('Basic token refresh failed:', error);\n        return throwError(() => error);\n      }));\n    }\n    clearAuthState() {\n      this.tokenStorage.clearTokens();\n      this.authStateSubject.next(false);\n    }\n    basicLogout() {\n      const refreshToken = this.tokenStorage.getRefreshToken();\n      if (!refreshToken) {\n        this.clearAuthState();\n        return of(void 0);\n      }\n      const url = `${this.getApiAuthUrl()}/basic/logout`;\n      const headers = this.getAuthHeaders().set('X-REFRESH-TOKEN', refreshToken);\n      return this.http.post(url, null, {\n        headers,\n        responseType: 'text'\n      }).pipe(map(response => {\n        const normalizedResponse = response.trim();\n        this.clearAuthState();\n        if (normalizedResponse !== 'Logout successful.') {\n          console.warn('Unexpected logout response:', response);\n        }\n        return void 0;\n      }), catchError(error => {\n        this.clearAuthState();\n        console.error('Basic logout failed:', error);\n        return throwError(() => error);\n      }));\n    }\n    static ɵfac = function AuthService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AuthService)();\n    };\n    static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthService,\n      factory: AuthService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return AuthService;\n})();", "map": {"version": 3, "names": ["inject", "HttpClient", "HttpHeaders", "Router", "tap", "BehaviorSubject", "catchError", "throwError", "map", "of", "TokenStorageService", "AuthService", "http", "router", "tokenStorage", "authConfig", "authStateSubject", "isAuthenticated", "authState$", "asObservable", "refreshSubscription", "setAuthConfig", "config", "getAuthConfig", "getPostLoginRedirectUrl", "postLoginRedirectUrl", "getApiAuthUrl", "apiAuthUrl", "Error", "getRedirectUrl", "redirectUrl", "getAuthHeaders", "Authorization", "getLoginFlow", "getLoginType", "loginSSO", "finalRedirectUrl", "url", "get", "headers", "pipe", "loginUrl", "window", "location", "href", "exchangeCodeForToken", "code", "encodedRedirectUrl", "encodeURIComponent", "post", "response", "storeLoginType", "access_token", "accessToken", "refresh_token", "refreshToken", "expires_in", "expiresIn", "user_name", "da_name", "email", "da_username", "id_token", "idToken", "storeTokens", "storeDaInfo", "next", "error", "console", "refreshTokenFromStorage", "getRefreshToken", "logout", "getIdToken", "set", "logoutUrl", "clearTokens", "getAccessToken", "basicLoginWithCredentials", "username", "password", "loginBody", "userName", "basicRefreshToken", "clearAuthState", "basicLogout", "responseType", "normalizedResponse", "trim", "warn", "factory", "ɵfac", "providedIn"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\auth\\services\\auth.service.ts"], "sourcesContent": ["import { Injectable, inject } from '@angular/core';\r\nimport {\r\n  HttpClient,\r\n  HttpHeaders,\r\n  HttpErrorResponse,\r\n} from '@angular/common/http';\r\nimport { Router } from '@angular/router';\r\nimport {\r\n  Observable,\r\n  tap,\r\n  BehaviorSubject,\r\n  catchError,\r\n  throwError,\r\n  map,\r\n  of,\r\n  timer,\r\n  Subscription,\r\n  from,\r\n} from 'rxjs';\r\nimport { TokenStorageService } from './token-storage.service';\r\nimport { AuthConfig } from '../interfaces/auth-config.interface';\r\n\r\ninterface LoginResponse {\r\n  loginUrl: string;\r\n}\r\n\r\ninterface TokenResponse {\r\n  token_type: string;\r\n  scope: string;\r\n  expires_in: number;\r\n  access_token: string;\r\n  id_token: string;\r\n  refresh_token: string;\r\n  user_name: string;\r\n  email: string;\r\n}\r\n\r\ninterface TokenPair {\r\n  accessToken: string;\r\n  refreshToken: string;\r\n}\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class AuthService {\r\n  private http = inject(HttpClient);\r\n  private router = inject(Router);\r\n  private tokenStorage = inject(TokenStorageService);\r\n  private authConfig: AuthConfig | null = null;\r\n  private authStateSubject = new BehaviorSubject<boolean>(\r\n    this.isAuthenticated(),\r\n  );\r\n  public authState$ = this.authStateSubject.asObservable();\r\n  private refreshSubscription: Subscription | null = null;\r\n\r\n  public setAuthConfig(config: AuthConfig) {\r\n    this.authConfig = config;\r\n  }\r\n\r\n  public getAuthConfig(): AuthConfig | null {\r\n    return this.authConfig;\r\n  }\r\n\r\n  public getPostLoginRedirectUrl(): string {\r\n    return this.authConfig?.postLoginRedirectUrl || '/dashboard';\r\n  }\r\n\r\n  private getApiAuthUrl(): string {\r\n    if (!this.authConfig?.apiAuthUrl) {\r\n      throw new Error('Auth configuration not set. Call setAuthConfig() first.');\r\n    }\r\n    return this.authConfig.apiAuthUrl;\r\n  }\r\n\r\n  private getRedirectUrl(): string {\r\n    if (!this.authConfig?.redirectUrl) {\r\n      throw new Error('Auth configuration not set. Call setAuthConfig() first.');\r\n    }\r\n    return this.authConfig.redirectUrl;\r\n  }\r\n\r\n  private getAuthHeaders(): HttpHeaders {\r\n    return new HttpHeaders({\r\n      Authorization: `Basic YWRtaW46YWRtaW4xMjM=`,\r\n    });\r\n  }\r\n\r\n  public getLoginFlow(): 'sso' | 'basic' {\r\n    return this.tokenStorage.getLoginType() || 'sso';\r\n  }\r\n\r\n  loginSSO(redirectUrl?: string): Observable<LoginResponse> {\r\n    const finalRedirectUrl = redirectUrl || this.getRedirectUrl();\r\n    const url = `${this.getApiAuthUrl()}/login-url?redirectUrl=${finalRedirectUrl}`;\r\n    return this.http\r\n      .get<LoginResponse>(url, { headers: this.getAuthHeaders() })\r\n      .pipe(\r\n        tap(({ loginUrl }) => {\r\n          window.location.href = loginUrl;\r\n        }),\r\n      );\r\n  }\r\n\r\n  exchangeCodeForToken(\r\n    code: string,\r\n    redirectUrl?: string,\r\n  ): Observable<TokenPair> {\r\n    const finalRedirectUrl = redirectUrl || this.getRedirectUrl();\r\n    const encodedRedirectUrl = encodeURIComponent(finalRedirectUrl);\r\n    const url = `${this.getApiAuthUrl()}/token?redirectUrl=${encodedRedirectUrl}`;\r\n    return this.http\r\n      .post<TokenResponse>(\r\n        url,\r\n        { code: code },\r\n        { headers: this.getAuthHeaders() },\r\n      )\r\n      .pipe(\r\n        tap((response) => {\r\n          this.tokenStorage.storeLoginType('sso');\r\n          const {\r\n            access_token: accessToken,\r\n            refresh_token: refreshToken,\r\n            expires_in: expiresIn,\r\n            user_name: da_name,\r\n            email: da_username,\r\n            id_token: idToken,\r\n          } = response;\r\n          this.tokenStorage.storeTokens(accessToken, refreshToken, expiresIn);\r\n          this.tokenStorage.storeDaInfo(da_name, da_username, idToken);\r\n          this.authStateSubject.next(true);\r\n        }),\r\n        map((response) => ({\r\n          accessToken: response.access_token,\r\n          refreshToken: response.refresh_token,\r\n        })),\r\n        catchError((error) => {\r\n          console.error('Token exchange failed:', error);\r\n          return throwError(() => error);\r\n        }),\r\n      );\r\n  }\r\n\r\n  refreshToken(refreshToken?: string): Observable<TokenPair> {\r\n    const refreshTokenFromStorage = this.tokenStorage.getRefreshToken();\r\n    const url = `${this.getApiAuthUrl()}/refresh-token`;\r\n    return this.http\r\n      .post<TokenResponse>(\r\n        url,\r\n        { refreshToken: refreshToken ? refreshToken : refreshTokenFromStorage },\r\n        { headers: this.getAuthHeaders() },\r\n      )\r\n      .pipe(\r\n        tap((response) => {\r\n          const {\r\n            access_token: accessToken,\r\n            refresh_token: refreshToken,\r\n            expires_in: expiresIn,\r\n            user_name: da_name,\r\n            email: da_username,\r\n            id_token: idToken,\r\n          } = response;\r\n          this.tokenStorage.storeTokens(accessToken, refreshToken, expiresIn);\r\n          this.tokenStorage.storeDaInfo(da_name, da_username, idToken);\r\n          this.authStateSubject.next(true);\r\n        }),\r\n        map((response) => ({\r\n          accessToken: response.access_token,\r\n          refreshToken: response.refresh_token,\r\n        })),\r\n        catchError((error) => {\r\n          console.error('Token refresh failed:', error);\r\n          return throwError(() => error);\r\n        }),\r\n      );\r\n  }\r\n\r\n  logout(redirectUrl?: string): Observable<any> {\r\n    const idToken = this.tokenStorage.getIdToken();\r\n    const finalRedirectUrl = redirectUrl || this.getRedirectUrl();\r\n    const url = `${this.getApiAuthUrl()}/logout-url?redirectUrl=${finalRedirectUrl}`;\r\n    const headers = this.getAuthHeaders().set('X-ID-TOKEN', idToken || '');\r\n    return this.http.get<any>(url, { headers }).pipe(\r\n      tap(({ logoutUrl }) => {\r\n        this.tokenStorage.clearTokens();\r\n        this.authStateSubject.next(false);\r\n        window.location.href = logoutUrl;\r\n      }),\r\n      catchError((error) => {\r\n        console.error('Logout failed:', error);\r\n        return throwError(() => error);\r\n      }),\r\n    );\r\n  }\r\n\r\n  isAuthenticated(): boolean {\r\n    return !!this.tokenStorage.getAccessToken();\r\n  }\r\n\r\n  basicLoginWithCredentials(\r\n    username: string,\r\n    password: string,\r\n  ): Observable<TokenResponse> {\r\n    const url = `${this.getApiAuthUrl()}/basic/login`;\r\n    const loginBody = {\r\n      userName: username,\r\n      password: password,\r\n    };\r\n    return this.http\r\n      .post<TokenResponse>(url, loginBody, { headers: this.getAuthHeaders() })\r\n      .pipe(\r\n        tap((response) => {\r\n          const {\r\n            access_token: accessToken,\r\n            refresh_token: refreshToken,\r\n            expires_in: expiresIn,\r\n            user_name: da_name,\r\n            email: da_username,\r\n            id_token: idToken,\r\n          } = response;\r\n          this.tokenStorage.storeTokens(accessToken, refreshToken, expiresIn);\r\n          this.tokenStorage.storeDaInfo(da_name, da_username, idToken);\r\n          this.authStateSubject.next(true);\r\n        }),\r\n        catchError((error: HttpErrorResponse) => {\r\n          console.error('Basic login failed:', error);\r\n          return throwError(() => error);\r\n        }),\r\n      );\r\n  }\r\n\r\n  basicRefreshToken(): Observable<TokenResponse> {\r\n    const refreshToken = this.tokenStorage.getRefreshToken();\r\n    const url = `${this.getApiAuthUrl()}/basic/refresh/token`;\r\n    return this.http\r\n      .post<TokenResponse>(\r\n        url,\r\n        { refreshToken },\r\n        { headers: this.getAuthHeaders() },\r\n      )\r\n      .pipe(\r\n        tap((response) => {\r\n          const {\r\n            access_token: accessToken,\r\n            refresh_token: refreshToken,\r\n            expires_in: expiresIn,\r\n            user_name: da_name,\r\n            email: da_username,\r\n            id_token: idToken,\r\n          } = response;\r\n          this.tokenStorage.storeTokens(accessToken, refreshToken, expiresIn);\r\n          this.tokenStorage.storeDaInfo(da_name, da_username, idToken);\r\n          this.authStateSubject.next(true);\r\n        }),\r\n        catchError((error) => {\r\n          console.error('Basic token refresh failed:', error);\r\n          return throwError(() => error);\r\n        }),\r\n      );\r\n  }\r\n  \r\n  private clearAuthState(): void {\r\n    this.tokenStorage.clearTokens();\r\n    this.authStateSubject.next(false);\r\n  }\r\n\r\n  basicLogout(): Observable<void> {\r\n    const refreshToken = this.tokenStorage.getRefreshToken();\r\n    if (!refreshToken) {\r\n      this.clearAuthState();\r\n      return of(void 0);\r\n    }\r\n    const url = `${this.getApiAuthUrl()}/basic/logout`;\r\n    const headers = this.getAuthHeaders().set('X-REFRESH-TOKEN', refreshToken);\r\n    return this.http\r\n      .post(url, null, {\r\n        headers,\r\n        responseType: 'text',\r\n      })\r\n      .pipe(\r\n        map((response) => {\r\n          const normalizedResponse = response.trim();\r\n          this.clearAuthState();\r\n          if (normalizedResponse !== 'Logout successful.') {\r\n            console.warn('Unexpected logout response:', response);\r\n          }\r\n          return void 0;\r\n        }),\r\n        catchError((error) => {\r\n          this.clearAuthState();\r\n          console.error('Basic logout failed:', error);\r\n          return throwError(() => error);\r\n        }),\r\n      );\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAAqBA,MAAM,QAAQ,eAAe;AAClD,SACEC,UAAU,EACVC,WAAW,QAEN,sBAAsB;AAC7B,SAASC,MAAM,QAAQ,iBAAiB;AACxC,SAEEC,GAAG,EACHC,eAAe,EACfC,UAAU,EACVC,UAAU,EACVC,GAAG,EACHC,EAAE,QAIG,MAAM;AACb,SAASC,mBAAmB,QAAQ,yBAAyB;;AA0B7D,WAAaC,WAAW;EAAlB,MAAOA,WAAW;IACdC,IAAI,GAAGZ,MAAM,CAACC,UAAU,CAAC;IACzBY,MAAM,GAAGb,MAAM,CAACG,MAAM,CAAC;IACvBW,YAAY,GAAGd,MAAM,CAACU,mBAAmB,CAAC;IAC1CK,UAAU,GAAsB,IAAI;IACpCC,gBAAgB,GAAG,IAAIX,eAAe,CAC5C,IAAI,CAACY,eAAe,EAAE,CACvB;IACMC,UAAU,GAAG,IAAI,CAACF,gBAAgB,CAACG,YAAY,EAAE;IAChDC,mBAAmB,GAAwB,IAAI;IAEhDC,aAAaA,CAACC,MAAkB;MACrC,IAAI,CAACP,UAAU,GAAGO,MAAM;IAC1B;IAEOC,aAAaA,CAAA;MAClB,OAAO,IAAI,CAACR,UAAU;IACxB;IAEOS,uBAAuBA,CAAA;MAC5B,OAAO,IAAI,CAACT,UAAU,EAAEU,oBAAoB,IAAI,YAAY;IAC9D;IAEQC,aAAaA,CAAA;MACnB,IAAI,CAAC,IAAI,CAACX,UAAU,EAAEY,UAAU,EAAE;QAChC,MAAM,IAAIC,KAAK,CAAC,yDAAyD,CAAC;MAC5E;MACA,OAAO,IAAI,CAACb,UAAU,CAACY,UAAU;IACnC;IAEQE,cAAcA,CAAA;MACpB,IAAI,CAAC,IAAI,CAACd,UAAU,EAAEe,WAAW,EAAE;QACjC,MAAM,IAAIF,KAAK,CAAC,yDAAyD,CAAC;MAC5E;MACA,OAAO,IAAI,CAACb,UAAU,CAACe,WAAW;IACpC;IAEQC,cAAcA,CAAA;MACpB,OAAO,IAAI7B,WAAW,CAAC;QACrB8B,aAAa,EAAE;OAChB,CAAC;IACJ;IAEOC,YAAYA,CAAA;MACjB,OAAO,IAAI,CAACnB,YAAY,CAACoB,YAAY,EAAE,IAAI,KAAK;IAClD;IAEAC,QAAQA,CAACL,WAAoB;MAC3B,MAAMM,gBAAgB,GAAGN,WAAW,IAAI,IAAI,CAACD,cAAc,EAAE;MAC7D,MAAMQ,GAAG,GAAG,GAAG,IAAI,CAACX,aAAa,EAAE,0BAA0BU,gBAAgB,EAAE;MAC/E,OAAO,IAAI,CAACxB,IAAI,CACb0B,GAAG,CAAgBD,GAAG,EAAE;QAAEE,OAAO,EAAE,IAAI,CAACR,cAAc;MAAE,CAAE,CAAC,CAC3DS,IAAI,CACHpC,GAAG,CAAC,CAAC;QAAEqC;MAAQ,CAAE,KAAI;QACnBC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAGH,QAAQ;MACjC,CAAC,CAAC,CACH;IACL;IAEAI,oBAAoBA,CAClBC,IAAY,EACZhB,WAAoB;MAEpB,MAAMM,gBAAgB,GAAGN,WAAW,IAAI,IAAI,CAACD,cAAc,EAAE;MAC7D,MAAMkB,kBAAkB,GAAGC,kBAAkB,CAACZ,gBAAgB,CAAC;MAC/D,MAAMC,GAAG,GAAG,GAAG,IAAI,CAACX,aAAa,EAAE,sBAAsBqB,kBAAkB,EAAE;MAC7E,OAAO,IAAI,CAACnC,IAAI,CACbqC,IAAI,CACHZ,GAAG,EACH;QAAES,IAAI,EAAEA;MAAI,CAAE,EACd;QAAEP,OAAO,EAAE,IAAI,CAACR,cAAc;MAAE,CAAE,CACnC,CACAS,IAAI,CACHpC,GAAG,CAAE8C,QAAQ,IAAI;QACf,IAAI,CAACpC,YAAY,CAACqC,cAAc,CAAC,KAAK,CAAC;QACvC,MAAM;UACJC,YAAY,EAAEC,WAAW;UACzBC,aAAa,EAAEC,YAAY;UAC3BC,UAAU,EAAEC,SAAS;UACrBC,SAAS,EAAEC,OAAO;UAClBC,KAAK,EAAEC,WAAW;UAClBC,QAAQ,EAAEC;QAAO,CAClB,GAAGb,QAAQ;QACZ,IAAI,CAACpC,YAAY,CAACkD,WAAW,CAACX,WAAW,EAAEE,YAAY,EAAEE,SAAS,CAAC;QACnE,IAAI,CAAC3C,YAAY,CAACmD,WAAW,CAACN,OAAO,EAAEE,WAAW,EAAEE,OAAO,CAAC;QAC5D,IAAI,CAAC/C,gBAAgB,CAACkD,IAAI,CAAC,IAAI,CAAC;MAClC,CAAC,CAAC,EACF1D,GAAG,CAAE0C,QAAQ,KAAM;QACjBG,WAAW,EAAEH,QAAQ,CAACE,YAAY;QAClCG,YAAY,EAAEL,QAAQ,CAACI;OACxB,CAAC,CAAC,EACHhD,UAAU,CAAE6D,KAAK,IAAI;QACnBC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,OAAO5D,UAAU,CAAC,MAAM4D,KAAK,CAAC;MAChC,CAAC,CAAC,CACH;IACL;IAEAZ,YAAYA,CAACA,YAAqB;MAChC,MAAMc,uBAAuB,GAAG,IAAI,CAACvD,YAAY,CAACwD,eAAe,EAAE;MACnE,MAAMjC,GAAG,GAAG,GAAG,IAAI,CAACX,aAAa,EAAE,gBAAgB;MACnD,OAAO,IAAI,CAACd,IAAI,CACbqC,IAAI,CACHZ,GAAG,EACH;QAAEkB,YAAY,EAAEA,YAAY,GAAGA,YAAY,GAAGc;MAAuB,CAAE,EACvE;QAAE9B,OAAO,EAAE,IAAI,CAACR,cAAc;MAAE,CAAE,CACnC,CACAS,IAAI,CACHpC,GAAG,CAAE8C,QAAQ,IAAI;QACf,MAAM;UACJE,YAAY,EAAEC,WAAW;UACzBC,aAAa,EAAEC,YAAY;UAC3BC,UAAU,EAAEC,SAAS;UACrBC,SAAS,EAAEC,OAAO;UAClBC,KAAK,EAAEC,WAAW;UAClBC,QAAQ,EAAEC;QAAO,CAClB,GAAGb,QAAQ;QACZ,IAAI,CAACpC,YAAY,CAACkD,WAAW,CAACX,WAAW,EAAEE,YAAY,EAAEE,SAAS,CAAC;QACnE,IAAI,CAAC3C,YAAY,CAACmD,WAAW,CAACN,OAAO,EAAEE,WAAW,EAAEE,OAAO,CAAC;QAC5D,IAAI,CAAC/C,gBAAgB,CAACkD,IAAI,CAAC,IAAI,CAAC;MAClC,CAAC,CAAC,EACF1D,GAAG,CAAE0C,QAAQ,KAAM;QACjBG,WAAW,EAAEH,QAAQ,CAACE,YAAY;QAClCG,YAAY,EAAEL,QAAQ,CAACI;OACxB,CAAC,CAAC,EACHhD,UAAU,CAAE6D,KAAK,IAAI;QACnBC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7C,OAAO5D,UAAU,CAAC,MAAM4D,KAAK,CAAC;MAChC,CAAC,CAAC,CACH;IACL;IAEAI,MAAMA,CAACzC,WAAoB;MACzB,MAAMiC,OAAO,GAAG,IAAI,CAACjD,YAAY,CAAC0D,UAAU,EAAE;MAC9C,MAAMpC,gBAAgB,GAAGN,WAAW,IAAI,IAAI,CAACD,cAAc,EAAE;MAC7D,MAAMQ,GAAG,GAAG,GAAG,IAAI,CAACX,aAAa,EAAE,2BAA2BU,gBAAgB,EAAE;MAChF,MAAMG,OAAO,GAAG,IAAI,CAACR,cAAc,EAAE,CAAC0C,GAAG,CAAC,YAAY,EAAEV,OAAO,IAAI,EAAE,CAAC;MACtE,OAAO,IAAI,CAACnD,IAAI,CAAC0B,GAAG,CAAMD,GAAG,EAAE;QAAEE;MAAO,CAAE,CAAC,CAACC,IAAI,CAC9CpC,GAAG,CAAC,CAAC;QAAEsE;MAAS,CAAE,KAAI;QACpB,IAAI,CAAC5D,YAAY,CAAC6D,WAAW,EAAE;QAC/B,IAAI,CAAC3D,gBAAgB,CAACkD,IAAI,CAAC,KAAK,CAAC;QACjCxB,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG8B,SAAS;MAClC,CAAC,CAAC,EACFpE,UAAU,CAAE6D,KAAK,IAAI;QACnBC,OAAO,CAACD,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;QACtC,OAAO5D,UAAU,CAAC,MAAM4D,KAAK,CAAC;MAChC,CAAC,CAAC,CACH;IACH;IAEAlD,eAAeA,CAAA;MACb,OAAO,CAAC,CAAC,IAAI,CAACH,YAAY,CAAC8D,cAAc,EAAE;IAC7C;IAEAC,yBAAyBA,CACvBC,QAAgB,EAChBC,QAAgB;MAEhB,MAAM1C,GAAG,GAAG,GAAG,IAAI,CAACX,aAAa,EAAE,cAAc;MACjD,MAAMsD,SAAS,GAAG;QAChBC,QAAQ,EAAEH,QAAQ;QAClBC,QAAQ,EAAEA;OACX;MACD,OAAO,IAAI,CAACnE,IAAI,CACbqC,IAAI,CAAgBZ,GAAG,EAAE2C,SAAS,EAAE;QAAEzC,OAAO,EAAE,IAAI,CAACR,cAAc;MAAE,CAAE,CAAC,CACvES,IAAI,CACHpC,GAAG,CAAE8C,QAAQ,IAAI;QACf,MAAM;UACJE,YAAY,EAAEC,WAAW;UACzBC,aAAa,EAAEC,YAAY;UAC3BC,UAAU,EAAEC,SAAS;UACrBC,SAAS,EAAEC,OAAO;UAClBC,KAAK,EAAEC,WAAW;UAClBC,QAAQ,EAAEC;QAAO,CAClB,GAAGb,QAAQ;QACZ,IAAI,CAACpC,YAAY,CAACkD,WAAW,CAACX,WAAW,EAAEE,YAAY,EAAEE,SAAS,CAAC;QACnE,IAAI,CAAC3C,YAAY,CAACmD,WAAW,CAACN,OAAO,EAAEE,WAAW,EAAEE,OAAO,CAAC;QAC5D,IAAI,CAAC/C,gBAAgB,CAACkD,IAAI,CAAC,IAAI,CAAC;MAClC,CAAC,CAAC,EACF5D,UAAU,CAAE6D,KAAwB,IAAI;QACtCC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3C,OAAO5D,UAAU,CAAC,MAAM4D,KAAK,CAAC;MAChC,CAAC,CAAC,CACH;IACL;IAEAe,iBAAiBA,CAAA;MACf,MAAM3B,YAAY,GAAG,IAAI,CAACzC,YAAY,CAACwD,eAAe,EAAE;MACxD,MAAMjC,GAAG,GAAG,GAAG,IAAI,CAACX,aAAa,EAAE,sBAAsB;MACzD,OAAO,IAAI,CAACd,IAAI,CACbqC,IAAI,CACHZ,GAAG,EACH;QAAEkB;MAAY,CAAE,EAChB;QAAEhB,OAAO,EAAE,IAAI,CAACR,cAAc;MAAE,CAAE,CACnC,CACAS,IAAI,CACHpC,GAAG,CAAE8C,QAAQ,IAAI;QACf,MAAM;UACJE,YAAY,EAAEC,WAAW;UACzBC,aAAa,EAAEC,YAAY;UAC3BC,UAAU,EAAEC,SAAS;UACrBC,SAAS,EAAEC,OAAO;UAClBC,KAAK,EAAEC,WAAW;UAClBC,QAAQ,EAAEC;QAAO,CAClB,GAAGb,QAAQ;QACZ,IAAI,CAACpC,YAAY,CAACkD,WAAW,CAACX,WAAW,EAAEE,YAAY,EAAEE,SAAS,CAAC;QACnE,IAAI,CAAC3C,YAAY,CAACmD,WAAW,CAACN,OAAO,EAAEE,WAAW,EAAEE,OAAO,CAAC;QAC5D,IAAI,CAAC/C,gBAAgB,CAACkD,IAAI,CAAC,IAAI,CAAC;MAClC,CAAC,CAAC,EACF5D,UAAU,CAAE6D,KAAK,IAAI;QACnBC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnD,OAAO5D,UAAU,CAAC,MAAM4D,KAAK,CAAC;MAChC,CAAC,CAAC,CACH;IACL;IAEQgB,cAAcA,CAAA;MACpB,IAAI,CAACrE,YAAY,CAAC6D,WAAW,EAAE;MAC/B,IAAI,CAAC3D,gBAAgB,CAACkD,IAAI,CAAC,KAAK,CAAC;IACnC;IAEAkB,WAAWA,CAAA;MACT,MAAM7B,YAAY,GAAG,IAAI,CAACzC,YAAY,CAACwD,eAAe,EAAE;MACxD,IAAI,CAACf,YAAY,EAAE;QACjB,IAAI,CAAC4B,cAAc,EAAE;QACrB,OAAO1E,EAAE,CAAC,KAAK,CAAC,CAAC;MACnB;MACA,MAAM4B,GAAG,GAAG,GAAG,IAAI,CAACX,aAAa,EAAE,eAAe;MAClD,MAAMa,OAAO,GAAG,IAAI,CAACR,cAAc,EAAE,CAAC0C,GAAG,CAAC,iBAAiB,EAAElB,YAAY,CAAC;MAC1E,OAAO,IAAI,CAAC3C,IAAI,CACbqC,IAAI,CAACZ,GAAG,EAAE,IAAI,EAAE;QACfE,OAAO;QACP8C,YAAY,EAAE;OACf,CAAC,CACD7C,IAAI,CACHhC,GAAG,CAAE0C,QAAQ,IAAI;QACf,MAAMoC,kBAAkB,GAAGpC,QAAQ,CAACqC,IAAI,EAAE;QAC1C,IAAI,CAACJ,cAAc,EAAE;QACrB,IAAIG,kBAAkB,KAAK,oBAAoB,EAAE;UAC/ClB,OAAO,CAACoB,IAAI,CAAC,6BAA6B,EAAEtC,QAAQ,CAAC;QACvD;QACA,OAAO,KAAK,CAAC;MACf,CAAC,CAAC,EACF5C,UAAU,CAAE6D,KAAK,IAAI;QACnB,IAAI,CAACgB,cAAc,EAAE;QACrBf,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C,OAAO5D,UAAU,CAAC,MAAM4D,KAAK,CAAC;MAChC,CAAC,CAAC,CACH;IACL;;uCAzPWxD,WAAW;IAAA;;aAAXA,WAAW;MAAA8E,OAAA,EAAX9E,WAAW,CAAA+E,IAAA;MAAAC,UAAA,EAFV;IAAM;;SAEPhF,WAAW;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}