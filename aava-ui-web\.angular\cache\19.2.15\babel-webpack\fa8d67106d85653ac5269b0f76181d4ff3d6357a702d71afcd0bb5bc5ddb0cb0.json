{"ast": null, "code": "import { Selection } from \"./index.js\";\nexport default function (compare) {\n  if (!compare) compare = ascending;\n  function compareNode(a, b) {\n    return a && b ? compare(a.__data__, b.__data__) : !a - !b;\n  }\n  for (var groups = this._groups, m = groups.length, sortgroups = new Array(m), j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, sortgroup = sortgroups[j] = new Array(n), node, i = 0; i < n; ++i) {\n      if (node = group[i]) {\n        sortgroup[i] = node;\n      }\n    }\n    sortgroup.sort(compareNode);\n  }\n  return new Selection(sortgroups, this._parents).order();\n}\nfunction ascending(a, b) {\n  return a < b ? -1 : a > b ? 1 : a >= b ? 0 : NaN;\n}", "map": {"version": 3, "names": ["Selection", "compare", "ascending", "compareNode", "a", "b", "__data__", "groups", "_groups", "m", "length", "sortgroups", "Array", "j", "group", "n", "sortgroup", "node", "i", "sort", "_parents", "order", "NaN"], "sources": ["C:/console/aava-ui-web/node_modules/d3-selection/src/selection/sort.js"], "sourcesContent": ["import {Selection} from \"./index.js\";\n\nexport default function(compare) {\n  if (!compare) compare = ascending;\n\n  function compareNode(a, b) {\n    return a && b ? compare(a.__data__, b.__data__) : !a - !b;\n  }\n\n  for (var groups = this._groups, m = groups.length, sortgroups = new Array(m), j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, sortgroup = sortgroups[j] = new Array(n), node, i = 0; i < n; ++i) {\n      if (node = group[i]) {\n        sortgroup[i] = node;\n      }\n    }\n    sortgroup.sort(compareNode);\n  }\n\n  return new Selection(sortgroups, this._parents).order();\n}\n\nfunction ascending(a, b) {\n  return a < b ? -1 : a > b ? 1 : a >= b ? 0 : NaN;\n}\n"], "mappings": "AAAA,SAAQA,SAAS,QAAO,YAAY;AAEpC,eAAe,UAASC,OAAO,EAAE;EAC/B,IAAI,CAACA,OAAO,EAAEA,OAAO,GAAGC,SAAS;EAEjC,SAASC,WAAWA,CAACC,CAAC,EAAEC,CAAC,EAAE;IACzB,OAAOD,CAAC,IAAIC,CAAC,GAAGJ,OAAO,CAACG,CAAC,CAACE,QAAQ,EAAED,CAAC,CAACC,QAAQ,CAAC,GAAG,CAACF,CAAC,GAAG,CAACC,CAAC;EAC3D;EAEA,KAAK,IAAIE,MAAM,GAAG,IAAI,CAACC,OAAO,EAAEC,CAAC,GAAGF,MAAM,CAACG,MAAM,EAAEC,UAAU,GAAG,IAAIC,KAAK,CAACH,CAAC,CAAC,EAAEI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,CAAC,EAAE,EAAEI,CAAC,EAAE;IAC/F,KAAK,IAAIC,KAAK,GAAGP,MAAM,CAACM,CAAC,CAAC,EAAEE,CAAC,GAAGD,KAAK,CAACJ,MAAM,EAAEM,SAAS,GAAGL,UAAU,CAACE,CAAC,CAAC,GAAG,IAAID,KAAK,CAACG,CAAC,CAAC,EAAEE,IAAI,EAAEC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,CAAC,EAAE,EAAEG,CAAC,EAAE;MAC/G,IAAID,IAAI,GAAGH,KAAK,CAACI,CAAC,CAAC,EAAE;QACnBF,SAAS,CAACE,CAAC,CAAC,GAAGD,IAAI;MACrB;IACF;IACAD,SAAS,CAACG,IAAI,CAAChB,WAAW,CAAC;EAC7B;EAEA,OAAO,IAAIH,SAAS,CAACW,UAAU,EAAE,IAAI,CAACS,QAAQ,CAAC,CAACC,KAAK,CAAC,CAAC;AACzD;AAEA,SAASnB,SAASA,CAACE,CAAC,EAAEC,CAAC,EAAE;EACvB,OAAOD,CAAC,GAAGC,CAAC,GAAG,CAAC,CAAC,GAAGD,CAAC,GAAGC,CAAC,GAAG,CAAC,GAAGD,CAAC,IAAIC,CAAC,GAAG,CAAC,GAAGiB,GAAG;AAClD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}