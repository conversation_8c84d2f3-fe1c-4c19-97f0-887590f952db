{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = this && this.__param || function (paramIndex, decorator) {\n  return function (target, key) {\n    decorator(target, key, paramIndex);\n  };\n};\nvar ContentHoverWidget_1;\nimport * as dom from '../../../../base/browser/dom.js';\nimport { IKeybindingService } from '../../../../platform/keybinding/common/keybinding.js';\nimport { ResizableContentWidget } from './resizableContentWidget.js';\nimport { IContextKeyService } from '../../../../platform/contextkey/common/contextkey.js';\nimport { IConfigurationService } from '../../../../platform/configuration/common/configuration.js';\nimport { IAccessibilityService } from '../../../../platform/accessibility/common/accessibility.js';\nimport { EditorContextKeys } from '../../../common/editorContextKeys.js';\nimport { getHoverAccessibleViewHint, HoverWidget } from '../../../../base/browser/ui/hover/hoverWidget.js';\nimport { Emitter } from '../../../../base/common/event.js';\nconst HORIZONTAL_SCROLLING_BY = 30;\nconst CONTAINER_HEIGHT_PADDING = 6;\nlet ContentHoverWidget = class ContentHoverWidget extends ResizableContentWidget {\n  static {\n    ContentHoverWidget_1 = this;\n  }\n  static {\n    this.ID = 'editor.contrib.resizableContentHoverWidget';\n  }\n  static {\n    this._lastDimensions = new dom.Dimension(0, 0);\n  }\n  get isVisibleFromKeyboard() {\n    return this._renderedHover?.source === 1 /* HoverStartSource.Keyboard */;\n  }\n  get isVisible() {\n    return this._hoverVisibleKey.get() ?? false;\n  }\n  get isFocused() {\n    return this._hoverFocusedKey.get() ?? false;\n  }\n  constructor(editor, contextKeyService, _configurationService, _accessibilityService, _keybindingService) {\n    const minimumHeight = editor.getOption(67 /* EditorOption.lineHeight */) + 8;\n    const minimumWidth = 150;\n    const minimumSize = new dom.Dimension(minimumWidth, minimumHeight);\n    super(editor, minimumSize);\n    this._configurationService = _configurationService;\n    this._accessibilityService = _accessibilityService;\n    this._keybindingService = _keybindingService;\n    this._hover = this._register(new HoverWidget());\n    this._onDidResize = this._register(new Emitter());\n    this.onDidResize = this._onDidResize.event;\n    this._minimumSize = minimumSize;\n    this._hoverVisibleKey = EditorContextKeys.hoverVisible.bindTo(contextKeyService);\n    this._hoverFocusedKey = EditorContextKeys.hoverFocused.bindTo(contextKeyService);\n    dom.append(this._resizableNode.domNode, this._hover.containerDomNode);\n    this._resizableNode.domNode.style.zIndex = '50';\n    this._register(this._editor.onDidLayoutChange(() => {\n      if (this.isVisible) {\n        this._updateMaxDimensions();\n      }\n    }));\n    this._register(this._editor.onDidChangeConfiguration(e => {\n      if (e.hasChanged(50 /* EditorOption.fontInfo */)) {\n        this._updateFont();\n      }\n    }));\n    const focusTracker = this._register(dom.trackFocus(this._resizableNode.domNode));\n    this._register(focusTracker.onDidFocus(() => {\n      this._hoverFocusedKey.set(true);\n    }));\n    this._register(focusTracker.onDidBlur(() => {\n      this._hoverFocusedKey.set(false);\n    }));\n    this._setRenderedHover(undefined);\n    this._editor.addContentWidget(this);\n  }\n  dispose() {\n    super.dispose();\n    this._renderedHover?.dispose();\n    this._editor.removeContentWidget(this);\n  }\n  getId() {\n    return ContentHoverWidget_1.ID;\n  }\n  static _applyDimensions(container, width, height) {\n    const transformedWidth = typeof width === 'number' ? `${width}px` : width;\n    const transformedHeight = typeof height === 'number' ? `${height}px` : height;\n    container.style.width = transformedWidth;\n    container.style.height = transformedHeight;\n  }\n  _setContentsDomNodeDimensions(width, height) {\n    const contentsDomNode = this._hover.contentsDomNode;\n    return ContentHoverWidget_1._applyDimensions(contentsDomNode, width, height);\n  }\n  _setContainerDomNodeDimensions(width, height) {\n    const containerDomNode = this._hover.containerDomNode;\n    return ContentHoverWidget_1._applyDimensions(containerDomNode, width, height);\n  }\n  _setHoverWidgetDimensions(width, height) {\n    this._setContentsDomNodeDimensions(width, height);\n    this._setContainerDomNodeDimensions(width, height);\n    this._layoutContentWidget();\n  }\n  static _applyMaxDimensions(container, width, height) {\n    const transformedWidth = typeof width === 'number' ? `${width}px` : width;\n    const transformedHeight = typeof height === 'number' ? `${height}px` : height;\n    container.style.maxWidth = transformedWidth;\n    container.style.maxHeight = transformedHeight;\n  }\n  _setHoverWidgetMaxDimensions(width, height) {\n    ContentHoverWidget_1._applyMaxDimensions(this._hover.contentsDomNode, width, height);\n    ContentHoverWidget_1._applyMaxDimensions(this._hover.containerDomNode, width, height);\n    this._hover.containerDomNode.style.setProperty('--vscode-hover-maxWidth', typeof width === 'number' ? `${width}px` : width);\n    this._layoutContentWidget();\n  }\n  _setAdjustedHoverWidgetDimensions(size) {\n    this._setHoverWidgetMaxDimensions('none', 'none');\n    const width = size.width;\n    const height = size.height;\n    this._setHoverWidgetDimensions(width, height);\n  }\n  _updateResizableNodeMaxDimensions() {\n    const maxRenderingWidth = this._findMaximumRenderingWidth() ?? Infinity;\n    const maxRenderingHeight = this._findMaximumRenderingHeight() ?? Infinity;\n    this._resizableNode.maxSize = new dom.Dimension(maxRenderingWidth, maxRenderingHeight);\n    this._setHoverWidgetMaxDimensions(maxRenderingWidth, maxRenderingHeight);\n  }\n  _resize(size) {\n    ContentHoverWidget_1._lastDimensions = new dom.Dimension(size.width, size.height);\n    this._setAdjustedHoverWidgetDimensions(size);\n    this._resizableNode.layout(size.height, size.width);\n    this._updateResizableNodeMaxDimensions();\n    this._hover.scrollbar.scanDomNode();\n    this._editor.layoutContentWidget(this);\n    this._onDidResize.fire();\n  }\n  _findAvailableSpaceVertically() {\n    const position = this._renderedHover?.showAtPosition;\n    if (!position) {\n      return;\n    }\n    return this._positionPreference === 1 /* ContentWidgetPositionPreference.ABOVE */ ? this._availableVerticalSpaceAbove(position) : this._availableVerticalSpaceBelow(position);\n  }\n  _findMaximumRenderingHeight() {\n    const availableSpace = this._findAvailableSpaceVertically();\n    if (!availableSpace) {\n      return;\n    }\n    // Padding needed in order to stop the resizing down to a smaller height\n    let maximumHeight = CONTAINER_HEIGHT_PADDING;\n    Array.from(this._hover.contentsDomNode.children).forEach(hoverPart => {\n      maximumHeight += hoverPart.clientHeight;\n    });\n    return Math.min(availableSpace, maximumHeight);\n  }\n  _isHoverTextOverflowing() {\n    // To find out if the text is overflowing, we will disable wrapping, check the widths, and then re-enable wrapping\n    this._hover.containerDomNode.style.setProperty('--vscode-hover-whiteSpace', 'nowrap');\n    this._hover.containerDomNode.style.setProperty('--vscode-hover-sourceWhiteSpace', 'nowrap');\n    const overflowing = Array.from(this._hover.contentsDomNode.children).some(hoverElement => {\n      return hoverElement.scrollWidth > hoverElement.clientWidth;\n    });\n    this._hover.containerDomNode.style.removeProperty('--vscode-hover-whiteSpace');\n    this._hover.containerDomNode.style.removeProperty('--vscode-hover-sourceWhiteSpace');\n    return overflowing;\n  }\n  _findMaximumRenderingWidth() {\n    if (!this._editor || !this._editor.hasModel()) {\n      return;\n    }\n    const overflowing = this._isHoverTextOverflowing();\n    const initialWidth = typeof this._contentWidth === 'undefined' ? 0 : this._contentWidth - 2 // - 2 for the borders\n    ;\n    if (overflowing || this._hover.containerDomNode.clientWidth < initialWidth) {\n      const bodyBoxWidth = dom.getClientArea(this._hover.containerDomNode.ownerDocument.body).width;\n      const horizontalPadding = 14;\n      return bodyBoxWidth - horizontalPadding;\n    } else {\n      return this._hover.containerDomNode.clientWidth + 2;\n    }\n  }\n  isMouseGettingCloser(posx, posy) {\n    if (!this._renderedHover) {\n      return false;\n    }\n    if (this._renderedHover.initialMousePosX === undefined || this._renderedHover.initialMousePosY === undefined) {\n      this._renderedHover.initialMousePosX = posx;\n      this._renderedHover.initialMousePosY = posy;\n      return false;\n    }\n    const widgetRect = dom.getDomNodePagePosition(this.getDomNode());\n    if (this._renderedHover.closestMouseDistance === undefined) {\n      this._renderedHover.closestMouseDistance = computeDistanceFromPointToRectangle(this._renderedHover.initialMousePosX, this._renderedHover.initialMousePosY, widgetRect.left, widgetRect.top, widgetRect.width, widgetRect.height);\n    }\n    const distance = computeDistanceFromPointToRectangle(posx, posy, widgetRect.left, widgetRect.top, widgetRect.width, widgetRect.height);\n    if (distance > this._renderedHover.closestMouseDistance + 4 /* tolerance of 4 pixels */) {\n      // The mouse is getting farther away\n      return false;\n    }\n    this._renderedHover.closestMouseDistance = Math.min(this._renderedHover.closestMouseDistance, distance);\n    return true;\n  }\n  _setRenderedHover(renderedHover) {\n    this._renderedHover?.dispose();\n    this._renderedHover = renderedHover;\n    this._hoverVisibleKey.set(!!renderedHover);\n    this._hover.containerDomNode.classList.toggle('hidden', !renderedHover);\n  }\n  _updateFont() {\n    const {\n      fontSize,\n      lineHeight\n    } = this._editor.getOption(50 /* EditorOption.fontInfo */);\n    const contentsDomNode = this._hover.contentsDomNode;\n    contentsDomNode.style.fontSize = `${fontSize}px`;\n    contentsDomNode.style.lineHeight = `${lineHeight / fontSize}`;\n    const codeClasses = Array.prototype.slice.call(this._hover.contentsDomNode.getElementsByClassName('code'));\n    codeClasses.forEach(node => this._editor.applyFontInfo(node));\n  }\n  _updateContent(node) {\n    const contentsDomNode = this._hover.contentsDomNode;\n    contentsDomNode.style.paddingBottom = '';\n    contentsDomNode.textContent = '';\n    contentsDomNode.appendChild(node);\n  }\n  _layoutContentWidget() {\n    this._editor.layoutContentWidget(this);\n    this._hover.onContentsChanged();\n  }\n  _updateMaxDimensions() {\n    const height = Math.max(this._editor.getLayoutInfo().height / 4, 250, ContentHoverWidget_1._lastDimensions.height);\n    const width = Math.max(this._editor.getLayoutInfo().width * 0.66, 500, ContentHoverWidget_1._lastDimensions.width);\n    this._setHoverWidgetMaxDimensions(width, height);\n  }\n  _render(renderedHover) {\n    this._setRenderedHover(renderedHover);\n    this._updateFont();\n    this._updateContent(renderedHover.domNode);\n    this._updateMaxDimensions();\n    this.onContentsChanged();\n    // Simply force a synchronous render on the editor\n    // such that the widget does not really render with left = '0px'\n    this._editor.render();\n  }\n  getPosition() {\n    if (!this._renderedHover) {\n      return null;\n    }\n    return {\n      position: this._renderedHover.showAtPosition,\n      secondaryPosition: this._renderedHover.showAtSecondaryPosition,\n      positionAffinity: this._renderedHover.shouldAppearBeforeContent ? 3 /* PositionAffinity.LeftOfInjectedText */ : undefined,\n      preference: [this._positionPreference ?? 1 /* ContentWidgetPositionPreference.ABOVE */]\n    };\n  }\n  show(renderedHover) {\n    if (!this._editor || !this._editor.hasModel()) {\n      return;\n    }\n    this._render(renderedHover);\n    const widgetHeight = dom.getTotalHeight(this._hover.containerDomNode);\n    const widgetPosition = renderedHover.showAtPosition;\n    this._positionPreference = this._findPositionPreference(widgetHeight, widgetPosition) ?? 1 /* ContentWidgetPositionPreference.ABOVE */;\n    // See https://github.com/microsoft/vscode/issues/140339\n    // TODO: Doing a second layout of the hover after force rendering the editor\n    this.onContentsChanged();\n    if (renderedHover.shouldFocus) {\n      this._hover.containerDomNode.focus();\n    }\n    this._onDidResize.fire();\n    // The aria label overrides the label, so if we add to it, add the contents of the hover\n    const hoverFocused = this._hover.containerDomNode.ownerDocument.activeElement === this._hover.containerDomNode;\n    const accessibleViewHint = hoverFocused && getHoverAccessibleViewHint(this._configurationService.getValue('accessibility.verbosity.hover') === true && this._accessibilityService.isScreenReaderOptimized(), this._keybindingService.lookupKeybinding('editor.action.accessibleView')?.getAriaLabel() ?? '');\n    if (accessibleViewHint) {\n      this._hover.contentsDomNode.ariaLabel = this._hover.contentsDomNode.textContent + ', ' + accessibleViewHint;\n    }\n  }\n  hide() {\n    if (!this._renderedHover) {\n      return;\n    }\n    const hoverStoleFocus = this._renderedHover.shouldFocus || this._hoverFocusedKey.get();\n    this._setRenderedHover(undefined);\n    this._resizableNode.maxSize = new dom.Dimension(Infinity, Infinity);\n    this._resizableNode.clearSashHoverState();\n    this._hoverFocusedKey.set(false);\n    this._editor.layoutContentWidget(this);\n    if (hoverStoleFocus) {\n      this._editor.focus();\n    }\n  }\n  _removeConstraintsRenderNormally() {\n    // Added because otherwise the initial size of the hover content is smaller than should be\n    const layoutInfo = this._editor.getLayoutInfo();\n    this._resizableNode.layout(layoutInfo.height, layoutInfo.width);\n    this._setHoverWidgetDimensions('auto', 'auto');\n  }\n  setMinimumDimensions(dimensions) {\n    // We combine the new minimum dimensions with the previous ones\n    this._minimumSize = new dom.Dimension(Math.max(this._minimumSize.width, dimensions.width), Math.max(this._minimumSize.height, dimensions.height));\n    this._updateMinimumWidth();\n  }\n  _updateMinimumWidth() {\n    const width = typeof this._contentWidth === 'undefined' ? this._minimumSize.width : Math.min(this._contentWidth, this._minimumSize.width);\n    // We want to avoid that the hover is artificially large, so we use the content width as minimum width\n    this._resizableNode.minSize = new dom.Dimension(width, this._minimumSize.height);\n  }\n  onContentsChanged() {\n    this._removeConstraintsRenderNormally();\n    const containerDomNode = this._hover.containerDomNode;\n    let height = dom.getTotalHeight(containerDomNode);\n    let width = dom.getTotalWidth(containerDomNode);\n    this._resizableNode.layout(height, width);\n    this._setHoverWidgetDimensions(width, height);\n    height = dom.getTotalHeight(containerDomNode);\n    width = dom.getTotalWidth(containerDomNode);\n    this._contentWidth = width;\n    this._updateMinimumWidth();\n    this._resizableNode.layout(height, width);\n    if (this._renderedHover?.showAtPosition) {\n      const widgetHeight = dom.getTotalHeight(this._hover.containerDomNode);\n      this._positionPreference = this._findPositionPreference(widgetHeight, this._renderedHover.showAtPosition);\n    }\n    this._layoutContentWidget();\n  }\n  focus() {\n    this._hover.containerDomNode.focus();\n  }\n  scrollUp() {\n    const scrollTop = this._hover.scrollbar.getScrollPosition().scrollTop;\n    const fontInfo = this._editor.getOption(50 /* EditorOption.fontInfo */);\n    this._hover.scrollbar.setScrollPosition({\n      scrollTop: scrollTop - fontInfo.lineHeight\n    });\n  }\n  scrollDown() {\n    const scrollTop = this._hover.scrollbar.getScrollPosition().scrollTop;\n    const fontInfo = this._editor.getOption(50 /* EditorOption.fontInfo */);\n    this._hover.scrollbar.setScrollPosition({\n      scrollTop: scrollTop + fontInfo.lineHeight\n    });\n  }\n  scrollLeft() {\n    const scrollLeft = this._hover.scrollbar.getScrollPosition().scrollLeft;\n    this._hover.scrollbar.setScrollPosition({\n      scrollLeft: scrollLeft - HORIZONTAL_SCROLLING_BY\n    });\n  }\n  scrollRight() {\n    const scrollLeft = this._hover.scrollbar.getScrollPosition().scrollLeft;\n    this._hover.scrollbar.setScrollPosition({\n      scrollLeft: scrollLeft + HORIZONTAL_SCROLLING_BY\n    });\n  }\n  pageUp() {\n    const scrollTop = this._hover.scrollbar.getScrollPosition().scrollTop;\n    const scrollHeight = this._hover.scrollbar.getScrollDimensions().height;\n    this._hover.scrollbar.setScrollPosition({\n      scrollTop: scrollTop - scrollHeight\n    });\n  }\n  pageDown() {\n    const scrollTop = this._hover.scrollbar.getScrollPosition().scrollTop;\n    const scrollHeight = this._hover.scrollbar.getScrollDimensions().height;\n    this._hover.scrollbar.setScrollPosition({\n      scrollTop: scrollTop + scrollHeight\n    });\n  }\n  goToTop() {\n    this._hover.scrollbar.setScrollPosition({\n      scrollTop: 0\n    });\n  }\n  goToBottom() {\n    this._hover.scrollbar.setScrollPosition({\n      scrollTop: this._hover.scrollbar.getScrollDimensions().scrollHeight\n    });\n  }\n};\nContentHoverWidget = ContentHoverWidget_1 = __decorate([__param(1, IContextKeyService), __param(2, IConfigurationService), __param(3, IAccessibilityService), __param(4, IKeybindingService)], ContentHoverWidget);\nexport { ContentHoverWidget };\nfunction computeDistanceFromPointToRectangle(pointX, pointY, left, top, width, height) {\n  const x = left + width / 2; // x center of rectangle\n  const y = top + height / 2; // y center of rectangle\n  const dx = Math.max(Math.abs(pointX - x) - width / 2, 0);\n  const dy = Math.max(Math.abs(pointY - y) - height / 2, 0);\n  return Math.sqrt(dx * dx + dy * dy);\n}", "map": {"version": 3, "names": ["__decorate", "decorators", "target", "key", "desc", "c", "arguments", "length", "r", "Object", "getOwnPropertyDescriptor", "d", "Reflect", "decorate", "i", "defineProperty", "__param", "paramIndex", "decorator", "ContentHoverWidget_1", "dom", "IKeybindingService", "ResizableContentWidget", "IContextKeyService", "IConfigurationService", "IAccessibilityService", "EditorContextKeys", "getHoverAccessibleViewHint", "HoverWidget", "Emitter", "HORIZONTAL_SCROLLING_BY", "CONTAINER_HEIGHT_PADDING", "ContentHoverWidget", "ID", "_lastDimensions", "Dimension", "isVisibleFromKeyboard", "_renderedHover", "source", "isVisible", "_hoverVisibleKey", "get", "isFocused", "_hoverFocusedKey", "constructor", "editor", "contextKeyService", "_configurationService", "_accessibilityService", "_keybindingService", "minimumHeight", "getOption", "minimumWidth", "minimumSize", "_hover", "_register", "_onDidResize", "onDidResize", "event", "_minimumSize", "hoverVisible", "bindTo", "hoverFocused", "append", "_resizableNode", "domNode", "containerDomNode", "style", "zIndex", "_editor", "onDidLayoutChange", "_updateMaxDimensions", "onDidChangeConfiguration", "e", "has<PERSON><PERSON>ed", "_updateFont", "focusTracker", "trackFocus", "onDidFocus", "set", "onDidBlur", "_setRenderedHover", "undefined", "addContentWidget", "dispose", "removeContentWidget", "getId", "_applyDimensions", "container", "width", "height", "transformed<PERSON><PERSON><PERSON>", "transformedHeight", "_setContentsDomNodeDimensions", "contentsDomNode", "_setContainerDomNodeDimensions", "_setHoverWidgetDimensions", "_layoutContentWidget", "_applyMaxDimensions", "max<PERSON><PERSON><PERSON>", "maxHeight", "_setHoverWidgetMaxDimensions", "setProperty", "_setAdjustedHoverWidgetDimensions", "size", "_updateResizableNodeMaxDimensions", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_findMaximumRenderingWidth", "Infinity", "maxRenderingHeight", "_findMaximumRenderingHeight", "maxSize", "_resize", "layout", "scrollbar", "scanDomNode", "layoutContentWidget", "fire", "_findAvailableSpaceVertically", "position", "showAtPosition", "_positionPreference", "_availableVerticalSpaceAbove", "_availableVerticalSpaceBelow", "availableSpace", "maximumHeight", "Array", "from", "children", "for<PERSON>ach", "hoverPart", "clientHeight", "Math", "min", "_isHoverTextOverflowing", "overflowing", "some", "hoverElement", "scrollWidth", "clientWidth", "removeProperty", "hasModel", "initialWidth", "_contentWidth", "bodyBoxWidth", "getClientArea", "ownerDocument", "body", "horizontalPadding", "isMouseGettingCloser", "posx", "posy", "initialMousePosX", "initialMousePosY", "widgetRect", "getDomNodePagePosition", "getDomNode", "closestMouseDistance", "computeDistanceFromPointToRectangle", "left", "top", "distance", "renderedHover", "classList", "toggle", "fontSize", "lineHeight", "codeClasses", "prototype", "slice", "call", "getElementsByClassName", "node", "applyFontInfo", "_updateContent", "paddingBottom", "textContent", "append<PERSON><PERSON><PERSON>", "onContentsChanged", "max", "getLayoutInfo", "_render", "render", "getPosition", "secondaryPosition", "showAtSecondaryPosition", "positionAffinity", "should<PERSON><PERSON>arBeforeContent", "preference", "show", "widgetHeight", "getTotalHeight", "widgetPosition", "_findPositionPreference", "shouldFocus", "focus", "activeElement", "accessibleViewHint", "getValue", "isScreenReaderOptimized", "lookupKeybinding", "getAriaLabel", "aria<PERSON><PERSON><PERSON>", "hide", "hoverStoleFocus", "clearSashHoverState", "_removeConstraintsRenderNormally", "layoutInfo", "setMinimumDimensions", "dimensions", "_updateMinimumWidth", "minSize", "getTotalWidth", "scrollUp", "scrollTop", "getScrollPosition", "fontInfo", "setScrollPosition", "scrollDown", "scrollLeft", "scrollRight", "pageUp", "scrollHeight", "getScrollDimensions", "pageDown", "goToTop", "goToBottom", "pointX", "pointY", "x", "y", "dx", "abs", "dy", "sqrt"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/contrib/hover/browser/contentHoverWidget.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = (this && this.__param) || function (paramIndex, decorator) {\n    return function (target, key) { decorator(target, key, paramIndex); }\n};\nvar ContentHoverWidget_1;\nimport * as dom from '../../../../base/browser/dom.js';\nimport { IKeybindingService } from '../../../../platform/keybinding/common/keybinding.js';\nimport { ResizableContentWidget } from './resizableContentWidget.js';\nimport { IContextKeyService } from '../../../../platform/contextkey/common/contextkey.js';\nimport { IConfigurationService } from '../../../../platform/configuration/common/configuration.js';\nimport { IAccessibilityService } from '../../../../platform/accessibility/common/accessibility.js';\nimport { EditorContextKeys } from '../../../common/editorContextKeys.js';\nimport { getHoverAccessibleViewHint, HoverWidget } from '../../../../base/browser/ui/hover/hoverWidget.js';\nimport { Emitter } from '../../../../base/common/event.js';\nconst HORIZONTAL_SCROLLING_BY = 30;\nconst CONTAINER_HEIGHT_PADDING = 6;\nlet ContentHoverWidget = class ContentHoverWidget extends ResizableContentWidget {\n    static { ContentHoverWidget_1 = this; }\n    static { this.ID = 'editor.contrib.resizableContentHoverWidget'; }\n    static { this._lastDimensions = new dom.Dimension(0, 0); }\n    get isVisibleFromKeyboard() {\n        return (this._renderedHover?.source === 1 /* HoverStartSource.Keyboard */);\n    }\n    get isVisible() {\n        return this._hoverVisibleKey.get() ?? false;\n    }\n    get isFocused() {\n        return this._hoverFocusedKey.get() ?? false;\n    }\n    constructor(editor, contextKeyService, _configurationService, _accessibilityService, _keybindingService) {\n        const minimumHeight = editor.getOption(67 /* EditorOption.lineHeight */) + 8;\n        const minimumWidth = 150;\n        const minimumSize = new dom.Dimension(minimumWidth, minimumHeight);\n        super(editor, minimumSize);\n        this._configurationService = _configurationService;\n        this._accessibilityService = _accessibilityService;\n        this._keybindingService = _keybindingService;\n        this._hover = this._register(new HoverWidget());\n        this._onDidResize = this._register(new Emitter());\n        this.onDidResize = this._onDidResize.event;\n        this._minimumSize = minimumSize;\n        this._hoverVisibleKey = EditorContextKeys.hoverVisible.bindTo(contextKeyService);\n        this._hoverFocusedKey = EditorContextKeys.hoverFocused.bindTo(contextKeyService);\n        dom.append(this._resizableNode.domNode, this._hover.containerDomNode);\n        this._resizableNode.domNode.style.zIndex = '50';\n        this._register(this._editor.onDidLayoutChange(() => {\n            if (this.isVisible) {\n                this._updateMaxDimensions();\n            }\n        }));\n        this._register(this._editor.onDidChangeConfiguration((e) => {\n            if (e.hasChanged(50 /* EditorOption.fontInfo */)) {\n                this._updateFont();\n            }\n        }));\n        const focusTracker = this._register(dom.trackFocus(this._resizableNode.domNode));\n        this._register(focusTracker.onDidFocus(() => {\n            this._hoverFocusedKey.set(true);\n        }));\n        this._register(focusTracker.onDidBlur(() => {\n            this._hoverFocusedKey.set(false);\n        }));\n        this._setRenderedHover(undefined);\n        this._editor.addContentWidget(this);\n    }\n    dispose() {\n        super.dispose();\n        this._renderedHover?.dispose();\n        this._editor.removeContentWidget(this);\n    }\n    getId() {\n        return ContentHoverWidget_1.ID;\n    }\n    static _applyDimensions(container, width, height) {\n        const transformedWidth = typeof width === 'number' ? `${width}px` : width;\n        const transformedHeight = typeof height === 'number' ? `${height}px` : height;\n        container.style.width = transformedWidth;\n        container.style.height = transformedHeight;\n    }\n    _setContentsDomNodeDimensions(width, height) {\n        const contentsDomNode = this._hover.contentsDomNode;\n        return ContentHoverWidget_1._applyDimensions(contentsDomNode, width, height);\n    }\n    _setContainerDomNodeDimensions(width, height) {\n        const containerDomNode = this._hover.containerDomNode;\n        return ContentHoverWidget_1._applyDimensions(containerDomNode, width, height);\n    }\n    _setHoverWidgetDimensions(width, height) {\n        this._setContentsDomNodeDimensions(width, height);\n        this._setContainerDomNodeDimensions(width, height);\n        this._layoutContentWidget();\n    }\n    static _applyMaxDimensions(container, width, height) {\n        const transformedWidth = typeof width === 'number' ? `${width}px` : width;\n        const transformedHeight = typeof height === 'number' ? `${height}px` : height;\n        container.style.maxWidth = transformedWidth;\n        container.style.maxHeight = transformedHeight;\n    }\n    _setHoverWidgetMaxDimensions(width, height) {\n        ContentHoverWidget_1._applyMaxDimensions(this._hover.contentsDomNode, width, height);\n        ContentHoverWidget_1._applyMaxDimensions(this._hover.containerDomNode, width, height);\n        this._hover.containerDomNode.style.setProperty('--vscode-hover-maxWidth', typeof width === 'number' ? `${width}px` : width);\n        this._layoutContentWidget();\n    }\n    _setAdjustedHoverWidgetDimensions(size) {\n        this._setHoverWidgetMaxDimensions('none', 'none');\n        const width = size.width;\n        const height = size.height;\n        this._setHoverWidgetDimensions(width, height);\n    }\n    _updateResizableNodeMaxDimensions() {\n        const maxRenderingWidth = this._findMaximumRenderingWidth() ?? Infinity;\n        const maxRenderingHeight = this._findMaximumRenderingHeight() ?? Infinity;\n        this._resizableNode.maxSize = new dom.Dimension(maxRenderingWidth, maxRenderingHeight);\n        this._setHoverWidgetMaxDimensions(maxRenderingWidth, maxRenderingHeight);\n    }\n    _resize(size) {\n        ContentHoverWidget_1._lastDimensions = new dom.Dimension(size.width, size.height);\n        this._setAdjustedHoverWidgetDimensions(size);\n        this._resizableNode.layout(size.height, size.width);\n        this._updateResizableNodeMaxDimensions();\n        this._hover.scrollbar.scanDomNode();\n        this._editor.layoutContentWidget(this);\n        this._onDidResize.fire();\n    }\n    _findAvailableSpaceVertically() {\n        const position = this._renderedHover?.showAtPosition;\n        if (!position) {\n            return;\n        }\n        return this._positionPreference === 1 /* ContentWidgetPositionPreference.ABOVE */ ?\n            this._availableVerticalSpaceAbove(position)\n            : this._availableVerticalSpaceBelow(position);\n    }\n    _findMaximumRenderingHeight() {\n        const availableSpace = this._findAvailableSpaceVertically();\n        if (!availableSpace) {\n            return;\n        }\n        // Padding needed in order to stop the resizing down to a smaller height\n        let maximumHeight = CONTAINER_HEIGHT_PADDING;\n        Array.from(this._hover.contentsDomNode.children).forEach((hoverPart) => {\n            maximumHeight += hoverPart.clientHeight;\n        });\n        return Math.min(availableSpace, maximumHeight);\n    }\n    _isHoverTextOverflowing() {\n        // To find out if the text is overflowing, we will disable wrapping, check the widths, and then re-enable wrapping\n        this._hover.containerDomNode.style.setProperty('--vscode-hover-whiteSpace', 'nowrap');\n        this._hover.containerDomNode.style.setProperty('--vscode-hover-sourceWhiteSpace', 'nowrap');\n        const overflowing = Array.from(this._hover.contentsDomNode.children).some((hoverElement) => {\n            return hoverElement.scrollWidth > hoverElement.clientWidth;\n        });\n        this._hover.containerDomNode.style.removeProperty('--vscode-hover-whiteSpace');\n        this._hover.containerDomNode.style.removeProperty('--vscode-hover-sourceWhiteSpace');\n        return overflowing;\n    }\n    _findMaximumRenderingWidth() {\n        if (!this._editor || !this._editor.hasModel()) {\n            return;\n        }\n        const overflowing = this._isHoverTextOverflowing();\n        const initialWidth = (typeof this._contentWidth === 'undefined'\n            ? 0\n            : this._contentWidth - 2 // - 2 for the borders\n        );\n        if (overflowing || this._hover.containerDomNode.clientWidth < initialWidth) {\n            const bodyBoxWidth = dom.getClientArea(this._hover.containerDomNode.ownerDocument.body).width;\n            const horizontalPadding = 14;\n            return bodyBoxWidth - horizontalPadding;\n        }\n        else {\n            return this._hover.containerDomNode.clientWidth + 2;\n        }\n    }\n    isMouseGettingCloser(posx, posy) {\n        if (!this._renderedHover) {\n            return false;\n        }\n        if (this._renderedHover.initialMousePosX === undefined || this._renderedHover.initialMousePosY === undefined) {\n            this._renderedHover.initialMousePosX = posx;\n            this._renderedHover.initialMousePosY = posy;\n            return false;\n        }\n        const widgetRect = dom.getDomNodePagePosition(this.getDomNode());\n        if (this._renderedHover.closestMouseDistance === undefined) {\n            this._renderedHover.closestMouseDistance = computeDistanceFromPointToRectangle(this._renderedHover.initialMousePosX, this._renderedHover.initialMousePosY, widgetRect.left, widgetRect.top, widgetRect.width, widgetRect.height);\n        }\n        const distance = computeDistanceFromPointToRectangle(posx, posy, widgetRect.left, widgetRect.top, widgetRect.width, widgetRect.height);\n        if (distance > this._renderedHover.closestMouseDistance + 4 /* tolerance of 4 pixels */) {\n            // The mouse is getting farther away\n            return false;\n        }\n        this._renderedHover.closestMouseDistance = Math.min(this._renderedHover.closestMouseDistance, distance);\n        return true;\n    }\n    _setRenderedHover(renderedHover) {\n        this._renderedHover?.dispose();\n        this._renderedHover = renderedHover;\n        this._hoverVisibleKey.set(!!renderedHover);\n        this._hover.containerDomNode.classList.toggle('hidden', !renderedHover);\n    }\n    _updateFont() {\n        const { fontSize, lineHeight } = this._editor.getOption(50 /* EditorOption.fontInfo */);\n        const contentsDomNode = this._hover.contentsDomNode;\n        contentsDomNode.style.fontSize = `${fontSize}px`;\n        contentsDomNode.style.lineHeight = `${lineHeight / fontSize}`;\n        const codeClasses = Array.prototype.slice.call(this._hover.contentsDomNode.getElementsByClassName('code'));\n        codeClasses.forEach(node => this._editor.applyFontInfo(node));\n    }\n    _updateContent(node) {\n        const contentsDomNode = this._hover.contentsDomNode;\n        contentsDomNode.style.paddingBottom = '';\n        contentsDomNode.textContent = '';\n        contentsDomNode.appendChild(node);\n    }\n    _layoutContentWidget() {\n        this._editor.layoutContentWidget(this);\n        this._hover.onContentsChanged();\n    }\n    _updateMaxDimensions() {\n        const height = Math.max(this._editor.getLayoutInfo().height / 4, 250, ContentHoverWidget_1._lastDimensions.height);\n        const width = Math.max(this._editor.getLayoutInfo().width * 0.66, 500, ContentHoverWidget_1._lastDimensions.width);\n        this._setHoverWidgetMaxDimensions(width, height);\n    }\n    _render(renderedHover) {\n        this._setRenderedHover(renderedHover);\n        this._updateFont();\n        this._updateContent(renderedHover.domNode);\n        this._updateMaxDimensions();\n        this.onContentsChanged();\n        // Simply force a synchronous render on the editor\n        // such that the widget does not really render with left = '0px'\n        this._editor.render();\n    }\n    getPosition() {\n        if (!this._renderedHover) {\n            return null;\n        }\n        return {\n            position: this._renderedHover.showAtPosition,\n            secondaryPosition: this._renderedHover.showAtSecondaryPosition,\n            positionAffinity: this._renderedHover.shouldAppearBeforeContent ? 3 /* PositionAffinity.LeftOfInjectedText */ : undefined,\n            preference: [this._positionPreference ?? 1 /* ContentWidgetPositionPreference.ABOVE */]\n        };\n    }\n    show(renderedHover) {\n        if (!this._editor || !this._editor.hasModel()) {\n            return;\n        }\n        this._render(renderedHover);\n        const widgetHeight = dom.getTotalHeight(this._hover.containerDomNode);\n        const widgetPosition = renderedHover.showAtPosition;\n        this._positionPreference = this._findPositionPreference(widgetHeight, widgetPosition) ?? 1 /* ContentWidgetPositionPreference.ABOVE */;\n        // See https://github.com/microsoft/vscode/issues/140339\n        // TODO: Doing a second layout of the hover after force rendering the editor\n        this.onContentsChanged();\n        if (renderedHover.shouldFocus) {\n            this._hover.containerDomNode.focus();\n        }\n        this._onDidResize.fire();\n        // The aria label overrides the label, so if we add to it, add the contents of the hover\n        const hoverFocused = this._hover.containerDomNode.ownerDocument.activeElement === this._hover.containerDomNode;\n        const accessibleViewHint = hoverFocused && getHoverAccessibleViewHint(this._configurationService.getValue('accessibility.verbosity.hover') === true && this._accessibilityService.isScreenReaderOptimized(), this._keybindingService.lookupKeybinding('editor.action.accessibleView')?.getAriaLabel() ?? '');\n        if (accessibleViewHint) {\n            this._hover.contentsDomNode.ariaLabel = this._hover.contentsDomNode.textContent + ', ' + accessibleViewHint;\n        }\n    }\n    hide() {\n        if (!this._renderedHover) {\n            return;\n        }\n        const hoverStoleFocus = this._renderedHover.shouldFocus || this._hoverFocusedKey.get();\n        this._setRenderedHover(undefined);\n        this._resizableNode.maxSize = new dom.Dimension(Infinity, Infinity);\n        this._resizableNode.clearSashHoverState();\n        this._hoverFocusedKey.set(false);\n        this._editor.layoutContentWidget(this);\n        if (hoverStoleFocus) {\n            this._editor.focus();\n        }\n    }\n    _removeConstraintsRenderNormally() {\n        // Added because otherwise the initial size of the hover content is smaller than should be\n        const layoutInfo = this._editor.getLayoutInfo();\n        this._resizableNode.layout(layoutInfo.height, layoutInfo.width);\n        this._setHoverWidgetDimensions('auto', 'auto');\n    }\n    setMinimumDimensions(dimensions) {\n        // We combine the new minimum dimensions with the previous ones\n        this._minimumSize = new dom.Dimension(Math.max(this._minimumSize.width, dimensions.width), Math.max(this._minimumSize.height, dimensions.height));\n        this._updateMinimumWidth();\n    }\n    _updateMinimumWidth() {\n        const width = (typeof this._contentWidth === 'undefined'\n            ? this._minimumSize.width\n            : Math.min(this._contentWidth, this._minimumSize.width));\n        // We want to avoid that the hover is artificially large, so we use the content width as minimum width\n        this._resizableNode.minSize = new dom.Dimension(width, this._minimumSize.height);\n    }\n    onContentsChanged() {\n        this._removeConstraintsRenderNormally();\n        const containerDomNode = this._hover.containerDomNode;\n        let height = dom.getTotalHeight(containerDomNode);\n        let width = dom.getTotalWidth(containerDomNode);\n        this._resizableNode.layout(height, width);\n        this._setHoverWidgetDimensions(width, height);\n        height = dom.getTotalHeight(containerDomNode);\n        width = dom.getTotalWidth(containerDomNode);\n        this._contentWidth = width;\n        this._updateMinimumWidth();\n        this._resizableNode.layout(height, width);\n        if (this._renderedHover?.showAtPosition) {\n            const widgetHeight = dom.getTotalHeight(this._hover.containerDomNode);\n            this._positionPreference = this._findPositionPreference(widgetHeight, this._renderedHover.showAtPosition);\n        }\n        this._layoutContentWidget();\n    }\n    focus() {\n        this._hover.containerDomNode.focus();\n    }\n    scrollUp() {\n        const scrollTop = this._hover.scrollbar.getScrollPosition().scrollTop;\n        const fontInfo = this._editor.getOption(50 /* EditorOption.fontInfo */);\n        this._hover.scrollbar.setScrollPosition({ scrollTop: scrollTop - fontInfo.lineHeight });\n    }\n    scrollDown() {\n        const scrollTop = this._hover.scrollbar.getScrollPosition().scrollTop;\n        const fontInfo = this._editor.getOption(50 /* EditorOption.fontInfo */);\n        this._hover.scrollbar.setScrollPosition({ scrollTop: scrollTop + fontInfo.lineHeight });\n    }\n    scrollLeft() {\n        const scrollLeft = this._hover.scrollbar.getScrollPosition().scrollLeft;\n        this._hover.scrollbar.setScrollPosition({ scrollLeft: scrollLeft - HORIZONTAL_SCROLLING_BY });\n    }\n    scrollRight() {\n        const scrollLeft = this._hover.scrollbar.getScrollPosition().scrollLeft;\n        this._hover.scrollbar.setScrollPosition({ scrollLeft: scrollLeft + HORIZONTAL_SCROLLING_BY });\n    }\n    pageUp() {\n        const scrollTop = this._hover.scrollbar.getScrollPosition().scrollTop;\n        const scrollHeight = this._hover.scrollbar.getScrollDimensions().height;\n        this._hover.scrollbar.setScrollPosition({ scrollTop: scrollTop - scrollHeight });\n    }\n    pageDown() {\n        const scrollTop = this._hover.scrollbar.getScrollPosition().scrollTop;\n        const scrollHeight = this._hover.scrollbar.getScrollDimensions().height;\n        this._hover.scrollbar.setScrollPosition({ scrollTop: scrollTop + scrollHeight });\n    }\n    goToTop() {\n        this._hover.scrollbar.setScrollPosition({ scrollTop: 0 });\n    }\n    goToBottom() {\n        this._hover.scrollbar.setScrollPosition({ scrollTop: this._hover.scrollbar.getScrollDimensions().scrollHeight });\n    }\n};\nContentHoverWidget = ContentHoverWidget_1 = __decorate([\n    __param(1, IContextKeyService),\n    __param(2, IConfigurationService),\n    __param(3, IAccessibilityService),\n    __param(4, IKeybindingService)\n], ContentHoverWidget);\nexport { ContentHoverWidget };\nfunction computeDistanceFromPointToRectangle(pointX, pointY, left, top, width, height) {\n    const x = (left + width / 2); // x center of rectangle\n    const y = (top + height / 2); // y center of rectangle\n    const dx = Math.max(Math.abs(pointX - x) - width / 2, 0);\n    const dy = Math.max(Math.abs(pointY - y) - height / 2, 0);\n    return Math.sqrt(dx * dx + dy * dy);\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,IAAIA,UAAU,GAAI,IAAI,IAAI,IAAI,CAACA,UAAU,IAAK,UAAUC,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAE;EACnF,IAAIC,CAAC,GAAGC,SAAS,CAACC,MAAM;IAAEC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGH,MAAM,GAAGE,IAAI,KAAK,IAAI,GAAGA,IAAI,GAAGK,MAAM,CAACC,wBAAwB,CAACR,MAAM,EAAEC,GAAG,CAAC,GAAGC,IAAI;IAAEO,CAAC;EAC5H,IAAI,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,CAACC,QAAQ,KAAK,UAAU,EAAEL,CAAC,GAAGI,OAAO,CAACC,QAAQ,CAACZ,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,CAAC,CAAC,KAC1H,KAAK,IAAIU,CAAC,GAAGb,UAAU,CAACM,MAAM,GAAG,CAAC,EAAEO,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE,IAAIH,CAAC,GAAGV,UAAU,CAACa,CAAC,CAAC,EAAEN,CAAC,GAAG,CAACH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACH,CAAC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACT,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,GAAGG,CAAC,CAACT,MAAM,EAAEC,GAAG,CAAC,KAAKK,CAAC;EACjJ,OAAOH,CAAC,GAAG,CAAC,IAAIG,CAAC,IAAIC,MAAM,CAACM,cAAc,CAACb,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,EAAEA,CAAC;AACjE,CAAC;AACD,IAAIQ,OAAO,GAAI,IAAI,IAAI,IAAI,CAACA,OAAO,IAAK,UAAUC,UAAU,EAAEC,SAAS,EAAE;EACrE,OAAO,UAAUhB,MAAM,EAAEC,GAAG,EAAE;IAAEe,SAAS,CAAChB,MAAM,EAAEC,GAAG,EAAEc,UAAU,CAAC;EAAE,CAAC;AACzE,CAAC;AACD,IAAIE,oBAAoB;AACxB,OAAO,KAAKC,GAAG,MAAM,iCAAiC;AACtD,SAASC,kBAAkB,QAAQ,sDAAsD;AACzF,SAASC,sBAAsB,QAAQ,6BAA6B;AACpE,SAASC,kBAAkB,QAAQ,sDAAsD;AACzF,SAASC,qBAAqB,QAAQ,4DAA4D;AAClG,SAASC,qBAAqB,QAAQ,4DAA4D;AAClG,SAASC,iBAAiB,QAAQ,sCAAsC;AACxE,SAASC,0BAA0B,EAAEC,WAAW,QAAQ,kDAAkD;AAC1G,SAASC,OAAO,QAAQ,kCAAkC;AAC1D,MAAMC,uBAAuB,GAAG,EAAE;AAClC,MAAMC,wBAAwB,GAAG,CAAC;AAClC,IAAIC,kBAAkB,GAAG,MAAMA,kBAAkB,SAASV,sBAAsB,CAAC;EAC7E;IAASH,oBAAoB,GAAG,IAAI;EAAE;EACtC;IAAS,IAAI,CAACc,EAAE,GAAG,4CAA4C;EAAE;EACjE;IAAS,IAAI,CAACC,eAAe,GAAG,IAAId,GAAG,CAACe,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;EAAE;EACzD,IAAIC,qBAAqBA,CAAA,EAAG;IACxB,OAAQ,IAAI,CAACC,cAAc,EAAEC,MAAM,KAAK,CAAC,CAAC;EAC9C;EACA,IAAIC,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACC,gBAAgB,CAACC,GAAG,CAAC,CAAC,IAAI,KAAK;EAC/C;EACA,IAAIC,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACC,gBAAgB,CAACF,GAAG,CAAC,CAAC,IAAI,KAAK;EAC/C;EACAG,WAAWA,CAACC,MAAM,EAAEC,iBAAiB,EAAEC,qBAAqB,EAAEC,qBAAqB,EAAEC,kBAAkB,EAAE;IACrG,MAAMC,aAAa,GAAGL,MAAM,CAACM,SAAS,CAAC,EAAE,CAAC,6BAA6B,CAAC,GAAG,CAAC;IAC5E,MAAMC,YAAY,GAAG,GAAG;IACxB,MAAMC,WAAW,GAAG,IAAIjC,GAAG,CAACe,SAAS,CAACiB,YAAY,EAAEF,aAAa,CAAC;IAClE,KAAK,CAACL,MAAM,EAAEQ,WAAW,CAAC;IAC1B,IAAI,CAACN,qBAAqB,GAAGA,qBAAqB;IAClD,IAAI,CAACC,qBAAqB,GAAGA,qBAAqB;IAClD,IAAI,CAACC,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACK,MAAM,GAAG,IAAI,CAACC,SAAS,CAAC,IAAI3B,WAAW,CAAC,CAAC,CAAC;IAC/C,IAAI,CAAC4B,YAAY,GAAG,IAAI,CAACD,SAAS,CAAC,IAAI1B,OAAO,CAAC,CAAC,CAAC;IACjD,IAAI,CAAC4B,WAAW,GAAG,IAAI,CAACD,YAAY,CAACE,KAAK;IAC1C,IAAI,CAACC,YAAY,GAAGN,WAAW;IAC/B,IAAI,CAACb,gBAAgB,GAAGd,iBAAiB,CAACkC,YAAY,CAACC,MAAM,CAACf,iBAAiB,CAAC;IAChF,IAAI,CAACH,gBAAgB,GAAGjB,iBAAiB,CAACoC,YAAY,CAACD,MAAM,CAACf,iBAAiB,CAAC;IAChF1B,GAAG,CAAC2C,MAAM,CAAC,IAAI,CAACC,cAAc,CAACC,OAAO,EAAE,IAAI,CAACX,MAAM,CAACY,gBAAgB,CAAC;IACrE,IAAI,CAACF,cAAc,CAACC,OAAO,CAACE,KAAK,CAACC,MAAM,GAAG,IAAI;IAC/C,IAAI,CAACb,SAAS,CAAC,IAAI,CAACc,OAAO,CAACC,iBAAiB,CAAC,MAAM;MAChD,IAAI,IAAI,CAAC/B,SAAS,EAAE;QAChB,IAAI,CAACgC,oBAAoB,CAAC,CAAC;MAC/B;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,CAAChB,SAAS,CAAC,IAAI,CAACc,OAAO,CAACG,wBAAwB,CAAEC,CAAC,IAAK;MACxD,IAAIA,CAAC,CAACC,UAAU,CAAC,EAAE,CAAC,2BAA2B,CAAC,EAAE;QAC9C,IAAI,CAACC,WAAW,CAAC,CAAC;MACtB;IACJ,CAAC,CAAC,CAAC;IACH,MAAMC,YAAY,GAAG,IAAI,CAACrB,SAAS,CAACnC,GAAG,CAACyD,UAAU,CAAC,IAAI,CAACb,cAAc,CAACC,OAAO,CAAC,CAAC;IAChF,IAAI,CAACV,SAAS,CAACqB,YAAY,CAACE,UAAU,CAAC,MAAM;MACzC,IAAI,CAACnC,gBAAgB,CAACoC,GAAG,CAAC,IAAI,CAAC;IACnC,CAAC,CAAC,CAAC;IACH,IAAI,CAACxB,SAAS,CAACqB,YAAY,CAACI,SAAS,CAAC,MAAM;MACxC,IAAI,CAACrC,gBAAgB,CAACoC,GAAG,CAAC,KAAK,CAAC;IACpC,CAAC,CAAC,CAAC;IACH,IAAI,CAACE,iBAAiB,CAACC,SAAS,CAAC;IACjC,IAAI,CAACb,OAAO,CAACc,gBAAgB,CAAC,IAAI,CAAC;EACvC;EACAC,OAAOA,CAAA,EAAG;IACN,KAAK,CAACA,OAAO,CAAC,CAAC;IACf,IAAI,CAAC/C,cAAc,EAAE+C,OAAO,CAAC,CAAC;IAC9B,IAAI,CAACf,OAAO,CAACgB,mBAAmB,CAAC,IAAI,CAAC;EAC1C;EACAC,KAAKA,CAAA,EAAG;IACJ,OAAOnE,oBAAoB,CAACc,EAAE;EAClC;EACA,OAAOsD,gBAAgBA,CAACC,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAE;IAC9C,MAAMC,gBAAgB,GAAG,OAAOF,KAAK,KAAK,QAAQ,GAAG,GAAGA,KAAK,IAAI,GAAGA,KAAK;IACzE,MAAMG,iBAAiB,GAAG,OAAOF,MAAM,KAAK,QAAQ,GAAG,GAAGA,MAAM,IAAI,GAAGA,MAAM;IAC7EF,SAAS,CAACrB,KAAK,CAACsB,KAAK,GAAGE,gBAAgB;IACxCH,SAAS,CAACrB,KAAK,CAACuB,MAAM,GAAGE,iBAAiB;EAC9C;EACAC,6BAA6BA,CAACJ,KAAK,EAAEC,MAAM,EAAE;IACzC,MAAMI,eAAe,GAAG,IAAI,CAACxC,MAAM,CAACwC,eAAe;IACnD,OAAO3E,oBAAoB,CAACoE,gBAAgB,CAACO,eAAe,EAAEL,KAAK,EAAEC,MAAM,CAAC;EAChF;EACAK,8BAA8BA,CAACN,KAAK,EAAEC,MAAM,EAAE;IAC1C,MAAMxB,gBAAgB,GAAG,IAAI,CAACZ,MAAM,CAACY,gBAAgB;IACrD,OAAO/C,oBAAoB,CAACoE,gBAAgB,CAACrB,gBAAgB,EAAEuB,KAAK,EAAEC,MAAM,CAAC;EACjF;EACAM,yBAAyBA,CAACP,KAAK,EAAEC,MAAM,EAAE;IACrC,IAAI,CAACG,6BAA6B,CAACJ,KAAK,EAAEC,MAAM,CAAC;IACjD,IAAI,CAACK,8BAA8B,CAACN,KAAK,EAAEC,MAAM,CAAC;IAClD,IAAI,CAACO,oBAAoB,CAAC,CAAC;EAC/B;EACA,OAAOC,mBAAmBA,CAACV,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAE;IACjD,MAAMC,gBAAgB,GAAG,OAAOF,KAAK,KAAK,QAAQ,GAAG,GAAGA,KAAK,IAAI,GAAGA,KAAK;IACzE,MAAMG,iBAAiB,GAAG,OAAOF,MAAM,KAAK,QAAQ,GAAG,GAAGA,MAAM,IAAI,GAAGA,MAAM;IAC7EF,SAAS,CAACrB,KAAK,CAACgC,QAAQ,GAAGR,gBAAgB;IAC3CH,SAAS,CAACrB,KAAK,CAACiC,SAAS,GAAGR,iBAAiB;EACjD;EACAS,4BAA4BA,CAACZ,KAAK,EAAEC,MAAM,EAAE;IACxCvE,oBAAoB,CAAC+E,mBAAmB,CAAC,IAAI,CAAC5C,MAAM,CAACwC,eAAe,EAAEL,KAAK,EAAEC,MAAM,CAAC;IACpFvE,oBAAoB,CAAC+E,mBAAmB,CAAC,IAAI,CAAC5C,MAAM,CAACY,gBAAgB,EAAEuB,KAAK,EAAEC,MAAM,CAAC;IACrF,IAAI,CAACpC,MAAM,CAACY,gBAAgB,CAACC,KAAK,CAACmC,WAAW,CAAC,yBAAyB,EAAE,OAAOb,KAAK,KAAK,QAAQ,GAAG,GAAGA,KAAK,IAAI,GAAGA,KAAK,CAAC;IAC3H,IAAI,CAACQ,oBAAoB,CAAC,CAAC;EAC/B;EACAM,iCAAiCA,CAACC,IAAI,EAAE;IACpC,IAAI,CAACH,4BAA4B,CAAC,MAAM,EAAE,MAAM,CAAC;IACjD,MAAMZ,KAAK,GAAGe,IAAI,CAACf,KAAK;IACxB,MAAMC,MAAM,GAAGc,IAAI,CAACd,MAAM;IAC1B,IAAI,CAACM,yBAAyB,CAACP,KAAK,EAAEC,MAAM,CAAC;EACjD;EACAe,iCAAiCA,CAAA,EAAG;IAChC,MAAMC,iBAAiB,GAAG,IAAI,CAACC,0BAA0B,CAAC,CAAC,IAAIC,QAAQ;IACvE,MAAMC,kBAAkB,GAAG,IAAI,CAACC,2BAA2B,CAAC,CAAC,IAAIF,QAAQ;IACzE,IAAI,CAAC5C,cAAc,CAAC+C,OAAO,GAAG,IAAI3F,GAAG,CAACe,SAAS,CAACuE,iBAAiB,EAAEG,kBAAkB,CAAC;IACtF,IAAI,CAACR,4BAA4B,CAACK,iBAAiB,EAAEG,kBAAkB,CAAC;EAC5E;EACAG,OAAOA,CAACR,IAAI,EAAE;IACVrF,oBAAoB,CAACe,eAAe,GAAG,IAAId,GAAG,CAACe,SAAS,CAACqE,IAAI,CAACf,KAAK,EAAEe,IAAI,CAACd,MAAM,CAAC;IACjF,IAAI,CAACa,iCAAiC,CAACC,IAAI,CAAC;IAC5C,IAAI,CAACxC,cAAc,CAACiD,MAAM,CAACT,IAAI,CAACd,MAAM,EAAEc,IAAI,CAACf,KAAK,CAAC;IACnD,IAAI,CAACgB,iCAAiC,CAAC,CAAC;IACxC,IAAI,CAACnD,MAAM,CAAC4D,SAAS,CAACC,WAAW,CAAC,CAAC;IACnC,IAAI,CAAC9C,OAAO,CAAC+C,mBAAmB,CAAC,IAAI,CAAC;IACtC,IAAI,CAAC5D,YAAY,CAAC6D,IAAI,CAAC,CAAC;EAC5B;EACAC,6BAA6BA,CAAA,EAAG;IAC5B,MAAMC,QAAQ,GAAG,IAAI,CAAClF,cAAc,EAAEmF,cAAc;IACpD,IAAI,CAACD,QAAQ,EAAE;MACX;IACJ;IACA,OAAO,IAAI,CAACE,mBAAmB,KAAK,CAAC,CAAC,8CAClC,IAAI,CAACC,4BAA4B,CAACH,QAAQ,CAAC,GACzC,IAAI,CAACI,4BAA4B,CAACJ,QAAQ,CAAC;EACrD;EACAT,2BAA2BA,CAAA,EAAG;IAC1B,MAAMc,cAAc,GAAG,IAAI,CAACN,6BAA6B,CAAC,CAAC;IAC3D,IAAI,CAACM,cAAc,EAAE;MACjB;IACJ;IACA;IACA,IAAIC,aAAa,GAAG9F,wBAAwB;IAC5C+F,KAAK,CAACC,IAAI,CAAC,IAAI,CAACzE,MAAM,CAACwC,eAAe,CAACkC,QAAQ,CAAC,CAACC,OAAO,CAAEC,SAAS,IAAK;MACpEL,aAAa,IAAIK,SAAS,CAACC,YAAY;IAC3C,CAAC,CAAC;IACF,OAAOC,IAAI,CAACC,GAAG,CAACT,cAAc,EAAEC,aAAa,CAAC;EAClD;EACAS,uBAAuBA,CAAA,EAAG;IACtB;IACA,IAAI,CAAChF,MAAM,CAACY,gBAAgB,CAACC,KAAK,CAACmC,WAAW,CAAC,2BAA2B,EAAE,QAAQ,CAAC;IACrF,IAAI,CAAChD,MAAM,CAACY,gBAAgB,CAACC,KAAK,CAACmC,WAAW,CAAC,iCAAiC,EAAE,QAAQ,CAAC;IAC3F,MAAMiC,WAAW,GAAGT,KAAK,CAACC,IAAI,CAAC,IAAI,CAACzE,MAAM,CAACwC,eAAe,CAACkC,QAAQ,CAAC,CAACQ,IAAI,CAAEC,YAAY,IAAK;MACxF,OAAOA,YAAY,CAACC,WAAW,GAAGD,YAAY,CAACE,WAAW;IAC9D,CAAC,CAAC;IACF,IAAI,CAACrF,MAAM,CAACY,gBAAgB,CAACC,KAAK,CAACyE,cAAc,CAAC,2BAA2B,CAAC;IAC9E,IAAI,CAACtF,MAAM,CAACY,gBAAgB,CAACC,KAAK,CAACyE,cAAc,CAAC,iCAAiC,CAAC;IACpF,OAAOL,WAAW;EACtB;EACA5B,0BAA0BA,CAAA,EAAG;IACzB,IAAI,CAAC,IAAI,CAACtC,OAAO,IAAI,CAAC,IAAI,CAACA,OAAO,CAACwE,QAAQ,CAAC,CAAC,EAAE;MAC3C;IACJ;IACA,MAAMN,WAAW,GAAG,IAAI,CAACD,uBAAuB,CAAC,CAAC;IAClD,MAAMQ,YAAY,GAAI,OAAO,IAAI,CAACC,aAAa,KAAK,WAAW,GACzD,CAAC,GACD,IAAI,CAACA,aAAa,GAAG,CAAC,CAAC;IAC5B;IACD,IAAIR,WAAW,IAAI,IAAI,CAACjF,MAAM,CAACY,gBAAgB,CAACyE,WAAW,GAAGG,YAAY,EAAE;MACxE,MAAME,YAAY,GAAG5H,GAAG,CAAC6H,aAAa,CAAC,IAAI,CAAC3F,MAAM,CAACY,gBAAgB,CAACgF,aAAa,CAACC,IAAI,CAAC,CAAC1D,KAAK;MAC7F,MAAM2D,iBAAiB,GAAG,EAAE;MAC5B,OAAOJ,YAAY,GAAGI,iBAAiB;IAC3C,CAAC,MACI;MACD,OAAO,IAAI,CAAC9F,MAAM,CAACY,gBAAgB,CAACyE,WAAW,GAAG,CAAC;IACvD;EACJ;EACAU,oBAAoBA,CAACC,IAAI,EAAEC,IAAI,EAAE;IAC7B,IAAI,CAAC,IAAI,CAAClH,cAAc,EAAE;MACtB,OAAO,KAAK;IAChB;IACA,IAAI,IAAI,CAACA,cAAc,CAACmH,gBAAgB,KAAKtE,SAAS,IAAI,IAAI,CAAC7C,cAAc,CAACoH,gBAAgB,KAAKvE,SAAS,EAAE;MAC1G,IAAI,CAAC7C,cAAc,CAACmH,gBAAgB,GAAGF,IAAI;MAC3C,IAAI,CAACjH,cAAc,CAACoH,gBAAgB,GAAGF,IAAI;MAC3C,OAAO,KAAK;IAChB;IACA,MAAMG,UAAU,GAAGtI,GAAG,CAACuI,sBAAsB,CAAC,IAAI,CAACC,UAAU,CAAC,CAAC,CAAC;IAChE,IAAI,IAAI,CAACvH,cAAc,CAACwH,oBAAoB,KAAK3E,SAAS,EAAE;MACxD,IAAI,CAAC7C,cAAc,CAACwH,oBAAoB,GAAGC,mCAAmC,CAAC,IAAI,CAACzH,cAAc,CAACmH,gBAAgB,EAAE,IAAI,CAACnH,cAAc,CAACoH,gBAAgB,EAAEC,UAAU,CAACK,IAAI,EAAEL,UAAU,CAACM,GAAG,EAAEN,UAAU,CAACjE,KAAK,EAAEiE,UAAU,CAAChE,MAAM,CAAC;IACpO;IACA,MAAMuE,QAAQ,GAAGH,mCAAmC,CAACR,IAAI,EAAEC,IAAI,EAAEG,UAAU,CAACK,IAAI,EAAEL,UAAU,CAACM,GAAG,EAAEN,UAAU,CAACjE,KAAK,EAAEiE,UAAU,CAAChE,MAAM,CAAC;IACtI,IAAIuE,QAAQ,GAAG,IAAI,CAAC5H,cAAc,CAACwH,oBAAoB,GAAG,CAAC,CAAC,6BAA6B;MACrF;MACA,OAAO,KAAK;IAChB;IACA,IAAI,CAACxH,cAAc,CAACwH,oBAAoB,GAAGzB,IAAI,CAACC,GAAG,CAAC,IAAI,CAAChG,cAAc,CAACwH,oBAAoB,EAAEI,QAAQ,CAAC;IACvG,OAAO,IAAI;EACf;EACAhF,iBAAiBA,CAACiF,aAAa,EAAE;IAC7B,IAAI,CAAC7H,cAAc,EAAE+C,OAAO,CAAC,CAAC;IAC9B,IAAI,CAAC/C,cAAc,GAAG6H,aAAa;IACnC,IAAI,CAAC1H,gBAAgB,CAACuC,GAAG,CAAC,CAAC,CAACmF,aAAa,CAAC;IAC1C,IAAI,CAAC5G,MAAM,CAACY,gBAAgB,CAACiG,SAAS,CAACC,MAAM,CAAC,QAAQ,EAAE,CAACF,aAAa,CAAC;EAC3E;EACAvF,WAAWA,CAAA,EAAG;IACV,MAAM;MAAE0F,QAAQ;MAAEC;IAAW,CAAC,GAAG,IAAI,CAACjG,OAAO,CAAClB,SAAS,CAAC,EAAE,CAAC,2BAA2B,CAAC;IACvF,MAAM2C,eAAe,GAAG,IAAI,CAACxC,MAAM,CAACwC,eAAe;IACnDA,eAAe,CAAC3B,KAAK,CAACkG,QAAQ,GAAG,GAAGA,QAAQ,IAAI;IAChDvE,eAAe,CAAC3B,KAAK,CAACmG,UAAU,GAAG,GAAGA,UAAU,GAAGD,QAAQ,EAAE;IAC7D,MAAME,WAAW,GAAGzC,KAAK,CAAC0C,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC,IAAI,CAACpH,MAAM,CAACwC,eAAe,CAAC6E,sBAAsB,CAAC,MAAM,CAAC,CAAC;IAC1GJ,WAAW,CAACtC,OAAO,CAAC2C,IAAI,IAAI,IAAI,CAACvG,OAAO,CAACwG,aAAa,CAACD,IAAI,CAAC,CAAC;EACjE;EACAE,cAAcA,CAACF,IAAI,EAAE;IACjB,MAAM9E,eAAe,GAAG,IAAI,CAACxC,MAAM,CAACwC,eAAe;IACnDA,eAAe,CAAC3B,KAAK,CAAC4G,aAAa,GAAG,EAAE;IACxCjF,eAAe,CAACkF,WAAW,GAAG,EAAE;IAChClF,eAAe,CAACmF,WAAW,CAACL,IAAI,CAAC;EACrC;EACA3E,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAAC5B,OAAO,CAAC+C,mBAAmB,CAAC,IAAI,CAAC;IACtC,IAAI,CAAC9D,MAAM,CAAC4H,iBAAiB,CAAC,CAAC;EACnC;EACA3G,oBAAoBA,CAAA,EAAG;IACnB,MAAMmB,MAAM,GAAG0C,IAAI,CAAC+C,GAAG,CAAC,IAAI,CAAC9G,OAAO,CAAC+G,aAAa,CAAC,CAAC,CAAC1F,MAAM,GAAG,CAAC,EAAE,GAAG,EAAEvE,oBAAoB,CAACe,eAAe,CAACwD,MAAM,CAAC;IAClH,MAAMD,KAAK,GAAG2C,IAAI,CAAC+C,GAAG,CAAC,IAAI,CAAC9G,OAAO,CAAC+G,aAAa,CAAC,CAAC,CAAC3F,KAAK,GAAG,IAAI,EAAE,GAAG,EAAEtE,oBAAoB,CAACe,eAAe,CAACuD,KAAK,CAAC;IAClH,IAAI,CAACY,4BAA4B,CAACZ,KAAK,EAAEC,MAAM,CAAC;EACpD;EACA2F,OAAOA,CAACnB,aAAa,EAAE;IACnB,IAAI,CAACjF,iBAAiB,CAACiF,aAAa,CAAC;IACrC,IAAI,CAACvF,WAAW,CAAC,CAAC;IAClB,IAAI,CAACmG,cAAc,CAACZ,aAAa,CAACjG,OAAO,CAAC;IAC1C,IAAI,CAACM,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAAC2G,iBAAiB,CAAC,CAAC;IACxB;IACA;IACA,IAAI,CAAC7G,OAAO,CAACiH,MAAM,CAAC,CAAC;EACzB;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC,IAAI,CAAClJ,cAAc,EAAE;MACtB,OAAO,IAAI;IACf;IACA,OAAO;MACHkF,QAAQ,EAAE,IAAI,CAAClF,cAAc,CAACmF,cAAc;MAC5CgE,iBAAiB,EAAE,IAAI,CAACnJ,cAAc,CAACoJ,uBAAuB;MAC9DC,gBAAgB,EAAE,IAAI,CAACrJ,cAAc,CAACsJ,yBAAyB,GAAG,CAAC,CAAC,4CAA4CzG,SAAS;MACzH0G,UAAU,EAAE,CAAC,IAAI,CAACnE,mBAAmB,IAAI,CAAC,CAAC;IAC/C,CAAC;EACL;EACAoE,IAAIA,CAAC3B,aAAa,EAAE;IAChB,IAAI,CAAC,IAAI,CAAC7F,OAAO,IAAI,CAAC,IAAI,CAACA,OAAO,CAACwE,QAAQ,CAAC,CAAC,EAAE;MAC3C;IACJ;IACA,IAAI,CAACwC,OAAO,CAACnB,aAAa,CAAC;IAC3B,MAAM4B,YAAY,GAAG1K,GAAG,CAAC2K,cAAc,CAAC,IAAI,CAACzI,MAAM,CAACY,gBAAgB,CAAC;IACrE,MAAM8H,cAAc,GAAG9B,aAAa,CAAC1C,cAAc;IACnD,IAAI,CAACC,mBAAmB,GAAG,IAAI,CAACwE,uBAAuB,CAACH,YAAY,EAAEE,cAAc,CAAC,IAAI,CAAC,CAAC;IAC3F;IACA;IACA,IAAI,CAACd,iBAAiB,CAAC,CAAC;IACxB,IAAIhB,aAAa,CAACgC,WAAW,EAAE;MAC3B,IAAI,CAAC5I,MAAM,CAACY,gBAAgB,CAACiI,KAAK,CAAC,CAAC;IACxC;IACA,IAAI,CAAC3I,YAAY,CAAC6D,IAAI,CAAC,CAAC;IACxB;IACA,MAAMvD,YAAY,GAAG,IAAI,CAACR,MAAM,CAACY,gBAAgB,CAACgF,aAAa,CAACkD,aAAa,KAAK,IAAI,CAAC9I,MAAM,CAACY,gBAAgB;IAC9G,MAAMmI,kBAAkB,GAAGvI,YAAY,IAAInC,0BAA0B,CAAC,IAAI,CAACoB,qBAAqB,CAACuJ,QAAQ,CAAC,+BAA+B,CAAC,KAAK,IAAI,IAAI,IAAI,CAACtJ,qBAAqB,CAACuJ,uBAAuB,CAAC,CAAC,EAAE,IAAI,CAACtJ,kBAAkB,CAACuJ,gBAAgB,CAAC,8BAA8B,CAAC,EAAEC,YAAY,CAAC,CAAC,IAAI,EAAE,CAAC;IAC5S,IAAIJ,kBAAkB,EAAE;MACpB,IAAI,CAAC/I,MAAM,CAACwC,eAAe,CAAC4G,SAAS,GAAG,IAAI,CAACpJ,MAAM,CAACwC,eAAe,CAACkF,WAAW,GAAG,IAAI,GAAGqB,kBAAkB;IAC/G;EACJ;EACAM,IAAIA,CAAA,EAAG;IACH,IAAI,CAAC,IAAI,CAACtK,cAAc,EAAE;MACtB;IACJ;IACA,MAAMuK,eAAe,GAAG,IAAI,CAACvK,cAAc,CAAC6J,WAAW,IAAI,IAAI,CAACvJ,gBAAgB,CAACF,GAAG,CAAC,CAAC;IACtF,IAAI,CAACwC,iBAAiB,CAACC,SAAS,CAAC;IACjC,IAAI,CAAClB,cAAc,CAAC+C,OAAO,GAAG,IAAI3F,GAAG,CAACe,SAAS,CAACyE,QAAQ,EAAEA,QAAQ,CAAC;IACnE,IAAI,CAAC5C,cAAc,CAAC6I,mBAAmB,CAAC,CAAC;IACzC,IAAI,CAAClK,gBAAgB,CAACoC,GAAG,CAAC,KAAK,CAAC;IAChC,IAAI,CAACV,OAAO,CAAC+C,mBAAmB,CAAC,IAAI,CAAC;IACtC,IAAIwF,eAAe,EAAE;MACjB,IAAI,CAACvI,OAAO,CAAC8H,KAAK,CAAC,CAAC;IACxB;EACJ;EACAW,gCAAgCA,CAAA,EAAG;IAC/B;IACA,MAAMC,UAAU,GAAG,IAAI,CAAC1I,OAAO,CAAC+G,aAAa,CAAC,CAAC;IAC/C,IAAI,CAACpH,cAAc,CAACiD,MAAM,CAAC8F,UAAU,CAACrH,MAAM,EAAEqH,UAAU,CAACtH,KAAK,CAAC;IAC/D,IAAI,CAACO,yBAAyB,CAAC,MAAM,EAAE,MAAM,CAAC;EAClD;EACAgH,oBAAoBA,CAACC,UAAU,EAAE;IAC7B;IACA,IAAI,CAACtJ,YAAY,GAAG,IAAIvC,GAAG,CAACe,SAAS,CAACiG,IAAI,CAAC+C,GAAG,CAAC,IAAI,CAACxH,YAAY,CAAC8B,KAAK,EAAEwH,UAAU,CAACxH,KAAK,CAAC,EAAE2C,IAAI,CAAC+C,GAAG,CAAC,IAAI,CAACxH,YAAY,CAAC+B,MAAM,EAAEuH,UAAU,CAACvH,MAAM,CAAC,CAAC;IACjJ,IAAI,CAACwH,mBAAmB,CAAC,CAAC;EAC9B;EACAA,mBAAmBA,CAAA,EAAG;IAClB,MAAMzH,KAAK,GAAI,OAAO,IAAI,CAACsD,aAAa,KAAK,WAAW,GAClD,IAAI,CAACpF,YAAY,CAAC8B,KAAK,GACvB2C,IAAI,CAACC,GAAG,CAAC,IAAI,CAACU,aAAa,EAAE,IAAI,CAACpF,YAAY,CAAC8B,KAAK,CAAE;IAC5D;IACA,IAAI,CAACzB,cAAc,CAACmJ,OAAO,GAAG,IAAI/L,GAAG,CAACe,SAAS,CAACsD,KAAK,EAAE,IAAI,CAAC9B,YAAY,CAAC+B,MAAM,CAAC;EACpF;EACAwF,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC4B,gCAAgC,CAAC,CAAC;IACvC,MAAM5I,gBAAgB,GAAG,IAAI,CAACZ,MAAM,CAACY,gBAAgB;IACrD,IAAIwB,MAAM,GAAGtE,GAAG,CAAC2K,cAAc,CAAC7H,gBAAgB,CAAC;IACjD,IAAIuB,KAAK,GAAGrE,GAAG,CAACgM,aAAa,CAAClJ,gBAAgB,CAAC;IAC/C,IAAI,CAACF,cAAc,CAACiD,MAAM,CAACvB,MAAM,EAAED,KAAK,CAAC;IACzC,IAAI,CAACO,yBAAyB,CAACP,KAAK,EAAEC,MAAM,CAAC;IAC7CA,MAAM,GAAGtE,GAAG,CAAC2K,cAAc,CAAC7H,gBAAgB,CAAC;IAC7CuB,KAAK,GAAGrE,GAAG,CAACgM,aAAa,CAAClJ,gBAAgB,CAAC;IAC3C,IAAI,CAAC6E,aAAa,GAAGtD,KAAK;IAC1B,IAAI,CAACyH,mBAAmB,CAAC,CAAC;IAC1B,IAAI,CAAClJ,cAAc,CAACiD,MAAM,CAACvB,MAAM,EAAED,KAAK,CAAC;IACzC,IAAI,IAAI,CAACpD,cAAc,EAAEmF,cAAc,EAAE;MACrC,MAAMsE,YAAY,GAAG1K,GAAG,CAAC2K,cAAc,CAAC,IAAI,CAACzI,MAAM,CAACY,gBAAgB,CAAC;MACrE,IAAI,CAACuD,mBAAmB,GAAG,IAAI,CAACwE,uBAAuB,CAACH,YAAY,EAAE,IAAI,CAACzJ,cAAc,CAACmF,cAAc,CAAC;IAC7G;IACA,IAAI,CAACvB,oBAAoB,CAAC,CAAC;EAC/B;EACAkG,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAC7I,MAAM,CAACY,gBAAgB,CAACiI,KAAK,CAAC,CAAC;EACxC;EACAkB,QAAQA,CAAA,EAAG;IACP,MAAMC,SAAS,GAAG,IAAI,CAAChK,MAAM,CAAC4D,SAAS,CAACqG,iBAAiB,CAAC,CAAC,CAACD,SAAS;IACrE,MAAME,QAAQ,GAAG,IAAI,CAACnJ,OAAO,CAAClB,SAAS,CAAC,EAAE,CAAC,2BAA2B,CAAC;IACvE,IAAI,CAACG,MAAM,CAAC4D,SAAS,CAACuG,iBAAiB,CAAC;MAAEH,SAAS,EAAEA,SAAS,GAAGE,QAAQ,CAAClD;IAAW,CAAC,CAAC;EAC3F;EACAoD,UAAUA,CAAA,EAAG;IACT,MAAMJ,SAAS,GAAG,IAAI,CAAChK,MAAM,CAAC4D,SAAS,CAACqG,iBAAiB,CAAC,CAAC,CAACD,SAAS;IACrE,MAAME,QAAQ,GAAG,IAAI,CAACnJ,OAAO,CAAClB,SAAS,CAAC,EAAE,CAAC,2BAA2B,CAAC;IACvE,IAAI,CAACG,MAAM,CAAC4D,SAAS,CAACuG,iBAAiB,CAAC;MAAEH,SAAS,EAAEA,SAAS,GAAGE,QAAQ,CAAClD;IAAW,CAAC,CAAC;EAC3F;EACAqD,UAAUA,CAAA,EAAG;IACT,MAAMA,UAAU,GAAG,IAAI,CAACrK,MAAM,CAAC4D,SAAS,CAACqG,iBAAiB,CAAC,CAAC,CAACI,UAAU;IACvE,IAAI,CAACrK,MAAM,CAAC4D,SAAS,CAACuG,iBAAiB,CAAC;MAAEE,UAAU,EAAEA,UAAU,GAAG7L;IAAwB,CAAC,CAAC;EACjG;EACA8L,WAAWA,CAAA,EAAG;IACV,MAAMD,UAAU,GAAG,IAAI,CAACrK,MAAM,CAAC4D,SAAS,CAACqG,iBAAiB,CAAC,CAAC,CAACI,UAAU;IACvE,IAAI,CAACrK,MAAM,CAAC4D,SAAS,CAACuG,iBAAiB,CAAC;MAAEE,UAAU,EAAEA,UAAU,GAAG7L;IAAwB,CAAC,CAAC;EACjG;EACA+L,MAAMA,CAAA,EAAG;IACL,MAAMP,SAAS,GAAG,IAAI,CAAChK,MAAM,CAAC4D,SAAS,CAACqG,iBAAiB,CAAC,CAAC,CAACD,SAAS;IACrE,MAAMQ,YAAY,GAAG,IAAI,CAACxK,MAAM,CAAC4D,SAAS,CAAC6G,mBAAmB,CAAC,CAAC,CAACrI,MAAM;IACvE,IAAI,CAACpC,MAAM,CAAC4D,SAAS,CAACuG,iBAAiB,CAAC;MAAEH,SAAS,EAAEA,SAAS,GAAGQ;IAAa,CAAC,CAAC;EACpF;EACAE,QAAQA,CAAA,EAAG;IACP,MAAMV,SAAS,GAAG,IAAI,CAAChK,MAAM,CAAC4D,SAAS,CAACqG,iBAAiB,CAAC,CAAC,CAACD,SAAS;IACrE,MAAMQ,YAAY,GAAG,IAAI,CAACxK,MAAM,CAAC4D,SAAS,CAAC6G,mBAAmB,CAAC,CAAC,CAACrI,MAAM;IACvE,IAAI,CAACpC,MAAM,CAAC4D,SAAS,CAACuG,iBAAiB,CAAC;MAAEH,SAAS,EAAEA,SAAS,GAAGQ;IAAa,CAAC,CAAC;EACpF;EACAG,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC3K,MAAM,CAAC4D,SAAS,CAACuG,iBAAiB,CAAC;MAAEH,SAAS,EAAE;IAAE,CAAC,CAAC;EAC7D;EACAY,UAAUA,CAAA,EAAG;IACT,IAAI,CAAC5K,MAAM,CAAC4D,SAAS,CAACuG,iBAAiB,CAAC;MAAEH,SAAS,EAAE,IAAI,CAAChK,MAAM,CAAC4D,SAAS,CAAC6G,mBAAmB,CAAC,CAAC,CAACD;IAAa,CAAC,CAAC;EACpH;AACJ,CAAC;AACD9L,kBAAkB,GAAGb,oBAAoB,GAAGnB,UAAU,CAAC,CACnDgB,OAAO,CAAC,CAAC,EAAEO,kBAAkB,CAAC,EAC9BP,OAAO,CAAC,CAAC,EAAEQ,qBAAqB,CAAC,EACjCR,OAAO,CAAC,CAAC,EAAES,qBAAqB,CAAC,EACjCT,OAAO,CAAC,CAAC,EAAEK,kBAAkB,CAAC,CACjC,EAAEW,kBAAkB,CAAC;AACtB,SAASA,kBAAkB;AAC3B,SAAS8H,mCAAmCA,CAACqE,MAAM,EAAEC,MAAM,EAAErE,IAAI,EAAEC,GAAG,EAAEvE,KAAK,EAAEC,MAAM,EAAE;EACnF,MAAM2I,CAAC,GAAItE,IAAI,GAAGtE,KAAK,GAAG,CAAE,CAAC,CAAC;EAC9B,MAAM6I,CAAC,GAAItE,GAAG,GAAGtE,MAAM,GAAG,CAAE,CAAC,CAAC;EAC9B,MAAM6I,EAAE,GAAGnG,IAAI,CAAC+C,GAAG,CAAC/C,IAAI,CAACoG,GAAG,CAACL,MAAM,GAAGE,CAAC,CAAC,GAAG5I,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC;EACxD,MAAMgJ,EAAE,GAAGrG,IAAI,CAAC+C,GAAG,CAAC/C,IAAI,CAACoG,GAAG,CAACJ,MAAM,GAAGE,CAAC,CAAC,GAAG5I,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC;EACzD,OAAO0C,IAAI,CAACsG,IAAI,CAACH,EAAE,GAAGA,EAAE,GAAGE,EAAE,GAAGA,EAAE,CAAC;AACvC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}