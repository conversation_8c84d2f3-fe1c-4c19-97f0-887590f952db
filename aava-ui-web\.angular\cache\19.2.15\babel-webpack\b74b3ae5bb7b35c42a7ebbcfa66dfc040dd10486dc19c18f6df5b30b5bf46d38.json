{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Emitter } from '../../../../base/common/event.js';\nexport class ColorPickerModel {\n  get color() {\n    return this._color;\n  }\n  set color(color) {\n    if (this._color.equals(color)) {\n      return;\n    }\n    this._color = color;\n    this._onDidChangeColor.fire(color);\n  }\n  get presentation() {\n    return this.colorPresentations[this.presentationIndex];\n  }\n  get colorPresentations() {\n    return this._colorPresentations;\n  }\n  set colorPresentations(colorPresentations) {\n    this._colorPresentations = colorPresentations;\n    if (this.presentationIndex > colorPresentations.length - 1) {\n      this.presentationIndex = 0;\n    }\n    this._onDidChangePresentation.fire(this.presentation);\n  }\n  constructor(color, availableColorPresentations, presentationIndex) {\n    this.presentationIndex = presentationIndex;\n    this._onColorFlushed = new Emitter();\n    this.onColorFlushed = this._onColorFlushed.event;\n    this._onDidChangeColor = new Emitter();\n    this.onDidChangeColor = this._onDidChangeColor.event;\n    this._onDidChangePresentation = new Emitter();\n    this.onDidChangePresentation = this._onDidChangePresentation.event;\n    this.originalColor = color;\n    this._color = color;\n    this._colorPresentations = availableColorPresentations;\n  }\n  selectNextColorPresentation() {\n    this.presentationIndex = (this.presentationIndex + 1) % this.colorPresentations.length;\n    this.flushColor();\n    this._onDidChangePresentation.fire(this.presentation);\n  }\n  guessColorPresentation(color, originalText) {\n    let presentationIndex = -1;\n    for (let i = 0; i < this.colorPresentations.length; i++) {\n      if (originalText.toLowerCase() === this.colorPresentations[i].label) {\n        presentationIndex = i;\n        break;\n      }\n    }\n    if (presentationIndex === -1) {\n      // check which color presentation text has same prefix as original text's prefix\n      const originalTextPrefix = originalText.split('(')[0].toLowerCase();\n      for (let i = 0; i < this.colorPresentations.length; i++) {\n        if (this.colorPresentations[i].label.toLowerCase().startsWith(originalTextPrefix)) {\n          presentationIndex = i;\n          break;\n        }\n      }\n    }\n    if (presentationIndex !== -1 && presentationIndex !== this.presentationIndex) {\n      this.presentationIndex = presentationIndex;\n      this._onDidChangePresentation.fire(this.presentation);\n    }\n  }\n  flushColor() {\n    this._onColorFlushed.fire(this._color);\n  }\n}", "map": {"version": 3, "names": ["Emitter", "ColorPickerModel", "color", "_color", "equals", "_onDidChangeColor", "fire", "presentation", "colorPresentations", "presentationIndex", "_colorPresentations", "length", "_onDidChangePresentation", "constructor", "availableColorPresentations", "_onColorFlushed", "onColorFlushed", "event", "onDidChangeColor", "onDidChangePresentation", "originalColor", "selectNextColorPresentation", "flushColor", "guessColorPresentation", "originalText", "i", "toLowerCase", "label", "originalTextPrefix", "split", "startsWith"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/contrib/colorPicker/browser/colorPickerModel.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Emitter } from '../../../../base/common/event.js';\nexport class ColorPickerModel {\n    get color() {\n        return this._color;\n    }\n    set color(color) {\n        if (this._color.equals(color)) {\n            return;\n        }\n        this._color = color;\n        this._onDidChangeColor.fire(color);\n    }\n    get presentation() { return this.colorPresentations[this.presentationIndex]; }\n    get colorPresentations() {\n        return this._colorPresentations;\n    }\n    set colorPresentations(colorPresentations) {\n        this._colorPresentations = colorPresentations;\n        if (this.presentationIndex > colorPresentations.length - 1) {\n            this.presentationIndex = 0;\n        }\n        this._onDidChangePresentation.fire(this.presentation);\n    }\n    constructor(color, availableColorPresentations, presentationIndex) {\n        this.presentationIndex = presentationIndex;\n        this._onColorFlushed = new Emitter();\n        this.onColorFlushed = this._onColorFlushed.event;\n        this._onDidChangeColor = new Emitter();\n        this.onDidChangeColor = this._onDidChangeColor.event;\n        this._onDidChangePresentation = new Emitter();\n        this.onDidChangePresentation = this._onDidChangePresentation.event;\n        this.originalColor = color;\n        this._color = color;\n        this._colorPresentations = availableColorPresentations;\n    }\n    selectNextColorPresentation() {\n        this.presentationIndex = (this.presentationIndex + 1) % this.colorPresentations.length;\n        this.flushColor();\n        this._onDidChangePresentation.fire(this.presentation);\n    }\n    guessColorPresentation(color, originalText) {\n        let presentationIndex = -1;\n        for (let i = 0; i < this.colorPresentations.length; i++) {\n            if (originalText.toLowerCase() === this.colorPresentations[i].label) {\n                presentationIndex = i;\n                break;\n            }\n        }\n        if (presentationIndex === -1) {\n            // check which color presentation text has same prefix as original text's prefix\n            const originalTextPrefix = originalText.split('(')[0].toLowerCase();\n            for (let i = 0; i < this.colorPresentations.length; i++) {\n                if (this.colorPresentations[i].label.toLowerCase().startsWith(originalTextPrefix)) {\n                    presentationIndex = i;\n                    break;\n                }\n            }\n        }\n        if (presentationIndex !== -1 && presentationIndex !== this.presentationIndex) {\n            this.presentationIndex = presentationIndex;\n            this._onDidChangePresentation.fire(this.presentation);\n        }\n    }\n    flushColor() {\n        this._onColorFlushed.fire(this._color);\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,OAAO,QAAQ,kCAAkC;AAC1D,OAAO,MAAMC,gBAAgB,CAAC;EAC1B,IAAIC,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACC,MAAM;EACtB;EACA,IAAID,KAAKA,CAACA,KAAK,EAAE;IACb,IAAI,IAAI,CAACC,MAAM,CAACC,MAAM,CAACF,KAAK,CAAC,EAAE;MAC3B;IACJ;IACA,IAAI,CAACC,MAAM,GAAGD,KAAK;IACnB,IAAI,CAACG,iBAAiB,CAACC,IAAI,CAACJ,KAAK,CAAC;EACtC;EACA,IAAIK,YAAYA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACC,kBAAkB,CAAC,IAAI,CAACC,iBAAiB,CAAC;EAAE;EAC7E,IAAID,kBAAkBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAACE,mBAAmB;EACnC;EACA,IAAIF,kBAAkBA,CAACA,kBAAkB,EAAE;IACvC,IAAI,CAACE,mBAAmB,GAAGF,kBAAkB;IAC7C,IAAI,IAAI,CAACC,iBAAiB,GAAGD,kBAAkB,CAACG,MAAM,GAAG,CAAC,EAAE;MACxD,IAAI,CAACF,iBAAiB,GAAG,CAAC;IAC9B;IACA,IAAI,CAACG,wBAAwB,CAACN,IAAI,CAAC,IAAI,CAACC,YAAY,CAAC;EACzD;EACAM,WAAWA,CAACX,KAAK,EAAEY,2BAA2B,EAAEL,iBAAiB,EAAE;IAC/D,IAAI,CAACA,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACM,eAAe,GAAG,IAAIf,OAAO,CAAC,CAAC;IACpC,IAAI,CAACgB,cAAc,GAAG,IAAI,CAACD,eAAe,CAACE,KAAK;IAChD,IAAI,CAACZ,iBAAiB,GAAG,IAAIL,OAAO,CAAC,CAAC;IACtC,IAAI,CAACkB,gBAAgB,GAAG,IAAI,CAACb,iBAAiB,CAACY,KAAK;IACpD,IAAI,CAACL,wBAAwB,GAAG,IAAIZ,OAAO,CAAC,CAAC;IAC7C,IAAI,CAACmB,uBAAuB,GAAG,IAAI,CAACP,wBAAwB,CAACK,KAAK;IAClE,IAAI,CAACG,aAAa,GAAGlB,KAAK;IAC1B,IAAI,CAACC,MAAM,GAAGD,KAAK;IACnB,IAAI,CAACQ,mBAAmB,GAAGI,2BAA2B;EAC1D;EACAO,2BAA2BA,CAAA,EAAG;IAC1B,IAAI,CAACZ,iBAAiB,GAAG,CAAC,IAAI,CAACA,iBAAiB,GAAG,CAAC,IAAI,IAAI,CAACD,kBAAkB,CAACG,MAAM;IACtF,IAAI,CAACW,UAAU,CAAC,CAAC;IACjB,IAAI,CAACV,wBAAwB,CAACN,IAAI,CAAC,IAAI,CAACC,YAAY,CAAC;EACzD;EACAgB,sBAAsBA,CAACrB,KAAK,EAAEsB,YAAY,EAAE;IACxC,IAAIf,iBAAiB,GAAG,CAAC,CAAC;IAC1B,KAAK,IAAIgB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACjB,kBAAkB,CAACG,MAAM,EAAEc,CAAC,EAAE,EAAE;MACrD,IAAID,YAAY,CAACE,WAAW,CAAC,CAAC,KAAK,IAAI,CAAClB,kBAAkB,CAACiB,CAAC,CAAC,CAACE,KAAK,EAAE;QACjElB,iBAAiB,GAAGgB,CAAC;QACrB;MACJ;IACJ;IACA,IAAIhB,iBAAiB,KAAK,CAAC,CAAC,EAAE;MAC1B;MACA,MAAMmB,kBAAkB,GAAGJ,YAAY,CAACK,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACH,WAAW,CAAC,CAAC;MACnE,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACjB,kBAAkB,CAACG,MAAM,EAAEc,CAAC,EAAE,EAAE;QACrD,IAAI,IAAI,CAACjB,kBAAkB,CAACiB,CAAC,CAAC,CAACE,KAAK,CAACD,WAAW,CAAC,CAAC,CAACI,UAAU,CAACF,kBAAkB,CAAC,EAAE;UAC/EnB,iBAAiB,GAAGgB,CAAC;UACrB;QACJ;MACJ;IACJ;IACA,IAAIhB,iBAAiB,KAAK,CAAC,CAAC,IAAIA,iBAAiB,KAAK,IAAI,CAACA,iBAAiB,EAAE;MAC1E,IAAI,CAACA,iBAAiB,GAAGA,iBAAiB;MAC1C,IAAI,CAACG,wBAAwB,CAACN,IAAI,CAAC,IAAI,CAACC,YAAY,CAAC;IACzD;EACJ;EACAe,UAAUA,CAAA,EAAG;IACT,IAAI,CAACP,eAAe,CAACT,IAAI,CAAC,IAAI,CAACH,MAAM,CAAC;EAC1C;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}