{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { splitLines } from '../../../../../base/common/strings.js';\nimport { Range } from '../../../core/range.js';\nimport { TextLength } from '../../../core/textLength.js';\n/**\n * The end must be greater than or equal to the start.\n*/\nexport function lengthDiff(startLineCount, startColumnCount, endLineCount, endColumnCount) {\n  return startLineCount !== endLineCount ? toLength(endLineCount - startLineCount, endColumnCount) : toLength(0, endColumnCount - startColumnCount);\n}\nexport const lengthZero = 0;\nexport function lengthIsZero(length) {\n  return length === 0;\n}\n/*\n * We have 52 bits available in a JS number.\n * We use the upper 26 bits to store the line and the lower 26 bits to store the column.\n */\n///*\nconst factor = 2 ** 26;\n/*/\nconst factor = 1000000;\n// */\nexport function toLength(lineCount, columnCount) {\n  // llllllllllllllllllllllllllcccccccccccccccccccccccccc (52 bits)\n  //       line count (26 bits)    column count (26 bits)\n  // If there is no overflow (all values/sums below 2^26 = 67108864),\n  // we have `toLength(lns1, cols1) + toLength(lns2, cols2) = toLength(lns1 + lns2, cols1 + cols2)`.\n  return lineCount * factor + columnCount;\n}\nexport function lengthToObj(length) {\n  const l = length;\n  const lineCount = Math.floor(l / factor);\n  const columnCount = l - lineCount * factor;\n  return new TextLength(lineCount, columnCount);\n}\nexport function lengthGetLineCount(length) {\n  return Math.floor(length / factor);\n}\n/**\n * Returns the amount of columns of the given length, assuming that it does not span any line.\n*/\nexport function lengthGetColumnCountIfZeroLineCount(length) {\n  return length;\n}\nexport function lengthAdd(l1, l2) {\n  let r = l1 + l2;\n  if (l2 >= factor) {\n    r = r - l1 % factor;\n  }\n  return r;\n}\nexport function sumLengths(items, lengthFn) {\n  return items.reduce((a, b) => lengthAdd(a, lengthFn(b)), lengthZero);\n}\nexport function lengthEquals(length1, length2) {\n  return length1 === length2;\n}\n/**\n * Returns a non negative length `result` such that `lengthAdd(length1, result) = length2`, or zero if such length does not exist.\n */\nexport function lengthDiffNonNegative(length1, length2) {\n  const l1 = length1;\n  const l2 = length2;\n  const diff = l2 - l1;\n  if (diff <= 0) {\n    // line-count of length1 is higher than line-count of length2\n    // or they are equal and column-count of length1 is higher than column-count of length2\n    return lengthZero;\n  }\n  const lineCount1 = Math.floor(l1 / factor);\n  const lineCount2 = Math.floor(l2 / factor);\n  const colCount2 = l2 - lineCount2 * factor;\n  if (lineCount1 === lineCount2) {\n    const colCount1 = l1 - lineCount1 * factor;\n    return toLength(0, colCount2 - colCount1);\n  } else {\n    return toLength(lineCount2 - lineCount1, colCount2);\n  }\n}\nexport function lengthLessThan(length1, length2) {\n  // First, compare line counts, then column counts.\n  return length1 < length2;\n}\nexport function lengthLessThanEqual(length1, length2) {\n  return length1 <= length2;\n}\nexport function lengthGreaterThanEqual(length1, length2) {\n  return length1 >= length2;\n}\nexport function positionToLength(position) {\n  return toLength(position.lineNumber - 1, position.column - 1);\n}\nexport function lengthsToRange(lengthStart, lengthEnd) {\n  const l = lengthStart;\n  const lineCount = Math.floor(l / factor);\n  const colCount = l - lineCount * factor;\n  const l2 = lengthEnd;\n  const lineCount2 = Math.floor(l2 / factor);\n  const colCount2 = l2 - lineCount2 * factor;\n  return new Range(lineCount + 1, colCount + 1, lineCount2 + 1, colCount2 + 1);\n}\nexport function lengthOfString(str) {\n  const lines = splitLines(str);\n  return toLength(lines.length - 1, lines[lines.length - 1].length);\n}", "map": {"version": 3, "names": ["splitLines", "Range", "TextLength", "lengthDiff", "startLineCount", "startColumnCount", "endLineCount", "endColumnCount", "to<PERSON><PERSON><PERSON>", "lengthZero", "lengthIsZero", "length", "factor", "lineCount", "columnCount", "lengthToObj", "l", "Math", "floor", "lengthGetLineCount", "lengthGetColumnCountIfZeroLineCount", "lengthAdd", "l1", "l2", "r", "sumLengths", "items", "lengthFn", "reduce", "a", "b", "lengthEquals", "length1", "length2", "lengthDiffNonNegative", "diff", "lineCount1", "lineCount2", "colCount2", "colCount1", "lengthLessThan", "lengthLessThanEqual", "lengthGreaterThanEqual", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "position", "lineNumber", "column", "lengthsToRange", "lengthStart", "lengthEnd", "col<PERSON>ount", "lengthOfString", "str", "lines"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/length.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { splitLines } from '../../../../../base/common/strings.js';\nimport { Range } from '../../../core/range.js';\nimport { TextLength } from '../../../core/textLength.js';\n/**\n * The end must be greater than or equal to the start.\n*/\nexport function lengthDiff(startLineCount, startColumnCount, endLineCount, endColumnCount) {\n    return (startLineCount !== endLineCount)\n        ? toLength(endLineCount - startLineCount, endColumnCount)\n        : toLength(0, endColumnCount - startColumnCount);\n}\nexport const lengthZero = 0;\nexport function lengthIsZero(length) {\n    return length === 0;\n}\n/*\n * We have 52 bits available in a JS number.\n * We use the upper 26 bits to store the line and the lower 26 bits to store the column.\n */\n///*\nconst factor = 2 ** 26;\n/*/\nconst factor = 1000000;\n// */\nexport function toLength(lineCount, columnCount) {\n    // llllllllllllllllllllllllllcccccccccccccccccccccccccc (52 bits)\n    //       line count (26 bits)    column count (26 bits)\n    // If there is no overflow (all values/sums below 2^26 = 67108864),\n    // we have `toLength(lns1, cols1) + toLength(lns2, cols2) = toLength(lns1 + lns2, cols1 + cols2)`.\n    return (lineCount * factor + columnCount);\n}\nexport function lengthToObj(length) {\n    const l = length;\n    const lineCount = Math.floor(l / factor);\n    const columnCount = l - lineCount * factor;\n    return new TextLength(lineCount, columnCount);\n}\nexport function lengthGetLineCount(length) {\n    return Math.floor(length / factor);\n}\n/**\n * Returns the amount of columns of the given length, assuming that it does not span any line.\n*/\nexport function lengthGetColumnCountIfZeroLineCount(length) {\n    return length;\n}\nexport function lengthAdd(l1, l2) {\n    let r = l1 + l2;\n    if (l2 >= factor) {\n        r = r - (l1 % factor);\n    }\n    return r;\n}\nexport function sumLengths(items, lengthFn) {\n    return items.reduce((a, b) => lengthAdd(a, lengthFn(b)), lengthZero);\n}\nexport function lengthEquals(length1, length2) {\n    return length1 === length2;\n}\n/**\n * Returns a non negative length `result` such that `lengthAdd(length1, result) = length2`, or zero if such length does not exist.\n */\nexport function lengthDiffNonNegative(length1, length2) {\n    const l1 = length1;\n    const l2 = length2;\n    const diff = l2 - l1;\n    if (diff <= 0) {\n        // line-count of length1 is higher than line-count of length2\n        // or they are equal and column-count of length1 is higher than column-count of length2\n        return lengthZero;\n    }\n    const lineCount1 = Math.floor(l1 / factor);\n    const lineCount2 = Math.floor(l2 / factor);\n    const colCount2 = l2 - lineCount2 * factor;\n    if (lineCount1 === lineCount2) {\n        const colCount1 = l1 - lineCount1 * factor;\n        return toLength(0, colCount2 - colCount1);\n    }\n    else {\n        return toLength(lineCount2 - lineCount1, colCount2);\n    }\n}\nexport function lengthLessThan(length1, length2) {\n    // First, compare line counts, then column counts.\n    return length1 < length2;\n}\nexport function lengthLessThanEqual(length1, length2) {\n    return length1 <= length2;\n}\nexport function lengthGreaterThanEqual(length1, length2) {\n    return length1 >= length2;\n}\nexport function positionToLength(position) {\n    return toLength(position.lineNumber - 1, position.column - 1);\n}\nexport function lengthsToRange(lengthStart, lengthEnd) {\n    const l = lengthStart;\n    const lineCount = Math.floor(l / factor);\n    const colCount = l - lineCount * factor;\n    const l2 = lengthEnd;\n    const lineCount2 = Math.floor(l2 / factor);\n    const colCount2 = l2 - lineCount2 * factor;\n    return new Range(lineCount + 1, colCount + 1, lineCount2 + 1, colCount2 + 1);\n}\nexport function lengthOfString(str) {\n    const lines = splitLines(str);\n    return toLength(lines.length - 1, lines[lines.length - 1].length);\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,UAAU,QAAQ,uCAAuC;AAClE,SAASC,KAAK,QAAQ,wBAAwB;AAC9C,SAASC,UAAU,QAAQ,6BAA6B;AACxD;AACA;AACA;AACA,OAAO,SAASC,UAAUA,CAACC,cAAc,EAAEC,gBAAgB,EAAEC,YAAY,EAAEC,cAAc,EAAE;EACvF,OAAQH,cAAc,KAAKE,YAAY,GACjCE,QAAQ,CAACF,YAAY,GAAGF,cAAc,EAAEG,cAAc,CAAC,GACvDC,QAAQ,CAAC,CAAC,EAAED,cAAc,GAAGF,gBAAgB,CAAC;AACxD;AACA,OAAO,MAAMI,UAAU,GAAG,CAAC;AAC3B,OAAO,SAASC,YAAYA,CAACC,MAAM,EAAE;EACjC,OAAOA,MAAM,KAAK,CAAC;AACvB;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,MAAM,GAAG,CAAC,IAAI,EAAE;AACtB;AACA;AACA;AACA,OAAO,SAASJ,QAAQA,CAACK,SAAS,EAAEC,WAAW,EAAE;EAC7C;EACA;EACA;EACA;EACA,OAAQD,SAAS,GAAGD,MAAM,GAAGE,WAAW;AAC5C;AACA,OAAO,SAASC,WAAWA,CAACJ,MAAM,EAAE;EAChC,MAAMK,CAAC,GAAGL,MAAM;EAChB,MAAME,SAAS,GAAGI,IAAI,CAACC,KAAK,CAACF,CAAC,GAAGJ,MAAM,CAAC;EACxC,MAAME,WAAW,GAAGE,CAAC,GAAGH,SAAS,GAAGD,MAAM;EAC1C,OAAO,IAAIV,UAAU,CAACW,SAAS,EAAEC,WAAW,CAAC;AACjD;AACA,OAAO,SAASK,kBAAkBA,CAACR,MAAM,EAAE;EACvC,OAAOM,IAAI,CAACC,KAAK,CAACP,MAAM,GAAGC,MAAM,CAAC;AACtC;AACA;AACA;AACA;AACA,OAAO,SAASQ,mCAAmCA,CAACT,MAAM,EAAE;EACxD,OAAOA,MAAM;AACjB;AACA,OAAO,SAASU,SAASA,CAACC,EAAE,EAAEC,EAAE,EAAE;EAC9B,IAAIC,CAAC,GAAGF,EAAE,GAAGC,EAAE;EACf,IAAIA,EAAE,IAAIX,MAAM,EAAE;IACdY,CAAC,GAAGA,CAAC,GAAIF,EAAE,GAAGV,MAAO;EACzB;EACA,OAAOY,CAAC;AACZ;AACA,OAAO,SAASC,UAAUA,CAACC,KAAK,EAAEC,QAAQ,EAAE;EACxC,OAAOD,KAAK,CAACE,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKT,SAAS,CAACQ,CAAC,EAAEF,QAAQ,CAACG,CAAC,CAAC,CAAC,EAAErB,UAAU,CAAC;AACxE;AACA,OAAO,SAASsB,YAAYA,CAACC,OAAO,EAAEC,OAAO,EAAE;EAC3C,OAAOD,OAAO,KAAKC,OAAO;AAC9B;AACA;AACA;AACA;AACA,OAAO,SAASC,qBAAqBA,CAACF,OAAO,EAAEC,OAAO,EAAE;EACpD,MAAMX,EAAE,GAAGU,OAAO;EAClB,MAAMT,EAAE,GAAGU,OAAO;EAClB,MAAME,IAAI,GAAGZ,EAAE,GAAGD,EAAE;EACpB,IAAIa,IAAI,IAAI,CAAC,EAAE;IACX;IACA;IACA,OAAO1B,UAAU;EACrB;EACA,MAAM2B,UAAU,GAAGnB,IAAI,CAACC,KAAK,CAACI,EAAE,GAAGV,MAAM,CAAC;EAC1C,MAAMyB,UAAU,GAAGpB,IAAI,CAACC,KAAK,CAACK,EAAE,GAAGX,MAAM,CAAC;EAC1C,MAAM0B,SAAS,GAAGf,EAAE,GAAGc,UAAU,GAAGzB,MAAM;EAC1C,IAAIwB,UAAU,KAAKC,UAAU,EAAE;IAC3B,MAAME,SAAS,GAAGjB,EAAE,GAAGc,UAAU,GAAGxB,MAAM;IAC1C,OAAOJ,QAAQ,CAAC,CAAC,EAAE8B,SAAS,GAAGC,SAAS,CAAC;EAC7C,CAAC,MACI;IACD,OAAO/B,QAAQ,CAAC6B,UAAU,GAAGD,UAAU,EAAEE,SAAS,CAAC;EACvD;AACJ;AACA,OAAO,SAASE,cAAcA,CAACR,OAAO,EAAEC,OAAO,EAAE;EAC7C;EACA,OAAOD,OAAO,GAAGC,OAAO;AAC5B;AACA,OAAO,SAASQ,mBAAmBA,CAACT,OAAO,EAAEC,OAAO,EAAE;EAClD,OAAOD,OAAO,IAAIC,OAAO;AAC7B;AACA,OAAO,SAASS,sBAAsBA,CAACV,OAAO,EAAEC,OAAO,EAAE;EACrD,OAAOD,OAAO,IAAIC,OAAO;AAC7B;AACA,OAAO,SAASU,gBAAgBA,CAACC,QAAQ,EAAE;EACvC,OAAOpC,QAAQ,CAACoC,QAAQ,CAACC,UAAU,GAAG,CAAC,EAAED,QAAQ,CAACE,MAAM,GAAG,CAAC,CAAC;AACjE;AACA,OAAO,SAASC,cAAcA,CAACC,WAAW,EAAEC,SAAS,EAAE;EACnD,MAAMjC,CAAC,GAAGgC,WAAW;EACrB,MAAMnC,SAAS,GAAGI,IAAI,CAACC,KAAK,CAACF,CAAC,GAAGJ,MAAM,CAAC;EACxC,MAAMsC,QAAQ,GAAGlC,CAAC,GAAGH,SAAS,GAAGD,MAAM;EACvC,MAAMW,EAAE,GAAG0B,SAAS;EACpB,MAAMZ,UAAU,GAAGpB,IAAI,CAACC,KAAK,CAACK,EAAE,GAAGX,MAAM,CAAC;EAC1C,MAAM0B,SAAS,GAAGf,EAAE,GAAGc,UAAU,GAAGzB,MAAM;EAC1C,OAAO,IAAIX,KAAK,CAACY,SAAS,GAAG,CAAC,EAAEqC,QAAQ,GAAG,CAAC,EAAEb,UAAU,GAAG,CAAC,EAAEC,SAAS,GAAG,CAAC,CAAC;AAChF;AACA,OAAO,SAASa,cAAcA,CAACC,GAAG,EAAE;EAChC,MAAMC,KAAK,GAAGrD,UAAU,CAACoD,GAAG,CAAC;EAC7B,OAAO5C,QAAQ,CAAC6C,KAAK,CAAC1C,MAAM,GAAG,CAAC,EAAE0C,KAAK,CAACA,KAAK,CAAC1C,MAAM,GAAG,CAAC,CAAC,CAACA,MAAM,CAAC;AACrE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}