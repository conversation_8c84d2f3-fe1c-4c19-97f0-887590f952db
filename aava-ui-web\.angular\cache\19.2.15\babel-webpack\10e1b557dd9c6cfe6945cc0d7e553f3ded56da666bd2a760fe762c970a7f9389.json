{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Emitter } from './event.js';\nimport { Disposable } from './lifecycle.js';\nexport class ScrollState {\n  constructor(_forceIntegerValues, width, scrollWidth, scrollLeft, height, scrollHeight, scrollTop) {\n    this._forceIntegerValues = _forceIntegerValues;\n    this._scrollStateBrand = undefined;\n    if (this._forceIntegerValues) {\n      width = width | 0;\n      scrollWidth = scrollWidth | 0;\n      scrollLeft = scrollLeft | 0;\n      height = height | 0;\n      scrollHeight = scrollHeight | 0;\n      scrollTop = scrollTop | 0;\n    }\n    this.rawScrollLeft = scrollLeft; // before validation\n    this.rawScrollTop = scrollTop; // before validation\n    if (width < 0) {\n      width = 0;\n    }\n    if (scrollLeft + width > scrollWidth) {\n      scrollLeft = scrollWidth - width;\n    }\n    if (scrollLeft < 0) {\n      scrollLeft = 0;\n    }\n    if (height < 0) {\n      height = 0;\n    }\n    if (scrollTop + height > scrollHeight) {\n      scrollTop = scrollHeight - height;\n    }\n    if (scrollTop < 0) {\n      scrollTop = 0;\n    }\n    this.width = width;\n    this.scrollWidth = scrollWidth;\n    this.scrollLeft = scrollLeft;\n    this.height = height;\n    this.scrollHeight = scrollHeight;\n    this.scrollTop = scrollTop;\n  }\n  equals(other) {\n    return this.rawScrollLeft === other.rawScrollLeft && this.rawScrollTop === other.rawScrollTop && this.width === other.width && this.scrollWidth === other.scrollWidth && this.scrollLeft === other.scrollLeft && this.height === other.height && this.scrollHeight === other.scrollHeight && this.scrollTop === other.scrollTop;\n  }\n  withScrollDimensions(update, useRawScrollPositions) {\n    return new ScrollState(this._forceIntegerValues, typeof update.width !== 'undefined' ? update.width : this.width, typeof update.scrollWidth !== 'undefined' ? update.scrollWidth : this.scrollWidth, useRawScrollPositions ? this.rawScrollLeft : this.scrollLeft, typeof update.height !== 'undefined' ? update.height : this.height, typeof update.scrollHeight !== 'undefined' ? update.scrollHeight : this.scrollHeight, useRawScrollPositions ? this.rawScrollTop : this.scrollTop);\n  }\n  withScrollPosition(update) {\n    return new ScrollState(this._forceIntegerValues, this.width, this.scrollWidth, typeof update.scrollLeft !== 'undefined' ? update.scrollLeft : this.rawScrollLeft, this.height, this.scrollHeight, typeof update.scrollTop !== 'undefined' ? update.scrollTop : this.rawScrollTop);\n  }\n  createScrollEvent(previous, inSmoothScrolling) {\n    const widthChanged = this.width !== previous.width;\n    const scrollWidthChanged = this.scrollWidth !== previous.scrollWidth;\n    const scrollLeftChanged = this.scrollLeft !== previous.scrollLeft;\n    const heightChanged = this.height !== previous.height;\n    const scrollHeightChanged = this.scrollHeight !== previous.scrollHeight;\n    const scrollTopChanged = this.scrollTop !== previous.scrollTop;\n    return {\n      inSmoothScrolling: inSmoothScrolling,\n      oldWidth: previous.width,\n      oldScrollWidth: previous.scrollWidth,\n      oldScrollLeft: previous.scrollLeft,\n      width: this.width,\n      scrollWidth: this.scrollWidth,\n      scrollLeft: this.scrollLeft,\n      oldHeight: previous.height,\n      oldScrollHeight: previous.scrollHeight,\n      oldScrollTop: previous.scrollTop,\n      height: this.height,\n      scrollHeight: this.scrollHeight,\n      scrollTop: this.scrollTop,\n      widthChanged: widthChanged,\n      scrollWidthChanged: scrollWidthChanged,\n      scrollLeftChanged: scrollLeftChanged,\n      heightChanged: heightChanged,\n      scrollHeightChanged: scrollHeightChanged,\n      scrollTopChanged: scrollTopChanged\n    };\n  }\n}\nexport class Scrollable extends Disposable {\n  constructor(options) {\n    super();\n    this._scrollableBrand = undefined;\n    this._onScroll = this._register(new Emitter());\n    this.onScroll = this._onScroll.event;\n    this._smoothScrollDuration = options.smoothScrollDuration;\n    this._scheduleAtNextAnimationFrame = options.scheduleAtNextAnimationFrame;\n    this._state = new ScrollState(options.forceIntegerValues, 0, 0, 0, 0, 0, 0);\n    this._smoothScrolling = null;\n  }\n  dispose() {\n    if (this._smoothScrolling) {\n      this._smoothScrolling.dispose();\n      this._smoothScrolling = null;\n    }\n    super.dispose();\n  }\n  setSmoothScrollDuration(smoothScrollDuration) {\n    this._smoothScrollDuration = smoothScrollDuration;\n  }\n  validateScrollPosition(scrollPosition) {\n    return this._state.withScrollPosition(scrollPosition);\n  }\n  getScrollDimensions() {\n    return this._state;\n  }\n  setScrollDimensions(dimensions, useRawScrollPositions) {\n    const newState = this._state.withScrollDimensions(dimensions, useRawScrollPositions);\n    this._setState(newState, Boolean(this._smoothScrolling));\n    // Validate outstanding animated scroll position target\n    this._smoothScrolling?.acceptScrollDimensions(this._state);\n  }\n  /**\n   * Returns the final scroll position that the instance will have once the smooth scroll animation concludes.\n   * If no scroll animation is occurring, it will return the current scroll position instead.\n   */\n  getFutureScrollPosition() {\n    if (this._smoothScrolling) {\n      return this._smoothScrolling.to;\n    }\n    return this._state;\n  }\n  /**\n   * Returns the current scroll position.\n   * Note: This result might be an intermediate scroll position, as there might be an ongoing smooth scroll animation.\n   */\n  getCurrentScrollPosition() {\n    return this._state;\n  }\n  setScrollPositionNow(update) {\n    // no smooth scrolling requested\n    const newState = this._state.withScrollPosition(update);\n    // Terminate any outstanding smooth scrolling\n    if (this._smoothScrolling) {\n      this._smoothScrolling.dispose();\n      this._smoothScrolling = null;\n    }\n    this._setState(newState, false);\n  }\n  setScrollPositionSmooth(update, reuseAnimation) {\n    if (this._smoothScrollDuration === 0) {\n      // Smooth scrolling not supported.\n      return this.setScrollPositionNow(update);\n    }\n    if (this._smoothScrolling) {\n      // Combine our pending scrollLeft/scrollTop with incoming scrollLeft/scrollTop\n      update = {\n        scrollLeft: typeof update.scrollLeft === 'undefined' ? this._smoothScrolling.to.scrollLeft : update.scrollLeft,\n        scrollTop: typeof update.scrollTop === 'undefined' ? this._smoothScrolling.to.scrollTop : update.scrollTop\n      };\n      // Validate `update`\n      const validTarget = this._state.withScrollPosition(update);\n      if (this._smoothScrolling.to.scrollLeft === validTarget.scrollLeft && this._smoothScrolling.to.scrollTop === validTarget.scrollTop) {\n        // No need to interrupt or extend the current animation since we're going to the same place\n        return;\n      }\n      let newSmoothScrolling;\n      if (reuseAnimation) {\n        newSmoothScrolling = new SmoothScrollingOperation(this._smoothScrolling.from, validTarget, this._smoothScrolling.startTime, this._smoothScrolling.duration);\n      } else {\n        newSmoothScrolling = this._smoothScrolling.combine(this._state, validTarget, this._smoothScrollDuration);\n      }\n      this._smoothScrolling.dispose();\n      this._smoothScrolling = newSmoothScrolling;\n    } else {\n      // Validate `update`\n      const validTarget = this._state.withScrollPosition(update);\n      this._smoothScrolling = SmoothScrollingOperation.start(this._state, validTarget, this._smoothScrollDuration);\n    }\n    // Begin smooth scrolling animation\n    this._smoothScrolling.animationFrameDisposable = this._scheduleAtNextAnimationFrame(() => {\n      if (!this._smoothScrolling) {\n        return;\n      }\n      this._smoothScrolling.animationFrameDisposable = null;\n      this._performSmoothScrolling();\n    });\n  }\n  hasPendingScrollAnimation() {\n    return Boolean(this._smoothScrolling);\n  }\n  _performSmoothScrolling() {\n    if (!this._smoothScrolling) {\n      return;\n    }\n    const update = this._smoothScrolling.tick();\n    const newState = this._state.withScrollPosition(update);\n    this._setState(newState, true);\n    if (!this._smoothScrolling) {\n      // Looks like someone canceled the smooth scrolling\n      // from the scroll event handler\n      return;\n    }\n    if (update.isDone) {\n      this._smoothScrolling.dispose();\n      this._smoothScrolling = null;\n      return;\n    }\n    // Continue smooth scrolling animation\n    this._smoothScrolling.animationFrameDisposable = this._scheduleAtNextAnimationFrame(() => {\n      if (!this._smoothScrolling) {\n        return;\n      }\n      this._smoothScrolling.animationFrameDisposable = null;\n      this._performSmoothScrolling();\n    });\n  }\n  _setState(newState, inSmoothScrolling) {\n    const oldState = this._state;\n    if (oldState.equals(newState)) {\n      // no change\n      return;\n    }\n    this._state = newState;\n    this._onScroll.fire(this._state.createScrollEvent(oldState, inSmoothScrolling));\n  }\n}\nexport class SmoothScrollingUpdate {\n  constructor(scrollLeft, scrollTop, isDone) {\n    this.scrollLeft = scrollLeft;\n    this.scrollTop = scrollTop;\n    this.isDone = isDone;\n  }\n}\nfunction createEaseOutCubic(from, to) {\n  const delta = to - from;\n  return function (completion) {\n    return from + delta * easeOutCubic(completion);\n  };\n}\nfunction createComposed(a, b, cut) {\n  return function (completion) {\n    if (completion < cut) {\n      return a(completion / cut);\n    }\n    return b((completion - cut) / (1 - cut));\n  };\n}\nexport class SmoothScrollingOperation {\n  constructor(from, to, startTime, duration) {\n    this.from = from;\n    this.to = to;\n    this.duration = duration;\n    this.startTime = startTime;\n    this.animationFrameDisposable = null;\n    this._initAnimations();\n  }\n  _initAnimations() {\n    this.scrollLeft = this._initAnimation(this.from.scrollLeft, this.to.scrollLeft, this.to.width);\n    this.scrollTop = this._initAnimation(this.from.scrollTop, this.to.scrollTop, this.to.height);\n  }\n  _initAnimation(from, to, viewportSize) {\n    const delta = Math.abs(from - to);\n    if (delta > 2.5 * viewportSize) {\n      let stop1, stop2;\n      if (from < to) {\n        // scroll to 75% of the viewportSize\n        stop1 = from + 0.75 * viewportSize;\n        stop2 = to - 0.75 * viewportSize;\n      } else {\n        stop1 = from - 0.75 * viewportSize;\n        stop2 = to + 0.75 * viewportSize;\n      }\n      return createComposed(createEaseOutCubic(from, stop1), createEaseOutCubic(stop2, to), 0.33);\n    }\n    return createEaseOutCubic(from, to);\n  }\n  dispose() {\n    if (this.animationFrameDisposable !== null) {\n      this.animationFrameDisposable.dispose();\n      this.animationFrameDisposable = null;\n    }\n  }\n  acceptScrollDimensions(state) {\n    this.to = state.withScrollPosition(this.to);\n    this._initAnimations();\n  }\n  tick() {\n    return this._tick(Date.now());\n  }\n  _tick(now) {\n    const completion = (now - this.startTime) / this.duration;\n    if (completion < 1) {\n      const newScrollLeft = this.scrollLeft(completion);\n      const newScrollTop = this.scrollTop(completion);\n      return new SmoothScrollingUpdate(newScrollLeft, newScrollTop, false);\n    }\n    return new SmoothScrollingUpdate(this.to.scrollLeft, this.to.scrollTop, true);\n  }\n  combine(from, to, duration) {\n    return SmoothScrollingOperation.start(from, to, duration);\n  }\n  static start(from, to, duration) {\n    // +10 / -10 : pretend the animation already started for a quicker response to a scroll request\n    duration = duration + 10;\n    const startTime = Date.now() - 10;\n    return new SmoothScrollingOperation(from, to, startTime, duration);\n  }\n}\nfunction easeInCubic(t) {\n  return Math.pow(t, 3);\n}\nfunction easeOutCubic(t) {\n  return 1 - easeInCubic(1 - t);\n}", "map": {"version": 3, "names": ["Emitter", "Disposable", "ScrollState", "constructor", "_forceIntegerValues", "width", "scrollWidth", "scrollLeft", "height", "scrollHeight", "scrollTop", "_scrollStateBrand", "undefined", "rawScrollLeft", "rawScrollTop", "equals", "other", "withScrollDimensions", "update", "useRawScrollPositions", "withScrollPosition", "createScrollEvent", "previous", "inSmoothScrolling", "widthChanged", "scrollWidthChanged", "scrollLeftChanged", "heightChanged", "scrollHeightChanged", "scrollTopChanged", "oldWidth", "oldScrollWidth", "oldScrollLeft", "oldHeight", "oldScrollHeight", "oldScrollTop", "Scrollable", "options", "_scrollable<PERSON><PERSON>", "_onScroll", "_register", "onScroll", "event", "_smoothScrollDuration", "smoothScrollDuration", "_scheduleAtNextAnimationFrame", "scheduleAtNextAnimationFrame", "_state", "forceIntegerValues", "_smoothScrolling", "dispose", "setSmoothScrollDuration", "validateScrollPosition", "scrollPosition", "getScrollDimensions", "setScrollDimensions", "dimensions", "newState", "_setState", "Boolean", "acceptScrollDimensions", "getFutureScrollPosition", "to", "getCurrentScrollPosition", "setScrollPositionNow", "setScrollPositionSmooth", "reuseAnimation", "valid<PERSON>arget", "newSmoothScrolling", "SmoothScrollingOperation", "from", "startTime", "duration", "combine", "start", "animationFrameDisposable", "_performSmoothScrolling", "hasPendingScrollAnimation", "tick", "isDone", "oldState", "fire", "SmoothScrollingUpdate", "createEaseOutCubic", "delta", "completion", "easeOutCubic", "createComposed", "a", "b", "cut", "_initAnimations", "_initAnimation", "viewportSize", "Math", "abs", "stop1", "stop2", "state", "_tick", "Date", "now", "newScrollLeft", "newScrollTop", "easeInCubic", "t", "pow"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/common/scrollable.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Emitter } from './event.js';\nimport { Disposable } from './lifecycle.js';\nexport class ScrollState {\n    constructor(_forceIntegerValues, width, scrollWidth, scrollLeft, height, scrollHeight, scrollTop) {\n        this._forceIntegerValues = _forceIntegerValues;\n        this._scrollStateBrand = undefined;\n        if (this._forceIntegerValues) {\n            width = width | 0;\n            scrollWidth = scrollWidth | 0;\n            scrollLeft = scrollLeft | 0;\n            height = height | 0;\n            scrollHeight = scrollHeight | 0;\n            scrollTop = scrollTop | 0;\n        }\n        this.rawScrollLeft = scrollLeft; // before validation\n        this.rawScrollTop = scrollTop; // before validation\n        if (width < 0) {\n            width = 0;\n        }\n        if (scrollLeft + width > scrollWidth) {\n            scrollLeft = scrollWidth - width;\n        }\n        if (scrollLeft < 0) {\n            scrollLeft = 0;\n        }\n        if (height < 0) {\n            height = 0;\n        }\n        if (scrollTop + height > scrollHeight) {\n            scrollTop = scrollHeight - height;\n        }\n        if (scrollTop < 0) {\n            scrollTop = 0;\n        }\n        this.width = width;\n        this.scrollWidth = scrollWidth;\n        this.scrollLeft = scrollLeft;\n        this.height = height;\n        this.scrollHeight = scrollHeight;\n        this.scrollTop = scrollTop;\n    }\n    equals(other) {\n        return (this.rawScrollLeft === other.rawScrollLeft\n            && this.rawScrollTop === other.rawScrollTop\n            && this.width === other.width\n            && this.scrollWidth === other.scrollWidth\n            && this.scrollLeft === other.scrollLeft\n            && this.height === other.height\n            && this.scrollHeight === other.scrollHeight\n            && this.scrollTop === other.scrollTop);\n    }\n    withScrollDimensions(update, useRawScrollPositions) {\n        return new ScrollState(this._forceIntegerValues, (typeof update.width !== 'undefined' ? update.width : this.width), (typeof update.scrollWidth !== 'undefined' ? update.scrollWidth : this.scrollWidth), useRawScrollPositions ? this.rawScrollLeft : this.scrollLeft, (typeof update.height !== 'undefined' ? update.height : this.height), (typeof update.scrollHeight !== 'undefined' ? update.scrollHeight : this.scrollHeight), useRawScrollPositions ? this.rawScrollTop : this.scrollTop);\n    }\n    withScrollPosition(update) {\n        return new ScrollState(this._forceIntegerValues, this.width, this.scrollWidth, (typeof update.scrollLeft !== 'undefined' ? update.scrollLeft : this.rawScrollLeft), this.height, this.scrollHeight, (typeof update.scrollTop !== 'undefined' ? update.scrollTop : this.rawScrollTop));\n    }\n    createScrollEvent(previous, inSmoothScrolling) {\n        const widthChanged = (this.width !== previous.width);\n        const scrollWidthChanged = (this.scrollWidth !== previous.scrollWidth);\n        const scrollLeftChanged = (this.scrollLeft !== previous.scrollLeft);\n        const heightChanged = (this.height !== previous.height);\n        const scrollHeightChanged = (this.scrollHeight !== previous.scrollHeight);\n        const scrollTopChanged = (this.scrollTop !== previous.scrollTop);\n        return {\n            inSmoothScrolling: inSmoothScrolling,\n            oldWidth: previous.width,\n            oldScrollWidth: previous.scrollWidth,\n            oldScrollLeft: previous.scrollLeft,\n            width: this.width,\n            scrollWidth: this.scrollWidth,\n            scrollLeft: this.scrollLeft,\n            oldHeight: previous.height,\n            oldScrollHeight: previous.scrollHeight,\n            oldScrollTop: previous.scrollTop,\n            height: this.height,\n            scrollHeight: this.scrollHeight,\n            scrollTop: this.scrollTop,\n            widthChanged: widthChanged,\n            scrollWidthChanged: scrollWidthChanged,\n            scrollLeftChanged: scrollLeftChanged,\n            heightChanged: heightChanged,\n            scrollHeightChanged: scrollHeightChanged,\n            scrollTopChanged: scrollTopChanged,\n        };\n    }\n}\nexport class Scrollable extends Disposable {\n    constructor(options) {\n        super();\n        this._scrollableBrand = undefined;\n        this._onScroll = this._register(new Emitter());\n        this.onScroll = this._onScroll.event;\n        this._smoothScrollDuration = options.smoothScrollDuration;\n        this._scheduleAtNextAnimationFrame = options.scheduleAtNextAnimationFrame;\n        this._state = new ScrollState(options.forceIntegerValues, 0, 0, 0, 0, 0, 0);\n        this._smoothScrolling = null;\n    }\n    dispose() {\n        if (this._smoothScrolling) {\n            this._smoothScrolling.dispose();\n            this._smoothScrolling = null;\n        }\n        super.dispose();\n    }\n    setSmoothScrollDuration(smoothScrollDuration) {\n        this._smoothScrollDuration = smoothScrollDuration;\n    }\n    validateScrollPosition(scrollPosition) {\n        return this._state.withScrollPosition(scrollPosition);\n    }\n    getScrollDimensions() {\n        return this._state;\n    }\n    setScrollDimensions(dimensions, useRawScrollPositions) {\n        const newState = this._state.withScrollDimensions(dimensions, useRawScrollPositions);\n        this._setState(newState, Boolean(this._smoothScrolling));\n        // Validate outstanding animated scroll position target\n        this._smoothScrolling?.acceptScrollDimensions(this._state);\n    }\n    /**\n     * Returns the final scroll position that the instance will have once the smooth scroll animation concludes.\n     * If no scroll animation is occurring, it will return the current scroll position instead.\n     */\n    getFutureScrollPosition() {\n        if (this._smoothScrolling) {\n            return this._smoothScrolling.to;\n        }\n        return this._state;\n    }\n    /**\n     * Returns the current scroll position.\n     * Note: This result might be an intermediate scroll position, as there might be an ongoing smooth scroll animation.\n     */\n    getCurrentScrollPosition() {\n        return this._state;\n    }\n    setScrollPositionNow(update) {\n        // no smooth scrolling requested\n        const newState = this._state.withScrollPosition(update);\n        // Terminate any outstanding smooth scrolling\n        if (this._smoothScrolling) {\n            this._smoothScrolling.dispose();\n            this._smoothScrolling = null;\n        }\n        this._setState(newState, false);\n    }\n    setScrollPositionSmooth(update, reuseAnimation) {\n        if (this._smoothScrollDuration === 0) {\n            // Smooth scrolling not supported.\n            return this.setScrollPositionNow(update);\n        }\n        if (this._smoothScrolling) {\n            // Combine our pending scrollLeft/scrollTop with incoming scrollLeft/scrollTop\n            update = {\n                scrollLeft: (typeof update.scrollLeft === 'undefined' ? this._smoothScrolling.to.scrollLeft : update.scrollLeft),\n                scrollTop: (typeof update.scrollTop === 'undefined' ? this._smoothScrolling.to.scrollTop : update.scrollTop)\n            };\n            // Validate `update`\n            const validTarget = this._state.withScrollPosition(update);\n            if (this._smoothScrolling.to.scrollLeft === validTarget.scrollLeft && this._smoothScrolling.to.scrollTop === validTarget.scrollTop) {\n                // No need to interrupt or extend the current animation since we're going to the same place\n                return;\n            }\n            let newSmoothScrolling;\n            if (reuseAnimation) {\n                newSmoothScrolling = new SmoothScrollingOperation(this._smoothScrolling.from, validTarget, this._smoothScrolling.startTime, this._smoothScrolling.duration);\n            }\n            else {\n                newSmoothScrolling = this._smoothScrolling.combine(this._state, validTarget, this._smoothScrollDuration);\n            }\n            this._smoothScrolling.dispose();\n            this._smoothScrolling = newSmoothScrolling;\n        }\n        else {\n            // Validate `update`\n            const validTarget = this._state.withScrollPosition(update);\n            this._smoothScrolling = SmoothScrollingOperation.start(this._state, validTarget, this._smoothScrollDuration);\n        }\n        // Begin smooth scrolling animation\n        this._smoothScrolling.animationFrameDisposable = this._scheduleAtNextAnimationFrame(() => {\n            if (!this._smoothScrolling) {\n                return;\n            }\n            this._smoothScrolling.animationFrameDisposable = null;\n            this._performSmoothScrolling();\n        });\n    }\n    hasPendingScrollAnimation() {\n        return Boolean(this._smoothScrolling);\n    }\n    _performSmoothScrolling() {\n        if (!this._smoothScrolling) {\n            return;\n        }\n        const update = this._smoothScrolling.tick();\n        const newState = this._state.withScrollPosition(update);\n        this._setState(newState, true);\n        if (!this._smoothScrolling) {\n            // Looks like someone canceled the smooth scrolling\n            // from the scroll event handler\n            return;\n        }\n        if (update.isDone) {\n            this._smoothScrolling.dispose();\n            this._smoothScrolling = null;\n            return;\n        }\n        // Continue smooth scrolling animation\n        this._smoothScrolling.animationFrameDisposable = this._scheduleAtNextAnimationFrame(() => {\n            if (!this._smoothScrolling) {\n                return;\n            }\n            this._smoothScrolling.animationFrameDisposable = null;\n            this._performSmoothScrolling();\n        });\n    }\n    _setState(newState, inSmoothScrolling) {\n        const oldState = this._state;\n        if (oldState.equals(newState)) {\n            // no change\n            return;\n        }\n        this._state = newState;\n        this._onScroll.fire(this._state.createScrollEvent(oldState, inSmoothScrolling));\n    }\n}\nexport class SmoothScrollingUpdate {\n    constructor(scrollLeft, scrollTop, isDone) {\n        this.scrollLeft = scrollLeft;\n        this.scrollTop = scrollTop;\n        this.isDone = isDone;\n    }\n}\nfunction createEaseOutCubic(from, to) {\n    const delta = to - from;\n    return function (completion) {\n        return from + delta * easeOutCubic(completion);\n    };\n}\nfunction createComposed(a, b, cut) {\n    return function (completion) {\n        if (completion < cut) {\n            return a(completion / cut);\n        }\n        return b((completion - cut) / (1 - cut));\n    };\n}\nexport class SmoothScrollingOperation {\n    constructor(from, to, startTime, duration) {\n        this.from = from;\n        this.to = to;\n        this.duration = duration;\n        this.startTime = startTime;\n        this.animationFrameDisposable = null;\n        this._initAnimations();\n    }\n    _initAnimations() {\n        this.scrollLeft = this._initAnimation(this.from.scrollLeft, this.to.scrollLeft, this.to.width);\n        this.scrollTop = this._initAnimation(this.from.scrollTop, this.to.scrollTop, this.to.height);\n    }\n    _initAnimation(from, to, viewportSize) {\n        const delta = Math.abs(from - to);\n        if (delta > 2.5 * viewportSize) {\n            let stop1, stop2;\n            if (from < to) {\n                // scroll to 75% of the viewportSize\n                stop1 = from + 0.75 * viewportSize;\n                stop2 = to - 0.75 * viewportSize;\n            }\n            else {\n                stop1 = from - 0.75 * viewportSize;\n                stop2 = to + 0.75 * viewportSize;\n            }\n            return createComposed(createEaseOutCubic(from, stop1), createEaseOutCubic(stop2, to), 0.33);\n        }\n        return createEaseOutCubic(from, to);\n    }\n    dispose() {\n        if (this.animationFrameDisposable !== null) {\n            this.animationFrameDisposable.dispose();\n            this.animationFrameDisposable = null;\n        }\n    }\n    acceptScrollDimensions(state) {\n        this.to = state.withScrollPosition(this.to);\n        this._initAnimations();\n    }\n    tick() {\n        return this._tick(Date.now());\n    }\n    _tick(now) {\n        const completion = (now - this.startTime) / this.duration;\n        if (completion < 1) {\n            const newScrollLeft = this.scrollLeft(completion);\n            const newScrollTop = this.scrollTop(completion);\n            return new SmoothScrollingUpdate(newScrollLeft, newScrollTop, false);\n        }\n        return new SmoothScrollingUpdate(this.to.scrollLeft, this.to.scrollTop, true);\n    }\n    combine(from, to, duration) {\n        return SmoothScrollingOperation.start(from, to, duration);\n    }\n    static start(from, to, duration) {\n        // +10 / -10 : pretend the animation already started for a quicker response to a scroll request\n        duration = duration + 10;\n        const startTime = Date.now() - 10;\n        return new SmoothScrollingOperation(from, to, startTime, duration);\n    }\n}\nfunction easeInCubic(t) {\n    return Math.pow(t, 3);\n}\nfunction easeOutCubic(t) {\n    return 1 - easeInCubic(1 - t);\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,OAAO,QAAQ,YAAY;AACpC,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,OAAO,MAAMC,WAAW,CAAC;EACrBC,WAAWA,CAACC,mBAAmB,EAAEC,KAAK,EAAEC,WAAW,EAAEC,UAAU,EAAEC,MAAM,EAAEC,YAAY,EAAEC,SAAS,EAAE;IAC9F,IAAI,CAACN,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACO,iBAAiB,GAAGC,SAAS;IAClC,IAAI,IAAI,CAACR,mBAAmB,EAAE;MAC1BC,KAAK,GAAGA,KAAK,GAAG,CAAC;MACjBC,WAAW,GAAGA,WAAW,GAAG,CAAC;MAC7BC,UAAU,GAAGA,UAAU,GAAG,CAAC;MAC3BC,MAAM,GAAGA,MAAM,GAAG,CAAC;MACnBC,YAAY,GAAGA,YAAY,GAAG,CAAC;MAC/BC,SAAS,GAAGA,SAAS,GAAG,CAAC;IAC7B;IACA,IAAI,CAACG,aAAa,GAAGN,UAAU,CAAC,CAAC;IACjC,IAAI,CAACO,YAAY,GAAGJ,SAAS,CAAC,CAAC;IAC/B,IAAIL,KAAK,GAAG,CAAC,EAAE;MACXA,KAAK,GAAG,CAAC;IACb;IACA,IAAIE,UAAU,GAAGF,KAAK,GAAGC,WAAW,EAAE;MAClCC,UAAU,GAAGD,WAAW,GAAGD,KAAK;IACpC;IACA,IAAIE,UAAU,GAAG,CAAC,EAAE;MAChBA,UAAU,GAAG,CAAC;IAClB;IACA,IAAIC,MAAM,GAAG,CAAC,EAAE;MACZA,MAAM,GAAG,CAAC;IACd;IACA,IAAIE,SAAS,GAAGF,MAAM,GAAGC,YAAY,EAAE;MACnCC,SAAS,GAAGD,YAAY,GAAGD,MAAM;IACrC;IACA,IAAIE,SAAS,GAAG,CAAC,EAAE;MACfA,SAAS,GAAG,CAAC;IACjB;IACA,IAAI,CAACL,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,SAAS,GAAGA,SAAS;EAC9B;EACAK,MAAMA,CAACC,KAAK,EAAE;IACV,OAAQ,IAAI,CAACH,aAAa,KAAKG,KAAK,CAACH,aAAa,IAC3C,IAAI,CAACC,YAAY,KAAKE,KAAK,CAACF,YAAY,IACxC,IAAI,CAACT,KAAK,KAAKW,KAAK,CAACX,KAAK,IAC1B,IAAI,CAACC,WAAW,KAAKU,KAAK,CAACV,WAAW,IACtC,IAAI,CAACC,UAAU,KAAKS,KAAK,CAACT,UAAU,IACpC,IAAI,CAACC,MAAM,KAAKQ,KAAK,CAACR,MAAM,IAC5B,IAAI,CAACC,YAAY,KAAKO,KAAK,CAACP,YAAY,IACxC,IAAI,CAACC,SAAS,KAAKM,KAAK,CAACN,SAAS;EAC7C;EACAO,oBAAoBA,CAACC,MAAM,EAAEC,qBAAqB,EAAE;IAChD,OAAO,IAAIjB,WAAW,CAAC,IAAI,CAACE,mBAAmB,EAAG,OAAOc,MAAM,CAACb,KAAK,KAAK,WAAW,GAAGa,MAAM,CAACb,KAAK,GAAG,IAAI,CAACA,KAAK,EAAI,OAAOa,MAAM,CAACZ,WAAW,KAAK,WAAW,GAAGY,MAAM,CAACZ,WAAW,GAAG,IAAI,CAACA,WAAW,EAAGa,qBAAqB,GAAG,IAAI,CAACN,aAAa,GAAG,IAAI,CAACN,UAAU,EAAG,OAAOW,MAAM,CAACV,MAAM,KAAK,WAAW,GAAGU,MAAM,CAACV,MAAM,GAAG,IAAI,CAACA,MAAM,EAAI,OAAOU,MAAM,CAACT,YAAY,KAAK,WAAW,GAAGS,MAAM,CAACT,YAAY,GAAG,IAAI,CAACA,YAAY,EAAGU,qBAAqB,GAAG,IAAI,CAACL,YAAY,GAAG,IAAI,CAACJ,SAAS,CAAC;EACpe;EACAU,kBAAkBA,CAACF,MAAM,EAAE;IACvB,OAAO,IAAIhB,WAAW,CAAC,IAAI,CAACE,mBAAmB,EAAE,IAAI,CAACC,KAAK,EAAE,IAAI,CAACC,WAAW,EAAG,OAAOY,MAAM,CAACX,UAAU,KAAK,WAAW,GAAGW,MAAM,CAACX,UAAU,GAAG,IAAI,CAACM,aAAa,EAAG,IAAI,CAACL,MAAM,EAAE,IAAI,CAACC,YAAY,EAAG,OAAOS,MAAM,CAACR,SAAS,KAAK,WAAW,GAAGQ,MAAM,CAACR,SAAS,GAAG,IAAI,CAACI,YAAa,CAAC;EACzR;EACAO,iBAAiBA,CAACC,QAAQ,EAAEC,iBAAiB,EAAE;IAC3C,MAAMC,YAAY,GAAI,IAAI,CAACnB,KAAK,KAAKiB,QAAQ,CAACjB,KAAM;IACpD,MAAMoB,kBAAkB,GAAI,IAAI,CAACnB,WAAW,KAAKgB,QAAQ,CAAChB,WAAY;IACtE,MAAMoB,iBAAiB,GAAI,IAAI,CAACnB,UAAU,KAAKe,QAAQ,CAACf,UAAW;IACnE,MAAMoB,aAAa,GAAI,IAAI,CAACnB,MAAM,KAAKc,QAAQ,CAACd,MAAO;IACvD,MAAMoB,mBAAmB,GAAI,IAAI,CAACnB,YAAY,KAAKa,QAAQ,CAACb,YAAa;IACzE,MAAMoB,gBAAgB,GAAI,IAAI,CAACnB,SAAS,KAAKY,QAAQ,CAACZ,SAAU;IAChE,OAAO;MACHa,iBAAiB,EAAEA,iBAAiB;MACpCO,QAAQ,EAAER,QAAQ,CAACjB,KAAK;MACxB0B,cAAc,EAAET,QAAQ,CAAChB,WAAW;MACpC0B,aAAa,EAAEV,QAAQ,CAACf,UAAU;MAClCF,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBC,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7BC,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3B0B,SAAS,EAAEX,QAAQ,CAACd,MAAM;MAC1B0B,eAAe,EAAEZ,QAAQ,CAACb,YAAY;MACtC0B,YAAY,EAAEb,QAAQ,CAACZ,SAAS;MAChCF,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBC,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/BC,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBc,YAAY,EAAEA,YAAY;MAC1BC,kBAAkB,EAAEA,kBAAkB;MACtCC,iBAAiB,EAAEA,iBAAiB;MACpCC,aAAa,EAAEA,aAAa;MAC5BC,mBAAmB,EAAEA,mBAAmB;MACxCC,gBAAgB,EAAEA;IACtB,CAAC;EACL;AACJ;AACA,OAAO,MAAMO,UAAU,SAASnC,UAAU,CAAC;EACvCE,WAAWA,CAACkC,OAAO,EAAE;IACjB,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,gBAAgB,GAAG1B,SAAS;IACjC,IAAI,CAAC2B,SAAS,GAAG,IAAI,CAACC,SAAS,CAAC,IAAIxC,OAAO,CAAC,CAAC,CAAC;IAC9C,IAAI,CAACyC,QAAQ,GAAG,IAAI,CAACF,SAAS,CAACG,KAAK;IACpC,IAAI,CAACC,qBAAqB,GAAGN,OAAO,CAACO,oBAAoB;IACzD,IAAI,CAACC,6BAA6B,GAAGR,OAAO,CAACS,4BAA4B;IACzE,IAAI,CAACC,MAAM,GAAG,IAAI7C,WAAW,CAACmC,OAAO,CAACW,kBAAkB,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC3E,IAAI,CAACC,gBAAgB,GAAG,IAAI;EAChC;EACAC,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACD,gBAAgB,EAAE;MACvB,IAAI,CAACA,gBAAgB,CAACC,OAAO,CAAC,CAAC;MAC/B,IAAI,CAACD,gBAAgB,GAAG,IAAI;IAChC;IACA,KAAK,CAACC,OAAO,CAAC,CAAC;EACnB;EACAC,uBAAuBA,CAACP,oBAAoB,EAAE;IAC1C,IAAI,CAACD,qBAAqB,GAAGC,oBAAoB;EACrD;EACAQ,sBAAsBA,CAACC,cAAc,EAAE;IACnC,OAAO,IAAI,CAACN,MAAM,CAAC3B,kBAAkB,CAACiC,cAAc,CAAC;EACzD;EACAC,mBAAmBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACP,MAAM;EACtB;EACAQ,mBAAmBA,CAACC,UAAU,EAAErC,qBAAqB,EAAE;IACnD,MAAMsC,QAAQ,GAAG,IAAI,CAACV,MAAM,CAAC9B,oBAAoB,CAACuC,UAAU,EAAErC,qBAAqB,CAAC;IACpF,IAAI,CAACuC,SAAS,CAACD,QAAQ,EAAEE,OAAO,CAAC,IAAI,CAACV,gBAAgB,CAAC,CAAC;IACxD;IACA,IAAI,CAACA,gBAAgB,EAAEW,sBAAsB,CAAC,IAAI,CAACb,MAAM,CAAC;EAC9D;EACA;AACJ;AACA;AACA;EACIc,uBAAuBA,CAAA,EAAG;IACtB,IAAI,IAAI,CAACZ,gBAAgB,EAAE;MACvB,OAAO,IAAI,CAACA,gBAAgB,CAACa,EAAE;IACnC;IACA,OAAO,IAAI,CAACf,MAAM;EACtB;EACA;AACJ;AACA;AACA;EACIgB,wBAAwBA,CAAA,EAAG;IACvB,OAAO,IAAI,CAAChB,MAAM;EACtB;EACAiB,oBAAoBA,CAAC9C,MAAM,EAAE;IACzB;IACA,MAAMuC,QAAQ,GAAG,IAAI,CAACV,MAAM,CAAC3B,kBAAkB,CAACF,MAAM,CAAC;IACvD;IACA,IAAI,IAAI,CAAC+B,gBAAgB,EAAE;MACvB,IAAI,CAACA,gBAAgB,CAACC,OAAO,CAAC,CAAC;MAC/B,IAAI,CAACD,gBAAgB,GAAG,IAAI;IAChC;IACA,IAAI,CAACS,SAAS,CAACD,QAAQ,EAAE,KAAK,CAAC;EACnC;EACAQ,uBAAuBA,CAAC/C,MAAM,EAAEgD,cAAc,EAAE;IAC5C,IAAI,IAAI,CAACvB,qBAAqB,KAAK,CAAC,EAAE;MAClC;MACA,OAAO,IAAI,CAACqB,oBAAoB,CAAC9C,MAAM,CAAC;IAC5C;IACA,IAAI,IAAI,CAAC+B,gBAAgB,EAAE;MACvB;MACA/B,MAAM,GAAG;QACLX,UAAU,EAAG,OAAOW,MAAM,CAACX,UAAU,KAAK,WAAW,GAAG,IAAI,CAAC0C,gBAAgB,CAACa,EAAE,CAACvD,UAAU,GAAGW,MAAM,CAACX,UAAW;QAChHG,SAAS,EAAG,OAAOQ,MAAM,CAACR,SAAS,KAAK,WAAW,GAAG,IAAI,CAACuC,gBAAgB,CAACa,EAAE,CAACpD,SAAS,GAAGQ,MAAM,CAACR;MACtG,CAAC;MACD;MACA,MAAMyD,WAAW,GAAG,IAAI,CAACpB,MAAM,CAAC3B,kBAAkB,CAACF,MAAM,CAAC;MAC1D,IAAI,IAAI,CAAC+B,gBAAgB,CAACa,EAAE,CAACvD,UAAU,KAAK4D,WAAW,CAAC5D,UAAU,IAAI,IAAI,CAAC0C,gBAAgB,CAACa,EAAE,CAACpD,SAAS,KAAKyD,WAAW,CAACzD,SAAS,EAAE;QAChI;QACA;MACJ;MACA,IAAI0D,kBAAkB;MACtB,IAAIF,cAAc,EAAE;QAChBE,kBAAkB,GAAG,IAAIC,wBAAwB,CAAC,IAAI,CAACpB,gBAAgB,CAACqB,IAAI,EAAEH,WAAW,EAAE,IAAI,CAAClB,gBAAgB,CAACsB,SAAS,EAAE,IAAI,CAACtB,gBAAgB,CAACuB,QAAQ,CAAC;MAC/J,CAAC,MACI;QACDJ,kBAAkB,GAAG,IAAI,CAACnB,gBAAgB,CAACwB,OAAO,CAAC,IAAI,CAAC1B,MAAM,EAAEoB,WAAW,EAAE,IAAI,CAACxB,qBAAqB,CAAC;MAC5G;MACA,IAAI,CAACM,gBAAgB,CAACC,OAAO,CAAC,CAAC;MAC/B,IAAI,CAACD,gBAAgB,GAAGmB,kBAAkB;IAC9C,CAAC,MACI;MACD;MACA,MAAMD,WAAW,GAAG,IAAI,CAACpB,MAAM,CAAC3B,kBAAkB,CAACF,MAAM,CAAC;MAC1D,IAAI,CAAC+B,gBAAgB,GAAGoB,wBAAwB,CAACK,KAAK,CAAC,IAAI,CAAC3B,MAAM,EAAEoB,WAAW,EAAE,IAAI,CAACxB,qBAAqB,CAAC;IAChH;IACA;IACA,IAAI,CAACM,gBAAgB,CAAC0B,wBAAwB,GAAG,IAAI,CAAC9B,6BAA6B,CAAC,MAAM;MACtF,IAAI,CAAC,IAAI,CAACI,gBAAgB,EAAE;QACxB;MACJ;MACA,IAAI,CAACA,gBAAgB,CAAC0B,wBAAwB,GAAG,IAAI;MACrD,IAAI,CAACC,uBAAuB,CAAC,CAAC;IAClC,CAAC,CAAC;EACN;EACAC,yBAAyBA,CAAA,EAAG;IACxB,OAAOlB,OAAO,CAAC,IAAI,CAACV,gBAAgB,CAAC;EACzC;EACA2B,uBAAuBA,CAAA,EAAG;IACtB,IAAI,CAAC,IAAI,CAAC3B,gBAAgB,EAAE;MACxB;IACJ;IACA,MAAM/B,MAAM,GAAG,IAAI,CAAC+B,gBAAgB,CAAC6B,IAAI,CAAC,CAAC;IAC3C,MAAMrB,QAAQ,GAAG,IAAI,CAACV,MAAM,CAAC3B,kBAAkB,CAACF,MAAM,CAAC;IACvD,IAAI,CAACwC,SAAS,CAACD,QAAQ,EAAE,IAAI,CAAC;IAC9B,IAAI,CAAC,IAAI,CAACR,gBAAgB,EAAE;MACxB;MACA;MACA;IACJ;IACA,IAAI/B,MAAM,CAAC6D,MAAM,EAAE;MACf,IAAI,CAAC9B,gBAAgB,CAACC,OAAO,CAAC,CAAC;MAC/B,IAAI,CAACD,gBAAgB,GAAG,IAAI;MAC5B;IACJ;IACA;IACA,IAAI,CAACA,gBAAgB,CAAC0B,wBAAwB,GAAG,IAAI,CAAC9B,6BAA6B,CAAC,MAAM;MACtF,IAAI,CAAC,IAAI,CAACI,gBAAgB,EAAE;QACxB;MACJ;MACA,IAAI,CAACA,gBAAgB,CAAC0B,wBAAwB,GAAG,IAAI;MACrD,IAAI,CAACC,uBAAuB,CAAC,CAAC;IAClC,CAAC,CAAC;EACN;EACAlB,SAASA,CAACD,QAAQ,EAAElC,iBAAiB,EAAE;IACnC,MAAMyD,QAAQ,GAAG,IAAI,CAACjC,MAAM;IAC5B,IAAIiC,QAAQ,CAACjE,MAAM,CAAC0C,QAAQ,CAAC,EAAE;MAC3B;MACA;IACJ;IACA,IAAI,CAACV,MAAM,GAAGU,QAAQ;IACtB,IAAI,CAAClB,SAAS,CAAC0C,IAAI,CAAC,IAAI,CAAClC,MAAM,CAAC1B,iBAAiB,CAAC2D,QAAQ,EAAEzD,iBAAiB,CAAC,CAAC;EACnF;AACJ;AACA,OAAO,MAAM2D,qBAAqB,CAAC;EAC/B/E,WAAWA,CAACI,UAAU,EAAEG,SAAS,EAAEqE,MAAM,EAAE;IACvC,IAAI,CAACxE,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACG,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACqE,MAAM,GAAGA,MAAM;EACxB;AACJ;AACA,SAASI,kBAAkBA,CAACb,IAAI,EAAER,EAAE,EAAE;EAClC,MAAMsB,KAAK,GAAGtB,EAAE,GAAGQ,IAAI;EACvB,OAAO,UAAUe,UAAU,EAAE;IACzB,OAAOf,IAAI,GAAGc,KAAK,GAAGE,YAAY,CAACD,UAAU,CAAC;EAClD,CAAC;AACL;AACA,SAASE,cAAcA,CAACC,CAAC,EAAEC,CAAC,EAAEC,GAAG,EAAE;EAC/B,OAAO,UAAUL,UAAU,EAAE;IACzB,IAAIA,UAAU,GAAGK,GAAG,EAAE;MAClB,OAAOF,CAAC,CAACH,UAAU,GAAGK,GAAG,CAAC;IAC9B;IACA,OAAOD,CAAC,CAAC,CAACJ,UAAU,GAAGK,GAAG,KAAK,CAAC,GAAGA,GAAG,CAAC,CAAC;EAC5C,CAAC;AACL;AACA,OAAO,MAAMrB,wBAAwB,CAAC;EAClClE,WAAWA,CAACmE,IAAI,EAAER,EAAE,EAAES,SAAS,EAAEC,QAAQ,EAAE;IACvC,IAAI,CAACF,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACR,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACU,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACD,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACI,wBAAwB,GAAG,IAAI;IACpC,IAAI,CAACgB,eAAe,CAAC,CAAC;EAC1B;EACAA,eAAeA,CAAA,EAAG;IACd,IAAI,CAACpF,UAAU,GAAG,IAAI,CAACqF,cAAc,CAAC,IAAI,CAACtB,IAAI,CAAC/D,UAAU,EAAE,IAAI,CAACuD,EAAE,CAACvD,UAAU,EAAE,IAAI,CAACuD,EAAE,CAACzD,KAAK,CAAC;IAC9F,IAAI,CAACK,SAAS,GAAG,IAAI,CAACkF,cAAc,CAAC,IAAI,CAACtB,IAAI,CAAC5D,SAAS,EAAE,IAAI,CAACoD,EAAE,CAACpD,SAAS,EAAE,IAAI,CAACoD,EAAE,CAACtD,MAAM,CAAC;EAChG;EACAoF,cAAcA,CAACtB,IAAI,EAAER,EAAE,EAAE+B,YAAY,EAAE;IACnC,MAAMT,KAAK,GAAGU,IAAI,CAACC,GAAG,CAACzB,IAAI,GAAGR,EAAE,CAAC;IACjC,IAAIsB,KAAK,GAAG,GAAG,GAAGS,YAAY,EAAE;MAC5B,IAAIG,KAAK,EAAEC,KAAK;MAChB,IAAI3B,IAAI,GAAGR,EAAE,EAAE;QACX;QACAkC,KAAK,GAAG1B,IAAI,GAAG,IAAI,GAAGuB,YAAY;QAClCI,KAAK,GAAGnC,EAAE,GAAG,IAAI,GAAG+B,YAAY;MACpC,CAAC,MACI;QACDG,KAAK,GAAG1B,IAAI,GAAG,IAAI,GAAGuB,YAAY;QAClCI,KAAK,GAAGnC,EAAE,GAAG,IAAI,GAAG+B,YAAY;MACpC;MACA,OAAON,cAAc,CAACJ,kBAAkB,CAACb,IAAI,EAAE0B,KAAK,CAAC,EAAEb,kBAAkB,CAACc,KAAK,EAAEnC,EAAE,CAAC,EAAE,IAAI,CAAC;IAC/F;IACA,OAAOqB,kBAAkB,CAACb,IAAI,EAAER,EAAE,CAAC;EACvC;EACAZ,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACyB,wBAAwB,KAAK,IAAI,EAAE;MACxC,IAAI,CAACA,wBAAwB,CAACzB,OAAO,CAAC,CAAC;MACvC,IAAI,CAACyB,wBAAwB,GAAG,IAAI;IACxC;EACJ;EACAf,sBAAsBA,CAACsC,KAAK,EAAE;IAC1B,IAAI,CAACpC,EAAE,GAAGoC,KAAK,CAAC9E,kBAAkB,CAAC,IAAI,CAAC0C,EAAE,CAAC;IAC3C,IAAI,CAAC6B,eAAe,CAAC,CAAC;EAC1B;EACAb,IAAIA,CAAA,EAAG;IACH,OAAO,IAAI,CAACqB,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;EACjC;EACAF,KAAKA,CAACE,GAAG,EAAE;IACP,MAAMhB,UAAU,GAAG,CAACgB,GAAG,GAAG,IAAI,CAAC9B,SAAS,IAAI,IAAI,CAACC,QAAQ;IACzD,IAAIa,UAAU,GAAG,CAAC,EAAE;MAChB,MAAMiB,aAAa,GAAG,IAAI,CAAC/F,UAAU,CAAC8E,UAAU,CAAC;MACjD,MAAMkB,YAAY,GAAG,IAAI,CAAC7F,SAAS,CAAC2E,UAAU,CAAC;MAC/C,OAAO,IAAIH,qBAAqB,CAACoB,aAAa,EAAEC,YAAY,EAAE,KAAK,CAAC;IACxE;IACA,OAAO,IAAIrB,qBAAqB,CAAC,IAAI,CAACpB,EAAE,CAACvD,UAAU,EAAE,IAAI,CAACuD,EAAE,CAACpD,SAAS,EAAE,IAAI,CAAC;EACjF;EACA+D,OAAOA,CAACH,IAAI,EAAER,EAAE,EAAEU,QAAQ,EAAE;IACxB,OAAOH,wBAAwB,CAACK,KAAK,CAACJ,IAAI,EAAER,EAAE,EAAEU,QAAQ,CAAC;EAC7D;EACA,OAAOE,KAAKA,CAACJ,IAAI,EAAER,EAAE,EAAEU,QAAQ,EAAE;IAC7B;IACAA,QAAQ,GAAGA,QAAQ,GAAG,EAAE;IACxB,MAAMD,SAAS,GAAG6B,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE;IACjC,OAAO,IAAIhC,wBAAwB,CAACC,IAAI,EAAER,EAAE,EAAES,SAAS,EAAEC,QAAQ,CAAC;EACtE;AACJ;AACA,SAASgC,WAAWA,CAACC,CAAC,EAAE;EACpB,OAAOX,IAAI,CAACY,GAAG,CAACD,CAAC,EAAE,CAAC,CAAC;AACzB;AACA,SAASnB,YAAYA,CAACmB,CAAC,EAAE;EACrB,OAAO,CAAC,GAAGD,WAAW,CAAC,CAAC,GAAGC,CAAC,CAAC;AACjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}