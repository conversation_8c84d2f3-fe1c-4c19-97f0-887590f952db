{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as browser from '../../../../base/browser/browser.js';\nimport { createFastDomNode } from '../../../../base/browser/fastDomNode.js';\nimport * as platform from '../../../../base/common/platform.js';\nimport { RangeUtil } from './rangeUtil.js';\nimport { Float<PERSON>orizontalR<PERSON><PERSON>, VisibleRanges } from '../../view/renderingContext.js';\nimport { LineDecoration } from '../../../common/viewLayout/lineDecorations.js';\nimport { RenderLineInput, renderViewLine, LineRange, DomPosition } from '../../../common/viewLayout/viewLineRenderer.js';\nimport { isHighContrast } from '../../../../platform/theme/common/theme.js';\nimport { EditorFontLigatures } from '../../../common/config/editorOptions.js';\nconst canUseFastRenderedViewLine = function () {\n  if (platform.isNative) {\n    // In VSCode we know very well when the zoom level changes\n    return true;\n  }\n  if (platform.isLinux || browser.isFirefox || browser.isSafari) {\n    // On Linux, it appears that zooming affects char widths (in pixels), which is unexpected.\n    // --\n    // Even though we read character widths correctly, having read them at a specific zoom level\n    // does not mean they are the same at the current zoom level.\n    // --\n    // This could be improved if we ever figure out how to get an event when browsers zoom,\n    // but until then we have to stick with reading client rects.\n    // --\n    // The same has been observed with Firefox on Windows7\n    // --\n    // The same has been oversved with Safari\n    return false;\n  }\n  return true;\n}();\nlet monospaceAssumptionsAreValid = true;\nexport class ViewLineOptions {\n  constructor(config, themeType) {\n    this.themeType = themeType;\n    const options = config.options;\n    const fontInfo = options.get(50 /* EditorOption.fontInfo */);\n    const experimentalWhitespaceRendering = options.get(38 /* EditorOption.experimentalWhitespaceRendering */);\n    if (experimentalWhitespaceRendering === 'off') {\n      this.renderWhitespace = options.get(100 /* EditorOption.renderWhitespace */);\n    } else {\n      // whitespace is rendered in a different layer\n      this.renderWhitespace = 'none';\n    }\n    this.renderControlCharacters = options.get(95 /* EditorOption.renderControlCharacters */);\n    this.spaceWidth = fontInfo.spaceWidth;\n    this.middotWidth = fontInfo.middotWidth;\n    this.wsmiddotWidth = fontInfo.wsmiddotWidth;\n    this.useMonospaceOptimizations = fontInfo.isMonospace && !options.get(33 /* EditorOption.disableMonospaceOptimizations */);\n    this.canUseHalfwidthRightwardsArrow = fontInfo.canUseHalfwidthRightwardsArrow;\n    this.lineHeight = options.get(67 /* EditorOption.lineHeight */);\n    this.stopRenderingLineAfter = options.get(118 /* EditorOption.stopRenderingLineAfter */);\n    this.fontLigatures = options.get(51 /* EditorOption.fontLigatures */);\n  }\n  equals(other) {\n    return this.themeType === other.themeType && this.renderWhitespace === other.renderWhitespace && this.renderControlCharacters === other.renderControlCharacters && this.spaceWidth === other.spaceWidth && this.middotWidth === other.middotWidth && this.wsmiddotWidth === other.wsmiddotWidth && this.useMonospaceOptimizations === other.useMonospaceOptimizations && this.canUseHalfwidthRightwardsArrow === other.canUseHalfwidthRightwardsArrow && this.lineHeight === other.lineHeight && this.stopRenderingLineAfter === other.stopRenderingLineAfter && this.fontLigatures === other.fontLigatures;\n  }\n}\nexport let ViewLine = /*#__PURE__*/(() => {\n  class ViewLine {\n    static {\n      this.CLASS_NAME = 'view-line';\n    }\n    constructor(options) {\n      this._options = options;\n      this._isMaybeInvalid = true;\n      this._renderedViewLine = null;\n    }\n    // --- begin IVisibleLineData\n    getDomNode() {\n      if (this._renderedViewLine && this._renderedViewLine.domNode) {\n        return this._renderedViewLine.domNode.domNode;\n      }\n      return null;\n    }\n    setDomNode(domNode) {\n      if (this._renderedViewLine) {\n        this._renderedViewLine.domNode = createFastDomNode(domNode);\n      } else {\n        throw new Error('I have no rendered view line to set the dom node to...');\n      }\n    }\n    onContentChanged() {\n      this._isMaybeInvalid = true;\n    }\n    onTokensChanged() {\n      this._isMaybeInvalid = true;\n    }\n    onDecorationsChanged() {\n      this._isMaybeInvalid = true;\n    }\n    onOptionsChanged(newOptions) {\n      this._isMaybeInvalid = true;\n      this._options = newOptions;\n    }\n    onSelectionChanged() {\n      if (isHighContrast(this._options.themeType) || this._options.renderWhitespace === 'selection') {\n        this._isMaybeInvalid = true;\n        return true;\n      }\n      return false;\n    }\n    renderLine(lineNumber, deltaTop, lineHeight, viewportData, sb) {\n      if (this._isMaybeInvalid === false) {\n        // it appears that nothing relevant has changed\n        return false;\n      }\n      this._isMaybeInvalid = false;\n      const lineData = viewportData.getViewLineRenderingData(lineNumber);\n      const options = this._options;\n      const actualInlineDecorations = LineDecoration.filter(lineData.inlineDecorations, lineNumber, lineData.minColumn, lineData.maxColumn);\n      // Only send selection information when needed for rendering whitespace\n      let selectionsOnLine = null;\n      if (isHighContrast(options.themeType) || this._options.renderWhitespace === 'selection') {\n        const selections = viewportData.selections;\n        for (const selection of selections) {\n          if (selection.endLineNumber < lineNumber || selection.startLineNumber > lineNumber) {\n            // Selection does not intersect line\n            continue;\n          }\n          const startColumn = selection.startLineNumber === lineNumber ? selection.startColumn : lineData.minColumn;\n          const endColumn = selection.endLineNumber === lineNumber ? selection.endColumn : lineData.maxColumn;\n          if (startColumn < endColumn) {\n            if (isHighContrast(options.themeType)) {\n              actualInlineDecorations.push(new LineDecoration(startColumn, endColumn, 'inline-selected-text', 0 /* InlineDecorationType.Regular */));\n            }\n            if (this._options.renderWhitespace === 'selection') {\n              if (!selectionsOnLine) {\n                selectionsOnLine = [];\n              }\n              selectionsOnLine.push(new LineRange(startColumn - 1, endColumn - 1));\n            }\n          }\n        }\n      }\n      const renderLineInput = new RenderLineInput(options.useMonospaceOptimizations, options.canUseHalfwidthRightwardsArrow, lineData.content, lineData.continuesWithWrappedLine, lineData.isBasicASCII, lineData.containsRTL, lineData.minColumn - 1, lineData.tokens, actualInlineDecorations, lineData.tabSize, lineData.startVisibleColumn, options.spaceWidth, options.middotWidth, options.wsmiddotWidth, options.stopRenderingLineAfter, options.renderWhitespace, options.renderControlCharacters, options.fontLigatures !== EditorFontLigatures.OFF, selectionsOnLine);\n      if (this._renderedViewLine && this._renderedViewLine.input.equals(renderLineInput)) {\n        // no need to do anything, we have the same render input\n        return false;\n      }\n      sb.appendString('<div style=\"top:');\n      sb.appendString(String(deltaTop));\n      sb.appendString('px;height:');\n      sb.appendString(String(lineHeight));\n      sb.appendString('px;\" class=\"');\n      sb.appendString(ViewLine.CLASS_NAME);\n      sb.appendString('\">');\n      const output = renderViewLine(renderLineInput, sb);\n      sb.appendString('</div>');\n      let renderedViewLine = null;\n      if (monospaceAssumptionsAreValid && canUseFastRenderedViewLine && lineData.isBasicASCII && options.useMonospaceOptimizations && output.containsForeignElements === 0 /* ForeignElementType.None */) {\n        renderedViewLine = new FastRenderedViewLine(this._renderedViewLine ? this._renderedViewLine.domNode : null, renderLineInput, output.characterMapping);\n      }\n      if (!renderedViewLine) {\n        renderedViewLine = createRenderedLine(this._renderedViewLine ? this._renderedViewLine.domNode : null, renderLineInput, output.characterMapping, output.containsRTL, output.containsForeignElements);\n      }\n      this._renderedViewLine = renderedViewLine;\n      return true;\n    }\n    layoutLine(lineNumber, deltaTop, lineHeight) {\n      if (this._renderedViewLine && this._renderedViewLine.domNode) {\n        this._renderedViewLine.domNode.setTop(deltaTop);\n        this._renderedViewLine.domNode.setHeight(lineHeight);\n      }\n    }\n    // --- end IVisibleLineData\n    getWidth(context) {\n      if (!this._renderedViewLine) {\n        return 0;\n      }\n      return this._renderedViewLine.getWidth(context);\n    }\n    getWidthIsFast() {\n      if (!this._renderedViewLine) {\n        return true;\n      }\n      return this._renderedViewLine.getWidthIsFast();\n    }\n    needsMonospaceFontCheck() {\n      if (!this._renderedViewLine) {\n        return false;\n      }\n      return this._renderedViewLine instanceof FastRenderedViewLine;\n    }\n    monospaceAssumptionsAreValid() {\n      if (!this._renderedViewLine) {\n        return monospaceAssumptionsAreValid;\n      }\n      if (this._renderedViewLine instanceof FastRenderedViewLine) {\n        return this._renderedViewLine.monospaceAssumptionsAreValid();\n      }\n      return monospaceAssumptionsAreValid;\n    }\n    onMonospaceAssumptionsInvalidated() {\n      if (this._renderedViewLine && this._renderedViewLine instanceof FastRenderedViewLine) {\n        this._renderedViewLine = this._renderedViewLine.toSlowRenderedLine();\n      }\n    }\n    getVisibleRangesForRange(lineNumber, startColumn, endColumn, context) {\n      if (!this._renderedViewLine) {\n        return null;\n      }\n      startColumn = Math.min(this._renderedViewLine.input.lineContent.length + 1, Math.max(1, startColumn));\n      endColumn = Math.min(this._renderedViewLine.input.lineContent.length + 1, Math.max(1, endColumn));\n      const stopRenderingLineAfter = this._renderedViewLine.input.stopRenderingLineAfter;\n      if (stopRenderingLineAfter !== -1 && startColumn > stopRenderingLineAfter + 1 && endColumn > stopRenderingLineAfter + 1) {\n        // This range is obviously not visible\n        return new VisibleRanges(true, [new FloatHorizontalRange(this.getWidth(context), 0)]);\n      }\n      if (stopRenderingLineAfter !== -1 && startColumn > stopRenderingLineAfter + 1) {\n        startColumn = stopRenderingLineAfter + 1;\n      }\n      if (stopRenderingLineAfter !== -1 && endColumn > stopRenderingLineAfter + 1) {\n        endColumn = stopRenderingLineAfter + 1;\n      }\n      const horizontalRanges = this._renderedViewLine.getVisibleRangesForRange(lineNumber, startColumn, endColumn, context);\n      if (horizontalRanges && horizontalRanges.length > 0) {\n        return new VisibleRanges(false, horizontalRanges);\n      }\n      return null;\n    }\n    getColumnOfNodeOffset(spanNode, offset) {\n      if (!this._renderedViewLine) {\n        return 1;\n      }\n      return this._renderedViewLine.getColumnOfNodeOffset(spanNode, offset);\n    }\n  }\n  return ViewLine;\n})();\n/**\n * A rendered line which is guaranteed to contain only regular ASCII and is rendered with a monospace font.\n */\nclass FastRenderedViewLine {\n  constructor(domNode, renderLineInput, characterMapping) {\n    this._cachedWidth = -1;\n    this.domNode = domNode;\n    this.input = renderLineInput;\n    const keyColumnCount = Math.floor(renderLineInput.lineContent.length / 300 /* Constants.MaxMonospaceDistance */);\n    if (keyColumnCount > 0) {\n      this._keyColumnPixelOffsetCache = new Float32Array(keyColumnCount);\n      for (let i = 0; i < keyColumnCount; i++) {\n        this._keyColumnPixelOffsetCache[i] = -1;\n      }\n    } else {\n      this._keyColumnPixelOffsetCache = null;\n    }\n    this._characterMapping = characterMapping;\n    this._charWidth = renderLineInput.spaceWidth;\n  }\n  getWidth(context) {\n    if (!this.domNode || this.input.lineContent.length < 300 /* Constants.MaxMonospaceDistance */) {\n      const horizontalOffset = this._characterMapping.getHorizontalOffset(this._characterMapping.length);\n      return Math.round(this._charWidth * horizontalOffset);\n    }\n    if (this._cachedWidth === -1) {\n      this._cachedWidth = this._getReadingTarget(this.domNode).offsetWidth;\n      context?.markDidDomLayout();\n    }\n    return this._cachedWidth;\n  }\n  getWidthIsFast() {\n    return this.input.lineContent.length < 300 /* Constants.MaxMonospaceDistance */ || this._cachedWidth !== -1;\n  }\n  monospaceAssumptionsAreValid() {\n    if (!this.domNode) {\n      return monospaceAssumptionsAreValid;\n    }\n    if (this.input.lineContent.length < 300 /* Constants.MaxMonospaceDistance */) {\n      const expectedWidth = this.getWidth(null);\n      const actualWidth = this.domNode.domNode.firstChild.offsetWidth;\n      if (Math.abs(expectedWidth - actualWidth) >= 2) {\n        // more than 2px off\n        console.warn(`monospace assumptions have been violated, therefore disabling monospace optimizations!`);\n        monospaceAssumptionsAreValid = false;\n      }\n    }\n    return monospaceAssumptionsAreValid;\n  }\n  toSlowRenderedLine() {\n    return createRenderedLine(this.domNode, this.input, this._characterMapping, false, 0 /* ForeignElementType.None */);\n  }\n  getVisibleRangesForRange(lineNumber, startColumn, endColumn, context) {\n    const startPosition = this._getColumnPixelOffset(lineNumber, startColumn, context);\n    const endPosition = this._getColumnPixelOffset(lineNumber, endColumn, context);\n    return [new FloatHorizontalRange(startPosition, endPosition - startPosition)];\n  }\n  _getColumnPixelOffset(lineNumber, column, context) {\n    if (column <= 300 /* Constants.MaxMonospaceDistance */) {\n      const horizontalOffset = this._characterMapping.getHorizontalOffset(column);\n      return this._charWidth * horizontalOffset;\n    }\n    const keyColumnOrdinal = Math.floor((column - 1) / 300 /* Constants.MaxMonospaceDistance */) - 1;\n    const keyColumn = (keyColumnOrdinal + 1) * 300 /* Constants.MaxMonospaceDistance */ + 1;\n    let keyColumnPixelOffset = -1;\n    if (this._keyColumnPixelOffsetCache) {\n      keyColumnPixelOffset = this._keyColumnPixelOffsetCache[keyColumnOrdinal];\n      if (keyColumnPixelOffset === -1) {\n        keyColumnPixelOffset = this._actualReadPixelOffset(lineNumber, keyColumn, context);\n        this._keyColumnPixelOffsetCache[keyColumnOrdinal] = keyColumnPixelOffset;\n      }\n    }\n    if (keyColumnPixelOffset === -1) {\n      // Could not read actual key column pixel offset\n      const horizontalOffset = this._characterMapping.getHorizontalOffset(column);\n      return this._charWidth * horizontalOffset;\n    }\n    const keyColumnHorizontalOffset = this._characterMapping.getHorizontalOffset(keyColumn);\n    const horizontalOffset = this._characterMapping.getHorizontalOffset(column);\n    return keyColumnPixelOffset + this._charWidth * (horizontalOffset - keyColumnHorizontalOffset);\n  }\n  _getReadingTarget(myDomNode) {\n    return myDomNode.domNode.firstChild;\n  }\n  _actualReadPixelOffset(lineNumber, column, context) {\n    if (!this.domNode) {\n      return -1;\n    }\n    const domPosition = this._characterMapping.getDomPosition(column);\n    const r = RangeUtil.readHorizontalRanges(this._getReadingTarget(this.domNode), domPosition.partIndex, domPosition.charIndex, domPosition.partIndex, domPosition.charIndex, context);\n    if (!r || r.length === 0) {\n      return -1;\n    }\n    return r[0].left;\n  }\n  getColumnOfNodeOffset(spanNode, offset) {\n    return getColumnOfNodeOffset(this._characterMapping, spanNode, offset);\n  }\n}\n/**\n * Every time we render a line, we save what we have rendered in an instance of this class.\n */\nclass RenderedViewLine {\n  constructor(domNode, renderLineInput, characterMapping, containsRTL, containsForeignElements) {\n    this.domNode = domNode;\n    this.input = renderLineInput;\n    this._characterMapping = characterMapping;\n    this._isWhitespaceOnly = /^\\s*$/.test(renderLineInput.lineContent);\n    this._containsForeignElements = containsForeignElements;\n    this._cachedWidth = -1;\n    this._pixelOffsetCache = null;\n    if (!containsRTL || this._characterMapping.length === 0 /* the line is empty */) {\n      this._pixelOffsetCache = new Float32Array(Math.max(2, this._characterMapping.length + 1));\n      for (let column = 0, len = this._characterMapping.length; column <= len; column++) {\n        this._pixelOffsetCache[column] = -1;\n      }\n    }\n  }\n  // --- Reading from the DOM methods\n  _getReadingTarget(myDomNode) {\n    return myDomNode.domNode.firstChild;\n  }\n  /**\n   * Width of the line in pixels\n   */\n  getWidth(context) {\n    if (!this.domNode) {\n      return 0;\n    }\n    if (this._cachedWidth === -1) {\n      this._cachedWidth = this._getReadingTarget(this.domNode).offsetWidth;\n      context?.markDidDomLayout();\n    }\n    return this._cachedWidth;\n  }\n  getWidthIsFast() {\n    if (this._cachedWidth === -1) {\n      return false;\n    }\n    return true;\n  }\n  /**\n   * Visible ranges for a model range\n   */\n  getVisibleRangesForRange(lineNumber, startColumn, endColumn, context) {\n    if (!this.domNode) {\n      return null;\n    }\n    if (this._pixelOffsetCache !== null) {\n      // the text is LTR\n      const startOffset = this._readPixelOffset(this.domNode, lineNumber, startColumn, context);\n      if (startOffset === -1) {\n        return null;\n      }\n      const endOffset = this._readPixelOffset(this.domNode, lineNumber, endColumn, context);\n      if (endOffset === -1) {\n        return null;\n      }\n      return [new FloatHorizontalRange(startOffset, endOffset - startOffset)];\n    }\n    return this._readVisibleRangesForRange(this.domNode, lineNumber, startColumn, endColumn, context);\n  }\n  _readVisibleRangesForRange(domNode, lineNumber, startColumn, endColumn, context) {\n    if (startColumn === endColumn) {\n      const pixelOffset = this._readPixelOffset(domNode, lineNumber, startColumn, context);\n      if (pixelOffset === -1) {\n        return null;\n      } else {\n        return [new FloatHorizontalRange(pixelOffset, 0)];\n      }\n    } else {\n      return this._readRawVisibleRangesForRange(domNode, startColumn, endColumn, context);\n    }\n  }\n  _readPixelOffset(domNode, lineNumber, column, context) {\n    if (this._characterMapping.length === 0) {\n      // This line has no content\n      if (this._containsForeignElements === 0 /* ForeignElementType.None */) {\n        // We can assume the line is really empty\n        return 0;\n      }\n      if (this._containsForeignElements === 2 /* ForeignElementType.After */) {\n        // We have foreign elements after the (empty) line\n        return 0;\n      }\n      if (this._containsForeignElements === 1 /* ForeignElementType.Before */) {\n        // We have foreign elements before the (empty) line\n        return this.getWidth(context);\n      }\n      // We have foreign elements before & after the (empty) line\n      const readingTarget = this._getReadingTarget(domNode);\n      if (readingTarget.firstChild) {\n        context.markDidDomLayout();\n        return readingTarget.firstChild.offsetWidth;\n      } else {\n        return 0;\n      }\n    }\n    if (this._pixelOffsetCache !== null) {\n      // the text is LTR\n      const cachedPixelOffset = this._pixelOffsetCache[column];\n      if (cachedPixelOffset !== -1) {\n        return cachedPixelOffset;\n      }\n      const result = this._actualReadPixelOffset(domNode, lineNumber, column, context);\n      this._pixelOffsetCache[column] = result;\n      return result;\n    }\n    return this._actualReadPixelOffset(domNode, lineNumber, column, context);\n  }\n  _actualReadPixelOffset(domNode, lineNumber, column, context) {\n    if (this._characterMapping.length === 0) {\n      // This line has no content\n      const r = RangeUtil.readHorizontalRanges(this._getReadingTarget(domNode), 0, 0, 0, 0, context);\n      if (!r || r.length === 0) {\n        return -1;\n      }\n      return r[0].left;\n    }\n    if (column === this._characterMapping.length && this._isWhitespaceOnly && this._containsForeignElements === 0 /* ForeignElementType.None */) {\n      // This branch helps in the case of whitespace only lines which have a width set\n      return this.getWidth(context);\n    }\n    const domPosition = this._characterMapping.getDomPosition(column);\n    const r = RangeUtil.readHorizontalRanges(this._getReadingTarget(domNode), domPosition.partIndex, domPosition.charIndex, domPosition.partIndex, domPosition.charIndex, context);\n    if (!r || r.length === 0) {\n      return -1;\n    }\n    const result = r[0].left;\n    if (this.input.isBasicASCII) {\n      const horizontalOffset = this._characterMapping.getHorizontalOffset(column);\n      const expectedResult = Math.round(this.input.spaceWidth * horizontalOffset);\n      if (Math.abs(expectedResult - result) <= 1) {\n        return expectedResult;\n      }\n    }\n    return result;\n  }\n  _readRawVisibleRangesForRange(domNode, startColumn, endColumn, context) {\n    if (startColumn === 1 && endColumn === this._characterMapping.length) {\n      // This branch helps IE with bidi text & gives a performance boost to other browsers when reading visible ranges for an entire line\n      return [new FloatHorizontalRange(0, this.getWidth(context))];\n    }\n    const startDomPosition = this._characterMapping.getDomPosition(startColumn);\n    const endDomPosition = this._characterMapping.getDomPosition(endColumn);\n    return RangeUtil.readHorizontalRanges(this._getReadingTarget(domNode), startDomPosition.partIndex, startDomPosition.charIndex, endDomPosition.partIndex, endDomPosition.charIndex, context);\n  }\n  /**\n   * Returns the column for the text found at a specific offset inside a rendered dom node\n   */\n  getColumnOfNodeOffset(spanNode, offset) {\n    return getColumnOfNodeOffset(this._characterMapping, spanNode, offset);\n  }\n}\nclass WebKitRenderedViewLine extends RenderedViewLine {\n  _readVisibleRangesForRange(domNode, lineNumber, startColumn, endColumn, context) {\n    const output = super._readVisibleRangesForRange(domNode, lineNumber, startColumn, endColumn, context);\n    if (!output || output.length === 0 || startColumn === endColumn || startColumn === 1 && endColumn === this._characterMapping.length) {\n      return output;\n    }\n    // WebKit is buggy and returns an expanded range (to contain words in some cases)\n    // The last client rect is enlarged (I think)\n    if (!this.input.containsRTL) {\n      // This is an attempt to patch things up\n      // Find position of last column\n      const endPixelOffset = this._readPixelOffset(domNode, lineNumber, endColumn, context);\n      if (endPixelOffset !== -1) {\n        const lastRange = output[output.length - 1];\n        if (lastRange.left < endPixelOffset) {\n          // Trim down the width of the last visible range to not go after the last column's position\n          lastRange.width = endPixelOffset - lastRange.left;\n        }\n      }\n    }\n    return output;\n  }\n}\nconst createRenderedLine = function () {\n  if (browser.isWebKit) {\n    return createWebKitRenderedLine;\n  }\n  return createNormalRenderedLine;\n}();\nfunction createWebKitRenderedLine(domNode, renderLineInput, characterMapping, containsRTL, containsForeignElements) {\n  return new WebKitRenderedViewLine(domNode, renderLineInput, characterMapping, containsRTL, containsForeignElements);\n}\nfunction createNormalRenderedLine(domNode, renderLineInput, characterMapping, containsRTL, containsForeignElements) {\n  return new RenderedViewLine(domNode, renderLineInput, characterMapping, containsRTL, containsForeignElements);\n}\nexport function getColumnOfNodeOffset(characterMapping, spanNode, offset) {\n  const spanNodeTextContentLength = spanNode.textContent.length;\n  let spanIndex = -1;\n  while (spanNode) {\n    spanNode = spanNode.previousSibling;\n    spanIndex++;\n  }\n  return characterMapping.getColumn(new DomPosition(spanIndex, offset), spanNodeTextContentLength);\n}", "map": {"version": 3, "names": ["browser", "createFastDomNode", "platform", "RangeUtil", "FloatHorizontalRange", "VisibleRanges", "LineDecoration", "RenderLineInput", "renderViewLine", "LineRange", "DomPosition", "isHighContrast", "EditorFontLigatures", "canUseFastRenderedViewLine", "isNative", "isLinux", "isFirefox", "<PERSON><PERSON><PERSON><PERSON>", "monospaceAssumptionsAreValid", "ViewLineOptions", "constructor", "config", "themeType", "options", "fontInfo", "get", "experimentalWhitespaceRendering", "renderWhitespace", "renderControlCharacters", "spaceWidth", "middotWidth", "wsmid<PERSON>t<PERSON><PERSON>th", "useMonospaceOptimizations", "isMonospace", "canUseHalfwidthRightwardsArrow", "lineHeight", "stopRenderingLineAfter", "fontLigatures", "equals", "other", "ViewLine", "CLASS_NAME", "_options", "_isMaybeInvalid", "_renderedViewLine", "getDomNode", "domNode", "setDomNode", "Error", "onContentChanged", "onTokensChanged", "onDecorationsChanged", "onOptionsChanged", "newOptions", "onSelectionChanged", "renderLine", "lineNumber", "deltaTop", "viewportData", "sb", "lineData", "getViewLineRenderingData", "actualInlineDecorations", "filter", "inlineDecorations", "minColumn", "maxColumn", "selectionsOnLine", "selections", "selection", "endLineNumber", "startLineNumber", "startColumn", "endColumn", "push", "renderLineInput", "content", "continuesWithWrappedLine", "isBasicASCII", "containsRTL", "tokens", "tabSize", "startVisibleColumn", "OFF", "input", "appendString", "String", "output", "renderedViewLine", "containsForeignElements", "FastRenderedViewLine", "characterMapping", "createRenderedLine", "layoutLine", "setTop", "setHeight", "getWidth", "context", "getWidthIsFast", "needsMonospaceFontCheck", "onMonospaceAssumptionsInvalidated", "toSlowRenderedLine", "getVisibleRangesForRange", "Math", "min", "lineContent", "length", "max", "horizontalRanges", "getColumnOfNodeOffset", "spanNode", "offset", "_cachedWidth", "keyColumnCount", "floor", "_keyColumnPixelOffsetCache", "Float32Array", "i", "_characterMapping", "_char<PERSON>idth", "horizontalOffset", "getHorizontalOffset", "round", "_getReadingTarget", "offsetWidth", "markDidDomLayout", "expectedWidth", "actualWidth", "<PERSON><PERSON><PERSON><PERSON>", "abs", "console", "warn", "startPosition", "_getColumnPixelOffset", "endPosition", "column", "keyColumnOrdinal", "keyColumn", "keyColumnPixelOffset", "_actualReadPixelOffset", "keyColumnHorizontalOffset", "myDomNode", "domPosition", "getDomPosition", "r", "readHorizontalRanges", "partIndex", "charIndex", "left", "RenderedViewLine", "_isWhitespaceOnly", "test", "_containsForeignElements", "_pixelOffsetCache", "len", "startOffset", "_readPixelOffset", "endOffset", "_readVisibleRangesForRange", "pixelOffset", "_readRawVisibleRangesForRange", "reading<PERSON>arget", "cachedPixelOffset", "result", "expectedResult", "startDomPosition", "endDomPosition", "WebKitRenderedViewLine", "endPixelOffset", "<PERSON><PERSON><PERSON><PERSON>", "width", "isWebKit", "createWebKitRenderedLine", "createNormalRenderedLine", "spanNodeTextContentLength", "textContent", "spanIndex", "previousSibling", "getColumn"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/browser/viewParts/lines/viewLine.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as browser from '../../../../base/browser/browser.js';\nimport { createFastDomNode } from '../../../../base/browser/fastDomNode.js';\nimport * as platform from '../../../../base/common/platform.js';\nimport { RangeUtil } from './rangeUtil.js';\nimport { Float<PERSON>orizontalR<PERSON><PERSON>, VisibleRanges } from '../../view/renderingContext.js';\nimport { LineDecoration } from '../../../common/viewLayout/lineDecorations.js';\nimport { RenderLineInput, renderViewLine, LineRange, DomPosition } from '../../../common/viewLayout/viewLineRenderer.js';\nimport { isHighContrast } from '../../../../platform/theme/common/theme.js';\nimport { EditorFontLigatures } from '../../../common/config/editorOptions.js';\nconst canUseFastRenderedViewLine = (function () {\n    if (platform.isNative) {\n        // In VSCode we know very well when the zoom level changes\n        return true;\n    }\n    if (platform.isLinux || browser.isFirefox || browser.isSafari) {\n        // On Linux, it appears that zooming affects char widths (in pixels), which is unexpected.\n        // --\n        // Even though we read character widths correctly, having read them at a specific zoom level\n        // does not mean they are the same at the current zoom level.\n        // --\n        // This could be improved if we ever figure out how to get an event when browsers zoom,\n        // but until then we have to stick with reading client rects.\n        // --\n        // The same has been observed with Firefox on Windows7\n        // --\n        // The same has been oversved with Safari\n        return false;\n    }\n    return true;\n})();\nlet monospaceAssumptionsAreValid = true;\nexport class ViewLineOptions {\n    constructor(config, themeType) {\n        this.themeType = themeType;\n        const options = config.options;\n        const fontInfo = options.get(50 /* EditorOption.fontInfo */);\n        const experimentalWhitespaceRendering = options.get(38 /* EditorOption.experimentalWhitespaceRendering */);\n        if (experimentalWhitespaceRendering === 'off') {\n            this.renderWhitespace = options.get(100 /* EditorOption.renderWhitespace */);\n        }\n        else {\n            // whitespace is rendered in a different layer\n            this.renderWhitespace = 'none';\n        }\n        this.renderControlCharacters = options.get(95 /* EditorOption.renderControlCharacters */);\n        this.spaceWidth = fontInfo.spaceWidth;\n        this.middotWidth = fontInfo.middotWidth;\n        this.wsmiddotWidth = fontInfo.wsmiddotWidth;\n        this.useMonospaceOptimizations = (fontInfo.isMonospace\n            && !options.get(33 /* EditorOption.disableMonospaceOptimizations */));\n        this.canUseHalfwidthRightwardsArrow = fontInfo.canUseHalfwidthRightwardsArrow;\n        this.lineHeight = options.get(67 /* EditorOption.lineHeight */);\n        this.stopRenderingLineAfter = options.get(118 /* EditorOption.stopRenderingLineAfter */);\n        this.fontLigatures = options.get(51 /* EditorOption.fontLigatures */);\n    }\n    equals(other) {\n        return (this.themeType === other.themeType\n            && this.renderWhitespace === other.renderWhitespace\n            && this.renderControlCharacters === other.renderControlCharacters\n            && this.spaceWidth === other.spaceWidth\n            && this.middotWidth === other.middotWidth\n            && this.wsmiddotWidth === other.wsmiddotWidth\n            && this.useMonospaceOptimizations === other.useMonospaceOptimizations\n            && this.canUseHalfwidthRightwardsArrow === other.canUseHalfwidthRightwardsArrow\n            && this.lineHeight === other.lineHeight\n            && this.stopRenderingLineAfter === other.stopRenderingLineAfter\n            && this.fontLigatures === other.fontLigatures);\n    }\n}\nexport class ViewLine {\n    static { this.CLASS_NAME = 'view-line'; }\n    constructor(options) {\n        this._options = options;\n        this._isMaybeInvalid = true;\n        this._renderedViewLine = null;\n    }\n    // --- begin IVisibleLineData\n    getDomNode() {\n        if (this._renderedViewLine && this._renderedViewLine.domNode) {\n            return this._renderedViewLine.domNode.domNode;\n        }\n        return null;\n    }\n    setDomNode(domNode) {\n        if (this._renderedViewLine) {\n            this._renderedViewLine.domNode = createFastDomNode(domNode);\n        }\n        else {\n            throw new Error('I have no rendered view line to set the dom node to...');\n        }\n    }\n    onContentChanged() {\n        this._isMaybeInvalid = true;\n    }\n    onTokensChanged() {\n        this._isMaybeInvalid = true;\n    }\n    onDecorationsChanged() {\n        this._isMaybeInvalid = true;\n    }\n    onOptionsChanged(newOptions) {\n        this._isMaybeInvalid = true;\n        this._options = newOptions;\n    }\n    onSelectionChanged() {\n        if (isHighContrast(this._options.themeType) || this._options.renderWhitespace === 'selection') {\n            this._isMaybeInvalid = true;\n            return true;\n        }\n        return false;\n    }\n    renderLine(lineNumber, deltaTop, lineHeight, viewportData, sb) {\n        if (this._isMaybeInvalid === false) {\n            // it appears that nothing relevant has changed\n            return false;\n        }\n        this._isMaybeInvalid = false;\n        const lineData = viewportData.getViewLineRenderingData(lineNumber);\n        const options = this._options;\n        const actualInlineDecorations = LineDecoration.filter(lineData.inlineDecorations, lineNumber, lineData.minColumn, lineData.maxColumn);\n        // Only send selection information when needed for rendering whitespace\n        let selectionsOnLine = null;\n        if (isHighContrast(options.themeType) || this._options.renderWhitespace === 'selection') {\n            const selections = viewportData.selections;\n            for (const selection of selections) {\n                if (selection.endLineNumber < lineNumber || selection.startLineNumber > lineNumber) {\n                    // Selection does not intersect line\n                    continue;\n                }\n                const startColumn = (selection.startLineNumber === lineNumber ? selection.startColumn : lineData.minColumn);\n                const endColumn = (selection.endLineNumber === lineNumber ? selection.endColumn : lineData.maxColumn);\n                if (startColumn < endColumn) {\n                    if (isHighContrast(options.themeType)) {\n                        actualInlineDecorations.push(new LineDecoration(startColumn, endColumn, 'inline-selected-text', 0 /* InlineDecorationType.Regular */));\n                    }\n                    if (this._options.renderWhitespace === 'selection') {\n                        if (!selectionsOnLine) {\n                            selectionsOnLine = [];\n                        }\n                        selectionsOnLine.push(new LineRange(startColumn - 1, endColumn - 1));\n                    }\n                }\n            }\n        }\n        const renderLineInput = new RenderLineInput(options.useMonospaceOptimizations, options.canUseHalfwidthRightwardsArrow, lineData.content, lineData.continuesWithWrappedLine, lineData.isBasicASCII, lineData.containsRTL, lineData.minColumn - 1, lineData.tokens, actualInlineDecorations, lineData.tabSize, lineData.startVisibleColumn, options.spaceWidth, options.middotWidth, options.wsmiddotWidth, options.stopRenderingLineAfter, options.renderWhitespace, options.renderControlCharacters, options.fontLigatures !== EditorFontLigatures.OFF, selectionsOnLine);\n        if (this._renderedViewLine && this._renderedViewLine.input.equals(renderLineInput)) {\n            // no need to do anything, we have the same render input\n            return false;\n        }\n        sb.appendString('<div style=\"top:');\n        sb.appendString(String(deltaTop));\n        sb.appendString('px;height:');\n        sb.appendString(String(lineHeight));\n        sb.appendString('px;\" class=\"');\n        sb.appendString(ViewLine.CLASS_NAME);\n        sb.appendString('\">');\n        const output = renderViewLine(renderLineInput, sb);\n        sb.appendString('</div>');\n        let renderedViewLine = null;\n        if (monospaceAssumptionsAreValid && canUseFastRenderedViewLine && lineData.isBasicASCII && options.useMonospaceOptimizations && output.containsForeignElements === 0 /* ForeignElementType.None */) {\n            renderedViewLine = new FastRenderedViewLine(this._renderedViewLine ? this._renderedViewLine.domNode : null, renderLineInput, output.characterMapping);\n        }\n        if (!renderedViewLine) {\n            renderedViewLine = createRenderedLine(this._renderedViewLine ? this._renderedViewLine.domNode : null, renderLineInput, output.characterMapping, output.containsRTL, output.containsForeignElements);\n        }\n        this._renderedViewLine = renderedViewLine;\n        return true;\n    }\n    layoutLine(lineNumber, deltaTop, lineHeight) {\n        if (this._renderedViewLine && this._renderedViewLine.domNode) {\n            this._renderedViewLine.domNode.setTop(deltaTop);\n            this._renderedViewLine.domNode.setHeight(lineHeight);\n        }\n    }\n    // --- end IVisibleLineData\n    getWidth(context) {\n        if (!this._renderedViewLine) {\n            return 0;\n        }\n        return this._renderedViewLine.getWidth(context);\n    }\n    getWidthIsFast() {\n        if (!this._renderedViewLine) {\n            return true;\n        }\n        return this._renderedViewLine.getWidthIsFast();\n    }\n    needsMonospaceFontCheck() {\n        if (!this._renderedViewLine) {\n            return false;\n        }\n        return (this._renderedViewLine instanceof FastRenderedViewLine);\n    }\n    monospaceAssumptionsAreValid() {\n        if (!this._renderedViewLine) {\n            return monospaceAssumptionsAreValid;\n        }\n        if (this._renderedViewLine instanceof FastRenderedViewLine) {\n            return this._renderedViewLine.monospaceAssumptionsAreValid();\n        }\n        return monospaceAssumptionsAreValid;\n    }\n    onMonospaceAssumptionsInvalidated() {\n        if (this._renderedViewLine && this._renderedViewLine instanceof FastRenderedViewLine) {\n            this._renderedViewLine = this._renderedViewLine.toSlowRenderedLine();\n        }\n    }\n    getVisibleRangesForRange(lineNumber, startColumn, endColumn, context) {\n        if (!this._renderedViewLine) {\n            return null;\n        }\n        startColumn = Math.min(this._renderedViewLine.input.lineContent.length + 1, Math.max(1, startColumn));\n        endColumn = Math.min(this._renderedViewLine.input.lineContent.length + 1, Math.max(1, endColumn));\n        const stopRenderingLineAfter = this._renderedViewLine.input.stopRenderingLineAfter;\n        if (stopRenderingLineAfter !== -1 && startColumn > stopRenderingLineAfter + 1 && endColumn > stopRenderingLineAfter + 1) {\n            // This range is obviously not visible\n            return new VisibleRanges(true, [new FloatHorizontalRange(this.getWidth(context), 0)]);\n        }\n        if (stopRenderingLineAfter !== -1 && startColumn > stopRenderingLineAfter + 1) {\n            startColumn = stopRenderingLineAfter + 1;\n        }\n        if (stopRenderingLineAfter !== -1 && endColumn > stopRenderingLineAfter + 1) {\n            endColumn = stopRenderingLineAfter + 1;\n        }\n        const horizontalRanges = this._renderedViewLine.getVisibleRangesForRange(lineNumber, startColumn, endColumn, context);\n        if (horizontalRanges && horizontalRanges.length > 0) {\n            return new VisibleRanges(false, horizontalRanges);\n        }\n        return null;\n    }\n    getColumnOfNodeOffset(spanNode, offset) {\n        if (!this._renderedViewLine) {\n            return 1;\n        }\n        return this._renderedViewLine.getColumnOfNodeOffset(spanNode, offset);\n    }\n}\n/**\n * A rendered line which is guaranteed to contain only regular ASCII and is rendered with a monospace font.\n */\nclass FastRenderedViewLine {\n    constructor(domNode, renderLineInput, characterMapping) {\n        this._cachedWidth = -1;\n        this.domNode = domNode;\n        this.input = renderLineInput;\n        const keyColumnCount = Math.floor(renderLineInput.lineContent.length / 300 /* Constants.MaxMonospaceDistance */);\n        if (keyColumnCount > 0) {\n            this._keyColumnPixelOffsetCache = new Float32Array(keyColumnCount);\n            for (let i = 0; i < keyColumnCount; i++) {\n                this._keyColumnPixelOffsetCache[i] = -1;\n            }\n        }\n        else {\n            this._keyColumnPixelOffsetCache = null;\n        }\n        this._characterMapping = characterMapping;\n        this._charWidth = renderLineInput.spaceWidth;\n    }\n    getWidth(context) {\n        if (!this.domNode || this.input.lineContent.length < 300 /* Constants.MaxMonospaceDistance */) {\n            const horizontalOffset = this._characterMapping.getHorizontalOffset(this._characterMapping.length);\n            return Math.round(this._charWidth * horizontalOffset);\n        }\n        if (this._cachedWidth === -1) {\n            this._cachedWidth = this._getReadingTarget(this.domNode).offsetWidth;\n            context?.markDidDomLayout();\n        }\n        return this._cachedWidth;\n    }\n    getWidthIsFast() {\n        return (this.input.lineContent.length < 300 /* Constants.MaxMonospaceDistance */) || this._cachedWidth !== -1;\n    }\n    monospaceAssumptionsAreValid() {\n        if (!this.domNode) {\n            return monospaceAssumptionsAreValid;\n        }\n        if (this.input.lineContent.length < 300 /* Constants.MaxMonospaceDistance */) {\n            const expectedWidth = this.getWidth(null);\n            const actualWidth = this.domNode.domNode.firstChild.offsetWidth;\n            if (Math.abs(expectedWidth - actualWidth) >= 2) {\n                // more than 2px off\n                console.warn(`monospace assumptions have been violated, therefore disabling monospace optimizations!`);\n                monospaceAssumptionsAreValid = false;\n            }\n        }\n        return monospaceAssumptionsAreValid;\n    }\n    toSlowRenderedLine() {\n        return createRenderedLine(this.domNode, this.input, this._characterMapping, false, 0 /* ForeignElementType.None */);\n    }\n    getVisibleRangesForRange(lineNumber, startColumn, endColumn, context) {\n        const startPosition = this._getColumnPixelOffset(lineNumber, startColumn, context);\n        const endPosition = this._getColumnPixelOffset(lineNumber, endColumn, context);\n        return [new FloatHorizontalRange(startPosition, endPosition - startPosition)];\n    }\n    _getColumnPixelOffset(lineNumber, column, context) {\n        if (column <= 300 /* Constants.MaxMonospaceDistance */) {\n            const horizontalOffset = this._characterMapping.getHorizontalOffset(column);\n            return this._charWidth * horizontalOffset;\n        }\n        const keyColumnOrdinal = Math.floor((column - 1) / 300 /* Constants.MaxMonospaceDistance */) - 1;\n        const keyColumn = (keyColumnOrdinal + 1) * 300 /* Constants.MaxMonospaceDistance */ + 1;\n        let keyColumnPixelOffset = -1;\n        if (this._keyColumnPixelOffsetCache) {\n            keyColumnPixelOffset = this._keyColumnPixelOffsetCache[keyColumnOrdinal];\n            if (keyColumnPixelOffset === -1) {\n                keyColumnPixelOffset = this._actualReadPixelOffset(lineNumber, keyColumn, context);\n                this._keyColumnPixelOffsetCache[keyColumnOrdinal] = keyColumnPixelOffset;\n            }\n        }\n        if (keyColumnPixelOffset === -1) {\n            // Could not read actual key column pixel offset\n            const horizontalOffset = this._characterMapping.getHorizontalOffset(column);\n            return this._charWidth * horizontalOffset;\n        }\n        const keyColumnHorizontalOffset = this._characterMapping.getHorizontalOffset(keyColumn);\n        const horizontalOffset = this._characterMapping.getHorizontalOffset(column);\n        return keyColumnPixelOffset + this._charWidth * (horizontalOffset - keyColumnHorizontalOffset);\n    }\n    _getReadingTarget(myDomNode) {\n        return myDomNode.domNode.firstChild;\n    }\n    _actualReadPixelOffset(lineNumber, column, context) {\n        if (!this.domNode) {\n            return -1;\n        }\n        const domPosition = this._characterMapping.getDomPosition(column);\n        const r = RangeUtil.readHorizontalRanges(this._getReadingTarget(this.domNode), domPosition.partIndex, domPosition.charIndex, domPosition.partIndex, domPosition.charIndex, context);\n        if (!r || r.length === 0) {\n            return -1;\n        }\n        return r[0].left;\n    }\n    getColumnOfNodeOffset(spanNode, offset) {\n        return getColumnOfNodeOffset(this._characterMapping, spanNode, offset);\n    }\n}\n/**\n * Every time we render a line, we save what we have rendered in an instance of this class.\n */\nclass RenderedViewLine {\n    constructor(domNode, renderLineInput, characterMapping, containsRTL, containsForeignElements) {\n        this.domNode = domNode;\n        this.input = renderLineInput;\n        this._characterMapping = characterMapping;\n        this._isWhitespaceOnly = /^\\s*$/.test(renderLineInput.lineContent);\n        this._containsForeignElements = containsForeignElements;\n        this._cachedWidth = -1;\n        this._pixelOffsetCache = null;\n        if (!containsRTL || this._characterMapping.length === 0 /* the line is empty */) {\n            this._pixelOffsetCache = new Float32Array(Math.max(2, this._characterMapping.length + 1));\n            for (let column = 0, len = this._characterMapping.length; column <= len; column++) {\n                this._pixelOffsetCache[column] = -1;\n            }\n        }\n    }\n    // --- Reading from the DOM methods\n    _getReadingTarget(myDomNode) {\n        return myDomNode.domNode.firstChild;\n    }\n    /**\n     * Width of the line in pixels\n     */\n    getWidth(context) {\n        if (!this.domNode) {\n            return 0;\n        }\n        if (this._cachedWidth === -1) {\n            this._cachedWidth = this._getReadingTarget(this.domNode).offsetWidth;\n            context?.markDidDomLayout();\n        }\n        return this._cachedWidth;\n    }\n    getWidthIsFast() {\n        if (this._cachedWidth === -1) {\n            return false;\n        }\n        return true;\n    }\n    /**\n     * Visible ranges for a model range\n     */\n    getVisibleRangesForRange(lineNumber, startColumn, endColumn, context) {\n        if (!this.domNode) {\n            return null;\n        }\n        if (this._pixelOffsetCache !== null) {\n            // the text is LTR\n            const startOffset = this._readPixelOffset(this.domNode, lineNumber, startColumn, context);\n            if (startOffset === -1) {\n                return null;\n            }\n            const endOffset = this._readPixelOffset(this.domNode, lineNumber, endColumn, context);\n            if (endOffset === -1) {\n                return null;\n            }\n            return [new FloatHorizontalRange(startOffset, endOffset - startOffset)];\n        }\n        return this._readVisibleRangesForRange(this.domNode, lineNumber, startColumn, endColumn, context);\n    }\n    _readVisibleRangesForRange(domNode, lineNumber, startColumn, endColumn, context) {\n        if (startColumn === endColumn) {\n            const pixelOffset = this._readPixelOffset(domNode, lineNumber, startColumn, context);\n            if (pixelOffset === -1) {\n                return null;\n            }\n            else {\n                return [new FloatHorizontalRange(pixelOffset, 0)];\n            }\n        }\n        else {\n            return this._readRawVisibleRangesForRange(domNode, startColumn, endColumn, context);\n        }\n    }\n    _readPixelOffset(domNode, lineNumber, column, context) {\n        if (this._characterMapping.length === 0) {\n            // This line has no content\n            if (this._containsForeignElements === 0 /* ForeignElementType.None */) {\n                // We can assume the line is really empty\n                return 0;\n            }\n            if (this._containsForeignElements === 2 /* ForeignElementType.After */) {\n                // We have foreign elements after the (empty) line\n                return 0;\n            }\n            if (this._containsForeignElements === 1 /* ForeignElementType.Before */) {\n                // We have foreign elements before the (empty) line\n                return this.getWidth(context);\n            }\n            // We have foreign elements before & after the (empty) line\n            const readingTarget = this._getReadingTarget(domNode);\n            if (readingTarget.firstChild) {\n                context.markDidDomLayout();\n                return readingTarget.firstChild.offsetWidth;\n            }\n            else {\n                return 0;\n            }\n        }\n        if (this._pixelOffsetCache !== null) {\n            // the text is LTR\n            const cachedPixelOffset = this._pixelOffsetCache[column];\n            if (cachedPixelOffset !== -1) {\n                return cachedPixelOffset;\n            }\n            const result = this._actualReadPixelOffset(domNode, lineNumber, column, context);\n            this._pixelOffsetCache[column] = result;\n            return result;\n        }\n        return this._actualReadPixelOffset(domNode, lineNumber, column, context);\n    }\n    _actualReadPixelOffset(domNode, lineNumber, column, context) {\n        if (this._characterMapping.length === 0) {\n            // This line has no content\n            const r = RangeUtil.readHorizontalRanges(this._getReadingTarget(domNode), 0, 0, 0, 0, context);\n            if (!r || r.length === 0) {\n                return -1;\n            }\n            return r[0].left;\n        }\n        if (column === this._characterMapping.length && this._isWhitespaceOnly && this._containsForeignElements === 0 /* ForeignElementType.None */) {\n            // This branch helps in the case of whitespace only lines which have a width set\n            return this.getWidth(context);\n        }\n        const domPosition = this._characterMapping.getDomPosition(column);\n        const r = RangeUtil.readHorizontalRanges(this._getReadingTarget(domNode), domPosition.partIndex, domPosition.charIndex, domPosition.partIndex, domPosition.charIndex, context);\n        if (!r || r.length === 0) {\n            return -1;\n        }\n        const result = r[0].left;\n        if (this.input.isBasicASCII) {\n            const horizontalOffset = this._characterMapping.getHorizontalOffset(column);\n            const expectedResult = Math.round(this.input.spaceWidth * horizontalOffset);\n            if (Math.abs(expectedResult - result) <= 1) {\n                return expectedResult;\n            }\n        }\n        return result;\n    }\n    _readRawVisibleRangesForRange(domNode, startColumn, endColumn, context) {\n        if (startColumn === 1 && endColumn === this._characterMapping.length) {\n            // This branch helps IE with bidi text & gives a performance boost to other browsers when reading visible ranges for an entire line\n            return [new FloatHorizontalRange(0, this.getWidth(context))];\n        }\n        const startDomPosition = this._characterMapping.getDomPosition(startColumn);\n        const endDomPosition = this._characterMapping.getDomPosition(endColumn);\n        return RangeUtil.readHorizontalRanges(this._getReadingTarget(domNode), startDomPosition.partIndex, startDomPosition.charIndex, endDomPosition.partIndex, endDomPosition.charIndex, context);\n    }\n    /**\n     * Returns the column for the text found at a specific offset inside a rendered dom node\n     */\n    getColumnOfNodeOffset(spanNode, offset) {\n        return getColumnOfNodeOffset(this._characterMapping, spanNode, offset);\n    }\n}\nclass WebKitRenderedViewLine extends RenderedViewLine {\n    _readVisibleRangesForRange(domNode, lineNumber, startColumn, endColumn, context) {\n        const output = super._readVisibleRangesForRange(domNode, lineNumber, startColumn, endColumn, context);\n        if (!output || output.length === 0 || startColumn === endColumn || (startColumn === 1 && endColumn === this._characterMapping.length)) {\n            return output;\n        }\n        // WebKit is buggy and returns an expanded range (to contain words in some cases)\n        // The last client rect is enlarged (I think)\n        if (!this.input.containsRTL) {\n            // This is an attempt to patch things up\n            // Find position of last column\n            const endPixelOffset = this._readPixelOffset(domNode, lineNumber, endColumn, context);\n            if (endPixelOffset !== -1) {\n                const lastRange = output[output.length - 1];\n                if (lastRange.left < endPixelOffset) {\n                    // Trim down the width of the last visible range to not go after the last column's position\n                    lastRange.width = endPixelOffset - lastRange.left;\n                }\n            }\n        }\n        return output;\n    }\n}\nconst createRenderedLine = (function () {\n    if (browser.isWebKit) {\n        return createWebKitRenderedLine;\n    }\n    return createNormalRenderedLine;\n})();\nfunction createWebKitRenderedLine(domNode, renderLineInput, characterMapping, containsRTL, containsForeignElements) {\n    return new WebKitRenderedViewLine(domNode, renderLineInput, characterMapping, containsRTL, containsForeignElements);\n}\nfunction createNormalRenderedLine(domNode, renderLineInput, characterMapping, containsRTL, containsForeignElements) {\n    return new RenderedViewLine(domNode, renderLineInput, characterMapping, containsRTL, containsForeignElements);\n}\nexport function getColumnOfNodeOffset(characterMapping, spanNode, offset) {\n    const spanNodeTextContentLength = spanNode.textContent.length;\n    let spanIndex = -1;\n    while (spanNode) {\n        spanNode = spanNode.previousSibling;\n        spanIndex++;\n    }\n    return characterMapping.getColumn(new DomPosition(spanIndex, offset), spanNodeTextContentLength);\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,KAAKA,OAAO,MAAM,qCAAqC;AAC9D,SAASC,iBAAiB,QAAQ,yCAAyC;AAC3E,OAAO,KAAKC,QAAQ,MAAM,qCAAqC;AAC/D,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,oBAAoB,EAAEC,aAAa,QAAQ,gCAAgC;AACpF,SAASC,cAAc,QAAQ,+CAA+C;AAC9E,SAASC,eAAe,EAAEC,cAAc,EAAEC,SAAS,EAAEC,WAAW,QAAQ,gDAAgD;AACxH,SAASC,cAAc,QAAQ,4CAA4C;AAC3E,SAASC,mBAAmB,QAAQ,yCAAyC;AAC7E,MAAMC,0BAA0B,GAAI,YAAY;EAC5C,IAAIX,QAAQ,CAACY,QAAQ,EAAE;IACnB;IACA,OAAO,IAAI;EACf;EACA,IAAIZ,QAAQ,CAACa,OAAO,IAAIf,OAAO,CAACgB,SAAS,IAAIhB,OAAO,CAACiB,QAAQ,EAAE;IAC3D;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,OAAO,KAAK;EAChB;EACA,OAAO,IAAI;AACf,CAAC,CAAE,CAAC;AACJ,IAAIC,4BAA4B,GAAG,IAAI;AACvC,OAAO,MAAMC,eAAe,CAAC;EACzBC,WAAWA,CAACC,MAAM,EAAEC,SAAS,EAAE;IAC3B,IAAI,CAACA,SAAS,GAAGA,SAAS;IAC1B,MAAMC,OAAO,GAAGF,MAAM,CAACE,OAAO;IAC9B,MAAMC,QAAQ,GAAGD,OAAO,CAACE,GAAG,CAAC,EAAE,CAAC,2BAA2B,CAAC;IAC5D,MAAMC,+BAA+B,GAAGH,OAAO,CAACE,GAAG,CAAC,EAAE,CAAC,kDAAkD,CAAC;IAC1G,IAAIC,+BAA+B,KAAK,KAAK,EAAE;MAC3C,IAAI,CAACC,gBAAgB,GAAGJ,OAAO,CAACE,GAAG,CAAC,GAAG,CAAC,mCAAmC,CAAC;IAChF,CAAC,MACI;MACD;MACA,IAAI,CAACE,gBAAgB,GAAG,MAAM;IAClC;IACA,IAAI,CAACC,uBAAuB,GAAGL,OAAO,CAACE,GAAG,CAAC,EAAE,CAAC,0CAA0C,CAAC;IACzF,IAAI,CAACI,UAAU,GAAGL,QAAQ,CAACK,UAAU;IACrC,IAAI,CAACC,WAAW,GAAGN,QAAQ,CAACM,WAAW;IACvC,IAAI,CAACC,aAAa,GAAGP,QAAQ,CAACO,aAAa;IAC3C,IAAI,CAACC,yBAAyB,GAAIR,QAAQ,CAACS,WAAW,IAC/C,CAACV,OAAO,CAACE,GAAG,CAAC,EAAE,CAAC,gDAAgD,CAAE;IACzE,IAAI,CAACS,8BAA8B,GAAGV,QAAQ,CAACU,8BAA8B;IAC7E,IAAI,CAACC,UAAU,GAAGZ,OAAO,CAACE,GAAG,CAAC,EAAE,CAAC,6BAA6B,CAAC;IAC/D,IAAI,CAACW,sBAAsB,GAAGb,OAAO,CAACE,GAAG,CAAC,GAAG,CAAC,yCAAyC,CAAC;IACxF,IAAI,CAACY,aAAa,GAAGd,OAAO,CAACE,GAAG,CAAC,EAAE,CAAC,gCAAgC,CAAC;EACzE;EACAa,MAAMA,CAACC,KAAK,EAAE;IACV,OAAQ,IAAI,CAACjB,SAAS,KAAKiB,KAAK,CAACjB,SAAS,IACnC,IAAI,CAACK,gBAAgB,KAAKY,KAAK,CAACZ,gBAAgB,IAChD,IAAI,CAACC,uBAAuB,KAAKW,KAAK,CAACX,uBAAuB,IAC9D,IAAI,CAACC,UAAU,KAAKU,KAAK,CAACV,UAAU,IACpC,IAAI,CAACC,WAAW,KAAKS,KAAK,CAACT,WAAW,IACtC,IAAI,CAACC,aAAa,KAAKQ,KAAK,CAACR,aAAa,IAC1C,IAAI,CAACC,yBAAyB,KAAKO,KAAK,CAACP,yBAAyB,IAClE,IAAI,CAACE,8BAA8B,KAAKK,KAAK,CAACL,8BAA8B,IAC5E,IAAI,CAACC,UAAU,KAAKI,KAAK,CAACJ,UAAU,IACpC,IAAI,CAACC,sBAAsB,KAAKG,KAAK,CAACH,sBAAsB,IAC5D,IAAI,CAACC,aAAa,KAAKE,KAAK,CAACF,aAAa;EACrD;AACJ;AACA,WAAaG,QAAQ;EAAd,MAAMA,QAAQ,CAAC;IAClB;MAAS,IAAI,CAACC,UAAU,GAAG,WAAW;IAAE;IACxCrB,WAAWA,CAACG,OAAO,EAAE;MACjB,IAAI,CAACmB,QAAQ,GAAGnB,OAAO;MACvB,IAAI,CAACoB,eAAe,GAAG,IAAI;MAC3B,IAAI,CAACC,iBAAiB,GAAG,IAAI;IACjC;IACA;IACAC,UAAUA,CAAA,EAAG;MACT,IAAI,IAAI,CAACD,iBAAiB,IAAI,IAAI,CAACA,iBAAiB,CAACE,OAAO,EAAE;QAC1D,OAAO,IAAI,CAACF,iBAAiB,CAACE,OAAO,CAACA,OAAO;MACjD;MACA,OAAO,IAAI;IACf;IACAC,UAAUA,CAACD,OAAO,EAAE;MAChB,IAAI,IAAI,CAACF,iBAAiB,EAAE;QACxB,IAAI,CAACA,iBAAiB,CAACE,OAAO,GAAG7C,iBAAiB,CAAC6C,OAAO,CAAC;MAC/D,CAAC,MACI;QACD,MAAM,IAAIE,KAAK,CAAC,wDAAwD,CAAC;MAC7E;IACJ;IACAC,gBAAgBA,CAAA,EAAG;MACf,IAAI,CAACN,eAAe,GAAG,IAAI;IAC/B;IACAO,eAAeA,CAAA,EAAG;MACd,IAAI,CAACP,eAAe,GAAG,IAAI;IAC/B;IACAQ,oBAAoBA,CAAA,EAAG;MACnB,IAAI,CAACR,eAAe,GAAG,IAAI;IAC/B;IACAS,gBAAgBA,CAACC,UAAU,EAAE;MACzB,IAAI,CAACV,eAAe,GAAG,IAAI;MAC3B,IAAI,CAACD,QAAQ,GAAGW,UAAU;IAC9B;IACAC,kBAAkBA,CAAA,EAAG;MACjB,IAAI3C,cAAc,CAAC,IAAI,CAAC+B,QAAQ,CAACpB,SAAS,CAAC,IAAI,IAAI,CAACoB,QAAQ,CAACf,gBAAgB,KAAK,WAAW,EAAE;QAC3F,IAAI,CAACgB,eAAe,GAAG,IAAI;QAC3B,OAAO,IAAI;MACf;MACA,OAAO,KAAK;IAChB;IACAY,UAAUA,CAACC,UAAU,EAAEC,QAAQ,EAAEtB,UAAU,EAAEuB,YAAY,EAAEC,EAAE,EAAE;MAC3D,IAAI,IAAI,CAAChB,eAAe,KAAK,KAAK,EAAE;QAChC;QACA,OAAO,KAAK;MAChB;MACA,IAAI,CAACA,eAAe,GAAG,KAAK;MAC5B,MAAMiB,QAAQ,GAAGF,YAAY,CAACG,wBAAwB,CAACL,UAAU,CAAC;MAClE,MAAMjC,OAAO,GAAG,IAAI,CAACmB,QAAQ;MAC7B,MAAMoB,uBAAuB,GAAGxD,cAAc,CAACyD,MAAM,CAACH,QAAQ,CAACI,iBAAiB,EAAER,UAAU,EAAEI,QAAQ,CAACK,SAAS,EAAEL,QAAQ,CAACM,SAAS,CAAC;MACrI;MACA,IAAIC,gBAAgB,GAAG,IAAI;MAC3B,IAAIxD,cAAc,CAACY,OAAO,CAACD,SAAS,CAAC,IAAI,IAAI,CAACoB,QAAQ,CAACf,gBAAgB,KAAK,WAAW,EAAE;QACrF,MAAMyC,UAAU,GAAGV,YAAY,CAACU,UAAU;QAC1C,KAAK,MAAMC,SAAS,IAAID,UAAU,EAAE;UAChC,IAAIC,SAAS,CAACC,aAAa,GAAGd,UAAU,IAAIa,SAAS,CAACE,eAAe,GAAGf,UAAU,EAAE;YAChF;YACA;UACJ;UACA,MAAMgB,WAAW,GAAIH,SAAS,CAACE,eAAe,KAAKf,UAAU,GAAGa,SAAS,CAACG,WAAW,GAAGZ,QAAQ,CAACK,SAAU;UAC3G,MAAMQ,SAAS,GAAIJ,SAAS,CAACC,aAAa,KAAKd,UAAU,GAAGa,SAAS,CAACI,SAAS,GAAGb,QAAQ,CAACM,SAAU;UACrG,IAAIM,WAAW,GAAGC,SAAS,EAAE;YACzB,IAAI9D,cAAc,CAACY,OAAO,CAACD,SAAS,CAAC,EAAE;cACnCwC,uBAAuB,CAACY,IAAI,CAAC,IAAIpE,cAAc,CAACkE,WAAW,EAAEC,SAAS,EAAE,sBAAsB,EAAE,CAAC,CAAC,kCAAkC,CAAC,CAAC;YAC1I;YACA,IAAI,IAAI,CAAC/B,QAAQ,CAACf,gBAAgB,KAAK,WAAW,EAAE;cAChD,IAAI,CAACwC,gBAAgB,EAAE;gBACnBA,gBAAgB,GAAG,EAAE;cACzB;cACAA,gBAAgB,CAACO,IAAI,CAAC,IAAIjE,SAAS,CAAC+D,WAAW,GAAG,CAAC,EAAEC,SAAS,GAAG,CAAC,CAAC,CAAC;YACxE;UACJ;QACJ;MACJ;MACA,MAAME,eAAe,GAAG,IAAIpE,eAAe,CAACgB,OAAO,CAACS,yBAAyB,EAAET,OAAO,CAACW,8BAA8B,EAAE0B,QAAQ,CAACgB,OAAO,EAAEhB,QAAQ,CAACiB,wBAAwB,EAAEjB,QAAQ,CAACkB,YAAY,EAAElB,QAAQ,CAACmB,WAAW,EAAEnB,QAAQ,CAACK,SAAS,GAAG,CAAC,EAAEL,QAAQ,CAACoB,MAAM,EAAElB,uBAAuB,EAAEF,QAAQ,CAACqB,OAAO,EAAErB,QAAQ,CAACsB,kBAAkB,EAAE3D,OAAO,CAACM,UAAU,EAAEN,OAAO,CAACO,WAAW,EAAEP,OAAO,CAACQ,aAAa,EAAER,OAAO,CAACa,sBAAsB,EAAEb,OAAO,CAACI,gBAAgB,EAAEJ,OAAO,CAACK,uBAAuB,EAAEL,OAAO,CAACc,aAAa,KAAKzB,mBAAmB,CAACuE,GAAG,EAAEhB,gBAAgB,CAAC;MACziB,IAAI,IAAI,CAACvB,iBAAiB,IAAI,IAAI,CAACA,iBAAiB,CAACwC,KAAK,CAAC9C,MAAM,CAACqC,eAAe,CAAC,EAAE;QAChF;QACA,OAAO,KAAK;MAChB;MACAhB,EAAE,CAAC0B,YAAY,CAAC,kBAAkB,CAAC;MACnC1B,EAAE,CAAC0B,YAAY,CAACC,MAAM,CAAC7B,QAAQ,CAAC,CAAC;MACjCE,EAAE,CAAC0B,YAAY,CAAC,YAAY,CAAC;MAC7B1B,EAAE,CAAC0B,YAAY,CAACC,MAAM,CAACnD,UAAU,CAAC,CAAC;MACnCwB,EAAE,CAAC0B,YAAY,CAAC,cAAc,CAAC;MAC/B1B,EAAE,CAAC0B,YAAY,CAAC7C,QAAQ,CAACC,UAAU,CAAC;MACpCkB,EAAE,CAAC0B,YAAY,CAAC,IAAI,CAAC;MACrB,MAAME,MAAM,GAAG/E,cAAc,CAACmE,eAAe,EAAEhB,EAAE,CAAC;MAClDA,EAAE,CAAC0B,YAAY,CAAC,QAAQ,CAAC;MACzB,IAAIG,gBAAgB,GAAG,IAAI;MAC3B,IAAItE,4BAA4B,IAAIL,0BAA0B,IAAI+C,QAAQ,CAACkB,YAAY,IAAIvD,OAAO,CAACS,yBAAyB,IAAIuD,MAAM,CAACE,uBAAuB,KAAK,CAAC,CAAC,+BAA+B;QAChMD,gBAAgB,GAAG,IAAIE,oBAAoB,CAAC,IAAI,CAAC9C,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACE,OAAO,GAAG,IAAI,EAAE6B,eAAe,EAAEY,MAAM,CAACI,gBAAgB,CAAC;MACzJ;MACA,IAAI,CAACH,gBAAgB,EAAE;QACnBA,gBAAgB,GAAGI,kBAAkB,CAAC,IAAI,CAAChD,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACE,OAAO,GAAG,IAAI,EAAE6B,eAAe,EAAEY,MAAM,CAACI,gBAAgB,EAAEJ,MAAM,CAACR,WAAW,EAAEQ,MAAM,CAACE,uBAAuB,CAAC;MACvM;MACA,IAAI,CAAC7C,iBAAiB,GAAG4C,gBAAgB;MACzC,OAAO,IAAI;IACf;IACAK,UAAUA,CAACrC,UAAU,EAAEC,QAAQ,EAAEtB,UAAU,EAAE;MACzC,IAAI,IAAI,CAACS,iBAAiB,IAAI,IAAI,CAACA,iBAAiB,CAACE,OAAO,EAAE;QAC1D,IAAI,CAACF,iBAAiB,CAACE,OAAO,CAACgD,MAAM,CAACrC,QAAQ,CAAC;QAC/C,IAAI,CAACb,iBAAiB,CAACE,OAAO,CAACiD,SAAS,CAAC5D,UAAU,CAAC;MACxD;IACJ;IACA;IACA6D,QAAQA,CAACC,OAAO,EAAE;MACd,IAAI,CAAC,IAAI,CAACrD,iBAAiB,EAAE;QACzB,OAAO,CAAC;MACZ;MACA,OAAO,IAAI,CAACA,iBAAiB,CAACoD,QAAQ,CAACC,OAAO,CAAC;IACnD;IACAC,cAAcA,CAAA,EAAG;MACb,IAAI,CAAC,IAAI,CAACtD,iBAAiB,EAAE;QACzB,OAAO,IAAI;MACf;MACA,OAAO,IAAI,CAACA,iBAAiB,CAACsD,cAAc,CAAC,CAAC;IAClD;IACAC,uBAAuBA,CAAA,EAAG;MACtB,IAAI,CAAC,IAAI,CAACvD,iBAAiB,EAAE;QACzB,OAAO,KAAK;MAChB;MACA,OAAQ,IAAI,CAACA,iBAAiB,YAAY8C,oBAAoB;IAClE;IACAxE,4BAA4BA,CAAA,EAAG;MAC3B,IAAI,CAAC,IAAI,CAAC0B,iBAAiB,EAAE;QACzB,OAAO1B,4BAA4B;MACvC;MACA,IAAI,IAAI,CAAC0B,iBAAiB,YAAY8C,oBAAoB,EAAE;QACxD,OAAO,IAAI,CAAC9C,iBAAiB,CAAC1B,4BAA4B,CAAC,CAAC;MAChE;MACA,OAAOA,4BAA4B;IACvC;IACAkF,iCAAiCA,CAAA,EAAG;MAChC,IAAI,IAAI,CAACxD,iBAAiB,IAAI,IAAI,CAACA,iBAAiB,YAAY8C,oBAAoB,EAAE;QAClF,IAAI,CAAC9C,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACyD,kBAAkB,CAAC,CAAC;MACxE;IACJ;IACAC,wBAAwBA,CAAC9C,UAAU,EAAEgB,WAAW,EAAEC,SAAS,EAAEwB,OAAO,EAAE;MAClE,IAAI,CAAC,IAAI,CAACrD,iBAAiB,EAAE;QACzB,OAAO,IAAI;MACf;MACA4B,WAAW,GAAG+B,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC5D,iBAAiB,CAACwC,KAAK,CAACqB,WAAW,CAACC,MAAM,GAAG,CAAC,EAAEH,IAAI,CAACI,GAAG,CAAC,CAAC,EAAEnC,WAAW,CAAC,CAAC;MACrGC,SAAS,GAAG8B,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC5D,iBAAiB,CAACwC,KAAK,CAACqB,WAAW,CAACC,MAAM,GAAG,CAAC,EAAEH,IAAI,CAACI,GAAG,CAAC,CAAC,EAAElC,SAAS,CAAC,CAAC;MACjG,MAAMrC,sBAAsB,GAAG,IAAI,CAACQ,iBAAiB,CAACwC,KAAK,CAAChD,sBAAsB;MAClF,IAAIA,sBAAsB,KAAK,CAAC,CAAC,IAAIoC,WAAW,GAAGpC,sBAAsB,GAAG,CAAC,IAAIqC,SAAS,GAAGrC,sBAAsB,GAAG,CAAC,EAAE;QACrH;QACA,OAAO,IAAI/B,aAAa,CAAC,IAAI,EAAE,CAAC,IAAID,oBAAoB,CAAC,IAAI,CAAC4F,QAAQ,CAACC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACzF;MACA,IAAI7D,sBAAsB,KAAK,CAAC,CAAC,IAAIoC,WAAW,GAAGpC,sBAAsB,GAAG,CAAC,EAAE;QAC3EoC,WAAW,GAAGpC,sBAAsB,GAAG,CAAC;MAC5C;MACA,IAAIA,sBAAsB,KAAK,CAAC,CAAC,IAAIqC,SAAS,GAAGrC,sBAAsB,GAAG,CAAC,EAAE;QACzEqC,SAAS,GAAGrC,sBAAsB,GAAG,CAAC;MAC1C;MACA,MAAMwE,gBAAgB,GAAG,IAAI,CAAChE,iBAAiB,CAAC0D,wBAAwB,CAAC9C,UAAU,EAAEgB,WAAW,EAAEC,SAAS,EAAEwB,OAAO,CAAC;MACrH,IAAIW,gBAAgB,IAAIA,gBAAgB,CAACF,MAAM,GAAG,CAAC,EAAE;QACjD,OAAO,IAAIrG,aAAa,CAAC,KAAK,EAAEuG,gBAAgB,CAAC;MACrD;MACA,OAAO,IAAI;IACf;IACAC,qBAAqBA,CAACC,QAAQ,EAAEC,MAAM,EAAE;MACpC,IAAI,CAAC,IAAI,CAACnE,iBAAiB,EAAE;QACzB,OAAO,CAAC;MACZ;MACA,OAAO,IAAI,CAACA,iBAAiB,CAACiE,qBAAqB,CAACC,QAAQ,EAAEC,MAAM,CAAC;IACzE;EACJ;EAAC,OAvKYvE,QAAQ;AAAA;AAwKrB;AACA;AACA;AACA,MAAMkD,oBAAoB,CAAC;EACvBtE,WAAWA,CAAC0B,OAAO,EAAE6B,eAAe,EAAEgB,gBAAgB,EAAE;IACpD,IAAI,CAACqB,YAAY,GAAG,CAAC,CAAC;IACtB,IAAI,CAAClE,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACsC,KAAK,GAAGT,eAAe;IAC5B,MAAMsC,cAAc,GAAGV,IAAI,CAACW,KAAK,CAACvC,eAAe,CAAC8B,WAAW,CAACC,MAAM,GAAG,GAAG,CAAC,oCAAoC,CAAC;IAChH,IAAIO,cAAc,GAAG,CAAC,EAAE;MACpB,IAAI,CAACE,0BAA0B,GAAG,IAAIC,YAAY,CAACH,cAAc,CAAC;MAClE,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,cAAc,EAAEI,CAAC,EAAE,EAAE;QACrC,IAAI,CAACF,0BAA0B,CAACE,CAAC,CAAC,GAAG,CAAC,CAAC;MAC3C;IACJ,CAAC,MACI;MACD,IAAI,CAACF,0BAA0B,GAAG,IAAI;IAC1C;IACA,IAAI,CAACG,iBAAiB,GAAG3B,gBAAgB;IACzC,IAAI,CAAC4B,UAAU,GAAG5C,eAAe,CAAC9C,UAAU;EAChD;EACAmE,QAAQA,CAACC,OAAO,EAAE;IACd,IAAI,CAAC,IAAI,CAACnD,OAAO,IAAI,IAAI,CAACsC,KAAK,CAACqB,WAAW,CAACC,MAAM,GAAG,GAAG,CAAC,sCAAsC;MAC3F,MAAMc,gBAAgB,GAAG,IAAI,CAACF,iBAAiB,CAACG,mBAAmB,CAAC,IAAI,CAACH,iBAAiB,CAACZ,MAAM,CAAC;MAClG,OAAOH,IAAI,CAACmB,KAAK,CAAC,IAAI,CAACH,UAAU,GAAGC,gBAAgB,CAAC;IACzD;IACA,IAAI,IAAI,CAACR,YAAY,KAAK,CAAC,CAAC,EAAE;MAC1B,IAAI,CAACA,YAAY,GAAG,IAAI,CAACW,iBAAiB,CAAC,IAAI,CAAC7E,OAAO,CAAC,CAAC8E,WAAW;MACpE3B,OAAO,EAAE4B,gBAAgB,CAAC,CAAC;IAC/B;IACA,OAAO,IAAI,CAACb,YAAY;EAC5B;EACAd,cAAcA,CAAA,EAAG;IACb,OAAQ,IAAI,CAACd,KAAK,CAACqB,WAAW,CAACC,MAAM,GAAG,GAAG,CAAC,wCAAyC,IAAI,CAACM,YAAY,KAAK,CAAC,CAAC;EACjH;EACA9F,4BAA4BA,CAAA,EAAG;IAC3B,IAAI,CAAC,IAAI,CAAC4B,OAAO,EAAE;MACf,OAAO5B,4BAA4B;IACvC;IACA,IAAI,IAAI,CAACkE,KAAK,CAACqB,WAAW,CAACC,MAAM,GAAG,GAAG,CAAC,sCAAsC;MAC1E,MAAMoB,aAAa,GAAG,IAAI,CAAC9B,QAAQ,CAAC,IAAI,CAAC;MACzC,MAAM+B,WAAW,GAAG,IAAI,CAACjF,OAAO,CAACA,OAAO,CAACkF,UAAU,CAACJ,WAAW;MAC/D,IAAIrB,IAAI,CAAC0B,GAAG,CAACH,aAAa,GAAGC,WAAW,CAAC,IAAI,CAAC,EAAE;QAC5C;QACAG,OAAO,CAACC,IAAI,CAAC,wFAAwF,CAAC;QACtGjH,4BAA4B,GAAG,KAAK;MACxC;IACJ;IACA,OAAOA,4BAA4B;EACvC;EACAmF,kBAAkBA,CAAA,EAAG;IACjB,OAAOT,kBAAkB,CAAC,IAAI,CAAC9C,OAAO,EAAE,IAAI,CAACsC,KAAK,EAAE,IAAI,CAACkC,iBAAiB,EAAE,KAAK,EAAE,CAAC,CAAC,6BAA6B,CAAC;EACvH;EACAhB,wBAAwBA,CAAC9C,UAAU,EAAEgB,WAAW,EAAEC,SAAS,EAAEwB,OAAO,EAAE;IAClE,MAAMmC,aAAa,GAAG,IAAI,CAACC,qBAAqB,CAAC7E,UAAU,EAAEgB,WAAW,EAAEyB,OAAO,CAAC;IAClF,MAAMqC,WAAW,GAAG,IAAI,CAACD,qBAAqB,CAAC7E,UAAU,EAAEiB,SAAS,EAAEwB,OAAO,CAAC;IAC9E,OAAO,CAAC,IAAI7F,oBAAoB,CAACgI,aAAa,EAAEE,WAAW,GAAGF,aAAa,CAAC,CAAC;EACjF;EACAC,qBAAqBA,CAAC7E,UAAU,EAAE+E,MAAM,EAAEtC,OAAO,EAAE;IAC/C,IAAIsC,MAAM,IAAI,GAAG,CAAC,sCAAsC;MACpD,MAAMf,gBAAgB,GAAG,IAAI,CAACF,iBAAiB,CAACG,mBAAmB,CAACc,MAAM,CAAC;MAC3E,OAAO,IAAI,CAAChB,UAAU,GAAGC,gBAAgB;IAC7C;IACA,MAAMgB,gBAAgB,GAAGjC,IAAI,CAACW,KAAK,CAAC,CAACqB,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC,oCAAoC,CAAC,GAAG,CAAC;IAChG,MAAME,SAAS,GAAG,CAACD,gBAAgB,GAAG,CAAC,IAAI,GAAG,CAAC,uCAAuC,CAAC;IACvF,IAAIE,oBAAoB,GAAG,CAAC,CAAC;IAC7B,IAAI,IAAI,CAACvB,0BAA0B,EAAE;MACjCuB,oBAAoB,GAAG,IAAI,CAACvB,0BAA0B,CAACqB,gBAAgB,CAAC;MACxE,IAAIE,oBAAoB,KAAK,CAAC,CAAC,EAAE;QAC7BA,oBAAoB,GAAG,IAAI,CAACC,sBAAsB,CAACnF,UAAU,EAAEiF,SAAS,EAAExC,OAAO,CAAC;QAClF,IAAI,CAACkB,0BAA0B,CAACqB,gBAAgB,CAAC,GAAGE,oBAAoB;MAC5E;IACJ;IACA,IAAIA,oBAAoB,KAAK,CAAC,CAAC,EAAE;MAC7B;MACA,MAAMlB,gBAAgB,GAAG,IAAI,CAACF,iBAAiB,CAACG,mBAAmB,CAACc,MAAM,CAAC;MAC3E,OAAO,IAAI,CAAChB,UAAU,GAAGC,gBAAgB;IAC7C;IACA,MAAMoB,yBAAyB,GAAG,IAAI,CAACtB,iBAAiB,CAACG,mBAAmB,CAACgB,SAAS,CAAC;IACvF,MAAMjB,gBAAgB,GAAG,IAAI,CAACF,iBAAiB,CAACG,mBAAmB,CAACc,MAAM,CAAC;IAC3E,OAAOG,oBAAoB,GAAG,IAAI,CAACnB,UAAU,IAAIC,gBAAgB,GAAGoB,yBAAyB,CAAC;EAClG;EACAjB,iBAAiBA,CAACkB,SAAS,EAAE;IACzB,OAAOA,SAAS,CAAC/F,OAAO,CAACkF,UAAU;EACvC;EACAW,sBAAsBA,CAACnF,UAAU,EAAE+E,MAAM,EAAEtC,OAAO,EAAE;IAChD,IAAI,CAAC,IAAI,CAACnD,OAAO,EAAE;MACf,OAAO,CAAC,CAAC;IACb;IACA,MAAMgG,WAAW,GAAG,IAAI,CAACxB,iBAAiB,CAACyB,cAAc,CAACR,MAAM,CAAC;IACjE,MAAMS,CAAC,GAAG7I,SAAS,CAAC8I,oBAAoB,CAAC,IAAI,CAACtB,iBAAiB,CAAC,IAAI,CAAC7E,OAAO,CAAC,EAAEgG,WAAW,CAACI,SAAS,EAAEJ,WAAW,CAACK,SAAS,EAAEL,WAAW,CAACI,SAAS,EAAEJ,WAAW,CAACK,SAAS,EAAElD,OAAO,CAAC;IACnL,IAAI,CAAC+C,CAAC,IAAIA,CAAC,CAACtC,MAAM,KAAK,CAAC,EAAE;MACtB,OAAO,CAAC,CAAC;IACb;IACA,OAAOsC,CAAC,CAAC,CAAC,CAAC,CAACI,IAAI;EACpB;EACAvC,qBAAqBA,CAACC,QAAQ,EAAEC,MAAM,EAAE;IACpC,OAAOF,qBAAqB,CAAC,IAAI,CAACS,iBAAiB,EAAER,QAAQ,EAAEC,MAAM,CAAC;EAC1E;AACJ;AACA;AACA;AACA;AACA,MAAMsC,gBAAgB,CAAC;EACnBjI,WAAWA,CAAC0B,OAAO,EAAE6B,eAAe,EAAEgB,gBAAgB,EAAEZ,WAAW,EAAEU,uBAAuB,EAAE;IAC1F,IAAI,CAAC3C,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACsC,KAAK,GAAGT,eAAe;IAC5B,IAAI,CAAC2C,iBAAiB,GAAG3B,gBAAgB;IACzC,IAAI,CAAC2D,iBAAiB,GAAG,OAAO,CAACC,IAAI,CAAC5E,eAAe,CAAC8B,WAAW,CAAC;IAClE,IAAI,CAAC+C,wBAAwB,GAAG/D,uBAAuB;IACvD,IAAI,CAACuB,YAAY,GAAG,CAAC,CAAC;IACtB,IAAI,CAACyC,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAAC1E,WAAW,IAAI,IAAI,CAACuC,iBAAiB,CAACZ,MAAM,KAAK,CAAC,CAAC,yBAAyB;MAC7E,IAAI,CAAC+C,iBAAiB,GAAG,IAAIrC,YAAY,CAACb,IAAI,CAACI,GAAG,CAAC,CAAC,EAAE,IAAI,CAACW,iBAAiB,CAACZ,MAAM,GAAG,CAAC,CAAC,CAAC;MACzF,KAAK,IAAI6B,MAAM,GAAG,CAAC,EAAEmB,GAAG,GAAG,IAAI,CAACpC,iBAAiB,CAACZ,MAAM,EAAE6B,MAAM,IAAImB,GAAG,EAAEnB,MAAM,EAAE,EAAE;QAC/E,IAAI,CAACkB,iBAAiB,CAAClB,MAAM,CAAC,GAAG,CAAC,CAAC;MACvC;IACJ;EACJ;EACA;EACAZ,iBAAiBA,CAACkB,SAAS,EAAE;IACzB,OAAOA,SAAS,CAAC/F,OAAO,CAACkF,UAAU;EACvC;EACA;AACJ;AACA;EACIhC,QAAQA,CAACC,OAAO,EAAE;IACd,IAAI,CAAC,IAAI,CAACnD,OAAO,EAAE;MACf,OAAO,CAAC;IACZ;IACA,IAAI,IAAI,CAACkE,YAAY,KAAK,CAAC,CAAC,EAAE;MAC1B,IAAI,CAACA,YAAY,GAAG,IAAI,CAACW,iBAAiB,CAAC,IAAI,CAAC7E,OAAO,CAAC,CAAC8E,WAAW;MACpE3B,OAAO,EAAE4B,gBAAgB,CAAC,CAAC;IAC/B;IACA,OAAO,IAAI,CAACb,YAAY;EAC5B;EACAd,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAACc,YAAY,KAAK,CAAC,CAAC,EAAE;MAC1B,OAAO,KAAK;IAChB;IACA,OAAO,IAAI;EACf;EACA;AACJ;AACA;EACIV,wBAAwBA,CAAC9C,UAAU,EAAEgB,WAAW,EAAEC,SAAS,EAAEwB,OAAO,EAAE;IAClE,IAAI,CAAC,IAAI,CAACnD,OAAO,EAAE;MACf,OAAO,IAAI;IACf;IACA,IAAI,IAAI,CAAC2G,iBAAiB,KAAK,IAAI,EAAE;MACjC;MACA,MAAME,WAAW,GAAG,IAAI,CAACC,gBAAgB,CAAC,IAAI,CAAC9G,OAAO,EAAEU,UAAU,EAAEgB,WAAW,EAAEyB,OAAO,CAAC;MACzF,IAAI0D,WAAW,KAAK,CAAC,CAAC,EAAE;QACpB,OAAO,IAAI;MACf;MACA,MAAME,SAAS,GAAG,IAAI,CAACD,gBAAgB,CAAC,IAAI,CAAC9G,OAAO,EAAEU,UAAU,EAAEiB,SAAS,EAAEwB,OAAO,CAAC;MACrF,IAAI4D,SAAS,KAAK,CAAC,CAAC,EAAE;QAClB,OAAO,IAAI;MACf;MACA,OAAO,CAAC,IAAIzJ,oBAAoB,CAACuJ,WAAW,EAAEE,SAAS,GAAGF,WAAW,CAAC,CAAC;IAC3E;IACA,OAAO,IAAI,CAACG,0BAA0B,CAAC,IAAI,CAAChH,OAAO,EAAEU,UAAU,EAAEgB,WAAW,EAAEC,SAAS,EAAEwB,OAAO,CAAC;EACrG;EACA6D,0BAA0BA,CAAChH,OAAO,EAAEU,UAAU,EAAEgB,WAAW,EAAEC,SAAS,EAAEwB,OAAO,EAAE;IAC7E,IAAIzB,WAAW,KAAKC,SAAS,EAAE;MAC3B,MAAMsF,WAAW,GAAG,IAAI,CAACH,gBAAgB,CAAC9G,OAAO,EAAEU,UAAU,EAAEgB,WAAW,EAAEyB,OAAO,CAAC;MACpF,IAAI8D,WAAW,KAAK,CAAC,CAAC,EAAE;QACpB,OAAO,IAAI;MACf,CAAC,MACI;QACD,OAAO,CAAC,IAAI3J,oBAAoB,CAAC2J,WAAW,EAAE,CAAC,CAAC,CAAC;MACrD;IACJ,CAAC,MACI;MACD,OAAO,IAAI,CAACC,6BAA6B,CAAClH,OAAO,EAAE0B,WAAW,EAAEC,SAAS,EAAEwB,OAAO,CAAC;IACvF;EACJ;EACA2D,gBAAgBA,CAAC9G,OAAO,EAAEU,UAAU,EAAE+E,MAAM,EAAEtC,OAAO,EAAE;IACnD,IAAI,IAAI,CAACqB,iBAAiB,CAACZ,MAAM,KAAK,CAAC,EAAE;MACrC;MACA,IAAI,IAAI,CAAC8C,wBAAwB,KAAK,CAAC,CAAC,+BAA+B;QACnE;QACA,OAAO,CAAC;MACZ;MACA,IAAI,IAAI,CAACA,wBAAwB,KAAK,CAAC,CAAC,gCAAgC;QACpE;QACA,OAAO,CAAC;MACZ;MACA,IAAI,IAAI,CAACA,wBAAwB,KAAK,CAAC,CAAC,iCAAiC;QACrE;QACA,OAAO,IAAI,CAACxD,QAAQ,CAACC,OAAO,CAAC;MACjC;MACA;MACA,MAAMgE,aAAa,GAAG,IAAI,CAACtC,iBAAiB,CAAC7E,OAAO,CAAC;MACrD,IAAImH,aAAa,CAACjC,UAAU,EAAE;QAC1B/B,OAAO,CAAC4B,gBAAgB,CAAC,CAAC;QAC1B,OAAOoC,aAAa,CAACjC,UAAU,CAACJ,WAAW;MAC/C,CAAC,MACI;QACD,OAAO,CAAC;MACZ;IACJ;IACA,IAAI,IAAI,CAAC6B,iBAAiB,KAAK,IAAI,EAAE;MACjC;MACA,MAAMS,iBAAiB,GAAG,IAAI,CAACT,iBAAiB,CAAClB,MAAM,CAAC;MACxD,IAAI2B,iBAAiB,KAAK,CAAC,CAAC,EAAE;QAC1B,OAAOA,iBAAiB;MAC5B;MACA,MAAMC,MAAM,GAAG,IAAI,CAACxB,sBAAsB,CAAC7F,OAAO,EAAEU,UAAU,EAAE+E,MAAM,EAAEtC,OAAO,CAAC;MAChF,IAAI,CAACwD,iBAAiB,CAAClB,MAAM,CAAC,GAAG4B,MAAM;MACvC,OAAOA,MAAM;IACjB;IACA,OAAO,IAAI,CAACxB,sBAAsB,CAAC7F,OAAO,EAAEU,UAAU,EAAE+E,MAAM,EAAEtC,OAAO,CAAC;EAC5E;EACA0C,sBAAsBA,CAAC7F,OAAO,EAAEU,UAAU,EAAE+E,MAAM,EAAEtC,OAAO,EAAE;IACzD,IAAI,IAAI,CAACqB,iBAAiB,CAACZ,MAAM,KAAK,CAAC,EAAE;MACrC;MACA,MAAMsC,CAAC,GAAG7I,SAAS,CAAC8I,oBAAoB,CAAC,IAAI,CAACtB,iBAAiB,CAAC7E,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAEmD,OAAO,CAAC;MAC9F,IAAI,CAAC+C,CAAC,IAAIA,CAAC,CAACtC,MAAM,KAAK,CAAC,EAAE;QACtB,OAAO,CAAC,CAAC;MACb;MACA,OAAOsC,CAAC,CAAC,CAAC,CAAC,CAACI,IAAI;IACpB;IACA,IAAIb,MAAM,KAAK,IAAI,CAACjB,iBAAiB,CAACZ,MAAM,IAAI,IAAI,CAAC4C,iBAAiB,IAAI,IAAI,CAACE,wBAAwB,KAAK,CAAC,CAAC,+BAA+B;MACzI;MACA,OAAO,IAAI,CAACxD,QAAQ,CAACC,OAAO,CAAC;IACjC;IACA,MAAM6C,WAAW,GAAG,IAAI,CAACxB,iBAAiB,CAACyB,cAAc,CAACR,MAAM,CAAC;IACjE,MAAMS,CAAC,GAAG7I,SAAS,CAAC8I,oBAAoB,CAAC,IAAI,CAACtB,iBAAiB,CAAC7E,OAAO,CAAC,EAAEgG,WAAW,CAACI,SAAS,EAAEJ,WAAW,CAACK,SAAS,EAAEL,WAAW,CAACI,SAAS,EAAEJ,WAAW,CAACK,SAAS,EAAElD,OAAO,CAAC;IAC9K,IAAI,CAAC+C,CAAC,IAAIA,CAAC,CAACtC,MAAM,KAAK,CAAC,EAAE;MACtB,OAAO,CAAC,CAAC;IACb;IACA,MAAMyD,MAAM,GAAGnB,CAAC,CAAC,CAAC,CAAC,CAACI,IAAI;IACxB,IAAI,IAAI,CAAChE,KAAK,CAACN,YAAY,EAAE;MACzB,MAAM0C,gBAAgB,GAAG,IAAI,CAACF,iBAAiB,CAACG,mBAAmB,CAACc,MAAM,CAAC;MAC3E,MAAM6B,cAAc,GAAG7D,IAAI,CAACmB,KAAK,CAAC,IAAI,CAACtC,KAAK,CAACvD,UAAU,GAAG2F,gBAAgB,CAAC;MAC3E,IAAIjB,IAAI,CAAC0B,GAAG,CAACmC,cAAc,GAAGD,MAAM,CAAC,IAAI,CAAC,EAAE;QACxC,OAAOC,cAAc;MACzB;IACJ;IACA,OAAOD,MAAM;EACjB;EACAH,6BAA6BA,CAAClH,OAAO,EAAE0B,WAAW,EAAEC,SAAS,EAAEwB,OAAO,EAAE;IACpE,IAAIzB,WAAW,KAAK,CAAC,IAAIC,SAAS,KAAK,IAAI,CAAC6C,iBAAiB,CAACZ,MAAM,EAAE;MAClE;MACA,OAAO,CAAC,IAAItG,oBAAoB,CAAC,CAAC,EAAE,IAAI,CAAC4F,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC;IAChE;IACA,MAAMoE,gBAAgB,GAAG,IAAI,CAAC/C,iBAAiB,CAACyB,cAAc,CAACvE,WAAW,CAAC;IAC3E,MAAM8F,cAAc,GAAG,IAAI,CAAChD,iBAAiB,CAACyB,cAAc,CAACtE,SAAS,CAAC;IACvE,OAAOtE,SAAS,CAAC8I,oBAAoB,CAAC,IAAI,CAACtB,iBAAiB,CAAC7E,OAAO,CAAC,EAAEuH,gBAAgB,CAACnB,SAAS,EAAEmB,gBAAgB,CAAClB,SAAS,EAAEmB,cAAc,CAACpB,SAAS,EAAEoB,cAAc,CAACnB,SAAS,EAAElD,OAAO,CAAC;EAC/L;EACA;AACJ;AACA;EACIY,qBAAqBA,CAACC,QAAQ,EAAEC,MAAM,EAAE;IACpC,OAAOF,qBAAqB,CAAC,IAAI,CAACS,iBAAiB,EAAER,QAAQ,EAAEC,MAAM,CAAC;EAC1E;AACJ;AACA,MAAMwD,sBAAsB,SAASlB,gBAAgB,CAAC;EAClDS,0BAA0BA,CAAChH,OAAO,EAAEU,UAAU,EAAEgB,WAAW,EAAEC,SAAS,EAAEwB,OAAO,EAAE;IAC7E,MAAMV,MAAM,GAAG,KAAK,CAACuE,0BAA0B,CAAChH,OAAO,EAAEU,UAAU,EAAEgB,WAAW,EAAEC,SAAS,EAAEwB,OAAO,CAAC;IACrG,IAAI,CAACV,MAAM,IAAIA,MAAM,CAACmB,MAAM,KAAK,CAAC,IAAIlC,WAAW,KAAKC,SAAS,IAAKD,WAAW,KAAK,CAAC,IAAIC,SAAS,KAAK,IAAI,CAAC6C,iBAAiB,CAACZ,MAAO,EAAE;MACnI,OAAOnB,MAAM;IACjB;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAACH,KAAK,CAACL,WAAW,EAAE;MACzB;MACA;MACA,MAAMyF,cAAc,GAAG,IAAI,CAACZ,gBAAgB,CAAC9G,OAAO,EAAEU,UAAU,EAAEiB,SAAS,EAAEwB,OAAO,CAAC;MACrF,IAAIuE,cAAc,KAAK,CAAC,CAAC,EAAE;QACvB,MAAMC,SAAS,GAAGlF,MAAM,CAACA,MAAM,CAACmB,MAAM,GAAG,CAAC,CAAC;QAC3C,IAAI+D,SAAS,CAACrB,IAAI,GAAGoB,cAAc,EAAE;UACjC;UACAC,SAAS,CAACC,KAAK,GAAGF,cAAc,GAAGC,SAAS,CAACrB,IAAI;QACrD;MACJ;IACJ;IACA,OAAO7D,MAAM;EACjB;AACJ;AACA,MAAMK,kBAAkB,GAAI,YAAY;EACpC,IAAI5F,OAAO,CAAC2K,QAAQ,EAAE;IAClB,OAAOC,wBAAwB;EACnC;EACA,OAAOC,wBAAwB;AACnC,CAAC,CAAE,CAAC;AACJ,SAASD,wBAAwBA,CAAC9H,OAAO,EAAE6B,eAAe,EAAEgB,gBAAgB,EAAEZ,WAAW,EAAEU,uBAAuB,EAAE;EAChH,OAAO,IAAI8E,sBAAsB,CAACzH,OAAO,EAAE6B,eAAe,EAAEgB,gBAAgB,EAAEZ,WAAW,EAAEU,uBAAuB,CAAC;AACvH;AACA,SAASoF,wBAAwBA,CAAC/H,OAAO,EAAE6B,eAAe,EAAEgB,gBAAgB,EAAEZ,WAAW,EAAEU,uBAAuB,EAAE;EAChH,OAAO,IAAI4D,gBAAgB,CAACvG,OAAO,EAAE6B,eAAe,EAAEgB,gBAAgB,EAAEZ,WAAW,EAAEU,uBAAuB,CAAC;AACjH;AACA,OAAO,SAASoB,qBAAqBA,CAAClB,gBAAgB,EAAEmB,QAAQ,EAAEC,MAAM,EAAE;EACtE,MAAM+D,yBAAyB,GAAGhE,QAAQ,CAACiE,WAAW,CAACrE,MAAM;EAC7D,IAAIsE,SAAS,GAAG,CAAC,CAAC;EAClB,OAAOlE,QAAQ,EAAE;IACbA,QAAQ,GAAGA,QAAQ,CAACmE,eAAe;IACnCD,SAAS,EAAE;EACf;EACA,OAAOrF,gBAAgB,CAACuF,SAAS,CAAC,IAAIxK,WAAW,CAACsK,SAAS,EAAEjE,MAAM,CAAC,EAAE+D,yBAAyB,CAAC;AACpG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}