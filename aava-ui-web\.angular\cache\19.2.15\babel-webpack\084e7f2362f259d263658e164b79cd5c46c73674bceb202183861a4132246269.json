{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { getCharIndex } from './minimapCharSheet.js';\nimport { toUint8 } from '../../../../base/common/uint.js';\nexport class MinimapCharRenderer {\n  constructor(charData, scale) {\n    this.scale = scale;\n    this._minimapCharRendererBrand = undefined;\n    this.charDataNormal = MinimapCharRenderer.soften(charData, 12 / 15);\n    this.charDataLight = MinimapCharRenderer.soften(charData, 50 / 60);\n  }\n  static soften(input, ratio) {\n    const result = new Uint8ClampedArray(input.length);\n    for (let i = 0, len = input.length; i < len; i++) {\n      result[i] = toUint8(input[i] * ratio);\n    }\n    return result;\n  }\n  renderChar(target, dx, dy, chCode, color, foregroundAlpha, backgroundColor, backgroundAlpha, fontScale, useLighterFont, force1pxHeight) {\n    const charWidth = 1 /* Constants.BASE_CHAR_WIDTH */ * this.scale;\n    const charHeight = 2 /* Constants.BASE_CHAR_HEIGHT */ * this.scale;\n    const renderHeight = force1pxHeight ? 1 : charHeight;\n    if (dx + charWidth > target.width || dy + renderHeight > target.height) {\n      console.warn('bad render request outside image data');\n      return;\n    }\n    const charData = useLighterFont ? this.charDataLight : this.charDataNormal;\n    const charIndex = getCharIndex(chCode, fontScale);\n    const destWidth = target.width * 4 /* Constants.RGBA_CHANNELS_CNT */;\n    const backgroundR = backgroundColor.r;\n    const backgroundG = backgroundColor.g;\n    const backgroundB = backgroundColor.b;\n    const deltaR = color.r - backgroundR;\n    const deltaG = color.g - backgroundG;\n    const deltaB = color.b - backgroundB;\n    const destAlpha = Math.max(foregroundAlpha, backgroundAlpha);\n    const dest = target.data;\n    let sourceOffset = charIndex * charWidth * charHeight;\n    let row = dy * destWidth + dx * 4 /* Constants.RGBA_CHANNELS_CNT */;\n    for (let y = 0; y < renderHeight; y++) {\n      let column = row;\n      for (let x = 0; x < charWidth; x++) {\n        const c = charData[sourceOffset++] / 255 * (foregroundAlpha / 255);\n        dest[column++] = backgroundR + deltaR * c;\n        dest[column++] = backgroundG + deltaG * c;\n        dest[column++] = backgroundB + deltaB * c;\n        dest[column++] = destAlpha;\n      }\n      row += destWidth;\n    }\n  }\n  blockRenderChar(target, dx, dy, color, foregroundAlpha, backgroundColor, backgroundAlpha, force1pxHeight) {\n    const charWidth = 1 /* Constants.BASE_CHAR_WIDTH */ * this.scale;\n    const charHeight = 2 /* Constants.BASE_CHAR_HEIGHT */ * this.scale;\n    const renderHeight = force1pxHeight ? 1 : charHeight;\n    if (dx + charWidth > target.width || dy + renderHeight > target.height) {\n      console.warn('bad render request outside image data');\n      return;\n    }\n    const destWidth = target.width * 4 /* Constants.RGBA_CHANNELS_CNT */;\n    const c = 0.5 * (foregroundAlpha / 255);\n    const backgroundR = backgroundColor.r;\n    const backgroundG = backgroundColor.g;\n    const backgroundB = backgroundColor.b;\n    const deltaR = color.r - backgroundR;\n    const deltaG = color.g - backgroundG;\n    const deltaB = color.b - backgroundB;\n    const colorR = backgroundR + deltaR * c;\n    const colorG = backgroundG + deltaG * c;\n    const colorB = backgroundB + deltaB * c;\n    const destAlpha = Math.max(foregroundAlpha, backgroundAlpha);\n    const dest = target.data;\n    let row = dy * destWidth + dx * 4 /* Constants.RGBA_CHANNELS_CNT */;\n    for (let y = 0; y < renderHeight; y++) {\n      let column = row;\n      for (let x = 0; x < charWidth; x++) {\n        dest[column++] = colorR;\n        dest[column++] = colorG;\n        dest[column++] = colorB;\n        dest[column++] = destAlpha;\n      }\n      row += destWidth;\n    }\n  }\n}", "map": {"version": 3, "names": ["getCharIndex", "toUint8", "Minimap<PERSON>har<PERSON><PERSON><PERSON>", "constructor", "char<PERSON><PERSON>", "scale", "_minimapChar<PERSON><PERSON><PERSON><PERSON><PERSON>", "undefined", "char<PERSON><PERSON><PERSON><PERSON><PERSON>", "soften", "charDataLight", "input", "ratio", "result", "Uint8ClampedArray", "length", "i", "len", "renderChar", "target", "dx", "dy", "chCode", "color", "foregroundAlpha", "backgroundColor", "backgroundAlpha", "fontScale", "useLighterFont", "force1pxHeight", "char<PERSON><PERSON><PERSON>", "charHeight", "renderHeight", "width", "height", "console", "warn", "charIndex", "destWidth", "backgroundR", "r", "backgroundG", "g", "backgroundB", "b", "deltaR", "deltaG", "deltaB", "destAlpha", "Math", "max", "dest", "data", "sourceOffset", "row", "y", "column", "x", "c", "blockRenderChar", "colorR", "colorG", "colorB"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/browser/viewParts/minimap/minimapCharRenderer.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { getCharIndex } from './minimapCharSheet.js';\nimport { toUint8 } from '../../../../base/common/uint.js';\nexport class MinimapCharRenderer {\n    constructor(charData, scale) {\n        this.scale = scale;\n        this._minimapCharRendererBrand = undefined;\n        this.charDataNormal = MinimapCharRenderer.soften(charData, 12 / 15);\n        this.charDataLight = MinimapCharRenderer.soften(charData, 50 / 60);\n    }\n    static soften(input, ratio) {\n        const result = new Uint8ClampedArray(input.length);\n        for (let i = 0, len = input.length; i < len; i++) {\n            result[i] = toUint8(input[i] * ratio);\n        }\n        return result;\n    }\n    renderChar(target, dx, dy, chCode, color, foregroundAlpha, backgroundColor, backgroundAlpha, fontScale, useLighterFont, force1pxHeight) {\n        const charWidth = 1 /* Constants.BASE_CHAR_WIDTH */ * this.scale;\n        const charHeight = 2 /* Constants.BASE_CHAR_HEIGHT */ * this.scale;\n        const renderHeight = (force1pxHeight ? 1 : charHeight);\n        if (dx + charWidth > target.width || dy + renderHeight > target.height) {\n            console.warn('bad render request outside image data');\n            return;\n        }\n        const charData = useLighterFont ? this.charDataLight : this.charDataNormal;\n        const charIndex = getCharIndex(chCode, fontScale);\n        const destWidth = target.width * 4 /* Constants.RGBA_CHANNELS_CNT */;\n        const backgroundR = backgroundColor.r;\n        const backgroundG = backgroundColor.g;\n        const backgroundB = backgroundColor.b;\n        const deltaR = color.r - backgroundR;\n        const deltaG = color.g - backgroundG;\n        const deltaB = color.b - backgroundB;\n        const destAlpha = Math.max(foregroundAlpha, backgroundAlpha);\n        const dest = target.data;\n        let sourceOffset = charIndex * charWidth * charHeight;\n        let row = dy * destWidth + dx * 4 /* Constants.RGBA_CHANNELS_CNT */;\n        for (let y = 0; y < renderHeight; y++) {\n            let column = row;\n            for (let x = 0; x < charWidth; x++) {\n                const c = (charData[sourceOffset++] / 255) * (foregroundAlpha / 255);\n                dest[column++] = backgroundR + deltaR * c;\n                dest[column++] = backgroundG + deltaG * c;\n                dest[column++] = backgroundB + deltaB * c;\n                dest[column++] = destAlpha;\n            }\n            row += destWidth;\n        }\n    }\n    blockRenderChar(target, dx, dy, color, foregroundAlpha, backgroundColor, backgroundAlpha, force1pxHeight) {\n        const charWidth = 1 /* Constants.BASE_CHAR_WIDTH */ * this.scale;\n        const charHeight = 2 /* Constants.BASE_CHAR_HEIGHT */ * this.scale;\n        const renderHeight = (force1pxHeight ? 1 : charHeight);\n        if (dx + charWidth > target.width || dy + renderHeight > target.height) {\n            console.warn('bad render request outside image data');\n            return;\n        }\n        const destWidth = target.width * 4 /* Constants.RGBA_CHANNELS_CNT */;\n        const c = 0.5 * (foregroundAlpha / 255);\n        const backgroundR = backgroundColor.r;\n        const backgroundG = backgroundColor.g;\n        const backgroundB = backgroundColor.b;\n        const deltaR = color.r - backgroundR;\n        const deltaG = color.g - backgroundG;\n        const deltaB = color.b - backgroundB;\n        const colorR = backgroundR + deltaR * c;\n        const colorG = backgroundG + deltaG * c;\n        const colorB = backgroundB + deltaB * c;\n        const destAlpha = Math.max(foregroundAlpha, backgroundAlpha);\n        const dest = target.data;\n        let row = dy * destWidth + dx * 4 /* Constants.RGBA_CHANNELS_CNT */;\n        for (let y = 0; y < renderHeight; y++) {\n            let column = row;\n            for (let x = 0; x < charWidth; x++) {\n                dest[column++] = colorR;\n                dest[column++] = colorG;\n                dest[column++] = colorB;\n                dest[column++] = destAlpha;\n            }\n            row += destWidth;\n        }\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,YAAY,QAAQ,uBAAuB;AACpD,SAASC,OAAO,QAAQ,iCAAiC;AACzD,OAAO,MAAMC,mBAAmB,CAAC;EAC7BC,WAAWA,CAACC,QAAQ,EAAEC,KAAK,EAAE;IACzB,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,yBAAyB,GAAGC,SAAS;IAC1C,IAAI,CAACC,cAAc,GAAGN,mBAAmB,CAACO,MAAM,CAACL,QAAQ,EAAE,EAAE,GAAG,EAAE,CAAC;IACnE,IAAI,CAACM,aAAa,GAAGR,mBAAmB,CAACO,MAAM,CAACL,QAAQ,EAAE,EAAE,GAAG,EAAE,CAAC;EACtE;EACA,OAAOK,MAAMA,CAACE,KAAK,EAAEC,KAAK,EAAE;IACxB,MAAMC,MAAM,GAAG,IAAIC,iBAAiB,CAACH,KAAK,CAACI,MAAM,CAAC;IAClD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGN,KAAK,CAACI,MAAM,EAAEC,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;MAC9CH,MAAM,CAACG,CAAC,CAAC,GAAGf,OAAO,CAACU,KAAK,CAACK,CAAC,CAAC,GAAGJ,KAAK,CAAC;IACzC;IACA,OAAOC,MAAM;EACjB;EACAK,UAAUA,CAACC,MAAM,EAAEC,EAAE,EAAEC,EAAE,EAAEC,MAAM,EAAEC,KAAK,EAAEC,eAAe,EAAEC,eAAe,EAAEC,eAAe,EAAEC,SAAS,EAAEC,cAAc,EAAEC,cAAc,EAAE;IACpI,MAAMC,SAAS,GAAG,CAAC,CAAC,kCAAkC,IAAI,CAACzB,KAAK;IAChE,MAAM0B,UAAU,GAAG,CAAC,CAAC,mCAAmC,IAAI,CAAC1B,KAAK;IAClE,MAAM2B,YAAY,GAAIH,cAAc,GAAG,CAAC,GAAGE,UAAW;IACtD,IAAIX,EAAE,GAAGU,SAAS,GAAGX,MAAM,CAACc,KAAK,IAAIZ,EAAE,GAAGW,YAAY,GAAGb,MAAM,CAACe,MAAM,EAAE;MACpEC,OAAO,CAACC,IAAI,CAAC,uCAAuC,CAAC;MACrD;IACJ;IACA,MAAMhC,QAAQ,GAAGwB,cAAc,GAAG,IAAI,CAAClB,aAAa,GAAG,IAAI,CAACF,cAAc;IAC1E,MAAM6B,SAAS,GAAGrC,YAAY,CAACsB,MAAM,EAAEK,SAAS,CAAC;IACjD,MAAMW,SAAS,GAAGnB,MAAM,CAACc,KAAK,GAAG,CAAC,CAAC;IACnC,MAAMM,WAAW,GAAGd,eAAe,CAACe,CAAC;IACrC,MAAMC,WAAW,GAAGhB,eAAe,CAACiB,CAAC;IACrC,MAAMC,WAAW,GAAGlB,eAAe,CAACmB,CAAC;IACrC,MAAMC,MAAM,GAAGtB,KAAK,CAACiB,CAAC,GAAGD,WAAW;IACpC,MAAMO,MAAM,GAAGvB,KAAK,CAACmB,CAAC,GAAGD,WAAW;IACpC,MAAMM,MAAM,GAAGxB,KAAK,CAACqB,CAAC,GAAGD,WAAW;IACpC,MAAMK,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC1B,eAAe,EAAEE,eAAe,CAAC;IAC5D,MAAMyB,IAAI,GAAGhC,MAAM,CAACiC,IAAI;IACxB,IAAIC,YAAY,GAAGhB,SAAS,GAAGP,SAAS,GAAGC,UAAU;IACrD,IAAIuB,GAAG,GAAGjC,EAAE,GAAGiB,SAAS,GAAGlB,EAAE,GAAG,CAAC,CAAC;IAClC,KAAK,IAAImC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvB,YAAY,EAAEuB,CAAC,EAAE,EAAE;MACnC,IAAIC,MAAM,GAAGF,GAAG;MAChB,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3B,SAAS,EAAE2B,CAAC,EAAE,EAAE;QAChC,MAAMC,CAAC,GAAItD,QAAQ,CAACiD,YAAY,EAAE,CAAC,GAAG,GAAG,IAAK7B,eAAe,GAAG,GAAG,CAAC;QACpE2B,IAAI,CAACK,MAAM,EAAE,CAAC,GAAGjB,WAAW,GAAGM,MAAM,GAAGa,CAAC;QACzCP,IAAI,CAACK,MAAM,EAAE,CAAC,GAAGf,WAAW,GAAGK,MAAM,GAAGY,CAAC;QACzCP,IAAI,CAACK,MAAM,EAAE,CAAC,GAAGb,WAAW,GAAGI,MAAM,GAAGW,CAAC;QACzCP,IAAI,CAACK,MAAM,EAAE,CAAC,GAAGR,SAAS;MAC9B;MACAM,GAAG,IAAIhB,SAAS;IACpB;EACJ;EACAqB,eAAeA,CAACxC,MAAM,EAAEC,EAAE,EAAEC,EAAE,EAAEE,KAAK,EAAEC,eAAe,EAAEC,eAAe,EAAEC,eAAe,EAAEG,cAAc,EAAE;IACtG,MAAMC,SAAS,GAAG,CAAC,CAAC,kCAAkC,IAAI,CAACzB,KAAK;IAChE,MAAM0B,UAAU,GAAG,CAAC,CAAC,mCAAmC,IAAI,CAAC1B,KAAK;IAClE,MAAM2B,YAAY,GAAIH,cAAc,GAAG,CAAC,GAAGE,UAAW;IACtD,IAAIX,EAAE,GAAGU,SAAS,GAAGX,MAAM,CAACc,KAAK,IAAIZ,EAAE,GAAGW,YAAY,GAAGb,MAAM,CAACe,MAAM,EAAE;MACpEC,OAAO,CAACC,IAAI,CAAC,uCAAuC,CAAC;MACrD;IACJ;IACA,MAAME,SAAS,GAAGnB,MAAM,CAACc,KAAK,GAAG,CAAC,CAAC;IACnC,MAAMyB,CAAC,GAAG,GAAG,IAAIlC,eAAe,GAAG,GAAG,CAAC;IACvC,MAAMe,WAAW,GAAGd,eAAe,CAACe,CAAC;IACrC,MAAMC,WAAW,GAAGhB,eAAe,CAACiB,CAAC;IACrC,MAAMC,WAAW,GAAGlB,eAAe,CAACmB,CAAC;IACrC,MAAMC,MAAM,GAAGtB,KAAK,CAACiB,CAAC,GAAGD,WAAW;IACpC,MAAMO,MAAM,GAAGvB,KAAK,CAACmB,CAAC,GAAGD,WAAW;IACpC,MAAMM,MAAM,GAAGxB,KAAK,CAACqB,CAAC,GAAGD,WAAW;IACpC,MAAMiB,MAAM,GAAGrB,WAAW,GAAGM,MAAM,GAAGa,CAAC;IACvC,MAAMG,MAAM,GAAGpB,WAAW,GAAGK,MAAM,GAAGY,CAAC;IACvC,MAAMI,MAAM,GAAGnB,WAAW,GAAGI,MAAM,GAAGW,CAAC;IACvC,MAAMV,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC1B,eAAe,EAAEE,eAAe,CAAC;IAC5D,MAAMyB,IAAI,GAAGhC,MAAM,CAACiC,IAAI;IACxB,IAAIE,GAAG,GAAGjC,EAAE,GAAGiB,SAAS,GAAGlB,EAAE,GAAG,CAAC,CAAC;IAClC,KAAK,IAAImC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvB,YAAY,EAAEuB,CAAC,EAAE,EAAE;MACnC,IAAIC,MAAM,GAAGF,GAAG;MAChB,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3B,SAAS,EAAE2B,CAAC,EAAE,EAAE;QAChCN,IAAI,CAACK,MAAM,EAAE,CAAC,GAAGI,MAAM;QACvBT,IAAI,CAACK,MAAM,EAAE,CAAC,GAAGK,MAAM;QACvBV,IAAI,CAACK,MAAM,EAAE,CAAC,GAAGM,MAAM;QACvBX,IAAI,CAACK,MAAM,EAAE,CAAC,GAAGR,SAAS;MAC9B;MACAM,GAAG,IAAIhB,SAAS;IACpB;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}