<div class="agent-stepper-card" [class.active]="isActive">
  <!-- Stepper Section -->
  <div class="stepper-section">
    <!-- Stepper Circle -->
    <div [class]="stepperClass">
      <ava-icon 
        *ngIf="isCompleted" 
        iconName="check" 
        [iconSize]="16" 
        iconColor="white">
      </ava-icon>
    </div>
    
    <!-- Connector Line -->
    <div *ngIf="!isLast" class="stepper-line" [class.active]="isCompleted"></div>
  </div>

  <!-- Card Content -->
  <div class="card-content">
    <!-- Agent Header -->
    <div class="agent-header" (click)="agent.hasInputs ? toggleExpanded() : null">
      <div class="agent-info">
        <div class="bot-icon">
          <ava-icon
            iconName="bot"
            [iconSize]="20"
            iconColor="#0078E8">
          </ava-icon>
        </div>
        <h4 class="agent-name" [title]="agent.name">{{ agent.name }}</h4>
      </div>

      <div class="header-actions">
        <ava-icon
          [class.expanded]="isExpanded"
          [iconName]="isExpanded ? 'chevron-up' : 'chevron-down'"
          [iconSize]="16"
          [title]="!agent.hasInputs ? 'No Input Required' : ''"
          [disabled]="!agent.hasInputs"
          [class.disabled]="!agent.hasInputs"
          iconColor="#444653">
        </ava-icon>
      </div>
    </div>

    <!-- Expanded Content -->
    <div class="agent-details" *ngIf="isExpanded" [@slideDown]>
      <!-- Input Fields Only -->
      <div class="input-section" *ngIf="agent.hasInputs && agent.inputs">
        <div class="inputs-container" [class.scrollable]="agent.inputs.length > maxVisibleInputs">
          <div 
            class="input-field-container" 
            *ngFor="let input of visibleInputs; let i = index; trackBy: trackByIndex">
            
            <label class="input-label">{{ input.inputName }}</label>
            
            <div class="input-container">
              <textarea
                [(ngModel)]="input.value"
                [disabled]="isInputDisabled(input)"
                (input)="onInputChange(i, $event)"
                (keydown.enter)="handleSendMessage(i); $event.preventDefault()"
                [placeholder]="input.inputType === 'image' ? 'Please attach an image file' : 'Enter ' + input.inputName"
                class="input-textarea"
                [class.disabled]="isInputDisabled(input)">
              </textarea>

              <button
                *ngIf="showFileUploadButton(input)"
                class="attach-btn"
                [title]="input.inputType === 'image' ? 'Attach Image' : 'Attach File'"
                [disabled]="input.inputType === 'text' && isInputDisabled(input)"
                (click)="onFileInputClick(i)">
                <ava-icon
                  iconName="paperclip"
                  [iconSize]="18"
                  iconColor="#03ACC1">
                </ava-icon>
              </button>

              <button
                class="send-btn"
                title="Send"
                (click)="handleSendMessage(i)"
                [disabled]="!input.value && (!input.files || input.files.length === 0)">
                <ava-icon
                  iconName="send-horizontal"
                  [iconSize]="18"
                  iconColor="#03ACC1">
                </ava-icon>
              </button>
            </div>

            <!-- Display uploaded files -->
            <div class="uploaded-files" *ngIf="input.files && input.files.length > 0">
              <div
                class="file-item"
                *ngFor="let file of input.files; let fileIndex = index">
                <span class="file-name">{{ file.name }}</span>
                <button
                  class="remove-file"
                  (click)="removeFile(i, fileIndex)"
                  title="Remove file">
                  <ava-icon iconName="x" [iconSize]="12" iconColor="#e53e3e"></ava-icon>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Show More/Less Toggle -->
        <div class="show-more-section" *ngIf="hiddenInputsCount > 0">
          <button class="show-more-btn" (click)="toggleShowAllInputs()">
            <span>{{ showAllInputs ? 'Show Less' : 'Show ' + hiddenInputsCount + ' More' }}</span>
            <ava-icon
              [iconName]="showAllInputs ? 'chevron-up' : 'chevron-down'"
              [iconSize]="14"
              iconColor="#1A46A7">
            </ava-icon>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Hidden file input -->
  <input
    #fileInput
    type="file"
    style="display: none"
    multiple
    (change)="onFileSelected($event)">
</div>
