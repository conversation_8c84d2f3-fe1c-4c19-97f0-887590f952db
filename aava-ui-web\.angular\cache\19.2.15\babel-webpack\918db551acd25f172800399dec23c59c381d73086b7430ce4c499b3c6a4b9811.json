{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { OffsetRange } from '../../../core/offsetRange.js';\nimport { SequenceDiff, InfiniteTimeout, DiffAlgorithmResult } from './diffAlgorithm.js';\nimport { Array2D } from '../utils.js';\n/**\n * A O(MN) diffing algorithm that supports a score function.\n * The algorithm can be improved by processing the 2d array diagonally.\n*/\nexport class DynamicProgrammingDiffing {\n  compute(sequence1, sequence2, timeout = InfiniteTimeout.instance, equalityScore) {\n    if (sequence1.length === 0 || sequence2.length === 0) {\n      return DiffAlgorithmResult.trivial(sequence1, sequence2);\n    }\n    /**\n     * lcsLengths.get(i, j): Length of the longest common subsequence of sequence1.substring(0, i + 1) and sequence2.substring(0, j + 1).\n     */\n    const lcsLengths = new Array2D(sequence1.length, sequence2.length);\n    const directions = new Array2D(sequence1.length, sequence2.length);\n    const lengths = new Array2D(sequence1.length, sequence2.length);\n    // ==== Initializing lcsLengths ====\n    for (let s1 = 0; s1 < sequence1.length; s1++) {\n      for (let s2 = 0; s2 < sequence2.length; s2++) {\n        if (!timeout.isValid()) {\n          return DiffAlgorithmResult.trivialTimedOut(sequence1, sequence2);\n        }\n        const horizontalLen = s1 === 0 ? 0 : lcsLengths.get(s1 - 1, s2);\n        const verticalLen = s2 === 0 ? 0 : lcsLengths.get(s1, s2 - 1);\n        let extendedSeqScore;\n        if (sequence1.getElement(s1) === sequence2.getElement(s2)) {\n          if (s1 === 0 || s2 === 0) {\n            extendedSeqScore = 0;\n          } else {\n            extendedSeqScore = lcsLengths.get(s1 - 1, s2 - 1);\n          }\n          if (s1 > 0 && s2 > 0 && directions.get(s1 - 1, s2 - 1) === 3) {\n            // Prefer consecutive diagonals\n            extendedSeqScore += lengths.get(s1 - 1, s2 - 1);\n          }\n          extendedSeqScore += equalityScore ? equalityScore(s1, s2) : 1;\n        } else {\n          extendedSeqScore = -1;\n        }\n        const newValue = Math.max(horizontalLen, verticalLen, extendedSeqScore);\n        if (newValue === extendedSeqScore) {\n          // Prefer diagonals\n          const prevLen = s1 > 0 && s2 > 0 ? lengths.get(s1 - 1, s2 - 1) : 0;\n          lengths.set(s1, s2, prevLen + 1);\n          directions.set(s1, s2, 3);\n        } else if (newValue === horizontalLen) {\n          lengths.set(s1, s2, 0);\n          directions.set(s1, s2, 1);\n        } else if (newValue === verticalLen) {\n          lengths.set(s1, s2, 0);\n          directions.set(s1, s2, 2);\n        }\n        lcsLengths.set(s1, s2, newValue);\n      }\n    }\n    // ==== Backtracking ====\n    const result = [];\n    let lastAligningPosS1 = sequence1.length;\n    let lastAligningPosS2 = sequence2.length;\n    function reportDecreasingAligningPositions(s1, s2) {\n      if (s1 + 1 !== lastAligningPosS1 || s2 + 1 !== lastAligningPosS2) {\n        result.push(new SequenceDiff(new OffsetRange(s1 + 1, lastAligningPosS1), new OffsetRange(s2 + 1, lastAligningPosS2)));\n      }\n      lastAligningPosS1 = s1;\n      lastAligningPosS2 = s2;\n    }\n    let s1 = sequence1.length - 1;\n    let s2 = sequence2.length - 1;\n    while (s1 >= 0 && s2 >= 0) {\n      if (directions.get(s1, s2) === 3) {\n        reportDecreasingAligningPositions(s1, s2);\n        s1--;\n        s2--;\n      } else {\n        if (directions.get(s1, s2) === 1) {\n          s1--;\n        } else {\n          s2--;\n        }\n      }\n    }\n    reportDecreasingAligningPositions(-1, -1);\n    result.reverse();\n    return new DiffAlgorithmResult(result, false);\n  }\n}", "map": {"version": 3, "names": ["OffsetRange", "SequenceDiff", "InfiniteTimeout", "DiffAlgorithmResult", "Array2D", "DynamicProgrammingDiffing", "compute", "sequence1", "sequence2", "timeout", "instance", "equalityScore", "length", "trivial", "lcsLengths", "directions", "lengths", "s1", "s2", "<PERSON><PERSON><PERSON><PERSON>", "trivialTimedOut", "horizontalLen", "get", "verticalLen", "extendedSeqScore", "getElement", "newValue", "Math", "max", "prevLen", "set", "result", "lastAligningPosS1", "lastAligningPosS2", "reportDecreasingAligningPositions", "push", "reverse"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/diff/defaultLinesDiffComputer/algorithms/dynamicProgrammingDiffing.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { OffsetRange } from '../../../core/offsetRange.js';\nimport { SequenceDiff, InfiniteTimeout, DiffAlgorithmResult } from './diffAlgorithm.js';\nimport { Array2D } from '../utils.js';\n/**\n * A O(MN) diffing algorithm that supports a score function.\n * The algorithm can be improved by processing the 2d array diagonally.\n*/\nexport class DynamicProgrammingDiffing {\n    compute(sequence1, sequence2, timeout = InfiniteTimeout.instance, equalityScore) {\n        if (sequence1.length === 0 || sequence2.length === 0) {\n            return DiffAlgorithmResult.trivial(sequence1, sequence2);\n        }\n        /**\n         * lcsLengths.get(i, j): Length of the longest common subsequence of sequence1.substring(0, i + 1) and sequence2.substring(0, j + 1).\n         */\n        const lcsLengths = new Array2D(sequence1.length, sequence2.length);\n        const directions = new Array2D(sequence1.length, sequence2.length);\n        const lengths = new Array2D(sequence1.length, sequence2.length);\n        // ==== Initializing lcsLengths ====\n        for (let s1 = 0; s1 < sequence1.length; s1++) {\n            for (let s2 = 0; s2 < sequence2.length; s2++) {\n                if (!timeout.isValid()) {\n                    return DiffAlgorithmResult.trivialTimedOut(sequence1, sequence2);\n                }\n                const horizontalLen = s1 === 0 ? 0 : lcsLengths.get(s1 - 1, s2);\n                const verticalLen = s2 === 0 ? 0 : lcsLengths.get(s1, s2 - 1);\n                let extendedSeqScore;\n                if (sequence1.getElement(s1) === sequence2.getElement(s2)) {\n                    if (s1 === 0 || s2 === 0) {\n                        extendedSeqScore = 0;\n                    }\n                    else {\n                        extendedSeqScore = lcsLengths.get(s1 - 1, s2 - 1);\n                    }\n                    if (s1 > 0 && s2 > 0 && directions.get(s1 - 1, s2 - 1) === 3) {\n                        // Prefer consecutive diagonals\n                        extendedSeqScore += lengths.get(s1 - 1, s2 - 1);\n                    }\n                    extendedSeqScore += (equalityScore ? equalityScore(s1, s2) : 1);\n                }\n                else {\n                    extendedSeqScore = -1;\n                }\n                const newValue = Math.max(horizontalLen, verticalLen, extendedSeqScore);\n                if (newValue === extendedSeqScore) {\n                    // Prefer diagonals\n                    const prevLen = s1 > 0 && s2 > 0 ? lengths.get(s1 - 1, s2 - 1) : 0;\n                    lengths.set(s1, s2, prevLen + 1);\n                    directions.set(s1, s2, 3);\n                }\n                else if (newValue === horizontalLen) {\n                    lengths.set(s1, s2, 0);\n                    directions.set(s1, s2, 1);\n                }\n                else if (newValue === verticalLen) {\n                    lengths.set(s1, s2, 0);\n                    directions.set(s1, s2, 2);\n                }\n                lcsLengths.set(s1, s2, newValue);\n            }\n        }\n        // ==== Backtracking ====\n        const result = [];\n        let lastAligningPosS1 = sequence1.length;\n        let lastAligningPosS2 = sequence2.length;\n        function reportDecreasingAligningPositions(s1, s2) {\n            if (s1 + 1 !== lastAligningPosS1 || s2 + 1 !== lastAligningPosS2) {\n                result.push(new SequenceDiff(new OffsetRange(s1 + 1, lastAligningPosS1), new OffsetRange(s2 + 1, lastAligningPosS2)));\n            }\n            lastAligningPosS1 = s1;\n            lastAligningPosS2 = s2;\n        }\n        let s1 = sequence1.length - 1;\n        let s2 = sequence2.length - 1;\n        while (s1 >= 0 && s2 >= 0) {\n            if (directions.get(s1, s2) === 3) {\n                reportDecreasingAligningPositions(s1, s2);\n                s1--;\n                s2--;\n            }\n            else {\n                if (directions.get(s1, s2) === 1) {\n                    s1--;\n                }\n                else {\n                    s2--;\n                }\n            }\n        }\n        reportDecreasingAligningPositions(-1, -1);\n        result.reverse();\n        return new DiffAlgorithmResult(result, false);\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,WAAW,QAAQ,8BAA8B;AAC1D,SAASC,YAAY,EAAEC,eAAe,EAAEC,mBAAmB,QAAQ,oBAAoB;AACvF,SAASC,OAAO,QAAQ,aAAa;AACrC;AACA;AACA;AACA;AACA,OAAO,MAAMC,yBAAyB,CAAC;EACnCC,OAAOA,CAACC,SAAS,EAAEC,SAAS,EAAEC,OAAO,GAAGP,eAAe,CAACQ,QAAQ,EAAEC,aAAa,EAAE;IAC7E,IAAIJ,SAAS,CAACK,MAAM,KAAK,CAAC,IAAIJ,SAAS,CAACI,MAAM,KAAK,CAAC,EAAE;MAClD,OAAOT,mBAAmB,CAACU,OAAO,CAACN,SAAS,EAAEC,SAAS,CAAC;IAC5D;IACA;AACR;AACA;IACQ,MAAMM,UAAU,GAAG,IAAIV,OAAO,CAACG,SAAS,CAACK,MAAM,EAAEJ,SAAS,CAACI,MAAM,CAAC;IAClE,MAAMG,UAAU,GAAG,IAAIX,OAAO,CAACG,SAAS,CAACK,MAAM,EAAEJ,SAAS,CAACI,MAAM,CAAC;IAClE,MAAMI,OAAO,GAAG,IAAIZ,OAAO,CAACG,SAAS,CAACK,MAAM,EAAEJ,SAAS,CAACI,MAAM,CAAC;IAC/D;IACA,KAAK,IAAIK,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGV,SAAS,CAACK,MAAM,EAAEK,EAAE,EAAE,EAAE;MAC1C,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGV,SAAS,CAACI,MAAM,EAAEM,EAAE,EAAE,EAAE;QAC1C,IAAI,CAACT,OAAO,CAACU,OAAO,CAAC,CAAC,EAAE;UACpB,OAAOhB,mBAAmB,CAACiB,eAAe,CAACb,SAAS,EAAEC,SAAS,CAAC;QACpE;QACA,MAAMa,aAAa,GAAGJ,EAAE,KAAK,CAAC,GAAG,CAAC,GAAGH,UAAU,CAACQ,GAAG,CAACL,EAAE,GAAG,CAAC,EAAEC,EAAE,CAAC;QAC/D,MAAMK,WAAW,GAAGL,EAAE,KAAK,CAAC,GAAG,CAAC,GAAGJ,UAAU,CAACQ,GAAG,CAACL,EAAE,EAAEC,EAAE,GAAG,CAAC,CAAC;QAC7D,IAAIM,gBAAgB;QACpB,IAAIjB,SAAS,CAACkB,UAAU,CAACR,EAAE,CAAC,KAAKT,SAAS,CAACiB,UAAU,CAACP,EAAE,CAAC,EAAE;UACvD,IAAID,EAAE,KAAK,CAAC,IAAIC,EAAE,KAAK,CAAC,EAAE;YACtBM,gBAAgB,GAAG,CAAC;UACxB,CAAC,MACI;YACDA,gBAAgB,GAAGV,UAAU,CAACQ,GAAG,CAACL,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAG,CAAC,CAAC;UACrD;UACA,IAAID,EAAE,GAAG,CAAC,IAAIC,EAAE,GAAG,CAAC,IAAIH,UAAU,CAACO,GAAG,CAACL,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE;YAC1D;YACAM,gBAAgB,IAAIR,OAAO,CAACM,GAAG,CAACL,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAG,CAAC,CAAC;UACnD;UACAM,gBAAgB,IAAKb,aAAa,GAAGA,aAAa,CAACM,EAAE,EAAEC,EAAE,CAAC,GAAG,CAAE;QACnE,CAAC,MACI;UACDM,gBAAgB,GAAG,CAAC,CAAC;QACzB;QACA,MAAME,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACP,aAAa,EAAEE,WAAW,EAAEC,gBAAgB,CAAC;QACvE,IAAIE,QAAQ,KAAKF,gBAAgB,EAAE;UAC/B;UACA,MAAMK,OAAO,GAAGZ,EAAE,GAAG,CAAC,IAAIC,EAAE,GAAG,CAAC,GAAGF,OAAO,CAACM,GAAG,CAACL,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC;UAClEF,OAAO,CAACc,GAAG,CAACb,EAAE,EAAEC,EAAE,EAAEW,OAAO,GAAG,CAAC,CAAC;UAChCd,UAAU,CAACe,GAAG,CAACb,EAAE,EAAEC,EAAE,EAAE,CAAC,CAAC;QAC7B,CAAC,MACI,IAAIQ,QAAQ,KAAKL,aAAa,EAAE;UACjCL,OAAO,CAACc,GAAG,CAACb,EAAE,EAAEC,EAAE,EAAE,CAAC,CAAC;UACtBH,UAAU,CAACe,GAAG,CAACb,EAAE,EAAEC,EAAE,EAAE,CAAC,CAAC;QAC7B,CAAC,MACI,IAAIQ,QAAQ,KAAKH,WAAW,EAAE;UAC/BP,OAAO,CAACc,GAAG,CAACb,EAAE,EAAEC,EAAE,EAAE,CAAC,CAAC;UACtBH,UAAU,CAACe,GAAG,CAACb,EAAE,EAAEC,EAAE,EAAE,CAAC,CAAC;QAC7B;QACAJ,UAAU,CAACgB,GAAG,CAACb,EAAE,EAAEC,EAAE,EAAEQ,QAAQ,CAAC;MACpC;IACJ;IACA;IACA,MAAMK,MAAM,GAAG,EAAE;IACjB,IAAIC,iBAAiB,GAAGzB,SAAS,CAACK,MAAM;IACxC,IAAIqB,iBAAiB,GAAGzB,SAAS,CAACI,MAAM;IACxC,SAASsB,iCAAiCA,CAACjB,EAAE,EAAEC,EAAE,EAAE;MAC/C,IAAID,EAAE,GAAG,CAAC,KAAKe,iBAAiB,IAAId,EAAE,GAAG,CAAC,KAAKe,iBAAiB,EAAE;QAC9DF,MAAM,CAACI,IAAI,CAAC,IAAIlC,YAAY,CAAC,IAAID,WAAW,CAACiB,EAAE,GAAG,CAAC,EAAEe,iBAAiB,CAAC,EAAE,IAAIhC,WAAW,CAACkB,EAAE,GAAG,CAAC,EAAEe,iBAAiB,CAAC,CAAC,CAAC;MACzH;MACAD,iBAAiB,GAAGf,EAAE;MACtBgB,iBAAiB,GAAGf,EAAE;IAC1B;IACA,IAAID,EAAE,GAAGV,SAAS,CAACK,MAAM,GAAG,CAAC;IAC7B,IAAIM,EAAE,GAAGV,SAAS,CAACI,MAAM,GAAG,CAAC;IAC7B,OAAOK,EAAE,IAAI,CAAC,IAAIC,EAAE,IAAI,CAAC,EAAE;MACvB,IAAIH,UAAU,CAACO,GAAG,CAACL,EAAE,EAAEC,EAAE,CAAC,KAAK,CAAC,EAAE;QAC9BgB,iCAAiC,CAACjB,EAAE,EAAEC,EAAE,CAAC;QACzCD,EAAE,EAAE;QACJC,EAAE,EAAE;MACR,CAAC,MACI;QACD,IAAIH,UAAU,CAACO,GAAG,CAACL,EAAE,EAAEC,EAAE,CAAC,KAAK,CAAC,EAAE;UAC9BD,EAAE,EAAE;QACR,CAAC,MACI;UACDC,EAAE,EAAE;QACR;MACJ;IACJ;IACAgB,iCAAiC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACzCH,MAAM,CAACK,OAAO,CAAC,CAAC;IAChB,OAAO,IAAIjC,mBAAmB,CAAC4B,MAAM,EAAE,KAAK,CAAC;EACjD;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}