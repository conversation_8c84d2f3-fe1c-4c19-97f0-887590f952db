{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { createTrustedTypesPolicy } from '../../../../../../base/browser/trustedTypes.js';\nimport { applyFontInfo } from '../../../../config/domFontInfo.js';\nimport { EditorFontLigatures } from '../../../../../common/config/editorOptions.js';\nimport { StringBuilder } from '../../../../../common/core/stringBuilder.js';\nimport { LineDecoration } from '../../../../../common/viewLayout/lineDecorations.js';\nimport { RenderLineInput, renderViewLine } from '../../../../../common/viewLayout/viewLineRenderer.js';\nimport { ViewLineRenderingData } from '../../../../../common/viewModel.js';\nconst ttPolicy = createTrustedTypesPolicy('diffEditorWidget', {\n  createHTML: value => value\n});\nexport function renderLines(source, options, decorations, domNode) {\n  applyFontInfo(domNode, options.fontInfo);\n  const hasCharChanges = decorations.length > 0;\n  const sb = new StringBuilder(10000);\n  let maxCharsPerLine = 0;\n  let renderedLineCount = 0;\n  const viewLineCounts = [];\n  for (let lineIndex = 0; lineIndex < source.lineTokens.length; lineIndex++) {\n    const lineNumber = lineIndex + 1;\n    const lineTokens = source.lineTokens[lineIndex];\n    const lineBreakData = source.lineBreakData[lineIndex];\n    const actualDecorations = LineDecoration.filter(decorations, lineNumber, 1, Number.MAX_SAFE_INTEGER);\n    if (lineBreakData) {\n      let lastBreakOffset = 0;\n      for (const breakOffset of lineBreakData.breakOffsets) {\n        const viewLineTokens = lineTokens.sliceAndInflate(lastBreakOffset, breakOffset, 0);\n        maxCharsPerLine = Math.max(maxCharsPerLine, renderOriginalLine(renderedLineCount, viewLineTokens, LineDecoration.extractWrapped(actualDecorations, lastBreakOffset, breakOffset), hasCharChanges, source.mightContainNonBasicASCII, source.mightContainRTL, options, sb));\n        renderedLineCount++;\n        lastBreakOffset = breakOffset;\n      }\n      viewLineCounts.push(lineBreakData.breakOffsets.length);\n    } else {\n      viewLineCounts.push(1);\n      maxCharsPerLine = Math.max(maxCharsPerLine, renderOriginalLine(renderedLineCount, lineTokens, actualDecorations, hasCharChanges, source.mightContainNonBasicASCII, source.mightContainRTL, options, sb));\n      renderedLineCount++;\n    }\n  }\n  maxCharsPerLine += options.scrollBeyondLastColumn;\n  const html = sb.build();\n  const trustedhtml = ttPolicy ? ttPolicy.createHTML(html) : html;\n  domNode.innerHTML = trustedhtml;\n  const minWidthInPx = maxCharsPerLine * options.typicalHalfwidthCharacterWidth;\n  return {\n    heightInLines: renderedLineCount,\n    minWidthInPx,\n    viewLineCounts\n  };\n}\nexport class LineSource {\n  constructor(lineTokens, lineBreakData, mightContainNonBasicASCII, mightContainRTL) {\n    this.lineTokens = lineTokens;\n    this.lineBreakData = lineBreakData;\n    this.mightContainNonBasicASCII = mightContainNonBasicASCII;\n    this.mightContainRTL = mightContainRTL;\n  }\n}\nexport class RenderOptions {\n  static fromEditor(editor) {\n    const modifiedEditorOptions = editor.getOptions();\n    const fontInfo = modifiedEditorOptions.get(50 /* EditorOption.fontInfo */);\n    const layoutInfo = modifiedEditorOptions.get(146 /* EditorOption.layoutInfo */);\n    return new RenderOptions(editor.getModel()?.getOptions().tabSize || 0, fontInfo, modifiedEditorOptions.get(33 /* EditorOption.disableMonospaceOptimizations */), fontInfo.typicalHalfwidthCharacterWidth, modifiedEditorOptions.get(105 /* EditorOption.scrollBeyondLastColumn */), modifiedEditorOptions.get(67 /* EditorOption.lineHeight */), layoutInfo.decorationsWidth, modifiedEditorOptions.get(118 /* EditorOption.stopRenderingLineAfter */), modifiedEditorOptions.get(100 /* EditorOption.renderWhitespace */), modifiedEditorOptions.get(95 /* EditorOption.renderControlCharacters */), modifiedEditorOptions.get(51 /* EditorOption.fontLigatures */));\n  }\n  constructor(tabSize, fontInfo, disableMonospaceOptimizations, typicalHalfwidthCharacterWidth, scrollBeyondLastColumn, lineHeight, lineDecorationsWidth, stopRenderingLineAfter, renderWhitespace, renderControlCharacters, fontLigatures) {\n    this.tabSize = tabSize;\n    this.fontInfo = fontInfo;\n    this.disableMonospaceOptimizations = disableMonospaceOptimizations;\n    this.typicalHalfwidthCharacterWidth = typicalHalfwidthCharacterWidth;\n    this.scrollBeyondLastColumn = scrollBeyondLastColumn;\n    this.lineHeight = lineHeight;\n    this.lineDecorationsWidth = lineDecorationsWidth;\n    this.stopRenderingLineAfter = stopRenderingLineAfter;\n    this.renderWhitespace = renderWhitespace;\n    this.renderControlCharacters = renderControlCharacters;\n    this.fontLigatures = fontLigatures;\n  }\n}\nfunction renderOriginalLine(viewLineIdx, lineTokens, decorations, hasCharChanges, mightContainNonBasicASCII, mightContainRTL, options, sb) {\n  sb.appendString('<div class=\"view-line');\n  if (!hasCharChanges) {\n    // No char changes\n    sb.appendString(' char-delete');\n  }\n  sb.appendString('\" style=\"top:');\n  sb.appendString(String(viewLineIdx * options.lineHeight));\n  sb.appendString('px;width:1000000px;\">');\n  const lineContent = lineTokens.getLineContent();\n  const isBasicASCII = ViewLineRenderingData.isBasicASCII(lineContent, mightContainNonBasicASCII);\n  const containsRTL = ViewLineRenderingData.containsRTL(lineContent, isBasicASCII, mightContainRTL);\n  const output = renderViewLine(new RenderLineInput(options.fontInfo.isMonospace && !options.disableMonospaceOptimizations, options.fontInfo.canUseHalfwidthRightwardsArrow, lineContent, false, isBasicASCII, containsRTL, 0, lineTokens, decorations, options.tabSize, 0, options.fontInfo.spaceWidth, options.fontInfo.middotWidth, options.fontInfo.wsmiddotWidth, options.stopRenderingLineAfter, options.renderWhitespace, options.renderControlCharacters, options.fontLigatures !== EditorFontLigatures.OFF, null // Send no selections, original line cannot be selected\n  ), sb);\n  sb.appendString('</div>');\n  return output.characterMapping.getHorizontalOffset(output.characterMapping.length);\n}", "map": {"version": 3, "names": ["createTrustedTypesPolicy", "applyFontInfo", "EditorFontLigatures", "StringBuilder", "LineDecoration", "RenderLineInput", "renderViewLine", "ViewLineRenderingData", "ttPolicy", "createHTML", "value", "renderLines", "source", "options", "decorations", "domNode", "fontInfo", "has<PERSON>har<PERSON><PERSON><PERSON>", "length", "sb", "maxCharsPerLine", "renderedLineCount", "viewLineCounts", "lineIndex", "lineTokens", "lineNumber", "lineBreakData", "actualDecorations", "filter", "Number", "MAX_SAFE_INTEGER", "lastBreakOffset", "breakOffset", "breakOffsets", "viewLineTokens", "sliceAndInflate", "Math", "max", "renderOriginalLine", "extractWrapped", "mightContainNonBasicASCII", "mightContainRTL", "push", "scrollBeyondLastColumn", "html", "build", "trustedhtml", "innerHTML", "minWidthInPx", "typicalHalfwidthCharacterWidth", "heightInLines", "LineSource", "constructor", "RenderOptions", "fromEditor", "editor", "modifiedEditorOptions", "getOptions", "get", "layoutInfo", "getModel", "tabSize", "decorationsWidth", "disableMonospaceOptimizations", "lineHeight", "lineDecorationsWidth", "stopRenderingLineAfter", "renderWhitespace", "renderControlCharacters", "fontLigatures", "viewLineIdx", "appendString", "String", "lineContent", "get<PERSON>ineC<PERSON>nt", "isBasicASCII", "containsRTL", "output", "isMonospace", "canUseHalfwidthRightwardsArrow", "spaceWidth", "middotWidth", "wsmid<PERSON>t<PERSON><PERSON>th", "OFF", "characterMapping", "getHorizontalOffset"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/browser/widget/diffEditor/components/diffEditorViewZones/renderLines.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { createTrustedTypesPolicy } from '../../../../../../base/browser/trustedTypes.js';\nimport { applyFontInfo } from '../../../../config/domFontInfo.js';\nimport { EditorFontLigatures } from '../../../../../common/config/editorOptions.js';\nimport { StringBuilder } from '../../../../../common/core/stringBuilder.js';\nimport { LineDecoration } from '../../../../../common/viewLayout/lineDecorations.js';\nimport { RenderLineInput, renderViewLine } from '../../../../../common/viewLayout/viewLineRenderer.js';\nimport { ViewLineRenderingData } from '../../../../../common/viewModel.js';\nconst ttPolicy = createTrustedTypesPolicy('diffEditorWidget', { createHTML: value => value });\nexport function renderLines(source, options, decorations, domNode) {\n    applyFontInfo(domNode, options.fontInfo);\n    const hasCharChanges = (decorations.length > 0);\n    const sb = new StringBuilder(10000);\n    let maxCharsPerLine = 0;\n    let renderedLineCount = 0;\n    const viewLineCounts = [];\n    for (let lineIndex = 0; lineIndex < source.lineTokens.length; lineIndex++) {\n        const lineNumber = lineIndex + 1;\n        const lineTokens = source.lineTokens[lineIndex];\n        const lineBreakData = source.lineBreakData[lineIndex];\n        const actualDecorations = LineDecoration.filter(decorations, lineNumber, 1, Number.MAX_SAFE_INTEGER);\n        if (lineBreakData) {\n            let lastBreakOffset = 0;\n            for (const breakOffset of lineBreakData.breakOffsets) {\n                const viewLineTokens = lineTokens.sliceAndInflate(lastBreakOffset, breakOffset, 0);\n                maxCharsPerLine = Math.max(maxCharsPerLine, renderOriginalLine(renderedLineCount, viewLineTokens, LineDecoration.extractWrapped(actualDecorations, lastBreakOffset, breakOffset), hasCharChanges, source.mightContainNonBasicASCII, source.mightContainRTL, options, sb));\n                renderedLineCount++;\n                lastBreakOffset = breakOffset;\n            }\n            viewLineCounts.push(lineBreakData.breakOffsets.length);\n        }\n        else {\n            viewLineCounts.push(1);\n            maxCharsPerLine = Math.max(maxCharsPerLine, renderOriginalLine(renderedLineCount, lineTokens, actualDecorations, hasCharChanges, source.mightContainNonBasicASCII, source.mightContainRTL, options, sb));\n            renderedLineCount++;\n        }\n    }\n    maxCharsPerLine += options.scrollBeyondLastColumn;\n    const html = sb.build();\n    const trustedhtml = ttPolicy ? ttPolicy.createHTML(html) : html;\n    domNode.innerHTML = trustedhtml;\n    const minWidthInPx = (maxCharsPerLine * options.typicalHalfwidthCharacterWidth);\n    return {\n        heightInLines: renderedLineCount,\n        minWidthInPx,\n        viewLineCounts,\n    };\n}\nexport class LineSource {\n    constructor(lineTokens, lineBreakData, mightContainNonBasicASCII, mightContainRTL) {\n        this.lineTokens = lineTokens;\n        this.lineBreakData = lineBreakData;\n        this.mightContainNonBasicASCII = mightContainNonBasicASCII;\n        this.mightContainRTL = mightContainRTL;\n    }\n}\nexport class RenderOptions {\n    static fromEditor(editor) {\n        const modifiedEditorOptions = editor.getOptions();\n        const fontInfo = modifiedEditorOptions.get(50 /* EditorOption.fontInfo */);\n        const layoutInfo = modifiedEditorOptions.get(146 /* EditorOption.layoutInfo */);\n        return new RenderOptions(editor.getModel()?.getOptions().tabSize || 0, fontInfo, modifiedEditorOptions.get(33 /* EditorOption.disableMonospaceOptimizations */), fontInfo.typicalHalfwidthCharacterWidth, modifiedEditorOptions.get(105 /* EditorOption.scrollBeyondLastColumn */), modifiedEditorOptions.get(67 /* EditorOption.lineHeight */), layoutInfo.decorationsWidth, modifiedEditorOptions.get(118 /* EditorOption.stopRenderingLineAfter */), modifiedEditorOptions.get(100 /* EditorOption.renderWhitespace */), modifiedEditorOptions.get(95 /* EditorOption.renderControlCharacters */), modifiedEditorOptions.get(51 /* EditorOption.fontLigatures */));\n    }\n    constructor(tabSize, fontInfo, disableMonospaceOptimizations, typicalHalfwidthCharacterWidth, scrollBeyondLastColumn, lineHeight, lineDecorationsWidth, stopRenderingLineAfter, renderWhitespace, renderControlCharacters, fontLigatures) {\n        this.tabSize = tabSize;\n        this.fontInfo = fontInfo;\n        this.disableMonospaceOptimizations = disableMonospaceOptimizations;\n        this.typicalHalfwidthCharacterWidth = typicalHalfwidthCharacterWidth;\n        this.scrollBeyondLastColumn = scrollBeyondLastColumn;\n        this.lineHeight = lineHeight;\n        this.lineDecorationsWidth = lineDecorationsWidth;\n        this.stopRenderingLineAfter = stopRenderingLineAfter;\n        this.renderWhitespace = renderWhitespace;\n        this.renderControlCharacters = renderControlCharacters;\n        this.fontLigatures = fontLigatures;\n    }\n}\nfunction renderOriginalLine(viewLineIdx, lineTokens, decorations, hasCharChanges, mightContainNonBasicASCII, mightContainRTL, options, sb) {\n    sb.appendString('<div class=\"view-line');\n    if (!hasCharChanges) {\n        // No char changes\n        sb.appendString(' char-delete');\n    }\n    sb.appendString('\" style=\"top:');\n    sb.appendString(String(viewLineIdx * options.lineHeight));\n    sb.appendString('px;width:1000000px;\">');\n    const lineContent = lineTokens.getLineContent();\n    const isBasicASCII = ViewLineRenderingData.isBasicASCII(lineContent, mightContainNonBasicASCII);\n    const containsRTL = ViewLineRenderingData.containsRTL(lineContent, isBasicASCII, mightContainRTL);\n    const output = renderViewLine(new RenderLineInput((options.fontInfo.isMonospace && !options.disableMonospaceOptimizations), options.fontInfo.canUseHalfwidthRightwardsArrow, lineContent, false, isBasicASCII, containsRTL, 0, lineTokens, decorations, options.tabSize, 0, options.fontInfo.spaceWidth, options.fontInfo.middotWidth, options.fontInfo.wsmiddotWidth, options.stopRenderingLineAfter, options.renderWhitespace, options.renderControlCharacters, options.fontLigatures !== EditorFontLigatures.OFF, null // Send no selections, original line cannot be selected\n    ), sb);\n    sb.appendString('</div>');\n    return output.characterMapping.getHorizontalOffset(output.characterMapping.length);\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,wBAAwB,QAAQ,gDAAgD;AACzF,SAASC,aAAa,QAAQ,mCAAmC;AACjE,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,aAAa,QAAQ,6CAA6C;AAC3E,SAASC,cAAc,QAAQ,qDAAqD;AACpF,SAASC,eAAe,EAAEC,cAAc,QAAQ,sDAAsD;AACtG,SAASC,qBAAqB,QAAQ,oCAAoC;AAC1E,MAAMC,QAAQ,GAAGR,wBAAwB,CAAC,kBAAkB,EAAE;EAAES,UAAU,EAAEC,KAAK,IAAIA;AAAM,CAAC,CAAC;AAC7F,OAAO,SAASC,WAAWA,CAACC,MAAM,EAAEC,OAAO,EAAEC,WAAW,EAAEC,OAAO,EAAE;EAC/Dd,aAAa,CAACc,OAAO,EAAEF,OAAO,CAACG,QAAQ,CAAC;EACxC,MAAMC,cAAc,GAAIH,WAAW,CAACI,MAAM,GAAG,CAAE;EAC/C,MAAMC,EAAE,GAAG,IAAIhB,aAAa,CAAC,KAAK,CAAC;EACnC,IAAIiB,eAAe,GAAG,CAAC;EACvB,IAAIC,iBAAiB,GAAG,CAAC;EACzB,MAAMC,cAAc,GAAG,EAAE;EACzB,KAAK,IAAIC,SAAS,GAAG,CAAC,EAAEA,SAAS,GAAGX,MAAM,CAACY,UAAU,CAACN,MAAM,EAAEK,SAAS,EAAE,EAAE;IACvE,MAAME,UAAU,GAAGF,SAAS,GAAG,CAAC;IAChC,MAAMC,UAAU,GAAGZ,MAAM,CAACY,UAAU,CAACD,SAAS,CAAC;IAC/C,MAAMG,aAAa,GAAGd,MAAM,CAACc,aAAa,CAACH,SAAS,CAAC;IACrD,MAAMI,iBAAiB,GAAGvB,cAAc,CAACwB,MAAM,CAACd,WAAW,EAAEW,UAAU,EAAE,CAAC,EAAEI,MAAM,CAACC,gBAAgB,CAAC;IACpG,IAAIJ,aAAa,EAAE;MACf,IAAIK,eAAe,GAAG,CAAC;MACvB,KAAK,MAAMC,WAAW,IAAIN,aAAa,CAACO,YAAY,EAAE;QAClD,MAAMC,cAAc,GAAGV,UAAU,CAACW,eAAe,CAACJ,eAAe,EAAEC,WAAW,EAAE,CAAC,CAAC;QAClFZ,eAAe,GAAGgB,IAAI,CAACC,GAAG,CAACjB,eAAe,EAAEkB,kBAAkB,CAACjB,iBAAiB,EAAEa,cAAc,EAAE9B,cAAc,CAACmC,cAAc,CAACZ,iBAAiB,EAAEI,eAAe,EAAEC,WAAW,CAAC,EAAEf,cAAc,EAAEL,MAAM,CAAC4B,yBAAyB,EAAE5B,MAAM,CAAC6B,eAAe,EAAE5B,OAAO,EAAEM,EAAE,CAAC,CAAC;QACzQE,iBAAiB,EAAE;QACnBU,eAAe,GAAGC,WAAW;MACjC;MACAV,cAAc,CAACoB,IAAI,CAAChB,aAAa,CAACO,YAAY,CAACf,MAAM,CAAC;IAC1D,CAAC,MACI;MACDI,cAAc,CAACoB,IAAI,CAAC,CAAC,CAAC;MACtBtB,eAAe,GAAGgB,IAAI,CAACC,GAAG,CAACjB,eAAe,EAAEkB,kBAAkB,CAACjB,iBAAiB,EAAEG,UAAU,EAAEG,iBAAiB,EAAEV,cAAc,EAAEL,MAAM,CAAC4B,yBAAyB,EAAE5B,MAAM,CAAC6B,eAAe,EAAE5B,OAAO,EAAEM,EAAE,CAAC,CAAC;MACxME,iBAAiB,EAAE;IACvB;EACJ;EACAD,eAAe,IAAIP,OAAO,CAAC8B,sBAAsB;EACjD,MAAMC,IAAI,GAAGzB,EAAE,CAAC0B,KAAK,CAAC,CAAC;EACvB,MAAMC,WAAW,GAAGtC,QAAQ,GAAGA,QAAQ,CAACC,UAAU,CAACmC,IAAI,CAAC,GAAGA,IAAI;EAC/D7B,OAAO,CAACgC,SAAS,GAAGD,WAAW;EAC/B,MAAME,YAAY,GAAI5B,eAAe,GAAGP,OAAO,CAACoC,8BAA+B;EAC/E,OAAO;IACHC,aAAa,EAAE7B,iBAAiB;IAChC2B,YAAY;IACZ1B;EACJ,CAAC;AACL;AACA,OAAO,MAAM6B,UAAU,CAAC;EACpBC,WAAWA,CAAC5B,UAAU,EAAEE,aAAa,EAAEc,yBAAyB,EAAEC,eAAe,EAAE;IAC/E,IAAI,CAACjB,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACE,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACc,yBAAyB,GAAGA,yBAAyB;IAC1D,IAAI,CAACC,eAAe,GAAGA,eAAe;EAC1C;AACJ;AACA,OAAO,MAAMY,aAAa,CAAC;EACvB,OAAOC,UAAUA,CAACC,MAAM,EAAE;IACtB,MAAMC,qBAAqB,GAAGD,MAAM,CAACE,UAAU,CAAC,CAAC;IACjD,MAAMzC,QAAQ,GAAGwC,qBAAqB,CAACE,GAAG,CAAC,EAAE,CAAC,2BAA2B,CAAC;IAC1E,MAAMC,UAAU,GAAGH,qBAAqB,CAACE,GAAG,CAAC,GAAG,CAAC,6BAA6B,CAAC;IAC/E,OAAO,IAAIL,aAAa,CAACE,MAAM,CAACK,QAAQ,CAAC,CAAC,EAAEH,UAAU,CAAC,CAAC,CAACI,OAAO,IAAI,CAAC,EAAE7C,QAAQ,EAAEwC,qBAAqB,CAACE,GAAG,CAAC,EAAE,CAAC,gDAAgD,CAAC,EAAE1C,QAAQ,CAACiC,8BAA8B,EAAEO,qBAAqB,CAACE,GAAG,CAAC,GAAG,CAAC,yCAAyC,CAAC,EAAEF,qBAAqB,CAACE,GAAG,CAAC,EAAE,CAAC,6BAA6B,CAAC,EAAEC,UAAU,CAACG,gBAAgB,EAAEN,qBAAqB,CAACE,GAAG,CAAC,GAAG,CAAC,yCAAyC,CAAC,EAAEF,qBAAqB,CAACE,GAAG,CAAC,GAAG,CAAC,mCAAmC,CAAC,EAAEF,qBAAqB,CAACE,GAAG,CAAC,EAAE,CAAC,0CAA0C,CAAC,EAAEF,qBAAqB,CAACE,GAAG,CAAC,EAAE,CAAC,gCAAgC,CAAC,CAAC;EACzoB;EACAN,WAAWA,CAACS,OAAO,EAAE7C,QAAQ,EAAE+C,6BAA6B,EAAEd,8BAA8B,EAAEN,sBAAsB,EAAEqB,UAAU,EAAEC,oBAAoB,EAAEC,sBAAsB,EAAEC,gBAAgB,EAAEC,uBAAuB,EAAEC,aAAa,EAAE;IACtO,IAAI,CAACR,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC7C,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAAC+C,6BAA6B,GAAGA,6BAA6B;IAClE,IAAI,CAACd,8BAA8B,GAAGA,8BAA8B;IACpE,IAAI,CAACN,sBAAsB,GAAGA,sBAAsB;IACpD,IAAI,CAACqB,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,oBAAoB,GAAGA,oBAAoB;IAChD,IAAI,CAACC,sBAAsB,GAAGA,sBAAsB;IACpD,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACC,uBAAuB,GAAGA,uBAAuB;IACtD,IAAI,CAACC,aAAa,GAAGA,aAAa;EACtC;AACJ;AACA,SAAS/B,kBAAkBA,CAACgC,WAAW,EAAE9C,UAAU,EAAEV,WAAW,EAAEG,cAAc,EAAEuB,yBAAyB,EAAEC,eAAe,EAAE5B,OAAO,EAAEM,EAAE,EAAE;EACvIA,EAAE,CAACoD,YAAY,CAAC,uBAAuB,CAAC;EACxC,IAAI,CAACtD,cAAc,EAAE;IACjB;IACAE,EAAE,CAACoD,YAAY,CAAC,cAAc,CAAC;EACnC;EACApD,EAAE,CAACoD,YAAY,CAAC,eAAe,CAAC;EAChCpD,EAAE,CAACoD,YAAY,CAACC,MAAM,CAACF,WAAW,GAAGzD,OAAO,CAACmD,UAAU,CAAC,CAAC;EACzD7C,EAAE,CAACoD,YAAY,CAAC,uBAAuB,CAAC;EACxC,MAAME,WAAW,GAAGjD,UAAU,CAACkD,cAAc,CAAC,CAAC;EAC/C,MAAMC,YAAY,GAAGpE,qBAAqB,CAACoE,YAAY,CAACF,WAAW,EAAEjC,yBAAyB,CAAC;EAC/F,MAAMoC,WAAW,GAAGrE,qBAAqB,CAACqE,WAAW,CAACH,WAAW,EAAEE,YAAY,EAAElC,eAAe,CAAC;EACjG,MAAMoC,MAAM,GAAGvE,cAAc,CAAC,IAAID,eAAe,CAAEQ,OAAO,CAACG,QAAQ,CAAC8D,WAAW,IAAI,CAACjE,OAAO,CAACkD,6BAA6B,EAAGlD,OAAO,CAACG,QAAQ,CAAC+D,8BAA8B,EAAEN,WAAW,EAAE,KAAK,EAAEE,YAAY,EAAEC,WAAW,EAAE,CAAC,EAAEpD,UAAU,EAAEV,WAAW,EAAED,OAAO,CAACgD,OAAO,EAAE,CAAC,EAAEhD,OAAO,CAACG,QAAQ,CAACgE,UAAU,EAAEnE,OAAO,CAACG,QAAQ,CAACiE,WAAW,EAAEpE,OAAO,CAACG,QAAQ,CAACkE,aAAa,EAAErE,OAAO,CAACqD,sBAAsB,EAAErD,OAAO,CAACsD,gBAAgB,EAAEtD,OAAO,CAACuD,uBAAuB,EAAEvD,OAAO,CAACwD,aAAa,KAAKnE,mBAAmB,CAACiF,GAAG,EAAE,IAAI,CAAC;EAC1f,CAAC,EAAEhE,EAAE,CAAC;EACNA,EAAE,CAACoD,YAAY,CAAC,QAAQ,CAAC;EACzB,OAAOM,MAAM,CAACO,gBAAgB,CAACC,mBAAmB,CAACR,MAAM,CAACO,gBAAgB,CAAClE,MAAM,CAAC;AACtF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}