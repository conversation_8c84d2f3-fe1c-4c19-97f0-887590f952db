{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { runWhenGlobalIdle } from '../../../base/common/async.js';\nimport { BugIndicatingError, onUnexpectedError } from '../../../base/common/errors.js';\nimport { setTimeout0 } from '../../../base/common/platform.js';\nimport { StopWatch } from '../../../base/common/stopwatch.js';\nimport { countEOL } from '../core/eolCounter.js';\nimport { LineRange } from '../core/lineRange.js';\nimport { OffsetRange } from '../core/offsetRange.js';\nimport { nullTokenizeEncoded } from '../languages/nullTokenize.js';\nimport { FixedArray } from './fixedArray.js';\nimport { ContiguousMultilineTokensBuilder } from '../tokens/contiguousMultilineTokensBuilder.js';\nimport { LineTokens } from '../tokens/lineTokens.js';\nexport class TokenizerWithStateStore {\n  constructor(lineCount, tokenizationSupport) {\n    this.tokenizationSupport = tokenizationSupport;\n    this.initialState = this.tokenizationSupport.getInitialState();\n    this.store = new TrackingTokenizationStateStore(lineCount);\n  }\n  getStartState(lineNumber) {\n    return this.store.getStartState(lineNumber, this.initialState);\n  }\n  getFirstInvalidLine() {\n    return this.store.getFirstInvalidLine(this.initialState);\n  }\n}\nexport class TokenizerWithStateStoreAndTextModel extends TokenizerWithStateStore {\n  constructor(lineCount, tokenizationSupport, _textModel, _languageIdCodec) {\n    super(lineCount, tokenizationSupport);\n    this._textModel = _textModel;\n    this._languageIdCodec = _languageIdCodec;\n  }\n  updateTokensUntilLine(builder, lineNumber) {\n    const languageId = this._textModel.getLanguageId();\n    while (true) {\n      const lineToTokenize = this.getFirstInvalidLine();\n      if (!lineToTokenize || lineToTokenize.lineNumber > lineNumber) {\n        break;\n      }\n      const text = this._textModel.getLineContent(lineToTokenize.lineNumber);\n      const r = safeTokenize(this._languageIdCodec, languageId, this.tokenizationSupport, text, true, lineToTokenize.startState);\n      builder.add(lineToTokenize.lineNumber, r.tokens);\n      this.store.setEndState(lineToTokenize.lineNumber, r.endState);\n    }\n  }\n  /** assumes state is up to date */\n  getTokenTypeIfInsertingCharacter(position, character) {\n    // TODO@hediet: use tokenizeLineWithEdit\n    const lineStartState = this.getStartState(position.lineNumber);\n    if (!lineStartState) {\n      return 0 /* StandardTokenType.Other */;\n    }\n    const languageId = this._textModel.getLanguageId();\n    const lineContent = this._textModel.getLineContent(position.lineNumber);\n    // Create the text as if `character` was inserted\n    const text = lineContent.substring(0, position.column - 1) + character + lineContent.substring(position.column - 1);\n    const r = safeTokenize(this._languageIdCodec, languageId, this.tokenizationSupport, text, true, lineStartState);\n    const lineTokens = new LineTokens(r.tokens, text, this._languageIdCodec);\n    if (lineTokens.getCount() === 0) {\n      return 0 /* StandardTokenType.Other */;\n    }\n    const tokenIndex = lineTokens.findTokenIndexAtOffset(position.column - 1);\n    return lineTokens.getStandardTokenType(tokenIndex);\n  }\n  /** assumes state is up to date */\n  tokenizeLineWithEdit(position, length, newText) {\n    const lineNumber = position.lineNumber;\n    const column = position.column;\n    const lineStartState = this.getStartState(lineNumber);\n    if (!lineStartState) {\n      return null;\n    }\n    const curLineContent = this._textModel.getLineContent(lineNumber);\n    const newLineContent = curLineContent.substring(0, column - 1) + newText + curLineContent.substring(column - 1 + length);\n    const languageId = this._textModel.getLanguageIdAtPosition(lineNumber, 0);\n    const result = safeTokenize(this._languageIdCodec, languageId, this.tokenizationSupport, newLineContent, true, lineStartState);\n    const lineTokens = new LineTokens(result.tokens, newLineContent, this._languageIdCodec);\n    return lineTokens;\n  }\n  hasAccurateTokensForLine(lineNumber) {\n    const firstInvalidLineNumber = this.store.getFirstInvalidEndStateLineNumberOrMax();\n    return lineNumber < firstInvalidLineNumber;\n  }\n  isCheapToTokenize(lineNumber) {\n    const firstInvalidLineNumber = this.store.getFirstInvalidEndStateLineNumberOrMax();\n    if (lineNumber < firstInvalidLineNumber) {\n      return true;\n    }\n    if (lineNumber === firstInvalidLineNumber && this._textModel.getLineLength(lineNumber) < 2048 /* Constants.CHEAP_TOKENIZATION_LENGTH_LIMIT */) {\n      return true;\n    }\n    return false;\n  }\n  /**\n   * The result is not cached.\n   */\n  tokenizeHeuristically(builder, startLineNumber, endLineNumber) {\n    if (endLineNumber <= this.store.getFirstInvalidEndStateLineNumberOrMax()) {\n      // nothing to do\n      return {\n        heuristicTokens: false\n      };\n    }\n    if (startLineNumber <= this.store.getFirstInvalidEndStateLineNumberOrMax()) {\n      // tokenization has reached the viewport start...\n      this.updateTokensUntilLine(builder, endLineNumber);\n      return {\n        heuristicTokens: false\n      };\n    }\n    let state = this.guessStartState(startLineNumber);\n    const languageId = this._textModel.getLanguageId();\n    for (let lineNumber = startLineNumber; lineNumber <= endLineNumber; lineNumber++) {\n      const text = this._textModel.getLineContent(lineNumber);\n      const r = safeTokenize(this._languageIdCodec, languageId, this.tokenizationSupport, text, true, state);\n      builder.add(lineNumber, r.tokens);\n      state = r.endState;\n    }\n    return {\n      heuristicTokens: true\n    };\n  }\n  guessStartState(lineNumber) {\n    let nonWhitespaceColumn = this._textModel.getLineFirstNonWhitespaceColumn(lineNumber);\n    const likelyRelevantLines = [];\n    let initialState = null;\n    for (let i = lineNumber - 1; nonWhitespaceColumn > 1 && i >= 1; i--) {\n      const newNonWhitespaceIndex = this._textModel.getLineFirstNonWhitespaceColumn(i);\n      // Ignore lines full of whitespace\n      if (newNonWhitespaceIndex === 0) {\n        continue;\n      }\n      if (newNonWhitespaceIndex < nonWhitespaceColumn) {\n        likelyRelevantLines.push(this._textModel.getLineContent(i));\n        nonWhitespaceColumn = newNonWhitespaceIndex;\n        initialState = this.getStartState(i);\n        if (initialState) {\n          break;\n        }\n      }\n    }\n    if (!initialState) {\n      initialState = this.tokenizationSupport.getInitialState();\n    }\n    likelyRelevantLines.reverse();\n    const languageId = this._textModel.getLanguageId();\n    let state = initialState;\n    for (const line of likelyRelevantLines) {\n      const r = safeTokenize(this._languageIdCodec, languageId, this.tokenizationSupport, line, false, state);\n      state = r.endState;\n    }\n    return state;\n  }\n}\n/**\n * **Invariant:**\n * If the text model is retokenized from line 1 to {@link getFirstInvalidEndStateLineNumber}() - 1,\n * then the recomputed end state for line l will be equal to {@link getEndState}(l).\n */\nexport class TrackingTokenizationStateStore {\n  constructor(lineCount) {\n    this.lineCount = lineCount;\n    this._tokenizationStateStore = new TokenizationStateStore();\n    this._invalidEndStatesLineNumbers = new RangePriorityQueueImpl();\n    this._invalidEndStatesLineNumbers.addRange(new OffsetRange(1, lineCount + 1));\n  }\n  getEndState(lineNumber) {\n    return this._tokenizationStateStore.getEndState(lineNumber);\n  }\n  /**\n   * @returns if the end state has changed.\n   */\n  setEndState(lineNumber, state) {\n    if (!state) {\n      throw new BugIndicatingError('Cannot set null/undefined state');\n    }\n    this._invalidEndStatesLineNumbers.delete(lineNumber);\n    const r = this._tokenizationStateStore.setEndState(lineNumber, state);\n    if (r && lineNumber < this.lineCount) {\n      // because the state changed, we cannot trust the next state anymore and have to invalidate it.\n      this._invalidEndStatesLineNumbers.addRange(new OffsetRange(lineNumber + 1, lineNumber + 2));\n    }\n    return r;\n  }\n  acceptChange(range, newLineCount) {\n    this.lineCount += newLineCount - range.length;\n    this._tokenizationStateStore.acceptChange(range, newLineCount);\n    this._invalidEndStatesLineNumbers.addRangeAndResize(new OffsetRange(range.startLineNumber, range.endLineNumberExclusive), newLineCount);\n  }\n  acceptChanges(changes) {\n    for (const c of changes) {\n      const [eolCount] = countEOL(c.text);\n      this.acceptChange(new LineRange(c.range.startLineNumber, c.range.endLineNumber + 1), eolCount + 1);\n    }\n  }\n  invalidateEndStateRange(range) {\n    this._invalidEndStatesLineNumbers.addRange(new OffsetRange(range.startLineNumber, range.endLineNumberExclusive));\n  }\n  getFirstInvalidEndStateLineNumber() {\n    return this._invalidEndStatesLineNumbers.min;\n  }\n  getFirstInvalidEndStateLineNumberOrMax() {\n    return this.getFirstInvalidEndStateLineNumber() || Number.MAX_SAFE_INTEGER;\n  }\n  allStatesValid() {\n    return this._invalidEndStatesLineNumbers.min === null;\n  }\n  getStartState(lineNumber, initialState) {\n    if (lineNumber === 1) {\n      return initialState;\n    }\n    return this.getEndState(lineNumber - 1);\n  }\n  getFirstInvalidLine(initialState) {\n    const lineNumber = this.getFirstInvalidEndStateLineNumber();\n    if (lineNumber === null) {\n      return null;\n    }\n    const startState = this.getStartState(lineNumber, initialState);\n    if (!startState) {\n      throw new BugIndicatingError('Start state must be defined');\n    }\n    return {\n      lineNumber,\n      startState\n    };\n  }\n}\nexport class TokenizationStateStore {\n  constructor() {\n    this._lineEndStates = new FixedArray(null);\n  }\n  getEndState(lineNumber) {\n    return this._lineEndStates.get(lineNumber);\n  }\n  setEndState(lineNumber, state) {\n    const oldState = this._lineEndStates.get(lineNumber);\n    if (oldState && oldState.equals(state)) {\n      return false;\n    }\n    this._lineEndStates.set(lineNumber, state);\n    return true;\n  }\n  acceptChange(range, newLineCount) {\n    let length = range.length;\n    if (newLineCount > 0 && length > 0) {\n      // Keep the last state, even though it is unrelated.\n      // But if the new state happens to agree with this last state, then we know we can stop tokenizing.\n      length--;\n      newLineCount--;\n    }\n    this._lineEndStates.replace(range.startLineNumber, length, newLineCount);\n  }\n}\nexport class RangePriorityQueueImpl {\n  constructor() {\n    this._ranges = [];\n  }\n  get min() {\n    if (this._ranges.length === 0) {\n      return null;\n    }\n    return this._ranges[0].start;\n  }\n  delete(value) {\n    const idx = this._ranges.findIndex(r => r.contains(value));\n    if (idx !== -1) {\n      const range = this._ranges[idx];\n      if (range.start === value) {\n        if (range.endExclusive === value + 1) {\n          this._ranges.splice(idx, 1);\n        } else {\n          this._ranges[idx] = new OffsetRange(value + 1, range.endExclusive);\n        }\n      } else {\n        if (range.endExclusive === value + 1) {\n          this._ranges[idx] = new OffsetRange(range.start, value);\n        } else {\n          this._ranges.splice(idx, 1, new OffsetRange(range.start, value), new OffsetRange(value + 1, range.endExclusive));\n        }\n      }\n    }\n  }\n  addRange(range) {\n    OffsetRange.addRange(range, this._ranges);\n  }\n  addRangeAndResize(range, newLength) {\n    let idxFirstMightBeIntersecting = 0;\n    while (!(idxFirstMightBeIntersecting >= this._ranges.length || range.start <= this._ranges[idxFirstMightBeIntersecting].endExclusive)) {\n      idxFirstMightBeIntersecting++;\n    }\n    let idxFirstIsAfter = idxFirstMightBeIntersecting;\n    while (!(idxFirstIsAfter >= this._ranges.length || range.endExclusive < this._ranges[idxFirstIsAfter].start)) {\n      idxFirstIsAfter++;\n    }\n    const delta = newLength - range.length;\n    for (let i = idxFirstIsAfter; i < this._ranges.length; i++) {\n      this._ranges[i] = this._ranges[i].delta(delta);\n    }\n    if (idxFirstMightBeIntersecting === idxFirstIsAfter) {\n      const newRange = new OffsetRange(range.start, range.start + newLength);\n      if (!newRange.isEmpty) {\n        this._ranges.splice(idxFirstMightBeIntersecting, 0, newRange);\n      }\n    } else {\n      const start = Math.min(range.start, this._ranges[idxFirstMightBeIntersecting].start);\n      const endEx = Math.max(range.endExclusive, this._ranges[idxFirstIsAfter - 1].endExclusive);\n      const newRange = new OffsetRange(start, endEx + delta);\n      if (!newRange.isEmpty) {\n        this._ranges.splice(idxFirstMightBeIntersecting, idxFirstIsAfter - idxFirstMightBeIntersecting, newRange);\n      } else {\n        this._ranges.splice(idxFirstMightBeIntersecting, idxFirstIsAfter - idxFirstMightBeIntersecting);\n      }\n    }\n  }\n  toString() {\n    return this._ranges.map(r => r.toString()).join(' + ');\n  }\n}\nfunction safeTokenize(languageIdCodec, languageId, tokenizationSupport, text, hasEOL, state) {\n  let r = null;\n  if (tokenizationSupport) {\n    try {\n      r = tokenizationSupport.tokenizeEncoded(text, hasEOL, state.clone());\n    } catch (e) {\n      onUnexpectedError(e);\n    }\n  }\n  if (!r) {\n    r = nullTokenizeEncoded(languageIdCodec.encodeLanguageId(languageId), state);\n  }\n  LineTokens.convertToEndOffset(r.tokens, text.length);\n  return r;\n}\nexport class DefaultBackgroundTokenizer {\n  constructor(_tokenizerWithStateStore, _backgroundTokenStore) {\n    this._tokenizerWithStateStore = _tokenizerWithStateStore;\n    this._backgroundTokenStore = _backgroundTokenStore;\n    this._isDisposed = false;\n    this._isScheduled = false;\n  }\n  dispose() {\n    this._isDisposed = true;\n  }\n  handleChanges() {\n    this._beginBackgroundTokenization();\n  }\n  _beginBackgroundTokenization() {\n    if (this._isScheduled || !this._tokenizerWithStateStore._textModel.isAttachedToEditor() || !this._hasLinesToTokenize()) {\n      return;\n    }\n    this._isScheduled = true;\n    runWhenGlobalIdle(deadline => {\n      this._isScheduled = false;\n      this._backgroundTokenizeWithDeadline(deadline);\n    });\n  }\n  /**\n   * Tokenize until the deadline occurs, but try to yield every 1-2ms.\n   */\n  _backgroundTokenizeWithDeadline(deadline) {\n    // Read the time remaining from the `deadline` immediately because it is unclear\n    // if the `deadline` object will be valid after execution leaves this function.\n    const endTime = Date.now() + deadline.timeRemaining();\n    const execute = () => {\n      if (this._isDisposed || !this._tokenizerWithStateStore._textModel.isAttachedToEditor() || !this._hasLinesToTokenize()) {\n        // disposed in the meantime or detached or finished\n        return;\n      }\n      this._backgroundTokenizeForAtLeast1ms();\n      if (Date.now() < endTime) {\n        // There is still time before reaching the deadline, so yield to the browser and then\n        // continue execution\n        setTimeout0(execute);\n      } else {\n        // The deadline has been reached, so schedule a new idle callback if necessary\n        this._beginBackgroundTokenization();\n      }\n    };\n    execute();\n  }\n  /**\n   * Tokenize for at least 1ms.\n   */\n  _backgroundTokenizeForAtLeast1ms() {\n    const lineCount = this._tokenizerWithStateStore._textModel.getLineCount();\n    const builder = new ContiguousMultilineTokensBuilder();\n    const sw = StopWatch.create(false);\n    do {\n      if (sw.elapsed() > 1) {\n        // the comparison is intentionally > 1 and not >= 1 to ensure that\n        // a full millisecond has elapsed, given how microseconds are rounded\n        // to milliseconds\n        break;\n      }\n      const tokenizedLineNumber = this._tokenizeOneInvalidLine(builder);\n      if (tokenizedLineNumber >= lineCount) {\n        break;\n      }\n    } while (this._hasLinesToTokenize());\n    this._backgroundTokenStore.setTokens(builder.finalize());\n    this.checkFinished();\n  }\n  _hasLinesToTokenize() {\n    if (!this._tokenizerWithStateStore) {\n      return false;\n    }\n    return !this._tokenizerWithStateStore.store.allStatesValid();\n  }\n  _tokenizeOneInvalidLine(builder) {\n    const firstInvalidLine = this._tokenizerWithStateStore?.getFirstInvalidLine();\n    if (!firstInvalidLine) {\n      return this._tokenizerWithStateStore._textModel.getLineCount() + 1;\n    }\n    this._tokenizerWithStateStore.updateTokensUntilLine(builder, firstInvalidLine.lineNumber);\n    return firstInvalidLine.lineNumber;\n  }\n  checkFinished() {\n    if (this._isDisposed) {\n      return;\n    }\n    if (this._tokenizerWithStateStore.store.allStatesValid()) {\n      this._backgroundTokenStore.backgroundTokenizationFinished();\n    }\n  }\n  requestTokens(startLineNumber, endLineNumberExclusive) {\n    this._tokenizerWithStateStore.store.invalidateEndStateRange(new LineRange(startLineNumber, endLineNumberExclusive));\n  }\n}", "map": {"version": 3, "names": ["runWhenGlobalIdle", "BugIndicatingError", "onUnexpectedError", "setTimeout0", "StopWatch", "countEOL", "LineRange", "OffsetRange", "nullTokenizeEncoded", "FixedArray", "ContiguousMultilineTokensBuilder", "LineTokens", "TokenizerWithStateStore", "constructor", "lineCount", "tokenizationSupport", "initialState", "getInitialState", "store", "TrackingTokenizationStateStore", "getStartState", "lineNumber", "getFirstInvalidLine", "TokenizerWithStateStoreAndTextModel", "_textModel", "_languageIdCodec", "updateTokensUntilLine", "builder", "languageId", "getLanguageId", "lineToTokenize", "text", "get<PERSON>ineC<PERSON>nt", "r", "safeTokenize", "startState", "add", "tokens", "setEndState", "endState", "getTokenTypeIfInsertingCharacter", "position", "character", "lineStartState", "lineContent", "substring", "column", "lineTokens", "getCount", "tokenIndex", "findTokenIndexAtOffset", "getStandardTokenType", "tokenizeLineWithEdit", "length", "newText", "cur<PERSON><PERSON><PERSON><PERSON><PERSON>", "newLineContent", "getLanguageIdAtPosition", "result", "hasAccurateTokensForLine", "firstInvalidLineNumber", "getFirstInvalidEndStateLineNumberOrMax", "isCheapToTokenize", "getLine<PERSON><PERSON>th", "tokenizeHeuristically", "startLineNumber", "endLineNumber", "heuristicTokens", "state", "guessStartState", "nonWhitespaceColumn", "getLineFirstNonWhitespaceColumn", "likelyRelevantLines", "i", "newNonWhitespaceIndex", "push", "reverse", "line", "_tokenizationStateStore", "TokenizationStateStore", "_invalidEndStatesLineNumbers", "RangePriorityQueueImpl", "addRange", "getEndState", "delete", "acceptChange", "range", "newLineCount", "addRangeAndResize", "endLineNumberExclusive", "acceptChanges", "changes", "c", "eolCount", "invalidateEndStateRange", "getFirstInvalidEndStateLineNumber", "min", "Number", "MAX_SAFE_INTEGER", "allStatesValid", "_lineEndStates", "get", "oldState", "equals", "set", "replace", "_ranges", "start", "value", "idx", "findIndex", "contains", "endExclusive", "splice", "<PERSON><PERSON><PERSON><PERSON>", "idxFirstMightBeIntersecting", "idxFirstIsAfter", "delta", "newRange", "isEmpty", "Math", "endEx", "max", "toString", "map", "join", "languageIdCodec", "hasEOL", "tokenizeEncoded", "clone", "e", "encodeLanguageId", "convertToEndOffset", "DefaultBackgroundTokenizer", "_tokenizerWithStateStore", "_backgroundTokenStore", "_isDisposed", "_isScheduled", "dispose", "handleChanges", "_beginBackgroundTokenization", "isAttachedToEditor", "_hasLinesToTokenize", "deadline", "_backgroundTokenizeWithDeadline", "endTime", "Date", "now", "timeRemaining", "execute", "_backgroundTokenizeForAtLeast1ms", "getLineCount", "sw", "create", "elapsed", "tokenizedLineNumber", "_tokenizeOneInvalidLine", "setTokens", "finalize", "checkFinished", "firstInvalidLine", "backgroundTokenizationFinished", "requestTokens"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/model/textModelTokens.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { runWhenGlobalIdle } from '../../../base/common/async.js';\nimport { BugIndicatingError, onUnexpectedError } from '../../../base/common/errors.js';\nimport { setTimeout0 } from '../../../base/common/platform.js';\nimport { StopWatch } from '../../../base/common/stopwatch.js';\nimport { countEOL } from '../core/eolCounter.js';\nimport { LineRange } from '../core/lineRange.js';\nimport { OffsetRange } from '../core/offsetRange.js';\nimport { nullTokenizeEncoded } from '../languages/nullTokenize.js';\nimport { FixedArray } from './fixedArray.js';\nimport { ContiguousMultilineTokensBuilder } from '../tokens/contiguousMultilineTokensBuilder.js';\nimport { LineTokens } from '../tokens/lineTokens.js';\nexport class TokenizerWithStateStore {\n    constructor(lineCount, tokenizationSupport) {\n        this.tokenizationSupport = tokenizationSupport;\n        this.initialState = this.tokenizationSupport.getInitialState();\n        this.store = new TrackingTokenizationStateStore(lineCount);\n    }\n    getStartState(lineNumber) {\n        return this.store.getStartState(lineNumber, this.initialState);\n    }\n    getFirstInvalidLine() {\n        return this.store.getFirstInvalidLine(this.initialState);\n    }\n}\nexport class TokenizerWithStateStoreAndTextModel extends TokenizerWithStateStore {\n    constructor(lineCount, tokenizationSupport, _textModel, _languageIdCodec) {\n        super(lineCount, tokenizationSupport);\n        this._textModel = _textModel;\n        this._languageIdCodec = _languageIdCodec;\n    }\n    updateTokensUntilLine(builder, lineNumber) {\n        const languageId = this._textModel.getLanguageId();\n        while (true) {\n            const lineToTokenize = this.getFirstInvalidLine();\n            if (!lineToTokenize || lineToTokenize.lineNumber > lineNumber) {\n                break;\n            }\n            const text = this._textModel.getLineContent(lineToTokenize.lineNumber);\n            const r = safeTokenize(this._languageIdCodec, languageId, this.tokenizationSupport, text, true, lineToTokenize.startState);\n            builder.add(lineToTokenize.lineNumber, r.tokens);\n            this.store.setEndState(lineToTokenize.lineNumber, r.endState);\n        }\n    }\n    /** assumes state is up to date */\n    getTokenTypeIfInsertingCharacter(position, character) {\n        // TODO@hediet: use tokenizeLineWithEdit\n        const lineStartState = this.getStartState(position.lineNumber);\n        if (!lineStartState) {\n            return 0 /* StandardTokenType.Other */;\n        }\n        const languageId = this._textModel.getLanguageId();\n        const lineContent = this._textModel.getLineContent(position.lineNumber);\n        // Create the text as if `character` was inserted\n        const text = (lineContent.substring(0, position.column - 1)\n            + character\n            + lineContent.substring(position.column - 1));\n        const r = safeTokenize(this._languageIdCodec, languageId, this.tokenizationSupport, text, true, lineStartState);\n        const lineTokens = new LineTokens(r.tokens, text, this._languageIdCodec);\n        if (lineTokens.getCount() === 0) {\n            return 0 /* StandardTokenType.Other */;\n        }\n        const tokenIndex = lineTokens.findTokenIndexAtOffset(position.column - 1);\n        return lineTokens.getStandardTokenType(tokenIndex);\n    }\n    /** assumes state is up to date */\n    tokenizeLineWithEdit(position, length, newText) {\n        const lineNumber = position.lineNumber;\n        const column = position.column;\n        const lineStartState = this.getStartState(lineNumber);\n        if (!lineStartState) {\n            return null;\n        }\n        const curLineContent = this._textModel.getLineContent(lineNumber);\n        const newLineContent = curLineContent.substring(0, column - 1)\n            + newText + curLineContent.substring(column - 1 + length);\n        const languageId = this._textModel.getLanguageIdAtPosition(lineNumber, 0);\n        const result = safeTokenize(this._languageIdCodec, languageId, this.tokenizationSupport, newLineContent, true, lineStartState);\n        const lineTokens = new LineTokens(result.tokens, newLineContent, this._languageIdCodec);\n        return lineTokens;\n    }\n    hasAccurateTokensForLine(lineNumber) {\n        const firstInvalidLineNumber = this.store.getFirstInvalidEndStateLineNumberOrMax();\n        return (lineNumber < firstInvalidLineNumber);\n    }\n    isCheapToTokenize(lineNumber) {\n        const firstInvalidLineNumber = this.store.getFirstInvalidEndStateLineNumberOrMax();\n        if (lineNumber < firstInvalidLineNumber) {\n            return true;\n        }\n        if (lineNumber === firstInvalidLineNumber\n            && this._textModel.getLineLength(lineNumber) < 2048 /* Constants.CHEAP_TOKENIZATION_LENGTH_LIMIT */) {\n            return true;\n        }\n        return false;\n    }\n    /**\n     * The result is not cached.\n     */\n    tokenizeHeuristically(builder, startLineNumber, endLineNumber) {\n        if (endLineNumber <= this.store.getFirstInvalidEndStateLineNumberOrMax()) {\n            // nothing to do\n            return { heuristicTokens: false };\n        }\n        if (startLineNumber <= this.store.getFirstInvalidEndStateLineNumberOrMax()) {\n            // tokenization has reached the viewport start...\n            this.updateTokensUntilLine(builder, endLineNumber);\n            return { heuristicTokens: false };\n        }\n        let state = this.guessStartState(startLineNumber);\n        const languageId = this._textModel.getLanguageId();\n        for (let lineNumber = startLineNumber; lineNumber <= endLineNumber; lineNumber++) {\n            const text = this._textModel.getLineContent(lineNumber);\n            const r = safeTokenize(this._languageIdCodec, languageId, this.tokenizationSupport, text, true, state);\n            builder.add(lineNumber, r.tokens);\n            state = r.endState;\n        }\n        return { heuristicTokens: true };\n    }\n    guessStartState(lineNumber) {\n        let nonWhitespaceColumn = this._textModel.getLineFirstNonWhitespaceColumn(lineNumber);\n        const likelyRelevantLines = [];\n        let initialState = null;\n        for (let i = lineNumber - 1; nonWhitespaceColumn > 1 && i >= 1; i--) {\n            const newNonWhitespaceIndex = this._textModel.getLineFirstNonWhitespaceColumn(i);\n            // Ignore lines full of whitespace\n            if (newNonWhitespaceIndex === 0) {\n                continue;\n            }\n            if (newNonWhitespaceIndex < nonWhitespaceColumn) {\n                likelyRelevantLines.push(this._textModel.getLineContent(i));\n                nonWhitespaceColumn = newNonWhitespaceIndex;\n                initialState = this.getStartState(i);\n                if (initialState) {\n                    break;\n                }\n            }\n        }\n        if (!initialState) {\n            initialState = this.tokenizationSupport.getInitialState();\n        }\n        likelyRelevantLines.reverse();\n        const languageId = this._textModel.getLanguageId();\n        let state = initialState;\n        for (const line of likelyRelevantLines) {\n            const r = safeTokenize(this._languageIdCodec, languageId, this.tokenizationSupport, line, false, state);\n            state = r.endState;\n        }\n        return state;\n    }\n}\n/**\n * **Invariant:**\n * If the text model is retokenized from line 1 to {@link getFirstInvalidEndStateLineNumber}() - 1,\n * then the recomputed end state for line l will be equal to {@link getEndState}(l).\n */\nexport class TrackingTokenizationStateStore {\n    constructor(lineCount) {\n        this.lineCount = lineCount;\n        this._tokenizationStateStore = new TokenizationStateStore();\n        this._invalidEndStatesLineNumbers = new RangePriorityQueueImpl();\n        this._invalidEndStatesLineNumbers.addRange(new OffsetRange(1, lineCount + 1));\n    }\n    getEndState(lineNumber) {\n        return this._tokenizationStateStore.getEndState(lineNumber);\n    }\n    /**\n     * @returns if the end state has changed.\n     */\n    setEndState(lineNumber, state) {\n        if (!state) {\n            throw new BugIndicatingError('Cannot set null/undefined state');\n        }\n        this._invalidEndStatesLineNumbers.delete(lineNumber);\n        const r = this._tokenizationStateStore.setEndState(lineNumber, state);\n        if (r && lineNumber < this.lineCount) {\n            // because the state changed, we cannot trust the next state anymore and have to invalidate it.\n            this._invalidEndStatesLineNumbers.addRange(new OffsetRange(lineNumber + 1, lineNumber + 2));\n        }\n        return r;\n    }\n    acceptChange(range, newLineCount) {\n        this.lineCount += newLineCount - range.length;\n        this._tokenizationStateStore.acceptChange(range, newLineCount);\n        this._invalidEndStatesLineNumbers.addRangeAndResize(new OffsetRange(range.startLineNumber, range.endLineNumberExclusive), newLineCount);\n    }\n    acceptChanges(changes) {\n        for (const c of changes) {\n            const [eolCount] = countEOL(c.text);\n            this.acceptChange(new LineRange(c.range.startLineNumber, c.range.endLineNumber + 1), eolCount + 1);\n        }\n    }\n    invalidateEndStateRange(range) {\n        this._invalidEndStatesLineNumbers.addRange(new OffsetRange(range.startLineNumber, range.endLineNumberExclusive));\n    }\n    getFirstInvalidEndStateLineNumber() { return this._invalidEndStatesLineNumbers.min; }\n    getFirstInvalidEndStateLineNumberOrMax() {\n        return this.getFirstInvalidEndStateLineNumber() || Number.MAX_SAFE_INTEGER;\n    }\n    allStatesValid() { return this._invalidEndStatesLineNumbers.min === null; }\n    getStartState(lineNumber, initialState) {\n        if (lineNumber === 1) {\n            return initialState;\n        }\n        return this.getEndState(lineNumber - 1);\n    }\n    getFirstInvalidLine(initialState) {\n        const lineNumber = this.getFirstInvalidEndStateLineNumber();\n        if (lineNumber === null) {\n            return null;\n        }\n        const startState = this.getStartState(lineNumber, initialState);\n        if (!startState) {\n            throw new BugIndicatingError('Start state must be defined');\n        }\n        return { lineNumber, startState };\n    }\n}\nexport class TokenizationStateStore {\n    constructor() {\n        this._lineEndStates = new FixedArray(null);\n    }\n    getEndState(lineNumber) {\n        return this._lineEndStates.get(lineNumber);\n    }\n    setEndState(lineNumber, state) {\n        const oldState = this._lineEndStates.get(lineNumber);\n        if (oldState && oldState.equals(state)) {\n            return false;\n        }\n        this._lineEndStates.set(lineNumber, state);\n        return true;\n    }\n    acceptChange(range, newLineCount) {\n        let length = range.length;\n        if (newLineCount > 0 && length > 0) {\n            // Keep the last state, even though it is unrelated.\n            // But if the new state happens to agree with this last state, then we know we can stop tokenizing.\n            length--;\n            newLineCount--;\n        }\n        this._lineEndStates.replace(range.startLineNumber, length, newLineCount);\n    }\n}\nexport class RangePriorityQueueImpl {\n    constructor() {\n        this._ranges = [];\n    }\n    get min() {\n        if (this._ranges.length === 0) {\n            return null;\n        }\n        return this._ranges[0].start;\n    }\n    delete(value) {\n        const idx = this._ranges.findIndex(r => r.contains(value));\n        if (idx !== -1) {\n            const range = this._ranges[idx];\n            if (range.start === value) {\n                if (range.endExclusive === value + 1) {\n                    this._ranges.splice(idx, 1);\n                }\n                else {\n                    this._ranges[idx] = new OffsetRange(value + 1, range.endExclusive);\n                }\n            }\n            else {\n                if (range.endExclusive === value + 1) {\n                    this._ranges[idx] = new OffsetRange(range.start, value);\n                }\n                else {\n                    this._ranges.splice(idx, 1, new OffsetRange(range.start, value), new OffsetRange(value + 1, range.endExclusive));\n                }\n            }\n        }\n    }\n    addRange(range) {\n        OffsetRange.addRange(range, this._ranges);\n    }\n    addRangeAndResize(range, newLength) {\n        let idxFirstMightBeIntersecting = 0;\n        while (!(idxFirstMightBeIntersecting >= this._ranges.length || range.start <= this._ranges[idxFirstMightBeIntersecting].endExclusive)) {\n            idxFirstMightBeIntersecting++;\n        }\n        let idxFirstIsAfter = idxFirstMightBeIntersecting;\n        while (!(idxFirstIsAfter >= this._ranges.length || range.endExclusive < this._ranges[idxFirstIsAfter].start)) {\n            idxFirstIsAfter++;\n        }\n        const delta = newLength - range.length;\n        for (let i = idxFirstIsAfter; i < this._ranges.length; i++) {\n            this._ranges[i] = this._ranges[i].delta(delta);\n        }\n        if (idxFirstMightBeIntersecting === idxFirstIsAfter) {\n            const newRange = new OffsetRange(range.start, range.start + newLength);\n            if (!newRange.isEmpty) {\n                this._ranges.splice(idxFirstMightBeIntersecting, 0, newRange);\n            }\n        }\n        else {\n            const start = Math.min(range.start, this._ranges[idxFirstMightBeIntersecting].start);\n            const endEx = Math.max(range.endExclusive, this._ranges[idxFirstIsAfter - 1].endExclusive);\n            const newRange = new OffsetRange(start, endEx + delta);\n            if (!newRange.isEmpty) {\n                this._ranges.splice(idxFirstMightBeIntersecting, idxFirstIsAfter - idxFirstMightBeIntersecting, newRange);\n            }\n            else {\n                this._ranges.splice(idxFirstMightBeIntersecting, idxFirstIsAfter - idxFirstMightBeIntersecting);\n            }\n        }\n    }\n    toString() {\n        return this._ranges.map(r => r.toString()).join(' + ');\n    }\n}\nfunction safeTokenize(languageIdCodec, languageId, tokenizationSupport, text, hasEOL, state) {\n    let r = null;\n    if (tokenizationSupport) {\n        try {\n            r = tokenizationSupport.tokenizeEncoded(text, hasEOL, state.clone());\n        }\n        catch (e) {\n            onUnexpectedError(e);\n        }\n    }\n    if (!r) {\n        r = nullTokenizeEncoded(languageIdCodec.encodeLanguageId(languageId), state);\n    }\n    LineTokens.convertToEndOffset(r.tokens, text.length);\n    return r;\n}\nexport class DefaultBackgroundTokenizer {\n    constructor(_tokenizerWithStateStore, _backgroundTokenStore) {\n        this._tokenizerWithStateStore = _tokenizerWithStateStore;\n        this._backgroundTokenStore = _backgroundTokenStore;\n        this._isDisposed = false;\n        this._isScheduled = false;\n    }\n    dispose() {\n        this._isDisposed = true;\n    }\n    handleChanges() {\n        this._beginBackgroundTokenization();\n    }\n    _beginBackgroundTokenization() {\n        if (this._isScheduled || !this._tokenizerWithStateStore._textModel.isAttachedToEditor() || !this._hasLinesToTokenize()) {\n            return;\n        }\n        this._isScheduled = true;\n        runWhenGlobalIdle((deadline) => {\n            this._isScheduled = false;\n            this._backgroundTokenizeWithDeadline(deadline);\n        });\n    }\n    /**\n     * Tokenize until the deadline occurs, but try to yield every 1-2ms.\n     */\n    _backgroundTokenizeWithDeadline(deadline) {\n        // Read the time remaining from the `deadline` immediately because it is unclear\n        // if the `deadline` object will be valid after execution leaves this function.\n        const endTime = Date.now() + deadline.timeRemaining();\n        const execute = () => {\n            if (this._isDisposed || !this._tokenizerWithStateStore._textModel.isAttachedToEditor() || !this._hasLinesToTokenize()) {\n                // disposed in the meantime or detached or finished\n                return;\n            }\n            this._backgroundTokenizeForAtLeast1ms();\n            if (Date.now() < endTime) {\n                // There is still time before reaching the deadline, so yield to the browser and then\n                // continue execution\n                setTimeout0(execute);\n            }\n            else {\n                // The deadline has been reached, so schedule a new idle callback if necessary\n                this._beginBackgroundTokenization();\n            }\n        };\n        execute();\n    }\n    /**\n     * Tokenize for at least 1ms.\n     */\n    _backgroundTokenizeForAtLeast1ms() {\n        const lineCount = this._tokenizerWithStateStore._textModel.getLineCount();\n        const builder = new ContiguousMultilineTokensBuilder();\n        const sw = StopWatch.create(false);\n        do {\n            if (sw.elapsed() > 1) {\n                // the comparison is intentionally > 1 and not >= 1 to ensure that\n                // a full millisecond has elapsed, given how microseconds are rounded\n                // to milliseconds\n                break;\n            }\n            const tokenizedLineNumber = this._tokenizeOneInvalidLine(builder);\n            if (tokenizedLineNumber >= lineCount) {\n                break;\n            }\n        } while (this._hasLinesToTokenize());\n        this._backgroundTokenStore.setTokens(builder.finalize());\n        this.checkFinished();\n    }\n    _hasLinesToTokenize() {\n        if (!this._tokenizerWithStateStore) {\n            return false;\n        }\n        return !this._tokenizerWithStateStore.store.allStatesValid();\n    }\n    _tokenizeOneInvalidLine(builder) {\n        const firstInvalidLine = this._tokenizerWithStateStore?.getFirstInvalidLine();\n        if (!firstInvalidLine) {\n            return this._tokenizerWithStateStore._textModel.getLineCount() + 1;\n        }\n        this._tokenizerWithStateStore.updateTokensUntilLine(builder, firstInvalidLine.lineNumber);\n        return firstInvalidLine.lineNumber;\n    }\n    checkFinished() {\n        if (this._isDisposed) {\n            return;\n        }\n        if (this._tokenizerWithStateStore.store.allStatesValid()) {\n            this._backgroundTokenStore.backgroundTokenizationFinished();\n        }\n    }\n    requestTokens(startLineNumber, endLineNumberExclusive) {\n        this._tokenizerWithStateStore.store.invalidateEndStateRange(new LineRange(startLineNumber, endLineNumberExclusive));\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,iBAAiB,QAAQ,+BAA+B;AACjE,SAASC,kBAAkB,EAAEC,iBAAiB,QAAQ,gCAAgC;AACtF,SAASC,WAAW,QAAQ,kCAAkC;AAC9D,SAASC,SAAS,QAAQ,mCAAmC;AAC7D,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,SAAS,QAAQ,sBAAsB;AAChD,SAASC,WAAW,QAAQ,wBAAwB;AACpD,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,UAAU,QAAQ,iBAAiB;AAC5C,SAASC,gCAAgC,QAAQ,+CAA+C;AAChG,SAASC,UAAU,QAAQ,yBAAyB;AACpD,OAAO,MAAMC,uBAAuB,CAAC;EACjCC,WAAWA,CAACC,SAAS,EAAEC,mBAAmB,EAAE;IACxC,IAAI,CAACA,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACC,YAAY,GAAG,IAAI,CAACD,mBAAmB,CAACE,eAAe,CAAC,CAAC;IAC9D,IAAI,CAACC,KAAK,GAAG,IAAIC,8BAA8B,CAACL,SAAS,CAAC;EAC9D;EACAM,aAAaA,CAACC,UAAU,EAAE;IACtB,OAAO,IAAI,CAACH,KAAK,CAACE,aAAa,CAACC,UAAU,EAAE,IAAI,CAACL,YAAY,CAAC;EAClE;EACAM,mBAAmBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACJ,KAAK,CAACI,mBAAmB,CAAC,IAAI,CAACN,YAAY,CAAC;EAC5D;AACJ;AACA,OAAO,MAAMO,mCAAmC,SAASX,uBAAuB,CAAC;EAC7EC,WAAWA,CAACC,SAAS,EAAEC,mBAAmB,EAAES,UAAU,EAAEC,gBAAgB,EAAE;IACtE,KAAK,CAACX,SAAS,EAAEC,mBAAmB,CAAC;IACrC,IAAI,CAACS,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;EAC5C;EACAC,qBAAqBA,CAACC,OAAO,EAAEN,UAAU,EAAE;IACvC,MAAMO,UAAU,GAAG,IAAI,CAACJ,UAAU,CAACK,aAAa,CAAC,CAAC;IAClD,OAAO,IAAI,EAAE;MACT,MAAMC,cAAc,GAAG,IAAI,CAACR,mBAAmB,CAAC,CAAC;MACjD,IAAI,CAACQ,cAAc,IAAIA,cAAc,CAACT,UAAU,GAAGA,UAAU,EAAE;QAC3D;MACJ;MACA,MAAMU,IAAI,GAAG,IAAI,CAACP,UAAU,CAACQ,cAAc,CAACF,cAAc,CAACT,UAAU,CAAC;MACtE,MAAMY,CAAC,GAAGC,YAAY,CAAC,IAAI,CAACT,gBAAgB,EAAEG,UAAU,EAAE,IAAI,CAACb,mBAAmB,EAAEgB,IAAI,EAAE,IAAI,EAAED,cAAc,CAACK,UAAU,CAAC;MAC1HR,OAAO,CAACS,GAAG,CAACN,cAAc,CAACT,UAAU,EAAEY,CAAC,CAACI,MAAM,CAAC;MAChD,IAAI,CAACnB,KAAK,CAACoB,WAAW,CAACR,cAAc,CAACT,UAAU,EAAEY,CAAC,CAACM,QAAQ,CAAC;IACjE;EACJ;EACA;EACAC,gCAAgCA,CAACC,QAAQ,EAAEC,SAAS,EAAE;IAClD;IACA,MAAMC,cAAc,GAAG,IAAI,CAACvB,aAAa,CAACqB,QAAQ,CAACpB,UAAU,CAAC;IAC9D,IAAI,CAACsB,cAAc,EAAE;MACjB,OAAO,CAAC,CAAC;IACb;IACA,MAAMf,UAAU,GAAG,IAAI,CAACJ,UAAU,CAACK,aAAa,CAAC,CAAC;IAClD,MAAMe,WAAW,GAAG,IAAI,CAACpB,UAAU,CAACQ,cAAc,CAACS,QAAQ,CAACpB,UAAU,CAAC;IACvE;IACA,MAAMU,IAAI,GAAIa,WAAW,CAACC,SAAS,CAAC,CAAC,EAAEJ,QAAQ,CAACK,MAAM,GAAG,CAAC,CAAC,GACrDJ,SAAS,GACTE,WAAW,CAACC,SAAS,CAACJ,QAAQ,CAACK,MAAM,GAAG,CAAC,CAAE;IACjD,MAAMb,CAAC,GAAGC,YAAY,CAAC,IAAI,CAACT,gBAAgB,EAAEG,UAAU,EAAE,IAAI,CAACb,mBAAmB,EAAEgB,IAAI,EAAE,IAAI,EAAEY,cAAc,CAAC;IAC/G,MAAMI,UAAU,GAAG,IAAIpC,UAAU,CAACsB,CAAC,CAACI,MAAM,EAAEN,IAAI,EAAE,IAAI,CAACN,gBAAgB,CAAC;IACxE,IAAIsB,UAAU,CAACC,QAAQ,CAAC,CAAC,KAAK,CAAC,EAAE;MAC7B,OAAO,CAAC,CAAC;IACb;IACA,MAAMC,UAAU,GAAGF,UAAU,CAACG,sBAAsB,CAACT,QAAQ,CAACK,MAAM,GAAG,CAAC,CAAC;IACzE,OAAOC,UAAU,CAACI,oBAAoB,CAACF,UAAU,CAAC;EACtD;EACA;EACAG,oBAAoBA,CAACX,QAAQ,EAAEY,MAAM,EAAEC,OAAO,EAAE;IAC5C,MAAMjC,UAAU,GAAGoB,QAAQ,CAACpB,UAAU;IACtC,MAAMyB,MAAM,GAAGL,QAAQ,CAACK,MAAM;IAC9B,MAAMH,cAAc,GAAG,IAAI,CAACvB,aAAa,CAACC,UAAU,CAAC;IACrD,IAAI,CAACsB,cAAc,EAAE;MACjB,OAAO,IAAI;IACf;IACA,MAAMY,cAAc,GAAG,IAAI,CAAC/B,UAAU,CAACQ,cAAc,CAACX,UAAU,CAAC;IACjE,MAAMmC,cAAc,GAAGD,cAAc,CAACV,SAAS,CAAC,CAAC,EAAEC,MAAM,GAAG,CAAC,CAAC,GACxDQ,OAAO,GAAGC,cAAc,CAACV,SAAS,CAACC,MAAM,GAAG,CAAC,GAAGO,MAAM,CAAC;IAC7D,MAAMzB,UAAU,GAAG,IAAI,CAACJ,UAAU,CAACiC,uBAAuB,CAACpC,UAAU,EAAE,CAAC,CAAC;IACzE,MAAMqC,MAAM,GAAGxB,YAAY,CAAC,IAAI,CAACT,gBAAgB,EAAEG,UAAU,EAAE,IAAI,CAACb,mBAAmB,EAAEyC,cAAc,EAAE,IAAI,EAAEb,cAAc,CAAC;IAC9H,MAAMI,UAAU,GAAG,IAAIpC,UAAU,CAAC+C,MAAM,CAACrB,MAAM,EAAEmB,cAAc,EAAE,IAAI,CAAC/B,gBAAgB,CAAC;IACvF,OAAOsB,UAAU;EACrB;EACAY,wBAAwBA,CAACtC,UAAU,EAAE;IACjC,MAAMuC,sBAAsB,GAAG,IAAI,CAAC1C,KAAK,CAAC2C,sCAAsC,CAAC,CAAC;IAClF,OAAQxC,UAAU,GAAGuC,sBAAsB;EAC/C;EACAE,iBAAiBA,CAACzC,UAAU,EAAE;IAC1B,MAAMuC,sBAAsB,GAAG,IAAI,CAAC1C,KAAK,CAAC2C,sCAAsC,CAAC,CAAC;IAClF,IAAIxC,UAAU,GAAGuC,sBAAsB,EAAE;MACrC,OAAO,IAAI;IACf;IACA,IAAIvC,UAAU,KAAKuC,sBAAsB,IAClC,IAAI,CAACpC,UAAU,CAACuC,aAAa,CAAC1C,UAAU,CAAC,GAAG,IAAI,CAAC,iDAAiD;MACrG,OAAO,IAAI;IACf;IACA,OAAO,KAAK;EAChB;EACA;AACJ;AACA;EACI2C,qBAAqBA,CAACrC,OAAO,EAAEsC,eAAe,EAAEC,aAAa,EAAE;IAC3D,IAAIA,aAAa,IAAI,IAAI,CAAChD,KAAK,CAAC2C,sCAAsC,CAAC,CAAC,EAAE;MACtE;MACA,OAAO;QAAEM,eAAe,EAAE;MAAM,CAAC;IACrC;IACA,IAAIF,eAAe,IAAI,IAAI,CAAC/C,KAAK,CAAC2C,sCAAsC,CAAC,CAAC,EAAE;MACxE;MACA,IAAI,CAACnC,qBAAqB,CAACC,OAAO,EAAEuC,aAAa,CAAC;MAClD,OAAO;QAAEC,eAAe,EAAE;MAAM,CAAC;IACrC;IACA,IAAIC,KAAK,GAAG,IAAI,CAACC,eAAe,CAACJ,eAAe,CAAC;IACjD,MAAMrC,UAAU,GAAG,IAAI,CAACJ,UAAU,CAACK,aAAa,CAAC,CAAC;IAClD,KAAK,IAAIR,UAAU,GAAG4C,eAAe,EAAE5C,UAAU,IAAI6C,aAAa,EAAE7C,UAAU,EAAE,EAAE;MAC9E,MAAMU,IAAI,GAAG,IAAI,CAACP,UAAU,CAACQ,cAAc,CAACX,UAAU,CAAC;MACvD,MAAMY,CAAC,GAAGC,YAAY,CAAC,IAAI,CAACT,gBAAgB,EAAEG,UAAU,EAAE,IAAI,CAACb,mBAAmB,EAAEgB,IAAI,EAAE,IAAI,EAAEqC,KAAK,CAAC;MACtGzC,OAAO,CAACS,GAAG,CAACf,UAAU,EAAEY,CAAC,CAACI,MAAM,CAAC;MACjC+B,KAAK,GAAGnC,CAAC,CAACM,QAAQ;IACtB;IACA,OAAO;MAAE4B,eAAe,EAAE;IAAK,CAAC;EACpC;EACAE,eAAeA,CAAChD,UAAU,EAAE;IACxB,IAAIiD,mBAAmB,GAAG,IAAI,CAAC9C,UAAU,CAAC+C,+BAA+B,CAAClD,UAAU,CAAC;IACrF,MAAMmD,mBAAmB,GAAG,EAAE;IAC9B,IAAIxD,YAAY,GAAG,IAAI;IACvB,KAAK,IAAIyD,CAAC,GAAGpD,UAAU,GAAG,CAAC,EAAEiD,mBAAmB,GAAG,CAAC,IAAIG,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MACjE,MAAMC,qBAAqB,GAAG,IAAI,CAAClD,UAAU,CAAC+C,+BAA+B,CAACE,CAAC,CAAC;MAChF;MACA,IAAIC,qBAAqB,KAAK,CAAC,EAAE;QAC7B;MACJ;MACA,IAAIA,qBAAqB,GAAGJ,mBAAmB,EAAE;QAC7CE,mBAAmB,CAACG,IAAI,CAAC,IAAI,CAACnD,UAAU,CAACQ,cAAc,CAACyC,CAAC,CAAC,CAAC;QAC3DH,mBAAmB,GAAGI,qBAAqB;QAC3C1D,YAAY,GAAG,IAAI,CAACI,aAAa,CAACqD,CAAC,CAAC;QACpC,IAAIzD,YAAY,EAAE;UACd;QACJ;MACJ;IACJ;IACA,IAAI,CAACA,YAAY,EAAE;MACfA,YAAY,GAAG,IAAI,CAACD,mBAAmB,CAACE,eAAe,CAAC,CAAC;IAC7D;IACAuD,mBAAmB,CAACI,OAAO,CAAC,CAAC;IAC7B,MAAMhD,UAAU,GAAG,IAAI,CAACJ,UAAU,CAACK,aAAa,CAAC,CAAC;IAClD,IAAIuC,KAAK,GAAGpD,YAAY;IACxB,KAAK,MAAM6D,IAAI,IAAIL,mBAAmB,EAAE;MACpC,MAAMvC,CAAC,GAAGC,YAAY,CAAC,IAAI,CAACT,gBAAgB,EAAEG,UAAU,EAAE,IAAI,CAACb,mBAAmB,EAAE8D,IAAI,EAAE,KAAK,EAAET,KAAK,CAAC;MACvGA,KAAK,GAAGnC,CAAC,CAACM,QAAQ;IACtB;IACA,OAAO6B,KAAK;EAChB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMjD,8BAA8B,CAAC;EACxCN,WAAWA,CAACC,SAAS,EAAE;IACnB,IAAI,CAACA,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACgE,uBAAuB,GAAG,IAAIC,sBAAsB,CAAC,CAAC;IAC3D,IAAI,CAACC,4BAA4B,GAAG,IAAIC,sBAAsB,CAAC,CAAC;IAChE,IAAI,CAACD,4BAA4B,CAACE,QAAQ,CAAC,IAAI3E,WAAW,CAAC,CAAC,EAAEO,SAAS,GAAG,CAAC,CAAC,CAAC;EACjF;EACAqE,WAAWA,CAAC9D,UAAU,EAAE;IACpB,OAAO,IAAI,CAACyD,uBAAuB,CAACK,WAAW,CAAC9D,UAAU,CAAC;EAC/D;EACA;AACJ;AACA;EACIiB,WAAWA,CAACjB,UAAU,EAAE+C,KAAK,EAAE;IAC3B,IAAI,CAACA,KAAK,EAAE;MACR,MAAM,IAAInE,kBAAkB,CAAC,iCAAiC,CAAC;IACnE;IACA,IAAI,CAAC+E,4BAA4B,CAACI,MAAM,CAAC/D,UAAU,CAAC;IACpD,MAAMY,CAAC,GAAG,IAAI,CAAC6C,uBAAuB,CAACxC,WAAW,CAACjB,UAAU,EAAE+C,KAAK,CAAC;IACrE,IAAInC,CAAC,IAAIZ,UAAU,GAAG,IAAI,CAACP,SAAS,EAAE;MAClC;MACA,IAAI,CAACkE,4BAA4B,CAACE,QAAQ,CAAC,IAAI3E,WAAW,CAACc,UAAU,GAAG,CAAC,EAAEA,UAAU,GAAG,CAAC,CAAC,CAAC;IAC/F;IACA,OAAOY,CAAC;EACZ;EACAoD,YAAYA,CAACC,KAAK,EAAEC,YAAY,EAAE;IAC9B,IAAI,CAACzE,SAAS,IAAIyE,YAAY,GAAGD,KAAK,CAACjC,MAAM;IAC7C,IAAI,CAACyB,uBAAuB,CAACO,YAAY,CAACC,KAAK,EAAEC,YAAY,CAAC;IAC9D,IAAI,CAACP,4BAA4B,CAACQ,iBAAiB,CAAC,IAAIjF,WAAW,CAAC+E,KAAK,CAACrB,eAAe,EAAEqB,KAAK,CAACG,sBAAsB,CAAC,EAAEF,YAAY,CAAC;EAC3I;EACAG,aAAaA,CAACC,OAAO,EAAE;IACnB,KAAK,MAAMC,CAAC,IAAID,OAAO,EAAE;MACrB,MAAM,CAACE,QAAQ,CAAC,GAAGxF,QAAQ,CAACuF,CAAC,CAAC7D,IAAI,CAAC;MACnC,IAAI,CAACsD,YAAY,CAAC,IAAI/E,SAAS,CAACsF,CAAC,CAACN,KAAK,CAACrB,eAAe,EAAE2B,CAAC,CAACN,KAAK,CAACpB,aAAa,GAAG,CAAC,CAAC,EAAE2B,QAAQ,GAAG,CAAC,CAAC;IACtG;EACJ;EACAC,uBAAuBA,CAACR,KAAK,EAAE;IAC3B,IAAI,CAACN,4BAA4B,CAACE,QAAQ,CAAC,IAAI3E,WAAW,CAAC+E,KAAK,CAACrB,eAAe,EAAEqB,KAAK,CAACG,sBAAsB,CAAC,CAAC;EACpH;EACAM,iCAAiCA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACf,4BAA4B,CAACgB,GAAG;EAAE;EACpFnC,sCAAsCA,CAAA,EAAG;IACrC,OAAO,IAAI,CAACkC,iCAAiC,CAAC,CAAC,IAAIE,MAAM,CAACC,gBAAgB;EAC9E;EACAC,cAAcA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACnB,4BAA4B,CAACgB,GAAG,KAAK,IAAI;EAAE;EAC1E5E,aAAaA,CAACC,UAAU,EAAEL,YAAY,EAAE;IACpC,IAAIK,UAAU,KAAK,CAAC,EAAE;MAClB,OAAOL,YAAY;IACvB;IACA,OAAO,IAAI,CAACmE,WAAW,CAAC9D,UAAU,GAAG,CAAC,CAAC;EAC3C;EACAC,mBAAmBA,CAACN,YAAY,EAAE;IAC9B,MAAMK,UAAU,GAAG,IAAI,CAAC0E,iCAAiC,CAAC,CAAC;IAC3D,IAAI1E,UAAU,KAAK,IAAI,EAAE;MACrB,OAAO,IAAI;IACf;IACA,MAAMc,UAAU,GAAG,IAAI,CAACf,aAAa,CAACC,UAAU,EAAEL,YAAY,CAAC;IAC/D,IAAI,CAACmB,UAAU,EAAE;MACb,MAAM,IAAIlC,kBAAkB,CAAC,6BAA6B,CAAC;IAC/D;IACA,OAAO;MAAEoB,UAAU;MAAEc;IAAW,CAAC;EACrC;AACJ;AACA,OAAO,MAAM4C,sBAAsB,CAAC;EAChClE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACuF,cAAc,GAAG,IAAI3F,UAAU,CAAC,IAAI,CAAC;EAC9C;EACA0E,WAAWA,CAAC9D,UAAU,EAAE;IACpB,OAAO,IAAI,CAAC+E,cAAc,CAACC,GAAG,CAAChF,UAAU,CAAC;EAC9C;EACAiB,WAAWA,CAACjB,UAAU,EAAE+C,KAAK,EAAE;IAC3B,MAAMkC,QAAQ,GAAG,IAAI,CAACF,cAAc,CAACC,GAAG,CAAChF,UAAU,CAAC;IACpD,IAAIiF,QAAQ,IAAIA,QAAQ,CAACC,MAAM,CAACnC,KAAK,CAAC,EAAE;MACpC,OAAO,KAAK;IAChB;IACA,IAAI,CAACgC,cAAc,CAACI,GAAG,CAACnF,UAAU,EAAE+C,KAAK,CAAC;IAC1C,OAAO,IAAI;EACf;EACAiB,YAAYA,CAACC,KAAK,EAAEC,YAAY,EAAE;IAC9B,IAAIlC,MAAM,GAAGiC,KAAK,CAACjC,MAAM;IACzB,IAAIkC,YAAY,GAAG,CAAC,IAAIlC,MAAM,GAAG,CAAC,EAAE;MAChC;MACA;MACAA,MAAM,EAAE;MACRkC,YAAY,EAAE;IAClB;IACA,IAAI,CAACa,cAAc,CAACK,OAAO,CAACnB,KAAK,CAACrB,eAAe,EAAEZ,MAAM,EAAEkC,YAAY,CAAC;EAC5E;AACJ;AACA,OAAO,MAAMN,sBAAsB,CAAC;EAChCpE,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC6F,OAAO,GAAG,EAAE;EACrB;EACA,IAAIV,GAAGA,CAAA,EAAG;IACN,IAAI,IAAI,CAACU,OAAO,CAACrD,MAAM,KAAK,CAAC,EAAE;MAC3B,OAAO,IAAI;IACf;IACA,OAAO,IAAI,CAACqD,OAAO,CAAC,CAAC,CAAC,CAACC,KAAK;EAChC;EACAvB,MAAMA,CAACwB,KAAK,EAAE;IACV,MAAMC,GAAG,GAAG,IAAI,CAACH,OAAO,CAACI,SAAS,CAAC7E,CAAC,IAAIA,CAAC,CAAC8E,QAAQ,CAACH,KAAK,CAAC,CAAC;IAC1D,IAAIC,GAAG,KAAK,CAAC,CAAC,EAAE;MACZ,MAAMvB,KAAK,GAAG,IAAI,CAACoB,OAAO,CAACG,GAAG,CAAC;MAC/B,IAAIvB,KAAK,CAACqB,KAAK,KAAKC,KAAK,EAAE;QACvB,IAAItB,KAAK,CAAC0B,YAAY,KAAKJ,KAAK,GAAG,CAAC,EAAE;UAClC,IAAI,CAACF,OAAO,CAACO,MAAM,CAACJ,GAAG,EAAE,CAAC,CAAC;QAC/B,CAAC,MACI;UACD,IAAI,CAACH,OAAO,CAACG,GAAG,CAAC,GAAG,IAAItG,WAAW,CAACqG,KAAK,GAAG,CAAC,EAAEtB,KAAK,CAAC0B,YAAY,CAAC;QACtE;MACJ,CAAC,MACI;QACD,IAAI1B,KAAK,CAAC0B,YAAY,KAAKJ,KAAK,GAAG,CAAC,EAAE;UAClC,IAAI,CAACF,OAAO,CAACG,GAAG,CAAC,GAAG,IAAItG,WAAW,CAAC+E,KAAK,CAACqB,KAAK,EAAEC,KAAK,CAAC;QAC3D,CAAC,MACI;UACD,IAAI,CAACF,OAAO,CAACO,MAAM,CAACJ,GAAG,EAAE,CAAC,EAAE,IAAItG,WAAW,CAAC+E,KAAK,CAACqB,KAAK,EAAEC,KAAK,CAAC,EAAE,IAAIrG,WAAW,CAACqG,KAAK,GAAG,CAAC,EAAEtB,KAAK,CAAC0B,YAAY,CAAC,CAAC;QACpH;MACJ;IACJ;EACJ;EACA9B,QAAQA,CAACI,KAAK,EAAE;IACZ/E,WAAW,CAAC2E,QAAQ,CAACI,KAAK,EAAE,IAAI,CAACoB,OAAO,CAAC;EAC7C;EACAlB,iBAAiBA,CAACF,KAAK,EAAE4B,SAAS,EAAE;IAChC,IAAIC,2BAA2B,GAAG,CAAC;IACnC,OAAO,EAAEA,2BAA2B,IAAI,IAAI,CAACT,OAAO,CAACrD,MAAM,IAAIiC,KAAK,CAACqB,KAAK,IAAI,IAAI,CAACD,OAAO,CAACS,2BAA2B,CAAC,CAACH,YAAY,CAAC,EAAE;MACnIG,2BAA2B,EAAE;IACjC;IACA,IAAIC,eAAe,GAAGD,2BAA2B;IACjD,OAAO,EAAEC,eAAe,IAAI,IAAI,CAACV,OAAO,CAACrD,MAAM,IAAIiC,KAAK,CAAC0B,YAAY,GAAG,IAAI,CAACN,OAAO,CAACU,eAAe,CAAC,CAACT,KAAK,CAAC,EAAE;MAC1GS,eAAe,EAAE;IACrB;IACA,MAAMC,KAAK,GAAGH,SAAS,GAAG5B,KAAK,CAACjC,MAAM;IACtC,KAAK,IAAIoB,CAAC,GAAG2C,eAAe,EAAE3C,CAAC,GAAG,IAAI,CAACiC,OAAO,CAACrD,MAAM,EAAEoB,CAAC,EAAE,EAAE;MACxD,IAAI,CAACiC,OAAO,CAACjC,CAAC,CAAC,GAAG,IAAI,CAACiC,OAAO,CAACjC,CAAC,CAAC,CAAC4C,KAAK,CAACA,KAAK,CAAC;IAClD;IACA,IAAIF,2BAA2B,KAAKC,eAAe,EAAE;MACjD,MAAME,QAAQ,GAAG,IAAI/G,WAAW,CAAC+E,KAAK,CAACqB,KAAK,EAAErB,KAAK,CAACqB,KAAK,GAAGO,SAAS,CAAC;MACtE,IAAI,CAACI,QAAQ,CAACC,OAAO,EAAE;QACnB,IAAI,CAACb,OAAO,CAACO,MAAM,CAACE,2BAA2B,EAAE,CAAC,EAAEG,QAAQ,CAAC;MACjE;IACJ,CAAC,MACI;MACD,MAAMX,KAAK,GAAGa,IAAI,CAACxB,GAAG,CAACV,KAAK,CAACqB,KAAK,EAAE,IAAI,CAACD,OAAO,CAACS,2BAA2B,CAAC,CAACR,KAAK,CAAC;MACpF,MAAMc,KAAK,GAAGD,IAAI,CAACE,GAAG,CAACpC,KAAK,CAAC0B,YAAY,EAAE,IAAI,CAACN,OAAO,CAACU,eAAe,GAAG,CAAC,CAAC,CAACJ,YAAY,CAAC;MAC1F,MAAMM,QAAQ,GAAG,IAAI/G,WAAW,CAACoG,KAAK,EAAEc,KAAK,GAAGJ,KAAK,CAAC;MACtD,IAAI,CAACC,QAAQ,CAACC,OAAO,EAAE;QACnB,IAAI,CAACb,OAAO,CAACO,MAAM,CAACE,2BAA2B,EAAEC,eAAe,GAAGD,2BAA2B,EAAEG,QAAQ,CAAC;MAC7G,CAAC,MACI;QACD,IAAI,CAACZ,OAAO,CAACO,MAAM,CAACE,2BAA2B,EAAEC,eAAe,GAAGD,2BAA2B,CAAC;MACnG;IACJ;EACJ;EACAQ,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACjB,OAAO,CAACkB,GAAG,CAAC3F,CAAC,IAAIA,CAAC,CAAC0F,QAAQ,CAAC,CAAC,CAAC,CAACE,IAAI,CAAC,KAAK,CAAC;EAC1D;AACJ;AACA,SAAS3F,YAAYA,CAAC4F,eAAe,EAAElG,UAAU,EAAEb,mBAAmB,EAAEgB,IAAI,EAAEgG,MAAM,EAAE3D,KAAK,EAAE;EACzF,IAAInC,CAAC,GAAG,IAAI;EACZ,IAAIlB,mBAAmB,EAAE;IACrB,IAAI;MACAkB,CAAC,GAAGlB,mBAAmB,CAACiH,eAAe,CAACjG,IAAI,EAAEgG,MAAM,EAAE3D,KAAK,CAAC6D,KAAK,CAAC,CAAC,CAAC;IACxE,CAAC,CACD,OAAOC,CAAC,EAAE;MACNhI,iBAAiB,CAACgI,CAAC,CAAC;IACxB;EACJ;EACA,IAAI,CAACjG,CAAC,EAAE;IACJA,CAAC,GAAGzB,mBAAmB,CAACsH,eAAe,CAACK,gBAAgB,CAACvG,UAAU,CAAC,EAAEwC,KAAK,CAAC;EAChF;EACAzD,UAAU,CAACyH,kBAAkB,CAACnG,CAAC,CAACI,MAAM,EAAEN,IAAI,CAACsB,MAAM,CAAC;EACpD,OAAOpB,CAAC;AACZ;AACA,OAAO,MAAMoG,0BAA0B,CAAC;EACpCxH,WAAWA,CAACyH,wBAAwB,EAAEC,qBAAqB,EAAE;IACzD,IAAI,CAACD,wBAAwB,GAAGA,wBAAwB;IACxD,IAAI,CAACC,qBAAqB,GAAGA,qBAAqB;IAClD,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,YAAY,GAAG,KAAK;EAC7B;EACAC,OAAOA,CAAA,EAAG;IACN,IAAI,CAACF,WAAW,GAAG,IAAI;EAC3B;EACAG,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACC,4BAA4B,CAAC,CAAC;EACvC;EACAA,4BAA4BA,CAAA,EAAG;IAC3B,IAAI,IAAI,CAACH,YAAY,IAAI,CAAC,IAAI,CAACH,wBAAwB,CAAC9G,UAAU,CAACqH,kBAAkB,CAAC,CAAC,IAAI,CAAC,IAAI,CAACC,mBAAmB,CAAC,CAAC,EAAE;MACpH;IACJ;IACA,IAAI,CAACL,YAAY,GAAG,IAAI;IACxBzI,iBAAiB,CAAE+I,QAAQ,IAAK;MAC5B,IAAI,CAACN,YAAY,GAAG,KAAK;MACzB,IAAI,CAACO,+BAA+B,CAACD,QAAQ,CAAC;IAClD,CAAC,CAAC;EACN;EACA;AACJ;AACA;EACIC,+BAA+BA,CAACD,QAAQ,EAAE;IACtC;IACA;IACA,MAAME,OAAO,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGJ,QAAQ,CAACK,aAAa,CAAC,CAAC;IACrD,MAAMC,OAAO,GAAGA,CAAA,KAAM;MAClB,IAAI,IAAI,CAACb,WAAW,IAAI,CAAC,IAAI,CAACF,wBAAwB,CAAC9G,UAAU,CAACqH,kBAAkB,CAAC,CAAC,IAAI,CAAC,IAAI,CAACC,mBAAmB,CAAC,CAAC,EAAE;QACnH;QACA;MACJ;MACA,IAAI,CAACQ,gCAAgC,CAAC,CAAC;MACvC,IAAIJ,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,OAAO,EAAE;QACtB;QACA;QACA9I,WAAW,CAACkJ,OAAO,CAAC;MACxB,CAAC,MACI;QACD;QACA,IAAI,CAACT,4BAA4B,CAAC,CAAC;MACvC;IACJ,CAAC;IACDS,OAAO,CAAC,CAAC;EACb;EACA;AACJ;AACA;EACIC,gCAAgCA,CAAA,EAAG;IAC/B,MAAMxI,SAAS,GAAG,IAAI,CAACwH,wBAAwB,CAAC9G,UAAU,CAAC+H,YAAY,CAAC,CAAC;IACzE,MAAM5H,OAAO,GAAG,IAAIjB,gCAAgC,CAAC,CAAC;IACtD,MAAM8I,EAAE,GAAGpJ,SAAS,CAACqJ,MAAM,CAAC,KAAK,CAAC;IAClC,GAAG;MACC,IAAID,EAAE,CAACE,OAAO,CAAC,CAAC,GAAG,CAAC,EAAE;QAClB;QACA;QACA;QACA;MACJ;MACA,MAAMC,mBAAmB,GAAG,IAAI,CAACC,uBAAuB,CAACjI,OAAO,CAAC;MACjE,IAAIgI,mBAAmB,IAAI7I,SAAS,EAAE;QAClC;MACJ;IACJ,CAAC,QAAQ,IAAI,CAACgI,mBAAmB,CAAC,CAAC;IACnC,IAAI,CAACP,qBAAqB,CAACsB,SAAS,CAAClI,OAAO,CAACmI,QAAQ,CAAC,CAAC,CAAC;IACxD,IAAI,CAACC,aAAa,CAAC,CAAC;EACxB;EACAjB,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAAC,IAAI,CAACR,wBAAwB,EAAE;MAChC,OAAO,KAAK;IAChB;IACA,OAAO,CAAC,IAAI,CAACA,wBAAwB,CAACpH,KAAK,CAACiF,cAAc,CAAC,CAAC;EAChE;EACAyD,uBAAuBA,CAACjI,OAAO,EAAE;IAC7B,MAAMqI,gBAAgB,GAAG,IAAI,CAAC1B,wBAAwB,EAAEhH,mBAAmB,CAAC,CAAC;IAC7E,IAAI,CAAC0I,gBAAgB,EAAE;MACnB,OAAO,IAAI,CAAC1B,wBAAwB,CAAC9G,UAAU,CAAC+H,YAAY,CAAC,CAAC,GAAG,CAAC;IACtE;IACA,IAAI,CAACjB,wBAAwB,CAAC5G,qBAAqB,CAACC,OAAO,EAAEqI,gBAAgB,CAAC3I,UAAU,CAAC;IACzF,OAAO2I,gBAAgB,CAAC3I,UAAU;EACtC;EACA0I,aAAaA,CAAA,EAAG;IACZ,IAAI,IAAI,CAACvB,WAAW,EAAE;MAClB;IACJ;IACA,IAAI,IAAI,CAACF,wBAAwB,CAACpH,KAAK,CAACiF,cAAc,CAAC,CAAC,EAAE;MACtD,IAAI,CAACoC,qBAAqB,CAAC0B,8BAA8B,CAAC,CAAC;IAC/D;EACJ;EACAC,aAAaA,CAACjG,eAAe,EAAEwB,sBAAsB,EAAE;IACnD,IAAI,CAAC6C,wBAAwB,CAACpH,KAAK,CAAC4E,uBAAuB,CAAC,IAAIxF,SAAS,CAAC2D,eAAe,EAAEwB,sBAAsB,CAAC,CAAC;EACvH;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}