{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { IntervalTimer } from '../../../../base/common/async.js';\nimport { Disposable, DisposableStore, dispose, toDisposable } from '../../../../base/common/lifecycle.js';\nimport { URI } from '../../../../base/common/uri.js';\nimport { Position } from '../../core/position.js';\nimport { Range } from '../../core/range.js';\nimport { ensureValidWordDefinition, getWordAtText } from '../../core/wordHelper.js';\nimport { MirrorTextModel as BaseMirrorModel } from '../../model/mirrorTextModel.js';\n/**\n * Stop syncing a model to the worker if it was not needed for 1 min.\n */\nexport const STOP_SYNC_MODEL_DELTA_TIME_MS = 60 * 1000;\nexport class WorkerTextModelSyncClient extends Disposable {\n  constructor(proxy, modelService, keepIdleModels = false) {\n    super();\n    this._syncedModels = Object.create(null);\n    this._syncedModelsLastUsedTime = Object.create(null);\n    this._proxy = proxy;\n    this._modelService = modelService;\n    if (!keepIdleModels) {\n      const timer = new IntervalTimer();\n      timer.cancelAndSet(() => this._checkStopModelSync(), Math.round(STOP_SYNC_MODEL_DELTA_TIME_MS / 2));\n      this._register(timer);\n    }\n  }\n  dispose() {\n    for (const modelUrl in this._syncedModels) {\n      dispose(this._syncedModels[modelUrl]);\n    }\n    this._syncedModels = Object.create(null);\n    this._syncedModelsLastUsedTime = Object.create(null);\n    super.dispose();\n  }\n  ensureSyncedResources(resources, forceLargeModels = false) {\n    for (const resource of resources) {\n      const resourceStr = resource.toString();\n      if (!this._syncedModels[resourceStr]) {\n        this._beginModelSync(resource, forceLargeModels);\n      }\n      if (this._syncedModels[resourceStr]) {\n        this._syncedModelsLastUsedTime[resourceStr] = new Date().getTime();\n      }\n    }\n  }\n  _checkStopModelSync() {\n    const currentTime = new Date().getTime();\n    const toRemove = [];\n    for (const modelUrl in this._syncedModelsLastUsedTime) {\n      const elapsedTime = currentTime - this._syncedModelsLastUsedTime[modelUrl];\n      if (elapsedTime > STOP_SYNC_MODEL_DELTA_TIME_MS) {\n        toRemove.push(modelUrl);\n      }\n    }\n    for (const e of toRemove) {\n      this._stopModelSync(e);\n    }\n  }\n  _beginModelSync(resource, forceLargeModels) {\n    const model = this._modelService.getModel(resource);\n    if (!model) {\n      return;\n    }\n    if (!forceLargeModels && model.isTooLargeForSyncing()) {\n      return;\n    }\n    const modelUrl = resource.toString();\n    this._proxy.$acceptNewModel({\n      url: model.uri.toString(),\n      lines: model.getLinesContent(),\n      EOL: model.getEOL(),\n      versionId: model.getVersionId()\n    });\n    const toDispose = new DisposableStore();\n    toDispose.add(model.onDidChangeContent(e => {\n      this._proxy.$acceptModelChanged(modelUrl.toString(), e);\n    }));\n    toDispose.add(model.onWillDispose(() => {\n      this._stopModelSync(modelUrl);\n    }));\n    toDispose.add(toDisposable(() => {\n      this._proxy.$acceptRemovedModel(modelUrl);\n    }));\n    this._syncedModels[modelUrl] = toDispose;\n  }\n  _stopModelSync(modelUrl) {\n    const toDispose = this._syncedModels[modelUrl];\n    delete this._syncedModels[modelUrl];\n    delete this._syncedModelsLastUsedTime[modelUrl];\n    dispose(toDispose);\n  }\n}\nexport class WorkerTextModelSyncServer {\n  constructor() {\n    this._models = Object.create(null);\n  }\n  getModel(uri) {\n    return this._models[uri];\n  }\n  getModels() {\n    const all = [];\n    Object.keys(this._models).forEach(key => all.push(this._models[key]));\n    return all;\n  }\n  $acceptNewModel(data) {\n    this._models[data.url] = new MirrorModel(URI.parse(data.url), data.lines, data.EOL, data.versionId);\n  }\n  $acceptModelChanged(uri, e) {\n    if (!this._models[uri]) {\n      return;\n    }\n    const model = this._models[uri];\n    model.onEvents(e);\n  }\n  $acceptRemovedModel(uri) {\n    if (!this._models[uri]) {\n      return;\n    }\n    delete this._models[uri];\n  }\n}\nexport class MirrorModel extends BaseMirrorModel {\n  get uri() {\n    return this._uri;\n  }\n  get eol() {\n    return this._eol;\n  }\n  getValue() {\n    return this.getText();\n  }\n  findMatches(regex) {\n    const matches = [];\n    for (let i = 0; i < this._lines.length; i++) {\n      const line = this._lines[i];\n      const offsetToAdd = this.offsetAt(new Position(i + 1, 1));\n      const iteratorOverMatches = line.matchAll(regex);\n      for (const match of iteratorOverMatches) {\n        if (match.index || match.index === 0) {\n          match.index = match.index + offsetToAdd;\n        }\n        matches.push(match);\n      }\n    }\n    return matches;\n  }\n  getLinesContent() {\n    return this._lines.slice(0);\n  }\n  getLineCount() {\n    return this._lines.length;\n  }\n  getLineContent(lineNumber) {\n    return this._lines[lineNumber - 1];\n  }\n  getWordAtPosition(position, wordDefinition) {\n    const wordAtText = getWordAtText(position.column, ensureValidWordDefinition(wordDefinition), this._lines[position.lineNumber - 1], 0);\n    if (wordAtText) {\n      return new Range(position.lineNumber, wordAtText.startColumn, position.lineNumber, wordAtText.endColumn);\n    }\n    return null;\n  }\n  words(wordDefinition) {\n    const lines = this._lines;\n    const wordenize = this._wordenize.bind(this);\n    let lineNumber = 0;\n    let lineText = '';\n    let wordRangesIdx = 0;\n    let wordRanges = [];\n    return {\n      *[Symbol.iterator]() {\n        while (true) {\n          if (wordRangesIdx < wordRanges.length) {\n            const value = lineText.substring(wordRanges[wordRangesIdx].start, wordRanges[wordRangesIdx].end);\n            wordRangesIdx += 1;\n            yield value;\n          } else {\n            if (lineNumber < lines.length) {\n              lineText = lines[lineNumber];\n              wordRanges = wordenize(lineText, wordDefinition);\n              wordRangesIdx = 0;\n              lineNumber += 1;\n            } else {\n              break;\n            }\n          }\n        }\n      }\n    };\n  }\n  getLineWords(lineNumber, wordDefinition) {\n    const content = this._lines[lineNumber - 1];\n    const ranges = this._wordenize(content, wordDefinition);\n    const words = [];\n    for (const range of ranges) {\n      words.push({\n        word: content.substring(range.start, range.end),\n        startColumn: range.start + 1,\n        endColumn: range.end + 1\n      });\n    }\n    return words;\n  }\n  _wordenize(content, wordDefinition) {\n    const result = [];\n    let match;\n    wordDefinition.lastIndex = 0; // reset lastIndex just to be sure\n    while (match = wordDefinition.exec(content)) {\n      if (match[0].length === 0) {\n        // it did match the empty string\n        break;\n      }\n      result.push({\n        start: match.index,\n        end: match.index + match[0].length\n      });\n    }\n    return result;\n  }\n  getValueInRange(range) {\n    range = this._validateRange(range);\n    if (range.startLineNumber === range.endLineNumber) {\n      return this._lines[range.startLineNumber - 1].substring(range.startColumn - 1, range.endColumn - 1);\n    }\n    const lineEnding = this._eol;\n    const startLineIndex = range.startLineNumber - 1;\n    const endLineIndex = range.endLineNumber - 1;\n    const resultLines = [];\n    resultLines.push(this._lines[startLineIndex].substring(range.startColumn - 1));\n    for (let i = startLineIndex + 1; i < endLineIndex; i++) {\n      resultLines.push(this._lines[i]);\n    }\n    resultLines.push(this._lines[endLineIndex].substring(0, range.endColumn - 1));\n    return resultLines.join(lineEnding);\n  }\n  offsetAt(position) {\n    position = this._validatePosition(position);\n    this._ensureLineStarts();\n    return this._lineStarts.getPrefixSum(position.lineNumber - 2) + (position.column - 1);\n  }\n  positionAt(offset) {\n    offset = Math.floor(offset);\n    offset = Math.max(0, offset);\n    this._ensureLineStarts();\n    const out = this._lineStarts.getIndexOf(offset);\n    const lineLength = this._lines[out.index].length;\n    // Ensure we return a valid position\n    return {\n      lineNumber: 1 + out.index,\n      column: 1 + Math.min(out.remainder, lineLength)\n    };\n  }\n  _validateRange(range) {\n    const start = this._validatePosition({\n      lineNumber: range.startLineNumber,\n      column: range.startColumn\n    });\n    const end = this._validatePosition({\n      lineNumber: range.endLineNumber,\n      column: range.endColumn\n    });\n    if (start.lineNumber !== range.startLineNumber || start.column !== range.startColumn || end.lineNumber !== range.endLineNumber || end.column !== range.endColumn) {\n      return {\n        startLineNumber: start.lineNumber,\n        startColumn: start.column,\n        endLineNumber: end.lineNumber,\n        endColumn: end.column\n      };\n    }\n    return range;\n  }\n  _validatePosition(position) {\n    if (!Position.isIPosition(position)) {\n      throw new Error('bad position');\n    }\n    let {\n      lineNumber,\n      column\n    } = position;\n    let hasChanged = false;\n    if (lineNumber < 1) {\n      lineNumber = 1;\n      column = 1;\n      hasChanged = true;\n    } else if (lineNumber > this._lines.length) {\n      lineNumber = this._lines.length;\n      column = this._lines[lineNumber - 1].length + 1;\n      hasChanged = true;\n    } else {\n      const maxCharacter = this._lines[lineNumber - 1].length + 1;\n      if (column < 1) {\n        column = 1;\n        hasChanged = true;\n      } else if (column > maxCharacter) {\n        column = maxCharacter;\n        hasChanged = true;\n      }\n    }\n    if (!hasChanged) {\n      return position;\n    } else {\n      return {\n        lineNumber,\n        column\n      };\n    }\n  }\n}", "map": {"version": 3, "names": ["IntervalTimer", "Disposable", "DisposableStore", "dispose", "toDisposable", "URI", "Position", "Range", "ensureValidWordDefinition", "getWordAtText", "MirrorTextModel", "BaseMirrorModel", "STOP_SYNC_MODEL_DELTA_TIME_MS", "WorkerTextModelSyncClient", "constructor", "proxy", "modelService", "keepIdleModels", "_syncedModels", "Object", "create", "_syncedModelsLastUsedTime", "_proxy", "_modelService", "timer", "cancelAndSet", "_checkStopModelSync", "Math", "round", "_register", "modelUrl", "ensureSyncedResources", "resources", "forceLargeModels", "resource", "resourceStr", "toString", "_beginModelSync", "Date", "getTime", "currentTime", "toRemove", "elapsedTime", "push", "e", "_stopModelSync", "model", "getModel", "isTooLargeForSyncing", "$acceptNewModel", "url", "uri", "lines", "getLinesContent", "EOL", "getEOL", "versionId", "getVersionId", "toDispose", "add", "onDidChangeContent", "$acceptModelChanged", "onWillDispose", "$acceptRemovedModel", "WorkerTextModelSyncServer", "_models", "getModels", "all", "keys", "for<PERSON>ach", "key", "data", "MirrorModel", "parse", "onEvents", "_uri", "eol", "_eol", "getValue", "getText", "findMatches", "regex", "matches", "i", "_lines", "length", "line", "offsetToAdd", "offsetAt", "iteratorOverMatches", "matchAll", "match", "index", "slice", "getLineCount", "get<PERSON>ineC<PERSON>nt", "lineNumber", "getWordAtPosition", "position", "wordDefinition", "wordAtText", "column", "startColumn", "endColumn", "words", "wordenize", "_wordenize", "bind", "lineText", "wordRangesIdx", "wordRanges", "Symbol", "iterator", "value", "substring", "start", "end", "getLineWords", "content", "ranges", "range", "word", "result", "lastIndex", "exec", "getValueInRange", "_validate<PERSON>ange", "startLineNumber", "endLineNumber", "lineEnding", "startLineIndex", "endLineIndex", "resultLines", "join", "_validatePosition", "_ensureLineStarts", "_lineStarts", "getPrefixSum", "positionAt", "offset", "floor", "max", "out", "getIndexOf", "lineLength", "min", "remainder", "isIPosition", "Error", "has<PERSON><PERSON>ed", "maxCharacter"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/services/textModelSync/textModelSync.impl.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { IntervalTimer } from '../../../../base/common/async.js';\nimport { Disposable, DisposableStore, dispose, toDisposable } from '../../../../base/common/lifecycle.js';\nimport { URI } from '../../../../base/common/uri.js';\nimport { Position } from '../../core/position.js';\nimport { Range } from '../../core/range.js';\nimport { ensureValidWordDefinition, getWordAtText } from '../../core/wordHelper.js';\nimport { MirrorTextModel as BaseMirrorModel } from '../../model/mirrorTextModel.js';\n/**\n * Stop syncing a model to the worker if it was not needed for 1 min.\n */\nexport const STOP_SYNC_MODEL_DELTA_TIME_MS = 60 * 1000;\nexport class WorkerTextModelSyncClient extends Disposable {\n    constructor(proxy, modelService, keepIdleModels = false) {\n        super();\n        this._syncedModels = Object.create(null);\n        this._syncedModelsLastUsedTime = Object.create(null);\n        this._proxy = proxy;\n        this._modelService = modelService;\n        if (!keepIdleModels) {\n            const timer = new IntervalTimer();\n            timer.cancelAndSet(() => this._checkStopModelSync(), Math.round(STOP_SYNC_MODEL_DELTA_TIME_MS / 2));\n            this._register(timer);\n        }\n    }\n    dispose() {\n        for (const modelUrl in this._syncedModels) {\n            dispose(this._syncedModels[modelUrl]);\n        }\n        this._syncedModels = Object.create(null);\n        this._syncedModelsLastUsedTime = Object.create(null);\n        super.dispose();\n    }\n    ensureSyncedResources(resources, forceLargeModels = false) {\n        for (const resource of resources) {\n            const resourceStr = resource.toString();\n            if (!this._syncedModels[resourceStr]) {\n                this._beginModelSync(resource, forceLargeModels);\n            }\n            if (this._syncedModels[resourceStr]) {\n                this._syncedModelsLastUsedTime[resourceStr] = (new Date()).getTime();\n            }\n        }\n    }\n    _checkStopModelSync() {\n        const currentTime = (new Date()).getTime();\n        const toRemove = [];\n        for (const modelUrl in this._syncedModelsLastUsedTime) {\n            const elapsedTime = currentTime - this._syncedModelsLastUsedTime[modelUrl];\n            if (elapsedTime > STOP_SYNC_MODEL_DELTA_TIME_MS) {\n                toRemove.push(modelUrl);\n            }\n        }\n        for (const e of toRemove) {\n            this._stopModelSync(e);\n        }\n    }\n    _beginModelSync(resource, forceLargeModels) {\n        const model = this._modelService.getModel(resource);\n        if (!model) {\n            return;\n        }\n        if (!forceLargeModels && model.isTooLargeForSyncing()) {\n            return;\n        }\n        const modelUrl = resource.toString();\n        this._proxy.$acceptNewModel({\n            url: model.uri.toString(),\n            lines: model.getLinesContent(),\n            EOL: model.getEOL(),\n            versionId: model.getVersionId()\n        });\n        const toDispose = new DisposableStore();\n        toDispose.add(model.onDidChangeContent((e) => {\n            this._proxy.$acceptModelChanged(modelUrl.toString(), e);\n        }));\n        toDispose.add(model.onWillDispose(() => {\n            this._stopModelSync(modelUrl);\n        }));\n        toDispose.add(toDisposable(() => {\n            this._proxy.$acceptRemovedModel(modelUrl);\n        }));\n        this._syncedModels[modelUrl] = toDispose;\n    }\n    _stopModelSync(modelUrl) {\n        const toDispose = this._syncedModels[modelUrl];\n        delete this._syncedModels[modelUrl];\n        delete this._syncedModelsLastUsedTime[modelUrl];\n        dispose(toDispose);\n    }\n}\nexport class WorkerTextModelSyncServer {\n    constructor() {\n        this._models = Object.create(null);\n    }\n    getModel(uri) {\n        return this._models[uri];\n    }\n    getModels() {\n        const all = [];\n        Object.keys(this._models).forEach((key) => all.push(this._models[key]));\n        return all;\n    }\n    $acceptNewModel(data) {\n        this._models[data.url] = new MirrorModel(URI.parse(data.url), data.lines, data.EOL, data.versionId);\n    }\n    $acceptModelChanged(uri, e) {\n        if (!this._models[uri]) {\n            return;\n        }\n        const model = this._models[uri];\n        model.onEvents(e);\n    }\n    $acceptRemovedModel(uri) {\n        if (!this._models[uri]) {\n            return;\n        }\n        delete this._models[uri];\n    }\n}\nexport class MirrorModel extends BaseMirrorModel {\n    get uri() {\n        return this._uri;\n    }\n    get eol() {\n        return this._eol;\n    }\n    getValue() {\n        return this.getText();\n    }\n    findMatches(regex) {\n        const matches = [];\n        for (let i = 0; i < this._lines.length; i++) {\n            const line = this._lines[i];\n            const offsetToAdd = this.offsetAt(new Position(i + 1, 1));\n            const iteratorOverMatches = line.matchAll(regex);\n            for (const match of iteratorOverMatches) {\n                if (match.index || match.index === 0) {\n                    match.index = match.index + offsetToAdd;\n                }\n                matches.push(match);\n            }\n        }\n        return matches;\n    }\n    getLinesContent() {\n        return this._lines.slice(0);\n    }\n    getLineCount() {\n        return this._lines.length;\n    }\n    getLineContent(lineNumber) {\n        return this._lines[lineNumber - 1];\n    }\n    getWordAtPosition(position, wordDefinition) {\n        const wordAtText = getWordAtText(position.column, ensureValidWordDefinition(wordDefinition), this._lines[position.lineNumber - 1], 0);\n        if (wordAtText) {\n            return new Range(position.lineNumber, wordAtText.startColumn, position.lineNumber, wordAtText.endColumn);\n        }\n        return null;\n    }\n    words(wordDefinition) {\n        const lines = this._lines;\n        const wordenize = this._wordenize.bind(this);\n        let lineNumber = 0;\n        let lineText = '';\n        let wordRangesIdx = 0;\n        let wordRanges = [];\n        return {\n            *[Symbol.iterator]() {\n                while (true) {\n                    if (wordRangesIdx < wordRanges.length) {\n                        const value = lineText.substring(wordRanges[wordRangesIdx].start, wordRanges[wordRangesIdx].end);\n                        wordRangesIdx += 1;\n                        yield value;\n                    }\n                    else {\n                        if (lineNumber < lines.length) {\n                            lineText = lines[lineNumber];\n                            wordRanges = wordenize(lineText, wordDefinition);\n                            wordRangesIdx = 0;\n                            lineNumber += 1;\n                        }\n                        else {\n                            break;\n                        }\n                    }\n                }\n            }\n        };\n    }\n    getLineWords(lineNumber, wordDefinition) {\n        const content = this._lines[lineNumber - 1];\n        const ranges = this._wordenize(content, wordDefinition);\n        const words = [];\n        for (const range of ranges) {\n            words.push({\n                word: content.substring(range.start, range.end),\n                startColumn: range.start + 1,\n                endColumn: range.end + 1\n            });\n        }\n        return words;\n    }\n    _wordenize(content, wordDefinition) {\n        const result = [];\n        let match;\n        wordDefinition.lastIndex = 0; // reset lastIndex just to be sure\n        while (match = wordDefinition.exec(content)) {\n            if (match[0].length === 0) {\n                // it did match the empty string\n                break;\n            }\n            result.push({ start: match.index, end: match.index + match[0].length });\n        }\n        return result;\n    }\n    getValueInRange(range) {\n        range = this._validateRange(range);\n        if (range.startLineNumber === range.endLineNumber) {\n            return this._lines[range.startLineNumber - 1].substring(range.startColumn - 1, range.endColumn - 1);\n        }\n        const lineEnding = this._eol;\n        const startLineIndex = range.startLineNumber - 1;\n        const endLineIndex = range.endLineNumber - 1;\n        const resultLines = [];\n        resultLines.push(this._lines[startLineIndex].substring(range.startColumn - 1));\n        for (let i = startLineIndex + 1; i < endLineIndex; i++) {\n            resultLines.push(this._lines[i]);\n        }\n        resultLines.push(this._lines[endLineIndex].substring(0, range.endColumn - 1));\n        return resultLines.join(lineEnding);\n    }\n    offsetAt(position) {\n        position = this._validatePosition(position);\n        this._ensureLineStarts();\n        return this._lineStarts.getPrefixSum(position.lineNumber - 2) + (position.column - 1);\n    }\n    positionAt(offset) {\n        offset = Math.floor(offset);\n        offset = Math.max(0, offset);\n        this._ensureLineStarts();\n        const out = this._lineStarts.getIndexOf(offset);\n        const lineLength = this._lines[out.index].length;\n        // Ensure we return a valid position\n        return {\n            lineNumber: 1 + out.index,\n            column: 1 + Math.min(out.remainder, lineLength)\n        };\n    }\n    _validateRange(range) {\n        const start = this._validatePosition({ lineNumber: range.startLineNumber, column: range.startColumn });\n        const end = this._validatePosition({ lineNumber: range.endLineNumber, column: range.endColumn });\n        if (start.lineNumber !== range.startLineNumber\n            || start.column !== range.startColumn\n            || end.lineNumber !== range.endLineNumber\n            || end.column !== range.endColumn) {\n            return {\n                startLineNumber: start.lineNumber,\n                startColumn: start.column,\n                endLineNumber: end.lineNumber,\n                endColumn: end.column\n            };\n        }\n        return range;\n    }\n    _validatePosition(position) {\n        if (!Position.isIPosition(position)) {\n            throw new Error('bad position');\n        }\n        let { lineNumber, column } = position;\n        let hasChanged = false;\n        if (lineNumber < 1) {\n            lineNumber = 1;\n            column = 1;\n            hasChanged = true;\n        }\n        else if (lineNumber > this._lines.length) {\n            lineNumber = this._lines.length;\n            column = this._lines[lineNumber - 1].length + 1;\n            hasChanged = true;\n        }\n        else {\n            const maxCharacter = this._lines[lineNumber - 1].length + 1;\n            if (column < 1) {\n                column = 1;\n                hasChanged = true;\n            }\n            else if (column > maxCharacter) {\n                column = maxCharacter;\n                hasChanged = true;\n            }\n        }\n        if (!hasChanged) {\n            return position;\n        }\n        else {\n            return { lineNumber, column };\n        }\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,aAAa,QAAQ,kCAAkC;AAChE,SAASC,UAAU,EAAEC,eAAe,EAAEC,OAAO,EAAEC,YAAY,QAAQ,sCAAsC;AACzG,SAASC,GAAG,QAAQ,gCAAgC;AACpD,SAASC,QAAQ,QAAQ,wBAAwB;AACjD,SAASC,KAAK,QAAQ,qBAAqB;AAC3C,SAASC,yBAAyB,EAAEC,aAAa,QAAQ,0BAA0B;AACnF,SAASC,eAAe,IAAIC,eAAe,QAAQ,gCAAgC;AACnF;AACA;AACA;AACA,OAAO,MAAMC,6BAA6B,GAAG,EAAE,GAAG,IAAI;AACtD,OAAO,MAAMC,yBAAyB,SAASZ,UAAU,CAAC;EACtDa,WAAWA,CAACC,KAAK,EAAEC,YAAY,EAAEC,cAAc,GAAG,KAAK,EAAE;IACrD,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,aAAa,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IACxC,IAAI,CAACC,yBAAyB,GAAGF,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IACpD,IAAI,CAACE,MAAM,GAAGP,KAAK;IACnB,IAAI,CAACQ,aAAa,GAAGP,YAAY;IACjC,IAAI,CAACC,cAAc,EAAE;MACjB,MAAMO,KAAK,GAAG,IAAIxB,aAAa,CAAC,CAAC;MACjCwB,KAAK,CAACC,YAAY,CAAC,MAAM,IAAI,CAACC,mBAAmB,CAAC,CAAC,EAAEC,IAAI,CAACC,KAAK,CAAChB,6BAA6B,GAAG,CAAC,CAAC,CAAC;MACnG,IAAI,CAACiB,SAAS,CAACL,KAAK,CAAC;IACzB;EACJ;EACArB,OAAOA,CAAA,EAAG;IACN,KAAK,MAAM2B,QAAQ,IAAI,IAAI,CAACZ,aAAa,EAAE;MACvCf,OAAO,CAAC,IAAI,CAACe,aAAa,CAACY,QAAQ,CAAC,CAAC;IACzC;IACA,IAAI,CAACZ,aAAa,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IACxC,IAAI,CAACC,yBAAyB,GAAGF,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IACpD,KAAK,CAACjB,OAAO,CAAC,CAAC;EACnB;EACA4B,qBAAqBA,CAACC,SAAS,EAAEC,gBAAgB,GAAG,KAAK,EAAE;IACvD,KAAK,MAAMC,QAAQ,IAAIF,SAAS,EAAE;MAC9B,MAAMG,WAAW,GAAGD,QAAQ,CAACE,QAAQ,CAAC,CAAC;MACvC,IAAI,CAAC,IAAI,CAAClB,aAAa,CAACiB,WAAW,CAAC,EAAE;QAClC,IAAI,CAACE,eAAe,CAACH,QAAQ,EAAED,gBAAgB,CAAC;MACpD;MACA,IAAI,IAAI,CAACf,aAAa,CAACiB,WAAW,CAAC,EAAE;QACjC,IAAI,CAACd,yBAAyB,CAACc,WAAW,CAAC,GAAI,IAAIG,IAAI,CAAC,CAAC,CAAEC,OAAO,CAAC,CAAC;MACxE;IACJ;EACJ;EACAb,mBAAmBA,CAAA,EAAG;IAClB,MAAMc,WAAW,GAAI,IAAIF,IAAI,CAAC,CAAC,CAAEC,OAAO,CAAC,CAAC;IAC1C,MAAME,QAAQ,GAAG,EAAE;IACnB,KAAK,MAAMX,QAAQ,IAAI,IAAI,CAACT,yBAAyB,EAAE;MACnD,MAAMqB,WAAW,GAAGF,WAAW,GAAG,IAAI,CAACnB,yBAAyB,CAACS,QAAQ,CAAC;MAC1E,IAAIY,WAAW,GAAG9B,6BAA6B,EAAE;QAC7C6B,QAAQ,CAACE,IAAI,CAACb,QAAQ,CAAC;MAC3B;IACJ;IACA,KAAK,MAAMc,CAAC,IAAIH,QAAQ,EAAE;MACtB,IAAI,CAACI,cAAc,CAACD,CAAC,CAAC;IAC1B;EACJ;EACAP,eAAeA,CAACH,QAAQ,EAAED,gBAAgB,EAAE;IACxC,MAAMa,KAAK,GAAG,IAAI,CAACvB,aAAa,CAACwB,QAAQ,CAACb,QAAQ,CAAC;IACnD,IAAI,CAACY,KAAK,EAAE;MACR;IACJ;IACA,IAAI,CAACb,gBAAgB,IAAIa,KAAK,CAACE,oBAAoB,CAAC,CAAC,EAAE;MACnD;IACJ;IACA,MAAMlB,QAAQ,GAAGI,QAAQ,CAACE,QAAQ,CAAC,CAAC;IACpC,IAAI,CAACd,MAAM,CAAC2B,eAAe,CAAC;MACxBC,GAAG,EAAEJ,KAAK,CAACK,GAAG,CAACf,QAAQ,CAAC,CAAC;MACzBgB,KAAK,EAAEN,KAAK,CAACO,eAAe,CAAC,CAAC;MAC9BC,GAAG,EAAER,KAAK,CAACS,MAAM,CAAC,CAAC;MACnBC,SAAS,EAAEV,KAAK,CAACW,YAAY,CAAC;IAClC,CAAC,CAAC;IACF,MAAMC,SAAS,GAAG,IAAIxD,eAAe,CAAC,CAAC;IACvCwD,SAAS,CAACC,GAAG,CAACb,KAAK,CAACc,kBAAkB,CAAEhB,CAAC,IAAK;MAC1C,IAAI,CAACtB,MAAM,CAACuC,mBAAmB,CAAC/B,QAAQ,CAACM,QAAQ,CAAC,CAAC,EAAEQ,CAAC,CAAC;IAC3D,CAAC,CAAC,CAAC;IACHc,SAAS,CAACC,GAAG,CAACb,KAAK,CAACgB,aAAa,CAAC,MAAM;MACpC,IAAI,CAACjB,cAAc,CAACf,QAAQ,CAAC;IACjC,CAAC,CAAC,CAAC;IACH4B,SAAS,CAACC,GAAG,CAACvD,YAAY,CAAC,MAAM;MAC7B,IAAI,CAACkB,MAAM,CAACyC,mBAAmB,CAACjC,QAAQ,CAAC;IAC7C,CAAC,CAAC,CAAC;IACH,IAAI,CAACZ,aAAa,CAACY,QAAQ,CAAC,GAAG4B,SAAS;EAC5C;EACAb,cAAcA,CAACf,QAAQ,EAAE;IACrB,MAAM4B,SAAS,GAAG,IAAI,CAACxC,aAAa,CAACY,QAAQ,CAAC;IAC9C,OAAO,IAAI,CAACZ,aAAa,CAACY,QAAQ,CAAC;IACnC,OAAO,IAAI,CAACT,yBAAyB,CAACS,QAAQ,CAAC;IAC/C3B,OAAO,CAACuD,SAAS,CAAC;EACtB;AACJ;AACA,OAAO,MAAMM,yBAAyB,CAAC;EACnClD,WAAWA,CAAA,EAAG;IACV,IAAI,CAACmD,OAAO,GAAG9C,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EACtC;EACA2B,QAAQA,CAACI,GAAG,EAAE;IACV,OAAO,IAAI,CAACc,OAAO,CAACd,GAAG,CAAC;EAC5B;EACAe,SAASA,CAAA,EAAG;IACR,MAAMC,GAAG,GAAG,EAAE;IACdhD,MAAM,CAACiD,IAAI,CAAC,IAAI,CAACH,OAAO,CAAC,CAACI,OAAO,CAAEC,GAAG,IAAKH,GAAG,CAACxB,IAAI,CAAC,IAAI,CAACsB,OAAO,CAACK,GAAG,CAAC,CAAC,CAAC;IACvE,OAAOH,GAAG;EACd;EACAlB,eAAeA,CAACsB,IAAI,EAAE;IAClB,IAAI,CAACN,OAAO,CAACM,IAAI,CAACrB,GAAG,CAAC,GAAG,IAAIsB,WAAW,CAACnE,GAAG,CAACoE,KAAK,CAACF,IAAI,CAACrB,GAAG,CAAC,EAAEqB,IAAI,CAACnB,KAAK,EAAEmB,IAAI,CAACjB,GAAG,EAAEiB,IAAI,CAACf,SAAS,CAAC;EACvG;EACAK,mBAAmBA,CAACV,GAAG,EAAEP,CAAC,EAAE;IACxB,IAAI,CAAC,IAAI,CAACqB,OAAO,CAACd,GAAG,CAAC,EAAE;MACpB;IACJ;IACA,MAAML,KAAK,GAAG,IAAI,CAACmB,OAAO,CAACd,GAAG,CAAC;IAC/BL,KAAK,CAAC4B,QAAQ,CAAC9B,CAAC,CAAC;EACrB;EACAmB,mBAAmBA,CAACZ,GAAG,EAAE;IACrB,IAAI,CAAC,IAAI,CAACc,OAAO,CAACd,GAAG,CAAC,EAAE;MACpB;IACJ;IACA,OAAO,IAAI,CAACc,OAAO,CAACd,GAAG,CAAC;EAC5B;AACJ;AACA,OAAO,MAAMqB,WAAW,SAAS7D,eAAe,CAAC;EAC7C,IAAIwC,GAAGA,CAAA,EAAG;IACN,OAAO,IAAI,CAACwB,IAAI;EACpB;EACA,IAAIC,GAAGA,CAAA,EAAG;IACN,OAAO,IAAI,CAACC,IAAI;EACpB;EACAC,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACC,OAAO,CAAC,CAAC;EACzB;EACAC,WAAWA,CAACC,KAAK,EAAE;IACf,MAAMC,OAAO,GAAG,EAAE;IAClB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACC,MAAM,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MACzC,MAAMG,IAAI,GAAG,IAAI,CAACF,MAAM,CAACD,CAAC,CAAC;MAC3B,MAAMI,WAAW,GAAG,IAAI,CAACC,QAAQ,CAAC,IAAIlF,QAAQ,CAAC6E,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;MACzD,MAAMM,mBAAmB,GAAGH,IAAI,CAACI,QAAQ,CAACT,KAAK,CAAC;MAChD,KAAK,MAAMU,KAAK,IAAIF,mBAAmB,EAAE;QACrC,IAAIE,KAAK,CAACC,KAAK,IAAID,KAAK,CAACC,KAAK,KAAK,CAAC,EAAE;UAClCD,KAAK,CAACC,KAAK,GAAGD,KAAK,CAACC,KAAK,GAAGL,WAAW;QAC3C;QACAL,OAAO,CAACvC,IAAI,CAACgD,KAAK,CAAC;MACvB;IACJ;IACA,OAAOT,OAAO;EAClB;EACA7B,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAAC+B,MAAM,CAACS,KAAK,CAAC,CAAC,CAAC;EAC/B;EACAC,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAACV,MAAM,CAACC,MAAM;EAC7B;EACAU,cAAcA,CAACC,UAAU,EAAE;IACvB,OAAO,IAAI,CAACZ,MAAM,CAACY,UAAU,GAAG,CAAC,CAAC;EACtC;EACAC,iBAAiBA,CAACC,QAAQ,EAAEC,cAAc,EAAE;IACxC,MAAMC,UAAU,GAAG3F,aAAa,CAACyF,QAAQ,CAACG,MAAM,EAAE7F,yBAAyB,CAAC2F,cAAc,CAAC,EAAE,IAAI,CAACf,MAAM,CAACc,QAAQ,CAACF,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IACrI,IAAII,UAAU,EAAE;MACZ,OAAO,IAAI7F,KAAK,CAAC2F,QAAQ,CAACF,UAAU,EAAEI,UAAU,CAACE,WAAW,EAAEJ,QAAQ,CAACF,UAAU,EAAEI,UAAU,CAACG,SAAS,CAAC;IAC5G;IACA,OAAO,IAAI;EACf;EACAC,KAAKA,CAACL,cAAc,EAAE;IAClB,MAAM/C,KAAK,GAAG,IAAI,CAACgC,MAAM;IACzB,MAAMqB,SAAS,GAAG,IAAI,CAACC,UAAU,CAACC,IAAI,CAAC,IAAI,CAAC;IAC5C,IAAIX,UAAU,GAAG,CAAC;IAClB,IAAIY,QAAQ,GAAG,EAAE;IACjB,IAAIC,aAAa,GAAG,CAAC;IACrB,IAAIC,UAAU,GAAG,EAAE;IACnB,OAAO;MACH,EAAEC,MAAM,CAACC,QAAQ,IAAI;QACjB,OAAO,IAAI,EAAE;UACT,IAAIH,aAAa,GAAGC,UAAU,CAACzB,MAAM,EAAE;YACnC,MAAM4B,KAAK,GAAGL,QAAQ,CAACM,SAAS,CAACJ,UAAU,CAACD,aAAa,CAAC,CAACM,KAAK,EAAEL,UAAU,CAACD,aAAa,CAAC,CAACO,GAAG,CAAC;YAChGP,aAAa,IAAI,CAAC;YAClB,MAAMI,KAAK;UACf,CAAC,MACI;YACD,IAAIjB,UAAU,GAAG5C,KAAK,CAACiC,MAAM,EAAE;cAC3BuB,QAAQ,GAAGxD,KAAK,CAAC4C,UAAU,CAAC;cAC5Bc,UAAU,GAAGL,SAAS,CAACG,QAAQ,EAAET,cAAc,CAAC;cAChDU,aAAa,GAAG,CAAC;cACjBb,UAAU,IAAI,CAAC;YACnB,CAAC,MACI;cACD;YACJ;UACJ;QACJ;MACJ;IACJ,CAAC;EACL;EACAqB,YAAYA,CAACrB,UAAU,EAAEG,cAAc,EAAE;IACrC,MAAMmB,OAAO,GAAG,IAAI,CAAClC,MAAM,CAACY,UAAU,GAAG,CAAC,CAAC;IAC3C,MAAMuB,MAAM,GAAG,IAAI,CAACb,UAAU,CAACY,OAAO,EAAEnB,cAAc,CAAC;IACvD,MAAMK,KAAK,GAAG,EAAE;IAChB,KAAK,MAAMgB,KAAK,IAAID,MAAM,EAAE;MACxBf,KAAK,CAAC7D,IAAI,CAAC;QACP8E,IAAI,EAAEH,OAAO,CAACJ,SAAS,CAACM,KAAK,CAACL,KAAK,EAAEK,KAAK,CAACJ,GAAG,CAAC;QAC/Cd,WAAW,EAAEkB,KAAK,CAACL,KAAK,GAAG,CAAC;QAC5BZ,SAAS,EAAEiB,KAAK,CAACJ,GAAG,GAAG;MAC3B,CAAC,CAAC;IACN;IACA,OAAOZ,KAAK;EAChB;EACAE,UAAUA,CAACY,OAAO,EAAEnB,cAAc,EAAE;IAChC,MAAMuB,MAAM,GAAG,EAAE;IACjB,IAAI/B,KAAK;IACTQ,cAAc,CAACwB,SAAS,GAAG,CAAC,CAAC,CAAC;IAC9B,OAAOhC,KAAK,GAAGQ,cAAc,CAACyB,IAAI,CAACN,OAAO,CAAC,EAAE;MACzC,IAAI3B,KAAK,CAAC,CAAC,CAAC,CAACN,MAAM,KAAK,CAAC,EAAE;QACvB;QACA;MACJ;MACAqC,MAAM,CAAC/E,IAAI,CAAC;QAAEwE,KAAK,EAAExB,KAAK,CAACC,KAAK;QAAEwB,GAAG,EAAEzB,KAAK,CAACC,KAAK,GAAGD,KAAK,CAAC,CAAC,CAAC,CAACN;MAAO,CAAC,CAAC;IAC3E;IACA,OAAOqC,MAAM;EACjB;EACAG,eAAeA,CAACL,KAAK,EAAE;IACnBA,KAAK,GAAG,IAAI,CAACM,cAAc,CAACN,KAAK,CAAC;IAClC,IAAIA,KAAK,CAACO,eAAe,KAAKP,KAAK,CAACQ,aAAa,EAAE;MAC/C,OAAO,IAAI,CAAC5C,MAAM,CAACoC,KAAK,CAACO,eAAe,GAAG,CAAC,CAAC,CAACb,SAAS,CAACM,KAAK,CAAClB,WAAW,GAAG,CAAC,EAAEkB,KAAK,CAACjB,SAAS,GAAG,CAAC,CAAC;IACvG;IACA,MAAM0B,UAAU,GAAG,IAAI,CAACpD,IAAI;IAC5B,MAAMqD,cAAc,GAAGV,KAAK,CAACO,eAAe,GAAG,CAAC;IAChD,MAAMI,YAAY,GAAGX,KAAK,CAACQ,aAAa,GAAG,CAAC;IAC5C,MAAMI,WAAW,GAAG,EAAE;IACtBA,WAAW,CAACzF,IAAI,CAAC,IAAI,CAACyC,MAAM,CAAC8C,cAAc,CAAC,CAAChB,SAAS,CAACM,KAAK,CAAClB,WAAW,GAAG,CAAC,CAAC,CAAC;IAC9E,KAAK,IAAInB,CAAC,GAAG+C,cAAc,GAAG,CAAC,EAAE/C,CAAC,GAAGgD,YAAY,EAAEhD,CAAC,EAAE,EAAE;MACpDiD,WAAW,CAACzF,IAAI,CAAC,IAAI,CAACyC,MAAM,CAACD,CAAC,CAAC,CAAC;IACpC;IACAiD,WAAW,CAACzF,IAAI,CAAC,IAAI,CAACyC,MAAM,CAAC+C,YAAY,CAAC,CAACjB,SAAS,CAAC,CAAC,EAAEM,KAAK,CAACjB,SAAS,GAAG,CAAC,CAAC,CAAC;IAC7E,OAAO6B,WAAW,CAACC,IAAI,CAACJ,UAAU,CAAC;EACvC;EACAzC,QAAQA,CAACU,QAAQ,EAAE;IACfA,QAAQ,GAAG,IAAI,CAACoC,iBAAiB,CAACpC,QAAQ,CAAC;IAC3C,IAAI,CAACqC,iBAAiB,CAAC,CAAC;IACxB,OAAO,IAAI,CAACC,WAAW,CAACC,YAAY,CAACvC,QAAQ,CAACF,UAAU,GAAG,CAAC,CAAC,IAAIE,QAAQ,CAACG,MAAM,GAAG,CAAC,CAAC;EACzF;EACAqC,UAAUA,CAACC,MAAM,EAAE;IACfA,MAAM,GAAGhH,IAAI,CAACiH,KAAK,CAACD,MAAM,CAAC;IAC3BA,MAAM,GAAGhH,IAAI,CAACkH,GAAG,CAAC,CAAC,EAAEF,MAAM,CAAC;IAC5B,IAAI,CAACJ,iBAAiB,CAAC,CAAC;IACxB,MAAMO,GAAG,GAAG,IAAI,CAACN,WAAW,CAACO,UAAU,CAACJ,MAAM,CAAC;IAC/C,MAAMK,UAAU,GAAG,IAAI,CAAC5D,MAAM,CAAC0D,GAAG,CAAClD,KAAK,CAAC,CAACP,MAAM;IAChD;IACA,OAAO;MACHW,UAAU,EAAE,CAAC,GAAG8C,GAAG,CAAClD,KAAK;MACzBS,MAAM,EAAE,CAAC,GAAG1E,IAAI,CAACsH,GAAG,CAACH,GAAG,CAACI,SAAS,EAAEF,UAAU;IAClD,CAAC;EACL;EACAlB,cAAcA,CAACN,KAAK,EAAE;IAClB,MAAML,KAAK,GAAG,IAAI,CAACmB,iBAAiB,CAAC;MAAEtC,UAAU,EAAEwB,KAAK,CAACO,eAAe;MAAE1B,MAAM,EAAEmB,KAAK,CAAClB;IAAY,CAAC,CAAC;IACtG,MAAMc,GAAG,GAAG,IAAI,CAACkB,iBAAiB,CAAC;MAAEtC,UAAU,EAAEwB,KAAK,CAACQ,aAAa;MAAE3B,MAAM,EAAEmB,KAAK,CAACjB;IAAU,CAAC,CAAC;IAChG,IAAIY,KAAK,CAACnB,UAAU,KAAKwB,KAAK,CAACO,eAAe,IACvCZ,KAAK,CAACd,MAAM,KAAKmB,KAAK,CAAClB,WAAW,IAClCc,GAAG,CAACpB,UAAU,KAAKwB,KAAK,CAACQ,aAAa,IACtCZ,GAAG,CAACf,MAAM,KAAKmB,KAAK,CAACjB,SAAS,EAAE;MACnC,OAAO;QACHwB,eAAe,EAAEZ,KAAK,CAACnB,UAAU;QACjCM,WAAW,EAAEa,KAAK,CAACd,MAAM;QACzB2B,aAAa,EAAEZ,GAAG,CAACpB,UAAU;QAC7BO,SAAS,EAAEa,GAAG,CAACf;MACnB,CAAC;IACL;IACA,OAAOmB,KAAK;EAChB;EACAc,iBAAiBA,CAACpC,QAAQ,EAAE;IACxB,IAAI,CAAC5F,QAAQ,CAAC6I,WAAW,CAACjD,QAAQ,CAAC,EAAE;MACjC,MAAM,IAAIkD,KAAK,CAAC,cAAc,CAAC;IACnC;IACA,IAAI;MAAEpD,UAAU;MAAEK;IAAO,CAAC,GAAGH,QAAQ;IACrC,IAAImD,UAAU,GAAG,KAAK;IACtB,IAAIrD,UAAU,GAAG,CAAC,EAAE;MAChBA,UAAU,GAAG,CAAC;MACdK,MAAM,GAAG,CAAC;MACVgD,UAAU,GAAG,IAAI;IACrB,CAAC,MACI,IAAIrD,UAAU,GAAG,IAAI,CAACZ,MAAM,CAACC,MAAM,EAAE;MACtCW,UAAU,GAAG,IAAI,CAACZ,MAAM,CAACC,MAAM;MAC/BgB,MAAM,GAAG,IAAI,CAACjB,MAAM,CAACY,UAAU,GAAG,CAAC,CAAC,CAACX,MAAM,GAAG,CAAC;MAC/CgE,UAAU,GAAG,IAAI;IACrB,CAAC,MACI;MACD,MAAMC,YAAY,GAAG,IAAI,CAAClE,MAAM,CAACY,UAAU,GAAG,CAAC,CAAC,CAACX,MAAM,GAAG,CAAC;MAC3D,IAAIgB,MAAM,GAAG,CAAC,EAAE;QACZA,MAAM,GAAG,CAAC;QACVgD,UAAU,GAAG,IAAI;MACrB,CAAC,MACI,IAAIhD,MAAM,GAAGiD,YAAY,EAAE;QAC5BjD,MAAM,GAAGiD,YAAY;QACrBD,UAAU,GAAG,IAAI;MACrB;IACJ;IACA,IAAI,CAACA,UAAU,EAAE;MACb,OAAOnD,QAAQ;IACnB,CAAC,MACI;MACD,OAAO;QAAEF,UAAU;QAAEK;MAAO,CAAC;IACjC;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}