{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = this && this.__param || function (paramIndex, decorator) {\n  return function (target, key) {\n    decorator(target, key, paramIndex);\n  };\n};\nimport { derived, observableFromEvent, observableValue } from '../../../../base/common/observable.js';\nimport { derivedConstOnceDefined } from '../../../../base/common/observableInternal/utils.js';\nimport { allowsTrueInlineDiffRendering } from './components/diffEditorViewZones/diffEditorViewZones.js';\nimport { diffEditorDefaultOptions } from '../../../common/config/diffEditor.js';\nimport { clampedFloat, clampedInt, boolean as validateBooleanOption, stringSet as validateStringSetOption } from '../../../common/config/editorOptions.js';\nimport { IAccessibilityService } from '../../../../platform/accessibility/common/accessibility.js';\nlet DiffEditorOptions = class DiffEditorOptions {\n  get editorOptions() {\n    return this._options;\n  }\n  constructor(options, _accessibilityService) {\n    this._accessibilityService = _accessibilityService;\n    this._diffEditorWidth = observableValue(this, 0);\n    this._screenReaderMode = observableFromEvent(this, this._accessibilityService.onDidChangeScreenReaderOptimized, () => this._accessibilityService.isScreenReaderOptimized());\n    this.couldShowInlineViewBecauseOfSize = derived(this, reader => this._options.read(reader).renderSideBySide && this._diffEditorWidth.read(reader) <= this._options.read(reader).renderSideBySideInlineBreakpoint);\n    this.renderOverviewRuler = derived(this, reader => this._options.read(reader).renderOverviewRuler);\n    this.renderSideBySide = derived(this, reader => {\n      if (this.compactMode.read(reader)) {\n        if (this.shouldRenderInlineViewInSmartMode.read(reader)) {\n          return false;\n        }\n      }\n      return this._options.read(reader).renderSideBySide && !(this._options.read(reader).useInlineViewWhenSpaceIsLimited && this.couldShowInlineViewBecauseOfSize.read(reader) && !this._screenReaderMode.read(reader));\n    });\n    this.readOnly = derived(this, reader => this._options.read(reader).readOnly);\n    this.shouldRenderOldRevertArrows = derived(this, reader => {\n      if (!this._options.read(reader).renderMarginRevertIcon) {\n        return false;\n      }\n      if (!this.renderSideBySide.read(reader)) {\n        return false;\n      }\n      if (this.readOnly.read(reader)) {\n        return false;\n      }\n      if (this.shouldRenderGutterMenu.read(reader)) {\n        return false;\n      }\n      return true;\n    });\n    this.shouldRenderGutterMenu = derived(this, reader => this._options.read(reader).renderGutterMenu);\n    this.renderIndicators = derived(this, reader => this._options.read(reader).renderIndicators);\n    this.enableSplitViewResizing = derived(this, reader => this._options.read(reader).enableSplitViewResizing);\n    this.splitViewDefaultRatio = derived(this, reader => this._options.read(reader).splitViewDefaultRatio);\n    this.ignoreTrimWhitespace = derived(this, reader => this._options.read(reader).ignoreTrimWhitespace);\n    this.maxComputationTimeMs = derived(this, reader => this._options.read(reader).maxComputationTime);\n    this.showMoves = derived(this, reader => this._options.read(reader).experimental.showMoves && this.renderSideBySide.read(reader));\n    this.isInEmbeddedEditor = derived(this, reader => this._options.read(reader).isInEmbeddedEditor);\n    this.diffWordWrap = derived(this, reader => this._options.read(reader).diffWordWrap);\n    this.originalEditable = derived(this, reader => this._options.read(reader).originalEditable);\n    this.diffCodeLens = derived(this, reader => this._options.read(reader).diffCodeLens);\n    this.accessibilityVerbose = derived(this, reader => this._options.read(reader).accessibilityVerbose);\n    this.diffAlgorithm = derived(this, reader => this._options.read(reader).diffAlgorithm);\n    this.showEmptyDecorations = derived(this, reader => this._options.read(reader).experimental.showEmptyDecorations);\n    this.onlyShowAccessibleDiffViewer = derived(this, reader => this._options.read(reader).onlyShowAccessibleDiffViewer);\n    this.compactMode = derived(this, reader => this._options.read(reader).compactMode);\n    this.trueInlineDiffRenderingEnabled = derived(this, reader => this._options.read(reader).experimental.useTrueInlineView);\n    this.useTrueInlineDiffRendering = derived(this, reader => !this.renderSideBySide.read(reader) && this.trueInlineDiffRenderingEnabled.read(reader));\n    this.hideUnchangedRegions = derived(this, reader => this._options.read(reader).hideUnchangedRegions.enabled);\n    this.hideUnchangedRegionsRevealLineCount = derived(this, reader => this._options.read(reader).hideUnchangedRegions.revealLineCount);\n    this.hideUnchangedRegionsContextLineCount = derived(this, reader => this._options.read(reader).hideUnchangedRegions.contextLineCount);\n    this.hideUnchangedRegionsMinimumLineCount = derived(this, reader => this._options.read(reader).hideUnchangedRegions.minimumLineCount);\n    this._model = observableValue(this, undefined);\n    this.shouldRenderInlineViewInSmartMode = this._model.map(this, model => derivedConstOnceDefined(this, reader => {\n      const diffs = model?.diff.read(reader);\n      return diffs ? isSimpleDiff(diffs, this.trueInlineDiffRenderingEnabled.read(reader)) : undefined;\n    })).flatten().map(this, v => !!v);\n    this.inlineViewHideOriginalLineNumbers = this.compactMode;\n    const optionsCopy = {\n      ...options,\n      ...validateDiffEditorOptions(options, diffEditorDefaultOptions)\n    };\n    this._options = observableValue(this, optionsCopy);\n  }\n  updateOptions(changedOptions) {\n    const newDiffEditorOptions = validateDiffEditorOptions(changedOptions, this._options.get());\n    const newOptions = {\n      ...this._options.get(),\n      ...changedOptions,\n      ...newDiffEditorOptions\n    };\n    this._options.set(newOptions, undefined, {\n      changedOptions: changedOptions\n    });\n  }\n  setWidth(width) {\n    this._diffEditorWidth.set(width, undefined);\n  }\n  setModel(model) {\n    this._model.set(model, undefined);\n  }\n};\nDiffEditorOptions = __decorate([__param(1, IAccessibilityService)], DiffEditorOptions);\nexport { DiffEditorOptions };\nfunction isSimpleDiff(diff, supportsTrueDiffRendering) {\n  return diff.mappings.every(m => isInsertion(m.lineRangeMapping) || isDeletion(m.lineRangeMapping) || supportsTrueDiffRendering && allowsTrueInlineDiffRendering(m.lineRangeMapping));\n}\nfunction isInsertion(mapping) {\n  return mapping.original.length === 0;\n}\nfunction isDeletion(mapping) {\n  return mapping.modified.length === 0;\n}\nfunction validateDiffEditorOptions(options, defaults) {\n  return {\n    enableSplitViewResizing: validateBooleanOption(options.enableSplitViewResizing, defaults.enableSplitViewResizing),\n    splitViewDefaultRatio: clampedFloat(options.splitViewDefaultRatio, 0.5, 0.1, 0.9),\n    renderSideBySide: validateBooleanOption(options.renderSideBySide, defaults.renderSideBySide),\n    renderMarginRevertIcon: validateBooleanOption(options.renderMarginRevertIcon, defaults.renderMarginRevertIcon),\n    maxComputationTime: clampedInt(options.maxComputationTime, defaults.maxComputationTime, 0, 1073741824 /* Constants.MAX_SAFE_SMALL_INTEGER */),\n    maxFileSize: clampedInt(options.maxFileSize, defaults.maxFileSize, 0, 1073741824 /* Constants.MAX_SAFE_SMALL_INTEGER */),\n    ignoreTrimWhitespace: validateBooleanOption(options.ignoreTrimWhitespace, defaults.ignoreTrimWhitespace),\n    renderIndicators: validateBooleanOption(options.renderIndicators, defaults.renderIndicators),\n    originalEditable: validateBooleanOption(options.originalEditable, defaults.originalEditable),\n    diffCodeLens: validateBooleanOption(options.diffCodeLens, defaults.diffCodeLens),\n    renderOverviewRuler: validateBooleanOption(options.renderOverviewRuler, defaults.renderOverviewRuler),\n    diffWordWrap: validateStringSetOption(options.diffWordWrap, defaults.diffWordWrap, ['off', 'on', 'inherit']),\n    diffAlgorithm: validateStringSetOption(options.diffAlgorithm, defaults.diffAlgorithm, ['legacy', 'advanced'], {\n      'smart': 'legacy',\n      'experimental': 'advanced'\n    }),\n    accessibilityVerbose: validateBooleanOption(options.accessibilityVerbose, defaults.accessibilityVerbose),\n    experimental: {\n      showMoves: validateBooleanOption(options.experimental?.showMoves, defaults.experimental.showMoves),\n      showEmptyDecorations: validateBooleanOption(options.experimental?.showEmptyDecorations, defaults.experimental.showEmptyDecorations),\n      useTrueInlineView: validateBooleanOption(options.experimental?.useTrueInlineView, defaults.experimental.useTrueInlineView)\n    },\n    hideUnchangedRegions: {\n      enabled: validateBooleanOption(options.hideUnchangedRegions?.enabled ?? options.experimental?.collapseUnchangedRegions, defaults.hideUnchangedRegions.enabled),\n      contextLineCount: clampedInt(options.hideUnchangedRegions?.contextLineCount, defaults.hideUnchangedRegions.contextLineCount, 0, 1073741824 /* Constants.MAX_SAFE_SMALL_INTEGER */),\n      minimumLineCount: clampedInt(options.hideUnchangedRegions?.minimumLineCount, defaults.hideUnchangedRegions.minimumLineCount, 0, 1073741824 /* Constants.MAX_SAFE_SMALL_INTEGER */),\n      revealLineCount: clampedInt(options.hideUnchangedRegions?.revealLineCount, defaults.hideUnchangedRegions.revealLineCount, 0, 1073741824 /* Constants.MAX_SAFE_SMALL_INTEGER */)\n    },\n    isInEmbeddedEditor: validateBooleanOption(options.isInEmbeddedEditor, defaults.isInEmbeddedEditor),\n    onlyShowAccessibleDiffViewer: validateBooleanOption(options.onlyShowAccessibleDiffViewer, defaults.onlyShowAccessibleDiffViewer),\n    renderSideBySideInlineBreakpoint: clampedInt(options.renderSideBySideInlineBreakpoint, defaults.renderSideBySideInlineBreakpoint, 0, 1073741824 /* Constants.MAX_SAFE_SMALL_INTEGER */),\n    useInlineViewWhenSpaceIsLimited: validateBooleanOption(options.useInlineViewWhenSpaceIsLimited, defaults.useInlineViewWhenSpaceIsLimited),\n    renderGutterMenu: validateBooleanOption(options.renderGutterMenu, defaults.renderGutterMenu),\n    compactMode: validateBooleanOption(options.compactMode, defaults.compactMode)\n  };\n}", "map": {"version": 3, "names": ["__decorate", "decorators", "target", "key", "desc", "c", "arguments", "length", "r", "Object", "getOwnPropertyDescriptor", "d", "Reflect", "decorate", "i", "defineProperty", "__param", "paramIndex", "decorator", "derived", "observableFromEvent", "observableValue", "derivedConstOnceDefined", "allowsTrueInlineDiffRendering", "diffEditorDefaultOptions", "clampedFloat", "clampedInt", "boolean", "validateBooleanOption", "stringSet", "validateStringSetOption", "IAccessibilityService", "DiffEditorOptions", "editorOptions", "_options", "constructor", "options", "_accessibilityService", "_diffEditorWidth", "_screenReaderMode", "onDidChangeScreenReaderOptimized", "isScreenReaderOptimized", "couldShowInlineViewBecauseOfSize", "reader", "read", "renderSideBySide", "renderSideBySideInlineBreakpoint", "renderOverviewRuler", "compactMode", "shouldRenderInlineViewInSmartMode", "useInlineViewWhenSpaceIsLimited", "readOnly", "shouldRenderOldRevertArrows", "renderMarginRevertIcon", "shouldRenderGutterMenu", "renderGutterMenu", "renderIndicators", "enableSplitViewResizing", "splitViewDefaultRatio", "ignoreTrimWhitespace", "maxComputationTimeMs", "maxComputationTime", "showMoves", "experimental", "isInEmbeddedEditor", "diffWordWrap", "originalEditable", "diffCodeLens", "accessibilityVerbose", "diffAlgorithm", "showEmptyDecorations", "onlyShowAccessibleDiffViewer", "trueInlineDiffRenderingEnabled", "useTrueInlineView", "useTrueInlineDiffRendering", "hideUnchangedRegions", "enabled", "hideUnchangedRegionsRevealLineCount", "revealLineCount", "hideUnchangedRegionsContextLineCount", "contextLineCount", "hideUnchangedRegionsMinimumLineCount", "minimumLineCount", "_model", "undefined", "map", "model", "diffs", "diff", "isSimpleDiff", "flatten", "v", "inlineViewHideOriginalLineNumbers", "optionsCopy", "validateDiffEditorOptions", "updateOptions", "changedOptions", "newDiffEditorOptions", "get", "newOptions", "set", "<PERSON><PERSON><PERSON><PERSON>", "width", "setModel", "supportsTrueDiffRendering", "mappings", "every", "m", "isInsertion", "lineRangeMapping", "isDeletion", "mapping", "original", "modified", "defaults", "maxFileSize", "collapseUnchangedRegions"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/browser/widget/diffEditor/diffEditorOptions.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = (this && this.__param) || function (paramIndex, decorator) {\n    return function (target, key) { decorator(target, key, paramIndex); }\n};\nimport { derived, observableFromEvent, observableValue } from '../../../../base/common/observable.js';\nimport { derivedConstOnceDefined } from '../../../../base/common/observableInternal/utils.js';\nimport { allowsTrueInlineDiffRendering } from './components/diffEditorViewZones/diffEditorViewZones.js';\nimport { diffEditorDefaultOptions } from '../../../common/config/diffEditor.js';\nimport { clampedFloat, clampedInt, boolean as validateBooleanOption, stringSet as validateStringSetOption } from '../../../common/config/editorOptions.js';\nimport { IAccessibilityService } from '../../../../platform/accessibility/common/accessibility.js';\nlet DiffEditorOptions = class DiffEditorOptions {\n    get editorOptions() { return this._options; }\n    constructor(options, _accessibilityService) {\n        this._accessibilityService = _accessibilityService;\n        this._diffEditorWidth = observableValue(this, 0);\n        this._screenReaderMode = observableFromEvent(this, this._accessibilityService.onDidChangeScreenReaderOptimized, () => this._accessibilityService.isScreenReaderOptimized());\n        this.couldShowInlineViewBecauseOfSize = derived(this, reader => this._options.read(reader).renderSideBySide && this._diffEditorWidth.read(reader) <= this._options.read(reader).renderSideBySideInlineBreakpoint);\n        this.renderOverviewRuler = derived(this, reader => this._options.read(reader).renderOverviewRuler);\n        this.renderSideBySide = derived(this, reader => {\n            if (this.compactMode.read(reader)) {\n                if (this.shouldRenderInlineViewInSmartMode.read(reader)) {\n                    return false;\n                }\n            }\n            return this._options.read(reader).renderSideBySide\n                && !(this._options.read(reader).useInlineViewWhenSpaceIsLimited && this.couldShowInlineViewBecauseOfSize.read(reader) && !this._screenReaderMode.read(reader));\n        });\n        this.readOnly = derived(this, reader => this._options.read(reader).readOnly);\n        this.shouldRenderOldRevertArrows = derived(this, reader => {\n            if (!this._options.read(reader).renderMarginRevertIcon) {\n                return false;\n            }\n            if (!this.renderSideBySide.read(reader)) {\n                return false;\n            }\n            if (this.readOnly.read(reader)) {\n                return false;\n            }\n            if (this.shouldRenderGutterMenu.read(reader)) {\n                return false;\n            }\n            return true;\n        });\n        this.shouldRenderGutterMenu = derived(this, reader => this._options.read(reader).renderGutterMenu);\n        this.renderIndicators = derived(this, reader => this._options.read(reader).renderIndicators);\n        this.enableSplitViewResizing = derived(this, reader => this._options.read(reader).enableSplitViewResizing);\n        this.splitViewDefaultRatio = derived(this, reader => this._options.read(reader).splitViewDefaultRatio);\n        this.ignoreTrimWhitespace = derived(this, reader => this._options.read(reader).ignoreTrimWhitespace);\n        this.maxComputationTimeMs = derived(this, reader => this._options.read(reader).maxComputationTime);\n        this.showMoves = derived(this, reader => this._options.read(reader).experimental.showMoves && this.renderSideBySide.read(reader));\n        this.isInEmbeddedEditor = derived(this, reader => this._options.read(reader).isInEmbeddedEditor);\n        this.diffWordWrap = derived(this, reader => this._options.read(reader).diffWordWrap);\n        this.originalEditable = derived(this, reader => this._options.read(reader).originalEditable);\n        this.diffCodeLens = derived(this, reader => this._options.read(reader).diffCodeLens);\n        this.accessibilityVerbose = derived(this, reader => this._options.read(reader).accessibilityVerbose);\n        this.diffAlgorithm = derived(this, reader => this._options.read(reader).diffAlgorithm);\n        this.showEmptyDecorations = derived(this, reader => this._options.read(reader).experimental.showEmptyDecorations);\n        this.onlyShowAccessibleDiffViewer = derived(this, reader => this._options.read(reader).onlyShowAccessibleDiffViewer);\n        this.compactMode = derived(this, reader => this._options.read(reader).compactMode);\n        this.trueInlineDiffRenderingEnabled = derived(this, reader => this._options.read(reader).experimental.useTrueInlineView);\n        this.useTrueInlineDiffRendering = derived(this, reader => !this.renderSideBySide.read(reader) && this.trueInlineDiffRenderingEnabled.read(reader));\n        this.hideUnchangedRegions = derived(this, reader => this._options.read(reader).hideUnchangedRegions.enabled);\n        this.hideUnchangedRegionsRevealLineCount = derived(this, reader => this._options.read(reader).hideUnchangedRegions.revealLineCount);\n        this.hideUnchangedRegionsContextLineCount = derived(this, reader => this._options.read(reader).hideUnchangedRegions.contextLineCount);\n        this.hideUnchangedRegionsMinimumLineCount = derived(this, reader => this._options.read(reader).hideUnchangedRegions.minimumLineCount);\n        this._model = observableValue(this, undefined);\n        this.shouldRenderInlineViewInSmartMode = this._model\n            .map(this, model => derivedConstOnceDefined(this, reader => {\n            const diffs = model?.diff.read(reader);\n            return diffs ? isSimpleDiff(diffs, this.trueInlineDiffRenderingEnabled.read(reader)) : undefined;\n        }))\n            .flatten()\n            .map(this, v => !!v);\n        this.inlineViewHideOriginalLineNumbers = this.compactMode;\n        const optionsCopy = { ...options, ...validateDiffEditorOptions(options, diffEditorDefaultOptions) };\n        this._options = observableValue(this, optionsCopy);\n    }\n    updateOptions(changedOptions) {\n        const newDiffEditorOptions = validateDiffEditorOptions(changedOptions, this._options.get());\n        const newOptions = { ...this._options.get(), ...changedOptions, ...newDiffEditorOptions };\n        this._options.set(newOptions, undefined, { changedOptions: changedOptions });\n    }\n    setWidth(width) {\n        this._diffEditorWidth.set(width, undefined);\n    }\n    setModel(model) {\n        this._model.set(model, undefined);\n    }\n};\nDiffEditorOptions = __decorate([\n    __param(1, IAccessibilityService)\n], DiffEditorOptions);\nexport { DiffEditorOptions };\nfunction isSimpleDiff(diff, supportsTrueDiffRendering) {\n    return diff.mappings.every(m => isInsertion(m.lineRangeMapping) || isDeletion(m.lineRangeMapping) || (supportsTrueDiffRendering && allowsTrueInlineDiffRendering(m.lineRangeMapping)));\n}\nfunction isInsertion(mapping) {\n    return mapping.original.length === 0;\n}\nfunction isDeletion(mapping) {\n    return mapping.modified.length === 0;\n}\nfunction validateDiffEditorOptions(options, defaults) {\n    return {\n        enableSplitViewResizing: validateBooleanOption(options.enableSplitViewResizing, defaults.enableSplitViewResizing),\n        splitViewDefaultRatio: clampedFloat(options.splitViewDefaultRatio, 0.5, 0.1, 0.9),\n        renderSideBySide: validateBooleanOption(options.renderSideBySide, defaults.renderSideBySide),\n        renderMarginRevertIcon: validateBooleanOption(options.renderMarginRevertIcon, defaults.renderMarginRevertIcon),\n        maxComputationTime: clampedInt(options.maxComputationTime, defaults.maxComputationTime, 0, 1073741824 /* Constants.MAX_SAFE_SMALL_INTEGER */),\n        maxFileSize: clampedInt(options.maxFileSize, defaults.maxFileSize, 0, 1073741824 /* Constants.MAX_SAFE_SMALL_INTEGER */),\n        ignoreTrimWhitespace: validateBooleanOption(options.ignoreTrimWhitespace, defaults.ignoreTrimWhitespace),\n        renderIndicators: validateBooleanOption(options.renderIndicators, defaults.renderIndicators),\n        originalEditable: validateBooleanOption(options.originalEditable, defaults.originalEditable),\n        diffCodeLens: validateBooleanOption(options.diffCodeLens, defaults.diffCodeLens),\n        renderOverviewRuler: validateBooleanOption(options.renderOverviewRuler, defaults.renderOverviewRuler),\n        diffWordWrap: validateStringSetOption(options.diffWordWrap, defaults.diffWordWrap, ['off', 'on', 'inherit']),\n        diffAlgorithm: validateStringSetOption(options.diffAlgorithm, defaults.diffAlgorithm, ['legacy', 'advanced'], { 'smart': 'legacy', 'experimental': 'advanced' }),\n        accessibilityVerbose: validateBooleanOption(options.accessibilityVerbose, defaults.accessibilityVerbose),\n        experimental: {\n            showMoves: validateBooleanOption(options.experimental?.showMoves, defaults.experimental.showMoves),\n            showEmptyDecorations: validateBooleanOption(options.experimental?.showEmptyDecorations, defaults.experimental.showEmptyDecorations),\n            useTrueInlineView: validateBooleanOption(options.experimental?.useTrueInlineView, defaults.experimental.useTrueInlineView),\n        },\n        hideUnchangedRegions: {\n            enabled: validateBooleanOption(options.hideUnchangedRegions?.enabled ?? options.experimental?.collapseUnchangedRegions, defaults.hideUnchangedRegions.enabled),\n            contextLineCount: clampedInt(options.hideUnchangedRegions?.contextLineCount, defaults.hideUnchangedRegions.contextLineCount, 0, 1073741824 /* Constants.MAX_SAFE_SMALL_INTEGER */),\n            minimumLineCount: clampedInt(options.hideUnchangedRegions?.minimumLineCount, defaults.hideUnchangedRegions.minimumLineCount, 0, 1073741824 /* Constants.MAX_SAFE_SMALL_INTEGER */),\n            revealLineCount: clampedInt(options.hideUnchangedRegions?.revealLineCount, defaults.hideUnchangedRegions.revealLineCount, 0, 1073741824 /* Constants.MAX_SAFE_SMALL_INTEGER */),\n        },\n        isInEmbeddedEditor: validateBooleanOption(options.isInEmbeddedEditor, defaults.isInEmbeddedEditor),\n        onlyShowAccessibleDiffViewer: validateBooleanOption(options.onlyShowAccessibleDiffViewer, defaults.onlyShowAccessibleDiffViewer),\n        renderSideBySideInlineBreakpoint: clampedInt(options.renderSideBySideInlineBreakpoint, defaults.renderSideBySideInlineBreakpoint, 0, 1073741824 /* Constants.MAX_SAFE_SMALL_INTEGER */),\n        useInlineViewWhenSpaceIsLimited: validateBooleanOption(options.useInlineViewWhenSpaceIsLimited, defaults.useInlineViewWhenSpaceIsLimited),\n        renderGutterMenu: validateBooleanOption(options.renderGutterMenu, defaults.renderGutterMenu),\n        compactMode: validateBooleanOption(options.compactMode, defaults.compactMode),\n    };\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,IAAIA,UAAU,GAAI,IAAI,IAAI,IAAI,CAACA,UAAU,IAAK,UAAUC,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAE;EACnF,IAAIC,CAAC,GAAGC,SAAS,CAACC,MAAM;IAAEC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGH,MAAM,GAAGE,IAAI,KAAK,IAAI,GAAGA,IAAI,GAAGK,MAAM,CAACC,wBAAwB,CAACR,MAAM,EAAEC,GAAG,CAAC,GAAGC,IAAI;IAAEO,CAAC;EAC5H,IAAI,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,CAACC,QAAQ,KAAK,UAAU,EAAEL,CAAC,GAAGI,OAAO,CAACC,QAAQ,CAACZ,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,CAAC,CAAC,KAC1H,KAAK,IAAIU,CAAC,GAAGb,UAAU,CAACM,MAAM,GAAG,CAAC,EAAEO,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE,IAAIH,CAAC,GAAGV,UAAU,CAACa,CAAC,CAAC,EAAEN,CAAC,GAAG,CAACH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACH,CAAC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACT,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,GAAGG,CAAC,CAACT,MAAM,EAAEC,GAAG,CAAC,KAAKK,CAAC;EACjJ,OAAOH,CAAC,GAAG,CAAC,IAAIG,CAAC,IAAIC,MAAM,CAACM,cAAc,CAACb,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,EAAEA,CAAC;AACjE,CAAC;AACD,IAAIQ,OAAO,GAAI,IAAI,IAAI,IAAI,CAACA,OAAO,IAAK,UAAUC,UAAU,EAAEC,SAAS,EAAE;EACrE,OAAO,UAAUhB,MAAM,EAAEC,GAAG,EAAE;IAAEe,SAAS,CAAChB,MAAM,EAAEC,GAAG,EAAEc,UAAU,CAAC;EAAE,CAAC;AACzE,CAAC;AACD,SAASE,OAAO,EAAEC,mBAAmB,EAAEC,eAAe,QAAQ,uCAAuC;AACrG,SAASC,uBAAuB,QAAQ,qDAAqD;AAC7F,SAASC,6BAA6B,QAAQ,yDAAyD;AACvG,SAASC,wBAAwB,QAAQ,sCAAsC;AAC/E,SAASC,YAAY,EAAEC,UAAU,EAAEC,OAAO,IAAIC,qBAAqB,EAAEC,SAAS,IAAIC,uBAAuB,QAAQ,yCAAyC;AAC1J,SAASC,qBAAqB,QAAQ,4DAA4D;AAClG,IAAIC,iBAAiB,GAAG,MAAMA,iBAAiB,CAAC;EAC5C,IAAIC,aAAaA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACC,QAAQ;EAAE;EAC5CC,WAAWA,CAACC,OAAO,EAAEC,qBAAqB,EAAE;IACxC,IAAI,CAACA,qBAAqB,GAAGA,qBAAqB;IAClD,IAAI,CAACC,gBAAgB,GAAGjB,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC;IAChD,IAAI,CAACkB,iBAAiB,GAAGnB,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAACiB,qBAAqB,CAACG,gCAAgC,EAAE,MAAM,IAAI,CAACH,qBAAqB,CAACI,uBAAuB,CAAC,CAAC,CAAC;IAC3K,IAAI,CAACC,gCAAgC,GAAGvB,OAAO,CAAC,IAAI,EAAEwB,MAAM,IAAI,IAAI,CAACT,QAAQ,CAACU,IAAI,CAACD,MAAM,CAAC,CAACE,gBAAgB,IAAI,IAAI,CAACP,gBAAgB,CAACM,IAAI,CAACD,MAAM,CAAC,IAAI,IAAI,CAACT,QAAQ,CAACU,IAAI,CAACD,MAAM,CAAC,CAACG,gCAAgC,CAAC;IACjN,IAAI,CAACC,mBAAmB,GAAG5B,OAAO,CAAC,IAAI,EAAEwB,MAAM,IAAI,IAAI,CAACT,QAAQ,CAACU,IAAI,CAACD,MAAM,CAAC,CAACI,mBAAmB,CAAC;IAClG,IAAI,CAACF,gBAAgB,GAAG1B,OAAO,CAAC,IAAI,EAAEwB,MAAM,IAAI;MAC5C,IAAI,IAAI,CAACK,WAAW,CAACJ,IAAI,CAACD,MAAM,CAAC,EAAE;QAC/B,IAAI,IAAI,CAACM,iCAAiC,CAACL,IAAI,CAACD,MAAM,CAAC,EAAE;UACrD,OAAO,KAAK;QAChB;MACJ;MACA,OAAO,IAAI,CAACT,QAAQ,CAACU,IAAI,CAACD,MAAM,CAAC,CAACE,gBAAgB,IAC3C,EAAE,IAAI,CAACX,QAAQ,CAACU,IAAI,CAACD,MAAM,CAAC,CAACO,+BAA+B,IAAI,IAAI,CAACR,gCAAgC,CAACE,IAAI,CAACD,MAAM,CAAC,IAAI,CAAC,IAAI,CAACJ,iBAAiB,CAACK,IAAI,CAACD,MAAM,CAAC,CAAC;IACtK,CAAC,CAAC;IACF,IAAI,CAACQ,QAAQ,GAAGhC,OAAO,CAAC,IAAI,EAAEwB,MAAM,IAAI,IAAI,CAACT,QAAQ,CAACU,IAAI,CAACD,MAAM,CAAC,CAACQ,QAAQ,CAAC;IAC5E,IAAI,CAACC,2BAA2B,GAAGjC,OAAO,CAAC,IAAI,EAAEwB,MAAM,IAAI;MACvD,IAAI,CAAC,IAAI,CAACT,QAAQ,CAACU,IAAI,CAACD,MAAM,CAAC,CAACU,sBAAsB,EAAE;QACpD,OAAO,KAAK;MAChB;MACA,IAAI,CAAC,IAAI,CAACR,gBAAgB,CAACD,IAAI,CAACD,MAAM,CAAC,EAAE;QACrC,OAAO,KAAK;MAChB;MACA,IAAI,IAAI,CAACQ,QAAQ,CAACP,IAAI,CAACD,MAAM,CAAC,EAAE;QAC5B,OAAO,KAAK;MAChB;MACA,IAAI,IAAI,CAACW,sBAAsB,CAACV,IAAI,CAACD,MAAM,CAAC,EAAE;QAC1C,OAAO,KAAK;MAChB;MACA,OAAO,IAAI;IACf,CAAC,CAAC;IACF,IAAI,CAACW,sBAAsB,GAAGnC,OAAO,CAAC,IAAI,EAAEwB,MAAM,IAAI,IAAI,CAACT,QAAQ,CAACU,IAAI,CAACD,MAAM,CAAC,CAACY,gBAAgB,CAAC;IAClG,IAAI,CAACC,gBAAgB,GAAGrC,OAAO,CAAC,IAAI,EAAEwB,MAAM,IAAI,IAAI,CAACT,QAAQ,CAACU,IAAI,CAACD,MAAM,CAAC,CAACa,gBAAgB,CAAC;IAC5F,IAAI,CAACC,uBAAuB,GAAGtC,OAAO,CAAC,IAAI,EAAEwB,MAAM,IAAI,IAAI,CAACT,QAAQ,CAACU,IAAI,CAACD,MAAM,CAAC,CAACc,uBAAuB,CAAC;IAC1G,IAAI,CAACC,qBAAqB,GAAGvC,OAAO,CAAC,IAAI,EAAEwB,MAAM,IAAI,IAAI,CAACT,QAAQ,CAACU,IAAI,CAACD,MAAM,CAAC,CAACe,qBAAqB,CAAC;IACtG,IAAI,CAACC,oBAAoB,GAAGxC,OAAO,CAAC,IAAI,EAAEwB,MAAM,IAAI,IAAI,CAACT,QAAQ,CAACU,IAAI,CAACD,MAAM,CAAC,CAACgB,oBAAoB,CAAC;IACpG,IAAI,CAACC,oBAAoB,GAAGzC,OAAO,CAAC,IAAI,EAAEwB,MAAM,IAAI,IAAI,CAACT,QAAQ,CAACU,IAAI,CAACD,MAAM,CAAC,CAACkB,kBAAkB,CAAC;IAClG,IAAI,CAACC,SAAS,GAAG3C,OAAO,CAAC,IAAI,EAAEwB,MAAM,IAAI,IAAI,CAACT,QAAQ,CAACU,IAAI,CAACD,MAAM,CAAC,CAACoB,YAAY,CAACD,SAAS,IAAI,IAAI,CAACjB,gBAAgB,CAACD,IAAI,CAACD,MAAM,CAAC,CAAC;IACjI,IAAI,CAACqB,kBAAkB,GAAG7C,OAAO,CAAC,IAAI,EAAEwB,MAAM,IAAI,IAAI,CAACT,QAAQ,CAACU,IAAI,CAACD,MAAM,CAAC,CAACqB,kBAAkB,CAAC;IAChG,IAAI,CAACC,YAAY,GAAG9C,OAAO,CAAC,IAAI,EAAEwB,MAAM,IAAI,IAAI,CAACT,QAAQ,CAACU,IAAI,CAACD,MAAM,CAAC,CAACsB,YAAY,CAAC;IACpF,IAAI,CAACC,gBAAgB,GAAG/C,OAAO,CAAC,IAAI,EAAEwB,MAAM,IAAI,IAAI,CAACT,QAAQ,CAACU,IAAI,CAACD,MAAM,CAAC,CAACuB,gBAAgB,CAAC;IAC5F,IAAI,CAACC,YAAY,GAAGhD,OAAO,CAAC,IAAI,EAAEwB,MAAM,IAAI,IAAI,CAACT,QAAQ,CAACU,IAAI,CAACD,MAAM,CAAC,CAACwB,YAAY,CAAC;IACpF,IAAI,CAACC,oBAAoB,GAAGjD,OAAO,CAAC,IAAI,EAAEwB,MAAM,IAAI,IAAI,CAACT,QAAQ,CAACU,IAAI,CAACD,MAAM,CAAC,CAACyB,oBAAoB,CAAC;IACpG,IAAI,CAACC,aAAa,GAAGlD,OAAO,CAAC,IAAI,EAAEwB,MAAM,IAAI,IAAI,CAACT,QAAQ,CAACU,IAAI,CAACD,MAAM,CAAC,CAAC0B,aAAa,CAAC;IACtF,IAAI,CAACC,oBAAoB,GAAGnD,OAAO,CAAC,IAAI,EAAEwB,MAAM,IAAI,IAAI,CAACT,QAAQ,CAACU,IAAI,CAACD,MAAM,CAAC,CAACoB,YAAY,CAACO,oBAAoB,CAAC;IACjH,IAAI,CAACC,4BAA4B,GAAGpD,OAAO,CAAC,IAAI,EAAEwB,MAAM,IAAI,IAAI,CAACT,QAAQ,CAACU,IAAI,CAACD,MAAM,CAAC,CAAC4B,4BAA4B,CAAC;IACpH,IAAI,CAACvB,WAAW,GAAG7B,OAAO,CAAC,IAAI,EAAEwB,MAAM,IAAI,IAAI,CAACT,QAAQ,CAACU,IAAI,CAACD,MAAM,CAAC,CAACK,WAAW,CAAC;IAClF,IAAI,CAACwB,8BAA8B,GAAGrD,OAAO,CAAC,IAAI,EAAEwB,MAAM,IAAI,IAAI,CAACT,QAAQ,CAACU,IAAI,CAACD,MAAM,CAAC,CAACoB,YAAY,CAACU,iBAAiB,CAAC;IACxH,IAAI,CAACC,0BAA0B,GAAGvD,OAAO,CAAC,IAAI,EAAEwB,MAAM,IAAI,CAAC,IAAI,CAACE,gBAAgB,CAACD,IAAI,CAACD,MAAM,CAAC,IAAI,IAAI,CAAC6B,8BAA8B,CAAC5B,IAAI,CAACD,MAAM,CAAC,CAAC;IAClJ,IAAI,CAACgC,oBAAoB,GAAGxD,OAAO,CAAC,IAAI,EAAEwB,MAAM,IAAI,IAAI,CAACT,QAAQ,CAACU,IAAI,CAACD,MAAM,CAAC,CAACgC,oBAAoB,CAACC,OAAO,CAAC;IAC5G,IAAI,CAACC,mCAAmC,GAAG1D,OAAO,CAAC,IAAI,EAAEwB,MAAM,IAAI,IAAI,CAACT,QAAQ,CAACU,IAAI,CAACD,MAAM,CAAC,CAACgC,oBAAoB,CAACG,eAAe,CAAC;IACnI,IAAI,CAACC,oCAAoC,GAAG5D,OAAO,CAAC,IAAI,EAAEwB,MAAM,IAAI,IAAI,CAACT,QAAQ,CAACU,IAAI,CAACD,MAAM,CAAC,CAACgC,oBAAoB,CAACK,gBAAgB,CAAC;IACrI,IAAI,CAACC,oCAAoC,GAAG9D,OAAO,CAAC,IAAI,EAAEwB,MAAM,IAAI,IAAI,CAACT,QAAQ,CAACU,IAAI,CAACD,MAAM,CAAC,CAACgC,oBAAoB,CAACO,gBAAgB,CAAC;IACrI,IAAI,CAACC,MAAM,GAAG9D,eAAe,CAAC,IAAI,EAAE+D,SAAS,CAAC;IAC9C,IAAI,CAACnC,iCAAiC,GAAG,IAAI,CAACkC,MAAM,CAC/CE,GAAG,CAAC,IAAI,EAAEC,KAAK,IAAIhE,uBAAuB,CAAC,IAAI,EAAEqB,MAAM,IAAI;MAC5D,MAAM4C,KAAK,GAAGD,KAAK,EAAEE,IAAI,CAAC5C,IAAI,CAACD,MAAM,CAAC;MACtC,OAAO4C,KAAK,GAAGE,YAAY,CAACF,KAAK,EAAE,IAAI,CAACf,8BAA8B,CAAC5B,IAAI,CAACD,MAAM,CAAC,CAAC,GAAGyC,SAAS;IACpG,CAAC,CAAC,CAAC,CACEM,OAAO,CAAC,CAAC,CACTL,GAAG,CAAC,IAAI,EAAEM,CAAC,IAAI,CAAC,CAACA,CAAC,CAAC;IACxB,IAAI,CAACC,iCAAiC,GAAG,IAAI,CAAC5C,WAAW;IACzD,MAAM6C,WAAW,GAAG;MAAE,GAAGzD,OAAO;MAAE,GAAG0D,yBAAyB,CAAC1D,OAAO,EAAEZ,wBAAwB;IAAE,CAAC;IACnG,IAAI,CAACU,QAAQ,GAAGb,eAAe,CAAC,IAAI,EAAEwE,WAAW,CAAC;EACtD;EACAE,aAAaA,CAACC,cAAc,EAAE;IAC1B,MAAMC,oBAAoB,GAAGH,yBAAyB,CAACE,cAAc,EAAE,IAAI,CAAC9D,QAAQ,CAACgE,GAAG,CAAC,CAAC,CAAC;IAC3F,MAAMC,UAAU,GAAG;MAAE,GAAG,IAAI,CAACjE,QAAQ,CAACgE,GAAG,CAAC,CAAC;MAAE,GAAGF,cAAc;MAAE,GAAGC;IAAqB,CAAC;IACzF,IAAI,CAAC/D,QAAQ,CAACkE,GAAG,CAACD,UAAU,EAAEf,SAAS,EAAE;MAAEY,cAAc,EAAEA;IAAe,CAAC,CAAC;EAChF;EACAK,QAAQA,CAACC,KAAK,EAAE;IACZ,IAAI,CAAChE,gBAAgB,CAAC8D,GAAG,CAACE,KAAK,EAAElB,SAAS,CAAC;EAC/C;EACAmB,QAAQA,CAACjB,KAAK,EAAE;IACZ,IAAI,CAACH,MAAM,CAACiB,GAAG,CAACd,KAAK,EAAEF,SAAS,CAAC;EACrC;AACJ,CAAC;AACDpD,iBAAiB,GAAGhC,UAAU,CAAC,CAC3BgB,OAAO,CAAC,CAAC,EAAEe,qBAAqB,CAAC,CACpC,EAAEC,iBAAiB,CAAC;AACrB,SAASA,iBAAiB;AAC1B,SAASyD,YAAYA,CAACD,IAAI,EAAEgB,yBAAyB,EAAE;EACnD,OAAOhB,IAAI,CAACiB,QAAQ,CAACC,KAAK,CAACC,CAAC,IAAIC,WAAW,CAACD,CAAC,CAACE,gBAAgB,CAAC,IAAIC,UAAU,CAACH,CAAC,CAACE,gBAAgB,CAAC,IAAKL,yBAAyB,IAAIjF,6BAA6B,CAACoF,CAAC,CAACE,gBAAgB,CAAE,CAAC;AAC1L;AACA,SAASD,WAAWA,CAACG,OAAO,EAAE;EAC1B,OAAOA,OAAO,CAACC,QAAQ,CAACzG,MAAM,KAAK,CAAC;AACxC;AACA,SAASuG,UAAUA,CAACC,OAAO,EAAE;EACzB,OAAOA,OAAO,CAACE,QAAQ,CAAC1G,MAAM,KAAK,CAAC;AACxC;AACA,SAASuF,yBAAyBA,CAAC1D,OAAO,EAAE8E,QAAQ,EAAE;EAClD,OAAO;IACHzD,uBAAuB,EAAE7B,qBAAqB,CAACQ,OAAO,CAACqB,uBAAuB,EAAEyD,QAAQ,CAACzD,uBAAuB,CAAC;IACjHC,qBAAqB,EAAEjC,YAAY,CAACW,OAAO,CAACsB,qBAAqB,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IACjFb,gBAAgB,EAAEjB,qBAAqB,CAACQ,OAAO,CAACS,gBAAgB,EAAEqE,QAAQ,CAACrE,gBAAgB,CAAC;IAC5FQ,sBAAsB,EAAEzB,qBAAqB,CAACQ,OAAO,CAACiB,sBAAsB,EAAE6D,QAAQ,CAAC7D,sBAAsB,CAAC;IAC9GQ,kBAAkB,EAAEnC,UAAU,CAACU,OAAO,CAACyB,kBAAkB,EAAEqD,QAAQ,CAACrD,kBAAkB,EAAE,CAAC,EAAE,UAAU,CAAC,sCAAsC,CAAC;IAC7IsD,WAAW,EAAEzF,UAAU,CAACU,OAAO,CAAC+E,WAAW,EAAED,QAAQ,CAACC,WAAW,EAAE,CAAC,EAAE,UAAU,CAAC,sCAAsC,CAAC;IACxHxD,oBAAoB,EAAE/B,qBAAqB,CAACQ,OAAO,CAACuB,oBAAoB,EAAEuD,QAAQ,CAACvD,oBAAoB,CAAC;IACxGH,gBAAgB,EAAE5B,qBAAqB,CAACQ,OAAO,CAACoB,gBAAgB,EAAE0D,QAAQ,CAAC1D,gBAAgB,CAAC;IAC5FU,gBAAgB,EAAEtC,qBAAqB,CAACQ,OAAO,CAAC8B,gBAAgB,EAAEgD,QAAQ,CAAChD,gBAAgB,CAAC;IAC5FC,YAAY,EAAEvC,qBAAqB,CAACQ,OAAO,CAAC+B,YAAY,EAAE+C,QAAQ,CAAC/C,YAAY,CAAC;IAChFpB,mBAAmB,EAAEnB,qBAAqB,CAACQ,OAAO,CAACW,mBAAmB,EAAEmE,QAAQ,CAACnE,mBAAmB,CAAC;IACrGkB,YAAY,EAAEnC,uBAAuB,CAACM,OAAO,CAAC6B,YAAY,EAAEiD,QAAQ,CAACjD,YAAY,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;IAC5GI,aAAa,EAAEvC,uBAAuB,CAACM,OAAO,CAACiC,aAAa,EAAE6C,QAAQ,CAAC7C,aAAa,EAAE,CAAC,QAAQ,EAAE,UAAU,CAAC,EAAE;MAAE,OAAO,EAAE,QAAQ;MAAE,cAAc,EAAE;IAAW,CAAC,CAAC;IAChKD,oBAAoB,EAAExC,qBAAqB,CAACQ,OAAO,CAACgC,oBAAoB,EAAE8C,QAAQ,CAAC9C,oBAAoB,CAAC;IACxGL,YAAY,EAAE;MACVD,SAAS,EAAElC,qBAAqB,CAACQ,OAAO,CAAC2B,YAAY,EAAED,SAAS,EAAEoD,QAAQ,CAACnD,YAAY,CAACD,SAAS,CAAC;MAClGQ,oBAAoB,EAAE1C,qBAAqB,CAACQ,OAAO,CAAC2B,YAAY,EAAEO,oBAAoB,EAAE4C,QAAQ,CAACnD,YAAY,CAACO,oBAAoB,CAAC;MACnIG,iBAAiB,EAAE7C,qBAAqB,CAACQ,OAAO,CAAC2B,YAAY,EAAEU,iBAAiB,EAAEyC,QAAQ,CAACnD,YAAY,CAACU,iBAAiB;IAC7H,CAAC;IACDE,oBAAoB,EAAE;MAClBC,OAAO,EAAEhD,qBAAqB,CAACQ,OAAO,CAACuC,oBAAoB,EAAEC,OAAO,IAAIxC,OAAO,CAAC2B,YAAY,EAAEqD,wBAAwB,EAAEF,QAAQ,CAACvC,oBAAoB,CAACC,OAAO,CAAC;MAC9JI,gBAAgB,EAAEtD,UAAU,CAACU,OAAO,CAACuC,oBAAoB,EAAEK,gBAAgB,EAAEkC,QAAQ,CAACvC,oBAAoB,CAACK,gBAAgB,EAAE,CAAC,EAAE,UAAU,CAAC,sCAAsC,CAAC;MAClLE,gBAAgB,EAAExD,UAAU,CAACU,OAAO,CAACuC,oBAAoB,EAAEO,gBAAgB,EAAEgC,QAAQ,CAACvC,oBAAoB,CAACO,gBAAgB,EAAE,CAAC,EAAE,UAAU,CAAC,sCAAsC,CAAC;MAClLJ,eAAe,EAAEpD,UAAU,CAACU,OAAO,CAACuC,oBAAoB,EAAEG,eAAe,EAAEoC,QAAQ,CAACvC,oBAAoB,CAACG,eAAe,EAAE,CAAC,EAAE,UAAU,CAAC,sCAAsC;IAClL,CAAC;IACDd,kBAAkB,EAAEpC,qBAAqB,CAACQ,OAAO,CAAC4B,kBAAkB,EAAEkD,QAAQ,CAAClD,kBAAkB,CAAC;IAClGO,4BAA4B,EAAE3C,qBAAqB,CAACQ,OAAO,CAACmC,4BAA4B,EAAE2C,QAAQ,CAAC3C,4BAA4B,CAAC;IAChIzB,gCAAgC,EAAEpB,UAAU,CAACU,OAAO,CAACU,gCAAgC,EAAEoE,QAAQ,CAACpE,gCAAgC,EAAE,CAAC,EAAE,UAAU,CAAC,sCAAsC,CAAC;IACvLI,+BAA+B,EAAEtB,qBAAqB,CAACQ,OAAO,CAACc,+BAA+B,EAAEgE,QAAQ,CAAChE,+BAA+B,CAAC;IACzIK,gBAAgB,EAAE3B,qBAAqB,CAACQ,OAAO,CAACmB,gBAAgB,EAAE2D,QAAQ,CAAC3D,gBAAgB,CAAC;IAC5FP,WAAW,EAAEpB,qBAAqB,CAACQ,OAAO,CAACY,WAAW,EAAEkE,QAAQ,CAAClE,WAAW;EAChF,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}