{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as dom from '../../dom.js';\nimport { StandardKeyboardEvent } from '../../keyboardEvent.js';\nimport { DomScrollableElement } from '../scrollbar/scrollableElement.js';\nimport { Disposable } from '../../../common/lifecycle.js';\nimport './hoverWidget.css';\nimport { localize } from '../../../../nls.js';\nconst $ = dom.$;\nexport class HoverWidget extends Disposable {\n  constructor() {\n    super();\n    this.containerDomNode = document.createElement('div');\n    this.containerDomNode.className = 'monaco-hover';\n    this.containerDomNode.tabIndex = 0;\n    this.containerDomNode.setAttribute('role', 'tooltip');\n    this.contentsDomNode = document.createElement('div');\n    this.contentsDomNode.className = 'monaco-hover-content';\n    this.scrollbar = this._register(new DomScrollableElement(this.contentsDomNode, {\n      consumeMouseWheelIfScrollbarIsNeeded: true\n    }));\n    this.containerDomNode.appendChild(this.scrollbar.getDomNode());\n  }\n  onContentsChanged() {\n    this.scrollbar.scanDomNode();\n  }\n}\nexport class HoverAction extends Disposable {\n  static render(parent, actionOptions, keybindingLabel) {\n    return new HoverAction(parent, actionOptions, keybindingLabel);\n  }\n  constructor(parent, actionOptions, keybindingLabel) {\n    super();\n    this.actionLabel = actionOptions.label;\n    this.actionKeybindingLabel = keybindingLabel;\n    this.actionContainer = dom.append(parent, $('div.action-container'));\n    this.actionContainer.setAttribute('tabindex', '0');\n    this.action = dom.append(this.actionContainer, $('a.action'));\n    this.action.setAttribute('role', 'button');\n    if (actionOptions.iconClass) {\n      dom.append(this.action, $(`span.icon.${actionOptions.iconClass}`));\n    }\n    const label = dom.append(this.action, $('span'));\n    label.textContent = keybindingLabel ? `${actionOptions.label} (${keybindingLabel})` : actionOptions.label;\n    this._store.add(new ClickAction(this.actionContainer, actionOptions.run));\n    this._store.add(new KeyDownAction(this.actionContainer, actionOptions.run, [3 /* KeyCode.Enter */, 10 /* KeyCode.Space */]));\n    this.setEnabled(true);\n  }\n  setEnabled(enabled) {\n    if (enabled) {\n      this.actionContainer.classList.remove('disabled');\n      this.actionContainer.removeAttribute('aria-disabled');\n    } else {\n      this.actionContainer.classList.add('disabled');\n      this.actionContainer.setAttribute('aria-disabled', 'true');\n    }\n  }\n}\nexport function getHoverAccessibleViewHint(shouldHaveHint, keybinding) {\n  return shouldHaveHint && keybinding ? localize('acessibleViewHint', \"Inspect this in the accessible view with {0}.\", keybinding) : shouldHaveHint ? localize('acessibleViewHintNoKbOpen', \"Inspect this in the accessible view via the command Open Accessible View which is currently not triggerable via keybinding.\") : '';\n}\nexport class ClickAction extends Disposable {\n  constructor(container, run) {\n    super();\n    this._register(dom.addDisposableListener(container, dom.EventType.CLICK, e => {\n      e.stopPropagation();\n      e.preventDefault();\n      run(container);\n    }));\n  }\n}\nexport class KeyDownAction extends Disposable {\n  constructor(container, run, keyCodes) {\n    super();\n    this._register(dom.addDisposableListener(container, dom.EventType.KEY_DOWN, e => {\n      const event = new StandardKeyboardEvent(e);\n      if (keyCodes.some(keyCode => event.equals(keyCode))) {\n        e.stopPropagation();\n        e.preventDefault();\n        run(container);\n      }\n    }));\n  }\n}", "map": {"version": 3, "names": ["dom", "StandardKeyboardEvent", "DomScrollableElement", "Disposable", "localize", "$", "HoverWidget", "constructor", "containerDomNode", "document", "createElement", "className", "tabIndex", "setAttribute", "contentsDomNode", "scrollbar", "_register", "consumeMouseWheelIfScrollbarIsNeeded", "append<PERSON><PERSON><PERSON>", "getDomNode", "onContentsChanged", "scanDomNode", "HoverAction", "render", "parent", "actionOptions", "keybinding<PERSON>abel", "actionLabel", "label", "actionKeybindingLabel", "actionContainer", "append", "action", "iconClass", "textContent", "_store", "add", "ClickAction", "run", "KeyDownAction", "setEnabled", "enabled", "classList", "remove", "removeAttribute", "getHoverAccessibleViewHint", "shouldHaveHint", "keybinding", "container", "addDisposableListener", "EventType", "CLICK", "e", "stopPropagation", "preventDefault", "keyCodes", "KEY_DOWN", "event", "some", "keyCode", "equals"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/browser/ui/hover/hoverWidget.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as dom from '../../dom.js';\nimport { StandardKeyboardEvent } from '../../keyboardEvent.js';\nimport { DomScrollableElement } from '../scrollbar/scrollableElement.js';\nimport { Disposable } from '../../../common/lifecycle.js';\nimport './hoverWidget.css';\nimport { localize } from '../../../../nls.js';\nconst $ = dom.$;\nexport class HoverWidget extends Disposable {\n    constructor() {\n        super();\n        this.containerDomNode = document.createElement('div');\n        this.containerDomNode.className = 'monaco-hover';\n        this.containerDomNode.tabIndex = 0;\n        this.containerDomNode.setAttribute('role', 'tooltip');\n        this.contentsDomNode = document.createElement('div');\n        this.contentsDomNode.className = 'monaco-hover-content';\n        this.scrollbar = this._register(new DomScrollableElement(this.contentsDomNode, {\n            consumeMouseWheelIfScrollbarIsNeeded: true\n        }));\n        this.containerDomNode.appendChild(this.scrollbar.getDomNode());\n    }\n    onContentsChanged() {\n        this.scrollbar.scanDomNode();\n    }\n}\nexport class HoverAction extends Disposable {\n    static render(parent, actionOptions, keybindingLabel) {\n        return new HoverAction(parent, actionOptions, keybindingLabel);\n    }\n    constructor(parent, actionOptions, keybindingLabel) {\n        super();\n        this.actionLabel = actionOptions.label;\n        this.actionKeybindingLabel = keybindingLabel;\n        this.actionContainer = dom.append(parent, $('div.action-container'));\n        this.actionContainer.setAttribute('tabindex', '0');\n        this.action = dom.append(this.actionContainer, $('a.action'));\n        this.action.setAttribute('role', 'button');\n        if (actionOptions.iconClass) {\n            dom.append(this.action, $(`span.icon.${actionOptions.iconClass}`));\n        }\n        const label = dom.append(this.action, $('span'));\n        label.textContent = keybindingLabel ? `${actionOptions.label} (${keybindingLabel})` : actionOptions.label;\n        this._store.add(new ClickAction(this.actionContainer, actionOptions.run));\n        this._store.add(new KeyDownAction(this.actionContainer, actionOptions.run, [3 /* KeyCode.Enter */, 10 /* KeyCode.Space */]));\n        this.setEnabled(true);\n    }\n    setEnabled(enabled) {\n        if (enabled) {\n            this.actionContainer.classList.remove('disabled');\n            this.actionContainer.removeAttribute('aria-disabled');\n        }\n        else {\n            this.actionContainer.classList.add('disabled');\n            this.actionContainer.setAttribute('aria-disabled', 'true');\n        }\n    }\n}\nexport function getHoverAccessibleViewHint(shouldHaveHint, keybinding) {\n    return shouldHaveHint && keybinding ? localize('acessibleViewHint', \"Inspect this in the accessible view with {0}.\", keybinding) : shouldHaveHint ? localize('acessibleViewHintNoKbOpen', \"Inspect this in the accessible view via the command Open Accessible View which is currently not triggerable via keybinding.\") : '';\n}\nexport class ClickAction extends Disposable {\n    constructor(container, run) {\n        super();\n        this._register(dom.addDisposableListener(container, dom.EventType.CLICK, e => {\n            e.stopPropagation();\n            e.preventDefault();\n            run(container);\n        }));\n    }\n}\nexport class KeyDownAction extends Disposable {\n    constructor(container, run, keyCodes) {\n        super();\n        this._register(dom.addDisposableListener(container, dom.EventType.KEY_DOWN, e => {\n            const event = new StandardKeyboardEvent(e);\n            if (keyCodes.some(keyCode => event.equals(keyCode))) {\n                e.stopPropagation();\n                e.preventDefault();\n                run(container);\n            }\n        }));\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,KAAKA,GAAG,MAAM,cAAc;AACnC,SAASC,qBAAqB,QAAQ,wBAAwB;AAC9D,SAASC,oBAAoB,QAAQ,mCAAmC;AACxE,SAASC,UAAU,QAAQ,8BAA8B;AACzD,OAAO,mBAAmB;AAC1B,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,MAAMC,CAAC,GAAGL,GAAG,CAACK,CAAC;AACf,OAAO,MAAMC,WAAW,SAASH,UAAU,CAAC;EACxCI,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,gBAAgB,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IACrD,IAAI,CAACF,gBAAgB,CAACG,SAAS,GAAG,cAAc;IAChD,IAAI,CAACH,gBAAgB,CAACI,QAAQ,GAAG,CAAC;IAClC,IAAI,CAACJ,gBAAgB,CAACK,YAAY,CAAC,MAAM,EAAE,SAAS,CAAC;IACrD,IAAI,CAACC,eAAe,GAAGL,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IACpD,IAAI,CAACI,eAAe,CAACH,SAAS,GAAG,sBAAsB;IACvD,IAAI,CAACI,SAAS,GAAG,IAAI,CAACC,SAAS,CAAC,IAAId,oBAAoB,CAAC,IAAI,CAACY,eAAe,EAAE;MAC3EG,oCAAoC,EAAE;IAC1C,CAAC,CAAC,CAAC;IACH,IAAI,CAACT,gBAAgB,CAACU,WAAW,CAAC,IAAI,CAACH,SAAS,CAACI,UAAU,CAAC,CAAC,CAAC;EAClE;EACAC,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACL,SAAS,CAACM,WAAW,CAAC,CAAC;EAChC;AACJ;AACA,OAAO,MAAMC,WAAW,SAASnB,UAAU,CAAC;EACxC,OAAOoB,MAAMA,CAACC,MAAM,EAAEC,aAAa,EAAEC,eAAe,EAAE;IAClD,OAAO,IAAIJ,WAAW,CAACE,MAAM,EAAEC,aAAa,EAAEC,eAAe,CAAC;EAClE;EACAnB,WAAWA,CAACiB,MAAM,EAAEC,aAAa,EAAEC,eAAe,EAAE;IAChD,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,WAAW,GAAGF,aAAa,CAACG,KAAK;IACtC,IAAI,CAACC,qBAAqB,GAAGH,eAAe;IAC5C,IAAI,CAACI,eAAe,GAAG9B,GAAG,CAAC+B,MAAM,CAACP,MAAM,EAAEnB,CAAC,CAAC,sBAAsB,CAAC,CAAC;IACpE,IAAI,CAACyB,eAAe,CAACjB,YAAY,CAAC,UAAU,EAAE,GAAG,CAAC;IAClD,IAAI,CAACmB,MAAM,GAAGhC,GAAG,CAAC+B,MAAM,CAAC,IAAI,CAACD,eAAe,EAAEzB,CAAC,CAAC,UAAU,CAAC,CAAC;IAC7D,IAAI,CAAC2B,MAAM,CAACnB,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC;IAC1C,IAAIY,aAAa,CAACQ,SAAS,EAAE;MACzBjC,GAAG,CAAC+B,MAAM,CAAC,IAAI,CAACC,MAAM,EAAE3B,CAAC,CAAC,aAAaoB,aAAa,CAACQ,SAAS,EAAE,CAAC,CAAC;IACtE;IACA,MAAML,KAAK,GAAG5B,GAAG,CAAC+B,MAAM,CAAC,IAAI,CAACC,MAAM,EAAE3B,CAAC,CAAC,MAAM,CAAC,CAAC;IAChDuB,KAAK,CAACM,WAAW,GAAGR,eAAe,GAAG,GAAGD,aAAa,CAACG,KAAK,KAAKF,eAAe,GAAG,GAAGD,aAAa,CAACG,KAAK;IACzG,IAAI,CAACO,MAAM,CAACC,GAAG,CAAC,IAAIC,WAAW,CAAC,IAAI,CAACP,eAAe,EAAEL,aAAa,CAACa,GAAG,CAAC,CAAC;IACzE,IAAI,CAACH,MAAM,CAACC,GAAG,CAAC,IAAIG,aAAa,CAAC,IAAI,CAACT,eAAe,EAAEL,aAAa,CAACa,GAAG,EAAE,CAAC,CAAC,CAAC,qBAAqB,EAAE,CAAC,oBAAoB,CAAC,CAAC;IAC5H,IAAI,CAACE,UAAU,CAAC,IAAI,CAAC;EACzB;EACAA,UAAUA,CAACC,OAAO,EAAE;IAChB,IAAIA,OAAO,EAAE;MACT,IAAI,CAACX,eAAe,CAACY,SAAS,CAACC,MAAM,CAAC,UAAU,CAAC;MACjD,IAAI,CAACb,eAAe,CAACc,eAAe,CAAC,eAAe,CAAC;IACzD,CAAC,MACI;MACD,IAAI,CAACd,eAAe,CAACY,SAAS,CAACN,GAAG,CAAC,UAAU,CAAC;MAC9C,IAAI,CAACN,eAAe,CAACjB,YAAY,CAAC,eAAe,EAAE,MAAM,CAAC;IAC9D;EACJ;AACJ;AACA,OAAO,SAASgC,0BAA0BA,CAACC,cAAc,EAAEC,UAAU,EAAE;EACnE,OAAOD,cAAc,IAAIC,UAAU,GAAG3C,QAAQ,CAAC,mBAAmB,EAAE,+CAA+C,EAAE2C,UAAU,CAAC,GAAGD,cAAc,GAAG1C,QAAQ,CAAC,2BAA2B,EAAE,6HAA6H,CAAC,GAAG,EAAE;AACjU;AACA,OAAO,MAAMiC,WAAW,SAASlC,UAAU,CAAC;EACxCI,WAAWA,CAACyC,SAAS,EAAEV,GAAG,EAAE;IACxB,KAAK,CAAC,CAAC;IACP,IAAI,CAACtB,SAAS,CAAChB,GAAG,CAACiD,qBAAqB,CAACD,SAAS,EAAEhD,GAAG,CAACkD,SAAS,CAACC,KAAK,EAAEC,CAAC,IAAI;MAC1EA,CAAC,CAACC,eAAe,CAAC,CAAC;MACnBD,CAAC,CAACE,cAAc,CAAC,CAAC;MAClBhB,GAAG,CAACU,SAAS,CAAC;IAClB,CAAC,CAAC,CAAC;EACP;AACJ;AACA,OAAO,MAAMT,aAAa,SAASpC,UAAU,CAAC;EAC1CI,WAAWA,CAACyC,SAAS,EAAEV,GAAG,EAAEiB,QAAQ,EAAE;IAClC,KAAK,CAAC,CAAC;IACP,IAAI,CAACvC,SAAS,CAAChB,GAAG,CAACiD,qBAAqB,CAACD,SAAS,EAAEhD,GAAG,CAACkD,SAAS,CAACM,QAAQ,EAAEJ,CAAC,IAAI;MAC7E,MAAMK,KAAK,GAAG,IAAIxD,qBAAqB,CAACmD,CAAC,CAAC;MAC1C,IAAIG,QAAQ,CAACG,IAAI,CAACC,OAAO,IAAIF,KAAK,CAACG,MAAM,CAACD,OAAO,CAAC,CAAC,EAAE;QACjDP,CAAC,CAACC,eAAe,CAAC,CAAC;QACnBD,CAAC,CAACE,cAAc,CAAC,CAAC;QAClBhB,GAAG,CAACU,SAAS,CAAC;MAClB;IACJ,CAAC,CAAC,CAAC;EACP;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}