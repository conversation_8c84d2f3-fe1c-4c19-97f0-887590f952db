{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { EventEmitter } from '@angular/core';\nimport { IconComponent } from '@ava/play-comp-library';\nimport { AgentStepperCardComponent } from '../agent-stepper-card/agent-stepper-card.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nfunction WorkflowPlaygroundComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"div\", 5)(2, \"button\", 6);\n    i0.ɵɵlistener(\"click\", function WorkflowPlaygroundComponent_div_1_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onBackClick());\n    });\n    i0.ɵɵelement(3, \"ava-icon\", 7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"button\", 8);\n    i0.ɵɵlistener(\"click\", function WorkflowPlaygroundComponent_div_1_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onCollapseToggle());\n    });\n    i0.ɵɵelement(5, \"ava-icon\", 9);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"iconSize\", 20);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"title\", \"Collapse Panel\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"iconSize\", 20);\n  }\n}\nfunction WorkflowPlaygroundComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"button\", 11);\n    i0.ɵɵlistener(\"click\", function WorkflowPlaygroundComponent_div_2_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onCollapseToggle());\n    });\n    i0.ɵɵelement(2, \"ava-icon\", 12);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"iconSize\", 20);\n  }\n}\nfunction WorkflowPlaygroundComponent_div_3_app_agent_stepper_card_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-agent-stepper-card\", 16);\n    i0.ɵɵlistener(\"inputChanged\", function WorkflowPlaygroundComponent_div_3_app_agent_stepper_card_2_Template_app_agent_stepper_card_inputChanged_0_listener($event) {\n      const agent_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onAgentInputChange(agent_r5.id, $event.inputIndex, $event.value));\n    })(\"fileSelected\", function WorkflowPlaygroundComponent_div_3_app_agent_stepper_card_2_Template_app_agent_stepper_card_fileSelected_0_listener($event) {\n      const agent_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onAgentFileSelect(agent_r5.id, $event.inputIndex, $event.files));\n    })(\"messageSent\", function WorkflowPlaygroundComponent_div_3_app_agent_stepper_card_2_Template_app_agent_stepper_card_messageSent_0_listener($event) {\n      const i_r6 = i0.ɵɵrestoreView(_r4).index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onMessageSent(i_r6, $event));\n    })(\"stepCompleted\", function WorkflowPlaygroundComponent_div_3_app_agent_stepper_card_2_Template_app_agent_stepper_card_stepCompleted_0_listener() {\n      const i_r6 = i0.ɵɵrestoreView(_r4).index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onStepCompleted(i_r6));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const agent_r5 = ctx.$implicit;\n    const i_r6 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"agent\", agent_r5)(\"stepNumber\", i_r6 + 1)(\"isFirst\", i_r6 === 0)(\"isLast\", i_r6 === ctx_r1.agents.length - 1)(\"isActive\", ctx_r1.isStepActive(i_r6))(\"isCompleted\", ctx_r1.isStepCompleted(i_r6));\n  }\n}\nfunction WorkflowPlaygroundComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"div\", 14);\n    i0.ɵɵtemplate(2, WorkflowPlaygroundComponent_div_3_app_agent_stepper_card_2_Template, 1, 6, \"app-agent-stepper-card\", 15);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.agents)(\"ngForTrackBy\", ctx_r1.trackByAgentId);\n  }\n}\nexport let WorkflowPlaygroundComponent = /*#__PURE__*/(() => {\n  class WorkflowPlaygroundComponent {\n    agents = [];\n    isCollapsed = false;\n    workflowName = 'Workflow';\n    backClicked = new EventEmitter();\n    collapseToggled = new EventEmitter();\n    agentInputChanged = new EventEmitter();\n    agentFileSelected = new EventEmitter();\n    messageSent = new EventEmitter();\n    currentActiveStep = 0;\n    completedSteps = new Set();\n    onBackClick() {\n      this.backClicked.emit();\n    }\n    onCollapseToggle() {\n      this.isCollapsed = !this.isCollapsed;\n      this.collapseToggled.emit(this.isCollapsed);\n    }\n    onAgentInputChange(agentId, inputIndex, value) {\n      this.agentInputChanged.emit({\n        agentId,\n        inputIndex,\n        value\n      });\n    }\n    onAgentFileSelect(agentId, inputIndex, files) {\n      this.agentFileSelected.emit({\n        agentId,\n        inputIndex,\n        files\n      });\n    }\n    onMessageSent(agentIndex, event) {\n      const agent = this.agents[agentIndex];\n      if (agent) {\n        this.messageSent.emit({\n          agentId: agent.id,\n          inputIndex: event.inputIndex,\n          value: event.value,\n          files: event.files\n        });\n      }\n    }\n    onStepCompleted(agentIndex) {\n      this.completedSteps.add(agentIndex);\n      // Move to next step if available\n      if (agentIndex === this.currentActiveStep && agentIndex < this.agents.length - 1) {\n        this.currentActiveStep = agentIndex + 1;\n      }\n    }\n    isStepActive(index) {\n      return index === this.currentActiveStep;\n    }\n    isStepCompleted(index) {\n      return this.completedSteps.has(index);\n    }\n    trackByAgentId(index, agent) {\n      return agent.id;\n    }\n    static ɵfac = function WorkflowPlaygroundComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || WorkflowPlaygroundComponent)();\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: WorkflowPlaygroundComponent,\n      selectors: [[\"app-workflow-playground\"]],\n      inputs: {\n        agents: \"agents\",\n        isCollapsed: \"isCollapsed\",\n        workflowName: \"workflowName\"\n      },\n      outputs: {\n        backClicked: \"backClicked\",\n        collapseToggled: \"collapseToggled\",\n        agentInputChanged: \"agentInputChanged\",\n        agentFileSelected: \"agentFileSelected\",\n        messageSent: \"messageSent\"\n      },\n      decls: 4,\n      vars: 5,\n      consts: [[1, \"workflow-playground-container\"], [\"class\", \"playground-header\", 4, \"ngIf\"], [\"class\", \"collapsed-header\", 4, \"ngIf\"], [\"class\", \"playground-content\", 4, \"ngIf\"], [1, \"playground-header\"], [1, \"header-left\"], [\"title\", \"Go Back\", 1, \"back-btn\", 3, \"click\"], [\"iconName\", \"arrow-left\", \"iconColor\", \"var(--color-brand-primary)\", 3, \"iconSize\"], [1, \"collapse-btn\", 3, \"click\", \"title\"], [\"iconName\", \"panel-left\", \"iconColor\", \"var(--color-brand-primary)\", 3, \"iconSize\"], [1, \"collapsed-header\"], [\"title\", \"Expand Panel\", 1, \"expand-btn\", 3, \"click\"], [\"iconName\", \"panel-right\", \"iconColor\", \"var(--color-brand-primary)\", 3, \"iconSize\"], [1, \"playground-content\"], [1, \"agents-container\"], [3, \"agent\", \"stepNumber\", \"isFirst\", \"isLast\", \"isActive\", \"isCompleted\", \"inputChanged\", \"fileSelected\", \"messageSent\", \"stepCompleted\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [3, \"inputChanged\", \"fileSelected\", \"messageSent\", \"stepCompleted\", \"agent\", \"stepNumber\", \"isFirst\", \"isLast\", \"isActive\", \"isCompleted\"]],\n      template: function WorkflowPlaygroundComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, WorkflowPlaygroundComponent_div_1_Template, 6, 3, \"div\", 1)(2, WorkflowPlaygroundComponent_div_2_Template, 3, 1, \"div\", 2)(3, WorkflowPlaygroundComponent_div_3_Template, 3, 2, \"div\", 3);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"collapsed\", ctx.isCollapsed);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isCollapsed);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isCollapsed);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isCollapsed);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, i1.NgIf, IconComponent, AgentStepperCardComponent],\n      styles: [\".workflow-playground-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%;\\n  background: var(--card-bg);\\n  border-radius: 8px;\\n  box-shadow: 0 4px 15px var(--card-shadow, rgba(0, 0, 0, 0.05));\\n  transition: all 0.3s ease;\\n  overflow: hidden;\\n}\\n.workflow-playground-container.collapsed[_ngcontent-%COMP%] {\\n  width: 60px;\\n  min-width: 60px;\\n}\\n\\n.playground-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 16px 20px;\\n  background: #E6F3FF;\\n  color: #000000;\\n  min-height: 64px;\\n  box-sizing: border-box;\\n}\\n.playground-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  flex: 1;\\n  min-width: 0;\\n}\\n.playground-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 36px;\\n  height: 36px;\\n  border: none;\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 6px;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  flex-shrink: 0;\\n}\\n.playground-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.2);\\n  transform: translateX(-2px);\\n}\\n.playground-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%]:active {\\n  transform: translateX(-1px);\\n}\\n.playground-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .workflow-title[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: white;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  min-width: 0;\\n}\\n.playground-header[_ngcontent-%COMP%]   .collapse-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 36px;\\n  height: 36px;\\n  border: none;\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 6px;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  flex-shrink: 0;\\n}\\n.playground-header[_ngcontent-%COMP%]   .collapse-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.2);\\n}\\n\\n.collapsed-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  padding: 16px 8px;\\n  background: #E6F3FF;\\n  min-height: 64px;\\n  box-sizing: border-box;\\n}\\n.collapsed-header[_ngcontent-%COMP%]   .expand-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 36px;\\n  height: 36px;\\n  border: none;\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 6px;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  flex-shrink: 0;\\n}\\n.collapsed-header[_ngcontent-%COMP%]   .expand-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.2);\\n}\\n\\n.playground-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow: hidden;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.agents-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: 20px;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0;\\n}\\n.agents-container[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n.agents-container[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: var(--scrollbar-track, #f1f1f1);\\n  border-radius: 3px;\\n}\\n.agents-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: var(--scrollbar-thumb, #c1c1c1);\\n  border-radius: 3px;\\n}\\n.agents-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: var(--scrollbar-thumb-hover, #a8a8a8);\\n}\\n\\n.collapsed-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 20px 10px;\\n}\\n.collapsed-content[_ngcontent-%COMP%]   .collapsed-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 8px;\\n  text-align: center;\\n}\\n.collapsed-content[_ngcontent-%COMP%]   .collapsed-info[_ngcontent-%COMP%]   .collapsed-text[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: var(--text-secondary);\\n  writing-mode: vertical-rl;\\n  text-orientation: mixed;\\n}\\n\\n@media (max-width: 1024px) {\\n  .workflow-playground-container.collapsed[_ngcontent-%COMP%] {\\n    width: 55px;\\n    min-width: 55px;\\n  }\\n  .playground-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .workflow-title[_ngcontent-%COMP%] {\\n    font-size: 17px;\\n  }\\n  .agents-container[_ngcontent-%COMP%] {\\n    padding: 18px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .workflow-playground-container.collapsed[_ngcontent-%COMP%] {\\n    width: 50px;\\n    min-width: 50px;\\n  }\\n  .playground-header[_ngcontent-%COMP%] {\\n    padding: 12px 16px;\\n    min-height: 56px;\\n  }\\n  .playground-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%] {\\n    gap: 8px;\\n  }\\n  .playground-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%], \\n   .playground-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .collapse-btn[_ngcontent-%COMP%] {\\n    width: 32px;\\n    height: 32px;\\n  }\\n  .playground-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .workflow-title[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n  .agents-container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .workflow-playground-container.collapsed[_ngcontent-%COMP%] {\\n    width: 45px;\\n    min-width: 45px;\\n  }\\n  .playground-header[_ngcontent-%COMP%] {\\n    padding: 10px 12px;\\n    min-height: 52px;\\n  }\\n  .playground-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%] {\\n    gap: 6px;\\n  }\\n  .playground-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%], \\n   .playground-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .collapse-btn[_ngcontent-%COMP%] {\\n    width: 28px;\\n    height: 28px;\\n  }\\n  .playground-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .workflow-title[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n  .agents-container[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n  .collapsed-content[_ngcontent-%COMP%] {\\n    padding: 16px 8px;\\n  }\\n  .collapsed-content[_ngcontent-%COMP%]   .collapsed-info[_ngcontent-%COMP%] {\\n    gap: 6px;\\n  }\\n  .collapsed-content[_ngcontent-%COMP%]   .collapsed-info[_ngcontent-%COMP%]   .collapsed-text[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return WorkflowPlaygroundComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "EventEmitter", "IconComponent", "AgentStepperCardComponent", "i0", "ɵɵelementStart", "ɵɵlistener", "WorkflowPlaygroundComponent_div_1_Template_button_click_2_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onBackClick", "ɵɵelement", "ɵɵelementEnd", "WorkflowPlaygroundComponent_div_1_Template_button_click_4_listener", "onCollapseToggle", "ɵɵadvance", "ɵɵproperty", "WorkflowPlaygroundComponent_div_2_Template_button_click_1_listener", "_r3", "WorkflowPlaygroundComponent_div_3_app_agent_stepper_card_2_Template_app_agent_stepper_card_inputChanged_0_listener", "$event", "agent_r5", "_r4", "$implicit", "onAgentInputChange", "id", "inputIndex", "value", "WorkflowPlaygroundComponent_div_3_app_agent_stepper_card_2_Template_app_agent_stepper_card_fileSelected_0_listener", "onAgentFileSelect", "files", "WorkflowPlaygroundComponent_div_3_app_agent_stepper_card_2_Template_app_agent_stepper_card_messageSent_0_listener", "i_r6", "index", "onMessageSent", "WorkflowPlaygroundComponent_div_3_app_agent_stepper_card_2_Template_app_agent_stepper_card_stepCompleted_0_listener", "onStepCompleted", "agents", "length", "isStepActive", "isStepCompleted", "ɵɵtemplate", "WorkflowPlaygroundComponent_div_3_app_agent_stepper_card_2_Template", "trackByAgentId", "WorkflowPlaygroundComponent", "isCollapsed", "workflowName", "backClicked", "collapseToggled", "agentInputChanged", "agentFileSelected", "messageSent", "currentActiveStep", "completedSteps", "Set", "emit", "agentId", "agentIndex", "event", "agent", "add", "has", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "WorkflowPlaygroundComponent_Template", "rf", "ctx", "WorkflowPlaygroundComponent_div_1_Template", "WorkflowPlaygroundComponent_div_2_Template", "WorkflowPlaygroundComponent_div_3_Template", "ɵɵclassProp", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\pages\\workflows\\workflow-execution\\components\\workflow-playground\\workflow-playground.component.ts", "C:\\console\\aava-ui-web\\projects\\shared\\pages\\workflows\\workflow-execution\\components\\workflow-playground\\workflow-playground.component.html"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { Component, EventEmitter, Input, Output } from '@angular/core';\nimport { IconComponent } from '@ava/play-comp-library';\nimport { AgentStepperCardComponent } from '../agent-stepper-card/agent-stepper-card.component';\n\nexport interface AgentData {\n  id: number;\n  name: string;\n  role: string;\n  goal: string;\n  backstory: string;\n  task: {\n    description: string;\n    expectedOutput: string;\n  };\n  serial: number;\n  inputs?: AgentInput[];\n  hasInputs?: boolean;\n}\n\nexport interface AgentInput {\n  placeholder: string;\n  inputName: string;\n  inputType: 'text' | 'image';\n  value?: string;\n  files?: File[];\n}\n\n@Component({\n  selector: 'app-workflow-playground',\n  standalone: true,\n  imports: [\n    CommonModule,\n    IconComponent,\n    AgentStepperCardComponent\n  ],\n  templateUrl: './workflow-playground.component.html',\n  styleUrls: ['./workflow-playground.component.scss']\n})\nexport class WorkflowPlaygroundComponent {\n  @Input() agents: AgentData[] = [];\n  @Input() isCollapsed: boolean = false;\n  @Input() workflowName: string = 'Workflow';\n\n  @Output() backClicked = new EventEmitter<void>();\n  @Output() collapseToggled = new EventEmitter<boolean>();\n  @Output() agentInputChanged = new EventEmitter<{agentId: number, inputIndex: number, value: string}>();\n  @Output() agentFileSelected = new EventEmitter<{agentId: number, inputIndex: number, files: File[]}>();\n  @Output() messageSent = new EventEmitter<{agentId: number, inputIndex: number, value: string, files?: File[]}>();\n\n  currentActiveStep: number = 0;\n  completedSteps: Set<number> = new Set();\n\n  onBackClick(): void {\n    this.backClicked.emit();\n  }\n\n  onCollapseToggle(): void {\n    this.isCollapsed = !this.isCollapsed;\n    this.collapseToggled.emit(this.isCollapsed);\n  }\n\n  onAgentInputChange(agentId: number, inputIndex: number, value: string): void {\n    this.agentInputChanged.emit({ agentId, inputIndex, value });\n  }\n\n  onAgentFileSelect(agentId: number, inputIndex: number, files: File[]): void {\n    this.agentFileSelected.emit({ agentId, inputIndex, files });\n  }\n\n  onMessageSent(agentIndex: number, event: {inputIndex: number, value: string, files?: File[]}): void {\n    const agent = this.agents[agentIndex];\n    if (agent) {\n      this.messageSent.emit({\n        agentId: agent.id,\n        inputIndex: event.inputIndex,\n        value: event.value,\n        files: event.files\n      });\n    }\n  }\n\n  onStepCompleted(agentIndex: number): void {\n    this.completedSteps.add(agentIndex);\n\n    // Move to next step if available\n    if (agentIndex === this.currentActiveStep && agentIndex < this.agents.length - 1) {\n      this.currentActiveStep = agentIndex + 1;\n    }\n  }\n\n  isStepActive(index: number): boolean {\n    return index === this.currentActiveStep;\n  }\n\n  isStepCompleted(index: number): boolean {\n    return this.completedSteps.has(index);\n  }\n\n  trackByAgentId(index: number, agent: AgentData): number {\n    return agent.id;\n  }\n}\n", "<div class=\"workflow-playground-container\" [class.collapsed]=\"isCollapsed\">\n  <!-- Header Section - Full when expanded -->\n  <div class=\"playground-header\" *ngIf=\"!isCollapsed\">\n    <div class=\"header-left\">\n      <button class=\"back-btn\" (click)=\"onBackClick()\" title=\"Go Back\">\n        <ava-icon iconName=\"arrow-left\" [iconSize]=\"20\" iconColor=\"var(--color-brand-primary)\"></ava-icon>\n      </button>\n      <!-- <h3 class=\"workflow-title\">{{ workflowName }}</h3> -->\n    </div>\n\n    <button class=\"collapse-btn\" (click)=\"onCollapseToggle()\" [title]=\"'Collapse Panel'\">\n      <ava-icon\n        iconName=\"panel-left\"\n        [iconSize]=\"20\"\n        iconColor=\"var(--color-brand-primary)\">\n      </ava-icon>\n    </button>\n  </div>\n\n  <!-- Collapsed Header - Only collapse icon -->\n  <div class=\"collapsed-header\" *ngIf=\"isCollapsed\">\n    <button class=\"expand-btn\" (click)=\"onCollapseToggle()\" title=\"Expand Panel\">\n      <ava-icon\n        iconName=\"panel-right\"\n        [iconSize]=\"20\"\n        iconColor=\"var(--color-brand-primary)\">\n      </ava-icon>\n    </button>\n  </div>\n\n  <!-- Content Section -->\n  <div class=\"playground-content\" *ngIf=\"!isCollapsed\">\n    <div class=\"agents-container\">\n      <app-agent-stepper-card\n        *ngFor=\"let agent of agents; let i = index; trackBy: trackByAgentId\"\n        [agent]=\"agent\"\n        [stepNumber]=\"i + 1\"\n        [isFirst]=\"i === 0\"\n        [isLast]=\"i === agents.length - 1\"\n        [isActive]=\"isStepActive(i)\"\n        [isCompleted]=\"isStepCompleted(i)\"\n        (inputChanged)=\"onAgentInputChange(agent.id, $event.inputIndex, $event.value)\"\n        (fileSelected)=\"onAgentFileSelect(agent.id, $event.inputIndex, $event.files)\"\n        (messageSent)=\"onMessageSent(i, $event)\"\n        (stepCompleted)=\"onStepCompleted(i)\">\n      </app-agent-stepper-card>\n    </div>\n  </div>\n\n\n</div>\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAAoBC,YAAY,QAAuB,eAAe;AACtE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,yBAAyB,QAAQ,oDAAoD;;;;;;ICCxFC,EAFJ,CAAAC,cAAA,aAAoD,aACzB,gBAC0C;IAAxCD,EAAA,CAAAE,UAAA,mBAAAC,mEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAC9CT,EAAA,CAAAU,SAAA,kBAAkG;IAGtGV,EAFE,CAAAW,YAAA,EAAS,EAEL;IAENX,EAAA,CAAAC,cAAA,gBAAqF;IAAxDD,EAAA,CAAAE,UAAA,mBAAAU,mEAAA;MAAAZ,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAO,gBAAA,EAAkB;IAAA,EAAC;IACvDb,EAAA,CAAAU,SAAA,kBAIW;IAEfV,EADE,CAAAW,YAAA,EAAS,EACL;;;IAZgCX,EAAA,CAAAc,SAAA,GAAe;IAAfd,EAAA,CAAAe,UAAA,gBAAe;IAKOf,EAAA,CAAAc,SAAA,EAA0B;IAA1Bd,EAAA,CAAAe,UAAA,2BAA0B;IAGhFf,EAAA,CAAAc,SAAA,EAAe;IAAfd,EAAA,CAAAe,UAAA,gBAAe;;;;;;IAQnBf,EADF,CAAAC,cAAA,cAAkD,iBAC6B;IAAlDD,EAAA,CAAAE,UAAA,mBAAAc,mEAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAAa,GAAA;MAAA,MAAAX,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAO,gBAAA,EAAkB;IAAA,EAAC;IACrDb,EAAA,CAAAU,SAAA,mBAIW;IAEfV,EADE,CAAAW,YAAA,EAAS,EACL;;;IAJAX,EAAA,CAAAc,SAAA,GAAe;IAAfd,EAAA,CAAAe,UAAA,gBAAe;;;;;;IASjBf,EAAA,CAAAC,cAAA,iCAWuC;IAArCD,EAHA,CAAAE,UAAA,0BAAAgB,mHAAAC,MAAA;MAAA,MAAAC,QAAA,GAAApB,EAAA,CAAAI,aAAA,CAAAiB,GAAA,EAAAC,SAAA;MAAA,MAAAhB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAgBF,MAAA,CAAAiB,kBAAA,CAAAH,QAAA,CAAAI,EAAA,EAAAL,MAAA,CAAAM,UAAA,EAAAN,MAAA,CAAAO,KAAA,CAA6D;IAAA,EAAC,0BAAAC,mHAAAR,MAAA;MAAA,MAAAC,QAAA,GAAApB,EAAA,CAAAI,aAAA,CAAAiB,GAAA,EAAAC,SAAA;MAAA,MAAAhB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAC9DF,MAAA,CAAAsB,iBAAA,CAAAR,QAAA,CAAAI,EAAA,EAAAL,MAAA,CAAAM,UAAA,EAAAN,MAAA,CAAAU,KAAA,CAA4D;IAAA,EAAC,yBAAAC,kHAAAX,MAAA;MAAA,MAAAY,IAAA,GAAA/B,EAAA,CAAAI,aAAA,CAAAiB,GAAA,EAAAW,KAAA;MAAA,MAAA1B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAC9DF,MAAA,CAAA2B,aAAA,CAAAF,IAAA,EAAAZ,MAAA,CAAwB;IAAA,EAAC,2BAAAe,oHAAA;MAAA,MAAAH,IAAA,GAAA/B,EAAA,CAAAI,aAAA,CAAAiB,GAAA,EAAAW,KAAA;MAAA,MAAA1B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CACvBF,MAAA,CAAA6B,eAAA,CAAAJ,IAAA,CAAkB;IAAA,EAAC;IACtC/B,EAAA,CAAAW,YAAA,EAAyB;;;;;;IALvBX,EALA,CAAAe,UAAA,UAAAK,QAAA,CAAe,eAAAW,IAAA,KACK,YAAAA,IAAA,OACD,WAAAA,IAAA,KAAAzB,MAAA,CAAA8B,MAAA,CAAAC,MAAA,KACe,aAAA/B,MAAA,CAAAgC,YAAA,CAAAP,IAAA,EACN,gBAAAzB,MAAA,CAAAiC,eAAA,CAAAR,IAAA,EACM;;;;;IARtC/B,EADF,CAAAC,cAAA,cAAqD,cACrB;IAC5BD,EAAA,CAAAwC,UAAA,IAAAC,mEAAA,qCAWuC;IAG3CzC,EADE,CAAAW,YAAA,EAAM,EACF;;;;IAbkBX,EAAA,CAAAc,SAAA,GAAW;IAAed,EAA1B,CAAAe,UAAA,YAAAT,MAAA,CAAA8B,MAAA,CAAW,iBAAA9B,MAAA,CAAAoC,cAAA,CAAsC;;;ADK3E,WAAaC,2BAA2B;EAAlC,MAAOA,2BAA2B;IAC7BP,MAAM,GAAgB,EAAE;IACxBQ,WAAW,GAAY,KAAK;IAC5BC,YAAY,GAAW,UAAU;IAEhCC,WAAW,GAAG,IAAIjD,YAAY,EAAQ;IACtCkD,eAAe,GAAG,IAAIlD,YAAY,EAAW;IAC7CmD,iBAAiB,GAAG,IAAInD,YAAY,EAAwD;IAC5FoD,iBAAiB,GAAG,IAAIpD,YAAY,EAAwD;IAC5FqD,WAAW,GAAG,IAAIrD,YAAY,EAAwE;IAEhHsD,iBAAiB,GAAW,CAAC;IAC7BC,cAAc,GAAgB,IAAIC,GAAG,EAAE;IAEvC5C,WAAWA,CAAA;MACT,IAAI,CAACqC,WAAW,CAACQ,IAAI,EAAE;IACzB;IAEAzC,gBAAgBA,CAAA;MACd,IAAI,CAAC+B,WAAW,GAAG,CAAC,IAAI,CAACA,WAAW;MACpC,IAAI,CAACG,eAAe,CAACO,IAAI,CAAC,IAAI,CAACV,WAAW,CAAC;IAC7C;IAEArB,kBAAkBA,CAACgC,OAAe,EAAE9B,UAAkB,EAAEC,KAAa;MACnE,IAAI,CAACsB,iBAAiB,CAACM,IAAI,CAAC;QAAEC,OAAO;QAAE9B,UAAU;QAAEC;MAAK,CAAE,CAAC;IAC7D;IAEAE,iBAAiBA,CAAC2B,OAAe,EAAE9B,UAAkB,EAAEI,KAAa;MAClE,IAAI,CAACoB,iBAAiB,CAACK,IAAI,CAAC;QAAEC,OAAO;QAAE9B,UAAU;QAAEI;MAAK,CAAE,CAAC;IAC7D;IAEAI,aAAaA,CAACuB,UAAkB,EAAEC,KAA0D;MAC1F,MAAMC,KAAK,GAAG,IAAI,CAACtB,MAAM,CAACoB,UAAU,CAAC;MACrC,IAAIE,KAAK,EAAE;QACT,IAAI,CAACR,WAAW,CAACI,IAAI,CAAC;UACpBC,OAAO,EAAEG,KAAK,CAAClC,EAAE;UACjBC,UAAU,EAAEgC,KAAK,CAAChC,UAAU;UAC5BC,KAAK,EAAE+B,KAAK,CAAC/B,KAAK;UAClBG,KAAK,EAAE4B,KAAK,CAAC5B;SACd,CAAC;MACJ;IACF;IAEAM,eAAeA,CAACqB,UAAkB;MAChC,IAAI,CAACJ,cAAc,CAACO,GAAG,CAACH,UAAU,CAAC;MAEnC;MACA,IAAIA,UAAU,KAAK,IAAI,CAACL,iBAAiB,IAAIK,UAAU,GAAG,IAAI,CAACpB,MAAM,CAACC,MAAM,GAAG,CAAC,EAAE;QAChF,IAAI,CAACc,iBAAiB,GAAGK,UAAU,GAAG,CAAC;MACzC;IACF;IAEAlB,YAAYA,CAACN,KAAa;MACxB,OAAOA,KAAK,KAAK,IAAI,CAACmB,iBAAiB;IACzC;IAEAZ,eAAeA,CAACP,KAAa;MAC3B,OAAO,IAAI,CAACoB,cAAc,CAACQ,GAAG,CAAC5B,KAAK,CAAC;IACvC;IAEAU,cAAcA,CAACV,KAAa,EAAE0B,KAAgB;MAC5C,OAAOA,KAAK,CAAClC,EAAE;IACjB;;uCA9DWmB,2BAA2B;IAAA;;YAA3BA,2BAA2B;MAAAkB,SAAA;MAAAC,MAAA;QAAA1B,MAAA;QAAAQ,WAAA;QAAAC,YAAA;MAAA;MAAAkB,OAAA;QAAAjB,WAAA;QAAAC,eAAA;QAAAC,iBAAA;QAAAC,iBAAA;QAAAC,WAAA;MAAA;MAAAc,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCvCxCrE,EAAA,CAAAC,cAAA,aAA2E;UA+BzED,EA7BA,CAAAwC,UAAA,IAAA+B,0CAAA,iBAAoD,IAAAC,0CAAA,iBAkBF,IAAAC,0CAAA,iBAWG;UAmBvDzE,EAAA,CAAAW,YAAA,EAAM;;;UAlDqCX,EAAA,CAAA0E,WAAA,cAAAJ,GAAA,CAAA1B,WAAA,CAA+B;UAExC5C,EAAA,CAAAc,SAAA,EAAkB;UAAlBd,EAAA,CAAAe,UAAA,UAAAuD,GAAA,CAAA1B,WAAA,CAAkB;UAkBnB5C,EAAA,CAAAc,SAAA,EAAiB;UAAjBd,EAAA,CAAAe,UAAA,SAAAuD,GAAA,CAAA1B,WAAA,CAAiB;UAWf5C,EAAA,CAAAc,SAAA,EAAkB;UAAlBd,EAAA,CAAAe,UAAA,UAAAuD,GAAA,CAAA1B,WAAA,CAAkB;;;qBDCjDhD,YAAY,EAAA+E,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZ/E,aAAa,EACbC,yBAAyB;MAAA+E,MAAA;IAAA;;SAKhBnC,2BAA2B;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}