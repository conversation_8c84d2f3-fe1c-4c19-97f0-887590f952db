{"ast": null, "code": "import _asyncToGenerator from \"C:/console/aava-ui-web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { addStandardDisposableListener, getDomNodePagePosition } from '../../../../../../base/browser/dom.js';\nimport { Action } from '../../../../../../base/common/actions.js';\nimport { Codicon } from '../../../../../../base/common/codicons.js';\nimport { Disposable } from '../../../../../../base/common/lifecycle.js';\nimport { isIOS } from '../../../../../../base/common/platform.js';\nimport { ThemeIcon } from '../../../../../../base/common/themables.js';\nimport { localize } from '../../../../../../nls.js';\nexport class InlineDiffDeletedCodeMargin extends Disposable {\n  get visibility() {\n    return this._visibility;\n  }\n  set visibility(_visibility) {\n    if (this._visibility !== _visibility) {\n      this._visibility = _visibility;\n      this._diffActions.style.visibility = _visibility ? 'visible' : 'hidden';\n    }\n  }\n  constructor(_getViewZoneId, _marginDomNode, _modifiedEditor, _diff, _editor, _viewLineCounts, _originalTextModel, _contextMenuService, _clipboardService) {\n    var _this;\n    super();\n    _this = this;\n    this._getViewZoneId = _getViewZoneId;\n    this._marginDomNode = _marginDomNode;\n    this._modifiedEditor = _modifiedEditor;\n    this._diff = _diff;\n    this._editor = _editor;\n    this._viewLineCounts = _viewLineCounts;\n    this._originalTextModel = _originalTextModel;\n    this._contextMenuService = _contextMenuService;\n    this._clipboardService = _clipboardService;\n    this._visibility = false;\n    // make sure the diff margin shows above overlay.\n    this._marginDomNode.style.zIndex = '10';\n    this._diffActions = document.createElement('div');\n    this._diffActions.className = ThemeIcon.asClassName(Codicon.lightBulb) + ' lightbulb-glyph';\n    this._diffActions.style.position = 'absolute';\n    const lineHeight = this._modifiedEditor.getOption(67 /* EditorOption.lineHeight */);\n    this._diffActions.style.right = '0px';\n    this._diffActions.style.visibility = 'hidden';\n    this._diffActions.style.height = `${lineHeight}px`;\n    this._diffActions.style.lineHeight = `${lineHeight}px`;\n    this._marginDomNode.appendChild(this._diffActions);\n    let currentLineNumberOffset = 0;\n    const useShadowDOM = _modifiedEditor.getOption(128 /* EditorOption.useShadowDOM */) && !isIOS; // Do not use shadow dom on IOS #122035\n    const showContextMenu = (x, y) => {\n      this._contextMenuService.showContextMenu({\n        domForShadowRoot: useShadowDOM ? _modifiedEditor.getDomNode() ?? undefined : undefined,\n        getAnchor: () => ({\n          x,\n          y\n        }),\n        getActions: () => {\n          const actions = [];\n          const isDeletion = _diff.modified.isEmpty;\n          // default action\n          actions.push(new Action('diff.clipboard.copyDeletedContent', isDeletion ? _diff.original.length > 1 ? localize('diff.clipboard.copyDeletedLinesContent.label', \"Copy deleted lines\") : localize('diff.clipboard.copyDeletedLinesContent.single.label', \"Copy deleted line\") : _diff.original.length > 1 ? localize('diff.clipboard.copyChangedLinesContent.label', \"Copy changed lines\") : localize('diff.clipboard.copyChangedLinesContent.single.label', \"Copy changed line\"), undefined, true, /*#__PURE__*/_asyncToGenerator(function* () {\n            const originalText = _this._originalTextModel.getValueInRange(_diff.original.toExclusiveRange());\n            yield _this._clipboardService.writeText(originalText);\n          })));\n          if (_diff.original.length > 1) {\n            actions.push(new Action('diff.clipboard.copyDeletedLineContent', isDeletion ? localize('diff.clipboard.copyDeletedLineContent.label', \"Copy deleted line ({0})\", _diff.original.startLineNumber + currentLineNumberOffset) : localize('diff.clipboard.copyChangedLineContent.label', \"Copy changed line ({0})\", _diff.original.startLineNumber + currentLineNumberOffset), undefined, true, /*#__PURE__*/_asyncToGenerator(function* () {\n              let lineContent = _this._originalTextModel.getLineContent(_diff.original.startLineNumber + currentLineNumberOffset);\n              if (lineContent === '') {\n                // empty line -> new line\n                const eof = _this._originalTextModel.getEndOfLineSequence();\n                lineContent = eof === 0 /* EndOfLineSequence.LF */ ? '\\n' : '\\r\\n';\n              }\n              yield _this._clipboardService.writeText(lineContent);\n            })));\n          }\n          const readOnly = _modifiedEditor.getOption(92 /* EditorOption.readOnly */);\n          if (!readOnly) {\n            actions.push(new Action('diff.inline.revertChange', localize('diff.inline.revertChange.label', \"Revert this change\"), undefined, true, /*#__PURE__*/_asyncToGenerator(function* () {\n              _this._editor.revert(_this._diff);\n            })));\n          }\n          return actions;\n        },\n        autoSelectFirstItem: true\n      });\n    };\n    this._register(addStandardDisposableListener(this._diffActions, 'mousedown', e => {\n      if (!e.leftButton) {\n        return;\n      }\n      const {\n        top,\n        height\n      } = getDomNodePagePosition(this._diffActions);\n      const pad = Math.floor(lineHeight / 3);\n      e.preventDefault();\n      showContextMenu(e.posx, top + height + pad);\n    }));\n    this._register(_modifiedEditor.onMouseMove(e => {\n      if ((e.target.type === 8 /* MouseTargetType.CONTENT_VIEW_ZONE */ || e.target.type === 5 /* MouseTargetType.GUTTER_VIEW_ZONE */) && e.target.detail.viewZoneId === this._getViewZoneId()) {\n        currentLineNumberOffset = this._updateLightBulbPosition(this._marginDomNode, e.event.browserEvent.y, lineHeight);\n        this.visibility = true;\n      } else {\n        this.visibility = false;\n      }\n    }));\n    this._register(_modifiedEditor.onMouseDown(e => {\n      if (!e.event.leftButton) {\n        return;\n      }\n      if (e.target.type === 8 /* MouseTargetType.CONTENT_VIEW_ZONE */ || e.target.type === 5 /* MouseTargetType.GUTTER_VIEW_ZONE */) {\n        const viewZoneId = e.target.detail.viewZoneId;\n        if (viewZoneId === this._getViewZoneId()) {\n          e.event.preventDefault();\n          currentLineNumberOffset = this._updateLightBulbPosition(this._marginDomNode, e.event.browserEvent.y, lineHeight);\n          showContextMenu(e.event.posx, e.event.posy + lineHeight);\n        }\n      }\n    }));\n  }\n  _updateLightBulbPosition(marginDomNode, y, lineHeight) {\n    const {\n      top\n    } = getDomNodePagePosition(marginDomNode);\n    const offset = y - top;\n    const lineNumberOffset = Math.floor(offset / lineHeight);\n    const newTop = lineNumberOffset * lineHeight;\n    this._diffActions.style.top = `${newTop}px`;\n    if (this._viewLineCounts) {\n      let acc = 0;\n      for (let i = 0; i < this._viewLineCounts.length; i++) {\n        acc += this._viewLineCounts[i];\n        if (lineNumberOffset < acc) {\n          return i;\n        }\n      }\n    }\n    return lineNumberOffset;\n  }\n}", "map": {"version": 3, "names": ["addStandardDisposableListener", "getDomNodePagePosition", "Action", "Codicon", "Disposable", "isIOS", "ThemeIcon", "localize", "InlineDiffDeletedCodeMargin", "visibility", "_visibility", "_diffActions", "style", "constructor", "_getViewZoneId", "_marginDomNode", "_modifiedEditor", "_diff", "_editor", "_viewLineCounts", "_originalTextModel", "_contextMenuService", "_clipboardService", "_this", "this", "zIndex", "document", "createElement", "className", "asClassName", "lightBulb", "position", "lineHeight", "getOption", "right", "height", "append<PERSON><PERSON><PERSON>", "currentLineNumberOffset", "useShadowDOM", "showContextMenu", "x", "y", "domForShadowRoot", "getDomNode", "undefined", "getAnchor", "getActions", "actions", "isDeletion", "modified", "isEmpty", "push", "original", "length", "_asyncToGenerator", "originalText", "getValueInRange", "toExclusiveRange", "writeText", "startLineNumber", "lineContent", "get<PERSON>ineC<PERSON>nt", "eof", "getEndOfLineSequence", "readOnly", "revert", "autoSelectFirstItem", "_register", "e", "leftButton", "top", "pad", "Math", "floor", "preventDefault", "posx", "onMouseMove", "target", "type", "detail", "viewZoneId", "_updateLightBulbPosition", "event", "browserEvent", "onMouseDown", "posy", "marginDomNode", "offset", "lineNumberOffset", "newTop", "acc", "i"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/browser/widget/diffEditor/components/diffEditorViewZones/inlineDiffDeletedCodeMargin.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { addStandardDisposableListener, getDomNodePagePosition } from '../../../../../../base/browser/dom.js';\nimport { Action } from '../../../../../../base/common/actions.js';\nimport { Codicon } from '../../../../../../base/common/codicons.js';\nimport { Disposable } from '../../../../../../base/common/lifecycle.js';\nimport { isIOS } from '../../../../../../base/common/platform.js';\nimport { ThemeIcon } from '../../../../../../base/common/themables.js';\nimport { localize } from '../../../../../../nls.js';\nexport class InlineDiffDeletedCodeMargin extends Disposable {\n    get visibility() {\n        return this._visibility;\n    }\n    set visibility(_visibility) {\n        if (this._visibility !== _visibility) {\n            this._visibility = _visibility;\n            this._diffActions.style.visibility = _visibility ? 'visible' : 'hidden';\n        }\n    }\n    constructor(_getViewZoneId, _marginDomNode, _modifiedEditor, _diff, _editor, _viewLineCounts, _originalTextModel, _contextMenuService, _clipboardService) {\n        super();\n        this._getViewZoneId = _getViewZoneId;\n        this._marginDomNode = _marginDomNode;\n        this._modifiedEditor = _modifiedEditor;\n        this._diff = _diff;\n        this._editor = _editor;\n        this._viewLineCounts = _viewLineCounts;\n        this._originalTextModel = _originalTextModel;\n        this._contextMenuService = _contextMenuService;\n        this._clipboardService = _clipboardService;\n        this._visibility = false;\n        // make sure the diff margin shows above overlay.\n        this._marginDomNode.style.zIndex = '10';\n        this._diffActions = document.createElement('div');\n        this._diffActions.className = ThemeIcon.asClassName(Codicon.lightBulb) + ' lightbulb-glyph';\n        this._diffActions.style.position = 'absolute';\n        const lineHeight = this._modifiedEditor.getOption(67 /* EditorOption.lineHeight */);\n        this._diffActions.style.right = '0px';\n        this._diffActions.style.visibility = 'hidden';\n        this._diffActions.style.height = `${lineHeight}px`;\n        this._diffActions.style.lineHeight = `${lineHeight}px`;\n        this._marginDomNode.appendChild(this._diffActions);\n        let currentLineNumberOffset = 0;\n        const useShadowDOM = _modifiedEditor.getOption(128 /* EditorOption.useShadowDOM */) && !isIOS; // Do not use shadow dom on IOS #122035\n        const showContextMenu = (x, y) => {\n            this._contextMenuService.showContextMenu({\n                domForShadowRoot: useShadowDOM ? _modifiedEditor.getDomNode() ?? undefined : undefined,\n                getAnchor: () => ({ x, y }),\n                getActions: () => {\n                    const actions = [];\n                    const isDeletion = _diff.modified.isEmpty;\n                    // default action\n                    actions.push(new Action('diff.clipboard.copyDeletedContent', isDeletion\n                        ? (_diff.original.length > 1\n                            ? localize('diff.clipboard.copyDeletedLinesContent.label', \"Copy deleted lines\")\n                            : localize('diff.clipboard.copyDeletedLinesContent.single.label', \"Copy deleted line\"))\n                        : (_diff.original.length > 1\n                            ? localize('diff.clipboard.copyChangedLinesContent.label', \"Copy changed lines\")\n                            : localize('diff.clipboard.copyChangedLinesContent.single.label', \"Copy changed line\")), undefined, true, async () => {\n                        const originalText = this._originalTextModel.getValueInRange(_diff.original.toExclusiveRange());\n                        await this._clipboardService.writeText(originalText);\n                    }));\n                    if (_diff.original.length > 1) {\n                        actions.push(new Action('diff.clipboard.copyDeletedLineContent', isDeletion\n                            ? localize('diff.clipboard.copyDeletedLineContent.label', \"Copy deleted line ({0})\", _diff.original.startLineNumber + currentLineNumberOffset)\n                            : localize('diff.clipboard.copyChangedLineContent.label', \"Copy changed line ({0})\", _diff.original.startLineNumber + currentLineNumberOffset), undefined, true, async () => {\n                            let lineContent = this._originalTextModel.getLineContent(_diff.original.startLineNumber + currentLineNumberOffset);\n                            if (lineContent === '') {\n                                // empty line -> new line\n                                const eof = this._originalTextModel.getEndOfLineSequence();\n                                lineContent = eof === 0 /* EndOfLineSequence.LF */ ? '\\n' : '\\r\\n';\n                            }\n                            await this._clipboardService.writeText(lineContent);\n                        }));\n                    }\n                    const readOnly = _modifiedEditor.getOption(92 /* EditorOption.readOnly */);\n                    if (!readOnly) {\n                        actions.push(new Action('diff.inline.revertChange', localize('diff.inline.revertChange.label', \"Revert this change\"), undefined, true, async () => {\n                            this._editor.revert(this._diff);\n                        }));\n                    }\n                    return actions;\n                },\n                autoSelectFirstItem: true\n            });\n        };\n        this._register(addStandardDisposableListener(this._diffActions, 'mousedown', e => {\n            if (!e.leftButton) {\n                return;\n            }\n            const { top, height } = getDomNodePagePosition(this._diffActions);\n            const pad = Math.floor(lineHeight / 3);\n            e.preventDefault();\n            showContextMenu(e.posx, top + height + pad);\n        }));\n        this._register(_modifiedEditor.onMouseMove((e) => {\n            if ((e.target.type === 8 /* MouseTargetType.CONTENT_VIEW_ZONE */ || e.target.type === 5 /* MouseTargetType.GUTTER_VIEW_ZONE */) && e.target.detail.viewZoneId === this._getViewZoneId()) {\n                currentLineNumberOffset = this._updateLightBulbPosition(this._marginDomNode, e.event.browserEvent.y, lineHeight);\n                this.visibility = true;\n            }\n            else {\n                this.visibility = false;\n            }\n        }));\n        this._register(_modifiedEditor.onMouseDown((e) => {\n            if (!e.event.leftButton) {\n                return;\n            }\n            if (e.target.type === 8 /* MouseTargetType.CONTENT_VIEW_ZONE */ || e.target.type === 5 /* MouseTargetType.GUTTER_VIEW_ZONE */) {\n                const viewZoneId = e.target.detail.viewZoneId;\n                if (viewZoneId === this._getViewZoneId()) {\n                    e.event.preventDefault();\n                    currentLineNumberOffset = this._updateLightBulbPosition(this._marginDomNode, e.event.browserEvent.y, lineHeight);\n                    showContextMenu(e.event.posx, e.event.posy + lineHeight);\n                }\n            }\n        }));\n    }\n    _updateLightBulbPosition(marginDomNode, y, lineHeight) {\n        const { top } = getDomNodePagePosition(marginDomNode);\n        const offset = y - top;\n        const lineNumberOffset = Math.floor(offset / lineHeight);\n        const newTop = lineNumberOffset * lineHeight;\n        this._diffActions.style.top = `${newTop}px`;\n        if (this._viewLineCounts) {\n            let acc = 0;\n            for (let i = 0; i < this._viewLineCounts.length; i++) {\n                acc += this._viewLineCounts[i];\n                if (lineNumberOffset < acc) {\n                    return i;\n                }\n            }\n        }\n        return lineNumberOffset;\n    }\n}\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA,SAASA,6BAA6B,EAAEC,sBAAsB,QAAQ,uCAAuC;AAC7G,SAASC,MAAM,QAAQ,0CAA0C;AACjE,SAASC,OAAO,QAAQ,2CAA2C;AACnE,SAASC,UAAU,QAAQ,4CAA4C;AACvE,SAASC,KAAK,QAAQ,2CAA2C;AACjE,SAASC,SAAS,QAAQ,4CAA4C;AACtE,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,OAAO,MAAMC,2BAA2B,SAASJ,UAAU,CAAC;EACxD,IAAIK,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACC,WAAW;EAC3B;EACA,IAAID,UAAUA,CAACC,WAAW,EAAE;IACxB,IAAI,IAAI,CAACA,WAAW,KAAKA,WAAW,EAAE;MAClC,IAAI,CAACA,WAAW,GAAGA,WAAW;MAC9B,IAAI,CAACC,YAAY,CAACC,KAAK,CAACH,UAAU,GAAGC,WAAW,GAAG,SAAS,GAAG,QAAQ;IAC3E;EACJ;EACAG,WAAWA,CAACC,cAAc,EAAEC,cAAc,EAAEC,eAAe,EAAEC,KAAK,EAAEC,OAAO,EAAEC,eAAe,EAAEC,kBAAkB,EAAEC,mBAAmB,EAAEC,iBAAiB,EAAE;IAAA,IAAAC,KAAA;IACtJ,KAAK,CAAC,CAAC;IAAAA,KAAA,GAAAC,IAAA;IACP,IAAI,CAACV,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACZ,WAAW,GAAG,KAAK;IACxB;IACA,IAAI,CAACK,cAAc,CAACH,KAAK,CAACa,MAAM,GAAG,IAAI;IACvC,IAAI,CAACd,YAAY,GAAGe,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IACjD,IAAI,CAAChB,YAAY,CAACiB,SAAS,GAAGtB,SAAS,CAACuB,WAAW,CAAC1B,OAAO,CAAC2B,SAAS,CAAC,GAAG,kBAAkB;IAC3F,IAAI,CAACnB,YAAY,CAACC,KAAK,CAACmB,QAAQ,GAAG,UAAU;IAC7C,MAAMC,UAAU,GAAG,IAAI,CAAChB,eAAe,CAACiB,SAAS,CAAC,EAAE,CAAC,6BAA6B,CAAC;IACnF,IAAI,CAACtB,YAAY,CAACC,KAAK,CAACsB,KAAK,GAAG,KAAK;IACrC,IAAI,CAACvB,YAAY,CAACC,KAAK,CAACH,UAAU,GAAG,QAAQ;IAC7C,IAAI,CAACE,YAAY,CAACC,KAAK,CAACuB,MAAM,GAAG,GAAGH,UAAU,IAAI;IAClD,IAAI,CAACrB,YAAY,CAACC,KAAK,CAACoB,UAAU,GAAG,GAAGA,UAAU,IAAI;IACtD,IAAI,CAACjB,cAAc,CAACqB,WAAW,CAAC,IAAI,CAACzB,YAAY,CAAC;IAClD,IAAI0B,uBAAuB,GAAG,CAAC;IAC/B,MAAMC,YAAY,GAAGtB,eAAe,CAACiB,SAAS,CAAC,GAAG,CAAC,+BAA+B,CAAC,IAAI,CAAC5B,KAAK,CAAC,CAAC;IAC/F,MAAMkC,eAAe,GAAGA,CAACC,CAAC,EAAEC,CAAC,KAAK;MAC9B,IAAI,CAACpB,mBAAmB,CAACkB,eAAe,CAAC;QACrCG,gBAAgB,EAAEJ,YAAY,GAAGtB,eAAe,CAAC2B,UAAU,CAAC,CAAC,IAAIC,SAAS,GAAGA,SAAS;QACtFC,SAAS,EAAEA,CAAA,MAAO;UAAEL,CAAC;UAAEC;QAAE,CAAC,CAAC;QAC3BK,UAAU,EAAEA,CAAA,KAAM;UACd,MAAMC,OAAO,GAAG,EAAE;UAClB,MAAMC,UAAU,GAAG/B,KAAK,CAACgC,QAAQ,CAACC,OAAO;UACzC;UACAH,OAAO,CAACI,IAAI,CAAC,IAAIjD,MAAM,CAAC,mCAAmC,EAAE8C,UAAU,GAChE/B,KAAK,CAACmC,QAAQ,CAACC,MAAM,GAAG,CAAC,GACtB9C,QAAQ,CAAC,8CAA8C,EAAE,oBAAoB,CAAC,GAC9EA,QAAQ,CAAC,qDAAqD,EAAE,mBAAmB,CAAC,GACvFU,KAAK,CAACmC,QAAQ,CAACC,MAAM,GAAG,CAAC,GACtB9C,QAAQ,CAAC,8CAA8C,EAAE,oBAAoB,CAAC,GAC9EA,QAAQ,CAAC,qDAAqD,EAAE,mBAAmB,CAAE,EAAEqC,SAAS,EAAE,IAAI,eAAAU,iBAAA,CAAE,aAAY;YAC1H,MAAMC,YAAY,GAAGhC,KAAI,CAACH,kBAAkB,CAACoC,eAAe,CAACvC,KAAK,CAACmC,QAAQ,CAACK,gBAAgB,CAAC,CAAC,CAAC;YAC/F,MAAMlC,KAAI,CAACD,iBAAiB,CAACoC,SAAS,CAACH,YAAY,CAAC;UACxD,CAAC,EAAC,CAAC;UACH,IAAItC,KAAK,CAACmC,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;YAC3BN,OAAO,CAACI,IAAI,CAAC,IAAIjD,MAAM,CAAC,uCAAuC,EAAE8C,UAAU,GACrEzC,QAAQ,CAAC,6CAA6C,EAAE,yBAAyB,EAAEU,KAAK,CAACmC,QAAQ,CAACO,eAAe,GAAGtB,uBAAuB,CAAC,GAC5I9B,QAAQ,CAAC,6CAA6C,EAAE,yBAAyB,EAAEU,KAAK,CAACmC,QAAQ,CAACO,eAAe,GAAGtB,uBAAuB,CAAC,EAAEO,SAAS,EAAE,IAAI,eAAAU,iBAAA,CAAE,aAAY;cAC7K,IAAIM,WAAW,GAAGrC,KAAI,CAACH,kBAAkB,CAACyC,cAAc,CAAC5C,KAAK,CAACmC,QAAQ,CAACO,eAAe,GAAGtB,uBAAuB,CAAC;cAClH,IAAIuB,WAAW,KAAK,EAAE,EAAE;gBACpB;gBACA,MAAME,GAAG,GAAGvC,KAAI,CAACH,kBAAkB,CAAC2C,oBAAoB,CAAC,CAAC;gBAC1DH,WAAW,GAAGE,GAAG,KAAK,CAAC,CAAC,6BAA6B,IAAI,GAAG,MAAM;cACtE;cACA,MAAMvC,KAAI,CAACD,iBAAiB,CAACoC,SAAS,CAACE,WAAW,CAAC;YACvD,CAAC,EAAC,CAAC;UACP;UACA,MAAMI,QAAQ,GAAGhD,eAAe,CAACiB,SAAS,CAAC,EAAE,CAAC,2BAA2B,CAAC;UAC1E,IAAI,CAAC+B,QAAQ,EAAE;YACXjB,OAAO,CAACI,IAAI,CAAC,IAAIjD,MAAM,CAAC,0BAA0B,EAAEK,QAAQ,CAAC,gCAAgC,EAAE,oBAAoB,CAAC,EAAEqC,SAAS,EAAE,IAAI,eAAAU,iBAAA,CAAE,aAAY;cAC/I/B,KAAI,CAACL,OAAO,CAAC+C,MAAM,CAAC1C,KAAI,CAACN,KAAK,CAAC;YACnC,CAAC,EAAC,CAAC;UACP;UACA,OAAO8B,OAAO;QAClB,CAAC;QACDmB,mBAAmB,EAAE;MACzB,CAAC,CAAC;IACN,CAAC;IACD,IAAI,CAACC,SAAS,CAACnE,6BAA6B,CAAC,IAAI,CAACW,YAAY,EAAE,WAAW,EAAEyD,CAAC,IAAI;MAC9E,IAAI,CAACA,CAAC,CAACC,UAAU,EAAE;QACf;MACJ;MACA,MAAM;QAAEC,GAAG;QAAEnC;MAAO,CAAC,GAAGlC,sBAAsB,CAAC,IAAI,CAACU,YAAY,CAAC;MACjE,MAAM4D,GAAG,GAAGC,IAAI,CAACC,KAAK,CAACzC,UAAU,GAAG,CAAC,CAAC;MACtCoC,CAAC,CAACM,cAAc,CAAC,CAAC;MAClBnC,eAAe,CAAC6B,CAAC,CAACO,IAAI,EAAEL,GAAG,GAAGnC,MAAM,GAAGoC,GAAG,CAAC;IAC/C,CAAC,CAAC,CAAC;IACH,IAAI,CAACJ,SAAS,CAACnD,eAAe,CAAC4D,WAAW,CAAER,CAAC,IAAK;MAC9C,IAAI,CAACA,CAAC,CAACS,MAAM,CAACC,IAAI,KAAK,CAAC,CAAC,2CAA2CV,CAAC,CAACS,MAAM,CAACC,IAAI,KAAK,CAAC,CAAC,2CAA2CV,CAAC,CAACS,MAAM,CAACE,MAAM,CAACC,UAAU,KAAK,IAAI,CAAClE,cAAc,CAAC,CAAC,EAAE;QACrLuB,uBAAuB,GAAG,IAAI,CAAC4C,wBAAwB,CAAC,IAAI,CAAClE,cAAc,EAAEqD,CAAC,CAACc,KAAK,CAACC,YAAY,CAAC1C,CAAC,EAAET,UAAU,CAAC;QAChH,IAAI,CAACvB,UAAU,GAAG,IAAI;MAC1B,CAAC,MACI;QACD,IAAI,CAACA,UAAU,GAAG,KAAK;MAC3B;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,CAAC0D,SAAS,CAACnD,eAAe,CAACoE,WAAW,CAAEhB,CAAC,IAAK;MAC9C,IAAI,CAACA,CAAC,CAACc,KAAK,CAACb,UAAU,EAAE;QACrB;MACJ;MACA,IAAID,CAAC,CAACS,MAAM,CAACC,IAAI,KAAK,CAAC,CAAC,2CAA2CV,CAAC,CAACS,MAAM,CAACC,IAAI,KAAK,CAAC,CAAC,wCAAwC;QAC3H,MAAME,UAAU,GAAGZ,CAAC,CAACS,MAAM,CAACE,MAAM,CAACC,UAAU;QAC7C,IAAIA,UAAU,KAAK,IAAI,CAAClE,cAAc,CAAC,CAAC,EAAE;UACtCsD,CAAC,CAACc,KAAK,CAACR,cAAc,CAAC,CAAC;UACxBrC,uBAAuB,GAAG,IAAI,CAAC4C,wBAAwB,CAAC,IAAI,CAAClE,cAAc,EAAEqD,CAAC,CAACc,KAAK,CAACC,YAAY,CAAC1C,CAAC,EAAET,UAAU,CAAC;UAChHO,eAAe,CAAC6B,CAAC,CAACc,KAAK,CAACP,IAAI,EAAEP,CAAC,CAACc,KAAK,CAACG,IAAI,GAAGrD,UAAU,CAAC;QAC5D;MACJ;IACJ,CAAC,CAAC,CAAC;EACP;EACAiD,wBAAwBA,CAACK,aAAa,EAAE7C,CAAC,EAAET,UAAU,EAAE;IACnD,MAAM;MAAEsC;IAAI,CAAC,GAAGrE,sBAAsB,CAACqF,aAAa,CAAC;IACrD,MAAMC,MAAM,GAAG9C,CAAC,GAAG6B,GAAG;IACtB,MAAMkB,gBAAgB,GAAGhB,IAAI,CAACC,KAAK,CAACc,MAAM,GAAGvD,UAAU,CAAC;IACxD,MAAMyD,MAAM,GAAGD,gBAAgB,GAAGxD,UAAU;IAC5C,IAAI,CAACrB,YAAY,CAACC,KAAK,CAAC0D,GAAG,GAAG,GAAGmB,MAAM,IAAI;IAC3C,IAAI,IAAI,CAACtE,eAAe,EAAE;MACtB,IAAIuE,GAAG,GAAG,CAAC;MACX,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACxE,eAAe,CAACkC,MAAM,EAAEsC,CAAC,EAAE,EAAE;QAClDD,GAAG,IAAI,IAAI,CAACvE,eAAe,CAACwE,CAAC,CAAC;QAC9B,IAAIH,gBAAgB,GAAGE,GAAG,EAAE;UACxB,OAAOC,CAAC;QACZ;MACJ;IACJ;IACA,OAAOH,gBAAgB;EAC3B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}