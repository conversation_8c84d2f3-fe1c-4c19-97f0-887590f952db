{"ast": null, "code": "export default function* () {\n  var node = this,\n    current,\n    next = [node],\n    children,\n    i,\n    n;\n  do {\n    current = next.reverse(), next = [];\n    while (node = current.pop()) {\n      yield node;\n      if (children = node.children) {\n        for (i = 0, n = children.length; i < n; ++i) {\n          next.push(children[i]);\n        }\n      }\n    }\n  } while (next.length);\n}", "map": {"version": 3, "names": ["node", "current", "next", "children", "i", "n", "reverse", "pop", "length", "push"], "sources": ["C:/console/aava-ui-web/node_modules/d3-hierarchy/src/hierarchy/iterator.js"], "sourcesContent": ["export default function*() {\n  var node = this, current, next = [node], children, i, n;\n  do {\n    current = next.reverse(), next = [];\n    while (node = current.pop()) {\n      yield node;\n      if (children = node.children) {\n        for (i = 0, n = children.length; i < n; ++i) {\n          next.push(children[i]);\n        }\n      }\n    }\n  } while (next.length);\n}\n"], "mappings": "AAAA,eAAe,aAAY;EACzB,IAAIA,IAAI,GAAG,IAAI;IAAEC,OAAO;IAAEC,IAAI,GAAG,CAACF,IAAI,CAAC;IAAEG,QAAQ;IAAEC,CAAC;IAAEC,CAAC;EACvD,GAAG;IACDJ,OAAO,GAAGC,IAAI,CAACI,OAAO,CAAC,CAAC,EAAEJ,IAAI,GAAG,EAAE;IACnC,OAAOF,IAAI,GAAGC,OAAO,CAACM,GAAG,CAAC,CAAC,EAAE;MAC3B,MAAMP,IAAI;MACV,IAAIG,QAAQ,GAAGH,IAAI,CAACG,QAAQ,EAAE;QAC5B,KAAKC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGF,QAAQ,CAACK,MAAM,EAAEJ,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;UAC3CF,IAAI,CAACO,IAAI,CAACN,QAAQ,CAACC,CAAC,CAAC,CAAC;QACxB;MACF;IACF;EACF,CAAC,QAAQF,IAAI,CAACM,MAAM;AACtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}