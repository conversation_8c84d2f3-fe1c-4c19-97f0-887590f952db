{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Widget } from '../widget.js';\nimport { ThemeIcon } from '../../../common/themables.js';\nimport { Emitter } from '../../../common/event.js';\nimport './toggle.css';\nimport { getDefaultHoverDelegate } from '../hover/hoverDelegateFactory.js';\nimport { getBaseLayerHoverDelegate } from '../hover/hoverDelegate2.js';\nexport const unthemedToggleStyles = {\n  inputActiveOptionBorder: '#007ACC00',\n  inputActiveOptionForeground: '#FFFFFF',\n  inputActiveOptionBackground: '#0E639C50'\n};\nexport class Toggle extends Widget {\n  constructor(opts) {\n    super();\n    this._onChange = this._register(new Emitter());\n    this.onChange = this._onChange.event;\n    this._onKeyDown = this._register(new Emitter());\n    this.onKeyDown = this._onKeyDown.event;\n    this._opts = opts;\n    this._checked = this._opts.isChecked;\n    const classes = ['monaco-custom-toggle'];\n    if (this._opts.icon) {\n      this._icon = this._opts.icon;\n      classes.push(...ThemeIcon.asClassNameArray(this._icon));\n    }\n    if (this._opts.actionClassName) {\n      classes.push(...this._opts.actionClassName.split(' '));\n    }\n    if (this._checked) {\n      classes.push('checked');\n    }\n    this.domNode = document.createElement('div');\n    this._hover = this._register(getBaseLayerHoverDelegate().setupManagedHover(opts.hoverDelegate ?? getDefaultHoverDelegate('mouse'), this.domNode, this._opts.title));\n    this.domNode.classList.add(...classes);\n    if (!this._opts.notFocusable) {\n      this.domNode.tabIndex = 0;\n    }\n    this.domNode.setAttribute('role', 'checkbox');\n    this.domNode.setAttribute('aria-checked', String(this._checked));\n    this.domNode.setAttribute('aria-label', this._opts.title);\n    this.applyStyles();\n    this.onclick(this.domNode, ev => {\n      if (this.enabled) {\n        this.checked = !this._checked;\n        this._onChange.fire(false);\n        ev.preventDefault();\n      }\n    });\n    this._register(this.ignoreGesture(this.domNode));\n    this.onkeydown(this.domNode, keyboardEvent => {\n      if (keyboardEvent.keyCode === 10 /* KeyCode.Space */ || keyboardEvent.keyCode === 3 /* KeyCode.Enter */) {\n        this.checked = !this._checked;\n        this._onChange.fire(true);\n        keyboardEvent.preventDefault();\n        keyboardEvent.stopPropagation();\n        return;\n      }\n      this._onKeyDown.fire(keyboardEvent);\n    });\n  }\n  get enabled() {\n    return this.domNode.getAttribute('aria-disabled') !== 'true';\n  }\n  focus() {\n    this.domNode.focus();\n  }\n  get checked() {\n    return this._checked;\n  }\n  set checked(newIsChecked) {\n    this._checked = newIsChecked;\n    this.domNode.setAttribute('aria-checked', String(this._checked));\n    this.domNode.classList.toggle('checked', this._checked);\n    this.applyStyles();\n  }\n  width() {\n    return 2 /*margin left*/ + 2 /*border*/ + 2 /*padding*/ + 16 /* icon width */;\n  }\n  applyStyles() {\n    if (this.domNode) {\n      this.domNode.style.borderColor = this._checked && this._opts.inputActiveOptionBorder || '';\n      this.domNode.style.color = this._checked && this._opts.inputActiveOptionForeground || 'inherit';\n      this.domNode.style.backgroundColor = this._checked && this._opts.inputActiveOptionBackground || '';\n    }\n  }\n  enable() {\n    this.domNode.setAttribute('aria-disabled', String(false));\n  }\n  disable() {\n    this.domNode.setAttribute('aria-disabled', String(true));\n  }\n}", "map": {"version": 3, "names": ["Widget", "ThemeIcon", "Emitter", "getDefaultHoverDelegate", "getBaseLayerHoverDelegate", "unthemedToggleStyles", "inputActiveOptionBorder", "inputActiveOptionForeground", "inputActiveOptionBackground", "Toggle", "constructor", "opts", "_onChange", "_register", "onChange", "event", "_onKeyDown", "onKeyDown", "_opts", "_checked", "isChecked", "classes", "icon", "_icon", "push", "asClassNameArray", "actionClassName", "split", "domNode", "document", "createElement", "_hover", "setupManagedHover", "hoverDelegate", "title", "classList", "add", "notFocusable", "tabIndex", "setAttribute", "String", "applyStyles", "onclick", "ev", "enabled", "checked", "fire", "preventDefault", "ignoreGesture", "onkeydown", "keyboardEvent", "keyCode", "stopPropagation", "getAttribute", "focus", "newIsChecked", "toggle", "width", "style", "borderColor", "color", "backgroundColor", "enable", "disable"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/browser/ui/toggle/toggle.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Widget } from '../widget.js';\nimport { ThemeIcon } from '../../../common/themables.js';\nimport { Emitter } from '../../../common/event.js';\nimport './toggle.css';\nimport { getDefaultHoverDelegate } from '../hover/hoverDelegateFactory.js';\nimport { getBaseLayerHoverDelegate } from '../hover/hoverDelegate2.js';\nexport const unthemedToggleStyles = {\n    inputActiveOptionBorder: '#007ACC00',\n    inputActiveOptionForeground: '#FFFFFF',\n    inputActiveOptionBackground: '#0E639C50'\n};\nexport class Toggle extends Widget {\n    constructor(opts) {\n        super();\n        this._onChange = this._register(new Emitter());\n        this.onChange = this._onChange.event;\n        this._onKeyDown = this._register(new Emitter());\n        this.onKeyDown = this._onKeyDown.event;\n        this._opts = opts;\n        this._checked = this._opts.isChecked;\n        const classes = ['monaco-custom-toggle'];\n        if (this._opts.icon) {\n            this._icon = this._opts.icon;\n            classes.push(...ThemeIcon.asClassNameArray(this._icon));\n        }\n        if (this._opts.actionClassName) {\n            classes.push(...this._opts.actionClassName.split(' '));\n        }\n        if (this._checked) {\n            classes.push('checked');\n        }\n        this.domNode = document.createElement('div');\n        this._hover = this._register(getBaseLayerHoverDelegate().setupManagedHover(opts.hoverDelegate ?? getDefaultHoverDelegate('mouse'), this.domNode, this._opts.title));\n        this.domNode.classList.add(...classes);\n        if (!this._opts.notFocusable) {\n            this.domNode.tabIndex = 0;\n        }\n        this.domNode.setAttribute('role', 'checkbox');\n        this.domNode.setAttribute('aria-checked', String(this._checked));\n        this.domNode.setAttribute('aria-label', this._opts.title);\n        this.applyStyles();\n        this.onclick(this.domNode, (ev) => {\n            if (this.enabled) {\n                this.checked = !this._checked;\n                this._onChange.fire(false);\n                ev.preventDefault();\n            }\n        });\n        this._register(this.ignoreGesture(this.domNode));\n        this.onkeydown(this.domNode, (keyboardEvent) => {\n            if (keyboardEvent.keyCode === 10 /* KeyCode.Space */ || keyboardEvent.keyCode === 3 /* KeyCode.Enter */) {\n                this.checked = !this._checked;\n                this._onChange.fire(true);\n                keyboardEvent.preventDefault();\n                keyboardEvent.stopPropagation();\n                return;\n            }\n            this._onKeyDown.fire(keyboardEvent);\n        });\n    }\n    get enabled() {\n        return this.domNode.getAttribute('aria-disabled') !== 'true';\n    }\n    focus() {\n        this.domNode.focus();\n    }\n    get checked() {\n        return this._checked;\n    }\n    set checked(newIsChecked) {\n        this._checked = newIsChecked;\n        this.domNode.setAttribute('aria-checked', String(this._checked));\n        this.domNode.classList.toggle('checked', this._checked);\n        this.applyStyles();\n    }\n    width() {\n        return 2 /*margin left*/ + 2 /*border*/ + 2 /*padding*/ + 16 /* icon width */;\n    }\n    applyStyles() {\n        if (this.domNode) {\n            this.domNode.style.borderColor = (this._checked && this._opts.inputActiveOptionBorder) || '';\n            this.domNode.style.color = (this._checked && this._opts.inputActiveOptionForeground) || 'inherit';\n            this.domNode.style.backgroundColor = (this._checked && this._opts.inputActiveOptionBackground) || '';\n        }\n    }\n    enable() {\n        this.domNode.setAttribute('aria-disabled', String(false));\n    }\n    disable() {\n        this.domNode.setAttribute('aria-disabled', String(true));\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,MAAM,QAAQ,cAAc;AACrC,SAASC,SAAS,QAAQ,8BAA8B;AACxD,SAASC,OAAO,QAAQ,0BAA0B;AAClD,OAAO,cAAc;AACrB,SAASC,uBAAuB,QAAQ,kCAAkC;AAC1E,SAASC,yBAAyB,QAAQ,4BAA4B;AACtE,OAAO,MAAMC,oBAAoB,GAAG;EAChCC,uBAAuB,EAAE,WAAW;EACpCC,2BAA2B,EAAE,SAAS;EACtCC,2BAA2B,EAAE;AACjC,CAAC;AACD,OAAO,MAAMC,MAAM,SAAST,MAAM,CAAC;EAC/BU,WAAWA,CAACC,IAAI,EAAE;IACd,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,SAAS,GAAG,IAAI,CAACC,SAAS,CAAC,IAAIX,OAAO,CAAC,CAAC,CAAC;IAC9C,IAAI,CAACY,QAAQ,GAAG,IAAI,CAACF,SAAS,CAACG,KAAK;IACpC,IAAI,CAACC,UAAU,GAAG,IAAI,CAACH,SAAS,CAAC,IAAIX,OAAO,CAAC,CAAC,CAAC;IAC/C,IAAI,CAACe,SAAS,GAAG,IAAI,CAACD,UAAU,CAACD,KAAK;IACtC,IAAI,CAACG,KAAK,GAAGP,IAAI;IACjB,IAAI,CAACQ,QAAQ,GAAG,IAAI,CAACD,KAAK,CAACE,SAAS;IACpC,MAAMC,OAAO,GAAG,CAAC,sBAAsB,CAAC;IACxC,IAAI,IAAI,CAACH,KAAK,CAACI,IAAI,EAAE;MACjB,IAAI,CAACC,KAAK,GAAG,IAAI,CAACL,KAAK,CAACI,IAAI;MAC5BD,OAAO,CAACG,IAAI,CAAC,GAAGvB,SAAS,CAACwB,gBAAgB,CAAC,IAAI,CAACF,KAAK,CAAC,CAAC;IAC3D;IACA,IAAI,IAAI,CAACL,KAAK,CAACQ,eAAe,EAAE;MAC5BL,OAAO,CAACG,IAAI,CAAC,GAAG,IAAI,CAACN,KAAK,CAACQ,eAAe,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC1D;IACA,IAAI,IAAI,CAACR,QAAQ,EAAE;MACfE,OAAO,CAACG,IAAI,CAAC,SAAS,CAAC;IAC3B;IACA,IAAI,CAACI,OAAO,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC5C,IAAI,CAACC,MAAM,GAAG,IAAI,CAAClB,SAAS,CAACT,yBAAyB,CAAC,CAAC,CAAC4B,iBAAiB,CAACrB,IAAI,CAACsB,aAAa,IAAI9B,uBAAuB,CAAC,OAAO,CAAC,EAAE,IAAI,CAACyB,OAAO,EAAE,IAAI,CAACV,KAAK,CAACgB,KAAK,CAAC,CAAC;IACnK,IAAI,CAACN,OAAO,CAACO,SAAS,CAACC,GAAG,CAAC,GAAGf,OAAO,CAAC;IACtC,IAAI,CAAC,IAAI,CAACH,KAAK,CAACmB,YAAY,EAAE;MAC1B,IAAI,CAACT,OAAO,CAACU,QAAQ,GAAG,CAAC;IAC7B;IACA,IAAI,CAACV,OAAO,CAACW,YAAY,CAAC,MAAM,EAAE,UAAU,CAAC;IAC7C,IAAI,CAACX,OAAO,CAACW,YAAY,CAAC,cAAc,EAAEC,MAAM,CAAC,IAAI,CAACrB,QAAQ,CAAC,CAAC;IAChE,IAAI,CAACS,OAAO,CAACW,YAAY,CAAC,YAAY,EAAE,IAAI,CAACrB,KAAK,CAACgB,KAAK,CAAC;IACzD,IAAI,CAACO,WAAW,CAAC,CAAC;IAClB,IAAI,CAACC,OAAO,CAAC,IAAI,CAACd,OAAO,EAAGe,EAAE,IAAK;MAC/B,IAAI,IAAI,CAACC,OAAO,EAAE;QACd,IAAI,CAACC,OAAO,GAAG,CAAC,IAAI,CAAC1B,QAAQ;QAC7B,IAAI,CAACP,SAAS,CAACkC,IAAI,CAAC,KAAK,CAAC;QAC1BH,EAAE,CAACI,cAAc,CAAC,CAAC;MACvB;IACJ,CAAC,CAAC;IACF,IAAI,CAAClC,SAAS,CAAC,IAAI,CAACmC,aAAa,CAAC,IAAI,CAACpB,OAAO,CAAC,CAAC;IAChD,IAAI,CAACqB,SAAS,CAAC,IAAI,CAACrB,OAAO,EAAGsB,aAAa,IAAK;MAC5C,IAAIA,aAAa,CAACC,OAAO,KAAK,EAAE,CAAC,uBAAuBD,aAAa,CAACC,OAAO,KAAK,CAAC,CAAC,qBAAqB;QACrG,IAAI,CAACN,OAAO,GAAG,CAAC,IAAI,CAAC1B,QAAQ;QAC7B,IAAI,CAACP,SAAS,CAACkC,IAAI,CAAC,IAAI,CAAC;QACzBI,aAAa,CAACH,cAAc,CAAC,CAAC;QAC9BG,aAAa,CAACE,eAAe,CAAC,CAAC;QAC/B;MACJ;MACA,IAAI,CAACpC,UAAU,CAAC8B,IAAI,CAACI,aAAa,CAAC;IACvC,CAAC,CAAC;EACN;EACA,IAAIN,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAAChB,OAAO,CAACyB,YAAY,CAAC,eAAe,CAAC,KAAK,MAAM;EAChE;EACAC,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAC1B,OAAO,CAAC0B,KAAK,CAAC,CAAC;EACxB;EACA,IAAIT,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC1B,QAAQ;EACxB;EACA,IAAI0B,OAAOA,CAACU,YAAY,EAAE;IACtB,IAAI,CAACpC,QAAQ,GAAGoC,YAAY;IAC5B,IAAI,CAAC3B,OAAO,CAACW,YAAY,CAAC,cAAc,EAAEC,MAAM,CAAC,IAAI,CAACrB,QAAQ,CAAC,CAAC;IAChE,IAAI,CAACS,OAAO,CAACO,SAAS,CAACqB,MAAM,CAAC,SAAS,EAAE,IAAI,CAACrC,QAAQ,CAAC;IACvD,IAAI,CAACsB,WAAW,CAAC,CAAC;EACtB;EACAgB,KAAKA,CAAA,EAAG;IACJ,OAAO,CAAC,CAAC,kBAAkB,CAAC,CAAC,aAAa,CAAC,CAAC,cAAc,EAAE,CAAC;EACjE;EACAhB,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACb,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAAC8B,KAAK,CAACC,WAAW,GAAI,IAAI,CAACxC,QAAQ,IAAI,IAAI,CAACD,KAAK,CAACZ,uBAAuB,IAAK,EAAE;MAC5F,IAAI,CAACsB,OAAO,CAAC8B,KAAK,CAACE,KAAK,GAAI,IAAI,CAACzC,QAAQ,IAAI,IAAI,CAACD,KAAK,CAACX,2BAA2B,IAAK,SAAS;MACjG,IAAI,CAACqB,OAAO,CAAC8B,KAAK,CAACG,eAAe,GAAI,IAAI,CAAC1C,QAAQ,IAAI,IAAI,CAACD,KAAK,CAACV,2BAA2B,IAAK,EAAE;IACxG;EACJ;EACAsD,MAAMA,CAAA,EAAG;IACL,IAAI,CAAClC,OAAO,CAACW,YAAY,CAAC,eAAe,EAAEC,MAAM,CAAC,KAAK,CAAC,CAAC;EAC7D;EACAuB,OAAOA,CAAA,EAAG;IACN,IAAI,CAACnC,OAAO,CAACW,YAAY,CAAC,eAAe,EAAEC,MAAM,CAAC,IAAI,CAAC,CAAC;EAC5D;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}