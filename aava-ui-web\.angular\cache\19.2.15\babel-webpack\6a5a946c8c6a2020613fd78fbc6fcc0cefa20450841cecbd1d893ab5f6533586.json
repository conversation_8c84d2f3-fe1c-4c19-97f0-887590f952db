{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport class ColorZone {\n  constructor(from, to, colorId) {\n    this._colorZoneBrand = undefined;\n    this.from = from | 0;\n    this.to = to | 0;\n    this.colorId = colorId | 0;\n  }\n  static compare(a, b) {\n    if (a.colorId === b.colorId) {\n      if (a.from === b.from) {\n        return a.to - b.to;\n      }\n      return a.from - b.from;\n    }\n    return a.colorId - b.colorId;\n  }\n}\n/**\n * A zone in the overview ruler\n */\nexport class OverviewRulerZone {\n  constructor(startLineNumber, endLineNumber, heightInLines, color) {\n    this._overviewRulerZoneBrand = undefined;\n    this.startLineNumber = startLineNumber;\n    this.endLineNumber = endLineNumber;\n    this.heightInLines = heightInLines;\n    this.color = color;\n    this._colorZone = null;\n  }\n  static compare(a, b) {\n    if (a.color === b.color) {\n      if (a.startLineNumber === b.startLineNumber) {\n        if (a.heightInLines === b.heightInLines) {\n          return a.endLineNumber - b.endLineNumber;\n        }\n        return a.heightInLines - b.heightInLines;\n      }\n      return a.startLineNumber - b.startLineNumber;\n    }\n    return a.color < b.color ? -1 : 1;\n  }\n  setColorZone(colorZone) {\n    this._colorZone = colorZone;\n  }\n  getColorZones() {\n    return this._colorZone;\n  }\n}\nexport class OverviewZoneManager {\n  constructor(getVerticalOffsetForLine) {\n    this._getVerticalOffsetForLine = getVerticalOffsetForLine;\n    this._zones = [];\n    this._colorZonesInvalid = false;\n    this._lineHeight = 0;\n    this._domWidth = 0;\n    this._domHeight = 0;\n    this._outerHeight = 0;\n    this._pixelRatio = 1;\n    this._lastAssignedId = 0;\n    this._color2Id = Object.create(null);\n    this._id2Color = [];\n  }\n  getId2Color() {\n    return this._id2Color;\n  }\n  setZones(newZones) {\n    this._zones = newZones;\n    this._zones.sort(OverviewRulerZone.compare);\n  }\n  setLineHeight(lineHeight) {\n    if (this._lineHeight === lineHeight) {\n      return false;\n    }\n    this._lineHeight = lineHeight;\n    this._colorZonesInvalid = true;\n    return true;\n  }\n  setPixelRatio(pixelRatio) {\n    this._pixelRatio = pixelRatio;\n    this._colorZonesInvalid = true;\n  }\n  getDOMWidth() {\n    return this._domWidth;\n  }\n  getCanvasWidth() {\n    return this._domWidth * this._pixelRatio;\n  }\n  setDOMWidth(width) {\n    if (this._domWidth === width) {\n      return false;\n    }\n    this._domWidth = width;\n    this._colorZonesInvalid = true;\n    return true;\n  }\n  getDOMHeight() {\n    return this._domHeight;\n  }\n  getCanvasHeight() {\n    return this._domHeight * this._pixelRatio;\n  }\n  setDOMHeight(height) {\n    if (this._domHeight === height) {\n      return false;\n    }\n    this._domHeight = height;\n    this._colorZonesInvalid = true;\n    return true;\n  }\n  getOuterHeight() {\n    return this._outerHeight;\n  }\n  setOuterHeight(outerHeight) {\n    if (this._outerHeight === outerHeight) {\n      return false;\n    }\n    this._outerHeight = outerHeight;\n    this._colorZonesInvalid = true;\n    return true;\n  }\n  resolveColorZones() {\n    const colorZonesInvalid = this._colorZonesInvalid;\n    const lineHeight = Math.floor(this._lineHeight);\n    const totalHeight = Math.floor(this.getCanvasHeight());\n    const outerHeight = Math.floor(this._outerHeight);\n    const heightRatio = totalHeight / outerHeight;\n    const halfMinimumHeight = Math.floor(4 /* Constants.MINIMUM_HEIGHT */ * this._pixelRatio / 2);\n    const allColorZones = [];\n    for (let i = 0, len = this._zones.length; i < len; i++) {\n      const zone = this._zones[i];\n      if (!colorZonesInvalid) {\n        const colorZone = zone.getColorZones();\n        if (colorZone) {\n          allColorZones.push(colorZone);\n          continue;\n        }\n      }\n      const offset1 = this._getVerticalOffsetForLine(zone.startLineNumber);\n      const offset2 = zone.heightInLines === 0 ? this._getVerticalOffsetForLine(zone.endLineNumber) + lineHeight : offset1 + zone.heightInLines * lineHeight;\n      const y1 = Math.floor(heightRatio * offset1);\n      const y2 = Math.floor(heightRatio * offset2);\n      let ycenter = Math.floor((y1 + y2) / 2);\n      let halfHeight = y2 - ycenter;\n      if (halfHeight < halfMinimumHeight) {\n        halfHeight = halfMinimumHeight;\n      }\n      if (ycenter - halfHeight < 0) {\n        ycenter = halfHeight;\n      }\n      if (ycenter + halfHeight > totalHeight) {\n        ycenter = totalHeight - halfHeight;\n      }\n      const color = zone.color;\n      let colorId = this._color2Id[color];\n      if (!colorId) {\n        colorId = ++this._lastAssignedId;\n        this._color2Id[color] = colorId;\n        this._id2Color[colorId] = color;\n      }\n      const colorZone = new ColorZone(ycenter - halfHeight, ycenter + halfHeight, colorId);\n      zone.setColorZone(colorZone);\n      allColorZones.push(colorZone);\n    }\n    this._colorZonesInvalid = false;\n    allColorZones.sort(ColorZone.compare);\n    return allColorZones;\n  }\n}", "map": {"version": 3, "names": ["ColorZone", "constructor", "from", "to", "colorId", "_colorZoneBrand", "undefined", "compare", "a", "b", "OverviewRulerZone", "startLineNumber", "endLineNumber", "heightInLines", "color", "_overviewRulerZoneBrand", "_colorZone", "setColorZone", "colorZone", "getColorZones", "OverviewZoneManager", "getVerticalOffsetForLine", "_getVerticalOffsetForLine", "_zones", "_colorZonesInvalid", "_lineHeight", "_dom<PERSON><PERSON>th", "_domHeight", "_outerHeight", "_pixelRatio", "_lastAssignedId", "_color2Id", "Object", "create", "_id2Color", "getId2Color", "setZones", "newZones", "sort", "setLineHeight", "lineHeight", "setPixelRatio", "pixelRatio", "getDOMWidth", "getCanvasWidth", "setDOMWidth", "width", "getDOMHeight", "getCanvasHeight", "setDOMHeight", "height", "getOuterHeight", "setOuterHeight", "outerHeight", "resolveColorZones", "colorZonesInvalid", "Math", "floor", "totalHeight", "heightRatio", "halfMinimumHeight", "allColorZones", "i", "len", "length", "zone", "push", "offset1", "offset2", "y1", "y2", "ycenter", "halfHeight"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/viewModel/overviewZoneManager.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport class ColorZone {\n    constructor(from, to, colorId) {\n        this._colorZoneBrand = undefined;\n        this.from = from | 0;\n        this.to = to | 0;\n        this.colorId = colorId | 0;\n    }\n    static compare(a, b) {\n        if (a.colorId === b.colorId) {\n            if (a.from === b.from) {\n                return a.to - b.to;\n            }\n            return a.from - b.from;\n        }\n        return a.colorId - b.colorId;\n    }\n}\n/**\n * A zone in the overview ruler\n */\nexport class OverviewRulerZone {\n    constructor(startLineNumber, endLineNumber, heightInLines, color) {\n        this._overviewRulerZoneBrand = undefined;\n        this.startLineNumber = startLineNumber;\n        this.endLineNumber = endLineNumber;\n        this.heightInLines = heightInLines;\n        this.color = color;\n        this._colorZone = null;\n    }\n    static compare(a, b) {\n        if (a.color === b.color) {\n            if (a.startLineNumber === b.startLineNumber) {\n                if (a.heightInLines === b.heightInLines) {\n                    return a.endLineNumber - b.endLineNumber;\n                }\n                return a.heightInLines - b.heightInLines;\n            }\n            return a.startLineNumber - b.startLineNumber;\n        }\n        return a.color < b.color ? -1 : 1;\n    }\n    setColorZone(colorZone) {\n        this._colorZone = colorZone;\n    }\n    getColorZones() {\n        return this._colorZone;\n    }\n}\nexport class OverviewZoneManager {\n    constructor(getVerticalOffsetForLine) {\n        this._getVerticalOffsetForLine = getVerticalOffsetForLine;\n        this._zones = [];\n        this._colorZonesInvalid = false;\n        this._lineHeight = 0;\n        this._domWidth = 0;\n        this._domHeight = 0;\n        this._outerHeight = 0;\n        this._pixelRatio = 1;\n        this._lastAssignedId = 0;\n        this._color2Id = Object.create(null);\n        this._id2Color = [];\n    }\n    getId2Color() {\n        return this._id2Color;\n    }\n    setZones(newZones) {\n        this._zones = newZones;\n        this._zones.sort(OverviewRulerZone.compare);\n    }\n    setLineHeight(lineHeight) {\n        if (this._lineHeight === lineHeight) {\n            return false;\n        }\n        this._lineHeight = lineHeight;\n        this._colorZonesInvalid = true;\n        return true;\n    }\n    setPixelRatio(pixelRatio) {\n        this._pixelRatio = pixelRatio;\n        this._colorZonesInvalid = true;\n    }\n    getDOMWidth() {\n        return this._domWidth;\n    }\n    getCanvasWidth() {\n        return this._domWidth * this._pixelRatio;\n    }\n    setDOMWidth(width) {\n        if (this._domWidth === width) {\n            return false;\n        }\n        this._domWidth = width;\n        this._colorZonesInvalid = true;\n        return true;\n    }\n    getDOMHeight() {\n        return this._domHeight;\n    }\n    getCanvasHeight() {\n        return this._domHeight * this._pixelRatio;\n    }\n    setDOMHeight(height) {\n        if (this._domHeight === height) {\n            return false;\n        }\n        this._domHeight = height;\n        this._colorZonesInvalid = true;\n        return true;\n    }\n    getOuterHeight() {\n        return this._outerHeight;\n    }\n    setOuterHeight(outerHeight) {\n        if (this._outerHeight === outerHeight) {\n            return false;\n        }\n        this._outerHeight = outerHeight;\n        this._colorZonesInvalid = true;\n        return true;\n    }\n    resolveColorZones() {\n        const colorZonesInvalid = this._colorZonesInvalid;\n        const lineHeight = Math.floor(this._lineHeight);\n        const totalHeight = Math.floor(this.getCanvasHeight());\n        const outerHeight = Math.floor(this._outerHeight);\n        const heightRatio = totalHeight / outerHeight;\n        const halfMinimumHeight = Math.floor(4 /* Constants.MINIMUM_HEIGHT */ * this._pixelRatio / 2);\n        const allColorZones = [];\n        for (let i = 0, len = this._zones.length; i < len; i++) {\n            const zone = this._zones[i];\n            if (!colorZonesInvalid) {\n                const colorZone = zone.getColorZones();\n                if (colorZone) {\n                    allColorZones.push(colorZone);\n                    continue;\n                }\n            }\n            const offset1 = this._getVerticalOffsetForLine(zone.startLineNumber);\n            const offset2 = (zone.heightInLines === 0\n                ? this._getVerticalOffsetForLine(zone.endLineNumber) + lineHeight\n                : offset1 + zone.heightInLines * lineHeight);\n            const y1 = Math.floor(heightRatio * offset1);\n            const y2 = Math.floor(heightRatio * offset2);\n            let ycenter = Math.floor((y1 + y2) / 2);\n            let halfHeight = (y2 - ycenter);\n            if (halfHeight < halfMinimumHeight) {\n                halfHeight = halfMinimumHeight;\n            }\n            if (ycenter - halfHeight < 0) {\n                ycenter = halfHeight;\n            }\n            if (ycenter + halfHeight > totalHeight) {\n                ycenter = totalHeight - halfHeight;\n            }\n            const color = zone.color;\n            let colorId = this._color2Id[color];\n            if (!colorId) {\n                colorId = (++this._lastAssignedId);\n                this._color2Id[color] = colorId;\n                this._id2Color[colorId] = color;\n            }\n            const colorZone = new ColorZone(ycenter - halfHeight, ycenter + halfHeight, colorId);\n            zone.setColorZone(colorZone);\n            allColorZones.push(colorZone);\n        }\n        this._colorZonesInvalid = false;\n        allColorZones.sort(ColorZone.compare);\n        return allColorZones;\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,MAAMA,SAAS,CAAC;EACnBC,WAAWA,CAACC,IAAI,EAAEC,EAAE,EAAEC,OAAO,EAAE;IAC3B,IAAI,CAACC,eAAe,GAAGC,SAAS;IAChC,IAAI,CAACJ,IAAI,GAAGA,IAAI,GAAG,CAAC;IACpB,IAAI,CAACC,EAAE,GAAGA,EAAE,GAAG,CAAC;IAChB,IAAI,CAACC,OAAO,GAAGA,OAAO,GAAG,CAAC;EAC9B;EACA,OAAOG,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;IACjB,IAAID,CAAC,CAACJ,OAAO,KAAKK,CAAC,CAACL,OAAO,EAAE;MACzB,IAAII,CAAC,CAACN,IAAI,KAAKO,CAAC,CAACP,IAAI,EAAE;QACnB,OAAOM,CAAC,CAACL,EAAE,GAAGM,CAAC,CAACN,EAAE;MACtB;MACA,OAAOK,CAAC,CAACN,IAAI,GAAGO,CAAC,CAACP,IAAI;IAC1B;IACA,OAAOM,CAAC,CAACJ,OAAO,GAAGK,CAAC,CAACL,OAAO;EAChC;AACJ;AACA;AACA;AACA;AACA,OAAO,MAAMM,iBAAiB,CAAC;EAC3BT,WAAWA,CAACU,eAAe,EAAEC,aAAa,EAAEC,aAAa,EAAEC,KAAK,EAAE;IAC9D,IAAI,CAACC,uBAAuB,GAAGT,SAAS;IACxC,IAAI,CAACK,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACE,UAAU,GAAG,IAAI;EAC1B;EACA,OAAOT,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;IACjB,IAAID,CAAC,CAACM,KAAK,KAAKL,CAAC,CAACK,KAAK,EAAE;MACrB,IAAIN,CAAC,CAACG,eAAe,KAAKF,CAAC,CAACE,eAAe,EAAE;QACzC,IAAIH,CAAC,CAACK,aAAa,KAAKJ,CAAC,CAACI,aAAa,EAAE;UACrC,OAAOL,CAAC,CAACI,aAAa,GAAGH,CAAC,CAACG,aAAa;QAC5C;QACA,OAAOJ,CAAC,CAACK,aAAa,GAAGJ,CAAC,CAACI,aAAa;MAC5C;MACA,OAAOL,CAAC,CAACG,eAAe,GAAGF,CAAC,CAACE,eAAe;IAChD;IACA,OAAOH,CAAC,CAACM,KAAK,GAAGL,CAAC,CAACK,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC;EACrC;EACAG,YAAYA,CAACC,SAAS,EAAE;IACpB,IAAI,CAACF,UAAU,GAAGE,SAAS;EAC/B;EACAC,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACH,UAAU;EAC1B;AACJ;AACA,OAAO,MAAMI,mBAAmB,CAAC;EAC7BnB,WAAWA,CAACoB,wBAAwB,EAAE;IAClC,IAAI,CAACC,yBAAyB,GAAGD,wBAAwB;IACzD,IAAI,CAACE,MAAM,GAAG,EAAE;IAChB,IAAI,CAACC,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACC,WAAW,GAAG,CAAC;IACpB,IAAI,CAACC,SAAS,GAAG,CAAC;IAClB,IAAI,CAACC,UAAU,GAAG,CAAC;IACnB,IAAI,CAACC,YAAY,GAAG,CAAC;IACrB,IAAI,CAACC,WAAW,GAAG,CAAC;IACpB,IAAI,CAACC,eAAe,GAAG,CAAC;IACxB,IAAI,CAACC,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IACpC,IAAI,CAACC,SAAS,GAAG,EAAE;EACvB;EACAC,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACD,SAAS;EACzB;EACAE,QAAQA,CAACC,QAAQ,EAAE;IACf,IAAI,CAACd,MAAM,GAAGc,QAAQ;IACtB,IAAI,CAACd,MAAM,CAACe,IAAI,CAAC5B,iBAAiB,CAACH,OAAO,CAAC;EAC/C;EACAgC,aAAaA,CAACC,UAAU,EAAE;IACtB,IAAI,IAAI,CAACf,WAAW,KAAKe,UAAU,EAAE;MACjC,OAAO,KAAK;IAChB;IACA,IAAI,CAACf,WAAW,GAAGe,UAAU;IAC7B,IAAI,CAAChB,kBAAkB,GAAG,IAAI;IAC9B,OAAO,IAAI;EACf;EACAiB,aAAaA,CAACC,UAAU,EAAE;IACtB,IAAI,CAACb,WAAW,GAAGa,UAAU;IAC7B,IAAI,CAAClB,kBAAkB,GAAG,IAAI;EAClC;EACAmB,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACjB,SAAS;EACzB;EACAkB,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAAClB,SAAS,GAAG,IAAI,CAACG,WAAW;EAC5C;EACAgB,WAAWA,CAACC,KAAK,EAAE;IACf,IAAI,IAAI,CAACpB,SAAS,KAAKoB,KAAK,EAAE;MAC1B,OAAO,KAAK;IAChB;IACA,IAAI,CAACpB,SAAS,GAAGoB,KAAK;IACtB,IAAI,CAACtB,kBAAkB,GAAG,IAAI;IAC9B,OAAO,IAAI;EACf;EACAuB,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAACpB,UAAU;EAC1B;EACAqB,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAACrB,UAAU,GAAG,IAAI,CAACE,WAAW;EAC7C;EACAoB,YAAYA,CAACC,MAAM,EAAE;IACjB,IAAI,IAAI,CAACvB,UAAU,KAAKuB,MAAM,EAAE;MAC5B,OAAO,KAAK;IAChB;IACA,IAAI,CAACvB,UAAU,GAAGuB,MAAM;IACxB,IAAI,CAAC1B,kBAAkB,GAAG,IAAI;IAC9B,OAAO,IAAI;EACf;EACA2B,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAACvB,YAAY;EAC5B;EACAwB,cAAcA,CAACC,WAAW,EAAE;IACxB,IAAI,IAAI,CAACzB,YAAY,KAAKyB,WAAW,EAAE;MACnC,OAAO,KAAK;IAChB;IACA,IAAI,CAACzB,YAAY,GAAGyB,WAAW;IAC/B,IAAI,CAAC7B,kBAAkB,GAAG,IAAI;IAC9B,OAAO,IAAI;EACf;EACA8B,iBAAiBA,CAAA,EAAG;IAChB,MAAMC,iBAAiB,GAAG,IAAI,CAAC/B,kBAAkB;IACjD,MAAMgB,UAAU,GAAGgB,IAAI,CAACC,KAAK,CAAC,IAAI,CAAChC,WAAW,CAAC;IAC/C,MAAMiC,WAAW,GAAGF,IAAI,CAACC,KAAK,CAAC,IAAI,CAACT,eAAe,CAAC,CAAC,CAAC;IACtD,MAAMK,WAAW,GAAGG,IAAI,CAACC,KAAK,CAAC,IAAI,CAAC7B,YAAY,CAAC;IACjD,MAAM+B,WAAW,GAAGD,WAAW,GAAGL,WAAW;IAC7C,MAAMO,iBAAiB,GAAGJ,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC,iCAAiC,IAAI,CAAC5B,WAAW,GAAG,CAAC,CAAC;IAC7F,MAAMgC,aAAa,GAAG,EAAE;IACxB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAG,IAAI,CAACxC,MAAM,CAACyC,MAAM,EAAEF,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;MACpD,MAAMG,IAAI,GAAG,IAAI,CAAC1C,MAAM,CAACuC,CAAC,CAAC;MAC3B,IAAI,CAACP,iBAAiB,EAAE;QACpB,MAAMrC,SAAS,GAAG+C,IAAI,CAAC9C,aAAa,CAAC,CAAC;QACtC,IAAID,SAAS,EAAE;UACX2C,aAAa,CAACK,IAAI,CAAChD,SAAS,CAAC;UAC7B;QACJ;MACJ;MACA,MAAMiD,OAAO,GAAG,IAAI,CAAC7C,yBAAyB,CAAC2C,IAAI,CAACtD,eAAe,CAAC;MACpE,MAAMyD,OAAO,GAAIH,IAAI,CAACpD,aAAa,KAAK,CAAC,GACnC,IAAI,CAACS,yBAAyB,CAAC2C,IAAI,CAACrD,aAAa,CAAC,GAAG4B,UAAU,GAC/D2B,OAAO,GAAGF,IAAI,CAACpD,aAAa,GAAG2B,UAAW;MAChD,MAAM6B,EAAE,GAAGb,IAAI,CAACC,KAAK,CAACE,WAAW,GAAGQ,OAAO,CAAC;MAC5C,MAAMG,EAAE,GAAGd,IAAI,CAACC,KAAK,CAACE,WAAW,GAAGS,OAAO,CAAC;MAC5C,IAAIG,OAAO,GAAGf,IAAI,CAACC,KAAK,CAAC,CAACY,EAAE,GAAGC,EAAE,IAAI,CAAC,CAAC;MACvC,IAAIE,UAAU,GAAIF,EAAE,GAAGC,OAAQ;MAC/B,IAAIC,UAAU,GAAGZ,iBAAiB,EAAE;QAChCY,UAAU,GAAGZ,iBAAiB;MAClC;MACA,IAAIW,OAAO,GAAGC,UAAU,GAAG,CAAC,EAAE;QAC1BD,OAAO,GAAGC,UAAU;MACxB;MACA,IAAID,OAAO,GAAGC,UAAU,GAAGd,WAAW,EAAE;QACpCa,OAAO,GAAGb,WAAW,GAAGc,UAAU;MACtC;MACA,MAAM1D,KAAK,GAAGmD,IAAI,CAACnD,KAAK;MACxB,IAAIV,OAAO,GAAG,IAAI,CAAC2B,SAAS,CAACjB,KAAK,CAAC;MACnC,IAAI,CAACV,OAAO,EAAE;QACVA,OAAO,GAAI,EAAE,IAAI,CAAC0B,eAAgB;QAClC,IAAI,CAACC,SAAS,CAACjB,KAAK,CAAC,GAAGV,OAAO;QAC/B,IAAI,CAAC8B,SAAS,CAAC9B,OAAO,CAAC,GAAGU,KAAK;MACnC;MACA,MAAMI,SAAS,GAAG,IAAIlB,SAAS,CAACuE,OAAO,GAAGC,UAAU,EAAED,OAAO,GAAGC,UAAU,EAAEpE,OAAO,CAAC;MACpF6D,IAAI,CAAChD,YAAY,CAACC,SAAS,CAAC;MAC5B2C,aAAa,CAACK,IAAI,CAAChD,SAAS,CAAC;IACjC;IACA,IAAI,CAACM,kBAAkB,GAAG,KAAK;IAC/BqC,aAAa,CAACvB,IAAI,CAACtC,SAAS,CAACO,OAAO,CAAC;IACrC,OAAOsD,aAAa;EACxB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}