{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Range } from '../../../common/core/range.js';\nimport { OverviewRulerLane } from '../../../common/model.js';\nimport { ModelDecorationOptions } from '../../../common/model/textModel.js';\nimport { minimapFindMatch, overviewRulerFindMatchForeground } from '../../../../platform/theme/common/colorRegistry.js';\nimport { themeColorFromId } from '../../../../platform/theme/common/themeService.js';\nexport class FindDecorations {\n  constructor(editor) {\n    this._editor = editor;\n    this._decorations = [];\n    this._overviewRulerApproximateDecorations = [];\n    this._findScopeDecorationIds = [];\n    this._rangeHighlightDecorationId = null;\n    this._highlightedDecorationId = null;\n    this._startPosition = this._editor.getPosition();\n  }\n  dispose() {\n    this._editor.removeDecorations(this._allDecorations());\n    this._decorations = [];\n    this._overviewRulerApproximateDecorations = [];\n    this._findScopeDecorationIds = [];\n    this._rangeHighlightDecorationId = null;\n    this._highlightedDecorationId = null;\n  }\n  reset() {\n    this._decorations = [];\n    this._overviewRulerApproximateDecorations = [];\n    this._findScopeDecorationIds = [];\n    this._rangeHighlightDecorationId = null;\n    this._highlightedDecorationId = null;\n  }\n  getCount() {\n    return this._decorations.length;\n  }\n  /** @deprecated use getFindScopes to support multiple selections */\n  getFindScope() {\n    if (this._findScopeDecorationIds[0]) {\n      return this._editor.getModel().getDecorationRange(this._findScopeDecorationIds[0]);\n    }\n    return null;\n  }\n  getFindScopes() {\n    if (this._findScopeDecorationIds.length) {\n      const scopes = this._findScopeDecorationIds.map(findScopeDecorationId => this._editor.getModel().getDecorationRange(findScopeDecorationId)).filter(element => !!element);\n      if (scopes.length) {\n        return scopes;\n      }\n    }\n    return null;\n  }\n  getStartPosition() {\n    return this._startPosition;\n  }\n  setStartPosition(newStartPosition) {\n    this._startPosition = newStartPosition;\n    this.setCurrentFindMatch(null);\n  }\n  _getDecorationIndex(decorationId) {\n    const index = this._decorations.indexOf(decorationId);\n    if (index >= 0) {\n      return index + 1;\n    }\n    return 1;\n  }\n  getDecorationRangeAt(index) {\n    const decorationId = index < this._decorations.length ? this._decorations[index] : null;\n    if (decorationId) {\n      return this._editor.getModel().getDecorationRange(decorationId);\n    }\n    return null;\n  }\n  getCurrentMatchesPosition(desiredRange) {\n    const candidates = this._editor.getModel().getDecorationsInRange(desiredRange);\n    for (const candidate of candidates) {\n      const candidateOpts = candidate.options;\n      if (candidateOpts === FindDecorations._FIND_MATCH_DECORATION || candidateOpts === FindDecorations._CURRENT_FIND_MATCH_DECORATION) {\n        return this._getDecorationIndex(candidate.id);\n      }\n    }\n    // We don't know the current match position, so returns zero to show '?' in find widget\n    return 0;\n  }\n  setCurrentFindMatch(nextMatch) {\n    let newCurrentDecorationId = null;\n    let matchPosition = 0;\n    if (nextMatch) {\n      for (let i = 0, len = this._decorations.length; i < len; i++) {\n        const range = this._editor.getModel().getDecorationRange(this._decorations[i]);\n        if (nextMatch.equalsRange(range)) {\n          newCurrentDecorationId = this._decorations[i];\n          matchPosition = i + 1;\n          break;\n        }\n      }\n    }\n    if (this._highlightedDecorationId !== null || newCurrentDecorationId !== null) {\n      this._editor.changeDecorations(changeAccessor => {\n        if (this._highlightedDecorationId !== null) {\n          changeAccessor.changeDecorationOptions(this._highlightedDecorationId, FindDecorations._FIND_MATCH_DECORATION);\n          this._highlightedDecorationId = null;\n        }\n        if (newCurrentDecorationId !== null) {\n          this._highlightedDecorationId = newCurrentDecorationId;\n          changeAccessor.changeDecorationOptions(this._highlightedDecorationId, FindDecorations._CURRENT_FIND_MATCH_DECORATION);\n        }\n        if (this._rangeHighlightDecorationId !== null) {\n          changeAccessor.removeDecoration(this._rangeHighlightDecorationId);\n          this._rangeHighlightDecorationId = null;\n        }\n        if (newCurrentDecorationId !== null) {\n          let rng = this._editor.getModel().getDecorationRange(newCurrentDecorationId);\n          if (rng.startLineNumber !== rng.endLineNumber && rng.endColumn === 1) {\n            const lineBeforeEnd = rng.endLineNumber - 1;\n            const lineBeforeEndMaxColumn = this._editor.getModel().getLineMaxColumn(lineBeforeEnd);\n            rng = new Range(rng.startLineNumber, rng.startColumn, lineBeforeEnd, lineBeforeEndMaxColumn);\n          }\n          this._rangeHighlightDecorationId = changeAccessor.addDecoration(rng, FindDecorations._RANGE_HIGHLIGHT_DECORATION);\n        }\n      });\n    }\n    return matchPosition;\n  }\n  set(findMatches, findScopes) {\n    this._editor.changeDecorations(accessor => {\n      let findMatchesOptions = FindDecorations._FIND_MATCH_DECORATION;\n      const newOverviewRulerApproximateDecorations = [];\n      if (findMatches.length > 1000) {\n        // we go into a mode where the overview ruler gets \"approximate\" decorations\n        // the reason is that the overview ruler paints all the decorations in the file and we don't want to cause freezes\n        findMatchesOptions = FindDecorations._FIND_MATCH_NO_OVERVIEW_DECORATION;\n        // approximate a distance in lines where matches should be merged\n        const lineCount = this._editor.getModel().getLineCount();\n        const height = this._editor.getLayoutInfo().height;\n        const approxPixelsPerLine = height / lineCount;\n        const mergeLinesDelta = Math.max(2, Math.ceil(3 / approxPixelsPerLine));\n        // merge decorations as much as possible\n        let prevStartLineNumber = findMatches[0].range.startLineNumber;\n        let prevEndLineNumber = findMatches[0].range.endLineNumber;\n        for (let i = 1, len = findMatches.length; i < len; i++) {\n          const range = findMatches[i].range;\n          if (prevEndLineNumber + mergeLinesDelta >= range.startLineNumber) {\n            if (range.endLineNumber > prevEndLineNumber) {\n              prevEndLineNumber = range.endLineNumber;\n            }\n          } else {\n            newOverviewRulerApproximateDecorations.push({\n              range: new Range(prevStartLineNumber, 1, prevEndLineNumber, 1),\n              options: FindDecorations._FIND_MATCH_ONLY_OVERVIEW_DECORATION\n            });\n            prevStartLineNumber = range.startLineNumber;\n            prevEndLineNumber = range.endLineNumber;\n          }\n        }\n        newOverviewRulerApproximateDecorations.push({\n          range: new Range(prevStartLineNumber, 1, prevEndLineNumber, 1),\n          options: FindDecorations._FIND_MATCH_ONLY_OVERVIEW_DECORATION\n        });\n      }\n      // Find matches\n      const newFindMatchesDecorations = new Array(findMatches.length);\n      for (let i = 0, len = findMatches.length; i < len; i++) {\n        newFindMatchesDecorations[i] = {\n          range: findMatches[i].range,\n          options: findMatchesOptions\n        };\n      }\n      this._decorations = accessor.deltaDecorations(this._decorations, newFindMatchesDecorations);\n      // Overview ruler approximate decorations\n      this._overviewRulerApproximateDecorations = accessor.deltaDecorations(this._overviewRulerApproximateDecorations, newOverviewRulerApproximateDecorations);\n      // Range highlight\n      if (this._rangeHighlightDecorationId) {\n        accessor.removeDecoration(this._rangeHighlightDecorationId);\n        this._rangeHighlightDecorationId = null;\n      }\n      // Find scope\n      if (this._findScopeDecorationIds.length) {\n        this._findScopeDecorationIds.forEach(findScopeDecorationId => accessor.removeDecoration(findScopeDecorationId));\n        this._findScopeDecorationIds = [];\n      }\n      if (findScopes?.length) {\n        this._findScopeDecorationIds = findScopes.map(findScope => accessor.addDecoration(findScope, FindDecorations._FIND_SCOPE_DECORATION));\n      }\n    });\n  }\n  matchBeforePosition(position) {\n    if (this._decorations.length === 0) {\n      return null;\n    }\n    for (let i = this._decorations.length - 1; i >= 0; i--) {\n      const decorationId = this._decorations[i];\n      const r = this._editor.getModel().getDecorationRange(decorationId);\n      if (!r || r.endLineNumber > position.lineNumber) {\n        continue;\n      }\n      if (r.endLineNumber < position.lineNumber) {\n        return r;\n      }\n      if (r.endColumn > position.column) {\n        continue;\n      }\n      return r;\n    }\n    return this._editor.getModel().getDecorationRange(this._decorations[this._decorations.length - 1]);\n  }\n  matchAfterPosition(position) {\n    if (this._decorations.length === 0) {\n      return null;\n    }\n    for (let i = 0, len = this._decorations.length; i < len; i++) {\n      const decorationId = this._decorations[i];\n      const r = this._editor.getModel().getDecorationRange(decorationId);\n      if (!r || r.startLineNumber < position.lineNumber) {\n        continue;\n      }\n      if (r.startLineNumber > position.lineNumber) {\n        return r;\n      }\n      if (r.startColumn < position.column) {\n        continue;\n      }\n      return r;\n    }\n    return this._editor.getModel().getDecorationRange(this._decorations[0]);\n  }\n  _allDecorations() {\n    let result = [];\n    result = result.concat(this._decorations);\n    result = result.concat(this._overviewRulerApproximateDecorations);\n    if (this._findScopeDecorationIds.length) {\n      result.push(...this._findScopeDecorationIds);\n    }\n    if (this._rangeHighlightDecorationId) {\n      result.push(this._rangeHighlightDecorationId);\n    }\n    return result;\n  }\n  static {\n    this._CURRENT_FIND_MATCH_DECORATION = ModelDecorationOptions.register({\n      description: 'current-find-match',\n      stickiness: 1 /* TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges */,\n      zIndex: 13,\n      className: 'currentFindMatch',\n      inlineClassName: 'currentFindMatchInline',\n      showIfCollapsed: true,\n      overviewRuler: {\n        color: themeColorFromId(overviewRulerFindMatchForeground),\n        position: OverviewRulerLane.Center\n      },\n      minimap: {\n        color: themeColorFromId(minimapFindMatch),\n        position: 1 /* MinimapPosition.Inline */\n      }\n    });\n  }\n  static {\n    this._FIND_MATCH_DECORATION = ModelDecorationOptions.register({\n      description: 'find-match',\n      stickiness: 1 /* TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges */,\n      zIndex: 10,\n      className: 'findMatch',\n      inlineClassName: 'findMatchInline',\n      showIfCollapsed: true,\n      overviewRuler: {\n        color: themeColorFromId(overviewRulerFindMatchForeground),\n        position: OverviewRulerLane.Center\n      },\n      minimap: {\n        color: themeColorFromId(minimapFindMatch),\n        position: 1 /* MinimapPosition.Inline */\n      }\n    });\n  }\n  static {\n    this._FIND_MATCH_NO_OVERVIEW_DECORATION = ModelDecorationOptions.register({\n      description: 'find-match-no-overview',\n      stickiness: 1 /* TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges */,\n      className: 'findMatch',\n      showIfCollapsed: true\n    });\n  }\n  static {\n    this._FIND_MATCH_ONLY_OVERVIEW_DECORATION = ModelDecorationOptions.register({\n      description: 'find-match-only-overview',\n      stickiness: 1 /* TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges */,\n      overviewRuler: {\n        color: themeColorFromId(overviewRulerFindMatchForeground),\n        position: OverviewRulerLane.Center\n      }\n    });\n  }\n  static {\n    this._RANGE_HIGHLIGHT_DECORATION = ModelDecorationOptions.register({\n      description: 'find-range-highlight',\n      stickiness: 1 /* TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges */,\n      className: 'rangeHighlight',\n      isWholeLine: true\n    });\n  }\n  static {\n    this._FIND_SCOPE_DECORATION = ModelDecorationOptions.register({\n      description: 'find-scope',\n      className: 'findScope',\n      isWholeLine: true\n    });\n  }\n}", "map": {"version": 3, "names": ["Range", "OverviewRulerLane", "ModelDecorationOptions", "minimapFindMatch", "overviewRulerFindMatchForeground", "themeColorFromId", "FindDecorations", "constructor", "editor", "_editor", "_decorations", "_overviewRulerApproximateDecorations", "_findScopeDecorationIds", "_rangeHighlightDecorationId", "_highlightedDecorationId", "_startPosition", "getPosition", "dispose", "removeDecorations", "_allDecorations", "reset", "getCount", "length", "getFindScope", "getModel", "getDecorationRange", "getFindScopes", "scopes", "map", "findScopeDecorationId", "filter", "element", "getStartPosition", "setStartPosition", "newStartPosition", "setCurrentFindMatch", "_getDecorationIndex", "decorationId", "index", "indexOf", "getDecorationRangeAt", "getCurrentMatchesPosition", "desired<PERSON><PERSON><PERSON>", "candidates", "getDecorationsInRange", "candidate", "candidateOpts", "options", "_FIND_MATCH_DECORATION", "_CURRENT_FIND_MATCH_DECORATION", "id", "nextMatch", "newCurrentDecorationId", "matchPosition", "i", "len", "range", "equalsRange", "changeDecorations", "changeAccessor", "changeDecorationOptions", "removeDecoration", "rng", "startLineNumber", "endLineNumber", "endColumn", "lineBeforeEnd", "lineBeforeEndMaxColumn", "getLineMaxColumn", "startColumn", "addDecoration", "_RANGE_HIGHLIGHT_DECORATION", "set", "findMatches", "findScopes", "accessor", "findMatchesOptions", "newOverviewRulerApproximateDecorations", "_FIND_MATCH_NO_OVERVIEW_DECORATION", "lineCount", "getLineCount", "height", "getLayoutInfo", "approxPixelsPerLine", "mergeLinesDelta", "Math", "max", "ceil", "prevStartLineNumber", "prevEndLineNumber", "push", "_FIND_MATCH_ONLY_OVERVIEW_DECORATION", "newFindMatchesDecorations", "Array", "deltaDecorations", "for<PERSON>ach", "findScope", "_FIND_SCOPE_DECORATION", "matchBeforePosition", "position", "r", "lineNumber", "column", "matchAfterPosition", "result", "concat", "register", "description", "stickiness", "zIndex", "className", "inlineClassName", "showIfCollapsed", "overviewRuler", "color", "Center", "minimap", "isWholeLine"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/contrib/find/browser/findDecorations.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Range } from '../../../common/core/range.js';\nimport { OverviewRulerLane } from '../../../common/model.js';\nimport { ModelDecorationOptions } from '../../../common/model/textModel.js';\nimport { minimapFindMatch, overviewRulerFindMatchForeground } from '../../../../platform/theme/common/colorRegistry.js';\nimport { themeColorFromId } from '../../../../platform/theme/common/themeService.js';\nexport class FindDecorations {\n    constructor(editor) {\n        this._editor = editor;\n        this._decorations = [];\n        this._overviewRulerApproximateDecorations = [];\n        this._findScopeDecorationIds = [];\n        this._rangeHighlightDecorationId = null;\n        this._highlightedDecorationId = null;\n        this._startPosition = this._editor.getPosition();\n    }\n    dispose() {\n        this._editor.removeDecorations(this._allDecorations());\n        this._decorations = [];\n        this._overviewRulerApproximateDecorations = [];\n        this._findScopeDecorationIds = [];\n        this._rangeHighlightDecorationId = null;\n        this._highlightedDecorationId = null;\n    }\n    reset() {\n        this._decorations = [];\n        this._overviewRulerApproximateDecorations = [];\n        this._findScopeDecorationIds = [];\n        this._rangeHighlightDecorationId = null;\n        this._highlightedDecorationId = null;\n    }\n    getCount() {\n        return this._decorations.length;\n    }\n    /** @deprecated use getFindScopes to support multiple selections */\n    getFindScope() {\n        if (this._findScopeDecorationIds[0]) {\n            return this._editor.getModel().getDecorationRange(this._findScopeDecorationIds[0]);\n        }\n        return null;\n    }\n    getFindScopes() {\n        if (this._findScopeDecorationIds.length) {\n            const scopes = this._findScopeDecorationIds.map(findScopeDecorationId => this._editor.getModel().getDecorationRange(findScopeDecorationId)).filter(element => !!element);\n            if (scopes.length) {\n                return scopes;\n            }\n        }\n        return null;\n    }\n    getStartPosition() {\n        return this._startPosition;\n    }\n    setStartPosition(newStartPosition) {\n        this._startPosition = newStartPosition;\n        this.setCurrentFindMatch(null);\n    }\n    _getDecorationIndex(decorationId) {\n        const index = this._decorations.indexOf(decorationId);\n        if (index >= 0) {\n            return index + 1;\n        }\n        return 1;\n    }\n    getDecorationRangeAt(index) {\n        const decorationId = index < this._decorations.length ? this._decorations[index] : null;\n        if (decorationId) {\n            return this._editor.getModel().getDecorationRange(decorationId);\n        }\n        return null;\n    }\n    getCurrentMatchesPosition(desiredRange) {\n        const candidates = this._editor.getModel().getDecorationsInRange(desiredRange);\n        for (const candidate of candidates) {\n            const candidateOpts = candidate.options;\n            if (candidateOpts === FindDecorations._FIND_MATCH_DECORATION || candidateOpts === FindDecorations._CURRENT_FIND_MATCH_DECORATION) {\n                return this._getDecorationIndex(candidate.id);\n            }\n        }\n        // We don't know the current match position, so returns zero to show '?' in find widget\n        return 0;\n    }\n    setCurrentFindMatch(nextMatch) {\n        let newCurrentDecorationId = null;\n        let matchPosition = 0;\n        if (nextMatch) {\n            for (let i = 0, len = this._decorations.length; i < len; i++) {\n                const range = this._editor.getModel().getDecorationRange(this._decorations[i]);\n                if (nextMatch.equalsRange(range)) {\n                    newCurrentDecorationId = this._decorations[i];\n                    matchPosition = (i + 1);\n                    break;\n                }\n            }\n        }\n        if (this._highlightedDecorationId !== null || newCurrentDecorationId !== null) {\n            this._editor.changeDecorations((changeAccessor) => {\n                if (this._highlightedDecorationId !== null) {\n                    changeAccessor.changeDecorationOptions(this._highlightedDecorationId, FindDecorations._FIND_MATCH_DECORATION);\n                    this._highlightedDecorationId = null;\n                }\n                if (newCurrentDecorationId !== null) {\n                    this._highlightedDecorationId = newCurrentDecorationId;\n                    changeAccessor.changeDecorationOptions(this._highlightedDecorationId, FindDecorations._CURRENT_FIND_MATCH_DECORATION);\n                }\n                if (this._rangeHighlightDecorationId !== null) {\n                    changeAccessor.removeDecoration(this._rangeHighlightDecorationId);\n                    this._rangeHighlightDecorationId = null;\n                }\n                if (newCurrentDecorationId !== null) {\n                    let rng = this._editor.getModel().getDecorationRange(newCurrentDecorationId);\n                    if (rng.startLineNumber !== rng.endLineNumber && rng.endColumn === 1) {\n                        const lineBeforeEnd = rng.endLineNumber - 1;\n                        const lineBeforeEndMaxColumn = this._editor.getModel().getLineMaxColumn(lineBeforeEnd);\n                        rng = new Range(rng.startLineNumber, rng.startColumn, lineBeforeEnd, lineBeforeEndMaxColumn);\n                    }\n                    this._rangeHighlightDecorationId = changeAccessor.addDecoration(rng, FindDecorations._RANGE_HIGHLIGHT_DECORATION);\n                }\n            });\n        }\n        return matchPosition;\n    }\n    set(findMatches, findScopes) {\n        this._editor.changeDecorations((accessor) => {\n            let findMatchesOptions = FindDecorations._FIND_MATCH_DECORATION;\n            const newOverviewRulerApproximateDecorations = [];\n            if (findMatches.length > 1000) {\n                // we go into a mode where the overview ruler gets \"approximate\" decorations\n                // the reason is that the overview ruler paints all the decorations in the file and we don't want to cause freezes\n                findMatchesOptions = FindDecorations._FIND_MATCH_NO_OVERVIEW_DECORATION;\n                // approximate a distance in lines where matches should be merged\n                const lineCount = this._editor.getModel().getLineCount();\n                const height = this._editor.getLayoutInfo().height;\n                const approxPixelsPerLine = height / lineCount;\n                const mergeLinesDelta = Math.max(2, Math.ceil(3 / approxPixelsPerLine));\n                // merge decorations as much as possible\n                let prevStartLineNumber = findMatches[0].range.startLineNumber;\n                let prevEndLineNumber = findMatches[0].range.endLineNumber;\n                for (let i = 1, len = findMatches.length; i < len; i++) {\n                    const range = findMatches[i].range;\n                    if (prevEndLineNumber + mergeLinesDelta >= range.startLineNumber) {\n                        if (range.endLineNumber > prevEndLineNumber) {\n                            prevEndLineNumber = range.endLineNumber;\n                        }\n                    }\n                    else {\n                        newOverviewRulerApproximateDecorations.push({\n                            range: new Range(prevStartLineNumber, 1, prevEndLineNumber, 1),\n                            options: FindDecorations._FIND_MATCH_ONLY_OVERVIEW_DECORATION\n                        });\n                        prevStartLineNumber = range.startLineNumber;\n                        prevEndLineNumber = range.endLineNumber;\n                    }\n                }\n                newOverviewRulerApproximateDecorations.push({\n                    range: new Range(prevStartLineNumber, 1, prevEndLineNumber, 1),\n                    options: FindDecorations._FIND_MATCH_ONLY_OVERVIEW_DECORATION\n                });\n            }\n            // Find matches\n            const newFindMatchesDecorations = new Array(findMatches.length);\n            for (let i = 0, len = findMatches.length; i < len; i++) {\n                newFindMatchesDecorations[i] = {\n                    range: findMatches[i].range,\n                    options: findMatchesOptions\n                };\n            }\n            this._decorations = accessor.deltaDecorations(this._decorations, newFindMatchesDecorations);\n            // Overview ruler approximate decorations\n            this._overviewRulerApproximateDecorations = accessor.deltaDecorations(this._overviewRulerApproximateDecorations, newOverviewRulerApproximateDecorations);\n            // Range highlight\n            if (this._rangeHighlightDecorationId) {\n                accessor.removeDecoration(this._rangeHighlightDecorationId);\n                this._rangeHighlightDecorationId = null;\n            }\n            // Find scope\n            if (this._findScopeDecorationIds.length) {\n                this._findScopeDecorationIds.forEach(findScopeDecorationId => accessor.removeDecoration(findScopeDecorationId));\n                this._findScopeDecorationIds = [];\n            }\n            if (findScopes?.length) {\n                this._findScopeDecorationIds = findScopes.map(findScope => accessor.addDecoration(findScope, FindDecorations._FIND_SCOPE_DECORATION));\n            }\n        });\n    }\n    matchBeforePosition(position) {\n        if (this._decorations.length === 0) {\n            return null;\n        }\n        for (let i = this._decorations.length - 1; i >= 0; i--) {\n            const decorationId = this._decorations[i];\n            const r = this._editor.getModel().getDecorationRange(decorationId);\n            if (!r || r.endLineNumber > position.lineNumber) {\n                continue;\n            }\n            if (r.endLineNumber < position.lineNumber) {\n                return r;\n            }\n            if (r.endColumn > position.column) {\n                continue;\n            }\n            return r;\n        }\n        return this._editor.getModel().getDecorationRange(this._decorations[this._decorations.length - 1]);\n    }\n    matchAfterPosition(position) {\n        if (this._decorations.length === 0) {\n            return null;\n        }\n        for (let i = 0, len = this._decorations.length; i < len; i++) {\n            const decorationId = this._decorations[i];\n            const r = this._editor.getModel().getDecorationRange(decorationId);\n            if (!r || r.startLineNumber < position.lineNumber) {\n                continue;\n            }\n            if (r.startLineNumber > position.lineNumber) {\n                return r;\n            }\n            if (r.startColumn < position.column) {\n                continue;\n            }\n            return r;\n        }\n        return this._editor.getModel().getDecorationRange(this._decorations[0]);\n    }\n    _allDecorations() {\n        let result = [];\n        result = result.concat(this._decorations);\n        result = result.concat(this._overviewRulerApproximateDecorations);\n        if (this._findScopeDecorationIds.length) {\n            result.push(...this._findScopeDecorationIds);\n        }\n        if (this._rangeHighlightDecorationId) {\n            result.push(this._rangeHighlightDecorationId);\n        }\n        return result;\n    }\n    static { this._CURRENT_FIND_MATCH_DECORATION = ModelDecorationOptions.register({\n        description: 'current-find-match',\n        stickiness: 1 /* TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges */,\n        zIndex: 13,\n        className: 'currentFindMatch',\n        inlineClassName: 'currentFindMatchInline',\n        showIfCollapsed: true,\n        overviewRuler: {\n            color: themeColorFromId(overviewRulerFindMatchForeground),\n            position: OverviewRulerLane.Center\n        },\n        minimap: {\n            color: themeColorFromId(minimapFindMatch),\n            position: 1 /* MinimapPosition.Inline */\n        }\n    }); }\n    static { this._FIND_MATCH_DECORATION = ModelDecorationOptions.register({\n        description: 'find-match',\n        stickiness: 1 /* TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges */,\n        zIndex: 10,\n        className: 'findMatch',\n        inlineClassName: 'findMatchInline',\n        showIfCollapsed: true,\n        overviewRuler: {\n            color: themeColorFromId(overviewRulerFindMatchForeground),\n            position: OverviewRulerLane.Center\n        },\n        minimap: {\n            color: themeColorFromId(minimapFindMatch),\n            position: 1 /* MinimapPosition.Inline */\n        }\n    }); }\n    static { this._FIND_MATCH_NO_OVERVIEW_DECORATION = ModelDecorationOptions.register({\n        description: 'find-match-no-overview',\n        stickiness: 1 /* TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges */,\n        className: 'findMatch',\n        showIfCollapsed: true\n    }); }\n    static { this._FIND_MATCH_ONLY_OVERVIEW_DECORATION = ModelDecorationOptions.register({\n        description: 'find-match-only-overview',\n        stickiness: 1 /* TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges */,\n        overviewRuler: {\n            color: themeColorFromId(overviewRulerFindMatchForeground),\n            position: OverviewRulerLane.Center\n        }\n    }); }\n    static { this._RANGE_HIGHLIGHT_DECORATION = ModelDecorationOptions.register({\n        description: 'find-range-highlight',\n        stickiness: 1 /* TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges */,\n        className: 'rangeHighlight',\n        isWholeLine: true\n    }); }\n    static { this._FIND_SCOPE_DECORATION = ModelDecorationOptions.register({\n        description: 'find-scope',\n        className: 'findScope',\n        isWholeLine: true\n    }); }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,KAAK,QAAQ,+BAA+B;AACrD,SAASC,iBAAiB,QAAQ,0BAA0B;AAC5D,SAASC,sBAAsB,QAAQ,oCAAoC;AAC3E,SAASC,gBAAgB,EAAEC,gCAAgC,QAAQ,oDAAoD;AACvH,SAASC,gBAAgB,QAAQ,mDAAmD;AACpF,OAAO,MAAMC,eAAe,CAAC;EACzBC,WAAWA,CAACC,MAAM,EAAE;IAChB,IAAI,CAACC,OAAO,GAAGD,MAAM;IACrB,IAAI,CAACE,YAAY,GAAG,EAAE;IACtB,IAAI,CAACC,oCAAoC,GAAG,EAAE;IAC9C,IAAI,CAACC,uBAAuB,GAAG,EAAE;IACjC,IAAI,CAACC,2BAA2B,GAAG,IAAI;IACvC,IAAI,CAACC,wBAAwB,GAAG,IAAI;IACpC,IAAI,CAACC,cAAc,GAAG,IAAI,CAACN,OAAO,CAACO,WAAW,CAAC,CAAC;EACpD;EACAC,OAAOA,CAAA,EAAG;IACN,IAAI,CAACR,OAAO,CAACS,iBAAiB,CAAC,IAAI,CAACC,eAAe,CAAC,CAAC,CAAC;IACtD,IAAI,CAACT,YAAY,GAAG,EAAE;IACtB,IAAI,CAACC,oCAAoC,GAAG,EAAE;IAC9C,IAAI,CAACC,uBAAuB,GAAG,EAAE;IACjC,IAAI,CAACC,2BAA2B,GAAG,IAAI;IACvC,IAAI,CAACC,wBAAwB,GAAG,IAAI;EACxC;EACAM,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACV,YAAY,GAAG,EAAE;IACtB,IAAI,CAACC,oCAAoC,GAAG,EAAE;IAC9C,IAAI,CAACC,uBAAuB,GAAG,EAAE;IACjC,IAAI,CAACC,2BAA2B,GAAG,IAAI;IACvC,IAAI,CAACC,wBAAwB,GAAG,IAAI;EACxC;EACAO,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACX,YAAY,CAACY,MAAM;EACnC;EACA;EACAC,YAAYA,CAAA,EAAG;IACX,IAAI,IAAI,CAACX,uBAAuB,CAAC,CAAC,CAAC,EAAE;MACjC,OAAO,IAAI,CAACH,OAAO,CAACe,QAAQ,CAAC,CAAC,CAACC,kBAAkB,CAAC,IAAI,CAACb,uBAAuB,CAAC,CAAC,CAAC,CAAC;IACtF;IACA,OAAO,IAAI;EACf;EACAc,aAAaA,CAAA,EAAG;IACZ,IAAI,IAAI,CAACd,uBAAuB,CAACU,MAAM,EAAE;MACrC,MAAMK,MAAM,GAAG,IAAI,CAACf,uBAAuB,CAACgB,GAAG,CAACC,qBAAqB,IAAI,IAAI,CAACpB,OAAO,CAACe,QAAQ,CAAC,CAAC,CAACC,kBAAkB,CAACI,qBAAqB,CAAC,CAAC,CAACC,MAAM,CAACC,OAAO,IAAI,CAAC,CAACA,OAAO,CAAC;MACxK,IAAIJ,MAAM,CAACL,MAAM,EAAE;QACf,OAAOK,MAAM;MACjB;IACJ;IACA,OAAO,IAAI;EACf;EACAK,gBAAgBA,CAAA,EAAG;IACf,OAAO,IAAI,CAACjB,cAAc;EAC9B;EACAkB,gBAAgBA,CAACC,gBAAgB,EAAE;IAC/B,IAAI,CAACnB,cAAc,GAAGmB,gBAAgB;IACtC,IAAI,CAACC,mBAAmB,CAAC,IAAI,CAAC;EAClC;EACAC,mBAAmBA,CAACC,YAAY,EAAE;IAC9B,MAAMC,KAAK,GAAG,IAAI,CAAC5B,YAAY,CAAC6B,OAAO,CAACF,YAAY,CAAC;IACrD,IAAIC,KAAK,IAAI,CAAC,EAAE;MACZ,OAAOA,KAAK,GAAG,CAAC;IACpB;IACA,OAAO,CAAC;EACZ;EACAE,oBAAoBA,CAACF,KAAK,EAAE;IACxB,MAAMD,YAAY,GAAGC,KAAK,GAAG,IAAI,CAAC5B,YAAY,CAACY,MAAM,GAAG,IAAI,CAACZ,YAAY,CAAC4B,KAAK,CAAC,GAAG,IAAI;IACvF,IAAID,YAAY,EAAE;MACd,OAAO,IAAI,CAAC5B,OAAO,CAACe,QAAQ,CAAC,CAAC,CAACC,kBAAkB,CAACY,YAAY,CAAC;IACnE;IACA,OAAO,IAAI;EACf;EACAI,yBAAyBA,CAACC,YAAY,EAAE;IACpC,MAAMC,UAAU,GAAG,IAAI,CAAClC,OAAO,CAACe,QAAQ,CAAC,CAAC,CAACoB,qBAAqB,CAACF,YAAY,CAAC;IAC9E,KAAK,MAAMG,SAAS,IAAIF,UAAU,EAAE;MAChC,MAAMG,aAAa,GAAGD,SAAS,CAACE,OAAO;MACvC,IAAID,aAAa,KAAKxC,eAAe,CAAC0C,sBAAsB,IAAIF,aAAa,KAAKxC,eAAe,CAAC2C,8BAA8B,EAAE;QAC9H,OAAO,IAAI,CAACb,mBAAmB,CAACS,SAAS,CAACK,EAAE,CAAC;MACjD;IACJ;IACA;IACA,OAAO,CAAC;EACZ;EACAf,mBAAmBA,CAACgB,SAAS,EAAE;IAC3B,IAAIC,sBAAsB,GAAG,IAAI;IACjC,IAAIC,aAAa,GAAG,CAAC;IACrB,IAAIF,SAAS,EAAE;MACX,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAG,IAAI,CAAC7C,YAAY,CAACY,MAAM,EAAEgC,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;QAC1D,MAAME,KAAK,GAAG,IAAI,CAAC/C,OAAO,CAACe,QAAQ,CAAC,CAAC,CAACC,kBAAkB,CAAC,IAAI,CAACf,YAAY,CAAC4C,CAAC,CAAC,CAAC;QAC9E,IAAIH,SAAS,CAACM,WAAW,CAACD,KAAK,CAAC,EAAE;UAC9BJ,sBAAsB,GAAG,IAAI,CAAC1C,YAAY,CAAC4C,CAAC,CAAC;UAC7CD,aAAa,GAAIC,CAAC,GAAG,CAAE;UACvB;QACJ;MACJ;IACJ;IACA,IAAI,IAAI,CAACxC,wBAAwB,KAAK,IAAI,IAAIsC,sBAAsB,KAAK,IAAI,EAAE;MAC3E,IAAI,CAAC3C,OAAO,CAACiD,iBAAiB,CAAEC,cAAc,IAAK;QAC/C,IAAI,IAAI,CAAC7C,wBAAwB,KAAK,IAAI,EAAE;UACxC6C,cAAc,CAACC,uBAAuB,CAAC,IAAI,CAAC9C,wBAAwB,EAAER,eAAe,CAAC0C,sBAAsB,CAAC;UAC7G,IAAI,CAAClC,wBAAwB,GAAG,IAAI;QACxC;QACA,IAAIsC,sBAAsB,KAAK,IAAI,EAAE;UACjC,IAAI,CAACtC,wBAAwB,GAAGsC,sBAAsB;UACtDO,cAAc,CAACC,uBAAuB,CAAC,IAAI,CAAC9C,wBAAwB,EAAER,eAAe,CAAC2C,8BAA8B,CAAC;QACzH;QACA,IAAI,IAAI,CAACpC,2BAA2B,KAAK,IAAI,EAAE;UAC3C8C,cAAc,CAACE,gBAAgB,CAAC,IAAI,CAAChD,2BAA2B,CAAC;UACjE,IAAI,CAACA,2BAA2B,GAAG,IAAI;QAC3C;QACA,IAAIuC,sBAAsB,KAAK,IAAI,EAAE;UACjC,IAAIU,GAAG,GAAG,IAAI,CAACrD,OAAO,CAACe,QAAQ,CAAC,CAAC,CAACC,kBAAkB,CAAC2B,sBAAsB,CAAC;UAC5E,IAAIU,GAAG,CAACC,eAAe,KAAKD,GAAG,CAACE,aAAa,IAAIF,GAAG,CAACG,SAAS,KAAK,CAAC,EAAE;YAClE,MAAMC,aAAa,GAAGJ,GAAG,CAACE,aAAa,GAAG,CAAC;YAC3C,MAAMG,sBAAsB,GAAG,IAAI,CAAC1D,OAAO,CAACe,QAAQ,CAAC,CAAC,CAAC4C,gBAAgB,CAACF,aAAa,CAAC;YACtFJ,GAAG,GAAG,IAAI9D,KAAK,CAAC8D,GAAG,CAACC,eAAe,EAAED,GAAG,CAACO,WAAW,EAAEH,aAAa,EAAEC,sBAAsB,CAAC;UAChG;UACA,IAAI,CAACtD,2BAA2B,GAAG8C,cAAc,CAACW,aAAa,CAACR,GAAG,EAAExD,eAAe,CAACiE,2BAA2B,CAAC;QACrH;MACJ,CAAC,CAAC;IACN;IACA,OAAOlB,aAAa;EACxB;EACAmB,GAAGA,CAACC,WAAW,EAAEC,UAAU,EAAE;IACzB,IAAI,CAACjE,OAAO,CAACiD,iBAAiB,CAAEiB,QAAQ,IAAK;MACzC,IAAIC,kBAAkB,GAAGtE,eAAe,CAAC0C,sBAAsB;MAC/D,MAAM6B,sCAAsC,GAAG,EAAE;MACjD,IAAIJ,WAAW,CAACnD,MAAM,GAAG,IAAI,EAAE;QAC3B;QACA;QACAsD,kBAAkB,GAAGtE,eAAe,CAACwE,kCAAkC;QACvE;QACA,MAAMC,SAAS,GAAG,IAAI,CAACtE,OAAO,CAACe,QAAQ,CAAC,CAAC,CAACwD,YAAY,CAAC,CAAC;QACxD,MAAMC,MAAM,GAAG,IAAI,CAACxE,OAAO,CAACyE,aAAa,CAAC,CAAC,CAACD,MAAM;QAClD,MAAME,mBAAmB,GAAGF,MAAM,GAAGF,SAAS;QAC9C,MAAMK,eAAe,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,IAAI,CAAC,CAAC,GAAGJ,mBAAmB,CAAC,CAAC;QACvE;QACA,IAAIK,mBAAmB,GAAGf,WAAW,CAAC,CAAC,CAAC,CAACjB,KAAK,CAACO,eAAe;QAC9D,IAAI0B,iBAAiB,GAAGhB,WAAW,CAAC,CAAC,CAAC,CAACjB,KAAK,CAACQ,aAAa;QAC1D,KAAK,IAAIV,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGkB,WAAW,CAACnD,MAAM,EAAEgC,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;UACpD,MAAME,KAAK,GAAGiB,WAAW,CAACnB,CAAC,CAAC,CAACE,KAAK;UAClC,IAAIiC,iBAAiB,GAAGL,eAAe,IAAI5B,KAAK,CAACO,eAAe,EAAE;YAC9D,IAAIP,KAAK,CAACQ,aAAa,GAAGyB,iBAAiB,EAAE;cACzCA,iBAAiB,GAAGjC,KAAK,CAACQ,aAAa;YAC3C;UACJ,CAAC,MACI;YACDa,sCAAsC,CAACa,IAAI,CAAC;cACxClC,KAAK,EAAE,IAAIxD,KAAK,CAACwF,mBAAmB,EAAE,CAAC,EAAEC,iBAAiB,EAAE,CAAC,CAAC;cAC9D1C,OAAO,EAAEzC,eAAe,CAACqF;YAC7B,CAAC,CAAC;YACFH,mBAAmB,GAAGhC,KAAK,CAACO,eAAe;YAC3C0B,iBAAiB,GAAGjC,KAAK,CAACQ,aAAa;UAC3C;QACJ;QACAa,sCAAsC,CAACa,IAAI,CAAC;UACxClC,KAAK,EAAE,IAAIxD,KAAK,CAACwF,mBAAmB,EAAE,CAAC,EAAEC,iBAAiB,EAAE,CAAC,CAAC;UAC9D1C,OAAO,EAAEzC,eAAe,CAACqF;QAC7B,CAAC,CAAC;MACN;MACA;MACA,MAAMC,yBAAyB,GAAG,IAAIC,KAAK,CAACpB,WAAW,CAACnD,MAAM,CAAC;MAC/D,KAAK,IAAIgC,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGkB,WAAW,CAACnD,MAAM,EAAEgC,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;QACpDsC,yBAAyB,CAACtC,CAAC,CAAC,GAAG;UAC3BE,KAAK,EAAEiB,WAAW,CAACnB,CAAC,CAAC,CAACE,KAAK;UAC3BT,OAAO,EAAE6B;QACb,CAAC;MACL;MACA,IAAI,CAAClE,YAAY,GAAGiE,QAAQ,CAACmB,gBAAgB,CAAC,IAAI,CAACpF,YAAY,EAAEkF,yBAAyB,CAAC;MAC3F;MACA,IAAI,CAACjF,oCAAoC,GAAGgE,QAAQ,CAACmB,gBAAgB,CAAC,IAAI,CAACnF,oCAAoC,EAAEkE,sCAAsC,CAAC;MACxJ;MACA,IAAI,IAAI,CAAChE,2BAA2B,EAAE;QAClC8D,QAAQ,CAACd,gBAAgB,CAAC,IAAI,CAAChD,2BAA2B,CAAC;QAC3D,IAAI,CAACA,2BAA2B,GAAG,IAAI;MAC3C;MACA;MACA,IAAI,IAAI,CAACD,uBAAuB,CAACU,MAAM,EAAE;QACrC,IAAI,CAACV,uBAAuB,CAACmF,OAAO,CAAClE,qBAAqB,IAAI8C,QAAQ,CAACd,gBAAgB,CAAChC,qBAAqB,CAAC,CAAC;QAC/G,IAAI,CAACjB,uBAAuB,GAAG,EAAE;MACrC;MACA,IAAI8D,UAAU,EAAEpD,MAAM,EAAE;QACpB,IAAI,CAACV,uBAAuB,GAAG8D,UAAU,CAAC9C,GAAG,CAACoE,SAAS,IAAIrB,QAAQ,CAACL,aAAa,CAAC0B,SAAS,EAAE1F,eAAe,CAAC2F,sBAAsB,CAAC,CAAC;MACzI;IACJ,CAAC,CAAC;EACN;EACAC,mBAAmBA,CAACC,QAAQ,EAAE;IAC1B,IAAI,IAAI,CAACzF,YAAY,CAACY,MAAM,KAAK,CAAC,EAAE;MAChC,OAAO,IAAI;IACf;IACA,KAAK,IAAIgC,CAAC,GAAG,IAAI,CAAC5C,YAAY,CAACY,MAAM,GAAG,CAAC,EAAEgC,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MACpD,MAAMjB,YAAY,GAAG,IAAI,CAAC3B,YAAY,CAAC4C,CAAC,CAAC;MACzC,MAAM8C,CAAC,GAAG,IAAI,CAAC3F,OAAO,CAACe,QAAQ,CAAC,CAAC,CAACC,kBAAkB,CAACY,YAAY,CAAC;MAClE,IAAI,CAAC+D,CAAC,IAAIA,CAAC,CAACpC,aAAa,GAAGmC,QAAQ,CAACE,UAAU,EAAE;QAC7C;MACJ;MACA,IAAID,CAAC,CAACpC,aAAa,GAAGmC,QAAQ,CAACE,UAAU,EAAE;QACvC,OAAOD,CAAC;MACZ;MACA,IAAIA,CAAC,CAACnC,SAAS,GAAGkC,QAAQ,CAACG,MAAM,EAAE;QAC/B;MACJ;MACA,OAAOF,CAAC;IACZ;IACA,OAAO,IAAI,CAAC3F,OAAO,CAACe,QAAQ,CAAC,CAAC,CAACC,kBAAkB,CAAC,IAAI,CAACf,YAAY,CAAC,IAAI,CAACA,YAAY,CAACY,MAAM,GAAG,CAAC,CAAC,CAAC;EACtG;EACAiF,kBAAkBA,CAACJ,QAAQ,EAAE;IACzB,IAAI,IAAI,CAACzF,YAAY,CAACY,MAAM,KAAK,CAAC,EAAE;MAChC,OAAO,IAAI;IACf;IACA,KAAK,IAAIgC,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAG,IAAI,CAAC7C,YAAY,CAACY,MAAM,EAAEgC,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;MAC1D,MAAMjB,YAAY,GAAG,IAAI,CAAC3B,YAAY,CAAC4C,CAAC,CAAC;MACzC,MAAM8C,CAAC,GAAG,IAAI,CAAC3F,OAAO,CAACe,QAAQ,CAAC,CAAC,CAACC,kBAAkB,CAACY,YAAY,CAAC;MAClE,IAAI,CAAC+D,CAAC,IAAIA,CAAC,CAACrC,eAAe,GAAGoC,QAAQ,CAACE,UAAU,EAAE;QAC/C;MACJ;MACA,IAAID,CAAC,CAACrC,eAAe,GAAGoC,QAAQ,CAACE,UAAU,EAAE;QACzC,OAAOD,CAAC;MACZ;MACA,IAAIA,CAAC,CAAC/B,WAAW,GAAG8B,QAAQ,CAACG,MAAM,EAAE;QACjC;MACJ;MACA,OAAOF,CAAC;IACZ;IACA,OAAO,IAAI,CAAC3F,OAAO,CAACe,QAAQ,CAAC,CAAC,CAACC,kBAAkB,CAAC,IAAI,CAACf,YAAY,CAAC,CAAC,CAAC,CAAC;EAC3E;EACAS,eAAeA,CAAA,EAAG;IACd,IAAIqF,MAAM,GAAG,EAAE;IACfA,MAAM,GAAGA,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC/F,YAAY,CAAC;IACzC8F,MAAM,GAAGA,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC9F,oCAAoC,CAAC;IACjE,IAAI,IAAI,CAACC,uBAAuB,CAACU,MAAM,EAAE;MACrCkF,MAAM,CAACd,IAAI,CAAC,GAAG,IAAI,CAAC9E,uBAAuB,CAAC;IAChD;IACA,IAAI,IAAI,CAACC,2BAA2B,EAAE;MAClC2F,MAAM,CAACd,IAAI,CAAC,IAAI,CAAC7E,2BAA2B,CAAC;IACjD;IACA,OAAO2F,MAAM;EACjB;EACA;IAAS,IAAI,CAACvD,8BAA8B,GAAG/C,sBAAsB,CAACwG,QAAQ,CAAC;MAC3EC,WAAW,EAAE,oBAAoB;MACjCC,UAAU,EAAE,CAAC,CAAC;MACdC,MAAM,EAAE,EAAE;MACVC,SAAS,EAAE,kBAAkB;MAC7BC,eAAe,EAAE,wBAAwB;MACzCC,eAAe,EAAE,IAAI;MACrBC,aAAa,EAAE;QACXC,KAAK,EAAE7G,gBAAgB,CAACD,gCAAgC,CAAC;QACzD+F,QAAQ,EAAElG,iBAAiB,CAACkH;MAChC,CAAC;MACDC,OAAO,EAAE;QACLF,KAAK,EAAE7G,gBAAgB,CAACF,gBAAgB,CAAC;QACzCgG,QAAQ,EAAE,CAAC,CAAC;MAChB;IACJ,CAAC,CAAC;EAAE;EACJ;IAAS,IAAI,CAACnD,sBAAsB,GAAG9C,sBAAsB,CAACwG,QAAQ,CAAC;MACnEC,WAAW,EAAE,YAAY;MACzBC,UAAU,EAAE,CAAC,CAAC;MACdC,MAAM,EAAE,EAAE;MACVC,SAAS,EAAE,WAAW;MACtBC,eAAe,EAAE,iBAAiB;MAClCC,eAAe,EAAE,IAAI;MACrBC,aAAa,EAAE;QACXC,KAAK,EAAE7G,gBAAgB,CAACD,gCAAgC,CAAC;QACzD+F,QAAQ,EAAElG,iBAAiB,CAACkH;MAChC,CAAC;MACDC,OAAO,EAAE;QACLF,KAAK,EAAE7G,gBAAgB,CAACF,gBAAgB,CAAC;QACzCgG,QAAQ,EAAE,CAAC,CAAC;MAChB;IACJ,CAAC,CAAC;EAAE;EACJ;IAAS,IAAI,CAACrB,kCAAkC,GAAG5E,sBAAsB,CAACwG,QAAQ,CAAC;MAC/EC,WAAW,EAAE,wBAAwB;MACrCC,UAAU,EAAE,CAAC,CAAC;MACdE,SAAS,EAAE,WAAW;MACtBE,eAAe,EAAE;IACrB,CAAC,CAAC;EAAE;EACJ;IAAS,IAAI,CAACrB,oCAAoC,GAAGzF,sBAAsB,CAACwG,QAAQ,CAAC;MACjFC,WAAW,EAAE,0BAA0B;MACvCC,UAAU,EAAE,CAAC,CAAC;MACdK,aAAa,EAAE;QACXC,KAAK,EAAE7G,gBAAgB,CAACD,gCAAgC,CAAC;QACzD+F,QAAQ,EAAElG,iBAAiB,CAACkH;MAChC;IACJ,CAAC,CAAC;EAAE;EACJ;IAAS,IAAI,CAAC5C,2BAA2B,GAAGrE,sBAAsB,CAACwG,QAAQ,CAAC;MACxEC,WAAW,EAAE,sBAAsB;MACnCC,UAAU,EAAE,CAAC,CAAC;MACdE,SAAS,EAAE,gBAAgB;MAC3BO,WAAW,EAAE;IACjB,CAAC,CAAC;EAAE;EACJ;IAAS,IAAI,CAACpB,sBAAsB,GAAG/F,sBAAsB,CAACwG,QAAQ,CAAC;MACnEC,WAAW,EAAE,YAAY;MACzBG,SAAS,EAAE,WAAW;MACtBO,WAAW,EAAE;IACjB,CAAC,CAAC;EAAE;AACR", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}