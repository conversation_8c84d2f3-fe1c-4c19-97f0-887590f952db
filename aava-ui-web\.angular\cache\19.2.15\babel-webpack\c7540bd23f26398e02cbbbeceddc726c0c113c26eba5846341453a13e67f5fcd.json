{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { arrayInsert } from '../../../base/common/arrays.js';\n/**\n * An array that avoids being sparse by always\n * filling up unused indices with a default value.\n */\nexport class FixedArray {\n  constructor(_default) {\n    this._default = _default;\n    this._store = [];\n  }\n  get(index) {\n    if (index < this._store.length) {\n      return this._store[index];\n    }\n    return this._default;\n  }\n  set(index, value) {\n    while (index >= this._store.length) {\n      this._store[this._store.length] = this._default;\n    }\n    this._store[index] = value;\n  }\n  replace(index, oldLength, newLength) {\n    if (index >= this._store.length) {\n      return;\n    }\n    if (oldLength === 0) {\n      this.insert(index, newLength);\n      return;\n    } else if (newLength === 0) {\n      this.delete(index, oldLength);\n      return;\n    }\n    const before = this._store.slice(0, index);\n    const after = this._store.slice(index + oldLength);\n    const insertArr = arrayFill(newLength, this._default);\n    this._store = before.concat(insertArr, after);\n  }\n  delete(deleteIndex, deleteCount) {\n    if (deleteCount === 0 || deleteIndex >= this._store.length) {\n      return;\n    }\n    this._store.splice(deleteIndex, deleteCount);\n  }\n  insert(insertIndex, insertCount) {\n    if (insertCount === 0 || insertIndex >= this._store.length) {\n      return;\n    }\n    const arr = [];\n    for (let i = 0; i < insertCount; i++) {\n      arr[i] = this._default;\n    }\n    this._store = arrayInsert(this._store, insertIndex, arr);\n  }\n}\nfunction arrayFill(length, value) {\n  const arr = [];\n  for (let i = 0; i < length; i++) {\n    arr[i] = value;\n  }\n  return arr;\n}", "map": {"version": 3, "names": ["arrayInsert", "FixedArray", "constructor", "_default", "_store", "get", "index", "length", "set", "value", "replace", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "insert", "delete", "before", "slice", "after", "insertArr", "arrayFill", "concat", "deleteIndex", "deleteCount", "splice", "insertIndex", "insertCount", "arr", "i"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/model/fixedArray.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { arrayInsert } from '../../../base/common/arrays.js';\n/**\n * An array that avoids being sparse by always\n * filling up unused indices with a default value.\n */\nexport class FixedArray {\n    constructor(_default) {\n        this._default = _default;\n        this._store = [];\n    }\n    get(index) {\n        if (index < this._store.length) {\n            return this._store[index];\n        }\n        return this._default;\n    }\n    set(index, value) {\n        while (index >= this._store.length) {\n            this._store[this._store.length] = this._default;\n        }\n        this._store[index] = value;\n    }\n    replace(index, oldLength, newLength) {\n        if (index >= this._store.length) {\n            return;\n        }\n        if (oldLength === 0) {\n            this.insert(index, newLength);\n            return;\n        }\n        else if (newLength === 0) {\n            this.delete(index, oldLength);\n            return;\n        }\n        const before = this._store.slice(0, index);\n        const after = this._store.slice(index + oldLength);\n        const insertArr = arrayFill(newLength, this._default);\n        this._store = before.concat(insertArr, after);\n    }\n    delete(deleteIndex, deleteCount) {\n        if (deleteCount === 0 || deleteIndex >= this._store.length) {\n            return;\n        }\n        this._store.splice(deleteIndex, deleteCount);\n    }\n    insert(insertIndex, insertCount) {\n        if (insertCount === 0 || insertIndex >= this._store.length) {\n            return;\n        }\n        const arr = [];\n        for (let i = 0; i < insertCount; i++) {\n            arr[i] = this._default;\n        }\n        this._store = arrayInsert(this._store, insertIndex, arr);\n    }\n}\nfunction arrayFill(length, value) {\n    const arr = [];\n    for (let i = 0; i < length; i++) {\n        arr[i] = value;\n    }\n    return arr;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,WAAW,QAAQ,gCAAgC;AAC5D;AACA;AACA;AACA;AACA,OAAO,MAAMC,UAAU,CAAC;EACpBC,WAAWA,CAACC,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,MAAM,GAAG,EAAE;EACpB;EACAC,GAAGA,CAACC,KAAK,EAAE;IACP,IAAIA,KAAK,GAAG,IAAI,CAACF,MAAM,CAACG,MAAM,EAAE;MAC5B,OAAO,IAAI,CAACH,MAAM,CAACE,KAAK,CAAC;IAC7B;IACA,OAAO,IAAI,CAACH,QAAQ;EACxB;EACAK,GAAGA,CAACF,KAAK,EAAEG,KAAK,EAAE;IACd,OAAOH,KAAK,IAAI,IAAI,CAACF,MAAM,CAACG,MAAM,EAAE;MAChC,IAAI,CAACH,MAAM,CAAC,IAAI,CAACA,MAAM,CAACG,MAAM,CAAC,GAAG,IAAI,CAACJ,QAAQ;IACnD;IACA,IAAI,CAACC,MAAM,CAACE,KAAK,CAAC,GAAGG,KAAK;EAC9B;EACAC,OAAOA,CAACJ,KAAK,EAAEK,SAAS,EAAEC,SAAS,EAAE;IACjC,IAAIN,KAAK,IAAI,IAAI,CAACF,MAAM,CAACG,MAAM,EAAE;MAC7B;IACJ;IACA,IAAII,SAAS,KAAK,CAAC,EAAE;MACjB,IAAI,CAACE,MAAM,CAACP,KAAK,EAAEM,SAAS,CAAC;MAC7B;IACJ,CAAC,MACI,IAAIA,SAAS,KAAK,CAAC,EAAE;MACtB,IAAI,CAACE,MAAM,CAACR,KAAK,EAAEK,SAAS,CAAC;MAC7B;IACJ;IACA,MAAMI,MAAM,GAAG,IAAI,CAACX,MAAM,CAACY,KAAK,CAAC,CAAC,EAAEV,KAAK,CAAC;IAC1C,MAAMW,KAAK,GAAG,IAAI,CAACb,MAAM,CAACY,KAAK,CAACV,KAAK,GAAGK,SAAS,CAAC;IAClD,MAAMO,SAAS,GAAGC,SAAS,CAACP,SAAS,EAAE,IAAI,CAACT,QAAQ,CAAC;IACrD,IAAI,CAACC,MAAM,GAAGW,MAAM,CAACK,MAAM,CAACF,SAAS,EAAED,KAAK,CAAC;EACjD;EACAH,MAAMA,CAACO,WAAW,EAAEC,WAAW,EAAE;IAC7B,IAAIA,WAAW,KAAK,CAAC,IAAID,WAAW,IAAI,IAAI,CAACjB,MAAM,CAACG,MAAM,EAAE;MACxD;IACJ;IACA,IAAI,CAACH,MAAM,CAACmB,MAAM,CAACF,WAAW,EAAEC,WAAW,CAAC;EAChD;EACAT,MAAMA,CAACW,WAAW,EAAEC,WAAW,EAAE;IAC7B,IAAIA,WAAW,KAAK,CAAC,IAAID,WAAW,IAAI,IAAI,CAACpB,MAAM,CAACG,MAAM,EAAE;MACxD;IACJ;IACA,MAAMmB,GAAG,GAAG,EAAE;IACd,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,WAAW,EAAEE,CAAC,EAAE,EAAE;MAClCD,GAAG,CAACC,CAAC,CAAC,GAAG,IAAI,CAACxB,QAAQ;IAC1B;IACA,IAAI,CAACC,MAAM,GAAGJ,WAAW,CAAC,IAAI,CAACI,MAAM,EAAEoB,WAAW,EAAEE,GAAG,CAAC;EAC5D;AACJ;AACA,SAASP,SAASA,CAACZ,MAAM,EAAEE,KAAK,EAAE;EAC9B,MAAMiB,GAAG,GAAG,EAAE;EACd,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpB,MAAM,EAAEoB,CAAC,EAAE,EAAE;IAC7BD,GAAG,CAACC,CAAC,CAAC,GAAGlB,KAAK;EAClB;EACA,OAAOiB,GAAG;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}