{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as dom from '../../dom.js';\nimport { ThemeIcon } from '../../../common/themables.js';\nconst labelWithIconsRegex = new RegExp(`(\\\\\\\\)?\\\\$\\\\((${ThemeIcon.iconNameExpression}(?:${ThemeIcon.iconModifierExpression})?)\\\\)`, 'g');\nexport function renderLabelWithIcons(text) {\n  const elements = new Array();\n  let match;\n  let textStart = 0,\n    textStop = 0;\n  while ((match = labelWithIconsRegex.exec(text)) !== null) {\n    textStop = match.index || 0;\n    if (textStart < textStop) {\n      elements.push(text.substring(textStart, textStop));\n    }\n    textStart = (match.index || 0) + match[0].length;\n    const [, escaped, codicon] = match;\n    elements.push(escaped ? `$(${codicon})` : renderIcon({\n      id: codicon\n    }));\n  }\n  if (textStart < text.length) {\n    elements.push(text.substring(textStart));\n  }\n  return elements;\n}\nexport function renderIcon(icon) {\n  const node = dom.$(`span`);\n  node.classList.add(...ThemeIcon.asClassNameArray(icon));\n  return node;\n}", "map": {"version": 3, "names": ["dom", "ThemeIcon", "labelWithIconsRegex", "RegExp", "iconNameExpression", "iconModifierExpression", "renderLabelWithIcons", "text", "elements", "Array", "match", "textStart", "textStop", "exec", "index", "push", "substring", "length", "escaped", "codicon", "renderIcon", "id", "icon", "node", "$", "classList", "add", "asClassNameArray"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/browser/ui/iconLabel/iconLabels.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as dom from '../../dom.js';\nimport { ThemeIcon } from '../../../common/themables.js';\nconst labelWithIconsRegex = new RegExp(`(\\\\\\\\)?\\\\$\\\\((${ThemeIcon.iconNameExpression}(?:${ThemeIcon.iconModifierExpression})?)\\\\)`, 'g');\nexport function renderLabelWithIcons(text) {\n    const elements = new Array();\n    let match;\n    let textStart = 0, textStop = 0;\n    while ((match = labelWithIconsRegex.exec(text)) !== null) {\n        textStop = match.index || 0;\n        if (textStart < textStop) {\n            elements.push(text.substring(textStart, textStop));\n        }\n        textStart = (match.index || 0) + match[0].length;\n        const [, escaped, codicon] = match;\n        elements.push(escaped ? `$(${codicon})` : renderIcon({ id: codicon }));\n    }\n    if (textStart < text.length) {\n        elements.push(text.substring(textStart));\n    }\n    return elements;\n}\nexport function renderIcon(icon) {\n    const node = dom.$(`span`);\n    node.classList.add(...ThemeIcon.asClassNameArray(icon));\n    return node;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,KAAKA,GAAG,MAAM,cAAc;AACnC,SAASC,SAAS,QAAQ,8BAA8B;AACxD,MAAMC,mBAAmB,GAAG,IAAIC,MAAM,CAAC,iBAAiBF,SAAS,CAACG,kBAAkB,MAAMH,SAAS,CAACI,sBAAsB,QAAQ,EAAE,GAAG,CAAC;AACxI,OAAO,SAASC,oBAAoBA,CAACC,IAAI,EAAE;EACvC,MAAMC,QAAQ,GAAG,IAAIC,KAAK,CAAC,CAAC;EAC5B,IAAIC,KAAK;EACT,IAAIC,SAAS,GAAG,CAAC;IAAEC,QAAQ,GAAG,CAAC;EAC/B,OAAO,CAACF,KAAK,GAAGR,mBAAmB,CAACW,IAAI,CAACN,IAAI,CAAC,MAAM,IAAI,EAAE;IACtDK,QAAQ,GAAGF,KAAK,CAACI,KAAK,IAAI,CAAC;IAC3B,IAAIH,SAAS,GAAGC,QAAQ,EAAE;MACtBJ,QAAQ,CAACO,IAAI,CAACR,IAAI,CAACS,SAAS,CAACL,SAAS,EAAEC,QAAQ,CAAC,CAAC;IACtD;IACAD,SAAS,GAAG,CAACD,KAAK,CAACI,KAAK,IAAI,CAAC,IAAIJ,KAAK,CAAC,CAAC,CAAC,CAACO,MAAM;IAChD,MAAM,GAAGC,OAAO,EAAEC,OAAO,CAAC,GAAGT,KAAK;IAClCF,QAAQ,CAACO,IAAI,CAACG,OAAO,GAAG,KAAKC,OAAO,GAAG,GAAGC,UAAU,CAAC;MAAEC,EAAE,EAAEF;IAAQ,CAAC,CAAC,CAAC;EAC1E;EACA,IAAIR,SAAS,GAAGJ,IAAI,CAACU,MAAM,EAAE;IACzBT,QAAQ,CAACO,IAAI,CAACR,IAAI,CAACS,SAAS,CAACL,SAAS,CAAC,CAAC;EAC5C;EACA,OAAOH,QAAQ;AACnB;AACA,OAAO,SAASY,UAAUA,CAACE,IAAI,EAAE;EAC7B,MAAMC,IAAI,GAAGvB,GAAG,CAACwB,CAAC,CAAC,MAAM,CAAC;EAC1BD,IAAI,CAACE,SAAS,CAACC,GAAG,CAAC,GAAGzB,SAAS,CAAC0B,gBAAgB,CAACL,IAAI,CAAC,CAAC;EACvD,OAAOC,IAAI;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}