{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { Subject, takeUntil } from 'rxjs';\nimport { FormsModule, Validators } from '@angular/forms';\n// Import child components\nimport { ChatInterfaceComponent } from '@shared/components/chat-interface/chat-interface.component';\n// Removed AgentOutputComponent - now using inline output display\nimport { ButtonComponent, IconComponent, TabsComponent } from '@ava/play-comp-library';\nimport { environment } from '@shared/environments/environment';\nimport workflowConstants from './../constants/workflows.json';\nimport { ExecutionStatus } from '@shared/models/execution.model';\nimport { WorkflowPlaygroundComponent } from './components/workflow-playground/workflow-playground.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@shared/services/workflow.service\";\nimport * as i3 from \"@shared/services/workflow-input-extractor.service\";\nimport * as i4 from \"@shared/index\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"@angular/common\";\nconst _c0 = () => ({\n  background: \"linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)\",\n  \"--button-effect-color\": \"33, 90, 214\",\n  \"border-radius\": \"8px\",\n  \"box-shadow\": \"none\"\n});\nconst _c1 = () => ({\n  width: \"100%\"\n});\nfunction WorkflowExecutionComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"ava-button\", 20);\n    i0.ɵɵlistener(\"click\", function WorkflowExecutionComponent_div_9_Template_ava_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.executeWorkflow());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"customStyles\", i0.ɵɵpureFunction0(1, _c1));\n  }\n}\nfunction WorkflowExecutionComponent_div_17_div_17_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 47);\n    i0.ɵɵtext(1, \"Agent 1 is currently working\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction WorkflowExecutionComponent_div_17_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41)(1, \"div\", 42);\n    i0.ɵɵelement(2, \"ava-icon\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 44)(4, \"span\", 45);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, WorkflowExecutionComponent_div_17_div_17_span_6_Template, 2, 0, \"span\", 46);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const agent_r3 = ctx.$implicit;\n    const i_r4 = ctx.index;\n    i0.ɵɵclassProp(\"active\", i_r4 === 0)(\"completed\", false);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"iconSize\", 20);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(agent_r3.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r4 === 0);\n  }\n}\nfunction WorkflowExecutionComponent_div_17_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48)(1, \"span\", 49);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 50);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const log_r5 = ctx.$implicit;\n    const i_r6 = ctx.index;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate((i_r6 + 1 < 10 ? \"0\" : \"\") + (i_r6 + 1));\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"color\", log_r5.color);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(log_r5.content);\n  }\n}\nfunction WorkflowExecutionComponent_div_17_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 51)(1, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function WorkflowExecutionComponent_div_17_div_26_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleShowAllLogs());\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.showAllLogs ? \"Show Less\" : \"Show More (\" + (ctx_r1.workflowLogs.length - ctx_r1.displayedLogsCount) + \" more)\", \" \");\n  }\n}\nfunction WorkflowExecutionComponent_div_17_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 53)(1, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function WorkflowExecutionComponent_div_17_div_27_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleShowAllLogs());\n    });\n    i0.ɵɵtext(2, \"Show Less\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction WorkflowExecutionComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"div\", 22)(2, \"h2\", 23);\n    i0.ɵɵtext(3, \"Execution Monitor\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 24)(5, \"span\", 25);\n    i0.ɵɵtext(6, \"Overall Progress\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 26)(8, \"div\", 27);\n    i0.ɵɵelement(9, \"div\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\", 29);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(12, \"div\", 30)(13, \"div\", 31)(14, \"h3\");\n    i0.ɵɵtext(15, \"Pipeline Steps\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 32);\n    i0.ɵɵtemplate(17, WorkflowExecutionComponent_div_17_div_17_Template, 7, 7, \"div\", 33);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 34)(19, \"div\", 35)(20, \"h3\");\n    i0.ɵɵtext(21, \"Execution Logs\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"span\", 36);\n    i0.ɵɵtext(23, \"Agent 1's Output\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 37);\n    i0.ɵɵtemplate(25, WorkflowExecutionComponent_div_17_div_25_Template, 5, 4, \"div\", 38)(26, WorkflowExecutionComponent_div_17_div_26_Template, 3, 1, \"div\", 39)(27, WorkflowExecutionComponent_div_17_div_27_Template, 3, 0, \"div\", 40);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵstyleProp(\"width\", ctx_r1.progress, \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.progress, \"%\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.agents);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.displayedLogs);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasMoreLogs);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showAllLogs && ctx_r1.workflowLogs.length > ctx_r1.displayedLogsCount);\n  }\n}\nfunction WorkflowExecutionComponent_div_18_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵtext(1, \" No agent outputs available yet. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction WorkflowExecutionComponent_div_18_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"div\", 64)(2, \"h3\");\n    i0.ɵɵtext(3, \"Workflow Output\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"pre\", 65);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.resMessage);\n  }\n}\nfunction WorkflowExecutionComponent_div_18_div_9_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64)(1, \"h4\");\n    i0.ɵɵtext(2, \"Generated Output\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"pre\", 65);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const output_r10 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(output_r10.raw);\n  }\n}\nfunction WorkflowExecutionComponent_div_18_div_9_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64)(1, \"h4\");\n    i0.ɵɵtext(2, \"Task Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const output_r10 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(output_r10.description);\n  }\n}\nfunction WorkflowExecutionComponent_div_18_div_9_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64)(1, \"h4\");\n    i0.ɵɵtext(2, \"Expected Output\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const output_r10 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(output_r10.expected_output);\n  }\n}\nfunction WorkflowExecutionComponent_div_18_div_9_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64)(1, \"h4\");\n    i0.ɵɵtext(2, \"Summary\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const output_r10 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(output_r10.summary);\n  }\n}\nfunction WorkflowExecutionComponent_div_18_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"div\", 66)(2, \"h3\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(4, WorkflowExecutionComponent_div_18_div_9_div_4_Template, 5, 1, \"div\", 67)(5, WorkflowExecutionComponent_div_18_div_9_div_5_Template, 5, 1, \"div\", 67)(6, WorkflowExecutionComponent_div_18_div_9_div_6_Template, 5, 1, \"div\", 67)(7, WorkflowExecutionComponent_div_18_div_9_div_7_Template, 5, 1, \"div\", 67);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const output_r10 = ctx.$implicit;\n    const i_r11 = ctx.index;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Agent Output \", i_r11 + 1, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", output_r10.raw);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", output_r10.description);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", output_r10.expected_output);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", output_r10.summary);\n  }\n}\nfunction WorkflowExecutionComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"div\", 55)(2, \"div\", 56)(3, \"h3\");\n    i0.ɵɵtext(4, \"Agent Output\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"ava-button\", 57);\n    i0.ɵɵlistener(\"userClick\", function WorkflowExecutionComponent_div_18_Template_ava_button_userClick_5_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.exportResults(\"output\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 58);\n    i0.ɵɵtemplate(7, WorkflowExecutionComponent_div_18_div_7_Template, 2, 0, \"div\", 59)(8, WorkflowExecutionComponent_div_18_div_8_Template, 6, 1, \"div\", 60)(9, WorkflowExecutionComponent_div_18_div_9_Template, 8, 5, \"div\", 61);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"iconName\", \"Download\")(\"iconPosition\", \"right\")(\"iconColor\", \"white\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.resMessage && (!ctx_r1.taskMessage || ctx_r1.taskMessage.length === 0));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.resMessage);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.taskMessage);\n  }\n}\nfunction WorkflowExecutionComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 68)(1, \"div\", 69)(2, \"p\");\n    i0.ɵɵtext(3, \"Configuration settings will be displayed here.\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nexport let WorkflowExecutionComponent = /*#__PURE__*/(() => {\n  class WorkflowExecutionComponent {\n    route;\n    router;\n    workflowService;\n    inputExtractorService;\n    tokenStorage;\n    loaderService;\n    formBuilder;\n    navigationTabs = [{\n      id: 'nav-home',\n      label: 'Execution'\n    }, {\n      id: 'nav-products',\n      label: 'Output'\n    }, {\n      id: 'nav-services',\n      label: 'Configuration'\n    }];\n    // Workflow details\n    workflowId = null;\n    workflowName = 'Workflow';\n    constants = workflowConstants;\n    chatInterfaceComp;\n    // Activity logs\n    activityLogs = [];\n    activityProgress = 0;\n    executionDetails;\n    isRunning = false;\n    status = ExecutionStatus.notStarted;\n    // Chat messages\n    chatMessages = [];\n    isProcessingChat = false;\n    inputText = '';\n    // Agent outputs\n    agentOutputs = [];\n    workflowForm;\n    fileType = '.zip';\n    // Execution state\n    executionStartTime = null;\n    executionCompleted = false;\n    executionId;\n    workflowLogs = [];\n    displayedLogsCount = 10;\n    showAllLogs = false;\n    enableStreamingLog = environment.enableLogStreaming || 'all';\n    isExecutionComplete = false;\n    progressInterval;\n    // Component lifecycle\n    destroy$ = new Subject();\n    selectedTab = 'Agent Activity';\n    demoTabs = [{\n      id: 'activity',\n      label: 'Agent Activity'\n    }, {\n      id: 'agents',\n      label: 'Agent Output'\n    }, {\n      id: 'preview',\n      label: 'Preview',\n      disabled: true\n    }];\n    errorMsg = false;\n    resMessage;\n    taskMessage = [];\n    isJsonValid = false;\n    disableChat = false;\n    selectedFiles = [];\n    workflowAgents = [];\n    userInputList = [];\n    progress = 0;\n    isLoading = false;\n    loaderColor = '';\n    inputFieldOrder = [];\n    currentInputIndex = 0;\n    activeTabId = 'nav-home';\n    // New properties for workflow playground\n    agents = [];\n    isPlaygroundCollapsed = false;\n    pipelineAgents = [];\n    constructor(route, router, workflowService, inputExtractorService, tokenStorage, loaderService, formBuilder) {\n      this.route = route;\n      this.router = router;\n      this.workflowService = workflowService;\n      this.inputExtractorService = inputExtractorService;\n      this.tokenStorage = tokenStorage;\n      this.loaderService = loaderService;\n      this.formBuilder = formBuilder;\n    }\n    ngOnInit() {\n      this.loaderService.disableLoader();\n      this.selectedTab = 'Agent Activity';\n      this.executionId = crypto.randomUUID();\n      // Get workflow ID from route params\n      this.route.paramMap.pipe(takeUntil(this.destroy$)).subscribe(params => {\n        this.workflowId = params.get('id');\n        if (this.workflowId) {\n          this.loadWorkflow(this.workflowId);\n        } else {\n          // No workflow ID, redirect back to workflows page\n          this.router.navigate(['/build/workflows']);\n        }\n      });\n      // this.executeWorkflow()\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n      this.loaderService.enableLoader();\n    }\n    onTabChange(event) {\n      this.selectedTab = event.label;\n      this.activeTabId = event.id;\n      console.log('Tab changed:', event);\n    }\n    // Load workflow data\n    loadWorkflow(id) {\n      // In a real app, this would fetch the workflow from a service\n      console.log(`Loading workflow with ID: ${id}`);\n      this.chatMessages = [{\n        from: 'ai',\n        text: 'I am your workflow assistant. I will help you in executing this workflow.'\n      }];\n      this.workflowForm = this.formBuilder.group({});\n      // Load workflow data for chat interface\n      this.workflowService.getWorkflowById(id).subscribe({\n        next: res => {\n          this.workflowAgents = res.workflowAgents;\n          this.userInputList = this.extractInputField(this.workflowAgents);\n          if (this.userInputList.length === 0) {\n            this.disableChat = true;\n          }\n          this.workflowName = res.name;\n          this.initializeForm();\n          this.startInputCollection();\n          this.initializeDemoLogs(); // Initialize demo logs for testing\n        },\n        error: err => {\n          this.disableChat = true;\n          this.chatInterfaceComp.addAiResponse('No input is required for this workflow. Click on send button to execute the workflow.');\n          console.log(err);\n        }\n      });\n      // Also load workflow details for the new playground component\n      this.getWorkflowDetails(id);\n    }\n    // New method to get workflow details using workflow service\n    getWorkflowDetails(id) {\n      this.workflowService.getOneWorkflow(id).subscribe({\n        next: res => {\n          if (res?.pipeLineAgents?.length) {\n            this.pipelineAgents = res.pipeLineAgents;\n            this.workflowName = res.name;\n            // Convert pipeline agents to AgentData format\n            try {\n              this.agents = this.inputExtractorService.convertToAgentData(this.pipelineAgents);\n              console.log('Converted agents:', this.agents);\n            } catch (error) {\n              console.error('Error converting pipeline agents to AgentData:', error);\n              this.agents = [];\n            }\n            // Extract input fields for form initialization\n            this.userInputList = this.extractInputField(res.pipeLineAgents);\n            this.initializeForm();\n          } else {\n            console.warn('No pipeline agents found in workflow response');\n            this.agents = [];\n          }\n        },\n        error: e => console.error(e)\n      });\n    }\n    extractInputField(pipeLineAgents) {\n      const PLACEHOLDER_PATTERNS = /(%\\d+\\$s)|\\{\\{([a-zA-Z0-9-_]+)\\}\\}/g;\n      const placeholderMap = {};\n      pipeLineAgents.forEach(pipelineAgent => {\n        const agentName = pipelineAgent?.agent?.name;\n        const agentDescription = pipelineAgent?.agent?.task?.description;\n        const matches = agentDescription?.matchAll(PLACEHOLDER_PATTERNS) || [];\n        for (const match of matches) {\n          const placeholder = match[1] || match[2];\n          const placeholderInput = match[0];\n          if (!placeholderMap[placeholder]) {\n            placeholderMap[placeholder] = {\n              agents: new Set(),\n              inputs: new Set()\n            };\n            ;\n          }\n          if (agentName) {\n            placeholderMap[placeholder].agents.add(agentName);\n          }\n          placeholderMap[placeholder].inputs.add(placeholderInput);\n        }\n      });\n      return Object.entries(placeholderMap).map(([placeholder, {\n        agents,\n        inputs\n      }]) => ({\n        name: [...agents].length > 2 ? `${[...agents].slice(0, -1).join(\", \")} and ${[...agents].at(-1)}` : [...agents].join(\" and \"),\n        placeholder,\n        input: [...inputs][0]\n      }));\n    }\n    isImageInput(input) {\n      const match = input.match(/{{(.*?)}}/);\n      if (match && match[1]) {\n        const variableName = match[1].trim();\n        return variableName.startsWith('image') || variableName.startsWith('Image');\n      }\n      return false;\n    }\n    initializeForm() {\n      console.log('Initializing form with userInputList:', this.userInputList);\n      this.userInputList.forEach(label => {\n        console.log('Adding form control for:', label.input);\n        this.workflowForm.addControl(label.input, this.formBuilder.control('', Validators.required));\n      });\n      // Also add form controls for agent inputs to ensure compatibility\n      if (this.agents) {\n        this.agents.forEach(agent => {\n          if (agent.inputs) {\n            agent.inputs.forEach(input => {\n              const controlName = input.placeholder;\n              if (!this.workflowForm.get(controlName)) {\n                console.log('Adding additional form control for agent input:', controlName);\n                this.workflowForm.addControl(controlName, this.formBuilder.control('', Validators.required));\n              }\n            });\n          }\n        });\n      }\n      console.log('Final form controls:', Object.keys(this.workflowForm.controls));\n    }\n    isInputValid() {\n      return this.workflowForm.valid && this.workflowId;\n    }\n    startFakeProgress() {\n      this.progress = 0;\n      this.progressInterval = setInterval(() => {\n        if (this.progress < 90) {\n          this.progress += 5; // Increase slowly\n        }\n      }, 200); // Adjust speed\n    }\n    stopFakeProgress() {\n      clearInterval(this.progressInterval);\n      this.progress = 100;\n      setTimeout(() => {\n        this.isLoading = false;\n      }, 500); // Small delay to let user see 100%\n    }\n    // Handle new chat message from user\n    handleChatMessage(message) {\n      // console.log('message ', message, 'is blank', message.trim() === '');\n      this.isProcessingChat = true;\n      if (message.trim() === '') {\n        if (this.inputFieldOrder.length === 0) {\n          this.chatInterfaceComp.addAiResponse('Executing the workflow...');\n          this.executeWorkflow();\n        }\n        return;\n      }\n      if (this.isExecutionComplete || this.currentInputIndex === this.inputFieldOrder.length) {\n        this.chatInterfaceComp.addAiResponse('Executing the workflow...');\n        this.executeWorkflow();\n        return;\n      }\n      const field = this.inputFieldOrder[this.currentInputIndex];\n      if (this.isImageInput(field)) {\n        // Ignore text input, wait for file input\n        this.chatInterfaceComp.addAiResponse(`Please upload an image file for ${field}`);\n        return;\n      }\n      this.workflowForm.get(field)?.setValue(message);\n      this.currentInputIndex++;\n      if (this.currentInputIndex < this.inputFieldOrder.length) {\n        this.promptForCurrentField();\n      } else {\n        this.chatInterfaceComp.addAiResponse('Thank you for the input! Executing the workflow...');\n        this.executeWorkflow();\n      }\n    }\n    // Save execution logs\n    saveLogs() {\n      console.log('Saving execution logs...');\n      // This would typically save to a service\n    }\n    // Export results\n    exportResults(section) {\n      console.log(`Exporting ${section} data...`);\n      if (section === 'activity') {\n        const data = this.activityLogs.map(log => `[${log.timestamp}] ${log.message}`).join('\\n');\n        this.downloadAsFile(data, 'workflow-activity-logs.txt', 'text/plain');\n      } else {\n        const data = JSON.stringify(this.agentOutputs, null, 2);\n        this.downloadAsFile(data, 'workflow-outputs.json', 'application/json');\n      }\n    }\n    // Helper method to download data as a file\n    downloadAsFile(data, filename, type) {\n      const blob = new Blob([data], {\n        type\n      });\n      const url = URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = filename;\n      link.click();\n      URL.revokeObjectURL(url);\n    }\n    // Handle controls for execution\n    handleControlAction(action) {\n      console.log(`Control action: ${action}`);\n      // In a real app, this would control the workflow execution\n      if (action === 'play') {\n        this.isRunning = true;\n      } else if (action === 'pause' || action === 'stop') {\n        this.isRunning = false;\n      }\n    }\n    // Navigate back to workflow listing\n    navigateBack() {\n      this.router.navigate(['/build/workflows']);\n    }\n    // Navigate to edit workflow\n    editWorkflow() {\n      if (this.workflowId) {\n        this.router.navigate(['/build/workflows/edit', this.workflowId]);\n      }\n    }\n    logExecutionStatus(delay = 2000) {\n      setTimeout(() => {\n        if (!this.isExecutionComplete) {\n          console.log(this.constants);\n          console.log(this.constants['labels'].workflowExecProcessing);\n          this.workflowLogs.push({\n            content: this.constants['labels'].workflowExecProcessing,\n            color: '#F9DB24'\n          });\n        }\n      }, delay);\n    }\n    // public parseAnsiString(ansiString: string) {\n    //   const regex = ansiRegex();\n    //   const parts = ansiString.split(regex);\n    //   const matches = [...ansiString.matchAll(regex)];\n    //   parts.forEach((part, index) => {\n    //     if (part.trim() !== '') {\n    //       let colorCode = matches[index - 1][0];\n    //       if (index - 2 >= 0 && matches[index - 2]?.includes('\\u001b[1m')) {\n    //         colorCode = `\\u001b[1m${colorCode}`;\n    //       }\n    //       this.workflowLogs.push({\n    //         content: part,\n    //         color: this.colorMap[colorCode] || 'white',\n    //       });\n    //     }\n    //   });\n    // }\n    getWorkflowLogs(executionId) {\n      console.log('Attempting to connect to WebSocket for executionId:', executionId);\n      try {\n        this.workflowService.workflowLogConnect(executionId).pipe(takeUntil(this.destroy$)).subscribe({\n          next: message => {\n            console.log('WebSocket message received:', message);\n            const {\n              content,\n              color\n            } = message;\n            if (color) {\n              this.workflowLogs.push({\n                content,\n                color\n              });\n            } else if (this.enableStreamingLog === 'all') {\n              // this.parseAnsiString(content);\n              this.workflowLogs.push({\n                content,\n                color: '#6B7280'\n              });\n            }\n          },\n          error: err => {\n            console.error('WebSocket connection error:', err);\n            this.workflowLogs.push({\n              content: 'WebSocket connection failed. Using demo logs instead.',\n              color: 'red'\n            });\n          },\n          complete: () => {\n            this.logExecutionStatus();\n            console.log('WebSocket connection closed');\n          }\n        });\n      } catch (error) {\n        console.error('Failed to establish WebSocket connection:', error);\n        this.workflowLogs.push({\n          content: 'Failed to connect to log streaming service. Using demo logs.',\n          color: 'red'\n        });\n      }\n    }\n    // public parseAnsiString(ansiString: string) {\n    //   const regex = ansiRegex();\n    //   const parts = ansiString.split(regex);\n    //   const matches = [...ansiString.matchAll(regex)];\n    //   parts.forEach((part, index) => {\n    //     if (part.trim() !== '') {\n    //       let colorCode = matches[index-1][0];\n    //       if(index - 2 >= 0 && matches[index-2]?.includes('\\u001b[1m')) {\n    //         colorCode = `\\u001b[1m${colorCode}`;\n    //       }\n    //       this.workflowLogs.push({\n    //         content: part, \n    //         color: this.colorMap[colorCode] || 'white', \n    //       });\n    //     }\n    //   });\n    // }\n    validateJson(output) {\n      this.isJsonValid = false;\n      try {\n        const parsedOutput = JSON.parse(output);\n        this.isJsonValid = true;\n        return parsedOutput;\n      } catch (e) {\n        return null;\n      }\n    }\n    executeWorkflow() {\n      let payload = new FormData();\n      let queryString = '';\n      console.log('Executing workflow with form value:', this.workflowForm.value);\n      console.log('Form controls:', Object.keys(this.workflowForm.controls));\n      console.log('Agent inputs:', this.agents.map(a => ({\n        name: a.name,\n        inputs: a.inputs\n      })));\n      this.status = ExecutionStatus.running;\n      if (this.selectedFiles.length) {\n        this.selectedFiles.forEach(file => {\n          payload.append('files', file);\n        });\n        payload.append('workflowId', this.workflowId);\n        payload.append('userInputs', JSON.stringify(this.workflowForm.value));\n        payload.append('user', this.tokenStorage.getDaUsername());\n        payload.append('executionId', this.executionId);\n        queryString = '/files';\n      } else {\n        payload = {\n          pipeLineId: this.workflowId,\n          userInputs: this.workflowForm.value,\n          executionId: this.executionId,\n          user: this.tokenStorage.getDaUsername()\n        };\n      }\n      console.log('Final payload:', payload);\n      this.getWorkflowLogs(this.executionId);\n      this.startFakeProgress();\n      this.workflowService.executeWorkflow(payload, queryString).pipe(takeUntil(this.destroy$)).subscribe({\n        next: res => {\n          this.isProcessingChat = false;\n          this.isRunning = false;\n          this.chatInterfaceComp.addAiResponse(res?.message || \"Workflow execution completed successfully!\");\n          console.log('Workflow execution response:', res);\n          // Check if we have workflow response and output\n          if (res?.workflowResponse?.pipeline) {\n            const pipeline = res.workflowResponse.pipeline;\n            console.log('Pipeline data:', pipeline);\n            this.isExecutionComplete = true;\n            this.workflowLogs.push({\n              content: this.constants['labels'].workflowExecComplete,\n              color: '#0F8251'\n            });\n            this.errorMsg = false;\n            // Set the output message\n            this.resMessage = pipeline.output || 'Workflow completed successfully';\n            console.log('Setting resMessage to:', this.resMessage);\n            // Always process task outputs if they exist (remove the length check)\n            if (pipeline.tasksOutputs && Array.isArray(pipeline.tasksOutputs)) {\n              console.log('Processing tasksOutputs:', pipeline.tasksOutputs);\n              this.agentOutputs = pipeline.tasksOutputs.map(task => {\n                return {\n                  id: task?.id || Math.random().toString(36).substring(2, 11),\n                  title: task?.title || 'Task Output',\n                  content: task?.raw || task?.content || '',\n                  agentName: task?.agentName || 'Agent',\n                  timestamp: task?.timestamp || new Date().toISOString(),\n                  type: task?.type || 'text',\n                  description: task?.description || '',\n                  expected_output: task?.expected_output || '',\n                  summary: task?.summary || '',\n                  raw: task?.raw || ''\n                };\n              });\n              console.log('Agent outputs for display:', this.agentOutputs);\n              console.log('Agent outputs length:', this.agentOutputs.length);\n            } else {\n              console.log('No tasksOutputs found, checking for main output');\n              if (pipeline.output) {\n                // If no task outputs but we have main output, create a single output item\n                console.log('Creating output from main pipeline output');\n                this.agentOutputs = [{\n                  id: 'main-output',\n                  title: 'Workflow Output',\n                  content: pipeline.output,\n                  agentName: 'Workflow',\n                  timestamp: new Date().toISOString(),\n                  type: 'text',\n                  description: 'Main workflow output',\n                  expected_output: 'Workflow execution result',\n                  summary: 'Workflow completed successfully',\n                  raw: pipeline.output\n                }];\n                console.log('Created fallback agent output:', this.agentOutputs);\n              }\n            }\n            // Process task messages - this should always run if tasksOutputs exists\n            if (pipeline.tasksOutputs && Array.isArray(pipeline.tasksOutputs)) {\n              this.taskMessage = pipeline.tasksOutputs.map(task => {\n                return {\n                  description: task.description,\n                  summary: task.summary,\n                  raw: task.raw,\n                  expected_output: task.expected_output\n                };\n              });\n              console.log('Task messages processed:', this.taskMessage);\n              console.log('Task messages length:', this.taskMessage.length);\n            } else {\n              console.log('No tasksOutputs found or not an array:', pipeline.tasksOutputs);\n            }\n            // if(\"file_download_url\" in res?.pipeline){\n            //   this.isFileWriter = true;\n            //   this.fileDownloadLink = res?.pipeline?.file_download_url;\n            //   if(!this.fileDownloadLink){\n            //     this.fileDownloadUrlError = [];\n            //     this.fileDownloadUrlError.push(\"Output file is not generated yet!\")\n            //   }\n            // }\n            // this.isAccordian = true\n          }\n          this.validateJson(this.resMessage);\n          this.status = ExecutionStatus.completed;\n          this.stopFakeProgress();\n          this.selectedFiles = [];\n        },\n        error: error => {\n          this.isExecutionComplete = true;\n          this.isProcessingChat = false;\n          this.errorMsg = true;\n          this.resMessage = error?.error?.detail;\n          this.workflowService.workflowLogDisconnect();\n          this.workflowLogs.push({\n            content: this.constants['labels'].workflowLogFailed,\n            color: 'red'\n          });\n          this.chatInterfaceComp.addAiResponse('Something went wrong, Workflow execution has failed.');\n          this.selectedFiles = [];\n          this.stopFakeProgress();\n          console.log('error is', error.message);\n        }\n      });\n    }\n    // public asyncExecutePipeline() {\n    //   const payload: FormData = new FormData();\n    //   if (this.selectedFiles?.length) {\n    //     for (const element of this.selectedFiles) {\n    //       payload.append('files', element)\n    //     }\n    //   }\n    //   payload.append('pipeLineId', String(this.workflowId));\n    //   payload.append('userInputs', JSON.stringify(this.workflowForm.value));\n    //   payload.append('user', this.tokenStorage.getDaUsername() || '');\n    //   payload.append('executionId', this.executionId);\n    //   this.workflowService.asyncExecutePipeline(payload).pipe().subscribe({\n    //     next: (res: any) => {\n    //       if(res) {\n    //         // res handling\n    //         console.log(res);\n    //       }\n    //     },\n    //     error: e => {\n    //       // error handling\n    //       console.log(e);\n    //     }\n    //   })\n    // }\n    handleAttachment() {\n      console.log('handleAttachment');\n    }\n    onAttachmentsSelected(files) {\n      if (this.currentInputIndex === this.inputFieldOrder.length || this.inputFieldOrder.length === 0) {\n        this.selectedFiles = files;\n        return;\n      }\n      const field = this.inputFieldOrder[this.currentInputIndex];\n      if (this.isImageInput(field)) {\n        if (files && files.length > 0) {\n          this.onImageSelected(files[0]);\n        }\n      } else {\n        this.selectedFiles = files;\n      }\n    }\n    startInputCollection() {\n      this.inputFieldOrder = Object.keys(this.workflowForm.controls);\n      this.currentInputIndex = 0;\n      if (this.inputFieldOrder.length > 0) {\n        this.promptForCurrentField();\n      } else {\n        this.disableChat = true;\n        this.chatInterfaceComp.addAiResponse('No input is required for this workflow. Click on send button to execute the workflow.');\n      }\n    }\n    promptForCurrentField() {\n      const field = this.inputFieldOrder[this.currentInputIndex];\n      if (this.isImageInput(field)) {\n        this.fileType = '.jpeg,.png,.jpg,.svg';\n        this.chatInterfaceComp.addAiResponse(`Please upload an image for ${field}`);\n        // UI should now show a file input for the user\n      } else {\n        this.fileType = '.zip'; // or whatever default you want for non-image\n        this.chatInterfaceComp.addAiResponse(`Please enter the value of ${field}`);\n      }\n    }\n    onImageSelected(file) {\n      const field = this.inputFieldOrder[this.currentInputIndex];\n      if (!this.isImageInput(field)) return;\n      const reader = new FileReader();\n      reader.onload = () => {\n        const base64String = reader.result;\n        this.workflowForm.get(field)?.setValue(base64String);\n        this.currentInputIndex++;\n        if (this.currentInputIndex < this.inputFieldOrder.length) {\n          this.promptForCurrentField();\n        } else {\n          this.chatInterfaceComp.addAiResponse('Thank you! Running the workflow...');\n          this.executeWorkflow();\n        }\n      };\n      reader.readAsDataURL(file);\n    }\n    // Event handlers for workflow playground\n    onPlaygroundBackClicked() {\n      this.navigateBack();\n    }\n    onPlaygroundCollapseToggled(isCollapsed) {\n      this.isPlaygroundCollapsed = isCollapsed;\n    }\n    onAgentInputChanged(event) {\n      // Find the agent and update the input value\n      const agent = this.agents.find(a => a.id === event.agentId);\n      if (agent && agent.inputs && agent.inputs[event.inputIndex]) {\n        agent.inputs[event.inputIndex].value = event.value;\n        // Update the form control if it exists\n        const placeholder = agent.inputs[event.inputIndex].placeholder;\n        if (this.workflowForm.get(placeholder)) {\n          this.workflowForm.get(placeholder)?.setValue(event.value);\n        }\n      }\n    }\n    onAgentFileSelected(event) {\n      // Find the agent and update the files\n      const agent = this.agents.find(a => a.id === event.agentId);\n      if (agent && agent.inputs && agent.inputs[event.inputIndex]) {\n        agent.inputs[event.inputIndex].files = event.files;\n        // Add files to selectedFiles array for execution\n        this.selectedFiles = [...this.selectedFiles, ...event.files];\n      }\n    }\n    onMessageSent(event) {\n      console.log('Message sent from agent:', event);\n      // Find the agent and update the input value\n      const agent = this.agents.find(a => a.id === event.agentId);\n      if (agent && agent.inputs && agent.inputs[event.inputIndex]) {\n        agent.inputs[event.inputIndex].value = event.value;\n        // Update files if provided\n        if (event.files) {\n          agent.inputs[event.inputIndex].files = event.files;\n          this.selectedFiles = [...this.selectedFiles, ...event.files];\n        }\n        // Update the form control if it exists\n        const placeholder = agent.inputs[event.inputIndex].placeholder;\n        console.log('Updating form field:', placeholder, 'with value:', event.value);\n        console.log('Form controls available:', Object.keys(this.workflowForm.controls));\n        console.log('Current form value before update:', this.workflowForm.value);\n        if (this.workflowForm.get(placeholder)) {\n          this.workflowForm.get(placeholder)?.setValue(event.value);\n          console.log('Form value after update:', this.workflowForm.value);\n        } else {\n          console.warn('Form control not found for placeholder:', placeholder);\n          // Try to add the control dynamically\n          this.workflowForm.addControl(placeholder, this.formBuilder.control(event.value));\n          console.log('Added new form control. Form value:', this.workflowForm.value);\n        }\n        // Log the input submission\n        this.workflowLogs.push({\n          content: `Agent \"${agent.name}\" - Input \"${agent.inputs[event.inputIndex].inputName}\": ${event.value}`,\n          color: '#10B981'\n        });\n      }\n    }\n    /**\n     * Get the logs to display based on the current view state\n     */\n    get displayedLogs() {\n      if (this.showAllLogs) {\n        return this.workflowLogs;\n      }\n      return this.workflowLogs.slice(0, this.displayedLogsCount);\n    }\n    /**\n     * Toggle between showing limited logs and all logs\n     */\n    toggleShowAllLogs() {\n      this.showAllLogs = !this.showAllLogs;\n    }\n    /**\n     * Check if there are more logs to show\n     */\n    get hasMoreLogs() {\n      return this.workflowLogs.length > this.displayedLogsCount && !this.showAllLogs;\n    }\n    /**\n     * Initialize demo logs for testing the log display functionality\n     */\n    initializeDemoLogs() {\n      // Only add demo logs if there are no existing logs\n      if (this.workflowLogs.length === 0) {\n        const demoLogs = [{\n          content: 'Workflow execution started',\n          color: '#0F8251'\n        }, {\n          content: 'Loading pipeline configuration...',\n          color: '#6B7280'\n        }, {\n          content: 'Initializing Test Case Normalizer agent',\n          color: '#2563EB'\n        }, {\n          content: 'Processing input parameters',\n          color: '#6B7280'\n        }, {\n          content: 'Agent 1 is currently working',\n          color: '#F9DB24'\n        }, {\n          content: 'Analyzing test case structure...',\n          color: '#6B7280'\n        }, {\n          content: 'Normalizing test case format',\n          color: '#6B7280'\n        }, {\n          content: 'Validating output format',\n          color: '#6B7280'\n        }, {\n          content: 'Test Case Normalizer completed successfully',\n          color: '#0F8251'\n        }, {\n          content: 'Initializing ELT_OAMA Frameworkagent',\n          color: '#2563EB'\n        }, {\n          content: 'Loading framework configuration',\n          color: '#6B7280'\n        }, {\n          content: 'Processing framework parameters',\n          color: '#6B7280'\n        }, {\n          content: 'Agent 2 is currently working',\n          color: '#F9DB24'\n        }, {\n          content: 'Executing framework analysis...',\n          color: '#6B7280'\n        }, {\n          content: 'Generating framework recommendations',\n          color: '#6B7280'\n        }, {\n          content: 'ELT_OAMA Framework analysis completed',\n          color: '#0F8251'\n        }, {\n          content: 'Initializing ELT_QE_HPX_Automated_Script agent',\n          color: '#2563EB'\n        }, {\n          content: 'Loading automation script templates',\n          color: '#6B7280'\n        }, {\n          content: 'Agent 3 is currently working',\n          color: '#F9DB24'\n        }, {\n          content: 'Generating automated test scripts...',\n          color: '#6B7280'\n        }, {\n          content: 'Validating script syntax and structure',\n          color: '#6B7280'\n        }, {\n          content: 'Script generation completed successfully',\n          color: '#0F8251'\n        }, {\n          content: 'All pipeline agents completed successfully',\n          color: '#0F8251'\n        }, {\n          content: 'Workflow execution completed',\n          color: '#0F8251'\n        }];\n        this.workflowLogs = demoLogs;\n      }\n    }\n    static ɵfac = function WorkflowExecutionComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || WorkflowExecutionComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.WorkflowService), i0.ɵɵdirectiveInject(i3.WorkflowInputExtractorService), i0.ɵɵdirectiveInject(i4.TokenStorageService), i0.ɵɵdirectiveInject(i4.LoaderService), i0.ɵɵdirectiveInject(i5.FormBuilder));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: WorkflowExecutionComponent,\n      selectors: [[\"app-workflow-execution\"]],\n      viewQuery: function WorkflowExecutionComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(ChatInterfaceComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.chatInterfaceComp = _t.first);\n        }\n      },\n      decls: 20,\n      vars: 16,\n      consts: [[1, \"workflow-execution-container\"], [\"width\", \"0\", \"height\", \"0\", 2, \"position\", \"absolute\"], [\"id\", \"gradient\", \"x1\", \"0%\", \"y1\", \"0%\", \"x2\", \"100%\", \"y2\", \"0%\"], [\"offset\", \"0%\", \"stop-color\", \"#6566CD\"], [\"offset\", \"100%\", \"stop-color\", \"#F96CAB\"], [\"role\", \"main\", 1, \"execution-content\"], [\"role\", \"region\", \"aria-label\", \"Workflow Input Panel\", 1, \"left-panel\"], [\"role\", \"region\", \"aria-label\", \"Workflow Playground\", 1, \"playground-component\", 3, \"backClicked\", \"collapseToggled\", \"agentInputChanged\", \"agentFileSelected\", \"messageSent\", \"agents\", \"isCollapsed\", \"workflowName\"], [\"class\", \"execute-button-container\", 4, \"ngIf\"], [\"role\", \"region\", \"aria-label\", \"Workflow Results\", 1, \"right-panel\"], [1, \"tabs-header\"], [1, \"tabs-container\"], [\"variant\", \"button\", \"ariaLabel\", \"Workflow sections navigation\", 3, \"tabChange\", \"tabs\", \"activeTabId\", \"showContentPanels\"], [1, \"header-actions\"], [\"label\", \"Send for Approval\", \"variant\", \"primary\", \"size\", \"medium\", 3, \"customStyles\"], [1, \"content-sections\"], [\"class\", \"section-content execution-section\", 4, \"ngIf\"], [\"class\", \"section-content output-section\", 4, \"ngIf\"], [\"class\", \"section-content configuration-section\", 4, \"ngIf\"], [1, \"execute-button-container\"], [\"label\", \"Execute Workflow\", \"variant\", \"primary\", \"size\", \"large\", 3, \"click\", \"customStyles\"], [1, \"section-content\", \"execution-section\"], [1, \"execution-monitor-header\"], [1, \"monitor-title\"], [1, \"progress-section\"], [1, \"progress-label\"], [1, \"progress-container\"], [1, \"progress-bar\"], [1, \"progress-fill\"], [1, \"progress-text\"], [1, \"execution-content-grid\"], [1, \"pipeline-steps-section\"], [1, \"pipeline-agents-list\"], [\"class\", \"pipeline-agent-item\", 3, \"active\", \"completed\", 4, \"ngFor\", \"ngForOf\"], [1, \"execution-logs-section\"], [1, \"logs-header\"], [1, \"logs-subtitle\"], [1, \"logs-content\"], [\"class\", \"log-entry\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"show-more\", 4, \"ngIf\"], [\"class\", \"show-less\", 4, \"ngIf\"], [1, \"pipeline-agent-item\"], [1, \"agent-icon\"], [\"iconName\", \"bot\", \"iconColor\", \"#666\", 3, \"iconSize\"], [1, \"agent-info\"], [1, \"agent-name\"], [\"class\", \"agent-status\", 4, \"ngIf\"], [1, \"agent-status\"], [1, \"log-entry\"], [1, \"log-number\"], [1, \"log-message\"], [1, \"show-more\"], [1, \"show-more-btn\", 3, \"click\"], [1, \"show-less\"], [1, \"section-content\", \"output-section\"], [1, \"agent-output\"], [1, \"output-header\"], [\"aria-label\", \"Export agent outputs\", \"title\", \"Export agent outputs\", \"variant\", \"primary\", \"size\", \"medium\", \"label\", \"Export\", 3, \"userClick\", \"iconName\", \"iconPosition\", \"iconColor\"], [1, \"output-content\"], [\"class\", \"no-outputs\", 4, \"ngIf\"], [\"class\", \"output-card\", 4, \"ngIf\"], [\"class\", \"output-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"no-outputs\"], [1, \"output-card\"], [1, \"output-card-content\"], [1, \"output-text\"], [1, \"output-card-header\"], [\"class\", \"output-card-content\", 4, \"ngIf\"], [1, \"section-content\", \"configuration-section\"], [1, \"configuration-content\"]],\n      template: function WorkflowExecutionComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(1, \"svg\", 1)(2, \"defs\")(3, \"linearGradient\", 2);\n          i0.ɵɵelement(4, \"stop\", 3)(5, \"stop\", 4);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(6, \"div\", 5)(7, \"div\", 6)(8, \"app-workflow-playground\", 7);\n          i0.ɵɵlistener(\"backClicked\", function WorkflowExecutionComponent_Template_app_workflow_playground_backClicked_8_listener() {\n            return ctx.onPlaygroundBackClicked();\n          })(\"collapseToggled\", function WorkflowExecutionComponent_Template_app_workflow_playground_collapseToggled_8_listener($event) {\n            return ctx.onPlaygroundCollapseToggled($event);\n          })(\"agentInputChanged\", function WorkflowExecutionComponent_Template_app_workflow_playground_agentInputChanged_8_listener($event) {\n            return ctx.onAgentInputChanged($event);\n          })(\"agentFileSelected\", function WorkflowExecutionComponent_Template_app_workflow_playground_agentFileSelected_8_listener($event) {\n            return ctx.onAgentFileSelected($event);\n          })(\"messageSent\", function WorkflowExecutionComponent_Template_app_workflow_playground_messageSent_8_listener($event) {\n            return ctx.onMessageSent($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(9, WorkflowExecutionComponent_div_9_Template, 2, 2, \"div\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"div\", 9)(11, \"div\", 10)(12, \"div\", 11)(13, \"ava-tabs\", 12);\n          i0.ɵɵlistener(\"tabChange\", function WorkflowExecutionComponent_Template_ava_tabs_tabChange_13_listener($event) {\n            return ctx.onTabChange($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(14, \"div\", 13);\n          i0.ɵɵelement(15, \"ava-button\", 14);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"div\", 15);\n          i0.ɵɵtemplate(17, WorkflowExecutionComponent_div_17_Template, 28, 7, \"div\", 16)(18, WorkflowExecutionComponent_div_18_Template, 10, 6, \"div\", 17)(19, WorkflowExecutionComponent_div_19_Template, 4, 0, \"div\", 18);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵclassProp(\"collapsed\", ctx.isPlaygroundCollapsed);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"agents\", ctx.agents)(\"isCollapsed\", ctx.isPlaygroundCollapsed)(\"workflowName\", ctx.workflowName);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isPlaygroundCollapsed);\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"expanded\", ctx.isPlaygroundCollapsed);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"tabs\", ctx.navigationTabs)(\"activeTabId\", ctx.activeTabId)(\"showContentPanels\", false);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"customStyles\", i0.ɵɵpureFunction0(15, _c0));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.activeTabId === \"nav-home\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.activeTabId === \"nav-products\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.activeTabId === \"nav-services\");\n        }\n      },\n      dependencies: [CommonModule, i6.NgForOf, i6.NgIf, FormsModule, TabsComponent, ButtonComponent, IconComponent, WorkflowPlaygroundComponent],\n      styles: [\".ava-tabs {\\n  background: none !important;\\n}\\n\\n  .ava-tabs__container {\\n  border-radius: none !important;\\n  border: none !important;\\n  padding: 0 !important;\\n  box-shadow: none !important;\\n  background: none !important;\\n}\\n\\n  .ava-tabs__list {\\n  padding: 0 !important;\\n}\\n\\n.workflow-execution-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  height: 93vh;\\n  overflow: hidden;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 20px 24px;\\n  border-bottom: 1px solid var(--border-color, #e0e0e0);\\n  flex-shrink: 0;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-title[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 24px;\\n  font-weight: 600;\\n  color: var(--text-color, #333);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-title[_ngcontent-%COMP%]   .header-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%], \\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .edit-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 8px 16px;\\n  border-radius: 5px;\\n  font-weight: 500;\\n  font-size: 14px;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%], \\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .edit-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  margin-right: 6px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%] {\\n  background-color: var(--bg-muted, #f5f5f5);\\n  border: 1px solid var(--border-color, #e0e0e0);\\n  color: var(--text-color, #333);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]:hover {\\n  background-color: var(--bg-muted-hover, #e9e9e9);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .edit-button[_ngcontent-%COMP%] {\\n  background-color: var(--card-bg, #fff);\\n  border: 1px solid var(--border-color, #e0e0e0);\\n  color: var(--text-color, #333);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .edit-button[_ngcontent-%COMP%]:hover {\\n  background-color: var(--card-bg-hover, #f9f9f9);\\n  border-color: var(--border-color-dark, #d0d0d0);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex: 1;\\n  min-height: 0;\\n  overflow: hidden;\\n  gap: 20px;\\n  padding: 20px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .left-panel[_ngcontent-%COMP%] {\\n  flex: 2.5;\\n  display: flex;\\n  flex-direction: column;\\n  min-width: 0;\\n  background: transparent;\\n  transition: all 0.3s ease;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .left-panel.collapsed[_ngcontent-%COMP%] {\\n  flex: 0;\\n  min-width: 3%;\\n  width: 3%;\\n  overflow: hidden;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .left-panel[_ngcontent-%COMP%]   .playground-component[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  min-height: 0;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .left-panel[_ngcontent-%COMP%]   .execute-button-container[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  background-color: var(--card-bg, white);\\n  border-radius: 0 0 8px 8px;\\n  border-top: 1px solid var(--border-color, #e0e0e0);\\n  flex-shrink: 0;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .left-panel[_ngcontent-%COMP%]   .execute-button-container[_ngcontent-%COMP%]     .ava-button {\\n  width: 100% !important;\\n  min-height: 48px;\\n  font-weight: 600;\\n  font-size: 16px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .left-panel[_ngcontent-%COMP%]   .execute-button-container[_ngcontent-%COMP%]     .ava-button:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%] {\\n  flex: 5.5;\\n  display: flex;\\n  flex-direction: column;\\n  background-color: var(--card-bg, white);\\n  border-radius: 8px;\\n  box-shadow: 0 4px 15px var(--card-shadow, rgba(0, 0, 0, 0.05));\\n  overflow: hidden;\\n  transition: all 0.3s ease;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel.expanded[_ngcontent-%COMP%] {\\n  flex: 8;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .tabs-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 8px 16px;\\n  background: #E6F3FF;\\n  height: 64px;\\n  flex-shrink: 0;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .tabs-header[_ngcontent-%COMP%]   .tabs-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .tabs-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n  margin-left: 16px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-monitor-header[_ngcontent-%COMP%] {\\n  padding: 20px 24px;\\n  border-bottom: 1px solid var(--border-color, #e0e0e0);\\n  background-color: var(--card-bg, white);\\n  flex-shrink: 0;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-monitor-header[_ngcontent-%COMP%]   .monitor-title[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: var(--text-color, #333);\\n  margin-bottom: 16px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-monitor-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0 0 16px 0;\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: var(--text-color, #333);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-monitor-header[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-label[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: var(--text-muted, #666);\\n  margin-bottom: 8px;\\n  display: block;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-monitor-header[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-monitor-header[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 8px;\\n  background-color: var(--bg-muted, #f0f0f0);\\n  border-radius: 4px;\\n  overflow: hidden;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-monitor-header[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: linear-gradient(90deg, #6566cd 0%, #f96cab 100%);\\n  border-radius: 4px;\\n  transition: width 0.3s ease;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-monitor-header[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%]   .progress-text[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: var(--text-color, #333);\\n  min-width: 40px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex: 1;\\n  min-height: 0;\\n  gap: 16px;\\n  padding: 20px;\\n  background-color: var(--card-bg, white);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .pipeline-steps-section[_ngcontent-%COMP%], \\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .execution-logs-section[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-height: 0;\\n  background-color: #F7F8F9;\\n  border-radius: 16px;\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .pipeline-steps-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding: 20px 24px 16px 24px;\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: var(--text-color, #333);\\n  background-color: #F7F8F9;\\n  flex-shrink: 0;\\n  border-bottom: 1px solid rgba(0, 0, 0, 0.05);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .pipeline-steps-section[_ngcontent-%COMP%]   .pipeline-agents-list[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-height: 0;\\n  padding: 0 24px 24px 24px;\\n  background-color: #F7F8F9;\\n  overflow-y: auto;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .pipeline-steps-section[_ngcontent-%COMP%]   .pipeline-agents-list[_ngcontent-%COMP%]   .pipeline-agent-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n  padding: 16px 20px;\\n  margin-bottom: 12px;\\n  border: 1px solid #E5E7EB;\\n  border-radius: 16px;\\n  background-color: white;\\n  transition: all 0.2s ease;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .pipeline-steps-section[_ngcontent-%COMP%]   .pipeline-agents-list[_ngcontent-%COMP%]   .pipeline-agent-item.active[_ngcontent-%COMP%] {\\n  border-color: #6566cd;\\n  background-color: white;\\n  box-shadow: 0 2px 8px rgba(101, 102, 205, 0.1);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .pipeline-steps-section[_ngcontent-%COMP%]   .pipeline-agents-list[_ngcontent-%COMP%]   .pipeline-agent-item.completed[_ngcontent-%COMP%] {\\n  border-color: #10b981;\\n  background-color: white;\\n  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.1);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .pipeline-steps-section[_ngcontent-%COMP%]   .pipeline-agents-list[_ngcontent-%COMP%]   .pipeline-agent-item[_ngcontent-%COMP%]   .agent-icon[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  background-color: #F3F4F6;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  flex-shrink: 0;\\n  border: 2px solid white;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .pipeline-steps-section[_ngcontent-%COMP%]   .pipeline-agents-list[_ngcontent-%COMP%]   .pipeline-agent-item[_ngcontent-%COMP%]   .agent-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .pipeline-steps-section[_ngcontent-%COMP%]   .pipeline-agents-list[_ngcontent-%COMP%]   .pipeline-agent-item[_ngcontent-%COMP%]   .agent-info[_ngcontent-%COMP%]   .agent-name[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: var(--text-color, #333);\\n  margin-bottom: 4px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .pipeline-steps-section[_ngcontent-%COMP%]   .pipeline-agents-list[_ngcontent-%COMP%]   .pipeline-agent-item[_ngcontent-%COMP%]   .agent-info[_ngcontent-%COMP%]   .agent-status[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #6B7280;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .execution-logs-section[_ngcontent-%COMP%]   .logs-header[_ngcontent-%COMP%] {\\n  padding: 20px 24px 16px 24px;\\n  background-color: #F7F8F9;\\n  flex-shrink: 0;\\n  border-bottom: 1px solid rgba(0, 0, 0, 0.05);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .execution-logs-section[_ngcontent-%COMP%]   .logs-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 4px 0;\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: var(--text-color, #333);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .execution-logs-section[_ngcontent-%COMP%]   .logs-header[_ngcontent-%COMP%]   .logs-subtitle[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #6B7280;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .execution-logs-section[_ngcontent-%COMP%]   .logs-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-height: 0;\\n  padding: 0 24px 24px 24px;\\n  overflow-y: auto;\\n  font-family: \\\"SF Mono\\\", \\\"Monaco\\\", \\\"Inconsolata\\\", \\\"Roboto Mono\\\", monospace;\\n  background-color: #F7F8F9;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .execution-logs-section[_ngcontent-%COMP%]   .logs-content[_ngcontent-%COMP%]   .log-entry[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  margin-bottom: 6px;\\n  font-size: 13px;\\n  line-height: 1.5;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .execution-logs-section[_ngcontent-%COMP%]   .logs-content[_ngcontent-%COMP%]   .log-entry[_ngcontent-%COMP%]   .log-number[_ngcontent-%COMP%] {\\n  color: #6B7280;\\n  font-weight: 600;\\n  min-width: 28px;\\n  flex-shrink: 0;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .execution-logs-section[_ngcontent-%COMP%]   .logs-content[_ngcontent-%COMP%]   .log-entry[_ngcontent-%COMP%]   .log-message[_ngcontent-%COMP%] {\\n  flex: 1;\\n  word-break: break-word;\\n  color: #374151;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .execution-logs-section[_ngcontent-%COMP%]   .logs-content[_ngcontent-%COMP%]   .show-more[_ngcontent-%COMP%], \\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .execution-logs-section[_ngcontent-%COMP%]   .logs-content[_ngcontent-%COMP%]   .show-less[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-top: 20px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .execution-logs-section[_ngcontent-%COMP%]   .logs-content[_ngcontent-%COMP%]   .show-more[_ngcontent-%COMP%]   .show-more-btn[_ngcontent-%COMP%], \\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .execution-logs-section[_ngcontent-%COMP%]   .logs-content[_ngcontent-%COMP%]   .show-less[_ngcontent-%COMP%]   .show-more-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #6566cd;\\n  font-size: 14px;\\n  cursor: pointer;\\n  text-decoration: underline;\\n  padding: 8px 16px;\\n  border-radius: 8px;\\n  transition: all 0.2s ease;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .execution-logs-section[_ngcontent-%COMP%]   .logs-content[_ngcontent-%COMP%]   .show-more[_ngcontent-%COMP%]   .show-more-btn[_ngcontent-%COMP%]:hover, \\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .execution-logs-section[_ngcontent-%COMP%]   .logs-content[_ngcontent-%COMP%]   .show-less[_ngcontent-%COMP%]   .show-more-btn[_ngcontent-%COMP%]:hover {\\n  color: #5555bb;\\n  background-color: rgba(101, 102, 205, 0.05);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.output-section[_ngcontent-%COMP%]   app-agent-output[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.configuration-section[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  overflow-y: auto;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.configuration-section[_ngcontent-%COMP%]   .configuration-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  height: 100%;\\n  text-align: center;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.configuration-section[_ngcontent-%COMP%]   .configuration-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: var(--text-color, #666);\\n  font-size: 16px;\\n  margin: 0;\\n  font-style: italic;\\n}\\n\\n  nav.ava-tabs__list {\\n  background: #e9effd;\\n  padding: 4px;\\n}\\n\\n  button.ava-button.primary.active {\\n  background: #616161;\\n  color: #fff;\\n}\\n\\n  .column-header .ava-tabs[data-variant=button] .ava-tabs__tab--pill {\\n  border-radius: 8px !important;\\n  padding: 12px 16px !important;\\n  font-family: \\\"Mulish\\\";\\n}\\n\\n  .ava-tabs[data-variant=button] .ava-tabs__tab--active .ava-tabs__tab-text {\\n  color: white;\\n}\\n\\n  .ava-tabs__tab-text {\\n  color: #4c515b;\\n  font-family: \\\"Mulish\\\";\\n  font-weight: 600;\\n}\\n\\n  .right-section-header .ava-button.secondary {\\n  color: #1a46a7;\\n  border: none;\\n}\\n\\n  .right-section-header .ava-button.secondary:hover {\\n  color: #1a46a7;\\n  border: none;\\n}\\n\\n.right-section-header[_ngcontent-%COMP%] {\\n  text-align: end;\\n}\\n\\n.agent-output[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%;\\n  padding: 20px;\\n  background: var(--card-bg);\\n}\\n.agent-output[_ngcontent-%COMP%]   .output-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 24px;\\n}\\n.agent-output[_ngcontent-%COMP%]   .output-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: var(--text-color);\\n}\\n.agent-output[_ngcontent-%COMP%]   .output-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n}\\n.agent-output[_ngcontent-%COMP%]   .output-content[_ngcontent-%COMP%]   .no-outputs[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  height: 200px;\\n  color: var(--text-secondary);\\n  font-size: 16px;\\n  text-align: center;\\n  background-color: var(--dashboard-bg-lighter);\\n  border-radius: 8px;\\n  border: 2px dashed var(--dashboard-border-light);\\n}\\n.agent-output[_ngcontent-%COMP%]   .output-content[_ngcontent-%COMP%]   .output-card[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n  border: 1px solid var(--dashboard-border-light);\\n  border-radius: 8px;\\n  overflow: hidden;\\n  background-color: var(--card-bg);\\n  box-shadow: 0 2px 4px var(--card-shadow);\\n  padding: 1rem;\\n}\\n.agent-output[_ngcontent-%COMP%]   .output-content[_ngcontent-%COMP%]   .output-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  font-weight: 700;\\n  margin-bottom: 12px;\\n  color: var(--text-color);\\n}\\n.agent-output[_ngcontent-%COMP%]   .output-content[_ngcontent-%COMP%]   .output-card[_ngcontent-%COMP%]   .output-card-content[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n.agent-output[_ngcontent-%COMP%]   .output-content[_ngcontent-%COMP%]   .output-card[_ngcontent-%COMP%]   .output-card-content[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.agent-output[_ngcontent-%COMP%]   .output-content[_ngcontent-%COMP%]   .output-card[_ngcontent-%COMP%]   .output-card-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 8px 0;\\n  color: var(--text-color);\\n  line-height: 1.5;\\n}\\n\\n.output-text[_ngcontent-%COMP%] {\\n  white-space: pre-wrap;\\n  word-break: break-word;\\n  font-family: \\\"Courier New\\\", monospace;\\n  font-size: 14px;\\n  line-height: 1.5;\\n  background-color: #f8f9fa;\\n  padding: 16px;\\n  border-radius: 4px;\\n  border: 1px solid #e9ecef;\\n  margin: 8px 0;\\n  max-height: 400px;\\n  overflow-y: auto;\\n  color: #333;\\n}\\n\\n@media (max-width: 1024px) {\\n  .workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%] {\\n    gap: 16px;\\n    height: 94vh;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%] {\\n    padding: 16px 20px;\\n  }\\n  .workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-title[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 20px;\\n  }\\n  .workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 12px;\\n    padding: 12px;\\n  }\\n  .workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .left-panel[_ngcontent-%COMP%] {\\n    flex: 1;\\n    min-height: 400px;\\n  }\\n  .workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%] {\\n    flex: 1;\\n    min-height: 300px;\\n  }\\n  .workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 12px;\\n    padding: 16px;\\n  }\\n  .workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .pipeline-steps-section[_ngcontent-%COMP%], \\n   .workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .execution-logs-section[_ngcontent-%COMP%] {\\n    flex: 1;\\n    min-height: 200px;\\n    border-radius: 16px;\\n    background-color: #F7F8F9;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%] {\\n    padding: 12px 16px;\\n  }\\n  .workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-title[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n  }\\n  .workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%] {\\n    gap: 8px;\\n  }\\n  .workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%], \\n   .workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .edit-button[_ngcontent-%COMP%] {\\n    padding: 8px 12px;\\n    font-size: 14px;\\n  }\\n  .workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%] {\\n    gap: 8px;\\n    padding: 8px;\\n  }\\n  .workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .left-panel[_ngcontent-%COMP%] {\\n    flex: 1;\\n    min-height: 350px;\\n  }\\n  .workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%] {\\n    flex: 1;\\n    min-height: 250px;\\n  }\\n  .workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 12px;\\n    padding: 12px;\\n  }\\n  .workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .pipeline-steps-section[_ngcontent-%COMP%], \\n   .workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .execution-logs-section[_ngcontent-%COMP%] {\\n    flex: 1;\\n    min-height: 180px;\\n    border-radius: 16px;\\n    background-color: #F7F8F9;\\n  }\\n  .workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .pipeline-steps-section[_ngcontent-%COMP%]   .pipeline-agents-list[_ngcontent-%COMP%], \\n   .workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .pipeline-steps-section[_ngcontent-%COMP%]   .logs-content[_ngcontent-%COMP%], \\n   .workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .execution-logs-section[_ngcontent-%COMP%]   .pipeline-agents-list[_ngcontent-%COMP%], \\n   .workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .execution-logs-section[_ngcontent-%COMP%]   .logs-content[_ngcontent-%COMP%] {\\n    padding: 12px 16px;\\n  }\\n  .workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .pipeline-steps-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], \\n   .workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .execution-logs-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n    padding: 16px 20px 12px 20px;\\n  }\\n  .workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .execution-monitor-header[_ngcontent-%COMP%] {\\n    padding: 16px 20px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return WorkflowExecutionComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "Subject", "takeUntil", "FormsModule", "Validators", "ChatInterfaceComponent", "ButtonComponent", "IconComponent", "TabsComponent", "environment", "workflowConstants", "ExecutionStatus", "WorkflowPlaygroundComponent", "i0", "ɵɵelementStart", "ɵɵlistener", "WorkflowExecutionComponent_div_9_Template_ava_button_click_1_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "executeWorkflow", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c1", "ɵɵtext", "ɵɵelement", "ɵɵtemplate", "WorkflowExecutionComponent_div_17_div_17_span_6_Template", "ɵɵclassProp", "i_r4", "ɵɵtextInterpolate", "agent_r3", "name", "i_r6", "ɵɵstyleProp", "log_r5", "color", "content", "WorkflowExecutionComponent_div_17_div_26_Template_button_click_1_listener", "_r7", "toggleShowAllLogs", "ɵɵtextInterpolate1", "showAllLogs", "workflowLogs", "length", "displayedLogsCount", "WorkflowExecutionComponent_div_17_div_27_Template_button_click_1_listener", "_r8", "WorkflowExecutionComponent_div_17_div_17_Template", "WorkflowExecutionComponent_div_17_div_25_Template", "WorkflowExecutionComponent_div_17_div_26_Template", "WorkflowExecutionComponent_div_17_div_27_Template", "progress", "agents", "displayedLogs", "hasMoreLogs", "resMessage", "output_r10", "raw", "description", "expected_output", "summary", "WorkflowExecutionComponent_div_18_div_9_div_4_Template", "WorkflowExecutionComponent_div_18_div_9_div_5_Template", "WorkflowExecutionComponent_div_18_div_9_div_6_Template", "WorkflowExecutionComponent_div_18_div_9_div_7_Template", "i_r11", "WorkflowExecutionComponent_div_18_Template_ava_button_userClick_5_listener", "_r9", "exportResults", "WorkflowExecutionComponent_div_18_div_7_Template", "WorkflowExecutionComponent_div_18_div_8_Template", "WorkflowExecutionComponent_div_18_div_9_Template", "taskMessage", "WorkflowExecutionComponent", "route", "router", "workflowService", "inputExtractorService", "tokenStorage", "loaderService", "formBuilder", "navigationTabs", "id", "label", "workflowId", "workflowName", "constants", "chatInterfaceComp", "activityLogs", "activityProgress", "executionDetails", "isRunning", "status", "notStarted", "chatMessages", "isProcessingChat", "inputText", "agentOutputs", "workflowForm", "fileType", "executionStartTime", "executionCompleted", "executionId", "enableStreamingLog", "enableLogStreaming", "isExecutionComplete", "progressInterval", "destroy$", "selectedTab", "demoTabs", "disabled", "errorMsg", "isJsonValid", "disable<PERSON>hat", "selectedFiles", "workflowAgents", "userInputList", "isLoading", "loaderColor", "inputFieldOrder", "currentInputIndex", "activeTabId", "isPlaygroundCollapsed", "pipelineAgents", "constructor", "ngOnInit", "disable<PERSON><PERSON><PERSON>", "crypto", "randomUUID", "paramMap", "pipe", "subscribe", "params", "get", "loadWorkflow", "navigate", "ngOnDestroy", "next", "complete", "<PERSON><PERSON><PERSON><PERSON>", "onTabChange", "event", "console", "log", "from", "text", "group", "getWorkflowById", "res", "extractInputField", "initializeForm", "startInputCollection", "initializeDemoLogs", "error", "err", "addAiResponse", "getWorkflowDetails", "getOneWorkflow", "pipeLineAgents", "convertToAgentData", "warn", "e", "PLACEHOLDER_PATTERNS", "placeholderM<PERSON>", "for<PERSON>ach", "pipelineAgent", "<PERSON><PERSON><PERSON>", "agent", "agentDescription", "task", "matches", "matchAll", "match", "placeholder", "placeholderInput", "Set", "inputs", "add", "Object", "entries", "map", "slice", "join", "at", "input", "isImageInput", "variableName", "trim", "startsWith", "addControl", "control", "required", "controlName", "keys", "controls", "isInputValid", "valid", "startFakeProgress", "setInterval", "stopFakeProgress", "clearInterval", "setTimeout", "handleChatMessage", "message", "field", "setValue", "promptForCurrentField", "saveLogs", "section", "data", "timestamp", "downloadAsFile", "JSON", "stringify", "filename", "type", "blob", "Blob", "url", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "click", "revokeObjectURL", "handleControlAction", "action", "navigateBack", "editWorkflow", "logExecutionStatus", "delay", "workflowExecProcessing", "push", "getWorkflowLogs", "workflowLogConnect", "validate<PERSON><PERSON>", "output", "parsedOutput", "parse", "payload", "FormData", "queryString", "value", "a", "running", "file", "append", "getDaUsername", "pipeLineId", "userInputs", "user", "workflowResponse", "pipeline", "workflowExecComplete", "tasksOutputs", "Array", "isArray", "Math", "random", "toString", "substring", "title", "Date", "toISOString", "completed", "detail", "workflowLogDisconnect", "workflowLogFailed", "handleAttachment", "onAttachmentsSelected", "files", "onImageSelected", "reader", "FileReader", "onload", "base64String", "result", "readAsDataURL", "onPlaygroundBackClicked", "onPlaygroundCollapseToggled", "isCollapsed", "onAgentInputChanged", "find", "agentId", "inputIndex", "onAgentFileSelected", "onMessageSent", "inputName", "demoLogs", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "WorkflowService", "i3", "WorkflowInputExtractorService", "i4", "TokenStorageService", "LoaderService", "i5", "FormBuilder", "selectors", "viewQuery", "WorkflowExecutionComponent_Query", "rf", "ctx", "WorkflowExecutionComponent_Template_app_workflow_playground_backClicked_8_listener", "WorkflowExecutionComponent_Template_app_workflow_playground_collapseToggled_8_listener", "$event", "WorkflowExecutionComponent_Template_app_workflow_playground_agentInputChanged_8_listener", "WorkflowExecutionComponent_Template_app_workflow_playground_agentFileSelected_8_listener", "WorkflowExecutionComponent_Template_app_workflow_playground_messageSent_8_listener", "WorkflowExecutionComponent_div_9_Template", "WorkflowExecutionComponent_Template_ava_tabs_tabChange_13_listener", "WorkflowExecutionComponent_div_17_Template", "WorkflowExecutionComponent_div_18_Template", "WorkflowExecutionComponent_div_19_Template", "_c0", "i6", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\pages\\workflows\\workflow-execution\\workflow-execution.component.ts", "C:\\console\\aava-ui-web\\projects\\shared\\pages\\workflows\\workflow-execution\\workflow-execution.component.html"], "sourcesContent": ["import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { CommonModule } from '@angular/common';\r\nimport { Subject, takeUntil } from 'rxjs';\r\nimport { Form<PERSON>uilder, FormGroup, FormsModule, Validators } from '@angular/forms';\r\n\r\n// Import child components\r\nimport { ChatInterfaceComponent } from '@shared/components/chat-interface/chat-interface.component';\r\nimport { ChatMessage } from '@shared/components/chat-window/chat-window.component';\r\nimport {\r\n  AgentActivityComponent,\r\n} from './components/agent-activity/agent-activity.component';\r\n// Removed AgentOutputComponent - now using inline output display\r\nimport {\r\n  ButtonComponent,\r\n  IconComponent,\r\n  TabItem,\r\n  TabsComponent,\r\n} from '@ava/play-comp-library';\r\nimport { WorkflowService } from '@shared/services/workflow.service';\r\nimport { WorkflowInputExtractorService } from '@shared/services/workflow-input-extractor.service';\r\nimport { environment } from '@shared/environments/environment';\r\nimport workflowConstants from './../constants/workflows.json';\r\nimport { TokenStorageService, LoaderService } from '@shared/index';\r\nimport { AvaTab } from '@shared/models/tab.model';\r\nimport { ExecutionStatus, ActivityLog } from '@shared/models/execution.model';\r\nimport { AgentActivityExecutionDetails } from './components/agent-activity/agent-activity.component';\r\nimport { WorkflowPlaygroundComponent, AgentData } from './components/workflow-playground/workflow-playground.component';\r\n\r\n// Define WorkflowOutputItem interface locally to avoid naming conflicts\r\nexport interface WorkflowOutputItem {\r\n  id: string;\r\n  title: string;\r\n  content: string;\r\n  agentName: string;\r\n  timestamp: string;\r\n  type?: 'code' | 'text' | 'json' | 'markdown';\r\n  description: string;\r\n  expected_output: string;\r\n  summary: string;\r\n  raw: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-workflow-execution',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    ChatInterfaceComponent,\r\n    AgentActivityComponent,\r\n    TabsComponent,\r\n    ButtonComponent,\r\n    IconComponent,\r\n    WorkflowPlaygroundComponent,\r\n  ],\r\n  templateUrl: './workflow-execution.component.html',\r\n  styleUrls: ['./workflow-execution.component.scss'],\r\n})\r\nexport class WorkflowExecutionComponent implements OnInit, OnDestroy {\r\n  navigationTabs: TabItem[] = [\r\n    { id: 'nav-home', label: 'Execution' },\r\n    { id: 'nav-products', label: 'Output' },\r\n    { id: 'nav-services', label: 'Configuration' },\r\n  ];\r\n  // Workflow details\r\n  workflowId: string | null = null;\r\n  workflowName: string = 'Workflow';\r\n\r\n  constants = workflowConstants as Record<string, any>;\r\n\r\n  @ViewChild(ChatInterfaceComponent, { static: false })\r\n  chatInterfaceComp!: ChatInterfaceComponent;\r\n\r\n  // Activity logs\r\n  activityLogs: ActivityLog[] = [];\r\n  activityProgress: number = 0;\r\n  executionDetails?: AgentActivityExecutionDetails;\r\n  isRunning: boolean = false;\r\n  status: ExecutionStatus = ExecutionStatus.notStarted;\r\n\r\n  // Chat messages\r\n  chatMessages: ChatMessage[] = [];\r\n  isProcessingChat: boolean = false;\r\n  inputText = '';\r\n\r\n  // Agent outputs\r\n  agentOutputs: WorkflowOutputItem[] = [];\r\n  public workflowForm!: FormGroup;\r\n  public fileType : string = '.zip';\r\n\r\n  // Execution state\r\n  executionStartTime: Date | null = null;\r\n  executionCompleted: boolean = false;\r\n  executionId!: string;\r\n\r\n  public workflowLogs: any[] = [];\r\n  public displayedLogsCount: number = 10;\r\n  public showAllLogs: boolean = false;\r\n  enableStreamingLog = environment.enableLogStreaming || 'all';\r\n\r\n  public isExecutionComplete: boolean = false;\r\n  progressInterval: any;\r\n\r\n  // Component lifecycle\r\n  private destroy$ = new Subject<void>();\r\n  selectedTab: string = 'Agent Activity';\r\n  demoTabs: AvaTab[] = [\r\n    { id: 'activity', label: 'Agent Activity' },\r\n    { id: 'agents', label: 'Agent Output' },\r\n    { id: 'preview', label: 'Preview', disabled: true },\r\n  ];\r\n  errorMsg = false;\r\n  resMessage: any;\r\n  taskMessage: {\r\n    description: any;\r\n    summary: any;\r\n    raw: any;\r\n    expected_output: any;\r\n  }[] = [];\r\n  isJsonValid = false;\r\n  disableChat : boolean = false;\r\n  selectedFiles: File[] = [];\r\n  workflowAgents: any[] = [];\r\n  userInputList: any[] = [];\r\n  progress = 0;\r\n  isLoading = false;\r\n  loaderColor: string = '';\r\n\r\n  inputFieldOrder: string[] = [];\r\n  currentInputIndex: number = 0;\r\n  activeTabId: string = 'nav-home';\r\n  \r\n\r\n  // New properties for workflow playground\r\n  agents: AgentData[] = [];\r\n  isPlaygroundCollapsed: boolean = false;\r\n  pipelineAgents: any[] = [];\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private workflowService: WorkflowService,\r\n    private inputExtractorService: WorkflowInputExtractorService,\r\n    private tokenStorage: TokenStorageService,\r\n    private loaderService: LoaderService,\r\n    private formBuilder: FormBuilder,\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.loaderService.disableLoader();\r\n    this.selectedTab = 'Agent Activity';\r\n    this.executionId = crypto.randomUUID();\r\n    // Get workflow ID from route params\r\n    this.route.paramMap.pipe(takeUntil(this.destroy$)).subscribe((params) => {\r\n      this.workflowId = params.get('id');\r\n      if (this.workflowId) {\r\n        this.loadWorkflow(this.workflowId);\r\n      } else {\r\n        // No workflow ID, redirect back to workflows page\r\n        this.router.navigate(['/build/workflows']);\r\n      }\r\n    });\r\n    // this.executeWorkflow()\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.destroy$.next();\r\n    this.destroy$.complete();\r\n    this.loaderService.enableLoader();\r\n  }\r\n  onTabChange(event: { id: string, label: string }) {\r\n    this.selectedTab = event.label;\r\n    this.activeTabId = event.id;\r\n    console.log('Tab changed:', event);\r\n  }\r\n\r\n  // Load workflow data\r\n  loadWorkflow(id: string): void {\r\n    // In a real app, this would fetch the workflow from a service\r\n    console.log(`Loading workflow with ID: ${id}`);\r\n    this.chatMessages = [\r\n      {\r\n        from: 'ai',\r\n        text: 'I am your workflow assistant. I will help you in executing this workflow.',\r\n      } as ChatMessage,\r\n    ]\r\n    this.workflowForm = this.formBuilder.group({});\r\n    \r\n    // Load workflow data for chat interface\r\n    this.workflowService.getWorkflowById(id).subscribe({\r\n        next: (res) => {\r\n        this.workflowAgents = res.workflowAgents;\r\n        this.userInputList = this.extractInputField(this.workflowAgents);\r\n        if(this.userInputList.length === 0){\r\n          this.disableChat = true;\r\n        }\r\n        this.workflowName = res.name;\r\n        this.initializeForm();\r\n        this.startInputCollection();\r\n        this.initializeDemoLogs(); // Initialize demo logs for testing\r\n      },\r\n        error: (err) => {\r\n          this.disableChat = true;\r\n          this.chatInterfaceComp.addAiResponse('No input is required for this workflow. Click on send button to execute the workflow.');\r\n          console.log(err);\r\n        }\r\n    });\r\n\r\n    // Also load workflow details for the new playground component\r\n    this.getWorkflowDetails(id);\r\n\r\n  }\r\n\r\n  // New method to get workflow details using workflow service\r\n  public getWorkflowDetails(id: string) {\r\n    this.workflowService.getOneWorkflow(id).subscribe({\r\n      next: (res: any) => {\r\n        if (res?.pipeLineAgents?.length) {\r\n          this.pipelineAgents = res.pipeLineAgents;\r\n          this.workflowName = res.name;\r\n\r\n          // Convert pipeline agents to AgentData format\r\n          try {\r\n            this.agents = this.inputExtractorService.convertToAgentData(this.pipelineAgents);\r\n            console.log('Converted agents:', this.agents);\r\n          } catch (error) {\r\n            console.error('Error converting pipeline agents to AgentData:', error);\r\n            this.agents = [];\r\n          }\r\n\r\n          // Extract input fields for form initialization\r\n          this.userInputList = this.extractInputField(res.pipeLineAgents);\r\n          this.initializeForm();\r\n        } else {\r\n          console.warn('No pipeline agents found in workflow response');\r\n          this.agents = [];\r\n        }\r\n      },\r\n      error: (e: any) => console.error(e)\r\n    });\r\n  }\r\n\r\n  public extractInputField(pipeLineAgents: any) {\r\n    const PLACEHOLDER_PATTERNS =  /(%\\d+\\$s)|\\{\\{([a-zA-Z0-9-_]+)\\}\\}/g;\r\n    const placeholderMap: { [key: string]: { agents: Set<string>; inputs: Set<string> } } = {};\r\n\r\n    pipeLineAgents.forEach((pipelineAgent: any) => {\r\n      const agentName = pipelineAgent?.agent?.name;\r\n      const agentDescription = pipelineAgent?.agent?.task?.description;\r\n      const matches = agentDescription?.matchAll(PLACEHOLDER_PATTERNS) || [];\r\n\r\n      for (const match of matches) {\r\n        const placeholder = match[1] || match[2];\r\n        const placeholderInput = match[0];\r\n        if (!placeholderMap[placeholder]) {\r\n          placeholderMap[placeholder] = { agents: new Set(), inputs: new Set() };;\r\n        }\r\n        if (agentName) {\r\n          placeholderMap[placeholder].agents.add(agentName);\r\n        }\r\n        placeholderMap[placeholder].inputs.add(placeholderInput);\r\n      }\r\n    })\r\n\r\n    return Object.entries(placeholderMap).map(([placeholder, { agents, inputs }]) => ({\r\n      name: [...agents].length > 2\r\n        ? `${[...agents].slice(0, -1).join(\", \")} and ${[...agents].at(-1)}`\r\n        : [...agents].join(\" and \"),\r\n      placeholder,\r\n      input: [...inputs][0],\r\n    }));\r\n  }\r\n\r\n  public isImageInput(input: string): boolean {\r\n    const match = input.match(/{{(.*?)}}/);\r\n    if (match && match[1]) {\r\n      const variableName = match[1].trim();\r\n      return variableName.startsWith('image') || variableName.startsWith('Image');\r\n    }\r\n    return false;\r\n  }\r\n\r\n  public initializeForm() {\r\n    console.log('Initializing form with userInputList:', this.userInputList);\r\n    this.userInputList.forEach((label: any) => {\r\n      console.log('Adding form control for:', label.input);\r\n      this.workflowForm.addControl(label.input, this.formBuilder.control('', Validators.required));\r\n    });\r\n\r\n    // Also add form controls for agent inputs to ensure compatibility\r\n    if (this.agents) {\r\n      this.agents.forEach(agent => {\r\n        if (agent.inputs) {\r\n          agent.inputs.forEach(input => {\r\n            const controlName = input.placeholder;\r\n            if (!this.workflowForm.get(controlName)) {\r\n              console.log('Adding additional form control for agent input:', controlName);\r\n              this.workflowForm.addControl(controlName, this.formBuilder.control('', Validators.required));\r\n            }\r\n          });\r\n        }\r\n      });\r\n    }\r\n\r\n    console.log('Final form controls:', Object.keys(this.workflowForm.controls));\r\n  }\r\n\r\n  public isInputValid() {\r\n    return this.workflowForm.valid && this.workflowId;\r\n  }\r\n\r\n  startFakeProgress() {\r\n    this.progress = 0;\r\n    this.progressInterval = setInterval(() => {\r\n      if (this.progress < 90) {\r\n        this.progress += 5; // Increase slowly\r\n      }\r\n    }, 200); // Adjust speed\r\n  }\r\n\r\n  stopFakeProgress() {\r\n    clearInterval(this.progressInterval);\r\n    this.progress = 100;\r\n\r\n    setTimeout(() => {\r\n      this.isLoading = false;\r\n    }, 500); // Small delay to let user see 100%\r\n  }\r\n\r\n  // Handle new chat message from user\r\n  handleChatMessage(message: string): void {\r\n    // console.log('message ', message, 'is blank', message.trim() === '');\r\n    this.isProcessingChat = true;\r\n    if(message.trim() === ''){\r\n      if(this.inputFieldOrder.length === 0){\r\n        this.chatInterfaceComp.addAiResponse('Executing the workflow...');\r\n        this.executeWorkflow();\r\n      }\r\n      return;\r\n    }\r\n\r\n    if(this.isExecutionComplete || this.currentInputIndex===this.inputFieldOrder.length){\r\n        this.chatInterfaceComp.addAiResponse('Executing the workflow...');\r\n        this.executeWorkflow();\r\n      return;\r\n    }\r\n\r\n    const field = this.inputFieldOrder[this.currentInputIndex];\r\n    if (this.isImageInput(field)) {\r\n      // Ignore text input, wait for file input\r\n      this.chatInterfaceComp.addAiResponse(`Please upload an image file for ${field}`);\r\n      return;\r\n    }\r\n\r\n    this.workflowForm.get(field)?.setValue(message);\r\n    this.currentInputIndex++;\r\n\r\n    if (this.currentInputIndex < this.inputFieldOrder.length) {\r\n      this.promptForCurrentField();\r\n    } else {\r\n      this.chatInterfaceComp.addAiResponse('Thank you for the input! Executing the workflow...');\r\n      this.executeWorkflow();\r\n    }\r\n  }\r\n\r\n  // Save execution logs\r\n  saveLogs(): void {\r\n    console.log('Saving execution logs...');\r\n    // This would typically save to a service\r\n  }\r\n\r\n  // Export results\r\n  exportResults(section: 'activity' | 'output'): void {\r\n    console.log(`Exporting ${section} data...`);\r\n\r\n    if (section === 'activity') {\r\n      const data = this.activityLogs\r\n        .map((log) => `[${log.timestamp}] ${log.message}`)\r\n        .join('\\n');\r\n      this.downloadAsFile(data, 'workflow-activity-logs.txt', 'text/plain');\r\n    } else {\r\n      const data = JSON.stringify(this.agentOutputs, null, 2);\r\n      this.downloadAsFile(data, 'workflow-outputs.json', 'application/json');\r\n    }\r\n  }\r\n\r\n  // Helper method to download data as a file\r\n  private downloadAsFile(data: string, filename: string, type: string): void {\r\n    const blob = new Blob([data], { type });\r\n    const url = URL.createObjectURL(blob);\r\n    const link = document.createElement('a');\r\n    link.href = url;\r\n    link.download = filename;\r\n    link.click();\r\n    URL.revokeObjectURL(url);\r\n  }\r\n\r\n  // Handle controls for execution\r\n  handleControlAction(action: 'play' | 'pause' | 'stop'): void {\r\n    console.log(`Control action: ${action}`);\r\n    // In a real app, this would control the workflow execution\r\n\r\n    if (action === 'play') {\r\n      this.isRunning = true;\r\n    } else if (action === 'pause' || action === 'stop') {\r\n      this.isRunning = false;\r\n    }\r\n  }\r\n\r\n  // Navigate back to workflow listing\r\n  navigateBack(): void {\r\n    this.router.navigate(['/build/workflows']);\r\n  }\r\n\r\n  // Navigate to edit workflow\r\n  editWorkflow(): void {\r\n    if (this.workflowId) {\r\n      this.router.navigate(['/build/workflows/edit', this.workflowId]);\r\n    }\r\n  }\r\n\r\n  public logExecutionStatus(delay: number = 2000) {\r\n    setTimeout(() => {\r\n      if (!this.isExecutionComplete) {\r\n        console.log(this.constants);\r\n        console.log(this.constants['labels'].workflowExecProcessing);\r\n        this.workflowLogs.push({\r\n          content: this.constants['labels'].workflowExecProcessing,\r\n          color: '#F9DB24',\r\n        });\r\n      }\r\n    }, delay);\r\n  }\r\n\r\n  // public parseAnsiString(ansiString: string) {\r\n  //   const regex = ansiRegex();\r\n  //   const parts = ansiString.split(regex);\r\n  //   const matches = [...ansiString.matchAll(regex)];\r\n  //   parts.forEach((part, index) => {\r\n  //     if (part.trim() !== '') {\r\n  //       let colorCode = matches[index - 1][0];\r\n  //       if (index - 2 >= 0 && matches[index - 2]?.includes('\\u001b[1m')) {\r\n  //         colorCode = `\\u001b[1m${colorCode}`;\r\n  //       }\r\n  //       this.workflowLogs.push({\r\n  //         content: part,\r\n  //         color: this.colorMap[colorCode] || 'white',\r\n  //       });\r\n  //     }\r\n  //   });\r\n  // }\r\n\r\n  public getWorkflowLogs(executionId: string) {\r\n    console.log('Attempting to connect to WebSocket for executionId:', executionId);\r\n\r\n    try {\r\n      this.workflowService\r\n        .workflowLogConnect(executionId)\r\n        .pipe(takeUntil(this.destroy$))\r\n        .subscribe({\r\n          next: (message) => {\r\n            console.log('WebSocket message received:', message);\r\n            const { content, color } = message;\r\n            if (color) {\r\n              this.workflowLogs.push({ content, color });\r\n            } else if (this.enableStreamingLog === 'all') {\r\n              // this.parseAnsiString(content);\r\n              this.workflowLogs.push({ content, color: '#6B7280' });\r\n            }\r\n          },\r\n          error: (err) => {\r\n            console.error('WebSocket connection error:', err);\r\n            this.workflowLogs.push({\r\n              content: 'WebSocket connection failed. Using demo logs instead.',\r\n              color: 'red',\r\n            });\r\n          },\r\n          complete: () => {\r\n            this.logExecutionStatus();\r\n            console.log('WebSocket connection closed');\r\n          },\r\n        });\r\n    } catch (error) {\r\n      console.error('Failed to establish WebSocket connection:', error);\r\n      this.workflowLogs.push({\r\n        content: 'Failed to connect to log streaming service. Using demo logs.',\r\n        color: 'red',\r\n      });\r\n    }\r\n  }\r\n\r\n  // public parseAnsiString(ansiString: string) {\r\n  //   const regex = ansiRegex();\r\n  //   const parts = ansiString.split(regex);\r\n  //   const matches = [...ansiString.matchAll(regex)];\r\n  //   parts.forEach((part, index) => {\r\n  //     if (part.trim() !== '') {\r\n  //       let colorCode = matches[index-1][0];\r\n  //       if(index - 2 >= 0 && matches[index-2]?.includes('\\u001b[1m')) {\r\n  //         colorCode = `\\u001b[1m${colorCode}`;\r\n  //       }\r\n  //       this.workflowLogs.push({\r\n  //         content: part, \r\n  //         color: this.colorMap[colorCode] || 'white', \r\n  //       });\r\n  //     }\r\n  //   });\r\n  // }\r\n\r\n  public validateJson(output: string): any | null {\r\n    this.isJsonValid = false;\r\n    try {\r\n      const parsedOutput = JSON.parse(output);\r\n      this.isJsonValid = true;\r\n      return parsedOutput;\r\n    } catch (e) {\r\n      return null;\r\n    }\r\n  }\r\n\r\n  public executeWorkflow() {\r\n    let payload: FormData | Record<string, any> = new FormData();\r\n    let queryString = '';\r\n\r\n    console.log('Executing workflow with form value:', this.workflowForm.value);\r\n    console.log('Form controls:', Object.keys(this.workflowForm.controls));\r\n    console.log('Agent inputs:', this.agents.map(a => ({ name: a.name, inputs: a.inputs })));\r\n\r\n    this.status = ExecutionStatus.running;\r\n    if (this.selectedFiles.length) {\r\n      this.selectedFiles.forEach((file) => {\r\n        payload.append('files', file);\r\n      });\r\n      payload.append('workflowId', this.workflowId);\r\n      payload.append('userInputs', JSON.stringify(this.workflowForm.value));\r\n      payload.append('user', this.tokenStorage.getDaUsername());\r\n      payload.append('executionId', this.executionId);\r\n      queryString = '/files';\r\n    } else {\r\n      payload = {\r\n        pipeLineId: this.workflowId,\r\n        userInputs: this.workflowForm.value,\r\n        executionId: this.executionId,\r\n        user: this.tokenStorage.getDaUsername(),\r\n      };\r\n    }\r\n\r\n    console.log('Final payload:', payload);\r\n\r\n    this.getWorkflowLogs(this.executionId);\r\n    this.startFakeProgress();\r\n\r\n    this.workflowService\r\n      .executeWorkflow(payload, queryString)\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe({\r\n        next: (res) => {\r\n          this.isProcessingChat = false;\r\n          this.isRunning = false;\r\n          this.chatInterfaceComp.addAiResponse(res?.message || \"Workflow execution completed successfully!\");\r\n\r\n          console.log('Workflow execution response:', res);\r\n\r\n          // Check if we have workflow response and output\r\n          if (res?.workflowResponse?.pipeline) {\r\n            const pipeline = res.workflowResponse.pipeline;\r\n            console.log('Pipeline data:', pipeline);\r\n\r\n            this.isExecutionComplete = true;\r\n            this.workflowLogs.push({\r\n              content: this.constants['labels'].workflowExecComplete,\r\n              color: '#0F8251',\r\n            });\r\n            this.errorMsg = false;\r\n\r\n            // Set the output message\r\n            this.resMessage = pipeline.output || 'Workflow completed successfully';\r\n            console.log('Setting resMessage to:', this.resMessage);\r\n\r\n            // Always process task outputs if they exist (remove the length check)\r\n            if (pipeline.tasksOutputs && Array.isArray(pipeline.tasksOutputs)) {\r\n              console.log('Processing tasksOutputs:', pipeline.tasksOutputs);\r\n              this.agentOutputs = pipeline.tasksOutputs.map((task: any) => {\r\n                return {\r\n                  id: task?.id || Math.random().toString(36).substring(2, 11),\r\n                  title: task?.title || 'Task Output',\r\n                  content: task?.raw || task?.content || '',\r\n                  agentName: task?.agentName || 'Agent',\r\n                  timestamp: task?.timestamp || new Date().toISOString(),\r\n                  type: task?.type || 'text',\r\n                  description: task?.description || '',\r\n                  expected_output: task?.expected_output || '',\r\n                  summary: task?.summary || '',\r\n                  raw: task?.raw || '',\r\n                };\r\n              });\r\n              console.log('Agent outputs for display:', this.agentOutputs);\r\n              console.log('Agent outputs length:', this.agentOutputs.length);\r\n            } else {\r\n              console.log('No tasksOutputs found, checking for main output');\r\n              if (pipeline.output) {\r\n                // If no task outputs but we have main output, create a single output item\r\n                console.log('Creating output from main pipeline output');\r\n                this.agentOutputs = [{\r\n                  id: 'main-output',\r\n                  title: 'Workflow Output',\r\n                  content: pipeline.output,\r\n                  agentName: 'Workflow',\r\n                  timestamp: new Date().toISOString(),\r\n                  type: 'text',\r\n                  description: 'Main workflow output',\r\n                  expected_output: 'Workflow execution result',\r\n                  summary: 'Workflow completed successfully',\r\n                  raw: pipeline.output,\r\n                }];\r\n                console.log('Created fallback agent output:', this.agentOutputs);\r\n              }\r\n            }\r\n\r\n            // Process task messages - this should always run if tasksOutputs exists\r\n            if (pipeline.tasksOutputs && Array.isArray(pipeline.tasksOutputs)) {\r\n              this.taskMessage = pipeline.tasksOutputs.map(\r\n                (task: {\r\n                  description: any;\r\n                  summary: any;\r\n                  raw: any;\r\n                  expected_output: any;\r\n                }) => {\r\n                  return {\r\n                    description: task.description,\r\n                    summary: task.summary,\r\n                    raw: task.raw,\r\n                    expected_output: task.expected_output,\r\n                  };\r\n                },\r\n              );\r\n              console.log('Task messages processed:', this.taskMessage);\r\n              console.log('Task messages length:', this.taskMessage.length);\r\n            } else {\r\n              console.log('No tasksOutputs found or not an array:', pipeline.tasksOutputs);\r\n            }\r\n\r\n            // if(\"file_download_url\" in res?.pipeline){\r\n            //   this.isFileWriter = true;\r\n            //   this.fileDownloadLink = res?.pipeline?.file_download_url;\r\n\r\n            //   if(!this.fileDownloadLink){\r\n            //     this.fileDownloadUrlError = [];\r\n            //     this.fileDownloadUrlError.push(\"Output file is not generated yet!\")\r\n            //   }\r\n            // }\r\n            // this.isAccordian = true\r\n          }\r\n          this.validateJson(this.resMessage);\r\n          this.status = ExecutionStatus.completed;\r\n          this.stopFakeProgress();\r\n          this.selectedFiles = [];\r\n        },\r\n        error: (error) => {\r\n          this.isExecutionComplete = true;\r\n          this.isProcessingChat = false;\r\n          this.errorMsg = true;\r\n          this.resMessage = error?.error?.detail;\r\n          this.workflowService.workflowLogDisconnect();\r\n          this.workflowLogs.push({\r\n            content: this.constants['labels'].workflowLogFailed,\r\n            color: 'red',\r\n          });\r\n          this.chatInterfaceComp.addAiResponse(\r\n            'Something went wrong, Workflow execution has failed.',\r\n          );\r\n          this.selectedFiles = [];\r\n          this.stopFakeProgress();\r\n          console.log('error is', error.message);\r\n        },\r\n      });\r\n  }\r\n\r\n  // public asyncExecutePipeline() {\r\n  //   const payload: FormData = new FormData();\r\n  //   if (this.selectedFiles?.length) {\r\n  //     for (const element of this.selectedFiles) {\r\n  //       payload.append('files', element)\r\n  //     }\r\n  //   }\r\n  //   payload.append('pipeLineId', String(this.workflowId));\r\n  //   payload.append('userInputs', JSON.stringify(this.workflowForm.value));\r\n  //   payload.append('user', this.tokenStorage.getDaUsername() || '');\r\n  //   payload.append('executionId', this.executionId);\r\n\r\n  //   this.workflowService.asyncExecutePipeline(payload).pipe().subscribe({\r\n  //     next: (res: any) => {\r\n  //       if(res) {\r\n  //         // res handling\r\n  //         console.log(res);\r\n  //       }\r\n  //     },\r\n  //     error: e => {\r\n  //       // error handling\r\n  //       console.log(e);\r\n  //     }\r\n  //   })\r\n  // }\r\n\r\n  handleAttachment() {\r\n    console.log('handleAttachment');\r\n  }\r\n\r\n  onAttachmentsSelected(files: File[]) {\r\n    if(this.currentInputIndex===this.inputFieldOrder.length || this.inputFieldOrder.length===0){\r\n      this.selectedFiles = files;\r\n      return;\r\n    }\r\n\r\n    const field = this.inputFieldOrder[this.currentInputIndex];\r\n    if(this.isImageInput(field)){\r\n      if (files && files.length > 0) {\r\n        this.onImageSelected(files[0]);\r\n      }\r\n    } else {\r\n      this.selectedFiles = files;\r\n    }\r\n  }\r\n\r\n  startInputCollection(){\r\n    this.inputFieldOrder = Object.keys(this.workflowForm.controls);\r\n    this.currentInputIndex = 0;\r\n    if (this.inputFieldOrder.length > 0) {\r\n      this.promptForCurrentField();\r\n    }\r\n    else{\r\n      this.disableChat = true;\r\n      this.chatInterfaceComp.addAiResponse('No input is required for this workflow. Click on send button to execute the workflow.');\r\n    }\r\n  }\r\n\r\n  promptForCurrentField() {\r\n    const field = this.inputFieldOrder[this.currentInputIndex];\r\n    if (this.isImageInput(field)) {\r\n      this.fileType = '.jpeg,.png,.jpg,.svg';\r\n      this.chatInterfaceComp.addAiResponse(`Please upload an image for ${field}`);\r\n      // UI should now show a file input for the user\r\n    } else {\r\n      this.fileType = '.zip'; // or whatever default you want for non-image\r\n      this.chatInterfaceComp.addAiResponse(`Please enter the value of ${field}`);\r\n    }\r\n  }\r\n\r\n  onImageSelected(file: File) {\r\n    const field = this.inputFieldOrder[this.currentInputIndex];\r\n    if (!this.isImageInput(field)) return;\r\n\r\n    const reader = new FileReader();\r\n    reader.onload = () => {\r\n      const base64String = (reader.result as string); \r\n      this.workflowForm.get(field)?.setValue(base64String);\r\n      this.currentInputIndex++;\r\n      if (this.currentInputIndex < this.inputFieldOrder.length) {\r\n        this.promptForCurrentField();\r\n      } else {\r\n        this.chatInterfaceComp.addAiResponse('Thank you! Running the workflow...');\r\n        this.executeWorkflow();\r\n      }\r\n    };\r\n    reader.readAsDataURL(file);\r\n  }\r\n\r\n  // Event handlers for workflow playground\r\n  onPlaygroundBackClicked(): void {\r\n    this.navigateBack();\r\n  }\r\n\r\n  onPlaygroundCollapseToggled(isCollapsed: boolean): void {\r\n    this.isPlaygroundCollapsed = isCollapsed;\r\n  }\r\n\r\n  onAgentInputChanged(event: {agentId: number, inputIndex: number, value: string}): void {\r\n    // Find the agent and update the input value\r\n    const agent = this.agents.find(a => a.id === event.agentId);\r\n    if (agent && agent.inputs && agent.inputs[event.inputIndex]) {\r\n      agent.inputs[event.inputIndex].value = event.value;\r\n\r\n      // Update the form control if it exists\r\n      const placeholder = agent.inputs[event.inputIndex].placeholder;\r\n      if (this.workflowForm.get(placeholder)) {\r\n        this.workflowForm.get(placeholder)?.setValue(event.value);\r\n      }\r\n    }\r\n  }\r\n\r\n  onAgentFileSelected(event: {agentId: number, inputIndex: number, files: File[]}): void {\r\n    // Find the agent and update the files\r\n    const agent = this.agents.find(a => a.id === event.agentId);\r\n    if (agent && agent.inputs && agent.inputs[event.inputIndex]) {\r\n      agent.inputs[event.inputIndex].files = event.files;\r\n\r\n      // Add files to selectedFiles array for execution\r\n      this.selectedFiles = [...this.selectedFiles, ...event.files];\r\n    }\r\n  }\r\n\r\n  onMessageSent(event: {agentId: number, inputIndex: number, value: string, files?: File[]}): void {\r\n    console.log('Message sent from agent:', event);\r\n\r\n    // Find the agent and update the input value\r\n    const agent = this.agents.find(a => a.id === event.agentId);\r\n    if (agent && agent.inputs && agent.inputs[event.inputIndex]) {\r\n      agent.inputs[event.inputIndex].value = event.value;\r\n\r\n      // Update files if provided\r\n      if (event.files) {\r\n        agent.inputs[event.inputIndex].files = event.files;\r\n        this.selectedFiles = [...this.selectedFiles, ...event.files];\r\n      }\r\n\r\n      // Update the form control if it exists\r\n      const placeholder = agent.inputs[event.inputIndex].placeholder;\r\n      console.log('Updating form field:', placeholder, 'with value:', event.value);\r\n      console.log('Form controls available:', Object.keys(this.workflowForm.controls));\r\n      console.log('Current form value before update:', this.workflowForm.value);\r\n\r\n      if (this.workflowForm.get(placeholder)) {\r\n        this.workflowForm.get(placeholder)?.setValue(event.value);\r\n        console.log('Form value after update:', this.workflowForm.value);\r\n      } else {\r\n        console.warn('Form control not found for placeholder:', placeholder);\r\n        // Try to add the control dynamically\r\n        this.workflowForm.addControl(placeholder, this.formBuilder.control(event.value));\r\n        console.log('Added new form control. Form value:', this.workflowForm.value);\r\n      }\r\n\r\n      // Log the input submission\r\n      this.workflowLogs.push({\r\n        content: `Agent \"${agent.name}\" - Input \"${agent.inputs[event.inputIndex].inputName}\": ${event.value}`,\r\n        color: '#10B981'\r\n      });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get the logs to display based on the current view state\r\n   */\r\n  get displayedLogs(): any[] {\r\n    if (this.showAllLogs) {\r\n      return this.workflowLogs;\r\n    }\r\n    return this.workflowLogs.slice(0, this.displayedLogsCount);\r\n  }\r\n\r\n  /**\r\n   * Toggle between showing limited logs and all logs\r\n   */\r\n  toggleShowAllLogs(): void {\r\n    this.showAllLogs = !this.showAllLogs;\r\n  }\r\n\r\n  /**\r\n   * Check if there are more logs to show\r\n   */\r\n  get hasMoreLogs(): boolean {\r\n    return this.workflowLogs.length > this.displayedLogsCount && !this.showAllLogs;\r\n  }\r\n\r\n  /**\r\n   * Initialize demo logs for testing the log display functionality\r\n   */\r\n  private initializeDemoLogs(): void {\r\n    // Only add demo logs if there are no existing logs\r\n    if (this.workflowLogs.length === 0) {\r\n      const demoLogs = [\r\n        { content: 'Workflow execution started', color: '#0F8251' },\r\n        { content: 'Loading pipeline configuration...', color: '#6B7280' },\r\n        { content: 'Initializing Test Case Normalizer agent', color: '#2563EB' },\r\n        { content: 'Processing input parameters', color: '#6B7280' },\r\n        { content: 'Agent 1 is currently working', color: '#F9DB24' },\r\n        { content: 'Analyzing test case structure...', color: '#6B7280' },\r\n        { content: 'Normalizing test case format', color: '#6B7280' },\r\n        { content: 'Validating output format', color: '#6B7280' },\r\n        { content: 'Test Case Normalizer completed successfully', color: '#0F8251' },\r\n        { content: 'Initializing ELT_OAMA Frameworkagent', color: '#2563EB' },\r\n        { content: 'Loading framework configuration', color: '#6B7280' },\r\n        { content: 'Processing framework parameters', color: '#6B7280' },\r\n        { content: 'Agent 2 is currently working', color: '#F9DB24' },\r\n        { content: 'Executing framework analysis...', color: '#6B7280' },\r\n        { content: 'Generating framework recommendations', color: '#6B7280' },\r\n        { content: 'ELT_OAMA Framework analysis completed', color: '#0F8251' },\r\n        { content: 'Initializing ELT_QE_HPX_Automated_Script agent', color: '#2563EB' },\r\n        { content: 'Loading automation script templates', color: '#6B7280' },\r\n        { content: 'Agent 3 is currently working', color: '#F9DB24' },\r\n        { content: 'Generating automated test scripts...', color: '#6B7280' },\r\n        { content: 'Validating script syntax and structure', color: '#6B7280' },\r\n        { content: 'Script generation completed successfully', color: '#0F8251' },\r\n        { content: 'All pipeline agents completed successfully', color: '#0F8251' },\r\n        { content: 'Workflow execution completed', color: '#0F8251' }\r\n      ];\r\n\r\n      this.workflowLogs = demoLogs;\r\n    }\r\n  }\r\n}\r\n", "<div class=\"workflow-execution-container\">\r\n  <!-- SVG Gradient Definitions for Icons -->\r\n  <svg width=\"0\" height=\"0\" style=\"position: absolute\">\r\n    <defs>\r\n      <linearGradient id=\"gradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"0%\">\r\n        <stop offset=\"0%\" stop-color=\"#6566CD\" />\r\n        <stop offset=\"100%\" stop-color=\"#F96CAB\" />\r\n      </linearGradient>\r\n    </defs>\r\n  </svg>\r\n  <div class=\"execution-content\" role=\"main\">\r\n    <!-- Left Panel: Workflow Playground with Execute Button -->\r\n    <div class=\"left-panel\" [class.collapsed]=\"isPlaygroundCollapsed\" role=\"region\" aria-label=\"Workflow Input Panel\">\r\n      <app-workflow-playground\r\n        [agents]=\"agents\"\r\n        [isCollapsed]=\"isPlaygroundCollapsed\"\r\n        [workflowName]=\"workflowName\"\r\n        (backClicked)=\"onPlaygroundBackClicked()\"\r\n        (collapseToggled)=\"onPlaygroundCollapseToggled($event)\"\r\n        (agentInputChanged)=\"onAgentInputChanged($event)\"\r\n        (agentFileSelected)=\"onAgentFileSelected($event)\"\r\n        (messageSent)=\"onMessageSent($event)\"\r\n        class=\"playground-component\"\r\n        role=\"region\"\r\n        aria-label=\"Workflow Playground\">\r\n      </app-workflow-playground>\r\n\r\n      <!-- Execute Button -->\r\n      <div class=\"execute-button-container\" *ngIf=\"!isPlaygroundCollapsed\">\r\n        <ava-button\r\n          label=\"Execute Workflow\"\r\n          variant=\"primary\"\r\n          size=\"large\"\r\n          [customStyles]=\"{\r\n            width: '100%',}\"\r\n          (click)=\"executeWorkflow()\"\r\n        >\r\n        </ava-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Right Panel: Three Sections (Execution, Output, Configuration) -->\r\n    <div class=\"right-panel\" [class.expanded]=\"isPlaygroundCollapsed\" role=\"region\" aria-label=\"Workflow Results\">\r\n      <!-- Tab Navigation -->\r\n      <div class=\"tabs-header\">\r\n        <div class=\"tabs-container\">\r\n          <ava-tabs\r\n            [tabs]=\"navigationTabs\"\r\n            [activeTabId]=\"activeTabId\"\r\n            variant=\"button\"\r\n            [showContentPanels]=\"false\"\r\n            (tabChange)=\"onTabChange($event)\"\r\n            ariaLabel=\"Workflow sections navigation\"\r\n          ></ava-tabs>\r\n        </div>\r\n        <div class=\"header-actions\">\r\n          <ava-button\r\n            label=\"Send for Approval\"\r\n            variant=\"primary\"\r\n            [customStyles]=\"{\r\n              background: 'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',\r\n              '--button-effect-color': '33, 90, 214',\r\n              'border-radius': '8px',\r\n              'box-shadow': 'none'\r\n            }\"\r\n            size=\"medium\"\r\n          >\r\n          </ava-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Content Sections -->\r\n      <div class=\"content-sections\">\r\n        <!-- Execution Section -->\r\n        <div *ngIf=\"activeTabId === 'nav-home'\" class=\"section-content execution-section\">\r\n          <!-- Execution Monitor Header -->\r\n          <div class=\"execution-monitor-header\">\r\n            <h2 class=\"monitor-title\">Execution Monitor</h2>\r\n            <div class=\"progress-section\">\r\n              <span class=\"progress-label\">Overall Progress</span>\r\n              <div class=\"progress-container\">\r\n                <div class=\"progress-bar\">\r\n                  <div class=\"progress-fill\" [style.width.%]=\"progress\"></div>\r\n                </div>\r\n                <span class=\"progress-text\">{{ progress }}%</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Two Column Layout: Pipeline Steps + Execution Logs -->\r\n          <div class=\"execution-content-grid\">\r\n            <!-- Left Column: Pipeline Steps -->\r\n            <div class=\"pipeline-steps-section\">\r\n              <h3>Pipeline Steps</h3>\r\n              <div class=\"pipeline-agents-list\">\r\n                <div\r\n                  *ngFor=\"let agent of agents; let i = index\"\r\n                  class=\"pipeline-agent-item\"\r\n                  [class.active]=\"i === 0\"\r\n                  [class.completed]=\"false\">\r\n                  <div class=\"agent-icon\">\r\n                    <ava-icon iconName=\"bot\" [iconSize]=\"20\" iconColor=\"#666\"></ava-icon>\r\n                  </div>\r\n                  <div class=\"agent-info\">\r\n                    <span class=\"agent-name\">{{ agent.name }}</span>\r\n                    <span class=\"agent-status\" *ngIf=\"i === 0\">Agent 1 is currently working</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Right Column: Execution Logs -->\r\n            <div class=\"execution-logs-section\">\r\n              <div class=\"logs-header\">\r\n                <h3>Execution Logs</h3>\r\n                <span class=\"logs-subtitle\">Agent 1's Output</span>\r\n              </div>\r\n              <div class=\"logs-content\">\r\n                <div class=\"log-entry\" *ngFor=\"let log of displayedLogs; let i = index\">\r\n                  <span class=\"log-number\">{{ (i + 1 < 10 ? '0' : '') + (i + 1) }}</span>\r\n                  <span class=\"log-message\" [style.color]=\"log.color\">{{ log.content }}</span>\r\n                </div>\r\n                <div class=\"show-more\" *ngIf=\"hasMoreLogs\">\r\n                  <button class=\"show-more-btn\" (click)=\"toggleShowAllLogs()\">\r\n                    {{ showAllLogs ? 'Show Less' : 'Show More (' + (workflowLogs.length - displayedLogsCount) + ' more)' }}\r\n                  </button>\r\n                </div>\r\n                <div class=\"show-less\" *ngIf=\"showAllLogs && workflowLogs.length > displayedLogsCount\">\r\n                  <button class=\"show-more-btn\" (click)=\"toggleShowAllLogs()\">Show Less</button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Output Section -->\r\n        <div *ngIf=\"activeTabId === 'nav-products'\" class=\"section-content output-section\">\r\n          <div class=\"agent-output\">\r\n            <div class=\"output-header\">\r\n              <h3>Agent Output</h3>\r\n              <ava-button\r\n                (userClick)=\"exportResults('output')\"\r\n                aria-label=\"Export agent outputs\"\r\n                title=\"Export agent outputs\"\r\n                variant=\"primary\"\r\n                size=\"medium\"\r\n                label=\"Export\"\r\n                [iconName]=\"'Download'\"\r\n                [iconPosition]=\"'right'\"\r\n                [iconColor]=\"'white'\">\r\n              </ava-button>\r\n            </div>\r\n\r\n            <div class=\"output-content\">\r\n              <!-- No outputs message -->\r\n              <div *ngIf=\"!resMessage && (!taskMessage || taskMessage.length === 0)\" class=\"no-outputs\">\r\n                No agent outputs available yet.\r\n              </div>\r\n\r\n              <!-- Main workflow output -->\r\n              <div *ngIf=\"resMessage\" class=\"output-card\">\r\n                <div class=\"output-card-content\">\r\n                  <h3>Workflow Output</h3>\r\n                  <pre class=\"output-text\">{{resMessage}}</pre>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- Task outputs -->\r\n              <div *ngFor=\"let output of taskMessage; let i = index\" class=\"output-card\">\r\n                <div class=\"output-card-header\">\r\n                  <h3>Agent Output {{i + 1}}</h3>\r\n                </div>\r\n\r\n                <div class=\"output-card-content\" *ngIf=\"output.raw\">\r\n                  <h4>Generated Output</h4>\r\n                  <pre class=\"output-text\">{{output.raw}}</pre>\r\n                </div>\r\n\r\n                <div class=\"output-card-content\" *ngIf=\"output.description\">\r\n                  <h4>Task Description</h4>\r\n                  <p>{{output.description}}</p>\r\n                </div>\r\n\r\n                <div class=\"output-card-content\" *ngIf=\"output.expected_output\">\r\n                  <h4>Expected Output</h4>\r\n                  <p>{{output.expected_output}}</p>\r\n                </div>\r\n\r\n                <div class=\"output-card-content\" *ngIf=\"output.summary\">\r\n                  <h4>Summary</h4>\r\n                  <p>{{output.summary}}</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Configuration Section -->\r\n        <div *ngIf=\"activeTabId === 'nav-services'\" class=\"section-content configuration-section\">\r\n          <div class=\"configuration-content\">\r\n            <p>Configuration settings will be displayed here.</p>\r\n            <!-- Add configuration content as needed -->\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,OAAO,EAAEC,SAAS,QAAQ,MAAM;AACzC,SAAiCC,WAAW,EAAEC,UAAU,QAAQ,gBAAgB;AAEhF;AACA,SAASC,sBAAsB,QAAQ,4DAA4D;AAKnG;AACA,SACEC,eAAe,EACfC,aAAa,EAEbC,aAAa,QACR,wBAAwB;AAG/B,SAASC,WAAW,QAAQ,kCAAkC;AAC9D,OAAOC,iBAAiB,MAAM,+BAA+B;AAG7D,SAASC,eAAe,QAAqB,gCAAgC;AAE7E,SAASC,2BAA2B,QAAmB,gEAAgE;;;;;;;;;;;;;;;;;;;;ICE/GC,EADF,CAAAC,cAAA,cAAqE,qBAQlE;IADCD,EAAA,CAAAE,UAAA,mBAAAC,sEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,eAAA,EAAiB;IAAA,EAAC;IAG/BT,EADE,CAAAU,YAAA,EAAa,EACT;;;IALFV,EAAA,CAAAW,SAAA,EACkB;IADlBX,EAAA,CAAAY,UAAA,iBAAAZ,EAAA,CAAAa,eAAA,IAAAC,GAAA,EACkB;;;;;IAuERd,EAAA,CAAAC,cAAA,eAA2C;IAAAD,EAAA,CAAAe,MAAA,mCAA4B;IAAAf,EAAA,CAAAU,YAAA,EAAO;;;;;IALhFV,EALF,CAAAC,cAAA,cAI4B,cACF;IACtBD,EAAA,CAAAgB,SAAA,mBAAqE;IACvEhB,EAAA,CAAAU,YAAA,EAAM;IAEJV,EADF,CAAAC,cAAA,cAAwB,eACG;IAAAD,EAAA,CAAAe,MAAA,GAAgB;IAAAf,EAAA,CAAAU,YAAA,EAAO;IAChDV,EAAA,CAAAiB,UAAA,IAAAC,wDAAA,mBAA2C;IAE/ClB,EADE,CAAAU,YAAA,EAAM,EACF;;;;;IARJV,EADA,CAAAmB,WAAA,WAAAC,IAAA,OAAwB,oBACC;IAEEpB,EAAA,CAAAW,SAAA,GAAe;IAAfX,EAAA,CAAAY,UAAA,gBAAe;IAGfZ,EAAA,CAAAW,SAAA,GAAgB;IAAhBX,EAAA,CAAAqB,iBAAA,CAAAC,QAAA,CAAAC,IAAA,CAAgB;IACbvB,EAAA,CAAAW,SAAA,EAAa;IAAbX,EAAA,CAAAY,UAAA,SAAAQ,IAAA,OAAa;;;;;IAc3CpB,EADF,CAAAC,cAAA,cAAwE,eAC7C;IAAAD,EAAA,CAAAe,MAAA,GAAuC;IAAAf,EAAA,CAAAU,YAAA,EAAO;IACvEV,EAAA,CAAAC,cAAA,eAAoD;IAAAD,EAAA,CAAAe,MAAA,GAAiB;IACvEf,EADuE,CAAAU,YAAA,EAAO,EACxE;;;;;IAFqBV,EAAA,CAAAW,SAAA,GAAuC;IAAvCX,EAAA,CAAAqB,iBAAA,EAAAG,IAAA,yBAAAA,IAAA,MAAuC;IACtCxB,EAAA,CAAAW,SAAA,EAAyB;IAAzBX,EAAA,CAAAyB,WAAA,UAAAC,MAAA,CAAAC,KAAA,CAAyB;IAAC3B,EAAA,CAAAW,SAAA,EAAiB;IAAjBX,EAAA,CAAAqB,iBAAA,CAAAK,MAAA,CAAAE,OAAA,CAAiB;;;;;;IAGrE5B,EADF,CAAAC,cAAA,cAA2C,iBACmB;IAA9BD,EAAA,CAAAE,UAAA,mBAAA2B,0EAAA;MAAA7B,EAAA,CAAAI,aAAA,CAAA0B,GAAA;MAAA,MAAAxB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAyB,iBAAA,EAAmB;IAAA,EAAC;IACzD/B,EAAA,CAAAe,MAAA,GACF;IACFf,EADE,CAAAU,YAAA,EAAS,EACL;;;;IAFFV,EAAA,CAAAW,SAAA,GACF;IADEX,EAAA,CAAAgC,kBAAA,MAAA1B,MAAA,CAAA2B,WAAA,kCAAA3B,MAAA,CAAA4B,YAAA,CAAAC,MAAA,GAAA7B,MAAA,CAAA8B,kBAAA,kBACF;;;;;;IAGApC,EADF,CAAAC,cAAA,cAAuF,iBACzB;IAA9BD,EAAA,CAAAE,UAAA,mBAAAmC,0EAAA;MAAArC,EAAA,CAAAI,aAAA,CAAAkC,GAAA;MAAA,MAAAhC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAyB,iBAAA,EAAmB;IAAA,EAAC;IAAC/B,EAAA,CAAAe,MAAA,gBAAS;IACvEf,EADuE,CAAAU,YAAA,EAAS,EAC1E;;;;;IApDVV,EAHJ,CAAAC,cAAA,cAAkF,cAE1C,aACV;IAAAD,EAAA,CAAAe,MAAA,wBAAiB;IAAAf,EAAA,CAAAU,YAAA,EAAK;IAE9CV,EADF,CAAAC,cAAA,cAA8B,eACC;IAAAD,EAAA,CAAAe,MAAA,uBAAgB;IAAAf,EAAA,CAAAU,YAAA,EAAO;IAElDV,EADF,CAAAC,cAAA,cAAgC,cACJ;IACxBD,EAAA,CAAAgB,SAAA,cAA4D;IAC9DhB,EAAA,CAAAU,YAAA,EAAM;IACNV,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAe,MAAA,IAAe;IAGjDf,EAHiD,CAAAU,YAAA,EAAO,EAC9C,EACF,EACF;IAMFV,EAHJ,CAAAC,cAAA,eAAoC,eAEE,UAC9B;IAAAD,EAAA,CAAAe,MAAA,sBAAc;IAAAf,EAAA,CAAAU,YAAA,EAAK;IACvBV,EAAA,CAAAC,cAAA,eAAkC;IAChCD,EAAA,CAAAiB,UAAA,KAAAsB,iDAAA,kBAI4B;IAUhCvC,EADE,CAAAU,YAAA,EAAM,EACF;IAKFV,EAFJ,CAAAC,cAAA,eAAoC,eACT,UACnB;IAAAD,EAAA,CAAAe,MAAA,sBAAc;IAAAf,EAAA,CAAAU,YAAA,EAAK;IACvBV,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAe,MAAA,wBAAgB;IAC9Cf,EAD8C,CAAAU,YAAA,EAAO,EAC/C;IACNV,EAAA,CAAAC,cAAA,eAA0B;IAUxBD,EATA,CAAAiB,UAAA,KAAAuB,iDAAA,kBAAwE,KAAAC,iDAAA,kBAI7B,KAAAC,iDAAA,kBAK4C;IAM/F1C,EAHM,CAAAU,YAAA,EAAM,EACF,EACF,EACF;;;;IAnD+BV,EAAA,CAAAW,SAAA,GAA0B;IAA1BX,EAAA,CAAAyB,WAAA,UAAAnB,MAAA,CAAAqC,QAAA,MAA0B;IAE3B3C,EAAA,CAAAW,SAAA,GAAe;IAAfX,EAAA,CAAAgC,kBAAA,KAAA1B,MAAA,CAAAqC,QAAA,MAAe;IAYvB3C,EAAA,CAAAW,SAAA,GAAW;IAAXX,EAAA,CAAAY,UAAA,YAAAN,MAAA,CAAAsC,MAAA,CAAW;IAsBQ5C,EAAA,CAAAW,SAAA,GAAkB;IAAlBX,EAAA,CAAAY,UAAA,YAAAN,MAAA,CAAAuC,aAAA,CAAkB;IAIjC7C,EAAA,CAAAW,SAAA,EAAiB;IAAjBX,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAAwC,WAAA,CAAiB;IAKjB9C,EAAA,CAAAW,SAAA,EAA6D;IAA7DX,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAA2B,WAAA,IAAA3B,MAAA,CAAA4B,YAAA,CAAAC,MAAA,GAAA7B,MAAA,CAAA8B,kBAAA,CAA6D;;;;;IA4BvFpC,EAAA,CAAAC,cAAA,cAA0F;IACxFD,EAAA,CAAAe,MAAA,wCACF;IAAAf,EAAA,CAAAU,YAAA,EAAM;;;;;IAKFV,EAFJ,CAAAC,cAAA,cAA4C,cACT,SAC3B;IAAAD,EAAA,CAAAe,MAAA,sBAAe;IAAAf,EAAA,CAAAU,YAAA,EAAK;IACxBV,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAe,MAAA,GAAc;IAE3Cf,EAF2C,CAAAU,YAAA,EAAM,EACzC,EACF;;;;IAFuBV,EAAA,CAAAW,SAAA,GAAc;IAAdX,EAAA,CAAAqB,iBAAA,CAAAf,MAAA,CAAAyC,UAAA,CAAc;;;;;IAWvC/C,EADF,CAAAC,cAAA,cAAoD,SAC9C;IAAAD,EAAA,CAAAe,MAAA,uBAAgB;IAAAf,EAAA,CAAAU,YAAA,EAAK;IACzBV,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAe,MAAA,GAAc;IACzCf,EADyC,CAAAU,YAAA,EAAM,EACzC;;;;IADqBV,EAAA,CAAAW,SAAA,GAAc;IAAdX,EAAA,CAAAqB,iBAAA,CAAA2B,UAAA,CAAAC,GAAA,CAAc;;;;;IAIvCjD,EADF,CAAAC,cAAA,cAA4D,SACtD;IAAAD,EAAA,CAAAe,MAAA,uBAAgB;IAAAf,EAAA,CAAAU,YAAA,EAAK;IACzBV,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAe,MAAA,GAAsB;IAC3Bf,EAD2B,CAAAU,YAAA,EAAI,EACzB;;;;IADDV,EAAA,CAAAW,SAAA,GAAsB;IAAtBX,EAAA,CAAAqB,iBAAA,CAAA2B,UAAA,CAAAE,WAAA,CAAsB;;;;;IAIzBlD,EADF,CAAAC,cAAA,cAAgE,SAC1D;IAAAD,EAAA,CAAAe,MAAA,sBAAe;IAAAf,EAAA,CAAAU,YAAA,EAAK;IACxBV,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAe,MAAA,GAA0B;IAC/Bf,EAD+B,CAAAU,YAAA,EAAI,EAC7B;;;;IADDV,EAAA,CAAAW,SAAA,GAA0B;IAA1BX,EAAA,CAAAqB,iBAAA,CAAA2B,UAAA,CAAAG,eAAA,CAA0B;;;;;IAI7BnD,EADF,CAAAC,cAAA,cAAwD,SAClD;IAAAD,EAAA,CAAAe,MAAA,cAAO;IAAAf,EAAA,CAAAU,YAAA,EAAK;IAChBV,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAe,MAAA,GAAkB;IACvBf,EADuB,CAAAU,YAAA,EAAI,EACrB;;;;IADDV,EAAA,CAAAW,SAAA,GAAkB;IAAlBX,EAAA,CAAAqB,iBAAA,CAAA2B,UAAA,CAAAI,OAAA,CAAkB;;;;;IApBrBpD,EAFJ,CAAAC,cAAA,cAA2E,cACzC,SAC1B;IAAAD,EAAA,CAAAe,MAAA,GAAsB;IAC5Bf,EAD4B,CAAAU,YAAA,EAAK,EAC3B;IAiBNV,EAfA,CAAAiB,UAAA,IAAAoC,sDAAA,kBAAoD,IAAAC,sDAAA,kBAKQ,IAAAC,sDAAA,kBAKI,IAAAC,sDAAA,kBAKR;IAI1DxD,EAAA,CAAAU,YAAA,EAAM;;;;;IAtBEV,EAAA,CAAAW,SAAA,GAAsB;IAAtBX,EAAA,CAAAgC,kBAAA,kBAAAyB,KAAA,SAAsB;IAGMzD,EAAA,CAAAW,SAAA,EAAgB;IAAhBX,EAAA,CAAAY,UAAA,SAAAoC,UAAA,CAAAC,GAAA,CAAgB;IAKhBjD,EAAA,CAAAW,SAAA,EAAwB;IAAxBX,EAAA,CAAAY,UAAA,SAAAoC,UAAA,CAAAE,WAAA,CAAwB;IAKxBlD,EAAA,CAAAW,SAAA,EAA4B;IAA5BX,EAAA,CAAAY,UAAA,SAAAoC,UAAA,CAAAG,eAAA,CAA4B;IAK5BnD,EAAA,CAAAW,SAAA,EAAoB;IAApBX,EAAA,CAAAY,UAAA,SAAAoC,UAAA,CAAAI,OAAA,CAAoB;;;;;;IAjDxDpD,EAHN,CAAAC,cAAA,cAAmF,cACvD,cACG,SACrB;IAAAD,EAAA,CAAAe,MAAA,mBAAY;IAAAf,EAAA,CAAAU,YAAA,EAAK;IACrBV,EAAA,CAAAC,cAAA,qBASwB;IARtBD,EAAA,CAAAE,UAAA,uBAAAwD,2EAAA;MAAA1D,EAAA,CAAAI,aAAA,CAAAuD,GAAA;MAAA,MAAArD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAaF,MAAA,CAAAsD,aAAA,CAAc,QAAQ,CAAC;IAAA,EAAC;IAUzC5D,EADE,CAAAU,YAAA,EAAa,EACT;IAENV,EAAA,CAAAC,cAAA,cAA4B;IAe1BD,EAbA,CAAAiB,UAAA,IAAA4C,gDAAA,kBAA0F,IAAAC,gDAAA,kBAK9C,IAAAC,gDAAA,kBAQ+B;IA2BjF/D,EAFI,CAAAU,YAAA,EAAM,EACF,EACF;;;;IAhDEV,EAAA,CAAAW,SAAA,GAAuB;IAEvBX,EAFA,CAAAY,UAAA,wBAAuB,yBACC,sBACH;IAMjBZ,EAAA,CAAAW,SAAA,GAA+D;IAA/DX,EAAA,CAAAY,UAAA,UAAAN,MAAA,CAAAyC,UAAA,MAAAzC,MAAA,CAAA0D,WAAA,IAAA1D,MAAA,CAAA0D,WAAA,CAAA7B,MAAA,QAA+D;IAK/DnC,EAAA,CAAAW,SAAA,EAAgB;IAAhBX,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAAyC,UAAA,CAAgB;IAQE/C,EAAA,CAAAW,SAAA,EAAgB;IAAhBX,EAAA,CAAAY,UAAA,YAAAN,MAAA,CAAA0D,WAAA,CAAgB;;;;;IAgC1ChE,EAFJ,CAAAC,cAAA,cAA0F,cACrD,QAC9B;IAAAD,EAAA,CAAAe,MAAA,qDAA8C;IAGrDf,EAHqD,CAAAU,YAAA,EAAI,EAEjD,EACF;;;ADhJd,WAAauD,0BAA0B;EAAjC,MAAOA,0BAA0B;IAiF3BC,KAAA;IACAC,MAAA;IACAC,eAAA;IACAC,qBAAA;IACAC,YAAA;IACAC,aAAA;IACAC,WAAA;IAtFVC,cAAc,GAAc,CAC1B;MAAEC,EAAE,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAW,CAAE,EACtC;MAAED,EAAE,EAAE,cAAc;MAAEC,KAAK,EAAE;IAAQ,CAAE,EACvC;MAAED,EAAE,EAAE,cAAc;MAAEC,KAAK,EAAE;IAAe,CAAE,CAC/C;IACD;IACAC,UAAU,GAAkB,IAAI;IAChCC,YAAY,GAAW,UAAU;IAEjCC,SAAS,GAAGjF,iBAAwC;IAGpDkF,iBAAiB;IAEjB;IACAC,YAAY,GAAkB,EAAE;IAChCC,gBAAgB,GAAW,CAAC;IAC5BC,gBAAgB;IAChBC,SAAS,GAAY,KAAK;IAC1BC,MAAM,GAAoBtF,eAAe,CAACuF,UAAU;IAEpD;IACAC,YAAY,GAAkB,EAAE;IAChCC,gBAAgB,GAAY,KAAK;IACjCC,SAAS,GAAG,EAAE;IAEd;IACAC,YAAY,GAAyB,EAAE;IAChCC,YAAY;IACZC,QAAQ,GAAY,MAAM;IAEjC;IACAC,kBAAkB,GAAgB,IAAI;IACtCC,kBAAkB,GAAY,KAAK;IACnCC,WAAW;IAEJ5D,YAAY,GAAU,EAAE;IACxBE,kBAAkB,GAAW,EAAE;IAC/BH,WAAW,GAAY,KAAK;IACnC8D,kBAAkB,GAAGnG,WAAW,CAACoG,kBAAkB,IAAI,KAAK;IAErDC,mBAAmB,GAAY,KAAK;IAC3CC,gBAAgB;IAEhB;IACQC,QAAQ,GAAG,IAAI/G,OAAO,EAAQ;IACtCgH,WAAW,GAAW,gBAAgB;IACtCC,QAAQ,GAAa,CACnB;MAAE3B,EAAE,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAgB,CAAE,EAC3C;MAAED,EAAE,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAc,CAAE,EACvC;MAAED,EAAE,EAAE,SAAS;MAAEC,KAAK,EAAE,SAAS;MAAE2B,QAAQ,EAAE;IAAI,CAAE,CACpD;IACDC,QAAQ,GAAG,KAAK;IAChBxD,UAAU;IACViB,WAAW,GAKL,EAAE;IACRwC,WAAW,GAAG,KAAK;IACnBC,WAAW,GAAa,KAAK;IAC7BC,aAAa,GAAW,EAAE;IAC1BC,cAAc,GAAU,EAAE;IAC1BC,aAAa,GAAU,EAAE;IACzBjE,QAAQ,GAAG,CAAC;IACZkE,SAAS,GAAG,KAAK;IACjBC,WAAW,GAAW,EAAE;IAExBC,eAAe,GAAa,EAAE;IAC9BC,iBAAiB,GAAW,CAAC;IAC7BC,WAAW,GAAW,UAAU;IAGhC;IACArE,MAAM,GAAgB,EAAE;IACxBsE,qBAAqB,GAAY,KAAK;IACtCC,cAAc,GAAU,EAAE;IAE1BC,YACUlD,KAAqB,EACrBC,MAAc,EACdC,eAAgC,EAChCC,qBAAoD,EACpDC,YAAiC,EACjCC,aAA4B,EAC5BC,WAAwB;MANxB,KAAAN,KAAK,GAALA,KAAK;MACL,KAAAC,MAAM,GAANA,MAAM;MACN,KAAAC,eAAe,GAAfA,eAAe;MACf,KAAAC,qBAAqB,GAArBA,qBAAqB;MACrB,KAAAC,YAAY,GAAZA,YAAY;MACZ,KAAAC,aAAa,GAAbA,aAAa;MACb,KAAAC,WAAW,GAAXA,WAAW;IAClB;IAEH6C,QAAQA,CAAA;MACN,IAAI,CAAC9C,aAAa,CAAC+C,aAAa,EAAE;MAClC,IAAI,CAAClB,WAAW,GAAG,gBAAgB;MACnC,IAAI,CAACN,WAAW,GAAGyB,MAAM,CAACC,UAAU,EAAE;MACtC;MACA,IAAI,CAACtD,KAAK,CAACuD,QAAQ,CAACC,IAAI,CAACrI,SAAS,CAAC,IAAI,CAAC8G,QAAQ,CAAC,CAAC,CAACwB,SAAS,CAAEC,MAAM,IAAI;QACtE,IAAI,CAAChD,UAAU,GAAGgD,MAAM,CAACC,GAAG,CAAC,IAAI,CAAC;QAClC,IAAI,IAAI,CAACjD,UAAU,EAAE;UACnB,IAAI,CAACkD,YAAY,CAAC,IAAI,CAAClD,UAAU,CAAC;QACpC,CAAC,MAAM;UACL;UACA,IAAI,CAACT,MAAM,CAAC4D,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;QAC5C;MACF,CAAC,CAAC;MACF;IACF;IAEAC,WAAWA,CAAA;MACT,IAAI,CAAC7B,QAAQ,CAAC8B,IAAI,EAAE;MACpB,IAAI,CAAC9B,QAAQ,CAAC+B,QAAQ,EAAE;MACxB,IAAI,CAAC3D,aAAa,CAAC4D,YAAY,EAAE;IACnC;IACAC,WAAWA,CAACC,KAAoC;MAC9C,IAAI,CAACjC,WAAW,GAAGiC,KAAK,CAAC1D,KAAK;MAC9B,IAAI,CAACsC,WAAW,GAAGoB,KAAK,CAAC3D,EAAE;MAC3B4D,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEF,KAAK,CAAC;IACpC;IAEA;IACAP,YAAYA,CAACpD,EAAU;MACrB;MACA4D,OAAO,CAACC,GAAG,CAAC,6BAA6B7D,EAAE,EAAE,CAAC;MAC9C,IAAI,CAACY,YAAY,GAAG,CAClB;QACEkD,IAAI,EAAE,IAAI;QACVC,IAAI,EAAE;OACQ,CACjB;MACD,IAAI,CAAC/C,YAAY,GAAG,IAAI,CAAClB,WAAW,CAACkE,KAAK,CAAC,EAAE,CAAC;MAE9C;MACA,IAAI,CAACtE,eAAe,CAACuE,eAAe,CAACjE,EAAE,CAAC,CAACiD,SAAS,CAAC;QAC/CM,IAAI,EAAGW,GAAG,IAAI;UACd,IAAI,CAACjC,cAAc,GAAGiC,GAAG,CAACjC,cAAc;UACxC,IAAI,CAACC,aAAa,GAAG,IAAI,CAACiC,iBAAiB,CAAC,IAAI,CAAClC,cAAc,CAAC;UAChE,IAAG,IAAI,CAACC,aAAa,CAACzE,MAAM,KAAK,CAAC,EAAC;YACjC,IAAI,CAACsE,WAAW,GAAG,IAAI;UACzB;UACA,IAAI,CAAC5B,YAAY,GAAG+D,GAAG,CAACrH,IAAI;UAC5B,IAAI,CAACuH,cAAc,EAAE;UACrB,IAAI,CAACC,oBAAoB,EAAE;UAC3B,IAAI,CAACC,kBAAkB,EAAE,CAAC,CAAC;QAC7B,CAAC;QACCC,KAAK,EAAGC,GAAG,IAAI;UACb,IAAI,CAACzC,WAAW,GAAG,IAAI;UACvB,IAAI,CAAC1B,iBAAiB,CAACoE,aAAa,CAAC,uFAAuF,CAAC;UAC7Hb,OAAO,CAACC,GAAG,CAACW,GAAG,CAAC;QAClB;OACH,CAAC;MAEF;MACA,IAAI,CAACE,kBAAkB,CAAC1E,EAAE,CAAC;IAE7B;IAEA;IACO0E,kBAAkBA,CAAC1E,EAAU;MAClC,IAAI,CAACN,eAAe,CAACiF,cAAc,CAAC3E,EAAE,CAAC,CAACiD,SAAS,CAAC;QAChDM,IAAI,EAAGW,GAAQ,IAAI;UACjB,IAAIA,GAAG,EAAEU,cAAc,EAAEnH,MAAM,EAAE;YAC/B,IAAI,CAACgF,cAAc,GAAGyB,GAAG,CAACU,cAAc;YACxC,IAAI,CAACzE,YAAY,GAAG+D,GAAG,CAACrH,IAAI;YAE5B;YACA,IAAI;cACF,IAAI,CAACqB,MAAM,GAAG,IAAI,CAACyB,qBAAqB,CAACkF,kBAAkB,CAAC,IAAI,CAACpC,cAAc,CAAC;cAChFmB,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAAC3F,MAAM,CAAC;YAC/C,CAAC,CAAC,OAAOqG,KAAK,EAAE;cACdX,OAAO,CAACW,KAAK,CAAC,gDAAgD,EAAEA,KAAK,CAAC;cACtE,IAAI,CAACrG,MAAM,GAAG,EAAE;YAClB;YAEA;YACA,IAAI,CAACgE,aAAa,GAAG,IAAI,CAACiC,iBAAiB,CAACD,GAAG,CAACU,cAAc,CAAC;YAC/D,IAAI,CAACR,cAAc,EAAE;UACvB,CAAC,MAAM;YACLR,OAAO,CAACkB,IAAI,CAAC,+CAA+C,CAAC;YAC7D,IAAI,CAAC5G,MAAM,GAAG,EAAE;UAClB;QACF,CAAC;QACDqG,KAAK,EAAGQ,CAAM,IAAKnB,OAAO,CAACW,KAAK,CAACQ,CAAC;OACnC,CAAC;IACJ;IAEOZ,iBAAiBA,CAACS,cAAmB;MAC1C,MAAMI,oBAAoB,GAAI,qCAAqC;MACnE,MAAMC,cAAc,GAAoE,EAAE;MAE1FL,cAAc,CAACM,OAAO,CAAEC,aAAkB,IAAI;QAC5C,MAAMC,SAAS,GAAGD,aAAa,EAAEE,KAAK,EAAExI,IAAI;QAC5C,MAAMyI,gBAAgB,GAAGH,aAAa,EAAEE,KAAK,EAAEE,IAAI,EAAE/G,WAAW;QAChE,MAAMgH,OAAO,GAAGF,gBAAgB,EAAEG,QAAQ,CAACT,oBAAoB,CAAC,IAAI,EAAE;QAEtE,KAAK,MAAMU,KAAK,IAAIF,OAAO,EAAE;UAC3B,MAAMG,WAAW,GAAGD,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC;UACxC,MAAME,gBAAgB,GAAGF,KAAK,CAAC,CAAC,CAAC;UACjC,IAAI,CAACT,cAAc,CAACU,WAAW,CAAC,EAAE;YAChCV,cAAc,CAACU,WAAW,CAAC,GAAG;cAAEzH,MAAM,EAAE,IAAI2H,GAAG,EAAE;cAAEC,MAAM,EAAE,IAAID,GAAG;YAAE,CAAE;YAAC;UACzE;UACA,IAAIT,SAAS,EAAE;YACbH,cAAc,CAACU,WAAW,CAAC,CAACzH,MAAM,CAAC6H,GAAG,CAACX,SAAS,CAAC;UACnD;UACAH,cAAc,CAACU,WAAW,CAAC,CAACG,MAAM,CAACC,GAAG,CAACH,gBAAgB,CAAC;QAC1D;MACF,CAAC,CAAC;MAEF,OAAOI,MAAM,CAACC,OAAO,CAAChB,cAAc,CAAC,CAACiB,GAAG,CAAC,CAAC,CAACP,WAAW,EAAE;QAAEzH,MAAM;QAAE4H;MAAM,CAAE,CAAC,MAAM;QAChFjJ,IAAI,EAAE,CAAC,GAAGqB,MAAM,CAAC,CAACT,MAAM,GAAG,CAAC,GACxB,GAAG,CAAC,GAAGS,MAAM,CAAC,CAACiI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAGlI,MAAM,CAAC,CAACmI,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,GAClE,CAAC,GAAGnI,MAAM,CAAC,CAACkI,IAAI,CAAC,OAAO,CAAC;QAC7BT,WAAW;QACXW,KAAK,EAAE,CAAC,GAAGR,MAAM,CAAC,CAAC,CAAC;OACrB,CAAC,CAAC;IACL;IAEOS,YAAYA,CAACD,KAAa;MAC/B,MAAMZ,KAAK,GAAGY,KAAK,CAACZ,KAAK,CAAC,WAAW,CAAC;MACtC,IAAIA,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC,EAAE;QACrB,MAAMc,YAAY,GAAGd,KAAK,CAAC,CAAC,CAAC,CAACe,IAAI,EAAE;QACpC,OAAOD,YAAY,CAACE,UAAU,CAAC,OAAO,CAAC,IAAIF,YAAY,CAACE,UAAU,CAAC,OAAO,CAAC;MAC7E;MACA,OAAO,KAAK;IACd;IAEOtC,cAAcA,CAAA;MACnBR,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE,IAAI,CAAC3B,aAAa,CAAC;MACxE,IAAI,CAACA,aAAa,CAACgD,OAAO,CAAEjF,KAAU,IAAI;QACxC2D,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE5D,KAAK,CAACqG,KAAK,CAAC;QACpD,IAAI,CAACtF,YAAY,CAAC2F,UAAU,CAAC1G,KAAK,CAACqG,KAAK,EAAE,IAAI,CAACxG,WAAW,CAAC8G,OAAO,CAAC,EAAE,EAAE/L,UAAU,CAACgM,QAAQ,CAAC,CAAC;MAC9F,CAAC,CAAC;MAEF;MACA,IAAI,IAAI,CAAC3I,MAAM,EAAE;QACf,IAAI,CAACA,MAAM,CAACgH,OAAO,CAACG,KAAK,IAAG;UAC1B,IAAIA,KAAK,CAACS,MAAM,EAAE;YAChBT,KAAK,CAACS,MAAM,CAACZ,OAAO,CAACoB,KAAK,IAAG;cAC3B,MAAMQ,WAAW,GAAGR,KAAK,CAACX,WAAW;cACrC,IAAI,CAAC,IAAI,CAAC3E,YAAY,CAACmC,GAAG,CAAC2D,WAAW,CAAC,EAAE;gBACvClD,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAEiD,WAAW,CAAC;gBAC3E,IAAI,CAAC9F,YAAY,CAAC2F,UAAU,CAACG,WAAW,EAAE,IAAI,CAAChH,WAAW,CAAC8G,OAAO,CAAC,EAAE,EAAE/L,UAAU,CAACgM,QAAQ,CAAC,CAAC;cAC9F;YACF,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;MACJ;MAEAjD,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEmC,MAAM,CAACe,IAAI,CAAC,IAAI,CAAC/F,YAAY,CAACgG,QAAQ,CAAC,CAAC;IAC9E;IAEOC,YAAYA,CAAA;MACjB,OAAO,IAAI,CAACjG,YAAY,CAACkG,KAAK,IAAI,IAAI,CAAChH,UAAU;IACnD;IAEAiH,iBAAiBA,CAAA;MACf,IAAI,CAAClJ,QAAQ,GAAG,CAAC;MACjB,IAAI,CAACuD,gBAAgB,GAAG4F,WAAW,CAAC,MAAK;QACvC,IAAI,IAAI,CAACnJ,QAAQ,GAAG,EAAE,EAAE;UACtB,IAAI,CAACA,QAAQ,IAAI,CAAC,CAAC,CAAC;QACtB;MACF,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IACX;IAEAoJ,gBAAgBA,CAAA;MACdC,aAAa,CAAC,IAAI,CAAC9F,gBAAgB,CAAC;MACpC,IAAI,CAACvD,QAAQ,GAAG,GAAG;MAEnBsJ,UAAU,CAAC,MAAK;QACd,IAAI,CAACpF,SAAS,GAAG,KAAK;MACxB,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IACX;IAEA;IACAqF,iBAAiBA,CAACC,OAAe;MAC/B;MACA,IAAI,CAAC5G,gBAAgB,GAAG,IAAI;MAC5B,IAAG4G,OAAO,CAAChB,IAAI,EAAE,KAAK,EAAE,EAAC;QACvB,IAAG,IAAI,CAACpE,eAAe,CAAC5E,MAAM,KAAK,CAAC,EAAC;UACnC,IAAI,CAAC4C,iBAAiB,CAACoE,aAAa,CAAC,2BAA2B,CAAC;UACjE,IAAI,CAAC1I,eAAe,EAAE;QACxB;QACA;MACF;MAEA,IAAG,IAAI,CAACwF,mBAAmB,IAAI,IAAI,CAACe,iBAAiB,KAAG,IAAI,CAACD,eAAe,CAAC5E,MAAM,EAAC;QAChF,IAAI,CAAC4C,iBAAiB,CAACoE,aAAa,CAAC,2BAA2B,CAAC;QACjE,IAAI,CAAC1I,eAAe,EAAE;QACxB;MACF;MAEA,MAAM2L,KAAK,GAAG,IAAI,CAACrF,eAAe,CAAC,IAAI,CAACC,iBAAiB,CAAC;MAC1D,IAAI,IAAI,CAACiE,YAAY,CAACmB,KAAK,CAAC,EAAE;QAC5B;QACA,IAAI,CAACrH,iBAAiB,CAACoE,aAAa,CAAC,mCAAmCiD,KAAK,EAAE,CAAC;QAChF;MACF;MAEA,IAAI,CAAC1G,YAAY,CAACmC,GAAG,CAACuE,KAAK,CAAC,EAAEC,QAAQ,CAACF,OAAO,CAAC;MAC/C,IAAI,CAACnF,iBAAiB,EAAE;MAExB,IAAI,IAAI,CAACA,iBAAiB,GAAG,IAAI,CAACD,eAAe,CAAC5E,MAAM,EAAE;QACxD,IAAI,CAACmK,qBAAqB,EAAE;MAC9B,CAAC,MAAM;QACL,IAAI,CAACvH,iBAAiB,CAACoE,aAAa,CAAC,oDAAoD,CAAC;QAC1F,IAAI,CAAC1I,eAAe,EAAE;MACxB;IACF;IAEA;IACA8L,QAAQA,CAAA;MACNjE,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;MACvC;IACF;IAEA;IACA3E,aAAaA,CAAC4I,OAA8B;MAC1ClE,OAAO,CAACC,GAAG,CAAC,aAAaiE,OAAO,UAAU,CAAC;MAE3C,IAAIA,OAAO,KAAK,UAAU,EAAE;QAC1B,MAAMC,IAAI,GAAG,IAAI,CAACzH,YAAY,CAC3B4F,GAAG,CAAErC,GAAG,IAAK,IAAIA,GAAG,CAACmE,SAAS,KAAKnE,GAAG,CAAC4D,OAAO,EAAE,CAAC,CACjDrB,IAAI,CAAC,IAAI,CAAC;QACb,IAAI,CAAC6B,cAAc,CAACF,IAAI,EAAE,4BAA4B,EAAE,YAAY,CAAC;MACvE,CAAC,MAAM;QACL,MAAMA,IAAI,GAAGG,IAAI,CAACC,SAAS,CAAC,IAAI,CAACpH,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC;QACvD,IAAI,CAACkH,cAAc,CAACF,IAAI,EAAE,uBAAuB,EAAE,kBAAkB,CAAC;MACxE;IACF;IAEA;IACQE,cAAcA,CAACF,IAAY,EAAEK,QAAgB,EAAEC,IAAY;MACjE,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACR,IAAI,CAAC,EAAE;QAAEM;MAAI,CAAE,CAAC;MACvC,MAAMG,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;MACrC,MAAMK,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGN,GAAG;MACfG,IAAI,CAACI,QAAQ,GAAGX,QAAQ;MACxBO,IAAI,CAACK,KAAK,EAAE;MACZP,GAAG,CAACQ,eAAe,CAACT,GAAG,CAAC;IAC1B;IAEA;IACAU,mBAAmBA,CAACC,MAAiC;MACnDvF,OAAO,CAACC,GAAG,CAAC,mBAAmBsF,MAAM,EAAE,CAAC;MACxC;MAEA,IAAIA,MAAM,KAAK,MAAM,EAAE;QACrB,IAAI,CAAC1I,SAAS,GAAG,IAAI;MACvB,CAAC,MAAM,IAAI0I,MAAM,KAAK,OAAO,IAAIA,MAAM,KAAK,MAAM,EAAE;QAClD,IAAI,CAAC1I,SAAS,GAAG,KAAK;MACxB;IACF;IAEA;IACA2I,YAAYA,CAAA;MACV,IAAI,CAAC3J,MAAM,CAAC4D,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;IAC5C;IAEA;IACAgG,YAAYA,CAAA;MACV,IAAI,IAAI,CAACnJ,UAAU,EAAE;QACnB,IAAI,CAACT,MAAM,CAAC4D,QAAQ,CAAC,CAAC,uBAAuB,EAAE,IAAI,CAACnD,UAAU,CAAC,CAAC;MAClE;IACF;IAEOoJ,kBAAkBA,CAACC,KAAA,GAAgB,IAAI;MAC5ChC,UAAU,CAAC,MAAK;QACd,IAAI,CAAC,IAAI,CAAChG,mBAAmB,EAAE;UAC7BqC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACzD,SAAS,CAAC;UAC3BwD,OAAO,CAACC,GAAG,CAAC,IAAI,CAACzD,SAAS,CAAC,QAAQ,CAAC,CAACoJ,sBAAsB,CAAC;UAC5D,IAAI,CAAChM,YAAY,CAACiM,IAAI,CAAC;YACrBvM,OAAO,EAAE,IAAI,CAACkD,SAAS,CAAC,QAAQ,CAAC,CAACoJ,sBAAsB;YACxDvM,KAAK,EAAE;WACR,CAAC;QACJ;MACF,CAAC,EAAEsM,KAAK,CAAC;IACX;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEOG,eAAeA,CAACtI,WAAmB;MACxCwC,OAAO,CAACC,GAAG,CAAC,qDAAqD,EAAEzC,WAAW,CAAC;MAE/E,IAAI;QACF,IAAI,CAAC1B,eAAe,CACjBiK,kBAAkB,CAACvI,WAAW,CAAC,CAC/B4B,IAAI,CAACrI,SAAS,CAAC,IAAI,CAAC8G,QAAQ,CAAC,CAAC,CAC9BwB,SAAS,CAAC;UACTM,IAAI,EAAGkE,OAAO,IAAI;YAChB7D,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE4D,OAAO,CAAC;YACnD,MAAM;cAAEvK,OAAO;cAAED;YAAK,CAAE,GAAGwK,OAAO;YAClC,IAAIxK,KAAK,EAAE;cACT,IAAI,CAACO,YAAY,CAACiM,IAAI,CAAC;gBAAEvM,OAAO;gBAAED;cAAK,CAAE,CAAC;YAC5C,CAAC,MAAM,IAAI,IAAI,CAACoE,kBAAkB,KAAK,KAAK,EAAE;cAC5C;cACA,IAAI,CAAC7D,YAAY,CAACiM,IAAI,CAAC;gBAAEvM,OAAO;gBAAED,KAAK,EAAE;cAAS,CAAE,CAAC;YACvD;UACF,CAAC;UACDsH,KAAK,EAAGC,GAAG,IAAI;YACbZ,OAAO,CAACW,KAAK,CAAC,6BAA6B,EAAEC,GAAG,CAAC;YACjD,IAAI,CAAChH,YAAY,CAACiM,IAAI,CAAC;cACrBvM,OAAO,EAAE,uDAAuD;cAChED,KAAK,EAAE;aACR,CAAC;UACJ,CAAC;UACDuG,QAAQ,EAAEA,CAAA,KAAK;YACb,IAAI,CAAC8F,kBAAkB,EAAE;YACzB1F,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;UAC5C;SACD,CAAC;MACN,CAAC,CAAC,OAAOU,KAAK,EAAE;QACdX,OAAO,CAACW,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;QACjE,IAAI,CAAC/G,YAAY,CAACiM,IAAI,CAAC;UACrBvM,OAAO,EAAE,8DAA8D;UACvED,KAAK,EAAE;SACR,CAAC;MACJ;IACF;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEO2M,YAAYA,CAACC,MAAc;MAChC,IAAI,CAAC/H,WAAW,GAAG,KAAK;MACxB,IAAI;QACF,MAAMgI,YAAY,GAAG5B,IAAI,CAAC6B,KAAK,CAACF,MAAM,CAAC;QACvC,IAAI,CAAC/H,WAAW,GAAG,IAAI;QACvB,OAAOgI,YAAY;MACrB,CAAC,CAAC,OAAO/E,CAAC,EAAE;QACV,OAAO,IAAI;MACb;IACF;IAEOhJ,eAAeA,CAAA;MACpB,IAAIiO,OAAO,GAAmC,IAAIC,QAAQ,EAAE;MAC5D,IAAIC,WAAW,GAAG,EAAE;MAEpBtG,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE,IAAI,CAAC7C,YAAY,CAACmJ,KAAK,CAAC;MAC3EvG,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEmC,MAAM,CAACe,IAAI,CAAC,IAAI,CAAC/F,YAAY,CAACgG,QAAQ,CAAC,CAAC;MACtEpD,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAAC3F,MAAM,CAACgI,GAAG,CAACkE,CAAC,KAAK;QAAEvN,IAAI,EAAEuN,CAAC,CAACvN,IAAI;QAAEiJ,MAAM,EAAEsE,CAAC,CAACtE;MAAM,CAAE,CAAC,CAAC,CAAC;MAExF,IAAI,CAACpF,MAAM,GAAGtF,eAAe,CAACiP,OAAO;MACrC,IAAI,IAAI,CAACrI,aAAa,CAACvE,MAAM,EAAE;QAC7B,IAAI,CAACuE,aAAa,CAACkD,OAAO,CAAEoF,IAAI,IAAI;UAClCN,OAAO,CAACO,MAAM,CAAC,OAAO,EAAED,IAAI,CAAC;QAC/B,CAAC,CAAC;QACFN,OAAO,CAACO,MAAM,CAAC,YAAY,EAAE,IAAI,CAACrK,UAAU,CAAC;QAC7C8J,OAAO,CAACO,MAAM,CAAC,YAAY,EAAErC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACnH,YAAY,CAACmJ,KAAK,CAAC,CAAC;QACrEH,OAAO,CAACO,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC3K,YAAY,CAAC4K,aAAa,EAAE,CAAC;QACzDR,OAAO,CAACO,MAAM,CAAC,aAAa,EAAE,IAAI,CAACnJ,WAAW,CAAC;QAC/C8I,WAAW,GAAG,QAAQ;MACxB,CAAC,MAAM;QACLF,OAAO,GAAG;UACRS,UAAU,EAAE,IAAI,CAACvK,UAAU;UAC3BwK,UAAU,EAAE,IAAI,CAAC1J,YAAY,CAACmJ,KAAK;UACnC/I,WAAW,EAAE,IAAI,CAACA,WAAW;UAC7BuJ,IAAI,EAAE,IAAI,CAAC/K,YAAY,CAAC4K,aAAa;SACtC;MACH;MAEA5G,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEmG,OAAO,CAAC;MAEtC,IAAI,CAACN,eAAe,CAAC,IAAI,CAACtI,WAAW,CAAC;MACtC,IAAI,CAAC+F,iBAAiB,EAAE;MAExB,IAAI,CAACzH,eAAe,CACjB3D,eAAe,CAACiO,OAAO,EAAEE,WAAW,CAAC,CACrClH,IAAI,CAACrI,SAAS,CAAC,IAAI,CAAC8G,QAAQ,CAAC,CAAC,CAC9BwB,SAAS,CAAC;QACTM,IAAI,EAAGW,GAAG,IAAI;UACZ,IAAI,CAACrD,gBAAgB,GAAG,KAAK;UAC7B,IAAI,CAACJ,SAAS,GAAG,KAAK;UACtB,IAAI,CAACJ,iBAAiB,CAACoE,aAAa,CAACP,GAAG,EAAEuD,OAAO,IAAI,4CAA4C,CAAC;UAElG7D,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEK,GAAG,CAAC;UAEhD;UACA,IAAIA,GAAG,EAAE0G,gBAAgB,EAAEC,QAAQ,EAAE;YACnC,MAAMA,QAAQ,GAAG3G,GAAG,CAAC0G,gBAAgB,CAACC,QAAQ;YAC9CjH,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEgH,QAAQ,CAAC;YAEvC,IAAI,CAACtJ,mBAAmB,GAAG,IAAI;YAC/B,IAAI,CAAC/D,YAAY,CAACiM,IAAI,CAAC;cACrBvM,OAAO,EAAE,IAAI,CAACkD,SAAS,CAAC,QAAQ,CAAC,CAAC0K,oBAAoB;cACtD7N,KAAK,EAAE;aACR,CAAC;YACF,IAAI,CAAC4E,QAAQ,GAAG,KAAK;YAErB;YACA,IAAI,CAACxD,UAAU,GAAGwM,QAAQ,CAAChB,MAAM,IAAI,iCAAiC;YACtEjG,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE,IAAI,CAACxF,UAAU,CAAC;YAEtD;YACA,IAAIwM,QAAQ,CAACE,YAAY,IAAIC,KAAK,CAACC,OAAO,CAACJ,QAAQ,CAACE,YAAY,CAAC,EAAE;cACjEnH,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEgH,QAAQ,CAACE,YAAY,CAAC;cAC9D,IAAI,CAAChK,YAAY,GAAG8J,QAAQ,CAACE,YAAY,CAAC7E,GAAG,CAAEX,IAAS,IAAI;gBAC1D,OAAO;kBACLvF,EAAE,EAAEuF,IAAI,EAAEvF,EAAE,IAAIkL,IAAI,CAACC,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;kBAC3DC,KAAK,EAAE/F,IAAI,EAAE+F,KAAK,IAAI,aAAa;kBACnCpO,OAAO,EAAEqI,IAAI,EAAEhH,GAAG,IAAIgH,IAAI,EAAErI,OAAO,IAAI,EAAE;kBACzCkI,SAAS,EAAEG,IAAI,EAAEH,SAAS,IAAI,OAAO;kBACrC4C,SAAS,EAAEzC,IAAI,EAAEyC,SAAS,IAAI,IAAIuD,IAAI,EAAE,CAACC,WAAW,EAAE;kBACtDnD,IAAI,EAAE9C,IAAI,EAAE8C,IAAI,IAAI,MAAM;kBAC1B7J,WAAW,EAAE+G,IAAI,EAAE/G,WAAW,IAAI,EAAE;kBACpCC,eAAe,EAAE8G,IAAI,EAAE9G,eAAe,IAAI,EAAE;kBAC5CC,OAAO,EAAE6G,IAAI,EAAE7G,OAAO,IAAI,EAAE;kBAC5BH,GAAG,EAAEgH,IAAI,EAAEhH,GAAG,IAAI;iBACnB;cACH,CAAC,CAAC;cACFqF,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAAC9C,YAAY,CAAC;cAC5D6C,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAAC9C,YAAY,CAACtD,MAAM,CAAC;YAChE,CAAC,MAAM;cACLmG,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;cAC9D,IAAIgH,QAAQ,CAAChB,MAAM,EAAE;gBACnB;gBACAjG,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;gBACxD,IAAI,CAAC9C,YAAY,GAAG,CAAC;kBACnBf,EAAE,EAAE,aAAa;kBACjBsL,KAAK,EAAE,iBAAiB;kBACxBpO,OAAO,EAAE2N,QAAQ,CAAChB,MAAM;kBACxBzE,SAAS,EAAE,UAAU;kBACrB4C,SAAS,EAAE,IAAIuD,IAAI,EAAE,CAACC,WAAW,EAAE;kBACnCnD,IAAI,EAAE,MAAM;kBACZ7J,WAAW,EAAE,sBAAsB;kBACnCC,eAAe,EAAE,2BAA2B;kBAC5CC,OAAO,EAAE,iCAAiC;kBAC1CH,GAAG,EAAEsM,QAAQ,CAAChB;iBACf,CAAC;gBACFjG,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE,IAAI,CAAC9C,YAAY,CAAC;cAClE;YACF;YAEA;YACA,IAAI8J,QAAQ,CAACE,YAAY,IAAIC,KAAK,CAACC,OAAO,CAACJ,QAAQ,CAACE,YAAY,CAAC,EAAE;cACjE,IAAI,CAACzL,WAAW,GAAGuL,QAAQ,CAACE,YAAY,CAAC7E,GAAG,CACzCX,IAKA,IAAI;gBACH,OAAO;kBACL/G,WAAW,EAAE+G,IAAI,CAAC/G,WAAW;kBAC7BE,OAAO,EAAE6G,IAAI,CAAC7G,OAAO;kBACrBH,GAAG,EAAEgH,IAAI,CAAChH,GAAG;kBACbE,eAAe,EAAE8G,IAAI,CAAC9G;iBACvB;cACH,CAAC,CACF;cACDmF,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAACvE,WAAW,CAAC;cACzDsE,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAACvE,WAAW,CAAC7B,MAAM,CAAC;YAC/D,CAAC,MAAM;cACLmG,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEgH,QAAQ,CAACE,YAAY,CAAC;YAC9E;YAEA;YACA;YACA;YAEA;YACA;YACA;YACA;YACA;YACA;UACF;UACA,IAAI,CAACnB,YAAY,CAAC,IAAI,CAACvL,UAAU,CAAC;UAClC,IAAI,CAACqC,MAAM,GAAGtF,eAAe,CAACqQ,SAAS;UACvC,IAAI,CAACpE,gBAAgB,EAAE;UACvB,IAAI,CAACrF,aAAa,GAAG,EAAE;QACzB,CAAC;QACDuC,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAAChD,mBAAmB,GAAG,IAAI;UAC/B,IAAI,CAACV,gBAAgB,GAAG,KAAK;UAC7B,IAAI,CAACgB,QAAQ,GAAG,IAAI;UACpB,IAAI,CAACxD,UAAU,GAAGkG,KAAK,EAAEA,KAAK,EAAEmH,MAAM;UACtC,IAAI,CAAChM,eAAe,CAACiM,qBAAqB,EAAE;UAC5C,IAAI,CAACnO,YAAY,CAACiM,IAAI,CAAC;YACrBvM,OAAO,EAAE,IAAI,CAACkD,SAAS,CAAC,QAAQ,CAAC,CAACwL,iBAAiB;YACnD3O,KAAK,EAAE;WACR,CAAC;UACF,IAAI,CAACoD,iBAAiB,CAACoE,aAAa,CAClC,sDAAsD,CACvD;UACD,IAAI,CAACzC,aAAa,GAAG,EAAE;UACvB,IAAI,CAACqF,gBAAgB,EAAE;UACvBzD,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEU,KAAK,CAACkD,OAAO,CAAC;QACxC;OACD,CAAC;IACN;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEAoE,gBAAgBA,CAAA;MACdjI,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;IACjC;IAEAiI,qBAAqBA,CAACC,KAAa;MACjC,IAAG,IAAI,CAACzJ,iBAAiB,KAAG,IAAI,CAACD,eAAe,CAAC5E,MAAM,IAAI,IAAI,CAAC4E,eAAe,CAAC5E,MAAM,KAAG,CAAC,EAAC;QACzF,IAAI,CAACuE,aAAa,GAAG+J,KAAK;QAC1B;MACF;MAEA,MAAMrE,KAAK,GAAG,IAAI,CAACrF,eAAe,CAAC,IAAI,CAACC,iBAAiB,CAAC;MAC1D,IAAG,IAAI,CAACiE,YAAY,CAACmB,KAAK,CAAC,EAAC;QAC1B,IAAIqE,KAAK,IAAIA,KAAK,CAACtO,MAAM,GAAG,CAAC,EAAE;UAC7B,IAAI,CAACuO,eAAe,CAACD,KAAK,CAAC,CAAC,CAAC,CAAC;QAChC;MACF,CAAC,MAAM;QACL,IAAI,CAAC/J,aAAa,GAAG+J,KAAK;MAC5B;IACF;IAEA1H,oBAAoBA,CAAA;MAClB,IAAI,CAAChC,eAAe,GAAG2D,MAAM,CAACe,IAAI,CAAC,IAAI,CAAC/F,YAAY,CAACgG,QAAQ,CAAC;MAC9D,IAAI,CAAC1E,iBAAiB,GAAG,CAAC;MAC1B,IAAI,IAAI,CAACD,eAAe,CAAC5E,MAAM,GAAG,CAAC,EAAE;QACnC,IAAI,CAACmK,qBAAqB,EAAE;MAC9B,CAAC,MACG;QACF,IAAI,CAAC7F,WAAW,GAAG,IAAI;QACvB,IAAI,CAAC1B,iBAAiB,CAACoE,aAAa,CAAC,uFAAuF,CAAC;MAC/H;IACF;IAEAmD,qBAAqBA,CAAA;MACnB,MAAMF,KAAK,GAAG,IAAI,CAACrF,eAAe,CAAC,IAAI,CAACC,iBAAiB,CAAC;MAC1D,IAAI,IAAI,CAACiE,YAAY,CAACmB,KAAK,CAAC,EAAE;QAC5B,IAAI,CAACzG,QAAQ,GAAG,sBAAsB;QACtC,IAAI,CAACZ,iBAAiB,CAACoE,aAAa,CAAC,8BAA8BiD,KAAK,EAAE,CAAC;QAC3E;MACF,CAAC,MAAM;QACL,IAAI,CAACzG,QAAQ,GAAG,MAAM,CAAC,CAAC;QACxB,IAAI,CAACZ,iBAAiB,CAACoE,aAAa,CAAC,6BAA6BiD,KAAK,EAAE,CAAC;MAC5E;IACF;IAEAsE,eAAeA,CAAC1B,IAAU;MACxB,MAAM5C,KAAK,GAAG,IAAI,CAACrF,eAAe,CAAC,IAAI,CAACC,iBAAiB,CAAC;MAC1D,IAAI,CAAC,IAAI,CAACiE,YAAY,CAACmB,KAAK,CAAC,EAAE;MAE/B,MAAMuE,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC/BD,MAAM,CAACE,MAAM,GAAG,MAAK;QACnB,MAAMC,YAAY,GAAIH,MAAM,CAACI,MAAiB;QAC9C,IAAI,CAACrL,YAAY,CAACmC,GAAG,CAACuE,KAAK,CAAC,EAAEC,QAAQ,CAACyE,YAAY,CAAC;QACpD,IAAI,CAAC9J,iBAAiB,EAAE;QACxB,IAAI,IAAI,CAACA,iBAAiB,GAAG,IAAI,CAACD,eAAe,CAAC5E,MAAM,EAAE;UACxD,IAAI,CAACmK,qBAAqB,EAAE;QAC9B,CAAC,MAAM;UACL,IAAI,CAACvH,iBAAiB,CAACoE,aAAa,CAAC,oCAAoC,CAAC;UAC1E,IAAI,CAAC1I,eAAe,EAAE;QACxB;MACF,CAAC;MACDkQ,MAAM,CAACK,aAAa,CAAChC,IAAI,CAAC;IAC5B;IAEA;IACAiC,uBAAuBA,CAAA;MACrB,IAAI,CAACnD,YAAY,EAAE;IACrB;IAEAoD,2BAA2BA,CAACC,WAAoB;MAC9C,IAAI,CAACjK,qBAAqB,GAAGiK,WAAW;IAC1C;IAEAC,mBAAmBA,CAAC/I,KAA2D;MAC7E;MACA,MAAM0B,KAAK,GAAG,IAAI,CAACnH,MAAM,CAACyO,IAAI,CAACvC,CAAC,IAAIA,CAAC,CAACpK,EAAE,KAAK2D,KAAK,CAACiJ,OAAO,CAAC;MAC3D,IAAIvH,KAAK,IAAIA,KAAK,CAACS,MAAM,IAAIT,KAAK,CAACS,MAAM,CAACnC,KAAK,CAACkJ,UAAU,CAAC,EAAE;QAC3DxH,KAAK,CAACS,MAAM,CAACnC,KAAK,CAACkJ,UAAU,CAAC,CAAC1C,KAAK,GAAGxG,KAAK,CAACwG,KAAK;QAElD;QACA,MAAMxE,WAAW,GAAGN,KAAK,CAACS,MAAM,CAACnC,KAAK,CAACkJ,UAAU,CAAC,CAAClH,WAAW;QAC9D,IAAI,IAAI,CAAC3E,YAAY,CAACmC,GAAG,CAACwC,WAAW,CAAC,EAAE;UACtC,IAAI,CAAC3E,YAAY,CAACmC,GAAG,CAACwC,WAAW,CAAC,EAAEgC,QAAQ,CAAChE,KAAK,CAACwG,KAAK,CAAC;QAC3D;MACF;IACF;IAEA2C,mBAAmBA,CAACnJ,KAA2D;MAC7E;MACA,MAAM0B,KAAK,GAAG,IAAI,CAACnH,MAAM,CAACyO,IAAI,CAACvC,CAAC,IAAIA,CAAC,CAACpK,EAAE,KAAK2D,KAAK,CAACiJ,OAAO,CAAC;MAC3D,IAAIvH,KAAK,IAAIA,KAAK,CAACS,MAAM,IAAIT,KAAK,CAACS,MAAM,CAACnC,KAAK,CAACkJ,UAAU,CAAC,EAAE;QAC3DxH,KAAK,CAACS,MAAM,CAACnC,KAAK,CAACkJ,UAAU,CAAC,CAACd,KAAK,GAAGpI,KAAK,CAACoI,KAAK;QAElD;QACA,IAAI,CAAC/J,aAAa,GAAG,CAAC,GAAG,IAAI,CAACA,aAAa,EAAE,GAAG2B,KAAK,CAACoI,KAAK,CAAC;MAC9D;IACF;IAEAgB,aAAaA,CAACpJ,KAA2E;MACvFC,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEF,KAAK,CAAC;MAE9C;MACA,MAAM0B,KAAK,GAAG,IAAI,CAACnH,MAAM,CAACyO,IAAI,CAACvC,CAAC,IAAIA,CAAC,CAACpK,EAAE,KAAK2D,KAAK,CAACiJ,OAAO,CAAC;MAC3D,IAAIvH,KAAK,IAAIA,KAAK,CAACS,MAAM,IAAIT,KAAK,CAACS,MAAM,CAACnC,KAAK,CAACkJ,UAAU,CAAC,EAAE;QAC3DxH,KAAK,CAACS,MAAM,CAACnC,KAAK,CAACkJ,UAAU,CAAC,CAAC1C,KAAK,GAAGxG,KAAK,CAACwG,KAAK;QAElD;QACA,IAAIxG,KAAK,CAACoI,KAAK,EAAE;UACf1G,KAAK,CAACS,MAAM,CAACnC,KAAK,CAACkJ,UAAU,CAAC,CAACd,KAAK,GAAGpI,KAAK,CAACoI,KAAK;UAClD,IAAI,CAAC/J,aAAa,GAAG,CAAC,GAAG,IAAI,CAACA,aAAa,EAAE,GAAG2B,KAAK,CAACoI,KAAK,CAAC;QAC9D;QAEA;QACA,MAAMpG,WAAW,GAAGN,KAAK,CAACS,MAAM,CAACnC,KAAK,CAACkJ,UAAU,CAAC,CAAClH,WAAW;QAC9D/B,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE8B,WAAW,EAAE,aAAa,EAAEhC,KAAK,CAACwG,KAAK,CAAC;QAC5EvG,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEmC,MAAM,CAACe,IAAI,CAAC,IAAI,CAAC/F,YAAY,CAACgG,QAAQ,CAAC,CAAC;QAChFpD,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE,IAAI,CAAC7C,YAAY,CAACmJ,KAAK,CAAC;QAEzE,IAAI,IAAI,CAACnJ,YAAY,CAACmC,GAAG,CAACwC,WAAW,CAAC,EAAE;UACtC,IAAI,CAAC3E,YAAY,CAACmC,GAAG,CAACwC,WAAW,CAAC,EAAEgC,QAAQ,CAAChE,KAAK,CAACwG,KAAK,CAAC;UACzDvG,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAAC7C,YAAY,CAACmJ,KAAK,CAAC;QAClE,CAAC,MAAM;UACLvG,OAAO,CAACkB,IAAI,CAAC,yCAAyC,EAAEa,WAAW,CAAC;UACpE;UACA,IAAI,CAAC3E,YAAY,CAAC2F,UAAU,CAAChB,WAAW,EAAE,IAAI,CAAC7F,WAAW,CAAC8G,OAAO,CAACjD,KAAK,CAACwG,KAAK,CAAC,CAAC;UAChFvG,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE,IAAI,CAAC7C,YAAY,CAACmJ,KAAK,CAAC;QAC7E;QAEA;QACA,IAAI,CAAC3M,YAAY,CAACiM,IAAI,CAAC;UACrBvM,OAAO,EAAE,UAAUmI,KAAK,CAACxI,IAAI,cAAcwI,KAAK,CAACS,MAAM,CAACnC,KAAK,CAACkJ,UAAU,CAAC,CAACG,SAAS,MAAMrJ,KAAK,CAACwG,KAAK,EAAE;UACtGlN,KAAK,EAAE;SACR,CAAC;MACJ;IACF;IAEA;;;IAGA,IAAIkB,aAAaA,CAAA;MACf,IAAI,IAAI,CAACZ,WAAW,EAAE;QACpB,OAAO,IAAI,CAACC,YAAY;MAC1B;MACA,OAAO,IAAI,CAACA,YAAY,CAAC2I,KAAK,CAAC,CAAC,EAAE,IAAI,CAACzI,kBAAkB,CAAC;IAC5D;IAEA;;;IAGAL,iBAAiBA,CAAA;MACf,IAAI,CAACE,WAAW,GAAG,CAAC,IAAI,CAACA,WAAW;IACtC;IAEA;;;IAGA,IAAIa,WAAWA,CAAA;MACb,OAAO,IAAI,CAACZ,YAAY,CAACC,MAAM,GAAG,IAAI,CAACC,kBAAkB,IAAI,CAAC,IAAI,CAACH,WAAW;IAChF;IAEA;;;IAGQ+G,kBAAkBA,CAAA;MACxB;MACA,IAAI,IAAI,CAAC9G,YAAY,CAACC,MAAM,KAAK,CAAC,EAAE;QAClC,MAAMwP,QAAQ,GAAG,CACf;UAAE/P,OAAO,EAAE,4BAA4B;UAAED,KAAK,EAAE;QAAS,CAAE,EAC3D;UAAEC,OAAO,EAAE,mCAAmC;UAAED,KAAK,EAAE;QAAS,CAAE,EAClE;UAAEC,OAAO,EAAE,yCAAyC;UAAED,KAAK,EAAE;QAAS,CAAE,EACxE;UAAEC,OAAO,EAAE,6BAA6B;UAAED,KAAK,EAAE;QAAS,CAAE,EAC5D;UAAEC,OAAO,EAAE,8BAA8B;UAAED,KAAK,EAAE;QAAS,CAAE,EAC7D;UAAEC,OAAO,EAAE,kCAAkC;UAAED,KAAK,EAAE;QAAS,CAAE,EACjE;UAAEC,OAAO,EAAE,8BAA8B;UAAED,KAAK,EAAE;QAAS,CAAE,EAC7D;UAAEC,OAAO,EAAE,0BAA0B;UAAED,KAAK,EAAE;QAAS,CAAE,EACzD;UAAEC,OAAO,EAAE,6CAA6C;UAAED,KAAK,EAAE;QAAS,CAAE,EAC5E;UAAEC,OAAO,EAAE,sCAAsC;UAAED,KAAK,EAAE;QAAS,CAAE,EACrE;UAAEC,OAAO,EAAE,iCAAiC;UAAED,KAAK,EAAE;QAAS,CAAE,EAChE;UAAEC,OAAO,EAAE,iCAAiC;UAAED,KAAK,EAAE;QAAS,CAAE,EAChE;UAAEC,OAAO,EAAE,8BAA8B;UAAED,KAAK,EAAE;QAAS,CAAE,EAC7D;UAAEC,OAAO,EAAE,iCAAiC;UAAED,KAAK,EAAE;QAAS,CAAE,EAChE;UAAEC,OAAO,EAAE,sCAAsC;UAAED,KAAK,EAAE;QAAS,CAAE,EACrE;UAAEC,OAAO,EAAE,uCAAuC;UAAED,KAAK,EAAE;QAAS,CAAE,EACtE;UAAEC,OAAO,EAAE,gDAAgD;UAAED,KAAK,EAAE;QAAS,CAAE,EAC/E;UAAEC,OAAO,EAAE,qCAAqC;UAAED,KAAK,EAAE;QAAS,CAAE,EACpE;UAAEC,OAAO,EAAE,8BAA8B;UAAED,KAAK,EAAE;QAAS,CAAE,EAC7D;UAAEC,OAAO,EAAE,sCAAsC;UAAED,KAAK,EAAE;QAAS,CAAE,EACrE;UAAEC,OAAO,EAAE,wCAAwC;UAAED,KAAK,EAAE;QAAS,CAAE,EACvE;UAAEC,OAAO,EAAE,0CAA0C;UAAED,KAAK,EAAE;QAAS,CAAE,EACzE;UAAEC,OAAO,EAAE,4CAA4C;UAAED,KAAK,EAAE;QAAS,CAAE,EAC3E;UAAEC,OAAO,EAAE,8BAA8B;UAAED,KAAK,EAAE;QAAS,CAAE,CAC9D;QAED,IAAI,CAACO,YAAY,GAAGyP,QAAQ;MAC9B;IACF;;uCAx0BW1N,0BAA0B,EAAAjE,EAAA,CAAA4R,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA9R,EAAA,CAAA4R,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAA/R,EAAA,CAAA4R,iBAAA,CAAAI,EAAA,CAAAC,eAAA,GAAAjS,EAAA,CAAA4R,iBAAA,CAAAM,EAAA,CAAAC,6BAAA,GAAAnS,EAAA,CAAA4R,iBAAA,CAAAQ,EAAA,CAAAC,mBAAA,GAAArS,EAAA,CAAA4R,iBAAA,CAAAQ,EAAA,CAAAE,aAAA,GAAAtS,EAAA,CAAA4R,iBAAA,CAAAW,EAAA,CAAAC,WAAA;IAAA;;YAA1BvO,0BAA0B;MAAAwO,SAAA;MAAAC,SAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;yBAY1BpT,sBAAsB;;;;;;;;;;;;UCvEnCQ,EAAA,CAAAC,cAAA,aAA0C;;UAIpCD,EAFJ,CAAAC,cAAA,aAAqD,WAC7C,wBAC4D;UAE9DD,EADA,CAAAgB,SAAA,cAAyC,cACE;UAGjDhB,EAFI,CAAAU,YAAA,EAAiB,EACZ,EACH;;UAIFV,EAHJ,CAAAC,cAAA,aAA2C,aAEyE,iCAY7E;UAHjCD,EAJA,CAAAE,UAAA,yBAAA4S,mFAAA;YAAA,OAAeD,GAAA,CAAA5B,uBAAA,EAAyB;UAAA,EAAC,6BAAA8B,uFAAAC,MAAA;YAAA,OACtBH,GAAA,CAAA3B,2BAAA,CAAA8B,MAAA,CAAmC;UAAA,EAAC,+BAAAC,yFAAAD,MAAA;YAAA,OAClCH,GAAA,CAAAzB,mBAAA,CAAA4B,MAAA,CAA2B;UAAA,EAAC,+BAAAE,yFAAAF,MAAA;YAAA,OAC5BH,GAAA,CAAArB,mBAAA,CAAAwB,MAAA,CAA2B;UAAA,EAAC,yBAAAG,mFAAAH,MAAA;YAAA,OAClCH,GAAA,CAAApB,aAAA,CAAAuB,MAAA,CAAqB;UAAA,EAAC;UAIvChT,EAAA,CAAAU,YAAA,EAA0B;UAG1BV,EAAA,CAAAiB,UAAA,IAAAmS,yCAAA,iBAAqE;UAWvEpT,EAAA,CAAAU,YAAA,EAAM;UAOAV,EAJN,CAAAC,cAAA,cAA8G,eAEnF,eACK,oBAQzB;UAFCD,EAAA,CAAAE,UAAA,uBAAAmT,mEAAAL,MAAA;YAAA,OAAaH,GAAA,CAAAzK,WAAA,CAAA4K,MAAA,CAAmB;UAAA,EAAC;UAGrChT,EADG,CAAAU,YAAA,EAAW,EACR;UACNV,EAAA,CAAAC,cAAA,eAA4B;UAC1BD,EAAA,CAAAgB,SAAA,sBAWa;UAEjBhB,EADE,CAAAU,YAAA,EAAM,EACF;UAGNV,EAAA,CAAAC,cAAA,eAA8B;UA8H5BD,EA5HA,CAAAiB,UAAA,KAAAqS,0CAAA,mBAAkF,KAAAC,0CAAA,mBA8DC,KAAAC,0CAAA,kBA8DO;UASlGxT,EAHM,CAAAU,YAAA,EAAM,EACF,EACF,EACF;;;UAnMsBV,EAAA,CAAAW,SAAA,GAAyC;UAAzCX,EAAA,CAAAmB,WAAA,cAAA0R,GAAA,CAAA3L,qBAAA,CAAyC;UAE7DlH,EAAA,CAAAW,SAAA,EAAiB;UAEjBX,EAFA,CAAAY,UAAA,WAAAiS,GAAA,CAAAjQ,MAAA,CAAiB,gBAAAiQ,GAAA,CAAA3L,qBAAA,CACoB,iBAAA2L,GAAA,CAAAhO,YAAA,CACR;UAYQ7E,EAAA,CAAAW,SAAA,EAA4B;UAA5BX,EAAA,CAAAY,UAAA,UAAAiS,GAAA,CAAA3L,qBAAA,CAA4B;UAc5ClH,EAAA,CAAAW,SAAA,EAAwC;UAAxCX,EAAA,CAAAmB,WAAA,aAAA0R,GAAA,CAAA3L,qBAAA,CAAwC;UAKzDlH,EAAA,CAAAW,SAAA,GAAuB;UAGvBX,EAHA,CAAAY,UAAA,SAAAiS,GAAA,CAAApO,cAAA,CAAuB,gBAAAoO,GAAA,CAAA5L,WAAA,CACI,4BAEA;UAS3BjH,EAAA,CAAAW,SAAA,GAKE;UALFX,EAAA,CAAAY,UAAA,iBAAAZ,EAAA,CAAAa,eAAA,KAAA4S,GAAA,EAKE;UAUAzT,EAAA,CAAAW,SAAA,GAAgC;UAAhCX,EAAA,CAAAY,UAAA,SAAAiS,GAAA,CAAA5L,WAAA,gBAAgC;UA8DhCjH,EAAA,CAAAW,SAAA,EAAoC;UAApCX,EAAA,CAAAY,UAAA,SAAAiS,GAAA,CAAA5L,WAAA,oBAAoC;UA8DpCjH,EAAA,CAAAW,SAAA,EAAoC;UAApCX,EAAA,CAAAY,UAAA,SAAAiS,GAAA,CAAA5L,WAAA,oBAAoC;;;qBDvJ9C9H,YAAY,EAAAuU,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZtU,WAAW,EAGXK,aAAa,EACbF,eAAe,EACfC,aAAa,EACbK,2BAA2B;MAAA8T,MAAA;IAAA;;SAKlB5P,0BAA0B;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}