{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Position } from '../core/position.js';\nimport { Range } from '../core/range.js';\nimport { countEOL } from '../core/eolCounter.js';\n/**\n * Represents sparse tokens over a contiguous range of lines.\n */\nexport class SparseMultilineTokens {\n  static create(startLineNumber, tokens) {\n    return new SparseMultilineTokens(startLineNumber, new SparseMultilineTokensStorage(tokens));\n  }\n  /**\n   * (Inclusive) start line number for these tokens.\n   */\n  get startLineNumber() {\n    return this._startLineNumber;\n  }\n  /**\n   * (Inclusive) end line number for these tokens.\n   */\n  get endLineNumber() {\n    return this._endLineNumber;\n  }\n  constructor(startLineNumber, tokens) {\n    this._startLineNumber = startLineNumber;\n    this._tokens = tokens;\n    this._endLineNumber = this._startLineNumber + this._tokens.getMaxDeltaLine();\n  }\n  toString() {\n    return this._tokens.toString(this._startLineNumber);\n  }\n  _updateEndLineNumber() {\n    this._endLineNumber = this._startLineNumber + this._tokens.getMaxDeltaLine();\n  }\n  isEmpty() {\n    return this._tokens.isEmpty();\n  }\n  getLineTokens(lineNumber) {\n    if (this._startLineNumber <= lineNumber && lineNumber <= this._endLineNumber) {\n      return this._tokens.getLineTokens(lineNumber - this._startLineNumber);\n    }\n    return null;\n  }\n  getRange() {\n    const deltaRange = this._tokens.getRange();\n    if (!deltaRange) {\n      return deltaRange;\n    }\n    return new Range(this._startLineNumber + deltaRange.startLineNumber, deltaRange.startColumn, this._startLineNumber + deltaRange.endLineNumber, deltaRange.endColumn);\n  }\n  removeTokens(range) {\n    const startLineIndex = range.startLineNumber - this._startLineNumber;\n    const endLineIndex = range.endLineNumber - this._startLineNumber;\n    this._startLineNumber += this._tokens.removeTokens(startLineIndex, range.startColumn - 1, endLineIndex, range.endColumn - 1);\n    this._updateEndLineNumber();\n  }\n  split(range) {\n    // split tokens to two:\n    // a) all the tokens before `range`\n    // b) all the tokens after `range`\n    const startLineIndex = range.startLineNumber - this._startLineNumber;\n    const endLineIndex = range.endLineNumber - this._startLineNumber;\n    const [a, b, bDeltaLine] = this._tokens.split(startLineIndex, range.startColumn - 1, endLineIndex, range.endColumn - 1);\n    return [new SparseMultilineTokens(this._startLineNumber, a), new SparseMultilineTokens(this._startLineNumber + bDeltaLine, b)];\n  }\n  applyEdit(range, text) {\n    const [eolCount, firstLineLength, lastLineLength] = countEOL(text);\n    this.acceptEdit(range, eolCount, firstLineLength, lastLineLength, text.length > 0 ? text.charCodeAt(0) : 0 /* CharCode.Null */);\n  }\n  acceptEdit(range, eolCount, firstLineLength, lastLineLength, firstCharCode) {\n    this._acceptDeleteRange(range);\n    this._acceptInsertText(new Position(range.startLineNumber, range.startColumn), eolCount, firstLineLength, lastLineLength, firstCharCode);\n    this._updateEndLineNumber();\n  }\n  _acceptDeleteRange(range) {\n    if (range.startLineNumber === range.endLineNumber && range.startColumn === range.endColumn) {\n      // Nothing to delete\n      return;\n    }\n    const firstLineIndex = range.startLineNumber - this._startLineNumber;\n    const lastLineIndex = range.endLineNumber - this._startLineNumber;\n    if (lastLineIndex < 0) {\n      // this deletion occurs entirely before this block, so we only need to adjust line numbers\n      const deletedLinesCount = lastLineIndex - firstLineIndex;\n      this._startLineNumber -= deletedLinesCount;\n      return;\n    }\n    const tokenMaxDeltaLine = this._tokens.getMaxDeltaLine();\n    if (firstLineIndex >= tokenMaxDeltaLine + 1) {\n      // this deletion occurs entirely after this block, so there is nothing to do\n      return;\n    }\n    if (firstLineIndex < 0 && lastLineIndex >= tokenMaxDeltaLine + 1) {\n      // this deletion completely encompasses this block\n      this._startLineNumber = 0;\n      this._tokens.clear();\n      return;\n    }\n    if (firstLineIndex < 0) {\n      const deletedBefore = -firstLineIndex;\n      this._startLineNumber -= deletedBefore;\n      this._tokens.acceptDeleteRange(range.startColumn - 1, 0, 0, lastLineIndex, range.endColumn - 1);\n    } else {\n      this._tokens.acceptDeleteRange(0, firstLineIndex, range.startColumn - 1, lastLineIndex, range.endColumn - 1);\n    }\n  }\n  _acceptInsertText(position, eolCount, firstLineLength, lastLineLength, firstCharCode) {\n    if (eolCount === 0 && firstLineLength === 0) {\n      // Nothing to insert\n      return;\n    }\n    const lineIndex = position.lineNumber - this._startLineNumber;\n    if (lineIndex < 0) {\n      // this insertion occurs before this block, so we only need to adjust line numbers\n      this._startLineNumber += eolCount;\n      return;\n    }\n    const tokenMaxDeltaLine = this._tokens.getMaxDeltaLine();\n    if (lineIndex >= tokenMaxDeltaLine + 1) {\n      // this insertion occurs after this block, so there is nothing to do\n      return;\n    }\n    this._tokens.acceptInsertText(lineIndex, position.column - 1, eolCount, firstLineLength, lastLineLength, firstCharCode);\n  }\n}\nclass SparseMultilineTokensStorage {\n  constructor(tokens) {\n    this._tokens = tokens;\n    this._tokenCount = tokens.length / 4;\n  }\n  toString(startLineNumber) {\n    const pieces = [];\n    for (let i = 0; i < this._tokenCount; i++) {\n      pieces.push(`(${this._getDeltaLine(i) + startLineNumber},${this._getStartCharacter(i)}-${this._getEndCharacter(i)})`);\n    }\n    return `[${pieces.join(',')}]`;\n  }\n  getMaxDeltaLine() {\n    const tokenCount = this._getTokenCount();\n    if (tokenCount === 0) {\n      return -1;\n    }\n    return this._getDeltaLine(tokenCount - 1);\n  }\n  getRange() {\n    const tokenCount = this._getTokenCount();\n    if (tokenCount === 0) {\n      return null;\n    }\n    const startChar = this._getStartCharacter(0);\n    const maxDeltaLine = this._getDeltaLine(tokenCount - 1);\n    const endChar = this._getEndCharacter(tokenCount - 1);\n    return new Range(0, startChar + 1, maxDeltaLine, endChar + 1);\n  }\n  _getTokenCount() {\n    return this._tokenCount;\n  }\n  _getDeltaLine(tokenIndex) {\n    return this._tokens[4 * tokenIndex];\n  }\n  _getStartCharacter(tokenIndex) {\n    return this._tokens[4 * tokenIndex + 1];\n  }\n  _getEndCharacter(tokenIndex) {\n    return this._tokens[4 * tokenIndex + 2];\n  }\n  isEmpty() {\n    return this._getTokenCount() === 0;\n  }\n  getLineTokens(deltaLine) {\n    let low = 0;\n    let high = this._getTokenCount() - 1;\n    while (low < high) {\n      const mid = low + Math.floor((high - low) / 2);\n      const midDeltaLine = this._getDeltaLine(mid);\n      if (midDeltaLine < deltaLine) {\n        low = mid + 1;\n      } else if (midDeltaLine > deltaLine) {\n        high = mid - 1;\n      } else {\n        let min = mid;\n        while (min > low && this._getDeltaLine(min - 1) === deltaLine) {\n          min--;\n        }\n        let max = mid;\n        while (max < high && this._getDeltaLine(max + 1) === deltaLine) {\n          max++;\n        }\n        return new SparseLineTokens(this._tokens.subarray(4 * min, 4 * max + 4));\n      }\n    }\n    if (this._getDeltaLine(low) === deltaLine) {\n      return new SparseLineTokens(this._tokens.subarray(4 * low, 4 * low + 4));\n    }\n    return null;\n  }\n  clear() {\n    this._tokenCount = 0;\n  }\n  removeTokens(startDeltaLine, startChar, endDeltaLine, endChar) {\n    const tokens = this._tokens;\n    const tokenCount = this._tokenCount;\n    let newTokenCount = 0;\n    let hasDeletedTokens = false;\n    let firstDeltaLine = 0;\n    for (let i = 0; i < tokenCount; i++) {\n      const srcOffset = 4 * i;\n      const tokenDeltaLine = tokens[srcOffset];\n      const tokenStartCharacter = tokens[srcOffset + 1];\n      const tokenEndCharacter = tokens[srcOffset + 2];\n      const tokenMetadata = tokens[srcOffset + 3];\n      if ((tokenDeltaLine > startDeltaLine || tokenDeltaLine === startDeltaLine && tokenEndCharacter >= startChar) && (tokenDeltaLine < endDeltaLine || tokenDeltaLine === endDeltaLine && tokenStartCharacter <= endChar)) {\n        hasDeletedTokens = true;\n      } else {\n        if (newTokenCount === 0) {\n          firstDeltaLine = tokenDeltaLine;\n        }\n        if (hasDeletedTokens) {\n          // must move the token to the left\n          const destOffset = 4 * newTokenCount;\n          tokens[destOffset] = tokenDeltaLine - firstDeltaLine;\n          tokens[destOffset + 1] = tokenStartCharacter;\n          tokens[destOffset + 2] = tokenEndCharacter;\n          tokens[destOffset + 3] = tokenMetadata;\n        }\n        newTokenCount++;\n      }\n    }\n    this._tokenCount = newTokenCount;\n    return firstDeltaLine;\n  }\n  split(startDeltaLine, startChar, endDeltaLine, endChar) {\n    const tokens = this._tokens;\n    const tokenCount = this._tokenCount;\n    const aTokens = [];\n    const bTokens = [];\n    let destTokens = aTokens;\n    let destOffset = 0;\n    let destFirstDeltaLine = 0;\n    for (let i = 0; i < tokenCount; i++) {\n      const srcOffset = 4 * i;\n      const tokenDeltaLine = tokens[srcOffset];\n      const tokenStartCharacter = tokens[srcOffset + 1];\n      const tokenEndCharacter = tokens[srcOffset + 2];\n      const tokenMetadata = tokens[srcOffset + 3];\n      if (tokenDeltaLine > startDeltaLine || tokenDeltaLine === startDeltaLine && tokenEndCharacter >= startChar) {\n        if (tokenDeltaLine < endDeltaLine || tokenDeltaLine === endDeltaLine && tokenStartCharacter <= endChar) {\n          // this token is touching the range\n          continue;\n        } else {\n          // this token is after the range\n          if (destTokens !== bTokens) {\n            // this token is the first token after the range\n            destTokens = bTokens;\n            destOffset = 0;\n            destFirstDeltaLine = tokenDeltaLine;\n          }\n        }\n      }\n      destTokens[destOffset++] = tokenDeltaLine - destFirstDeltaLine;\n      destTokens[destOffset++] = tokenStartCharacter;\n      destTokens[destOffset++] = tokenEndCharacter;\n      destTokens[destOffset++] = tokenMetadata;\n    }\n    return [new SparseMultilineTokensStorage(new Uint32Array(aTokens)), new SparseMultilineTokensStorage(new Uint32Array(bTokens)), destFirstDeltaLine];\n  }\n  acceptDeleteRange(horizontalShiftForFirstLineTokens, startDeltaLine, startCharacter, endDeltaLine, endCharacter) {\n    // This is a bit complex, here are the cases I used to think about this:\n    //\n    // 1. The token starts before the deletion range\n    // 1a. The token is completely before the deletion range\n    //               -----------\n    //                          xxxxxxxxxxx\n    // 1b. The token starts before, the deletion range ends after the token\n    //               -----------\n    //                      xxxxxxxxxxx\n    // 1c. The token starts before, the deletion range ends precisely with the token\n    //               ---------------\n    //                      xxxxxxxx\n    // 1d. The token starts before, the deletion range is inside the token\n    //               ---------------\n    //                    xxxxx\n    //\n    // 2. The token starts at the same position with the deletion range\n    // 2a. The token starts at the same position, and ends inside the deletion range\n    //               -------\n    //               xxxxxxxxxxx\n    // 2b. The token starts at the same position, and ends at the same position as the deletion range\n    //               ----------\n    //               xxxxxxxxxx\n    // 2c. The token starts at the same position, and ends after the deletion range\n    //               -------------\n    //               xxxxxxx\n    //\n    // 3. The token starts inside the deletion range\n    // 3a. The token is inside the deletion range\n    //                -------\n    //             xxxxxxxxxxxxx\n    // 3b. The token starts inside the deletion range, and ends at the same position as the deletion range\n    //                ----------\n    //             xxxxxxxxxxxxx\n    // 3c. The token starts inside the deletion range, and ends after the deletion range\n    //                ------------\n    //             xxxxxxxxxxx\n    //\n    // 4. The token starts after the deletion range\n    //                  -----------\n    //          xxxxxxxx\n    //\n    const tokens = this._tokens;\n    const tokenCount = this._tokenCount;\n    const deletedLineCount = endDeltaLine - startDeltaLine;\n    let newTokenCount = 0;\n    let hasDeletedTokens = false;\n    for (let i = 0; i < tokenCount; i++) {\n      const srcOffset = 4 * i;\n      let tokenDeltaLine = tokens[srcOffset];\n      let tokenStartCharacter = tokens[srcOffset + 1];\n      let tokenEndCharacter = tokens[srcOffset + 2];\n      const tokenMetadata = tokens[srcOffset + 3];\n      if (tokenDeltaLine < startDeltaLine || tokenDeltaLine === startDeltaLine && tokenEndCharacter <= startCharacter) {\n        // 1a. The token is completely before the deletion range\n        // => nothing to do\n        newTokenCount++;\n        continue;\n      } else if (tokenDeltaLine === startDeltaLine && tokenStartCharacter < startCharacter) {\n        // 1b, 1c, 1d\n        // => the token survives, but it needs to shrink\n        if (tokenDeltaLine === endDeltaLine && tokenEndCharacter > endCharacter) {\n          // 1d. The token starts before, the deletion range is inside the token\n          // => the token shrinks by the deletion character count\n          tokenEndCharacter -= endCharacter - startCharacter;\n        } else {\n          // 1b. The token starts before, the deletion range ends after the token\n          // 1c. The token starts before, the deletion range ends precisely with the token\n          // => the token shrinks its ending to the deletion start\n          tokenEndCharacter = startCharacter;\n        }\n      } else if (tokenDeltaLine === startDeltaLine && tokenStartCharacter === startCharacter) {\n        // 2a, 2b, 2c\n        if (tokenDeltaLine === endDeltaLine && tokenEndCharacter > endCharacter) {\n          // 2c. The token starts at the same position, and ends after the deletion range\n          // => the token shrinks by the deletion character count\n          tokenEndCharacter -= endCharacter - startCharacter;\n        } else {\n          // 2a. The token starts at the same position, and ends inside the deletion range\n          // 2b. The token starts at the same position, and ends at the same position as the deletion range\n          // => the token is deleted\n          hasDeletedTokens = true;\n          continue;\n        }\n      } else if (tokenDeltaLine < endDeltaLine || tokenDeltaLine === endDeltaLine && tokenStartCharacter < endCharacter) {\n        // 3a, 3b, 3c\n        if (tokenDeltaLine === endDeltaLine && tokenEndCharacter > endCharacter) {\n          // 3c. The token starts inside the deletion range, and ends after the deletion range\n          // => the token moves to continue right after the deletion\n          tokenDeltaLine = startDeltaLine;\n          tokenStartCharacter = startCharacter;\n          tokenEndCharacter = tokenStartCharacter + (tokenEndCharacter - endCharacter);\n        } else {\n          // 3a. The token is inside the deletion range\n          // 3b. The token starts inside the deletion range, and ends at the same position as the deletion range\n          // => the token is deleted\n          hasDeletedTokens = true;\n          continue;\n        }\n      } else if (tokenDeltaLine > endDeltaLine) {\n        // 4. (partial) The token starts after the deletion range, on a line below...\n        if (deletedLineCount === 0 && !hasDeletedTokens) {\n          // early stop, there is no need to walk all the tokens and do nothing...\n          newTokenCount = tokenCount;\n          break;\n        }\n        tokenDeltaLine -= deletedLineCount;\n      } else if (tokenDeltaLine === endDeltaLine && tokenStartCharacter >= endCharacter) {\n        // 4. (continued) The token starts after the deletion range, on the last line where a deletion occurs\n        if (horizontalShiftForFirstLineTokens && tokenDeltaLine === 0) {\n          tokenStartCharacter += horizontalShiftForFirstLineTokens;\n          tokenEndCharacter += horizontalShiftForFirstLineTokens;\n        }\n        tokenDeltaLine -= deletedLineCount;\n        tokenStartCharacter -= endCharacter - startCharacter;\n        tokenEndCharacter -= endCharacter - startCharacter;\n      } else {\n        throw new Error(`Not possible!`);\n      }\n      const destOffset = 4 * newTokenCount;\n      tokens[destOffset] = tokenDeltaLine;\n      tokens[destOffset + 1] = tokenStartCharacter;\n      tokens[destOffset + 2] = tokenEndCharacter;\n      tokens[destOffset + 3] = tokenMetadata;\n      newTokenCount++;\n    }\n    this._tokenCount = newTokenCount;\n  }\n  acceptInsertText(deltaLine, character, eolCount, firstLineLength, lastLineLength, firstCharCode) {\n    // Here are the cases I used to think about this:\n    //\n    // 1. The token is completely before the insertion point\n    //            -----------   |\n    // 2. The token ends precisely at the insertion point\n    //            -----------|\n    // 3. The token contains the insertion point\n    //            -----|------\n    // 4. The token starts precisely at the insertion point\n    //            |-----------\n    // 5. The token is completely after the insertion point\n    //            |   -----------\n    //\n    const isInsertingPreciselyOneWordCharacter = eolCount === 0 && firstLineLength === 1 && (firstCharCode >= 48 /* CharCode.Digit0 */ && firstCharCode <= 57 /* CharCode.Digit9 */ || firstCharCode >= 65 /* CharCode.A */ && firstCharCode <= 90 /* CharCode.Z */ || firstCharCode >= 97 /* CharCode.a */ && firstCharCode <= 122 /* CharCode.z */);\n    const tokens = this._tokens;\n    const tokenCount = this._tokenCount;\n    for (let i = 0; i < tokenCount; i++) {\n      const offset = 4 * i;\n      let tokenDeltaLine = tokens[offset];\n      let tokenStartCharacter = tokens[offset + 1];\n      let tokenEndCharacter = tokens[offset + 2];\n      if (tokenDeltaLine < deltaLine || tokenDeltaLine === deltaLine && tokenEndCharacter < character) {\n        // 1. The token is completely before the insertion point\n        // => nothing to do\n        continue;\n      } else if (tokenDeltaLine === deltaLine && tokenEndCharacter === character) {\n        // 2. The token ends precisely at the insertion point\n        // => expand the end character only if inserting precisely one character that is a word character\n        if (isInsertingPreciselyOneWordCharacter) {\n          tokenEndCharacter += 1;\n        } else {\n          continue;\n        }\n      } else if (tokenDeltaLine === deltaLine && tokenStartCharacter < character && character < tokenEndCharacter) {\n        // 3. The token contains the insertion point\n        if (eolCount === 0) {\n          // => just expand the end character\n          tokenEndCharacter += firstLineLength;\n        } else {\n          // => cut off the token\n          tokenEndCharacter = character;\n        }\n      } else {\n        // 4. or 5.\n        if (tokenDeltaLine === deltaLine && tokenStartCharacter === character) {\n          // 4. The token starts precisely at the insertion point\n          // => grow the token (by keeping its start constant) only if inserting precisely one character that is a word character\n          // => otherwise behave as in case 5.\n          if (isInsertingPreciselyOneWordCharacter) {\n            continue;\n          }\n        }\n        // => the token must move and keep its size constant\n        if (tokenDeltaLine === deltaLine) {\n          tokenDeltaLine += eolCount;\n          // this token is on the line where the insertion is taking place\n          if (eolCount === 0) {\n            tokenStartCharacter += firstLineLength;\n            tokenEndCharacter += firstLineLength;\n          } else {\n            const tokenLength = tokenEndCharacter - tokenStartCharacter;\n            tokenStartCharacter = lastLineLength + (tokenStartCharacter - character);\n            tokenEndCharacter = tokenStartCharacter + tokenLength;\n          }\n        } else {\n          tokenDeltaLine += eolCount;\n        }\n      }\n      tokens[offset] = tokenDeltaLine;\n      tokens[offset + 1] = tokenStartCharacter;\n      tokens[offset + 2] = tokenEndCharacter;\n    }\n  }\n}\nexport class SparseLineTokens {\n  constructor(tokens) {\n    this._tokens = tokens;\n  }\n  getCount() {\n    return this._tokens.length / 4;\n  }\n  getStartCharacter(tokenIndex) {\n    return this._tokens[4 * tokenIndex + 1];\n  }\n  getEndCharacter(tokenIndex) {\n    return this._tokens[4 * tokenIndex + 2];\n  }\n  getMetadata(tokenIndex) {\n    return this._tokens[4 * tokenIndex + 3];\n  }\n}", "map": {"version": 3, "names": ["Position", "Range", "countEOL", "SparseMultilineTokens", "create", "startLineNumber", "tokens", "SparseMultilineTokensStorage", "_startLineNumber", "endLineNumber", "_endLineNumber", "constructor", "_tokens", "getMaxDeltaLine", "toString", "_updateEndLineNumber", "isEmpty", "getLineTokens", "lineNumber", "getRange", "deltaRange", "startColumn", "endColumn", "removeTokens", "range", "startLineIndex", "endLineIndex", "split", "a", "b", "bDeltaLine", "applyEdit", "text", "eolCount", "firstLine<PERSON>ength", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acceptEdit", "length", "charCodeAt", "firstCharCode", "_acceptDeleteRange", "_acceptInsertText", "firstLineIndex", "lastLineIndex", "deletedLinesCount", "tokenMaxDeltaLine", "clear", "deletedBefore", "acceptDeleteRange", "position", "lineIndex", "acceptInsertText", "column", "_tokenCount", "pieces", "i", "push", "_getDeltaLine", "_getStartCharacter", "_getEndCharacter", "join", "tokenCount", "_getTokenCount", "startChar", "maxDeltaLine", "endChar", "tokenIndex", "deltaLine", "low", "high", "mid", "Math", "floor", "midDeltaLine", "min", "max", "SparseLineTokens", "subarray", "startDeltaLine", "endDeltaLine", "newTokenCount", "hasDeletedTokens", "firstDeltaLine", "srcOffset", "tokenDeltaLine", "tokenStartCharacter", "tokenEndCharacter", "tokenMetadata", "destOffset", "aTokens", "bTokens", "destTokens", "destFirstDeltaLine", "Uint32Array", "horizontalShiftForFirstLineTokens", "startCharacter", "endCharacter", "deletedLineCount", "Error", "character", "isInsertingPreciselyOneWordCharacter", "offset", "token<PERSON><PERSON>th", "getCount", "getStartCharacter", "getEndCharacter", "getMetadata"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/tokens/sparseMultilineTokens.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Position } from '../core/position.js';\nimport { Range } from '../core/range.js';\nimport { countEOL } from '../core/eolCounter.js';\n/**\n * Represents sparse tokens over a contiguous range of lines.\n */\nexport class SparseMultilineTokens {\n    static create(startLineNumber, tokens) {\n        return new SparseMultilineTokens(startLineNumber, new SparseMultilineTokensStorage(tokens));\n    }\n    /**\n     * (Inclusive) start line number for these tokens.\n     */\n    get startLineNumber() {\n        return this._startLineNumber;\n    }\n    /**\n     * (Inclusive) end line number for these tokens.\n     */\n    get endLineNumber() {\n        return this._endLineNumber;\n    }\n    constructor(startLineNumber, tokens) {\n        this._startLineNumber = startLineNumber;\n        this._tokens = tokens;\n        this._endLineNumber = this._startLineNumber + this._tokens.getMaxDeltaLine();\n    }\n    toString() {\n        return this._tokens.toString(this._startLineNumber);\n    }\n    _updateEndLineNumber() {\n        this._endLineNumber = this._startLineNumber + this._tokens.getMaxDeltaLine();\n    }\n    isEmpty() {\n        return this._tokens.isEmpty();\n    }\n    getLineTokens(lineNumber) {\n        if (this._startLineNumber <= lineNumber && lineNumber <= this._endLineNumber) {\n            return this._tokens.getLineTokens(lineNumber - this._startLineNumber);\n        }\n        return null;\n    }\n    getRange() {\n        const deltaRange = this._tokens.getRange();\n        if (!deltaRange) {\n            return deltaRange;\n        }\n        return new Range(this._startLineNumber + deltaRange.startLineNumber, deltaRange.startColumn, this._startLineNumber + deltaRange.endLineNumber, deltaRange.endColumn);\n    }\n    removeTokens(range) {\n        const startLineIndex = range.startLineNumber - this._startLineNumber;\n        const endLineIndex = range.endLineNumber - this._startLineNumber;\n        this._startLineNumber += this._tokens.removeTokens(startLineIndex, range.startColumn - 1, endLineIndex, range.endColumn - 1);\n        this._updateEndLineNumber();\n    }\n    split(range) {\n        // split tokens to two:\n        // a) all the tokens before `range`\n        // b) all the tokens after `range`\n        const startLineIndex = range.startLineNumber - this._startLineNumber;\n        const endLineIndex = range.endLineNumber - this._startLineNumber;\n        const [a, b, bDeltaLine] = this._tokens.split(startLineIndex, range.startColumn - 1, endLineIndex, range.endColumn - 1);\n        return [new SparseMultilineTokens(this._startLineNumber, a), new SparseMultilineTokens(this._startLineNumber + bDeltaLine, b)];\n    }\n    applyEdit(range, text) {\n        const [eolCount, firstLineLength, lastLineLength] = countEOL(text);\n        this.acceptEdit(range, eolCount, firstLineLength, lastLineLength, text.length > 0 ? text.charCodeAt(0) : 0 /* CharCode.Null */);\n    }\n    acceptEdit(range, eolCount, firstLineLength, lastLineLength, firstCharCode) {\n        this._acceptDeleteRange(range);\n        this._acceptInsertText(new Position(range.startLineNumber, range.startColumn), eolCount, firstLineLength, lastLineLength, firstCharCode);\n        this._updateEndLineNumber();\n    }\n    _acceptDeleteRange(range) {\n        if (range.startLineNumber === range.endLineNumber && range.startColumn === range.endColumn) {\n            // Nothing to delete\n            return;\n        }\n        const firstLineIndex = range.startLineNumber - this._startLineNumber;\n        const lastLineIndex = range.endLineNumber - this._startLineNumber;\n        if (lastLineIndex < 0) {\n            // this deletion occurs entirely before this block, so we only need to adjust line numbers\n            const deletedLinesCount = lastLineIndex - firstLineIndex;\n            this._startLineNumber -= deletedLinesCount;\n            return;\n        }\n        const tokenMaxDeltaLine = this._tokens.getMaxDeltaLine();\n        if (firstLineIndex >= tokenMaxDeltaLine + 1) {\n            // this deletion occurs entirely after this block, so there is nothing to do\n            return;\n        }\n        if (firstLineIndex < 0 && lastLineIndex >= tokenMaxDeltaLine + 1) {\n            // this deletion completely encompasses this block\n            this._startLineNumber = 0;\n            this._tokens.clear();\n            return;\n        }\n        if (firstLineIndex < 0) {\n            const deletedBefore = -firstLineIndex;\n            this._startLineNumber -= deletedBefore;\n            this._tokens.acceptDeleteRange(range.startColumn - 1, 0, 0, lastLineIndex, range.endColumn - 1);\n        }\n        else {\n            this._tokens.acceptDeleteRange(0, firstLineIndex, range.startColumn - 1, lastLineIndex, range.endColumn - 1);\n        }\n    }\n    _acceptInsertText(position, eolCount, firstLineLength, lastLineLength, firstCharCode) {\n        if (eolCount === 0 && firstLineLength === 0) {\n            // Nothing to insert\n            return;\n        }\n        const lineIndex = position.lineNumber - this._startLineNumber;\n        if (lineIndex < 0) {\n            // this insertion occurs before this block, so we only need to adjust line numbers\n            this._startLineNumber += eolCount;\n            return;\n        }\n        const tokenMaxDeltaLine = this._tokens.getMaxDeltaLine();\n        if (lineIndex >= tokenMaxDeltaLine + 1) {\n            // this insertion occurs after this block, so there is nothing to do\n            return;\n        }\n        this._tokens.acceptInsertText(lineIndex, position.column - 1, eolCount, firstLineLength, lastLineLength, firstCharCode);\n    }\n}\nclass SparseMultilineTokensStorage {\n    constructor(tokens) {\n        this._tokens = tokens;\n        this._tokenCount = tokens.length / 4;\n    }\n    toString(startLineNumber) {\n        const pieces = [];\n        for (let i = 0; i < this._tokenCount; i++) {\n            pieces.push(`(${this._getDeltaLine(i) + startLineNumber},${this._getStartCharacter(i)}-${this._getEndCharacter(i)})`);\n        }\n        return `[${pieces.join(',')}]`;\n    }\n    getMaxDeltaLine() {\n        const tokenCount = this._getTokenCount();\n        if (tokenCount === 0) {\n            return -1;\n        }\n        return this._getDeltaLine(tokenCount - 1);\n    }\n    getRange() {\n        const tokenCount = this._getTokenCount();\n        if (tokenCount === 0) {\n            return null;\n        }\n        const startChar = this._getStartCharacter(0);\n        const maxDeltaLine = this._getDeltaLine(tokenCount - 1);\n        const endChar = this._getEndCharacter(tokenCount - 1);\n        return new Range(0, startChar + 1, maxDeltaLine, endChar + 1);\n    }\n    _getTokenCount() {\n        return this._tokenCount;\n    }\n    _getDeltaLine(tokenIndex) {\n        return this._tokens[4 * tokenIndex];\n    }\n    _getStartCharacter(tokenIndex) {\n        return this._tokens[4 * tokenIndex + 1];\n    }\n    _getEndCharacter(tokenIndex) {\n        return this._tokens[4 * tokenIndex + 2];\n    }\n    isEmpty() {\n        return (this._getTokenCount() === 0);\n    }\n    getLineTokens(deltaLine) {\n        let low = 0;\n        let high = this._getTokenCount() - 1;\n        while (low < high) {\n            const mid = low + Math.floor((high - low) / 2);\n            const midDeltaLine = this._getDeltaLine(mid);\n            if (midDeltaLine < deltaLine) {\n                low = mid + 1;\n            }\n            else if (midDeltaLine > deltaLine) {\n                high = mid - 1;\n            }\n            else {\n                let min = mid;\n                while (min > low && this._getDeltaLine(min - 1) === deltaLine) {\n                    min--;\n                }\n                let max = mid;\n                while (max < high && this._getDeltaLine(max + 1) === deltaLine) {\n                    max++;\n                }\n                return new SparseLineTokens(this._tokens.subarray(4 * min, 4 * max + 4));\n            }\n        }\n        if (this._getDeltaLine(low) === deltaLine) {\n            return new SparseLineTokens(this._tokens.subarray(4 * low, 4 * low + 4));\n        }\n        return null;\n    }\n    clear() {\n        this._tokenCount = 0;\n    }\n    removeTokens(startDeltaLine, startChar, endDeltaLine, endChar) {\n        const tokens = this._tokens;\n        const tokenCount = this._tokenCount;\n        let newTokenCount = 0;\n        let hasDeletedTokens = false;\n        let firstDeltaLine = 0;\n        for (let i = 0; i < tokenCount; i++) {\n            const srcOffset = 4 * i;\n            const tokenDeltaLine = tokens[srcOffset];\n            const tokenStartCharacter = tokens[srcOffset + 1];\n            const tokenEndCharacter = tokens[srcOffset + 2];\n            const tokenMetadata = tokens[srcOffset + 3];\n            if ((tokenDeltaLine > startDeltaLine || (tokenDeltaLine === startDeltaLine && tokenEndCharacter >= startChar))\n                && (tokenDeltaLine < endDeltaLine || (tokenDeltaLine === endDeltaLine && tokenStartCharacter <= endChar))) {\n                hasDeletedTokens = true;\n            }\n            else {\n                if (newTokenCount === 0) {\n                    firstDeltaLine = tokenDeltaLine;\n                }\n                if (hasDeletedTokens) {\n                    // must move the token to the left\n                    const destOffset = 4 * newTokenCount;\n                    tokens[destOffset] = tokenDeltaLine - firstDeltaLine;\n                    tokens[destOffset + 1] = tokenStartCharacter;\n                    tokens[destOffset + 2] = tokenEndCharacter;\n                    tokens[destOffset + 3] = tokenMetadata;\n                }\n                newTokenCount++;\n            }\n        }\n        this._tokenCount = newTokenCount;\n        return firstDeltaLine;\n    }\n    split(startDeltaLine, startChar, endDeltaLine, endChar) {\n        const tokens = this._tokens;\n        const tokenCount = this._tokenCount;\n        const aTokens = [];\n        const bTokens = [];\n        let destTokens = aTokens;\n        let destOffset = 0;\n        let destFirstDeltaLine = 0;\n        for (let i = 0; i < tokenCount; i++) {\n            const srcOffset = 4 * i;\n            const tokenDeltaLine = tokens[srcOffset];\n            const tokenStartCharacter = tokens[srcOffset + 1];\n            const tokenEndCharacter = tokens[srcOffset + 2];\n            const tokenMetadata = tokens[srcOffset + 3];\n            if ((tokenDeltaLine > startDeltaLine || (tokenDeltaLine === startDeltaLine && tokenEndCharacter >= startChar))) {\n                if ((tokenDeltaLine < endDeltaLine || (tokenDeltaLine === endDeltaLine && tokenStartCharacter <= endChar))) {\n                    // this token is touching the range\n                    continue;\n                }\n                else {\n                    // this token is after the range\n                    if (destTokens !== bTokens) {\n                        // this token is the first token after the range\n                        destTokens = bTokens;\n                        destOffset = 0;\n                        destFirstDeltaLine = tokenDeltaLine;\n                    }\n                }\n            }\n            destTokens[destOffset++] = tokenDeltaLine - destFirstDeltaLine;\n            destTokens[destOffset++] = tokenStartCharacter;\n            destTokens[destOffset++] = tokenEndCharacter;\n            destTokens[destOffset++] = tokenMetadata;\n        }\n        return [new SparseMultilineTokensStorage(new Uint32Array(aTokens)), new SparseMultilineTokensStorage(new Uint32Array(bTokens)), destFirstDeltaLine];\n    }\n    acceptDeleteRange(horizontalShiftForFirstLineTokens, startDeltaLine, startCharacter, endDeltaLine, endCharacter) {\n        // This is a bit complex, here are the cases I used to think about this:\n        //\n        // 1. The token starts before the deletion range\n        // 1a. The token is completely before the deletion range\n        //               -----------\n        //                          xxxxxxxxxxx\n        // 1b. The token starts before, the deletion range ends after the token\n        //               -----------\n        //                      xxxxxxxxxxx\n        // 1c. The token starts before, the deletion range ends precisely with the token\n        //               ---------------\n        //                      xxxxxxxx\n        // 1d. The token starts before, the deletion range is inside the token\n        //               ---------------\n        //                    xxxxx\n        //\n        // 2. The token starts at the same position with the deletion range\n        // 2a. The token starts at the same position, and ends inside the deletion range\n        //               -------\n        //               xxxxxxxxxxx\n        // 2b. The token starts at the same position, and ends at the same position as the deletion range\n        //               ----------\n        //               xxxxxxxxxx\n        // 2c. The token starts at the same position, and ends after the deletion range\n        //               -------------\n        //               xxxxxxx\n        //\n        // 3. The token starts inside the deletion range\n        // 3a. The token is inside the deletion range\n        //                -------\n        //             xxxxxxxxxxxxx\n        // 3b. The token starts inside the deletion range, and ends at the same position as the deletion range\n        //                ----------\n        //             xxxxxxxxxxxxx\n        // 3c. The token starts inside the deletion range, and ends after the deletion range\n        //                ------------\n        //             xxxxxxxxxxx\n        //\n        // 4. The token starts after the deletion range\n        //                  -----------\n        //          xxxxxxxx\n        //\n        const tokens = this._tokens;\n        const tokenCount = this._tokenCount;\n        const deletedLineCount = (endDeltaLine - startDeltaLine);\n        let newTokenCount = 0;\n        let hasDeletedTokens = false;\n        for (let i = 0; i < tokenCount; i++) {\n            const srcOffset = 4 * i;\n            let tokenDeltaLine = tokens[srcOffset];\n            let tokenStartCharacter = tokens[srcOffset + 1];\n            let tokenEndCharacter = tokens[srcOffset + 2];\n            const tokenMetadata = tokens[srcOffset + 3];\n            if (tokenDeltaLine < startDeltaLine || (tokenDeltaLine === startDeltaLine && tokenEndCharacter <= startCharacter)) {\n                // 1a. The token is completely before the deletion range\n                // => nothing to do\n                newTokenCount++;\n                continue;\n            }\n            else if (tokenDeltaLine === startDeltaLine && tokenStartCharacter < startCharacter) {\n                // 1b, 1c, 1d\n                // => the token survives, but it needs to shrink\n                if (tokenDeltaLine === endDeltaLine && tokenEndCharacter > endCharacter) {\n                    // 1d. The token starts before, the deletion range is inside the token\n                    // => the token shrinks by the deletion character count\n                    tokenEndCharacter -= (endCharacter - startCharacter);\n                }\n                else {\n                    // 1b. The token starts before, the deletion range ends after the token\n                    // 1c. The token starts before, the deletion range ends precisely with the token\n                    // => the token shrinks its ending to the deletion start\n                    tokenEndCharacter = startCharacter;\n                }\n            }\n            else if (tokenDeltaLine === startDeltaLine && tokenStartCharacter === startCharacter) {\n                // 2a, 2b, 2c\n                if (tokenDeltaLine === endDeltaLine && tokenEndCharacter > endCharacter) {\n                    // 2c. The token starts at the same position, and ends after the deletion range\n                    // => the token shrinks by the deletion character count\n                    tokenEndCharacter -= (endCharacter - startCharacter);\n                }\n                else {\n                    // 2a. The token starts at the same position, and ends inside the deletion range\n                    // 2b. The token starts at the same position, and ends at the same position as the deletion range\n                    // => the token is deleted\n                    hasDeletedTokens = true;\n                    continue;\n                }\n            }\n            else if (tokenDeltaLine < endDeltaLine || (tokenDeltaLine === endDeltaLine && tokenStartCharacter < endCharacter)) {\n                // 3a, 3b, 3c\n                if (tokenDeltaLine === endDeltaLine && tokenEndCharacter > endCharacter) {\n                    // 3c. The token starts inside the deletion range, and ends after the deletion range\n                    // => the token moves to continue right after the deletion\n                    tokenDeltaLine = startDeltaLine;\n                    tokenStartCharacter = startCharacter;\n                    tokenEndCharacter = tokenStartCharacter + (tokenEndCharacter - endCharacter);\n                }\n                else {\n                    // 3a. The token is inside the deletion range\n                    // 3b. The token starts inside the deletion range, and ends at the same position as the deletion range\n                    // => the token is deleted\n                    hasDeletedTokens = true;\n                    continue;\n                }\n            }\n            else if (tokenDeltaLine > endDeltaLine) {\n                // 4. (partial) The token starts after the deletion range, on a line below...\n                if (deletedLineCount === 0 && !hasDeletedTokens) {\n                    // early stop, there is no need to walk all the tokens and do nothing...\n                    newTokenCount = tokenCount;\n                    break;\n                }\n                tokenDeltaLine -= deletedLineCount;\n            }\n            else if (tokenDeltaLine === endDeltaLine && tokenStartCharacter >= endCharacter) {\n                // 4. (continued) The token starts after the deletion range, on the last line where a deletion occurs\n                if (horizontalShiftForFirstLineTokens && tokenDeltaLine === 0) {\n                    tokenStartCharacter += horizontalShiftForFirstLineTokens;\n                    tokenEndCharacter += horizontalShiftForFirstLineTokens;\n                }\n                tokenDeltaLine -= deletedLineCount;\n                tokenStartCharacter -= (endCharacter - startCharacter);\n                tokenEndCharacter -= (endCharacter - startCharacter);\n            }\n            else {\n                throw new Error(`Not possible!`);\n            }\n            const destOffset = 4 * newTokenCount;\n            tokens[destOffset] = tokenDeltaLine;\n            tokens[destOffset + 1] = tokenStartCharacter;\n            tokens[destOffset + 2] = tokenEndCharacter;\n            tokens[destOffset + 3] = tokenMetadata;\n            newTokenCount++;\n        }\n        this._tokenCount = newTokenCount;\n    }\n    acceptInsertText(deltaLine, character, eolCount, firstLineLength, lastLineLength, firstCharCode) {\n        // Here are the cases I used to think about this:\n        //\n        // 1. The token is completely before the insertion point\n        //            -----------   |\n        // 2. The token ends precisely at the insertion point\n        //            -----------|\n        // 3. The token contains the insertion point\n        //            -----|------\n        // 4. The token starts precisely at the insertion point\n        //            |-----------\n        // 5. The token is completely after the insertion point\n        //            |   -----------\n        //\n        const isInsertingPreciselyOneWordCharacter = (eolCount === 0\n            && firstLineLength === 1\n            && ((firstCharCode >= 48 /* CharCode.Digit0 */ && firstCharCode <= 57 /* CharCode.Digit9 */)\n                || (firstCharCode >= 65 /* CharCode.A */ && firstCharCode <= 90 /* CharCode.Z */)\n                || (firstCharCode >= 97 /* CharCode.a */ && firstCharCode <= 122 /* CharCode.z */)));\n        const tokens = this._tokens;\n        const tokenCount = this._tokenCount;\n        for (let i = 0; i < tokenCount; i++) {\n            const offset = 4 * i;\n            let tokenDeltaLine = tokens[offset];\n            let tokenStartCharacter = tokens[offset + 1];\n            let tokenEndCharacter = tokens[offset + 2];\n            if (tokenDeltaLine < deltaLine || (tokenDeltaLine === deltaLine && tokenEndCharacter < character)) {\n                // 1. The token is completely before the insertion point\n                // => nothing to do\n                continue;\n            }\n            else if (tokenDeltaLine === deltaLine && tokenEndCharacter === character) {\n                // 2. The token ends precisely at the insertion point\n                // => expand the end character only if inserting precisely one character that is a word character\n                if (isInsertingPreciselyOneWordCharacter) {\n                    tokenEndCharacter += 1;\n                }\n                else {\n                    continue;\n                }\n            }\n            else if (tokenDeltaLine === deltaLine && tokenStartCharacter < character && character < tokenEndCharacter) {\n                // 3. The token contains the insertion point\n                if (eolCount === 0) {\n                    // => just expand the end character\n                    tokenEndCharacter += firstLineLength;\n                }\n                else {\n                    // => cut off the token\n                    tokenEndCharacter = character;\n                }\n            }\n            else {\n                // 4. or 5.\n                if (tokenDeltaLine === deltaLine && tokenStartCharacter === character) {\n                    // 4. The token starts precisely at the insertion point\n                    // => grow the token (by keeping its start constant) only if inserting precisely one character that is a word character\n                    // => otherwise behave as in case 5.\n                    if (isInsertingPreciselyOneWordCharacter) {\n                        continue;\n                    }\n                }\n                // => the token must move and keep its size constant\n                if (tokenDeltaLine === deltaLine) {\n                    tokenDeltaLine += eolCount;\n                    // this token is on the line where the insertion is taking place\n                    if (eolCount === 0) {\n                        tokenStartCharacter += firstLineLength;\n                        tokenEndCharacter += firstLineLength;\n                    }\n                    else {\n                        const tokenLength = tokenEndCharacter - tokenStartCharacter;\n                        tokenStartCharacter = lastLineLength + (tokenStartCharacter - character);\n                        tokenEndCharacter = tokenStartCharacter + tokenLength;\n                    }\n                }\n                else {\n                    tokenDeltaLine += eolCount;\n                }\n            }\n            tokens[offset] = tokenDeltaLine;\n            tokens[offset + 1] = tokenStartCharacter;\n            tokens[offset + 2] = tokenEndCharacter;\n        }\n    }\n}\nexport class SparseLineTokens {\n    constructor(tokens) {\n        this._tokens = tokens;\n    }\n    getCount() {\n        return this._tokens.length / 4;\n    }\n    getStartCharacter(tokenIndex) {\n        return this._tokens[4 * tokenIndex + 1];\n    }\n    getEndCharacter(tokenIndex) {\n        return this._tokens[4 * tokenIndex + 2];\n    }\n    getMetadata(tokenIndex) {\n        return this._tokens[4 * tokenIndex + 3];\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,QAAQ,QAAQ,uBAAuB;AAChD;AACA;AACA;AACA,OAAO,MAAMC,qBAAqB,CAAC;EAC/B,OAAOC,MAAMA,CAACC,eAAe,EAAEC,MAAM,EAAE;IACnC,OAAO,IAAIH,qBAAqB,CAACE,eAAe,EAAE,IAAIE,4BAA4B,CAACD,MAAM,CAAC,CAAC;EAC/F;EACA;AACJ;AACA;EACI,IAAID,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACG,gBAAgB;EAChC;EACA;AACJ;AACA;EACI,IAAIC,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACC,cAAc;EAC9B;EACAC,WAAWA,CAACN,eAAe,EAAEC,MAAM,EAAE;IACjC,IAAI,CAACE,gBAAgB,GAAGH,eAAe;IACvC,IAAI,CAACO,OAAO,GAAGN,MAAM;IACrB,IAAI,CAACI,cAAc,GAAG,IAAI,CAACF,gBAAgB,GAAG,IAAI,CAACI,OAAO,CAACC,eAAe,CAAC,CAAC;EAChF;EACAC,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACF,OAAO,CAACE,QAAQ,CAAC,IAAI,CAACN,gBAAgB,CAAC;EACvD;EACAO,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACL,cAAc,GAAG,IAAI,CAACF,gBAAgB,GAAG,IAAI,CAACI,OAAO,CAACC,eAAe,CAAC,CAAC;EAChF;EACAG,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACJ,OAAO,CAACI,OAAO,CAAC,CAAC;EACjC;EACAC,aAAaA,CAACC,UAAU,EAAE;IACtB,IAAI,IAAI,CAACV,gBAAgB,IAAIU,UAAU,IAAIA,UAAU,IAAI,IAAI,CAACR,cAAc,EAAE;MAC1E,OAAO,IAAI,CAACE,OAAO,CAACK,aAAa,CAACC,UAAU,GAAG,IAAI,CAACV,gBAAgB,CAAC;IACzE;IACA,OAAO,IAAI;EACf;EACAW,QAAQA,CAAA,EAAG;IACP,MAAMC,UAAU,GAAG,IAAI,CAACR,OAAO,CAACO,QAAQ,CAAC,CAAC;IAC1C,IAAI,CAACC,UAAU,EAAE;MACb,OAAOA,UAAU;IACrB;IACA,OAAO,IAAInB,KAAK,CAAC,IAAI,CAACO,gBAAgB,GAAGY,UAAU,CAACf,eAAe,EAAEe,UAAU,CAACC,WAAW,EAAE,IAAI,CAACb,gBAAgB,GAAGY,UAAU,CAACX,aAAa,EAAEW,UAAU,CAACE,SAAS,CAAC;EACxK;EACAC,YAAYA,CAACC,KAAK,EAAE;IAChB,MAAMC,cAAc,GAAGD,KAAK,CAACnB,eAAe,GAAG,IAAI,CAACG,gBAAgB;IACpE,MAAMkB,YAAY,GAAGF,KAAK,CAACf,aAAa,GAAG,IAAI,CAACD,gBAAgB;IAChE,IAAI,CAACA,gBAAgB,IAAI,IAAI,CAACI,OAAO,CAACW,YAAY,CAACE,cAAc,EAAED,KAAK,CAACH,WAAW,GAAG,CAAC,EAAEK,YAAY,EAAEF,KAAK,CAACF,SAAS,GAAG,CAAC,CAAC;IAC5H,IAAI,CAACP,oBAAoB,CAAC,CAAC;EAC/B;EACAY,KAAKA,CAACH,KAAK,EAAE;IACT;IACA;IACA;IACA,MAAMC,cAAc,GAAGD,KAAK,CAACnB,eAAe,GAAG,IAAI,CAACG,gBAAgB;IACpE,MAAMkB,YAAY,GAAGF,KAAK,CAACf,aAAa,GAAG,IAAI,CAACD,gBAAgB;IAChE,MAAM,CAACoB,CAAC,EAAEC,CAAC,EAAEC,UAAU,CAAC,GAAG,IAAI,CAAClB,OAAO,CAACe,KAAK,CAACF,cAAc,EAAED,KAAK,CAACH,WAAW,GAAG,CAAC,EAAEK,YAAY,EAAEF,KAAK,CAACF,SAAS,GAAG,CAAC,CAAC;IACvH,OAAO,CAAC,IAAInB,qBAAqB,CAAC,IAAI,CAACK,gBAAgB,EAAEoB,CAAC,CAAC,EAAE,IAAIzB,qBAAqB,CAAC,IAAI,CAACK,gBAAgB,GAAGsB,UAAU,EAAED,CAAC,CAAC,CAAC;EAClI;EACAE,SAASA,CAACP,KAAK,EAAEQ,IAAI,EAAE;IACnB,MAAM,CAACC,QAAQ,EAAEC,eAAe,EAAEC,cAAc,CAAC,GAAGjC,QAAQ,CAAC8B,IAAI,CAAC;IAClE,IAAI,CAACI,UAAU,CAACZ,KAAK,EAAES,QAAQ,EAAEC,eAAe,EAAEC,cAAc,EAAEH,IAAI,CAACK,MAAM,GAAG,CAAC,GAAGL,IAAI,CAACM,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,mBAAmB,CAAC;EACnI;EACAF,UAAUA,CAACZ,KAAK,EAAES,QAAQ,EAAEC,eAAe,EAAEC,cAAc,EAAEI,aAAa,EAAE;IACxE,IAAI,CAACC,kBAAkB,CAAChB,KAAK,CAAC;IAC9B,IAAI,CAACiB,iBAAiB,CAAC,IAAIzC,QAAQ,CAACwB,KAAK,CAACnB,eAAe,EAAEmB,KAAK,CAACH,WAAW,CAAC,EAAEY,QAAQ,EAAEC,eAAe,EAAEC,cAAc,EAAEI,aAAa,CAAC;IACxI,IAAI,CAACxB,oBAAoB,CAAC,CAAC;EAC/B;EACAyB,kBAAkBA,CAAChB,KAAK,EAAE;IACtB,IAAIA,KAAK,CAACnB,eAAe,KAAKmB,KAAK,CAACf,aAAa,IAAIe,KAAK,CAACH,WAAW,KAAKG,KAAK,CAACF,SAAS,EAAE;MACxF;MACA;IACJ;IACA,MAAMoB,cAAc,GAAGlB,KAAK,CAACnB,eAAe,GAAG,IAAI,CAACG,gBAAgB;IACpE,MAAMmC,aAAa,GAAGnB,KAAK,CAACf,aAAa,GAAG,IAAI,CAACD,gBAAgB;IACjE,IAAImC,aAAa,GAAG,CAAC,EAAE;MACnB;MACA,MAAMC,iBAAiB,GAAGD,aAAa,GAAGD,cAAc;MACxD,IAAI,CAAClC,gBAAgB,IAAIoC,iBAAiB;MAC1C;IACJ;IACA,MAAMC,iBAAiB,GAAG,IAAI,CAACjC,OAAO,CAACC,eAAe,CAAC,CAAC;IACxD,IAAI6B,cAAc,IAAIG,iBAAiB,GAAG,CAAC,EAAE;MACzC;MACA;IACJ;IACA,IAAIH,cAAc,GAAG,CAAC,IAAIC,aAAa,IAAIE,iBAAiB,GAAG,CAAC,EAAE;MAC9D;MACA,IAAI,CAACrC,gBAAgB,GAAG,CAAC;MACzB,IAAI,CAACI,OAAO,CAACkC,KAAK,CAAC,CAAC;MACpB;IACJ;IACA,IAAIJ,cAAc,GAAG,CAAC,EAAE;MACpB,MAAMK,aAAa,GAAG,CAACL,cAAc;MACrC,IAAI,CAAClC,gBAAgB,IAAIuC,aAAa;MACtC,IAAI,CAACnC,OAAO,CAACoC,iBAAiB,CAACxB,KAAK,CAACH,WAAW,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAEsB,aAAa,EAAEnB,KAAK,CAACF,SAAS,GAAG,CAAC,CAAC;IACnG,CAAC,MACI;MACD,IAAI,CAACV,OAAO,CAACoC,iBAAiB,CAAC,CAAC,EAAEN,cAAc,EAAElB,KAAK,CAACH,WAAW,GAAG,CAAC,EAAEsB,aAAa,EAAEnB,KAAK,CAACF,SAAS,GAAG,CAAC,CAAC;IAChH;EACJ;EACAmB,iBAAiBA,CAACQ,QAAQ,EAAEhB,QAAQ,EAAEC,eAAe,EAAEC,cAAc,EAAEI,aAAa,EAAE;IAClF,IAAIN,QAAQ,KAAK,CAAC,IAAIC,eAAe,KAAK,CAAC,EAAE;MACzC;MACA;IACJ;IACA,MAAMgB,SAAS,GAAGD,QAAQ,CAAC/B,UAAU,GAAG,IAAI,CAACV,gBAAgB;IAC7D,IAAI0C,SAAS,GAAG,CAAC,EAAE;MACf;MACA,IAAI,CAAC1C,gBAAgB,IAAIyB,QAAQ;MACjC;IACJ;IACA,MAAMY,iBAAiB,GAAG,IAAI,CAACjC,OAAO,CAACC,eAAe,CAAC,CAAC;IACxD,IAAIqC,SAAS,IAAIL,iBAAiB,GAAG,CAAC,EAAE;MACpC;MACA;IACJ;IACA,IAAI,CAACjC,OAAO,CAACuC,gBAAgB,CAACD,SAAS,EAAED,QAAQ,CAACG,MAAM,GAAG,CAAC,EAAEnB,QAAQ,EAAEC,eAAe,EAAEC,cAAc,EAAEI,aAAa,CAAC;EAC3H;AACJ;AACA,MAAMhC,4BAA4B,CAAC;EAC/BI,WAAWA,CAACL,MAAM,EAAE;IAChB,IAAI,CAACM,OAAO,GAAGN,MAAM;IACrB,IAAI,CAAC+C,WAAW,GAAG/C,MAAM,CAAC+B,MAAM,GAAG,CAAC;EACxC;EACAvB,QAAQA,CAACT,eAAe,EAAE;IACtB,MAAMiD,MAAM,GAAG,EAAE;IACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACF,WAAW,EAAEE,CAAC,EAAE,EAAE;MACvCD,MAAM,CAACE,IAAI,CAAC,IAAI,IAAI,CAACC,aAAa,CAACF,CAAC,CAAC,GAAGlD,eAAe,IAAI,IAAI,CAACqD,kBAAkB,CAACH,CAAC,CAAC,IAAI,IAAI,CAACI,gBAAgB,CAACJ,CAAC,CAAC,GAAG,CAAC;IACzH;IACA,OAAO,IAAID,MAAM,CAACM,IAAI,CAAC,GAAG,CAAC,GAAG;EAClC;EACA/C,eAAeA,CAAA,EAAG;IACd,MAAMgD,UAAU,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACxC,IAAID,UAAU,KAAK,CAAC,EAAE;MAClB,OAAO,CAAC,CAAC;IACb;IACA,OAAO,IAAI,CAACJ,aAAa,CAACI,UAAU,GAAG,CAAC,CAAC;EAC7C;EACA1C,QAAQA,CAAA,EAAG;IACP,MAAM0C,UAAU,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACxC,IAAID,UAAU,KAAK,CAAC,EAAE;MAClB,OAAO,IAAI;IACf;IACA,MAAME,SAAS,GAAG,IAAI,CAACL,kBAAkB,CAAC,CAAC,CAAC;IAC5C,MAAMM,YAAY,GAAG,IAAI,CAACP,aAAa,CAACI,UAAU,GAAG,CAAC,CAAC;IACvD,MAAMI,OAAO,GAAG,IAAI,CAACN,gBAAgB,CAACE,UAAU,GAAG,CAAC,CAAC;IACrD,OAAO,IAAI5D,KAAK,CAAC,CAAC,EAAE8D,SAAS,GAAG,CAAC,EAAEC,YAAY,EAAEC,OAAO,GAAG,CAAC,CAAC;EACjE;EACAH,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAACT,WAAW;EAC3B;EACAI,aAAaA,CAACS,UAAU,EAAE;IACtB,OAAO,IAAI,CAACtD,OAAO,CAAC,CAAC,GAAGsD,UAAU,CAAC;EACvC;EACAR,kBAAkBA,CAACQ,UAAU,EAAE;IAC3B,OAAO,IAAI,CAACtD,OAAO,CAAC,CAAC,GAAGsD,UAAU,GAAG,CAAC,CAAC;EAC3C;EACAP,gBAAgBA,CAACO,UAAU,EAAE;IACzB,OAAO,IAAI,CAACtD,OAAO,CAAC,CAAC,GAAGsD,UAAU,GAAG,CAAC,CAAC;EAC3C;EACAlD,OAAOA,CAAA,EAAG;IACN,OAAQ,IAAI,CAAC8C,cAAc,CAAC,CAAC,KAAK,CAAC;EACvC;EACA7C,aAAaA,CAACkD,SAAS,EAAE;IACrB,IAAIC,GAAG,GAAG,CAAC;IACX,IAAIC,IAAI,GAAG,IAAI,CAACP,cAAc,CAAC,CAAC,GAAG,CAAC;IACpC,OAAOM,GAAG,GAAGC,IAAI,EAAE;MACf,MAAMC,GAAG,GAAGF,GAAG,GAAGG,IAAI,CAACC,KAAK,CAAC,CAACH,IAAI,GAAGD,GAAG,IAAI,CAAC,CAAC;MAC9C,MAAMK,YAAY,GAAG,IAAI,CAAChB,aAAa,CAACa,GAAG,CAAC;MAC5C,IAAIG,YAAY,GAAGN,SAAS,EAAE;QAC1BC,GAAG,GAAGE,GAAG,GAAG,CAAC;MACjB,CAAC,MACI,IAAIG,YAAY,GAAGN,SAAS,EAAE;QAC/BE,IAAI,GAAGC,GAAG,GAAG,CAAC;MAClB,CAAC,MACI;QACD,IAAII,GAAG,GAAGJ,GAAG;QACb,OAAOI,GAAG,GAAGN,GAAG,IAAI,IAAI,CAACX,aAAa,CAACiB,GAAG,GAAG,CAAC,CAAC,KAAKP,SAAS,EAAE;UAC3DO,GAAG,EAAE;QACT;QACA,IAAIC,GAAG,GAAGL,GAAG;QACb,OAAOK,GAAG,GAAGN,IAAI,IAAI,IAAI,CAACZ,aAAa,CAACkB,GAAG,GAAG,CAAC,CAAC,KAAKR,SAAS,EAAE;UAC5DQ,GAAG,EAAE;QACT;QACA,OAAO,IAAIC,gBAAgB,CAAC,IAAI,CAAChE,OAAO,CAACiE,QAAQ,CAAC,CAAC,GAAGH,GAAG,EAAE,CAAC,GAAGC,GAAG,GAAG,CAAC,CAAC,CAAC;MAC5E;IACJ;IACA,IAAI,IAAI,CAAClB,aAAa,CAACW,GAAG,CAAC,KAAKD,SAAS,EAAE;MACvC,OAAO,IAAIS,gBAAgB,CAAC,IAAI,CAAChE,OAAO,CAACiE,QAAQ,CAAC,CAAC,GAAGT,GAAG,EAAE,CAAC,GAAGA,GAAG,GAAG,CAAC,CAAC,CAAC;IAC5E;IACA,OAAO,IAAI;EACf;EACAtB,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACO,WAAW,GAAG,CAAC;EACxB;EACA9B,YAAYA,CAACuD,cAAc,EAAEf,SAAS,EAAEgB,YAAY,EAAEd,OAAO,EAAE;IAC3D,MAAM3D,MAAM,GAAG,IAAI,CAACM,OAAO;IAC3B,MAAMiD,UAAU,GAAG,IAAI,CAACR,WAAW;IACnC,IAAI2B,aAAa,GAAG,CAAC;IACrB,IAAIC,gBAAgB,GAAG,KAAK;IAC5B,IAAIC,cAAc,GAAG,CAAC;IACtB,KAAK,IAAI3B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGM,UAAU,EAAEN,CAAC,EAAE,EAAE;MACjC,MAAM4B,SAAS,GAAG,CAAC,GAAG5B,CAAC;MACvB,MAAM6B,cAAc,GAAG9E,MAAM,CAAC6E,SAAS,CAAC;MACxC,MAAME,mBAAmB,GAAG/E,MAAM,CAAC6E,SAAS,GAAG,CAAC,CAAC;MACjD,MAAMG,iBAAiB,GAAGhF,MAAM,CAAC6E,SAAS,GAAG,CAAC,CAAC;MAC/C,MAAMI,aAAa,GAAGjF,MAAM,CAAC6E,SAAS,GAAG,CAAC,CAAC;MAC3C,IAAI,CAACC,cAAc,GAAGN,cAAc,IAAKM,cAAc,KAAKN,cAAc,IAAIQ,iBAAiB,IAAIvB,SAAU,MACrGqB,cAAc,GAAGL,YAAY,IAAKK,cAAc,KAAKL,YAAY,IAAIM,mBAAmB,IAAIpB,OAAQ,CAAC,EAAE;QAC3GgB,gBAAgB,GAAG,IAAI;MAC3B,CAAC,MACI;QACD,IAAID,aAAa,KAAK,CAAC,EAAE;UACrBE,cAAc,GAAGE,cAAc;QACnC;QACA,IAAIH,gBAAgB,EAAE;UAClB;UACA,MAAMO,UAAU,GAAG,CAAC,GAAGR,aAAa;UACpC1E,MAAM,CAACkF,UAAU,CAAC,GAAGJ,cAAc,GAAGF,cAAc;UACpD5E,MAAM,CAACkF,UAAU,GAAG,CAAC,CAAC,GAAGH,mBAAmB;UAC5C/E,MAAM,CAACkF,UAAU,GAAG,CAAC,CAAC,GAAGF,iBAAiB;UAC1ChF,MAAM,CAACkF,UAAU,GAAG,CAAC,CAAC,GAAGD,aAAa;QAC1C;QACAP,aAAa,EAAE;MACnB;IACJ;IACA,IAAI,CAAC3B,WAAW,GAAG2B,aAAa;IAChC,OAAOE,cAAc;EACzB;EACAvD,KAAKA,CAACmD,cAAc,EAAEf,SAAS,EAAEgB,YAAY,EAAEd,OAAO,EAAE;IACpD,MAAM3D,MAAM,GAAG,IAAI,CAACM,OAAO;IAC3B,MAAMiD,UAAU,GAAG,IAAI,CAACR,WAAW;IACnC,MAAMoC,OAAO,GAAG,EAAE;IAClB,MAAMC,OAAO,GAAG,EAAE;IAClB,IAAIC,UAAU,GAAGF,OAAO;IACxB,IAAID,UAAU,GAAG,CAAC;IAClB,IAAII,kBAAkB,GAAG,CAAC;IAC1B,KAAK,IAAIrC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGM,UAAU,EAAEN,CAAC,EAAE,EAAE;MACjC,MAAM4B,SAAS,GAAG,CAAC,GAAG5B,CAAC;MACvB,MAAM6B,cAAc,GAAG9E,MAAM,CAAC6E,SAAS,CAAC;MACxC,MAAME,mBAAmB,GAAG/E,MAAM,CAAC6E,SAAS,GAAG,CAAC,CAAC;MACjD,MAAMG,iBAAiB,GAAGhF,MAAM,CAAC6E,SAAS,GAAG,CAAC,CAAC;MAC/C,MAAMI,aAAa,GAAGjF,MAAM,CAAC6E,SAAS,GAAG,CAAC,CAAC;MAC3C,IAAKC,cAAc,GAAGN,cAAc,IAAKM,cAAc,KAAKN,cAAc,IAAIQ,iBAAiB,IAAIvB,SAAU,EAAG;QAC5G,IAAKqB,cAAc,GAAGL,YAAY,IAAKK,cAAc,KAAKL,YAAY,IAAIM,mBAAmB,IAAIpB,OAAQ,EAAG;UACxG;UACA;QACJ,CAAC,MACI;UACD;UACA,IAAI0B,UAAU,KAAKD,OAAO,EAAE;YACxB;YACAC,UAAU,GAAGD,OAAO;YACpBF,UAAU,GAAG,CAAC;YACdI,kBAAkB,GAAGR,cAAc;UACvC;QACJ;MACJ;MACAO,UAAU,CAACH,UAAU,EAAE,CAAC,GAAGJ,cAAc,GAAGQ,kBAAkB;MAC9DD,UAAU,CAACH,UAAU,EAAE,CAAC,GAAGH,mBAAmB;MAC9CM,UAAU,CAACH,UAAU,EAAE,CAAC,GAAGF,iBAAiB;MAC5CK,UAAU,CAACH,UAAU,EAAE,CAAC,GAAGD,aAAa;IAC5C;IACA,OAAO,CAAC,IAAIhF,4BAA4B,CAAC,IAAIsF,WAAW,CAACJ,OAAO,CAAC,CAAC,EAAE,IAAIlF,4BAA4B,CAAC,IAAIsF,WAAW,CAACH,OAAO,CAAC,CAAC,EAAEE,kBAAkB,CAAC;EACvJ;EACA5C,iBAAiBA,CAAC8C,iCAAiC,EAAEhB,cAAc,EAAEiB,cAAc,EAAEhB,YAAY,EAAEiB,YAAY,EAAE;IAC7G;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAM1F,MAAM,GAAG,IAAI,CAACM,OAAO;IAC3B,MAAMiD,UAAU,GAAG,IAAI,CAACR,WAAW;IACnC,MAAM4C,gBAAgB,GAAIlB,YAAY,GAAGD,cAAe;IACxD,IAAIE,aAAa,GAAG,CAAC;IACrB,IAAIC,gBAAgB,GAAG,KAAK;IAC5B,KAAK,IAAI1B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGM,UAAU,EAAEN,CAAC,EAAE,EAAE;MACjC,MAAM4B,SAAS,GAAG,CAAC,GAAG5B,CAAC;MACvB,IAAI6B,cAAc,GAAG9E,MAAM,CAAC6E,SAAS,CAAC;MACtC,IAAIE,mBAAmB,GAAG/E,MAAM,CAAC6E,SAAS,GAAG,CAAC,CAAC;MAC/C,IAAIG,iBAAiB,GAAGhF,MAAM,CAAC6E,SAAS,GAAG,CAAC,CAAC;MAC7C,MAAMI,aAAa,GAAGjF,MAAM,CAAC6E,SAAS,GAAG,CAAC,CAAC;MAC3C,IAAIC,cAAc,GAAGN,cAAc,IAAKM,cAAc,KAAKN,cAAc,IAAIQ,iBAAiB,IAAIS,cAAe,EAAE;QAC/G;QACA;QACAf,aAAa,EAAE;QACf;MACJ,CAAC,MACI,IAAII,cAAc,KAAKN,cAAc,IAAIO,mBAAmB,GAAGU,cAAc,EAAE;QAChF;QACA;QACA,IAAIX,cAAc,KAAKL,YAAY,IAAIO,iBAAiB,GAAGU,YAAY,EAAE;UACrE;UACA;UACAV,iBAAiB,IAAKU,YAAY,GAAGD,cAAe;QACxD,CAAC,MACI;UACD;UACA;UACA;UACAT,iBAAiB,GAAGS,cAAc;QACtC;MACJ,CAAC,MACI,IAAIX,cAAc,KAAKN,cAAc,IAAIO,mBAAmB,KAAKU,cAAc,EAAE;QAClF;QACA,IAAIX,cAAc,KAAKL,YAAY,IAAIO,iBAAiB,GAAGU,YAAY,EAAE;UACrE;UACA;UACAV,iBAAiB,IAAKU,YAAY,GAAGD,cAAe;QACxD,CAAC,MACI;UACD;UACA;UACA;UACAd,gBAAgB,GAAG,IAAI;UACvB;QACJ;MACJ,CAAC,MACI,IAAIG,cAAc,GAAGL,YAAY,IAAKK,cAAc,KAAKL,YAAY,IAAIM,mBAAmB,GAAGW,YAAa,EAAE;QAC/G;QACA,IAAIZ,cAAc,KAAKL,YAAY,IAAIO,iBAAiB,GAAGU,YAAY,EAAE;UACrE;UACA;UACAZ,cAAc,GAAGN,cAAc;UAC/BO,mBAAmB,GAAGU,cAAc;UACpCT,iBAAiB,GAAGD,mBAAmB,IAAIC,iBAAiB,GAAGU,YAAY,CAAC;QAChF,CAAC,MACI;UACD;UACA;UACA;UACAf,gBAAgB,GAAG,IAAI;UACvB;QACJ;MACJ,CAAC,MACI,IAAIG,cAAc,GAAGL,YAAY,EAAE;QACpC;QACA,IAAIkB,gBAAgB,KAAK,CAAC,IAAI,CAAChB,gBAAgB,EAAE;UAC7C;UACAD,aAAa,GAAGnB,UAAU;UAC1B;QACJ;QACAuB,cAAc,IAAIa,gBAAgB;MACtC,CAAC,MACI,IAAIb,cAAc,KAAKL,YAAY,IAAIM,mBAAmB,IAAIW,YAAY,EAAE;QAC7E;QACA,IAAIF,iCAAiC,IAAIV,cAAc,KAAK,CAAC,EAAE;UAC3DC,mBAAmB,IAAIS,iCAAiC;UACxDR,iBAAiB,IAAIQ,iCAAiC;QAC1D;QACAV,cAAc,IAAIa,gBAAgB;QAClCZ,mBAAmB,IAAKW,YAAY,GAAGD,cAAe;QACtDT,iBAAiB,IAAKU,YAAY,GAAGD,cAAe;MACxD,CAAC,MACI;QACD,MAAM,IAAIG,KAAK,CAAC,eAAe,CAAC;MACpC;MACA,MAAMV,UAAU,GAAG,CAAC,GAAGR,aAAa;MACpC1E,MAAM,CAACkF,UAAU,CAAC,GAAGJ,cAAc;MACnC9E,MAAM,CAACkF,UAAU,GAAG,CAAC,CAAC,GAAGH,mBAAmB;MAC5C/E,MAAM,CAACkF,UAAU,GAAG,CAAC,CAAC,GAAGF,iBAAiB;MAC1ChF,MAAM,CAACkF,UAAU,GAAG,CAAC,CAAC,GAAGD,aAAa;MACtCP,aAAa,EAAE;IACnB;IACA,IAAI,CAAC3B,WAAW,GAAG2B,aAAa;EACpC;EACA7B,gBAAgBA,CAACgB,SAAS,EAAEgC,SAAS,EAAElE,QAAQ,EAAEC,eAAe,EAAEC,cAAc,EAAEI,aAAa,EAAE;IAC7F;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAM6D,oCAAoC,GAAInE,QAAQ,KAAK,CAAC,IACrDC,eAAe,KAAK,CAAC,KACnBK,aAAa,IAAI,EAAE,CAAC,yBAAyBA,aAAa,IAAI,EAAE,CAAC,yBAC9DA,aAAa,IAAI,EAAE,CAAC,oBAAoBA,aAAa,IAAI,EAAE,CAAC,gBAAiB,IAC7EA,aAAa,IAAI,EAAE,CAAC,oBAAoBA,aAAa,IAAI,GAAG,CAAC,gBAAiB,CAAE;IAC5F,MAAMjC,MAAM,GAAG,IAAI,CAACM,OAAO;IAC3B,MAAMiD,UAAU,GAAG,IAAI,CAACR,WAAW;IACnC,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGM,UAAU,EAAEN,CAAC,EAAE,EAAE;MACjC,MAAM8C,MAAM,GAAG,CAAC,GAAG9C,CAAC;MACpB,IAAI6B,cAAc,GAAG9E,MAAM,CAAC+F,MAAM,CAAC;MACnC,IAAIhB,mBAAmB,GAAG/E,MAAM,CAAC+F,MAAM,GAAG,CAAC,CAAC;MAC5C,IAAIf,iBAAiB,GAAGhF,MAAM,CAAC+F,MAAM,GAAG,CAAC,CAAC;MAC1C,IAAIjB,cAAc,GAAGjB,SAAS,IAAKiB,cAAc,KAAKjB,SAAS,IAAImB,iBAAiB,GAAGa,SAAU,EAAE;QAC/F;QACA;QACA;MACJ,CAAC,MACI,IAAIf,cAAc,KAAKjB,SAAS,IAAImB,iBAAiB,KAAKa,SAAS,EAAE;QACtE;QACA;QACA,IAAIC,oCAAoC,EAAE;UACtCd,iBAAiB,IAAI,CAAC;QAC1B,CAAC,MACI;UACD;QACJ;MACJ,CAAC,MACI,IAAIF,cAAc,KAAKjB,SAAS,IAAIkB,mBAAmB,GAAGc,SAAS,IAAIA,SAAS,GAAGb,iBAAiB,EAAE;QACvG;QACA,IAAIrD,QAAQ,KAAK,CAAC,EAAE;UAChB;UACAqD,iBAAiB,IAAIpD,eAAe;QACxC,CAAC,MACI;UACD;UACAoD,iBAAiB,GAAGa,SAAS;QACjC;MACJ,CAAC,MACI;QACD;QACA,IAAIf,cAAc,KAAKjB,SAAS,IAAIkB,mBAAmB,KAAKc,SAAS,EAAE;UACnE;UACA;UACA;UACA,IAAIC,oCAAoC,EAAE;YACtC;UACJ;QACJ;QACA;QACA,IAAIhB,cAAc,KAAKjB,SAAS,EAAE;UAC9BiB,cAAc,IAAInD,QAAQ;UAC1B;UACA,IAAIA,QAAQ,KAAK,CAAC,EAAE;YAChBoD,mBAAmB,IAAInD,eAAe;YACtCoD,iBAAiB,IAAIpD,eAAe;UACxC,CAAC,MACI;YACD,MAAMoE,WAAW,GAAGhB,iBAAiB,GAAGD,mBAAmB;YAC3DA,mBAAmB,GAAGlD,cAAc,IAAIkD,mBAAmB,GAAGc,SAAS,CAAC;YACxEb,iBAAiB,GAAGD,mBAAmB,GAAGiB,WAAW;UACzD;QACJ,CAAC,MACI;UACDlB,cAAc,IAAInD,QAAQ;QAC9B;MACJ;MACA3B,MAAM,CAAC+F,MAAM,CAAC,GAAGjB,cAAc;MAC/B9E,MAAM,CAAC+F,MAAM,GAAG,CAAC,CAAC,GAAGhB,mBAAmB;MACxC/E,MAAM,CAAC+F,MAAM,GAAG,CAAC,CAAC,GAAGf,iBAAiB;IAC1C;EACJ;AACJ;AACA,OAAO,MAAMV,gBAAgB,CAAC;EAC1BjE,WAAWA,CAACL,MAAM,EAAE;IAChB,IAAI,CAACM,OAAO,GAAGN,MAAM;EACzB;EACAiG,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAAC3F,OAAO,CAACyB,MAAM,GAAG,CAAC;EAClC;EACAmE,iBAAiBA,CAACtC,UAAU,EAAE;IAC1B,OAAO,IAAI,CAACtD,OAAO,CAAC,CAAC,GAAGsD,UAAU,GAAG,CAAC,CAAC;EAC3C;EACAuC,eAAeA,CAACvC,UAAU,EAAE;IACxB,OAAO,IAAI,CAACtD,OAAO,CAAC,CAAC,GAAGsD,UAAU,GAAG,CAAC,CAAC;EAC3C;EACAwC,WAAWA,CAACxC,UAAU,EAAE;IACpB,OAAO,IAAI,CAACtD,OAAO,CAAC,CAAC,GAAGsD,UAAU,GAAG,CAAC,CAAC;EAC3C;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}