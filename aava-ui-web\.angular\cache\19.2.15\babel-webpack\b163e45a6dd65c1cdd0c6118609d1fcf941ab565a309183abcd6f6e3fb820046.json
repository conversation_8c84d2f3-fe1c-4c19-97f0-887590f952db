{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { env } from './process.js';\nexport function isHotReloadEnabled() {\n  return env && !!env['VSCODE_DEV'];\n}\nexport function registerHotReloadHandler(handler) {\n  if (!isHotReloadEnabled()) {\n    return {\n      dispose() {}\n    };\n  } else {\n    const handlers = registerGlobalHotReloadHandler();\n    handlers.add(handler);\n    return {\n      dispose() {\n        handlers.delete(handler);\n      }\n    };\n  }\n}\nfunction registerGlobalHotReloadHandler() {\n  if (!hotReloadHandlers) {\n    hotReloadHandlers = new Set();\n  }\n  const g = globalThis;\n  if (!g.$hotReload_applyNewExports) {\n    g.$hotReload_applyNewExports = args => {\n      const args2 = {\n        config: {\n          mode: undefined\n        },\n        ...args\n      };\n      const results = [];\n      for (const h of hotReloadHandlers) {\n        const result = h(args2);\n        if (result) {\n          results.push(result);\n        }\n      }\n      if (results.length > 0) {\n        return newExports => {\n          let result = false;\n          for (const r of results) {\n            if (r(newExports)) {\n              result = true;\n            }\n          }\n          return result;\n        };\n      }\n      return undefined;\n    };\n  }\n  return hotReloadHandlers;\n}\nlet hotReloadHandlers = undefined;\nif (isHotReloadEnabled()) {\n  // This code does not run in production.\n  registerHotReloadHandler(({\n    oldExports,\n    newSrc,\n    config\n  }) => {\n    if (config.mode !== 'patch-prototype') {\n      return undefined;\n    }\n    return newExports => {\n      for (const key in newExports) {\n        const exportedItem = newExports[key];\n        console.log(`[hot-reload] Patching prototype methods of '${key}'`, {\n          exportedItem\n        });\n        if (typeof exportedItem === 'function' && exportedItem.prototype) {\n          const oldExportedItem = oldExports[key];\n          if (oldExportedItem) {\n            for (const prop of Object.getOwnPropertyNames(exportedItem.prototype)) {\n              const descriptor = Object.getOwnPropertyDescriptor(exportedItem.prototype, prop);\n              const oldDescriptor = Object.getOwnPropertyDescriptor(oldExportedItem.prototype, prop);\n              if (descriptor?.value?.toString() !== oldDescriptor?.value?.toString()) {\n                console.log(`[hot-reload] Patching prototype method '${key}.${prop}'`);\n              }\n              Object.defineProperty(oldExportedItem.prototype, prop, descriptor);\n            }\n            newExports[key] = oldExportedItem;\n          }\n        }\n      }\n      return true;\n    };\n  });\n}", "map": {"version": 3, "names": ["env", "isHotReloadEnabled", "registerHotReloadHandler", "handler", "dispose", "handlers", "registerGlobalHotReloadHandler", "add", "delete", "hotReloadHandlers", "Set", "g", "globalThis", "$hotReload_applyNewExports", "args", "args2", "config", "mode", "undefined", "results", "h", "result", "push", "length", "newExports", "r", "oldExports", "newSrc", "key", "exportedItem", "console", "log", "prototype", "oldExportedItem", "prop", "Object", "getOwnPropertyNames", "descriptor", "getOwnPropertyDescriptor", "oldDescriptor", "value", "toString", "defineProperty"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/common/hotReload.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { env } from './process.js';\nexport function isHotReloadEnabled() {\n    return env && !!env['VSCODE_DEV'];\n}\nexport function registerHotReloadHandler(handler) {\n    if (!isHotReloadEnabled()) {\n        return { dispose() { } };\n    }\n    else {\n        const handlers = registerGlobalHotReloadHandler();\n        handlers.add(handler);\n        return {\n            dispose() { handlers.delete(handler); }\n        };\n    }\n}\nfunction registerGlobalHotReloadHandler() {\n    if (!hotReloadHandlers) {\n        hotReloadHandlers = new Set();\n    }\n    const g = globalThis;\n    if (!g.$hotReload_applyNewExports) {\n        g.$hotReload_applyNewExports = args => {\n            const args2 = { config: { mode: undefined }, ...args };\n            const results = [];\n            for (const h of hotReloadHandlers) {\n                const result = h(args2);\n                if (result) {\n                    results.push(result);\n                }\n            }\n            if (results.length > 0) {\n                return newExports => {\n                    let result = false;\n                    for (const r of results) {\n                        if (r(newExports)) {\n                            result = true;\n                        }\n                    }\n                    return result;\n                };\n            }\n            return undefined;\n        };\n    }\n    return hotReloadHandlers;\n}\nlet hotReloadHandlers = undefined;\nif (isHotReloadEnabled()) {\n    // This code does not run in production.\n    registerHotReloadHandler(({ oldExports, newSrc, config }) => {\n        if (config.mode !== 'patch-prototype') {\n            return undefined;\n        }\n        return newExports => {\n            for (const key in newExports) {\n                const exportedItem = newExports[key];\n                console.log(`[hot-reload] Patching prototype methods of '${key}'`, { exportedItem });\n                if (typeof exportedItem === 'function' && exportedItem.prototype) {\n                    const oldExportedItem = oldExports[key];\n                    if (oldExportedItem) {\n                        for (const prop of Object.getOwnPropertyNames(exportedItem.prototype)) {\n                            const descriptor = Object.getOwnPropertyDescriptor(exportedItem.prototype, prop);\n                            const oldDescriptor = Object.getOwnPropertyDescriptor(oldExportedItem.prototype, prop);\n                            if (descriptor?.value?.toString() !== oldDescriptor?.value?.toString()) {\n                                console.log(`[hot-reload] Patching prototype method '${key}.${prop}'`);\n                            }\n                            Object.defineProperty(oldExportedItem.prototype, prop, descriptor);\n                        }\n                        newExports[key] = oldExportedItem;\n                    }\n                }\n            }\n            return true;\n        };\n    });\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,GAAG,QAAQ,cAAc;AAClC,OAAO,SAASC,kBAAkBA,CAAA,EAAG;EACjC,OAAOD,GAAG,IAAI,CAAC,CAACA,GAAG,CAAC,YAAY,CAAC;AACrC;AACA,OAAO,SAASE,wBAAwBA,CAACC,OAAO,EAAE;EAC9C,IAAI,CAACF,kBAAkB,CAAC,CAAC,EAAE;IACvB,OAAO;MAAEG,OAAOA,CAAA,EAAG,CAAE;IAAE,CAAC;EAC5B,CAAC,MACI;IACD,MAAMC,QAAQ,GAAGC,8BAA8B,CAAC,CAAC;IACjDD,QAAQ,CAACE,GAAG,CAACJ,OAAO,CAAC;IACrB,OAAO;MACHC,OAAOA,CAAA,EAAG;QAAEC,QAAQ,CAACG,MAAM,CAACL,OAAO,CAAC;MAAE;IAC1C,CAAC;EACL;AACJ;AACA,SAASG,8BAA8BA,CAAA,EAAG;EACtC,IAAI,CAACG,iBAAiB,EAAE;IACpBA,iBAAiB,GAAG,IAAIC,GAAG,CAAC,CAAC;EACjC;EACA,MAAMC,CAAC,GAAGC,UAAU;EACpB,IAAI,CAACD,CAAC,CAACE,0BAA0B,EAAE;IAC/BF,CAAC,CAACE,0BAA0B,GAAGC,IAAI,IAAI;MACnC,MAAMC,KAAK,GAAG;QAAEC,MAAM,EAAE;UAAEC,IAAI,EAAEC;QAAU,CAAC;QAAE,GAAGJ;MAAK,CAAC;MACtD,MAAMK,OAAO,GAAG,EAAE;MAClB,KAAK,MAAMC,CAAC,IAAIX,iBAAiB,EAAE;QAC/B,MAAMY,MAAM,GAAGD,CAAC,CAACL,KAAK,CAAC;QACvB,IAAIM,MAAM,EAAE;UACRF,OAAO,CAACG,IAAI,CAACD,MAAM,CAAC;QACxB;MACJ;MACA,IAAIF,OAAO,CAACI,MAAM,GAAG,CAAC,EAAE;QACpB,OAAOC,UAAU,IAAI;UACjB,IAAIH,MAAM,GAAG,KAAK;UAClB,KAAK,MAAMI,CAAC,IAAIN,OAAO,EAAE;YACrB,IAAIM,CAAC,CAACD,UAAU,CAAC,EAAE;cACfH,MAAM,GAAG,IAAI;YACjB;UACJ;UACA,OAAOA,MAAM;QACjB,CAAC;MACL;MACA,OAAOH,SAAS;IACpB,CAAC;EACL;EACA,OAAOT,iBAAiB;AAC5B;AACA,IAAIA,iBAAiB,GAAGS,SAAS;AACjC,IAAIjB,kBAAkB,CAAC,CAAC,EAAE;EACtB;EACAC,wBAAwB,CAAC,CAAC;IAAEwB,UAAU;IAAEC,MAAM;IAAEX;EAAO,CAAC,KAAK;IACzD,IAAIA,MAAM,CAACC,IAAI,KAAK,iBAAiB,EAAE;MACnC,OAAOC,SAAS;IACpB;IACA,OAAOM,UAAU,IAAI;MACjB,KAAK,MAAMI,GAAG,IAAIJ,UAAU,EAAE;QAC1B,MAAMK,YAAY,GAAGL,UAAU,CAACI,GAAG,CAAC;QACpCE,OAAO,CAACC,GAAG,CAAC,+CAA+CH,GAAG,GAAG,EAAE;UAAEC;QAAa,CAAC,CAAC;QACpF,IAAI,OAAOA,YAAY,KAAK,UAAU,IAAIA,YAAY,CAACG,SAAS,EAAE;UAC9D,MAAMC,eAAe,GAAGP,UAAU,CAACE,GAAG,CAAC;UACvC,IAAIK,eAAe,EAAE;YACjB,KAAK,MAAMC,IAAI,IAAIC,MAAM,CAACC,mBAAmB,CAACP,YAAY,CAACG,SAAS,CAAC,EAAE;cACnE,MAAMK,UAAU,GAAGF,MAAM,CAACG,wBAAwB,CAACT,YAAY,CAACG,SAAS,EAAEE,IAAI,CAAC;cAChF,MAAMK,aAAa,GAAGJ,MAAM,CAACG,wBAAwB,CAACL,eAAe,CAACD,SAAS,EAAEE,IAAI,CAAC;cACtF,IAAIG,UAAU,EAAEG,KAAK,EAAEC,QAAQ,CAAC,CAAC,KAAKF,aAAa,EAAEC,KAAK,EAAEC,QAAQ,CAAC,CAAC,EAAE;gBACpEX,OAAO,CAACC,GAAG,CAAC,2CAA2CH,GAAG,IAAIM,IAAI,GAAG,CAAC;cAC1E;cACAC,MAAM,CAACO,cAAc,CAACT,eAAe,CAACD,SAAS,EAAEE,IAAI,EAAEG,UAAU,CAAC;YACtE;YACAb,UAAU,CAACI,GAAG,CAAC,GAAGK,eAAe;UACrC;QACJ;MACJ;MACA,OAAO,IAAI;IACf,CAAC;EACL,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}