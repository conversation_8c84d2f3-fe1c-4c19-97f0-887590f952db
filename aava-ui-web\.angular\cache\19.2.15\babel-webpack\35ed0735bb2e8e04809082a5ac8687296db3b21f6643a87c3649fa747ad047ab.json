{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { coalesce } from '../../../../base/common/arrays.js';\nimport { AsyncIterableObject } from '../../../../base/common/async.js';\nexport class ContentHoverComputer {\n  get anchor() {\n    return this._anchor;\n  }\n  set anchor(value) {\n    this._anchor = value;\n  }\n  get shouldFocus() {\n    return this._shouldFocus;\n  }\n  set shouldFocus(value) {\n    this._shouldFocus = value;\n  }\n  get source() {\n    return this._source;\n  }\n  set source(value) {\n    this._source = value;\n  }\n  get insistOnKeepingHoverVisible() {\n    return this._insistOnKeepingHoverVisible;\n  }\n  set insistOnKeepingHoverVisible(value) {\n    this._insistOnKeepingHoverVisible = value;\n  }\n  constructor(_editor, _participants) {\n    this._editor = _editor;\n    this._participants = _participants;\n    this._anchor = null;\n    this._shouldFocus = false;\n    this._source = 0 /* HoverStartSource.Mouse */;\n    this._insistOnKeepingHoverVisible = false;\n  }\n  static _getLineDecorations(editor, anchor) {\n    if (anchor.type !== 1 /* HoverAnchorType.Range */ && !anchor.supportsMarkerHover) {\n      return [];\n    }\n    const model = editor.getModel();\n    const lineNumber = anchor.range.startLineNumber;\n    if (lineNumber > model.getLineCount()) {\n      // invalid line\n      return [];\n    }\n    const maxColumn = model.getLineMaxColumn(lineNumber);\n    return editor.getLineDecorations(lineNumber).filter(d => {\n      if (d.options.isWholeLine) {\n        return true;\n      }\n      const startColumn = d.range.startLineNumber === lineNumber ? d.range.startColumn : 1;\n      const endColumn = d.range.endLineNumber === lineNumber ? d.range.endColumn : maxColumn;\n      if (d.options.showIfCollapsed) {\n        // Relax check around `showIfCollapsed` decorations to also include +/- 1 character\n        if (startColumn > anchor.range.startColumn + 1 || anchor.range.endColumn - 1 > endColumn) {\n          return false;\n        }\n      } else {\n        if (startColumn > anchor.range.startColumn || anchor.range.endColumn > endColumn) {\n          return false;\n        }\n      }\n      return true;\n    });\n  }\n  computeAsync(token) {\n    const anchor = this._anchor;\n    if (!this._editor.hasModel() || !anchor) {\n      return AsyncIterableObject.EMPTY;\n    }\n    const lineDecorations = ContentHoverComputer._getLineDecorations(this._editor, anchor);\n    return AsyncIterableObject.merge(this._participants.map(participant => {\n      if (!participant.computeAsync) {\n        return AsyncIterableObject.EMPTY;\n      }\n      return participant.computeAsync(anchor, lineDecorations, token);\n    }));\n  }\n  computeSync() {\n    if (!this._editor.hasModel() || !this._anchor) {\n      return [];\n    }\n    const lineDecorations = ContentHoverComputer._getLineDecorations(this._editor, this._anchor);\n    let result = [];\n    for (const participant of this._participants) {\n      result = result.concat(participant.computeSync(this._anchor, lineDecorations));\n    }\n    return coalesce(result);\n  }\n}", "map": {"version": 3, "names": ["coalesce", "AsyncIterableObject", "ContentHoverComputer", "anchor", "_anchor", "value", "shouldFocus", "_shouldFocus", "source", "_source", "insistOnKeepingHoverVisible", "_insistOnKeepingHoverVisible", "constructor", "_editor", "_participants", "_getLineDecorations", "editor", "type", "supportsMarkerHover", "model", "getModel", "lineNumber", "range", "startLineNumber", "getLineCount", "maxColumn", "getLineMaxColumn", "getLineDecorations", "filter", "d", "options", "isWholeLine", "startColumn", "endColumn", "endLineNumber", "showIfCollapsed", "computeAsync", "token", "hasModel", "EMPTY", "lineDecorations", "merge", "map", "participant", "computeSync", "result", "concat"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/contrib/hover/browser/contentHoverComputer.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { coalesce } from '../../../../base/common/arrays.js';\nimport { AsyncIterableObject } from '../../../../base/common/async.js';\nexport class ContentHoverComputer {\n    get anchor() { return this._anchor; }\n    set anchor(value) { this._anchor = value; }\n    get shouldFocus() { return this._shouldFocus; }\n    set shouldFocus(value) { this._shouldFocus = value; }\n    get source() { return this._source; }\n    set source(value) { this._source = value; }\n    get insistOnKeepingHoverVisible() { return this._insistOnKeepingHoverVisible; }\n    set insistOnKeepingHoverVisible(value) { this._insistOnKeepingHoverVisible = value; }\n    constructor(_editor, _participants) {\n        this._editor = _editor;\n        this._participants = _participants;\n        this._anchor = null;\n        this._shouldFocus = false;\n        this._source = 0 /* HoverStartSource.Mouse */;\n        this._insistOnKeepingHoverVisible = false;\n    }\n    static _getLineDecorations(editor, anchor) {\n        if (anchor.type !== 1 /* HoverAnchorType.Range */ && !anchor.supportsMarkerHover) {\n            return [];\n        }\n        const model = editor.getModel();\n        const lineNumber = anchor.range.startLineNumber;\n        if (lineNumber > model.getLineCount()) {\n            // invalid line\n            return [];\n        }\n        const maxColumn = model.getLineMaxColumn(lineNumber);\n        return editor.getLineDecorations(lineNumber).filter((d) => {\n            if (d.options.isWholeLine) {\n                return true;\n            }\n            const startColumn = (d.range.startLineNumber === lineNumber) ? d.range.startColumn : 1;\n            const endColumn = (d.range.endLineNumber === lineNumber) ? d.range.endColumn : maxColumn;\n            if (d.options.showIfCollapsed) {\n                // Relax check around `showIfCollapsed` decorations to also include +/- 1 character\n                if (startColumn > anchor.range.startColumn + 1 || anchor.range.endColumn - 1 > endColumn) {\n                    return false;\n                }\n            }\n            else {\n                if (startColumn > anchor.range.startColumn || anchor.range.endColumn > endColumn) {\n                    return false;\n                }\n            }\n            return true;\n        });\n    }\n    computeAsync(token) {\n        const anchor = this._anchor;\n        if (!this._editor.hasModel() || !anchor) {\n            return AsyncIterableObject.EMPTY;\n        }\n        const lineDecorations = ContentHoverComputer._getLineDecorations(this._editor, anchor);\n        return AsyncIterableObject.merge(this._participants.map((participant) => {\n            if (!participant.computeAsync) {\n                return AsyncIterableObject.EMPTY;\n            }\n            return participant.computeAsync(anchor, lineDecorations, token);\n        }));\n    }\n    computeSync() {\n        if (!this._editor.hasModel() || !this._anchor) {\n            return [];\n        }\n        const lineDecorations = ContentHoverComputer._getLineDecorations(this._editor, this._anchor);\n        let result = [];\n        for (const participant of this._participants) {\n            result = result.concat(participant.computeSync(this._anchor, lineDecorations));\n        }\n        return coalesce(result);\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,QAAQ,QAAQ,mCAAmC;AAC5D,SAASC,mBAAmB,QAAQ,kCAAkC;AACtE,OAAO,MAAMC,oBAAoB,CAAC;EAC9B,IAAIC,MAAMA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACC,OAAO;EAAE;EACpC,IAAID,MAAMA,CAACE,KAAK,EAAE;IAAE,IAAI,CAACD,OAAO,GAAGC,KAAK;EAAE;EAC1C,IAAIC,WAAWA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACC,YAAY;EAAE;EAC9C,IAAID,WAAWA,CAACD,KAAK,EAAE;IAAE,IAAI,CAACE,YAAY,GAAGF,KAAK;EAAE;EACpD,IAAIG,MAAMA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACC,OAAO;EAAE;EACpC,IAAID,MAAMA,CAACH,KAAK,EAAE;IAAE,IAAI,CAACI,OAAO,GAAGJ,KAAK;EAAE;EAC1C,IAAIK,2BAA2BA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACC,4BAA4B;EAAE;EAC9E,IAAID,2BAA2BA,CAACL,KAAK,EAAE;IAAE,IAAI,CAACM,4BAA4B,GAAGN,KAAK;EAAE;EACpFO,WAAWA,CAACC,OAAO,EAAEC,aAAa,EAAE;IAChC,IAAI,CAACD,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACV,OAAO,GAAG,IAAI;IACnB,IAAI,CAACG,YAAY,GAAG,KAAK;IACzB,IAAI,CAACE,OAAO,GAAG,CAAC,CAAC;IACjB,IAAI,CAACE,4BAA4B,GAAG,KAAK;EAC7C;EACA,OAAOI,mBAAmBA,CAACC,MAAM,EAAEb,MAAM,EAAE;IACvC,IAAIA,MAAM,CAACc,IAAI,KAAK,CAAC,CAAC,+BAA+B,CAACd,MAAM,CAACe,mBAAmB,EAAE;MAC9E,OAAO,EAAE;IACb;IACA,MAAMC,KAAK,GAAGH,MAAM,CAACI,QAAQ,CAAC,CAAC;IAC/B,MAAMC,UAAU,GAAGlB,MAAM,CAACmB,KAAK,CAACC,eAAe;IAC/C,IAAIF,UAAU,GAAGF,KAAK,CAACK,YAAY,CAAC,CAAC,EAAE;MACnC;MACA,OAAO,EAAE;IACb;IACA,MAAMC,SAAS,GAAGN,KAAK,CAACO,gBAAgB,CAACL,UAAU,CAAC;IACpD,OAAOL,MAAM,CAACW,kBAAkB,CAACN,UAAU,CAAC,CAACO,MAAM,CAAEC,CAAC,IAAK;MACvD,IAAIA,CAAC,CAACC,OAAO,CAACC,WAAW,EAAE;QACvB,OAAO,IAAI;MACf;MACA,MAAMC,WAAW,GAAIH,CAAC,CAACP,KAAK,CAACC,eAAe,KAAKF,UAAU,GAAIQ,CAAC,CAACP,KAAK,CAACU,WAAW,GAAG,CAAC;MACtF,MAAMC,SAAS,GAAIJ,CAAC,CAACP,KAAK,CAACY,aAAa,KAAKb,UAAU,GAAIQ,CAAC,CAACP,KAAK,CAACW,SAAS,GAAGR,SAAS;MACxF,IAAII,CAAC,CAACC,OAAO,CAACK,eAAe,EAAE;QAC3B;QACA,IAAIH,WAAW,GAAG7B,MAAM,CAACmB,KAAK,CAACU,WAAW,GAAG,CAAC,IAAI7B,MAAM,CAACmB,KAAK,CAACW,SAAS,GAAG,CAAC,GAAGA,SAAS,EAAE;UACtF,OAAO,KAAK;QAChB;MACJ,CAAC,MACI;QACD,IAAID,WAAW,GAAG7B,MAAM,CAACmB,KAAK,CAACU,WAAW,IAAI7B,MAAM,CAACmB,KAAK,CAACW,SAAS,GAAGA,SAAS,EAAE;UAC9E,OAAO,KAAK;QAChB;MACJ;MACA,OAAO,IAAI;IACf,CAAC,CAAC;EACN;EACAG,YAAYA,CAACC,KAAK,EAAE;IAChB,MAAMlC,MAAM,GAAG,IAAI,CAACC,OAAO;IAC3B,IAAI,CAAC,IAAI,CAACS,OAAO,CAACyB,QAAQ,CAAC,CAAC,IAAI,CAACnC,MAAM,EAAE;MACrC,OAAOF,mBAAmB,CAACsC,KAAK;IACpC;IACA,MAAMC,eAAe,GAAGtC,oBAAoB,CAACa,mBAAmB,CAAC,IAAI,CAACF,OAAO,EAAEV,MAAM,CAAC;IACtF,OAAOF,mBAAmB,CAACwC,KAAK,CAAC,IAAI,CAAC3B,aAAa,CAAC4B,GAAG,CAAEC,WAAW,IAAK;MACrE,IAAI,CAACA,WAAW,CAACP,YAAY,EAAE;QAC3B,OAAOnC,mBAAmB,CAACsC,KAAK;MACpC;MACA,OAAOI,WAAW,CAACP,YAAY,CAACjC,MAAM,EAAEqC,eAAe,EAAEH,KAAK,CAAC;IACnE,CAAC,CAAC,CAAC;EACP;EACAO,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC,IAAI,CAAC/B,OAAO,CAACyB,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAClC,OAAO,EAAE;MAC3C,OAAO,EAAE;IACb;IACA,MAAMoC,eAAe,GAAGtC,oBAAoB,CAACa,mBAAmB,CAAC,IAAI,CAACF,OAAO,EAAE,IAAI,CAACT,OAAO,CAAC;IAC5F,IAAIyC,MAAM,GAAG,EAAE;IACf,KAAK,MAAMF,WAAW,IAAI,IAAI,CAAC7B,aAAa,EAAE;MAC1C+B,MAAM,GAAGA,MAAM,CAACC,MAAM,CAACH,WAAW,CAACC,WAAW,CAAC,IAAI,CAACxC,OAAO,EAAEoC,eAAe,CAAC,CAAC;IAClF;IACA,OAAOxC,QAAQ,CAAC6C,MAAM,CAAC;EAC3B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}