{"ast": null, "code": "import _asyncToGenerator from \"C:/console/aava-ui-web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = this && this.__param || function (paramIndex, decorator) {\n  return function (target, key) {\n    decorator(target, key, paramIndex);\n  };\n};\nimport { Disposable, DisposableStore } from '../../../../base/common/lifecycle.js';\nimport { ILanguageFeaturesService } from '../../../common/services/languageFeatures.js';\nimport { OutlineElement, OutlineGroup, OutlineModel } from '../../documentSymbols/browser/outlineModel.js';\nimport { createCancelablePromise, Delayer } from '../../../../base/common/async.js';\nimport { FoldingController, RangesLimitReporter } from '../../folding/browser/folding.js';\nimport { SyntaxRangeProvider } from '../../folding/browser/syntaxRangeProvider.js';\nimport { IndentRangeProvider } from '../../folding/browser/indentRangeProvider.js';\nimport { ILanguageConfigurationService } from '../../../common/languages/languageConfigurationRegistry.js';\nimport { onUnexpectedError } from '../../../../base/common/errors.js';\nimport { StickyElement, StickyModel, StickyRange } from './stickyScrollElement.js';\nimport { Iterable } from '../../../../base/common/iterator.js';\nimport { IInstantiationService } from '../../../../platform/instantiation/common/instantiation.js';\nvar ModelProvider = /*#__PURE__*/function (ModelProvider) {\n  ModelProvider[\"OUTLINE_MODEL\"] = \"outlineModel\";\n  ModelProvider[\"FOLDING_PROVIDER_MODEL\"] = \"foldingProviderModel\";\n  ModelProvider[\"INDENTATION_MODEL\"] = \"indentationModel\";\n  return ModelProvider;\n}(ModelProvider || {});\nvar Status = /*#__PURE__*/function (Status) {\n  Status[Status[\"VALID\"] = 0] = \"VALID\";\n  Status[Status[\"INVALID\"] = 1] = \"INVALID\";\n  Status[Status[\"CANCELED\"] = 2] = \"CANCELED\";\n  return Status;\n}(Status || {});\nlet StickyModelProvider = class StickyModelProvider extends Disposable {\n  constructor(_editor, onProviderUpdate, _languageConfigurationService, _languageFeaturesService) {\n    super();\n    this._editor = _editor;\n    this._modelProviders = [];\n    this._modelPromise = null;\n    this._updateScheduler = this._register(new Delayer(300));\n    this._updateOperation = this._register(new DisposableStore());\n    switch (this._editor.getOption(116 /* EditorOption.stickyScroll */).defaultModel) {\n      case ModelProvider.OUTLINE_MODEL:\n        this._modelProviders.push(new StickyModelFromCandidateOutlineProvider(this._editor, _languageFeaturesService));\n      // fall through\n      case ModelProvider.FOLDING_PROVIDER_MODEL:\n        this._modelProviders.push(new StickyModelFromCandidateSyntaxFoldingProvider(this._editor, onProviderUpdate, _languageFeaturesService));\n      // fall through\n      case ModelProvider.INDENTATION_MODEL:\n        this._modelProviders.push(new StickyModelFromCandidateIndentationFoldingProvider(this._editor, _languageConfigurationService));\n        break;\n    }\n  }\n  dispose() {\n    this._modelProviders.forEach(provider => provider.dispose());\n    this._updateOperation.clear();\n    this._cancelModelPromise();\n    super.dispose();\n  }\n  _cancelModelPromise() {\n    if (this._modelPromise) {\n      this._modelPromise.cancel();\n      this._modelPromise = null;\n    }\n  }\n  update(token) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this._updateOperation.clear();\n      _this._updateOperation.add({\n        dispose: () => {\n          _this._cancelModelPromise();\n          _this._updateScheduler.cancel();\n        }\n      });\n      _this._cancelModelPromise();\n      return yield _this._updateScheduler.trigger(/*#__PURE__*/_asyncToGenerator(function* () {\n        for (const modelProvider of _this._modelProviders) {\n          const {\n            statusPromise,\n            modelPromise\n          } = modelProvider.computeStickyModel(token);\n          _this._modelPromise = modelPromise;\n          const status = yield statusPromise;\n          if (_this._modelPromise !== modelPromise) {\n            return null;\n          }\n          switch (status) {\n            case Status.CANCELED:\n              _this._updateOperation.clear();\n              return null;\n            case Status.VALID:\n              return modelProvider.stickyModel;\n          }\n        }\n        return null;\n      })).catch(error => {\n        onUnexpectedError(error);\n        return null;\n      });\n    })();\n  }\n};\nStickyModelProvider = __decorate([__param(2, IInstantiationService), __param(3, ILanguageFeaturesService)], StickyModelProvider);\nexport { StickyModelProvider };\nclass StickyModelCandidateProvider extends Disposable {\n  constructor(_editor) {\n    super();\n    this._editor = _editor;\n    this._stickyModel = null;\n  }\n  get stickyModel() {\n    return this._stickyModel;\n  }\n  _invalid() {\n    this._stickyModel = null;\n    return Status.INVALID;\n  }\n  computeStickyModel(token) {\n    if (token.isCancellationRequested || !this.isProviderValid()) {\n      return {\n        statusPromise: this._invalid(),\n        modelPromise: null\n      };\n    }\n    const providerModelPromise = createCancelablePromise(token => this.createModelFromProvider(token));\n    return {\n      statusPromise: providerModelPromise.then(providerModel => {\n        if (!this.isModelValid(providerModel)) {\n          return this._invalid();\n        }\n        if (token.isCancellationRequested) {\n          return Status.CANCELED;\n        }\n        this._stickyModel = this.createStickyModel(token, providerModel);\n        return Status.VALID;\n      }).then(undefined, err => {\n        onUnexpectedError(err);\n        return Status.CANCELED;\n      }),\n      modelPromise: providerModelPromise\n    };\n  }\n  /**\n   * Method which checks whether the model returned by the provider is valid and can be used to compute a sticky model.\n   * This method by default returns true.\n   * @param model model returned by the provider\n   * @returns boolean indicating whether the model is valid\n   */\n  isModelValid(model) {\n    return true;\n  }\n  /**\n   * Method which checks whether the provider is valid before applying it to find the provider model.\n   * This method by default returns true.\n   * @returns boolean indicating whether the provider is valid\n   */\n  isProviderValid() {\n    return true;\n  }\n}\nlet StickyModelFromCandidateOutlineProvider = class StickyModelFromCandidateOutlineProvider extends StickyModelCandidateProvider {\n  constructor(_editor, _languageFeaturesService) {\n    super(_editor);\n    this._languageFeaturesService = _languageFeaturesService;\n  }\n  createModelFromProvider(token) {\n    return OutlineModel.create(this._languageFeaturesService.documentSymbolProvider, this._editor.getModel(), token);\n  }\n  createStickyModel(token, model) {\n    const {\n      stickyOutlineElement,\n      providerID\n    } = this._stickyModelFromOutlineModel(model, this._stickyModel?.outlineProviderId);\n    const textModel = this._editor.getModel();\n    return new StickyModel(textModel.uri, textModel.getVersionId(), stickyOutlineElement, providerID);\n  }\n  isModelValid(model) {\n    return model && model.children.size > 0;\n  }\n  _stickyModelFromOutlineModel(outlineModel, preferredProvider) {\n    let outlineElements;\n    // When several possible outline providers\n    if (Iterable.first(outlineModel.children.values()) instanceof OutlineGroup) {\n      const provider = Iterable.find(outlineModel.children.values(), outlineGroupOfModel => outlineGroupOfModel.id === preferredProvider);\n      if (provider) {\n        outlineElements = provider.children;\n      } else {\n        let tempID = '';\n        let maxTotalSumOfRanges = -1;\n        let optimalOutlineGroup = undefined;\n        for (const [_key, outlineGroup] of outlineModel.children.entries()) {\n          const totalSumRanges = this._findSumOfRangesOfGroup(outlineGroup);\n          if (totalSumRanges > maxTotalSumOfRanges) {\n            optimalOutlineGroup = outlineGroup;\n            maxTotalSumOfRanges = totalSumRanges;\n            tempID = outlineGroup.id;\n          }\n        }\n        preferredProvider = tempID;\n        outlineElements = optimalOutlineGroup.children;\n      }\n    } else {\n      outlineElements = outlineModel.children;\n    }\n    const stickyChildren = [];\n    const outlineElementsArray = Array.from(outlineElements.values()).sort((element1, element2) => {\n      const range1 = new StickyRange(element1.symbol.range.startLineNumber, element1.symbol.range.endLineNumber);\n      const range2 = new StickyRange(element2.symbol.range.startLineNumber, element2.symbol.range.endLineNumber);\n      return this._comparator(range1, range2);\n    });\n    for (const outlineElement of outlineElementsArray) {\n      stickyChildren.push(this._stickyModelFromOutlineElement(outlineElement, outlineElement.symbol.selectionRange.startLineNumber));\n    }\n    const stickyOutlineElement = new StickyElement(undefined, stickyChildren, undefined);\n    return {\n      stickyOutlineElement: stickyOutlineElement,\n      providerID: preferredProvider\n    };\n  }\n  _stickyModelFromOutlineElement(outlineElement, previousStartLine) {\n    const children = [];\n    for (const child of outlineElement.children.values()) {\n      if (child.symbol.selectionRange.startLineNumber !== child.symbol.range.endLineNumber) {\n        if (child.symbol.selectionRange.startLineNumber !== previousStartLine) {\n          children.push(this._stickyModelFromOutlineElement(child, child.symbol.selectionRange.startLineNumber));\n        } else {\n          for (const subchild of child.children.values()) {\n            children.push(this._stickyModelFromOutlineElement(subchild, child.symbol.selectionRange.startLineNumber));\n          }\n        }\n      }\n    }\n    children.sort((child1, child2) => this._comparator(child1.range, child2.range));\n    const range = new StickyRange(outlineElement.symbol.selectionRange.startLineNumber, outlineElement.symbol.range.endLineNumber);\n    return new StickyElement(range, children, undefined);\n  }\n  _comparator(range1, range2) {\n    if (range1.startLineNumber !== range2.startLineNumber) {\n      return range1.startLineNumber - range2.startLineNumber;\n    } else {\n      return range2.endLineNumber - range1.endLineNumber;\n    }\n  }\n  _findSumOfRangesOfGroup(outline) {\n    let res = 0;\n    for (const child of outline.children.values()) {\n      res += this._findSumOfRangesOfGroup(child);\n    }\n    if (outline instanceof OutlineElement) {\n      return res + outline.symbol.range.endLineNumber - outline.symbol.selectionRange.startLineNumber;\n    } else {\n      return res;\n    }\n  }\n};\nStickyModelFromCandidateOutlineProvider = __decorate([__param(1, ILanguageFeaturesService)], StickyModelFromCandidateOutlineProvider);\nclass StickyModelFromCandidateFoldingProvider extends StickyModelCandidateProvider {\n  constructor(editor) {\n    super(editor);\n    this._foldingLimitReporter = new RangesLimitReporter(editor);\n  }\n  createStickyModel(token, model) {\n    const foldingElement = this._fromFoldingRegions(model);\n    const textModel = this._editor.getModel();\n    return new StickyModel(textModel.uri, textModel.getVersionId(), foldingElement, undefined);\n  }\n  isModelValid(model) {\n    return model !== null;\n  }\n  _fromFoldingRegions(foldingRegions) {\n    const length = foldingRegions.length;\n    const orderedStickyElements = [];\n    // The root sticky outline element\n    const stickyOutlineElement = new StickyElement(undefined, [], undefined);\n    for (let i = 0; i < length; i++) {\n      // Finding the parent index of the current range\n      const parentIndex = foldingRegions.getParentIndex(i);\n      let parentNode;\n      if (parentIndex !== -1) {\n        // Access the reference of the parent node\n        parentNode = orderedStickyElements[parentIndex];\n      } else {\n        // In that case the parent node is the root node\n        parentNode = stickyOutlineElement;\n      }\n      const child = new StickyElement(new StickyRange(foldingRegions.getStartLineNumber(i), foldingRegions.getEndLineNumber(i) + 1), [], parentNode);\n      parentNode.children.push(child);\n      orderedStickyElements.push(child);\n    }\n    return stickyOutlineElement;\n  }\n}\nlet StickyModelFromCandidateIndentationFoldingProvider = class StickyModelFromCandidateIndentationFoldingProvider extends StickyModelFromCandidateFoldingProvider {\n  constructor(editor, _languageConfigurationService) {\n    super(editor);\n    this._languageConfigurationService = _languageConfigurationService;\n    this.provider = this._register(new IndentRangeProvider(editor.getModel(), this._languageConfigurationService, this._foldingLimitReporter));\n  }\n  createModelFromProvider(token) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      return _this2.provider.compute(token);\n    })();\n  }\n};\nStickyModelFromCandidateIndentationFoldingProvider = __decorate([__param(1, ILanguageConfigurationService)], StickyModelFromCandidateIndentationFoldingProvider);\nlet StickyModelFromCandidateSyntaxFoldingProvider = class StickyModelFromCandidateSyntaxFoldingProvider extends StickyModelFromCandidateFoldingProvider {\n  constructor(editor, onProviderUpdate, _languageFeaturesService) {\n    super(editor);\n    this._languageFeaturesService = _languageFeaturesService;\n    const selectedProviders = FoldingController.getFoldingRangeProviders(this._languageFeaturesService, editor.getModel());\n    if (selectedProviders.length > 0) {\n      this.provider = this._register(new SyntaxRangeProvider(editor.getModel(), selectedProviders, onProviderUpdate, this._foldingLimitReporter, undefined));\n    }\n  }\n  isProviderValid() {\n    return this.provider !== undefined;\n  }\n  createModelFromProvider(token) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      return _this3.provider?.compute(token) ?? null;\n    })();\n  }\n};\nStickyModelFromCandidateSyntaxFoldingProvider = __decorate([__param(2, ILanguageFeaturesService)], StickyModelFromCandidateSyntaxFoldingProvider);", "map": {"version": 3, "names": ["__decorate", "decorators", "target", "key", "desc", "c", "arguments", "length", "r", "Object", "getOwnPropertyDescriptor", "d", "Reflect", "decorate", "i", "defineProperty", "__param", "paramIndex", "decorator", "Disposable", "DisposableStore", "ILanguageFeaturesService", "OutlineElement", "OutlineGroup", "OutlineModel", "createCancelablePromise", "<PERSON><PERSON><PERSON>", "FoldingController", "RangesLimitReporter", "SyntaxRangeProvider", "IndentRangeProvider", "ILanguageConfigurationService", "onUnexpectedError", "StickyElement", "StickyModel", "StickyRange", "Iterable", "IInstantiationService", "Model<PERSON>rovider", "Status", "StickyModelProvider", "constructor", "_editor", "onProviderUpdate", "_languageConfigurationService", "_languageFeaturesService", "_modelProviders", "_modelPromise", "_updateScheduler", "_register", "_updateOperation", "getOption", "defaultModel", "OUTLINE_MODEL", "push", "StickyModelFromCandidateOutlineProvider", "FOLDING_PROVIDER_MODEL", "StickyModelFromCandidateSyntaxFoldingProvider", "INDENTATION_MODEL", "StickyModelFromCandidateIndentationFoldingProvider", "dispose", "for<PERSON>ach", "provider", "clear", "_cancelModelPromise", "cancel", "update", "token", "_this", "_asyncToGenerator", "add", "trigger", "modelProvider", "statusPromise", "modelPromise", "computeStickyModel", "status", "CANCELED", "VALID", "stickyModel", "catch", "error", "StickyModelCandidateProvider", "_stickyModel", "_invalid", "INVALID", "isCancellationRequested", "isProviderValid", "providerModelPromise", "createModelFromProvider", "then", "providerModel", "isModelValid", "createStickyModel", "undefined", "err", "model", "create", "documentSymbolProvider", "getModel", "stickyOutlineElement", "providerID", "_stickyModelFromOutlineModel", "outlineProviderId", "textModel", "uri", "getVersionId", "children", "size", "outlineModel", "preferredProvider", "outlineElements", "first", "values", "find", "outlineGroupOfModel", "id", "tempID", "maxTotalSumOfRanges", "optimalOutlineGroup", "_key", "outlineGroup", "entries", "totalSumRanges", "_findSumOfRangesOfGroup", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outlineElementsArray", "Array", "from", "sort", "element1", "element2", "range1", "symbol", "range", "startLineNumber", "endLineNumber", "range2", "_comparator", "outlineElement", "_stickyModelFromOutlineElement", "<PERSON><PERSON><PERSON><PERSON>", "previousStartLine", "child", "subchild", "child1", "child2", "outline", "res", "StickyModelFromCandidateFoldingProvider", "editor", "_foldingLimitReporter", "foldingElement", "_fromFoldingRegions", "foldingRegions", "orderedStickyElements", "parentIndex", "getParentIndex", "parentNode", "getStartLineNumber", "getEndLineNumber", "_this2", "compute", "selectedProviders", "getFoldingRangeProviders", "_this3"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/contrib/stickyScroll/browser/stickyScrollModelProvider.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = (this && this.__param) || function (paramIndex, decorator) {\n    return function (target, key) { decorator(target, key, paramIndex); }\n};\nimport { Disposable, DisposableStore } from '../../../../base/common/lifecycle.js';\nimport { ILanguageFeaturesService } from '../../../common/services/languageFeatures.js';\nimport { OutlineElement, OutlineGroup, OutlineModel } from '../../documentSymbols/browser/outlineModel.js';\nimport { createCancelablePromise, Delayer } from '../../../../base/common/async.js';\nimport { FoldingController, RangesLimitReporter } from '../../folding/browser/folding.js';\nimport { SyntaxRangeProvider } from '../../folding/browser/syntaxRangeProvider.js';\nimport { IndentRangeProvider } from '../../folding/browser/indentRangeProvider.js';\nimport { ILanguageConfigurationService } from '../../../common/languages/languageConfigurationRegistry.js';\nimport { onUnexpectedError } from '../../../../base/common/errors.js';\nimport { StickyElement, StickyModel, StickyRange } from './stickyScrollElement.js';\nimport { Iterable } from '../../../../base/common/iterator.js';\nimport { IInstantiationService } from '../../../../platform/instantiation/common/instantiation.js';\nvar ModelProvider;\n(function (ModelProvider) {\n    ModelProvider[\"OUTLINE_MODEL\"] = \"outlineModel\";\n    ModelProvider[\"FOLDING_PROVIDER_MODEL\"] = \"foldingProviderModel\";\n    ModelProvider[\"INDENTATION_MODEL\"] = \"indentationModel\";\n})(ModelProvider || (ModelProvider = {}));\nvar Status;\n(function (Status) {\n    Status[Status[\"VALID\"] = 0] = \"VALID\";\n    Status[Status[\"INVALID\"] = 1] = \"INVALID\";\n    Status[Status[\"CANCELED\"] = 2] = \"CANCELED\";\n})(Status || (Status = {}));\nlet StickyModelProvider = class StickyModelProvider extends Disposable {\n    constructor(_editor, onProviderUpdate, _languageConfigurationService, _languageFeaturesService) {\n        super();\n        this._editor = _editor;\n        this._modelProviders = [];\n        this._modelPromise = null;\n        this._updateScheduler = this._register(new Delayer(300));\n        this._updateOperation = this._register(new DisposableStore());\n        switch (this._editor.getOption(116 /* EditorOption.stickyScroll */).defaultModel) {\n            case ModelProvider.OUTLINE_MODEL:\n                this._modelProviders.push(new StickyModelFromCandidateOutlineProvider(this._editor, _languageFeaturesService));\n            // fall through\n            case ModelProvider.FOLDING_PROVIDER_MODEL:\n                this._modelProviders.push(new StickyModelFromCandidateSyntaxFoldingProvider(this._editor, onProviderUpdate, _languageFeaturesService));\n            // fall through\n            case ModelProvider.INDENTATION_MODEL:\n                this._modelProviders.push(new StickyModelFromCandidateIndentationFoldingProvider(this._editor, _languageConfigurationService));\n                break;\n        }\n    }\n    dispose() {\n        this._modelProviders.forEach(provider => provider.dispose());\n        this._updateOperation.clear();\n        this._cancelModelPromise();\n        super.dispose();\n    }\n    _cancelModelPromise() {\n        if (this._modelPromise) {\n            this._modelPromise.cancel();\n            this._modelPromise = null;\n        }\n    }\n    async update(token) {\n        this._updateOperation.clear();\n        this._updateOperation.add({\n            dispose: () => {\n                this._cancelModelPromise();\n                this._updateScheduler.cancel();\n            }\n        });\n        this._cancelModelPromise();\n        return await this._updateScheduler.trigger(async () => {\n            for (const modelProvider of this._modelProviders) {\n                const { statusPromise, modelPromise } = modelProvider.computeStickyModel(token);\n                this._modelPromise = modelPromise;\n                const status = await statusPromise;\n                if (this._modelPromise !== modelPromise) {\n                    return null;\n                }\n                switch (status) {\n                    case Status.CANCELED:\n                        this._updateOperation.clear();\n                        return null;\n                    case Status.VALID:\n                        return modelProvider.stickyModel;\n                }\n            }\n            return null;\n        }).catch((error) => {\n            onUnexpectedError(error);\n            return null;\n        });\n    }\n};\nStickyModelProvider = __decorate([\n    __param(2, IInstantiationService),\n    __param(3, ILanguageFeaturesService)\n], StickyModelProvider);\nexport { StickyModelProvider };\nclass StickyModelCandidateProvider extends Disposable {\n    constructor(_editor) {\n        super();\n        this._editor = _editor;\n        this._stickyModel = null;\n    }\n    get stickyModel() {\n        return this._stickyModel;\n    }\n    _invalid() {\n        this._stickyModel = null;\n        return Status.INVALID;\n    }\n    computeStickyModel(token) {\n        if (token.isCancellationRequested || !this.isProviderValid()) {\n            return { statusPromise: this._invalid(), modelPromise: null };\n        }\n        const providerModelPromise = createCancelablePromise(token => this.createModelFromProvider(token));\n        return {\n            statusPromise: providerModelPromise.then(providerModel => {\n                if (!this.isModelValid(providerModel)) {\n                    return this._invalid();\n                }\n                if (token.isCancellationRequested) {\n                    return Status.CANCELED;\n                }\n                this._stickyModel = this.createStickyModel(token, providerModel);\n                return Status.VALID;\n            }).then(undefined, (err) => {\n                onUnexpectedError(err);\n                return Status.CANCELED;\n            }),\n            modelPromise: providerModelPromise\n        };\n    }\n    /**\n     * Method which checks whether the model returned by the provider is valid and can be used to compute a sticky model.\n     * This method by default returns true.\n     * @param model model returned by the provider\n     * @returns boolean indicating whether the model is valid\n     */\n    isModelValid(model) {\n        return true;\n    }\n    /**\n     * Method which checks whether the provider is valid before applying it to find the provider model.\n     * This method by default returns true.\n     * @returns boolean indicating whether the provider is valid\n     */\n    isProviderValid() {\n        return true;\n    }\n}\nlet StickyModelFromCandidateOutlineProvider = class StickyModelFromCandidateOutlineProvider extends StickyModelCandidateProvider {\n    constructor(_editor, _languageFeaturesService) {\n        super(_editor);\n        this._languageFeaturesService = _languageFeaturesService;\n    }\n    createModelFromProvider(token) {\n        return OutlineModel.create(this._languageFeaturesService.documentSymbolProvider, this._editor.getModel(), token);\n    }\n    createStickyModel(token, model) {\n        const { stickyOutlineElement, providerID } = this._stickyModelFromOutlineModel(model, this._stickyModel?.outlineProviderId);\n        const textModel = this._editor.getModel();\n        return new StickyModel(textModel.uri, textModel.getVersionId(), stickyOutlineElement, providerID);\n    }\n    isModelValid(model) {\n        return model && model.children.size > 0;\n    }\n    _stickyModelFromOutlineModel(outlineModel, preferredProvider) {\n        let outlineElements;\n        // When several possible outline providers\n        if (Iterable.first(outlineModel.children.values()) instanceof OutlineGroup) {\n            const provider = Iterable.find(outlineModel.children.values(), outlineGroupOfModel => outlineGroupOfModel.id === preferredProvider);\n            if (provider) {\n                outlineElements = provider.children;\n            }\n            else {\n                let tempID = '';\n                let maxTotalSumOfRanges = -1;\n                let optimalOutlineGroup = undefined;\n                for (const [_key, outlineGroup] of outlineModel.children.entries()) {\n                    const totalSumRanges = this._findSumOfRangesOfGroup(outlineGroup);\n                    if (totalSumRanges > maxTotalSumOfRanges) {\n                        optimalOutlineGroup = outlineGroup;\n                        maxTotalSumOfRanges = totalSumRanges;\n                        tempID = outlineGroup.id;\n                    }\n                }\n                preferredProvider = tempID;\n                outlineElements = optimalOutlineGroup.children;\n            }\n        }\n        else {\n            outlineElements = outlineModel.children;\n        }\n        const stickyChildren = [];\n        const outlineElementsArray = Array.from(outlineElements.values()).sort((element1, element2) => {\n            const range1 = new StickyRange(element1.symbol.range.startLineNumber, element1.symbol.range.endLineNumber);\n            const range2 = new StickyRange(element2.symbol.range.startLineNumber, element2.symbol.range.endLineNumber);\n            return this._comparator(range1, range2);\n        });\n        for (const outlineElement of outlineElementsArray) {\n            stickyChildren.push(this._stickyModelFromOutlineElement(outlineElement, outlineElement.symbol.selectionRange.startLineNumber));\n        }\n        const stickyOutlineElement = new StickyElement(undefined, stickyChildren, undefined);\n        return {\n            stickyOutlineElement: stickyOutlineElement,\n            providerID: preferredProvider\n        };\n    }\n    _stickyModelFromOutlineElement(outlineElement, previousStartLine) {\n        const children = [];\n        for (const child of outlineElement.children.values()) {\n            if (child.symbol.selectionRange.startLineNumber !== child.symbol.range.endLineNumber) {\n                if (child.symbol.selectionRange.startLineNumber !== previousStartLine) {\n                    children.push(this._stickyModelFromOutlineElement(child, child.symbol.selectionRange.startLineNumber));\n                }\n                else {\n                    for (const subchild of child.children.values()) {\n                        children.push(this._stickyModelFromOutlineElement(subchild, child.symbol.selectionRange.startLineNumber));\n                    }\n                }\n            }\n        }\n        children.sort((child1, child2) => this._comparator(child1.range, child2.range));\n        const range = new StickyRange(outlineElement.symbol.selectionRange.startLineNumber, outlineElement.symbol.range.endLineNumber);\n        return new StickyElement(range, children, undefined);\n    }\n    _comparator(range1, range2) {\n        if (range1.startLineNumber !== range2.startLineNumber) {\n            return range1.startLineNumber - range2.startLineNumber;\n        }\n        else {\n            return range2.endLineNumber - range1.endLineNumber;\n        }\n    }\n    _findSumOfRangesOfGroup(outline) {\n        let res = 0;\n        for (const child of outline.children.values()) {\n            res += this._findSumOfRangesOfGroup(child);\n        }\n        if (outline instanceof OutlineElement) {\n            return res + outline.symbol.range.endLineNumber - outline.symbol.selectionRange.startLineNumber;\n        }\n        else {\n            return res;\n        }\n    }\n};\nStickyModelFromCandidateOutlineProvider = __decorate([\n    __param(1, ILanguageFeaturesService)\n], StickyModelFromCandidateOutlineProvider);\nclass StickyModelFromCandidateFoldingProvider extends StickyModelCandidateProvider {\n    constructor(editor) {\n        super(editor);\n        this._foldingLimitReporter = new RangesLimitReporter(editor);\n    }\n    createStickyModel(token, model) {\n        const foldingElement = this._fromFoldingRegions(model);\n        const textModel = this._editor.getModel();\n        return new StickyModel(textModel.uri, textModel.getVersionId(), foldingElement, undefined);\n    }\n    isModelValid(model) {\n        return model !== null;\n    }\n    _fromFoldingRegions(foldingRegions) {\n        const length = foldingRegions.length;\n        const orderedStickyElements = [];\n        // The root sticky outline element\n        const stickyOutlineElement = new StickyElement(undefined, [], undefined);\n        for (let i = 0; i < length; i++) {\n            // Finding the parent index of the current range\n            const parentIndex = foldingRegions.getParentIndex(i);\n            let parentNode;\n            if (parentIndex !== -1) {\n                // Access the reference of the parent node\n                parentNode = orderedStickyElements[parentIndex];\n            }\n            else {\n                // In that case the parent node is the root node\n                parentNode = stickyOutlineElement;\n            }\n            const child = new StickyElement(new StickyRange(foldingRegions.getStartLineNumber(i), foldingRegions.getEndLineNumber(i) + 1), [], parentNode);\n            parentNode.children.push(child);\n            orderedStickyElements.push(child);\n        }\n        return stickyOutlineElement;\n    }\n}\nlet StickyModelFromCandidateIndentationFoldingProvider = class StickyModelFromCandidateIndentationFoldingProvider extends StickyModelFromCandidateFoldingProvider {\n    constructor(editor, _languageConfigurationService) {\n        super(editor);\n        this._languageConfigurationService = _languageConfigurationService;\n        this.provider = this._register(new IndentRangeProvider(editor.getModel(), this._languageConfigurationService, this._foldingLimitReporter));\n    }\n    async createModelFromProvider(token) {\n        return this.provider.compute(token);\n    }\n};\nStickyModelFromCandidateIndentationFoldingProvider = __decorate([\n    __param(1, ILanguageConfigurationService)\n], StickyModelFromCandidateIndentationFoldingProvider);\nlet StickyModelFromCandidateSyntaxFoldingProvider = class StickyModelFromCandidateSyntaxFoldingProvider extends StickyModelFromCandidateFoldingProvider {\n    constructor(editor, onProviderUpdate, _languageFeaturesService) {\n        super(editor);\n        this._languageFeaturesService = _languageFeaturesService;\n        const selectedProviders = FoldingController.getFoldingRangeProviders(this._languageFeaturesService, editor.getModel());\n        if (selectedProviders.length > 0) {\n            this.provider = this._register(new SyntaxRangeProvider(editor.getModel(), selectedProviders, onProviderUpdate, this._foldingLimitReporter, undefined));\n        }\n    }\n    isProviderValid() {\n        return this.provider !== undefined;\n    }\n    async createModelFromProvider(token) {\n        return this.provider?.compute(token) ?? null;\n    }\n};\nStickyModelFromCandidateSyntaxFoldingProvider = __decorate([\n    __param(2, ILanguageFeaturesService)\n], StickyModelFromCandidateSyntaxFoldingProvider);\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA,IAAIA,UAAU,GAAI,IAAI,IAAI,IAAI,CAACA,UAAU,IAAK,UAAUC,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAE;EACnF,IAAIC,CAAC,GAAGC,SAAS,CAACC,MAAM;IAAEC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGH,MAAM,GAAGE,IAAI,KAAK,IAAI,GAAGA,IAAI,GAAGK,MAAM,CAACC,wBAAwB,CAACR,MAAM,EAAEC,GAAG,CAAC,GAAGC,IAAI;IAAEO,CAAC;EAC5H,IAAI,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,CAACC,QAAQ,KAAK,UAAU,EAAEL,CAAC,GAAGI,OAAO,CAACC,QAAQ,CAACZ,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,CAAC,CAAC,KAC1H,KAAK,IAAIU,CAAC,GAAGb,UAAU,CAACM,MAAM,GAAG,CAAC,EAAEO,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE,IAAIH,CAAC,GAAGV,UAAU,CAACa,CAAC,CAAC,EAAEN,CAAC,GAAG,CAACH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACH,CAAC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACT,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,GAAGG,CAAC,CAACT,MAAM,EAAEC,GAAG,CAAC,KAAKK,CAAC;EACjJ,OAAOH,CAAC,GAAG,CAAC,IAAIG,CAAC,IAAIC,MAAM,CAACM,cAAc,CAACb,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,EAAEA,CAAC;AACjE,CAAC;AACD,IAAIQ,OAAO,GAAI,IAAI,IAAI,IAAI,CAACA,OAAO,IAAK,UAAUC,UAAU,EAAEC,SAAS,EAAE;EACrE,OAAO,UAAUhB,MAAM,EAAEC,GAAG,EAAE;IAAEe,SAAS,CAAChB,MAAM,EAAEC,GAAG,EAAEc,UAAU,CAAC;EAAE,CAAC;AACzE,CAAC;AACD,SAASE,UAAU,EAAEC,eAAe,QAAQ,sCAAsC;AAClF,SAASC,wBAAwB,QAAQ,8CAA8C;AACvF,SAASC,cAAc,EAAEC,YAAY,EAAEC,YAAY,QAAQ,+CAA+C;AAC1G,SAASC,uBAAuB,EAAEC,OAAO,QAAQ,kCAAkC;AACnF,SAASC,iBAAiB,EAAEC,mBAAmB,QAAQ,kCAAkC;AACzF,SAASC,mBAAmB,QAAQ,8CAA8C;AAClF,SAASC,mBAAmB,QAAQ,8CAA8C;AAClF,SAASC,6BAA6B,QAAQ,4DAA4D;AAC1G,SAASC,iBAAiB,QAAQ,mCAAmC;AACrE,SAASC,aAAa,EAAEC,WAAW,EAAEC,WAAW,QAAQ,0BAA0B;AAClF,SAASC,QAAQ,QAAQ,qCAAqC;AAC9D,SAASC,qBAAqB,QAAQ,4DAA4D;AAClG,IAAIC,aAAa,gBAChB,UAAUA,aAAa,EAAE;EACtBA,aAAa,CAAC,eAAe,CAAC,GAAG,cAAc;EAC/CA,aAAa,CAAC,wBAAwB,CAAC,GAAG,sBAAsB;EAChEA,aAAa,CAAC,mBAAmB,CAAC,GAAG,kBAAkB;EAAC,OAHjDA,aAAa;AAIxB,CAAC,CAAEA,aAAa,IAAqB,CAAC,CAAE,CALvB;AAMjB,IAAIC,MAAM,gBACT,UAAUA,MAAM,EAAE;EACfA,MAAM,CAACA,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;EACrCA,MAAM,CAACA,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS;EACzCA,MAAM,CAACA,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU;EAAC,OAHrCA,MAAM;AAIjB,CAAC,CAAEA,MAAM,IAAc,CAAC,CAAE,CALhB;AAMV,IAAIC,mBAAmB,GAAG,MAAMA,mBAAmB,SAASrB,UAAU,CAAC;EACnEsB,WAAWA,CAACC,OAAO,EAAEC,gBAAgB,EAAEC,6BAA6B,EAAEC,wBAAwB,EAAE;IAC5F,KAAK,CAAC,CAAC;IACP,IAAI,CAACH,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACI,eAAe,GAAG,EAAE;IACzB,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACC,SAAS,CAAC,IAAIvB,OAAO,CAAC,GAAG,CAAC,CAAC;IACxD,IAAI,CAACwB,gBAAgB,GAAG,IAAI,CAACD,SAAS,CAAC,IAAI7B,eAAe,CAAC,CAAC,CAAC;IAC7D,QAAQ,IAAI,CAACsB,OAAO,CAACS,SAAS,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAACC,YAAY;MAC5E,KAAKd,aAAa,CAACe,aAAa;QAC5B,IAAI,CAACP,eAAe,CAACQ,IAAI,CAAC,IAAIC,uCAAuC,CAAC,IAAI,CAACb,OAAO,EAAEG,wBAAwB,CAAC,CAAC;MAClH;MACA,KAAKP,aAAa,CAACkB,sBAAsB;QACrC,IAAI,CAACV,eAAe,CAACQ,IAAI,CAAC,IAAIG,6CAA6C,CAAC,IAAI,CAACf,OAAO,EAAEC,gBAAgB,EAAEE,wBAAwB,CAAC,CAAC;MAC1I;MACA,KAAKP,aAAa,CAACoB,iBAAiB;QAChC,IAAI,CAACZ,eAAe,CAACQ,IAAI,CAAC,IAAIK,kDAAkD,CAAC,IAAI,CAACjB,OAAO,EAAEE,6BAA6B,CAAC,CAAC;QAC9H;IACR;EACJ;EACAgB,OAAOA,CAAA,EAAG;IACN,IAAI,CAACd,eAAe,CAACe,OAAO,CAACC,QAAQ,IAAIA,QAAQ,CAACF,OAAO,CAAC,CAAC,CAAC;IAC5D,IAAI,CAACV,gBAAgB,CAACa,KAAK,CAAC,CAAC;IAC7B,IAAI,CAACC,mBAAmB,CAAC,CAAC;IAC1B,KAAK,CAACJ,OAAO,CAAC,CAAC;EACnB;EACAI,mBAAmBA,CAAA,EAAG;IAClB,IAAI,IAAI,CAACjB,aAAa,EAAE;MACpB,IAAI,CAACA,aAAa,CAACkB,MAAM,CAAC,CAAC;MAC3B,IAAI,CAAClB,aAAa,GAAG,IAAI;IAC7B;EACJ;EACMmB,MAAMA,CAACC,KAAK,EAAE;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAChBD,KAAI,CAAClB,gBAAgB,CAACa,KAAK,CAAC,CAAC;MAC7BK,KAAI,CAAClB,gBAAgB,CAACoB,GAAG,CAAC;QACtBV,OAAO,EAAEA,CAAA,KAAM;UACXQ,KAAI,CAACJ,mBAAmB,CAAC,CAAC;UAC1BI,KAAI,CAACpB,gBAAgB,CAACiB,MAAM,CAAC,CAAC;QAClC;MACJ,CAAC,CAAC;MACFG,KAAI,CAACJ,mBAAmB,CAAC,CAAC;MAC1B,aAAaI,KAAI,CAACpB,gBAAgB,CAACuB,OAAO,cAAAF,iBAAA,CAAC,aAAY;QACnD,KAAK,MAAMG,aAAa,IAAIJ,KAAI,CAACtB,eAAe,EAAE;UAC9C,MAAM;YAAE2B,aAAa;YAAEC;UAAa,CAAC,GAAGF,aAAa,CAACG,kBAAkB,CAACR,KAAK,CAAC;UAC/EC,KAAI,CAACrB,aAAa,GAAG2B,YAAY;UACjC,MAAME,MAAM,SAASH,aAAa;UAClC,IAAIL,KAAI,CAACrB,aAAa,KAAK2B,YAAY,EAAE;YACrC,OAAO,IAAI;UACf;UACA,QAAQE,MAAM;YACV,KAAKrC,MAAM,CAACsC,QAAQ;cAChBT,KAAI,CAAClB,gBAAgB,CAACa,KAAK,CAAC,CAAC;cAC7B,OAAO,IAAI;YACf,KAAKxB,MAAM,CAACuC,KAAK;cACb,OAAON,aAAa,CAACO,WAAW;UACxC;QACJ;QACA,OAAO,IAAI;MACf,CAAC,EAAC,CAACC,KAAK,CAAEC,KAAK,IAAK;QAChBjD,iBAAiB,CAACiD,KAAK,CAAC;QACxB,OAAO,IAAI;MACf,CAAC,CAAC;IAAC;EACP;AACJ,CAAC;AACDzC,mBAAmB,GAAGxC,UAAU,CAAC,CAC7BgB,OAAO,CAAC,CAAC,EAAEqB,qBAAqB,CAAC,EACjCrB,OAAO,CAAC,CAAC,EAAEK,wBAAwB,CAAC,CACvC,EAAEmB,mBAAmB,CAAC;AACvB,SAASA,mBAAmB;AAC5B,MAAM0C,4BAA4B,SAAS/D,UAAU,CAAC;EAClDsB,WAAWA,CAACC,OAAO,EAAE;IACjB,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACyC,YAAY,GAAG,IAAI;EAC5B;EACA,IAAIJ,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACI,YAAY;EAC5B;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACD,YAAY,GAAG,IAAI;IACxB,OAAO5C,MAAM,CAAC8C,OAAO;EACzB;EACAV,kBAAkBA,CAACR,KAAK,EAAE;IACtB,IAAIA,KAAK,CAACmB,uBAAuB,IAAI,CAAC,IAAI,CAACC,eAAe,CAAC,CAAC,EAAE;MAC1D,OAAO;QAAEd,aAAa,EAAE,IAAI,CAACW,QAAQ,CAAC,CAAC;QAAEV,YAAY,EAAE;MAAK,CAAC;IACjE;IACA,MAAMc,oBAAoB,GAAG/D,uBAAuB,CAAC0C,KAAK,IAAI,IAAI,CAACsB,uBAAuB,CAACtB,KAAK,CAAC,CAAC;IAClG,OAAO;MACHM,aAAa,EAAEe,oBAAoB,CAACE,IAAI,CAACC,aAAa,IAAI;QACtD,IAAI,CAAC,IAAI,CAACC,YAAY,CAACD,aAAa,CAAC,EAAE;UACnC,OAAO,IAAI,CAACP,QAAQ,CAAC,CAAC;QAC1B;QACA,IAAIjB,KAAK,CAACmB,uBAAuB,EAAE;UAC/B,OAAO/C,MAAM,CAACsC,QAAQ;QAC1B;QACA,IAAI,CAACM,YAAY,GAAG,IAAI,CAACU,iBAAiB,CAAC1B,KAAK,EAAEwB,aAAa,CAAC;QAChE,OAAOpD,MAAM,CAACuC,KAAK;MACvB,CAAC,CAAC,CAACY,IAAI,CAACI,SAAS,EAAGC,GAAG,IAAK;QACxB/D,iBAAiB,CAAC+D,GAAG,CAAC;QACtB,OAAOxD,MAAM,CAACsC,QAAQ;MAC1B,CAAC,CAAC;MACFH,YAAY,EAAEc;IAClB,CAAC;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;EACII,YAAYA,CAACI,KAAK,EAAE;IAChB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACIT,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI;EACf;AACJ;AACA,IAAIhC,uCAAuC,GAAG,MAAMA,uCAAuC,SAAS2B,4BAA4B,CAAC;EAC7HzC,WAAWA,CAACC,OAAO,EAAEG,wBAAwB,EAAE;IAC3C,KAAK,CAACH,OAAO,CAAC;IACd,IAAI,CAACG,wBAAwB,GAAGA,wBAAwB;EAC5D;EACA4C,uBAAuBA,CAACtB,KAAK,EAAE;IAC3B,OAAO3C,YAAY,CAACyE,MAAM,CAAC,IAAI,CAACpD,wBAAwB,CAACqD,sBAAsB,EAAE,IAAI,CAACxD,OAAO,CAACyD,QAAQ,CAAC,CAAC,EAAEhC,KAAK,CAAC;EACpH;EACA0B,iBAAiBA,CAAC1B,KAAK,EAAE6B,KAAK,EAAE;IAC5B,MAAM;MAAEI,oBAAoB;MAAEC;IAAW,CAAC,GAAG,IAAI,CAACC,4BAA4B,CAACN,KAAK,EAAE,IAAI,CAACb,YAAY,EAAEoB,iBAAiB,CAAC;IAC3H,MAAMC,SAAS,GAAG,IAAI,CAAC9D,OAAO,CAACyD,QAAQ,CAAC,CAAC;IACzC,OAAO,IAAIjE,WAAW,CAACsE,SAAS,CAACC,GAAG,EAAED,SAAS,CAACE,YAAY,CAAC,CAAC,EAAEN,oBAAoB,EAAEC,UAAU,CAAC;EACrG;EACAT,YAAYA,CAACI,KAAK,EAAE;IAChB,OAAOA,KAAK,IAAIA,KAAK,CAACW,QAAQ,CAACC,IAAI,GAAG,CAAC;EAC3C;EACAN,4BAA4BA,CAACO,YAAY,EAAEC,iBAAiB,EAAE;IAC1D,IAAIC,eAAe;IACnB;IACA,IAAI3E,QAAQ,CAAC4E,KAAK,CAACH,YAAY,CAACF,QAAQ,CAACM,MAAM,CAAC,CAAC,CAAC,YAAY1F,YAAY,EAAE;MACxE,MAAMuC,QAAQ,GAAG1B,QAAQ,CAAC8E,IAAI,CAACL,YAAY,CAACF,QAAQ,CAACM,MAAM,CAAC,CAAC,EAAEE,mBAAmB,IAAIA,mBAAmB,CAACC,EAAE,KAAKN,iBAAiB,CAAC;MACnI,IAAIhD,QAAQ,EAAE;QACViD,eAAe,GAAGjD,QAAQ,CAAC6C,QAAQ;MACvC,CAAC,MACI;QACD,IAAIU,MAAM,GAAG,EAAE;QACf,IAAIC,mBAAmB,GAAG,CAAC,CAAC;QAC5B,IAAIC,mBAAmB,GAAGzB,SAAS;QACnC,KAAK,MAAM,CAAC0B,IAAI,EAAEC,YAAY,CAAC,IAAIZ,YAAY,CAACF,QAAQ,CAACe,OAAO,CAAC,CAAC,EAAE;UAChE,MAAMC,cAAc,GAAG,IAAI,CAACC,uBAAuB,CAACH,YAAY,CAAC;UACjE,IAAIE,cAAc,GAAGL,mBAAmB,EAAE;YACtCC,mBAAmB,GAAGE,YAAY;YAClCH,mBAAmB,GAAGK,cAAc;YACpCN,MAAM,GAAGI,YAAY,CAACL,EAAE;UAC5B;QACJ;QACAN,iBAAiB,GAAGO,MAAM;QAC1BN,eAAe,GAAGQ,mBAAmB,CAACZ,QAAQ;MAClD;IACJ,CAAC,MACI;MACDI,eAAe,GAAGF,YAAY,CAACF,QAAQ;IAC3C;IACA,MAAMkB,cAAc,GAAG,EAAE;IACzB,MAAMC,oBAAoB,GAAGC,KAAK,CAACC,IAAI,CAACjB,eAAe,CAACE,MAAM,CAAC,CAAC,CAAC,CAACgB,IAAI,CAAC,CAACC,QAAQ,EAAEC,QAAQ,KAAK;MAC3F,MAAMC,MAAM,GAAG,IAAIjG,WAAW,CAAC+F,QAAQ,CAACG,MAAM,CAACC,KAAK,CAACC,eAAe,EAAEL,QAAQ,CAACG,MAAM,CAACC,KAAK,CAACE,aAAa,CAAC;MAC1G,MAAMC,MAAM,GAAG,IAAItG,WAAW,CAACgG,QAAQ,CAACE,MAAM,CAACC,KAAK,CAACC,eAAe,EAAEJ,QAAQ,CAACE,MAAM,CAACC,KAAK,CAACE,aAAa,CAAC;MAC1G,OAAO,IAAI,CAACE,WAAW,CAACN,MAAM,EAAEK,MAAM,CAAC;IAC3C,CAAC,CAAC;IACF,KAAK,MAAME,cAAc,IAAIb,oBAAoB,EAAE;MAC/CD,cAAc,CAACvE,IAAI,CAAC,IAAI,CAACsF,8BAA8B,CAACD,cAAc,EAAEA,cAAc,CAACN,MAAM,CAACQ,cAAc,CAACN,eAAe,CAAC,CAAC;IAClI;IACA,MAAMnC,oBAAoB,GAAG,IAAInE,aAAa,CAAC6D,SAAS,EAAE+B,cAAc,EAAE/B,SAAS,CAAC;IACpF,OAAO;MACHM,oBAAoB,EAAEA,oBAAoB;MAC1CC,UAAU,EAAES;IAChB,CAAC;EACL;EACA8B,8BAA8BA,CAACD,cAAc,EAAEG,iBAAiB,EAAE;IAC9D,MAAMnC,QAAQ,GAAG,EAAE;IACnB,KAAK,MAAMoC,KAAK,IAAIJ,cAAc,CAAChC,QAAQ,CAACM,MAAM,CAAC,CAAC,EAAE;MAClD,IAAI8B,KAAK,CAACV,MAAM,CAACQ,cAAc,CAACN,eAAe,KAAKQ,KAAK,CAACV,MAAM,CAACC,KAAK,CAACE,aAAa,EAAE;QAClF,IAAIO,KAAK,CAACV,MAAM,CAACQ,cAAc,CAACN,eAAe,KAAKO,iBAAiB,EAAE;UACnEnC,QAAQ,CAACrD,IAAI,CAAC,IAAI,CAACsF,8BAA8B,CAACG,KAAK,EAAEA,KAAK,CAACV,MAAM,CAACQ,cAAc,CAACN,eAAe,CAAC,CAAC;QAC1G,CAAC,MACI;UACD,KAAK,MAAMS,QAAQ,IAAID,KAAK,CAACpC,QAAQ,CAACM,MAAM,CAAC,CAAC,EAAE;YAC5CN,QAAQ,CAACrD,IAAI,CAAC,IAAI,CAACsF,8BAA8B,CAACI,QAAQ,EAAED,KAAK,CAACV,MAAM,CAACQ,cAAc,CAACN,eAAe,CAAC,CAAC;UAC7G;QACJ;MACJ;IACJ;IACA5B,QAAQ,CAACsB,IAAI,CAAC,CAACgB,MAAM,EAAEC,MAAM,KAAK,IAAI,CAACR,WAAW,CAACO,MAAM,CAACX,KAAK,EAAEY,MAAM,CAACZ,KAAK,CAAC,CAAC;IAC/E,MAAMA,KAAK,GAAG,IAAInG,WAAW,CAACwG,cAAc,CAACN,MAAM,CAACQ,cAAc,CAACN,eAAe,EAAEI,cAAc,CAACN,MAAM,CAACC,KAAK,CAACE,aAAa,CAAC;IAC9H,OAAO,IAAIvG,aAAa,CAACqG,KAAK,EAAE3B,QAAQ,EAAEb,SAAS,CAAC;EACxD;EACA4C,WAAWA,CAACN,MAAM,EAAEK,MAAM,EAAE;IACxB,IAAIL,MAAM,CAACG,eAAe,KAAKE,MAAM,CAACF,eAAe,EAAE;MACnD,OAAOH,MAAM,CAACG,eAAe,GAAGE,MAAM,CAACF,eAAe;IAC1D,CAAC,MACI;MACD,OAAOE,MAAM,CAACD,aAAa,GAAGJ,MAAM,CAACI,aAAa;IACtD;EACJ;EACAZ,uBAAuBA,CAACuB,OAAO,EAAE;IAC7B,IAAIC,GAAG,GAAG,CAAC;IACX,KAAK,MAAML,KAAK,IAAII,OAAO,CAACxC,QAAQ,CAACM,MAAM,CAAC,CAAC,EAAE;MAC3CmC,GAAG,IAAI,IAAI,CAACxB,uBAAuB,CAACmB,KAAK,CAAC;IAC9C;IACA,IAAII,OAAO,YAAY7H,cAAc,EAAE;MACnC,OAAO8H,GAAG,GAAGD,OAAO,CAACd,MAAM,CAACC,KAAK,CAACE,aAAa,GAAGW,OAAO,CAACd,MAAM,CAACQ,cAAc,CAACN,eAAe;IACnG,CAAC,MACI;MACD,OAAOa,GAAG;IACd;EACJ;AACJ,CAAC;AACD7F,uCAAuC,GAAGvD,UAAU,CAAC,CACjDgB,OAAO,CAAC,CAAC,EAAEK,wBAAwB,CAAC,CACvC,EAAEkC,uCAAuC,CAAC;AAC3C,MAAM8F,uCAAuC,SAASnE,4BAA4B,CAAC;EAC/EzC,WAAWA,CAAC6G,MAAM,EAAE;IAChB,KAAK,CAACA,MAAM,CAAC;IACb,IAAI,CAACC,qBAAqB,GAAG,IAAI3H,mBAAmB,CAAC0H,MAAM,CAAC;EAChE;EACAzD,iBAAiBA,CAAC1B,KAAK,EAAE6B,KAAK,EAAE;IAC5B,MAAMwD,cAAc,GAAG,IAAI,CAACC,mBAAmB,CAACzD,KAAK,CAAC;IACtD,MAAMQ,SAAS,GAAG,IAAI,CAAC9D,OAAO,CAACyD,QAAQ,CAAC,CAAC;IACzC,OAAO,IAAIjE,WAAW,CAACsE,SAAS,CAACC,GAAG,EAAED,SAAS,CAACE,YAAY,CAAC,CAAC,EAAE8C,cAAc,EAAE1D,SAAS,CAAC;EAC9F;EACAF,YAAYA,CAACI,KAAK,EAAE;IAChB,OAAOA,KAAK,KAAK,IAAI;EACzB;EACAyD,mBAAmBA,CAACC,cAAc,EAAE;IAChC,MAAMnJ,MAAM,GAAGmJ,cAAc,CAACnJ,MAAM;IACpC,MAAMoJ,qBAAqB,GAAG,EAAE;IAChC;IACA,MAAMvD,oBAAoB,GAAG,IAAInE,aAAa,CAAC6D,SAAS,EAAE,EAAE,EAAEA,SAAS,CAAC;IACxE,KAAK,IAAIhF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,MAAM,EAAEO,CAAC,EAAE,EAAE;MAC7B;MACA,MAAM8I,WAAW,GAAGF,cAAc,CAACG,cAAc,CAAC/I,CAAC,CAAC;MACpD,IAAIgJ,UAAU;MACd,IAAIF,WAAW,KAAK,CAAC,CAAC,EAAE;QACpB;QACAE,UAAU,GAAGH,qBAAqB,CAACC,WAAW,CAAC;MACnD,CAAC,MACI;QACD;QACAE,UAAU,GAAG1D,oBAAoB;MACrC;MACA,MAAM2C,KAAK,GAAG,IAAI9G,aAAa,CAAC,IAAIE,WAAW,CAACuH,cAAc,CAACK,kBAAkB,CAACjJ,CAAC,CAAC,EAAE4I,cAAc,CAACM,gBAAgB,CAAClJ,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAEgJ,UAAU,CAAC;MAC9IA,UAAU,CAACnD,QAAQ,CAACrD,IAAI,CAACyF,KAAK,CAAC;MAC/BY,qBAAqB,CAACrG,IAAI,CAACyF,KAAK,CAAC;IACrC;IACA,OAAO3C,oBAAoB;EAC/B;AACJ;AACA,IAAIzC,kDAAkD,GAAG,MAAMA,kDAAkD,SAAS0F,uCAAuC,CAAC;EAC9J5G,WAAWA,CAAC6G,MAAM,EAAE1G,6BAA6B,EAAE;IAC/C,KAAK,CAAC0G,MAAM,CAAC;IACb,IAAI,CAAC1G,6BAA6B,GAAGA,6BAA6B;IAClE,IAAI,CAACkB,QAAQ,GAAG,IAAI,CAACb,SAAS,CAAC,IAAInB,mBAAmB,CAACwH,MAAM,CAACnD,QAAQ,CAAC,CAAC,EAAE,IAAI,CAACvD,6BAA6B,EAAE,IAAI,CAAC2G,qBAAqB,CAAC,CAAC;EAC9I;EACM9D,uBAAuBA,CAACtB,KAAK,EAAE;IAAA,IAAA8F,MAAA;IAAA,OAAA5F,iBAAA;MACjC,OAAO4F,MAAI,CAACnG,QAAQ,CAACoG,OAAO,CAAC/F,KAAK,CAAC;IAAC;EACxC;AACJ,CAAC;AACDR,kDAAkD,GAAG3D,UAAU,CAAC,CAC5DgB,OAAO,CAAC,CAAC,EAAEe,6BAA6B,CAAC,CAC5C,EAAE4B,kDAAkD,CAAC;AACtD,IAAIF,6CAA6C,GAAG,MAAMA,6CAA6C,SAAS4F,uCAAuC,CAAC;EACpJ5G,WAAWA,CAAC6G,MAAM,EAAE3G,gBAAgB,EAAEE,wBAAwB,EAAE;IAC5D,KAAK,CAACyG,MAAM,CAAC;IACb,IAAI,CAACzG,wBAAwB,GAAGA,wBAAwB;IACxD,MAAMsH,iBAAiB,GAAGxI,iBAAiB,CAACyI,wBAAwB,CAAC,IAAI,CAACvH,wBAAwB,EAAEyG,MAAM,CAACnD,QAAQ,CAAC,CAAC,CAAC;IACtH,IAAIgE,iBAAiB,CAAC5J,MAAM,GAAG,CAAC,EAAE;MAC9B,IAAI,CAACuD,QAAQ,GAAG,IAAI,CAACb,SAAS,CAAC,IAAIpB,mBAAmB,CAACyH,MAAM,CAACnD,QAAQ,CAAC,CAAC,EAAEgE,iBAAiB,EAAExH,gBAAgB,EAAE,IAAI,CAAC4G,qBAAqB,EAAEzD,SAAS,CAAC,CAAC;IAC1J;EACJ;EACAP,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAACzB,QAAQ,KAAKgC,SAAS;EACtC;EACML,uBAAuBA,CAACtB,KAAK,EAAE;IAAA,IAAAkG,MAAA;IAAA,OAAAhG,iBAAA;MACjC,OAAOgG,MAAI,CAACvG,QAAQ,EAAEoG,OAAO,CAAC/F,KAAK,CAAC,IAAI,IAAI;IAAC;EACjD;AACJ,CAAC;AACDV,6CAA6C,GAAGzD,UAAU,CAAC,CACvDgB,OAAO,CAAC,CAAC,EAAEK,wBAAwB,CAAC,CACvC,EAAEoC,6CAA6C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}