{"ast": null, "code": "import { ArrayNavigator } from './navigator.js';\nexport class HistoryNavigator {\n  constructor(history = [], limit = 10) {\n    this._initialize(history);\n    this._limit = limit;\n    this._onChange();\n  }\n  getHistory() {\n    return this._elements;\n  }\n  add(t) {\n    this._history.delete(t);\n    this._history.add(t);\n    this._onChange();\n  }\n  next() {\n    // This will navigate past the end of the last element, and in that case the input should be cleared\n    return this._navigator.next();\n  }\n  previous() {\n    if (this._currentPosition() !== 0) {\n      return this._navigator.previous();\n    }\n    return null;\n  }\n  current() {\n    return this._navigator.current();\n  }\n  first() {\n    return this._navigator.first();\n  }\n  last() {\n    return this._navigator.last();\n  }\n  isLast() {\n    return this._currentPosition() >= this._elements.length - 1;\n  }\n  isNowhere() {\n    return this._navigator.current() === null;\n  }\n  has(t) {\n    return this._history.has(t);\n  }\n  _onChange() {\n    this._reduceToLimit();\n    const elements = this._elements;\n    this._navigator = new ArrayNavigator(elements, 0, elements.length, elements.length);\n  }\n  _reduceToLimit() {\n    const data = this._elements;\n    if (data.length > this._limit) {\n      this._initialize(data.slice(data.length - this._limit));\n    }\n  }\n  _currentPosition() {\n    const currentElement = this._navigator.current();\n    if (!currentElement) {\n      return -1;\n    }\n    return this._elements.indexOf(currentElement);\n  }\n  _initialize(history) {\n    this._history = new Set();\n    for (const entry of history) {\n      this._history.add(entry);\n    }\n  }\n  get _elements() {\n    const elements = [];\n    this._history.forEach(e => elements.push(e));\n    return elements;\n  }\n}", "map": {"version": 3, "names": ["ArrayNavigator", "HistoryNavigator", "constructor", "history", "limit", "_initialize", "_limit", "_onChange", "getHistory", "_elements", "add", "t", "_history", "delete", "next", "_navigator", "previous", "_currentPosition", "current", "first", "last", "isLast", "length", "isNowhere", "has", "_reduceToLimit", "elements", "data", "slice", "currentElement", "indexOf", "Set", "entry", "for<PERSON>ach", "e", "push"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/common/history.js"], "sourcesContent": ["import { ArrayNavigator } from './navigator.js';\nexport class HistoryNavigator {\n    constructor(history = [], limit = 10) {\n        this._initialize(history);\n        this._limit = limit;\n        this._onChange();\n    }\n    getHistory() {\n        return this._elements;\n    }\n    add(t) {\n        this._history.delete(t);\n        this._history.add(t);\n        this._onChange();\n    }\n    next() {\n        // This will navigate past the end of the last element, and in that case the input should be cleared\n        return this._navigator.next();\n    }\n    previous() {\n        if (this._currentPosition() !== 0) {\n            return this._navigator.previous();\n        }\n        return null;\n    }\n    current() {\n        return this._navigator.current();\n    }\n    first() {\n        return this._navigator.first();\n    }\n    last() {\n        return this._navigator.last();\n    }\n    isLast() {\n        return this._currentPosition() >= this._elements.length - 1;\n    }\n    isNowhere() {\n        return this._navigator.current() === null;\n    }\n    has(t) {\n        return this._history.has(t);\n    }\n    _onChange() {\n        this._reduceToLimit();\n        const elements = this._elements;\n        this._navigator = new ArrayNavigator(elements, 0, elements.length, elements.length);\n    }\n    _reduceToLimit() {\n        const data = this._elements;\n        if (data.length > this._limit) {\n            this._initialize(data.slice(data.length - this._limit));\n        }\n    }\n    _currentPosition() {\n        const currentElement = this._navigator.current();\n        if (!currentElement) {\n            return -1;\n        }\n        return this._elements.indexOf(currentElement);\n    }\n    _initialize(history) {\n        this._history = new Set();\n        for (const entry of history) {\n            this._history.add(entry);\n        }\n    }\n    get _elements() {\n        const elements = [];\n        this._history.forEach(e => elements.push(e));\n        return elements;\n    }\n}\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,gBAAgB;AAC/C,OAAO,MAAMC,gBAAgB,CAAC;EAC1BC,WAAWA,CAACC,OAAO,GAAG,EAAE,EAAEC,KAAK,GAAG,EAAE,EAAE;IAClC,IAAI,CAACC,WAAW,CAACF,OAAO,CAAC;IACzB,IAAI,CAACG,MAAM,GAAGF,KAAK;IACnB,IAAI,CAACG,SAAS,CAAC,CAAC;EACpB;EACAC,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAACC,SAAS;EACzB;EACAC,GAAGA,CAACC,CAAC,EAAE;IACH,IAAI,CAACC,QAAQ,CAACC,MAAM,CAACF,CAAC,CAAC;IACvB,IAAI,CAACC,QAAQ,CAACF,GAAG,CAACC,CAAC,CAAC;IACpB,IAAI,CAACJ,SAAS,CAAC,CAAC;EACpB;EACAO,IAAIA,CAAA,EAAG;IACH;IACA,OAAO,IAAI,CAACC,UAAU,CAACD,IAAI,CAAC,CAAC;EACjC;EACAE,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACC,gBAAgB,CAAC,CAAC,KAAK,CAAC,EAAE;MAC/B,OAAO,IAAI,CAACF,UAAU,CAACC,QAAQ,CAAC,CAAC;IACrC;IACA,OAAO,IAAI;EACf;EACAE,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACH,UAAU,CAACG,OAAO,CAAC,CAAC;EACpC;EACAC,KAAKA,CAAA,EAAG;IACJ,OAAO,IAAI,CAACJ,UAAU,CAACI,KAAK,CAAC,CAAC;EAClC;EACAC,IAAIA,CAAA,EAAG;IACH,OAAO,IAAI,CAACL,UAAU,CAACK,IAAI,CAAC,CAAC;EACjC;EACAC,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI,CAACJ,gBAAgB,CAAC,CAAC,IAAI,IAAI,CAACR,SAAS,CAACa,MAAM,GAAG,CAAC;EAC/D;EACAC,SAASA,CAAA,EAAG;IACR,OAAO,IAAI,CAACR,UAAU,CAACG,OAAO,CAAC,CAAC,KAAK,IAAI;EAC7C;EACAM,GAAGA,CAACb,CAAC,EAAE;IACH,OAAO,IAAI,CAACC,QAAQ,CAACY,GAAG,CAACb,CAAC,CAAC;EAC/B;EACAJ,SAASA,CAAA,EAAG;IACR,IAAI,CAACkB,cAAc,CAAC,CAAC;IACrB,MAAMC,QAAQ,GAAG,IAAI,CAACjB,SAAS;IAC/B,IAAI,CAACM,UAAU,GAAG,IAAIf,cAAc,CAAC0B,QAAQ,EAAE,CAAC,EAAEA,QAAQ,CAACJ,MAAM,EAAEI,QAAQ,CAACJ,MAAM,CAAC;EACvF;EACAG,cAAcA,CAAA,EAAG;IACb,MAAME,IAAI,GAAG,IAAI,CAAClB,SAAS;IAC3B,IAAIkB,IAAI,CAACL,MAAM,GAAG,IAAI,CAAChB,MAAM,EAAE;MAC3B,IAAI,CAACD,WAAW,CAACsB,IAAI,CAACC,KAAK,CAACD,IAAI,CAACL,MAAM,GAAG,IAAI,CAAChB,MAAM,CAAC,CAAC;IAC3D;EACJ;EACAW,gBAAgBA,CAAA,EAAG;IACf,MAAMY,cAAc,GAAG,IAAI,CAACd,UAAU,CAACG,OAAO,CAAC,CAAC;IAChD,IAAI,CAACW,cAAc,EAAE;MACjB,OAAO,CAAC,CAAC;IACb;IACA,OAAO,IAAI,CAACpB,SAAS,CAACqB,OAAO,CAACD,cAAc,CAAC;EACjD;EACAxB,WAAWA,CAACF,OAAO,EAAE;IACjB,IAAI,CAACS,QAAQ,GAAG,IAAImB,GAAG,CAAC,CAAC;IACzB,KAAK,MAAMC,KAAK,IAAI7B,OAAO,EAAE;MACzB,IAAI,CAACS,QAAQ,CAACF,GAAG,CAACsB,KAAK,CAAC;IAC5B;EACJ;EACA,IAAIvB,SAASA,CAAA,EAAG;IACZ,MAAMiB,QAAQ,GAAG,EAAE;IACnB,IAAI,CAACd,QAAQ,CAACqB,OAAO,CAACC,CAAC,IAAIR,QAAQ,CAACS,IAAI,CAACD,CAAC,CAAC,CAAC;IAC5C,OAAOR,QAAQ;EACnB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}