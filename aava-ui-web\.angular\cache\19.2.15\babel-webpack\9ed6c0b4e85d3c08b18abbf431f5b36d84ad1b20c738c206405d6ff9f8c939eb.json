{"ast": null, "code": "import _asyncToGenerator from \"C:/console/aava-ui-web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as DOM from '../../dom.js';\nimport { StandardKeyboardEvent } from '../../keyboardEvent.js';\nimport { ActionViewItem, BaseActionViewItem } from './actionViewItems.js';\nimport { createInstantHoverDelegate } from '../hover/hoverDelegateFactory.js';\nimport { ActionRunner, Separator } from '../../../common/actions.js';\nimport { Emitter } from '../../../common/event.js';\nimport { Disposable, DisposableMap, DisposableStore, dispose } from '../../../common/lifecycle.js';\nimport * as types from '../../../common/types.js';\nimport './actionbar.css';\nexport class ActionBar extends Disposable {\n  constructor(container, options = {}) {\n    super();\n    this._actionRunnerDisposables = this._register(new DisposableStore());\n    this.viewItemDisposables = this._register(new DisposableMap());\n    // Trigger Key Tracking\n    this.triggerKeyDown = false;\n    this.focusable = true;\n    this._onDidBlur = this._register(new Emitter());\n    this.onDidBlur = this._onDidBlur.event;\n    this._onDidCancel = this._register(new Emitter({\n      onWillAddFirstListener: () => this.cancelHasListener = true\n    }));\n    this.onDidCancel = this._onDidCancel.event;\n    this.cancelHasListener = false;\n    this._onDidRun = this._register(new Emitter());\n    this.onDidRun = this._onDidRun.event;\n    this._onWillRun = this._register(new Emitter());\n    this.onWillRun = this._onWillRun.event;\n    this.options = options;\n    this._context = options.context ?? null;\n    this._orientation = this.options.orientation ?? 0 /* ActionsOrientation.HORIZONTAL */;\n    this._triggerKeys = {\n      keyDown: this.options.triggerKeys?.keyDown ?? false,\n      keys: this.options.triggerKeys?.keys ?? [3 /* KeyCode.Enter */, 10 /* KeyCode.Space */]\n    };\n    this._hoverDelegate = options.hoverDelegate ?? this._register(createInstantHoverDelegate());\n    if (this.options.actionRunner) {\n      this._actionRunner = this.options.actionRunner;\n    } else {\n      this._actionRunner = new ActionRunner();\n      this._actionRunnerDisposables.add(this._actionRunner);\n    }\n    this._actionRunnerDisposables.add(this._actionRunner.onDidRun(e => this._onDidRun.fire(e)));\n    this._actionRunnerDisposables.add(this._actionRunner.onWillRun(e => this._onWillRun.fire(e)));\n    this.viewItems = [];\n    this.focusedItem = undefined;\n    this.domNode = document.createElement('div');\n    this.domNode.className = 'monaco-action-bar';\n    let previousKeys;\n    let nextKeys;\n    switch (this._orientation) {\n      case 0 /* ActionsOrientation.HORIZONTAL */:\n        previousKeys = [15 /* KeyCode.LeftArrow */];\n        nextKeys = [17 /* KeyCode.RightArrow */];\n        break;\n      case 1 /* ActionsOrientation.VERTICAL */:\n        previousKeys = [16 /* KeyCode.UpArrow */];\n        nextKeys = [18 /* KeyCode.DownArrow */];\n        this.domNode.className += ' vertical';\n        break;\n    }\n    this._register(DOM.addDisposableListener(this.domNode, DOM.EventType.KEY_DOWN, e => {\n      const event = new StandardKeyboardEvent(e);\n      let eventHandled = true;\n      const focusedItem = typeof this.focusedItem === 'number' ? this.viewItems[this.focusedItem] : undefined;\n      if (previousKeys && (event.equals(previousKeys[0]) || event.equals(previousKeys[1]))) {\n        eventHandled = this.focusPrevious();\n      } else if (nextKeys && (event.equals(nextKeys[0]) || event.equals(nextKeys[1]))) {\n        eventHandled = this.focusNext();\n      } else if (event.equals(9 /* KeyCode.Escape */) && this.cancelHasListener) {\n        this._onDidCancel.fire();\n      } else if (event.equals(14 /* KeyCode.Home */)) {\n        eventHandled = this.focusFirst();\n      } else if (event.equals(13 /* KeyCode.End */)) {\n        eventHandled = this.focusLast();\n      } else if (event.equals(2 /* KeyCode.Tab */) && focusedItem instanceof BaseActionViewItem && focusedItem.trapsArrowNavigation) {\n        // Tab, so forcibly focus next #219199\n        eventHandled = this.focusNext(undefined, true);\n      } else if (this.isTriggerKeyEvent(event)) {\n        // Staying out of the else branch even if not triggered\n        if (this._triggerKeys.keyDown) {\n          this.doTrigger(event);\n        } else {\n          this.triggerKeyDown = true;\n        }\n      } else {\n        eventHandled = false;\n      }\n      if (eventHandled) {\n        event.preventDefault();\n        event.stopPropagation();\n      }\n    }));\n    this._register(DOM.addDisposableListener(this.domNode, DOM.EventType.KEY_UP, e => {\n      const event = new StandardKeyboardEvent(e);\n      // Run action on Enter/Space\n      if (this.isTriggerKeyEvent(event)) {\n        if (!this._triggerKeys.keyDown && this.triggerKeyDown) {\n          this.triggerKeyDown = false;\n          this.doTrigger(event);\n        }\n        event.preventDefault();\n        event.stopPropagation();\n      }\n      // Recompute focused item\n      else if (event.equals(2 /* KeyCode.Tab */) || event.equals(1024 /* KeyMod.Shift */ | 2 /* KeyCode.Tab */) || event.equals(16 /* KeyCode.UpArrow */) || event.equals(18 /* KeyCode.DownArrow */) || event.equals(15 /* KeyCode.LeftArrow */) || event.equals(17 /* KeyCode.RightArrow */)) {\n        this.updateFocusedItem();\n      }\n    }));\n    this.focusTracker = this._register(DOM.trackFocus(this.domNode));\n    this._register(this.focusTracker.onDidBlur(() => {\n      if (DOM.getActiveElement() === this.domNode || !DOM.isAncestor(DOM.getActiveElement(), this.domNode)) {\n        this._onDidBlur.fire();\n        this.previouslyFocusedItem = this.focusedItem;\n        this.focusedItem = undefined;\n        this.triggerKeyDown = false;\n      }\n    }));\n    this._register(this.focusTracker.onDidFocus(() => this.updateFocusedItem()));\n    this.actionsList = document.createElement('ul');\n    this.actionsList.className = 'actions-container';\n    if (this.options.highlightToggledItems) {\n      this.actionsList.classList.add('highlight-toggled');\n    }\n    this.actionsList.setAttribute('role', this.options.ariaRole || 'toolbar');\n    if (this.options.ariaLabel) {\n      this.actionsList.setAttribute('aria-label', this.options.ariaLabel);\n    }\n    this.domNode.appendChild(this.actionsList);\n    container.appendChild(this.domNode);\n  }\n  refreshRole() {\n    if (this.length() >= 1) {\n      this.actionsList.setAttribute('role', this.options.ariaRole || 'toolbar');\n    } else {\n      this.actionsList.setAttribute('role', 'presentation');\n    }\n  }\n  // Some action bars should not be focusable at times\n  // When an action bar is not focusable make sure to make all the elements inside it not focusable\n  // When an action bar is focusable again, make sure the first item can be focused\n  setFocusable(focusable) {\n    this.focusable = focusable;\n    if (this.focusable) {\n      const firstEnabled = this.viewItems.find(vi => vi instanceof BaseActionViewItem && vi.isEnabled());\n      if (firstEnabled instanceof BaseActionViewItem) {\n        firstEnabled.setFocusable(true);\n      }\n    } else {\n      this.viewItems.forEach(vi => {\n        if (vi instanceof BaseActionViewItem) {\n          vi.setFocusable(false);\n        }\n      });\n    }\n  }\n  isTriggerKeyEvent(event) {\n    let ret = false;\n    this._triggerKeys.keys.forEach(keyCode => {\n      ret = ret || event.equals(keyCode);\n    });\n    return ret;\n  }\n  updateFocusedItem() {\n    for (let i = 0; i < this.actionsList.children.length; i++) {\n      const elem = this.actionsList.children[i];\n      if (DOM.isAncestor(DOM.getActiveElement(), elem)) {\n        this.focusedItem = i;\n        this.viewItems[this.focusedItem]?.showHover?.();\n        break;\n      }\n    }\n  }\n  get context() {\n    return this._context;\n  }\n  set context(context) {\n    this._context = context;\n    this.viewItems.forEach(i => i.setActionContext(context));\n  }\n  get actionRunner() {\n    return this._actionRunner;\n  }\n  set actionRunner(actionRunner) {\n    this._actionRunner = actionRunner;\n    // when setting a new `IActionRunner` make sure to dispose old listeners and\n    // start to forward events from the new listener\n    this._actionRunnerDisposables.clear();\n    this._actionRunnerDisposables.add(this._actionRunner.onDidRun(e => this._onDidRun.fire(e)));\n    this._actionRunnerDisposables.add(this._actionRunner.onWillRun(e => this._onWillRun.fire(e)));\n    this.viewItems.forEach(item => item.actionRunner = actionRunner);\n  }\n  getContainer() {\n    return this.domNode;\n  }\n  getAction(indexOrElement) {\n    // by index\n    if (typeof indexOrElement === 'number') {\n      return this.viewItems[indexOrElement]?.action;\n    }\n    // by element\n    if (DOM.isHTMLElement(indexOrElement)) {\n      while (indexOrElement.parentElement !== this.actionsList) {\n        if (!indexOrElement.parentElement) {\n          return undefined;\n        }\n        indexOrElement = indexOrElement.parentElement;\n      }\n      for (let i = 0; i < this.actionsList.childNodes.length; i++) {\n        if (this.actionsList.childNodes[i] === indexOrElement) {\n          return this.viewItems[i].action;\n        }\n      }\n    }\n    return undefined;\n  }\n  push(arg, options = {}) {\n    const actions = Array.isArray(arg) ? arg : [arg];\n    let index = types.isNumber(options.index) ? options.index : null;\n    actions.forEach(action => {\n      const actionViewItemElement = document.createElement('li');\n      actionViewItemElement.className = 'action-item';\n      actionViewItemElement.setAttribute('role', 'presentation');\n      let item;\n      const viewItemOptions = {\n        hoverDelegate: this._hoverDelegate,\n        ...options,\n        isTabList: this.options.ariaRole === 'tablist'\n      };\n      if (this.options.actionViewItemProvider) {\n        item = this.options.actionViewItemProvider(action, viewItemOptions);\n      }\n      if (!item) {\n        item = new ActionViewItem(this.context, action, viewItemOptions);\n      }\n      // Prevent native context menu on actions\n      if (!this.options.allowContextMenu) {\n        this.viewItemDisposables.set(item, DOM.addDisposableListener(actionViewItemElement, DOM.EventType.CONTEXT_MENU, e => {\n          DOM.EventHelper.stop(e, true);\n        }));\n      }\n      item.actionRunner = this._actionRunner;\n      item.setActionContext(this.context);\n      item.render(actionViewItemElement);\n      if (this.focusable && item instanceof BaseActionViewItem && this.viewItems.length === 0) {\n        // We need to allow for the first enabled item to be focused on using tab navigation #106441\n        item.setFocusable(true);\n      }\n      if (index === null || index < 0 || index >= this.actionsList.children.length) {\n        this.actionsList.appendChild(actionViewItemElement);\n        this.viewItems.push(item);\n      } else {\n        this.actionsList.insertBefore(actionViewItemElement, this.actionsList.children[index]);\n        this.viewItems.splice(index, 0, item);\n        index++;\n      }\n    });\n    if (typeof this.focusedItem === 'number') {\n      // After a clear actions might be re-added to simply toggle some actions. We should preserve focus #97128\n      this.focus(this.focusedItem);\n    }\n    this.refreshRole();\n  }\n  clear() {\n    if (this.isEmpty()) {\n      return;\n    }\n    this.viewItems = dispose(this.viewItems);\n    this.viewItemDisposables.clearAndDisposeAll();\n    DOM.clearNode(this.actionsList);\n    this.refreshRole();\n  }\n  length() {\n    return this.viewItems.length;\n  }\n  isEmpty() {\n    return this.viewItems.length === 0;\n  }\n  focus(arg) {\n    let selectFirst = false;\n    let index = undefined;\n    if (arg === undefined) {\n      selectFirst = true;\n    } else if (typeof arg === 'number') {\n      index = arg;\n    } else if (typeof arg === 'boolean') {\n      selectFirst = arg;\n    }\n    if (selectFirst && typeof this.focusedItem === 'undefined') {\n      const firstEnabled = this.viewItems.findIndex(item => item.isEnabled());\n      // Focus the first enabled item\n      this.focusedItem = firstEnabled === -1 ? undefined : firstEnabled;\n      this.updateFocus(undefined, undefined, true);\n    } else {\n      if (index !== undefined) {\n        this.focusedItem = index;\n      }\n      this.updateFocus(undefined, undefined, true);\n    }\n  }\n  focusFirst() {\n    this.focusedItem = this.length() - 1;\n    return this.focusNext(true);\n  }\n  focusLast() {\n    this.focusedItem = 0;\n    return this.focusPrevious(true);\n  }\n  focusNext(forceLoop, forceFocus) {\n    if (typeof this.focusedItem === 'undefined') {\n      this.focusedItem = this.viewItems.length - 1;\n    } else if (this.viewItems.length <= 1) {\n      return false;\n    }\n    const startIndex = this.focusedItem;\n    let item;\n    do {\n      if (!forceLoop && this.options.preventLoopNavigation && this.focusedItem + 1 >= this.viewItems.length) {\n        this.focusedItem = startIndex;\n        return false;\n      }\n      this.focusedItem = (this.focusedItem + 1) % this.viewItems.length;\n      item = this.viewItems[this.focusedItem];\n    } while (this.focusedItem !== startIndex && (this.options.focusOnlyEnabledItems && !item.isEnabled() || item.action.id === Separator.ID));\n    this.updateFocus(undefined, undefined, forceFocus);\n    return true;\n  }\n  focusPrevious(forceLoop) {\n    if (typeof this.focusedItem === 'undefined') {\n      this.focusedItem = 0;\n    } else if (this.viewItems.length <= 1) {\n      return false;\n    }\n    const startIndex = this.focusedItem;\n    let item;\n    do {\n      this.focusedItem = this.focusedItem - 1;\n      if (this.focusedItem < 0) {\n        if (!forceLoop && this.options.preventLoopNavigation) {\n          this.focusedItem = startIndex;\n          return false;\n        }\n        this.focusedItem = this.viewItems.length - 1;\n      }\n      item = this.viewItems[this.focusedItem];\n    } while (this.focusedItem !== startIndex && (this.options.focusOnlyEnabledItems && !item.isEnabled() || item.action.id === Separator.ID));\n    this.updateFocus(true);\n    return true;\n  }\n  updateFocus(fromRight, preventScroll, forceFocus = false) {\n    if (typeof this.focusedItem === 'undefined') {\n      this.actionsList.focus({\n        preventScroll\n      });\n    }\n    if (this.previouslyFocusedItem !== undefined && this.previouslyFocusedItem !== this.focusedItem) {\n      this.viewItems[this.previouslyFocusedItem]?.blur();\n    }\n    const actionViewItem = this.focusedItem !== undefined ? this.viewItems[this.focusedItem] : undefined;\n    if (actionViewItem) {\n      let focusItem = true;\n      if (!types.isFunction(actionViewItem.focus)) {\n        focusItem = false;\n      }\n      if (this.options.focusOnlyEnabledItems && types.isFunction(actionViewItem.isEnabled) && !actionViewItem.isEnabled()) {\n        focusItem = false;\n      }\n      if (actionViewItem.action.id === Separator.ID) {\n        focusItem = false;\n      }\n      if (!focusItem) {\n        this.actionsList.focus({\n          preventScroll\n        });\n        this.previouslyFocusedItem = undefined;\n      } else if (forceFocus || this.previouslyFocusedItem !== this.focusedItem) {\n        actionViewItem.focus(fromRight);\n        this.previouslyFocusedItem = this.focusedItem;\n      }\n      if (focusItem) {\n        actionViewItem.showHover?.();\n      }\n    }\n  }\n  doTrigger(event) {\n    if (typeof this.focusedItem === 'undefined') {\n      return; //nothing to focus\n    }\n    // trigger action\n    const actionViewItem = this.viewItems[this.focusedItem];\n    if (actionViewItem instanceof BaseActionViewItem) {\n      const context = actionViewItem._context === null || actionViewItem._context === undefined ? event : actionViewItem._context;\n      this.run(actionViewItem._action, context);\n    }\n  }\n  run(action, context) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      yield _this._actionRunner.run(action, context);\n    })();\n  }\n  dispose() {\n    this._context = undefined;\n    this.viewItems = dispose(this.viewItems);\n    this.getContainer().remove();\n    super.dispose();\n  }\n}", "map": {"version": 3, "names": ["DOM", "StandardKeyboardEvent", "ActionViewItem", "BaseActionViewItem", "createInstantHoverDelegate", "ActionRunner", "Separator", "Emitter", "Disposable", "DisposableMap", "DisposableStore", "dispose", "types", "ActionBar", "constructor", "container", "options", "_actionRunnerDisposables", "_register", "viewItemDisposables", "triggerKeyDown", "focusable", "_onDidBlur", "onDidBlur", "event", "_onDidCancel", "onWillAddFirstListener", "cancelHasListener", "onDidCancel", "_onDidRun", "onDidRun", "_onWillRun", "onWillRun", "_context", "context", "_orientation", "orientation", "_triggerKeys", "keyDown", "triggerKeys", "keys", "_hoverDelegate", "hoverDelegate", "actionRunner", "_actionRunner", "add", "e", "fire", "viewItems", "focusedItem", "undefined", "domNode", "document", "createElement", "className", "previousKeys", "nextKeys", "addDisposableListener", "EventType", "KEY_DOWN", "eventHandled", "equals", "focusPrevious", "focusNext", "focusFirst", "focusLast", "trapsArrowNavigation", "isTriggerKeyEvent", "doTrigger", "preventDefault", "stopPropagation", "KEY_UP", "updateFocusedItem", "focusTracker", "trackFocus", "getActiveElement", "isAncestor", "previouslyFocusedItem", "onDidFocus", "actionsList", "highlightToggledItems", "classList", "setAttribute", "ariaRole", "aria<PERSON><PERSON><PERSON>", "append<PERSON><PERSON><PERSON>", "refreshRole", "length", "setFocusable", "firstEnabled", "find", "vi", "isEnabled", "for<PERSON>ach", "ret", "keyCode", "i", "children", "elem", "showHover", "setActionContext", "clear", "item", "getContainer", "getAction", "indexOrElement", "action", "isHTMLElement", "parentElement", "childNodes", "push", "arg", "actions", "Array", "isArray", "index", "isNumber", "actionViewItemElement", "viewItemOptions", "isTabList", "actionViewItemProvider", "allowContextMenu", "set", "CONTEXT_MENU", "EventHelper", "stop", "render", "insertBefore", "splice", "focus", "isEmpty", "clearAndDisposeAll", "clearNode", "selectFirst", "findIndex", "updateFocus", "forceLoop", "forceFocus", "startIndex", "preventLoopNavigation", "focusOnlyEnabledItems", "id", "ID", "fromRight", "preventScroll", "blur", "actionViewItem", "focusItem", "isFunction", "run", "_action", "_this", "_asyncToGenerator", "remove"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/browser/ui/actionbar/actionbar.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as DOM from '../../dom.js';\nimport { StandardKeyboardEvent } from '../../keyboardEvent.js';\nimport { ActionViewItem, BaseActionViewItem } from './actionViewItems.js';\nimport { createInstantHoverDelegate } from '../hover/hoverDelegateFactory.js';\nimport { <PERSON><PERSON><PERSON><PERSON>, Separator } from '../../../common/actions.js';\nimport { Emitter } from '../../../common/event.js';\nimport { Disposable, DisposableMap, DisposableStore, dispose } from '../../../common/lifecycle.js';\nimport * as types from '../../../common/types.js';\nimport './actionbar.css';\nexport class ActionBar extends Disposable {\n    constructor(container, options = {}) {\n        super();\n        this._actionRunnerDisposables = this._register(new DisposableStore());\n        this.viewItemDisposables = this._register(new DisposableMap());\n        // Trigger Key Tracking\n        this.triggerKeyDown = false;\n        this.focusable = true;\n        this._onDidBlur = this._register(new Emitter());\n        this.onDidBlur = this._onDidBlur.event;\n        this._onDidCancel = this._register(new Emitter({ onWillAddFirstListener: () => this.cancelHasListener = true }));\n        this.onDidCancel = this._onDidCancel.event;\n        this.cancelHasListener = false;\n        this._onDidRun = this._register(new Emitter());\n        this.onDidRun = this._onDidRun.event;\n        this._onWillRun = this._register(new Emitter());\n        this.onWillRun = this._onWillRun.event;\n        this.options = options;\n        this._context = options.context ?? null;\n        this._orientation = this.options.orientation ?? 0 /* ActionsOrientation.HORIZONTAL */;\n        this._triggerKeys = {\n            keyDown: this.options.triggerKeys?.keyDown ?? false,\n            keys: this.options.triggerKeys?.keys ?? [3 /* KeyCode.Enter */, 10 /* KeyCode.Space */]\n        };\n        this._hoverDelegate = options.hoverDelegate ?? this._register(createInstantHoverDelegate());\n        if (this.options.actionRunner) {\n            this._actionRunner = this.options.actionRunner;\n        }\n        else {\n            this._actionRunner = new ActionRunner();\n            this._actionRunnerDisposables.add(this._actionRunner);\n        }\n        this._actionRunnerDisposables.add(this._actionRunner.onDidRun(e => this._onDidRun.fire(e)));\n        this._actionRunnerDisposables.add(this._actionRunner.onWillRun(e => this._onWillRun.fire(e)));\n        this.viewItems = [];\n        this.focusedItem = undefined;\n        this.domNode = document.createElement('div');\n        this.domNode.className = 'monaco-action-bar';\n        let previousKeys;\n        let nextKeys;\n        switch (this._orientation) {\n            case 0 /* ActionsOrientation.HORIZONTAL */:\n                previousKeys = [15 /* KeyCode.LeftArrow */];\n                nextKeys = [17 /* KeyCode.RightArrow */];\n                break;\n            case 1 /* ActionsOrientation.VERTICAL */:\n                previousKeys = [16 /* KeyCode.UpArrow */];\n                nextKeys = [18 /* KeyCode.DownArrow */];\n                this.domNode.className += ' vertical';\n                break;\n        }\n        this._register(DOM.addDisposableListener(this.domNode, DOM.EventType.KEY_DOWN, e => {\n            const event = new StandardKeyboardEvent(e);\n            let eventHandled = true;\n            const focusedItem = typeof this.focusedItem === 'number' ? this.viewItems[this.focusedItem] : undefined;\n            if (previousKeys && (event.equals(previousKeys[0]) || event.equals(previousKeys[1]))) {\n                eventHandled = this.focusPrevious();\n            }\n            else if (nextKeys && (event.equals(nextKeys[0]) || event.equals(nextKeys[1]))) {\n                eventHandled = this.focusNext();\n            }\n            else if (event.equals(9 /* KeyCode.Escape */) && this.cancelHasListener) {\n                this._onDidCancel.fire();\n            }\n            else if (event.equals(14 /* KeyCode.Home */)) {\n                eventHandled = this.focusFirst();\n            }\n            else if (event.equals(13 /* KeyCode.End */)) {\n                eventHandled = this.focusLast();\n            }\n            else if (event.equals(2 /* KeyCode.Tab */) && focusedItem instanceof BaseActionViewItem && focusedItem.trapsArrowNavigation) {\n                // Tab, so forcibly focus next #219199\n                eventHandled = this.focusNext(undefined, true);\n            }\n            else if (this.isTriggerKeyEvent(event)) {\n                // Staying out of the else branch even if not triggered\n                if (this._triggerKeys.keyDown) {\n                    this.doTrigger(event);\n                }\n                else {\n                    this.triggerKeyDown = true;\n                }\n            }\n            else {\n                eventHandled = false;\n            }\n            if (eventHandled) {\n                event.preventDefault();\n                event.stopPropagation();\n            }\n        }));\n        this._register(DOM.addDisposableListener(this.domNode, DOM.EventType.KEY_UP, e => {\n            const event = new StandardKeyboardEvent(e);\n            // Run action on Enter/Space\n            if (this.isTriggerKeyEvent(event)) {\n                if (!this._triggerKeys.keyDown && this.triggerKeyDown) {\n                    this.triggerKeyDown = false;\n                    this.doTrigger(event);\n                }\n                event.preventDefault();\n                event.stopPropagation();\n            }\n            // Recompute focused item\n            else if (event.equals(2 /* KeyCode.Tab */) || event.equals(1024 /* KeyMod.Shift */ | 2 /* KeyCode.Tab */) || event.equals(16 /* KeyCode.UpArrow */) || event.equals(18 /* KeyCode.DownArrow */) || event.equals(15 /* KeyCode.LeftArrow */) || event.equals(17 /* KeyCode.RightArrow */)) {\n                this.updateFocusedItem();\n            }\n        }));\n        this.focusTracker = this._register(DOM.trackFocus(this.domNode));\n        this._register(this.focusTracker.onDidBlur(() => {\n            if (DOM.getActiveElement() === this.domNode || !DOM.isAncestor(DOM.getActiveElement(), this.domNode)) {\n                this._onDidBlur.fire();\n                this.previouslyFocusedItem = this.focusedItem;\n                this.focusedItem = undefined;\n                this.triggerKeyDown = false;\n            }\n        }));\n        this._register(this.focusTracker.onDidFocus(() => this.updateFocusedItem()));\n        this.actionsList = document.createElement('ul');\n        this.actionsList.className = 'actions-container';\n        if (this.options.highlightToggledItems) {\n            this.actionsList.classList.add('highlight-toggled');\n        }\n        this.actionsList.setAttribute('role', this.options.ariaRole || 'toolbar');\n        if (this.options.ariaLabel) {\n            this.actionsList.setAttribute('aria-label', this.options.ariaLabel);\n        }\n        this.domNode.appendChild(this.actionsList);\n        container.appendChild(this.domNode);\n    }\n    refreshRole() {\n        if (this.length() >= 1) {\n            this.actionsList.setAttribute('role', this.options.ariaRole || 'toolbar');\n        }\n        else {\n            this.actionsList.setAttribute('role', 'presentation');\n        }\n    }\n    // Some action bars should not be focusable at times\n    // When an action bar is not focusable make sure to make all the elements inside it not focusable\n    // When an action bar is focusable again, make sure the first item can be focused\n    setFocusable(focusable) {\n        this.focusable = focusable;\n        if (this.focusable) {\n            const firstEnabled = this.viewItems.find(vi => vi instanceof BaseActionViewItem && vi.isEnabled());\n            if (firstEnabled instanceof BaseActionViewItem) {\n                firstEnabled.setFocusable(true);\n            }\n        }\n        else {\n            this.viewItems.forEach(vi => {\n                if (vi instanceof BaseActionViewItem) {\n                    vi.setFocusable(false);\n                }\n            });\n        }\n    }\n    isTriggerKeyEvent(event) {\n        let ret = false;\n        this._triggerKeys.keys.forEach(keyCode => {\n            ret = ret || event.equals(keyCode);\n        });\n        return ret;\n    }\n    updateFocusedItem() {\n        for (let i = 0; i < this.actionsList.children.length; i++) {\n            const elem = this.actionsList.children[i];\n            if (DOM.isAncestor(DOM.getActiveElement(), elem)) {\n                this.focusedItem = i;\n                this.viewItems[this.focusedItem]?.showHover?.();\n                break;\n            }\n        }\n    }\n    get context() {\n        return this._context;\n    }\n    set context(context) {\n        this._context = context;\n        this.viewItems.forEach(i => i.setActionContext(context));\n    }\n    get actionRunner() {\n        return this._actionRunner;\n    }\n    set actionRunner(actionRunner) {\n        this._actionRunner = actionRunner;\n        // when setting a new `IActionRunner` make sure to dispose old listeners and\n        // start to forward events from the new listener\n        this._actionRunnerDisposables.clear();\n        this._actionRunnerDisposables.add(this._actionRunner.onDidRun(e => this._onDidRun.fire(e)));\n        this._actionRunnerDisposables.add(this._actionRunner.onWillRun(e => this._onWillRun.fire(e)));\n        this.viewItems.forEach(item => item.actionRunner = actionRunner);\n    }\n    getContainer() {\n        return this.domNode;\n    }\n    getAction(indexOrElement) {\n        // by index\n        if (typeof indexOrElement === 'number') {\n            return this.viewItems[indexOrElement]?.action;\n        }\n        // by element\n        if (DOM.isHTMLElement(indexOrElement)) {\n            while (indexOrElement.parentElement !== this.actionsList) {\n                if (!indexOrElement.parentElement) {\n                    return undefined;\n                }\n                indexOrElement = indexOrElement.parentElement;\n            }\n            for (let i = 0; i < this.actionsList.childNodes.length; i++) {\n                if (this.actionsList.childNodes[i] === indexOrElement) {\n                    return this.viewItems[i].action;\n                }\n            }\n        }\n        return undefined;\n    }\n    push(arg, options = {}) {\n        const actions = Array.isArray(arg) ? arg : [arg];\n        let index = types.isNumber(options.index) ? options.index : null;\n        actions.forEach((action) => {\n            const actionViewItemElement = document.createElement('li');\n            actionViewItemElement.className = 'action-item';\n            actionViewItemElement.setAttribute('role', 'presentation');\n            let item;\n            const viewItemOptions = { hoverDelegate: this._hoverDelegate, ...options, isTabList: this.options.ariaRole === 'tablist' };\n            if (this.options.actionViewItemProvider) {\n                item = this.options.actionViewItemProvider(action, viewItemOptions);\n            }\n            if (!item) {\n                item = new ActionViewItem(this.context, action, viewItemOptions);\n            }\n            // Prevent native context menu on actions\n            if (!this.options.allowContextMenu) {\n                this.viewItemDisposables.set(item, DOM.addDisposableListener(actionViewItemElement, DOM.EventType.CONTEXT_MENU, (e) => {\n                    DOM.EventHelper.stop(e, true);\n                }));\n            }\n            item.actionRunner = this._actionRunner;\n            item.setActionContext(this.context);\n            item.render(actionViewItemElement);\n            if (this.focusable && item instanceof BaseActionViewItem && this.viewItems.length === 0) {\n                // We need to allow for the first enabled item to be focused on using tab navigation #106441\n                item.setFocusable(true);\n            }\n            if (index === null || index < 0 || index >= this.actionsList.children.length) {\n                this.actionsList.appendChild(actionViewItemElement);\n                this.viewItems.push(item);\n            }\n            else {\n                this.actionsList.insertBefore(actionViewItemElement, this.actionsList.children[index]);\n                this.viewItems.splice(index, 0, item);\n                index++;\n            }\n        });\n        if (typeof this.focusedItem === 'number') {\n            // After a clear actions might be re-added to simply toggle some actions. We should preserve focus #97128\n            this.focus(this.focusedItem);\n        }\n        this.refreshRole();\n    }\n    clear() {\n        if (this.isEmpty()) {\n            return;\n        }\n        this.viewItems = dispose(this.viewItems);\n        this.viewItemDisposables.clearAndDisposeAll();\n        DOM.clearNode(this.actionsList);\n        this.refreshRole();\n    }\n    length() {\n        return this.viewItems.length;\n    }\n    isEmpty() {\n        return this.viewItems.length === 0;\n    }\n    focus(arg) {\n        let selectFirst = false;\n        let index = undefined;\n        if (arg === undefined) {\n            selectFirst = true;\n        }\n        else if (typeof arg === 'number') {\n            index = arg;\n        }\n        else if (typeof arg === 'boolean') {\n            selectFirst = arg;\n        }\n        if (selectFirst && typeof this.focusedItem === 'undefined') {\n            const firstEnabled = this.viewItems.findIndex(item => item.isEnabled());\n            // Focus the first enabled item\n            this.focusedItem = firstEnabled === -1 ? undefined : firstEnabled;\n            this.updateFocus(undefined, undefined, true);\n        }\n        else {\n            if (index !== undefined) {\n                this.focusedItem = index;\n            }\n            this.updateFocus(undefined, undefined, true);\n        }\n    }\n    focusFirst() {\n        this.focusedItem = this.length() - 1;\n        return this.focusNext(true);\n    }\n    focusLast() {\n        this.focusedItem = 0;\n        return this.focusPrevious(true);\n    }\n    focusNext(forceLoop, forceFocus) {\n        if (typeof this.focusedItem === 'undefined') {\n            this.focusedItem = this.viewItems.length - 1;\n        }\n        else if (this.viewItems.length <= 1) {\n            return false;\n        }\n        const startIndex = this.focusedItem;\n        let item;\n        do {\n            if (!forceLoop && this.options.preventLoopNavigation && this.focusedItem + 1 >= this.viewItems.length) {\n                this.focusedItem = startIndex;\n                return false;\n            }\n            this.focusedItem = (this.focusedItem + 1) % this.viewItems.length;\n            item = this.viewItems[this.focusedItem];\n        } while (this.focusedItem !== startIndex && ((this.options.focusOnlyEnabledItems && !item.isEnabled()) || item.action.id === Separator.ID));\n        this.updateFocus(undefined, undefined, forceFocus);\n        return true;\n    }\n    focusPrevious(forceLoop) {\n        if (typeof this.focusedItem === 'undefined') {\n            this.focusedItem = 0;\n        }\n        else if (this.viewItems.length <= 1) {\n            return false;\n        }\n        const startIndex = this.focusedItem;\n        let item;\n        do {\n            this.focusedItem = this.focusedItem - 1;\n            if (this.focusedItem < 0) {\n                if (!forceLoop && this.options.preventLoopNavigation) {\n                    this.focusedItem = startIndex;\n                    return false;\n                }\n                this.focusedItem = this.viewItems.length - 1;\n            }\n            item = this.viewItems[this.focusedItem];\n        } while (this.focusedItem !== startIndex && ((this.options.focusOnlyEnabledItems && !item.isEnabled()) || item.action.id === Separator.ID));\n        this.updateFocus(true);\n        return true;\n    }\n    updateFocus(fromRight, preventScroll, forceFocus = false) {\n        if (typeof this.focusedItem === 'undefined') {\n            this.actionsList.focus({ preventScroll });\n        }\n        if (this.previouslyFocusedItem !== undefined && this.previouslyFocusedItem !== this.focusedItem) {\n            this.viewItems[this.previouslyFocusedItem]?.blur();\n        }\n        const actionViewItem = this.focusedItem !== undefined ? this.viewItems[this.focusedItem] : undefined;\n        if (actionViewItem) {\n            let focusItem = true;\n            if (!types.isFunction(actionViewItem.focus)) {\n                focusItem = false;\n            }\n            if (this.options.focusOnlyEnabledItems && types.isFunction(actionViewItem.isEnabled) && !actionViewItem.isEnabled()) {\n                focusItem = false;\n            }\n            if (actionViewItem.action.id === Separator.ID) {\n                focusItem = false;\n            }\n            if (!focusItem) {\n                this.actionsList.focus({ preventScroll });\n                this.previouslyFocusedItem = undefined;\n            }\n            else if (forceFocus || this.previouslyFocusedItem !== this.focusedItem) {\n                actionViewItem.focus(fromRight);\n                this.previouslyFocusedItem = this.focusedItem;\n            }\n            if (focusItem) {\n                actionViewItem.showHover?.();\n            }\n        }\n    }\n    doTrigger(event) {\n        if (typeof this.focusedItem === 'undefined') {\n            return; //nothing to focus\n        }\n        // trigger action\n        const actionViewItem = this.viewItems[this.focusedItem];\n        if (actionViewItem instanceof BaseActionViewItem) {\n            const context = (actionViewItem._context === null || actionViewItem._context === undefined) ? event : actionViewItem._context;\n            this.run(actionViewItem._action, context);\n        }\n    }\n    async run(action, context) {\n        await this._actionRunner.run(action, context);\n    }\n    dispose() {\n        this._context = undefined;\n        this.viewItems = dispose(this.viewItems);\n        this.getContainer().remove();\n        super.dispose();\n    }\n}\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA,OAAO,KAAKA,GAAG,MAAM,cAAc;AACnC,SAASC,qBAAqB,QAAQ,wBAAwB;AAC9D,SAASC,cAAc,EAAEC,kBAAkB,QAAQ,sBAAsB;AACzE,SAASC,0BAA0B,QAAQ,kCAAkC;AAC7E,SAASC,YAAY,EAAEC,SAAS,QAAQ,4BAA4B;AACpE,SAASC,OAAO,QAAQ,0BAA0B;AAClD,SAASC,UAAU,EAAEC,aAAa,EAAEC,eAAe,EAAEC,OAAO,QAAQ,8BAA8B;AAClG,OAAO,KAAKC,KAAK,MAAM,0BAA0B;AACjD,OAAO,iBAAiB;AACxB,OAAO,MAAMC,SAAS,SAASL,UAAU,CAAC;EACtCM,WAAWA,CAACC,SAAS,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IACjC,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,wBAAwB,GAAG,IAAI,CAACC,SAAS,CAAC,IAAIR,eAAe,CAAC,CAAC,CAAC;IACrE,IAAI,CAACS,mBAAmB,GAAG,IAAI,CAACD,SAAS,CAAC,IAAIT,aAAa,CAAC,CAAC,CAAC;IAC9D;IACA,IAAI,CAACW,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,UAAU,GAAG,IAAI,CAACJ,SAAS,CAAC,IAAIX,OAAO,CAAC,CAAC,CAAC;IAC/C,IAAI,CAACgB,SAAS,GAAG,IAAI,CAACD,UAAU,CAACE,KAAK;IACtC,IAAI,CAACC,YAAY,GAAG,IAAI,CAACP,SAAS,CAAC,IAAIX,OAAO,CAAC;MAAEmB,sBAAsB,EAAEA,CAAA,KAAM,IAAI,CAACC,iBAAiB,GAAG;IAAK,CAAC,CAAC,CAAC;IAChH,IAAI,CAACC,WAAW,GAAG,IAAI,CAACH,YAAY,CAACD,KAAK;IAC1C,IAAI,CAACG,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACE,SAAS,GAAG,IAAI,CAACX,SAAS,CAAC,IAAIX,OAAO,CAAC,CAAC,CAAC;IAC9C,IAAI,CAACuB,QAAQ,GAAG,IAAI,CAACD,SAAS,CAACL,KAAK;IACpC,IAAI,CAACO,UAAU,GAAG,IAAI,CAACb,SAAS,CAAC,IAAIX,OAAO,CAAC,CAAC,CAAC;IAC/C,IAAI,CAACyB,SAAS,GAAG,IAAI,CAACD,UAAU,CAACP,KAAK;IACtC,IAAI,CAACR,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACiB,QAAQ,GAAGjB,OAAO,CAACkB,OAAO,IAAI,IAAI;IACvC,IAAI,CAACC,YAAY,GAAG,IAAI,CAACnB,OAAO,CAACoB,WAAW,IAAI,CAAC,CAAC;IAClD,IAAI,CAACC,YAAY,GAAG;MAChBC,OAAO,EAAE,IAAI,CAACtB,OAAO,CAACuB,WAAW,EAAED,OAAO,IAAI,KAAK;MACnDE,IAAI,EAAE,IAAI,CAACxB,OAAO,CAACuB,WAAW,EAAEC,IAAI,IAAI,CAAC,CAAC,CAAC,qBAAqB,EAAE,CAAC;IACvE,CAAC;IACD,IAAI,CAACC,cAAc,GAAGzB,OAAO,CAAC0B,aAAa,IAAI,IAAI,CAACxB,SAAS,CAACd,0BAA0B,CAAC,CAAC,CAAC;IAC3F,IAAI,IAAI,CAACY,OAAO,CAAC2B,YAAY,EAAE;MAC3B,IAAI,CAACC,aAAa,GAAG,IAAI,CAAC5B,OAAO,CAAC2B,YAAY;IAClD,CAAC,MACI;MACD,IAAI,CAACC,aAAa,GAAG,IAAIvC,YAAY,CAAC,CAAC;MACvC,IAAI,CAACY,wBAAwB,CAAC4B,GAAG,CAAC,IAAI,CAACD,aAAa,CAAC;IACzD;IACA,IAAI,CAAC3B,wBAAwB,CAAC4B,GAAG,CAAC,IAAI,CAACD,aAAa,CAACd,QAAQ,CAACgB,CAAC,IAAI,IAAI,CAACjB,SAAS,CAACkB,IAAI,CAACD,CAAC,CAAC,CAAC,CAAC;IAC3F,IAAI,CAAC7B,wBAAwB,CAAC4B,GAAG,CAAC,IAAI,CAACD,aAAa,CAACZ,SAAS,CAACc,CAAC,IAAI,IAAI,CAACf,UAAU,CAACgB,IAAI,CAACD,CAAC,CAAC,CAAC,CAAC;IAC7F,IAAI,CAACE,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,WAAW,GAAGC,SAAS;IAC5B,IAAI,CAACC,OAAO,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC5C,IAAI,CAACF,OAAO,CAACG,SAAS,GAAG,mBAAmB;IAC5C,IAAIC,YAAY;IAChB,IAAIC,QAAQ;IACZ,QAAQ,IAAI,CAACrB,YAAY;MACrB,KAAK,CAAC,CAAC;QACHoB,YAAY,GAAG,CAAC,EAAE,CAAC,wBAAwB;QAC3CC,QAAQ,GAAG,CAAC,EAAE,CAAC,yBAAyB;QACxC;MACJ,KAAK,CAAC,CAAC;QACHD,YAAY,GAAG,CAAC,EAAE,CAAC,sBAAsB;QACzCC,QAAQ,GAAG,CAAC,EAAE,CAAC,wBAAwB;QACvC,IAAI,CAACL,OAAO,CAACG,SAAS,IAAI,WAAW;QACrC;IACR;IACA,IAAI,CAACpC,SAAS,CAAClB,GAAG,CAACyD,qBAAqB,CAAC,IAAI,CAACN,OAAO,EAAEnD,GAAG,CAAC0D,SAAS,CAACC,QAAQ,EAAEb,CAAC,IAAI;MAChF,MAAMtB,KAAK,GAAG,IAAIvB,qBAAqB,CAAC6C,CAAC,CAAC;MAC1C,IAAIc,YAAY,GAAG,IAAI;MACvB,MAAMX,WAAW,GAAG,OAAO,IAAI,CAACA,WAAW,KAAK,QAAQ,GAAG,IAAI,CAACD,SAAS,CAAC,IAAI,CAACC,WAAW,CAAC,GAAGC,SAAS;MACvG,IAAIK,YAAY,KAAK/B,KAAK,CAACqC,MAAM,CAACN,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI/B,KAAK,CAACqC,MAAM,CAACN,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAClFK,YAAY,GAAG,IAAI,CAACE,aAAa,CAAC,CAAC;MACvC,CAAC,MACI,IAAIN,QAAQ,KAAKhC,KAAK,CAACqC,MAAM,CAACL,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAIhC,KAAK,CAACqC,MAAM,CAACL,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC3EI,YAAY,GAAG,IAAI,CAACG,SAAS,CAAC,CAAC;MACnC,CAAC,MACI,IAAIvC,KAAK,CAACqC,MAAM,CAAC,CAAC,CAAC,oBAAoB,CAAC,IAAI,IAAI,CAAClC,iBAAiB,EAAE;QACrE,IAAI,CAACF,YAAY,CAACsB,IAAI,CAAC,CAAC;MAC5B,CAAC,MACI,IAAIvB,KAAK,CAACqC,MAAM,CAAC,EAAE,CAAC,kBAAkB,CAAC,EAAE;QAC1CD,YAAY,GAAG,IAAI,CAACI,UAAU,CAAC,CAAC;MACpC,CAAC,MACI,IAAIxC,KAAK,CAACqC,MAAM,CAAC,EAAE,CAAC,iBAAiB,CAAC,EAAE;QACzCD,YAAY,GAAG,IAAI,CAACK,SAAS,CAAC,CAAC;MACnC,CAAC,MACI,IAAIzC,KAAK,CAACqC,MAAM,CAAC,CAAC,CAAC,iBAAiB,CAAC,IAAIZ,WAAW,YAAY9C,kBAAkB,IAAI8C,WAAW,CAACiB,oBAAoB,EAAE;QACzH;QACAN,YAAY,GAAG,IAAI,CAACG,SAAS,CAACb,SAAS,EAAE,IAAI,CAAC;MAClD,CAAC,MACI,IAAI,IAAI,CAACiB,iBAAiB,CAAC3C,KAAK,CAAC,EAAE;QACpC;QACA,IAAI,IAAI,CAACa,YAAY,CAACC,OAAO,EAAE;UAC3B,IAAI,CAAC8B,SAAS,CAAC5C,KAAK,CAAC;QACzB,CAAC,MACI;UACD,IAAI,CAACJ,cAAc,GAAG,IAAI;QAC9B;MACJ,CAAC,MACI;QACDwC,YAAY,GAAG,KAAK;MACxB;MACA,IAAIA,YAAY,EAAE;QACdpC,KAAK,CAAC6C,cAAc,CAAC,CAAC;QACtB7C,KAAK,CAAC8C,eAAe,CAAC,CAAC;MAC3B;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,CAACpD,SAAS,CAAClB,GAAG,CAACyD,qBAAqB,CAAC,IAAI,CAACN,OAAO,EAAEnD,GAAG,CAAC0D,SAAS,CAACa,MAAM,EAAEzB,CAAC,IAAI;MAC9E,MAAMtB,KAAK,GAAG,IAAIvB,qBAAqB,CAAC6C,CAAC,CAAC;MAC1C;MACA,IAAI,IAAI,CAACqB,iBAAiB,CAAC3C,KAAK,CAAC,EAAE;QAC/B,IAAI,CAAC,IAAI,CAACa,YAAY,CAACC,OAAO,IAAI,IAAI,CAAClB,cAAc,EAAE;UACnD,IAAI,CAACA,cAAc,GAAG,KAAK;UAC3B,IAAI,CAACgD,SAAS,CAAC5C,KAAK,CAAC;QACzB;QACAA,KAAK,CAAC6C,cAAc,CAAC,CAAC;QACtB7C,KAAK,CAAC8C,eAAe,CAAC,CAAC;MAC3B;MACA;MAAA,KACK,IAAI9C,KAAK,CAACqC,MAAM,CAAC,CAAC,CAAC,iBAAiB,CAAC,IAAIrC,KAAK,CAACqC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,iBAAiB,CAAC,IAAIrC,KAAK,CAACqC,MAAM,CAAC,EAAE,CAAC,qBAAqB,CAAC,IAAIrC,KAAK,CAACqC,MAAM,CAAC,EAAE,CAAC,uBAAuB,CAAC,IAAIrC,KAAK,CAACqC,MAAM,CAAC,EAAE,CAAC,uBAAuB,CAAC,IAAIrC,KAAK,CAACqC,MAAM,CAAC,EAAE,CAAC,wBAAwB,CAAC,EAAE;QACtR,IAAI,CAACW,iBAAiB,CAAC,CAAC;MAC5B;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,CAACC,YAAY,GAAG,IAAI,CAACvD,SAAS,CAAClB,GAAG,CAAC0E,UAAU,CAAC,IAAI,CAACvB,OAAO,CAAC,CAAC;IAChE,IAAI,CAACjC,SAAS,CAAC,IAAI,CAACuD,YAAY,CAAClD,SAAS,CAAC,MAAM;MAC7C,IAAIvB,GAAG,CAAC2E,gBAAgB,CAAC,CAAC,KAAK,IAAI,CAACxB,OAAO,IAAI,CAACnD,GAAG,CAAC4E,UAAU,CAAC5E,GAAG,CAAC2E,gBAAgB,CAAC,CAAC,EAAE,IAAI,CAACxB,OAAO,CAAC,EAAE;QAClG,IAAI,CAAC7B,UAAU,CAACyB,IAAI,CAAC,CAAC;QACtB,IAAI,CAAC8B,qBAAqB,GAAG,IAAI,CAAC5B,WAAW;QAC7C,IAAI,CAACA,WAAW,GAAGC,SAAS;QAC5B,IAAI,CAAC9B,cAAc,GAAG,KAAK;MAC/B;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,CAACF,SAAS,CAAC,IAAI,CAACuD,YAAY,CAACK,UAAU,CAAC,MAAM,IAAI,CAACN,iBAAiB,CAAC,CAAC,CAAC,CAAC;IAC5E,IAAI,CAACO,WAAW,GAAG3B,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC;IAC/C,IAAI,CAAC0B,WAAW,CAACzB,SAAS,GAAG,mBAAmB;IAChD,IAAI,IAAI,CAACtC,OAAO,CAACgE,qBAAqB,EAAE;MACpC,IAAI,CAACD,WAAW,CAACE,SAAS,CAACpC,GAAG,CAAC,mBAAmB,CAAC;IACvD;IACA,IAAI,CAACkC,WAAW,CAACG,YAAY,CAAC,MAAM,EAAE,IAAI,CAAClE,OAAO,CAACmE,QAAQ,IAAI,SAAS,CAAC;IACzE,IAAI,IAAI,CAACnE,OAAO,CAACoE,SAAS,EAAE;MACxB,IAAI,CAACL,WAAW,CAACG,YAAY,CAAC,YAAY,EAAE,IAAI,CAAClE,OAAO,CAACoE,SAAS,CAAC;IACvE;IACA,IAAI,CAACjC,OAAO,CAACkC,WAAW,CAAC,IAAI,CAACN,WAAW,CAAC;IAC1ChE,SAAS,CAACsE,WAAW,CAAC,IAAI,CAAClC,OAAO,CAAC;EACvC;EACAmC,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE;MACpB,IAAI,CAACR,WAAW,CAACG,YAAY,CAAC,MAAM,EAAE,IAAI,CAAClE,OAAO,CAACmE,QAAQ,IAAI,SAAS,CAAC;IAC7E,CAAC,MACI;MACD,IAAI,CAACJ,WAAW,CAACG,YAAY,CAAC,MAAM,EAAE,cAAc,CAAC;IACzD;EACJ;EACA;EACA;EACA;EACAM,YAAYA,CAACnE,SAAS,EAAE;IACpB,IAAI,CAACA,SAAS,GAAGA,SAAS;IAC1B,IAAI,IAAI,CAACA,SAAS,EAAE;MAChB,MAAMoE,YAAY,GAAG,IAAI,CAACzC,SAAS,CAAC0C,IAAI,CAACC,EAAE,IAAIA,EAAE,YAAYxF,kBAAkB,IAAIwF,EAAE,CAACC,SAAS,CAAC,CAAC,CAAC;MAClG,IAAIH,YAAY,YAAYtF,kBAAkB,EAAE;QAC5CsF,YAAY,CAACD,YAAY,CAAC,IAAI,CAAC;MACnC;IACJ,CAAC,MACI;MACD,IAAI,CAACxC,SAAS,CAAC6C,OAAO,CAACF,EAAE,IAAI;QACzB,IAAIA,EAAE,YAAYxF,kBAAkB,EAAE;UAClCwF,EAAE,CAACH,YAAY,CAAC,KAAK,CAAC;QAC1B;MACJ,CAAC,CAAC;IACN;EACJ;EACArB,iBAAiBA,CAAC3C,KAAK,EAAE;IACrB,IAAIsE,GAAG,GAAG,KAAK;IACf,IAAI,CAACzD,YAAY,CAACG,IAAI,CAACqD,OAAO,CAACE,OAAO,IAAI;MACtCD,GAAG,GAAGA,GAAG,IAAItE,KAAK,CAACqC,MAAM,CAACkC,OAAO,CAAC;IACtC,CAAC,CAAC;IACF,OAAOD,GAAG;EACd;EACAtB,iBAAiBA,CAAA,EAAG;IAChB,KAAK,IAAIwB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACjB,WAAW,CAACkB,QAAQ,CAACV,MAAM,EAAES,CAAC,EAAE,EAAE;MACvD,MAAME,IAAI,GAAG,IAAI,CAACnB,WAAW,CAACkB,QAAQ,CAACD,CAAC,CAAC;MACzC,IAAIhG,GAAG,CAAC4E,UAAU,CAAC5E,GAAG,CAAC2E,gBAAgB,CAAC,CAAC,EAAEuB,IAAI,CAAC,EAAE;QAC9C,IAAI,CAACjD,WAAW,GAAG+C,CAAC;QACpB,IAAI,CAAChD,SAAS,CAAC,IAAI,CAACC,WAAW,CAAC,EAAEkD,SAAS,GAAG,CAAC;QAC/C;MACJ;IACJ;EACJ;EACA,IAAIjE,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACD,QAAQ;EACxB;EACA,IAAIC,OAAOA,CAACA,OAAO,EAAE;IACjB,IAAI,CAACD,QAAQ,GAAGC,OAAO;IACvB,IAAI,CAACc,SAAS,CAAC6C,OAAO,CAACG,CAAC,IAAIA,CAAC,CAACI,gBAAgB,CAAClE,OAAO,CAAC,CAAC;EAC5D;EACA,IAAIS,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACC,aAAa;EAC7B;EACA,IAAID,YAAYA,CAACA,YAAY,EAAE;IAC3B,IAAI,CAACC,aAAa,GAAGD,YAAY;IACjC;IACA;IACA,IAAI,CAAC1B,wBAAwB,CAACoF,KAAK,CAAC,CAAC;IACrC,IAAI,CAACpF,wBAAwB,CAAC4B,GAAG,CAAC,IAAI,CAACD,aAAa,CAACd,QAAQ,CAACgB,CAAC,IAAI,IAAI,CAACjB,SAAS,CAACkB,IAAI,CAACD,CAAC,CAAC,CAAC,CAAC;IAC3F,IAAI,CAAC7B,wBAAwB,CAAC4B,GAAG,CAAC,IAAI,CAACD,aAAa,CAACZ,SAAS,CAACc,CAAC,IAAI,IAAI,CAACf,UAAU,CAACgB,IAAI,CAACD,CAAC,CAAC,CAAC,CAAC;IAC7F,IAAI,CAACE,SAAS,CAAC6C,OAAO,CAACS,IAAI,IAAIA,IAAI,CAAC3D,YAAY,GAAGA,YAAY,CAAC;EACpE;EACA4D,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAACpD,OAAO;EACvB;EACAqD,SAASA,CAACC,cAAc,EAAE;IACtB;IACA,IAAI,OAAOA,cAAc,KAAK,QAAQ,EAAE;MACpC,OAAO,IAAI,CAACzD,SAAS,CAACyD,cAAc,CAAC,EAAEC,MAAM;IACjD;IACA;IACA,IAAI1G,GAAG,CAAC2G,aAAa,CAACF,cAAc,CAAC,EAAE;MACnC,OAAOA,cAAc,CAACG,aAAa,KAAK,IAAI,CAAC7B,WAAW,EAAE;QACtD,IAAI,CAAC0B,cAAc,CAACG,aAAa,EAAE;UAC/B,OAAO1D,SAAS;QACpB;QACAuD,cAAc,GAAGA,cAAc,CAACG,aAAa;MACjD;MACA,KAAK,IAAIZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACjB,WAAW,CAAC8B,UAAU,CAACtB,MAAM,EAAES,CAAC,EAAE,EAAE;QACzD,IAAI,IAAI,CAACjB,WAAW,CAAC8B,UAAU,CAACb,CAAC,CAAC,KAAKS,cAAc,EAAE;UACnD,OAAO,IAAI,CAACzD,SAAS,CAACgD,CAAC,CAAC,CAACU,MAAM;QACnC;MACJ;IACJ;IACA,OAAOxD,SAAS;EACpB;EACA4D,IAAIA,CAACC,GAAG,EAAE/F,OAAO,GAAG,CAAC,CAAC,EAAE;IACpB,MAAMgG,OAAO,GAAGC,KAAK,CAACC,OAAO,CAACH,GAAG,CAAC,GAAGA,GAAG,GAAG,CAACA,GAAG,CAAC;IAChD,IAAII,KAAK,GAAGvG,KAAK,CAACwG,QAAQ,CAACpG,OAAO,CAACmG,KAAK,CAAC,GAAGnG,OAAO,CAACmG,KAAK,GAAG,IAAI;IAChEH,OAAO,CAACnB,OAAO,CAAEa,MAAM,IAAK;MACxB,MAAMW,qBAAqB,GAAGjE,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC;MAC1DgE,qBAAqB,CAAC/D,SAAS,GAAG,aAAa;MAC/C+D,qBAAqB,CAACnC,YAAY,CAAC,MAAM,EAAE,cAAc,CAAC;MAC1D,IAAIoB,IAAI;MACR,MAAMgB,eAAe,GAAG;QAAE5E,aAAa,EAAE,IAAI,CAACD,cAAc;QAAE,GAAGzB,OAAO;QAAEuG,SAAS,EAAE,IAAI,CAACvG,OAAO,CAACmE,QAAQ,KAAK;MAAU,CAAC;MAC1H,IAAI,IAAI,CAACnE,OAAO,CAACwG,sBAAsB,EAAE;QACrClB,IAAI,GAAG,IAAI,CAACtF,OAAO,CAACwG,sBAAsB,CAACd,MAAM,EAAEY,eAAe,CAAC;MACvE;MACA,IAAI,CAAChB,IAAI,EAAE;QACPA,IAAI,GAAG,IAAIpG,cAAc,CAAC,IAAI,CAACgC,OAAO,EAAEwE,MAAM,EAAEY,eAAe,CAAC;MACpE;MACA;MACA,IAAI,CAAC,IAAI,CAACtG,OAAO,CAACyG,gBAAgB,EAAE;QAChC,IAAI,CAACtG,mBAAmB,CAACuG,GAAG,CAACpB,IAAI,EAAEtG,GAAG,CAACyD,qBAAqB,CAAC4D,qBAAqB,EAAErH,GAAG,CAAC0D,SAAS,CAACiE,YAAY,EAAG7E,CAAC,IAAK;UACnH9C,GAAG,CAAC4H,WAAW,CAACC,IAAI,CAAC/E,CAAC,EAAE,IAAI,CAAC;QACjC,CAAC,CAAC,CAAC;MACP;MACAwD,IAAI,CAAC3D,YAAY,GAAG,IAAI,CAACC,aAAa;MACtC0D,IAAI,CAACF,gBAAgB,CAAC,IAAI,CAAClE,OAAO,CAAC;MACnCoE,IAAI,CAACwB,MAAM,CAACT,qBAAqB,CAAC;MAClC,IAAI,IAAI,CAAChG,SAAS,IAAIiF,IAAI,YAAYnG,kBAAkB,IAAI,IAAI,CAAC6C,SAAS,CAACuC,MAAM,KAAK,CAAC,EAAE;QACrF;QACAe,IAAI,CAACd,YAAY,CAAC,IAAI,CAAC;MAC3B;MACA,IAAI2B,KAAK,KAAK,IAAI,IAAIA,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAI,IAAI,CAACpC,WAAW,CAACkB,QAAQ,CAACV,MAAM,EAAE;QAC1E,IAAI,CAACR,WAAW,CAACM,WAAW,CAACgC,qBAAqB,CAAC;QACnD,IAAI,CAACrE,SAAS,CAAC8D,IAAI,CAACR,IAAI,CAAC;MAC7B,CAAC,MACI;QACD,IAAI,CAACvB,WAAW,CAACgD,YAAY,CAACV,qBAAqB,EAAE,IAAI,CAACtC,WAAW,CAACkB,QAAQ,CAACkB,KAAK,CAAC,CAAC;QACtF,IAAI,CAACnE,SAAS,CAACgF,MAAM,CAACb,KAAK,EAAE,CAAC,EAAEb,IAAI,CAAC;QACrCa,KAAK,EAAE;MACX;IACJ,CAAC,CAAC;IACF,IAAI,OAAO,IAAI,CAAClE,WAAW,KAAK,QAAQ,EAAE;MACtC;MACA,IAAI,CAACgF,KAAK,CAAC,IAAI,CAAChF,WAAW,CAAC;IAChC;IACA,IAAI,CAACqC,WAAW,CAAC,CAAC;EACtB;EACAe,KAAKA,CAAA,EAAG;IACJ,IAAI,IAAI,CAAC6B,OAAO,CAAC,CAAC,EAAE;MAChB;IACJ;IACA,IAAI,CAAClF,SAAS,GAAGrC,OAAO,CAAC,IAAI,CAACqC,SAAS,CAAC;IACxC,IAAI,CAAC7B,mBAAmB,CAACgH,kBAAkB,CAAC,CAAC;IAC7CnI,GAAG,CAACoI,SAAS,CAAC,IAAI,CAACrD,WAAW,CAAC;IAC/B,IAAI,CAACO,WAAW,CAAC,CAAC;EACtB;EACAC,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI,CAACvC,SAAS,CAACuC,MAAM;EAChC;EACA2C,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAAClF,SAAS,CAACuC,MAAM,KAAK,CAAC;EACtC;EACA0C,KAAKA,CAAClB,GAAG,EAAE;IACP,IAAIsB,WAAW,GAAG,KAAK;IACvB,IAAIlB,KAAK,GAAGjE,SAAS;IACrB,IAAI6D,GAAG,KAAK7D,SAAS,EAAE;MACnBmF,WAAW,GAAG,IAAI;IACtB,CAAC,MACI,IAAI,OAAOtB,GAAG,KAAK,QAAQ,EAAE;MAC9BI,KAAK,GAAGJ,GAAG;IACf,CAAC,MACI,IAAI,OAAOA,GAAG,KAAK,SAAS,EAAE;MAC/BsB,WAAW,GAAGtB,GAAG;IACrB;IACA,IAAIsB,WAAW,IAAI,OAAO,IAAI,CAACpF,WAAW,KAAK,WAAW,EAAE;MACxD,MAAMwC,YAAY,GAAG,IAAI,CAACzC,SAAS,CAACsF,SAAS,CAAChC,IAAI,IAAIA,IAAI,CAACV,SAAS,CAAC,CAAC,CAAC;MACvE;MACA,IAAI,CAAC3C,WAAW,GAAGwC,YAAY,KAAK,CAAC,CAAC,GAAGvC,SAAS,GAAGuC,YAAY;MACjE,IAAI,CAAC8C,WAAW,CAACrF,SAAS,EAAEA,SAAS,EAAE,IAAI,CAAC;IAChD,CAAC,MACI;MACD,IAAIiE,KAAK,KAAKjE,SAAS,EAAE;QACrB,IAAI,CAACD,WAAW,GAAGkE,KAAK;MAC5B;MACA,IAAI,CAACoB,WAAW,CAACrF,SAAS,EAAEA,SAAS,EAAE,IAAI,CAAC;IAChD;EACJ;EACAc,UAAUA,CAAA,EAAG;IACT,IAAI,CAACf,WAAW,GAAG,IAAI,CAACsC,MAAM,CAAC,CAAC,GAAG,CAAC;IACpC,OAAO,IAAI,CAACxB,SAAS,CAAC,IAAI,CAAC;EAC/B;EACAE,SAASA,CAAA,EAAG;IACR,IAAI,CAAChB,WAAW,GAAG,CAAC;IACpB,OAAO,IAAI,CAACa,aAAa,CAAC,IAAI,CAAC;EACnC;EACAC,SAASA,CAACyE,SAAS,EAAEC,UAAU,EAAE;IAC7B,IAAI,OAAO,IAAI,CAACxF,WAAW,KAAK,WAAW,EAAE;MACzC,IAAI,CAACA,WAAW,GAAG,IAAI,CAACD,SAAS,CAACuC,MAAM,GAAG,CAAC;IAChD,CAAC,MACI,IAAI,IAAI,CAACvC,SAAS,CAACuC,MAAM,IAAI,CAAC,EAAE;MACjC,OAAO,KAAK;IAChB;IACA,MAAMmD,UAAU,GAAG,IAAI,CAACzF,WAAW;IACnC,IAAIqD,IAAI;IACR,GAAG;MACC,IAAI,CAACkC,SAAS,IAAI,IAAI,CAACxH,OAAO,CAAC2H,qBAAqB,IAAI,IAAI,CAAC1F,WAAW,GAAG,CAAC,IAAI,IAAI,CAACD,SAAS,CAACuC,MAAM,EAAE;QACnG,IAAI,CAACtC,WAAW,GAAGyF,UAAU;QAC7B,OAAO,KAAK;MAChB;MACA,IAAI,CAACzF,WAAW,GAAG,CAAC,IAAI,CAACA,WAAW,GAAG,CAAC,IAAI,IAAI,CAACD,SAAS,CAACuC,MAAM;MACjEe,IAAI,GAAG,IAAI,CAACtD,SAAS,CAAC,IAAI,CAACC,WAAW,CAAC;IAC3C,CAAC,QAAQ,IAAI,CAACA,WAAW,KAAKyF,UAAU,KAAM,IAAI,CAAC1H,OAAO,CAAC4H,qBAAqB,IAAI,CAACtC,IAAI,CAACV,SAAS,CAAC,CAAC,IAAKU,IAAI,CAACI,MAAM,CAACmC,EAAE,KAAKvI,SAAS,CAACwI,EAAE,CAAC;IAC1I,IAAI,CAACP,WAAW,CAACrF,SAAS,EAAEA,SAAS,EAAEuF,UAAU,CAAC;IAClD,OAAO,IAAI;EACf;EACA3E,aAAaA,CAAC0E,SAAS,EAAE;IACrB,IAAI,OAAO,IAAI,CAACvF,WAAW,KAAK,WAAW,EAAE;MACzC,IAAI,CAACA,WAAW,GAAG,CAAC;IACxB,CAAC,MACI,IAAI,IAAI,CAACD,SAAS,CAACuC,MAAM,IAAI,CAAC,EAAE;MACjC,OAAO,KAAK;IAChB;IACA,MAAMmD,UAAU,GAAG,IAAI,CAACzF,WAAW;IACnC,IAAIqD,IAAI;IACR,GAAG;MACC,IAAI,CAACrD,WAAW,GAAG,IAAI,CAACA,WAAW,GAAG,CAAC;MACvC,IAAI,IAAI,CAACA,WAAW,GAAG,CAAC,EAAE;QACtB,IAAI,CAACuF,SAAS,IAAI,IAAI,CAACxH,OAAO,CAAC2H,qBAAqB,EAAE;UAClD,IAAI,CAAC1F,WAAW,GAAGyF,UAAU;UAC7B,OAAO,KAAK;QAChB;QACA,IAAI,CAACzF,WAAW,GAAG,IAAI,CAACD,SAAS,CAACuC,MAAM,GAAG,CAAC;MAChD;MACAe,IAAI,GAAG,IAAI,CAACtD,SAAS,CAAC,IAAI,CAACC,WAAW,CAAC;IAC3C,CAAC,QAAQ,IAAI,CAACA,WAAW,KAAKyF,UAAU,KAAM,IAAI,CAAC1H,OAAO,CAAC4H,qBAAqB,IAAI,CAACtC,IAAI,CAACV,SAAS,CAAC,CAAC,IAAKU,IAAI,CAACI,MAAM,CAACmC,EAAE,KAAKvI,SAAS,CAACwI,EAAE,CAAC;IAC1I,IAAI,CAACP,WAAW,CAAC,IAAI,CAAC;IACtB,OAAO,IAAI;EACf;EACAA,WAAWA,CAACQ,SAAS,EAAEC,aAAa,EAAEP,UAAU,GAAG,KAAK,EAAE;IACtD,IAAI,OAAO,IAAI,CAACxF,WAAW,KAAK,WAAW,EAAE;MACzC,IAAI,CAAC8B,WAAW,CAACkD,KAAK,CAAC;QAAEe;MAAc,CAAC,CAAC;IAC7C;IACA,IAAI,IAAI,CAACnE,qBAAqB,KAAK3B,SAAS,IAAI,IAAI,CAAC2B,qBAAqB,KAAK,IAAI,CAAC5B,WAAW,EAAE;MAC7F,IAAI,CAACD,SAAS,CAAC,IAAI,CAAC6B,qBAAqB,CAAC,EAAEoE,IAAI,CAAC,CAAC;IACtD;IACA,MAAMC,cAAc,GAAG,IAAI,CAACjG,WAAW,KAAKC,SAAS,GAAG,IAAI,CAACF,SAAS,CAAC,IAAI,CAACC,WAAW,CAAC,GAAGC,SAAS;IACpG,IAAIgG,cAAc,EAAE;MAChB,IAAIC,SAAS,GAAG,IAAI;MACpB,IAAI,CAACvI,KAAK,CAACwI,UAAU,CAACF,cAAc,CAACjB,KAAK,CAAC,EAAE;QACzCkB,SAAS,GAAG,KAAK;MACrB;MACA,IAAI,IAAI,CAACnI,OAAO,CAAC4H,qBAAqB,IAAIhI,KAAK,CAACwI,UAAU,CAACF,cAAc,CAACtD,SAAS,CAAC,IAAI,CAACsD,cAAc,CAACtD,SAAS,CAAC,CAAC,EAAE;QACjHuD,SAAS,GAAG,KAAK;MACrB;MACA,IAAID,cAAc,CAACxC,MAAM,CAACmC,EAAE,KAAKvI,SAAS,CAACwI,EAAE,EAAE;QAC3CK,SAAS,GAAG,KAAK;MACrB;MACA,IAAI,CAACA,SAAS,EAAE;QACZ,IAAI,CAACpE,WAAW,CAACkD,KAAK,CAAC;UAAEe;QAAc,CAAC,CAAC;QACzC,IAAI,CAACnE,qBAAqB,GAAG3B,SAAS;MAC1C,CAAC,MACI,IAAIuF,UAAU,IAAI,IAAI,CAAC5D,qBAAqB,KAAK,IAAI,CAAC5B,WAAW,EAAE;QACpEiG,cAAc,CAACjB,KAAK,CAACc,SAAS,CAAC;QAC/B,IAAI,CAAClE,qBAAqB,GAAG,IAAI,CAAC5B,WAAW;MACjD;MACA,IAAIkG,SAAS,EAAE;QACXD,cAAc,CAAC/C,SAAS,GAAG,CAAC;MAChC;IACJ;EACJ;EACA/B,SAASA,CAAC5C,KAAK,EAAE;IACb,IAAI,OAAO,IAAI,CAACyB,WAAW,KAAK,WAAW,EAAE;MACzC,OAAO,CAAC;IACZ;IACA;IACA,MAAMiG,cAAc,GAAG,IAAI,CAAClG,SAAS,CAAC,IAAI,CAACC,WAAW,CAAC;IACvD,IAAIiG,cAAc,YAAY/I,kBAAkB,EAAE;MAC9C,MAAM+B,OAAO,GAAIgH,cAAc,CAACjH,QAAQ,KAAK,IAAI,IAAIiH,cAAc,CAACjH,QAAQ,KAAKiB,SAAS,GAAI1B,KAAK,GAAG0H,cAAc,CAACjH,QAAQ;MAC7H,IAAI,CAACoH,GAAG,CAACH,cAAc,CAACI,OAAO,EAAEpH,OAAO,CAAC;IAC7C;EACJ;EACMmH,GAAGA,CAAC3C,MAAM,EAAExE,OAAO,EAAE;IAAA,IAAAqH,KAAA;IAAA,OAAAC,iBAAA;MACvB,MAAMD,KAAI,CAAC3G,aAAa,CAACyG,GAAG,CAAC3C,MAAM,EAAExE,OAAO,CAAC;IAAC;EAClD;EACAvB,OAAOA,CAAA,EAAG;IACN,IAAI,CAACsB,QAAQ,GAAGiB,SAAS;IACzB,IAAI,CAACF,SAAS,GAAGrC,OAAO,CAAC,IAAI,CAACqC,SAAS,CAAC;IACxC,IAAI,CAACuD,YAAY,CAAC,CAAC,CAACkD,MAAM,CAAC,CAAC;IAC5B,KAAK,CAAC9I,OAAO,CAAC,CAAC;EACnB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}