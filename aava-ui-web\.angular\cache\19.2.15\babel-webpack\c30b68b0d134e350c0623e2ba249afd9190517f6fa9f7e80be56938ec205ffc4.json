{"ast": null, "code": "import _asyncToGenerator from \"C:/console/aava-ui-web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { takeUntil, of, map, catchError, Subject, debounceTime, distinctUntilChanged, startWith } from 'rxjs';\nimport { PageFooterComponent } from '@shared/components/page-footer/page-footer.component';\nimport { formatToDisplayDate } from '@shared/utils/date-utils';\nimport promptsLabels from './constants/prompts.json';\nimport { PROMPTS_BASE_ACTIONS } from './prompts-actions';\nimport { AvaTextboxComponent, TextCardComponent, DropdownComponent, IconComponent } from '@ava/play-comp-library';\nimport { LucideAngularModule } from 'lucide-angular';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { ConsoleCardComponent } from '@shared/components/console-card/console-card.component';\nimport { TimeAgoPipe } from '@shared/pipes/time-ago.pipe';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@shared/services/pagination.service\";\nimport * as i2 from \"@shared/services/prompts.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@ava/play-comp-library\";\nimport * as i6 from \"@angular/common\";\nfunction PromptsComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"div\", 14)(2, \"h5\", 15);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.promptLabels.noResults);\n  }\n}\nfunction PromptsComponent_ng_container_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"ava-console-card\", 16);\n    i0.ɵɵpipe(2, \"timeAgo\");\n    i0.ɵɵlistener(\"actionClick\", function PromptsComponent_ng_container_11_Template_ava_console_card_actionClick_1_listener($event) {\n      const prompt_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onActionClick($event, prompt_r3.id));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const prompt_r3 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"title\", prompt_r3 == null ? null : prompt_r3.title)(\"description\", prompt_r3 == null ? null : prompt_r3.description)(\"author\", (prompt_r3 == null ? null : prompt_r3.owner) || \"AAVA\")(\"date\", i0.ɵɵpipeBind1(2, 6, prompt_r3 == null ? null : prompt_r3.createdDate))(\"actions\", ctx_r0.defaultActions)(\"skeleton\", ctx_r0.isLoading);\n  }\n}\nfunction PromptsComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"div\", 18)(2, \"app-page-footer\", 19);\n    i0.ɵɵlistener(\"pageChange\", function PromptsComponent_div_12_Template_app_page_footer_pageChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onPageChange($event));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"totalItems\", ctx_r0.filteredPrompts.length + 1)(\"currentPage\", ctx_r0.currentPage)(\"itemsPerPage\", ctx_r0.itemsPerPage);\n  }\n}\nexport let PromptsComponent = /*#__PURE__*/(() => {\n  class PromptsComponent {\n    paginationService;\n    promptsService;\n    router;\n    route;\n    fb;\n    dialogService;\n    // Popup state for success messages\n    showSuccessPopup = false;\n    popupMessage = '';\n    popupTitle = '';\n    iconName = 'info';\n    submissionSuccess = false;\n    // popup for delete confirmation\n    showDeletePopup = false;\n    promptToDelete = null;\n    simpleOptions = [{\n      name: 'Option 1',\n      value: '1'\n    }, {\n      name: 'Option 2',\n      value: '2'\n    }, {\n      name: 'Option 3',\n      value: '3'\n    }, {\n      name: 'Option 4',\n      value: '4'\n    }, {\n      name: 'Option 5',\n      value: '5'\n    }];\n    defaultActions = [{\n      id: 'delete',\n      icon: 'trash',\n      label: 'Delete item',\n      tooltip: 'Delete'\n    }, {\n      id: 'edit',\n      icon: 'edit',\n      label: 'Edit item',\n      tooltip: 'Edit'\n    }, {\n      id: 'view',\n      icon: 'eye',\n      label: 'View Prompts',\n      tooltip: 'View',\n      isPrimary: true\n    }];\n    promptLabels = promptsLabels.labels;\n    allPrompts = [];\n    filteredPrompts = [];\n    displayedPrompts = [];\n    searchForm;\n    isLoading = false;\n    currentPage = 1;\n    itemsPerPage = 11;\n    totalPages = 1;\n    destroy$ = new Subject();\n    selectedData = null;\n    cardSkeletonPlaceholders = Array(11);\n    constructor(paginationService, promptsService, router, route, fb, dialogService) {\n      this.paginationService = paginationService;\n      this.promptsService = promptsService;\n      this.router = router;\n      this.route = route;\n      this.fb = fb;\n      this.dialogService = dialogService;\n      this.searchForm = this.fb.group({\n        search: ['']\n      });\n    }\n    ngOnInit() {\n      this.isLoading = true;\n      this.initSearchListener();\n      this.promptsService.fetchAllPrompts().pipe(takeUntil(this.destroy$), map(this.transformResponseToCardData.bind(this)), catchError(error => {\n        console.error('Error fetching prompts:', error);\n        this.isLoading = false;\n        return of({\n          prompts: []\n        });\n      })).subscribe({\n        next: ({\n          prompts\n        }) => {\n          this.allPrompts = prompts;\n          this.filteredPrompts = [...prompts];\n          this.updateDisplayedPrompts();\n          this.setInitialPageFromQueryParam();\n        },\n        error: err => console.error('Subscription error:', err.message),\n        complete: () => {\n          this.isLoading = false;\n        }\n      });\n    }\n    onSelectionChange(data) {\n      this.selectedData = data;\n    }\n    onCreatePrompt() {\n      this.router.navigate(['/libraries/prompts/create']);\n    }\n    getHeaderIcons(prompt) {\n      return [{\n        iconName: 'NotebookText',\n        title: prompt.toolType || 'Prompt'\n      }, {\n        iconName: 'users',\n        title: `${prompt.userCount || 30}`\n      }];\n    }\n    getFooterIcons(prompt) {\n      return [{\n        iconName: 'user',\n        title: prompt.owner || 'AAVA'\n      }, {\n        iconName: 'calendar-days',\n        title: prompt.createdDate\n      }];\n    }\n    onPageChange(page) {\n      this.currentPage = page;\n      this.updateDisplayedPrompts();\n      this.router.navigate([], {\n        relativeTo: this.route,\n        queryParams: {\n          page: this.currentPage\n        },\n        queryParamsHandling: 'merge'\n      });\n    }\n    onActionClick(event, promptId) {\n      switch (event.actionId) {\n        case 'delete':\n          this.showDeleteConfirmation(promptId);\n          break;\n        case 'edit':\n          this.executePrompt(promptId);\n          break;\n        case 'view':\n          this.viewPrompt(promptId);\n          break;\n        default:\n          break;\n      }\n    }\n    viewPrompt(promptId) {\n      this.router.navigate(['/libraries/prompts/edit', promptId], {\n        queryParams: {\n          view: 'true',\n          returnPage: this.currentPage\n        }\n      });\n    }\n    showDeleteConfirmation(promptId) {\n      const prompt = this.allPrompts.find(p => p.id === promptId);\n      if (!prompt) return;\n      this.dialogService.confirmation({\n        title: 'Delete Prompt?',\n        message: `Are you sure you want to delete \"${prompt.title || ''}\"?`,\n        confirmButtonText: 'Delete',\n        cancelButtonText: 'Cancel',\n        confirmButtonVariant: 'danger',\n        icon: 'trash'\n      }).then(result => {\n        if (result.confirmed) {\n          this.deletePrompt(promptId);\n        }\n      });\n    }\n    deletePrompt(promptId) {\n      // Show loading dialog\n      this.dialogService.loading({\n        title: 'Deleting Prompt...',\n        message: 'Please wait while we delete the prompt.',\n        showProgress: false,\n        showCancelButton: false\n      });\n      this.promptsService.deletePrompt(promptId).subscribe({\n        next: res => {\n          this.dialogService.close(); // Close loading dialog\n          if (res && res.success !== false) {\n            // Update local prompt lists\n            this.allPrompts = this.allPrompts.filter(p => p.id !== promptId);\n            this.filteredPrompts = this.filteredPrompts.filter(p => p.id !== promptId);\n            this.updateDisplayedPrompts();\n            // Show success dialog\n            this.dialogService.success({\n              title: 'Success!',\n              message: 'Prompt deleted successfully.'\n            });\n          } else {\n            this.dialogService.error({\n              title: 'Error',\n              message: 'Failed to delete prompt.'\n            });\n          }\n        },\n        error: err => {\n          this.dialogService.close(); // Close loading dialog\n          console.error('Failed to delete prompt:', err);\n          this.dialogService.error({\n            title: 'Error',\n            message: 'An unexpected error occurred while deleting the prompt.',\n            showRetryButton: true,\n            retryButtonText: 'Retry'\n          }).then(result => {\n            if (result.action === 'retry') {\n              this.deletePrompt(promptId);\n            }\n          });\n        }\n      });\n    }\n    transformResponseToCardData(response) {\n      const prompts = 'prompts' in response ? response.prompts : response;\n      return {\n        prompts: prompts.map(this.formatPromptCard.bind(this))\n      };\n    }\n    formatPromptCard(item) {\n      const {\n        name,\n        updatedAt,\n        categoryName,\n        domainName,\n        tags = [],\n        ...rest\n      } = item;\n      const customTags = this.getCustomTags(categoryName, domainName);\n      const allTags = [...tags, ...customTags];\n      const tagSummary = this.getTagSummary(allTags);\n      const createdDate = formatToDisplayDate(updatedAt);\n      return {\n        title: name,\n        createdDate,\n        tags: allTags,\n        tagSummary,\n        actions: PROMPTS_BASE_ACTIONS,\n        ...rest\n      };\n    }\n    executePrompt(promptId) {\n      this.router.navigate(['/libraries/prompts/edit', promptId], {\n        queryParams: {\n          execute: 'true',\n          returnPage: this.currentPage\n        }\n      });\n    }\n    copyPrompt(promptId) {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        if (!promptId) return;\n        const prompt = _this.allPrompts.find(p => p.id === promptId);\n        if (prompt) {\n          try {\n            yield navigator.clipboard.writeText(JSON.stringify(prompt, null, 2));\n            _this.dialogService.success({\n              title: 'Copied!',\n              message: 'Prompt copied to clipboard successfully.'\n            });\n          } catch (err) {\n            _this.dialogService.error({\n              title: 'Copy Failed',\n              message: 'Failed to copy prompt to clipboard.'\n            });\n          }\n        }\n      })();\n    }\n    getCustomTags(categoryName, domainName) {\n      const tags = [];\n      if (categoryName) tags.push({\n        label: categoryName,\n        type: 'primary'\n      });\n      if (domainName) tags.push({\n        label: domainName,\n        type: 'secondary'\n      });\n      return tags;\n    }\n    getTagSummary(tags) {\n      return tags.map(tag => tag.label).join(', ');\n    }\n    setInitialPageFromQueryParam() {\n      const pageParam = this.route.snapshot.queryParamMap.get('page');\n      if (pageParam) {\n        const page = parseInt(pageParam, 10);\n        if (!isNaN(page)) this.currentPage = page;\n      }\n    }\n    initSearchListener() {\n      this.searchForm.get('search').valueChanges.pipe(startWith(''), debounceTime(300), distinctUntilChanged(), map(value => value?.toLowerCase() ?? ''), takeUntil(this.destroy$)).subscribe(searchText => {\n        this.filterPrompts(searchText);\n      });\n    }\n    updateDisplayedPrompts() {\n      this.itemsPerPage = this.currentPage === 1 ? 12 : 11;\n      const {\n        displayedItems,\n        totalPages\n      } = this.paginationService.getPaginatedItems(this.filteredPrompts, this.currentPage, this.itemsPerPage);\n      this.displayedPrompts = displayedItems;\n      this.totalPages = totalPages;\n    }\n    filterPrompts(searchText) {\n      this.filteredPrompts = this.allPrompts.filter(prompt => {\n        const titleMatch = prompt.title?.toLowerCase().includes(searchText);\n        const descriptionMatch = prompt.description?.toLowerCase().includes(searchText);\n        const tagMatch = prompt.tags?.some(tag => tag.label?.toLowerCase().includes(searchText));\n        return titleMatch || descriptionMatch || tagMatch;\n      });\n      this.currentPage = 1;\n      this.updateDisplayedPrompts();\n    }\n    static ɵfac = function PromptsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PromptsComponent)(i0.ɵɵdirectiveInject(i1.PaginationService), i0.ɵɵdirectiveInject(i2.PromptsService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i4.FormBuilder), i0.ɵɵdirectiveInject(i5.DialogService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PromptsComponent,\n      selectors: [[\"app-prompts\"]],\n      decls: 13,\n      vars: 10,\n      consts: [[\"id\", \"prompts-container\", 1, \"container-fluid\"], [\"id\", \"search-filter-container\", 1, \"row\", \"g-3\"], [1, \"col-12\", \"col-md-8\", \"col-lg-9\", \"col-xl-10\", \"search-section\"], [3, \"formGroup\"], [\"placeholder\", \"Search \\\"Prompts\\\"\", \"hoverEffect\", \"glow\", \"pressedEffect\", \"solid\", \"formControlName\", \"search\"], [\"slot\", \"icon-start\", \"iconName\", \"search\", \"iconColor\", \"var(--color-brand-primary)\", 3, \"iconSize\"], [1, \"col-12\", \"col-md-4\", \"col-lg-3\", \"col-xl-2\", \"action-buttons\"], [\"dropdownTitle\", \"choose prompt\", 3, \"selectionChange\", \"options\"], [\"id\", \"prompts-card-container\", 1, \"row\", \"g-3\"], [\"iconColor\", \"#144692\", 1, \"col-12\", \"col-sm-6\", \"col-md-4\", \"col-lg-3\", \"col-xl-3\", \"col-xxl-2\", \"mt-5\", 3, \"cardClick\", \"type\", \"iconName\", \"title\", \"isLoading\"], [\"class\", \"col-12 d-flex justify-content-center align-items-center py-5\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"row\", 4, \"ngIf\"], [1, \"col-12\", \"d-flex\", \"justify-content-center\", \"align-items-center\", \"py-5\"], [1, \"text-center\"], [1, \"text-muted\"], [\"categoryIcon\", \"plus\", \"categoryTitle\", \"Prompts\", \"categoryValue\", \"42\", 1, \"col-12\", \"col-sm-6\", \"col-md-4\", \"col-lg-3\", \"col-xl-3\", \"col-xxl-2\", \"mt-5\", 3, \"actionClick\", \"title\", \"description\", \"author\", \"date\", \"actions\", \"skeleton\"], [1, \"row\"], [1, \"col-12\", \"d-flex\", \"justify-content-center\", \"mt-4\"], [3, \"pageChange\", \"totalItems\", \"currentPage\", \"itemsPerPage\"]],\n      template: function PromptsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"form\", 3)(4, \"ava-textbox\", 4);\n          i0.ɵɵelement(5, \"ava-icon\", 5);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(6, \"div\", 6)(7, \"ava-dropdown\", 7);\n          i0.ɵɵlistener(\"selectionChange\", function PromptsComponent_Template_ava_dropdown_selectionChange_7_listener($event) {\n            return ctx.onSelectionChange($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(8, \"div\", 8)(9, \"ava-text-card\", 9);\n          i0.ɵɵlistener(\"cardClick\", function PromptsComponent_Template_ava_text_card_cardClick_9_listener() {\n            return ctx.onCreatePrompt();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(10, PromptsComponent_div_10_Template, 4, 1, \"div\", 10)(11, PromptsComponent_ng_container_11_Template, 3, 8, \"ng-container\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(12, PromptsComponent_div_12_Template, 3, 3, \"div\", 12);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"formGroup\", ctx.searchForm);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"iconSize\", 16);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"options\", ctx.simpleOptions);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"type\", \"create\")(\"iconName\", \"plus\")(\"title\", ctx.promptLabels.createPrompt)(\"isLoading\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.filteredPrompts.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.isLoading && ctx.displayedPrompts.length === 0 ? ctx.cardSkeletonPlaceholders : ctx.displayedPrompts);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.filteredPrompts.length > 0);\n        }\n      },\n      dependencies: [CommonModule, i6.NgForOf, i6.NgIf, PageFooterComponent, TextCardComponent, AvaTextboxComponent, DropdownComponent, LucideAngularModule, IconComponent, ReactiveFormsModule, i4.ɵNgNoValidate, i4.NgControlStatus, i4.NgControlStatusGroup, i4.FormGroupDirective, i4.FormControlName, ConsoleCardComponent, TimeAgoPipe],\n      styles: [\".ava-dropdown {\\n  width: 100% !important;\\n}\\n\\n.mt-5[_ngcontent-%COMP%] {\\n  margin-top: 2rem;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3Byb2plY3RzL3NoYXJlZC9wYWdlcy9saWJyYXJpZXMvcHJvbXB0cy9wcm9tcHRzLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0Usc0JBQUE7QUFDRjs7QUFFQTtFQUNFLGdCQUFBO0FBQ0YiLCJzb3VyY2VzQ29udGVudCI6WyI6Om5nLWRlZXAgLmF2YS1kcm9wZG93biB7XHJcbiAgd2lkdGg6IDEwMCUgIWltcG9ydGFudDtcclxufVxyXG5cclxuLm10LTUge1xyXG4gIG1hcmdpbi10b3A6IDJyZW07XHJcbn1cclxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n  return PromptsComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "takeUntil", "of", "map", "catchError", "Subject", "debounceTime", "distinctUntilChanged", "startWith", "PageFooterComponent", "formatToDisplayDate", "prompts<PERSON><PERSON><PERSON>", "PROMPTS_BASE_ACTIONS", "AvaTextboxComponent", "TextCardComponent", "DropdownComponent", "IconComponent", "LucideAngularModule", "ReactiveFormsModule", "ConsoleCardComponent", "TimeAgoPipe", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "prompt<PERSON><PERSON><PERSON>", "noResults", "ɵɵelementContainerStart", "ɵɵlistener", "PromptsComponent_ng_container_11_Template_ava_console_card_actionClick_1_listener", "$event", "prompt_r3", "ɵɵrestoreView", "_r2", "$implicit", "ɵɵnextContext", "ɵɵresetView", "onActionClick", "id", "ɵɵproperty", "title", "description", "owner", "ɵɵpipeBind1", "createdDate", "defaultActions", "isLoading", "PromptsComponent_div_12_Template_app_page_footer_pageChange_2_listener", "_r4", "onPageChange", "filteredPrompts", "length", "currentPage", "itemsPerPage", "PromptsComponent", "paginationService", "promptsService", "router", "route", "fb", "dialogService", "showSuccessPopup", "popupMessage", "popupTitle", "iconName", "submissionSuccess", "showDeletePopup", "promptToDelete", "simpleOptions", "name", "value", "icon", "label", "tooltip", "isPrimary", "labels", "allPrompts", "displayedPrompts", "searchForm", "totalPages", "destroy$", "selectedData", "cardSkeletonPlaceholders", "Array", "constructor", "group", "search", "ngOnInit", "initSearchListener", "fetchAllPrompts", "pipe", "transformResponseToCardData", "bind", "error", "console", "prompts", "subscribe", "next", "updateDisplayedPrompts", "setInitialPageFromQueryParam", "err", "message", "complete", "onSelectionChange", "data", "onCreatePrompt", "navigate", "getHeaderIcons", "prompt", "toolType", "userCount", "getFooterIcons", "page", "relativeTo", "queryParams", "queryParamsHandling", "event", "promptId", "actionId", "showDeleteConfirmation", "executePrompt", "viewPrompt", "view", "returnPage", "find", "p", "confirmation", "confirmButtonText", "cancelButtonText", "confirmButtonVariant", "then", "result", "confirmed", "deletePrompt", "loading", "showProgress", "showCancelButton", "res", "close", "success", "filter", "showRetryButton", "retryButtonText", "action", "response", "formatPromptCard", "item", "updatedAt", "categoryName", "domainName", "tags", "rest", "customTags", "getCustomTags", "allTags", "tagSummary", "getTagSummary", "actions", "execute", "copyPrompt", "_this", "_asyncToGenerator", "navigator", "clipboard", "writeText", "JSON", "stringify", "push", "type", "tag", "join", "pageParam", "snapshot", "queryParamMap", "get", "parseInt", "isNaN", "valueChanges", "toLowerCase", "searchText", "filterPrompts", "displayedItems", "getPaginatedItems", "titleMatch", "includes", "descriptionMatch", "tagMatch", "some", "ɵɵdirectiveInject", "i1", "PaginationService", "i2", "PromptsService", "i3", "Router", "ActivatedRoute", "i4", "FormBuilder", "i5", "DialogService", "selectors", "decls", "vars", "consts", "template", "PromptsComponent_Template", "rf", "ctx", "ɵɵelement", "PromptsComponent_Template_ava_dropdown_selectionChange_7_listener", "PromptsComponent_Template_ava_text_card_cardClick_9_listener", "ɵɵtemplate", "PromptsComponent_div_10_Template", "PromptsComponent_ng_container_11_Template", "PromptsComponent_div_12_Template", "createPrompt", "i6", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "ɵNgNoValidate", "NgControlStatus", "NgControlStatusGroup", "FormGroupDirective", "FormControlName", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\pages\\libraries\\prompts\\prompts.component.ts", "C:\\console\\aava-ui-web\\projects\\shared\\pages\\libraries\\prompts\\prompts.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { Router, ActivatedRoute } from '@angular/router';\r\nimport {\r\n  takeUntil,\r\n  of,\r\n  map,\r\n  catchError,\r\n  Subject,\r\n  debounceTime,\r\n  distinctUntilChanged,\r\n  startWith,\r\n} from 'rxjs';\r\nimport { CardData, CardTag } from '@shared/models/card.model';\r\nimport { PageFooterComponent } from '@shared/components/page-footer/page-footer.component';\r\nimport { PaginationService } from '@shared/services/pagination.service';\r\nimport { PromptsService } from '@shared/services/prompts.service';\r\nimport { formatToDisplayDate } from '@shared/utils/date-utils';\r\nimport promptsLabels from './constants/prompts.json';\r\nimport { PROMPTS_BASE_ACTIONS } from './prompts-actions';\r\nimport {\r\n  AvaTextboxComponent,\r\n  TextCardComponent,\r\n  DropdownComponent,\r\n  DropdownOption,\r\n  IconComponent,\r\n  PopupComponent,\r\n  DialogService\r\n} from '@ava/play-comp-library';\r\nimport { LucideAngularModule } from 'lucide-angular';\r\nimport { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';\r\nimport {\r\n  ConsoleCardAction,\r\n  ConsoleCardComponent,\r\n} from '@shared/components/console-card/console-card.component';\r\nimport { TimeAgoPipe } from '@shared/pipes/time-ago.pipe';\r\n\r\n@Component({\r\n  selector: 'app-prompts',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    PageFooterComponent,\r\n    TextCardComponent,\r\n    AvaTextboxComponent,\r\n    DropdownComponent,\r\n    LucideAngularModule,\r\n    IconComponent,\r\n    PopupComponent,\r\n    ReactiveFormsModule,\r\n    ConsoleCardComponent,\r\n    TimeAgoPipe,\r\n  ],\r\n  templateUrl: './prompts.component.html',\r\n  styleUrl: './prompts.component.scss',\r\n})\r\nexport class PromptsComponent implements OnInit {\r\n  // Popup state for success messages\r\n  showSuccessPopup = false;\r\n  popupMessage = '';\r\n  popupTitle = '';\r\n  iconName = 'info';\r\n  submissionSuccess = false;\r\n  // popup for delete confirmation\r\n  showDeletePopup: boolean = false;\r\n  promptToDelete: CardData | null = null;\r\n\r\n  simpleOptions: DropdownOption[] = [\r\n    { name: 'Option 1', value: '1' },\r\n    { name: 'Option 2', value: '2' },\r\n    { name: 'Option 3', value: '3' },\r\n    { name: 'Option 4', value: '4' },\r\n    { name: 'Option 5', value: '5' },\r\n  ];\r\n\r\n  defaultActions: ConsoleCardAction[] = [\r\n    {\r\n      id: 'delete',\r\n      icon: 'trash',\r\n      label: 'Delete item',\r\n      tooltip: 'Delete',\r\n    },\r\n    {\r\n      id: 'edit',\r\n      icon: 'edit',\r\n      label: 'Edit item',\r\n      tooltip: 'Edit',\r\n    },\r\n\r\n    {\r\n      id: 'view',\r\n      icon: 'eye',\r\n      label: 'View Prompts',\r\n      tooltip: 'View',\r\n      isPrimary: true,\r\n    },\r\n  ];\r\n  public promptLabels = promptsLabels.labels;\r\n  allPrompts: CardData[] = [];\r\n  filteredPrompts: CardData[] = [];\r\n  displayedPrompts: CardData[] = [];\r\n  searchForm!: FormGroup;\r\n  isLoading = false;\r\n  currentPage: number = 1;\r\n  itemsPerPage: number = 11;\r\n  totalPages: number = 1;\r\n  protected destroy$ = new Subject<void>();\r\n  selectedData: any = null;\r\n  cardSkeletonPlaceholders = Array(11);\r\n\r\n  constructor(\r\n    private paginationService: PaginationService,\r\n    private promptsService: PromptsService,\r\n    private router: Router,\r\n    private route: ActivatedRoute,\r\n    private fb: FormBuilder,\r\n    private dialogService: DialogService\r\n  ) {\r\n    this.searchForm = this.fb.group({\r\n      search: [''],\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.isLoading = true;\r\n    this.initSearchListener();\r\n    this.promptsService\r\n      .fetchAllPrompts()\r\n      .pipe(\r\n        takeUntil(this.destroy$),\r\n        map(this.transformResponseToCardData.bind(this)),\r\n        catchError((error) => {\r\n          console.error('Error fetching prompts:', error);\r\n          this.isLoading = false;\r\n          return of({ prompts: [] });\r\n        }),\r\n      )\r\n      .subscribe({\r\n        next: ({ prompts }) => {\r\n          this.allPrompts = prompts;\r\n          this.filteredPrompts = [...prompts];\r\n          this.updateDisplayedPrompts();\r\n          this.setInitialPageFromQueryParam();\r\n        },\r\n        error: (err) => console.error('Subscription error:', err.message),\r\n        complete: () => {\r\n          this.isLoading = false;\r\n        },\r\n      });\r\n  }\r\n\r\n  onSelectionChange(data: any) {\r\n    this.selectedData = data;\r\n  }\r\n\r\n  onCreatePrompt(): void {\r\n    this.router.navigate(['/libraries/prompts/create']);\r\n  }\r\n\r\n  getHeaderIcons(prompt: any): { iconName: string; title: string }[] {\r\n    return [\r\n      { iconName: 'NotebookText', title: prompt.toolType || 'Prompt' },\r\n      { iconName: 'users', title: `${prompt.userCount || 30}` },\r\n    ];\r\n  }\r\n\r\n  getFooterIcons(prompt: any): { iconName: string; title: string }[] {\r\n    return [\r\n      { iconName: 'user', title: prompt.owner || 'AAVA' },\r\n      { iconName: 'calendar-days', title: prompt.createdDate },\r\n    ];\r\n  }\r\n\r\n  onPageChange(page: number): void {\r\n    this.currentPage = page;\r\n    this.updateDisplayedPrompts();\r\n    this.router.navigate([], {\r\n      relativeTo: this.route,\r\n      queryParams: { page: this.currentPage },\r\n      queryParamsHandling: 'merge',\r\n    });\r\n  }\r\n\r\n  onActionClick(\r\n    event: { actionId: string; action: ConsoleCardAction },\r\n    promptId: string,\r\n  ): void {\r\n    switch (event.actionId) {\r\n      case 'delete':\r\n        this.showDeleteConfirmation(promptId);\r\n        break;\r\n      case 'edit':\r\n        this.executePrompt(promptId);\r\n        break;\r\n      case 'view':\r\n        this.viewPrompt(promptId);\r\n        break;\r\n      default:\r\n        break;\r\n    }\r\n  }\r\n\r\n  private viewPrompt(promptId: string): void {\r\n    this.router.navigate(['/libraries/prompts/edit', promptId], {\r\n      queryParams: { view: 'true', returnPage: this.currentPage },\r\n    });\r\n  }\r\n\r\n  private showDeleteConfirmation(promptId: string): void {\r\n    const prompt = this.allPrompts.find((p) => p.id === promptId);\r\n    if (!prompt) return;\r\n\r\n    this.dialogService.confirmation({\r\n      title: 'Delete Prompt?',\r\n      message: `Are you sure you want to delete \"${prompt.title || ''}\"?`,\r\n      confirmButtonText: 'Delete',\r\n      cancelButtonText: 'Cancel',\r\n      confirmButtonVariant: 'danger',\r\n      icon:'trash'\r\n    }).then(result => {\r\n      if (result.confirmed) {\r\n        this.deletePrompt(promptId);\r\n      }\r\n    });\r\n  }\r\n\r\n  private deletePrompt(promptId: string): void {\r\n       // Show loading dialog\r\n    this.dialogService.loading({\r\n      title: 'Deleting Prompt...',\r\n      message: 'Please wait while we delete the prompt.',\r\n      showProgress: false,\r\n      showCancelButton: false\r\n    });\r\n\r\n    this.promptsService.deletePrompt(promptId).subscribe({\r\n      next: (res) => {\r\n        this.dialogService.close(); // Close loading dialog\r\n        if (res && res.success !== false) {\r\n          // Update local prompt lists\r\n          this.allPrompts = this.allPrompts.filter((p) => p.id !== promptId);\r\n          this.filteredPrompts = this.filteredPrompts.filter(\r\n            (p) => p.id !== promptId,\r\n          );\r\n          this.updateDisplayedPrompts();\r\n\r\n          // Show success dialog\r\n          this.dialogService.success({\r\n            title: 'Success!',\r\n            message: 'Prompt deleted successfully.'\r\n          });\r\n        } else {\r\n          this.dialogService.error({\r\n            title: 'Error',\r\n            message: 'Failed to delete prompt.'\r\n          });\r\n        }\r\n      },\r\n      error: (err) => {\r\n        this.dialogService.close(); // Close loading dialog\r\n        console.error('Failed to delete prompt:', err);\r\n        this.dialogService.error({\r\n          title: 'Error',\r\n          message: 'An unexpected error occurred while deleting the prompt.',\r\n          showRetryButton: true,\r\n          retryButtonText: 'Retry'\r\n        }).then(result => {\r\n          if (result.action === 'retry') {\r\n            this.deletePrompt(promptId);\r\n          }\r\n        });\r\n      },\r\n    });\r\n  }\r\n\r\n  private transformResponseToCardData(\r\n    response: CardData[] | { prompts: CardData[] },\r\n  ): { prompts: (CardData & { tagSummary: string })[] } {\r\n    const prompts = 'prompts' in response ? response.prompts : response;\r\n    return {\r\n      prompts: prompts.map(this.formatPromptCard.bind(this)),\r\n    };\r\n  }\r\n\r\n  private formatPromptCard(item: any): CardData & { tagSummary: string } {\r\n    const {\r\n      name,\r\n      updatedAt,\r\n      categoryName,\r\n      domainName,\r\n      tags = [],\r\n      ...rest\r\n    } = item;\r\n    const customTags = this.getCustomTags(categoryName, domainName);\r\n    const allTags = [...tags, ...customTags];\r\n    const tagSummary = this.getTagSummary(allTags);\r\n    const createdDate = formatToDisplayDate(updatedAt);\r\n    return {\r\n      title: name,\r\n      createdDate,\r\n      tags: allTags,\r\n      tagSummary,\r\n      actions: PROMPTS_BASE_ACTIONS,\r\n      ...rest,\r\n    };\r\n  }\r\n\r\n  private executePrompt(promptId: string): void {\r\n    this.router.navigate(['/libraries/prompts/edit', promptId], {\r\n      queryParams: { execute: 'true', returnPage: this.currentPage },\r\n    });\r\n  }\r\n\r\n  private async copyPrompt(promptId: string): Promise<void> {\r\n    if (!promptId) return;\r\n    const prompt = this.allPrompts.find((p) => p.id === promptId);\r\n    if (prompt) {\r\n      try {\r\n        await navigator.clipboard.writeText(JSON.stringify(prompt, null, 2));\r\n        this.dialogService.success({\r\n          title: 'Copied!',\r\n          message: 'Prompt copied to clipboard successfully.'\r\n        });\r\n      } catch (err) {\r\n        this.dialogService.error({\r\n          title: 'Copy Failed',\r\n          message: 'Failed to copy prompt to clipboard.'\r\n        });\r\n      }\r\n    }\r\n  }\r\n\r\n  private getCustomTags(categoryName?: string, domainName?: string): CardTag[] {\r\n    const tags: CardTag[] = [];\r\n    if (categoryName) tags.push({ label: categoryName, type: 'primary' });\r\n    if (domainName) tags.push({ label: domainName, type: 'secondary' });\r\n    return tags;\r\n  }\r\n\r\n  private getTagSummary(tags: CardTag[]): string {\r\n    return tags.map((tag) => tag.label).join(', ');\r\n  }\r\n\r\n  private setInitialPageFromQueryParam(): void {\r\n    const pageParam = this.route.snapshot.queryParamMap.get('page');\r\n    if (pageParam) {\r\n      const page = parseInt(pageParam, 10);\r\n      if (!isNaN(page)) this.currentPage = page;\r\n    }\r\n  }\r\n\r\n  private initSearchListener(): void {\r\n    this.searchForm\r\n      .get('search')!\r\n      .valueChanges.pipe(\r\n        startWith(''),\r\n        debounceTime(300),\r\n        distinctUntilChanged(),\r\n        map((value) => value?.toLowerCase() ?? ''),\r\n        takeUntil(this.destroy$),\r\n      )\r\n      .subscribe((searchText) => {\r\n        this.filterPrompts(searchText);\r\n      });\r\n  }\r\n\r\n  private updateDisplayedPrompts(): void {\r\n    this.itemsPerPage = this.currentPage === 1 ? 12 : 11;\r\n    const { displayedItems, totalPages } =\r\n      this.paginationService.getPaginatedItems(\r\n        this.filteredPrompts,\r\n        this.currentPage,\r\n        this.itemsPerPage,\r\n      );\r\n    this.displayedPrompts = displayedItems;\r\n    this.totalPages = totalPages;\r\n  }\r\n\r\n  private filterPrompts(searchText: string): void {\r\n    this.filteredPrompts = this.allPrompts.filter((prompt) => {\r\n      const titleMatch = prompt.title?.toLowerCase().includes(searchText);\r\n      const descriptionMatch = prompt.description\r\n        ?.toLowerCase()\r\n        .includes(searchText);\r\n      const tagMatch = prompt.tags?.some((tag) =>\r\n        tag.label?.toLowerCase().includes(searchText),\r\n      );\r\n      return titleMatch || descriptionMatch || tagMatch;\r\n    });\r\n    this.currentPage = 1;\r\n    this.updateDisplayedPrompts();\r\n  }\r\n}\r\n", "<div id=\"prompts-container\" class=\"container-fluid\">\r\n  <div id=\"search-filter-container\" class=\"row g-3\">\r\n    <div class=\"col-12 col-md-8 col-lg-9 col-xl-10 search-section\">\r\n      <form [formGroup]=\"searchForm\">\r\n        <ava-textbox\r\n          placeholder='Search \"Prompts\"'\r\n          hoverEffect=\"glow\"\r\n          pressedEffect=\"solid\"\r\n          formControlName=\"search\"\r\n        >\r\n          <ava-icon\r\n            slot=\"icon-start\"\r\n            iconName=\"search\"\r\n            [iconSize]=\"16\"\r\n            iconColor=\"var(--color-brand-primary)\"\r\n          >\r\n          </ava-icon>\r\n        </ava-textbox>\r\n      </form>\r\n    </div>\r\n    <div class=\"col-12 col-md-4 col-lg-3 col-xl-2 action-buttons\">\r\n      <ava-dropdown\r\n        dropdownTitle=\"choose prompt\"\r\n        [options]=\"simpleOptions\"\r\n        (selectionChange)=\"onSelectionChange($event)\"\r\n      >\r\n      </ava-dropdown>\r\n    </div>\r\n  </div>\r\n\r\n  <div id=\"prompts-card-container\" class=\"row g-3\">\r\n    <ava-text-card\r\n      class=\"col-12 col-sm-6 col-md-4 col-lg-3 col-xl-3 col-xxl-2 mt-5\"\r\n      [type]=\"'create'\"\r\n      [iconName]=\"'plus'\"\r\n      iconColor=\"#144692\"\r\n      [title]=\"promptLabels.createPrompt\"\r\n      (cardClick)=\"onCreatePrompt()\"\r\n      [isLoading]=\"isLoading\"\r\n    >\r\n    </ava-text-card>\r\n\r\n    <!-- No Results Message -->\r\n    <div\r\n      class=\"col-12 d-flex justify-content-center align-items-center py-5\"\r\n      *ngIf=\"!isLoading && filteredPrompts.length === 0\"\r\n    >\r\n      <div class=\"text-center\">\r\n        <h5 class=\"text-muted\">{{ promptLabels.noResults }}</h5>\r\n      </div>\r\n    </div>\r\n\r\n    <ng-container\r\n      *ngFor=\"\r\n        let prompt of isLoading && displayedPrompts.length === 0\r\n          ? cardSkeletonPlaceholders\r\n          : displayedPrompts\r\n      \"\r\n    >\r\n      <ava-console-card\r\n        class=\"col-12 col-sm-6 col-md-4 col-lg-3 col-xl-3 col-xxl-2 mt-5\"\r\n        [title]=\"prompt?.title\"\r\n        [description]=\"prompt?.description\"\r\n        categoryIcon=\"plus\"\r\n        categoryTitle=\"Prompts\"\r\n        categoryValue=\"42\"\r\n        [author]=\"prompt?.owner || 'AAVA'\"\r\n        [date]=\"prompt?.createdDate | timeAgo\"\r\n        [actions]=\"defaultActions\"\r\n        (actionClick)=\"onActionClick($event, prompt.id)\"\r\n        [skeleton]=\"isLoading\"\r\n      >\r\n      </ava-console-card>\r\n    </ng-container>\r\n  </div>\r\n\r\n  <!-- Pagination Footer -->\r\n  <div class=\"row\" *ngIf=\"filteredPrompts.length > 0\">\r\n    <div class=\"col-12 d-flex justify-content-center mt-4\">\r\n      <app-page-footer\r\n        [totalItems]=\"filteredPrompts.length + 1\"\r\n        [currentPage]=\"currentPage\"\r\n        [itemsPerPage]=\"itemsPerPage\"\r\n        (pageChange)=\"onPageChange($event)\"\r\n      ></app-page-footer>\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n\r\n"], "mappings": ";AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SACEC,SAAS,EACTC,EAAE,EACFC,GAAG,EACHC,UAAU,EACVC,OAAO,EACPC,YAAY,EACZC,oBAAoB,EACpBC,SAAS,QACJ,MAAM;AAEb,SAASC,mBAAmB,QAAQ,sDAAsD;AAG1F,SAASC,mBAAmB,QAAQ,0BAA0B;AAC9D,OAAOC,aAAa,MAAM,0BAA0B;AACpD,SAASC,oBAAoB,QAAQ,mBAAmB;AACxD,SACEC,mBAAmB,EACnBC,iBAAiB,EACjBC,iBAAiB,EAEjBC,aAAa,QAGR,wBAAwB;AAC/B,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAAiCC,mBAAmB,QAAQ,gBAAgB;AAC5E,SAEEC,oBAAoB,QACf,wDAAwD;AAC/D,SAASC,WAAW,QAAQ,6BAA6B;;;;;;;;;;ICajDC,EALJ,CAAAC,cAAA,cAGC,cAC0B,aACA;IAAAD,EAAA,CAAAE,MAAA,GAA4B;IAEvDF,EAFuD,CAAAG,YAAA,EAAK,EACpD,EACF;;;;IAFqBH,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,YAAA,CAAAC,SAAA,CAA4B;;;;;;IAIvDR,EAAA,CAAAS,uBAAA,GAMC;IACCT,EAAA,CAAAC,cAAA,2BAYC;;IAFCD,EAAA,CAAAU,UAAA,yBAAAC,kFAAAC,MAAA;MAAA,MAAAC,SAAA,GAAAb,EAAA,CAAAc,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAV,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAeZ,MAAA,CAAAa,aAAA,CAAAP,MAAA,EAAAC,SAAA,CAAAO,EAAA,CAAgC;IAAA,EAAC;IAGlDpB,EAAA,CAAAG,YAAA,EAAmB;;;;;;IAXjBH,EAAA,CAAAI,SAAA,EAAuB;IASvBJ,EATA,CAAAqB,UAAA,UAAAR,SAAA,kBAAAA,SAAA,CAAAS,KAAA,CAAuB,gBAAAT,SAAA,kBAAAA,SAAA,CAAAU,WAAA,CACY,YAAAV,SAAA,kBAAAA,SAAA,CAAAW,KAAA,YAID,SAAAxB,EAAA,CAAAyB,WAAA,OAAAZ,SAAA,kBAAAA,SAAA,CAAAa,WAAA,EACI,YAAApB,MAAA,CAAAqB,cAAA,CACZ,aAAArB,MAAA,CAAAsB,SAAA,CAEJ;;;;;;IASxB5B,EAFJ,CAAAC,cAAA,cAAoD,cACK,0BAMpD;IADCD,EAAA,CAAAU,UAAA,wBAAAmB,uEAAAjB,MAAA;MAAAZ,EAAA,CAAAc,aAAA,CAAAgB,GAAA;MAAA,MAAAxB,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAcZ,MAAA,CAAAyB,YAAA,CAAAnB,MAAA,CAAoB;IAAA,EAAC;IAGzCZ,EAFK,CAAAG,YAAA,EAAkB,EACf,EACF;;;;IANAH,EAAA,CAAAI,SAAA,GAAyC;IAEzCJ,EAFA,CAAAqB,UAAA,eAAAf,MAAA,CAAA0B,eAAA,CAAAC,MAAA,KAAyC,gBAAA3B,MAAA,CAAA4B,WAAA,CACd,iBAAA5B,MAAA,CAAA6B,YAAA,CACE;;;AD1BrC,WAAaC,gBAAgB;EAAvB,MAAOA,gBAAgB;IAuDjBC,iBAAA;IACAC,cAAA;IACAC,MAAA;IACAC,KAAA;IACAC,EAAA;IACAC,aAAA;IA3DV;IACAC,gBAAgB,GAAG,KAAK;IACxBC,YAAY,GAAG,EAAE;IACjBC,UAAU,GAAG,EAAE;IACfC,QAAQ,GAAG,MAAM;IACjBC,iBAAiB,GAAG,KAAK;IACzB;IACAC,eAAe,GAAY,KAAK;IAChCC,cAAc,GAAoB,IAAI;IAEtCC,aAAa,GAAqB,CAChC;MAAEC,IAAI,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAG,CAAE,EAChC;MAAED,IAAI,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAG,CAAE,EAChC;MAAED,IAAI,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAG,CAAE,EAChC;MAAED,IAAI,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAG,CAAE,EAChC;MAAED,IAAI,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAG,CAAE,CACjC;IAEDzB,cAAc,GAAwB,CACpC;MACEP,EAAE,EAAE,QAAQ;MACZiC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,aAAa;MACpBC,OAAO,EAAE;KACV,EACD;MACEnC,EAAE,EAAE,MAAM;MACViC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,WAAW;MAClBC,OAAO,EAAE;KACV,EAED;MACEnC,EAAE,EAAE,MAAM;MACViC,IAAI,EAAE,KAAK;MACXC,KAAK,EAAE,cAAc;MACrBC,OAAO,EAAE,MAAM;MACfC,SAAS,EAAE;KACZ,CACF;IACMjD,YAAY,GAAGjB,aAAa,CAACmE,MAAM;IAC1CC,UAAU,GAAe,EAAE;IAC3B1B,eAAe,GAAe,EAAE;IAChC2B,gBAAgB,GAAe,EAAE;IACjCC,UAAU;IACVhC,SAAS,GAAG,KAAK;IACjBM,WAAW,GAAW,CAAC;IACvBC,YAAY,GAAW,EAAE;IACzB0B,UAAU,GAAW,CAAC;IACZC,QAAQ,GAAG,IAAI9E,OAAO,EAAQ;IACxC+E,YAAY,GAAQ,IAAI;IACxBC,wBAAwB,GAAGC,KAAK,CAAC,EAAE,CAAC;IAEpCC,YACU7B,iBAAoC,EACpCC,cAA8B,EAC9BC,MAAc,EACdC,KAAqB,EACrBC,EAAe,EACfC,aAA4B;MAL5B,KAAAL,iBAAiB,GAAjBA,iBAAiB;MACjB,KAAAC,cAAc,GAAdA,cAAc;MACd,KAAAC,MAAM,GAANA,MAAM;MACN,KAAAC,KAAK,GAALA,KAAK;MACL,KAAAC,EAAE,GAAFA,EAAE;MACF,KAAAC,aAAa,GAAbA,aAAa;MAErB,IAAI,CAACkB,UAAU,GAAG,IAAI,CAACnB,EAAE,CAAC0B,KAAK,CAAC;QAC9BC,MAAM,EAAE,CAAC,EAAE;OACZ,CAAC;IACJ;IAEAC,QAAQA,CAAA;MACN,IAAI,CAACzC,SAAS,GAAG,IAAI;MACrB,IAAI,CAAC0C,kBAAkB,EAAE;MACzB,IAAI,CAAChC,cAAc,CAChBiC,eAAe,EAAE,CACjBC,IAAI,CACH5F,SAAS,CAAC,IAAI,CAACkF,QAAQ,CAAC,EACxBhF,GAAG,CAAC,IAAI,CAAC2F,2BAA2B,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC,EAChD3F,UAAU,CAAE4F,KAAK,IAAI;QACnBC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAAC/C,SAAS,GAAG,KAAK;QACtB,OAAO/C,EAAE,CAAC;UAAEgG,OAAO,EAAE;QAAE,CAAE,CAAC;MAC5B,CAAC,CAAC,CACH,CACAC,SAAS,CAAC;QACTC,IAAI,EAAEA,CAAC;UAAEF;QAAO,CAAE,KAAI;UACpB,IAAI,CAACnB,UAAU,GAAGmB,OAAO;UACzB,IAAI,CAAC7C,eAAe,GAAG,CAAC,GAAG6C,OAAO,CAAC;UACnC,IAAI,CAACG,sBAAsB,EAAE;UAC7B,IAAI,CAACC,4BAA4B,EAAE;QACrC,CAAC;QACDN,KAAK,EAAGO,GAAG,IAAKN,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEO,GAAG,CAACC,OAAO,CAAC;QACjEC,QAAQ,EAAEA,CAAA,KAAK;UACb,IAAI,CAACxD,SAAS,GAAG,KAAK;QACxB;OACD,CAAC;IACN;IAEAyD,iBAAiBA,CAACC,IAAS;MACzB,IAAI,CAACvB,YAAY,GAAGuB,IAAI;IAC1B;IAEAC,cAAcA,CAAA;MACZ,IAAI,CAAChD,MAAM,CAACiD,QAAQ,CAAC,CAAC,2BAA2B,CAAC,CAAC;IACrD;IAEAC,cAAcA,CAACC,MAAW;MACxB,OAAO,CACL;QAAE5C,QAAQ,EAAE,cAAc;QAAExB,KAAK,EAAEoE,MAAM,CAACC,QAAQ,IAAI;MAAQ,CAAE,EAChE;QAAE7C,QAAQ,EAAE,OAAO;QAAExB,KAAK,EAAE,GAAGoE,MAAM,CAACE,SAAS,IAAI,EAAE;MAAE,CAAE,CAC1D;IACH;IAEAC,cAAcA,CAACH,MAAW;MACxB,OAAO,CACL;QAAE5C,QAAQ,EAAE,MAAM;QAAExB,KAAK,EAAEoE,MAAM,CAAClE,KAAK,IAAI;MAAM,CAAE,EACnD;QAAEsB,QAAQ,EAAE,eAAe;QAAExB,KAAK,EAAEoE,MAAM,CAAChE;MAAW,CAAE,CACzD;IACH;IAEAK,YAAYA,CAAC+D,IAAY;MACvB,IAAI,CAAC5D,WAAW,GAAG4D,IAAI;MACvB,IAAI,CAACd,sBAAsB,EAAE;MAC7B,IAAI,CAACzC,MAAM,CAACiD,QAAQ,CAAC,EAAE,EAAE;QACvBO,UAAU,EAAE,IAAI,CAACvD,KAAK;QACtBwD,WAAW,EAAE;UAAEF,IAAI,EAAE,IAAI,CAAC5D;QAAW,CAAE;QACvC+D,mBAAmB,EAAE;OACtB,CAAC;IACJ;IAEA9E,aAAaA,CACX+E,KAAsD,EACtDC,QAAgB;MAEhB,QAAQD,KAAK,CAACE,QAAQ;QACpB,KAAK,QAAQ;UACX,IAAI,CAACC,sBAAsB,CAACF,QAAQ,CAAC;UACrC;QACF,KAAK,MAAM;UACT,IAAI,CAACG,aAAa,CAACH,QAAQ,CAAC;UAC5B;QACF,KAAK,MAAM;UACT,IAAI,CAACI,UAAU,CAACJ,QAAQ,CAAC;UACzB;QACF;UACE;MACJ;IACF;IAEQI,UAAUA,CAACJ,QAAgB;MACjC,IAAI,CAAC5D,MAAM,CAACiD,QAAQ,CAAC,CAAC,yBAAyB,EAAEW,QAAQ,CAAC,EAAE;QAC1DH,WAAW,EAAE;UAAEQ,IAAI,EAAE,MAAM;UAAEC,UAAU,EAAE,IAAI,CAACvE;QAAW;OAC1D,CAAC;IACJ;IAEQmE,sBAAsBA,CAACF,QAAgB;MAC7C,MAAMT,MAAM,GAAG,IAAI,CAAChC,UAAU,CAACgD,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACvF,EAAE,KAAK+E,QAAQ,CAAC;MAC7D,IAAI,CAACT,MAAM,EAAE;MAEb,IAAI,CAAChD,aAAa,CAACkE,YAAY,CAAC;QAC9BtF,KAAK,EAAE,gBAAgB;QACvB6D,OAAO,EAAE,oCAAoCO,MAAM,CAACpE,KAAK,IAAI,EAAE,IAAI;QACnEuF,iBAAiB,EAAE,QAAQ;QAC3BC,gBAAgB,EAAE,QAAQ;QAC1BC,oBAAoB,EAAE,QAAQ;QAC9B1D,IAAI,EAAC;OACN,CAAC,CAAC2D,IAAI,CAACC,MAAM,IAAG;QACf,IAAIA,MAAM,CAACC,SAAS,EAAE;UACpB,IAAI,CAACC,YAAY,CAAChB,QAAQ,CAAC;QAC7B;MACF,CAAC,CAAC;IACJ;IAEQgB,YAAYA,CAAChB,QAAgB;MAChC;MACH,IAAI,CAACzD,aAAa,CAAC0E,OAAO,CAAC;QACzB9F,KAAK,EAAE,oBAAoB;QAC3B6D,OAAO,EAAE,yCAAyC;QAClDkC,YAAY,EAAE,KAAK;QACnBC,gBAAgB,EAAE;OACnB,CAAC;MAEF,IAAI,CAAChF,cAAc,CAAC6E,YAAY,CAAChB,QAAQ,CAAC,CAACrB,SAAS,CAAC;QACnDC,IAAI,EAAGwC,GAAG,IAAI;UACZ,IAAI,CAAC7E,aAAa,CAAC8E,KAAK,EAAE,CAAC,CAAC;UAC5B,IAAID,GAAG,IAAIA,GAAG,CAACE,OAAO,KAAK,KAAK,EAAE;YAChC;YACA,IAAI,CAAC/D,UAAU,GAAG,IAAI,CAACA,UAAU,CAACgE,MAAM,CAAEf,CAAC,IAAKA,CAAC,CAACvF,EAAE,KAAK+E,QAAQ,CAAC;YAClE,IAAI,CAACnE,eAAe,GAAG,IAAI,CAACA,eAAe,CAAC0F,MAAM,CAC/Cf,CAAC,IAAKA,CAAC,CAACvF,EAAE,KAAK+E,QAAQ,CACzB;YACD,IAAI,CAACnB,sBAAsB,EAAE;YAE7B;YACA,IAAI,CAACtC,aAAa,CAAC+E,OAAO,CAAC;cACzBnG,KAAK,EAAE,UAAU;cACjB6D,OAAO,EAAE;aACV,CAAC;UACJ,CAAC,MAAM;YACL,IAAI,CAACzC,aAAa,CAACiC,KAAK,CAAC;cACvBrD,KAAK,EAAE,OAAO;cACd6D,OAAO,EAAE;aACV,CAAC;UACJ;QACF,CAAC;QACDR,KAAK,EAAGO,GAAG,IAAI;UACb,IAAI,CAACxC,aAAa,CAAC8E,KAAK,EAAE,CAAC,CAAC;UAC5B5C,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEO,GAAG,CAAC;UAC9C,IAAI,CAACxC,aAAa,CAACiC,KAAK,CAAC;YACvBrD,KAAK,EAAE,OAAO;YACd6D,OAAO,EAAE,yDAAyD;YAClEwC,eAAe,EAAE,IAAI;YACrBC,eAAe,EAAE;WAClB,CAAC,CAACZ,IAAI,CAACC,MAAM,IAAG;YACf,IAAIA,MAAM,CAACY,MAAM,KAAK,OAAO,EAAE;cAC7B,IAAI,CAACV,YAAY,CAAChB,QAAQ,CAAC;YAC7B;UACF,CAAC,CAAC;QACJ;OACD,CAAC;IACJ;IAEQ1B,2BAA2BA,CACjCqD,QAA8C;MAE9C,MAAMjD,OAAO,GAAG,SAAS,IAAIiD,QAAQ,GAAGA,QAAQ,CAACjD,OAAO,GAAGiD,QAAQ;MACnE,OAAO;QACLjD,OAAO,EAAEA,OAAO,CAAC/F,GAAG,CAAC,IAAI,CAACiJ,gBAAgB,CAACrD,IAAI,CAAC,IAAI,CAAC;OACtD;IACH;IAEQqD,gBAAgBA,CAACC,IAAS;MAChC,MAAM;QACJ7E,IAAI;QACJ8E,SAAS;QACTC,YAAY;QACZC,UAAU;QACVC,IAAI,GAAG,EAAE;QACT,GAAGC;MAAI,CACR,GAAGL,IAAI;MACR,MAAMM,UAAU,GAAG,IAAI,CAACC,aAAa,CAACL,YAAY,EAAEC,UAAU,CAAC;MAC/D,MAAMK,OAAO,GAAG,CAAC,GAAGJ,IAAI,EAAE,GAAGE,UAAU,CAAC;MACxC,MAAMG,UAAU,GAAG,IAAI,CAACC,aAAa,CAACF,OAAO,CAAC;MAC9C,MAAM9G,WAAW,GAAGrC,mBAAmB,CAAC4I,SAAS,CAAC;MAClD,OAAO;QACL3G,KAAK,EAAE6B,IAAI;QACXzB,WAAW;QACX0G,IAAI,EAAEI,OAAO;QACbC,UAAU;QACVE,OAAO,EAAEpJ,oBAAoB;QAC7B,GAAG8I;OACJ;IACH;IAEQ/B,aAAaA,CAACH,QAAgB;MACpC,IAAI,CAAC5D,MAAM,CAACiD,QAAQ,CAAC,CAAC,yBAAyB,EAAEW,QAAQ,CAAC,EAAE;QAC1DH,WAAW,EAAE;UAAE4C,OAAO,EAAE,MAAM;UAAEnC,UAAU,EAAE,IAAI,CAACvE;QAAW;OAC7D,CAAC;IACJ;IAEc2G,UAAUA,CAAC1C,QAAgB;MAAA,IAAA2C,KAAA;MAAA,OAAAC,iBAAA;QACvC,IAAI,CAAC5C,QAAQ,EAAE;QACf,MAAMT,MAAM,GAAGoD,KAAI,CAACpF,UAAU,CAACgD,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACvF,EAAE,KAAK+E,QAAQ,CAAC;QAC7D,IAAIT,MAAM,EAAE;UACV,IAAI;YACF,MAAMsD,SAAS,CAACC,SAAS,CAACC,SAAS,CAACC,IAAI,CAACC,SAAS,CAAC1D,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;YACpEoD,KAAI,CAACpG,aAAa,CAAC+E,OAAO,CAAC;cACzBnG,KAAK,EAAE,SAAS;cAChB6D,OAAO,EAAE;aACV,CAAC;UACJ,CAAC,CAAC,OAAOD,GAAG,EAAE;YACZ4D,KAAI,CAACpG,aAAa,CAACiC,KAAK,CAAC;cACvBrD,KAAK,EAAE,aAAa;cACpB6D,OAAO,EAAE;aACV,CAAC;UACJ;QACF;MAAC;IACH;IAEQoD,aAAaA,CAACL,YAAqB,EAAEC,UAAmB;MAC9D,MAAMC,IAAI,GAAc,EAAE;MAC1B,IAAIF,YAAY,EAAEE,IAAI,CAACiB,IAAI,CAAC;QAAE/F,KAAK,EAAE4E,YAAY;QAAEoB,IAAI,EAAE;MAAS,CAAE,CAAC;MACrE,IAAInB,UAAU,EAAEC,IAAI,CAACiB,IAAI,CAAC;QAAE/F,KAAK,EAAE6E,UAAU;QAAEmB,IAAI,EAAE;MAAW,CAAE,CAAC;MACnE,OAAOlB,IAAI;IACb;IAEQM,aAAaA,CAACN,IAAe;MACnC,OAAOA,IAAI,CAACtJ,GAAG,CAAEyK,GAAG,IAAKA,GAAG,CAACjG,KAAK,CAAC,CAACkG,IAAI,CAAC,IAAI,CAAC;IAChD;IAEQvE,4BAA4BA,CAAA;MAClC,MAAMwE,SAAS,GAAG,IAAI,CAACjH,KAAK,CAACkH,QAAQ,CAACC,aAAa,CAACC,GAAG,CAAC,MAAM,CAAC;MAC/D,IAAIH,SAAS,EAAE;QACb,MAAM3D,IAAI,GAAG+D,QAAQ,CAACJ,SAAS,EAAE,EAAE,CAAC;QACpC,IAAI,CAACK,KAAK,CAAChE,IAAI,CAAC,EAAE,IAAI,CAAC5D,WAAW,GAAG4D,IAAI;MAC3C;IACF;IAEQxB,kBAAkBA,CAAA;MACxB,IAAI,CAACV,UAAU,CACZgG,GAAG,CAAC,QAAQ,CAAE,CACdG,YAAY,CAACvF,IAAI,CAChBrF,SAAS,CAAC,EAAE,CAAC,EACbF,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBJ,GAAG,CAAEsE,KAAK,IAAKA,KAAK,EAAE4G,WAAW,EAAE,IAAI,EAAE,CAAC,EAC1CpL,SAAS,CAAC,IAAI,CAACkF,QAAQ,CAAC,CACzB,CACAgB,SAAS,CAAEmF,UAAU,IAAI;QACxB,IAAI,CAACC,aAAa,CAACD,UAAU,CAAC;MAChC,CAAC,CAAC;IACN;IAEQjF,sBAAsBA,CAAA;MAC5B,IAAI,CAAC7C,YAAY,GAAG,IAAI,CAACD,WAAW,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE;MACpD,MAAM;QAAEiI,cAAc;QAAEtG;MAAU,CAAE,GAClC,IAAI,CAACxB,iBAAiB,CAAC+H,iBAAiB,CACtC,IAAI,CAACpI,eAAe,EACpB,IAAI,CAACE,WAAW,EAChB,IAAI,CAACC,YAAY,CAClB;MACH,IAAI,CAACwB,gBAAgB,GAAGwG,cAAc;MACtC,IAAI,CAACtG,UAAU,GAAGA,UAAU;IAC9B;IAEQqG,aAAaA,CAACD,UAAkB;MACtC,IAAI,CAACjI,eAAe,GAAG,IAAI,CAAC0B,UAAU,CAACgE,MAAM,CAAEhC,MAAM,IAAI;QACvD,MAAM2E,UAAU,GAAG3E,MAAM,CAACpE,KAAK,EAAE0I,WAAW,EAAE,CAACM,QAAQ,CAACL,UAAU,CAAC;QACnE,MAAMM,gBAAgB,GAAG7E,MAAM,CAACnE,WAAW,EACvCyI,WAAW,EAAE,CACdM,QAAQ,CAACL,UAAU,CAAC;QACvB,MAAMO,QAAQ,GAAG9E,MAAM,CAAC0C,IAAI,EAAEqC,IAAI,CAAElB,GAAG,IACrCA,GAAG,CAACjG,KAAK,EAAE0G,WAAW,EAAE,CAACM,QAAQ,CAACL,UAAU,CAAC,CAC9C;QACD,OAAOI,UAAU,IAAIE,gBAAgB,IAAIC,QAAQ;MACnD,CAAC,CAAC;MACF,IAAI,CAACtI,WAAW,GAAG,CAAC;MACpB,IAAI,CAAC8C,sBAAsB,EAAE;IAC/B;;uCA/UW5C,gBAAgB,EAAApC,EAAA,CAAA0K,iBAAA,CAAAC,EAAA,CAAAC,iBAAA,GAAA5K,EAAA,CAAA0K,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAA9K,EAAA,CAAA0K,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAAhL,EAAA,CAAA0K,iBAAA,CAAAK,EAAA,CAAAE,cAAA,GAAAjL,EAAA,CAAA0K,iBAAA,CAAAQ,EAAA,CAAAC,WAAA,GAAAnL,EAAA,CAAA0K,iBAAA,CAAAU,EAAA,CAAAC,aAAA;IAAA;;YAAhBjJ,gBAAgB;MAAAkJ,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCpDrB5L,EAJR,CAAAC,cAAA,aAAoD,aACA,aACe,cAC9B,qBAM5B;UACCD,EAAA,CAAA8L,SAAA,kBAMW;UAGjB9L,EAFI,CAAAG,YAAA,EAAc,EACT,EACH;UAEJH,EADF,CAAAC,cAAA,aAA8D,sBAK3D;UADCD,EAAA,CAAAU,UAAA,6BAAAqL,kEAAAnL,MAAA;YAAA,OAAmBiL,GAAA,CAAAxG,iBAAA,CAAAzE,MAAA,CAAyB;UAAA,EAAC;UAInDZ,EAFI,CAAAG,YAAA,EAAe,EACX,EACF;UAGJH,EADF,CAAAC,cAAA,aAAiD,uBAS9C;UAFCD,EAAA,CAAAU,UAAA,uBAAAsL,6DAAA;YAAA,OAAaH,GAAA,CAAAtG,cAAA,EAAgB;UAAA,EAAC;UAGhCvF,EAAA,CAAAG,YAAA,EAAgB;UAYhBH,EATA,CAAAiM,UAAA,KAAAC,gCAAA,kBAGC,KAAAC,yCAAA,2BAYA;UAgBHnM,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAiM,UAAA,KAAAG,gCAAA,kBAAoD;UAUtDpM,EAAA,CAAAG,YAAA,EAAM;;;UApFMH,EAAA,CAAAI,SAAA,GAAwB;UAAxBJ,EAAA,CAAAqB,UAAA,cAAAwK,GAAA,CAAAjI,UAAA,CAAwB;UAUxB5D,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAAqB,UAAA,gBAAe;UAUnBrB,EAAA,CAAAI,SAAA,GAAyB;UAAzBJ,EAAA,CAAAqB,UAAA,YAAAwK,GAAA,CAAA3I,aAAA,CAAyB;UAU3BlD,EAAA,CAAAI,SAAA,GAAiB;UAKjBJ,EALA,CAAAqB,UAAA,kBAAiB,oBACE,UAAAwK,GAAA,CAAAtL,YAAA,CAAA8L,YAAA,CAEgB,cAAAR,GAAA,CAAAjK,SAAA,CAEZ;UAOtB5B,EAAA,CAAAI,SAAA,EAAgD;UAAhDJ,EAAA,CAAAqB,UAAA,UAAAwK,GAAA,CAAAjK,SAAA,IAAAiK,GAAA,CAAA7J,eAAA,CAAAC,MAAA,OAAgD;UAS3BjC,EAAA,CAAAI,SAAA,EAGvB;UAHuBJ,EAAA,CAAAqB,UAAA,YAAAwK,GAAA,CAAAjK,SAAA,IAAAiK,GAAA,CAAAlI,gBAAA,CAAA1B,MAAA,SAAA4J,GAAA,CAAA7H,wBAAA,GAAA6H,GAAA,CAAAlI,gBAAA,CAGvB;UAoBe3D,EAAA,CAAAI,SAAA,EAAgC;UAAhCJ,EAAA,CAAAqB,UAAA,SAAAwK,GAAA,CAAA7J,eAAA,CAAAC,MAAA,KAAgC;;;qBDpChDtD,YAAY,EAAA2N,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZpN,mBAAmB,EACnBK,iBAAiB,EACjBD,mBAAmB,EACnBE,iBAAiB,EACjBE,mBAAmB,EACnBD,aAAa,EAEbE,mBAAmB,EAAAqL,EAAA,CAAAuB,aAAA,EAAAvB,EAAA,CAAAwB,eAAA,EAAAxB,EAAA,CAAAyB,oBAAA,EAAAzB,EAAA,CAAA0B,kBAAA,EAAA1B,EAAA,CAAA2B,eAAA,EACnB/M,oBAAoB,EACpBC,WAAW;MAAA+M,MAAA;IAAA;;SAKF1K,gBAAgB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}