{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { autorunOpts } from '../../../base/common/observable.js';\nimport { observableFromEventOpts } from '../../../base/common/observableInternal/utils.js';\n/** Creates an observable update when a configuration key updates. */\nexport function observableConfigValue(key, defaultValue, configurationService) {\n  return observableFromEventOpts({\n    debugName: () => `Configuration Key \"${key}\"`\n  }, handleChange => configurationService.onDidChangeConfiguration(e => {\n    if (e.affectsConfiguration(key)) {\n      handleChange(e);\n    }\n  }), () => configurationService.getValue(key) ?? defaultValue);\n}\n/** Update the configuration key with a value derived from observables. */\nexport function bindContextKey(key, service, computeValue) {\n  const boundKey = key.bindTo(service);\n  return autorunOpts({\n    debugName: () => `Set Context Key \"${key.key}\"`\n  }, reader => {\n    boundKey.set(computeValue(reader));\n  });\n}", "map": {"version": 3, "names": ["autorunOpts", "observableFromEventOpts", "observableConfigValue", "key", "defaultValue", "configurationService", "debugName", "handleChange", "onDidChangeConfiguration", "e", "affectsConfiguration", "getValue", "bindContextKey", "service", "computeValue", "bound<PERSON><PERSON>", "bindTo", "reader", "set"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/platform/observable/common/platformObservableUtils.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { autorunOpts } from '../../../base/common/observable.js';\nimport { observableFromEventOpts } from '../../../base/common/observableInternal/utils.js';\n/** Creates an observable update when a configuration key updates. */\nexport function observableConfigValue(key, defaultValue, configurationService) {\n    return observableFromEventOpts({ debugName: () => `Configuration Key \"${key}\"`, }, (handleChange) => configurationService.onDidChangeConfiguration(e => {\n        if (e.affectsConfiguration(key)) {\n            handleChange(e);\n        }\n    }), () => configurationService.getValue(key) ?? defaultValue);\n}\n/** Update the configuration key with a value derived from observables. */\nexport function bindContextKey(key, service, computeValue) {\n    const boundKey = key.bindTo(service);\n    return autorunOpts({ debugName: () => `Set Context Key \"${key.key}\"` }, reader => {\n        boundKey.set(computeValue(reader));\n    });\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,WAAW,QAAQ,oCAAoC;AAChE,SAASC,uBAAuB,QAAQ,kDAAkD;AAC1F;AACA,OAAO,SAASC,qBAAqBA,CAACC,GAAG,EAAEC,YAAY,EAAEC,oBAAoB,EAAE;EAC3E,OAAOJ,uBAAuB,CAAC;IAAEK,SAAS,EAAEA,CAAA,KAAM,sBAAsBH,GAAG;EAAK,CAAC,EAAGI,YAAY,IAAKF,oBAAoB,CAACG,wBAAwB,CAACC,CAAC,IAAI;IACpJ,IAAIA,CAAC,CAACC,oBAAoB,CAACP,GAAG,CAAC,EAAE;MAC7BI,YAAY,CAACE,CAAC,CAAC;IACnB;EACJ,CAAC,CAAC,EAAE,MAAMJ,oBAAoB,CAACM,QAAQ,CAACR,GAAG,CAAC,IAAIC,YAAY,CAAC;AACjE;AACA;AACA,OAAO,SAASQ,cAAcA,CAACT,GAAG,EAAEU,OAAO,EAAEC,YAAY,EAAE;EACvD,MAAMC,QAAQ,GAAGZ,GAAG,CAACa,MAAM,CAACH,OAAO,CAAC;EACpC,OAAOb,WAAW,CAAC;IAAEM,SAAS,EAAEA,CAAA,KAAM,oBAAoBH,GAAG,CAACA,GAAG;EAAI,CAAC,EAAEc,MAAM,IAAI;IAC9EF,QAAQ,CAACG,GAAG,CAACJ,YAAY,CAACG,MAAM,CAAC,CAAC;EACtC,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}