{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Position } from '../../core/position.js';\nimport { Range } from '../../core/range.js';\nimport { FindMatch } from '../../model.js';\nimport { SENTINEL, TreeNode, fixInsert, leftest, rbDelete, righttest, updateTreeMetadata } from './rbTreeBase.js';\nimport { Searcher, createFindMatch, isValidMatch } from '../textModelSearch.js';\n// const lfRegex = new RegExp(/\\r\\n|\\r|\\n/g);\nconst AverageBufferSize = 65535;\nfunction createUintArray(arr) {\n  let r;\n  if (arr[arr.length - 1] < 65536) {\n    r = new Uint16Array(arr.length);\n  } else {\n    r = new Uint32Array(arr.length);\n  }\n  r.set(arr, 0);\n  return r;\n}\nclass LineStarts {\n  constructor(lineStarts, cr, lf, crlf, isBasicASCII) {\n    this.lineStarts = lineStarts;\n    this.cr = cr;\n    this.lf = lf;\n    this.crlf = crlf;\n    this.isBasicASCII = isBasicASCII;\n  }\n}\nexport function createLineStartsFast(str, readonly = true) {\n  const r = [0];\n  let rLength = 1;\n  for (let i = 0, len = str.length; i < len; i++) {\n    const chr = str.charCodeAt(i);\n    if (chr === 13 /* CharCode.CarriageReturn */) {\n      if (i + 1 < len && str.charCodeAt(i + 1) === 10 /* CharCode.LineFeed */) {\n        // \\r\\n... case\n        r[rLength++] = i + 2;\n        i++; // skip \\n\n      } else {\n        // \\r... case\n        r[rLength++] = i + 1;\n      }\n    } else if (chr === 10 /* CharCode.LineFeed */) {\n      r[rLength++] = i + 1;\n    }\n  }\n  if (readonly) {\n    return createUintArray(r);\n  } else {\n    return r;\n  }\n}\nexport function createLineStarts(r, str) {\n  r.length = 0;\n  r[0] = 0;\n  let rLength = 1;\n  let cr = 0,\n    lf = 0,\n    crlf = 0;\n  let isBasicASCII = true;\n  for (let i = 0, len = str.length; i < len; i++) {\n    const chr = str.charCodeAt(i);\n    if (chr === 13 /* CharCode.CarriageReturn */) {\n      if (i + 1 < len && str.charCodeAt(i + 1) === 10 /* CharCode.LineFeed */) {\n        // \\r\\n... case\n        crlf++;\n        r[rLength++] = i + 2;\n        i++; // skip \\n\n      } else {\n        cr++;\n        // \\r... case\n        r[rLength++] = i + 1;\n      }\n    } else if (chr === 10 /* CharCode.LineFeed */) {\n      lf++;\n      r[rLength++] = i + 1;\n    } else {\n      if (isBasicASCII) {\n        if (chr !== 9 /* CharCode.Tab */ && (chr < 32 || chr > 126)) {\n          isBasicASCII = false;\n        }\n      }\n    }\n  }\n  const result = new LineStarts(createUintArray(r), cr, lf, crlf, isBasicASCII);\n  r.length = 0;\n  return result;\n}\nexport class Piece {\n  constructor(bufferIndex, start, end, lineFeedCnt, length) {\n    this.bufferIndex = bufferIndex;\n    this.start = start;\n    this.end = end;\n    this.lineFeedCnt = lineFeedCnt;\n    this.length = length;\n  }\n}\nexport class StringBuffer {\n  constructor(buffer, lineStarts) {\n    this.buffer = buffer;\n    this.lineStarts = lineStarts;\n  }\n}\n/**\n * Readonly snapshot for piece tree.\n * In a real multiple thread environment, to make snapshot reading always work correctly, we need to\n * 1. Make TreeNode.piece immutable, then reading and writing can run in parallel.\n * 2. TreeNode/Buffers normalization should not happen during snapshot reading.\n */\nclass PieceTreeSnapshot {\n  constructor(tree, BOM) {\n    this._pieces = [];\n    this._tree = tree;\n    this._BOM = BOM;\n    this._index = 0;\n    if (tree.root !== SENTINEL) {\n      tree.iterate(tree.root, node => {\n        if (node !== SENTINEL) {\n          this._pieces.push(node.piece);\n        }\n        return true;\n      });\n    }\n  }\n  read() {\n    if (this._pieces.length === 0) {\n      if (this._index === 0) {\n        this._index++;\n        return this._BOM;\n      } else {\n        return null;\n      }\n    }\n    if (this._index > this._pieces.length - 1) {\n      return null;\n    }\n    if (this._index === 0) {\n      return this._BOM + this._tree.getPieceContent(this._pieces[this._index++]);\n    }\n    return this._tree.getPieceContent(this._pieces[this._index++]);\n  }\n}\nclass PieceTreeSearchCache {\n  constructor(limit) {\n    this._limit = limit;\n    this._cache = [];\n  }\n  get(offset) {\n    for (let i = this._cache.length - 1; i >= 0; i--) {\n      const nodePos = this._cache[i];\n      if (nodePos.nodeStartOffset <= offset && nodePos.nodeStartOffset + nodePos.node.piece.length >= offset) {\n        return nodePos;\n      }\n    }\n    return null;\n  }\n  get2(lineNumber) {\n    for (let i = this._cache.length - 1; i >= 0; i--) {\n      const nodePos = this._cache[i];\n      if (nodePos.nodeStartLineNumber && nodePos.nodeStartLineNumber < lineNumber && nodePos.nodeStartLineNumber + nodePos.node.piece.lineFeedCnt >= lineNumber) {\n        return nodePos;\n      }\n    }\n    return null;\n  }\n  set(nodePosition) {\n    if (this._cache.length >= this._limit) {\n      this._cache.shift();\n    }\n    this._cache.push(nodePosition);\n  }\n  validate(offset) {\n    let hasInvalidVal = false;\n    const tmp = this._cache;\n    for (let i = 0; i < tmp.length; i++) {\n      const nodePos = tmp[i];\n      if (nodePos.node.parent === null || nodePos.nodeStartOffset >= offset) {\n        tmp[i] = null;\n        hasInvalidVal = true;\n        continue;\n      }\n    }\n    if (hasInvalidVal) {\n      const newArr = [];\n      for (const entry of tmp) {\n        if (entry !== null) {\n          newArr.push(entry);\n        }\n      }\n      this._cache = newArr;\n    }\n  }\n}\nexport class PieceTreeBase {\n  constructor(chunks, eol, eolNormalized) {\n    this.create(chunks, eol, eolNormalized);\n  }\n  create(chunks, eol, eolNormalized) {\n    this._buffers = [new StringBuffer('', [0])];\n    this._lastChangeBufferPos = {\n      line: 0,\n      column: 0\n    };\n    this.root = SENTINEL;\n    this._lineCnt = 1;\n    this._length = 0;\n    this._EOL = eol;\n    this._EOLLength = eol.length;\n    this._EOLNormalized = eolNormalized;\n    let lastNode = null;\n    for (let i = 0, len = chunks.length; i < len; i++) {\n      if (chunks[i].buffer.length > 0) {\n        if (!chunks[i].lineStarts) {\n          chunks[i].lineStarts = createLineStartsFast(chunks[i].buffer);\n        }\n        const piece = new Piece(i + 1, {\n          line: 0,\n          column: 0\n        }, {\n          line: chunks[i].lineStarts.length - 1,\n          column: chunks[i].buffer.length - chunks[i].lineStarts[chunks[i].lineStarts.length - 1]\n        }, chunks[i].lineStarts.length - 1, chunks[i].buffer.length);\n        this._buffers.push(chunks[i]);\n        lastNode = this.rbInsertRight(lastNode, piece);\n      }\n    }\n    this._searchCache = new PieceTreeSearchCache(1);\n    this._lastVisitedLine = {\n      lineNumber: 0,\n      value: ''\n    };\n    this.computeBufferMetadata();\n  }\n  normalizeEOL(eol) {\n    const averageBufferSize = AverageBufferSize;\n    const min = averageBufferSize - Math.floor(averageBufferSize / 3);\n    const max = min * 2;\n    let tempChunk = '';\n    let tempChunkLen = 0;\n    const chunks = [];\n    this.iterate(this.root, node => {\n      const str = this.getNodeContent(node);\n      const len = str.length;\n      if (tempChunkLen <= min || tempChunkLen + len < max) {\n        tempChunk += str;\n        tempChunkLen += len;\n        return true;\n      }\n      // flush anyways\n      const text = tempChunk.replace(/\\r\\n|\\r|\\n/g, eol);\n      chunks.push(new StringBuffer(text, createLineStartsFast(text)));\n      tempChunk = str;\n      tempChunkLen = len;\n      return true;\n    });\n    if (tempChunkLen > 0) {\n      const text = tempChunk.replace(/\\r\\n|\\r|\\n/g, eol);\n      chunks.push(new StringBuffer(text, createLineStartsFast(text)));\n    }\n    this.create(chunks, eol, true);\n  }\n  // #region Buffer API\n  getEOL() {\n    return this._EOL;\n  }\n  setEOL(newEOL) {\n    this._EOL = newEOL;\n    this._EOLLength = this._EOL.length;\n    this.normalizeEOL(newEOL);\n  }\n  createSnapshot(BOM) {\n    return new PieceTreeSnapshot(this, BOM);\n  }\n  getOffsetAt(lineNumber, column) {\n    let leftLen = 0; // inorder\n    let x = this.root;\n    while (x !== SENTINEL) {\n      if (x.left !== SENTINEL && x.lf_left + 1 >= lineNumber) {\n        x = x.left;\n      } else if (x.lf_left + x.piece.lineFeedCnt + 1 >= lineNumber) {\n        leftLen += x.size_left;\n        // lineNumber >= 2\n        const accumualtedValInCurrentIndex = this.getAccumulatedValue(x, lineNumber - x.lf_left - 2);\n        return leftLen += accumualtedValInCurrentIndex + column - 1;\n      } else {\n        lineNumber -= x.lf_left + x.piece.lineFeedCnt;\n        leftLen += x.size_left + x.piece.length;\n        x = x.right;\n      }\n    }\n    return leftLen;\n  }\n  getPositionAt(offset) {\n    offset = Math.floor(offset);\n    offset = Math.max(0, offset);\n    let x = this.root;\n    let lfCnt = 0;\n    const originalOffset = offset;\n    while (x !== SENTINEL) {\n      if (x.size_left !== 0 && x.size_left >= offset) {\n        x = x.left;\n      } else if (x.size_left + x.piece.length >= offset) {\n        const out = this.getIndexOf(x, offset - x.size_left);\n        lfCnt += x.lf_left + out.index;\n        if (out.index === 0) {\n          const lineStartOffset = this.getOffsetAt(lfCnt + 1, 1);\n          const column = originalOffset - lineStartOffset;\n          return new Position(lfCnt + 1, column + 1);\n        }\n        return new Position(lfCnt + 1, out.remainder + 1);\n      } else {\n        offset -= x.size_left + x.piece.length;\n        lfCnt += x.lf_left + x.piece.lineFeedCnt;\n        if (x.right === SENTINEL) {\n          // last node\n          const lineStartOffset = this.getOffsetAt(lfCnt + 1, 1);\n          const column = originalOffset - offset - lineStartOffset;\n          return new Position(lfCnt + 1, column + 1);\n        } else {\n          x = x.right;\n        }\n      }\n    }\n    return new Position(1, 1);\n  }\n  getValueInRange(range, eol) {\n    if (range.startLineNumber === range.endLineNumber && range.startColumn === range.endColumn) {\n      return '';\n    }\n    const startPosition = this.nodeAt2(range.startLineNumber, range.startColumn);\n    const endPosition = this.nodeAt2(range.endLineNumber, range.endColumn);\n    const value = this.getValueInRange2(startPosition, endPosition);\n    if (eol) {\n      if (eol !== this._EOL || !this._EOLNormalized) {\n        return value.replace(/\\r\\n|\\r|\\n/g, eol);\n      }\n      if (eol === this.getEOL() && this._EOLNormalized) {\n        if (eol === '\\r\\n') {}\n        return value;\n      }\n      return value.replace(/\\r\\n|\\r|\\n/g, eol);\n    }\n    return value;\n  }\n  getValueInRange2(startPosition, endPosition) {\n    if (startPosition.node === endPosition.node) {\n      const node = startPosition.node;\n      const buffer = this._buffers[node.piece.bufferIndex].buffer;\n      const startOffset = this.offsetInBuffer(node.piece.bufferIndex, node.piece.start);\n      return buffer.substring(startOffset + startPosition.remainder, startOffset + endPosition.remainder);\n    }\n    let x = startPosition.node;\n    const buffer = this._buffers[x.piece.bufferIndex].buffer;\n    const startOffset = this.offsetInBuffer(x.piece.bufferIndex, x.piece.start);\n    let ret = buffer.substring(startOffset + startPosition.remainder, startOffset + x.piece.length);\n    x = x.next();\n    while (x !== SENTINEL) {\n      const buffer = this._buffers[x.piece.bufferIndex].buffer;\n      const startOffset = this.offsetInBuffer(x.piece.bufferIndex, x.piece.start);\n      if (x === endPosition.node) {\n        ret += buffer.substring(startOffset, startOffset + endPosition.remainder);\n        break;\n      } else {\n        ret += buffer.substr(startOffset, x.piece.length);\n      }\n      x = x.next();\n    }\n    return ret;\n  }\n  getLinesContent() {\n    const lines = [];\n    let linesLength = 0;\n    let currentLine = '';\n    let danglingCR = false;\n    this.iterate(this.root, node => {\n      if (node === SENTINEL) {\n        return true;\n      }\n      const piece = node.piece;\n      let pieceLength = piece.length;\n      if (pieceLength === 0) {\n        return true;\n      }\n      const buffer = this._buffers[piece.bufferIndex].buffer;\n      const lineStarts = this._buffers[piece.bufferIndex].lineStarts;\n      const pieceStartLine = piece.start.line;\n      const pieceEndLine = piece.end.line;\n      let pieceStartOffset = lineStarts[pieceStartLine] + piece.start.column;\n      if (danglingCR) {\n        if (buffer.charCodeAt(pieceStartOffset) === 10 /* CharCode.LineFeed */) {\n          // pretend the \\n was in the previous piece..\n          pieceStartOffset++;\n          pieceLength--;\n        }\n        lines[linesLength++] = currentLine;\n        currentLine = '';\n        danglingCR = false;\n        if (pieceLength === 0) {\n          return true;\n        }\n      }\n      if (pieceStartLine === pieceEndLine) {\n        // this piece has no new lines\n        if (!this._EOLNormalized && buffer.charCodeAt(pieceStartOffset + pieceLength - 1) === 13 /* CharCode.CarriageReturn */) {\n          danglingCR = true;\n          currentLine += buffer.substr(pieceStartOffset, pieceLength - 1);\n        } else {\n          currentLine += buffer.substr(pieceStartOffset, pieceLength);\n        }\n        return true;\n      }\n      // add the text before the first line start in this piece\n      currentLine += this._EOLNormalized ? buffer.substring(pieceStartOffset, Math.max(pieceStartOffset, lineStarts[pieceStartLine + 1] - this._EOLLength)) : buffer.substring(pieceStartOffset, lineStarts[pieceStartLine + 1]).replace(/(\\r\\n|\\r|\\n)$/, '');\n      lines[linesLength++] = currentLine;\n      for (let line = pieceStartLine + 1; line < pieceEndLine; line++) {\n        currentLine = this._EOLNormalized ? buffer.substring(lineStarts[line], lineStarts[line + 1] - this._EOLLength) : buffer.substring(lineStarts[line], lineStarts[line + 1]).replace(/(\\r\\n|\\r|\\n)$/, '');\n        lines[linesLength++] = currentLine;\n      }\n      if (!this._EOLNormalized && buffer.charCodeAt(lineStarts[pieceEndLine] + piece.end.column - 1) === 13 /* CharCode.CarriageReturn */) {\n        danglingCR = true;\n        if (piece.end.column === 0) {\n          // The last line ended with a \\r, let's undo the push, it will be pushed by next iteration\n          linesLength--;\n        } else {\n          currentLine = buffer.substr(lineStarts[pieceEndLine], piece.end.column - 1);\n        }\n      } else {\n        currentLine = buffer.substr(lineStarts[pieceEndLine], piece.end.column);\n      }\n      return true;\n    });\n    if (danglingCR) {\n      lines[linesLength++] = currentLine;\n      currentLine = '';\n    }\n    lines[linesLength++] = currentLine;\n    return lines;\n  }\n  getLength() {\n    return this._length;\n  }\n  getLineCount() {\n    return this._lineCnt;\n  }\n  getLineContent(lineNumber) {\n    if (this._lastVisitedLine.lineNumber === lineNumber) {\n      return this._lastVisitedLine.value;\n    }\n    this._lastVisitedLine.lineNumber = lineNumber;\n    if (lineNumber === this._lineCnt) {\n      this._lastVisitedLine.value = this.getLineRawContent(lineNumber);\n    } else if (this._EOLNormalized) {\n      this._lastVisitedLine.value = this.getLineRawContent(lineNumber, this._EOLLength);\n    } else {\n      this._lastVisitedLine.value = this.getLineRawContent(lineNumber).replace(/(\\r\\n|\\r|\\n)$/, '');\n    }\n    return this._lastVisitedLine.value;\n  }\n  _getCharCode(nodePos) {\n    if (nodePos.remainder === nodePos.node.piece.length) {\n      // the char we want to fetch is at the head of next node.\n      const matchingNode = nodePos.node.next();\n      if (!matchingNode) {\n        return 0;\n      }\n      const buffer = this._buffers[matchingNode.piece.bufferIndex];\n      const startOffset = this.offsetInBuffer(matchingNode.piece.bufferIndex, matchingNode.piece.start);\n      return buffer.buffer.charCodeAt(startOffset);\n    } else {\n      const buffer = this._buffers[nodePos.node.piece.bufferIndex];\n      const startOffset = this.offsetInBuffer(nodePos.node.piece.bufferIndex, nodePos.node.piece.start);\n      const targetOffset = startOffset + nodePos.remainder;\n      return buffer.buffer.charCodeAt(targetOffset);\n    }\n  }\n  getLineCharCode(lineNumber, index) {\n    const nodePos = this.nodeAt2(lineNumber, index + 1);\n    return this._getCharCode(nodePos);\n  }\n  getLineLength(lineNumber) {\n    if (lineNumber === this.getLineCount()) {\n      const startOffset = this.getOffsetAt(lineNumber, 1);\n      return this.getLength() - startOffset;\n    }\n    return this.getOffsetAt(lineNumber + 1, 1) - this.getOffsetAt(lineNumber, 1) - this._EOLLength;\n  }\n  findMatchesInNode(node, searcher, startLineNumber, startColumn, startCursor, endCursor, searchData, captureMatches, limitResultCount, resultLen, result) {\n    const buffer = this._buffers[node.piece.bufferIndex];\n    const startOffsetInBuffer = this.offsetInBuffer(node.piece.bufferIndex, node.piece.start);\n    const start = this.offsetInBuffer(node.piece.bufferIndex, startCursor);\n    const end = this.offsetInBuffer(node.piece.bufferIndex, endCursor);\n    let m;\n    // Reset regex to search from the beginning\n    const ret = {\n      line: 0,\n      column: 0\n    };\n    let searchText;\n    let offsetInBuffer;\n    if (searcher._wordSeparators) {\n      searchText = buffer.buffer.substring(start, end);\n      offsetInBuffer = offset => offset + start;\n      searcher.reset(0);\n    } else {\n      searchText = buffer.buffer;\n      offsetInBuffer = offset => offset;\n      searcher.reset(start);\n    }\n    do {\n      m = searcher.next(searchText);\n      if (m) {\n        if (offsetInBuffer(m.index) >= end) {\n          return resultLen;\n        }\n        this.positionInBuffer(node, offsetInBuffer(m.index) - startOffsetInBuffer, ret);\n        const lineFeedCnt = this.getLineFeedCnt(node.piece.bufferIndex, startCursor, ret);\n        const retStartColumn = ret.line === startCursor.line ? ret.column - startCursor.column + startColumn : ret.column + 1;\n        const retEndColumn = retStartColumn + m[0].length;\n        result[resultLen++] = createFindMatch(new Range(startLineNumber + lineFeedCnt, retStartColumn, startLineNumber + lineFeedCnt, retEndColumn), m, captureMatches);\n        if (offsetInBuffer(m.index) + m[0].length >= end) {\n          return resultLen;\n        }\n        if (resultLen >= limitResultCount) {\n          return resultLen;\n        }\n      }\n    } while (m);\n    return resultLen;\n  }\n  findMatchesLineByLine(searchRange, searchData, captureMatches, limitResultCount) {\n    const result = [];\n    let resultLen = 0;\n    const searcher = new Searcher(searchData.wordSeparators, searchData.regex);\n    let startPosition = this.nodeAt2(searchRange.startLineNumber, searchRange.startColumn);\n    if (startPosition === null) {\n      return [];\n    }\n    const endPosition = this.nodeAt2(searchRange.endLineNumber, searchRange.endColumn);\n    if (endPosition === null) {\n      return [];\n    }\n    let start = this.positionInBuffer(startPosition.node, startPosition.remainder);\n    const end = this.positionInBuffer(endPosition.node, endPosition.remainder);\n    if (startPosition.node === endPosition.node) {\n      this.findMatchesInNode(startPosition.node, searcher, searchRange.startLineNumber, searchRange.startColumn, start, end, searchData, captureMatches, limitResultCount, resultLen, result);\n      return result;\n    }\n    let startLineNumber = searchRange.startLineNumber;\n    let currentNode = startPosition.node;\n    while (currentNode !== endPosition.node) {\n      const lineBreakCnt = this.getLineFeedCnt(currentNode.piece.bufferIndex, start, currentNode.piece.end);\n      if (lineBreakCnt >= 1) {\n        // last line break position\n        const lineStarts = this._buffers[currentNode.piece.bufferIndex].lineStarts;\n        const startOffsetInBuffer = this.offsetInBuffer(currentNode.piece.bufferIndex, currentNode.piece.start);\n        const nextLineStartOffset = lineStarts[start.line + lineBreakCnt];\n        const startColumn = startLineNumber === searchRange.startLineNumber ? searchRange.startColumn : 1;\n        resultLen = this.findMatchesInNode(currentNode, searcher, startLineNumber, startColumn, start, this.positionInBuffer(currentNode, nextLineStartOffset - startOffsetInBuffer), searchData, captureMatches, limitResultCount, resultLen, result);\n        if (resultLen >= limitResultCount) {\n          return result;\n        }\n        startLineNumber += lineBreakCnt;\n      }\n      const startColumn = startLineNumber === searchRange.startLineNumber ? searchRange.startColumn - 1 : 0;\n      // search for the remaining content\n      if (startLineNumber === searchRange.endLineNumber) {\n        const text = this.getLineContent(startLineNumber).substring(startColumn, searchRange.endColumn - 1);\n        resultLen = this._findMatchesInLine(searchData, searcher, text, searchRange.endLineNumber, startColumn, resultLen, result, captureMatches, limitResultCount);\n        return result;\n      }\n      resultLen = this._findMatchesInLine(searchData, searcher, this.getLineContent(startLineNumber).substr(startColumn), startLineNumber, startColumn, resultLen, result, captureMatches, limitResultCount);\n      if (resultLen >= limitResultCount) {\n        return result;\n      }\n      startLineNumber++;\n      startPosition = this.nodeAt2(startLineNumber, 1);\n      currentNode = startPosition.node;\n      start = this.positionInBuffer(startPosition.node, startPosition.remainder);\n    }\n    if (startLineNumber === searchRange.endLineNumber) {\n      const startColumn = startLineNumber === searchRange.startLineNumber ? searchRange.startColumn - 1 : 0;\n      const text = this.getLineContent(startLineNumber).substring(startColumn, searchRange.endColumn - 1);\n      resultLen = this._findMatchesInLine(searchData, searcher, text, searchRange.endLineNumber, startColumn, resultLen, result, captureMatches, limitResultCount);\n      return result;\n    }\n    const startColumn = startLineNumber === searchRange.startLineNumber ? searchRange.startColumn : 1;\n    resultLen = this.findMatchesInNode(endPosition.node, searcher, startLineNumber, startColumn, start, end, searchData, captureMatches, limitResultCount, resultLen, result);\n    return result;\n  }\n  _findMatchesInLine(searchData, searcher, text, lineNumber, deltaOffset, resultLen, result, captureMatches, limitResultCount) {\n    const wordSeparators = searchData.wordSeparators;\n    if (!captureMatches && searchData.simpleSearch) {\n      const searchString = searchData.simpleSearch;\n      const searchStringLen = searchString.length;\n      const textLength = text.length;\n      let lastMatchIndex = -searchStringLen;\n      while ((lastMatchIndex = text.indexOf(searchString, lastMatchIndex + searchStringLen)) !== -1) {\n        if (!wordSeparators || isValidMatch(wordSeparators, text, textLength, lastMatchIndex, searchStringLen)) {\n          result[resultLen++] = new FindMatch(new Range(lineNumber, lastMatchIndex + 1 + deltaOffset, lineNumber, lastMatchIndex + 1 + searchStringLen + deltaOffset), null);\n          if (resultLen >= limitResultCount) {\n            return resultLen;\n          }\n        }\n      }\n      return resultLen;\n    }\n    let m;\n    // Reset regex to search from the beginning\n    searcher.reset(0);\n    do {\n      m = searcher.next(text);\n      if (m) {\n        result[resultLen++] = createFindMatch(new Range(lineNumber, m.index + 1 + deltaOffset, lineNumber, m.index + 1 + m[0].length + deltaOffset), m, captureMatches);\n        if (resultLen >= limitResultCount) {\n          return resultLen;\n        }\n      }\n    } while (m);\n    return resultLen;\n  }\n  // #endregion\n  // #region Piece Table\n  insert(offset, value, eolNormalized = false) {\n    this._EOLNormalized = this._EOLNormalized && eolNormalized;\n    this._lastVisitedLine.lineNumber = 0;\n    this._lastVisitedLine.value = '';\n    if (this.root !== SENTINEL) {\n      const {\n        node,\n        remainder,\n        nodeStartOffset\n      } = this.nodeAt(offset);\n      const piece = node.piece;\n      const bufferIndex = piece.bufferIndex;\n      const insertPosInBuffer = this.positionInBuffer(node, remainder);\n      if (node.piece.bufferIndex === 0 && piece.end.line === this._lastChangeBufferPos.line && piece.end.column === this._lastChangeBufferPos.column && nodeStartOffset + piece.length === offset && value.length < AverageBufferSize) {\n        // changed buffer\n        this.appendToNode(node, value);\n        this.computeBufferMetadata();\n        return;\n      }\n      if (nodeStartOffset === offset) {\n        this.insertContentToNodeLeft(value, node);\n        this._searchCache.validate(offset);\n      } else if (nodeStartOffset + node.piece.length > offset) {\n        // we are inserting into the middle of a node.\n        const nodesToDel = [];\n        let newRightPiece = new Piece(piece.bufferIndex, insertPosInBuffer, piece.end, this.getLineFeedCnt(piece.bufferIndex, insertPosInBuffer, piece.end), this.offsetInBuffer(bufferIndex, piece.end) - this.offsetInBuffer(bufferIndex, insertPosInBuffer));\n        if (this.shouldCheckCRLF() && this.endWithCR(value)) {\n          const headOfRight = this.nodeCharCodeAt(node, remainder);\n          if (headOfRight === 10 /** \\n */) {\n            const newStart = {\n              line: newRightPiece.start.line + 1,\n              column: 0\n            };\n            newRightPiece = new Piece(newRightPiece.bufferIndex, newStart, newRightPiece.end, this.getLineFeedCnt(newRightPiece.bufferIndex, newStart, newRightPiece.end), newRightPiece.length - 1);\n            value += '\\n';\n          }\n        }\n        // reuse node for content before insertion point.\n        if (this.shouldCheckCRLF() && this.startWithLF(value)) {\n          const tailOfLeft = this.nodeCharCodeAt(node, remainder - 1);\n          if (tailOfLeft === 13 /** \\r */) {\n            const previousPos = this.positionInBuffer(node, remainder - 1);\n            this.deleteNodeTail(node, previousPos);\n            value = '\\r' + value;\n            if (node.piece.length === 0) {\n              nodesToDel.push(node);\n            }\n          } else {\n            this.deleteNodeTail(node, insertPosInBuffer);\n          }\n        } else {\n          this.deleteNodeTail(node, insertPosInBuffer);\n        }\n        const newPieces = this.createNewPieces(value);\n        if (newRightPiece.length > 0) {\n          this.rbInsertRight(node, newRightPiece);\n        }\n        let tmpNode = node;\n        for (let k = 0; k < newPieces.length; k++) {\n          tmpNode = this.rbInsertRight(tmpNode, newPieces[k]);\n        }\n        this.deleteNodes(nodesToDel);\n      } else {\n        this.insertContentToNodeRight(value, node);\n      }\n    } else {\n      // insert new node\n      const pieces = this.createNewPieces(value);\n      let node = this.rbInsertLeft(null, pieces[0]);\n      for (let k = 1; k < pieces.length; k++) {\n        node = this.rbInsertRight(node, pieces[k]);\n      }\n    }\n    // todo, this is too brutal. Total line feed count should be updated the same way as lf_left.\n    this.computeBufferMetadata();\n  }\n  delete(offset, cnt) {\n    this._lastVisitedLine.lineNumber = 0;\n    this._lastVisitedLine.value = '';\n    if (cnt <= 0 || this.root === SENTINEL) {\n      return;\n    }\n    const startPosition = this.nodeAt(offset);\n    const endPosition = this.nodeAt(offset + cnt);\n    const startNode = startPosition.node;\n    const endNode = endPosition.node;\n    if (startNode === endNode) {\n      const startSplitPosInBuffer = this.positionInBuffer(startNode, startPosition.remainder);\n      const endSplitPosInBuffer = this.positionInBuffer(startNode, endPosition.remainder);\n      if (startPosition.nodeStartOffset === offset) {\n        if (cnt === startNode.piece.length) {\n          // delete node\n          const next = startNode.next();\n          rbDelete(this, startNode);\n          this.validateCRLFWithPrevNode(next);\n          this.computeBufferMetadata();\n          return;\n        }\n        this.deleteNodeHead(startNode, endSplitPosInBuffer);\n        this._searchCache.validate(offset);\n        this.validateCRLFWithPrevNode(startNode);\n        this.computeBufferMetadata();\n        return;\n      }\n      if (startPosition.nodeStartOffset + startNode.piece.length === offset + cnt) {\n        this.deleteNodeTail(startNode, startSplitPosInBuffer);\n        this.validateCRLFWithNextNode(startNode);\n        this.computeBufferMetadata();\n        return;\n      }\n      // delete content in the middle, this node will be splitted to nodes\n      this.shrinkNode(startNode, startSplitPosInBuffer, endSplitPosInBuffer);\n      this.computeBufferMetadata();\n      return;\n    }\n    const nodesToDel = [];\n    const startSplitPosInBuffer = this.positionInBuffer(startNode, startPosition.remainder);\n    this.deleteNodeTail(startNode, startSplitPosInBuffer);\n    this._searchCache.validate(offset);\n    if (startNode.piece.length === 0) {\n      nodesToDel.push(startNode);\n    }\n    // update last touched node\n    const endSplitPosInBuffer = this.positionInBuffer(endNode, endPosition.remainder);\n    this.deleteNodeHead(endNode, endSplitPosInBuffer);\n    if (endNode.piece.length === 0) {\n      nodesToDel.push(endNode);\n    }\n    // delete nodes in between\n    const secondNode = startNode.next();\n    for (let node = secondNode; node !== SENTINEL && node !== endNode; node = node.next()) {\n      nodesToDel.push(node);\n    }\n    const prev = startNode.piece.length === 0 ? startNode.prev() : startNode;\n    this.deleteNodes(nodesToDel);\n    this.validateCRLFWithNextNode(prev);\n    this.computeBufferMetadata();\n  }\n  insertContentToNodeLeft(value, node) {\n    // we are inserting content to the beginning of node\n    const nodesToDel = [];\n    if (this.shouldCheckCRLF() && this.endWithCR(value) && this.startWithLF(node)) {\n      // move `\\n` to new node.\n      const piece = node.piece;\n      const newStart = {\n        line: piece.start.line + 1,\n        column: 0\n      };\n      const nPiece = new Piece(piece.bufferIndex, newStart, piece.end, this.getLineFeedCnt(piece.bufferIndex, newStart, piece.end), piece.length - 1);\n      node.piece = nPiece;\n      value += '\\n';\n      updateTreeMetadata(this, node, -1, -1);\n      if (node.piece.length === 0) {\n        nodesToDel.push(node);\n      }\n    }\n    const newPieces = this.createNewPieces(value);\n    let newNode = this.rbInsertLeft(node, newPieces[newPieces.length - 1]);\n    for (let k = newPieces.length - 2; k >= 0; k--) {\n      newNode = this.rbInsertLeft(newNode, newPieces[k]);\n    }\n    this.validateCRLFWithPrevNode(newNode);\n    this.deleteNodes(nodesToDel);\n  }\n  insertContentToNodeRight(value, node) {\n    // we are inserting to the right of this node.\n    if (this.adjustCarriageReturnFromNext(value, node)) {\n      // move \\n to the new node.\n      value += '\\n';\n    }\n    const newPieces = this.createNewPieces(value);\n    const newNode = this.rbInsertRight(node, newPieces[0]);\n    let tmpNode = newNode;\n    for (let k = 1; k < newPieces.length; k++) {\n      tmpNode = this.rbInsertRight(tmpNode, newPieces[k]);\n    }\n    this.validateCRLFWithPrevNode(newNode);\n  }\n  positionInBuffer(node, remainder, ret) {\n    const piece = node.piece;\n    const bufferIndex = node.piece.bufferIndex;\n    const lineStarts = this._buffers[bufferIndex].lineStarts;\n    const startOffset = lineStarts[piece.start.line] + piece.start.column;\n    const offset = startOffset + remainder;\n    // binary search offset between startOffset and endOffset\n    let low = piece.start.line;\n    let high = piece.end.line;\n    let mid = 0;\n    let midStop = 0;\n    let midStart = 0;\n    while (low <= high) {\n      mid = low + (high - low) / 2 | 0;\n      midStart = lineStarts[mid];\n      if (mid === high) {\n        break;\n      }\n      midStop = lineStarts[mid + 1];\n      if (offset < midStart) {\n        high = mid - 1;\n      } else if (offset >= midStop) {\n        low = mid + 1;\n      } else {\n        break;\n      }\n    }\n    if (ret) {\n      ret.line = mid;\n      ret.column = offset - midStart;\n      return null;\n    }\n    return {\n      line: mid,\n      column: offset - midStart\n    };\n  }\n  getLineFeedCnt(bufferIndex, start, end) {\n    // we don't need to worry about start: abc\\r|\\n, or abc|\\r, or abc|\\n, or abc|\\r\\n doesn't change the fact that, there is one line break after start.\n    // now let's take care of end: abc\\r|\\n, if end is in between \\r and \\n, we need to add line feed count by 1\n    if (end.column === 0) {\n      return end.line - start.line;\n    }\n    const lineStarts = this._buffers[bufferIndex].lineStarts;\n    if (end.line === lineStarts.length - 1) {\n      // it means, there is no \\n after end, otherwise, there will be one more lineStart.\n      return end.line - start.line;\n    }\n    const nextLineStartOffset = lineStarts[end.line + 1];\n    const endOffset = lineStarts[end.line] + end.column;\n    if (nextLineStartOffset > endOffset + 1) {\n      // there are more than 1 character after end, which means it can't be \\n\n      return end.line - start.line;\n    }\n    // endOffset + 1 === nextLineStartOffset\n    // character at endOffset is \\n, so we check the character before first\n    // if character at endOffset is \\r, end.column is 0 and we can't get here.\n    const previousCharOffset = endOffset - 1; // end.column > 0 so it's okay.\n    const buffer = this._buffers[bufferIndex].buffer;\n    if (buffer.charCodeAt(previousCharOffset) === 13) {\n      return end.line - start.line + 1;\n    } else {\n      return end.line - start.line;\n    }\n  }\n  offsetInBuffer(bufferIndex, cursor) {\n    const lineStarts = this._buffers[bufferIndex].lineStarts;\n    return lineStarts[cursor.line] + cursor.column;\n  }\n  deleteNodes(nodes) {\n    for (let i = 0; i < nodes.length; i++) {\n      rbDelete(this, nodes[i]);\n    }\n  }\n  createNewPieces(text) {\n    if (text.length > AverageBufferSize) {\n      // the content is large, operations like substring, charCode becomes slow\n      // so here we split it into smaller chunks, just like what we did for CR/LF normalization\n      const newPieces = [];\n      while (text.length > AverageBufferSize) {\n        const lastChar = text.charCodeAt(AverageBufferSize - 1);\n        let splitText;\n        if (lastChar === 13 /* CharCode.CarriageReturn */ || lastChar >= 0xD800 && lastChar <= 0xDBFF) {\n          // last character is \\r or a high surrogate => keep it back\n          splitText = text.substring(0, AverageBufferSize - 1);\n          text = text.substring(AverageBufferSize - 1);\n        } else {\n          splitText = text.substring(0, AverageBufferSize);\n          text = text.substring(AverageBufferSize);\n        }\n        const lineStarts = createLineStartsFast(splitText);\n        newPieces.push(new Piece(this._buffers.length, /* buffer index */{\n          line: 0,\n          column: 0\n        }, {\n          line: lineStarts.length - 1,\n          column: splitText.length - lineStarts[lineStarts.length - 1]\n        }, lineStarts.length - 1, splitText.length));\n        this._buffers.push(new StringBuffer(splitText, lineStarts));\n      }\n      const lineStarts = createLineStartsFast(text);\n      newPieces.push(new Piece(this._buffers.length, /* buffer index */{\n        line: 0,\n        column: 0\n      }, {\n        line: lineStarts.length - 1,\n        column: text.length - lineStarts[lineStarts.length - 1]\n      }, lineStarts.length - 1, text.length));\n      this._buffers.push(new StringBuffer(text, lineStarts));\n      return newPieces;\n    }\n    let startOffset = this._buffers[0].buffer.length;\n    const lineStarts = createLineStartsFast(text, false);\n    let start = this._lastChangeBufferPos;\n    if (this._buffers[0].lineStarts[this._buffers[0].lineStarts.length - 1] === startOffset && startOffset !== 0 && this.startWithLF(text) && this.endWithCR(this._buffers[0].buffer) // todo, we can check this._lastChangeBufferPos's column as it's the last one\n    ) {\n      this._lastChangeBufferPos = {\n        line: this._lastChangeBufferPos.line,\n        column: this._lastChangeBufferPos.column + 1\n      };\n      start = this._lastChangeBufferPos;\n      for (let i = 0; i < lineStarts.length; i++) {\n        lineStarts[i] += startOffset + 1;\n      }\n      this._buffers[0].lineStarts = this._buffers[0].lineStarts.concat(lineStarts.slice(1));\n      this._buffers[0].buffer += '_' + text;\n      startOffset += 1;\n    } else {\n      if (startOffset !== 0) {\n        for (let i = 0; i < lineStarts.length; i++) {\n          lineStarts[i] += startOffset;\n        }\n      }\n      this._buffers[0].lineStarts = this._buffers[0].lineStarts.concat(lineStarts.slice(1));\n      this._buffers[0].buffer += text;\n    }\n    const endOffset = this._buffers[0].buffer.length;\n    const endIndex = this._buffers[0].lineStarts.length - 1;\n    const endColumn = endOffset - this._buffers[0].lineStarts[endIndex];\n    const endPos = {\n      line: endIndex,\n      column: endColumn\n    };\n    const newPiece = new Piece(0, /** todo@peng */start, endPos, this.getLineFeedCnt(0, start, endPos), endOffset - startOffset);\n    this._lastChangeBufferPos = endPos;\n    return [newPiece];\n  }\n  getLineRawContent(lineNumber, endOffset = 0) {\n    let x = this.root;\n    let ret = '';\n    const cache = this._searchCache.get2(lineNumber);\n    if (cache) {\n      x = cache.node;\n      const prevAccumulatedValue = this.getAccumulatedValue(x, lineNumber - cache.nodeStartLineNumber - 1);\n      const buffer = this._buffers[x.piece.bufferIndex].buffer;\n      const startOffset = this.offsetInBuffer(x.piece.bufferIndex, x.piece.start);\n      if (cache.nodeStartLineNumber + x.piece.lineFeedCnt === lineNumber) {\n        ret = buffer.substring(startOffset + prevAccumulatedValue, startOffset + x.piece.length);\n      } else {\n        const accumulatedValue = this.getAccumulatedValue(x, lineNumber - cache.nodeStartLineNumber);\n        return buffer.substring(startOffset + prevAccumulatedValue, startOffset + accumulatedValue - endOffset);\n      }\n    } else {\n      let nodeStartOffset = 0;\n      const originalLineNumber = lineNumber;\n      while (x !== SENTINEL) {\n        if (x.left !== SENTINEL && x.lf_left >= lineNumber - 1) {\n          x = x.left;\n        } else if (x.lf_left + x.piece.lineFeedCnt > lineNumber - 1) {\n          const prevAccumulatedValue = this.getAccumulatedValue(x, lineNumber - x.lf_left - 2);\n          const accumulatedValue = this.getAccumulatedValue(x, lineNumber - x.lf_left - 1);\n          const buffer = this._buffers[x.piece.bufferIndex].buffer;\n          const startOffset = this.offsetInBuffer(x.piece.bufferIndex, x.piece.start);\n          nodeStartOffset += x.size_left;\n          this._searchCache.set({\n            node: x,\n            nodeStartOffset,\n            nodeStartLineNumber: originalLineNumber - (lineNumber - 1 - x.lf_left)\n          });\n          return buffer.substring(startOffset + prevAccumulatedValue, startOffset + accumulatedValue - endOffset);\n        } else if (x.lf_left + x.piece.lineFeedCnt === lineNumber - 1) {\n          const prevAccumulatedValue = this.getAccumulatedValue(x, lineNumber - x.lf_left - 2);\n          const buffer = this._buffers[x.piece.bufferIndex].buffer;\n          const startOffset = this.offsetInBuffer(x.piece.bufferIndex, x.piece.start);\n          ret = buffer.substring(startOffset + prevAccumulatedValue, startOffset + x.piece.length);\n          break;\n        } else {\n          lineNumber -= x.lf_left + x.piece.lineFeedCnt;\n          nodeStartOffset += x.size_left + x.piece.length;\n          x = x.right;\n        }\n      }\n    }\n    // search in order, to find the node contains end column\n    x = x.next();\n    while (x !== SENTINEL) {\n      const buffer = this._buffers[x.piece.bufferIndex].buffer;\n      if (x.piece.lineFeedCnt > 0) {\n        const accumulatedValue = this.getAccumulatedValue(x, 0);\n        const startOffset = this.offsetInBuffer(x.piece.bufferIndex, x.piece.start);\n        ret += buffer.substring(startOffset, startOffset + accumulatedValue - endOffset);\n        return ret;\n      } else {\n        const startOffset = this.offsetInBuffer(x.piece.bufferIndex, x.piece.start);\n        ret += buffer.substr(startOffset, x.piece.length);\n      }\n      x = x.next();\n    }\n    return ret;\n  }\n  computeBufferMetadata() {\n    let x = this.root;\n    let lfCnt = 1;\n    let len = 0;\n    while (x !== SENTINEL) {\n      lfCnt += x.lf_left + x.piece.lineFeedCnt;\n      len += x.size_left + x.piece.length;\n      x = x.right;\n    }\n    this._lineCnt = lfCnt;\n    this._length = len;\n    this._searchCache.validate(this._length);\n  }\n  // #region node operations\n  getIndexOf(node, accumulatedValue) {\n    const piece = node.piece;\n    const pos = this.positionInBuffer(node, accumulatedValue);\n    const lineCnt = pos.line - piece.start.line;\n    if (this.offsetInBuffer(piece.bufferIndex, piece.end) - this.offsetInBuffer(piece.bufferIndex, piece.start) === accumulatedValue) {\n      // we are checking the end of this node, so a CRLF check is necessary.\n      const realLineCnt = this.getLineFeedCnt(node.piece.bufferIndex, piece.start, pos);\n      if (realLineCnt !== lineCnt) {\n        // aha yes, CRLF\n        return {\n          index: realLineCnt,\n          remainder: 0\n        };\n      }\n    }\n    return {\n      index: lineCnt,\n      remainder: pos.column\n    };\n  }\n  getAccumulatedValue(node, index) {\n    if (index < 0) {\n      return 0;\n    }\n    const piece = node.piece;\n    const lineStarts = this._buffers[piece.bufferIndex].lineStarts;\n    const expectedLineStartIndex = piece.start.line + index + 1;\n    if (expectedLineStartIndex > piece.end.line) {\n      return lineStarts[piece.end.line] + piece.end.column - lineStarts[piece.start.line] - piece.start.column;\n    } else {\n      return lineStarts[expectedLineStartIndex] - lineStarts[piece.start.line] - piece.start.column;\n    }\n  }\n  deleteNodeTail(node, pos) {\n    const piece = node.piece;\n    const originalLFCnt = piece.lineFeedCnt;\n    const originalEndOffset = this.offsetInBuffer(piece.bufferIndex, piece.end);\n    const newEnd = pos;\n    const newEndOffset = this.offsetInBuffer(piece.bufferIndex, newEnd);\n    const newLineFeedCnt = this.getLineFeedCnt(piece.bufferIndex, piece.start, newEnd);\n    const lf_delta = newLineFeedCnt - originalLFCnt;\n    const size_delta = newEndOffset - originalEndOffset;\n    const newLength = piece.length + size_delta;\n    node.piece = new Piece(piece.bufferIndex, piece.start, newEnd, newLineFeedCnt, newLength);\n    updateTreeMetadata(this, node, size_delta, lf_delta);\n  }\n  deleteNodeHead(node, pos) {\n    const piece = node.piece;\n    const originalLFCnt = piece.lineFeedCnt;\n    const originalStartOffset = this.offsetInBuffer(piece.bufferIndex, piece.start);\n    const newStart = pos;\n    const newLineFeedCnt = this.getLineFeedCnt(piece.bufferIndex, newStart, piece.end);\n    const newStartOffset = this.offsetInBuffer(piece.bufferIndex, newStart);\n    const lf_delta = newLineFeedCnt - originalLFCnt;\n    const size_delta = originalStartOffset - newStartOffset;\n    const newLength = piece.length + size_delta;\n    node.piece = new Piece(piece.bufferIndex, newStart, piece.end, newLineFeedCnt, newLength);\n    updateTreeMetadata(this, node, size_delta, lf_delta);\n  }\n  shrinkNode(node, start, end) {\n    const piece = node.piece;\n    const originalStartPos = piece.start;\n    const originalEndPos = piece.end;\n    // old piece, originalStartPos, start\n    const oldLength = piece.length;\n    const oldLFCnt = piece.lineFeedCnt;\n    const newEnd = start;\n    const newLineFeedCnt = this.getLineFeedCnt(piece.bufferIndex, piece.start, newEnd);\n    const newLength = this.offsetInBuffer(piece.bufferIndex, start) - this.offsetInBuffer(piece.bufferIndex, originalStartPos);\n    node.piece = new Piece(piece.bufferIndex, piece.start, newEnd, newLineFeedCnt, newLength);\n    updateTreeMetadata(this, node, newLength - oldLength, newLineFeedCnt - oldLFCnt);\n    // new right piece, end, originalEndPos\n    const newPiece = new Piece(piece.bufferIndex, end, originalEndPos, this.getLineFeedCnt(piece.bufferIndex, end, originalEndPos), this.offsetInBuffer(piece.bufferIndex, originalEndPos) - this.offsetInBuffer(piece.bufferIndex, end));\n    const newNode = this.rbInsertRight(node, newPiece);\n    this.validateCRLFWithPrevNode(newNode);\n  }\n  appendToNode(node, value) {\n    if (this.adjustCarriageReturnFromNext(value, node)) {\n      value += '\\n';\n    }\n    const hitCRLF = this.shouldCheckCRLF() && this.startWithLF(value) && this.endWithCR(node);\n    const startOffset = this._buffers[0].buffer.length;\n    this._buffers[0].buffer += value;\n    const lineStarts = createLineStartsFast(value, false);\n    for (let i = 0; i < lineStarts.length; i++) {\n      lineStarts[i] += startOffset;\n    }\n    if (hitCRLF) {\n      const prevStartOffset = this._buffers[0].lineStarts[this._buffers[0].lineStarts.length - 2];\n      this._buffers[0].lineStarts.pop();\n      // _lastChangeBufferPos is already wrong\n      this._lastChangeBufferPos = {\n        line: this._lastChangeBufferPos.line - 1,\n        column: startOffset - prevStartOffset\n      };\n    }\n    this._buffers[0].lineStarts = this._buffers[0].lineStarts.concat(lineStarts.slice(1));\n    const endIndex = this._buffers[0].lineStarts.length - 1;\n    const endColumn = this._buffers[0].buffer.length - this._buffers[0].lineStarts[endIndex];\n    const newEnd = {\n      line: endIndex,\n      column: endColumn\n    };\n    const newLength = node.piece.length + value.length;\n    const oldLineFeedCnt = node.piece.lineFeedCnt;\n    const newLineFeedCnt = this.getLineFeedCnt(0, node.piece.start, newEnd);\n    const lf_delta = newLineFeedCnt - oldLineFeedCnt;\n    node.piece = new Piece(node.piece.bufferIndex, node.piece.start, newEnd, newLineFeedCnt, newLength);\n    this._lastChangeBufferPos = newEnd;\n    updateTreeMetadata(this, node, value.length, lf_delta);\n  }\n  nodeAt(offset) {\n    let x = this.root;\n    const cache = this._searchCache.get(offset);\n    if (cache) {\n      return {\n        node: cache.node,\n        nodeStartOffset: cache.nodeStartOffset,\n        remainder: offset - cache.nodeStartOffset\n      };\n    }\n    let nodeStartOffset = 0;\n    while (x !== SENTINEL) {\n      if (x.size_left > offset) {\n        x = x.left;\n      } else if (x.size_left + x.piece.length >= offset) {\n        nodeStartOffset += x.size_left;\n        const ret = {\n          node: x,\n          remainder: offset - x.size_left,\n          nodeStartOffset\n        };\n        this._searchCache.set(ret);\n        return ret;\n      } else {\n        offset -= x.size_left + x.piece.length;\n        nodeStartOffset += x.size_left + x.piece.length;\n        x = x.right;\n      }\n    }\n    return null;\n  }\n  nodeAt2(lineNumber, column) {\n    let x = this.root;\n    let nodeStartOffset = 0;\n    while (x !== SENTINEL) {\n      if (x.left !== SENTINEL && x.lf_left >= lineNumber - 1) {\n        x = x.left;\n      } else if (x.lf_left + x.piece.lineFeedCnt > lineNumber - 1) {\n        const prevAccumualtedValue = this.getAccumulatedValue(x, lineNumber - x.lf_left - 2);\n        const accumulatedValue = this.getAccumulatedValue(x, lineNumber - x.lf_left - 1);\n        nodeStartOffset += x.size_left;\n        return {\n          node: x,\n          remainder: Math.min(prevAccumualtedValue + column - 1, accumulatedValue),\n          nodeStartOffset\n        };\n      } else if (x.lf_left + x.piece.lineFeedCnt === lineNumber - 1) {\n        const prevAccumualtedValue = this.getAccumulatedValue(x, lineNumber - x.lf_left - 2);\n        if (prevAccumualtedValue + column - 1 <= x.piece.length) {\n          return {\n            node: x,\n            remainder: prevAccumualtedValue + column - 1,\n            nodeStartOffset\n          };\n        } else {\n          column -= x.piece.length - prevAccumualtedValue;\n          break;\n        }\n      } else {\n        lineNumber -= x.lf_left + x.piece.lineFeedCnt;\n        nodeStartOffset += x.size_left + x.piece.length;\n        x = x.right;\n      }\n    }\n    // search in order, to find the node contains position.column\n    x = x.next();\n    while (x !== SENTINEL) {\n      if (x.piece.lineFeedCnt > 0) {\n        const accumulatedValue = this.getAccumulatedValue(x, 0);\n        const nodeStartOffset = this.offsetOfNode(x);\n        return {\n          node: x,\n          remainder: Math.min(column - 1, accumulatedValue),\n          nodeStartOffset\n        };\n      } else {\n        if (x.piece.length >= column - 1) {\n          const nodeStartOffset = this.offsetOfNode(x);\n          return {\n            node: x,\n            remainder: column - 1,\n            nodeStartOffset\n          };\n        } else {\n          column -= x.piece.length;\n        }\n      }\n      x = x.next();\n    }\n    return null;\n  }\n  nodeCharCodeAt(node, offset) {\n    if (node.piece.lineFeedCnt < 1) {\n      return -1;\n    }\n    const buffer = this._buffers[node.piece.bufferIndex];\n    const newOffset = this.offsetInBuffer(node.piece.bufferIndex, node.piece.start) + offset;\n    return buffer.buffer.charCodeAt(newOffset);\n  }\n  offsetOfNode(node) {\n    if (!node) {\n      return 0;\n    }\n    let pos = node.size_left;\n    while (node !== this.root) {\n      if (node.parent.right === node) {\n        pos += node.parent.size_left + node.parent.piece.length;\n      }\n      node = node.parent;\n    }\n    return pos;\n  }\n  // #endregion\n  // #region CRLF\n  shouldCheckCRLF() {\n    return !(this._EOLNormalized && this._EOL === '\\n');\n  }\n  startWithLF(val) {\n    if (typeof val === 'string') {\n      return val.charCodeAt(0) === 10;\n    }\n    if (val === SENTINEL || val.piece.lineFeedCnt === 0) {\n      return false;\n    }\n    const piece = val.piece;\n    const lineStarts = this._buffers[piece.bufferIndex].lineStarts;\n    const line = piece.start.line;\n    const startOffset = lineStarts[line] + piece.start.column;\n    if (line === lineStarts.length - 1) {\n      // last line, so there is no line feed at the end of this line\n      return false;\n    }\n    const nextLineOffset = lineStarts[line + 1];\n    if (nextLineOffset > startOffset + 1) {\n      return false;\n    }\n    return this._buffers[piece.bufferIndex].buffer.charCodeAt(startOffset) === 10;\n  }\n  endWithCR(val) {\n    if (typeof val === 'string') {\n      return val.charCodeAt(val.length - 1) === 13;\n    }\n    if (val === SENTINEL || val.piece.lineFeedCnt === 0) {\n      return false;\n    }\n    return this.nodeCharCodeAt(val, val.piece.length - 1) === 13;\n  }\n  validateCRLFWithPrevNode(nextNode) {\n    if (this.shouldCheckCRLF() && this.startWithLF(nextNode)) {\n      const node = nextNode.prev();\n      if (this.endWithCR(node)) {\n        this.fixCRLF(node, nextNode);\n      }\n    }\n  }\n  validateCRLFWithNextNode(node) {\n    if (this.shouldCheckCRLF() && this.endWithCR(node)) {\n      const nextNode = node.next();\n      if (this.startWithLF(nextNode)) {\n        this.fixCRLF(node, nextNode);\n      }\n    }\n  }\n  fixCRLF(prev, next) {\n    const nodesToDel = [];\n    // update node\n    const lineStarts = this._buffers[prev.piece.bufferIndex].lineStarts;\n    let newEnd;\n    if (prev.piece.end.column === 0) {\n      // it means, last line ends with \\r, not \\r\\n\n      newEnd = {\n        line: prev.piece.end.line - 1,\n        column: lineStarts[prev.piece.end.line] - lineStarts[prev.piece.end.line - 1] - 1\n      };\n    } else {\n      // \\r\\n\n      newEnd = {\n        line: prev.piece.end.line,\n        column: prev.piece.end.column - 1\n      };\n    }\n    const prevNewLength = prev.piece.length - 1;\n    const prevNewLFCnt = prev.piece.lineFeedCnt - 1;\n    prev.piece = new Piece(prev.piece.bufferIndex, prev.piece.start, newEnd, prevNewLFCnt, prevNewLength);\n    updateTreeMetadata(this, prev, -1, -1);\n    if (prev.piece.length === 0) {\n      nodesToDel.push(prev);\n    }\n    // update nextNode\n    const newStart = {\n      line: next.piece.start.line + 1,\n      column: 0\n    };\n    const newLength = next.piece.length - 1;\n    const newLineFeedCnt = this.getLineFeedCnt(next.piece.bufferIndex, newStart, next.piece.end);\n    next.piece = new Piece(next.piece.bufferIndex, newStart, next.piece.end, newLineFeedCnt, newLength);\n    updateTreeMetadata(this, next, -1, -1);\n    if (next.piece.length === 0) {\n      nodesToDel.push(next);\n    }\n    // create new piece which contains \\r\\n\n    const pieces = this.createNewPieces('\\r\\n');\n    this.rbInsertRight(prev, pieces[0]);\n    // delete empty nodes\n    for (let i = 0; i < nodesToDel.length; i++) {\n      rbDelete(this, nodesToDel[i]);\n    }\n  }\n  adjustCarriageReturnFromNext(value, node) {\n    if (this.shouldCheckCRLF() && this.endWithCR(value)) {\n      const nextNode = node.next();\n      if (this.startWithLF(nextNode)) {\n        // move `\\n` forward\n        value += '\\n';\n        if (nextNode.piece.length === 1) {\n          rbDelete(this, nextNode);\n        } else {\n          const piece = nextNode.piece;\n          const newStart = {\n            line: piece.start.line + 1,\n            column: 0\n          };\n          const newLength = piece.length - 1;\n          const newLineFeedCnt = this.getLineFeedCnt(piece.bufferIndex, newStart, piece.end);\n          nextNode.piece = new Piece(piece.bufferIndex, newStart, piece.end, newLineFeedCnt, newLength);\n          updateTreeMetadata(this, nextNode, -1, -1);\n        }\n        return true;\n      }\n    }\n    return false;\n  }\n  // #endregion\n  // #endregion\n  // #region Tree operations\n  iterate(node, callback) {\n    if (node === SENTINEL) {\n      return callback(SENTINEL);\n    }\n    const leftRet = this.iterate(node.left, callback);\n    if (!leftRet) {\n      return leftRet;\n    }\n    return callback(node) && this.iterate(node.right, callback);\n  }\n  getNodeContent(node) {\n    if (node === SENTINEL) {\n      return '';\n    }\n    const buffer = this._buffers[node.piece.bufferIndex];\n    const piece = node.piece;\n    const startOffset = this.offsetInBuffer(piece.bufferIndex, piece.start);\n    const endOffset = this.offsetInBuffer(piece.bufferIndex, piece.end);\n    const currentContent = buffer.buffer.substring(startOffset, endOffset);\n    return currentContent;\n  }\n  getPieceContent(piece) {\n    const buffer = this._buffers[piece.bufferIndex];\n    const startOffset = this.offsetInBuffer(piece.bufferIndex, piece.start);\n    const endOffset = this.offsetInBuffer(piece.bufferIndex, piece.end);\n    const currentContent = buffer.buffer.substring(startOffset, endOffset);\n    return currentContent;\n  }\n  /**\n   *      node              node\n   *     /  \\              /  \\\n   *    a   b    <----   a    b\n   *                         /\n   *                        z\n   */\n  rbInsertRight(node, p) {\n    const z = new TreeNode(p, 1 /* NodeColor.Red */);\n    z.left = SENTINEL;\n    z.right = SENTINEL;\n    z.parent = SENTINEL;\n    z.size_left = 0;\n    z.lf_left = 0;\n    const x = this.root;\n    if (x === SENTINEL) {\n      this.root = z;\n      z.color = 0 /* NodeColor.Black */;\n    } else if (node.right === SENTINEL) {\n      node.right = z;\n      z.parent = node;\n    } else {\n      const nextNode = leftest(node.right);\n      nextNode.left = z;\n      z.parent = nextNode;\n    }\n    fixInsert(this, z);\n    return z;\n  }\n  /**\n   *      node              node\n   *     /  \\              /  \\\n   *    a   b     ---->   a    b\n   *                       \\\n   *                        z\n   */\n  rbInsertLeft(node, p) {\n    const z = new TreeNode(p, 1 /* NodeColor.Red */);\n    z.left = SENTINEL;\n    z.right = SENTINEL;\n    z.parent = SENTINEL;\n    z.size_left = 0;\n    z.lf_left = 0;\n    if (this.root === SENTINEL) {\n      this.root = z;\n      z.color = 0 /* NodeColor.Black */;\n    } else if (node.left === SENTINEL) {\n      node.left = z;\n      z.parent = node;\n    } else {\n      const prevNode = righttest(node.left); // a\n      prevNode.right = z;\n      z.parent = prevNode;\n    }\n    fixInsert(this, z);\n    return z;\n  }\n}", "map": {"version": 3, "names": ["Position", "Range", "FindMatch", "SENTINEL", "TreeNode", "fixInsert", "leftest", "rbDelete", "righttest", "updateTreeMetadata", "Searcher", "createFindMatch", "isValidMatch", "AverageBufferSize", "createUintArray", "arr", "r", "length", "Uint16Array", "Uint32Array", "set", "LineStarts", "constructor", "lineStarts", "cr", "lf", "crlf", "isBasicASCII", "createLineStartsFast", "str", "readonly", "r<PERSON><PERSON><PERSON>", "i", "len", "chr", "charCodeAt", "createLineStarts", "result", "Piece", "bufferIndex", "start", "end", "lineFeedCnt", "StringBuffer", "buffer", "PieceTreeSnapshot", "tree", "BOM", "_pieces", "_tree", "_BOM", "_index", "root", "iterate", "node", "push", "piece", "read", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PieceTreeSearchCache", "limit", "_limit", "_cache", "get", "offset", "nodePos", "nodeStartOffset", "get2", "lineNumber", "nodeStartLineNumber", "nodePosition", "shift", "validate", "hasInvalidVal", "tmp", "parent", "newArr", "entry", "PieceTreeBase", "chunks", "eol", "eolNormalized", "create", "_buffers", "_lastChangeBufferPos", "line", "column", "_lineCnt", "_length", "_EOL", "_EOLLength", "_EOLNormalized", "lastNode", "rbInsertRight", "_searchCache", "_lastVisitedLine", "value", "computeBufferMetadata", "normalizeEOL", "averageBufferSize", "min", "Math", "floor", "max", "tempChunk", "tempChunkLen", "getNodeContent", "text", "replace", "getEOL", "setEOL", "newEOL", "createSnapshot", "getOffsetAt", "leftLen", "x", "left", "lf_left", "size_left", "accumualtedValInCurrentIndex", "getAccumulatedValue", "right", "getPositionAt", "lfCnt", "originalOffset", "out", "getIndexOf", "index", "lineStartOffset", "remainder", "getValueInRange", "range", "startLineNumber", "endLineNumber", "startColumn", "endColumn", "startPosition", "nodeAt2", "endPosition", "getValueInRange2", "startOffset", "offsetInBuffer", "substring", "ret", "next", "substr", "getLinesContent", "lines", "linesLength", "currentLine", "danglingCR", "piece<PERSON>ength", "pieceStartLine", "pieceEndLine", "pieceStartOffset", "<PERSON><PERSON><PERSON><PERSON>", "getLineCount", "get<PERSON>ineC<PERSON>nt", "getLineRawContent", "_getCharCode", "matchingNode", "targetOffset", "getLineCharCode", "getLine<PERSON><PERSON>th", "findMatchesInNode", "searcher", "startCursor", "endCursor", "searchData", "capture<PERSON><PERSON>es", "limitResultCount", "resultLen", "startOffsetInBuffer", "m", "searchText", "_wordSeparators", "reset", "position<PERSON>n<PERSON>uffer", "getLineFeedCnt", "retStartColumn", "retEndColumn", "findMatchesLineByLine", "searchRange", "wordSeparators", "regex", "currentNode", "lineBreakCnt", "nextLineStartOffset", "_findMatchesInLine", "deltaOffset", "simpleSearch", "searchString", "searchStringLen", "textLength", "lastMatchIndex", "indexOf", "insert", "nodeAt", "insertPosInBuffer", "appendToNode", "insertContentToNodeLeft", "nodesToDel", "newRightPiece", "shouldCheckCRLF", "endWithCR", "headOfRight", "nodeCharCodeAt", "newStart", "startWithLF", "tailOfLeft", "previousPos", "deleteNodeTail", "newPieces", "createNewPieces", "tmpNode", "k", "deleteNodes", "insertContentToNodeRight", "pieces", "rbInsertLeft", "delete", "cnt", "startNode", "endNode", "startSplitPosInBuffer", "endSplitPosInBuffer", "validateCRLFWithPrevNode", "deleteNodeHead", "validateCRLFWithNextNode", "shrinkNode", "secondNode", "prev", "nPiece", "newNode", "adjustCarriageReturnFromNext", "low", "high", "mid", "midStop", "midStart", "endOffset", "previousCharOffset", "cursor", "nodes", "lastChar", "splitText", "concat", "slice", "endIndex", "endPos", "newPiece", "cache", "prevAccumulatedValue", "accumulatedValue", "originalLineNumber", "pos", "lineCnt", "realLineCnt", "expectedLineStartIndex", "originalLFCnt", "originalEndOffset", "newEnd", "newEndOffset", "newLineFeedCnt", "lf_delta", "size_delta", "<PERSON><PERSON><PERSON><PERSON>", "originalStartOffset", "newStartOffset", "originalStartPos", "originalEndPos", "<PERSON><PERSON><PERSON><PERSON>", "oldLFCnt", "hitCRLF", "prevStartOffset", "pop", "oldLineFeedCnt", "prevAccumualtedValue", "offsetOfNode", "newOffset", "val", "nextLineOffset", "nextNode", "fixCRLF", "prevNew<PERSON>ength", "prevNewLFCnt", "callback", "leftRet", "currentC<PERSON>nt", "p", "z", "color", "prevNode"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/model/pieceTreeTextBuffer/pieceTreeBase.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Position } from '../../core/position.js';\nimport { Range } from '../../core/range.js';\nimport { FindMatch } from '../../model.js';\nimport { SENTINEL, TreeNode, fixInsert, leftest, rbDelete, righttest, updateTreeMetadata } from './rbTreeBase.js';\nimport { Searcher, createFindMatch, isValidMatch } from '../textModelSearch.js';\n// const lfRegex = new RegExp(/\\r\\n|\\r|\\n/g);\nconst AverageBufferSize = 65535;\nfunction createUintArray(arr) {\n    let r;\n    if (arr[arr.length - 1] < 65536) {\n        r = new Uint16Array(arr.length);\n    }\n    else {\n        r = new Uint32Array(arr.length);\n    }\n    r.set(arr, 0);\n    return r;\n}\nclass LineStarts {\n    constructor(lineStarts, cr, lf, crlf, isBasicASCII) {\n        this.lineStarts = lineStarts;\n        this.cr = cr;\n        this.lf = lf;\n        this.crlf = crlf;\n        this.isBasicASCII = isBasicASCII;\n    }\n}\nexport function createLineStartsFast(str, readonly = true) {\n    const r = [0];\n    let rLength = 1;\n    for (let i = 0, len = str.length; i < len; i++) {\n        const chr = str.charCodeAt(i);\n        if (chr === 13 /* CharCode.CarriageReturn */) {\n            if (i + 1 < len && str.charCodeAt(i + 1) === 10 /* CharCode.LineFeed */) {\n                // \\r\\n... case\n                r[rLength++] = i + 2;\n                i++; // skip \\n\n            }\n            else {\n                // \\r... case\n                r[rLength++] = i + 1;\n            }\n        }\n        else if (chr === 10 /* CharCode.LineFeed */) {\n            r[rLength++] = i + 1;\n        }\n    }\n    if (readonly) {\n        return createUintArray(r);\n    }\n    else {\n        return r;\n    }\n}\nexport function createLineStarts(r, str) {\n    r.length = 0;\n    r[0] = 0;\n    let rLength = 1;\n    let cr = 0, lf = 0, crlf = 0;\n    let isBasicASCII = true;\n    for (let i = 0, len = str.length; i < len; i++) {\n        const chr = str.charCodeAt(i);\n        if (chr === 13 /* CharCode.CarriageReturn */) {\n            if (i + 1 < len && str.charCodeAt(i + 1) === 10 /* CharCode.LineFeed */) {\n                // \\r\\n... case\n                crlf++;\n                r[rLength++] = i + 2;\n                i++; // skip \\n\n            }\n            else {\n                cr++;\n                // \\r... case\n                r[rLength++] = i + 1;\n            }\n        }\n        else if (chr === 10 /* CharCode.LineFeed */) {\n            lf++;\n            r[rLength++] = i + 1;\n        }\n        else {\n            if (isBasicASCII) {\n                if (chr !== 9 /* CharCode.Tab */ && (chr < 32 || chr > 126)) {\n                    isBasicASCII = false;\n                }\n            }\n        }\n    }\n    const result = new LineStarts(createUintArray(r), cr, lf, crlf, isBasicASCII);\n    r.length = 0;\n    return result;\n}\nexport class Piece {\n    constructor(bufferIndex, start, end, lineFeedCnt, length) {\n        this.bufferIndex = bufferIndex;\n        this.start = start;\n        this.end = end;\n        this.lineFeedCnt = lineFeedCnt;\n        this.length = length;\n    }\n}\nexport class StringBuffer {\n    constructor(buffer, lineStarts) {\n        this.buffer = buffer;\n        this.lineStarts = lineStarts;\n    }\n}\n/**\n * Readonly snapshot for piece tree.\n * In a real multiple thread environment, to make snapshot reading always work correctly, we need to\n * 1. Make TreeNode.piece immutable, then reading and writing can run in parallel.\n * 2. TreeNode/Buffers normalization should not happen during snapshot reading.\n */\nclass PieceTreeSnapshot {\n    constructor(tree, BOM) {\n        this._pieces = [];\n        this._tree = tree;\n        this._BOM = BOM;\n        this._index = 0;\n        if (tree.root !== SENTINEL) {\n            tree.iterate(tree.root, node => {\n                if (node !== SENTINEL) {\n                    this._pieces.push(node.piece);\n                }\n                return true;\n            });\n        }\n    }\n    read() {\n        if (this._pieces.length === 0) {\n            if (this._index === 0) {\n                this._index++;\n                return this._BOM;\n            }\n            else {\n                return null;\n            }\n        }\n        if (this._index > this._pieces.length - 1) {\n            return null;\n        }\n        if (this._index === 0) {\n            return this._BOM + this._tree.getPieceContent(this._pieces[this._index++]);\n        }\n        return this._tree.getPieceContent(this._pieces[this._index++]);\n    }\n}\nclass PieceTreeSearchCache {\n    constructor(limit) {\n        this._limit = limit;\n        this._cache = [];\n    }\n    get(offset) {\n        for (let i = this._cache.length - 1; i >= 0; i--) {\n            const nodePos = this._cache[i];\n            if (nodePos.nodeStartOffset <= offset && nodePos.nodeStartOffset + nodePos.node.piece.length >= offset) {\n                return nodePos;\n            }\n        }\n        return null;\n    }\n    get2(lineNumber) {\n        for (let i = this._cache.length - 1; i >= 0; i--) {\n            const nodePos = this._cache[i];\n            if (nodePos.nodeStartLineNumber && nodePos.nodeStartLineNumber < lineNumber && nodePos.nodeStartLineNumber + nodePos.node.piece.lineFeedCnt >= lineNumber) {\n                return nodePos;\n            }\n        }\n        return null;\n    }\n    set(nodePosition) {\n        if (this._cache.length >= this._limit) {\n            this._cache.shift();\n        }\n        this._cache.push(nodePosition);\n    }\n    validate(offset) {\n        let hasInvalidVal = false;\n        const tmp = this._cache;\n        for (let i = 0; i < tmp.length; i++) {\n            const nodePos = tmp[i];\n            if (nodePos.node.parent === null || nodePos.nodeStartOffset >= offset) {\n                tmp[i] = null;\n                hasInvalidVal = true;\n                continue;\n            }\n        }\n        if (hasInvalidVal) {\n            const newArr = [];\n            for (const entry of tmp) {\n                if (entry !== null) {\n                    newArr.push(entry);\n                }\n            }\n            this._cache = newArr;\n        }\n    }\n}\nexport class PieceTreeBase {\n    constructor(chunks, eol, eolNormalized) {\n        this.create(chunks, eol, eolNormalized);\n    }\n    create(chunks, eol, eolNormalized) {\n        this._buffers = [\n            new StringBuffer('', [0])\n        ];\n        this._lastChangeBufferPos = { line: 0, column: 0 };\n        this.root = SENTINEL;\n        this._lineCnt = 1;\n        this._length = 0;\n        this._EOL = eol;\n        this._EOLLength = eol.length;\n        this._EOLNormalized = eolNormalized;\n        let lastNode = null;\n        for (let i = 0, len = chunks.length; i < len; i++) {\n            if (chunks[i].buffer.length > 0) {\n                if (!chunks[i].lineStarts) {\n                    chunks[i].lineStarts = createLineStartsFast(chunks[i].buffer);\n                }\n                const piece = new Piece(i + 1, { line: 0, column: 0 }, { line: chunks[i].lineStarts.length - 1, column: chunks[i].buffer.length - chunks[i].lineStarts[chunks[i].lineStarts.length - 1] }, chunks[i].lineStarts.length - 1, chunks[i].buffer.length);\n                this._buffers.push(chunks[i]);\n                lastNode = this.rbInsertRight(lastNode, piece);\n            }\n        }\n        this._searchCache = new PieceTreeSearchCache(1);\n        this._lastVisitedLine = { lineNumber: 0, value: '' };\n        this.computeBufferMetadata();\n    }\n    normalizeEOL(eol) {\n        const averageBufferSize = AverageBufferSize;\n        const min = averageBufferSize - Math.floor(averageBufferSize / 3);\n        const max = min * 2;\n        let tempChunk = '';\n        let tempChunkLen = 0;\n        const chunks = [];\n        this.iterate(this.root, node => {\n            const str = this.getNodeContent(node);\n            const len = str.length;\n            if (tempChunkLen <= min || tempChunkLen + len < max) {\n                tempChunk += str;\n                tempChunkLen += len;\n                return true;\n            }\n            // flush anyways\n            const text = tempChunk.replace(/\\r\\n|\\r|\\n/g, eol);\n            chunks.push(new StringBuffer(text, createLineStartsFast(text)));\n            tempChunk = str;\n            tempChunkLen = len;\n            return true;\n        });\n        if (tempChunkLen > 0) {\n            const text = tempChunk.replace(/\\r\\n|\\r|\\n/g, eol);\n            chunks.push(new StringBuffer(text, createLineStartsFast(text)));\n        }\n        this.create(chunks, eol, true);\n    }\n    // #region Buffer API\n    getEOL() {\n        return this._EOL;\n    }\n    setEOL(newEOL) {\n        this._EOL = newEOL;\n        this._EOLLength = this._EOL.length;\n        this.normalizeEOL(newEOL);\n    }\n    createSnapshot(BOM) {\n        return new PieceTreeSnapshot(this, BOM);\n    }\n    getOffsetAt(lineNumber, column) {\n        let leftLen = 0; // inorder\n        let x = this.root;\n        while (x !== SENTINEL) {\n            if (x.left !== SENTINEL && x.lf_left + 1 >= lineNumber) {\n                x = x.left;\n            }\n            else if (x.lf_left + x.piece.lineFeedCnt + 1 >= lineNumber) {\n                leftLen += x.size_left;\n                // lineNumber >= 2\n                const accumualtedValInCurrentIndex = this.getAccumulatedValue(x, lineNumber - x.lf_left - 2);\n                return leftLen += accumualtedValInCurrentIndex + column - 1;\n            }\n            else {\n                lineNumber -= x.lf_left + x.piece.lineFeedCnt;\n                leftLen += x.size_left + x.piece.length;\n                x = x.right;\n            }\n        }\n        return leftLen;\n    }\n    getPositionAt(offset) {\n        offset = Math.floor(offset);\n        offset = Math.max(0, offset);\n        let x = this.root;\n        let lfCnt = 0;\n        const originalOffset = offset;\n        while (x !== SENTINEL) {\n            if (x.size_left !== 0 && x.size_left >= offset) {\n                x = x.left;\n            }\n            else if (x.size_left + x.piece.length >= offset) {\n                const out = this.getIndexOf(x, offset - x.size_left);\n                lfCnt += x.lf_left + out.index;\n                if (out.index === 0) {\n                    const lineStartOffset = this.getOffsetAt(lfCnt + 1, 1);\n                    const column = originalOffset - lineStartOffset;\n                    return new Position(lfCnt + 1, column + 1);\n                }\n                return new Position(lfCnt + 1, out.remainder + 1);\n            }\n            else {\n                offset -= x.size_left + x.piece.length;\n                lfCnt += x.lf_left + x.piece.lineFeedCnt;\n                if (x.right === SENTINEL) {\n                    // last node\n                    const lineStartOffset = this.getOffsetAt(lfCnt + 1, 1);\n                    const column = originalOffset - offset - lineStartOffset;\n                    return new Position(lfCnt + 1, column + 1);\n                }\n                else {\n                    x = x.right;\n                }\n            }\n        }\n        return new Position(1, 1);\n    }\n    getValueInRange(range, eol) {\n        if (range.startLineNumber === range.endLineNumber && range.startColumn === range.endColumn) {\n            return '';\n        }\n        const startPosition = this.nodeAt2(range.startLineNumber, range.startColumn);\n        const endPosition = this.nodeAt2(range.endLineNumber, range.endColumn);\n        const value = this.getValueInRange2(startPosition, endPosition);\n        if (eol) {\n            if (eol !== this._EOL || !this._EOLNormalized) {\n                return value.replace(/\\r\\n|\\r|\\n/g, eol);\n            }\n            if (eol === this.getEOL() && this._EOLNormalized) {\n                if (eol === '\\r\\n') {\n                }\n                return value;\n            }\n            return value.replace(/\\r\\n|\\r|\\n/g, eol);\n        }\n        return value;\n    }\n    getValueInRange2(startPosition, endPosition) {\n        if (startPosition.node === endPosition.node) {\n            const node = startPosition.node;\n            const buffer = this._buffers[node.piece.bufferIndex].buffer;\n            const startOffset = this.offsetInBuffer(node.piece.bufferIndex, node.piece.start);\n            return buffer.substring(startOffset + startPosition.remainder, startOffset + endPosition.remainder);\n        }\n        let x = startPosition.node;\n        const buffer = this._buffers[x.piece.bufferIndex].buffer;\n        const startOffset = this.offsetInBuffer(x.piece.bufferIndex, x.piece.start);\n        let ret = buffer.substring(startOffset + startPosition.remainder, startOffset + x.piece.length);\n        x = x.next();\n        while (x !== SENTINEL) {\n            const buffer = this._buffers[x.piece.bufferIndex].buffer;\n            const startOffset = this.offsetInBuffer(x.piece.bufferIndex, x.piece.start);\n            if (x === endPosition.node) {\n                ret += buffer.substring(startOffset, startOffset + endPosition.remainder);\n                break;\n            }\n            else {\n                ret += buffer.substr(startOffset, x.piece.length);\n            }\n            x = x.next();\n        }\n        return ret;\n    }\n    getLinesContent() {\n        const lines = [];\n        let linesLength = 0;\n        let currentLine = '';\n        let danglingCR = false;\n        this.iterate(this.root, node => {\n            if (node === SENTINEL) {\n                return true;\n            }\n            const piece = node.piece;\n            let pieceLength = piece.length;\n            if (pieceLength === 0) {\n                return true;\n            }\n            const buffer = this._buffers[piece.bufferIndex].buffer;\n            const lineStarts = this._buffers[piece.bufferIndex].lineStarts;\n            const pieceStartLine = piece.start.line;\n            const pieceEndLine = piece.end.line;\n            let pieceStartOffset = lineStarts[pieceStartLine] + piece.start.column;\n            if (danglingCR) {\n                if (buffer.charCodeAt(pieceStartOffset) === 10 /* CharCode.LineFeed */) {\n                    // pretend the \\n was in the previous piece..\n                    pieceStartOffset++;\n                    pieceLength--;\n                }\n                lines[linesLength++] = currentLine;\n                currentLine = '';\n                danglingCR = false;\n                if (pieceLength === 0) {\n                    return true;\n                }\n            }\n            if (pieceStartLine === pieceEndLine) {\n                // this piece has no new lines\n                if (!this._EOLNormalized && buffer.charCodeAt(pieceStartOffset + pieceLength - 1) === 13 /* CharCode.CarriageReturn */) {\n                    danglingCR = true;\n                    currentLine += buffer.substr(pieceStartOffset, pieceLength - 1);\n                }\n                else {\n                    currentLine += buffer.substr(pieceStartOffset, pieceLength);\n                }\n                return true;\n            }\n            // add the text before the first line start in this piece\n            currentLine += (this._EOLNormalized\n                ? buffer.substring(pieceStartOffset, Math.max(pieceStartOffset, lineStarts[pieceStartLine + 1] - this._EOLLength))\n                : buffer.substring(pieceStartOffset, lineStarts[pieceStartLine + 1]).replace(/(\\r\\n|\\r|\\n)$/, ''));\n            lines[linesLength++] = currentLine;\n            for (let line = pieceStartLine + 1; line < pieceEndLine; line++) {\n                currentLine = (this._EOLNormalized\n                    ? buffer.substring(lineStarts[line], lineStarts[line + 1] - this._EOLLength)\n                    : buffer.substring(lineStarts[line], lineStarts[line + 1]).replace(/(\\r\\n|\\r|\\n)$/, ''));\n                lines[linesLength++] = currentLine;\n            }\n            if (!this._EOLNormalized && buffer.charCodeAt(lineStarts[pieceEndLine] + piece.end.column - 1) === 13 /* CharCode.CarriageReturn */) {\n                danglingCR = true;\n                if (piece.end.column === 0) {\n                    // The last line ended with a \\r, let's undo the push, it will be pushed by next iteration\n                    linesLength--;\n                }\n                else {\n                    currentLine = buffer.substr(lineStarts[pieceEndLine], piece.end.column - 1);\n                }\n            }\n            else {\n                currentLine = buffer.substr(lineStarts[pieceEndLine], piece.end.column);\n            }\n            return true;\n        });\n        if (danglingCR) {\n            lines[linesLength++] = currentLine;\n            currentLine = '';\n        }\n        lines[linesLength++] = currentLine;\n        return lines;\n    }\n    getLength() {\n        return this._length;\n    }\n    getLineCount() {\n        return this._lineCnt;\n    }\n    getLineContent(lineNumber) {\n        if (this._lastVisitedLine.lineNumber === lineNumber) {\n            return this._lastVisitedLine.value;\n        }\n        this._lastVisitedLine.lineNumber = lineNumber;\n        if (lineNumber === this._lineCnt) {\n            this._lastVisitedLine.value = this.getLineRawContent(lineNumber);\n        }\n        else if (this._EOLNormalized) {\n            this._lastVisitedLine.value = this.getLineRawContent(lineNumber, this._EOLLength);\n        }\n        else {\n            this._lastVisitedLine.value = this.getLineRawContent(lineNumber).replace(/(\\r\\n|\\r|\\n)$/, '');\n        }\n        return this._lastVisitedLine.value;\n    }\n    _getCharCode(nodePos) {\n        if (nodePos.remainder === nodePos.node.piece.length) {\n            // the char we want to fetch is at the head of next node.\n            const matchingNode = nodePos.node.next();\n            if (!matchingNode) {\n                return 0;\n            }\n            const buffer = this._buffers[matchingNode.piece.bufferIndex];\n            const startOffset = this.offsetInBuffer(matchingNode.piece.bufferIndex, matchingNode.piece.start);\n            return buffer.buffer.charCodeAt(startOffset);\n        }\n        else {\n            const buffer = this._buffers[nodePos.node.piece.bufferIndex];\n            const startOffset = this.offsetInBuffer(nodePos.node.piece.bufferIndex, nodePos.node.piece.start);\n            const targetOffset = startOffset + nodePos.remainder;\n            return buffer.buffer.charCodeAt(targetOffset);\n        }\n    }\n    getLineCharCode(lineNumber, index) {\n        const nodePos = this.nodeAt2(lineNumber, index + 1);\n        return this._getCharCode(nodePos);\n    }\n    getLineLength(lineNumber) {\n        if (lineNumber === this.getLineCount()) {\n            const startOffset = this.getOffsetAt(lineNumber, 1);\n            return this.getLength() - startOffset;\n        }\n        return this.getOffsetAt(lineNumber + 1, 1) - this.getOffsetAt(lineNumber, 1) - this._EOLLength;\n    }\n    findMatchesInNode(node, searcher, startLineNumber, startColumn, startCursor, endCursor, searchData, captureMatches, limitResultCount, resultLen, result) {\n        const buffer = this._buffers[node.piece.bufferIndex];\n        const startOffsetInBuffer = this.offsetInBuffer(node.piece.bufferIndex, node.piece.start);\n        const start = this.offsetInBuffer(node.piece.bufferIndex, startCursor);\n        const end = this.offsetInBuffer(node.piece.bufferIndex, endCursor);\n        let m;\n        // Reset regex to search from the beginning\n        const ret = { line: 0, column: 0 };\n        let searchText;\n        let offsetInBuffer;\n        if (searcher._wordSeparators) {\n            searchText = buffer.buffer.substring(start, end);\n            offsetInBuffer = (offset) => offset + start;\n            searcher.reset(0);\n        }\n        else {\n            searchText = buffer.buffer;\n            offsetInBuffer = (offset) => offset;\n            searcher.reset(start);\n        }\n        do {\n            m = searcher.next(searchText);\n            if (m) {\n                if (offsetInBuffer(m.index) >= end) {\n                    return resultLen;\n                }\n                this.positionInBuffer(node, offsetInBuffer(m.index) - startOffsetInBuffer, ret);\n                const lineFeedCnt = this.getLineFeedCnt(node.piece.bufferIndex, startCursor, ret);\n                const retStartColumn = ret.line === startCursor.line ? ret.column - startCursor.column + startColumn : ret.column + 1;\n                const retEndColumn = retStartColumn + m[0].length;\n                result[resultLen++] = createFindMatch(new Range(startLineNumber + lineFeedCnt, retStartColumn, startLineNumber + lineFeedCnt, retEndColumn), m, captureMatches);\n                if (offsetInBuffer(m.index) + m[0].length >= end) {\n                    return resultLen;\n                }\n                if (resultLen >= limitResultCount) {\n                    return resultLen;\n                }\n            }\n        } while (m);\n        return resultLen;\n    }\n    findMatchesLineByLine(searchRange, searchData, captureMatches, limitResultCount) {\n        const result = [];\n        let resultLen = 0;\n        const searcher = new Searcher(searchData.wordSeparators, searchData.regex);\n        let startPosition = this.nodeAt2(searchRange.startLineNumber, searchRange.startColumn);\n        if (startPosition === null) {\n            return [];\n        }\n        const endPosition = this.nodeAt2(searchRange.endLineNumber, searchRange.endColumn);\n        if (endPosition === null) {\n            return [];\n        }\n        let start = this.positionInBuffer(startPosition.node, startPosition.remainder);\n        const end = this.positionInBuffer(endPosition.node, endPosition.remainder);\n        if (startPosition.node === endPosition.node) {\n            this.findMatchesInNode(startPosition.node, searcher, searchRange.startLineNumber, searchRange.startColumn, start, end, searchData, captureMatches, limitResultCount, resultLen, result);\n            return result;\n        }\n        let startLineNumber = searchRange.startLineNumber;\n        let currentNode = startPosition.node;\n        while (currentNode !== endPosition.node) {\n            const lineBreakCnt = this.getLineFeedCnt(currentNode.piece.bufferIndex, start, currentNode.piece.end);\n            if (lineBreakCnt >= 1) {\n                // last line break position\n                const lineStarts = this._buffers[currentNode.piece.bufferIndex].lineStarts;\n                const startOffsetInBuffer = this.offsetInBuffer(currentNode.piece.bufferIndex, currentNode.piece.start);\n                const nextLineStartOffset = lineStarts[start.line + lineBreakCnt];\n                const startColumn = startLineNumber === searchRange.startLineNumber ? searchRange.startColumn : 1;\n                resultLen = this.findMatchesInNode(currentNode, searcher, startLineNumber, startColumn, start, this.positionInBuffer(currentNode, nextLineStartOffset - startOffsetInBuffer), searchData, captureMatches, limitResultCount, resultLen, result);\n                if (resultLen >= limitResultCount) {\n                    return result;\n                }\n                startLineNumber += lineBreakCnt;\n            }\n            const startColumn = startLineNumber === searchRange.startLineNumber ? searchRange.startColumn - 1 : 0;\n            // search for the remaining content\n            if (startLineNumber === searchRange.endLineNumber) {\n                const text = this.getLineContent(startLineNumber).substring(startColumn, searchRange.endColumn - 1);\n                resultLen = this._findMatchesInLine(searchData, searcher, text, searchRange.endLineNumber, startColumn, resultLen, result, captureMatches, limitResultCount);\n                return result;\n            }\n            resultLen = this._findMatchesInLine(searchData, searcher, this.getLineContent(startLineNumber).substr(startColumn), startLineNumber, startColumn, resultLen, result, captureMatches, limitResultCount);\n            if (resultLen >= limitResultCount) {\n                return result;\n            }\n            startLineNumber++;\n            startPosition = this.nodeAt2(startLineNumber, 1);\n            currentNode = startPosition.node;\n            start = this.positionInBuffer(startPosition.node, startPosition.remainder);\n        }\n        if (startLineNumber === searchRange.endLineNumber) {\n            const startColumn = startLineNumber === searchRange.startLineNumber ? searchRange.startColumn - 1 : 0;\n            const text = this.getLineContent(startLineNumber).substring(startColumn, searchRange.endColumn - 1);\n            resultLen = this._findMatchesInLine(searchData, searcher, text, searchRange.endLineNumber, startColumn, resultLen, result, captureMatches, limitResultCount);\n            return result;\n        }\n        const startColumn = startLineNumber === searchRange.startLineNumber ? searchRange.startColumn : 1;\n        resultLen = this.findMatchesInNode(endPosition.node, searcher, startLineNumber, startColumn, start, end, searchData, captureMatches, limitResultCount, resultLen, result);\n        return result;\n    }\n    _findMatchesInLine(searchData, searcher, text, lineNumber, deltaOffset, resultLen, result, captureMatches, limitResultCount) {\n        const wordSeparators = searchData.wordSeparators;\n        if (!captureMatches && searchData.simpleSearch) {\n            const searchString = searchData.simpleSearch;\n            const searchStringLen = searchString.length;\n            const textLength = text.length;\n            let lastMatchIndex = -searchStringLen;\n            while ((lastMatchIndex = text.indexOf(searchString, lastMatchIndex + searchStringLen)) !== -1) {\n                if (!wordSeparators || isValidMatch(wordSeparators, text, textLength, lastMatchIndex, searchStringLen)) {\n                    result[resultLen++] = new FindMatch(new Range(lineNumber, lastMatchIndex + 1 + deltaOffset, lineNumber, lastMatchIndex + 1 + searchStringLen + deltaOffset), null);\n                    if (resultLen >= limitResultCount) {\n                        return resultLen;\n                    }\n                }\n            }\n            return resultLen;\n        }\n        let m;\n        // Reset regex to search from the beginning\n        searcher.reset(0);\n        do {\n            m = searcher.next(text);\n            if (m) {\n                result[resultLen++] = createFindMatch(new Range(lineNumber, m.index + 1 + deltaOffset, lineNumber, m.index + 1 + m[0].length + deltaOffset), m, captureMatches);\n                if (resultLen >= limitResultCount) {\n                    return resultLen;\n                }\n            }\n        } while (m);\n        return resultLen;\n    }\n    // #endregion\n    // #region Piece Table\n    insert(offset, value, eolNormalized = false) {\n        this._EOLNormalized = this._EOLNormalized && eolNormalized;\n        this._lastVisitedLine.lineNumber = 0;\n        this._lastVisitedLine.value = '';\n        if (this.root !== SENTINEL) {\n            const { node, remainder, nodeStartOffset } = this.nodeAt(offset);\n            const piece = node.piece;\n            const bufferIndex = piece.bufferIndex;\n            const insertPosInBuffer = this.positionInBuffer(node, remainder);\n            if (node.piece.bufferIndex === 0 &&\n                piece.end.line === this._lastChangeBufferPos.line &&\n                piece.end.column === this._lastChangeBufferPos.column &&\n                (nodeStartOffset + piece.length === offset) &&\n                value.length < AverageBufferSize) {\n                // changed buffer\n                this.appendToNode(node, value);\n                this.computeBufferMetadata();\n                return;\n            }\n            if (nodeStartOffset === offset) {\n                this.insertContentToNodeLeft(value, node);\n                this._searchCache.validate(offset);\n            }\n            else if (nodeStartOffset + node.piece.length > offset) {\n                // we are inserting into the middle of a node.\n                const nodesToDel = [];\n                let newRightPiece = new Piece(piece.bufferIndex, insertPosInBuffer, piece.end, this.getLineFeedCnt(piece.bufferIndex, insertPosInBuffer, piece.end), this.offsetInBuffer(bufferIndex, piece.end) - this.offsetInBuffer(bufferIndex, insertPosInBuffer));\n                if (this.shouldCheckCRLF() && this.endWithCR(value)) {\n                    const headOfRight = this.nodeCharCodeAt(node, remainder);\n                    if (headOfRight === 10 /** \\n */) {\n                        const newStart = { line: newRightPiece.start.line + 1, column: 0 };\n                        newRightPiece = new Piece(newRightPiece.bufferIndex, newStart, newRightPiece.end, this.getLineFeedCnt(newRightPiece.bufferIndex, newStart, newRightPiece.end), newRightPiece.length - 1);\n                        value += '\\n';\n                    }\n                }\n                // reuse node for content before insertion point.\n                if (this.shouldCheckCRLF() && this.startWithLF(value)) {\n                    const tailOfLeft = this.nodeCharCodeAt(node, remainder - 1);\n                    if (tailOfLeft === 13 /** \\r */) {\n                        const previousPos = this.positionInBuffer(node, remainder - 1);\n                        this.deleteNodeTail(node, previousPos);\n                        value = '\\r' + value;\n                        if (node.piece.length === 0) {\n                            nodesToDel.push(node);\n                        }\n                    }\n                    else {\n                        this.deleteNodeTail(node, insertPosInBuffer);\n                    }\n                }\n                else {\n                    this.deleteNodeTail(node, insertPosInBuffer);\n                }\n                const newPieces = this.createNewPieces(value);\n                if (newRightPiece.length > 0) {\n                    this.rbInsertRight(node, newRightPiece);\n                }\n                let tmpNode = node;\n                for (let k = 0; k < newPieces.length; k++) {\n                    tmpNode = this.rbInsertRight(tmpNode, newPieces[k]);\n                }\n                this.deleteNodes(nodesToDel);\n            }\n            else {\n                this.insertContentToNodeRight(value, node);\n            }\n        }\n        else {\n            // insert new node\n            const pieces = this.createNewPieces(value);\n            let node = this.rbInsertLeft(null, pieces[0]);\n            for (let k = 1; k < pieces.length; k++) {\n                node = this.rbInsertRight(node, pieces[k]);\n            }\n        }\n        // todo, this is too brutal. Total line feed count should be updated the same way as lf_left.\n        this.computeBufferMetadata();\n    }\n    delete(offset, cnt) {\n        this._lastVisitedLine.lineNumber = 0;\n        this._lastVisitedLine.value = '';\n        if (cnt <= 0 || this.root === SENTINEL) {\n            return;\n        }\n        const startPosition = this.nodeAt(offset);\n        const endPosition = this.nodeAt(offset + cnt);\n        const startNode = startPosition.node;\n        const endNode = endPosition.node;\n        if (startNode === endNode) {\n            const startSplitPosInBuffer = this.positionInBuffer(startNode, startPosition.remainder);\n            const endSplitPosInBuffer = this.positionInBuffer(startNode, endPosition.remainder);\n            if (startPosition.nodeStartOffset === offset) {\n                if (cnt === startNode.piece.length) { // delete node\n                    const next = startNode.next();\n                    rbDelete(this, startNode);\n                    this.validateCRLFWithPrevNode(next);\n                    this.computeBufferMetadata();\n                    return;\n                }\n                this.deleteNodeHead(startNode, endSplitPosInBuffer);\n                this._searchCache.validate(offset);\n                this.validateCRLFWithPrevNode(startNode);\n                this.computeBufferMetadata();\n                return;\n            }\n            if (startPosition.nodeStartOffset + startNode.piece.length === offset + cnt) {\n                this.deleteNodeTail(startNode, startSplitPosInBuffer);\n                this.validateCRLFWithNextNode(startNode);\n                this.computeBufferMetadata();\n                return;\n            }\n            // delete content in the middle, this node will be splitted to nodes\n            this.shrinkNode(startNode, startSplitPosInBuffer, endSplitPosInBuffer);\n            this.computeBufferMetadata();\n            return;\n        }\n        const nodesToDel = [];\n        const startSplitPosInBuffer = this.positionInBuffer(startNode, startPosition.remainder);\n        this.deleteNodeTail(startNode, startSplitPosInBuffer);\n        this._searchCache.validate(offset);\n        if (startNode.piece.length === 0) {\n            nodesToDel.push(startNode);\n        }\n        // update last touched node\n        const endSplitPosInBuffer = this.positionInBuffer(endNode, endPosition.remainder);\n        this.deleteNodeHead(endNode, endSplitPosInBuffer);\n        if (endNode.piece.length === 0) {\n            nodesToDel.push(endNode);\n        }\n        // delete nodes in between\n        const secondNode = startNode.next();\n        for (let node = secondNode; node !== SENTINEL && node !== endNode; node = node.next()) {\n            nodesToDel.push(node);\n        }\n        const prev = startNode.piece.length === 0 ? startNode.prev() : startNode;\n        this.deleteNodes(nodesToDel);\n        this.validateCRLFWithNextNode(prev);\n        this.computeBufferMetadata();\n    }\n    insertContentToNodeLeft(value, node) {\n        // we are inserting content to the beginning of node\n        const nodesToDel = [];\n        if (this.shouldCheckCRLF() && this.endWithCR(value) && this.startWithLF(node)) {\n            // move `\\n` to new node.\n            const piece = node.piece;\n            const newStart = { line: piece.start.line + 1, column: 0 };\n            const nPiece = new Piece(piece.bufferIndex, newStart, piece.end, this.getLineFeedCnt(piece.bufferIndex, newStart, piece.end), piece.length - 1);\n            node.piece = nPiece;\n            value += '\\n';\n            updateTreeMetadata(this, node, -1, -1);\n            if (node.piece.length === 0) {\n                nodesToDel.push(node);\n            }\n        }\n        const newPieces = this.createNewPieces(value);\n        let newNode = this.rbInsertLeft(node, newPieces[newPieces.length - 1]);\n        for (let k = newPieces.length - 2; k >= 0; k--) {\n            newNode = this.rbInsertLeft(newNode, newPieces[k]);\n        }\n        this.validateCRLFWithPrevNode(newNode);\n        this.deleteNodes(nodesToDel);\n    }\n    insertContentToNodeRight(value, node) {\n        // we are inserting to the right of this node.\n        if (this.adjustCarriageReturnFromNext(value, node)) {\n            // move \\n to the new node.\n            value += '\\n';\n        }\n        const newPieces = this.createNewPieces(value);\n        const newNode = this.rbInsertRight(node, newPieces[0]);\n        let tmpNode = newNode;\n        for (let k = 1; k < newPieces.length; k++) {\n            tmpNode = this.rbInsertRight(tmpNode, newPieces[k]);\n        }\n        this.validateCRLFWithPrevNode(newNode);\n    }\n    positionInBuffer(node, remainder, ret) {\n        const piece = node.piece;\n        const bufferIndex = node.piece.bufferIndex;\n        const lineStarts = this._buffers[bufferIndex].lineStarts;\n        const startOffset = lineStarts[piece.start.line] + piece.start.column;\n        const offset = startOffset + remainder;\n        // binary search offset between startOffset and endOffset\n        let low = piece.start.line;\n        let high = piece.end.line;\n        let mid = 0;\n        let midStop = 0;\n        let midStart = 0;\n        while (low <= high) {\n            mid = low + ((high - low) / 2) | 0;\n            midStart = lineStarts[mid];\n            if (mid === high) {\n                break;\n            }\n            midStop = lineStarts[mid + 1];\n            if (offset < midStart) {\n                high = mid - 1;\n            }\n            else if (offset >= midStop) {\n                low = mid + 1;\n            }\n            else {\n                break;\n            }\n        }\n        if (ret) {\n            ret.line = mid;\n            ret.column = offset - midStart;\n            return null;\n        }\n        return {\n            line: mid,\n            column: offset - midStart\n        };\n    }\n    getLineFeedCnt(bufferIndex, start, end) {\n        // we don't need to worry about start: abc\\r|\\n, or abc|\\r, or abc|\\n, or abc|\\r\\n doesn't change the fact that, there is one line break after start.\n        // now let's take care of end: abc\\r|\\n, if end is in between \\r and \\n, we need to add line feed count by 1\n        if (end.column === 0) {\n            return end.line - start.line;\n        }\n        const lineStarts = this._buffers[bufferIndex].lineStarts;\n        if (end.line === lineStarts.length - 1) { // it means, there is no \\n after end, otherwise, there will be one more lineStart.\n            return end.line - start.line;\n        }\n        const nextLineStartOffset = lineStarts[end.line + 1];\n        const endOffset = lineStarts[end.line] + end.column;\n        if (nextLineStartOffset > endOffset + 1) { // there are more than 1 character after end, which means it can't be \\n\n            return end.line - start.line;\n        }\n        // endOffset + 1 === nextLineStartOffset\n        // character at endOffset is \\n, so we check the character before first\n        // if character at endOffset is \\r, end.column is 0 and we can't get here.\n        const previousCharOffset = endOffset - 1; // end.column > 0 so it's okay.\n        const buffer = this._buffers[bufferIndex].buffer;\n        if (buffer.charCodeAt(previousCharOffset) === 13) {\n            return end.line - start.line + 1;\n        }\n        else {\n            return end.line - start.line;\n        }\n    }\n    offsetInBuffer(bufferIndex, cursor) {\n        const lineStarts = this._buffers[bufferIndex].lineStarts;\n        return lineStarts[cursor.line] + cursor.column;\n    }\n    deleteNodes(nodes) {\n        for (let i = 0; i < nodes.length; i++) {\n            rbDelete(this, nodes[i]);\n        }\n    }\n    createNewPieces(text) {\n        if (text.length > AverageBufferSize) {\n            // the content is large, operations like substring, charCode becomes slow\n            // so here we split it into smaller chunks, just like what we did for CR/LF normalization\n            const newPieces = [];\n            while (text.length > AverageBufferSize) {\n                const lastChar = text.charCodeAt(AverageBufferSize - 1);\n                let splitText;\n                if (lastChar === 13 /* CharCode.CarriageReturn */ || (lastChar >= 0xD800 && lastChar <= 0xDBFF)) {\n                    // last character is \\r or a high surrogate => keep it back\n                    splitText = text.substring(0, AverageBufferSize - 1);\n                    text = text.substring(AverageBufferSize - 1);\n                }\n                else {\n                    splitText = text.substring(0, AverageBufferSize);\n                    text = text.substring(AverageBufferSize);\n                }\n                const lineStarts = createLineStartsFast(splitText);\n                newPieces.push(new Piece(this._buffers.length, /* buffer index */ { line: 0, column: 0 }, { line: lineStarts.length - 1, column: splitText.length - lineStarts[lineStarts.length - 1] }, lineStarts.length - 1, splitText.length));\n                this._buffers.push(new StringBuffer(splitText, lineStarts));\n            }\n            const lineStarts = createLineStartsFast(text);\n            newPieces.push(new Piece(this._buffers.length, /* buffer index */ { line: 0, column: 0 }, { line: lineStarts.length - 1, column: text.length - lineStarts[lineStarts.length - 1] }, lineStarts.length - 1, text.length));\n            this._buffers.push(new StringBuffer(text, lineStarts));\n            return newPieces;\n        }\n        let startOffset = this._buffers[0].buffer.length;\n        const lineStarts = createLineStartsFast(text, false);\n        let start = this._lastChangeBufferPos;\n        if (this._buffers[0].lineStarts[this._buffers[0].lineStarts.length - 1] === startOffset\n            && startOffset !== 0\n            && this.startWithLF(text)\n            && this.endWithCR(this._buffers[0].buffer) // todo, we can check this._lastChangeBufferPos's column as it's the last one\n        ) {\n            this._lastChangeBufferPos = { line: this._lastChangeBufferPos.line, column: this._lastChangeBufferPos.column + 1 };\n            start = this._lastChangeBufferPos;\n            for (let i = 0; i < lineStarts.length; i++) {\n                lineStarts[i] += startOffset + 1;\n            }\n            this._buffers[0].lineStarts = this._buffers[0].lineStarts.concat(lineStarts.slice(1));\n            this._buffers[0].buffer += '_' + text;\n            startOffset += 1;\n        }\n        else {\n            if (startOffset !== 0) {\n                for (let i = 0; i < lineStarts.length; i++) {\n                    lineStarts[i] += startOffset;\n                }\n            }\n            this._buffers[0].lineStarts = this._buffers[0].lineStarts.concat(lineStarts.slice(1));\n            this._buffers[0].buffer += text;\n        }\n        const endOffset = this._buffers[0].buffer.length;\n        const endIndex = this._buffers[0].lineStarts.length - 1;\n        const endColumn = endOffset - this._buffers[0].lineStarts[endIndex];\n        const endPos = { line: endIndex, column: endColumn };\n        const newPiece = new Piece(0, /** todo@peng */ start, endPos, this.getLineFeedCnt(0, start, endPos), endOffset - startOffset);\n        this._lastChangeBufferPos = endPos;\n        return [newPiece];\n    }\n    getLineRawContent(lineNumber, endOffset = 0) {\n        let x = this.root;\n        let ret = '';\n        const cache = this._searchCache.get2(lineNumber);\n        if (cache) {\n            x = cache.node;\n            const prevAccumulatedValue = this.getAccumulatedValue(x, lineNumber - cache.nodeStartLineNumber - 1);\n            const buffer = this._buffers[x.piece.bufferIndex].buffer;\n            const startOffset = this.offsetInBuffer(x.piece.bufferIndex, x.piece.start);\n            if (cache.nodeStartLineNumber + x.piece.lineFeedCnt === lineNumber) {\n                ret = buffer.substring(startOffset + prevAccumulatedValue, startOffset + x.piece.length);\n            }\n            else {\n                const accumulatedValue = this.getAccumulatedValue(x, lineNumber - cache.nodeStartLineNumber);\n                return buffer.substring(startOffset + prevAccumulatedValue, startOffset + accumulatedValue - endOffset);\n            }\n        }\n        else {\n            let nodeStartOffset = 0;\n            const originalLineNumber = lineNumber;\n            while (x !== SENTINEL) {\n                if (x.left !== SENTINEL && x.lf_left >= lineNumber - 1) {\n                    x = x.left;\n                }\n                else if (x.lf_left + x.piece.lineFeedCnt > lineNumber - 1) {\n                    const prevAccumulatedValue = this.getAccumulatedValue(x, lineNumber - x.lf_left - 2);\n                    const accumulatedValue = this.getAccumulatedValue(x, lineNumber - x.lf_left - 1);\n                    const buffer = this._buffers[x.piece.bufferIndex].buffer;\n                    const startOffset = this.offsetInBuffer(x.piece.bufferIndex, x.piece.start);\n                    nodeStartOffset += x.size_left;\n                    this._searchCache.set({\n                        node: x,\n                        nodeStartOffset,\n                        nodeStartLineNumber: originalLineNumber - (lineNumber - 1 - x.lf_left)\n                    });\n                    return buffer.substring(startOffset + prevAccumulatedValue, startOffset + accumulatedValue - endOffset);\n                }\n                else if (x.lf_left + x.piece.lineFeedCnt === lineNumber - 1) {\n                    const prevAccumulatedValue = this.getAccumulatedValue(x, lineNumber - x.lf_left - 2);\n                    const buffer = this._buffers[x.piece.bufferIndex].buffer;\n                    const startOffset = this.offsetInBuffer(x.piece.bufferIndex, x.piece.start);\n                    ret = buffer.substring(startOffset + prevAccumulatedValue, startOffset + x.piece.length);\n                    break;\n                }\n                else {\n                    lineNumber -= x.lf_left + x.piece.lineFeedCnt;\n                    nodeStartOffset += x.size_left + x.piece.length;\n                    x = x.right;\n                }\n            }\n        }\n        // search in order, to find the node contains end column\n        x = x.next();\n        while (x !== SENTINEL) {\n            const buffer = this._buffers[x.piece.bufferIndex].buffer;\n            if (x.piece.lineFeedCnt > 0) {\n                const accumulatedValue = this.getAccumulatedValue(x, 0);\n                const startOffset = this.offsetInBuffer(x.piece.bufferIndex, x.piece.start);\n                ret += buffer.substring(startOffset, startOffset + accumulatedValue - endOffset);\n                return ret;\n            }\n            else {\n                const startOffset = this.offsetInBuffer(x.piece.bufferIndex, x.piece.start);\n                ret += buffer.substr(startOffset, x.piece.length);\n            }\n            x = x.next();\n        }\n        return ret;\n    }\n    computeBufferMetadata() {\n        let x = this.root;\n        let lfCnt = 1;\n        let len = 0;\n        while (x !== SENTINEL) {\n            lfCnt += x.lf_left + x.piece.lineFeedCnt;\n            len += x.size_left + x.piece.length;\n            x = x.right;\n        }\n        this._lineCnt = lfCnt;\n        this._length = len;\n        this._searchCache.validate(this._length);\n    }\n    // #region node operations\n    getIndexOf(node, accumulatedValue) {\n        const piece = node.piece;\n        const pos = this.positionInBuffer(node, accumulatedValue);\n        const lineCnt = pos.line - piece.start.line;\n        if (this.offsetInBuffer(piece.bufferIndex, piece.end) - this.offsetInBuffer(piece.bufferIndex, piece.start) === accumulatedValue) {\n            // we are checking the end of this node, so a CRLF check is necessary.\n            const realLineCnt = this.getLineFeedCnt(node.piece.bufferIndex, piece.start, pos);\n            if (realLineCnt !== lineCnt) {\n                // aha yes, CRLF\n                return { index: realLineCnt, remainder: 0 };\n            }\n        }\n        return { index: lineCnt, remainder: pos.column };\n    }\n    getAccumulatedValue(node, index) {\n        if (index < 0) {\n            return 0;\n        }\n        const piece = node.piece;\n        const lineStarts = this._buffers[piece.bufferIndex].lineStarts;\n        const expectedLineStartIndex = piece.start.line + index + 1;\n        if (expectedLineStartIndex > piece.end.line) {\n            return lineStarts[piece.end.line] + piece.end.column - lineStarts[piece.start.line] - piece.start.column;\n        }\n        else {\n            return lineStarts[expectedLineStartIndex] - lineStarts[piece.start.line] - piece.start.column;\n        }\n    }\n    deleteNodeTail(node, pos) {\n        const piece = node.piece;\n        const originalLFCnt = piece.lineFeedCnt;\n        const originalEndOffset = this.offsetInBuffer(piece.bufferIndex, piece.end);\n        const newEnd = pos;\n        const newEndOffset = this.offsetInBuffer(piece.bufferIndex, newEnd);\n        const newLineFeedCnt = this.getLineFeedCnt(piece.bufferIndex, piece.start, newEnd);\n        const lf_delta = newLineFeedCnt - originalLFCnt;\n        const size_delta = newEndOffset - originalEndOffset;\n        const newLength = piece.length + size_delta;\n        node.piece = new Piece(piece.bufferIndex, piece.start, newEnd, newLineFeedCnt, newLength);\n        updateTreeMetadata(this, node, size_delta, lf_delta);\n    }\n    deleteNodeHead(node, pos) {\n        const piece = node.piece;\n        const originalLFCnt = piece.lineFeedCnt;\n        const originalStartOffset = this.offsetInBuffer(piece.bufferIndex, piece.start);\n        const newStart = pos;\n        const newLineFeedCnt = this.getLineFeedCnt(piece.bufferIndex, newStart, piece.end);\n        const newStartOffset = this.offsetInBuffer(piece.bufferIndex, newStart);\n        const lf_delta = newLineFeedCnt - originalLFCnt;\n        const size_delta = originalStartOffset - newStartOffset;\n        const newLength = piece.length + size_delta;\n        node.piece = new Piece(piece.bufferIndex, newStart, piece.end, newLineFeedCnt, newLength);\n        updateTreeMetadata(this, node, size_delta, lf_delta);\n    }\n    shrinkNode(node, start, end) {\n        const piece = node.piece;\n        const originalStartPos = piece.start;\n        const originalEndPos = piece.end;\n        // old piece, originalStartPos, start\n        const oldLength = piece.length;\n        const oldLFCnt = piece.lineFeedCnt;\n        const newEnd = start;\n        const newLineFeedCnt = this.getLineFeedCnt(piece.bufferIndex, piece.start, newEnd);\n        const newLength = this.offsetInBuffer(piece.bufferIndex, start) - this.offsetInBuffer(piece.bufferIndex, originalStartPos);\n        node.piece = new Piece(piece.bufferIndex, piece.start, newEnd, newLineFeedCnt, newLength);\n        updateTreeMetadata(this, node, newLength - oldLength, newLineFeedCnt - oldLFCnt);\n        // new right piece, end, originalEndPos\n        const newPiece = new Piece(piece.bufferIndex, end, originalEndPos, this.getLineFeedCnt(piece.bufferIndex, end, originalEndPos), this.offsetInBuffer(piece.bufferIndex, originalEndPos) - this.offsetInBuffer(piece.bufferIndex, end));\n        const newNode = this.rbInsertRight(node, newPiece);\n        this.validateCRLFWithPrevNode(newNode);\n    }\n    appendToNode(node, value) {\n        if (this.adjustCarriageReturnFromNext(value, node)) {\n            value += '\\n';\n        }\n        const hitCRLF = this.shouldCheckCRLF() && this.startWithLF(value) && this.endWithCR(node);\n        const startOffset = this._buffers[0].buffer.length;\n        this._buffers[0].buffer += value;\n        const lineStarts = createLineStartsFast(value, false);\n        for (let i = 0; i < lineStarts.length; i++) {\n            lineStarts[i] += startOffset;\n        }\n        if (hitCRLF) {\n            const prevStartOffset = this._buffers[0].lineStarts[this._buffers[0].lineStarts.length - 2];\n            this._buffers[0].lineStarts.pop();\n            // _lastChangeBufferPos is already wrong\n            this._lastChangeBufferPos = { line: this._lastChangeBufferPos.line - 1, column: startOffset - prevStartOffset };\n        }\n        this._buffers[0].lineStarts = this._buffers[0].lineStarts.concat(lineStarts.slice(1));\n        const endIndex = this._buffers[0].lineStarts.length - 1;\n        const endColumn = this._buffers[0].buffer.length - this._buffers[0].lineStarts[endIndex];\n        const newEnd = { line: endIndex, column: endColumn };\n        const newLength = node.piece.length + value.length;\n        const oldLineFeedCnt = node.piece.lineFeedCnt;\n        const newLineFeedCnt = this.getLineFeedCnt(0, node.piece.start, newEnd);\n        const lf_delta = newLineFeedCnt - oldLineFeedCnt;\n        node.piece = new Piece(node.piece.bufferIndex, node.piece.start, newEnd, newLineFeedCnt, newLength);\n        this._lastChangeBufferPos = newEnd;\n        updateTreeMetadata(this, node, value.length, lf_delta);\n    }\n    nodeAt(offset) {\n        let x = this.root;\n        const cache = this._searchCache.get(offset);\n        if (cache) {\n            return {\n                node: cache.node,\n                nodeStartOffset: cache.nodeStartOffset,\n                remainder: offset - cache.nodeStartOffset\n            };\n        }\n        let nodeStartOffset = 0;\n        while (x !== SENTINEL) {\n            if (x.size_left > offset) {\n                x = x.left;\n            }\n            else if (x.size_left + x.piece.length >= offset) {\n                nodeStartOffset += x.size_left;\n                const ret = {\n                    node: x,\n                    remainder: offset - x.size_left,\n                    nodeStartOffset\n                };\n                this._searchCache.set(ret);\n                return ret;\n            }\n            else {\n                offset -= x.size_left + x.piece.length;\n                nodeStartOffset += x.size_left + x.piece.length;\n                x = x.right;\n            }\n        }\n        return null;\n    }\n    nodeAt2(lineNumber, column) {\n        let x = this.root;\n        let nodeStartOffset = 0;\n        while (x !== SENTINEL) {\n            if (x.left !== SENTINEL && x.lf_left >= lineNumber - 1) {\n                x = x.left;\n            }\n            else if (x.lf_left + x.piece.lineFeedCnt > lineNumber - 1) {\n                const prevAccumualtedValue = this.getAccumulatedValue(x, lineNumber - x.lf_left - 2);\n                const accumulatedValue = this.getAccumulatedValue(x, lineNumber - x.lf_left - 1);\n                nodeStartOffset += x.size_left;\n                return {\n                    node: x,\n                    remainder: Math.min(prevAccumualtedValue + column - 1, accumulatedValue),\n                    nodeStartOffset\n                };\n            }\n            else if (x.lf_left + x.piece.lineFeedCnt === lineNumber - 1) {\n                const prevAccumualtedValue = this.getAccumulatedValue(x, lineNumber - x.lf_left - 2);\n                if (prevAccumualtedValue + column - 1 <= x.piece.length) {\n                    return {\n                        node: x,\n                        remainder: prevAccumualtedValue + column - 1,\n                        nodeStartOffset\n                    };\n                }\n                else {\n                    column -= x.piece.length - prevAccumualtedValue;\n                    break;\n                }\n            }\n            else {\n                lineNumber -= x.lf_left + x.piece.lineFeedCnt;\n                nodeStartOffset += x.size_left + x.piece.length;\n                x = x.right;\n            }\n        }\n        // search in order, to find the node contains position.column\n        x = x.next();\n        while (x !== SENTINEL) {\n            if (x.piece.lineFeedCnt > 0) {\n                const accumulatedValue = this.getAccumulatedValue(x, 0);\n                const nodeStartOffset = this.offsetOfNode(x);\n                return {\n                    node: x,\n                    remainder: Math.min(column - 1, accumulatedValue),\n                    nodeStartOffset\n                };\n            }\n            else {\n                if (x.piece.length >= column - 1) {\n                    const nodeStartOffset = this.offsetOfNode(x);\n                    return {\n                        node: x,\n                        remainder: column - 1,\n                        nodeStartOffset\n                    };\n                }\n                else {\n                    column -= x.piece.length;\n                }\n            }\n            x = x.next();\n        }\n        return null;\n    }\n    nodeCharCodeAt(node, offset) {\n        if (node.piece.lineFeedCnt < 1) {\n            return -1;\n        }\n        const buffer = this._buffers[node.piece.bufferIndex];\n        const newOffset = this.offsetInBuffer(node.piece.bufferIndex, node.piece.start) + offset;\n        return buffer.buffer.charCodeAt(newOffset);\n    }\n    offsetOfNode(node) {\n        if (!node) {\n            return 0;\n        }\n        let pos = node.size_left;\n        while (node !== this.root) {\n            if (node.parent.right === node) {\n                pos += node.parent.size_left + node.parent.piece.length;\n            }\n            node = node.parent;\n        }\n        return pos;\n    }\n    // #endregion\n    // #region CRLF\n    shouldCheckCRLF() {\n        return !(this._EOLNormalized && this._EOL === '\\n');\n    }\n    startWithLF(val) {\n        if (typeof val === 'string') {\n            return val.charCodeAt(0) === 10;\n        }\n        if (val === SENTINEL || val.piece.lineFeedCnt === 0) {\n            return false;\n        }\n        const piece = val.piece;\n        const lineStarts = this._buffers[piece.bufferIndex].lineStarts;\n        const line = piece.start.line;\n        const startOffset = lineStarts[line] + piece.start.column;\n        if (line === lineStarts.length - 1) {\n            // last line, so there is no line feed at the end of this line\n            return false;\n        }\n        const nextLineOffset = lineStarts[line + 1];\n        if (nextLineOffset > startOffset + 1) {\n            return false;\n        }\n        return this._buffers[piece.bufferIndex].buffer.charCodeAt(startOffset) === 10;\n    }\n    endWithCR(val) {\n        if (typeof val === 'string') {\n            return val.charCodeAt(val.length - 1) === 13;\n        }\n        if (val === SENTINEL || val.piece.lineFeedCnt === 0) {\n            return false;\n        }\n        return this.nodeCharCodeAt(val, val.piece.length - 1) === 13;\n    }\n    validateCRLFWithPrevNode(nextNode) {\n        if (this.shouldCheckCRLF() && this.startWithLF(nextNode)) {\n            const node = nextNode.prev();\n            if (this.endWithCR(node)) {\n                this.fixCRLF(node, nextNode);\n            }\n        }\n    }\n    validateCRLFWithNextNode(node) {\n        if (this.shouldCheckCRLF() && this.endWithCR(node)) {\n            const nextNode = node.next();\n            if (this.startWithLF(nextNode)) {\n                this.fixCRLF(node, nextNode);\n            }\n        }\n    }\n    fixCRLF(prev, next) {\n        const nodesToDel = [];\n        // update node\n        const lineStarts = this._buffers[prev.piece.bufferIndex].lineStarts;\n        let newEnd;\n        if (prev.piece.end.column === 0) {\n            // it means, last line ends with \\r, not \\r\\n\n            newEnd = { line: prev.piece.end.line - 1, column: lineStarts[prev.piece.end.line] - lineStarts[prev.piece.end.line - 1] - 1 };\n        }\n        else {\n            // \\r\\n\n            newEnd = { line: prev.piece.end.line, column: prev.piece.end.column - 1 };\n        }\n        const prevNewLength = prev.piece.length - 1;\n        const prevNewLFCnt = prev.piece.lineFeedCnt - 1;\n        prev.piece = new Piece(prev.piece.bufferIndex, prev.piece.start, newEnd, prevNewLFCnt, prevNewLength);\n        updateTreeMetadata(this, prev, -1, -1);\n        if (prev.piece.length === 0) {\n            nodesToDel.push(prev);\n        }\n        // update nextNode\n        const newStart = { line: next.piece.start.line + 1, column: 0 };\n        const newLength = next.piece.length - 1;\n        const newLineFeedCnt = this.getLineFeedCnt(next.piece.bufferIndex, newStart, next.piece.end);\n        next.piece = new Piece(next.piece.bufferIndex, newStart, next.piece.end, newLineFeedCnt, newLength);\n        updateTreeMetadata(this, next, -1, -1);\n        if (next.piece.length === 0) {\n            nodesToDel.push(next);\n        }\n        // create new piece which contains \\r\\n\n        const pieces = this.createNewPieces('\\r\\n');\n        this.rbInsertRight(prev, pieces[0]);\n        // delete empty nodes\n        for (let i = 0; i < nodesToDel.length; i++) {\n            rbDelete(this, nodesToDel[i]);\n        }\n    }\n    adjustCarriageReturnFromNext(value, node) {\n        if (this.shouldCheckCRLF() && this.endWithCR(value)) {\n            const nextNode = node.next();\n            if (this.startWithLF(nextNode)) {\n                // move `\\n` forward\n                value += '\\n';\n                if (nextNode.piece.length === 1) {\n                    rbDelete(this, nextNode);\n                }\n                else {\n                    const piece = nextNode.piece;\n                    const newStart = { line: piece.start.line + 1, column: 0 };\n                    const newLength = piece.length - 1;\n                    const newLineFeedCnt = this.getLineFeedCnt(piece.bufferIndex, newStart, piece.end);\n                    nextNode.piece = new Piece(piece.bufferIndex, newStart, piece.end, newLineFeedCnt, newLength);\n                    updateTreeMetadata(this, nextNode, -1, -1);\n                }\n                return true;\n            }\n        }\n        return false;\n    }\n    // #endregion\n    // #endregion\n    // #region Tree operations\n    iterate(node, callback) {\n        if (node === SENTINEL) {\n            return callback(SENTINEL);\n        }\n        const leftRet = this.iterate(node.left, callback);\n        if (!leftRet) {\n            return leftRet;\n        }\n        return callback(node) && this.iterate(node.right, callback);\n    }\n    getNodeContent(node) {\n        if (node === SENTINEL) {\n            return '';\n        }\n        const buffer = this._buffers[node.piece.bufferIndex];\n        const piece = node.piece;\n        const startOffset = this.offsetInBuffer(piece.bufferIndex, piece.start);\n        const endOffset = this.offsetInBuffer(piece.bufferIndex, piece.end);\n        const currentContent = buffer.buffer.substring(startOffset, endOffset);\n        return currentContent;\n    }\n    getPieceContent(piece) {\n        const buffer = this._buffers[piece.bufferIndex];\n        const startOffset = this.offsetInBuffer(piece.bufferIndex, piece.start);\n        const endOffset = this.offsetInBuffer(piece.bufferIndex, piece.end);\n        const currentContent = buffer.buffer.substring(startOffset, endOffset);\n        return currentContent;\n    }\n    /**\n     *      node              node\n     *     /  \\              /  \\\n     *    a   b    <----   a    b\n     *                         /\n     *                        z\n     */\n    rbInsertRight(node, p) {\n        const z = new TreeNode(p, 1 /* NodeColor.Red */);\n        z.left = SENTINEL;\n        z.right = SENTINEL;\n        z.parent = SENTINEL;\n        z.size_left = 0;\n        z.lf_left = 0;\n        const x = this.root;\n        if (x === SENTINEL) {\n            this.root = z;\n            z.color = 0 /* NodeColor.Black */;\n        }\n        else if (node.right === SENTINEL) {\n            node.right = z;\n            z.parent = node;\n        }\n        else {\n            const nextNode = leftest(node.right);\n            nextNode.left = z;\n            z.parent = nextNode;\n        }\n        fixInsert(this, z);\n        return z;\n    }\n    /**\n     *      node              node\n     *     /  \\              /  \\\n     *    a   b     ---->   a    b\n     *                       \\\n     *                        z\n     */\n    rbInsertLeft(node, p) {\n        const z = new TreeNode(p, 1 /* NodeColor.Red */);\n        z.left = SENTINEL;\n        z.right = SENTINEL;\n        z.parent = SENTINEL;\n        z.size_left = 0;\n        z.lf_left = 0;\n        if (this.root === SENTINEL) {\n            this.root = z;\n            z.color = 0 /* NodeColor.Black */;\n        }\n        else if (node.left === SENTINEL) {\n            node.left = z;\n            z.parent = node;\n        }\n        else {\n            const prevNode = righttest(node.left); // a\n            prevNode.right = z;\n            z.parent = prevNode;\n        }\n        fixInsert(this, z);\n        return z;\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,QAAQ,QAAQ,wBAAwB;AACjD,SAASC,KAAK,QAAQ,qBAAqB;AAC3C,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,kBAAkB,QAAQ,iBAAiB;AACjH,SAASC,QAAQ,EAAEC,eAAe,EAAEC,YAAY,QAAQ,uBAAuB;AAC/E;AACA,MAAMC,iBAAiB,GAAG,KAAK;AAC/B,SAASC,eAAeA,CAACC,GAAG,EAAE;EAC1B,IAAIC,CAAC;EACL,IAAID,GAAG,CAACA,GAAG,CAACE,MAAM,GAAG,CAAC,CAAC,GAAG,KAAK,EAAE;IAC7BD,CAAC,GAAG,IAAIE,WAAW,CAACH,GAAG,CAACE,MAAM,CAAC;EACnC,CAAC,MACI;IACDD,CAAC,GAAG,IAAIG,WAAW,CAACJ,GAAG,CAACE,MAAM,CAAC;EACnC;EACAD,CAAC,CAACI,GAAG,CAACL,GAAG,EAAE,CAAC,CAAC;EACb,OAAOC,CAAC;AACZ;AACA,MAAMK,UAAU,CAAC;EACbC,WAAWA,CAACC,UAAU,EAAEC,EAAE,EAAEC,EAAE,EAAEC,IAAI,EAAEC,YAAY,EAAE;IAChD,IAAI,CAACJ,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,YAAY,GAAGA,YAAY;EACpC;AACJ;AACA,OAAO,SAASC,oBAAoBA,CAACC,GAAG,EAAEC,QAAQ,GAAG,IAAI,EAAE;EACvD,MAAMd,CAAC,GAAG,CAAC,CAAC,CAAC;EACb,IAAIe,OAAO,GAAG,CAAC;EACf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGJ,GAAG,CAACZ,MAAM,EAAEe,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;IAC5C,MAAME,GAAG,GAAGL,GAAG,CAACM,UAAU,CAACH,CAAC,CAAC;IAC7B,IAAIE,GAAG,KAAK,EAAE,CAAC,+BAA+B;MAC1C,IAAIF,CAAC,GAAG,CAAC,GAAGC,GAAG,IAAIJ,GAAG,CAACM,UAAU,CAACH,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,yBAAyB;QACrE;QACAhB,CAAC,CAACe,OAAO,EAAE,CAAC,GAAGC,CAAC,GAAG,CAAC;QACpBA,CAAC,EAAE,CAAC,CAAC;MACT,CAAC,MACI;QACD;QACAhB,CAAC,CAACe,OAAO,EAAE,CAAC,GAAGC,CAAC,GAAG,CAAC;MACxB;IACJ,CAAC,MACI,IAAIE,GAAG,KAAK,EAAE,CAAC,yBAAyB;MACzClB,CAAC,CAACe,OAAO,EAAE,CAAC,GAAGC,CAAC,GAAG,CAAC;IACxB;EACJ;EACA,IAAIF,QAAQ,EAAE;IACV,OAAOhB,eAAe,CAACE,CAAC,CAAC;EAC7B,CAAC,MACI;IACD,OAAOA,CAAC;EACZ;AACJ;AACA,OAAO,SAASoB,gBAAgBA,CAACpB,CAAC,EAAEa,GAAG,EAAE;EACrCb,CAAC,CAACC,MAAM,GAAG,CAAC;EACZD,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;EACR,IAAIe,OAAO,GAAG,CAAC;EACf,IAAIP,EAAE,GAAG,CAAC;IAAEC,EAAE,GAAG,CAAC;IAAEC,IAAI,GAAG,CAAC;EAC5B,IAAIC,YAAY,GAAG,IAAI;EACvB,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGJ,GAAG,CAACZ,MAAM,EAAEe,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;IAC5C,MAAME,GAAG,GAAGL,GAAG,CAACM,UAAU,CAACH,CAAC,CAAC;IAC7B,IAAIE,GAAG,KAAK,EAAE,CAAC,+BAA+B;MAC1C,IAAIF,CAAC,GAAG,CAAC,GAAGC,GAAG,IAAIJ,GAAG,CAACM,UAAU,CAACH,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,yBAAyB;QACrE;QACAN,IAAI,EAAE;QACNV,CAAC,CAACe,OAAO,EAAE,CAAC,GAAGC,CAAC,GAAG,CAAC;QACpBA,CAAC,EAAE,CAAC,CAAC;MACT,CAAC,MACI;QACDR,EAAE,EAAE;QACJ;QACAR,CAAC,CAACe,OAAO,EAAE,CAAC,GAAGC,CAAC,GAAG,CAAC;MACxB;IACJ,CAAC,MACI,IAAIE,GAAG,KAAK,EAAE,CAAC,yBAAyB;MACzCT,EAAE,EAAE;MACJT,CAAC,CAACe,OAAO,EAAE,CAAC,GAAGC,CAAC,GAAG,CAAC;IACxB,CAAC,MACI;MACD,IAAIL,YAAY,EAAE;QACd,IAAIO,GAAG,KAAK,CAAC,CAAC,uBAAuBA,GAAG,GAAG,EAAE,IAAIA,GAAG,GAAG,GAAG,CAAC,EAAE;UACzDP,YAAY,GAAG,KAAK;QACxB;MACJ;IACJ;EACJ;EACA,MAAMU,MAAM,GAAG,IAAIhB,UAAU,CAACP,eAAe,CAACE,CAAC,CAAC,EAAEQ,EAAE,EAAEC,EAAE,EAAEC,IAAI,EAAEC,YAAY,CAAC;EAC7EX,CAAC,CAACC,MAAM,GAAG,CAAC;EACZ,OAAOoB,MAAM;AACjB;AACA,OAAO,MAAMC,KAAK,CAAC;EACfhB,WAAWA,CAACiB,WAAW,EAAEC,KAAK,EAAEC,GAAG,EAAEC,WAAW,EAAEzB,MAAM,EAAE;IACtD,IAAI,CAACsB,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACzB,MAAM,GAAGA,MAAM;EACxB;AACJ;AACA,OAAO,MAAM0B,YAAY,CAAC;EACtBrB,WAAWA,CAACsB,MAAM,EAAErB,UAAU,EAAE;IAC5B,IAAI,CAACqB,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACrB,UAAU,GAAGA,UAAU;EAChC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMsB,iBAAiB,CAAC;EACpBvB,WAAWA,CAACwB,IAAI,EAAEC,GAAG,EAAE;IACnB,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACC,KAAK,GAAGH,IAAI;IACjB,IAAI,CAACI,IAAI,GAAGH,GAAG;IACf,IAAI,CAACI,MAAM,GAAG,CAAC;IACf,IAAIL,IAAI,CAACM,IAAI,KAAKjD,QAAQ,EAAE;MACxB2C,IAAI,CAACO,OAAO,CAACP,IAAI,CAACM,IAAI,EAAEE,IAAI,IAAI;QAC5B,IAAIA,IAAI,KAAKnD,QAAQ,EAAE;UACnB,IAAI,CAAC6C,OAAO,CAACO,IAAI,CAACD,IAAI,CAACE,KAAK,CAAC;QACjC;QACA,OAAO,IAAI;MACf,CAAC,CAAC;IACN;EACJ;EACAC,IAAIA,CAAA,EAAG;IACH,IAAI,IAAI,CAACT,OAAO,CAAC/B,MAAM,KAAK,CAAC,EAAE;MAC3B,IAAI,IAAI,CAACkC,MAAM,KAAK,CAAC,EAAE;QACnB,IAAI,CAACA,MAAM,EAAE;QACb,OAAO,IAAI,CAACD,IAAI;MACpB,CAAC,MACI;QACD,OAAO,IAAI;MACf;IACJ;IACA,IAAI,IAAI,CAACC,MAAM,GAAG,IAAI,CAACH,OAAO,CAAC/B,MAAM,GAAG,CAAC,EAAE;MACvC,OAAO,IAAI;IACf;IACA,IAAI,IAAI,CAACkC,MAAM,KAAK,CAAC,EAAE;MACnB,OAAO,IAAI,CAACD,IAAI,GAAG,IAAI,CAACD,KAAK,CAACS,eAAe,CAAC,IAAI,CAACV,OAAO,CAAC,IAAI,CAACG,MAAM,EAAE,CAAC,CAAC;IAC9E;IACA,OAAO,IAAI,CAACF,KAAK,CAACS,eAAe,CAAC,IAAI,CAACV,OAAO,CAAC,IAAI,CAACG,MAAM,EAAE,CAAC,CAAC;EAClE;AACJ;AACA,MAAMQ,oBAAoB,CAAC;EACvBrC,WAAWA,CAACsC,KAAK,EAAE;IACf,IAAI,CAACC,MAAM,GAAGD,KAAK;IACnB,IAAI,CAACE,MAAM,GAAG,EAAE;EACpB;EACAC,GAAGA,CAACC,MAAM,EAAE;IACR,KAAK,IAAIhC,CAAC,GAAG,IAAI,CAAC8B,MAAM,CAAC7C,MAAM,GAAG,CAAC,EAAEe,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC9C,MAAMiC,OAAO,GAAG,IAAI,CAACH,MAAM,CAAC9B,CAAC,CAAC;MAC9B,IAAIiC,OAAO,CAACC,eAAe,IAAIF,MAAM,IAAIC,OAAO,CAACC,eAAe,GAAGD,OAAO,CAACX,IAAI,CAACE,KAAK,CAACvC,MAAM,IAAI+C,MAAM,EAAE;QACpG,OAAOC,OAAO;MAClB;IACJ;IACA,OAAO,IAAI;EACf;EACAE,IAAIA,CAACC,UAAU,EAAE;IACb,KAAK,IAAIpC,CAAC,GAAG,IAAI,CAAC8B,MAAM,CAAC7C,MAAM,GAAG,CAAC,EAAEe,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC9C,MAAMiC,OAAO,GAAG,IAAI,CAACH,MAAM,CAAC9B,CAAC,CAAC;MAC9B,IAAIiC,OAAO,CAACI,mBAAmB,IAAIJ,OAAO,CAACI,mBAAmB,GAAGD,UAAU,IAAIH,OAAO,CAACI,mBAAmB,GAAGJ,OAAO,CAACX,IAAI,CAACE,KAAK,CAACd,WAAW,IAAI0B,UAAU,EAAE;QACvJ,OAAOH,OAAO;MAClB;IACJ;IACA,OAAO,IAAI;EACf;EACA7C,GAAGA,CAACkD,YAAY,EAAE;IACd,IAAI,IAAI,CAACR,MAAM,CAAC7C,MAAM,IAAI,IAAI,CAAC4C,MAAM,EAAE;MACnC,IAAI,CAACC,MAAM,CAACS,KAAK,CAAC,CAAC;IACvB;IACA,IAAI,CAACT,MAAM,CAACP,IAAI,CAACe,YAAY,CAAC;EAClC;EACAE,QAAQA,CAACR,MAAM,EAAE;IACb,IAAIS,aAAa,GAAG,KAAK;IACzB,MAAMC,GAAG,GAAG,IAAI,CAACZ,MAAM;IACvB,KAAK,IAAI9B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0C,GAAG,CAACzD,MAAM,EAAEe,CAAC,EAAE,EAAE;MACjC,MAAMiC,OAAO,GAAGS,GAAG,CAAC1C,CAAC,CAAC;MACtB,IAAIiC,OAAO,CAACX,IAAI,CAACqB,MAAM,KAAK,IAAI,IAAIV,OAAO,CAACC,eAAe,IAAIF,MAAM,EAAE;QACnEU,GAAG,CAAC1C,CAAC,CAAC,GAAG,IAAI;QACbyC,aAAa,GAAG,IAAI;QACpB;MACJ;IACJ;IACA,IAAIA,aAAa,EAAE;MACf,MAAMG,MAAM,GAAG,EAAE;MACjB,KAAK,MAAMC,KAAK,IAAIH,GAAG,EAAE;QACrB,IAAIG,KAAK,KAAK,IAAI,EAAE;UAChBD,MAAM,CAACrB,IAAI,CAACsB,KAAK,CAAC;QACtB;MACJ;MACA,IAAI,CAACf,MAAM,GAAGc,MAAM;IACxB;EACJ;AACJ;AACA,OAAO,MAAME,aAAa,CAAC;EACvBxD,WAAWA,CAACyD,MAAM,EAAEC,GAAG,EAAEC,aAAa,EAAE;IACpC,IAAI,CAACC,MAAM,CAACH,MAAM,EAAEC,GAAG,EAAEC,aAAa,CAAC;EAC3C;EACAC,MAAMA,CAACH,MAAM,EAAEC,GAAG,EAAEC,aAAa,EAAE;IAC/B,IAAI,CAACE,QAAQ,GAAG,CACZ,IAAIxC,YAAY,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAC5B;IACD,IAAI,CAACyC,oBAAoB,GAAG;MAAEC,IAAI,EAAE,CAAC;MAAEC,MAAM,EAAE;IAAE,CAAC;IAClD,IAAI,CAAClC,IAAI,GAAGjD,QAAQ;IACpB,IAAI,CAACoF,QAAQ,GAAG,CAAC;IACjB,IAAI,CAACC,OAAO,GAAG,CAAC;IAChB,IAAI,CAACC,IAAI,GAAGT,GAAG;IACf,IAAI,CAACU,UAAU,GAAGV,GAAG,CAAC/D,MAAM;IAC5B,IAAI,CAAC0E,cAAc,GAAGV,aAAa;IACnC,IAAIW,QAAQ,GAAG,IAAI;IACnB,KAAK,IAAI5D,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAG8C,MAAM,CAAC9D,MAAM,EAAEe,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;MAC/C,IAAI+C,MAAM,CAAC/C,CAAC,CAAC,CAACY,MAAM,CAAC3B,MAAM,GAAG,CAAC,EAAE;QAC7B,IAAI,CAAC8D,MAAM,CAAC/C,CAAC,CAAC,CAACT,UAAU,EAAE;UACvBwD,MAAM,CAAC/C,CAAC,CAAC,CAACT,UAAU,GAAGK,oBAAoB,CAACmD,MAAM,CAAC/C,CAAC,CAAC,CAACY,MAAM,CAAC;QACjE;QACA,MAAMY,KAAK,GAAG,IAAIlB,KAAK,CAACN,CAAC,GAAG,CAAC,EAAE;UAAEqD,IAAI,EAAE,CAAC;UAAEC,MAAM,EAAE;QAAE,CAAC,EAAE;UAAED,IAAI,EAAEN,MAAM,CAAC/C,CAAC,CAAC,CAACT,UAAU,CAACN,MAAM,GAAG,CAAC;UAAEqE,MAAM,EAAEP,MAAM,CAAC/C,CAAC,CAAC,CAACY,MAAM,CAAC3B,MAAM,GAAG8D,MAAM,CAAC/C,CAAC,CAAC,CAACT,UAAU,CAACwD,MAAM,CAAC/C,CAAC,CAAC,CAACT,UAAU,CAACN,MAAM,GAAG,CAAC;QAAE,CAAC,EAAE8D,MAAM,CAAC/C,CAAC,CAAC,CAACT,UAAU,CAACN,MAAM,GAAG,CAAC,EAAE8D,MAAM,CAAC/C,CAAC,CAAC,CAACY,MAAM,CAAC3B,MAAM,CAAC;QACpP,IAAI,CAACkE,QAAQ,CAAC5B,IAAI,CAACwB,MAAM,CAAC/C,CAAC,CAAC,CAAC;QAC7B4D,QAAQ,GAAG,IAAI,CAACC,aAAa,CAACD,QAAQ,EAAEpC,KAAK,CAAC;MAClD;IACJ;IACA,IAAI,CAACsC,YAAY,GAAG,IAAInC,oBAAoB,CAAC,CAAC,CAAC;IAC/C,IAAI,CAACoC,gBAAgB,GAAG;MAAE3B,UAAU,EAAE,CAAC;MAAE4B,KAAK,EAAE;IAAG,CAAC;IACpD,IAAI,CAACC,qBAAqB,CAAC,CAAC;EAChC;EACAC,YAAYA,CAAClB,GAAG,EAAE;IACd,MAAMmB,iBAAiB,GAAGtF,iBAAiB;IAC3C,MAAMuF,GAAG,GAAGD,iBAAiB,GAAGE,IAAI,CAACC,KAAK,CAACH,iBAAiB,GAAG,CAAC,CAAC;IACjE,MAAMI,GAAG,GAAGH,GAAG,GAAG,CAAC;IACnB,IAAII,SAAS,GAAG,EAAE;IAClB,IAAIC,YAAY,GAAG,CAAC;IACpB,MAAM1B,MAAM,GAAG,EAAE;IACjB,IAAI,CAAC1B,OAAO,CAAC,IAAI,CAACD,IAAI,EAAEE,IAAI,IAAI;MAC5B,MAAMzB,GAAG,GAAG,IAAI,CAAC6E,cAAc,CAACpD,IAAI,CAAC;MACrC,MAAMrB,GAAG,GAAGJ,GAAG,CAACZ,MAAM;MACtB,IAAIwF,YAAY,IAAIL,GAAG,IAAIK,YAAY,GAAGxE,GAAG,GAAGsE,GAAG,EAAE;QACjDC,SAAS,IAAI3E,GAAG;QAChB4E,YAAY,IAAIxE,GAAG;QACnB,OAAO,IAAI;MACf;MACA;MACA,MAAM0E,IAAI,GAAGH,SAAS,CAACI,OAAO,CAAC,aAAa,EAAE5B,GAAG,CAAC;MAClDD,MAAM,CAACxB,IAAI,CAAC,IAAIZ,YAAY,CAACgE,IAAI,EAAE/E,oBAAoB,CAAC+E,IAAI,CAAC,CAAC,CAAC;MAC/DH,SAAS,GAAG3E,GAAG;MACf4E,YAAY,GAAGxE,GAAG;MAClB,OAAO,IAAI;IACf,CAAC,CAAC;IACF,IAAIwE,YAAY,GAAG,CAAC,EAAE;MAClB,MAAME,IAAI,GAAGH,SAAS,CAACI,OAAO,CAAC,aAAa,EAAE5B,GAAG,CAAC;MAClDD,MAAM,CAACxB,IAAI,CAAC,IAAIZ,YAAY,CAACgE,IAAI,EAAE/E,oBAAoB,CAAC+E,IAAI,CAAC,CAAC,CAAC;IACnE;IACA,IAAI,CAACzB,MAAM,CAACH,MAAM,EAAEC,GAAG,EAAE,IAAI,CAAC;EAClC;EACA;EACA6B,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI,CAACpB,IAAI;EACpB;EACAqB,MAAMA,CAACC,MAAM,EAAE;IACX,IAAI,CAACtB,IAAI,GAAGsB,MAAM;IAClB,IAAI,CAACrB,UAAU,GAAG,IAAI,CAACD,IAAI,CAACxE,MAAM;IAClC,IAAI,CAACiF,YAAY,CAACa,MAAM,CAAC;EAC7B;EACAC,cAAcA,CAACjE,GAAG,EAAE;IAChB,OAAO,IAAIF,iBAAiB,CAAC,IAAI,EAAEE,GAAG,CAAC;EAC3C;EACAkE,WAAWA,CAAC7C,UAAU,EAAEkB,MAAM,EAAE;IAC5B,IAAI4B,OAAO,GAAG,CAAC,CAAC,CAAC;IACjB,IAAIC,CAAC,GAAG,IAAI,CAAC/D,IAAI;IACjB,OAAO+D,CAAC,KAAKhH,QAAQ,EAAE;MACnB,IAAIgH,CAAC,CAACC,IAAI,KAAKjH,QAAQ,IAAIgH,CAAC,CAACE,OAAO,GAAG,CAAC,IAAIjD,UAAU,EAAE;QACpD+C,CAAC,GAAGA,CAAC,CAACC,IAAI;MACd,CAAC,MACI,IAAID,CAAC,CAACE,OAAO,GAAGF,CAAC,CAAC3D,KAAK,CAACd,WAAW,GAAG,CAAC,IAAI0B,UAAU,EAAE;QACxD8C,OAAO,IAAIC,CAAC,CAACG,SAAS;QACtB;QACA,MAAMC,4BAA4B,GAAG,IAAI,CAACC,mBAAmB,CAACL,CAAC,EAAE/C,UAAU,GAAG+C,CAAC,CAACE,OAAO,GAAG,CAAC,CAAC;QAC5F,OAAOH,OAAO,IAAIK,4BAA4B,GAAGjC,MAAM,GAAG,CAAC;MAC/D,CAAC,MACI;QACDlB,UAAU,IAAI+C,CAAC,CAACE,OAAO,GAAGF,CAAC,CAAC3D,KAAK,CAACd,WAAW;QAC7CwE,OAAO,IAAIC,CAAC,CAACG,SAAS,GAAGH,CAAC,CAAC3D,KAAK,CAACvC,MAAM;QACvCkG,CAAC,GAAGA,CAAC,CAACM,KAAK;MACf;IACJ;IACA,OAAOP,OAAO;EAClB;EACAQ,aAAaA,CAAC1D,MAAM,EAAE;IAClBA,MAAM,GAAGqC,IAAI,CAACC,KAAK,CAACtC,MAAM,CAAC;IAC3BA,MAAM,GAAGqC,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEvC,MAAM,CAAC;IAC5B,IAAImD,CAAC,GAAG,IAAI,CAAC/D,IAAI;IACjB,IAAIuE,KAAK,GAAG,CAAC;IACb,MAAMC,cAAc,GAAG5D,MAAM;IAC7B,OAAOmD,CAAC,KAAKhH,QAAQ,EAAE;MACnB,IAAIgH,CAAC,CAACG,SAAS,KAAK,CAAC,IAAIH,CAAC,CAACG,SAAS,IAAItD,MAAM,EAAE;QAC5CmD,CAAC,GAAGA,CAAC,CAACC,IAAI;MACd,CAAC,MACI,IAAID,CAAC,CAACG,SAAS,GAAGH,CAAC,CAAC3D,KAAK,CAACvC,MAAM,IAAI+C,MAAM,EAAE;QAC7C,MAAM6D,GAAG,GAAG,IAAI,CAACC,UAAU,CAACX,CAAC,EAAEnD,MAAM,GAAGmD,CAAC,CAACG,SAAS,CAAC;QACpDK,KAAK,IAAIR,CAAC,CAACE,OAAO,GAAGQ,GAAG,CAACE,KAAK;QAC9B,IAAIF,GAAG,CAACE,KAAK,KAAK,CAAC,EAAE;UACjB,MAAMC,eAAe,GAAG,IAAI,CAACf,WAAW,CAACU,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC;UACtD,MAAMrC,MAAM,GAAGsC,cAAc,GAAGI,eAAe;UAC/C,OAAO,IAAIhI,QAAQ,CAAC2H,KAAK,GAAG,CAAC,EAAErC,MAAM,GAAG,CAAC,CAAC;QAC9C;QACA,OAAO,IAAItF,QAAQ,CAAC2H,KAAK,GAAG,CAAC,EAAEE,GAAG,CAACI,SAAS,GAAG,CAAC,CAAC;MACrD,CAAC,MACI;QACDjE,MAAM,IAAImD,CAAC,CAACG,SAAS,GAAGH,CAAC,CAAC3D,KAAK,CAACvC,MAAM;QACtC0G,KAAK,IAAIR,CAAC,CAACE,OAAO,GAAGF,CAAC,CAAC3D,KAAK,CAACd,WAAW;QACxC,IAAIyE,CAAC,CAACM,KAAK,KAAKtH,QAAQ,EAAE;UACtB;UACA,MAAM6H,eAAe,GAAG,IAAI,CAACf,WAAW,CAACU,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC;UACtD,MAAMrC,MAAM,GAAGsC,cAAc,GAAG5D,MAAM,GAAGgE,eAAe;UACxD,OAAO,IAAIhI,QAAQ,CAAC2H,KAAK,GAAG,CAAC,EAAErC,MAAM,GAAG,CAAC,CAAC;QAC9C,CAAC,MACI;UACD6B,CAAC,GAAGA,CAAC,CAACM,KAAK;QACf;MACJ;IACJ;IACA,OAAO,IAAIzH,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;EAC7B;EACAkI,eAAeA,CAACC,KAAK,EAAEnD,GAAG,EAAE;IACxB,IAAImD,KAAK,CAACC,eAAe,KAAKD,KAAK,CAACE,aAAa,IAAIF,KAAK,CAACG,WAAW,KAAKH,KAAK,CAACI,SAAS,EAAE;MACxF,OAAO,EAAE;IACb;IACA,MAAMC,aAAa,GAAG,IAAI,CAACC,OAAO,CAACN,KAAK,CAACC,eAAe,EAAED,KAAK,CAACG,WAAW,CAAC;IAC5E,MAAMI,WAAW,GAAG,IAAI,CAACD,OAAO,CAACN,KAAK,CAACE,aAAa,EAAEF,KAAK,CAACI,SAAS,CAAC;IACtE,MAAMvC,KAAK,GAAG,IAAI,CAAC2C,gBAAgB,CAACH,aAAa,EAAEE,WAAW,CAAC;IAC/D,IAAI1D,GAAG,EAAE;MACL,IAAIA,GAAG,KAAK,IAAI,CAACS,IAAI,IAAI,CAAC,IAAI,CAACE,cAAc,EAAE;QAC3C,OAAOK,KAAK,CAACY,OAAO,CAAC,aAAa,EAAE5B,GAAG,CAAC;MAC5C;MACA,IAAIA,GAAG,KAAK,IAAI,CAAC6B,MAAM,CAAC,CAAC,IAAI,IAAI,CAAClB,cAAc,EAAE;QAC9C,IAAIX,GAAG,KAAK,MAAM,EAAE,CACpB;QACA,OAAOgB,KAAK;MAChB;MACA,OAAOA,KAAK,CAACY,OAAO,CAAC,aAAa,EAAE5B,GAAG,CAAC;IAC5C;IACA,OAAOgB,KAAK;EAChB;EACA2C,gBAAgBA,CAACH,aAAa,EAAEE,WAAW,EAAE;IACzC,IAAIF,aAAa,CAAClF,IAAI,KAAKoF,WAAW,CAACpF,IAAI,EAAE;MACzC,MAAMA,IAAI,GAAGkF,aAAa,CAAClF,IAAI;MAC/B,MAAMV,MAAM,GAAG,IAAI,CAACuC,QAAQ,CAAC7B,IAAI,CAACE,KAAK,CAACjB,WAAW,CAAC,CAACK,MAAM;MAC3D,MAAMgG,WAAW,GAAG,IAAI,CAACC,cAAc,CAACvF,IAAI,CAACE,KAAK,CAACjB,WAAW,EAAEe,IAAI,CAACE,KAAK,CAAChB,KAAK,CAAC;MACjF,OAAOI,MAAM,CAACkG,SAAS,CAACF,WAAW,GAAGJ,aAAa,CAACP,SAAS,EAAEW,WAAW,GAAGF,WAAW,CAACT,SAAS,CAAC;IACvG;IACA,IAAId,CAAC,GAAGqB,aAAa,CAAClF,IAAI;IAC1B,MAAMV,MAAM,GAAG,IAAI,CAACuC,QAAQ,CAACgC,CAAC,CAAC3D,KAAK,CAACjB,WAAW,CAAC,CAACK,MAAM;IACxD,MAAMgG,WAAW,GAAG,IAAI,CAACC,cAAc,CAAC1B,CAAC,CAAC3D,KAAK,CAACjB,WAAW,EAAE4E,CAAC,CAAC3D,KAAK,CAAChB,KAAK,CAAC;IAC3E,IAAIuG,GAAG,GAAGnG,MAAM,CAACkG,SAAS,CAACF,WAAW,GAAGJ,aAAa,CAACP,SAAS,EAAEW,WAAW,GAAGzB,CAAC,CAAC3D,KAAK,CAACvC,MAAM,CAAC;IAC/FkG,CAAC,GAAGA,CAAC,CAAC6B,IAAI,CAAC,CAAC;IACZ,OAAO7B,CAAC,KAAKhH,QAAQ,EAAE;MACnB,MAAMyC,MAAM,GAAG,IAAI,CAACuC,QAAQ,CAACgC,CAAC,CAAC3D,KAAK,CAACjB,WAAW,CAAC,CAACK,MAAM;MACxD,MAAMgG,WAAW,GAAG,IAAI,CAACC,cAAc,CAAC1B,CAAC,CAAC3D,KAAK,CAACjB,WAAW,EAAE4E,CAAC,CAAC3D,KAAK,CAAChB,KAAK,CAAC;MAC3E,IAAI2E,CAAC,KAAKuB,WAAW,CAACpF,IAAI,EAAE;QACxByF,GAAG,IAAInG,MAAM,CAACkG,SAAS,CAACF,WAAW,EAAEA,WAAW,GAAGF,WAAW,CAACT,SAAS,CAAC;QACzE;MACJ,CAAC,MACI;QACDc,GAAG,IAAInG,MAAM,CAACqG,MAAM,CAACL,WAAW,EAAEzB,CAAC,CAAC3D,KAAK,CAACvC,MAAM,CAAC;MACrD;MACAkG,CAAC,GAAGA,CAAC,CAAC6B,IAAI,CAAC,CAAC;IAChB;IACA,OAAOD,GAAG;EACd;EACAG,eAAeA,CAAA,EAAG;IACd,MAAMC,KAAK,GAAG,EAAE;IAChB,IAAIC,WAAW,GAAG,CAAC;IACnB,IAAIC,WAAW,GAAG,EAAE;IACpB,IAAIC,UAAU,GAAG,KAAK;IACtB,IAAI,CAACjG,OAAO,CAAC,IAAI,CAACD,IAAI,EAAEE,IAAI,IAAI;MAC5B,IAAIA,IAAI,KAAKnD,QAAQ,EAAE;QACnB,OAAO,IAAI;MACf;MACA,MAAMqD,KAAK,GAAGF,IAAI,CAACE,KAAK;MACxB,IAAI+F,WAAW,GAAG/F,KAAK,CAACvC,MAAM;MAC9B,IAAIsI,WAAW,KAAK,CAAC,EAAE;QACnB,OAAO,IAAI;MACf;MACA,MAAM3G,MAAM,GAAG,IAAI,CAACuC,QAAQ,CAAC3B,KAAK,CAACjB,WAAW,CAAC,CAACK,MAAM;MACtD,MAAMrB,UAAU,GAAG,IAAI,CAAC4D,QAAQ,CAAC3B,KAAK,CAACjB,WAAW,CAAC,CAAChB,UAAU;MAC9D,MAAMiI,cAAc,GAAGhG,KAAK,CAAChB,KAAK,CAAC6C,IAAI;MACvC,MAAMoE,YAAY,GAAGjG,KAAK,CAACf,GAAG,CAAC4C,IAAI;MACnC,IAAIqE,gBAAgB,GAAGnI,UAAU,CAACiI,cAAc,CAAC,GAAGhG,KAAK,CAAChB,KAAK,CAAC8C,MAAM;MACtE,IAAIgE,UAAU,EAAE;QACZ,IAAI1G,MAAM,CAACT,UAAU,CAACuH,gBAAgB,CAAC,KAAK,EAAE,CAAC,yBAAyB;UACpE;UACAA,gBAAgB,EAAE;UAClBH,WAAW,EAAE;QACjB;QACAJ,KAAK,CAACC,WAAW,EAAE,CAAC,GAAGC,WAAW;QAClCA,WAAW,GAAG,EAAE;QAChBC,UAAU,GAAG,KAAK;QAClB,IAAIC,WAAW,KAAK,CAAC,EAAE;UACnB,OAAO,IAAI;QACf;MACJ;MACA,IAAIC,cAAc,KAAKC,YAAY,EAAE;QACjC;QACA,IAAI,CAAC,IAAI,CAAC9D,cAAc,IAAI/C,MAAM,CAACT,UAAU,CAACuH,gBAAgB,GAAGH,WAAW,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,+BAA+B;UACpHD,UAAU,GAAG,IAAI;UACjBD,WAAW,IAAIzG,MAAM,CAACqG,MAAM,CAACS,gBAAgB,EAAEH,WAAW,GAAG,CAAC,CAAC;QACnE,CAAC,MACI;UACDF,WAAW,IAAIzG,MAAM,CAACqG,MAAM,CAACS,gBAAgB,EAAEH,WAAW,CAAC;QAC/D;QACA,OAAO,IAAI;MACf;MACA;MACAF,WAAW,IAAK,IAAI,CAAC1D,cAAc,GAC7B/C,MAAM,CAACkG,SAAS,CAACY,gBAAgB,EAAErD,IAAI,CAACE,GAAG,CAACmD,gBAAgB,EAAEnI,UAAU,CAACiI,cAAc,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC9D,UAAU,CAAC,CAAC,GAChH9C,MAAM,CAACkG,SAAS,CAACY,gBAAgB,EAAEnI,UAAU,CAACiI,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC5C,OAAO,CAAC,eAAe,EAAE,EAAE,CAAE;MACtGuC,KAAK,CAACC,WAAW,EAAE,CAAC,GAAGC,WAAW;MAClC,KAAK,IAAIhE,IAAI,GAAGmE,cAAc,GAAG,CAAC,EAAEnE,IAAI,GAAGoE,YAAY,EAAEpE,IAAI,EAAE,EAAE;QAC7DgE,WAAW,GAAI,IAAI,CAAC1D,cAAc,GAC5B/C,MAAM,CAACkG,SAAS,CAACvH,UAAU,CAAC8D,IAAI,CAAC,EAAE9D,UAAU,CAAC8D,IAAI,GAAG,CAAC,CAAC,GAAG,IAAI,CAACK,UAAU,CAAC,GAC1E9C,MAAM,CAACkG,SAAS,CAACvH,UAAU,CAAC8D,IAAI,CAAC,EAAE9D,UAAU,CAAC8D,IAAI,GAAG,CAAC,CAAC,CAAC,CAACuB,OAAO,CAAC,eAAe,EAAE,EAAE,CAAE;QAC5FuC,KAAK,CAACC,WAAW,EAAE,CAAC,GAAGC,WAAW;MACtC;MACA,IAAI,CAAC,IAAI,CAAC1D,cAAc,IAAI/C,MAAM,CAACT,UAAU,CAACZ,UAAU,CAACkI,YAAY,CAAC,GAAGjG,KAAK,CAACf,GAAG,CAAC6C,MAAM,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,+BAA+B;QACjIgE,UAAU,GAAG,IAAI;QACjB,IAAI9F,KAAK,CAACf,GAAG,CAAC6C,MAAM,KAAK,CAAC,EAAE;UACxB;UACA8D,WAAW,EAAE;QACjB,CAAC,MACI;UACDC,WAAW,GAAGzG,MAAM,CAACqG,MAAM,CAAC1H,UAAU,CAACkI,YAAY,CAAC,EAAEjG,KAAK,CAACf,GAAG,CAAC6C,MAAM,GAAG,CAAC,CAAC;QAC/E;MACJ,CAAC,MACI;QACD+D,WAAW,GAAGzG,MAAM,CAACqG,MAAM,CAAC1H,UAAU,CAACkI,YAAY,CAAC,EAAEjG,KAAK,CAACf,GAAG,CAAC6C,MAAM,CAAC;MAC3E;MACA,OAAO,IAAI;IACf,CAAC,CAAC;IACF,IAAIgE,UAAU,EAAE;MACZH,KAAK,CAACC,WAAW,EAAE,CAAC,GAAGC,WAAW;MAClCA,WAAW,GAAG,EAAE;IACpB;IACAF,KAAK,CAACC,WAAW,EAAE,CAAC,GAAGC,WAAW;IAClC,OAAOF,KAAK;EAChB;EACAQ,SAASA,CAAA,EAAG;IACR,OAAO,IAAI,CAACnE,OAAO;EACvB;EACAoE,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAACrE,QAAQ;EACxB;EACAsE,cAAcA,CAACzF,UAAU,EAAE;IACvB,IAAI,IAAI,CAAC2B,gBAAgB,CAAC3B,UAAU,KAAKA,UAAU,EAAE;MACjD,OAAO,IAAI,CAAC2B,gBAAgB,CAACC,KAAK;IACtC;IACA,IAAI,CAACD,gBAAgB,CAAC3B,UAAU,GAAGA,UAAU;IAC7C,IAAIA,UAAU,KAAK,IAAI,CAACmB,QAAQ,EAAE;MAC9B,IAAI,CAACQ,gBAAgB,CAACC,KAAK,GAAG,IAAI,CAAC8D,iBAAiB,CAAC1F,UAAU,CAAC;IACpE,CAAC,MACI,IAAI,IAAI,CAACuB,cAAc,EAAE;MAC1B,IAAI,CAACI,gBAAgB,CAACC,KAAK,GAAG,IAAI,CAAC8D,iBAAiB,CAAC1F,UAAU,EAAE,IAAI,CAACsB,UAAU,CAAC;IACrF,CAAC,MACI;MACD,IAAI,CAACK,gBAAgB,CAACC,KAAK,GAAG,IAAI,CAAC8D,iBAAiB,CAAC1F,UAAU,CAAC,CAACwC,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC;IACjG;IACA,OAAO,IAAI,CAACb,gBAAgB,CAACC,KAAK;EACtC;EACA+D,YAAYA,CAAC9F,OAAO,EAAE;IAClB,IAAIA,OAAO,CAACgE,SAAS,KAAKhE,OAAO,CAACX,IAAI,CAACE,KAAK,CAACvC,MAAM,EAAE;MACjD;MACA,MAAM+I,YAAY,GAAG/F,OAAO,CAACX,IAAI,CAAC0F,IAAI,CAAC,CAAC;MACxC,IAAI,CAACgB,YAAY,EAAE;QACf,OAAO,CAAC;MACZ;MACA,MAAMpH,MAAM,GAAG,IAAI,CAACuC,QAAQ,CAAC6E,YAAY,CAACxG,KAAK,CAACjB,WAAW,CAAC;MAC5D,MAAMqG,WAAW,GAAG,IAAI,CAACC,cAAc,CAACmB,YAAY,CAACxG,KAAK,CAACjB,WAAW,EAAEyH,YAAY,CAACxG,KAAK,CAAChB,KAAK,CAAC;MACjG,OAAOI,MAAM,CAACA,MAAM,CAACT,UAAU,CAACyG,WAAW,CAAC;IAChD,CAAC,MACI;MACD,MAAMhG,MAAM,GAAG,IAAI,CAACuC,QAAQ,CAAClB,OAAO,CAACX,IAAI,CAACE,KAAK,CAACjB,WAAW,CAAC;MAC5D,MAAMqG,WAAW,GAAG,IAAI,CAACC,cAAc,CAAC5E,OAAO,CAACX,IAAI,CAACE,KAAK,CAACjB,WAAW,EAAE0B,OAAO,CAACX,IAAI,CAACE,KAAK,CAAChB,KAAK,CAAC;MACjG,MAAMyH,YAAY,GAAGrB,WAAW,GAAG3E,OAAO,CAACgE,SAAS;MACpD,OAAOrF,MAAM,CAACA,MAAM,CAACT,UAAU,CAAC8H,YAAY,CAAC;IACjD;EACJ;EACAC,eAAeA,CAAC9F,UAAU,EAAE2D,KAAK,EAAE;IAC/B,MAAM9D,OAAO,GAAG,IAAI,CAACwE,OAAO,CAACrE,UAAU,EAAE2D,KAAK,GAAG,CAAC,CAAC;IACnD,OAAO,IAAI,CAACgC,YAAY,CAAC9F,OAAO,CAAC;EACrC;EACAkG,aAAaA,CAAC/F,UAAU,EAAE;IACtB,IAAIA,UAAU,KAAK,IAAI,CAACwF,YAAY,CAAC,CAAC,EAAE;MACpC,MAAMhB,WAAW,GAAG,IAAI,CAAC3B,WAAW,CAAC7C,UAAU,EAAE,CAAC,CAAC;MACnD,OAAO,IAAI,CAACuF,SAAS,CAAC,CAAC,GAAGf,WAAW;IACzC;IACA,OAAO,IAAI,CAAC3B,WAAW,CAAC7C,UAAU,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC6C,WAAW,CAAC7C,UAAU,EAAE,CAAC,CAAC,GAAG,IAAI,CAACsB,UAAU;EAClG;EACA0E,iBAAiBA,CAAC9G,IAAI,EAAE+G,QAAQ,EAAEjC,eAAe,EAAEE,WAAW,EAAEgC,WAAW,EAAEC,SAAS,EAAEC,UAAU,EAAEC,cAAc,EAAEC,gBAAgB,EAAEC,SAAS,EAAEtI,MAAM,EAAE;IACrJ,MAAMO,MAAM,GAAG,IAAI,CAACuC,QAAQ,CAAC7B,IAAI,CAACE,KAAK,CAACjB,WAAW,CAAC;IACpD,MAAMqI,mBAAmB,GAAG,IAAI,CAAC/B,cAAc,CAACvF,IAAI,CAACE,KAAK,CAACjB,WAAW,EAAEe,IAAI,CAACE,KAAK,CAAChB,KAAK,CAAC;IACzF,MAAMA,KAAK,GAAG,IAAI,CAACqG,cAAc,CAACvF,IAAI,CAACE,KAAK,CAACjB,WAAW,EAAE+H,WAAW,CAAC;IACtE,MAAM7H,GAAG,GAAG,IAAI,CAACoG,cAAc,CAACvF,IAAI,CAACE,KAAK,CAACjB,WAAW,EAAEgI,SAAS,CAAC;IAClE,IAAIM,CAAC;IACL;IACA,MAAM9B,GAAG,GAAG;MAAE1D,IAAI,EAAE,CAAC;MAAEC,MAAM,EAAE;IAAE,CAAC;IAClC,IAAIwF,UAAU;IACd,IAAIjC,cAAc;IAClB,IAAIwB,QAAQ,CAACU,eAAe,EAAE;MAC1BD,UAAU,GAAGlI,MAAM,CAACA,MAAM,CAACkG,SAAS,CAACtG,KAAK,EAAEC,GAAG,CAAC;MAChDoG,cAAc,GAAI7E,MAAM,IAAKA,MAAM,GAAGxB,KAAK;MAC3C6H,QAAQ,CAACW,KAAK,CAAC,CAAC,CAAC;IACrB,CAAC,MACI;MACDF,UAAU,GAAGlI,MAAM,CAACA,MAAM;MAC1BiG,cAAc,GAAI7E,MAAM,IAAKA,MAAM;MACnCqG,QAAQ,CAACW,KAAK,CAACxI,KAAK,CAAC;IACzB;IACA,GAAG;MACCqI,CAAC,GAAGR,QAAQ,CAACrB,IAAI,CAAC8B,UAAU,CAAC;MAC7B,IAAID,CAAC,EAAE;QACH,IAAIhC,cAAc,CAACgC,CAAC,CAAC9C,KAAK,CAAC,IAAItF,GAAG,EAAE;UAChC,OAAOkI,SAAS;QACpB;QACA,IAAI,CAACM,gBAAgB,CAAC3H,IAAI,EAAEuF,cAAc,CAACgC,CAAC,CAAC9C,KAAK,CAAC,GAAG6C,mBAAmB,EAAE7B,GAAG,CAAC;QAC/E,MAAMrG,WAAW,GAAG,IAAI,CAACwI,cAAc,CAAC5H,IAAI,CAACE,KAAK,CAACjB,WAAW,EAAE+H,WAAW,EAAEvB,GAAG,CAAC;QACjF,MAAMoC,cAAc,GAAGpC,GAAG,CAAC1D,IAAI,KAAKiF,WAAW,CAACjF,IAAI,GAAG0D,GAAG,CAACzD,MAAM,GAAGgF,WAAW,CAAChF,MAAM,GAAGgD,WAAW,GAAGS,GAAG,CAACzD,MAAM,GAAG,CAAC;QACrH,MAAM8F,YAAY,GAAGD,cAAc,GAAGN,CAAC,CAAC,CAAC,CAAC,CAAC5J,MAAM;QACjDoB,MAAM,CAACsI,SAAS,EAAE,CAAC,GAAGhK,eAAe,CAAC,IAAIV,KAAK,CAACmI,eAAe,GAAG1F,WAAW,EAAEyI,cAAc,EAAE/C,eAAe,GAAG1F,WAAW,EAAE0I,YAAY,CAAC,EAAEP,CAAC,EAAEJ,cAAc,CAAC;QAC/J,IAAI5B,cAAc,CAACgC,CAAC,CAAC9C,KAAK,CAAC,GAAG8C,CAAC,CAAC,CAAC,CAAC,CAAC5J,MAAM,IAAIwB,GAAG,EAAE;UAC9C,OAAOkI,SAAS;QACpB;QACA,IAAIA,SAAS,IAAID,gBAAgB,EAAE;UAC/B,OAAOC,SAAS;QACpB;MACJ;IACJ,CAAC,QAAQE,CAAC;IACV,OAAOF,SAAS;EACpB;EACAU,qBAAqBA,CAACC,WAAW,EAAEd,UAAU,EAAEC,cAAc,EAAEC,gBAAgB,EAAE;IAC7E,MAAMrI,MAAM,GAAG,EAAE;IACjB,IAAIsI,SAAS,GAAG,CAAC;IACjB,MAAMN,QAAQ,GAAG,IAAI3J,QAAQ,CAAC8J,UAAU,CAACe,cAAc,EAAEf,UAAU,CAACgB,KAAK,CAAC;IAC1E,IAAIhD,aAAa,GAAG,IAAI,CAACC,OAAO,CAAC6C,WAAW,CAAClD,eAAe,EAAEkD,WAAW,CAAChD,WAAW,CAAC;IACtF,IAAIE,aAAa,KAAK,IAAI,EAAE;MACxB,OAAO,EAAE;IACb;IACA,MAAME,WAAW,GAAG,IAAI,CAACD,OAAO,CAAC6C,WAAW,CAACjD,aAAa,EAAEiD,WAAW,CAAC/C,SAAS,CAAC;IAClF,IAAIG,WAAW,KAAK,IAAI,EAAE;MACtB,OAAO,EAAE;IACb;IACA,IAAIlG,KAAK,GAAG,IAAI,CAACyI,gBAAgB,CAACzC,aAAa,CAAClF,IAAI,EAAEkF,aAAa,CAACP,SAAS,CAAC;IAC9E,MAAMxF,GAAG,GAAG,IAAI,CAACwI,gBAAgB,CAACvC,WAAW,CAACpF,IAAI,EAAEoF,WAAW,CAACT,SAAS,CAAC;IAC1E,IAAIO,aAAa,CAAClF,IAAI,KAAKoF,WAAW,CAACpF,IAAI,EAAE;MACzC,IAAI,CAAC8G,iBAAiB,CAAC5B,aAAa,CAAClF,IAAI,EAAE+G,QAAQ,EAAEiB,WAAW,CAAClD,eAAe,EAAEkD,WAAW,CAAChD,WAAW,EAAE9F,KAAK,EAAEC,GAAG,EAAE+H,UAAU,EAAEC,cAAc,EAAEC,gBAAgB,EAAEC,SAAS,EAAEtI,MAAM,CAAC;MACvL,OAAOA,MAAM;IACjB;IACA,IAAI+F,eAAe,GAAGkD,WAAW,CAAClD,eAAe;IACjD,IAAIqD,WAAW,GAAGjD,aAAa,CAAClF,IAAI;IACpC,OAAOmI,WAAW,KAAK/C,WAAW,CAACpF,IAAI,EAAE;MACrC,MAAMoI,YAAY,GAAG,IAAI,CAACR,cAAc,CAACO,WAAW,CAACjI,KAAK,CAACjB,WAAW,EAAEC,KAAK,EAAEiJ,WAAW,CAACjI,KAAK,CAACf,GAAG,CAAC;MACrG,IAAIiJ,YAAY,IAAI,CAAC,EAAE;QACnB;QACA,MAAMnK,UAAU,GAAG,IAAI,CAAC4D,QAAQ,CAACsG,WAAW,CAACjI,KAAK,CAACjB,WAAW,CAAC,CAAChB,UAAU;QAC1E,MAAMqJ,mBAAmB,GAAG,IAAI,CAAC/B,cAAc,CAAC4C,WAAW,CAACjI,KAAK,CAACjB,WAAW,EAAEkJ,WAAW,CAACjI,KAAK,CAAChB,KAAK,CAAC;QACvG,MAAMmJ,mBAAmB,GAAGpK,UAAU,CAACiB,KAAK,CAAC6C,IAAI,GAAGqG,YAAY,CAAC;QACjE,MAAMpD,WAAW,GAAGF,eAAe,KAAKkD,WAAW,CAAClD,eAAe,GAAGkD,WAAW,CAAChD,WAAW,GAAG,CAAC;QACjGqC,SAAS,GAAG,IAAI,CAACP,iBAAiB,CAACqB,WAAW,EAAEpB,QAAQ,EAAEjC,eAAe,EAAEE,WAAW,EAAE9F,KAAK,EAAE,IAAI,CAACyI,gBAAgB,CAACQ,WAAW,EAAEE,mBAAmB,GAAGf,mBAAmB,CAAC,EAAEJ,UAAU,EAAEC,cAAc,EAAEC,gBAAgB,EAAEC,SAAS,EAAEtI,MAAM,CAAC;QAC9O,IAAIsI,SAAS,IAAID,gBAAgB,EAAE;UAC/B,OAAOrI,MAAM;QACjB;QACA+F,eAAe,IAAIsD,YAAY;MACnC;MACA,MAAMpD,WAAW,GAAGF,eAAe,KAAKkD,WAAW,CAAClD,eAAe,GAAGkD,WAAW,CAAChD,WAAW,GAAG,CAAC,GAAG,CAAC;MACrG;MACA,IAAIF,eAAe,KAAKkD,WAAW,CAACjD,aAAa,EAAE;QAC/C,MAAM1B,IAAI,GAAG,IAAI,CAACkD,cAAc,CAACzB,eAAe,CAAC,CAACU,SAAS,CAACR,WAAW,EAAEgD,WAAW,CAAC/C,SAAS,GAAG,CAAC,CAAC;QACnGoC,SAAS,GAAG,IAAI,CAACiB,kBAAkB,CAACpB,UAAU,EAAEH,QAAQ,EAAE1D,IAAI,EAAE2E,WAAW,CAACjD,aAAa,EAAEC,WAAW,EAAEqC,SAAS,EAAEtI,MAAM,EAAEoI,cAAc,EAAEC,gBAAgB,CAAC;QAC5J,OAAOrI,MAAM;MACjB;MACAsI,SAAS,GAAG,IAAI,CAACiB,kBAAkB,CAACpB,UAAU,EAAEH,QAAQ,EAAE,IAAI,CAACR,cAAc,CAACzB,eAAe,CAAC,CAACa,MAAM,CAACX,WAAW,CAAC,EAAEF,eAAe,EAAEE,WAAW,EAAEqC,SAAS,EAAEtI,MAAM,EAAEoI,cAAc,EAAEC,gBAAgB,CAAC;MACtM,IAAIC,SAAS,IAAID,gBAAgB,EAAE;QAC/B,OAAOrI,MAAM;MACjB;MACA+F,eAAe,EAAE;MACjBI,aAAa,GAAG,IAAI,CAACC,OAAO,CAACL,eAAe,EAAE,CAAC,CAAC;MAChDqD,WAAW,GAAGjD,aAAa,CAAClF,IAAI;MAChCd,KAAK,GAAG,IAAI,CAACyI,gBAAgB,CAACzC,aAAa,CAAClF,IAAI,EAAEkF,aAAa,CAACP,SAAS,CAAC;IAC9E;IACA,IAAIG,eAAe,KAAKkD,WAAW,CAACjD,aAAa,EAAE;MAC/C,MAAMC,WAAW,GAAGF,eAAe,KAAKkD,WAAW,CAAClD,eAAe,GAAGkD,WAAW,CAAChD,WAAW,GAAG,CAAC,GAAG,CAAC;MACrG,MAAM3B,IAAI,GAAG,IAAI,CAACkD,cAAc,CAACzB,eAAe,CAAC,CAACU,SAAS,CAACR,WAAW,EAAEgD,WAAW,CAAC/C,SAAS,GAAG,CAAC,CAAC;MACnGoC,SAAS,GAAG,IAAI,CAACiB,kBAAkB,CAACpB,UAAU,EAAEH,QAAQ,EAAE1D,IAAI,EAAE2E,WAAW,CAACjD,aAAa,EAAEC,WAAW,EAAEqC,SAAS,EAAEtI,MAAM,EAAEoI,cAAc,EAAEC,gBAAgB,CAAC;MAC5J,OAAOrI,MAAM;IACjB;IACA,MAAMiG,WAAW,GAAGF,eAAe,KAAKkD,WAAW,CAAClD,eAAe,GAAGkD,WAAW,CAAChD,WAAW,GAAG,CAAC;IACjGqC,SAAS,GAAG,IAAI,CAACP,iBAAiB,CAAC1B,WAAW,CAACpF,IAAI,EAAE+G,QAAQ,EAAEjC,eAAe,EAAEE,WAAW,EAAE9F,KAAK,EAAEC,GAAG,EAAE+H,UAAU,EAAEC,cAAc,EAAEC,gBAAgB,EAAEC,SAAS,EAAEtI,MAAM,CAAC;IACzK,OAAOA,MAAM;EACjB;EACAuJ,kBAAkBA,CAACpB,UAAU,EAAEH,QAAQ,EAAE1D,IAAI,EAAEvC,UAAU,EAAEyH,WAAW,EAAElB,SAAS,EAAEtI,MAAM,EAAEoI,cAAc,EAAEC,gBAAgB,EAAE;IACzH,MAAMa,cAAc,GAAGf,UAAU,CAACe,cAAc;IAChD,IAAI,CAACd,cAAc,IAAID,UAAU,CAACsB,YAAY,EAAE;MAC5C,MAAMC,YAAY,GAAGvB,UAAU,CAACsB,YAAY;MAC5C,MAAME,eAAe,GAAGD,YAAY,CAAC9K,MAAM;MAC3C,MAAMgL,UAAU,GAAGtF,IAAI,CAAC1F,MAAM;MAC9B,IAAIiL,cAAc,GAAG,CAACF,eAAe;MACrC,OAAO,CAACE,cAAc,GAAGvF,IAAI,CAACwF,OAAO,CAACJ,YAAY,EAAEG,cAAc,GAAGF,eAAe,CAAC,MAAM,CAAC,CAAC,EAAE;QAC3F,IAAI,CAACT,cAAc,IAAI3K,YAAY,CAAC2K,cAAc,EAAE5E,IAAI,EAAEsF,UAAU,EAAEC,cAAc,EAAEF,eAAe,CAAC,EAAE;UACpG3J,MAAM,CAACsI,SAAS,EAAE,CAAC,GAAG,IAAIzK,SAAS,CAAC,IAAID,KAAK,CAACmE,UAAU,EAAE8H,cAAc,GAAG,CAAC,GAAGL,WAAW,EAAEzH,UAAU,EAAE8H,cAAc,GAAG,CAAC,GAAGF,eAAe,GAAGH,WAAW,CAAC,EAAE,IAAI,CAAC;UAClK,IAAIlB,SAAS,IAAID,gBAAgB,EAAE;YAC/B,OAAOC,SAAS;UACpB;QACJ;MACJ;MACA,OAAOA,SAAS;IACpB;IACA,IAAIE,CAAC;IACL;IACAR,QAAQ,CAACW,KAAK,CAAC,CAAC,CAAC;IACjB,GAAG;MACCH,CAAC,GAAGR,QAAQ,CAACrB,IAAI,CAACrC,IAAI,CAAC;MACvB,IAAIkE,CAAC,EAAE;QACHxI,MAAM,CAACsI,SAAS,EAAE,CAAC,GAAGhK,eAAe,CAAC,IAAIV,KAAK,CAACmE,UAAU,EAAEyG,CAAC,CAAC9C,KAAK,GAAG,CAAC,GAAG8D,WAAW,EAAEzH,UAAU,EAAEyG,CAAC,CAAC9C,KAAK,GAAG,CAAC,GAAG8C,CAAC,CAAC,CAAC,CAAC,CAAC5J,MAAM,GAAG4K,WAAW,CAAC,EAAEhB,CAAC,EAAEJ,cAAc,CAAC;QAC/J,IAAIE,SAAS,IAAID,gBAAgB,EAAE;UAC/B,OAAOC,SAAS;QACpB;MACJ;IACJ,CAAC,QAAQE,CAAC;IACV,OAAOF,SAAS;EACpB;EACA;EACA;EACAyB,MAAMA,CAACpI,MAAM,EAAEgC,KAAK,EAAEf,aAAa,GAAG,KAAK,EAAE;IACzC,IAAI,CAACU,cAAc,GAAG,IAAI,CAACA,cAAc,IAAIV,aAAa;IAC1D,IAAI,CAACc,gBAAgB,CAAC3B,UAAU,GAAG,CAAC;IACpC,IAAI,CAAC2B,gBAAgB,CAACC,KAAK,GAAG,EAAE;IAChC,IAAI,IAAI,CAAC5C,IAAI,KAAKjD,QAAQ,EAAE;MACxB,MAAM;QAAEmD,IAAI;QAAE2E,SAAS;QAAE/D;MAAgB,CAAC,GAAG,IAAI,CAACmI,MAAM,CAACrI,MAAM,CAAC;MAChE,MAAMR,KAAK,GAAGF,IAAI,CAACE,KAAK;MACxB,MAAMjB,WAAW,GAAGiB,KAAK,CAACjB,WAAW;MACrC,MAAM+J,iBAAiB,GAAG,IAAI,CAACrB,gBAAgB,CAAC3H,IAAI,EAAE2E,SAAS,CAAC;MAChE,IAAI3E,IAAI,CAACE,KAAK,CAACjB,WAAW,KAAK,CAAC,IAC5BiB,KAAK,CAACf,GAAG,CAAC4C,IAAI,KAAK,IAAI,CAACD,oBAAoB,CAACC,IAAI,IACjD7B,KAAK,CAACf,GAAG,CAAC6C,MAAM,KAAK,IAAI,CAACF,oBAAoB,CAACE,MAAM,IACpDpB,eAAe,GAAGV,KAAK,CAACvC,MAAM,KAAK+C,MAAO,IAC3CgC,KAAK,CAAC/E,MAAM,GAAGJ,iBAAiB,EAAE;QAClC;QACA,IAAI,CAAC0L,YAAY,CAACjJ,IAAI,EAAE0C,KAAK,CAAC;QAC9B,IAAI,CAACC,qBAAqB,CAAC,CAAC;QAC5B;MACJ;MACA,IAAI/B,eAAe,KAAKF,MAAM,EAAE;QAC5B,IAAI,CAACwI,uBAAuB,CAACxG,KAAK,EAAE1C,IAAI,CAAC;QACzC,IAAI,CAACwC,YAAY,CAACtB,QAAQ,CAACR,MAAM,CAAC;MACtC,CAAC,MACI,IAAIE,eAAe,GAAGZ,IAAI,CAACE,KAAK,CAACvC,MAAM,GAAG+C,MAAM,EAAE;QACnD;QACA,MAAMyI,UAAU,GAAG,EAAE;QACrB,IAAIC,aAAa,GAAG,IAAIpK,KAAK,CAACkB,KAAK,CAACjB,WAAW,EAAE+J,iBAAiB,EAAE9I,KAAK,CAACf,GAAG,EAAE,IAAI,CAACyI,cAAc,CAAC1H,KAAK,CAACjB,WAAW,EAAE+J,iBAAiB,EAAE9I,KAAK,CAACf,GAAG,CAAC,EAAE,IAAI,CAACoG,cAAc,CAACtG,WAAW,EAAEiB,KAAK,CAACf,GAAG,CAAC,GAAG,IAAI,CAACoG,cAAc,CAACtG,WAAW,EAAE+J,iBAAiB,CAAC,CAAC;QACvP,IAAI,IAAI,CAACK,eAAe,CAAC,CAAC,IAAI,IAAI,CAACC,SAAS,CAAC5G,KAAK,CAAC,EAAE;UACjD,MAAM6G,WAAW,GAAG,IAAI,CAACC,cAAc,CAACxJ,IAAI,EAAE2E,SAAS,CAAC;UACxD,IAAI4E,WAAW,KAAK,EAAE,CAAC,WAAW;YAC9B,MAAME,QAAQ,GAAG;cAAE1H,IAAI,EAAEqH,aAAa,CAAClK,KAAK,CAAC6C,IAAI,GAAG,CAAC;cAAEC,MAAM,EAAE;YAAE,CAAC;YAClEoH,aAAa,GAAG,IAAIpK,KAAK,CAACoK,aAAa,CAACnK,WAAW,EAAEwK,QAAQ,EAAEL,aAAa,CAACjK,GAAG,EAAE,IAAI,CAACyI,cAAc,CAACwB,aAAa,CAACnK,WAAW,EAAEwK,QAAQ,EAAEL,aAAa,CAACjK,GAAG,CAAC,EAAEiK,aAAa,CAACzL,MAAM,GAAG,CAAC,CAAC;YACxL+E,KAAK,IAAI,IAAI;UACjB;QACJ;QACA;QACA,IAAI,IAAI,CAAC2G,eAAe,CAAC,CAAC,IAAI,IAAI,CAACK,WAAW,CAAChH,KAAK,CAAC,EAAE;UACnD,MAAMiH,UAAU,GAAG,IAAI,CAACH,cAAc,CAACxJ,IAAI,EAAE2E,SAAS,GAAG,CAAC,CAAC;UAC3D,IAAIgF,UAAU,KAAK,EAAE,CAAC,WAAW;YAC7B,MAAMC,WAAW,GAAG,IAAI,CAACjC,gBAAgB,CAAC3H,IAAI,EAAE2E,SAAS,GAAG,CAAC,CAAC;YAC9D,IAAI,CAACkF,cAAc,CAAC7J,IAAI,EAAE4J,WAAW,CAAC;YACtClH,KAAK,GAAG,IAAI,GAAGA,KAAK;YACpB,IAAI1C,IAAI,CAACE,KAAK,CAACvC,MAAM,KAAK,CAAC,EAAE;cACzBwL,UAAU,CAAClJ,IAAI,CAACD,IAAI,CAAC;YACzB;UACJ,CAAC,MACI;YACD,IAAI,CAAC6J,cAAc,CAAC7J,IAAI,EAAEgJ,iBAAiB,CAAC;UAChD;QACJ,CAAC,MACI;UACD,IAAI,CAACa,cAAc,CAAC7J,IAAI,EAAEgJ,iBAAiB,CAAC;QAChD;QACA,MAAMc,SAAS,GAAG,IAAI,CAACC,eAAe,CAACrH,KAAK,CAAC;QAC7C,IAAI0G,aAAa,CAACzL,MAAM,GAAG,CAAC,EAAE;UAC1B,IAAI,CAAC4E,aAAa,CAACvC,IAAI,EAAEoJ,aAAa,CAAC;QAC3C;QACA,IAAIY,OAAO,GAAGhK,IAAI;QAClB,KAAK,IAAIiK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,SAAS,CAACnM,MAAM,EAAEsM,CAAC,EAAE,EAAE;UACvCD,OAAO,GAAG,IAAI,CAACzH,aAAa,CAACyH,OAAO,EAAEF,SAAS,CAACG,CAAC,CAAC,CAAC;QACvD;QACA,IAAI,CAACC,WAAW,CAACf,UAAU,CAAC;MAChC,CAAC,MACI;QACD,IAAI,CAACgB,wBAAwB,CAACzH,KAAK,EAAE1C,IAAI,CAAC;MAC9C;IACJ,CAAC,MACI;MACD;MACA,MAAMoK,MAAM,GAAG,IAAI,CAACL,eAAe,CAACrH,KAAK,CAAC;MAC1C,IAAI1C,IAAI,GAAG,IAAI,CAACqK,YAAY,CAAC,IAAI,EAAED,MAAM,CAAC,CAAC,CAAC,CAAC;MAC7C,KAAK,IAAIH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGG,MAAM,CAACzM,MAAM,EAAEsM,CAAC,EAAE,EAAE;QACpCjK,IAAI,GAAG,IAAI,CAACuC,aAAa,CAACvC,IAAI,EAAEoK,MAAM,CAACH,CAAC,CAAC,CAAC;MAC9C;IACJ;IACA;IACA,IAAI,CAACtH,qBAAqB,CAAC,CAAC;EAChC;EACA2H,MAAMA,CAAC5J,MAAM,EAAE6J,GAAG,EAAE;IAChB,IAAI,CAAC9H,gBAAgB,CAAC3B,UAAU,GAAG,CAAC;IACpC,IAAI,CAAC2B,gBAAgB,CAACC,KAAK,GAAG,EAAE;IAChC,IAAI6H,GAAG,IAAI,CAAC,IAAI,IAAI,CAACzK,IAAI,KAAKjD,QAAQ,EAAE;MACpC;IACJ;IACA,MAAMqI,aAAa,GAAG,IAAI,CAAC6D,MAAM,CAACrI,MAAM,CAAC;IACzC,MAAM0E,WAAW,GAAG,IAAI,CAAC2D,MAAM,CAACrI,MAAM,GAAG6J,GAAG,CAAC;IAC7C,MAAMC,SAAS,GAAGtF,aAAa,CAAClF,IAAI;IACpC,MAAMyK,OAAO,GAAGrF,WAAW,CAACpF,IAAI;IAChC,IAAIwK,SAAS,KAAKC,OAAO,EAAE;MACvB,MAAMC,qBAAqB,GAAG,IAAI,CAAC/C,gBAAgB,CAAC6C,SAAS,EAAEtF,aAAa,CAACP,SAAS,CAAC;MACvF,MAAMgG,mBAAmB,GAAG,IAAI,CAAChD,gBAAgB,CAAC6C,SAAS,EAAEpF,WAAW,CAACT,SAAS,CAAC;MACnF,IAAIO,aAAa,CAACtE,eAAe,KAAKF,MAAM,EAAE;QAC1C,IAAI6J,GAAG,KAAKC,SAAS,CAACtK,KAAK,CAACvC,MAAM,EAAE;UAAE;UAClC,MAAM+H,IAAI,GAAG8E,SAAS,CAAC9E,IAAI,CAAC,CAAC;UAC7BzI,QAAQ,CAAC,IAAI,EAAEuN,SAAS,CAAC;UACzB,IAAI,CAACI,wBAAwB,CAAClF,IAAI,CAAC;UACnC,IAAI,CAAC/C,qBAAqB,CAAC,CAAC;UAC5B;QACJ;QACA,IAAI,CAACkI,cAAc,CAACL,SAAS,EAAEG,mBAAmB,CAAC;QACnD,IAAI,CAACnI,YAAY,CAACtB,QAAQ,CAACR,MAAM,CAAC;QAClC,IAAI,CAACkK,wBAAwB,CAACJ,SAAS,CAAC;QACxC,IAAI,CAAC7H,qBAAqB,CAAC,CAAC;QAC5B;MACJ;MACA,IAAIuC,aAAa,CAACtE,eAAe,GAAG4J,SAAS,CAACtK,KAAK,CAACvC,MAAM,KAAK+C,MAAM,GAAG6J,GAAG,EAAE;QACzE,IAAI,CAACV,cAAc,CAACW,SAAS,EAAEE,qBAAqB,CAAC;QACrD,IAAI,CAACI,wBAAwB,CAACN,SAAS,CAAC;QACxC,IAAI,CAAC7H,qBAAqB,CAAC,CAAC;QAC5B;MACJ;MACA;MACA,IAAI,CAACoI,UAAU,CAACP,SAAS,EAAEE,qBAAqB,EAAEC,mBAAmB,CAAC;MACtE,IAAI,CAAChI,qBAAqB,CAAC,CAAC;MAC5B;IACJ;IACA,MAAMwG,UAAU,GAAG,EAAE;IACrB,MAAMuB,qBAAqB,GAAG,IAAI,CAAC/C,gBAAgB,CAAC6C,SAAS,EAAEtF,aAAa,CAACP,SAAS,CAAC;IACvF,IAAI,CAACkF,cAAc,CAACW,SAAS,EAAEE,qBAAqB,CAAC;IACrD,IAAI,CAAClI,YAAY,CAACtB,QAAQ,CAACR,MAAM,CAAC;IAClC,IAAI8J,SAAS,CAACtK,KAAK,CAACvC,MAAM,KAAK,CAAC,EAAE;MAC9BwL,UAAU,CAAClJ,IAAI,CAACuK,SAAS,CAAC;IAC9B;IACA;IACA,MAAMG,mBAAmB,GAAG,IAAI,CAAChD,gBAAgB,CAAC8C,OAAO,EAAErF,WAAW,CAACT,SAAS,CAAC;IACjF,IAAI,CAACkG,cAAc,CAACJ,OAAO,EAAEE,mBAAmB,CAAC;IACjD,IAAIF,OAAO,CAACvK,KAAK,CAACvC,MAAM,KAAK,CAAC,EAAE;MAC5BwL,UAAU,CAAClJ,IAAI,CAACwK,OAAO,CAAC;IAC5B;IACA;IACA,MAAMO,UAAU,GAAGR,SAAS,CAAC9E,IAAI,CAAC,CAAC;IACnC,KAAK,IAAI1F,IAAI,GAAGgL,UAAU,EAAEhL,IAAI,KAAKnD,QAAQ,IAAImD,IAAI,KAAKyK,OAAO,EAAEzK,IAAI,GAAGA,IAAI,CAAC0F,IAAI,CAAC,CAAC,EAAE;MACnFyD,UAAU,CAAClJ,IAAI,CAACD,IAAI,CAAC;IACzB;IACA,MAAMiL,IAAI,GAAGT,SAAS,CAACtK,KAAK,CAACvC,MAAM,KAAK,CAAC,GAAG6M,SAAS,CAACS,IAAI,CAAC,CAAC,GAAGT,SAAS;IACxE,IAAI,CAACN,WAAW,CAACf,UAAU,CAAC;IAC5B,IAAI,CAAC2B,wBAAwB,CAACG,IAAI,CAAC;IACnC,IAAI,CAACtI,qBAAqB,CAAC,CAAC;EAChC;EACAuG,uBAAuBA,CAACxG,KAAK,EAAE1C,IAAI,EAAE;IACjC;IACA,MAAMmJ,UAAU,GAAG,EAAE;IACrB,IAAI,IAAI,CAACE,eAAe,CAAC,CAAC,IAAI,IAAI,CAACC,SAAS,CAAC5G,KAAK,CAAC,IAAI,IAAI,CAACgH,WAAW,CAAC1J,IAAI,CAAC,EAAE;MAC3E;MACA,MAAME,KAAK,GAAGF,IAAI,CAACE,KAAK;MACxB,MAAMuJ,QAAQ,GAAG;QAAE1H,IAAI,EAAE7B,KAAK,CAAChB,KAAK,CAAC6C,IAAI,GAAG,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MAC1D,MAAMkJ,MAAM,GAAG,IAAIlM,KAAK,CAACkB,KAAK,CAACjB,WAAW,EAAEwK,QAAQ,EAAEvJ,KAAK,CAACf,GAAG,EAAE,IAAI,CAACyI,cAAc,CAAC1H,KAAK,CAACjB,WAAW,EAAEwK,QAAQ,EAAEvJ,KAAK,CAACf,GAAG,CAAC,EAAEe,KAAK,CAACvC,MAAM,GAAG,CAAC,CAAC;MAC/IqC,IAAI,CAACE,KAAK,GAAGgL,MAAM;MACnBxI,KAAK,IAAI,IAAI;MACbvF,kBAAkB,CAAC,IAAI,EAAE6C,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACtC,IAAIA,IAAI,CAACE,KAAK,CAACvC,MAAM,KAAK,CAAC,EAAE;QACzBwL,UAAU,CAAClJ,IAAI,CAACD,IAAI,CAAC;MACzB;IACJ;IACA,MAAM8J,SAAS,GAAG,IAAI,CAACC,eAAe,CAACrH,KAAK,CAAC;IAC7C,IAAIyI,OAAO,GAAG,IAAI,CAACd,YAAY,CAACrK,IAAI,EAAE8J,SAAS,CAACA,SAAS,CAACnM,MAAM,GAAG,CAAC,CAAC,CAAC;IACtE,KAAK,IAAIsM,CAAC,GAAGH,SAAS,CAACnM,MAAM,GAAG,CAAC,EAAEsM,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC5CkB,OAAO,GAAG,IAAI,CAACd,YAAY,CAACc,OAAO,EAAErB,SAAS,CAACG,CAAC,CAAC,CAAC;IACtD;IACA,IAAI,CAACW,wBAAwB,CAACO,OAAO,CAAC;IACtC,IAAI,CAACjB,WAAW,CAACf,UAAU,CAAC;EAChC;EACAgB,wBAAwBA,CAACzH,KAAK,EAAE1C,IAAI,EAAE;IAClC;IACA,IAAI,IAAI,CAACoL,4BAA4B,CAAC1I,KAAK,EAAE1C,IAAI,CAAC,EAAE;MAChD;MACA0C,KAAK,IAAI,IAAI;IACjB;IACA,MAAMoH,SAAS,GAAG,IAAI,CAACC,eAAe,CAACrH,KAAK,CAAC;IAC7C,MAAMyI,OAAO,GAAG,IAAI,CAAC5I,aAAa,CAACvC,IAAI,EAAE8J,SAAS,CAAC,CAAC,CAAC,CAAC;IACtD,IAAIE,OAAO,GAAGmB,OAAO;IACrB,KAAK,IAAIlB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,SAAS,CAACnM,MAAM,EAAEsM,CAAC,EAAE,EAAE;MACvCD,OAAO,GAAG,IAAI,CAACzH,aAAa,CAACyH,OAAO,EAAEF,SAAS,CAACG,CAAC,CAAC,CAAC;IACvD;IACA,IAAI,CAACW,wBAAwB,CAACO,OAAO,CAAC;EAC1C;EACAxD,gBAAgBA,CAAC3H,IAAI,EAAE2E,SAAS,EAAEc,GAAG,EAAE;IACnC,MAAMvF,KAAK,GAAGF,IAAI,CAACE,KAAK;IACxB,MAAMjB,WAAW,GAAGe,IAAI,CAACE,KAAK,CAACjB,WAAW;IAC1C,MAAMhB,UAAU,GAAG,IAAI,CAAC4D,QAAQ,CAAC5C,WAAW,CAAC,CAAChB,UAAU;IACxD,MAAMqH,WAAW,GAAGrH,UAAU,CAACiC,KAAK,CAAChB,KAAK,CAAC6C,IAAI,CAAC,GAAG7B,KAAK,CAAChB,KAAK,CAAC8C,MAAM;IACrE,MAAMtB,MAAM,GAAG4E,WAAW,GAAGX,SAAS;IACtC;IACA,IAAI0G,GAAG,GAAGnL,KAAK,CAAChB,KAAK,CAAC6C,IAAI;IAC1B,IAAIuJ,IAAI,GAAGpL,KAAK,CAACf,GAAG,CAAC4C,IAAI;IACzB,IAAIwJ,GAAG,GAAG,CAAC;IACX,IAAIC,OAAO,GAAG,CAAC;IACf,IAAIC,QAAQ,GAAG,CAAC;IAChB,OAAOJ,GAAG,IAAIC,IAAI,EAAE;MAChBC,GAAG,GAAGF,GAAG,GAAI,CAACC,IAAI,GAAGD,GAAG,IAAI,CAAE,GAAG,CAAC;MAClCI,QAAQ,GAAGxN,UAAU,CAACsN,GAAG,CAAC;MAC1B,IAAIA,GAAG,KAAKD,IAAI,EAAE;QACd;MACJ;MACAE,OAAO,GAAGvN,UAAU,CAACsN,GAAG,GAAG,CAAC,CAAC;MAC7B,IAAI7K,MAAM,GAAG+K,QAAQ,EAAE;QACnBH,IAAI,GAAGC,GAAG,GAAG,CAAC;MAClB,CAAC,MACI,IAAI7K,MAAM,IAAI8K,OAAO,EAAE;QACxBH,GAAG,GAAGE,GAAG,GAAG,CAAC;MACjB,CAAC,MACI;QACD;MACJ;IACJ;IACA,IAAI9F,GAAG,EAAE;MACLA,GAAG,CAAC1D,IAAI,GAAGwJ,GAAG;MACd9F,GAAG,CAACzD,MAAM,GAAGtB,MAAM,GAAG+K,QAAQ;MAC9B,OAAO,IAAI;IACf;IACA,OAAO;MACH1J,IAAI,EAAEwJ,GAAG;MACTvJ,MAAM,EAAEtB,MAAM,GAAG+K;IACrB,CAAC;EACL;EACA7D,cAAcA,CAAC3I,WAAW,EAAEC,KAAK,EAAEC,GAAG,EAAE;IACpC;IACA;IACA,IAAIA,GAAG,CAAC6C,MAAM,KAAK,CAAC,EAAE;MAClB,OAAO7C,GAAG,CAAC4C,IAAI,GAAG7C,KAAK,CAAC6C,IAAI;IAChC;IACA,MAAM9D,UAAU,GAAG,IAAI,CAAC4D,QAAQ,CAAC5C,WAAW,CAAC,CAAChB,UAAU;IACxD,IAAIkB,GAAG,CAAC4C,IAAI,KAAK9D,UAAU,CAACN,MAAM,GAAG,CAAC,EAAE;MAAE;MACtC,OAAOwB,GAAG,CAAC4C,IAAI,GAAG7C,KAAK,CAAC6C,IAAI;IAChC;IACA,MAAMsG,mBAAmB,GAAGpK,UAAU,CAACkB,GAAG,CAAC4C,IAAI,GAAG,CAAC,CAAC;IACpD,MAAM2J,SAAS,GAAGzN,UAAU,CAACkB,GAAG,CAAC4C,IAAI,CAAC,GAAG5C,GAAG,CAAC6C,MAAM;IACnD,IAAIqG,mBAAmB,GAAGqD,SAAS,GAAG,CAAC,EAAE;MAAE;MACvC,OAAOvM,GAAG,CAAC4C,IAAI,GAAG7C,KAAK,CAAC6C,IAAI;IAChC;IACA;IACA;IACA;IACA,MAAM4J,kBAAkB,GAAGD,SAAS,GAAG,CAAC,CAAC,CAAC;IAC1C,MAAMpM,MAAM,GAAG,IAAI,CAACuC,QAAQ,CAAC5C,WAAW,CAAC,CAACK,MAAM;IAChD,IAAIA,MAAM,CAACT,UAAU,CAAC8M,kBAAkB,CAAC,KAAK,EAAE,EAAE;MAC9C,OAAOxM,GAAG,CAAC4C,IAAI,GAAG7C,KAAK,CAAC6C,IAAI,GAAG,CAAC;IACpC,CAAC,MACI;MACD,OAAO5C,GAAG,CAAC4C,IAAI,GAAG7C,KAAK,CAAC6C,IAAI;IAChC;EACJ;EACAwD,cAAcA,CAACtG,WAAW,EAAE2M,MAAM,EAAE;IAChC,MAAM3N,UAAU,GAAG,IAAI,CAAC4D,QAAQ,CAAC5C,WAAW,CAAC,CAAChB,UAAU;IACxD,OAAOA,UAAU,CAAC2N,MAAM,CAAC7J,IAAI,CAAC,GAAG6J,MAAM,CAAC5J,MAAM;EAClD;EACAkI,WAAWA,CAAC2B,KAAK,EAAE;IACf,KAAK,IAAInN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmN,KAAK,CAAClO,MAAM,EAAEe,CAAC,EAAE,EAAE;MACnCzB,QAAQ,CAAC,IAAI,EAAE4O,KAAK,CAACnN,CAAC,CAAC,CAAC;IAC5B;EACJ;EACAqL,eAAeA,CAAC1G,IAAI,EAAE;IAClB,IAAIA,IAAI,CAAC1F,MAAM,GAAGJ,iBAAiB,EAAE;MACjC;MACA;MACA,MAAMuM,SAAS,GAAG,EAAE;MACpB,OAAOzG,IAAI,CAAC1F,MAAM,GAAGJ,iBAAiB,EAAE;QACpC,MAAMuO,QAAQ,GAAGzI,IAAI,CAACxE,UAAU,CAACtB,iBAAiB,GAAG,CAAC,CAAC;QACvD,IAAIwO,SAAS;QACb,IAAID,QAAQ,KAAK,EAAE,CAAC,iCAAkCA,QAAQ,IAAI,MAAM,IAAIA,QAAQ,IAAI,MAAO,EAAE;UAC7F;UACAC,SAAS,GAAG1I,IAAI,CAACmC,SAAS,CAAC,CAAC,EAAEjI,iBAAiB,GAAG,CAAC,CAAC;UACpD8F,IAAI,GAAGA,IAAI,CAACmC,SAAS,CAACjI,iBAAiB,GAAG,CAAC,CAAC;QAChD,CAAC,MACI;UACDwO,SAAS,GAAG1I,IAAI,CAACmC,SAAS,CAAC,CAAC,EAAEjI,iBAAiB,CAAC;UAChD8F,IAAI,GAAGA,IAAI,CAACmC,SAAS,CAACjI,iBAAiB,CAAC;QAC5C;QACA,MAAMU,UAAU,GAAGK,oBAAoB,CAACyN,SAAS,CAAC;QAClDjC,SAAS,CAAC7J,IAAI,CAAC,IAAIjB,KAAK,CAAC,IAAI,CAAC6C,QAAQ,CAAClE,MAAM,EAAE,kBAAmB;UAAEoE,IAAI,EAAE,CAAC;UAAEC,MAAM,EAAE;QAAE,CAAC,EAAE;UAAED,IAAI,EAAE9D,UAAU,CAACN,MAAM,GAAG,CAAC;UAAEqE,MAAM,EAAE+J,SAAS,CAACpO,MAAM,GAAGM,UAAU,CAACA,UAAU,CAACN,MAAM,GAAG,CAAC;QAAE,CAAC,EAAEM,UAAU,CAACN,MAAM,GAAG,CAAC,EAAEoO,SAAS,CAACpO,MAAM,CAAC,CAAC;QAClO,IAAI,CAACkE,QAAQ,CAAC5B,IAAI,CAAC,IAAIZ,YAAY,CAAC0M,SAAS,EAAE9N,UAAU,CAAC,CAAC;MAC/D;MACA,MAAMA,UAAU,GAAGK,oBAAoB,CAAC+E,IAAI,CAAC;MAC7CyG,SAAS,CAAC7J,IAAI,CAAC,IAAIjB,KAAK,CAAC,IAAI,CAAC6C,QAAQ,CAAClE,MAAM,EAAE,kBAAmB;QAAEoE,IAAI,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC,EAAE;QAAED,IAAI,EAAE9D,UAAU,CAACN,MAAM,GAAG,CAAC;QAAEqE,MAAM,EAAEqB,IAAI,CAAC1F,MAAM,GAAGM,UAAU,CAACA,UAAU,CAACN,MAAM,GAAG,CAAC;MAAE,CAAC,EAAEM,UAAU,CAACN,MAAM,GAAG,CAAC,EAAE0F,IAAI,CAAC1F,MAAM,CAAC,CAAC;MACxN,IAAI,CAACkE,QAAQ,CAAC5B,IAAI,CAAC,IAAIZ,YAAY,CAACgE,IAAI,EAAEpF,UAAU,CAAC,CAAC;MACtD,OAAO6L,SAAS;IACpB;IACA,IAAIxE,WAAW,GAAG,IAAI,CAACzD,QAAQ,CAAC,CAAC,CAAC,CAACvC,MAAM,CAAC3B,MAAM;IAChD,MAAMM,UAAU,GAAGK,oBAAoB,CAAC+E,IAAI,EAAE,KAAK,CAAC;IACpD,IAAInE,KAAK,GAAG,IAAI,CAAC4C,oBAAoB;IACrC,IAAI,IAAI,CAACD,QAAQ,CAAC,CAAC,CAAC,CAAC5D,UAAU,CAAC,IAAI,CAAC4D,QAAQ,CAAC,CAAC,CAAC,CAAC5D,UAAU,CAACN,MAAM,GAAG,CAAC,CAAC,KAAK2H,WAAW,IAChFA,WAAW,KAAK,CAAC,IACjB,IAAI,CAACoE,WAAW,CAACrG,IAAI,CAAC,IACtB,IAAI,CAACiG,SAAS,CAAC,IAAI,CAACzH,QAAQ,CAAC,CAAC,CAAC,CAACvC,MAAM,CAAC,CAAC;IAAA,EAC7C;MACE,IAAI,CAACwC,oBAAoB,GAAG;QAAEC,IAAI,EAAE,IAAI,CAACD,oBAAoB,CAACC,IAAI;QAAEC,MAAM,EAAE,IAAI,CAACF,oBAAoB,CAACE,MAAM,GAAG;MAAE,CAAC;MAClH9C,KAAK,GAAG,IAAI,CAAC4C,oBAAoB;MACjC,KAAK,IAAIpD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,UAAU,CAACN,MAAM,EAAEe,CAAC,EAAE,EAAE;QACxCT,UAAU,CAACS,CAAC,CAAC,IAAI4G,WAAW,GAAG,CAAC;MACpC;MACA,IAAI,CAACzD,QAAQ,CAAC,CAAC,CAAC,CAAC5D,UAAU,GAAG,IAAI,CAAC4D,QAAQ,CAAC,CAAC,CAAC,CAAC5D,UAAU,CAAC+N,MAAM,CAAC/N,UAAU,CAACgO,KAAK,CAAC,CAAC,CAAC,CAAC;MACrF,IAAI,CAACpK,QAAQ,CAAC,CAAC,CAAC,CAACvC,MAAM,IAAI,GAAG,GAAG+D,IAAI;MACrCiC,WAAW,IAAI,CAAC;IACpB,CAAC,MACI;MACD,IAAIA,WAAW,KAAK,CAAC,EAAE;QACnB,KAAK,IAAI5G,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,UAAU,CAACN,MAAM,EAAEe,CAAC,EAAE,EAAE;UACxCT,UAAU,CAACS,CAAC,CAAC,IAAI4G,WAAW;QAChC;MACJ;MACA,IAAI,CAACzD,QAAQ,CAAC,CAAC,CAAC,CAAC5D,UAAU,GAAG,IAAI,CAAC4D,QAAQ,CAAC,CAAC,CAAC,CAAC5D,UAAU,CAAC+N,MAAM,CAAC/N,UAAU,CAACgO,KAAK,CAAC,CAAC,CAAC,CAAC;MACrF,IAAI,CAACpK,QAAQ,CAAC,CAAC,CAAC,CAACvC,MAAM,IAAI+D,IAAI;IACnC;IACA,MAAMqI,SAAS,GAAG,IAAI,CAAC7J,QAAQ,CAAC,CAAC,CAAC,CAACvC,MAAM,CAAC3B,MAAM;IAChD,MAAMuO,QAAQ,GAAG,IAAI,CAACrK,QAAQ,CAAC,CAAC,CAAC,CAAC5D,UAAU,CAACN,MAAM,GAAG,CAAC;IACvD,MAAMsH,SAAS,GAAGyG,SAAS,GAAG,IAAI,CAAC7J,QAAQ,CAAC,CAAC,CAAC,CAAC5D,UAAU,CAACiO,QAAQ,CAAC;IACnE,MAAMC,MAAM,GAAG;MAAEpK,IAAI,EAAEmK,QAAQ;MAAElK,MAAM,EAAEiD;IAAU,CAAC;IACpD,MAAMmH,QAAQ,GAAG,IAAIpN,KAAK,CAAC,CAAC,EAAE,gBAAiBE,KAAK,EAAEiN,MAAM,EAAE,IAAI,CAACvE,cAAc,CAAC,CAAC,EAAE1I,KAAK,EAAEiN,MAAM,CAAC,EAAET,SAAS,GAAGpG,WAAW,CAAC;IAC7H,IAAI,CAACxD,oBAAoB,GAAGqK,MAAM;IAClC,OAAO,CAACC,QAAQ,CAAC;EACrB;EACA5F,iBAAiBA,CAAC1F,UAAU,EAAE4K,SAAS,GAAG,CAAC,EAAE;IACzC,IAAI7H,CAAC,GAAG,IAAI,CAAC/D,IAAI;IACjB,IAAI2F,GAAG,GAAG,EAAE;IACZ,MAAM4G,KAAK,GAAG,IAAI,CAAC7J,YAAY,CAAC3B,IAAI,CAACC,UAAU,CAAC;IAChD,IAAIuL,KAAK,EAAE;MACPxI,CAAC,GAAGwI,KAAK,CAACrM,IAAI;MACd,MAAMsM,oBAAoB,GAAG,IAAI,CAACpI,mBAAmB,CAACL,CAAC,EAAE/C,UAAU,GAAGuL,KAAK,CAACtL,mBAAmB,GAAG,CAAC,CAAC;MACpG,MAAMzB,MAAM,GAAG,IAAI,CAACuC,QAAQ,CAACgC,CAAC,CAAC3D,KAAK,CAACjB,WAAW,CAAC,CAACK,MAAM;MACxD,MAAMgG,WAAW,GAAG,IAAI,CAACC,cAAc,CAAC1B,CAAC,CAAC3D,KAAK,CAACjB,WAAW,EAAE4E,CAAC,CAAC3D,KAAK,CAAChB,KAAK,CAAC;MAC3E,IAAImN,KAAK,CAACtL,mBAAmB,GAAG8C,CAAC,CAAC3D,KAAK,CAACd,WAAW,KAAK0B,UAAU,EAAE;QAChE2E,GAAG,GAAGnG,MAAM,CAACkG,SAAS,CAACF,WAAW,GAAGgH,oBAAoB,EAAEhH,WAAW,GAAGzB,CAAC,CAAC3D,KAAK,CAACvC,MAAM,CAAC;MAC5F,CAAC,MACI;QACD,MAAM4O,gBAAgB,GAAG,IAAI,CAACrI,mBAAmB,CAACL,CAAC,EAAE/C,UAAU,GAAGuL,KAAK,CAACtL,mBAAmB,CAAC;QAC5F,OAAOzB,MAAM,CAACkG,SAAS,CAACF,WAAW,GAAGgH,oBAAoB,EAAEhH,WAAW,GAAGiH,gBAAgB,GAAGb,SAAS,CAAC;MAC3G;IACJ,CAAC,MACI;MACD,IAAI9K,eAAe,GAAG,CAAC;MACvB,MAAM4L,kBAAkB,GAAG1L,UAAU;MACrC,OAAO+C,CAAC,KAAKhH,QAAQ,EAAE;QACnB,IAAIgH,CAAC,CAACC,IAAI,KAAKjH,QAAQ,IAAIgH,CAAC,CAACE,OAAO,IAAIjD,UAAU,GAAG,CAAC,EAAE;UACpD+C,CAAC,GAAGA,CAAC,CAACC,IAAI;QACd,CAAC,MACI,IAAID,CAAC,CAACE,OAAO,GAAGF,CAAC,CAAC3D,KAAK,CAACd,WAAW,GAAG0B,UAAU,GAAG,CAAC,EAAE;UACvD,MAAMwL,oBAAoB,GAAG,IAAI,CAACpI,mBAAmB,CAACL,CAAC,EAAE/C,UAAU,GAAG+C,CAAC,CAACE,OAAO,GAAG,CAAC,CAAC;UACpF,MAAMwI,gBAAgB,GAAG,IAAI,CAACrI,mBAAmB,CAACL,CAAC,EAAE/C,UAAU,GAAG+C,CAAC,CAACE,OAAO,GAAG,CAAC,CAAC;UAChF,MAAMzE,MAAM,GAAG,IAAI,CAACuC,QAAQ,CAACgC,CAAC,CAAC3D,KAAK,CAACjB,WAAW,CAAC,CAACK,MAAM;UACxD,MAAMgG,WAAW,GAAG,IAAI,CAACC,cAAc,CAAC1B,CAAC,CAAC3D,KAAK,CAACjB,WAAW,EAAE4E,CAAC,CAAC3D,KAAK,CAAChB,KAAK,CAAC;UAC3E0B,eAAe,IAAIiD,CAAC,CAACG,SAAS;UAC9B,IAAI,CAACxB,YAAY,CAAC1E,GAAG,CAAC;YAClBkC,IAAI,EAAE6D,CAAC;YACPjD,eAAe;YACfG,mBAAmB,EAAEyL,kBAAkB,IAAI1L,UAAU,GAAG,CAAC,GAAG+C,CAAC,CAACE,OAAO;UACzE,CAAC,CAAC;UACF,OAAOzE,MAAM,CAACkG,SAAS,CAACF,WAAW,GAAGgH,oBAAoB,EAAEhH,WAAW,GAAGiH,gBAAgB,GAAGb,SAAS,CAAC;QAC3G,CAAC,MACI,IAAI7H,CAAC,CAACE,OAAO,GAAGF,CAAC,CAAC3D,KAAK,CAACd,WAAW,KAAK0B,UAAU,GAAG,CAAC,EAAE;UACzD,MAAMwL,oBAAoB,GAAG,IAAI,CAACpI,mBAAmB,CAACL,CAAC,EAAE/C,UAAU,GAAG+C,CAAC,CAACE,OAAO,GAAG,CAAC,CAAC;UACpF,MAAMzE,MAAM,GAAG,IAAI,CAACuC,QAAQ,CAACgC,CAAC,CAAC3D,KAAK,CAACjB,WAAW,CAAC,CAACK,MAAM;UACxD,MAAMgG,WAAW,GAAG,IAAI,CAACC,cAAc,CAAC1B,CAAC,CAAC3D,KAAK,CAACjB,WAAW,EAAE4E,CAAC,CAAC3D,KAAK,CAAChB,KAAK,CAAC;UAC3EuG,GAAG,GAAGnG,MAAM,CAACkG,SAAS,CAACF,WAAW,GAAGgH,oBAAoB,EAAEhH,WAAW,GAAGzB,CAAC,CAAC3D,KAAK,CAACvC,MAAM,CAAC;UACxF;QACJ,CAAC,MACI;UACDmD,UAAU,IAAI+C,CAAC,CAACE,OAAO,GAAGF,CAAC,CAAC3D,KAAK,CAACd,WAAW;UAC7CwB,eAAe,IAAIiD,CAAC,CAACG,SAAS,GAAGH,CAAC,CAAC3D,KAAK,CAACvC,MAAM;UAC/CkG,CAAC,GAAGA,CAAC,CAACM,KAAK;QACf;MACJ;IACJ;IACA;IACAN,CAAC,GAAGA,CAAC,CAAC6B,IAAI,CAAC,CAAC;IACZ,OAAO7B,CAAC,KAAKhH,QAAQ,EAAE;MACnB,MAAMyC,MAAM,GAAG,IAAI,CAACuC,QAAQ,CAACgC,CAAC,CAAC3D,KAAK,CAACjB,WAAW,CAAC,CAACK,MAAM;MACxD,IAAIuE,CAAC,CAAC3D,KAAK,CAACd,WAAW,GAAG,CAAC,EAAE;QACzB,MAAMmN,gBAAgB,GAAG,IAAI,CAACrI,mBAAmB,CAACL,CAAC,EAAE,CAAC,CAAC;QACvD,MAAMyB,WAAW,GAAG,IAAI,CAACC,cAAc,CAAC1B,CAAC,CAAC3D,KAAK,CAACjB,WAAW,EAAE4E,CAAC,CAAC3D,KAAK,CAAChB,KAAK,CAAC;QAC3EuG,GAAG,IAAInG,MAAM,CAACkG,SAAS,CAACF,WAAW,EAAEA,WAAW,GAAGiH,gBAAgB,GAAGb,SAAS,CAAC;QAChF,OAAOjG,GAAG;MACd,CAAC,MACI;QACD,MAAMH,WAAW,GAAG,IAAI,CAACC,cAAc,CAAC1B,CAAC,CAAC3D,KAAK,CAACjB,WAAW,EAAE4E,CAAC,CAAC3D,KAAK,CAAChB,KAAK,CAAC;QAC3EuG,GAAG,IAAInG,MAAM,CAACqG,MAAM,CAACL,WAAW,EAAEzB,CAAC,CAAC3D,KAAK,CAACvC,MAAM,CAAC;MACrD;MACAkG,CAAC,GAAGA,CAAC,CAAC6B,IAAI,CAAC,CAAC;IAChB;IACA,OAAOD,GAAG;EACd;EACA9C,qBAAqBA,CAAA,EAAG;IACpB,IAAIkB,CAAC,GAAG,IAAI,CAAC/D,IAAI;IACjB,IAAIuE,KAAK,GAAG,CAAC;IACb,IAAI1F,GAAG,GAAG,CAAC;IACX,OAAOkF,CAAC,KAAKhH,QAAQ,EAAE;MACnBwH,KAAK,IAAIR,CAAC,CAACE,OAAO,GAAGF,CAAC,CAAC3D,KAAK,CAACd,WAAW;MACxCT,GAAG,IAAIkF,CAAC,CAACG,SAAS,GAAGH,CAAC,CAAC3D,KAAK,CAACvC,MAAM;MACnCkG,CAAC,GAAGA,CAAC,CAACM,KAAK;IACf;IACA,IAAI,CAAClC,QAAQ,GAAGoC,KAAK;IACrB,IAAI,CAACnC,OAAO,GAAGvD,GAAG;IAClB,IAAI,CAAC6D,YAAY,CAACtB,QAAQ,CAAC,IAAI,CAACgB,OAAO,CAAC;EAC5C;EACA;EACAsC,UAAUA,CAACxE,IAAI,EAAEuM,gBAAgB,EAAE;IAC/B,MAAMrM,KAAK,GAAGF,IAAI,CAACE,KAAK;IACxB,MAAMuM,GAAG,GAAG,IAAI,CAAC9E,gBAAgB,CAAC3H,IAAI,EAAEuM,gBAAgB,CAAC;IACzD,MAAMG,OAAO,GAAGD,GAAG,CAAC1K,IAAI,GAAG7B,KAAK,CAAChB,KAAK,CAAC6C,IAAI;IAC3C,IAAI,IAAI,CAACwD,cAAc,CAACrF,KAAK,CAACjB,WAAW,EAAEiB,KAAK,CAACf,GAAG,CAAC,GAAG,IAAI,CAACoG,cAAc,CAACrF,KAAK,CAACjB,WAAW,EAAEiB,KAAK,CAAChB,KAAK,CAAC,KAAKqN,gBAAgB,EAAE;MAC9H;MACA,MAAMI,WAAW,GAAG,IAAI,CAAC/E,cAAc,CAAC5H,IAAI,CAACE,KAAK,CAACjB,WAAW,EAAEiB,KAAK,CAAChB,KAAK,EAAEuN,GAAG,CAAC;MACjF,IAAIE,WAAW,KAAKD,OAAO,EAAE;QACzB;QACA,OAAO;UAAEjI,KAAK,EAAEkI,WAAW;UAAEhI,SAAS,EAAE;QAAE,CAAC;MAC/C;IACJ;IACA,OAAO;MAAEF,KAAK,EAAEiI,OAAO;MAAE/H,SAAS,EAAE8H,GAAG,CAACzK;IAAO,CAAC;EACpD;EACAkC,mBAAmBA,CAAClE,IAAI,EAAEyE,KAAK,EAAE;IAC7B,IAAIA,KAAK,GAAG,CAAC,EAAE;MACX,OAAO,CAAC;IACZ;IACA,MAAMvE,KAAK,GAAGF,IAAI,CAACE,KAAK;IACxB,MAAMjC,UAAU,GAAG,IAAI,CAAC4D,QAAQ,CAAC3B,KAAK,CAACjB,WAAW,CAAC,CAAChB,UAAU;IAC9D,MAAM2O,sBAAsB,GAAG1M,KAAK,CAAChB,KAAK,CAAC6C,IAAI,GAAG0C,KAAK,GAAG,CAAC;IAC3D,IAAImI,sBAAsB,GAAG1M,KAAK,CAACf,GAAG,CAAC4C,IAAI,EAAE;MACzC,OAAO9D,UAAU,CAACiC,KAAK,CAACf,GAAG,CAAC4C,IAAI,CAAC,GAAG7B,KAAK,CAACf,GAAG,CAAC6C,MAAM,GAAG/D,UAAU,CAACiC,KAAK,CAAChB,KAAK,CAAC6C,IAAI,CAAC,GAAG7B,KAAK,CAAChB,KAAK,CAAC8C,MAAM;IAC5G,CAAC,MACI;MACD,OAAO/D,UAAU,CAAC2O,sBAAsB,CAAC,GAAG3O,UAAU,CAACiC,KAAK,CAAChB,KAAK,CAAC6C,IAAI,CAAC,GAAG7B,KAAK,CAAChB,KAAK,CAAC8C,MAAM;IACjG;EACJ;EACA6H,cAAcA,CAAC7J,IAAI,EAAEyM,GAAG,EAAE;IACtB,MAAMvM,KAAK,GAAGF,IAAI,CAACE,KAAK;IACxB,MAAM2M,aAAa,GAAG3M,KAAK,CAACd,WAAW;IACvC,MAAM0N,iBAAiB,GAAG,IAAI,CAACvH,cAAc,CAACrF,KAAK,CAACjB,WAAW,EAAEiB,KAAK,CAACf,GAAG,CAAC;IAC3E,MAAM4N,MAAM,GAAGN,GAAG;IAClB,MAAMO,YAAY,GAAG,IAAI,CAACzH,cAAc,CAACrF,KAAK,CAACjB,WAAW,EAAE8N,MAAM,CAAC;IACnE,MAAME,cAAc,GAAG,IAAI,CAACrF,cAAc,CAAC1H,KAAK,CAACjB,WAAW,EAAEiB,KAAK,CAAChB,KAAK,EAAE6N,MAAM,CAAC;IAClF,MAAMG,QAAQ,GAAGD,cAAc,GAAGJ,aAAa;IAC/C,MAAMM,UAAU,GAAGH,YAAY,GAAGF,iBAAiB;IACnD,MAAMM,SAAS,GAAGlN,KAAK,CAACvC,MAAM,GAAGwP,UAAU;IAC3CnN,IAAI,CAACE,KAAK,GAAG,IAAIlB,KAAK,CAACkB,KAAK,CAACjB,WAAW,EAAEiB,KAAK,CAAChB,KAAK,EAAE6N,MAAM,EAAEE,cAAc,EAAEG,SAAS,CAAC;IACzFjQ,kBAAkB,CAAC,IAAI,EAAE6C,IAAI,EAAEmN,UAAU,EAAED,QAAQ,CAAC;EACxD;EACArC,cAAcA,CAAC7K,IAAI,EAAEyM,GAAG,EAAE;IACtB,MAAMvM,KAAK,GAAGF,IAAI,CAACE,KAAK;IACxB,MAAM2M,aAAa,GAAG3M,KAAK,CAACd,WAAW;IACvC,MAAMiO,mBAAmB,GAAG,IAAI,CAAC9H,cAAc,CAACrF,KAAK,CAACjB,WAAW,EAAEiB,KAAK,CAAChB,KAAK,CAAC;IAC/E,MAAMuK,QAAQ,GAAGgD,GAAG;IACpB,MAAMQ,cAAc,GAAG,IAAI,CAACrF,cAAc,CAAC1H,KAAK,CAACjB,WAAW,EAAEwK,QAAQ,EAAEvJ,KAAK,CAACf,GAAG,CAAC;IAClF,MAAMmO,cAAc,GAAG,IAAI,CAAC/H,cAAc,CAACrF,KAAK,CAACjB,WAAW,EAAEwK,QAAQ,CAAC;IACvE,MAAMyD,QAAQ,GAAGD,cAAc,GAAGJ,aAAa;IAC/C,MAAMM,UAAU,GAAGE,mBAAmB,GAAGC,cAAc;IACvD,MAAMF,SAAS,GAAGlN,KAAK,CAACvC,MAAM,GAAGwP,UAAU;IAC3CnN,IAAI,CAACE,KAAK,GAAG,IAAIlB,KAAK,CAACkB,KAAK,CAACjB,WAAW,EAAEwK,QAAQ,EAAEvJ,KAAK,CAACf,GAAG,EAAE8N,cAAc,EAAEG,SAAS,CAAC;IACzFjQ,kBAAkB,CAAC,IAAI,EAAE6C,IAAI,EAAEmN,UAAU,EAAED,QAAQ,CAAC;EACxD;EACAnC,UAAUA,CAAC/K,IAAI,EAAEd,KAAK,EAAEC,GAAG,EAAE;IACzB,MAAMe,KAAK,GAAGF,IAAI,CAACE,KAAK;IACxB,MAAMqN,gBAAgB,GAAGrN,KAAK,CAAChB,KAAK;IACpC,MAAMsO,cAAc,GAAGtN,KAAK,CAACf,GAAG;IAChC;IACA,MAAMsO,SAAS,GAAGvN,KAAK,CAACvC,MAAM;IAC9B,MAAM+P,QAAQ,GAAGxN,KAAK,CAACd,WAAW;IAClC,MAAM2N,MAAM,GAAG7N,KAAK;IACpB,MAAM+N,cAAc,GAAG,IAAI,CAACrF,cAAc,CAAC1H,KAAK,CAACjB,WAAW,EAAEiB,KAAK,CAAChB,KAAK,EAAE6N,MAAM,CAAC;IAClF,MAAMK,SAAS,GAAG,IAAI,CAAC7H,cAAc,CAACrF,KAAK,CAACjB,WAAW,EAAEC,KAAK,CAAC,GAAG,IAAI,CAACqG,cAAc,CAACrF,KAAK,CAACjB,WAAW,EAAEsO,gBAAgB,CAAC;IAC1HvN,IAAI,CAACE,KAAK,GAAG,IAAIlB,KAAK,CAACkB,KAAK,CAACjB,WAAW,EAAEiB,KAAK,CAAChB,KAAK,EAAE6N,MAAM,EAAEE,cAAc,EAAEG,SAAS,CAAC;IACzFjQ,kBAAkB,CAAC,IAAI,EAAE6C,IAAI,EAAEoN,SAAS,GAAGK,SAAS,EAAER,cAAc,GAAGS,QAAQ,CAAC;IAChF;IACA,MAAMtB,QAAQ,GAAG,IAAIpN,KAAK,CAACkB,KAAK,CAACjB,WAAW,EAAEE,GAAG,EAAEqO,cAAc,EAAE,IAAI,CAAC5F,cAAc,CAAC1H,KAAK,CAACjB,WAAW,EAAEE,GAAG,EAAEqO,cAAc,CAAC,EAAE,IAAI,CAACjI,cAAc,CAACrF,KAAK,CAACjB,WAAW,EAAEuO,cAAc,CAAC,GAAG,IAAI,CAACjI,cAAc,CAACrF,KAAK,CAACjB,WAAW,EAAEE,GAAG,CAAC,CAAC;IACrO,MAAMgM,OAAO,GAAG,IAAI,CAAC5I,aAAa,CAACvC,IAAI,EAAEoM,QAAQ,CAAC;IAClD,IAAI,CAACxB,wBAAwB,CAACO,OAAO,CAAC;EAC1C;EACAlC,YAAYA,CAACjJ,IAAI,EAAE0C,KAAK,EAAE;IACtB,IAAI,IAAI,CAAC0I,4BAA4B,CAAC1I,KAAK,EAAE1C,IAAI,CAAC,EAAE;MAChD0C,KAAK,IAAI,IAAI;IACjB;IACA,MAAMiL,OAAO,GAAG,IAAI,CAACtE,eAAe,CAAC,CAAC,IAAI,IAAI,CAACK,WAAW,CAAChH,KAAK,CAAC,IAAI,IAAI,CAAC4G,SAAS,CAACtJ,IAAI,CAAC;IACzF,MAAMsF,WAAW,GAAG,IAAI,CAACzD,QAAQ,CAAC,CAAC,CAAC,CAACvC,MAAM,CAAC3B,MAAM;IAClD,IAAI,CAACkE,QAAQ,CAAC,CAAC,CAAC,CAACvC,MAAM,IAAIoD,KAAK;IAChC,MAAMzE,UAAU,GAAGK,oBAAoB,CAACoE,KAAK,EAAE,KAAK,CAAC;IACrD,KAAK,IAAIhE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,UAAU,CAACN,MAAM,EAAEe,CAAC,EAAE,EAAE;MACxCT,UAAU,CAACS,CAAC,CAAC,IAAI4G,WAAW;IAChC;IACA,IAAIqI,OAAO,EAAE;MACT,MAAMC,eAAe,GAAG,IAAI,CAAC/L,QAAQ,CAAC,CAAC,CAAC,CAAC5D,UAAU,CAAC,IAAI,CAAC4D,QAAQ,CAAC,CAAC,CAAC,CAAC5D,UAAU,CAACN,MAAM,GAAG,CAAC,CAAC;MAC3F,IAAI,CAACkE,QAAQ,CAAC,CAAC,CAAC,CAAC5D,UAAU,CAAC4P,GAAG,CAAC,CAAC;MACjC;MACA,IAAI,CAAC/L,oBAAoB,GAAG;QAAEC,IAAI,EAAE,IAAI,CAACD,oBAAoB,CAACC,IAAI,GAAG,CAAC;QAAEC,MAAM,EAAEsD,WAAW,GAAGsI;MAAgB,CAAC;IACnH;IACA,IAAI,CAAC/L,QAAQ,CAAC,CAAC,CAAC,CAAC5D,UAAU,GAAG,IAAI,CAAC4D,QAAQ,CAAC,CAAC,CAAC,CAAC5D,UAAU,CAAC+N,MAAM,CAAC/N,UAAU,CAACgO,KAAK,CAAC,CAAC,CAAC,CAAC;IACrF,MAAMC,QAAQ,GAAG,IAAI,CAACrK,QAAQ,CAAC,CAAC,CAAC,CAAC5D,UAAU,CAACN,MAAM,GAAG,CAAC;IACvD,MAAMsH,SAAS,GAAG,IAAI,CAACpD,QAAQ,CAAC,CAAC,CAAC,CAACvC,MAAM,CAAC3B,MAAM,GAAG,IAAI,CAACkE,QAAQ,CAAC,CAAC,CAAC,CAAC5D,UAAU,CAACiO,QAAQ,CAAC;IACxF,MAAMa,MAAM,GAAG;MAAEhL,IAAI,EAAEmK,QAAQ;MAAElK,MAAM,EAAEiD;IAAU,CAAC;IACpD,MAAMmI,SAAS,GAAGpN,IAAI,CAACE,KAAK,CAACvC,MAAM,GAAG+E,KAAK,CAAC/E,MAAM;IAClD,MAAMmQ,cAAc,GAAG9N,IAAI,CAACE,KAAK,CAACd,WAAW;IAC7C,MAAM6N,cAAc,GAAG,IAAI,CAACrF,cAAc,CAAC,CAAC,EAAE5H,IAAI,CAACE,KAAK,CAAChB,KAAK,EAAE6N,MAAM,CAAC;IACvE,MAAMG,QAAQ,GAAGD,cAAc,GAAGa,cAAc;IAChD9N,IAAI,CAACE,KAAK,GAAG,IAAIlB,KAAK,CAACgB,IAAI,CAACE,KAAK,CAACjB,WAAW,EAAEe,IAAI,CAACE,KAAK,CAAChB,KAAK,EAAE6N,MAAM,EAAEE,cAAc,EAAEG,SAAS,CAAC;IACnG,IAAI,CAACtL,oBAAoB,GAAGiL,MAAM;IAClC5P,kBAAkB,CAAC,IAAI,EAAE6C,IAAI,EAAE0C,KAAK,CAAC/E,MAAM,EAAEuP,QAAQ,CAAC;EAC1D;EACAnE,MAAMA,CAACrI,MAAM,EAAE;IACX,IAAImD,CAAC,GAAG,IAAI,CAAC/D,IAAI;IACjB,MAAMuM,KAAK,GAAG,IAAI,CAAC7J,YAAY,CAAC/B,GAAG,CAACC,MAAM,CAAC;IAC3C,IAAI2L,KAAK,EAAE;MACP,OAAO;QACHrM,IAAI,EAAEqM,KAAK,CAACrM,IAAI;QAChBY,eAAe,EAAEyL,KAAK,CAACzL,eAAe;QACtC+D,SAAS,EAAEjE,MAAM,GAAG2L,KAAK,CAACzL;MAC9B,CAAC;IACL;IACA,IAAIA,eAAe,GAAG,CAAC;IACvB,OAAOiD,CAAC,KAAKhH,QAAQ,EAAE;MACnB,IAAIgH,CAAC,CAACG,SAAS,GAAGtD,MAAM,EAAE;QACtBmD,CAAC,GAAGA,CAAC,CAACC,IAAI;MACd,CAAC,MACI,IAAID,CAAC,CAACG,SAAS,GAAGH,CAAC,CAAC3D,KAAK,CAACvC,MAAM,IAAI+C,MAAM,EAAE;QAC7CE,eAAe,IAAIiD,CAAC,CAACG,SAAS;QAC9B,MAAMyB,GAAG,GAAG;UACRzF,IAAI,EAAE6D,CAAC;UACPc,SAAS,EAAEjE,MAAM,GAAGmD,CAAC,CAACG,SAAS;UAC/BpD;QACJ,CAAC;QACD,IAAI,CAAC4B,YAAY,CAAC1E,GAAG,CAAC2H,GAAG,CAAC;QAC1B,OAAOA,GAAG;MACd,CAAC,MACI;QACD/E,MAAM,IAAImD,CAAC,CAACG,SAAS,GAAGH,CAAC,CAAC3D,KAAK,CAACvC,MAAM;QACtCiD,eAAe,IAAIiD,CAAC,CAACG,SAAS,GAAGH,CAAC,CAAC3D,KAAK,CAACvC,MAAM;QAC/CkG,CAAC,GAAGA,CAAC,CAACM,KAAK;MACf;IACJ;IACA,OAAO,IAAI;EACf;EACAgB,OAAOA,CAACrE,UAAU,EAAEkB,MAAM,EAAE;IACxB,IAAI6B,CAAC,GAAG,IAAI,CAAC/D,IAAI;IACjB,IAAIc,eAAe,GAAG,CAAC;IACvB,OAAOiD,CAAC,KAAKhH,QAAQ,EAAE;MACnB,IAAIgH,CAAC,CAACC,IAAI,KAAKjH,QAAQ,IAAIgH,CAAC,CAACE,OAAO,IAAIjD,UAAU,GAAG,CAAC,EAAE;QACpD+C,CAAC,GAAGA,CAAC,CAACC,IAAI;MACd,CAAC,MACI,IAAID,CAAC,CAACE,OAAO,GAAGF,CAAC,CAAC3D,KAAK,CAACd,WAAW,GAAG0B,UAAU,GAAG,CAAC,EAAE;QACvD,MAAMiN,oBAAoB,GAAG,IAAI,CAAC7J,mBAAmB,CAACL,CAAC,EAAE/C,UAAU,GAAG+C,CAAC,CAACE,OAAO,GAAG,CAAC,CAAC;QACpF,MAAMwI,gBAAgB,GAAG,IAAI,CAACrI,mBAAmB,CAACL,CAAC,EAAE/C,UAAU,GAAG+C,CAAC,CAACE,OAAO,GAAG,CAAC,CAAC;QAChFnD,eAAe,IAAIiD,CAAC,CAACG,SAAS;QAC9B,OAAO;UACHhE,IAAI,EAAE6D,CAAC;UACPc,SAAS,EAAE5B,IAAI,CAACD,GAAG,CAACiL,oBAAoB,GAAG/L,MAAM,GAAG,CAAC,EAAEuK,gBAAgB,CAAC;UACxE3L;QACJ,CAAC;MACL,CAAC,MACI,IAAIiD,CAAC,CAACE,OAAO,GAAGF,CAAC,CAAC3D,KAAK,CAACd,WAAW,KAAK0B,UAAU,GAAG,CAAC,EAAE;QACzD,MAAMiN,oBAAoB,GAAG,IAAI,CAAC7J,mBAAmB,CAACL,CAAC,EAAE/C,UAAU,GAAG+C,CAAC,CAACE,OAAO,GAAG,CAAC,CAAC;QACpF,IAAIgK,oBAAoB,GAAG/L,MAAM,GAAG,CAAC,IAAI6B,CAAC,CAAC3D,KAAK,CAACvC,MAAM,EAAE;UACrD,OAAO;YACHqC,IAAI,EAAE6D,CAAC;YACPc,SAAS,EAAEoJ,oBAAoB,GAAG/L,MAAM,GAAG,CAAC;YAC5CpB;UACJ,CAAC;QACL,CAAC,MACI;UACDoB,MAAM,IAAI6B,CAAC,CAAC3D,KAAK,CAACvC,MAAM,GAAGoQ,oBAAoB;UAC/C;QACJ;MACJ,CAAC,MACI;QACDjN,UAAU,IAAI+C,CAAC,CAACE,OAAO,GAAGF,CAAC,CAAC3D,KAAK,CAACd,WAAW;QAC7CwB,eAAe,IAAIiD,CAAC,CAACG,SAAS,GAAGH,CAAC,CAAC3D,KAAK,CAACvC,MAAM;QAC/CkG,CAAC,GAAGA,CAAC,CAACM,KAAK;MACf;IACJ;IACA;IACAN,CAAC,GAAGA,CAAC,CAAC6B,IAAI,CAAC,CAAC;IACZ,OAAO7B,CAAC,KAAKhH,QAAQ,EAAE;MACnB,IAAIgH,CAAC,CAAC3D,KAAK,CAACd,WAAW,GAAG,CAAC,EAAE;QACzB,MAAMmN,gBAAgB,GAAG,IAAI,CAACrI,mBAAmB,CAACL,CAAC,EAAE,CAAC,CAAC;QACvD,MAAMjD,eAAe,GAAG,IAAI,CAACoN,YAAY,CAACnK,CAAC,CAAC;QAC5C,OAAO;UACH7D,IAAI,EAAE6D,CAAC;UACPc,SAAS,EAAE5B,IAAI,CAACD,GAAG,CAACd,MAAM,GAAG,CAAC,EAAEuK,gBAAgB,CAAC;UACjD3L;QACJ,CAAC;MACL,CAAC,MACI;QACD,IAAIiD,CAAC,CAAC3D,KAAK,CAACvC,MAAM,IAAIqE,MAAM,GAAG,CAAC,EAAE;UAC9B,MAAMpB,eAAe,GAAG,IAAI,CAACoN,YAAY,CAACnK,CAAC,CAAC;UAC5C,OAAO;YACH7D,IAAI,EAAE6D,CAAC;YACPc,SAAS,EAAE3C,MAAM,GAAG,CAAC;YACrBpB;UACJ,CAAC;QACL,CAAC,MACI;UACDoB,MAAM,IAAI6B,CAAC,CAAC3D,KAAK,CAACvC,MAAM;QAC5B;MACJ;MACAkG,CAAC,GAAGA,CAAC,CAAC6B,IAAI,CAAC,CAAC;IAChB;IACA,OAAO,IAAI;EACf;EACA8D,cAAcA,CAACxJ,IAAI,EAAEU,MAAM,EAAE;IACzB,IAAIV,IAAI,CAACE,KAAK,CAACd,WAAW,GAAG,CAAC,EAAE;MAC5B,OAAO,CAAC,CAAC;IACb;IACA,MAAME,MAAM,GAAG,IAAI,CAACuC,QAAQ,CAAC7B,IAAI,CAACE,KAAK,CAACjB,WAAW,CAAC;IACpD,MAAMgP,SAAS,GAAG,IAAI,CAAC1I,cAAc,CAACvF,IAAI,CAACE,KAAK,CAACjB,WAAW,EAAEe,IAAI,CAACE,KAAK,CAAChB,KAAK,CAAC,GAAGwB,MAAM;IACxF,OAAOpB,MAAM,CAACA,MAAM,CAACT,UAAU,CAACoP,SAAS,CAAC;EAC9C;EACAD,YAAYA,CAAChO,IAAI,EAAE;IACf,IAAI,CAACA,IAAI,EAAE;MACP,OAAO,CAAC;IACZ;IACA,IAAIyM,GAAG,GAAGzM,IAAI,CAACgE,SAAS;IACxB,OAAOhE,IAAI,KAAK,IAAI,CAACF,IAAI,EAAE;MACvB,IAAIE,IAAI,CAACqB,MAAM,CAAC8C,KAAK,KAAKnE,IAAI,EAAE;QAC5ByM,GAAG,IAAIzM,IAAI,CAACqB,MAAM,CAAC2C,SAAS,GAAGhE,IAAI,CAACqB,MAAM,CAACnB,KAAK,CAACvC,MAAM;MAC3D;MACAqC,IAAI,GAAGA,IAAI,CAACqB,MAAM;IACtB;IACA,OAAOoL,GAAG;EACd;EACA;EACA;EACApD,eAAeA,CAAA,EAAG;IACd,OAAO,EAAE,IAAI,CAAChH,cAAc,IAAI,IAAI,CAACF,IAAI,KAAK,IAAI,CAAC;EACvD;EACAuH,WAAWA,CAACwE,GAAG,EAAE;IACb,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;MACzB,OAAOA,GAAG,CAACrP,UAAU,CAAC,CAAC,CAAC,KAAK,EAAE;IACnC;IACA,IAAIqP,GAAG,KAAKrR,QAAQ,IAAIqR,GAAG,CAAChO,KAAK,CAACd,WAAW,KAAK,CAAC,EAAE;MACjD,OAAO,KAAK;IAChB;IACA,MAAMc,KAAK,GAAGgO,GAAG,CAAChO,KAAK;IACvB,MAAMjC,UAAU,GAAG,IAAI,CAAC4D,QAAQ,CAAC3B,KAAK,CAACjB,WAAW,CAAC,CAAChB,UAAU;IAC9D,MAAM8D,IAAI,GAAG7B,KAAK,CAAChB,KAAK,CAAC6C,IAAI;IAC7B,MAAMuD,WAAW,GAAGrH,UAAU,CAAC8D,IAAI,CAAC,GAAG7B,KAAK,CAAChB,KAAK,CAAC8C,MAAM;IACzD,IAAID,IAAI,KAAK9D,UAAU,CAACN,MAAM,GAAG,CAAC,EAAE;MAChC;MACA,OAAO,KAAK;IAChB;IACA,MAAMwQ,cAAc,GAAGlQ,UAAU,CAAC8D,IAAI,GAAG,CAAC,CAAC;IAC3C,IAAIoM,cAAc,GAAG7I,WAAW,GAAG,CAAC,EAAE;MAClC,OAAO,KAAK;IAChB;IACA,OAAO,IAAI,CAACzD,QAAQ,CAAC3B,KAAK,CAACjB,WAAW,CAAC,CAACK,MAAM,CAACT,UAAU,CAACyG,WAAW,CAAC,KAAK,EAAE;EACjF;EACAgE,SAASA,CAAC4E,GAAG,EAAE;IACX,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;MACzB,OAAOA,GAAG,CAACrP,UAAU,CAACqP,GAAG,CAACvQ,MAAM,GAAG,CAAC,CAAC,KAAK,EAAE;IAChD;IACA,IAAIuQ,GAAG,KAAKrR,QAAQ,IAAIqR,GAAG,CAAChO,KAAK,CAACd,WAAW,KAAK,CAAC,EAAE;MACjD,OAAO,KAAK;IAChB;IACA,OAAO,IAAI,CAACoK,cAAc,CAAC0E,GAAG,EAAEA,GAAG,CAAChO,KAAK,CAACvC,MAAM,GAAG,CAAC,CAAC,KAAK,EAAE;EAChE;EACAiN,wBAAwBA,CAACwD,QAAQ,EAAE;IAC/B,IAAI,IAAI,CAAC/E,eAAe,CAAC,CAAC,IAAI,IAAI,CAACK,WAAW,CAAC0E,QAAQ,CAAC,EAAE;MACtD,MAAMpO,IAAI,GAAGoO,QAAQ,CAACnD,IAAI,CAAC,CAAC;MAC5B,IAAI,IAAI,CAAC3B,SAAS,CAACtJ,IAAI,CAAC,EAAE;QACtB,IAAI,CAACqO,OAAO,CAACrO,IAAI,EAAEoO,QAAQ,CAAC;MAChC;IACJ;EACJ;EACAtD,wBAAwBA,CAAC9K,IAAI,EAAE;IAC3B,IAAI,IAAI,CAACqJ,eAAe,CAAC,CAAC,IAAI,IAAI,CAACC,SAAS,CAACtJ,IAAI,CAAC,EAAE;MAChD,MAAMoO,QAAQ,GAAGpO,IAAI,CAAC0F,IAAI,CAAC,CAAC;MAC5B,IAAI,IAAI,CAACgE,WAAW,CAAC0E,QAAQ,CAAC,EAAE;QAC5B,IAAI,CAACC,OAAO,CAACrO,IAAI,EAAEoO,QAAQ,CAAC;MAChC;IACJ;EACJ;EACAC,OAAOA,CAACpD,IAAI,EAAEvF,IAAI,EAAE;IAChB,MAAMyD,UAAU,GAAG,EAAE;IACrB;IACA,MAAMlL,UAAU,GAAG,IAAI,CAAC4D,QAAQ,CAACoJ,IAAI,CAAC/K,KAAK,CAACjB,WAAW,CAAC,CAAChB,UAAU;IACnE,IAAI8O,MAAM;IACV,IAAI9B,IAAI,CAAC/K,KAAK,CAACf,GAAG,CAAC6C,MAAM,KAAK,CAAC,EAAE;MAC7B;MACA+K,MAAM,GAAG;QAAEhL,IAAI,EAAEkJ,IAAI,CAAC/K,KAAK,CAACf,GAAG,CAAC4C,IAAI,GAAG,CAAC;QAAEC,MAAM,EAAE/D,UAAU,CAACgN,IAAI,CAAC/K,KAAK,CAACf,GAAG,CAAC4C,IAAI,CAAC,GAAG9D,UAAU,CAACgN,IAAI,CAAC/K,KAAK,CAACf,GAAG,CAAC4C,IAAI,GAAG,CAAC,CAAC,GAAG;MAAE,CAAC;IACjI,CAAC,MACI;MACD;MACAgL,MAAM,GAAG;QAAEhL,IAAI,EAAEkJ,IAAI,CAAC/K,KAAK,CAACf,GAAG,CAAC4C,IAAI;QAAEC,MAAM,EAAEiJ,IAAI,CAAC/K,KAAK,CAACf,GAAG,CAAC6C,MAAM,GAAG;MAAE,CAAC;IAC7E;IACA,MAAMsM,aAAa,GAAGrD,IAAI,CAAC/K,KAAK,CAACvC,MAAM,GAAG,CAAC;IAC3C,MAAM4Q,YAAY,GAAGtD,IAAI,CAAC/K,KAAK,CAACd,WAAW,GAAG,CAAC;IAC/C6L,IAAI,CAAC/K,KAAK,GAAG,IAAIlB,KAAK,CAACiM,IAAI,CAAC/K,KAAK,CAACjB,WAAW,EAAEgM,IAAI,CAAC/K,KAAK,CAAChB,KAAK,EAAE6N,MAAM,EAAEwB,YAAY,EAAED,aAAa,CAAC;IACrGnR,kBAAkB,CAAC,IAAI,EAAE8N,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACtC,IAAIA,IAAI,CAAC/K,KAAK,CAACvC,MAAM,KAAK,CAAC,EAAE;MACzBwL,UAAU,CAAClJ,IAAI,CAACgL,IAAI,CAAC;IACzB;IACA;IACA,MAAMxB,QAAQ,GAAG;MAAE1H,IAAI,EAAE2D,IAAI,CAACxF,KAAK,CAAChB,KAAK,CAAC6C,IAAI,GAAG,CAAC;MAAEC,MAAM,EAAE;IAAE,CAAC;IAC/D,MAAMoL,SAAS,GAAG1H,IAAI,CAACxF,KAAK,CAACvC,MAAM,GAAG,CAAC;IACvC,MAAMsP,cAAc,GAAG,IAAI,CAACrF,cAAc,CAAClC,IAAI,CAACxF,KAAK,CAACjB,WAAW,EAAEwK,QAAQ,EAAE/D,IAAI,CAACxF,KAAK,CAACf,GAAG,CAAC;IAC5FuG,IAAI,CAACxF,KAAK,GAAG,IAAIlB,KAAK,CAAC0G,IAAI,CAACxF,KAAK,CAACjB,WAAW,EAAEwK,QAAQ,EAAE/D,IAAI,CAACxF,KAAK,CAACf,GAAG,EAAE8N,cAAc,EAAEG,SAAS,CAAC;IACnGjQ,kBAAkB,CAAC,IAAI,EAAEuI,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACtC,IAAIA,IAAI,CAACxF,KAAK,CAACvC,MAAM,KAAK,CAAC,EAAE;MACzBwL,UAAU,CAAClJ,IAAI,CAACyF,IAAI,CAAC;IACzB;IACA;IACA,MAAM0E,MAAM,GAAG,IAAI,CAACL,eAAe,CAAC,MAAM,CAAC;IAC3C,IAAI,CAACxH,aAAa,CAAC0I,IAAI,EAAEb,MAAM,CAAC,CAAC,CAAC,CAAC;IACnC;IACA,KAAK,IAAI1L,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyK,UAAU,CAACxL,MAAM,EAAEe,CAAC,EAAE,EAAE;MACxCzB,QAAQ,CAAC,IAAI,EAAEkM,UAAU,CAACzK,CAAC,CAAC,CAAC;IACjC;EACJ;EACA0M,4BAA4BA,CAAC1I,KAAK,EAAE1C,IAAI,EAAE;IACtC,IAAI,IAAI,CAACqJ,eAAe,CAAC,CAAC,IAAI,IAAI,CAACC,SAAS,CAAC5G,KAAK,CAAC,EAAE;MACjD,MAAM0L,QAAQ,GAAGpO,IAAI,CAAC0F,IAAI,CAAC,CAAC;MAC5B,IAAI,IAAI,CAACgE,WAAW,CAAC0E,QAAQ,CAAC,EAAE;QAC5B;QACA1L,KAAK,IAAI,IAAI;QACb,IAAI0L,QAAQ,CAAClO,KAAK,CAACvC,MAAM,KAAK,CAAC,EAAE;UAC7BV,QAAQ,CAAC,IAAI,EAAEmR,QAAQ,CAAC;QAC5B,CAAC,MACI;UACD,MAAMlO,KAAK,GAAGkO,QAAQ,CAAClO,KAAK;UAC5B,MAAMuJ,QAAQ,GAAG;YAAE1H,IAAI,EAAE7B,KAAK,CAAChB,KAAK,CAAC6C,IAAI,GAAG,CAAC;YAAEC,MAAM,EAAE;UAAE,CAAC;UAC1D,MAAMoL,SAAS,GAAGlN,KAAK,CAACvC,MAAM,GAAG,CAAC;UAClC,MAAMsP,cAAc,GAAG,IAAI,CAACrF,cAAc,CAAC1H,KAAK,CAACjB,WAAW,EAAEwK,QAAQ,EAAEvJ,KAAK,CAACf,GAAG,CAAC;UAClFiP,QAAQ,CAAClO,KAAK,GAAG,IAAIlB,KAAK,CAACkB,KAAK,CAACjB,WAAW,EAAEwK,QAAQ,EAAEvJ,KAAK,CAACf,GAAG,EAAE8N,cAAc,EAAEG,SAAS,CAAC;UAC7FjQ,kBAAkB,CAAC,IAAI,EAAEiR,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC9C;QACA,OAAO,IAAI;MACf;IACJ;IACA,OAAO,KAAK;EAChB;EACA;EACA;EACA;EACArO,OAAOA,CAACC,IAAI,EAAEwO,QAAQ,EAAE;IACpB,IAAIxO,IAAI,KAAKnD,QAAQ,EAAE;MACnB,OAAO2R,QAAQ,CAAC3R,QAAQ,CAAC;IAC7B;IACA,MAAM4R,OAAO,GAAG,IAAI,CAAC1O,OAAO,CAACC,IAAI,CAAC8D,IAAI,EAAE0K,QAAQ,CAAC;IACjD,IAAI,CAACC,OAAO,EAAE;MACV,OAAOA,OAAO;IAClB;IACA,OAAOD,QAAQ,CAACxO,IAAI,CAAC,IAAI,IAAI,CAACD,OAAO,CAACC,IAAI,CAACmE,KAAK,EAAEqK,QAAQ,CAAC;EAC/D;EACApL,cAAcA,CAACpD,IAAI,EAAE;IACjB,IAAIA,IAAI,KAAKnD,QAAQ,EAAE;MACnB,OAAO,EAAE;IACb;IACA,MAAMyC,MAAM,GAAG,IAAI,CAACuC,QAAQ,CAAC7B,IAAI,CAACE,KAAK,CAACjB,WAAW,CAAC;IACpD,MAAMiB,KAAK,GAAGF,IAAI,CAACE,KAAK;IACxB,MAAMoF,WAAW,GAAG,IAAI,CAACC,cAAc,CAACrF,KAAK,CAACjB,WAAW,EAAEiB,KAAK,CAAChB,KAAK,CAAC;IACvE,MAAMwM,SAAS,GAAG,IAAI,CAACnG,cAAc,CAACrF,KAAK,CAACjB,WAAW,EAAEiB,KAAK,CAACf,GAAG,CAAC;IACnE,MAAMuP,cAAc,GAAGpP,MAAM,CAACA,MAAM,CAACkG,SAAS,CAACF,WAAW,EAAEoG,SAAS,CAAC;IACtE,OAAOgD,cAAc;EACzB;EACAtO,eAAeA,CAACF,KAAK,EAAE;IACnB,MAAMZ,MAAM,GAAG,IAAI,CAACuC,QAAQ,CAAC3B,KAAK,CAACjB,WAAW,CAAC;IAC/C,MAAMqG,WAAW,GAAG,IAAI,CAACC,cAAc,CAACrF,KAAK,CAACjB,WAAW,EAAEiB,KAAK,CAAChB,KAAK,CAAC;IACvE,MAAMwM,SAAS,GAAG,IAAI,CAACnG,cAAc,CAACrF,KAAK,CAACjB,WAAW,EAAEiB,KAAK,CAACf,GAAG,CAAC;IACnE,MAAMuP,cAAc,GAAGpP,MAAM,CAACA,MAAM,CAACkG,SAAS,CAACF,WAAW,EAAEoG,SAAS,CAAC;IACtE,OAAOgD,cAAc;EACzB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACInM,aAAaA,CAACvC,IAAI,EAAE2O,CAAC,EAAE;IACnB,MAAMC,CAAC,GAAG,IAAI9R,QAAQ,CAAC6R,CAAC,EAAE,CAAC,CAAC,mBAAmB,CAAC;IAChDC,CAAC,CAAC9K,IAAI,GAAGjH,QAAQ;IACjB+R,CAAC,CAACzK,KAAK,GAAGtH,QAAQ;IAClB+R,CAAC,CAACvN,MAAM,GAAGxE,QAAQ;IACnB+R,CAAC,CAAC5K,SAAS,GAAG,CAAC;IACf4K,CAAC,CAAC7K,OAAO,GAAG,CAAC;IACb,MAAMF,CAAC,GAAG,IAAI,CAAC/D,IAAI;IACnB,IAAI+D,CAAC,KAAKhH,QAAQ,EAAE;MAChB,IAAI,CAACiD,IAAI,GAAG8O,CAAC;MACbA,CAAC,CAACC,KAAK,GAAG,CAAC,CAAC;IAChB,CAAC,MACI,IAAI7O,IAAI,CAACmE,KAAK,KAAKtH,QAAQ,EAAE;MAC9BmD,IAAI,CAACmE,KAAK,GAAGyK,CAAC;MACdA,CAAC,CAACvN,MAAM,GAAGrB,IAAI;IACnB,CAAC,MACI;MACD,MAAMoO,QAAQ,GAAGpR,OAAO,CAACgD,IAAI,CAACmE,KAAK,CAAC;MACpCiK,QAAQ,CAACtK,IAAI,GAAG8K,CAAC;MACjBA,CAAC,CAACvN,MAAM,GAAG+M,QAAQ;IACvB;IACArR,SAAS,CAAC,IAAI,EAAE6R,CAAC,CAAC;IAClB,OAAOA,CAAC;EACZ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIvE,YAAYA,CAACrK,IAAI,EAAE2O,CAAC,EAAE;IAClB,MAAMC,CAAC,GAAG,IAAI9R,QAAQ,CAAC6R,CAAC,EAAE,CAAC,CAAC,mBAAmB,CAAC;IAChDC,CAAC,CAAC9K,IAAI,GAAGjH,QAAQ;IACjB+R,CAAC,CAACzK,KAAK,GAAGtH,QAAQ;IAClB+R,CAAC,CAACvN,MAAM,GAAGxE,QAAQ;IACnB+R,CAAC,CAAC5K,SAAS,GAAG,CAAC;IACf4K,CAAC,CAAC7K,OAAO,GAAG,CAAC;IACb,IAAI,IAAI,CAACjE,IAAI,KAAKjD,QAAQ,EAAE;MACxB,IAAI,CAACiD,IAAI,GAAG8O,CAAC;MACbA,CAAC,CAACC,KAAK,GAAG,CAAC,CAAC;IAChB,CAAC,MACI,IAAI7O,IAAI,CAAC8D,IAAI,KAAKjH,QAAQ,EAAE;MAC7BmD,IAAI,CAAC8D,IAAI,GAAG8K,CAAC;MACbA,CAAC,CAACvN,MAAM,GAAGrB,IAAI;IACnB,CAAC,MACI;MACD,MAAM8O,QAAQ,GAAG5R,SAAS,CAAC8C,IAAI,CAAC8D,IAAI,CAAC,CAAC,CAAC;MACvCgL,QAAQ,CAAC3K,KAAK,GAAGyK,CAAC;MAClBA,CAAC,CAACvN,MAAM,GAAGyN,QAAQ;IACvB;IACA/R,SAAS,CAAC,IAAI,EAAE6R,CAAC,CAAC;IAClB,OAAOA,CAAC;EACZ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}