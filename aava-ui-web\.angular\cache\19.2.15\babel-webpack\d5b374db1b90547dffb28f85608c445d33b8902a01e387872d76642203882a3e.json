{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { h, reset } from '../../../../../base/browser/dom.js';\nimport { Disposable, toDisposable } from '../../../../../base/common/lifecycle.js';\nimport { autorun, observableFromEvent, observableSignal, observableSignalFromEvent, observableValue, transaction } from '../../../../../base/common/observable.js';\nimport { LineRange } from '../../../../common/core/lineRange.js';\nimport { OffsetRange } from '../../../../common/core/offsetRange.js';\nexport class EditorGutter extends Disposable {\n  constructor(_editor, _domNode, itemProvider) {\n    super();\n    this._editor = _editor;\n    this._domNode = _domNode;\n    this.itemProvider = itemProvider;\n    this.scrollTop = observableFromEvent(this, this._editor.onDidScrollChange, e => /** @description editor.onDidScrollChange */this._editor.getScrollTop());\n    this.isScrollTopZero = this.scrollTop.map(scrollTop => /** @description isScrollTopZero */scrollTop === 0);\n    this.modelAttached = observableFromEvent(this, this._editor.onDidChangeModel, e => /** @description editor.onDidChangeModel */this._editor.hasModel());\n    this.editorOnDidChangeViewZones = observableSignalFromEvent('onDidChangeViewZones', this._editor.onDidChangeViewZones);\n    this.editorOnDidContentSizeChange = observableSignalFromEvent('onDidContentSizeChange', this._editor.onDidContentSizeChange);\n    this.domNodeSizeChanged = observableSignal('domNodeSizeChanged');\n    this.views = new Map();\n    this._domNode.className = 'gutter monaco-editor';\n    const scrollDecoration = this._domNode.appendChild(h('div.scroll-decoration', {\n      role: 'presentation',\n      ariaHidden: 'true',\n      style: {\n        width: '100%'\n      }\n    }).root);\n    const o = new ResizeObserver(() => {\n      transaction(tx => {\n        /** @description ResizeObserver: size changed */\n        this.domNodeSizeChanged.trigger(tx);\n      });\n    });\n    o.observe(this._domNode);\n    this._register(toDisposable(() => o.disconnect()));\n    this._register(autorun(reader => {\n      /** @description update scroll decoration */\n      scrollDecoration.className = this.isScrollTopZero.read(reader) ? '' : 'scroll-decoration';\n    }));\n    this._register(autorun(reader => /** @description EditorGutter.Render */this.render(reader)));\n  }\n  dispose() {\n    super.dispose();\n    reset(this._domNode);\n  }\n  render(reader) {\n    if (!this.modelAttached.read(reader)) {\n      return;\n    }\n    this.domNodeSizeChanged.read(reader);\n    this.editorOnDidChangeViewZones.read(reader);\n    this.editorOnDidContentSizeChange.read(reader);\n    const scrollTop = this.scrollTop.read(reader);\n    const visibleRanges = this._editor.getVisibleRanges();\n    const unusedIds = new Set(this.views.keys());\n    const viewRange = OffsetRange.ofStartAndLength(0, this._domNode.clientHeight);\n    if (!viewRange.isEmpty) {\n      for (const visibleRange of visibleRanges) {\n        const visibleRange2 = new LineRange(visibleRange.startLineNumber, visibleRange.endLineNumber + 1);\n        const gutterItems = this.itemProvider.getIntersectingGutterItems(visibleRange2, reader);\n        transaction(tx => {\n          /** EditorGutter.render */\n          for (const gutterItem of gutterItems) {\n            if (!gutterItem.range.intersect(visibleRange2)) {\n              continue;\n            }\n            unusedIds.delete(gutterItem.id);\n            let view = this.views.get(gutterItem.id);\n            if (!view) {\n              const viewDomNode = document.createElement('div');\n              this._domNode.appendChild(viewDomNode);\n              const gutterItemObs = observableValue('item', gutterItem);\n              const itemView = this.itemProvider.createView(gutterItemObs, viewDomNode);\n              view = new ManagedGutterItemView(gutterItemObs, itemView, viewDomNode);\n              this.views.set(gutterItem.id, view);\n            } else {\n              view.item.set(gutterItem, tx);\n            }\n            const top = gutterItem.range.startLineNumber <= this._editor.getModel().getLineCount() ? this._editor.getTopForLineNumber(gutterItem.range.startLineNumber, true) - scrollTop : this._editor.getBottomForLineNumber(gutterItem.range.startLineNumber - 1, false) - scrollTop;\n            const bottom = gutterItem.range.endLineNumberExclusive === 1 ? Math.max(top, this._editor.getTopForLineNumber(gutterItem.range.startLineNumber, false) - scrollTop) : Math.max(top, this._editor.getBottomForLineNumber(gutterItem.range.endLineNumberExclusive - 1, true) - scrollTop);\n            const height = bottom - top;\n            view.domNode.style.top = `${top}px`;\n            view.domNode.style.height = `${height}px`;\n            view.gutterItemView.layout(OffsetRange.ofStartAndLength(top, height), viewRange);\n          }\n        });\n      }\n    }\n    for (const id of unusedIds) {\n      const view = this.views.get(id);\n      view.gutterItemView.dispose();\n      view.domNode.remove();\n      this.views.delete(id);\n    }\n  }\n}\nclass ManagedGutterItemView {\n  constructor(item, gutterItemView, domNode) {\n    this.item = item;\n    this.gutterItemView = gutterItemView;\n    this.domNode = domNode;\n  }\n}", "map": {"version": 3, "names": ["h", "reset", "Disposable", "toDisposable", "autorun", "observableFromEvent", "observableSignal", "observableSignalFromEvent", "observableValue", "transaction", "LineRange", "OffsetRange", "Editor<PERSON><PERSON>", "constructor", "_editor", "_domNode", "itemProvider", "scrollTop", "onDidScrollChange", "e", "getScrollTop", "isScrollTopZero", "map", "modelAttached", "onDidChangeModel", "hasModel", "editorOnDidChangeViewZones", "onDidChangeViewZones", "editorOnDidContentSizeChange", "onDidContentSizeChange", "domNodeSizeChanged", "views", "Map", "className", "scrollDecoration", "append<PERSON><PERSON><PERSON>", "role", "ariaHidden", "style", "width", "root", "o", "ResizeObserver", "tx", "trigger", "observe", "_register", "disconnect", "reader", "read", "render", "dispose", "visibleRanges", "getVisibleRanges", "unusedIds", "Set", "keys", "viewRange", "ofStartAndLength", "clientHeight", "isEmpty", "visibleRange", "visibleRange2", "startLineNumber", "endLineNumber", "gutterItems", "getIntersectingGutterItems", "gutterItem", "range", "intersect", "delete", "id", "view", "get", "viewDomNode", "document", "createElement", "gutterItemObs", "itemView", "createView", "ManagedGutterItemView", "set", "item", "top", "getModel", "getLineCount", "getTopForLineNumber", "getBottomForLineNumber", "bottom", "endLineNumberExclusive", "Math", "max", "height", "domNode", "gutterItemView", "layout", "remove"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/browser/widget/diffEditor/utils/editorGutter.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { h, reset } from '../../../../../base/browser/dom.js';\nimport { Disposable, toDisposable } from '../../../../../base/common/lifecycle.js';\nimport { autorun, observableFromEvent, observableSignal, observableSignalFromEvent, observableValue, transaction } from '../../../../../base/common/observable.js';\nimport { LineRange } from '../../../../common/core/lineRange.js';\nimport { OffsetRange } from '../../../../common/core/offsetRange.js';\nexport class EditorGutter extends Disposable {\n    constructor(_editor, _domNode, itemProvider) {\n        super();\n        this._editor = _editor;\n        this._domNode = _domNode;\n        this.itemProvider = itemProvider;\n        this.scrollTop = observableFromEvent(this, this._editor.onDidScrollChange, (e) => /** @description editor.onDidScrollChange */ this._editor.getScrollTop());\n        this.isScrollTopZero = this.scrollTop.map((scrollTop) => /** @description isScrollTopZero */ scrollTop === 0);\n        this.modelAttached = observableFromEvent(this, this._editor.onDidChangeModel, (e) => /** @description editor.onDidChangeModel */ this._editor.hasModel());\n        this.editorOnDidChangeViewZones = observableSignalFromEvent('onDidChangeViewZones', this._editor.onDidChangeViewZones);\n        this.editorOnDidContentSizeChange = observableSignalFromEvent('onDidContentSizeChange', this._editor.onDidContentSizeChange);\n        this.domNodeSizeChanged = observableSignal('domNodeSizeChanged');\n        this.views = new Map();\n        this._domNode.className = 'gutter monaco-editor';\n        const scrollDecoration = this._domNode.appendChild(h('div.scroll-decoration', { role: 'presentation', ariaHidden: 'true', style: { width: '100%' } })\n            .root);\n        const o = new ResizeObserver(() => {\n            transaction(tx => {\n                /** @description ResizeObserver: size changed */\n                this.domNodeSizeChanged.trigger(tx);\n            });\n        });\n        o.observe(this._domNode);\n        this._register(toDisposable(() => o.disconnect()));\n        this._register(autorun(reader => {\n            /** @description update scroll decoration */\n            scrollDecoration.className = this.isScrollTopZero.read(reader) ? '' : 'scroll-decoration';\n        }));\n        this._register(autorun(reader => /** @description EditorGutter.Render */ this.render(reader)));\n    }\n    dispose() {\n        super.dispose();\n        reset(this._domNode);\n    }\n    render(reader) {\n        if (!this.modelAttached.read(reader)) {\n            return;\n        }\n        this.domNodeSizeChanged.read(reader);\n        this.editorOnDidChangeViewZones.read(reader);\n        this.editorOnDidContentSizeChange.read(reader);\n        const scrollTop = this.scrollTop.read(reader);\n        const visibleRanges = this._editor.getVisibleRanges();\n        const unusedIds = new Set(this.views.keys());\n        const viewRange = OffsetRange.ofStartAndLength(0, this._domNode.clientHeight);\n        if (!viewRange.isEmpty) {\n            for (const visibleRange of visibleRanges) {\n                const visibleRange2 = new LineRange(visibleRange.startLineNumber, visibleRange.endLineNumber + 1);\n                const gutterItems = this.itemProvider.getIntersectingGutterItems(visibleRange2, reader);\n                transaction(tx => {\n                    /** EditorGutter.render */\n                    for (const gutterItem of gutterItems) {\n                        if (!gutterItem.range.intersect(visibleRange2)) {\n                            continue;\n                        }\n                        unusedIds.delete(gutterItem.id);\n                        let view = this.views.get(gutterItem.id);\n                        if (!view) {\n                            const viewDomNode = document.createElement('div');\n                            this._domNode.appendChild(viewDomNode);\n                            const gutterItemObs = observableValue('item', gutterItem);\n                            const itemView = this.itemProvider.createView(gutterItemObs, viewDomNode);\n                            view = new ManagedGutterItemView(gutterItemObs, itemView, viewDomNode);\n                            this.views.set(gutterItem.id, view);\n                        }\n                        else {\n                            view.item.set(gutterItem, tx);\n                        }\n                        const top = gutterItem.range.startLineNumber <= this._editor.getModel().getLineCount()\n                            ? this._editor.getTopForLineNumber(gutterItem.range.startLineNumber, true) - scrollTop\n                            : this._editor.getBottomForLineNumber(gutterItem.range.startLineNumber - 1, false) - scrollTop;\n                        const bottom = gutterItem.range.endLineNumberExclusive === 1 ?\n                            Math.max(top, this._editor.getTopForLineNumber(gutterItem.range.startLineNumber, false) - scrollTop)\n                            : Math.max(top, this._editor.getBottomForLineNumber(gutterItem.range.endLineNumberExclusive - 1, true) - scrollTop);\n                        const height = bottom - top;\n                        view.domNode.style.top = `${top}px`;\n                        view.domNode.style.height = `${height}px`;\n                        view.gutterItemView.layout(OffsetRange.ofStartAndLength(top, height), viewRange);\n                    }\n                });\n            }\n        }\n        for (const id of unusedIds) {\n            const view = this.views.get(id);\n            view.gutterItemView.dispose();\n            view.domNode.remove();\n            this.views.delete(id);\n        }\n    }\n}\nclass ManagedGutterItemView {\n    constructor(item, gutterItemView, domNode) {\n        this.item = item;\n        this.gutterItemView = gutterItemView;\n        this.domNode = domNode;\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,CAAC,EAAEC,KAAK,QAAQ,oCAAoC;AAC7D,SAASC,UAAU,EAAEC,YAAY,QAAQ,yCAAyC;AAClF,SAASC,OAAO,EAAEC,mBAAmB,EAAEC,gBAAgB,EAAEC,yBAAyB,EAAEC,eAAe,EAAEC,WAAW,QAAQ,0CAA0C;AAClK,SAASC,SAAS,QAAQ,sCAAsC;AAChE,SAASC,WAAW,QAAQ,wCAAwC;AACpE,OAAO,MAAMC,YAAY,SAASV,UAAU,CAAC;EACzCW,WAAWA,CAACC,OAAO,EAAEC,QAAQ,EAAEC,YAAY,EAAE;IACzC,KAAK,CAAC,CAAC;IACP,IAAI,CAACF,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,SAAS,GAAGZ,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAACS,OAAO,CAACI,iBAAiB,EAAGC,CAAC,IAAK,4CAA6C,IAAI,CAACL,OAAO,CAACM,YAAY,CAAC,CAAC,CAAC;IAC3J,IAAI,CAACC,eAAe,GAAG,IAAI,CAACJ,SAAS,CAACK,GAAG,CAAEL,SAAS,IAAK,mCAAoCA,SAAS,KAAK,CAAC,CAAC;IAC7G,IAAI,CAACM,aAAa,GAAGlB,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAACS,OAAO,CAACU,gBAAgB,EAAGL,CAAC,IAAK,2CAA4C,IAAI,CAACL,OAAO,CAACW,QAAQ,CAAC,CAAC,CAAC;IACzJ,IAAI,CAACC,0BAA0B,GAAGnB,yBAAyB,CAAC,sBAAsB,EAAE,IAAI,CAACO,OAAO,CAACa,oBAAoB,CAAC;IACtH,IAAI,CAACC,4BAA4B,GAAGrB,yBAAyB,CAAC,wBAAwB,EAAE,IAAI,CAACO,OAAO,CAACe,sBAAsB,CAAC;IAC5H,IAAI,CAACC,kBAAkB,GAAGxB,gBAAgB,CAAC,oBAAoB,CAAC;IAChE,IAAI,CAACyB,KAAK,GAAG,IAAIC,GAAG,CAAC,CAAC;IACtB,IAAI,CAACjB,QAAQ,CAACkB,SAAS,GAAG,sBAAsB;IAChD,MAAMC,gBAAgB,GAAG,IAAI,CAACnB,QAAQ,CAACoB,WAAW,CAACnC,CAAC,CAAC,uBAAuB,EAAE;MAAEoC,IAAI,EAAE,cAAc;MAAEC,UAAU,EAAE,MAAM;MAAEC,KAAK,EAAE;QAAEC,KAAK,EAAE;MAAO;IAAE,CAAC,CAAC,CAChJC,IAAI,CAAC;IACV,MAAMC,CAAC,GAAG,IAAIC,cAAc,CAAC,MAAM;MAC/BjC,WAAW,CAACkC,EAAE,IAAI;QACd;QACA,IAAI,CAACb,kBAAkB,CAACc,OAAO,CAACD,EAAE,CAAC;MACvC,CAAC,CAAC;IACN,CAAC,CAAC;IACFF,CAAC,CAACI,OAAO,CAAC,IAAI,CAAC9B,QAAQ,CAAC;IACxB,IAAI,CAAC+B,SAAS,CAAC3C,YAAY,CAAC,MAAMsC,CAAC,CAACM,UAAU,CAAC,CAAC,CAAC,CAAC;IAClD,IAAI,CAACD,SAAS,CAAC1C,OAAO,CAAC4C,MAAM,IAAI;MAC7B;MACAd,gBAAgB,CAACD,SAAS,GAAG,IAAI,CAACZ,eAAe,CAAC4B,IAAI,CAACD,MAAM,CAAC,GAAG,EAAE,GAAG,mBAAmB;IAC7F,CAAC,CAAC,CAAC;IACH,IAAI,CAACF,SAAS,CAAC1C,OAAO,CAAC4C,MAAM,IAAI,uCAAwC,IAAI,CAACE,MAAM,CAACF,MAAM,CAAC,CAAC,CAAC;EAClG;EACAG,OAAOA,CAAA,EAAG;IACN,KAAK,CAACA,OAAO,CAAC,CAAC;IACflD,KAAK,CAAC,IAAI,CAACc,QAAQ,CAAC;EACxB;EACAmC,MAAMA,CAACF,MAAM,EAAE;IACX,IAAI,CAAC,IAAI,CAACzB,aAAa,CAAC0B,IAAI,CAACD,MAAM,CAAC,EAAE;MAClC;IACJ;IACA,IAAI,CAAClB,kBAAkB,CAACmB,IAAI,CAACD,MAAM,CAAC;IACpC,IAAI,CAACtB,0BAA0B,CAACuB,IAAI,CAACD,MAAM,CAAC;IAC5C,IAAI,CAACpB,4BAA4B,CAACqB,IAAI,CAACD,MAAM,CAAC;IAC9C,MAAM/B,SAAS,GAAG,IAAI,CAACA,SAAS,CAACgC,IAAI,CAACD,MAAM,CAAC;IAC7C,MAAMI,aAAa,GAAG,IAAI,CAACtC,OAAO,CAACuC,gBAAgB,CAAC,CAAC;IACrD,MAAMC,SAAS,GAAG,IAAIC,GAAG,CAAC,IAAI,CAACxB,KAAK,CAACyB,IAAI,CAAC,CAAC,CAAC;IAC5C,MAAMC,SAAS,GAAG9C,WAAW,CAAC+C,gBAAgB,CAAC,CAAC,EAAE,IAAI,CAAC3C,QAAQ,CAAC4C,YAAY,CAAC;IAC7E,IAAI,CAACF,SAAS,CAACG,OAAO,EAAE;MACpB,KAAK,MAAMC,YAAY,IAAIT,aAAa,EAAE;QACtC,MAAMU,aAAa,GAAG,IAAIpD,SAAS,CAACmD,YAAY,CAACE,eAAe,EAAEF,YAAY,CAACG,aAAa,GAAG,CAAC,CAAC;QACjG,MAAMC,WAAW,GAAG,IAAI,CAACjD,YAAY,CAACkD,0BAA0B,CAACJ,aAAa,EAAEd,MAAM,CAAC;QACvFvC,WAAW,CAACkC,EAAE,IAAI;UACd;UACA,KAAK,MAAMwB,UAAU,IAAIF,WAAW,EAAE;YAClC,IAAI,CAACE,UAAU,CAACC,KAAK,CAACC,SAAS,CAACP,aAAa,CAAC,EAAE;cAC5C;YACJ;YACAR,SAAS,CAACgB,MAAM,CAACH,UAAU,CAACI,EAAE,CAAC;YAC/B,IAAIC,IAAI,GAAG,IAAI,CAACzC,KAAK,CAAC0C,GAAG,CAACN,UAAU,CAACI,EAAE,CAAC;YACxC,IAAI,CAACC,IAAI,EAAE;cACP,MAAME,WAAW,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;cACjD,IAAI,CAAC7D,QAAQ,CAACoB,WAAW,CAACuC,WAAW,CAAC;cACtC,MAAMG,aAAa,GAAGrE,eAAe,CAAC,MAAM,EAAE2D,UAAU,CAAC;cACzD,MAAMW,QAAQ,GAAG,IAAI,CAAC9D,YAAY,CAAC+D,UAAU,CAACF,aAAa,EAAEH,WAAW,CAAC;cACzEF,IAAI,GAAG,IAAIQ,qBAAqB,CAACH,aAAa,EAAEC,QAAQ,EAAEJ,WAAW,CAAC;cACtE,IAAI,CAAC3C,KAAK,CAACkD,GAAG,CAACd,UAAU,CAACI,EAAE,EAAEC,IAAI,CAAC;YACvC,CAAC,MACI;cACDA,IAAI,CAACU,IAAI,CAACD,GAAG,CAACd,UAAU,EAAExB,EAAE,CAAC;YACjC;YACA,MAAMwC,GAAG,GAAGhB,UAAU,CAACC,KAAK,CAACL,eAAe,IAAI,IAAI,CAACjD,OAAO,CAACsE,QAAQ,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC,GAChF,IAAI,CAACvE,OAAO,CAACwE,mBAAmB,CAACnB,UAAU,CAACC,KAAK,CAACL,eAAe,EAAE,IAAI,CAAC,GAAG9C,SAAS,GACpF,IAAI,CAACH,OAAO,CAACyE,sBAAsB,CAACpB,UAAU,CAACC,KAAK,CAACL,eAAe,GAAG,CAAC,EAAE,KAAK,CAAC,GAAG9C,SAAS;YAClG,MAAMuE,MAAM,GAAGrB,UAAU,CAACC,KAAK,CAACqB,sBAAsB,KAAK,CAAC,GACxDC,IAAI,CAACC,GAAG,CAACR,GAAG,EAAE,IAAI,CAACrE,OAAO,CAACwE,mBAAmB,CAACnB,UAAU,CAACC,KAAK,CAACL,eAAe,EAAE,KAAK,CAAC,GAAG9C,SAAS,CAAC,GAClGyE,IAAI,CAACC,GAAG,CAACR,GAAG,EAAE,IAAI,CAACrE,OAAO,CAACyE,sBAAsB,CAACpB,UAAU,CAACC,KAAK,CAACqB,sBAAsB,GAAG,CAAC,EAAE,IAAI,CAAC,GAAGxE,SAAS,CAAC;YACvH,MAAM2E,MAAM,GAAGJ,MAAM,GAAGL,GAAG;YAC3BX,IAAI,CAACqB,OAAO,CAACvD,KAAK,CAAC6C,GAAG,GAAG,GAAGA,GAAG,IAAI;YACnCX,IAAI,CAACqB,OAAO,CAACvD,KAAK,CAACsD,MAAM,GAAG,GAAGA,MAAM,IAAI;YACzCpB,IAAI,CAACsB,cAAc,CAACC,MAAM,CAACpF,WAAW,CAAC+C,gBAAgB,CAACyB,GAAG,EAAES,MAAM,CAAC,EAAEnC,SAAS,CAAC;UACpF;QACJ,CAAC,CAAC;MACN;IACJ;IACA,KAAK,MAAMc,EAAE,IAAIjB,SAAS,EAAE;MACxB,MAAMkB,IAAI,GAAG,IAAI,CAACzC,KAAK,CAAC0C,GAAG,CAACF,EAAE,CAAC;MAC/BC,IAAI,CAACsB,cAAc,CAAC3C,OAAO,CAAC,CAAC;MAC7BqB,IAAI,CAACqB,OAAO,CAACG,MAAM,CAAC,CAAC;MACrB,IAAI,CAACjE,KAAK,CAACuC,MAAM,CAACC,EAAE,CAAC;IACzB;EACJ;AACJ;AACA,MAAMS,qBAAqB,CAAC;EACxBnE,WAAWA,CAACqE,IAAI,EAAEY,cAAc,EAAED,OAAO,EAAE;IACvC,IAAI,CAACX,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACY,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACD,OAAO,GAAGA,OAAO;EAC1B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}