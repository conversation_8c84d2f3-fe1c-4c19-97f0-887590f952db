{"ast": null, "code": "import { RouterOutlet } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nexport let AppComponent = /*#__PURE__*/(() => {\n  class AppComponent {\n    title = 'marketing';\n    static ɵfac = function AppComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AppComponent)();\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      decls: 3,\n      vars: 0,\n      consts: [[1, \"marketing-app\"], [1, \"app-content\"]],\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"main\", 1);\n          i0.ɵɵelement(2, \"router-outlet\");\n          i0.ɵɵelementEnd()();\n        }\n      },\n      dependencies: [RouterOutlet],\n      styles: [\".marketing-app[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.app-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  color: white;\\n  padding: 1rem 2rem;\\n  text-align: center;\\n}\\n\\n.app-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 2rem;\\n  font-weight: 300;\\n}\\n\\n.app-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 2rem;\\n  background-color: #f8f9fa;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3Byb2plY3RzL21hcmtldGluZy9zcmMvYXBwL2FwcC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGlCQUFBO0VBQ0EsYUFBQTtFQUNBLHNCQUFBO0FBQ0Y7O0FBRUE7RUFDRSw2REFBQTtFQUNBLFlBQUE7RUFDQSxrQkFBQTtFQUNBLGtCQUFBO0FBQ0Y7O0FBRUE7RUFDRSxTQUFBO0VBQ0EsZUFBQTtFQUNBLGdCQUFBO0FBQ0Y7O0FBRUE7RUFDRSxPQUFBO0VBQ0EsYUFBQTtFQUNBLHlCQUFBO0FBQ0YiLCJzb3VyY2VzQ29udGVudCI6WyIubWFya2V0aW5nLWFwcCB7XHJcbiAgbWluLWhlaWdodDogMTAwdmg7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG59XHJcblxyXG4uYXBwLWhlYWRlciB7XHJcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzY2N2VlYSAwJSwgIzc2NGJhMiAxMDAlKTtcclxuICBjb2xvcjogd2hpdGU7XHJcbiAgcGFkZGluZzogMXJlbSAycmVtO1xyXG4gIHRleHQtYWxpZ246IGNlbnRlcjtcclxufVxyXG5cclxuLmFwcC1oZWFkZXIgaDEge1xyXG4gIG1hcmdpbjogMDtcclxuICBmb250LXNpemU6IDJyZW07XHJcbiAgZm9udC13ZWlnaHQ6IDMwMDtcclxufVxyXG5cclxuLmFwcC1jb250ZW50IHtcclxuICBmbGV4OiAxO1xyXG4gIHBhZGRpbmc6IDJyZW07XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogI2Y4ZjlmYTtcclxufSAiXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n  return AppComponent;\n})();", "map": {"version": 3, "names": ["RouterOutlet", "AppComponent", "title", "selectors", "decls", "vars", "consts", "template", "AppComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\marketing\\src\\app\\app.component.ts", "C:\\console\\aava-ui-web\\projects\\marketing\\src\\app\\app.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { RouterOutlet } from '@angular/router';\r\n\r\n@Component({\r\n  selector: 'app-root',\r\n  standalone: true,\r\n  imports: [RouterOutlet],\r\n  templateUrl: './app.component.html',\r\n  styleUrls: ['./app.component.scss']\r\n})\r\nexport class AppComponent {\r\n  title = 'marketing';\r\n} ", "<div class=\"marketing-app\">\r\n  <main class=\"app-content\">\r\n    <router-outlet></router-outlet>\r\n  </main>\r\n</div> "], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;;AAS9C,WAAaC,YAAY;EAAnB,MAAOA,YAAY;IACvBC,KAAK,GAAG,WAAW;;uCADRD,YAAY;IAAA;;YAAZA,YAAY;MAAAE,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCTvBE,EADF,CAAAC,cAAA,aAA2B,cACC;UACxBD,EAAA,CAAAE,SAAA,oBAA+B;UAEnCF,EADE,CAAAG,YAAA,EAAO,EACH;;;qBDEMd,YAAY;MAAAe,MAAA;IAAA;;SAIXd,YAAY;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}