import { Component, CUSTOM_ELEMENTS_SCHEMA, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DecimalPipe } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  FormControl,
  FormsModule,
  Validators,
} from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import knowledgeBaseLabels from '../constants/knowledge-base.json';
import {
  SliderComponent,
  DropdownComponent,
  AvaTextboxComponent,
  AvaTextareaComponent,
  FileUploadComponent,
  ButtonComponent,
  TableComponent,
  DialogService
} from '@ava/play-comp-library';
import { KnowledgeBaseService } from '@shared/services/knowledge-base.service';
import { PopupComponent } from '@ava/play-comp-library';
import { LucideAngularModule } from 'lucide-angular';
import { map } from 'rxjs/internal/operators/map';

@Component({
  selector: 'app-create-knowledge-base',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    LucideAngularModule,
    DropdownComponent,
    AvaTextboxComponent,
    AvaTextareaComponent,
    SliderComponent,
    FileUploadComponent,
    PopupComponent,
    ButtonComponent,
    TableComponent,
  ],
  providers: [DecimalPipe],
  templateUrl: './create-knowledge-base.component.html',
  styleUrls: ['./create-knowledge-base.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class CreateKnowledgeBaseComponent implements OnInit {
  // Form group for the knowledge base creation form
  knowledgeBaseForm: FormGroup;

  // Stores route param for edit mode
  knowledgeBaseId: string | null = null;

  // Popup & submission state
  submissionMessage: string | null = null;
  submissionSuccess = false;
  showInfoPopup = false;
  fileUploadRequired = false;

  // UI mode tracking
  isEditMode = false;

  //maxFile size is 15MB 
  maxFileSize = 15 * 1024 * 1024;

  // Allowed file formats for upload
  allowedFormats: string[] = knowledgeBaseLabels.allowedFormats;

  // UI text constants
  componentTitle: string = 'Upload File Here';
  showUploadButton = true;
  showDialogCloseIcon = false;

  // Selected options
  selectedRetriever = knowledgeBaseLabels.labels.default;
  selectedUploadType = 'upload-files';
  uploadPlaceholder = 'Upload Files';
  iconName = 'circle-check';
  iconColor = 'red';

  // Model search input text (currently unused)
  searchModelText = '';

  // Uploaded files
  uploadedFiles: File[] = [];

  hasZeroKbFile = false;

  // Split size fields
  splitSize: number = 5000;
  parentSplitSize: number = 5000;
  childSplitSize: number = 2000;

  // Dropdown data
  embeddingModelOptions: { name: string; value: string }[] = [];
  schemeOptions: { name: string; value: string }[] = [];

  uploadOptions: any[] = [];

  // Label map
  kbLabels = knowledgeBaseLabels.labels;

  // Retriever options for toggle
  retrieverOptions: string[] = [this.kbLabels.default, this.kbLabels.parentDoc];

  // Submit button label (Save / Update)
  saveOrUpdateLabel: string = this.kbLabels.save;

  // Controls required per upload type
  private selectedControls: Record<string, string[]> = {
    'upload-files': [],
    'Azure Blob': knowledgeBaseLabels.azureBlob,
    'GitHub': knowledgeBaseLabels.github,
    'Share Point': knowledgeBaseLabels.sharePoint,
    'Confluence Wiki': knowledgeBaseLabels.confluenceWiki,
    'Database': knowledgeBaseLabels.database
  };

  // API path suffix map based on upload source
  private pathSuffix: Record<string, string> = {
    'Azure Blob': '/blob',
    'GitHub': '/github',
    'Share Point': '/sharepoint',
    'Confluence Wiki': '/confluence',
    'Database': '/database',
  };

  tableColumns = [
    { key: 'fileName', label: 'File Name', type: 'text' },
    { key: 'fileSizeFormatted', label: 'File Size', type: 'text' },
    { key: 'uploadDateFormatted', label: 'Upload Date', type: 'text' },
    { key: 'retrieverType', label: 'Retriever Type', type: 'text' },
  ];

  tableData: any[] = [];

  isLeftCollapsed = false;

  toggleLeftPanel() {
    this.isLeftCollapsed = !this.isLeftCollapsed;
  }

  // Constructor injecting services and initializing form group
  constructor(
    private fb: FormBuilder,
    private router: Router,
    public _decimalPipe: DecimalPipe,
    private knowledgeBaseService: KnowledgeBaseService,
    private route: ActivatedRoute,
    private dialogService: DialogService,
  ) {
    // Define form controls and validators
    this.knowledgeBaseForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(3)]],
      description: ['', Validators.required],
      retriever: [this.kbLabels.default],
      splitSize: [5000],
      parentSplitSize: [5000],
      childSplitSize: [2000],
      embeddingModel: [null, Validators.required],
      uploadType: ['', Validators.required],
      containerName: [''],
      accountName: [''],
      accountKey: [''],
      githubKey: [''],
      githubAccount: [''],
      githubRepo: [''],
      githubBranch: [''],
      tenantId: [''],
      sharepointSiteName: [''],
      sharepointFolderPath: [''],
      email: [''],
      clientId: [''],
      clientSecret: [''],
      apiToken: [''],
      baseUrl: [''],
      spaceKey: [''],
      overlap: [''],
      scheme: [''],
      host: [''],
      port: [''],
      user: [''],
      password: [''],
      dbname: [''],
      query: [''],
    });
  }

  // Lifecycle hook: on component init
  ngOnInit(): void {
    this.loadEmbeddingModelOptions();
    this.loadUploadOptionsAndSetDefault();
    this.loadSchemeDropdownOptions();
    // Check if editing an existing knowledge base
    this.knowledgeBaseId = this.route.snapshot.paramMap.get('id');
    this.isEditMode = !!this.knowledgeBaseId;
    if (this.isEditMode) {
      this.loadEditModeData();
    }

    this.saveOrUpdateLabel = this.isEditMode
      ? this.kbLabels.update
      : this.kbLabels.save;
  }

  // Fetch available embedding models
  loadEmbeddingModelOptions(): void {
    this.knowledgeBaseService
      .getEmbeddingModelOptions()
      .subscribe({
        next: (options) => {
          this.embeddingModelOptions = options.map((opt: any) => ({
            name: opt.modelDeploymentName,
            value: String(opt.id),
          }));
        },
        error: (err) => {
          this.dialogService.error({
            title: 'Loading Failed',
            message: 'Failed to load embedding models. Please refresh the page.',
            showRetryButton: true,
            retryButtonText: 'Retry'
          }).then(result => {
            if (result.action === 'retry') {
              this.loadEmbeddingModelOptions();
            }
          });
        }
      });
  }


  // Scheme Dropdown values
  loadSchemeDropdownOptions(): void {
    this.knowledgeBaseService.getSchemeDropdowns().subscribe({
      next: (response) => {
        if (response && response.value) {
          try {
          const parsedValue = JSON.parse(response.value); // parse string to object
            this.schemeOptions = Object.keys(parsedValue).map((key) => ({
              name: key,
              value: parsedValue[key]
            }));
          } catch (error) {
            console.error('Failed to parse scheme options:', error);
            this.schemeOptions = [];
          }
        } else {
          this.schemeOptions = [];
        }
      },
      error: (err) => {
        this.dialogService.error({
          title: 'Loading Failed',
          message: 'Failed to load scheme options. Please refresh the page.',
          showRetryButton: true,
          retryButtonText: 'Retry'
        }).then(result => {
          if (result.action === 'retry') {
            this.loadSchemeDropdownOptions();
          }
        });
      }
    });
  }

  // Triggered when upload type changes
  onUploadTypeChange(event: any): void {
    const selected = event?.selectedOptions?.[0]?.value;
    this.selectedUploadType = selected || '';
    this.uploadPlaceholder = '';

    if (this.selectedUploadType !== 'upload-files') {
      this.uploadedFiles = [];
    }
    // Clear all old validators AND values
    Object.values(this.selectedControls)
      .flat()
      .forEach((ctrlName) => {
        const ctrl = this.getControl(ctrlName);
        if (ctrl) {
          ctrl.clearValidators();
          ctrl.setValue(null); // or ''
          ctrl.markAsPristine();
          ctrl.markAsUntouched();
        }
      });


    // Apply required validators to new controls
    this.selectedControls[this.selectedUploadType]?.forEach((ctrl) => {
      this.getControl(ctrl)?.setValidators(Validators.required);
    });

    // Reapply validator to uploadType itself
    this.getControl('uploadType')?.setValidators(Validators.required);

    // Mark uploadType as interacted
    this.getControl('uploadType')?.markAsTouched();
    this.getControl('uploadType')?.markAsDirty();

    // Recalculate form status
    this.knowledgeBaseForm.updateValueAndValidity();
  }

  // Submit form handler
  onSave(): void {
    // Remove popup state resets
    // this.submissionSuccess = false;
    this.fileUploadRequired = false;

    // Basic form validation
    if (this.knowledgeBaseForm.invalid && this.uploadedFiles.length === 0) {
      this.knowledgeBaseForm.markAllAsTouched();
      this.fileUploadRequired = true;
      return;
    }

    const type = this.knowledgeBaseForm.value.uploadType;
    const suffix = this.pathSuffix[type] || '';
    const retrieverType =
      this.selectedRetriever === this.kbLabels.parentDoc
        ? 'parent_doc_retriever'
        : 'normal';

    const knowledgeBase = this.getControl('name').value;
    const description = this.getControl('description').value;
    const splitSize = this.getControl('splitSize').value;
    const parentSplitSize = this.getControl('parentSplitSize')?.value;
    const childSplitSize = this.getControl('childSplitSize')?.value;

    // Get selected model ID
    const selectedModelValue = this.knowledgeBaseForm.value['embeddingModel'];
    const selectedModel = this.embeddingModelOptions.find(
      (opt) =>
        opt.name === selectedModelValue || opt.value === selectedModelValue,
    );
    const selectedModelId = selectedModel ? selectedModel.value : null;

    // Construct base payload
    const payload: Record<string, any> = {};

    if (this.isEditMode && this.knowledgeBaseId) {
      // Only send masterId during edit
      payload['masterId'] = this.knowledgeBaseId;
    } else {
      // Full payload during create
      payload['knowledgeBase'] = knowledgeBase;
      payload['description'] = description;
      payload['model-ref'] = selectedModelId;
      payload['type'] = retrieverType;

      if (retrieverType === 'parent_doc_retriever') {
        payload['parentSplitSize'] = parentSplitSize;
        payload['splitSize'] = childSplitSize;
      } else {
        payload['splitSize'] = splitSize;
      }
    }

    // Show loading dialog
    this.dialogService.loading({
      title: this.isEditMode ? 'Updating Knowledge Base...' : 'Creating Knowledge Base...',
      message: 'Please wait while we process your request.',
      showProgress: false,
      showCancelButton: false
    });

    // Handle file upload case
    if (type === 'upload-files' || type == 'Upload Files') {
      const file = this.uploadedFiles;
      if (!file) return;

      const formData = new FormData();
      this.uploadedFiles.forEach((file: File) => {
        formData.append('files', file);
      });

      // Submit form with files
      this.knowledgeBaseService
        .submitUpload(formData, payload, suffix)
        .subscribe({
          next: (info) => {
            this.dialogService.close(); // Close loading dialog
            this.dialogService.success({
              title: 'Success!',
              message: info?.info?.message || info.message || 'Knowledge Base processed successfully.'
            }).then(() => {
              this.router.navigate(['/libraries/knowledge-base']);
            });
          },
          error: (err) => {
            this.dialogService.close(); // Close loading dialog
            this.dialogService.error({
              title: 'Operation Failed',
              message: err?.error?.message || err.message || 'Knowledge Base creation or update failed. Please try again.',
              showRetryButton: true,
              retryButtonText: 'Retry'
            }).then(result => {
              if (result.action === 'retry') {
                this.onSave();
              }
            });
          },
        });

      // Handle non-file (external source) upload case
    } else {
      const data: Record<string, any> = {};

      // Add selected controls to both body and query params

      this.selectedControls[type]?.forEach((key) => {
        const value = this.getControl(key)?.value;
        data[key] = value;

        // If edit mode, also include these as query params (i.e., in payload)
        if (this.isEditMode) {
          payload[key] = value;
        }
      });

      // Also add masterId for edit mode

      this.knowledgeBaseService.submitUpload(data, payload, suffix).subscribe({
        next: (info) => {
          this.dialogService.close(); // Close loading dialog
          this.dialogService.success({
            title: 'Success!',
            message: info?.info?.message || info.message || 'Knowledge Base processed successfully.'
          }).then(() => {
            this.router.navigate(['/libraries/knowledge-base']);
          });
        },
        error: (err) => {
          this.dialogService.close(); // Close loading dialog
          this.dialogService.error({
            title: 'Operation Failed',
            message: err?.error?.message || err.message || 'Knowledge Base creation or update failed. Please try again.',
            showRetryButton: true,
            retryButtonText: 'Retry'
          }).then(result => {
            if (result.action === 'retry') {
              this.onSave();
            }
          });
        },
      });
    }
  }

  // Get a typed form control
  getControl(name: string): FormControl {
    return this.knowledgeBaseForm.get(name) as FormControl;
  }

  // Utility to return readable field validation error
  getFieldError(fieldName: string): string {
    const field = this.knowledgeBaseForm.get(fieldName);
    // Capitalize only if first letter is not already uppercase
    const formattedFieldName = /^[A-Z]/.test(fieldName)
      ? fieldName
      : fieldName.charAt(0).toUpperCase() + fieldName.slice(1);
    if (field && field.invalid && (field.touched || field.dirty)) {
      if (field.errors?.['required']) {
        return `${formattedFieldName} is required`;
      }
      if (field.errors?.['email']) {
        return 'Please enter a valid email address';
      }
      if (field.errors?.['minlength']) {
        return `${formattedFieldName} must be at least ${field.errors['minlength'].requiredLength} characters long`;
      }
    } else if (fieldName == 'scheme' || fieldName == 'embeddingModel') {
      if (field && field.errors?.['required']) {
        return `${formattedFieldName} is required`;
      }
    }
    return '';
  }

  // Navigate back to listing
  onExit(): void {
    this.router.navigate(['/libraries/knowledge-base']);
  }

  // Load upload options and set first one as default
  loadUploadOptionsAndSetDefault(): void {
    this.knowledgeBaseService.getUploadDropdowns().subscribe({
      next: (res) => {
        if (res?.value) {
          this.uploadOptions = this.getUploadTypeOptions(res.value);

          // Safely assign default value and apply
          const firstOption = this.uploadOptions[0];
          if (firstOption && firstOption.value) {
            const defaultValue = firstOption.value;

            // Set form control
            this.knowledgeBaseForm.get('uploadType')?.setValue(defaultValue);

            // Set component state
            this.selectedUploadType = defaultValue;

            // Trigger validators and placeholder update
            this.onUploadTypeChange({
              selectedOptions: [{ value: defaultValue }],
            });
          }
        }
      },
      error: (err) => {
        this.dialogService.error({
          title: 'Loading Failed',
          message: 'Failed to load upload options. Please refresh the page.',
          showRetryButton: true,
          retryButtonText: 'Retry'
        }).then(result => {
          if (result.action === 'retry') {
            this.loadUploadOptionsAndSetDefault();
          }
        });
      },
    });
  }

  // Converts string labels into slug format
  getUploadTypeOptions(rawValue: string): { name: string; value: string }[] {
    const parsed = JSON.parse(rawValue);
    return Object.keys(parsed).map((label) => ({
      name: label,
      value: this.toSlug(label),
    }));
  }

  // Utility for slug formatting
  toSlug(label: string): string {
    return label.toLowerCase().replace(/\s+/g, '-');
  }

  // Handle retriever mode toggle
  selectRetriever(retriever: string): void {
    this.selectedRetriever = retriever;
    this.knowledgeBaseForm.get('retriever')?.setValue(retriever);
  }

  onParentSplitSizeChange(event: any): void {
    this.parentSplitSize = event;
    this.knowledgeBaseForm
      .get('parentSplitSize')
      ?.setValue(this.parentSplitSize);
  }

  onChildSplitSizeChange(event: any): void {
    this.childSplitSize = event;
    this.knowledgeBaseForm.get('childSplitSize')?.setValue(this.childSplitSize);
  }

  // splitSize input setter
  splitSizeChange(event: any) {
    this.splitSize = event;
    this.knowledgeBaseForm
      .get('splitSize')
      ?.setValue(this.splitSize);
  }

  
  // Update file list after change
  filesListChanged(file: File[]) {
    this.uploadedFiles = [...file];
    this.hasZeroKbFile = this.uploadedFiles.some(file => file.size === 0);
  }

  // Disable submit based on form or file state
  isSubmitDisabled(): boolean {
    const isFormInvalid = this.knowledgeBaseForm.invalid;
    const isUploadTypeFile = this.selectedUploadType === 'upload-files';
    const isFileEmpty = this.uploadedFiles.length === 0;
    const isParentDoc = this.selectedRetriever === this.kbLabels.parentDoc;

    const splitSize = Number(this.knowledgeBaseForm.get('splitSize')?.value);
    const parentSplitSize = Number(this.knowledgeBaseForm.get('parentSplitSize')?.value);
    const childSplitSize = Number(this.knowledgeBaseForm.get('childSplitSize')?.value);

    //  Normal condition: splitSize must be at least 100
    const isNormalSplitSizeInvalid =
      !isParentDoc && splitSize < 100;

    //  Parent-child condition:
    // 1. parent must be > child
    // 2. both must be >= 100
    const isParentChildInvalid =
      isParentDoc &&
      (parentSplitSize <= childSplitSize ||
        parentSplitSize < 100 ||
        childSplitSize < 100);

    return (
      isFormInvalid ||
      (isUploadTypeFile && isFileEmpty) ||
      isParentChildInvalid ||
      isNormalSplitSizeInvalid ||
      this.hasZeroKbFile
    );
  }


  // Close popup and navigate on success
  handlePopupClose(): void {
    this.showInfoPopup = false;
    if (this.submissionSuccess) {
      this.router.navigate(['/libraries/knowledge-base']);
    }
  }

  // Format file size from bytes to MB string
  formatDocumentSize(size: number): string {
    if (size < 102400) {
      return this._decimalPipe.transform(size / 1024, '1.0-2') + ' KB';
    } else {
      return this._decimalPipe.transform(size / 1048576, '1.2-2') + ' MB';
    }
  }

  // Format rows
  formatRows(row: number) {
    return row > 1 ? `${row} rows` : `${row} row`;
  }
  
  // Format date to dd/mm/yy, hh:mm format
  formatUploadDate(dateStr: string): string {
    const date = new Date(dateStr);
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = String(date.getFullYear()).slice(-2);
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return `${day}/${month}/${year}, ${hours}:${minutes}`;
  }

  // Returns a user-friendly label for the given retriever type
  getRetrieverLabel(type: string): string {
    const map: Record<string, string> = {
      normal: 'Default',
      parent_doc_retriever: 'Parent Doc',
    };
    this.selectedRetriever = map[type];
    return map[type] || 'Unknown';
  }

  private loadEditModeData(): void {
    const kbId = Number(this.knowledgeBaseId);

    // Fetch files and other details
    this.knowledgeBaseService.getKnowledgeBaseById(kbId).subscribe({
      next: (response) => {
        if (response?.files?.length > 0) {
          const retrieverType = response.retrieverType || 'normal';
          this.tableData = response.files.map((file: any) => ({
            fileName: file.fileName,
            fileSizeFormatted: file.source === 'database'
              ? this.formatRows(file.fileSizeBytes)
              : this.formatDocumentSize(file.fileSizeBytes),
            uploadDateFormatted: this.formatUploadDate(file.uploadDate),
            retrieverType: this.getRetrieverLabel(retrieverType)
          }));

          // Disable fields in edit mode
          ['name', 'description', 'splitSize', 'embeddingModel'].forEach(field =>
            this.knowledgeBaseForm.get(field)?.disable()
          );
        } else {
          this.tableData = [];
        }
      },
      error: (err) => {
        console.error('Error fetching knowledge base data', err);
        this.dialogService.error({
          title: 'Loading Failed',
          message: 'Failed to load knowledge base data. Please try again.',
          showRetryButton: true,
          retryButtonText: 'Retry'
        }).then(result => {
          if (result.action === 'retry') {
            this.loadEditModeData();
          }
        });
        this.tableData = [];
      }
    });

    // Fetch and patch name/description
    this.knowledgeBaseService.fetchAllKnowledge()
      .pipe(
        map((response: any[]) => {
          const match = response.find(item => item.id === kbId);
          if (match) {
            this.knowledgeBaseForm.patchValue({
              name: match.collectionName,
              description: match.description,
            });
          }
          return response;
        })
      )
      .subscribe({
        next: () => {
          // Successfully patched the form
        },
        error: (err) => {
          this.dialogService.error({
            title: 'Loading Failed',
            message: 'Failed to load knowledge base details.',
            showRetryButton: false
          });
        }
      });
  }

  onSliderInputChange(event: any, controlName: string): void {
    let inputValue: number;

    // Handle both native event and direct value
    if (event?.target) {
      inputValue = (event.target as HTMLInputElement).valueAsNumber;
    } else if (typeof event === 'number') {
      inputValue = event;
    } else {
      inputValue = Number(event);
    }

    // Cap to max value (you can also parametrize this if needed)
    const cappedValue = Math.min(inputValue, 20000);

    // Update local variable based on controlName
    if (controlName === 'parentSplitSize') {
      this.parentSplitSize = cappedValue;
    } else if (controlName === 'childSplitSize') {
      this.childSplitSize = cappedValue;
    } else if (controlName === 'splitSize') {
      this.splitSize = cappedValue;
    }
    // Update the corresponding form control
    this.knowledgeBaseForm.get(controlName)?.setValue(cappedValue);
  }

  confirmSave(): void {
    this.dialogService.confirmation({
      title: 'Save Knowledge Base?',
      message: 'Are you sure you want to save this knowledge base?',
      confirmButtonText: 'Save',
      cancelButtonText: 'Cancel',
      confirmButtonVariant: 'primary',
      icon:'save'
    }).then(result => {
      if (result.confirmed) {
        this.onSave();
      }
    });
  }

  confirmUpdate(): void {
    this.dialogService.confirmation({
      title: 'Update Knowledge Base?',
      message: 'Are you sure you want to update this knowledge base?',
      confirmButtonText: 'Update',
      cancelButtonText: 'Cancel',
      confirmButtonVariant: 'primary',
      icon: 'square-pen'
    }).then(result => {
      if (result.confirmed) {
        this.onSave();
      }
    });
  }
}
