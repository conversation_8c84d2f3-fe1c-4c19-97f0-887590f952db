{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = this && this.__param || function (paramIndex, decorator) {\n  return function (target, key) {\n    decorator(target, key, paramIndex);\n  };\n};\nimport { addDisposableListener, getWindow } from '../../../base/browser/dom.js';\nimport { StandardMouseEvent } from '../../../base/browser/mouseEvent.js';\nimport { ToggleMenuAction, ToolBar } from '../../../base/browser/ui/toolbar/toolbar.js';\nimport { Separator, toAction } from '../../../base/common/actions.js';\nimport { coalesceInPlace } from '../../../base/common/arrays.js';\nimport { intersection } from '../../../base/common/collections.js';\nimport { BugIndicatingError } from '../../../base/common/errors.js';\nimport { Emitter } from '../../../base/common/event.js';\nimport { Iterable } from '../../../base/common/iterator.js';\nimport { DisposableStore } from '../../../base/common/lifecycle.js';\nimport { localize } from '../../../nls.js';\nimport { createAndFillInActionBarActions } from './menuEntryActionViewItem.js';\nimport { IMenuService, MenuItemAction, SubmenuItemAction } from '../common/actions.js';\nimport { createConfigureKeybindingAction } from '../common/menuService.js';\nimport { ICommandService } from '../../commands/common/commands.js';\nimport { IContextKeyService } from '../../contextkey/common/contextkey.js';\nimport { IContextMenuService } from '../../contextview/browser/contextView.js';\nimport { IKeybindingService } from '../../keybinding/common/keybinding.js';\nimport { ITelemetryService } from '../../telemetry/common/telemetry.js';\n/**\n * The `WorkbenchToolBar` does\n * - support hiding of menu items\n * - lookup keybindings for each actions automatically\n * - send `workbenchActionExecuted`-events for each action\n *\n * See {@link MenuWorkbenchToolBar} for a toolbar that is backed by a menu.\n */\nlet WorkbenchToolBar = class WorkbenchToolBar extends ToolBar {\n  constructor(container, _options, _menuService, _contextKeyService, _contextMenuService, _keybindingService, _commandService, telemetryService) {\n    super(container, _contextMenuService, {\n      // defaults\n      getKeyBinding: action => _keybindingService.lookupKeybinding(action.id) ?? undefined,\n      // options (override defaults)\n      ..._options,\n      // mandatory (overide options)\n      allowContextMenu: true,\n      skipTelemetry: typeof _options?.telemetrySource === 'string'\n    });\n    this._options = _options;\n    this._menuService = _menuService;\n    this._contextKeyService = _contextKeyService;\n    this._contextMenuService = _contextMenuService;\n    this._keybindingService = _keybindingService;\n    this._commandService = _commandService;\n    this._sessionDisposables = this._store.add(new DisposableStore());\n    // telemetry logic\n    const telemetrySource = _options?.telemetrySource;\n    if (telemetrySource) {\n      this._store.add(this.actionBar.onDidRun(e => telemetryService.publicLog2('workbenchActionExecuted', {\n        id: e.action.id,\n        from: telemetrySource\n      })));\n    }\n  }\n  setActions(_primary, _secondary = [], menuIds) {\n    this._sessionDisposables.clear();\n    const primary = _primary.slice(); // for hiding and overflow we set some items to undefined\n    const secondary = _secondary.slice();\n    const toggleActions = [];\n    let toggleActionsCheckedCount = 0;\n    const extraSecondary = [];\n    let someAreHidden = false;\n    // unless disabled, move all hidden items to secondary group or ignore them\n    if (this._options?.hiddenItemStrategy !== -1 /* HiddenItemStrategy.NoHide */) {\n      for (let i = 0; i < primary.length; i++) {\n        const action = primary[i];\n        if (!(action instanceof MenuItemAction) && !(action instanceof SubmenuItemAction)) {\n          // console.warn(`Action ${action.id}/${action.label} is not a MenuItemAction`);\n          continue;\n        }\n        if (!action.hideActions) {\n          continue;\n        }\n        // collect all toggle actions\n        toggleActions.push(action.hideActions.toggle);\n        if (action.hideActions.toggle.checked) {\n          toggleActionsCheckedCount++;\n        }\n        // hidden items move into overflow or ignore\n        if (action.hideActions.isHidden) {\n          someAreHidden = true;\n          primary[i] = undefined;\n          if (this._options?.hiddenItemStrategy !== 0 /* HiddenItemStrategy.Ignore */) {\n            extraSecondary[i] = action;\n          }\n        }\n      }\n    }\n    // count for max\n    if (this._options?.overflowBehavior !== undefined) {\n      const exemptedIds = intersection(new Set(this._options.overflowBehavior.exempted), Iterable.map(primary, a => a?.id));\n      const maxItems = this._options.overflowBehavior.maxItems - exemptedIds.size;\n      let count = 0;\n      for (let i = 0; i < primary.length; i++) {\n        const action = primary[i];\n        if (!action) {\n          continue;\n        }\n        count++;\n        if (exemptedIds.has(action.id)) {\n          continue;\n        }\n        if (count >= maxItems) {\n          primary[i] = undefined;\n          extraSecondary[i] = action;\n        }\n      }\n    }\n    // coalesce turns Array<IAction|undefined> into IAction[]\n    coalesceInPlace(primary);\n    coalesceInPlace(extraSecondary);\n    super.setActions(primary, Separator.join(extraSecondary, secondary));\n    // add context menu for toggle and configure keybinding actions\n    if (toggleActions.length > 0 || primary.length > 0) {\n      this._sessionDisposables.add(addDisposableListener(this.getElement(), 'contextmenu', e => {\n        const event = new StandardMouseEvent(getWindow(this.getElement()), e);\n        const action = this.getItemAction(event.target);\n        if (!action) {\n          return;\n        }\n        event.preventDefault();\n        event.stopPropagation();\n        const primaryActions = [];\n        // -- Configure Keybinding Action --\n        if (action instanceof MenuItemAction && action.menuKeybinding) {\n          primaryActions.push(action.menuKeybinding);\n        } else if (!(action instanceof SubmenuItemAction || action instanceof ToggleMenuAction)) {\n          // only enable the configure keybinding action for actions that support keybindings\n          const supportsKeybindings = !!this._keybindingService.lookupKeybinding(action.id);\n          primaryActions.push(createConfigureKeybindingAction(this._commandService, this._keybindingService, action.id, undefined, supportsKeybindings));\n        }\n        // -- Hide Actions --\n        if (toggleActions.length > 0) {\n          let noHide = false;\n          // last item cannot be hidden when using ignore strategy\n          if (toggleActionsCheckedCount === 1 && this._options?.hiddenItemStrategy === 0 /* HiddenItemStrategy.Ignore */) {\n            noHide = true;\n            for (let i = 0; i < toggleActions.length; i++) {\n              if (toggleActions[i].checked) {\n                toggleActions[i] = toAction({\n                  id: action.id,\n                  label: action.label,\n                  checked: true,\n                  enabled: false,\n                  run() {}\n                });\n                break; // there is only one\n              }\n            }\n          }\n          // add \"hide foo\" actions\n          if (!noHide && (action instanceof MenuItemAction || action instanceof SubmenuItemAction)) {\n            if (!action.hideActions) {\n              // no context menu for MenuItemAction instances that support no hiding\n              // those are fake actions and need to be cleaned up\n              return;\n            }\n            primaryActions.push(action.hideActions.hide);\n          } else {\n            primaryActions.push(toAction({\n              id: 'label',\n              label: localize('hide', \"Hide\"),\n              enabled: false,\n              run() {}\n            }));\n          }\n        }\n        const actions = Separator.join(primaryActions, toggleActions);\n        // add \"Reset Menu\" action\n        if (this._options?.resetMenu && !menuIds) {\n          menuIds = [this._options.resetMenu];\n        }\n        if (someAreHidden && menuIds) {\n          actions.push(new Separator());\n          actions.push(toAction({\n            id: 'resetThisMenu',\n            label: localize('resetThisMenu', \"Reset Menu\"),\n            run: () => this._menuService.resetHiddenStates(menuIds)\n          }));\n        }\n        if (actions.length === 0) {\n          return;\n        }\n        this._contextMenuService.showContextMenu({\n          getAnchor: () => event,\n          getActions: () => actions,\n          // add context menu actions (iff appicable)\n          menuId: this._options?.contextMenu,\n          menuActionOptions: {\n            renderShortTitle: true,\n            ...this._options?.menuOptions\n          },\n          skipTelemetry: typeof this._options?.telemetrySource === 'string',\n          contextKeyService: this._contextKeyService\n        });\n      }));\n    }\n  }\n};\nWorkbenchToolBar = __decorate([__param(2, IMenuService), __param(3, IContextKeyService), __param(4, IContextMenuService), __param(5, IKeybindingService), __param(6, ICommandService), __param(7, ITelemetryService)], WorkbenchToolBar);\nexport { WorkbenchToolBar };\n/**\n * A {@link WorkbenchToolBar workbench toolbar} that is purely driven from a {@link MenuId menu}-identifier.\n *\n * *Note* that Manual updates via `setActions` are NOT supported.\n */\nlet MenuWorkbenchToolBar = class MenuWorkbenchToolBar extends WorkbenchToolBar {\n  constructor(container, menuId, options, menuService, contextKeyService, contextMenuService, keybindingService, commandService, telemetryService) {\n    super(container, {\n      resetMenu: menuId,\n      ...options\n    }, menuService, contextKeyService, contextMenuService, keybindingService, commandService, telemetryService);\n    this._onDidChangeMenuItems = this._store.add(new Emitter());\n    this.onDidChangeMenuItems = this._onDidChangeMenuItems.event;\n    // update logic\n    const menu = this._store.add(menuService.createMenu(menuId, contextKeyService, {\n      emitEventsForSubmenuChanges: true\n    }));\n    const updateToolbar = () => {\n      const primary = [];\n      const secondary = [];\n      createAndFillInActionBarActions(menu, options?.menuOptions, {\n        primary,\n        secondary\n      }, options?.toolbarOptions?.primaryGroup, options?.toolbarOptions?.shouldInlineSubmenu, options?.toolbarOptions?.useSeparatorsInPrimaryActions);\n      container.classList.toggle('has-no-actions', primary.length === 0 && secondary.length === 0);\n      super.setActions(primary, secondary);\n    };\n    this._store.add(menu.onDidChange(() => {\n      updateToolbar();\n      this._onDidChangeMenuItems.fire(this);\n    }));\n    updateToolbar();\n  }\n  /**\n   * @deprecated The WorkbenchToolBar does not support this method because it works with menus.\n   */\n  setActions() {\n    throw new BugIndicatingError('This toolbar is populated from a menu.');\n  }\n};\nMenuWorkbenchToolBar = __decorate([__param(3, IMenuService), __param(4, IContextKeyService), __param(5, IContextMenuService), __param(6, IKeybindingService), __param(7, ICommandService), __param(8, ITelemetryService)], MenuWorkbenchToolBar);\nexport { MenuWorkbenchToolBar };", "map": {"version": 3, "names": ["__decorate", "decorators", "target", "key", "desc", "c", "arguments", "length", "r", "Object", "getOwnPropertyDescriptor", "d", "Reflect", "decorate", "i", "defineProperty", "__param", "paramIndex", "decorator", "addDisposableListener", "getWindow", "StandardMouseEvent", "ToggleMenuAction", "<PERSON><PERSON><PERSON><PERSON>", "Separator", "toAction", "coalesceInPlace", "intersection", "BugIndicatingError", "Emitter", "Iterable", "DisposableStore", "localize", "createAndFillInActionBarActions", "IMenuService", "MenuItemAction", "SubmenuItemAction", "createConfigureKeybindingAction", "ICommandService", "IContextKeyService", "IContextMenuService", "IKeybindingService", "ITelemetryService", "WorkbenchToolBar", "constructor", "container", "_options", "_menuService", "_contextKeyService", "_contextMenuService", "_keybindingService", "_commandService", "telemetryService", "getKeyBinding", "action", "lookupKeybinding", "id", "undefined", "allowContextMenu", "skipTelemetry", "telemetrySource", "_sessionDisposables", "_store", "add", "actionBar", "onDidRun", "e", "publicLog2", "from", "setActions", "_primary", "_secondary", "menuIds", "clear", "primary", "slice", "secondary", "toggleActions", "toggleActionsCheckedCount", "extraSecondary", "someAreHidden", "hiddenItemStrategy", "hideActions", "push", "toggle", "checked", "isHidden", "overflowBehavior", "exemptedIds", "Set", "exempted", "map", "a", "maxItems", "size", "count", "has", "join", "getElement", "event", "getItemAction", "preventDefault", "stopPropagation", "primaryActions", "menuKeybinding", "supportsKeybindings", "noHide", "label", "enabled", "run", "hide", "actions", "resetMenu", "resetHiddenStates", "showContextMenu", "getAnchor", "getActions", "menuId", "contextMenu", "menuActionOptions", "renderShortTitle", "menuOptions", "contextKeyService", "MenuWorkbenchToolBar", "options", "menuService", "contextMenuService", "keybindingService", "commandService", "_onDidChangeMenuItems", "onDidChangeMenuItems", "menu", "createMenu", "emitEventsForSubmenuChanges", "updateToolbar", "toolbarOptions", "primaryGroup", "shouldInlineSubmenu", "useSeparatorsInPrimaryActions", "classList", "onDidChange", "fire"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/platform/actions/browser/toolbar.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = (this && this.__param) || function (paramIndex, decorator) {\n    return function (target, key) { decorator(target, key, paramIndex); }\n};\nimport { addDisposableListener, getWindow } from '../../../base/browser/dom.js';\nimport { StandardMouseEvent } from '../../../base/browser/mouseEvent.js';\nimport { ToggleMenuAction, ToolBar } from '../../../base/browser/ui/toolbar/toolbar.js';\nimport { Separator, toAction } from '../../../base/common/actions.js';\nimport { coalesceInPlace } from '../../../base/common/arrays.js';\nimport { intersection } from '../../../base/common/collections.js';\nimport { BugIndicatingError } from '../../../base/common/errors.js';\nimport { Emitter } from '../../../base/common/event.js';\nimport { Iterable } from '../../../base/common/iterator.js';\nimport { DisposableStore } from '../../../base/common/lifecycle.js';\nimport { localize } from '../../../nls.js';\nimport { createAndFillInActionBarActions } from './menuEntryActionViewItem.js';\nimport { IMenuService, MenuItemAction, SubmenuItemAction } from '../common/actions.js';\nimport { createConfigureKeybindingAction } from '../common/menuService.js';\nimport { ICommandService } from '../../commands/common/commands.js';\nimport { IContextKeyService } from '../../contextkey/common/contextkey.js';\nimport { IContextMenuService } from '../../contextview/browser/contextView.js';\nimport { IKeybindingService } from '../../keybinding/common/keybinding.js';\nimport { ITelemetryService } from '../../telemetry/common/telemetry.js';\n/**\n * The `WorkbenchToolBar` does\n * - support hiding of menu items\n * - lookup keybindings for each actions automatically\n * - send `workbenchActionExecuted`-events for each action\n *\n * See {@link MenuWorkbenchToolBar} for a toolbar that is backed by a menu.\n */\nlet WorkbenchToolBar = class WorkbenchToolBar extends ToolBar {\n    constructor(container, _options, _menuService, _contextKeyService, _contextMenuService, _keybindingService, _commandService, telemetryService) {\n        super(container, _contextMenuService, {\n            // defaults\n            getKeyBinding: (action) => _keybindingService.lookupKeybinding(action.id) ?? undefined,\n            // options (override defaults)\n            ..._options,\n            // mandatory (overide options)\n            allowContextMenu: true,\n            skipTelemetry: typeof _options?.telemetrySource === 'string',\n        });\n        this._options = _options;\n        this._menuService = _menuService;\n        this._contextKeyService = _contextKeyService;\n        this._contextMenuService = _contextMenuService;\n        this._keybindingService = _keybindingService;\n        this._commandService = _commandService;\n        this._sessionDisposables = this._store.add(new DisposableStore());\n        // telemetry logic\n        const telemetrySource = _options?.telemetrySource;\n        if (telemetrySource) {\n            this._store.add(this.actionBar.onDidRun(e => telemetryService.publicLog2('workbenchActionExecuted', { id: e.action.id, from: telemetrySource })));\n        }\n    }\n    setActions(_primary, _secondary = [], menuIds) {\n        this._sessionDisposables.clear();\n        const primary = _primary.slice(); // for hiding and overflow we set some items to undefined\n        const secondary = _secondary.slice();\n        const toggleActions = [];\n        let toggleActionsCheckedCount = 0;\n        const extraSecondary = [];\n        let someAreHidden = false;\n        // unless disabled, move all hidden items to secondary group or ignore them\n        if (this._options?.hiddenItemStrategy !== -1 /* HiddenItemStrategy.NoHide */) {\n            for (let i = 0; i < primary.length; i++) {\n                const action = primary[i];\n                if (!(action instanceof MenuItemAction) && !(action instanceof SubmenuItemAction)) {\n                    // console.warn(`Action ${action.id}/${action.label} is not a MenuItemAction`);\n                    continue;\n                }\n                if (!action.hideActions) {\n                    continue;\n                }\n                // collect all toggle actions\n                toggleActions.push(action.hideActions.toggle);\n                if (action.hideActions.toggle.checked) {\n                    toggleActionsCheckedCount++;\n                }\n                // hidden items move into overflow or ignore\n                if (action.hideActions.isHidden) {\n                    someAreHidden = true;\n                    primary[i] = undefined;\n                    if (this._options?.hiddenItemStrategy !== 0 /* HiddenItemStrategy.Ignore */) {\n                        extraSecondary[i] = action;\n                    }\n                }\n            }\n        }\n        // count for max\n        if (this._options?.overflowBehavior !== undefined) {\n            const exemptedIds = intersection(new Set(this._options.overflowBehavior.exempted), Iterable.map(primary, a => a?.id));\n            const maxItems = this._options.overflowBehavior.maxItems - exemptedIds.size;\n            let count = 0;\n            for (let i = 0; i < primary.length; i++) {\n                const action = primary[i];\n                if (!action) {\n                    continue;\n                }\n                count++;\n                if (exemptedIds.has(action.id)) {\n                    continue;\n                }\n                if (count >= maxItems) {\n                    primary[i] = undefined;\n                    extraSecondary[i] = action;\n                }\n            }\n        }\n        // coalesce turns Array<IAction|undefined> into IAction[]\n        coalesceInPlace(primary);\n        coalesceInPlace(extraSecondary);\n        super.setActions(primary, Separator.join(extraSecondary, secondary));\n        // add context menu for toggle and configure keybinding actions\n        if (toggleActions.length > 0 || primary.length > 0) {\n            this._sessionDisposables.add(addDisposableListener(this.getElement(), 'contextmenu', e => {\n                const event = new StandardMouseEvent(getWindow(this.getElement()), e);\n                const action = this.getItemAction(event.target);\n                if (!(action)) {\n                    return;\n                }\n                event.preventDefault();\n                event.stopPropagation();\n                const primaryActions = [];\n                // -- Configure Keybinding Action --\n                if (action instanceof MenuItemAction && action.menuKeybinding) {\n                    primaryActions.push(action.menuKeybinding);\n                }\n                else if (!(action instanceof SubmenuItemAction || action instanceof ToggleMenuAction)) {\n                    // only enable the configure keybinding action for actions that support keybindings\n                    const supportsKeybindings = !!this._keybindingService.lookupKeybinding(action.id);\n                    primaryActions.push(createConfigureKeybindingAction(this._commandService, this._keybindingService, action.id, undefined, supportsKeybindings));\n                }\n                // -- Hide Actions --\n                if (toggleActions.length > 0) {\n                    let noHide = false;\n                    // last item cannot be hidden when using ignore strategy\n                    if (toggleActionsCheckedCount === 1 && this._options?.hiddenItemStrategy === 0 /* HiddenItemStrategy.Ignore */) {\n                        noHide = true;\n                        for (let i = 0; i < toggleActions.length; i++) {\n                            if (toggleActions[i].checked) {\n                                toggleActions[i] = toAction({\n                                    id: action.id,\n                                    label: action.label,\n                                    checked: true,\n                                    enabled: false,\n                                    run() { }\n                                });\n                                break; // there is only one\n                            }\n                        }\n                    }\n                    // add \"hide foo\" actions\n                    if (!noHide && (action instanceof MenuItemAction || action instanceof SubmenuItemAction)) {\n                        if (!action.hideActions) {\n                            // no context menu for MenuItemAction instances that support no hiding\n                            // those are fake actions and need to be cleaned up\n                            return;\n                        }\n                        primaryActions.push(action.hideActions.hide);\n                    }\n                    else {\n                        primaryActions.push(toAction({\n                            id: 'label',\n                            label: localize('hide', \"Hide\"),\n                            enabled: false,\n                            run() { }\n                        }));\n                    }\n                }\n                const actions = Separator.join(primaryActions, toggleActions);\n                // add \"Reset Menu\" action\n                if (this._options?.resetMenu && !menuIds) {\n                    menuIds = [this._options.resetMenu];\n                }\n                if (someAreHidden && menuIds) {\n                    actions.push(new Separator());\n                    actions.push(toAction({\n                        id: 'resetThisMenu',\n                        label: localize('resetThisMenu', \"Reset Menu\"),\n                        run: () => this._menuService.resetHiddenStates(menuIds)\n                    }));\n                }\n                if (actions.length === 0) {\n                    return;\n                }\n                this._contextMenuService.showContextMenu({\n                    getAnchor: () => event,\n                    getActions: () => actions,\n                    // add context menu actions (iff appicable)\n                    menuId: this._options?.contextMenu,\n                    menuActionOptions: { renderShortTitle: true, ...this._options?.menuOptions },\n                    skipTelemetry: typeof this._options?.telemetrySource === 'string',\n                    contextKeyService: this._contextKeyService,\n                });\n            }));\n        }\n    }\n};\nWorkbenchToolBar = __decorate([\n    __param(2, IMenuService),\n    __param(3, IContextKeyService),\n    __param(4, IContextMenuService),\n    __param(5, IKeybindingService),\n    __param(6, ICommandService),\n    __param(7, ITelemetryService)\n], WorkbenchToolBar);\nexport { WorkbenchToolBar };\n/**\n * A {@link WorkbenchToolBar workbench toolbar} that is purely driven from a {@link MenuId menu}-identifier.\n *\n * *Note* that Manual updates via `setActions` are NOT supported.\n */\nlet MenuWorkbenchToolBar = class MenuWorkbenchToolBar extends WorkbenchToolBar {\n    constructor(container, menuId, options, menuService, contextKeyService, contextMenuService, keybindingService, commandService, telemetryService) {\n        super(container, { resetMenu: menuId, ...options }, menuService, contextKeyService, contextMenuService, keybindingService, commandService, telemetryService);\n        this._onDidChangeMenuItems = this._store.add(new Emitter());\n        this.onDidChangeMenuItems = this._onDidChangeMenuItems.event;\n        // update logic\n        const menu = this._store.add(menuService.createMenu(menuId, contextKeyService, { emitEventsForSubmenuChanges: true }));\n        const updateToolbar = () => {\n            const primary = [];\n            const secondary = [];\n            createAndFillInActionBarActions(menu, options?.menuOptions, { primary, secondary }, options?.toolbarOptions?.primaryGroup, options?.toolbarOptions?.shouldInlineSubmenu, options?.toolbarOptions?.useSeparatorsInPrimaryActions);\n            container.classList.toggle('has-no-actions', primary.length === 0 && secondary.length === 0);\n            super.setActions(primary, secondary);\n        };\n        this._store.add(menu.onDidChange(() => {\n            updateToolbar();\n            this._onDidChangeMenuItems.fire(this);\n        }));\n        updateToolbar();\n    }\n    /**\n     * @deprecated The WorkbenchToolBar does not support this method because it works with menus.\n     */\n    setActions() {\n        throw new BugIndicatingError('This toolbar is populated from a menu.');\n    }\n};\nMenuWorkbenchToolBar = __decorate([\n    __param(3, IMenuService),\n    __param(4, IContextKeyService),\n    __param(5, IContextMenuService),\n    __param(6, IKeybindingService),\n    __param(7, ICommandService),\n    __param(8, ITelemetryService)\n], MenuWorkbenchToolBar);\nexport { MenuWorkbenchToolBar };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,IAAIA,UAAU,GAAI,IAAI,IAAI,IAAI,CAACA,UAAU,IAAK,UAAUC,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAE;EACnF,IAAIC,CAAC,GAAGC,SAAS,CAACC,MAAM;IAAEC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGH,MAAM,GAAGE,IAAI,KAAK,IAAI,GAAGA,IAAI,GAAGK,MAAM,CAACC,wBAAwB,CAACR,MAAM,EAAEC,GAAG,CAAC,GAAGC,IAAI;IAAEO,CAAC;EAC5H,IAAI,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,CAACC,QAAQ,KAAK,UAAU,EAAEL,CAAC,GAAGI,OAAO,CAACC,QAAQ,CAACZ,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,CAAC,CAAC,KAC1H,KAAK,IAAIU,CAAC,GAAGb,UAAU,CAACM,MAAM,GAAG,CAAC,EAAEO,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE,IAAIH,CAAC,GAAGV,UAAU,CAACa,CAAC,CAAC,EAAEN,CAAC,GAAG,CAACH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACH,CAAC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACT,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,GAAGG,CAAC,CAACT,MAAM,EAAEC,GAAG,CAAC,KAAKK,CAAC;EACjJ,OAAOH,CAAC,GAAG,CAAC,IAAIG,CAAC,IAAIC,MAAM,CAACM,cAAc,CAACb,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,EAAEA,CAAC;AACjE,CAAC;AACD,IAAIQ,OAAO,GAAI,IAAI,IAAI,IAAI,CAACA,OAAO,IAAK,UAAUC,UAAU,EAAEC,SAAS,EAAE;EACrE,OAAO,UAAUhB,MAAM,EAAEC,GAAG,EAAE;IAAEe,SAAS,CAAChB,MAAM,EAAEC,GAAG,EAAEc,UAAU,CAAC;EAAE,CAAC;AACzE,CAAC;AACD,SAASE,qBAAqB,EAAEC,SAAS,QAAQ,8BAA8B;AAC/E,SAASC,kBAAkB,QAAQ,qCAAqC;AACxE,SAASC,gBAAgB,EAAEC,OAAO,QAAQ,6CAA6C;AACvF,SAASC,SAAS,EAAEC,QAAQ,QAAQ,iCAAiC;AACrE,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,YAAY,QAAQ,qCAAqC;AAClE,SAASC,kBAAkB,QAAQ,gCAAgC;AACnE,SAASC,OAAO,QAAQ,+BAA+B;AACvD,SAASC,QAAQ,QAAQ,kCAAkC;AAC3D,SAASC,eAAe,QAAQ,mCAAmC;AACnE,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,+BAA+B,QAAQ,8BAA8B;AAC9E,SAASC,YAAY,EAAEC,cAAc,EAAEC,iBAAiB,QAAQ,sBAAsB;AACtF,SAASC,+BAA+B,QAAQ,0BAA0B;AAC1E,SAASC,eAAe,QAAQ,mCAAmC;AACnE,SAASC,kBAAkB,QAAQ,uCAAuC;AAC1E,SAASC,mBAAmB,QAAQ,0CAA0C;AAC9E,SAASC,kBAAkB,QAAQ,uCAAuC;AAC1E,SAASC,iBAAiB,QAAQ,qCAAqC;AACvE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,gBAAgB,GAAG,MAAMA,gBAAgB,SAASpB,OAAO,CAAC;EAC1DqB,WAAWA,CAACC,SAAS,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,kBAAkB,EAAEC,mBAAmB,EAAEC,kBAAkB,EAAEC,eAAe,EAAEC,gBAAgB,EAAE;IAC3I,KAAK,CAACP,SAAS,EAAEI,mBAAmB,EAAE;MAClC;MACAI,aAAa,EAAGC,MAAM,IAAKJ,kBAAkB,CAACK,gBAAgB,CAACD,MAAM,CAACE,EAAE,CAAC,IAAIC,SAAS;MACtF;MACA,GAAGX,QAAQ;MACX;MACAY,gBAAgB,EAAE,IAAI;MACtBC,aAAa,EAAE,OAAOb,QAAQ,EAAEc,eAAe,KAAK;IACxD,CAAC,CAAC;IACF,IAAI,CAACd,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACC,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACU,mBAAmB,GAAG,IAAI,CAACC,MAAM,CAACC,GAAG,CAAC,IAAIhC,eAAe,CAAC,CAAC,CAAC;IACjE;IACA,MAAM6B,eAAe,GAAGd,QAAQ,EAAEc,eAAe;IACjD,IAAIA,eAAe,EAAE;MACjB,IAAI,CAACE,MAAM,CAACC,GAAG,CAAC,IAAI,CAACC,SAAS,CAACC,QAAQ,CAACC,CAAC,IAAId,gBAAgB,CAACe,UAAU,CAAC,yBAAyB,EAAE;QAAEX,EAAE,EAAEU,CAAC,CAACZ,MAAM,CAACE,EAAE;QAAEY,IAAI,EAAER;MAAgB,CAAC,CAAC,CAAC,CAAC;IACrJ;EACJ;EACAS,UAAUA,CAACC,QAAQ,EAAEC,UAAU,GAAG,EAAE,EAAEC,OAAO,EAAE;IAC3C,IAAI,CAACX,mBAAmB,CAACY,KAAK,CAAC,CAAC;IAChC,MAAMC,OAAO,GAAGJ,QAAQ,CAACK,KAAK,CAAC,CAAC,CAAC,CAAC;IAClC,MAAMC,SAAS,GAAGL,UAAU,CAACI,KAAK,CAAC,CAAC;IACpC,MAAME,aAAa,GAAG,EAAE;IACxB,IAAIC,yBAAyB,GAAG,CAAC;IACjC,MAAMC,cAAc,GAAG,EAAE;IACzB,IAAIC,aAAa,GAAG,KAAK;IACzB;IACA,IAAI,IAAI,CAAClC,QAAQ,EAAEmC,kBAAkB,KAAK,CAAC,CAAC,CAAC,iCAAiC;MAC1E,KAAK,IAAInE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4D,OAAO,CAACnE,MAAM,EAAEO,CAAC,EAAE,EAAE;QACrC,MAAMwC,MAAM,GAAGoB,OAAO,CAAC5D,CAAC,CAAC;QACzB,IAAI,EAAEwC,MAAM,YAAYnB,cAAc,CAAC,IAAI,EAAEmB,MAAM,YAAYlB,iBAAiB,CAAC,EAAE;UAC/E;UACA;QACJ;QACA,IAAI,CAACkB,MAAM,CAAC4B,WAAW,EAAE;UACrB;QACJ;QACA;QACAL,aAAa,CAACM,IAAI,CAAC7B,MAAM,CAAC4B,WAAW,CAACE,MAAM,CAAC;QAC7C,IAAI9B,MAAM,CAAC4B,WAAW,CAACE,MAAM,CAACC,OAAO,EAAE;UACnCP,yBAAyB,EAAE;QAC/B;QACA;QACA,IAAIxB,MAAM,CAAC4B,WAAW,CAACI,QAAQ,EAAE;UAC7BN,aAAa,GAAG,IAAI;UACpBN,OAAO,CAAC5D,CAAC,CAAC,GAAG2C,SAAS;UACtB,IAAI,IAAI,CAACX,QAAQ,EAAEmC,kBAAkB,KAAK,CAAC,CAAC,iCAAiC;YACzEF,cAAc,CAACjE,CAAC,CAAC,GAAGwC,MAAM;UAC9B;QACJ;MACJ;IACJ;IACA;IACA,IAAI,IAAI,CAACR,QAAQ,EAAEyC,gBAAgB,KAAK9B,SAAS,EAAE;MAC/C,MAAM+B,WAAW,GAAG7D,YAAY,CAAC,IAAI8D,GAAG,CAAC,IAAI,CAAC3C,QAAQ,CAACyC,gBAAgB,CAACG,QAAQ,CAAC,EAAE5D,QAAQ,CAAC6D,GAAG,CAACjB,OAAO,EAAEkB,CAAC,IAAIA,CAAC,EAAEpC,EAAE,CAAC,CAAC;MACrH,MAAMqC,QAAQ,GAAG,IAAI,CAAC/C,QAAQ,CAACyC,gBAAgB,CAACM,QAAQ,GAAGL,WAAW,CAACM,IAAI;MAC3E,IAAIC,KAAK,GAAG,CAAC;MACb,KAAK,IAAIjF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4D,OAAO,CAACnE,MAAM,EAAEO,CAAC,EAAE,EAAE;QACrC,MAAMwC,MAAM,GAAGoB,OAAO,CAAC5D,CAAC,CAAC;QACzB,IAAI,CAACwC,MAAM,EAAE;UACT;QACJ;QACAyC,KAAK,EAAE;QACP,IAAIP,WAAW,CAACQ,GAAG,CAAC1C,MAAM,CAACE,EAAE,CAAC,EAAE;UAC5B;QACJ;QACA,IAAIuC,KAAK,IAAIF,QAAQ,EAAE;UACnBnB,OAAO,CAAC5D,CAAC,CAAC,GAAG2C,SAAS;UACtBsB,cAAc,CAACjE,CAAC,CAAC,GAAGwC,MAAM;QAC9B;MACJ;IACJ;IACA;IACA5B,eAAe,CAACgD,OAAO,CAAC;IACxBhD,eAAe,CAACqD,cAAc,CAAC;IAC/B,KAAK,CAACV,UAAU,CAACK,OAAO,EAAElD,SAAS,CAACyE,IAAI,CAAClB,cAAc,EAAEH,SAAS,CAAC,CAAC;IACpE;IACA,IAAIC,aAAa,CAACtE,MAAM,GAAG,CAAC,IAAImE,OAAO,CAACnE,MAAM,GAAG,CAAC,EAAE;MAChD,IAAI,CAACsD,mBAAmB,CAACE,GAAG,CAAC5C,qBAAqB,CAAC,IAAI,CAAC+E,UAAU,CAAC,CAAC,EAAE,aAAa,EAAEhC,CAAC,IAAI;QACtF,MAAMiC,KAAK,GAAG,IAAI9E,kBAAkB,CAACD,SAAS,CAAC,IAAI,CAAC8E,UAAU,CAAC,CAAC,CAAC,EAAEhC,CAAC,CAAC;QACrE,MAAMZ,MAAM,GAAG,IAAI,CAAC8C,aAAa,CAACD,KAAK,CAACjG,MAAM,CAAC;QAC/C,IAAI,CAAEoD,MAAO,EAAE;UACX;QACJ;QACA6C,KAAK,CAACE,cAAc,CAAC,CAAC;QACtBF,KAAK,CAACG,eAAe,CAAC,CAAC;QACvB,MAAMC,cAAc,GAAG,EAAE;QACzB;QACA,IAAIjD,MAAM,YAAYnB,cAAc,IAAImB,MAAM,CAACkD,cAAc,EAAE;UAC3DD,cAAc,CAACpB,IAAI,CAAC7B,MAAM,CAACkD,cAAc,CAAC;QAC9C,CAAC,MACI,IAAI,EAAElD,MAAM,YAAYlB,iBAAiB,IAAIkB,MAAM,YAAYhC,gBAAgB,CAAC,EAAE;UACnF;UACA,MAAMmF,mBAAmB,GAAG,CAAC,CAAC,IAAI,CAACvD,kBAAkB,CAACK,gBAAgB,CAACD,MAAM,CAACE,EAAE,CAAC;UACjF+C,cAAc,CAACpB,IAAI,CAAC9C,+BAA+B,CAAC,IAAI,CAACc,eAAe,EAAE,IAAI,CAACD,kBAAkB,EAAEI,MAAM,CAACE,EAAE,EAAEC,SAAS,EAAEgD,mBAAmB,CAAC,CAAC;QAClJ;QACA;QACA,IAAI5B,aAAa,CAACtE,MAAM,GAAG,CAAC,EAAE;UAC1B,IAAImG,MAAM,GAAG,KAAK;UAClB;UACA,IAAI5B,yBAAyB,KAAK,CAAC,IAAI,IAAI,CAAChC,QAAQ,EAAEmC,kBAAkB,KAAK,CAAC,CAAC,iCAAiC;YAC5GyB,MAAM,GAAG,IAAI;YACb,KAAK,IAAI5F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+D,aAAa,CAACtE,MAAM,EAAEO,CAAC,EAAE,EAAE;cAC3C,IAAI+D,aAAa,CAAC/D,CAAC,CAAC,CAACuE,OAAO,EAAE;gBAC1BR,aAAa,CAAC/D,CAAC,CAAC,GAAGW,QAAQ,CAAC;kBACxB+B,EAAE,EAAEF,MAAM,CAACE,EAAE;kBACbmD,KAAK,EAAErD,MAAM,CAACqD,KAAK;kBACnBtB,OAAO,EAAE,IAAI;kBACbuB,OAAO,EAAE,KAAK;kBACdC,GAAGA,CAAA,EAAG,CAAE;gBACZ,CAAC,CAAC;gBACF,MAAM,CAAC;cACX;YACJ;UACJ;UACA;UACA,IAAI,CAACH,MAAM,KAAKpD,MAAM,YAAYnB,cAAc,IAAImB,MAAM,YAAYlB,iBAAiB,CAAC,EAAE;YACtF,IAAI,CAACkB,MAAM,CAAC4B,WAAW,EAAE;cACrB;cACA;cACA;YACJ;YACAqB,cAAc,CAACpB,IAAI,CAAC7B,MAAM,CAAC4B,WAAW,CAAC4B,IAAI,CAAC;UAChD,CAAC,MACI;YACDP,cAAc,CAACpB,IAAI,CAAC1D,QAAQ,CAAC;cACzB+B,EAAE,EAAE,OAAO;cACXmD,KAAK,EAAE3E,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC;cAC/B4E,OAAO,EAAE,KAAK;cACdC,GAAGA,CAAA,EAAG,CAAE;YACZ,CAAC,CAAC,CAAC;UACP;QACJ;QACA,MAAME,OAAO,GAAGvF,SAAS,CAACyE,IAAI,CAACM,cAAc,EAAE1B,aAAa,CAAC;QAC7D;QACA,IAAI,IAAI,CAAC/B,QAAQ,EAAEkE,SAAS,IAAI,CAACxC,OAAO,EAAE;UACtCA,OAAO,GAAG,CAAC,IAAI,CAAC1B,QAAQ,CAACkE,SAAS,CAAC;QACvC;QACA,IAAIhC,aAAa,IAAIR,OAAO,EAAE;UAC1BuC,OAAO,CAAC5B,IAAI,CAAC,IAAI3D,SAAS,CAAC,CAAC,CAAC;UAC7BuF,OAAO,CAAC5B,IAAI,CAAC1D,QAAQ,CAAC;YAClB+B,EAAE,EAAE,eAAe;YACnBmD,KAAK,EAAE3E,QAAQ,CAAC,eAAe,EAAE,YAAY,CAAC;YAC9C6E,GAAG,EAAEA,CAAA,KAAM,IAAI,CAAC9D,YAAY,CAACkE,iBAAiB,CAACzC,OAAO;UAC1D,CAAC,CAAC,CAAC;QACP;QACA,IAAIuC,OAAO,CAACxG,MAAM,KAAK,CAAC,EAAE;UACtB;QACJ;QACA,IAAI,CAAC0C,mBAAmB,CAACiE,eAAe,CAAC;UACrCC,SAAS,EAAEA,CAAA,KAAMhB,KAAK;UACtBiB,UAAU,EAAEA,CAAA,KAAML,OAAO;UACzB;UACAM,MAAM,EAAE,IAAI,CAACvE,QAAQ,EAAEwE,WAAW;UAClCC,iBAAiB,EAAE;YAAEC,gBAAgB,EAAE,IAAI;YAAE,GAAG,IAAI,CAAC1E,QAAQ,EAAE2E;UAAY,CAAC;UAC5E9D,aAAa,EAAE,OAAO,IAAI,CAACb,QAAQ,EAAEc,eAAe,KAAK,QAAQ;UACjE8D,iBAAiB,EAAE,IAAI,CAAC1E;QAC5B,CAAC,CAAC;MACN,CAAC,CAAC,CAAC;IACP;EACJ;AACJ,CAAC;AACDL,gBAAgB,GAAG3C,UAAU,CAAC,CAC1BgB,OAAO,CAAC,CAAC,EAAEkB,YAAY,CAAC,EACxBlB,OAAO,CAAC,CAAC,EAAEuB,kBAAkB,CAAC,EAC9BvB,OAAO,CAAC,CAAC,EAAEwB,mBAAmB,CAAC,EAC/BxB,OAAO,CAAC,CAAC,EAAEyB,kBAAkB,CAAC,EAC9BzB,OAAO,CAAC,CAAC,EAAEsB,eAAe,CAAC,EAC3BtB,OAAO,CAAC,CAAC,EAAE0B,iBAAiB,CAAC,CAChC,EAAEC,gBAAgB,CAAC;AACpB,SAASA,gBAAgB;AACzB;AACA;AACA;AACA;AACA;AACA,IAAIgF,oBAAoB,GAAG,MAAMA,oBAAoB,SAAShF,gBAAgB,CAAC;EAC3EC,WAAWA,CAACC,SAAS,EAAEwE,MAAM,EAAEO,OAAO,EAAEC,WAAW,EAAEH,iBAAiB,EAAEI,kBAAkB,EAAEC,iBAAiB,EAAEC,cAAc,EAAE5E,gBAAgB,EAAE;IAC7I,KAAK,CAACP,SAAS,EAAE;MAAEmE,SAAS,EAAEK,MAAM;MAAE,GAAGO;IAAQ,CAAC,EAAEC,WAAW,EAAEH,iBAAiB,EAAEI,kBAAkB,EAAEC,iBAAiB,EAAEC,cAAc,EAAE5E,gBAAgB,CAAC;IAC5J,IAAI,CAAC6E,qBAAqB,GAAG,IAAI,CAACnE,MAAM,CAACC,GAAG,CAAC,IAAIlC,OAAO,CAAC,CAAC,CAAC;IAC3D,IAAI,CAACqG,oBAAoB,GAAG,IAAI,CAACD,qBAAqB,CAAC9B,KAAK;IAC5D;IACA,MAAMgC,IAAI,GAAG,IAAI,CAACrE,MAAM,CAACC,GAAG,CAAC8D,WAAW,CAACO,UAAU,CAACf,MAAM,EAAEK,iBAAiB,EAAE;MAAEW,2BAA2B,EAAE;IAAK,CAAC,CAAC,CAAC;IACtH,MAAMC,aAAa,GAAGA,CAAA,KAAM;MACxB,MAAM5D,OAAO,GAAG,EAAE;MAClB,MAAME,SAAS,GAAG,EAAE;MACpB3C,+BAA+B,CAACkG,IAAI,EAAEP,OAAO,EAAEH,WAAW,EAAE;QAAE/C,OAAO;QAAEE;MAAU,CAAC,EAAEgD,OAAO,EAAEW,cAAc,EAAEC,YAAY,EAAEZ,OAAO,EAAEW,cAAc,EAAEE,mBAAmB,EAAEb,OAAO,EAAEW,cAAc,EAAEG,6BAA6B,CAAC;MAChO7F,SAAS,CAAC8F,SAAS,CAACvD,MAAM,CAAC,gBAAgB,EAAEV,OAAO,CAACnE,MAAM,KAAK,CAAC,IAAIqE,SAAS,CAACrE,MAAM,KAAK,CAAC,CAAC;MAC5F,KAAK,CAAC8D,UAAU,CAACK,OAAO,EAAEE,SAAS,CAAC;IACxC,CAAC;IACD,IAAI,CAACd,MAAM,CAACC,GAAG,CAACoE,IAAI,CAACS,WAAW,CAAC,MAAM;MACnCN,aAAa,CAAC,CAAC;MACf,IAAI,CAACL,qBAAqB,CAACY,IAAI,CAAC,IAAI,CAAC;IACzC,CAAC,CAAC,CAAC;IACHP,aAAa,CAAC,CAAC;EACnB;EACA;AACJ;AACA;EACIjE,UAAUA,CAAA,EAAG;IACT,MAAM,IAAIzC,kBAAkB,CAAC,wCAAwC,CAAC;EAC1E;AACJ,CAAC;AACD+F,oBAAoB,GAAG3H,UAAU,CAAC,CAC9BgB,OAAO,CAAC,CAAC,EAAEkB,YAAY,CAAC,EACxBlB,OAAO,CAAC,CAAC,EAAEuB,kBAAkB,CAAC,EAC9BvB,OAAO,CAAC,CAAC,EAAEwB,mBAAmB,CAAC,EAC/BxB,OAAO,CAAC,CAAC,EAAEyB,kBAAkB,CAAC,EAC9BzB,OAAO,CAAC,CAAC,EAAEsB,eAAe,CAAC,EAC3BtB,OAAO,CAAC,CAAC,EAAE0B,iBAAiB,CAAC,CAChC,EAAEiF,oBAAoB,CAAC;AACxB,SAASA,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}