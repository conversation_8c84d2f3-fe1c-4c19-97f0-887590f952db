{"ast": null, "code": "import _asyncToGenerator from \"C:/console/aava-ui-web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { timeout } from '../../../base/common/async.js';\nimport { CancellationTokenSource } from '../../../base/common/cancellation.js';\nimport { Disposable, DisposableStore, MutableDisposable } from '../../../base/common/lifecycle.js';\nimport { isFunction } from '../../../base/common/types.js';\nexport var TriggerAction = /*#__PURE__*/function (TriggerAction) {\n  /**\n   * Do nothing after the button was clicked.\n   */\n  TriggerAction[TriggerAction[\"NO_ACTION\"] = 0] = \"NO_ACTION\";\n  /**\n   * Close the picker.\n   */\n  TriggerAction[TriggerAction[\"CLOSE_PICKER\"] = 1] = \"CLOSE_PICKER\";\n  /**\n   * Update the results of the picker.\n   */\n  TriggerAction[TriggerAction[\"REFRESH_PICKER\"] = 2] = \"REFRESH_PICKER\";\n  /**\n   * Remove the item from the picker.\n   */\n  TriggerAction[TriggerAction[\"REMOVE_ITEM\"] = 3] = \"REMOVE_ITEM\";\n  return TriggerAction;\n}(TriggerAction || {});\nfunction isPicksWithActive(obj) {\n  const candidate = obj;\n  return Array.isArray(candidate.items);\n}\nfunction isFastAndSlowPicks(obj) {\n  const candidate = obj;\n  return !!candidate.picks && candidate.additionalPicks instanceof Promise;\n}\nexport class PickerQuickAccessProvider extends Disposable {\n  constructor(prefix, options) {\n    super();\n    this.prefix = prefix;\n    this.options = options;\n  }\n  provide(picker, token, runOptions) {\n    var _this = this;\n    const disposables = new DisposableStore();\n    // Apply options if any\n    picker.canAcceptInBackground = !!this.options?.canAcceptInBackground;\n    // Disable filtering & sorting, we control the results\n    picker.matchOnLabel = picker.matchOnDescription = picker.matchOnDetail = picker.sortByLabel = false;\n    // Set initial picks and update on type\n    let picksCts = undefined;\n    const picksDisposable = disposables.add(new MutableDisposable());\n    const updatePickerItems = /*#__PURE__*/function () {\n      var _ref = _asyncToGenerator(function* () {\n        const picksDisposables = picksDisposable.value = new DisposableStore();\n        // Cancel any previous ask for picks and busy\n        picksCts?.dispose(true);\n        picker.busy = false;\n        // Create new cancellation source for this run\n        picksCts = new CancellationTokenSource(token);\n        // Collect picks and support both long running and short or combined\n        const picksToken = picksCts.token;\n        let picksFilter = picker.value.substring(_this.prefix.length);\n        if (!_this.options?.shouldSkipTrimPickFilter) {\n          picksFilter = picksFilter.trim();\n        }\n        const providedPicks = _this._getPicks(picksFilter, picksDisposables, picksToken, runOptions);\n        const applyPicks = (picks, skipEmpty) => {\n          let items;\n          let activeItem = undefined;\n          if (isPicksWithActive(picks)) {\n            items = picks.items;\n            activeItem = picks.active;\n          } else {\n            items = picks;\n          }\n          if (items.length === 0) {\n            if (skipEmpty) {\n              return false;\n            }\n            // We show the no results pick if we have no input to prevent completely empty pickers #172613\n            if ((picksFilter.length > 0 || picker.hideInput) && _this.options?.noResultsPick) {\n              if (isFunction(_this.options.noResultsPick)) {\n                items = [_this.options.noResultsPick(picksFilter)];\n              } else {\n                items = [_this.options.noResultsPick];\n              }\n            }\n          }\n          picker.items = items;\n          if (activeItem) {\n            picker.activeItems = [activeItem];\n          }\n          return true;\n        };\n        const applyFastAndSlowPicks = /*#__PURE__*/function () {\n          var _ref2 = _asyncToGenerator(function* (fastAndSlowPicks) {\n            let fastPicksApplied = false;\n            let slowPicksApplied = false;\n            yield Promise.all([\n            // Fast Picks: if `mergeDelay` is configured, in order to reduce\n            // amount of flicker, we race against the slow picks over some delay\n            // and then set the fast picks.\n            // If the slow picks are faster, we reduce the flicker by only\n            // setting the items once.\n            _asyncToGenerator(function* () {\n              if (typeof fastAndSlowPicks.mergeDelay === 'number') {\n                yield timeout(fastAndSlowPicks.mergeDelay);\n                if (picksToken.isCancellationRequested) {\n                  return;\n                }\n              }\n              if (!slowPicksApplied) {\n                fastPicksApplied = applyPicks(fastAndSlowPicks.picks, true /* skip over empty to reduce flicker */);\n              }\n            })(),\n            // Slow Picks: we await the slow picks and then set them at\n            // once together with the fast picks, but only if we actually\n            // have additional results.\n            _asyncToGenerator(function* () {\n              picker.busy = true;\n              try {\n                const awaitedAdditionalPicks = yield fastAndSlowPicks.additionalPicks;\n                if (picksToken.isCancellationRequested) {\n                  return;\n                }\n                let picks;\n                let activePick = undefined;\n                if (isPicksWithActive(fastAndSlowPicks.picks)) {\n                  picks = fastAndSlowPicks.picks.items;\n                  activePick = fastAndSlowPicks.picks.active;\n                } else {\n                  picks = fastAndSlowPicks.picks;\n                }\n                let additionalPicks;\n                let additionalActivePick = undefined;\n                if (isPicksWithActive(awaitedAdditionalPicks)) {\n                  additionalPicks = awaitedAdditionalPicks.items;\n                  additionalActivePick = awaitedAdditionalPicks.active;\n                } else {\n                  additionalPicks = awaitedAdditionalPicks;\n                }\n                if (additionalPicks.length > 0 || !fastPicksApplied) {\n                  // If we do not have any activePick or additionalActivePick\n                  // we try to preserve the currently active pick from the\n                  // fast results. This fixes an issue where the user might\n                  // have made a pick active before the additional results\n                  // kick in.\n                  // See https://github.com/microsoft/vscode/issues/102480\n                  let fallbackActivePick = undefined;\n                  if (!activePick && !additionalActivePick) {\n                    const fallbackActivePickCandidate = picker.activeItems[0];\n                    if (fallbackActivePickCandidate && picks.indexOf(fallbackActivePickCandidate) !== -1) {\n                      fallbackActivePick = fallbackActivePickCandidate;\n                    }\n                  }\n                  applyPicks({\n                    items: [...picks, ...additionalPicks],\n                    active: activePick || additionalActivePick || fallbackActivePick\n                  });\n                }\n              } finally {\n                if (!picksToken.isCancellationRequested) {\n                  picker.busy = false;\n                }\n                slowPicksApplied = true;\n              }\n            })()]);\n          });\n          return function applyFastAndSlowPicks(_x) {\n            return _ref2.apply(this, arguments);\n          };\n        }();\n        // No Picks\n        if (providedPicks === null) {\n          // Ignore\n        }\n        // Fast and Slow Picks\n        else if (isFastAndSlowPicks(providedPicks)) {\n          yield applyFastAndSlowPicks(providedPicks);\n        }\n        // Fast Picks\n        else if (!(providedPicks instanceof Promise)) {\n          applyPicks(providedPicks);\n        }\n        // Slow Picks\n        else {\n          picker.busy = true;\n          try {\n            const awaitedPicks = yield providedPicks;\n            if (picksToken.isCancellationRequested) {\n              return;\n            }\n            if (isFastAndSlowPicks(awaitedPicks)) {\n              yield applyFastAndSlowPicks(awaitedPicks);\n            } else {\n              applyPicks(awaitedPicks);\n            }\n          } finally {\n            if (!picksToken.isCancellationRequested) {\n              picker.busy = false;\n            }\n          }\n        }\n      });\n      return function updatePickerItems() {\n        return _ref.apply(this, arguments);\n      };\n    }();\n    disposables.add(picker.onDidChangeValue(() => updatePickerItems()));\n    updatePickerItems();\n    // Accept the pick on accept and hide picker\n    disposables.add(picker.onDidAccept(event => {\n      if (runOptions?.handleAccept) {\n        if (!event.inBackground) {\n          picker.hide(); // hide picker unless we accept in background\n        }\n        runOptions.handleAccept?.(picker.activeItems[0]);\n        return;\n      }\n      const [item] = picker.selectedItems;\n      if (typeof item?.accept === 'function') {\n        if (!event.inBackground) {\n          picker.hide(); // hide picker unless we accept in background\n        }\n        item.accept(picker.keyMods, event);\n      }\n    }));\n    const buttonTrigger = /*#__PURE__*/function () {\n      var _ref5 = _asyncToGenerator(function* (button, item) {\n        if (typeof item.trigger !== 'function') {\n          return;\n        }\n        const buttonIndex = item.buttons?.indexOf(button) ?? -1;\n        if (buttonIndex >= 0) {\n          const result = item.trigger(buttonIndex, picker.keyMods);\n          const action = typeof result === 'number' ? result : yield result;\n          if (token.isCancellationRequested) {\n            return;\n          }\n          switch (action) {\n            case TriggerAction.NO_ACTION:\n              break;\n            case TriggerAction.CLOSE_PICKER:\n              picker.hide();\n              break;\n            case TriggerAction.REFRESH_PICKER:\n              updatePickerItems();\n              break;\n            case TriggerAction.REMOVE_ITEM:\n              {\n                const index = picker.items.indexOf(item);\n                if (index !== -1) {\n                  const items = picker.items.slice();\n                  const removed = items.splice(index, 1);\n                  const activeItems = picker.activeItems.filter(activeItem => activeItem !== removed[0]);\n                  const keepScrollPositionBefore = picker.keepScrollPosition;\n                  picker.keepScrollPosition = true;\n                  picker.items = items;\n                  if (activeItems) {\n                    picker.activeItems = activeItems;\n                  }\n                  picker.keepScrollPosition = keepScrollPositionBefore;\n                }\n                break;\n              }\n          }\n        }\n      });\n      return function buttonTrigger(_x2, _x3) {\n        return _ref5.apply(this, arguments);\n      };\n    }();\n    // Trigger the pick with button index if button triggered\n    disposables.add(picker.onDidTriggerItemButton(({\n      button,\n      item\n    }) => buttonTrigger(button, item)));\n    disposables.add(picker.onDidTriggerSeparatorButton(({\n      button,\n      separator\n    }) => buttonTrigger(button, separator)));\n    return disposables;\n  }\n}", "map": {"version": 3, "names": ["timeout", "CancellationTokenSource", "Disposable", "DisposableStore", "MutableDisposable", "isFunction", "TriggerAction", "isPicksWithActive", "obj", "candidate", "Array", "isArray", "items", "isFastAndSlowPicks", "picks", "additionalPicks", "Promise", "PickerQuickAccessProvider", "constructor", "prefix", "options", "provide", "picker", "token", "runOptions", "_this", "disposables", "canAcceptInBackground", "matchOnLabel", "matchOnDescription", "matchOnDetail", "sortByLabel", "picksCts", "undefined", "picksDisposable", "add", "updatePickerItems", "_ref", "_asyncToGenerator", "picksDisposables", "value", "dispose", "busy", "picksToken", "picksFilter", "substring", "length", "shouldSkipTrimPickFilter", "trim", "providedPicks", "_get<PERSON>icks", "applyPicks", "<PERSON><PERSON><PERSON><PERSON>", "activeItem", "active", "hideInput", "noResultsPick", "activeItems", "applyFastAndSlowPicks", "_ref2", "fastAndSlowPicks", "fastPicksApplied", "slowPicksApplied", "all", "mergeDelay", "isCancellationRequested", "awaitedAdditionalPicks", "activePick", "additionalActivePick", "fallbackActivePick", "fallbackActivePickCandidate", "indexOf", "_x", "apply", "arguments", "awaitedPicks", "onDidChangeValue", "onDidAccept", "event", "handleAccept", "inBackground", "hide", "item", "selectedItems", "accept", "keyMods", "buttonTrigger", "_ref5", "button", "trigger", "buttonIndex", "buttons", "result", "action", "NO_ACTION", "CLOSE_PICKER", "REFRESH_PICKER", "REMOVE_ITEM", "index", "slice", "removed", "splice", "filter", "keepScrollPositionBefore", "keepScrollPosition", "_x2", "_x3", "onDidTriggerItemButton", "onDidTriggerSeparatorButton", "separator"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/platform/quickinput/browser/pickerQuickAccess.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { timeout } from '../../../base/common/async.js';\nimport { CancellationTokenSource } from '../../../base/common/cancellation.js';\nimport { Disposable, DisposableStore, MutableDisposable } from '../../../base/common/lifecycle.js';\nimport { isFunction } from '../../../base/common/types.js';\nexport var TriggerAction;\n(function (TriggerAction) {\n    /**\n     * Do nothing after the button was clicked.\n     */\n    TriggerAction[TriggerAction[\"NO_ACTION\"] = 0] = \"NO_ACTION\";\n    /**\n     * Close the picker.\n     */\n    TriggerAction[TriggerAction[\"CLOSE_PICKER\"] = 1] = \"CLOSE_PICKER\";\n    /**\n     * Update the results of the picker.\n     */\n    TriggerAction[TriggerAction[\"REFRESH_PICKER\"] = 2] = \"REFRESH_PICKER\";\n    /**\n     * Remove the item from the picker.\n     */\n    TriggerAction[TriggerAction[\"REMOVE_ITEM\"] = 3] = \"REMOVE_ITEM\";\n})(TriggerAction || (TriggerAction = {}));\nfunction isPicksWithActive(obj) {\n    const candidate = obj;\n    return Array.isArray(candidate.items);\n}\nfunction isFastAndSlowPicks(obj) {\n    const candidate = obj;\n    return !!candidate.picks && candidate.additionalPicks instanceof Promise;\n}\nexport class PickerQuickAccessProvider extends Disposable {\n    constructor(prefix, options) {\n        super();\n        this.prefix = prefix;\n        this.options = options;\n    }\n    provide(picker, token, runOptions) {\n        const disposables = new DisposableStore();\n        // Apply options if any\n        picker.canAcceptInBackground = !!this.options?.canAcceptInBackground;\n        // Disable filtering & sorting, we control the results\n        picker.matchOnLabel = picker.matchOnDescription = picker.matchOnDetail = picker.sortByLabel = false;\n        // Set initial picks and update on type\n        let picksCts = undefined;\n        const picksDisposable = disposables.add(new MutableDisposable());\n        const updatePickerItems = async () => {\n            const picksDisposables = picksDisposable.value = new DisposableStore();\n            // Cancel any previous ask for picks and busy\n            picksCts?.dispose(true);\n            picker.busy = false;\n            // Create new cancellation source for this run\n            picksCts = new CancellationTokenSource(token);\n            // Collect picks and support both long running and short or combined\n            const picksToken = picksCts.token;\n            let picksFilter = picker.value.substring(this.prefix.length);\n            if (!this.options?.shouldSkipTrimPickFilter) {\n                picksFilter = picksFilter.trim();\n            }\n            const providedPicks = this._getPicks(picksFilter, picksDisposables, picksToken, runOptions);\n            const applyPicks = (picks, skipEmpty) => {\n                let items;\n                let activeItem = undefined;\n                if (isPicksWithActive(picks)) {\n                    items = picks.items;\n                    activeItem = picks.active;\n                }\n                else {\n                    items = picks;\n                }\n                if (items.length === 0) {\n                    if (skipEmpty) {\n                        return false;\n                    }\n                    // We show the no results pick if we have no input to prevent completely empty pickers #172613\n                    if ((picksFilter.length > 0 || picker.hideInput) && this.options?.noResultsPick) {\n                        if (isFunction(this.options.noResultsPick)) {\n                            items = [this.options.noResultsPick(picksFilter)];\n                        }\n                        else {\n                            items = [this.options.noResultsPick];\n                        }\n                    }\n                }\n                picker.items = items;\n                if (activeItem) {\n                    picker.activeItems = [activeItem];\n                }\n                return true;\n            };\n            const applyFastAndSlowPicks = async (fastAndSlowPicks) => {\n                let fastPicksApplied = false;\n                let slowPicksApplied = false;\n                await Promise.all([\n                    // Fast Picks: if `mergeDelay` is configured, in order to reduce\n                    // amount of flicker, we race against the slow picks over some delay\n                    // and then set the fast picks.\n                    // If the slow picks are faster, we reduce the flicker by only\n                    // setting the items once.\n                    (async () => {\n                        if (typeof fastAndSlowPicks.mergeDelay === 'number') {\n                            await timeout(fastAndSlowPicks.mergeDelay);\n                            if (picksToken.isCancellationRequested) {\n                                return;\n                            }\n                        }\n                        if (!slowPicksApplied) {\n                            fastPicksApplied = applyPicks(fastAndSlowPicks.picks, true /* skip over empty to reduce flicker */);\n                        }\n                    })(),\n                    // Slow Picks: we await the slow picks and then set them at\n                    // once together with the fast picks, but only if we actually\n                    // have additional results.\n                    (async () => {\n                        picker.busy = true;\n                        try {\n                            const awaitedAdditionalPicks = await fastAndSlowPicks.additionalPicks;\n                            if (picksToken.isCancellationRequested) {\n                                return;\n                            }\n                            let picks;\n                            let activePick = undefined;\n                            if (isPicksWithActive(fastAndSlowPicks.picks)) {\n                                picks = fastAndSlowPicks.picks.items;\n                                activePick = fastAndSlowPicks.picks.active;\n                            }\n                            else {\n                                picks = fastAndSlowPicks.picks;\n                            }\n                            let additionalPicks;\n                            let additionalActivePick = undefined;\n                            if (isPicksWithActive(awaitedAdditionalPicks)) {\n                                additionalPicks = awaitedAdditionalPicks.items;\n                                additionalActivePick = awaitedAdditionalPicks.active;\n                            }\n                            else {\n                                additionalPicks = awaitedAdditionalPicks;\n                            }\n                            if (additionalPicks.length > 0 || !fastPicksApplied) {\n                                // If we do not have any activePick or additionalActivePick\n                                // we try to preserve the currently active pick from the\n                                // fast results. This fixes an issue where the user might\n                                // have made a pick active before the additional results\n                                // kick in.\n                                // See https://github.com/microsoft/vscode/issues/102480\n                                let fallbackActivePick = undefined;\n                                if (!activePick && !additionalActivePick) {\n                                    const fallbackActivePickCandidate = picker.activeItems[0];\n                                    if (fallbackActivePickCandidate && picks.indexOf(fallbackActivePickCandidate) !== -1) {\n                                        fallbackActivePick = fallbackActivePickCandidate;\n                                    }\n                                }\n                                applyPicks({\n                                    items: [...picks, ...additionalPicks],\n                                    active: activePick || additionalActivePick || fallbackActivePick\n                                });\n                            }\n                        }\n                        finally {\n                            if (!picksToken.isCancellationRequested) {\n                                picker.busy = false;\n                            }\n                            slowPicksApplied = true;\n                        }\n                    })()\n                ]);\n            };\n            // No Picks\n            if (providedPicks === null) {\n                // Ignore\n            }\n            // Fast and Slow Picks\n            else if (isFastAndSlowPicks(providedPicks)) {\n                await applyFastAndSlowPicks(providedPicks);\n            }\n            // Fast Picks\n            else if (!(providedPicks instanceof Promise)) {\n                applyPicks(providedPicks);\n            }\n            // Slow Picks\n            else {\n                picker.busy = true;\n                try {\n                    const awaitedPicks = await providedPicks;\n                    if (picksToken.isCancellationRequested) {\n                        return;\n                    }\n                    if (isFastAndSlowPicks(awaitedPicks)) {\n                        await applyFastAndSlowPicks(awaitedPicks);\n                    }\n                    else {\n                        applyPicks(awaitedPicks);\n                    }\n                }\n                finally {\n                    if (!picksToken.isCancellationRequested) {\n                        picker.busy = false;\n                    }\n                }\n            }\n        };\n        disposables.add(picker.onDidChangeValue(() => updatePickerItems()));\n        updatePickerItems();\n        // Accept the pick on accept and hide picker\n        disposables.add(picker.onDidAccept(event => {\n            if (runOptions?.handleAccept) {\n                if (!event.inBackground) {\n                    picker.hide(); // hide picker unless we accept in background\n                }\n                runOptions.handleAccept?.(picker.activeItems[0]);\n                return;\n            }\n            const [item] = picker.selectedItems;\n            if (typeof item?.accept === 'function') {\n                if (!event.inBackground) {\n                    picker.hide(); // hide picker unless we accept in background\n                }\n                item.accept(picker.keyMods, event);\n            }\n        }));\n        const buttonTrigger = async (button, item) => {\n            if (typeof item.trigger !== 'function') {\n                return;\n            }\n            const buttonIndex = item.buttons?.indexOf(button) ?? -1;\n            if (buttonIndex >= 0) {\n                const result = item.trigger(buttonIndex, picker.keyMods);\n                const action = (typeof result === 'number') ? result : await result;\n                if (token.isCancellationRequested) {\n                    return;\n                }\n                switch (action) {\n                    case TriggerAction.NO_ACTION:\n                        break;\n                    case TriggerAction.CLOSE_PICKER:\n                        picker.hide();\n                        break;\n                    case TriggerAction.REFRESH_PICKER:\n                        updatePickerItems();\n                        break;\n                    case TriggerAction.REMOVE_ITEM: {\n                        const index = picker.items.indexOf(item);\n                        if (index !== -1) {\n                            const items = picker.items.slice();\n                            const removed = items.splice(index, 1);\n                            const activeItems = picker.activeItems.filter(activeItem => activeItem !== removed[0]);\n                            const keepScrollPositionBefore = picker.keepScrollPosition;\n                            picker.keepScrollPosition = true;\n                            picker.items = items;\n                            if (activeItems) {\n                                picker.activeItems = activeItems;\n                            }\n                            picker.keepScrollPosition = keepScrollPositionBefore;\n                        }\n                        break;\n                    }\n                }\n            }\n        };\n        // Trigger the pick with button index if button triggered\n        disposables.add(picker.onDidTriggerItemButton(({ button, item }) => buttonTrigger(button, item)));\n        disposables.add(picker.onDidTriggerSeparatorButton(({ button, separator }) => buttonTrigger(button, separator)));\n        return disposables;\n    }\n}\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA,SAASA,OAAO,QAAQ,+BAA+B;AACvD,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,UAAU,EAAEC,eAAe,EAAEC,iBAAiB,QAAQ,mCAAmC;AAClG,SAASC,UAAU,QAAQ,+BAA+B;AAC1D,OAAO,IAAIC,aAAa,gBACvB,UAAUA,aAAa,EAAE;EACtB;AACJ;AACA;EACIA,aAAa,CAACA,aAAa,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW;EAC3D;AACJ;AACA;EACIA,aAAa,CAACA,aAAa,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,GAAG,cAAc;EACjE;AACJ;AACA;EACIA,aAAa,CAACA,aAAa,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,GAAG,gBAAgB;EACrE;AACJ;AACA;EACIA,aAAa,CAACA,aAAa,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,GAAG,aAAa;EAAC,OAhBzDA,aAAa;AAiBxB,CAAC,CAAEA,aAAa,IAAqB,CAAC,CAAE,CAlBhB;AAmBxB,SAASC,iBAAiBA,CAACC,GAAG,EAAE;EAC5B,MAAMC,SAAS,GAAGD,GAAG;EACrB,OAAOE,KAAK,CAACC,OAAO,CAACF,SAAS,CAACG,KAAK,CAAC;AACzC;AACA,SAASC,kBAAkBA,CAACL,GAAG,EAAE;EAC7B,MAAMC,SAAS,GAAGD,GAAG;EACrB,OAAO,CAAC,CAACC,SAAS,CAACK,KAAK,IAAIL,SAAS,CAACM,eAAe,YAAYC,OAAO;AAC5E;AACA,OAAO,MAAMC,yBAAyB,SAASf,UAAU,CAAC;EACtDgB,WAAWA,CAACC,MAAM,EAAEC,OAAO,EAAE;IACzB,KAAK,CAAC,CAAC;IACP,IAAI,CAACD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,OAAO,GAAGA,OAAO;EAC1B;EACAC,OAAOA,CAACC,MAAM,EAAEC,KAAK,EAAEC,UAAU,EAAE;IAAA,IAAAC,KAAA;IAC/B,MAAMC,WAAW,GAAG,IAAIvB,eAAe,CAAC,CAAC;IACzC;IACAmB,MAAM,CAACK,qBAAqB,GAAG,CAAC,CAAC,IAAI,CAACP,OAAO,EAAEO,qBAAqB;IACpE;IACAL,MAAM,CAACM,YAAY,GAAGN,MAAM,CAACO,kBAAkB,GAAGP,MAAM,CAACQ,aAAa,GAAGR,MAAM,CAACS,WAAW,GAAG,KAAK;IACnG;IACA,IAAIC,QAAQ,GAAGC,SAAS;IACxB,MAAMC,eAAe,GAAGR,WAAW,CAACS,GAAG,CAAC,IAAI/B,iBAAiB,CAAC,CAAC,CAAC;IAChE,MAAMgC,iBAAiB;MAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,aAAY;QAClC,MAAMC,gBAAgB,GAAGL,eAAe,CAACM,KAAK,GAAG,IAAIrC,eAAe,CAAC,CAAC;QACtE;QACA6B,QAAQ,EAAES,OAAO,CAAC,IAAI,CAAC;QACvBnB,MAAM,CAACoB,IAAI,GAAG,KAAK;QACnB;QACAV,QAAQ,GAAG,IAAI/B,uBAAuB,CAACsB,KAAK,CAAC;QAC7C;QACA,MAAMoB,UAAU,GAAGX,QAAQ,CAACT,KAAK;QACjC,IAAIqB,WAAW,GAAGtB,MAAM,CAACkB,KAAK,CAACK,SAAS,CAACpB,KAAI,CAACN,MAAM,CAAC2B,MAAM,CAAC;QAC5D,IAAI,CAACrB,KAAI,CAACL,OAAO,EAAE2B,wBAAwB,EAAE;UACzCH,WAAW,GAAGA,WAAW,CAACI,IAAI,CAAC,CAAC;QACpC;QACA,MAAMC,aAAa,GAAGxB,KAAI,CAACyB,SAAS,CAACN,WAAW,EAAEL,gBAAgB,EAAEI,UAAU,EAAEnB,UAAU,CAAC;QAC3F,MAAM2B,UAAU,GAAGA,CAACrC,KAAK,EAAEsC,SAAS,KAAK;UACrC,IAAIxC,KAAK;UACT,IAAIyC,UAAU,GAAGpB,SAAS;UAC1B,IAAI1B,iBAAiB,CAACO,KAAK,CAAC,EAAE;YAC1BF,KAAK,GAAGE,KAAK,CAACF,KAAK;YACnByC,UAAU,GAAGvC,KAAK,CAACwC,MAAM;UAC7B,CAAC,MACI;YACD1C,KAAK,GAAGE,KAAK;UACjB;UACA,IAAIF,KAAK,CAACkC,MAAM,KAAK,CAAC,EAAE;YACpB,IAAIM,SAAS,EAAE;cACX,OAAO,KAAK;YAChB;YACA;YACA,IAAI,CAACR,WAAW,CAACE,MAAM,GAAG,CAAC,IAAIxB,MAAM,CAACiC,SAAS,KAAK9B,KAAI,CAACL,OAAO,EAAEoC,aAAa,EAAE;cAC7E,IAAInD,UAAU,CAACoB,KAAI,CAACL,OAAO,CAACoC,aAAa,CAAC,EAAE;gBACxC5C,KAAK,GAAG,CAACa,KAAI,CAACL,OAAO,CAACoC,aAAa,CAACZ,WAAW,CAAC,CAAC;cACrD,CAAC,MACI;gBACDhC,KAAK,GAAG,CAACa,KAAI,CAACL,OAAO,CAACoC,aAAa,CAAC;cACxC;YACJ;UACJ;UACAlC,MAAM,CAACV,KAAK,GAAGA,KAAK;UACpB,IAAIyC,UAAU,EAAE;YACZ/B,MAAM,CAACmC,WAAW,GAAG,CAACJ,UAAU,CAAC;UACrC;UACA,OAAO,IAAI;QACf,CAAC;QACD,MAAMK,qBAAqB;UAAA,IAAAC,KAAA,GAAArB,iBAAA,CAAG,WAAOsB,gBAAgB,EAAK;YACtD,IAAIC,gBAAgB,GAAG,KAAK;YAC5B,IAAIC,gBAAgB,GAAG,KAAK;YAC5B,MAAM9C,OAAO,CAAC+C,GAAG,CAAC;YACd;YACA;YACA;YACA;YACA;YACAzB,iBAAA,CAAC,aAAY;cACT,IAAI,OAAOsB,gBAAgB,CAACI,UAAU,KAAK,QAAQ,EAAE;gBACjD,MAAMhE,OAAO,CAAC4D,gBAAgB,CAACI,UAAU,CAAC;gBAC1C,IAAIrB,UAAU,CAACsB,uBAAuB,EAAE;kBACpC;gBACJ;cACJ;cACA,IAAI,CAACH,gBAAgB,EAAE;gBACnBD,gBAAgB,GAAGV,UAAU,CAACS,gBAAgB,CAAC9C,KAAK,EAAE,IAAI,CAAC,uCAAuC,CAAC;cACvG;YACJ,CAAC,EAAE,CAAC;YACJ;YACA;YACA;YACAwB,iBAAA,CAAC,aAAY;cACThB,MAAM,CAACoB,IAAI,GAAG,IAAI;cAClB,IAAI;gBACA,MAAMwB,sBAAsB,SAASN,gBAAgB,CAAC7C,eAAe;gBACrE,IAAI4B,UAAU,CAACsB,uBAAuB,EAAE;kBACpC;gBACJ;gBACA,IAAInD,KAAK;gBACT,IAAIqD,UAAU,GAAGlC,SAAS;gBAC1B,IAAI1B,iBAAiB,CAACqD,gBAAgB,CAAC9C,KAAK,CAAC,EAAE;kBAC3CA,KAAK,GAAG8C,gBAAgB,CAAC9C,KAAK,CAACF,KAAK;kBACpCuD,UAAU,GAAGP,gBAAgB,CAAC9C,KAAK,CAACwC,MAAM;gBAC9C,CAAC,MACI;kBACDxC,KAAK,GAAG8C,gBAAgB,CAAC9C,KAAK;gBAClC;gBACA,IAAIC,eAAe;gBACnB,IAAIqD,oBAAoB,GAAGnC,SAAS;gBACpC,IAAI1B,iBAAiB,CAAC2D,sBAAsB,CAAC,EAAE;kBAC3CnD,eAAe,GAAGmD,sBAAsB,CAACtD,KAAK;kBAC9CwD,oBAAoB,GAAGF,sBAAsB,CAACZ,MAAM;gBACxD,CAAC,MACI;kBACDvC,eAAe,GAAGmD,sBAAsB;gBAC5C;gBACA,IAAInD,eAAe,CAAC+B,MAAM,GAAG,CAAC,IAAI,CAACe,gBAAgB,EAAE;kBACjD;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA,IAAIQ,kBAAkB,GAAGpC,SAAS;kBAClC,IAAI,CAACkC,UAAU,IAAI,CAACC,oBAAoB,EAAE;oBACtC,MAAME,2BAA2B,GAAGhD,MAAM,CAACmC,WAAW,CAAC,CAAC,CAAC;oBACzD,IAAIa,2BAA2B,IAAIxD,KAAK,CAACyD,OAAO,CAACD,2BAA2B,CAAC,KAAK,CAAC,CAAC,EAAE;sBAClFD,kBAAkB,GAAGC,2BAA2B;oBACpD;kBACJ;kBACAnB,UAAU,CAAC;oBACPvC,KAAK,EAAE,CAAC,GAAGE,KAAK,EAAE,GAAGC,eAAe,CAAC;oBACrCuC,MAAM,EAAEa,UAAU,IAAIC,oBAAoB,IAAIC;kBAClD,CAAC,CAAC;gBACN;cACJ,CAAC,SACO;gBACJ,IAAI,CAAC1B,UAAU,CAACsB,uBAAuB,EAAE;kBACrC3C,MAAM,CAACoB,IAAI,GAAG,KAAK;gBACvB;gBACAoB,gBAAgB,GAAG,IAAI;cAC3B;YACJ,CAAC,EAAE,CAAC,CACP,CAAC;UACN,CAAC;UAAA,gBA5EKJ,qBAAqBA,CAAAc,EAAA;YAAA,OAAAb,KAAA,CAAAc,KAAA,OAAAC,SAAA;UAAA;QAAA,GA4E1B;QACD;QACA,IAAIzB,aAAa,KAAK,IAAI,EAAE;UACxB;QAAA;QAEJ;QAAA,KACK,IAAIpC,kBAAkB,CAACoC,aAAa,CAAC,EAAE;UACxC,MAAMS,qBAAqB,CAACT,aAAa,CAAC;QAC9C;QACA;QAAA,KACK,IAAI,EAAEA,aAAa,YAAYjC,OAAO,CAAC,EAAE;UAC1CmC,UAAU,CAACF,aAAa,CAAC;QAC7B;QACA;QAAA,KACK;UACD3B,MAAM,CAACoB,IAAI,GAAG,IAAI;UAClB,IAAI;YACA,MAAMiC,YAAY,SAAS1B,aAAa;YACxC,IAAIN,UAAU,CAACsB,uBAAuB,EAAE;cACpC;YACJ;YACA,IAAIpD,kBAAkB,CAAC8D,YAAY,CAAC,EAAE;cAClC,MAAMjB,qBAAqB,CAACiB,YAAY,CAAC;YAC7C,CAAC,MACI;cACDxB,UAAU,CAACwB,YAAY,CAAC;YAC5B;UACJ,CAAC,SACO;YACJ,IAAI,CAAChC,UAAU,CAACsB,uBAAuB,EAAE;cACrC3C,MAAM,CAACoB,IAAI,GAAG,KAAK;YACvB;UACJ;QACJ;MACJ,CAAC;MAAA,gBA1JKN,iBAAiBA,CAAA;QAAA,OAAAC,IAAA,CAAAoC,KAAA,OAAAC,SAAA;MAAA;IAAA,GA0JtB;IACDhD,WAAW,CAACS,GAAG,CAACb,MAAM,CAACsD,gBAAgB,CAAC,MAAMxC,iBAAiB,CAAC,CAAC,CAAC,CAAC;IACnEA,iBAAiB,CAAC,CAAC;IACnB;IACAV,WAAW,CAACS,GAAG,CAACb,MAAM,CAACuD,WAAW,CAACC,KAAK,IAAI;MACxC,IAAItD,UAAU,EAAEuD,YAAY,EAAE;QAC1B,IAAI,CAACD,KAAK,CAACE,YAAY,EAAE;UACrB1D,MAAM,CAAC2D,IAAI,CAAC,CAAC,CAAC,CAAC;QACnB;QACAzD,UAAU,CAACuD,YAAY,GAAGzD,MAAM,CAACmC,WAAW,CAAC,CAAC,CAAC,CAAC;QAChD;MACJ;MACA,MAAM,CAACyB,IAAI,CAAC,GAAG5D,MAAM,CAAC6D,aAAa;MACnC,IAAI,OAAOD,IAAI,EAAEE,MAAM,KAAK,UAAU,EAAE;QACpC,IAAI,CAACN,KAAK,CAACE,YAAY,EAAE;UACrB1D,MAAM,CAAC2D,IAAI,CAAC,CAAC,CAAC,CAAC;QACnB;QACAC,IAAI,CAACE,MAAM,CAAC9D,MAAM,CAAC+D,OAAO,EAAEP,KAAK,CAAC;MACtC;IACJ,CAAC,CAAC,CAAC;IACH,MAAMQ,aAAa;MAAA,IAAAC,KAAA,GAAAjD,iBAAA,CAAG,WAAOkD,MAAM,EAAEN,IAAI,EAAK;QAC1C,IAAI,OAAOA,IAAI,CAACO,OAAO,KAAK,UAAU,EAAE;UACpC;QACJ;QACA,MAAMC,WAAW,GAAGR,IAAI,CAACS,OAAO,EAAEpB,OAAO,CAACiB,MAAM,CAAC,IAAI,CAAC,CAAC;QACvD,IAAIE,WAAW,IAAI,CAAC,EAAE;UAClB,MAAME,MAAM,GAAGV,IAAI,CAACO,OAAO,CAACC,WAAW,EAAEpE,MAAM,CAAC+D,OAAO,CAAC;UACxD,MAAMQ,MAAM,GAAI,OAAOD,MAAM,KAAK,QAAQ,GAAIA,MAAM,SAASA,MAAM;UACnE,IAAIrE,KAAK,CAAC0C,uBAAuB,EAAE;YAC/B;UACJ;UACA,QAAQ4B,MAAM;YACV,KAAKvF,aAAa,CAACwF,SAAS;cACxB;YACJ,KAAKxF,aAAa,CAACyF,YAAY;cAC3BzE,MAAM,CAAC2D,IAAI,CAAC,CAAC;cACb;YACJ,KAAK3E,aAAa,CAAC0F,cAAc;cAC7B5D,iBAAiB,CAAC,CAAC;cACnB;YACJ,KAAK9B,aAAa,CAAC2F,WAAW;cAAE;gBAC5B,MAAMC,KAAK,GAAG5E,MAAM,CAACV,KAAK,CAAC2D,OAAO,CAACW,IAAI,CAAC;gBACxC,IAAIgB,KAAK,KAAK,CAAC,CAAC,EAAE;kBACd,MAAMtF,KAAK,GAAGU,MAAM,CAACV,KAAK,CAACuF,KAAK,CAAC,CAAC;kBAClC,MAAMC,OAAO,GAAGxF,KAAK,CAACyF,MAAM,CAACH,KAAK,EAAE,CAAC,CAAC;kBACtC,MAAMzC,WAAW,GAAGnC,MAAM,CAACmC,WAAW,CAAC6C,MAAM,CAACjD,UAAU,IAAIA,UAAU,KAAK+C,OAAO,CAAC,CAAC,CAAC,CAAC;kBACtF,MAAMG,wBAAwB,GAAGjF,MAAM,CAACkF,kBAAkB;kBAC1DlF,MAAM,CAACkF,kBAAkB,GAAG,IAAI;kBAChClF,MAAM,CAACV,KAAK,GAAGA,KAAK;kBACpB,IAAI6C,WAAW,EAAE;oBACbnC,MAAM,CAACmC,WAAW,GAAGA,WAAW;kBACpC;kBACAnC,MAAM,CAACkF,kBAAkB,GAAGD,wBAAwB;gBACxD;gBACA;cACJ;UACJ;QACJ;MACJ,CAAC;MAAA,gBAtCKjB,aAAaA,CAAAmB,GAAA,EAAAC,GAAA;QAAA,OAAAnB,KAAA,CAAAd,KAAA,OAAAC,SAAA;MAAA;IAAA,GAsClB;IACD;IACAhD,WAAW,CAACS,GAAG,CAACb,MAAM,CAACqF,sBAAsB,CAAC,CAAC;MAAEnB,MAAM;MAAEN;IAAK,CAAC,KAAKI,aAAa,CAACE,MAAM,EAAEN,IAAI,CAAC,CAAC,CAAC;IACjGxD,WAAW,CAACS,GAAG,CAACb,MAAM,CAACsF,2BAA2B,CAAC,CAAC;MAAEpB,MAAM;MAAEqB;IAAU,CAAC,KAAKvB,aAAa,CAACE,MAAM,EAAEqB,SAAS,CAAC,CAAC,CAAC;IAChH,OAAOnF,WAAW;EACtB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}