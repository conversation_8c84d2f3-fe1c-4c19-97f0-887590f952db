{"ast": null, "code": "export class ObjectPool {\n  constructor(_create) {\n    this._create = _create;\n    this._unused = new Set();\n    this._used = new Set();\n    this._itemData = new Map();\n  }\n  getUnusedObj(data) {\n    let obj;\n    if (this._unused.size === 0) {\n      obj = this._create(data);\n      this._itemData.set(obj, data);\n    } else {\n      const values = [...this._unused.values()];\n      obj = values.find(obj => this._itemData.get(obj).getId() === data.getId()) ?? values[0];\n      this._unused.delete(obj);\n      this._itemData.set(obj, data);\n      obj.setData(data);\n    }\n    this._used.add(obj);\n    return {\n      object: obj,\n      dispose: () => {\n        this._used.delete(obj);\n        if (this._unused.size > 5) {\n          obj.dispose();\n        } else {\n          this._unused.add(obj);\n        }\n      }\n    };\n  }\n  dispose() {\n    for (const obj of this._used) {\n      obj.dispose();\n    }\n    for (const obj of this._unused) {\n      obj.dispose();\n    }\n    this._used.clear();\n    this._unused.clear();\n  }\n}", "map": {"version": 3, "names": ["ObjectPool", "constructor", "_create", "_unused", "Set", "_used", "_itemData", "Map", "getUnusedObj", "data", "obj", "size", "set", "values", "find", "get", "getId", "delete", "setData", "add", "object", "dispose", "clear"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/browser/widget/multiDiffEditor/objectPool.js"], "sourcesContent": ["export class ObjectPool {\n    constructor(_create) {\n        this._create = _create;\n        this._unused = new Set();\n        this._used = new Set();\n        this._itemData = new Map();\n    }\n    getUnusedObj(data) {\n        let obj;\n        if (this._unused.size === 0) {\n            obj = this._create(data);\n            this._itemData.set(obj, data);\n        }\n        else {\n            const values = [...this._unused.values()];\n            obj = values.find(obj => this._itemData.get(obj).getId() === data.getId()) ?? values[0];\n            this._unused.delete(obj);\n            this._itemData.set(obj, data);\n            obj.setData(data);\n        }\n        this._used.add(obj);\n        return {\n            object: obj,\n            dispose: () => {\n                this._used.delete(obj);\n                if (this._unused.size > 5) {\n                    obj.dispose();\n                }\n                else {\n                    this._unused.add(obj);\n                }\n            }\n        };\n    }\n    dispose() {\n        for (const obj of this._used) {\n            obj.dispose();\n        }\n        for (const obj of this._unused) {\n            obj.dispose();\n        }\n        this._used.clear();\n        this._unused.clear();\n    }\n}\n"], "mappings": "AAAA,OAAO,MAAMA,UAAU,CAAC;EACpBC,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,OAAO,GAAG,IAAIC,GAAG,CAAC,CAAC;IACxB,IAAI,CAACC,KAAK,GAAG,IAAID,GAAG,CAAC,CAAC;IACtB,IAAI,CAACE,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC9B;EACAC,YAAYA,CAACC,IAAI,EAAE;IACf,IAAIC,GAAG;IACP,IAAI,IAAI,CAACP,OAAO,CAACQ,IAAI,KAAK,CAAC,EAAE;MACzBD,GAAG,GAAG,IAAI,CAACR,OAAO,CAACO,IAAI,CAAC;MACxB,IAAI,CAACH,SAAS,CAACM,GAAG,CAACF,GAAG,EAAED,IAAI,CAAC;IACjC,CAAC,MACI;MACD,MAAMI,MAAM,GAAG,CAAC,GAAG,IAAI,CAACV,OAAO,CAACU,MAAM,CAAC,CAAC,CAAC;MACzCH,GAAG,GAAGG,MAAM,CAACC,IAAI,CAACJ,GAAG,IAAI,IAAI,CAACJ,SAAS,CAACS,GAAG,CAACL,GAAG,CAAC,CAACM,KAAK,CAAC,CAAC,KAAKP,IAAI,CAACO,KAAK,CAAC,CAAC,CAAC,IAAIH,MAAM,CAAC,CAAC,CAAC;MACvF,IAAI,CAACV,OAAO,CAACc,MAAM,CAACP,GAAG,CAAC;MACxB,IAAI,CAACJ,SAAS,CAACM,GAAG,CAACF,GAAG,EAAED,IAAI,CAAC;MAC7BC,GAAG,CAACQ,OAAO,CAACT,IAAI,CAAC;IACrB;IACA,IAAI,CAACJ,KAAK,CAACc,GAAG,CAACT,GAAG,CAAC;IACnB,OAAO;MACHU,MAAM,EAAEV,GAAG;MACXW,OAAO,EAAEA,CAAA,KAAM;QACX,IAAI,CAAChB,KAAK,CAACY,MAAM,CAACP,GAAG,CAAC;QACtB,IAAI,IAAI,CAACP,OAAO,CAACQ,IAAI,GAAG,CAAC,EAAE;UACvBD,GAAG,CAACW,OAAO,CAAC,CAAC;QACjB,CAAC,MACI;UACD,IAAI,CAAClB,OAAO,CAACgB,GAAG,CAACT,GAAG,CAAC;QACzB;MACJ;IACJ,CAAC;EACL;EACAW,OAAOA,CAAA,EAAG;IACN,KAAK,MAAMX,GAAG,IAAI,IAAI,CAACL,KAAK,EAAE;MAC1BK,GAAG,CAACW,OAAO,CAAC,CAAC;IACjB;IACA,KAAK,MAAMX,GAAG,IAAI,IAAI,CAACP,OAAO,EAAE;MAC5BO,GAAG,CAACW,OAAO,CAAC,CAAC;IACjB;IACA,IAAI,CAAChB,KAAK,CAACiB,KAAK,CAAC,CAAC;IAClB,IAAI,CAACnB,OAAO,CAACmB,KAAK,CAAC,CAAC;EACxB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}