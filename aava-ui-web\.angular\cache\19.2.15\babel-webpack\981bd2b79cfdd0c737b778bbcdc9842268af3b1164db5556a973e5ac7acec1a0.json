{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = this && this.__param || function (paramIndex, decorator) {\n  return function (target, key) {\n    decorator(target, key, paramIndex);\n  };\n};\nimport { CancellationToken } from '../../../base/common/cancellation.js';\nimport { Emitter } from '../../../base/common/event.js';\nimport { IContextKeyService, RawContextKey } from '../../contextkey/common/contextkey.js';\nimport { IInstantiationService } from '../../instantiation/common/instantiation.js';\nimport { ILayoutService } from '../../layout/browser/layoutService.js';\nimport { IOpenerService } from '../../opener/common/opener.js';\nimport { QuickAccessController } from './quickAccess.js';\nimport { defaultButtonStyles, defaultCountBadgeStyles, defaultInputBoxStyles, defaultKeybindingLabelStyles, defaultProgressBarStyles, defaultToggleStyles, getListStyles } from '../../theme/browser/defaultStyles.js';\nimport { activeContrastBorder, asCssVariable, pickerGroupBorder, pickerGroupForeground, quickInputBackground, quickInputForeground, quickInputListFocusBackground, quickInputListFocusForeground, quickInputListFocusIconForeground, quickInputTitleBackground, widgetBorder, widgetShadow } from '../../theme/common/colorRegistry.js';\nimport { IThemeService, Themable } from '../../theme/common/themeService.js';\nimport { QuickInputHoverDelegate } from './quickInput.js';\nimport { QuickInputController } from './quickInputController.js';\nimport { IConfigurationService } from '../../configuration/common/configuration.js';\nimport { getWindow } from '../../../base/browser/dom.js';\nlet QuickInputService = class QuickInputService extends Themable {\n  get controller() {\n    if (!this._controller) {\n      this._controller = this._register(this.createController());\n    }\n    return this._controller;\n  }\n  get hasController() {\n    return !!this._controller;\n  }\n  get currentQuickInput() {\n    return this.controller.currentQuickInput;\n  }\n  get quickAccess() {\n    if (!this._quickAccess) {\n      this._quickAccess = this._register(this.instantiationService.createInstance(QuickAccessController));\n    }\n    return this._quickAccess;\n  }\n  constructor(instantiationService, contextKeyService, themeService, layoutService, configurationService) {\n    super(themeService);\n    this.instantiationService = instantiationService;\n    this.contextKeyService = contextKeyService;\n    this.layoutService = layoutService;\n    this.configurationService = configurationService;\n    this._onShow = this._register(new Emitter());\n    this._onHide = this._register(new Emitter());\n    this.contexts = new Map();\n  }\n  createController(host = this.layoutService, options) {\n    const defaultOptions = {\n      idPrefix: 'quickInput_',\n      container: host.activeContainer,\n      ignoreFocusOut: () => false,\n      backKeybindingLabel: () => undefined,\n      setContextKey: id => this.setContextKey(id),\n      linkOpenerDelegate: content => {\n        // HACK: https://github.com/microsoft/vscode/issues/173691\n        this.instantiationService.invokeFunction(accessor => {\n          const openerService = accessor.get(IOpenerService);\n          openerService.open(content, {\n            allowCommands: true,\n            fromUserGesture: true\n          });\n        });\n      },\n      returnFocus: () => host.focus(),\n      styles: this.computeStyles(),\n      hoverDelegate: this._register(this.instantiationService.createInstance(QuickInputHoverDelegate))\n    };\n    const controller = this._register(this.instantiationService.createInstance(QuickInputController, {\n      ...defaultOptions,\n      ...options\n    }));\n    controller.layout(host.activeContainerDimension, host.activeContainerOffset.quickPickTop);\n    // Layout changes\n    this._register(host.onDidLayoutActiveContainer(dimension => {\n      if (getWindow(host.activeContainer) === getWindow(controller.container)) {\n        controller.layout(dimension, host.activeContainerOffset.quickPickTop);\n      }\n    }));\n    this._register(host.onDidChangeActiveContainer(() => {\n      if (controller.isVisible()) {\n        return;\n      }\n      controller.layout(host.activeContainerDimension, host.activeContainerOffset.quickPickTop);\n    }));\n    // Context keys\n    this._register(controller.onShow(() => {\n      this.resetContextKeys();\n      this._onShow.fire();\n    }));\n    this._register(controller.onHide(() => {\n      this.resetContextKeys();\n      this._onHide.fire();\n    }));\n    return controller;\n  }\n  setContextKey(id) {\n    let key;\n    if (id) {\n      key = this.contexts.get(id);\n      if (!key) {\n        key = new RawContextKey(id, false).bindTo(this.contextKeyService);\n        this.contexts.set(id, key);\n      }\n    }\n    if (key && key.get()) {\n      return; // already active context\n    }\n    this.resetContextKeys();\n    key?.set(true);\n  }\n  resetContextKeys() {\n    this.contexts.forEach(context => {\n      if (context.get()) {\n        context.reset();\n      }\n    });\n  }\n  pick(picks, options, token = CancellationToken.None) {\n    return this.controller.pick(picks, options, token);\n  }\n  createQuickPick(options = {\n    useSeparators: false\n  }) {\n    return this.controller.createQuickPick(options);\n  }\n  createInputBox() {\n    return this.controller.createInputBox();\n  }\n  updateStyles() {\n    if (this.hasController) {\n      this.controller.applyStyles(this.computeStyles());\n    }\n  }\n  computeStyles() {\n    return {\n      widget: {\n        quickInputBackground: asCssVariable(quickInputBackground),\n        quickInputForeground: asCssVariable(quickInputForeground),\n        quickInputTitleBackground: asCssVariable(quickInputTitleBackground),\n        widgetBorder: asCssVariable(widgetBorder),\n        widgetShadow: asCssVariable(widgetShadow)\n      },\n      inputBox: defaultInputBoxStyles,\n      toggle: defaultToggleStyles,\n      countBadge: defaultCountBadgeStyles,\n      button: defaultButtonStyles,\n      progressBar: defaultProgressBarStyles,\n      keybindingLabel: defaultKeybindingLabelStyles,\n      list: getListStyles({\n        listBackground: quickInputBackground,\n        listFocusBackground: quickInputListFocusBackground,\n        listFocusForeground: quickInputListFocusForeground,\n        // Look like focused when inactive.\n        listInactiveFocusForeground: quickInputListFocusForeground,\n        listInactiveSelectionIconForeground: quickInputListFocusIconForeground,\n        listInactiveFocusBackground: quickInputListFocusBackground,\n        listFocusOutline: activeContrastBorder,\n        listInactiveFocusOutline: activeContrastBorder\n      }),\n      pickerGroup: {\n        pickerGroupBorder: asCssVariable(pickerGroupBorder),\n        pickerGroupForeground: asCssVariable(pickerGroupForeground)\n      }\n    };\n  }\n};\nQuickInputService = __decorate([__param(0, IInstantiationService), __param(1, IContextKeyService), __param(2, IThemeService), __param(3, ILayoutService), __param(4, IConfigurationService)], QuickInputService);\nexport { QuickInputService };", "map": {"version": 3, "names": ["__decorate", "decorators", "target", "key", "desc", "c", "arguments", "length", "r", "Object", "getOwnPropertyDescriptor", "d", "Reflect", "decorate", "i", "defineProperty", "__param", "paramIndex", "decorator", "CancellationToken", "Emitter", "IContextKeyService", "RawContextKey", "IInstantiationService", "ILayoutService", "IOpenerService", "QuickAccessController", "defaultButtonStyles", "defaultCountBadgeStyles", "defaultInputBoxStyles", "defaultKeybindingLabelStyles", "defaultProgressBarStyles", "defaultToggleStyles", "getListStyles", "activeContrastBorder", "asCssVariable", "pickerGroupBorder", "pickerGroupForeground", "quickInputBackground", "quickInputForeground", "quickInputListFocusBackground", "quickInputListFocusForeground", "quickInputListFocusIconForeground", "quickInputTitleBackground", "widgetBorder", "widgetShadow", "IThemeService", "Themable", "QuickInputHoverDelegate", "QuickInputController", "IConfigurationService", "getWindow", "QuickInputService", "controller", "_controller", "_register", "createController", "hasController", "currentQuickInput", "quickAccess", "_quickAccess", "instantiationService", "createInstance", "constructor", "contextKeyService", "themeService", "layoutService", "configurationService", "_onShow", "_onHide", "contexts", "Map", "host", "options", "defaultOptions", "idPrefix", "container", "activeContainer", "ignoreFocusOut", "backKeybindingLabel", "undefined", "setContextKey", "id", "linkOpenerDelegate", "content", "invokeFunction", "accessor", "openerService", "get", "open", "allowCommands", "fromUserGesture", "returnFocus", "focus", "styles", "computeStyles", "hoverDelegate", "layout", "activeContainerDimension", "activeContainerOffset", "quickPickTop", "onDidLayoutActiveContainer", "dimension", "onDidChangeActiveContainer", "isVisible", "onShow", "resetContextKeys", "fire", "onHide", "bindTo", "set", "for<PERSON>ach", "context", "reset", "pick", "picks", "token", "None", "createQuickPick", "useSeparators", "createInputBox", "updateStyles", "applyStyles", "widget", "inputBox", "toggle", "countBadge", "button", "progressBar", "keybinding<PERSON>abel", "list", "listBackground", "listFocusBackground", "listFocusForeground", "listInactiveFocusForeground", "listInactiveSelectionIconForeground", "listInactiveFocusBackground", "listFocusOutline", "listInactiveFocusOutline", "pickerGroup"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/platform/quickinput/browser/quickInputService.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = (this && this.__param) || function (paramIndex, decorator) {\n    return function (target, key) { decorator(target, key, paramIndex); }\n};\nimport { CancellationToken } from '../../../base/common/cancellation.js';\nimport { Emitter } from '../../../base/common/event.js';\nimport { IContextKeyService, RawContextKey } from '../../contextkey/common/contextkey.js';\nimport { IInstantiationService } from '../../instantiation/common/instantiation.js';\nimport { ILayoutService } from '../../layout/browser/layoutService.js';\nimport { IOpenerService } from '../../opener/common/opener.js';\nimport { QuickAccessController } from './quickAccess.js';\nimport { defaultButtonStyles, defaultCountBadgeStyles, defaultInputBoxStyles, defaultKeybindingLabelStyles, defaultProgressBarStyles, defaultToggleStyles, getListStyles } from '../../theme/browser/defaultStyles.js';\nimport { activeContrastBorder, asCssVariable, pickerGroupBorder, pickerGroupForeground, quickInputBackground, quickInputForeground, quickInputListFocusBackground, quickInputListFocusForeground, quickInputListFocusIconForeground, quickInputTitleBackground, widgetBorder, widgetShadow } from '../../theme/common/colorRegistry.js';\nimport { IThemeService, Themable } from '../../theme/common/themeService.js';\nimport { QuickInputHoverDelegate } from './quickInput.js';\nimport { QuickInputController } from './quickInputController.js';\nimport { IConfigurationService } from '../../configuration/common/configuration.js';\nimport { getWindow } from '../../../base/browser/dom.js';\nlet QuickInputService = class QuickInputService extends Themable {\n    get controller() {\n        if (!this._controller) {\n            this._controller = this._register(this.createController());\n        }\n        return this._controller;\n    }\n    get hasController() { return !!this._controller; }\n    get currentQuickInput() { return this.controller.currentQuickInput; }\n    get quickAccess() {\n        if (!this._quickAccess) {\n            this._quickAccess = this._register(this.instantiationService.createInstance(QuickAccessController));\n        }\n        return this._quickAccess;\n    }\n    constructor(instantiationService, contextKeyService, themeService, layoutService, configurationService) {\n        super(themeService);\n        this.instantiationService = instantiationService;\n        this.contextKeyService = contextKeyService;\n        this.layoutService = layoutService;\n        this.configurationService = configurationService;\n        this._onShow = this._register(new Emitter());\n        this._onHide = this._register(new Emitter());\n        this.contexts = new Map();\n    }\n    createController(host = this.layoutService, options) {\n        const defaultOptions = {\n            idPrefix: 'quickInput_',\n            container: host.activeContainer,\n            ignoreFocusOut: () => false,\n            backKeybindingLabel: () => undefined,\n            setContextKey: (id) => this.setContextKey(id),\n            linkOpenerDelegate: (content) => {\n                // HACK: https://github.com/microsoft/vscode/issues/173691\n                this.instantiationService.invokeFunction(accessor => {\n                    const openerService = accessor.get(IOpenerService);\n                    openerService.open(content, { allowCommands: true, fromUserGesture: true });\n                });\n            },\n            returnFocus: () => host.focus(),\n            styles: this.computeStyles(),\n            hoverDelegate: this._register(this.instantiationService.createInstance(QuickInputHoverDelegate))\n        };\n        const controller = this._register(this.instantiationService.createInstance(QuickInputController, {\n            ...defaultOptions,\n            ...options\n        }));\n        controller.layout(host.activeContainerDimension, host.activeContainerOffset.quickPickTop);\n        // Layout changes\n        this._register(host.onDidLayoutActiveContainer(dimension => {\n            if (getWindow(host.activeContainer) === getWindow(controller.container)) {\n                controller.layout(dimension, host.activeContainerOffset.quickPickTop);\n            }\n        }));\n        this._register(host.onDidChangeActiveContainer(() => {\n            if (controller.isVisible()) {\n                return;\n            }\n            controller.layout(host.activeContainerDimension, host.activeContainerOffset.quickPickTop);\n        }));\n        // Context keys\n        this._register(controller.onShow(() => {\n            this.resetContextKeys();\n            this._onShow.fire();\n        }));\n        this._register(controller.onHide(() => {\n            this.resetContextKeys();\n            this._onHide.fire();\n        }));\n        return controller;\n    }\n    setContextKey(id) {\n        let key;\n        if (id) {\n            key = this.contexts.get(id);\n            if (!key) {\n                key = new RawContextKey(id, false)\n                    .bindTo(this.contextKeyService);\n                this.contexts.set(id, key);\n            }\n        }\n        if (key && key.get()) {\n            return; // already active context\n        }\n        this.resetContextKeys();\n        key?.set(true);\n    }\n    resetContextKeys() {\n        this.contexts.forEach(context => {\n            if (context.get()) {\n                context.reset();\n            }\n        });\n    }\n    pick(picks, options, token = CancellationToken.None) {\n        return this.controller.pick(picks, options, token);\n    }\n    createQuickPick(options = { useSeparators: false }) {\n        return this.controller.createQuickPick(options);\n    }\n    createInputBox() {\n        return this.controller.createInputBox();\n    }\n    updateStyles() {\n        if (this.hasController) {\n            this.controller.applyStyles(this.computeStyles());\n        }\n    }\n    computeStyles() {\n        return {\n            widget: {\n                quickInputBackground: asCssVariable(quickInputBackground),\n                quickInputForeground: asCssVariable(quickInputForeground),\n                quickInputTitleBackground: asCssVariable(quickInputTitleBackground),\n                widgetBorder: asCssVariable(widgetBorder),\n                widgetShadow: asCssVariable(widgetShadow),\n            },\n            inputBox: defaultInputBoxStyles,\n            toggle: defaultToggleStyles,\n            countBadge: defaultCountBadgeStyles,\n            button: defaultButtonStyles,\n            progressBar: defaultProgressBarStyles,\n            keybindingLabel: defaultKeybindingLabelStyles,\n            list: getListStyles({\n                listBackground: quickInputBackground,\n                listFocusBackground: quickInputListFocusBackground,\n                listFocusForeground: quickInputListFocusForeground,\n                // Look like focused when inactive.\n                listInactiveFocusForeground: quickInputListFocusForeground,\n                listInactiveSelectionIconForeground: quickInputListFocusIconForeground,\n                listInactiveFocusBackground: quickInputListFocusBackground,\n                listFocusOutline: activeContrastBorder,\n                listInactiveFocusOutline: activeContrastBorder,\n            }),\n            pickerGroup: {\n                pickerGroupBorder: asCssVariable(pickerGroupBorder),\n                pickerGroupForeground: asCssVariable(pickerGroupForeground),\n            }\n        };\n    }\n};\nQuickInputService = __decorate([\n    __param(0, IInstantiationService),\n    __param(1, IContextKeyService),\n    __param(2, IThemeService),\n    __param(3, ILayoutService),\n    __param(4, IConfigurationService)\n], QuickInputService);\nexport { QuickInputService };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,IAAIA,UAAU,GAAI,IAAI,IAAI,IAAI,CAACA,UAAU,IAAK,UAAUC,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAE;EACnF,IAAIC,CAAC,GAAGC,SAAS,CAACC,MAAM;IAAEC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGH,MAAM,GAAGE,IAAI,KAAK,IAAI,GAAGA,IAAI,GAAGK,MAAM,CAACC,wBAAwB,CAACR,MAAM,EAAEC,GAAG,CAAC,GAAGC,IAAI;IAAEO,CAAC;EAC5H,IAAI,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,CAACC,QAAQ,KAAK,UAAU,EAAEL,CAAC,GAAGI,OAAO,CAACC,QAAQ,CAACZ,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,CAAC,CAAC,KAC1H,KAAK,IAAIU,CAAC,GAAGb,UAAU,CAACM,MAAM,GAAG,CAAC,EAAEO,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE,IAAIH,CAAC,GAAGV,UAAU,CAACa,CAAC,CAAC,EAAEN,CAAC,GAAG,CAACH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACH,CAAC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACT,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,GAAGG,CAAC,CAACT,MAAM,EAAEC,GAAG,CAAC,KAAKK,CAAC;EACjJ,OAAOH,CAAC,GAAG,CAAC,IAAIG,CAAC,IAAIC,MAAM,CAACM,cAAc,CAACb,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,EAAEA,CAAC;AACjE,CAAC;AACD,IAAIQ,OAAO,GAAI,IAAI,IAAI,IAAI,CAACA,OAAO,IAAK,UAAUC,UAAU,EAAEC,SAAS,EAAE;EACrE,OAAO,UAAUhB,MAAM,EAAEC,GAAG,EAAE;IAAEe,SAAS,CAAChB,MAAM,EAAEC,GAAG,EAAEc,UAAU,CAAC;EAAE,CAAC;AACzE,CAAC;AACD,SAASE,iBAAiB,QAAQ,sCAAsC;AACxE,SAASC,OAAO,QAAQ,+BAA+B;AACvD,SAASC,kBAAkB,EAAEC,aAAa,QAAQ,uCAAuC;AACzF,SAASC,qBAAqB,QAAQ,6CAA6C;AACnF,SAASC,cAAc,QAAQ,uCAAuC;AACtE,SAASC,cAAc,QAAQ,+BAA+B;AAC9D,SAASC,qBAAqB,QAAQ,kBAAkB;AACxD,SAASC,mBAAmB,EAAEC,uBAAuB,EAAEC,qBAAqB,EAAEC,4BAA4B,EAAEC,wBAAwB,EAAEC,mBAAmB,EAAEC,aAAa,QAAQ,sCAAsC;AACtN,SAASC,oBAAoB,EAAEC,aAAa,EAAEC,iBAAiB,EAAEC,qBAAqB,EAAEC,oBAAoB,EAAEC,oBAAoB,EAAEC,6BAA6B,EAAEC,6BAA6B,EAAEC,iCAAiC,EAAEC,yBAAyB,EAAEC,YAAY,EAAEC,YAAY,QAAQ,qCAAqC;AACvU,SAASC,aAAa,EAAEC,QAAQ,QAAQ,oCAAoC;AAC5E,SAASC,uBAAuB,QAAQ,iBAAiB;AACzD,SAASC,oBAAoB,QAAQ,2BAA2B;AAChE,SAASC,qBAAqB,QAAQ,6CAA6C;AACnF,SAASC,SAAS,QAAQ,8BAA8B;AACxD,IAAIC,iBAAiB,GAAG,MAAMA,iBAAiB,SAASL,QAAQ,CAAC;EAC7D,IAAIM,UAAUA,CAAA,EAAG;IACb,IAAI,CAAC,IAAI,CAACC,WAAW,EAAE;MACnB,IAAI,CAACA,WAAW,GAAG,IAAI,CAACC,SAAS,CAAC,IAAI,CAACC,gBAAgB,CAAC,CAAC,CAAC;IAC9D;IACA,OAAO,IAAI,CAACF,WAAW;EAC3B;EACA,IAAIG,aAAaA,CAAA,EAAG;IAAE,OAAO,CAAC,CAAC,IAAI,CAACH,WAAW;EAAE;EACjD,IAAII,iBAAiBA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACL,UAAU,CAACK,iBAAiB;EAAE;EACpE,IAAIC,WAAWA,CAAA,EAAG;IACd,IAAI,CAAC,IAAI,CAACC,YAAY,EAAE;MACpB,IAAI,CAACA,YAAY,GAAG,IAAI,CAACL,SAAS,CAAC,IAAI,CAACM,oBAAoB,CAACC,cAAc,CAACpC,qBAAqB,CAAC,CAAC;IACvG;IACA,OAAO,IAAI,CAACkC,YAAY;EAC5B;EACAG,WAAWA,CAACF,oBAAoB,EAAEG,iBAAiB,EAAEC,YAAY,EAAEC,aAAa,EAAEC,oBAAoB,EAAE;IACpG,KAAK,CAACF,YAAY,CAAC;IACnB,IAAI,CAACJ,oBAAoB,GAAGA,oBAAoB;IAChD,IAAI,CAACG,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACE,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,oBAAoB,GAAGA,oBAAoB;IAChD,IAAI,CAACC,OAAO,GAAG,IAAI,CAACb,SAAS,CAAC,IAAInC,OAAO,CAAC,CAAC,CAAC;IAC5C,IAAI,CAACiD,OAAO,GAAG,IAAI,CAACd,SAAS,CAAC,IAAInC,OAAO,CAAC,CAAC,CAAC;IAC5C,IAAI,CAACkD,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC7B;EACAf,gBAAgBA,CAACgB,IAAI,GAAG,IAAI,CAACN,aAAa,EAAEO,OAAO,EAAE;IACjD,MAAMC,cAAc,GAAG;MACnBC,QAAQ,EAAE,aAAa;MACvBC,SAAS,EAAEJ,IAAI,CAACK,eAAe;MAC/BC,cAAc,EAAEA,CAAA,KAAM,KAAK;MAC3BC,mBAAmB,EAAEA,CAAA,KAAMC,SAAS;MACpCC,aAAa,EAAGC,EAAE,IAAK,IAAI,CAACD,aAAa,CAACC,EAAE,CAAC;MAC7CC,kBAAkB,EAAGC,OAAO,IAAK;QAC7B;QACA,IAAI,CAACvB,oBAAoB,CAACwB,cAAc,CAACC,QAAQ,IAAI;UACjD,MAAMC,aAAa,GAAGD,QAAQ,CAACE,GAAG,CAAC/D,cAAc,CAAC;UAClD8D,aAAa,CAACE,IAAI,CAACL,OAAO,EAAE;YAAEM,aAAa,EAAE,IAAI;YAAEC,eAAe,EAAE;UAAK,CAAC,CAAC;QAC/E,CAAC,CAAC;MACN,CAAC;MACDC,WAAW,EAAEA,CAAA,KAAMpB,IAAI,CAACqB,KAAK,CAAC,CAAC;MAC/BC,MAAM,EAAE,IAAI,CAACC,aAAa,CAAC,CAAC;MAC5BC,aAAa,EAAE,IAAI,CAACzC,SAAS,CAAC,IAAI,CAACM,oBAAoB,CAACC,cAAc,CAACd,uBAAuB,CAAC;IACnG,CAAC;IACD,MAAMK,UAAU,GAAG,IAAI,CAACE,SAAS,CAAC,IAAI,CAACM,oBAAoB,CAACC,cAAc,CAACb,oBAAoB,EAAE;MAC7F,GAAGyB,cAAc;MACjB,GAAGD;IACP,CAAC,CAAC,CAAC;IACHpB,UAAU,CAAC4C,MAAM,CAACzB,IAAI,CAAC0B,wBAAwB,EAAE1B,IAAI,CAAC2B,qBAAqB,CAACC,YAAY,CAAC;IACzF;IACA,IAAI,CAAC7C,SAAS,CAACiB,IAAI,CAAC6B,0BAA0B,CAACC,SAAS,IAAI;MACxD,IAAInD,SAAS,CAACqB,IAAI,CAACK,eAAe,CAAC,KAAK1B,SAAS,CAACE,UAAU,CAACuB,SAAS,CAAC,EAAE;QACrEvB,UAAU,CAAC4C,MAAM,CAACK,SAAS,EAAE9B,IAAI,CAAC2B,qBAAqB,CAACC,YAAY,CAAC;MACzE;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,CAAC7C,SAAS,CAACiB,IAAI,CAAC+B,0BAA0B,CAAC,MAAM;MACjD,IAAIlD,UAAU,CAACmD,SAAS,CAAC,CAAC,EAAE;QACxB;MACJ;MACAnD,UAAU,CAAC4C,MAAM,CAACzB,IAAI,CAAC0B,wBAAwB,EAAE1B,IAAI,CAAC2B,qBAAqB,CAACC,YAAY,CAAC;IAC7F,CAAC,CAAC,CAAC;IACH;IACA,IAAI,CAAC7C,SAAS,CAACF,UAAU,CAACoD,MAAM,CAAC,MAAM;MACnC,IAAI,CAACC,gBAAgB,CAAC,CAAC;MACvB,IAAI,CAACtC,OAAO,CAACuC,IAAI,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC;IACH,IAAI,CAACpD,SAAS,CAACF,UAAU,CAACuD,MAAM,CAAC,MAAM;MACnC,IAAI,CAACF,gBAAgB,CAAC,CAAC;MACvB,IAAI,CAACrC,OAAO,CAACsC,IAAI,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC;IACH,OAAOtD,UAAU;EACrB;EACA4B,aAAaA,CAACC,EAAE,EAAE;IACd,IAAI/E,GAAG;IACP,IAAI+E,EAAE,EAAE;MACJ/E,GAAG,GAAG,IAAI,CAACmE,QAAQ,CAACkB,GAAG,CAACN,EAAE,CAAC;MAC3B,IAAI,CAAC/E,GAAG,EAAE;QACNA,GAAG,GAAG,IAAImB,aAAa,CAAC4D,EAAE,EAAE,KAAK,CAAC,CAC7B2B,MAAM,CAAC,IAAI,CAAC7C,iBAAiB,CAAC;QACnC,IAAI,CAACM,QAAQ,CAACwC,GAAG,CAAC5B,EAAE,EAAE/E,GAAG,CAAC;MAC9B;IACJ;IACA,IAAIA,GAAG,IAAIA,GAAG,CAACqF,GAAG,CAAC,CAAC,EAAE;MAClB,OAAO,CAAC;IACZ;IACA,IAAI,CAACkB,gBAAgB,CAAC,CAAC;IACvBvG,GAAG,EAAE2G,GAAG,CAAC,IAAI,CAAC;EAClB;EACAJ,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACpC,QAAQ,CAACyC,OAAO,CAACC,OAAO,IAAI;MAC7B,IAAIA,OAAO,CAACxB,GAAG,CAAC,CAAC,EAAE;QACfwB,OAAO,CAACC,KAAK,CAAC,CAAC;MACnB;IACJ,CAAC,CAAC;EACN;EACAC,IAAIA,CAACC,KAAK,EAAE1C,OAAO,EAAE2C,KAAK,GAAGjG,iBAAiB,CAACkG,IAAI,EAAE;IACjD,OAAO,IAAI,CAAChE,UAAU,CAAC6D,IAAI,CAACC,KAAK,EAAE1C,OAAO,EAAE2C,KAAK,CAAC;EACtD;EACAE,eAAeA,CAAC7C,OAAO,GAAG;IAAE8C,aAAa,EAAE;EAAM,CAAC,EAAE;IAChD,OAAO,IAAI,CAAClE,UAAU,CAACiE,eAAe,CAAC7C,OAAO,CAAC;EACnD;EACA+C,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAACnE,UAAU,CAACmE,cAAc,CAAC,CAAC;EAC3C;EACAC,YAAYA,CAAA,EAAG;IACX,IAAI,IAAI,CAAChE,aAAa,EAAE;MACpB,IAAI,CAACJ,UAAU,CAACqE,WAAW,CAAC,IAAI,CAAC3B,aAAa,CAAC,CAAC,CAAC;IACrD;EACJ;EACAA,aAAaA,CAAA,EAAG;IACZ,OAAO;MACH4B,MAAM,EAAE;QACJrF,oBAAoB,EAAEH,aAAa,CAACG,oBAAoB,CAAC;QACzDC,oBAAoB,EAAEJ,aAAa,CAACI,oBAAoB,CAAC;QACzDI,yBAAyB,EAAER,aAAa,CAACQ,yBAAyB,CAAC;QACnEC,YAAY,EAAET,aAAa,CAACS,YAAY,CAAC;QACzCC,YAAY,EAAEV,aAAa,CAACU,YAAY;MAC5C,CAAC;MACD+E,QAAQ,EAAE/F,qBAAqB;MAC/BgG,MAAM,EAAE7F,mBAAmB;MAC3B8F,UAAU,EAAElG,uBAAuB;MACnCmG,MAAM,EAAEpG,mBAAmB;MAC3BqG,WAAW,EAAEjG,wBAAwB;MACrCkG,eAAe,EAAEnG,4BAA4B;MAC7CoG,IAAI,EAAEjG,aAAa,CAAC;QAChBkG,cAAc,EAAE7F,oBAAoB;QACpC8F,mBAAmB,EAAE5F,6BAA6B;QAClD6F,mBAAmB,EAAE5F,6BAA6B;QAClD;QACA6F,2BAA2B,EAAE7F,6BAA6B;QAC1D8F,mCAAmC,EAAE7F,iCAAiC;QACtE8F,2BAA2B,EAAEhG,6BAA6B;QAC1DiG,gBAAgB,EAAEvG,oBAAoB;QACtCwG,wBAAwB,EAAExG;MAC9B,CAAC,CAAC;MACFyG,WAAW,EAAE;QACTvG,iBAAiB,EAAED,aAAa,CAACC,iBAAiB,CAAC;QACnDC,qBAAqB,EAAEF,aAAa,CAACE,qBAAqB;MAC9D;IACJ,CAAC;EACL;AACJ,CAAC;AACDe,iBAAiB,GAAGpD,UAAU,CAAC,CAC3BgB,OAAO,CAAC,CAAC,EAAEO,qBAAqB,CAAC,EACjCP,OAAO,CAAC,CAAC,EAAEK,kBAAkB,CAAC,EAC9BL,OAAO,CAAC,CAAC,EAAE8B,aAAa,CAAC,EACzB9B,OAAO,CAAC,CAAC,EAAEQ,cAAc,CAAC,EAC1BR,OAAO,CAAC,CAAC,EAAEkC,qBAAqB,CAAC,CACpC,EAAEE,iBAAiB,CAAC;AACrB,SAASA,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}