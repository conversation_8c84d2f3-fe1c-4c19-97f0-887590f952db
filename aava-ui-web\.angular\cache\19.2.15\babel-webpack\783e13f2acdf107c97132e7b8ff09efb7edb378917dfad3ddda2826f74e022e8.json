{"ast": null, "code": "import { inject } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { of } from 'rxjs';\nimport { map, catchError } from 'rxjs/operators';\nimport { AuthService } from '../services/auth.service';\nimport { TokenStorageService } from '../services/token-storage.service';\nimport * as i0 from \"@angular/core\";\nexport let AuthGuard = /*#__PURE__*/(() => {\n  class AuthGuard {\n    authService = inject(AuthService);\n    router = inject(Router);\n    tokenStorage = inject(TokenStorageService);\n    canActivate(route, state) {\n      const accessToken = this.tokenStorage.getAccessToken();\n      const refreshToken = this.tokenStorage.getRefreshToken();\n      // If we have a refresh token but no access token, try to refresh\n      if (!accessToken && refreshToken) {\n        return this.authService.refreshToken(refreshToken).pipe(map(() => true), catchError(() => of(this.router.createUrlTree(['/login']))));\n      }\n      // If no tokens at all, redirect to login\n      if (!accessToken && !refreshToken) {\n        return of(this.router.createUrlTree(['/login']));\n      }\n      // If we have tokens, allow access\n      return of(true);\n    }\n    static ɵfac = function AuthGuard_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AuthGuard)();\n    };\n    static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthGuard,\n      factory: AuthGuard.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return AuthGuard;\n})();", "map": {"version": 3, "names": ["inject", "Router", "of", "map", "catchError", "AuthService", "TokenStorageService", "<PERSON><PERSON><PERSON><PERSON>", "authService", "router", "tokenStorage", "canActivate", "route", "state", "accessToken", "getAccessToken", "refreshToken", "getRefreshToken", "pipe", "createUrlTree", "factory", "ɵfac", "providedIn"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\auth\\guards\\auth.guard.ts"], "sourcesContent": ["import { Injectable, inject } from '@angular/core';\r\nimport {\r\n  CanActivate,\r\n  Router,\r\n  ActivatedRouteSnapshot,\r\n  RouterStateSnapshot,\r\n  UrlTree,\r\n} from '@angular/router';\r\nimport { Observable, of } from 'rxjs';\r\nimport { map, take, catchError } from 'rxjs/operators';\r\nimport { AuthService } from '../services/auth.service';\r\nimport { TokenStorageService } from '../services/token-storage.service';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class AuthGuard implements CanActivate {\r\n  private readonly authService = inject(AuthService);\r\n  private readonly router = inject(Router);\r\n  private readonly tokenStorage = inject(TokenStorageService);\r\n\r\n  canActivate(\r\n    route: ActivatedRouteSnapshot,\r\n    state: RouterStateSnapshot,\r\n  ): Observable<boolean | UrlTree> {\r\n    const accessToken = this.tokenStorage.getAccessToken();\r\n    const refreshToken = this.tokenStorage.getRefreshToken();\r\n\r\n    // If we have a refresh token but no access token, try to refresh\r\n    if (!accessToken && refreshToken) {\r\n      return this.authService.refreshToken(refreshToken).pipe(\r\n        map(() => true),\r\n        catchError(() => of(this.router.createUrlTree(['/login'])))\r\n      );\r\n    }\r\n\r\n    // If no tokens at all, redirect to login\r\n    if (!accessToken && !refreshToken) {\r\n      return of(this.router.createUrlTree(['/login']));\r\n    }\r\n\r\n    // If we have tokens, allow access\r\n    return of(true);\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAAqBA,MAAM,QAAQ,eAAe;AAClD,SAEEC,MAAM,QAID,iBAAiB;AACxB,SAAqBC,EAAE,QAAQ,MAAM;AACrC,SAASC,GAAG,EAAQC,UAAU,QAAQ,gBAAgB;AACtD,SAASC,WAAW,QAAQ,0BAA0B;AACtD,SAASC,mBAAmB,QAAQ,mCAAmC;;AAKvE,WAAaC,SAAS;EAAhB,MAAOA,SAAS;IACHC,WAAW,GAAGR,MAAM,CAACK,WAAW,CAAC;IACjCI,MAAM,GAAGT,MAAM,CAACC,MAAM,CAAC;IACvBS,YAAY,GAAGV,MAAM,CAACM,mBAAmB,CAAC;IAE3DK,WAAWA,CACTC,KAA6B,EAC7BC,KAA0B;MAE1B,MAAMC,WAAW,GAAG,IAAI,CAACJ,YAAY,CAACK,cAAc,EAAE;MACtD,MAAMC,YAAY,GAAG,IAAI,CAACN,YAAY,CAACO,eAAe,EAAE;MAExD;MACA,IAAI,CAACH,WAAW,IAAIE,YAAY,EAAE;QAChC,OAAO,IAAI,CAACR,WAAW,CAACQ,YAAY,CAACA,YAAY,CAAC,CAACE,IAAI,CACrDf,GAAG,CAAC,MAAM,IAAI,CAAC,EACfC,UAAU,CAAC,MAAMF,EAAE,CAAC,IAAI,CAACO,MAAM,CAACU,aAAa,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAC5D;MACH;MAEA;MACA,IAAI,CAACL,WAAW,IAAI,CAACE,YAAY,EAAE;QACjC,OAAOd,EAAE,CAAC,IAAI,CAACO,MAAM,CAACU,aAAa,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;MAClD;MAEA;MACA,OAAOjB,EAAE,CAAC,IAAI,CAAC;IACjB;;uCA3BWK,SAAS;IAAA;;aAATA,SAAS;MAAAa,OAAA,EAATb,SAAS,CAAAc,IAAA;MAAAC,UAAA,EAFR;IAAM;;SAEPf,SAAS;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}