{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { assertFn } from '../assert.js';\nimport { DisposableStore, markAsDisposed, toDisposable, trackDisposable } from '../lifecycle.js';\nimport { DebugNameData } from './debugName.js';\nimport { getLogger } from './logging.js';\n/**\n * Runs immediately and whenever a transaction ends and an observed observable changed.\n * {@link fn} should start with a JS Doc using `@description` to name the autorun.\n */\nexport function autorun(fn) {\n  return new AutorunObserver(new DebugNameData(undefined, undefined, fn), fn, undefined, undefined);\n}\n/**\n * Runs immediately and whenever a transaction ends and an observed observable changed.\n * {@link fn} should start with a JS Doc using `@description` to name the autorun.\n */\nexport function autorunOpts(options, fn) {\n  return new AutorunObserver(new DebugNameData(options.owner, options.debugName, options.debugReferenceFn ?? fn), fn, undefined, undefined);\n}\n/**\n * Runs immediately and whenever a transaction ends and an observed observable changed.\n * {@link fn} should start with a JS Doc using `@description` to name the autorun.\n *\n * Use `createEmptyChangeSummary` to create a \"change summary\" that can collect the changes.\n * Use `handleChange` to add a reported change to the change summary.\n * The run function is given the last change summary.\n * The change summary is discarded after the run function was called.\n *\n * @see autorun\n */\nexport function autorunHandleChanges(options, fn) {\n  return new AutorunObserver(new DebugNameData(options.owner, options.debugName, options.debugReferenceFn ?? fn), fn, options.createEmptyChangeSummary, options.handleChange);\n}\n/**\n * @see autorunHandleChanges (but with a disposable store that is cleared before the next run or on dispose)\n */\nexport function autorunWithStoreHandleChanges(options, fn) {\n  const store = new DisposableStore();\n  const disposable = autorunHandleChanges({\n    owner: options.owner,\n    debugName: options.debugName,\n    debugReferenceFn: options.debugReferenceFn ?? fn,\n    createEmptyChangeSummary: options.createEmptyChangeSummary,\n    handleChange: options.handleChange\n  }, (reader, changeSummary) => {\n    store.clear();\n    fn(reader, changeSummary, store);\n  });\n  return toDisposable(() => {\n    disposable.dispose();\n    store.dispose();\n  });\n}\n/**\n * @see autorun (but with a disposable store that is cleared before the next run or on dispose)\n */\nexport function autorunWithStore(fn) {\n  const store = new DisposableStore();\n  const disposable = autorunOpts({\n    owner: undefined,\n    debugName: undefined,\n    debugReferenceFn: fn\n  }, reader => {\n    store.clear();\n    fn(reader, store);\n  });\n  return toDisposable(() => {\n    disposable.dispose();\n    store.dispose();\n  });\n}\nexport class AutorunObserver {\n  get debugName() {\n    return this._debugNameData.getDebugName(this) ?? '(anonymous)';\n  }\n  constructor(_debugNameData, _runFn, createChangeSummary, _handleChange) {\n    this._debugNameData = _debugNameData;\n    this._runFn = _runFn;\n    this.createChangeSummary = createChangeSummary;\n    this._handleChange = _handleChange;\n    this.state = 2 /* AutorunState.stale */;\n    this.updateCount = 0;\n    this.disposed = false;\n    this.dependencies = new Set();\n    this.dependenciesToBeRemoved = new Set();\n    this.changeSummary = this.createChangeSummary?.();\n    getLogger()?.handleAutorunCreated(this);\n    this._runIfNeeded();\n    trackDisposable(this);\n  }\n  dispose() {\n    this.disposed = true;\n    for (const o of this.dependencies) {\n      o.removeObserver(this);\n    }\n    this.dependencies.clear();\n    markAsDisposed(this);\n  }\n  _runIfNeeded() {\n    if (this.state === 3 /* AutorunState.upToDate */) {\n      return;\n    }\n    const emptySet = this.dependenciesToBeRemoved;\n    this.dependenciesToBeRemoved = this.dependencies;\n    this.dependencies = emptySet;\n    this.state = 3 /* AutorunState.upToDate */;\n    const isDisposed = this.disposed;\n    try {\n      if (!isDisposed) {\n        getLogger()?.handleAutorunTriggered(this);\n        const changeSummary = this.changeSummary;\n        this.changeSummary = this.createChangeSummary?.();\n        this._runFn(this, changeSummary);\n      }\n    } finally {\n      if (!isDisposed) {\n        getLogger()?.handleAutorunFinished(this);\n      }\n      // We don't want our observed observables to think that they are (not even temporarily) not being observed.\n      // Thus, we only unsubscribe from observables that are definitely not read anymore.\n      for (const o of this.dependenciesToBeRemoved) {\n        o.removeObserver(this);\n      }\n      this.dependenciesToBeRemoved.clear();\n    }\n  }\n  toString() {\n    return `Autorun<${this.debugName}>`;\n  }\n  // IObserver implementation\n  beginUpdate() {\n    if (this.state === 3 /* AutorunState.upToDate */) {\n      this.state = 1 /* AutorunState.dependenciesMightHaveChanged */;\n    }\n    this.updateCount++;\n  }\n  endUpdate() {\n    if (this.updateCount === 1) {\n      do {\n        if (this.state === 1 /* AutorunState.dependenciesMightHaveChanged */) {\n          this.state = 3 /* AutorunState.upToDate */;\n          for (const d of this.dependencies) {\n            d.reportChanges();\n            if (this.state === 2 /* AutorunState.stale */) {\n              // The other dependencies will refresh on demand\n              break;\n            }\n          }\n        }\n        this._runIfNeeded();\n      } while (this.state !== 3 /* AutorunState.upToDate */);\n    }\n    this.updateCount--;\n    assertFn(() => this.updateCount >= 0);\n  }\n  handlePossibleChange(observable) {\n    if (this.state === 3 /* AutorunState.upToDate */ && this.dependencies.has(observable) && !this.dependenciesToBeRemoved.has(observable)) {\n      this.state = 1 /* AutorunState.dependenciesMightHaveChanged */;\n    }\n  }\n  handleChange(observable, change) {\n    if (this.dependencies.has(observable) && !this.dependenciesToBeRemoved.has(observable)) {\n      const shouldReact = this._handleChange ? this._handleChange({\n        changedObservable: observable,\n        change,\n        didChange: o => o === observable\n      }, this.changeSummary) : true;\n      if (shouldReact) {\n        this.state = 2 /* AutorunState.stale */;\n      }\n    }\n  }\n  // IReader implementation\n  readObservable(observable) {\n    // In case the run action disposes the autorun\n    if (this.disposed) {\n      return observable.get();\n    }\n    observable.addObserver(this);\n    const value = observable.get();\n    this.dependencies.add(observable);\n    this.dependenciesToBeRemoved.delete(observable);\n    return value;\n  }\n}\n(function (autorun) {\n  autorun.Observer = AutorunObserver;\n})(autorun || (autorun = {}));", "map": {"version": 3, "names": ["assertFn", "DisposableStore", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "toDisposable", "trackDisposable", "DebugNameData", "<PERSON><PERSON><PERSON><PERSON>", "autorun", "fn", "AutorunObserver", "undefined", "autorunOpts", "options", "owner", "debugName", "debugReferenceFn", "autorunHandleChanges", "createEmptyChangeSummary", "handleChange", "autorunWithStoreHandleChanges", "store", "disposable", "reader", "changeSummary", "clear", "dispose", "autorunWithStore", "_debugNameData", "getDebugName", "constructor", "_runFn", "createChangeSummary", "_handleChange", "state", "updateCount", "disposed", "dependencies", "Set", "dependenciesToBeRemoved", "handleAutorunCreated", "_runIfNeeded", "o", "removeObserver", "emptySet", "isDisposed", "handleAutorunTriggered", "handleAutorunFinished", "toString", "beginUpdate", "endUpdate", "d", "reportChanges", "handlePossibleChange", "observable", "has", "change", "shouldReact", "changedObservable", "<PERSON><PERSON><PERSON><PERSON>", "readObservable", "get", "addObserver", "value", "add", "delete", "Observer"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/common/observableInternal/autorun.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { assertFn } from '../assert.js';\nimport { DisposableStore, markAsDisposed, toDisposable, trackDisposable } from '../lifecycle.js';\nimport { DebugNameData } from './debugName.js';\nimport { getLogger } from './logging.js';\n/**\n * Runs immediately and whenever a transaction ends and an observed observable changed.\n * {@link fn} should start with a JS Doc using `@description` to name the autorun.\n */\nexport function autorun(fn) {\n    return new AutorunObserver(new DebugNameData(undefined, undefined, fn), fn, undefined, undefined);\n}\n/**\n * Runs immediately and whenever a transaction ends and an observed observable changed.\n * {@link fn} should start with a JS Doc using `@description` to name the autorun.\n */\nexport function autorunOpts(options, fn) {\n    return new AutorunObserver(new DebugNameData(options.owner, options.debugName, options.debugReferenceFn ?? fn), fn, undefined, undefined);\n}\n/**\n * Runs immediately and whenever a transaction ends and an observed observable changed.\n * {@link fn} should start with a JS Doc using `@description` to name the autorun.\n *\n * Use `createEmptyChangeSummary` to create a \"change summary\" that can collect the changes.\n * Use `handleChange` to add a reported change to the change summary.\n * The run function is given the last change summary.\n * The change summary is discarded after the run function was called.\n *\n * @see autorun\n */\nexport function autorunHandleChanges(options, fn) {\n    return new AutorunObserver(new DebugNameData(options.owner, options.debugName, options.debugReferenceFn ?? fn), fn, options.createEmptyChangeSummary, options.handleChange);\n}\n/**\n * @see autorunHandleChanges (but with a disposable store that is cleared before the next run or on dispose)\n */\nexport function autorunWithStoreHandleChanges(options, fn) {\n    const store = new DisposableStore();\n    const disposable = autorunHandleChanges({\n        owner: options.owner,\n        debugName: options.debugName,\n        debugReferenceFn: options.debugReferenceFn ?? fn,\n        createEmptyChangeSummary: options.createEmptyChangeSummary,\n        handleChange: options.handleChange,\n    }, (reader, changeSummary) => {\n        store.clear();\n        fn(reader, changeSummary, store);\n    });\n    return toDisposable(() => {\n        disposable.dispose();\n        store.dispose();\n    });\n}\n/**\n * @see autorun (but with a disposable store that is cleared before the next run or on dispose)\n */\nexport function autorunWithStore(fn) {\n    const store = new DisposableStore();\n    const disposable = autorunOpts({\n        owner: undefined,\n        debugName: undefined,\n        debugReferenceFn: fn,\n    }, reader => {\n        store.clear();\n        fn(reader, store);\n    });\n    return toDisposable(() => {\n        disposable.dispose();\n        store.dispose();\n    });\n}\nexport class AutorunObserver {\n    get debugName() {\n        return this._debugNameData.getDebugName(this) ?? '(anonymous)';\n    }\n    constructor(_debugNameData, _runFn, createChangeSummary, _handleChange) {\n        this._debugNameData = _debugNameData;\n        this._runFn = _runFn;\n        this.createChangeSummary = createChangeSummary;\n        this._handleChange = _handleChange;\n        this.state = 2 /* AutorunState.stale */;\n        this.updateCount = 0;\n        this.disposed = false;\n        this.dependencies = new Set();\n        this.dependenciesToBeRemoved = new Set();\n        this.changeSummary = this.createChangeSummary?.();\n        getLogger()?.handleAutorunCreated(this);\n        this._runIfNeeded();\n        trackDisposable(this);\n    }\n    dispose() {\n        this.disposed = true;\n        for (const o of this.dependencies) {\n            o.removeObserver(this);\n        }\n        this.dependencies.clear();\n        markAsDisposed(this);\n    }\n    _runIfNeeded() {\n        if (this.state === 3 /* AutorunState.upToDate */) {\n            return;\n        }\n        const emptySet = this.dependenciesToBeRemoved;\n        this.dependenciesToBeRemoved = this.dependencies;\n        this.dependencies = emptySet;\n        this.state = 3 /* AutorunState.upToDate */;\n        const isDisposed = this.disposed;\n        try {\n            if (!isDisposed) {\n                getLogger()?.handleAutorunTriggered(this);\n                const changeSummary = this.changeSummary;\n                this.changeSummary = this.createChangeSummary?.();\n                this._runFn(this, changeSummary);\n            }\n        }\n        finally {\n            if (!isDisposed) {\n                getLogger()?.handleAutorunFinished(this);\n            }\n            // We don't want our observed observables to think that they are (not even temporarily) not being observed.\n            // Thus, we only unsubscribe from observables that are definitely not read anymore.\n            for (const o of this.dependenciesToBeRemoved) {\n                o.removeObserver(this);\n            }\n            this.dependenciesToBeRemoved.clear();\n        }\n    }\n    toString() {\n        return `Autorun<${this.debugName}>`;\n    }\n    // IObserver implementation\n    beginUpdate() {\n        if (this.state === 3 /* AutorunState.upToDate */) {\n            this.state = 1 /* AutorunState.dependenciesMightHaveChanged */;\n        }\n        this.updateCount++;\n    }\n    endUpdate() {\n        if (this.updateCount === 1) {\n            do {\n                if (this.state === 1 /* AutorunState.dependenciesMightHaveChanged */) {\n                    this.state = 3 /* AutorunState.upToDate */;\n                    for (const d of this.dependencies) {\n                        d.reportChanges();\n                        if (this.state === 2 /* AutorunState.stale */) {\n                            // The other dependencies will refresh on demand\n                            break;\n                        }\n                    }\n                }\n                this._runIfNeeded();\n            } while (this.state !== 3 /* AutorunState.upToDate */);\n        }\n        this.updateCount--;\n        assertFn(() => this.updateCount >= 0);\n    }\n    handlePossibleChange(observable) {\n        if (this.state === 3 /* AutorunState.upToDate */ && this.dependencies.has(observable) && !this.dependenciesToBeRemoved.has(observable)) {\n            this.state = 1 /* AutorunState.dependenciesMightHaveChanged */;\n        }\n    }\n    handleChange(observable, change) {\n        if (this.dependencies.has(observable) && !this.dependenciesToBeRemoved.has(observable)) {\n            const shouldReact = this._handleChange ? this._handleChange({\n                changedObservable: observable,\n                change,\n                didChange: (o) => o === observable,\n            }, this.changeSummary) : true;\n            if (shouldReact) {\n                this.state = 2 /* AutorunState.stale */;\n            }\n        }\n    }\n    // IReader implementation\n    readObservable(observable) {\n        // In case the run action disposes the autorun\n        if (this.disposed) {\n            return observable.get();\n        }\n        observable.addObserver(this);\n        const value = observable.get();\n        this.dependencies.add(observable);\n        this.dependenciesToBeRemoved.delete(observable);\n        return value;\n    }\n}\n(function (autorun) {\n    autorun.Observer = AutorunObserver;\n})(autorun || (autorun = {}));\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,QAAQ,QAAQ,cAAc;AACvC,SAASC,eAAe,EAAEC,cAAc,EAAEC,YAAY,EAAEC,eAAe,QAAQ,iBAAiB;AAChG,SAASC,aAAa,QAAQ,gBAAgB;AAC9C,SAASC,SAAS,QAAQ,cAAc;AACxC;AACA;AACA;AACA;AACA,OAAO,SAASC,OAAOA,CAACC,EAAE,EAAE;EACxB,OAAO,IAAIC,eAAe,CAAC,IAAIJ,aAAa,CAACK,SAAS,EAAEA,SAAS,EAAEF,EAAE,CAAC,EAAEA,EAAE,EAAEE,SAAS,EAAEA,SAAS,CAAC;AACrG;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAACC,OAAO,EAAEJ,EAAE,EAAE;EACrC,OAAO,IAAIC,eAAe,CAAC,IAAIJ,aAAa,CAACO,OAAO,CAACC,KAAK,EAAED,OAAO,CAACE,SAAS,EAAEF,OAAO,CAACG,gBAAgB,IAAIP,EAAE,CAAC,EAAEA,EAAE,EAAEE,SAAS,EAAEA,SAAS,CAAC;AAC7I;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASM,oBAAoBA,CAACJ,OAAO,EAAEJ,EAAE,EAAE;EAC9C,OAAO,IAAIC,eAAe,CAAC,IAAIJ,aAAa,CAACO,OAAO,CAACC,KAAK,EAAED,OAAO,CAACE,SAAS,EAAEF,OAAO,CAACG,gBAAgB,IAAIP,EAAE,CAAC,EAAEA,EAAE,EAAEI,OAAO,CAACK,wBAAwB,EAAEL,OAAO,CAACM,YAAY,CAAC;AAC/K;AACA;AACA;AACA;AACA,OAAO,SAASC,6BAA6BA,CAACP,OAAO,EAAEJ,EAAE,EAAE;EACvD,MAAMY,KAAK,GAAG,IAAInB,eAAe,CAAC,CAAC;EACnC,MAAMoB,UAAU,GAAGL,oBAAoB,CAAC;IACpCH,KAAK,EAAED,OAAO,CAACC,KAAK;IACpBC,SAAS,EAAEF,OAAO,CAACE,SAAS;IAC5BC,gBAAgB,EAAEH,OAAO,CAACG,gBAAgB,IAAIP,EAAE;IAChDS,wBAAwB,EAAEL,OAAO,CAACK,wBAAwB;IAC1DC,YAAY,EAAEN,OAAO,CAACM;EAC1B,CAAC,EAAE,CAACI,MAAM,EAAEC,aAAa,KAAK;IAC1BH,KAAK,CAACI,KAAK,CAAC,CAAC;IACbhB,EAAE,CAACc,MAAM,EAAEC,aAAa,EAAEH,KAAK,CAAC;EACpC,CAAC,CAAC;EACF,OAAOjB,YAAY,CAAC,MAAM;IACtBkB,UAAU,CAACI,OAAO,CAAC,CAAC;IACpBL,KAAK,CAACK,OAAO,CAAC,CAAC;EACnB,CAAC,CAAC;AACN;AACA;AACA;AACA;AACA,OAAO,SAASC,gBAAgBA,CAAClB,EAAE,EAAE;EACjC,MAAMY,KAAK,GAAG,IAAInB,eAAe,CAAC,CAAC;EACnC,MAAMoB,UAAU,GAAGV,WAAW,CAAC;IAC3BE,KAAK,EAAEH,SAAS;IAChBI,SAAS,EAAEJ,SAAS;IACpBK,gBAAgB,EAAEP;EACtB,CAAC,EAAEc,MAAM,IAAI;IACTF,KAAK,CAACI,KAAK,CAAC,CAAC;IACbhB,EAAE,CAACc,MAAM,EAAEF,KAAK,CAAC;EACrB,CAAC,CAAC;EACF,OAAOjB,YAAY,CAAC,MAAM;IACtBkB,UAAU,CAACI,OAAO,CAAC,CAAC;IACpBL,KAAK,CAACK,OAAO,CAAC,CAAC;EACnB,CAAC,CAAC;AACN;AACA,OAAO,MAAMhB,eAAe,CAAC;EACzB,IAAIK,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACa,cAAc,CAACC,YAAY,CAAC,IAAI,CAAC,IAAI,aAAa;EAClE;EACAC,WAAWA,CAACF,cAAc,EAAEG,MAAM,EAAEC,mBAAmB,EAAEC,aAAa,EAAE;IACpE,IAAI,CAACL,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACG,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;IACf,IAAI,CAACC,WAAW,GAAG,CAAC;IACpB,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,YAAY,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC7B,IAAI,CAACC,uBAAuB,GAAG,IAAID,GAAG,CAAC,CAAC;IACxC,IAAI,CAACd,aAAa,GAAG,IAAI,CAACQ,mBAAmB,GAAG,CAAC;IACjDzB,SAAS,CAAC,CAAC,EAAEiC,oBAAoB,CAAC,IAAI,CAAC;IACvC,IAAI,CAACC,YAAY,CAAC,CAAC;IACnBpC,eAAe,CAAC,IAAI,CAAC;EACzB;EACAqB,OAAOA,CAAA,EAAG;IACN,IAAI,CAACU,QAAQ,GAAG,IAAI;IACpB,KAAK,MAAMM,CAAC,IAAI,IAAI,CAACL,YAAY,EAAE;MAC/BK,CAAC,CAACC,cAAc,CAAC,IAAI,CAAC;IAC1B;IACA,IAAI,CAACN,YAAY,CAACZ,KAAK,CAAC,CAAC;IACzBtB,cAAc,CAAC,IAAI,CAAC;EACxB;EACAsC,YAAYA,CAAA,EAAG;IACX,IAAI,IAAI,CAACP,KAAK,KAAK,CAAC,CAAC,6BAA6B;MAC9C;IACJ;IACA,MAAMU,QAAQ,GAAG,IAAI,CAACL,uBAAuB;IAC7C,IAAI,CAACA,uBAAuB,GAAG,IAAI,CAACF,YAAY;IAChD,IAAI,CAACA,YAAY,GAAGO,QAAQ;IAC5B,IAAI,CAACV,KAAK,GAAG,CAAC,CAAC;IACf,MAAMW,UAAU,GAAG,IAAI,CAACT,QAAQ;IAChC,IAAI;MACA,IAAI,CAACS,UAAU,EAAE;QACbtC,SAAS,CAAC,CAAC,EAAEuC,sBAAsB,CAAC,IAAI,CAAC;QACzC,MAAMtB,aAAa,GAAG,IAAI,CAACA,aAAa;QACxC,IAAI,CAACA,aAAa,GAAG,IAAI,CAACQ,mBAAmB,GAAG,CAAC;QACjD,IAAI,CAACD,MAAM,CAAC,IAAI,EAAEP,aAAa,CAAC;MACpC;IACJ,CAAC,SACO;MACJ,IAAI,CAACqB,UAAU,EAAE;QACbtC,SAAS,CAAC,CAAC,EAAEwC,qBAAqB,CAAC,IAAI,CAAC;MAC5C;MACA;MACA;MACA,KAAK,MAAML,CAAC,IAAI,IAAI,CAACH,uBAAuB,EAAE;QAC1CG,CAAC,CAACC,cAAc,CAAC,IAAI,CAAC;MAC1B;MACA,IAAI,CAACJ,uBAAuB,CAACd,KAAK,CAAC,CAAC;IACxC;EACJ;EACAuB,QAAQA,CAAA,EAAG;IACP,OAAO,WAAW,IAAI,CAACjC,SAAS,GAAG;EACvC;EACA;EACAkC,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACf,KAAK,KAAK,CAAC,CAAC,6BAA6B;MAC9C,IAAI,CAACA,KAAK,GAAG,CAAC,CAAC;IACnB;IACA,IAAI,CAACC,WAAW,EAAE;EACtB;EACAe,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAACf,WAAW,KAAK,CAAC,EAAE;MACxB,GAAG;QACC,IAAI,IAAI,CAACD,KAAK,KAAK,CAAC,CAAC,iDAAiD;UAClE,IAAI,CAACA,KAAK,GAAG,CAAC,CAAC;UACf,KAAK,MAAMiB,CAAC,IAAI,IAAI,CAACd,YAAY,EAAE;YAC/Bc,CAAC,CAACC,aAAa,CAAC,CAAC;YACjB,IAAI,IAAI,CAAClB,KAAK,KAAK,CAAC,CAAC,0BAA0B;cAC3C;cACA;YACJ;UACJ;QACJ;QACA,IAAI,CAACO,YAAY,CAAC,CAAC;MACvB,CAAC,QAAQ,IAAI,CAACP,KAAK,KAAK,CAAC,CAAC;IAC9B;IACA,IAAI,CAACC,WAAW,EAAE;IAClBlC,QAAQ,CAAC,MAAM,IAAI,CAACkC,WAAW,IAAI,CAAC,CAAC;EACzC;EACAkB,oBAAoBA,CAACC,UAAU,EAAE;IAC7B,IAAI,IAAI,CAACpB,KAAK,KAAK,CAAC,CAAC,+BAA+B,IAAI,CAACG,YAAY,CAACkB,GAAG,CAACD,UAAU,CAAC,IAAI,CAAC,IAAI,CAACf,uBAAuB,CAACgB,GAAG,CAACD,UAAU,CAAC,EAAE;MACpI,IAAI,CAACpB,KAAK,GAAG,CAAC,CAAC;IACnB;EACJ;EACAf,YAAYA,CAACmC,UAAU,EAAEE,MAAM,EAAE;IAC7B,IAAI,IAAI,CAACnB,YAAY,CAACkB,GAAG,CAACD,UAAU,CAAC,IAAI,CAAC,IAAI,CAACf,uBAAuB,CAACgB,GAAG,CAACD,UAAU,CAAC,EAAE;MACpF,MAAMG,WAAW,GAAG,IAAI,CAACxB,aAAa,GAAG,IAAI,CAACA,aAAa,CAAC;QACxDyB,iBAAiB,EAAEJ,UAAU;QAC7BE,MAAM;QACNG,SAAS,EAAGjB,CAAC,IAAKA,CAAC,KAAKY;MAC5B,CAAC,EAAE,IAAI,CAAC9B,aAAa,CAAC,GAAG,IAAI;MAC7B,IAAIiC,WAAW,EAAE;QACb,IAAI,CAACvB,KAAK,GAAG,CAAC,CAAC;MACnB;IACJ;EACJ;EACA;EACA0B,cAAcA,CAACN,UAAU,EAAE;IACvB;IACA,IAAI,IAAI,CAAClB,QAAQ,EAAE;MACf,OAAOkB,UAAU,CAACO,GAAG,CAAC,CAAC;IAC3B;IACAP,UAAU,CAACQ,WAAW,CAAC,IAAI,CAAC;IAC5B,MAAMC,KAAK,GAAGT,UAAU,CAACO,GAAG,CAAC,CAAC;IAC9B,IAAI,CAACxB,YAAY,CAAC2B,GAAG,CAACV,UAAU,CAAC;IACjC,IAAI,CAACf,uBAAuB,CAAC0B,MAAM,CAACX,UAAU,CAAC;IAC/C,OAAOS,KAAK;EAChB;AACJ;AACA,CAAC,UAAUvD,OAAO,EAAE;EAChBA,OAAO,CAAC0D,QAAQ,GAAGxD,eAAe;AACtC,CAAC,EAAEF,OAAO,KAAKA,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}