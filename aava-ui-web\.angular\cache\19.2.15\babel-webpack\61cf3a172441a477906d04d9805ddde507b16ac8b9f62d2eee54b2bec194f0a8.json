{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport class CombinedSpliceable {\n  constructor(spliceables) {\n    this.spliceables = spliceables;\n  }\n  splice(start, deleteCount, elements) {\n    this.spliceables.forEach(s => s.splice(start, deleteCount, elements));\n  }\n}", "map": {"version": 3, "names": ["CombinedSpliceable", "constructor", "spliceables", "splice", "start", "deleteCount", "elements", "for<PERSON>ach", "s"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/browser/ui/list/splice.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport class CombinedSpliceable {\n    constructor(spliceables) {\n        this.spliceables = spliceables;\n    }\n    splice(start, deleteCount, elements) {\n        this.spliceables.forEach(s => s.splice(start, deleteCount, elements));\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,MAAMA,kBAAkB,CAAC;EAC5BC,WAAWA,CAACC,WAAW,EAAE;IACrB,IAAI,CAACA,WAAW,GAAGA,WAAW;EAClC;EACAC,MAAMA,CAACC,KAAK,EAAEC,WAAW,EAAEC,QAAQ,EAAE;IACjC,IAAI,CAACJ,WAAW,CAACK,OAAO,CAACC,CAAC,IAAIA,CAAC,CAACL,MAAM,CAACC,KAAK,EAAEC,WAAW,EAAEC,QAAQ,CAAC,CAAC;EACzE;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}