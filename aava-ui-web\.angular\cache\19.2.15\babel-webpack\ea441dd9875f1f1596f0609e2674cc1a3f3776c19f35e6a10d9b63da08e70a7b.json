{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { StandardWheelEvent } from '../../mouseEvent.js';\nimport { AbstractScrollbar } from './abstractScrollbar.js';\nimport { ARROW_IMG_SIZE } from './scrollbarArrow.js';\nimport { ScrollbarState } from './scrollbarState.js';\nimport { Codicon } from '../../../common/codicons.js';\nexport class VerticalScrollbar extends AbstractScrollbar {\n  constructor(scrollable, options, host) {\n    const scrollDimensions = scrollable.getScrollDimensions();\n    const scrollPosition = scrollable.getCurrentScrollPosition();\n    super({\n      lazyRender: options.lazyRender,\n      host: host,\n      scrollbarState: new ScrollbarState(options.verticalHasArrows ? options.arrowSize : 0, options.vertical === 2 /* ScrollbarVisibility.Hidden */ ? 0 : options.verticalScrollbarSize,\n      // give priority to vertical scroll bar over horizontal and let it scroll all the way to the bottom\n      0, scrollDimensions.height, scrollDimensions.scrollHeight, scrollPosition.scrollTop),\n      visibility: options.vertical,\n      extraScrollbarClassName: 'vertical',\n      scrollable: scrollable,\n      scrollByPage: options.scrollByPage\n    });\n    if (options.verticalHasArrows) {\n      const arrowDelta = (options.arrowSize - ARROW_IMG_SIZE) / 2;\n      const scrollbarDelta = (options.verticalScrollbarSize - ARROW_IMG_SIZE) / 2;\n      this._createArrow({\n        className: 'scra',\n        icon: Codicon.scrollbarButtonUp,\n        top: arrowDelta,\n        left: scrollbarDelta,\n        bottom: undefined,\n        right: undefined,\n        bgWidth: options.verticalScrollbarSize,\n        bgHeight: options.arrowSize,\n        onActivate: () => this._host.onMouseWheel(new StandardWheelEvent(null, 0, 1))\n      });\n      this._createArrow({\n        className: 'scra',\n        icon: Codicon.scrollbarButtonDown,\n        top: undefined,\n        left: scrollbarDelta,\n        bottom: arrowDelta,\n        right: undefined,\n        bgWidth: options.verticalScrollbarSize,\n        bgHeight: options.arrowSize,\n        onActivate: () => this._host.onMouseWheel(new StandardWheelEvent(null, 0, -1))\n      });\n    }\n    this._createSlider(0, Math.floor((options.verticalScrollbarSize - options.verticalSliderSize) / 2), options.verticalSliderSize, undefined);\n  }\n  _updateSlider(sliderSize, sliderPosition) {\n    this.slider.setHeight(sliderSize);\n    this.slider.setTop(sliderPosition);\n  }\n  _renderDomNode(largeSize, smallSize) {\n    this.domNode.setWidth(smallSize);\n    this.domNode.setHeight(largeSize);\n    this.domNode.setRight(0);\n    this.domNode.setTop(0);\n  }\n  onDidScroll(e) {\n    this._shouldRender = this._onElementScrollSize(e.scrollHeight) || this._shouldRender;\n    this._shouldRender = this._onElementScrollPosition(e.scrollTop) || this._shouldRender;\n    this._shouldRender = this._onElementSize(e.height) || this._shouldRender;\n    return this._shouldRender;\n  }\n  _pointerDownRelativePosition(offsetX, offsetY) {\n    return offsetY;\n  }\n  _sliderPointerPosition(e) {\n    return e.pageY;\n  }\n  _sliderOrthogonalPointerPosition(e) {\n    return e.pageX;\n  }\n  _updateScrollbarSize(size) {\n    this.slider.setWidth(size);\n  }\n  writeScrollPosition(target, scrollPosition) {\n    target.scrollTop = scrollPosition;\n  }\n  updateOptions(options) {\n    this.updateScrollbarSize(options.vertical === 2 /* ScrollbarVisibility.Hidden */ ? 0 : options.verticalScrollbarSize);\n    // give priority to vertical scroll bar over horizontal and let it scroll all the way to the bottom\n    this._scrollbarState.setOppositeScrollbarSize(0);\n    this._visibilityController.setVisibility(options.vertical);\n    this._scrollByPage = options.scrollByPage;\n  }\n}", "map": {"version": 3, "names": ["StandardWheelEvent", "AbstractScrollbar", "ARROW_IMG_SIZE", "ScrollbarState", "Codicon", "VerticalScrollbar", "constructor", "scrollable", "options", "host", "scrollDimensions", "getScrollDimensions", "scrollPosition", "getCurrentScrollPosition", "lazy<PERSON>ender", "scrollbarState", "verticalHasArrows", "arrowSize", "vertical", "verticalScrollbarSize", "height", "scrollHeight", "scrollTop", "visibility", "extraScrollbarClassName", "scrollByPage", "arrow<PERSON><PERSON><PERSON>", "scrollbarDel<PERSON>", "_createArrow", "className", "icon", "scrollbarButtonUp", "top", "left", "bottom", "undefined", "right", "bgWidth", "bgHeight", "onActivate", "_host", "onMouseWheel", "scrollbarButtonDown", "_createSlider", "Math", "floor", "verticalSliderSize", "_updateSlider", "sliderSize", "sliderPosition", "slider", "setHeight", "setTop", "_renderDomNode", "largeSize", "smallSize", "domNode", "<PERSON><PERSON><PERSON><PERSON>", "setRight", "onDidScroll", "e", "_shouldRender", "_onElementScrollSize", "_onElementScrollPosition", "_onElementSize", "_pointerDownRelativePosition", "offsetX", "offsetY", "_sliderPointerPosition", "pageY", "_sliderOrthogonalPointerPosition", "pageX", "_updateScrollbarSize", "size", "writeScrollPosition", "target", "updateOptions", "updateScrollbarSize", "_scrollbarState", "setOppositeScrollbarSize", "_visibilityController", "setVisibility", "_scrollByPage"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/browser/ui/scrollbar/verticalScrollbar.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { StandardWheelEvent } from '../../mouseEvent.js';\nimport { AbstractScrollbar } from './abstractScrollbar.js';\nimport { ARROW_IMG_SIZE } from './scrollbarArrow.js';\nimport { ScrollbarState } from './scrollbarState.js';\nimport { Codicon } from '../../../common/codicons.js';\nexport class VerticalScrollbar extends AbstractScrollbar {\n    constructor(scrollable, options, host) {\n        const scrollDimensions = scrollable.getScrollDimensions();\n        const scrollPosition = scrollable.getCurrentScrollPosition();\n        super({\n            lazyRender: options.lazyRender,\n            host: host,\n            scrollbarState: new ScrollbarState((options.verticalHasArrows ? options.arrowSize : 0), (options.vertical === 2 /* ScrollbarVisibility.Hidden */ ? 0 : options.verticalScrollbarSize), \n            // give priority to vertical scroll bar over horizontal and let it scroll all the way to the bottom\n            0, scrollDimensions.height, scrollDimensions.scrollHeight, scrollPosition.scrollTop),\n            visibility: options.vertical,\n            extraScrollbarClassName: 'vertical',\n            scrollable: scrollable,\n            scrollByPage: options.scrollByPage\n        });\n        if (options.verticalHasArrows) {\n            const arrowDelta = (options.arrowSize - ARROW_IMG_SIZE) / 2;\n            const scrollbarDelta = (options.verticalScrollbarSize - ARROW_IMG_SIZE) / 2;\n            this._createArrow({\n                className: 'scra',\n                icon: Codicon.scrollbarButtonUp,\n                top: arrowDelta,\n                left: scrollbarDelta,\n                bottom: undefined,\n                right: undefined,\n                bgWidth: options.verticalScrollbarSize,\n                bgHeight: options.arrowSize,\n                onActivate: () => this._host.onMouseWheel(new StandardWheelEvent(null, 0, 1)),\n            });\n            this._createArrow({\n                className: 'scra',\n                icon: Codicon.scrollbarButtonDown,\n                top: undefined,\n                left: scrollbarDelta,\n                bottom: arrowDelta,\n                right: undefined,\n                bgWidth: options.verticalScrollbarSize,\n                bgHeight: options.arrowSize,\n                onActivate: () => this._host.onMouseWheel(new StandardWheelEvent(null, 0, -1)),\n            });\n        }\n        this._createSlider(0, Math.floor((options.verticalScrollbarSize - options.verticalSliderSize) / 2), options.verticalSliderSize, undefined);\n    }\n    _updateSlider(sliderSize, sliderPosition) {\n        this.slider.setHeight(sliderSize);\n        this.slider.setTop(sliderPosition);\n    }\n    _renderDomNode(largeSize, smallSize) {\n        this.domNode.setWidth(smallSize);\n        this.domNode.setHeight(largeSize);\n        this.domNode.setRight(0);\n        this.domNode.setTop(0);\n    }\n    onDidScroll(e) {\n        this._shouldRender = this._onElementScrollSize(e.scrollHeight) || this._shouldRender;\n        this._shouldRender = this._onElementScrollPosition(e.scrollTop) || this._shouldRender;\n        this._shouldRender = this._onElementSize(e.height) || this._shouldRender;\n        return this._shouldRender;\n    }\n    _pointerDownRelativePosition(offsetX, offsetY) {\n        return offsetY;\n    }\n    _sliderPointerPosition(e) {\n        return e.pageY;\n    }\n    _sliderOrthogonalPointerPosition(e) {\n        return e.pageX;\n    }\n    _updateScrollbarSize(size) {\n        this.slider.setWidth(size);\n    }\n    writeScrollPosition(target, scrollPosition) {\n        target.scrollTop = scrollPosition;\n    }\n    updateOptions(options) {\n        this.updateScrollbarSize(options.vertical === 2 /* ScrollbarVisibility.Hidden */ ? 0 : options.verticalScrollbarSize);\n        // give priority to vertical scroll bar over horizontal and let it scroll all the way to the bottom\n        this._scrollbarState.setOppositeScrollbarSize(0);\n        this._visibilityController.setVisibility(options.vertical);\n        this._scrollByPage = options.scrollByPage;\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,kBAAkB,QAAQ,qBAAqB;AACxD,SAASC,iBAAiB,QAAQ,wBAAwB;AAC1D,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,OAAO,QAAQ,6BAA6B;AACrD,OAAO,MAAMC,iBAAiB,SAASJ,iBAAiB,CAAC;EACrDK,WAAWA,CAACC,UAAU,EAAEC,OAAO,EAAEC,IAAI,EAAE;IACnC,MAAMC,gBAAgB,GAAGH,UAAU,CAACI,mBAAmB,CAAC,CAAC;IACzD,MAAMC,cAAc,GAAGL,UAAU,CAACM,wBAAwB,CAAC,CAAC;IAC5D,KAAK,CAAC;MACFC,UAAU,EAAEN,OAAO,CAACM,UAAU;MAC9BL,IAAI,EAAEA,IAAI;MACVM,cAAc,EAAE,IAAIZ,cAAc,CAAEK,OAAO,CAACQ,iBAAiB,GAAGR,OAAO,CAACS,SAAS,GAAG,CAAC,EAAIT,OAAO,CAACU,QAAQ,KAAK,CAAC,CAAC,mCAAmC,CAAC,GAAGV,OAAO,CAACW,qBAAqB;MACpL;MACA,CAAC,EAAET,gBAAgB,CAACU,MAAM,EAAEV,gBAAgB,CAACW,YAAY,EAAET,cAAc,CAACU,SAAS,CAAC;MACpFC,UAAU,EAAEf,OAAO,CAACU,QAAQ;MAC5BM,uBAAuB,EAAE,UAAU;MACnCjB,UAAU,EAAEA,UAAU;MACtBkB,YAAY,EAAEjB,OAAO,CAACiB;IAC1B,CAAC,CAAC;IACF,IAAIjB,OAAO,CAACQ,iBAAiB,EAAE;MAC3B,MAAMU,UAAU,GAAG,CAAClB,OAAO,CAACS,SAAS,GAAGf,cAAc,IAAI,CAAC;MAC3D,MAAMyB,cAAc,GAAG,CAACnB,OAAO,CAACW,qBAAqB,GAAGjB,cAAc,IAAI,CAAC;MAC3E,IAAI,CAAC0B,YAAY,CAAC;QACdC,SAAS,EAAE,MAAM;QACjBC,IAAI,EAAE1B,OAAO,CAAC2B,iBAAiB;QAC/BC,GAAG,EAAEN,UAAU;QACfO,IAAI,EAAEN,cAAc;QACpBO,MAAM,EAAEC,SAAS;QACjBC,KAAK,EAAED,SAAS;QAChBE,OAAO,EAAE7B,OAAO,CAACW,qBAAqB;QACtCmB,QAAQ,EAAE9B,OAAO,CAACS,SAAS;QAC3BsB,UAAU,EAAEA,CAAA,KAAM,IAAI,CAACC,KAAK,CAACC,YAAY,CAAC,IAAIzC,kBAAkB,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;MAChF,CAAC,CAAC;MACF,IAAI,CAAC4B,YAAY,CAAC;QACdC,SAAS,EAAE,MAAM;QACjBC,IAAI,EAAE1B,OAAO,CAACsC,mBAAmB;QACjCV,GAAG,EAAEG,SAAS;QACdF,IAAI,EAAEN,cAAc;QACpBO,MAAM,EAAER,UAAU;QAClBU,KAAK,EAAED,SAAS;QAChBE,OAAO,EAAE7B,OAAO,CAACW,qBAAqB;QACtCmB,QAAQ,EAAE9B,OAAO,CAACS,SAAS;QAC3BsB,UAAU,EAAEA,CAAA,KAAM,IAAI,CAACC,KAAK,CAACC,YAAY,CAAC,IAAIzC,kBAAkB,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;MACjF,CAAC,CAAC;IACN;IACA,IAAI,CAAC2C,aAAa,CAAC,CAAC,EAAEC,IAAI,CAACC,KAAK,CAAC,CAACrC,OAAO,CAACW,qBAAqB,GAAGX,OAAO,CAACsC,kBAAkB,IAAI,CAAC,CAAC,EAAEtC,OAAO,CAACsC,kBAAkB,EAAEX,SAAS,CAAC;EAC9I;EACAY,aAAaA,CAACC,UAAU,EAAEC,cAAc,EAAE;IACtC,IAAI,CAACC,MAAM,CAACC,SAAS,CAACH,UAAU,CAAC;IACjC,IAAI,CAACE,MAAM,CAACE,MAAM,CAACH,cAAc,CAAC;EACtC;EACAI,cAAcA,CAACC,SAAS,EAAEC,SAAS,EAAE;IACjC,IAAI,CAACC,OAAO,CAACC,QAAQ,CAACF,SAAS,CAAC;IAChC,IAAI,CAACC,OAAO,CAACL,SAAS,CAACG,SAAS,CAAC;IACjC,IAAI,CAACE,OAAO,CAACE,QAAQ,CAAC,CAAC,CAAC;IACxB,IAAI,CAACF,OAAO,CAACJ,MAAM,CAAC,CAAC,CAAC;EAC1B;EACAO,WAAWA,CAACC,CAAC,EAAE;IACX,IAAI,CAACC,aAAa,GAAG,IAAI,CAACC,oBAAoB,CAACF,CAAC,CAACvC,YAAY,CAAC,IAAI,IAAI,CAACwC,aAAa;IACpF,IAAI,CAACA,aAAa,GAAG,IAAI,CAACE,wBAAwB,CAACH,CAAC,CAACtC,SAAS,CAAC,IAAI,IAAI,CAACuC,aAAa;IACrF,IAAI,CAACA,aAAa,GAAG,IAAI,CAACG,cAAc,CAACJ,CAAC,CAACxC,MAAM,CAAC,IAAI,IAAI,CAACyC,aAAa;IACxE,OAAO,IAAI,CAACA,aAAa;EAC7B;EACAI,4BAA4BA,CAACC,OAAO,EAAEC,OAAO,EAAE;IAC3C,OAAOA,OAAO;EAClB;EACAC,sBAAsBA,CAACR,CAAC,EAAE;IACtB,OAAOA,CAAC,CAACS,KAAK;EAClB;EACAC,gCAAgCA,CAACV,CAAC,EAAE;IAChC,OAAOA,CAAC,CAACW,KAAK;EAClB;EACAC,oBAAoBA,CAACC,IAAI,EAAE;IACvB,IAAI,CAACvB,MAAM,CAACO,QAAQ,CAACgB,IAAI,CAAC;EAC9B;EACAC,mBAAmBA,CAACC,MAAM,EAAE/D,cAAc,EAAE;IACxC+D,MAAM,CAACrD,SAAS,GAAGV,cAAc;EACrC;EACAgE,aAAaA,CAACpE,OAAO,EAAE;IACnB,IAAI,CAACqE,mBAAmB,CAACrE,OAAO,CAACU,QAAQ,KAAK,CAAC,CAAC,mCAAmC,CAAC,GAAGV,OAAO,CAACW,qBAAqB,CAAC;IACrH;IACA,IAAI,CAAC2D,eAAe,CAACC,wBAAwB,CAAC,CAAC,CAAC;IAChD,IAAI,CAACC,qBAAqB,CAACC,aAAa,CAACzE,OAAO,CAACU,QAAQ,CAAC;IAC1D,IAAI,CAACgE,aAAa,GAAG1E,OAAO,CAACiB,YAAY;EAC7C;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}