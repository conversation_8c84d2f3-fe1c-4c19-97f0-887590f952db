{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { LegacyLinesDiffComputer } from './legacyLinesDiffComputer.js';\nimport { DefaultLinesDiffComputer } from './defaultLinesDiffComputer/defaultLinesDiffComputer.js';\nexport const linesDiffComputers = {\n  getLegacy: () => new LegacyLinesDiffComputer(),\n  getDefault: () => new DefaultLinesDiffComputer()\n};", "map": {"version": 3, "names": ["LegacyLinesDiffComputer", "DefaultLinesDiffComputer", "linesDiffComputers", "getLegacy", "getDefault"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/diff/linesDiffComputers.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { LegacyLinesDiffComputer } from './legacyLinesDiffComputer.js';\nimport { DefaultLinesDiffComputer } from './defaultLinesDiffComputer/defaultLinesDiffComputer.js';\nexport const linesDiffComputers = {\n    getLegacy: () => new LegacyLinesDiffComputer(),\n    getDefault: () => new DefaultLinesDiffComputer(),\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,uBAAuB,QAAQ,8BAA8B;AACtE,SAASC,wBAAwB,QAAQ,wDAAwD;AACjG,OAAO,MAAMC,kBAAkB,GAAG;EAC9BC,SAAS,EAAEA,CAAA,KAAM,IAAIH,uBAAuB,CAAC,CAAC;EAC9CI,UAAU,EAAEA,CAAA,KAAM,IAAIH,wBAAwB,CAAC;AACnD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}