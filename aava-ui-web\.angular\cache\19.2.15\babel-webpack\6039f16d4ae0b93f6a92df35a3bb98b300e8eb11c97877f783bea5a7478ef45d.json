{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport class LinePart {\n  constructor(\n  /**\n   * last char index of this token (not inclusive).\n   */\n  endIndex, type, metadata, containsRTL) {\n    this.endIndex = endIndex;\n    this.type = type;\n    this.metadata = metadata;\n    this.containsRTL = containsRTL;\n    this._linePartBrand = undefined;\n  }\n  isWhitespace() {\n    return this.metadata & 1 /* LinePartMetadata.IS_WHITESPACE_MASK */ ? true : false;\n  }\n  isPseudoAfter() {\n    return this.metadata & 4 /* LinePartMetadata.PSEUDO_AFTER_MASK */ ? true : false;\n  }\n}", "map": {"version": 3, "names": ["LinePart", "constructor", "endIndex", "type", "metadata", "containsRTL", "_line<PERSON><PERSON><PERSON>rand", "undefined", "isWhitespace", "isPseudoAfter"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/viewLayout/linePart.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport class LinePart {\n    constructor(\n    /**\n     * last char index of this token (not inclusive).\n     */\n    endIndex, type, metadata, containsRTL) {\n        this.endIndex = endIndex;\n        this.type = type;\n        this.metadata = metadata;\n        this.containsRTL = containsRTL;\n        this._linePartBrand = undefined;\n    }\n    isWhitespace() {\n        return (this.metadata & 1 /* LinePartMetadata.IS_WHITESPACE_MASK */ ? true : false);\n    }\n    isPseudoAfter() {\n        return (this.metadata & 4 /* LinePartMetadata.PSEUDO_AFTER_MASK */ ? true : false);\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,MAAMA,QAAQ,CAAC;EAClBC,WAAWA;EACX;AACJ;AACA;EACIC,QAAQ,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,WAAW,EAAE;IACnC,IAAI,CAACH,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,cAAc,GAAGC,SAAS;EACnC;EACAC,YAAYA,CAAA,EAAG;IACX,OAAQ,IAAI,CAACJ,QAAQ,GAAG,CAAC,CAAC,4CAA4C,IAAI,GAAG,KAAK;EACtF;EACAK,aAAaA,CAAA,EAAG;IACZ,OAAQ,IAAI,CAACL,QAAQ,GAAG,CAAC,CAAC,2CAA2C,IAAI,GAAG,KAAK;EACrF;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}