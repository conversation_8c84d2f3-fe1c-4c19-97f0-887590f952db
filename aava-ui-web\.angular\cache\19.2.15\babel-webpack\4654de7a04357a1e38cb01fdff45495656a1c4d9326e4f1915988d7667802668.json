{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport class HoverResult {\n  constructor(anchor, hoverParts, isComplete) {\n    this.anchor = anchor;\n    this.hoverParts = hoverParts;\n    this.isComplete = isComplete;\n  }\n  filter(anchor) {\n    const filteredHoverParts = this.hoverParts.filter(m => m.isValidForHoverAnchor(anchor));\n    if (filteredHoverParts.length === this.hoverParts.length) {\n      return this;\n    }\n    return new FilteredHoverResult(this, this.anchor, filteredHoverParts, this.isComplete);\n  }\n}\nexport class FilteredHoverResult extends HoverResult {\n  constructor(original, anchor, messages, isComplete) {\n    super(anchor, messages, isComplete);\n    this.original = original;\n  }\n  filter(anchor) {\n    return this.original.filter(anchor);\n  }\n}", "map": {"version": 3, "names": ["HoverResult", "constructor", "anchor", "hoverParts", "isComplete", "filter", "filteredHoverParts", "m", "isValidForHoverAnchor", "length", "FilteredHoverResult", "original", "messages"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/contrib/hover/browser/contentHoverTypes.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport class HoverResult {\n    constructor(anchor, hoverParts, isComplete) {\n        this.anchor = anchor;\n        this.hoverParts = hoverParts;\n        this.isComplete = isComplete;\n    }\n    filter(anchor) {\n        const filteredHoverParts = this.hoverParts.filter((m) => m.isValidForHoverAnchor(anchor));\n        if (filteredHoverParts.length === this.hoverParts.length) {\n            return this;\n        }\n        return new FilteredHoverResult(this, this.anchor, filteredHoverParts, this.isComplete);\n    }\n}\nexport class FilteredHoverResult extends HoverResult {\n    constructor(original, anchor, messages, isComplete) {\n        super(anchor, messages, isComplete);\n        this.original = original;\n    }\n    filter(anchor) {\n        return this.original.filter(anchor);\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,MAAMA,WAAW,CAAC;EACrBC,WAAWA,CAACC,MAAM,EAAEC,UAAU,EAAEC,UAAU,EAAE;IACxC,IAAI,CAACF,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,UAAU,GAAGA,UAAU;EAChC;EACAC,MAAMA,CAACH,MAAM,EAAE;IACX,MAAMI,kBAAkB,GAAG,IAAI,CAACH,UAAU,CAACE,MAAM,CAAEE,CAAC,IAAKA,CAAC,CAACC,qBAAqB,CAACN,MAAM,CAAC,CAAC;IACzF,IAAII,kBAAkB,CAACG,MAAM,KAAK,IAAI,CAACN,UAAU,CAACM,MAAM,EAAE;MACtD,OAAO,IAAI;IACf;IACA,OAAO,IAAIC,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAACR,MAAM,EAAEI,kBAAkB,EAAE,IAAI,CAACF,UAAU,CAAC;EAC1F;AACJ;AACA,OAAO,MAAMM,mBAAmB,SAASV,WAAW,CAAC;EACjDC,WAAWA,CAACU,QAAQ,EAAET,MAAM,EAAEU,QAAQ,EAAER,UAAU,EAAE;IAChD,KAAK,CAACF,MAAM,EAAEU,QAAQ,EAAER,UAAU,CAAC;IACnC,IAAI,CAACO,QAAQ,GAAGA,QAAQ;EAC5B;EACAN,MAAMA,CAACH,MAAM,EAAE;IACX,OAAO,IAAI,CAACS,QAAQ,CAACN,MAAM,CAACH,MAAM,CAAC;EACvC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}