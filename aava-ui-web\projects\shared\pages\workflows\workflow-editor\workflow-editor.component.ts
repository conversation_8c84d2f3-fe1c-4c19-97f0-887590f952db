import {
  <PERSON>mpo<PERSON>,
  <PERSON><PERSON><PERSON>t,
  <PERSON><PERSON><PERSON><PERSON>,
  AfterViewInit,
  ChangeDetectorRef,
  Input,
  ElementRef,
  ViewChild,
  HostListener,
  ViewContainerRef,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  FormControl,
} from '@angular/forms';
import { Router, ActivatedRoute, RouterModule } from '@angular/router';
import {
  WorkflowGraphService,
  WorkflowNode,
  WorkflowEdge,
} from './services/workflow-graph.service';
import { ReactFlowService } from './services/react-flow.service';
import { AgentNodeComponent } from './components/agent-node/agent-node.component';
import { debounceTime, distinctUntilChanged, map, startWith, Subscription } from 'rxjs';
import { Agent } from './models/agent.model';
import { DragDropModule } from '@angular/cdk/drag-drop';
import workflowLabels from './../constants/workflows.json';
import { WorkflowService } from '@shared/services/workflow.service';
import {
  CanvasBoardComponent,
  CanvasNode,
  CanvasEdge,
} from '@shared/components/canvas-board/canvas-board.component';

import {
  AvaTextboxComponent,
  ButtonComponent,
  DropdownComponent,
  DropdownOption,
  IconComponent,
  DialogService,
  SliderComponent,
  ToggleComponent,
} from '@ava/play-comp-library';
import { TokenStorageService } from '@shared/index';
import { Model } from '@shared/models/card.model';
import { WorkflowModes } from '../constants/workflow.constants';
import { AgentServiceService } from '@shared/pages/agents/services/agent-service.service';
import { WorkflowPreviewPanelComponent } from './workflow-preview-panel/workflow-preview-panel.component';
import { DrawerService } from '@shared/services/drawer/drawer.service';

interface NodePosition {
  x: number;
  y: number;
}

@Component({
  selector: 'app-workflow-editor',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterModule,
    AgentNodeComponent,
    DragDropModule,
    CanvasBoardComponent,
    ToggleComponent,
    ButtonComponent,
    IconComponent,
    AvaTextboxComponent,
    SliderComponent,
    DropdownComponent
  ],
  providers: [DialogService],
  templateUrl: './workflow-editor.component.html',
  styleUrls: ['./workflow-editor.component.scss'],
})
export class WorkflowEditorComponent
  implements OnInit, AfterViewInit, OnDestroy
{
  @ViewChild('drawerContainer', { read: ViewContainerRef }) drawerContainer!: ViewContainerRef;
  @Input() primaryButtonText: string = '';

  workflowId: string | null = null;
  isEditMode: boolean = false;
  isDuplicateMode: boolean = false;
  workflowForm: FormGroup;
  modelList: DropdownOption[] = [];

  savedWorkFlowDetils: Record<string, any> = {};

  // Labels used across the Knowledge Base UI components (titles)
  public workFlowLabels = workflowLabels.labels;

  showLlm = false;

  levels = ['organization', 'domain', 'project', 'team'];
  filterLabels = ['organization', 'domain', 'project', 'team'];
  optionsMap: { [level: number]: any[] } = {};
  levelOptionsMap: Record<number, any[]> = {};

  inputFieldsConfig = {
    // agentName: { enabled: true, placeholder: 'Workflow Name', required: true },
    agentName: { enabled: true, placeholder: 'Agent Name', required: true },
    agentDetails: {
      enabled: true, // Re-enabled - this is the correct Workflow Details section
      label: 'Workflow Details',
      namePlaceholder: 'Enter Workflow name',
      detailPlaceholder: 'Enter workflow description',
      detailLabel: 'Description',
    },
  };

  // Canvas board properties
  canvasNodes: CanvasNode[] = [];
  canvasEdges: CanvasEdge[] = [];
  navigationHints: string[] = [
    // `${workflowLabels.labels.alt} + ${workflowLabels.labels.drag} ${workflowLabels.labels.toPanCanvas}`,
    // `${workflowLabels.labels.mouseWheel} ${workflowLabels.labels.toZoom}`,
    // `${workflowLabels.labels.space} ${workflowLabels.labels.toResetView}`
  ];

  // Agent library
  agents: Agent[] = [
    {
      id: 'agent1',
      name: 'Test Agent Agent Agent Agent Agent Agent',
      description: 'Translating user needs into actionable development tasks.',
      type: 'Individual',
      capabilities: ['Code Generation', 'Translation'],
    },
    {
      id: 'agent2',
      name: 'Test Agent 2',
      description: 'Processing data and generating insights.',
      type: 'Individual',
      capabilities: ['Data Analysis', 'Visualization'],
    },
    {
      id: 'agent3',
      name: 'Test Agent 3',
      description: 'Handling complex natural language processing tasks.',
      type: 'Collaborative',
      capabilities: ['NLP', 'Summarization', 'Translation'],
    },
  ];

  // Filtered agents for search
  filteredAgents: Agent[] = [];

  // Available agents (filtered and not used yet)
  availableAgents: Agent[] = [];

  // Workflow nodes and edges (original format)
  nodes: WorkflowNode[] = [];
  edges: WorkflowEdge[] = [];

  // Selected node
  selectedNodeId: string | null = null;

  // Subscriptions
  private subscriptions: Subscription[] = [];

  // Track used agent IDs
  usedAgentIds: Set<string> = new Set();

  // Panel state
  isPanelCollapsed: boolean = false;
  public currentPage = 1;
  public pageSize = 50;
  public isDeleted = false;

  // Search form for the panel
  searchForm: FormGroup;
  builtInToolValues: any[] = [];
  private nodeToolValues: Map<string, any[]> = new Map();
  workflowData: any = {};
  totalNumberOfRecords: number = 0;

  @ViewChild('llmSettingsRef') llmSettingsRef!: ElementRef;


  constructor(
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private workflowGraphService: WorkflowGraphService,
    private workflowService: WorkflowService,
    private reactFlowService: ReactFlowService,
    private cdr: ChangeDetectorRef,
    private agentService: AgentServiceService,
    private tokenStorage: TokenStorageService,
    private drawerService: DrawerService,
    private dialogService: DialogService,
  ) {
    this.workflowForm = this.fb.group({
      // Workflow details
      name: [''],
      description: [''],

      // Filters
      organization: [''],
      domain: [''],
      project: [''],
      team: [''],

      // Enable Manager LLM
      enableManagerLLM: [false],

      // LLM Configuration settings
      modelDeploymentName: [],
      temperature: [0.3],
      topP: [0.95],
      maxRPM: [0],
      maxToken: [4000],
      maxIteration: [1],
      maxExecutionTime: [30],

      // Search filter
      agentFilter: [''],
    });

    // Initialize search form for the panel
    this.searchForm = this.fb.group({
      agentFilter: [''],
    });
  }

  searchList() {
    this.searchForm
      .get('agentFilter')!
      .valueChanges.pipe(
        startWith(''),
        debounceTime(300),
        distinctUntilChanged(),
        map((value) => value?.toLowerCase() ?? ''),
      )
      .subscribe((searchText) => {
        this.filterWorkflow(searchText);
      });
  }

  filterWorkflow(searchText: string): void {
    this.filteredAgents = this.availableAgents.filter((res: any) => {
      const inTitle = res.name?.toLowerCase().includes(searchText);
    
      return inTitle;
    });
  }

  // Panel toggle functionality
  togglePanel(): void {
    this.isPanelCollapsed = !this.isPanelCollapsed;
  }

  // Item preview functionality
  onItemPreview(agent: Agent): void {
    console.log('Preview agent:', agent);
    // Implement preview functionality
    this.drawerService.open(WorkflowPreviewPanelComponent, {
      previewData: {
        type: 'agent',
        title: agent.name,
        data: agent,
      },
      closePreview: () => this.drawerService.clear(),
    });
  }

  // Create new agent functionality
  onCreateNewAgent(): void {
    console.log('Create new agent');
    // Navigate to agent creation page or open modal
    this.router.navigate(['/build/agents/create']);
  }

  isModeDuplicate() {
    return this.route.snapshot.queryParams['mode'] === WorkflowModes.duplicate;
  }

  getAndPatchWorkFlowDetails(id: string) {
    const isDuplicateMode = this.isModeDuplicate();

    this.workflowService.getWorkflowById(id).subscribe((response) => {
      this.savedWorkFlowDetils = response;
      // console.log(this.savedWorkFlowDetils);
      const agents = (response?.workflowAgents || []) as any[];
      agents.sort((a: any, b: any) => a.serial - b.serial);
      let x = 400;
      let y = 150;
      // console.log(agents);
      agents.forEach((agent, i) => {
        this.createNewNode(agent.agentDetails, { x, y });
        x += 400;
        const position = i + 1;
        if (position % 3 == 0) {
          y += 120;
          x = 40;
        }
      });
      this.workflowData = response;

      const formData: Record<string, any> = {};

      if(!this.isEditMode){
        formData['createdBy'] = response?.createdBy;
      }

      if (!isDuplicateMode) {
        formData['name'] = response?.name;
        formData['description'] = response?.description;
      }

      if (response?.managerLlm) {
        formData['enableManagerLLM'] = true;
        Object.assign(formData, response?.managerLlm);
      }

      this.workflowForm.patchValue(formData);
    });
  }

  ngOnInit(): void {
    this.searchList();
    // this.fetchChildOptions(0, -1);
    // Check if we're in edit mode
    this.workflowId =
      this.route.snapshot.paramMap.get('id') ||
      this.route.snapshot.queryParams['id'];
    this.isEditMode = !!this.workflowId;

    const mode = this.route.snapshot.queryParams['mode'];
    if (mode === 'duplicate') {
      this.isEditMode = false;
      this.isDuplicateMode = true;
    }

    if (this.isEditMode && this.workflowId) {
      // In a real app, you would fetch the workflow data by ID
      console.log(`Editing workflow with ID: ${this.workflowId}`);
      this.getAndPatchWorkFlowDetails(this.workflowId);
      // this.loadWorkflowData(this.workflowId);
    }

    if(this.isDuplicateMode && this.workflowId){
      console.log(`Cloning workflow with ID: ${this.workflowId}`);
      this.getAndPatchWorkFlowDetails(this.workflowId);
    }

    // Subscribe to nodes to track used agents
    this.subscriptions.push(
      this.workflowGraphService.nodes$.subscribe((nodes) => {
        this.nodes = nodes;
        this.canvasNodes = this.convertToCanvasNodes(nodes);

        // Update used agent IDs
        this.updateUsedAgentIds();
      }),
    );

    this.subscriptions.push(
      this.workflowGraphService.edges$.subscribe((edges) => {
        this.edges = edges;
        this.canvasEdges = this.convertToCanvasEdges(edges);
      }),
    );

    // Subscribe to the search filter changes
    this.subscriptions.push(
      this.getControl('agentFilter').valueChanges.subscribe((filterValue) => {
        this.filterAgents(filterValue);
      }),
    );

    this.getCollaborativeAgents();      

    this.workflowService.getAllGenerativeModels().subscribe((response) => {
      this.modelList = response.map((modle: Model) => {
        return {
          name: modle.modelDeploymentName,
          value: modle.modelDeploymentName,
        };
      });
    });
  }

  ngAfterViewInit(): void {
    // Canvas board handles its own initialization
     this.drawerService.registerViewContainer(this.drawerContainer);
  }

  private getCollaborativeAgents(){
    this.agentService
      .getAllCollaborativeAgentsPagination(this.currentPage, this.totalNumberOfRecords, this.isDeleted)
      .subscribe((response : any) => {
        this.agents = response.agentDetails;
        this.filterAgents(this.getControl('agentFilter').value);
        // Making sure the dropdown value get patched agin after options are added
        setTimeout(() => {
          this.workflowForm.patchValue({
            modelDeploymentName:
              this.savedWorkFlowDetils['managerLlm']?.modelDeploymentName,
          });
        });
      });
  }

  // HostListener to close llm-settings when clicking outside
  @HostListener('document:mousedown', ['$event'])
  onDocumentClick(event: MouseEvent) {
    if (this.showLlm && this.llmSettingsRef) {
      const target = event.target as HTMLElement;
      if (!this.llmSettingsRef.nativeElement.contains(target)) {
        this.showLlm = false;
      }
    }
  }

  // Convert WorkflowNode to CanvasNode
  private convertToCanvasNodes(nodes: WorkflowNode[]): CanvasNode[] {
    return nodes.map((node) => ({
      id: node.id,
      type: node.type,
      data: node.data,
      position: node.position,
    }));
  }

  // Convert WorkflowEdge to CanvasEdge
  private convertToCanvasEdges(edges: WorkflowEdge[]): CanvasEdge[] {
    return edges.map((edge) => ({
      id: edge.id,
      source: edge.source,
      target: edge.target,
      animated: edge.animated,
    }));
  }

  private convertNodesToEdges(nodes: WorkflowNode[]) {
    return nodes.slice(1).map((node, i) => {
      const perviousNode = i <= 0 ? nodes[0] : nodes[i - 1];

      return {
        id: `${perviousNode.id}-${node.id}`,
        source: perviousNode.id,
        target: node.id,
        // animated: node.animated,
      };
    });
  }

  onLevelChange(level: number, selectedValue: string | string[]): void {
    const selected = Array.isArray(selectedValue)
      ? selectedValue[0]
      : selectedValue;

    if (!selected) {
      return;
    }

    const controlName = this.filterLabels[level];
    const control = this.workflowForm.get(controlName);

    if (control) {
      control.setValue(selected);
    }

    // Reset controls and options for levels below the current one
    for (let i = level + 1; i < this.levels.length; i++) {
      this.resetControlAtLevel(i);
      this.levelOptionsMap[i] = [];
    }

    const selectedNumber = Number(selected);
    if (!isNaN(selectedNumber)) {
      this.fetchChildOptions(level + 1, selectedNumber);
    }
  }

  resetControlAtLevel(level: number): void {
    const controlName = this.filterLabels[level];
    const control = this.workflowForm.get(controlName);
    if (control) {
      control.setValue(null);
    }
  }

  getOptionsForLevel(level: number): any[] {
    return this.levelOptionsMap[level] || [];
  }

  fetchChildOptions(level: number, parentId: number) {
    if (!this.filterLabels[level]) return;

    this.workflowService.getDropdownList(level, parentId).subscribe({
      next: (res) => {
        this.levelOptionsMap[level] = Array.isArray(res) ? res : [];
      },
      error: () => {
        this.levelOptionsMap[level] = [];
      },
    });
  }
  ngOnDestroy(): void {
    // Clean up subscriptions
    this.subscriptions.forEach((sub) => sub.unsubscribe());
    this.workflowGraphService.clearWorkflow();
  }

  createNewNode(agent: any, position: { x: number; y: number }) {
    if (!agent) {
      console.error('Agent is undefined');
      return;
    }
    const agentTools = agent.agentConfigs?.toolRef || [];
    const agentUserTools = agent.agentConfigs?.userToolRef || [];
    const tools = [...agentTools, ...agentUserTools]
      .map((tool) => tool?.toolName)
      .filter(Boolean) // Filter out any undefined or null values
      .join(', ');
    const model = agent.agentConfigs?.modelRef?.[0] || {};
    const newNode: WorkflowNode = {
      id: this.workflowGraphService.generateNodeId(),
      type: 'agent',
      data: {
        label: agent.name,
        agentId: agent.id,
        agentName: agent.name,
        description: agent.description,
        capabilities: agent.expectedOutput,
        width: 280,
        model: model?.modelDeploymentName,
        tools,
        agentTools
      },
      position: position,
    };
  
    this.workflowGraphService.addNode(newNode);
  
    if (this.getControl('enableManagerLLM').value) return;
    const nodes = this.workflowGraphService.getAllNodes();
    const lastNode = nodes[nodes.length - 2]; // Ensure you are accessing the correct index
    if (lastNode) {
      this.workflowGraphService.addEdge({
        id: `${lastNode.id}-${newNode.id}`,
        source: lastNode.id,
        target: newNode.id,
      });
    }
  }
  
  // Canvas board event handlers
  onCanvasDropped(event: { event: DragEvent; position: { x: number; y: number } }): void {
    const dragEvent = event.event;
    const position = event.position;
  
    if (dragEvent.dataTransfer) {
      const agentData = dragEvent.dataTransfer.getData('application/reactflow');
      if (agentData) {
        try {
          const agent = JSON.parse(agentData);
          this.createNewNode(agent, position);
  
          // Remove the agent from the available agents list
          this.availableAgents = this.availableAgents.filter(a => a.id !== agent.id);
          this.filteredAgents = this.filteredAgents.filter(a => a.id !== agent.id);
        } catch (error) {
          console.error('Error adding node:', error);
        }
      }
    }
  }
    

  onConnectionCreated(edge: CanvasEdge): void {
    const newEdge: WorkflowEdge = {
      id: edge.id,
      source: edge.source,
      target: edge.target,
      animated: edge.animated || true,
    };

    this.workflowGraphService.addEdge(newEdge);
  }

  filterAgents(filterValue: string): void {
    // First filter by search term
    if (!filterValue || filterValue.trim() === '') {
      this.filteredAgents = [...this.agents];
    } else {
      filterValue = filterValue.toLowerCase().trim();
      this.filteredAgents = this.agents.filter(
        (agent) =>
          agent.name.toLowerCase().includes(filterValue) ||
          agent.description.toLowerCase().includes(filterValue) ||
          (agent.type && agent.type.toLowerCase().includes(filterValue)) ||
          (agent.capabilities &&
            agent.capabilities.some((cap) =>
              cap.toLowerCase().includes(filterValue),
            )),
      );
    }

    // Then filter out agents that are already used
    this.updateAvailableAgents();
  }

  updateAvailableAgents(): void {
    this.availableAgents = this.agents.filter(
      (agent) => !this.usedAgentIds.has(agent.id),
    );
  }

  onDragStart(event: DragEvent, agent: Agent): void {
    if (event.dataTransfer) {
      event.dataTransfer.setData(
        'application/reactflow',
        JSON.stringify(agent),
      );
      event.dataTransfer.effectAllowed = 'move';
    }
  }

  onDeleteNode(nodeId: string): void {
    const node = this.nodes.find(node => node.id === nodeId);
    if (node?.data?.agentId) {
      const agentId = node.data.agentId;
      const agent = this.agents.find(a => a.id === agentId);
      if (agent) {
        this.availableAgents.push(agent);
        this.filteredAgents.push(agent);
      }
    }
    this.workflowGraphService.removeNode(nodeId);
    if (this.selectedNodeId === nodeId) {
      this.selectedNodeId = null;
    }
    this.nodeToolValues.delete(nodeId);
  }

  onNodeSelected(nodeId: string): void {
    this.selectedNodeId = nodeId;
  }

  onNodeMoved(data: { nodeId: string; position: NodePosition }): void {
    // Find the node index
    const nodeIndex = this.nodes.findIndex((node) => node.id === data.nodeId);
    if (nodeIndex === -1) return;

    // Create a new array with the updated node
    const updatedNodes = [...this.nodes];
    updatedNodes[nodeIndex] = {
      ...this.nodes[nodeIndex],
      position: data.position,
    };

    // Update the node positions through service
    this.workflowGraphService.updateNodePositions(updatedNodes);

    // Force a change detection cycle
    this.cdr.detectChanges();
  }

  onStartConnection(data: {
    nodeId: string;
    handleType: 'source' | 'target';
    event: MouseEvent;
  }): void {
    // Canvas board handles connection logic
    // This method is called when a connection starts but the canvas board manages the temp connection
  }

  updateNodePosition(data: {
    nodeId: string;
    position: { x: number; y: number };
  }): void {
    // Canvas board handles connection point updates
  }
  
  builtInToolValuesChanged(event: { nodeId: string, toolValues: any[] }): void {
    if (!event || !event.nodeId || !event.toolValues) {
      console.warn('Invalid tool values event:', event);
      return;
    }

    // console.log('Tool values changed for node:', event.nodeId, event.toolValues);

    this.nodeToolValues.set(event.nodeId, event.toolValues);

    const allToolValues = Array.from(this.nodeToolValues.values())
      .filter(values => values != null)
      .flat()
      .filter(tool => tool != null);

    this.builtInToolValues = allToolValues;

    // console.log('All tool values updated:', this.builtInToolValues);
  }


  onSave(): void {
    const workflowData = {
      ...this.workflowForm.value,
      nodes: this.nodes,
      edges: this.edges,
    };

    console.log('Saving workflow:', workflowData);

    if (this.isEditMode) {
      console.log('Updating existing workflow');
      // this.workflowService.updateWorkflow(this.workflowId, workflowData);
    } else {
      console.log('Creating new workflow');
      // this.workflowService.createWorkflow(workflowData);
    }

    this.router.navigate(['/build/workflows']);
  }

  onExit(): void {
    this.router.navigate(['/build/workflows']);
  }

  onReset(): void {
    this.workflowGraphService.clearWorkflow();
    this.selectedNodeId = null;
  }

  onUndo(): void {
    console.log('Undo triggered from workflow editor');
  }

  onRedo(): void {
    console.log('Redo triggered from workflow editor');
  }

  onCanvasStateChanged(state: {
    nodes: CanvasNode[];
    edges: CanvasEdge[];
  }): void {
    console.log('Canvas state changed:', state);

    // Convert canvas nodes back to workflow nodes
    const workflowNodes: WorkflowNode[] = state.nodes.map((node) => ({
      id: node.id,
      type: node.type,
      data: node.data,
      position: node.position,
    }));

    // Convert canvas edges back to workflow edges
    const workflowEdges: WorkflowEdge[] = state.edges.map((edge) => ({
      id: edge.id,
      source: edge.source,
      target: edge.target,
      animated: edge.animated,
    }));

    // Update the workflow service to sync the state
    this.workflowGraphService.setNodes(workflowNodes);
    this.workflowGraphService.setEdges(workflowEdges);
  }

  makeWorkFlowPayload() {
    const formValue = this.workflowForm.getRawValue();
    const payload: Record<string, any> = {
      name: formValue?.name || null,
      description: formValue?.description || null,
      workflowAgents: [],
      workflowConfigs: {
        levelId: 199, 
        modelRef: [],
        workflowAgentTools: [],
        managerLlm: [],
        topP: formValue?.topP || 0.95,
        maxToken: formValue?.maxToken || 1500,
        temperature: formValue?.temperature || 0.3,
        enableAgenticMemory: false,
        embeddingModelRef: [],
      },
      createdBy: this.isEditMode ? formValue?.createdBy : this.tokenStorage.getDaUsername(),
      modifiedBy: this.tokenStorage.getDaUsername()
    };

    const nodes = this.workflowGraphService.getAllNodes();

    if (nodes.length) {
      payload['workflowAgents'] = nodes.map((node, index) => ({
        serial: index + 1,
        agentId: node.data.agentId,
      }));
    }

    if (this.builtInToolValues.length > 0) {
      const toolEntries : any[] = [];
      this.builtInToolValues.forEach((toolGroup) => {
        toolGroup.parameters.forEach((param : any) => {
          toolEntries.push({
            agentId: param.agentId,
            toolId: toolGroup.toolId,
            toolParameterId: param.agentToolId || param.toolParameterId, 
            parameterName: param.parameterName,
            value: param.value
          });
        });
      });

      payload['workflowConfigs'].workflowAgentTools = toolEntries;
    }

    if (formValue?.modelDeploymentName) {
      const managerModel = {
        id: formValue.modelRefId, 
        modelDeploymentName: formValue.modelDeploymentName,
        topP: formValue.topP,
        maxToken: formValue.maxToken,
        temperature: formValue.temperature
      };

      payload['workflowConfigs'].managerLlm = [managerModel];
    }

    if(formValue?.embeddingModelRefs?.length){
      payload['workflowConfigs'].embeddingModelRef = [...formValue.embeddingModelRefs];
      payload['workflowConfigs'].modelRef = [...formValue.embeddingModelRefs];
    }

    if (this.workflowId && !this.isModeDuplicate()) {
      payload['id'] = this.workflowId;
    }

    return payload;
  }


  onExecute(): void {
    // Navigate to workflow execution page
    if (!this.workflowForm.get('name')?.value) {
      this.dialogService.warning({
        title: 'Warning',
        message: 'Please provide the workflow name before executing the workflow.',
        showProceedButton: true,
        proceedButtonText: 'Ok'      
      });
      return;
    }
    const payload = this.makeWorkFlowPayload();
    if (this.workflowId && !this.isDuplicateMode) {
      this.workflowService.updateWorkFlow(payload).subscribe(() => {
        this.router.navigate(['/build/workflows/execute', this.workflowId]);
      });
    } else {
      // For new workflows, save first then navigate
      this.workflowService.saveWorkFlow(payload).subscribe((response: any) => {
        this.router.navigate(['/build/workflows/execute', response.workflowId]);
      });
      // alert('Please save the workflow before executing it.');
    }
  }

  // Helper method to get form controls easily from the template
  getControl(name: string): FormControl {
    return this.workflowForm.get(name) as FormControl;
  }

  /**
   * Check if an agent is already used in the workflow
   * @param agentId ID of the agent to check
   */
  isAgentUsed(agentId: string): boolean {
    return this.usedAgentIds.has(agentId);
  }

  /**
   * Update the set of used agent IDs
   */
  updateUsedAgentIds(): void {
    this.usedAgentIds.clear();
    this.nodes.forEach((node) => {
      if (node.data && node.data.agentId) {
        this.usedAgentIds.add(node.data.agentId);
      }
    });

    // Update available agents whenever used agents change
    this.updateAvailableAgents();
  }
  savedEdges: WorkflowEdge[] = [];
  onToggleChnage(event: boolean) {
    console.log(event);
    this.getControl('enableManagerLLM').setValue(event);
    this.showLlm = event;
    if (event) {
      this.savedEdges = this.workflowGraphService.getAllEdges();
      this.workflowGraphService.setEdges([]);
    } else {
      const nodes = this.workflowGraphService.getAllNodes();
      const edges = this.convertNodesToEdges(nodes);
      this.workflowGraphService.setEdges(edges);
      if (!this.savedWorkFlowDetils['managerLlm']) {
        this.getControl('modelDeploymentName').setValue(null);
      }
    }
  }
  toggleLlm(show: boolean) {
    this.showLlm = show;
  }

  getToolPatchValueForAgent(agentId: number): { toolId: number, parameterName: string, value: any } | null {
    const patchTool = this.workflowData?.workflowConfigs?.workflowAgentTools;

    if (patchTool?.agentId === agentId) {
      return {
        toolId: patchTool.toolId,
        parameterName: patchTool.parameterName,
        value: patchTool.value
      };
    }
    return null;
  }

}
