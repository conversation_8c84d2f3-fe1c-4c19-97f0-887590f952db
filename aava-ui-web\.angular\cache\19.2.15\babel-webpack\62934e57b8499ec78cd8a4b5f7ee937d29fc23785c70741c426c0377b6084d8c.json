{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport class DebugNameData {\n  constructor(owner, debugNameSource, referenceFn) {\n    this.owner = owner;\n    this.debugNameSource = debugNameSource;\n    this.referenceFn = referenceFn;\n  }\n  getDebugName(target) {\n    return getDebugName(target, this);\n  }\n}\nconst countPerName = new Map();\nconst cachedDebugName = new WeakMap();\nexport function getDebugName(target, data) {\n  const cached = cachedDebugName.get(target);\n  if (cached) {\n    return cached;\n  }\n  const dbgName = computeDebugName(target, data);\n  if (dbgName) {\n    let count = countPerName.get(dbgName) ?? 0;\n    count++;\n    countPerName.set(dbgName, count);\n    const result = count === 1 ? dbgName : `${dbgName}#${count}`;\n    cachedDebugName.set(target, result);\n    return result;\n  }\n  return undefined;\n}\nfunction computeDebugName(self, data) {\n  const cached = cachedDebugName.get(self);\n  if (cached) {\n    return cached;\n  }\n  const ownerStr = data.owner ? formatOwner(data.owner) + `.` : '';\n  let result;\n  const debugNameSource = data.debugNameSource;\n  if (debugNameSource !== undefined) {\n    if (typeof debugNameSource === 'function') {\n      result = debugNameSource();\n      if (result !== undefined) {\n        return ownerStr + result;\n      }\n    } else {\n      return ownerStr + debugNameSource;\n    }\n  }\n  const referenceFn = data.referenceFn;\n  if (referenceFn !== undefined) {\n    result = getFunctionName(referenceFn);\n    if (result !== undefined) {\n      return ownerStr + result;\n    }\n  }\n  if (data.owner !== undefined) {\n    const key = findKey(data.owner, self);\n    if (key !== undefined) {\n      return ownerStr + key;\n    }\n  }\n  return undefined;\n}\nfunction findKey(obj, value) {\n  for (const key in obj) {\n    if (obj[key] === value) {\n      return key;\n    }\n  }\n  return undefined;\n}\nconst countPerClassName = new Map();\nconst ownerId = new WeakMap();\nfunction formatOwner(owner) {\n  const id = ownerId.get(owner);\n  if (id) {\n    return id;\n  }\n  const className = getClassName(owner);\n  let count = countPerClassName.get(className) ?? 0;\n  count++;\n  countPerClassName.set(className, count);\n  const result = count === 1 ? className : `${className}#${count}`;\n  ownerId.set(owner, result);\n  return result;\n}\nfunction getClassName(obj) {\n  const ctor = obj.constructor;\n  if (ctor) {\n    return ctor.name;\n  }\n  return 'Object';\n}\nexport function getFunctionName(fn) {\n  const fnSrc = fn.toString();\n  // Pattern: /** @description ... */\n  const regexp = /\\/\\*\\*\\s*@description\\s*([^*]*)\\*\\//;\n  const match = regexp.exec(fnSrc);\n  const result = match ? match[1] : undefined;\n  return result?.trim();\n}", "map": {"version": 3, "names": ["DebugNameData", "constructor", "owner", "debugNameSource", "referenceFn", "getDebugName", "target", "count<PERSON><PERSON><PERSON><PERSON>", "Map", "cachedDebugName", "WeakMap", "data", "cached", "get", "dbgName", "computeDebugName", "count", "set", "result", "undefined", "self", "ownerStr", "formatOwner", "getFunctionName", "key", "<PERSON><PERSON><PERSON>", "obj", "value", "countPerClassName", "ownerId", "id", "className", "getClassName", "ctor", "name", "fn", "fnSrc", "toString", "regexp", "match", "exec", "trim"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/common/observableInternal/debugName.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport class DebugNameData {\n    constructor(owner, debugNameSource, referenceFn) {\n        this.owner = owner;\n        this.debugNameSource = debugNameSource;\n        this.referenceFn = referenceFn;\n    }\n    getDebugName(target) {\n        return getDebugName(target, this);\n    }\n}\nconst countPerName = new Map();\nconst cachedDebugName = new WeakMap();\nexport function getDebugName(target, data) {\n    const cached = cachedDebugName.get(target);\n    if (cached) {\n        return cached;\n    }\n    const dbgName = computeDebugName(target, data);\n    if (dbgName) {\n        let count = countPerName.get(dbgName) ?? 0;\n        count++;\n        countPerName.set(dbgName, count);\n        const result = count === 1 ? dbgName : `${dbgName}#${count}`;\n        cachedDebugName.set(target, result);\n        return result;\n    }\n    return undefined;\n}\nfunction computeDebugName(self, data) {\n    const cached = cachedDebugName.get(self);\n    if (cached) {\n        return cached;\n    }\n    const ownerStr = data.owner ? formatOwner(data.owner) + `.` : '';\n    let result;\n    const debugNameSource = data.debugNameSource;\n    if (debugNameSource !== undefined) {\n        if (typeof debugNameSource === 'function') {\n            result = debugNameSource();\n            if (result !== undefined) {\n                return ownerStr + result;\n            }\n        }\n        else {\n            return ownerStr + debugNameSource;\n        }\n    }\n    const referenceFn = data.referenceFn;\n    if (referenceFn !== undefined) {\n        result = getFunctionName(referenceFn);\n        if (result !== undefined) {\n            return ownerStr + result;\n        }\n    }\n    if (data.owner !== undefined) {\n        const key = findKey(data.owner, self);\n        if (key !== undefined) {\n            return ownerStr + key;\n        }\n    }\n    return undefined;\n}\nfunction findKey(obj, value) {\n    for (const key in obj) {\n        if (obj[key] === value) {\n            return key;\n        }\n    }\n    return undefined;\n}\nconst countPerClassName = new Map();\nconst ownerId = new WeakMap();\nfunction formatOwner(owner) {\n    const id = ownerId.get(owner);\n    if (id) {\n        return id;\n    }\n    const className = getClassName(owner);\n    let count = countPerClassName.get(className) ?? 0;\n    count++;\n    countPerClassName.set(className, count);\n    const result = count === 1 ? className : `${className}#${count}`;\n    ownerId.set(owner, result);\n    return result;\n}\nfunction getClassName(obj) {\n    const ctor = obj.constructor;\n    if (ctor) {\n        return ctor.name;\n    }\n    return 'Object';\n}\nexport function getFunctionName(fn) {\n    const fnSrc = fn.toString();\n    // Pattern: /** @description ... */\n    const regexp = /\\/\\*\\*\\s*@description\\s*([^*]*)\\*\\//;\n    const match = regexp.exec(fnSrc);\n    const result = match ? match[1] : undefined;\n    return result?.trim();\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,MAAMA,aAAa,CAAC;EACvBC,WAAWA,CAACC,KAAK,EAAEC,eAAe,EAAEC,WAAW,EAAE;IAC7C,IAAI,CAACF,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,WAAW,GAAGA,WAAW;EAClC;EACAC,YAAYA,CAACC,MAAM,EAAE;IACjB,OAAOD,YAAY,CAACC,MAAM,EAAE,IAAI,CAAC;EACrC;AACJ;AACA,MAAMC,YAAY,GAAG,IAAIC,GAAG,CAAC,CAAC;AAC9B,MAAMC,eAAe,GAAG,IAAIC,OAAO,CAAC,CAAC;AACrC,OAAO,SAASL,YAAYA,CAACC,MAAM,EAAEK,IAAI,EAAE;EACvC,MAAMC,MAAM,GAAGH,eAAe,CAACI,GAAG,CAACP,MAAM,CAAC;EAC1C,IAAIM,MAAM,EAAE;IACR,OAAOA,MAAM;EACjB;EACA,MAAME,OAAO,GAAGC,gBAAgB,CAACT,MAAM,EAAEK,IAAI,CAAC;EAC9C,IAAIG,OAAO,EAAE;IACT,IAAIE,KAAK,GAAGT,YAAY,CAACM,GAAG,CAACC,OAAO,CAAC,IAAI,CAAC;IAC1CE,KAAK,EAAE;IACPT,YAAY,CAACU,GAAG,CAACH,OAAO,EAAEE,KAAK,CAAC;IAChC,MAAME,MAAM,GAAGF,KAAK,KAAK,CAAC,GAAGF,OAAO,GAAG,GAAGA,OAAO,IAAIE,KAAK,EAAE;IAC5DP,eAAe,CAACQ,GAAG,CAACX,MAAM,EAAEY,MAAM,CAAC;IACnC,OAAOA,MAAM;EACjB;EACA,OAAOC,SAAS;AACpB;AACA,SAASJ,gBAAgBA,CAACK,IAAI,EAAET,IAAI,EAAE;EAClC,MAAMC,MAAM,GAAGH,eAAe,CAACI,GAAG,CAACO,IAAI,CAAC;EACxC,IAAIR,MAAM,EAAE;IACR,OAAOA,MAAM;EACjB;EACA,MAAMS,QAAQ,GAAGV,IAAI,CAACT,KAAK,GAAGoB,WAAW,CAACX,IAAI,CAACT,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE;EAChE,IAAIgB,MAAM;EACV,MAAMf,eAAe,GAAGQ,IAAI,CAACR,eAAe;EAC5C,IAAIA,eAAe,KAAKgB,SAAS,EAAE;IAC/B,IAAI,OAAOhB,eAAe,KAAK,UAAU,EAAE;MACvCe,MAAM,GAAGf,eAAe,CAAC,CAAC;MAC1B,IAAIe,MAAM,KAAKC,SAAS,EAAE;QACtB,OAAOE,QAAQ,GAAGH,MAAM;MAC5B;IACJ,CAAC,MACI;MACD,OAAOG,QAAQ,GAAGlB,eAAe;IACrC;EACJ;EACA,MAAMC,WAAW,GAAGO,IAAI,CAACP,WAAW;EACpC,IAAIA,WAAW,KAAKe,SAAS,EAAE;IAC3BD,MAAM,GAAGK,eAAe,CAACnB,WAAW,CAAC;IACrC,IAAIc,MAAM,KAAKC,SAAS,EAAE;MACtB,OAAOE,QAAQ,GAAGH,MAAM;IAC5B;EACJ;EACA,IAAIP,IAAI,CAACT,KAAK,KAAKiB,SAAS,EAAE;IAC1B,MAAMK,GAAG,GAAGC,OAAO,CAACd,IAAI,CAACT,KAAK,EAAEkB,IAAI,CAAC;IACrC,IAAII,GAAG,KAAKL,SAAS,EAAE;MACnB,OAAOE,QAAQ,GAAGG,GAAG;IACzB;EACJ;EACA,OAAOL,SAAS;AACpB;AACA,SAASM,OAAOA,CAACC,GAAG,EAAEC,KAAK,EAAE;EACzB,KAAK,MAAMH,GAAG,IAAIE,GAAG,EAAE;IACnB,IAAIA,GAAG,CAACF,GAAG,CAAC,KAAKG,KAAK,EAAE;MACpB,OAAOH,GAAG;IACd;EACJ;EACA,OAAOL,SAAS;AACpB;AACA,MAAMS,iBAAiB,GAAG,IAAIpB,GAAG,CAAC,CAAC;AACnC,MAAMqB,OAAO,GAAG,IAAInB,OAAO,CAAC,CAAC;AAC7B,SAASY,WAAWA,CAACpB,KAAK,EAAE;EACxB,MAAM4B,EAAE,GAAGD,OAAO,CAAChB,GAAG,CAACX,KAAK,CAAC;EAC7B,IAAI4B,EAAE,EAAE;IACJ,OAAOA,EAAE;EACb;EACA,MAAMC,SAAS,GAAGC,YAAY,CAAC9B,KAAK,CAAC;EACrC,IAAIc,KAAK,GAAGY,iBAAiB,CAACf,GAAG,CAACkB,SAAS,CAAC,IAAI,CAAC;EACjDf,KAAK,EAAE;EACPY,iBAAiB,CAACX,GAAG,CAACc,SAAS,EAAEf,KAAK,CAAC;EACvC,MAAME,MAAM,GAAGF,KAAK,KAAK,CAAC,GAAGe,SAAS,GAAG,GAAGA,SAAS,IAAIf,KAAK,EAAE;EAChEa,OAAO,CAACZ,GAAG,CAACf,KAAK,EAAEgB,MAAM,CAAC;EAC1B,OAAOA,MAAM;AACjB;AACA,SAASc,YAAYA,CAACN,GAAG,EAAE;EACvB,MAAMO,IAAI,GAAGP,GAAG,CAACzB,WAAW;EAC5B,IAAIgC,IAAI,EAAE;IACN,OAAOA,IAAI,CAACC,IAAI;EACpB;EACA,OAAO,QAAQ;AACnB;AACA,OAAO,SAASX,eAAeA,CAACY,EAAE,EAAE;EAChC,MAAMC,KAAK,GAAGD,EAAE,CAACE,QAAQ,CAAC,CAAC;EAC3B;EACA,MAAMC,MAAM,GAAG,qCAAqC;EACpD,MAAMC,KAAK,GAAGD,MAAM,CAACE,IAAI,CAACJ,KAAK,CAAC;EAChC,MAAMlB,MAAM,GAAGqB,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGpB,SAAS;EAC3C,OAAOD,MAAM,EAAEuB,IAAI,CAAC,CAAC;AACzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}