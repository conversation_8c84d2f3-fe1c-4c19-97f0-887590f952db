{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { CharacterClassifier } from '../core/characterClassifier.js';\nclass Uint8Matrix {\n  constructor(rows, cols, defaultValue) {\n    const data = new Uint8Array(rows * cols);\n    for (let i = 0, len = rows * cols; i < len; i++) {\n      data[i] = defaultValue;\n    }\n    this._data = data;\n    this.rows = rows;\n    this.cols = cols;\n  }\n  get(row, col) {\n    return this._data[row * this.cols + col];\n  }\n  set(row, col, value) {\n    this._data[row * this.cols + col] = value;\n  }\n}\nexport class StateMachine {\n  constructor(edges) {\n    let maxCharCode = 0;\n    let maxState = 0 /* State.Invalid */;\n    for (let i = 0, len = edges.length; i < len; i++) {\n      const [from, chCode, to] = edges[i];\n      if (chCode > maxCharCode) {\n        maxCharCode = chCode;\n      }\n      if (from > maxState) {\n        maxState = from;\n      }\n      if (to > maxState) {\n        maxState = to;\n      }\n    }\n    maxCharCode++;\n    maxState++;\n    const states = new Uint8Matrix(maxState, maxCharCode, 0 /* State.Invalid */);\n    for (let i = 0, len = edges.length; i < len; i++) {\n      const [from, chCode, to] = edges[i];\n      states.set(from, chCode, to);\n    }\n    this._states = states;\n    this._maxCharCode = maxCharCode;\n  }\n  nextState(currentState, chCode) {\n    if (chCode < 0 || chCode >= this._maxCharCode) {\n      return 0 /* State.Invalid */;\n    }\n    return this._states.get(currentState, chCode);\n  }\n}\n// State machine for http:// or https:// or file://\nlet _stateMachine = null;\nfunction getStateMachine() {\n  if (_stateMachine === null) {\n    _stateMachine = new StateMachine([[1 /* State.Start */, 104 /* CharCode.h */, 2 /* State.H */], [1 /* State.Start */, 72 /* CharCode.H */, 2 /* State.H */], [1 /* State.Start */, 102 /* CharCode.f */, 6 /* State.F */], [1 /* State.Start */, 70 /* CharCode.F */, 6 /* State.F */], [2 /* State.H */, 116 /* CharCode.t */, 3 /* State.HT */], [2 /* State.H */, 84 /* CharCode.T */, 3 /* State.HT */], [3 /* State.HT */, 116 /* CharCode.t */, 4 /* State.HTT */], [3 /* State.HT */, 84 /* CharCode.T */, 4 /* State.HTT */], [4 /* State.HTT */, 112 /* CharCode.p */, 5 /* State.HTTP */], [4 /* State.HTT */, 80 /* CharCode.P */, 5 /* State.HTTP */], [5 /* State.HTTP */, 115 /* CharCode.s */, 9 /* State.BeforeColon */], [5 /* State.HTTP */, 83 /* CharCode.S */, 9 /* State.BeforeColon */], [5 /* State.HTTP */, 58 /* CharCode.Colon */, 10 /* State.AfterColon */], [6 /* State.F */, 105 /* CharCode.i */, 7 /* State.FI */], [6 /* State.F */, 73 /* CharCode.I */, 7 /* State.FI */], [7 /* State.FI */, 108 /* CharCode.l */, 8 /* State.FIL */], [7 /* State.FI */, 76 /* CharCode.L */, 8 /* State.FIL */], [8 /* State.FIL */, 101 /* CharCode.e */, 9 /* State.BeforeColon */], [8 /* State.FIL */, 69 /* CharCode.E */, 9 /* State.BeforeColon */], [9 /* State.BeforeColon */, 58 /* CharCode.Colon */, 10 /* State.AfterColon */], [10 /* State.AfterColon */, 47 /* CharCode.Slash */, 11 /* State.AlmostThere */], [11 /* State.AlmostThere */, 47 /* CharCode.Slash */, 12 /* State.End */]]);\n  }\n  return _stateMachine;\n}\nlet _classifier = null;\nfunction getClassifier() {\n  if (_classifier === null) {\n    _classifier = new CharacterClassifier(0 /* CharacterClass.None */);\n    // allow-any-unicode-next-line\n    const FORCE_TERMINATION_CHARACTERS = ' \\t<>\\'\\\"、。｡､，．：；‘〈「『〔（［｛｢｣｝］）〕』」〉’｀～…';\n    for (let i = 0; i < FORCE_TERMINATION_CHARACTERS.length; i++) {\n      _classifier.set(FORCE_TERMINATION_CHARACTERS.charCodeAt(i), 1 /* CharacterClass.ForceTermination */);\n    }\n    const CANNOT_END_WITH_CHARACTERS = '.,;:';\n    for (let i = 0; i < CANNOT_END_WITH_CHARACTERS.length; i++) {\n      _classifier.set(CANNOT_END_WITH_CHARACTERS.charCodeAt(i), 2 /* CharacterClass.CannotEndIn */);\n    }\n  }\n  return _classifier;\n}\nexport class LinkComputer {\n  static _createLink(classifier, line, lineNumber, linkBeginIndex, linkEndIndex) {\n    // Do not allow to end link in certain characters...\n    let lastIncludedCharIndex = linkEndIndex - 1;\n    do {\n      const chCode = line.charCodeAt(lastIncludedCharIndex);\n      const chClass = classifier.get(chCode);\n      if (chClass !== 2 /* CharacterClass.CannotEndIn */) {\n        break;\n      }\n      lastIncludedCharIndex--;\n    } while (lastIncludedCharIndex > linkBeginIndex);\n    // Handle links enclosed in parens, square brackets and curlys.\n    if (linkBeginIndex > 0) {\n      const charCodeBeforeLink = line.charCodeAt(linkBeginIndex - 1);\n      const lastCharCodeInLink = line.charCodeAt(lastIncludedCharIndex);\n      if (charCodeBeforeLink === 40 /* CharCode.OpenParen */ && lastCharCodeInLink === 41 /* CharCode.CloseParen */ || charCodeBeforeLink === 91 /* CharCode.OpenSquareBracket */ && lastCharCodeInLink === 93 /* CharCode.CloseSquareBracket */ || charCodeBeforeLink === 123 /* CharCode.OpenCurlyBrace */ && lastCharCodeInLink === 125 /* CharCode.CloseCurlyBrace */) {\n        // Do not end in ) if ( is before the link start\n        // Do not end in ] if [ is before the link start\n        // Do not end in } if { is before the link start\n        lastIncludedCharIndex--;\n      }\n    }\n    return {\n      range: {\n        startLineNumber: lineNumber,\n        startColumn: linkBeginIndex + 1,\n        endLineNumber: lineNumber,\n        endColumn: lastIncludedCharIndex + 2\n      },\n      url: line.substring(linkBeginIndex, lastIncludedCharIndex + 1)\n    };\n  }\n  static computeLinks(model, stateMachine = getStateMachine()) {\n    const classifier = getClassifier();\n    const result = [];\n    for (let i = 1, lineCount = model.getLineCount(); i <= lineCount; i++) {\n      const line = model.getLineContent(i);\n      const len = line.length;\n      let j = 0;\n      let linkBeginIndex = 0;\n      let linkBeginChCode = 0;\n      let state = 1 /* State.Start */;\n      let hasOpenParens = false;\n      let hasOpenSquareBracket = false;\n      let inSquareBrackets = false;\n      let hasOpenCurlyBracket = false;\n      while (j < len) {\n        let resetStateMachine = false;\n        const chCode = line.charCodeAt(j);\n        if (state === 13 /* State.Accept */) {\n          let chClass;\n          switch (chCode) {\n            case 40 /* CharCode.OpenParen */:\n              hasOpenParens = true;\n              chClass = 0 /* CharacterClass.None */;\n              break;\n            case 41 /* CharCode.CloseParen */:\n              chClass = hasOpenParens ? 0 /* CharacterClass.None */ : 1 /* CharacterClass.ForceTermination */;\n              break;\n            case 91 /* CharCode.OpenSquareBracket */:\n              inSquareBrackets = true;\n              hasOpenSquareBracket = true;\n              chClass = 0 /* CharacterClass.None */;\n              break;\n            case 93 /* CharCode.CloseSquareBracket */:\n              inSquareBrackets = false;\n              chClass = hasOpenSquareBracket ? 0 /* CharacterClass.None */ : 1 /* CharacterClass.ForceTermination */;\n              break;\n            case 123 /* CharCode.OpenCurlyBrace */:\n              hasOpenCurlyBracket = true;\n              chClass = 0 /* CharacterClass.None */;\n              break;\n            case 125 /* CharCode.CloseCurlyBrace */:\n              chClass = hasOpenCurlyBracket ? 0 /* CharacterClass.None */ : 1 /* CharacterClass.ForceTermination */;\n              break;\n            // The following three rules make it that ' or \" or ` are allowed inside links\n            // only if the link is wrapped by some other quote character\n            case 39 /* CharCode.SingleQuote */:\n            case 34 /* CharCode.DoubleQuote */:\n            case 96 /* CharCode.BackTick */:\n              if (linkBeginChCode === chCode) {\n                chClass = 1 /* CharacterClass.ForceTermination */;\n              } else if (linkBeginChCode === 39 /* CharCode.SingleQuote */ || linkBeginChCode === 34 /* CharCode.DoubleQuote */ || linkBeginChCode === 96 /* CharCode.BackTick */) {\n                chClass = 0 /* CharacterClass.None */;\n              } else {\n                chClass = 1 /* CharacterClass.ForceTermination */;\n              }\n              break;\n            case 42 /* CharCode.Asterisk */:\n              // `*` terminates a link if the link began with `*`\n              chClass = linkBeginChCode === 42 /* CharCode.Asterisk */ ? 1 /* CharacterClass.ForceTermination */ : 0 /* CharacterClass.None */;\n              break;\n            case 124 /* CharCode.Pipe */:\n              // `|` terminates a link if the link began with `|`\n              chClass = linkBeginChCode === 124 /* CharCode.Pipe */ ? 1 /* CharacterClass.ForceTermination */ : 0 /* CharacterClass.None */;\n              break;\n            case 32 /* CharCode.Space */:\n              // ` ` allow space in between [ and ]\n              chClass = inSquareBrackets ? 0 /* CharacterClass.None */ : 1 /* CharacterClass.ForceTermination */;\n              break;\n            default:\n              chClass = classifier.get(chCode);\n          }\n          // Check if character terminates link\n          if (chClass === 1 /* CharacterClass.ForceTermination */) {\n            result.push(LinkComputer._createLink(classifier, line, i, linkBeginIndex, j));\n            resetStateMachine = true;\n          }\n        } else if (state === 12 /* State.End */) {\n          let chClass;\n          if (chCode === 91 /* CharCode.OpenSquareBracket */) {\n            // Allow for the authority part to contain ipv6 addresses which contain [ and ]\n            hasOpenSquareBracket = true;\n            chClass = 0 /* CharacterClass.None */;\n          } else {\n            chClass = classifier.get(chCode);\n          }\n          // Check if character terminates link\n          if (chClass === 1 /* CharacterClass.ForceTermination */) {\n            resetStateMachine = true;\n          } else {\n            state = 13 /* State.Accept */;\n          }\n        } else {\n          state = stateMachine.nextState(state, chCode);\n          if (state === 0 /* State.Invalid */) {\n            resetStateMachine = true;\n          }\n        }\n        if (resetStateMachine) {\n          state = 1 /* State.Start */;\n          hasOpenParens = false;\n          hasOpenSquareBracket = false;\n          hasOpenCurlyBracket = false;\n          // Record where the link started\n          linkBeginIndex = j + 1;\n          linkBeginChCode = chCode;\n        }\n        j++;\n      }\n      if (state === 13 /* State.Accept */) {\n        result.push(LinkComputer._createLink(classifier, line, i, linkBeginIndex, len));\n      }\n    }\n    return result;\n  }\n}\n/**\n * Returns an array of all links contains in the provided\n * document. *Note* that this operation is computational\n * expensive and should not run in the UI thread.\n */\nexport function computeLinks(model) {\n  if (!model || typeof model.getLineCount !== 'function' || typeof model.getLineContent !== 'function') {\n    // Unknown caller!\n    return [];\n  }\n  return LinkComputer.computeLinks(model);\n}", "map": {"version": 3, "names": ["CharacterClassifier", "Uint8Matrix", "constructor", "rows", "cols", "defaultValue", "data", "Uint8Array", "i", "len", "_data", "get", "row", "col", "set", "value", "StateMachine", "edges", "maxCharCode", "maxState", "length", "from", "chCode", "to", "states", "_states", "_maxCharCode", "nextState", "currentState", "_stateMachine", "getStateMachine", "_classifier", "getClassifier", "FORCE_TERMINATION_CHARACTERS", "charCodeAt", "CANNOT_END_WITH_CHARACTERS", "LinkComputer", "_createLink", "classifier", "line", "lineNumber", "linkBeginIndex", "linkEndIndex", "lastIncludedCharIndex", "chClass", "charCodeBeforeLink", "lastCharCodeInLink", "range", "startLineNumber", "startColumn", "endLineNumber", "endColumn", "url", "substring", "computeLinks", "model", "stateMachine", "result", "lineCount", "getLineCount", "get<PERSON>ineC<PERSON>nt", "j", "linkBeginChCode", "state", "hasOpenParens", "hasOpenSquareBracket", "inSquareBrackets", "hasOpenCurlyBracket", "resetStateMachine", "push"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/languages/linkComputer.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { CharacterClassifier } from '../core/characterClassifier.js';\nclass Uint8Matrix {\n    constructor(rows, cols, defaultValue) {\n        const data = new Uint8Array(rows * cols);\n        for (let i = 0, len = rows * cols; i < len; i++) {\n            data[i] = defaultValue;\n        }\n        this._data = data;\n        this.rows = rows;\n        this.cols = cols;\n    }\n    get(row, col) {\n        return this._data[row * this.cols + col];\n    }\n    set(row, col, value) {\n        this._data[row * this.cols + col] = value;\n    }\n}\nexport class StateMachine {\n    constructor(edges) {\n        let maxCharCode = 0;\n        let maxState = 0 /* State.Invalid */;\n        for (let i = 0, len = edges.length; i < len; i++) {\n            const [from, chCode, to] = edges[i];\n            if (chCode > maxCharCode) {\n                maxCharCode = chCode;\n            }\n            if (from > maxState) {\n                maxState = from;\n            }\n            if (to > maxState) {\n                maxState = to;\n            }\n        }\n        maxCharCode++;\n        maxState++;\n        const states = new Uint8Matrix(maxState, maxCharCode, 0 /* State.Invalid */);\n        for (let i = 0, len = edges.length; i < len; i++) {\n            const [from, chCode, to] = edges[i];\n            states.set(from, chCode, to);\n        }\n        this._states = states;\n        this._maxCharCode = maxCharCode;\n    }\n    nextState(currentState, chCode) {\n        if (chCode < 0 || chCode >= this._maxCharCode) {\n            return 0 /* State.Invalid */;\n        }\n        return this._states.get(currentState, chCode);\n    }\n}\n// State machine for http:// or https:// or file://\nlet _stateMachine = null;\nfunction getStateMachine() {\n    if (_stateMachine === null) {\n        _stateMachine = new StateMachine([\n            [1 /* State.Start */, 104 /* CharCode.h */, 2 /* State.H */],\n            [1 /* State.Start */, 72 /* CharCode.H */, 2 /* State.H */],\n            [1 /* State.Start */, 102 /* CharCode.f */, 6 /* State.F */],\n            [1 /* State.Start */, 70 /* CharCode.F */, 6 /* State.F */],\n            [2 /* State.H */, 116 /* CharCode.t */, 3 /* State.HT */],\n            [2 /* State.H */, 84 /* CharCode.T */, 3 /* State.HT */],\n            [3 /* State.HT */, 116 /* CharCode.t */, 4 /* State.HTT */],\n            [3 /* State.HT */, 84 /* CharCode.T */, 4 /* State.HTT */],\n            [4 /* State.HTT */, 112 /* CharCode.p */, 5 /* State.HTTP */],\n            [4 /* State.HTT */, 80 /* CharCode.P */, 5 /* State.HTTP */],\n            [5 /* State.HTTP */, 115 /* CharCode.s */, 9 /* State.BeforeColon */],\n            [5 /* State.HTTP */, 83 /* CharCode.S */, 9 /* State.BeforeColon */],\n            [5 /* State.HTTP */, 58 /* CharCode.Colon */, 10 /* State.AfterColon */],\n            [6 /* State.F */, 105 /* CharCode.i */, 7 /* State.FI */],\n            [6 /* State.F */, 73 /* CharCode.I */, 7 /* State.FI */],\n            [7 /* State.FI */, 108 /* CharCode.l */, 8 /* State.FIL */],\n            [7 /* State.FI */, 76 /* CharCode.L */, 8 /* State.FIL */],\n            [8 /* State.FIL */, 101 /* CharCode.e */, 9 /* State.BeforeColon */],\n            [8 /* State.FIL */, 69 /* CharCode.E */, 9 /* State.BeforeColon */],\n            [9 /* State.BeforeColon */, 58 /* CharCode.Colon */, 10 /* State.AfterColon */],\n            [10 /* State.AfterColon */, 47 /* CharCode.Slash */, 11 /* State.AlmostThere */],\n            [11 /* State.AlmostThere */, 47 /* CharCode.Slash */, 12 /* State.End */],\n        ]);\n    }\n    return _stateMachine;\n}\nlet _classifier = null;\nfunction getClassifier() {\n    if (_classifier === null) {\n        _classifier = new CharacterClassifier(0 /* CharacterClass.None */);\n        // allow-any-unicode-next-line\n        const FORCE_TERMINATION_CHARACTERS = ' \\t<>\\'\\\"、。｡､，．：；‘〈「『〔（［｛｢｣｝］）〕』」〉’｀～…';\n        for (let i = 0; i < FORCE_TERMINATION_CHARACTERS.length; i++) {\n            _classifier.set(FORCE_TERMINATION_CHARACTERS.charCodeAt(i), 1 /* CharacterClass.ForceTermination */);\n        }\n        const CANNOT_END_WITH_CHARACTERS = '.,;:';\n        for (let i = 0; i < CANNOT_END_WITH_CHARACTERS.length; i++) {\n            _classifier.set(CANNOT_END_WITH_CHARACTERS.charCodeAt(i), 2 /* CharacterClass.CannotEndIn */);\n        }\n    }\n    return _classifier;\n}\nexport class LinkComputer {\n    static _createLink(classifier, line, lineNumber, linkBeginIndex, linkEndIndex) {\n        // Do not allow to end link in certain characters...\n        let lastIncludedCharIndex = linkEndIndex - 1;\n        do {\n            const chCode = line.charCodeAt(lastIncludedCharIndex);\n            const chClass = classifier.get(chCode);\n            if (chClass !== 2 /* CharacterClass.CannotEndIn */) {\n                break;\n            }\n            lastIncludedCharIndex--;\n        } while (lastIncludedCharIndex > linkBeginIndex);\n        // Handle links enclosed in parens, square brackets and curlys.\n        if (linkBeginIndex > 0) {\n            const charCodeBeforeLink = line.charCodeAt(linkBeginIndex - 1);\n            const lastCharCodeInLink = line.charCodeAt(lastIncludedCharIndex);\n            if ((charCodeBeforeLink === 40 /* CharCode.OpenParen */ && lastCharCodeInLink === 41 /* CharCode.CloseParen */)\n                || (charCodeBeforeLink === 91 /* CharCode.OpenSquareBracket */ && lastCharCodeInLink === 93 /* CharCode.CloseSquareBracket */)\n                || (charCodeBeforeLink === 123 /* CharCode.OpenCurlyBrace */ && lastCharCodeInLink === 125 /* CharCode.CloseCurlyBrace */)) {\n                // Do not end in ) if ( is before the link start\n                // Do not end in ] if [ is before the link start\n                // Do not end in } if { is before the link start\n                lastIncludedCharIndex--;\n            }\n        }\n        return {\n            range: {\n                startLineNumber: lineNumber,\n                startColumn: linkBeginIndex + 1,\n                endLineNumber: lineNumber,\n                endColumn: lastIncludedCharIndex + 2\n            },\n            url: line.substring(linkBeginIndex, lastIncludedCharIndex + 1)\n        };\n    }\n    static computeLinks(model, stateMachine = getStateMachine()) {\n        const classifier = getClassifier();\n        const result = [];\n        for (let i = 1, lineCount = model.getLineCount(); i <= lineCount; i++) {\n            const line = model.getLineContent(i);\n            const len = line.length;\n            let j = 0;\n            let linkBeginIndex = 0;\n            let linkBeginChCode = 0;\n            let state = 1 /* State.Start */;\n            let hasOpenParens = false;\n            let hasOpenSquareBracket = false;\n            let inSquareBrackets = false;\n            let hasOpenCurlyBracket = false;\n            while (j < len) {\n                let resetStateMachine = false;\n                const chCode = line.charCodeAt(j);\n                if (state === 13 /* State.Accept */) {\n                    let chClass;\n                    switch (chCode) {\n                        case 40 /* CharCode.OpenParen */:\n                            hasOpenParens = true;\n                            chClass = 0 /* CharacterClass.None */;\n                            break;\n                        case 41 /* CharCode.CloseParen */:\n                            chClass = (hasOpenParens ? 0 /* CharacterClass.None */ : 1 /* CharacterClass.ForceTermination */);\n                            break;\n                        case 91 /* CharCode.OpenSquareBracket */:\n                            inSquareBrackets = true;\n                            hasOpenSquareBracket = true;\n                            chClass = 0 /* CharacterClass.None */;\n                            break;\n                        case 93 /* CharCode.CloseSquareBracket */:\n                            inSquareBrackets = false;\n                            chClass = (hasOpenSquareBracket ? 0 /* CharacterClass.None */ : 1 /* CharacterClass.ForceTermination */);\n                            break;\n                        case 123 /* CharCode.OpenCurlyBrace */:\n                            hasOpenCurlyBracket = true;\n                            chClass = 0 /* CharacterClass.None */;\n                            break;\n                        case 125 /* CharCode.CloseCurlyBrace */:\n                            chClass = (hasOpenCurlyBracket ? 0 /* CharacterClass.None */ : 1 /* CharacterClass.ForceTermination */);\n                            break;\n                        // The following three rules make it that ' or \" or ` are allowed inside links\n                        // only if the link is wrapped by some other quote character\n                        case 39 /* CharCode.SingleQuote */:\n                        case 34 /* CharCode.DoubleQuote */:\n                        case 96 /* CharCode.BackTick */:\n                            if (linkBeginChCode === chCode) {\n                                chClass = 1 /* CharacterClass.ForceTermination */;\n                            }\n                            else if (linkBeginChCode === 39 /* CharCode.SingleQuote */ || linkBeginChCode === 34 /* CharCode.DoubleQuote */ || linkBeginChCode === 96 /* CharCode.BackTick */) {\n                                chClass = 0 /* CharacterClass.None */;\n                            }\n                            else {\n                                chClass = 1 /* CharacterClass.ForceTermination */;\n                            }\n                            break;\n                        case 42 /* CharCode.Asterisk */:\n                            // `*` terminates a link if the link began with `*`\n                            chClass = (linkBeginChCode === 42 /* CharCode.Asterisk */) ? 1 /* CharacterClass.ForceTermination */ : 0 /* CharacterClass.None */;\n                            break;\n                        case 124 /* CharCode.Pipe */:\n                            // `|` terminates a link if the link began with `|`\n                            chClass = (linkBeginChCode === 124 /* CharCode.Pipe */) ? 1 /* CharacterClass.ForceTermination */ : 0 /* CharacterClass.None */;\n                            break;\n                        case 32 /* CharCode.Space */:\n                            // ` ` allow space in between [ and ]\n                            chClass = (inSquareBrackets ? 0 /* CharacterClass.None */ : 1 /* CharacterClass.ForceTermination */);\n                            break;\n                        default:\n                            chClass = classifier.get(chCode);\n                    }\n                    // Check if character terminates link\n                    if (chClass === 1 /* CharacterClass.ForceTermination */) {\n                        result.push(LinkComputer._createLink(classifier, line, i, linkBeginIndex, j));\n                        resetStateMachine = true;\n                    }\n                }\n                else if (state === 12 /* State.End */) {\n                    let chClass;\n                    if (chCode === 91 /* CharCode.OpenSquareBracket */) {\n                        // Allow for the authority part to contain ipv6 addresses which contain [ and ]\n                        hasOpenSquareBracket = true;\n                        chClass = 0 /* CharacterClass.None */;\n                    }\n                    else {\n                        chClass = classifier.get(chCode);\n                    }\n                    // Check if character terminates link\n                    if (chClass === 1 /* CharacterClass.ForceTermination */) {\n                        resetStateMachine = true;\n                    }\n                    else {\n                        state = 13 /* State.Accept */;\n                    }\n                }\n                else {\n                    state = stateMachine.nextState(state, chCode);\n                    if (state === 0 /* State.Invalid */) {\n                        resetStateMachine = true;\n                    }\n                }\n                if (resetStateMachine) {\n                    state = 1 /* State.Start */;\n                    hasOpenParens = false;\n                    hasOpenSquareBracket = false;\n                    hasOpenCurlyBracket = false;\n                    // Record where the link started\n                    linkBeginIndex = j + 1;\n                    linkBeginChCode = chCode;\n                }\n                j++;\n            }\n            if (state === 13 /* State.Accept */) {\n                result.push(LinkComputer._createLink(classifier, line, i, linkBeginIndex, len));\n            }\n        }\n        return result;\n    }\n}\n/**\n * Returns an array of all links contains in the provided\n * document. *Note* that this operation is computational\n * expensive and should not run in the UI thread.\n */\nexport function computeLinks(model) {\n    if (!model || typeof model.getLineCount !== 'function' || typeof model.getLineContent !== 'function') {\n        // Unknown caller!\n        return [];\n    }\n    return LinkComputer.computeLinks(model);\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,mBAAmB,QAAQ,gCAAgC;AACpE,MAAMC,WAAW,CAAC;EACdC,WAAWA,CAACC,IAAI,EAAEC,IAAI,EAAEC,YAAY,EAAE;IAClC,MAAMC,IAAI,GAAG,IAAIC,UAAU,CAACJ,IAAI,GAAGC,IAAI,CAAC;IACxC,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGN,IAAI,GAAGC,IAAI,EAAEI,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;MAC7CF,IAAI,CAACE,CAAC,CAAC,GAAGH,YAAY;IAC1B;IACA,IAAI,CAACK,KAAK,GAAGJ,IAAI;IACjB,IAAI,CAACH,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,IAAI,GAAGA,IAAI;EACpB;EACAO,GAAGA,CAACC,GAAG,EAAEC,GAAG,EAAE;IACV,OAAO,IAAI,CAACH,KAAK,CAACE,GAAG,GAAG,IAAI,CAACR,IAAI,GAAGS,GAAG,CAAC;EAC5C;EACAC,GAAGA,CAACF,GAAG,EAAEC,GAAG,EAAEE,KAAK,EAAE;IACjB,IAAI,CAACL,KAAK,CAACE,GAAG,GAAG,IAAI,CAACR,IAAI,GAAGS,GAAG,CAAC,GAAGE,KAAK;EAC7C;AACJ;AACA,OAAO,MAAMC,YAAY,CAAC;EACtBd,WAAWA,CAACe,KAAK,EAAE;IACf,IAAIC,WAAW,GAAG,CAAC;IACnB,IAAIC,QAAQ,GAAG,CAAC,CAAC;IACjB,KAAK,IAAIX,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGQ,KAAK,CAACG,MAAM,EAAEZ,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;MAC9C,MAAM,CAACa,IAAI,EAAEC,MAAM,EAAEC,EAAE,CAAC,GAAGN,KAAK,CAACT,CAAC,CAAC;MACnC,IAAIc,MAAM,GAAGJ,WAAW,EAAE;QACtBA,WAAW,GAAGI,MAAM;MACxB;MACA,IAAID,IAAI,GAAGF,QAAQ,EAAE;QACjBA,QAAQ,GAAGE,IAAI;MACnB;MACA,IAAIE,EAAE,GAAGJ,QAAQ,EAAE;QACfA,QAAQ,GAAGI,EAAE;MACjB;IACJ;IACAL,WAAW,EAAE;IACbC,QAAQ,EAAE;IACV,MAAMK,MAAM,GAAG,IAAIvB,WAAW,CAACkB,QAAQ,EAAED,WAAW,EAAE,CAAC,CAAC,mBAAmB,CAAC;IAC5E,KAAK,IAAIV,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGQ,KAAK,CAACG,MAAM,EAAEZ,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;MAC9C,MAAM,CAACa,IAAI,EAAEC,MAAM,EAAEC,EAAE,CAAC,GAAGN,KAAK,CAACT,CAAC,CAAC;MACnCgB,MAAM,CAACV,GAAG,CAACO,IAAI,EAAEC,MAAM,EAAEC,EAAE,CAAC;IAChC;IACA,IAAI,CAACE,OAAO,GAAGD,MAAM;IACrB,IAAI,CAACE,YAAY,GAAGR,WAAW;EACnC;EACAS,SAASA,CAACC,YAAY,EAAEN,MAAM,EAAE;IAC5B,IAAIA,MAAM,GAAG,CAAC,IAAIA,MAAM,IAAI,IAAI,CAACI,YAAY,EAAE;MAC3C,OAAO,CAAC,CAAC;IACb;IACA,OAAO,IAAI,CAACD,OAAO,CAACd,GAAG,CAACiB,YAAY,EAAEN,MAAM,CAAC;EACjD;AACJ;AACA;AACA,IAAIO,aAAa,GAAG,IAAI;AACxB,SAASC,eAAeA,CAAA,EAAG;EACvB,IAAID,aAAa,KAAK,IAAI,EAAE;IACxBA,aAAa,GAAG,IAAIb,YAAY,CAAC,CAC7B,CAAC,CAAC,CAAC,mBAAmB,GAAG,CAAC,kBAAkB,CAAC,CAAC,cAAc,EAC5D,CAAC,CAAC,CAAC,mBAAmB,EAAE,CAAC,kBAAkB,CAAC,CAAC,cAAc,EAC3D,CAAC,CAAC,CAAC,mBAAmB,GAAG,CAAC,kBAAkB,CAAC,CAAC,cAAc,EAC5D,CAAC,CAAC,CAAC,mBAAmB,EAAE,CAAC,kBAAkB,CAAC,CAAC,cAAc,EAC3D,CAAC,CAAC,CAAC,eAAe,GAAG,CAAC,kBAAkB,CAAC,CAAC,eAAe,EACzD,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,kBAAkB,CAAC,CAAC,eAAe,EACxD,CAAC,CAAC,CAAC,gBAAgB,GAAG,CAAC,kBAAkB,CAAC,CAAC,gBAAgB,EAC3D,CAAC,CAAC,CAAC,gBAAgB,EAAE,CAAC,kBAAkB,CAAC,CAAC,gBAAgB,EAC1D,CAAC,CAAC,CAAC,iBAAiB,GAAG,CAAC,kBAAkB,CAAC,CAAC,iBAAiB,EAC7D,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC,kBAAkB,CAAC,CAAC,iBAAiB,EAC5D,CAAC,CAAC,CAAC,kBAAkB,GAAG,CAAC,kBAAkB,CAAC,CAAC,wBAAwB,EACrE,CAAC,CAAC,CAAC,kBAAkB,EAAE,CAAC,kBAAkB,CAAC,CAAC,wBAAwB,EACpE,CAAC,CAAC,CAAC,kBAAkB,EAAE,CAAC,sBAAsB,EAAE,CAAC,uBAAuB,EACxE,CAAC,CAAC,CAAC,eAAe,GAAG,CAAC,kBAAkB,CAAC,CAAC,eAAe,EACzD,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,kBAAkB,CAAC,CAAC,eAAe,EACxD,CAAC,CAAC,CAAC,gBAAgB,GAAG,CAAC,kBAAkB,CAAC,CAAC,gBAAgB,EAC3D,CAAC,CAAC,CAAC,gBAAgB,EAAE,CAAC,kBAAkB,CAAC,CAAC,gBAAgB,EAC1D,CAAC,CAAC,CAAC,iBAAiB,GAAG,CAAC,kBAAkB,CAAC,CAAC,wBAAwB,EACpE,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC,kBAAkB,CAAC,CAAC,wBAAwB,EACnE,CAAC,CAAC,CAAC,yBAAyB,EAAE,CAAC,sBAAsB,EAAE,CAAC,uBAAuB,EAC/E,CAAC,EAAE,CAAC,wBAAwB,EAAE,CAAC,sBAAsB,EAAE,CAAC,wBAAwB,EAChF,CAAC,EAAE,CAAC,yBAAyB,EAAE,CAAC,sBAAsB,EAAE,CAAC,gBAAgB,CAC5E,CAAC;EACN;EACA,OAAOa,aAAa;AACxB;AACA,IAAIE,WAAW,GAAG,IAAI;AACtB,SAASC,aAAaA,CAAA,EAAG;EACrB,IAAID,WAAW,KAAK,IAAI,EAAE;IACtBA,WAAW,GAAG,IAAI/B,mBAAmB,CAAC,CAAC,CAAC,yBAAyB,CAAC;IAClE;IACA,MAAMiC,4BAA4B,GAAG,wCAAwC;IAC7E,KAAK,IAAIzB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyB,4BAA4B,CAACb,MAAM,EAAEZ,CAAC,EAAE,EAAE;MAC1DuB,WAAW,CAACjB,GAAG,CAACmB,4BAA4B,CAACC,UAAU,CAAC1B,CAAC,CAAC,EAAE,CAAC,CAAC,qCAAqC,CAAC;IACxG;IACA,MAAM2B,0BAA0B,GAAG,MAAM;IACzC,KAAK,IAAI3B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2B,0BAA0B,CAACf,MAAM,EAAEZ,CAAC,EAAE,EAAE;MACxDuB,WAAW,CAACjB,GAAG,CAACqB,0BAA0B,CAACD,UAAU,CAAC1B,CAAC,CAAC,EAAE,CAAC,CAAC,gCAAgC,CAAC;IACjG;EACJ;EACA,OAAOuB,WAAW;AACtB;AACA,OAAO,MAAMK,YAAY,CAAC;EACtB,OAAOC,WAAWA,CAACC,UAAU,EAAEC,IAAI,EAAEC,UAAU,EAAEC,cAAc,EAAEC,YAAY,EAAE;IAC3E;IACA,IAAIC,qBAAqB,GAAGD,YAAY,GAAG,CAAC;IAC5C,GAAG;MACC,MAAMpB,MAAM,GAAGiB,IAAI,CAACL,UAAU,CAACS,qBAAqB,CAAC;MACrD,MAAMC,OAAO,GAAGN,UAAU,CAAC3B,GAAG,CAACW,MAAM,CAAC;MACtC,IAAIsB,OAAO,KAAK,CAAC,CAAC,kCAAkC;QAChD;MACJ;MACAD,qBAAqB,EAAE;IAC3B,CAAC,QAAQA,qBAAqB,GAAGF,cAAc;IAC/C;IACA,IAAIA,cAAc,GAAG,CAAC,EAAE;MACpB,MAAMI,kBAAkB,GAAGN,IAAI,CAACL,UAAU,CAACO,cAAc,GAAG,CAAC,CAAC;MAC9D,MAAMK,kBAAkB,GAAGP,IAAI,CAACL,UAAU,CAACS,qBAAqB,CAAC;MACjE,IAAKE,kBAAkB,KAAK,EAAE,CAAC,4BAA4BC,kBAAkB,KAAK,EAAE,CAAC,6BAC7ED,kBAAkB,KAAK,EAAE,CAAC,oCAAoCC,kBAAkB,KAAK,EAAE,CAAC,iCAAkC,IAC1HD,kBAAkB,KAAK,GAAG,CAAC,iCAAiCC,kBAAkB,KAAK,GAAG,CAAC,8BAA+B,EAAE;QAC5H;QACA;QACA;QACAH,qBAAqB,EAAE;MAC3B;IACJ;IACA,OAAO;MACHI,KAAK,EAAE;QACHC,eAAe,EAAER,UAAU;QAC3BS,WAAW,EAAER,cAAc,GAAG,CAAC;QAC/BS,aAAa,EAAEV,UAAU;QACzBW,SAAS,EAAER,qBAAqB,GAAG;MACvC,CAAC;MACDS,GAAG,EAAEb,IAAI,CAACc,SAAS,CAACZ,cAAc,EAAEE,qBAAqB,GAAG,CAAC;IACjE,CAAC;EACL;EACA,OAAOW,YAAYA,CAACC,KAAK,EAAEC,YAAY,GAAG1B,eAAe,CAAC,CAAC,EAAE;IACzD,MAAMQ,UAAU,GAAGN,aAAa,CAAC,CAAC;IAClC,MAAMyB,MAAM,GAAG,EAAE;IACjB,KAAK,IAAIjD,CAAC,GAAG,CAAC,EAAEkD,SAAS,GAAGH,KAAK,CAACI,YAAY,CAAC,CAAC,EAAEnD,CAAC,IAAIkD,SAAS,EAAElD,CAAC,EAAE,EAAE;MACnE,MAAM+B,IAAI,GAAGgB,KAAK,CAACK,cAAc,CAACpD,CAAC,CAAC;MACpC,MAAMC,GAAG,GAAG8B,IAAI,CAACnB,MAAM;MACvB,IAAIyC,CAAC,GAAG,CAAC;MACT,IAAIpB,cAAc,GAAG,CAAC;MACtB,IAAIqB,eAAe,GAAG,CAAC;MACvB,IAAIC,KAAK,GAAG,CAAC,CAAC;MACd,IAAIC,aAAa,GAAG,KAAK;MACzB,IAAIC,oBAAoB,GAAG,KAAK;MAChC,IAAIC,gBAAgB,GAAG,KAAK;MAC5B,IAAIC,mBAAmB,GAAG,KAAK;MAC/B,OAAON,CAAC,GAAGpD,GAAG,EAAE;QACZ,IAAI2D,iBAAiB,GAAG,KAAK;QAC7B,MAAM9C,MAAM,GAAGiB,IAAI,CAACL,UAAU,CAAC2B,CAAC,CAAC;QACjC,IAAIE,KAAK,KAAK,EAAE,CAAC,oBAAoB;UACjC,IAAInB,OAAO;UACX,QAAQtB,MAAM;YACV,KAAK,EAAE,CAAC;cACJ0C,aAAa,GAAG,IAAI;cACpBpB,OAAO,GAAG,CAAC,CAAC;cACZ;YACJ,KAAK,EAAE,CAAC;cACJA,OAAO,GAAIoB,aAAa,GAAG,CAAC,CAAC,4BAA4B,CAAC,CAAC,qCAAsC;cACjG;YACJ,KAAK,EAAE,CAAC;cACJE,gBAAgB,GAAG,IAAI;cACvBD,oBAAoB,GAAG,IAAI;cAC3BrB,OAAO,GAAG,CAAC,CAAC;cACZ;YACJ,KAAK,EAAE,CAAC;cACJsB,gBAAgB,GAAG,KAAK;cACxBtB,OAAO,GAAIqB,oBAAoB,GAAG,CAAC,CAAC,4BAA4B,CAAC,CAAC,qCAAsC;cACxG;YACJ,KAAK,GAAG,CAAC;cACLE,mBAAmB,GAAG,IAAI;cAC1BvB,OAAO,GAAG,CAAC,CAAC;cACZ;YACJ,KAAK,GAAG,CAAC;cACLA,OAAO,GAAIuB,mBAAmB,GAAG,CAAC,CAAC,4BAA4B,CAAC,CAAC,qCAAsC;cACvG;YACJ;YACA;YACA,KAAK,EAAE,CAAC;YACR,KAAK,EAAE,CAAC;YACR,KAAK,EAAE,CAAC;cACJ,IAAIL,eAAe,KAAKxC,MAAM,EAAE;gBAC5BsB,OAAO,GAAG,CAAC,CAAC;cAChB,CAAC,MACI,IAAIkB,eAAe,KAAK,EAAE,CAAC,8BAA8BA,eAAe,KAAK,EAAE,CAAC,8BAA8BA,eAAe,KAAK,EAAE,CAAC,yBAAyB;gBAC/JlB,OAAO,GAAG,CAAC,CAAC;cAChB,CAAC,MACI;gBACDA,OAAO,GAAG,CAAC,CAAC;cAChB;cACA;YACJ,KAAK,EAAE,CAAC;cACJ;cACAA,OAAO,GAAIkB,eAAe,KAAK,EAAE,CAAC,0BAA2B,CAAC,CAAC,wCAAwC,CAAC,CAAC;cACzG;YACJ,KAAK,GAAG,CAAC;cACL;cACAlB,OAAO,GAAIkB,eAAe,KAAK,GAAG,CAAC,sBAAuB,CAAC,CAAC,wCAAwC,CAAC,CAAC;cACtG;YACJ,KAAK,EAAE,CAAC;cACJ;cACAlB,OAAO,GAAIsB,gBAAgB,GAAG,CAAC,CAAC,4BAA4B,CAAC,CAAC,qCAAsC;cACpG;YACJ;cACItB,OAAO,GAAGN,UAAU,CAAC3B,GAAG,CAACW,MAAM,CAAC;UACxC;UACA;UACA,IAAIsB,OAAO,KAAK,CAAC,CAAC,uCAAuC;YACrDa,MAAM,CAACY,IAAI,CAACjC,YAAY,CAACC,WAAW,CAACC,UAAU,EAAEC,IAAI,EAAE/B,CAAC,EAAEiC,cAAc,EAAEoB,CAAC,CAAC,CAAC;YAC7EO,iBAAiB,GAAG,IAAI;UAC5B;QACJ,CAAC,MACI,IAAIL,KAAK,KAAK,EAAE,CAAC,iBAAiB;UACnC,IAAInB,OAAO;UACX,IAAItB,MAAM,KAAK,EAAE,CAAC,kCAAkC;YAChD;YACA2C,oBAAoB,GAAG,IAAI;YAC3BrB,OAAO,GAAG,CAAC,CAAC;UAChB,CAAC,MACI;YACDA,OAAO,GAAGN,UAAU,CAAC3B,GAAG,CAACW,MAAM,CAAC;UACpC;UACA;UACA,IAAIsB,OAAO,KAAK,CAAC,CAAC,uCAAuC;YACrDwB,iBAAiB,GAAG,IAAI;UAC5B,CAAC,MACI;YACDL,KAAK,GAAG,EAAE,CAAC;UACf;QACJ,CAAC,MACI;UACDA,KAAK,GAAGP,YAAY,CAAC7B,SAAS,CAACoC,KAAK,EAAEzC,MAAM,CAAC;UAC7C,IAAIyC,KAAK,KAAK,CAAC,CAAC,qBAAqB;YACjCK,iBAAiB,GAAG,IAAI;UAC5B;QACJ;QACA,IAAIA,iBAAiB,EAAE;UACnBL,KAAK,GAAG,CAAC,CAAC;UACVC,aAAa,GAAG,KAAK;UACrBC,oBAAoB,GAAG,KAAK;UAC5BE,mBAAmB,GAAG,KAAK;UAC3B;UACA1B,cAAc,GAAGoB,CAAC,GAAG,CAAC;UACtBC,eAAe,GAAGxC,MAAM;QAC5B;QACAuC,CAAC,EAAE;MACP;MACA,IAAIE,KAAK,KAAK,EAAE,CAAC,oBAAoB;QACjCN,MAAM,CAACY,IAAI,CAACjC,YAAY,CAACC,WAAW,CAACC,UAAU,EAAEC,IAAI,EAAE/B,CAAC,EAAEiC,cAAc,EAAEhC,GAAG,CAAC,CAAC;MACnF;IACJ;IACA,OAAOgD,MAAM;EACjB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASH,YAAYA,CAACC,KAAK,EAAE;EAChC,IAAI,CAACA,KAAK,IAAI,OAAOA,KAAK,CAACI,YAAY,KAAK,UAAU,IAAI,OAAOJ,KAAK,CAACK,cAAc,KAAK,UAAU,EAAE;IAClG;IACA,OAAO,EAAE;EACb;EACA,OAAOxB,YAAY,CAACkB,YAAY,CAACC,KAAK,CAAC;AAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}