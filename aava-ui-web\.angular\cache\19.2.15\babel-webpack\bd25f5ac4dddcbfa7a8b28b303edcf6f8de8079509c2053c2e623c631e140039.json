{"ast": null, "code": "export function memoize(_target, key, descriptor) {\n  let fnKey = null;\n  let fn = null;\n  if (typeof descriptor.value === 'function') {\n    fnKey = 'value';\n    fn = descriptor.value;\n    if (fn.length !== 0) {\n      console.warn('Memoize should only be used in functions with zero parameters');\n    }\n  } else if (typeof descriptor.get === 'function') {\n    fnKey = 'get';\n    fn = descriptor.get;\n  }\n  if (!fn) {\n    throw new Error('not supported');\n  }\n  const memoizeKey = `$memoize$${key}`;\n  descriptor[fnKey] = function (...args) {\n    if (!this.hasOwnProperty(memoizeKey)) {\n      Object.defineProperty(this, memoizeKey, {\n        configurable: false,\n        enumerable: false,\n        writable: false,\n        value: fn.apply(this, args)\n      });\n    }\n    return this[memoizeKey];\n  };\n}", "map": {"version": 3, "names": ["memoize", "_target", "key", "descriptor", "fnKey", "fn", "value", "length", "console", "warn", "get", "Error", "memoize<PERSON>ey", "args", "hasOwnProperty", "Object", "defineProperty", "configurable", "enumerable", "writable", "apply"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/common/decorators.js"], "sourcesContent": ["export function memoize(_target, key, descriptor) {\n    let fnKey = null;\n    let fn = null;\n    if (typeof descriptor.value === 'function') {\n        fnKey = 'value';\n        fn = descriptor.value;\n        if (fn.length !== 0) {\n            console.warn('Memoize should only be used in functions with zero parameters');\n        }\n    }\n    else if (typeof descriptor.get === 'function') {\n        fnKey = 'get';\n        fn = descriptor.get;\n    }\n    if (!fn) {\n        throw new Error('not supported');\n    }\n    const memoizeKey = `$memoize$${key}`;\n    descriptor[fnKey] = function (...args) {\n        if (!this.hasOwnProperty(memoizeKey)) {\n            Object.defineProperty(this, memoizeKey, {\n                configurable: false,\n                enumerable: false,\n                writable: false,\n                value: fn.apply(this, args)\n            });\n        }\n        return this[memoizeKey];\n    };\n}\n"], "mappings": "AAAA,OAAO,SAASA,OAAOA,CAACC,OAAO,EAAEC,GAAG,EAAEC,UAAU,EAAE;EAC9C,IAAIC,KAAK,GAAG,IAAI;EAChB,IAAIC,EAAE,GAAG,IAAI;EACb,IAAI,OAAOF,UAAU,CAACG,KAAK,KAAK,UAAU,EAAE;IACxCF,KAAK,GAAG,OAAO;IACfC,EAAE,GAAGF,UAAU,CAACG,KAAK;IACrB,IAAID,EAAE,CAACE,MAAM,KAAK,CAAC,EAAE;MACjBC,OAAO,CAACC,IAAI,CAAC,+DAA+D,CAAC;IACjF;EACJ,CAAC,MACI,IAAI,OAAON,UAAU,CAACO,GAAG,KAAK,UAAU,EAAE;IAC3CN,KAAK,GAAG,KAAK;IACbC,EAAE,GAAGF,UAAU,CAACO,GAAG;EACvB;EACA,IAAI,CAACL,EAAE,EAAE;IACL,MAAM,IAAIM,KAAK,CAAC,eAAe,CAAC;EACpC;EACA,MAAMC,UAAU,GAAG,YAAYV,GAAG,EAAE;EACpCC,UAAU,CAACC,KAAK,CAAC,GAAG,UAAU,GAAGS,IAAI,EAAE;IACnC,IAAI,CAAC,IAAI,CAACC,cAAc,CAACF,UAAU,CAAC,EAAE;MAClCG,MAAM,CAACC,cAAc,CAAC,IAAI,EAAEJ,UAAU,EAAE;QACpCK,YAAY,EAAE,KAAK;QACnBC,UAAU,EAAE,KAAK;QACjBC,QAAQ,EAAE,KAAK;QACfb,KAAK,EAAED,EAAE,CAACe,KAAK,CAAC,IAAI,EAAEP,IAAI;MAC9B,CAAC,CAAC;IACN;IACA,OAAO,IAAI,CAACD,UAAU,CAAC;EAC3B,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}