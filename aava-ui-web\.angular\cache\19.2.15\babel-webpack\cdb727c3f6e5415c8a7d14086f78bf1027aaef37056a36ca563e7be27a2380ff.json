{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { HeaderComponent } from '@awe/play-comp-library';\nimport { NavigationEnd, NavigationStart } from '@angular/router';\nimport { filter } from 'rxjs/operators';\nimport { Subscription } from 'rxjs';\nimport { DropdownComponent, IconComponent } from '@ava/play-comp-library';\nimport { ButtonComponent } from '@ava/play-comp-library';\nimport { SharedNavItemComponent } from '../nav-item/nav-item.component';\nimport { Validators } from '@angular/forms';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../auth/services/token-storage.service\";\nimport * as i3 from \"../../auth/services/auth.service\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@angular/common\";\nconst _c0 = [\"orgPathTrigger\"];\nconst _c1 = [\"popover\"];\nconst _c2 = () => [];\nfunction SharedAppHeaderComponent_div_0_div_9_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 25);\n  }\n  if (rf & 2) {\n    const i_r3 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"active\", i_r3 === ctx_r1.currentLogoIndex);\n  }\n}\nfunction SharedAppHeaderComponent_div_0_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23);\n    i0.ɵɵtemplate(1, SharedAppHeaderComponent_div_0_div_9_div_1_Template, 1, 2, \"div\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.studioLogos);\n  }\n}\nfunction SharedAppHeaderComponent_div_0_shared_nav_item_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"shared-nav-item\", 26);\n    i0.ɵɵlistener(\"toggleDropdownEvent\", function SharedAppHeaderComponent_div_0_shared_nav_item_15_Template_shared_nav_item_toggleDropdownEvent_0_listener() {\n      const i_r5 = i0.ɵɵrestoreView(_r4).index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleDropdown(i_r5));\n    })(\"navigateEvent\", function SharedAppHeaderComponent_div_0_shared_nav_item_15_Template_shared_nav_item_navigateEvent_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.navigateTo($event));\n    })(\"selectEvent\", function SharedAppHeaderComponent_div_0_shared_nav_item_15_Template_shared_nav_item_selectEvent_0_listener() {\n      const i_r5 = i0.ɵɵrestoreView(_r4).index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.selectMenuItem(i_r5));\n    })(\"dropdownItemSelected\", function SharedAppHeaderComponent_div_0_shared_nav_item_15_Template_shared_nav_item_dropdownItemSelected_0_listener($event) {\n      const i_r5 = i0.ɵɵrestoreView(_r4).index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onDropdownItemSelected($event, i_r5));\n    })(\"dropdownPortalOpen\", function SharedAppHeaderComponent_div_0_shared_nav_item_15_Template_shared_nav_item_dropdownPortalOpen_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onDropdownPortalOpen($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r6 = ctx.$implicit;\n    i0.ɵɵproperty(\"label\", item_r6.label)(\"route\", item_r6.route)(\"selected\", item_r6.selected)(\"hasDropdown\", item_r6.hasDropdown)(\"dropdownOpen\", item_r6.dropdownOpen || false)(\"dropdownItems\", item_r6.dropdownItems || i0.ɵɵpureFunction0(8, _c2))(\"icon\", item_r6.icon)(\"disabled\", item_r6.disabled || false);\n  }\n}\nfunction SharedAppHeaderComponent_div_0_div_17_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 36, 1)(2, \"form\", 37)(3, \"div\", 38);\n    i0.ɵɵtext(4, \"Filter Configuration\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 39)(6, \"label\", 40);\n    i0.ɵɵtext(7, \"Choose Organization\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"ava-dropdown\", 41);\n    i0.ɵɵlistener(\"selectionChange\", function SharedAppHeaderComponent_div_0_div_17_div_8_Template_ava_dropdown_selectionChange_8_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onOrgSelect($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"label\", 40);\n    i0.ɵɵtext(10, \"Choose Domain\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"ava-dropdown\", 41);\n    i0.ɵɵlistener(\"selectionChange\", function SharedAppHeaderComponent_div_0_div_17_div_8_Template_ava_dropdown_selectionChange_11_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onDomainSelect($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"label\", 40);\n    i0.ɵɵtext(13, \"Choose Project\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"ava-dropdown\", 41);\n    i0.ɵɵlistener(\"selectionChange\", function SharedAppHeaderComponent_div_0_div_17_div_8_Template_ava_dropdown_selectionChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onProjectSelect($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"label\", 40);\n    i0.ɵɵtext(16, \"Choose Team\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"ava-dropdown\", 41);\n    i0.ɵɵlistener(\"selectionChange\", function SharedAppHeaderComponent_div_0_div_17_div_8_Template_ava_dropdown_selectionChange_17_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onTeamSelect($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 42)(19, \"ava-button\", 43);\n    i0.ɵɵlistener(\"userClick\", function SharedAppHeaderComponent_div_0_div_17_div_8_Template_ava_button_userClick_19_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.closeOrgDialog());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"ava-button\", 44);\n    i0.ɵɵlistener(\"userClick\", function SharedAppHeaderComponent_div_0_div_17_div_8_Template_ava_button_userClick_20_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.saveOrgPathAndClose());\n    });\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.popoverAlign);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.headerConfigForm);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"dropdownTitle\", \"Select Organization\")(\"options\", ctx_r1.orgOptions)(\"selectedValue\", ctx_r1.selectedOrgName)(\"disabled\", false)(\"search\", true)(\"enableSearch\", true);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"dropdownTitle\", \"Select Domain\")(\"options\", ctx_r1.domainOptions)(\"selectedValue\", ctx_r1.selectedDomainName)(\"disabled\", !ctx_r1.selectedOrg)(\"search\", true)(\"enableSearch\", true);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"dropdownTitle\", \"Select Project\")(\"options\", ctx_r1.projectOptions)(\"selectedValue\", ctx_r1.selectedProjectName)(\"disabled\", !ctx_r1.selectedDomain)(\"search\", true)(\"enableSearch\", true);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"dropdownTitle\", \"Select Team\")(\"options\", ctx_r1.teamOptions)(\"selectedValue\", ctx_r1.selectedTeamName)(\"disabled\", !ctx_r1.selectedProject)(\"search\", true)(\"enableSearch\", true);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.headerConfigForm.valid);\n  }\n}\nfunction SharedAppHeaderComponent_div_0_div_17_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵlistener(\"click\", function SharedAppHeaderComponent_div_0_div_17_div_9_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.closeOrgDialog());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SharedAppHeaderComponent_div_0_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"div\", 28, 0);\n    i0.ɵɵlistener(\"click\", function SharedAppHeaderComponent_div_0_div_17_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleOrgDialog());\n    });\n    i0.ɵɵelementStart(3, \"span\", 29);\n    i0.ɵɵelement(4, \"img\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 31);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(6, \"svg\", 32);\n    i0.ɵɵelement(7, \"path\", 33);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(8, SharedAppHeaderComponent_div_0_div_17_div_8_Template, 21, 27, \"div\", 34)(9, SharedAppHeaderComponent_div_0_div_17_div_9_Template, 1, 0, \"div\", 35);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"open\", ctx_r1.isOrgDialogOpen);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isOrgDialogOpen);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isOrgDialogOpen);\n  }\n}\nfunction SharedAppHeaderComponent_div_0_div_18_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 54);\n    i0.ɵɵlistener(\"click\", function SharedAppHeaderComponent_div_0_div_18_div_3_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.closeAppDrawer());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SharedAppHeaderComponent_div_0_div_18_div_7_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const app_r13 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", app_r13.description, \" \");\n  }\n}\nfunction SharedAppHeaderComponent_div_0_div_18_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵlistener(\"click\", function SharedAppHeaderComponent_div_0_div_18_div_7_Template_div_click_0_listener() {\n      const app_r13 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.navigateToApp(app_r13));\n    });\n    i0.ɵɵelementStart(1, \"div\", 56);\n    i0.ɵɵelement(2, \"img\", 57);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 58)(4, \"div\", 59);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, SharedAppHeaderComponent_div_0_div_18_div_7_div_6_Template, 2, 1, \"div\", 60);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const app_r13 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", app_r13.icon, i0.ɵɵsanitizeUrl)(\"alt\", app_r13.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(app_r13.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", app_r13.description);\n  }\n}\nfunction SharedAppHeaderComponent_div_0_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"div\", 47);\n    i0.ɵɵlistener(\"click\", function SharedAppHeaderComponent_div_0_div_18_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleAppDrawer());\n    });\n    i0.ɵɵelement(2, \"ava-icon\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, SharedAppHeaderComponent_div_0_div_18_div_3_Template, 1, 0, \"div\", 49);\n    i0.ɵɵelementStart(4, \"div\", 50)(5, \"div\", 51)(6, \"div\", 52);\n    i0.ɵɵtemplate(7, SharedAppHeaderComponent_div_0_div_18_div_7_Template, 7, 4, \"div\", 53);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active\", ctx_r1.isAppDrawerOpen);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isAppDrawerOpen);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"visible\", ctx_r1.isAppDrawerOpen);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getFilteredApps());\n  }\n}\nfunction SharedAppHeaderComponent_div_0_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵlistener(\"click\", function SharedAppHeaderComponent_div_0_div_19_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleTheme());\n    });\n    i0.ɵɵelement(1, \"img\", 63);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r1.themeMenuIcon, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction SharedAppHeaderComponent_div_0_div_20_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 84);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.userDesignation, \" \");\n  }\n}\nfunction SharedAppHeaderComponent_div_0_div_20_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 85);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.userEmail, \" \");\n  }\n}\nfunction SharedAppHeaderComponent_div_0_div_20_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 86)(1, \"div\", 87)(2, \"span\", 88);\n    i0.ɵɵtext(3, \"Mode\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 89)(5, \"button\", 90);\n    i0.ɵɵlistener(\"click\", function SharedAppHeaderComponent_div_0_div_20_div_14_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.currentTheme !== \"light\" && ctx_r1.toggleTheme());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(6, \"svg\", 80);\n    i0.ɵɵelement(7, \"circle\", 91)(8, \"path\", 92);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(9, \" Light \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(10, \"button\", 90);\n    i0.ɵɵlistener(\"click\", function SharedAppHeaderComponent_div_0_div_20_div_14_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.currentTheme !== \"dark\" && ctx_r1.toggleTheme());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(11, \"svg\", 80);\n    i0.ɵɵelement(12, \"path\", 93);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(13, \" Dark \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"active\", ctx_r1.currentTheme === \"light\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"active\", ctx_r1.currentTheme === \"dark\");\n  }\n}\nfunction SharedAppHeaderComponent_div_0_div_20_div_15_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 96);\n    i0.ɵɵlistener(\"click\", function SharedAppHeaderComponent_div_0_div_20_div_15_button_5_Template_button_click_0_listener() {\n      const language_r18 = i0.ɵɵrestoreView(_r17).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.switchLanguage(language_r18.code));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const language_r18 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵclassProp(\"active\", ctx_r1.currentLanguage === language_r18.code);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", language_r18.name, \" \");\n  }\n}\nfunction SharedAppHeaderComponent_div_0_div_20_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 86)(1, \"div\", 87)(2, \"span\", 88);\n    i0.ɵɵtext(3, \"Language\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 94);\n    i0.ɵɵtemplate(5, SharedAppHeaderComponent_div_0_div_20_div_15_button_5_Template, 2, 3, \"button\", 95);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.config.availableLanguages);\n  }\n}\nfunction SharedAppHeaderComponent_div_0_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 64)(1, \"div\", 65);\n    i0.ɵɵlistener(\"click\", function SharedAppHeaderComponent_div_0_div_20_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleProfileDropdown());\n    });\n    i0.ɵɵelement(2, \"img\", 66);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 67)(4, \"div\", 68)(5, \"div\", 69)(6, \"div\", 70);\n    i0.ɵɵelement(7, \"img\", 71);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 72)(9, \"div\", 73);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, SharedAppHeaderComponent_div_0_div_20_div_11_Template, 2, 1, \"div\", 74)(12, SharedAppHeaderComponent_div_0_div_20_div_12_Template, 2, 1, \"div\", 75);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(13, \"div\", 76);\n    i0.ɵɵtemplate(14, SharedAppHeaderComponent_div_0_div_20_div_14_Template, 14, 4, \"div\", 77)(15, SharedAppHeaderComponent_div_0_div_20_div_15_Template, 6, 1, \"div\", 77);\n    i0.ɵɵelement(16, \"div\", 76);\n    i0.ɵɵelementStart(17, \"div\", 78)(18, \"button\", 79);\n    i0.ɵɵlistener(\"click\", function SharedAppHeaderComponent_div_0_div_20_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.logout());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(19, \"svg\", 80);\n    i0.ɵɵelement(20, \"path\", 81)(21, \"polyline\", 82)(22, \"line\", 83);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(23, \" Sign Out \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active\", ctx_r1.profileDropdownOpen);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r1.userAvatar, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"visible\", ctx_r1.profileDropdownOpen);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", ctx_r1.userAvatar, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.userName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.userDesignation);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.userEmail);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.config.showThemeToggleInProfile);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.config.showLanguageSwitcher);\n  }\n}\nfunction SharedAppHeaderComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 4)(2, \"defs\")(3, \"clipPath\", 5);\n    i0.ɵɵelement(4, \"path\", 6);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(5, \"awe-header\", 7)(6, \"div\", 8)(7, \"div\", 9);\n    i0.ɵɵlistener(\"mouseenter\", function SharedAppHeaderComponent_div_0_Template_div_mouseenter_7_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.pauseLogoAnimation());\n    })(\"mouseleave\", function SharedAppHeaderComponent_div_0_Template_div_mouseleave_7_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.resumeLogoAnimation());\n    });\n    i0.ɵɵelement(8, \"img\", 10);\n    i0.ɵɵtemplate(9, SharedAppHeaderComponent_div_0_div_9_Template, 2, 1, \"div\", 11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 12)(11, \"div\", 13);\n    i0.ɵɵelement(12, \"div\", 14);\n    i0.ɵɵelementStart(13, \"div\", 15)(14, \"div\", 16);\n    i0.ɵɵtemplate(15, SharedAppHeaderComponent_div_0_shared_nav_item_15_Template, 1, 9, \"shared-nav-item\", 17);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(16, \"div\", 18);\n    i0.ɵɵtemplate(17, SharedAppHeaderComponent_div_0_div_17_Template, 10, 4, \"div\", 19)(18, SharedAppHeaderComponent_div_0_div_18_Template, 8, 6, \"div\", 20)(19, SharedAppHeaderComponent_div_0_div_19_Template, 2, 1, \"div\", 21)(20, SharedAppHeaderComponent_div_0_div_20_Template, 24, 11, \"div\", 22);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵattribute(\"data-studio\", ctx_r1.currentStudioName)(\"data-index\", ctx_r1.currentLogoIndex);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"logo-transitioning\", ctx_r1.isLogoAnimating);\n    i0.ɵɵproperty(\"src\", ctx_r1.currentLogo, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.currentStudioName + \" Logo\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.studioLogos.length > 1);\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassMap(ctx_r1.navItemsClasses);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.config.navItems);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.config.showOrgSelector);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.config.showAppDrawer);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.config.showThemeToggle && !ctx_r1.config.showAppDrawer);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.config.showProfileDropdown);\n  }\n}\nfunction SharedAppHeaderComponent_div_1_div_2_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 105);\n  }\n  if (rf & 2) {\n    const item_r20 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"src\", item_r20.icon, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction SharedAppHeaderComponent_div_1_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 100);\n    i0.ɵɵlistener(\"click\", function SharedAppHeaderComponent_div_1_div_2_Template_div_click_0_listener() {\n      const item_r20 = i0.ɵɵrestoreView(_r19).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onDropdownItemSelected({\n        route: item_r20.route,\n        label: item_r20.label\n      }, 0));\n    });\n    i0.ɵɵtemplate(1, SharedAppHeaderComponent_div_1_div_2_img_1_Template, 1, 1, \"img\", 101);\n    i0.ɵɵelementStart(2, \"div\", 102)(3, \"div\", 103);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 104);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r20 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r20.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r20.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r20.description);\n  }\n}\nfunction SharedAppHeaderComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 97)(1, \"div\", 98);\n    i0.ɵɵtemplate(2, SharedAppHeaderComponent_div_1_div_2_Template, 7, 3, \"div\", 99);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"top\", ctx_r1.dropdownPortal.rect.bottom + 4 + \"px\")(\"left\", ctx_r1.dropdownPortal.rect.left + \"px\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.dropdownPortal.items);\n  }\n}\nexport let SharedAppHeaderComponent = /*#__PURE__*/(() => {\n  class SharedAppHeaderComponent {\n    router;\n    cdr;\n    renderer;\n    elementRef;\n    tokenStorage;\n    authService;\n    formBuilder;\n    config;\n    customNavItemComponent; // Allow custom nav item component\n    orgConfigService; // Allow injecting org config service from parent\n    themeService; // Allow injecting theme service from parent\n    // Header state\n    currentTheme = 'light';\n    profileDropdownOpen = false;\n    userName = '';\n    userEmail = '';\n    userDesignation = '';\n    userAvatar = '';\n    themeMenuIcon = '';\n    isLoginPage = false; // Added to track login pages\n    // Animated logo management\n    studioLogos = ['assets/svgs/ascendion-logo/header-ascendion-logo.svg', 'assets/svgs/ascendion-logo-light.svg', 'assets/svgs/ascendion-logo-dark.svg'];\n    studioLogosDark = []; // Dark theme logos array\n    studioNames = ['Console', 'Experience Studio', 'Product Studio'];\n    currentLogoIndex = 0;\n    currentLogo = '';\n    currentStudioName = '';\n    logoAnimationInterval;\n    isLogoAnimating = false;\n    // Language management\n    currentLanguage = 'en';\n    // Computed property for dynamic nav classes based on content\n    get navItemsClasses() {\n      const itemCount = this.config?.navItems?.length || 0;\n      let classes = '';\n      if (itemCount === 1) {\n        classes += 'single-item ';\n      } else if (itemCount >= 8) {\n        classes += 'very-many-items ';\n      } else if (itemCount >= 6) {\n        classes += 'many-items ';\n      }\n      return classes.trim();\n    }\n    // Events for parent components to handle\n    navigationEvent = new EventEmitter();\n    dropdownItemSelected = new EventEmitter();\n    profileAction = new EventEmitter();\n    themeToggle = new EventEmitter();\n    languageChange = new EventEmitter();\n    orgConfigChange = new EventEmitter();\n    // Organization selector state (if enabled)\n    isOrgDialogOpen = false;\n    // App drawer state\n    isAppDrawerOpen = false;\n    // Organization dropdown state\n    orgOptions = [];\n    domainOptions = [];\n    projectOptions = [];\n    teamOptions = [];\n    // Selected values (IDs for form)\n    selectedOrg = '';\n    selectedDomain = '';\n    selectedProject = '';\n    selectedTeam = '';\n    // Selected names (for dropdown pre-selection)\n    selectedOrgName = '';\n    selectedDomainName = '';\n    selectedProjectName = '';\n    selectedTeamName = '';\n    // Form for organization config\n    headerConfigForm;\n    // Store the hierarchy data from API\n    hierarchyData = [];\n    orgPathTrigger;\n    popoverRef;\n    popoverAlign = 'left';\n    // Dropdown portal state\n    dropdownPortal = {\n      open: false,\n      rect: null,\n      items: [],\n      parentLabel: '',\n      navItemId: ''\n    };\n    // Subscription management\n    subscriptions = new Subscription();\n    constructor(router, cdr, renderer, elementRef, tokenStorage, authService, formBuilder) {\n      this.router = router;\n      this.cdr = cdr;\n      this.renderer = renderer;\n      this.elementRef = elementRef;\n      this.tokenStorage = tokenStorage;\n      this.authService = authService;\n      this.formBuilder = formBuilder;\n    }\n    ngOnInit() {\n      this.initializeHeader();\n      this.setupRouterSubscription();\n      this.setupThemeSubscription();\n      this.loadUserInfo();\n      this.initializeForm();\n      this.initializeLogo();\n      // Initialize organization selector if enabled\n      if (this.config.showOrgSelector) {\n        this.initOrgPathFromCookie();\n      }\n    }\n    ngAfterViewInit() {\n      this.updateActiveMenuItemByRoute(this.router.url);\n    }\n    ngOnDestroy() {\n      this.subscriptions.unsubscribe();\n      if (this.logoAnimationInterval) {\n        clearInterval(this.logoAnimationInterval);\n      }\n    }\n    // ========================================\n    // LOGO ANIMATION METHODS\n    // ========================================\n    initializeLogo() {\n      // Use config logos if provided, otherwise use defaults\n      if (this.config.studioLogos && this.config.studioLogos.length > 0) {\n        this.studioLogos = this.config.studioLogos;\n        console.log('🎨 Using custom studio logos:', this.studioLogos);\n      } else {\n        console.log('🎨 Using default studio logos:', this.studioLogos);\n      }\n      // Initialize dark theme logos if theme-aware logos are enabled\n      if (this.config.enableThemeAwareLogos && this.config.studioLogosDark && this.config.studioLogosDark.length > 0) {\n        this.studioLogosDark = this.config.studioLogosDark;\n        console.log('🎨 Using custom dark studio logos:', this.studioLogosDark);\n      }\n      if (this.config.studioNames && this.config.studioNames.length > 0) {\n        this.studioNames = this.config.studioNames;\n      }\n      // Set initial logo - use animated logos when animation is enabled\n      if (this.config.enableLogoAnimation !== false && this.getActiveStudioLogos().length > 0) {\n        // Use first logo from animated array (theme-aware)\n        const activeLogos = this.getActiveStudioLogos();\n        this.currentLogo = activeLogos[0];\n        this.currentStudioName = this.studioNames[0];\n        console.log('🎨 Initial animated logo set to:', this.currentLogo);\n        console.log('🎨 Initial studio name set to:', this.currentStudioName);\n      } else {\n        // Fallback to config logoSrc when animation is disabled\n        this.currentLogo = this.config.logoSrc;\n        this.currentStudioName = this.config.projectName || 'Studio';\n        console.log('🎨 Using static logo:', this.currentLogo);\n      }\n      // Start animation cycle only if enabled (default: true)\n      if (this.config.enableLogoAnimation !== false) {\n        this.startLogoAnimation();\n      }\n    }\n    /**\n     * Get the active studio logos array based on current theme\n     * Returns dark logos if theme-aware logos are enabled and current theme is dark\n     */\n    getActiveStudioLogos() {\n      if (this.config.enableThemeAwareLogos && this.currentTheme === 'dark' && this.studioLogosDark.length > 0) {\n        return this.studioLogosDark;\n      }\n      return this.studioLogos;\n    }\n    startLogoAnimation() {\n      // Clear any existing interval\n      if (this.logoAnimationInterval) {\n        clearInterval(this.logoAnimationInterval);\n      }\n      // Use config interval or default to 3 seconds\n      const interval = this.config.logoAnimationInterval || 3000;\n      // Start the animation cycle\n      this.logoAnimationInterval = setInterval(() => {\n        this.animateToNextLogo();\n      }, interval);\n    }\n    animateToNextLogo() {\n      if (this.isLogoAnimating) return;\n      this.isLogoAnimating = true;\n      // Get animation style from config (default: 'rotate')\n      const animationStyle = this.config.logoAnimationStyle || 'fade';\n      // Add rotation animation class\n      const logoElement = document.querySelector('.animated-logo');\n      if (logoElement) {\n        logoElement.classList.add('logo-transitioning');\n        // Apply specific animation style\n        logoElement.classList.add(`logo-${animationStyle}`);\n      }\n      // Determine timing based on animation style\n      const timings = {\n        fade: {\n          changeAt: 600,\n          duration: 1200\n        },\n        // Professional fade timing\n        smooth: {\n          changeAt: 500,\n          duration: 1000\n        },\n        // Smooth fade timing\n        crossfade: {\n          changeAt: 700,\n          duration: 1400\n        } // Crossfade timing\n      };\n      const timing = timings[animationStyle] || timings['fade'];\n      // Change the image source at the optimal point in the animation\n      setTimeout(() => {\n        // Get active logos array (theme-aware)\n        const activeLogos = this.getActiveStudioLogos();\n        // Move to next logo index\n        this.currentLogoIndex = (this.currentLogoIndex + 1) % activeLogos.length;\n        this.currentLogo = activeLogos[this.currentLogoIndex];\n        this.currentStudioName = this.studioNames[this.currentLogoIndex];\n        // Trigger change detection\n        this.cdr.detectChanges();\n      }, timing.changeAt);\n      // Remove animation classes after animation completes\n      setTimeout(() => {\n        if (logoElement) {\n          logoElement.classList.remove('logo-transitioning');\n          logoElement.classList.remove(`logo-${animationStyle}`);\n        }\n        this.isLogoAnimating = false;\n      }, timing.duration);\n    }\n    pauseLogoAnimation() {\n      if (this.logoAnimationInterval) {\n        clearInterval(this.logoAnimationInterval);\n        this.logoAnimationInterval = undefined;\n      }\n    }\n    resumeLogoAnimation() {\n      if (!this.logoAnimationInterval) {\n        this.startLogoAnimation();\n      }\n    }\n    initializeHeader() {\n      if (!this.config) {\n        console.warn('SharedAppHeaderComponent: config is required');\n        return;\n      }\n      // Set default values\n      this.config.showOrgSelector = this.config.showOrgSelector ?? false;\n      this.config.showThemeToggle = this.config.showThemeToggle ?? true;\n      this.config.showAppDrawer = this.config.showAppDrawer ?? false;\n      this.config.showProfileDropdown = this.config.showProfileDropdown ?? true;\n      this.config.projectName = this.config.projectName ?? 'Application';\n      this.config.redirectUrl = this.config.redirectUrl ?? '/';\n      this.config.currentApp = this.config.currentApp ?? '';\n      // Default studio apps\n      this.config.availableApps = this.config.availableApps ?? [{\n        name: 'Console',\n        route: '/console',\n        icon: 'assets/svgs/studios/console-icon.svg',\n        description: 'Agent & Workflow Management'\n      }, {\n        name: 'Experience Studio',\n        route: '/experience-studio',\n        icon: 'assets/svgs/studios/experience-studio-icon.svg',\n        description: 'UI/UX Design & Prototyping'\n      }, {\n        name: 'Product Studio',\n        route: '/product-studio',\n        icon: 'assets/svgs/studios/product-studio-icon.svg',\n        description: 'Product Development'\n      }, {\n        name: 'Launchpad',\n        route: '/launchpad',\n        icon: 'assets/svgs/studios/launchpad-icon.svg',\n        description: 'Project Launch Hub'\n      }];\n      // Profile dropdown defaults\n      this.config.showThemeToggleInProfile = this.config.showThemeToggleInProfile ?? true;\n      this.config.showLanguageSwitcher = this.config.showLanguageSwitcher ?? false;\n      this.config.availableLanguages = this.config.availableLanguages ?? [{\n        code: 'en',\n        name: 'English'\n      }, {\n        code: 'fil',\n        name: 'Filipino'\n      }, {\n        code: 'es',\n        name: 'Español'\n      }];\n    }\n    initializeForm() {\n      this.headerConfigForm = this.formBuilder.group({\n        org: ['', Validators.required],\n        domain: ['', Validators.required],\n        project: ['', Validators.required],\n        team: ['', Validators.required]\n      });\n    }\n    setupRouterSubscription() {\n      //Force-close dropdowns, portal, and overlays as soon as navigation starts\n      const navigationStartSub = this.router.events.pipe(filter(event => event instanceof NavigationStart)).subscribe(() => {\n        this.closeDropdownPortal();\n        this.profileDropdownOpen = false;\n        this.isOrgDialogOpen = false;\n        this.isAppDrawerOpen = false;\n      });\n      this.subscriptions.add(navigationStartSub);\n      const routerSub = this.router.events.pipe(filter(event => event instanceof NavigationEnd)).subscribe(event => {\n        // Check if current route is a login page\n        this.isLoginPage = this.checkIfLoginPage(event.url);\n        this.updateActiveMenuItemByRoute(event.url);\n        // Always close dropdown portal and all dropdowns on navigation\n        this.closeDropdownPortal();\n      });\n      this.subscriptions.add(routerSub);\n      // Check initial route\n      this.isLoginPage = this.checkIfLoginPage(this.router.url);\n    }\n    checkIfLoginPage(url) {\n      // Check for login routes across different studios\n      const loginRoutes = ['/login', '/auth/login', '/experience/login', '/product/login', '/console/login'];\n      return loginRoutes.some(route => url.includes(route));\n    }\n    setupThemeSubscription() {\n      // If theme service is provided, subscribe to theme changes\n      if (this.themeService && this.themeService.themeObservable) {\n        this.subscriptions.add(this.themeService.themeObservable.subscribe(theme => {\n          this.currentTheme = theme;\n          this.updateThemeAssets();\n          this.cdr.markForCheck();\n        }));\n      }\n      this.updateThemeAssets();\n    }\n    loadUserInfo() {\n      this.userName = this.tokenStorage.getDaName() || 'User';\n      this.userEmail = this.tokenStorage.getDaUserEmail?.() || ''; // Safe call if method exists\n      this.userDesignation = this.tokenStorage.getDaUserDesignation?.() || 'Employee'; // Safe call if method exists\n      this.generateUserAvatar();\n    }\n    generateUserAvatar() {\n      // Generate avatar from user initials if no profile picture is available\n      if (this.userName) {\n        const nameParts = this.userName.trim().split(' ');\n        let initials = '';\n        if (nameParts.length >= 2) {\n          // First letter of first name and first letter of last name\n          initials = nameParts[0][0] + nameParts[nameParts.length - 1][0];\n        } else if (nameParts.length === 1) {\n          // Just first letter if only one name\n          initials = nameParts[0][0];\n        } else {\n          initials = 'U'; // Default to 'U' for User\n        }\n        initials = initials.toUpperCase();\n        // Generate a colored avatar with initials\n        const colors = ['#8B5CF6', '#06B6D4', '#10B981', '#F59E0B', '#EF4444', '#8B5A2B', '#6366F1', '#EC4899'];\n        const colorIndex = this.userName.length % colors.length;\n        const backgroundColor = colors[colorIndex];\n        this.userAvatar = `data:image/svg+xml;base64,${btoa(`\n        <svg width=\"40\" height=\"40\" xmlns=\"http://www.w3.org/2000/svg\">\n          <circle cx=\"20\" cy=\"20\" r=\"20\" fill=\"${backgroundColor}\"/>\n          <text x=\"20\" y=\"26\" font-family=\"Inter, Arial, sans-serif\" font-size=\"14\" font-weight=\"600\" fill=\"white\" text-anchor=\"middle\">${initials}</text>\n        </svg>\n      `)}`;\n      }\n    }\n    updateThemeAssets() {\n      // Update theme-specific assets\n      this.themeMenuIcon = this.currentTheme === 'light' ? 'assets/svgs/header/toggle-theme/toggle-to-dark.svg' : 'assets/svgs/header/toggle-theme/toggle-to-light.svg';\n      // Update current logo if theme-aware logos are enabled and animation is active\n      if (this.config.enableThemeAwareLogos && this.config.enableLogoAnimation !== false) {\n        const activeLogos = this.getActiveStudioLogos();\n        if (activeLogos.length > 0 && this.currentLogoIndex < activeLogos.length) {\n          this.currentLogo = activeLogos[this.currentLogoIndex];\n          console.log('🎨 Theme changed - updated logo to:', this.currentLogo);\n        }\n      }\n    }\n    // Organization selector methods\n    initOrgPathFromCookie() {\n      const path = this.tokenStorage.getCookie('org_path');\n      if (path) {\n        const parts = path.split('::');\n        const usecasePath = parts[0] || '';\n        const usecaseIdPath = parts[1] || '';\n        // Parse the IDs\n        const ids = usecaseIdPath.split('@').map(Number);\n        // Set form values (IDs)\n        this.headerConfigForm.patchValue({\n          org: ids[0]?.toString() || '',\n          domain: ids[1]?.toString() || '',\n          project: ids[2]?.toString() || '',\n          team: ids[3]?.toString() || ''\n        });\n        // Store the IDs for form and the names for dropdown pre-selection\n        this.selectedOrg = ids[0]?.toString() || '';\n        this.selectedDomain = ids[1]?.toString() || '';\n        this.selectedProject = ids[2]?.toString() || '';\n        this.selectedTeam = ids[3]?.toString() || '';\n        // Store the names for dropdown pre-selection\n        const pathParts = usecasePath.split('@');\n        this.selectedOrgName = pathParts[0] || '';\n        this.selectedDomainName = pathParts[1] || '';\n        this.selectedProjectName = pathParts[2] || '';\n        this.selectedTeamName = pathParts[3] || '';\n        // Load dropdown options\n        this.loadData();\n      } else {\n        this.loadData();\n      }\n    }\n    loadData() {\n      if (this.orgConfigService) {\n        this.orgConfigService.getOrganizationHierarchy().subscribe({\n          next: data => {\n            this.hierarchyData = data;\n            this.loadOrganizations();\n            // After loading organizations, load cascading dropdowns if we have pre-selected values\n            if (this.selectedOrg) {\n              this.loadDomains(this.selectedOrg);\n              if (this.selectedDomain) {\n                this.loadProjects(this.selectedDomain);\n                if (this.selectedProject) {\n                  this.loadTeams(this.selectedProject);\n                }\n              }\n            }\n          },\n          error: error => {\n            console.error('Error loading organization data:', error);\n          }\n        });\n      }\n    }\n    loadOrganizations() {\n      this.orgOptions = this.hierarchyData.map(org => ({\n        name: org.organizationName,\n        value: org.orgId.toString()\n      }));\n    }\n    onOrgSelect(event) {\n      const selectedOrgId = event.selectedOptions?.[0]?.value;\n      const selectedOrgName = event.selectedOptions?.[0]?.name;\n      if (selectedOrgId) {\n        this.selectedOrg = selectedOrgId;\n        this.selectedOrgName = selectedOrgName;\n        this.headerConfigForm.patchValue({\n          org: selectedOrgId\n        });\n        this.loadDomains(selectedOrgId);\n        // Clear dependent dropdowns\n        this.headerConfigForm.patchValue({\n          domain: '',\n          project: '',\n          team: ''\n        });\n        this.selectedDomain = '';\n        this.selectedProject = '';\n        this.selectedTeam = '';\n        this.selectedDomainName = '';\n        this.selectedProjectName = '';\n        this.selectedTeamName = '';\n        this.projectOptions = [];\n        this.teamOptions = [];\n      }\n    }\n    loadDomains(orgId) {\n      const org = this.hierarchyData.find(o => o.orgId.toString() === orgId);\n      if (org) {\n        this.domainOptions = org.domains.map(domain => ({\n          name: domain.domainName,\n          value: domain.domainId.toString()\n        }));\n      } else {\n        this.domainOptions = [];\n      }\n    }\n    onDomainSelect(event) {\n      const selectedDomainId = event.selectedOptions?.[0]?.value;\n      const selectedDomainName = event.selectedOptions?.[0]?.name;\n      if (selectedDomainId) {\n        this.selectedDomain = selectedDomainId;\n        this.selectedDomainName = selectedDomainName;\n        this.headerConfigForm.patchValue({\n          domain: selectedDomainId\n        });\n        this.loadProjects(selectedDomainId);\n        // Clear dependent dropdowns\n        this.headerConfigForm.patchValue({\n          project: '',\n          team: ''\n        });\n        this.selectedProject = '';\n        this.selectedTeam = '';\n        this.selectedProjectName = '';\n        this.selectedTeamName = '';\n        this.teamOptions = [];\n      }\n    }\n    loadProjects(domainId) {\n      const org = this.hierarchyData.find(o => o.domains.some(d => d.domainId.toString() === domainId));\n      if (org) {\n        const domain = org.domains.find(d => d.domainId.toString() === domainId);\n        if (domain) {\n          this.projectOptions = domain.projects.map(project => ({\n            name: project.projectName,\n            value: project.projectId.toString()\n          }));\n        } else {\n          this.projectOptions = [];\n        }\n      } else {\n        this.projectOptions = [];\n      }\n    }\n    onProjectSelect(event) {\n      const selectedProjectId = event.selectedOptions?.[0]?.value;\n      const selectedProjectName = event.selectedOptions?.[0]?.name;\n      if (selectedProjectId) {\n        this.selectedProject = selectedProjectId;\n        this.selectedProjectName = selectedProjectName;\n        this.headerConfigForm.patchValue({\n          project: selectedProjectId\n        });\n        this.loadTeams(selectedProjectId);\n        // Clear dependent dropdowns\n        this.headerConfigForm.patchValue({\n          team: ''\n        });\n        this.selectedTeam = '';\n        this.selectedTeamName = '';\n      }\n    }\n    loadTeams(projectId) {\n      const org = this.hierarchyData.find(o => o.domains.some(d => d.projects.some(p => p.projectId.toString() === projectId)));\n      if (org) {\n        const domain = org.domains.find(d => d.projects.some(p => p.projectId.toString() === projectId));\n        if (domain) {\n          const project = domain.projects.find(p => p.projectId.toString() === projectId);\n          if (project) {\n            this.teamOptions = project.teams.map(team => ({\n              name: team.teamName,\n              value: team.teamId.toString()\n            }));\n          } else {\n            this.teamOptions = [];\n          }\n        } else {\n          this.teamOptions = [];\n        }\n      } else {\n        this.teamOptions = [];\n      }\n    }\n    onTeamSelect(event) {\n      const selectedTeamId = event.selectedOptions?.[0]?.value;\n      const selectedTeamName = event.selectedOptions?.[0]?.name;\n      if (selectedTeamId) {\n        this.selectedTeam = selectedTeamId;\n        this.selectedTeamName = selectedTeamName;\n        this.headerConfigForm.patchValue({\n          team: selectedTeamId\n        });\n      }\n    }\n    get orgLabel() {\n      // Try to get the org name from the org_path cookie\n      const orgPath = this.tokenStorage.getCookie('org_path');\n      if (orgPath) {\n        const orgName = orgPath.split('::')[0].split('@')[0];\n        if (orgName) return orgName;\n      }\n      // Fallback to dropdown label\n      return this.orgOptions.find(o => o.value === this.selectedOrg)?.name || 'Select Organization';\n    }\n    saveOrgPathAndClose() {\n      if (this.headerConfigForm.valid) {\n        const formValue = this.headerConfigForm.value;\n        // Build the org path string\n        const orgPath = `${this.selectedOrgName}@${this.selectedDomainName}@${this.selectedProjectName}@${this.selectedTeamName}::${formValue.org}@${formValue.domain}@${formValue.project}@${formValue.team}`;\n        // Save to cookie\n        this.tokenStorage.setCookie('org_path', orgPath);\n        // Emit the change event\n        this.orgConfigChange.emit({\n          orgPath,\n          selectedValues: {\n            org: this.selectedOrg,\n            domain: this.selectedDomain,\n            project: this.selectedProject,\n            team: this.selectedTeam\n          },\n          selectedNames: {\n            org: this.selectedOrgName,\n            domain: this.selectedDomainName,\n            project: this.selectedProjectName,\n            team: this.selectedTeamName\n          }\n        });\n        this.closeOrgDialog();\n      }\n    }\n    // Navigation methods\n    navigateTo(route) {\n      // Defensive: Always close dropdowns before navigating\n      this.closeDropdownPortal();\n      this.router.navigate([route]);\n      this.navigationEvent.emit(route);\n    }\n    selectMenuItem(index) {\n      this.config.navItems.forEach((item, i) => {\n        item.selected = i === index;\n      });\n    }\n    // Toggle dropdown and optionally force close the portal\n    toggleDropdown(index, event) {\n      const clickedItem = this.config.navItems[index];\n      const isAlreadyOpen = clickedItem.dropdownOpen;\n      // Close all dropdowns first\n      this.config.navItems.forEach((item, i) => {\n        item.dropdownOpen = false;\n      });\n      if (isAlreadyOpen) {\n        this.closeDropdownPortal();\n        return;\n      }\n      // Open the clicked dropdown only\n      clickedItem.dropdownOpen = true;\n      // Set portal\n      if (event) {\n        const rect = event.currentTarget.getBoundingClientRect();\n        this.onDropdownPortalOpen({\n          rect,\n          items: clickedItem.dropdownItems || [],\n          parentLabel: clickedItem.label,\n          navItemId: clickedItem.label\n        });\n      }\n    }\n    onDropdownItemSelected(event, parentIndex) {\n      this.navigateTo(event.route);\n      this.dropdownItemSelected.emit(event);\n      this.selectMenuItem(parentIndex);\n      this.closeDropdownPortal();\n    }\n    onDropdownPortalOpen(event) {\n      this.dropdownPortal = {\n        open: true,\n        rect: event.rect,\n        items: event.items,\n        parentLabel: event.parentLabel,\n        navItemId: event.navItemId\n      };\n      //Force change detection to flush portal render and avoid \"frozen\" dropdowns\n      this.cdr.detectChanges();\n    }\n    closeDropdownPortal() {\n      this.dropdownPortal.open = false;\n      this.dropdownPortal.navItemId = '';\n      this.config.navItems.forEach(item => item.dropdownOpen = false);\n    }\n    // Profile dropdown methods\n    toggleProfileDropdown() {\n      this.profileDropdownOpen = !this.profileDropdownOpen;\n    }\n    logout() {\n      this.authService.logout();\n      this.profileAction.emit('logout');\n    }\n    // Theme methods\n    toggleTheme() {\n      if (this.themeService && this.themeService.toggleTheme) {\n        // Use the injected theme service if available\n        this.themeService.toggleTheme();\n      } else {\n        // Fallback to local theme toggle\n        const newTheme = this.currentTheme === 'light' ? 'dark' : 'light';\n        this.currentTheme = newTheme;\n        this.updateThemeAssets();\n        this.themeToggle.emit(newTheme);\n      }\n    }\n    // Language methods\n    switchLanguage(languageCode) {\n      this.currentLanguage = languageCode;\n      this.languageChange.emit(languageCode);\n    }\n    getCurrentLanguageName() {\n      const language = this.config.availableLanguages?.find(lang => lang.code === this.currentLanguage);\n      return language?.name || 'English';\n    }\n    // App drawer methods\n    toggleAppDrawer() {\n      this.isAppDrawerOpen = !this.isAppDrawerOpen;\n      if (this.isAppDrawerOpen) {\n        // Calculate position to prevent overflow\n        setTimeout(() => {\n          this.calculateAppDrawerPosition();\n        });\n      }\n    }\n    closeAppDrawer() {\n      this.isAppDrawerOpen = false;\n    }\n    navigateToApp(app) {\n      if (app.route.startsWith('http')) {\n        // External URL\n        window.open(app.route, '_blank');\n      } else {\n        // Internal route\n        this.router.navigate([app.route]);\n      }\n      this.closeAppDrawer();\n      this.navigationEvent.emit(app.route);\n    }\n    getFilteredApps() {\n      if (!this.config.availableApps) return [];\n      // Filter out the current app\n      return this.config.availableApps.filter(app => app.name !== this.config.currentApp);\n    }\n    // Organization selector methods (if enabled)\n    toggleOrgDialog() {\n      if (this.config.showOrgSelector) {\n        this.isOrgDialogOpen = !this.isOrgDialogOpen;\n        if (this.isOrgDialogOpen) {\n          // Calculate popover position\n          setTimeout(() => {\n            this.calculatePopoverPosition();\n          });\n        }\n      }\n    }\n    closeOrgDialog() {\n      this.isOrgDialogOpen = false;\n    }\n    calculatePopoverPosition() {\n      if (this.orgPathTrigger && this.popoverRef) {\n        const triggerRect = this.orgPathTrigger.nativeElement.getBoundingClientRect();\n        const popoverRect = this.popoverRef.nativeElement.getBoundingClientRect();\n        const viewportWidth = window.innerWidth;\n        // Check if popover would overflow on the right\n        if (triggerRect.left + popoverRect.width > viewportWidth - 20) {\n          this.popoverAlign = 'right';\n        } else {\n          this.popoverAlign = 'left';\n        }\n      }\n    }\n    calculateAppDrawerPosition() {\n      // Find the app drawer dropdown element\n      const appDrawerDropdown = this.elementRef.nativeElement.querySelector('.app-drawer-dropdown');\n      if (appDrawerDropdown) {\n        const dropdownRect = appDrawerDropdown.getBoundingClientRect();\n        const viewportWidth = window.innerWidth;\n        // If dropdown would overflow on the right, adjust position\n        if (dropdownRect.right > viewportWidth - 20) {\n          const overflow = dropdownRect.right - viewportWidth + 20;\n          appDrawerDropdown.style.right = `${overflow}px`;\n        } else {\n          appDrawerDropdown.style.right = '0px';\n        }\n      }\n    }\n    // Navigation item click handler\n    onNavItemClick(item, index, event) {\n      if (item.disabled) {\n        event.preventDefault();\n        event.stopPropagation();\n        return;\n      }\n      if (item.hasDropdown && item.dropdownItems) {\n        event.stopPropagation();\n        this.toggleDropdown(index, event); //Only one place toggles\n      } else {\n        this.navigateTo(item.route);\n        this.selectMenuItem(index);\n      }\n    }\n    // Route-based active menu item update\n    updateActiveMenuItemByRoute(url) {\n      // Reset all selections\n      this.config.navItems.forEach(item => {\n        item.selected = false;\n      });\n      // Find the matching parent route or parent of a child route\n      const parentItem = this.config.navItems.find(item => {\n        // Check if this is a direct match for the parent route\n        if (url === item.route) {\n          return true;\n        }\n        // Check if this is a dropdown parent with a matching child\n        if (item.hasDropdown && item.dropdownItems) {\n          // Check if the URL starts with the parent route path (for nested routes)\n          // OR if any child route exactly matches the URL\n          return url.startsWith(item.route + '/') || item.dropdownItems.some(child => url === child.route);\n        }\n        // Even if hasDropdown is false, check for dropdownItems\n        if (!item.hasDropdown && item.dropdownItems) {\n          return item.dropdownItems.some(child => url === child.route);\n        }\n        return false;\n      });\n      if (parentItem) {\n        parentItem.selected = true;\n      } else {\n        // Default to first non-disabled item if no match found\n        const defaultItem = this.config.navItems.find(item => !item.disabled);\n        if (defaultItem) {\n          defaultItem.selected = true;\n        }\n      }\n    }\n    // Document click listener to close dropdowns\n    onDocumentClick(event) {\n      const target = event.target;\n      // Close profile dropdown if clicking outside\n      if (this.profileDropdownOpen && !this.elementRef.nativeElement.contains(target)) {\n        this.profileDropdownOpen = false;\n      }\n      // Close org dialog if clicking outside\n      if (this.isOrgDialogOpen && !target.closest('.org-path-dropdown-container')) {\n        this.closeOrgDialog();\n      }\n      // Close app drawer if clicking outside\n      if (this.isAppDrawerOpen && !target.closest('.app-drawer-container')) {\n        this.closeAppDrawer();\n      }\n      //SAFELY close dropdowns only if the click is fully outside nav items + dropdown menu\n      const path = event.composedPath?.() || [];\n      const clickedInsideDropdown = path.some(el => el?.classList?.contains?.('dropdown-portal-menu'));\n      const clickedInsideNavItem = path.some(el => el?.classList?.contains?.('nav-item-wrapper'));\n      if (this.dropdownPortal.open && !clickedInsideDropdown && !clickedInsideNavItem) {\n        this.closeDropdownPortal();\n      }\n    }\n    static ɵfac = function SharedAppHeaderComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SharedAppHeaderComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i2.TokenStorageService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.FormBuilder));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SharedAppHeaderComponent,\n      selectors: [[\"shared-app-header\"]],\n      viewQuery: function SharedAppHeaderComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.orgPathTrigger = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.popoverRef = _t.first);\n        }\n      },\n      hostBindings: function SharedAppHeaderComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function SharedAppHeaderComponent_click_HostBindingHandler($event) {\n            return ctx.onDocumentClick($event);\n          }, false, i0.ɵɵresolveDocument);\n        }\n      },\n      inputs: {\n        config: \"config\",\n        customNavItemComponent: \"customNavItemComponent\",\n        orgConfigService: \"orgConfigService\",\n        themeService: \"themeService\"\n      },\n      outputs: {\n        navigationEvent: \"navigationEvent\",\n        dropdownItemSelected: \"dropdownItemSelected\",\n        profileAction: \"profileAction\",\n        themeToggle: \"themeToggle\",\n        languageChange: \"languageChange\",\n        orgConfigChange: \"orgConfigChange\"\n      },\n      decls: 2,\n      vars: 2,\n      consts: [[\"orgPathTrigger\", \"\"], [\"popover\", \"\"], [4, \"ngIf\"], [\"class\", \"dropdown-portal-menu\", 3, \"top\", \"left\", 4, \"ngIf\"], [\"width\", \"0\", \"height\", \"0\", 2, \"position\", \"absolute\"], [\"id\", \"headerClip\", \"clipPathUnits\", \"objectBoundingBox\"], [\"d\", \"\\n          M 0.03,0 \\n          L 0.97,0 \\n          L 0.95,0.71 \\n          Q 0.939,1    0.91,1 \\n          L 0.09,1 \\n          Q 0.061,1    0.05,0.69 \\n          Z\"], [\"theme\", \"light\", \"id\", \"main-header\"], [\"left-content\", \"\"], [1, \"animated-logo-container\", 3, \"mouseenter\", \"mouseleave\"], [1, \"header-logo\", \"animated-logo\", 3, \"src\", \"alt\"], [\"class\", \"studio-indicators\", 4, \"ngIf\"], [\"center-content\", \"\"], [1, \"header-wrapper\"], [1, \"header-shadow\"], [1, \"nav-menu\"], [1, \"nav-items\"], [\"class\", \"nav-item-wrapper\", 3, \"label\", \"route\", \"selected\", \"hasDropdown\", \"dropdownOpen\", \"dropdownItems\", \"icon\", \"disabled\", \"toggleDropdownEvent\", \"navigateEvent\", \"selectEvent\", \"dropdownItemSelected\", \"dropdownPortalOpen\", 4, \"ngFor\", \"ngForOf\"], [\"right-content\", \"\", 1, \"user-info-container\"], [\"class\", \"org-path-dropdown-container\", 4, \"ngIf\"], [\"class\", \"app-drawer-container\", 4, \"ngIf\"], [\"class\", \"theme-toggle\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"profile-container\", 4, \"ngIf\"], [1, \"studio-indicators\"], [\"class\", \"studio-dot\", 3, \"active\", 4, \"ngFor\", \"ngForOf\"], [1, \"studio-dot\"], [1, \"nav-item-wrapper\", 3, \"toggleDropdownEvent\", \"navigateEvent\", \"selectEvent\", \"dropdownItemSelected\", \"dropdownPortalOpen\", \"label\", \"route\", \"selected\", \"hasDropdown\", \"dropdownOpen\", \"dropdownItems\", \"icon\", \"disabled\"], [1, \"org-path-dropdown-container\"], [1, \"org-path-trigger\", 3, \"click\"], [1, \"org-icon\"], [\"src\", \"header-ascendion-logo.svg\", \"alt\", \"Organization Logo\", \"width\", \"40\", \"height\", \"40\"], [1, \"org-dropdown-arrow\"], [\"width\", \"16\", \"height\", \"16\", \"viewBox\", \"0 0 12 12\", \"fill\", \"none\"], [\"d\", \"M2.5 4L6 7.5L9.5 4\", \"stroke\", \"currentColor\", \"stroke-width\", \"1.5\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"class\", \"org-path-popover\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"org-path-backdrop\", 3, \"click\", 4, \"ngIf\"], [1, \"org-path-popover\", 3, \"ngClass\"], [3, \"formGroup\"], [1, \"filter-config-title\"], [1, \"dropdown-row-vertical\"], [1, \"filter-label\", \"required\"], [3, \"selectionChange\", \"dropdownTitle\", \"options\", \"selectedValue\", \"disabled\", \"search\", \"enableSearch\"], [1, \"popover-actions\"], [\"label\", \"Cancel\", \"variant\", \"secondary\", \"size\", \"medium\", 3, \"userClick\"], [\"label\", \"Apply\", \"variant\", \"primary\", \"size\", \"medium\", 3, \"userClick\", \"disabled\"], [1, \"org-path-backdrop\", 3, \"click\"], [1, \"app-drawer-container\"], [1, \"app-drawer-trigger\", 3, \"click\"], [\"iconName\", \"layout-grid\"], [\"class\", \"app-drawer-backdrop\", 3, \"click\", 4, \"ngIf\"], [1, \"app-drawer-dropdown\"], [1, \"app-drawer-content\"], [1, \"app-drawer-grid\"], [\"class\", \"app-drawer-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"app-drawer-backdrop\", 3, \"click\"], [1, \"app-drawer-item\", 3, \"click\"], [1, \"app-icon\"], [3, \"src\", \"alt\"], [1, \"app-info\"], [1, \"app-name\"], [\"class\", \"app-description\", 4, \"ngIf\"], [1, \"app-description\"], [1, \"theme-toggle\", 3, \"click\"], [\"alt\", \"Toggle Theme\", \"width\", \"24\", \"height\", \"24\", 3, \"src\"], [1, \"profile-container\"], [1, \"profile-trigger\", 3, \"click\"], [\"alt\", \"User Profile\", 1, \"profile-avatar\", 3, \"src\"], [1, \"profile-dropdown\"], [1, \"profile-dropdown-content\"], [1, \"profile-user-info\"], [1, \"profile-avatar-large\"], [\"alt\", \"User Profile\", 3, \"src\"], [1, \"profile-details\"], [1, \"profile-name\"], [\"class\", \"profile-designation\", 4, \"ngIf\"], [\"class\", \"profile-email\", 4, \"ngIf\"], [1, \"profile-divider\"], [\"class\", \"profile-section\", 4, \"ngIf\"], [1, \"profile-actions\"], [1, \"profile-action-item\", \"logout-btn\", 3, \"click\"], [\"width\", \"16\", \"height\", \"16\", \"viewBox\", \"0 0 24 24\", \"fill\", \"none\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\"], [\"d\", \"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4\"], [\"points\", \"16,17 21,12 16,7\"], [\"x1\", \"21\", \"y1\", \"12\", \"x2\", \"9\", \"y2\", \"12\"], [1, \"profile-designation\"], [1, \"profile-email\"], [1, \"profile-section\"], [1, \"profile-section-header\"], [1, \"section-title\"], [1, \"theme-toggle-container\"], [1, \"theme-option\", 3, \"click\"], [\"cx\", \"12\", \"cy\", \"12\", \"r\", \"5\"], [\"d\", \"M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42\"], [\"d\", \"M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z\"], [1, \"language-options\"], [\"class\", \"language-option\", 3, \"active\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"language-option\", 3, \"click\"], [1, \"dropdown-portal-menu\"], [1, \"dropdown-menu\"], [\"class\", \"dropdown-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"dropdown-item\", 3, \"click\"], [\"alt\", \"\", \"class\", \"dropdown-icon\", 3, \"src\", 4, \"ngIf\"], [1, \"dropdown-content\"], [1, \"dropdown-label\"], [1, \"dropdown-description\"], [\"alt\", \"\", 1, \"dropdown-icon\", 3, \"src\"]],\n      template: function SharedAppHeaderComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, SharedAppHeaderComponent_div_0_Template, 21, 14, \"div\", 2)(1, SharedAppHeaderComponent_div_1_Template, 3, 5, \"div\", 3);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoginPage);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.dropdownPortal.open && ctx.dropdownPortal.rect);\n        }\n      },\n      dependencies: [HeaderComponent, CommonModule, i5.NgClass, i5.NgForOf, i5.NgIf, SharedNavItemComponent, DropdownComponent, ButtonComponent, ReactiveFormsModule, i4.ɵNgNoValidate, i4.NgControlStatusGroup, i4.FormGroupDirective, IconComponent],\n      styles: [\"[_nghost-%COMP%] {\\n  --header-bg: #ffffff;\\n  --header-text: #374151;\\n  --header-border: rgba(0, 0, 0, 0.08);\\n  --nav-bg: #ffffff;\\n  --nav-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  --nav-text: #000000;\\n  --nav-arrow: #222222;\\n  --dropdown-bg: #ffffff;\\n  --dropdown-text: #374151;\\n  --dropdown-border: #e5e7eb;\\n  --dropdown-hover: #f3f4f6;\\n  display: block;\\n  padding: 1.5rem 0 0 0;\\n  margin: 0;\\n  width: 100%;\\n  position: relative;\\n}\\n.theme-dark[_nghost-%COMP%], .dark-theme   [_nghost-%COMP%] {\\n  --header-bg: #1f2937;\\n  --header-text: #f9fafb;\\n  --header-border: rgba(255, 255, 255, 0.1);\\n  --nav-bg: #374151;\\n  --nav-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);\\n  --nav-text: #ffffff;\\n  --nav-arrow: #d1d5db;\\n  --dropdown-bg: #374151;\\n  --dropdown-text: #f9fafb;\\n  --dropdown-border: #4b5563;\\n  --dropdown-hover: #4b5563;\\n}\\n@media (min-width: 1200px) {\\n  [_nghost-%COMP%] {\\n    padding-top: 0;\\n  }\\n}\\n@media (min-width: 1400px) {\\n  [_nghost-%COMP%] {\\n    padding-top: 0;\\n  }\\n}\\n\\n  .outer-box.light {\\n  background-color: transparent !important;\\n  box-shadow: none !important;\\n  margin: 0 !important;\\n  min-height: auto !important;\\n  width: 100% !important;\\n}\\n\\n  awe-header {\\n  width: 100% !important;\\n  margin: 0 !important;\\n  padding: 0 !important;\\n  background: var(--header-bg) !important;\\n  color: var(--header-text) !important;\\n  border-bottom: 1px solid var(--header-border) !important;\\n}\\n  awe-header .header-content {\\n  display: flex !important;\\n  align-items: center !important;\\n  justify-content: space-between !important;\\n  width: 100% !important;\\n  height: 60px !important;\\n  min-height: 60px !important;\\n  padding: 0 1rem !important;\\n  box-sizing: border-box !important;\\n}\\n@media (max-width: 768px) {\\n    awe-header .header-content {\\n    height: 56px !important;\\n    min-height: 56px !important;\\n    padding: 0 0.5rem !important;\\n  }\\n}\\n  awe-header [left-content] {\\n  flex: 0 0 auto !important;\\n  width: 240px !important;\\n  height: 60px !important;\\n  display: flex !important;\\n  align-items: center !important;\\n  justify-content: flex-start !important;\\n  overflow: hidden !important;\\n  z-index: 10 !important;\\n}\\n@media (max-width: 1200px) {\\n    awe-header [left-content] {\\n    width: 220px !important;\\n  }\\n}\\n@media (max-width: 768px) {\\n    awe-header [left-content] {\\n    width: 200px !important;\\n    height: 56px !important;\\n  }\\n}\\n@media (max-width: 480px) {\\n    awe-header [left-content] {\\n    width: 180px !important;\\n  }\\n}\\n  awe-header [center-content] {\\n  flex: 1 1 auto !important;\\n  display: flex !important;\\n  justify-content: center !important;\\n  align-items: center !important;\\n  overflow: visible !important;\\n  margin: 0 2rem !important;\\n}\\n@media (max-width: 1400px) {\\n    awe-header [center-content] {\\n    margin: 0 1.5rem !important;\\n  }\\n}\\n@media (max-width: 1200px) {\\n    awe-header [center-content] {\\n    margin: 0 1rem !important;\\n  }\\n}\\n@media (max-width: 768px) {\\n    awe-header [center-content] {\\n    margin: 0 0.5rem !important;\\n    height: 56px !important;\\n  }\\n}\\n@media (max-width: 480px) {\\n    awe-header [center-content] {\\n    margin: 0 0.25rem !important;\\n  }\\n}\\n  awe-header [right-content] {\\n  flex: 0 0 auto !important;\\n  width: 100% !important;\\n  height: 60px !important;\\n  display: flex !important;\\n  align-items: center !important;\\n  justify-content: flex-end !important;\\n  overflow: visible !important;\\n}\\n@media (max-width: 1200px) {\\n    awe-header [right-content] {\\n    width: 260px !important;\\n  }\\n}\\n@media (max-width: 768px) {\\n    awe-header [right-content] {\\n    width: 240px !important;\\n    height: 56px !important;\\n  }\\n}\\n@media (max-width: 480px) {\\n    awe-header [right-content] {\\n    width: 200px !important;\\n  }\\n}\\n\\n.header-wrapper[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  justify-content: center;\\n  align-items: flex-start;\\n  background: transparent;\\n  box-shadow: none;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\n.header-shadow[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  width: fit-content !important;\\n  height: 60px;\\n  background: #215ad6;\\n  border-radius: 0 0 55px 55px;\\n  filter: blur(10px);\\n  opacity: 0.18;\\n  z-index: 1;\\n  pointer-events: none;\\n}\\n@media (max-width: 1200px) {\\n  .header-shadow[_ngcontent-%COMP%] {\\n    width: min(700px, 100vw - 320px);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .header-shadow[_ngcontent-%COMP%] {\\n    width: min(500px, 100vw - 240px);\\n  }\\n}\\n@media (max-width: 480px) {\\n  .header-shadow[_ngcontent-%COMP%] {\\n    width: min(300px, 100vw - 180px);\\n  }\\n}\\n\\n.header-logo[_ngcontent-%COMP%] {\\n  max-height: 40px !important;\\n  max-width: 160px !important;\\n  width: auto !important;\\n  height: auto !important;\\n  padding: 0 0.5rem !important;\\n  margin: 0 !important;\\n}\\n@media (min-width: 1200px) {\\n  .header-logo[_ngcontent-%COMP%] {\\n    max-height: 50px !important;\\n    padding: 0 !important;\\n  }\\n}\\n@media (min-width: 1400px) {\\n  .header-logo[_ngcontent-%COMP%] {\\n    max-height: 40px !important;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .header-logo[_ngcontent-%COMP%] {\\n    max-height: 28px !important;\\n    padding: 0 0.25rem !important;\\n  }\\n}\\n\\n.animated-logo-container[_ngcontent-%COMP%] {\\n  min-width: 180px;\\n  max-width: 200px;\\n  height: 80px;\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n  justify-content: flex-start;\\n  cursor: pointer;\\n  perspective: 1000px;\\n  overflow: hidden;\\n}\\n@media (max-width: 1200px) {\\n  .animated-logo-container[_ngcontent-%COMP%] {\\n    min-width: 160px;\\n    max-width: 180px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .animated-logo-container[_ngcontent-%COMP%] {\\n    min-width: 140px;\\n    max-width: 160px;\\n    height: 56px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .animated-logo-container[_ngcontent-%COMP%] {\\n    min-width: 120px;\\n    max-width: 140px;\\n  }\\n}\\n.animated-logo-container[_ngcontent-%COMP%]   .animated-logo[_ngcontent-%COMP%] {\\n  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);\\n  transform: scale(1);\\n  opacity: 1;\\n  filter: brightness(1) saturate(1) blur(0px);\\n  transform-style: preserve-3d;\\n}\\n.animated-logo-container[_ngcontent-%COMP%]   .animated-logo[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.02);\\n  filter: brightness(1.05) saturate(1.1) blur(0px);\\n  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\n}\\n.animated-logo-container[_ngcontent-%COMP%]:hover::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: -4px;\\n  left: -4px;\\n  right: -4px;\\n  bottom: -4px;\\n  background: linear-gradient(45deg, rgba(59, 130, 246, 0.15), rgba(139, 92, 246, 0.1), rgba(59, 130, 246, 0.15));\\n  border-radius: 12px;\\n  filter: blur(12px);\\n  opacity: 0;\\n  animation: _ngcontent-%COMP%_logoGlow 0.4s ease-in-out forwards;\\n  z-index: -1;\\n  pointer-events: none;\\n}\\n\\n@keyframes _ngcontent-%COMP%_logoFadeTransition {\\n  0% {\\n    opacity: 1;\\n    transform: scale(1);\\n  }\\n  50% {\\n    opacity: 0;\\n    transform: scale(0.95);\\n  }\\n  100% {\\n    opacity: 1;\\n    transform: scale(1);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_logoSmoothFade {\\n  0% {\\n    opacity: 1;\\n    transform: scale(1) translateY(0);\\n  }\\n  50% {\\n    opacity: 0;\\n    transform: scale(0.98) translateY(-2px);\\n  }\\n  100% {\\n    opacity: 1;\\n    transform: scale(1) translateY(0);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_logoCrossfade {\\n  0% {\\n    opacity: 1;\\n    filter: brightness(1) blur(0px);\\n  }\\n  25% {\\n    opacity: 0.7;\\n    filter: brightness(1.1) blur(0.5px);\\n  }\\n  50% {\\n    opacity: 0;\\n    filter: brightness(1.2) blur(1px);\\n  }\\n  75% {\\n    opacity: 0.7;\\n    filter: brightness(1.1) blur(0.5px);\\n  }\\n  100% {\\n    opacity: 1;\\n    filter: brightness(1) blur(0px);\\n  }\\n}\\n.animated-logo.logo-transitioning.logo-fade[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_logoFadeTransition 1.2s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.animated-logo.logo-transitioning.logo-smooth[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_logoSmoothFade 1s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\n}\\n.animated-logo.logo-transitioning.logo-crossfade[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_logoCrossfade 1.4s cubic-bezier(0.23, 1, 0.32, 1);\\n}\\n\\n@keyframes _ngcontent-%COMP%_logoGlow {\\n  0% {\\n    opacity: 0;\\n    transform: scale(0.8);\\n  }\\n  100% {\\n    opacity: 1;\\n    transform: scale(1);\\n  }\\n}\\n.animated-logo-container[_ngcontent-%COMP%]::after {\\n  content: attr(data-studio);\\n  position: absolute;\\n  bottom: -20px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  font-size: 10px;\\n  font-weight: 600;\\n  color: var(--nav-arrow);\\n  opacity: 0;\\n  transition: opacity 0.2s ease;\\n  pointer-events: none;\\n  white-space: nowrap;\\n}\\n\\n.animated-logo-container[_ngcontent-%COMP%]:hover::after {\\n  opacity: 0.7;\\n}\\n\\n.studio-indicators[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: -30px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  display: flex;\\n  gap: 4px;\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n.studio-indicators[_ngcontent-%COMP%]   .studio-dot[_ngcontent-%COMP%] {\\n  width: 6px;\\n  height: 6px;\\n  border-radius: 50%;\\n  background: var(--nav-arrow);\\n  opacity: 0.4;\\n  transition: all 0.3s ease;\\n}\\n.studio-indicators[_ngcontent-%COMP%]   .studio-dot.active[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  background: linear-gradient(135deg, #8b5cf6, #ec4899);\\n  transform: scale(1.3);\\n}\\n\\n.animated-logo-container[_ngcontent-%COMP%]:hover   .studio-indicators[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n\\n@media (max-width: 768px) {\\n  .animated-logo-container[_ngcontent-%COMP%]   .animated-logo[_ngcontent-%COMP%]:hover {\\n    transform: scale(1.02);\\n  }\\n  .animated-logo-container[_ngcontent-%COMP%]   .animated-logo.logo-transitioning[_ngcontent-%COMP%] {\\n    transform: scale(0.98);\\n  }\\n  .animated-logo-container[_ngcontent-%COMP%]::after {\\n    display: none;\\n  }\\n  .animated-logo-container[_ngcontent-%COMP%]:hover::before {\\n    display: none;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .animated-logo-container[_ngcontent-%COMP%]   .animated-logo[_ngcontent-%COMP%] {\\n    transition: all 0.2s ease;\\n  }\\n}\\n.nav-menu[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 2;\\n  width: 100%;\\n  height: 100%;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n@media (max-width: 1200px) {\\n  .nav-menu[_ngcontent-%COMP%] {\\n    padding: 0 16px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .nav-menu[_ngcontent-%COMP%] {\\n    padding: 0 12px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .nav-menu[_ngcontent-%COMP%] {\\n    padding: 0 8px;\\n  }\\n}\\n\\n.nav-items[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  height: auto;\\n  margin: 0px 0px 20px 0px;\\n  background: var(--nav-bg);\\n  border-radius: 0 0 40px 40px;\\n  min-width: fit-content;\\n  width: max-content;\\n  max-width: calc(100vw - 560px);\\n  min-height: 56px;\\n  gap: 12px;\\n  padding: 12px 24px;\\n  box-shadow: var(--nav-shadow) !important;\\n  border: 1px solid var(--header-border) !important;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n@media (max-width: 1400px) {\\n  .nav-items[_ngcontent-%COMP%] {\\n    max-width: calc(100vw - 500px);\\n  }\\n}\\n@media (max-width: 1200px) {\\n  .nav-items[_ngcontent-%COMP%] {\\n    max-width: calc(100vw - 480px);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .nav-items[_ngcontent-%COMP%] {\\n    max-width: calc(100vw - 440px);\\n    padding: 10px 20px;\\n    gap: 10px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .nav-items[_ngcontent-%COMP%] {\\n    max-width: calc(100vw - 380px);\\n    padding: 8px 16px;\\n    gap: 8px;\\n  }\\n}\\n.nav-items[_ngcontent-%COMP%]:has(:only-child) {\\n  min-width: 240px;\\n}\\n.nav-items.single-item[_ngcontent-%COMP%] {\\n  min-width: 240px;\\n}\\n.nav-items[_ngcontent-%COMP%]:has(:nth-child(6)) {\\n  padding: 10px 20px;\\n  gap: 10px;\\n}\\n.nav-items[_ngcontent-%COMP%]:has(:nth-child(8)) {\\n  padding: 8px 18px;\\n  gap: 8px;\\n}\\n.nav-items.many-items[_ngcontent-%COMP%] {\\n  padding: 0;\\n  gap: 0;\\n}\\n.nav-items.very-many-items[_ngcontent-%COMP%] {\\n  padding: 8px 18px;\\n  gap: 8px;\\n}\\n@media (max-width: 1400px) {\\n  .nav-items[_ngcontent-%COMP%] {\\n    max-width: calc(100vw - 312px);\\n    padding: 10px 20px;\\n    gap: 10px;\\n  }\\n}\\n@media (max-width: 1200px) {\\n  .nav-items[_ngcontent-%COMP%] {\\n    max-width: calc(100vw - 272px);\\n    padding: 8px 16px;\\n    gap: 8px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .nav-items[_ngcontent-%COMP%] {\\n    max-width: calc(100vw - 224px);\\n    padding: 8px 12px;\\n    gap: 6px;\\n    min-height: 48px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .nav-items[_ngcontent-%COMP%] {\\n    max-width: calc(100vw - 166px);\\n    padding: 6px 10px;\\n    border-radius: 0 0 24px 24px;\\n    flex-wrap: wrap;\\n    justify-content: center;\\n  }\\n}\\n\\n.nav-item-wrapper[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 5;\\n}\\n\\n  shared-nav-item .nav-item-container {\\n  position: relative;\\n}\\n  shared-nav-item .nav-item {\\n  display: flex;\\n  align-items: center;\\n  font-size: 16px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  padding: 8px;\\n  border-radius: 24px;\\n  color: #64748b;\\n  background: transparent;\\n  border: none;\\n  white-space: nowrap;\\n  min-height: 40px;\\n  position: relative;\\n  overflow: visible;\\n  margin: 2px;\\n}\\n@media (max-width: 1400px) {\\n    shared-nav-item .nav-item {\\n    padding: 9px 14px;\\n    font-size: 15px;\\n    gap: 6px;\\n  }\\n}\\n@media (max-width: 1200px) {\\n    shared-nav-item .nav-item {\\n    padding: 8px 12px;\\n    font-size: 14px;\\n    gap: 6px;\\n    min-height: 36px;\\n  }\\n}\\n@media (max-width: 768px) {\\n    shared-nav-item .nav-item {\\n    padding: 6px 10px;\\n    font-size: 13px;\\n    gap: 4px;\\n    min-height: 32px;\\n    border-radius: 16px;\\n    margin: 1px;\\n  }\\n}\\n@media (max-width: 480px) {\\n    shared-nav-item .nav-item {\\n    padding: 4px 8px;\\n    font-size: 12px;\\n    gap: 3px;\\n    min-height: 28px;\\n    border-radius: 12px;\\n    margin: 1px;\\n  }\\n}\\n  shared-nav-item .nav-item.selected {\\n  color: var(--rgb-brand-primary);\\n  font-weight: 600;\\n}\\n  shared-nav-item .nav-item.selected .nav-icon {\\n  filter: brightness(0) invert(1);\\n}\\n  shared-nav-item .nav-item.selected .dropdown-arrow svg {\\n  color: var(--rgb-brand-primary);\\n}\\n  shared-nav-item .nav-item.disabled {\\n  opacity: 0.4;\\n  cursor: not-allowed;\\n  pointer-events: none;\\n  color: #94a3b8;\\n}\\n  shared-nav-item .nav-item:focus-visible {\\n  outline: 2px solid #3b82f6;\\n  outline-offset: 2px;\\n}\\n  shared-nav-item .nav-item .item-icon {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 20px;\\n  height: 20px;\\n  transition: all 0.3s ease;\\n}\\n@media (max-width: 768px) {\\n    shared-nav-item .nav-item .item-icon {\\n    width: 18px;\\n    height: 18px;\\n  }\\n}\\n@media (max-width: 480px) {\\n    shared-nav-item .nav-item .item-icon {\\n    width: 16px;\\n    height: 16px;\\n  }\\n}\\n  shared-nav-item .nav-item .nav-icon {\\n  width: 20px;\\n  height: 20px;\\n  object-fit: contain;\\n  transition: all 0.3s ease;\\n}\\n@media (max-width: 768px) {\\n    shared-nav-item .nav-item .nav-icon {\\n    width: 18px;\\n    height: 18px;\\n  }\\n}\\n@media (max-width: 480px) {\\n    shared-nav-item .nav-item .nav-icon {\\n    width: 16px;\\n    height: 16px;\\n  }\\n}\\n  shared-nav-item .nav-item .item-label {\\n  font-family: \\\"Inter\\\", \\\"Segoe UI\\\", sans-serif;\\n  font-size: 15px;\\n  font-weight: 500;\\n  letter-spacing: -0.01em;\\n  transition: all 0.3s ease;\\n}\\n@media (max-width: 1200px) {\\n    shared-nav-item .nav-item .item-label {\\n    font-size: 14px;\\n  }\\n}\\n@media (max-width: 768px) {\\n    shared-nav-item .nav-item .item-label {\\n    font-size: 13px;\\n  }\\n}\\n@media (max-width: 480px) {\\n    shared-nav-item .nav-item .item-label {\\n    font-size: 12px;\\n    letter-spacing: -0.02em;\\n  }\\n}\\n  shared-nav-item .nav-item .dropdown-arrow {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 16px;\\n  height: 16px;\\n  margin-left: 4px;\\n  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n  shared-nav-item .nav-item .dropdown-arrow.open {\\n  transform: rotate(180deg);\\n}\\n  shared-nav-item .nav-item .dropdown-arrow svg {\\n  width: 14px;\\n  height: 14px;\\n  transition: color 0.3s ease;\\n}\\n  shared-nav-item .nav-item::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  width: 0;\\n  height: 0;\\n  border-radius: 50%;\\n  background: rgba(59, 130, 246, 0.3);\\n  transition: width 0.6s, height 0.6s, top 0.6s, left 0.6s;\\n  transform: translate(-50%, -50%);\\n  z-index: -1;\\n}\\n  shared-nav-item .nav-item:active::before {\\n  width: 200px;\\n  height: 200px;\\n  top: 50%;\\n  left: 50%;\\n}\\n\\n.user-info-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  height: 60px;\\n  overflow: visible;\\n}\\n.user-info-container[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  height: 40px;\\n}\\n@media (min-width: 1200px) {\\n  .user-info-container[_ngcontent-%COMP%] {\\n    gap: 16px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .user-info-container[_ngcontent-%COMP%] {\\n    gap: 8px;\\n    height: 56px;\\n  }\\n  .user-info-container[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%] {\\n    height: 36px;\\n  }\\n}\\n\\n.org-path-dropdown-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: inline-block;\\n  z-index: 1;\\n}\\n\\n.org-path-trigger[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: flex-start;\\n  background: var(--nav-bg) !important;\\n  border-radius: 20px;\\n  padding: 8px;\\n  height: 40px;\\n  font-size: 14px;\\n  box-shadow: var(--nav-shadow) !important;\\n  cursor: pointer;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  border: 1px solid var(--header-border) !important;\\n  gap: 8px;\\n}\\n.org-path-trigger[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\\n  transform: translateY(-1px);\\n}\\n.org-path-trigger[_ngcontent-%COMP%]   .org-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-right: 8px;\\n  border-radius: 50%;\\n  background: transparent;\\n  flex-shrink: 0;\\n}\\n.org-path-trigger[_ngcontent-%COMP%]   .org-icon[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n}\\n.org-path-trigger[_ngcontent-%COMP%]   .org-label-text[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: var(--nav-text) !important;\\n  display: flex;\\n  align-items: center;\\n  flex: 1;\\n  min-width: 0;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n.org-path-trigger[_ngcontent-%COMP%]   .org-dropdown-arrow[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  transition: transform 0.3s ease;\\n  margin-left: auto;\\n}\\n.org-path-trigger[_ngcontent-%COMP%]   .org-dropdown-arrow.open[_ngcontent-%COMP%] {\\n  transform: rotate(180deg);\\n}\\n.org-path-trigger[_ngcontent-%COMP%]   .org-dropdown-arrow[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 14px;\\n  height: 14px;\\n  color: var(--nav-text) !important;\\n}\\n@media screen and (min-width: 1200px) {\\n  .org-path-trigger[_ngcontent-%COMP%]   .org-icon[_ngcontent-%COMP%] {\\n    margin-right: 10px;\\n  }\\n  .org-path-trigger[_ngcontent-%COMP%]   .org-icon[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    width: 24px;\\n    height: 24px;\\n  }\\n  .org-path-trigger[_ngcontent-%COMP%]   .org-label-text[_ngcontent-%COMP%] {\\n    font-size: 15px;\\n  }\\n  .org-path-trigger[_ngcontent-%COMP%]   .org-dropdown-arrow[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n    width: 16px;\\n    height: 16px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .org-path-trigger[_ngcontent-%COMP%] {\\n    padding: 6px 10px;\\n    height: 36px;\\n    gap: 6px;\\n  }\\n  .org-path-trigger[_ngcontent-%COMP%]   .org-icon[_ngcontent-%COMP%] {\\n    margin-right: 6px;\\n  }\\n  .org-path-trigger[_ngcontent-%COMP%]   .org-icon[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    width: 18px;\\n    height: 18px;\\n  }\\n  .org-path-trigger[_ngcontent-%COMP%]   .org-label-text[_ngcontent-%COMP%] {\\n    font-size: 13px;\\n  }\\n  .org-path-trigger[_ngcontent-%COMP%]   .org-dropdown-arrow[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n    width: 12px;\\n    height: 12px;\\n  }\\n}\\n\\n.org-path-popover[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 100%;\\n  left: 0;\\n  margin-top: 8px;\\n  z-index: 10000;\\n  background: var(--dropdown-bg) !important;\\n  border-radius: 16px;\\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15), 0 0 0 3px rgba(59, 130, 246, 0.3);\\n  border: 1px solid var(--dropdown-border) !important;\\n  padding: 24px;\\n  overflow-y: auto;\\n  overflow-x: hidden;\\n  max-height: 80vh;\\n  max-width: 400px;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: flex-start;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n}\\n.org-path-popover.right[_ngcontent-%COMP%] {\\n  left: auto;\\n  right: 0;\\n}\\n.org-path-popover.left[_ngcontent-%COMP%] {\\n  left: 0;\\n  right: auto;\\n}\\n.org-path-popover[_ngcontent-%COMP%]   .filter-config-title[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 700;\\n  margin-bottom: 20px;\\n  color: var(--dropdown-text) !important;\\n}\\n.org-path-popover[_ngcontent-%COMP%]   .dropdown-row-vertical[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n  align-items: stretch;\\n  width: 100%;\\n}\\n.org-path-popover[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-weight: 600;\\n  font-size: 14px;\\n  color: var(--dropdown-text) !important;\\n  margin-bottom: 6px;\\n}\\n.org-path-popover[_ngcontent-%COMP%]   .filter-label.required[_ngcontent-%COMP%]::after {\\n  content: \\\" *\\\";\\n  color: #ef4444;\\n  font-weight: bold;\\n}\\n.org-path-popover[_ngcontent-%COMP%]   .popover-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  justify-content: flex-end;\\n  width: 100%;\\n  margin-top: 24px;\\n}\\n@media (max-width: 768px) {\\n  .org-path-popover[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n    min-width: 300px;\\n    padding: 20px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .org-path-popover[_ngcontent-%COMP%] {\\n    max-width: 98vw;\\n    min-width: 280px;\\n    padding: 16px;\\n    margin-top: 4px;\\n  }\\n  .org-path-popover[_ngcontent-%COMP%]   .filter-config-title[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n    margin-bottom: 16px;\\n  }\\n  .org-path-popover[_ngcontent-%COMP%]   .dropdown-row-vertical[_ngcontent-%COMP%] {\\n    gap: 12px;\\n  }\\n  .org-path-popover[_ngcontent-%COMP%]   .popover-actions[_ngcontent-%COMP%] {\\n    margin-top: 20px;\\n  }\\n}\\n\\n.org-path-backdrop[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: transparent;\\n  z-index: 9999;\\n  cursor: default;\\n}\\n\\n.app-drawer-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n}\\n.app-drawer-container[_ngcontent-%COMP%]   .app-drawer-trigger[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 8px;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  color: var(--nav-arrow) !important;\\n}\\n.app-drawer-container[_ngcontent-%COMP%]   .app-drawer-trigger.active[_ngcontent-%COMP%] {\\n  background: rgba(139, 92, 246, 0.1);\\n  color: #8b5cf6;\\n}\\n.app-drawer-container[_ngcontent-%COMP%]   .app-drawer-trigger[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  display: block;\\n}\\n.app-drawer-container[_ngcontent-%COMP%]   .app-drawer-dropdown[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: calc(100% + 8px);\\n  right: 0;\\n  z-index: 1000;\\n  background: var(--dropdown-bg) !important;\\n  border-radius: 16px;\\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);\\n  border: 1px solid var(--dropdown-border) !important;\\n  width: 240px;\\n  max-width: calc(100vw - 40px);\\n  opacity: 0;\\n  visibility: hidden;\\n  transform: translateY(-10px);\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  overflow: hidden;\\n}\\n.app-drawer-container[_ngcontent-%COMP%]   .app-drawer-dropdown.visible[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  visibility: visible;\\n  transform: translateY(0);\\n}\\n.app-drawer-container[_ngcontent-%COMP%]   .app-drawer-dropdown[_ngcontent-%COMP%]   .app-drawer-content[_ngcontent-%COMP%] {\\n  padding: 0;\\n  overflow: hidden;\\n}\\n.app-drawer-container[_ngcontent-%COMP%]   .app-drawer-dropdown[_ngcontent-%COMP%]   .app-drawer-header[_ngcontent-%COMP%] {\\n  padding: 20px 24px 16px 24px;\\n  background: linear-gradient(135deg, #8b5cf6 0%, #ec4899 100%);\\n  color: white;\\n}\\n.app-drawer-container[_ngcontent-%COMP%]   .app-drawer-dropdown[_ngcontent-%COMP%]   .app-drawer-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 18px;\\n  font-weight: 700;\\n  text-align: center;\\n}\\n.app-drawer-container[_ngcontent-%COMP%]   .app-drawer-dropdown[_ngcontent-%COMP%]   .app-drawer-grid[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n  padding: 16px 20px 20px 20px;\\n}\\n.app-drawer-container[_ngcontent-%COMP%]   .app-drawer-dropdown[_ngcontent-%COMP%]   .app-drawer-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 12px;\\n  border-radius: 12px;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  border: 1px solid transparent;\\n}\\n.app-drawer-container[_ngcontent-%COMP%]   .app-drawer-dropdown[_ngcontent-%COMP%]   .app-drawer-item[_ngcontent-%COMP%]   .app-icon[_ngcontent-%COMP%] {\\n  margin-right: 12px;\\n  width: 40px;\\n  height: 40px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background: var(--dropdown-hover) !important;\\n  border-radius: 10px;\\n  flex-shrink: 0;\\n}\\n.app-drawer-container[_ngcontent-%COMP%]   .app-drawer-dropdown[_ngcontent-%COMP%]   .app-drawer-item[_ngcontent-%COMP%]   .app-icon[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n  object-fit: contain;\\n}\\n.app-drawer-container[_ngcontent-%COMP%]   .app-drawer-dropdown[_ngcontent-%COMP%]   .app-drawer-item[_ngcontent-%COMP%]   .app-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0;\\n}\\n.app-drawer-container[_ngcontent-%COMP%]   .app-drawer-dropdown[_ngcontent-%COMP%]   .app-drawer-item[_ngcontent-%COMP%]   .app-info[_ngcontent-%COMP%]   .app-name[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: var(--dropdown-text) !important;\\n  margin-bottom: 2px;\\n  word-wrap: break-word;\\n  overflow-wrap: break-word;\\n}\\n.app-drawer-container[_ngcontent-%COMP%]   .app-drawer-dropdown[_ngcontent-%COMP%]   .app-drawer-item[_ngcontent-%COMP%]   .app-info[_ngcontent-%COMP%]   .app-description[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: var(--nav-arrow) !important;\\n  line-height: 1.3;\\n  word-wrap: break-word;\\n  overflow-wrap: break-word;\\n  white-space: normal;\\n}\\n@media (max-width: 768px) {\\n  .app-drawer-container[_ngcontent-%COMP%]   .app-drawer-dropdown[_ngcontent-%COMP%] {\\n    width: 280px;\\n    max-width: calc(100vw - 20px);\\n    right: -10px;\\n  }\\n  .app-drawer-container[_ngcontent-%COMP%]   .app-drawer-dropdown[_ngcontent-%COMP%]   .app-drawer-header[_ngcontent-%COMP%] {\\n    padding: 16px 20px 12px 20px;\\n  }\\n  .app-drawer-container[_ngcontent-%COMP%]   .app-drawer-dropdown[_ngcontent-%COMP%]   .app-drawer-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n  .app-drawer-container[_ngcontent-%COMP%]   .app-drawer-dropdown[_ngcontent-%COMP%]   .app-drawer-grid[_ngcontent-%COMP%] {\\n    gap: 6px;\\n    padding: 12px 16px 16px 16px;\\n  }\\n  .app-drawer-container[_ngcontent-%COMP%]   .app-drawer-dropdown[_ngcontent-%COMP%]   .app-drawer-item[_ngcontent-%COMP%] {\\n    padding: 10px;\\n  }\\n  .app-drawer-container[_ngcontent-%COMP%]   .app-drawer-dropdown[_ngcontent-%COMP%]   .app-drawer-item[_ngcontent-%COMP%]   .app-icon[_ngcontent-%COMP%] {\\n    width: 36px;\\n    height: 36px;\\n  }\\n  .app-drawer-container[_ngcontent-%COMP%]   .app-drawer-dropdown[_ngcontent-%COMP%]   .app-drawer-item[_ngcontent-%COMP%]   .app-icon[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    width: 20px;\\n    height: 20px;\\n  }\\n  .app-drawer-container[_ngcontent-%COMP%]   .app-drawer-dropdown[_ngcontent-%COMP%]   .app-drawer-item[_ngcontent-%COMP%]   .app-info[_ngcontent-%COMP%]   .app-name[_ngcontent-%COMP%] {\\n    font-size: 13px;\\n  }\\n  .app-drawer-container[_ngcontent-%COMP%]   .app-drawer-dropdown[_ngcontent-%COMP%]   .app-drawer-item[_ngcontent-%COMP%]   .app-info[_ngcontent-%COMP%]   .app-description[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .app-drawer-container[_ngcontent-%COMP%]   .app-drawer-dropdown[_ngcontent-%COMP%] {\\n    width: 260px;\\n    max-width: calc(100vw - 10px);\\n    right: -5px;\\n  }\\n  .app-drawer-container[_ngcontent-%COMP%]   .app-drawer-dropdown[_ngcontent-%COMP%]   .app-drawer-grid[_ngcontent-%COMP%] {\\n    padding: 10px 12px 12px 12px;\\n  }\\n}\\n\\n.theme-toggle[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  cursor: pointer;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  background: rgba(255, 255, 255, 0.8);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.theme-toggle[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  transition: transform 0.3s ease;\\n}\\n.theme-toggle[_ngcontent-%COMP%]:active   img[_ngcontent-%COMP%] {\\n  transform: scale(0.95);\\n}\\n\\n.profile-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-trigger[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  border-radius: 50%;\\n  transition: all 0.2s ease;\\n  padding: 2px;\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-trigger.active[_ngcontent-%COMP%] {\\n  background: rgba(0, 0, 0, 0.1);\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-trigger[_ngcontent-%COMP%]   .profile-avatar[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  border: 2px solid rgba(255, 255, 255, 0.2);\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: calc(100% + 8px);\\n  right: 0;\\n  z-index: 1000;\\n  background: var(--dropdown-bg) !important;\\n  border-radius: 16px;\\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);\\n  border: 1px solid var(--dropdown-border) !important;\\n  min-width: 320px;\\n  max-width: 400px;\\n  opacity: 0;\\n  visibility: hidden;\\n  transform: translateY(-10px);\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown.visible[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  visibility: visible;\\n  transform: translateY(0);\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .profile-dropdown-content[_ngcontent-%COMP%] {\\n  padding: 0;\\n  overflow: hidden;\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .profile-user-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 12px;\\n  padding: 24px 24px 20px 24px;\\n  background: linear-gradient(135deg, #8b5cf6 0%, #ec4899 100%);\\n  color: white;\\n  position: relative;\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .profile-user-info[_ngcontent-%COMP%]   .profile-avatar-large[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .profile-user-info[_ngcontent-%COMP%]   .profile-avatar-large[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 56px;\\n  height: 56px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  border: 3px solid rgba(255, 255, 255, 0.3);\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .profile-user-info[_ngcontent-%COMP%]   .profile-details[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0;\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .profile-user-info[_ngcontent-%COMP%]   .profile-details[_ngcontent-%COMP%]   .profile-name[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 700;\\n  margin-bottom: 4px;\\n  color: white;\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .profile-user-info[_ngcontent-%COMP%]   .profile-details[_ngcontent-%COMP%]   .profile-designation[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  opacity: 0.9;\\n  margin-bottom: 2px;\\n  color: rgba(255, 255, 255, 0.9);\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .profile-user-info[_ngcontent-%COMP%]   .profile-details[_ngcontent-%COMP%]   .profile-email[_ngcontent-%COMP%] {\\n  font-size: 13px;\\n  opacity: 0.8;\\n  color: rgba(255, 255, 255, 0.8);\\n  word-break: break-word;\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .profile-user-info[_ngcontent-%COMP%]   .profile-close-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 16px;\\n  right: 16px;\\n  background: none;\\n  border: none;\\n  color: rgba(255, 255, 255, 0.8);\\n  cursor: pointer;\\n  padding: 4px;\\n  border-radius: 4px;\\n  transition: all 0.2s ease;\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .profile-user-info[_ngcontent-%COMP%]   .profile-close-btn[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  display: block;\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .profile-divider[_ngcontent-%COMP%] {\\n  height: 1px;\\n  background: var(--dropdown-border) !important;\\n  margin: 0;\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .profile-section[_ngcontent-%COMP%] {\\n  padding: 16px 24px;\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .profile-section[_ngcontent-%COMP%]   .profile-section-header[_ngcontent-%COMP%] {\\n  margin-bottom: 12px;\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .profile-section[_ngcontent-%COMP%]   .profile-section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: var(--dropdown-text) !important;\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .theme-toggle-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .theme-toggle-container[_ngcontent-%COMP%]   .theme-option[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 10px 12px;\\n  border: 1px solid var(--dropdown-border) !important;\\n  border-radius: 8px;\\n  background: var(--dropdown-bg) !important;\\n  color: var(--nav-arrow) !important;\\n  font-size: 14px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .theme-toggle-container[_ngcontent-%COMP%]   .theme-option.active[_ngcontent-%COMP%] {\\n  border-color: #8b5cf6;\\n  background: var(--dropdown-hover) !important;\\n  color: #8b5cf6;\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .theme-toggle-container[_ngcontent-%COMP%]   .theme-option.active[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  color: #8b5cf6;\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .theme-toggle-container[_ngcontent-%COMP%]   .theme-option[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n  color: currentColor;\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .language-options[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 4px;\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .language-options[_ngcontent-%COMP%]   .language-option[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  width: 100%;\\n  padding: 10px 12px;\\n  border: none;\\n  border-radius: 6px;\\n  background: transparent;\\n  color: var(--nav-arrow) !important;\\n  font-size: 14px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  text-align: left;\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .language-options[_ngcontent-%COMP%]   .language-option.active[_ngcontent-%COMP%] {\\n  background: rgba(139, 92, 246, 0.1) !important;\\n  color: #8b5cf6;\\n  font-weight: 600;\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .profile-actions[_ngcontent-%COMP%] {\\n  padding: 16px 24px 24px 24px;\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .profile-actions[_ngcontent-%COMP%]   .profile-action-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  width: 100%;\\n  padding: 12px 16px;\\n  border: none;\\n  border-radius: 8px;\\n  background: transparent;\\n  color: #dc2626;\\n  font-size: 14px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .profile-actions[_ngcontent-%COMP%]   .profile-action-item[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n  color: currentColor;\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .profile-actions[_ngcontent-%COMP%]   .logout-btn[_ngcontent-%COMP%] {\\n  justify-content: center;\\n  background: linear-gradient(135deg, #8b5cf6 0%, #ec4899 100%);\\n  color: white;\\n}\\n@media (max-width: 768px) {\\n  .profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%] {\\n    min-width: 280px;\\n    right: -20px;\\n  }\\n  .profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .profile-user-info[_ngcontent-%COMP%] {\\n    padding: 20px;\\n  }\\n  .profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .profile-user-info[_ngcontent-%COMP%]   .profile-avatar-large[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    width: 48px;\\n    height: 48px;\\n  }\\n  .profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .profile-user-info[_ngcontent-%COMP%]   .profile-details[_ngcontent-%COMP%]   .profile-name[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n  .profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .profile-section[_ngcontent-%COMP%] {\\n    padding: 12px 20px;\\n  }\\n  .profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .profile-actions[_ngcontent-%COMP%] {\\n    padding: 12px 20px 20px 20px;\\n  }\\n}\\n\\n.dropdown-portal-menu[_ngcontent-%COMP%] {\\n  position: fixed;\\n  z-index: 2000;\\n}\\n\\n.dropdown-menu[_ngcontent-%COMP%] {\\n  background: var(--dropdown-bg) !important;\\n  border-radius: 16px;\\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);\\n  border: 1px solid var(--dropdown-border) !important;\\n  min-width: 320px;\\n  padding: 8px;\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  margin-top: 8px;\\n}\\n\\n.dropdown-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 16px;\\n  border-radius: 12px;\\n  cursor: pointer;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.dropdown-item[_ngcontent-%COMP%]:hover {\\n  background: var(--dropdown-hover) !important;\\n  transform: translateX(2px);\\n}\\n\\n.dropdown-icon[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n  object-fit: contain;\\n  flex-shrink: 0;\\n}\\n\\n.dropdown-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.dropdown-label[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  font-size: 16px;\\n  color: var(--dropdown-text) !important;\\n  margin-bottom: 4px;\\n}\\n\\n.dropdown-description[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: var(--nav-arrow) !important;\\n  line-height: 1.5;\\n}\\n\\n@media (max-width: 900px) {\\n  .header-shadow[_ngcontent-%COMP%], \\n   .nav-items[_ngcontent-%COMP%] {\\n    width: 98vw;\\n    min-width: unset;\\n    padding: 8px 12px;\\n  }\\n}\\n@media (max-width: 600px) {\\n  .header-shadow[_ngcontent-%COMP%], \\n   .nav-items[_ngcontent-%COMP%] {\\n    width: 100vw;\\n    min-width: unset;\\n    padding: 6px 8px;\\n    gap: 4px;\\n  }\\n  .nav-items[_ngcontent-%COMP%] {\\n    min-height: 48px;\\n  }\\n    shared-nav-item {\\n    flex: 1 1 auto;\\n    min-width: 0;\\n  }\\n    shared-nav-item .nav-item {\\n    padding: 8px 12px;\\n    font-size: 14px;\\n    border-radius: 20px;\\n    white-space: nowrap;\\n    overflow: hidden;\\n    text-overflow: ellipsis;\\n  }\\n    shared-nav-item .nav-item .item-label {\\n    font-size: 14px;\\n    white-space: nowrap;\\n    overflow: hidden;\\n    text-overflow: ellipsis;\\n  }\\n}\\n@media (max-width: 768px) {\\n    shared-nav-item .nav-item {\\n    padding: 6px 10px;\\n    font-size: 13px;\\n  }\\n    shared-nav-item .nav-item .item-label {\\n    font-size: 13px;\\n  }\\n  .user-info-container[_ngcontent-%COMP%] {\\n    gap: 8px;\\n  }\\n}\\n@media (max-width: 480px) {\\n    shared-nav-item .nav-item {\\n    padding: 4px 8px;\\n    font-size: 12px;\\n  }\\n    shared-nav-item .nav-item .item-label {\\n    font-size: 12px;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_fadeInUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(10px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.nav-items[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n@media (max-width: 1200px) {\\n    #main-header .col-auto {\\n    width: 16% !important;\\n    padding: 0 !important;\\n  }\\n    #main-header .center-content-wrapper {\\n    width: 68% !important;\\n    padding: 0 !important;\\n  }\\n}\\n@media (min-width: 1201px) {\\n    #main-header .col-auto {\\n    width: 16% !important;\\n    padding: 0 !important;\\n  }\\n    #main-header .center-content-wrapper {\\n    width: 68% !important;\\n    padding: 0 !important;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return SharedAppHeaderComponent;\n})();", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "HeaderComponent", "NavigationEnd", "NavigationStart", "filter", "Subscription", "DropdownComponent", "IconComponent", "ButtonComponent", "SharedNavItemComponent", "Validators", "ReactiveFormsModule", "i0", "ɵɵelement", "ɵɵclassProp", "i_r3", "ctx_r1", "currentLogoIndex", "ɵɵelementStart", "ɵɵtemplate", "SharedAppHeaderComponent_div_0_div_9_div_1_Template", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "studioLogos", "ɵɵlistener", "SharedAppHeaderComponent_div_0_shared_nav_item_15_Template_shared_nav_item_toggleDropdownEvent_0_listener", "i_r5", "ɵɵrestoreView", "_r4", "index", "ɵɵnextContext", "ɵɵresetView", "toggleDropdown", "SharedAppHeaderComponent_div_0_shared_nav_item_15_Template_shared_nav_item_navigateEvent_0_listener", "$event", "navigateTo", "SharedAppHeaderComponent_div_0_shared_nav_item_15_Template_shared_nav_item_selectEvent_0_listener", "selectMenuItem", "SharedAppHeaderComponent_div_0_shared_nav_item_15_Template_shared_nav_item_dropdownItemSelected_0_listener", "onDropdownItemSelected", "SharedAppHeaderComponent_div_0_shared_nav_item_15_Template_shared_nav_item_dropdownPortalOpen_0_listener", "onDropdownPortalOpen", "item_r6", "label", "route", "selected", "hasDropdown", "dropdownOpen", "dropdownItems", "ɵɵpureFunction0", "_c2", "icon", "disabled", "ɵɵtext", "SharedAppHeaderComponent_div_0_div_17_div_8_Template_ava_dropdown_selectionChange_8_listener", "_r8", "onOrgSelect", "SharedAppHeaderComponent_div_0_div_17_div_8_Template_ava_dropdown_selectionChange_11_listener", "onDomainSelect", "SharedAppHeaderComponent_div_0_div_17_div_8_Template_ava_dropdown_selectionChange_14_listener", "onProjectSelect", "SharedAppHeaderComponent_div_0_div_17_div_8_Template_ava_dropdown_selectionChange_17_listener", "onTeamSelect", "SharedAppHeaderComponent_div_0_div_17_div_8_Template_ava_button_userClick_19_listener", "closeOrgDialog", "SharedAppHeaderComponent_div_0_div_17_div_8_Template_ava_button_userClick_20_listener", "saveOrgPathAndClose", "popoverAlign", "headerConfigForm", "orgOptions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "domainOptions", "selected<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "projectOptions", "selectedProjectName", "<PERSON><PERSON><PERSON><PERSON>", "teamOptions", "selectedTeamName", "selectedProject", "valid", "SharedAppHeaderComponent_div_0_div_17_div_9_Template_div_click_0_listener", "_r9", "SharedAppHeaderComponent_div_0_div_17_Template_div_click_1_listener", "_r7", "toggleOrgDialog", "SharedAppHeaderComponent_div_0_div_17_div_8_Template", "SharedAppHeaderComponent_div_0_div_17_div_9_Template", "isOrgDialogOpen", "SharedAppHeaderComponent_div_0_div_18_div_3_Template_div_click_0_listener", "_r11", "closeAppDrawer", "ɵɵtextInterpolate1", "app_r13", "description", "SharedAppHeaderComponent_div_0_div_18_div_7_Template_div_click_0_listener", "_r12", "$implicit", "navigateToApp", "SharedAppHeaderComponent_div_0_div_18_div_7_div_6_Template", "ɵɵsanitizeUrl", "name", "ɵɵtextInterpolate", "SharedAppHeaderComponent_div_0_div_18_Template_div_click_1_listener", "_r10", "toggleAppDrawer", "SharedAppHeaderComponent_div_0_div_18_div_3_Template", "SharedAppHeaderComponent_div_0_div_18_div_7_Template", "isAppDrawerOpen", "getFilteredApps", "SharedAppHeaderComponent_div_0_div_19_Template_div_click_0_listener", "_r14", "toggleTheme", "themeMenuIcon", "userDesignation", "userEmail", "SharedAppHeaderComponent_div_0_div_20_div_14_Template_button_click_5_listener", "_r16", "currentTheme", "SharedAppHeaderComponent_div_0_div_20_div_14_Template_button_click_10_listener", "SharedAppHeaderComponent_div_0_div_20_div_15_button_5_Template_button_click_0_listener", "language_r18", "_r17", "switchLanguage", "code", "currentLanguage", "SharedAppHeaderComponent_div_0_div_20_div_15_button_5_Template", "config", "availableLanguages", "SharedAppHeaderComponent_div_0_div_20_Template_div_click_1_listener", "_r15", "toggleProfileDropdown", "SharedAppHeaderComponent_div_0_div_20_div_11_Template", "SharedAppHeaderComponent_div_0_div_20_div_12_Template", "SharedAppHeaderComponent_div_0_div_20_div_14_Template", "SharedAppHeaderComponent_div_0_div_20_div_15_Template", "SharedAppHeaderComponent_div_0_div_20_Template_button_click_18_listener", "logout", "profileDropdownOpen", "userAvatar", "userName", "showThemeToggleInProfile", "showLanguageSwitcher", "SharedAppHeaderComponent_div_0_Template_div_mouseenter_7_listener", "_r1", "pauseLogoAnimation", "SharedAppHeaderComponent_div_0_Template_div_mouseleave_7_listener", "resumeLogoAnimation", "SharedAppHeaderComponent_div_0_div_9_Template", "SharedAppHeaderComponent_div_0_shared_nav_item_15_Template", "SharedAppHeaderComponent_div_0_div_17_Template", "SharedAppHeaderComponent_div_0_div_18_Template", "SharedAppHeaderComponent_div_0_div_19_Template", "SharedAppHeaderComponent_div_0_div_20_Template", "isLogoAnimating", "currentLogo", "currentStudioName", "length", "ɵɵclassMap", "navItemsClasses", "navItems", "showOrgSelector", "showAppDrawer", "showThemeToggle", "showProfileDropdown", "item_r20", "SharedAppHeaderComponent_div_1_div_2_Template_div_click_0_listener", "_r19", "SharedAppHeaderComponent_div_1_div_2_img_1_Template", "SharedAppHeaderComponent_div_1_div_2_Template", "ɵɵstyleProp", "dropdownPortal", "rect", "bottom", "left", "items", "SharedAppHeaderComponent", "router", "cdr", "renderer", "elementRef", "tokenStorage", "authService", "formBuilder", "customNavItemComponent", "orgConfigService", "themeService", "isLoginPage", "studioLogosDark", "studioNames", "logoAnimationInterval", "itemCount", "classes", "trim", "navigationEvent", "dropdownItemSelected", "profileAction", "themeToggle", "languageChange", "orgConfigChange", "selectedTeam", "hierarchyData", "orgPathTrigger", "popoverRef", "open", "parentLabel", "navItemId", "subscriptions", "constructor", "ngOnInit", "initializeHeader", "setupRouterSubscription", "setupThemeSubscription", "loadUserInfo", "initializeForm", "initializeLogo", "initOrgPathFromCookie", "ngAfterViewInit", "updateActiveMenuItemByRoute", "url", "ngOnDestroy", "unsubscribe", "clearInterval", "console", "log", "enableThemeAwareLogos", "enableLogoAnimation", "getActiveStudioLogos", "activeLogos", "logoSrc", "projectName", "startLogoAnimation", "interval", "setInterval", "animateToNextLogo", "animationStyle", "logoAnimationStyle", "logoElement", "document", "querySelector", "classList", "add", "timings", "fade", "changeAt", "duration", "smooth", "crossfade", "timing", "setTimeout", "detectChanges", "remove", "undefined", "warn", "redirectUrl", "currentApp", "availableApps", "group", "org", "required", "domain", "project", "team", "navigationStartSub", "events", "pipe", "event", "subscribe", "closeDropdownPortal", "routerSub", "checkIfLoginPage", "loginRoutes", "some", "includes", "themeObservable", "theme", "updateThemeAssets", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getDaName", "getDaUserEmail", "getDaUserDesignation", "generateUserAvatar", "nameParts", "split", "initials", "toUpperCase", "colors", "colorIndex", "backgroundColor", "btoa", "path", "<PERSON><PERSON><PERSON><PERSON>", "parts", "usecasePath", "usecaseIdPath", "ids", "map", "Number", "patchValue", "toString", "pathParts", "loadData", "getOrganizationHierarchy", "next", "data", "loadOrganizations", "loadDomains", "loadProjects", "loadTeams", "error", "organizationName", "value", "orgId", "selectedOrgId", "selectedOptions", "find", "o", "domains", "domainName", "domainId", "selectedDomainId", "d", "projects", "projectId", "selectedProjectId", "p", "teams", "teamName", "teamId", "selectedTeamId", "orgLabel", "orgPath", "orgName", "formValue", "<PERSON><PERSON><PERSON><PERSON>", "emit", "<PERSON><PERSON><PERSON><PERSON>", "selected<PERSON><PERSON><PERSON>", "navigate", "for<PERSON>ach", "item", "i", "clickedItem", "isAlreadyOpen", "currentTarget", "getBoundingClientRect", "parentIndex", "newTheme", "languageCode", "getCurrentLanguageName", "language", "lang", "calculateAppDrawerPosition", "app", "startsWith", "window", "calculatePopoverPosition", "triggerRect", "nativeElement", "popoverRect", "viewportWidth", "innerWidth", "width", "appDrawerDropdown", "dropdownRect", "right", "overflow", "style", "onNavItemClick", "preventDefault", "stopPropagation", "parentItem", "child", "defaultItem", "onDocumentClick", "target", "contains", "closest", "<PERSON><PERSON><PERSON>", "clickedInsideDropdown", "el", "clickedInsideNavItem", "ɵɵdirectiveInject", "i1", "Router", "ChangeDetectorRef", "Renderer2", "ElementRef", "i2", "TokenStorageService", "i3", "AuthService", "i4", "FormBuilder", "selectors", "viewQuery", "SharedAppHeaderComponent_Query", "rf", "ctx", "SharedAppHeaderComponent_click_HostBindingHandler", "ɵɵresolveDocument", "SharedAppHeaderComponent_div_0_Template", "SharedAppHeaderComponent_div_1_Template", "i5", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "ɵNgNoValidate", "NgControlStatusGroup", "FormGroupDirective", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\components\\app-header\\app-header.component.ts", "C:\\console\\aava-ui-web\\projects\\shared\\components\\app-header\\app-header.component.html"], "sourcesContent": ["import {\r\n  Component,\r\n  Input,\r\n  Output,\r\n  EventE<PERSON>ter,\r\n  <PERSON><PERSON>nit,\r\n  <PERSON><PERSON><PERSON>roy,\r\n  HostListener,\r\n  ElementRef,\r\n  ChangeDetectorRef,\r\n  ViewChild,\r\n  Renderer2,\r\n  AfterViewInit,\r\n  inject,\r\n  Optional,\r\n} from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { HeaderComponent } from '@awe/play-comp-library';\r\nimport { Router, NavigationEnd, NavigationStart } from '@angular/router';\r\nimport { filter } from 'rxjs/operators';\r\nimport { Subscription } from 'rxjs';\r\n// import { ThemeService } from '../../services/theme/theme.service'; // Optional - can be injected if available\r\nimport { TokenStorageService } from '../../auth/services/token-storage.service';\r\nimport { AuthService } from '../../auth/services/auth.service';\r\nimport {\r\n  DropdownComponent,\r\n  DropdownOption,\r\n  IconComponent,\r\n} from '@ava/play-comp-library';\r\nimport { ButtonComponent } from '@ava/play-comp-library';\r\nimport { SharedNavItemComponent } from '../nav-item/nav-item.component';\r\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { ReactiveFormsModule } from '@angular/forms';\r\n\r\n// Organization hierarchy interfaces\r\ninterface Team {\r\n  teamId: number;\r\n  teamName: string;\r\n}\r\n\r\ninterface Project {\r\n  projectId: number;\r\n  projectName: string;\r\n  teams: Team[];\r\n}\r\n\r\ninterface Domain {\r\n  domainId: number;\r\n  domainName: string;\r\n  projects: Project[];\r\n}\r\n\r\ninterface Organization {\r\n  orgId: number;\r\n  organizationName: string;\r\n  domains: Domain[];\r\n}\r\n\r\n// Shared interfaces for navigation configuration\r\nexport interface SharedDropdownItem {\r\n  label: string;\r\n  description: string;\r\n  route: string;\r\n  icon: string;\r\n}\r\n\r\nexport interface SharedNavItem {\r\n  label: string;\r\n  route: string;\r\n  selected: boolean;\r\n  hasDropdown: boolean;\r\n  dropdownOpen?: boolean;\r\n  icon: string;\r\n  dropdownItems?: SharedDropdownItem[];\r\n  disabled?: boolean;\r\n}\r\n\r\nexport interface StudioApp {\r\n  name: string;\r\n  route: string;\r\n  icon: string;\r\n  description?: string;\r\n}\r\n\r\nexport interface HeaderConfig {\r\n  logoSrc: string;\r\n  navItems: SharedNavItem[];\r\n  showOrgSelector?: boolean;\r\n  showThemeToggle?: boolean;\r\n  showAppDrawer?: boolean;\r\n  showProfileDropdown?: boolean;\r\n  projectName?: string;\r\n  redirectUrl?: string;\r\n  currentApp?: string; // Current app name to filter from drawer\r\n  availableApps?: StudioApp[]; // Available studio apps\r\n  // Profile dropdown configuration\r\n  showThemeToggleInProfile?: boolean;\r\n  showLanguageSwitcher?: boolean;\r\n  availableLanguages?: { code: string; name: string; flag?: string }[];\r\n  // Logo animation configuration\r\n  enableLogoAnimation?: boolean;\r\n  logoAnimationInterval?: number; // Animation interval in milliseconds (default: 3000)\r\n  logoAnimationStyle?: 'fade' | 'smooth' | 'crossfade'; // Professional fade animation styles (default: 'fade')\r\n  studioLogos?: string[]; // Custom studio logos array (light theme)\r\n  studioNames?: string[]; // Custom studio names array\r\n  // Theme-aware logo configuration\r\n  studioLogosDark?: string[]; // Dark theme studio logos array\r\n  enableThemeAwareLogos?: boolean; // Enable automatic theme-aware logo switching (default: false)\r\n}\r\n\r\n@Component({\r\n  selector: 'shared-app-header',\r\n  standalone: true,\r\n  imports: [\r\n    HeaderComponent,\r\n    CommonModule,\r\n    SharedNavItemComponent,\r\n    DropdownComponent,\r\n    ButtonComponent,\r\n    ReactiveFormsModule,\r\n    IconComponent,\r\n  ],\r\n  templateUrl: './app-header.component.html',\r\n  styleUrls: ['./app-header.component.scss'],\r\n})\r\nexport class SharedAppHeaderComponent\r\n  implements OnInit, OnDestroy, AfterViewInit\r\n{\r\n  @Input() config!: HeaderConfig;\r\n  @Input() customNavItemComponent?: any; // Allow custom nav item component\r\n  @Input() orgConfigService?: any; // Allow injecting org config service from parent\r\n  @Input() themeService?: any; // Allow injecting theme service from parent\r\n\r\n  // Header state\r\n  currentTheme: 'light' | 'dark' = 'light';\r\n  profileDropdownOpen: boolean = false;\r\n  userName: string = '';\r\n  userEmail: string = '';\r\n  userDesignation: string = '';\r\n  userAvatar: string = '';\r\n  themeMenuIcon: string = '';\r\n  isLoginPage: boolean = false; // Added to track login pages\r\n\r\n  // Animated logo management\r\n  studioLogos: string[] = [\r\n    'assets/svgs/ascendion-logo/header-ascendion-logo.svg',\r\n    'assets/svgs/ascendion-logo-light.svg',\r\n    'assets/svgs/ascendion-logo-dark.svg',\r\n  ];\r\n  studioLogosDark: string[] = []; // Dark theme logos array\r\n  studioNames: string[] = ['Console', 'Experience Studio', 'Product Studio'];\r\n  currentLogoIndex: number = 0;\r\n  currentLogo: string = '';\r\n  currentStudioName: string = '';\r\n  logoAnimationInterval?: any;\r\n  isLogoAnimating: boolean = false;\r\n\r\n  // Language management\r\n  currentLanguage: string = 'en';\r\n\r\n  // Computed property for dynamic nav classes based on content\r\n  get navItemsClasses(): string {\r\n    const itemCount = this.config?.navItems?.length || 0;\r\n    let classes = '';\r\n\r\n    if (itemCount === 1) {\r\n      classes += 'single-item ';\r\n    } else if (itemCount >= 8) {\r\n      classes += 'very-many-items ';\r\n    } else if (itemCount >= 6) {\r\n      classes += 'many-items ';\r\n    }\r\n\r\n    return classes.trim();\r\n  }\r\n\r\n  // Events for parent components to handle\r\n  @Output() navigationEvent = new EventEmitter<string>();\r\n  @Output() dropdownItemSelected = new EventEmitter<{\r\n    route: string;\r\n    label: string;\r\n  }>();\r\n  @Output() profileAction = new EventEmitter<string>();\r\n  @Output() themeToggle = new EventEmitter<'light' | 'dark'>();\r\n  @Output() languageChange = new EventEmitter<string>();\r\n  @Output() orgConfigChange = new EventEmitter<any>();\r\n\r\n  // Organization selector state (if enabled)\r\n  isOrgDialogOpen: boolean = false;\r\n\r\n  // App drawer state\r\n  isAppDrawerOpen: boolean = false;\r\n\r\n  // Organization dropdown state\r\n  orgOptions: DropdownOption[] = [];\r\n  domainOptions: DropdownOption[] = [];\r\n  projectOptions: DropdownOption[] = [];\r\n  teamOptions: DropdownOption[] = [];\r\n\r\n  // Selected values (IDs for form)\r\n  selectedOrg: string = '';\r\n  selectedDomain: string = '';\r\n  selectedProject: string = '';\r\n  selectedTeam: string = '';\r\n\r\n  // Selected names (for dropdown pre-selection)\r\n  selectedOrgName: string = '';\r\n  selectedDomainName: string = '';\r\n  selectedProjectName: string = '';\r\n  selectedTeamName: string = '';\r\n\r\n  // Form for organization config\r\n  headerConfigForm!: FormGroup;\r\n\r\n  // Store the hierarchy data from API\r\n  private hierarchyData: Organization[] = [];\r\n\r\n  @ViewChild('orgPathTrigger', { static: false }) orgPathTrigger!: ElementRef;\r\n  @ViewChild('popover', { static: false }) popoverRef!: ElementRef;\r\n  popoverAlign: 'left' | 'right' = 'left';\r\n\r\n  // Dropdown portal state\r\n  dropdownPortal: {\r\n    open: boolean;\r\n    rect: DOMRect | null;\r\n    items: SharedDropdownItem[];\r\n    parentLabel: string;\r\n    navItemId: string;\r\n  } = {\r\n    open: false,\r\n    rect: null,\r\n    items: [],\r\n    parentLabel: '',\r\n    navItemId: '',\r\n  };\r\n\r\n  // Subscription management\r\n  private subscriptions = new Subscription();\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private cdr: ChangeDetectorRef,\r\n    private renderer: Renderer2,\r\n    private elementRef: ElementRef,\r\n    private tokenStorage: TokenStorageService,\r\n    private authService: AuthService,\r\n    private formBuilder: FormBuilder,\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.initializeHeader();\r\n    this.setupRouterSubscription();\r\n    this.setupThemeSubscription();\r\n    this.loadUserInfo();\r\n    this.initializeForm();\r\n    this.initializeLogo();\r\n\r\n    // Initialize organization selector if enabled\r\n    if (this.config.showOrgSelector) {\r\n      this.initOrgPathFromCookie();\r\n    }\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    this.updateActiveMenuItemByRoute(this.router.url);\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.subscriptions.unsubscribe();\r\n    if (this.logoAnimationInterval) {\r\n      clearInterval(this.logoAnimationInterval);\r\n    }\r\n  }\r\n\r\n  // ========================================\r\n  // LOGO ANIMATION METHODS\r\n  // ========================================\r\n\r\n  private initializeLogo(): void {\r\n    // Use config logos if provided, otherwise use defaults\r\n    if (this.config.studioLogos && this.config.studioLogos.length > 0) {\r\n      this.studioLogos = this.config.studioLogos;\r\n      console.log('🎨 Using custom studio logos:', this.studioLogos);\r\n    } else {\r\n      console.log('🎨 Using default studio logos:', this.studioLogos);\r\n    }\r\n\r\n    // Initialize dark theme logos if theme-aware logos are enabled\r\n    if (this.config.enableThemeAwareLogos && this.config.studioLogosDark && this.config.studioLogosDark.length > 0) {\r\n      this.studioLogosDark = this.config.studioLogosDark;\r\n      console.log('🎨 Using custom dark studio logos:', this.studioLogosDark);\r\n    }\r\n\r\n    if (this.config.studioNames && this.config.studioNames.length > 0) {\r\n      this.studioNames = this.config.studioNames;\r\n    }\r\n\r\n    // Set initial logo - use animated logos when animation is enabled\r\n    if (\r\n      this.config.enableLogoAnimation !== false &&\r\n      this.getActiveStudioLogos().length > 0\r\n    ) {\r\n      // Use first logo from animated array (theme-aware)\r\n      const activeLogos = this.getActiveStudioLogos();\r\n      this.currentLogo = activeLogos[0];\r\n      this.currentStudioName = this.studioNames[0];\r\n      console.log('🎨 Initial animated logo set to:', this.currentLogo);\r\n      console.log('🎨 Initial studio name set to:', this.currentStudioName);\r\n    } else {\r\n      // Fallback to config logoSrc when animation is disabled\r\n      this.currentLogo = this.config.logoSrc;\r\n      this.currentStudioName = this.config.projectName || 'Studio';\r\n      console.log('🎨 Using static logo:', this.currentLogo);\r\n    }\r\n\r\n    // Start animation cycle only if enabled (default: true)\r\n    if (this.config.enableLogoAnimation !== false) {\r\n      this.startLogoAnimation();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get the active studio logos array based on current theme\r\n   * Returns dark logos if theme-aware logos are enabled and current theme is dark\r\n   */\r\n  private getActiveStudioLogos(): string[] {\r\n    if (this.config.enableThemeAwareLogos && this.currentTheme === 'dark' && this.studioLogosDark.length > 0) {\r\n      return this.studioLogosDark;\r\n    }\r\n    return this.studioLogos;\r\n  }\r\n\r\n  private startLogoAnimation(): void {\r\n    // Clear any existing interval\r\n    if (this.logoAnimationInterval) {\r\n      clearInterval(this.logoAnimationInterval);\r\n    }\r\n\r\n    // Use config interval or default to 3 seconds\r\n    const interval = this.config.logoAnimationInterval || 3000;\r\n\r\n    // Start the animation cycle\r\n    this.logoAnimationInterval = setInterval(() => {\r\n      this.animateToNextLogo();\r\n    }, interval);\r\n  }\r\n\r\n  private animateToNextLogo(): void {\r\n    if (this.isLogoAnimating) return;\r\n\r\n    this.isLogoAnimating = true;\r\n\r\n    // Get animation style from config (default: 'rotate')\r\n    const animationStyle = this.config.logoAnimationStyle || 'fade';\r\n\r\n    // Add rotation animation class\r\n    const logoElement = document.querySelector('.animated-logo');\r\n    if (logoElement) {\r\n      logoElement.classList.add('logo-transitioning');\r\n\r\n      // Apply specific animation style\r\n      logoElement.classList.add(`logo-${animationStyle}`);\r\n    }\r\n\r\n    // Determine timing based on animation style\r\n    const timings = {\r\n      fade: { changeAt: 600, duration: 1200 }, // Professional fade timing\r\n      smooth: { changeAt: 500, duration: 1000 }, // Smooth fade timing\r\n      crossfade: { changeAt: 700, duration: 1400 }, // Crossfade timing\r\n    };\r\n\r\n    const timing = timings[animationStyle] || timings['fade'];\r\n\r\n    // Change the image source at the optimal point in the animation\r\n    setTimeout(() => {\r\n      // Get active logos array (theme-aware)\r\n      const activeLogos = this.getActiveStudioLogos();\r\n\r\n      // Move to next logo index\r\n      this.currentLogoIndex =\r\n        (this.currentLogoIndex + 1) % activeLogos.length;\r\n      this.currentLogo = activeLogos[this.currentLogoIndex];\r\n      this.currentStudioName = this.studioNames[this.currentLogoIndex];\r\n\r\n      // Trigger change detection\r\n      this.cdr.detectChanges();\r\n    }, timing.changeAt);\r\n\r\n    // Remove animation classes after animation completes\r\n    setTimeout(() => {\r\n      if (logoElement) {\r\n        logoElement.classList.remove('logo-transitioning');\r\n        logoElement.classList.remove(`logo-${animationStyle}`);\r\n      }\r\n      this.isLogoAnimating = false;\r\n    }, timing.duration);\r\n  }\r\n\r\n  public pauseLogoAnimation(): void {\r\n    if (this.logoAnimationInterval) {\r\n      clearInterval(this.logoAnimationInterval);\r\n      this.logoAnimationInterval = undefined;\r\n    }\r\n  }\r\n\r\n  public resumeLogoAnimation(): void {\r\n    if (!this.logoAnimationInterval) {\r\n      this.startLogoAnimation();\r\n    }\r\n  }\r\n\r\n  private initializeHeader(): void {\r\n    if (!this.config) {\r\n      console.warn('SharedAppHeaderComponent: config is required');\r\n      return;\r\n    }\r\n\r\n    // Set default values\r\n    this.config.showOrgSelector = this.config.showOrgSelector ?? false;\r\n    this.config.showThemeToggle = this.config.showThemeToggle ?? true;\r\n    this.config.showAppDrawer = this.config.showAppDrawer ?? false;\r\n    this.config.showProfileDropdown = this.config.showProfileDropdown ?? true;\r\n    this.config.projectName = this.config.projectName ?? 'Application';\r\n    this.config.redirectUrl = this.config.redirectUrl ?? '/';\r\n    this.config.currentApp = this.config.currentApp ?? '';\r\n\r\n    // Default studio apps\r\n    this.config.availableApps = this.config.availableApps ?? [\r\n      {\r\n        name: 'Console',\r\n        route: '/console',\r\n        icon: 'assets/svgs/studios/console-icon.svg',\r\n        description: 'Agent & Workflow Management',\r\n      },\r\n      {\r\n        name: 'Experience Studio',\r\n        route: '/experience-studio',\r\n        icon: 'assets/svgs/studios/experience-studio-icon.svg',\r\n        description: 'UI/UX Design & Prototyping',\r\n      },\r\n      {\r\n        name: 'Product Studio',\r\n        route: '/product-studio',\r\n        icon: 'assets/svgs/studios/product-studio-icon.svg',\r\n        description: 'Product Development',\r\n      },\r\n      {\r\n        name: 'Launchpad',\r\n        route: '/launchpad',\r\n        icon: 'assets/svgs/studios/launchpad-icon.svg',\r\n        description: 'Project Launch Hub',\r\n      },\r\n    ];\r\n\r\n    // Profile dropdown defaults\r\n    this.config.showThemeToggleInProfile =\r\n      this.config.showThemeToggleInProfile ?? true;\r\n    this.config.showLanguageSwitcher =\r\n      this.config.showLanguageSwitcher ?? false;\r\n    this.config.availableLanguages = this.config.availableLanguages ?? [\r\n      { code: 'en', name: 'English' },\r\n      { code: 'fil', name: 'Filipino' },\r\n      { code: 'es', name: 'Español' },\r\n    ];\r\n  }\r\n\r\n  private initializeForm(): void {\r\n    this.headerConfigForm = this.formBuilder.group({\r\n      org: ['', Validators.required],\r\n      domain: ['', Validators.required],\r\n      project: ['', Validators.required],\r\n      team: ['', Validators.required],\r\n    });\r\n  }\r\n\r\n  private setupRouterSubscription(): void {\r\n    //Force-close dropdowns, portal, and overlays as soon as navigation starts\r\n    const navigationStartSub = this.router.events\r\n      .pipe(filter((event) => event instanceof NavigationStart))\r\n      .subscribe(() => {\r\n        this.closeDropdownPortal();\r\n        this.profileDropdownOpen = false;\r\n        this.isOrgDialogOpen = false;\r\n        this.isAppDrawerOpen = false;\r\n      });\r\n    this.subscriptions.add(navigationStartSub);\r\n\r\n    const routerSub = this.router.events\r\n      .pipe(filter((event) => event instanceof NavigationEnd))\r\n      .subscribe((event: NavigationEnd) => {\r\n        // Check if current route is a login page\r\n        this.isLoginPage = this.checkIfLoginPage(event.url);\r\n        this.updateActiveMenuItemByRoute(event.url);\r\n        // Always close dropdown portal and all dropdowns on navigation\r\n        this.closeDropdownPortal();\r\n      });\r\n    this.subscriptions.add(routerSub);\r\n\r\n    // Check initial route\r\n    this.isLoginPage = this.checkIfLoginPage(this.router.url);\r\n  }\r\n\r\n  private checkIfLoginPage(url: string): boolean {\r\n    // Check for login routes across different studios\r\n    const loginRoutes = [\r\n      '/login',\r\n      '/auth/login',\r\n      '/experience/login',\r\n      '/product/login',\r\n      '/console/login',\r\n    ];\r\n    return loginRoutes.some((route) => url.includes(route));\r\n  }\r\n\r\n  private setupThemeSubscription(): void {\r\n    // If theme service is provided, subscribe to theme changes\r\n    if (this.themeService && this.themeService.themeObservable) {\r\n      this.subscriptions.add(\r\n        this.themeService.themeObservable.subscribe(\r\n          (theme: 'light' | 'dark') => {\r\n            this.currentTheme = theme;\r\n            this.updateThemeAssets();\r\n            this.cdr.markForCheck();\r\n          },\r\n        ),\r\n      );\r\n    }\r\n    this.updateThemeAssets();\r\n  }\r\n\r\n  private loadUserInfo(): void {\r\n    this.userName = this.tokenStorage.getDaName() || 'User';\r\n    this.userEmail = (this.tokenStorage as any).getDaUserEmail?.() || ''; // Safe call if method exists\r\n    this.userDesignation =\r\n      (this.tokenStorage as any).getDaUserDesignation?.() || 'Employee'; // Safe call if method exists\r\n    this.generateUserAvatar();\r\n  }\r\n\r\n  private generateUserAvatar(): void {\r\n    // Generate avatar from user initials if no profile picture is available\r\n    if (this.userName) {\r\n      const nameParts = this.userName.trim().split(' ');\r\n      let initials = '';\r\n\r\n      if (nameParts.length >= 2) {\r\n        // First letter of first name and first letter of last name\r\n        initials = nameParts[0][0] + nameParts[nameParts.length - 1][0];\r\n      } else if (nameParts.length === 1) {\r\n        // Just first letter if only one name\r\n        initials = nameParts[0][0];\r\n      } else {\r\n        initials = 'U'; // Default to 'U' for User\r\n      }\r\n\r\n      initials = initials.toUpperCase();\r\n\r\n      // Generate a colored avatar with initials\r\n      const colors = [\r\n        '#8B5CF6',\r\n        '#06B6D4',\r\n        '#10B981',\r\n        '#F59E0B',\r\n        '#EF4444',\r\n        '#8B5A2B',\r\n        '#6366F1',\r\n        '#EC4899',\r\n      ];\r\n      const colorIndex = this.userName.length % colors.length;\r\n      const backgroundColor = colors[colorIndex];\r\n\r\n      this.userAvatar = `data:image/svg+xml;base64,${btoa(`\r\n        <svg width=\"40\" height=\"40\" xmlns=\"http://www.w3.org/2000/svg\">\r\n          <circle cx=\"20\" cy=\"20\" r=\"20\" fill=\"${backgroundColor}\"/>\r\n          <text x=\"20\" y=\"26\" font-family=\"Inter, Arial, sans-serif\" font-size=\"14\" font-weight=\"600\" fill=\"white\" text-anchor=\"middle\">${initials}</text>\r\n        </svg>\r\n      `)}`;\r\n    }\r\n  }\r\n\r\n  private updateThemeAssets(): void {\r\n    // Update theme-specific assets\r\n    this.themeMenuIcon =\r\n      this.currentTheme === 'light'\r\n        ? 'assets/svgs/header/toggle-theme/toggle-to-dark.svg'\r\n        : 'assets/svgs/header/toggle-theme/toggle-to-light.svg';\r\n\r\n    // Update current logo if theme-aware logos are enabled and animation is active\r\n    if (this.config.enableThemeAwareLogos && this.config.enableLogoAnimation !== false) {\r\n      const activeLogos = this.getActiveStudioLogos();\r\n      if (activeLogos.length > 0 && this.currentLogoIndex < activeLogos.length) {\r\n        this.currentLogo = activeLogos[this.currentLogoIndex];\r\n        console.log('🎨 Theme changed - updated logo to:', this.currentLogo);\r\n      }\r\n    }\r\n  }\r\n\r\n  // Organization selector methods\r\n  initOrgPathFromCookie(): void {\r\n    const path = this.tokenStorage.getCookie('org_path');\r\n    if (path) {\r\n      const parts = path.split('::');\r\n      const usecasePath = parts[0] || '';\r\n      const usecaseIdPath = parts[1] || '';\r\n\r\n      // Parse the IDs\r\n      const ids = usecaseIdPath.split('@').map(Number);\r\n\r\n      // Set form values (IDs)\r\n      this.headerConfigForm.patchValue({\r\n        org: ids[0]?.toString() || '',\r\n        domain: ids[1]?.toString() || '',\r\n        project: ids[2]?.toString() || '',\r\n        team: ids[3]?.toString() || '',\r\n      });\r\n\r\n      // Store the IDs for form and the names for dropdown pre-selection\r\n      this.selectedOrg = ids[0]?.toString() || '';\r\n      this.selectedDomain = ids[1]?.toString() || '';\r\n      this.selectedProject = ids[2]?.toString() || '';\r\n      this.selectedTeam = ids[3]?.toString() || '';\r\n\r\n      // Store the names for dropdown pre-selection\r\n      const pathParts = usecasePath.split('@');\r\n      this.selectedOrgName = pathParts[0] || '';\r\n      this.selectedDomainName = pathParts[1] || '';\r\n      this.selectedProjectName = pathParts[2] || '';\r\n      this.selectedTeamName = pathParts[3] || '';\r\n\r\n      // Load dropdown options\r\n      this.loadData();\r\n    } else {\r\n      this.loadData();\r\n    }\r\n  }\r\n\r\n  loadData(): void {\r\n    if (this.orgConfigService) {\r\n      this.orgConfigService.getOrganizationHierarchy().subscribe({\r\n        next: (data: Organization[]) => {\r\n          this.hierarchyData = data;\r\n          this.loadOrganizations();\r\n\r\n          // After loading organizations, load cascading dropdowns if we have pre-selected values\r\n          if (this.selectedOrg) {\r\n            this.loadDomains(this.selectedOrg);\r\n            if (this.selectedDomain) {\r\n              this.loadProjects(this.selectedDomain);\r\n              if (this.selectedProject) {\r\n                this.loadTeams(this.selectedProject);\r\n              }\r\n            }\r\n          }\r\n        },\r\n        error: (error: any) => {\r\n          console.error('Error loading organization data:', error);\r\n        },\r\n      });\r\n    }\r\n  }\r\n\r\n  loadOrganizations(): void {\r\n    this.orgOptions = this.hierarchyData.map((org) => ({\r\n      name: org.organizationName,\r\n      value: org.orgId.toString(),\r\n    }));\r\n  }\r\n\r\n  onOrgSelect(event: any): void {\r\n    const selectedOrgId = event.selectedOptions?.[0]?.value;\r\n    const selectedOrgName = event.selectedOptions?.[0]?.name;\r\n    if (selectedOrgId) {\r\n      this.selectedOrg = selectedOrgId;\r\n      this.selectedOrgName = selectedOrgName;\r\n      this.headerConfigForm.patchValue({ org: selectedOrgId });\r\n      this.loadDomains(selectedOrgId);\r\n      // Clear dependent dropdowns\r\n      this.headerConfigForm.patchValue({ domain: '', project: '', team: '' });\r\n      this.selectedDomain = '';\r\n      this.selectedProject = '';\r\n      this.selectedTeam = '';\r\n      this.selectedDomainName = '';\r\n      this.selectedProjectName = '';\r\n      this.selectedTeamName = '';\r\n      this.projectOptions = [];\r\n      this.teamOptions = [];\r\n    }\r\n  }\r\n\r\n  loadDomains(orgId: string): void {\r\n    const org = this.hierarchyData.find((o) => o.orgId.toString() === orgId);\r\n    if (org) {\r\n      this.domainOptions = org.domains.map((domain) => ({\r\n        name: domain.domainName,\r\n        value: domain.domainId.toString(),\r\n      }));\r\n    } else {\r\n      this.domainOptions = [];\r\n    }\r\n  }\r\n\r\n  onDomainSelect(event: any): void {\r\n    const selectedDomainId = event.selectedOptions?.[0]?.value;\r\n    const selectedDomainName = event.selectedOptions?.[0]?.name;\r\n    if (selectedDomainId) {\r\n      this.selectedDomain = selectedDomainId;\r\n      this.selectedDomainName = selectedDomainName;\r\n      this.headerConfigForm.patchValue({ domain: selectedDomainId });\r\n      this.loadProjects(selectedDomainId);\r\n      // Clear dependent dropdowns\r\n      this.headerConfigForm.patchValue({ project: '', team: '' });\r\n      this.selectedProject = '';\r\n      this.selectedTeam = '';\r\n      this.selectedProjectName = '';\r\n      this.selectedTeamName = '';\r\n      this.teamOptions = [];\r\n    }\r\n  }\r\n\r\n  loadProjects(domainId: string): void {\r\n    const org = this.hierarchyData.find((o) =>\r\n      o.domains.some((d) => d.domainId.toString() === domainId),\r\n    );\r\n    if (org) {\r\n      const domain = org.domains.find(\r\n        (d) => d.domainId.toString() === domainId,\r\n      );\r\n      if (domain) {\r\n        this.projectOptions = domain.projects.map((project) => ({\r\n          name: project.projectName,\r\n          value: project.projectId.toString(),\r\n        }));\r\n      } else {\r\n        this.projectOptions = [];\r\n      }\r\n    } else {\r\n      this.projectOptions = [];\r\n    }\r\n  }\r\n\r\n  onProjectSelect(event: any): void {\r\n    const selectedProjectId = event.selectedOptions?.[0]?.value;\r\n    const selectedProjectName = event.selectedOptions?.[0]?.name;\r\n    if (selectedProjectId) {\r\n      this.selectedProject = selectedProjectId;\r\n      this.selectedProjectName = selectedProjectName;\r\n      this.headerConfigForm.patchValue({ project: selectedProjectId });\r\n      this.loadTeams(selectedProjectId);\r\n      // Clear dependent dropdowns\r\n      this.headerConfigForm.patchValue({ team: '' });\r\n      this.selectedTeam = '';\r\n      this.selectedTeamName = '';\r\n    }\r\n  }\r\n\r\n  loadTeams(projectId: string): void {\r\n    const org = this.hierarchyData.find((o) =>\r\n      o.domains.some((d) =>\r\n        d.projects.some((p) => p.projectId.toString() === projectId),\r\n      ),\r\n    );\r\n    if (org) {\r\n      const domain = org.domains.find((d) =>\r\n        d.projects.some((p) => p.projectId.toString() === projectId),\r\n      );\r\n      if (domain) {\r\n        const project = domain.projects.find(\r\n          (p) => p.projectId.toString() === projectId,\r\n        );\r\n        if (project) {\r\n          this.teamOptions = project.teams.map((team) => ({\r\n            name: team.teamName,\r\n            value: team.teamId.toString(),\r\n          }));\r\n        } else {\r\n          this.teamOptions = [];\r\n        }\r\n      } else {\r\n        this.teamOptions = [];\r\n      }\r\n    } else {\r\n      this.teamOptions = [];\r\n    }\r\n  }\r\n\r\n  onTeamSelect(event: any): void {\r\n    const selectedTeamId = event.selectedOptions?.[0]?.value;\r\n    const selectedTeamName = event.selectedOptions?.[0]?.name;\r\n    if (selectedTeamId) {\r\n      this.selectedTeam = selectedTeamId;\r\n      this.selectedTeamName = selectedTeamName;\r\n      this.headerConfigForm.patchValue({ team: selectedTeamId });\r\n    }\r\n  }\r\n\r\n  get orgLabel(): string {\r\n    // Try to get the org name from the org_path cookie\r\n    const orgPath = this.tokenStorage.getCookie('org_path');\r\n    if (orgPath) {\r\n      const orgName = orgPath.split('::')[0].split('@')[0];\r\n      if (orgName) return orgName;\r\n    }\r\n    // Fallback to dropdown label\r\n    return (\r\n      this.orgOptions.find((o) => o.value === this.selectedOrg)?.name ||\r\n      'Select Organization'\r\n    );\r\n  }\r\n\r\n  saveOrgPathAndClose(): void {\r\n    if (this.headerConfigForm.valid) {\r\n      const formValue = this.headerConfigForm.value;\r\n\r\n      // Build the org path string\r\n      const orgPath = `${this.selectedOrgName}@${this.selectedDomainName}@${this.selectedProjectName}@${this.selectedTeamName}::${formValue.org}@${formValue.domain}@${formValue.project}@${formValue.team}`;\r\n\r\n      // Save to cookie\r\n      this.tokenStorage.setCookie('org_path', orgPath);\r\n\r\n      // Emit the change event\r\n      this.orgConfigChange.emit({\r\n        orgPath,\r\n        selectedValues: {\r\n          org: this.selectedOrg,\r\n          domain: this.selectedDomain,\r\n          project: this.selectedProject,\r\n          team: this.selectedTeam,\r\n        },\r\n        selectedNames: {\r\n          org: this.selectedOrgName,\r\n          domain: this.selectedDomainName,\r\n          project: this.selectedProjectName,\r\n          team: this.selectedTeamName,\r\n        },\r\n      });\r\n\r\n      this.closeOrgDialog();\r\n    }\r\n  }\r\n\r\n  // Navigation methods\r\n  navigateTo(route: string): void {\r\n    // Defensive: Always close dropdowns before navigating\r\n    this.closeDropdownPortal();\r\n    this.router.navigate([route]);\r\n    this.navigationEvent.emit(route);\r\n  }\r\n\r\n  selectMenuItem(index: number): void {\r\n    this.config.navItems.forEach((item, i) => {\r\n      item.selected = i === index;\r\n    });\r\n  }\r\n\r\n  // Toggle dropdown and optionally force close the portal\r\n\r\n  toggleDropdown(index: number, event?: MouseEvent): void {\r\n  const clickedItem = this.config.navItems[index];\r\n\r\n  const isAlreadyOpen = clickedItem.dropdownOpen;\r\n\r\n  // Close all dropdowns first\r\n  this.config.navItems.forEach((item, i) => {\r\n    item.dropdownOpen = false;\r\n  });\r\n\r\n  if (isAlreadyOpen) {\r\n    this.closeDropdownPortal();\r\n    return;\r\n  }\r\n\r\n  // Open the clicked dropdown only\r\n  clickedItem.dropdownOpen = true;\r\n  // Set portal\r\n  if (event) {\r\n    const rect = (event.currentTarget as HTMLElement).getBoundingClientRect();\r\n    this.onDropdownPortalOpen({\r\n      rect,\r\n      items: clickedItem.dropdownItems || [],\r\n      parentLabel: clickedItem.label,\r\n      navItemId: clickedItem.label,\r\n    });\r\n  }\r\n}\r\n\r\n  onDropdownItemSelected(\r\n    event: { route: string; label: string },\r\n    parentIndex: number,\r\n  ): void {\r\n    this.navigateTo(event.route);\r\n    this.dropdownItemSelected.emit(event);\r\n    this.selectMenuItem(parentIndex);\r\n    this.closeDropdownPortal();\r\n  }\r\n\r\n  onDropdownPortalOpen(event: {\r\n    rect: DOMRect;\r\n    items: SharedDropdownItem[];\r\n    parentLabel: string;\r\n    navItemId: string;\r\n  }): void {\r\n    this.dropdownPortal = {\r\n      open: true,\r\n      rect: event.rect,\r\n      items: event.items,\r\n      parentLabel: event.parentLabel,\r\n      navItemId: event.navItemId,\r\n    };\r\n    //Force change detection to flush portal render and avoid \"frozen\" dropdowns\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  closeDropdownPortal(): void {\r\n    this.dropdownPortal.open = false;\r\n    this.dropdownPortal.navItemId = '';\r\n    this.config.navItems.forEach((item) => (item.dropdownOpen = false));\r\n  }\r\n\r\n  // Profile dropdown methods\r\n  toggleProfileDropdown(): void {\r\n    this.profileDropdownOpen = !this.profileDropdownOpen;\r\n  }\r\n\r\n  logout(): void {\r\n    this.authService.logout();\r\n    this.profileAction.emit('logout');\r\n  }\r\n\r\n  // Theme methods\r\n  toggleTheme(): void {\r\n    if (this.themeService && this.themeService.toggleTheme) {\r\n      // Use the injected theme service if available\r\n      this.themeService.toggleTheme();\r\n    } else {\r\n      // Fallback to local theme toggle\r\n      const newTheme = this.currentTheme === 'light' ? 'dark' : 'light';\r\n      this.currentTheme = newTheme;\r\n      this.updateThemeAssets();\r\n      this.themeToggle.emit(newTheme);\r\n    }\r\n  }\r\n\r\n  // Language methods\r\n  switchLanguage(languageCode: string): void {\r\n    this.currentLanguage = languageCode;\r\n    this.languageChange.emit(languageCode);\r\n  }\r\n\r\n  getCurrentLanguageName(): string {\r\n    const language = this.config.availableLanguages?.find(\r\n      (lang) => lang.code === this.currentLanguage,\r\n    );\r\n    return language?.name || 'English';\r\n  }\r\n\r\n  // App drawer methods\r\n  toggleAppDrawer(): void {\r\n    this.isAppDrawerOpen = !this.isAppDrawerOpen;\r\n\r\n    if (this.isAppDrawerOpen) {\r\n      // Calculate position to prevent overflow\r\n      setTimeout(() => {\r\n        this.calculateAppDrawerPosition();\r\n      });\r\n    }\r\n  }\r\n\r\n  closeAppDrawer(): void {\r\n    this.isAppDrawerOpen = false;\r\n  }\r\n\r\n  navigateToApp(app: StudioApp): void {\r\n    if (app.route.startsWith('http')) {\r\n      // External URL\r\n      window.open(app.route, '_blank');\r\n    } else {\r\n      // Internal route\r\n      this.router.navigate([app.route]);\r\n    }\r\n    this.closeAppDrawer();\r\n    this.navigationEvent.emit(app.route);\r\n  }\r\n\r\n  getFilteredApps(): StudioApp[] {\r\n    if (!this.config.availableApps) return [];\r\n\r\n    // Filter out the current app\r\n    return this.config.availableApps.filter(\r\n      (app) => app.name !== this.config.currentApp,\r\n    );\r\n  }\r\n\r\n  // Organization selector methods (if enabled)\r\n  toggleOrgDialog(): void {\r\n    if (this.config.showOrgSelector) {\r\n      this.isOrgDialogOpen = !this.isOrgDialogOpen;\r\n\r\n      if (this.isOrgDialogOpen) {\r\n        // Calculate popover position\r\n        setTimeout(() => {\r\n          this.calculatePopoverPosition();\r\n        });\r\n      }\r\n    }\r\n  }\r\n\r\n  closeOrgDialog(): void {\r\n    this.isOrgDialogOpen = false;\r\n  }\r\n\r\n  private calculatePopoverPosition(): void {\r\n    if (this.orgPathTrigger && this.popoverRef) {\r\n      const triggerRect =\r\n        this.orgPathTrigger.nativeElement.getBoundingClientRect();\r\n      const popoverRect = this.popoverRef.nativeElement.getBoundingClientRect();\r\n      const viewportWidth = window.innerWidth;\r\n\r\n      // Check if popover would overflow on the right\r\n      if (triggerRect.left + popoverRect.width > viewportWidth - 20) {\r\n        this.popoverAlign = 'right';\r\n      } else {\r\n        this.popoverAlign = 'left';\r\n      }\r\n    }\r\n  }\r\n\r\n  private calculateAppDrawerPosition(): void {\r\n    // Find the app drawer dropdown element\r\n    const appDrawerDropdown = this.elementRef.nativeElement.querySelector(\r\n      '.app-drawer-dropdown',\r\n    );\r\n    if (appDrawerDropdown) {\r\n      const dropdownRect = appDrawerDropdown.getBoundingClientRect();\r\n      const viewportWidth = window.innerWidth;\r\n\r\n      // If dropdown would overflow on the right, adjust position\r\n      if (dropdownRect.right > viewportWidth - 20) {\r\n        const overflow = dropdownRect.right - viewportWidth + 20;\r\n        appDrawerDropdown.style.right = `${overflow}px`;\r\n      } else {\r\n        appDrawerDropdown.style.right = '0px';\r\n      }\r\n    }\r\n  }\r\n\r\n  // Navigation item click handler\r\nonNavItemClick(item: SharedNavItem, index: number, event: MouseEvent): void {\r\n  if (item.disabled) {\r\n    event.preventDefault();\r\n    event.stopPropagation();\r\n    return;\r\n  }\r\n\r\n  if (item.hasDropdown && item.dropdownItems) {\r\n    event.stopPropagation();\r\n    this.toggleDropdown(index, event); //Only one place toggles\r\n  } else {\r\n    this.navigateTo(item.route);\r\n    this.selectMenuItem(index);\r\n  }\r\n}\r\n\r\n  // Route-based active menu item update\r\n  updateActiveMenuItemByRoute(url: string): void {\r\n    // Reset all selections\r\n    this.config.navItems.forEach((item) => {\r\n      item.selected = false;\r\n    });\r\n\r\n    // Find the matching parent route or parent of a child route\r\n    const parentItem = this.config.navItems.find((item) => {\r\n      // Check if this is a direct match for the parent route\r\n      if (url === item.route) {\r\n        return true;\r\n      }\r\n\r\n      // Check if this is a dropdown parent with a matching child\r\n      if (item.hasDropdown && item.dropdownItems) {\r\n        // Check if the URL starts with the parent route path (for nested routes)\r\n        // OR if any child route exactly matches the URL\r\n        return (\r\n          url.startsWith(item.route + '/') ||\r\n          item.dropdownItems.some((child) => url === child.route)\r\n        );\r\n      }\r\n\r\n      // Even if hasDropdown is false, check for dropdownItems\r\n      if (!item.hasDropdown && item.dropdownItems) {\r\n        return item.dropdownItems.some((child) => url === child.route);\r\n      }\r\n\r\n      return false;\r\n    });\r\n\r\n    if (parentItem) {\r\n      parentItem.selected = true;\r\n    } else {\r\n      // Default to first non-disabled item if no match found\r\n      const defaultItem = this.config.navItems.find((item) => !item.disabled);\r\n      if (defaultItem) {\r\n        defaultItem.selected = true;\r\n      }\r\n    }\r\n  }\r\n\r\n  // Document click listener to close dropdowns\r\n@HostListener('document:click', ['$event'])\r\nonDocumentClick(event: MouseEvent): void {\r\n  const target = event.target as HTMLElement;\r\n\r\n  // Close profile dropdown if clicking outside\r\n  if (\r\n    this.profileDropdownOpen &&\r\n    !this.elementRef.nativeElement.contains(target)\r\n  ) {\r\n    this.profileDropdownOpen = false;\r\n  }\r\n\r\n  // Close org dialog if clicking outside\r\n  if (\r\n    this.isOrgDialogOpen &&\r\n    !target.closest('.org-path-dropdown-container')\r\n  ) {\r\n    this.closeOrgDialog();\r\n  }\r\n\r\n  // Close app drawer if clicking outside\r\n  if (this.isAppDrawerOpen && !target.closest('.app-drawer-container')) {\r\n    this.closeAppDrawer();\r\n  }\r\n\r\n  //SAFELY close dropdowns only if the click is fully outside nav items + dropdown menu\r\n  const path = event.composedPath?.() || [];\r\n\r\n  const clickedInsideDropdown = path.some((el: any) =>\r\n    el?.classList?.contains?.('dropdown-portal-menu')\r\n  );\r\n\r\n  const clickedInsideNavItem = path.some((el: any) =>\r\n    el?.classList?.contains?.('nav-item-wrapper')\r\n  );\r\n\r\n  if (this.dropdownPortal.open && !clickedInsideDropdown && !clickedInsideNavItem) {\r\n    this.closeDropdownPortal();\r\n  }\r\n}\r\n}\r\n", "<!-- Hide header on login pages -->\r\n<div *ngIf=\"!isLoginPage\">\r\n  <!-- SVG Clip Path Definition for Header Design -->\r\n  <svg width=\"0\" height=\"0\" style=\"position: absolute\">\r\n    <defs>\r\n      <clipPath id=\"headerClip\" clipPathUnits=\"objectBoundingBox\">\r\n        <path\r\n          d=\"\r\n          M 0.03,0 \r\n          L 0.97,0 \r\n          L 0.95,0.71 \r\n          Q 0.939,1    0.91,1 \r\n          L 0.09,1 \r\n          Q 0.061,1    0.05,0.69 \r\n          Z\"\r\n        />\r\n      </clipPath>\r\n    </defs>\r\n  </svg>\r\n\r\n  <awe-header theme=\"light\" id=\"main-header\">\r\n    <div left-content>\r\n      <div\r\n        class=\"animated-logo-container\"\r\n        [attr.data-studio]=\"currentStudioName\"\r\n        [attr.data-index]=\"currentLogoIndex\"\r\n        (mouseenter)=\"pauseLogoAnimation()\"\r\n        (mouseleave)=\"resumeLogoAnimation()\"\r\n      >\r\n        <img\r\n          [src]=\"currentLogo\"\r\n          class=\"header-logo animated-logo\"\r\n          [class.logo-transitioning]=\"isLogoAnimating\"\r\n          [alt]=\"currentStudioName + ' Logo'\"\r\n        />\r\n\r\n        <!-- Optional: Studio indicator dots -->\r\n        <div class=\"studio-indicators\" *ngIf=\"studioLogos.length > 1\">\r\n          <div\r\n            *ngFor=\"let logo of studioLogos; let i = index\"\r\n            class=\"studio-dot\"\r\n            [class.active]=\"i === currentLogoIndex\"\r\n          ></div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div center-content>\r\n      <div class=\"header-wrapper\">\r\n        <div class=\"header-shadow\"></div>\r\n        <div class=\"nav-menu\">\r\n          <div class=\"nav-items\" [class]=\"navItemsClasses\">\r\n            <!-- Navigation items -->\r\n            <shared-nav-item\r\n              *ngFor=\"let item of config.navItems; let i = index\"\r\n              [label]=\"item.label\"\r\n              [route]=\"item.route\"\r\n              [selected]=\"item.selected\"\r\n              [hasDropdown]=\"item.hasDropdown\"\r\n              [dropdownOpen]=\"item.dropdownOpen || false\"\r\n              [dropdownItems]=\"item.dropdownItems || []\"\r\n              [icon]=\"item.icon\"\r\n              [disabled]=\"item.disabled || false\"\r\n              (toggleDropdownEvent)=\"toggleDropdown(i)\"\r\n              (navigateEvent)=\"navigateTo($event)\"\r\n              (selectEvent)=\"selectMenuItem(i)\"\r\n              (dropdownItemSelected)=\"onDropdownItemSelected($event, i)\"\r\n              (dropdownPortalOpen)=\"onDropdownPortalOpen($event)\"\r\n              class=\"nav-item-wrapper\"\r\n            >\r\n            </shared-nav-item>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div right-content class=\"user-info-container\">\r\n      <!-- Organization Selector (if enabled) -->\r\n      <div *ngIf=\"config.showOrgSelector\" class=\"org-path-dropdown-container\">\r\n        <div\r\n          class=\"org-path-trigger\"\r\n          (click)=\"toggleOrgDialog()\"\r\n          #orgPathTrigger\r\n        >\r\n          <span class=\"org-icon\">\r\n            <img\r\n              src=\"header-ascendion-logo.svg\"\r\n              alt=\"Organization Logo\"\r\n              width=\"40\"\r\n              height=\"40\"\r\n            />\r\n          </span>\r\n          <span class=\"org-dropdown-arrow\" [class.open]=\"isOrgDialogOpen\">\r\n            <svg width=\"16\" height=\"16\" viewBox=\"0 0 12 12\" fill=\"none\">\r\n              <path\r\n                d=\"M2.5 4L6 7.5L9.5 4\"\r\n                stroke=\"currentColor\"\r\n                stroke-width=\"1.5\"\r\n                stroke-linecap=\"round\"\r\n                stroke-linejoin=\"round\"\r\n              />\r\n            </svg>\r\n          </span>\r\n        </div>\r\n\r\n        <!-- Organization Configuration Dialog -->\r\n        <div\r\n          *ngIf=\"isOrgDialogOpen\"\r\n          class=\"org-path-popover\"\r\n          #popover\r\n          [ngClass]=\"popoverAlign\"\r\n        >\r\n          <form [formGroup]=\"headerConfigForm\">\r\n            <div class=\"filter-config-title\">Filter Configuration</div>\r\n            <div class=\"dropdown-row-vertical\">\r\n              <label class=\"filter-label required\">Choose Organization</label>\r\n              <ava-dropdown\r\n                [dropdownTitle]=\"'Select Organization'\"\r\n                [options]=\"orgOptions\"\r\n                [selectedValue]=\"selectedOrgName\"\r\n                [disabled]=\"false\"\r\n                (selectionChange)=\"onOrgSelect($event)\"\r\n                [search]=\"true\"\r\n                [enableSearch]=\"true\"\r\n              >\r\n              </ava-dropdown>\r\n\r\n              <label class=\"filter-label required\">Choose Domain</label>\r\n              <ava-dropdown\r\n                [dropdownTitle]=\"'Select Domain'\"\r\n                [options]=\"domainOptions\"\r\n                [selectedValue]=\"selectedDomainName\"\r\n                [disabled]=\"!selectedOrg\"\r\n                (selectionChange)=\"onDomainSelect($event)\"\r\n                [search]=\"true\"\r\n                [enableSearch]=\"true\"\r\n              >\r\n              </ava-dropdown>\r\n\r\n              <label class=\"filter-label required\">Choose Project</label>\r\n              <ava-dropdown\r\n                [dropdownTitle]=\"'Select Project'\"\r\n                [options]=\"projectOptions\"\r\n                [selectedValue]=\"selectedProjectName\"\r\n                [disabled]=\"!selectedDomain\"\r\n                (selectionChange)=\"onProjectSelect($event)\"\r\n                [search]=\"true\"\r\n                [enableSearch]=\"true\"\r\n              >\r\n              </ava-dropdown>\r\n\r\n              <label class=\"filter-label required\">Choose Team</label>\r\n              <ava-dropdown\r\n                [dropdownTitle]=\"'Select Team'\"\r\n                [options]=\"teamOptions\"\r\n                [selectedValue]=\"selectedTeamName\"\r\n                [disabled]=\"!selectedProject\"\r\n                (selectionChange)=\"onTeamSelect($event)\"\r\n                [search]=\"true\"\r\n                [enableSearch]=\"true\"\r\n              >\r\n              </ava-dropdown>\r\n            </div>\r\n            <div class=\"popover-actions\">\r\n              <ava-button\r\n                label=\"Cancel\"\r\n                variant=\"secondary\"\r\n                size=\"medium\"\r\n                (userClick)=\"closeOrgDialog()\"\r\n              >\r\n              </ava-button>\r\n              <ava-button\r\n                label=\"Apply\"\r\n                variant=\"primary\"\r\n                size=\"medium\"\r\n                [disabled]=\"!headerConfigForm.valid\"\r\n                (userClick)=\"saveOrgPathAndClose()\"\r\n              >\r\n              </ava-button>\r\n            </div>\r\n          </form>\r\n        </div>\r\n\r\n        <!-- Organization dialog backdrop -->\r\n        <div\r\n          *ngIf=\"isOrgDialogOpen\"\r\n          class=\"org-path-backdrop\"\r\n          (click)=\"closeOrgDialog()\"\r\n        ></div>\r\n      </div>\r\n\r\n      <!-- App Drawer (if enabled) -->\r\n      <div *ngIf=\"config.showAppDrawer\" class=\"app-drawer-container\">\r\n        <div\r\n          class=\"app-drawer-trigger\"\r\n          [class.active]=\"isAppDrawerOpen\"\r\n          (click)=\"toggleAppDrawer()\"\r\n        >\r\n          <ava-icon iconName=\"layout-grid\"></ava-icon>\r\n        </div>\r\n\r\n        <!-- App Drawer Backdrop -->\r\n        <div\r\n          *ngIf=\"isAppDrawerOpen\"\r\n          class=\"app-drawer-backdrop\"\r\n          (click)=\"closeAppDrawer()\"\r\n        ></div>\r\n\r\n        <!-- App Drawer Dropdown -->\r\n        <div class=\"app-drawer-dropdown\" [class.visible]=\"isAppDrawerOpen\">\r\n          <div class=\"app-drawer-content\">\r\n            <!-- <div class=\"app-drawer-header\">\r\n            <h3>Ascendion Studios</h3>\r\n          </div> -->\r\n            <div class=\"app-drawer-grid\">\r\n              <div\r\n                *ngFor=\"let app of getFilteredApps()\"\r\n                class=\"app-drawer-item\"\r\n                (click)=\"navigateToApp(app)\"\r\n              >\r\n                <div class=\"app-icon\">\r\n                  <img [src]=\"app.icon\" [alt]=\"app.name\" />\r\n                </div>\r\n                <div class=\"app-info\">\r\n                  <div class=\"app-name\">{{ app.name }}</div>\r\n                  <div class=\"app-description\" *ngIf=\"app.description\">\r\n                    {{ app.description }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Theme Toggle (if enabled and app drawer is disabled) -->\r\n      <div\r\n        *ngIf=\"config.showThemeToggle && !config.showAppDrawer\"\r\n        class=\"theme-toggle\"\r\n        (click)=\"toggleTheme()\"\r\n      >\r\n        <img [src]=\"themeMenuIcon\" alt=\"Toggle Theme\" width=\"24\" height=\"24\" />\r\n      </div>\r\n\r\n      <!-- Profile Dropdown -->\r\n      <div *ngIf=\"config.showProfileDropdown\" class=\"profile-container\">\r\n        <div\r\n          class=\"profile-trigger\"\r\n          [class.active]=\"profileDropdownOpen\"\r\n          (click)=\"toggleProfileDropdown()\"\r\n        >\r\n          <img [src]=\"userAvatar\" alt=\"User Profile\" class=\"profile-avatar\" />\r\n        </div>\r\n\r\n        <!-- Enhanced Profile Dropdown -->\r\n        <div class=\"profile-dropdown\" [class.visible]=\"profileDropdownOpen\">\r\n          <div class=\"profile-dropdown-content\">\r\n            <!-- User Info Section -->\r\n            <div class=\"profile-user-info\">\r\n              <div class=\"profile-avatar-large\">\r\n                <img [src]=\"userAvatar\" alt=\"User Profile\" />\r\n              </div>\r\n              <div class=\"profile-details\">\r\n                <div class=\"profile-name\">{{ userName }}</div>\r\n                <div class=\"profile-designation\" *ngIf=\"userDesignation\">\r\n                  {{ userDesignation }}\r\n                </div>\r\n                <div class=\"profile-email\" *ngIf=\"userEmail\">\r\n                  {{ userEmail }}\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"profile-divider\"></div>\r\n\r\n            <!-- Theme Toggle Section (if enabled) -->\r\n            <div\r\n              *ngIf=\"config.showThemeToggleInProfile\"\r\n              class=\"profile-section\"\r\n            >\r\n              <div class=\"profile-section-header\">\r\n                <span class=\"section-title\">Mode</span>\r\n              </div>\r\n              <div class=\"theme-toggle-container\">\r\n                <button\r\n                  class=\"theme-option\"\r\n                  [class.active]=\"currentTheme === 'light'\"\r\n                  (click)=\"currentTheme !== 'light' && toggleTheme()\"\r\n                >\r\n                  <svg\r\n                    width=\"16\"\r\n                    height=\"16\"\r\n                    viewBox=\"0 0 24 24\"\r\n                    fill=\"none\"\r\n                    stroke=\"currentColor\"\r\n                    stroke-width=\"2\"\r\n                  >\r\n                    <circle cx=\"12\" cy=\"12\" r=\"5\" />\r\n                    <path\r\n                      d=\"M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42\"\r\n                    />\r\n                  </svg>\r\n                  Light\r\n                </button>\r\n                <button\r\n                  class=\"theme-option\"\r\n                  [class.active]=\"currentTheme === 'dark'\"\r\n                  (click)=\"currentTheme !== 'dark' && toggleTheme()\"\r\n                >\r\n                  <svg\r\n                    width=\"16\"\r\n                    height=\"16\"\r\n                    viewBox=\"0 0 24 24\"\r\n                    fill=\"none\"\r\n                    stroke=\"currentColor\"\r\n                    stroke-width=\"2\"\r\n                  >\r\n                    <path d=\"M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z\" />\r\n                  </svg>\r\n                  Dark\r\n                </button>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Language Switcher Section (if enabled) -->\r\n            <div *ngIf=\"config.showLanguageSwitcher\" class=\"profile-section\">\r\n              <div class=\"profile-section-header\">\r\n                <span class=\"section-title\">Language</span>\r\n              </div>\r\n              <div class=\"language-options\">\r\n                <button\r\n                  *ngFor=\"let language of config.availableLanguages\"\r\n                  class=\"language-option\"\r\n                  [class.active]=\"currentLanguage === language.code\"\r\n                  (click)=\"switchLanguage(language.code)\"\r\n                >\r\n                  {{ language.name }}\r\n                </button>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"profile-divider\"></div>\r\n\r\n            <!-- Actions Section -->\r\n            <div class=\"profile-actions\">\r\n              <button class=\"profile-action-item logout-btn\" (click)=\"logout()\">\r\n                <svg\r\n                  width=\"16\"\r\n                  height=\"16\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  fill=\"none\"\r\n                  stroke=\"currentColor\"\r\n                  stroke-width=\"2\"\r\n                >\r\n                  <path d=\"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4\" />\r\n                  <polyline points=\"16,17 21,12 16,7\" />\r\n                  <line x1=\"21\" y1=\"12\" x2=\"9\" y2=\"12\" />\r\n                </svg>\r\n                Sign Out\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </awe-header>\r\n</div>\r\n\r\n<!-- Dropdown Portal Container -->\r\n<div\r\n  *ngIf=\"dropdownPortal.open && dropdownPortal.rect\"\r\n  class=\"dropdown-portal-menu\"\r\n  [style.top]=\"dropdownPortal.rect.bottom + 4 + 'px'\"\r\n  [style.left]=\"dropdownPortal.rect.left + 'px'\"\r\n>\r\n  <div class=\"dropdown-menu\">\r\n    <div\r\n      *ngFor=\"let item of dropdownPortal.items\"\r\n      class=\"dropdown-item\"\r\n      (click)=\"\r\n        onDropdownItemSelected({ route: item.route, label: item.label }, 0)\r\n      \"\r\n    >\r\n      <img *ngIf=\"item.icon\" [src]=\"item.icon\" alt=\"\" class=\"dropdown-icon\" />\r\n      <div class=\"dropdown-content\">\r\n        <div class=\"dropdown-label\">{{ item.label }}</div>\r\n        <div class=\"dropdown-description\">{{ item.description }}</div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAAA,SAIEA,YAAY,QAWP,eAAe;AACtB,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,QAAQ,wBAAwB;AACxD,SAAiBC,aAAa,EAAEC,eAAe,QAAQ,iBAAiB;AACxE,SAASC,MAAM,QAAQ,gBAAgB;AACvC,SAASC,YAAY,QAAQ,MAAM;AAInC,SACEC,iBAAiB,EAEjBC,aAAa,QACR,wBAAwB;AAC/B,SAASC,eAAe,QAAQ,wBAAwB;AACxD,SAASC,sBAAsB,QAAQ,gCAAgC;AACvE,SAAiCC,UAAU,QAAQ,gBAAgB;AACnE,SAASC,mBAAmB,QAAQ,gBAAgB;;;;;;;;;;;;ICM1CC,EAAA,CAAAC,SAAA,cAIO;;;;;IADLD,EAAA,CAAAE,WAAA,WAAAC,IAAA,KAAAC,MAAA,CAAAC,gBAAA,CAAuC;;;;;IAJ3CL,EAAA,CAAAM,cAAA,cAA8D;IAC5DN,EAAA,CAAAO,UAAA,IAAAC,mDAAA,kBAIC;IACHR,EAAA,CAAAS,YAAA,EAAM;;;;IAJeT,EAAA,CAAAU,SAAA,EAAgB;IAAhBV,EAAA,CAAAW,UAAA,YAAAP,MAAA,CAAAQ,WAAA,CAAgB;;;;;;IAcjCZ,EAAA,CAAAM,cAAA,0BAgBC;IAFCN,EAJA,CAAAa,UAAA,iCAAAC,0GAAA;MAAA,MAAAC,IAAA,GAAAf,EAAA,CAAAgB,aAAA,CAAAC,GAAA,EAAAC,KAAA;MAAA,MAAAd,MAAA,GAAAJ,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAuBhB,MAAA,CAAAiB,cAAA,CAAAN,IAAA,CAAiB;IAAA,EAAC,2BAAAO,oGAAAC,MAAA;MAAAvB,EAAA,CAAAgB,aAAA,CAAAC,GAAA;MAAA,MAAAb,MAAA,GAAAJ,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CACxBhB,MAAA,CAAAoB,UAAA,CAAAD,MAAA,CAAkB;IAAA,EAAC,yBAAAE,kGAAA;MAAA,MAAAV,IAAA,GAAAf,EAAA,CAAAgB,aAAA,CAAAC,GAAA,EAAAC,KAAA;MAAA,MAAAd,MAAA,GAAAJ,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CACrBhB,MAAA,CAAAsB,cAAA,CAAAX,IAAA,CAAiB;IAAA,EAAC,kCAAAY,2GAAAJ,MAAA;MAAA,MAAAR,IAAA,GAAAf,EAAA,CAAAgB,aAAA,CAAAC,GAAA,EAAAC,KAAA;MAAA,MAAAd,MAAA,GAAAJ,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CACThB,MAAA,CAAAwB,sBAAA,CAAAL,MAAA,EAAAR,IAAA,CAAiC;IAAA,EAAC,gCAAAc,yGAAAN,MAAA;MAAAvB,EAAA,CAAAgB,aAAA,CAAAC,GAAA;MAAA,MAAAb,MAAA,GAAAJ,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CACpChB,MAAA,CAAA0B,oBAAA,CAAAP,MAAA,CAA4B;IAAA,EAAC;IAGrDvB,EAAA,CAAAS,YAAA,EAAkB;;;;IARhBT,EAPA,CAAAW,UAAA,UAAAoB,OAAA,CAAAC,KAAA,CAAoB,UAAAD,OAAA,CAAAE,KAAA,CACA,aAAAF,OAAA,CAAAG,QAAA,CACM,gBAAAH,OAAA,CAAAI,WAAA,CACM,iBAAAJ,OAAA,CAAAK,YAAA,UACW,kBAAAL,OAAA,CAAAM,aAAA,IAAArC,EAAA,CAAAsC,eAAA,IAAAC,GAAA,EACD,SAAAR,OAAA,CAAAS,IAAA,CACxB,aAAAT,OAAA,CAAAU,QAAA,UACiB;;;;;;IAmDrCzC,EAPJ,CAAAM,cAAA,iBAKC,eACsC,cACF;IAAAN,EAAA,CAAA0C,MAAA,2BAAoB;IAAA1C,EAAA,CAAAS,YAAA,EAAM;IAEzDT,EADF,CAAAM,cAAA,cAAmC,gBACI;IAAAN,EAAA,CAAA0C,MAAA,0BAAmB;IAAA1C,EAAA,CAAAS,YAAA,EAAQ;IAChET,EAAA,CAAAM,cAAA,uBAQC;IAHCN,EAAA,CAAAa,UAAA,6BAAA8B,6FAAApB,MAAA;MAAAvB,EAAA,CAAAgB,aAAA,CAAA4B,GAAA;MAAA,MAAAxC,MAAA,GAAAJ,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAmBhB,MAAA,CAAAyC,WAAA,CAAAtB,MAAA,CAAmB;IAAA,EAAC;IAIzCvB,EAAA,CAAAS,YAAA,EAAe;IAEfT,EAAA,CAAAM,cAAA,gBAAqC;IAAAN,EAAA,CAAA0C,MAAA,qBAAa;IAAA1C,EAAA,CAAAS,YAAA,EAAQ;IAC1DT,EAAA,CAAAM,cAAA,wBAQC;IAHCN,EAAA,CAAAa,UAAA,6BAAAiC,8FAAAvB,MAAA;MAAAvB,EAAA,CAAAgB,aAAA,CAAA4B,GAAA;MAAA,MAAAxC,MAAA,GAAAJ,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAmBhB,MAAA,CAAA2C,cAAA,CAAAxB,MAAA,CAAsB;IAAA,EAAC;IAI5CvB,EAAA,CAAAS,YAAA,EAAe;IAEfT,EAAA,CAAAM,cAAA,iBAAqC;IAAAN,EAAA,CAAA0C,MAAA,sBAAc;IAAA1C,EAAA,CAAAS,YAAA,EAAQ;IAC3DT,EAAA,CAAAM,cAAA,wBAQC;IAHCN,EAAA,CAAAa,UAAA,6BAAAmC,8FAAAzB,MAAA;MAAAvB,EAAA,CAAAgB,aAAA,CAAA4B,GAAA;MAAA,MAAAxC,MAAA,GAAAJ,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAmBhB,MAAA,CAAA6C,eAAA,CAAA1B,MAAA,CAAuB;IAAA,EAAC;IAI7CvB,EAAA,CAAAS,YAAA,EAAe;IAEfT,EAAA,CAAAM,cAAA,iBAAqC;IAAAN,EAAA,CAAA0C,MAAA,mBAAW;IAAA1C,EAAA,CAAAS,YAAA,EAAQ;IACxDT,EAAA,CAAAM,cAAA,wBAQC;IAHCN,EAAA,CAAAa,UAAA,6BAAAqC,8FAAA3B,MAAA;MAAAvB,EAAA,CAAAgB,aAAA,CAAA4B,GAAA;MAAA,MAAAxC,MAAA,GAAAJ,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAmBhB,MAAA,CAAA+C,YAAA,CAAA5B,MAAA,CAAoB;IAAA,EAAC;IAK5CvB,EADE,CAAAS,YAAA,EAAe,EACX;IAEJT,EADF,CAAAM,cAAA,eAA6B,sBAM1B;IADCN,EAAA,CAAAa,UAAA,uBAAAuC,sFAAA;MAAApD,EAAA,CAAAgB,aAAA,CAAA4B,GAAA;MAAA,MAAAxC,MAAA,GAAAJ,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAahB,MAAA,CAAAiD,cAAA,EAAgB;IAAA,EAAC;IAEhCrD,EAAA,CAAAS,YAAA,EAAa;IACbT,EAAA,CAAAM,cAAA,sBAMC;IADCN,EAAA,CAAAa,UAAA,uBAAAyC,sFAAA;MAAAtD,EAAA,CAAAgB,aAAA,CAAA4B,GAAA;MAAA,MAAAxC,MAAA,GAAAJ,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAahB,MAAA,CAAAmD,mBAAA,EAAqB;IAAA,EAAC;IAK3CvD,EAHM,CAAAS,YAAA,EAAa,EACT,EACD,EACH;;;;IAvEJT,EAAA,CAAAW,UAAA,YAAAP,MAAA,CAAAoD,YAAA,CAAwB;IAElBxD,EAAA,CAAAU,SAAA,GAA8B;IAA9BV,EAAA,CAAAW,UAAA,cAAAP,MAAA,CAAAqD,gBAAA,CAA8B;IAK9BzD,EAAA,CAAAU,SAAA,GAAuC;IAMvCV,EANA,CAAAW,UAAA,wCAAuC,YAAAP,MAAA,CAAAsD,UAAA,CACjB,kBAAAtD,MAAA,CAAAuD,eAAA,CACW,mBACf,gBAEH,sBACM;IAMrB3D,EAAA,CAAAU,SAAA,GAAiC;IAMjCV,EANA,CAAAW,UAAA,kCAAiC,YAAAP,MAAA,CAAAwD,aAAA,CACR,kBAAAxD,MAAA,CAAAyD,kBAAA,CACW,cAAAzD,MAAA,CAAA0D,WAAA,CACX,gBAEV,sBACM;IAMrB9D,EAAA,CAAAU,SAAA,GAAkC;IAMlCV,EANA,CAAAW,UAAA,mCAAkC,YAAAP,MAAA,CAAA2D,cAAA,CACR,kBAAA3D,MAAA,CAAA4D,mBAAA,CACW,cAAA5D,MAAA,CAAA6D,cAAA,CACT,gBAEb,sBACM;IAMrBjE,EAAA,CAAAU,SAAA,GAA+B;IAM/BV,EANA,CAAAW,UAAA,gCAA+B,YAAAP,MAAA,CAAA8D,WAAA,CACR,kBAAA9D,MAAA,CAAA+D,gBAAA,CACW,cAAA/D,MAAA,CAAAgE,eAAA,CACL,gBAEd,sBACM;IAgBrBpE,EAAA,CAAAU,SAAA,GAAoC;IAApCV,EAAA,CAAAW,UAAA,cAAAP,MAAA,CAAAqD,gBAAA,CAAAY,KAAA,CAAoC;;;;;;IAS5CrE,EAAA,CAAAM,cAAA,cAIC;IADCN,EAAA,CAAAa,UAAA,mBAAAyD,0EAAA;MAAAtE,EAAA,CAAAgB,aAAA,CAAAuD,GAAA;MAAA,MAAAnE,MAAA,GAAAJ,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAShB,MAAA,CAAAiD,cAAA,EAAgB;IAAA,EAAC;IAC3BrD,EAAA,CAAAS,YAAA,EAAM;;;;;;IA7GPT,EADF,CAAAM,cAAA,cAAwE,iBAKrE;IAFCN,EAAA,CAAAa,UAAA,mBAAA2D,oEAAA;MAAAxE,EAAA,CAAAgB,aAAA,CAAAyD,GAAA;MAAA,MAAArE,MAAA,GAAAJ,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAShB,MAAA,CAAAsE,eAAA,EAAiB;IAAA,EAAC;IAG3B1E,EAAA,CAAAM,cAAA,eAAuB;IACrBN,EAAA,CAAAC,SAAA,cAKE;IACJD,EAAA,CAAAS,YAAA,EAAO;IACPT,EAAA,CAAAM,cAAA,eAAgE;;IAC9DN,EAAA,CAAAM,cAAA,cAA4D;IAC1DN,EAAA,CAAAC,SAAA,eAME;IAGRD,EAFI,CAAAS,YAAA,EAAM,EACD,EACH;IAiFNT,EA9EA,CAAAO,UAAA,IAAAoE,oDAAA,oBAKC,IAAAC,oDAAA,kBA6EA;IACH5E,EAAA,CAAAS,YAAA,EAAM;;;;IAjG+BT,EAAA,CAAAU,SAAA,GAA8B;IAA9BV,EAAA,CAAAE,WAAA,SAAAE,MAAA,CAAAyE,eAAA,CAA8B;IAe9D7E,EAAA,CAAAU,SAAA,GAAqB;IAArBV,EAAA,CAAAW,UAAA,SAAAP,MAAA,CAAAyE,eAAA,CAAqB;IA8ErB7E,EAAA,CAAAU,SAAA,EAAqB;IAArBV,EAAA,CAAAW,UAAA,SAAAP,MAAA,CAAAyE,eAAA,CAAqB;;;;;;IAiBxB7E,EAAA,CAAAM,cAAA,cAIC;IADCN,EAAA,CAAAa,UAAA,mBAAAiE,0EAAA;MAAA9E,EAAA,CAAAgB,aAAA,CAAA+D,IAAA;MAAA,MAAA3E,MAAA,GAAAJ,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAShB,MAAA,CAAA4E,cAAA,EAAgB;IAAA,EAAC;IAC3BhF,EAAA,CAAAS,YAAA,EAAM;;;;;IAmBGT,EAAA,CAAAM,cAAA,cAAqD;IACnDN,EAAA,CAAA0C,MAAA,GACF;IAAA1C,EAAA,CAAAS,YAAA,EAAM;;;;IADJT,EAAA,CAAAU,SAAA,EACF;IADEV,EAAA,CAAAiF,kBAAA,MAAAC,OAAA,CAAAC,WAAA,MACF;;;;;;IAZJnF,EAAA,CAAAM,cAAA,cAIC;IADCN,EAAA,CAAAa,UAAA,mBAAAuE,0EAAA;MAAA,MAAAF,OAAA,GAAAlF,EAAA,CAAAgB,aAAA,CAAAqE,IAAA,EAAAC,SAAA;MAAA,MAAAlF,MAAA,GAAAJ,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAShB,MAAA,CAAAmF,aAAA,CAAAL,OAAA,CAAkB;IAAA,EAAC;IAE5BlF,EAAA,CAAAM,cAAA,cAAsB;IACpBN,EAAA,CAAAC,SAAA,cAAyC;IAC3CD,EAAA,CAAAS,YAAA,EAAM;IAEJT,EADF,CAAAM,cAAA,cAAsB,cACE;IAAAN,EAAA,CAAA0C,MAAA,GAAc;IAAA1C,EAAA,CAAAS,YAAA,EAAM;IAC1CT,EAAA,CAAAO,UAAA,IAAAiF,0DAAA,kBAAqD;IAIzDxF,EADE,CAAAS,YAAA,EAAM,EACF;;;;IARGT,EAAA,CAAAU,SAAA,GAAgB;IAACV,EAAjB,CAAAW,UAAA,QAAAuE,OAAA,CAAA1C,IAAA,EAAAxC,EAAA,CAAAyF,aAAA,CAAgB,QAAAP,OAAA,CAAAQ,IAAA,CAAiB;IAGhB1F,EAAA,CAAAU,SAAA,GAAc;IAAdV,EAAA,CAAA2F,iBAAA,CAAAT,OAAA,CAAAQ,IAAA,CAAc;IACN1F,EAAA,CAAAU,SAAA,EAAqB;IAArBV,EAAA,CAAAW,UAAA,SAAAuE,OAAA,CAAAC,WAAA,CAAqB;;;;;;IAhC7DnF,EADF,CAAAM,cAAA,cAA+D,cAK5D;IADCN,EAAA,CAAAa,UAAA,mBAAA+E,oEAAA;MAAA5F,EAAA,CAAAgB,aAAA,CAAA6E,IAAA;MAAA,MAAAzF,MAAA,GAAAJ,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAShB,MAAA,CAAA0F,eAAA,EAAiB;IAAA,EAAC;IAE3B9F,EAAA,CAAAC,SAAA,mBAA4C;IAC9CD,EAAA,CAAAS,YAAA,EAAM;IAGNT,EAAA,CAAAO,UAAA,IAAAwF,oDAAA,kBAIC;IAQG/F,EALJ,CAAAM,cAAA,cAAmE,cACjC,cAID;IAC3BN,EAAA,CAAAO,UAAA,IAAAyF,oDAAA,kBAIC;IAcThG,EAHM,CAAAS,YAAA,EAAM,EACF,EACF,EACF;;;;IAtCFT,EAAA,CAAAU,SAAA,EAAgC;IAAhCV,EAAA,CAAAE,WAAA,WAAAE,MAAA,CAAA6F,eAAA,CAAgC;IAQ/BjG,EAAA,CAAAU,SAAA,GAAqB;IAArBV,EAAA,CAAAW,UAAA,SAAAP,MAAA,CAAA6F,eAAA,CAAqB;IAMSjG,EAAA,CAAAU,SAAA,EAAiC;IAAjCV,EAAA,CAAAE,WAAA,YAAAE,MAAA,CAAA6F,eAAA,CAAiC;IAO1CjG,EAAA,CAAAU,SAAA,GAAoB;IAApBV,EAAA,CAAAW,UAAA,YAAAP,MAAA,CAAA8F,eAAA,GAAoB;;;;;;IAoB9ClG,EAAA,CAAAM,cAAA,cAIC;IADCN,EAAA,CAAAa,UAAA,mBAAAsF,oEAAA;MAAAnG,EAAA,CAAAgB,aAAA,CAAAoF,IAAA;MAAA,MAAAhG,MAAA,GAAAJ,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAShB,MAAA,CAAAiG,WAAA,EAAa;IAAA,EAAC;IAEvBrG,EAAA,CAAAC,SAAA,cAAuE;IACzED,EAAA,CAAAS,YAAA,EAAM;;;;IADCT,EAAA,CAAAU,SAAA,EAAqB;IAArBV,EAAA,CAAAW,UAAA,QAAAP,MAAA,CAAAkG,aAAA,EAAAtG,EAAA,CAAAyF,aAAA,CAAqB;;;;;IAuBlBzF,EAAA,CAAAM,cAAA,cAAyD;IACvDN,EAAA,CAAA0C,MAAA,GACF;IAAA1C,EAAA,CAAAS,YAAA,EAAM;;;;IADJT,EAAA,CAAAU,SAAA,EACF;IADEV,EAAA,CAAAiF,kBAAA,MAAA7E,MAAA,CAAAmG,eAAA,MACF;;;;;IACAvG,EAAA,CAAAM,cAAA,cAA6C;IAC3CN,EAAA,CAAA0C,MAAA,GACF;IAAA1C,EAAA,CAAAS,YAAA,EAAM;;;;IADJT,EAAA,CAAAU,SAAA,EACF;IADEV,EAAA,CAAAiF,kBAAA,MAAA7E,MAAA,CAAAoG,SAAA,MACF;;;;;;IAYAxG,EALJ,CAAAM,cAAA,cAGC,cACqC,eACN;IAAAN,EAAA,CAAA0C,MAAA,WAAI;IAClC1C,EADkC,CAAAS,YAAA,EAAO,EACnC;IAEJT,EADF,CAAAM,cAAA,cAAoC,iBAKjC;IADCN,EAAA,CAAAa,UAAA,mBAAA4F,8EAAA;MAAAzG,EAAA,CAAAgB,aAAA,CAAA0F,IAAA;MAAA,MAAAtG,MAAA,GAAAJ,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAAhB,MAAA,CAAAuG,YAAA,KAA0B,OAAO,IAAIvG,MAAA,CAAAiG,WAAA,EAAa;IAAA,EAAC;;IAEnDrG,EAAA,CAAAM,cAAA,cAOC;IAECN,EADA,CAAAC,SAAA,iBAAgC,eAG9B;IACJD,EAAA,CAAAS,YAAA,EAAM;IACNT,EAAA,CAAA0C,MAAA,cACF;IAAA1C,EAAA,CAAAS,YAAA,EAAS;;IACTT,EAAA,CAAAM,cAAA,kBAIC;IADCN,EAAA,CAAAa,UAAA,mBAAA+F,+EAAA;MAAA5G,EAAA,CAAAgB,aAAA,CAAA0F,IAAA;MAAA,MAAAtG,MAAA,GAAAJ,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAAhB,MAAA,CAAAuG,YAAA,KAA0B,MAAM,IAAIvG,MAAA,CAAAiG,WAAA,EAAa;IAAA,EAAC;;IAElDrG,EAAA,CAAAM,cAAA,eAOC;IACCN,EAAA,CAAAC,SAAA,gBAA4D;IAC9DD,EAAA,CAAAS,YAAA,EAAM;IACNT,EAAA,CAAA0C,MAAA,cACF;IAEJ1C,EAFI,CAAAS,YAAA,EAAS,EACL,EACF;;;;IApCAT,EAAA,CAAAU,SAAA,GAAyC;IAAzCV,EAAA,CAAAE,WAAA,WAAAE,MAAA,CAAAuG,YAAA,aAAyC;IAoBzC3G,EAAA,CAAAU,SAAA,GAAwC;IAAxCV,EAAA,CAAAE,WAAA,WAAAE,MAAA,CAAAuG,YAAA,YAAwC;;;;;;IAwB1C3G,EAAA,CAAAM,cAAA,iBAKC;IADCN,EAAA,CAAAa,UAAA,mBAAAgG,uFAAA;MAAA,MAAAC,YAAA,GAAA9G,EAAA,CAAAgB,aAAA,CAAA+F,IAAA,EAAAzB,SAAA;MAAA,MAAAlF,MAAA,GAAAJ,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAShB,MAAA,CAAA4G,cAAA,CAAAF,YAAA,CAAAG,IAAA,CAA6B;IAAA,EAAC;IAEvCjH,EAAA,CAAA0C,MAAA,GACF;IAAA1C,EAAA,CAAAS,YAAA,EAAS;;;;;IAJPT,EAAA,CAAAE,WAAA,WAAAE,MAAA,CAAA8G,eAAA,KAAAJ,YAAA,CAAAG,IAAA,CAAkD;IAGlDjH,EAAA,CAAAU,SAAA,EACF;IADEV,EAAA,CAAAiF,kBAAA,MAAA6B,YAAA,CAAApB,IAAA,MACF;;;;;IAVA1F,EAFJ,CAAAM,cAAA,cAAiE,cAC3B,eACN;IAAAN,EAAA,CAAA0C,MAAA,eAAQ;IACtC1C,EADsC,CAAAS,YAAA,EAAO,EACvC;IACNT,EAAA,CAAAM,cAAA,cAA8B;IAC5BN,EAAA,CAAAO,UAAA,IAAA4G,8DAAA,qBAKC;IAILnH,EADE,CAAAS,YAAA,EAAM,EACF;;;;IARqBT,EAAA,CAAAU,SAAA,GAA4B;IAA5BV,EAAA,CAAAW,UAAA,YAAAP,MAAA,CAAAgH,MAAA,CAAAC,kBAAA,CAA4B;;;;;;IArF3DrH,EADF,CAAAM,cAAA,cAAkE,cAK/D;IADCN,EAAA,CAAAa,UAAA,mBAAAyG,oEAAA;MAAAtH,EAAA,CAAAgB,aAAA,CAAAuG,IAAA;MAAA,MAAAnH,MAAA,GAAAJ,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAShB,MAAA,CAAAoH,qBAAA,EAAuB;IAAA,EAAC;IAEjCxH,EAAA,CAAAC,SAAA,cAAoE;IACtED,EAAA,CAAAS,YAAA,EAAM;IAOAT,EAJN,CAAAM,cAAA,cAAoE,cAC5B,cAEL,cACK;IAChCN,EAAA,CAAAC,SAAA,cAA6C;IAC/CD,EAAA,CAAAS,YAAA,EAAM;IAEJT,EADF,CAAAM,cAAA,cAA6B,cACD;IAAAN,EAAA,CAAA0C,MAAA,IAAc;IAAA1C,EAAA,CAAAS,YAAA,EAAM;IAI9CT,EAHA,CAAAO,UAAA,KAAAkH,qDAAA,kBAAyD,KAAAC,qDAAA,kBAGZ;IAIjD1H,EADE,CAAAS,YAAA,EAAM,EACF;IAENT,EAAA,CAAAC,SAAA,eAAmC;IAoDnCD,EAjDA,CAAAO,UAAA,KAAAoH,qDAAA,mBAGC,KAAAC,qDAAA,kBA8CgE;IAgBjE5H,EAAA,CAAAC,SAAA,eAAmC;IAIjCD,EADF,CAAAM,cAAA,eAA6B,kBACuC;IAAnBN,EAAA,CAAAa,UAAA,mBAAAgH,wEAAA;MAAA7H,EAAA,CAAAgB,aAAA,CAAAuG,IAAA;MAAA,MAAAnH,MAAA,GAAAJ,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAShB,MAAA,CAAA0H,MAAA,EAAQ;IAAA,EAAC;;IAC/D9H,EAAA,CAAAM,cAAA,eAOC;IAGCN,EAFA,CAAAC,SAAA,gBAAoD,oBACd,gBACC;IACzCD,EAAA,CAAAS,YAAA,EAAM;IACNT,EAAA,CAAA0C,MAAA,kBACF;IAIR1C,EAJQ,CAAAS,YAAA,EAAS,EACL,EACF,EACF,EACF;;;;IAnHFT,EAAA,CAAAU,SAAA,EAAoC;IAApCV,EAAA,CAAAE,WAAA,WAAAE,MAAA,CAAA2H,mBAAA,CAAoC;IAG/B/H,EAAA,CAAAU,SAAA,EAAkB;IAAlBV,EAAA,CAAAW,UAAA,QAAAP,MAAA,CAAA4H,UAAA,EAAAhI,EAAA,CAAAyF,aAAA,CAAkB;IAIKzF,EAAA,CAAAU,SAAA,EAAqC;IAArCV,EAAA,CAAAE,WAAA,YAAAE,MAAA,CAAA2H,mBAAA,CAAqC;IAKtD/H,EAAA,CAAAU,SAAA,GAAkB;IAAlBV,EAAA,CAAAW,UAAA,QAAAP,MAAA,CAAA4H,UAAA,EAAAhI,EAAA,CAAAyF,aAAA,CAAkB;IAGGzF,EAAA,CAAAU,SAAA,GAAc;IAAdV,EAAA,CAAA2F,iBAAA,CAAAvF,MAAA,CAAA6H,QAAA,CAAc;IACNjI,EAAA,CAAAU,SAAA,EAAqB;IAArBV,EAAA,CAAAW,UAAA,SAAAP,MAAA,CAAAmG,eAAA,CAAqB;IAG3BvG,EAAA,CAAAU,SAAA,EAAe;IAAfV,EAAA,CAAAW,UAAA,SAAAP,MAAA,CAAAoG,SAAA,CAAe;IAU5CxG,EAAA,CAAAU,SAAA,GAAqC;IAArCV,EAAA,CAAAW,UAAA,SAAAP,MAAA,CAAAgH,MAAA,CAAAc,wBAAA,CAAqC;IAgDlClI,EAAA,CAAAU,SAAA,EAAiC;IAAjCV,EAAA,CAAAW,UAAA,SAAAP,MAAA,CAAAgH,MAAA,CAAAe,oBAAA,CAAiC;;;;;;IApUnDnI,EAAA,CAAAM,cAAA,UAA0B;;IAIpBN,EAFJ,CAAAM,cAAA,aAAqD,WAC7C,kBACwD;IAC1DN,EAAA,CAAAC,SAAA,cASE;IAGRD,EAFI,CAAAS,YAAA,EAAW,EACN,EACH;;IAIFT,EAFJ,CAAAM,cAAA,oBAA2C,aACvB,aAOf;IADCN,EADA,CAAAa,UAAA,wBAAAuH,kEAAA;MAAApI,EAAA,CAAAgB,aAAA,CAAAqH,GAAA;MAAA,MAAAjI,MAAA,GAAAJ,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAchB,MAAA,CAAAkI,kBAAA,EAAoB;IAAA,EAAC,wBAAAC,kEAAA;MAAAvI,EAAA,CAAAgB,aAAA,CAAAqH,GAAA;MAAA,MAAAjI,MAAA,GAAAJ,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CACrBhB,MAAA,CAAAoI,mBAAA,EAAqB;IAAA,EAAC;IAEpCxI,EAAA,CAAAC,SAAA,cAKE;IAGFD,EAAA,CAAAO,UAAA,IAAAkI,6CAAA,kBAA8D;IAQlEzI,EADE,CAAAS,YAAA,EAAM,EACF;IAGJT,EADF,CAAAM,cAAA,eAAoB,eACU;IAC1BN,EAAA,CAAAC,SAAA,eAAiC;IAE/BD,EADF,CAAAM,cAAA,eAAsB,eAC6B;IAE/CN,EAAA,CAAAO,UAAA,KAAAmI,0DAAA,8BAgBC;IAKT1I,EAHM,CAAAS,YAAA,EAAM,EACF,EACF,EACF;IAENT,EAAA,CAAAM,cAAA,eAA+C;IAyK7CN,EAvKA,CAAAO,UAAA,KAAAoI,8CAAA,mBAAwE,KAAAC,8CAAA,kBAkHT,KAAAC,8CAAA,kBAgD9D,KAAAC,8CAAA,oBAKiE;IAyHxE9I,EAFI,CAAAS,YAAA,EAAM,EACK,EACT;;;;IAtVET,EAAA,CAAAU,SAAA,GAAsC;;IAQpCV,EAAA,CAAAU,SAAA,EAA4C;IAA5CV,EAAA,CAAAE,WAAA,uBAAAE,MAAA,CAAA2I,eAAA,CAA4C;IAC5C/I,EAHA,CAAAW,UAAA,QAAAP,MAAA,CAAA4I,WAAA,EAAAhJ,EAAA,CAAAyF,aAAA,CAAmB,QAAArF,MAAA,CAAA6I,iBAAA,WAGgB;IAILjJ,EAAA,CAAAU,SAAA,EAA4B;IAA5BV,EAAA,CAAAW,UAAA,SAAAP,MAAA,CAAAQ,WAAA,CAAAsI,MAAA,KAA4B;IAcnClJ,EAAA,CAAAU,SAAA,GAAyB;IAAzBV,EAAA,CAAAmJ,UAAA,CAAA/I,MAAA,CAAAgJ,eAAA,CAAyB;IAG3BpJ,EAAA,CAAAU,SAAA,EAAoB;IAApBV,EAAA,CAAAW,UAAA,YAAAP,MAAA,CAAAgH,MAAA,CAAAiC,QAAA,CAAoB;IAwBvCrJ,EAAA,CAAAU,SAAA,GAA4B;IAA5BV,EAAA,CAAAW,UAAA,SAAAP,MAAA,CAAAgH,MAAA,CAAAkC,eAAA,CAA4B;IAkH5BtJ,EAAA,CAAAU,SAAA,EAA0B;IAA1BV,EAAA,CAAAW,UAAA,SAAAP,MAAA,CAAAgH,MAAA,CAAAmC,aAAA,CAA0B;IA6C7BvJ,EAAA,CAAAU,SAAA,EAAqD;IAArDV,EAAA,CAAAW,UAAA,SAAAP,MAAA,CAAAgH,MAAA,CAAAoC,eAAA,KAAApJ,MAAA,CAAAgH,MAAA,CAAAmC,aAAA,CAAqD;IAQlDvJ,EAAA,CAAAU,SAAA,EAAgC;IAAhCV,EAAA,CAAAW,UAAA,SAAAP,MAAA,CAAAgH,MAAA,CAAAqC,mBAAA,CAAgC;;;;;IA0ItCzJ,EAAA,CAAAC,SAAA,eAAwE;;;;IAAjDD,EAAA,CAAAW,UAAA,QAAA+I,QAAA,CAAAlH,IAAA,EAAAxC,EAAA,CAAAyF,aAAA,CAAiB;;;;;;IAP1CzF,EAAA,CAAAM,cAAA,eAMC;IAHCN,EAAA,CAAAa,UAAA,mBAAA8I,mEAAA;MAAA,MAAAD,QAAA,GAAA1J,EAAA,CAAAgB,aAAA,CAAA4I,IAAA,EAAAtE,SAAA;MAAA,MAAAlF,MAAA,GAAAJ,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CACWhB,MAAA,CAAAwB,sBAAA,CAAuB;QAAAK,KAAA,EAAAyH,QAAA,CAAAzH,KAAA;QAAAD,KAAA,EAAA0H,QAAA,CAAA1H;MAAA,CACrC,EAAE,CAAC,CAAC;IAAA;IAEDhC,EAAA,CAAAO,UAAA,IAAAsJ,mDAAA,mBAAwE;IAEtE7J,EADF,CAAAM,cAAA,eAA8B,eACA;IAAAN,EAAA,CAAA0C,MAAA,GAAgB;IAAA1C,EAAA,CAAAS,YAAA,EAAM;IAClDT,EAAA,CAAAM,cAAA,eAAkC;IAAAN,EAAA,CAAA0C,MAAA,GAAsB;IAE5D1C,EAF4D,CAAAS,YAAA,EAAM,EAC1D,EACF;;;;IALET,EAAA,CAAAU,SAAA,EAAe;IAAfV,EAAA,CAAAW,UAAA,SAAA+I,QAAA,CAAAlH,IAAA,CAAe;IAESxC,EAAA,CAAAU,SAAA,GAAgB;IAAhBV,EAAA,CAAA2F,iBAAA,CAAA+D,QAAA,CAAA1H,KAAA,CAAgB;IACVhC,EAAA,CAAAU,SAAA,GAAsB;IAAtBV,EAAA,CAAA2F,iBAAA,CAAA+D,QAAA,CAAAvE,WAAA,CAAsB;;;;;IAX9DnF,EANF,CAAAM,cAAA,cAKC,cAC4B;IACzBN,EAAA,CAAAO,UAAA,IAAAuJ,6CAAA,kBAMC;IAQL9J,EADE,CAAAS,YAAA,EAAM,EACF;;;;IAjBJT,EADA,CAAA+J,WAAA,QAAA3J,MAAA,CAAA4J,cAAA,CAAAC,IAAA,CAAAC,MAAA,YAAmD,SAAA9J,MAAA,CAAA4J,cAAA,CAAAC,IAAA,CAAAE,IAAA,QACL;IAIzBnK,EAAA,CAAAU,SAAA,GAAuB;IAAvBV,EAAA,CAAAW,UAAA,YAAAP,MAAA,CAAA4J,cAAA,CAAAI,KAAA,CAAuB;;;AD5P9C,WAAaC,wBAAwB;EAA/B,MAAOA,wBAAwB;IAmHzBC,MAAA;IACAC,GAAA;IACAC,QAAA;IACAC,UAAA;IACAC,YAAA;IACAC,WAAA;IACAC,WAAA;IAtHDxD,MAAM;IACNyD,sBAAsB,CAAO,CAAC;IAC9BC,gBAAgB,CAAO,CAAC;IACxBC,YAAY,CAAO,CAAC;IAE7B;IACApE,YAAY,GAAqB,OAAO;IACxCoB,mBAAmB,GAAY,KAAK;IACpCE,QAAQ,GAAW,EAAE;IACrBzB,SAAS,GAAW,EAAE;IACtBD,eAAe,GAAW,EAAE;IAC5ByB,UAAU,GAAW,EAAE;IACvB1B,aAAa,GAAW,EAAE;IAC1B0E,WAAW,GAAY,KAAK,CAAC,CAAC;IAE9B;IACApK,WAAW,GAAa,CACtB,sDAAsD,EACtD,sCAAsC,EACtC,qCAAqC,CACtC;IACDqK,eAAe,GAAa,EAAE,CAAC,CAAC;IAChCC,WAAW,GAAa,CAAC,SAAS,EAAE,mBAAmB,EAAE,gBAAgB,CAAC;IAC1E7K,gBAAgB,GAAW,CAAC;IAC5B2I,WAAW,GAAW,EAAE;IACxBC,iBAAiB,GAAW,EAAE;IAC9BkC,qBAAqB;IACrBpC,eAAe,GAAY,KAAK;IAEhC;IACA7B,eAAe,GAAW,IAAI;IAE9B;IACA,IAAIkC,eAAeA,CAAA;MACjB,MAAMgC,SAAS,GAAG,IAAI,CAAChE,MAAM,EAAEiC,QAAQ,EAAEH,MAAM,IAAI,CAAC;MACpD,IAAImC,OAAO,GAAG,EAAE;MAEhB,IAAID,SAAS,KAAK,CAAC,EAAE;QACnBC,OAAO,IAAI,cAAc;MAC3B,CAAC,MAAM,IAAID,SAAS,IAAI,CAAC,EAAE;QACzBC,OAAO,IAAI,kBAAkB;MAC/B,CAAC,MAAM,IAAID,SAAS,IAAI,CAAC,EAAE;QACzBC,OAAO,IAAI,aAAa;MAC1B;MAEA,OAAOA,OAAO,CAACC,IAAI,EAAE;IACvB;IAEA;IACUC,eAAe,GAAG,IAAIpM,YAAY,EAAU;IAC5CqM,oBAAoB,GAAG,IAAIrM,YAAY,EAG7C;IACMsM,aAAa,GAAG,IAAItM,YAAY,EAAU;IAC1CuM,WAAW,GAAG,IAAIvM,YAAY,EAAoB;IAClDwM,cAAc,GAAG,IAAIxM,YAAY,EAAU;IAC3CyM,eAAe,GAAG,IAAIzM,YAAY,EAAO;IAEnD;IACA0F,eAAe,GAAY,KAAK;IAEhC;IACAoB,eAAe,GAAY,KAAK;IAEhC;IACAvC,UAAU,GAAqB,EAAE;IACjCE,aAAa,GAAqB,EAAE;IACpCG,cAAc,GAAqB,EAAE;IACrCG,WAAW,GAAqB,EAAE;IAElC;IACAJ,WAAW,GAAW,EAAE;IACxBG,cAAc,GAAW,EAAE;IAC3BG,eAAe,GAAW,EAAE;IAC5ByH,YAAY,GAAW,EAAE;IAEzB;IACAlI,eAAe,GAAW,EAAE;IAC5BE,kBAAkB,GAAW,EAAE;IAC/BG,mBAAmB,GAAW,EAAE;IAChCG,gBAAgB,GAAW,EAAE;IAE7B;IACAV,gBAAgB;IAEhB;IACQqI,aAAa,GAAmB,EAAE;IAEMC,cAAc;IACrBC,UAAU;IACnDxI,YAAY,GAAqB,MAAM;IAEvC;IACAwG,cAAc,GAMV;MACFiC,IAAI,EAAE,KAAK;MACXhC,IAAI,EAAE,IAAI;MACVG,KAAK,EAAE,EAAE;MACT8B,WAAW,EAAE,EAAE;MACfC,SAAS,EAAE;KACZ;IAED;IACQC,aAAa,GAAG,IAAI3M,YAAY,EAAE;IAE1C4M,YACU/B,MAAc,EACdC,GAAsB,EACtBC,QAAmB,EACnBC,UAAsB,EACtBC,YAAiC,EACjCC,WAAwB,EACxBC,WAAwB;MANxB,KAAAN,MAAM,GAANA,MAAM;MACN,KAAAC,GAAG,GAAHA,GAAG;MACH,KAAAC,QAAQ,GAARA,QAAQ;MACR,KAAAC,UAAU,GAAVA,UAAU;MACV,KAAAC,YAAY,GAAZA,YAAY;MACZ,KAAAC,WAAW,GAAXA,WAAW;MACX,KAAAC,WAAW,GAAXA,WAAW;IAClB;IAEH0B,QAAQA,CAAA;MACN,IAAI,CAACC,gBAAgB,EAAE;MACvB,IAAI,CAACC,uBAAuB,EAAE;MAC9B,IAAI,CAACC,sBAAsB,EAAE;MAC7B,IAAI,CAACC,YAAY,EAAE;MACnB,IAAI,CAACC,cAAc,EAAE;MACrB,IAAI,CAACC,cAAc,EAAE;MAErB;MACA,IAAI,IAAI,CAACxF,MAAM,CAACkC,eAAe,EAAE;QAC/B,IAAI,CAACuD,qBAAqB,EAAE;MAC9B;IACF;IAEAC,eAAeA,CAAA;MACb,IAAI,CAACC,2BAA2B,CAAC,IAAI,CAACzC,MAAM,CAAC0C,GAAG,CAAC;IACnD;IAEAC,WAAWA,CAAA;MACT,IAAI,CAACb,aAAa,CAACc,WAAW,EAAE;MAChC,IAAI,IAAI,CAAC/B,qBAAqB,EAAE;QAC9BgC,aAAa,CAAC,IAAI,CAAChC,qBAAqB,CAAC;MAC3C;IACF;IAEA;IACA;IACA;IAEQyB,cAAcA,CAAA;MACpB;MACA,IAAI,IAAI,CAACxF,MAAM,CAACxG,WAAW,IAAI,IAAI,CAACwG,MAAM,CAACxG,WAAW,CAACsI,MAAM,GAAG,CAAC,EAAE;QACjE,IAAI,CAACtI,WAAW,GAAG,IAAI,CAACwG,MAAM,CAACxG,WAAW;QAC1CwM,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE,IAAI,CAACzM,WAAW,CAAC;MAChE,CAAC,MAAM;QACLwM,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE,IAAI,CAACzM,WAAW,CAAC;MACjE;MAEA;MACA,IAAI,IAAI,CAACwG,MAAM,CAACkG,qBAAqB,IAAI,IAAI,CAAClG,MAAM,CAAC6D,eAAe,IAAI,IAAI,CAAC7D,MAAM,CAAC6D,eAAe,CAAC/B,MAAM,GAAG,CAAC,EAAE;QAC9G,IAAI,CAAC+B,eAAe,GAAG,IAAI,CAAC7D,MAAM,CAAC6D,eAAe;QAClDmC,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE,IAAI,CAACpC,eAAe,CAAC;MACzE;MAEA,IAAI,IAAI,CAAC7D,MAAM,CAAC8D,WAAW,IAAI,IAAI,CAAC9D,MAAM,CAAC8D,WAAW,CAAChC,MAAM,GAAG,CAAC,EAAE;QACjE,IAAI,CAACgC,WAAW,GAAG,IAAI,CAAC9D,MAAM,CAAC8D,WAAW;MAC5C;MAEA;MACA,IACE,IAAI,CAAC9D,MAAM,CAACmG,mBAAmB,KAAK,KAAK,IACzC,IAAI,CAACC,oBAAoB,EAAE,CAACtE,MAAM,GAAG,CAAC,EACtC;QACA;QACA,MAAMuE,WAAW,GAAG,IAAI,CAACD,oBAAoB,EAAE;QAC/C,IAAI,CAACxE,WAAW,GAAGyE,WAAW,CAAC,CAAC,CAAC;QACjC,IAAI,CAACxE,iBAAiB,GAAG,IAAI,CAACiC,WAAW,CAAC,CAAC,CAAC;QAC5CkC,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE,IAAI,CAACrE,WAAW,CAAC;QACjEoE,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE,IAAI,CAACpE,iBAAiB,CAAC;MACvE,CAAC,MAAM;QACL;QACA,IAAI,CAACD,WAAW,GAAG,IAAI,CAAC5B,MAAM,CAACsG,OAAO;QACtC,IAAI,CAACzE,iBAAiB,GAAG,IAAI,CAAC7B,MAAM,CAACuG,WAAW,IAAI,QAAQ;QAC5DP,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAACrE,WAAW,CAAC;MACxD;MAEA;MACA,IAAI,IAAI,CAAC5B,MAAM,CAACmG,mBAAmB,KAAK,KAAK,EAAE;QAC7C,IAAI,CAACK,kBAAkB,EAAE;MAC3B;IACF;IAEA;;;;IAIQJ,oBAAoBA,CAAA;MAC1B,IAAI,IAAI,CAACpG,MAAM,CAACkG,qBAAqB,IAAI,IAAI,CAAC3G,YAAY,KAAK,MAAM,IAAI,IAAI,CAACsE,eAAe,CAAC/B,MAAM,GAAG,CAAC,EAAE;QACxG,OAAO,IAAI,CAAC+B,eAAe;MAC7B;MACA,OAAO,IAAI,CAACrK,WAAW;IACzB;IAEQgN,kBAAkBA,CAAA;MACxB;MACA,IAAI,IAAI,CAACzC,qBAAqB,EAAE;QAC9BgC,aAAa,CAAC,IAAI,CAAChC,qBAAqB,CAAC;MAC3C;MAEA;MACA,MAAM0C,QAAQ,GAAG,IAAI,CAACzG,MAAM,CAAC+D,qBAAqB,IAAI,IAAI;MAE1D;MACA,IAAI,CAACA,qBAAqB,GAAG2C,WAAW,CAAC,MAAK;QAC5C,IAAI,CAACC,iBAAiB,EAAE;MAC1B,CAAC,EAAEF,QAAQ,CAAC;IACd;IAEQE,iBAAiBA,CAAA;MACvB,IAAI,IAAI,CAAChF,eAAe,EAAE;MAE1B,IAAI,CAACA,eAAe,GAAG,IAAI;MAE3B;MACA,MAAMiF,cAAc,GAAG,IAAI,CAAC5G,MAAM,CAAC6G,kBAAkB,IAAI,MAAM;MAE/D;MACA,MAAMC,WAAW,GAAGC,QAAQ,CAACC,aAAa,CAAC,gBAAgB,CAAC;MAC5D,IAAIF,WAAW,EAAE;QACfA,WAAW,CAACG,SAAS,CAACC,GAAG,CAAC,oBAAoB,CAAC;QAE/C;QACAJ,WAAW,CAACG,SAAS,CAACC,GAAG,CAAC,QAAQN,cAAc,EAAE,CAAC;MACrD;MAEA;MACA,MAAMO,OAAO,GAAG;QACdC,IAAI,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAAE;QACzCC,MAAM,EAAE;UAAEF,QAAQ,EAAE,GAAG;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAAE;QAC3CE,SAAS,EAAE;UAAEH,QAAQ,EAAE,GAAG;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAE;OAC/C;MAED,MAAMG,MAAM,GAAGN,OAAO,CAACP,cAAc,CAAC,IAAIO,OAAO,CAAC,MAAM,CAAC;MAEzD;MACAO,UAAU,CAAC,MAAK;QACd;QACA,MAAMrB,WAAW,GAAG,IAAI,CAACD,oBAAoB,EAAE;QAE/C;QACA,IAAI,CAACnN,gBAAgB,GACnB,CAAC,IAAI,CAACA,gBAAgB,GAAG,CAAC,IAAIoN,WAAW,CAACvE,MAAM;QAClD,IAAI,CAACF,WAAW,GAAGyE,WAAW,CAAC,IAAI,CAACpN,gBAAgB,CAAC;QACrD,IAAI,CAAC4I,iBAAiB,GAAG,IAAI,CAACiC,WAAW,CAAC,IAAI,CAAC7K,gBAAgB,CAAC;QAEhE;QACA,IAAI,CAACkK,GAAG,CAACwE,aAAa,EAAE;MAC1B,CAAC,EAAEF,MAAM,CAACJ,QAAQ,CAAC;MAEnB;MACAK,UAAU,CAAC,MAAK;QACd,IAAIZ,WAAW,EAAE;UACfA,WAAW,CAACG,SAAS,CAACW,MAAM,CAAC,oBAAoB,CAAC;UAClDd,WAAW,CAACG,SAAS,CAACW,MAAM,CAAC,QAAQhB,cAAc,EAAE,CAAC;QACxD;QACA,IAAI,CAACjF,eAAe,GAAG,KAAK;MAC9B,CAAC,EAAE8F,MAAM,CAACH,QAAQ,CAAC;IACrB;IAEOpG,kBAAkBA,CAAA;MACvB,IAAI,IAAI,CAAC6C,qBAAqB,EAAE;QAC9BgC,aAAa,CAAC,IAAI,CAAChC,qBAAqB,CAAC;QACzC,IAAI,CAACA,qBAAqB,GAAG8D,SAAS;MACxC;IACF;IAEOzG,mBAAmBA,CAAA;MACxB,IAAI,CAAC,IAAI,CAAC2C,qBAAqB,EAAE;QAC/B,IAAI,CAACyC,kBAAkB,EAAE;MAC3B;IACF;IAEQrB,gBAAgBA,CAAA;MACtB,IAAI,CAAC,IAAI,CAACnF,MAAM,EAAE;QAChBgG,OAAO,CAAC8B,IAAI,CAAC,8CAA8C,CAAC;QAC5D;MACF;MAEA;MACA,IAAI,CAAC9H,MAAM,CAACkC,eAAe,GAAG,IAAI,CAAClC,MAAM,CAACkC,eAAe,IAAI,KAAK;MAClE,IAAI,CAAClC,MAAM,CAACoC,eAAe,GAAG,IAAI,CAACpC,MAAM,CAACoC,eAAe,IAAI,IAAI;MACjE,IAAI,CAACpC,MAAM,CAACmC,aAAa,GAAG,IAAI,CAACnC,MAAM,CAACmC,aAAa,IAAI,KAAK;MAC9D,IAAI,CAACnC,MAAM,CAACqC,mBAAmB,GAAG,IAAI,CAACrC,MAAM,CAACqC,mBAAmB,IAAI,IAAI;MACzE,IAAI,CAACrC,MAAM,CAACuG,WAAW,GAAG,IAAI,CAACvG,MAAM,CAACuG,WAAW,IAAI,aAAa;MAClE,IAAI,CAACvG,MAAM,CAAC+H,WAAW,GAAG,IAAI,CAAC/H,MAAM,CAAC+H,WAAW,IAAI,GAAG;MACxD,IAAI,CAAC/H,MAAM,CAACgI,UAAU,GAAG,IAAI,CAAChI,MAAM,CAACgI,UAAU,IAAI,EAAE;MAErD;MACA,IAAI,CAAChI,MAAM,CAACiI,aAAa,GAAG,IAAI,CAACjI,MAAM,CAACiI,aAAa,IAAI,CACvD;QACE3J,IAAI,EAAE,SAAS;QACfzD,KAAK,EAAE,UAAU;QACjBO,IAAI,EAAE,sCAAsC;QAC5C2C,WAAW,EAAE;OACd,EACD;QACEO,IAAI,EAAE,mBAAmB;QACzBzD,KAAK,EAAE,oBAAoB;QAC3BO,IAAI,EAAE,gDAAgD;QACtD2C,WAAW,EAAE;OACd,EACD;QACEO,IAAI,EAAE,gBAAgB;QACtBzD,KAAK,EAAE,iBAAiB;QACxBO,IAAI,EAAE,6CAA6C;QACnD2C,WAAW,EAAE;OACd,EACD;QACEO,IAAI,EAAE,WAAW;QACjBzD,KAAK,EAAE,YAAY;QACnBO,IAAI,EAAE,wCAAwC;QAC9C2C,WAAW,EAAE;OACd,CACF;MAED;MACA,IAAI,CAACiC,MAAM,CAACc,wBAAwB,GAClC,IAAI,CAACd,MAAM,CAACc,wBAAwB,IAAI,IAAI;MAC9C,IAAI,CAACd,MAAM,CAACe,oBAAoB,GAC9B,IAAI,CAACf,MAAM,CAACe,oBAAoB,IAAI,KAAK;MAC3C,IAAI,CAACf,MAAM,CAACC,kBAAkB,GAAG,IAAI,CAACD,MAAM,CAACC,kBAAkB,IAAI,CACjE;QAAEJ,IAAI,EAAE,IAAI;QAAEvB,IAAI,EAAE;MAAS,CAAE,EAC/B;QAAEuB,IAAI,EAAE,KAAK;QAAEvB,IAAI,EAAE;MAAU,CAAE,EACjC;QAAEuB,IAAI,EAAE,IAAI;QAAEvB,IAAI,EAAE;MAAS,CAAE,CAChC;IACH;IAEQiH,cAAcA,CAAA;MACpB,IAAI,CAAClJ,gBAAgB,GAAG,IAAI,CAACmH,WAAW,CAAC0E,KAAK,CAAC;QAC7CC,GAAG,EAAE,CAAC,EAAE,EAAEzP,UAAU,CAAC0P,QAAQ,CAAC;QAC9BC,MAAM,EAAE,CAAC,EAAE,EAAE3P,UAAU,CAAC0P,QAAQ,CAAC;QACjCE,OAAO,EAAE,CAAC,EAAE,EAAE5P,UAAU,CAAC0P,QAAQ,CAAC;QAClCG,IAAI,EAAE,CAAC,EAAE,EAAE7P,UAAU,CAAC0P,QAAQ;OAC/B,CAAC;IACJ;IAEQhD,uBAAuBA,CAAA;MAC7B;MACA,MAAMoD,kBAAkB,GAAG,IAAI,CAACtF,MAAM,CAACuF,MAAM,CAC1CC,IAAI,CAACtQ,MAAM,CAAEuQ,KAAK,IAAKA,KAAK,YAAYxQ,eAAe,CAAC,CAAC,CACzDyQ,SAAS,CAAC,MAAK;QACd,IAAI,CAACC,mBAAmB,EAAE;QAC1B,IAAI,CAAClI,mBAAmB,GAAG,KAAK;QAChC,IAAI,CAAClD,eAAe,GAAG,KAAK;QAC5B,IAAI,CAACoB,eAAe,GAAG,KAAK;MAC9B,CAAC,CAAC;MACJ,IAAI,CAACmG,aAAa,CAACkC,GAAG,CAACsB,kBAAkB,CAAC;MAE1C,MAAMM,SAAS,GAAG,IAAI,CAAC5F,MAAM,CAACuF,MAAM,CACjCC,IAAI,CAACtQ,MAAM,CAAEuQ,KAAK,IAAKA,KAAK,YAAYzQ,aAAa,CAAC,CAAC,CACvD0Q,SAAS,CAAED,KAAoB,IAAI;QAClC;QACA,IAAI,CAAC/E,WAAW,GAAG,IAAI,CAACmF,gBAAgB,CAACJ,KAAK,CAAC/C,GAAG,CAAC;QACnD,IAAI,CAACD,2BAA2B,CAACgD,KAAK,CAAC/C,GAAG,CAAC;QAC3C;QACA,IAAI,CAACiD,mBAAmB,EAAE;MAC5B,CAAC,CAAC;MACJ,IAAI,CAAC7D,aAAa,CAACkC,GAAG,CAAC4B,SAAS,CAAC;MAEjC;MACA,IAAI,CAAClF,WAAW,GAAG,IAAI,CAACmF,gBAAgB,CAAC,IAAI,CAAC7F,MAAM,CAAC0C,GAAG,CAAC;IAC3D;IAEQmD,gBAAgBA,CAACnD,GAAW;MAClC;MACA,MAAMoD,WAAW,GAAG,CAClB,QAAQ,EACR,aAAa,EACb,mBAAmB,EACnB,gBAAgB,EAChB,gBAAgB,CACjB;MACD,OAAOA,WAAW,CAACC,IAAI,CAAEpO,KAAK,IAAK+K,GAAG,CAACsD,QAAQ,CAACrO,KAAK,CAAC,CAAC;IACzD;IAEQwK,sBAAsBA,CAAA;MAC5B;MACA,IAAI,IAAI,CAAC1B,YAAY,IAAI,IAAI,CAACA,YAAY,CAACwF,eAAe,EAAE;QAC1D,IAAI,CAACnE,aAAa,CAACkC,GAAG,CACpB,IAAI,CAACvD,YAAY,CAACwF,eAAe,CAACP,SAAS,CACxCQ,KAAuB,IAAI;UAC1B,IAAI,CAAC7J,YAAY,GAAG6J,KAAK;UACzB,IAAI,CAACC,iBAAiB,EAAE;UACxB,IAAI,CAAClG,GAAG,CAACmG,YAAY,EAAE;QACzB,CAAC,CACF,CACF;MACH;MACA,IAAI,CAACD,iBAAiB,EAAE;IAC1B;IAEQ/D,YAAYA,CAAA;MAClB,IAAI,CAACzE,QAAQ,GAAG,IAAI,CAACyC,YAAY,CAACiG,SAAS,EAAE,IAAI,MAAM;MACvD,IAAI,CAACnK,SAAS,GAAI,IAAI,CAACkE,YAAoB,CAACkG,cAAc,GAAE,CAAE,IAAI,EAAE,CAAC,CAAC;MACtE,IAAI,CAACrK,eAAe,GACjB,IAAI,CAACmE,YAAoB,CAACmG,oBAAoB,GAAE,CAAE,IAAI,UAAU,CAAC,CAAC;MACrE,IAAI,CAACC,kBAAkB,EAAE;IAC3B;IAEQA,kBAAkBA,CAAA;MACxB;MACA,IAAI,IAAI,CAAC7I,QAAQ,EAAE;QACjB,MAAM8I,SAAS,GAAG,IAAI,CAAC9I,QAAQ,CAACqD,IAAI,EAAE,CAAC0F,KAAK,CAAC,GAAG,CAAC;QACjD,IAAIC,QAAQ,GAAG,EAAE;QAEjB,IAAIF,SAAS,CAAC7H,MAAM,IAAI,CAAC,EAAE;UACzB;UACA+H,QAAQ,GAAGF,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,SAAS,CAACA,SAAS,CAAC7H,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACjE,CAAC,MAAM,IAAI6H,SAAS,CAAC7H,MAAM,KAAK,CAAC,EAAE;UACjC;UACA+H,QAAQ,GAAGF,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,MAAM;UACLE,QAAQ,GAAG,GAAG,CAAC,CAAC;QAClB;QAEAA,QAAQ,GAAGA,QAAQ,CAACC,WAAW,EAAE;QAEjC;QACA,MAAMC,MAAM,GAAG,CACb,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACV;QACD,MAAMC,UAAU,GAAG,IAAI,CAACnJ,QAAQ,CAACiB,MAAM,GAAGiI,MAAM,CAACjI,MAAM;QACvD,MAAMmI,eAAe,GAAGF,MAAM,CAACC,UAAU,CAAC;QAE1C,IAAI,CAACpJ,UAAU,GAAG,6BAA6BsJ,IAAI,CAAC;;iDAETD,eAAe;0IAC0EJ,QAAQ;;OAE3I,CAAC,EAAE;MACN;IACF;IAEQR,iBAAiBA,CAAA;MACvB;MACA,IAAI,CAACnK,aAAa,GAChB,IAAI,CAACK,YAAY,KAAK,OAAO,GACzB,oDAAoD,GACpD,qDAAqD;MAE3D;MACA,IAAI,IAAI,CAACS,MAAM,CAACkG,qBAAqB,IAAI,IAAI,CAAClG,MAAM,CAACmG,mBAAmB,KAAK,KAAK,EAAE;QAClF,MAAME,WAAW,GAAG,IAAI,CAACD,oBAAoB,EAAE;QAC/C,IAAIC,WAAW,CAACvE,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC7I,gBAAgB,GAAGoN,WAAW,CAACvE,MAAM,EAAE;UACxE,IAAI,CAACF,WAAW,GAAGyE,WAAW,CAAC,IAAI,CAACpN,gBAAgB,CAAC;UACrD+M,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE,IAAI,CAACrE,WAAW,CAAC;QACtE;MACF;IACF;IAEA;IACA6D,qBAAqBA,CAAA;MACnB,MAAM0E,IAAI,GAAG,IAAI,CAAC7G,YAAY,CAAC8G,SAAS,CAAC,UAAU,CAAC;MACpD,IAAID,IAAI,EAAE;QACR,MAAME,KAAK,GAAGF,IAAI,CAACP,KAAK,CAAC,IAAI,CAAC;QAC9B,MAAMU,WAAW,GAAGD,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE;QAClC,MAAME,aAAa,GAAGF,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE;QAEpC;QACA,MAAMG,GAAG,GAAGD,aAAa,CAACX,KAAK,CAAC,GAAG,CAAC,CAACa,GAAG,CAACC,MAAM,CAAC;QAEhD;QACA,IAAI,CAACrO,gBAAgB,CAACsO,UAAU,CAAC;UAC/BxC,GAAG,EAAEqC,GAAG,CAAC,CAAC,CAAC,EAAEI,QAAQ,EAAE,IAAI,EAAE;UAC7BvC,MAAM,EAAEmC,GAAG,CAAC,CAAC,CAAC,EAAEI,QAAQ,EAAE,IAAI,EAAE;UAChCtC,OAAO,EAAEkC,GAAG,CAAC,CAAC,CAAC,EAAEI,QAAQ,EAAE,IAAI,EAAE;UACjCrC,IAAI,EAAEiC,GAAG,CAAC,CAAC,CAAC,EAAEI,QAAQ,EAAE,IAAI;SAC7B,CAAC;QAEF;QACA,IAAI,CAAClO,WAAW,GAAG8N,GAAG,CAAC,CAAC,CAAC,EAAEI,QAAQ,EAAE,IAAI,EAAE;QAC3C,IAAI,CAAC/N,cAAc,GAAG2N,GAAG,CAAC,CAAC,CAAC,EAAEI,QAAQ,EAAE,IAAI,EAAE;QAC9C,IAAI,CAAC5N,eAAe,GAAGwN,GAAG,CAAC,CAAC,CAAC,EAAEI,QAAQ,EAAE,IAAI,EAAE;QAC/C,IAAI,CAACnG,YAAY,GAAG+F,GAAG,CAAC,CAAC,CAAC,EAAEI,QAAQ,EAAE,IAAI,EAAE;QAE5C;QACA,MAAMC,SAAS,GAAGP,WAAW,CAACV,KAAK,CAAC,GAAG,CAAC;QACxC,IAAI,CAACrN,eAAe,GAAGsO,SAAS,CAAC,CAAC,CAAC,IAAI,EAAE;QACzC,IAAI,CAACpO,kBAAkB,GAAGoO,SAAS,CAAC,CAAC,CAAC,IAAI,EAAE;QAC5C,IAAI,CAACjO,mBAAmB,GAAGiO,SAAS,CAAC,CAAC,CAAC,IAAI,EAAE;QAC7C,IAAI,CAAC9N,gBAAgB,GAAG8N,SAAS,CAAC,CAAC,CAAC,IAAI,EAAE;QAE1C;QACA,IAAI,CAACC,QAAQ,EAAE;MACjB,CAAC,MAAM;QACL,IAAI,CAACA,QAAQ,EAAE;MACjB;IACF;IAEAA,QAAQA,CAAA;MACN,IAAI,IAAI,CAACpH,gBAAgB,EAAE;QACzB,IAAI,CAACA,gBAAgB,CAACqH,wBAAwB,EAAE,CAACnC,SAAS,CAAC;UACzDoC,IAAI,EAAGC,IAAoB,IAAI;YAC7B,IAAI,CAACvG,aAAa,GAAGuG,IAAI;YACzB,IAAI,CAACC,iBAAiB,EAAE;YAExB;YACA,IAAI,IAAI,CAACxO,WAAW,EAAE;cACpB,IAAI,CAACyO,WAAW,CAAC,IAAI,CAACzO,WAAW,CAAC;cAClC,IAAI,IAAI,CAACG,cAAc,EAAE;gBACvB,IAAI,CAACuO,YAAY,CAAC,IAAI,CAACvO,cAAc,CAAC;gBACtC,IAAI,IAAI,CAACG,eAAe,EAAE;kBACxB,IAAI,CAACqO,SAAS,CAAC,IAAI,CAACrO,eAAe,CAAC;gBACtC;cACF;YACF;UACF,CAAC;UACDsO,KAAK,EAAGA,KAAU,IAAI;YACpBtF,OAAO,CAACsF,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;UAC1D;SACD,CAAC;MACJ;IACF;IAEAJ,iBAAiBA,CAAA;MACf,IAAI,CAAC5O,UAAU,GAAG,IAAI,CAACoI,aAAa,CAAC+F,GAAG,CAAEtC,GAAG,KAAM;QACjD7J,IAAI,EAAE6J,GAAG,CAACoD,gBAAgB;QAC1BC,KAAK,EAAErD,GAAG,CAACsD,KAAK,CAACb,QAAQ;OAC1B,CAAC,CAAC;IACL;IAEAnP,WAAWA,CAACkN,KAAU;MACpB,MAAM+C,aAAa,GAAG/C,KAAK,CAACgD,eAAe,GAAG,CAAC,CAAC,EAAEH,KAAK;MACvD,MAAMjP,eAAe,GAAGoM,KAAK,CAACgD,eAAe,GAAG,CAAC,CAAC,EAAErN,IAAI;MACxD,IAAIoN,aAAa,EAAE;QACjB,IAAI,CAAChP,WAAW,GAAGgP,aAAa;QAChC,IAAI,CAACnP,eAAe,GAAGA,eAAe;QACtC,IAAI,CAACF,gBAAgB,CAACsO,UAAU,CAAC;UAAExC,GAAG,EAAEuD;QAAa,CAAE,CAAC;QACxD,IAAI,CAACP,WAAW,CAACO,aAAa,CAAC;QAC/B;QACA,IAAI,CAACrP,gBAAgB,CAACsO,UAAU,CAAC;UAAEtC,MAAM,EAAE,EAAE;UAAEC,OAAO,EAAE,EAAE;UAAEC,IAAI,EAAE;QAAE,CAAE,CAAC;QACvE,IAAI,CAAC1L,cAAc,GAAG,EAAE;QACxB,IAAI,CAACG,eAAe,GAAG,EAAE;QACzB,IAAI,CAACyH,YAAY,GAAG,EAAE;QACtB,IAAI,CAAChI,kBAAkB,GAAG,EAAE;QAC5B,IAAI,CAACG,mBAAmB,GAAG,EAAE;QAC7B,IAAI,CAACG,gBAAgB,GAAG,EAAE;QAC1B,IAAI,CAACJ,cAAc,GAAG,EAAE;QACxB,IAAI,CAACG,WAAW,GAAG,EAAE;MACvB;IACF;IAEAqO,WAAWA,CAACM,KAAa;MACvB,MAAMtD,GAAG,GAAG,IAAI,CAACzD,aAAa,CAACkH,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACJ,KAAK,CAACb,QAAQ,EAAE,KAAKa,KAAK,CAAC;MACxE,IAAItD,GAAG,EAAE;QACP,IAAI,CAAC3L,aAAa,GAAG2L,GAAG,CAAC2D,OAAO,CAACrB,GAAG,CAAEpC,MAAM,KAAM;UAChD/J,IAAI,EAAE+J,MAAM,CAAC0D,UAAU;UACvBP,KAAK,EAAEnD,MAAM,CAAC2D,QAAQ,CAACpB,QAAQ;SAChC,CAAC,CAAC;MACL,CAAC,MAAM;QACL,IAAI,CAACpO,aAAa,GAAG,EAAE;MACzB;IACF;IAEAb,cAAcA,CAACgN,KAAU;MACvB,MAAMsD,gBAAgB,GAAGtD,KAAK,CAACgD,eAAe,GAAG,CAAC,CAAC,EAAEH,KAAK;MAC1D,MAAM/O,kBAAkB,GAAGkM,KAAK,CAACgD,eAAe,GAAG,CAAC,CAAC,EAAErN,IAAI;MAC3D,IAAI2N,gBAAgB,EAAE;QACpB,IAAI,CAACpP,cAAc,GAAGoP,gBAAgB;QACtC,IAAI,CAACxP,kBAAkB,GAAGA,kBAAkB;QAC5C,IAAI,CAACJ,gBAAgB,CAACsO,UAAU,CAAC;UAAEtC,MAAM,EAAE4D;QAAgB,CAAE,CAAC;QAC9D,IAAI,CAACb,YAAY,CAACa,gBAAgB,CAAC;QACnC;QACA,IAAI,CAAC5P,gBAAgB,CAACsO,UAAU,CAAC;UAAErC,OAAO,EAAE,EAAE;UAAEC,IAAI,EAAE;QAAE,CAAE,CAAC;QAC3D,IAAI,CAACvL,eAAe,GAAG,EAAE;QACzB,IAAI,CAACyH,YAAY,GAAG,EAAE;QACtB,IAAI,CAAC7H,mBAAmB,GAAG,EAAE;QAC7B,IAAI,CAACG,gBAAgB,GAAG,EAAE;QAC1B,IAAI,CAACD,WAAW,GAAG,EAAE;MACvB;IACF;IAEAsO,YAAYA,CAACY,QAAgB;MAC3B,MAAM7D,GAAG,GAAG,IAAI,CAACzD,aAAa,CAACkH,IAAI,CAAEC,CAAC,IACpCA,CAAC,CAACC,OAAO,CAAC7C,IAAI,CAAEiD,CAAC,IAAKA,CAAC,CAACF,QAAQ,CAACpB,QAAQ,EAAE,KAAKoB,QAAQ,CAAC,CAC1D;MACD,IAAI7D,GAAG,EAAE;QACP,MAAME,MAAM,GAAGF,GAAG,CAAC2D,OAAO,CAACF,IAAI,CAC5BM,CAAC,IAAKA,CAAC,CAACF,QAAQ,CAACpB,QAAQ,EAAE,KAAKoB,QAAQ,CAC1C;QACD,IAAI3D,MAAM,EAAE;UACV,IAAI,CAAC1L,cAAc,GAAG0L,MAAM,CAAC8D,QAAQ,CAAC1B,GAAG,CAAEnC,OAAO,KAAM;YACtDhK,IAAI,EAAEgK,OAAO,CAAC/B,WAAW;YACzBiF,KAAK,EAAElD,OAAO,CAAC8D,SAAS,CAACxB,QAAQ;WAClC,CAAC,CAAC;QACL,CAAC,MAAM;UACL,IAAI,CAACjO,cAAc,GAAG,EAAE;QAC1B;MACF,CAAC,MAAM;QACL,IAAI,CAACA,cAAc,GAAG,EAAE;MAC1B;IACF;IAEAd,eAAeA,CAAC8M,KAAU;MACxB,MAAM0D,iBAAiB,GAAG1D,KAAK,CAACgD,eAAe,GAAG,CAAC,CAAC,EAAEH,KAAK;MAC3D,MAAM5O,mBAAmB,GAAG+L,KAAK,CAACgD,eAAe,GAAG,CAAC,CAAC,EAAErN,IAAI;MAC5D,IAAI+N,iBAAiB,EAAE;QACrB,IAAI,CAACrP,eAAe,GAAGqP,iBAAiB;QACxC,IAAI,CAACzP,mBAAmB,GAAGA,mBAAmB;QAC9C,IAAI,CAACP,gBAAgB,CAACsO,UAAU,CAAC;UAAErC,OAAO,EAAE+D;QAAiB,CAAE,CAAC;QAChE,IAAI,CAAChB,SAAS,CAACgB,iBAAiB,CAAC;QACjC;QACA,IAAI,CAAChQ,gBAAgB,CAACsO,UAAU,CAAC;UAAEpC,IAAI,EAAE;QAAE,CAAE,CAAC;QAC9C,IAAI,CAAC9D,YAAY,GAAG,EAAE;QACtB,IAAI,CAAC1H,gBAAgB,GAAG,EAAE;MAC5B;IACF;IAEAsO,SAASA,CAACe,SAAiB;MACzB,MAAMjE,GAAG,GAAG,IAAI,CAACzD,aAAa,CAACkH,IAAI,CAAEC,CAAC,IACpCA,CAAC,CAACC,OAAO,CAAC7C,IAAI,CAAEiD,CAAC,IACfA,CAAC,CAACC,QAAQ,CAAClD,IAAI,CAAEqD,CAAC,IAAKA,CAAC,CAACF,SAAS,CAACxB,QAAQ,EAAE,KAAKwB,SAAS,CAAC,CAC7D,CACF;MACD,IAAIjE,GAAG,EAAE;QACP,MAAME,MAAM,GAAGF,GAAG,CAAC2D,OAAO,CAACF,IAAI,CAAEM,CAAC,IAChCA,CAAC,CAACC,QAAQ,CAAClD,IAAI,CAAEqD,CAAC,IAAKA,CAAC,CAACF,SAAS,CAACxB,QAAQ,EAAE,KAAKwB,SAAS,CAAC,CAC7D;QACD,IAAI/D,MAAM,EAAE;UACV,MAAMC,OAAO,GAAGD,MAAM,CAAC8D,QAAQ,CAACP,IAAI,CACjCU,CAAC,IAAKA,CAAC,CAACF,SAAS,CAACxB,QAAQ,EAAE,KAAKwB,SAAS,CAC5C;UACD,IAAI9D,OAAO,EAAE;YACX,IAAI,CAACxL,WAAW,GAAGwL,OAAO,CAACiE,KAAK,CAAC9B,GAAG,CAAElC,IAAI,KAAM;cAC9CjK,IAAI,EAAEiK,IAAI,CAACiE,QAAQ;cACnBhB,KAAK,EAAEjD,IAAI,CAACkE,MAAM,CAAC7B,QAAQ;aAC5B,CAAC,CAAC;UACL,CAAC,MAAM;YACL,IAAI,CAAC9N,WAAW,GAAG,EAAE;UACvB;QACF,CAAC,MAAM;UACL,IAAI,CAACA,WAAW,GAAG,EAAE;QACvB;MACF,CAAC,MAAM;QACL,IAAI,CAACA,WAAW,GAAG,EAAE;MACvB;IACF;IAEAf,YAAYA,CAAC4M,KAAU;MACrB,MAAM+D,cAAc,GAAG/D,KAAK,CAACgD,eAAe,GAAG,CAAC,CAAC,EAAEH,KAAK;MACxD,MAAMzO,gBAAgB,GAAG4L,KAAK,CAACgD,eAAe,GAAG,CAAC,CAAC,EAAErN,IAAI;MACzD,IAAIoO,cAAc,EAAE;QAClB,IAAI,CAACjI,YAAY,GAAGiI,cAAc;QAClC,IAAI,CAAC3P,gBAAgB,GAAGA,gBAAgB;QACxC,IAAI,CAACV,gBAAgB,CAACsO,UAAU,CAAC;UAAEpC,IAAI,EAAEmE;QAAc,CAAE,CAAC;MAC5D;IACF;IAEA,IAAIC,QAAQA,CAAA;MACV;MACA,MAAMC,OAAO,GAAG,IAAI,CAACtJ,YAAY,CAAC8G,SAAS,CAAC,UAAU,CAAC;MACvD,IAAIwC,OAAO,EAAE;QACX,MAAMC,OAAO,GAAGD,OAAO,CAAChD,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAACA,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACpD,IAAIiD,OAAO,EAAE,OAAOA,OAAO;MAC7B;MACA;MACA,OACE,IAAI,CAACvQ,UAAU,CAACsP,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACL,KAAK,KAAK,IAAI,CAAC9O,WAAW,CAAC,EAAE4B,IAAI,IAC/D,qBAAqB;IAEzB;IAEAnC,mBAAmBA,CAAA;MACjB,IAAI,IAAI,CAACE,gBAAgB,CAACY,KAAK,EAAE;QAC/B,MAAM6P,SAAS,GAAG,IAAI,CAACzQ,gBAAgB,CAACmP,KAAK;QAE7C;QACA,MAAMoB,OAAO,GAAG,GAAG,IAAI,CAACrQ,eAAe,IAAI,IAAI,CAACE,kBAAkB,IAAI,IAAI,CAACG,mBAAmB,IAAI,IAAI,CAACG,gBAAgB,KAAK+P,SAAS,CAAC3E,GAAG,IAAI2E,SAAS,CAACzE,MAAM,IAAIyE,SAAS,CAACxE,OAAO,IAAIwE,SAAS,CAACvE,IAAI,EAAE;QAEtM;QACA,IAAI,CAACjF,YAAY,CAACyJ,SAAS,CAAC,UAAU,EAAEH,OAAO,CAAC;QAEhD;QACA,IAAI,CAACpI,eAAe,CAACwI,IAAI,CAAC;UACxBJ,OAAO;UACPK,cAAc,EAAE;YACd9E,GAAG,EAAE,IAAI,CAACzL,WAAW;YACrB2L,MAAM,EAAE,IAAI,CAACxL,cAAc;YAC3ByL,OAAO,EAAE,IAAI,CAACtL,eAAe;YAC7BuL,IAAI,EAAE,IAAI,CAAC9D;WACZ;UACDyI,aAAa,EAAE;YACb/E,GAAG,EAAE,IAAI,CAAC5L,eAAe;YACzB8L,MAAM,EAAE,IAAI,CAAC5L,kBAAkB;YAC/B6L,OAAO,EAAE,IAAI,CAAC1L,mBAAmB;YACjC2L,IAAI,EAAE,IAAI,CAACxL;;SAEd,CAAC;QAEF,IAAI,CAACd,cAAc,EAAE;MACvB;IACF;IAEA;IACA7B,UAAUA,CAACS,KAAa;MACtB;MACA,IAAI,CAACgO,mBAAmB,EAAE;MAC1B,IAAI,CAAC3F,MAAM,CAACiK,QAAQ,CAAC,CAACtS,KAAK,CAAC,CAAC;MAC7B,IAAI,CAACsJ,eAAe,CAAC6I,IAAI,CAACnS,KAAK,CAAC;IAClC;IAEAP,cAAcA,CAACR,KAAa;MAC1B,IAAI,CAACkG,MAAM,CAACiC,QAAQ,CAACmL,OAAO,CAAC,CAACC,IAAI,EAAEC,CAAC,KAAI;QACvCD,IAAI,CAACvS,QAAQ,GAAGwS,CAAC,KAAKxT,KAAK;MAC7B,CAAC,CAAC;IACJ;IAEA;IAEAG,cAAcA,CAACH,KAAa,EAAE6O,KAAkB;MAChD,MAAM4E,WAAW,GAAG,IAAI,CAACvN,MAAM,CAACiC,QAAQ,CAACnI,KAAK,CAAC;MAE/C,MAAM0T,aAAa,GAAGD,WAAW,CAACvS,YAAY;MAE9C;MACA,IAAI,CAACgF,MAAM,CAACiC,QAAQ,CAACmL,OAAO,CAAC,CAACC,IAAI,EAAEC,CAAC,KAAI;QACvCD,IAAI,CAACrS,YAAY,GAAG,KAAK;MAC3B,CAAC,CAAC;MAEF,IAAIwS,aAAa,EAAE;QACjB,IAAI,CAAC3E,mBAAmB,EAAE;QAC1B;MACF;MAEA;MACA0E,WAAW,CAACvS,YAAY,GAAG,IAAI;MAC/B;MACA,IAAI2N,KAAK,EAAE;QACT,MAAM9F,IAAI,GAAI8F,KAAK,CAAC8E,aAA6B,CAACC,qBAAqB,EAAE;QACzE,IAAI,CAAChT,oBAAoB,CAAC;UACxBmI,IAAI;UACJG,KAAK,EAAEuK,WAAW,CAACtS,aAAa,IAAI,EAAE;UACtC6J,WAAW,EAAEyI,WAAW,CAAC3S,KAAK;UAC9BmK,SAAS,EAAEwI,WAAW,CAAC3S;SACxB,CAAC;MACJ;IACF;IAEEJ,sBAAsBA,CACpBmO,KAAuC,EACvCgF,WAAmB;MAEnB,IAAI,CAACvT,UAAU,CAACuO,KAAK,CAAC9N,KAAK,CAAC;MAC5B,IAAI,CAACuJ,oBAAoB,CAAC4I,IAAI,CAACrE,KAAK,CAAC;MACrC,IAAI,CAACrO,cAAc,CAACqT,WAAW,CAAC;MAChC,IAAI,CAAC9E,mBAAmB,EAAE;IAC5B;IAEAnO,oBAAoBA,CAACiO,KAKpB;MACC,IAAI,CAAC/F,cAAc,GAAG;QACpBiC,IAAI,EAAE,IAAI;QACVhC,IAAI,EAAE8F,KAAK,CAAC9F,IAAI;QAChBG,KAAK,EAAE2F,KAAK,CAAC3F,KAAK;QAClB8B,WAAW,EAAE6D,KAAK,CAAC7D,WAAW;QAC9BC,SAAS,EAAE4D,KAAK,CAAC5D;OAClB;MACD;MACA,IAAI,CAAC5B,GAAG,CAACwE,aAAa,EAAE;IAC1B;IAEAkB,mBAAmBA,CAAA;MACjB,IAAI,CAACjG,cAAc,CAACiC,IAAI,GAAG,KAAK;MAChC,IAAI,CAACjC,cAAc,CAACmC,SAAS,GAAG,EAAE;MAClC,IAAI,CAAC/E,MAAM,CAACiC,QAAQ,CAACmL,OAAO,CAAEC,IAAI,IAAMA,IAAI,CAACrS,YAAY,GAAG,KAAM,CAAC;IACrE;IAEA;IACAoF,qBAAqBA,CAAA;MACnB,IAAI,CAACO,mBAAmB,GAAG,CAAC,IAAI,CAACA,mBAAmB;IACtD;IAEAD,MAAMA,CAAA;MACJ,IAAI,CAAC6C,WAAW,CAAC7C,MAAM,EAAE;MACzB,IAAI,CAAC2D,aAAa,CAAC2I,IAAI,CAAC,QAAQ,CAAC;IACnC;IAEA;IACA/N,WAAWA,CAAA;MACT,IAAI,IAAI,CAAC0E,YAAY,IAAI,IAAI,CAACA,YAAY,CAAC1E,WAAW,EAAE;QACtD;QACA,IAAI,CAAC0E,YAAY,CAAC1E,WAAW,EAAE;MACjC,CAAC,MAAM;QACL;QACA,MAAM2O,QAAQ,GAAG,IAAI,CAACrO,YAAY,KAAK,OAAO,GAAG,MAAM,GAAG,OAAO;QACjE,IAAI,CAACA,YAAY,GAAGqO,QAAQ;QAC5B,IAAI,CAACvE,iBAAiB,EAAE;QACxB,IAAI,CAAC/E,WAAW,CAAC0I,IAAI,CAACY,QAAQ,CAAC;MACjC;IACF;IAEA;IACAhO,cAAcA,CAACiO,YAAoB;MACjC,IAAI,CAAC/N,eAAe,GAAG+N,YAAY;MACnC,IAAI,CAACtJ,cAAc,CAACyI,IAAI,CAACa,YAAY,CAAC;IACxC;IAEAC,sBAAsBA,CAAA;MACpB,MAAMC,QAAQ,GAAG,IAAI,CAAC/N,MAAM,CAACC,kBAAkB,EAAE2L,IAAI,CAClDoC,IAAI,IAAKA,IAAI,CAACnO,IAAI,KAAK,IAAI,CAACC,eAAe,CAC7C;MACD,OAAOiO,QAAQ,EAAEzP,IAAI,IAAI,SAAS;IACpC;IAEA;IACAI,eAAeA,CAAA;MACb,IAAI,CAACG,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;MAE5C,IAAI,IAAI,CAACA,eAAe,EAAE;QACxB;QACA6I,UAAU,CAAC,MAAK;UACd,IAAI,CAACuG,0BAA0B,EAAE;QACnC,CAAC,CAAC;MACJ;IACF;IAEArQ,cAAcA,CAAA;MACZ,IAAI,CAACiB,eAAe,GAAG,KAAK;IAC9B;IAEAV,aAAaA,CAAC+P,GAAc;MAC1B,IAAIA,GAAG,CAACrT,KAAK,CAACsT,UAAU,CAAC,MAAM,CAAC,EAAE;QAChC;QACAC,MAAM,CAACvJ,IAAI,CAACqJ,GAAG,CAACrT,KAAK,EAAE,QAAQ,CAAC;MAClC,CAAC,MAAM;QACL;QACA,IAAI,CAACqI,MAAM,CAACiK,QAAQ,CAAC,CAACe,GAAG,CAACrT,KAAK,CAAC,CAAC;MACnC;MACA,IAAI,CAAC+C,cAAc,EAAE;MACrB,IAAI,CAACuG,eAAe,CAAC6I,IAAI,CAACkB,GAAG,CAACrT,KAAK,CAAC;IACtC;IAEAiE,eAAeA,CAAA;MACb,IAAI,CAAC,IAAI,CAACkB,MAAM,CAACiI,aAAa,EAAE,OAAO,EAAE;MAEzC;MACA,OAAO,IAAI,CAACjI,MAAM,CAACiI,aAAa,CAAC7P,MAAM,CACpC8V,GAAG,IAAKA,GAAG,CAAC5P,IAAI,KAAK,IAAI,CAAC0B,MAAM,CAACgI,UAAU,CAC7C;IACH;IAEA;IACA1K,eAAeA,CAAA;MACb,IAAI,IAAI,CAAC0C,MAAM,CAACkC,eAAe,EAAE;QAC/B,IAAI,CAACzE,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;QAE5C,IAAI,IAAI,CAACA,eAAe,EAAE;UACxB;UACAiK,UAAU,CAAC,MAAK;YACd,IAAI,CAAC2G,wBAAwB,EAAE;UACjC,CAAC,CAAC;QACJ;MACF;IACF;IAEApS,cAAcA,CAAA;MACZ,IAAI,CAACwB,eAAe,GAAG,KAAK;IAC9B;IAEQ4Q,wBAAwBA,CAAA;MAC9B,IAAI,IAAI,CAAC1J,cAAc,IAAI,IAAI,CAACC,UAAU,EAAE;QAC1C,MAAM0J,WAAW,GACf,IAAI,CAAC3J,cAAc,CAAC4J,aAAa,CAACb,qBAAqB,EAAE;QAC3D,MAAMc,WAAW,GAAG,IAAI,CAAC5J,UAAU,CAAC2J,aAAa,CAACb,qBAAqB,EAAE;QACzE,MAAMe,aAAa,GAAGL,MAAM,CAACM,UAAU;QAEvC;QACA,IAAIJ,WAAW,CAACvL,IAAI,GAAGyL,WAAW,CAACG,KAAK,GAAGF,aAAa,GAAG,EAAE,EAAE;UAC7D,IAAI,CAACrS,YAAY,GAAG,OAAO;QAC7B,CAAC,MAAM;UACL,IAAI,CAACA,YAAY,GAAG,MAAM;QAC5B;MACF;IACF;IAEQ6R,0BAA0BA,CAAA;MAChC;MACA,MAAMW,iBAAiB,GAAG,IAAI,CAACvL,UAAU,CAACkL,aAAa,CAACvH,aAAa,CACnE,sBAAsB,CACvB;MACD,IAAI4H,iBAAiB,EAAE;QACrB,MAAMC,YAAY,GAAGD,iBAAiB,CAAClB,qBAAqB,EAAE;QAC9D,MAAMe,aAAa,GAAGL,MAAM,CAACM,UAAU;QAEvC;QACA,IAAIG,YAAY,CAACC,KAAK,GAAGL,aAAa,GAAG,EAAE,EAAE;UAC3C,MAAMM,QAAQ,GAAGF,YAAY,CAACC,KAAK,GAAGL,aAAa,GAAG,EAAE;UACxDG,iBAAiB,CAACI,KAAK,CAACF,KAAK,GAAG,GAAGC,QAAQ,IAAI;QACjD,CAAC,MAAM;UACLH,iBAAiB,CAACI,KAAK,CAACF,KAAK,GAAG,KAAK;QACvC;MACF;IACF;IAEA;IACFG,cAAcA,CAAC5B,IAAmB,EAAEvT,KAAa,EAAE6O,KAAiB;MAClE,IAAI0E,IAAI,CAAChS,QAAQ,EAAE;QACjBsN,KAAK,CAACuG,cAAc,EAAE;QACtBvG,KAAK,CAACwG,eAAe,EAAE;QACvB;MACF;MAEA,IAAI9B,IAAI,CAACtS,WAAW,IAAIsS,IAAI,CAACpS,aAAa,EAAE;QAC1C0N,KAAK,CAACwG,eAAe,EAAE;QACvB,IAAI,CAAClV,cAAc,CAACH,KAAK,EAAE6O,KAAK,CAAC,CAAC,CAAC;MACrC,CAAC,MAAM;QACL,IAAI,CAACvO,UAAU,CAACiT,IAAI,CAACxS,KAAK,CAAC;QAC3B,IAAI,CAACP,cAAc,CAACR,KAAK,CAAC;MAC5B;IACF;IAEE;IACA6L,2BAA2BA,CAACC,GAAW;MACrC;MACA,IAAI,CAAC5F,MAAM,CAACiC,QAAQ,CAACmL,OAAO,CAAEC,IAAI,IAAI;QACpCA,IAAI,CAACvS,QAAQ,GAAG,KAAK;MACvB,CAAC,CAAC;MAEF;MACA,MAAMsU,UAAU,GAAG,IAAI,CAACpP,MAAM,CAACiC,QAAQ,CAAC2J,IAAI,CAAEyB,IAAI,IAAI;QACpD;QACA,IAAIzH,GAAG,KAAKyH,IAAI,CAACxS,KAAK,EAAE;UACtB,OAAO,IAAI;QACb;QAEA;QACA,IAAIwS,IAAI,CAACtS,WAAW,IAAIsS,IAAI,CAACpS,aAAa,EAAE;UAC1C;UACA;UACA,OACE2K,GAAG,CAACuI,UAAU,CAACd,IAAI,CAACxS,KAAK,GAAG,GAAG,CAAC,IAChCwS,IAAI,CAACpS,aAAa,CAACgO,IAAI,CAAEoG,KAAK,IAAKzJ,GAAG,KAAKyJ,KAAK,CAACxU,KAAK,CAAC;QAE3D;QAEA;QACA,IAAI,CAACwS,IAAI,CAACtS,WAAW,IAAIsS,IAAI,CAACpS,aAAa,EAAE;UAC3C,OAAOoS,IAAI,CAACpS,aAAa,CAACgO,IAAI,CAAEoG,KAAK,IAAKzJ,GAAG,KAAKyJ,KAAK,CAACxU,KAAK,CAAC;QAChE;QAEA,OAAO,KAAK;MACd,CAAC,CAAC;MAEF,IAAIuU,UAAU,EAAE;QACdA,UAAU,CAACtU,QAAQ,GAAG,IAAI;MAC5B,CAAC,MAAM;QACL;QACA,MAAMwU,WAAW,GAAG,IAAI,CAACtP,MAAM,CAACiC,QAAQ,CAAC2J,IAAI,CAAEyB,IAAI,IAAK,CAACA,IAAI,CAAChS,QAAQ,CAAC;QACvE,IAAIiU,WAAW,EAAE;UACfA,WAAW,CAACxU,QAAQ,GAAG,IAAI;QAC7B;MACF;IACF;IAEA;IAEFyU,eAAeA,CAAC5G,KAAiB;MAC/B,MAAM6G,MAAM,GAAG7G,KAAK,CAAC6G,MAAqB;MAE1C;MACA,IACE,IAAI,CAAC7O,mBAAmB,IACxB,CAAC,IAAI,CAAC0C,UAAU,CAACkL,aAAa,CAACkB,QAAQ,CAACD,MAAM,CAAC,EAC/C;QACA,IAAI,CAAC7O,mBAAmB,GAAG,KAAK;MAClC;MAEA;MACA,IACE,IAAI,CAAClD,eAAe,IACpB,CAAC+R,MAAM,CAACE,OAAO,CAAC,8BAA8B,CAAC,EAC/C;QACA,IAAI,CAACzT,cAAc,EAAE;MACvB;MAEA;MACA,IAAI,IAAI,CAAC4C,eAAe,IAAI,CAAC2Q,MAAM,CAACE,OAAO,CAAC,uBAAuB,CAAC,EAAE;QACpE,IAAI,CAAC9R,cAAc,EAAE;MACvB;MAEA;MACA,MAAMuM,IAAI,GAAGxB,KAAK,CAACgH,YAAY,GAAE,CAAE,IAAI,EAAE;MAEzC,MAAMC,qBAAqB,GAAGzF,IAAI,CAAClB,IAAI,CAAE4G,EAAO,IAC9CA,EAAE,EAAE5I,SAAS,EAAEwI,QAAQ,GAAG,sBAAsB,CAAC,CAClD;MAED,MAAMK,oBAAoB,GAAG3F,IAAI,CAAClB,IAAI,CAAE4G,EAAO,IAC7CA,EAAE,EAAE5I,SAAS,EAAEwI,QAAQ,GAAG,kBAAkB,CAAC,CAC9C;MAED,IAAI,IAAI,CAAC7M,cAAc,CAACiC,IAAI,IAAI,CAAC+K,qBAAqB,IAAI,CAACE,oBAAoB,EAAE;QAC/E,IAAI,CAACjH,mBAAmB,EAAE;MAC5B;IACF;;uCA3/Ba5F,wBAAwB,EAAArK,EAAA,CAAAmX,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAArX,EAAA,CAAAmX,iBAAA,CAAAnX,EAAA,CAAAsX,iBAAA,GAAAtX,EAAA,CAAAmX,iBAAA,CAAAnX,EAAA,CAAAuX,SAAA,GAAAvX,EAAA,CAAAmX,iBAAA,CAAAnX,EAAA,CAAAwX,UAAA,GAAAxX,EAAA,CAAAmX,iBAAA,CAAAM,EAAA,CAAAC,mBAAA,GAAA1X,EAAA,CAAAmX,iBAAA,CAAAQ,EAAA,CAAAC,WAAA,GAAA5X,EAAA,CAAAmX,iBAAA,CAAAU,EAAA,CAAAC,WAAA;IAAA;;YAAxBzN,wBAAwB;MAAA0N,SAAA;MAAAC,SAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;UAAxBlY,EAAA,CAAAa,UAAA,mBAAAuX,kDAAA7W,MAAA;YAAA,OAAA4W,GAAA,CAAAxB,eAAA,CAAApV,MAAA,CAAuB;UAAA,UAAAvB,EAAA,CAAAqY,iBAAA,CAAC;;;;;;;;;;;;;;;;;;;;;;UCoPrCrY,EAhXA,CAAAO,UAAA,IAAA+X,uCAAA,mBAA0B,IAAAC,uCAAA,iBAqXzB;;;UArXKvY,EAAA,CAAAW,UAAA,UAAAwX,GAAA,CAAAnN,WAAA,CAAkB;UAiXrBhL,EAAA,CAAAU,SAAA,EAAgD;UAAhDV,EAAA,CAAAW,UAAA,SAAAwX,GAAA,CAAAnO,cAAA,CAAAiC,IAAA,IAAAkM,GAAA,CAAAnO,cAAA,CAAAC,IAAA,CAAgD;;;qBDhQ/C5K,eAAe,EACfD,YAAY,EAAAoZ,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EACZ9Y,sBAAsB,EACtBH,iBAAiB,EACjBE,eAAe,EACfG,mBAAmB,EAAA8X,EAAA,CAAAe,aAAA,EAAAf,EAAA,CAAAgB,oBAAA,EAAAhB,EAAA,CAAAiB,kBAAA,EACnBnZ,aAAa;MAAAoZ,MAAA;IAAA;;SAKJ1O,wBAAwB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}