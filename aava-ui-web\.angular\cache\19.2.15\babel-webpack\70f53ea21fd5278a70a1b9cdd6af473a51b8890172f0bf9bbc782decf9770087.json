{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = this && this.__param || function (paramIndex, decorator) {\n  return function (target, key) {\n    decorator(target, key, paramIndex);\n  };\n};\nimport * as dom from '../../../../base/browser/dom.js';\nimport { ActionBar } from '../../../../base/browser/ui/actionbar/actionbar.js';\nimport { DisposableStore } from '../../../../base/common/lifecycle.js';\nimport { TextOnlyMenuEntryActionViewItem } from '../../../../platform/actions/browser/menuEntryActionViewItem.js';\nimport { IMenuService, MenuItemAction } from '../../../../platform/actions/common/actions.js';\nimport { IContextKeyService } from '../../../../platform/contextkey/common/contextkey.js';\nimport { IInstantiationService } from '../../../../platform/instantiation/common/instantiation.js';\nlet SuggestWidgetStatus = class SuggestWidgetStatus {\n  constructor(container, _menuId, instantiationService, _menuService, _contextKeyService) {\n    this._menuId = _menuId;\n    this._menuService = _menuService;\n    this._contextKeyService = _contextKeyService;\n    this._menuDisposables = new DisposableStore();\n    this.element = dom.append(container, dom.$('.suggest-status-bar'));\n    const actionViewItemProvider = action => {\n      return action instanceof MenuItemAction ? instantiationService.createInstance(TextOnlyMenuEntryActionViewItem, action, {\n        useComma: true\n      }) : undefined;\n    };\n    this._leftActions = new ActionBar(this.element, {\n      actionViewItemProvider\n    });\n    this._rightActions = new ActionBar(this.element, {\n      actionViewItemProvider\n    });\n    this._leftActions.domNode.classList.add('left');\n    this._rightActions.domNode.classList.add('right');\n  }\n  dispose() {\n    this._menuDisposables.dispose();\n    this._leftActions.dispose();\n    this._rightActions.dispose();\n    this.element.remove();\n  }\n  show() {\n    const menu = this._menuService.createMenu(this._menuId, this._contextKeyService);\n    const renderMenu = () => {\n      const left = [];\n      const right = [];\n      for (const [group, actions] of menu.getActions()) {\n        if (group === 'left') {\n          left.push(...actions);\n        } else {\n          right.push(...actions);\n        }\n      }\n      this._leftActions.clear();\n      this._leftActions.push(left);\n      this._rightActions.clear();\n      this._rightActions.push(right);\n    };\n    this._menuDisposables.add(menu.onDidChange(() => renderMenu()));\n    this._menuDisposables.add(menu);\n  }\n  hide() {\n    this._menuDisposables.clear();\n  }\n};\nSuggestWidgetStatus = __decorate([__param(2, IInstantiationService), __param(3, IMenuService), __param(4, IContextKeyService)], SuggestWidgetStatus);\nexport { SuggestWidgetStatus };", "map": {"version": 3, "names": ["__decorate", "decorators", "target", "key", "desc", "c", "arguments", "length", "r", "Object", "getOwnPropertyDescriptor", "d", "Reflect", "decorate", "i", "defineProperty", "__param", "paramIndex", "decorator", "dom", "ActionBar", "DisposableStore", "TextOnlyMenuEntryActionViewItem", "IMenuService", "MenuItemAction", "IContextKeyService", "IInstantiationService", "SuggestWidgetStatus", "constructor", "container", "_menuId", "instantiationService", "_menuService", "_contextKeyService", "_menuDisposables", "element", "append", "$", "actionViewItemProvider", "action", "createInstance", "useComma", "undefined", "_leftActions", "_rightActions", "domNode", "classList", "add", "dispose", "remove", "show", "menu", "createMenu", "renderMenu", "left", "right", "group", "actions", "getActions", "push", "clear", "onDidChange", "hide"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/contrib/suggest/browser/suggestWidgetStatus.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = (this && this.__param) || function (paramIndex, decorator) {\n    return function (target, key) { decorator(target, key, paramIndex); }\n};\nimport * as dom from '../../../../base/browser/dom.js';\nimport { ActionBar } from '../../../../base/browser/ui/actionbar/actionbar.js';\nimport { DisposableStore } from '../../../../base/common/lifecycle.js';\nimport { TextOnlyMenuEntryActionViewItem } from '../../../../platform/actions/browser/menuEntryActionViewItem.js';\nimport { IMenuService, MenuItemAction } from '../../../../platform/actions/common/actions.js';\nimport { IContextKeyService } from '../../../../platform/contextkey/common/contextkey.js';\nimport { IInstantiationService } from '../../../../platform/instantiation/common/instantiation.js';\nlet SuggestWidgetStatus = class SuggestWidgetStatus {\n    constructor(container, _menuId, instantiationService, _menuService, _contextKeyService) {\n        this._menuId = _menuId;\n        this._menuService = _menuService;\n        this._contextKeyService = _contextKeyService;\n        this._menuDisposables = new DisposableStore();\n        this.element = dom.append(container, dom.$('.suggest-status-bar'));\n        const actionViewItemProvider = (action => {\n            return action instanceof MenuItemAction ? instantiationService.createInstance(TextOnlyMenuEntryActionViewItem, action, { useComma: true }) : undefined;\n        });\n        this._leftActions = new ActionBar(this.element, { actionViewItemProvider });\n        this._rightActions = new ActionBar(this.element, { actionViewItemProvider });\n        this._leftActions.domNode.classList.add('left');\n        this._rightActions.domNode.classList.add('right');\n    }\n    dispose() {\n        this._menuDisposables.dispose();\n        this._leftActions.dispose();\n        this._rightActions.dispose();\n        this.element.remove();\n    }\n    show() {\n        const menu = this._menuService.createMenu(this._menuId, this._contextKeyService);\n        const renderMenu = () => {\n            const left = [];\n            const right = [];\n            for (const [group, actions] of menu.getActions()) {\n                if (group === 'left') {\n                    left.push(...actions);\n                }\n                else {\n                    right.push(...actions);\n                }\n            }\n            this._leftActions.clear();\n            this._leftActions.push(left);\n            this._rightActions.clear();\n            this._rightActions.push(right);\n        };\n        this._menuDisposables.add(menu.onDidChange(() => renderMenu()));\n        this._menuDisposables.add(menu);\n    }\n    hide() {\n        this._menuDisposables.clear();\n    }\n};\nSuggestWidgetStatus = __decorate([\n    __param(2, IInstantiationService),\n    __param(3, IMenuService),\n    __param(4, IContextKeyService)\n], SuggestWidgetStatus);\nexport { SuggestWidgetStatus };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,IAAIA,UAAU,GAAI,IAAI,IAAI,IAAI,CAACA,UAAU,IAAK,UAAUC,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAE;EACnF,IAAIC,CAAC,GAAGC,SAAS,CAACC,MAAM;IAAEC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGH,MAAM,GAAGE,IAAI,KAAK,IAAI,GAAGA,IAAI,GAAGK,MAAM,CAACC,wBAAwB,CAACR,MAAM,EAAEC,GAAG,CAAC,GAAGC,IAAI;IAAEO,CAAC;EAC5H,IAAI,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,CAACC,QAAQ,KAAK,UAAU,EAAEL,CAAC,GAAGI,OAAO,CAACC,QAAQ,CAACZ,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,CAAC,CAAC,KAC1H,KAAK,IAAIU,CAAC,GAAGb,UAAU,CAACM,MAAM,GAAG,CAAC,EAAEO,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE,IAAIH,CAAC,GAAGV,UAAU,CAACa,CAAC,CAAC,EAAEN,CAAC,GAAG,CAACH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACH,CAAC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACT,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,GAAGG,CAAC,CAACT,MAAM,EAAEC,GAAG,CAAC,KAAKK,CAAC;EACjJ,OAAOH,CAAC,GAAG,CAAC,IAAIG,CAAC,IAAIC,MAAM,CAACM,cAAc,CAACb,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,EAAEA,CAAC;AACjE,CAAC;AACD,IAAIQ,OAAO,GAAI,IAAI,IAAI,IAAI,CAACA,OAAO,IAAK,UAAUC,UAAU,EAAEC,SAAS,EAAE;EACrE,OAAO,UAAUhB,MAAM,EAAEC,GAAG,EAAE;IAAEe,SAAS,CAAChB,MAAM,EAAEC,GAAG,EAAEc,UAAU,CAAC;EAAE,CAAC;AACzE,CAAC;AACD,OAAO,KAAKE,GAAG,MAAM,iCAAiC;AACtD,SAASC,SAAS,QAAQ,oDAAoD;AAC9E,SAASC,eAAe,QAAQ,sCAAsC;AACtE,SAASC,+BAA+B,QAAQ,iEAAiE;AACjH,SAASC,YAAY,EAAEC,cAAc,QAAQ,gDAAgD;AAC7F,SAASC,kBAAkB,QAAQ,sDAAsD;AACzF,SAASC,qBAAqB,QAAQ,4DAA4D;AAClG,IAAIC,mBAAmB,GAAG,MAAMA,mBAAmB,CAAC;EAChDC,WAAWA,CAACC,SAAS,EAAEC,OAAO,EAAEC,oBAAoB,EAAEC,YAAY,EAAEC,kBAAkB,EAAE;IACpF,IAAI,CAACH,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACE,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,gBAAgB,GAAG,IAAIb,eAAe,CAAC,CAAC;IAC7C,IAAI,CAACc,OAAO,GAAGhB,GAAG,CAACiB,MAAM,CAACP,SAAS,EAAEV,GAAG,CAACkB,CAAC,CAAC,qBAAqB,CAAC,CAAC;IAClE,MAAMC,sBAAsB,GAAIC,MAAM,IAAI;MACtC,OAAOA,MAAM,YAAYf,cAAc,GAAGO,oBAAoB,CAACS,cAAc,CAAClB,+BAA+B,EAAEiB,MAAM,EAAE;QAAEE,QAAQ,EAAE;MAAK,CAAC,CAAC,GAAGC,SAAS;IAC1J,CAAE;IACF,IAAI,CAACC,YAAY,GAAG,IAAIvB,SAAS,CAAC,IAAI,CAACe,OAAO,EAAE;MAAEG;IAAuB,CAAC,CAAC;IAC3E,IAAI,CAACM,aAAa,GAAG,IAAIxB,SAAS,CAAC,IAAI,CAACe,OAAO,EAAE;MAAEG;IAAuB,CAAC,CAAC;IAC5E,IAAI,CAACK,YAAY,CAACE,OAAO,CAACC,SAAS,CAACC,GAAG,CAAC,MAAM,CAAC;IAC/C,IAAI,CAACH,aAAa,CAACC,OAAO,CAACC,SAAS,CAACC,GAAG,CAAC,OAAO,CAAC;EACrD;EACAC,OAAOA,CAAA,EAAG;IACN,IAAI,CAACd,gBAAgB,CAACc,OAAO,CAAC,CAAC;IAC/B,IAAI,CAACL,YAAY,CAACK,OAAO,CAAC,CAAC;IAC3B,IAAI,CAACJ,aAAa,CAACI,OAAO,CAAC,CAAC;IAC5B,IAAI,CAACb,OAAO,CAACc,MAAM,CAAC,CAAC;EACzB;EACAC,IAAIA,CAAA,EAAG;IACH,MAAMC,IAAI,GAAG,IAAI,CAACnB,YAAY,CAACoB,UAAU,CAAC,IAAI,CAACtB,OAAO,EAAE,IAAI,CAACG,kBAAkB,CAAC;IAChF,MAAMoB,UAAU,GAAGA,CAAA,KAAM;MACrB,MAAMC,IAAI,GAAG,EAAE;MACf,MAAMC,KAAK,GAAG,EAAE;MAChB,KAAK,MAAM,CAACC,KAAK,EAAEC,OAAO,CAAC,IAAIN,IAAI,CAACO,UAAU,CAAC,CAAC,EAAE;QAC9C,IAAIF,KAAK,KAAK,MAAM,EAAE;UAClBF,IAAI,CAACK,IAAI,CAAC,GAAGF,OAAO,CAAC;QACzB,CAAC,MACI;UACDF,KAAK,CAACI,IAAI,CAAC,GAAGF,OAAO,CAAC;QAC1B;MACJ;MACA,IAAI,CAACd,YAAY,CAACiB,KAAK,CAAC,CAAC;MACzB,IAAI,CAACjB,YAAY,CAACgB,IAAI,CAACL,IAAI,CAAC;MAC5B,IAAI,CAACV,aAAa,CAACgB,KAAK,CAAC,CAAC;MAC1B,IAAI,CAAChB,aAAa,CAACe,IAAI,CAACJ,KAAK,CAAC;IAClC,CAAC;IACD,IAAI,CAACrB,gBAAgB,CAACa,GAAG,CAACI,IAAI,CAACU,WAAW,CAAC,MAAMR,UAAU,CAAC,CAAC,CAAC,CAAC;IAC/D,IAAI,CAACnB,gBAAgB,CAACa,GAAG,CAACI,IAAI,CAAC;EACnC;EACAW,IAAIA,CAAA,EAAG;IACH,IAAI,CAAC5B,gBAAgB,CAAC0B,KAAK,CAAC,CAAC;EACjC;AACJ,CAAC;AACDjC,mBAAmB,GAAG3B,UAAU,CAAC,CAC7BgB,OAAO,CAAC,CAAC,EAAEU,qBAAqB,CAAC,EACjCV,OAAO,CAAC,CAAC,EAAEO,YAAY,CAAC,EACxBP,OAAO,CAAC,CAAC,EAAES,kBAAkB,CAAC,CACjC,EAAEE,mBAAmB,CAAC;AACvB,SAASA,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}