{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { EventEmitter } from '@angular/core';\nimport { FormsModule } from '@angular/forms';\nimport { IconComponent } from '@ava/play-comp-library';\nimport { trigger, style, transition, animate } from '@angular/animations';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/forms\";\nconst _c0 = [\"fileInput\"];\nfunction AgentStepperCardComponent_ava_icon_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ava-icon\", 15);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"iconSize\", 16);\n  }\n}\nfunction AgentStepperCardComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 16);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r1.isCompleted);\n  }\n}\nfunction AgentStepperCardComponent_div_14_div_1_div_2_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function AgentStepperCardComponent_div_14_div_1_div_2_button_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const i_r5 = i0.ɵɵnextContext().index;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onFileInputClick(i_r5));\n    });\n    i0.ɵɵelement(1, \"ava-icon\", 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const input_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"title\", input_r4.inputType === \"image\" ? \"Attach Image\" : \"Attach File\")(\"disabled\", input_r4.inputType === \"text\" && ctx_r1.isInputDisabled(input_r4));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"iconSize\", 18);\n  }\n}\nfunction AgentStepperCardComponent_div_14_div_1_div_2_div_9_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"span\", 36);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function AgentStepperCardComponent_div_14_div_1_div_2_div_9_div_1_Template_button_click_3_listener() {\n      const fileIndex_r8 = i0.ɵɵrestoreView(_r7).index;\n      const i_r5 = i0.ɵɵnextContext(2).index;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.removeFile(i_r5, fileIndex_r8));\n    });\n    i0.ɵɵelement(4, \"ava-icon\", 38);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r9 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(file_r9.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"iconSize\", 12);\n  }\n}\nfunction AgentStepperCardComponent_div_14_div_1_div_2_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtemplate(1, AgentStepperCardComponent_div_14_div_1_div_2_div_9_div_1_Template, 5, 2, \"div\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const input_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", input_r4.files);\n  }\n}\nfunction AgentStepperCardComponent_div_14_div_1_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"label\", 24);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 25)(4, \"textarea\", 26);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function AgentStepperCardComponent_div_14_div_1_div_2_Template_textarea_ngModelChange_4_listener($event) {\n      const input_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      i0.ɵɵtwoWayBindingSet(input_r4.value, $event) || (input_r4.value = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function AgentStepperCardComponent_div_14_div_1_div_2_Template_textarea_input_4_listener($event) {\n      const i_r5 = i0.ɵɵrestoreView(_r3).index;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onInputChange(i_r5, $event));\n    })(\"keydown.enter\", function AgentStepperCardComponent_div_14_div_1_div_2_Template_textarea_keydown_enter_4_listener($event) {\n      const i_r5 = i0.ɵɵrestoreView(_r3).index;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      ctx_r1.handleSendMessage(i_r5);\n      return i0.ɵɵresetView($event.preventDefault());\n    });\n    i0.ɵɵtext(5, \"              \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, AgentStepperCardComponent_div_14_div_1_div_2_button_6_Template, 2, 3, \"button\", 27);\n    i0.ɵɵelementStart(7, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function AgentStepperCardComponent_div_14_div_1_div_2_Template_button_click_7_listener() {\n      const i_r5 = i0.ɵɵrestoreView(_r3).index;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.handleSendMessage(i_r5));\n    });\n    i0.ɵɵelement(8, \"ava-icon\", 29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(9, AgentStepperCardComponent_div_14_div_1_div_2_div_9_Template, 2, 1, \"div\", 30);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const input_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(input_r4.inputName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.isInputDisabled(input_r4));\n    i0.ɵɵtwoWayProperty(\"ngModel\", input_r4.value);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isInputDisabled(input_r4))(\"placeholder\", input_r4.inputType === \"image\" ? \"Please attach an image file\" : \"Enter \" + input_r4.inputName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showFileUploadButton(input_r4));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", !input_r4.value && (!input_r4.files || input_r4.files.length === 0));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"iconSize\", 18);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", input_r4.files && input_r4.files.length > 0);\n  }\n}\nfunction AgentStepperCardComponent_div_14_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function AgentStepperCardComponent_div_14_div_1_div_3_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.toggleShowAllInputs());\n    });\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"ava-icon\", 41);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.showAllInputs ? \"Show Less\" : \"Show \" + ctx_r1.hiddenInputsCount + \" More\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"iconName\", ctx_r1.showAllInputs ? \"chevron-up\" : \"chevron-down\")(\"iconSize\", 14);\n  }\n}\nfunction AgentStepperCardComponent_div_14_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"div\", 20);\n    i0.ɵɵtemplate(2, AgentStepperCardComponent_div_14_div_1_div_2_Template, 10, 10, \"div\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, AgentStepperCardComponent_div_14_div_1_div_3_Template, 5, 3, \"div\", 22);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"scrollable\", ctx_r1.agent.inputs.length > ctx_r1.maxVisibleInputs);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.visibleInputs)(\"ngForTrackBy\", ctx_r1.trackByIndex);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hiddenInputsCount > 0);\n  }\n}\nfunction AgentStepperCardComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵtemplate(1, AgentStepperCardComponent_div_14_div_1_Template, 4, 5, \"div\", 18);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"@slideDown\", undefined);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.agent.hasInputs && ctx_r1.agent.inputs);\n  }\n}\nexport let AgentStepperCardComponent = /*#__PURE__*/(() => {\n  class AgentStepperCardComponent {\n    agent;\n    stepNumber = 1;\n    isFirst = false;\n    isLast = false;\n    isActive = false;\n    isCompleted = false;\n    inputChanged = new EventEmitter();\n    fileSelected = new EventEmitter();\n    messageSent = new EventEmitter();\n    stepCompleted = new EventEmitter();\n    fileInput;\n    isExpanded = false;\n    showAllInputs = false;\n    maxVisibleInputs = 2;\n    get visibleInputs() {\n      if (!this.agent.inputs) return [];\n      if (this.showAllInputs || this.agent.inputs.length <= this.maxVisibleInputs) {\n        return this.agent.inputs;\n      }\n      return this.agent.inputs.slice(0, this.maxVisibleInputs);\n    }\n    get hiddenInputsCount() {\n      if (!this.agent.inputs) return 0;\n      return Math.max(0, this.agent.inputs.length - this.maxVisibleInputs);\n    }\n    get stepperClass() {\n      const classes = ['stepper-circle'];\n      if (this.isCompleted) classes.push('completed');else if (this.isActive) classes.push('active');else classes.push('inactive');\n      return classes.join(' ');\n    }\n    toggleExpanded() {\n      if (this.agent.hasInputs) {\n        this.isExpanded = !this.isExpanded;\n      }\n    }\n    toggleShowAllInputs() {\n      this.showAllInputs = !this.showAllInputs;\n    }\n    onInputChange(inputIndex, event) {\n      const target = event.target;\n      this.inputChanged.emit({\n        inputIndex,\n        value: target.value\n      });\n    }\n    onFileInputClick(inputIndex) {\n      // Store the input index for file selection\n      this.fileInput.nativeElement.dataset['inputIndex'] = inputIndex.toString();\n      this.fileInput.nativeElement.click();\n    }\n    onFileSelected(event) {\n      const target = event.target;\n      const files = target.files;\n      const inputIndex = parseInt(target.dataset['inputIndex'] || '0');\n      if (files && files.length > 0) {\n        const fileArray = Array.from(files);\n        this.fileSelected.emit({\n          inputIndex,\n          files: fileArray\n        });\n      }\n    }\n    removeFile(inputIndex, fileIndex) {\n      if (this.agent.inputs && this.agent.inputs[inputIndex] && this.agent.inputs[inputIndex].files) {\n        this.agent.inputs[inputIndex].files.splice(fileIndex, 1);\n        this.fileSelected.emit({\n          inputIndex,\n          files: this.agent.inputs[inputIndex].files\n        });\n      }\n    }\n    handleSendMessage(inputIndex) {\n      if (!this.agent.inputs || !this.agent.inputs[inputIndex]) return;\n      const input = this.agent.inputs[inputIndex];\n      const hasValue = input.value && input.value.trim().length > 0;\n      const hasFiles = input.files && input.files.length > 0;\n      if (!hasValue && !hasFiles) return;\n      // Create payload with input value\n      const payload = {\n        inputIndex,\n        value: input.value || '',\n        files: input.files\n      };\n      // Emit the message sent event\n      this.messageSent.emit(payload);\n      // Mark step as completed and emit completion event\n      this.isCompleted = true;\n      this.stepCompleted.emit();\n      // Don't clear the input after sending - keep the value visible\n      // input.value = '';\n      // if (input.files) {\n      //   input.files = [];\n      // }\n    }\n    getAcceptedFileType(input) {\n      return input.inputType === 'image' ? '.png,.jpg,.jpeg,.gif,.bmp,.svg' : '.pdf,.doc,.docx,.txt,.csv,.xlsx,.xls,.ppt,.pptx,.png,.jpg,.jpeg,.gif,.bmp,.svg';\n    }\n    isInputDisabled(input) {\n      return input.inputType === 'image';\n    }\n    showFileUploadButton(input) {\n      return input.inputType === 'image' || input.inputType === 'text';\n    }\n    trackByIndex(index, item) {\n      return index;\n    }\n    static ɵfac = function AgentStepperCardComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AgentStepperCardComponent)();\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AgentStepperCardComponent,\n      selectors: [[\"app-agent-stepper-card\"]],\n      viewQuery: function AgentStepperCardComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n        }\n      },\n      inputs: {\n        agent: \"agent\",\n        stepNumber: \"stepNumber\",\n        isFirst: \"isFirst\",\n        isLast: \"isLast\",\n        isActive: \"isActive\",\n        isCompleted: \"isCompleted\"\n      },\n      outputs: {\n        inputChanged: \"inputChanged\",\n        fileSelected: \"fileSelected\",\n        messageSent: \"messageSent\",\n        stepCompleted: \"stepCompleted\"\n      },\n      decls: 17,\n      vars: 18,\n      consts: [[\"fileInput\", \"\"], [1, \"agent-stepper-card\"], [1, \"stepper-section\"], [\"iconName\", \"check\", \"iconColor\", \"white\", 3, \"iconSize\", 4, \"ngIf\"], [\"class\", \"stepper-line\", 3, \"active\", 4, \"ngIf\"], [1, \"card-content\"], [1, \"agent-header\", 3, \"click\"], [1, \"agent-info\"], [1, \"bot-icon\"], [\"iconName\", \"bot\", \"iconColor\", \"#0078E8\", 3, \"iconSize\"], [1, \"agent-name\", 3, \"title\"], [1, \"header-actions\"], [\"iconColor\", \"#444653\", 3, \"iconName\", \"iconSize\", \"title\", \"disabled\"], [\"class\", \"agent-details\", 4, \"ngIf\"], [\"type\", \"file\", \"multiple\", \"\", 2, \"display\", \"none\", 3, \"change\"], [\"iconName\", \"check\", \"iconColor\", \"white\", 3, \"iconSize\"], [1, \"stepper-line\"], [1, \"agent-details\"], [\"class\", \"input-section\", 4, \"ngIf\"], [1, \"input-section\"], [1, \"inputs-container\"], [\"class\", \"input-field-container\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"show-more-section\", 4, \"ngIf\"], [1, \"input-field-container\"], [1, \"input-label\"], [1, \"input-container\"], [1, \"input-textarea\", 3, \"ngModelChange\", \"input\", \"keydown.enter\", \"ngModel\", \"disabled\", \"placeholder\"], [\"class\", \"attach-btn\", 3, \"title\", \"disabled\", \"click\", 4, \"ngIf\"], [\"title\", \"Send\", 1, \"send-btn\", 3, \"click\", \"disabled\"], [\"iconName\", \"send-horizontal\", \"iconColor\", \"#03ACC1\", 3, \"iconSize\"], [\"class\", \"uploaded-files\", 4, \"ngIf\"], [1, \"attach-btn\", 3, \"click\", \"title\", \"disabled\"], [\"iconName\", \"paperclip\", \"iconColor\", \"#03ACC1\", 3, \"iconSize\"], [1, \"uploaded-files\"], [\"class\", \"file-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"file-item\"], [1, \"file-name\"], [\"title\", \"Remove file\", 1, \"remove-file\", 3, \"click\"], [\"iconName\", \"x\", \"iconColor\", \"#e53e3e\", 3, \"iconSize\"], [1, \"show-more-section\"], [1, \"show-more-btn\", 3, \"click\"], [\"iconColor\", \"#1A46A7\", 3, \"iconName\", \"iconSize\"]],\n      template: function AgentStepperCardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"div\");\n          i0.ɵɵtemplate(3, AgentStepperCardComponent_ava_icon_3_Template, 1, 1, \"ava-icon\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(4, AgentStepperCardComponent_div_4_Template, 1, 2, \"div\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 5)(6, \"div\", 6);\n          i0.ɵɵlistener(\"click\", function AgentStepperCardComponent_Template_div_click_6_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.agent.hasInputs ? ctx.toggleExpanded() : null);\n          });\n          i0.ɵɵelementStart(7, \"div\", 7)(8, \"div\", 8);\n          i0.ɵɵelement(9, \"ava-icon\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"h4\", 10);\n          i0.ɵɵtext(11);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(12, \"div\", 11);\n          i0.ɵɵelement(13, \"ava-icon\", 12);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(14, AgentStepperCardComponent_div_14_Template, 2, 2, \"div\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"input\", 14, 0);\n          i0.ɵɵlistener(\"change\", function AgentStepperCardComponent_Template_input_change_15_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onFileSelected($event));\n          });\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"active\", ctx.isActive);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassMap(ctx.stepperClass);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isCompleted);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLast);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"iconSize\", 20);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"title\", ctx.agent.name);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate(ctx.agent.name);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"expanded\", ctx.isExpanded)(\"disabled\", !ctx.agent.hasInputs);\n          i0.ɵɵproperty(\"iconName\", ctx.isExpanded ? \"chevron-up\" : \"chevron-down\")(\"iconSize\", 16)(\"title\", !ctx.agent.hasInputs ? \"No Input Required\" : \"\")(\"disabled\", !ctx.agent.hasInputs);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isExpanded);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, i1.NgIf, FormsModule, i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgModel, IconComponent],\n      styles: [\".agent-stepper-card[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: stretch;\\n  gap: 16px;\\n  margin-bottom: 24px;\\n  position: relative;\\n}\\n.agent-stepper-card[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 2rem;\\n}\\n\\n.stepper-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  width: 40px;\\n  flex-shrink: 0;\\n  height: 100%;\\n  padding-top: 24px;\\n}\\n\\n.stepper-circle[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background: #0084FF;\\n  color: white;\\n  border: 2px solid #0084FF;\\n  box-shadow: 0 0 0 4px rgba(0, 132, 255, 0.1);\\n}\\n.stepper-circle.active[_ngcontent-%COMP%], .stepper-circle.completed[_ngcontent-%COMP%] {\\n  background: #0084ff;\\n  color: white;\\n  border-color: #0084ff;\\n  box-shadow: 0 0 0 4px rgba(0, 132, 255, 0.25);\\n}\\n.stepper-circle.inactive[_ngcontent-%COMP%] {\\n  background: #e5e7eb;\\n  color: #6b7280;\\n  border: 2px solid #d1d5db;\\n}\\n.stepper-circle[_ngcontent-%COMP%]   .step-number[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  font-weight: 600;\\n}\\n\\n.stepper-line[_ngcontent-%COMP%] {\\n  width: 2px;\\n  flex: 1;\\n  margin-top: 8px;\\n  background: linear-gradient(180deg, #0084FF 0%, #E6F3FF 100%);\\n  border: none;\\n  min-height: 40px;\\n}\\n.stepper-line.active[_ngcontent-%COMP%] {\\n  background: linear-gradient(180deg, #0084FF 0%, #0084FF 100%);\\n}\\n\\n.card-content[_ngcontent-%COMP%] {\\n  flex-grow: 1;\\n  background: #e6f3ff;\\n  border: 1px solid #e6f3ff;\\n  border-radius: 16px;\\n  padding: 0;\\n  display: flex;\\n  flex-direction: column;\\n  transition: all 0.3s ease-in-out;\\n  overflow: hidden;\\n}\\n.card-content[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n}\\n\\n.agent-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  width: 100%;\\n  cursor: pointer;\\n  transition: background-color 0.2s ease;\\n  padding: 16px;\\n  box-sizing: border-box;\\n}\\n.agent-header[_ngcontent-%COMP%]   .agent-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n  flex: 1;\\n  min-width: 0;\\n  max-width: calc(100% - 40px);\\n}\\n.agent-header[_ngcontent-%COMP%]   .agent-info[_ngcontent-%COMP%]   .bot-icon[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  background: #fff;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  flex-shrink: 0;\\n  border: 2px solid transparent;\\n  transition: border-color 0.3s ease;\\n}\\n.agent-header[_ngcontent-%COMP%]   .agent-info[_ngcontent-%COMP%]   .agent-name[_ngcontent-%COMP%] {\\n  font-family: \\\"Mulish\\\", sans-serif;\\n  font-weight: 600;\\n  font-size: 22px;\\n  line-height: 100%;\\n  letter-spacing: 0%;\\n  color: #000000;\\n  margin: 0;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  flex: 1;\\n  min-width: 0;\\n}\\n.agent-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.agent-stepper-card.active[_ngcontent-%COMP%]   .bot-icon[_ngcontent-%COMP%] {\\n  border-color: #0084ff;\\n}\\n\\n.agent-details[_ngcontent-%COMP%] {\\n  position: static;\\n  padding: 0 16px 16px 16px;\\n  background: #e6f3ff;\\n  animation: _ngcontent-%COMP%_slideDown 0.4s ease-in-out;\\n}\\n\\n.input-section[_ngcontent-%COMP%] {\\n  padding: 0;\\n}\\n\\n.inputs-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n}\\n.inputs-container.scrollable[_ngcontent-%COMP%] {\\n  max-height: 300px;\\n  overflow-y: auto;\\n  padding-right: 8px;\\n}\\n.inputs-container.scrollable[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 4px;\\n}\\n.inputs-container.scrollable[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f7fafc;\\n  border-radius: 2px;\\n}\\n.inputs-container.scrollable[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #cbd5e0;\\n  border-radius: 2px;\\n}\\n.inputs-container.scrollable[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #a0aec0;\\n}\\n\\n.input-field-container[_ngcontent-%COMP%]   .input-label[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-bottom: 8px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #1a202c;\\n}\\n\\n.input-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  background: #fff;\\n  border: 2px solid;\\n  border: 2px solid #03acc1;\\n  border-radius: 16px;\\n  padding: 12px;\\n  margin: 1rem 0;\\n  box-sizing: border-box;\\n  min-height: 80px;\\n  transition: border-color 0.2s ease;\\n}\\n.input-container[_ngcontent-%COMP%]:focus-within {\\n  border-color: #03ACC1;\\n  box-shadow: 0 0 0 3px rgba(3, 172, 193, 0.1);\\n}\\n.input-container[_ngcontent-%COMP%]   .input-textarea[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border: none;\\n  resize: none;\\n  background: transparent;\\n  font-size: 14px;\\n  font-family: \\\"Inter\\\", sans-serif;\\n  line-height: 1.4;\\n  outline: none;\\n  padding: 0;\\n  padding-right: 48px;\\n  box-sizing: border-box;\\n  min-height: 3em;\\n  max-height: 4.2em;\\n  overflow-y: auto;\\n  color: #1a202c;\\n}\\n.input-container[_ngcontent-%COMP%]   .input-textarea[_ngcontent-%COMP%]::placeholder {\\n  color: #999;\\n}\\n.input-container[_ngcontent-%COMP%]   .input-textarea.disabled[_ngcontent-%COMP%] {\\n  background: #f7fafc;\\n  color: #a0aec0;\\n  cursor: not-allowed;\\n}\\n.input-container[_ngcontent-%COMP%]   .attach-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 8px;\\n  left: 12px;\\n  background: none;\\n  border: none;\\n  padding: 4px;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.input-container[_ngcontent-%COMP%]   .attach-btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: scale(1.1);\\n}\\n.input-container[_ngcontent-%COMP%]   .attach-btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n.input-container[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 8px;\\n  right: 12px;\\n  background: none;\\n  border: none;\\n  padding: 4px;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.input-container[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: scale(1.1);\\n}\\n.input-container[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n\\n.uploaded-files[_ngcontent-%COMP%] {\\n  margin-top: 12px;\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 8px;\\n}\\n.uploaded-files[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  background: #f7fafc;\\n  border: 1px solid #e2e8f0;\\n  border-radius: 6px;\\n  padding: 6px 10px;\\n  font-size: 12px;\\n}\\n.uploaded-files[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .file-name[_ngcontent-%COMP%] {\\n  color: #718096;\\n  max-width: 150px;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n.uploaded-files[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .remove-file[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 16px;\\n  height: 16px;\\n  border: none;\\n  background: transparent;\\n  cursor: pointer;\\n  border-radius: 2px;\\n  transition: background-color 0.2s ease;\\n}\\n.uploaded-files[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .remove-file[_ngcontent-%COMP%]:hover {\\n  background: #fed7d7;\\n}\\n\\n.show-more-section[_ngcontent-%COMP%] {\\n  margin-top: 16px;\\n  text-align: center;\\n}\\n.show-more-section[_ngcontent-%COMP%]   .show-more-btn[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 6px;\\n  background: none;\\n  border: none;\\n  color: #1A46A7;\\n  font-size: 14px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  padding: 8px 12px;\\n  border-radius: 6px;\\n  transition: all 0.2s ease;\\n}\\n.show-more-section[_ngcontent-%COMP%]   .show-more-btn[_ngcontent-%COMP%]:hover {\\n  background: #ebf4ff;\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideDown {\\n  from {\\n    opacity: 0;\\n    transform: translateY(-10px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .agent-stepper-card[_ngcontent-%COMP%] {\\n    gap: 12px;\\n    margin-bottom: 20px;\\n  }\\n  .stepper-section[_ngcontent-%COMP%] {\\n    width: 32px;\\n  }\\n  .stepper-circle[_ngcontent-%COMP%] {\\n    width: 28px;\\n    height: 28px;\\n    font-size: 12px;\\n  }\\n  .agent-header[_ngcontent-%COMP%] {\\n    padding: 12px 16px;\\n  }\\n  .agent-header[_ngcontent-%COMP%]   .agent-info[_ngcontent-%COMP%]   .agent-name[_ngcontent-%COMP%] {\\n    font-size: 15px;\\n  }\\n  .agent-details[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .input-container[_ngcontent-%COMP%] {\\n    padding: 10px;\\n  }\\n  .input-container[_ngcontent-%COMP%]   .input-textarea[_ngcontent-%COMP%] {\\n    min-height: 36px;\\n    font-size: 13px;\\n  }\\n  .input-container[_ngcontent-%COMP%]   .attach-btn[_ngcontent-%COMP%], \\n   .input-container[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%] {\\n    width: 28px;\\n    height: 28px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      data: {\n        animation: [trigger('slideDown', [transition(':enter', [style({\n          opacity: 0,\n          transform: 'translateY(-10px)'\n        }), animate('300ms ease-out', style({\n          opacity: 1,\n          transform: 'translateY(0)'\n        }))])])]\n      }\n    });\n  }\n  return AgentStepperCardComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "EventEmitter", "FormsModule", "IconComponent", "trigger", "style", "transition", "animate", "i0", "ɵɵelement", "ɵɵproperty", "ɵɵclassProp", "ctx_r1", "isCompleted", "ɵɵelementStart", "ɵɵlistener", "AgentStepperCardComponent_div_14_div_1_div_2_button_6_Template_button_click_0_listener", "ɵɵrestoreView", "_r6", "i_r5", "ɵɵnextContext", "index", "ɵɵresetView", "onFileInputClick", "ɵɵelementEnd", "input_r4", "inputType", "isInputDisabled", "ɵɵadvance", "ɵɵtext", "AgentStepperCardComponent_div_14_div_1_div_2_div_9_div_1_Template_button_click_3_listener", "fileIndex_r8", "_r7", "removeFile", "ɵɵtextInterpolate", "file_r9", "name", "ɵɵtemplate", "AgentStepperCardComponent_div_14_div_1_div_2_div_9_div_1_Template", "files", "ɵɵtwoWayListener", "AgentStepperCardComponent_div_14_div_1_div_2_Template_textarea_ngModelChange_4_listener", "$event", "_r3", "$implicit", "ɵɵtwoWayBindingSet", "value", "AgentStepperCardComponent_div_14_div_1_div_2_Template_textarea_input_4_listener", "onInputChange", "AgentStepperCardComponent_div_14_div_1_div_2_Template_textarea_keydown_enter_4_listener", "handleSendMessage", "preventDefault", "AgentStepperCardComponent_div_14_div_1_div_2_button_6_Template", "AgentStepperCardComponent_div_14_div_1_div_2_Template_button_click_7_listener", "AgentStepperCardComponent_div_14_div_1_div_2_div_9_Template", "inputName", "ɵɵtwoWayProperty", "showFileUploadButton", "length", "AgentStepperCardComponent_div_14_div_1_div_3_Template_button_click_1_listener", "_r10", "toggleShowAllInputs", "showAllInputs", "hiddenInputsCount", "AgentStepperCardComponent_div_14_div_1_div_2_Template", "AgentStepperCardComponent_div_14_div_1_div_3_Template", "agent", "inputs", "maxVisibleInputs", "visibleInputs", "trackByIndex", "AgentStepperCardComponent_div_14_div_1_Template", "undefined", "hasInputs", "AgentStepperCardComponent", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "isLast", "isActive", "inputChanged", "fileSelected", "messageSent", "stepCompleted", "fileInput", "isExpanded", "slice", "Math", "max", "stepperClass", "classes", "push", "join", "toggleExpanded", "inputIndex", "event", "target", "emit", "nativeElement", "dataset", "toString", "click", "onFileSelected", "parseInt", "fileArray", "Array", "from", "fileIndex", "splice", "input", "hasValue", "trim", "hasFiles", "payload", "getAcceptedFileType", "item", "selectors", "viewQuery", "AgentStepperCardComponent_Query", "rf", "ctx", "AgentStepperCardComponent_ava_icon_3_Template", "AgentStepperCardComponent_div_4_Template", "AgentStepperCardComponent_Template_div_click_6_listener", "_r1", "AgentStepperCardComponent_div_14_Template", "AgentStepperCardComponent_Template_input_change_15_listener", "ɵɵclassMap", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i2", "DefaultValueAccessor", "NgControlStatus", "NgModel", "styles", "data", "animation", "opacity", "transform"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\pages\\workflows\\workflow-execution\\components\\agent-stepper-card\\agent-stepper-card.component.ts", "C:\\console\\aava-ui-web\\projects\\shared\\pages\\workflows\\workflow-execution\\components\\agent-stepper-card\\agent-stepper-card.component.html"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { Component, EventEmitter, Input, Output, ViewChild, ElementRef } from '@angular/core';\nimport { FormsModule } from '@angular/forms';\nimport { IconComponent } from '@ava/play-comp-library';\nimport { AgentData, AgentInput } from '../workflow-playground/workflow-playground.component';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\n\n@Component({\n  selector: 'app-agent-stepper-card',\n  standalone: true,\n  imports: [\n    CommonModule,\n    FormsModule,\n    IconComponent\n  ],\n  templateUrl: './agent-stepper-card.component.html',\n  styleUrls: ['./agent-stepper-card.component.scss'],\n  animations: [\n    trigger('slideDown', [\n      transition(':enter', [\n        style({ opacity: 0, transform: 'translateY(-10px)' }),\n        animate('300ms ease-out', style({ opacity: 1, transform: 'translateY(0)' }))\n      ])\n    ])\n  ]\n})\nexport class AgentStepperCardComponent {\n  @Input() agent!: AgentData;\n  @Input() stepNumber: number = 1;\n  @Input() isFirst: boolean = false;\n  @Input() isLast: boolean = false;\n  @Input() isActive: boolean = false;\n  @Input() isCompleted: boolean = false;\n  \n  @Output() inputChanged = new EventEmitter<{inputIndex: number, value: string}>();\n  @Output() fileSelected = new EventEmitter<{inputIndex: number, files: File[]}>();\n  @Output() messageSent = new EventEmitter<{inputIndex: number, value: string, files?: File[]}>();\n  @Output() stepCompleted = new EventEmitter<void>();\n\n  @ViewChild('fileInput') fileInput!: ElementRef;\n\n  isExpanded: boolean = false;\n  showAllInputs: boolean = false;\n  maxVisibleInputs: number = 2;\n\n  get visibleInputs(): AgentInput[] {\n    if (!this.agent.inputs) return [];\n    if (this.showAllInputs || this.agent.inputs.length <= this.maxVisibleInputs) {\n      return this.agent.inputs;\n    }\n    return this.agent.inputs.slice(0, this.maxVisibleInputs);\n  }\n\n  get hiddenInputsCount(): number {\n    if (!this.agent.inputs) return 0;\n    return Math.max(0, this.agent.inputs.length - this.maxVisibleInputs);\n  }\n\n  get stepperClass(): string {\n    const classes = ['stepper-circle'];\n    if (this.isCompleted) classes.push('completed');\n    else if (this.isActive) classes.push('active');\n    else classes.push('inactive');\n    return classes.join(' ');\n  }\n\n  toggleExpanded(): void {\n    if (this.agent.hasInputs) {\n      this.isExpanded = !this.isExpanded;\n    }\n  }\n\n  toggleShowAllInputs(): void {\n    this.showAllInputs = !this.showAllInputs;\n  }\n\n  onInputChange(inputIndex: number, event: Event): void {\n    const target = event.target as HTMLTextAreaElement;\n    this.inputChanged.emit({ inputIndex, value: target.value });\n  }\n\n  onFileInputClick(inputIndex: number): void {\n    // Store the input index for file selection\n    this.fileInput.nativeElement.dataset['inputIndex'] = inputIndex.toString();\n    this.fileInput.nativeElement.click();\n  }\n\n  onFileSelected(event: Event): void {\n    const target = event.target as HTMLInputElement;\n    const files = target.files;\n    const inputIndex = parseInt(target.dataset['inputIndex'] || '0');\n    \n    if (files && files.length > 0) {\n      const fileArray = Array.from(files);\n      this.fileSelected.emit({ inputIndex, files: fileArray });\n    }\n  }\n\n  removeFile(inputIndex: number, fileIndex: number): void {\n    if (this.agent.inputs && this.agent.inputs[inputIndex] && this.agent.inputs[inputIndex].files) {\n      this.agent.inputs[inputIndex].files!.splice(fileIndex, 1);\n      this.fileSelected.emit({ inputIndex, files: this.agent.inputs[inputIndex].files! });\n    }\n  }\n\n  handleSendMessage(inputIndex: number): void {\n    if (!this.agent.inputs || !this.agent.inputs[inputIndex]) return;\n\n    const input = this.agent.inputs[inputIndex];\n    const hasValue = input.value && input.value.trim().length > 0;\n    const hasFiles = input.files && input.files.length > 0;\n\n    if (!hasValue && !hasFiles) return;\n\n    // Create payload with input value\n    const payload = {\n      inputIndex,\n      value: input.value || '',\n      files: input.files\n    };\n\n    // Emit the message sent event\n    this.messageSent.emit(payload);\n\n    // Mark step as completed and emit completion event\n    this.isCompleted = true;\n    this.stepCompleted.emit();\n\n    // Don't clear the input after sending - keep the value visible\n    // input.value = '';\n    // if (input.files) {\n    //   input.files = [];\n    // }\n  }\n\n  getAcceptedFileType(input: AgentInput): string {\n    return input.inputType === 'image' \n      ? '.png,.jpg,.jpeg,.gif,.bmp,.svg'\n      : '.pdf,.doc,.docx,.txt,.csv,.xlsx,.xls,.ppt,.pptx,.png,.jpg,.jpeg,.gif,.bmp,.svg';\n  }\n\n  isInputDisabled(input: AgentInput): boolean {\n    return input.inputType === 'image';\n  }\n\n  showFileUploadButton(input: AgentInput): boolean {\n    return input.inputType === 'image' || input.inputType === 'text';\n  }\n\n  trackByIndex(index: number, item: any): number {\n    return index;\n  }\n}\n", "<div class=\"agent-stepper-card\" [class.active]=\"isActive\">\n  <!-- Stepper Section -->\n  <div class=\"stepper-section\">\n    <!-- Stepper Circle -->\n    <div [class]=\"stepperClass\">\n      <ava-icon \n        *ngIf=\"isCompleted\" \n        iconName=\"check\" \n        [iconSize]=\"16\" \n        iconColor=\"white\">\n      </ava-icon>\n    </div>\n    \n    <!-- Connector Line -->\n    <div *ngIf=\"!isLast\" class=\"stepper-line\" [class.active]=\"isCompleted\"></div>\n  </div>\n\n  <!-- Card Content -->\n  <div class=\"card-content\">\n    <!-- Agent Header -->\n    <div class=\"agent-header\" (click)=\"agent.hasInputs ? toggleExpanded() : null\">\n      <div class=\"agent-info\">\n        <div class=\"bot-icon\">\n          <ava-icon\n            iconName=\"bot\"\n            [iconSize]=\"20\"\n            iconColor=\"#0078E8\">\n          </ava-icon>\n        </div>\n        <h4 class=\"agent-name\" [title]=\"agent.name\">{{ agent.name }}</h4>\n      </div>\n\n      <div class=\"header-actions\">\n        <ava-icon\n          [class.expanded]=\"isExpanded\"\n          [iconName]=\"isExpanded ? 'chevron-up' : 'chevron-down'\"\n          [iconSize]=\"16\"\n          [title]=\"!agent.hasInputs ? 'No Input Required' : ''\"\n          [disabled]=\"!agent.hasInputs\"\n          [class.disabled]=\"!agent.hasInputs\"\n          iconColor=\"#444653\">\n        </ava-icon>\n      </div>\n    </div>\n\n    <!-- Expanded Content -->\n    <div class=\"agent-details\" *ngIf=\"isExpanded\" [@slideDown]>\n      <!-- Input Fields Only -->\n      <div class=\"input-section\" *ngIf=\"agent.hasInputs && agent.inputs\">\n        <div class=\"inputs-container\" [class.scrollable]=\"agent.inputs.length > maxVisibleInputs\">\n          <div \n            class=\"input-field-container\" \n            *ngFor=\"let input of visibleInputs; let i = index; trackBy: trackByIndex\">\n            \n            <label class=\"input-label\">{{ input.inputName }}</label>\n            \n            <div class=\"input-container\">\n              <textarea\n                [(ngModel)]=\"input.value\"\n                [disabled]=\"isInputDisabled(input)\"\n                (input)=\"onInputChange(i, $event)\"\n                (keydown.enter)=\"handleSendMessage(i); $event.preventDefault()\"\n                [placeholder]=\"input.inputType === 'image' ? 'Please attach an image file' : 'Enter ' + input.inputName\"\n                class=\"input-textarea\"\n                [class.disabled]=\"isInputDisabled(input)\">\n              </textarea>\n\n              <button\n                *ngIf=\"showFileUploadButton(input)\"\n                class=\"attach-btn\"\n                [title]=\"input.inputType === 'image' ? 'Attach Image' : 'Attach File'\"\n                [disabled]=\"input.inputType === 'text' && isInputDisabled(input)\"\n                (click)=\"onFileInputClick(i)\">\n                <ava-icon\n                  iconName=\"paperclip\"\n                  [iconSize]=\"18\"\n                  iconColor=\"#03ACC1\">\n                </ava-icon>\n              </button>\n\n              <button\n                class=\"send-btn\"\n                title=\"Send\"\n                (click)=\"handleSendMessage(i)\"\n                [disabled]=\"!input.value && (!input.files || input.files.length === 0)\">\n                <ava-icon\n                  iconName=\"send-horizontal\"\n                  [iconSize]=\"18\"\n                  iconColor=\"#03ACC1\">\n                </ava-icon>\n              </button>\n            </div>\n\n            <!-- Display uploaded files -->\n            <div class=\"uploaded-files\" *ngIf=\"input.files && input.files.length > 0\">\n              <div\n                class=\"file-item\"\n                *ngFor=\"let file of input.files; let fileIndex = index\">\n                <span class=\"file-name\">{{ file.name }}</span>\n                <button\n                  class=\"remove-file\"\n                  (click)=\"removeFile(i, fileIndex)\"\n                  title=\"Remove file\">\n                  <ava-icon iconName=\"x\" [iconSize]=\"12\" iconColor=\"#e53e3e\"></ava-icon>\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Show More/Less Toggle -->\n        <div class=\"show-more-section\" *ngIf=\"hiddenInputsCount > 0\">\n          <button class=\"show-more-btn\" (click)=\"toggleShowAllInputs()\">\n            <span>{{ showAllInputs ? 'Show Less' : 'Show ' + hiddenInputsCount + ' More' }}</span>\n            <ava-icon\n              [iconName]=\"showAllInputs ? 'chevron-up' : 'chevron-down'\"\n              [iconSize]=\"14\"\n              iconColor=\"#1A46A7\">\n            </ava-icon>\n          </button>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Hidden file input -->\n  <input\n    #fileInput\n    type=\"file\"\n    style=\"display: none\"\n    multiple\n    (change)=\"onFileSelected($event)\">\n</div>\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAAoBC,YAAY,QAA8C,eAAe;AAC7F,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,aAAa,QAAQ,wBAAwB;AAEtD,SAASC,OAAO,EAASC,KAAK,EAAEC,UAAU,EAAEC,OAAO,QAAQ,qBAAqB;;;;;;;ICA1EC,EAAA,CAAAC,SAAA,mBAKW;;;IAFTD,EAAA,CAAAE,UAAA,gBAAe;;;;;IAMnBF,EAAA,CAAAC,SAAA,cAA6E;;;;IAAnCD,EAAA,CAAAG,WAAA,WAAAC,MAAA,CAAAC,WAAA,CAA4B;;;;;;IAqD5DL,EAAA,CAAAM,cAAA,iBAKgC;IAA9BN,EAAA,CAAAO,UAAA,mBAAAC,uFAAA;MAAAR,EAAA,CAAAS,aAAA,CAAAC,GAAA;MAAA,MAAAC,IAAA,GAAAX,EAAA,CAAAY,aAAA,GAAAC,KAAA;MAAA,MAAAT,MAAA,GAAAJ,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAc,WAAA,CAASV,MAAA,CAAAW,gBAAA,CAAAJ,IAAA,CAAmB;IAAA,EAAC;IAC7BX,EAAA,CAAAC,SAAA,mBAIW;IACbD,EAAA,CAAAgB,YAAA,EAAS;;;;;IAPPhB,EADA,CAAAE,UAAA,UAAAe,QAAA,CAAAC,SAAA,8CAAsE,aAAAD,QAAA,CAAAC,SAAA,eAAAd,MAAA,CAAAe,eAAA,CAAAF,QAAA,EACL;IAI/DjB,EAAA,CAAAoB,SAAA,EAAe;IAAfpB,EAAA,CAAAE,UAAA,gBAAe;;;;;;IAuBjBF,EAHF,CAAAM,cAAA,cAE0D,eAChC;IAAAN,EAAA,CAAAqB,MAAA,GAAe;IAAArB,EAAA,CAAAgB,YAAA,EAAO;IAC9ChB,EAAA,CAAAM,cAAA,iBAGsB;IADpBN,EAAA,CAAAO,UAAA,mBAAAe,0FAAA;MAAA,MAAAC,YAAA,GAAAvB,EAAA,CAAAS,aAAA,CAAAe,GAAA,EAAAX,KAAA;MAAA,MAAAF,IAAA,GAAAX,EAAA,CAAAY,aAAA,IAAAC,KAAA;MAAA,MAAAT,MAAA,GAAAJ,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAc,WAAA,CAASV,MAAA,CAAAqB,UAAA,CAAAd,IAAA,EAAAY,YAAA,CAAwB;IAAA,EAAC;IAElCvB,EAAA,CAAAC,SAAA,mBAAsE;IAE1ED,EADE,CAAAgB,YAAA,EAAS,EACL;;;;IAPoBhB,EAAA,CAAAoB,SAAA,GAAe;IAAfpB,EAAA,CAAA0B,iBAAA,CAAAC,OAAA,CAAAC,IAAA,CAAe;IAKd5B,EAAA,CAAAoB,SAAA,GAAe;IAAfpB,EAAA,CAAAE,UAAA,gBAAe;;;;;IAT5CF,EAAA,CAAAM,cAAA,cAA0E;IACxEN,EAAA,CAAA6B,UAAA,IAAAC,iEAAA,kBAE0D;IAS5D9B,EAAA,CAAAgB,YAAA,EAAM;;;;IATehB,EAAA,CAAAoB,SAAA,EAAgB;IAAhBpB,EAAA,CAAAE,UAAA,YAAAe,QAAA,CAAAc,KAAA,CAAgB;;;;;;IA3CrC/B,EAJF,CAAAM,cAAA,cAE4E,gBAE/C;IAAAN,EAAA,CAAAqB,MAAA,GAAqB;IAAArB,EAAA,CAAAgB,YAAA,EAAQ;IAGtDhB,EADF,CAAAM,cAAA,cAA6B,mBAQiB;IAN1CN,EAAA,CAAAgC,gBAAA,2BAAAC,wFAAAC,MAAA;MAAA,MAAAjB,QAAA,GAAAjB,EAAA,CAAAS,aAAA,CAAA0B,GAAA,EAAAC,SAAA;MAAApC,EAAA,CAAAqC,kBAAA,CAAApB,QAAA,CAAAqB,KAAA,EAAAJ,MAAA,MAAAjB,QAAA,CAAAqB,KAAA,GAAAJ,MAAA;MAAA,OAAAlC,EAAA,CAAAc,WAAA,CAAAoB,MAAA;IAAA,EAAyB;IAGzBlC,EADA,CAAAO,UAAA,mBAAAgC,gFAAAL,MAAA;MAAA,MAAAvB,IAAA,GAAAX,EAAA,CAAAS,aAAA,CAAA0B,GAAA,EAAAtB,KAAA;MAAA,MAAAT,MAAA,GAAAJ,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAc,WAAA,CAASV,MAAA,CAAAoC,aAAA,CAAA7B,IAAA,EAAAuB,MAAA,CAAwB;IAAA,EAAC,2BAAAO,wFAAAP,MAAA;MAAA,MAAAvB,IAAA,GAAAX,EAAA,CAAAS,aAAA,CAAA0B,GAAA,EAAAtB,KAAA;MAAA,MAAAT,MAAA,GAAAJ,EAAA,CAAAY,aAAA;MACjBR,MAAA,CAAAsC,iBAAA,CAAA/B,IAAA,CAAoB;MAAA,OAAAX,EAAA,CAAAc,WAAA,CAAEoB,MAAA,CAAAS,cAAA,EAAuB;IAAA,EAAC;IAIjE3C,EAAA,CAAAqB,MAAA;IAAArB,EAAA,CAAAgB,YAAA,EAAW;IAEXhB,EAAA,CAAA6B,UAAA,IAAAe,8DAAA,qBAKgC;IAQhC5C,EAAA,CAAAM,cAAA,iBAI0E;IADxEN,EAAA,CAAAO,UAAA,mBAAAsC,8EAAA;MAAA,MAAAlC,IAAA,GAAAX,EAAA,CAAAS,aAAA,CAAA0B,GAAA,EAAAtB,KAAA;MAAA,MAAAT,MAAA,GAAAJ,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAc,WAAA,CAASV,MAAA,CAAAsC,iBAAA,CAAA/B,IAAA,CAAoB;IAAA,EAAC;IAE9BX,EAAA,CAAAC,SAAA,mBAIW;IAEfD,EADE,CAAAgB,YAAA,EAAS,EACL;IAGNhB,EAAA,CAAA6B,UAAA,IAAAiB,2DAAA,kBAA0E;IAa5E9C,EAAA,CAAAgB,YAAA,EAAM;;;;;IArDuBhB,EAAA,CAAAoB,SAAA,GAAqB;IAArBpB,EAAA,CAAA0B,iBAAA,CAAAT,QAAA,CAAA8B,SAAA,CAAqB;IAU5C/C,EAAA,CAAAoB,SAAA,GAAyC;IAAzCpB,EAAA,CAAAG,WAAA,aAAAC,MAAA,CAAAe,eAAA,CAAAF,QAAA,EAAyC;IANzCjB,EAAA,CAAAgD,gBAAA,YAAA/B,QAAA,CAAAqB,KAAA,CAAyB;IAIzBtC,EAHA,CAAAE,UAAA,aAAAE,MAAA,CAAAe,eAAA,CAAAF,QAAA,EAAmC,gBAAAA,QAAA,CAAAC,SAAA,0DAAAD,QAAA,CAAA8B,SAAA,CAGqE;IAMvG/C,EAAA,CAAAoB,SAAA,GAAiC;IAAjCpB,EAAA,CAAAE,UAAA,SAAAE,MAAA,CAAA6C,oBAAA,CAAAhC,QAAA,EAAiC;IAgBlCjB,EAAA,CAAAoB,SAAA,EAAuE;IAAvEpB,EAAA,CAAAE,UAAA,cAAAe,QAAA,CAAAqB,KAAA,MAAArB,QAAA,CAAAc,KAAA,IAAAd,QAAA,CAAAc,KAAA,CAAAmB,MAAA,QAAuE;IAGrElD,EAAA,CAAAoB,SAAA,EAAe;IAAfpB,EAAA,CAAAE,UAAA,gBAAe;IAOQF,EAAA,CAAAoB,SAAA,EAA2C;IAA3CpB,EAAA,CAAAE,UAAA,SAAAe,QAAA,CAAAc,KAAA,IAAAd,QAAA,CAAAc,KAAA,CAAAmB,MAAA,KAA2C;;;;;;IAkB1ElD,EADF,CAAAM,cAAA,cAA6D,iBACG;IAAhCN,EAAA,CAAAO,UAAA,mBAAA4C,8EAAA;MAAAnD,EAAA,CAAAS,aAAA,CAAA2C,IAAA;MAAA,MAAAhD,MAAA,GAAAJ,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAc,WAAA,CAASV,MAAA,CAAAiD,mBAAA,EAAqB;IAAA,EAAC;IAC3DrD,EAAA,CAAAM,cAAA,WAAM;IAAAN,EAAA,CAAAqB,MAAA,GAAyE;IAAArB,EAAA,CAAAgB,YAAA,EAAO;IACtFhB,EAAA,CAAAC,SAAA,mBAIW;IAEfD,EADE,CAAAgB,YAAA,EAAS,EACL;;;;IAPIhB,EAAA,CAAAoB,SAAA,GAAyE;IAAzEpB,EAAA,CAAA0B,iBAAA,CAAAtB,MAAA,CAAAkD,aAAA,2BAAAlD,MAAA,CAAAmD,iBAAA,WAAyE;IAE7EvD,EAAA,CAAAoB,SAAA,EAA0D;IAC1DpB,EADA,CAAAE,UAAA,aAAAE,MAAA,CAAAkD,aAAA,iCAA0D,gBAC3C;;;;;IAnErBtD,EADF,CAAAM,cAAA,cAAmE,cACyB;IACxFN,EAAA,CAAA6B,UAAA,IAAA2B,qDAAA,oBAE4E;IAwD9ExD,EAAA,CAAAgB,YAAA,EAAM;IAGNhB,EAAA,CAAA6B,UAAA,IAAA4B,qDAAA,kBAA6D;IAU/DzD,EAAA,CAAAgB,YAAA,EAAM;;;;IAxE0BhB,EAAA,CAAAoB,SAAA,EAA2D;IAA3DpB,EAAA,CAAAG,WAAA,eAAAC,MAAA,CAAAsD,KAAA,CAAAC,MAAA,CAAAT,MAAA,GAAA9C,MAAA,CAAAwD,gBAAA,CAA2D;IAGnE5D,EAAA,CAAAoB,SAAA,EAAkB;IAAepB,EAAjC,CAAAE,UAAA,YAAAE,MAAA,CAAAyD,aAAA,CAAkB,iBAAAzD,MAAA,CAAA0D,YAAA,CAAoC;IA2D5C9D,EAAA,CAAAoB,SAAA,EAA2B;IAA3BpB,EAAA,CAAAE,UAAA,SAAAE,MAAA,CAAAmD,iBAAA,KAA2B;;;;;IAjE/DvD,EAAA,CAAAM,cAAA,cAA2D;IAEzDN,EAAA,CAAA6B,UAAA,IAAAkC,+CAAA,kBAAmE;IA0ErE/D,EAAA,CAAAgB,YAAA,EAAM;;;;IA5EwChB,EAAA,CAAAE,UAAA,eAAA8D,SAAA,CAAY;IAE5BhE,EAAA,CAAAoB,SAAA,EAAqC;IAArCpB,EAAA,CAAAE,UAAA,SAAAE,MAAA,CAAAsD,KAAA,CAAAO,SAAA,IAAA7D,MAAA,CAAAsD,KAAA,CAAAC,MAAA,CAAqC;;;ADtBvE,WAAaO,yBAAyB;EAAhC,MAAOA,yBAAyB;IAC3BR,KAAK;IACLS,UAAU,GAAW,CAAC;IACtBC,OAAO,GAAY,KAAK;IACxBC,MAAM,GAAY,KAAK;IACvBC,QAAQ,GAAY,KAAK;IACzBjE,WAAW,GAAY,KAAK;IAE3BkE,YAAY,GAAG,IAAI9E,YAAY,EAAuC;IACtE+E,YAAY,GAAG,IAAI/E,YAAY,EAAuC;IACtEgF,WAAW,GAAG,IAAIhF,YAAY,EAAuD;IACrFiF,aAAa,GAAG,IAAIjF,YAAY,EAAQ;IAE1BkF,SAAS;IAEjCC,UAAU,GAAY,KAAK;IAC3BtB,aAAa,GAAY,KAAK;IAC9BM,gBAAgB,GAAW,CAAC;IAE5B,IAAIC,aAAaA,CAAA;MACf,IAAI,CAAC,IAAI,CAACH,KAAK,CAACC,MAAM,EAAE,OAAO,EAAE;MACjC,IAAI,IAAI,CAACL,aAAa,IAAI,IAAI,CAACI,KAAK,CAACC,MAAM,CAACT,MAAM,IAAI,IAAI,CAACU,gBAAgB,EAAE;QAC3E,OAAO,IAAI,CAACF,KAAK,CAACC,MAAM;MAC1B;MACA,OAAO,IAAI,CAACD,KAAK,CAACC,MAAM,CAACkB,KAAK,CAAC,CAAC,EAAE,IAAI,CAACjB,gBAAgB,CAAC;IAC1D;IAEA,IAAIL,iBAAiBA,CAAA;MACnB,IAAI,CAAC,IAAI,CAACG,KAAK,CAACC,MAAM,EAAE,OAAO,CAAC;MAChC,OAAOmB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAACrB,KAAK,CAACC,MAAM,CAACT,MAAM,GAAG,IAAI,CAACU,gBAAgB,CAAC;IACtE;IAEA,IAAIoB,YAAYA,CAAA;MACd,MAAMC,OAAO,GAAG,CAAC,gBAAgB,CAAC;MAClC,IAAI,IAAI,CAAC5E,WAAW,EAAE4E,OAAO,CAACC,IAAI,CAAC,WAAW,CAAC,CAAC,KAC3C,IAAI,IAAI,CAACZ,QAAQ,EAAEW,OAAO,CAACC,IAAI,CAAC,QAAQ,CAAC,CAAC,KAC1CD,OAAO,CAACC,IAAI,CAAC,UAAU,CAAC;MAC7B,OAAOD,OAAO,CAACE,IAAI,CAAC,GAAG,CAAC;IAC1B;IAEAC,cAAcA,CAAA;MACZ,IAAI,IAAI,CAAC1B,KAAK,CAACO,SAAS,EAAE;QACxB,IAAI,CAACW,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;MACpC;IACF;IAEAvB,mBAAmBA,CAAA;MACjB,IAAI,CAACC,aAAa,GAAG,CAAC,IAAI,CAACA,aAAa;IAC1C;IAEAd,aAAaA,CAAC6C,UAAkB,EAAEC,KAAY;MAC5C,MAAMC,MAAM,GAAGD,KAAK,CAACC,MAA6B;MAClD,IAAI,CAAChB,YAAY,CAACiB,IAAI,CAAC;QAAEH,UAAU;QAAE/C,KAAK,EAAEiD,MAAM,CAACjD;MAAK,CAAE,CAAC;IAC7D;IAEAvB,gBAAgBA,CAACsE,UAAkB;MACjC;MACA,IAAI,CAACV,SAAS,CAACc,aAAa,CAACC,OAAO,CAAC,YAAY,CAAC,GAAGL,UAAU,CAACM,QAAQ,EAAE;MAC1E,IAAI,CAAChB,SAAS,CAACc,aAAa,CAACG,KAAK,EAAE;IACtC;IAEAC,cAAcA,CAACP,KAAY;MACzB,MAAMC,MAAM,GAAGD,KAAK,CAACC,MAA0B;MAC/C,MAAMxD,KAAK,GAAGwD,MAAM,CAACxD,KAAK;MAC1B,MAAMsD,UAAU,GAAGS,QAAQ,CAACP,MAAM,CAACG,OAAO,CAAC,YAAY,CAAC,IAAI,GAAG,CAAC;MAEhE,IAAI3D,KAAK,IAAIA,KAAK,CAACmB,MAAM,GAAG,CAAC,EAAE;QAC7B,MAAM6C,SAAS,GAAGC,KAAK,CAACC,IAAI,CAAClE,KAAK,CAAC;QACnC,IAAI,CAACyC,YAAY,CAACgB,IAAI,CAAC;UAAEH,UAAU;UAAEtD,KAAK,EAAEgE;QAAS,CAAE,CAAC;MAC1D;IACF;IAEAtE,UAAUA,CAAC4D,UAAkB,EAAEa,SAAiB;MAC9C,IAAI,IAAI,CAACxC,KAAK,CAACC,MAAM,IAAI,IAAI,CAACD,KAAK,CAACC,MAAM,CAAC0B,UAAU,CAAC,IAAI,IAAI,CAAC3B,KAAK,CAACC,MAAM,CAAC0B,UAAU,CAAC,CAACtD,KAAK,EAAE;QAC7F,IAAI,CAAC2B,KAAK,CAACC,MAAM,CAAC0B,UAAU,CAAC,CAACtD,KAAM,CAACoE,MAAM,CAACD,SAAS,EAAE,CAAC,CAAC;QACzD,IAAI,CAAC1B,YAAY,CAACgB,IAAI,CAAC;UAAEH,UAAU;UAAEtD,KAAK,EAAE,IAAI,CAAC2B,KAAK,CAACC,MAAM,CAAC0B,UAAU,CAAC,CAACtD;QAAM,CAAE,CAAC;MACrF;IACF;IAEAW,iBAAiBA,CAAC2C,UAAkB;MAClC,IAAI,CAAC,IAAI,CAAC3B,KAAK,CAACC,MAAM,IAAI,CAAC,IAAI,CAACD,KAAK,CAACC,MAAM,CAAC0B,UAAU,CAAC,EAAE;MAE1D,MAAMe,KAAK,GAAG,IAAI,CAAC1C,KAAK,CAACC,MAAM,CAAC0B,UAAU,CAAC;MAC3C,MAAMgB,QAAQ,GAAGD,KAAK,CAAC9D,KAAK,IAAI8D,KAAK,CAAC9D,KAAK,CAACgE,IAAI,EAAE,CAACpD,MAAM,GAAG,CAAC;MAC7D,MAAMqD,QAAQ,GAAGH,KAAK,CAACrE,KAAK,IAAIqE,KAAK,CAACrE,KAAK,CAACmB,MAAM,GAAG,CAAC;MAEtD,IAAI,CAACmD,QAAQ,IAAI,CAACE,QAAQ,EAAE;MAE5B;MACA,MAAMC,OAAO,GAAG;QACdnB,UAAU;QACV/C,KAAK,EAAE8D,KAAK,CAAC9D,KAAK,IAAI,EAAE;QACxBP,KAAK,EAAEqE,KAAK,CAACrE;OACd;MAED;MACA,IAAI,CAAC0C,WAAW,CAACe,IAAI,CAACgB,OAAO,CAAC;MAE9B;MACA,IAAI,CAACnG,WAAW,GAAG,IAAI;MACvB,IAAI,CAACqE,aAAa,CAACc,IAAI,EAAE;MAEzB;MACA;MACA;MACA;MACA;IACF;IAEAiB,mBAAmBA,CAACL,KAAiB;MACnC,OAAOA,KAAK,CAAClF,SAAS,KAAK,OAAO,GAC9B,gCAAgC,GAChC,gFAAgF;IACtF;IAEAC,eAAeA,CAACiF,KAAiB;MAC/B,OAAOA,KAAK,CAAClF,SAAS,KAAK,OAAO;IACpC;IAEA+B,oBAAoBA,CAACmD,KAAiB;MACpC,OAAOA,KAAK,CAAClF,SAAS,KAAK,OAAO,IAAIkF,KAAK,CAAClF,SAAS,KAAK,MAAM;IAClE;IAEA4C,YAAYA,CAACjD,KAAa,EAAE6F,IAAS;MACnC,OAAO7F,KAAK;IACd;;uCA7HWqD,yBAAyB;IAAA;;YAAzBA,yBAAyB;MAAAyC,SAAA;MAAAC,SAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;UCtBlC9G,EAJJ,CAAAM,cAAA,aAA0D,aAE3B,UAEC;UAC1BN,EAAA,CAAA6B,UAAA,IAAAmF,6CAAA,sBAIoB;UAEtBhH,EAAA,CAAAgB,YAAA,EAAM;UAGNhB,EAAA,CAAA6B,UAAA,IAAAoF,wCAAA,iBAAuE;UACzEjH,EAAA,CAAAgB,YAAA,EAAM;UAKJhB,EAFF,CAAAM,cAAA,aAA0B,aAEsD;UAApDN,EAAA,CAAAO,UAAA,mBAAA2G,wDAAA;YAAAlH,EAAA,CAAAS,aAAA,CAAA0G,GAAA;YAAA,OAAAnH,EAAA,CAAAc,WAAA,CAAAiG,GAAA,CAAArD,KAAA,CAAAO,SAAA,GAA2B8C,GAAA,CAAA3B,cAAA,EAAgB,GAAG,IAAI;UAAA,EAAC;UAEzEpF,EADF,CAAAM,cAAA,aAAwB,aACA;UACpBN,EAAA,CAAAC,SAAA,kBAIW;UACbD,EAAA,CAAAgB,YAAA,EAAM;UACNhB,EAAA,CAAAM,cAAA,cAA4C;UAAAN,EAAA,CAAAqB,MAAA,IAAgB;UAC9DrB,EAD8D,CAAAgB,YAAA,EAAK,EAC7D;UAENhB,EAAA,CAAAM,cAAA,eAA4B;UAC1BN,EAAA,CAAAC,SAAA,oBAQW;UAEfD,EADE,CAAAgB,YAAA,EAAM,EACF;UAGNhB,EAAA,CAAA6B,UAAA,KAAAuF,yCAAA,kBAA2D;UA6E7DpH,EAAA,CAAAgB,YAAA,EAAM;UAGNhB,EAAA,CAAAM,cAAA,oBAKoC;UAAlCN,EAAA,CAAAO,UAAA,oBAAA8G,4DAAAnF,MAAA;YAAAlC,EAAA,CAAAS,aAAA,CAAA0G,GAAA;YAAA,OAAAnH,EAAA,CAAAc,WAAA,CAAUiG,GAAA,CAAAlB,cAAA,CAAA3D,MAAA,CAAsB;UAAA,EAAC;UACrClC,EANE,CAAAgB,YAAA,EAKoC,EAChC;;;UApI0BhB,EAAA,CAAAG,WAAA,WAAA4G,GAAA,CAAAzC,QAAA,CAAyB;UAIhDtE,EAAA,CAAAoB,SAAA,GAAsB;UAAtBpB,EAAA,CAAAsH,UAAA,CAAAP,GAAA,CAAA/B,YAAA,CAAsB;UAEtBhF,EAAA,CAAAoB,SAAA,EAAiB;UAAjBpB,EAAA,CAAAE,UAAA,SAAA6G,GAAA,CAAA1G,WAAA,CAAiB;UAQhBL,EAAA,CAAAoB,SAAA,EAAa;UAAbpB,EAAA,CAAAE,UAAA,UAAA6G,GAAA,CAAA1C,MAAA,CAAa;UAWXrE,EAAA,CAAAoB,SAAA,GAAe;UAAfpB,EAAA,CAAAE,UAAA,gBAAe;UAIIF,EAAA,CAAAoB,SAAA,EAAoB;UAApBpB,EAAA,CAAAE,UAAA,UAAA6G,GAAA,CAAArD,KAAA,CAAA9B,IAAA,CAAoB;UAAC5B,EAAA,CAAAoB,SAAA,EAAgB;UAAhBpB,EAAA,CAAA0B,iBAAA,CAAAqF,GAAA,CAAArD,KAAA,CAAA9B,IAAA,CAAgB;UAK1D5B,EAAA,CAAAoB,SAAA,GAA6B;UAK7BpB,EALA,CAAAG,WAAA,aAAA4G,GAAA,CAAAnC,UAAA,CAA6B,cAAAmC,GAAA,CAAArD,KAAA,CAAAO,SAAA,CAKM;UADnCjE,EAHA,CAAAE,UAAA,aAAA6G,GAAA,CAAAnC,UAAA,iCAAuD,gBACxC,WAAAmC,GAAA,CAAArD,KAAA,CAAAO,SAAA,4BACsC,cAAA8C,GAAA,CAAArD,KAAA,CAAAO,SAAA,CACxB;UAQPjE,EAAA,CAAAoB,SAAA,EAAgB;UAAhBpB,EAAA,CAAAE,UAAA,SAAA6G,GAAA,CAAAnC,UAAA,CAAgB;;;qBDnC5CpF,YAAY,EAAA+H,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZ/H,WAAW,EAAAgI,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EACXlI,aAAa;MAAAmI,MAAA;MAAAC,IAAA;QAAAC,SAAA,EAIH,CACVpI,OAAO,CAAC,WAAW,EAAE,CACnBE,UAAU,CAAC,QAAQ,EAAE,CACnBD,KAAK,CAAC;UAAEoI,OAAO,EAAE,CAAC;UAAEC,SAAS,EAAE;QAAmB,CAAE,CAAC,EACrDnI,OAAO,CAAC,gBAAgB,EAAEF,KAAK,CAAC;UAAEoI,OAAO,EAAE,CAAC;UAAEC,SAAS,EAAE;QAAe,CAAE,CAAC,CAAC,CAC7E,CAAC,CACH,CAAC;MACH;IAAA;;SAEUhE,yBAAyB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}