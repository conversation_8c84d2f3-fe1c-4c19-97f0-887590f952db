{"ast": null, "code": "import { OffsetRange } from './offsetRange.js';\nimport { TextLength } from './textLength.js';\nexport class PositionOffsetTransformer {\n  constructor(text) {\n    this.text = text;\n    this.lineStartOffsetByLineIdx = [];\n    this.lineStartOffsetByLineIdx.push(0);\n    for (let i = 0; i < text.length; i++) {\n      if (text.charAt(i) === '\\n') {\n        this.lineStartOffsetByLineIdx.push(i + 1);\n      }\n    }\n  }\n  getOffset(position) {\n    return this.lineStartOffsetByLineIdx[position.lineNumber - 1] + position.column - 1;\n  }\n  getOffsetRange(range) {\n    return new OffsetRange(this.getOffset(range.getStartPosition()), this.getOffset(range.getEndPosition()));\n  }\n  get textLength() {\n    const lineIdx = this.lineStartOffsetByLineIdx.length - 1;\n    return new TextLength(lineIdx, this.text.length - this.lineStartOffsetByLineIdx[lineIdx]);\n  }\n}", "map": {"version": 3, "names": ["OffsetRange", "TextLength", "PositionOffsetTransformer", "constructor", "text", "lineStartOffsetByLineIdx", "push", "i", "length", "char<PERSON>t", "getOffset", "position", "lineNumber", "column", "getOffsetRange", "range", "getStartPosition", "getEndPosition", "textLength", "lineIdx"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/core/positionToOffset.js"], "sourcesContent": ["import { OffsetRange } from './offsetRange.js';\nimport { TextLength } from './textLength.js';\nexport class PositionOffsetTransformer {\n    constructor(text) {\n        this.text = text;\n        this.lineStartOffsetByLineIdx = [];\n        this.lineStartOffsetByLineIdx.push(0);\n        for (let i = 0; i < text.length; i++) {\n            if (text.charAt(i) === '\\n') {\n                this.lineStartOffsetByLineIdx.push(i + 1);\n            }\n        }\n    }\n    getOffset(position) {\n        return this.lineStartOffsetByLineIdx[position.lineNumber - 1] + position.column - 1;\n    }\n    getOffsetRange(range) {\n        return new OffsetRange(this.getOffset(range.getStartPosition()), this.getOffset(range.getEndPosition()));\n    }\n    get textLength() {\n        const lineIdx = this.lineStartOffsetByLineIdx.length - 1;\n        return new TextLength(lineIdx, this.text.length - this.lineStartOffsetByLineIdx[lineIdx]);\n    }\n}\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,UAAU,QAAQ,iBAAiB;AAC5C,OAAO,MAAMC,yBAAyB,CAAC;EACnCC,WAAWA,CAACC,IAAI,EAAE;IACd,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,wBAAwB,GAAG,EAAE;IAClC,IAAI,CAACA,wBAAwB,CAACC,IAAI,CAAC,CAAC,CAAC;IACrC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,IAAI,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;MAClC,IAAIH,IAAI,CAACK,MAAM,CAACF,CAAC,CAAC,KAAK,IAAI,EAAE;QACzB,IAAI,CAACF,wBAAwB,CAACC,IAAI,CAACC,CAAC,GAAG,CAAC,CAAC;MAC7C;IACJ;EACJ;EACAG,SAASA,CAACC,QAAQ,EAAE;IAChB,OAAO,IAAI,CAACN,wBAAwB,CAACM,QAAQ,CAACC,UAAU,GAAG,CAAC,CAAC,GAAGD,QAAQ,CAACE,MAAM,GAAG,CAAC;EACvF;EACAC,cAAcA,CAACC,KAAK,EAAE;IAClB,OAAO,IAAIf,WAAW,CAAC,IAAI,CAACU,SAAS,CAACK,KAAK,CAACC,gBAAgB,CAAC,CAAC,CAAC,EAAE,IAAI,CAACN,SAAS,CAACK,KAAK,CAACE,cAAc,CAAC,CAAC,CAAC,CAAC;EAC5G;EACA,IAAIC,UAAUA,CAAA,EAAG;IACb,MAAMC,OAAO,GAAG,IAAI,CAACd,wBAAwB,CAACG,MAAM,GAAG,CAAC;IACxD,OAAO,IAAIP,UAAU,CAACkB,OAAO,EAAE,IAAI,CAACf,IAAI,CAACI,MAAM,GAAG,IAAI,CAACH,wBAAwB,CAACc,OAAO,CAAC,CAAC;EAC7F;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}