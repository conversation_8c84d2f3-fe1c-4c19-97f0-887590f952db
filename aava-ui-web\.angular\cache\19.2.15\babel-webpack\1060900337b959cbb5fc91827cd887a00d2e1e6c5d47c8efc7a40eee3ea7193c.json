{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { BrowserFeatures } from '../../canIUse.js';\nimport * as DOM from '../../dom.js';\nimport { Disposable, DisposableStore, toDisposable } from '../../../common/lifecycle.js';\nimport * as platform from '../../../common/platform.js';\nimport { Range } from '../../../common/range.js';\nimport './contextview.css';\nexport function isAnchor(obj) {\n  const anchor = obj;\n  return !!anchor && typeof anchor.x === 'number' && typeof anchor.y === 'number';\n}\nexport var LayoutAnchorMode = /*#__PURE__*/function (LayoutAnchorMode) {\n  LayoutAnchorMode[LayoutAnchorMode[\"AVOID\"] = 0] = \"AVOID\";\n  LayoutAnchorMode[LayoutAnchorMode[\"ALIGN\"] = 1] = \"ALIGN\";\n  return LayoutAnchorMode;\n}(LayoutAnchorMode || {});\n/**\n * Lays out a one dimensional view next to an anchor in a viewport.\n *\n * @returns The view offset within the viewport.\n */\nexport function layout(viewportSize, viewSize, anchor) {\n  const layoutAfterAnchorBoundary = anchor.mode === LayoutAnchorMode.ALIGN ? anchor.offset : anchor.offset + anchor.size;\n  const layoutBeforeAnchorBoundary = anchor.mode === LayoutAnchorMode.ALIGN ? anchor.offset + anchor.size : anchor.offset;\n  if (anchor.position === 0 /* LayoutAnchorPosition.Before */) {\n    if (viewSize <= viewportSize - layoutAfterAnchorBoundary) {\n      return layoutAfterAnchorBoundary; // happy case, lay it out after the anchor\n    }\n    if (viewSize <= layoutBeforeAnchorBoundary) {\n      return layoutBeforeAnchorBoundary - viewSize; // ok case, lay it out before the anchor\n    }\n    return Math.max(viewportSize - viewSize, 0); // sad case, lay it over the anchor\n  } else {\n    if (viewSize <= layoutBeforeAnchorBoundary) {\n      return layoutBeforeAnchorBoundary - viewSize; // happy case, lay it out before the anchor\n    }\n    if (viewSize <= viewportSize - layoutAfterAnchorBoundary) {\n      return layoutAfterAnchorBoundary; // ok case, lay it out after the anchor\n    }\n    return 0; // sad case, lay it over the anchor\n  }\n}\nexport let ContextView = /*#__PURE__*/(() => {\n  class ContextView extends Disposable {\n    static {\n      this.BUBBLE_UP_EVENTS = ['click', 'keydown', 'focus', 'blur'];\n    }\n    static {\n      this.BUBBLE_DOWN_EVENTS = ['click'];\n    }\n    constructor(container, domPosition) {\n      super();\n      this.container = null;\n      this.useFixedPosition = false;\n      this.useShadowDOM = false;\n      this.delegate = null;\n      this.toDisposeOnClean = Disposable.None;\n      this.toDisposeOnSetContainer = Disposable.None;\n      this.shadowRoot = null;\n      this.shadowRootHostElement = null;\n      this.view = DOM.$('.context-view');\n      DOM.hide(this.view);\n      this.setContainer(container, domPosition);\n      this._register(toDisposable(() => this.setContainer(null, 1 /* ContextViewDOMPosition.ABSOLUTE */)));\n    }\n    setContainer(container, domPosition) {\n      this.useFixedPosition = domPosition !== 1 /* ContextViewDOMPosition.ABSOLUTE */;\n      const usedShadowDOM = this.useShadowDOM;\n      this.useShadowDOM = domPosition === 3 /* ContextViewDOMPosition.FIXED_SHADOW */;\n      if (container === this.container && usedShadowDOM === this.useShadowDOM) {\n        return; // container is the same and no shadow DOM usage has changed\n      }\n      if (this.container) {\n        this.toDisposeOnSetContainer.dispose();\n        this.view.remove();\n        if (this.shadowRoot) {\n          this.shadowRoot = null;\n          this.shadowRootHostElement?.remove();\n          this.shadowRootHostElement = null;\n        }\n        this.container = null;\n      }\n      if (container) {\n        this.container = container;\n        if (this.useShadowDOM) {\n          this.shadowRootHostElement = DOM.$('.shadow-root-host');\n          this.container.appendChild(this.shadowRootHostElement);\n          this.shadowRoot = this.shadowRootHostElement.attachShadow({\n            mode: 'open'\n          });\n          const style = document.createElement('style');\n          style.textContent = SHADOW_ROOT_CSS;\n          this.shadowRoot.appendChild(style);\n          this.shadowRoot.appendChild(this.view);\n          this.shadowRoot.appendChild(DOM.$('slot'));\n        } else {\n          this.container.appendChild(this.view);\n        }\n        const toDisposeOnSetContainer = new DisposableStore();\n        ContextView.BUBBLE_UP_EVENTS.forEach(event => {\n          toDisposeOnSetContainer.add(DOM.addStandardDisposableListener(this.container, event, e => {\n            this.onDOMEvent(e, false);\n          }));\n        });\n        ContextView.BUBBLE_DOWN_EVENTS.forEach(event => {\n          toDisposeOnSetContainer.add(DOM.addStandardDisposableListener(this.container, event, e => {\n            this.onDOMEvent(e, true);\n          }, true));\n        });\n        this.toDisposeOnSetContainer = toDisposeOnSetContainer;\n      }\n    }\n    show(delegate) {\n      if (this.isVisible()) {\n        this.hide();\n      }\n      // Show static box\n      DOM.clearNode(this.view);\n      this.view.className = 'context-view monaco-component';\n      this.view.style.top = '0px';\n      this.view.style.left = '0px';\n      this.view.style.zIndex = `${2575 + (delegate.layer ?? 0)}`;\n      this.view.style.position = this.useFixedPosition ? 'fixed' : 'absolute';\n      DOM.show(this.view);\n      // Render content\n      this.toDisposeOnClean = delegate.render(this.view) || Disposable.None;\n      // Set active delegate\n      this.delegate = delegate;\n      // Layout\n      this.doLayout();\n      // Focus\n      this.delegate.focus?.();\n    }\n    getViewElement() {\n      return this.view;\n    }\n    layout() {\n      if (!this.isVisible()) {\n        return;\n      }\n      if (this.delegate.canRelayout === false && !(platform.isIOS && BrowserFeatures.pointerEvents)) {\n        this.hide();\n        return;\n      }\n      this.delegate?.layout?.();\n      this.doLayout();\n    }\n    doLayout() {\n      // Check that we still have a delegate - this.delegate.layout may have hidden\n      if (!this.isVisible()) {\n        return;\n      }\n      // Get anchor\n      const anchor = this.delegate.getAnchor();\n      // Compute around\n      let around;\n      // Get the element's position and size (to anchor the view)\n      if (DOM.isHTMLElement(anchor)) {\n        const elementPosition = DOM.getDomNodePagePosition(anchor);\n        // In areas where zoom is applied to the element or its ancestors, we need to adjust the size of the element\n        // e.g. The title bar has counter zoom behavior meaning it applies the inverse of zoom level.\n        // Window Zoom Level: 1.5, Title Bar Zoom: 1/1.5, Size Multiplier: 1.5\n        const zoom = DOM.getDomNodeZoomLevel(anchor);\n        around = {\n          top: elementPosition.top * zoom,\n          left: elementPosition.left * zoom,\n          width: elementPosition.width * zoom,\n          height: elementPosition.height * zoom\n        };\n      } else if (isAnchor(anchor)) {\n        around = {\n          top: anchor.y,\n          left: anchor.x,\n          width: anchor.width || 1,\n          height: anchor.height || 2\n        };\n      } else {\n        around = {\n          top: anchor.posy,\n          left: anchor.posx,\n          // We are about to position the context view where the mouse\n          // cursor is. To prevent the view being exactly under the mouse\n          // when showing and thus potentially triggering an action within,\n          // we treat the mouse location like a small sized block element.\n          width: 2,\n          height: 2\n        };\n      }\n      const viewSizeWidth = DOM.getTotalWidth(this.view);\n      const viewSizeHeight = DOM.getTotalHeight(this.view);\n      const anchorPosition = this.delegate.anchorPosition || 0 /* AnchorPosition.BELOW */;\n      const anchorAlignment = this.delegate.anchorAlignment || 0 /* AnchorAlignment.LEFT */;\n      const anchorAxisAlignment = this.delegate.anchorAxisAlignment || 0 /* AnchorAxisAlignment.VERTICAL */;\n      let top;\n      let left;\n      const activeWindow = DOM.getActiveWindow();\n      if (anchorAxisAlignment === 0 /* AnchorAxisAlignment.VERTICAL */) {\n        const verticalAnchor = {\n          offset: around.top - activeWindow.pageYOffset,\n          size: around.height,\n          position: anchorPosition === 0 /* AnchorPosition.BELOW */ ? 0 /* LayoutAnchorPosition.Before */ : 1 /* LayoutAnchorPosition.After */\n        };\n        const horizontalAnchor = {\n          offset: around.left,\n          size: around.width,\n          position: anchorAlignment === 0 /* AnchorAlignment.LEFT */ ? 0 /* LayoutAnchorPosition.Before */ : 1 /* LayoutAnchorPosition.After */,\n          mode: LayoutAnchorMode.ALIGN\n        };\n        top = layout(activeWindow.innerHeight, viewSizeHeight, verticalAnchor) + activeWindow.pageYOffset;\n        // if view intersects vertically with anchor,  we must avoid the anchor\n        if (Range.intersects({\n          start: top,\n          end: top + viewSizeHeight\n        }, {\n          start: verticalAnchor.offset,\n          end: verticalAnchor.offset + verticalAnchor.size\n        })) {\n          horizontalAnchor.mode = LayoutAnchorMode.AVOID;\n        }\n        left = layout(activeWindow.innerWidth, viewSizeWidth, horizontalAnchor);\n      } else {\n        const horizontalAnchor = {\n          offset: around.left,\n          size: around.width,\n          position: anchorAlignment === 0 /* AnchorAlignment.LEFT */ ? 0 /* LayoutAnchorPosition.Before */ : 1 /* LayoutAnchorPosition.After */\n        };\n        const verticalAnchor = {\n          offset: around.top,\n          size: around.height,\n          position: anchorPosition === 0 /* AnchorPosition.BELOW */ ? 0 /* LayoutAnchorPosition.Before */ : 1 /* LayoutAnchorPosition.After */,\n          mode: LayoutAnchorMode.ALIGN\n        };\n        left = layout(activeWindow.innerWidth, viewSizeWidth, horizontalAnchor);\n        // if view intersects horizontally with anchor, we must avoid the anchor\n        if (Range.intersects({\n          start: left,\n          end: left + viewSizeWidth\n        }, {\n          start: horizontalAnchor.offset,\n          end: horizontalAnchor.offset + horizontalAnchor.size\n        })) {\n          verticalAnchor.mode = LayoutAnchorMode.AVOID;\n        }\n        top = layout(activeWindow.innerHeight, viewSizeHeight, verticalAnchor) + activeWindow.pageYOffset;\n      }\n      this.view.classList.remove('top', 'bottom', 'left', 'right');\n      this.view.classList.add(anchorPosition === 0 /* AnchorPosition.BELOW */ ? 'bottom' : 'top');\n      this.view.classList.add(anchorAlignment === 0 /* AnchorAlignment.LEFT */ ? 'left' : 'right');\n      this.view.classList.toggle('fixed', this.useFixedPosition);\n      const containerPosition = DOM.getDomNodePagePosition(this.container);\n      this.view.style.top = `${top - (this.useFixedPosition ? DOM.getDomNodePagePosition(this.view).top : containerPosition.top)}px`;\n      this.view.style.left = `${left - (this.useFixedPosition ? DOM.getDomNodePagePosition(this.view).left : containerPosition.left)}px`;\n      this.view.style.width = 'initial';\n    }\n    hide(data) {\n      const delegate = this.delegate;\n      this.delegate = null;\n      if (delegate?.onHide) {\n        delegate.onHide(data);\n      }\n      this.toDisposeOnClean.dispose();\n      DOM.hide(this.view);\n    }\n    isVisible() {\n      return !!this.delegate;\n    }\n    onDOMEvent(e, onCapture) {\n      if (this.delegate) {\n        if (this.delegate.onDOMEvent) {\n          this.delegate.onDOMEvent(e, DOM.getWindow(e).document.activeElement);\n        } else if (onCapture && !DOM.isAncestor(e.target, this.container)) {\n          this.hide();\n        }\n      }\n    }\n    dispose() {\n      this.hide();\n      super.dispose();\n    }\n  }\n  return ContextView;\n})();\nconst SHADOW_ROOT_CSS = /* css */`\n\t:host {\n\t\tall: initial; /* 1st rule so subsequent properties are reset. */\n\t}\n\n\t.codicon[class*='codicon-'] {\n\t\tfont: normal normal normal 16px/1 codicon;\n\t\tdisplay: inline-block;\n\t\ttext-decoration: none;\n\t\ttext-rendering: auto;\n\t\ttext-align: center;\n\t\t-webkit-font-smoothing: antialiased;\n\t\t-moz-osx-font-smoothing: grayscale;\n\t\tuser-select: none;\n\t\t-webkit-user-select: none;\n\t\t-ms-user-select: none;\n\t}\n\n\t:host {\n\t\tfont-family: -apple-system, BlinkMacSystemFont, \"Segoe WPC\", \"Segoe UI\", \"HelveticaNeue-Light\", system-ui, \"Ubuntu\", \"Droid Sans\", sans-serif;\n\t}\n\n\t:host-context(.mac) { font-family: -apple-system, BlinkMacSystemFont, sans-serif; }\n\t:host-context(.mac:lang(zh-Hans)) { font-family: -apple-system, BlinkMacSystemFont, \"PingFang SC\", \"Hiragino Sans GB\", sans-serif; }\n\t:host-context(.mac:lang(zh-Hant)) { font-family: -apple-system, BlinkMacSystemFont, \"PingFang TC\", sans-serif; }\n\t:host-context(.mac:lang(ja)) { font-family: -apple-system, BlinkMacSystemFont, \"Hiragino Kaku Gothic Pro\", sans-serif; }\n\t:host-context(.mac:lang(ko)) { font-family: -apple-system, BlinkMacSystemFont, \"Nanum Gothic\", \"Apple SD Gothic Neo\", \"AppleGothic\", sans-serif; }\n\n\t:host-context(.windows) { font-family: \"Segoe WPC\", \"Segoe UI\", sans-serif; }\n\t:host-context(.windows:lang(zh-Hans)) { font-family: \"Segoe WPC\", \"Segoe UI\", \"Microsoft YaHei\", sans-serif; }\n\t:host-context(.windows:lang(zh-Hant)) { font-family: \"Segoe WPC\", \"Segoe UI\", \"Microsoft Jhenghei\", sans-serif; }\n\t:host-context(.windows:lang(ja)) { font-family: \"Segoe WPC\", \"Segoe UI\", \"Yu Gothic UI\", \"Meiryo UI\", sans-serif; }\n\t:host-context(.windows:lang(ko)) { font-family: \"Segoe WPC\", \"Segoe UI\", \"Malgun Gothic\", \"Dotom\", sans-serif; }\n\n\t:host-context(.linux) { font-family: system-ui, \"Ubuntu\", \"Droid Sans\", sans-serif; }\n\t:host-context(.linux:lang(zh-Hans)) { font-family: system-ui, \"Ubuntu\", \"Droid Sans\", \"Source Han Sans SC\", \"Source Han Sans CN\", \"Source Han Sans\", sans-serif; }\n\t:host-context(.linux:lang(zh-Hant)) { font-family: system-ui, \"Ubuntu\", \"Droid Sans\", \"Source Han Sans TC\", \"Source Han Sans TW\", \"Source Han Sans\", sans-serif; }\n\t:host-context(.linux:lang(ja)) { font-family: system-ui, \"Ubuntu\", \"Droid Sans\", \"Source Han Sans J\", \"Source Han Sans JP\", \"Source Han Sans\", sans-serif; }\n\t:host-context(.linux:lang(ko)) { font-family: system-ui, \"Ubuntu\", \"Droid Sans\", \"Source Han Sans K\", \"Source Han Sans JR\", \"Source Han Sans\", \"UnDotum\", \"FBaekmuk Gulim\", sans-serif; }\n`;", "map": {"version": 3, "names": ["BrowserFeatures", "DOM", "Disposable", "DisposableStore", "toDisposable", "platform", "Range", "isAnchor", "obj", "anchor", "x", "y", "LayoutAnchorMode", "layout", "viewportSize", "viewSize", "layoutAfterAnchorBoundary", "mode", "ALIGN", "offset", "size", "layoutBeforeAnchorBoundary", "position", "Math", "max", "ContextView", "BUBBLE_UP_EVENTS", "BUBBLE_DOWN_EVENTS", "constructor", "container", "domPosition", "useFixedPosition", "useShadowDOM", "delegate", "toDisposeOnClean", "None", "toDisposeOnSetContainer", "shadowRoot", "shadowRootHostElement", "view", "$", "hide", "<PERSON><PERSON><PERSON><PERSON>", "_register", "usedShadowDOM", "dispose", "remove", "append<PERSON><PERSON><PERSON>", "attachShadow", "style", "document", "createElement", "textContent", "SHADOW_ROOT_CSS", "for<PERSON>ach", "event", "add", "addStandardDisposableListener", "e", "onDOMEvent", "show", "isVisible", "clearNode", "className", "top", "left", "zIndex", "layer", "render", "doLayout", "focus", "getViewElement", "canRelayout", "isIOS", "pointerEvents", "getAnchor", "around", "isHTMLElement", "elementPosition", "getDomNodePagePosition", "zoom", "getDomNodeZoomLevel", "width", "height", "posy", "posx", "viewSizeWidth", "getTotalWidth", "viewSizeHeight", "getTotalHeight", "anchorPosition", "anchorAlignment", "anchorAxisAlignment", "activeWindow", "getActiveWindow", "verticalAnchor", "pageYOffset", "horizontalAnchor", "innerHeight", "intersects", "start", "end", "AVOID", "innerWidth", "classList", "toggle", "containerPosition", "data", "onHide", "onCapture", "getWindow", "activeElement", "isAncestor", "target"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/browser/ui/contextview/contextview.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { BrowserFeatures } from '../../canIUse.js';\nimport * as DOM from '../../dom.js';\nimport { Disposable, DisposableStore, toDisposable } from '../../../common/lifecycle.js';\nimport * as platform from '../../../common/platform.js';\nimport { Range } from '../../../common/range.js';\nimport './contextview.css';\nexport function isAnchor(obj) {\n    const anchor = obj;\n    return !!anchor && typeof anchor.x === 'number' && typeof anchor.y === 'number';\n}\nexport var LayoutAnchorMode;\n(function (LayoutAnchorMode) {\n    LayoutAnchorMode[LayoutAnchorMode[\"AVOID\"] = 0] = \"AVOID\";\n    LayoutAnchorMode[LayoutAnchorMode[\"ALIGN\"] = 1] = \"ALIGN\";\n})(LayoutAnchorMode || (LayoutAnchorMode = {}));\n/**\n * Lays out a one dimensional view next to an anchor in a viewport.\n *\n * @returns The view offset within the viewport.\n */\nexport function layout(viewportSize, viewSize, anchor) {\n    const layoutAfterAnchorBoundary = anchor.mode === LayoutAnchorMode.ALIGN ? anchor.offset : anchor.offset + anchor.size;\n    const layoutBeforeAnchorBoundary = anchor.mode === LayoutAnchorMode.ALIGN ? anchor.offset + anchor.size : anchor.offset;\n    if (anchor.position === 0 /* LayoutAnchorPosition.Before */) {\n        if (viewSize <= viewportSize - layoutAfterAnchorBoundary) {\n            return layoutAfterAnchorBoundary; // happy case, lay it out after the anchor\n        }\n        if (viewSize <= layoutBeforeAnchorBoundary) {\n            return layoutBeforeAnchorBoundary - viewSize; // ok case, lay it out before the anchor\n        }\n        return Math.max(viewportSize - viewSize, 0); // sad case, lay it over the anchor\n    }\n    else {\n        if (viewSize <= layoutBeforeAnchorBoundary) {\n            return layoutBeforeAnchorBoundary - viewSize; // happy case, lay it out before the anchor\n        }\n        if (viewSize <= viewportSize - layoutAfterAnchorBoundary) {\n            return layoutAfterAnchorBoundary; // ok case, lay it out after the anchor\n        }\n        return 0; // sad case, lay it over the anchor\n    }\n}\nexport class ContextView extends Disposable {\n    static { this.BUBBLE_UP_EVENTS = ['click', 'keydown', 'focus', 'blur']; }\n    static { this.BUBBLE_DOWN_EVENTS = ['click']; }\n    constructor(container, domPosition) {\n        super();\n        this.container = null;\n        this.useFixedPosition = false;\n        this.useShadowDOM = false;\n        this.delegate = null;\n        this.toDisposeOnClean = Disposable.None;\n        this.toDisposeOnSetContainer = Disposable.None;\n        this.shadowRoot = null;\n        this.shadowRootHostElement = null;\n        this.view = DOM.$('.context-view');\n        DOM.hide(this.view);\n        this.setContainer(container, domPosition);\n        this._register(toDisposable(() => this.setContainer(null, 1 /* ContextViewDOMPosition.ABSOLUTE */)));\n    }\n    setContainer(container, domPosition) {\n        this.useFixedPosition = domPosition !== 1 /* ContextViewDOMPosition.ABSOLUTE */;\n        const usedShadowDOM = this.useShadowDOM;\n        this.useShadowDOM = domPosition === 3 /* ContextViewDOMPosition.FIXED_SHADOW */;\n        if (container === this.container && usedShadowDOM === this.useShadowDOM) {\n            return; // container is the same and no shadow DOM usage has changed\n        }\n        if (this.container) {\n            this.toDisposeOnSetContainer.dispose();\n            this.view.remove();\n            if (this.shadowRoot) {\n                this.shadowRoot = null;\n                this.shadowRootHostElement?.remove();\n                this.shadowRootHostElement = null;\n            }\n            this.container = null;\n        }\n        if (container) {\n            this.container = container;\n            if (this.useShadowDOM) {\n                this.shadowRootHostElement = DOM.$('.shadow-root-host');\n                this.container.appendChild(this.shadowRootHostElement);\n                this.shadowRoot = this.shadowRootHostElement.attachShadow({ mode: 'open' });\n                const style = document.createElement('style');\n                style.textContent = SHADOW_ROOT_CSS;\n                this.shadowRoot.appendChild(style);\n                this.shadowRoot.appendChild(this.view);\n                this.shadowRoot.appendChild(DOM.$('slot'));\n            }\n            else {\n                this.container.appendChild(this.view);\n            }\n            const toDisposeOnSetContainer = new DisposableStore();\n            ContextView.BUBBLE_UP_EVENTS.forEach(event => {\n                toDisposeOnSetContainer.add(DOM.addStandardDisposableListener(this.container, event, e => {\n                    this.onDOMEvent(e, false);\n                }));\n            });\n            ContextView.BUBBLE_DOWN_EVENTS.forEach(event => {\n                toDisposeOnSetContainer.add(DOM.addStandardDisposableListener(this.container, event, e => {\n                    this.onDOMEvent(e, true);\n                }, true));\n            });\n            this.toDisposeOnSetContainer = toDisposeOnSetContainer;\n        }\n    }\n    show(delegate) {\n        if (this.isVisible()) {\n            this.hide();\n        }\n        // Show static box\n        DOM.clearNode(this.view);\n        this.view.className = 'context-view monaco-component';\n        this.view.style.top = '0px';\n        this.view.style.left = '0px';\n        this.view.style.zIndex = `${2575 + (delegate.layer ?? 0)}`;\n        this.view.style.position = this.useFixedPosition ? 'fixed' : 'absolute';\n        DOM.show(this.view);\n        // Render content\n        this.toDisposeOnClean = delegate.render(this.view) || Disposable.None;\n        // Set active delegate\n        this.delegate = delegate;\n        // Layout\n        this.doLayout();\n        // Focus\n        this.delegate.focus?.();\n    }\n    getViewElement() {\n        return this.view;\n    }\n    layout() {\n        if (!this.isVisible()) {\n            return;\n        }\n        if (this.delegate.canRelayout === false && !(platform.isIOS && BrowserFeatures.pointerEvents)) {\n            this.hide();\n            return;\n        }\n        this.delegate?.layout?.();\n        this.doLayout();\n    }\n    doLayout() {\n        // Check that we still have a delegate - this.delegate.layout may have hidden\n        if (!this.isVisible()) {\n            return;\n        }\n        // Get anchor\n        const anchor = this.delegate.getAnchor();\n        // Compute around\n        let around;\n        // Get the element's position and size (to anchor the view)\n        if (DOM.isHTMLElement(anchor)) {\n            const elementPosition = DOM.getDomNodePagePosition(anchor);\n            // In areas where zoom is applied to the element or its ancestors, we need to adjust the size of the element\n            // e.g. The title bar has counter zoom behavior meaning it applies the inverse of zoom level.\n            // Window Zoom Level: 1.5, Title Bar Zoom: 1/1.5, Size Multiplier: 1.5\n            const zoom = DOM.getDomNodeZoomLevel(anchor);\n            around = {\n                top: elementPosition.top * zoom,\n                left: elementPosition.left * zoom,\n                width: elementPosition.width * zoom,\n                height: elementPosition.height * zoom\n            };\n        }\n        else if (isAnchor(anchor)) {\n            around = {\n                top: anchor.y,\n                left: anchor.x,\n                width: anchor.width || 1,\n                height: anchor.height || 2\n            };\n        }\n        else {\n            around = {\n                top: anchor.posy,\n                left: anchor.posx,\n                // We are about to position the context view where the mouse\n                // cursor is. To prevent the view being exactly under the mouse\n                // when showing and thus potentially triggering an action within,\n                // we treat the mouse location like a small sized block element.\n                width: 2,\n                height: 2\n            };\n        }\n        const viewSizeWidth = DOM.getTotalWidth(this.view);\n        const viewSizeHeight = DOM.getTotalHeight(this.view);\n        const anchorPosition = this.delegate.anchorPosition || 0 /* AnchorPosition.BELOW */;\n        const anchorAlignment = this.delegate.anchorAlignment || 0 /* AnchorAlignment.LEFT */;\n        const anchorAxisAlignment = this.delegate.anchorAxisAlignment || 0 /* AnchorAxisAlignment.VERTICAL */;\n        let top;\n        let left;\n        const activeWindow = DOM.getActiveWindow();\n        if (anchorAxisAlignment === 0 /* AnchorAxisAlignment.VERTICAL */) {\n            const verticalAnchor = { offset: around.top - activeWindow.pageYOffset, size: around.height, position: anchorPosition === 0 /* AnchorPosition.BELOW */ ? 0 /* LayoutAnchorPosition.Before */ : 1 /* LayoutAnchorPosition.After */ };\n            const horizontalAnchor = { offset: around.left, size: around.width, position: anchorAlignment === 0 /* AnchorAlignment.LEFT */ ? 0 /* LayoutAnchorPosition.Before */ : 1 /* LayoutAnchorPosition.After */, mode: LayoutAnchorMode.ALIGN };\n            top = layout(activeWindow.innerHeight, viewSizeHeight, verticalAnchor) + activeWindow.pageYOffset;\n            // if view intersects vertically with anchor,  we must avoid the anchor\n            if (Range.intersects({ start: top, end: top + viewSizeHeight }, { start: verticalAnchor.offset, end: verticalAnchor.offset + verticalAnchor.size })) {\n                horizontalAnchor.mode = LayoutAnchorMode.AVOID;\n            }\n            left = layout(activeWindow.innerWidth, viewSizeWidth, horizontalAnchor);\n        }\n        else {\n            const horizontalAnchor = { offset: around.left, size: around.width, position: anchorAlignment === 0 /* AnchorAlignment.LEFT */ ? 0 /* LayoutAnchorPosition.Before */ : 1 /* LayoutAnchorPosition.After */ };\n            const verticalAnchor = { offset: around.top, size: around.height, position: anchorPosition === 0 /* AnchorPosition.BELOW */ ? 0 /* LayoutAnchorPosition.Before */ : 1 /* LayoutAnchorPosition.After */, mode: LayoutAnchorMode.ALIGN };\n            left = layout(activeWindow.innerWidth, viewSizeWidth, horizontalAnchor);\n            // if view intersects horizontally with anchor, we must avoid the anchor\n            if (Range.intersects({ start: left, end: left + viewSizeWidth }, { start: horizontalAnchor.offset, end: horizontalAnchor.offset + horizontalAnchor.size })) {\n                verticalAnchor.mode = LayoutAnchorMode.AVOID;\n            }\n            top = layout(activeWindow.innerHeight, viewSizeHeight, verticalAnchor) + activeWindow.pageYOffset;\n        }\n        this.view.classList.remove('top', 'bottom', 'left', 'right');\n        this.view.classList.add(anchorPosition === 0 /* AnchorPosition.BELOW */ ? 'bottom' : 'top');\n        this.view.classList.add(anchorAlignment === 0 /* AnchorAlignment.LEFT */ ? 'left' : 'right');\n        this.view.classList.toggle('fixed', this.useFixedPosition);\n        const containerPosition = DOM.getDomNodePagePosition(this.container);\n        this.view.style.top = `${top - (this.useFixedPosition ? DOM.getDomNodePagePosition(this.view).top : containerPosition.top)}px`;\n        this.view.style.left = `${left - (this.useFixedPosition ? DOM.getDomNodePagePosition(this.view).left : containerPosition.left)}px`;\n        this.view.style.width = 'initial';\n    }\n    hide(data) {\n        const delegate = this.delegate;\n        this.delegate = null;\n        if (delegate?.onHide) {\n            delegate.onHide(data);\n        }\n        this.toDisposeOnClean.dispose();\n        DOM.hide(this.view);\n    }\n    isVisible() {\n        return !!this.delegate;\n    }\n    onDOMEvent(e, onCapture) {\n        if (this.delegate) {\n            if (this.delegate.onDOMEvent) {\n                this.delegate.onDOMEvent(e, DOM.getWindow(e).document.activeElement);\n            }\n            else if (onCapture && !DOM.isAncestor(e.target, this.container)) {\n                this.hide();\n            }\n        }\n    }\n    dispose() {\n        this.hide();\n        super.dispose();\n    }\n}\nconst SHADOW_ROOT_CSS = /* css */ `\n\t:host {\n\t\tall: initial; /* 1st rule so subsequent properties are reset. */\n\t}\n\n\t.codicon[class*='codicon-'] {\n\t\tfont: normal normal normal 16px/1 codicon;\n\t\tdisplay: inline-block;\n\t\ttext-decoration: none;\n\t\ttext-rendering: auto;\n\t\ttext-align: center;\n\t\t-webkit-font-smoothing: antialiased;\n\t\t-moz-osx-font-smoothing: grayscale;\n\t\tuser-select: none;\n\t\t-webkit-user-select: none;\n\t\t-ms-user-select: none;\n\t}\n\n\t:host {\n\t\tfont-family: -apple-system, BlinkMacSystemFont, \"Segoe WPC\", \"Segoe UI\", \"HelveticaNeue-Light\", system-ui, \"Ubuntu\", \"Droid Sans\", sans-serif;\n\t}\n\n\t:host-context(.mac) { font-family: -apple-system, BlinkMacSystemFont, sans-serif; }\n\t:host-context(.mac:lang(zh-Hans)) { font-family: -apple-system, BlinkMacSystemFont, \"PingFang SC\", \"Hiragino Sans GB\", sans-serif; }\n\t:host-context(.mac:lang(zh-Hant)) { font-family: -apple-system, BlinkMacSystemFont, \"PingFang TC\", sans-serif; }\n\t:host-context(.mac:lang(ja)) { font-family: -apple-system, BlinkMacSystemFont, \"Hiragino Kaku Gothic Pro\", sans-serif; }\n\t:host-context(.mac:lang(ko)) { font-family: -apple-system, BlinkMacSystemFont, \"Nanum Gothic\", \"Apple SD Gothic Neo\", \"AppleGothic\", sans-serif; }\n\n\t:host-context(.windows) { font-family: \"Segoe WPC\", \"Segoe UI\", sans-serif; }\n\t:host-context(.windows:lang(zh-Hans)) { font-family: \"Segoe WPC\", \"Segoe UI\", \"Microsoft YaHei\", sans-serif; }\n\t:host-context(.windows:lang(zh-Hant)) { font-family: \"Segoe WPC\", \"Segoe UI\", \"Microsoft Jhenghei\", sans-serif; }\n\t:host-context(.windows:lang(ja)) { font-family: \"Segoe WPC\", \"Segoe UI\", \"Yu Gothic UI\", \"Meiryo UI\", sans-serif; }\n\t:host-context(.windows:lang(ko)) { font-family: \"Segoe WPC\", \"Segoe UI\", \"Malgun Gothic\", \"Dotom\", sans-serif; }\n\n\t:host-context(.linux) { font-family: system-ui, \"Ubuntu\", \"Droid Sans\", sans-serif; }\n\t:host-context(.linux:lang(zh-Hans)) { font-family: system-ui, \"Ubuntu\", \"Droid Sans\", \"Source Han Sans SC\", \"Source Han Sans CN\", \"Source Han Sans\", sans-serif; }\n\t:host-context(.linux:lang(zh-Hant)) { font-family: system-ui, \"Ubuntu\", \"Droid Sans\", \"Source Han Sans TC\", \"Source Han Sans TW\", \"Source Han Sans\", sans-serif; }\n\t:host-context(.linux:lang(ja)) { font-family: system-ui, \"Ubuntu\", \"Droid Sans\", \"Source Han Sans J\", \"Source Han Sans JP\", \"Source Han Sans\", sans-serif; }\n\t:host-context(.linux:lang(ko)) { font-family: system-ui, \"Ubuntu\", \"Droid Sans\", \"Source Han Sans K\", \"Source Han Sans JR\", \"Source Han Sans\", \"UnDotum\", \"FBaekmuk Gulim\", sans-serif; }\n`;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,eAAe,QAAQ,kBAAkB;AAClD,OAAO,KAAKC,GAAG,MAAM,cAAc;AACnC,SAASC,UAAU,EAAEC,eAAe,EAAEC,YAAY,QAAQ,8BAA8B;AACxF,OAAO,KAAKC,QAAQ,MAAM,6BAA6B;AACvD,SAASC,KAAK,QAAQ,0BAA0B;AAChD,OAAO,mBAAmB;AAC1B,OAAO,SAASC,QAAQA,CAACC,GAAG,EAAE;EAC1B,MAAMC,MAAM,GAAGD,GAAG;EAClB,OAAO,CAAC,CAACC,MAAM,IAAI,OAAOA,MAAM,CAACC,CAAC,KAAK,QAAQ,IAAI,OAAOD,MAAM,CAACE,CAAC,KAAK,QAAQ;AACnF;AACA,OAAO,IAAIC,gBAAgB,gBAC1B,UAAUA,gBAAgB,EAAE;EACzBA,gBAAgB,CAACA,gBAAgB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;EACzDA,gBAAgB,CAACA,gBAAgB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;EAAC,OAFnDA,gBAAgB;AAG3B,CAAC,CAAEA,gBAAgB,IAAwB,CAAC,CAAE,CAJnB;AAK3B;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,MAAMA,CAACC,YAAY,EAAEC,QAAQ,EAAEN,MAAM,EAAE;EACnD,MAAMO,yBAAyB,GAAGP,MAAM,CAACQ,IAAI,KAAKL,gBAAgB,CAACM,KAAK,GAAGT,MAAM,CAACU,MAAM,GAAGV,MAAM,CAACU,MAAM,GAAGV,MAAM,CAACW,IAAI;EACtH,MAAMC,0BAA0B,GAAGZ,MAAM,CAACQ,IAAI,KAAKL,gBAAgB,CAACM,KAAK,GAAGT,MAAM,CAACU,MAAM,GAAGV,MAAM,CAACW,IAAI,GAAGX,MAAM,CAACU,MAAM;EACvH,IAAIV,MAAM,CAACa,QAAQ,KAAK,CAAC,CAAC,mCAAmC;IACzD,IAAIP,QAAQ,IAAID,YAAY,GAAGE,yBAAyB,EAAE;MACtD,OAAOA,yBAAyB,CAAC,CAAC;IACtC;IACA,IAAID,QAAQ,IAAIM,0BAA0B,EAAE;MACxC,OAAOA,0BAA0B,GAAGN,QAAQ,CAAC,CAAC;IAClD;IACA,OAAOQ,IAAI,CAACC,GAAG,CAACV,YAAY,GAAGC,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC;EACjD,CAAC,MACI;IACD,IAAIA,QAAQ,IAAIM,0BAA0B,EAAE;MACxC,OAAOA,0BAA0B,GAAGN,QAAQ,CAAC,CAAC;IAClD;IACA,IAAIA,QAAQ,IAAID,YAAY,GAAGE,yBAAyB,EAAE;MACtD,OAAOA,yBAAyB,CAAC,CAAC;IACtC;IACA,OAAO,CAAC,CAAC,CAAC;EACd;AACJ;AACA,WAAaS,WAAW;EAAjB,MAAMA,WAAW,SAASvB,UAAU,CAAC;IACxC;MAAS,IAAI,CAACwB,gBAAgB,GAAG,CAAC,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,CAAC;IAAE;IACxE;MAAS,IAAI,CAACC,kBAAkB,GAAG,CAAC,OAAO,CAAC;IAAE;IAC9CC,WAAWA,CAACC,SAAS,EAAEC,WAAW,EAAE;MAChC,KAAK,CAAC,CAAC;MACP,IAAI,CAACD,SAAS,GAAG,IAAI;MACrB,IAAI,CAACE,gBAAgB,GAAG,KAAK;MAC7B,IAAI,CAACC,YAAY,GAAG,KAAK;MACzB,IAAI,CAACC,QAAQ,GAAG,IAAI;MACpB,IAAI,CAACC,gBAAgB,GAAGhC,UAAU,CAACiC,IAAI;MACvC,IAAI,CAACC,uBAAuB,GAAGlC,UAAU,CAACiC,IAAI;MAC9C,IAAI,CAACE,UAAU,GAAG,IAAI;MACtB,IAAI,CAACC,qBAAqB,GAAG,IAAI;MACjC,IAAI,CAACC,IAAI,GAAGtC,GAAG,CAACuC,CAAC,CAAC,eAAe,CAAC;MAClCvC,GAAG,CAACwC,IAAI,CAAC,IAAI,CAACF,IAAI,CAAC;MACnB,IAAI,CAACG,YAAY,CAACb,SAAS,EAAEC,WAAW,CAAC;MACzC,IAAI,CAACa,SAAS,CAACvC,YAAY,CAAC,MAAM,IAAI,CAACsC,YAAY,CAAC,IAAI,EAAE,CAAC,CAAC,qCAAqC,CAAC,CAAC,CAAC;IACxG;IACAA,YAAYA,CAACb,SAAS,EAAEC,WAAW,EAAE;MACjC,IAAI,CAACC,gBAAgB,GAAGD,WAAW,KAAK,CAAC,CAAC;MAC1C,MAAMc,aAAa,GAAG,IAAI,CAACZ,YAAY;MACvC,IAAI,CAACA,YAAY,GAAGF,WAAW,KAAK,CAAC,CAAC;MACtC,IAAID,SAAS,KAAK,IAAI,CAACA,SAAS,IAAIe,aAAa,KAAK,IAAI,CAACZ,YAAY,EAAE;QACrE,OAAO,CAAC;MACZ;MACA,IAAI,IAAI,CAACH,SAAS,EAAE;QAChB,IAAI,CAACO,uBAAuB,CAACS,OAAO,CAAC,CAAC;QACtC,IAAI,CAACN,IAAI,CAACO,MAAM,CAAC,CAAC;QAClB,IAAI,IAAI,CAACT,UAAU,EAAE;UACjB,IAAI,CAACA,UAAU,GAAG,IAAI;UACtB,IAAI,CAACC,qBAAqB,EAAEQ,MAAM,CAAC,CAAC;UACpC,IAAI,CAACR,qBAAqB,GAAG,IAAI;QACrC;QACA,IAAI,CAACT,SAAS,GAAG,IAAI;MACzB;MACA,IAAIA,SAAS,EAAE;QACX,IAAI,CAACA,SAAS,GAAGA,SAAS;QAC1B,IAAI,IAAI,CAACG,YAAY,EAAE;UACnB,IAAI,CAACM,qBAAqB,GAAGrC,GAAG,CAACuC,CAAC,CAAC,mBAAmB,CAAC;UACvD,IAAI,CAACX,SAAS,CAACkB,WAAW,CAAC,IAAI,CAACT,qBAAqB,CAAC;UACtD,IAAI,CAACD,UAAU,GAAG,IAAI,CAACC,qBAAqB,CAACU,YAAY,CAAC;YAAE/B,IAAI,EAAE;UAAO,CAAC,CAAC;UAC3E,MAAMgC,KAAK,GAAGC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;UAC7CF,KAAK,CAACG,WAAW,GAAGC,eAAe;UACnC,IAAI,CAAChB,UAAU,CAACU,WAAW,CAACE,KAAK,CAAC;UAClC,IAAI,CAACZ,UAAU,CAACU,WAAW,CAAC,IAAI,CAACR,IAAI,CAAC;UACtC,IAAI,CAACF,UAAU,CAACU,WAAW,CAAC9C,GAAG,CAACuC,CAAC,CAAC,MAAM,CAAC,CAAC;QAC9C,CAAC,MACI;UACD,IAAI,CAACX,SAAS,CAACkB,WAAW,CAAC,IAAI,CAACR,IAAI,CAAC;QACzC;QACA,MAAMH,uBAAuB,GAAG,IAAIjC,eAAe,CAAC,CAAC;QACrDsB,WAAW,CAACC,gBAAgB,CAAC4B,OAAO,CAACC,KAAK,IAAI;UAC1CnB,uBAAuB,CAACoB,GAAG,CAACvD,GAAG,CAACwD,6BAA6B,CAAC,IAAI,CAAC5B,SAAS,EAAE0B,KAAK,EAAEG,CAAC,IAAI;YACtF,IAAI,CAACC,UAAU,CAACD,CAAC,EAAE,KAAK,CAAC;UAC7B,CAAC,CAAC,CAAC;QACP,CAAC,CAAC;QACFjC,WAAW,CAACE,kBAAkB,CAAC2B,OAAO,CAACC,KAAK,IAAI;UAC5CnB,uBAAuB,CAACoB,GAAG,CAACvD,GAAG,CAACwD,6BAA6B,CAAC,IAAI,CAAC5B,SAAS,EAAE0B,KAAK,EAAEG,CAAC,IAAI;YACtF,IAAI,CAACC,UAAU,CAACD,CAAC,EAAE,IAAI,CAAC;UAC5B,CAAC,EAAE,IAAI,CAAC,CAAC;QACb,CAAC,CAAC;QACF,IAAI,CAACtB,uBAAuB,GAAGA,uBAAuB;MAC1D;IACJ;IACAwB,IAAIA,CAAC3B,QAAQ,EAAE;MACX,IAAI,IAAI,CAAC4B,SAAS,CAAC,CAAC,EAAE;QAClB,IAAI,CAACpB,IAAI,CAAC,CAAC;MACf;MACA;MACAxC,GAAG,CAAC6D,SAAS,CAAC,IAAI,CAACvB,IAAI,CAAC;MACxB,IAAI,CAACA,IAAI,CAACwB,SAAS,GAAG,+BAA+B;MACrD,IAAI,CAACxB,IAAI,CAACU,KAAK,CAACe,GAAG,GAAG,KAAK;MAC3B,IAAI,CAACzB,IAAI,CAACU,KAAK,CAACgB,IAAI,GAAG,KAAK;MAC5B,IAAI,CAAC1B,IAAI,CAACU,KAAK,CAACiB,MAAM,GAAG,GAAG,IAAI,IAAIjC,QAAQ,CAACkC,KAAK,IAAI,CAAC,CAAC,EAAE;MAC1D,IAAI,CAAC5B,IAAI,CAACU,KAAK,CAAC3B,QAAQ,GAAG,IAAI,CAACS,gBAAgB,GAAG,OAAO,GAAG,UAAU;MACvE9B,GAAG,CAAC2D,IAAI,CAAC,IAAI,CAACrB,IAAI,CAAC;MACnB;MACA,IAAI,CAACL,gBAAgB,GAAGD,QAAQ,CAACmC,MAAM,CAAC,IAAI,CAAC7B,IAAI,CAAC,IAAIrC,UAAU,CAACiC,IAAI;MACrE;MACA,IAAI,CAACF,QAAQ,GAAGA,QAAQ;MACxB;MACA,IAAI,CAACoC,QAAQ,CAAC,CAAC;MACf;MACA,IAAI,CAACpC,QAAQ,CAACqC,KAAK,GAAG,CAAC;IAC3B;IACAC,cAAcA,CAAA,EAAG;MACb,OAAO,IAAI,CAAChC,IAAI;IACpB;IACA1B,MAAMA,CAAA,EAAG;MACL,IAAI,CAAC,IAAI,CAACgD,SAAS,CAAC,CAAC,EAAE;QACnB;MACJ;MACA,IAAI,IAAI,CAAC5B,QAAQ,CAACuC,WAAW,KAAK,KAAK,IAAI,EAAEnE,QAAQ,CAACoE,KAAK,IAAIzE,eAAe,CAAC0E,aAAa,CAAC,EAAE;QAC3F,IAAI,CAACjC,IAAI,CAAC,CAAC;QACX;MACJ;MACA,IAAI,CAACR,QAAQ,EAAEpB,MAAM,GAAG,CAAC;MACzB,IAAI,CAACwD,QAAQ,CAAC,CAAC;IACnB;IACAA,QAAQA,CAAA,EAAG;MACP;MACA,IAAI,CAAC,IAAI,CAACR,SAAS,CAAC,CAAC,EAAE;QACnB;MACJ;MACA;MACA,MAAMpD,MAAM,GAAG,IAAI,CAACwB,QAAQ,CAAC0C,SAAS,CAAC,CAAC;MACxC;MACA,IAAIC,MAAM;MACV;MACA,IAAI3E,GAAG,CAAC4E,aAAa,CAACpE,MAAM,CAAC,EAAE;QAC3B,MAAMqE,eAAe,GAAG7E,GAAG,CAAC8E,sBAAsB,CAACtE,MAAM,CAAC;QAC1D;QACA;QACA;QACA,MAAMuE,IAAI,GAAG/E,GAAG,CAACgF,mBAAmB,CAACxE,MAAM,CAAC;QAC5CmE,MAAM,GAAG;UACLZ,GAAG,EAAEc,eAAe,CAACd,GAAG,GAAGgB,IAAI;UAC/Bf,IAAI,EAAEa,eAAe,CAACb,IAAI,GAAGe,IAAI;UACjCE,KAAK,EAAEJ,eAAe,CAACI,KAAK,GAAGF,IAAI;UACnCG,MAAM,EAAEL,eAAe,CAACK,MAAM,GAAGH;QACrC,CAAC;MACL,CAAC,MACI,IAAIzE,QAAQ,CAACE,MAAM,CAAC,EAAE;QACvBmE,MAAM,GAAG;UACLZ,GAAG,EAAEvD,MAAM,CAACE,CAAC;UACbsD,IAAI,EAAExD,MAAM,CAACC,CAAC;UACdwE,KAAK,EAAEzE,MAAM,CAACyE,KAAK,IAAI,CAAC;UACxBC,MAAM,EAAE1E,MAAM,CAAC0E,MAAM,IAAI;QAC7B,CAAC;MACL,CAAC,MACI;QACDP,MAAM,GAAG;UACLZ,GAAG,EAAEvD,MAAM,CAAC2E,IAAI;UAChBnB,IAAI,EAAExD,MAAM,CAAC4E,IAAI;UACjB;UACA;UACA;UACA;UACAH,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE;QACZ,CAAC;MACL;MACA,MAAMG,aAAa,GAAGrF,GAAG,CAACsF,aAAa,CAAC,IAAI,CAAChD,IAAI,CAAC;MAClD,MAAMiD,cAAc,GAAGvF,GAAG,CAACwF,cAAc,CAAC,IAAI,CAAClD,IAAI,CAAC;MACpD,MAAMmD,cAAc,GAAG,IAAI,CAACzD,QAAQ,CAACyD,cAAc,IAAI,CAAC,CAAC;MACzD,MAAMC,eAAe,GAAG,IAAI,CAAC1D,QAAQ,CAAC0D,eAAe,IAAI,CAAC,CAAC;MAC3D,MAAMC,mBAAmB,GAAG,IAAI,CAAC3D,QAAQ,CAAC2D,mBAAmB,IAAI,CAAC,CAAC;MACnE,IAAI5B,GAAG;MACP,IAAIC,IAAI;MACR,MAAM4B,YAAY,GAAG5F,GAAG,CAAC6F,eAAe,CAAC,CAAC;MAC1C,IAAIF,mBAAmB,KAAK,CAAC,CAAC,oCAAoC;QAC9D,MAAMG,cAAc,GAAG;UAAE5E,MAAM,EAAEyD,MAAM,CAACZ,GAAG,GAAG6B,YAAY,CAACG,WAAW;UAAE5E,IAAI,EAAEwD,MAAM,CAACO,MAAM;UAAE7D,QAAQ,EAAEoE,cAAc,KAAK,CAAC,CAAC,6BAA6B,CAAC,CAAC,oCAAoC,CAAC,CAAC;QAAiC,CAAC;QACnO,MAAMO,gBAAgB,GAAG;UAAE9E,MAAM,EAAEyD,MAAM,CAACX,IAAI;UAAE7C,IAAI,EAAEwD,MAAM,CAACM,KAAK;UAAE5D,QAAQ,EAAEqE,eAAe,KAAK,CAAC,CAAC,6BAA6B,CAAC,CAAC,oCAAoC,CAAC,CAAC;UAAkC1E,IAAI,EAAEL,gBAAgB,CAACM;QAAM,CAAC;QACzO8C,GAAG,GAAGnD,MAAM,CAACgF,YAAY,CAACK,WAAW,EAAEV,cAAc,EAAEO,cAAc,CAAC,GAAGF,YAAY,CAACG,WAAW;QACjG;QACA,IAAI1F,KAAK,CAAC6F,UAAU,CAAC;UAAEC,KAAK,EAAEpC,GAAG;UAAEqC,GAAG,EAAErC,GAAG,GAAGwB;QAAe,CAAC,EAAE;UAAEY,KAAK,EAAEL,cAAc,CAAC5E,MAAM;UAAEkF,GAAG,EAAEN,cAAc,CAAC5E,MAAM,GAAG4E,cAAc,CAAC3E;QAAK,CAAC,CAAC,EAAE;UACjJ6E,gBAAgB,CAAChF,IAAI,GAAGL,gBAAgB,CAAC0F,KAAK;QAClD;QACArC,IAAI,GAAGpD,MAAM,CAACgF,YAAY,CAACU,UAAU,EAAEjB,aAAa,EAAEW,gBAAgB,CAAC;MAC3E,CAAC,MACI;QACD,MAAMA,gBAAgB,GAAG;UAAE9E,MAAM,EAAEyD,MAAM,CAACX,IAAI;UAAE7C,IAAI,EAAEwD,MAAM,CAACM,KAAK;UAAE5D,QAAQ,EAAEqE,eAAe,KAAK,CAAC,CAAC,6BAA6B,CAAC,CAAC,oCAAoC,CAAC,CAAC;QAAiC,CAAC;QAC3M,MAAMI,cAAc,GAAG;UAAE5E,MAAM,EAAEyD,MAAM,CAACZ,GAAG;UAAE5C,IAAI,EAAEwD,MAAM,CAACO,MAAM;UAAE7D,QAAQ,EAAEoE,cAAc,KAAK,CAAC,CAAC,6BAA6B,CAAC,CAAC,oCAAoC,CAAC,CAAC;UAAkCzE,IAAI,EAAEL,gBAAgB,CAACM;QAAM,CAAC;QACtO+C,IAAI,GAAGpD,MAAM,CAACgF,YAAY,CAACU,UAAU,EAAEjB,aAAa,EAAEW,gBAAgB,CAAC;QACvE;QACA,IAAI3F,KAAK,CAAC6F,UAAU,CAAC;UAAEC,KAAK,EAAEnC,IAAI;UAAEoC,GAAG,EAAEpC,IAAI,GAAGqB;QAAc,CAAC,EAAE;UAAEc,KAAK,EAAEH,gBAAgB,CAAC9E,MAAM;UAAEkF,GAAG,EAAEJ,gBAAgB,CAAC9E,MAAM,GAAG8E,gBAAgB,CAAC7E;QAAK,CAAC,CAAC,EAAE;UACxJ2E,cAAc,CAAC9E,IAAI,GAAGL,gBAAgB,CAAC0F,KAAK;QAChD;QACAtC,GAAG,GAAGnD,MAAM,CAACgF,YAAY,CAACK,WAAW,EAAEV,cAAc,EAAEO,cAAc,CAAC,GAAGF,YAAY,CAACG,WAAW;MACrG;MACA,IAAI,CAACzD,IAAI,CAACiE,SAAS,CAAC1D,MAAM,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC;MAC5D,IAAI,CAACP,IAAI,CAACiE,SAAS,CAAChD,GAAG,CAACkC,cAAc,KAAK,CAAC,CAAC,6BAA6B,QAAQ,GAAG,KAAK,CAAC;MAC3F,IAAI,CAACnD,IAAI,CAACiE,SAAS,CAAChD,GAAG,CAACmC,eAAe,KAAK,CAAC,CAAC,6BAA6B,MAAM,GAAG,OAAO,CAAC;MAC5F,IAAI,CAACpD,IAAI,CAACiE,SAAS,CAACC,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC1E,gBAAgB,CAAC;MAC1D,MAAM2E,iBAAiB,GAAGzG,GAAG,CAAC8E,sBAAsB,CAAC,IAAI,CAAClD,SAAS,CAAC;MACpE,IAAI,CAACU,IAAI,CAACU,KAAK,CAACe,GAAG,GAAG,GAAGA,GAAG,IAAI,IAAI,CAACjC,gBAAgB,GAAG9B,GAAG,CAAC8E,sBAAsB,CAAC,IAAI,CAACxC,IAAI,CAAC,CAACyB,GAAG,GAAG0C,iBAAiB,CAAC1C,GAAG,CAAC,IAAI;MAC9H,IAAI,CAACzB,IAAI,CAACU,KAAK,CAACgB,IAAI,GAAG,GAAGA,IAAI,IAAI,IAAI,CAAClC,gBAAgB,GAAG9B,GAAG,CAAC8E,sBAAsB,CAAC,IAAI,CAACxC,IAAI,CAAC,CAAC0B,IAAI,GAAGyC,iBAAiB,CAACzC,IAAI,CAAC,IAAI;MAClI,IAAI,CAAC1B,IAAI,CAACU,KAAK,CAACiC,KAAK,GAAG,SAAS;IACrC;IACAzC,IAAIA,CAACkE,IAAI,EAAE;MACP,MAAM1E,QAAQ,GAAG,IAAI,CAACA,QAAQ;MAC9B,IAAI,CAACA,QAAQ,GAAG,IAAI;MACpB,IAAIA,QAAQ,EAAE2E,MAAM,EAAE;QAClB3E,QAAQ,CAAC2E,MAAM,CAACD,IAAI,CAAC;MACzB;MACA,IAAI,CAACzE,gBAAgB,CAACW,OAAO,CAAC,CAAC;MAC/B5C,GAAG,CAACwC,IAAI,CAAC,IAAI,CAACF,IAAI,CAAC;IACvB;IACAsB,SAASA,CAAA,EAAG;MACR,OAAO,CAAC,CAAC,IAAI,CAAC5B,QAAQ;IAC1B;IACA0B,UAAUA,CAACD,CAAC,EAAEmD,SAAS,EAAE;MACrB,IAAI,IAAI,CAAC5E,QAAQ,EAAE;QACf,IAAI,IAAI,CAACA,QAAQ,CAAC0B,UAAU,EAAE;UAC1B,IAAI,CAAC1B,QAAQ,CAAC0B,UAAU,CAACD,CAAC,EAAEzD,GAAG,CAAC6G,SAAS,CAACpD,CAAC,CAAC,CAACR,QAAQ,CAAC6D,aAAa,CAAC;QACxE,CAAC,MACI,IAAIF,SAAS,IAAI,CAAC5G,GAAG,CAAC+G,UAAU,CAACtD,CAAC,CAACuD,MAAM,EAAE,IAAI,CAACpF,SAAS,CAAC,EAAE;UAC7D,IAAI,CAACY,IAAI,CAAC,CAAC;QACf;MACJ;IACJ;IACAI,OAAOA,CAAA,EAAG;MACN,IAAI,CAACJ,IAAI,CAAC,CAAC;MACX,KAAK,CAACI,OAAO,CAAC,CAAC;IACnB;EACJ;EAAC,OA7MYpB,WAAW;AAAA;AA8MxB,MAAM4B,eAAe,GAAG,SAAU;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}