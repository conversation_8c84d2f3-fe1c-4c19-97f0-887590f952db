{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { ArrayQueue } from '../../../../../base/common/arrays.js';\nimport { TextEditInfo } from './beforeEditPositionMapper.js';\nimport { lengthAdd, lengthDiffNonNegative, lengthEquals, lengthIsZero, lengthToObj, lengthZero, sumLengths } from './length.js';\nexport function combineTextEditInfos(textEditInfoFirst, textEditInfoSecond) {\n  if (textEditInfoFirst.length === 0) {\n    return textEditInfoSecond;\n  }\n  if (textEditInfoSecond.length === 0) {\n    return textEditInfoFirst;\n  }\n  // s0: State before any edits\n  const s0ToS1Map = new ArrayQueue(toLengthMapping(textEditInfoFirst));\n  // s1: State after first edit, but before second edit\n  const s1ToS2Map = toLengthMapping(textEditInfoSecond);\n  s1ToS2Map.push({\n    modified: false,\n    lengthBefore: undefined,\n    lengthAfter: undefined\n  }); // Copy everything from old to new\n  // s2: State after both edits\n  let curItem = s0ToS1Map.dequeue();\n  /**\n   * @param s1Length Use undefined for length \"infinity\"\n   */\n  function nextS0ToS1MapWithS1LengthOf(s1Length) {\n    if (s1Length === undefined) {\n      const arr = s0ToS1Map.takeWhile(v => true) || [];\n      if (curItem) {\n        arr.unshift(curItem);\n      }\n      return arr;\n    }\n    const result = [];\n    while (curItem && !lengthIsZero(s1Length)) {\n      const [item, remainingItem] = curItem.splitAt(s1Length);\n      result.push(item);\n      s1Length = lengthDiffNonNegative(item.lengthAfter, s1Length);\n      curItem = remainingItem ?? s0ToS1Map.dequeue();\n    }\n    if (!lengthIsZero(s1Length)) {\n      result.push(new LengthMapping(false, s1Length, s1Length));\n    }\n    return result;\n  }\n  const result = [];\n  function pushEdit(startOffset, endOffset, newLength) {\n    if (result.length > 0 && lengthEquals(result[result.length - 1].endOffset, startOffset)) {\n      const lastResult = result[result.length - 1];\n      result[result.length - 1] = new TextEditInfo(lastResult.startOffset, endOffset, lengthAdd(lastResult.newLength, newLength));\n    } else {\n      result.push({\n        startOffset,\n        endOffset,\n        newLength\n      });\n    }\n  }\n  let s0offset = lengthZero;\n  for (const s1ToS2 of s1ToS2Map) {\n    const s0ToS1Map = nextS0ToS1MapWithS1LengthOf(s1ToS2.lengthBefore);\n    if (s1ToS2.modified) {\n      const s0Length = sumLengths(s0ToS1Map, s => s.lengthBefore);\n      const s0EndOffset = lengthAdd(s0offset, s0Length);\n      pushEdit(s0offset, s0EndOffset, s1ToS2.lengthAfter);\n      s0offset = s0EndOffset;\n    } else {\n      for (const s1 of s0ToS1Map) {\n        const s0startOffset = s0offset;\n        s0offset = lengthAdd(s0offset, s1.lengthBefore);\n        if (s1.modified) {\n          pushEdit(s0startOffset, s0offset, s1.lengthAfter);\n        }\n      }\n    }\n  }\n  return result;\n}\nclass LengthMapping {\n  constructor(\n  /**\n   * If false, length before and length after equal.\n   */\n  modified, lengthBefore, lengthAfter) {\n    this.modified = modified;\n    this.lengthBefore = lengthBefore;\n    this.lengthAfter = lengthAfter;\n  }\n  splitAt(lengthAfter) {\n    const remainingLengthAfter = lengthDiffNonNegative(lengthAfter, this.lengthAfter);\n    if (lengthEquals(remainingLengthAfter, lengthZero)) {\n      return [this, undefined];\n    } else if (this.modified) {\n      return [new LengthMapping(this.modified, this.lengthBefore, lengthAfter), new LengthMapping(this.modified, lengthZero, remainingLengthAfter)];\n    } else {\n      return [new LengthMapping(this.modified, lengthAfter, lengthAfter), new LengthMapping(this.modified, remainingLengthAfter, remainingLengthAfter)];\n    }\n  }\n  toString() {\n    return `${this.modified ? 'M' : 'U'}:${lengthToObj(this.lengthBefore)} -> ${lengthToObj(this.lengthAfter)}`;\n  }\n}\nfunction toLengthMapping(textEditInfos) {\n  const result = [];\n  let lastOffset = lengthZero;\n  for (const textEditInfo of textEditInfos) {\n    const spaceLength = lengthDiffNonNegative(lastOffset, textEditInfo.startOffset);\n    if (!lengthIsZero(spaceLength)) {\n      result.push(new LengthMapping(false, spaceLength, spaceLength));\n    }\n    const lengthBefore = lengthDiffNonNegative(textEditInfo.startOffset, textEditInfo.endOffset);\n    result.push(new LengthMapping(true, lengthBefore, textEditInfo.newLength));\n    lastOffset = textEditInfo.endOffset;\n  }\n  return result;\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TextEditInfo", "lengthAdd", "lengthDiffNonNegative", "lengthEquals", "lengthIsZero", "lengthToObj", "lengthZero", "sumLengths", "combineTextEditInfos", "textEditInfoFirst", "textEditInfoSecond", "length", "s0ToS1Map", "toLengthMapping", "s1ToS2Map", "push", "modified", "lengthBefore", "undefined", "lengthAfter", "curItem", "dequeue", "nextS0ToS1MapWithS1LengthOf", "s1Length", "arr", "<PERSON><PERSON><PERSON><PERSON>", "v", "unshift", "result", "item", "remainingItem", "splitAt", "LengthMapping", "pushEdit", "startOffset", "endOffset", "<PERSON><PERSON><PERSON><PERSON>", "lastResult", "s0offset", "s1ToS2", "s0Length", "s", "s0EndOffset", "s1", "s0startOffset", "constructor", "remainingLengthAfter", "toString", "textEditInfos", "lastOffset", "textEditInfo", "spaceLength"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/combineTextEditInfos.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { ArrayQueue } from '../../../../../base/common/arrays.js';\nimport { TextEditInfo } from './beforeEditPositionMapper.js';\nimport { lengthAdd, lengthDiffNonNegative, lengthEquals, lengthIsZero, lengthToObj, lengthZero, sumLengths } from './length.js';\nexport function combineTextEditInfos(textEditInfoFirst, textEditInfoSecond) {\n    if (textEditInfoFirst.length === 0) {\n        return textEditInfoSecond;\n    }\n    if (textEditInfoSecond.length === 0) {\n        return textEditInfoFirst;\n    }\n    // s0: State before any edits\n    const s0ToS1Map = new ArrayQueue(toLengthMapping(textEditInfoFirst));\n    // s1: State after first edit, but before second edit\n    const s1ToS2Map = toLengthMapping(textEditInfoSecond);\n    s1ToS2Map.push({ modified: false, lengthBefore: undefined, lengthAfter: undefined }); // Copy everything from old to new\n    // s2: State after both edits\n    let curItem = s0ToS1Map.dequeue();\n    /**\n     * @param s1Length Use undefined for length \"infinity\"\n     */\n    function nextS0ToS1MapWithS1LengthOf(s1Length) {\n        if (s1Length === undefined) {\n            const arr = s0ToS1Map.takeWhile(v => true) || [];\n            if (curItem) {\n                arr.unshift(curItem);\n            }\n            return arr;\n        }\n        const result = [];\n        while (curItem && !lengthIsZero(s1Length)) {\n            const [item, remainingItem] = curItem.splitAt(s1Length);\n            result.push(item);\n            s1Length = lengthDiffNonNegative(item.lengthAfter, s1Length);\n            curItem = remainingItem ?? s0ToS1Map.dequeue();\n        }\n        if (!lengthIsZero(s1Length)) {\n            result.push(new LengthMapping(false, s1Length, s1Length));\n        }\n        return result;\n    }\n    const result = [];\n    function pushEdit(startOffset, endOffset, newLength) {\n        if (result.length > 0 && lengthEquals(result[result.length - 1].endOffset, startOffset)) {\n            const lastResult = result[result.length - 1];\n            result[result.length - 1] = new TextEditInfo(lastResult.startOffset, endOffset, lengthAdd(lastResult.newLength, newLength));\n        }\n        else {\n            result.push({ startOffset, endOffset, newLength });\n        }\n    }\n    let s0offset = lengthZero;\n    for (const s1ToS2 of s1ToS2Map) {\n        const s0ToS1Map = nextS0ToS1MapWithS1LengthOf(s1ToS2.lengthBefore);\n        if (s1ToS2.modified) {\n            const s0Length = sumLengths(s0ToS1Map, s => s.lengthBefore);\n            const s0EndOffset = lengthAdd(s0offset, s0Length);\n            pushEdit(s0offset, s0EndOffset, s1ToS2.lengthAfter);\n            s0offset = s0EndOffset;\n        }\n        else {\n            for (const s1 of s0ToS1Map) {\n                const s0startOffset = s0offset;\n                s0offset = lengthAdd(s0offset, s1.lengthBefore);\n                if (s1.modified) {\n                    pushEdit(s0startOffset, s0offset, s1.lengthAfter);\n                }\n            }\n        }\n    }\n    return result;\n}\nclass LengthMapping {\n    constructor(\n    /**\n     * If false, length before and length after equal.\n     */\n    modified, lengthBefore, lengthAfter) {\n        this.modified = modified;\n        this.lengthBefore = lengthBefore;\n        this.lengthAfter = lengthAfter;\n    }\n    splitAt(lengthAfter) {\n        const remainingLengthAfter = lengthDiffNonNegative(lengthAfter, this.lengthAfter);\n        if (lengthEquals(remainingLengthAfter, lengthZero)) {\n            return [this, undefined];\n        }\n        else if (this.modified) {\n            return [\n                new LengthMapping(this.modified, this.lengthBefore, lengthAfter),\n                new LengthMapping(this.modified, lengthZero, remainingLengthAfter)\n            ];\n        }\n        else {\n            return [\n                new LengthMapping(this.modified, lengthAfter, lengthAfter),\n                new LengthMapping(this.modified, remainingLengthAfter, remainingLengthAfter)\n            ];\n        }\n    }\n    toString() {\n        return `${this.modified ? 'M' : 'U'}:${lengthToObj(this.lengthBefore)} -> ${lengthToObj(this.lengthAfter)}`;\n    }\n}\nfunction toLengthMapping(textEditInfos) {\n    const result = [];\n    let lastOffset = lengthZero;\n    for (const textEditInfo of textEditInfos) {\n        const spaceLength = lengthDiffNonNegative(lastOffset, textEditInfo.startOffset);\n        if (!lengthIsZero(spaceLength)) {\n            result.push(new LengthMapping(false, spaceLength, spaceLength));\n        }\n        const lengthBefore = lengthDiffNonNegative(textEditInfo.startOffset, textEditInfo.endOffset);\n        result.push(new LengthMapping(true, lengthBefore, textEditInfo.newLength));\n        lastOffset = textEditInfo.endOffset;\n    }\n    return result;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,UAAU,QAAQ,sCAAsC;AACjE,SAASC,YAAY,QAAQ,+BAA+B;AAC5D,SAASC,SAAS,EAAEC,qBAAqB,EAAEC,YAAY,EAAEC,YAAY,EAAEC,WAAW,EAAEC,UAAU,EAAEC,UAAU,QAAQ,aAAa;AAC/H,OAAO,SAASC,oBAAoBA,CAACC,iBAAiB,EAAEC,kBAAkB,EAAE;EACxE,IAAID,iBAAiB,CAACE,MAAM,KAAK,CAAC,EAAE;IAChC,OAAOD,kBAAkB;EAC7B;EACA,IAAIA,kBAAkB,CAACC,MAAM,KAAK,CAAC,EAAE;IACjC,OAAOF,iBAAiB;EAC5B;EACA;EACA,MAAMG,SAAS,GAAG,IAAIb,UAAU,CAACc,eAAe,CAACJ,iBAAiB,CAAC,CAAC;EACpE;EACA,MAAMK,SAAS,GAAGD,eAAe,CAACH,kBAAkB,CAAC;EACrDI,SAAS,CAACC,IAAI,CAAC;IAAEC,QAAQ,EAAE,KAAK;IAAEC,YAAY,EAAEC,SAAS;IAAEC,WAAW,EAAED;EAAU,CAAC,CAAC,CAAC,CAAC;EACtF;EACA,IAAIE,OAAO,GAAGR,SAAS,CAACS,OAAO,CAAC,CAAC;EACjC;AACJ;AACA;EACI,SAASC,2BAA2BA,CAACC,QAAQ,EAAE;IAC3C,IAAIA,QAAQ,KAAKL,SAAS,EAAE;MACxB,MAAMM,GAAG,GAAGZ,SAAS,CAACa,SAAS,CAACC,CAAC,IAAI,IAAI,CAAC,IAAI,EAAE;MAChD,IAAIN,OAAO,EAAE;QACTI,GAAG,CAACG,OAAO,CAACP,OAAO,CAAC;MACxB;MACA,OAAOI,GAAG;IACd;IACA,MAAMI,MAAM,GAAG,EAAE;IACjB,OAAOR,OAAO,IAAI,CAAChB,YAAY,CAACmB,QAAQ,CAAC,EAAE;MACvC,MAAM,CAACM,IAAI,EAAEC,aAAa,CAAC,GAAGV,OAAO,CAACW,OAAO,CAACR,QAAQ,CAAC;MACvDK,MAAM,CAACb,IAAI,CAACc,IAAI,CAAC;MACjBN,QAAQ,GAAGrB,qBAAqB,CAAC2B,IAAI,CAACV,WAAW,EAAEI,QAAQ,CAAC;MAC5DH,OAAO,GAAGU,aAAa,IAAIlB,SAAS,CAACS,OAAO,CAAC,CAAC;IAClD;IACA,IAAI,CAACjB,YAAY,CAACmB,QAAQ,CAAC,EAAE;MACzBK,MAAM,CAACb,IAAI,CAAC,IAAIiB,aAAa,CAAC,KAAK,EAAET,QAAQ,EAAEA,QAAQ,CAAC,CAAC;IAC7D;IACA,OAAOK,MAAM;EACjB;EACA,MAAMA,MAAM,GAAG,EAAE;EACjB,SAASK,QAAQA,CAACC,WAAW,EAAEC,SAAS,EAAEC,SAAS,EAAE;IACjD,IAAIR,MAAM,CAACjB,MAAM,GAAG,CAAC,IAAIR,YAAY,CAACyB,MAAM,CAACA,MAAM,CAACjB,MAAM,GAAG,CAAC,CAAC,CAACwB,SAAS,EAAED,WAAW,CAAC,EAAE;MACrF,MAAMG,UAAU,GAAGT,MAAM,CAACA,MAAM,CAACjB,MAAM,GAAG,CAAC,CAAC;MAC5CiB,MAAM,CAACA,MAAM,CAACjB,MAAM,GAAG,CAAC,CAAC,GAAG,IAAIX,YAAY,CAACqC,UAAU,CAACH,WAAW,EAAEC,SAAS,EAAElC,SAAS,CAACoC,UAAU,CAACD,SAAS,EAAEA,SAAS,CAAC,CAAC;IAC/H,CAAC,MACI;MACDR,MAAM,CAACb,IAAI,CAAC;QAAEmB,WAAW;QAAEC,SAAS;QAAEC;MAAU,CAAC,CAAC;IACtD;EACJ;EACA,IAAIE,QAAQ,GAAGhC,UAAU;EACzB,KAAK,MAAMiC,MAAM,IAAIzB,SAAS,EAAE;IAC5B,MAAMF,SAAS,GAAGU,2BAA2B,CAACiB,MAAM,CAACtB,YAAY,CAAC;IAClE,IAAIsB,MAAM,CAACvB,QAAQ,EAAE;MACjB,MAAMwB,QAAQ,GAAGjC,UAAU,CAACK,SAAS,EAAE6B,CAAC,IAAIA,CAAC,CAACxB,YAAY,CAAC;MAC3D,MAAMyB,WAAW,GAAGzC,SAAS,CAACqC,QAAQ,EAAEE,QAAQ,CAAC;MACjDP,QAAQ,CAACK,QAAQ,EAAEI,WAAW,EAAEH,MAAM,CAACpB,WAAW,CAAC;MACnDmB,QAAQ,GAAGI,WAAW;IAC1B,CAAC,MACI;MACD,KAAK,MAAMC,EAAE,IAAI/B,SAAS,EAAE;QACxB,MAAMgC,aAAa,GAAGN,QAAQ;QAC9BA,QAAQ,GAAGrC,SAAS,CAACqC,QAAQ,EAAEK,EAAE,CAAC1B,YAAY,CAAC;QAC/C,IAAI0B,EAAE,CAAC3B,QAAQ,EAAE;UACbiB,QAAQ,CAACW,aAAa,EAAEN,QAAQ,EAAEK,EAAE,CAACxB,WAAW,CAAC;QACrD;MACJ;IACJ;EACJ;EACA,OAAOS,MAAM;AACjB;AACA,MAAMI,aAAa,CAAC;EAChBa,WAAWA;EACX;AACJ;AACA;EACI7B,QAAQ,EAAEC,YAAY,EAAEE,WAAW,EAAE;IACjC,IAAI,CAACH,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACE,WAAW,GAAGA,WAAW;EAClC;EACAY,OAAOA,CAACZ,WAAW,EAAE;IACjB,MAAM2B,oBAAoB,GAAG5C,qBAAqB,CAACiB,WAAW,EAAE,IAAI,CAACA,WAAW,CAAC;IACjF,IAAIhB,YAAY,CAAC2C,oBAAoB,EAAExC,UAAU,CAAC,EAAE;MAChD,OAAO,CAAC,IAAI,EAAEY,SAAS,CAAC;IAC5B,CAAC,MACI,IAAI,IAAI,CAACF,QAAQ,EAAE;MACpB,OAAO,CACH,IAAIgB,aAAa,CAAC,IAAI,CAAChB,QAAQ,EAAE,IAAI,CAACC,YAAY,EAAEE,WAAW,CAAC,EAChE,IAAIa,aAAa,CAAC,IAAI,CAAChB,QAAQ,EAAEV,UAAU,EAAEwC,oBAAoB,CAAC,CACrE;IACL,CAAC,MACI;MACD,OAAO,CACH,IAAId,aAAa,CAAC,IAAI,CAAChB,QAAQ,EAAEG,WAAW,EAAEA,WAAW,CAAC,EAC1D,IAAIa,aAAa,CAAC,IAAI,CAAChB,QAAQ,EAAE8B,oBAAoB,EAAEA,oBAAoB,CAAC,CAC/E;IACL;EACJ;EACAC,QAAQA,CAAA,EAAG;IACP,OAAO,GAAG,IAAI,CAAC/B,QAAQ,GAAG,GAAG,GAAG,GAAG,IAAIX,WAAW,CAAC,IAAI,CAACY,YAAY,CAAC,OAAOZ,WAAW,CAAC,IAAI,CAACc,WAAW,CAAC,EAAE;EAC/G;AACJ;AACA,SAASN,eAAeA,CAACmC,aAAa,EAAE;EACpC,MAAMpB,MAAM,GAAG,EAAE;EACjB,IAAIqB,UAAU,GAAG3C,UAAU;EAC3B,KAAK,MAAM4C,YAAY,IAAIF,aAAa,EAAE;IACtC,MAAMG,WAAW,GAAGjD,qBAAqB,CAAC+C,UAAU,EAAEC,YAAY,CAAChB,WAAW,CAAC;IAC/E,IAAI,CAAC9B,YAAY,CAAC+C,WAAW,CAAC,EAAE;MAC5BvB,MAAM,CAACb,IAAI,CAAC,IAAIiB,aAAa,CAAC,KAAK,EAAEmB,WAAW,EAAEA,WAAW,CAAC,CAAC;IACnE;IACA,MAAMlC,YAAY,GAAGf,qBAAqB,CAACgD,YAAY,CAAChB,WAAW,EAAEgB,YAAY,CAACf,SAAS,CAAC;IAC5FP,MAAM,CAACb,IAAI,CAAC,IAAIiB,aAAa,CAAC,IAAI,EAAEf,YAAY,EAAEiC,YAAY,CAACd,SAAS,CAAC,CAAC;IAC1Ea,UAAU,GAAGC,YAAY,CAACf,SAAS;EACvC;EACA,OAAOP,MAAM;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}