{"ast": null, "code": "import { tau } from \"../math.js\";\nimport noop from \"../noop.js\";\nexport default function PathContext(context) {\n  this._context = context;\n}\nPathContext.prototype = {\n  _radius: 4.5,\n  pointRadius: function (_) {\n    return this._radius = _, this;\n  },\n  polygonStart: function () {\n    this._line = 0;\n  },\n  polygonEnd: function () {\n    this._line = NaN;\n  },\n  lineStart: function () {\n    this._point = 0;\n  },\n  lineEnd: function () {\n    if (this._line === 0) this._context.closePath();\n    this._point = NaN;\n  },\n  point: function (x, y) {\n    switch (this._point) {\n      case 0:\n        {\n          this._context.moveTo(x, y);\n          this._point = 1;\n          break;\n        }\n      case 1:\n        {\n          this._context.lineTo(x, y);\n          break;\n        }\n      default:\n        {\n          this._context.moveTo(x + this._radius, y);\n          this._context.arc(x, y, this._radius, 0, tau);\n          break;\n        }\n    }\n  },\n  result: noop\n};", "map": {"version": 3, "names": ["tau", "noop", "PathContext", "context", "_context", "prototype", "_radius", "pointRadius", "_", "polygonStart", "_line", "polygonEnd", "NaN", "lineStart", "_point", "lineEnd", "closePath", "point", "x", "y", "moveTo", "lineTo", "arc", "result"], "sources": ["C:/console/aava-ui-web/node_modules/d3-geo/src/path/context.js"], "sourcesContent": ["import {tau} from \"../math.js\";\nimport noop from \"../noop.js\";\n\nexport default function PathContext(context) {\n  this._context = context;\n}\n\nPathContext.prototype = {\n  _radius: 4.5,\n  pointRadius: function(_) {\n    return this._radius = _, this;\n  },\n  polygonStart: function() {\n    this._line = 0;\n  },\n  polygonEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._point = 0;\n  },\n  lineEnd: function() {\n    if (this._line === 0) this._context.closePath();\n    this._point = NaN;\n  },\n  point: function(x, y) {\n    switch (this._point) {\n      case 0: {\n        this._context.moveTo(x, y);\n        this._point = 1;\n        break;\n      }\n      case 1: {\n        this._context.lineTo(x, y);\n        break;\n      }\n      default: {\n        this._context.moveTo(x + this._radius, y);\n        this._context.arc(x, y, this._radius, 0, tau);\n        break;\n      }\n    }\n  },\n  result: noop\n};\n"], "mappings": "AAAA,SAAQA,GAAG,QAAO,YAAY;AAC9B,OAAOC,IAAI,MAAM,YAAY;AAE7B,eAAe,SAASC,WAAWA,CAACC,OAAO,EAAE;EAC3C,IAAI,CAACC,QAAQ,GAAGD,OAAO;AACzB;AAEAD,WAAW,CAACG,SAAS,GAAG;EACtBC,OAAO,EAAE,GAAG;EACZC,WAAW,EAAE,SAAAA,CAASC,CAAC,EAAE;IACvB,OAAO,IAAI,CAACF,OAAO,GAAGE,CAAC,EAAE,IAAI;EAC/B,CAAC;EACDC,YAAY,EAAE,SAAAA,CAAA,EAAW;IACvB,IAAI,CAACC,KAAK,GAAG,CAAC;EAChB,CAAC;EACDC,UAAU,EAAE,SAAAA,CAAA,EAAW;IACrB,IAAI,CAACD,KAAK,GAAGE,GAAG;EAClB,CAAC;EACDC,SAAS,EAAE,SAAAA,CAAA,EAAW;IACpB,IAAI,CAACC,MAAM,GAAG,CAAC;EACjB,CAAC;EACDC,OAAO,EAAE,SAAAA,CAAA,EAAW;IAClB,IAAI,IAAI,CAACL,KAAK,KAAK,CAAC,EAAE,IAAI,CAACN,QAAQ,CAACY,SAAS,CAAC,CAAC;IAC/C,IAAI,CAACF,MAAM,GAAGF,GAAG;EACnB,CAAC;EACDK,KAAK,EAAE,SAAAA,CAASC,CAAC,EAAEC,CAAC,EAAE;IACpB,QAAQ,IAAI,CAACL,MAAM;MACjB,KAAK,CAAC;QAAE;UACN,IAAI,CAACV,QAAQ,CAACgB,MAAM,CAACF,CAAC,EAAEC,CAAC,CAAC;UAC1B,IAAI,CAACL,MAAM,GAAG,CAAC;UACf;QACF;MACA,KAAK,CAAC;QAAE;UACN,IAAI,CAACV,QAAQ,CAACiB,MAAM,CAACH,CAAC,EAAEC,CAAC,CAAC;UAC1B;QACF;MACA;QAAS;UACP,IAAI,CAACf,QAAQ,CAACgB,MAAM,CAACF,CAAC,GAAG,IAAI,CAACZ,OAAO,EAAEa,CAAC,CAAC;UACzC,IAAI,CAACf,QAAQ,CAACkB,GAAG,CAACJ,CAAC,EAAEC,CAAC,EAAE,IAAI,CAACb,OAAO,EAAE,CAAC,EAAEN,GAAG,CAAC;UAC7C;QACF;IACF;EACF,CAAC;EACDuB,MAAM,EAAEtB;AACV,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}