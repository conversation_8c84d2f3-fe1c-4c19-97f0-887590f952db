{"ast": null, "code": "import { get, set } from \"./schedule.js\";\nfunction tweenRemove(id, name) {\n  var tween0, tween1;\n  return function () {\n    var schedule = set(this, id),\n      tween = schedule.tween;\n\n    // If this node shared tween with the previous node,\n    // just assign the updated shared tween and we’re done!\n    // Otherwise, copy-on-write.\n    if (tween !== tween0) {\n      tween1 = tween0 = tween;\n      for (var i = 0, n = tween1.length; i < n; ++i) {\n        if (tween1[i].name === name) {\n          tween1 = tween1.slice();\n          tween1.splice(i, 1);\n          break;\n        }\n      }\n    }\n    schedule.tween = tween1;\n  };\n}\nfunction tweenFunction(id, name, value) {\n  var tween0, tween1;\n  if (typeof value !== \"function\") throw new Error();\n  return function () {\n    var schedule = set(this, id),\n      tween = schedule.tween;\n\n    // If this node shared tween with the previous node,\n    // just assign the updated shared tween and we’re done!\n    // Otherwise, copy-on-write.\n    if (tween !== tween0) {\n      tween1 = (tween0 = tween).slice();\n      for (var t = {\n          name: name,\n          value: value\n        }, i = 0, n = tween1.length; i < n; ++i) {\n        if (tween1[i].name === name) {\n          tween1[i] = t;\n          break;\n        }\n      }\n      if (i === n) tween1.push(t);\n    }\n    schedule.tween = tween1;\n  };\n}\nexport default function (name, value) {\n  var id = this._id;\n  name += \"\";\n  if (arguments.length < 2) {\n    var tween = get(this.node(), id).tween;\n    for (var i = 0, n = tween.length, t; i < n; ++i) {\n      if ((t = tween[i]).name === name) {\n        return t.value;\n      }\n    }\n    return null;\n  }\n  return this.each((value == null ? tweenRemove : tweenFunction)(id, name, value));\n}\nexport function tweenValue(transition, name, value) {\n  var id = transition._id;\n  transition.each(function () {\n    var schedule = set(this, id);\n    (schedule.value || (schedule.value = {}))[name] = value.apply(this, arguments);\n  });\n  return function (node) {\n    return get(node, id).value[name];\n  };\n}", "map": {"version": 3, "names": ["get", "set", "tweenRemove", "id", "name", "tween0", "tween1", "schedule", "tween", "i", "n", "length", "slice", "splice", "tweenFunction", "value", "Error", "t", "push", "_id", "arguments", "node", "each", "tweenValue", "transition", "apply"], "sources": ["C:/console/aava-ui-web/node_modules/d3-transition/src/transition/tween.js"], "sourcesContent": ["import {get, set} from \"./schedule.js\";\n\nfunction tweenRemove(id, name) {\n  var tween0, tween1;\n  return function() {\n    var schedule = set(this, id),\n        tween = schedule.tween;\n\n    // If this node shared tween with the previous node,\n    // just assign the updated shared tween and we’re done!\n    // Otherwise, copy-on-write.\n    if (tween !== tween0) {\n      tween1 = tween0 = tween;\n      for (var i = 0, n = tween1.length; i < n; ++i) {\n        if (tween1[i].name === name) {\n          tween1 = tween1.slice();\n          tween1.splice(i, 1);\n          break;\n        }\n      }\n    }\n\n    schedule.tween = tween1;\n  };\n}\n\nfunction tweenFunction(id, name, value) {\n  var tween0, tween1;\n  if (typeof value !== \"function\") throw new Error;\n  return function() {\n    var schedule = set(this, id),\n        tween = schedule.tween;\n\n    // If this node shared tween with the previous node,\n    // just assign the updated shared tween and we’re done!\n    // Otherwise, copy-on-write.\n    if (tween !== tween0) {\n      tween1 = (tween0 = tween).slice();\n      for (var t = {name: name, value: value}, i = 0, n = tween1.length; i < n; ++i) {\n        if (tween1[i].name === name) {\n          tween1[i] = t;\n          break;\n        }\n      }\n      if (i === n) tween1.push(t);\n    }\n\n    schedule.tween = tween1;\n  };\n}\n\nexport default function(name, value) {\n  var id = this._id;\n\n  name += \"\";\n\n  if (arguments.length < 2) {\n    var tween = get(this.node(), id).tween;\n    for (var i = 0, n = tween.length, t; i < n; ++i) {\n      if ((t = tween[i]).name === name) {\n        return t.value;\n      }\n    }\n    return null;\n  }\n\n  return this.each((value == null ? tweenRemove : tweenFunction)(id, name, value));\n}\n\nexport function tweenValue(transition, name, value) {\n  var id = transition._id;\n\n  transition.each(function() {\n    var schedule = set(this, id);\n    (schedule.value || (schedule.value = {}))[name] = value.apply(this, arguments);\n  });\n\n  return function(node) {\n    return get(node, id).value[name];\n  };\n}\n"], "mappings": "AAAA,SAAQA,GAAG,EAAEC,GAAG,QAAO,eAAe;AAEtC,SAASC,WAAWA,CAACC,EAAE,EAAEC,IAAI,EAAE;EAC7B,IAAIC,MAAM,EAAEC,MAAM;EAClB,OAAO,YAAW;IAChB,IAAIC,QAAQ,GAAGN,GAAG,CAAC,IAAI,EAAEE,EAAE,CAAC;MACxBK,KAAK,GAAGD,QAAQ,CAACC,KAAK;;IAE1B;IACA;IACA;IACA,IAAIA,KAAK,KAAKH,MAAM,EAAE;MACpBC,MAAM,GAAGD,MAAM,GAAGG,KAAK;MACvB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGJ,MAAM,CAACK,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;QAC7C,IAAIH,MAAM,CAACG,CAAC,CAAC,CAACL,IAAI,KAAKA,IAAI,EAAE;UAC3BE,MAAM,GAAGA,MAAM,CAACM,KAAK,CAAC,CAAC;UACvBN,MAAM,CAACO,MAAM,CAACJ,CAAC,EAAE,CAAC,CAAC;UACnB;QACF;MACF;IACF;IAEAF,QAAQ,CAACC,KAAK,GAAGF,MAAM;EACzB,CAAC;AACH;AAEA,SAASQ,aAAaA,CAACX,EAAE,EAAEC,IAAI,EAAEW,KAAK,EAAE;EACtC,IAAIV,MAAM,EAAEC,MAAM;EAClB,IAAI,OAAOS,KAAK,KAAK,UAAU,EAAE,MAAM,IAAIC,KAAK,CAAD,CAAC;EAChD,OAAO,YAAW;IAChB,IAAIT,QAAQ,GAAGN,GAAG,CAAC,IAAI,EAAEE,EAAE,CAAC;MACxBK,KAAK,GAAGD,QAAQ,CAACC,KAAK;;IAE1B;IACA;IACA;IACA,IAAIA,KAAK,KAAKH,MAAM,EAAE;MACpBC,MAAM,GAAG,CAACD,MAAM,GAAGG,KAAK,EAAEI,KAAK,CAAC,CAAC;MACjC,KAAK,IAAIK,CAAC,GAAG;UAACb,IAAI,EAAEA,IAAI;UAAEW,KAAK,EAAEA;QAAK,CAAC,EAAEN,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGJ,MAAM,CAACK,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;QAC7E,IAAIH,MAAM,CAACG,CAAC,CAAC,CAACL,IAAI,KAAKA,IAAI,EAAE;UAC3BE,MAAM,CAACG,CAAC,CAAC,GAAGQ,CAAC;UACb;QACF;MACF;MACA,IAAIR,CAAC,KAAKC,CAAC,EAAEJ,MAAM,CAACY,IAAI,CAACD,CAAC,CAAC;IAC7B;IAEAV,QAAQ,CAACC,KAAK,GAAGF,MAAM;EACzB,CAAC;AACH;AAEA,eAAe,UAASF,IAAI,EAAEW,KAAK,EAAE;EACnC,IAAIZ,EAAE,GAAG,IAAI,CAACgB,GAAG;EAEjBf,IAAI,IAAI,EAAE;EAEV,IAAIgB,SAAS,CAACT,MAAM,GAAG,CAAC,EAAE;IACxB,IAAIH,KAAK,GAAGR,GAAG,CAAC,IAAI,CAACqB,IAAI,CAAC,CAAC,EAAElB,EAAE,CAAC,CAACK,KAAK;IACtC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGF,KAAK,CAACG,MAAM,EAAEM,CAAC,EAAER,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;MAC/C,IAAI,CAACQ,CAAC,GAAGT,KAAK,CAACC,CAAC,CAAC,EAAEL,IAAI,KAAKA,IAAI,EAAE;QAChC,OAAOa,CAAC,CAACF,KAAK;MAChB;IACF;IACA,OAAO,IAAI;EACb;EAEA,OAAO,IAAI,CAACO,IAAI,CAAC,CAACP,KAAK,IAAI,IAAI,GAAGb,WAAW,GAAGY,aAAa,EAAEX,EAAE,EAAEC,IAAI,EAAEW,KAAK,CAAC,CAAC;AAClF;AAEA,OAAO,SAASQ,UAAUA,CAACC,UAAU,EAAEpB,IAAI,EAAEW,KAAK,EAAE;EAClD,IAAIZ,EAAE,GAAGqB,UAAU,CAACL,GAAG;EAEvBK,UAAU,CAACF,IAAI,CAAC,YAAW;IACzB,IAAIf,QAAQ,GAAGN,GAAG,CAAC,IAAI,EAAEE,EAAE,CAAC;IAC5B,CAACI,QAAQ,CAACQ,KAAK,KAAKR,QAAQ,CAACQ,KAAK,GAAG,CAAC,CAAC,CAAC,EAAEX,IAAI,CAAC,GAAGW,KAAK,CAACU,KAAK,CAAC,IAAI,EAAEL,SAAS,CAAC;EAChF,CAAC,CAAC;EAEF,OAAO,UAASC,IAAI,EAAE;IACpB,OAAOrB,GAAG,CAACqB,IAAI,EAAElB,EAAE,CAAC,CAACY,KAAK,CAACX,IAAI,CAAC;EAClC,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}