{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport '../../../../base/browser/ui/codicons/codiconStyles.js'; // The codicon symbol styles are defined here and must be loaded\nimport { Codicon } from '../../../../base/common/codicons.js';\nimport { CodeActionKind } from '../common/types.js';\nimport '../../symbolIcons/browser/symbolIcons.js'; // The codicon symbol colors are defined here and must be loaded to get colors\nimport { localize } from '../../../../nls.js';\nimport { HierarchicalKind } from '../../../../base/common/hierarchicalKind.js';\nconst uncategorizedCodeActionGroup = Object.freeze({\n  kind: HierarchicalKind.Empty,\n  title: localize('codeAction.widget.id.more', 'More Actions...')\n});\nconst codeActionGroups = Object.freeze([{\n  kind: CodeActionKind.QuickFix,\n  title: localize('codeAction.widget.id.quickfix', 'Quick Fix')\n}, {\n  kind: CodeActionKind.RefactorExtract,\n  title: localize('codeAction.widget.id.extract', 'Extract'),\n  icon: Codicon.wrench\n}, {\n  kind: CodeActionKind.RefactorInline,\n  title: localize('codeAction.widget.id.inline', 'Inline'),\n  icon: Codicon.wrench\n}, {\n  kind: CodeActionKind.RefactorRewrite,\n  title: localize('codeAction.widget.id.convert', 'Rewrite'),\n  icon: Codicon.wrench\n}, {\n  kind: CodeActionKind.RefactorMove,\n  title: localize('codeAction.widget.id.move', 'Move'),\n  icon: Codicon.wrench\n}, {\n  kind: CodeActionKind.SurroundWith,\n  title: localize('codeAction.widget.id.surround', 'Surround With'),\n  icon: Codicon.surroundWith\n}, {\n  kind: CodeActionKind.Source,\n  title: localize('codeAction.widget.id.source', 'Source Action'),\n  icon: Codicon.symbolFile\n}, uncategorizedCodeActionGroup]);\nexport function toMenuItems(inputCodeActions, showHeaders, keybindingResolver) {\n  if (!showHeaders) {\n    return inputCodeActions.map(action => {\n      return {\n        kind: \"action\" /* ActionListItemKind.Action */,\n        item: action,\n        group: uncategorizedCodeActionGroup,\n        disabled: !!action.action.disabled,\n        label: action.action.disabled || action.action.title,\n        canPreview: !!action.action.edit?.edits.length\n      };\n    });\n  }\n  // Group code actions\n  const menuEntries = codeActionGroups.map(group => ({\n    group,\n    actions: []\n  }));\n  for (const action of inputCodeActions) {\n    const kind = action.action.kind ? new HierarchicalKind(action.action.kind) : HierarchicalKind.None;\n    for (const menuEntry of menuEntries) {\n      if (menuEntry.group.kind.contains(kind)) {\n        menuEntry.actions.push(action);\n        break;\n      }\n    }\n  }\n  const allMenuItems = [];\n  for (const menuEntry of menuEntries) {\n    if (menuEntry.actions.length) {\n      allMenuItems.push({\n        kind: \"header\" /* ActionListItemKind.Header */,\n        group: menuEntry.group\n      });\n      for (const action of menuEntry.actions) {\n        const group = menuEntry.group;\n        allMenuItems.push({\n          kind: \"action\" /* ActionListItemKind.Action */,\n          item: action,\n          group: action.action.isAI ? {\n            title: group.title,\n            kind: group.kind,\n            icon: Codicon.sparkle\n          } : group,\n          label: action.action.title,\n          disabled: !!action.action.disabled,\n          keybinding: keybindingResolver(action.action)\n        });\n      }\n    }\n  }\n  return allMenuItems;\n}", "map": {"version": 3, "names": ["Codicon", "CodeActionKind", "localize", "HierarchicalKind", "uncategorizedCodeActionGroup", "Object", "freeze", "kind", "Empty", "title", "codeActionGroups", "QuickFix", "RefactorExtract", "icon", "wrench", "RefactorInline", "RefactorRewrite", "Refactor<PERSON>ove", "SurroundWith", "surroundWith", "Source", "symbolFile", "toMenuItems", "inputCodeActions", "showHeaders", "keybindingResolver", "map", "action", "item", "group", "disabled", "label", "canPreview", "edit", "edits", "length", "menuEntries", "actions", "None", "menuEntry", "contains", "push", "allMenuItems", "isAI", "sparkle", "keybinding"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/contrib/codeAction/browser/codeActionMenu.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport '../../../../base/browser/ui/codicons/codiconStyles.js'; // The codicon symbol styles are defined here and must be loaded\nimport { Codicon } from '../../../../base/common/codicons.js';\nimport { CodeActionKind } from '../common/types.js';\nimport '../../symbolIcons/browser/symbolIcons.js'; // The codicon symbol colors are defined here and must be loaded to get colors\nimport { localize } from '../../../../nls.js';\nimport { HierarchicalKind } from '../../../../base/common/hierarchicalKind.js';\nconst uncategorizedCodeActionGroup = Object.freeze({ kind: HierarchicalKind.Empty, title: localize('codeAction.widget.id.more', 'More Actions...') });\nconst codeActionGroups = Object.freeze([\n    { kind: CodeActionKind.QuickFix, title: localize('codeAction.widget.id.quickfix', 'Quick Fix') },\n    { kind: CodeActionKind.RefactorExtract, title: localize('codeAction.widget.id.extract', 'Extract'), icon: Codicon.wrench },\n    { kind: CodeActionKind.RefactorInline, title: localize('codeAction.widget.id.inline', 'Inline'), icon: Codicon.wrench },\n    { kind: CodeActionKind.RefactorRewrite, title: localize('codeAction.widget.id.convert', 'Rewrite'), icon: Codicon.wrench },\n    { kind: CodeActionKind.RefactorMove, title: localize('codeAction.widget.id.move', 'Move'), icon: Codicon.wrench },\n    { kind: CodeActionKind.SurroundWith, title: localize('codeAction.widget.id.surround', 'Surround With'), icon: Codicon.surroundWith },\n    { kind: CodeActionKind.Source, title: localize('codeAction.widget.id.source', 'Source Action'), icon: Codicon.symbolFile },\n    uncategorizedCodeActionGroup,\n]);\nexport function toMenuItems(inputCodeActions, showHeaders, keybindingResolver) {\n    if (!showHeaders) {\n        return inputCodeActions.map((action) => {\n            return {\n                kind: \"action\" /* ActionListItemKind.Action */,\n                item: action,\n                group: uncategorizedCodeActionGroup,\n                disabled: !!action.action.disabled,\n                label: action.action.disabled || action.action.title,\n                canPreview: !!action.action.edit?.edits.length,\n            };\n        });\n    }\n    // Group code actions\n    const menuEntries = codeActionGroups.map(group => ({ group, actions: [] }));\n    for (const action of inputCodeActions) {\n        const kind = action.action.kind ? new HierarchicalKind(action.action.kind) : HierarchicalKind.None;\n        for (const menuEntry of menuEntries) {\n            if (menuEntry.group.kind.contains(kind)) {\n                menuEntry.actions.push(action);\n                break;\n            }\n        }\n    }\n    const allMenuItems = [];\n    for (const menuEntry of menuEntries) {\n        if (menuEntry.actions.length) {\n            allMenuItems.push({ kind: \"header\" /* ActionListItemKind.Header */, group: menuEntry.group });\n            for (const action of menuEntry.actions) {\n                const group = menuEntry.group;\n                allMenuItems.push({\n                    kind: \"action\" /* ActionListItemKind.Action */,\n                    item: action,\n                    group: action.action.isAI ? { title: group.title, kind: group.kind, icon: Codicon.sparkle } : group,\n                    label: action.action.title,\n                    disabled: !!action.action.disabled,\n                    keybinding: keybindingResolver(action.action),\n                });\n            }\n        }\n    }\n    return allMenuItems;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,uDAAuD,CAAC,CAAC;AAChE,SAASA,OAAO,QAAQ,qCAAqC;AAC7D,SAASC,cAAc,QAAQ,oBAAoB;AACnD,OAAO,0CAA0C,CAAC,CAAC;AACnD,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,SAASC,gBAAgB,QAAQ,6CAA6C;AAC9E,MAAMC,4BAA4B,GAAGC,MAAM,CAACC,MAAM,CAAC;EAAEC,IAAI,EAAEJ,gBAAgB,CAACK,KAAK;EAAEC,KAAK,EAAEP,QAAQ,CAAC,2BAA2B,EAAE,iBAAiB;AAAE,CAAC,CAAC;AACrJ,MAAMQ,gBAAgB,GAAGL,MAAM,CAACC,MAAM,CAAC,CACnC;EAAEC,IAAI,EAAEN,cAAc,CAACU,QAAQ;EAAEF,KAAK,EAAEP,QAAQ,CAAC,+BAA+B,EAAE,WAAW;AAAE,CAAC,EAChG;EAAEK,IAAI,EAAEN,cAAc,CAACW,eAAe;EAAEH,KAAK,EAAEP,QAAQ,CAAC,8BAA8B,EAAE,SAAS,CAAC;EAAEW,IAAI,EAAEb,OAAO,CAACc;AAAO,CAAC,EAC1H;EAAEP,IAAI,EAAEN,cAAc,CAACc,cAAc;EAAEN,KAAK,EAAEP,QAAQ,CAAC,6BAA6B,EAAE,QAAQ,CAAC;EAAEW,IAAI,EAAEb,OAAO,CAACc;AAAO,CAAC,EACvH;EAAEP,IAAI,EAAEN,cAAc,CAACe,eAAe;EAAEP,KAAK,EAAEP,QAAQ,CAAC,8BAA8B,EAAE,SAAS,CAAC;EAAEW,IAAI,EAAEb,OAAO,CAACc;AAAO,CAAC,EAC1H;EAAEP,IAAI,EAAEN,cAAc,CAACgB,YAAY;EAAER,KAAK,EAAEP,QAAQ,CAAC,2BAA2B,EAAE,MAAM,CAAC;EAAEW,IAAI,EAAEb,OAAO,CAACc;AAAO,CAAC,EACjH;EAAEP,IAAI,EAAEN,cAAc,CAACiB,YAAY;EAAET,KAAK,EAAEP,QAAQ,CAAC,+BAA+B,EAAE,eAAe,CAAC;EAAEW,IAAI,EAAEb,OAAO,CAACmB;AAAa,CAAC,EACpI;EAAEZ,IAAI,EAAEN,cAAc,CAACmB,MAAM;EAAEX,KAAK,EAAEP,QAAQ,CAAC,6BAA6B,EAAE,eAAe,CAAC;EAAEW,IAAI,EAAEb,OAAO,CAACqB;AAAW,CAAC,EAC1HjB,4BAA4B,CAC/B,CAAC;AACF,OAAO,SAASkB,WAAWA,CAACC,gBAAgB,EAAEC,WAAW,EAAEC,kBAAkB,EAAE;EAC3E,IAAI,CAACD,WAAW,EAAE;IACd,OAAOD,gBAAgB,CAACG,GAAG,CAAEC,MAAM,IAAK;MACpC,OAAO;QACHpB,IAAI,EAAE,QAAQ,CAAC;QACfqB,IAAI,EAAED,MAAM;QACZE,KAAK,EAAEzB,4BAA4B;QACnC0B,QAAQ,EAAE,CAAC,CAACH,MAAM,CAACA,MAAM,CAACG,QAAQ;QAClCC,KAAK,EAAEJ,MAAM,CAACA,MAAM,CAACG,QAAQ,IAAIH,MAAM,CAACA,MAAM,CAAClB,KAAK;QACpDuB,UAAU,EAAE,CAAC,CAACL,MAAM,CAACA,MAAM,CAACM,IAAI,EAAEC,KAAK,CAACC;MAC5C,CAAC;IACL,CAAC,CAAC;EACN;EACA;EACA,MAAMC,WAAW,GAAG1B,gBAAgB,CAACgB,GAAG,CAACG,KAAK,KAAK;IAAEA,KAAK;IAAEQ,OAAO,EAAE;EAAG,CAAC,CAAC,CAAC;EAC3E,KAAK,MAAMV,MAAM,IAAIJ,gBAAgB,EAAE;IACnC,MAAMhB,IAAI,GAAGoB,MAAM,CAACA,MAAM,CAACpB,IAAI,GAAG,IAAIJ,gBAAgB,CAACwB,MAAM,CAACA,MAAM,CAACpB,IAAI,CAAC,GAAGJ,gBAAgB,CAACmC,IAAI;IAClG,KAAK,MAAMC,SAAS,IAAIH,WAAW,EAAE;MACjC,IAAIG,SAAS,CAACV,KAAK,CAACtB,IAAI,CAACiC,QAAQ,CAACjC,IAAI,CAAC,EAAE;QACrCgC,SAAS,CAACF,OAAO,CAACI,IAAI,CAACd,MAAM,CAAC;QAC9B;MACJ;IACJ;EACJ;EACA,MAAMe,YAAY,GAAG,EAAE;EACvB,KAAK,MAAMH,SAAS,IAAIH,WAAW,EAAE;IACjC,IAAIG,SAAS,CAACF,OAAO,CAACF,MAAM,EAAE;MAC1BO,YAAY,CAACD,IAAI,CAAC;QAAElC,IAAI,EAAE,QAAQ,CAAC;QAAiCsB,KAAK,EAAEU,SAAS,CAACV;MAAM,CAAC,CAAC;MAC7F,KAAK,MAAMF,MAAM,IAAIY,SAAS,CAACF,OAAO,EAAE;QACpC,MAAMR,KAAK,GAAGU,SAAS,CAACV,KAAK;QAC7Ba,YAAY,CAACD,IAAI,CAAC;UACdlC,IAAI,EAAE,QAAQ,CAAC;UACfqB,IAAI,EAAED,MAAM;UACZE,KAAK,EAAEF,MAAM,CAACA,MAAM,CAACgB,IAAI,GAAG;YAAElC,KAAK,EAAEoB,KAAK,CAACpB,KAAK;YAAEF,IAAI,EAAEsB,KAAK,CAACtB,IAAI;YAAEM,IAAI,EAAEb,OAAO,CAAC4C;UAAQ,CAAC,GAAGf,KAAK;UACnGE,KAAK,EAAEJ,MAAM,CAACA,MAAM,CAAClB,KAAK;UAC1BqB,QAAQ,EAAE,CAAC,CAACH,MAAM,CAACA,MAAM,CAACG,QAAQ;UAClCe,UAAU,EAAEpB,kBAAkB,CAACE,MAAM,CAACA,MAAM;QAChD,CAAC,CAAC;MACN;IACJ;EACJ;EACA,OAAOe,YAAY;AACvB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}