{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport function toUint8(v) {\n  if (v < 0) {\n    return 0;\n  }\n  if (v > 255 /* Constants.MAX_UINT_8 */) {\n    return 255 /* Constants.MAX_UINT_8 */;\n  }\n  return v | 0;\n}\nexport function toUint32(v) {\n  if (v < 0) {\n    return 0;\n  }\n  if (v > ********** /* Constants.MAX_UINT_32 */) {\n    return ********** /* Constants.MAX_UINT_32 */;\n  }\n  return v | 0;\n}", "map": {"version": 3, "names": ["toUint8", "v", "toUint32"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/common/uint.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport function toUint8(v) {\n    if (v < 0) {\n        return 0;\n    }\n    if (v > 255 /* Constants.MAX_UINT_8 */) {\n        return 255 /* Constants.MAX_UINT_8 */;\n    }\n    return v | 0;\n}\nexport function toUint32(v) {\n    if (v < 0) {\n        return 0;\n    }\n    if (v > ********** /* Constants.MAX_UINT_32 */) {\n        return ********** /* Constants.MAX_UINT_32 */;\n    }\n    return v | 0;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,SAASA,OAAOA,CAACC,CAAC,EAAE;EACvB,IAAIA,CAAC,GAAG,CAAC,EAAE;IACP,OAAO,CAAC;EACZ;EACA,IAAIA,CAAC,GAAG,GAAG,CAAC,4BAA4B;IACpC,OAAO,GAAG,CAAC;EACf;EACA,OAAOA,CAAC,GAAG,CAAC;AAChB;AACA,OAAO,SAASC,QAAQA,CAACD,CAAC,EAAE;EACxB,IAAIA,CAAC,GAAG,CAAC,EAAE;IACP,OAAO,CAAC;EACZ;EACA,IAAIA,CAAC,GAAG,UAAU,CAAC,6BAA6B;IAC5C,OAAO,UAAU,CAAC;EACtB;EACA,OAAOA,CAAC,GAAG,CAAC;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}