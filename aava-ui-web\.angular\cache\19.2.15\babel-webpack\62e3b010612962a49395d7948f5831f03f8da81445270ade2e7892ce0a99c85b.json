{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Emitter } from './event.js';\nexport class IMEImpl {\n  constructor() {\n    this._onDidChange = new Emitter();\n    this.onDidChange = this._onDidChange.event;\n    this._enabled = true;\n  }\n  get enabled() {\n    return this._enabled;\n  }\n  /**\n   * Enable IME\n   */\n  enable() {\n    this._enabled = true;\n    this._onDidChange.fire();\n  }\n  /**\n   * Disable IME\n   */\n  disable() {\n    this._enabled = false;\n    this._onDidChange.fire();\n  }\n}\nexport const IME = new IMEImpl();", "map": {"version": 3, "names": ["Emitter", "IMEImpl", "constructor", "_onDidChange", "onDidChange", "event", "_enabled", "enabled", "enable", "fire", "disable", "IME"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/common/ime.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Emitter } from './event.js';\nexport class IMEImpl {\n    constructor() {\n        this._onDidChange = new Emitter();\n        this.onDidChange = this._onDidChange.event;\n        this._enabled = true;\n    }\n    get enabled() {\n        return this._enabled;\n    }\n    /**\n     * Enable IME\n     */\n    enable() {\n        this._enabled = true;\n        this._onDidChange.fire();\n    }\n    /**\n     * Disable IME\n     */\n    disable() {\n        this._enabled = false;\n        this._onDidChange.fire();\n    }\n}\nexport const IME = new IMEImpl();\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,OAAO,QAAQ,YAAY;AACpC,OAAO,MAAMC,OAAO,CAAC;EACjBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,YAAY,GAAG,IAAIH,OAAO,CAAC,CAAC;IACjC,IAAI,CAACI,WAAW,GAAG,IAAI,CAACD,YAAY,CAACE,KAAK;IAC1C,IAAI,CAACC,QAAQ,GAAG,IAAI;EACxB;EACA,IAAIC,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACD,QAAQ;EACxB;EACA;AACJ;AACA;EACIE,MAAMA,CAAA,EAAG;IACL,IAAI,CAACF,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACH,YAAY,CAACM,IAAI,CAAC,CAAC;EAC5B;EACA;AACJ;AACA;EACIC,OAAOA,CAAA,EAAG;IACN,IAAI,CAACJ,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACH,YAAY,CAACM,IAAI,CAAC,CAAC;EAC5B;AACJ;AACA,OAAO,MAAME,GAAG,GAAG,IAAIV,OAAO,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}