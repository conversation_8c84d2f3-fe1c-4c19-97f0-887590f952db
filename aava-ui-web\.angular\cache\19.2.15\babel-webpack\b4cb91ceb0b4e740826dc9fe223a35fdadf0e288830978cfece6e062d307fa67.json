{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Sash } from '../../../../../base/browser/ui/sash/sash.js';\nimport { Disposable } from '../../../../../base/common/lifecycle.js';\nimport { autorun, observableValue } from '../../../../../base/common/observable.js';\nimport { derivedWithSetter } from '../../../../../base/common/observableInternal/derived.js';\nexport class SashLayout {\n  resetSash() {\n    this._sashRatio.set(undefined, undefined);\n  }\n  constructor(_options, dimensions) {\n    this._options = _options;\n    this.dimensions = dimensions;\n    this.sashLeft = derivedWithSetter(this, reader => {\n      const ratio = this._sashRatio.read(reader) ?? this._options.splitViewDefaultRatio.read(reader);\n      return this._computeSashLeft(ratio, reader);\n    }, (value, tx) => {\n      const contentWidth = this.dimensions.width.get();\n      this._sashRatio.set(value / contentWidth, tx);\n    });\n    this._sashRatio = observableValue(this, undefined);\n  }\n  /** @pure */\n  _computeSashLeft(desiredRatio, reader) {\n    const contentWidth = this.dimensions.width.read(reader);\n    const midPoint = Math.floor(this._options.splitViewDefaultRatio.read(reader) * contentWidth);\n    const sashLeft = this._options.enableSplitViewResizing.read(reader) ? Math.floor(desiredRatio * contentWidth) : midPoint;\n    const MINIMUM_EDITOR_WIDTH = 100;\n    if (contentWidth <= MINIMUM_EDITOR_WIDTH * 2) {\n      return midPoint;\n    }\n    if (sashLeft < MINIMUM_EDITOR_WIDTH) {\n      return MINIMUM_EDITOR_WIDTH;\n    }\n    if (sashLeft > contentWidth - MINIMUM_EDITOR_WIDTH) {\n      return contentWidth - MINIMUM_EDITOR_WIDTH;\n    }\n    return sashLeft;\n  }\n}\nexport class DiffEditorSash extends Disposable {\n  constructor(_domNode, _dimensions, _enabled, _boundarySashes, sashLeft, _resetSash) {\n    super();\n    this._domNode = _domNode;\n    this._dimensions = _dimensions;\n    this._enabled = _enabled;\n    this._boundarySashes = _boundarySashes;\n    this.sashLeft = sashLeft;\n    this._resetSash = _resetSash;\n    this._sash = this._register(new Sash(this._domNode, {\n      getVerticalSashTop: _sash => 0,\n      getVerticalSashLeft: _sash => this.sashLeft.get(),\n      getVerticalSashHeight: _sash => this._dimensions.height.get()\n    }, {\n      orientation: 0 /* Orientation.VERTICAL */\n    }));\n    this._startSashPosition = undefined;\n    this._register(this._sash.onDidStart(() => {\n      this._startSashPosition = this.sashLeft.get();\n    }));\n    this._register(this._sash.onDidChange(e => {\n      this.sashLeft.set(this._startSashPosition + (e.currentX - e.startX), undefined);\n    }));\n    this._register(this._sash.onDidEnd(() => this._sash.layout()));\n    this._register(this._sash.onDidReset(() => this._resetSash()));\n    this._register(autorun(reader => {\n      const sashes = this._boundarySashes.read(reader);\n      if (sashes) {\n        this._sash.orthogonalEndSash = sashes.bottom;\n      }\n    }));\n    this._register(autorun(reader => {\n      /** @description DiffEditorSash.layoutSash */\n      const enabled = this._enabled.read(reader);\n      this._sash.state = enabled ? 3 /* SashState.Enabled */ : 0 /* SashState.Disabled */;\n      this.sashLeft.read(reader);\n      this._dimensions.height.read(reader);\n      this._sash.layout();\n    }));\n  }\n}", "map": {"version": 3, "names": ["<PERSON>sh", "Disposable", "autorun", "observableValue", "derivedWithSetter", "SashLayout", "resetSash", "_sashRatio", "set", "undefined", "constructor", "_options", "dimensions", "sashLeft", "reader", "ratio", "read", "splitViewDefaultRatio", "_computeSashLeft", "value", "tx", "contentWidth", "width", "get", "desiredRatio", "midPoint", "Math", "floor", "enableSplitViewResizing", "MINIMUM_EDITOR_WIDTH", "DiffEditorSash", "_domNode", "_dimensions", "_enabled", "_boundarySashes", "_resetSash", "_sash", "_register", "getVerticalSashTop", "getVerticalSashLeft", "getVerticalSashHeight", "height", "orientation", "_startSashPosition", "onDidStart", "onDidChange", "e", "currentX", "startX", "onDidEnd", "layout", "onDidReset", "sashes", "orthogonalEndSash", "bottom", "enabled", "state"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/browser/widget/diffEditor/components/diffEditorSash.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Sash } from '../../../../../base/browser/ui/sash/sash.js';\nimport { Disposable } from '../../../../../base/common/lifecycle.js';\nimport { autorun, observableValue } from '../../../../../base/common/observable.js';\nimport { derivedWithSetter } from '../../../../../base/common/observableInternal/derived.js';\nexport class SashLayout {\n    resetSash() {\n        this._sashRatio.set(undefined, undefined);\n    }\n    constructor(_options, dimensions) {\n        this._options = _options;\n        this.dimensions = dimensions;\n        this.sashLeft = derivedWithSetter(this, reader => {\n            const ratio = this._sashRatio.read(reader) ?? this._options.splitViewDefaultRatio.read(reader);\n            return this._computeSashLeft(ratio, reader);\n        }, (value, tx) => {\n            const contentWidth = this.dimensions.width.get();\n            this._sashRatio.set(value / contentWidth, tx);\n        });\n        this._sashRatio = observableValue(this, undefined);\n    }\n    /** @pure */\n    _computeSashLeft(desiredRatio, reader) {\n        const contentWidth = this.dimensions.width.read(reader);\n        const midPoint = Math.floor(this._options.splitViewDefaultRatio.read(reader) * contentWidth);\n        const sashLeft = this._options.enableSplitViewResizing.read(reader) ? Math.floor(desiredRatio * contentWidth) : midPoint;\n        const MINIMUM_EDITOR_WIDTH = 100;\n        if (contentWidth <= MINIMUM_EDITOR_WIDTH * 2) {\n            return midPoint;\n        }\n        if (sashLeft < MINIMUM_EDITOR_WIDTH) {\n            return MINIMUM_EDITOR_WIDTH;\n        }\n        if (sashLeft > contentWidth - MINIMUM_EDITOR_WIDTH) {\n            return contentWidth - MINIMUM_EDITOR_WIDTH;\n        }\n        return sashLeft;\n    }\n}\nexport class DiffEditorSash extends Disposable {\n    constructor(_domNode, _dimensions, _enabled, _boundarySashes, sashLeft, _resetSash) {\n        super();\n        this._domNode = _domNode;\n        this._dimensions = _dimensions;\n        this._enabled = _enabled;\n        this._boundarySashes = _boundarySashes;\n        this.sashLeft = sashLeft;\n        this._resetSash = _resetSash;\n        this._sash = this._register(new Sash(this._domNode, {\n            getVerticalSashTop: (_sash) => 0,\n            getVerticalSashLeft: (_sash) => this.sashLeft.get(),\n            getVerticalSashHeight: (_sash) => this._dimensions.height.get(),\n        }, { orientation: 0 /* Orientation.VERTICAL */ }));\n        this._startSashPosition = undefined;\n        this._register(this._sash.onDidStart(() => {\n            this._startSashPosition = this.sashLeft.get();\n        }));\n        this._register(this._sash.onDidChange((e) => {\n            this.sashLeft.set(this._startSashPosition + (e.currentX - e.startX), undefined);\n        }));\n        this._register(this._sash.onDidEnd(() => this._sash.layout()));\n        this._register(this._sash.onDidReset(() => this._resetSash()));\n        this._register(autorun(reader => {\n            const sashes = this._boundarySashes.read(reader);\n            if (sashes) {\n                this._sash.orthogonalEndSash = sashes.bottom;\n            }\n        }));\n        this._register(autorun(reader => {\n            /** @description DiffEditorSash.layoutSash */\n            const enabled = this._enabled.read(reader);\n            this._sash.state = enabled ? 3 /* SashState.Enabled */ : 0 /* SashState.Disabled */;\n            this.sashLeft.read(reader);\n            this._dimensions.height.read(reader);\n            this._sash.layout();\n        }));\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,IAAI,QAAQ,6CAA6C;AAClE,SAASC,UAAU,QAAQ,yCAAyC;AACpE,SAASC,OAAO,EAAEC,eAAe,QAAQ,0CAA0C;AACnF,SAASC,iBAAiB,QAAQ,0DAA0D;AAC5F,OAAO,MAAMC,UAAU,CAAC;EACpBC,SAASA,CAAA,EAAG;IACR,IAAI,CAACC,UAAU,CAACC,GAAG,CAACC,SAAS,EAAEA,SAAS,CAAC;EAC7C;EACAC,WAAWA,CAACC,QAAQ,EAAEC,UAAU,EAAE;IAC9B,IAAI,CAACD,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,QAAQ,GAAGT,iBAAiB,CAAC,IAAI,EAAEU,MAAM,IAAI;MAC9C,MAAMC,KAAK,GAAG,IAAI,CAACR,UAAU,CAACS,IAAI,CAACF,MAAM,CAAC,IAAI,IAAI,CAACH,QAAQ,CAACM,qBAAqB,CAACD,IAAI,CAACF,MAAM,CAAC;MAC9F,OAAO,IAAI,CAACI,gBAAgB,CAACH,KAAK,EAAED,MAAM,CAAC;IAC/C,CAAC,EAAE,CAACK,KAAK,EAAEC,EAAE,KAAK;MACd,MAAMC,YAAY,GAAG,IAAI,CAACT,UAAU,CAACU,KAAK,CAACC,GAAG,CAAC,CAAC;MAChD,IAAI,CAAChB,UAAU,CAACC,GAAG,CAACW,KAAK,GAAGE,YAAY,EAAED,EAAE,CAAC;IACjD,CAAC,CAAC;IACF,IAAI,CAACb,UAAU,GAAGJ,eAAe,CAAC,IAAI,EAAEM,SAAS,CAAC;EACtD;EACA;EACAS,gBAAgBA,CAACM,YAAY,EAAEV,MAAM,EAAE;IACnC,MAAMO,YAAY,GAAG,IAAI,CAACT,UAAU,CAACU,KAAK,CAACN,IAAI,CAACF,MAAM,CAAC;IACvD,MAAMW,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAAC,IAAI,CAAChB,QAAQ,CAACM,qBAAqB,CAACD,IAAI,CAACF,MAAM,CAAC,GAAGO,YAAY,CAAC;IAC5F,MAAMR,QAAQ,GAAG,IAAI,CAACF,QAAQ,CAACiB,uBAAuB,CAACZ,IAAI,CAACF,MAAM,CAAC,GAAGY,IAAI,CAACC,KAAK,CAACH,YAAY,GAAGH,YAAY,CAAC,GAAGI,QAAQ;IACxH,MAAMI,oBAAoB,GAAG,GAAG;IAChC,IAAIR,YAAY,IAAIQ,oBAAoB,GAAG,CAAC,EAAE;MAC1C,OAAOJ,QAAQ;IACnB;IACA,IAAIZ,QAAQ,GAAGgB,oBAAoB,EAAE;MACjC,OAAOA,oBAAoB;IAC/B;IACA,IAAIhB,QAAQ,GAAGQ,YAAY,GAAGQ,oBAAoB,EAAE;MAChD,OAAOR,YAAY,GAAGQ,oBAAoB;IAC9C;IACA,OAAOhB,QAAQ;EACnB;AACJ;AACA,OAAO,MAAMiB,cAAc,SAAS7B,UAAU,CAAC;EAC3CS,WAAWA,CAACqB,QAAQ,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,eAAe,EAAErB,QAAQ,EAAEsB,UAAU,EAAE;IAChF,KAAK,CAAC,CAAC;IACP,IAAI,CAACJ,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACrB,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACsB,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,KAAK,GAAG,IAAI,CAACC,SAAS,CAAC,IAAIrC,IAAI,CAAC,IAAI,CAAC+B,QAAQ,EAAE;MAChDO,kBAAkB,EAAGF,KAAK,IAAK,CAAC;MAChCG,mBAAmB,EAAGH,KAAK,IAAK,IAAI,CAACvB,QAAQ,CAACU,GAAG,CAAC,CAAC;MACnDiB,qBAAqB,EAAGJ,KAAK,IAAK,IAAI,CAACJ,WAAW,CAACS,MAAM,CAAClB,GAAG,CAAC;IAClE,CAAC,EAAE;MAAEmB,WAAW,EAAE,CAAC,CAAC;IAA2B,CAAC,CAAC,CAAC;IAClD,IAAI,CAACC,kBAAkB,GAAGlC,SAAS;IACnC,IAAI,CAAC4B,SAAS,CAAC,IAAI,CAACD,KAAK,CAACQ,UAAU,CAAC,MAAM;MACvC,IAAI,CAACD,kBAAkB,GAAG,IAAI,CAAC9B,QAAQ,CAACU,GAAG,CAAC,CAAC;IACjD,CAAC,CAAC,CAAC;IACH,IAAI,CAACc,SAAS,CAAC,IAAI,CAACD,KAAK,CAACS,WAAW,CAAEC,CAAC,IAAK;MACzC,IAAI,CAACjC,QAAQ,CAACL,GAAG,CAAC,IAAI,CAACmC,kBAAkB,IAAIG,CAAC,CAACC,QAAQ,GAAGD,CAAC,CAACE,MAAM,CAAC,EAAEvC,SAAS,CAAC;IACnF,CAAC,CAAC,CAAC;IACH,IAAI,CAAC4B,SAAS,CAAC,IAAI,CAACD,KAAK,CAACa,QAAQ,CAAC,MAAM,IAAI,CAACb,KAAK,CAACc,MAAM,CAAC,CAAC,CAAC,CAAC;IAC9D,IAAI,CAACb,SAAS,CAAC,IAAI,CAACD,KAAK,CAACe,UAAU,CAAC,MAAM,IAAI,CAAChB,UAAU,CAAC,CAAC,CAAC,CAAC;IAC9D,IAAI,CAACE,SAAS,CAACnC,OAAO,CAACY,MAAM,IAAI;MAC7B,MAAMsC,MAAM,GAAG,IAAI,CAAClB,eAAe,CAAClB,IAAI,CAACF,MAAM,CAAC;MAChD,IAAIsC,MAAM,EAAE;QACR,IAAI,CAAChB,KAAK,CAACiB,iBAAiB,GAAGD,MAAM,CAACE,MAAM;MAChD;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,CAACjB,SAAS,CAACnC,OAAO,CAACY,MAAM,IAAI;MAC7B;MACA,MAAMyC,OAAO,GAAG,IAAI,CAACtB,QAAQ,CAACjB,IAAI,CAACF,MAAM,CAAC;MAC1C,IAAI,CAACsB,KAAK,CAACoB,KAAK,GAAGD,OAAO,GAAG,CAAC,CAAC,0BAA0B,CAAC,CAAC;MAC3D,IAAI,CAAC1C,QAAQ,CAACG,IAAI,CAACF,MAAM,CAAC;MAC1B,IAAI,CAACkB,WAAW,CAACS,MAAM,CAACzB,IAAI,CAACF,MAAM,CAAC;MACpC,IAAI,CAACsB,KAAK,CAACc,MAAM,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC;EACP;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}