{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { $ } from '../../dom.js';\nexport class RowCache {\n  constructor(renderers) {\n    this.renderers = renderers;\n    this.cache = new Map();\n    this.transactionNodesPendingRemoval = new Set();\n    this.inTransaction = false;\n  }\n  /**\n   * Returns a row either by creating a new one or reusing\n   * a previously released row which shares the same templateId.\n   *\n   * @returns A row and `isReusingConnectedDomNode` if the row's node is already in the dom in a stale position.\n   */\n  alloc(templateId) {\n    let result = this.getTemplateCache(templateId).pop();\n    let isStale = false;\n    if (result) {\n      isStale = this.transactionNodesPendingRemoval.has(result.domNode);\n      if (isStale) {\n        this.transactionNodesPendingRemoval.delete(result.domNode);\n      }\n    } else {\n      const domNode = $('.monaco-list-row');\n      const renderer = this.getRenderer(templateId);\n      const templateData = renderer.renderTemplate(domNode);\n      result = {\n        domNode,\n        templateId,\n        templateData\n      };\n    }\n    return {\n      row: result,\n      isReusingConnectedDomNode: isStale\n    };\n  }\n  /**\n   * Releases the row for eventual reuse.\n   */\n  release(row) {\n    if (!row) {\n      return;\n    }\n    this.releaseRow(row);\n  }\n  /**\n   * Begin a set of changes that use the cache. This lets us skip work when a row is removed and then inserted again.\n   */\n  transact(makeChanges) {\n    if (this.inTransaction) {\n      throw new Error('Already in transaction');\n    }\n    this.inTransaction = true;\n    try {\n      makeChanges();\n    } finally {\n      for (const domNode of this.transactionNodesPendingRemoval) {\n        this.doRemoveNode(domNode);\n      }\n      this.transactionNodesPendingRemoval.clear();\n      this.inTransaction = false;\n    }\n  }\n  releaseRow(row) {\n    const {\n      domNode,\n      templateId\n    } = row;\n    if (domNode) {\n      if (this.inTransaction) {\n        this.transactionNodesPendingRemoval.add(domNode);\n      } else {\n        this.doRemoveNode(domNode);\n      }\n    }\n    const cache = this.getTemplateCache(templateId);\n    cache.push(row);\n  }\n  doRemoveNode(domNode) {\n    domNode.classList.remove('scrolling');\n    domNode.remove();\n  }\n  getTemplateCache(templateId) {\n    let result = this.cache.get(templateId);\n    if (!result) {\n      result = [];\n      this.cache.set(templateId, result);\n    }\n    return result;\n  }\n  dispose() {\n    this.cache.forEach((cachedRows, templateId) => {\n      for (const cachedRow of cachedRows) {\n        const renderer = this.getRenderer(templateId);\n        renderer.disposeTemplate(cachedRow.templateData);\n        cachedRow.templateData = null;\n      }\n    });\n    this.cache.clear();\n    this.transactionNodesPendingRemoval.clear();\n  }\n  getRenderer(templateId) {\n    const renderer = this.renderers.get(templateId);\n    if (!renderer) {\n      throw new Error(`No renderer found for ${templateId}`);\n    }\n    return renderer;\n  }\n}", "map": {"version": 3, "names": ["$", "<PERSON><PERSON><PERSON>", "constructor", "renderers", "cache", "Map", "transactionNodesPendingRemoval", "Set", "inTransaction", "alloc", "templateId", "result", "getTemplateCache", "pop", "isStale", "has", "domNode", "delete", "renderer", "<PERSON><PERSON><PERSON><PERSON>", "templateData", "renderTemplate", "row", "isReusingConnectedDomNode", "release", "releaseRow", "transact", "makeChanges", "Error", "doRemoveNode", "clear", "add", "push", "classList", "remove", "get", "set", "dispose", "for<PERSON>ach", "cachedRows", "cachedRow", "disposeTemplate"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/browser/ui/list/rowCache.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { $ } from '../../dom.js';\nexport class RowCache {\n    constructor(renderers) {\n        this.renderers = renderers;\n        this.cache = new Map();\n        this.transactionNodesPendingRemoval = new Set();\n        this.inTransaction = false;\n    }\n    /**\n     * Returns a row either by creating a new one or reusing\n     * a previously released row which shares the same templateId.\n     *\n     * @returns A row and `isReusingConnectedDomNode` if the row's node is already in the dom in a stale position.\n     */\n    alloc(templateId) {\n        let result = this.getTemplateCache(templateId).pop();\n        let isStale = false;\n        if (result) {\n            isStale = this.transactionNodesPendingRemoval.has(result.domNode);\n            if (isStale) {\n                this.transactionNodesPendingRemoval.delete(result.domNode);\n            }\n        }\n        else {\n            const domNode = $('.monaco-list-row');\n            const renderer = this.getRenderer(templateId);\n            const templateData = renderer.renderTemplate(domNode);\n            result = { domNode, templateId, templateData };\n        }\n        return { row: result, isReusingConnectedDomNode: isStale };\n    }\n    /**\n     * Releases the row for eventual reuse.\n     */\n    release(row) {\n        if (!row) {\n            return;\n        }\n        this.releaseRow(row);\n    }\n    /**\n     * Begin a set of changes that use the cache. This lets us skip work when a row is removed and then inserted again.\n     */\n    transact(makeChanges) {\n        if (this.inTransaction) {\n            throw new Error('Already in transaction');\n        }\n        this.inTransaction = true;\n        try {\n            makeChanges();\n        }\n        finally {\n            for (const domNode of this.transactionNodesPendingRemoval) {\n                this.doRemoveNode(domNode);\n            }\n            this.transactionNodesPendingRemoval.clear();\n            this.inTransaction = false;\n        }\n    }\n    releaseRow(row) {\n        const { domNode, templateId } = row;\n        if (domNode) {\n            if (this.inTransaction) {\n                this.transactionNodesPendingRemoval.add(domNode);\n            }\n            else {\n                this.doRemoveNode(domNode);\n            }\n        }\n        const cache = this.getTemplateCache(templateId);\n        cache.push(row);\n    }\n    doRemoveNode(domNode) {\n        domNode.classList.remove('scrolling');\n        domNode.remove();\n    }\n    getTemplateCache(templateId) {\n        let result = this.cache.get(templateId);\n        if (!result) {\n            result = [];\n            this.cache.set(templateId, result);\n        }\n        return result;\n    }\n    dispose() {\n        this.cache.forEach((cachedRows, templateId) => {\n            for (const cachedRow of cachedRows) {\n                const renderer = this.getRenderer(templateId);\n                renderer.disposeTemplate(cachedRow.templateData);\n                cachedRow.templateData = null;\n            }\n        });\n        this.cache.clear();\n        this.transactionNodesPendingRemoval.clear();\n    }\n    getRenderer(templateId) {\n        const renderer = this.renderers.get(templateId);\n        if (!renderer) {\n            throw new Error(`No renderer found for ${templateId}`);\n        }\n        return renderer;\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,CAAC,QAAQ,cAAc;AAChC,OAAO,MAAMC,QAAQ,CAAC;EAClBC,WAAWA,CAACC,SAAS,EAAE;IACnB,IAAI,CAACA,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,KAAK,GAAG,IAAIC,GAAG,CAAC,CAAC;IACtB,IAAI,CAACC,8BAA8B,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC/C,IAAI,CAACC,aAAa,GAAG,KAAK;EAC9B;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,KAAKA,CAACC,UAAU,EAAE;IACd,IAAIC,MAAM,GAAG,IAAI,CAACC,gBAAgB,CAACF,UAAU,CAAC,CAACG,GAAG,CAAC,CAAC;IACpD,IAAIC,OAAO,GAAG,KAAK;IACnB,IAAIH,MAAM,EAAE;MACRG,OAAO,GAAG,IAAI,CAACR,8BAA8B,CAACS,GAAG,CAACJ,MAAM,CAACK,OAAO,CAAC;MACjE,IAAIF,OAAO,EAAE;QACT,IAAI,CAACR,8BAA8B,CAACW,MAAM,CAACN,MAAM,CAACK,OAAO,CAAC;MAC9D;IACJ,CAAC,MACI;MACD,MAAMA,OAAO,GAAGhB,CAAC,CAAC,kBAAkB,CAAC;MACrC,MAAMkB,QAAQ,GAAG,IAAI,CAACC,WAAW,CAACT,UAAU,CAAC;MAC7C,MAAMU,YAAY,GAAGF,QAAQ,CAACG,cAAc,CAACL,OAAO,CAAC;MACrDL,MAAM,GAAG;QAAEK,OAAO;QAAEN,UAAU;QAAEU;MAAa,CAAC;IAClD;IACA,OAAO;MAAEE,GAAG,EAAEX,MAAM;MAAEY,yBAAyB,EAAET;IAAQ,CAAC;EAC9D;EACA;AACJ;AACA;EACIU,OAAOA,CAACF,GAAG,EAAE;IACT,IAAI,CAACA,GAAG,EAAE;MACN;IACJ;IACA,IAAI,CAACG,UAAU,CAACH,GAAG,CAAC;EACxB;EACA;AACJ;AACA;EACII,QAAQA,CAACC,WAAW,EAAE;IAClB,IAAI,IAAI,CAACnB,aAAa,EAAE;MACpB,MAAM,IAAIoB,KAAK,CAAC,wBAAwB,CAAC;IAC7C;IACA,IAAI,CAACpB,aAAa,GAAG,IAAI;IACzB,IAAI;MACAmB,WAAW,CAAC,CAAC;IACjB,CAAC,SACO;MACJ,KAAK,MAAMX,OAAO,IAAI,IAAI,CAACV,8BAA8B,EAAE;QACvD,IAAI,CAACuB,YAAY,CAACb,OAAO,CAAC;MAC9B;MACA,IAAI,CAACV,8BAA8B,CAACwB,KAAK,CAAC,CAAC;MAC3C,IAAI,CAACtB,aAAa,GAAG,KAAK;IAC9B;EACJ;EACAiB,UAAUA,CAACH,GAAG,EAAE;IACZ,MAAM;MAAEN,OAAO;MAAEN;IAAW,CAAC,GAAGY,GAAG;IACnC,IAAIN,OAAO,EAAE;MACT,IAAI,IAAI,CAACR,aAAa,EAAE;QACpB,IAAI,CAACF,8BAA8B,CAACyB,GAAG,CAACf,OAAO,CAAC;MACpD,CAAC,MACI;QACD,IAAI,CAACa,YAAY,CAACb,OAAO,CAAC;MAC9B;IACJ;IACA,MAAMZ,KAAK,GAAG,IAAI,CAACQ,gBAAgB,CAACF,UAAU,CAAC;IAC/CN,KAAK,CAAC4B,IAAI,CAACV,GAAG,CAAC;EACnB;EACAO,YAAYA,CAACb,OAAO,EAAE;IAClBA,OAAO,CAACiB,SAAS,CAACC,MAAM,CAAC,WAAW,CAAC;IACrClB,OAAO,CAACkB,MAAM,CAAC,CAAC;EACpB;EACAtB,gBAAgBA,CAACF,UAAU,EAAE;IACzB,IAAIC,MAAM,GAAG,IAAI,CAACP,KAAK,CAAC+B,GAAG,CAACzB,UAAU,CAAC;IACvC,IAAI,CAACC,MAAM,EAAE;MACTA,MAAM,GAAG,EAAE;MACX,IAAI,CAACP,KAAK,CAACgC,GAAG,CAAC1B,UAAU,EAAEC,MAAM,CAAC;IACtC;IACA,OAAOA,MAAM;EACjB;EACA0B,OAAOA,CAAA,EAAG;IACN,IAAI,CAACjC,KAAK,CAACkC,OAAO,CAAC,CAACC,UAAU,EAAE7B,UAAU,KAAK;MAC3C,KAAK,MAAM8B,SAAS,IAAID,UAAU,EAAE;QAChC,MAAMrB,QAAQ,GAAG,IAAI,CAACC,WAAW,CAACT,UAAU,CAAC;QAC7CQ,QAAQ,CAACuB,eAAe,CAACD,SAAS,CAACpB,YAAY,CAAC;QAChDoB,SAAS,CAACpB,YAAY,GAAG,IAAI;MACjC;IACJ,CAAC,CAAC;IACF,IAAI,CAAChB,KAAK,CAAC0B,KAAK,CAAC,CAAC;IAClB,IAAI,CAACxB,8BAA8B,CAACwB,KAAK,CAAC,CAAC;EAC/C;EACAX,WAAWA,CAACT,UAAU,EAAE;IACpB,MAAMQ,QAAQ,GAAG,IAAI,CAACf,SAAS,CAACgC,GAAG,CAACzB,UAAU,CAAC;IAC/C,IAAI,CAACQ,QAAQ,EAAE;MACX,MAAM,IAAIU,KAAK,CAAC,yBAAyBlB,UAAU,EAAE,CAAC;IAC1D;IACA,OAAOQ,QAAQ;EACnB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}