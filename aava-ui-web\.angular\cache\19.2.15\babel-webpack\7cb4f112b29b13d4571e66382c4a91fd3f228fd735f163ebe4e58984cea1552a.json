{"ast": null, "code": "import _asyncToGenerator from \"C:/console/aava-ui-web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = this && this.__param || function (paramIndex, decorator) {\n  return function (target, key) {\n    decorator(target, key, paramIndex);\n  };\n};\nvar InlineEditsModel_1;\nimport { timeout } from '../../../../base/common/async.js';\nimport { CancellationToken, cancelOnDispose } from '../../../../base/common/cancellation.js';\nimport { itemsEquals, structuralEquals } from '../../../../base/common/equals.js';\nimport { BugIndicatingError } from '../../../../base/common/errors.js';\nimport { Disposable, DisposableStore, toDisposable } from '../../../../base/common/lifecycle.js';\nimport { ObservablePromise, derived, derivedHandleChanges, derivedOpts, disposableObservableValue, observableSignal, observableValue, recomputeInitiallyAndOnChange, subtransaction } from '../../../../base/common/observable.js';\nimport { derivedDisposable } from '../../../../base/common/observableInternal/derived.js';\nimport { URI } from '../../../../base/common/uri.js';\nimport { IDiffProviderFactoryService } from '../../../browser/widget/diffEditor/diffProviderFactoryService.js';\nimport { LineRange } from '../../../common/core/lineRange.js';\nimport { InlineCompletionTriggerKind } from '../../../common/languages.js';\nimport { ILanguageFeaturesService } from '../../../common/services/languageFeatures.js';\nimport { IModelService } from '../../../common/services/model.js';\nimport { provideInlineCompletions } from '../../inlineCompletions/browser/model/provideInlineCompletions.js';\nimport { InlineEdit } from './inlineEditsWidget.js';\nlet InlineEditsModel = /*#__PURE__*/(() => {\n  let InlineEditsModel = class InlineEditsModel extends Disposable {\n    static {\n      InlineEditsModel_1 = this;\n    }\n    static {\n      this._modelId = 0;\n    }\n    static _createUniqueUri() {\n      return URI.from({\n        scheme: 'inline-edits',\n        path: new Date().toString() + String(InlineEditsModel_1._modelId++)\n      });\n    }\n    constructor(textModel, _textModelVersionId, _selection, _debounceValue, languageFeaturesService, _diffProviderFactoryService, _modelService) {\n      var _this;\n      super();\n      _this = this;\n      this.textModel = textModel;\n      this._textModelVersionId = _textModelVersionId;\n      this._selection = _selection;\n      this._debounceValue = _debounceValue;\n      this.languageFeaturesService = languageFeaturesService;\n      this._diffProviderFactoryService = _diffProviderFactoryService;\n      this._modelService = _modelService;\n      this._forceUpdateExplicitlySignal = observableSignal(this);\n      // We use a semantic id to keep the same inline completion selected even if the provider reorders the completions.\n      this._selectedInlineCompletionId = observableValue(this, undefined);\n      this._isActive = observableValue(this, false);\n      this._originalModel = derivedDisposable(() => this._modelService.createModel('', null, InlineEditsModel_1._createUniqueUri())).keepObserved(this._store);\n      this._modifiedModel = derivedDisposable(() => this._modelService.createModel('', null, InlineEditsModel_1._createUniqueUri())).keepObserved(this._store);\n      this._pinnedRange = new TrackedRange(this.textModel, this._textModelVersionId);\n      this.isPinned = this._pinnedRange.range.map(range => !!range);\n      this.userPrompt = observableValue(this, undefined);\n      this.inlineEdit = derived(this, reader => {\n        return this._inlineEdit.read(reader)?.promiseResult.read(reader)?.data;\n      });\n      this._inlineEdit = derived(this, reader => {\n        const edit = this.selectedInlineEdit.read(reader);\n        if (!edit) {\n          return undefined;\n        }\n        const range = edit.inlineCompletion.range;\n        if (edit.inlineCompletion.insertText.trim() === '') {\n          return undefined;\n        }\n        let newLines = edit.inlineCompletion.insertText.split(/\\r\\n|\\r|\\n/);\n        function removeIndentation(lines) {\n          const indentation = lines[0].match(/^\\s*/)?.[0] ?? '';\n          return lines.map(l => l.replace(new RegExp('^' + indentation), ''));\n        }\n        newLines = removeIndentation(newLines);\n        const existing = this.textModel.getValueInRange(range);\n        let existingLines = existing.split(/\\r\\n|\\r|\\n/);\n        existingLines = removeIndentation(existingLines);\n        this._originalModel.get().setValue(existingLines.join('\\n'));\n        this._modifiedModel.get().setValue(newLines.join('\\n'));\n        const d = this._diffProviderFactoryService.createDiffProvider({\n          diffAlgorithm: 'advanced'\n        });\n        return ObservablePromise.fromFn(/*#__PURE__*/_asyncToGenerator(function* () {\n          const result = yield d.computeDiff(_this._originalModel.get(), _this._modifiedModel.get(), {\n            computeMoves: false,\n            ignoreTrimWhitespace: false,\n            maxComputationTimeMs: 1000\n          }, CancellationToken.None);\n          if (result.identical) {\n            return undefined;\n          }\n          return new InlineEdit(LineRange.fromRangeInclusive(range), removeIndentation(newLines), result.changes);\n        }));\n      });\n      this._fetchStore = this._register(new DisposableStore());\n      this._inlineEditsFetchResult = disposableObservableValue(this, undefined);\n      this._inlineEdits = derivedOpts({\n        owner: this,\n        equalsFn: structuralEquals\n      }, reader => {\n        return this._inlineEditsFetchResult.read(reader)?.completions.map(c => new InlineEditData(c)) ?? [];\n      });\n      this._fetchInlineEditsPromise = derivedHandleChanges({\n        owner: this,\n        createEmptyChangeSummary: () => ({\n          inlineCompletionTriggerKind: InlineCompletionTriggerKind.Automatic\n        }),\n        handleChange: (ctx, changeSummary) => {\n          /** @description fetch inline completions */\n          if (ctx.didChange(this._forceUpdateExplicitlySignal)) {\n            changeSummary.inlineCompletionTriggerKind = InlineCompletionTriggerKind.Explicit;\n          }\n          return true;\n        }\n      }, /*#__PURE__*/function () {\n        var _ref2 = _asyncToGenerator(function* (reader, changeSummary) {\n          _this._fetchStore.clear();\n          _this._forceUpdateExplicitlySignal.read(reader);\n          /*if (!this._isActive.read(reader)) {\n              return undefined;\n          }*/\n          _this._textModelVersionId.read(reader);\n          function mapValue(value, fn) {\n            return fn(value);\n          }\n          const selection = _this._pinnedRange.range.read(reader) ?? mapValue(_this._selection.read(reader), v => v.isEmpty() ? undefined : v);\n          if (!selection) {\n            _this._inlineEditsFetchResult.set(undefined, undefined);\n            _this.userPrompt.set(undefined, undefined);\n            return undefined;\n          }\n          const context = {\n            triggerKind: changeSummary.inlineCompletionTriggerKind,\n            selectedSuggestionInfo: undefined,\n            userPrompt: _this.userPrompt.read(reader)\n          };\n          const token = cancelOnDispose(_this._fetchStore);\n          yield timeout(200, token);\n          const result = yield provideInlineCompletions(_this.languageFeaturesService.inlineCompletionsProvider, selection, _this.textModel, context, token);\n          if (token.isCancellationRequested) {\n            return;\n          }\n          _this._inlineEditsFetchResult.set(result, undefined);\n        });\n        return function (_x, _x2) {\n          return _ref2.apply(this, arguments);\n        };\n      }());\n      this._filteredInlineEditItems = derivedOpts({\n        owner: this,\n        equalsFn: itemsEquals()\n      }, reader => {\n        return this._inlineEdits.read(reader);\n      });\n      this.selectedInlineCompletionIndex = derived(this, reader => {\n        const selectedInlineCompletionId = this._selectedInlineCompletionId.read(reader);\n        const filteredCompletions = this._filteredInlineEditItems.read(reader);\n        const idx = this._selectedInlineCompletionId === undefined ? -1 : filteredCompletions.findIndex(v => v.semanticId === selectedInlineCompletionId);\n        if (idx === -1) {\n          // Reset the selection so that the selection does not jump back when it appears again\n          this._selectedInlineCompletionId.set(undefined, undefined);\n          return 0;\n        }\n        return idx;\n      });\n      this.selectedInlineEdit = derived(this, reader => {\n        const filteredCompletions = this._filteredInlineEditItems.read(reader);\n        const idx = this.selectedInlineCompletionIndex.read(reader);\n        return filteredCompletions[idx];\n      });\n      this._register(recomputeInitiallyAndOnChange(this._fetchInlineEditsPromise));\n    }\n    triggerExplicitly(tx) {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        subtransaction(tx, tx => {\n          _this2._isActive.set(true, tx);\n          _this2._forceUpdateExplicitlySignal.trigger(tx);\n        });\n        yield _this2._fetchInlineEditsPromise.get();\n      })();\n    }\n    stop(tx) {\n      subtransaction(tx, tx => {\n        this.userPrompt.set(undefined, tx);\n        this._isActive.set(false, tx);\n        this._inlineEditsFetchResult.set(undefined, tx);\n        this._pinnedRange.setRange(undefined, tx);\n        //this._source.clear(tx);\n      });\n    }\n    _deltaSelectedInlineCompletionIndex(delta) {\n      var _this3 = this;\n      return _asyncToGenerator(function* () {\n        yield _this3.triggerExplicitly();\n        const completions = _this3._filteredInlineEditItems.get() || [];\n        if (completions.length > 0) {\n          const newIdx = (_this3.selectedInlineCompletionIndex.get() + delta + completions.length) % completions.length;\n          _this3._selectedInlineCompletionId.set(completions[newIdx].semanticId, undefined);\n        } else {\n          _this3._selectedInlineCompletionId.set(undefined, undefined);\n        }\n      })();\n    }\n    next() {\n      var _this4 = this;\n      return _asyncToGenerator(function* () {\n        yield _this4._deltaSelectedInlineCompletionIndex(1);\n      })();\n    }\n    previous() {\n      var _this5 = this;\n      return _asyncToGenerator(function* () {\n        yield _this5._deltaSelectedInlineCompletionIndex(-1);\n      })();\n    }\n    accept(editor) {\n      var _this6 = this;\n      return _asyncToGenerator(function* () {\n        if (editor.getModel() !== _this6.textModel) {\n          throw new BugIndicatingError();\n        }\n        const edit = _this6.selectedInlineEdit.get();\n        if (!edit) {\n          return;\n        }\n        editor.pushUndoStop();\n        editor.executeEdits('inlineSuggestion.accept', [edit.inlineCompletion.toSingleTextEdit().toSingleEditOperation()]);\n        _this6.stop();\n      })();\n    }\n  };\n  return InlineEditsModel;\n})();\nInlineEditsModel = InlineEditsModel_1 = __decorate([__param(4, ILanguageFeaturesService), __param(5, IDiffProviderFactoryService), __param(6, IModelService)], InlineEditsModel);\nexport { InlineEditsModel };\nclass InlineEditData {\n  constructor(inlineCompletion) {\n    this.inlineCompletion = inlineCompletion;\n    this.semanticId = this.inlineCompletion.hash();\n  }\n}\nclass TrackedRange extends Disposable {\n  constructor(_textModel, _versionId) {\n    super();\n    this._textModel = _textModel;\n    this._versionId = _versionId;\n    this._decorations = observableValue(this, []);\n    this.range = derived(this, reader => {\n      this._versionId.read(reader);\n      const deco = this._decorations.read(reader)[0];\n      if (!deco) {\n        return null;\n      }\n      return this._textModel.getDecorationRange(deco) ?? null;\n    });\n    this._register(toDisposable(() => {\n      this._textModel.deltaDecorations(this._decorations.get(), []);\n    }));\n  }\n  setRange(range, tx) {\n    this._decorations.set(this._textModel.deltaDecorations(this._decorations.get(), range ? [{\n      range,\n      options: {\n        description: 'trackedRange'\n      }\n    }] : []), tx);\n  }\n}", "map": {"version": 3, "names": ["__decorate", "decorators", "target", "key", "desc", "c", "arguments", "length", "r", "Object", "getOwnPropertyDescriptor", "d", "Reflect", "decorate", "i", "defineProperty", "__param", "paramIndex", "decorator", "InlineEditsModel_1", "timeout", "CancellationToken", "cancelOnDispose", "itemsEquals", "structuralEquals", "BugIndicatingError", "Disposable", "DisposableStore", "toDisposable", "ObservablePromise", "derived", "derivedHandleChanges", "derivedOpts", "disposableObservableValue", "observableSignal", "observableValue", "recomputeInitiallyAndOnChange", "subtransaction", "derivedDisposable", "URI", "IDiffProviderFactoryService", "LineRange", "InlineCompletionTriggerKind", "ILanguageFeaturesService", "IModelService", "provideInlineCompletions", "InlineEdit", "InlineEditsModel", "_modelId", "_createUniqueUri", "from", "scheme", "path", "Date", "toString", "String", "constructor", "textModel", "_textModelVersionId", "_selection", "_debounceValue", "languageFeaturesService", "_diffProviderFactoryService", "_modelService", "_this", "this", "_forceUpdateExplicitlySignal", "_selectedInlineCompletionId", "undefined", "_isActive", "_originalModel", "createModel", "keepObserved", "_store", "_modifiedModel", "_<PERSON><PERSON><PERSON><PERSON>", "TrackedRange", "isPinned", "range", "map", "userPrompt", "inlineEdit", "reader", "_inlineEdit", "read", "promiseResult", "data", "edit", "selectedInlineEdit", "inlineCompletion", "insertText", "trim", "newLines", "split", "removeIndentation", "lines", "indentation", "match", "l", "replace", "RegExp", "existing", "getValueInRange", "existingLines", "get", "setValue", "join", "createDiffProvider", "diffAlgorithm", "fromFn", "_asyncToGenerator", "result", "computeDiff", "computeMoves", "ignoreTrimWhitespace", "maxComputationTimeMs", "None", "identical", "fromRangeInclusive", "changes", "_fetchStore", "_register", "_inlineEditsFetchResult", "_inlineEdits", "owner", "equalsFn", "completions", "InlineEditData", "_fetchInlineEditsPromise", "createEmptyChangeSummary", "inlineCompletionTriggerKind", "Automatic", "handleChange", "ctx", "changeSummary", "<PERSON><PERSON><PERSON><PERSON>", "Explicit", "_ref2", "clear", "mapValue", "value", "fn", "selection", "v", "isEmpty", "set", "context", "trigger<PERSON>ind", "selectedSuggestionInfo", "token", "inlineCompletionsProvider", "isCancellationRequested", "_x", "_x2", "apply", "_filteredInlineEditItems", "selectedInlineCompletionIndex", "selectedInlineCompletionId", "filteredCompletions", "idx", "findIndex", "semanticId", "triggerExplicitly", "tx", "_this2", "trigger", "stop", "setRang<PERSON>", "_deltaSelectedInlineCompletionIndex", "delta", "_this3", "newIdx", "next", "_this4", "previous", "_this5", "accept", "editor", "_this6", "getModel", "pushUndoStop", "executeEdits", "toSingleTextEdit", "toSingleEditOperation", "hash", "_textModel", "_versionId", "_decorations", "deco", "getDecorationRange", "deltaDecorations", "options", "description"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/contrib/inlineEdits/browser/inlineEditsModel.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = (this && this.__param) || function (paramIndex, decorator) {\n    return function (target, key) { decorator(target, key, paramIndex); }\n};\nvar InlineEditsModel_1;\nimport { timeout } from '../../../../base/common/async.js';\nimport { CancellationToken, cancelOnDispose } from '../../../../base/common/cancellation.js';\nimport { itemsEquals, structuralEquals } from '../../../../base/common/equals.js';\nimport { BugIndicatingError } from '../../../../base/common/errors.js';\nimport { Disposable, DisposableStore, toDisposable } from '../../../../base/common/lifecycle.js';\nimport { ObservablePromise, derived, derivedHandleChanges, derivedOpts, disposableObservableValue, observableSignal, observableValue, recomputeInitiallyAndOnChange, subtransaction } from '../../../../base/common/observable.js';\nimport { derivedDisposable } from '../../../../base/common/observableInternal/derived.js';\nimport { URI } from '../../../../base/common/uri.js';\nimport { IDiffProviderFactoryService } from '../../../browser/widget/diffEditor/diffProviderFactoryService.js';\nimport { LineRange } from '../../../common/core/lineRange.js';\nimport { InlineCompletionTriggerKind } from '../../../common/languages.js';\nimport { ILanguageFeaturesService } from '../../../common/services/languageFeatures.js';\nimport { IModelService } from '../../../common/services/model.js';\nimport { provideInlineCompletions } from '../../inlineCompletions/browser/model/provideInlineCompletions.js';\nimport { InlineEdit } from './inlineEditsWidget.js';\nlet InlineEditsModel = class InlineEditsModel extends Disposable {\n    static { InlineEditsModel_1 = this; }\n    static { this._modelId = 0; }\n    static _createUniqueUri() {\n        return URI.from({ scheme: 'inline-edits', path: new Date().toString() + String(InlineEditsModel_1._modelId++) });\n    }\n    constructor(textModel, _textModelVersionId, _selection, _debounceValue, languageFeaturesService, _diffProviderFactoryService, _modelService) {\n        super();\n        this.textModel = textModel;\n        this._textModelVersionId = _textModelVersionId;\n        this._selection = _selection;\n        this._debounceValue = _debounceValue;\n        this.languageFeaturesService = languageFeaturesService;\n        this._diffProviderFactoryService = _diffProviderFactoryService;\n        this._modelService = _modelService;\n        this._forceUpdateExplicitlySignal = observableSignal(this);\n        // We use a semantic id to keep the same inline completion selected even if the provider reorders the completions.\n        this._selectedInlineCompletionId = observableValue(this, undefined);\n        this._isActive = observableValue(this, false);\n        this._originalModel = derivedDisposable(() => this._modelService.createModel('', null, InlineEditsModel_1._createUniqueUri())).keepObserved(this._store);\n        this._modifiedModel = derivedDisposable(() => this._modelService.createModel('', null, InlineEditsModel_1._createUniqueUri())).keepObserved(this._store);\n        this._pinnedRange = new TrackedRange(this.textModel, this._textModelVersionId);\n        this.isPinned = this._pinnedRange.range.map(range => !!range);\n        this.userPrompt = observableValue(this, undefined);\n        this.inlineEdit = derived(this, reader => {\n            return this._inlineEdit.read(reader)?.promiseResult.read(reader)?.data;\n        });\n        this._inlineEdit = derived(this, reader => {\n            const edit = this.selectedInlineEdit.read(reader);\n            if (!edit) {\n                return undefined;\n            }\n            const range = edit.inlineCompletion.range;\n            if (edit.inlineCompletion.insertText.trim() === '') {\n                return undefined;\n            }\n            let newLines = edit.inlineCompletion.insertText.split(/\\r\\n|\\r|\\n/);\n            function removeIndentation(lines) {\n                const indentation = lines[0].match(/^\\s*/)?.[0] ?? '';\n                return lines.map(l => l.replace(new RegExp('^' + indentation), ''));\n            }\n            newLines = removeIndentation(newLines);\n            const existing = this.textModel.getValueInRange(range);\n            let existingLines = existing.split(/\\r\\n|\\r|\\n/);\n            existingLines = removeIndentation(existingLines);\n            this._originalModel.get().setValue(existingLines.join('\\n'));\n            this._modifiedModel.get().setValue(newLines.join('\\n'));\n            const d = this._diffProviderFactoryService.createDiffProvider({ diffAlgorithm: 'advanced' });\n            return ObservablePromise.fromFn(async () => {\n                const result = await d.computeDiff(this._originalModel.get(), this._modifiedModel.get(), {\n                    computeMoves: false,\n                    ignoreTrimWhitespace: false,\n                    maxComputationTimeMs: 1000,\n                }, CancellationToken.None);\n                if (result.identical) {\n                    return undefined;\n                }\n                return new InlineEdit(LineRange.fromRangeInclusive(range), removeIndentation(newLines), result.changes);\n            });\n        });\n        this._fetchStore = this._register(new DisposableStore());\n        this._inlineEditsFetchResult = disposableObservableValue(this, undefined);\n        this._inlineEdits = derivedOpts({ owner: this, equalsFn: structuralEquals }, reader => {\n            return this._inlineEditsFetchResult.read(reader)?.completions.map(c => new InlineEditData(c)) ?? [];\n        });\n        this._fetchInlineEditsPromise = derivedHandleChanges({\n            owner: this,\n            createEmptyChangeSummary: () => ({\n                inlineCompletionTriggerKind: InlineCompletionTriggerKind.Automatic\n            }),\n            handleChange: (ctx, changeSummary) => {\n                /** @description fetch inline completions */\n                if (ctx.didChange(this._forceUpdateExplicitlySignal)) {\n                    changeSummary.inlineCompletionTriggerKind = InlineCompletionTriggerKind.Explicit;\n                }\n                return true;\n            },\n        }, async (reader, changeSummary) => {\n            this._fetchStore.clear();\n            this._forceUpdateExplicitlySignal.read(reader);\n            /*if (!this._isActive.read(reader)) {\n                return undefined;\n            }*/\n            this._textModelVersionId.read(reader);\n            function mapValue(value, fn) {\n                return fn(value);\n            }\n            const selection = this._pinnedRange.range.read(reader) ?? mapValue(this._selection.read(reader), v => v.isEmpty() ? undefined : v);\n            if (!selection) {\n                this._inlineEditsFetchResult.set(undefined, undefined);\n                this.userPrompt.set(undefined, undefined);\n                return undefined;\n            }\n            const context = {\n                triggerKind: changeSummary.inlineCompletionTriggerKind,\n                selectedSuggestionInfo: undefined,\n                userPrompt: this.userPrompt.read(reader),\n            };\n            const token = cancelOnDispose(this._fetchStore);\n            await timeout(200, token);\n            const result = await provideInlineCompletions(this.languageFeaturesService.inlineCompletionsProvider, selection, this.textModel, context, token);\n            if (token.isCancellationRequested) {\n                return;\n            }\n            this._inlineEditsFetchResult.set(result, undefined);\n        });\n        this._filteredInlineEditItems = derivedOpts({ owner: this, equalsFn: itemsEquals() }, reader => {\n            return this._inlineEdits.read(reader);\n        });\n        this.selectedInlineCompletionIndex = derived(this, (reader) => {\n            const selectedInlineCompletionId = this._selectedInlineCompletionId.read(reader);\n            const filteredCompletions = this._filteredInlineEditItems.read(reader);\n            const idx = this._selectedInlineCompletionId === undefined ? -1\n                : filteredCompletions.findIndex(v => v.semanticId === selectedInlineCompletionId);\n            if (idx === -1) {\n                // Reset the selection so that the selection does not jump back when it appears again\n                this._selectedInlineCompletionId.set(undefined, undefined);\n                return 0;\n            }\n            return idx;\n        });\n        this.selectedInlineEdit = derived(this, (reader) => {\n            const filteredCompletions = this._filteredInlineEditItems.read(reader);\n            const idx = this.selectedInlineCompletionIndex.read(reader);\n            return filteredCompletions[idx];\n        });\n        this._register(recomputeInitiallyAndOnChange(this._fetchInlineEditsPromise));\n    }\n    async triggerExplicitly(tx) {\n        subtransaction(tx, tx => {\n            this._isActive.set(true, tx);\n            this._forceUpdateExplicitlySignal.trigger(tx);\n        });\n        await this._fetchInlineEditsPromise.get();\n    }\n    stop(tx) {\n        subtransaction(tx, tx => {\n            this.userPrompt.set(undefined, tx);\n            this._isActive.set(false, tx);\n            this._inlineEditsFetchResult.set(undefined, tx);\n            this._pinnedRange.setRange(undefined, tx);\n            //this._source.clear(tx);\n        });\n    }\n    async _deltaSelectedInlineCompletionIndex(delta) {\n        await this.triggerExplicitly();\n        const completions = this._filteredInlineEditItems.get() || [];\n        if (completions.length > 0) {\n            const newIdx = (this.selectedInlineCompletionIndex.get() + delta + completions.length) % completions.length;\n            this._selectedInlineCompletionId.set(completions[newIdx].semanticId, undefined);\n        }\n        else {\n            this._selectedInlineCompletionId.set(undefined, undefined);\n        }\n    }\n    async next() {\n        await this._deltaSelectedInlineCompletionIndex(1);\n    }\n    async previous() {\n        await this._deltaSelectedInlineCompletionIndex(-1);\n    }\n    async accept(editor) {\n        if (editor.getModel() !== this.textModel) {\n            throw new BugIndicatingError();\n        }\n        const edit = this.selectedInlineEdit.get();\n        if (!edit) {\n            return;\n        }\n        editor.pushUndoStop();\n        editor.executeEdits('inlineSuggestion.accept', [\n            edit.inlineCompletion.toSingleTextEdit().toSingleEditOperation()\n        ]);\n        this.stop();\n    }\n};\nInlineEditsModel = InlineEditsModel_1 = __decorate([\n    __param(4, ILanguageFeaturesService),\n    __param(5, IDiffProviderFactoryService),\n    __param(6, IModelService)\n], InlineEditsModel);\nexport { InlineEditsModel };\nclass InlineEditData {\n    constructor(inlineCompletion) {\n        this.inlineCompletion = inlineCompletion;\n        this.semanticId = this.inlineCompletion.hash();\n    }\n}\nclass TrackedRange extends Disposable {\n    constructor(_textModel, _versionId) {\n        super();\n        this._textModel = _textModel;\n        this._versionId = _versionId;\n        this._decorations = observableValue(this, []);\n        this.range = derived(this, reader => {\n            this._versionId.read(reader);\n            const deco = this._decorations.read(reader)[0];\n            if (!deco) {\n                return null;\n            }\n            return this._textModel.getDecorationRange(deco) ?? null;\n        });\n        this._register(toDisposable(() => {\n            this._textModel.deltaDecorations(this._decorations.get(), []);\n        }));\n    }\n    setRange(range, tx) {\n        this._decorations.set(this._textModel.deltaDecorations(this._decorations.get(), range ? [{ range, options: { description: 'trackedRange' } }] : []), tx);\n    }\n}\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA,IAAIA,UAAU,GAAI,IAAI,IAAI,IAAI,CAACA,UAAU,IAAK,UAAUC,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAE;EACnF,IAAIC,CAAC,GAAGC,SAAS,CAACC,MAAM;IAAEC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGH,MAAM,GAAGE,IAAI,KAAK,IAAI,GAAGA,IAAI,GAAGK,MAAM,CAACC,wBAAwB,CAACR,MAAM,EAAEC,GAAG,CAAC,GAAGC,IAAI;IAAEO,CAAC;EAC5H,IAAI,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,CAACC,QAAQ,KAAK,UAAU,EAAEL,CAAC,GAAGI,OAAO,CAACC,QAAQ,CAACZ,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,CAAC,CAAC,KAC1H,KAAK,IAAIU,CAAC,GAAGb,UAAU,CAACM,MAAM,GAAG,CAAC,EAAEO,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE,IAAIH,CAAC,GAAGV,UAAU,CAACa,CAAC,CAAC,EAAEN,CAAC,GAAG,CAACH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACH,CAAC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACT,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,GAAGG,CAAC,CAACT,MAAM,EAAEC,GAAG,CAAC,KAAKK,CAAC;EACjJ,OAAOH,CAAC,GAAG,CAAC,IAAIG,CAAC,IAAIC,MAAM,CAACM,cAAc,CAACb,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,EAAEA,CAAC;AACjE,CAAC;AACD,IAAIQ,OAAO,GAAI,IAAI,IAAI,IAAI,CAACA,OAAO,IAAK,UAAUC,UAAU,EAAEC,SAAS,EAAE;EACrE,OAAO,UAAUhB,MAAM,EAAEC,GAAG,EAAE;IAAEe,SAAS,CAAChB,MAAM,EAAEC,GAAG,EAAEc,UAAU,CAAC;EAAE,CAAC;AACzE,CAAC;AACD,IAAIE,kBAAkB;AACtB,SAASC,OAAO,QAAQ,kCAAkC;AAC1D,SAASC,iBAAiB,EAAEC,eAAe,QAAQ,yCAAyC;AAC5F,SAASC,WAAW,EAAEC,gBAAgB,QAAQ,mCAAmC;AACjF,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,UAAU,EAAEC,eAAe,EAAEC,YAAY,QAAQ,sCAAsC;AAChG,SAASC,iBAAiB,EAAEC,OAAO,EAAEC,oBAAoB,EAAEC,WAAW,EAAEC,yBAAyB,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,6BAA6B,EAAEC,cAAc,QAAQ,uCAAuC;AAClO,SAASC,iBAAiB,QAAQ,uDAAuD;AACzF,SAASC,GAAG,QAAQ,gCAAgC;AACpD,SAASC,2BAA2B,QAAQ,kEAAkE;AAC9G,SAASC,SAAS,QAAQ,mCAAmC;AAC7D,SAASC,2BAA2B,QAAQ,8BAA8B;AAC1E,SAASC,wBAAwB,QAAQ,8CAA8C;AACvF,SAASC,aAAa,QAAQ,mCAAmC;AACjE,SAASC,wBAAwB,QAAQ,mEAAmE;AAC5G,SAASC,UAAU,QAAQ,wBAAwB;AACnD,IAAIC,gBAAgB;EAAA,IAAhBA,gBAAgB,GAAG,MAAMA,gBAAgB,SAASrB,UAAU,CAAC;IAC7D;MAASP,kBAAkB,GAAG,IAAI;IAAE;IACpC;MAAS,IAAI,CAAC6B,QAAQ,GAAG,CAAC;IAAE;IAC5B,OAAOC,gBAAgBA,CAAA,EAAG;MACtB,OAAOV,GAAG,CAACW,IAAI,CAAC;QAAEC,MAAM,EAAE,cAAc;QAAEC,IAAI,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,GAAGC,MAAM,CAACpC,kBAAkB,CAAC6B,QAAQ,EAAE;MAAE,CAAC,CAAC;IACpH;IACAQ,WAAWA,CAACC,SAAS,EAAEC,mBAAmB,EAAEC,UAAU,EAAEC,cAAc,EAAEC,uBAAuB,EAAEC,2BAA2B,EAAEC,aAAa,EAAE;MAAA,IAAAC,KAAA;MACzI,KAAK,CAAC,CAAC;MAAAA,KAAA,GAAAC,IAAA;MACP,IAAI,CAACR,SAAS,GAAGA,SAAS;MAC1B,IAAI,CAACC,mBAAmB,GAAGA,mBAAmB;MAC9C,IAAI,CAACC,UAAU,GAAGA,UAAU;MAC5B,IAAI,CAACC,cAAc,GAAGA,cAAc;MACpC,IAAI,CAACC,uBAAuB,GAAGA,uBAAuB;MACtD,IAAI,CAACC,2BAA2B,GAAGA,2BAA2B;MAC9D,IAAI,CAACC,aAAa,GAAGA,aAAa;MAClC,IAAI,CAACG,4BAA4B,GAAGhC,gBAAgB,CAAC,IAAI,CAAC;MAC1D;MACA,IAAI,CAACiC,2BAA2B,GAAGhC,eAAe,CAAC,IAAI,EAAEiC,SAAS,CAAC;MACnE,IAAI,CAACC,SAAS,GAAGlC,eAAe,CAAC,IAAI,EAAE,KAAK,CAAC;MAC7C,IAAI,CAACmC,cAAc,GAAGhC,iBAAiB,CAAC,MAAM,IAAI,CAACyB,aAAa,CAACQ,WAAW,CAAC,EAAE,EAAE,IAAI,EAAEpD,kBAAkB,CAAC8B,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAACuB,YAAY,CAAC,IAAI,CAACC,MAAM,CAAC;MACxJ,IAAI,CAACC,cAAc,GAAGpC,iBAAiB,CAAC,MAAM,IAAI,CAACyB,aAAa,CAACQ,WAAW,CAAC,EAAE,EAAE,IAAI,EAAEpD,kBAAkB,CAAC8B,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAACuB,YAAY,CAAC,IAAI,CAACC,MAAM,CAAC;MACxJ,IAAI,CAACE,YAAY,GAAG,IAAIC,YAAY,CAAC,IAAI,CAACnB,SAAS,EAAE,IAAI,CAACC,mBAAmB,CAAC;MAC9E,IAAI,CAACmB,QAAQ,GAAG,IAAI,CAACF,YAAY,CAACG,KAAK,CAACC,GAAG,CAACD,KAAK,IAAI,CAAC,CAACA,KAAK,CAAC;MAC7D,IAAI,CAACE,UAAU,GAAG7C,eAAe,CAAC,IAAI,EAAEiC,SAAS,CAAC;MAClD,IAAI,CAACa,UAAU,GAAGnD,OAAO,CAAC,IAAI,EAAEoD,MAAM,IAAI;QACtC,OAAO,IAAI,CAACC,WAAW,CAACC,IAAI,CAACF,MAAM,CAAC,EAAEG,aAAa,CAACD,IAAI,CAACF,MAAM,CAAC,EAAEI,IAAI;MAC1E,CAAC,CAAC;MACF,IAAI,CAACH,WAAW,GAAGrD,OAAO,CAAC,IAAI,EAAEoD,MAAM,IAAI;QACvC,MAAMK,IAAI,GAAG,IAAI,CAACC,kBAAkB,CAACJ,IAAI,CAACF,MAAM,CAAC;QACjD,IAAI,CAACK,IAAI,EAAE;UACP,OAAOnB,SAAS;QACpB;QACA,MAAMU,KAAK,GAAGS,IAAI,CAACE,gBAAgB,CAACX,KAAK;QACzC,IAAIS,IAAI,CAACE,gBAAgB,CAACC,UAAU,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;UAChD,OAAOvB,SAAS;QACpB;QACA,IAAIwB,QAAQ,GAAGL,IAAI,CAACE,gBAAgB,CAACC,UAAU,CAACG,KAAK,CAAC,YAAY,CAAC;QACnE,SAASC,iBAAiBA,CAACC,KAAK,EAAE;UAC9B,MAAMC,WAAW,GAAGD,KAAK,CAAC,CAAC,CAAC,CAACE,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE;UACrD,OAAOF,KAAK,CAAChB,GAAG,CAACmB,CAAC,IAAIA,CAAC,CAACC,OAAO,CAAC,IAAIC,MAAM,CAAC,GAAG,GAAGJ,WAAW,CAAC,EAAE,EAAE,CAAC,CAAC;QACvE;QACAJ,QAAQ,GAAGE,iBAAiB,CAACF,QAAQ,CAAC;QACtC,MAAMS,QAAQ,GAAG,IAAI,CAAC5C,SAAS,CAAC6C,eAAe,CAACxB,KAAK,CAAC;QACtD,IAAIyB,aAAa,GAAGF,QAAQ,CAACR,KAAK,CAAC,YAAY,CAAC;QAChDU,aAAa,GAAGT,iBAAiB,CAACS,aAAa,CAAC;QAChD,IAAI,CAACjC,cAAc,CAACkC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAACF,aAAa,CAACG,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5D,IAAI,CAAChC,cAAc,CAAC8B,GAAG,CAAC,CAAC,CAACC,QAAQ,CAACb,QAAQ,CAACc,IAAI,CAAC,IAAI,CAAC,CAAC;QACvD,MAAM/F,CAAC,GAAG,IAAI,CAACmD,2BAA2B,CAAC6C,kBAAkB,CAAC;UAAEC,aAAa,EAAE;QAAW,CAAC,CAAC;QAC5F,OAAO/E,iBAAiB,CAACgF,MAAM,cAAAC,iBAAA,CAAC,aAAY;UACxC,MAAMC,MAAM,SAASpG,CAAC,CAACqG,WAAW,CAAChD,KAAI,CAACM,cAAc,CAACkC,GAAG,CAAC,CAAC,EAAExC,KAAI,CAACU,cAAc,CAAC8B,GAAG,CAAC,CAAC,EAAE;YACrFS,YAAY,EAAE,KAAK;YACnBC,oBAAoB,EAAE,KAAK;YAC3BC,oBAAoB,EAAE;UAC1B,CAAC,EAAE9F,iBAAiB,CAAC+F,IAAI,CAAC;UAC1B,IAAIL,MAAM,CAACM,SAAS,EAAE;YAClB,OAAOjD,SAAS;UACpB;UACA,OAAO,IAAItB,UAAU,CAACL,SAAS,CAAC6E,kBAAkB,CAACxC,KAAK,CAAC,EAAEgB,iBAAiB,CAACF,QAAQ,CAAC,EAAEmB,MAAM,CAACQ,OAAO,CAAC;QAC3G,CAAC,EAAC;MACN,CAAC,CAAC;MACF,IAAI,CAACC,WAAW,GAAG,IAAI,CAACC,SAAS,CAAC,IAAI9F,eAAe,CAAC,CAAC,CAAC;MACxD,IAAI,CAAC+F,uBAAuB,GAAGzF,yBAAyB,CAAC,IAAI,EAAEmC,SAAS,CAAC;MACzE,IAAI,CAACuD,YAAY,GAAG3F,WAAW,CAAC;QAAE4F,KAAK,EAAE,IAAI;QAAEC,QAAQ,EAAErG;MAAiB,CAAC,EAAE0D,MAAM,IAAI;QACnF,OAAO,IAAI,CAACwC,uBAAuB,CAACtC,IAAI,CAACF,MAAM,CAAC,EAAE4C,WAAW,CAAC/C,GAAG,CAAC1E,CAAC,IAAI,IAAI0H,cAAc,CAAC1H,CAAC,CAAC,CAAC,IAAI,EAAE;MACvG,CAAC,CAAC;MACF,IAAI,CAAC2H,wBAAwB,GAAGjG,oBAAoB,CAAC;QACjD6F,KAAK,EAAE,IAAI;QACXK,wBAAwB,EAAEA,CAAA,MAAO;UAC7BC,2BAA2B,EAAExF,2BAA2B,CAACyF;QAC7D,CAAC,CAAC;QACFC,YAAY,EAAEA,CAACC,GAAG,EAAEC,aAAa,KAAK;UAClC;UACA,IAAID,GAAG,CAACE,SAAS,CAAC,IAAI,CAACrE,4BAA4B,CAAC,EAAE;YAClDoE,aAAa,CAACJ,2BAA2B,GAAGxF,2BAA2B,CAAC8F,QAAQ;UACpF;UACA,OAAO,IAAI;QACf;MACJ,CAAC;QAAA,IAAAC,KAAA,GAAA3B,iBAAA,CAAE,WAAO5B,MAAM,EAAEoD,aAAa,EAAK;UAChCtE,KAAI,CAACwD,WAAW,CAACkB,KAAK,CAAC,CAAC;UACxB1E,KAAI,CAACE,4BAA4B,CAACkB,IAAI,CAACF,MAAM,CAAC;UAC9C;AACZ;AACA;UACYlB,KAAI,CAACN,mBAAmB,CAAC0B,IAAI,CAACF,MAAM,CAAC;UACrC,SAASyD,QAAQA,CAACC,KAAK,EAAEC,EAAE,EAAE;YACzB,OAAOA,EAAE,CAACD,KAAK,CAAC;UACpB;UACA,MAAME,SAAS,GAAG9E,KAAI,CAACW,YAAY,CAACG,KAAK,CAACM,IAAI,CAACF,MAAM,CAAC,IAAIyD,QAAQ,CAAC3E,KAAI,CAACL,UAAU,CAACyB,IAAI,CAACF,MAAM,CAAC,EAAE6D,CAAC,IAAIA,CAAC,CAACC,OAAO,CAAC,CAAC,GAAG5E,SAAS,GAAG2E,CAAC,CAAC;UAClI,IAAI,CAACD,SAAS,EAAE;YACZ9E,KAAI,CAAC0D,uBAAuB,CAACuB,GAAG,CAAC7E,SAAS,EAAEA,SAAS,CAAC;YACtDJ,KAAI,CAACgB,UAAU,CAACiE,GAAG,CAAC7E,SAAS,EAAEA,SAAS,CAAC;YACzC,OAAOA,SAAS;UACpB;UACA,MAAM8E,OAAO,GAAG;YACZC,WAAW,EAAEb,aAAa,CAACJ,2BAA2B;YACtDkB,sBAAsB,EAAEhF,SAAS;YACjCY,UAAU,EAAEhB,KAAI,CAACgB,UAAU,CAACI,IAAI,CAACF,MAAM;UAC3C,CAAC;UACD,MAAMmE,KAAK,GAAG/H,eAAe,CAAC0C,KAAI,CAACwD,WAAW,CAAC;UAC/C,MAAMpG,OAAO,CAAC,GAAG,EAAEiI,KAAK,CAAC;UACzB,MAAMtC,MAAM,SAASlE,wBAAwB,CAACmB,KAAI,CAACH,uBAAuB,CAACyF,yBAAyB,EAAER,SAAS,EAAE9E,KAAI,CAACP,SAAS,EAAEyF,OAAO,EAAEG,KAAK,CAAC;UAChJ,IAAIA,KAAK,CAACE,uBAAuB,EAAE;YAC/B;UACJ;UACAvF,KAAI,CAAC0D,uBAAuB,CAACuB,GAAG,CAAClC,MAAM,EAAE3C,SAAS,CAAC;QACvD,CAAC;QAAA,iBAAAoF,EAAA,EAAAC,GAAA;UAAA,OAAAhB,KAAA,CAAAiB,KAAA,OAAApJ,SAAA;QAAA;MAAA,IAAC;MACF,IAAI,CAACqJ,wBAAwB,GAAG3H,WAAW,CAAC;QAAE4F,KAAK,EAAE,IAAI;QAAEC,QAAQ,EAAEtG,WAAW,CAAC;MAAE,CAAC,EAAE2D,MAAM,IAAI;QAC5F,OAAO,IAAI,CAACyC,YAAY,CAACvC,IAAI,CAACF,MAAM,CAAC;MACzC,CAAC,CAAC;MACF,IAAI,CAAC0E,6BAA6B,GAAG9H,OAAO,CAAC,IAAI,EAAGoD,MAAM,IAAK;QAC3D,MAAM2E,0BAA0B,GAAG,IAAI,CAAC1F,2BAA2B,CAACiB,IAAI,CAACF,MAAM,CAAC;QAChF,MAAM4E,mBAAmB,GAAG,IAAI,CAACH,wBAAwB,CAACvE,IAAI,CAACF,MAAM,CAAC;QACtE,MAAM6E,GAAG,GAAG,IAAI,CAAC5F,2BAA2B,KAAKC,SAAS,GAAG,CAAC,CAAC,GACzD0F,mBAAmB,CAACE,SAAS,CAACjB,CAAC,IAAIA,CAAC,CAACkB,UAAU,KAAKJ,0BAA0B,CAAC;QACrF,IAAIE,GAAG,KAAK,CAAC,CAAC,EAAE;UACZ;UACA,IAAI,CAAC5F,2BAA2B,CAAC8E,GAAG,CAAC7E,SAAS,EAAEA,SAAS,CAAC;UAC1D,OAAO,CAAC;QACZ;QACA,OAAO2F,GAAG;MACd,CAAC,CAAC;MACF,IAAI,CAACvE,kBAAkB,GAAG1D,OAAO,CAAC,IAAI,EAAGoD,MAAM,IAAK;QAChD,MAAM4E,mBAAmB,GAAG,IAAI,CAACH,wBAAwB,CAACvE,IAAI,CAACF,MAAM,CAAC;QACtE,MAAM6E,GAAG,GAAG,IAAI,CAACH,6BAA6B,CAACxE,IAAI,CAACF,MAAM,CAAC;QAC3D,OAAO4E,mBAAmB,CAACC,GAAG,CAAC;MACnC,CAAC,CAAC;MACF,IAAI,CAACtC,SAAS,CAACrF,6BAA6B,CAAC,IAAI,CAAC4F,wBAAwB,CAAC,CAAC;IAChF;IACMkC,iBAAiBA,CAACC,EAAE,EAAE;MAAA,IAAAC,MAAA;MAAA,OAAAtD,iBAAA;QACxBzE,cAAc,CAAC8H,EAAE,EAAEA,EAAE,IAAI;UACrBC,MAAI,CAAC/F,SAAS,CAAC4E,GAAG,CAAC,IAAI,EAAEkB,EAAE,CAAC;UAC5BC,MAAI,CAAClG,4BAA4B,CAACmG,OAAO,CAACF,EAAE,CAAC;QACjD,CAAC,CAAC;QACF,MAAMC,MAAI,CAACpC,wBAAwB,CAACxB,GAAG,CAAC,CAAC;MAAC;IAC9C;IACA8D,IAAIA,CAACH,EAAE,EAAE;MACL9H,cAAc,CAAC8H,EAAE,EAAEA,EAAE,IAAI;QACrB,IAAI,CAACnF,UAAU,CAACiE,GAAG,CAAC7E,SAAS,EAAE+F,EAAE,CAAC;QAClC,IAAI,CAAC9F,SAAS,CAAC4E,GAAG,CAAC,KAAK,EAAEkB,EAAE,CAAC;QAC7B,IAAI,CAACzC,uBAAuB,CAACuB,GAAG,CAAC7E,SAAS,EAAE+F,EAAE,CAAC;QAC/C,IAAI,CAACxF,YAAY,CAAC4F,QAAQ,CAACnG,SAAS,EAAE+F,EAAE,CAAC;QACzC;MACJ,CAAC,CAAC;IACN;IACMK,mCAAmCA,CAACC,KAAK,EAAE;MAAA,IAAAC,MAAA;MAAA,OAAA5D,iBAAA;QAC7C,MAAM4D,MAAI,CAACR,iBAAiB,CAAC,CAAC;QAC9B,MAAMpC,WAAW,GAAG4C,MAAI,CAACf,wBAAwB,CAACnD,GAAG,CAAC,CAAC,IAAI,EAAE;QAC7D,IAAIsB,WAAW,CAACvH,MAAM,GAAG,CAAC,EAAE;UACxB,MAAMoK,MAAM,GAAG,CAACD,MAAI,CAACd,6BAA6B,CAACpD,GAAG,CAAC,CAAC,GAAGiE,KAAK,GAAG3C,WAAW,CAACvH,MAAM,IAAIuH,WAAW,CAACvH,MAAM;UAC3GmK,MAAI,CAACvG,2BAA2B,CAAC8E,GAAG,CAACnB,WAAW,CAAC6C,MAAM,CAAC,CAACV,UAAU,EAAE7F,SAAS,CAAC;QACnF,CAAC,MACI;UACDsG,MAAI,CAACvG,2BAA2B,CAAC8E,GAAG,CAAC7E,SAAS,EAAEA,SAAS,CAAC;QAC9D;MAAC;IACL;IACMwG,IAAIA,CAAA,EAAG;MAAA,IAAAC,MAAA;MAAA,OAAA/D,iBAAA;QACT,MAAM+D,MAAI,CAACL,mCAAmC,CAAC,CAAC,CAAC;MAAC;IACtD;IACMM,QAAQA,CAAA,EAAG;MAAA,IAAAC,MAAA;MAAA,OAAAjE,iBAAA;QACb,MAAMiE,MAAI,CAACP,mCAAmC,CAAC,CAAC,CAAC,CAAC;MAAC;IACvD;IACMQ,MAAMA,CAACC,MAAM,EAAE;MAAA,IAAAC,MAAA;MAAA,OAAApE,iBAAA;QACjB,IAAImE,MAAM,CAACE,QAAQ,CAAC,CAAC,KAAKD,MAAI,CAACzH,SAAS,EAAE;UACtC,MAAM,IAAIhC,kBAAkB,CAAC,CAAC;QAClC;QACA,MAAM8D,IAAI,GAAG2F,MAAI,CAAC1F,kBAAkB,CAACgB,GAAG,CAAC,CAAC;QAC1C,IAAI,CAACjB,IAAI,EAAE;UACP;QACJ;QACA0F,MAAM,CAACG,YAAY,CAAC,CAAC;QACrBH,MAAM,CAACI,YAAY,CAAC,yBAAyB,EAAE,CAC3C9F,IAAI,CAACE,gBAAgB,CAAC6F,gBAAgB,CAAC,CAAC,CAACC,qBAAqB,CAAC,CAAC,CACnE,CAAC;QACFL,MAAI,CAACZ,IAAI,CAAC,CAAC;MAAC;IAChB;EACJ,CAAC;EAAA,OA/KGvH,gBAAgB;AAAA,IA+KnB;AACDA,gBAAgB,GAAG5B,kBAAkB,GAAGnB,UAAU,CAAC,CAC/CgB,OAAO,CAAC,CAAC,EAAE2B,wBAAwB,CAAC,EACpC3B,OAAO,CAAC,CAAC,EAAEwB,2BAA2B,CAAC,EACvCxB,OAAO,CAAC,CAAC,EAAE4B,aAAa,CAAC,CAC5B,EAAEG,gBAAgB,CAAC;AACpB,SAASA,gBAAgB;AACzB,MAAMgF,cAAc,CAAC;EACjBvE,WAAWA,CAACiC,gBAAgB,EAAE;IAC1B,IAAI,CAACA,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACwE,UAAU,GAAG,IAAI,CAACxE,gBAAgB,CAAC+F,IAAI,CAAC,CAAC;EAClD;AACJ;AACA,MAAM5G,YAAY,SAASlD,UAAU,CAAC;EAClC8B,WAAWA,CAACiI,UAAU,EAAEC,UAAU,EAAE;IAChC,KAAK,CAAC,CAAC;IACP,IAAI,CAACD,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,YAAY,GAAGxJ,eAAe,CAAC,IAAI,EAAE,EAAE,CAAC;IAC7C,IAAI,CAAC2C,KAAK,GAAGhD,OAAO,CAAC,IAAI,EAAEoD,MAAM,IAAI;MACjC,IAAI,CAACwG,UAAU,CAACtG,IAAI,CAACF,MAAM,CAAC;MAC5B,MAAM0G,IAAI,GAAG,IAAI,CAACD,YAAY,CAACvG,IAAI,CAACF,MAAM,CAAC,CAAC,CAAC,CAAC;MAC9C,IAAI,CAAC0G,IAAI,EAAE;QACP,OAAO,IAAI;MACf;MACA,OAAO,IAAI,CAACH,UAAU,CAACI,kBAAkB,CAACD,IAAI,CAAC,IAAI,IAAI;IAC3D,CAAC,CAAC;IACF,IAAI,CAACnE,SAAS,CAAC7F,YAAY,CAAC,MAAM;MAC9B,IAAI,CAAC6J,UAAU,CAACK,gBAAgB,CAAC,IAAI,CAACH,YAAY,CAACnF,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC;IACjE,CAAC,CAAC,CAAC;EACP;EACA+D,QAAQA,CAACzF,KAAK,EAAEqF,EAAE,EAAE;IAChB,IAAI,CAACwB,YAAY,CAAC1C,GAAG,CAAC,IAAI,CAACwC,UAAU,CAACK,gBAAgB,CAAC,IAAI,CAACH,YAAY,CAACnF,GAAG,CAAC,CAAC,EAAE1B,KAAK,GAAG,CAAC;MAAEA,KAAK;MAAEiH,OAAO,EAAE;QAAEC,WAAW,EAAE;MAAe;IAAE,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE7B,EAAE,CAAC;EAC5J;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}