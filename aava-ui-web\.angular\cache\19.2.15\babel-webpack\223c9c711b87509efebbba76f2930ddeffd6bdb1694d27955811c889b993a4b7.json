{"ast": null, "code": "import _asyncToGenerator from \"C:/console/aava-ui-web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { RenderedHoverParts } from './hoverTypes.js';\nimport { Disposable, DisposableStore, toDisposable } from '../../../../base/common/lifecycle.js';\nimport { EditorHoverStatusBar } from './contentHoverStatusBar.js';\nimport { ModelDecorationOptions } from '../../../common/model/textModel.js';\nimport { Position } from '../../../common/core/position.js';\nimport { Range } from '../../../common/core/range.js';\nimport * as dom from '../../../../base/browser/dom.js';\nimport { MarkdownHoverParticipant } from './markdownHoverParticipant.js';\nimport { ColorHoverParticipant } from '../../colorPicker/browser/colorHoverParticipant.js';\nimport { InlayHintsHover } from '../../inlayHints/browser/inlayHintsHover.js';\nimport { BugIndicatingError } from '../../../../base/common/errors.js';\nexport class RenderedContentHover extends Disposable {\n  constructor(editor, hoverResult, participants, computer, context, keybindingService) {\n    super();\n    const anchor = hoverResult.anchor;\n    const parts = hoverResult.hoverParts;\n    this._renderedHoverParts = this._register(new RenderedContentHoverParts(editor, participants, parts, keybindingService, context));\n    const {\n      showAtPosition,\n      showAtSecondaryPosition\n    } = RenderedContentHover.computeHoverPositions(editor, anchor.range, parts);\n    this.shouldAppearBeforeContent = parts.some(m => m.isBeforeContent);\n    this.showAtPosition = showAtPosition;\n    this.showAtSecondaryPosition = showAtSecondaryPosition;\n    this.initialMousePosX = anchor.initialMousePosX;\n    this.initialMousePosY = anchor.initialMousePosY;\n    this.shouldFocus = computer.shouldFocus;\n    this.source = computer.source;\n  }\n  get domNode() {\n    return this._renderedHoverParts.domNode;\n  }\n  get domNodeHasChildren() {\n    return this._renderedHoverParts.domNodeHasChildren;\n  }\n  get focusedHoverPartIndex() {\n    return this._renderedHoverParts.focusedHoverPartIndex;\n  }\n  updateHoverVerbosityLevel(action, index, focus) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this._renderedHoverParts.updateHoverVerbosityLevel(action, index, focus);\n    })();\n  }\n  isColorPickerVisible() {\n    return this._renderedHoverParts.isColorPickerVisible();\n  }\n  static computeHoverPositions(editor, anchorRange, hoverParts) {\n    let startColumnBoundary = 1;\n    if (editor.hasModel()) {\n      // Ensure the range is on the current view line\n      const viewModel = editor._getViewModel();\n      const coordinatesConverter = viewModel.coordinatesConverter;\n      const anchorViewRange = coordinatesConverter.convertModelRangeToViewRange(anchorRange);\n      const anchorViewMinColumn = viewModel.getLineMinColumn(anchorViewRange.startLineNumber);\n      const anchorViewRangeStart = new Position(anchorViewRange.startLineNumber, anchorViewMinColumn);\n      startColumnBoundary = coordinatesConverter.convertViewPositionToModelPosition(anchorViewRangeStart).column;\n    }\n    // The anchor range is always on a single line\n    const anchorStartLineNumber = anchorRange.startLineNumber;\n    let secondaryPositionColumn = anchorRange.startColumn;\n    let forceShowAtRange;\n    for (const hoverPart of hoverParts) {\n      const hoverPartRange = hoverPart.range;\n      const hoverPartRangeOnAnchorStartLine = hoverPartRange.startLineNumber === anchorStartLineNumber;\n      const hoverPartRangeOnAnchorEndLine = hoverPartRange.endLineNumber === anchorStartLineNumber;\n      const hoverPartRangeIsOnAnchorLine = hoverPartRangeOnAnchorStartLine && hoverPartRangeOnAnchorEndLine;\n      if (hoverPartRangeIsOnAnchorLine) {\n        // this message has a range that is completely sitting on the line of the anchor\n        const hoverPartStartColumn = hoverPartRange.startColumn;\n        const minSecondaryPositionColumn = Math.min(secondaryPositionColumn, hoverPartStartColumn);\n        secondaryPositionColumn = Math.max(minSecondaryPositionColumn, startColumnBoundary);\n      }\n      if (hoverPart.forceShowAtRange) {\n        forceShowAtRange = hoverPartRange;\n      }\n    }\n    let showAtPosition;\n    let showAtSecondaryPosition;\n    if (forceShowAtRange) {\n      const forceShowAtPosition = forceShowAtRange.getStartPosition();\n      showAtPosition = forceShowAtPosition;\n      showAtSecondaryPosition = forceShowAtPosition;\n    } else {\n      showAtPosition = anchorRange.getStartPosition();\n      showAtSecondaryPosition = new Position(anchorStartLineNumber, secondaryPositionColumn);\n    }\n    return {\n      showAtPosition,\n      showAtSecondaryPosition\n    };\n  }\n}\nclass RenderedStatusBar {\n  constructor(fragment, _statusBar) {\n    this._statusBar = _statusBar;\n    fragment.appendChild(this._statusBar.hoverElement);\n  }\n  get hoverElement() {\n    return this._statusBar.hoverElement;\n  }\n  get actions() {\n    return this._statusBar.actions;\n  }\n  dispose() {\n    this._statusBar.dispose();\n  }\n}\nclass RenderedContentHoverParts extends Disposable {\n  static {\n    this._DECORATION_OPTIONS = ModelDecorationOptions.register({\n      description: 'content-hover-highlight',\n      className: 'hoverHighlight'\n    });\n  }\n  constructor(editor, participants, hoverParts, keybindingService, context) {\n    super();\n    this._renderedParts = [];\n    this._focusedHoverPartIndex = -1;\n    this._context = context;\n    this._fragment = document.createDocumentFragment();\n    this._register(this._renderParts(participants, hoverParts, context, keybindingService));\n    this._register(this._registerListenersOnRenderedParts());\n    this._register(this._createEditorDecorations(editor, hoverParts));\n    this._updateMarkdownAndColorParticipantInfo(participants);\n  }\n  _createEditorDecorations(editor, hoverParts) {\n    if (hoverParts.length === 0) {\n      return Disposable.None;\n    }\n    let highlightRange = hoverParts[0].range;\n    for (const hoverPart of hoverParts) {\n      const hoverPartRange = hoverPart.range;\n      highlightRange = Range.plusRange(highlightRange, hoverPartRange);\n    }\n    const highlightDecoration = editor.createDecorationsCollection();\n    highlightDecoration.set([{\n      range: highlightRange,\n      options: RenderedContentHoverParts._DECORATION_OPTIONS\n    }]);\n    return toDisposable(() => {\n      highlightDecoration.clear();\n    });\n  }\n  _renderParts(participants, hoverParts, hoverContext, keybindingService) {\n    const statusBar = new EditorHoverStatusBar(keybindingService);\n    const hoverRenderingContext = {\n      fragment: this._fragment,\n      statusBar,\n      ...hoverContext\n    };\n    const disposables = new DisposableStore();\n    for (const participant of participants) {\n      const renderedHoverParts = this._renderHoverPartsForParticipant(hoverParts, participant, hoverRenderingContext);\n      disposables.add(renderedHoverParts);\n      for (const renderedHoverPart of renderedHoverParts.renderedHoverParts) {\n        this._renderedParts.push({\n          type: 'hoverPart',\n          participant,\n          hoverPart: renderedHoverPart.hoverPart,\n          hoverElement: renderedHoverPart.hoverElement\n        });\n      }\n    }\n    const renderedStatusBar = this._renderStatusBar(this._fragment, statusBar);\n    if (renderedStatusBar) {\n      disposables.add(renderedStatusBar);\n      this._renderedParts.push({\n        type: 'statusBar',\n        hoverElement: renderedStatusBar.hoverElement,\n        actions: renderedStatusBar.actions\n      });\n    }\n    return toDisposable(() => {\n      disposables.dispose();\n    });\n  }\n  _renderHoverPartsForParticipant(hoverParts, participant, hoverRenderingContext) {\n    const hoverPartsForParticipant = hoverParts.filter(hoverPart => hoverPart.owner === participant);\n    const hasHoverPartsForParticipant = hoverPartsForParticipant.length > 0;\n    if (!hasHoverPartsForParticipant) {\n      return new RenderedHoverParts([]);\n    }\n    return participant.renderHoverParts(hoverRenderingContext, hoverPartsForParticipant);\n  }\n  _renderStatusBar(fragment, statusBar) {\n    if (!statusBar.hasContent) {\n      return undefined;\n    }\n    return new RenderedStatusBar(fragment, statusBar);\n  }\n  _registerListenersOnRenderedParts() {\n    const disposables = new DisposableStore();\n    this._renderedParts.forEach((renderedPart, index) => {\n      const element = renderedPart.hoverElement;\n      element.tabIndex = 0;\n      disposables.add(dom.addDisposableListener(element, dom.EventType.FOCUS_IN, event => {\n        event.stopPropagation();\n        this._focusedHoverPartIndex = index;\n      }));\n      disposables.add(dom.addDisposableListener(element, dom.EventType.FOCUS_OUT, event => {\n        event.stopPropagation();\n        this._focusedHoverPartIndex = -1;\n      }));\n    });\n    return disposables;\n  }\n  _updateMarkdownAndColorParticipantInfo(participants) {\n    const markdownHoverParticipant = participants.find(p => {\n      return p instanceof MarkdownHoverParticipant && !(p instanceof InlayHintsHover);\n    });\n    if (markdownHoverParticipant) {\n      this._markdownHoverParticipant = markdownHoverParticipant;\n    }\n    this._colorHoverParticipant = participants.find(p => p instanceof ColorHoverParticipant);\n  }\n  updateHoverVerbosityLevel(action, index, focus) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this2._markdownHoverParticipant) {\n        return;\n      }\n      const normalizedMarkdownHoverIndex = _this2._normalizedIndexToMarkdownHoverIndexRange(_this2._markdownHoverParticipant, index);\n      if (normalizedMarkdownHoverIndex === undefined) {\n        return;\n      }\n      const renderedPart = yield _this2._markdownHoverParticipant.updateMarkdownHoverVerbosityLevel(action, normalizedMarkdownHoverIndex, focus);\n      if (!renderedPart) {\n        return;\n      }\n      _this2._renderedParts[index] = {\n        type: 'hoverPart',\n        participant: _this2._markdownHoverParticipant,\n        hoverPart: renderedPart.hoverPart,\n        hoverElement: renderedPart.hoverElement\n      };\n      _this2._context.onContentsChanged();\n    })();\n  }\n  isColorPickerVisible() {\n    return this._colorHoverParticipant?.isColorPickerVisible() ?? false;\n  }\n  _normalizedIndexToMarkdownHoverIndexRange(markdownHoverParticipant, index) {\n    const renderedPart = this._renderedParts[index];\n    if (!renderedPart || renderedPart.type !== 'hoverPart') {\n      return undefined;\n    }\n    const isHoverPartMarkdownHover = renderedPart.participant === markdownHoverParticipant;\n    if (!isHoverPartMarkdownHover) {\n      return undefined;\n    }\n    const firstIndexOfMarkdownHovers = this._renderedParts.findIndex(renderedPart => renderedPart.type === 'hoverPart' && renderedPart.participant === markdownHoverParticipant);\n    if (firstIndexOfMarkdownHovers === -1) {\n      throw new BugIndicatingError();\n    }\n    return index - firstIndexOfMarkdownHovers;\n  }\n  get domNode() {\n    return this._fragment;\n  }\n  get domNodeHasChildren() {\n    return this._fragment.hasChildNodes();\n  }\n  get focusedHoverPartIndex() {\n    return this._focusedHoverPartIndex;\n  }\n}", "map": {"version": 3, "names": ["RenderedHoverParts", "Disposable", "DisposableStore", "toDisposable", "EditorHoverStatusBar", "ModelDecorationOptions", "Position", "Range", "dom", "MarkdownHoverParticipant", "ColorHoverParticipant", "InlayHintsHover", "BugIndicatingError", "RenderedContentHover", "constructor", "editor", "hoverResult", "participants", "computer", "context", "keybindingService", "anchor", "parts", "hoverParts", "_renderedHoverParts", "_register", "RenderedContentHoverParts", "showAtPosition", "showAtSecondaryPosition", "computeHoverPositions", "range", "should<PERSON><PERSON>arBeforeContent", "some", "m", "isBeforeContent", "initialMousePosX", "initialMousePosY", "shouldFocus", "source", "domNode", "domNodeHasChildren", "focusedHoverPartIndex", "updateHoverVerbosityLevel", "action", "index", "focus", "_this", "_asyncToGenerator", "isColorPickerVisible", "anchorRange", "startColumnBoundary", "hasModel", "viewModel", "_getViewModel", "coordinatesConverter", "anchorViewRange", "convertModelRangeToViewRange", "anchorViewMinColumn", "getLineMinColumn", "startLineNumber", "anchorViewRangeStart", "convertViewPositionToModelPosition", "column", "anchorStartLineNumber", "secondaryPositionColumn", "startColumn", "forceShowAtRange", "hoverPart", "hoverPartRange", "hoverPartRangeOnAnchorStartLine", "hoverPartRangeOnAnchorEndLine", "endLineNumber", "hoverPartRangeIsOnAnchorLine", "hoverPartStartColumn", "minSecondaryPositionColumn", "Math", "min", "max", "forceShowAtPosition", "getStartPosition", "RenderedStatusBar", "fragment", "_statusBar", "append<PERSON><PERSON><PERSON>", "hoverElement", "actions", "dispose", "_DECORATION_OPTIONS", "register", "description", "className", "_renderedParts", "_focusedHoverPartIndex", "_context", "_fragment", "document", "createDocumentFragment", "_renderParts", "_registerListenersOnRenderedParts", "_createEditorDecorations", "_updateMarkdownAndColorParticipantInfo", "length", "None", "highlightRange", "plusRange", "highlightDecoration", "createDecorationsCollection", "set", "options", "clear", "hoverContext", "statusBar", "hoverRenderingContext", "disposables", "participant", "renderedHoverParts", "_renderHoverPartsForParticipant", "add", "renderedHoverPart", "push", "type", "renderedStatusBar", "_renderStatusBar", "hoverPartsForParticipant", "filter", "owner", "hasHoverPartsForParticipant", "renderHoverParts", "<PERSON><PERSON><PERSON><PERSON>", "undefined", "for<PERSON>ach", "renderedPart", "element", "tabIndex", "addDisposableListener", "EventType", "FOCUS_IN", "event", "stopPropagation", "FOCUS_OUT", "markdownHoverParticipant", "find", "p", "_markdownHoverParticipant", "_colorHoverParticipant", "_this2", "normalizedMarkdownHoverIndex", "_normalizedIndexToMarkdownHoverIndexRange", "updateMarkdownHoverVerbosityLevel", "onContentsChanged", "isHoverPartMarkdownHover", "firstIndexOfMarkdownHovers", "findIndex", "hasChildNodes"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/contrib/hover/browser/contentHoverRendered.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { RenderedHoverParts } from './hoverTypes.js';\nimport { Disposable, DisposableStore, toDisposable } from '../../../../base/common/lifecycle.js';\nimport { EditorHoverStatusBar } from './contentHoverStatusBar.js';\nimport { ModelDecorationOptions } from '../../../common/model/textModel.js';\nimport { Position } from '../../../common/core/position.js';\nimport { Range } from '../../../common/core/range.js';\nimport * as dom from '../../../../base/browser/dom.js';\nimport { MarkdownHoverParticipant } from './markdownHoverParticipant.js';\nimport { ColorHoverParticipant } from '../../colorPicker/browser/colorHoverParticipant.js';\nimport { InlayHintsHover } from '../../inlayHints/browser/inlayHintsHover.js';\nimport { BugIndicatingError } from '../../../../base/common/errors.js';\nexport class RenderedContentHover extends Disposable {\n    constructor(editor, hoverResult, participants, computer, context, keybindingService) {\n        super();\n        const anchor = hoverResult.anchor;\n        const parts = hoverResult.hoverParts;\n        this._renderedHoverParts = this._register(new RenderedContentHoverParts(editor, participants, parts, keybindingService, context));\n        const { showAtPosition, showAtSecondaryPosition } = RenderedContentHover.computeHoverPositions(editor, anchor.range, parts);\n        this.shouldAppearBeforeContent = parts.some(m => m.isBeforeContent);\n        this.showAtPosition = showAtPosition;\n        this.showAtSecondaryPosition = showAtSecondaryPosition;\n        this.initialMousePosX = anchor.initialMousePosX;\n        this.initialMousePosY = anchor.initialMousePosY;\n        this.shouldFocus = computer.shouldFocus;\n        this.source = computer.source;\n    }\n    get domNode() {\n        return this._renderedHoverParts.domNode;\n    }\n    get domNodeHasChildren() {\n        return this._renderedHoverParts.domNodeHasChildren;\n    }\n    get focusedHoverPartIndex() {\n        return this._renderedHoverParts.focusedHoverPartIndex;\n    }\n    async updateHoverVerbosityLevel(action, index, focus) {\n        this._renderedHoverParts.updateHoverVerbosityLevel(action, index, focus);\n    }\n    isColorPickerVisible() {\n        return this._renderedHoverParts.isColorPickerVisible();\n    }\n    static computeHoverPositions(editor, anchorRange, hoverParts) {\n        let startColumnBoundary = 1;\n        if (editor.hasModel()) {\n            // Ensure the range is on the current view line\n            const viewModel = editor._getViewModel();\n            const coordinatesConverter = viewModel.coordinatesConverter;\n            const anchorViewRange = coordinatesConverter.convertModelRangeToViewRange(anchorRange);\n            const anchorViewMinColumn = viewModel.getLineMinColumn(anchorViewRange.startLineNumber);\n            const anchorViewRangeStart = new Position(anchorViewRange.startLineNumber, anchorViewMinColumn);\n            startColumnBoundary = coordinatesConverter.convertViewPositionToModelPosition(anchorViewRangeStart).column;\n        }\n        // The anchor range is always on a single line\n        const anchorStartLineNumber = anchorRange.startLineNumber;\n        let secondaryPositionColumn = anchorRange.startColumn;\n        let forceShowAtRange;\n        for (const hoverPart of hoverParts) {\n            const hoverPartRange = hoverPart.range;\n            const hoverPartRangeOnAnchorStartLine = hoverPartRange.startLineNumber === anchorStartLineNumber;\n            const hoverPartRangeOnAnchorEndLine = hoverPartRange.endLineNumber === anchorStartLineNumber;\n            const hoverPartRangeIsOnAnchorLine = hoverPartRangeOnAnchorStartLine && hoverPartRangeOnAnchorEndLine;\n            if (hoverPartRangeIsOnAnchorLine) {\n                // this message has a range that is completely sitting on the line of the anchor\n                const hoverPartStartColumn = hoverPartRange.startColumn;\n                const minSecondaryPositionColumn = Math.min(secondaryPositionColumn, hoverPartStartColumn);\n                secondaryPositionColumn = Math.max(minSecondaryPositionColumn, startColumnBoundary);\n            }\n            if (hoverPart.forceShowAtRange) {\n                forceShowAtRange = hoverPartRange;\n            }\n        }\n        let showAtPosition;\n        let showAtSecondaryPosition;\n        if (forceShowAtRange) {\n            const forceShowAtPosition = forceShowAtRange.getStartPosition();\n            showAtPosition = forceShowAtPosition;\n            showAtSecondaryPosition = forceShowAtPosition;\n        }\n        else {\n            showAtPosition = anchorRange.getStartPosition();\n            showAtSecondaryPosition = new Position(anchorStartLineNumber, secondaryPositionColumn);\n        }\n        return {\n            showAtPosition,\n            showAtSecondaryPosition,\n        };\n    }\n}\nclass RenderedStatusBar {\n    constructor(fragment, _statusBar) {\n        this._statusBar = _statusBar;\n        fragment.appendChild(this._statusBar.hoverElement);\n    }\n    get hoverElement() {\n        return this._statusBar.hoverElement;\n    }\n    get actions() {\n        return this._statusBar.actions;\n    }\n    dispose() {\n        this._statusBar.dispose();\n    }\n}\nclass RenderedContentHoverParts extends Disposable {\n    static { this._DECORATION_OPTIONS = ModelDecorationOptions.register({\n        description: 'content-hover-highlight',\n        className: 'hoverHighlight'\n    }); }\n    constructor(editor, participants, hoverParts, keybindingService, context) {\n        super();\n        this._renderedParts = [];\n        this._focusedHoverPartIndex = -1;\n        this._context = context;\n        this._fragment = document.createDocumentFragment();\n        this._register(this._renderParts(participants, hoverParts, context, keybindingService));\n        this._register(this._registerListenersOnRenderedParts());\n        this._register(this._createEditorDecorations(editor, hoverParts));\n        this._updateMarkdownAndColorParticipantInfo(participants);\n    }\n    _createEditorDecorations(editor, hoverParts) {\n        if (hoverParts.length === 0) {\n            return Disposable.None;\n        }\n        let highlightRange = hoverParts[0].range;\n        for (const hoverPart of hoverParts) {\n            const hoverPartRange = hoverPart.range;\n            highlightRange = Range.plusRange(highlightRange, hoverPartRange);\n        }\n        const highlightDecoration = editor.createDecorationsCollection();\n        highlightDecoration.set([{\n                range: highlightRange,\n                options: RenderedContentHoverParts._DECORATION_OPTIONS\n            }]);\n        return toDisposable(() => {\n            highlightDecoration.clear();\n        });\n    }\n    _renderParts(participants, hoverParts, hoverContext, keybindingService) {\n        const statusBar = new EditorHoverStatusBar(keybindingService);\n        const hoverRenderingContext = {\n            fragment: this._fragment,\n            statusBar,\n            ...hoverContext\n        };\n        const disposables = new DisposableStore();\n        for (const participant of participants) {\n            const renderedHoverParts = this._renderHoverPartsForParticipant(hoverParts, participant, hoverRenderingContext);\n            disposables.add(renderedHoverParts);\n            for (const renderedHoverPart of renderedHoverParts.renderedHoverParts) {\n                this._renderedParts.push({\n                    type: 'hoverPart',\n                    participant,\n                    hoverPart: renderedHoverPart.hoverPart,\n                    hoverElement: renderedHoverPart.hoverElement,\n                });\n            }\n        }\n        const renderedStatusBar = this._renderStatusBar(this._fragment, statusBar);\n        if (renderedStatusBar) {\n            disposables.add(renderedStatusBar);\n            this._renderedParts.push({\n                type: 'statusBar',\n                hoverElement: renderedStatusBar.hoverElement,\n                actions: renderedStatusBar.actions,\n            });\n        }\n        return toDisposable(() => { disposables.dispose(); });\n    }\n    _renderHoverPartsForParticipant(hoverParts, participant, hoverRenderingContext) {\n        const hoverPartsForParticipant = hoverParts.filter(hoverPart => hoverPart.owner === participant);\n        const hasHoverPartsForParticipant = hoverPartsForParticipant.length > 0;\n        if (!hasHoverPartsForParticipant) {\n            return new RenderedHoverParts([]);\n        }\n        return participant.renderHoverParts(hoverRenderingContext, hoverPartsForParticipant);\n    }\n    _renderStatusBar(fragment, statusBar) {\n        if (!statusBar.hasContent) {\n            return undefined;\n        }\n        return new RenderedStatusBar(fragment, statusBar);\n    }\n    _registerListenersOnRenderedParts() {\n        const disposables = new DisposableStore();\n        this._renderedParts.forEach((renderedPart, index) => {\n            const element = renderedPart.hoverElement;\n            element.tabIndex = 0;\n            disposables.add(dom.addDisposableListener(element, dom.EventType.FOCUS_IN, (event) => {\n                event.stopPropagation();\n                this._focusedHoverPartIndex = index;\n            }));\n            disposables.add(dom.addDisposableListener(element, dom.EventType.FOCUS_OUT, (event) => {\n                event.stopPropagation();\n                this._focusedHoverPartIndex = -1;\n            }));\n        });\n        return disposables;\n    }\n    _updateMarkdownAndColorParticipantInfo(participants) {\n        const markdownHoverParticipant = participants.find(p => {\n            return (p instanceof MarkdownHoverParticipant) && !(p instanceof InlayHintsHover);\n        });\n        if (markdownHoverParticipant) {\n            this._markdownHoverParticipant = markdownHoverParticipant;\n        }\n        this._colorHoverParticipant = participants.find(p => p instanceof ColorHoverParticipant);\n    }\n    async updateHoverVerbosityLevel(action, index, focus) {\n        if (!this._markdownHoverParticipant) {\n            return;\n        }\n        const normalizedMarkdownHoverIndex = this._normalizedIndexToMarkdownHoverIndexRange(this._markdownHoverParticipant, index);\n        if (normalizedMarkdownHoverIndex === undefined) {\n            return;\n        }\n        const renderedPart = await this._markdownHoverParticipant.updateMarkdownHoverVerbosityLevel(action, normalizedMarkdownHoverIndex, focus);\n        if (!renderedPart) {\n            return;\n        }\n        this._renderedParts[index] = {\n            type: 'hoverPart',\n            participant: this._markdownHoverParticipant,\n            hoverPart: renderedPart.hoverPart,\n            hoverElement: renderedPart.hoverElement,\n        };\n        this._context.onContentsChanged();\n    }\n    isColorPickerVisible() {\n        return this._colorHoverParticipant?.isColorPickerVisible() ?? false;\n    }\n    _normalizedIndexToMarkdownHoverIndexRange(markdownHoverParticipant, index) {\n        const renderedPart = this._renderedParts[index];\n        if (!renderedPart || renderedPart.type !== 'hoverPart') {\n            return undefined;\n        }\n        const isHoverPartMarkdownHover = renderedPart.participant === markdownHoverParticipant;\n        if (!isHoverPartMarkdownHover) {\n            return undefined;\n        }\n        const firstIndexOfMarkdownHovers = this._renderedParts.findIndex(renderedPart => renderedPart.type === 'hoverPart'\n            && renderedPart.participant === markdownHoverParticipant);\n        if (firstIndexOfMarkdownHovers === -1) {\n            throw new BugIndicatingError();\n        }\n        return index - firstIndexOfMarkdownHovers;\n    }\n    get domNode() {\n        return this._fragment;\n    }\n    get domNodeHasChildren() {\n        return this._fragment.hasChildNodes();\n    }\n    get focusedHoverPartIndex() {\n        return this._focusedHoverPartIndex;\n    }\n}\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA,SAASA,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,UAAU,EAAEC,eAAe,EAAEC,YAAY,QAAQ,sCAAsC;AAChG,SAASC,oBAAoB,QAAQ,4BAA4B;AACjE,SAASC,sBAAsB,QAAQ,oCAAoC;AAC3E,SAASC,QAAQ,QAAQ,kCAAkC;AAC3D,SAASC,KAAK,QAAQ,+BAA+B;AACrD,OAAO,KAAKC,GAAG,MAAM,iCAAiC;AACtD,SAASC,wBAAwB,QAAQ,+BAA+B;AACxE,SAASC,qBAAqB,QAAQ,oDAAoD;AAC1F,SAASC,eAAe,QAAQ,6CAA6C;AAC7E,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,OAAO,MAAMC,oBAAoB,SAASZ,UAAU,CAAC;EACjDa,WAAWA,CAACC,MAAM,EAAEC,WAAW,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,iBAAiB,EAAE;IACjF,KAAK,CAAC,CAAC;IACP,MAAMC,MAAM,GAAGL,WAAW,CAACK,MAAM;IACjC,MAAMC,KAAK,GAAGN,WAAW,CAACO,UAAU;IACpC,IAAI,CAACC,mBAAmB,GAAG,IAAI,CAACC,SAAS,CAAC,IAAIC,yBAAyB,CAACX,MAAM,EAAEE,YAAY,EAAEK,KAAK,EAAEF,iBAAiB,EAAED,OAAO,CAAC,CAAC;IACjI,MAAM;MAAEQ,cAAc;MAAEC;IAAwB,CAAC,GAAGf,oBAAoB,CAACgB,qBAAqB,CAACd,MAAM,EAAEM,MAAM,CAACS,KAAK,EAAER,KAAK,CAAC;IAC3H,IAAI,CAACS,yBAAyB,GAAGT,KAAK,CAACU,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,eAAe,CAAC;IACnE,IAAI,CAACP,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,uBAAuB,GAAGA,uBAAuB;IACtD,IAAI,CAACO,gBAAgB,GAAGd,MAAM,CAACc,gBAAgB;IAC/C,IAAI,CAACC,gBAAgB,GAAGf,MAAM,CAACe,gBAAgB;IAC/C,IAAI,CAACC,WAAW,GAAGnB,QAAQ,CAACmB,WAAW;IACvC,IAAI,CAACC,MAAM,GAAGpB,QAAQ,CAACoB,MAAM;EACjC;EACA,IAAIC,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACf,mBAAmB,CAACe,OAAO;EAC3C;EACA,IAAIC,kBAAkBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAAChB,mBAAmB,CAACgB,kBAAkB;EACtD;EACA,IAAIC,qBAAqBA,CAAA,EAAG;IACxB,OAAO,IAAI,CAACjB,mBAAmB,CAACiB,qBAAqB;EACzD;EACMC,yBAAyBA,CAACC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAE;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAClDD,KAAI,CAACtB,mBAAmB,CAACkB,yBAAyB,CAACC,MAAM,EAAEC,KAAK,EAAEC,KAAK,CAAC;IAAC;EAC7E;EACAG,oBAAoBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACxB,mBAAmB,CAACwB,oBAAoB,CAAC,CAAC;EAC1D;EACA,OAAOnB,qBAAqBA,CAACd,MAAM,EAAEkC,WAAW,EAAE1B,UAAU,EAAE;IAC1D,IAAI2B,mBAAmB,GAAG,CAAC;IAC3B,IAAInC,MAAM,CAACoC,QAAQ,CAAC,CAAC,EAAE;MACnB;MACA,MAAMC,SAAS,GAAGrC,MAAM,CAACsC,aAAa,CAAC,CAAC;MACxC,MAAMC,oBAAoB,GAAGF,SAAS,CAACE,oBAAoB;MAC3D,MAAMC,eAAe,GAAGD,oBAAoB,CAACE,4BAA4B,CAACP,WAAW,CAAC;MACtF,MAAMQ,mBAAmB,GAAGL,SAAS,CAACM,gBAAgB,CAACH,eAAe,CAACI,eAAe,CAAC;MACvF,MAAMC,oBAAoB,GAAG,IAAItD,QAAQ,CAACiD,eAAe,CAACI,eAAe,EAAEF,mBAAmB,CAAC;MAC/FP,mBAAmB,GAAGI,oBAAoB,CAACO,kCAAkC,CAACD,oBAAoB,CAAC,CAACE,MAAM;IAC9G;IACA;IACA,MAAMC,qBAAqB,GAAGd,WAAW,CAACU,eAAe;IACzD,IAAIK,uBAAuB,GAAGf,WAAW,CAACgB,WAAW;IACrD,IAAIC,gBAAgB;IACpB,KAAK,MAAMC,SAAS,IAAI5C,UAAU,EAAE;MAChC,MAAM6C,cAAc,GAAGD,SAAS,CAACrC,KAAK;MACtC,MAAMuC,+BAA+B,GAAGD,cAAc,CAACT,eAAe,KAAKI,qBAAqB;MAChG,MAAMO,6BAA6B,GAAGF,cAAc,CAACG,aAAa,KAAKR,qBAAqB;MAC5F,MAAMS,4BAA4B,GAAGH,+BAA+B,IAAIC,6BAA6B;MACrG,IAAIE,4BAA4B,EAAE;QAC9B;QACA,MAAMC,oBAAoB,GAAGL,cAAc,CAACH,WAAW;QACvD,MAAMS,0BAA0B,GAAGC,IAAI,CAACC,GAAG,CAACZ,uBAAuB,EAAES,oBAAoB,CAAC;QAC1FT,uBAAuB,GAAGW,IAAI,CAACE,GAAG,CAACH,0BAA0B,EAAExB,mBAAmB,CAAC;MACvF;MACA,IAAIiB,SAAS,CAACD,gBAAgB,EAAE;QAC5BA,gBAAgB,GAAGE,cAAc;MACrC;IACJ;IACA,IAAIzC,cAAc;IAClB,IAAIC,uBAAuB;IAC3B,IAAIsC,gBAAgB,EAAE;MAClB,MAAMY,mBAAmB,GAAGZ,gBAAgB,CAACa,gBAAgB,CAAC,CAAC;MAC/DpD,cAAc,GAAGmD,mBAAmB;MACpClD,uBAAuB,GAAGkD,mBAAmB;IACjD,CAAC,MACI;MACDnD,cAAc,GAAGsB,WAAW,CAAC8B,gBAAgB,CAAC,CAAC;MAC/CnD,uBAAuB,GAAG,IAAItB,QAAQ,CAACyD,qBAAqB,EAAEC,uBAAuB,CAAC;IAC1F;IACA,OAAO;MACHrC,cAAc;MACdC;IACJ,CAAC;EACL;AACJ;AACA,MAAMoD,iBAAiB,CAAC;EACpBlE,WAAWA,CAACmE,QAAQ,EAAEC,UAAU,EAAE;IAC9B,IAAI,CAACA,UAAU,GAAGA,UAAU;IAC5BD,QAAQ,CAACE,WAAW,CAAC,IAAI,CAACD,UAAU,CAACE,YAAY,CAAC;EACtD;EACA,IAAIA,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACF,UAAU,CAACE,YAAY;EACvC;EACA,IAAIC,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACH,UAAU,CAACG,OAAO;EAClC;EACAC,OAAOA,CAAA,EAAG;IACN,IAAI,CAACJ,UAAU,CAACI,OAAO,CAAC,CAAC;EAC7B;AACJ;AACA,MAAM5D,yBAAyB,SAASzB,UAAU,CAAC;EAC/C;IAAS,IAAI,CAACsF,mBAAmB,GAAGlF,sBAAsB,CAACmF,QAAQ,CAAC;MAChEC,WAAW,EAAE,yBAAyB;MACtCC,SAAS,EAAE;IACf,CAAC,CAAC;EAAE;EACJ5E,WAAWA,CAACC,MAAM,EAAEE,YAAY,EAAEM,UAAU,EAAEH,iBAAiB,EAAED,OAAO,EAAE;IACtE,KAAK,CAAC,CAAC;IACP,IAAI,CAACwE,cAAc,GAAG,EAAE;IACxB,IAAI,CAACC,sBAAsB,GAAG,CAAC,CAAC;IAChC,IAAI,CAACC,QAAQ,GAAG1E,OAAO;IACvB,IAAI,CAAC2E,SAAS,GAAGC,QAAQ,CAACC,sBAAsB,CAAC,CAAC;IAClD,IAAI,CAACvE,SAAS,CAAC,IAAI,CAACwE,YAAY,CAAChF,YAAY,EAAEM,UAAU,EAAEJ,OAAO,EAAEC,iBAAiB,CAAC,CAAC;IACvF,IAAI,CAACK,SAAS,CAAC,IAAI,CAACyE,iCAAiC,CAAC,CAAC,CAAC;IACxD,IAAI,CAACzE,SAAS,CAAC,IAAI,CAAC0E,wBAAwB,CAACpF,MAAM,EAAEQ,UAAU,CAAC,CAAC;IACjE,IAAI,CAAC6E,sCAAsC,CAACnF,YAAY,CAAC;EAC7D;EACAkF,wBAAwBA,CAACpF,MAAM,EAAEQ,UAAU,EAAE;IACzC,IAAIA,UAAU,CAAC8E,MAAM,KAAK,CAAC,EAAE;MACzB,OAAOpG,UAAU,CAACqG,IAAI;IAC1B;IACA,IAAIC,cAAc,GAAGhF,UAAU,CAAC,CAAC,CAAC,CAACO,KAAK;IACxC,KAAK,MAAMqC,SAAS,IAAI5C,UAAU,EAAE;MAChC,MAAM6C,cAAc,GAAGD,SAAS,CAACrC,KAAK;MACtCyE,cAAc,GAAGhG,KAAK,CAACiG,SAAS,CAACD,cAAc,EAAEnC,cAAc,CAAC;IACpE;IACA,MAAMqC,mBAAmB,GAAG1F,MAAM,CAAC2F,2BAA2B,CAAC,CAAC;IAChED,mBAAmB,CAACE,GAAG,CAAC,CAAC;MACjB7E,KAAK,EAAEyE,cAAc;MACrBK,OAAO,EAAElF,yBAAyB,CAAC6D;IACvC,CAAC,CAAC,CAAC;IACP,OAAOpF,YAAY,CAAC,MAAM;MACtBsG,mBAAmB,CAACI,KAAK,CAAC,CAAC;IAC/B,CAAC,CAAC;EACN;EACAZ,YAAYA,CAAChF,YAAY,EAAEM,UAAU,EAAEuF,YAAY,EAAE1F,iBAAiB,EAAE;IACpE,MAAM2F,SAAS,GAAG,IAAI3G,oBAAoB,CAACgB,iBAAiB,CAAC;IAC7D,MAAM4F,qBAAqB,GAAG;MAC1B/B,QAAQ,EAAE,IAAI,CAACa,SAAS;MACxBiB,SAAS;MACT,GAAGD;IACP,CAAC;IACD,MAAMG,WAAW,GAAG,IAAI/G,eAAe,CAAC,CAAC;IACzC,KAAK,MAAMgH,WAAW,IAAIjG,YAAY,EAAE;MACpC,MAAMkG,kBAAkB,GAAG,IAAI,CAACC,+BAA+B,CAAC7F,UAAU,EAAE2F,WAAW,EAAEF,qBAAqB,CAAC;MAC/GC,WAAW,CAACI,GAAG,CAACF,kBAAkB,CAAC;MACnC,KAAK,MAAMG,iBAAiB,IAAIH,kBAAkB,CAACA,kBAAkB,EAAE;QACnE,IAAI,CAACxB,cAAc,CAAC4B,IAAI,CAAC;UACrBC,IAAI,EAAE,WAAW;UACjBN,WAAW;UACX/C,SAAS,EAAEmD,iBAAiB,CAACnD,SAAS;UACtCiB,YAAY,EAAEkC,iBAAiB,CAAClC;QACpC,CAAC,CAAC;MACN;IACJ;IACA,MAAMqC,iBAAiB,GAAG,IAAI,CAACC,gBAAgB,CAAC,IAAI,CAAC5B,SAAS,EAAEiB,SAAS,CAAC;IAC1E,IAAIU,iBAAiB,EAAE;MACnBR,WAAW,CAACI,GAAG,CAACI,iBAAiB,CAAC;MAClC,IAAI,CAAC9B,cAAc,CAAC4B,IAAI,CAAC;QACrBC,IAAI,EAAE,WAAW;QACjBpC,YAAY,EAAEqC,iBAAiB,CAACrC,YAAY;QAC5CC,OAAO,EAAEoC,iBAAiB,CAACpC;MAC/B,CAAC,CAAC;IACN;IACA,OAAOlF,YAAY,CAAC,MAAM;MAAE8G,WAAW,CAAC3B,OAAO,CAAC,CAAC;IAAE,CAAC,CAAC;EACzD;EACA8B,+BAA+BA,CAAC7F,UAAU,EAAE2F,WAAW,EAAEF,qBAAqB,EAAE;IAC5E,MAAMW,wBAAwB,GAAGpG,UAAU,CAACqG,MAAM,CAACzD,SAAS,IAAIA,SAAS,CAAC0D,KAAK,KAAKX,WAAW,CAAC;IAChG,MAAMY,2BAA2B,GAAGH,wBAAwB,CAACtB,MAAM,GAAG,CAAC;IACvE,IAAI,CAACyB,2BAA2B,EAAE;MAC9B,OAAO,IAAI9H,kBAAkB,CAAC,EAAE,CAAC;IACrC;IACA,OAAOkH,WAAW,CAACa,gBAAgB,CAACf,qBAAqB,EAAEW,wBAAwB,CAAC;EACxF;EACAD,gBAAgBA,CAACzC,QAAQ,EAAE8B,SAAS,EAAE;IAClC,IAAI,CAACA,SAAS,CAACiB,UAAU,EAAE;MACvB,OAAOC,SAAS;IACpB;IACA,OAAO,IAAIjD,iBAAiB,CAACC,QAAQ,EAAE8B,SAAS,CAAC;EACrD;EACAb,iCAAiCA,CAAA,EAAG;IAChC,MAAMe,WAAW,GAAG,IAAI/G,eAAe,CAAC,CAAC;IACzC,IAAI,CAACyF,cAAc,CAACuC,OAAO,CAAC,CAACC,YAAY,EAAEvF,KAAK,KAAK;MACjD,MAAMwF,OAAO,GAAGD,YAAY,CAAC/C,YAAY;MACzCgD,OAAO,CAACC,QAAQ,GAAG,CAAC;MACpBpB,WAAW,CAACI,GAAG,CAAC7G,GAAG,CAAC8H,qBAAqB,CAACF,OAAO,EAAE5H,GAAG,CAAC+H,SAAS,CAACC,QAAQ,EAAGC,KAAK,IAAK;QAClFA,KAAK,CAACC,eAAe,CAAC,CAAC;QACvB,IAAI,CAAC9C,sBAAsB,GAAGhD,KAAK;MACvC,CAAC,CAAC,CAAC;MACHqE,WAAW,CAACI,GAAG,CAAC7G,GAAG,CAAC8H,qBAAqB,CAACF,OAAO,EAAE5H,GAAG,CAAC+H,SAAS,CAACI,SAAS,EAAGF,KAAK,IAAK;QACnFA,KAAK,CAACC,eAAe,CAAC,CAAC;QACvB,IAAI,CAAC9C,sBAAsB,GAAG,CAAC,CAAC;MACpC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC;IACF,OAAOqB,WAAW;EACtB;EACAb,sCAAsCA,CAACnF,YAAY,EAAE;IACjD,MAAM2H,wBAAwB,GAAG3H,YAAY,CAAC4H,IAAI,CAACC,CAAC,IAAI;MACpD,OAAQA,CAAC,YAAYrI,wBAAwB,IAAK,EAAEqI,CAAC,YAAYnI,eAAe,CAAC;IACrF,CAAC,CAAC;IACF,IAAIiI,wBAAwB,EAAE;MAC1B,IAAI,CAACG,yBAAyB,GAAGH,wBAAwB;IAC7D;IACA,IAAI,CAACI,sBAAsB,GAAG/H,YAAY,CAAC4H,IAAI,CAACC,CAAC,IAAIA,CAAC,YAAYpI,qBAAqB,CAAC;EAC5F;EACMgC,yBAAyBA,CAACC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAE;IAAA,IAAAoG,MAAA;IAAA,OAAAlG,iBAAA;MAClD,IAAI,CAACkG,MAAI,CAACF,yBAAyB,EAAE;QACjC;MACJ;MACA,MAAMG,4BAA4B,GAAGD,MAAI,CAACE,yCAAyC,CAACF,MAAI,CAACF,yBAAyB,EAAEnG,KAAK,CAAC;MAC1H,IAAIsG,4BAA4B,KAAKjB,SAAS,EAAE;QAC5C;MACJ;MACA,MAAME,YAAY,SAASc,MAAI,CAACF,yBAAyB,CAACK,iCAAiC,CAACzG,MAAM,EAAEuG,4BAA4B,EAAErG,KAAK,CAAC;MACxI,IAAI,CAACsF,YAAY,EAAE;QACf;MACJ;MACAc,MAAI,CAACtD,cAAc,CAAC/C,KAAK,CAAC,GAAG;QACzB4E,IAAI,EAAE,WAAW;QACjBN,WAAW,EAAE+B,MAAI,CAACF,yBAAyB;QAC3C5E,SAAS,EAAEgE,YAAY,CAAChE,SAAS;QACjCiB,YAAY,EAAE+C,YAAY,CAAC/C;MAC/B,CAAC;MACD6D,MAAI,CAACpD,QAAQ,CAACwD,iBAAiB,CAAC,CAAC;IAAC;EACtC;EACArG,oBAAoBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACgG,sBAAsB,EAAEhG,oBAAoB,CAAC,CAAC,IAAI,KAAK;EACvE;EACAmG,yCAAyCA,CAACP,wBAAwB,EAAEhG,KAAK,EAAE;IACvE,MAAMuF,YAAY,GAAG,IAAI,CAACxC,cAAc,CAAC/C,KAAK,CAAC;IAC/C,IAAI,CAACuF,YAAY,IAAIA,YAAY,CAACX,IAAI,KAAK,WAAW,EAAE;MACpD,OAAOS,SAAS;IACpB;IACA,MAAMqB,wBAAwB,GAAGnB,YAAY,CAACjB,WAAW,KAAK0B,wBAAwB;IACtF,IAAI,CAACU,wBAAwB,EAAE;MAC3B,OAAOrB,SAAS;IACpB;IACA,MAAMsB,0BAA0B,GAAG,IAAI,CAAC5D,cAAc,CAAC6D,SAAS,CAACrB,YAAY,IAAIA,YAAY,CAACX,IAAI,KAAK,WAAW,IAC3GW,YAAY,CAACjB,WAAW,KAAK0B,wBAAwB,CAAC;IAC7D,IAAIW,0BAA0B,KAAK,CAAC,CAAC,EAAE;MACnC,MAAM,IAAI3I,kBAAkB,CAAC,CAAC;IAClC;IACA,OAAOgC,KAAK,GAAG2G,0BAA0B;EAC7C;EACA,IAAIhH,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACuD,SAAS;EACzB;EACA,IAAItD,kBAAkBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAACsD,SAAS,CAAC2D,aAAa,CAAC,CAAC;EACzC;EACA,IAAIhH,qBAAqBA,CAAA,EAAG;IACxB,OAAO,IAAI,CAACmD,sBAAsB;EACtC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}