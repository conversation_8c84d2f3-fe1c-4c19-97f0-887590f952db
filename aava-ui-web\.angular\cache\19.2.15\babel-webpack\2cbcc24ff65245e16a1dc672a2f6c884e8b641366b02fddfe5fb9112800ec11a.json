{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n/**\n * Represents information about a specific difference between two sequences.\n */\nexport class DiffChange {\n  /**\n   * Constructs a new DiffChange with the given sequence information\n   * and content.\n   */\n  constructor(originalStart, originalLength, modifiedStart, modifiedLength) {\n    //Debug.Assert(originalLength > 0 || modifiedLength > 0, \"originalLength and modifiedLength cannot both be <= 0\");\n    this.originalStart = originalStart;\n    this.originalLength = originalLength;\n    this.modifiedStart = modifiedStart;\n    this.modifiedLength = modifiedLength;\n  }\n  /**\n   * The end point (exclusive) of the change in the original sequence.\n   */\n  getOriginalEnd() {\n    return this.originalStart + this.originalLength;\n  }\n  /**\n   * The end point (exclusive) of the change in the modified sequence.\n   */\n  getModifiedEnd() {\n    return this.modifiedStart + this.modifiedLength;\n  }\n}", "map": {"version": 3, "names": ["DiffChange", "constructor", "originalStart", "original<PERSON>ength", "modifiedStart", "<PERSON><PERSON><PERSON><PERSON>", "getOriginalEnd", "getModifiedEnd"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/common/diff/diffChange.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n/**\n * Represents information about a specific difference between two sequences.\n */\nexport class DiffChange {\n    /**\n     * Constructs a new DiffChange with the given sequence information\n     * and content.\n     */\n    constructor(originalStart, originalLength, modifiedStart, modifiedLength) {\n        //Debug.Assert(originalLength > 0 || modifiedLength > 0, \"originalLength and modifiedLength cannot both be <= 0\");\n        this.originalStart = originalStart;\n        this.originalLength = originalLength;\n        this.modifiedStart = modifiedStart;\n        this.modifiedLength = modifiedLength;\n    }\n    /**\n     * The end point (exclusive) of the change in the original sequence.\n     */\n    getOriginalEnd() {\n        return this.originalStart + this.originalLength;\n    }\n    /**\n     * The end point (exclusive) of the change in the modified sequence.\n     */\n    getModifiedEnd() {\n        return this.modifiedStart + this.modifiedLength;\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,UAAU,CAAC;EACpB;AACJ;AACA;AACA;EACIC,WAAWA,CAACC,aAAa,EAAEC,cAAc,EAAEC,aAAa,EAAEC,cAAc,EAAE;IACtE;IACA,IAAI,CAACH,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,cAAc,GAAGA,cAAc;EACxC;EACA;AACJ;AACA;EACIC,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAACJ,aAAa,GAAG,IAAI,CAACC,cAAc;EACnD;EACA;AACJ;AACA;EACII,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAACH,aAAa,GAAG,IAAI,CAACC,cAAc;EACnD;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}