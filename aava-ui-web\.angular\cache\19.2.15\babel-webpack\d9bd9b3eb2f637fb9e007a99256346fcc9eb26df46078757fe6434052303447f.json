{"ast": null, "code": "import { hasDriveLetter } from './extpath.js';\nimport { isWindows } from './platform.js';\nexport function normalizeDriveLetter(path, isWindowsOS = isWindows) {\n  if (hasDriveLetter(path, isWindowsOS)) {\n    return path.charAt(0).toUpperCase() + path.slice(1);\n  }\n  return path;\n}\nlet normalizedUserHomeCached = Object.create(null);", "map": {"version": 3, "names": ["hasDriveLetter", "isWindows", "normalizeDriveLetter", "path", "isWindowsOS", "char<PERSON>t", "toUpperCase", "slice", "normalizedUserHomeCached", "Object", "create"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/common/labels.js"], "sourcesContent": ["import { hasDriveLetter } from './extpath.js';\nimport { isWindows } from './platform.js';\nexport function normalizeDriveLetter(path, isWindowsOS = isWindows) {\n    if (hasDriveLetter(path, isWindowsOS)) {\n        return path.charAt(0).toUpperCase() + path.slice(1);\n    }\n    return path;\n}\nlet normalizedUserHomeCached = Object.create(null);\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,cAAc;AAC7C,SAASC,SAAS,QAAQ,eAAe;AACzC,OAAO,SAASC,oBAAoBA,CAACC,IAAI,EAAEC,WAAW,GAAGH,SAAS,EAAE;EAChE,IAAID,cAAc,CAACG,IAAI,EAAEC,WAAW,CAAC,EAAE;IACnC,OAAOD,IAAI,CAACE,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGH,IAAI,CAACI,KAAK,CAAC,CAAC,CAAC;EACvD;EACA,OAAOJ,IAAI;AACf;AACA,IAAIK,wBAAwB,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}