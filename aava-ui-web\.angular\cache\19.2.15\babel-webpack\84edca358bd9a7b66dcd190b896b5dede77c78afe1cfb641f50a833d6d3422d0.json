{"ast": null, "code": "export default function (x) {\n  return typeof x === \"object\" && \"length\" in x ? x // Array, TypedArray, NodeList, array-like\n  : Array.from(x); // Map, Set, iterable, string, or anything else\n}\nexport function shuffle(array, random) {\n  let m = array.length,\n    t,\n    i;\n  while (m) {\n    i = random() * m-- | 0;\n    t = array[m];\n    array[m] = array[i];\n    array[i] = t;\n  }\n  return array;\n}", "map": {"version": 3, "names": ["x", "Array", "from", "shuffle", "array", "random", "m", "length", "t", "i"], "sources": ["C:/console/aava-ui-web/node_modules/d3-hierarchy/src/array.js"], "sourcesContent": ["export default function(x) {\n  return typeof x === \"object\" && \"length\" in x\n    ? x // Array, TypedArray, NodeList, array-like\n    : Array.from(x); // Map, Set, iterable, string, or anything else\n}\n\nexport function shuffle(array, random) {\n  let m = array.length,\n      t,\n      i;\n\n  while (m) {\n    i = random() * m-- | 0;\n    t = array[m];\n    array[m] = array[i];\n    array[i] = t;\n  }\n\n  return array;\n}\n"], "mappings": "AAAA,eAAe,UAASA,CAAC,EAAE;EACzB,OAAO,OAAOA,CAAC,KAAK,QAAQ,IAAI,QAAQ,IAAIA,CAAC,GACzCA,CAAC,CAAC;EAAA,EACFC,KAAK,CAACC,IAAI,CAACF,CAAC,CAAC,CAAC,CAAC;AACrB;AAEA,OAAO,SAASG,OAAOA,CAACC,KAAK,EAAEC,MAAM,EAAE;EACrC,IAAIC,CAAC,GAAGF,KAAK,CAACG,MAAM;IAChBC,CAAC;IACDC,CAAC;EAEL,OAAOH,CAAC,EAAE;IACRG,CAAC,GAAGJ,MAAM,CAAC,CAAC,GAAGC,CAAC,EAAE,GAAG,CAAC;IACtBE,CAAC,GAAGJ,KAAK,CAACE,CAAC,CAAC;IACZF,KAAK,CAACE,CAAC,CAAC,GAAGF,KAAK,CAACK,CAAC,CAAC;IACnBL,KAAK,CAACK,CAAC,CAAC,GAAGD,CAAC;EACd;EAEA,OAAOJ,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}