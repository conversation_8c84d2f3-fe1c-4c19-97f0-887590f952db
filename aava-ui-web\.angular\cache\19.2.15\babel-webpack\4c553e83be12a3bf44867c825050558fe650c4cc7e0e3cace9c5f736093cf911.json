{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Emitter } from '../../../../base/common/event.js';\nimport { Disposable } from '../../../../base/common/lifecycle.js';\nexport let DelegatingEditor = /*#__PURE__*/(() => {\n  class DelegatingEditor extends Disposable {\n    constructor() {\n      super(...arguments);\n      this._id = ++DelegatingEditor.idCounter;\n      this._onDidDispose = this._register(new Emitter());\n      this.onDidDispose = this._onDidDispose.event;\n      // #endregion\n    }\n    static {\n      this.idCounter = 0;\n    }\n    getId() {\n      return this.getEditorType() + ':v2:' + this._id;\n    }\n    // #region editorBrowser.IDiffEditor: Delegating to modified Editor\n    getVisibleColumnFromPosition(position) {\n      return this._targetEditor.getVisibleColumnFromPosition(position);\n    }\n    getPosition() {\n      return this._targetEditor.getPosition();\n    }\n    setPosition(position, source = 'api') {\n      this._targetEditor.setPosition(position, source);\n    }\n    revealLine(lineNumber, scrollType = 0 /* ScrollType.Smooth */) {\n      this._targetEditor.revealLine(lineNumber, scrollType);\n    }\n    revealLineInCenter(lineNumber, scrollType = 0 /* ScrollType.Smooth */) {\n      this._targetEditor.revealLineInCenter(lineNumber, scrollType);\n    }\n    revealLineInCenterIfOutsideViewport(lineNumber, scrollType = 0 /* ScrollType.Smooth */) {\n      this._targetEditor.revealLineInCenterIfOutsideViewport(lineNumber, scrollType);\n    }\n    revealLineNearTop(lineNumber, scrollType = 0 /* ScrollType.Smooth */) {\n      this._targetEditor.revealLineNearTop(lineNumber, scrollType);\n    }\n    revealPosition(position, scrollType = 0 /* ScrollType.Smooth */) {\n      this._targetEditor.revealPosition(position, scrollType);\n    }\n    revealPositionInCenter(position, scrollType = 0 /* ScrollType.Smooth */) {\n      this._targetEditor.revealPositionInCenter(position, scrollType);\n    }\n    revealPositionInCenterIfOutsideViewport(position, scrollType = 0 /* ScrollType.Smooth */) {\n      this._targetEditor.revealPositionInCenterIfOutsideViewport(position, scrollType);\n    }\n    revealPositionNearTop(position, scrollType = 0 /* ScrollType.Smooth */) {\n      this._targetEditor.revealPositionNearTop(position, scrollType);\n    }\n    getSelection() {\n      return this._targetEditor.getSelection();\n    }\n    getSelections() {\n      return this._targetEditor.getSelections();\n    }\n    setSelection(something, source = 'api') {\n      this._targetEditor.setSelection(something, source);\n    }\n    setSelections(ranges, source = 'api') {\n      this._targetEditor.setSelections(ranges, source);\n    }\n    revealLines(startLineNumber, endLineNumber, scrollType = 0 /* ScrollType.Smooth */) {\n      this._targetEditor.revealLines(startLineNumber, endLineNumber, scrollType);\n    }\n    revealLinesInCenter(startLineNumber, endLineNumber, scrollType = 0 /* ScrollType.Smooth */) {\n      this._targetEditor.revealLinesInCenter(startLineNumber, endLineNumber, scrollType);\n    }\n    revealLinesInCenterIfOutsideViewport(startLineNumber, endLineNumber, scrollType = 0 /* ScrollType.Smooth */) {\n      this._targetEditor.revealLinesInCenterIfOutsideViewport(startLineNumber, endLineNumber, scrollType);\n    }\n    revealLinesNearTop(startLineNumber, endLineNumber, scrollType = 0 /* ScrollType.Smooth */) {\n      this._targetEditor.revealLinesNearTop(startLineNumber, endLineNumber, scrollType);\n    }\n    revealRange(range, scrollType = 0 /* ScrollType.Smooth */, revealVerticalInCenter = false, revealHorizontal = true) {\n      this._targetEditor.revealRange(range, scrollType, revealVerticalInCenter, revealHorizontal);\n    }\n    revealRangeInCenter(range, scrollType = 0 /* ScrollType.Smooth */) {\n      this._targetEditor.revealRangeInCenter(range, scrollType);\n    }\n    revealRangeInCenterIfOutsideViewport(range, scrollType = 0 /* ScrollType.Smooth */) {\n      this._targetEditor.revealRangeInCenterIfOutsideViewport(range, scrollType);\n    }\n    revealRangeNearTop(range, scrollType = 0 /* ScrollType.Smooth */) {\n      this._targetEditor.revealRangeNearTop(range, scrollType);\n    }\n    revealRangeNearTopIfOutsideViewport(range, scrollType = 0 /* ScrollType.Smooth */) {\n      this._targetEditor.revealRangeNearTopIfOutsideViewport(range, scrollType);\n    }\n    revealRangeAtTop(range, scrollType = 0 /* ScrollType.Smooth */) {\n      this._targetEditor.revealRangeAtTop(range, scrollType);\n    }\n    getSupportedActions() {\n      return this._targetEditor.getSupportedActions();\n    }\n    focus() {\n      this._targetEditor.focus();\n    }\n    trigger(source, handlerId, payload) {\n      this._targetEditor.trigger(source, handlerId, payload);\n    }\n    createDecorationsCollection(decorations) {\n      return this._targetEditor.createDecorationsCollection(decorations);\n    }\n    changeDecorations(callback) {\n      return this._targetEditor.changeDecorations(callback);\n    }\n  }\n  return DelegatingEditor;\n})();", "map": {"version": 3, "names": ["Emitter", "Disposable", "DelegatingEditor", "constructor", "arguments", "_id", "idCounter", "_onDidDispose", "_register", "onDidDispose", "event", "getId", "getEditorType", "getVisibleColumnFromPosition", "position", "_targetEditor", "getPosition", "setPosition", "source", "revealLine", "lineNumber", "scrollType", "revealLineInCenter", "revealLineInCenterIfOutsideViewport", "revealLineNearTop", "revealPosition", "revealPositionInCenter", "revealPositionInCenterIfOutsideViewport", "revealPositionNearTop", "getSelection", "getSelections", "setSelection", "something", "setSelections", "ranges", "revealLines", "startLineNumber", "endLineNumber", "revealLinesInCenter", "revealLinesInCenterIfOutsideViewport", "revealLinesNearTop", "revealRange", "range", "revealVerticalInCenter", "revealHorizontal", "revealRangeInCenter", "revealRangeInCenterIfOutsideViewport", "revealRangeNearTop", "revealRangeNearTopIfOutsideViewport", "revealRangeAtTop", "getSupportedActions", "focus", "trigger", "handlerId", "payload", "createDecorationsCollection", "decorations", "changeDecorations", "callback"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/browser/widget/diffEditor/delegatingEditorImpl.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Emitter } from '../../../../base/common/event.js';\nimport { Disposable } from '../../../../base/common/lifecycle.js';\nexport class DelegatingEditor extends Disposable {\n    constructor() {\n        super(...arguments);\n        this._id = ++DelegatingEditor.idCounter;\n        this._onDidDispose = this._register(new Emitter());\n        this.onDidDispose = this._onDidDispose.event;\n        // #endregion\n    }\n    static { this.idCounter = 0; }\n    getId() { return this.getEditorType() + ':v2:' + this._id; }\n    // #region editorBrowser.IDiffEditor: Delegating to modified Editor\n    getVisibleColumnFromPosition(position) {\n        return this._targetEditor.getVisibleColumnFromPosition(position);\n    }\n    getPosition() {\n        return this._targetEditor.getPosition();\n    }\n    setPosition(position, source = 'api') {\n        this._targetEditor.setPosition(position, source);\n    }\n    revealLine(lineNumber, scrollType = 0 /* ScrollType.Smooth */) {\n        this._targetEditor.revealLine(lineNumber, scrollType);\n    }\n    revealLineInCenter(lineNumber, scrollType = 0 /* ScrollType.Smooth */) {\n        this._targetEditor.revealLineInCenter(lineNumber, scrollType);\n    }\n    revealLineInCenterIfOutsideViewport(lineNumber, scrollType = 0 /* ScrollType.Smooth */) {\n        this._targetEditor.revealLineInCenterIfOutsideViewport(lineNumber, scrollType);\n    }\n    revealLineNearTop(lineNumber, scrollType = 0 /* ScrollType.Smooth */) {\n        this._targetEditor.revealLineNearTop(lineNumber, scrollType);\n    }\n    revealPosition(position, scrollType = 0 /* ScrollType.Smooth */) {\n        this._targetEditor.revealPosition(position, scrollType);\n    }\n    revealPositionInCenter(position, scrollType = 0 /* ScrollType.Smooth */) {\n        this._targetEditor.revealPositionInCenter(position, scrollType);\n    }\n    revealPositionInCenterIfOutsideViewport(position, scrollType = 0 /* ScrollType.Smooth */) {\n        this._targetEditor.revealPositionInCenterIfOutsideViewport(position, scrollType);\n    }\n    revealPositionNearTop(position, scrollType = 0 /* ScrollType.Smooth */) {\n        this._targetEditor.revealPositionNearTop(position, scrollType);\n    }\n    getSelection() {\n        return this._targetEditor.getSelection();\n    }\n    getSelections() {\n        return this._targetEditor.getSelections();\n    }\n    setSelection(something, source = 'api') {\n        this._targetEditor.setSelection(something, source);\n    }\n    setSelections(ranges, source = 'api') {\n        this._targetEditor.setSelections(ranges, source);\n    }\n    revealLines(startLineNumber, endLineNumber, scrollType = 0 /* ScrollType.Smooth */) {\n        this._targetEditor.revealLines(startLineNumber, endLineNumber, scrollType);\n    }\n    revealLinesInCenter(startLineNumber, endLineNumber, scrollType = 0 /* ScrollType.Smooth */) {\n        this._targetEditor.revealLinesInCenter(startLineNumber, endLineNumber, scrollType);\n    }\n    revealLinesInCenterIfOutsideViewport(startLineNumber, endLineNumber, scrollType = 0 /* ScrollType.Smooth */) {\n        this._targetEditor.revealLinesInCenterIfOutsideViewport(startLineNumber, endLineNumber, scrollType);\n    }\n    revealLinesNearTop(startLineNumber, endLineNumber, scrollType = 0 /* ScrollType.Smooth */) {\n        this._targetEditor.revealLinesNearTop(startLineNumber, endLineNumber, scrollType);\n    }\n    revealRange(range, scrollType = 0 /* ScrollType.Smooth */, revealVerticalInCenter = false, revealHorizontal = true) {\n        this._targetEditor.revealRange(range, scrollType, revealVerticalInCenter, revealHorizontal);\n    }\n    revealRangeInCenter(range, scrollType = 0 /* ScrollType.Smooth */) {\n        this._targetEditor.revealRangeInCenter(range, scrollType);\n    }\n    revealRangeInCenterIfOutsideViewport(range, scrollType = 0 /* ScrollType.Smooth */) {\n        this._targetEditor.revealRangeInCenterIfOutsideViewport(range, scrollType);\n    }\n    revealRangeNearTop(range, scrollType = 0 /* ScrollType.Smooth */) {\n        this._targetEditor.revealRangeNearTop(range, scrollType);\n    }\n    revealRangeNearTopIfOutsideViewport(range, scrollType = 0 /* ScrollType.Smooth */) {\n        this._targetEditor.revealRangeNearTopIfOutsideViewport(range, scrollType);\n    }\n    revealRangeAtTop(range, scrollType = 0 /* ScrollType.Smooth */) {\n        this._targetEditor.revealRangeAtTop(range, scrollType);\n    }\n    getSupportedActions() {\n        return this._targetEditor.getSupportedActions();\n    }\n    focus() {\n        this._targetEditor.focus();\n    }\n    trigger(source, handlerId, payload) {\n        this._targetEditor.trigger(source, handlerId, payload);\n    }\n    createDecorationsCollection(decorations) {\n        return this._targetEditor.createDecorationsCollection(decorations);\n    }\n    changeDecorations(callback) {\n        return this._targetEditor.changeDecorations(callback);\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,OAAO,QAAQ,kCAAkC;AAC1D,SAASC,UAAU,QAAQ,sCAAsC;AACjE,WAAaC,gBAAgB;EAAtB,MAAMA,gBAAgB,SAASD,UAAU,CAAC;IAC7CE,WAAWA,CAAA,EAAG;MACV,KAAK,CAAC,GAAGC,SAAS,CAAC;MACnB,IAAI,CAACC,GAAG,GAAG,EAAEH,gBAAgB,CAACI,SAAS;MACvC,IAAI,CAACC,aAAa,GAAG,IAAI,CAACC,SAAS,CAAC,IAAIR,OAAO,CAAC,CAAC,CAAC;MAClD,IAAI,CAACS,YAAY,GAAG,IAAI,CAACF,aAAa,CAACG,KAAK;MAC5C;IACJ;IACA;MAAS,IAAI,CAACJ,SAAS,GAAG,CAAC;IAAE;IAC7BK,KAAKA,CAAA,EAAG;MAAE,OAAO,IAAI,CAACC,aAAa,CAAC,CAAC,GAAG,MAAM,GAAG,IAAI,CAACP,GAAG;IAAE;IAC3D;IACAQ,4BAA4BA,CAACC,QAAQ,EAAE;MACnC,OAAO,IAAI,CAACC,aAAa,CAACF,4BAA4B,CAACC,QAAQ,CAAC;IACpE;IACAE,WAAWA,CAAA,EAAG;MACV,OAAO,IAAI,CAACD,aAAa,CAACC,WAAW,CAAC,CAAC;IAC3C;IACAC,WAAWA,CAACH,QAAQ,EAAEI,MAAM,GAAG,KAAK,EAAE;MAClC,IAAI,CAACH,aAAa,CAACE,WAAW,CAACH,QAAQ,EAAEI,MAAM,CAAC;IACpD;IACAC,UAAUA,CAACC,UAAU,EAAEC,UAAU,GAAG,CAAC,CAAC,yBAAyB;MAC3D,IAAI,CAACN,aAAa,CAACI,UAAU,CAACC,UAAU,EAAEC,UAAU,CAAC;IACzD;IACAC,kBAAkBA,CAACF,UAAU,EAAEC,UAAU,GAAG,CAAC,CAAC,yBAAyB;MACnE,IAAI,CAACN,aAAa,CAACO,kBAAkB,CAACF,UAAU,EAAEC,UAAU,CAAC;IACjE;IACAE,mCAAmCA,CAACH,UAAU,EAAEC,UAAU,GAAG,CAAC,CAAC,yBAAyB;MACpF,IAAI,CAACN,aAAa,CAACQ,mCAAmC,CAACH,UAAU,EAAEC,UAAU,CAAC;IAClF;IACAG,iBAAiBA,CAACJ,UAAU,EAAEC,UAAU,GAAG,CAAC,CAAC,yBAAyB;MAClE,IAAI,CAACN,aAAa,CAACS,iBAAiB,CAACJ,UAAU,EAAEC,UAAU,CAAC;IAChE;IACAI,cAAcA,CAACX,QAAQ,EAAEO,UAAU,GAAG,CAAC,CAAC,yBAAyB;MAC7D,IAAI,CAACN,aAAa,CAACU,cAAc,CAACX,QAAQ,EAAEO,UAAU,CAAC;IAC3D;IACAK,sBAAsBA,CAACZ,QAAQ,EAAEO,UAAU,GAAG,CAAC,CAAC,yBAAyB;MACrE,IAAI,CAACN,aAAa,CAACW,sBAAsB,CAACZ,QAAQ,EAAEO,UAAU,CAAC;IACnE;IACAM,uCAAuCA,CAACb,QAAQ,EAAEO,UAAU,GAAG,CAAC,CAAC,yBAAyB;MACtF,IAAI,CAACN,aAAa,CAACY,uCAAuC,CAACb,QAAQ,EAAEO,UAAU,CAAC;IACpF;IACAO,qBAAqBA,CAACd,QAAQ,EAAEO,UAAU,GAAG,CAAC,CAAC,yBAAyB;MACpE,IAAI,CAACN,aAAa,CAACa,qBAAqB,CAACd,QAAQ,EAAEO,UAAU,CAAC;IAClE;IACAQ,YAAYA,CAAA,EAAG;MACX,OAAO,IAAI,CAACd,aAAa,CAACc,YAAY,CAAC,CAAC;IAC5C;IACAC,aAAaA,CAAA,EAAG;MACZ,OAAO,IAAI,CAACf,aAAa,CAACe,aAAa,CAAC,CAAC;IAC7C;IACAC,YAAYA,CAACC,SAAS,EAAEd,MAAM,GAAG,KAAK,EAAE;MACpC,IAAI,CAACH,aAAa,CAACgB,YAAY,CAACC,SAAS,EAAEd,MAAM,CAAC;IACtD;IACAe,aAAaA,CAACC,MAAM,EAAEhB,MAAM,GAAG,KAAK,EAAE;MAClC,IAAI,CAACH,aAAa,CAACkB,aAAa,CAACC,MAAM,EAAEhB,MAAM,CAAC;IACpD;IACAiB,WAAWA,CAACC,eAAe,EAAEC,aAAa,EAAEhB,UAAU,GAAG,CAAC,CAAC,yBAAyB;MAChF,IAAI,CAACN,aAAa,CAACoB,WAAW,CAACC,eAAe,EAAEC,aAAa,EAAEhB,UAAU,CAAC;IAC9E;IACAiB,mBAAmBA,CAACF,eAAe,EAAEC,aAAa,EAAEhB,UAAU,GAAG,CAAC,CAAC,yBAAyB;MACxF,IAAI,CAACN,aAAa,CAACuB,mBAAmB,CAACF,eAAe,EAAEC,aAAa,EAAEhB,UAAU,CAAC;IACtF;IACAkB,oCAAoCA,CAACH,eAAe,EAAEC,aAAa,EAAEhB,UAAU,GAAG,CAAC,CAAC,yBAAyB;MACzG,IAAI,CAACN,aAAa,CAACwB,oCAAoC,CAACH,eAAe,EAAEC,aAAa,EAAEhB,UAAU,CAAC;IACvG;IACAmB,kBAAkBA,CAACJ,eAAe,EAAEC,aAAa,EAAEhB,UAAU,GAAG,CAAC,CAAC,yBAAyB;MACvF,IAAI,CAACN,aAAa,CAACyB,kBAAkB,CAACJ,eAAe,EAAEC,aAAa,EAAEhB,UAAU,CAAC;IACrF;IACAoB,WAAWA,CAACC,KAAK,EAAErB,UAAU,GAAG,CAAC,CAAC,yBAAyBsB,sBAAsB,GAAG,KAAK,EAAEC,gBAAgB,GAAG,IAAI,EAAE;MAChH,IAAI,CAAC7B,aAAa,CAAC0B,WAAW,CAACC,KAAK,EAAErB,UAAU,EAAEsB,sBAAsB,EAAEC,gBAAgB,CAAC;IAC/F;IACAC,mBAAmBA,CAACH,KAAK,EAAErB,UAAU,GAAG,CAAC,CAAC,yBAAyB;MAC/D,IAAI,CAACN,aAAa,CAAC8B,mBAAmB,CAACH,KAAK,EAAErB,UAAU,CAAC;IAC7D;IACAyB,oCAAoCA,CAACJ,KAAK,EAAErB,UAAU,GAAG,CAAC,CAAC,yBAAyB;MAChF,IAAI,CAACN,aAAa,CAAC+B,oCAAoC,CAACJ,KAAK,EAAErB,UAAU,CAAC;IAC9E;IACA0B,kBAAkBA,CAACL,KAAK,EAAErB,UAAU,GAAG,CAAC,CAAC,yBAAyB;MAC9D,IAAI,CAACN,aAAa,CAACgC,kBAAkB,CAACL,KAAK,EAAErB,UAAU,CAAC;IAC5D;IACA2B,mCAAmCA,CAACN,KAAK,EAAErB,UAAU,GAAG,CAAC,CAAC,yBAAyB;MAC/E,IAAI,CAACN,aAAa,CAACiC,mCAAmC,CAACN,KAAK,EAAErB,UAAU,CAAC;IAC7E;IACA4B,gBAAgBA,CAACP,KAAK,EAAErB,UAAU,GAAG,CAAC,CAAC,yBAAyB;MAC5D,IAAI,CAACN,aAAa,CAACkC,gBAAgB,CAACP,KAAK,EAAErB,UAAU,CAAC;IAC1D;IACA6B,mBAAmBA,CAAA,EAAG;MAClB,OAAO,IAAI,CAACnC,aAAa,CAACmC,mBAAmB,CAAC,CAAC;IACnD;IACAC,KAAKA,CAAA,EAAG;MACJ,IAAI,CAACpC,aAAa,CAACoC,KAAK,CAAC,CAAC;IAC9B;IACAC,OAAOA,CAAClC,MAAM,EAAEmC,SAAS,EAAEC,OAAO,EAAE;MAChC,IAAI,CAACvC,aAAa,CAACqC,OAAO,CAAClC,MAAM,EAAEmC,SAAS,EAAEC,OAAO,CAAC;IAC1D;IACAC,2BAA2BA,CAACC,WAAW,EAAE;MACrC,OAAO,IAAI,CAACzC,aAAa,CAACwC,2BAA2B,CAACC,WAAW,CAAC;IACtE;IACAC,iBAAiBA,CAACC,QAAQ,EAAE;MACxB,OAAO,IAAI,CAAC3C,aAAa,CAAC0C,iBAAiB,CAACC,QAAQ,CAAC;IACzD;EACJ;EAAC,OArGYxD,gBAAgB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}