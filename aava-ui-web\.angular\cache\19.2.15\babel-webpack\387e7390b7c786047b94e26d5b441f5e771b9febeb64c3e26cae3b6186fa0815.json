{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = this && this.__param || function (paramIndex, decorator) {\n  return function (target, key) {\n    decorator(target, key, paramIndex);\n  };\n};\nimport { Emitter } from '../../../../../base/common/event.js';\nimport { Disposable } from '../../../../../base/common/lifecycle.js';\nimport { autorunHandleChanges, derivedOpts, observableFromEvent } from '../../../../../base/common/observable.js';\nimport { observableCodeEditor } from '../../../observableCodeEditor.js';\nimport { OverviewRulerFeature } from '../features/overviewRulerFeature.js';\nimport { EditorOptions } from '../../../../common/config/editorOptions.js';\nimport { Position } from '../../../../common/core/position.js';\nimport { localize } from '../../../../../nls.js';\nimport { IInstantiationService } from '../../../../../platform/instantiation/common/instantiation.js';\nimport { IKeybindingService } from '../../../../../platform/keybinding/common/keybinding.js';\nlet DiffEditorEditors = class DiffEditorEditors extends Disposable {\n  get onDidContentSizeChange() {\n    return this._onDidContentSizeChange.event;\n  }\n  constructor(originalEditorElement, modifiedEditorElement, _options, _argCodeEditorWidgetOptions, _createInnerEditor, _instantiationService, _keybindingService) {\n    super();\n    this.originalEditorElement = originalEditorElement;\n    this.modifiedEditorElement = modifiedEditorElement;\n    this._options = _options;\n    this._argCodeEditorWidgetOptions = _argCodeEditorWidgetOptions;\n    this._createInnerEditor = _createInnerEditor;\n    this._instantiationService = _instantiationService;\n    this._keybindingService = _keybindingService;\n    this.original = this._register(this._createLeftHandSideEditor(this._options.editorOptions.get(), this._argCodeEditorWidgetOptions.originalEditor || {}));\n    this.modified = this._register(this._createRightHandSideEditor(this._options.editorOptions.get(), this._argCodeEditorWidgetOptions.modifiedEditor || {}));\n    this._onDidContentSizeChange = this._register(new Emitter());\n    this.modifiedScrollTop = observableFromEvent(this, this.modified.onDidScrollChange, () => /** @description modified.getScrollTop */this.modified.getScrollTop());\n    this.modifiedScrollHeight = observableFromEvent(this, this.modified.onDidScrollChange, () => /** @description modified.getScrollHeight */this.modified.getScrollHeight());\n    this.modifiedObs = observableCodeEditor(this.modified);\n    this.originalObs = observableCodeEditor(this.original);\n    this.modifiedModel = this.modifiedObs.model;\n    this.modifiedSelections = observableFromEvent(this, this.modified.onDidChangeCursorSelection, () => this.modified.getSelections() ?? []);\n    this.modifiedCursor = derivedOpts({\n      owner: this,\n      equalsFn: Position.equals\n    }, reader => this.modifiedSelections.read(reader)[0]?.getPosition() ?? new Position(1, 1));\n    this.originalCursor = observableFromEvent(this, this.original.onDidChangeCursorPosition, () => this.original.getPosition() ?? new Position(1, 1));\n    this._argCodeEditorWidgetOptions = null;\n    this._register(autorunHandleChanges({\n      createEmptyChangeSummary: () => ({}),\n      handleChange: (ctx, changeSummary) => {\n        if (ctx.didChange(_options.editorOptions)) {\n          Object.assign(changeSummary, ctx.change.changedOptions);\n        }\n        return true;\n      }\n    }, (reader, changeSummary) => {\n      /** @description update editor options */\n      _options.editorOptions.read(reader);\n      this._options.renderSideBySide.read(reader);\n      this.modified.updateOptions(this._adjustOptionsForRightHandSide(reader, changeSummary));\n      this.original.updateOptions(this._adjustOptionsForLeftHandSide(reader, changeSummary));\n    }));\n  }\n  _createLeftHandSideEditor(options, codeEditorWidgetOptions) {\n    const leftHandSideOptions = this._adjustOptionsForLeftHandSide(undefined, options);\n    const editor = this._constructInnerEditor(this._instantiationService, this.originalEditorElement, leftHandSideOptions, codeEditorWidgetOptions);\n    editor.setContextValue('isInDiffLeftEditor', true);\n    return editor;\n  }\n  _createRightHandSideEditor(options, codeEditorWidgetOptions) {\n    const rightHandSideOptions = this._adjustOptionsForRightHandSide(undefined, options);\n    const editor = this._constructInnerEditor(this._instantiationService, this.modifiedEditorElement, rightHandSideOptions, codeEditorWidgetOptions);\n    editor.setContextValue('isInDiffRightEditor', true);\n    return editor;\n  }\n  _constructInnerEditor(instantiationService, container, options, editorWidgetOptions) {\n    const editor = this._createInnerEditor(instantiationService, container, options, editorWidgetOptions);\n    this._register(editor.onDidContentSizeChange(e => {\n      const width = this.original.getContentWidth() + this.modified.getContentWidth() + OverviewRulerFeature.ENTIRE_DIFF_OVERVIEW_WIDTH;\n      const height = Math.max(this.modified.getContentHeight(), this.original.getContentHeight());\n      this._onDidContentSizeChange.fire({\n        contentHeight: height,\n        contentWidth: width,\n        contentHeightChanged: e.contentHeightChanged,\n        contentWidthChanged: e.contentWidthChanged\n      });\n    }));\n    return editor;\n  }\n  _adjustOptionsForLeftHandSide(_reader, changedOptions) {\n    const result = this._adjustOptionsForSubEditor(changedOptions);\n    if (!this._options.renderSideBySide.get()) {\n      // never wrap hidden editor\n      result.wordWrapOverride1 = 'off';\n      result.wordWrapOverride2 = 'off';\n      result.stickyScroll = {\n        enabled: false\n      };\n      // Disable unicode highlighting for the original side in inline mode, as they are not shown anyway.\n      result.unicodeHighlight = {\n        nonBasicASCII: false,\n        ambiguousCharacters: false,\n        invisibleCharacters: false\n      };\n    } else {\n      result.unicodeHighlight = this._options.editorOptions.get().unicodeHighlight || {};\n      result.wordWrapOverride1 = this._options.diffWordWrap.get();\n    }\n    result.glyphMargin = this._options.renderSideBySide.get();\n    if (changedOptions.originalAriaLabel) {\n      result.ariaLabel = changedOptions.originalAriaLabel;\n    }\n    result.ariaLabel = this._updateAriaLabel(result.ariaLabel);\n    result.readOnly = !this._options.originalEditable.get();\n    result.dropIntoEditor = {\n      enabled: !result.readOnly\n    };\n    result.extraEditorClassName = 'original-in-monaco-diff-editor';\n    return result;\n  }\n  _adjustOptionsForRightHandSide(reader, changedOptions) {\n    const result = this._adjustOptionsForSubEditor(changedOptions);\n    if (changedOptions.modifiedAriaLabel) {\n      result.ariaLabel = changedOptions.modifiedAriaLabel;\n    }\n    result.ariaLabel = this._updateAriaLabel(result.ariaLabel);\n    result.wordWrapOverride1 = this._options.diffWordWrap.get();\n    result.revealHorizontalRightPadding = EditorOptions.revealHorizontalRightPadding.defaultValue + OverviewRulerFeature.ENTIRE_DIFF_OVERVIEW_WIDTH;\n    result.scrollbar.verticalHasArrows = false;\n    result.extraEditorClassName = 'modified-in-monaco-diff-editor';\n    return result;\n  }\n  _adjustOptionsForSubEditor(options) {\n    const clonedOptions = {\n      ...options,\n      dimension: {\n        height: 0,\n        width: 0\n      }\n    };\n    clonedOptions.inDiffEditor = true;\n    clonedOptions.automaticLayout = false;\n    // Clone scrollbar options before changing them\n    clonedOptions.scrollbar = {\n      ...(clonedOptions.scrollbar || {})\n    };\n    clonedOptions.folding = false;\n    clonedOptions.codeLens = this._options.diffCodeLens.get();\n    clonedOptions.fixedOverflowWidgets = true;\n    // Clone minimap options before changing them\n    clonedOptions.minimap = {\n      ...(clonedOptions.minimap || {})\n    };\n    clonedOptions.minimap.enabled = false;\n    if (this._options.hideUnchangedRegions.get()) {\n      clonedOptions.stickyScroll = {\n        enabled: false\n      };\n    } else {\n      clonedOptions.stickyScroll = this._options.editorOptions.get().stickyScroll;\n    }\n    return clonedOptions;\n  }\n  _updateAriaLabel(ariaLabel) {\n    if (!ariaLabel) {\n      ariaLabel = '';\n    }\n    const ariaNavigationTip = localize('diff-aria-navigation-tip', ' use {0} to open the accessibility help.', this._keybindingService.lookupKeybinding('editor.action.accessibilityHelp')?.getAriaLabel());\n    if (this._options.accessibilityVerbose.get()) {\n      return ariaLabel + ariaNavigationTip;\n    } else if (ariaLabel) {\n      return ariaLabel.replaceAll(ariaNavigationTip, '');\n    }\n    return '';\n  }\n};\nDiffEditorEditors = __decorate([__param(5, IInstantiationService), __param(6, IKeybindingService)], DiffEditorEditors);\nexport { DiffEditorEditors };", "map": {"version": 3, "names": ["__decorate", "decorators", "target", "key", "desc", "c", "arguments", "length", "r", "Object", "getOwnPropertyDescriptor", "d", "Reflect", "decorate", "i", "defineProperty", "__param", "paramIndex", "decorator", "Emitter", "Disposable", "autorunHandleChanges", "derivedOpts", "observableFromEvent", "observableCodeEditor", "OverviewRulerFeature", "EditorOptions", "Position", "localize", "IInstantiationService", "IKeybindingService", "DiffEditorEditors", "onDidContentSizeChange", "_onDidContentSizeChange", "event", "constructor", "originalEditorElement", "modifiedEditorElement", "_options", "_argCodeEditorWidgetOptions", "_createInnerEditor", "_instantiationService", "_keybindingService", "original", "_register", "_createLeftHandSideEditor", "editorOptions", "get", "originalEditor", "modified", "_createRightHandSideEditor", "modifiedEditor", "modifiedScrollTop", "onDidScrollChange", "getScrollTop", "modifiedScrollHeight", "getScrollHeight", "modifiedObs", "originalObs", "modifiedModel", "model", "modifiedSelections", "onDidChangeCursorSelection", "getSelections", "modifiedCursor", "owner", "equalsFn", "equals", "reader", "read", "getPosition", "originalCursor", "onDidChangeCursorPosition", "createEmptyChangeSummary", "handleChange", "ctx", "changeSummary", "<PERSON><PERSON><PERSON><PERSON>", "assign", "change", "changedOptions", "renderSideBySide", "updateOptions", "_adjustOptionsForRightHandSide", "_adjustOptionsForLeftHandSide", "options", "codeEditorWidgetOptions", "leftHandSideOptions", "undefined", "editor", "_constructInnerEditor", "setContextValue", "rightHandSideOptions", "instantiationService", "container", "editorWidgetOptions", "e", "width", "getContentWidth", "ENTIRE_DIFF_OVERVIEW_WIDTH", "height", "Math", "max", "getContentHeight", "fire", "contentHeight", "contentWidth", "contentHeightChanged", "contentWidthChanged", "_reader", "result", "_adjustOptionsForSubEditor", "wordWrapOverride1", "wordWrapOverride2", "stickyScroll", "enabled", "unicodeHighlight", "nonBasicASCII", "ambiguousCharacters", "invisibleCharacters", "diffWordWrap", "glyphMargin", "originalAriaLabel", "aria<PERSON><PERSON><PERSON>", "_updateAriaLabel", "readOnly", "originalEditable", "dropIntoEditor", "extraEditorClassName", "modifiedAriaLabel", "revealHorizontalRightPadding", "defaultValue", "scrollbar", "verticalHasArrows", "clonedOptions", "dimension", "inDiffEditor", "automaticLayout", "folding", "codeLens", "diffCodeLens", "fixedOverflowWidgets", "minimap", "hideUnchangedRegions", "ariaNavigationTip", "lookupKeybinding", "getAriaLabel", "accessibilityVerbose", "replaceAll"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/browser/widget/diffEditor/components/diffEditorEditors.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = (this && this.__param) || function (paramIndex, decorator) {\n    return function (target, key) { decorator(target, key, paramIndex); }\n};\nimport { Emitter } from '../../../../../base/common/event.js';\nimport { Disposable } from '../../../../../base/common/lifecycle.js';\nimport { autorunHandleChanges, derivedOpts, observableFromEvent } from '../../../../../base/common/observable.js';\nimport { observableCodeEditor } from '../../../observableCodeEditor.js';\nimport { OverviewRulerFeature } from '../features/overviewRulerFeature.js';\nimport { EditorOptions } from '../../../../common/config/editorOptions.js';\nimport { Position } from '../../../../common/core/position.js';\nimport { localize } from '../../../../../nls.js';\nimport { IInstantiationService } from '../../../../../platform/instantiation/common/instantiation.js';\nimport { IKeybindingService } from '../../../../../platform/keybinding/common/keybinding.js';\nlet DiffEditorEditors = class DiffEditorEditors extends Disposable {\n    get onDidContentSizeChange() { return this._onDidContentSizeChange.event; }\n    constructor(originalEditorElement, modifiedEditorElement, _options, _argCodeEditorWidgetOptions, _createInnerEditor, _instantiationService, _keybindingService) {\n        super();\n        this.originalEditorElement = originalEditorElement;\n        this.modifiedEditorElement = modifiedEditorElement;\n        this._options = _options;\n        this._argCodeEditorWidgetOptions = _argCodeEditorWidgetOptions;\n        this._createInnerEditor = _createInnerEditor;\n        this._instantiationService = _instantiationService;\n        this._keybindingService = _keybindingService;\n        this.original = this._register(this._createLeftHandSideEditor(this._options.editorOptions.get(), this._argCodeEditorWidgetOptions.originalEditor || {}));\n        this.modified = this._register(this._createRightHandSideEditor(this._options.editorOptions.get(), this._argCodeEditorWidgetOptions.modifiedEditor || {}));\n        this._onDidContentSizeChange = this._register(new Emitter());\n        this.modifiedScrollTop = observableFromEvent(this, this.modified.onDidScrollChange, () => /** @description modified.getScrollTop */ this.modified.getScrollTop());\n        this.modifiedScrollHeight = observableFromEvent(this, this.modified.onDidScrollChange, () => /** @description modified.getScrollHeight */ this.modified.getScrollHeight());\n        this.modifiedObs = observableCodeEditor(this.modified);\n        this.originalObs = observableCodeEditor(this.original);\n        this.modifiedModel = this.modifiedObs.model;\n        this.modifiedSelections = observableFromEvent(this, this.modified.onDidChangeCursorSelection, () => this.modified.getSelections() ?? []);\n        this.modifiedCursor = derivedOpts({ owner: this, equalsFn: Position.equals }, reader => this.modifiedSelections.read(reader)[0]?.getPosition() ?? new Position(1, 1));\n        this.originalCursor = observableFromEvent(this, this.original.onDidChangeCursorPosition, () => this.original.getPosition() ?? new Position(1, 1));\n        this._argCodeEditorWidgetOptions = null;\n        this._register(autorunHandleChanges({\n            createEmptyChangeSummary: () => ({}),\n            handleChange: (ctx, changeSummary) => {\n                if (ctx.didChange(_options.editorOptions)) {\n                    Object.assign(changeSummary, ctx.change.changedOptions);\n                }\n                return true;\n            }\n        }, (reader, changeSummary) => {\n            /** @description update editor options */\n            _options.editorOptions.read(reader);\n            this._options.renderSideBySide.read(reader);\n            this.modified.updateOptions(this._adjustOptionsForRightHandSide(reader, changeSummary));\n            this.original.updateOptions(this._adjustOptionsForLeftHandSide(reader, changeSummary));\n        }));\n    }\n    _createLeftHandSideEditor(options, codeEditorWidgetOptions) {\n        const leftHandSideOptions = this._adjustOptionsForLeftHandSide(undefined, options);\n        const editor = this._constructInnerEditor(this._instantiationService, this.originalEditorElement, leftHandSideOptions, codeEditorWidgetOptions);\n        editor.setContextValue('isInDiffLeftEditor', true);\n        return editor;\n    }\n    _createRightHandSideEditor(options, codeEditorWidgetOptions) {\n        const rightHandSideOptions = this._adjustOptionsForRightHandSide(undefined, options);\n        const editor = this._constructInnerEditor(this._instantiationService, this.modifiedEditorElement, rightHandSideOptions, codeEditorWidgetOptions);\n        editor.setContextValue('isInDiffRightEditor', true);\n        return editor;\n    }\n    _constructInnerEditor(instantiationService, container, options, editorWidgetOptions) {\n        const editor = this._createInnerEditor(instantiationService, container, options, editorWidgetOptions);\n        this._register(editor.onDidContentSizeChange(e => {\n            const width = this.original.getContentWidth() + this.modified.getContentWidth() + OverviewRulerFeature.ENTIRE_DIFF_OVERVIEW_WIDTH;\n            const height = Math.max(this.modified.getContentHeight(), this.original.getContentHeight());\n            this._onDidContentSizeChange.fire({\n                contentHeight: height,\n                contentWidth: width,\n                contentHeightChanged: e.contentHeightChanged,\n                contentWidthChanged: e.contentWidthChanged\n            });\n        }));\n        return editor;\n    }\n    _adjustOptionsForLeftHandSide(_reader, changedOptions) {\n        const result = this._adjustOptionsForSubEditor(changedOptions);\n        if (!this._options.renderSideBySide.get()) {\n            // never wrap hidden editor\n            result.wordWrapOverride1 = 'off';\n            result.wordWrapOverride2 = 'off';\n            result.stickyScroll = { enabled: false };\n            // Disable unicode highlighting for the original side in inline mode, as they are not shown anyway.\n            result.unicodeHighlight = { nonBasicASCII: false, ambiguousCharacters: false, invisibleCharacters: false };\n        }\n        else {\n            result.unicodeHighlight = this._options.editorOptions.get().unicodeHighlight || {};\n            result.wordWrapOverride1 = this._options.diffWordWrap.get();\n        }\n        result.glyphMargin = this._options.renderSideBySide.get();\n        if (changedOptions.originalAriaLabel) {\n            result.ariaLabel = changedOptions.originalAriaLabel;\n        }\n        result.ariaLabel = this._updateAriaLabel(result.ariaLabel);\n        result.readOnly = !this._options.originalEditable.get();\n        result.dropIntoEditor = { enabled: !result.readOnly };\n        result.extraEditorClassName = 'original-in-monaco-diff-editor';\n        return result;\n    }\n    _adjustOptionsForRightHandSide(reader, changedOptions) {\n        const result = this._adjustOptionsForSubEditor(changedOptions);\n        if (changedOptions.modifiedAriaLabel) {\n            result.ariaLabel = changedOptions.modifiedAriaLabel;\n        }\n        result.ariaLabel = this._updateAriaLabel(result.ariaLabel);\n        result.wordWrapOverride1 = this._options.diffWordWrap.get();\n        result.revealHorizontalRightPadding = EditorOptions.revealHorizontalRightPadding.defaultValue + OverviewRulerFeature.ENTIRE_DIFF_OVERVIEW_WIDTH;\n        result.scrollbar.verticalHasArrows = false;\n        result.extraEditorClassName = 'modified-in-monaco-diff-editor';\n        return result;\n    }\n    _adjustOptionsForSubEditor(options) {\n        const clonedOptions = {\n            ...options,\n            dimension: {\n                height: 0,\n                width: 0\n            },\n        };\n        clonedOptions.inDiffEditor = true;\n        clonedOptions.automaticLayout = false;\n        // Clone scrollbar options before changing them\n        clonedOptions.scrollbar = { ...(clonedOptions.scrollbar || {}) };\n        clonedOptions.folding = false;\n        clonedOptions.codeLens = this._options.diffCodeLens.get();\n        clonedOptions.fixedOverflowWidgets = true;\n        // Clone minimap options before changing them\n        clonedOptions.minimap = { ...(clonedOptions.minimap || {}) };\n        clonedOptions.minimap.enabled = false;\n        if (this._options.hideUnchangedRegions.get()) {\n            clonedOptions.stickyScroll = { enabled: false };\n        }\n        else {\n            clonedOptions.stickyScroll = this._options.editorOptions.get().stickyScroll;\n        }\n        return clonedOptions;\n    }\n    _updateAriaLabel(ariaLabel) {\n        if (!ariaLabel) {\n            ariaLabel = '';\n        }\n        const ariaNavigationTip = localize('diff-aria-navigation-tip', ' use {0} to open the accessibility help.', this._keybindingService.lookupKeybinding('editor.action.accessibilityHelp')?.getAriaLabel());\n        if (this._options.accessibilityVerbose.get()) {\n            return ariaLabel + ariaNavigationTip;\n        }\n        else if (ariaLabel) {\n            return ariaLabel.replaceAll(ariaNavigationTip, '');\n        }\n        return '';\n    }\n};\nDiffEditorEditors = __decorate([\n    __param(5, IInstantiationService),\n    __param(6, IKeybindingService)\n], DiffEditorEditors);\nexport { DiffEditorEditors };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,IAAIA,UAAU,GAAI,IAAI,IAAI,IAAI,CAACA,UAAU,IAAK,UAAUC,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAE;EACnF,IAAIC,CAAC,GAAGC,SAAS,CAACC,MAAM;IAAEC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGH,MAAM,GAAGE,IAAI,KAAK,IAAI,GAAGA,IAAI,GAAGK,MAAM,CAACC,wBAAwB,CAACR,MAAM,EAAEC,GAAG,CAAC,GAAGC,IAAI;IAAEO,CAAC;EAC5H,IAAI,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,CAACC,QAAQ,KAAK,UAAU,EAAEL,CAAC,GAAGI,OAAO,CAACC,QAAQ,CAACZ,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,CAAC,CAAC,KAC1H,KAAK,IAAIU,CAAC,GAAGb,UAAU,CAACM,MAAM,GAAG,CAAC,EAAEO,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE,IAAIH,CAAC,GAAGV,UAAU,CAACa,CAAC,CAAC,EAAEN,CAAC,GAAG,CAACH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACH,CAAC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACT,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,GAAGG,CAAC,CAACT,MAAM,EAAEC,GAAG,CAAC,KAAKK,CAAC;EACjJ,OAAOH,CAAC,GAAG,CAAC,IAAIG,CAAC,IAAIC,MAAM,CAACM,cAAc,CAACb,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,EAAEA,CAAC;AACjE,CAAC;AACD,IAAIQ,OAAO,GAAI,IAAI,IAAI,IAAI,CAACA,OAAO,IAAK,UAAUC,UAAU,EAAEC,SAAS,EAAE;EACrE,OAAO,UAAUhB,MAAM,EAAEC,GAAG,EAAE;IAAEe,SAAS,CAAChB,MAAM,EAAEC,GAAG,EAAEc,UAAU,CAAC;EAAE,CAAC;AACzE,CAAC;AACD,SAASE,OAAO,QAAQ,qCAAqC;AAC7D,SAASC,UAAU,QAAQ,yCAAyC;AACpE,SAASC,oBAAoB,EAAEC,WAAW,EAAEC,mBAAmB,QAAQ,0CAA0C;AACjH,SAASC,oBAAoB,QAAQ,kCAAkC;AACvE,SAASC,oBAAoB,QAAQ,qCAAqC;AAC1E,SAASC,aAAa,QAAQ,4CAA4C;AAC1E,SAASC,QAAQ,QAAQ,qCAAqC;AAC9D,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,qBAAqB,QAAQ,+DAA+D;AACrG,SAASC,kBAAkB,QAAQ,yDAAyD;AAC5F,IAAIC,iBAAiB,GAAG,MAAMA,iBAAiB,SAASX,UAAU,CAAC;EAC/D,IAAIY,sBAAsBA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACC,uBAAuB,CAACC,KAAK;EAAE;EAC1EC,WAAWA,CAACC,qBAAqB,EAAEC,qBAAqB,EAAEC,QAAQ,EAAEC,2BAA2B,EAAEC,kBAAkB,EAAEC,qBAAqB,EAAEC,kBAAkB,EAAE;IAC5J,KAAK,CAAC,CAAC;IACP,IAAI,CAACN,qBAAqB,GAAGA,qBAAqB;IAClD,IAAI,CAACC,qBAAqB,GAAGA,qBAAqB;IAClD,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,2BAA2B,GAAGA,2BAA2B;IAC9D,IAAI,CAACC,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,qBAAqB,GAAGA,qBAAqB;IAClD,IAAI,CAACC,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACC,SAAS,CAAC,IAAI,CAACC,yBAAyB,CAAC,IAAI,CAACP,QAAQ,CAACQ,aAAa,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAACR,2BAA2B,CAACS,cAAc,IAAI,CAAC,CAAC,CAAC,CAAC;IACxJ,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACL,SAAS,CAAC,IAAI,CAACM,0BAA0B,CAAC,IAAI,CAACZ,QAAQ,CAACQ,aAAa,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAACR,2BAA2B,CAACY,cAAc,IAAI,CAAC,CAAC,CAAC,CAAC;IACzJ,IAAI,CAAClB,uBAAuB,GAAG,IAAI,CAACW,SAAS,CAAC,IAAIzB,OAAO,CAAC,CAAC,CAAC;IAC5D,IAAI,CAACiC,iBAAiB,GAAG7B,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC0B,QAAQ,CAACI,iBAAiB,EAAE,MAAM,yCAA0C,IAAI,CAACJ,QAAQ,CAACK,YAAY,CAAC,CAAC,CAAC;IACjK,IAAI,CAACC,oBAAoB,GAAGhC,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC0B,QAAQ,CAACI,iBAAiB,EAAE,MAAM,4CAA6C,IAAI,CAACJ,QAAQ,CAACO,eAAe,CAAC,CAAC,CAAC;IAC1K,IAAI,CAACC,WAAW,GAAGjC,oBAAoB,CAAC,IAAI,CAACyB,QAAQ,CAAC;IACtD,IAAI,CAACS,WAAW,GAAGlC,oBAAoB,CAAC,IAAI,CAACmB,QAAQ,CAAC;IACtD,IAAI,CAACgB,aAAa,GAAG,IAAI,CAACF,WAAW,CAACG,KAAK;IAC3C,IAAI,CAACC,kBAAkB,GAAGtC,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC0B,QAAQ,CAACa,0BAA0B,EAAE,MAAM,IAAI,CAACb,QAAQ,CAACc,aAAa,CAAC,CAAC,IAAI,EAAE,CAAC;IACxI,IAAI,CAACC,cAAc,GAAG1C,WAAW,CAAC;MAAE2C,KAAK,EAAE,IAAI;MAAEC,QAAQ,EAAEvC,QAAQ,CAACwC;IAAO,CAAC,EAAEC,MAAM,IAAI,IAAI,CAACP,kBAAkB,CAACQ,IAAI,CAACD,MAAM,CAAC,CAAC,CAAC,CAAC,EAAEE,WAAW,CAAC,CAAC,IAAI,IAAI3C,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACrK,IAAI,CAAC4C,cAAc,GAAGhD,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAACoB,QAAQ,CAAC6B,yBAAyB,EAAE,MAAM,IAAI,CAAC7B,QAAQ,CAAC2B,WAAW,CAAC,CAAC,IAAI,IAAI3C,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACjJ,IAAI,CAACY,2BAA2B,GAAG,IAAI;IACvC,IAAI,CAACK,SAAS,CAACvB,oBAAoB,CAAC;MAChCoD,wBAAwB,EAAEA,CAAA,MAAO,CAAC,CAAC,CAAC;MACpCC,YAAY,EAAEA,CAACC,GAAG,EAAEC,aAAa,KAAK;QAClC,IAAID,GAAG,CAACE,SAAS,CAACvC,QAAQ,CAACQ,aAAa,CAAC,EAAE;UACvCrC,MAAM,CAACqE,MAAM,CAACF,aAAa,EAAED,GAAG,CAACI,MAAM,CAACC,cAAc,CAAC;QAC3D;QACA,OAAO,IAAI;MACf;IACJ,CAAC,EAAE,CAACZ,MAAM,EAAEQ,aAAa,KAAK;MAC1B;MACAtC,QAAQ,CAACQ,aAAa,CAACuB,IAAI,CAACD,MAAM,CAAC;MACnC,IAAI,CAAC9B,QAAQ,CAAC2C,gBAAgB,CAACZ,IAAI,CAACD,MAAM,CAAC;MAC3C,IAAI,CAACnB,QAAQ,CAACiC,aAAa,CAAC,IAAI,CAACC,8BAA8B,CAACf,MAAM,EAAEQ,aAAa,CAAC,CAAC;MACvF,IAAI,CAACjC,QAAQ,CAACuC,aAAa,CAAC,IAAI,CAACE,6BAA6B,CAAChB,MAAM,EAAEQ,aAAa,CAAC,CAAC;IAC1F,CAAC,CAAC,CAAC;EACP;EACA/B,yBAAyBA,CAACwC,OAAO,EAAEC,uBAAuB,EAAE;IACxD,MAAMC,mBAAmB,GAAG,IAAI,CAACH,6BAA6B,CAACI,SAAS,EAAEH,OAAO,CAAC;IAClF,MAAMI,MAAM,GAAG,IAAI,CAACC,qBAAqB,CAAC,IAAI,CAACjD,qBAAqB,EAAE,IAAI,CAACL,qBAAqB,EAAEmD,mBAAmB,EAAED,uBAAuB,CAAC;IAC/IG,MAAM,CAACE,eAAe,CAAC,oBAAoB,EAAE,IAAI,CAAC;IAClD,OAAOF,MAAM;EACjB;EACAvC,0BAA0BA,CAACmC,OAAO,EAAEC,uBAAuB,EAAE;IACzD,MAAMM,oBAAoB,GAAG,IAAI,CAACT,8BAA8B,CAACK,SAAS,EAAEH,OAAO,CAAC;IACpF,MAAMI,MAAM,GAAG,IAAI,CAACC,qBAAqB,CAAC,IAAI,CAACjD,qBAAqB,EAAE,IAAI,CAACJ,qBAAqB,EAAEuD,oBAAoB,EAAEN,uBAAuB,CAAC;IAChJG,MAAM,CAACE,eAAe,CAAC,qBAAqB,EAAE,IAAI,CAAC;IACnD,OAAOF,MAAM;EACjB;EACAC,qBAAqBA,CAACG,oBAAoB,EAAEC,SAAS,EAAET,OAAO,EAAEU,mBAAmB,EAAE;IACjF,MAAMN,MAAM,GAAG,IAAI,CAACjD,kBAAkB,CAACqD,oBAAoB,EAAEC,SAAS,EAAET,OAAO,EAAEU,mBAAmB,CAAC;IACrG,IAAI,CAACnD,SAAS,CAAC6C,MAAM,CAACzD,sBAAsB,CAACgE,CAAC,IAAI;MAC9C,MAAMC,KAAK,GAAG,IAAI,CAACtD,QAAQ,CAACuD,eAAe,CAAC,CAAC,GAAG,IAAI,CAACjD,QAAQ,CAACiD,eAAe,CAAC,CAAC,GAAGzE,oBAAoB,CAAC0E,0BAA0B;MACjI,MAAMC,MAAM,GAAGC,IAAI,CAACC,GAAG,CAAC,IAAI,CAACrD,QAAQ,CAACsD,gBAAgB,CAAC,CAAC,EAAE,IAAI,CAAC5D,QAAQ,CAAC4D,gBAAgB,CAAC,CAAC,CAAC;MAC3F,IAAI,CAACtE,uBAAuB,CAACuE,IAAI,CAAC;QAC9BC,aAAa,EAAEL,MAAM;QACrBM,YAAY,EAAET,KAAK;QACnBU,oBAAoB,EAAEX,CAAC,CAACW,oBAAoB;QAC5CC,mBAAmB,EAAEZ,CAAC,CAACY;MAC3B,CAAC,CAAC;IACN,CAAC,CAAC,CAAC;IACH,OAAOnB,MAAM;EACjB;EACAL,6BAA6BA,CAACyB,OAAO,EAAE7B,cAAc,EAAE;IACnD,MAAM8B,MAAM,GAAG,IAAI,CAACC,0BAA0B,CAAC/B,cAAc,CAAC;IAC9D,IAAI,CAAC,IAAI,CAAC1C,QAAQ,CAAC2C,gBAAgB,CAAClC,GAAG,CAAC,CAAC,EAAE;MACvC;MACA+D,MAAM,CAACE,iBAAiB,GAAG,KAAK;MAChCF,MAAM,CAACG,iBAAiB,GAAG,KAAK;MAChCH,MAAM,CAACI,YAAY,GAAG;QAAEC,OAAO,EAAE;MAAM,CAAC;MACxC;MACAL,MAAM,CAACM,gBAAgB,GAAG;QAAEC,aAAa,EAAE,KAAK;QAAEC,mBAAmB,EAAE,KAAK;QAAEC,mBAAmB,EAAE;MAAM,CAAC;IAC9G,CAAC,MACI;MACDT,MAAM,CAACM,gBAAgB,GAAG,IAAI,CAAC9E,QAAQ,CAACQ,aAAa,CAACC,GAAG,CAAC,CAAC,CAACqE,gBAAgB,IAAI,CAAC,CAAC;MAClFN,MAAM,CAACE,iBAAiB,GAAG,IAAI,CAAC1E,QAAQ,CAACkF,YAAY,CAACzE,GAAG,CAAC,CAAC;IAC/D;IACA+D,MAAM,CAACW,WAAW,GAAG,IAAI,CAACnF,QAAQ,CAAC2C,gBAAgB,CAAClC,GAAG,CAAC,CAAC;IACzD,IAAIiC,cAAc,CAAC0C,iBAAiB,EAAE;MAClCZ,MAAM,CAACa,SAAS,GAAG3C,cAAc,CAAC0C,iBAAiB;IACvD;IACAZ,MAAM,CAACa,SAAS,GAAG,IAAI,CAACC,gBAAgB,CAACd,MAAM,CAACa,SAAS,CAAC;IAC1Db,MAAM,CAACe,QAAQ,GAAG,CAAC,IAAI,CAACvF,QAAQ,CAACwF,gBAAgB,CAAC/E,GAAG,CAAC,CAAC;IACvD+D,MAAM,CAACiB,cAAc,GAAG;MAAEZ,OAAO,EAAE,CAACL,MAAM,CAACe;IAAS,CAAC;IACrDf,MAAM,CAACkB,oBAAoB,GAAG,gCAAgC;IAC9D,OAAOlB,MAAM;EACjB;EACA3B,8BAA8BA,CAACf,MAAM,EAAEY,cAAc,EAAE;IACnD,MAAM8B,MAAM,GAAG,IAAI,CAACC,0BAA0B,CAAC/B,cAAc,CAAC;IAC9D,IAAIA,cAAc,CAACiD,iBAAiB,EAAE;MAClCnB,MAAM,CAACa,SAAS,GAAG3C,cAAc,CAACiD,iBAAiB;IACvD;IACAnB,MAAM,CAACa,SAAS,GAAG,IAAI,CAACC,gBAAgB,CAACd,MAAM,CAACa,SAAS,CAAC;IAC1Db,MAAM,CAACE,iBAAiB,GAAG,IAAI,CAAC1E,QAAQ,CAACkF,YAAY,CAACzE,GAAG,CAAC,CAAC;IAC3D+D,MAAM,CAACoB,4BAA4B,GAAGxG,aAAa,CAACwG,4BAA4B,CAACC,YAAY,GAAG1G,oBAAoB,CAAC0E,0BAA0B;IAC/IW,MAAM,CAACsB,SAAS,CAACC,iBAAiB,GAAG,KAAK;IAC1CvB,MAAM,CAACkB,oBAAoB,GAAG,gCAAgC;IAC9D,OAAOlB,MAAM;EACjB;EACAC,0BAA0BA,CAAC1B,OAAO,EAAE;IAChC,MAAMiD,aAAa,GAAG;MAClB,GAAGjD,OAAO;MACVkD,SAAS,EAAE;QACPnC,MAAM,EAAE,CAAC;QACTH,KAAK,EAAE;MACX;IACJ,CAAC;IACDqC,aAAa,CAACE,YAAY,GAAG,IAAI;IACjCF,aAAa,CAACG,eAAe,GAAG,KAAK;IACrC;IACAH,aAAa,CAACF,SAAS,GAAG;MAAE,IAAIE,aAAa,CAACF,SAAS,IAAI,CAAC,CAAC;IAAE,CAAC;IAChEE,aAAa,CAACI,OAAO,GAAG,KAAK;IAC7BJ,aAAa,CAACK,QAAQ,GAAG,IAAI,CAACrG,QAAQ,CAACsG,YAAY,CAAC7F,GAAG,CAAC,CAAC;IACzDuF,aAAa,CAACO,oBAAoB,GAAG,IAAI;IACzC;IACAP,aAAa,CAACQ,OAAO,GAAG;MAAE,IAAIR,aAAa,CAACQ,OAAO,IAAI,CAAC,CAAC;IAAE,CAAC;IAC5DR,aAAa,CAACQ,OAAO,CAAC3B,OAAO,GAAG,KAAK;IACrC,IAAI,IAAI,CAAC7E,QAAQ,CAACyG,oBAAoB,CAAChG,GAAG,CAAC,CAAC,EAAE;MAC1CuF,aAAa,CAACpB,YAAY,GAAG;QAAEC,OAAO,EAAE;MAAM,CAAC;IACnD,CAAC,MACI;MACDmB,aAAa,CAACpB,YAAY,GAAG,IAAI,CAAC5E,QAAQ,CAACQ,aAAa,CAACC,GAAG,CAAC,CAAC,CAACmE,YAAY;IAC/E;IACA,OAAOoB,aAAa;EACxB;EACAV,gBAAgBA,CAACD,SAAS,EAAE;IACxB,IAAI,CAACA,SAAS,EAAE;MACZA,SAAS,GAAG,EAAE;IAClB;IACA,MAAMqB,iBAAiB,GAAGpH,QAAQ,CAAC,0BAA0B,EAAE,0CAA0C,EAAE,IAAI,CAACc,kBAAkB,CAACuG,gBAAgB,CAAC,iCAAiC,CAAC,EAAEC,YAAY,CAAC,CAAC,CAAC;IACvM,IAAI,IAAI,CAAC5G,QAAQ,CAAC6G,oBAAoB,CAACpG,GAAG,CAAC,CAAC,EAAE;MAC1C,OAAO4E,SAAS,GAAGqB,iBAAiB;IACxC,CAAC,MACI,IAAIrB,SAAS,EAAE;MAChB,OAAOA,SAAS,CAACyB,UAAU,CAACJ,iBAAiB,EAAE,EAAE,CAAC;IACtD;IACA,OAAO,EAAE;EACb;AACJ,CAAC;AACDjH,iBAAiB,GAAG/B,UAAU,CAAC,CAC3BgB,OAAO,CAAC,CAAC,EAAEa,qBAAqB,CAAC,EACjCb,OAAO,CAAC,CAAC,EAAEc,kBAAkB,CAAC,CACjC,EAAEC,iBAAiB,CAAC;AACrB,SAASA,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}