{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as nls from '../../nls.js';\nexport class ModifierLabelProvider {\n  constructor(mac, windows, linux = windows) {\n    this.modifierLabels = [null]; // index 0 will never me accessed.\n    this.modifierLabels[2 /* OperatingSystem.Macintosh */] = mac;\n    this.modifierLabels[1 /* OperatingSystem.Windows */] = windows;\n    this.modifierLabels[3 /* OperatingSystem.Linux */] = linux;\n  }\n  toLabel(OS, chords, keyLabelProvider) {\n    if (chords.length === 0) {\n      return null;\n    }\n    const result = [];\n    for (let i = 0, len = chords.length; i < len; i++) {\n      const chord = chords[i];\n      const keyLabel = keyLabelProvider(chord);\n      if (keyLabel === null) {\n        // this keybinding cannot be expressed...\n        return null;\n      }\n      result[i] = _simpleAsString(chord, keyLabel, this.modifierLabels[OS]);\n    }\n    return result.join(' ');\n  }\n}\n/**\n * A label provider that prints modifiers in a suitable format for displaying in the UI.\n */\nexport const UILabelProvider = new ModifierLabelProvider({\n  ctrlKey: '\\u2303',\n  shiftKey: '⇧',\n  altKey: '⌥',\n  metaKey: '⌘',\n  separator: ''\n}, {\n  ctrlKey: nls.localize({\n    key: 'ctrlKey',\n    comment: ['This is the short form for the Control key on the keyboard']\n  }, \"Ctrl\"),\n  shiftKey: nls.localize({\n    key: 'shiftKey',\n    comment: ['This is the short form for the Shift key on the keyboard']\n  }, \"Shift\"),\n  altKey: nls.localize({\n    key: 'altKey',\n    comment: ['This is the short form for the Alt key on the keyboard']\n  }, \"Alt\"),\n  metaKey: nls.localize({\n    key: 'windowsKey',\n    comment: ['This is the short form for the Windows key on the keyboard']\n  }, \"Windows\"),\n  separator: '+'\n}, {\n  ctrlKey: nls.localize({\n    key: 'ctrlKey',\n    comment: ['This is the short form for the Control key on the keyboard']\n  }, \"Ctrl\"),\n  shiftKey: nls.localize({\n    key: 'shiftKey',\n    comment: ['This is the short form for the Shift key on the keyboard']\n  }, \"Shift\"),\n  altKey: nls.localize({\n    key: 'altKey',\n    comment: ['This is the short form for the Alt key on the keyboard']\n  }, \"Alt\"),\n  metaKey: nls.localize({\n    key: 'superKey',\n    comment: ['This is the short form for the Super key on the keyboard']\n  }, \"Super\"),\n  separator: '+'\n});\n/**\n * A label provider that prints modifiers in a suitable format for ARIA.\n */\nexport const AriaLabelProvider = new ModifierLabelProvider({\n  ctrlKey: nls.localize({\n    key: 'ctrlKey.long',\n    comment: ['This is the long form for the Control key on the keyboard']\n  }, \"Control\"),\n  shiftKey: nls.localize({\n    key: 'shiftKey.long',\n    comment: ['This is the long form for the Shift key on the keyboard']\n  }, \"Shift\"),\n  altKey: nls.localize({\n    key: 'optKey.long',\n    comment: ['This is the long form for the Alt/Option key on the keyboard']\n  }, \"Option\"),\n  metaKey: nls.localize({\n    key: 'cmdKey.long',\n    comment: ['This is the long form for the Command key on the keyboard']\n  }, \"Command\"),\n  separator: '+'\n}, {\n  ctrlKey: nls.localize({\n    key: 'ctrlKey.long',\n    comment: ['This is the long form for the Control key on the keyboard']\n  }, \"Control\"),\n  shiftKey: nls.localize({\n    key: 'shiftKey.long',\n    comment: ['This is the long form for the Shift key on the keyboard']\n  }, \"Shift\"),\n  altKey: nls.localize({\n    key: 'altKey.long',\n    comment: ['This is the long form for the Alt key on the keyboard']\n  }, \"Alt\"),\n  metaKey: nls.localize({\n    key: 'windowsKey.long',\n    comment: ['This is the long form for the Windows key on the keyboard']\n  }, \"Windows\"),\n  separator: '+'\n}, {\n  ctrlKey: nls.localize({\n    key: 'ctrlKey.long',\n    comment: ['This is the long form for the Control key on the keyboard']\n  }, \"Control\"),\n  shiftKey: nls.localize({\n    key: 'shiftKey.long',\n    comment: ['This is the long form for the Shift key on the keyboard']\n  }, \"Shift\"),\n  altKey: nls.localize({\n    key: 'altKey.long',\n    comment: ['This is the long form for the Alt key on the keyboard']\n  }, \"Alt\"),\n  metaKey: nls.localize({\n    key: 'superKey.long',\n    comment: ['This is the long form for the Super key on the keyboard']\n  }, \"Super\"),\n  separator: '+'\n});\n/**\n * A label provider that prints modifiers in a suitable format for Electron Accelerators.\n * See https://github.com/electron/electron/blob/master/docs/api/accelerator.md\n */\nexport const ElectronAcceleratorLabelProvider = new ModifierLabelProvider({\n  ctrlKey: 'Ctrl',\n  shiftKey: 'Shift',\n  altKey: 'Alt',\n  metaKey: 'Cmd',\n  separator: '+'\n}, {\n  ctrlKey: 'Ctrl',\n  shiftKey: 'Shift',\n  altKey: 'Alt',\n  metaKey: 'Super',\n  separator: '+'\n});\n/**\n * A label provider that prints modifiers in a suitable format for user settings.\n */\nexport const UserSettingsLabelProvider = new ModifierLabelProvider({\n  ctrlKey: 'ctrl',\n  shiftKey: 'shift',\n  altKey: 'alt',\n  metaKey: 'cmd',\n  separator: '+'\n}, {\n  ctrlKey: 'ctrl',\n  shiftKey: 'shift',\n  altKey: 'alt',\n  metaKey: 'win',\n  separator: '+'\n}, {\n  ctrlKey: 'ctrl',\n  shiftKey: 'shift',\n  altKey: 'alt',\n  metaKey: 'meta',\n  separator: '+'\n});\nfunction _simpleAsString(modifiers, key, labels) {\n  if (key === null) {\n    return '';\n  }\n  const result = [];\n  // translate modifier keys: Ctrl-Shift-Alt-Meta\n  if (modifiers.ctrlKey) {\n    result.push(labels.ctrlKey);\n  }\n  if (modifiers.shiftKey) {\n    result.push(labels.shiftKey);\n  }\n  if (modifiers.altKey) {\n    result.push(labels.altKey);\n  }\n  if (modifiers.metaKey) {\n    result.push(labels.metaKey);\n  }\n  // the actual key\n  if (key !== '') {\n    result.push(key);\n  }\n  return result.join(labels.separator);\n}", "map": {"version": 3, "names": ["nls", "Modifier<PERSON><PERSON><PERSON>", "constructor", "mac", "windows", "linux", "modifierLabels", "<PERSON><PERSON><PERSON><PERSON>", "OS", "chords", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "length", "result", "i", "len", "chord", "<PERSON><PERSON><PERSON><PERSON>", "_simpleAsString", "join", "UILabe<PERSON>", "ctrl<PERSON>ey", "shift<PERSON>ey", "altKey", "metaKey", "separator", "localize", "key", "comment", "<PERSON><PERSON><PERSON><PERSON>", "ElectronAcceleratorLabelProvider", "UserSettingsLabelProvider", "modifiers", "labels", "push"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/common/keybindingLabels.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as nls from '../../nls.js';\nexport class ModifierLabelProvider {\n    constructor(mac, windows, linux = windows) {\n        this.modifierLabels = [null]; // index 0 will never me accessed.\n        this.modifierLabels[2 /* OperatingSystem.Macintosh */] = mac;\n        this.modifierLabels[1 /* OperatingSystem.Windows */] = windows;\n        this.modifierLabels[3 /* OperatingSystem.Linux */] = linux;\n    }\n    toLabel(OS, chords, keyLabelProvider) {\n        if (chords.length === 0) {\n            return null;\n        }\n        const result = [];\n        for (let i = 0, len = chords.length; i < len; i++) {\n            const chord = chords[i];\n            const keyLabel = keyLabelProvider(chord);\n            if (keyLabel === null) {\n                // this keybinding cannot be expressed...\n                return null;\n            }\n            result[i] = _simpleAsString(chord, keyLabel, this.modifierLabels[OS]);\n        }\n        return result.join(' ');\n    }\n}\n/**\n * A label provider that prints modifiers in a suitable format for displaying in the UI.\n */\nexport const UILabelProvider = new ModifierLabelProvider({\n    ctrlKey: '\\u2303',\n    shiftKey: '⇧',\n    altKey: '⌥',\n    metaKey: '⌘',\n    separator: '',\n}, {\n    ctrlKey: nls.localize({ key: 'ctrlKey', comment: ['This is the short form for the Control key on the keyboard'] }, \"Ctrl\"),\n    shiftKey: nls.localize({ key: 'shiftKey', comment: ['This is the short form for the Shift key on the keyboard'] }, \"Shift\"),\n    altKey: nls.localize({ key: 'altKey', comment: ['This is the short form for the Alt key on the keyboard'] }, \"Alt\"),\n    metaKey: nls.localize({ key: 'windowsKey', comment: ['This is the short form for the Windows key on the keyboard'] }, \"Windows\"),\n    separator: '+',\n}, {\n    ctrlKey: nls.localize({ key: 'ctrlKey', comment: ['This is the short form for the Control key on the keyboard'] }, \"Ctrl\"),\n    shiftKey: nls.localize({ key: 'shiftKey', comment: ['This is the short form for the Shift key on the keyboard'] }, \"Shift\"),\n    altKey: nls.localize({ key: 'altKey', comment: ['This is the short form for the Alt key on the keyboard'] }, \"Alt\"),\n    metaKey: nls.localize({ key: 'superKey', comment: ['This is the short form for the Super key on the keyboard'] }, \"Super\"),\n    separator: '+',\n});\n/**\n * A label provider that prints modifiers in a suitable format for ARIA.\n */\nexport const AriaLabelProvider = new ModifierLabelProvider({\n    ctrlKey: nls.localize({ key: 'ctrlKey.long', comment: ['This is the long form for the Control key on the keyboard'] }, \"Control\"),\n    shiftKey: nls.localize({ key: 'shiftKey.long', comment: ['This is the long form for the Shift key on the keyboard'] }, \"Shift\"),\n    altKey: nls.localize({ key: 'optKey.long', comment: ['This is the long form for the Alt/Option key on the keyboard'] }, \"Option\"),\n    metaKey: nls.localize({ key: 'cmdKey.long', comment: ['This is the long form for the Command key on the keyboard'] }, \"Command\"),\n    separator: '+',\n}, {\n    ctrlKey: nls.localize({ key: 'ctrlKey.long', comment: ['This is the long form for the Control key on the keyboard'] }, \"Control\"),\n    shiftKey: nls.localize({ key: 'shiftKey.long', comment: ['This is the long form for the Shift key on the keyboard'] }, \"Shift\"),\n    altKey: nls.localize({ key: 'altKey.long', comment: ['This is the long form for the Alt key on the keyboard'] }, \"Alt\"),\n    metaKey: nls.localize({ key: 'windowsKey.long', comment: ['This is the long form for the Windows key on the keyboard'] }, \"Windows\"),\n    separator: '+',\n}, {\n    ctrlKey: nls.localize({ key: 'ctrlKey.long', comment: ['This is the long form for the Control key on the keyboard'] }, \"Control\"),\n    shiftKey: nls.localize({ key: 'shiftKey.long', comment: ['This is the long form for the Shift key on the keyboard'] }, \"Shift\"),\n    altKey: nls.localize({ key: 'altKey.long', comment: ['This is the long form for the Alt key on the keyboard'] }, \"Alt\"),\n    metaKey: nls.localize({ key: 'superKey.long', comment: ['This is the long form for the Super key on the keyboard'] }, \"Super\"),\n    separator: '+',\n});\n/**\n * A label provider that prints modifiers in a suitable format for Electron Accelerators.\n * See https://github.com/electron/electron/blob/master/docs/api/accelerator.md\n */\nexport const ElectronAcceleratorLabelProvider = new ModifierLabelProvider({\n    ctrlKey: 'Ctrl',\n    shiftKey: 'Shift',\n    altKey: 'Alt',\n    metaKey: 'Cmd',\n    separator: '+',\n}, {\n    ctrlKey: 'Ctrl',\n    shiftKey: 'Shift',\n    altKey: 'Alt',\n    metaKey: 'Super',\n    separator: '+',\n});\n/**\n * A label provider that prints modifiers in a suitable format for user settings.\n */\nexport const UserSettingsLabelProvider = new ModifierLabelProvider({\n    ctrlKey: 'ctrl',\n    shiftKey: 'shift',\n    altKey: 'alt',\n    metaKey: 'cmd',\n    separator: '+',\n}, {\n    ctrlKey: 'ctrl',\n    shiftKey: 'shift',\n    altKey: 'alt',\n    metaKey: 'win',\n    separator: '+',\n}, {\n    ctrlKey: 'ctrl',\n    shiftKey: 'shift',\n    altKey: 'alt',\n    metaKey: 'meta',\n    separator: '+',\n});\nfunction _simpleAsString(modifiers, key, labels) {\n    if (key === null) {\n        return '';\n    }\n    const result = [];\n    // translate modifier keys: Ctrl-Shift-Alt-Meta\n    if (modifiers.ctrlKey) {\n        result.push(labels.ctrlKey);\n    }\n    if (modifiers.shiftKey) {\n        result.push(labels.shiftKey);\n    }\n    if (modifiers.altKey) {\n        result.push(labels.altKey);\n    }\n    if (modifiers.metaKey) {\n        result.push(labels.metaKey);\n    }\n    // the actual key\n    if (key !== '') {\n        result.push(key);\n    }\n    return result.join(labels.separator);\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,KAAKA,GAAG,MAAM,cAAc;AACnC,OAAO,MAAMC,qBAAqB,CAAC;EAC/BC,WAAWA,CAACC,GAAG,EAAEC,OAAO,EAAEC,KAAK,GAAGD,OAAO,EAAE;IACvC,IAAI,CAACE,cAAc,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;IAC9B,IAAI,CAACA,cAAc,CAAC,CAAC,CAAC,gCAAgC,GAAGH,GAAG;IAC5D,IAAI,CAACG,cAAc,CAAC,CAAC,CAAC,8BAA8B,GAAGF,OAAO;IAC9D,IAAI,CAACE,cAAc,CAAC,CAAC,CAAC,4BAA4B,GAAGD,KAAK;EAC9D;EACAE,OAAOA,CAACC,EAAE,EAAEC,MAAM,EAAEC,gBAAgB,EAAE;IAClC,IAAID,MAAM,CAACE,MAAM,KAAK,CAAC,EAAE;MACrB,OAAO,IAAI;IACf;IACA,MAAMC,MAAM,GAAG,EAAE;IACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGL,MAAM,CAACE,MAAM,EAAEE,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;MAC/C,MAAME,KAAK,GAAGN,MAAM,CAACI,CAAC,CAAC;MACvB,MAAMG,QAAQ,GAAGN,gBAAgB,CAACK,KAAK,CAAC;MACxC,IAAIC,QAAQ,KAAK,IAAI,EAAE;QACnB;QACA,OAAO,IAAI;MACf;MACAJ,MAAM,CAACC,CAAC,CAAC,GAAGI,eAAe,CAACF,KAAK,EAAEC,QAAQ,EAAE,IAAI,CAACV,cAAc,CAACE,EAAE,CAAC,CAAC;IACzE;IACA,OAAOI,MAAM,CAACM,IAAI,CAAC,GAAG,CAAC;EAC3B;AACJ;AACA;AACA;AACA;AACA,OAAO,MAAMC,eAAe,GAAG,IAAIlB,qBAAqB,CAAC;EACrDmB,OAAO,EAAE,QAAQ;EACjBC,QAAQ,EAAE,GAAG;EACbC,MAAM,EAAE,GAAG;EACXC,OAAO,EAAE,GAAG;EACZC,SAAS,EAAE;AACf,CAAC,EAAE;EACCJ,OAAO,EAAEpB,GAAG,CAACyB,QAAQ,CAAC;IAAEC,GAAG,EAAE,SAAS;IAAEC,OAAO,EAAE,CAAC,4DAA4D;EAAE,CAAC,EAAE,MAAM,CAAC;EAC1HN,QAAQ,EAAErB,GAAG,CAACyB,QAAQ,CAAC;IAAEC,GAAG,EAAE,UAAU;IAAEC,OAAO,EAAE,CAAC,0DAA0D;EAAE,CAAC,EAAE,OAAO,CAAC;EAC3HL,MAAM,EAAEtB,GAAG,CAACyB,QAAQ,CAAC;IAAEC,GAAG,EAAE,QAAQ;IAAEC,OAAO,EAAE,CAAC,wDAAwD;EAAE,CAAC,EAAE,KAAK,CAAC;EACnHJ,OAAO,EAAEvB,GAAG,CAACyB,QAAQ,CAAC;IAAEC,GAAG,EAAE,YAAY;IAAEC,OAAO,EAAE,CAAC,4DAA4D;EAAE,CAAC,EAAE,SAAS,CAAC;EAChIH,SAAS,EAAE;AACf,CAAC,EAAE;EACCJ,OAAO,EAAEpB,GAAG,CAACyB,QAAQ,CAAC;IAAEC,GAAG,EAAE,SAAS;IAAEC,OAAO,EAAE,CAAC,4DAA4D;EAAE,CAAC,EAAE,MAAM,CAAC;EAC1HN,QAAQ,EAAErB,GAAG,CAACyB,QAAQ,CAAC;IAAEC,GAAG,EAAE,UAAU;IAAEC,OAAO,EAAE,CAAC,0DAA0D;EAAE,CAAC,EAAE,OAAO,CAAC;EAC3HL,MAAM,EAAEtB,GAAG,CAACyB,QAAQ,CAAC;IAAEC,GAAG,EAAE,QAAQ;IAAEC,OAAO,EAAE,CAAC,wDAAwD;EAAE,CAAC,EAAE,KAAK,CAAC;EACnHJ,OAAO,EAAEvB,GAAG,CAACyB,QAAQ,CAAC;IAAEC,GAAG,EAAE,UAAU;IAAEC,OAAO,EAAE,CAAC,0DAA0D;EAAE,CAAC,EAAE,OAAO,CAAC;EAC1HH,SAAS,EAAE;AACf,CAAC,CAAC;AACF;AACA;AACA;AACA,OAAO,MAAMI,iBAAiB,GAAG,IAAI3B,qBAAqB,CAAC;EACvDmB,OAAO,EAAEpB,GAAG,CAACyB,QAAQ,CAAC;IAAEC,GAAG,EAAE,cAAc;IAAEC,OAAO,EAAE,CAAC,2DAA2D;EAAE,CAAC,EAAE,SAAS,CAAC;EACjIN,QAAQ,EAAErB,GAAG,CAACyB,QAAQ,CAAC;IAAEC,GAAG,EAAE,eAAe;IAAEC,OAAO,EAAE,CAAC,yDAAyD;EAAE,CAAC,EAAE,OAAO,CAAC;EAC/HL,MAAM,EAAEtB,GAAG,CAACyB,QAAQ,CAAC;IAAEC,GAAG,EAAE,aAAa;IAAEC,OAAO,EAAE,CAAC,8DAA8D;EAAE,CAAC,EAAE,QAAQ,CAAC;EACjIJ,OAAO,EAAEvB,GAAG,CAACyB,QAAQ,CAAC;IAAEC,GAAG,EAAE,aAAa;IAAEC,OAAO,EAAE,CAAC,2DAA2D;EAAE,CAAC,EAAE,SAAS,CAAC;EAChIH,SAAS,EAAE;AACf,CAAC,EAAE;EACCJ,OAAO,EAAEpB,GAAG,CAACyB,QAAQ,CAAC;IAAEC,GAAG,EAAE,cAAc;IAAEC,OAAO,EAAE,CAAC,2DAA2D;EAAE,CAAC,EAAE,SAAS,CAAC;EACjIN,QAAQ,EAAErB,GAAG,CAACyB,QAAQ,CAAC;IAAEC,GAAG,EAAE,eAAe;IAAEC,OAAO,EAAE,CAAC,yDAAyD;EAAE,CAAC,EAAE,OAAO,CAAC;EAC/HL,MAAM,EAAEtB,GAAG,CAACyB,QAAQ,CAAC;IAAEC,GAAG,EAAE,aAAa;IAAEC,OAAO,EAAE,CAAC,uDAAuD;EAAE,CAAC,EAAE,KAAK,CAAC;EACvHJ,OAAO,EAAEvB,GAAG,CAACyB,QAAQ,CAAC;IAAEC,GAAG,EAAE,iBAAiB;IAAEC,OAAO,EAAE,CAAC,2DAA2D;EAAE,CAAC,EAAE,SAAS,CAAC;EACpIH,SAAS,EAAE;AACf,CAAC,EAAE;EACCJ,OAAO,EAAEpB,GAAG,CAACyB,QAAQ,CAAC;IAAEC,GAAG,EAAE,cAAc;IAAEC,OAAO,EAAE,CAAC,2DAA2D;EAAE,CAAC,EAAE,SAAS,CAAC;EACjIN,QAAQ,EAAErB,GAAG,CAACyB,QAAQ,CAAC;IAAEC,GAAG,EAAE,eAAe;IAAEC,OAAO,EAAE,CAAC,yDAAyD;EAAE,CAAC,EAAE,OAAO,CAAC;EAC/HL,MAAM,EAAEtB,GAAG,CAACyB,QAAQ,CAAC;IAAEC,GAAG,EAAE,aAAa;IAAEC,OAAO,EAAE,CAAC,uDAAuD;EAAE,CAAC,EAAE,KAAK,CAAC;EACvHJ,OAAO,EAAEvB,GAAG,CAACyB,QAAQ,CAAC;IAAEC,GAAG,EAAE,eAAe;IAAEC,OAAO,EAAE,CAAC,yDAAyD;EAAE,CAAC,EAAE,OAAO,CAAC;EAC9HH,SAAS,EAAE;AACf,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA,OAAO,MAAMK,gCAAgC,GAAG,IAAI5B,qBAAqB,CAAC;EACtEmB,OAAO,EAAE,MAAM;EACfC,QAAQ,EAAE,OAAO;EACjBC,MAAM,EAAE,KAAK;EACbC,OAAO,EAAE,KAAK;EACdC,SAAS,EAAE;AACf,CAAC,EAAE;EACCJ,OAAO,EAAE,MAAM;EACfC,QAAQ,EAAE,OAAO;EACjBC,MAAM,EAAE,KAAK;EACbC,OAAO,EAAE,OAAO;EAChBC,SAAS,EAAE;AACf,CAAC,CAAC;AACF;AACA;AACA;AACA,OAAO,MAAMM,yBAAyB,GAAG,IAAI7B,qBAAqB,CAAC;EAC/DmB,OAAO,EAAE,MAAM;EACfC,QAAQ,EAAE,OAAO;EACjBC,MAAM,EAAE,KAAK;EACbC,OAAO,EAAE,KAAK;EACdC,SAAS,EAAE;AACf,CAAC,EAAE;EACCJ,OAAO,EAAE,MAAM;EACfC,QAAQ,EAAE,OAAO;EACjBC,MAAM,EAAE,KAAK;EACbC,OAAO,EAAE,KAAK;EACdC,SAAS,EAAE;AACf,CAAC,EAAE;EACCJ,OAAO,EAAE,MAAM;EACfC,QAAQ,EAAE,OAAO;EACjBC,MAAM,EAAE,KAAK;EACbC,OAAO,EAAE,MAAM;EACfC,SAAS,EAAE;AACf,CAAC,CAAC;AACF,SAASP,eAAeA,CAACc,SAAS,EAAEL,GAAG,EAAEM,MAAM,EAAE;EAC7C,IAAIN,GAAG,KAAK,IAAI,EAAE;IACd,OAAO,EAAE;EACb;EACA,MAAMd,MAAM,GAAG,EAAE;EACjB;EACA,IAAImB,SAAS,CAACX,OAAO,EAAE;IACnBR,MAAM,CAACqB,IAAI,CAACD,MAAM,CAACZ,OAAO,CAAC;EAC/B;EACA,IAAIW,SAAS,CAACV,QAAQ,EAAE;IACpBT,MAAM,CAACqB,IAAI,CAACD,MAAM,CAACX,QAAQ,CAAC;EAChC;EACA,IAAIU,SAAS,CAACT,MAAM,EAAE;IAClBV,MAAM,CAACqB,IAAI,CAACD,MAAM,CAACV,MAAM,CAAC;EAC9B;EACA,IAAIS,SAAS,CAACR,OAAO,EAAE;IACnBX,MAAM,CAACqB,IAAI,CAACD,MAAM,CAACT,OAAO,CAAC;EAC/B;EACA;EACA,IAAIG,GAAG,KAAK,EAAE,EAAE;IACZd,MAAM,CAACqB,IAAI,CAACP,GAAG,CAAC;EACpB;EACA,OAAOd,MAAM,CAACM,IAAI,CAACc,MAAM,CAACR,SAAS,CAAC;AACxC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}