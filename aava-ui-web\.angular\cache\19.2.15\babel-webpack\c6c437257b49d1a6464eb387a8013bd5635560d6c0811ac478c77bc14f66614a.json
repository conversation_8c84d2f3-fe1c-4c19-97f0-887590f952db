{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as dom from '../../dom.js';\nimport { DomEmitter } from '../../event.js';\nimport { renderFormattedText, renderText } from '../../formattedTextRenderer.js';\nimport { ActionBar } from '../actionbar/actionbar.js';\nimport * as aria from '../aria/aria.js';\nimport { getBaseLayerHoverDelegate } from '../hover/hoverDelegate2.js';\nimport { getDefaultHoverDelegate } from '../hover/hoverDelegateFactory.js';\nimport { ScrollableElement } from '../scrollbar/scrollableElement.js';\nimport { Widget } from '../widget.js';\nimport { Emitter, Event } from '../../../common/event.js';\nimport { HistoryNavigator } from '../../../common/history.js';\nimport { equals } from '../../../common/objects.js';\nimport './inputBox.css';\nimport * as nls from '../../../../nls.js';\nconst $ = dom.$;\nexport const unthemedInboxStyles = {\n  inputBackground: '#3C3C3C',\n  inputForeground: '#CCCCCC',\n  inputValidationInfoBorder: '#55AAFF',\n  inputValidationInfoBackground: '#063B49',\n  inputValidationWarningBorder: '#B89500',\n  inputValidationWarningBackground: '#352A05',\n  inputValidationErrorBorder: '#BE1100',\n  inputValidationErrorBackground: '#5A1D1D',\n  inputBorder: undefined,\n  inputValidationErrorForeground: undefined,\n  inputValidationInfoForeground: undefined,\n  inputValidationWarningForeground: undefined\n};\nexport class InputBox extends Widget {\n  constructor(container, contextViewProvider, options) {\n    super();\n    this.state = 'idle';\n    this.maxHeight = Number.POSITIVE_INFINITY;\n    this._onDidChange = this._register(new Emitter());\n    this.onDidChange = this._onDidChange.event;\n    this._onDidHeightChange = this._register(new Emitter());\n    this.onDidHeightChange = this._onDidHeightChange.event;\n    this.contextViewProvider = contextViewProvider;\n    this.options = options;\n    this.message = null;\n    this.placeholder = this.options.placeholder || '';\n    this.tooltip = this.options.tooltip ?? (this.placeholder || '');\n    this.ariaLabel = this.options.ariaLabel || '';\n    if (this.options.validationOptions) {\n      this.validation = this.options.validationOptions.validation;\n    }\n    this.element = dom.append(container, $('.monaco-inputbox.idle'));\n    const tagName = this.options.flexibleHeight ? 'textarea' : 'input';\n    const wrapper = dom.append(this.element, $('.ibwrapper'));\n    this.input = dom.append(wrapper, $(tagName + '.input.empty'));\n    this.input.setAttribute('autocorrect', 'off');\n    this.input.setAttribute('autocapitalize', 'off');\n    this.input.setAttribute('spellcheck', 'false');\n    this.onfocus(this.input, () => this.element.classList.add('synthetic-focus'));\n    this.onblur(this.input, () => this.element.classList.remove('synthetic-focus'));\n    if (this.options.flexibleHeight) {\n      this.maxHeight = typeof this.options.flexibleMaxHeight === 'number' ? this.options.flexibleMaxHeight : Number.POSITIVE_INFINITY;\n      this.mirror = dom.append(wrapper, $('div.mirror'));\n      this.mirror.innerText = '\\u00a0';\n      this.scrollableElement = new ScrollableElement(this.element, {\n        vertical: 1 /* ScrollbarVisibility.Auto */\n      });\n      if (this.options.flexibleWidth) {\n        this.input.setAttribute('wrap', 'off');\n        this.mirror.style.whiteSpace = 'pre';\n        this.mirror.style.wordWrap = 'initial';\n      }\n      dom.append(container, this.scrollableElement.getDomNode());\n      this._register(this.scrollableElement);\n      // from ScrollableElement to DOM\n      this._register(this.scrollableElement.onScroll(e => this.input.scrollTop = e.scrollTop));\n      const onSelectionChange = this._register(new DomEmitter(container.ownerDocument, 'selectionchange'));\n      const onAnchoredSelectionChange = Event.filter(onSelectionChange.event, () => {\n        const selection = container.ownerDocument.getSelection();\n        return selection?.anchorNode === wrapper;\n      });\n      // from DOM to ScrollableElement\n      this._register(onAnchoredSelectionChange(this.updateScrollDimensions, this));\n      this._register(this.onDidHeightChange(this.updateScrollDimensions, this));\n    } else {\n      this.input.type = this.options.type || 'text';\n      this.input.setAttribute('wrap', 'off');\n    }\n    if (this.ariaLabel) {\n      this.input.setAttribute('aria-label', this.ariaLabel);\n    }\n    if (this.placeholder && !this.options.showPlaceholderOnFocus) {\n      this.setPlaceHolder(this.placeholder);\n    }\n    if (this.tooltip) {\n      this.setTooltip(this.tooltip);\n    }\n    this.oninput(this.input, () => this.onValueChange());\n    this.onblur(this.input, () => this.onBlur());\n    this.onfocus(this.input, () => this.onFocus());\n    this._register(this.ignoreGesture(this.input));\n    setTimeout(() => this.updateMirror(), 0);\n    // Support actions\n    if (this.options.actions) {\n      this.actionbar = this._register(new ActionBar(this.element));\n      this.actionbar.push(this.options.actions, {\n        icon: true,\n        label: false\n      });\n    }\n    this.applyStyles();\n  }\n  onBlur() {\n    this._hideMessage();\n    if (this.options.showPlaceholderOnFocus) {\n      this.input.setAttribute('placeholder', '');\n    }\n  }\n  onFocus() {\n    this._showMessage();\n    if (this.options.showPlaceholderOnFocus) {\n      this.input.setAttribute('placeholder', this.placeholder || '');\n    }\n  }\n  setPlaceHolder(placeHolder) {\n    this.placeholder = placeHolder;\n    this.input.setAttribute('placeholder', placeHolder);\n  }\n  setTooltip(tooltip) {\n    this.tooltip = tooltip;\n    if (!this.hover) {\n      this.hover = this._register(getBaseLayerHoverDelegate().setupManagedHover(getDefaultHoverDelegate('mouse'), this.input, tooltip));\n    } else {\n      this.hover.update(tooltip);\n    }\n  }\n  get inputElement() {\n    return this.input;\n  }\n  get value() {\n    return this.input.value;\n  }\n  set value(newValue) {\n    if (this.input.value !== newValue) {\n      this.input.value = newValue;\n      this.onValueChange();\n    }\n  }\n  get height() {\n    return typeof this.cachedHeight === 'number' ? this.cachedHeight : dom.getTotalHeight(this.element);\n  }\n  focus() {\n    this.input.focus();\n  }\n  blur() {\n    this.input.blur();\n  }\n  hasFocus() {\n    return dom.isActiveElement(this.input);\n  }\n  select(range = null) {\n    this.input.select();\n    if (range) {\n      this.input.setSelectionRange(range.start, range.end);\n      if (range.end === this.input.value.length) {\n        this.input.scrollLeft = this.input.scrollWidth;\n      }\n    }\n  }\n  isSelectionAtEnd() {\n    return this.input.selectionEnd === this.input.value.length && this.input.selectionStart === this.input.selectionEnd;\n  }\n  getSelection() {\n    const selectionStart = this.input.selectionStart;\n    if (selectionStart === null) {\n      return null;\n    }\n    const selectionEnd = this.input.selectionEnd ?? selectionStart;\n    return {\n      start: selectionStart,\n      end: selectionEnd\n    };\n  }\n  enable() {\n    this.input.removeAttribute('disabled');\n  }\n  disable() {\n    this.blur();\n    this.input.disabled = true;\n    this._hideMessage();\n  }\n  set paddingRight(paddingRight) {\n    // Set width to avoid hint text overlapping buttons\n    this.input.style.width = `calc(100% - ${paddingRight}px)`;\n    if (this.mirror) {\n      this.mirror.style.paddingRight = paddingRight + 'px';\n    }\n  }\n  updateScrollDimensions() {\n    if (typeof this.cachedContentHeight !== 'number' || typeof this.cachedHeight !== 'number' || !this.scrollableElement) {\n      return;\n    }\n    const scrollHeight = this.cachedContentHeight;\n    const height = this.cachedHeight;\n    const scrollTop = this.input.scrollTop;\n    this.scrollableElement.setScrollDimensions({\n      scrollHeight,\n      height\n    });\n    this.scrollableElement.setScrollPosition({\n      scrollTop\n    });\n  }\n  showMessage(message, force) {\n    if (this.state === 'open' && equals(this.message, message)) {\n      // Already showing\n      return;\n    }\n    this.message = message;\n    this.element.classList.remove('idle');\n    this.element.classList.remove('info');\n    this.element.classList.remove('warning');\n    this.element.classList.remove('error');\n    this.element.classList.add(this.classForType(message.type));\n    const styles = this.stylesForType(this.message.type);\n    this.element.style.border = `1px solid ${dom.asCssValueWithDefault(styles.border, 'transparent')}`;\n    if (this.message.content && (this.hasFocus() || force)) {\n      this._showMessage();\n    }\n  }\n  hideMessage() {\n    this.message = null;\n    this.element.classList.remove('info');\n    this.element.classList.remove('warning');\n    this.element.classList.remove('error');\n    this.element.classList.add('idle');\n    this._hideMessage();\n    this.applyStyles();\n  }\n  validate() {\n    let errorMsg = null;\n    if (this.validation) {\n      errorMsg = this.validation(this.value);\n      if (errorMsg) {\n        this.inputElement.setAttribute('aria-invalid', 'true');\n        this.showMessage(errorMsg);\n      } else if (this.inputElement.hasAttribute('aria-invalid')) {\n        this.inputElement.removeAttribute('aria-invalid');\n        this.hideMessage();\n      }\n    }\n    return errorMsg?.type;\n  }\n  stylesForType(type) {\n    const styles = this.options.inputBoxStyles;\n    switch (type) {\n      case 1 /* MessageType.INFO */:\n        return {\n          border: styles.inputValidationInfoBorder,\n          background: styles.inputValidationInfoBackground,\n          foreground: styles.inputValidationInfoForeground\n        };\n      case 2 /* MessageType.WARNING */:\n        return {\n          border: styles.inputValidationWarningBorder,\n          background: styles.inputValidationWarningBackground,\n          foreground: styles.inputValidationWarningForeground\n        };\n      default:\n        return {\n          border: styles.inputValidationErrorBorder,\n          background: styles.inputValidationErrorBackground,\n          foreground: styles.inputValidationErrorForeground\n        };\n    }\n  }\n  classForType(type) {\n    switch (type) {\n      case 1 /* MessageType.INFO */:\n        return 'info';\n      case 2 /* MessageType.WARNING */:\n        return 'warning';\n      default:\n        return 'error';\n    }\n  }\n  _showMessage() {\n    if (!this.contextViewProvider || !this.message) {\n      return;\n    }\n    let div;\n    const layout = () => div.style.width = dom.getTotalWidth(this.element) + 'px';\n    this.contextViewProvider.showContextView({\n      getAnchor: () => this.element,\n      anchorAlignment: 1 /* AnchorAlignment.RIGHT */,\n      render: container => {\n        if (!this.message) {\n          return null;\n        }\n        div = dom.append(container, $('.monaco-inputbox-container'));\n        layout();\n        const renderOptions = {\n          inline: true,\n          className: 'monaco-inputbox-message'\n        };\n        const spanElement = this.message.formatContent ? renderFormattedText(this.message.content, renderOptions) : renderText(this.message.content, renderOptions);\n        spanElement.classList.add(this.classForType(this.message.type));\n        const styles = this.stylesForType(this.message.type);\n        spanElement.style.backgroundColor = styles.background ?? '';\n        spanElement.style.color = styles.foreground ?? '';\n        spanElement.style.border = styles.border ? `1px solid ${styles.border}` : '';\n        dom.append(div, spanElement);\n        return null;\n      },\n      onHide: () => {\n        this.state = 'closed';\n      },\n      layout: layout\n    });\n    // ARIA Support\n    let alertText;\n    if (this.message.type === 3 /* MessageType.ERROR */) {\n      alertText = nls.localize('alertErrorMessage', \"Error: {0}\", this.message.content);\n    } else if (this.message.type === 2 /* MessageType.WARNING */) {\n      alertText = nls.localize('alertWarningMessage', \"Warning: {0}\", this.message.content);\n    } else {\n      alertText = nls.localize('alertInfoMessage', \"Info: {0}\", this.message.content);\n    }\n    aria.alert(alertText);\n    this.state = 'open';\n  }\n  _hideMessage() {\n    if (!this.contextViewProvider) {\n      return;\n    }\n    if (this.state === 'open') {\n      this.contextViewProvider.hideContextView();\n    }\n    this.state = 'idle';\n  }\n  onValueChange() {\n    this._onDidChange.fire(this.value);\n    this.validate();\n    this.updateMirror();\n    this.input.classList.toggle('empty', !this.value);\n    if (this.state === 'open' && this.contextViewProvider) {\n      this.contextViewProvider.layout();\n    }\n  }\n  updateMirror() {\n    if (!this.mirror) {\n      return;\n    }\n    const value = this.value;\n    const lastCharCode = value.charCodeAt(value.length - 1);\n    const suffix = lastCharCode === 10 ? ' ' : '';\n    const mirrorTextContent = (value + suffix).replace(/\\u000c/g, ''); // Don't measure with the form feed character, which messes up sizing\n    if (mirrorTextContent) {\n      this.mirror.textContent = value + suffix;\n    } else {\n      this.mirror.innerText = '\\u00a0';\n    }\n    this.layout();\n  }\n  applyStyles() {\n    const styles = this.options.inputBoxStyles;\n    const background = styles.inputBackground ?? '';\n    const foreground = styles.inputForeground ?? '';\n    const border = styles.inputBorder ?? '';\n    this.element.style.backgroundColor = background;\n    this.element.style.color = foreground;\n    this.input.style.backgroundColor = 'inherit';\n    this.input.style.color = foreground;\n    // there's always a border, even if the color is not set.\n    this.element.style.border = `1px solid ${dom.asCssValueWithDefault(border, 'transparent')}`;\n  }\n  layout() {\n    if (!this.mirror) {\n      return;\n    }\n    const previousHeight = this.cachedContentHeight;\n    this.cachedContentHeight = dom.getTotalHeight(this.mirror);\n    if (previousHeight !== this.cachedContentHeight) {\n      this.cachedHeight = Math.min(this.cachedContentHeight, this.maxHeight);\n      this.input.style.height = this.cachedHeight + 'px';\n      this._onDidHeightChange.fire(this.cachedContentHeight);\n    }\n  }\n  insertAtCursor(text) {\n    const inputElement = this.inputElement;\n    const start = inputElement.selectionStart;\n    const end = inputElement.selectionEnd;\n    const content = inputElement.value;\n    if (start !== null && end !== null) {\n      this.value = content.substr(0, start) + text + content.substr(end);\n      inputElement.setSelectionRange(start + 1, start + 1);\n      this.layout();\n    }\n  }\n  dispose() {\n    this._hideMessage();\n    this.message = null;\n    this.actionbar?.dispose();\n    super.dispose();\n  }\n}\nexport class HistoryInputBox extends InputBox {\n  constructor(container, contextViewProvider, options) {\n    const NLS_PLACEHOLDER_HISTORY_HINT_SUFFIX_NO_PARENS = nls.localize({\n      key: 'history.inputbox.hint.suffix.noparens',\n      comment: ['Text is the suffix of an input field placeholder coming after the action the input field performs, this will be used when the input field ends in a closing parenthesis \")\", for example \"Filter (e.g. text, !exclude)\". The character inserted into the final string is \\u21C5 to represent the up and down arrow keys.']\n    }, ' or {0} for history', `\\u21C5`);\n    const NLS_PLACEHOLDER_HISTORY_HINT_SUFFIX_IN_PARENS = nls.localize({\n      key: 'history.inputbox.hint.suffix.inparens',\n      comment: ['Text is the suffix of an input field placeholder coming after the action the input field performs, this will be used when the input field does NOT end in a closing parenthesis (eg. \"Find\"). The character inserted into the final string is \\u21C5 to represent the up and down arrow keys.']\n    }, ' ({0} for history)', `\\u21C5`);\n    super(container, contextViewProvider, options);\n    this._onDidFocus = this._register(new Emitter());\n    this.onDidFocus = this._onDidFocus.event;\n    this._onDidBlur = this._register(new Emitter());\n    this.onDidBlur = this._onDidBlur.event;\n    this.history = new HistoryNavigator(options.history, 100);\n    // Function to append the history suffix to the placeholder if necessary\n    const addSuffix = () => {\n      if (options.showHistoryHint && options.showHistoryHint() && !this.placeholder.endsWith(NLS_PLACEHOLDER_HISTORY_HINT_SUFFIX_NO_PARENS) && !this.placeholder.endsWith(NLS_PLACEHOLDER_HISTORY_HINT_SUFFIX_IN_PARENS) && this.history.getHistory().length) {\n        const suffix = this.placeholder.endsWith(')') ? NLS_PLACEHOLDER_HISTORY_HINT_SUFFIX_NO_PARENS : NLS_PLACEHOLDER_HISTORY_HINT_SUFFIX_IN_PARENS;\n        const suffixedPlaceholder = this.placeholder + suffix;\n        if (options.showPlaceholderOnFocus && !dom.isActiveElement(this.input)) {\n          this.placeholder = suffixedPlaceholder;\n        } else {\n          this.setPlaceHolder(suffixedPlaceholder);\n        }\n      }\n    };\n    // Spot the change to the textarea class attribute which occurs when it changes between non-empty and empty,\n    // and add the history suffix to the placeholder if not yet present\n    this.observer = new MutationObserver((mutationList, observer) => {\n      mutationList.forEach(mutation => {\n        if (!mutation.target.textContent) {\n          addSuffix();\n        }\n      });\n    });\n    this.observer.observe(this.input, {\n      attributeFilter: ['class']\n    });\n    this.onfocus(this.input, () => addSuffix());\n    this.onblur(this.input, () => {\n      const resetPlaceholder = historyHint => {\n        if (!this.placeholder.endsWith(historyHint)) {\n          return false;\n        } else {\n          const revertedPlaceholder = this.placeholder.slice(0, this.placeholder.length - historyHint.length);\n          if (options.showPlaceholderOnFocus) {\n            this.placeholder = revertedPlaceholder;\n          } else {\n            this.setPlaceHolder(revertedPlaceholder);\n          }\n          return true;\n        }\n      };\n      if (!resetPlaceholder(NLS_PLACEHOLDER_HISTORY_HINT_SUFFIX_IN_PARENS)) {\n        resetPlaceholder(NLS_PLACEHOLDER_HISTORY_HINT_SUFFIX_NO_PARENS);\n      }\n    });\n  }\n  dispose() {\n    super.dispose();\n    if (this.observer) {\n      this.observer.disconnect();\n      this.observer = undefined;\n    }\n  }\n  addToHistory(always) {\n    if (this.value && (always || this.value !== this.getCurrentValue())) {\n      this.history.add(this.value);\n    }\n  }\n  isAtLastInHistory() {\n    return this.history.isLast();\n  }\n  isNowhereInHistory() {\n    return this.history.isNowhere();\n  }\n  showNextValue() {\n    if (!this.history.has(this.value)) {\n      this.addToHistory();\n    }\n    let next = this.getNextValue();\n    if (next) {\n      next = next === this.value ? this.getNextValue() : next;\n    }\n    this.value = next ?? '';\n    aria.status(this.value ? this.value : nls.localize('clearedInput', \"Cleared Input\"));\n  }\n  showPreviousValue() {\n    if (!this.history.has(this.value)) {\n      this.addToHistory();\n    }\n    let previous = this.getPreviousValue();\n    if (previous) {\n      previous = previous === this.value ? this.getPreviousValue() : previous;\n    }\n    if (previous) {\n      this.value = previous;\n      aria.status(this.value);\n    }\n  }\n  setPlaceHolder(placeHolder) {\n    super.setPlaceHolder(placeHolder);\n    this.setTooltip(placeHolder);\n  }\n  onBlur() {\n    super.onBlur();\n    this._onDidBlur.fire();\n  }\n  onFocus() {\n    super.onFocus();\n    this._onDidFocus.fire();\n  }\n  getCurrentValue() {\n    let currentValue = this.history.current();\n    if (!currentValue) {\n      currentValue = this.history.last();\n      this.history.next();\n    }\n    return currentValue;\n  }\n  getPreviousValue() {\n    return this.history.previous() || this.history.first();\n  }\n  getNextValue() {\n    return this.history.next();\n  }\n}", "map": {"version": 3, "names": ["dom", "DomEmitter", "renderFormattedText", "renderText", "ActionBar", "aria", "getBaseLayerHoverDelegate", "getDefaultHoverDelegate", "ScrollableElement", "Widget", "Emitter", "Event", "HistoryNavigator", "equals", "nls", "$", "unthemedInboxStyles", "inputBackground", "inputForeground", "inputValidationInfoBorder", "inputValidationInfoBackground", "inputValidationWarningBorder", "inputValidationWarningBackground", "inputValidationErrorBorder", "inputValidationErrorBackground", "inputBorder", "undefined", "inputValidationErrorForeground", "inputValidationInfoForeground", "inputValidationWarningForeground", "InputBox", "constructor", "container", "contextView<PERSON>rovider", "options", "state", "maxHeight", "Number", "POSITIVE_INFINITY", "_onDidChange", "_register", "onDidChange", "event", "_onDidHeightChange", "onDidHeightChange", "message", "placeholder", "tooltip", "aria<PERSON><PERSON><PERSON>", "validationOptions", "validation", "element", "append", "tagName", "flexibleHeight", "wrapper", "input", "setAttribute", "onfocus", "classList", "add", "onblur", "remove", "flexibleMaxHeight", "mirror", "innerText", "scrollableElement", "vertical", "flexibleWidth", "style", "whiteSpace", "wordWrap", "getDomNode", "onScroll", "e", "scrollTop", "onSelectionChange", "ownerDocument", "onAnchoredSelectionChange", "filter", "selection", "getSelection", "anchorNode", "updateScrollDimensions", "type", "showPlaceholderOnFocus", "setPlaceHolder", "setTooltip", "oninput", "onValueChange", "onBlur", "onFocus", "ignoreGesture", "setTimeout", "updateMirror", "actions", "actionbar", "push", "icon", "label", "applyStyles", "_hideMessage", "_showMessage", "placeHolder", "hover", "setupManagedHover", "update", "inputElement", "value", "newValue", "height", "cachedHeight", "getTotalHeight", "focus", "blur", "hasFocus", "isActiveElement", "select", "range", "setSelectionRange", "start", "end", "length", "scrollLeft", "scrollWidth", "isSelectionAtEnd", "selectionEnd", "selectionStart", "enable", "removeAttribute", "disable", "disabled", "paddingRight", "width", "cachedContentHeight", "scrollHeight", "setScrollDimensions", "setScrollPosition", "showMessage", "force", "classForType", "styles", "stylesForType", "border", "asCssValueWithDefault", "content", "hideMessage", "validate", "errorMsg", "hasAttribute", "inputBoxStyles", "background", "foreground", "div", "layout", "getTotalWidth", "showContextView", "getAnchor", "anchorAlignment", "render", "renderOptions", "inline", "className", "spanElement", "formatContent", "backgroundColor", "color", "onHide", "alertText", "localize", "alert", "hideContextView", "fire", "toggle", "lastCharCode", "charCodeAt", "suffix", "mirrorTextContent", "replace", "textContent", "previousHeight", "Math", "min", "insertAtCursor", "text", "substr", "dispose", "HistoryInputBox", "NLS_PLACEHOLDER_HISTORY_HINT_SUFFIX_NO_PARENS", "key", "comment", "NLS_PLACEHOLDER_HISTORY_HINT_SUFFIX_IN_PARENS", "_onDidFocus", "onDidFocus", "_onDidBlur", "onDidBlur", "history", "addSuffix", "showHistoryHint", "endsWith", "getHistory", "suffixedPlaceholder", "observer", "MutationObserver", "mutationList", "for<PERSON>ach", "mutation", "target", "observe", "attributeFilter", "resetPlaceholder", "historyHint", "revertedPlaceholder", "slice", "disconnect", "addToHistory", "always", "getCurrentValue", "isAtLastInHistory", "isLast", "isNowhereInHistory", "isNowhere", "showNextValue", "has", "next", "getNextValue", "status", "showPreviousValue", "previous", "getPreviousValue", "currentValue", "current", "last", "first"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/browser/ui/inputbox/inputBox.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as dom from '../../dom.js';\nimport { DomEmitter } from '../../event.js';\nimport { renderFormattedText, renderText } from '../../formattedTextRenderer.js';\nimport { ActionBar } from '../actionbar/actionbar.js';\nimport * as aria from '../aria/aria.js';\nimport { getBaseLayerHoverDelegate } from '../hover/hoverDelegate2.js';\nimport { getDefaultHoverDelegate } from '../hover/hoverDelegateFactory.js';\nimport { ScrollableElement } from '../scrollbar/scrollableElement.js';\nimport { Widget } from '../widget.js';\nimport { Emitter, Event } from '../../../common/event.js';\nimport { HistoryNavigator } from '../../../common/history.js';\nimport { equals } from '../../../common/objects.js';\nimport './inputBox.css';\nimport * as nls from '../../../../nls.js';\nconst $ = dom.$;\nexport const unthemedInboxStyles = {\n    inputBackground: '#3C3C3C',\n    inputForeground: '#CCCCCC',\n    inputValidationInfoBorder: '#55AAFF',\n    inputValidationInfoBackground: '#063B49',\n    inputValidationWarningBorder: '#B89500',\n    inputValidationWarningBackground: '#352A05',\n    inputValidationErrorBorder: '#BE1100',\n    inputValidationErrorBackground: '#5A1D1D',\n    inputBorder: undefined,\n    inputValidationErrorForeground: undefined,\n    inputValidationInfoForeground: undefined,\n    inputValidationWarningForeground: undefined\n};\nexport class InputBox extends Widget {\n    constructor(container, contextViewProvider, options) {\n        super();\n        this.state = 'idle';\n        this.maxHeight = Number.POSITIVE_INFINITY;\n        this._onDidChange = this._register(new Emitter());\n        this.onDidChange = this._onDidChange.event;\n        this._onDidHeightChange = this._register(new Emitter());\n        this.onDidHeightChange = this._onDidHeightChange.event;\n        this.contextViewProvider = contextViewProvider;\n        this.options = options;\n        this.message = null;\n        this.placeholder = this.options.placeholder || '';\n        this.tooltip = this.options.tooltip ?? (this.placeholder || '');\n        this.ariaLabel = this.options.ariaLabel || '';\n        if (this.options.validationOptions) {\n            this.validation = this.options.validationOptions.validation;\n        }\n        this.element = dom.append(container, $('.monaco-inputbox.idle'));\n        const tagName = this.options.flexibleHeight ? 'textarea' : 'input';\n        const wrapper = dom.append(this.element, $('.ibwrapper'));\n        this.input = dom.append(wrapper, $(tagName + '.input.empty'));\n        this.input.setAttribute('autocorrect', 'off');\n        this.input.setAttribute('autocapitalize', 'off');\n        this.input.setAttribute('spellcheck', 'false');\n        this.onfocus(this.input, () => this.element.classList.add('synthetic-focus'));\n        this.onblur(this.input, () => this.element.classList.remove('synthetic-focus'));\n        if (this.options.flexibleHeight) {\n            this.maxHeight = typeof this.options.flexibleMaxHeight === 'number' ? this.options.flexibleMaxHeight : Number.POSITIVE_INFINITY;\n            this.mirror = dom.append(wrapper, $('div.mirror'));\n            this.mirror.innerText = '\\u00a0';\n            this.scrollableElement = new ScrollableElement(this.element, { vertical: 1 /* ScrollbarVisibility.Auto */ });\n            if (this.options.flexibleWidth) {\n                this.input.setAttribute('wrap', 'off');\n                this.mirror.style.whiteSpace = 'pre';\n                this.mirror.style.wordWrap = 'initial';\n            }\n            dom.append(container, this.scrollableElement.getDomNode());\n            this._register(this.scrollableElement);\n            // from ScrollableElement to DOM\n            this._register(this.scrollableElement.onScroll(e => this.input.scrollTop = e.scrollTop));\n            const onSelectionChange = this._register(new DomEmitter(container.ownerDocument, 'selectionchange'));\n            const onAnchoredSelectionChange = Event.filter(onSelectionChange.event, () => {\n                const selection = container.ownerDocument.getSelection();\n                return selection?.anchorNode === wrapper;\n            });\n            // from DOM to ScrollableElement\n            this._register(onAnchoredSelectionChange(this.updateScrollDimensions, this));\n            this._register(this.onDidHeightChange(this.updateScrollDimensions, this));\n        }\n        else {\n            this.input.type = this.options.type || 'text';\n            this.input.setAttribute('wrap', 'off');\n        }\n        if (this.ariaLabel) {\n            this.input.setAttribute('aria-label', this.ariaLabel);\n        }\n        if (this.placeholder && !this.options.showPlaceholderOnFocus) {\n            this.setPlaceHolder(this.placeholder);\n        }\n        if (this.tooltip) {\n            this.setTooltip(this.tooltip);\n        }\n        this.oninput(this.input, () => this.onValueChange());\n        this.onblur(this.input, () => this.onBlur());\n        this.onfocus(this.input, () => this.onFocus());\n        this._register(this.ignoreGesture(this.input));\n        setTimeout(() => this.updateMirror(), 0);\n        // Support actions\n        if (this.options.actions) {\n            this.actionbar = this._register(new ActionBar(this.element));\n            this.actionbar.push(this.options.actions, { icon: true, label: false });\n        }\n        this.applyStyles();\n    }\n    onBlur() {\n        this._hideMessage();\n        if (this.options.showPlaceholderOnFocus) {\n            this.input.setAttribute('placeholder', '');\n        }\n    }\n    onFocus() {\n        this._showMessage();\n        if (this.options.showPlaceholderOnFocus) {\n            this.input.setAttribute('placeholder', this.placeholder || '');\n        }\n    }\n    setPlaceHolder(placeHolder) {\n        this.placeholder = placeHolder;\n        this.input.setAttribute('placeholder', placeHolder);\n    }\n    setTooltip(tooltip) {\n        this.tooltip = tooltip;\n        if (!this.hover) {\n            this.hover = this._register(getBaseLayerHoverDelegate().setupManagedHover(getDefaultHoverDelegate('mouse'), this.input, tooltip));\n        }\n        else {\n            this.hover.update(tooltip);\n        }\n    }\n    get inputElement() {\n        return this.input;\n    }\n    get value() {\n        return this.input.value;\n    }\n    set value(newValue) {\n        if (this.input.value !== newValue) {\n            this.input.value = newValue;\n            this.onValueChange();\n        }\n    }\n    get height() {\n        return typeof this.cachedHeight === 'number' ? this.cachedHeight : dom.getTotalHeight(this.element);\n    }\n    focus() {\n        this.input.focus();\n    }\n    blur() {\n        this.input.blur();\n    }\n    hasFocus() {\n        return dom.isActiveElement(this.input);\n    }\n    select(range = null) {\n        this.input.select();\n        if (range) {\n            this.input.setSelectionRange(range.start, range.end);\n            if (range.end === this.input.value.length) {\n                this.input.scrollLeft = this.input.scrollWidth;\n            }\n        }\n    }\n    isSelectionAtEnd() {\n        return this.input.selectionEnd === this.input.value.length && this.input.selectionStart === this.input.selectionEnd;\n    }\n    getSelection() {\n        const selectionStart = this.input.selectionStart;\n        if (selectionStart === null) {\n            return null;\n        }\n        const selectionEnd = this.input.selectionEnd ?? selectionStart;\n        return {\n            start: selectionStart,\n            end: selectionEnd,\n        };\n    }\n    enable() {\n        this.input.removeAttribute('disabled');\n    }\n    disable() {\n        this.blur();\n        this.input.disabled = true;\n        this._hideMessage();\n    }\n    set paddingRight(paddingRight) {\n        // Set width to avoid hint text overlapping buttons\n        this.input.style.width = `calc(100% - ${paddingRight}px)`;\n        if (this.mirror) {\n            this.mirror.style.paddingRight = paddingRight + 'px';\n        }\n    }\n    updateScrollDimensions() {\n        if (typeof this.cachedContentHeight !== 'number' || typeof this.cachedHeight !== 'number' || !this.scrollableElement) {\n            return;\n        }\n        const scrollHeight = this.cachedContentHeight;\n        const height = this.cachedHeight;\n        const scrollTop = this.input.scrollTop;\n        this.scrollableElement.setScrollDimensions({ scrollHeight, height });\n        this.scrollableElement.setScrollPosition({ scrollTop });\n    }\n    showMessage(message, force) {\n        if (this.state === 'open' && equals(this.message, message)) {\n            // Already showing\n            return;\n        }\n        this.message = message;\n        this.element.classList.remove('idle');\n        this.element.classList.remove('info');\n        this.element.classList.remove('warning');\n        this.element.classList.remove('error');\n        this.element.classList.add(this.classForType(message.type));\n        const styles = this.stylesForType(this.message.type);\n        this.element.style.border = `1px solid ${dom.asCssValueWithDefault(styles.border, 'transparent')}`;\n        if (this.message.content && (this.hasFocus() || force)) {\n            this._showMessage();\n        }\n    }\n    hideMessage() {\n        this.message = null;\n        this.element.classList.remove('info');\n        this.element.classList.remove('warning');\n        this.element.classList.remove('error');\n        this.element.classList.add('idle');\n        this._hideMessage();\n        this.applyStyles();\n    }\n    validate() {\n        let errorMsg = null;\n        if (this.validation) {\n            errorMsg = this.validation(this.value);\n            if (errorMsg) {\n                this.inputElement.setAttribute('aria-invalid', 'true');\n                this.showMessage(errorMsg);\n            }\n            else if (this.inputElement.hasAttribute('aria-invalid')) {\n                this.inputElement.removeAttribute('aria-invalid');\n                this.hideMessage();\n            }\n        }\n        return errorMsg?.type;\n    }\n    stylesForType(type) {\n        const styles = this.options.inputBoxStyles;\n        switch (type) {\n            case 1 /* MessageType.INFO */: return { border: styles.inputValidationInfoBorder, background: styles.inputValidationInfoBackground, foreground: styles.inputValidationInfoForeground };\n            case 2 /* MessageType.WARNING */: return { border: styles.inputValidationWarningBorder, background: styles.inputValidationWarningBackground, foreground: styles.inputValidationWarningForeground };\n            default: return { border: styles.inputValidationErrorBorder, background: styles.inputValidationErrorBackground, foreground: styles.inputValidationErrorForeground };\n        }\n    }\n    classForType(type) {\n        switch (type) {\n            case 1 /* MessageType.INFO */: return 'info';\n            case 2 /* MessageType.WARNING */: return 'warning';\n            default: return 'error';\n        }\n    }\n    _showMessage() {\n        if (!this.contextViewProvider || !this.message) {\n            return;\n        }\n        let div;\n        const layout = () => div.style.width = dom.getTotalWidth(this.element) + 'px';\n        this.contextViewProvider.showContextView({\n            getAnchor: () => this.element,\n            anchorAlignment: 1 /* AnchorAlignment.RIGHT */,\n            render: (container) => {\n                if (!this.message) {\n                    return null;\n                }\n                div = dom.append(container, $('.monaco-inputbox-container'));\n                layout();\n                const renderOptions = {\n                    inline: true,\n                    className: 'monaco-inputbox-message'\n                };\n                const spanElement = (this.message.formatContent\n                    ? renderFormattedText(this.message.content, renderOptions)\n                    : renderText(this.message.content, renderOptions));\n                spanElement.classList.add(this.classForType(this.message.type));\n                const styles = this.stylesForType(this.message.type);\n                spanElement.style.backgroundColor = styles.background ?? '';\n                spanElement.style.color = styles.foreground ?? '';\n                spanElement.style.border = styles.border ? `1px solid ${styles.border}` : '';\n                dom.append(div, spanElement);\n                return null;\n            },\n            onHide: () => {\n                this.state = 'closed';\n            },\n            layout: layout\n        });\n        // ARIA Support\n        let alertText;\n        if (this.message.type === 3 /* MessageType.ERROR */) {\n            alertText = nls.localize('alertErrorMessage', \"Error: {0}\", this.message.content);\n        }\n        else if (this.message.type === 2 /* MessageType.WARNING */) {\n            alertText = nls.localize('alertWarningMessage', \"Warning: {0}\", this.message.content);\n        }\n        else {\n            alertText = nls.localize('alertInfoMessage', \"Info: {0}\", this.message.content);\n        }\n        aria.alert(alertText);\n        this.state = 'open';\n    }\n    _hideMessage() {\n        if (!this.contextViewProvider) {\n            return;\n        }\n        if (this.state === 'open') {\n            this.contextViewProvider.hideContextView();\n        }\n        this.state = 'idle';\n    }\n    onValueChange() {\n        this._onDidChange.fire(this.value);\n        this.validate();\n        this.updateMirror();\n        this.input.classList.toggle('empty', !this.value);\n        if (this.state === 'open' && this.contextViewProvider) {\n            this.contextViewProvider.layout();\n        }\n    }\n    updateMirror() {\n        if (!this.mirror) {\n            return;\n        }\n        const value = this.value;\n        const lastCharCode = value.charCodeAt(value.length - 1);\n        const suffix = lastCharCode === 10 ? ' ' : '';\n        const mirrorTextContent = (value + suffix)\n            .replace(/\\u000c/g, ''); // Don't measure with the form feed character, which messes up sizing\n        if (mirrorTextContent) {\n            this.mirror.textContent = value + suffix;\n        }\n        else {\n            this.mirror.innerText = '\\u00a0';\n        }\n        this.layout();\n    }\n    applyStyles() {\n        const styles = this.options.inputBoxStyles;\n        const background = styles.inputBackground ?? '';\n        const foreground = styles.inputForeground ?? '';\n        const border = styles.inputBorder ?? '';\n        this.element.style.backgroundColor = background;\n        this.element.style.color = foreground;\n        this.input.style.backgroundColor = 'inherit';\n        this.input.style.color = foreground;\n        // there's always a border, even if the color is not set.\n        this.element.style.border = `1px solid ${dom.asCssValueWithDefault(border, 'transparent')}`;\n    }\n    layout() {\n        if (!this.mirror) {\n            return;\n        }\n        const previousHeight = this.cachedContentHeight;\n        this.cachedContentHeight = dom.getTotalHeight(this.mirror);\n        if (previousHeight !== this.cachedContentHeight) {\n            this.cachedHeight = Math.min(this.cachedContentHeight, this.maxHeight);\n            this.input.style.height = this.cachedHeight + 'px';\n            this._onDidHeightChange.fire(this.cachedContentHeight);\n        }\n    }\n    insertAtCursor(text) {\n        const inputElement = this.inputElement;\n        const start = inputElement.selectionStart;\n        const end = inputElement.selectionEnd;\n        const content = inputElement.value;\n        if (start !== null && end !== null) {\n            this.value = content.substr(0, start) + text + content.substr(end);\n            inputElement.setSelectionRange(start + 1, start + 1);\n            this.layout();\n        }\n    }\n    dispose() {\n        this._hideMessage();\n        this.message = null;\n        this.actionbar?.dispose();\n        super.dispose();\n    }\n}\nexport class HistoryInputBox extends InputBox {\n    constructor(container, contextViewProvider, options) {\n        const NLS_PLACEHOLDER_HISTORY_HINT_SUFFIX_NO_PARENS = nls.localize({\n            key: 'history.inputbox.hint.suffix.noparens',\n            comment: ['Text is the suffix of an input field placeholder coming after the action the input field performs, this will be used when the input field ends in a closing parenthesis \")\", for example \"Filter (e.g. text, !exclude)\". The character inserted into the final string is \\u21C5 to represent the up and down arrow keys.']\n        }, ' or {0} for history', `\\u21C5`);\n        const NLS_PLACEHOLDER_HISTORY_HINT_SUFFIX_IN_PARENS = nls.localize({\n            key: 'history.inputbox.hint.suffix.inparens',\n            comment: ['Text is the suffix of an input field placeholder coming after the action the input field performs, this will be used when the input field does NOT end in a closing parenthesis (eg. \"Find\"). The character inserted into the final string is \\u21C5 to represent the up and down arrow keys.']\n        }, ' ({0} for history)', `\\u21C5`);\n        super(container, contextViewProvider, options);\n        this._onDidFocus = this._register(new Emitter());\n        this.onDidFocus = this._onDidFocus.event;\n        this._onDidBlur = this._register(new Emitter());\n        this.onDidBlur = this._onDidBlur.event;\n        this.history = new HistoryNavigator(options.history, 100);\n        // Function to append the history suffix to the placeholder if necessary\n        const addSuffix = () => {\n            if (options.showHistoryHint && options.showHistoryHint() && !this.placeholder.endsWith(NLS_PLACEHOLDER_HISTORY_HINT_SUFFIX_NO_PARENS) && !this.placeholder.endsWith(NLS_PLACEHOLDER_HISTORY_HINT_SUFFIX_IN_PARENS) && this.history.getHistory().length) {\n                const suffix = this.placeholder.endsWith(')') ? NLS_PLACEHOLDER_HISTORY_HINT_SUFFIX_NO_PARENS : NLS_PLACEHOLDER_HISTORY_HINT_SUFFIX_IN_PARENS;\n                const suffixedPlaceholder = this.placeholder + suffix;\n                if (options.showPlaceholderOnFocus && !dom.isActiveElement(this.input)) {\n                    this.placeholder = suffixedPlaceholder;\n                }\n                else {\n                    this.setPlaceHolder(suffixedPlaceholder);\n                }\n            }\n        };\n        // Spot the change to the textarea class attribute which occurs when it changes between non-empty and empty,\n        // and add the history suffix to the placeholder if not yet present\n        this.observer = new MutationObserver((mutationList, observer) => {\n            mutationList.forEach((mutation) => {\n                if (!mutation.target.textContent) {\n                    addSuffix();\n                }\n            });\n        });\n        this.observer.observe(this.input, { attributeFilter: ['class'] });\n        this.onfocus(this.input, () => addSuffix());\n        this.onblur(this.input, () => {\n            const resetPlaceholder = (historyHint) => {\n                if (!this.placeholder.endsWith(historyHint)) {\n                    return false;\n                }\n                else {\n                    const revertedPlaceholder = this.placeholder.slice(0, this.placeholder.length - historyHint.length);\n                    if (options.showPlaceholderOnFocus) {\n                        this.placeholder = revertedPlaceholder;\n                    }\n                    else {\n                        this.setPlaceHolder(revertedPlaceholder);\n                    }\n                    return true;\n                }\n            };\n            if (!resetPlaceholder(NLS_PLACEHOLDER_HISTORY_HINT_SUFFIX_IN_PARENS)) {\n                resetPlaceholder(NLS_PLACEHOLDER_HISTORY_HINT_SUFFIX_NO_PARENS);\n            }\n        });\n    }\n    dispose() {\n        super.dispose();\n        if (this.observer) {\n            this.observer.disconnect();\n            this.observer = undefined;\n        }\n    }\n    addToHistory(always) {\n        if (this.value && (always || this.value !== this.getCurrentValue())) {\n            this.history.add(this.value);\n        }\n    }\n    isAtLastInHistory() {\n        return this.history.isLast();\n    }\n    isNowhereInHistory() {\n        return this.history.isNowhere();\n    }\n    showNextValue() {\n        if (!this.history.has(this.value)) {\n            this.addToHistory();\n        }\n        let next = this.getNextValue();\n        if (next) {\n            next = next === this.value ? this.getNextValue() : next;\n        }\n        this.value = next ?? '';\n        aria.status(this.value ? this.value : nls.localize('clearedInput', \"Cleared Input\"));\n    }\n    showPreviousValue() {\n        if (!this.history.has(this.value)) {\n            this.addToHistory();\n        }\n        let previous = this.getPreviousValue();\n        if (previous) {\n            previous = previous === this.value ? this.getPreviousValue() : previous;\n        }\n        if (previous) {\n            this.value = previous;\n            aria.status(this.value);\n        }\n    }\n    setPlaceHolder(placeHolder) {\n        super.setPlaceHolder(placeHolder);\n        this.setTooltip(placeHolder);\n    }\n    onBlur() {\n        super.onBlur();\n        this._onDidBlur.fire();\n    }\n    onFocus() {\n        super.onFocus();\n        this._onDidFocus.fire();\n    }\n    getCurrentValue() {\n        let currentValue = this.history.current();\n        if (!currentValue) {\n            currentValue = this.history.last();\n            this.history.next();\n        }\n        return currentValue;\n    }\n    getPreviousValue() {\n        return this.history.previous() || this.history.first();\n    }\n    getNextValue() {\n        return this.history.next();\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,KAAKA,GAAG,MAAM,cAAc;AACnC,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,SAASC,mBAAmB,EAAEC,UAAU,QAAQ,gCAAgC;AAChF,SAASC,SAAS,QAAQ,2BAA2B;AACrD,OAAO,KAAKC,IAAI,MAAM,iBAAiB;AACvC,SAASC,yBAAyB,QAAQ,4BAA4B;AACtE,SAASC,uBAAuB,QAAQ,kCAAkC;AAC1E,SAASC,iBAAiB,QAAQ,mCAAmC;AACrE,SAASC,MAAM,QAAQ,cAAc;AACrC,SAASC,OAAO,EAAEC,KAAK,QAAQ,0BAA0B;AACzD,SAASC,gBAAgB,QAAQ,4BAA4B;AAC7D,SAASC,MAAM,QAAQ,4BAA4B;AACnD,OAAO,gBAAgB;AACvB,OAAO,KAAKC,GAAG,MAAM,oBAAoB;AACzC,MAAMC,CAAC,GAAGf,GAAG,CAACe,CAAC;AACf,OAAO,MAAMC,mBAAmB,GAAG;EAC/BC,eAAe,EAAE,SAAS;EAC1BC,eAAe,EAAE,SAAS;EAC1BC,yBAAyB,EAAE,SAAS;EACpCC,6BAA6B,EAAE,SAAS;EACxCC,4BAA4B,EAAE,SAAS;EACvCC,gCAAgC,EAAE,SAAS;EAC3CC,0BAA0B,EAAE,SAAS;EACrCC,8BAA8B,EAAE,SAAS;EACzCC,WAAW,EAAEC,SAAS;EACtBC,8BAA8B,EAAED,SAAS;EACzCE,6BAA6B,EAAEF,SAAS;EACxCG,gCAAgC,EAAEH;AACtC,CAAC;AACD,OAAO,MAAMI,QAAQ,SAASrB,MAAM,CAAC;EACjCsB,WAAWA,CAACC,SAAS,EAAEC,mBAAmB,EAAEC,OAAO,EAAE;IACjD,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,KAAK,GAAG,MAAM;IACnB,IAAI,CAACC,SAAS,GAAGC,MAAM,CAACC,iBAAiB;IACzC,IAAI,CAACC,YAAY,GAAG,IAAI,CAACC,SAAS,CAAC,IAAI9B,OAAO,CAAC,CAAC,CAAC;IACjD,IAAI,CAAC+B,WAAW,GAAG,IAAI,CAACF,YAAY,CAACG,KAAK;IAC1C,IAAI,CAACC,kBAAkB,GAAG,IAAI,CAACH,SAAS,CAAC,IAAI9B,OAAO,CAAC,CAAC,CAAC;IACvD,IAAI,CAACkC,iBAAiB,GAAG,IAAI,CAACD,kBAAkB,CAACD,KAAK;IACtD,IAAI,CAACT,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACW,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,WAAW,GAAG,IAAI,CAACZ,OAAO,CAACY,WAAW,IAAI,EAAE;IACjD,IAAI,CAACC,OAAO,GAAG,IAAI,CAACb,OAAO,CAACa,OAAO,KAAK,IAAI,CAACD,WAAW,IAAI,EAAE,CAAC;IAC/D,IAAI,CAACE,SAAS,GAAG,IAAI,CAACd,OAAO,CAACc,SAAS,IAAI,EAAE;IAC7C,IAAI,IAAI,CAACd,OAAO,CAACe,iBAAiB,EAAE;MAChC,IAAI,CAACC,UAAU,GAAG,IAAI,CAAChB,OAAO,CAACe,iBAAiB,CAACC,UAAU;IAC/D;IACA,IAAI,CAACC,OAAO,GAAGnD,GAAG,CAACoD,MAAM,CAACpB,SAAS,EAAEjB,CAAC,CAAC,uBAAuB,CAAC,CAAC;IAChE,MAAMsC,OAAO,GAAG,IAAI,CAACnB,OAAO,CAACoB,cAAc,GAAG,UAAU,GAAG,OAAO;IAClE,MAAMC,OAAO,GAAGvD,GAAG,CAACoD,MAAM,CAAC,IAAI,CAACD,OAAO,EAAEpC,CAAC,CAAC,YAAY,CAAC,CAAC;IACzD,IAAI,CAACyC,KAAK,GAAGxD,GAAG,CAACoD,MAAM,CAACG,OAAO,EAAExC,CAAC,CAACsC,OAAO,GAAG,cAAc,CAAC,CAAC;IAC7D,IAAI,CAACG,KAAK,CAACC,YAAY,CAAC,aAAa,EAAE,KAAK,CAAC;IAC7C,IAAI,CAACD,KAAK,CAACC,YAAY,CAAC,gBAAgB,EAAE,KAAK,CAAC;IAChD,IAAI,CAACD,KAAK,CAACC,YAAY,CAAC,YAAY,EAAE,OAAO,CAAC;IAC9C,IAAI,CAACC,OAAO,CAAC,IAAI,CAACF,KAAK,EAAE,MAAM,IAAI,CAACL,OAAO,CAACQ,SAAS,CAACC,GAAG,CAAC,iBAAiB,CAAC,CAAC;IAC7E,IAAI,CAACC,MAAM,CAAC,IAAI,CAACL,KAAK,EAAE,MAAM,IAAI,CAACL,OAAO,CAACQ,SAAS,CAACG,MAAM,CAAC,iBAAiB,CAAC,CAAC;IAC/E,IAAI,IAAI,CAAC5B,OAAO,CAACoB,cAAc,EAAE;MAC7B,IAAI,CAAClB,SAAS,GAAG,OAAO,IAAI,CAACF,OAAO,CAAC6B,iBAAiB,KAAK,QAAQ,GAAG,IAAI,CAAC7B,OAAO,CAAC6B,iBAAiB,GAAG1B,MAAM,CAACC,iBAAiB;MAC/H,IAAI,CAAC0B,MAAM,GAAGhE,GAAG,CAACoD,MAAM,CAACG,OAAO,EAAExC,CAAC,CAAC,YAAY,CAAC,CAAC;MAClD,IAAI,CAACiD,MAAM,CAACC,SAAS,GAAG,QAAQ;MAChC,IAAI,CAACC,iBAAiB,GAAG,IAAI1D,iBAAiB,CAAC,IAAI,CAAC2C,OAAO,EAAE;QAAEgB,QAAQ,EAAE,CAAC,CAAC;MAA+B,CAAC,CAAC;MAC5G,IAAI,IAAI,CAACjC,OAAO,CAACkC,aAAa,EAAE;QAC5B,IAAI,CAACZ,KAAK,CAACC,YAAY,CAAC,MAAM,EAAE,KAAK,CAAC;QACtC,IAAI,CAACO,MAAM,CAACK,KAAK,CAACC,UAAU,GAAG,KAAK;QACpC,IAAI,CAACN,MAAM,CAACK,KAAK,CAACE,QAAQ,GAAG,SAAS;MAC1C;MACAvE,GAAG,CAACoD,MAAM,CAACpB,SAAS,EAAE,IAAI,CAACkC,iBAAiB,CAACM,UAAU,CAAC,CAAC,CAAC;MAC1D,IAAI,CAAChC,SAAS,CAAC,IAAI,CAAC0B,iBAAiB,CAAC;MACtC;MACA,IAAI,CAAC1B,SAAS,CAAC,IAAI,CAAC0B,iBAAiB,CAACO,QAAQ,CAACC,CAAC,IAAI,IAAI,CAAClB,KAAK,CAACmB,SAAS,GAAGD,CAAC,CAACC,SAAS,CAAC,CAAC;MACxF,MAAMC,iBAAiB,GAAG,IAAI,CAACpC,SAAS,CAAC,IAAIvC,UAAU,CAAC+B,SAAS,CAAC6C,aAAa,EAAE,iBAAiB,CAAC,CAAC;MACpG,MAAMC,yBAAyB,GAAGnE,KAAK,CAACoE,MAAM,CAACH,iBAAiB,CAAClC,KAAK,EAAE,MAAM;QAC1E,MAAMsC,SAAS,GAAGhD,SAAS,CAAC6C,aAAa,CAACI,YAAY,CAAC,CAAC;QACxD,OAAOD,SAAS,EAAEE,UAAU,KAAK3B,OAAO;MAC5C,CAAC,CAAC;MACF;MACA,IAAI,CAACf,SAAS,CAACsC,yBAAyB,CAAC,IAAI,CAACK,sBAAsB,EAAE,IAAI,CAAC,CAAC;MAC5E,IAAI,CAAC3C,SAAS,CAAC,IAAI,CAACI,iBAAiB,CAAC,IAAI,CAACuC,sBAAsB,EAAE,IAAI,CAAC,CAAC;IAC7E,CAAC,MACI;MACD,IAAI,CAAC3B,KAAK,CAAC4B,IAAI,GAAG,IAAI,CAAClD,OAAO,CAACkD,IAAI,IAAI,MAAM;MAC7C,IAAI,CAAC5B,KAAK,CAACC,YAAY,CAAC,MAAM,EAAE,KAAK,CAAC;IAC1C;IACA,IAAI,IAAI,CAACT,SAAS,EAAE;MAChB,IAAI,CAACQ,KAAK,CAACC,YAAY,CAAC,YAAY,EAAE,IAAI,CAACT,SAAS,CAAC;IACzD;IACA,IAAI,IAAI,CAACF,WAAW,IAAI,CAAC,IAAI,CAACZ,OAAO,CAACmD,sBAAsB,EAAE;MAC1D,IAAI,CAACC,cAAc,CAAC,IAAI,CAACxC,WAAW,CAAC;IACzC;IACA,IAAI,IAAI,CAACC,OAAO,EAAE;MACd,IAAI,CAACwC,UAAU,CAAC,IAAI,CAACxC,OAAO,CAAC;IACjC;IACA,IAAI,CAACyC,OAAO,CAAC,IAAI,CAAChC,KAAK,EAAE,MAAM,IAAI,CAACiC,aAAa,CAAC,CAAC,CAAC;IACpD,IAAI,CAAC5B,MAAM,CAAC,IAAI,CAACL,KAAK,EAAE,MAAM,IAAI,CAACkC,MAAM,CAAC,CAAC,CAAC;IAC5C,IAAI,CAAChC,OAAO,CAAC,IAAI,CAACF,KAAK,EAAE,MAAM,IAAI,CAACmC,OAAO,CAAC,CAAC,CAAC;IAC9C,IAAI,CAACnD,SAAS,CAAC,IAAI,CAACoD,aAAa,CAAC,IAAI,CAACpC,KAAK,CAAC,CAAC;IAC9CqC,UAAU,CAAC,MAAM,IAAI,CAACC,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC;IACxC;IACA,IAAI,IAAI,CAAC5D,OAAO,CAAC6D,OAAO,EAAE;MACtB,IAAI,CAACC,SAAS,GAAG,IAAI,CAACxD,SAAS,CAAC,IAAIpC,SAAS,CAAC,IAAI,CAAC+C,OAAO,CAAC,CAAC;MAC5D,IAAI,CAAC6C,SAAS,CAACC,IAAI,CAAC,IAAI,CAAC/D,OAAO,CAAC6D,OAAO,EAAE;QAAEG,IAAI,EAAE,IAAI;QAAEC,KAAK,EAAE;MAAM,CAAC,CAAC;IAC3E;IACA,IAAI,CAACC,WAAW,CAAC,CAAC;EACtB;EACAV,MAAMA,CAAA,EAAG;IACL,IAAI,CAACW,YAAY,CAAC,CAAC;IACnB,IAAI,IAAI,CAACnE,OAAO,CAACmD,sBAAsB,EAAE;MACrC,IAAI,CAAC7B,KAAK,CAACC,YAAY,CAAC,aAAa,EAAE,EAAE,CAAC;IAC9C;EACJ;EACAkC,OAAOA,CAAA,EAAG;IACN,IAAI,CAACW,YAAY,CAAC,CAAC;IACnB,IAAI,IAAI,CAACpE,OAAO,CAACmD,sBAAsB,EAAE;MACrC,IAAI,CAAC7B,KAAK,CAACC,YAAY,CAAC,aAAa,EAAE,IAAI,CAACX,WAAW,IAAI,EAAE,CAAC;IAClE;EACJ;EACAwC,cAAcA,CAACiB,WAAW,EAAE;IACxB,IAAI,CAACzD,WAAW,GAAGyD,WAAW;IAC9B,IAAI,CAAC/C,KAAK,CAACC,YAAY,CAAC,aAAa,EAAE8C,WAAW,CAAC;EACvD;EACAhB,UAAUA,CAACxC,OAAO,EAAE;IAChB,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC,IAAI,CAACyD,KAAK,EAAE;MACb,IAAI,CAACA,KAAK,GAAG,IAAI,CAAChE,SAAS,CAAClC,yBAAyB,CAAC,CAAC,CAACmG,iBAAiB,CAAClG,uBAAuB,CAAC,OAAO,CAAC,EAAE,IAAI,CAACiD,KAAK,EAAET,OAAO,CAAC,CAAC;IACrI,CAAC,MACI;MACD,IAAI,CAACyD,KAAK,CAACE,MAAM,CAAC3D,OAAO,CAAC;IAC9B;EACJ;EACA,IAAI4D,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACnD,KAAK;EACrB;EACA,IAAIoD,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACpD,KAAK,CAACoD,KAAK;EAC3B;EACA,IAAIA,KAAKA,CAACC,QAAQ,EAAE;IAChB,IAAI,IAAI,CAACrD,KAAK,CAACoD,KAAK,KAAKC,QAAQ,EAAE;MAC/B,IAAI,CAACrD,KAAK,CAACoD,KAAK,GAAGC,QAAQ;MAC3B,IAAI,CAACpB,aAAa,CAAC,CAAC;IACxB;EACJ;EACA,IAAIqB,MAAMA,CAAA,EAAG;IACT,OAAO,OAAO,IAAI,CAACC,YAAY,KAAK,QAAQ,GAAG,IAAI,CAACA,YAAY,GAAG/G,GAAG,CAACgH,cAAc,CAAC,IAAI,CAAC7D,OAAO,CAAC;EACvG;EACA8D,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACzD,KAAK,CAACyD,KAAK,CAAC,CAAC;EACtB;EACAC,IAAIA,CAAA,EAAG;IACH,IAAI,CAAC1D,KAAK,CAAC0D,IAAI,CAAC,CAAC;EACrB;EACAC,QAAQA,CAAA,EAAG;IACP,OAAOnH,GAAG,CAACoH,eAAe,CAAC,IAAI,CAAC5D,KAAK,CAAC;EAC1C;EACA6D,MAAMA,CAACC,KAAK,GAAG,IAAI,EAAE;IACjB,IAAI,CAAC9D,KAAK,CAAC6D,MAAM,CAAC,CAAC;IACnB,IAAIC,KAAK,EAAE;MACP,IAAI,CAAC9D,KAAK,CAAC+D,iBAAiB,CAACD,KAAK,CAACE,KAAK,EAAEF,KAAK,CAACG,GAAG,CAAC;MACpD,IAAIH,KAAK,CAACG,GAAG,KAAK,IAAI,CAACjE,KAAK,CAACoD,KAAK,CAACc,MAAM,EAAE;QACvC,IAAI,CAAClE,KAAK,CAACmE,UAAU,GAAG,IAAI,CAACnE,KAAK,CAACoE,WAAW;MAClD;IACJ;EACJ;EACAC,gBAAgBA,CAAA,EAAG;IACf,OAAO,IAAI,CAACrE,KAAK,CAACsE,YAAY,KAAK,IAAI,CAACtE,KAAK,CAACoD,KAAK,CAACc,MAAM,IAAI,IAAI,CAAClE,KAAK,CAACuE,cAAc,KAAK,IAAI,CAACvE,KAAK,CAACsE,YAAY;EACvH;EACA7C,YAAYA,CAAA,EAAG;IACX,MAAM8C,cAAc,GAAG,IAAI,CAACvE,KAAK,CAACuE,cAAc;IAChD,IAAIA,cAAc,KAAK,IAAI,EAAE;MACzB,OAAO,IAAI;IACf;IACA,MAAMD,YAAY,GAAG,IAAI,CAACtE,KAAK,CAACsE,YAAY,IAAIC,cAAc;IAC9D,OAAO;MACHP,KAAK,EAAEO,cAAc;MACrBN,GAAG,EAAEK;IACT,CAAC;EACL;EACAE,MAAMA,CAAA,EAAG;IACL,IAAI,CAACxE,KAAK,CAACyE,eAAe,CAAC,UAAU,CAAC;EAC1C;EACAC,OAAOA,CAAA,EAAG;IACN,IAAI,CAAChB,IAAI,CAAC,CAAC;IACX,IAAI,CAAC1D,KAAK,CAAC2E,QAAQ,GAAG,IAAI;IAC1B,IAAI,CAAC9B,YAAY,CAAC,CAAC;EACvB;EACA,IAAI+B,YAAYA,CAACA,YAAY,EAAE;IAC3B;IACA,IAAI,CAAC5E,KAAK,CAACa,KAAK,CAACgE,KAAK,GAAG,eAAeD,YAAY,KAAK;IACzD,IAAI,IAAI,CAACpE,MAAM,EAAE;MACb,IAAI,CAACA,MAAM,CAACK,KAAK,CAAC+D,YAAY,GAAGA,YAAY,GAAG,IAAI;IACxD;EACJ;EACAjD,sBAAsBA,CAAA,EAAG;IACrB,IAAI,OAAO,IAAI,CAACmD,mBAAmB,KAAK,QAAQ,IAAI,OAAO,IAAI,CAACvB,YAAY,KAAK,QAAQ,IAAI,CAAC,IAAI,CAAC7C,iBAAiB,EAAE;MAClH;IACJ;IACA,MAAMqE,YAAY,GAAG,IAAI,CAACD,mBAAmB;IAC7C,MAAMxB,MAAM,GAAG,IAAI,CAACC,YAAY;IAChC,MAAMpC,SAAS,GAAG,IAAI,CAACnB,KAAK,CAACmB,SAAS;IACtC,IAAI,CAACT,iBAAiB,CAACsE,mBAAmB,CAAC;MAAED,YAAY;MAAEzB;IAAO,CAAC,CAAC;IACpE,IAAI,CAAC5C,iBAAiB,CAACuE,iBAAiB,CAAC;MAAE9D;IAAU,CAAC,CAAC;EAC3D;EACA+D,WAAWA,CAAC7F,OAAO,EAAE8F,KAAK,EAAE;IACxB,IAAI,IAAI,CAACxG,KAAK,KAAK,MAAM,IAAItB,MAAM,CAAC,IAAI,CAACgC,OAAO,EAAEA,OAAO,CAAC,EAAE;MACxD;MACA;IACJ;IACA,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACM,OAAO,CAACQ,SAAS,CAACG,MAAM,CAAC,MAAM,CAAC;IACrC,IAAI,CAACX,OAAO,CAACQ,SAAS,CAACG,MAAM,CAAC,MAAM,CAAC;IACrC,IAAI,CAACX,OAAO,CAACQ,SAAS,CAACG,MAAM,CAAC,SAAS,CAAC;IACxC,IAAI,CAACX,OAAO,CAACQ,SAAS,CAACG,MAAM,CAAC,OAAO,CAAC;IACtC,IAAI,CAACX,OAAO,CAACQ,SAAS,CAACC,GAAG,CAAC,IAAI,CAACgF,YAAY,CAAC/F,OAAO,CAACuC,IAAI,CAAC,CAAC;IAC3D,MAAMyD,MAAM,GAAG,IAAI,CAACC,aAAa,CAAC,IAAI,CAACjG,OAAO,CAACuC,IAAI,CAAC;IACpD,IAAI,CAACjC,OAAO,CAACkB,KAAK,CAAC0E,MAAM,GAAG,aAAa/I,GAAG,CAACgJ,qBAAqB,CAACH,MAAM,CAACE,MAAM,EAAE,aAAa,CAAC,EAAE;IAClG,IAAI,IAAI,CAAClG,OAAO,CAACoG,OAAO,KAAK,IAAI,CAAC9B,QAAQ,CAAC,CAAC,IAAIwB,KAAK,CAAC,EAAE;MACpD,IAAI,CAACrC,YAAY,CAAC,CAAC;IACvB;EACJ;EACA4C,WAAWA,CAAA,EAAG;IACV,IAAI,CAACrG,OAAO,GAAG,IAAI;IACnB,IAAI,CAACM,OAAO,CAACQ,SAAS,CAACG,MAAM,CAAC,MAAM,CAAC;IACrC,IAAI,CAACX,OAAO,CAACQ,SAAS,CAACG,MAAM,CAAC,SAAS,CAAC;IACxC,IAAI,CAACX,OAAO,CAACQ,SAAS,CAACG,MAAM,CAAC,OAAO,CAAC;IACtC,IAAI,CAACX,OAAO,CAACQ,SAAS,CAACC,GAAG,CAAC,MAAM,CAAC;IAClC,IAAI,CAACyC,YAAY,CAAC,CAAC;IACnB,IAAI,CAACD,WAAW,CAAC,CAAC;EACtB;EACA+C,QAAQA,CAAA,EAAG;IACP,IAAIC,QAAQ,GAAG,IAAI;IACnB,IAAI,IAAI,CAAClG,UAAU,EAAE;MACjBkG,QAAQ,GAAG,IAAI,CAAClG,UAAU,CAAC,IAAI,CAAC0D,KAAK,CAAC;MACtC,IAAIwC,QAAQ,EAAE;QACV,IAAI,CAACzC,YAAY,CAAClD,YAAY,CAAC,cAAc,EAAE,MAAM,CAAC;QACtD,IAAI,CAACiF,WAAW,CAACU,QAAQ,CAAC;MAC9B,CAAC,MACI,IAAI,IAAI,CAACzC,YAAY,CAAC0C,YAAY,CAAC,cAAc,CAAC,EAAE;QACrD,IAAI,CAAC1C,YAAY,CAACsB,eAAe,CAAC,cAAc,CAAC;QACjD,IAAI,CAACiB,WAAW,CAAC,CAAC;MACtB;IACJ;IACA,OAAOE,QAAQ,EAAEhE,IAAI;EACzB;EACA0D,aAAaA,CAAC1D,IAAI,EAAE;IAChB,MAAMyD,MAAM,GAAG,IAAI,CAAC3G,OAAO,CAACoH,cAAc;IAC1C,QAAQlE,IAAI;MACR,KAAK,CAAC,CAAC;QAAwB,OAAO;UAAE2D,MAAM,EAAEF,MAAM,CAAC1H,yBAAyB;UAAEoI,UAAU,EAAEV,MAAM,CAACzH,6BAA6B;UAAEoI,UAAU,EAAEX,MAAM,CAACjH;QAA8B,CAAC;MACtL,KAAK,CAAC,CAAC;QAA2B,OAAO;UAAEmH,MAAM,EAAEF,MAAM,CAACxH,4BAA4B;UAAEkI,UAAU,EAAEV,MAAM,CAACvH,gCAAgC;UAAEkI,UAAU,EAAEX,MAAM,CAAChH;QAAiC,CAAC;MAClM;QAAS,OAAO;UAAEkH,MAAM,EAAEF,MAAM,CAACtH,0BAA0B;UAAEgI,UAAU,EAAEV,MAAM,CAACrH,8BAA8B;UAAEgI,UAAU,EAAEX,MAAM,CAAClH;QAA+B,CAAC;IACvK;EACJ;EACAiH,YAAYA,CAACxD,IAAI,EAAE;IACf,QAAQA,IAAI;MACR,KAAK,CAAC,CAAC;QAAwB,OAAO,MAAM;MAC5C,KAAK,CAAC,CAAC;QAA2B,OAAO,SAAS;MAClD;QAAS,OAAO,OAAO;IAC3B;EACJ;EACAkB,YAAYA,CAAA,EAAG;IACX,IAAI,CAAC,IAAI,CAACrE,mBAAmB,IAAI,CAAC,IAAI,CAACY,OAAO,EAAE;MAC5C;IACJ;IACA,IAAI4G,GAAG;IACP,MAAMC,MAAM,GAAGA,CAAA,KAAMD,GAAG,CAACpF,KAAK,CAACgE,KAAK,GAAGrI,GAAG,CAAC2J,aAAa,CAAC,IAAI,CAACxG,OAAO,CAAC,GAAG,IAAI;IAC7E,IAAI,CAAClB,mBAAmB,CAAC2H,eAAe,CAAC;MACrCC,SAAS,EAAEA,CAAA,KAAM,IAAI,CAAC1G,OAAO;MAC7B2G,eAAe,EAAE,CAAC,CAAC;MACnBC,MAAM,EAAG/H,SAAS,IAAK;QACnB,IAAI,CAAC,IAAI,CAACa,OAAO,EAAE;UACf,OAAO,IAAI;QACf;QACA4G,GAAG,GAAGzJ,GAAG,CAACoD,MAAM,CAACpB,SAAS,EAAEjB,CAAC,CAAC,4BAA4B,CAAC,CAAC;QAC5D2I,MAAM,CAAC,CAAC;QACR,MAAMM,aAAa,GAAG;UAClBC,MAAM,EAAE,IAAI;UACZC,SAAS,EAAE;QACf,CAAC;QACD,MAAMC,WAAW,GAAI,IAAI,CAACtH,OAAO,CAACuH,aAAa,GACzClK,mBAAmB,CAAC,IAAI,CAAC2C,OAAO,CAACoG,OAAO,EAAEe,aAAa,CAAC,GACxD7J,UAAU,CAAC,IAAI,CAAC0C,OAAO,CAACoG,OAAO,EAAEe,aAAa,CAAE;QACtDG,WAAW,CAACxG,SAAS,CAACC,GAAG,CAAC,IAAI,CAACgF,YAAY,CAAC,IAAI,CAAC/F,OAAO,CAACuC,IAAI,CAAC,CAAC;QAC/D,MAAMyD,MAAM,GAAG,IAAI,CAACC,aAAa,CAAC,IAAI,CAACjG,OAAO,CAACuC,IAAI,CAAC;QACpD+E,WAAW,CAAC9F,KAAK,CAACgG,eAAe,GAAGxB,MAAM,CAACU,UAAU,IAAI,EAAE;QAC3DY,WAAW,CAAC9F,KAAK,CAACiG,KAAK,GAAGzB,MAAM,CAACW,UAAU,IAAI,EAAE;QACjDW,WAAW,CAAC9F,KAAK,CAAC0E,MAAM,GAAGF,MAAM,CAACE,MAAM,GAAG,aAAaF,MAAM,CAACE,MAAM,EAAE,GAAG,EAAE;QAC5E/I,GAAG,CAACoD,MAAM,CAACqG,GAAG,EAAEU,WAAW,CAAC;QAC5B,OAAO,IAAI;MACf,CAAC;MACDI,MAAM,EAAEA,CAAA,KAAM;QACV,IAAI,CAACpI,KAAK,GAAG,QAAQ;MACzB,CAAC;MACDuH,MAAM,EAAEA;IACZ,CAAC,CAAC;IACF;IACA,IAAIc,SAAS;IACb,IAAI,IAAI,CAAC3H,OAAO,CAACuC,IAAI,KAAK,CAAC,CAAC,yBAAyB;MACjDoF,SAAS,GAAG1J,GAAG,CAAC2J,QAAQ,CAAC,mBAAmB,EAAE,YAAY,EAAE,IAAI,CAAC5H,OAAO,CAACoG,OAAO,CAAC;IACrF,CAAC,MACI,IAAI,IAAI,CAACpG,OAAO,CAACuC,IAAI,KAAK,CAAC,CAAC,2BAA2B;MACxDoF,SAAS,GAAG1J,GAAG,CAAC2J,QAAQ,CAAC,qBAAqB,EAAE,cAAc,EAAE,IAAI,CAAC5H,OAAO,CAACoG,OAAO,CAAC;IACzF,CAAC,MACI;MACDuB,SAAS,GAAG1J,GAAG,CAAC2J,QAAQ,CAAC,kBAAkB,EAAE,WAAW,EAAE,IAAI,CAAC5H,OAAO,CAACoG,OAAO,CAAC;IACnF;IACA5I,IAAI,CAACqK,KAAK,CAACF,SAAS,CAAC;IACrB,IAAI,CAACrI,KAAK,GAAG,MAAM;EACvB;EACAkE,YAAYA,CAAA,EAAG;IACX,IAAI,CAAC,IAAI,CAACpE,mBAAmB,EAAE;MAC3B;IACJ;IACA,IAAI,IAAI,CAACE,KAAK,KAAK,MAAM,EAAE;MACvB,IAAI,CAACF,mBAAmB,CAAC0I,eAAe,CAAC,CAAC;IAC9C;IACA,IAAI,CAACxI,KAAK,GAAG,MAAM;EACvB;EACAsD,aAAaA,CAAA,EAAG;IACZ,IAAI,CAAClD,YAAY,CAACqI,IAAI,CAAC,IAAI,CAAChE,KAAK,CAAC;IAClC,IAAI,CAACuC,QAAQ,CAAC,CAAC;IACf,IAAI,CAACrD,YAAY,CAAC,CAAC;IACnB,IAAI,CAACtC,KAAK,CAACG,SAAS,CAACkH,MAAM,CAAC,OAAO,EAAE,CAAC,IAAI,CAACjE,KAAK,CAAC;IACjD,IAAI,IAAI,CAACzE,KAAK,KAAK,MAAM,IAAI,IAAI,CAACF,mBAAmB,EAAE;MACnD,IAAI,CAACA,mBAAmB,CAACyH,MAAM,CAAC,CAAC;IACrC;EACJ;EACA5D,YAAYA,CAAA,EAAG;IACX,IAAI,CAAC,IAAI,CAAC9B,MAAM,EAAE;MACd;IACJ;IACA,MAAM4C,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,MAAMkE,YAAY,GAAGlE,KAAK,CAACmE,UAAU,CAACnE,KAAK,CAACc,MAAM,GAAG,CAAC,CAAC;IACvD,MAAMsD,MAAM,GAAGF,YAAY,KAAK,EAAE,GAAG,GAAG,GAAG,EAAE;IAC7C,MAAMG,iBAAiB,GAAG,CAACrE,KAAK,GAAGoE,MAAM,EACpCE,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,CAAC;IAC7B,IAAID,iBAAiB,EAAE;MACnB,IAAI,CAACjH,MAAM,CAACmH,WAAW,GAAGvE,KAAK,GAAGoE,MAAM;IAC5C,CAAC,MACI;MACD,IAAI,CAAChH,MAAM,CAACC,SAAS,GAAG,QAAQ;IACpC;IACA,IAAI,CAACyF,MAAM,CAAC,CAAC;EACjB;EACAtD,WAAWA,CAAA,EAAG;IACV,MAAMyC,MAAM,GAAG,IAAI,CAAC3G,OAAO,CAACoH,cAAc;IAC1C,MAAMC,UAAU,GAAGV,MAAM,CAAC5H,eAAe,IAAI,EAAE;IAC/C,MAAMuI,UAAU,GAAGX,MAAM,CAAC3H,eAAe,IAAI,EAAE;IAC/C,MAAM6H,MAAM,GAAGF,MAAM,CAACpH,WAAW,IAAI,EAAE;IACvC,IAAI,CAAC0B,OAAO,CAACkB,KAAK,CAACgG,eAAe,GAAGd,UAAU;IAC/C,IAAI,CAACpG,OAAO,CAACkB,KAAK,CAACiG,KAAK,GAAGd,UAAU;IACrC,IAAI,CAAChG,KAAK,CAACa,KAAK,CAACgG,eAAe,GAAG,SAAS;IAC5C,IAAI,CAAC7G,KAAK,CAACa,KAAK,CAACiG,KAAK,GAAGd,UAAU;IACnC;IACA,IAAI,CAACrG,OAAO,CAACkB,KAAK,CAAC0E,MAAM,GAAG,aAAa/I,GAAG,CAACgJ,qBAAqB,CAACD,MAAM,EAAE,aAAa,CAAC,EAAE;EAC/F;EACAW,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC,IAAI,CAAC1F,MAAM,EAAE;MACd;IACJ;IACA,MAAMoH,cAAc,GAAG,IAAI,CAAC9C,mBAAmB;IAC/C,IAAI,CAACA,mBAAmB,GAAGtI,GAAG,CAACgH,cAAc,CAAC,IAAI,CAAChD,MAAM,CAAC;IAC1D,IAAIoH,cAAc,KAAK,IAAI,CAAC9C,mBAAmB,EAAE;MAC7C,IAAI,CAACvB,YAAY,GAAGsE,IAAI,CAACC,GAAG,CAAC,IAAI,CAAChD,mBAAmB,EAAE,IAAI,CAAClG,SAAS,CAAC;MACtE,IAAI,CAACoB,KAAK,CAACa,KAAK,CAACyC,MAAM,GAAG,IAAI,CAACC,YAAY,GAAG,IAAI;MAClD,IAAI,CAACpE,kBAAkB,CAACiI,IAAI,CAAC,IAAI,CAACtC,mBAAmB,CAAC;IAC1D;EACJ;EACAiD,cAAcA,CAACC,IAAI,EAAE;IACjB,MAAM7E,YAAY,GAAG,IAAI,CAACA,YAAY;IACtC,MAAMa,KAAK,GAAGb,YAAY,CAACoB,cAAc;IACzC,MAAMN,GAAG,GAAGd,YAAY,CAACmB,YAAY;IACrC,MAAMmB,OAAO,GAAGtC,YAAY,CAACC,KAAK;IAClC,IAAIY,KAAK,KAAK,IAAI,IAAIC,GAAG,KAAK,IAAI,EAAE;MAChC,IAAI,CAACb,KAAK,GAAGqC,OAAO,CAACwC,MAAM,CAAC,CAAC,EAAEjE,KAAK,CAAC,GAAGgE,IAAI,GAAGvC,OAAO,CAACwC,MAAM,CAAChE,GAAG,CAAC;MAClEd,YAAY,CAACY,iBAAiB,CAACC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG,CAAC,CAAC;MACpD,IAAI,CAACkC,MAAM,CAAC,CAAC;IACjB;EACJ;EACAgC,OAAOA,CAAA,EAAG;IACN,IAAI,CAACrF,YAAY,CAAC,CAAC;IACnB,IAAI,CAACxD,OAAO,GAAG,IAAI;IACnB,IAAI,CAACmD,SAAS,EAAE0F,OAAO,CAAC,CAAC;IACzB,KAAK,CAACA,OAAO,CAAC,CAAC;EACnB;AACJ;AACA,OAAO,MAAMC,eAAe,SAAS7J,QAAQ,CAAC;EAC1CC,WAAWA,CAACC,SAAS,EAAEC,mBAAmB,EAAEC,OAAO,EAAE;IACjD,MAAM0J,6CAA6C,GAAG9K,GAAG,CAAC2J,QAAQ,CAAC;MAC/DoB,GAAG,EAAE,uCAAuC;MAC5CC,OAAO,EAAE,CAAC,0TAA0T;IACxU,CAAC,EAAE,qBAAqB,EAAE,QAAQ,CAAC;IACnC,MAAMC,6CAA6C,GAAGjL,GAAG,CAAC2J,QAAQ,CAAC;MAC/DoB,GAAG,EAAE,uCAAuC;MAC5CC,OAAO,EAAE,CAAC,+RAA+R;IAC7S,CAAC,EAAE,oBAAoB,EAAE,QAAQ,CAAC;IAClC,KAAK,CAAC9J,SAAS,EAAEC,mBAAmB,EAAEC,OAAO,CAAC;IAC9C,IAAI,CAAC8J,WAAW,GAAG,IAAI,CAACxJ,SAAS,CAAC,IAAI9B,OAAO,CAAC,CAAC,CAAC;IAChD,IAAI,CAACuL,UAAU,GAAG,IAAI,CAACD,WAAW,CAACtJ,KAAK;IACxC,IAAI,CAACwJ,UAAU,GAAG,IAAI,CAAC1J,SAAS,CAAC,IAAI9B,OAAO,CAAC,CAAC,CAAC;IAC/C,IAAI,CAACyL,SAAS,GAAG,IAAI,CAACD,UAAU,CAACxJ,KAAK;IACtC,IAAI,CAAC0J,OAAO,GAAG,IAAIxL,gBAAgB,CAACsB,OAAO,CAACkK,OAAO,EAAE,GAAG,CAAC;IACzD;IACA,MAAMC,SAAS,GAAGA,CAAA,KAAM;MACpB,IAAInK,OAAO,CAACoK,eAAe,IAAIpK,OAAO,CAACoK,eAAe,CAAC,CAAC,IAAI,CAAC,IAAI,CAACxJ,WAAW,CAACyJ,QAAQ,CAACX,6CAA6C,CAAC,IAAI,CAAC,IAAI,CAAC9I,WAAW,CAACyJ,QAAQ,CAACR,6CAA6C,CAAC,IAAI,IAAI,CAACK,OAAO,CAACI,UAAU,CAAC,CAAC,CAAC9E,MAAM,EAAE;QACpP,MAAMsD,MAAM,GAAG,IAAI,CAAClI,WAAW,CAACyJ,QAAQ,CAAC,GAAG,CAAC,GAAGX,6CAA6C,GAAGG,6CAA6C;QAC7I,MAAMU,mBAAmB,GAAG,IAAI,CAAC3J,WAAW,GAAGkI,MAAM;QACrD,IAAI9I,OAAO,CAACmD,sBAAsB,IAAI,CAACrF,GAAG,CAACoH,eAAe,CAAC,IAAI,CAAC5D,KAAK,CAAC,EAAE;UACpE,IAAI,CAACV,WAAW,GAAG2J,mBAAmB;QAC1C,CAAC,MACI;UACD,IAAI,CAACnH,cAAc,CAACmH,mBAAmB,CAAC;QAC5C;MACJ;IACJ,CAAC;IACD;IACA;IACA,IAAI,CAACC,QAAQ,GAAG,IAAIC,gBAAgB,CAAC,CAACC,YAAY,EAAEF,QAAQ,KAAK;MAC7DE,YAAY,CAACC,OAAO,CAAEC,QAAQ,IAAK;QAC/B,IAAI,CAACA,QAAQ,CAACC,MAAM,CAAC5B,WAAW,EAAE;UAC9BkB,SAAS,CAAC,CAAC;QACf;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;IACF,IAAI,CAACK,QAAQ,CAACM,OAAO,CAAC,IAAI,CAACxJ,KAAK,EAAE;MAAEyJ,eAAe,EAAE,CAAC,OAAO;IAAE,CAAC,CAAC;IACjE,IAAI,CAACvJ,OAAO,CAAC,IAAI,CAACF,KAAK,EAAE,MAAM6I,SAAS,CAAC,CAAC,CAAC;IAC3C,IAAI,CAACxI,MAAM,CAAC,IAAI,CAACL,KAAK,EAAE,MAAM;MAC1B,MAAM0J,gBAAgB,GAAIC,WAAW,IAAK;QACtC,IAAI,CAAC,IAAI,CAACrK,WAAW,CAACyJ,QAAQ,CAACY,WAAW,CAAC,EAAE;UACzC,OAAO,KAAK;QAChB,CAAC,MACI;UACD,MAAMC,mBAAmB,GAAG,IAAI,CAACtK,WAAW,CAACuK,KAAK,CAAC,CAAC,EAAE,IAAI,CAACvK,WAAW,CAAC4E,MAAM,GAAGyF,WAAW,CAACzF,MAAM,CAAC;UACnG,IAAIxF,OAAO,CAACmD,sBAAsB,EAAE;YAChC,IAAI,CAACvC,WAAW,GAAGsK,mBAAmB;UAC1C,CAAC,MACI;YACD,IAAI,CAAC9H,cAAc,CAAC8H,mBAAmB,CAAC;UAC5C;UACA,OAAO,IAAI;QACf;MACJ,CAAC;MACD,IAAI,CAACF,gBAAgB,CAACnB,6CAA6C,CAAC,EAAE;QAClEmB,gBAAgB,CAACtB,6CAA6C,CAAC;MACnE;IACJ,CAAC,CAAC;EACN;EACAF,OAAOA,CAAA,EAAG;IACN,KAAK,CAACA,OAAO,CAAC,CAAC;IACf,IAAI,IAAI,CAACgB,QAAQ,EAAE;MACf,IAAI,CAACA,QAAQ,CAACY,UAAU,CAAC,CAAC;MAC1B,IAAI,CAACZ,QAAQ,GAAGhL,SAAS;IAC7B;EACJ;EACA6L,YAAYA,CAACC,MAAM,EAAE;IACjB,IAAI,IAAI,CAAC5G,KAAK,KAAK4G,MAAM,IAAI,IAAI,CAAC5G,KAAK,KAAK,IAAI,CAAC6G,eAAe,CAAC,CAAC,CAAC,EAAE;MACjE,IAAI,CAACrB,OAAO,CAACxI,GAAG,CAAC,IAAI,CAACgD,KAAK,CAAC;IAChC;EACJ;EACA8G,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACtB,OAAO,CAACuB,MAAM,CAAC,CAAC;EAChC;EACAC,kBAAkBA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACxB,OAAO,CAACyB,SAAS,CAAC,CAAC;EACnC;EACAC,aAAaA,CAAA,EAAG;IACZ,IAAI,CAAC,IAAI,CAAC1B,OAAO,CAAC2B,GAAG,CAAC,IAAI,CAACnH,KAAK,CAAC,EAAE;MAC/B,IAAI,CAAC2G,YAAY,CAAC,CAAC;IACvB;IACA,IAAIS,IAAI,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IAC9B,IAAID,IAAI,EAAE;MACNA,IAAI,GAAGA,IAAI,KAAK,IAAI,CAACpH,KAAK,GAAG,IAAI,CAACqH,YAAY,CAAC,CAAC,GAAGD,IAAI;IAC3D;IACA,IAAI,CAACpH,KAAK,GAAGoH,IAAI,IAAI,EAAE;IACvB3N,IAAI,CAAC6N,MAAM,CAAC,IAAI,CAACtH,KAAK,GAAG,IAAI,CAACA,KAAK,GAAG9F,GAAG,CAAC2J,QAAQ,CAAC,cAAc,EAAE,eAAe,CAAC,CAAC;EACxF;EACA0D,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC,IAAI,CAAC/B,OAAO,CAAC2B,GAAG,CAAC,IAAI,CAACnH,KAAK,CAAC,EAAE;MAC/B,IAAI,CAAC2G,YAAY,CAAC,CAAC;IACvB;IACA,IAAIa,QAAQ,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;IACtC,IAAID,QAAQ,EAAE;MACVA,QAAQ,GAAGA,QAAQ,KAAK,IAAI,CAACxH,KAAK,GAAG,IAAI,CAACyH,gBAAgB,CAAC,CAAC,GAAGD,QAAQ;IAC3E;IACA,IAAIA,QAAQ,EAAE;MACV,IAAI,CAACxH,KAAK,GAAGwH,QAAQ;MACrB/N,IAAI,CAAC6N,MAAM,CAAC,IAAI,CAACtH,KAAK,CAAC;IAC3B;EACJ;EACAtB,cAAcA,CAACiB,WAAW,EAAE;IACxB,KAAK,CAACjB,cAAc,CAACiB,WAAW,CAAC;IACjC,IAAI,CAAChB,UAAU,CAACgB,WAAW,CAAC;EAChC;EACAb,MAAMA,CAAA,EAAG;IACL,KAAK,CAACA,MAAM,CAAC,CAAC;IACd,IAAI,CAACwG,UAAU,CAACtB,IAAI,CAAC,CAAC;EAC1B;EACAjF,OAAOA,CAAA,EAAG;IACN,KAAK,CAACA,OAAO,CAAC,CAAC;IACf,IAAI,CAACqG,WAAW,CAACpB,IAAI,CAAC,CAAC;EAC3B;EACA6C,eAAeA,CAAA,EAAG;IACd,IAAIa,YAAY,GAAG,IAAI,CAAClC,OAAO,CAACmC,OAAO,CAAC,CAAC;IACzC,IAAI,CAACD,YAAY,EAAE;MACfA,YAAY,GAAG,IAAI,CAAClC,OAAO,CAACoC,IAAI,CAAC,CAAC;MAClC,IAAI,CAACpC,OAAO,CAAC4B,IAAI,CAAC,CAAC;IACvB;IACA,OAAOM,YAAY;EACvB;EACAD,gBAAgBA,CAAA,EAAG;IACf,OAAO,IAAI,CAACjC,OAAO,CAACgC,QAAQ,CAAC,CAAC,IAAI,IAAI,CAAChC,OAAO,CAACqC,KAAK,CAAC,CAAC;EAC1D;EACAR,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC7B,OAAO,CAAC4B,IAAI,CAAC,CAAC;EAC9B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}