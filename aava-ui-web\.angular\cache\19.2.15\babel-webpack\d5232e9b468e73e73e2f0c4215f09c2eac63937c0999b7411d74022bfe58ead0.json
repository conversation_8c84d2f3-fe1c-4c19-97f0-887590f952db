{"ast": null, "code": "import _asyncToGenerator from \"C:/console/aava-ui-web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = this && this.__param || function (paramIndex, decorator) {\n  return function (target, key) {\n    decorator(target, key, paramIndex);\n  };\n};\nimport { compareBy, Permutation } from '../../../../../base/common/arrays.js';\nimport { mapFindFirst } from '../../../../../base/common/arraysFind.js';\nimport { itemsEquals } from '../../../../../base/common/equals.js';\nimport { BugIndicatingError, onUnexpectedError, onUnexpectedExternalError } from '../../../../../base/common/errors.js';\nimport { Disposable } from '../../../../../base/common/lifecycle.js';\nimport { autorun, derived, derivedHandleChanges, derivedOpts, observableSignal, observableValue, recomputeInitiallyAndOnChange, subtransaction, transaction } from '../../../../../base/common/observable.js';\nimport { commonPrefixLength, splitLinesIncludeSeparators } from '../../../../../base/common/strings.js';\nimport { isDefined } from '../../../../../base/common/types.js';\nimport { EditOperation } from '../../../../common/core/editOperation.js';\nimport { Position } from '../../../../common/core/position.js';\nimport { Range } from '../../../../common/core/range.js';\nimport { Selection } from '../../../../common/core/selection.js';\nimport { SingleTextEdit, TextEdit } from '../../../../common/core/textEdit.js';\nimport { TextLength } from '../../../../common/core/textLength.js';\nimport { InlineCompletionTriggerKind } from '../../../../common/languages.js';\nimport { ILanguageConfigurationService } from '../../../../common/languages/languageConfigurationRegistry.js';\nimport { GhostText, ghostTextOrReplacementEquals, ghostTextsOrReplacementsEqual } from './ghostText.js';\nimport { InlineCompletionsSource } from './inlineCompletionsSource.js';\nimport { computeGhostText, singleTextEditAugments, singleTextRemoveCommonPrefix } from './singleTextEdit.js';\nimport { addPositions, subtractPositions } from '../utils.js';\nimport { SnippetController2 } from '../../../snippet/browser/snippetController2.js';\nimport { ICommandService } from '../../../../../platform/commands/common/commands.js';\nimport { IInstantiationService } from '../../../../../platform/instantiation/common/instantiation.js';\nlet InlineCompletionsModel = class InlineCompletionsModel extends Disposable {\n  get isAcceptingPartially() {\n    return this._isAcceptingPartially;\n  }\n  constructor(textModel, selectedSuggestItem, _textModelVersionId, _positions, _debounceValue, _suggestPreviewEnabled, _suggestPreviewMode, _inlineSuggestMode, _enabled, _instantiationService, _commandService, _languageConfigurationService) {\n    super();\n    this.textModel = textModel;\n    this.selectedSuggestItem = selectedSuggestItem;\n    this._textModelVersionId = _textModelVersionId;\n    this._positions = _positions;\n    this._debounceValue = _debounceValue;\n    this._suggestPreviewEnabled = _suggestPreviewEnabled;\n    this._suggestPreviewMode = _suggestPreviewMode;\n    this._inlineSuggestMode = _inlineSuggestMode;\n    this._enabled = _enabled;\n    this._instantiationService = _instantiationService;\n    this._commandService = _commandService;\n    this._languageConfigurationService = _languageConfigurationService;\n    this._source = this._register(this._instantiationService.createInstance(InlineCompletionsSource, this.textModel, this._textModelVersionId, this._debounceValue));\n    this._isActive = observableValue(this, false);\n    this._forceUpdateExplicitlySignal = observableSignal(this);\n    // We use a semantic id to keep the same inline completion selected even if the provider reorders the completions.\n    this._selectedInlineCompletionId = observableValue(this, undefined);\n    this._primaryPosition = derived(this, reader => this._positions.read(reader)[0] ?? new Position(1, 1));\n    this._isAcceptingPartially = false;\n    this._preserveCurrentCompletionReasons = new Set([VersionIdChangeReason.Redo, VersionIdChangeReason.Undo, VersionIdChangeReason.AcceptWord]);\n    this._fetchInlineCompletionsPromise = derivedHandleChanges({\n      owner: this,\n      createEmptyChangeSummary: () => ({\n        preserveCurrentCompletion: false,\n        inlineCompletionTriggerKind: InlineCompletionTriggerKind.Automatic\n      }),\n      handleChange: (ctx, changeSummary) => {\n        /** @description fetch inline completions */\n        if (ctx.didChange(this._textModelVersionId) && this._preserveCurrentCompletionReasons.has(this._getReason(ctx.change))) {\n          changeSummary.preserveCurrentCompletion = true;\n        } else if (ctx.didChange(this._forceUpdateExplicitlySignal)) {\n          changeSummary.inlineCompletionTriggerKind = InlineCompletionTriggerKind.Explicit;\n        }\n        return true;\n      }\n    }, (reader, changeSummary) => {\n      this._forceUpdateExplicitlySignal.read(reader);\n      const shouldUpdate = this._enabled.read(reader) && this.selectedSuggestItem.read(reader) || this._isActive.read(reader);\n      if (!shouldUpdate) {\n        this._source.cancelUpdate();\n        return undefined;\n      }\n      this._textModelVersionId.read(reader); // Refetch on text change\n      const suggestWidgetInlineCompletions = this._source.suggestWidgetInlineCompletions.get();\n      const suggestItem = this.selectedSuggestItem.read(reader);\n      if (suggestWidgetInlineCompletions && !suggestItem) {\n        const inlineCompletions = this._source.inlineCompletions.get();\n        transaction(tx => {\n          /** @description Seed inline completions with (newer) suggest widget inline completions */\n          if (!inlineCompletions || suggestWidgetInlineCompletions.request.versionId > inlineCompletions.request.versionId) {\n            this._source.inlineCompletions.set(suggestWidgetInlineCompletions.clone(), tx);\n          }\n          this._source.clearSuggestWidgetInlineCompletions(tx);\n        });\n      }\n      const cursorPosition = this._primaryPosition.read(reader);\n      const context = {\n        triggerKind: changeSummary.inlineCompletionTriggerKind,\n        selectedSuggestionInfo: suggestItem?.toSelectedSuggestionInfo()\n      };\n      const itemToPreserveCandidate = this.selectedInlineCompletion.get();\n      const itemToPreserve = changeSummary.preserveCurrentCompletion || itemToPreserveCandidate?.forwardStable ? itemToPreserveCandidate : undefined;\n      return this._source.fetch(cursorPosition, context, itemToPreserve);\n    });\n    this._filteredInlineCompletionItems = derivedOpts({\n      owner: this,\n      equalsFn: itemsEquals()\n    }, reader => {\n      const c = this._source.inlineCompletions.read(reader);\n      if (!c) {\n        return [];\n      }\n      const cursorPosition = this._primaryPosition.read(reader);\n      const filteredCompletions = c.inlineCompletions.filter(c => c.isVisible(this.textModel, cursorPosition, reader));\n      return filteredCompletions;\n    });\n    this.selectedInlineCompletionIndex = derived(this, reader => {\n      const selectedInlineCompletionId = this._selectedInlineCompletionId.read(reader);\n      const filteredCompletions = this._filteredInlineCompletionItems.read(reader);\n      const idx = this._selectedInlineCompletionId === undefined ? -1 : filteredCompletions.findIndex(v => v.semanticId === selectedInlineCompletionId);\n      if (idx === -1) {\n        // Reset the selection so that the selection does not jump back when it appears again\n        this._selectedInlineCompletionId.set(undefined, undefined);\n        return 0;\n      }\n      return idx;\n    });\n    this.selectedInlineCompletion = derived(this, reader => {\n      const filteredCompletions = this._filteredInlineCompletionItems.read(reader);\n      const idx = this.selectedInlineCompletionIndex.read(reader);\n      return filteredCompletions[idx];\n    });\n    this.activeCommands = derivedOpts({\n      owner: this,\n      equalsFn: itemsEquals()\n    }, r => this.selectedInlineCompletion.read(r)?.inlineCompletion.source.inlineCompletions.commands ?? []);\n    this.lastTriggerKind = this._source.inlineCompletions.map(this, v => v?.request.context.triggerKind);\n    this.inlineCompletionsCount = derived(this, reader => {\n      if (this.lastTriggerKind.read(reader) === InlineCompletionTriggerKind.Explicit) {\n        return this._filteredInlineCompletionItems.read(reader).length;\n      } else {\n        return undefined;\n      }\n    });\n    this.state = derivedOpts({\n      owner: this,\n      equalsFn: (a, b) => {\n        if (!a || !b) {\n          return a === b;\n        }\n        return ghostTextsOrReplacementsEqual(a.ghostTexts, b.ghostTexts) && a.inlineCompletion === b.inlineCompletion && a.suggestItem === b.suggestItem;\n      }\n    }, reader => {\n      const model = this.textModel;\n      const suggestItem = this.selectedSuggestItem.read(reader);\n      if (suggestItem) {\n        const suggestCompletionEdit = singleTextRemoveCommonPrefix(suggestItem.toSingleTextEdit(), model);\n        const augmentation = this._computeAugmentation(suggestCompletionEdit, reader);\n        const isSuggestionPreviewEnabled = this._suggestPreviewEnabled.read(reader);\n        if (!isSuggestionPreviewEnabled && !augmentation) {\n          return undefined;\n        }\n        const fullEdit = augmentation?.edit ?? suggestCompletionEdit;\n        const fullEditPreviewLength = augmentation ? augmentation.edit.text.length - suggestCompletionEdit.text.length : 0;\n        const mode = this._suggestPreviewMode.read(reader);\n        const positions = this._positions.read(reader);\n        const edits = [fullEdit, ...getSecondaryEdits(this.textModel, positions, fullEdit)];\n        const ghostTexts = edits.map((edit, idx) => computeGhostText(edit, model, mode, positions[idx], fullEditPreviewLength)).filter(isDefined);\n        const primaryGhostText = ghostTexts[0] ?? new GhostText(fullEdit.range.endLineNumber, []);\n        return {\n          edits,\n          primaryGhostText,\n          ghostTexts,\n          inlineCompletion: augmentation?.completion,\n          suggestItem\n        };\n      } else {\n        if (!this._isActive.read(reader)) {\n          return undefined;\n        }\n        const inlineCompletion = this.selectedInlineCompletion.read(reader);\n        if (!inlineCompletion) {\n          return undefined;\n        }\n        const replacement = inlineCompletion.toSingleTextEdit(reader);\n        const mode = this._inlineSuggestMode.read(reader);\n        const positions = this._positions.read(reader);\n        const edits = [replacement, ...getSecondaryEdits(this.textModel, positions, replacement)];\n        const ghostTexts = edits.map((edit, idx) => computeGhostText(edit, model, mode, positions[idx], 0)).filter(isDefined);\n        if (!ghostTexts[0]) {\n          return undefined;\n        }\n        return {\n          edits,\n          primaryGhostText: ghostTexts[0],\n          ghostTexts,\n          inlineCompletion,\n          suggestItem: undefined\n        };\n      }\n    });\n    this.ghostTexts = derivedOpts({\n      owner: this,\n      equalsFn: ghostTextsOrReplacementsEqual\n    }, reader => {\n      const v = this.state.read(reader);\n      if (!v) {\n        return undefined;\n      }\n      return v.ghostTexts;\n    });\n    this.primaryGhostText = derivedOpts({\n      owner: this,\n      equalsFn: ghostTextOrReplacementEquals\n    }, reader => {\n      const v = this.state.read(reader);\n      if (!v) {\n        return undefined;\n      }\n      return v?.primaryGhostText;\n    });\n    this._register(recomputeInitiallyAndOnChange(this._fetchInlineCompletionsPromise));\n    let lastItem = undefined;\n    this._register(autorun(reader => {\n      /** @description call handleItemDidShow */\n      const item = this.state.read(reader);\n      const completion = item?.inlineCompletion;\n      if (completion?.semanticId !== lastItem?.semanticId) {\n        lastItem = completion;\n        if (completion) {\n          const i = completion.inlineCompletion;\n          const src = i.source;\n          src.provider.handleItemDidShow?.(src.inlineCompletions, i.sourceInlineCompletion, i.insertText);\n        }\n      }\n    }));\n  }\n  _getReason(e) {\n    if (e?.isUndoing) {\n      return VersionIdChangeReason.Undo;\n    }\n    if (e?.isRedoing) {\n      return VersionIdChangeReason.Redo;\n    }\n    if (this.isAcceptingPartially) {\n      return VersionIdChangeReason.AcceptWord;\n    }\n    return VersionIdChangeReason.Other;\n  }\n  trigger(tx) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this._isActive.set(true, tx);\n      yield _this._fetchInlineCompletionsPromise.get();\n    })();\n  }\n  triggerExplicitly(tx) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      subtransaction(tx, tx => {\n        _this2._isActive.set(true, tx);\n        _this2._forceUpdateExplicitlySignal.trigger(tx);\n      });\n      yield _this2._fetchInlineCompletionsPromise.get();\n    })();\n  }\n  stop(tx) {\n    subtransaction(tx, tx => {\n      this._isActive.set(false, tx);\n      this._source.clear(tx);\n    });\n  }\n  _computeAugmentation(suggestCompletion, reader) {\n    const model = this.textModel;\n    const suggestWidgetInlineCompletions = this._source.suggestWidgetInlineCompletions.read(reader);\n    const candidateInlineCompletions = suggestWidgetInlineCompletions ? suggestWidgetInlineCompletions.inlineCompletions : [this.selectedInlineCompletion.read(reader)].filter(isDefined);\n    const augmentedCompletion = mapFindFirst(candidateInlineCompletions, completion => {\n      let r = completion.toSingleTextEdit(reader);\n      r = singleTextRemoveCommonPrefix(r, model, Range.fromPositions(r.range.getStartPosition(), suggestCompletion.range.getEndPosition()));\n      return singleTextEditAugments(r, suggestCompletion) ? {\n        completion,\n        edit: r\n      } : undefined;\n    });\n    return augmentedCompletion;\n  }\n  _deltaSelectedInlineCompletionIndex(delta) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      yield _this3.triggerExplicitly();\n      const completions = _this3._filteredInlineCompletionItems.get() || [];\n      if (completions.length > 0) {\n        const newIdx = (_this3.selectedInlineCompletionIndex.get() + delta + completions.length) % completions.length;\n        _this3._selectedInlineCompletionId.set(completions[newIdx].semanticId, undefined);\n      } else {\n        _this3._selectedInlineCompletionId.set(undefined, undefined);\n      }\n    })();\n  }\n  next() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      yield _this4._deltaSelectedInlineCompletionIndex(1);\n    })();\n  }\n  previous() {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      yield _this5._deltaSelectedInlineCompletionIndex(-1);\n    })();\n  }\n  accept(editor) {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      if (editor.getModel() !== _this6.textModel) {\n        throw new BugIndicatingError();\n      }\n      const state = _this6.state.get();\n      if (!state || state.primaryGhostText.isEmpty() || !state.inlineCompletion) {\n        return;\n      }\n      const completion = state.inlineCompletion.toInlineCompletion(undefined);\n      if (completion.command) {\n        // Make sure the completion list will not be disposed.\n        completion.source.addRef();\n      }\n      editor.pushUndoStop();\n      if (completion.snippetInfo) {\n        editor.executeEdits('inlineSuggestion.accept', [EditOperation.replace(completion.range, ''), ...completion.additionalTextEdits]);\n        editor.setPosition(completion.snippetInfo.range.getStartPosition(), 'inlineCompletionAccept');\n        SnippetController2.get(editor)?.insert(completion.snippetInfo.snippet, {\n          undoStopBefore: false\n        });\n      } else {\n        const edits = state.edits;\n        const selections = getEndPositionsAfterApplying(edits).map(p => Selection.fromPositions(p));\n        editor.executeEdits('inlineSuggestion.accept', [...edits.map(edit => EditOperation.replace(edit.range, edit.text)), ...completion.additionalTextEdits]);\n        editor.setSelections(selections, 'inlineCompletionAccept');\n      }\n      // Reset before invoking the command, as the command might cause a follow up trigger (which we don't want to reset).\n      _this6.stop();\n      if (completion.command) {\n        yield _this6._commandService.executeCommand(completion.command.id, ...(completion.command.arguments || [])).then(undefined, onUnexpectedExternalError);\n        completion.source.removeRef();\n      }\n    })();\n  }\n  acceptNextWord(editor) {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      yield _this7._acceptNext(editor, (pos, text) => {\n        const langId = _this7.textModel.getLanguageIdAtPosition(pos.lineNumber, pos.column);\n        const config = _this7._languageConfigurationService.getLanguageConfiguration(langId);\n        const wordRegExp = new RegExp(config.wordDefinition.source, config.wordDefinition.flags.replace('g', ''));\n        const m1 = text.match(wordRegExp);\n        let acceptUntilIndexExclusive = 0;\n        if (m1 && m1.index !== undefined) {\n          if (m1.index === 0) {\n            acceptUntilIndexExclusive = m1[0].length;\n          } else {\n            acceptUntilIndexExclusive = m1.index;\n          }\n        } else {\n          acceptUntilIndexExclusive = text.length;\n        }\n        const wsRegExp = /\\s+/g;\n        const m2 = wsRegExp.exec(text);\n        if (m2 && m2.index !== undefined) {\n          if (m2.index + m2[0].length < acceptUntilIndexExclusive) {\n            acceptUntilIndexExclusive = m2.index + m2[0].length;\n          }\n        }\n        return acceptUntilIndexExclusive;\n      }, 0 /* PartialAcceptTriggerKind.Word */);\n    })();\n  }\n  acceptNextLine(editor) {\n    var _this8 = this;\n    return _asyncToGenerator(function* () {\n      yield _this8._acceptNext(editor, (pos, text) => {\n        const m = text.match(/\\n/);\n        if (m && m.index !== undefined) {\n          return m.index + 1;\n        }\n        return text.length;\n      }, 1 /* PartialAcceptTriggerKind.Line */);\n    })();\n  }\n  _acceptNext(editor, getAcceptUntilIndex, kind) {\n    var _this9 = this;\n    return _asyncToGenerator(function* () {\n      if (editor.getModel() !== _this9.textModel) {\n        throw new BugIndicatingError();\n      }\n      const state = _this9.state.get();\n      if (!state || state.primaryGhostText.isEmpty() || !state.inlineCompletion) {\n        return;\n      }\n      const ghostText = state.primaryGhostText;\n      const completion = state.inlineCompletion.toInlineCompletion(undefined);\n      if (completion.snippetInfo || completion.filterText !== completion.insertText) {\n        // not in WYSIWYG mode, partial commit might change completion, thus it is not supported\n        yield _this9.accept(editor);\n        return;\n      }\n      const firstPart = ghostText.parts[0];\n      const ghostTextPos = new Position(ghostText.lineNumber, firstPart.column);\n      const ghostTextVal = firstPart.text;\n      const acceptUntilIndexExclusive = getAcceptUntilIndex(ghostTextPos, ghostTextVal);\n      if (acceptUntilIndexExclusive === ghostTextVal.length && ghostText.parts.length === 1) {\n        _this9.accept(editor);\n        return;\n      }\n      const partialGhostTextVal = ghostTextVal.substring(0, acceptUntilIndexExclusive);\n      const positions = _this9._positions.get();\n      const cursorPosition = positions[0];\n      // Executing the edit might free the completion, so we have to hold a reference on it.\n      completion.source.addRef();\n      try {\n        _this9._isAcceptingPartially = true;\n        try {\n          editor.pushUndoStop();\n          const replaceRange = Range.fromPositions(cursorPosition, ghostTextPos);\n          const newText = editor.getModel().getValueInRange(replaceRange) + partialGhostTextVal;\n          const primaryEdit = new SingleTextEdit(replaceRange, newText);\n          const edits = [primaryEdit, ...getSecondaryEdits(_this9.textModel, positions, primaryEdit)];\n          const selections = getEndPositionsAfterApplying(edits).map(p => Selection.fromPositions(p));\n          editor.executeEdits('inlineSuggestion.accept', edits.map(edit => EditOperation.replace(edit.range, edit.text)));\n          editor.setSelections(selections, 'inlineCompletionPartialAccept');\n          editor.revealPositionInCenterIfOutsideViewport(editor.getPosition(), 1 /* ScrollType.Immediate */);\n        } finally {\n          _this9._isAcceptingPartially = false;\n        }\n        if (completion.source.provider.handlePartialAccept) {\n          const acceptedRange = Range.fromPositions(completion.range.getStartPosition(), TextLength.ofText(partialGhostTextVal).addToPosition(ghostTextPos));\n          // This assumes that the inline completion and the model use the same EOL style.\n          const text = editor.getModel().getValueInRange(acceptedRange, 1 /* EndOfLinePreference.LF */);\n          completion.source.provider.handlePartialAccept(completion.source.inlineCompletions, completion.sourceInlineCompletion, text.length, {\n            kind\n          });\n        }\n      } finally {\n        completion.source.removeRef();\n      }\n    })();\n  }\n  handleSuggestAccepted(item) {\n    const itemEdit = singleTextRemoveCommonPrefix(item.toSingleTextEdit(), this.textModel);\n    const augmentedCompletion = this._computeAugmentation(itemEdit, undefined);\n    if (!augmentedCompletion) {\n      return;\n    }\n    const inlineCompletion = augmentedCompletion.completion.inlineCompletion;\n    inlineCompletion.source.provider.handlePartialAccept?.(inlineCompletion.source.inlineCompletions, inlineCompletion.sourceInlineCompletion, itemEdit.text.length, {\n      kind: 2 /* PartialAcceptTriggerKind.Suggest */\n    });\n  }\n};\nInlineCompletionsModel = __decorate([__param(9, IInstantiationService), __param(10, ICommandService), __param(11, ILanguageConfigurationService)], InlineCompletionsModel);\nexport { InlineCompletionsModel };\nexport var VersionIdChangeReason = /*#__PURE__*/function (VersionIdChangeReason) {\n  VersionIdChangeReason[VersionIdChangeReason[\"Undo\"] = 0] = \"Undo\";\n  VersionIdChangeReason[VersionIdChangeReason[\"Redo\"] = 1] = \"Redo\";\n  VersionIdChangeReason[VersionIdChangeReason[\"AcceptWord\"] = 2] = \"AcceptWord\";\n  VersionIdChangeReason[VersionIdChangeReason[\"Other\"] = 3] = \"Other\";\n  return VersionIdChangeReason;\n}(VersionIdChangeReason || {});\nexport function getSecondaryEdits(textModel, positions, primaryEdit) {\n  if (positions.length === 1) {\n    // No secondary cursor positions\n    return [];\n  }\n  const primaryPosition = positions[0];\n  const secondaryPositions = positions.slice(1);\n  const primaryEditStartPosition = primaryEdit.range.getStartPosition();\n  const primaryEditEndPosition = primaryEdit.range.getEndPosition();\n  const replacedTextAfterPrimaryCursor = textModel.getValueInRange(Range.fromPositions(primaryPosition, primaryEditEndPosition));\n  const positionWithinTextEdit = subtractPositions(primaryPosition, primaryEditStartPosition);\n  if (positionWithinTextEdit.lineNumber < 1) {\n    onUnexpectedError(new BugIndicatingError(`positionWithinTextEdit line number should be bigger than 0.\n\t\t\tInvalid subtraction between ${primaryPosition.toString()} and ${primaryEditStartPosition.toString()}`));\n    return [];\n  }\n  const secondaryEditText = substringPos(primaryEdit.text, positionWithinTextEdit);\n  return secondaryPositions.map(pos => {\n    const posEnd = addPositions(subtractPositions(pos, primaryEditStartPosition), primaryEditEndPosition);\n    const textAfterSecondaryCursor = textModel.getValueInRange(Range.fromPositions(pos, posEnd));\n    const l = commonPrefixLength(replacedTextAfterPrimaryCursor, textAfterSecondaryCursor);\n    const range = Range.fromPositions(pos, pos.delta(0, l));\n    return new SingleTextEdit(range, secondaryEditText);\n  });\n}\nfunction substringPos(text, pos) {\n  let subtext = '';\n  const lines = splitLinesIncludeSeparators(text);\n  for (let i = pos.lineNumber - 1; i < lines.length; i++) {\n    subtext += lines[i].substring(i === pos.lineNumber - 1 ? pos.column - 1 : 0);\n  }\n  return subtext;\n}\nfunction getEndPositionsAfterApplying(edits) {\n  const sortPerm = Permutation.createSortPermutation(edits, compareBy(e => e.range, Range.compareRangesUsingStarts));\n  const edit = new TextEdit(sortPerm.apply(edits));\n  const sortedNewRanges = edit.getNewRanges();\n  const newRanges = sortPerm.inverse().apply(sortedNewRanges);\n  return newRanges.map(range => range.getEndPosition());\n}", "map": {"version": 3, "names": ["__decorate", "decorators", "target", "key", "desc", "c", "arguments", "length", "r", "Object", "getOwnPropertyDescriptor", "d", "Reflect", "decorate", "i", "defineProperty", "__param", "paramIndex", "decorator", "compareBy", "Permutation", "mapFindFirst", "itemsEquals", "BugIndicatingError", "onUnexpectedError", "onUnexpectedExternalError", "Disposable", "autorun", "derived", "derivedHandleChanges", "derivedOpts", "observableSignal", "observableValue", "recomputeInitiallyAndOnChange", "subtransaction", "transaction", "commonPrefixLength", "splitLinesIncludeSeparators", "isDefined", "EditOperation", "Position", "Range", "Selection", "SingleTextEdit", "TextEdit", "TextLength", "InlineCompletionTriggerKind", "ILanguageConfigurationService", "GhostText", "ghostTextOrReplacementEquals", "ghostTextsOrReplacementsEqual", "InlineCompletionsSource", "computeGhostText", "singleTextEditAugments", "singleTextRemoveCommonPrefix", "addPositions", "subtractPositions", "SnippetController2", "ICommandService", "IInstantiationService", "InlineCompletionsModel", "isAcceptingPartially", "_isAcceptingPartially", "constructor", "textModel", "selectedSuggestItem", "_textModelVersionId", "_positions", "_debounceValue", "_suggestPreviewEnabled", "_suggestPreviewMode", "_inlineSuggestMode", "_enabled", "_instantiationService", "_commandService", "_languageConfigurationService", "_source", "_register", "createInstance", "_isActive", "_forceUpdateExplicitlySignal", "_selectedInlineCompletionId", "undefined", "_primaryPosition", "reader", "read", "_preserveCurrentCompletionReasons", "Set", "VersionIdChangeReason", "Redo", "Undo", "AcceptWord", "_fetchInlineCompletionsPromise", "owner", "createEmptyChangeSummary", "preserveCurrentCompletion", "inlineCompletionTriggerKind", "Automatic", "handleChange", "ctx", "changeSummary", "<PERSON><PERSON><PERSON><PERSON>", "has", "_getReason", "change", "Explicit", "shouldUpdate", "cancelUpdate", "suggestWidgetInlineCompletions", "get", "suggestItem", "inlineCompletions", "tx", "request", "versionId", "set", "clone", "clearSuggestWidgetInlineCompletions", "cursorPosition", "context", "trigger<PERSON>ind", "selectedSuggestionInfo", "toSelectedSuggestionInfo", "itemToPreserveCandidate", "selectedInlineCompletion", "itemToPreserve", "forwardStable", "fetch", "_filteredInlineCompletionItems", "equalsFn", "filteredCompletions", "filter", "isVisible", "selectedInlineCompletionIndex", "selectedInlineCompletionId", "idx", "findIndex", "v", "semanticId", "activeCommands", "inlineCompletion", "source", "commands", "lastTriggerKind", "map", "inlineCompletionsCount", "state", "a", "b", "ghostTexts", "model", "suggestCompletionEdit", "toSingleTextEdit", "augmentation", "_computeAugmentation", "isSuggestionPreviewEnabled", "fullEdit", "edit", "fullEditPreviewLength", "text", "mode", "positions", "edits", "getSecondaryEdits", "primaryGhostText", "range", "endLineNumber", "completion", "replacement", "lastItem", "item", "src", "provider", "handleItemDidShow", "sourceInlineCompletion", "insertText", "e", "isUndoing", "isRedoing", "Other", "trigger", "_this", "_asyncToGenerator", "triggerExplicitly", "_this2", "stop", "clear", "suggestCompletion", "candidateInlineCompletions", "augmentedCompletion", "fromPositions", "getStartPosition", "getEndPosition", "_deltaSelectedInlineCompletionIndex", "delta", "_this3", "completions", "newIdx", "next", "_this4", "previous", "_this5", "accept", "editor", "_this6", "getModel", "isEmpty", "toInlineCompletion", "command", "addRef", "pushUndoStop", "snippetInfo", "executeEdits", "replace", "additionalTextEdits", "setPosition", "insert", "snippet", "undoStopBefore", "selections", "getEndPositionsAfterApplying", "p", "setSelections", "executeCommand", "id", "then", "removeRef", "acceptNextWord", "_this7", "_acceptNext", "pos", "langId", "getLanguageIdAtPosition", "lineNumber", "column", "config", "getLanguageConfiguration", "wordRegExp", "RegExp", "wordDefinition", "flags", "m1", "match", "acceptUntilIndexExclusive", "index", "wsRegExp", "m2", "exec", "acceptNextLine", "_this8", "m", "getAcceptUntilIndex", "kind", "_this9", "ghostText", "filterText", "firstPart", "parts", "ghostTextPos", "ghostTextVal", "partialGhostTextVal", "substring", "replaceRange", "newText", "getValueInRange", "primaryEdit", "revealPositionInCenterIfOutsideViewport", "getPosition", "handlePartialAccept", "<PERSON><PERSON><PERSON><PERSON>", "ofText", "addToPosition", "handleSuggestAccepted", "itemEdit", "primaryPosition", "secondaryPositions", "slice", "primaryEditStartPosition", "primaryEditEndPosition", "replacedTextAfterPrimaryCursor", "positionWithinTextEdit", "toString", "secondaryEditText", "substringPos", "posEnd", "textAfterSecondaryCursor", "l", "subtext", "lines", "sortPerm", "createSortPermutation", "compareRangesUsingStarts", "apply", "sortedNewRanges", "getNewRanges", "newRang<PERSON>", "inverse"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/contrib/inlineCompletions/browser/model/inlineCompletionsModel.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = (this && this.__param) || function (paramIndex, decorator) {\n    return function (target, key) { decorator(target, key, paramIndex); }\n};\nimport { compareBy, Permutation } from '../../../../../base/common/arrays.js';\nimport { mapFindFirst } from '../../../../../base/common/arraysFind.js';\nimport { itemsEquals } from '../../../../../base/common/equals.js';\nimport { BugIndicatingError, onUnexpectedError, onUnexpectedExternalError } from '../../../../../base/common/errors.js';\nimport { Disposable } from '../../../../../base/common/lifecycle.js';\nimport { autorun, derived, derivedHandleChanges, derivedOpts, observableSignal, observableValue, recomputeInitiallyAndOnChange, subtransaction, transaction } from '../../../../../base/common/observable.js';\nimport { commonPrefixLength, splitLinesIncludeSeparators } from '../../../../../base/common/strings.js';\nimport { isDefined } from '../../../../../base/common/types.js';\nimport { EditOperation } from '../../../../common/core/editOperation.js';\nimport { Position } from '../../../../common/core/position.js';\nimport { Range } from '../../../../common/core/range.js';\nimport { Selection } from '../../../../common/core/selection.js';\nimport { SingleTextEdit, TextEdit } from '../../../../common/core/textEdit.js';\nimport { TextLength } from '../../../../common/core/textLength.js';\nimport { InlineCompletionTriggerKind } from '../../../../common/languages.js';\nimport { ILanguageConfigurationService } from '../../../../common/languages/languageConfigurationRegistry.js';\nimport { GhostText, ghostTextOrReplacementEquals, ghostTextsOrReplacementsEqual } from './ghostText.js';\nimport { InlineCompletionsSource } from './inlineCompletionsSource.js';\nimport { computeGhostText, singleTextEditAugments, singleTextRemoveCommonPrefix } from './singleTextEdit.js';\nimport { addPositions, subtractPositions } from '../utils.js';\nimport { SnippetController2 } from '../../../snippet/browser/snippetController2.js';\nimport { ICommandService } from '../../../../../platform/commands/common/commands.js';\nimport { IInstantiationService } from '../../../../../platform/instantiation/common/instantiation.js';\nlet InlineCompletionsModel = class InlineCompletionsModel extends Disposable {\n    get isAcceptingPartially() { return this._isAcceptingPartially; }\n    constructor(textModel, selectedSuggestItem, _textModelVersionId, _positions, _debounceValue, _suggestPreviewEnabled, _suggestPreviewMode, _inlineSuggestMode, _enabled, _instantiationService, _commandService, _languageConfigurationService) {\n        super();\n        this.textModel = textModel;\n        this.selectedSuggestItem = selectedSuggestItem;\n        this._textModelVersionId = _textModelVersionId;\n        this._positions = _positions;\n        this._debounceValue = _debounceValue;\n        this._suggestPreviewEnabled = _suggestPreviewEnabled;\n        this._suggestPreviewMode = _suggestPreviewMode;\n        this._inlineSuggestMode = _inlineSuggestMode;\n        this._enabled = _enabled;\n        this._instantiationService = _instantiationService;\n        this._commandService = _commandService;\n        this._languageConfigurationService = _languageConfigurationService;\n        this._source = this._register(this._instantiationService.createInstance(InlineCompletionsSource, this.textModel, this._textModelVersionId, this._debounceValue));\n        this._isActive = observableValue(this, false);\n        this._forceUpdateExplicitlySignal = observableSignal(this);\n        // We use a semantic id to keep the same inline completion selected even if the provider reorders the completions.\n        this._selectedInlineCompletionId = observableValue(this, undefined);\n        this._primaryPosition = derived(this, reader => this._positions.read(reader)[0] ?? new Position(1, 1));\n        this._isAcceptingPartially = false;\n        this._preserveCurrentCompletionReasons = new Set([\n            VersionIdChangeReason.Redo,\n            VersionIdChangeReason.Undo,\n            VersionIdChangeReason.AcceptWord,\n        ]);\n        this._fetchInlineCompletionsPromise = derivedHandleChanges({\n            owner: this,\n            createEmptyChangeSummary: () => ({\n                preserveCurrentCompletion: false,\n                inlineCompletionTriggerKind: InlineCompletionTriggerKind.Automatic\n            }),\n            handleChange: (ctx, changeSummary) => {\n                /** @description fetch inline completions */\n                if (ctx.didChange(this._textModelVersionId) && this._preserveCurrentCompletionReasons.has(this._getReason(ctx.change))) {\n                    changeSummary.preserveCurrentCompletion = true;\n                }\n                else if (ctx.didChange(this._forceUpdateExplicitlySignal)) {\n                    changeSummary.inlineCompletionTriggerKind = InlineCompletionTriggerKind.Explicit;\n                }\n                return true;\n            },\n        }, (reader, changeSummary) => {\n            this._forceUpdateExplicitlySignal.read(reader);\n            const shouldUpdate = (this._enabled.read(reader) && this.selectedSuggestItem.read(reader)) || this._isActive.read(reader);\n            if (!shouldUpdate) {\n                this._source.cancelUpdate();\n                return undefined;\n            }\n            this._textModelVersionId.read(reader); // Refetch on text change\n            const suggestWidgetInlineCompletions = this._source.suggestWidgetInlineCompletions.get();\n            const suggestItem = this.selectedSuggestItem.read(reader);\n            if (suggestWidgetInlineCompletions && !suggestItem) {\n                const inlineCompletions = this._source.inlineCompletions.get();\n                transaction(tx => {\n                    /** @description Seed inline completions with (newer) suggest widget inline completions */\n                    if (!inlineCompletions || suggestWidgetInlineCompletions.request.versionId > inlineCompletions.request.versionId) {\n                        this._source.inlineCompletions.set(suggestWidgetInlineCompletions.clone(), tx);\n                    }\n                    this._source.clearSuggestWidgetInlineCompletions(tx);\n                });\n            }\n            const cursorPosition = this._primaryPosition.read(reader);\n            const context = {\n                triggerKind: changeSummary.inlineCompletionTriggerKind,\n                selectedSuggestionInfo: suggestItem?.toSelectedSuggestionInfo(),\n            };\n            const itemToPreserveCandidate = this.selectedInlineCompletion.get();\n            const itemToPreserve = changeSummary.preserveCurrentCompletion || itemToPreserveCandidate?.forwardStable\n                ? itemToPreserveCandidate : undefined;\n            return this._source.fetch(cursorPosition, context, itemToPreserve);\n        });\n        this._filteredInlineCompletionItems = derivedOpts({ owner: this, equalsFn: itemsEquals() }, reader => {\n            const c = this._source.inlineCompletions.read(reader);\n            if (!c) {\n                return [];\n            }\n            const cursorPosition = this._primaryPosition.read(reader);\n            const filteredCompletions = c.inlineCompletions.filter(c => c.isVisible(this.textModel, cursorPosition, reader));\n            return filteredCompletions;\n        });\n        this.selectedInlineCompletionIndex = derived(this, (reader) => {\n            const selectedInlineCompletionId = this._selectedInlineCompletionId.read(reader);\n            const filteredCompletions = this._filteredInlineCompletionItems.read(reader);\n            const idx = this._selectedInlineCompletionId === undefined ? -1\n                : filteredCompletions.findIndex(v => v.semanticId === selectedInlineCompletionId);\n            if (idx === -1) {\n                // Reset the selection so that the selection does not jump back when it appears again\n                this._selectedInlineCompletionId.set(undefined, undefined);\n                return 0;\n            }\n            return idx;\n        });\n        this.selectedInlineCompletion = derived(this, (reader) => {\n            const filteredCompletions = this._filteredInlineCompletionItems.read(reader);\n            const idx = this.selectedInlineCompletionIndex.read(reader);\n            return filteredCompletions[idx];\n        });\n        this.activeCommands = derivedOpts({ owner: this, equalsFn: itemsEquals() }, r => this.selectedInlineCompletion.read(r)?.inlineCompletion.source.inlineCompletions.commands ?? []);\n        this.lastTriggerKind = this._source.inlineCompletions.map(this, v => v?.request.context.triggerKind);\n        this.inlineCompletionsCount = derived(this, reader => {\n            if (this.lastTriggerKind.read(reader) === InlineCompletionTriggerKind.Explicit) {\n                return this._filteredInlineCompletionItems.read(reader).length;\n            }\n            else {\n                return undefined;\n            }\n        });\n        this.state = derivedOpts({\n            owner: this,\n            equalsFn: (a, b) => {\n                if (!a || !b) {\n                    return a === b;\n                }\n                return ghostTextsOrReplacementsEqual(a.ghostTexts, b.ghostTexts)\n                    && a.inlineCompletion === b.inlineCompletion\n                    && a.suggestItem === b.suggestItem;\n            }\n        }, (reader) => {\n            const model = this.textModel;\n            const suggestItem = this.selectedSuggestItem.read(reader);\n            if (suggestItem) {\n                const suggestCompletionEdit = singleTextRemoveCommonPrefix(suggestItem.toSingleTextEdit(), model);\n                const augmentation = this._computeAugmentation(suggestCompletionEdit, reader);\n                const isSuggestionPreviewEnabled = this._suggestPreviewEnabled.read(reader);\n                if (!isSuggestionPreviewEnabled && !augmentation) {\n                    return undefined;\n                }\n                const fullEdit = augmentation?.edit ?? suggestCompletionEdit;\n                const fullEditPreviewLength = augmentation ? augmentation.edit.text.length - suggestCompletionEdit.text.length : 0;\n                const mode = this._suggestPreviewMode.read(reader);\n                const positions = this._positions.read(reader);\n                const edits = [fullEdit, ...getSecondaryEdits(this.textModel, positions, fullEdit)];\n                const ghostTexts = edits\n                    .map((edit, idx) => computeGhostText(edit, model, mode, positions[idx], fullEditPreviewLength))\n                    .filter(isDefined);\n                const primaryGhostText = ghostTexts[0] ?? new GhostText(fullEdit.range.endLineNumber, []);\n                return { edits, primaryGhostText, ghostTexts, inlineCompletion: augmentation?.completion, suggestItem };\n            }\n            else {\n                if (!this._isActive.read(reader)) {\n                    return undefined;\n                }\n                const inlineCompletion = this.selectedInlineCompletion.read(reader);\n                if (!inlineCompletion) {\n                    return undefined;\n                }\n                const replacement = inlineCompletion.toSingleTextEdit(reader);\n                const mode = this._inlineSuggestMode.read(reader);\n                const positions = this._positions.read(reader);\n                const edits = [replacement, ...getSecondaryEdits(this.textModel, positions, replacement)];\n                const ghostTexts = edits\n                    .map((edit, idx) => computeGhostText(edit, model, mode, positions[idx], 0))\n                    .filter(isDefined);\n                if (!ghostTexts[0]) {\n                    return undefined;\n                }\n                return { edits, primaryGhostText: ghostTexts[0], ghostTexts, inlineCompletion, suggestItem: undefined };\n            }\n        });\n        this.ghostTexts = derivedOpts({ owner: this, equalsFn: ghostTextsOrReplacementsEqual }, reader => {\n            const v = this.state.read(reader);\n            if (!v) {\n                return undefined;\n            }\n            return v.ghostTexts;\n        });\n        this.primaryGhostText = derivedOpts({ owner: this, equalsFn: ghostTextOrReplacementEquals }, reader => {\n            const v = this.state.read(reader);\n            if (!v) {\n                return undefined;\n            }\n            return v?.primaryGhostText;\n        });\n        this._register(recomputeInitiallyAndOnChange(this._fetchInlineCompletionsPromise));\n        let lastItem = undefined;\n        this._register(autorun(reader => {\n            /** @description call handleItemDidShow */\n            const item = this.state.read(reader);\n            const completion = item?.inlineCompletion;\n            if (completion?.semanticId !== lastItem?.semanticId) {\n                lastItem = completion;\n                if (completion) {\n                    const i = completion.inlineCompletion;\n                    const src = i.source;\n                    src.provider.handleItemDidShow?.(src.inlineCompletions, i.sourceInlineCompletion, i.insertText);\n                }\n            }\n        }));\n    }\n    _getReason(e) {\n        if (e?.isUndoing) {\n            return VersionIdChangeReason.Undo;\n        }\n        if (e?.isRedoing) {\n            return VersionIdChangeReason.Redo;\n        }\n        if (this.isAcceptingPartially) {\n            return VersionIdChangeReason.AcceptWord;\n        }\n        return VersionIdChangeReason.Other;\n    }\n    async trigger(tx) {\n        this._isActive.set(true, tx);\n        await this._fetchInlineCompletionsPromise.get();\n    }\n    async triggerExplicitly(tx) {\n        subtransaction(tx, tx => {\n            this._isActive.set(true, tx);\n            this._forceUpdateExplicitlySignal.trigger(tx);\n        });\n        await this._fetchInlineCompletionsPromise.get();\n    }\n    stop(tx) {\n        subtransaction(tx, tx => {\n            this._isActive.set(false, tx);\n            this._source.clear(tx);\n        });\n    }\n    _computeAugmentation(suggestCompletion, reader) {\n        const model = this.textModel;\n        const suggestWidgetInlineCompletions = this._source.suggestWidgetInlineCompletions.read(reader);\n        const candidateInlineCompletions = suggestWidgetInlineCompletions\n            ? suggestWidgetInlineCompletions.inlineCompletions\n            : [this.selectedInlineCompletion.read(reader)].filter(isDefined);\n        const augmentedCompletion = mapFindFirst(candidateInlineCompletions, completion => {\n            let r = completion.toSingleTextEdit(reader);\n            r = singleTextRemoveCommonPrefix(r, model, Range.fromPositions(r.range.getStartPosition(), suggestCompletion.range.getEndPosition()));\n            return singleTextEditAugments(r, suggestCompletion) ? { completion, edit: r } : undefined;\n        });\n        return augmentedCompletion;\n    }\n    async _deltaSelectedInlineCompletionIndex(delta) {\n        await this.triggerExplicitly();\n        const completions = this._filteredInlineCompletionItems.get() || [];\n        if (completions.length > 0) {\n            const newIdx = (this.selectedInlineCompletionIndex.get() + delta + completions.length) % completions.length;\n            this._selectedInlineCompletionId.set(completions[newIdx].semanticId, undefined);\n        }\n        else {\n            this._selectedInlineCompletionId.set(undefined, undefined);\n        }\n    }\n    async next() {\n        await this._deltaSelectedInlineCompletionIndex(1);\n    }\n    async previous() {\n        await this._deltaSelectedInlineCompletionIndex(-1);\n    }\n    async accept(editor) {\n        if (editor.getModel() !== this.textModel) {\n            throw new BugIndicatingError();\n        }\n        const state = this.state.get();\n        if (!state || state.primaryGhostText.isEmpty() || !state.inlineCompletion) {\n            return;\n        }\n        const completion = state.inlineCompletion.toInlineCompletion(undefined);\n        if (completion.command) {\n            // Make sure the completion list will not be disposed.\n            completion.source.addRef();\n        }\n        editor.pushUndoStop();\n        if (completion.snippetInfo) {\n            editor.executeEdits('inlineSuggestion.accept', [\n                EditOperation.replace(completion.range, ''),\n                ...completion.additionalTextEdits\n            ]);\n            editor.setPosition(completion.snippetInfo.range.getStartPosition(), 'inlineCompletionAccept');\n            SnippetController2.get(editor)?.insert(completion.snippetInfo.snippet, { undoStopBefore: false });\n        }\n        else {\n            const edits = state.edits;\n            const selections = getEndPositionsAfterApplying(edits).map(p => Selection.fromPositions(p));\n            editor.executeEdits('inlineSuggestion.accept', [\n                ...edits.map(edit => EditOperation.replace(edit.range, edit.text)),\n                ...completion.additionalTextEdits\n            ]);\n            editor.setSelections(selections, 'inlineCompletionAccept');\n        }\n        // Reset before invoking the command, as the command might cause a follow up trigger (which we don't want to reset).\n        this.stop();\n        if (completion.command) {\n            await this._commandService\n                .executeCommand(completion.command.id, ...(completion.command.arguments || []))\n                .then(undefined, onUnexpectedExternalError);\n            completion.source.removeRef();\n        }\n    }\n    async acceptNextWord(editor) {\n        await this._acceptNext(editor, (pos, text) => {\n            const langId = this.textModel.getLanguageIdAtPosition(pos.lineNumber, pos.column);\n            const config = this._languageConfigurationService.getLanguageConfiguration(langId);\n            const wordRegExp = new RegExp(config.wordDefinition.source, config.wordDefinition.flags.replace('g', ''));\n            const m1 = text.match(wordRegExp);\n            let acceptUntilIndexExclusive = 0;\n            if (m1 && m1.index !== undefined) {\n                if (m1.index === 0) {\n                    acceptUntilIndexExclusive = m1[0].length;\n                }\n                else {\n                    acceptUntilIndexExclusive = m1.index;\n                }\n            }\n            else {\n                acceptUntilIndexExclusive = text.length;\n            }\n            const wsRegExp = /\\s+/g;\n            const m2 = wsRegExp.exec(text);\n            if (m2 && m2.index !== undefined) {\n                if (m2.index + m2[0].length < acceptUntilIndexExclusive) {\n                    acceptUntilIndexExclusive = m2.index + m2[0].length;\n                }\n            }\n            return acceptUntilIndexExclusive;\n        }, 0 /* PartialAcceptTriggerKind.Word */);\n    }\n    async acceptNextLine(editor) {\n        await this._acceptNext(editor, (pos, text) => {\n            const m = text.match(/\\n/);\n            if (m && m.index !== undefined) {\n                return m.index + 1;\n            }\n            return text.length;\n        }, 1 /* PartialAcceptTriggerKind.Line */);\n    }\n    async _acceptNext(editor, getAcceptUntilIndex, kind) {\n        if (editor.getModel() !== this.textModel) {\n            throw new BugIndicatingError();\n        }\n        const state = this.state.get();\n        if (!state || state.primaryGhostText.isEmpty() || !state.inlineCompletion) {\n            return;\n        }\n        const ghostText = state.primaryGhostText;\n        const completion = state.inlineCompletion.toInlineCompletion(undefined);\n        if (completion.snippetInfo || completion.filterText !== completion.insertText) {\n            // not in WYSIWYG mode, partial commit might change completion, thus it is not supported\n            await this.accept(editor);\n            return;\n        }\n        const firstPart = ghostText.parts[0];\n        const ghostTextPos = new Position(ghostText.lineNumber, firstPart.column);\n        const ghostTextVal = firstPart.text;\n        const acceptUntilIndexExclusive = getAcceptUntilIndex(ghostTextPos, ghostTextVal);\n        if (acceptUntilIndexExclusive === ghostTextVal.length && ghostText.parts.length === 1) {\n            this.accept(editor);\n            return;\n        }\n        const partialGhostTextVal = ghostTextVal.substring(0, acceptUntilIndexExclusive);\n        const positions = this._positions.get();\n        const cursorPosition = positions[0];\n        // Executing the edit might free the completion, so we have to hold a reference on it.\n        completion.source.addRef();\n        try {\n            this._isAcceptingPartially = true;\n            try {\n                editor.pushUndoStop();\n                const replaceRange = Range.fromPositions(cursorPosition, ghostTextPos);\n                const newText = editor.getModel().getValueInRange(replaceRange) + partialGhostTextVal;\n                const primaryEdit = new SingleTextEdit(replaceRange, newText);\n                const edits = [primaryEdit, ...getSecondaryEdits(this.textModel, positions, primaryEdit)];\n                const selections = getEndPositionsAfterApplying(edits).map(p => Selection.fromPositions(p));\n                editor.executeEdits('inlineSuggestion.accept', edits.map(edit => EditOperation.replace(edit.range, edit.text)));\n                editor.setSelections(selections, 'inlineCompletionPartialAccept');\n                editor.revealPositionInCenterIfOutsideViewport(editor.getPosition(), 1 /* ScrollType.Immediate */);\n            }\n            finally {\n                this._isAcceptingPartially = false;\n            }\n            if (completion.source.provider.handlePartialAccept) {\n                const acceptedRange = Range.fromPositions(completion.range.getStartPosition(), TextLength.ofText(partialGhostTextVal).addToPosition(ghostTextPos));\n                // This assumes that the inline completion and the model use the same EOL style.\n                const text = editor.getModel().getValueInRange(acceptedRange, 1 /* EndOfLinePreference.LF */);\n                completion.source.provider.handlePartialAccept(completion.source.inlineCompletions, completion.sourceInlineCompletion, text.length, { kind, });\n            }\n        }\n        finally {\n            completion.source.removeRef();\n        }\n    }\n    handleSuggestAccepted(item) {\n        const itemEdit = singleTextRemoveCommonPrefix(item.toSingleTextEdit(), this.textModel);\n        const augmentedCompletion = this._computeAugmentation(itemEdit, undefined);\n        if (!augmentedCompletion) {\n            return;\n        }\n        const inlineCompletion = augmentedCompletion.completion.inlineCompletion;\n        inlineCompletion.source.provider.handlePartialAccept?.(inlineCompletion.source.inlineCompletions, inlineCompletion.sourceInlineCompletion, itemEdit.text.length, {\n            kind: 2 /* PartialAcceptTriggerKind.Suggest */,\n        });\n    }\n};\nInlineCompletionsModel = __decorate([\n    __param(9, IInstantiationService),\n    __param(10, ICommandService),\n    __param(11, ILanguageConfigurationService)\n], InlineCompletionsModel);\nexport { InlineCompletionsModel };\nexport var VersionIdChangeReason;\n(function (VersionIdChangeReason) {\n    VersionIdChangeReason[VersionIdChangeReason[\"Undo\"] = 0] = \"Undo\";\n    VersionIdChangeReason[VersionIdChangeReason[\"Redo\"] = 1] = \"Redo\";\n    VersionIdChangeReason[VersionIdChangeReason[\"AcceptWord\"] = 2] = \"AcceptWord\";\n    VersionIdChangeReason[VersionIdChangeReason[\"Other\"] = 3] = \"Other\";\n})(VersionIdChangeReason || (VersionIdChangeReason = {}));\nexport function getSecondaryEdits(textModel, positions, primaryEdit) {\n    if (positions.length === 1) {\n        // No secondary cursor positions\n        return [];\n    }\n    const primaryPosition = positions[0];\n    const secondaryPositions = positions.slice(1);\n    const primaryEditStartPosition = primaryEdit.range.getStartPosition();\n    const primaryEditEndPosition = primaryEdit.range.getEndPosition();\n    const replacedTextAfterPrimaryCursor = textModel.getValueInRange(Range.fromPositions(primaryPosition, primaryEditEndPosition));\n    const positionWithinTextEdit = subtractPositions(primaryPosition, primaryEditStartPosition);\n    if (positionWithinTextEdit.lineNumber < 1) {\n        onUnexpectedError(new BugIndicatingError(`positionWithinTextEdit line number should be bigger than 0.\n\t\t\tInvalid subtraction between ${primaryPosition.toString()} and ${primaryEditStartPosition.toString()}`));\n        return [];\n    }\n    const secondaryEditText = substringPos(primaryEdit.text, positionWithinTextEdit);\n    return secondaryPositions.map(pos => {\n        const posEnd = addPositions(subtractPositions(pos, primaryEditStartPosition), primaryEditEndPosition);\n        const textAfterSecondaryCursor = textModel.getValueInRange(Range.fromPositions(pos, posEnd));\n        const l = commonPrefixLength(replacedTextAfterPrimaryCursor, textAfterSecondaryCursor);\n        const range = Range.fromPositions(pos, pos.delta(0, l));\n        return new SingleTextEdit(range, secondaryEditText);\n    });\n}\nfunction substringPos(text, pos) {\n    let subtext = '';\n    const lines = splitLinesIncludeSeparators(text);\n    for (let i = pos.lineNumber - 1; i < lines.length; i++) {\n        subtext += lines[i].substring(i === pos.lineNumber - 1 ? pos.column - 1 : 0);\n    }\n    return subtext;\n}\nfunction getEndPositionsAfterApplying(edits) {\n    const sortPerm = Permutation.createSortPermutation(edits, compareBy(e => e.range, Range.compareRangesUsingStarts));\n    const edit = new TextEdit(sortPerm.apply(edits));\n    const sortedNewRanges = edit.getNewRanges();\n    const newRanges = sortPerm.inverse().apply(sortedNewRanges);\n    return newRanges.map(range => range.getEndPosition());\n}\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA,IAAIA,UAAU,GAAI,IAAI,IAAI,IAAI,CAACA,UAAU,IAAK,UAAUC,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAE;EACnF,IAAIC,CAAC,GAAGC,SAAS,CAACC,MAAM;IAAEC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGH,MAAM,GAAGE,IAAI,KAAK,IAAI,GAAGA,IAAI,GAAGK,MAAM,CAACC,wBAAwB,CAACR,MAAM,EAAEC,GAAG,CAAC,GAAGC,IAAI;IAAEO,CAAC;EAC5H,IAAI,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,CAACC,QAAQ,KAAK,UAAU,EAAEL,CAAC,GAAGI,OAAO,CAACC,QAAQ,CAACZ,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,CAAC,CAAC,KAC1H,KAAK,IAAIU,CAAC,GAAGb,UAAU,CAACM,MAAM,GAAG,CAAC,EAAEO,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE,IAAIH,CAAC,GAAGV,UAAU,CAACa,CAAC,CAAC,EAAEN,CAAC,GAAG,CAACH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACH,CAAC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACT,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,GAAGG,CAAC,CAACT,MAAM,EAAEC,GAAG,CAAC,KAAKK,CAAC;EACjJ,OAAOH,CAAC,GAAG,CAAC,IAAIG,CAAC,IAAIC,MAAM,CAACM,cAAc,CAACb,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,EAAEA,CAAC;AACjE,CAAC;AACD,IAAIQ,OAAO,GAAI,IAAI,IAAI,IAAI,CAACA,OAAO,IAAK,UAAUC,UAAU,EAAEC,SAAS,EAAE;EACrE,OAAO,UAAUhB,MAAM,EAAEC,GAAG,EAAE;IAAEe,SAAS,CAAChB,MAAM,EAAEC,GAAG,EAAEc,UAAU,CAAC;EAAE,CAAC;AACzE,CAAC;AACD,SAASE,SAAS,EAAEC,WAAW,QAAQ,sCAAsC;AAC7E,SAASC,YAAY,QAAQ,0CAA0C;AACvE,SAASC,WAAW,QAAQ,sCAAsC;AAClE,SAASC,kBAAkB,EAAEC,iBAAiB,EAAEC,yBAAyB,QAAQ,sCAAsC;AACvH,SAASC,UAAU,QAAQ,yCAAyC;AACpE,SAASC,OAAO,EAAEC,OAAO,EAAEC,oBAAoB,EAAEC,WAAW,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,6BAA6B,EAAEC,cAAc,EAAEC,WAAW,QAAQ,0CAA0C;AAC7M,SAASC,kBAAkB,EAAEC,2BAA2B,QAAQ,uCAAuC;AACvG,SAASC,SAAS,QAAQ,qCAAqC;AAC/D,SAASC,aAAa,QAAQ,0CAA0C;AACxE,SAASC,QAAQ,QAAQ,qCAAqC;AAC9D,SAASC,KAAK,QAAQ,kCAAkC;AACxD,SAASC,SAAS,QAAQ,sCAAsC;AAChE,SAASC,cAAc,EAAEC,QAAQ,QAAQ,qCAAqC;AAC9E,SAASC,UAAU,QAAQ,uCAAuC;AAClE,SAASC,2BAA2B,QAAQ,iCAAiC;AAC7E,SAASC,6BAA6B,QAAQ,+DAA+D;AAC7G,SAASC,SAAS,EAAEC,4BAA4B,EAAEC,6BAA6B,QAAQ,gBAAgB;AACvG,SAASC,uBAAuB,QAAQ,8BAA8B;AACtE,SAASC,gBAAgB,EAAEC,sBAAsB,EAAEC,4BAA4B,QAAQ,qBAAqB;AAC5G,SAASC,YAAY,EAAEC,iBAAiB,QAAQ,aAAa;AAC7D,SAASC,kBAAkB,QAAQ,gDAAgD;AACnF,SAASC,eAAe,QAAQ,qDAAqD;AACrF,SAASC,qBAAqB,QAAQ,+DAA+D;AACrG,IAAIC,sBAAsB,GAAG,MAAMA,sBAAsB,SAASlC,UAAU,CAAC;EACzE,IAAImC,oBAAoBA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACC,qBAAqB;EAAE;EAChEC,WAAWA,CAACC,SAAS,EAAEC,mBAAmB,EAAEC,mBAAmB,EAAEC,UAAU,EAAEC,cAAc,EAAEC,sBAAsB,EAAEC,mBAAmB,EAAEC,kBAAkB,EAAEC,QAAQ,EAAEC,qBAAqB,EAAEC,eAAe,EAAEC,6BAA6B,EAAE;IAC3O,KAAK,CAAC,CAAC;IACP,IAAI,CAACX,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACC,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,sBAAsB,GAAGA,sBAAsB;IACpD,IAAI,CAACC,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACC,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,qBAAqB,GAAGA,qBAAqB;IAClD,IAAI,CAACC,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,6BAA6B,GAAGA,6BAA6B;IAClE,IAAI,CAACC,OAAO,GAAG,IAAI,CAACC,SAAS,CAAC,IAAI,CAACJ,qBAAqB,CAACK,cAAc,CAAC3B,uBAAuB,EAAE,IAAI,CAACa,SAAS,EAAE,IAAI,CAACE,mBAAmB,EAAE,IAAI,CAACE,cAAc,CAAC,CAAC;IAChK,IAAI,CAACW,SAAS,GAAG/C,eAAe,CAAC,IAAI,EAAE,KAAK,CAAC;IAC7C,IAAI,CAACgD,4BAA4B,GAAGjD,gBAAgB,CAAC,IAAI,CAAC;IAC1D;IACA,IAAI,CAACkD,2BAA2B,GAAGjD,eAAe,CAAC,IAAI,EAAEkD,SAAS,CAAC;IACnE,IAAI,CAACC,gBAAgB,GAAGvD,OAAO,CAAC,IAAI,EAAEwD,MAAM,IAAI,IAAI,CAACjB,UAAU,CAACkB,IAAI,CAACD,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI5C,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACtG,IAAI,CAACsB,qBAAqB,GAAG,KAAK;IAClC,IAAI,CAACwB,iCAAiC,GAAG,IAAIC,GAAG,CAAC,CAC7CC,qBAAqB,CAACC,IAAI,EAC1BD,qBAAqB,CAACE,IAAI,EAC1BF,qBAAqB,CAACG,UAAU,CACnC,CAAC;IACF,IAAI,CAACC,8BAA8B,GAAG/D,oBAAoB,CAAC;MACvDgE,KAAK,EAAE,IAAI;MACXC,wBAAwB,EAAEA,CAAA,MAAO;QAC7BC,yBAAyB,EAAE,KAAK;QAChCC,2BAA2B,EAAElD,2BAA2B,CAACmD;MAC7D,CAAC,CAAC;MACFC,YAAY,EAAEA,CAACC,GAAG,EAAEC,aAAa,KAAK;QAClC;QACA,IAAID,GAAG,CAACE,SAAS,CAAC,IAAI,CAACnC,mBAAmB,CAAC,IAAI,IAAI,CAACoB,iCAAiC,CAACgB,GAAG,CAAC,IAAI,CAACC,UAAU,CAACJ,GAAG,CAACK,MAAM,CAAC,CAAC,EAAE;UACpHJ,aAAa,CAACL,yBAAyB,GAAG,IAAI;QAClD,CAAC,MACI,IAAII,GAAG,CAACE,SAAS,CAAC,IAAI,CAACrB,4BAA4B,CAAC,EAAE;UACvDoB,aAAa,CAACJ,2BAA2B,GAAGlD,2BAA2B,CAAC2D,QAAQ;QACpF;QACA,OAAO,IAAI;MACf;IACJ,CAAC,EAAE,CAACrB,MAAM,EAAEgB,aAAa,KAAK;MAC1B,IAAI,CAACpB,4BAA4B,CAACK,IAAI,CAACD,MAAM,CAAC;MAC9C,MAAMsB,YAAY,GAAI,IAAI,CAAClC,QAAQ,CAACa,IAAI,CAACD,MAAM,CAAC,IAAI,IAAI,CAACnB,mBAAmB,CAACoB,IAAI,CAACD,MAAM,CAAC,IAAK,IAAI,CAACL,SAAS,CAACM,IAAI,CAACD,MAAM,CAAC;MACzH,IAAI,CAACsB,YAAY,EAAE;QACf,IAAI,CAAC9B,OAAO,CAAC+B,YAAY,CAAC,CAAC;QAC3B,OAAOzB,SAAS;MACpB;MACA,IAAI,CAAChB,mBAAmB,CAACmB,IAAI,CAACD,MAAM,CAAC,CAAC,CAAC;MACvC,MAAMwB,8BAA8B,GAAG,IAAI,CAAChC,OAAO,CAACgC,8BAA8B,CAACC,GAAG,CAAC,CAAC;MACxF,MAAMC,WAAW,GAAG,IAAI,CAAC7C,mBAAmB,CAACoB,IAAI,CAACD,MAAM,CAAC;MACzD,IAAIwB,8BAA8B,IAAI,CAACE,WAAW,EAAE;QAChD,MAAMC,iBAAiB,GAAG,IAAI,CAACnC,OAAO,CAACmC,iBAAiB,CAACF,GAAG,CAAC,CAAC;QAC9D1E,WAAW,CAAC6E,EAAE,IAAI;UACd;UACA,IAAI,CAACD,iBAAiB,IAAIH,8BAA8B,CAACK,OAAO,CAACC,SAAS,GAAGH,iBAAiB,CAACE,OAAO,CAACC,SAAS,EAAE;YAC9G,IAAI,CAACtC,OAAO,CAACmC,iBAAiB,CAACI,GAAG,CAACP,8BAA8B,CAACQ,KAAK,CAAC,CAAC,EAAEJ,EAAE,CAAC;UAClF;UACA,IAAI,CAACpC,OAAO,CAACyC,mCAAmC,CAACL,EAAE,CAAC;QACxD,CAAC,CAAC;MACN;MACA,MAAMM,cAAc,GAAG,IAAI,CAACnC,gBAAgB,CAACE,IAAI,CAACD,MAAM,CAAC;MACzD,MAAMmC,OAAO,GAAG;QACZC,WAAW,EAAEpB,aAAa,CAACJ,2BAA2B;QACtDyB,sBAAsB,EAAEX,WAAW,EAAEY,wBAAwB,CAAC;MAClE,CAAC;MACD,MAAMC,uBAAuB,GAAG,IAAI,CAACC,wBAAwB,CAACf,GAAG,CAAC,CAAC;MACnE,MAAMgB,cAAc,GAAGzB,aAAa,CAACL,yBAAyB,IAAI4B,uBAAuB,EAAEG,aAAa,GAClGH,uBAAuB,GAAGzC,SAAS;MACzC,OAAO,IAAI,CAACN,OAAO,CAACmD,KAAK,CAACT,cAAc,EAAEC,OAAO,EAAEM,cAAc,CAAC;IACtE,CAAC,CAAC;IACF,IAAI,CAACG,8BAA8B,GAAGlG,WAAW,CAAC;MAAE+D,KAAK,EAAE,IAAI;MAAEoC,QAAQ,EAAE3G,WAAW,CAAC;IAAE,CAAC,EAAE8D,MAAM,IAAI;MAClG,MAAM/E,CAAC,GAAG,IAAI,CAACuE,OAAO,CAACmC,iBAAiB,CAAC1B,IAAI,CAACD,MAAM,CAAC;MACrD,IAAI,CAAC/E,CAAC,EAAE;QACJ,OAAO,EAAE;MACb;MACA,MAAMiH,cAAc,GAAG,IAAI,CAACnC,gBAAgB,CAACE,IAAI,CAACD,MAAM,CAAC;MACzD,MAAM8C,mBAAmB,GAAG7H,CAAC,CAAC0G,iBAAiB,CAACoB,MAAM,CAAC9H,CAAC,IAAIA,CAAC,CAAC+H,SAAS,CAAC,IAAI,CAACpE,SAAS,EAAEsD,cAAc,EAAElC,MAAM,CAAC,CAAC;MAChH,OAAO8C,mBAAmB;IAC9B,CAAC,CAAC;IACF,IAAI,CAACG,6BAA6B,GAAGzG,OAAO,CAAC,IAAI,EAAGwD,MAAM,IAAK;MAC3D,MAAMkD,0BAA0B,GAAG,IAAI,CAACrD,2BAA2B,CAACI,IAAI,CAACD,MAAM,CAAC;MAChF,MAAM8C,mBAAmB,GAAG,IAAI,CAACF,8BAA8B,CAAC3C,IAAI,CAACD,MAAM,CAAC;MAC5E,MAAMmD,GAAG,GAAG,IAAI,CAACtD,2BAA2B,KAAKC,SAAS,GAAG,CAAC,CAAC,GACzDgD,mBAAmB,CAACM,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACC,UAAU,KAAKJ,0BAA0B,CAAC;MACrF,IAAIC,GAAG,KAAK,CAAC,CAAC,EAAE;QACZ;QACA,IAAI,CAACtD,2BAA2B,CAACkC,GAAG,CAACjC,SAAS,EAAEA,SAAS,CAAC;QAC1D,OAAO,CAAC;MACZ;MACA,OAAOqD,GAAG;IACd,CAAC,CAAC;IACF,IAAI,CAACX,wBAAwB,GAAGhG,OAAO,CAAC,IAAI,EAAGwD,MAAM,IAAK;MACtD,MAAM8C,mBAAmB,GAAG,IAAI,CAACF,8BAA8B,CAAC3C,IAAI,CAACD,MAAM,CAAC;MAC5E,MAAMmD,GAAG,GAAG,IAAI,CAACF,6BAA6B,CAAChD,IAAI,CAACD,MAAM,CAAC;MAC3D,OAAO8C,mBAAmB,CAACK,GAAG,CAAC;IACnC,CAAC,CAAC;IACF,IAAI,CAACI,cAAc,GAAG7G,WAAW,CAAC;MAAE+D,KAAK,EAAE,IAAI;MAAEoC,QAAQ,EAAE3G,WAAW,CAAC;IAAE,CAAC,EAAEd,CAAC,IAAI,IAAI,CAACoH,wBAAwB,CAACvC,IAAI,CAAC7E,CAAC,CAAC,EAAEoI,gBAAgB,CAACC,MAAM,CAAC9B,iBAAiB,CAAC+B,QAAQ,IAAI,EAAE,CAAC;IACjL,IAAI,CAACC,eAAe,GAAG,IAAI,CAACnE,OAAO,CAACmC,iBAAiB,CAACiC,GAAG,CAAC,IAAI,EAAEP,CAAC,IAAIA,CAAC,EAAExB,OAAO,CAACM,OAAO,CAACC,WAAW,CAAC;IACpG,IAAI,CAACyB,sBAAsB,GAAGrH,OAAO,CAAC,IAAI,EAAEwD,MAAM,IAAI;MAClD,IAAI,IAAI,CAAC2D,eAAe,CAAC1D,IAAI,CAACD,MAAM,CAAC,KAAKtC,2BAA2B,CAAC2D,QAAQ,EAAE;QAC5E,OAAO,IAAI,CAACuB,8BAA8B,CAAC3C,IAAI,CAACD,MAAM,CAAC,CAAC7E,MAAM;MAClE,CAAC,MACI;QACD,OAAO2E,SAAS;MACpB;IACJ,CAAC,CAAC;IACF,IAAI,CAACgE,KAAK,GAAGpH,WAAW,CAAC;MACrB+D,KAAK,EAAE,IAAI;MACXoC,QAAQ,EAAEA,CAACkB,CAAC,EAAEC,CAAC,KAAK;QAChB,IAAI,CAACD,CAAC,IAAI,CAACC,CAAC,EAAE;UACV,OAAOD,CAAC,KAAKC,CAAC;QAClB;QACA,OAAOlG,6BAA6B,CAACiG,CAAC,CAACE,UAAU,EAAED,CAAC,CAACC,UAAU,CAAC,IACzDF,CAAC,CAACP,gBAAgB,KAAKQ,CAAC,CAACR,gBAAgB,IACzCO,CAAC,CAACrC,WAAW,KAAKsC,CAAC,CAACtC,WAAW;MAC1C;IACJ,CAAC,EAAG1B,MAAM,IAAK;MACX,MAAMkE,KAAK,GAAG,IAAI,CAACtF,SAAS;MAC5B,MAAM8C,WAAW,GAAG,IAAI,CAAC7C,mBAAmB,CAACoB,IAAI,CAACD,MAAM,CAAC;MACzD,IAAI0B,WAAW,EAAE;QACb,MAAMyC,qBAAqB,GAAGjG,4BAA4B,CAACwD,WAAW,CAAC0C,gBAAgB,CAAC,CAAC,EAAEF,KAAK,CAAC;QACjG,MAAMG,YAAY,GAAG,IAAI,CAACC,oBAAoB,CAACH,qBAAqB,EAAEnE,MAAM,CAAC;QAC7E,MAAMuE,0BAA0B,GAAG,IAAI,CAACtF,sBAAsB,CAACgB,IAAI,CAACD,MAAM,CAAC;QAC3E,IAAI,CAACuE,0BAA0B,IAAI,CAACF,YAAY,EAAE;UAC9C,OAAOvE,SAAS;QACpB;QACA,MAAM0E,QAAQ,GAAGH,YAAY,EAAEI,IAAI,IAAIN,qBAAqB;QAC5D,MAAMO,qBAAqB,GAAGL,YAAY,GAAGA,YAAY,CAACI,IAAI,CAACE,IAAI,CAACxJ,MAAM,GAAGgJ,qBAAqB,CAACQ,IAAI,CAACxJ,MAAM,GAAG,CAAC;QAClH,MAAMyJ,IAAI,GAAG,IAAI,CAAC1F,mBAAmB,CAACe,IAAI,CAACD,MAAM,CAAC;QAClD,MAAM6E,SAAS,GAAG,IAAI,CAAC9F,UAAU,CAACkB,IAAI,CAACD,MAAM,CAAC;QAC9C,MAAM8E,KAAK,GAAG,CAACN,QAAQ,EAAE,GAAGO,iBAAiB,CAAC,IAAI,CAACnG,SAAS,EAAEiG,SAAS,EAAEL,QAAQ,CAAC,CAAC;QACnF,MAAMP,UAAU,GAAGa,KAAK,CACnBlB,GAAG,CAAC,CAACa,IAAI,EAAEtB,GAAG,KAAKnF,gBAAgB,CAACyG,IAAI,EAAEP,KAAK,EAAEU,IAAI,EAAEC,SAAS,CAAC1B,GAAG,CAAC,EAAEuB,qBAAqB,CAAC,CAAC,CAC9F3B,MAAM,CAAC7F,SAAS,CAAC;QACtB,MAAM8H,gBAAgB,GAAGf,UAAU,CAAC,CAAC,CAAC,IAAI,IAAIrG,SAAS,CAAC4G,QAAQ,CAACS,KAAK,CAACC,aAAa,EAAE,EAAE,CAAC;QACzF,OAAO;UAAEJ,KAAK;UAAEE,gBAAgB;UAAEf,UAAU;UAAET,gBAAgB,EAAEa,YAAY,EAAEc,UAAU;UAAEzD;QAAY,CAAC;MAC3G,CAAC,MACI;QACD,IAAI,CAAC,IAAI,CAAC/B,SAAS,CAACM,IAAI,CAACD,MAAM,CAAC,EAAE;UAC9B,OAAOF,SAAS;QACpB;QACA,MAAM0D,gBAAgB,GAAG,IAAI,CAAChB,wBAAwB,CAACvC,IAAI,CAACD,MAAM,CAAC;QACnE,IAAI,CAACwD,gBAAgB,EAAE;UACnB,OAAO1D,SAAS;QACpB;QACA,MAAMsF,WAAW,GAAG5B,gBAAgB,CAACY,gBAAgB,CAACpE,MAAM,CAAC;QAC7D,MAAM4E,IAAI,GAAG,IAAI,CAACzF,kBAAkB,CAACc,IAAI,CAACD,MAAM,CAAC;QACjD,MAAM6E,SAAS,GAAG,IAAI,CAAC9F,UAAU,CAACkB,IAAI,CAACD,MAAM,CAAC;QAC9C,MAAM8E,KAAK,GAAG,CAACM,WAAW,EAAE,GAAGL,iBAAiB,CAAC,IAAI,CAACnG,SAAS,EAAEiG,SAAS,EAAEO,WAAW,CAAC,CAAC;QACzF,MAAMnB,UAAU,GAAGa,KAAK,CACnBlB,GAAG,CAAC,CAACa,IAAI,EAAEtB,GAAG,KAAKnF,gBAAgB,CAACyG,IAAI,EAAEP,KAAK,EAAEU,IAAI,EAAEC,SAAS,CAAC1B,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAC1EJ,MAAM,CAAC7F,SAAS,CAAC;QACtB,IAAI,CAAC+G,UAAU,CAAC,CAAC,CAAC,EAAE;UAChB,OAAOnE,SAAS;QACpB;QACA,OAAO;UAAEgF,KAAK;UAAEE,gBAAgB,EAAEf,UAAU,CAAC,CAAC,CAAC;UAAEA,UAAU;UAAET,gBAAgB;UAAE9B,WAAW,EAAE5B;QAAU,CAAC;MAC3G;IACJ,CAAC,CAAC;IACF,IAAI,CAACmE,UAAU,GAAGvH,WAAW,CAAC;MAAE+D,KAAK,EAAE,IAAI;MAAEoC,QAAQ,EAAE/E;IAA8B,CAAC,EAAEkC,MAAM,IAAI;MAC9F,MAAMqD,CAAC,GAAG,IAAI,CAACS,KAAK,CAAC7D,IAAI,CAACD,MAAM,CAAC;MACjC,IAAI,CAACqD,CAAC,EAAE;QACJ,OAAOvD,SAAS;MACpB;MACA,OAAOuD,CAAC,CAACY,UAAU;IACvB,CAAC,CAAC;IACF,IAAI,CAACe,gBAAgB,GAAGtI,WAAW,CAAC;MAAE+D,KAAK,EAAE,IAAI;MAAEoC,QAAQ,EAAEhF;IAA6B,CAAC,EAAEmC,MAAM,IAAI;MACnG,MAAMqD,CAAC,GAAG,IAAI,CAACS,KAAK,CAAC7D,IAAI,CAACD,MAAM,CAAC;MACjC,IAAI,CAACqD,CAAC,EAAE;QACJ,OAAOvD,SAAS;MACpB;MACA,OAAOuD,CAAC,EAAE2B,gBAAgB;IAC9B,CAAC,CAAC;IACF,IAAI,CAACvF,SAAS,CAAC5C,6BAA6B,CAAC,IAAI,CAAC2D,8BAA8B,CAAC,CAAC;IAClF,IAAI6E,QAAQ,GAAGvF,SAAS;IACxB,IAAI,CAACL,SAAS,CAAClD,OAAO,CAACyD,MAAM,IAAI;MAC7B;MACA,MAAMsF,IAAI,GAAG,IAAI,CAACxB,KAAK,CAAC7D,IAAI,CAACD,MAAM,CAAC;MACpC,MAAMmF,UAAU,GAAGG,IAAI,EAAE9B,gBAAgB;MACzC,IAAI2B,UAAU,EAAE7B,UAAU,KAAK+B,QAAQ,EAAE/B,UAAU,EAAE;QACjD+B,QAAQ,GAAGF,UAAU;QACrB,IAAIA,UAAU,EAAE;UACZ,MAAMzJ,CAAC,GAAGyJ,UAAU,CAAC3B,gBAAgB;UACrC,MAAM+B,GAAG,GAAG7J,CAAC,CAAC+H,MAAM;UACpB8B,GAAG,CAACC,QAAQ,CAACC,iBAAiB,GAAGF,GAAG,CAAC5D,iBAAiB,EAAEjG,CAAC,CAACgK,sBAAsB,EAAEhK,CAAC,CAACiK,UAAU,CAAC;QACnG;MACJ;IACJ,CAAC,CAAC,CAAC;EACP;EACAxE,UAAUA,CAACyE,CAAC,EAAE;IACV,IAAIA,CAAC,EAAEC,SAAS,EAAE;MACd,OAAOzF,qBAAqB,CAACE,IAAI;IACrC;IACA,IAAIsF,CAAC,EAAEE,SAAS,EAAE;MACd,OAAO1F,qBAAqB,CAACC,IAAI;IACrC;IACA,IAAI,IAAI,CAAC5B,oBAAoB,EAAE;MAC3B,OAAO2B,qBAAqB,CAACG,UAAU;IAC3C;IACA,OAAOH,qBAAqB,CAAC2F,KAAK;EACtC;EACMC,OAAOA,CAACpE,EAAE,EAAE;IAAA,IAAAqE,KAAA;IAAA,OAAAC,iBAAA;MACdD,KAAI,CAACtG,SAAS,CAACoC,GAAG,CAAC,IAAI,EAAEH,EAAE,CAAC;MAC5B,MAAMqE,KAAI,CAACzF,8BAA8B,CAACiB,GAAG,CAAC,CAAC;IAAC;EACpD;EACM0E,iBAAiBA,CAACvE,EAAE,EAAE;IAAA,IAAAwE,MAAA;IAAA,OAAAF,iBAAA;MACxBpJ,cAAc,CAAC8E,EAAE,EAAEA,EAAE,IAAI;QACrBwE,MAAI,CAACzG,SAAS,CAACoC,GAAG,CAAC,IAAI,EAAEH,EAAE,CAAC;QAC5BwE,MAAI,CAACxG,4BAA4B,CAACoG,OAAO,CAACpE,EAAE,CAAC;MACjD,CAAC,CAAC;MACF,MAAMwE,MAAI,CAAC5F,8BAA8B,CAACiB,GAAG,CAAC,CAAC;IAAC;EACpD;EACA4E,IAAIA,CAACzE,EAAE,EAAE;IACL9E,cAAc,CAAC8E,EAAE,EAAEA,EAAE,IAAI;MACrB,IAAI,CAACjC,SAAS,CAACoC,GAAG,CAAC,KAAK,EAAEH,EAAE,CAAC;MAC7B,IAAI,CAACpC,OAAO,CAAC8G,KAAK,CAAC1E,EAAE,CAAC;IAC1B,CAAC,CAAC;EACN;EACA0C,oBAAoBA,CAACiC,iBAAiB,EAAEvG,MAAM,EAAE;IAC5C,MAAMkE,KAAK,GAAG,IAAI,CAACtF,SAAS;IAC5B,MAAM4C,8BAA8B,GAAG,IAAI,CAAChC,OAAO,CAACgC,8BAA8B,CAACvB,IAAI,CAACD,MAAM,CAAC;IAC/F,MAAMwG,0BAA0B,GAAGhF,8BAA8B,GAC3DA,8BAA8B,CAACG,iBAAiB,GAChD,CAAC,IAAI,CAACa,wBAAwB,CAACvC,IAAI,CAACD,MAAM,CAAC,CAAC,CAAC+C,MAAM,CAAC7F,SAAS,CAAC;IACpE,MAAMuJ,mBAAmB,GAAGxK,YAAY,CAACuK,0BAA0B,EAAErB,UAAU,IAAI;MAC/E,IAAI/J,CAAC,GAAG+J,UAAU,CAACf,gBAAgB,CAACpE,MAAM,CAAC;MAC3C5E,CAAC,GAAG8C,4BAA4B,CAAC9C,CAAC,EAAE8I,KAAK,EAAE7G,KAAK,CAACqJ,aAAa,CAACtL,CAAC,CAAC6J,KAAK,CAAC0B,gBAAgB,CAAC,CAAC,EAAEJ,iBAAiB,CAACtB,KAAK,CAAC2B,cAAc,CAAC,CAAC,CAAC,CAAC;MACrI,OAAO3I,sBAAsB,CAAC7C,CAAC,EAAEmL,iBAAiB,CAAC,GAAG;QAAEpB,UAAU;QAAEV,IAAI,EAAErJ;MAAE,CAAC,GAAG0E,SAAS;IAC7F,CAAC,CAAC;IACF,OAAO2G,mBAAmB;EAC9B;EACMI,mCAAmCA,CAACC,KAAK,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAAb,iBAAA;MAC7C,MAAMa,MAAI,CAACZ,iBAAiB,CAAC,CAAC;MAC9B,MAAMa,WAAW,GAAGD,MAAI,CAACnE,8BAA8B,CAACnB,GAAG,CAAC,CAAC,IAAI,EAAE;MACnE,IAAIuF,WAAW,CAAC7L,MAAM,GAAG,CAAC,EAAE;QACxB,MAAM8L,MAAM,GAAG,CAACF,MAAI,CAAC9D,6BAA6B,CAACxB,GAAG,CAAC,CAAC,GAAGqF,KAAK,GAAGE,WAAW,CAAC7L,MAAM,IAAI6L,WAAW,CAAC7L,MAAM;QAC3G4L,MAAI,CAAClH,2BAA2B,CAACkC,GAAG,CAACiF,WAAW,CAACC,MAAM,CAAC,CAAC3D,UAAU,EAAExD,SAAS,CAAC;MACnF,CAAC,MACI;QACDiH,MAAI,CAAClH,2BAA2B,CAACkC,GAAG,CAACjC,SAAS,EAAEA,SAAS,CAAC;MAC9D;IAAC;EACL;EACMoH,IAAIA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAjB,iBAAA;MACT,MAAMiB,MAAI,CAACN,mCAAmC,CAAC,CAAC,CAAC;IAAC;EACtD;EACMO,QAAQA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAnB,iBAAA;MACb,MAAMmB,MAAI,CAACR,mCAAmC,CAAC,CAAC,CAAC,CAAC;IAAC;EACvD;EACMS,MAAMA,CAACC,MAAM,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAAtB,iBAAA;MACjB,IAAIqB,MAAM,CAACE,QAAQ,CAAC,CAAC,KAAKD,MAAI,CAAC5I,SAAS,EAAE;QACtC,MAAM,IAAIzC,kBAAkB,CAAC,CAAC;MAClC;MACA,MAAM2H,KAAK,GAAG0D,MAAI,CAAC1D,KAAK,CAACrC,GAAG,CAAC,CAAC;MAC9B,IAAI,CAACqC,KAAK,IAAIA,KAAK,CAACkB,gBAAgB,CAAC0C,OAAO,CAAC,CAAC,IAAI,CAAC5D,KAAK,CAACN,gBAAgB,EAAE;QACvE;MACJ;MACA,MAAM2B,UAAU,GAAGrB,KAAK,CAACN,gBAAgB,CAACmE,kBAAkB,CAAC7H,SAAS,CAAC;MACvE,IAAIqF,UAAU,CAACyC,OAAO,EAAE;QACpB;QACAzC,UAAU,CAAC1B,MAAM,CAACoE,MAAM,CAAC,CAAC;MAC9B;MACAN,MAAM,CAACO,YAAY,CAAC,CAAC;MACrB,IAAI3C,UAAU,CAAC4C,WAAW,EAAE;QACxBR,MAAM,CAACS,YAAY,CAAC,yBAAyB,EAAE,CAC3C7K,aAAa,CAAC8K,OAAO,CAAC9C,UAAU,CAACF,KAAK,EAAE,EAAE,CAAC,EAC3C,GAAGE,UAAU,CAAC+C,mBAAmB,CACpC,CAAC;QACFX,MAAM,CAACY,WAAW,CAAChD,UAAU,CAAC4C,WAAW,CAAC9C,KAAK,CAAC0B,gBAAgB,CAAC,CAAC,EAAE,wBAAwB,CAAC;QAC7FtI,kBAAkB,CAACoD,GAAG,CAAC8F,MAAM,CAAC,EAAEa,MAAM,CAACjD,UAAU,CAAC4C,WAAW,CAACM,OAAO,EAAE;UAAEC,cAAc,EAAE;QAAM,CAAC,CAAC;MACrG,CAAC,MACI;QACD,MAAMxD,KAAK,GAAGhB,KAAK,CAACgB,KAAK;QACzB,MAAMyD,UAAU,GAAGC,4BAA4B,CAAC1D,KAAK,CAAC,CAAClB,GAAG,CAAC6E,CAAC,IAAInL,SAAS,CAACoJ,aAAa,CAAC+B,CAAC,CAAC,CAAC;QAC3FlB,MAAM,CAACS,YAAY,CAAC,yBAAyB,EAAE,CAC3C,GAAGlD,KAAK,CAAClB,GAAG,CAACa,IAAI,IAAItH,aAAa,CAAC8K,OAAO,CAACxD,IAAI,CAACQ,KAAK,EAAER,IAAI,CAACE,IAAI,CAAC,CAAC,EAClE,GAAGQ,UAAU,CAAC+C,mBAAmB,CACpC,CAAC;QACFX,MAAM,CAACmB,aAAa,CAACH,UAAU,EAAE,wBAAwB,CAAC;MAC9D;MACA;MACAf,MAAI,CAACnB,IAAI,CAAC,CAAC;MACX,IAAIlB,UAAU,CAACyC,OAAO,EAAE;QACpB,MAAMJ,MAAI,CAAClI,eAAe,CACrBqJ,cAAc,CAACxD,UAAU,CAACyC,OAAO,CAACgB,EAAE,EAAE,IAAIzD,UAAU,CAACyC,OAAO,CAAC1M,SAAS,IAAI,EAAE,CAAC,CAAC,CAC9E2N,IAAI,CAAC/I,SAAS,EAAEzD,yBAAyB,CAAC;QAC/C8I,UAAU,CAAC1B,MAAM,CAACqF,SAAS,CAAC,CAAC;MACjC;IAAC;EACL;EACMC,cAAcA,CAACxB,MAAM,EAAE;IAAA,IAAAyB,MAAA;IAAA,OAAA9C,iBAAA;MACzB,MAAM8C,MAAI,CAACC,WAAW,CAAC1B,MAAM,EAAE,CAAC2B,GAAG,EAAEvE,IAAI,KAAK;QAC1C,MAAMwE,MAAM,GAAGH,MAAI,CAACpK,SAAS,CAACwK,uBAAuB,CAACF,GAAG,CAACG,UAAU,EAAEH,GAAG,CAACI,MAAM,CAAC;QACjF,MAAMC,MAAM,GAAGP,MAAI,CAACzJ,6BAA6B,CAACiK,wBAAwB,CAACL,MAAM,CAAC;QAClF,MAAMM,UAAU,GAAG,IAAIC,MAAM,CAACH,MAAM,CAACI,cAAc,CAAClG,MAAM,EAAE8F,MAAM,CAACI,cAAc,CAACC,KAAK,CAAC3B,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;QACzG,MAAM4B,EAAE,GAAGlF,IAAI,CAACmF,KAAK,CAACL,UAAU,CAAC;QACjC,IAAIM,yBAAyB,GAAG,CAAC;QACjC,IAAIF,EAAE,IAAIA,EAAE,CAACG,KAAK,KAAKlK,SAAS,EAAE;UAC9B,IAAI+J,EAAE,CAACG,KAAK,KAAK,CAAC,EAAE;YAChBD,yBAAyB,GAAGF,EAAE,CAAC,CAAC,CAAC,CAAC1O,MAAM;UAC5C,CAAC,MACI;YACD4O,yBAAyB,GAAGF,EAAE,CAACG,KAAK;UACxC;QACJ,CAAC,MACI;UACDD,yBAAyB,GAAGpF,IAAI,CAACxJ,MAAM;QAC3C;QACA,MAAM8O,QAAQ,GAAG,MAAM;QACvB,MAAMC,EAAE,GAAGD,QAAQ,CAACE,IAAI,CAACxF,IAAI,CAAC;QAC9B,IAAIuF,EAAE,IAAIA,EAAE,CAACF,KAAK,KAAKlK,SAAS,EAAE;UAC9B,IAAIoK,EAAE,CAACF,KAAK,GAAGE,EAAE,CAAC,CAAC,CAAC,CAAC/O,MAAM,GAAG4O,yBAAyB,EAAE;YACrDA,yBAAyB,GAAGG,EAAE,CAACF,KAAK,GAAGE,EAAE,CAAC,CAAC,CAAC,CAAC/O,MAAM;UACvD;QACJ;QACA,OAAO4O,yBAAyB;MACpC,CAAC,EAAE,CAAC,CAAC,mCAAmC,CAAC;IAAC;EAC9C;EACMK,cAAcA,CAAC7C,MAAM,EAAE;IAAA,IAAA8C,MAAA;IAAA,OAAAnE,iBAAA;MACzB,MAAMmE,MAAI,CAACpB,WAAW,CAAC1B,MAAM,EAAE,CAAC2B,GAAG,EAAEvE,IAAI,KAAK;QAC1C,MAAM2F,CAAC,GAAG3F,IAAI,CAACmF,KAAK,CAAC,IAAI,CAAC;QAC1B,IAAIQ,CAAC,IAAIA,CAAC,CAACN,KAAK,KAAKlK,SAAS,EAAE;UAC5B,OAAOwK,CAAC,CAACN,KAAK,GAAG,CAAC;QACtB;QACA,OAAOrF,IAAI,CAACxJ,MAAM;MACtB,CAAC,EAAE,CAAC,CAAC,mCAAmC,CAAC;IAAC;EAC9C;EACM8N,WAAWA,CAAC1B,MAAM,EAAEgD,mBAAmB,EAAEC,IAAI,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAAvE,iBAAA;MACjD,IAAIqB,MAAM,CAACE,QAAQ,CAAC,CAAC,KAAKgD,MAAI,CAAC7L,SAAS,EAAE;QACtC,MAAM,IAAIzC,kBAAkB,CAAC,CAAC;MAClC;MACA,MAAM2H,KAAK,GAAG2G,MAAI,CAAC3G,KAAK,CAACrC,GAAG,CAAC,CAAC;MAC9B,IAAI,CAACqC,KAAK,IAAIA,KAAK,CAACkB,gBAAgB,CAAC0C,OAAO,CAAC,CAAC,IAAI,CAAC5D,KAAK,CAACN,gBAAgB,EAAE;QACvE;MACJ;MACA,MAAMkH,SAAS,GAAG5G,KAAK,CAACkB,gBAAgB;MACxC,MAAMG,UAAU,GAAGrB,KAAK,CAACN,gBAAgB,CAACmE,kBAAkB,CAAC7H,SAAS,CAAC;MACvE,IAAIqF,UAAU,CAAC4C,WAAW,IAAI5C,UAAU,CAACwF,UAAU,KAAKxF,UAAU,CAACQ,UAAU,EAAE;QAC3E;QACA,MAAM8E,MAAI,CAACnD,MAAM,CAACC,MAAM,CAAC;QACzB;MACJ;MACA,MAAMqD,SAAS,GAAGF,SAAS,CAACG,KAAK,CAAC,CAAC,CAAC;MACpC,MAAMC,YAAY,GAAG,IAAI1N,QAAQ,CAACsN,SAAS,CAACrB,UAAU,EAAEuB,SAAS,CAACtB,MAAM,CAAC;MACzE,MAAMyB,YAAY,GAAGH,SAAS,CAACjG,IAAI;MACnC,MAAMoF,yBAAyB,GAAGQ,mBAAmB,CAACO,YAAY,EAAEC,YAAY,CAAC;MACjF,IAAIhB,yBAAyB,KAAKgB,YAAY,CAAC5P,MAAM,IAAIuP,SAAS,CAACG,KAAK,CAAC1P,MAAM,KAAK,CAAC,EAAE;QACnFsP,MAAI,CAACnD,MAAM,CAACC,MAAM,CAAC;QACnB;MACJ;MACA,MAAMyD,mBAAmB,GAAGD,YAAY,CAACE,SAAS,CAAC,CAAC,EAAElB,yBAAyB,CAAC;MAChF,MAAMlF,SAAS,GAAG4F,MAAI,CAAC1L,UAAU,CAAC0C,GAAG,CAAC,CAAC;MACvC,MAAMS,cAAc,GAAG2C,SAAS,CAAC,CAAC,CAAC;MACnC;MACAM,UAAU,CAAC1B,MAAM,CAACoE,MAAM,CAAC,CAAC;MAC1B,IAAI;QACA4C,MAAI,CAAC/L,qBAAqB,GAAG,IAAI;QACjC,IAAI;UACA6I,MAAM,CAACO,YAAY,CAAC,CAAC;UACrB,MAAMoD,YAAY,GAAG7N,KAAK,CAACqJ,aAAa,CAACxE,cAAc,EAAE4I,YAAY,CAAC;UACtE,MAAMK,OAAO,GAAG5D,MAAM,CAACE,QAAQ,CAAC,CAAC,CAAC2D,eAAe,CAACF,YAAY,CAAC,GAAGF,mBAAmB;UACrF,MAAMK,WAAW,GAAG,IAAI9N,cAAc,CAAC2N,YAAY,EAAEC,OAAO,CAAC;UAC7D,MAAMrG,KAAK,GAAG,CAACuG,WAAW,EAAE,GAAGtG,iBAAiB,CAAC0F,MAAI,CAAC7L,SAAS,EAAEiG,SAAS,EAAEwG,WAAW,CAAC,CAAC;UACzF,MAAM9C,UAAU,GAAGC,4BAA4B,CAAC1D,KAAK,CAAC,CAAClB,GAAG,CAAC6E,CAAC,IAAInL,SAAS,CAACoJ,aAAa,CAAC+B,CAAC,CAAC,CAAC;UAC3FlB,MAAM,CAACS,YAAY,CAAC,yBAAyB,EAAElD,KAAK,CAAClB,GAAG,CAACa,IAAI,IAAItH,aAAa,CAAC8K,OAAO,CAACxD,IAAI,CAACQ,KAAK,EAAER,IAAI,CAACE,IAAI,CAAC,CAAC,CAAC;UAC/G4C,MAAM,CAACmB,aAAa,CAACH,UAAU,EAAE,+BAA+B,CAAC;UACjEhB,MAAM,CAAC+D,uCAAuC,CAAC/D,MAAM,CAACgE,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,0BAA0B,CAAC;QACtG,CAAC,SACO;UACJd,MAAI,CAAC/L,qBAAqB,GAAG,KAAK;QACtC;QACA,IAAIyG,UAAU,CAAC1B,MAAM,CAAC+B,QAAQ,CAACgG,mBAAmB,EAAE;UAChD,MAAMC,aAAa,GAAGpO,KAAK,CAACqJ,aAAa,CAACvB,UAAU,CAACF,KAAK,CAAC0B,gBAAgB,CAAC,CAAC,EAAElJ,UAAU,CAACiO,MAAM,CAACV,mBAAmB,CAAC,CAACW,aAAa,CAACb,YAAY,CAAC,CAAC;UAClJ;UACA,MAAMnG,IAAI,GAAG4C,MAAM,CAACE,QAAQ,CAAC,CAAC,CAAC2D,eAAe,CAACK,aAAa,EAAE,CAAC,CAAC,4BAA4B,CAAC;UAC7FtG,UAAU,CAAC1B,MAAM,CAAC+B,QAAQ,CAACgG,mBAAmB,CAACrG,UAAU,CAAC1B,MAAM,CAAC9B,iBAAiB,EAAEwD,UAAU,CAACO,sBAAsB,EAAEf,IAAI,CAACxJ,MAAM,EAAE;YAAEqP;UAAM,CAAC,CAAC;QAClJ;MACJ,CAAC,SACO;QACJrF,UAAU,CAAC1B,MAAM,CAACqF,SAAS,CAAC,CAAC;MACjC;IAAC;EACL;EACA8C,qBAAqBA,CAACtG,IAAI,EAAE;IACxB,MAAMuG,QAAQ,GAAG3N,4BAA4B,CAACoH,IAAI,CAAClB,gBAAgB,CAAC,CAAC,EAAE,IAAI,CAACxF,SAAS,CAAC;IACtF,MAAM6H,mBAAmB,GAAG,IAAI,CAACnC,oBAAoB,CAACuH,QAAQ,EAAE/L,SAAS,CAAC;IAC1E,IAAI,CAAC2G,mBAAmB,EAAE;MACtB;IACJ;IACA,MAAMjD,gBAAgB,GAAGiD,mBAAmB,CAACtB,UAAU,CAAC3B,gBAAgB;IACxEA,gBAAgB,CAACC,MAAM,CAAC+B,QAAQ,CAACgG,mBAAmB,GAAGhI,gBAAgB,CAACC,MAAM,CAAC9B,iBAAiB,EAAE6B,gBAAgB,CAACkC,sBAAsB,EAAEmG,QAAQ,CAAClH,IAAI,CAACxJ,MAAM,EAAE;MAC7JqP,IAAI,EAAE,CAAC,CAAC;IACZ,CAAC,CAAC;EACN;AACJ,CAAC;AACDhM,sBAAsB,GAAG5D,UAAU,CAAC,CAChCgB,OAAO,CAAC,CAAC,EAAE2C,qBAAqB,CAAC,EACjC3C,OAAO,CAAC,EAAE,EAAE0C,eAAe,CAAC,EAC5B1C,OAAO,CAAC,EAAE,EAAE+B,6BAA6B,CAAC,CAC7C,EAAEa,sBAAsB,CAAC;AAC1B,SAASA,sBAAsB;AAC/B,OAAO,IAAI4B,qBAAqB,gBAC/B,UAAUA,qBAAqB,EAAE;EAC9BA,qBAAqB,CAACA,qBAAqB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EACjEA,qBAAqB,CAACA,qBAAqB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EACjEA,qBAAqB,CAACA,qBAAqB,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,YAAY;EAC7EA,qBAAqB,CAACA,qBAAqB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;EAAC,OAJ7DA,qBAAqB;AAKhC,CAAC,CAAEA,qBAAqB,IAA6B,CAAC,CAAE,CANxB;AAOhC,OAAO,SAAS2E,iBAAiBA,CAACnG,SAAS,EAAEiG,SAAS,EAAEwG,WAAW,EAAE;EACjE,IAAIxG,SAAS,CAAC1J,MAAM,KAAK,CAAC,EAAE;IACxB;IACA,OAAO,EAAE;EACb;EACA,MAAM2Q,eAAe,GAAGjH,SAAS,CAAC,CAAC,CAAC;EACpC,MAAMkH,kBAAkB,GAAGlH,SAAS,CAACmH,KAAK,CAAC,CAAC,CAAC;EAC7C,MAAMC,wBAAwB,GAAGZ,WAAW,CAACpG,KAAK,CAAC0B,gBAAgB,CAAC,CAAC;EACrE,MAAMuF,sBAAsB,GAAGb,WAAW,CAACpG,KAAK,CAAC2B,cAAc,CAAC,CAAC;EACjE,MAAMuF,8BAA8B,GAAGvN,SAAS,CAACwM,eAAe,CAAC/N,KAAK,CAACqJ,aAAa,CAACoF,eAAe,EAAEI,sBAAsB,CAAC,CAAC;EAC9H,MAAME,sBAAsB,GAAGhO,iBAAiB,CAAC0N,eAAe,EAAEG,wBAAwB,CAAC;EAC3F,IAAIG,sBAAsB,CAAC/C,UAAU,GAAG,CAAC,EAAE;IACvCjN,iBAAiB,CAAC,IAAID,kBAAkB,CAAC;AACjD,iCAAiC2P,eAAe,CAACO,QAAQ,CAAC,CAAC,QAAQJ,wBAAwB,CAACI,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;IAClG,OAAO,EAAE;EACb;EACA,MAAMC,iBAAiB,GAAGC,YAAY,CAAClB,WAAW,CAAC1G,IAAI,EAAEyH,sBAAsB,CAAC;EAChF,OAAOL,kBAAkB,CAACnI,GAAG,CAACsF,GAAG,IAAI;IACjC,MAAMsD,MAAM,GAAGrO,YAAY,CAACC,iBAAiB,CAAC8K,GAAG,EAAE+C,wBAAwB,CAAC,EAAEC,sBAAsB,CAAC;IACrG,MAAMO,wBAAwB,GAAG7N,SAAS,CAACwM,eAAe,CAAC/N,KAAK,CAACqJ,aAAa,CAACwC,GAAG,EAAEsD,MAAM,CAAC,CAAC;IAC5F,MAAME,CAAC,GAAG1P,kBAAkB,CAACmP,8BAA8B,EAAEM,wBAAwB,CAAC;IACtF,MAAMxH,KAAK,GAAG5H,KAAK,CAACqJ,aAAa,CAACwC,GAAG,EAAEA,GAAG,CAACpC,KAAK,CAAC,CAAC,EAAE4F,CAAC,CAAC,CAAC;IACvD,OAAO,IAAInP,cAAc,CAAC0H,KAAK,EAAEqH,iBAAiB,CAAC;EACvD,CAAC,CAAC;AACN;AACA,SAASC,YAAYA,CAAC5H,IAAI,EAAEuE,GAAG,EAAE;EAC7B,IAAIyD,OAAO,GAAG,EAAE;EAChB,MAAMC,KAAK,GAAG3P,2BAA2B,CAAC0H,IAAI,CAAC;EAC/C,KAAK,IAAIjJ,CAAC,GAAGwN,GAAG,CAACG,UAAU,GAAG,CAAC,EAAE3N,CAAC,GAAGkR,KAAK,CAACzR,MAAM,EAAEO,CAAC,EAAE,EAAE;IACpDiR,OAAO,IAAIC,KAAK,CAAClR,CAAC,CAAC,CAACuP,SAAS,CAACvP,CAAC,KAAKwN,GAAG,CAACG,UAAU,GAAG,CAAC,GAAGH,GAAG,CAACI,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;EAChF;EACA,OAAOqD,OAAO;AAClB;AACA,SAASnE,4BAA4BA,CAAC1D,KAAK,EAAE;EACzC,MAAM+H,QAAQ,GAAG7Q,WAAW,CAAC8Q,qBAAqB,CAAChI,KAAK,EAAE/I,SAAS,CAAC6J,CAAC,IAAIA,CAAC,CAACX,KAAK,EAAE5H,KAAK,CAAC0P,wBAAwB,CAAC,CAAC;EAClH,MAAMtI,IAAI,GAAG,IAAIjH,QAAQ,CAACqP,QAAQ,CAACG,KAAK,CAAClI,KAAK,CAAC,CAAC;EAChD,MAAMmI,eAAe,GAAGxI,IAAI,CAACyI,YAAY,CAAC,CAAC;EAC3C,MAAMC,SAAS,GAAGN,QAAQ,CAACO,OAAO,CAAC,CAAC,CAACJ,KAAK,CAACC,eAAe,CAAC;EAC3D,OAAOE,SAAS,CAACvJ,GAAG,CAACqB,KAAK,IAAIA,KAAK,CAAC2B,cAAc,CAAC,CAAC,CAAC;AACzD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}