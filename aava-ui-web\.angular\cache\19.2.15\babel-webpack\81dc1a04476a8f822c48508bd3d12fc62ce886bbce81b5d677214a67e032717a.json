{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as dom from '../../dom.js';\nimport { CaseSensitiveToggle, RegexToggle, WholeWordsToggle } from './findInputToggles.js';\nimport { HistoryInputBox } from '../inputbox/inputBox.js';\nimport { Widget } from '../widget.js';\nimport { Emitter } from '../../../common/event.js';\nimport './findInput.css';\nimport * as nls from '../../../../nls.js';\nimport { DisposableStore, MutableDisposable } from '../../../common/lifecycle.js';\nimport { createInstantHoverDelegate } from '../hover/hoverDelegateFactory.js';\nconst NLS_DEFAULT_LABEL = nls.localize('defaultLabel', \"input\");\nexport class FindInput extends Widget {\n  constructor(parent, contextViewProvider, options) {\n    super();\n    this.fixFocusOnOptionClickEnabled = true;\n    this.imeSessionInProgress = false;\n    this.additionalTogglesDisposables = this._register(new MutableDisposable());\n    this.additionalToggles = [];\n    this._onDidOptionChange = this._register(new Emitter());\n    this.onDidOptionChange = this._onDidOptionChange.event;\n    this._onKeyDown = this._register(new Emitter());\n    this.onKeyDown = this._onKeyDown.event;\n    this._onMouseDown = this._register(new Emitter());\n    this.onMouseDown = this._onMouseDown.event;\n    this._onInput = this._register(new Emitter());\n    this._onKeyUp = this._register(new Emitter());\n    this._onCaseSensitiveKeyDown = this._register(new Emitter());\n    this.onCaseSensitiveKeyDown = this._onCaseSensitiveKeyDown.event;\n    this._onRegexKeyDown = this._register(new Emitter());\n    this.onRegexKeyDown = this._onRegexKeyDown.event;\n    this._lastHighlightFindOptions = 0;\n    this.placeholder = options.placeholder || '';\n    this.validation = options.validation;\n    this.label = options.label || NLS_DEFAULT_LABEL;\n    this.showCommonFindToggles = !!options.showCommonFindToggles;\n    const appendCaseSensitiveLabel = options.appendCaseSensitiveLabel || '';\n    const appendWholeWordsLabel = options.appendWholeWordsLabel || '';\n    const appendRegexLabel = options.appendRegexLabel || '';\n    const history = options.history || [];\n    const flexibleHeight = !!options.flexibleHeight;\n    const flexibleWidth = !!options.flexibleWidth;\n    const flexibleMaxHeight = options.flexibleMaxHeight;\n    this.domNode = document.createElement('div');\n    this.domNode.classList.add('monaco-findInput');\n    this.inputBox = this._register(new HistoryInputBox(this.domNode, contextViewProvider, {\n      placeholder: this.placeholder || '',\n      ariaLabel: this.label || '',\n      validationOptions: {\n        validation: this.validation\n      },\n      history,\n      showHistoryHint: options.showHistoryHint,\n      flexibleHeight,\n      flexibleWidth,\n      flexibleMaxHeight,\n      inputBoxStyles: options.inputBoxStyles\n    }));\n    const hoverDelegate = this._register(createInstantHoverDelegate());\n    if (this.showCommonFindToggles) {\n      this.regex = this._register(new RegexToggle({\n        appendTitle: appendRegexLabel,\n        isChecked: false,\n        hoverDelegate,\n        ...options.toggleStyles\n      }));\n      this._register(this.regex.onChange(viaKeyboard => {\n        this._onDidOptionChange.fire(viaKeyboard);\n        if (!viaKeyboard && this.fixFocusOnOptionClickEnabled) {\n          this.inputBox.focus();\n        }\n        this.validate();\n      }));\n      this._register(this.regex.onKeyDown(e => {\n        this._onRegexKeyDown.fire(e);\n      }));\n      this.wholeWords = this._register(new WholeWordsToggle({\n        appendTitle: appendWholeWordsLabel,\n        isChecked: false,\n        hoverDelegate,\n        ...options.toggleStyles\n      }));\n      this._register(this.wholeWords.onChange(viaKeyboard => {\n        this._onDidOptionChange.fire(viaKeyboard);\n        if (!viaKeyboard && this.fixFocusOnOptionClickEnabled) {\n          this.inputBox.focus();\n        }\n        this.validate();\n      }));\n      this.caseSensitive = this._register(new CaseSensitiveToggle({\n        appendTitle: appendCaseSensitiveLabel,\n        isChecked: false,\n        hoverDelegate,\n        ...options.toggleStyles\n      }));\n      this._register(this.caseSensitive.onChange(viaKeyboard => {\n        this._onDidOptionChange.fire(viaKeyboard);\n        if (!viaKeyboard && this.fixFocusOnOptionClickEnabled) {\n          this.inputBox.focus();\n        }\n        this.validate();\n      }));\n      this._register(this.caseSensitive.onKeyDown(e => {\n        this._onCaseSensitiveKeyDown.fire(e);\n      }));\n      // Arrow-Key support to navigate between options\n      const indexes = [this.caseSensitive.domNode, this.wholeWords.domNode, this.regex.domNode];\n      this.onkeydown(this.domNode, event => {\n        if (event.equals(15 /* KeyCode.LeftArrow */) || event.equals(17 /* KeyCode.RightArrow */) || event.equals(9 /* KeyCode.Escape */)) {\n          const index = indexes.indexOf(this.domNode.ownerDocument.activeElement);\n          if (index >= 0) {\n            let newIndex = -1;\n            if (event.equals(17 /* KeyCode.RightArrow */)) {\n              newIndex = (index + 1) % indexes.length;\n            } else if (event.equals(15 /* KeyCode.LeftArrow */)) {\n              if (index === 0) {\n                newIndex = indexes.length - 1;\n              } else {\n                newIndex = index - 1;\n              }\n            }\n            if (event.equals(9 /* KeyCode.Escape */)) {\n              indexes[index].blur();\n              this.inputBox.focus();\n            } else if (newIndex >= 0) {\n              indexes[newIndex].focus();\n            }\n            dom.EventHelper.stop(event, true);\n          }\n        }\n      });\n    }\n    this.controls = document.createElement('div');\n    this.controls.className = 'controls';\n    this.controls.style.display = this.showCommonFindToggles ? '' : 'none';\n    if (this.caseSensitive) {\n      this.controls.append(this.caseSensitive.domNode);\n    }\n    if (this.wholeWords) {\n      this.controls.appendChild(this.wholeWords.domNode);\n    }\n    if (this.regex) {\n      this.controls.appendChild(this.regex.domNode);\n    }\n    this.setAdditionalToggles(options?.additionalToggles);\n    if (this.controls) {\n      this.domNode.appendChild(this.controls);\n    }\n    parent?.appendChild(this.domNode);\n    this._register(dom.addDisposableListener(this.inputBox.inputElement, 'compositionstart', e => {\n      this.imeSessionInProgress = true;\n    }));\n    this._register(dom.addDisposableListener(this.inputBox.inputElement, 'compositionend', e => {\n      this.imeSessionInProgress = false;\n      this._onInput.fire();\n    }));\n    this.onkeydown(this.inputBox.inputElement, e => this._onKeyDown.fire(e));\n    this.onkeyup(this.inputBox.inputElement, e => this._onKeyUp.fire(e));\n    this.oninput(this.inputBox.inputElement, e => this._onInput.fire());\n    this.onmousedown(this.inputBox.inputElement, e => this._onMouseDown.fire(e));\n  }\n  get onDidChange() {\n    return this.inputBox.onDidChange;\n  }\n  layout(style) {\n    this.inputBox.layout();\n    this.updateInputBoxPadding(style.collapsedFindWidget);\n  }\n  enable() {\n    this.domNode.classList.remove('disabled');\n    this.inputBox.enable();\n    this.regex?.enable();\n    this.wholeWords?.enable();\n    this.caseSensitive?.enable();\n    for (const toggle of this.additionalToggles) {\n      toggle.enable();\n    }\n  }\n  disable() {\n    this.domNode.classList.add('disabled');\n    this.inputBox.disable();\n    this.regex?.disable();\n    this.wholeWords?.disable();\n    this.caseSensitive?.disable();\n    for (const toggle of this.additionalToggles) {\n      toggle.disable();\n    }\n  }\n  setFocusInputOnOptionClick(value) {\n    this.fixFocusOnOptionClickEnabled = value;\n  }\n  setEnabled(enabled) {\n    if (enabled) {\n      this.enable();\n    } else {\n      this.disable();\n    }\n  }\n  setAdditionalToggles(toggles) {\n    for (const currentToggle of this.additionalToggles) {\n      currentToggle.domNode.remove();\n    }\n    this.additionalToggles = [];\n    this.additionalTogglesDisposables.value = new DisposableStore();\n    for (const toggle of toggles ?? []) {\n      this.additionalTogglesDisposables.value.add(toggle);\n      this.controls.appendChild(toggle.domNode);\n      this.additionalTogglesDisposables.value.add(toggle.onChange(viaKeyboard => {\n        this._onDidOptionChange.fire(viaKeyboard);\n        if (!viaKeyboard && this.fixFocusOnOptionClickEnabled) {\n          this.inputBox.focus();\n        }\n      }));\n      this.additionalToggles.push(toggle);\n    }\n    if (this.additionalToggles.length > 0) {\n      this.controls.style.display = '';\n    }\n    this.updateInputBoxPadding();\n  }\n  updateInputBoxPadding(controlsHidden = false) {\n    if (controlsHidden) {\n      this.inputBox.paddingRight = 0;\n    } else {\n      this.inputBox.paddingRight = (this.caseSensitive?.width() ?? 0) + (this.wholeWords?.width() ?? 0) + (this.regex?.width() ?? 0) + this.additionalToggles.reduce((r, t) => r + t.width(), 0);\n    }\n  }\n  getValue() {\n    return this.inputBox.value;\n  }\n  setValue(value) {\n    if (this.inputBox.value !== value) {\n      this.inputBox.value = value;\n    }\n  }\n  select() {\n    this.inputBox.select();\n  }\n  focus() {\n    this.inputBox.focus();\n  }\n  getCaseSensitive() {\n    return this.caseSensitive?.checked ?? false;\n  }\n  setCaseSensitive(value) {\n    if (this.caseSensitive) {\n      this.caseSensitive.checked = value;\n    }\n  }\n  getWholeWords() {\n    return this.wholeWords?.checked ?? false;\n  }\n  setWholeWords(value) {\n    if (this.wholeWords) {\n      this.wholeWords.checked = value;\n    }\n  }\n  getRegex() {\n    return this.regex?.checked ?? false;\n  }\n  setRegex(value) {\n    if (this.regex) {\n      this.regex.checked = value;\n      this.validate();\n    }\n  }\n  focusOnCaseSensitive() {\n    this.caseSensitive?.focus();\n  }\n  highlightFindOptions() {\n    this.domNode.classList.remove('highlight-' + this._lastHighlightFindOptions);\n    this._lastHighlightFindOptions = 1 - this._lastHighlightFindOptions;\n    this.domNode.classList.add('highlight-' + this._lastHighlightFindOptions);\n  }\n  validate() {\n    this.inputBox.validate();\n  }\n  showMessage(message) {\n    this.inputBox.showMessage(message);\n  }\n  clearMessage() {\n    this.inputBox.hideMessage();\n  }\n}", "map": {"version": 3, "names": ["dom", "CaseSensitiveToggle", "RegexToggle", "WholeWordsToggle", "HistoryInputBox", "Widget", "Emitter", "nls", "DisposableStore", "MutableDisposable", "createInstantHoverDelegate", "NLS_DEFAULT_LABEL", "localize", "FindInput", "constructor", "parent", "contextView<PERSON>rovider", "options", "fixFocusOnOptionClickEnabled", "imeSessionInProgress", "additionalTogglesDisposables", "_register", "additionalToggles", "_onDidOptionChange", "onDidOptionChange", "event", "_onKeyDown", "onKeyDown", "_onMouseDown", "onMouseDown", "_onInput", "_onKeyUp", "_onCaseSensitiveKeyDown", "onCaseSensitiveKeyDown", "_onRegexKeyDown", "onRegexKeyDown", "_lastHighlightFindOptions", "placeholder", "validation", "label", "showCommonFindToggles", "appendCaseSensitiveLabel", "appendWholeWordsLabel", "appendRegexLabel", "history", "flexibleHeight", "flexibleWidth", "flexibleMaxHeight", "domNode", "document", "createElement", "classList", "add", "inputBox", "aria<PERSON><PERSON><PERSON>", "validationOptions", "showHistoryHint", "inputBoxStyles", "hoverDelegate", "regex", "appendTitle", "isChecked", "toggleStyles", "onChange", "viaKeyboard", "fire", "focus", "validate", "e", "wholeWords", "caseSensitive", "indexes", "onkeydown", "equals", "index", "indexOf", "ownerDocument", "activeElement", "newIndex", "length", "blur", "EventHelper", "stop", "controls", "className", "style", "display", "append", "append<PERSON><PERSON><PERSON>", "setAdditionalToggles", "addDisposableListener", "inputElement", "onkeyup", "oninput", "onmousedown", "onDidChange", "layout", "updateInputBoxPadding", "collapsedFindWidget", "enable", "remove", "toggle", "disable", "setFocusInputOnOptionClick", "value", "setEnabled", "enabled", "toggles", "currentToggle", "push", "controlsHidden", "paddingRight", "width", "reduce", "r", "t", "getValue", "setValue", "select", "getCaseSensitive", "checked", "setCaseSensitive", "getWholeWords", "setWholeWords", "getRegex", "setRegex", "focusOnCaseSensitive", "highlightFindOptions", "showMessage", "message", "clearMessage", "hideMessage"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/browser/ui/findinput/findInput.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as dom from '../../dom.js';\nimport { CaseSensitiveToggle, RegexToggle, WholeWordsToggle } from './findInputToggles.js';\nimport { HistoryInputBox } from '../inputbox/inputBox.js';\nimport { Widget } from '../widget.js';\nimport { Emitter } from '../../../common/event.js';\nimport './findInput.css';\nimport * as nls from '../../../../nls.js';\nimport { DisposableStore, MutableDisposable } from '../../../common/lifecycle.js';\nimport { createInstantHoverDelegate } from '../hover/hoverDelegateFactory.js';\nconst NLS_DEFAULT_LABEL = nls.localize('defaultLabel', \"input\");\nexport class FindInput extends Widget {\n    constructor(parent, contextViewProvider, options) {\n        super();\n        this.fixFocusOnOptionClickEnabled = true;\n        this.imeSessionInProgress = false;\n        this.additionalTogglesDisposables = this._register(new MutableDisposable());\n        this.additionalToggles = [];\n        this._onDidOptionChange = this._register(new Emitter());\n        this.onDidOptionChange = this._onDidOptionChange.event;\n        this._onKeyDown = this._register(new Emitter());\n        this.onKeyDown = this._onKeyDown.event;\n        this._onMouseDown = this._register(new Emitter());\n        this.onMouseDown = this._onMouseDown.event;\n        this._onInput = this._register(new Emitter());\n        this._onKeyUp = this._register(new Emitter());\n        this._onCaseSensitiveKeyDown = this._register(new Emitter());\n        this.onCaseSensitiveKeyDown = this._onCaseSensitiveKeyDown.event;\n        this._onRegexKeyDown = this._register(new Emitter());\n        this.onRegexKeyDown = this._onRegexKeyDown.event;\n        this._lastHighlightFindOptions = 0;\n        this.placeholder = options.placeholder || '';\n        this.validation = options.validation;\n        this.label = options.label || NLS_DEFAULT_LABEL;\n        this.showCommonFindToggles = !!options.showCommonFindToggles;\n        const appendCaseSensitiveLabel = options.appendCaseSensitiveLabel || '';\n        const appendWholeWordsLabel = options.appendWholeWordsLabel || '';\n        const appendRegexLabel = options.appendRegexLabel || '';\n        const history = options.history || [];\n        const flexibleHeight = !!options.flexibleHeight;\n        const flexibleWidth = !!options.flexibleWidth;\n        const flexibleMaxHeight = options.flexibleMaxHeight;\n        this.domNode = document.createElement('div');\n        this.domNode.classList.add('monaco-findInput');\n        this.inputBox = this._register(new HistoryInputBox(this.domNode, contextViewProvider, {\n            placeholder: this.placeholder || '',\n            ariaLabel: this.label || '',\n            validationOptions: {\n                validation: this.validation\n            },\n            history,\n            showHistoryHint: options.showHistoryHint,\n            flexibleHeight,\n            flexibleWidth,\n            flexibleMaxHeight,\n            inputBoxStyles: options.inputBoxStyles,\n        }));\n        const hoverDelegate = this._register(createInstantHoverDelegate());\n        if (this.showCommonFindToggles) {\n            this.regex = this._register(new RegexToggle({\n                appendTitle: appendRegexLabel,\n                isChecked: false,\n                hoverDelegate,\n                ...options.toggleStyles\n            }));\n            this._register(this.regex.onChange(viaKeyboard => {\n                this._onDidOptionChange.fire(viaKeyboard);\n                if (!viaKeyboard && this.fixFocusOnOptionClickEnabled) {\n                    this.inputBox.focus();\n                }\n                this.validate();\n            }));\n            this._register(this.regex.onKeyDown(e => {\n                this._onRegexKeyDown.fire(e);\n            }));\n            this.wholeWords = this._register(new WholeWordsToggle({\n                appendTitle: appendWholeWordsLabel,\n                isChecked: false,\n                hoverDelegate,\n                ...options.toggleStyles\n            }));\n            this._register(this.wholeWords.onChange(viaKeyboard => {\n                this._onDidOptionChange.fire(viaKeyboard);\n                if (!viaKeyboard && this.fixFocusOnOptionClickEnabled) {\n                    this.inputBox.focus();\n                }\n                this.validate();\n            }));\n            this.caseSensitive = this._register(new CaseSensitiveToggle({\n                appendTitle: appendCaseSensitiveLabel,\n                isChecked: false,\n                hoverDelegate,\n                ...options.toggleStyles\n            }));\n            this._register(this.caseSensitive.onChange(viaKeyboard => {\n                this._onDidOptionChange.fire(viaKeyboard);\n                if (!viaKeyboard && this.fixFocusOnOptionClickEnabled) {\n                    this.inputBox.focus();\n                }\n                this.validate();\n            }));\n            this._register(this.caseSensitive.onKeyDown(e => {\n                this._onCaseSensitiveKeyDown.fire(e);\n            }));\n            // Arrow-Key support to navigate between options\n            const indexes = [this.caseSensitive.domNode, this.wholeWords.domNode, this.regex.domNode];\n            this.onkeydown(this.domNode, (event) => {\n                if (event.equals(15 /* KeyCode.LeftArrow */) || event.equals(17 /* KeyCode.RightArrow */) || event.equals(9 /* KeyCode.Escape */)) {\n                    const index = indexes.indexOf(this.domNode.ownerDocument.activeElement);\n                    if (index >= 0) {\n                        let newIndex = -1;\n                        if (event.equals(17 /* KeyCode.RightArrow */)) {\n                            newIndex = (index + 1) % indexes.length;\n                        }\n                        else if (event.equals(15 /* KeyCode.LeftArrow */)) {\n                            if (index === 0) {\n                                newIndex = indexes.length - 1;\n                            }\n                            else {\n                                newIndex = index - 1;\n                            }\n                        }\n                        if (event.equals(9 /* KeyCode.Escape */)) {\n                            indexes[index].blur();\n                            this.inputBox.focus();\n                        }\n                        else if (newIndex >= 0) {\n                            indexes[newIndex].focus();\n                        }\n                        dom.EventHelper.stop(event, true);\n                    }\n                }\n            });\n        }\n        this.controls = document.createElement('div');\n        this.controls.className = 'controls';\n        this.controls.style.display = this.showCommonFindToggles ? '' : 'none';\n        if (this.caseSensitive) {\n            this.controls.append(this.caseSensitive.domNode);\n        }\n        if (this.wholeWords) {\n            this.controls.appendChild(this.wholeWords.domNode);\n        }\n        if (this.regex) {\n            this.controls.appendChild(this.regex.domNode);\n        }\n        this.setAdditionalToggles(options?.additionalToggles);\n        if (this.controls) {\n            this.domNode.appendChild(this.controls);\n        }\n        parent?.appendChild(this.domNode);\n        this._register(dom.addDisposableListener(this.inputBox.inputElement, 'compositionstart', (e) => {\n            this.imeSessionInProgress = true;\n        }));\n        this._register(dom.addDisposableListener(this.inputBox.inputElement, 'compositionend', (e) => {\n            this.imeSessionInProgress = false;\n            this._onInput.fire();\n        }));\n        this.onkeydown(this.inputBox.inputElement, (e) => this._onKeyDown.fire(e));\n        this.onkeyup(this.inputBox.inputElement, (e) => this._onKeyUp.fire(e));\n        this.oninput(this.inputBox.inputElement, (e) => this._onInput.fire());\n        this.onmousedown(this.inputBox.inputElement, (e) => this._onMouseDown.fire(e));\n    }\n    get onDidChange() {\n        return this.inputBox.onDidChange;\n    }\n    layout(style) {\n        this.inputBox.layout();\n        this.updateInputBoxPadding(style.collapsedFindWidget);\n    }\n    enable() {\n        this.domNode.classList.remove('disabled');\n        this.inputBox.enable();\n        this.regex?.enable();\n        this.wholeWords?.enable();\n        this.caseSensitive?.enable();\n        for (const toggle of this.additionalToggles) {\n            toggle.enable();\n        }\n    }\n    disable() {\n        this.domNode.classList.add('disabled');\n        this.inputBox.disable();\n        this.regex?.disable();\n        this.wholeWords?.disable();\n        this.caseSensitive?.disable();\n        for (const toggle of this.additionalToggles) {\n            toggle.disable();\n        }\n    }\n    setFocusInputOnOptionClick(value) {\n        this.fixFocusOnOptionClickEnabled = value;\n    }\n    setEnabled(enabled) {\n        if (enabled) {\n            this.enable();\n        }\n        else {\n            this.disable();\n        }\n    }\n    setAdditionalToggles(toggles) {\n        for (const currentToggle of this.additionalToggles) {\n            currentToggle.domNode.remove();\n        }\n        this.additionalToggles = [];\n        this.additionalTogglesDisposables.value = new DisposableStore();\n        for (const toggle of toggles ?? []) {\n            this.additionalTogglesDisposables.value.add(toggle);\n            this.controls.appendChild(toggle.domNode);\n            this.additionalTogglesDisposables.value.add(toggle.onChange(viaKeyboard => {\n                this._onDidOptionChange.fire(viaKeyboard);\n                if (!viaKeyboard && this.fixFocusOnOptionClickEnabled) {\n                    this.inputBox.focus();\n                }\n            }));\n            this.additionalToggles.push(toggle);\n        }\n        if (this.additionalToggles.length > 0) {\n            this.controls.style.display = '';\n        }\n        this.updateInputBoxPadding();\n    }\n    updateInputBoxPadding(controlsHidden = false) {\n        if (controlsHidden) {\n            this.inputBox.paddingRight = 0;\n        }\n        else {\n            this.inputBox.paddingRight =\n                ((this.caseSensitive?.width() ?? 0) + (this.wholeWords?.width() ?? 0) + (this.regex?.width() ?? 0))\n                    + this.additionalToggles.reduce((r, t) => r + t.width(), 0);\n        }\n    }\n    getValue() {\n        return this.inputBox.value;\n    }\n    setValue(value) {\n        if (this.inputBox.value !== value) {\n            this.inputBox.value = value;\n        }\n    }\n    select() {\n        this.inputBox.select();\n    }\n    focus() {\n        this.inputBox.focus();\n    }\n    getCaseSensitive() {\n        return this.caseSensitive?.checked ?? false;\n    }\n    setCaseSensitive(value) {\n        if (this.caseSensitive) {\n            this.caseSensitive.checked = value;\n        }\n    }\n    getWholeWords() {\n        return this.wholeWords?.checked ?? false;\n    }\n    setWholeWords(value) {\n        if (this.wholeWords) {\n            this.wholeWords.checked = value;\n        }\n    }\n    getRegex() {\n        return this.regex?.checked ?? false;\n    }\n    setRegex(value) {\n        if (this.regex) {\n            this.regex.checked = value;\n            this.validate();\n        }\n    }\n    focusOnCaseSensitive() {\n        this.caseSensitive?.focus();\n    }\n    highlightFindOptions() {\n        this.domNode.classList.remove('highlight-' + (this._lastHighlightFindOptions));\n        this._lastHighlightFindOptions = 1 - this._lastHighlightFindOptions;\n        this.domNode.classList.add('highlight-' + (this._lastHighlightFindOptions));\n    }\n    validate() {\n        this.inputBox.validate();\n    }\n    showMessage(message) {\n        this.inputBox.showMessage(message);\n    }\n    clearMessage() {\n        this.inputBox.hideMessage();\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,KAAKA,GAAG,MAAM,cAAc;AACnC,SAASC,mBAAmB,EAAEC,WAAW,EAAEC,gBAAgB,QAAQ,uBAAuB;AAC1F,SAASC,eAAe,QAAQ,yBAAyB;AACzD,SAASC,MAAM,QAAQ,cAAc;AACrC,SAASC,OAAO,QAAQ,0BAA0B;AAClD,OAAO,iBAAiB;AACxB,OAAO,KAAKC,GAAG,MAAM,oBAAoB;AACzC,SAASC,eAAe,EAAEC,iBAAiB,QAAQ,8BAA8B;AACjF,SAASC,0BAA0B,QAAQ,kCAAkC;AAC7E,MAAMC,iBAAiB,GAAGJ,GAAG,CAACK,QAAQ,CAAC,cAAc,EAAE,OAAO,CAAC;AAC/D,OAAO,MAAMC,SAAS,SAASR,MAAM,CAAC;EAClCS,WAAWA,CAACC,MAAM,EAAEC,mBAAmB,EAAEC,OAAO,EAAE;IAC9C,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,4BAA4B,GAAG,IAAI;IACxC,IAAI,CAACC,oBAAoB,GAAG,KAAK;IACjC,IAAI,CAACC,4BAA4B,GAAG,IAAI,CAACC,SAAS,CAAC,IAAIZ,iBAAiB,CAAC,CAAC,CAAC;IAC3E,IAAI,CAACa,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACC,kBAAkB,GAAG,IAAI,CAACF,SAAS,CAAC,IAAIf,OAAO,CAAC,CAAC,CAAC;IACvD,IAAI,CAACkB,iBAAiB,GAAG,IAAI,CAACD,kBAAkB,CAACE,KAAK;IACtD,IAAI,CAACC,UAAU,GAAG,IAAI,CAACL,SAAS,CAAC,IAAIf,OAAO,CAAC,CAAC,CAAC;IAC/C,IAAI,CAACqB,SAAS,GAAG,IAAI,CAACD,UAAU,CAACD,KAAK;IACtC,IAAI,CAACG,YAAY,GAAG,IAAI,CAACP,SAAS,CAAC,IAAIf,OAAO,CAAC,CAAC,CAAC;IACjD,IAAI,CAACuB,WAAW,GAAG,IAAI,CAACD,YAAY,CAACH,KAAK;IAC1C,IAAI,CAACK,QAAQ,GAAG,IAAI,CAACT,SAAS,CAAC,IAAIf,OAAO,CAAC,CAAC,CAAC;IAC7C,IAAI,CAACyB,QAAQ,GAAG,IAAI,CAACV,SAAS,CAAC,IAAIf,OAAO,CAAC,CAAC,CAAC;IAC7C,IAAI,CAAC0B,uBAAuB,GAAG,IAAI,CAACX,SAAS,CAAC,IAAIf,OAAO,CAAC,CAAC,CAAC;IAC5D,IAAI,CAAC2B,sBAAsB,GAAG,IAAI,CAACD,uBAAuB,CAACP,KAAK;IAChE,IAAI,CAACS,eAAe,GAAG,IAAI,CAACb,SAAS,CAAC,IAAIf,OAAO,CAAC,CAAC,CAAC;IACpD,IAAI,CAAC6B,cAAc,GAAG,IAAI,CAACD,eAAe,CAACT,KAAK;IAChD,IAAI,CAACW,yBAAyB,GAAG,CAAC;IAClC,IAAI,CAACC,WAAW,GAAGpB,OAAO,CAACoB,WAAW,IAAI,EAAE;IAC5C,IAAI,CAACC,UAAU,GAAGrB,OAAO,CAACqB,UAAU;IACpC,IAAI,CAACC,KAAK,GAAGtB,OAAO,CAACsB,KAAK,IAAI5B,iBAAiB;IAC/C,IAAI,CAAC6B,qBAAqB,GAAG,CAAC,CAACvB,OAAO,CAACuB,qBAAqB;IAC5D,MAAMC,wBAAwB,GAAGxB,OAAO,CAACwB,wBAAwB,IAAI,EAAE;IACvE,MAAMC,qBAAqB,GAAGzB,OAAO,CAACyB,qBAAqB,IAAI,EAAE;IACjE,MAAMC,gBAAgB,GAAG1B,OAAO,CAAC0B,gBAAgB,IAAI,EAAE;IACvD,MAAMC,OAAO,GAAG3B,OAAO,CAAC2B,OAAO,IAAI,EAAE;IACrC,MAAMC,cAAc,GAAG,CAAC,CAAC5B,OAAO,CAAC4B,cAAc;IAC/C,MAAMC,aAAa,GAAG,CAAC,CAAC7B,OAAO,CAAC6B,aAAa;IAC7C,MAAMC,iBAAiB,GAAG9B,OAAO,CAAC8B,iBAAiB;IACnD,IAAI,CAACC,OAAO,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC5C,IAAI,CAACF,OAAO,CAACG,SAAS,CAACC,GAAG,CAAC,kBAAkB,CAAC;IAC9C,IAAI,CAACC,QAAQ,GAAG,IAAI,CAAChC,SAAS,CAAC,IAAIjB,eAAe,CAAC,IAAI,CAAC4C,OAAO,EAAEhC,mBAAmB,EAAE;MAClFqB,WAAW,EAAE,IAAI,CAACA,WAAW,IAAI,EAAE;MACnCiB,SAAS,EAAE,IAAI,CAACf,KAAK,IAAI,EAAE;MAC3BgB,iBAAiB,EAAE;QACfjB,UAAU,EAAE,IAAI,CAACA;MACrB,CAAC;MACDM,OAAO;MACPY,eAAe,EAAEvC,OAAO,CAACuC,eAAe;MACxCX,cAAc;MACdC,aAAa;MACbC,iBAAiB;MACjBU,cAAc,EAAExC,OAAO,CAACwC;IAC5B,CAAC,CAAC,CAAC;IACH,MAAMC,aAAa,GAAG,IAAI,CAACrC,SAAS,CAACX,0BAA0B,CAAC,CAAC,CAAC;IAClE,IAAI,IAAI,CAAC8B,qBAAqB,EAAE;MAC5B,IAAI,CAACmB,KAAK,GAAG,IAAI,CAACtC,SAAS,CAAC,IAAInB,WAAW,CAAC;QACxC0D,WAAW,EAAEjB,gBAAgB;QAC7BkB,SAAS,EAAE,KAAK;QAChBH,aAAa;QACb,GAAGzC,OAAO,CAAC6C;MACf,CAAC,CAAC,CAAC;MACH,IAAI,CAACzC,SAAS,CAAC,IAAI,CAACsC,KAAK,CAACI,QAAQ,CAACC,WAAW,IAAI;QAC9C,IAAI,CAACzC,kBAAkB,CAAC0C,IAAI,CAACD,WAAW,CAAC;QACzC,IAAI,CAACA,WAAW,IAAI,IAAI,CAAC9C,4BAA4B,EAAE;UACnD,IAAI,CAACmC,QAAQ,CAACa,KAAK,CAAC,CAAC;QACzB;QACA,IAAI,CAACC,QAAQ,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC;MACH,IAAI,CAAC9C,SAAS,CAAC,IAAI,CAACsC,KAAK,CAAChC,SAAS,CAACyC,CAAC,IAAI;QACrC,IAAI,CAAClC,eAAe,CAAC+B,IAAI,CAACG,CAAC,CAAC;MAChC,CAAC,CAAC,CAAC;MACH,IAAI,CAACC,UAAU,GAAG,IAAI,CAAChD,SAAS,CAAC,IAAIlB,gBAAgB,CAAC;QAClDyD,WAAW,EAAElB,qBAAqB;QAClCmB,SAAS,EAAE,KAAK;QAChBH,aAAa;QACb,GAAGzC,OAAO,CAAC6C;MACf,CAAC,CAAC,CAAC;MACH,IAAI,CAACzC,SAAS,CAAC,IAAI,CAACgD,UAAU,CAACN,QAAQ,CAACC,WAAW,IAAI;QACnD,IAAI,CAACzC,kBAAkB,CAAC0C,IAAI,CAACD,WAAW,CAAC;QACzC,IAAI,CAACA,WAAW,IAAI,IAAI,CAAC9C,4BAA4B,EAAE;UACnD,IAAI,CAACmC,QAAQ,CAACa,KAAK,CAAC,CAAC;QACzB;QACA,IAAI,CAACC,QAAQ,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC;MACH,IAAI,CAACG,aAAa,GAAG,IAAI,CAACjD,SAAS,CAAC,IAAIpB,mBAAmB,CAAC;QACxD2D,WAAW,EAAEnB,wBAAwB;QACrCoB,SAAS,EAAE,KAAK;QAChBH,aAAa;QACb,GAAGzC,OAAO,CAAC6C;MACf,CAAC,CAAC,CAAC;MACH,IAAI,CAACzC,SAAS,CAAC,IAAI,CAACiD,aAAa,CAACP,QAAQ,CAACC,WAAW,IAAI;QACtD,IAAI,CAACzC,kBAAkB,CAAC0C,IAAI,CAACD,WAAW,CAAC;QACzC,IAAI,CAACA,WAAW,IAAI,IAAI,CAAC9C,4BAA4B,EAAE;UACnD,IAAI,CAACmC,QAAQ,CAACa,KAAK,CAAC,CAAC;QACzB;QACA,IAAI,CAACC,QAAQ,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC;MACH,IAAI,CAAC9C,SAAS,CAAC,IAAI,CAACiD,aAAa,CAAC3C,SAAS,CAACyC,CAAC,IAAI;QAC7C,IAAI,CAACpC,uBAAuB,CAACiC,IAAI,CAACG,CAAC,CAAC;MACxC,CAAC,CAAC,CAAC;MACH;MACA,MAAMG,OAAO,GAAG,CAAC,IAAI,CAACD,aAAa,CAACtB,OAAO,EAAE,IAAI,CAACqB,UAAU,CAACrB,OAAO,EAAE,IAAI,CAACW,KAAK,CAACX,OAAO,CAAC;MACzF,IAAI,CAACwB,SAAS,CAAC,IAAI,CAACxB,OAAO,EAAGvB,KAAK,IAAK;QACpC,IAAIA,KAAK,CAACgD,MAAM,CAAC,EAAE,CAAC,uBAAuB,CAAC,IAAIhD,KAAK,CAACgD,MAAM,CAAC,EAAE,CAAC,wBAAwB,CAAC,IAAIhD,KAAK,CAACgD,MAAM,CAAC,CAAC,CAAC,oBAAoB,CAAC,EAAE;UAC/H,MAAMC,KAAK,GAAGH,OAAO,CAACI,OAAO,CAAC,IAAI,CAAC3B,OAAO,CAAC4B,aAAa,CAACC,aAAa,CAAC;UACvE,IAAIH,KAAK,IAAI,CAAC,EAAE;YACZ,IAAII,QAAQ,GAAG,CAAC,CAAC;YACjB,IAAIrD,KAAK,CAACgD,MAAM,CAAC,EAAE,CAAC,wBAAwB,CAAC,EAAE;cAC3CK,QAAQ,GAAG,CAACJ,KAAK,GAAG,CAAC,IAAIH,OAAO,CAACQ,MAAM;YAC3C,CAAC,MACI,IAAItD,KAAK,CAACgD,MAAM,CAAC,EAAE,CAAC,uBAAuB,CAAC,EAAE;cAC/C,IAAIC,KAAK,KAAK,CAAC,EAAE;gBACbI,QAAQ,GAAGP,OAAO,CAACQ,MAAM,GAAG,CAAC;cACjC,CAAC,MACI;gBACDD,QAAQ,GAAGJ,KAAK,GAAG,CAAC;cACxB;YACJ;YACA,IAAIjD,KAAK,CAACgD,MAAM,CAAC,CAAC,CAAC,oBAAoB,CAAC,EAAE;cACtCF,OAAO,CAACG,KAAK,CAAC,CAACM,IAAI,CAAC,CAAC;cACrB,IAAI,CAAC3B,QAAQ,CAACa,KAAK,CAAC,CAAC;YACzB,CAAC,MACI,IAAIY,QAAQ,IAAI,CAAC,EAAE;cACpBP,OAAO,CAACO,QAAQ,CAAC,CAACZ,KAAK,CAAC,CAAC;YAC7B;YACAlE,GAAG,CAACiF,WAAW,CAACC,IAAI,CAACzD,KAAK,EAAE,IAAI,CAAC;UACrC;QACJ;MACJ,CAAC,CAAC;IACN;IACA,IAAI,CAAC0D,QAAQ,GAAGlC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC7C,IAAI,CAACiC,QAAQ,CAACC,SAAS,GAAG,UAAU;IACpC,IAAI,CAACD,QAAQ,CAACE,KAAK,CAACC,OAAO,GAAG,IAAI,CAAC9C,qBAAqB,GAAG,EAAE,GAAG,MAAM;IACtE,IAAI,IAAI,CAAC8B,aAAa,EAAE;MACpB,IAAI,CAACa,QAAQ,CAACI,MAAM,CAAC,IAAI,CAACjB,aAAa,CAACtB,OAAO,CAAC;IACpD;IACA,IAAI,IAAI,CAACqB,UAAU,EAAE;MACjB,IAAI,CAACc,QAAQ,CAACK,WAAW,CAAC,IAAI,CAACnB,UAAU,CAACrB,OAAO,CAAC;IACtD;IACA,IAAI,IAAI,CAACW,KAAK,EAAE;MACZ,IAAI,CAACwB,QAAQ,CAACK,WAAW,CAAC,IAAI,CAAC7B,KAAK,CAACX,OAAO,CAAC;IACjD;IACA,IAAI,CAACyC,oBAAoB,CAACxE,OAAO,EAAEK,iBAAiB,CAAC;IACrD,IAAI,IAAI,CAAC6D,QAAQ,EAAE;MACf,IAAI,CAACnC,OAAO,CAACwC,WAAW,CAAC,IAAI,CAACL,QAAQ,CAAC;IAC3C;IACApE,MAAM,EAAEyE,WAAW,CAAC,IAAI,CAACxC,OAAO,CAAC;IACjC,IAAI,CAAC3B,SAAS,CAACrB,GAAG,CAAC0F,qBAAqB,CAAC,IAAI,CAACrC,QAAQ,CAACsC,YAAY,EAAE,kBAAkB,EAAGvB,CAAC,IAAK;MAC5F,IAAI,CAACjD,oBAAoB,GAAG,IAAI;IACpC,CAAC,CAAC,CAAC;IACH,IAAI,CAACE,SAAS,CAACrB,GAAG,CAAC0F,qBAAqB,CAAC,IAAI,CAACrC,QAAQ,CAACsC,YAAY,EAAE,gBAAgB,EAAGvB,CAAC,IAAK;MAC1F,IAAI,CAACjD,oBAAoB,GAAG,KAAK;MACjC,IAAI,CAACW,QAAQ,CAACmC,IAAI,CAAC,CAAC;IACxB,CAAC,CAAC,CAAC;IACH,IAAI,CAACO,SAAS,CAAC,IAAI,CAACnB,QAAQ,CAACsC,YAAY,EAAGvB,CAAC,IAAK,IAAI,CAAC1C,UAAU,CAACuC,IAAI,CAACG,CAAC,CAAC,CAAC;IAC1E,IAAI,CAACwB,OAAO,CAAC,IAAI,CAACvC,QAAQ,CAACsC,YAAY,EAAGvB,CAAC,IAAK,IAAI,CAACrC,QAAQ,CAACkC,IAAI,CAACG,CAAC,CAAC,CAAC;IACtE,IAAI,CAACyB,OAAO,CAAC,IAAI,CAACxC,QAAQ,CAACsC,YAAY,EAAGvB,CAAC,IAAK,IAAI,CAACtC,QAAQ,CAACmC,IAAI,CAAC,CAAC,CAAC;IACrE,IAAI,CAAC6B,WAAW,CAAC,IAAI,CAACzC,QAAQ,CAACsC,YAAY,EAAGvB,CAAC,IAAK,IAAI,CAACxC,YAAY,CAACqC,IAAI,CAACG,CAAC,CAAC,CAAC;EAClF;EACA,IAAI2B,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAAC1C,QAAQ,CAAC0C,WAAW;EACpC;EACAC,MAAMA,CAACX,KAAK,EAAE;IACV,IAAI,CAAChC,QAAQ,CAAC2C,MAAM,CAAC,CAAC;IACtB,IAAI,CAACC,qBAAqB,CAACZ,KAAK,CAACa,mBAAmB,CAAC;EACzD;EACAC,MAAMA,CAAA,EAAG;IACL,IAAI,CAACnD,OAAO,CAACG,SAAS,CAACiD,MAAM,CAAC,UAAU,CAAC;IACzC,IAAI,CAAC/C,QAAQ,CAAC8C,MAAM,CAAC,CAAC;IACtB,IAAI,CAACxC,KAAK,EAAEwC,MAAM,CAAC,CAAC;IACpB,IAAI,CAAC9B,UAAU,EAAE8B,MAAM,CAAC,CAAC;IACzB,IAAI,CAAC7B,aAAa,EAAE6B,MAAM,CAAC,CAAC;IAC5B,KAAK,MAAME,MAAM,IAAI,IAAI,CAAC/E,iBAAiB,EAAE;MACzC+E,MAAM,CAACF,MAAM,CAAC,CAAC;IACnB;EACJ;EACAG,OAAOA,CAAA,EAAG;IACN,IAAI,CAACtD,OAAO,CAACG,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC;IACtC,IAAI,CAACC,QAAQ,CAACiD,OAAO,CAAC,CAAC;IACvB,IAAI,CAAC3C,KAAK,EAAE2C,OAAO,CAAC,CAAC;IACrB,IAAI,CAACjC,UAAU,EAAEiC,OAAO,CAAC,CAAC;IAC1B,IAAI,CAAChC,aAAa,EAAEgC,OAAO,CAAC,CAAC;IAC7B,KAAK,MAAMD,MAAM,IAAI,IAAI,CAAC/E,iBAAiB,EAAE;MACzC+E,MAAM,CAACC,OAAO,CAAC,CAAC;IACpB;EACJ;EACAC,0BAA0BA,CAACC,KAAK,EAAE;IAC9B,IAAI,CAACtF,4BAA4B,GAAGsF,KAAK;EAC7C;EACAC,UAAUA,CAACC,OAAO,EAAE;IAChB,IAAIA,OAAO,EAAE;MACT,IAAI,CAACP,MAAM,CAAC,CAAC;IACjB,CAAC,MACI;MACD,IAAI,CAACG,OAAO,CAAC,CAAC;IAClB;EACJ;EACAb,oBAAoBA,CAACkB,OAAO,EAAE;IAC1B,KAAK,MAAMC,aAAa,IAAI,IAAI,CAACtF,iBAAiB,EAAE;MAChDsF,aAAa,CAAC5D,OAAO,CAACoD,MAAM,CAAC,CAAC;IAClC;IACA,IAAI,CAAC9E,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACF,4BAA4B,CAACoF,KAAK,GAAG,IAAIhG,eAAe,CAAC,CAAC;IAC/D,KAAK,MAAM6F,MAAM,IAAIM,OAAO,IAAI,EAAE,EAAE;MAChC,IAAI,CAACvF,4BAA4B,CAACoF,KAAK,CAACpD,GAAG,CAACiD,MAAM,CAAC;MACnD,IAAI,CAAClB,QAAQ,CAACK,WAAW,CAACa,MAAM,CAACrD,OAAO,CAAC;MACzC,IAAI,CAAC5B,4BAA4B,CAACoF,KAAK,CAACpD,GAAG,CAACiD,MAAM,CAACtC,QAAQ,CAACC,WAAW,IAAI;QACvE,IAAI,CAACzC,kBAAkB,CAAC0C,IAAI,CAACD,WAAW,CAAC;QACzC,IAAI,CAACA,WAAW,IAAI,IAAI,CAAC9C,4BAA4B,EAAE;UACnD,IAAI,CAACmC,QAAQ,CAACa,KAAK,CAAC,CAAC;QACzB;MACJ,CAAC,CAAC,CAAC;MACH,IAAI,CAAC5C,iBAAiB,CAACuF,IAAI,CAACR,MAAM,CAAC;IACvC;IACA,IAAI,IAAI,CAAC/E,iBAAiB,CAACyD,MAAM,GAAG,CAAC,EAAE;MACnC,IAAI,CAACI,QAAQ,CAACE,KAAK,CAACC,OAAO,GAAG,EAAE;IACpC;IACA,IAAI,CAACW,qBAAqB,CAAC,CAAC;EAChC;EACAA,qBAAqBA,CAACa,cAAc,GAAG,KAAK,EAAE;IAC1C,IAAIA,cAAc,EAAE;MAChB,IAAI,CAACzD,QAAQ,CAAC0D,YAAY,GAAG,CAAC;IAClC,CAAC,MACI;MACD,IAAI,CAAC1D,QAAQ,CAAC0D,YAAY,GACrB,CAAC,IAAI,CAACzC,aAAa,EAAE0C,KAAK,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC3C,UAAU,EAAE2C,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,IAAI,CAACrD,KAAK,EAAEqD,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,GAC5F,IAAI,CAAC1F,iBAAiB,CAAC2F,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAACH,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;IACvE;EACJ;EACAI,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAAC/D,QAAQ,CAACmD,KAAK;EAC9B;EACAa,QAAQA,CAACb,KAAK,EAAE;IACZ,IAAI,IAAI,CAACnD,QAAQ,CAACmD,KAAK,KAAKA,KAAK,EAAE;MAC/B,IAAI,CAACnD,QAAQ,CAACmD,KAAK,GAAGA,KAAK;IAC/B;EACJ;EACAc,MAAMA,CAAA,EAAG;IACL,IAAI,CAACjE,QAAQ,CAACiE,MAAM,CAAC,CAAC;EAC1B;EACApD,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACb,QAAQ,CAACa,KAAK,CAAC,CAAC;EACzB;EACAqD,gBAAgBA,CAAA,EAAG;IACf,OAAO,IAAI,CAACjD,aAAa,EAAEkD,OAAO,IAAI,KAAK;EAC/C;EACAC,gBAAgBA,CAACjB,KAAK,EAAE;IACpB,IAAI,IAAI,CAAClC,aAAa,EAAE;MACpB,IAAI,CAACA,aAAa,CAACkD,OAAO,GAAGhB,KAAK;IACtC;EACJ;EACAkB,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACrD,UAAU,EAAEmD,OAAO,IAAI,KAAK;EAC5C;EACAG,aAAaA,CAACnB,KAAK,EAAE;IACjB,IAAI,IAAI,CAACnC,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAACmD,OAAO,GAAGhB,KAAK;IACnC;EACJ;EACAoB,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACjE,KAAK,EAAE6D,OAAO,IAAI,KAAK;EACvC;EACAK,QAAQA,CAACrB,KAAK,EAAE;IACZ,IAAI,IAAI,CAAC7C,KAAK,EAAE;MACZ,IAAI,CAACA,KAAK,CAAC6D,OAAO,GAAGhB,KAAK;MAC1B,IAAI,CAACrC,QAAQ,CAAC,CAAC;IACnB;EACJ;EACA2D,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACxD,aAAa,EAAEJ,KAAK,CAAC,CAAC;EAC/B;EACA6D,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAAC/E,OAAO,CAACG,SAAS,CAACiD,MAAM,CAAC,YAAY,GAAI,IAAI,CAAChE,yBAA0B,CAAC;IAC9E,IAAI,CAACA,yBAAyB,GAAG,CAAC,GAAG,IAAI,CAACA,yBAAyB;IACnE,IAAI,CAACY,OAAO,CAACG,SAAS,CAACC,GAAG,CAAC,YAAY,GAAI,IAAI,CAAChB,yBAA0B,CAAC;EAC/E;EACA+B,QAAQA,CAAA,EAAG;IACP,IAAI,CAACd,QAAQ,CAACc,QAAQ,CAAC,CAAC;EAC5B;EACA6D,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAI,CAAC5E,QAAQ,CAAC2E,WAAW,CAACC,OAAO,CAAC;EACtC;EACAC,YAAYA,CAAA,EAAG;IACX,IAAI,CAAC7E,QAAQ,CAAC8E,WAAW,CAAC,CAAC;EAC/B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}