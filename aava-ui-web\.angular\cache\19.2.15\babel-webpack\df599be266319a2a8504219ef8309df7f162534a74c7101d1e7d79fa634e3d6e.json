{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { GlobalPointerMoveMonitor } from '../../globalPointerMoveMonitor.js';\nimport { Widget } from '../widget.js';\nimport { TimeoutTimer } from '../../../common/async.js';\nimport { ThemeIcon } from '../../../common/themables.js';\nimport * as dom from '../../dom.js';\n/**\n * The arrow image size.\n */\nexport const ARROW_IMG_SIZE = 11;\nexport class ScrollbarArrow extends Widget {\n  constructor(opts) {\n    super();\n    this._onActivate = opts.onActivate;\n    this.bgDomNode = document.createElement('div');\n    this.bgDomNode.className = 'arrow-background';\n    this.bgDomNode.style.position = 'absolute';\n    this.bgDomNode.style.width = opts.bgWidth + 'px';\n    this.bgDomNode.style.height = opts.bgHeight + 'px';\n    if (typeof opts.top !== 'undefined') {\n      this.bgDomNode.style.top = '0px';\n    }\n    if (typeof opts.left !== 'undefined') {\n      this.bgDomNode.style.left = '0px';\n    }\n    if (typeof opts.bottom !== 'undefined') {\n      this.bgDomNode.style.bottom = '0px';\n    }\n    if (typeof opts.right !== 'undefined') {\n      this.bgDomNode.style.right = '0px';\n    }\n    this.domNode = document.createElement('div');\n    this.domNode.className = opts.className;\n    this.domNode.classList.add(...ThemeIcon.asClassNameArray(opts.icon));\n    this.domNode.style.position = 'absolute';\n    this.domNode.style.width = ARROW_IMG_SIZE + 'px';\n    this.domNode.style.height = ARROW_IMG_SIZE + 'px';\n    if (typeof opts.top !== 'undefined') {\n      this.domNode.style.top = opts.top + 'px';\n    }\n    if (typeof opts.left !== 'undefined') {\n      this.domNode.style.left = opts.left + 'px';\n    }\n    if (typeof opts.bottom !== 'undefined') {\n      this.domNode.style.bottom = opts.bottom + 'px';\n    }\n    if (typeof opts.right !== 'undefined') {\n      this.domNode.style.right = opts.right + 'px';\n    }\n    this._pointerMoveMonitor = this._register(new GlobalPointerMoveMonitor());\n    this._register(dom.addStandardDisposableListener(this.bgDomNode, dom.EventType.POINTER_DOWN, e => this._arrowPointerDown(e)));\n    this._register(dom.addStandardDisposableListener(this.domNode, dom.EventType.POINTER_DOWN, e => this._arrowPointerDown(e)));\n    this._pointerdownRepeatTimer = this._register(new dom.WindowIntervalTimer());\n    this._pointerdownScheduleRepeatTimer = this._register(new TimeoutTimer());\n  }\n  _arrowPointerDown(e) {\n    if (!e.target || !(e.target instanceof Element)) {\n      return;\n    }\n    const scheduleRepeater = () => {\n      this._pointerdownRepeatTimer.cancelAndSet(() => this._onActivate(), 1000 / 24, dom.getWindow(e));\n    };\n    this._onActivate();\n    this._pointerdownRepeatTimer.cancel();\n    this._pointerdownScheduleRepeatTimer.cancelAndSet(scheduleRepeater, 200);\n    this._pointerMoveMonitor.startMonitoring(e.target, e.pointerId, e.buttons, pointerMoveData => {}, () => {\n      this._pointerdownRepeatTimer.cancel();\n      this._pointerdownScheduleRepeatTimer.cancel();\n    });\n    e.preventDefault();\n  }\n}", "map": {"version": 3, "names": ["GlobalPointerMoveMonitor", "Widget", "TimeoutTimer", "ThemeIcon", "dom", "ARROW_IMG_SIZE", "ScrollbarArrow", "constructor", "opts", "_onActivate", "onActivate", "bgDomNode", "document", "createElement", "className", "style", "position", "width", "bgWidth", "height", "bgHeight", "top", "left", "bottom", "right", "domNode", "classList", "add", "asClassNameArray", "icon", "_pointerMoveMonitor", "_register", "addStandardDisposableListener", "EventType", "POINTER_DOWN", "e", "_arrowPointerDown", "_pointerdownRepeatTimer", "WindowIntervalTimer", "_pointerdownScheduleRepeatTimer", "target", "Element", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cancelAndSet", "getWindow", "cancel", "startMonitoring", "pointerId", "buttons", "pointerMoveData", "preventDefault"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/browser/ui/scrollbar/scrollbarArrow.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { GlobalPointerMoveMonitor } from '../../globalPointerMoveMonitor.js';\nimport { Widget } from '../widget.js';\nimport { TimeoutTimer } from '../../../common/async.js';\nimport { ThemeIcon } from '../../../common/themables.js';\nimport * as dom from '../../dom.js';\n/**\n * The arrow image size.\n */\nexport const ARROW_IMG_SIZE = 11;\nexport class ScrollbarArrow extends Widget {\n    constructor(opts) {\n        super();\n        this._onActivate = opts.onActivate;\n        this.bgDomNode = document.createElement('div');\n        this.bgDomNode.className = 'arrow-background';\n        this.bgDomNode.style.position = 'absolute';\n        this.bgDomNode.style.width = opts.bgWidth + 'px';\n        this.bgDomNode.style.height = opts.bgHeight + 'px';\n        if (typeof opts.top !== 'undefined') {\n            this.bgDomNode.style.top = '0px';\n        }\n        if (typeof opts.left !== 'undefined') {\n            this.bgDomNode.style.left = '0px';\n        }\n        if (typeof opts.bottom !== 'undefined') {\n            this.bgDomNode.style.bottom = '0px';\n        }\n        if (typeof opts.right !== 'undefined') {\n            this.bgDomNode.style.right = '0px';\n        }\n        this.domNode = document.createElement('div');\n        this.domNode.className = opts.className;\n        this.domNode.classList.add(...ThemeIcon.asClassNameArray(opts.icon));\n        this.domNode.style.position = 'absolute';\n        this.domNode.style.width = ARROW_IMG_SIZE + 'px';\n        this.domNode.style.height = ARROW_IMG_SIZE + 'px';\n        if (typeof opts.top !== 'undefined') {\n            this.domNode.style.top = opts.top + 'px';\n        }\n        if (typeof opts.left !== 'undefined') {\n            this.domNode.style.left = opts.left + 'px';\n        }\n        if (typeof opts.bottom !== 'undefined') {\n            this.domNode.style.bottom = opts.bottom + 'px';\n        }\n        if (typeof opts.right !== 'undefined') {\n            this.domNode.style.right = opts.right + 'px';\n        }\n        this._pointerMoveMonitor = this._register(new GlobalPointerMoveMonitor());\n        this._register(dom.addStandardDisposableListener(this.bgDomNode, dom.EventType.POINTER_DOWN, (e) => this._arrowPointerDown(e)));\n        this._register(dom.addStandardDisposableListener(this.domNode, dom.EventType.POINTER_DOWN, (e) => this._arrowPointerDown(e)));\n        this._pointerdownRepeatTimer = this._register(new dom.WindowIntervalTimer());\n        this._pointerdownScheduleRepeatTimer = this._register(new TimeoutTimer());\n    }\n    _arrowPointerDown(e) {\n        if (!e.target || !(e.target instanceof Element)) {\n            return;\n        }\n        const scheduleRepeater = () => {\n            this._pointerdownRepeatTimer.cancelAndSet(() => this._onActivate(), 1000 / 24, dom.getWindow(e));\n        };\n        this._onActivate();\n        this._pointerdownRepeatTimer.cancel();\n        this._pointerdownScheduleRepeatTimer.cancelAndSet(scheduleRepeater, 200);\n        this._pointerMoveMonitor.startMonitoring(e.target, e.pointerId, e.buttons, (pointerMoveData) => { }, () => {\n            this._pointerdownRepeatTimer.cancel();\n            this._pointerdownScheduleRepeatTimer.cancel();\n        });\n        e.preventDefault();\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,wBAAwB,QAAQ,mCAAmC;AAC5E,SAASC,MAAM,QAAQ,cAAc;AACrC,SAASC,YAAY,QAAQ,0BAA0B;AACvD,SAASC,SAAS,QAAQ,8BAA8B;AACxD,OAAO,KAAKC,GAAG,MAAM,cAAc;AACnC;AACA;AACA;AACA,OAAO,MAAMC,cAAc,GAAG,EAAE;AAChC,OAAO,MAAMC,cAAc,SAASL,MAAM,CAAC;EACvCM,WAAWA,CAACC,IAAI,EAAE;IACd,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,WAAW,GAAGD,IAAI,CAACE,UAAU;IAClC,IAAI,CAACC,SAAS,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC9C,IAAI,CAACF,SAAS,CAACG,SAAS,GAAG,kBAAkB;IAC7C,IAAI,CAACH,SAAS,CAACI,KAAK,CAACC,QAAQ,GAAG,UAAU;IAC1C,IAAI,CAACL,SAAS,CAACI,KAAK,CAACE,KAAK,GAAGT,IAAI,CAACU,OAAO,GAAG,IAAI;IAChD,IAAI,CAACP,SAAS,CAACI,KAAK,CAACI,MAAM,GAAGX,IAAI,CAACY,QAAQ,GAAG,IAAI;IAClD,IAAI,OAAOZ,IAAI,CAACa,GAAG,KAAK,WAAW,EAAE;MACjC,IAAI,CAACV,SAAS,CAACI,KAAK,CAACM,GAAG,GAAG,KAAK;IACpC;IACA,IAAI,OAAOb,IAAI,CAACc,IAAI,KAAK,WAAW,EAAE;MAClC,IAAI,CAACX,SAAS,CAACI,KAAK,CAACO,IAAI,GAAG,KAAK;IACrC;IACA,IAAI,OAAOd,IAAI,CAACe,MAAM,KAAK,WAAW,EAAE;MACpC,IAAI,CAACZ,SAAS,CAACI,KAAK,CAACQ,MAAM,GAAG,KAAK;IACvC;IACA,IAAI,OAAOf,IAAI,CAACgB,KAAK,KAAK,WAAW,EAAE;MACnC,IAAI,CAACb,SAAS,CAACI,KAAK,CAACS,KAAK,GAAG,KAAK;IACtC;IACA,IAAI,CAACC,OAAO,GAAGb,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC5C,IAAI,CAACY,OAAO,CAACX,SAAS,GAAGN,IAAI,CAACM,SAAS;IACvC,IAAI,CAACW,OAAO,CAACC,SAAS,CAACC,GAAG,CAAC,GAAGxB,SAAS,CAACyB,gBAAgB,CAACpB,IAAI,CAACqB,IAAI,CAAC,CAAC;IACpE,IAAI,CAACJ,OAAO,CAACV,KAAK,CAACC,QAAQ,GAAG,UAAU;IACxC,IAAI,CAACS,OAAO,CAACV,KAAK,CAACE,KAAK,GAAGZ,cAAc,GAAG,IAAI;IAChD,IAAI,CAACoB,OAAO,CAACV,KAAK,CAACI,MAAM,GAAGd,cAAc,GAAG,IAAI;IACjD,IAAI,OAAOG,IAAI,CAACa,GAAG,KAAK,WAAW,EAAE;MACjC,IAAI,CAACI,OAAO,CAACV,KAAK,CAACM,GAAG,GAAGb,IAAI,CAACa,GAAG,GAAG,IAAI;IAC5C;IACA,IAAI,OAAOb,IAAI,CAACc,IAAI,KAAK,WAAW,EAAE;MAClC,IAAI,CAACG,OAAO,CAACV,KAAK,CAACO,IAAI,GAAGd,IAAI,CAACc,IAAI,GAAG,IAAI;IAC9C;IACA,IAAI,OAAOd,IAAI,CAACe,MAAM,KAAK,WAAW,EAAE;MACpC,IAAI,CAACE,OAAO,CAACV,KAAK,CAACQ,MAAM,GAAGf,IAAI,CAACe,MAAM,GAAG,IAAI;IAClD;IACA,IAAI,OAAOf,IAAI,CAACgB,KAAK,KAAK,WAAW,EAAE;MACnC,IAAI,CAACC,OAAO,CAACV,KAAK,CAACS,KAAK,GAAGhB,IAAI,CAACgB,KAAK,GAAG,IAAI;IAChD;IACA,IAAI,CAACM,mBAAmB,GAAG,IAAI,CAACC,SAAS,CAAC,IAAI/B,wBAAwB,CAAC,CAAC,CAAC;IACzE,IAAI,CAAC+B,SAAS,CAAC3B,GAAG,CAAC4B,6BAA6B,CAAC,IAAI,CAACrB,SAAS,EAAEP,GAAG,CAAC6B,SAAS,CAACC,YAAY,EAAGC,CAAC,IAAK,IAAI,CAACC,iBAAiB,CAACD,CAAC,CAAC,CAAC,CAAC;IAC/H,IAAI,CAACJ,SAAS,CAAC3B,GAAG,CAAC4B,6BAA6B,CAAC,IAAI,CAACP,OAAO,EAAErB,GAAG,CAAC6B,SAAS,CAACC,YAAY,EAAGC,CAAC,IAAK,IAAI,CAACC,iBAAiB,CAACD,CAAC,CAAC,CAAC,CAAC;IAC7H,IAAI,CAACE,uBAAuB,GAAG,IAAI,CAACN,SAAS,CAAC,IAAI3B,GAAG,CAACkC,mBAAmB,CAAC,CAAC,CAAC;IAC5E,IAAI,CAACC,+BAA+B,GAAG,IAAI,CAACR,SAAS,CAAC,IAAI7B,YAAY,CAAC,CAAC,CAAC;EAC7E;EACAkC,iBAAiBA,CAACD,CAAC,EAAE;IACjB,IAAI,CAACA,CAAC,CAACK,MAAM,IAAI,EAAEL,CAAC,CAACK,MAAM,YAAYC,OAAO,CAAC,EAAE;MAC7C;IACJ;IACA,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;MAC3B,IAAI,CAACL,uBAAuB,CAACM,YAAY,CAAC,MAAM,IAAI,CAAClC,WAAW,CAAC,CAAC,EAAE,IAAI,GAAG,EAAE,EAAEL,GAAG,CAACwC,SAAS,CAACT,CAAC,CAAC,CAAC;IACpG,CAAC;IACD,IAAI,CAAC1B,WAAW,CAAC,CAAC;IAClB,IAAI,CAAC4B,uBAAuB,CAACQ,MAAM,CAAC,CAAC;IACrC,IAAI,CAACN,+BAA+B,CAACI,YAAY,CAACD,gBAAgB,EAAE,GAAG,CAAC;IACxE,IAAI,CAACZ,mBAAmB,CAACgB,eAAe,CAACX,CAAC,CAACK,MAAM,EAAEL,CAAC,CAACY,SAAS,EAAEZ,CAAC,CAACa,OAAO,EAAGC,eAAe,IAAK,CAAE,CAAC,EAAE,MAAM;MACvG,IAAI,CAACZ,uBAAuB,CAACQ,MAAM,CAAC,CAAC;MACrC,IAAI,CAACN,+BAA+B,CAACM,MAAM,CAAC,CAAC;IACjD,CAAC,CAAC;IACFV,CAAC,CAACe,cAAc,CAAC,CAAC;EACtB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}