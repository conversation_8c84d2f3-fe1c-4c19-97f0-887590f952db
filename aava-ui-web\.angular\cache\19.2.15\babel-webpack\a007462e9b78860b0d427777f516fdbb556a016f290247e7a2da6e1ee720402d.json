{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as dom from '../../../base/browser/dom.js';\nimport { StandardWheelEvent } from '../../../base/browser/mouseEvent.js';\nimport { Disposable } from '../../../base/common/lifecycle.js';\nimport * as platform from '../../../base/common/platform.js';\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, MouseTargetFactory } from './mouseTarget.js';\nimport { ClientCoordinates, EditorM<PERSON>Event, EditorMouseEventFactory, GlobalEditorPointerMoveMonitor, createEditorPagePosition, createCoordinatesRelativeToEditor, PageCoordinates } from '../editorDom.js';\nimport { EditorZoom } from '../../common/config/editorZoom.js';\nimport { Position } from '../../common/core/position.js';\nimport { Selection } from '../../common/core/selection.js';\nimport { ViewEventHandler } from '../../common/viewEventHandler.js';\nimport { MouseWheelClassifier } from '../../../base/browser/ui/scrollbar/scrollableElement.js';\nexport class MouseHandler extends ViewEventHandler {\n  constructor(context, viewController, viewHelper) {\n    super();\n    this._mouseLeaveMonitor = null;\n    this._context = context;\n    this.viewController = viewController;\n    this.viewHelper = viewHelper;\n    this.mouseTargetFactory = new MouseTargetFactory(this._context, viewHelper);\n    this._mouseDownOperation = this._register(new MouseDownOperation(this._context, this.viewController, this.viewHelper, this.mouseTargetFactory, (e, testEventTarget) => this._createMouseTarget(e, testEventTarget), e => this._getMouseColumn(e)));\n    this.lastMouseLeaveTime = -1;\n    this._height = this._context.configuration.options.get(146 /* EditorOption.layoutInfo */).height;\n    const mouseEvents = new EditorMouseEventFactory(this.viewHelper.viewDomNode);\n    this._register(mouseEvents.onContextMenu(this.viewHelper.viewDomNode, e => this._onContextMenu(e, true)));\n    this._register(mouseEvents.onMouseMove(this.viewHelper.viewDomNode, e => {\n      this._onMouseMove(e);\n      // See https://github.com/microsoft/vscode/issues/138789\n      // When moving the mouse really quickly, the browser sometimes forgets to\n      // send us a `mouseleave` or `mouseout` event. We therefore install here\n      // a global `mousemove` listener to manually recover if the mouse goes outside\n      // the editor. As soon as the mouse leaves outside of the editor, we\n      // remove this listener\n      if (!this._mouseLeaveMonitor) {\n        this._mouseLeaveMonitor = dom.addDisposableListener(this.viewHelper.viewDomNode.ownerDocument, 'mousemove', e => {\n          if (!this.viewHelper.viewDomNode.contains(e.target)) {\n            // went outside the editor!\n            this._onMouseLeave(new EditorMouseEvent(e, false, this.viewHelper.viewDomNode));\n          }\n        });\n      }\n    }));\n    this._register(mouseEvents.onMouseUp(this.viewHelper.viewDomNode, e => this._onMouseUp(e)));\n    this._register(mouseEvents.onMouseLeave(this.viewHelper.viewDomNode, e => this._onMouseLeave(e)));\n    // `pointerdown` events can't be used to determine if there's a double click, or triple click\n    // because their `e.detail` is always 0.\n    // We will therefore save the pointer id for the mouse and then reuse it in the `mousedown` event\n    // for `element.setPointerCapture`.\n    let capturePointerId = 0;\n    this._register(mouseEvents.onPointerDown(this.viewHelper.viewDomNode, (e, pointerId) => {\n      capturePointerId = pointerId;\n    }));\n    // The `pointerup` listener registered by `GlobalEditorPointerMoveMonitor` does not get invoked 100% of the times.\n    // I speculate that this is because the `pointerup` listener is only registered during the `mousedown` event, and perhaps\n    // the `pointerup` event is already queued for dispatching, which makes it that the new listener doesn't get fired.\n    // See https://github.com/microsoft/vscode/issues/146486 for repro steps.\n    // To compensate for that, we simply register here a `pointerup` listener and just communicate it.\n    this._register(dom.addDisposableListener(this.viewHelper.viewDomNode, dom.EventType.POINTER_UP, e => {\n      this._mouseDownOperation.onPointerUp();\n    }));\n    this._register(mouseEvents.onMouseDown(this.viewHelper.viewDomNode, e => this._onMouseDown(e, capturePointerId)));\n    this._setupMouseWheelZoomListener();\n    this._context.addEventHandler(this);\n  }\n  _setupMouseWheelZoomListener() {\n    const classifier = MouseWheelClassifier.INSTANCE;\n    let prevMouseWheelTime = 0;\n    let gestureStartZoomLevel = EditorZoom.getZoomLevel();\n    let gestureHasZoomModifiers = false;\n    let gestureAccumulatedDelta = 0;\n    const onMouseWheel = browserEvent => {\n      this.viewController.emitMouseWheel(browserEvent);\n      if (!this._context.configuration.options.get(76 /* EditorOption.mouseWheelZoom */)) {\n        return;\n      }\n      const e = new StandardWheelEvent(browserEvent);\n      classifier.acceptStandardWheelEvent(e);\n      if (classifier.isPhysicalMouseWheel()) {\n        if (hasMouseWheelZoomModifiers(browserEvent)) {\n          const zoomLevel = EditorZoom.getZoomLevel();\n          const delta = e.deltaY > 0 ? 1 : -1;\n          EditorZoom.setZoomLevel(zoomLevel + delta);\n          e.preventDefault();\n          e.stopPropagation();\n        }\n      } else {\n        // we consider mousewheel events that occur within 50ms of each other to be part of the same gesture\n        // we don't want to consider mouse wheel events where ctrl/cmd is pressed during the inertia phase\n        // we also want to accumulate deltaY values from the same gesture and use that to set the zoom level\n        if (Date.now() - prevMouseWheelTime > 50) {\n          // reset if more than 50ms have passed\n          gestureStartZoomLevel = EditorZoom.getZoomLevel();\n          gestureHasZoomModifiers = hasMouseWheelZoomModifiers(browserEvent);\n          gestureAccumulatedDelta = 0;\n        }\n        prevMouseWheelTime = Date.now();\n        gestureAccumulatedDelta += e.deltaY;\n        if (gestureHasZoomModifiers) {\n          EditorZoom.setZoomLevel(gestureStartZoomLevel + gestureAccumulatedDelta / 5);\n          e.preventDefault();\n          e.stopPropagation();\n        }\n      }\n    };\n    this._register(dom.addDisposableListener(this.viewHelper.viewDomNode, dom.EventType.MOUSE_WHEEL, onMouseWheel, {\n      capture: true,\n      passive: false\n    }));\n    function hasMouseWheelZoomModifiers(browserEvent) {\n      return platform.isMacintosh\n      // on macOS we support cmd + two fingers scroll (`metaKey` set)\n      // and also the two fingers pinch gesture (`ctrKey` set)\n      ? (browserEvent.metaKey || browserEvent.ctrlKey) && !browserEvent.shiftKey && !browserEvent.altKey : browserEvent.ctrlKey && !browserEvent.metaKey && !browserEvent.shiftKey && !browserEvent.altKey;\n    }\n  }\n  dispose() {\n    this._context.removeEventHandler(this);\n    if (this._mouseLeaveMonitor) {\n      this._mouseLeaveMonitor.dispose();\n      this._mouseLeaveMonitor = null;\n    }\n    super.dispose();\n  }\n  // --- begin event handlers\n  onConfigurationChanged(e) {\n    if (e.hasChanged(146 /* EditorOption.layoutInfo */)) {\n      // layout change\n      const height = this._context.configuration.options.get(146 /* EditorOption.layoutInfo */).height;\n      if (this._height !== height) {\n        this._height = height;\n        this._mouseDownOperation.onHeightChanged();\n      }\n    }\n    return false;\n  }\n  onCursorStateChanged(e) {\n    this._mouseDownOperation.onCursorStateChanged(e);\n    return false;\n  }\n  onFocusChanged(e) {\n    return false;\n  }\n  // --- end event handlers\n  getTargetAtClientPoint(clientX, clientY) {\n    const clientPos = new ClientCoordinates(clientX, clientY);\n    const pos = clientPos.toPageCoordinates(dom.getWindow(this.viewHelper.viewDomNode));\n    const editorPos = createEditorPagePosition(this.viewHelper.viewDomNode);\n    if (pos.y < editorPos.y || pos.y > editorPos.y + editorPos.height || pos.x < editorPos.x || pos.x > editorPos.x + editorPos.width) {\n      return null;\n    }\n    const relativePos = createCoordinatesRelativeToEditor(this.viewHelper.viewDomNode, editorPos, pos);\n    return this.mouseTargetFactory.createMouseTarget(this.viewHelper.getLastRenderData(), editorPos, pos, relativePos, null);\n  }\n  _createMouseTarget(e, testEventTarget) {\n    let target = e.target;\n    if (!this.viewHelper.viewDomNode.contains(target)) {\n      const shadowRoot = dom.getShadowRoot(this.viewHelper.viewDomNode);\n      if (shadowRoot) {\n        target = shadowRoot.elementsFromPoint(e.posx, e.posy).find(el => this.viewHelper.viewDomNode.contains(el));\n      }\n    }\n    return this.mouseTargetFactory.createMouseTarget(this.viewHelper.getLastRenderData(), e.editorPos, e.pos, e.relativePos, testEventTarget ? target : null);\n  }\n  _getMouseColumn(e) {\n    return this.mouseTargetFactory.getMouseColumn(e.relativePos);\n  }\n  _onContextMenu(e, testEventTarget) {\n    this.viewController.emitContextMenu({\n      event: e,\n      target: this._createMouseTarget(e, testEventTarget)\n    });\n  }\n  _onMouseMove(e) {\n    const targetIsWidget = this.mouseTargetFactory.mouseTargetIsWidget(e);\n    if (!targetIsWidget) {\n      e.preventDefault();\n    }\n    if (this._mouseDownOperation.isActive()) {\n      // In selection/drag operation\n      return;\n    }\n    const actualMouseMoveTime = e.timestamp;\n    if (actualMouseMoveTime < this.lastMouseLeaveTime) {\n      // Due to throttling, this event occurred before the mouse left the editor, therefore ignore it.\n      return;\n    }\n    this.viewController.emitMouseMove({\n      event: e,\n      target: this._createMouseTarget(e, true)\n    });\n  }\n  _onMouseLeave(e) {\n    if (this._mouseLeaveMonitor) {\n      this._mouseLeaveMonitor.dispose();\n      this._mouseLeaveMonitor = null;\n    }\n    this.lastMouseLeaveTime = new Date().getTime();\n    this.viewController.emitMouseLeave({\n      event: e,\n      target: null\n    });\n  }\n  _onMouseUp(e) {\n    this.viewController.emitMouseUp({\n      event: e,\n      target: this._createMouseTarget(e, true)\n    });\n  }\n  _onMouseDown(e, pointerId) {\n    const t = this._createMouseTarget(e, true);\n    const targetIsContent = t.type === 6 /* MouseTargetType.CONTENT_TEXT */ || t.type === 7 /* MouseTargetType.CONTENT_EMPTY */;\n    const targetIsGutter = t.type === 2 /* MouseTargetType.GUTTER_GLYPH_MARGIN */ || t.type === 3 /* MouseTargetType.GUTTER_LINE_NUMBERS */ || t.type === 4 /* MouseTargetType.GUTTER_LINE_DECORATIONS */;\n    const targetIsLineNumbers = t.type === 3 /* MouseTargetType.GUTTER_LINE_NUMBERS */;\n    const selectOnLineNumbers = this._context.configuration.options.get(110 /* EditorOption.selectOnLineNumbers */);\n    const targetIsViewZone = t.type === 8 /* MouseTargetType.CONTENT_VIEW_ZONE */ || t.type === 5 /* MouseTargetType.GUTTER_VIEW_ZONE */;\n    const targetIsWidget = t.type === 9 /* MouseTargetType.CONTENT_WIDGET */;\n    let shouldHandle = e.leftButton || e.middleButton;\n    if (platform.isMacintosh && e.leftButton && e.ctrlKey) {\n      shouldHandle = false;\n    }\n    const focus = () => {\n      e.preventDefault();\n      this.viewHelper.focusTextArea();\n    };\n    if (shouldHandle && (targetIsContent || targetIsLineNumbers && selectOnLineNumbers)) {\n      focus();\n      this._mouseDownOperation.start(t.type, e, pointerId);\n    } else if (targetIsGutter) {\n      // Do not steal focus\n      e.preventDefault();\n    } else if (targetIsViewZone) {\n      const viewZoneData = t.detail;\n      if (shouldHandle && this.viewHelper.shouldSuppressMouseDownOnViewZone(viewZoneData.viewZoneId)) {\n        focus();\n        this._mouseDownOperation.start(t.type, e, pointerId);\n        e.preventDefault();\n      }\n    } else if (targetIsWidget && this.viewHelper.shouldSuppressMouseDownOnWidget(t.detail)) {\n      focus();\n      e.preventDefault();\n    }\n    this.viewController.emitMouseDown({\n      event: e,\n      target: t\n    });\n  }\n}\nclass MouseDownOperation extends Disposable {\n  constructor(_context, _viewController, _viewHelper, _mouseTargetFactory, createMouseTarget, getMouseColumn) {\n    super();\n    this._context = _context;\n    this._viewController = _viewController;\n    this._viewHelper = _viewHelper;\n    this._mouseTargetFactory = _mouseTargetFactory;\n    this._createMouseTarget = createMouseTarget;\n    this._getMouseColumn = getMouseColumn;\n    this._mouseMoveMonitor = this._register(new GlobalEditorPointerMoveMonitor(this._viewHelper.viewDomNode));\n    this._topBottomDragScrolling = this._register(new TopBottomDragScrolling(this._context, this._viewHelper, this._mouseTargetFactory, (position, inSelectionMode, revealType) => this._dispatchMouse(position, inSelectionMode, revealType)));\n    this._mouseState = new MouseDownState();\n    this._currentSelection = new Selection(1, 1, 1, 1);\n    this._isActive = false;\n    this._lastMouseEvent = null;\n  }\n  dispose() {\n    super.dispose();\n  }\n  isActive() {\n    return this._isActive;\n  }\n  _onMouseDownThenMove(e) {\n    this._lastMouseEvent = e;\n    this._mouseState.setModifiers(e);\n    const position = this._findMousePosition(e, false);\n    if (!position) {\n      // Ignoring because position is unknown\n      return;\n    }\n    if (this._mouseState.isDragAndDrop) {\n      this._viewController.emitMouseDrag({\n        event: e,\n        target: position\n      });\n    } else {\n      if (position.type === 13 /* MouseTargetType.OUTSIDE_EDITOR */ && (position.outsidePosition === 'above' || position.outsidePosition === 'below')) {\n        this._topBottomDragScrolling.start(position, e);\n      } else {\n        this._topBottomDragScrolling.stop();\n        this._dispatchMouse(position, true, 1 /* NavigationCommandRevealType.Minimal */);\n      }\n    }\n  }\n  start(targetType, e, pointerId) {\n    this._lastMouseEvent = e;\n    this._mouseState.setStartedOnLineNumbers(targetType === 3 /* MouseTargetType.GUTTER_LINE_NUMBERS */);\n    this._mouseState.setStartButtons(e);\n    this._mouseState.setModifiers(e);\n    const position = this._findMousePosition(e, true);\n    if (!position || !position.position) {\n      // Ignoring because position is unknown\n      return;\n    }\n    this._mouseState.trySetCount(e.detail, position.position);\n    // Overwrite the detail of the MouseEvent, as it will be sent out in an event and contributions might rely on it.\n    e.detail = this._mouseState.count;\n    const options = this._context.configuration.options;\n    if (!options.get(92 /* EditorOption.readOnly */) && options.get(35 /* EditorOption.dragAndDrop */) && !options.get(22 /* EditorOption.columnSelection */) && !this._mouseState.altKey // we don't support multiple mouse\n    && e.detail < 2 // only single click on a selection can work\n    && !this._isActive // the mouse is not down yet\n    && !this._currentSelection.isEmpty() // we don't drag single cursor\n    && position.type === 6 /* MouseTargetType.CONTENT_TEXT */ // single click on text\n    && position.position && this._currentSelection.containsPosition(position.position) // single click on a selection\n    ) {\n      this._mouseState.isDragAndDrop = true;\n      this._isActive = true;\n      this._mouseMoveMonitor.startMonitoring(this._viewHelper.viewLinesDomNode, pointerId, e.buttons, e => this._onMouseDownThenMove(e), browserEvent => {\n        const position = this._findMousePosition(this._lastMouseEvent, false);\n        if (dom.isKeyboardEvent(browserEvent)) {\n          // cancel\n          this._viewController.emitMouseDropCanceled();\n        } else {\n          this._viewController.emitMouseDrop({\n            event: this._lastMouseEvent,\n            target: position ? this._createMouseTarget(this._lastMouseEvent, true) : null // Ignoring because position is unknown, e.g., Content View Zone\n          });\n        }\n        this._stop();\n      });\n      return;\n    }\n    this._mouseState.isDragAndDrop = false;\n    this._dispatchMouse(position, e.shiftKey, 1 /* NavigationCommandRevealType.Minimal */);\n    if (!this._isActive) {\n      this._isActive = true;\n      this._mouseMoveMonitor.startMonitoring(this._viewHelper.viewLinesDomNode, pointerId, e.buttons, e => this._onMouseDownThenMove(e), () => this._stop());\n    }\n  }\n  _stop() {\n    this._isActive = false;\n    this._topBottomDragScrolling.stop();\n  }\n  onHeightChanged() {\n    this._mouseMoveMonitor.stopMonitoring();\n  }\n  onPointerUp() {\n    this._mouseMoveMonitor.stopMonitoring();\n  }\n  onCursorStateChanged(e) {\n    this._currentSelection = e.selections[0];\n  }\n  _getPositionOutsideEditor(e) {\n    const editorContent = e.editorPos;\n    const model = this._context.viewModel;\n    const viewLayout = this._context.viewLayout;\n    const mouseColumn = this._getMouseColumn(e);\n    if (e.posy < editorContent.y) {\n      const outsideDistance = editorContent.y - e.posy;\n      const verticalOffset = Math.max(viewLayout.getCurrentScrollTop() - outsideDistance, 0);\n      const viewZoneData = HitTestContext.getZoneAtCoord(this._context, verticalOffset);\n      if (viewZoneData) {\n        const newPosition = this._helpPositionJumpOverViewZone(viewZoneData);\n        if (newPosition) {\n          return MouseTarget.createOutsideEditor(mouseColumn, newPosition, 'above', outsideDistance);\n        }\n      }\n      const aboveLineNumber = viewLayout.getLineNumberAtVerticalOffset(verticalOffset);\n      return MouseTarget.createOutsideEditor(mouseColumn, new Position(aboveLineNumber, 1), 'above', outsideDistance);\n    }\n    if (e.posy > editorContent.y + editorContent.height) {\n      const outsideDistance = e.posy - editorContent.y - editorContent.height;\n      const verticalOffset = viewLayout.getCurrentScrollTop() + e.relativePos.y;\n      const viewZoneData = HitTestContext.getZoneAtCoord(this._context, verticalOffset);\n      if (viewZoneData) {\n        const newPosition = this._helpPositionJumpOverViewZone(viewZoneData);\n        if (newPosition) {\n          return MouseTarget.createOutsideEditor(mouseColumn, newPosition, 'below', outsideDistance);\n        }\n      }\n      const belowLineNumber = viewLayout.getLineNumberAtVerticalOffset(verticalOffset);\n      return MouseTarget.createOutsideEditor(mouseColumn, new Position(belowLineNumber, model.getLineMaxColumn(belowLineNumber)), 'below', outsideDistance);\n    }\n    const possibleLineNumber = viewLayout.getLineNumberAtVerticalOffset(viewLayout.getCurrentScrollTop() + e.relativePos.y);\n    if (e.posx < editorContent.x) {\n      const outsideDistance = editorContent.x - e.posx;\n      return MouseTarget.createOutsideEditor(mouseColumn, new Position(possibleLineNumber, 1), 'left', outsideDistance);\n    }\n    if (e.posx > editorContent.x + editorContent.width) {\n      const outsideDistance = e.posx - editorContent.x - editorContent.width;\n      return MouseTarget.createOutsideEditor(mouseColumn, new Position(possibleLineNumber, model.getLineMaxColumn(possibleLineNumber)), 'right', outsideDistance);\n    }\n    return null;\n  }\n  _findMousePosition(e, testEventTarget) {\n    const positionOutsideEditor = this._getPositionOutsideEditor(e);\n    if (positionOutsideEditor) {\n      return positionOutsideEditor;\n    }\n    const t = this._createMouseTarget(e, testEventTarget);\n    const hintedPosition = t.position;\n    if (!hintedPosition) {\n      return null;\n    }\n    if (t.type === 8 /* MouseTargetType.CONTENT_VIEW_ZONE */ || t.type === 5 /* MouseTargetType.GUTTER_VIEW_ZONE */) {\n      const newPosition = this._helpPositionJumpOverViewZone(t.detail);\n      if (newPosition) {\n        return MouseTarget.createViewZone(t.type, t.element, t.mouseColumn, newPosition, t.detail);\n      }\n    }\n    return t;\n  }\n  _helpPositionJumpOverViewZone(viewZoneData) {\n    // Force position on view zones to go above or below depending on where selection started from\n    const selectionStart = new Position(this._currentSelection.selectionStartLineNumber, this._currentSelection.selectionStartColumn);\n    const positionBefore = viewZoneData.positionBefore;\n    const positionAfter = viewZoneData.positionAfter;\n    if (positionBefore && positionAfter) {\n      if (positionBefore.isBefore(selectionStart)) {\n        return positionBefore;\n      } else {\n        return positionAfter;\n      }\n    }\n    return null;\n  }\n  _dispatchMouse(position, inSelectionMode, revealType) {\n    if (!position.position) {\n      return;\n    }\n    this._viewController.dispatchMouse({\n      position: position.position,\n      mouseColumn: position.mouseColumn,\n      startedOnLineNumbers: this._mouseState.startedOnLineNumbers,\n      revealType,\n      inSelectionMode: inSelectionMode,\n      mouseDownCount: this._mouseState.count,\n      altKey: this._mouseState.altKey,\n      ctrlKey: this._mouseState.ctrlKey,\n      metaKey: this._mouseState.metaKey,\n      shiftKey: this._mouseState.shiftKey,\n      leftButton: this._mouseState.leftButton,\n      middleButton: this._mouseState.middleButton,\n      onInjectedText: position.type === 6 /* MouseTargetType.CONTENT_TEXT */ && position.detail.injectedText !== null\n    });\n  }\n}\nclass TopBottomDragScrolling extends Disposable {\n  constructor(_context, _viewHelper, _mouseTargetFactory, _dispatchMouse) {\n    super();\n    this._context = _context;\n    this._viewHelper = _viewHelper;\n    this._mouseTargetFactory = _mouseTargetFactory;\n    this._dispatchMouse = _dispatchMouse;\n    this._operation = null;\n  }\n  dispose() {\n    super.dispose();\n    this.stop();\n  }\n  start(position, mouseEvent) {\n    if (this._operation) {\n      this._operation.setPosition(position, mouseEvent);\n    } else {\n      this._operation = new TopBottomDragScrollingOperation(this._context, this._viewHelper, this._mouseTargetFactory, this._dispatchMouse, position, mouseEvent);\n    }\n  }\n  stop() {\n    if (this._operation) {\n      this._operation.dispose();\n      this._operation = null;\n    }\n  }\n}\nclass TopBottomDragScrollingOperation extends Disposable {\n  constructor(_context, _viewHelper, _mouseTargetFactory, _dispatchMouse, position, mouseEvent) {\n    super();\n    this._context = _context;\n    this._viewHelper = _viewHelper;\n    this._mouseTargetFactory = _mouseTargetFactory;\n    this._dispatchMouse = _dispatchMouse;\n    this._position = position;\n    this._mouseEvent = mouseEvent;\n    this._lastTime = Date.now();\n    this._animationFrameDisposable = dom.scheduleAtNextAnimationFrame(dom.getWindow(mouseEvent.browserEvent), () => this._execute());\n  }\n  dispose() {\n    this._animationFrameDisposable.dispose();\n    super.dispose();\n  }\n  setPosition(position, mouseEvent) {\n    this._position = position;\n    this._mouseEvent = mouseEvent;\n  }\n  /**\n   * update internal state and return elapsed ms since last time\n   */\n  _tick() {\n    const now = Date.now();\n    const elapsed = now - this._lastTime;\n    this._lastTime = now;\n    return elapsed;\n  }\n  /**\n   * get the number of lines per second to auto-scroll\n   */\n  _getScrollSpeed() {\n    const lineHeight = this._context.configuration.options.get(67 /* EditorOption.lineHeight */);\n    const viewportInLines = this._context.configuration.options.get(146 /* EditorOption.layoutInfo */).height / lineHeight;\n    const outsideDistanceInLines = this._position.outsideDistance / lineHeight;\n    if (outsideDistanceInLines <= 1.5) {\n      return Math.max(30, viewportInLines * (1 + outsideDistanceInLines));\n    }\n    if (outsideDistanceInLines <= 3) {\n      return Math.max(60, viewportInLines * (2 + outsideDistanceInLines));\n    }\n    return Math.max(200, viewportInLines * (7 + outsideDistanceInLines));\n  }\n  _execute() {\n    const lineHeight = this._context.configuration.options.get(67 /* EditorOption.lineHeight */);\n    const scrollSpeedInLines = this._getScrollSpeed();\n    const elapsed = this._tick();\n    const scrollInPixels = scrollSpeedInLines * (elapsed / 1000) * lineHeight;\n    const scrollValue = this._position.outsidePosition === 'above' ? -scrollInPixels : scrollInPixels;\n    this._context.viewModel.viewLayout.deltaScrollNow(0, scrollValue);\n    this._viewHelper.renderNow();\n    const viewportData = this._context.viewLayout.getLinesViewportData();\n    const edgeLineNumber = this._position.outsidePosition === 'above' ? viewportData.startLineNumber : viewportData.endLineNumber;\n    // First, try to find a position that matches the horizontal position of the mouse\n    let mouseTarget;\n    {\n      const editorPos = createEditorPagePosition(this._viewHelper.viewDomNode);\n      const horizontalScrollbarHeight = this._context.configuration.options.get(146 /* EditorOption.layoutInfo */).horizontalScrollbarHeight;\n      const pos = new PageCoordinates(this._mouseEvent.pos.x, editorPos.y + editorPos.height - horizontalScrollbarHeight - 0.1);\n      const relativePos = createCoordinatesRelativeToEditor(this._viewHelper.viewDomNode, editorPos, pos);\n      mouseTarget = this._mouseTargetFactory.createMouseTarget(this._viewHelper.getLastRenderData(), editorPos, pos, relativePos, null);\n    }\n    if (!mouseTarget.position || mouseTarget.position.lineNumber !== edgeLineNumber) {\n      if (this._position.outsidePosition === 'above') {\n        mouseTarget = MouseTarget.createOutsideEditor(this._position.mouseColumn, new Position(edgeLineNumber, 1), 'above', this._position.outsideDistance);\n      } else {\n        mouseTarget = MouseTarget.createOutsideEditor(this._position.mouseColumn, new Position(edgeLineNumber, this._context.viewModel.getLineMaxColumn(edgeLineNumber)), 'below', this._position.outsideDistance);\n      }\n    }\n    this._dispatchMouse(mouseTarget, true, 2 /* NavigationCommandRevealType.None */);\n    this._animationFrameDisposable = dom.scheduleAtNextAnimationFrame(dom.getWindow(mouseTarget.element), () => this._execute());\n  }\n}\nlet MouseDownState = /*#__PURE__*/(() => {\n  class MouseDownState {\n    static {\n      this.CLEAR_MOUSE_DOWN_COUNT_TIME = 400;\n    } // ms\n    get altKey() {\n      return this._altKey;\n    }\n    get ctrlKey() {\n      return this._ctrlKey;\n    }\n    get metaKey() {\n      return this._metaKey;\n    }\n    get shiftKey() {\n      return this._shiftKey;\n    }\n    get leftButton() {\n      return this._leftButton;\n    }\n    get middleButton() {\n      return this._middleButton;\n    }\n    get startedOnLineNumbers() {\n      return this._startedOnLineNumbers;\n    }\n    constructor() {\n      this._altKey = false;\n      this._ctrlKey = false;\n      this._metaKey = false;\n      this._shiftKey = false;\n      this._leftButton = false;\n      this._middleButton = false;\n      this._startedOnLineNumbers = false;\n      this._lastMouseDownPosition = null;\n      this._lastMouseDownPositionEqualCount = 0;\n      this._lastMouseDownCount = 0;\n      this._lastSetMouseDownCountTime = 0;\n      this.isDragAndDrop = false;\n    }\n    get count() {\n      return this._lastMouseDownCount;\n    }\n    setModifiers(source) {\n      this._altKey = source.altKey;\n      this._ctrlKey = source.ctrlKey;\n      this._metaKey = source.metaKey;\n      this._shiftKey = source.shiftKey;\n    }\n    setStartButtons(source) {\n      this._leftButton = source.leftButton;\n      this._middleButton = source.middleButton;\n    }\n    setStartedOnLineNumbers(startedOnLineNumbers) {\n      this._startedOnLineNumbers = startedOnLineNumbers;\n    }\n    trySetCount(setMouseDownCount, newMouseDownPosition) {\n      // a. Invalidate multiple clicking if too much time has passed (will be hit by IE because the detail field of mouse events contains garbage in IE10)\n      const currentTime = new Date().getTime();\n      if (currentTime - this._lastSetMouseDownCountTime > MouseDownState.CLEAR_MOUSE_DOWN_COUNT_TIME) {\n        setMouseDownCount = 1;\n      }\n      this._lastSetMouseDownCountTime = currentTime;\n      // b. Ensure that we don't jump from single click to triple click in one go (will be hit by IE because the detail field of mouse events contains garbage in IE10)\n      if (setMouseDownCount > this._lastMouseDownCount + 1) {\n        setMouseDownCount = this._lastMouseDownCount + 1;\n      }\n      // c. Invalidate multiple clicking if the logical position is different\n      if (this._lastMouseDownPosition && this._lastMouseDownPosition.equals(newMouseDownPosition)) {\n        this._lastMouseDownPositionEqualCount++;\n      } else {\n        this._lastMouseDownPositionEqualCount = 1;\n      }\n      this._lastMouseDownPosition = newMouseDownPosition;\n      // Finally set the lastMouseDownCount\n      this._lastMouseDownCount = Math.min(setMouseDownCount, this._lastMouseDownPositionEqualCount);\n    }\n  }\n  return MouseDownState;\n})();", "map": {"version": 3, "names": ["dom", "StandardWheelEvent", "Disposable", "platform", "HitTestContext", "<PERSON><PERSON><PERSON><PERSON>", "MouseTargetFactory", "ClientCoordinates", "EditorMouseEvent", "EditorMouseEventFactory", "GlobalEditorPointerMoveMonitor", "createEditorPagePosition", "createCoordinatesRelativeToEditor", "PageCoordinates", "EditorZoom", "Position", "Selection", "ViewEventHandler", "MouseWheelClassifier", "<PERSON><PERSON><PERSON><PERSON>", "constructor", "context", "viewController", "viewHelper", "_mouseLeaveMonitor", "_context", "mouseTargetFactory", "_mouseDownOperation", "_register", "MouseDownOperation", "e", "testEventTarget", "_createMouseTarget", "_getMouseColumn", "lastMouseLeaveTime", "_height", "configuration", "options", "get", "height", "mouseEvents", "viewDomNode", "onContextMenu", "_onContextMenu", "onMouseMove", "_onMouseMove", "addDisposableListener", "ownerDocument", "contains", "target", "_onMouseLeave", "onMouseUp", "_onMouseUp", "onMouseLeave", "capturePointerId", "onPointerDown", "pointerId", "EventType", "POINTER_UP", "onPointerUp", "onMouseDown", "_onMouseDown", "_setupMouseWheelZoomListener", "addEventHandler", "classifier", "INSTANCE", "prevMouseWheelTime", "gestureStartZoomLevel", "getZoomLevel", "gestureHasZoomModifiers", "gestureAccumulated<PERSON><PERSON><PERSON>", "onMouseWheel", "browserEvent", "emitMouseWheel", "acceptStandardWheelEvent", "isPhysicalMouseWheel", "hasMouseWheelZoomModifiers", "zoomLevel", "delta", "deltaY", "setZoomLevel", "preventDefault", "stopPropagation", "Date", "now", "MOUSE_WHEEL", "capture", "passive", "isMacintosh", "metaKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "altKey", "dispose", "removeEventHandler", "onConfigurationChanged", "has<PERSON><PERSON>ed", "onHeightChanged", "onCursorStateChanged", "onFocusChanged", "getTargetAtClientPoint", "clientX", "clientY", "clientPos", "pos", "toPageCoordinates", "getWindow", "editor<PERSON><PERSON>", "y", "x", "width", "relativePos", "createMouseTarget", "getLastRenderData", "shadowRoot", "getShadowRoot", "elementsFromPoint", "posx", "posy", "find", "el", "getMouseColumn", "emitContextMenu", "event", "targetIsWidget", "mouseTargetIsWidget", "isActive", "actualMouseMoveTime", "timestamp", "emitMouseMove", "getTime", "emitMouseLeave", "emitMouseUp", "t", "targetIsContent", "type", "targetIsGutter", "targetIsLineNumbers", "selectOnLineNumbers", "targetIsViewZone", "<PERSON><PERSON><PERSON><PERSON>", "leftButton", "middleButton", "focus", "focusTextArea", "start", "viewZoneData", "detail", "shouldSuppressMouseDownOnViewZone", "viewZoneId", "shouldSuppressMouseDownOnWidget", "emitMouseDown", "_viewController", "_viewHelper", "_mouseTargetFactory", "_mouseMoveMonitor", "_topBottomDragScrolling", "TopBottomDragScrolling", "position", "inSelectionMode", "revealType", "_dispatchMouse", "_mouseState", "MouseDownState", "_currentSelection", "_isActive", "_lastMouseEvent", "_onMouseDownThenMove", "setModifiers", "_findMousePosition", "isDragAndDrop", "emitMouseDrag", "outsidePosition", "stop", "targetType", "setStartedOnLineNumbers", "setStartButtons", "trySetCount", "count", "isEmpty", "containsPosition", "startMonitoring", "viewLinesDomNode", "buttons", "isKeyboardEvent", "emitMouseDropCanceled", "emitMouseDrop", "_stop", "stopMonitoring", "selections", "_getPositionOutsideEditor", "<PERSON><PERSON><PERSON><PERSON>", "model", "viewModel", "viewLayout", "mouseColumn", "outsideDistance", "verticalOffset", "Math", "max", "getCurrentScrollTop", "getZoneAtCoord", "newPosition", "_helpPositionJumpOverViewZone", "createOutsideEditor", "aboveLineNumber", "getLineNumberAtVerticalOffset", "belowLineNumber", "getLineMaxColumn", "possibleLineNumber", "positionOutsideEditor", "hintedPosition", "createViewZone", "element", "selectionStart", "selectionStartLineNumber", "selectionStartColumn", "positionBefore", "positionAfter", "isBefore", "dispatchMouse", "startedOnLineNumbers", "mouseDownCount", "onInjectedText", "injectedText", "_operation", "mouseEvent", "setPosition", "TopBottomDragScrollingOperation", "_position", "_mouseEvent", "_lastTime", "_animationFrameDisposable", "scheduleAtNextAnimationFrame", "_execute", "_tick", "elapsed", "_getScrollSpeed", "lineHeight", "viewportInLines", "outsideDistanceInLines", "scrollSpeedInLines", "scrollInPixels", "scrollValue", "deltaScrollNow", "renderNow", "viewportData", "getLinesViewportData", "edgeLineNumber", "startLineNumber", "endLineNumber", "mouse<PERSON>arget", "horizontalScrollbarHeight", "lineNumber", "CLEAR_MOUSE_DOWN_COUNT_TIME", "_altKey", "_ctrl<PERSON>ey", "_metaKey", "_shift<PERSON>ey", "_leftButton", "_middle<PERSON><PERSON>on", "_startedOnLineNumbers", "_lastMouseDownPosition", "_lastMouseDownPositionEqualCount", "_lastMouseDownCount", "_lastSetMouseDownCountTime", "source", "setMouseDownCount", "newMouseDownPosition", "currentTime", "equals", "min"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/browser/controller/mouseHandler.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as dom from '../../../base/browser/dom.js';\nimport { StandardWheelEvent } from '../../../base/browser/mouseEvent.js';\nimport { Disposable } from '../../../base/common/lifecycle.js';\nimport * as platform from '../../../base/common/platform.js';\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, MouseTargetFactory } from './mouseTarget.js';\nimport { ClientCoordinates, EditorM<PERSON>Event, EditorMouseEventFactory, GlobalEditorPointerMoveMonitor, createEditorPagePosition, createCoordinatesRelativeToEditor, PageCoordinates } from '../editorDom.js';\nimport { EditorZoom } from '../../common/config/editorZoom.js';\nimport { Position } from '../../common/core/position.js';\nimport { Selection } from '../../common/core/selection.js';\nimport { ViewEventHandler } from '../../common/viewEventHandler.js';\nimport { MouseWheelClassifier } from '../../../base/browser/ui/scrollbar/scrollableElement.js';\nexport class MouseHandler extends ViewEventHandler {\n    constructor(context, viewController, viewHelper) {\n        super();\n        this._mouseLeaveMonitor = null;\n        this._context = context;\n        this.viewController = viewController;\n        this.viewHelper = viewHelper;\n        this.mouseTargetFactory = new MouseTargetFactory(this._context, viewHelper);\n        this._mouseDownOperation = this._register(new MouseDownOperation(this._context, this.viewController, this.viewHelper, this.mouseTargetFactory, (e, testEventTarget) => this._createMouseTarget(e, testEventTarget), (e) => this._getMouseColumn(e)));\n        this.lastMouseLeaveTime = -1;\n        this._height = this._context.configuration.options.get(146 /* EditorOption.layoutInfo */).height;\n        const mouseEvents = new EditorMouseEventFactory(this.viewHelper.viewDomNode);\n        this._register(mouseEvents.onContextMenu(this.viewHelper.viewDomNode, (e) => this._onContextMenu(e, true)));\n        this._register(mouseEvents.onMouseMove(this.viewHelper.viewDomNode, (e) => {\n            this._onMouseMove(e);\n            // See https://github.com/microsoft/vscode/issues/138789\n            // When moving the mouse really quickly, the browser sometimes forgets to\n            // send us a `mouseleave` or `mouseout` event. We therefore install here\n            // a global `mousemove` listener to manually recover if the mouse goes outside\n            // the editor. As soon as the mouse leaves outside of the editor, we\n            // remove this listener\n            if (!this._mouseLeaveMonitor) {\n                this._mouseLeaveMonitor = dom.addDisposableListener(this.viewHelper.viewDomNode.ownerDocument, 'mousemove', (e) => {\n                    if (!this.viewHelper.viewDomNode.contains(e.target)) {\n                        // went outside the editor!\n                        this._onMouseLeave(new EditorMouseEvent(e, false, this.viewHelper.viewDomNode));\n                    }\n                });\n            }\n        }));\n        this._register(mouseEvents.onMouseUp(this.viewHelper.viewDomNode, (e) => this._onMouseUp(e)));\n        this._register(mouseEvents.onMouseLeave(this.viewHelper.viewDomNode, (e) => this._onMouseLeave(e)));\n        // `pointerdown` events can't be used to determine if there's a double click, or triple click\n        // because their `e.detail` is always 0.\n        // We will therefore save the pointer id for the mouse and then reuse it in the `mousedown` event\n        // for `element.setPointerCapture`.\n        let capturePointerId = 0;\n        this._register(mouseEvents.onPointerDown(this.viewHelper.viewDomNode, (e, pointerId) => {\n            capturePointerId = pointerId;\n        }));\n        // The `pointerup` listener registered by `GlobalEditorPointerMoveMonitor` does not get invoked 100% of the times.\n        // I speculate that this is because the `pointerup` listener is only registered during the `mousedown` event, and perhaps\n        // the `pointerup` event is already queued for dispatching, which makes it that the new listener doesn't get fired.\n        // See https://github.com/microsoft/vscode/issues/146486 for repro steps.\n        // To compensate for that, we simply register here a `pointerup` listener and just communicate it.\n        this._register(dom.addDisposableListener(this.viewHelper.viewDomNode, dom.EventType.POINTER_UP, (e) => {\n            this._mouseDownOperation.onPointerUp();\n        }));\n        this._register(mouseEvents.onMouseDown(this.viewHelper.viewDomNode, (e) => this._onMouseDown(e, capturePointerId)));\n        this._setupMouseWheelZoomListener();\n        this._context.addEventHandler(this);\n    }\n    _setupMouseWheelZoomListener() {\n        const classifier = MouseWheelClassifier.INSTANCE;\n        let prevMouseWheelTime = 0;\n        let gestureStartZoomLevel = EditorZoom.getZoomLevel();\n        let gestureHasZoomModifiers = false;\n        let gestureAccumulatedDelta = 0;\n        const onMouseWheel = (browserEvent) => {\n            this.viewController.emitMouseWheel(browserEvent);\n            if (!this._context.configuration.options.get(76 /* EditorOption.mouseWheelZoom */)) {\n                return;\n            }\n            const e = new StandardWheelEvent(browserEvent);\n            classifier.acceptStandardWheelEvent(e);\n            if (classifier.isPhysicalMouseWheel()) {\n                if (hasMouseWheelZoomModifiers(browserEvent)) {\n                    const zoomLevel = EditorZoom.getZoomLevel();\n                    const delta = e.deltaY > 0 ? 1 : -1;\n                    EditorZoom.setZoomLevel(zoomLevel + delta);\n                    e.preventDefault();\n                    e.stopPropagation();\n                }\n            }\n            else {\n                // we consider mousewheel events that occur within 50ms of each other to be part of the same gesture\n                // we don't want to consider mouse wheel events where ctrl/cmd is pressed during the inertia phase\n                // we also want to accumulate deltaY values from the same gesture and use that to set the zoom level\n                if (Date.now() - prevMouseWheelTime > 50) {\n                    // reset if more than 50ms have passed\n                    gestureStartZoomLevel = EditorZoom.getZoomLevel();\n                    gestureHasZoomModifiers = hasMouseWheelZoomModifiers(browserEvent);\n                    gestureAccumulatedDelta = 0;\n                }\n                prevMouseWheelTime = Date.now();\n                gestureAccumulatedDelta += e.deltaY;\n                if (gestureHasZoomModifiers) {\n                    EditorZoom.setZoomLevel(gestureStartZoomLevel + gestureAccumulatedDelta / 5);\n                    e.preventDefault();\n                    e.stopPropagation();\n                }\n            }\n        };\n        this._register(dom.addDisposableListener(this.viewHelper.viewDomNode, dom.EventType.MOUSE_WHEEL, onMouseWheel, { capture: true, passive: false }));\n        function hasMouseWheelZoomModifiers(browserEvent) {\n            return (platform.isMacintosh\n                // on macOS we support cmd + two fingers scroll (`metaKey` set)\n                // and also the two fingers pinch gesture (`ctrKey` set)\n                ? ((browserEvent.metaKey || browserEvent.ctrlKey) && !browserEvent.shiftKey && !browserEvent.altKey)\n                : (browserEvent.ctrlKey && !browserEvent.metaKey && !browserEvent.shiftKey && !browserEvent.altKey));\n        }\n    }\n    dispose() {\n        this._context.removeEventHandler(this);\n        if (this._mouseLeaveMonitor) {\n            this._mouseLeaveMonitor.dispose();\n            this._mouseLeaveMonitor = null;\n        }\n        super.dispose();\n    }\n    // --- begin event handlers\n    onConfigurationChanged(e) {\n        if (e.hasChanged(146 /* EditorOption.layoutInfo */)) {\n            // layout change\n            const height = this._context.configuration.options.get(146 /* EditorOption.layoutInfo */).height;\n            if (this._height !== height) {\n                this._height = height;\n                this._mouseDownOperation.onHeightChanged();\n            }\n        }\n        return false;\n    }\n    onCursorStateChanged(e) {\n        this._mouseDownOperation.onCursorStateChanged(e);\n        return false;\n    }\n    onFocusChanged(e) {\n        return false;\n    }\n    // --- end event handlers\n    getTargetAtClientPoint(clientX, clientY) {\n        const clientPos = new ClientCoordinates(clientX, clientY);\n        const pos = clientPos.toPageCoordinates(dom.getWindow(this.viewHelper.viewDomNode));\n        const editorPos = createEditorPagePosition(this.viewHelper.viewDomNode);\n        if (pos.y < editorPos.y || pos.y > editorPos.y + editorPos.height || pos.x < editorPos.x || pos.x > editorPos.x + editorPos.width) {\n            return null;\n        }\n        const relativePos = createCoordinatesRelativeToEditor(this.viewHelper.viewDomNode, editorPos, pos);\n        return this.mouseTargetFactory.createMouseTarget(this.viewHelper.getLastRenderData(), editorPos, pos, relativePos, null);\n    }\n    _createMouseTarget(e, testEventTarget) {\n        let target = e.target;\n        if (!this.viewHelper.viewDomNode.contains(target)) {\n            const shadowRoot = dom.getShadowRoot(this.viewHelper.viewDomNode);\n            if (shadowRoot) {\n                target = shadowRoot.elementsFromPoint(e.posx, e.posy).find((el) => this.viewHelper.viewDomNode.contains(el));\n            }\n        }\n        return this.mouseTargetFactory.createMouseTarget(this.viewHelper.getLastRenderData(), e.editorPos, e.pos, e.relativePos, testEventTarget ? target : null);\n    }\n    _getMouseColumn(e) {\n        return this.mouseTargetFactory.getMouseColumn(e.relativePos);\n    }\n    _onContextMenu(e, testEventTarget) {\n        this.viewController.emitContextMenu({\n            event: e,\n            target: this._createMouseTarget(e, testEventTarget)\n        });\n    }\n    _onMouseMove(e) {\n        const targetIsWidget = this.mouseTargetFactory.mouseTargetIsWidget(e);\n        if (!targetIsWidget) {\n            e.preventDefault();\n        }\n        if (this._mouseDownOperation.isActive()) {\n            // In selection/drag operation\n            return;\n        }\n        const actualMouseMoveTime = e.timestamp;\n        if (actualMouseMoveTime < this.lastMouseLeaveTime) {\n            // Due to throttling, this event occurred before the mouse left the editor, therefore ignore it.\n            return;\n        }\n        this.viewController.emitMouseMove({\n            event: e,\n            target: this._createMouseTarget(e, true)\n        });\n    }\n    _onMouseLeave(e) {\n        if (this._mouseLeaveMonitor) {\n            this._mouseLeaveMonitor.dispose();\n            this._mouseLeaveMonitor = null;\n        }\n        this.lastMouseLeaveTime = (new Date()).getTime();\n        this.viewController.emitMouseLeave({\n            event: e,\n            target: null\n        });\n    }\n    _onMouseUp(e) {\n        this.viewController.emitMouseUp({\n            event: e,\n            target: this._createMouseTarget(e, true)\n        });\n    }\n    _onMouseDown(e, pointerId) {\n        const t = this._createMouseTarget(e, true);\n        const targetIsContent = (t.type === 6 /* MouseTargetType.CONTENT_TEXT */ || t.type === 7 /* MouseTargetType.CONTENT_EMPTY */);\n        const targetIsGutter = (t.type === 2 /* MouseTargetType.GUTTER_GLYPH_MARGIN */ || t.type === 3 /* MouseTargetType.GUTTER_LINE_NUMBERS */ || t.type === 4 /* MouseTargetType.GUTTER_LINE_DECORATIONS */);\n        const targetIsLineNumbers = (t.type === 3 /* MouseTargetType.GUTTER_LINE_NUMBERS */);\n        const selectOnLineNumbers = this._context.configuration.options.get(110 /* EditorOption.selectOnLineNumbers */);\n        const targetIsViewZone = (t.type === 8 /* MouseTargetType.CONTENT_VIEW_ZONE */ || t.type === 5 /* MouseTargetType.GUTTER_VIEW_ZONE */);\n        const targetIsWidget = (t.type === 9 /* MouseTargetType.CONTENT_WIDGET */);\n        let shouldHandle = e.leftButton || e.middleButton;\n        if (platform.isMacintosh && e.leftButton && e.ctrlKey) {\n            shouldHandle = false;\n        }\n        const focus = () => {\n            e.preventDefault();\n            this.viewHelper.focusTextArea();\n        };\n        if (shouldHandle && (targetIsContent || (targetIsLineNumbers && selectOnLineNumbers))) {\n            focus();\n            this._mouseDownOperation.start(t.type, e, pointerId);\n        }\n        else if (targetIsGutter) {\n            // Do not steal focus\n            e.preventDefault();\n        }\n        else if (targetIsViewZone) {\n            const viewZoneData = t.detail;\n            if (shouldHandle && this.viewHelper.shouldSuppressMouseDownOnViewZone(viewZoneData.viewZoneId)) {\n                focus();\n                this._mouseDownOperation.start(t.type, e, pointerId);\n                e.preventDefault();\n            }\n        }\n        else if (targetIsWidget && this.viewHelper.shouldSuppressMouseDownOnWidget(t.detail)) {\n            focus();\n            e.preventDefault();\n        }\n        this.viewController.emitMouseDown({\n            event: e,\n            target: t\n        });\n    }\n}\nclass MouseDownOperation extends Disposable {\n    constructor(_context, _viewController, _viewHelper, _mouseTargetFactory, createMouseTarget, getMouseColumn) {\n        super();\n        this._context = _context;\n        this._viewController = _viewController;\n        this._viewHelper = _viewHelper;\n        this._mouseTargetFactory = _mouseTargetFactory;\n        this._createMouseTarget = createMouseTarget;\n        this._getMouseColumn = getMouseColumn;\n        this._mouseMoveMonitor = this._register(new GlobalEditorPointerMoveMonitor(this._viewHelper.viewDomNode));\n        this._topBottomDragScrolling = this._register(new TopBottomDragScrolling(this._context, this._viewHelper, this._mouseTargetFactory, (position, inSelectionMode, revealType) => this._dispatchMouse(position, inSelectionMode, revealType)));\n        this._mouseState = new MouseDownState();\n        this._currentSelection = new Selection(1, 1, 1, 1);\n        this._isActive = false;\n        this._lastMouseEvent = null;\n    }\n    dispose() {\n        super.dispose();\n    }\n    isActive() {\n        return this._isActive;\n    }\n    _onMouseDownThenMove(e) {\n        this._lastMouseEvent = e;\n        this._mouseState.setModifiers(e);\n        const position = this._findMousePosition(e, false);\n        if (!position) {\n            // Ignoring because position is unknown\n            return;\n        }\n        if (this._mouseState.isDragAndDrop) {\n            this._viewController.emitMouseDrag({\n                event: e,\n                target: position\n            });\n        }\n        else {\n            if (position.type === 13 /* MouseTargetType.OUTSIDE_EDITOR */ && (position.outsidePosition === 'above' || position.outsidePosition === 'below')) {\n                this._topBottomDragScrolling.start(position, e);\n            }\n            else {\n                this._topBottomDragScrolling.stop();\n                this._dispatchMouse(position, true, 1 /* NavigationCommandRevealType.Minimal */);\n            }\n        }\n    }\n    start(targetType, e, pointerId) {\n        this._lastMouseEvent = e;\n        this._mouseState.setStartedOnLineNumbers(targetType === 3 /* MouseTargetType.GUTTER_LINE_NUMBERS */);\n        this._mouseState.setStartButtons(e);\n        this._mouseState.setModifiers(e);\n        const position = this._findMousePosition(e, true);\n        if (!position || !position.position) {\n            // Ignoring because position is unknown\n            return;\n        }\n        this._mouseState.trySetCount(e.detail, position.position);\n        // Overwrite the detail of the MouseEvent, as it will be sent out in an event and contributions might rely on it.\n        e.detail = this._mouseState.count;\n        const options = this._context.configuration.options;\n        if (!options.get(92 /* EditorOption.readOnly */)\n            && options.get(35 /* EditorOption.dragAndDrop */)\n            && !options.get(22 /* EditorOption.columnSelection */)\n            && !this._mouseState.altKey // we don't support multiple mouse\n            && e.detail < 2 // only single click on a selection can work\n            && !this._isActive // the mouse is not down yet\n            && !this._currentSelection.isEmpty() // we don't drag single cursor\n            && (position.type === 6 /* MouseTargetType.CONTENT_TEXT */) // single click on text\n            && position.position && this._currentSelection.containsPosition(position.position) // single click on a selection\n        ) {\n            this._mouseState.isDragAndDrop = true;\n            this._isActive = true;\n            this._mouseMoveMonitor.startMonitoring(this._viewHelper.viewLinesDomNode, pointerId, e.buttons, (e) => this._onMouseDownThenMove(e), (browserEvent) => {\n                const position = this._findMousePosition(this._lastMouseEvent, false);\n                if (dom.isKeyboardEvent(browserEvent)) {\n                    // cancel\n                    this._viewController.emitMouseDropCanceled();\n                }\n                else {\n                    this._viewController.emitMouseDrop({\n                        event: this._lastMouseEvent,\n                        target: (position ? this._createMouseTarget(this._lastMouseEvent, true) : null) // Ignoring because position is unknown, e.g., Content View Zone\n                    });\n                }\n                this._stop();\n            });\n            return;\n        }\n        this._mouseState.isDragAndDrop = false;\n        this._dispatchMouse(position, e.shiftKey, 1 /* NavigationCommandRevealType.Minimal */);\n        if (!this._isActive) {\n            this._isActive = true;\n            this._mouseMoveMonitor.startMonitoring(this._viewHelper.viewLinesDomNode, pointerId, e.buttons, (e) => this._onMouseDownThenMove(e), () => this._stop());\n        }\n    }\n    _stop() {\n        this._isActive = false;\n        this._topBottomDragScrolling.stop();\n    }\n    onHeightChanged() {\n        this._mouseMoveMonitor.stopMonitoring();\n    }\n    onPointerUp() {\n        this._mouseMoveMonitor.stopMonitoring();\n    }\n    onCursorStateChanged(e) {\n        this._currentSelection = e.selections[0];\n    }\n    _getPositionOutsideEditor(e) {\n        const editorContent = e.editorPos;\n        const model = this._context.viewModel;\n        const viewLayout = this._context.viewLayout;\n        const mouseColumn = this._getMouseColumn(e);\n        if (e.posy < editorContent.y) {\n            const outsideDistance = editorContent.y - e.posy;\n            const verticalOffset = Math.max(viewLayout.getCurrentScrollTop() - outsideDistance, 0);\n            const viewZoneData = HitTestContext.getZoneAtCoord(this._context, verticalOffset);\n            if (viewZoneData) {\n                const newPosition = this._helpPositionJumpOverViewZone(viewZoneData);\n                if (newPosition) {\n                    return MouseTarget.createOutsideEditor(mouseColumn, newPosition, 'above', outsideDistance);\n                }\n            }\n            const aboveLineNumber = viewLayout.getLineNumberAtVerticalOffset(verticalOffset);\n            return MouseTarget.createOutsideEditor(mouseColumn, new Position(aboveLineNumber, 1), 'above', outsideDistance);\n        }\n        if (e.posy > editorContent.y + editorContent.height) {\n            const outsideDistance = e.posy - editorContent.y - editorContent.height;\n            const verticalOffset = viewLayout.getCurrentScrollTop() + e.relativePos.y;\n            const viewZoneData = HitTestContext.getZoneAtCoord(this._context, verticalOffset);\n            if (viewZoneData) {\n                const newPosition = this._helpPositionJumpOverViewZone(viewZoneData);\n                if (newPosition) {\n                    return MouseTarget.createOutsideEditor(mouseColumn, newPosition, 'below', outsideDistance);\n                }\n            }\n            const belowLineNumber = viewLayout.getLineNumberAtVerticalOffset(verticalOffset);\n            return MouseTarget.createOutsideEditor(mouseColumn, new Position(belowLineNumber, model.getLineMaxColumn(belowLineNumber)), 'below', outsideDistance);\n        }\n        const possibleLineNumber = viewLayout.getLineNumberAtVerticalOffset(viewLayout.getCurrentScrollTop() + e.relativePos.y);\n        if (e.posx < editorContent.x) {\n            const outsideDistance = editorContent.x - e.posx;\n            return MouseTarget.createOutsideEditor(mouseColumn, new Position(possibleLineNumber, 1), 'left', outsideDistance);\n        }\n        if (e.posx > editorContent.x + editorContent.width) {\n            const outsideDistance = e.posx - editorContent.x - editorContent.width;\n            return MouseTarget.createOutsideEditor(mouseColumn, new Position(possibleLineNumber, model.getLineMaxColumn(possibleLineNumber)), 'right', outsideDistance);\n        }\n        return null;\n    }\n    _findMousePosition(e, testEventTarget) {\n        const positionOutsideEditor = this._getPositionOutsideEditor(e);\n        if (positionOutsideEditor) {\n            return positionOutsideEditor;\n        }\n        const t = this._createMouseTarget(e, testEventTarget);\n        const hintedPosition = t.position;\n        if (!hintedPosition) {\n            return null;\n        }\n        if (t.type === 8 /* MouseTargetType.CONTENT_VIEW_ZONE */ || t.type === 5 /* MouseTargetType.GUTTER_VIEW_ZONE */) {\n            const newPosition = this._helpPositionJumpOverViewZone(t.detail);\n            if (newPosition) {\n                return MouseTarget.createViewZone(t.type, t.element, t.mouseColumn, newPosition, t.detail);\n            }\n        }\n        return t;\n    }\n    _helpPositionJumpOverViewZone(viewZoneData) {\n        // Force position on view zones to go above or below depending on where selection started from\n        const selectionStart = new Position(this._currentSelection.selectionStartLineNumber, this._currentSelection.selectionStartColumn);\n        const positionBefore = viewZoneData.positionBefore;\n        const positionAfter = viewZoneData.positionAfter;\n        if (positionBefore && positionAfter) {\n            if (positionBefore.isBefore(selectionStart)) {\n                return positionBefore;\n            }\n            else {\n                return positionAfter;\n            }\n        }\n        return null;\n    }\n    _dispatchMouse(position, inSelectionMode, revealType) {\n        if (!position.position) {\n            return;\n        }\n        this._viewController.dispatchMouse({\n            position: position.position,\n            mouseColumn: position.mouseColumn,\n            startedOnLineNumbers: this._mouseState.startedOnLineNumbers,\n            revealType,\n            inSelectionMode: inSelectionMode,\n            mouseDownCount: this._mouseState.count,\n            altKey: this._mouseState.altKey,\n            ctrlKey: this._mouseState.ctrlKey,\n            metaKey: this._mouseState.metaKey,\n            shiftKey: this._mouseState.shiftKey,\n            leftButton: this._mouseState.leftButton,\n            middleButton: this._mouseState.middleButton,\n            onInjectedText: position.type === 6 /* MouseTargetType.CONTENT_TEXT */ && position.detail.injectedText !== null\n        });\n    }\n}\nclass TopBottomDragScrolling extends Disposable {\n    constructor(_context, _viewHelper, _mouseTargetFactory, _dispatchMouse) {\n        super();\n        this._context = _context;\n        this._viewHelper = _viewHelper;\n        this._mouseTargetFactory = _mouseTargetFactory;\n        this._dispatchMouse = _dispatchMouse;\n        this._operation = null;\n    }\n    dispose() {\n        super.dispose();\n        this.stop();\n    }\n    start(position, mouseEvent) {\n        if (this._operation) {\n            this._operation.setPosition(position, mouseEvent);\n        }\n        else {\n            this._operation = new TopBottomDragScrollingOperation(this._context, this._viewHelper, this._mouseTargetFactory, this._dispatchMouse, position, mouseEvent);\n        }\n    }\n    stop() {\n        if (this._operation) {\n            this._operation.dispose();\n            this._operation = null;\n        }\n    }\n}\nclass TopBottomDragScrollingOperation extends Disposable {\n    constructor(_context, _viewHelper, _mouseTargetFactory, _dispatchMouse, position, mouseEvent) {\n        super();\n        this._context = _context;\n        this._viewHelper = _viewHelper;\n        this._mouseTargetFactory = _mouseTargetFactory;\n        this._dispatchMouse = _dispatchMouse;\n        this._position = position;\n        this._mouseEvent = mouseEvent;\n        this._lastTime = Date.now();\n        this._animationFrameDisposable = dom.scheduleAtNextAnimationFrame(dom.getWindow(mouseEvent.browserEvent), () => this._execute());\n    }\n    dispose() {\n        this._animationFrameDisposable.dispose();\n        super.dispose();\n    }\n    setPosition(position, mouseEvent) {\n        this._position = position;\n        this._mouseEvent = mouseEvent;\n    }\n    /**\n     * update internal state and return elapsed ms since last time\n     */\n    _tick() {\n        const now = Date.now();\n        const elapsed = now - this._lastTime;\n        this._lastTime = now;\n        return elapsed;\n    }\n    /**\n     * get the number of lines per second to auto-scroll\n     */\n    _getScrollSpeed() {\n        const lineHeight = this._context.configuration.options.get(67 /* EditorOption.lineHeight */);\n        const viewportInLines = this._context.configuration.options.get(146 /* EditorOption.layoutInfo */).height / lineHeight;\n        const outsideDistanceInLines = this._position.outsideDistance / lineHeight;\n        if (outsideDistanceInLines <= 1.5) {\n            return Math.max(30, viewportInLines * (1 + outsideDistanceInLines));\n        }\n        if (outsideDistanceInLines <= 3) {\n            return Math.max(60, viewportInLines * (2 + outsideDistanceInLines));\n        }\n        return Math.max(200, viewportInLines * (7 + outsideDistanceInLines));\n    }\n    _execute() {\n        const lineHeight = this._context.configuration.options.get(67 /* EditorOption.lineHeight */);\n        const scrollSpeedInLines = this._getScrollSpeed();\n        const elapsed = this._tick();\n        const scrollInPixels = scrollSpeedInLines * (elapsed / 1000) * lineHeight;\n        const scrollValue = (this._position.outsidePosition === 'above' ? -scrollInPixels : scrollInPixels);\n        this._context.viewModel.viewLayout.deltaScrollNow(0, scrollValue);\n        this._viewHelper.renderNow();\n        const viewportData = this._context.viewLayout.getLinesViewportData();\n        const edgeLineNumber = (this._position.outsidePosition === 'above' ? viewportData.startLineNumber : viewportData.endLineNumber);\n        // First, try to find a position that matches the horizontal position of the mouse\n        let mouseTarget;\n        {\n            const editorPos = createEditorPagePosition(this._viewHelper.viewDomNode);\n            const horizontalScrollbarHeight = this._context.configuration.options.get(146 /* EditorOption.layoutInfo */).horizontalScrollbarHeight;\n            const pos = new PageCoordinates(this._mouseEvent.pos.x, editorPos.y + editorPos.height - horizontalScrollbarHeight - 0.1);\n            const relativePos = createCoordinatesRelativeToEditor(this._viewHelper.viewDomNode, editorPos, pos);\n            mouseTarget = this._mouseTargetFactory.createMouseTarget(this._viewHelper.getLastRenderData(), editorPos, pos, relativePos, null);\n        }\n        if (!mouseTarget.position || mouseTarget.position.lineNumber !== edgeLineNumber) {\n            if (this._position.outsidePosition === 'above') {\n                mouseTarget = MouseTarget.createOutsideEditor(this._position.mouseColumn, new Position(edgeLineNumber, 1), 'above', this._position.outsideDistance);\n            }\n            else {\n                mouseTarget = MouseTarget.createOutsideEditor(this._position.mouseColumn, new Position(edgeLineNumber, this._context.viewModel.getLineMaxColumn(edgeLineNumber)), 'below', this._position.outsideDistance);\n            }\n        }\n        this._dispatchMouse(mouseTarget, true, 2 /* NavigationCommandRevealType.None */);\n        this._animationFrameDisposable = dom.scheduleAtNextAnimationFrame(dom.getWindow(mouseTarget.element), () => this._execute());\n    }\n}\nclass MouseDownState {\n    static { this.CLEAR_MOUSE_DOWN_COUNT_TIME = 400; } // ms\n    get altKey() { return this._altKey; }\n    get ctrlKey() { return this._ctrlKey; }\n    get metaKey() { return this._metaKey; }\n    get shiftKey() { return this._shiftKey; }\n    get leftButton() { return this._leftButton; }\n    get middleButton() { return this._middleButton; }\n    get startedOnLineNumbers() { return this._startedOnLineNumbers; }\n    constructor() {\n        this._altKey = false;\n        this._ctrlKey = false;\n        this._metaKey = false;\n        this._shiftKey = false;\n        this._leftButton = false;\n        this._middleButton = false;\n        this._startedOnLineNumbers = false;\n        this._lastMouseDownPosition = null;\n        this._lastMouseDownPositionEqualCount = 0;\n        this._lastMouseDownCount = 0;\n        this._lastSetMouseDownCountTime = 0;\n        this.isDragAndDrop = false;\n    }\n    get count() {\n        return this._lastMouseDownCount;\n    }\n    setModifiers(source) {\n        this._altKey = source.altKey;\n        this._ctrlKey = source.ctrlKey;\n        this._metaKey = source.metaKey;\n        this._shiftKey = source.shiftKey;\n    }\n    setStartButtons(source) {\n        this._leftButton = source.leftButton;\n        this._middleButton = source.middleButton;\n    }\n    setStartedOnLineNumbers(startedOnLineNumbers) {\n        this._startedOnLineNumbers = startedOnLineNumbers;\n    }\n    trySetCount(setMouseDownCount, newMouseDownPosition) {\n        // a. Invalidate multiple clicking if too much time has passed (will be hit by IE because the detail field of mouse events contains garbage in IE10)\n        const currentTime = (new Date()).getTime();\n        if (currentTime - this._lastSetMouseDownCountTime > MouseDownState.CLEAR_MOUSE_DOWN_COUNT_TIME) {\n            setMouseDownCount = 1;\n        }\n        this._lastSetMouseDownCountTime = currentTime;\n        // b. Ensure that we don't jump from single click to triple click in one go (will be hit by IE because the detail field of mouse events contains garbage in IE10)\n        if (setMouseDownCount > this._lastMouseDownCount + 1) {\n            setMouseDownCount = this._lastMouseDownCount + 1;\n        }\n        // c. Invalidate multiple clicking if the logical position is different\n        if (this._lastMouseDownPosition && this._lastMouseDownPosition.equals(newMouseDownPosition)) {\n            this._lastMouseDownPositionEqualCount++;\n        }\n        else {\n            this._lastMouseDownPositionEqualCount = 1;\n        }\n        this._lastMouseDownPosition = newMouseDownPosition;\n        // Finally set the lastMouseDownCount\n        this._lastMouseDownCount = Math.min(setMouseDownCount, this._lastMouseDownPositionEqualCount);\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,KAAKA,GAAG,MAAM,8BAA8B;AACnD,SAASC,kBAAkB,QAAQ,qCAAqC;AACxE,SAASC,UAAU,QAAQ,mCAAmC;AAC9D,OAAO,KAAKC,QAAQ,MAAM,kCAAkC;AAC5D,SAASC,cAAc,EAAEC,WAAW,EAAEC,kBAAkB,QAAQ,kBAAkB;AAClF,SAASC,iBAAiB,EAAEC,gBAAgB,EAAEC,uBAAuB,EAAEC,8BAA8B,EAAEC,wBAAwB,EAAEC,iCAAiC,EAAEC,eAAe,QAAQ,iBAAiB;AAC5M,SAASC,UAAU,QAAQ,mCAAmC;AAC9D,SAASC,QAAQ,QAAQ,+BAA+B;AACxD,SAASC,SAAS,QAAQ,gCAAgC;AAC1D,SAASC,gBAAgB,QAAQ,kCAAkC;AACnE,SAASC,oBAAoB,QAAQ,yDAAyD;AAC9F,OAAO,MAAMC,YAAY,SAASF,gBAAgB,CAAC;EAC/CG,WAAWA,CAACC,OAAO,EAAEC,cAAc,EAAEC,UAAU,EAAE;IAC7C,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,kBAAkB,GAAG,IAAI;IAC9B,IAAI,CAACC,QAAQ,GAAGJ,OAAO;IACvB,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACG,kBAAkB,GAAG,IAAIpB,kBAAkB,CAAC,IAAI,CAACmB,QAAQ,EAAEF,UAAU,CAAC;IAC3E,IAAI,CAACI,mBAAmB,GAAG,IAAI,CAACC,SAAS,CAAC,IAAIC,kBAAkB,CAAC,IAAI,CAACJ,QAAQ,EAAE,IAAI,CAACH,cAAc,EAAE,IAAI,CAACC,UAAU,EAAE,IAAI,CAACG,kBAAkB,EAAE,CAACI,CAAC,EAAEC,eAAe,KAAK,IAAI,CAACC,kBAAkB,CAACF,CAAC,EAAEC,eAAe,CAAC,EAAGD,CAAC,IAAK,IAAI,CAACG,eAAe,CAACH,CAAC,CAAC,CAAC,CAAC;IACpP,IAAI,CAACI,kBAAkB,GAAG,CAAC,CAAC;IAC5B,IAAI,CAACC,OAAO,GAAG,IAAI,CAACV,QAAQ,CAACW,aAAa,CAACC,OAAO,CAACC,GAAG,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAACC,MAAM;IAChG,MAAMC,WAAW,GAAG,IAAI/B,uBAAuB,CAAC,IAAI,CAACc,UAAU,CAACkB,WAAW,CAAC;IAC5E,IAAI,CAACb,SAAS,CAACY,WAAW,CAACE,aAAa,CAAC,IAAI,CAACnB,UAAU,CAACkB,WAAW,EAAGX,CAAC,IAAK,IAAI,CAACa,cAAc,CAACb,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;IAC3G,IAAI,CAACF,SAAS,CAACY,WAAW,CAACI,WAAW,CAAC,IAAI,CAACrB,UAAU,CAACkB,WAAW,EAAGX,CAAC,IAAK;MACvE,IAAI,CAACe,YAAY,CAACf,CAAC,CAAC;MACpB;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,CAAC,IAAI,CAACN,kBAAkB,EAAE;QAC1B,IAAI,CAACA,kBAAkB,GAAGxB,GAAG,CAAC8C,qBAAqB,CAAC,IAAI,CAACvB,UAAU,CAACkB,WAAW,CAACM,aAAa,EAAE,WAAW,EAAGjB,CAAC,IAAK;UAC/G,IAAI,CAAC,IAAI,CAACP,UAAU,CAACkB,WAAW,CAACO,QAAQ,CAAClB,CAAC,CAACmB,MAAM,CAAC,EAAE;YACjD;YACA,IAAI,CAACC,aAAa,CAAC,IAAI1C,gBAAgB,CAACsB,CAAC,EAAE,KAAK,EAAE,IAAI,CAACP,UAAU,CAACkB,WAAW,CAAC,CAAC;UACnF;QACJ,CAAC,CAAC;MACN;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,CAACb,SAAS,CAACY,WAAW,CAACW,SAAS,CAAC,IAAI,CAAC5B,UAAU,CAACkB,WAAW,EAAGX,CAAC,IAAK,IAAI,CAACsB,UAAU,CAACtB,CAAC,CAAC,CAAC,CAAC;IAC7F,IAAI,CAACF,SAAS,CAACY,WAAW,CAACa,YAAY,CAAC,IAAI,CAAC9B,UAAU,CAACkB,WAAW,EAAGX,CAAC,IAAK,IAAI,CAACoB,aAAa,CAACpB,CAAC,CAAC,CAAC,CAAC;IACnG;IACA;IACA;IACA;IACA,IAAIwB,gBAAgB,GAAG,CAAC;IACxB,IAAI,CAAC1B,SAAS,CAACY,WAAW,CAACe,aAAa,CAAC,IAAI,CAAChC,UAAU,CAACkB,WAAW,EAAE,CAACX,CAAC,EAAE0B,SAAS,KAAK;MACpFF,gBAAgB,GAAGE,SAAS;IAChC,CAAC,CAAC,CAAC;IACH;IACA;IACA;IACA;IACA;IACA,IAAI,CAAC5B,SAAS,CAAC5B,GAAG,CAAC8C,qBAAqB,CAAC,IAAI,CAACvB,UAAU,CAACkB,WAAW,EAAEzC,GAAG,CAACyD,SAAS,CAACC,UAAU,EAAG5B,CAAC,IAAK;MACnG,IAAI,CAACH,mBAAmB,CAACgC,WAAW,CAAC,CAAC;IAC1C,CAAC,CAAC,CAAC;IACH,IAAI,CAAC/B,SAAS,CAACY,WAAW,CAACoB,WAAW,CAAC,IAAI,CAACrC,UAAU,CAACkB,WAAW,EAAGX,CAAC,IAAK,IAAI,CAAC+B,YAAY,CAAC/B,CAAC,EAAEwB,gBAAgB,CAAC,CAAC,CAAC;IACnH,IAAI,CAACQ,4BAA4B,CAAC,CAAC;IACnC,IAAI,CAACrC,QAAQ,CAACsC,eAAe,CAAC,IAAI,CAAC;EACvC;EACAD,4BAA4BA,CAAA,EAAG;IAC3B,MAAME,UAAU,GAAG9C,oBAAoB,CAAC+C,QAAQ;IAChD,IAAIC,kBAAkB,GAAG,CAAC;IAC1B,IAAIC,qBAAqB,GAAGrD,UAAU,CAACsD,YAAY,CAAC,CAAC;IACrD,IAAIC,uBAAuB,GAAG,KAAK;IACnC,IAAIC,uBAAuB,GAAG,CAAC;IAC/B,MAAMC,YAAY,GAAIC,YAAY,IAAK;MACnC,IAAI,CAAClD,cAAc,CAACmD,cAAc,CAACD,YAAY,CAAC;MAChD,IAAI,CAAC,IAAI,CAAC/C,QAAQ,CAACW,aAAa,CAACC,OAAO,CAACC,GAAG,CAAC,EAAE,CAAC,iCAAiC,CAAC,EAAE;QAChF;MACJ;MACA,MAAMR,CAAC,GAAG,IAAI7B,kBAAkB,CAACuE,YAAY,CAAC;MAC9CR,UAAU,CAACU,wBAAwB,CAAC5C,CAAC,CAAC;MACtC,IAAIkC,UAAU,CAACW,oBAAoB,CAAC,CAAC,EAAE;QACnC,IAAIC,0BAA0B,CAACJ,YAAY,CAAC,EAAE;UAC1C,MAAMK,SAAS,GAAG/D,UAAU,CAACsD,YAAY,CAAC,CAAC;UAC3C,MAAMU,KAAK,GAAGhD,CAAC,CAACiD,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;UACnCjE,UAAU,CAACkE,YAAY,CAACH,SAAS,GAAGC,KAAK,CAAC;UAC1ChD,CAAC,CAACmD,cAAc,CAAC,CAAC;UAClBnD,CAAC,CAACoD,eAAe,CAAC,CAAC;QACvB;MACJ,CAAC,MACI;QACD;QACA;QACA;QACA,IAAIC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGlB,kBAAkB,GAAG,EAAE,EAAE;UACtC;UACAC,qBAAqB,GAAGrD,UAAU,CAACsD,YAAY,CAAC,CAAC;UACjDC,uBAAuB,GAAGO,0BAA0B,CAACJ,YAAY,CAAC;UAClEF,uBAAuB,GAAG,CAAC;QAC/B;QACAJ,kBAAkB,GAAGiB,IAAI,CAACC,GAAG,CAAC,CAAC;QAC/Bd,uBAAuB,IAAIxC,CAAC,CAACiD,MAAM;QACnC,IAAIV,uBAAuB,EAAE;UACzBvD,UAAU,CAACkE,YAAY,CAACb,qBAAqB,GAAGG,uBAAuB,GAAG,CAAC,CAAC;UAC5ExC,CAAC,CAACmD,cAAc,CAAC,CAAC;UAClBnD,CAAC,CAACoD,eAAe,CAAC,CAAC;QACvB;MACJ;IACJ,CAAC;IACD,IAAI,CAACtD,SAAS,CAAC5B,GAAG,CAAC8C,qBAAqB,CAAC,IAAI,CAACvB,UAAU,CAACkB,WAAW,EAAEzC,GAAG,CAACyD,SAAS,CAAC4B,WAAW,EAAEd,YAAY,EAAE;MAAEe,OAAO,EAAE,IAAI;MAAEC,OAAO,EAAE;IAAM,CAAC,CAAC,CAAC;IAClJ,SAASX,0BAA0BA,CAACJ,YAAY,EAAE;MAC9C,OAAQrE,QAAQ,CAACqF;MACb;MACA;MAAA,EACG,CAAChB,YAAY,CAACiB,OAAO,IAAIjB,YAAY,CAACkB,OAAO,KAAK,CAAClB,YAAY,CAACmB,QAAQ,IAAI,CAACnB,YAAY,CAACoB,MAAM,GAChGpB,YAAY,CAACkB,OAAO,IAAI,CAAClB,YAAY,CAACiB,OAAO,IAAI,CAACjB,YAAY,CAACmB,QAAQ,IAAI,CAACnB,YAAY,CAACoB,MAAO;IAC3G;EACJ;EACAC,OAAOA,CAAA,EAAG;IACN,IAAI,CAACpE,QAAQ,CAACqE,kBAAkB,CAAC,IAAI,CAAC;IACtC,IAAI,IAAI,CAACtE,kBAAkB,EAAE;MACzB,IAAI,CAACA,kBAAkB,CAACqE,OAAO,CAAC,CAAC;MACjC,IAAI,CAACrE,kBAAkB,GAAG,IAAI;IAClC;IACA,KAAK,CAACqE,OAAO,CAAC,CAAC;EACnB;EACA;EACAE,sBAAsBA,CAACjE,CAAC,EAAE;IACtB,IAAIA,CAAC,CAACkE,UAAU,CAAC,GAAG,CAAC,6BAA6B,CAAC,EAAE;MACjD;MACA,MAAMzD,MAAM,GAAG,IAAI,CAACd,QAAQ,CAACW,aAAa,CAACC,OAAO,CAACC,GAAG,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAACC,MAAM;MAChG,IAAI,IAAI,CAACJ,OAAO,KAAKI,MAAM,EAAE;QACzB,IAAI,CAACJ,OAAO,GAAGI,MAAM;QACrB,IAAI,CAACZ,mBAAmB,CAACsE,eAAe,CAAC,CAAC;MAC9C;IACJ;IACA,OAAO,KAAK;EAChB;EACAC,oBAAoBA,CAACpE,CAAC,EAAE;IACpB,IAAI,CAACH,mBAAmB,CAACuE,oBAAoB,CAACpE,CAAC,CAAC;IAChD,OAAO,KAAK;EAChB;EACAqE,cAAcA,CAACrE,CAAC,EAAE;IACd,OAAO,KAAK;EAChB;EACA;EACAsE,sBAAsBA,CAACC,OAAO,EAAEC,OAAO,EAAE;IACrC,MAAMC,SAAS,GAAG,IAAIhG,iBAAiB,CAAC8F,OAAO,EAAEC,OAAO,CAAC;IACzD,MAAME,GAAG,GAAGD,SAAS,CAACE,iBAAiB,CAACzG,GAAG,CAAC0G,SAAS,CAAC,IAAI,CAACnF,UAAU,CAACkB,WAAW,CAAC,CAAC;IACnF,MAAMkE,SAAS,GAAGhG,wBAAwB,CAAC,IAAI,CAACY,UAAU,CAACkB,WAAW,CAAC;IACvE,IAAI+D,GAAG,CAACI,CAAC,GAAGD,SAAS,CAACC,CAAC,IAAIJ,GAAG,CAACI,CAAC,GAAGD,SAAS,CAACC,CAAC,GAAGD,SAAS,CAACpE,MAAM,IAAIiE,GAAG,CAACK,CAAC,GAAGF,SAAS,CAACE,CAAC,IAAIL,GAAG,CAACK,CAAC,GAAGF,SAAS,CAACE,CAAC,GAAGF,SAAS,CAACG,KAAK,EAAE;MAC/H,OAAO,IAAI;IACf;IACA,MAAMC,WAAW,GAAGnG,iCAAiC,CAAC,IAAI,CAACW,UAAU,CAACkB,WAAW,EAAEkE,SAAS,EAAEH,GAAG,CAAC;IAClG,OAAO,IAAI,CAAC9E,kBAAkB,CAACsF,iBAAiB,CAAC,IAAI,CAACzF,UAAU,CAAC0F,iBAAiB,CAAC,CAAC,EAAEN,SAAS,EAAEH,GAAG,EAAEO,WAAW,EAAE,IAAI,CAAC;EAC5H;EACA/E,kBAAkBA,CAACF,CAAC,EAAEC,eAAe,EAAE;IACnC,IAAIkB,MAAM,GAAGnB,CAAC,CAACmB,MAAM;IACrB,IAAI,CAAC,IAAI,CAAC1B,UAAU,CAACkB,WAAW,CAACO,QAAQ,CAACC,MAAM,CAAC,EAAE;MAC/C,MAAMiE,UAAU,GAAGlH,GAAG,CAACmH,aAAa,CAAC,IAAI,CAAC5F,UAAU,CAACkB,WAAW,CAAC;MACjE,IAAIyE,UAAU,EAAE;QACZjE,MAAM,GAAGiE,UAAU,CAACE,iBAAiB,CAACtF,CAAC,CAACuF,IAAI,EAAEvF,CAAC,CAACwF,IAAI,CAAC,CAACC,IAAI,CAAEC,EAAE,IAAK,IAAI,CAACjG,UAAU,CAACkB,WAAW,CAACO,QAAQ,CAACwE,EAAE,CAAC,CAAC;MAChH;IACJ;IACA,OAAO,IAAI,CAAC9F,kBAAkB,CAACsF,iBAAiB,CAAC,IAAI,CAACzF,UAAU,CAAC0F,iBAAiB,CAAC,CAAC,EAAEnF,CAAC,CAAC6E,SAAS,EAAE7E,CAAC,CAAC0E,GAAG,EAAE1E,CAAC,CAACiF,WAAW,EAAEhF,eAAe,GAAGkB,MAAM,GAAG,IAAI,CAAC;EAC7J;EACAhB,eAAeA,CAACH,CAAC,EAAE;IACf,OAAO,IAAI,CAACJ,kBAAkB,CAAC+F,cAAc,CAAC3F,CAAC,CAACiF,WAAW,CAAC;EAChE;EACApE,cAAcA,CAACb,CAAC,EAAEC,eAAe,EAAE;IAC/B,IAAI,CAACT,cAAc,CAACoG,eAAe,CAAC;MAChCC,KAAK,EAAE7F,CAAC;MACRmB,MAAM,EAAE,IAAI,CAACjB,kBAAkB,CAACF,CAAC,EAAEC,eAAe;IACtD,CAAC,CAAC;EACN;EACAc,YAAYA,CAACf,CAAC,EAAE;IACZ,MAAM8F,cAAc,GAAG,IAAI,CAAClG,kBAAkB,CAACmG,mBAAmB,CAAC/F,CAAC,CAAC;IACrE,IAAI,CAAC8F,cAAc,EAAE;MACjB9F,CAAC,CAACmD,cAAc,CAAC,CAAC;IACtB;IACA,IAAI,IAAI,CAACtD,mBAAmB,CAACmG,QAAQ,CAAC,CAAC,EAAE;MACrC;MACA;IACJ;IACA,MAAMC,mBAAmB,GAAGjG,CAAC,CAACkG,SAAS;IACvC,IAAID,mBAAmB,GAAG,IAAI,CAAC7F,kBAAkB,EAAE;MAC/C;MACA;IACJ;IACA,IAAI,CAACZ,cAAc,CAAC2G,aAAa,CAAC;MAC9BN,KAAK,EAAE7F,CAAC;MACRmB,MAAM,EAAE,IAAI,CAACjB,kBAAkB,CAACF,CAAC,EAAE,IAAI;IAC3C,CAAC,CAAC;EACN;EACAoB,aAAaA,CAACpB,CAAC,EAAE;IACb,IAAI,IAAI,CAACN,kBAAkB,EAAE;MACzB,IAAI,CAACA,kBAAkB,CAACqE,OAAO,CAAC,CAAC;MACjC,IAAI,CAACrE,kBAAkB,GAAG,IAAI;IAClC;IACA,IAAI,CAACU,kBAAkB,GAAI,IAAIiD,IAAI,CAAC,CAAC,CAAE+C,OAAO,CAAC,CAAC;IAChD,IAAI,CAAC5G,cAAc,CAAC6G,cAAc,CAAC;MAC/BR,KAAK,EAAE7F,CAAC;MACRmB,MAAM,EAAE;IACZ,CAAC,CAAC;EACN;EACAG,UAAUA,CAACtB,CAAC,EAAE;IACV,IAAI,CAACR,cAAc,CAAC8G,WAAW,CAAC;MAC5BT,KAAK,EAAE7F,CAAC;MACRmB,MAAM,EAAE,IAAI,CAACjB,kBAAkB,CAACF,CAAC,EAAE,IAAI;IAC3C,CAAC,CAAC;EACN;EACA+B,YAAYA,CAAC/B,CAAC,EAAE0B,SAAS,EAAE;IACvB,MAAM6E,CAAC,GAAG,IAAI,CAACrG,kBAAkB,CAACF,CAAC,EAAE,IAAI,CAAC;IAC1C,MAAMwG,eAAe,GAAID,CAAC,CAACE,IAAI,KAAK,CAAC,CAAC,sCAAsCF,CAAC,CAACE,IAAI,KAAK,CAAC,CAAC,mCAAoC;IAC7H,MAAMC,cAAc,GAAIH,CAAC,CAACE,IAAI,KAAK,CAAC,CAAC,6CAA6CF,CAAC,CAACE,IAAI,KAAK,CAAC,CAAC,6CAA6CF,CAAC,CAACE,IAAI,KAAK,CAAC,CAAC,6CAA8C;IACvM,MAAME,mBAAmB,GAAIJ,CAAC,CAACE,IAAI,KAAK,CAAC,CAAC,yCAA0C;IACpF,MAAMG,mBAAmB,GAAG,IAAI,CAACjH,QAAQ,CAACW,aAAa,CAACC,OAAO,CAACC,GAAG,CAAC,GAAG,CAAC,sCAAsC,CAAC;IAC/G,MAAMqG,gBAAgB,GAAIN,CAAC,CAACE,IAAI,KAAK,CAAC,CAAC,2CAA2CF,CAAC,CAACE,IAAI,KAAK,CAAC,CAAC,sCAAuC;IACtI,MAAMX,cAAc,GAAIS,CAAC,CAACE,IAAI,KAAK,CAAC,CAAC,oCAAqC;IAC1E,IAAIK,YAAY,GAAG9G,CAAC,CAAC+G,UAAU,IAAI/G,CAAC,CAACgH,YAAY;IACjD,IAAI3I,QAAQ,CAACqF,WAAW,IAAI1D,CAAC,CAAC+G,UAAU,IAAI/G,CAAC,CAAC4D,OAAO,EAAE;MACnDkD,YAAY,GAAG,KAAK;IACxB;IACA,MAAMG,KAAK,GAAGA,CAAA,KAAM;MAChBjH,CAAC,CAACmD,cAAc,CAAC,CAAC;MAClB,IAAI,CAAC1D,UAAU,CAACyH,aAAa,CAAC,CAAC;IACnC,CAAC;IACD,IAAIJ,YAAY,KAAKN,eAAe,IAAKG,mBAAmB,IAAIC,mBAAoB,CAAC,EAAE;MACnFK,KAAK,CAAC,CAAC;MACP,IAAI,CAACpH,mBAAmB,CAACsH,KAAK,CAACZ,CAAC,CAACE,IAAI,EAAEzG,CAAC,EAAE0B,SAAS,CAAC;IACxD,CAAC,MACI,IAAIgF,cAAc,EAAE;MACrB;MACA1G,CAAC,CAACmD,cAAc,CAAC,CAAC;IACtB,CAAC,MACI,IAAI0D,gBAAgB,EAAE;MACvB,MAAMO,YAAY,GAAGb,CAAC,CAACc,MAAM;MAC7B,IAAIP,YAAY,IAAI,IAAI,CAACrH,UAAU,CAAC6H,iCAAiC,CAACF,YAAY,CAACG,UAAU,CAAC,EAAE;QAC5FN,KAAK,CAAC,CAAC;QACP,IAAI,CAACpH,mBAAmB,CAACsH,KAAK,CAACZ,CAAC,CAACE,IAAI,EAAEzG,CAAC,EAAE0B,SAAS,CAAC;QACpD1B,CAAC,CAACmD,cAAc,CAAC,CAAC;MACtB;IACJ,CAAC,MACI,IAAI2C,cAAc,IAAI,IAAI,CAACrG,UAAU,CAAC+H,+BAA+B,CAACjB,CAAC,CAACc,MAAM,CAAC,EAAE;MAClFJ,KAAK,CAAC,CAAC;MACPjH,CAAC,CAACmD,cAAc,CAAC,CAAC;IACtB;IACA,IAAI,CAAC3D,cAAc,CAACiI,aAAa,CAAC;MAC9B5B,KAAK,EAAE7F,CAAC;MACRmB,MAAM,EAAEoF;IACZ,CAAC,CAAC;EACN;AACJ;AACA,MAAMxG,kBAAkB,SAAS3B,UAAU,CAAC;EACxCkB,WAAWA,CAACK,QAAQ,EAAE+H,eAAe,EAAEC,WAAW,EAAEC,mBAAmB,EAAE1C,iBAAiB,EAAES,cAAc,EAAE;IACxG,KAAK,CAAC,CAAC;IACP,IAAI,CAAChG,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAAC+H,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAAC1H,kBAAkB,GAAGgF,iBAAiB;IAC3C,IAAI,CAAC/E,eAAe,GAAGwF,cAAc;IACrC,IAAI,CAACkC,iBAAiB,GAAG,IAAI,CAAC/H,SAAS,CAAC,IAAIlB,8BAA8B,CAAC,IAAI,CAAC+I,WAAW,CAAChH,WAAW,CAAC,CAAC;IACzG,IAAI,CAACmH,uBAAuB,GAAG,IAAI,CAAChI,SAAS,CAAC,IAAIiI,sBAAsB,CAAC,IAAI,CAACpI,QAAQ,EAAE,IAAI,CAACgI,WAAW,EAAE,IAAI,CAACC,mBAAmB,EAAE,CAACI,QAAQ,EAAEC,eAAe,EAAEC,UAAU,KAAK,IAAI,CAACC,cAAc,CAACH,QAAQ,EAAEC,eAAe,EAAEC,UAAU,CAAC,CAAC,CAAC;IAC3O,IAAI,CAACE,WAAW,GAAG,IAAIC,cAAc,CAAC,CAAC;IACvC,IAAI,CAACC,iBAAiB,GAAG,IAAIpJ,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAClD,IAAI,CAACqJ,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,eAAe,GAAG,IAAI;EAC/B;EACAzE,OAAOA,CAAA,EAAG;IACN,KAAK,CAACA,OAAO,CAAC,CAAC;EACnB;EACAiC,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACuC,SAAS;EACzB;EACAE,oBAAoBA,CAACzI,CAAC,EAAE;IACpB,IAAI,CAACwI,eAAe,GAAGxI,CAAC;IACxB,IAAI,CAACoI,WAAW,CAACM,YAAY,CAAC1I,CAAC,CAAC;IAChC,MAAMgI,QAAQ,GAAG,IAAI,CAACW,kBAAkB,CAAC3I,CAAC,EAAE,KAAK,CAAC;IAClD,IAAI,CAACgI,QAAQ,EAAE;MACX;MACA;IACJ;IACA,IAAI,IAAI,CAACI,WAAW,CAACQ,aAAa,EAAE;MAChC,IAAI,CAAClB,eAAe,CAACmB,aAAa,CAAC;QAC/BhD,KAAK,EAAE7F,CAAC;QACRmB,MAAM,EAAE6G;MACZ,CAAC,CAAC;IACN,CAAC,MACI;MACD,IAAIA,QAAQ,CAACvB,IAAI,KAAK,EAAE,CAAC,yCAAyCuB,QAAQ,CAACc,eAAe,KAAK,OAAO,IAAId,QAAQ,CAACc,eAAe,KAAK,OAAO,CAAC,EAAE;QAC7I,IAAI,CAAChB,uBAAuB,CAACX,KAAK,CAACa,QAAQ,EAAEhI,CAAC,CAAC;MACnD,CAAC,MACI;QACD,IAAI,CAAC8H,uBAAuB,CAACiB,IAAI,CAAC,CAAC;QACnC,IAAI,CAACZ,cAAc,CAACH,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,yCAAyC,CAAC;MACpF;IACJ;EACJ;EACAb,KAAKA,CAAC6B,UAAU,EAAEhJ,CAAC,EAAE0B,SAAS,EAAE;IAC5B,IAAI,CAAC8G,eAAe,GAAGxI,CAAC;IACxB,IAAI,CAACoI,WAAW,CAACa,uBAAuB,CAACD,UAAU,KAAK,CAAC,CAAC,yCAAyC,CAAC;IACpG,IAAI,CAACZ,WAAW,CAACc,eAAe,CAAClJ,CAAC,CAAC;IACnC,IAAI,CAACoI,WAAW,CAACM,YAAY,CAAC1I,CAAC,CAAC;IAChC,MAAMgI,QAAQ,GAAG,IAAI,CAACW,kBAAkB,CAAC3I,CAAC,EAAE,IAAI,CAAC;IACjD,IAAI,CAACgI,QAAQ,IAAI,CAACA,QAAQ,CAACA,QAAQ,EAAE;MACjC;MACA;IACJ;IACA,IAAI,CAACI,WAAW,CAACe,WAAW,CAACnJ,CAAC,CAACqH,MAAM,EAAEW,QAAQ,CAACA,QAAQ,CAAC;IACzD;IACAhI,CAAC,CAACqH,MAAM,GAAG,IAAI,CAACe,WAAW,CAACgB,KAAK;IACjC,MAAM7I,OAAO,GAAG,IAAI,CAACZ,QAAQ,CAACW,aAAa,CAACC,OAAO;IACnD,IAAI,CAACA,OAAO,CAACC,GAAG,CAAC,EAAE,CAAC,2BAA2B,CAAC,IACzCD,OAAO,CAACC,GAAG,CAAC,EAAE,CAAC,8BAA8B,CAAC,IAC9C,CAACD,OAAO,CAACC,GAAG,CAAC,EAAE,CAAC,kCAAkC,CAAC,IACnD,CAAC,IAAI,CAAC4H,WAAW,CAACtE,MAAM,CAAC;IAAA,GACzB9D,CAAC,CAACqH,MAAM,GAAG,CAAC,CAAC;IAAA,GACb,CAAC,IAAI,CAACkB,SAAS,CAAC;IAAA,GAChB,CAAC,IAAI,CAACD,iBAAiB,CAACe,OAAO,CAAC,CAAC,CAAC;IAAA,GACjCrB,QAAQ,CAACvB,IAAI,KAAK,CAAC,CAAC,kCAAmC,CAAC;IAAA,GACzDuB,QAAQ,CAACA,QAAQ,IAAI,IAAI,CAACM,iBAAiB,CAACgB,gBAAgB,CAACtB,QAAQ,CAACA,QAAQ,CAAC,CAAC;IAAA,EACrF;MACE,IAAI,CAACI,WAAW,CAACQ,aAAa,GAAG,IAAI;MACrC,IAAI,CAACL,SAAS,GAAG,IAAI;MACrB,IAAI,CAACV,iBAAiB,CAAC0B,eAAe,CAAC,IAAI,CAAC5B,WAAW,CAAC6B,gBAAgB,EAAE9H,SAAS,EAAE1B,CAAC,CAACyJ,OAAO,EAAGzJ,CAAC,IAAK,IAAI,CAACyI,oBAAoB,CAACzI,CAAC,CAAC,EAAG0C,YAAY,IAAK;QACnJ,MAAMsF,QAAQ,GAAG,IAAI,CAACW,kBAAkB,CAAC,IAAI,CAACH,eAAe,EAAE,KAAK,CAAC;QACrE,IAAItK,GAAG,CAACwL,eAAe,CAAChH,YAAY,CAAC,EAAE;UACnC;UACA,IAAI,CAACgF,eAAe,CAACiC,qBAAqB,CAAC,CAAC;QAChD,CAAC,MACI;UACD,IAAI,CAACjC,eAAe,CAACkC,aAAa,CAAC;YAC/B/D,KAAK,EAAE,IAAI,CAAC2C,eAAe;YAC3BrH,MAAM,EAAG6G,QAAQ,GAAG,IAAI,CAAC9H,kBAAkB,CAAC,IAAI,CAACsI,eAAe,EAAE,IAAI,CAAC,GAAG,IAAK,CAAC;UACpF,CAAC,CAAC;QACN;QACA,IAAI,CAACqB,KAAK,CAAC,CAAC;MAChB,CAAC,CAAC;MACF;IACJ;IACA,IAAI,CAACzB,WAAW,CAACQ,aAAa,GAAG,KAAK;IACtC,IAAI,CAACT,cAAc,CAACH,QAAQ,EAAEhI,CAAC,CAAC6D,QAAQ,EAAE,CAAC,CAAC,yCAAyC,CAAC;IACtF,IAAI,CAAC,IAAI,CAAC0E,SAAS,EAAE;MACjB,IAAI,CAACA,SAAS,GAAG,IAAI;MACrB,IAAI,CAACV,iBAAiB,CAAC0B,eAAe,CAAC,IAAI,CAAC5B,WAAW,CAAC6B,gBAAgB,EAAE9H,SAAS,EAAE1B,CAAC,CAACyJ,OAAO,EAAGzJ,CAAC,IAAK,IAAI,CAACyI,oBAAoB,CAACzI,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC6J,KAAK,CAAC,CAAC,CAAC;IAC5J;EACJ;EACAA,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACtB,SAAS,GAAG,KAAK;IACtB,IAAI,CAACT,uBAAuB,CAACiB,IAAI,CAAC,CAAC;EACvC;EACA5E,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC0D,iBAAiB,CAACiC,cAAc,CAAC,CAAC;EAC3C;EACAjI,WAAWA,CAAA,EAAG;IACV,IAAI,CAACgG,iBAAiB,CAACiC,cAAc,CAAC,CAAC;EAC3C;EACA1F,oBAAoBA,CAACpE,CAAC,EAAE;IACpB,IAAI,CAACsI,iBAAiB,GAAGtI,CAAC,CAAC+J,UAAU,CAAC,CAAC,CAAC;EAC5C;EACAC,yBAAyBA,CAAChK,CAAC,EAAE;IACzB,MAAMiK,aAAa,GAAGjK,CAAC,CAAC6E,SAAS;IACjC,MAAMqF,KAAK,GAAG,IAAI,CAACvK,QAAQ,CAACwK,SAAS;IACrC,MAAMC,UAAU,GAAG,IAAI,CAACzK,QAAQ,CAACyK,UAAU;IAC3C,MAAMC,WAAW,GAAG,IAAI,CAAClK,eAAe,CAACH,CAAC,CAAC;IAC3C,IAAIA,CAAC,CAACwF,IAAI,GAAGyE,aAAa,CAACnF,CAAC,EAAE;MAC1B,MAAMwF,eAAe,GAAGL,aAAa,CAACnF,CAAC,GAAG9E,CAAC,CAACwF,IAAI;MAChD,MAAM+E,cAAc,GAAGC,IAAI,CAACC,GAAG,CAACL,UAAU,CAACM,mBAAmB,CAAC,CAAC,GAAGJ,eAAe,EAAE,CAAC,CAAC;MACtF,MAAMlD,YAAY,GAAG9I,cAAc,CAACqM,cAAc,CAAC,IAAI,CAAChL,QAAQ,EAAE4K,cAAc,CAAC;MACjF,IAAInD,YAAY,EAAE;QACd,MAAMwD,WAAW,GAAG,IAAI,CAACC,6BAA6B,CAACzD,YAAY,CAAC;QACpE,IAAIwD,WAAW,EAAE;UACb,OAAOrM,WAAW,CAACuM,mBAAmB,CAACT,WAAW,EAAEO,WAAW,EAAE,OAAO,EAAEN,eAAe,CAAC;QAC9F;MACJ;MACA,MAAMS,eAAe,GAAGX,UAAU,CAACY,6BAA6B,CAACT,cAAc,CAAC;MAChF,OAAOhM,WAAW,CAACuM,mBAAmB,CAACT,WAAW,EAAE,IAAIpL,QAAQ,CAAC8L,eAAe,EAAE,CAAC,CAAC,EAAE,OAAO,EAAET,eAAe,CAAC;IACnH;IACA,IAAItK,CAAC,CAACwF,IAAI,GAAGyE,aAAa,CAACnF,CAAC,GAAGmF,aAAa,CAACxJ,MAAM,EAAE;MACjD,MAAM6J,eAAe,GAAGtK,CAAC,CAACwF,IAAI,GAAGyE,aAAa,CAACnF,CAAC,GAAGmF,aAAa,CAACxJ,MAAM;MACvE,MAAM8J,cAAc,GAAGH,UAAU,CAACM,mBAAmB,CAAC,CAAC,GAAG1K,CAAC,CAACiF,WAAW,CAACH,CAAC;MACzE,MAAMsC,YAAY,GAAG9I,cAAc,CAACqM,cAAc,CAAC,IAAI,CAAChL,QAAQ,EAAE4K,cAAc,CAAC;MACjF,IAAInD,YAAY,EAAE;QACd,MAAMwD,WAAW,GAAG,IAAI,CAACC,6BAA6B,CAACzD,YAAY,CAAC;QACpE,IAAIwD,WAAW,EAAE;UACb,OAAOrM,WAAW,CAACuM,mBAAmB,CAACT,WAAW,EAAEO,WAAW,EAAE,OAAO,EAAEN,eAAe,CAAC;QAC9F;MACJ;MACA,MAAMW,eAAe,GAAGb,UAAU,CAACY,6BAA6B,CAACT,cAAc,CAAC;MAChF,OAAOhM,WAAW,CAACuM,mBAAmB,CAACT,WAAW,EAAE,IAAIpL,QAAQ,CAACgM,eAAe,EAAEf,KAAK,CAACgB,gBAAgB,CAACD,eAAe,CAAC,CAAC,EAAE,OAAO,EAAEX,eAAe,CAAC;IACzJ;IACA,MAAMa,kBAAkB,GAAGf,UAAU,CAACY,6BAA6B,CAACZ,UAAU,CAACM,mBAAmB,CAAC,CAAC,GAAG1K,CAAC,CAACiF,WAAW,CAACH,CAAC,CAAC;IACvH,IAAI9E,CAAC,CAACuF,IAAI,GAAG0E,aAAa,CAAClF,CAAC,EAAE;MAC1B,MAAMuF,eAAe,GAAGL,aAAa,CAAClF,CAAC,GAAG/E,CAAC,CAACuF,IAAI;MAChD,OAAOhH,WAAW,CAACuM,mBAAmB,CAACT,WAAW,EAAE,IAAIpL,QAAQ,CAACkM,kBAAkB,EAAE,CAAC,CAAC,EAAE,MAAM,EAAEb,eAAe,CAAC;IACrH;IACA,IAAItK,CAAC,CAACuF,IAAI,GAAG0E,aAAa,CAAClF,CAAC,GAAGkF,aAAa,CAACjF,KAAK,EAAE;MAChD,MAAMsF,eAAe,GAAGtK,CAAC,CAACuF,IAAI,GAAG0E,aAAa,CAAClF,CAAC,GAAGkF,aAAa,CAACjF,KAAK;MACtE,OAAOzG,WAAW,CAACuM,mBAAmB,CAACT,WAAW,EAAE,IAAIpL,QAAQ,CAACkM,kBAAkB,EAAEjB,KAAK,CAACgB,gBAAgB,CAACC,kBAAkB,CAAC,CAAC,EAAE,OAAO,EAAEb,eAAe,CAAC;IAC/J;IACA,OAAO,IAAI;EACf;EACA3B,kBAAkBA,CAAC3I,CAAC,EAAEC,eAAe,EAAE;IACnC,MAAMmL,qBAAqB,GAAG,IAAI,CAACpB,yBAAyB,CAAChK,CAAC,CAAC;IAC/D,IAAIoL,qBAAqB,EAAE;MACvB,OAAOA,qBAAqB;IAChC;IACA,MAAM7E,CAAC,GAAG,IAAI,CAACrG,kBAAkB,CAACF,CAAC,EAAEC,eAAe,CAAC;IACrD,MAAMoL,cAAc,GAAG9E,CAAC,CAACyB,QAAQ;IACjC,IAAI,CAACqD,cAAc,EAAE;MACjB,OAAO,IAAI;IACf;IACA,IAAI9E,CAAC,CAACE,IAAI,KAAK,CAAC,CAAC,2CAA2CF,CAAC,CAACE,IAAI,KAAK,CAAC,CAAC,wCAAwC;MAC7G,MAAMmE,WAAW,GAAG,IAAI,CAACC,6BAA6B,CAACtE,CAAC,CAACc,MAAM,CAAC;MAChE,IAAIuD,WAAW,EAAE;QACb,OAAOrM,WAAW,CAAC+M,cAAc,CAAC/E,CAAC,CAACE,IAAI,EAAEF,CAAC,CAACgF,OAAO,EAAEhF,CAAC,CAAC8D,WAAW,EAAEO,WAAW,EAAErE,CAAC,CAACc,MAAM,CAAC;MAC9F;IACJ;IACA,OAAOd,CAAC;EACZ;EACAsE,6BAA6BA,CAACzD,YAAY,EAAE;IACxC;IACA,MAAMoE,cAAc,GAAG,IAAIvM,QAAQ,CAAC,IAAI,CAACqJ,iBAAiB,CAACmD,wBAAwB,EAAE,IAAI,CAACnD,iBAAiB,CAACoD,oBAAoB,CAAC;IACjI,MAAMC,cAAc,GAAGvE,YAAY,CAACuE,cAAc;IAClD,MAAMC,aAAa,GAAGxE,YAAY,CAACwE,aAAa;IAChD,IAAID,cAAc,IAAIC,aAAa,EAAE;MACjC,IAAID,cAAc,CAACE,QAAQ,CAACL,cAAc,CAAC,EAAE;QACzC,OAAOG,cAAc;MACzB,CAAC,MACI;QACD,OAAOC,aAAa;MACxB;IACJ;IACA,OAAO,IAAI;EACf;EACAzD,cAAcA,CAACH,QAAQ,EAAEC,eAAe,EAAEC,UAAU,EAAE;IAClD,IAAI,CAACF,QAAQ,CAACA,QAAQ,EAAE;MACpB;IACJ;IACA,IAAI,CAACN,eAAe,CAACoE,aAAa,CAAC;MAC/B9D,QAAQ,EAAEA,QAAQ,CAACA,QAAQ;MAC3BqC,WAAW,EAAErC,QAAQ,CAACqC,WAAW;MACjC0B,oBAAoB,EAAE,IAAI,CAAC3D,WAAW,CAAC2D,oBAAoB;MAC3D7D,UAAU;MACVD,eAAe,EAAEA,eAAe;MAChC+D,cAAc,EAAE,IAAI,CAAC5D,WAAW,CAACgB,KAAK;MACtCtF,MAAM,EAAE,IAAI,CAACsE,WAAW,CAACtE,MAAM;MAC/BF,OAAO,EAAE,IAAI,CAACwE,WAAW,CAACxE,OAAO;MACjCD,OAAO,EAAE,IAAI,CAACyE,WAAW,CAACzE,OAAO;MACjCE,QAAQ,EAAE,IAAI,CAACuE,WAAW,CAACvE,QAAQ;MACnCkD,UAAU,EAAE,IAAI,CAACqB,WAAW,CAACrB,UAAU;MACvCC,YAAY,EAAE,IAAI,CAACoB,WAAW,CAACpB,YAAY;MAC3CiF,cAAc,EAAEjE,QAAQ,CAACvB,IAAI,KAAK,CAAC,CAAC,sCAAsCuB,QAAQ,CAACX,MAAM,CAAC6E,YAAY,KAAK;IAC/G,CAAC,CAAC;EACN;AACJ;AACA,MAAMnE,sBAAsB,SAAS3J,UAAU,CAAC;EAC5CkB,WAAWA,CAACK,QAAQ,EAAEgI,WAAW,EAAEC,mBAAmB,EAAEO,cAAc,EAAE;IACpE,KAAK,CAAC,CAAC;IACP,IAAI,CAACxI,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACgI,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACO,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACgE,UAAU,GAAG,IAAI;EAC1B;EACApI,OAAOA,CAAA,EAAG;IACN,KAAK,CAACA,OAAO,CAAC,CAAC;IACf,IAAI,CAACgF,IAAI,CAAC,CAAC;EACf;EACA5B,KAAKA,CAACa,QAAQ,EAAEoE,UAAU,EAAE;IACxB,IAAI,IAAI,CAACD,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAACE,WAAW,CAACrE,QAAQ,EAAEoE,UAAU,CAAC;IACrD,CAAC,MACI;MACD,IAAI,CAACD,UAAU,GAAG,IAAIG,+BAA+B,CAAC,IAAI,CAAC3M,QAAQ,EAAE,IAAI,CAACgI,WAAW,EAAE,IAAI,CAACC,mBAAmB,EAAE,IAAI,CAACO,cAAc,EAAEH,QAAQ,EAAEoE,UAAU,CAAC;IAC/J;EACJ;EACArD,IAAIA,CAAA,EAAG;IACH,IAAI,IAAI,CAACoD,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAACpI,OAAO,CAAC,CAAC;MACzB,IAAI,CAACoI,UAAU,GAAG,IAAI;IAC1B;EACJ;AACJ;AACA,MAAMG,+BAA+B,SAASlO,UAAU,CAAC;EACrDkB,WAAWA,CAACK,QAAQ,EAAEgI,WAAW,EAAEC,mBAAmB,EAAEO,cAAc,EAAEH,QAAQ,EAAEoE,UAAU,EAAE;IAC1F,KAAK,CAAC,CAAC;IACP,IAAI,CAACzM,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACgI,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACO,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACoE,SAAS,GAAGvE,QAAQ;IACzB,IAAI,CAACwE,WAAW,GAAGJ,UAAU;IAC7B,IAAI,CAACK,SAAS,GAAGpJ,IAAI,CAACC,GAAG,CAAC,CAAC;IAC3B,IAAI,CAACoJ,yBAAyB,GAAGxO,GAAG,CAACyO,4BAA4B,CAACzO,GAAG,CAAC0G,SAAS,CAACwH,UAAU,CAAC1J,YAAY,CAAC,EAAE,MAAM,IAAI,CAACkK,QAAQ,CAAC,CAAC,CAAC;EACpI;EACA7I,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC2I,yBAAyB,CAAC3I,OAAO,CAAC,CAAC;IACxC,KAAK,CAACA,OAAO,CAAC,CAAC;EACnB;EACAsI,WAAWA,CAACrE,QAAQ,EAAEoE,UAAU,EAAE;IAC9B,IAAI,CAACG,SAAS,GAAGvE,QAAQ;IACzB,IAAI,CAACwE,WAAW,GAAGJ,UAAU;EACjC;EACA;AACJ;AACA;EACIS,KAAKA,CAAA,EAAG;IACJ,MAAMvJ,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;IACtB,MAAMwJ,OAAO,GAAGxJ,GAAG,GAAG,IAAI,CAACmJ,SAAS;IACpC,IAAI,CAACA,SAAS,GAAGnJ,GAAG;IACpB,OAAOwJ,OAAO;EAClB;EACA;AACJ;AACA;EACIC,eAAeA,CAAA,EAAG;IACd,MAAMC,UAAU,GAAG,IAAI,CAACrN,QAAQ,CAACW,aAAa,CAACC,OAAO,CAACC,GAAG,CAAC,EAAE,CAAC,6BAA6B,CAAC;IAC5F,MAAMyM,eAAe,GAAG,IAAI,CAACtN,QAAQ,CAACW,aAAa,CAACC,OAAO,CAACC,GAAG,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAACC,MAAM,GAAGuM,UAAU;IACtH,MAAME,sBAAsB,GAAG,IAAI,CAACX,SAAS,CAACjC,eAAe,GAAG0C,UAAU;IAC1E,IAAIE,sBAAsB,IAAI,GAAG,EAAE;MAC/B,OAAO1C,IAAI,CAACC,GAAG,CAAC,EAAE,EAAEwC,eAAe,IAAI,CAAC,GAAGC,sBAAsB,CAAC,CAAC;IACvE;IACA,IAAIA,sBAAsB,IAAI,CAAC,EAAE;MAC7B,OAAO1C,IAAI,CAACC,GAAG,CAAC,EAAE,EAAEwC,eAAe,IAAI,CAAC,GAAGC,sBAAsB,CAAC,CAAC;IACvE;IACA,OAAO1C,IAAI,CAACC,GAAG,CAAC,GAAG,EAAEwC,eAAe,IAAI,CAAC,GAAGC,sBAAsB,CAAC,CAAC;EACxE;EACAN,QAAQA,CAAA,EAAG;IACP,MAAMI,UAAU,GAAG,IAAI,CAACrN,QAAQ,CAACW,aAAa,CAACC,OAAO,CAACC,GAAG,CAAC,EAAE,CAAC,6BAA6B,CAAC;IAC5F,MAAM2M,kBAAkB,GAAG,IAAI,CAACJ,eAAe,CAAC,CAAC;IACjD,MAAMD,OAAO,GAAG,IAAI,CAACD,KAAK,CAAC,CAAC;IAC5B,MAAMO,cAAc,GAAGD,kBAAkB,IAAIL,OAAO,GAAG,IAAI,CAAC,GAAGE,UAAU;IACzE,MAAMK,WAAW,GAAI,IAAI,CAACd,SAAS,CAACzD,eAAe,KAAK,OAAO,GAAG,CAACsE,cAAc,GAAGA,cAAe;IACnG,IAAI,CAACzN,QAAQ,CAACwK,SAAS,CAACC,UAAU,CAACkD,cAAc,CAAC,CAAC,EAAED,WAAW,CAAC;IACjE,IAAI,CAAC1F,WAAW,CAAC4F,SAAS,CAAC,CAAC;IAC5B,MAAMC,YAAY,GAAG,IAAI,CAAC7N,QAAQ,CAACyK,UAAU,CAACqD,oBAAoB,CAAC,CAAC;IACpE,MAAMC,cAAc,GAAI,IAAI,CAACnB,SAAS,CAACzD,eAAe,KAAK,OAAO,GAAG0E,YAAY,CAACG,eAAe,GAAGH,YAAY,CAACI,aAAc;IAC/H;IACA,IAAIC,WAAW;IACf;MACI,MAAMhJ,SAAS,GAAGhG,wBAAwB,CAAC,IAAI,CAAC8I,WAAW,CAAChH,WAAW,CAAC;MACxE,MAAMmN,yBAAyB,GAAG,IAAI,CAACnO,QAAQ,CAACW,aAAa,CAACC,OAAO,CAACC,GAAG,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAACsN,yBAAyB;MACtI,MAAMpJ,GAAG,GAAG,IAAI3F,eAAe,CAAC,IAAI,CAACyN,WAAW,CAAC9H,GAAG,CAACK,CAAC,EAAEF,SAAS,CAACC,CAAC,GAAGD,SAAS,CAACpE,MAAM,GAAGqN,yBAAyB,GAAG,GAAG,CAAC;MACzH,MAAM7I,WAAW,GAAGnG,iCAAiC,CAAC,IAAI,CAAC6I,WAAW,CAAChH,WAAW,EAAEkE,SAAS,EAAEH,GAAG,CAAC;MACnGmJ,WAAW,GAAG,IAAI,CAACjG,mBAAmB,CAAC1C,iBAAiB,CAAC,IAAI,CAACyC,WAAW,CAACxC,iBAAiB,CAAC,CAAC,EAAEN,SAAS,EAAEH,GAAG,EAAEO,WAAW,EAAE,IAAI,CAAC;IACrI;IACA,IAAI,CAAC4I,WAAW,CAAC7F,QAAQ,IAAI6F,WAAW,CAAC7F,QAAQ,CAAC+F,UAAU,KAAKL,cAAc,EAAE;MAC7E,IAAI,IAAI,CAACnB,SAAS,CAACzD,eAAe,KAAK,OAAO,EAAE;QAC5C+E,WAAW,GAAGtP,WAAW,CAACuM,mBAAmB,CAAC,IAAI,CAACyB,SAAS,CAAClC,WAAW,EAAE,IAAIpL,QAAQ,CAACyO,cAAc,EAAE,CAAC,CAAC,EAAE,OAAO,EAAE,IAAI,CAACnB,SAAS,CAACjC,eAAe,CAAC;MACvJ,CAAC,MACI;QACDuD,WAAW,GAAGtP,WAAW,CAACuM,mBAAmB,CAAC,IAAI,CAACyB,SAAS,CAAClC,WAAW,EAAE,IAAIpL,QAAQ,CAACyO,cAAc,EAAE,IAAI,CAAC/N,QAAQ,CAACwK,SAAS,CAACe,gBAAgB,CAACwC,cAAc,CAAC,CAAC,EAAE,OAAO,EAAE,IAAI,CAACnB,SAAS,CAACjC,eAAe,CAAC;MAC9M;IACJ;IACA,IAAI,CAACnC,cAAc,CAAC0F,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC,sCAAsC,CAAC;IAChF,IAAI,CAACnB,yBAAyB,GAAGxO,GAAG,CAACyO,4BAA4B,CAACzO,GAAG,CAAC0G,SAAS,CAACiJ,WAAW,CAACtC,OAAO,CAAC,EAAE,MAAM,IAAI,CAACqB,QAAQ,CAAC,CAAC,CAAC;EAChI;AACJ;AAAC,IACKvE,cAAc;EAApB,MAAMA,cAAc,CAAC;IACjB;MAAS,IAAI,CAAC2F,2BAA2B,GAAG,GAAG;IAAE,CAAC,CAAC;IACnD,IAAIlK,MAAMA,CAAA,EAAG;MAAE,OAAO,IAAI,CAACmK,OAAO;IAAE;IACpC,IAAIrK,OAAOA,CAAA,EAAG;MAAE,OAAO,IAAI,CAACsK,QAAQ;IAAE;IACtC,IAAIvK,OAAOA,CAAA,EAAG;MAAE,OAAO,IAAI,CAACwK,QAAQ;IAAE;IACtC,IAAItK,QAAQA,CAAA,EAAG;MAAE,OAAO,IAAI,CAACuK,SAAS;IAAE;IACxC,IAAIrH,UAAUA,CAAA,EAAG;MAAE,OAAO,IAAI,CAACsH,WAAW;IAAE;IAC5C,IAAIrH,YAAYA,CAAA,EAAG;MAAE,OAAO,IAAI,CAACsH,aAAa;IAAE;IAChD,IAAIvC,oBAAoBA,CAAA,EAAG;MAAE,OAAO,IAAI,CAACwC,qBAAqB;IAAE;IAChEjP,WAAWA,CAAA,EAAG;MACV,IAAI,CAAC2O,OAAO,GAAG,KAAK;MACpB,IAAI,CAACC,QAAQ,GAAG,KAAK;MACrB,IAAI,CAACC,QAAQ,GAAG,KAAK;MACrB,IAAI,CAACC,SAAS,GAAG,KAAK;MACtB,IAAI,CAACC,WAAW,GAAG,KAAK;MACxB,IAAI,CAACC,aAAa,GAAG,KAAK;MAC1B,IAAI,CAACC,qBAAqB,GAAG,KAAK;MAClC,IAAI,CAACC,sBAAsB,GAAG,IAAI;MAClC,IAAI,CAACC,gCAAgC,GAAG,CAAC;MACzC,IAAI,CAACC,mBAAmB,GAAG,CAAC;MAC5B,IAAI,CAACC,0BAA0B,GAAG,CAAC;MACnC,IAAI,CAAC/F,aAAa,GAAG,KAAK;IAC9B;IACA,IAAIQ,KAAKA,CAAA,EAAG;MACR,OAAO,IAAI,CAACsF,mBAAmB;IACnC;IACAhG,YAAYA,CAACkG,MAAM,EAAE;MACjB,IAAI,CAACX,OAAO,GAAGW,MAAM,CAAC9K,MAAM;MAC5B,IAAI,CAACoK,QAAQ,GAAGU,MAAM,CAAChL,OAAO;MAC9B,IAAI,CAACuK,QAAQ,GAAGS,MAAM,CAACjL,OAAO;MAC9B,IAAI,CAACyK,SAAS,GAAGQ,MAAM,CAAC/K,QAAQ;IACpC;IACAqF,eAAeA,CAAC0F,MAAM,EAAE;MACpB,IAAI,CAACP,WAAW,GAAGO,MAAM,CAAC7H,UAAU;MACpC,IAAI,CAACuH,aAAa,GAAGM,MAAM,CAAC5H,YAAY;IAC5C;IACAiC,uBAAuBA,CAAC8C,oBAAoB,EAAE;MAC1C,IAAI,CAACwC,qBAAqB,GAAGxC,oBAAoB;IACrD;IACA5C,WAAWA,CAAC0F,iBAAiB,EAAEC,oBAAoB,EAAE;MACjD;MACA,MAAMC,WAAW,GAAI,IAAI1L,IAAI,CAAC,CAAC,CAAE+C,OAAO,CAAC,CAAC;MAC1C,IAAI2I,WAAW,GAAG,IAAI,CAACJ,0BAA0B,GAAGtG,cAAc,CAAC2F,2BAA2B,EAAE;QAC5Fa,iBAAiB,GAAG,CAAC;MACzB;MACA,IAAI,CAACF,0BAA0B,GAAGI,WAAW;MAC7C;MACA,IAAIF,iBAAiB,GAAG,IAAI,CAACH,mBAAmB,GAAG,CAAC,EAAE;QAClDG,iBAAiB,GAAG,IAAI,CAACH,mBAAmB,GAAG,CAAC;MACpD;MACA;MACA,IAAI,IAAI,CAACF,sBAAsB,IAAI,IAAI,CAACA,sBAAsB,CAACQ,MAAM,CAACF,oBAAoB,CAAC,EAAE;QACzF,IAAI,CAACL,gCAAgC,EAAE;MAC3C,CAAC,MACI;QACD,IAAI,CAACA,gCAAgC,GAAG,CAAC;MAC7C;MACA,IAAI,CAACD,sBAAsB,GAAGM,oBAAoB;MAClD;MACA,IAAI,CAACJ,mBAAmB,GAAGlE,IAAI,CAACyE,GAAG,CAACJ,iBAAiB,EAAE,IAAI,CAACJ,gCAAgC,CAAC;IACjG;EACJ;EAAC,OA7DKpG,cAAc;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}