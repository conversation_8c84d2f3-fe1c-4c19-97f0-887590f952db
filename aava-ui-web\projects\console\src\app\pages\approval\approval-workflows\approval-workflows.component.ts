import { CommonModule, formatDate } from '@angular/common';
import { Component, CUSTOM_ELEMENTS_SCHEMA, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import {
  ApprovalCardComponent,
  IconComponent,
  AvaTextboxComponent,
  TextCardComponent,
  PopupComponent,
  ConfirmationPopupComponent,
  AvaTagComponent,
  DropdownOption,
  ButtonComponent,
  DialogService,
  DialogButton,
} from '@ava/play-comp-library';
import approvalText from '../constants/approval.json';
import { ApprovalService } from '../../../shared/services/approval.service';
import { debounceTime, distinctUntilChanged, map, startWith } from 'rxjs';
import { DrawerService } from '../../../shared/services/drawer/drawer.service';
import { WorkflowService } from '../../../shared/services/workflow.service';
import { AgentsPreviewPanelComponent } from './agents-preview-panel/agents-preview-panel.component'; 
import { ApprovalTxtCardComponent } from '../approval-text-card/approval-text-card.component';

type RequestStatus = 'approved' | 'rejected' | 'review';

@Component({
  selector: 'app-approval-workflows',
  imports: [
    CommonModule,
    RouterModule,
    ApprovalCardComponent,
    IconComponent,
    AvaTextboxComponent,
    ReactiveFormsModule,
    AvaTagComponent,
    ButtonComponent,
    PopupComponent,
    ConfirmationPopupComponent,
    ApprovalTxtCardComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './approval-workflows.component.html',
  styleUrls: ['./approval-workflows.component.scss'],
})
export class ApprovalWorkflowsComponent implements OnInit {
  appLabels = approvalText.labels;
  public totalApprovedApprovals: number = 20;
  public totalPendingApprovals: number = 15;
  public totalApprovals: number = 60;
  public isBasicCollapsed: boolean = false;
  public quickActionsExpanded: boolean = true;
  public consoleApproval: any = {};
  public options: DropdownOption[] = [];
  public basicSidebarItems: any[] = [];
  public quickActions: any[] = [];
  public toolReviews: any[] = [];
  public workflowReviews: any[] = [];
  public filteredWorkflowReviews: any[] = [];
  public agentsReviews: any[] = [];
  public currentToolsPage = 1;
  public currentAgentsPage = 1;
  public currentWorkflowsPage = 1;
  public pageSize = 50;
  public totalRecords = 0;
  public isDeleted = false;
  public currentTab = 'Workflows';
  public selectedIndex = 0;
  public searchForm!: FormGroup;
  public labels: any = approvalText.labels;
  public approvedAgentId: number | null = null;
  public previewData: any = null;
  public selectedWorkflowId: number = 0;

  constructor(
    private router: Router,
    private approvalService: ApprovalService,
    private fb: FormBuilder,
    private workflowService: WorkflowService,
    private drawerService: DrawerService,
    private dialogService: DialogService
  ) {
    this.labels = approvalText.labels;
    this.options = [
      { name: this.labels.electronics, value: 'electronics' },
      { name: this.labels.clothing, value: 'clothing' },
      { name: this.labels.books, value: 'books' },
    ];
    this.basicSidebarItems = [
      {
        id: '1',
        icon: 'hammer',
        text: this.labels.agents,
        route: '',
        active: true,
      },
      { id: '2', icon: 'circle-check', text: this.labels.workflows, route: '' },
      { id: '3', icon: 'bot', text: this.labels.tools, route: '' },
    ];
    this.quickActions = [
      {
        icon: 'awe_agents',
        label: this.labels.agents,
        route: '',
      },
      {
        icon: 'awe_workflows',
        label: this.labels.workflows,
        route: '',
      },
      {
        icon: 'awe_tools',
        label: this.labels.tools,
        route: '',
      },
    ];
    this.searchForm = this.fb.group({
      search: [''],
    });
  }

  ngOnInit(): void {
    this.searchList();
    this.totalApprovals = 60;
    this.loadWorkflowReviews();
  }

  public searchList() {
    console.log(this.searchForm.get('search')?.value);
    this.searchForm
      .get('search')!
      .valueChanges.pipe(
        startWith(''),
        debounceTime(300),
        distinctUntilChanged(),
        map((value) => value?.toLowerCase() ?? ''),
      )
      .subscribe((searchText) => {
        this.applyFilter(searchText);
      });
  }

  public applyFilter(text: string) {
    const lower = text;
    if (!text) {
      this.updateConsoleApproval(this.workflowReviews, 'workflow');
      return;
    }

    this.filteredWorkflowReviews = this.workflowReviews.filter((item) =>
      item.workflowName?.toLowerCase().includes(lower),
    );
    this.updateConsoleApproval(this.filteredWorkflowReviews, 'workflow');
  }

  public onSelectionChange(data: any) {
    console.log('Selection changed:', data);
  }

  public uClick(i: any) {
    console.log('log' + i);
  }

  public toggleQuickActions(): void {
    this.quickActionsExpanded = !this.quickActionsExpanded;
  }

  public onBasicCollapseToggle(isCollapsed: boolean): void {
    this.isBasicCollapsed = isCollapsed;
    console.log('Basic sidebar collapsed:', isCollapsed);
  }

  public onBasicItemClick(item: any): void {
    this.basicSidebarItems.forEach((i) => (i.active = false));
    item.active = true;
    console.log(item);
  }

  public toRequestStatus(value: string | null | undefined): RequestStatus {
    return value === 'approved' || value === 'rejected' || value === 'review'
      ? value
      : 'review';
  }

  public handleMetaDataApproval(){
    this.showApprovalDialog();
  }

  public handeMetaDataSendback(){
    this.showFeedbackDialog();
  }

  public loadWorkflowReviews() {
    this.approvalService
      .getAllReviewWorkflows(
        this.currentWorkflowsPage,
        this.pageSize,
        this.isDeleted,
      )
      .subscribe((response) => {
        if (this.currentWorkflowsPage > 1) {
          this.workflowReviews = [
            ...this.workflowReviews,
            ...response.workflowReviewDetails,
          ];
        } else {
          this.workflowReviews = response?.workflowReviewDetails;
        }
        this.workflowReviews = this.workflowReviews.filter(
          (r) => r.status !== 'approved',
        );
        this.filteredWorkflowReviews = this.workflowReviews;
        this.totalRecords = this.workflowReviews.length;
        //   console.log('workflow reviews ', this.workflowReviews);
        this.updateConsoleApproval(this.workflowReviews, 'workflow');
      });
  }

  public loadMoreWorkflows(page: number) {
    this.currentWorkflowsPage = page;
    this.loadWorkflowReviews();
  }

  public loadReviews(name: string) {
    this.currentTab = name;
    this.loadWorkflowReviews();
  }

  public rejectApproval(idx: any) {
    console.log(idx);
    this.selectedIndex = idx;
  }

  public approveApproval(idx: any) {
    console.log(idx);
    this.selectedIndex = idx;
    console.log(this.filteredWorkflowReviews[this.selectedIndex]);
  }

  private showApprovalDialog(): void {
    this.dialogService.confirmation({
      title: this.labels.confirmApproval,
      message: `${this.labels.youAreAboutToApproveThis} Workflow. ${this.labels.itWillBeActiveAndAvailableIn} ${this.currentTab} ${this.labels.catalogueForUsersToExecute}`,
      confirmButtonText: this.labels.approve,
      cancelButtonText: 'Cancel',
      confirmButtonVariant: 'danger',
      icon:'circle-check'
    }).then(result => {
      if (result.confirmed) {
        this.handleApproval();
      }
    });
  }

  private showFeedbackDialog(): void {
     const customButtons: DialogButton[] = [
          { label: 'Cancel', variant: 'secondary', action: 'cancel' },
          { label: 'Send Back', variant: 'primary', action: 'sendback' }
        ];
    this.dialogService.feedback({
      title: 'Confirm Send Back',
      message: 'This Workflow will be send back for corrections and modification. Kindly comment what needs to be done.',
      buttons:customButtons,
    }).then(result => {
      if (result.confirmed && result.confirmed === true) {
        this.handleRejection(result.data);
      }
    });
  }

  public handleApproval() {
    this.handleWorkflowApproval();
  }

  public handleRejection(feedback: any) {
    this.handleWorkflowRejection(feedback);
  }

  public handleWorkflowApproval() {
    const workflowDetails = this.filteredWorkflowReviews[this.selectedIndex];
    const id = workflowDetails?.id;
    const workflowId = workflowDetails?.workflowId;
    const status = 'approved';
    const reviewedBy = workflowDetails?.reviewedBy;
    console.log(id, workflowId, status, reviewedBy);

    // Show loading dialog
    this.dialogService.loading({
      title: 'Approving Workflow...',
      message: 'Please wait while we approve the workflow.',
      showProgress: false,
      showCancelButton: false
    });

    this.approvalService
      .approveWorkflow(id, workflowId, status, reviewedBy)
      .subscribe({
        next: (response: any) => {
          this.dialogService.close(); // Close loading dialog
          
          const message = response?.message || this.labels.workflowSuccessApproveMessage;
          this.dialogService.success({
            title: 'Workflow Approved',
            message: message
          }).then(result => {
            if (result.action === 'secondary') {
              // Navigate to edit workflow screen
              this.router.navigate(['/build/workflows/edit', workflowId]);
            } else {
              this.loadWorkflowReviews(); // Refresh the list
            }
          });
        },
        error: (error) => {
          this.dialogService.close(); // Close loading dialog
          
          const errorMessage = error?.error?.message || this.labels.defaultErrorMessage;
          this.dialogService.error({
            title: 'Approval Failed',
            message: errorMessage,
            showRetryButton: true,
            retryButtonText: 'Retry'
          }).then(result => {
            if (result.action === 'retry') {
              this.handleWorkflowApproval();
            }
          });
        },
      });
  }

  public handleWorkflowRejection(feedback: any) {
    const workflowDetails = this.workflowReviews[this.selectedIndex];
    const id = workflowDetails?.id;
    const workflowId = workflowDetails?.workflowId;
    const status = 'rejected';
    const reviewedBy = workflowDetails?.reviewedBy;
    const message = feedback;
    console.log(id, workflowId, status, reviewedBy, message);

    // Show loading dialog
    this.dialogService.loading({
      title: 'Rejecting Workflow...',
      message: 'Please wait while we reject the workflow.',
      showProgress: false,
      showCancelButton: false
    });

    this.approvalService
      .rejectWorkflow(id, workflowId, status, reviewedBy, message)
      .subscribe({
        next: (response: any) => {
          this.dialogService.close(); // Close loading dialog
          
          const message = response?.message || this.labels.workflowSuccessRejectMessage;
          this.dialogService.success({
            title: 'Workflow Rejected',
            message: message
          }).then(() => {
            this.loadWorkflowReviews(); // Refresh the list
          });
        },
        error: (error) => {
          this.dialogService.close(); // Close loading dialog
          
          const errorMessage = error?.error?.message || this.labels.defaultErrorMessage;
          this.dialogService.error({
            title: 'Rejection Failed',
            message: errorMessage,
            showRetryButton: true,
            retryButtonText: 'Retry'
          }).then(result => {
            if (result.action === 'retry') {
              this.handleWorkflowRejection(feedback);
            }
          });
        },
      });
  }

  public handleTesting(index : any){
    console.log(index);
    const workflowId = this.filteredWorkflowReviews[index].workflowId;
    this.selectedWorkflowId = workflowId;
  }

  public onCardClick(index: number): void {
    console.log('Selected card index:', index);
    this.selectedIndex = index;
    const selectedWorkflow = this.filteredWorkflowReviews[this.selectedIndex];
    this.selectedWorkflowId = selectedWorkflow.workflowId;
    this.loadPreviewData(selectedWorkflow);
    console.log(selectedWorkflow);
    this.drawerService.open(AgentsPreviewPanelComponent, {
      previewData: this.previewData,
      closePreview: () => this.drawerService.clear(),
      editWorkflow: () => this.handleEditWorkflow(selectedWorkflow.workflowId),
      rejectApproval: () => this.handeMetaDataSendback(),
      approveApproval: () => this.handleMetaDataApproval(),
      testApproval: () => this.redirectToWorkflowPlayground(),
    });
  }

  public loadPreviewData(selectedWorkflow: any){
    this.previewData = {
      type: 'workflow',
      title: selectedWorkflow.workflowName,
      data: selectedWorkflow,
      loading: true,
      error: null,
    };
    this.workflowService.getWorkflowById(selectedWorkflow.workflowId).subscribe({
      next: (response) => {
        console.log('Workflow details', response);
        this.previewData.data = response;
        this.previewData.loading = false;
        this.previewData.error = null;
      },
      error: (error) => {
        console.error('Error:', error);
      },
    });
  }

  public redirectToWorkflowPlayground(): void {
    this.drawerService.clear();
    this.router.navigate(['/build/workflows/execute', this.selectedWorkflowId]);
  }

  public handleEditWorkflow(workflowId: string) {
    console.log('Edit Workflow', workflowId);
    this.drawerService.clear();
    this.router.navigate(['/build/workflows/edit', workflowId]);
  }

  public updateConsoleApproval(data: any[], type: string) {
    this.consoleApproval = {
      contents: data?.map((req: any) => {
        const statusIcons: Record<RequestStatus, string> = {
          approved: 'circle-check-big',
          rejected: 'circle-x',
          review: 'clock',
        };
        const statusTexts: Record<RequestStatus, string> = {
          approved: this.labels.approved,
          rejected: this.labels.rejected,
          review: this.labels.review,
        };
        const statusKey = this.toRequestStatus(req?.status);
        const specificId = req.workflowId;
        const title = req.workflowName;

        return {
          id: req.id,
          refId: specificId,
          type: type,
          session1: {
            title: title,
            labels: [
              {
                name: type,
                color: 'success',
                background: 'red',
                type: 'normal',
              },
              {
                name: req.changeRequestType,
                color: req.changeRequestType === 'update' ? 'error' : 'info',
                background: 'red',
                type: 'pill',
              },
            ],
          },
          session2: [
            {
              name: type,
              color: 'default',
              background: 'red',
              type: 'normal',
            },
            {
              name: req.status,
              color: 'default',
              background: 'red',
              type: 'normal',
            },
          ],
          session3: [
            {
              iconName: 'user',
              label: req.requestedBy,
            },
            {
              iconName: 'calendar-days',
              label: formatDate(req?.requestedAt, 'dd MMM yyyy', 'en-IN'),
            },
          ],
          session4: {
            status: statusTexts[statusKey],
            iconName: statusIcons[statusKey],
          },
        };
      }),
      footer: {},
    };
  }
}
