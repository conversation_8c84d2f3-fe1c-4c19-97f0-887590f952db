{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { $, addDisposableListener, append, EventHelper, EventType, isMouseEvent } from '../../dom.js';\nimport { StandardKeyboardEvent } from '../../keyboardEvent.js';\nimport { EventType as GestureEventType, Gesture } from '../../touch.js';\nimport { ActionRunner } from '../../../common/actions.js';\nimport { Emitter } from '../../../common/event.js';\nimport './dropdown.css';\nclass BaseDropdown extends ActionRunner {\n  constructor(container, options) {\n    super();\n    this._onDidChangeVisibility = this._register(new Emitter());\n    this.onDidChangeVisibility = this._onDidChangeVisibility.event;\n    this._element = append(container, $('.monaco-dropdown'));\n    this._label = append(this._element, $('.dropdown-label'));\n    let labelRenderer = options.labelRenderer;\n    if (!labelRenderer) {\n      labelRenderer = container => {\n        container.textContent = options.label || '';\n        return null;\n      };\n    }\n    for (const event of [EventType.CLICK, EventType.MOUSE_DOWN, GestureEventType.Tap]) {\n      this._register(addDisposableListener(this.element, event, e => EventHelper.stop(e, true))); // prevent default click behaviour to trigger\n    }\n    for (const event of [EventType.MOUSE_DOWN, GestureEventType.Tap]) {\n      this._register(addDisposableListener(this._label, event, e => {\n        if (isMouseEvent(e) && (e.detail > 1 || e.button !== 0)) {\n          // prevent right click trigger to allow separate context menu (https://github.com/microsoft/vscode/issues/151064)\n          // prevent multiple clicks to open multiple context menus (https://github.com/microsoft/vscode/issues/41363)\n          return;\n        }\n        if (this.visible) {\n          this.hide();\n        } else {\n          this.show();\n        }\n      }));\n    }\n    this._register(addDisposableListener(this._label, EventType.KEY_UP, e => {\n      const event = new StandardKeyboardEvent(e);\n      if (event.equals(3 /* KeyCode.Enter */) || event.equals(10 /* KeyCode.Space */)) {\n        EventHelper.stop(e, true); // https://github.com/microsoft/vscode/issues/57997\n        if (this.visible) {\n          this.hide();\n        } else {\n          this.show();\n        }\n      }\n    }));\n    const cleanupFn = labelRenderer(this._label);\n    if (cleanupFn) {\n      this._register(cleanupFn);\n    }\n    this._register(Gesture.addTarget(this._label));\n  }\n  get element() {\n    return this._element;\n  }\n  show() {\n    if (!this.visible) {\n      this.visible = true;\n      this._onDidChangeVisibility.fire(true);\n    }\n  }\n  hide() {\n    if (this.visible) {\n      this.visible = false;\n      this._onDidChangeVisibility.fire(false);\n    }\n  }\n  dispose() {\n    super.dispose();\n    this.hide();\n    if (this.boxContainer) {\n      this.boxContainer.remove();\n      this.boxContainer = undefined;\n    }\n    if (this.contents) {\n      this.contents.remove();\n      this.contents = undefined;\n    }\n    if (this._label) {\n      this._label.remove();\n      this._label = undefined;\n    }\n  }\n}\nexport class DropdownMenu extends BaseDropdown {\n  constructor(container, _options) {\n    super(container, _options);\n    this._options = _options;\n    this._actions = [];\n    this.actions = _options.actions || [];\n  }\n  set menuOptions(options) {\n    this._menuOptions = options;\n  }\n  get menuOptions() {\n    return this._menuOptions;\n  }\n  get actions() {\n    if (this._options.actionProvider) {\n      return this._options.actionProvider.getActions();\n    }\n    return this._actions;\n  }\n  set actions(actions) {\n    this._actions = actions;\n  }\n  show() {\n    super.show();\n    this.element.classList.add('active');\n    this._options.contextMenuProvider.showContextMenu({\n      getAnchor: () => this.element,\n      getActions: () => this.actions,\n      getActionsContext: () => this.menuOptions ? this.menuOptions.context : null,\n      getActionViewItem: (action, options) => this.menuOptions && this.menuOptions.actionViewItemProvider ? this.menuOptions.actionViewItemProvider(action, options) : undefined,\n      getKeyBinding: action => this.menuOptions && this.menuOptions.getKeyBinding ? this.menuOptions.getKeyBinding(action) : undefined,\n      getMenuClassName: () => this._options.menuClassName || '',\n      onHide: () => this.onHide(),\n      actionRunner: this.menuOptions ? this.menuOptions.actionRunner : undefined,\n      anchorAlignment: this.menuOptions ? this.menuOptions.anchorAlignment : 0 /* AnchorAlignment.LEFT */,\n      domForShadowRoot: this._options.menuAsChild ? this.element : undefined,\n      skipTelemetry: this._options.skipTelemetry\n    });\n  }\n  hide() {\n    super.hide();\n  }\n  onHide() {\n    this.hide();\n    this.element.classList.remove('active');\n  }\n}", "map": {"version": 3, "names": ["$", "addDisposableListener", "append", "EventHelper", "EventType", "isMouseEvent", "StandardKeyboardEvent", "GestureEventType", "Gesture", "ActionRunner", "Emitter", "BaseDropdown", "constructor", "container", "options", "_onDidChangeVisibility", "_register", "onDidChangeVisibility", "event", "_element", "_label", "labelRenderer", "textContent", "label", "CLICK", "MOUSE_DOWN", "Tap", "element", "e", "stop", "detail", "button", "visible", "hide", "show", "KEY_UP", "equals", "cleanupFn", "addTarget", "fire", "dispose", "boxContainer", "remove", "undefined", "contents", "DropdownMenu", "_options", "_actions", "actions", "menuOptions", "_menuOptions", "actionProvider", "getActions", "classList", "add", "contextMenuProvider", "showContextMenu", "getAnchor", "getActionsContext", "context", "getActionViewItem", "action", "actionViewItemProvider", "getKeyBinding", "getMenuClassName", "menuClassName", "onHide", "actionRunner", "anchorAlignment", "domForShadowRoot", "menuAsChild", "skipTelemetry"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/browser/ui/dropdown/dropdown.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { $, addDisposableListener, append, EventHelper, EventType, isMouseEvent } from '../../dom.js';\nimport { StandardKeyboardEvent } from '../../keyboardEvent.js';\nimport { EventType as GestureEventType, Gesture } from '../../touch.js';\nimport { ActionRunner } from '../../../common/actions.js';\nimport { Emitter } from '../../../common/event.js';\nimport './dropdown.css';\nclass BaseDropdown extends ActionRunner {\n    constructor(container, options) {\n        super();\n        this._onDidChangeVisibility = this._register(new Emitter());\n        this.onDidChangeVisibility = this._onDidChangeVisibility.event;\n        this._element = append(container, $('.monaco-dropdown'));\n        this._label = append(this._element, $('.dropdown-label'));\n        let labelRenderer = options.labelRenderer;\n        if (!labelRenderer) {\n            labelRenderer = (container) => {\n                container.textContent = options.label || '';\n                return null;\n            };\n        }\n        for (const event of [EventType.CLICK, EventType.MOUSE_DOWN, GestureEventType.Tap]) {\n            this._register(addDisposableListener(this.element, event, e => EventHelper.stop(e, true))); // prevent default click behaviour to trigger\n        }\n        for (const event of [EventType.MOUSE_DOWN, GestureEventType.Tap]) {\n            this._register(addDisposableListener(this._label, event, e => {\n                if (isMouseEvent(e) && (e.detail > 1 || e.button !== 0)) {\n                    // prevent right click trigger to allow separate context menu (https://github.com/microsoft/vscode/issues/151064)\n                    // prevent multiple clicks to open multiple context menus (https://github.com/microsoft/vscode/issues/41363)\n                    return;\n                }\n                if (this.visible) {\n                    this.hide();\n                }\n                else {\n                    this.show();\n                }\n            }));\n        }\n        this._register(addDisposableListener(this._label, EventType.KEY_UP, e => {\n            const event = new StandardKeyboardEvent(e);\n            if (event.equals(3 /* KeyCode.Enter */) || event.equals(10 /* KeyCode.Space */)) {\n                EventHelper.stop(e, true); // https://github.com/microsoft/vscode/issues/57997\n                if (this.visible) {\n                    this.hide();\n                }\n                else {\n                    this.show();\n                }\n            }\n        }));\n        const cleanupFn = labelRenderer(this._label);\n        if (cleanupFn) {\n            this._register(cleanupFn);\n        }\n        this._register(Gesture.addTarget(this._label));\n    }\n    get element() {\n        return this._element;\n    }\n    show() {\n        if (!this.visible) {\n            this.visible = true;\n            this._onDidChangeVisibility.fire(true);\n        }\n    }\n    hide() {\n        if (this.visible) {\n            this.visible = false;\n            this._onDidChangeVisibility.fire(false);\n        }\n    }\n    dispose() {\n        super.dispose();\n        this.hide();\n        if (this.boxContainer) {\n            this.boxContainer.remove();\n            this.boxContainer = undefined;\n        }\n        if (this.contents) {\n            this.contents.remove();\n            this.contents = undefined;\n        }\n        if (this._label) {\n            this._label.remove();\n            this._label = undefined;\n        }\n    }\n}\nexport class DropdownMenu extends BaseDropdown {\n    constructor(container, _options) {\n        super(container, _options);\n        this._options = _options;\n        this._actions = [];\n        this.actions = _options.actions || [];\n    }\n    set menuOptions(options) {\n        this._menuOptions = options;\n    }\n    get menuOptions() {\n        return this._menuOptions;\n    }\n    get actions() {\n        if (this._options.actionProvider) {\n            return this._options.actionProvider.getActions();\n        }\n        return this._actions;\n    }\n    set actions(actions) {\n        this._actions = actions;\n    }\n    show() {\n        super.show();\n        this.element.classList.add('active');\n        this._options.contextMenuProvider.showContextMenu({\n            getAnchor: () => this.element,\n            getActions: () => this.actions,\n            getActionsContext: () => this.menuOptions ? this.menuOptions.context : null,\n            getActionViewItem: (action, options) => this.menuOptions && this.menuOptions.actionViewItemProvider ? this.menuOptions.actionViewItemProvider(action, options) : undefined,\n            getKeyBinding: action => this.menuOptions && this.menuOptions.getKeyBinding ? this.menuOptions.getKeyBinding(action) : undefined,\n            getMenuClassName: () => this._options.menuClassName || '',\n            onHide: () => this.onHide(),\n            actionRunner: this.menuOptions ? this.menuOptions.actionRunner : undefined,\n            anchorAlignment: this.menuOptions ? this.menuOptions.anchorAlignment : 0 /* AnchorAlignment.LEFT */,\n            domForShadowRoot: this._options.menuAsChild ? this.element : undefined,\n            skipTelemetry: this._options.skipTelemetry\n        });\n    }\n    hide() {\n        super.hide();\n    }\n    onHide() {\n        this.hide();\n        this.element.classList.remove('active');\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,CAAC,EAAEC,qBAAqB,EAAEC,MAAM,EAAEC,WAAW,EAAEC,SAAS,EAAEC,YAAY,QAAQ,cAAc;AACrG,SAASC,qBAAqB,QAAQ,wBAAwB;AAC9D,SAASF,SAAS,IAAIG,gBAAgB,EAAEC,OAAO,QAAQ,gBAAgB;AACvE,SAASC,YAAY,QAAQ,4BAA4B;AACzD,SAASC,OAAO,QAAQ,0BAA0B;AAClD,OAAO,gBAAgB;AACvB,MAAMC,YAAY,SAASF,YAAY,CAAC;EACpCG,WAAWA,CAACC,SAAS,EAAEC,OAAO,EAAE;IAC5B,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,sBAAsB,GAAG,IAAI,CAACC,SAAS,CAAC,IAAIN,OAAO,CAAC,CAAC,CAAC;IAC3D,IAAI,CAACO,qBAAqB,GAAG,IAAI,CAACF,sBAAsB,CAACG,KAAK;IAC9D,IAAI,CAACC,QAAQ,GAAGjB,MAAM,CAACW,SAAS,EAAEb,CAAC,CAAC,kBAAkB,CAAC,CAAC;IACxD,IAAI,CAACoB,MAAM,GAAGlB,MAAM,CAAC,IAAI,CAACiB,QAAQ,EAAEnB,CAAC,CAAC,iBAAiB,CAAC,CAAC;IACzD,IAAIqB,aAAa,GAAGP,OAAO,CAACO,aAAa;IACzC,IAAI,CAACA,aAAa,EAAE;MAChBA,aAAa,GAAIR,SAAS,IAAK;QAC3BA,SAAS,CAACS,WAAW,GAAGR,OAAO,CAACS,KAAK,IAAI,EAAE;QAC3C,OAAO,IAAI;MACf,CAAC;IACL;IACA,KAAK,MAAML,KAAK,IAAI,CAACd,SAAS,CAACoB,KAAK,EAAEpB,SAAS,CAACqB,UAAU,EAAElB,gBAAgB,CAACmB,GAAG,CAAC,EAAE;MAC/E,IAAI,CAACV,SAAS,CAACf,qBAAqB,CAAC,IAAI,CAAC0B,OAAO,EAAET,KAAK,EAAEU,CAAC,IAAIzB,WAAW,CAAC0B,IAAI,CAACD,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IAChG;IACA,KAAK,MAAMV,KAAK,IAAI,CAACd,SAAS,CAACqB,UAAU,EAAElB,gBAAgB,CAACmB,GAAG,CAAC,EAAE;MAC9D,IAAI,CAACV,SAAS,CAACf,qBAAqB,CAAC,IAAI,CAACmB,MAAM,EAAEF,KAAK,EAAEU,CAAC,IAAI;QAC1D,IAAIvB,YAAY,CAACuB,CAAC,CAAC,KAAKA,CAAC,CAACE,MAAM,GAAG,CAAC,IAAIF,CAAC,CAACG,MAAM,KAAK,CAAC,CAAC,EAAE;UACrD;UACA;UACA;QACJ;QACA,IAAI,IAAI,CAACC,OAAO,EAAE;UACd,IAAI,CAACC,IAAI,CAAC,CAAC;QACf,CAAC,MACI;UACD,IAAI,CAACC,IAAI,CAAC,CAAC;QACf;MACJ,CAAC,CAAC,CAAC;IACP;IACA,IAAI,CAAClB,SAAS,CAACf,qBAAqB,CAAC,IAAI,CAACmB,MAAM,EAAEhB,SAAS,CAAC+B,MAAM,EAAEP,CAAC,IAAI;MACrE,MAAMV,KAAK,GAAG,IAAIZ,qBAAqB,CAACsB,CAAC,CAAC;MAC1C,IAAIV,KAAK,CAACkB,MAAM,CAAC,CAAC,CAAC,mBAAmB,CAAC,IAAIlB,KAAK,CAACkB,MAAM,CAAC,EAAE,CAAC,mBAAmB,CAAC,EAAE;QAC7EjC,WAAW,CAAC0B,IAAI,CAACD,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;QAC3B,IAAI,IAAI,CAACI,OAAO,EAAE;UACd,IAAI,CAACC,IAAI,CAAC,CAAC;QACf,CAAC,MACI;UACD,IAAI,CAACC,IAAI,CAAC,CAAC;QACf;MACJ;IACJ,CAAC,CAAC,CAAC;IACH,MAAMG,SAAS,GAAGhB,aAAa,CAAC,IAAI,CAACD,MAAM,CAAC;IAC5C,IAAIiB,SAAS,EAAE;MACX,IAAI,CAACrB,SAAS,CAACqB,SAAS,CAAC;IAC7B;IACA,IAAI,CAACrB,SAAS,CAACR,OAAO,CAAC8B,SAAS,CAAC,IAAI,CAAClB,MAAM,CAAC,CAAC;EAClD;EACA,IAAIO,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACR,QAAQ;EACxB;EACAe,IAAIA,CAAA,EAAG;IACH,IAAI,CAAC,IAAI,CAACF,OAAO,EAAE;MACf,IAAI,CAACA,OAAO,GAAG,IAAI;MACnB,IAAI,CAACjB,sBAAsB,CAACwB,IAAI,CAAC,IAAI,CAAC;IAC1C;EACJ;EACAN,IAAIA,CAAA,EAAG;IACH,IAAI,IAAI,CAACD,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,GAAG,KAAK;MACpB,IAAI,CAACjB,sBAAsB,CAACwB,IAAI,CAAC,KAAK,CAAC;IAC3C;EACJ;EACAC,OAAOA,CAAA,EAAG;IACN,KAAK,CAACA,OAAO,CAAC,CAAC;IACf,IAAI,CAACP,IAAI,CAAC,CAAC;IACX,IAAI,IAAI,CAACQ,YAAY,EAAE;MACnB,IAAI,CAACA,YAAY,CAACC,MAAM,CAAC,CAAC;MAC1B,IAAI,CAACD,YAAY,GAAGE,SAAS;IACjC;IACA,IAAI,IAAI,CAACC,QAAQ,EAAE;MACf,IAAI,CAACA,QAAQ,CAACF,MAAM,CAAC,CAAC;MACtB,IAAI,CAACE,QAAQ,GAAGD,SAAS;IAC7B;IACA,IAAI,IAAI,CAACvB,MAAM,EAAE;MACb,IAAI,CAACA,MAAM,CAACsB,MAAM,CAAC,CAAC;MACpB,IAAI,CAACtB,MAAM,GAAGuB,SAAS;IAC3B;EACJ;AACJ;AACA,OAAO,MAAME,YAAY,SAASlC,YAAY,CAAC;EAC3CC,WAAWA,CAACC,SAAS,EAAEiC,QAAQ,EAAE;IAC7B,KAAK,CAACjC,SAAS,EAAEiC,QAAQ,CAAC;IAC1B,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,OAAO,GAAGF,QAAQ,CAACE,OAAO,IAAI,EAAE;EACzC;EACA,IAAIC,WAAWA,CAACnC,OAAO,EAAE;IACrB,IAAI,CAACoC,YAAY,GAAGpC,OAAO;EAC/B;EACA,IAAImC,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACC,YAAY;EAC5B;EACA,IAAIF,OAAOA,CAAA,EAAG;IACV,IAAI,IAAI,CAACF,QAAQ,CAACK,cAAc,EAAE;MAC9B,OAAO,IAAI,CAACL,QAAQ,CAACK,cAAc,CAACC,UAAU,CAAC,CAAC;IACpD;IACA,OAAO,IAAI,CAACL,QAAQ;EACxB;EACA,IAAIC,OAAOA,CAACA,OAAO,EAAE;IACjB,IAAI,CAACD,QAAQ,GAAGC,OAAO;EAC3B;EACAd,IAAIA,CAAA,EAAG;IACH,KAAK,CAACA,IAAI,CAAC,CAAC;IACZ,IAAI,CAACP,OAAO,CAAC0B,SAAS,CAACC,GAAG,CAAC,QAAQ,CAAC;IACpC,IAAI,CAACR,QAAQ,CAACS,mBAAmB,CAACC,eAAe,CAAC;MAC9CC,SAAS,EAAEA,CAAA,KAAM,IAAI,CAAC9B,OAAO;MAC7ByB,UAAU,EAAEA,CAAA,KAAM,IAAI,CAACJ,OAAO;MAC9BU,iBAAiB,EAAEA,CAAA,KAAM,IAAI,CAACT,WAAW,GAAG,IAAI,CAACA,WAAW,CAACU,OAAO,GAAG,IAAI;MAC3EC,iBAAiB,EAAEA,CAACC,MAAM,EAAE/C,OAAO,KAAK,IAAI,CAACmC,WAAW,IAAI,IAAI,CAACA,WAAW,CAACa,sBAAsB,GAAG,IAAI,CAACb,WAAW,CAACa,sBAAsB,CAACD,MAAM,EAAE/C,OAAO,CAAC,GAAG6B,SAAS;MAC1KoB,aAAa,EAAEF,MAAM,IAAI,IAAI,CAACZ,WAAW,IAAI,IAAI,CAACA,WAAW,CAACc,aAAa,GAAG,IAAI,CAACd,WAAW,CAACc,aAAa,CAACF,MAAM,CAAC,GAAGlB,SAAS;MAChIqB,gBAAgB,EAAEA,CAAA,KAAM,IAAI,CAAClB,QAAQ,CAACmB,aAAa,IAAI,EAAE;MACzDC,MAAM,EAAEA,CAAA,KAAM,IAAI,CAACA,MAAM,CAAC,CAAC;MAC3BC,YAAY,EAAE,IAAI,CAAClB,WAAW,GAAG,IAAI,CAACA,WAAW,CAACkB,YAAY,GAAGxB,SAAS;MAC1EyB,eAAe,EAAE,IAAI,CAACnB,WAAW,GAAG,IAAI,CAACA,WAAW,CAACmB,eAAe,GAAG,CAAC,CAAC;MACzEC,gBAAgB,EAAE,IAAI,CAACvB,QAAQ,CAACwB,WAAW,GAAG,IAAI,CAAC3C,OAAO,GAAGgB,SAAS;MACtE4B,aAAa,EAAE,IAAI,CAACzB,QAAQ,CAACyB;IACjC,CAAC,CAAC;EACN;EACAtC,IAAIA,CAAA,EAAG;IACH,KAAK,CAACA,IAAI,CAAC,CAAC;EAChB;EACAiC,MAAMA,CAAA,EAAG;IACL,IAAI,CAACjC,IAAI,CAAC,CAAC;IACX,IAAI,CAACN,OAAO,CAAC0B,SAAS,CAACX,MAAM,CAAC,QAAQ,CAAC;EAC3C;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}