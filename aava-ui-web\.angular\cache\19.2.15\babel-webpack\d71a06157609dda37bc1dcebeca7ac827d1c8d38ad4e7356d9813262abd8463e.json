{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { TreeSitterTokenizationRegistry } from '../languages.js';\nimport { LineTokens } from '../tokens/lineTokens.js';\nimport { AbstractTokens } from './tokens.js';\nexport class TreeSitterTokens extends AbstractTokens {\n  constructor(_treeSitterService, languageIdCodec, textModel, languageId) {\n    super(languageIdCodec, textModel, languageId);\n    this._treeSitterService = _treeSitterService;\n    this._tokenizationSupport = null;\n    this._initialize();\n  }\n  _initialize() {\n    const newLanguage = this.getLanguageId();\n    if (!this._tokenizationSupport || this._lastLanguageId !== newLanguage) {\n      this._lastLanguageId = newLanguage;\n      this._tokenizationSupport = TreeSitterTokenizationRegistry.get(newLanguage);\n    }\n  }\n  getLineTokens(lineNumber) {\n    const content = this._textModel.getLineContent(lineNumber);\n    if (this._tokenizationSupport) {\n      const rawTokens = this._tokenizationSupport.tokenizeEncoded(lineNumber, this._textModel);\n      if (rawTokens) {\n        return new LineTokens(rawTokens, content, this._languageIdCodec);\n      }\n    }\n    return LineTokens.createEmpty(content, this._languageIdCodec);\n  }\n  resetTokenization(fireTokenChangeEvent = true) {\n    if (fireTokenChangeEvent) {\n      this._onDidChangeTokens.fire({\n        semanticTokensApplied: false,\n        ranges: [{\n          fromLineNumber: 1,\n          toLineNumber: this._textModel.getLineCount()\n        }]\n      });\n    }\n    this._initialize();\n  }\n  handleDidChangeAttached() {\n    // TODO @alexr00 implement for background tokenization\n  }\n  handleDidChangeContent(e) {\n    if (e.isFlush) {\n      // Don't fire the event, as the view might not have got the text change event yet\n      this.resetTokenization(false);\n    }\n  }\n  forceTokenization(lineNumber) {\n    // TODO @alexr00 implement\n  }\n  hasAccurateTokensForLine(lineNumber) {\n    // TODO @alexr00 update for background tokenization\n    return true;\n  }\n  isCheapToTokenize(lineNumber) {\n    // TODO @alexr00 update for background tokenization\n    return true;\n  }\n  getTokenTypeIfInsertingCharacter(lineNumber, column, character) {\n    // TODO @alexr00 implement once we have custom parsing and don't just feed in the whole text model value\n    return 0 /* StandardTokenType.Other */;\n  }\n  tokenizeLineWithEdit(position, length, newText) {\n    // TODO @alexr00 understand what this is for and implement\n    return null;\n  }\n  get hasTokens() {\n    // TODO @alexr00 once we have a token store, implement properly\n    const hasTree = this._treeSitterService.getParseResult(this._textModel) !== undefined;\n    return hasTree;\n  }\n}", "map": {"version": 3, "names": ["TreeSitterTokenizationRegistry", "LineTokens", "AbstractTokens", "TreeSitterTokens", "constructor", "_treeSitterService", "languageIdCodec", "textModel", "languageId", "_tokenizationSupport", "_initialize", "newLanguage", "getLanguageId", "_lastLanguageId", "get", "getLineTokens", "lineNumber", "content", "_textModel", "get<PERSON>ineC<PERSON>nt", "rawTokens", "tokenizeEncoded", "_languageIdCodec", "createEmpty", "resetTokenization", "fireTokenChangeEvent", "_onDidChangeTokens", "fire", "semanticTokensApplied", "ranges", "fromLineNumber", "toLineNumber", "getLineCount", "handleDidChangeAttached", "handleDidChangeContent", "e", "isFlush", "forceTokenization", "hasAccurateTokensForLine", "isCheapToTokenize", "getTokenTypeIfInsertingCharacter", "column", "character", "tokenizeLineWithEdit", "position", "length", "newText", "hasTokens", "hasTree", "getParseResult", "undefined"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/model/treeSitterTokens.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { TreeSitterTokenizationRegistry } from '../languages.js';\nimport { LineTokens } from '../tokens/lineTokens.js';\nimport { AbstractTokens } from './tokens.js';\nexport class TreeSitterTokens extends AbstractTokens {\n    constructor(_treeSitterService, languageIdCodec, textModel, languageId) {\n        super(languageIdCodec, textModel, languageId);\n        this._treeSitterService = _treeSitterService;\n        this._tokenizationSupport = null;\n        this._initialize();\n    }\n    _initialize() {\n        const newLanguage = this.getLanguageId();\n        if (!this._tokenizationSupport || this._lastLanguageId !== newLanguage) {\n            this._lastLanguageId = newLanguage;\n            this._tokenizationSupport = TreeSitterTokenizationRegistry.get(newLanguage);\n        }\n    }\n    getLineTokens(lineNumber) {\n        const content = this._textModel.getLineContent(lineNumber);\n        if (this._tokenizationSupport) {\n            const rawTokens = this._tokenizationSupport.tokenizeEncoded(lineNumber, this._textModel);\n            if (rawTokens) {\n                return new LineTokens(rawTokens, content, this._languageIdCodec);\n            }\n        }\n        return LineTokens.createEmpty(content, this._languageIdCodec);\n    }\n    resetTokenization(fireTokenChangeEvent = true) {\n        if (fireTokenChangeEvent) {\n            this._onDidChangeTokens.fire({\n                semanticTokensApplied: false,\n                ranges: [\n                    {\n                        fromLineNumber: 1,\n                        toLineNumber: this._textModel.getLineCount(),\n                    },\n                ],\n            });\n        }\n        this._initialize();\n    }\n    handleDidChangeAttached() {\n        // TODO @alexr00 implement for background tokenization\n    }\n    handleDidChangeContent(e) {\n        if (e.isFlush) {\n            // Don't fire the event, as the view might not have got the text change event yet\n            this.resetTokenization(false);\n        }\n    }\n    forceTokenization(lineNumber) {\n        // TODO @alexr00 implement\n    }\n    hasAccurateTokensForLine(lineNumber) {\n        // TODO @alexr00 update for background tokenization\n        return true;\n    }\n    isCheapToTokenize(lineNumber) {\n        // TODO @alexr00 update for background tokenization\n        return true;\n    }\n    getTokenTypeIfInsertingCharacter(lineNumber, column, character) {\n        // TODO @alexr00 implement once we have custom parsing and don't just feed in the whole text model value\n        return 0 /* StandardTokenType.Other */;\n    }\n    tokenizeLineWithEdit(position, length, newText) {\n        // TODO @alexr00 understand what this is for and implement\n        return null;\n    }\n    get hasTokens() {\n        // TODO @alexr00 once we have a token store, implement properly\n        const hasTree = this._treeSitterService.getParseResult(this._textModel) !== undefined;\n        return hasTree;\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,8BAA8B,QAAQ,iBAAiB;AAChE,SAASC,UAAU,QAAQ,yBAAyB;AACpD,SAASC,cAAc,QAAQ,aAAa;AAC5C,OAAO,MAAMC,gBAAgB,SAASD,cAAc,CAAC;EACjDE,WAAWA,CAACC,kBAAkB,EAAEC,eAAe,EAAEC,SAAS,EAAEC,UAAU,EAAE;IACpE,KAAK,CAACF,eAAe,EAAEC,SAAS,EAAEC,UAAU,CAAC;IAC7C,IAAI,CAACH,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACI,oBAAoB,GAAG,IAAI;IAChC,IAAI,CAACC,WAAW,CAAC,CAAC;EACtB;EACAA,WAAWA,CAAA,EAAG;IACV,MAAMC,WAAW,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC;IACxC,IAAI,CAAC,IAAI,CAACH,oBAAoB,IAAI,IAAI,CAACI,eAAe,KAAKF,WAAW,EAAE;MACpE,IAAI,CAACE,eAAe,GAAGF,WAAW;MAClC,IAAI,CAACF,oBAAoB,GAAGT,8BAA8B,CAACc,GAAG,CAACH,WAAW,CAAC;IAC/E;EACJ;EACAI,aAAaA,CAACC,UAAU,EAAE;IACtB,MAAMC,OAAO,GAAG,IAAI,CAACC,UAAU,CAACC,cAAc,CAACH,UAAU,CAAC;IAC1D,IAAI,IAAI,CAACP,oBAAoB,EAAE;MAC3B,MAAMW,SAAS,GAAG,IAAI,CAACX,oBAAoB,CAACY,eAAe,CAACL,UAAU,EAAE,IAAI,CAACE,UAAU,CAAC;MACxF,IAAIE,SAAS,EAAE;QACX,OAAO,IAAInB,UAAU,CAACmB,SAAS,EAAEH,OAAO,EAAE,IAAI,CAACK,gBAAgB,CAAC;MACpE;IACJ;IACA,OAAOrB,UAAU,CAACsB,WAAW,CAACN,OAAO,EAAE,IAAI,CAACK,gBAAgB,CAAC;EACjE;EACAE,iBAAiBA,CAACC,oBAAoB,GAAG,IAAI,EAAE;IAC3C,IAAIA,oBAAoB,EAAE;MACtB,IAAI,CAACC,kBAAkB,CAACC,IAAI,CAAC;QACzBC,qBAAqB,EAAE,KAAK;QAC5BC,MAAM,EAAE,CACJ;UACIC,cAAc,EAAE,CAAC;UACjBC,YAAY,EAAE,IAAI,CAACb,UAAU,CAACc,YAAY,CAAC;QAC/C,CAAC;MAET,CAAC,CAAC;IACN;IACA,IAAI,CAACtB,WAAW,CAAC,CAAC;EACtB;EACAuB,uBAAuBA,CAAA,EAAG;IACtB;EAAA;EAEJC,sBAAsBA,CAACC,CAAC,EAAE;IACtB,IAAIA,CAAC,CAACC,OAAO,EAAE;MACX;MACA,IAAI,CAACZ,iBAAiB,CAAC,KAAK,CAAC;IACjC;EACJ;EACAa,iBAAiBA,CAACrB,UAAU,EAAE;IAC1B;EAAA;EAEJsB,wBAAwBA,CAACtB,UAAU,EAAE;IACjC;IACA,OAAO,IAAI;EACf;EACAuB,iBAAiBA,CAACvB,UAAU,EAAE;IAC1B;IACA,OAAO,IAAI;EACf;EACAwB,gCAAgCA,CAACxB,UAAU,EAAEyB,MAAM,EAAEC,SAAS,EAAE;IAC5D;IACA,OAAO,CAAC,CAAC;EACb;EACAC,oBAAoBA,CAACC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAE;IAC5C;IACA,OAAO,IAAI;EACf;EACA,IAAIC,SAASA,CAAA,EAAG;IACZ;IACA,MAAMC,OAAO,GAAG,IAAI,CAAC3C,kBAAkB,CAAC4C,cAAc,CAAC,IAAI,CAAC/B,UAAU,CAAC,KAAKgC,SAAS;IACrF,OAAOF,OAAO;EAClB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}