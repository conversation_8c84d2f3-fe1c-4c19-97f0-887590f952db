// Generic electric card styles
.electric-card {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 12px 24px;
  // padding-bottom: 0px;
  margin-bottom: 30px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  border: 0.2px solid var(--Brand-Tertiary-500, #673ab7);
  box-shadow: var(--Elevation-01X) var(--Elevation-01Y) var(--Elevation-01Blur)
    var(--Elevation-01Spread) var(--BrandNeutraln-100);

  backdrop-filter: blur(24px);

  &:hover {
    box-shadow: var(--Elevation-01X) var(--Elevation-01Y)
      var(--Elevation-01Blur) var(--Elevation-01Spread) var(--BrandNeutraln-100);
    transform: scale(1.03) translateY(-5px); // pop and lift effect
  }

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      135deg,
      rgba(140, 101, 247, 0.1),
      rgba(232, 67, 147, 0.1)
    );
    opacity: 0;
    z-index: 0;
  }

  &::after {
    content: "";
    position: absolute;
    inset: 0;
    padding: 2px;
    border-radius: 12px;
    background: linear-gradient(
      90deg,
      rgba(255, 255, 255, 0.8) 0%,
      rgba(173, 216, 230, 0.8) 25%,
      rgba(255, 255, 255, 0.8) 50%,
      rgba(173, 216, 230, 0.8) 75%,
      rgba(255, 255, 255, 0.8) 100%
    );
    background-size: 400% 100%;
    -webkit-mask:
      linear-gradient(#fff 0 0) content-box,
      linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    opacity: 0;
    pointer-events: none;
    filter: drop-shadow(0 0 2px rgba(255, 255, 255, 0.7))
      drop-shadow(0 0 4px rgba(173, 216, 230, 0.5));
  }

  .card-content {
    position: relative;
    z-index: 1;
  }

  // Width-based truncation for card header
  .truncate-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    min-width: 0; // Allow flex items to shrink below content size

    .agent-title-truncate {
      flex: 1;
      min-width: 0; // Allow the title to shrink
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin: 0;
      margin-right: 8px; // Add some space between title and rating
    }

    .rating {
      flex-shrink: 0; // Prevent rating from shrinking
      display: flex;
      align-items: center;
      gap: 4px;
    }
  }
}

// Agent specific styles
.agents-container {
  display: flex;
  flex-direction: column;

  // Height and overflow behavior based on context
  &.marketplace-container {
    height: 100%;
    overflow: hidden; // Prevent container from scrolling in marketplace
  }

  &.launchpad-container {
    height: auto;
    overflow: visible; // Allow natural flow in launchpad
  }

  // Search results header
  .search-results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
    margin-bottom: 16px;
    border-bottom: 1px solid #e5e7eb;

    .search-results-info {
      display: flex;
      align-items: center;
      gap: 8px;

      .search-results-text {
        color: #616874;
        font-family: Mulish;
        font-size: 16px;
        font-weight: 500;
      }
    }

    .clear-search-btn {
      display: flex;
      align-items: center;
      gap: 4px;
      background: none;
      border: none;
      color: #616874;
      font-family: Mulish;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      padding: 8px 12px;
      border-radius: 8px;
      transition: all 0.3s ease;

      &:hover {
        background-color: rgba(97, 104, 116, 0.1);
      }

      &:active {
        transform: scale(0.95);
      }
    }
  }

  // Loading state
  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
    width: 100%;

    .loading-spinner {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 16px;

      .spinning {
        animation: spin 1s linear infinite;
      }

      p {
        color: #616874;
        font-family: Mulish;
        font-size: 16px;
        font-weight: 500;
        margin: 0;
      }
    }
  }

  // No agents found state
  .no-agents {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    width: 100%;
    gap: 16px;

    h3 {
      color: #4c515b;
      font-family: Mulish;
      font-size: 20px;
      font-weight: 600;
      margin: 0;
    }

    p {
      color: #616874;
      font-family: Mulish;
      font-size: 16px;
      font-weight: 400;
      margin: 0;
      text-align: center;
    }
  }

  // Content wrapper
  .agents-content {
    display: flex;
    flex-direction: column;

    // Height and overflow behavior based on context
    &.marketplace-content {
      height: 100%;
      overflow: hidden;
    }

    &.launchpad-content {
      height: auto;
      overflow: visible;
    }
  }

  // Height and overflow behavior based on context
  &.marketplace-container {
    height: 100%;
    overflow: hidden; // Prevent container from scrolling in marketplace
  }

  &.launchpad-container {
    height: auto;
    overflow: visible; // Allow natural flow in launchpad
  }

  // Search results header
  .search-results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
    margin-bottom: 16px;
    border-bottom: 1px solid #e5e7eb;

    .search-results-info {
      display: flex;
      align-items: center;
      gap: 8px;

      .search-results-text {
        color: #616874;
        font-family: Mulish;
        font-size: 16px;
        font-weight: 500;
      }
    }

    .clear-search-btn {
      display: flex;
      align-items: center;
      gap: 4px;
      background: none;
      border: none;
      color: #616874;
      font-family: Mulish;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      padding: 8px 12px;
      border-radius: 8px;
      transition: all 0.3s ease;

      &:hover {
        background-color: rgba(97, 104, 116, 0.1);
      }

      &:active {
        transform: scale(0.95);
      }
    }
  }

  // Loading state
  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
    width: 100%;

    .loading-spinner {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 16px;

      .spinning {
        animation: spin 1s linear infinite;
      }

      p {
        color: #616874;
        font-family: Mulish;
        font-size: 16px;
        font-weight: 500;
        margin: 0;
      }
    }
  }

  // No agents found state
  .no-agents {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    width: 100%;
    gap: 16px;

    h3 {
      color: #4c515b;
      font-family: Mulish;
      font-size: 20px;
      font-weight: 600;
      margin: 0;
    }

    p {
      color: #616874;
      font-family: Mulish;
      font-size: 16px;
      font-weight: 400;
      margin: 0;
      text-align: center;
    }
  }

  // Content wrapper
  .agents-content {
    display: flex;
    flex-direction: column;

    // Height and overflow behavior based on context
    &.marketplace-content {
      height: 100%;
      overflow: hidden;
    }

    &.launchpad-content {
      height: auto;
      overflow: visible;
    }
  }

  // Sticky filter tabs
  app-filter-tabs {
    position: sticky;
    top: 0;
    z-index: 2;
    flex-shrink: 0;
    background: white; // Ensure tabs have background when sticky
  }

  // Scrollable container for agents grid - only in marketplace
  .agents-grid-container {
    flex: 1;
    overflow-x: hidden;
    padding-bottom: 20px;

    // Scrolling only in marketplace
    &.marketplace-scroll {
      overflow-y: auto;
      padding: 24px;
      background: white;
    }

    // No scrolling in launchpad
    &.launchpad-no-scroll {
      overflow-y: visible;
    }
  }

  // Agents grid
  .agents-grid {
    padding: 0; // Bootstrap row padding
  }

  .col-12,
  .col-md-6,
  .col-lg-3 {
    padding: 12px;
    padding-bottom: 6px;
    padding-top: 6px;
  }

  .agent-card {
    /* Remove border-color and transition for border color */
    @extend .electric-card;
    margin: 0;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      padding: 0px;

      h2 {
        color: #4c515b;
        font-family: Mulish;
        font-size: 20px;
        font-weight: 600;
        letter-spacing: -0.456px;
        margin: 0;
      }

      .rating {
        display: flex;
        align-items: center;
        gap: 4px;
        font-weight: 500;
      }
    }

    .description {
      color: #474c6b;
      text-align: left;
      font-family: Inter;
      font-size: 14px;
      font-weight: 400;
      margin-top: 12px;
      margin-bottom: 0;
      height: 40px; // Fixed height for consistent card layout
      line-height: 21px; // 3 lines of text (20px * 3 = 60px)
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    .card-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0px;
      width: 100%;

      .users {
        color: #858aad;
        font-family: Mulish;
        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 4px;
      }

      .agent-time-ago {
        color: var(--Neutral-N-800, #474c6b);
        font-family: Mulish;
        font-size: 16px;
        font-style: normal;
        font-weight: 500;
        line-height: normal;
      }
    }

    // Marketplace specific spacing - 16px gaps between sections
    &.marketplace-card {
      .card-content {
        display: flex;
        flex-direction: column;
        gap: 16px;
      }
    }
  }
}

// Spinning animation
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
