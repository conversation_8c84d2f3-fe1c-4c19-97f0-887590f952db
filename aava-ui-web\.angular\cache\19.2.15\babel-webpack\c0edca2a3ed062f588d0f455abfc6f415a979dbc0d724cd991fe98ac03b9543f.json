{"ast": null, "code": "import { signal, inject } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, Validators } from '@angular/forms';\nimport logintext from './login.json';\nimport { AvaTextboxComponent, IconComponent, ButtonComponent, PopupComponent } from '@ava/play-comp-library';\nimport { AuthService } from '@shared/auth/services/auth.service';\nimport { TokenStorageService } from '../../../auth/services/token-storage.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nexport let LoginComponent = /*#__PURE__*/(() => {\n  class LoginComponent {\n    fb;\n    router;\n    loginMode = signal('sso');\n    isLoading = signal(false);\n    showPassword = signal(false);\n    loginForm;\n    errorMessage = signal(null);\n    showErrorPopup = signal(false);\n    popupMessage = signal('');\n    labels = logintext;\n    authService = inject(AuthService);\n    tokenStorage = inject(TokenStorageService);\n    constructor(fb, router) {\n      this.fb = fb;\n      this.router = router;\n      this.loginForm = this.fb.group({\n        username: ['', [Validators.required]],\n        password: ['', Validators.required],\n        keepSignedIn: [false]\n      });\n    }\n    ngOnInit() {\n      // Check if user is already authenticated\n      if (this.isAuthenticated()) {\n        const redirectUrl = this.authService.getPostLoginRedirectUrl();\n        this.router.navigate([redirectUrl]);\n        return;\n      }\n      const storedLoginType = this.tokenStorage.getLoginType();\n      if (storedLoginType === 'sso' || storedLoginType === 'basic') {\n        this.loginMode.set(storedLoginType === 'basic' ? 'form' : 'sso');\n      } else {\n        this.loginMode.set('sso');\n      }\n    }\n    // Simple authentication check\n    isAuthenticated() {\n      const accessToken = this.tokenStorage.getAccessToken();\n      const refreshToken = this.tokenStorage.getRefreshToken();\n      return !!(accessToken || refreshToken);\n    }\n    getControl(name) {\n      return this.loginForm.get(name);\n    }\n    onBasicLogin() {\n      if (this.loginForm.valid) {\n        this.isLoading.set(true);\n        this.errorMessage.set(null);\n        const {\n          username,\n          password\n        } = this.loginForm.value;\n        this.authService.basicLoginWithCredentials(username, password).subscribe({\n          next: () => {\n            this.tokenStorage.storeLoginType('basic');\n            const redirectUrl = this.authService.getPostLoginRedirectUrl();\n            this.router.navigate([redirectUrl]);\n          },\n          error: error => {\n            this.isLoading.set(false);\n            let errorMessage = 'Invalid username or password. Please try again.';\n            if (error.error && typeof error.error === 'string') {\n              errorMessage = error.error;\n            }\n            this.popupMessage.set(errorMessage);\n            this.showErrorPopup.set(true);\n            console.error('Login failed:', error);\n          }\n        });\n      } else {\n        this.markFormGroupTouched();\n      }\n    }\n    onCompanyLogin() {\n      this.isLoading.set(true);\n      this.errorMessage.set(null);\n      this.authService.loginSSO().subscribe({\n        next: () => {\n          this.isLoading.set(false);\n        },\n        error: error => {\n          console.error('Login failed:', error);\n          this.errorMessage.set('Failed to initiate login with company account.');\n          this.isLoading.set(false);\n        }\n      });\n    }\n    togglePasswordVisibility() {\n      this.showPassword.set(!this.showPassword());\n    }\n    clearInput(fieldName) {\n      this.loginForm.get(fieldName)?.setValue('');\n      this.loginForm.get(fieldName)?.markAsTouched();\n    }\n    clearUsername() {\n      this.clearInput('username');\n    }\n    markFormGroupTouched() {\n      Object.keys(this.loginForm.controls).forEach(key => {\n        const control = this.loginForm.get(key);\n        control?.markAsTouched();\n      });\n    }\n    getFieldError(fieldName) {\n      const field = this.loginForm.get(fieldName);\n      if (field?.touched && field?.errors) {\n        if (field.errors['required']) {\n          return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} is required`;\n        }\n        if (field.errors['email']) {\n          return 'Please enter a valid email address';\n        }\n        if (field.errors['minlength']) {\n          return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} must be at least ${field.errors['minlength'].requiredLength} characters`;\n        }\n      }\n      return '';\n    }\n    // Check if both username and password fields are filled\n    areFieldsFilled() {\n      const username = this.loginForm.get('username')?.value;\n      const password = this.loginForm.get('password')?.value;\n      return !!username && !!password && username.trim() !== '' && password.trim() !== '';\n    }\n    onForgotPassword() {\n      this.router.navigate(['/forgot-password']);\n    }\n    onTroubleSigningIn() {\n      this.router.navigate(['/help']);\n    }\n    static ɵfac = function LoginComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LoginComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.Router));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LoginComponent,\n      selectors: [[\"app-login\"]],\n      decls: 25,\n      vars: 39,\n      consts: [[\"id\", \"login-container\", 1, \"row\"], [1, \"col-5\", \"p-0\"], [\"src\", \"login.png\", \"alt\", \"\", 1, \"login-image\"], [1, \"col-7\", \"p-0\", \"login-section\"], [1, \"sign-in-container\"], [1, \"heading\"], [1, \"mb-2\", \"main-heading\"], [1, \"sub-heading\"], [1, \"new-login-form\", 3, \"formGroup\"], [1, \"form-field-wrapper\"], [\"formControlName\", \"username\", 3, \"label\", \"placeholder\", \"required\", \"error\", \"iconSeparator\", \"iconSpacing\"], [\"slot\", \"icon-end\", \"iconName\", \"x\", 3, \"click\", \"iconSize\", \"cursor\", \"disabled\"], [1, \"form-field-wrapper\", \"mt-4\", \"mb-4\"], [\"formControlName\", \"password\", 3, \"label\", \"type\", \"placeholder\", \"required\", \"error\", \"iconSeparator\", \"iconSpacing\"], [\"slot\", \"icon-end\", 3, \"click\", \"iconName\", \"iconSize\", \"cursor\", \"disabled\"], [1, \"new-buttons-container\"], [1, \"sign-in-button\", \"mb-5\"], [\"variant\", \"primary\", \"size\", \"large\", 1, \"mb-4\", 3, \"userClick\", \"label\", \"processing\", \"width\", \"disabled\"], [1, \"new-separator\"], [1, \"login-with-company\", \"mt-5\"], [\"variant\", \"secondary\", \"size\", \"large\", 3, \"userClick\", \"label\", \"width\"], [\"title\", \"Login Failed\", \"headerIconName\", \"alert-circle\", \"iconColor\", \"#dc3545\", 3, \"confirm\", \"closed\", \"show\", \"message\", \"showHeaderIcon\", \"showClose\", \"showCancel\", \"showConfirm\", \"confirmButtonLabel\", \"confirmButtonVariant\", \"confirmButtonBackground\"]],\n      template: function LoginComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"img\", 2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"h3\", 6);\n          i0.ɵɵtext(7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"p\", 7);\n          i0.ɵɵtext(9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"form\", 8)(11, \"div\", 9)(12, \"ava-textbox\", 10)(13, \"ava-icon\", 11);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_ava_icon_click_13_listener() {\n            return ctx.clearUsername();\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(14, \"div\", 12)(15, \"ava-textbox\", 13)(16, \"ava-icon\", 14);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_ava_icon_click_16_listener() {\n            return ctx.togglePasswordVisibility();\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(17, \"div\", 15)(18, \"div\", 16)(19, \"ava-button\", 17);\n          i0.ɵɵlistener(\"userClick\", function LoginComponent_Template_ava_button_userClick_19_listener() {\n            return ctx.onBasicLogin();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"div\", 18);\n          i0.ɵɵtext(21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"div\", 19)(23, \"ava-button\", 20);\n          i0.ɵɵlistener(\"userClick\", function LoginComponent_Template_ava_button_userClick_23_listener() {\n            return ctx.onCompanyLogin();\n          });\n          i0.ɵɵelementEnd()()()()()()();\n          i0.ɵɵelementStart(24, \"ava-popup\", 21);\n          i0.ɵɵlistener(\"confirm\", function LoginComponent_Template_ava_popup_confirm_24_listener() {\n            return ctx.showErrorPopup.set(false);\n          })(\"closed\", function LoginComponent_Template_ava_popup_closed_24_listener() {\n            return ctx.showErrorPopup.set(false);\n          });\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate(ctx.labels.labels.main_heading);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.labels.labels.sub_heading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"formGroup\", ctx.loginForm);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"label\", ctx.labels.labels.username)(\"placeholder\", ctx.labels.placeholders.username)(\"required\", true)(\"error\", ctx.getFieldError(\"username\"))(\"iconSeparator\", false)(\"iconSpacing\", \"normal\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"iconSize\", 16)(\"cursor\", true)(\"disabled\", false);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"label\", ctx.labels.labels.password)(\"type\", ctx.showPassword() ? \"text\" : \"password\")(\"placeholder\", ctx.labels.placeholders.password)(\"required\", true)(\"error\", ctx.getFieldError(\"password\"))(\"iconSeparator\", false)(\"iconSpacing\", \"normal\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"iconName\", ctx.showPassword() ? \"eye-off\" : \"eye\")(\"iconSize\", 18)(\"cursor\", true)(\"disabled\", false);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"label\", ctx.labels.labels.sign_in + \" \" + ctx.labels.labels.arrow)(\"processing\", ctx.isLoading())(\"width\", \"100%\")(\"disabled\", !ctx.areFieldsFilled() || ctx.isLoading());\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.labels.labels.seperator, \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"label\", ctx.labels.labels.login_with_company)(\"width\", \"100%\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"show\", ctx.showErrorPopup())(\"message\", ctx.popupMessage())(\"showHeaderIcon\", true)(\"showClose\", true)(\"showCancel\", false)(\"showConfirm\", true)(\"confirmButtonLabel\", \"OK\")(\"confirmButtonVariant\", \"primary\")(\"confirmButtonBackground\", \"#dc3545\");\n        }\n      },\n      dependencies: [CommonModule, ReactiveFormsModule, i1.ɵNgNoValidate, i1.NgControlStatus, i1.NgControlStatusGroup, i1.RequiredValidator, i1.FormGroupDirective, i1.FormControlName, AvaTextboxComponent, IconComponent, ButtonComponent, PopupComponent],\n      styles: [\"#login-container[_ngcontent-%COMP%]   .login-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100vh;\\n}\\n#login-container[_ngcontent-%COMP%]   .login-section[_ngcontent-%COMP%] {\\n  background-color: white;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n#login-container[_ngcontent-%COMP%]   .login-section[_ngcontent-%COMP%]   .sign-in-container[_ngcontent-%COMP%] {\\n  min-width: 510px;\\n  width: 50%;\\n  margin: 0 auto;\\n  padding: 24px;\\n  background: var(--Brand-Neutral-n-50, #f0f1f2);\\n  border: 1px solid var(--Brand-Neutral-n-50, #f0f1f2);\\n  border-radius: 16px;\\n}\\n\\n.p-0[_ngcontent-%COMP%] {\\n  padding: 0;\\n}\\n\\n.mb-2[_ngcontent-%COMP%] {\\n  margin-bottom: 0.5rem;\\n}\\n\\n.mt-4[_ngcontent-%COMP%] {\\n  margin-top: 1.5rem;\\n}\\n\\n.mb-4[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n}\\n\\n.mb-5[_ngcontent-%COMP%] {\\n  margin-bottom: 2rem;\\n}\\n\\n.mt-5[_ngcontent-%COMP%] {\\n  margin-top: 2rem;\\n}\\n\\n.heading[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 64px;\\n}\\n\\n.main-heading[_ngcontent-%COMP%] {\\n  font-size: 32px;\\n  font-weight: 700;\\n  color: #1a1a1a;\\n  margin-bottom: 8px;\\n}\\n\\n.sub-heading[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: #666;\\n  font-weight: 400;\\n}\\n\\n.new-separator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  text-align: center;\\n  color: #898e99;\\n  font-size: 18px;\\n  font-weight: 600;\\n}\\n.new-separator[_ngcontent-%COMP%]::before, .new-separator[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  flex: 1;\\n  height: 1px;\\n  background: #898e99;\\n}\\n.new-separator[_ngcontent-%COMP%]::before {\\n  margin-right: 16px;\\n}\\n.new-separator[_ngcontent-%COMP%]::after {\\n  margin-left: 16px;\\n}\\n\\n.sign-in-button[_ngcontent-%COMP%] {\\n  color: white !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return LoginComponent;\n})();", "map": {"version": 3, "names": ["signal", "inject", "CommonModule", "ReactiveFormsModule", "Validators", "logintext", "AvaTextboxComponent", "IconComponent", "ButtonComponent", "PopupComponent", "AuthService", "TokenStorageService", "LoginComponent", "fb", "router", "loginMode", "isLoading", "showPassword", "loginForm", "errorMessage", "showErrorPopup", "popupMessage", "labels", "authService", "tokenStorage", "constructor", "group", "username", "required", "password", "keepSignedIn", "ngOnInit", "isAuthenticated", "redirectUrl", "getPostLoginRedirectUrl", "navigate", "storedLoginType", "getLoginType", "set", "accessToken", "getAccessToken", "refreshToken", "getRefreshToken", "getControl", "name", "get", "onBasicLogin", "valid", "value", "basicLoginWithCredentials", "subscribe", "next", "storeLoginType", "error", "console", "markFormGroupTouched", "onCompanyLogin", "loginSSO", "togglePasswordVisibility", "clearInput", "fieldName", "setValue", "<PERSON><PERSON><PERSON><PERSON>ched", "clearUsername", "Object", "keys", "controls", "for<PERSON>ach", "key", "control", "getFieldError", "field", "touched", "errors", "char<PERSON>t", "toUpperCase", "slice", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>Filled", "trim", "onForgotPassword", "onTroubleSigningIn", "i0", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "Router", "selectors", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "LoginComponent_Template_ava_icon_click_13_listener", "LoginComponent_Template_ava_icon_click_16_listener", "LoginComponent_Template_ava_button_userClick_19_listener", "LoginComponent_Template_ava_button_userClick_23_listener", "LoginComponent_Template_ava_popup_confirm_24_listener", "LoginComponent_Template_ava_popup_closed_24_listener", "ɵɵadvance", "ɵɵtextInterpolate", "main_heading", "sub_heading", "ɵɵproperty", "placeholders", "sign_in", "arrow", "ɵɵtextInterpolate1", "seperator", "login_with_company", "ɵNgNoValidate", "NgControlStatus", "NgControlStatusGroup", "RequiredValidator", "FormGroupDirective", "FormControlName", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\auth\\components\\login\\login.component.ts", "C:\\console\\aava-ui-web\\projects\\shared\\auth\\components\\login\\login.component.html"], "sourcesContent": ["import { Component, OnInit, signal, inject } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport {\r\n  ReactiveFormsModule,\r\n  FormBuilder,\r\n  FormGroup,\r\n  Validators,\r\n  FormControl,\r\n} from '@angular/forms';\r\nimport { Router } from '@angular/router';\r\n\r\nimport { HttpErrorResponse } from '@angular/common/http';\r\nimport logintext from './login.json';\r\nimport {\r\n  AvaTextboxComponent,\r\n  IconComponent,\r\n  ButtonComponent,\r\n  PopupComponent,\r\n} from '@ava/play-comp-library';\r\nimport { AuthService } from '@shared/auth/services/auth.service';\r\nimport { TokenStorageService } from '../../../auth/services/token-storage.service';\r\n\r\nexport interface SavedAccount {\r\n  email: string;\r\n  profilePic?: string;\r\n  isSelected?: boolean;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-login',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    ReactiveFormsModule,\r\n    AvaTextboxComponent,\r\n    IconComponent,\r\n    ButtonComponent,\r\n    PopupComponent,\r\n  ],\r\n  templateUrl: './login.component.html',\r\n  styleUrls: ['./login.component.scss'],\r\n})\r\nexport class LoginComponent implements OnInit {\r\n  loginMode = signal<'sso' | 'form'>('sso');\r\n  isLoading = signal(false);\r\n  showPassword = signal(false);\r\n  loginForm: FormGroup;\r\n  errorMessage = signal<string | null>(null);\r\n  showErrorPopup = signal(false);\r\n  popupMessage = signal('');\r\n  public labels: any = logintext;\r\n\r\n  private authService = inject(AuthService);\r\n  private tokenStorage = inject(TokenStorageService);\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private router: Router,\r\n  ) {\r\n    this.loginForm = this.fb.group({\r\n      username: ['', [Validators.required]],\r\n      password: ['', Validators.required],\r\n      keepSignedIn: [false],\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    // Check if user is already authenticated\r\n    if (this.isAuthenticated()) {\r\n      const redirectUrl = this.authService.getPostLoginRedirectUrl();\r\n      this.router.navigate([redirectUrl]);\r\n      return;\r\n    }\r\n\r\n    const storedLoginType = this.tokenStorage.getLoginType();\r\n    if (storedLoginType === 'sso' || storedLoginType === 'basic') {\r\n      this.loginMode.set(storedLoginType === 'basic' ? 'form' : 'sso');\r\n    } else {\r\n      this.loginMode.set('sso');\r\n    }\r\n  }\r\n\r\n  // Simple authentication check\r\n  private isAuthenticated(): boolean {\r\n    const accessToken = this.tokenStorage.getAccessToken();\r\n    const refreshToken = this.tokenStorage.getRefreshToken();\r\n    return !!(accessToken || refreshToken);\r\n  }\r\n\r\n  getControl(name: string): FormControl {\r\n    return this.loginForm.get(name) as FormControl;\r\n  }\r\n\r\n  onBasicLogin(): void {\r\n    if (this.loginForm.valid) {\r\n      this.isLoading.set(true);\r\n      this.errorMessage.set(null);\r\n\r\n      const { username, password } = this.loginForm.value;\r\n      this.authService.basicLoginWithCredentials(username, password).subscribe({\r\n        next: () => {\r\n          this.tokenStorage.storeLoginType('basic');\r\n          const redirectUrl = this.authService.getPostLoginRedirectUrl();\r\n          this.router.navigate([redirectUrl]);\r\n        },\r\n        error: (error: HttpErrorResponse) => {\r\n          this.isLoading.set(false);\r\n          let errorMessage = 'Invalid username or password. Please try again.';\r\n          \r\n          if (error.error && typeof error.error === 'string') {\r\n            errorMessage = error.error;\r\n          }\r\n          this.popupMessage.set(errorMessage);\r\n          this.showErrorPopup.set(true);\r\n          console.error('Login failed:', error);\r\n        },\r\n      });\r\n    } else {\r\n      this.markFormGroupTouched();\r\n    }\r\n  }\r\n\r\n  onCompanyLogin(): void {\r\n    this.isLoading.set(true);\r\n    this.errorMessage.set(null);\r\n\r\n    this.authService.loginSSO().subscribe({\r\n      next: () => {\r\n        this.isLoading.set(false);\r\n      },\r\n      error: (error) => {\r\n        console.error('Login failed:', error);\r\n        this.errorMessage.set('Failed to initiate login with company account.');\r\n        this.isLoading.set(false);\r\n      },\r\n    });\r\n  }\r\n\r\n  togglePasswordVisibility(): void {\r\n    this.showPassword.set(!this.showPassword());\r\n  }\r\n\r\n  clearInput(fieldName: string): void {\r\n    this.loginForm.get(fieldName)?.setValue('');\r\n    this.loginForm.get(fieldName)?.markAsTouched();\r\n  }\r\n\r\n  clearUsername(): void {\r\n    this.clearInput('username');\r\n  }\r\n\r\n  private markFormGroupTouched(): void {\r\n    Object.keys(this.loginForm.controls).forEach((key) => {\r\n      const control = this.loginForm.get(key);\r\n      control?.markAsTouched();\r\n    });\r\n  }\r\n\r\n  getFieldError(fieldName: string): string {\r\n    const field = this.loginForm.get(fieldName);\r\n    if (field?.touched && field?.errors) {\r\n      if (field.errors['required']) {\r\n        return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} is required`;\r\n      }\r\n      if (field.errors['email']) {\r\n        return 'Please enter a valid email address';\r\n      }\r\n      if (field.errors['minlength']) {\r\n        return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} must be at least ${field.errors['minlength'].requiredLength} characters`;\r\n      }\r\n    }\r\n    return '';\r\n  }\r\n\r\n  // Check if both username and password fields are filled\r\n  areFieldsFilled(): boolean {\r\n    const username = this.loginForm.get('username')?.value;\r\n    const password = this.loginForm.get('password')?.value;\r\n    return !!username && !!password && username.trim() !== '' && password.trim() !== '';\r\n  }\r\n\r\n  onForgotPassword(): void {\r\n    this.router.navigate(['/forgot-password']);\r\n  }\r\n\r\n  onTroubleSigningIn(): void {\r\n    this.router.navigate(['/help']);\r\n  }\r\n}\r\n", "<div id=\"login-container\" class=\"row\">\r\n  <div class=\"col-5 p-0\">\r\n    <img class=\"login-image\" src=\"login.png\" alt=\"\" />\r\n  </div>\r\n  <div class=\"col-7 p-0 login-section\">\r\n    <div class=\"sign-in-container\">\r\n      <div class=\"heading\">\r\n        <h3 class=\"mb-2 main-heading\">{{ labels.labels.main_heading }}</h3>\r\n        <p class=\"sub-heading\">{{ labels.labels.sub_heading }}</p>\r\n      </div>\r\n      <form [formGroup]=\"loginForm\" class=\"new-login-form\">\r\n        <div class=\"form-field-wrapper\">\r\n          <ava-textbox\r\n            [label]=\"labels.labels.username\"\r\n            [placeholder]=\"labels.placeholders.username\"\r\n            [required]=\"true\"\r\n            formControlName=\"username\"\r\n            [error]=\"getFieldError('username')\"\r\n            [iconSeparator]=\"false\"\r\n            [iconSpacing]=\"'normal'\"\r\n          >\r\n            <ava-icon\r\n              slot=\"icon-end\"\r\n              iconName=\"x\"\r\n              [iconSize]=\"16\"\r\n              [cursor]=\"true\"\r\n              (click)=\"clearUsername()\"\r\n              [disabled]=\"false\"\r\n            >\r\n            </ava-icon>\r\n          </ava-textbox>\r\n        </div>\r\n        <div class=\"form-field-wrapper mt-4 mb-4\">\r\n          <ava-textbox\r\n            [label]=\"labels.labels.password\"\r\n            [type]=\"showPassword() ? 'text' : 'password'\"\r\n            [placeholder]=\"labels.placeholders.password\"\r\n            [required]=\"true\"\r\n            formControlName=\"password\"\r\n            [error]=\"getFieldError('password')\"\r\n            [iconSeparator]=\"false\"\r\n            [iconSpacing]=\"'normal'\"\r\n          >\r\n            <ava-icon\r\n              slot=\"icon-end\"\r\n              [iconName]=\"showPassword() ? 'eye-off' : 'eye'\"\r\n              [iconSize]=\"18\"\r\n              [cursor]=\"true\"\r\n              (click)=\"togglePasswordVisibility()\"\r\n              [disabled]=\"false\"\r\n            >\r\n            </ava-icon>\r\n          </ava-textbox>\r\n        </div>\r\n        <div class=\"new-buttons-container\">\r\n          <div class=\"sign-in-button mb-5\">\r\n            <ava-button\r\n              class=\"mb-4\"\r\n              [label]=\"labels.labels.sign_in + ' ' + labels.labels.arrow\"\r\n              variant=\"primary\"\r\n              size=\"large\"\r\n              [processing]=\"isLoading()\"\r\n              [width]=\"'100%'\"\r\n              (userClick)=\"onBasicLogin()\"\r\n              [disabled]=\"!areFieldsFilled() || isLoading()\"\r\n            >\r\n            </ava-button>\r\n          </div>\r\n\r\n          <div class=\"new-separator\">\r\n            {{ labels.labels.seperator }}\r\n          </div>\r\n          <div class=\"login-with-company mt-5\">\r\n            <ava-button\r\n              [label]=\"labels.labels.login_with_company\"\r\n              variant=\"secondary\"\r\n              size=\"large\"\r\n              [width]=\"'100%'\"\r\n              (userClick)=\"onCompanyLogin()\"\r\n            >\r\n            </ava-button>\r\n          </div>\r\n        </div>\r\n      </form>\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n<ava-popup\r\n  [show]=\"showErrorPopup()\"\r\n  title=\"Login Failed\"\r\n  [message]=\"popupMessage()\"\r\n  [showHeaderIcon]=\"true\"\r\n  headerIconName=\"alert-circle\"\r\n  iconColor=\"#dc3545\"\r\n  [showClose]=\"true\"\r\n  [showCancel]=\"false\"\r\n  [showConfirm]=\"true\"\r\n  [confirmButtonLabel]=\"'OK'\"\r\n  [confirmButtonVariant]=\"'primary'\"\r\n  [confirmButtonBackground]=\"'#dc3545'\"\r\n  (confirm)=\"showErrorPopup.set(false)\"\r\n  (closed)=\"showErrorPopup.set(false)\"\r\n>\r\n</ava-popup>\r\n"], "mappings": "AAAA,SAA4BA,MAAM,EAAEC,MAAM,QAAQ,eAAe;AACjE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SACEC,mBAAmB,EAGnBC,UAAU,QAEL,gBAAgB;AAIvB,OAAOC,SAAS,MAAM,cAAc;AACpC,SACEC,mBAAmB,EACnBC,aAAa,EACbC,eAAe,EACfC,cAAc,QACT,wBAAwB;AAC/B,SAASC,WAAW,QAAQ,oCAAoC;AAChE,SAASC,mBAAmB,QAAQ,8CAA8C;;;;AAsBlF,WAAaC,cAAc;EAArB,MAAOA,cAAc;IAcfC,EAAA;IACAC,MAAA;IAdVC,SAAS,GAAGf,MAAM,CAAiB,KAAK,CAAC;IACzCgB,SAAS,GAAGhB,MAAM,CAAC,KAAK,CAAC;IACzBiB,YAAY,GAAGjB,MAAM,CAAC,KAAK,CAAC;IAC5BkB,SAAS;IACTC,YAAY,GAAGnB,MAAM,CAAgB,IAAI,CAAC;IAC1CoB,cAAc,GAAGpB,MAAM,CAAC,KAAK,CAAC;IAC9BqB,YAAY,GAAGrB,MAAM,CAAC,EAAE,CAAC;IAClBsB,MAAM,GAAQjB,SAAS;IAEtBkB,WAAW,GAAGtB,MAAM,CAACS,WAAW,CAAC;IACjCc,YAAY,GAAGvB,MAAM,CAACU,mBAAmB,CAAC;IAElDc,YACUZ,EAAe,EACfC,MAAc;MADd,KAAAD,EAAE,GAAFA,EAAE;MACF,KAAAC,MAAM,GAANA,MAAM;MAEd,IAAI,CAACI,SAAS,GAAG,IAAI,CAACL,EAAE,CAACa,KAAK,CAAC;QAC7BC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACvB,UAAU,CAACwB,QAAQ,CAAC,CAAC;QACrCC,QAAQ,EAAE,CAAC,EAAE,EAAEzB,UAAU,CAACwB,QAAQ,CAAC;QACnCE,YAAY,EAAE,CAAC,KAAK;OACrB,CAAC;IACJ;IAEAC,QAAQA,CAAA;MACN;MACA,IAAI,IAAI,CAACC,eAAe,EAAE,EAAE;QAC1B,MAAMC,WAAW,GAAG,IAAI,CAACV,WAAW,CAACW,uBAAuB,EAAE;QAC9D,IAAI,CAACpB,MAAM,CAACqB,QAAQ,CAAC,CAACF,WAAW,CAAC,CAAC;QACnC;MACF;MAEA,MAAMG,eAAe,GAAG,IAAI,CAACZ,YAAY,CAACa,YAAY,EAAE;MACxD,IAAID,eAAe,KAAK,KAAK,IAAIA,eAAe,KAAK,OAAO,EAAE;QAC5D,IAAI,CAACrB,SAAS,CAACuB,GAAG,CAACF,eAAe,KAAK,OAAO,GAAG,MAAM,GAAG,KAAK,CAAC;MAClE,CAAC,MAAM;QACL,IAAI,CAACrB,SAAS,CAACuB,GAAG,CAAC,KAAK,CAAC;MAC3B;IACF;IAEA;IACQN,eAAeA,CAAA;MACrB,MAAMO,WAAW,GAAG,IAAI,CAACf,YAAY,CAACgB,cAAc,EAAE;MACtD,MAAMC,YAAY,GAAG,IAAI,CAACjB,YAAY,CAACkB,eAAe,EAAE;MACxD,OAAO,CAAC,EAAEH,WAAW,IAAIE,YAAY,CAAC;IACxC;IAEAE,UAAUA,CAACC,IAAY;MACrB,OAAO,IAAI,CAAC1B,SAAS,CAAC2B,GAAG,CAACD,IAAI,CAAgB;IAChD;IAEAE,YAAYA,CAAA;MACV,IAAI,IAAI,CAAC5B,SAAS,CAAC6B,KAAK,EAAE;QACxB,IAAI,CAAC/B,SAAS,CAACsB,GAAG,CAAC,IAAI,CAAC;QACxB,IAAI,CAACnB,YAAY,CAACmB,GAAG,CAAC,IAAI,CAAC;QAE3B,MAAM;UAAEX,QAAQ;UAAEE;QAAQ,CAAE,GAAG,IAAI,CAACX,SAAS,CAAC8B,KAAK;QACnD,IAAI,CAACzB,WAAW,CAAC0B,yBAAyB,CAACtB,QAAQ,EAAEE,QAAQ,CAAC,CAACqB,SAAS,CAAC;UACvEC,IAAI,EAAEA,CAAA,KAAK;YACT,IAAI,CAAC3B,YAAY,CAAC4B,cAAc,CAAC,OAAO,CAAC;YACzC,MAAMnB,WAAW,GAAG,IAAI,CAACV,WAAW,CAACW,uBAAuB,EAAE;YAC9D,IAAI,CAACpB,MAAM,CAACqB,QAAQ,CAAC,CAACF,WAAW,CAAC,CAAC;UACrC,CAAC;UACDoB,KAAK,EAAGA,KAAwB,IAAI;YAClC,IAAI,CAACrC,SAAS,CAACsB,GAAG,CAAC,KAAK,CAAC;YACzB,IAAInB,YAAY,GAAG,iDAAiD;YAEpE,IAAIkC,KAAK,CAACA,KAAK,IAAI,OAAOA,KAAK,CAACA,KAAK,KAAK,QAAQ,EAAE;cAClDlC,YAAY,GAAGkC,KAAK,CAACA,KAAK;YAC5B;YACA,IAAI,CAAChC,YAAY,CAACiB,GAAG,CAACnB,YAAY,CAAC;YACnC,IAAI,CAACC,cAAc,CAACkB,GAAG,CAAC,IAAI,CAAC;YAC7BgB,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;UACvC;SACD,CAAC;MACJ,CAAC,MAAM;QACL,IAAI,CAACE,oBAAoB,EAAE;MAC7B;IACF;IAEAC,cAAcA,CAAA;MACZ,IAAI,CAACxC,SAAS,CAACsB,GAAG,CAAC,IAAI,CAAC;MACxB,IAAI,CAACnB,YAAY,CAACmB,GAAG,CAAC,IAAI,CAAC;MAE3B,IAAI,CAACf,WAAW,CAACkC,QAAQ,EAAE,CAACP,SAAS,CAAC;QACpCC,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAACnC,SAAS,CAACsB,GAAG,CAAC,KAAK,CAAC;QAC3B,CAAC;QACDe,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;UACrC,IAAI,CAAClC,YAAY,CAACmB,GAAG,CAAC,gDAAgD,CAAC;UACvE,IAAI,CAACtB,SAAS,CAACsB,GAAG,CAAC,KAAK,CAAC;QAC3B;OACD,CAAC;IACJ;IAEAoB,wBAAwBA,CAAA;MACtB,IAAI,CAACzC,YAAY,CAACqB,GAAG,CAAC,CAAC,IAAI,CAACrB,YAAY,EAAE,CAAC;IAC7C;IAEA0C,UAAUA,CAACC,SAAiB;MAC1B,IAAI,CAAC1C,SAAS,CAAC2B,GAAG,CAACe,SAAS,CAAC,EAAEC,QAAQ,CAAC,EAAE,CAAC;MAC3C,IAAI,CAAC3C,SAAS,CAAC2B,GAAG,CAACe,SAAS,CAAC,EAAEE,aAAa,EAAE;IAChD;IAEAC,aAAaA,CAAA;MACX,IAAI,CAACJ,UAAU,CAAC,UAAU,CAAC;IAC7B;IAEQJ,oBAAoBA,CAAA;MAC1BS,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC/C,SAAS,CAACgD,QAAQ,CAAC,CAACC,OAAO,CAAEC,GAAG,IAAI;QACnD,MAAMC,OAAO,GAAG,IAAI,CAACnD,SAAS,CAAC2B,GAAG,CAACuB,GAAG,CAAC;QACvCC,OAAO,EAAEP,aAAa,EAAE;MAC1B,CAAC,CAAC;IACJ;IAEAQ,aAAaA,CAACV,SAAiB;MAC7B,MAAMW,KAAK,GAAG,IAAI,CAACrD,SAAS,CAAC2B,GAAG,CAACe,SAAS,CAAC;MAC3C,IAAIW,KAAK,EAAEC,OAAO,IAAID,KAAK,EAAEE,MAAM,EAAE;QACnC,IAAIF,KAAK,CAACE,MAAM,CAAC,UAAU,CAAC,EAAE;UAC5B,OAAO,GAAGb,SAAS,CAACc,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE,GAAGf,SAAS,CAACgB,KAAK,CAAC,CAAC,CAAC,cAAc;QAChF;QACA,IAAIL,KAAK,CAACE,MAAM,CAAC,OAAO,CAAC,EAAE;UACzB,OAAO,oCAAoC;QAC7C;QACA,IAAIF,KAAK,CAACE,MAAM,CAAC,WAAW,CAAC,EAAE;UAC7B,OAAO,GAAGb,SAAS,CAACc,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE,GAAGf,SAAS,CAACgB,KAAK,CAAC,CAAC,CAAC,qBAAqBL,KAAK,CAACE,MAAM,CAAC,WAAW,CAAC,CAACI,cAAc,aAAa;QAC5I;MACF;MACA,OAAO,EAAE;IACX;IAEA;IACAC,eAAeA,CAAA;MACb,MAAMnD,QAAQ,GAAG,IAAI,CAACT,SAAS,CAAC2B,GAAG,CAAC,UAAU,CAAC,EAAEG,KAAK;MACtD,MAAMnB,QAAQ,GAAG,IAAI,CAACX,SAAS,CAAC2B,GAAG,CAAC,UAAU,CAAC,EAAEG,KAAK;MACtD,OAAO,CAAC,CAACrB,QAAQ,IAAI,CAAC,CAACE,QAAQ,IAAIF,QAAQ,CAACoD,IAAI,EAAE,KAAK,EAAE,IAAIlD,QAAQ,CAACkD,IAAI,EAAE,KAAK,EAAE;IACrF;IAEAC,gBAAgBA,CAAA;MACd,IAAI,CAAClE,MAAM,CAACqB,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;IAC5C;IAEA8C,kBAAkBA,CAAA;MAChB,IAAI,CAACnE,MAAM,CAACqB,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;IACjC;;uCAjJWvB,cAAc,EAAAsE,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;;YAAd3E,cAAc;MAAA4E,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCzCzBZ,EADF,CAAAc,cAAA,aAAsC,aACb;UACrBd,EAAA,CAAAe,SAAA,aAAkD;UACpDf,EAAA,CAAAgB,YAAA,EAAM;UAIAhB,EAHN,CAAAc,cAAA,aAAqC,aACJ,aACR,YACW;UAAAd,EAAA,CAAAiB,MAAA,GAAgC;UAAAjB,EAAA,CAAAgB,YAAA,EAAK;UACnEhB,EAAA,CAAAc,cAAA,WAAuB;UAAAd,EAAA,CAAAiB,MAAA,GAA+B;UACxDjB,EADwD,CAAAgB,YAAA,EAAI,EACtD;UAYAhB,EAXN,CAAAc,cAAA,eAAqD,cACnB,uBAS7B,oBAQE;UAFCd,EAAA,CAAAkB,UAAA,mBAAAC,mDAAA;YAAA,OAASN,GAAA,CAAAhC,aAAA,EAAe;UAAA,EAAC;UAK/BmB,EAFI,CAAAgB,YAAA,EAAW,EACC,EACV;UAYFhB,EAXJ,CAAAc,cAAA,eAA0C,uBAUvC,oBAQE;UAFCd,EAAA,CAAAkB,UAAA,mBAAAE,mDAAA;YAAA,OAASP,GAAA,CAAArC,wBAAA,EAA0B;UAAA,EAAC;UAK1CwB,EAFI,CAAAgB,YAAA,EAAW,EACC,EACV;UAGFhB,EAFJ,CAAAc,cAAA,eAAmC,eACA,sBAU9B;UAFCd,EAAA,CAAAkB,UAAA,uBAAAG,yDAAA;YAAA,OAAaR,GAAA,CAAAjD,YAAA,EAAc;UAAA,EAAC;UAIhCoC,EADE,CAAAgB,YAAA,EAAa,EACT;UAENhB,EAAA,CAAAc,cAAA,eAA2B;UACzBd,EAAA,CAAAiB,MAAA,IACF;UAAAjB,EAAA,CAAAgB,YAAA,EAAM;UAEJhB,EADF,CAAAc,cAAA,eAAqC,sBAOlC;UADCd,EAAA,CAAAkB,UAAA,uBAAAI,yDAAA;YAAA,OAAaT,GAAA,CAAAvC,cAAA,EAAgB;UAAA,EAAC;UAQ5C0B,EANY,CAAAgB,YAAA,EAAa,EACT,EACF,EACD,EACH,EACF,EACF;UAENhB,EAAA,CAAAc,cAAA,qBAeC;UADCd,EADA,CAAAkB,UAAA,qBAAAK,sDAAA;YAAA,OAAWV,GAAA,CAAA3E,cAAA,CAAAkB,GAAA,CAAmB,KAAK,CAAC;UAAA,EAAC,oBAAAoE,qDAAA;YAAA,OAC3BX,GAAA,CAAA3E,cAAA,CAAAkB,GAAA,CAAmB,KAAK,CAAC;UAAA,EAAC;UAEtC4C,EAAA,CAAAgB,YAAA,EAAY;;;UAjG0BhB,EAAA,CAAAyB,SAAA,GAAgC;UAAhCzB,EAAA,CAAA0B,iBAAA,CAAAb,GAAA,CAAAzE,MAAA,CAAAA,MAAA,CAAAuF,YAAA,CAAgC;UACvC3B,EAAA,CAAAyB,SAAA,GAA+B;UAA/BzB,EAAA,CAAA0B,iBAAA,CAAAb,GAAA,CAAAzE,MAAA,CAAAA,MAAA,CAAAwF,WAAA,CAA+B;UAElD5B,EAAA,CAAAyB,SAAA,EAAuB;UAAvBzB,EAAA,CAAA6B,UAAA,cAAAhB,GAAA,CAAA7E,SAAA,CAAuB;UAGvBgE,EAAA,CAAAyB,SAAA,GAAgC;UAMhCzB,EANA,CAAA6B,UAAA,UAAAhB,GAAA,CAAAzE,MAAA,CAAAA,MAAA,CAAAK,QAAA,CAAgC,gBAAAoE,GAAA,CAAAzE,MAAA,CAAA0F,YAAA,CAAArF,QAAA,CACY,kBAC3B,UAAAoE,GAAA,CAAAzB,aAAA,aAEkB,wBACZ,yBACC;UAKtBY,EAAA,CAAAyB,SAAA,EAAe;UAGfzB,EAHA,CAAA6B,UAAA,gBAAe,gBACA,mBAEG;UAOpB7B,EAAA,CAAAyB,SAAA,GAAgC;UAOhCzB,EAPA,CAAA6B,UAAA,UAAAhB,GAAA,CAAAzE,MAAA,CAAAA,MAAA,CAAAO,QAAA,CAAgC,SAAAkE,GAAA,CAAA9E,YAAA,yBACa,gBAAA8E,GAAA,CAAAzE,MAAA,CAAA0F,YAAA,CAAAnF,QAAA,CACD,kBAC3B,UAAAkE,GAAA,CAAAzB,aAAA,aAEkB,wBACZ,yBACC;UAItBY,EAAA,CAAAyB,SAAA,EAA+C;UAI/CzB,EAJA,CAAA6B,UAAA,aAAAhB,GAAA,CAAA9E,YAAA,uBAA+C,gBAChC,gBACA,mBAEG;UASlBiE,EAAA,CAAAyB,SAAA,GAA2D;UAM3DzB,EANA,CAAA6B,UAAA,UAAAhB,GAAA,CAAAzE,MAAA,CAAAA,MAAA,CAAA2F,OAAA,SAAAlB,GAAA,CAAAzE,MAAA,CAAAA,MAAA,CAAA4F,KAAA,CAA2D,eAAAnB,GAAA,CAAA/E,SAAA,GAGjC,iBACV,cAAA+E,GAAA,CAAAjB,eAAA,MAAAiB,GAAA,CAAA/E,SAAA,GAE8B;UAMhDkE,EAAA,CAAAyB,SAAA,GACF;UADEzB,EAAA,CAAAiC,kBAAA,MAAApB,GAAA,CAAAzE,MAAA,CAAAA,MAAA,CAAA8F,SAAA,MACF;UAGIlC,EAAA,CAAAyB,SAAA,GAA0C;UAG1CzB,EAHA,CAAA6B,UAAA,UAAAhB,GAAA,CAAAzE,MAAA,CAAAA,MAAA,CAAA+F,kBAAA,CAA0C,iBAG1B;UAY5BnC,EAAA,CAAAyB,SAAA,EAAyB;UAWzBzB,EAXA,CAAA6B,UAAA,SAAAhB,GAAA,CAAA3E,cAAA,GAAyB,YAAA2E,GAAA,CAAA1E,YAAA,GAEC,wBACH,mBAGL,qBACE,qBACA,4BACO,mCACO,sCACG;;;qBDpEnCnB,YAAY,EACZC,mBAAmB,EAAAiF,EAAA,CAAAkC,aAAA,EAAAlC,EAAA,CAAAmC,eAAA,EAAAnC,EAAA,CAAAoC,oBAAA,EAAApC,EAAA,CAAAqC,iBAAA,EAAArC,EAAA,CAAAsC,kBAAA,EAAAtC,EAAA,CAAAuC,eAAA,EACnBrH,mBAAmB,EACnBC,aAAa,EACbC,eAAe,EACfC,cAAc;MAAAmH,MAAA;IAAA;;SAKLhH,cAAc;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}