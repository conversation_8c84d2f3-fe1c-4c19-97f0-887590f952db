{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = this && this.__param || function (paramIndex, decorator) {\n  return function (target, key) {\n    decorator(target, key, paramIndex);\n  };\n};\nimport { DeferredPromise } from '../../../base/common/async.js';\nimport { CancellationTokenSource } from '../../../base/common/cancellation.js';\nimport { Event } from '../../../base/common/event.js';\nimport { Disposable, DisposableStore, toDisposable } from '../../../base/common/lifecycle.js';\nimport { IInstantiationService } from '../../instantiation/common/instantiation.js';\nimport { DefaultQuickAccessFilterValue, Extensions } from '../common/quickAccess.js';\nimport { IQuickInputService, ItemActivation } from '../common/quickInput.js';\nimport { Registry } from '../../registry/common/platform.js';\nlet QuickAccessController = class QuickAccessController extends Disposable {\n  constructor(quickInputService, instantiationService) {\n    super();\n    this.quickInputService = quickInputService;\n    this.instantiationService = instantiationService;\n    this.registry = Registry.as(Extensions.Quickaccess);\n    this.mapProviderToDescriptor = new Map();\n    this.lastAcceptedPickerValues = new Map();\n    this.visibleQuickAccess = undefined;\n  }\n  show(value = '', options) {\n    this.doShowOrPick(value, false, options);\n  }\n  doShowOrPick(value, pick, options) {\n    // Find provider for the value to show\n    const [provider, descriptor] = this.getOrInstantiateProvider(value, options?.enabledProviderPrefixes);\n    // Return early if quick access is already showing on that same prefix\n    const visibleQuickAccess = this.visibleQuickAccess;\n    const visibleDescriptor = visibleQuickAccess?.descriptor;\n    if (visibleQuickAccess && descriptor && visibleDescriptor === descriptor) {\n      // Apply value only if it is more specific than the prefix\n      // from the provider and we are not instructed to preserve\n      if (value !== descriptor.prefix && !options?.preserveValue) {\n        visibleQuickAccess.picker.value = value;\n      }\n      // Always adjust selection\n      this.adjustValueSelection(visibleQuickAccess.picker, descriptor, options);\n      return;\n    }\n    // Rewrite the filter value based on certain rules unless disabled\n    if (descriptor && !options?.preserveValue) {\n      let newValue = undefined;\n      // If we have a visible provider with a value, take it's filter value but\n      // rewrite to new provider prefix in case they differ\n      if (visibleQuickAccess && visibleDescriptor && visibleDescriptor !== descriptor) {\n        const newValueCandidateWithoutPrefix = visibleQuickAccess.value.substr(visibleDescriptor.prefix.length);\n        if (newValueCandidateWithoutPrefix) {\n          newValue = `${descriptor.prefix}${newValueCandidateWithoutPrefix}`;\n        }\n      }\n      // Otherwise, take a default value as instructed\n      if (!newValue) {\n        const defaultFilterValue = provider?.defaultFilterValue;\n        if (defaultFilterValue === DefaultQuickAccessFilterValue.LAST) {\n          newValue = this.lastAcceptedPickerValues.get(descriptor);\n        } else if (typeof defaultFilterValue === 'string') {\n          newValue = `${descriptor.prefix}${defaultFilterValue}`;\n        }\n      }\n      if (typeof newValue === 'string') {\n        value = newValue;\n      }\n    }\n    // Store the existing selection if there was one.\n    const visibleSelection = visibleQuickAccess?.picker?.valueSelection;\n    const visibleValue = visibleQuickAccess?.picker?.value;\n    // Create a picker for the provider to use with the initial value\n    // and adjust the filtering to exclude the prefix from filtering\n    const disposables = new DisposableStore();\n    const picker = disposables.add(this.quickInputService.createQuickPick({\n      useSeparators: true\n    }));\n    picker.value = value;\n    this.adjustValueSelection(picker, descriptor, options);\n    picker.placeholder = options?.placeholder ?? descriptor?.placeholder;\n    picker.quickNavigate = options?.quickNavigateConfiguration;\n    picker.hideInput = !!picker.quickNavigate && !visibleQuickAccess; // only hide input if there was no picker opened already\n    if (typeof options?.itemActivation === 'number' || options?.quickNavigateConfiguration) {\n      picker.itemActivation = options?.itemActivation ?? ItemActivation.SECOND /* quick nav is always second */;\n    }\n    picker.contextKey = descriptor?.contextKey;\n    picker.filterValue = value => value.substring(descriptor ? descriptor.prefix.length : 0);\n    // Pick mode: setup a promise that can be resolved\n    // with the selected items and prevent execution\n    let pickPromise = undefined;\n    if (pick) {\n      pickPromise = new DeferredPromise();\n      disposables.add(Event.once(picker.onWillAccept)(e => {\n        e.veto();\n        picker.hide();\n      }));\n    }\n    // Register listeners\n    disposables.add(this.registerPickerListeners(picker, provider, descriptor, value, options));\n    // Ask provider to fill the picker as needed if we have one\n    // and pass over a cancellation token that will indicate when\n    // the picker is hiding without a pick being made.\n    const cts = disposables.add(new CancellationTokenSource());\n    if (provider) {\n      disposables.add(provider.provide(picker, cts.token, options?.providerOptions));\n    }\n    // Finally, trigger disposal and cancellation when the picker\n    // hides depending on items selected or not.\n    Event.once(picker.onDidHide)(() => {\n      if (picker.selectedItems.length === 0) {\n        cts.cancel();\n      }\n      // Start to dispose once picker hides\n      disposables.dispose();\n      // Resolve pick promise with selected items\n      pickPromise?.complete(picker.selectedItems.slice(0));\n    });\n    // Finally, show the picker. This is important because a provider\n    // may not call this and then our disposables would leak that rely\n    // on the onDidHide event.\n    picker.show();\n    // If the previous picker had a selection and the value is unchanged, we should set that in the new picker.\n    if (visibleSelection && visibleValue === value) {\n      picker.valueSelection = visibleSelection;\n    }\n    // Pick mode: return with promise\n    if (pick) {\n      return pickPromise?.p;\n    }\n  }\n  adjustValueSelection(picker, descriptor, options) {\n    let valueSelection;\n    // Preserve: just always put the cursor at the end\n    if (options?.preserveValue) {\n      valueSelection = [picker.value.length, picker.value.length];\n    }\n    // Otherwise: select the value up until the prefix\n    else {\n      valueSelection = [descriptor?.prefix.length ?? 0, picker.value.length];\n    }\n    picker.valueSelection = valueSelection;\n  }\n  registerPickerListeners(picker, provider, descriptor, value, options) {\n    const disposables = new DisposableStore();\n    // Remember as last visible picker and clean up once picker get's disposed\n    const visibleQuickAccess = this.visibleQuickAccess = {\n      picker,\n      descriptor,\n      value\n    };\n    disposables.add(toDisposable(() => {\n      if (visibleQuickAccess === this.visibleQuickAccess) {\n        this.visibleQuickAccess = undefined;\n      }\n    }));\n    // Whenever the value changes, check if the provider has\n    // changed and if so - re-create the picker from the beginning\n    disposables.add(picker.onDidChangeValue(value => {\n      const [providerForValue] = this.getOrInstantiateProvider(value, options?.enabledProviderPrefixes);\n      if (providerForValue !== provider) {\n        this.show(value, {\n          enabledProviderPrefixes: options?.enabledProviderPrefixes,\n          // do not rewrite value from user typing!\n          preserveValue: true,\n          // persist the value of the providerOptions from the original showing\n          providerOptions: options?.providerOptions\n        });\n      } else {\n        visibleQuickAccess.value = value; // remember the value in our visible one\n      }\n    }));\n    // Remember picker input for future use when accepting\n    if (descriptor) {\n      disposables.add(picker.onDidAccept(() => {\n        this.lastAcceptedPickerValues.set(descriptor, picker.value);\n      }));\n    }\n    return disposables;\n  }\n  getOrInstantiateProvider(value, enabledProviderPrefixes) {\n    const providerDescriptor = this.registry.getQuickAccessProvider(value);\n    if (!providerDescriptor || enabledProviderPrefixes && !enabledProviderPrefixes?.includes(providerDescriptor.prefix)) {\n      return [undefined, undefined];\n    }\n    let provider = this.mapProviderToDescriptor.get(providerDescriptor);\n    if (!provider) {\n      provider = this.instantiationService.createInstance(providerDescriptor.ctor);\n      this.mapProviderToDescriptor.set(providerDescriptor, provider);\n    }\n    return [provider, providerDescriptor];\n  }\n};\nQuickAccessController = __decorate([__param(0, IQuickInputService), __param(1, IInstantiationService)], QuickAccessController);\nexport { QuickAccessController };", "map": {"version": 3, "names": ["__decorate", "decorators", "target", "key", "desc", "c", "arguments", "length", "r", "Object", "getOwnPropertyDescriptor", "d", "Reflect", "decorate", "i", "defineProperty", "__param", "paramIndex", "decorator", "DeferredPromise", "CancellationTokenSource", "Event", "Disposable", "DisposableStore", "toDisposable", "IInstantiationService", "DefaultQuickAccessFilterValue", "Extensions", "IQuickInputService", "ItemActivation", "Registry", "QuickAccessController", "constructor", "quickInputService", "instantiationService", "registry", "as", "Quickaccess", "mapProviderToDescriptor", "Map", "lastAcceptedPickerV<PERSON>ues", "visibleQuickAccess", "undefined", "show", "value", "options", "doShowOrPick", "pick", "provider", "descriptor", "getOrInstantiateProvider", "enabledProviderPrefixes", "visibleDescriptor", "prefix", "preserveValue", "picker", "adjustValueSelection", "newValue", "newValueCandidateWithoutPrefix", "substr", "defaultFilterValue", "LAST", "get", "visibleSelection", "valueSelection", "visibleValue", "disposables", "add", "createQuickPick", "useSeparators", "placeholder", "quickNavigate", "quickNavigateConfiguration", "hideInput", "itemActivation", "SECOND", "<PERSON><PERSON>ey", "filterValue", "substring", "pickPromise", "once", "onWillAccept", "e", "veto", "hide", "registerPickerListeners", "cts", "provide", "token", "providerOptions", "onDidHide", "selectedItems", "cancel", "dispose", "complete", "slice", "p", "onDidChangeValue", "providerForValue", "onDidAccept", "set", "providerDescriptor", "getQuickAccessProvider", "includes", "createInstance", "ctor"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/platform/quickinput/browser/quickAccess.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = (this && this.__param) || function (paramIndex, decorator) {\n    return function (target, key) { decorator(target, key, paramIndex); }\n};\nimport { DeferredPromise } from '../../../base/common/async.js';\nimport { CancellationTokenSource } from '../../../base/common/cancellation.js';\nimport { Event } from '../../../base/common/event.js';\nimport { Disposable, DisposableStore, toDisposable } from '../../../base/common/lifecycle.js';\nimport { IInstantiationService } from '../../instantiation/common/instantiation.js';\nimport { DefaultQuickAccessFilterValue, Extensions } from '../common/quickAccess.js';\nimport { IQuickInputService, ItemActivation } from '../common/quickInput.js';\nimport { Registry } from '../../registry/common/platform.js';\nlet QuickAccessController = class QuickAccessController extends Disposable {\n    constructor(quickInputService, instantiationService) {\n        super();\n        this.quickInputService = quickInputService;\n        this.instantiationService = instantiationService;\n        this.registry = Registry.as(Extensions.Quickaccess);\n        this.mapProviderToDescriptor = new Map();\n        this.lastAcceptedPickerValues = new Map();\n        this.visibleQuickAccess = undefined;\n    }\n    show(value = '', options) {\n        this.doShowOrPick(value, false, options);\n    }\n    doShowOrPick(value, pick, options) {\n        // Find provider for the value to show\n        const [provider, descriptor] = this.getOrInstantiateProvider(value, options?.enabledProviderPrefixes);\n        // Return early if quick access is already showing on that same prefix\n        const visibleQuickAccess = this.visibleQuickAccess;\n        const visibleDescriptor = visibleQuickAccess?.descriptor;\n        if (visibleQuickAccess && descriptor && visibleDescriptor === descriptor) {\n            // Apply value only if it is more specific than the prefix\n            // from the provider and we are not instructed to preserve\n            if (value !== descriptor.prefix && !options?.preserveValue) {\n                visibleQuickAccess.picker.value = value;\n            }\n            // Always adjust selection\n            this.adjustValueSelection(visibleQuickAccess.picker, descriptor, options);\n            return;\n        }\n        // Rewrite the filter value based on certain rules unless disabled\n        if (descriptor && !options?.preserveValue) {\n            let newValue = undefined;\n            // If we have a visible provider with a value, take it's filter value but\n            // rewrite to new provider prefix in case they differ\n            if (visibleQuickAccess && visibleDescriptor && visibleDescriptor !== descriptor) {\n                const newValueCandidateWithoutPrefix = visibleQuickAccess.value.substr(visibleDescriptor.prefix.length);\n                if (newValueCandidateWithoutPrefix) {\n                    newValue = `${descriptor.prefix}${newValueCandidateWithoutPrefix}`;\n                }\n            }\n            // Otherwise, take a default value as instructed\n            if (!newValue) {\n                const defaultFilterValue = provider?.defaultFilterValue;\n                if (defaultFilterValue === DefaultQuickAccessFilterValue.LAST) {\n                    newValue = this.lastAcceptedPickerValues.get(descriptor);\n                }\n                else if (typeof defaultFilterValue === 'string') {\n                    newValue = `${descriptor.prefix}${defaultFilterValue}`;\n                }\n            }\n            if (typeof newValue === 'string') {\n                value = newValue;\n            }\n        }\n        // Store the existing selection if there was one.\n        const visibleSelection = visibleQuickAccess?.picker?.valueSelection;\n        const visibleValue = visibleQuickAccess?.picker?.value;\n        // Create a picker for the provider to use with the initial value\n        // and adjust the filtering to exclude the prefix from filtering\n        const disposables = new DisposableStore();\n        const picker = disposables.add(this.quickInputService.createQuickPick({ useSeparators: true }));\n        picker.value = value;\n        this.adjustValueSelection(picker, descriptor, options);\n        picker.placeholder = options?.placeholder ?? descriptor?.placeholder;\n        picker.quickNavigate = options?.quickNavigateConfiguration;\n        picker.hideInput = !!picker.quickNavigate && !visibleQuickAccess; // only hide input if there was no picker opened already\n        if (typeof options?.itemActivation === 'number' || options?.quickNavigateConfiguration) {\n            picker.itemActivation = options?.itemActivation ?? ItemActivation.SECOND /* quick nav is always second */;\n        }\n        picker.contextKey = descriptor?.contextKey;\n        picker.filterValue = (value) => value.substring(descriptor ? descriptor.prefix.length : 0);\n        // Pick mode: setup a promise that can be resolved\n        // with the selected items and prevent execution\n        let pickPromise = undefined;\n        if (pick) {\n            pickPromise = new DeferredPromise();\n            disposables.add(Event.once(picker.onWillAccept)(e => {\n                e.veto();\n                picker.hide();\n            }));\n        }\n        // Register listeners\n        disposables.add(this.registerPickerListeners(picker, provider, descriptor, value, options));\n        // Ask provider to fill the picker as needed if we have one\n        // and pass over a cancellation token that will indicate when\n        // the picker is hiding without a pick being made.\n        const cts = disposables.add(new CancellationTokenSource());\n        if (provider) {\n            disposables.add(provider.provide(picker, cts.token, options?.providerOptions));\n        }\n        // Finally, trigger disposal and cancellation when the picker\n        // hides depending on items selected or not.\n        Event.once(picker.onDidHide)(() => {\n            if (picker.selectedItems.length === 0) {\n                cts.cancel();\n            }\n            // Start to dispose once picker hides\n            disposables.dispose();\n            // Resolve pick promise with selected items\n            pickPromise?.complete(picker.selectedItems.slice(0));\n        });\n        // Finally, show the picker. This is important because a provider\n        // may not call this and then our disposables would leak that rely\n        // on the onDidHide event.\n        picker.show();\n        // If the previous picker had a selection and the value is unchanged, we should set that in the new picker.\n        if (visibleSelection && visibleValue === value) {\n            picker.valueSelection = visibleSelection;\n        }\n        // Pick mode: return with promise\n        if (pick) {\n            return pickPromise?.p;\n        }\n    }\n    adjustValueSelection(picker, descriptor, options) {\n        let valueSelection;\n        // Preserve: just always put the cursor at the end\n        if (options?.preserveValue) {\n            valueSelection = [picker.value.length, picker.value.length];\n        }\n        // Otherwise: select the value up until the prefix\n        else {\n            valueSelection = [descriptor?.prefix.length ?? 0, picker.value.length];\n        }\n        picker.valueSelection = valueSelection;\n    }\n    registerPickerListeners(picker, provider, descriptor, value, options) {\n        const disposables = new DisposableStore();\n        // Remember as last visible picker and clean up once picker get's disposed\n        const visibleQuickAccess = this.visibleQuickAccess = { picker, descriptor, value };\n        disposables.add(toDisposable(() => {\n            if (visibleQuickAccess === this.visibleQuickAccess) {\n                this.visibleQuickAccess = undefined;\n            }\n        }));\n        // Whenever the value changes, check if the provider has\n        // changed and if so - re-create the picker from the beginning\n        disposables.add(picker.onDidChangeValue(value => {\n            const [providerForValue] = this.getOrInstantiateProvider(value, options?.enabledProviderPrefixes);\n            if (providerForValue !== provider) {\n                this.show(value, {\n                    enabledProviderPrefixes: options?.enabledProviderPrefixes,\n                    // do not rewrite value from user typing!\n                    preserveValue: true,\n                    // persist the value of the providerOptions from the original showing\n                    providerOptions: options?.providerOptions\n                });\n            }\n            else {\n                visibleQuickAccess.value = value; // remember the value in our visible one\n            }\n        }));\n        // Remember picker input for future use when accepting\n        if (descriptor) {\n            disposables.add(picker.onDidAccept(() => {\n                this.lastAcceptedPickerValues.set(descriptor, picker.value);\n            }));\n        }\n        return disposables;\n    }\n    getOrInstantiateProvider(value, enabledProviderPrefixes) {\n        const providerDescriptor = this.registry.getQuickAccessProvider(value);\n        if (!providerDescriptor || enabledProviderPrefixes && !enabledProviderPrefixes?.includes(providerDescriptor.prefix)) {\n            return [undefined, undefined];\n        }\n        let provider = this.mapProviderToDescriptor.get(providerDescriptor);\n        if (!provider) {\n            provider = this.instantiationService.createInstance(providerDescriptor.ctor);\n            this.mapProviderToDescriptor.set(providerDescriptor, provider);\n        }\n        return [provider, providerDescriptor];\n    }\n};\nQuickAccessController = __decorate([\n    __param(0, IQuickInputService),\n    __param(1, IInstantiationService)\n], QuickAccessController);\nexport { QuickAccessController };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,IAAIA,UAAU,GAAI,IAAI,IAAI,IAAI,CAACA,UAAU,IAAK,UAAUC,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAE;EACnF,IAAIC,CAAC,GAAGC,SAAS,CAACC,MAAM;IAAEC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGH,MAAM,GAAGE,IAAI,KAAK,IAAI,GAAGA,IAAI,GAAGK,MAAM,CAACC,wBAAwB,CAACR,MAAM,EAAEC,GAAG,CAAC,GAAGC,IAAI;IAAEO,CAAC;EAC5H,IAAI,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,CAACC,QAAQ,KAAK,UAAU,EAAEL,CAAC,GAAGI,OAAO,CAACC,QAAQ,CAACZ,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,CAAC,CAAC,KAC1H,KAAK,IAAIU,CAAC,GAAGb,UAAU,CAACM,MAAM,GAAG,CAAC,EAAEO,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE,IAAIH,CAAC,GAAGV,UAAU,CAACa,CAAC,CAAC,EAAEN,CAAC,GAAG,CAACH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACH,CAAC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACT,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,GAAGG,CAAC,CAACT,MAAM,EAAEC,GAAG,CAAC,KAAKK,CAAC;EACjJ,OAAOH,CAAC,GAAG,CAAC,IAAIG,CAAC,IAAIC,MAAM,CAACM,cAAc,CAACb,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,EAAEA,CAAC;AACjE,CAAC;AACD,IAAIQ,OAAO,GAAI,IAAI,IAAI,IAAI,CAACA,OAAO,IAAK,UAAUC,UAAU,EAAEC,SAAS,EAAE;EACrE,OAAO,UAAUhB,MAAM,EAAEC,GAAG,EAAE;IAAEe,SAAS,CAAChB,MAAM,EAAEC,GAAG,EAAEc,UAAU,CAAC;EAAE,CAAC;AACzE,CAAC;AACD,SAASE,eAAe,QAAQ,+BAA+B;AAC/D,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,KAAK,QAAQ,+BAA+B;AACrD,SAASC,UAAU,EAAEC,eAAe,EAAEC,YAAY,QAAQ,mCAAmC;AAC7F,SAASC,qBAAqB,QAAQ,6CAA6C;AACnF,SAASC,6BAA6B,EAAEC,UAAU,QAAQ,0BAA0B;AACpF,SAASC,kBAAkB,EAAEC,cAAc,QAAQ,yBAAyB;AAC5E,SAASC,QAAQ,QAAQ,mCAAmC;AAC5D,IAAIC,qBAAqB,GAAG,MAAMA,qBAAqB,SAAST,UAAU,CAAC;EACvEU,WAAWA,CAACC,iBAAiB,EAAEC,oBAAoB,EAAE;IACjD,KAAK,CAAC,CAAC;IACP,IAAI,CAACD,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACC,oBAAoB,GAAGA,oBAAoB;IAChD,IAAI,CAACC,QAAQ,GAAGL,QAAQ,CAACM,EAAE,CAACT,UAAU,CAACU,WAAW,CAAC;IACnD,IAAI,CAACC,uBAAuB,GAAG,IAAIC,GAAG,CAAC,CAAC;IACxC,IAAI,CAACC,wBAAwB,GAAG,IAAID,GAAG,CAAC,CAAC;IACzC,IAAI,CAACE,kBAAkB,GAAGC,SAAS;EACvC;EACAC,IAAIA,CAACC,KAAK,GAAG,EAAE,EAAEC,OAAO,EAAE;IACtB,IAAI,CAACC,YAAY,CAACF,KAAK,EAAE,KAAK,EAAEC,OAAO,CAAC;EAC5C;EACAC,YAAYA,CAACF,KAAK,EAAEG,IAAI,EAAEF,OAAO,EAAE;IAC/B;IACA,MAAM,CAACG,QAAQ,EAAEC,UAAU,CAAC,GAAG,IAAI,CAACC,wBAAwB,CAACN,KAAK,EAAEC,OAAO,EAAEM,uBAAuB,CAAC;IACrG;IACA,MAAMV,kBAAkB,GAAG,IAAI,CAACA,kBAAkB;IAClD,MAAMW,iBAAiB,GAAGX,kBAAkB,EAAEQ,UAAU;IACxD,IAAIR,kBAAkB,IAAIQ,UAAU,IAAIG,iBAAiB,KAAKH,UAAU,EAAE;MACtE;MACA;MACA,IAAIL,KAAK,KAAKK,UAAU,CAACI,MAAM,IAAI,CAACR,OAAO,EAAES,aAAa,EAAE;QACxDb,kBAAkB,CAACc,MAAM,CAACX,KAAK,GAAGA,KAAK;MAC3C;MACA;MACA,IAAI,CAACY,oBAAoB,CAACf,kBAAkB,CAACc,MAAM,EAAEN,UAAU,EAAEJ,OAAO,CAAC;MACzE;IACJ;IACA;IACA,IAAII,UAAU,IAAI,CAACJ,OAAO,EAAES,aAAa,EAAE;MACvC,IAAIG,QAAQ,GAAGf,SAAS;MACxB;MACA;MACA,IAAID,kBAAkB,IAAIW,iBAAiB,IAAIA,iBAAiB,KAAKH,UAAU,EAAE;QAC7E,MAAMS,8BAA8B,GAAGjB,kBAAkB,CAACG,KAAK,CAACe,MAAM,CAACP,iBAAiB,CAACC,MAAM,CAAC9C,MAAM,CAAC;QACvG,IAAImD,8BAA8B,EAAE;UAChCD,QAAQ,GAAG,GAAGR,UAAU,CAACI,MAAM,GAAGK,8BAA8B,EAAE;QACtE;MACJ;MACA;MACA,IAAI,CAACD,QAAQ,EAAE;QACX,MAAMG,kBAAkB,GAAGZ,QAAQ,EAAEY,kBAAkB;QACvD,IAAIA,kBAAkB,KAAKlC,6BAA6B,CAACmC,IAAI,EAAE;UAC3DJ,QAAQ,GAAG,IAAI,CAACjB,wBAAwB,CAACsB,GAAG,CAACb,UAAU,CAAC;QAC5D,CAAC,MACI,IAAI,OAAOW,kBAAkB,KAAK,QAAQ,EAAE;UAC7CH,QAAQ,GAAG,GAAGR,UAAU,CAACI,MAAM,GAAGO,kBAAkB,EAAE;QAC1D;MACJ;MACA,IAAI,OAAOH,QAAQ,KAAK,QAAQ,EAAE;QAC9Bb,KAAK,GAAGa,QAAQ;MACpB;IACJ;IACA;IACA,MAAMM,gBAAgB,GAAGtB,kBAAkB,EAAEc,MAAM,EAAES,cAAc;IACnE,MAAMC,YAAY,GAAGxB,kBAAkB,EAAEc,MAAM,EAAEX,KAAK;IACtD;IACA;IACA,MAAMsB,WAAW,GAAG,IAAI3C,eAAe,CAAC,CAAC;IACzC,MAAMgC,MAAM,GAAGW,WAAW,CAACC,GAAG,CAAC,IAAI,CAAClC,iBAAiB,CAACmC,eAAe,CAAC;MAAEC,aAAa,EAAE;IAAK,CAAC,CAAC,CAAC;IAC/Fd,MAAM,CAACX,KAAK,GAAGA,KAAK;IACpB,IAAI,CAACY,oBAAoB,CAACD,MAAM,EAAEN,UAAU,EAAEJ,OAAO,CAAC;IACtDU,MAAM,CAACe,WAAW,GAAGzB,OAAO,EAAEyB,WAAW,IAAIrB,UAAU,EAAEqB,WAAW;IACpEf,MAAM,CAACgB,aAAa,GAAG1B,OAAO,EAAE2B,0BAA0B;IAC1DjB,MAAM,CAACkB,SAAS,GAAG,CAAC,CAAClB,MAAM,CAACgB,aAAa,IAAI,CAAC9B,kBAAkB,CAAC,CAAC;IAClE,IAAI,OAAOI,OAAO,EAAE6B,cAAc,KAAK,QAAQ,IAAI7B,OAAO,EAAE2B,0BAA0B,EAAE;MACpFjB,MAAM,CAACmB,cAAc,GAAG7B,OAAO,EAAE6B,cAAc,IAAI7C,cAAc,CAAC8C,MAAM,CAAC;IAC7E;IACApB,MAAM,CAACqB,UAAU,GAAG3B,UAAU,EAAE2B,UAAU;IAC1CrB,MAAM,CAACsB,WAAW,GAAIjC,KAAK,IAAKA,KAAK,CAACkC,SAAS,CAAC7B,UAAU,GAAGA,UAAU,CAACI,MAAM,CAAC9C,MAAM,GAAG,CAAC,CAAC;IAC1F;IACA;IACA,IAAIwE,WAAW,GAAGrC,SAAS;IAC3B,IAAIK,IAAI,EAAE;MACNgC,WAAW,GAAG,IAAI5D,eAAe,CAAC,CAAC;MACnC+C,WAAW,CAACC,GAAG,CAAC9C,KAAK,CAAC2D,IAAI,CAACzB,MAAM,CAAC0B,YAAY,CAAC,CAACC,CAAC,IAAI;QACjDA,CAAC,CAACC,IAAI,CAAC,CAAC;QACR5B,MAAM,CAAC6B,IAAI,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC;IACP;IACA;IACAlB,WAAW,CAACC,GAAG,CAAC,IAAI,CAACkB,uBAAuB,CAAC9B,MAAM,EAAEP,QAAQ,EAAEC,UAAU,EAAEL,KAAK,EAAEC,OAAO,CAAC,CAAC;IAC3F;IACA;IACA;IACA,MAAMyC,GAAG,GAAGpB,WAAW,CAACC,GAAG,CAAC,IAAI/C,uBAAuB,CAAC,CAAC,CAAC;IAC1D,IAAI4B,QAAQ,EAAE;MACVkB,WAAW,CAACC,GAAG,CAACnB,QAAQ,CAACuC,OAAO,CAAChC,MAAM,EAAE+B,GAAG,CAACE,KAAK,EAAE3C,OAAO,EAAE4C,eAAe,CAAC,CAAC;IAClF;IACA;IACA;IACApE,KAAK,CAAC2D,IAAI,CAACzB,MAAM,CAACmC,SAAS,CAAC,CAAC,MAAM;MAC/B,IAAInC,MAAM,CAACoC,aAAa,CAACpF,MAAM,KAAK,CAAC,EAAE;QACnC+E,GAAG,CAACM,MAAM,CAAC,CAAC;MAChB;MACA;MACA1B,WAAW,CAAC2B,OAAO,CAAC,CAAC;MACrB;MACAd,WAAW,EAAEe,QAAQ,CAACvC,MAAM,CAACoC,aAAa,CAACI,KAAK,CAAC,CAAC,CAAC,CAAC;IACxD,CAAC,CAAC;IACF;IACA;IACA;IACAxC,MAAM,CAACZ,IAAI,CAAC,CAAC;IACb;IACA,IAAIoB,gBAAgB,IAAIE,YAAY,KAAKrB,KAAK,EAAE;MAC5CW,MAAM,CAACS,cAAc,GAAGD,gBAAgB;IAC5C;IACA;IACA,IAAIhB,IAAI,EAAE;MACN,OAAOgC,WAAW,EAAEiB,CAAC;IACzB;EACJ;EACAxC,oBAAoBA,CAACD,MAAM,EAAEN,UAAU,EAAEJ,OAAO,EAAE;IAC9C,IAAImB,cAAc;IAClB;IACA,IAAInB,OAAO,EAAES,aAAa,EAAE;MACxBU,cAAc,GAAG,CAACT,MAAM,CAACX,KAAK,CAACrC,MAAM,EAAEgD,MAAM,CAACX,KAAK,CAACrC,MAAM,CAAC;IAC/D;IACA;IAAA,KACK;MACDyD,cAAc,GAAG,CAACf,UAAU,EAAEI,MAAM,CAAC9C,MAAM,IAAI,CAAC,EAAEgD,MAAM,CAACX,KAAK,CAACrC,MAAM,CAAC;IAC1E;IACAgD,MAAM,CAACS,cAAc,GAAGA,cAAc;EAC1C;EACAqB,uBAAuBA,CAAC9B,MAAM,EAAEP,QAAQ,EAAEC,UAAU,EAAEL,KAAK,EAAEC,OAAO,EAAE;IAClE,MAAMqB,WAAW,GAAG,IAAI3C,eAAe,CAAC,CAAC;IACzC;IACA,MAAMkB,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,GAAG;MAAEc,MAAM;MAAEN,UAAU;MAAEL;IAAM,CAAC;IAClFsB,WAAW,CAACC,GAAG,CAAC3C,YAAY,CAAC,MAAM;MAC/B,IAAIiB,kBAAkB,KAAK,IAAI,CAACA,kBAAkB,EAAE;QAChD,IAAI,CAACA,kBAAkB,GAAGC,SAAS;MACvC;IACJ,CAAC,CAAC,CAAC;IACH;IACA;IACAwB,WAAW,CAACC,GAAG,CAACZ,MAAM,CAAC0C,gBAAgB,CAACrD,KAAK,IAAI;MAC7C,MAAM,CAACsD,gBAAgB,CAAC,GAAG,IAAI,CAAChD,wBAAwB,CAACN,KAAK,EAAEC,OAAO,EAAEM,uBAAuB,CAAC;MACjG,IAAI+C,gBAAgB,KAAKlD,QAAQ,EAAE;QAC/B,IAAI,CAACL,IAAI,CAACC,KAAK,EAAE;UACbO,uBAAuB,EAAEN,OAAO,EAAEM,uBAAuB;UACzD;UACAG,aAAa,EAAE,IAAI;UACnB;UACAmC,eAAe,EAAE5C,OAAO,EAAE4C;QAC9B,CAAC,CAAC;MACN,CAAC,MACI;QACDhD,kBAAkB,CAACG,KAAK,GAAGA,KAAK,CAAC,CAAC;MACtC;IACJ,CAAC,CAAC,CAAC;IACH;IACA,IAAIK,UAAU,EAAE;MACZiB,WAAW,CAACC,GAAG,CAACZ,MAAM,CAAC4C,WAAW,CAAC,MAAM;QACrC,IAAI,CAAC3D,wBAAwB,CAAC4D,GAAG,CAACnD,UAAU,EAAEM,MAAM,CAACX,KAAK,CAAC;MAC/D,CAAC,CAAC,CAAC;IACP;IACA,OAAOsB,WAAW;EACtB;EACAhB,wBAAwBA,CAACN,KAAK,EAAEO,uBAAuB,EAAE;IACrD,MAAMkD,kBAAkB,GAAG,IAAI,CAAClE,QAAQ,CAACmE,sBAAsB,CAAC1D,KAAK,CAAC;IACtE,IAAI,CAACyD,kBAAkB,IAAIlD,uBAAuB,IAAI,CAACA,uBAAuB,EAAEoD,QAAQ,CAACF,kBAAkB,CAAChD,MAAM,CAAC,EAAE;MACjH,OAAO,CAACX,SAAS,EAAEA,SAAS,CAAC;IACjC;IACA,IAAIM,QAAQ,GAAG,IAAI,CAACV,uBAAuB,CAACwB,GAAG,CAACuC,kBAAkB,CAAC;IACnE,IAAI,CAACrD,QAAQ,EAAE;MACXA,QAAQ,GAAG,IAAI,CAACd,oBAAoB,CAACsE,cAAc,CAACH,kBAAkB,CAACI,IAAI,CAAC;MAC5E,IAAI,CAACnE,uBAAuB,CAAC8D,GAAG,CAACC,kBAAkB,EAAErD,QAAQ,CAAC;IAClE;IACA,OAAO,CAACA,QAAQ,EAAEqD,kBAAkB,CAAC;EACzC;AACJ,CAAC;AACDtE,qBAAqB,GAAG/B,UAAU,CAAC,CAC/BgB,OAAO,CAAC,CAAC,EAAEY,kBAAkB,CAAC,EAC9BZ,OAAO,CAAC,CAAC,EAAES,qBAAqB,CAAC,CACpC,EAAEM,qBAAqB,CAAC;AACzB,SAASA,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}