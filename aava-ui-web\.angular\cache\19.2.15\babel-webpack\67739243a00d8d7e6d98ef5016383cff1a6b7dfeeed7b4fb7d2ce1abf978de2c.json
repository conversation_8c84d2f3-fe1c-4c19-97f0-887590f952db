{"ast": null, "code": "import { CommonModule, DatePipe } from '@angular/common';\nimport { of, map, catchError, Subject, takeUntil, debounceTime, distinctUntilChanged, startWith } from 'rxjs';\nimport { PageFooterComponent } from '@shared/components/page-footer/page-footer.component';\nimport { AvaTextboxComponent, DropdownComponent, IconComponent, TextCardComponent } from '@ava/play-comp-library';\nimport { LucideAngularModule } from 'lucide-angular';\nimport knowledgeBaseLabels from '../knowledge-base/constants/knowledge-base.json';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { ConsoleCardComponent } from \"@shared/components/console-card/console-card.component\";\nimport { TimeAgoPipe } from '@shared/pipes/time-ago.pipe';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@shared/services/knowledge-base.service\";\nimport * as i2 from \"@shared/services/pagination.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nfunction KnowledgeBaseComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"div\", 14)(2, \"h5\", 15);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.kbLabels.noResults);\n  }\n}\nfunction KnowledgeBaseComponent_ng_container_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"ava-console-card\", 16);\n    i0.ɵɵpipe(2, \"timeAgo\");\n    i0.ɵɵlistener(\"actionClick\", function KnowledgeBaseComponent_ng_container_11_Template_ava_console_card_actionClick_1_listener($event) {\n      const knowledgeBase_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onActionClick($event, knowledgeBase_r3.id));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const knowledgeBase_r3 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"title\", knowledgeBase_r3 == null ? null : knowledgeBase_r3.title)(\"description\", knowledgeBase_r3 == null ? null : knowledgeBase_r3.description)(\"author\", (knowledgeBase_r3 == null ? null : knowledgeBase_r3.owner) || \"AAVA\")(\"date\", i0.ɵɵpipeBind1(2, 6, knowledgeBase_r3 == null ? null : knowledgeBase_r3.createdDate))(\"actions\", ctx_r0.defaultActions)(\"skeleton\", ctx_r0.isLoading);\n  }\n}\nfunction KnowledgeBaseComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"div\", 18)(2, \"app-page-footer\", 19);\n    i0.ɵɵlistener(\"pageChange\", function KnowledgeBaseComponent_div_12_Template_app_page_footer_pageChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onPageChange($event));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"totalItems\", ctx_r0.filteredKnowledgeBase.length + 1)(\"currentPage\", ctx_r0.currentPage)(\"itemsPerPage\", ctx_r0.itemsPerPage);\n  }\n}\nexport let KnowledgeBaseComponent = /*#__PURE__*/(() => {\n  class KnowledgeBaseComponent {\n    knowledgeBaseService;\n    paginationService;\n    router;\n    datePipe;\n    fb;\n    searchForm;\n    search;\n    kbLabels = knowledgeBaseLabels.labels;\n    allKnowledgeBase = [];\n    filteredKnowledgeBase = [];\n    displayedKnowledgeBase = [];\n    isLoading = false;\n    error = null;\n    currentPage = 1;\n    itemsPerPage = 11;\n    totalPages = 1;\n    showInfoPopup = false;\n    iconName = '';\n    infoMessage = '';\n    destroy$ = new Subject();\n    knowledgeBaseOptions = [{\n      name: 'All',\n      value: 'all'\n    }, {\n      name: 'Type A',\n      value: 'typeA'\n    }, {\n      name: 'Type B',\n      value: 'typeB'\n    }];\n    selectedData = null;\n    defaultActions = [{\n      id: 'delete',\n      icon: 'trash',\n      label: 'Delete item',\n      tooltip: 'Delete'\n    }, {\n      id: 'edit',\n      icon: 'edit',\n      label: 'Edit item',\n      tooltip: 'Edit'\n    }];\n    showDeletePopup = false;\n    knowledgeBaseToDelete = null;\n    cardSkeletonPlaceholders = Array(11);\n    constructor(knowledgeBaseService, paginationService, router, datePipe, fb) {\n      this.knowledgeBaseService = knowledgeBaseService;\n      this.paginationService = paginationService;\n      this.router = router;\n      this.datePipe = datePipe;\n      this.fb = fb;\n      this.searchForm = this.fb.group({\n        search: ['']\n      });\n    }\n    ngOnInit() {\n      this.searchForm.get('search').valueChanges.pipe(startWith(''), debounceTime(300), distinctUntilChanged(), map(value => value?.toLowerCase() ?? '')).subscribe(searchText => {\n        this.filterKnowledgeBase(searchText);\n      });\n      this.fetchAllKnowledge();\n    }\n    fetchAllKnowledge() {\n      this.isLoading = true;\n      this.knowledgeBaseService.fetchAllKnowledge().pipe(map(response => {\n        return response.map(item => {\n          const formattedDate = this.datePipe.transform(item.createdDate, 'MM/dd/yyyy') || '';\n          return {\n            ...item,\n            title: item.collectionName,\n            createdDate: formattedDate,\n            userCount: item.userCount || 0\n          };\n        });\n      }), catchError(error => {\n        this.isLoading = false;\n        return of([]);\n      }), takeUntil(this.destroy$)).subscribe({\n        next: res => {\n          this.allKnowledgeBase = res;\n          this.filteredKnowledgeBase = [...this.allKnowledgeBase];\n          this.updateDisplayedKnowledgeBase();\n          this.isLoading = false;\n        },\n        error: err => {\n          this.isLoading = false;\n        }\n      });\n    }\n    updateDisplayedKnowledgeBase() {\n      this.itemsPerPage = this.currentPage === 1 ? 12 : 11;\n      const paginationResult = this.paginationService.getPaginatedItems(this.filteredKnowledgeBase, this.currentPage, this.itemsPerPage);\n      this.displayedKnowledgeBase = paginationResult.displayedItems;\n      this.totalPages = paginationResult.totalPages;\n    }\n    filterKnowledgeBase(searchText) {\n      this.filteredKnowledgeBase = this.allKnowledgeBase.filter(know => {\n        const inTitle = know.title?.toLowerCase().includes(searchText);\n        const inDescription = know.description?.toLowerCase().includes(searchText);\n        const inTags = Array.isArray(know.tags) && know.tags?.some(tag => tag.label?.toLowerCase().includes(searchText));\n        return inTitle || inDescription || inTags;\n      });\n      this.updateDisplayedKnowledgeBase();\n    }\n    onCreateKnowledgeBase() {\n      this.router.navigate(['/libraries/knowledge-base/create']);\n    }\n    onCardClicked(knowledgeBaseId) {\n      // Navigate to knowledge base details page\n      this.router.navigate([`/libraries/knowledge-base/edit/${knowledgeBaseId}`]);\n    }\n    onActionClick(event, knowledgeBaseId) {\n      switch (event.actionId) {\n        case 'delete':\n          this.confirmDeleteKnowledgeBase(knowledgeBaseId);\n          break;\n        case 'edit':\n          this.editKnowledgeBase(knowledgeBaseId);\n          break;\n        default:\n          break;\n      }\n    }\n    editKnowledgeBase(knowledgeBaseId) {\n      // Implement duplicate logic\n      this.router.navigate([`/libraries/knowledge-base/edit/${knowledgeBaseId}`]);\n    }\n    deleteKnowledgeBase(knowledgeBaseId) {\n      const collectionObj = this.allKnowledgeBase.find(item => item.id === knowledgeBaseId);\n      const collectionName = collectionObj?.title;\n      if (collectionName) {\n        this.knowledgeBaseService.deleteByCollection(collectionName).subscribe({\n          next: res => {\n            console.log('Knowledge base deleted:', res);\n            this.fetchAllKnowledge(); // Refresh the list\n          },\n          error: err => {\n            console.error('Failed to delete knowledge base:', err);\n          }\n        });\n      } else {\n        console.warn(`Knowledge base with ID ${knowledgeBaseId} not found.`);\n      }\n    }\n    confirmDeleteKnowledgeBase(knowledgeBaseId) {\n      this.knowledgeBaseToDelete = this.allKnowledgeBase.find(item => item.id === knowledgeBaseId);\n      this.showDeletePopup = true;\n    }\n    onConfirmDelete() {\n      const collectionName = this.knowledgeBaseToDelete?.title;\n      if (collectionName) {\n        this.knowledgeBaseService.deleteByCollection(collectionName).subscribe({\n          next: res => {\n            console.log('Knowledge base deleted:', res);\n            // Show success popup\n            this.iconName = 'check-circle';\n            this.infoMessage = 'Knowledge Base deleted successfully.';\n            this.showInfoPopup = true;\n            this.fetchAllKnowledge();\n            this.closeDeletePopup();\n          },\n          error: err => {\n            console.error('Failed to delete knowledge base:', err);\n            this.closeDeletePopup();\n          }\n        });\n      }\n    }\n    closeDeletePopup() {\n      this.showDeletePopup = false;\n      this.knowledgeBaseToDelete = null;\n    }\n    duplicateKnowledgeBase(knowledgeBaseId) {\n      // Implement duplicate logic\n    }\n    onSelectionChange(data) {\n      this.selectedData = data;\n      // Implement filter logic if needed\n    }\n    onPageChange(page) {\n      this.currentPage = page;\n      this.updateDisplayedKnowledgeBase();\n    }\n    get showCreateCard() {\n      return !this.isLoading && !this.error;\n    }\n    handleInfoPopupClose() {\n      this.showInfoPopup = false;\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n    }\n    static ɵfac = function KnowledgeBaseComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || KnowledgeBaseComponent)(i0.ɵɵdirectiveInject(i1.KnowledgeBaseService), i0.ɵɵdirectiveInject(i2.PaginationService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.DatePipe), i0.ɵɵdirectiveInject(i5.FormBuilder));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: KnowledgeBaseComponent,\n      selectors: [[\"app-knowledge-base\"]],\n      features: [i0.ɵɵProvidersFeature([DatePipe])],\n      decls: 13,\n      vars: 10,\n      consts: [[\"id\", \"knowledge-base-container\", 1, \"container-fluid\"], [\"id\", \"search-filter-container\", 1, \"row\", \"g-3\"], [1, \"col-12\", \"col-md-8\", \"col-lg-9\", \"col-xl-10\", \"search-section\"], [3, \"formGroup\"], [\"placeholder\", \"Search \\\"Knowledge Base\\\"\", \"hoverEffect\", \"glow\", \"pressedEffect\", \"solid\", \"formControlName\", \"search\"], [\"slot\", \"icon-start\", \"iconName\", \"search\", \"iconColor\", \"var(--color-brand-primary)\", 3, \"iconSize\"], [1, \"col-12\", \"col-md-4\", \"col-lg-3\", \"col-xl-2\", \"action-buttons\"], [\"dropdownTitle\", \"choose knowledge base\", 3, \"selectionChange\", \"options\"], [\"id\", \"prompts-card-container\", 1, \"row\", \"g-3\"], [\"iconColor\", \"#144692\", 1, \"col-12\", \"col-sm-6\", \"col-md-4\", \"col-lg-3\", \"col-xl-3\", \"col-xxl-2\", \"mt-5\", 3, \"cardClick\", \"type\", \"iconName\", \"title\", \"isLoading\"], [\"class\", \"col-12 d-flex justify-content-center align-items-center py-5\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"row\", 4, \"ngIf\"], [1, \"col-12\", \"d-flex\", \"justify-content-center\", \"align-items-center\", \"py-5\"], [1, \"text-center\"], [1, \"text-muted\"], [\"categoryIcon\", \"book-open\", \"categoryTitle\", \"Models\", \"categoryValue\", \"42\", 1, \"col-12\", \"col-sm-6\", \"col-md-4\", \"col-lg-3\", \"col-xl-3\", \"col-xxl-2\", \"mt-5\", 3, \"actionClick\", \"title\", \"description\", \"author\", \"date\", \"actions\", \"skeleton\"], [1, \"row\"], [1, \"col-12\", \"d-flex\", \"justify-content-center\", \"mt-4\"], [3, \"pageChange\", \"totalItems\", \"currentPage\", \"itemsPerPage\"]],\n      template: function KnowledgeBaseComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"form\", 3)(4, \"ava-textbox\", 4);\n          i0.ɵɵelement(5, \"ava-icon\", 5);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(6, \"div\", 6)(7, \"ava-dropdown\", 7);\n          i0.ɵɵlistener(\"selectionChange\", function KnowledgeBaseComponent_Template_ava_dropdown_selectionChange_7_listener($event) {\n            return ctx.onSelectionChange($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(8, \"div\", 8)(9, \"ava-text-card\", 9);\n          i0.ɵɵlistener(\"cardClick\", function KnowledgeBaseComponent_Template_ava_text_card_cardClick_9_listener() {\n            return ctx.onCreateKnowledgeBase();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(10, KnowledgeBaseComponent_div_10_Template, 4, 1, \"div\", 10)(11, KnowledgeBaseComponent_ng_container_11_Template, 3, 8, \"ng-container\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(12, KnowledgeBaseComponent_div_12_Template, 3, 3, \"div\", 12);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"formGroup\", ctx.searchForm);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"iconSize\", 16);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"options\", ctx.knowledgeBaseOptions);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"type\", \"create\")(\"iconName\", \"plus\")(\"title\", ctx.kbLabels.createKnowledgeBase)(\"isLoading\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.filteredKnowledgeBase.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.isLoading && ctx.displayedKnowledgeBase.length === 0 ? ctx.cardSkeletonPlaceholders : ctx.displayedKnowledgeBase);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.filteredKnowledgeBase.length > 0);\n        }\n      },\n      dependencies: [CommonModule, i4.NgForOf, i4.NgIf, PageFooterComponent, TextCardComponent, AvaTextboxComponent, DropdownComponent, LucideAngularModule, IconComponent, ReactiveFormsModule, i5.ɵNgNoValidate, i5.NgControlStatus, i5.NgControlStatusGroup, i5.FormGroupDirective, i5.FormControlName, ConsoleCardComponent, TimeAgoPipe],\n      styles: [\".ava-dropdown {\\n  width: 100% !important;\\n}\\n\\n.mt-5[_ngcontent-%COMP%] {\\n  margin-top: 2rem;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3Byb2plY3RzL3NoYXJlZC9wYWdlcy9saWJyYXJpZXMva25vd2xlZGdlLWJhc2Uva25vd2xlZGdlLWJhc2UuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxzQkFBQTtBQUNGOztBQUVBO0VBQ0UsZ0JBQUE7QUFDRiIsInNvdXJjZXNDb250ZW50IjpbIjo6bmctZGVlcCAuYXZhLWRyb3Bkb3duIHtcclxuICB3aWR0aDogMTAwJSAhaW1wb3J0YW50O1xyXG59XHJcblxyXG4ubXQtNSB7XHJcbiAgbWFyZ2luLXRvcDogMnJlbTtcclxufVxyXG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n  return KnowledgeBaseComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "DatePipe", "of", "map", "catchError", "Subject", "takeUntil", "debounceTime", "distinctUntilChanged", "startWith", "PageFooterComponent", "AvaTextboxComponent", "DropdownComponent", "IconComponent", "TextCardComponent", "LucideAngularModule", "knowledgeBaseLabels", "ReactiveFormsModule", "ConsoleCardComponent", "TimeAgoPipe", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "kbLabels", "noResults", "ɵɵelementContainerStart", "ɵɵlistener", "KnowledgeBaseComponent_ng_container_11_Template_ava_console_card_actionClick_1_listener", "$event", "knowledgeBase_r3", "ɵɵrestoreView", "_r2", "$implicit", "ɵɵnextContext", "ɵɵresetView", "onActionClick", "id", "ɵɵproperty", "title", "description", "owner", "ɵɵpipeBind1", "createdDate", "defaultActions", "isLoading", "KnowledgeBaseComponent_div_12_Template_app_page_footer_pageChange_2_listener", "_r4", "onPageChange", "filteredKnowledgeBase", "length", "currentPage", "itemsPerPage", "KnowledgeBaseComponent", "knowledgeBaseService", "paginationService", "router", "datePipe", "fb", "searchForm", "search", "labels", "allKnowledgeBase", "displayedKnowledgeBase", "error", "totalPages", "showInfoPopup", "iconName", "infoMessage", "destroy$", "knowledgeBaseOptions", "name", "value", "selectedData", "icon", "label", "tooltip", "showDeletePopup", "knowledgeBaseToDelete", "cardSkeletonPlaceholders", "Array", "constructor", "group", "ngOnInit", "get", "valueChanges", "pipe", "toLowerCase", "subscribe", "searchText", "filterKnowledgeBase", "fetchAllKnowledge", "response", "item", "formattedDate", "transform", "collectionName", "userCount", "next", "res", "updateDisplayedKnowledgeBase", "err", "paginationResult", "getPaginatedItems", "displayedItems", "filter", "know", "inTitle", "includes", "inDescription", "inTags", "isArray", "tags", "some", "tag", "onCreateKnowledgeBase", "navigate", "onCardClicked", "knowledgeBaseId", "event", "actionId", "confirmDeleteKnowledgeBase", "editKnowledgeBase", "deleteKnowledgeBase", "collectionObj", "find", "deleteByCollection", "console", "log", "warn", "onConfirmDelete", "closeDeletePopup", "duplicateKnowledgeBase", "onSelectionChange", "data", "page", "showCreateCard", "handleInfoPopupClose", "ngOnDestroy", "complete", "ɵɵdirectiveInject", "i1", "KnowledgeBaseService", "i2", "PaginationService", "i3", "Router", "i4", "i5", "FormBuilder", "selectors", "features", "ɵɵProvidersFeature", "decls", "vars", "consts", "template", "KnowledgeBaseComponent_Template", "rf", "ctx", "ɵɵelement", "KnowledgeBaseComponent_Template_ava_dropdown_selectionChange_7_listener", "KnowledgeBaseComponent_Template_ava_text_card_cardClick_9_listener", "ɵɵtemplate", "KnowledgeBaseComponent_div_10_Template", "KnowledgeBaseComponent_ng_container_11_Template", "KnowledgeBaseComponent_div_12_Template", "createKnowledgeBase", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "ɵNgNoValidate", "NgControlStatus", "NgControlStatusGroup", "FormGroupDirective", "FormControlName", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\pages\\libraries\\knowledge-base\\knowledge-base.component.ts", "C:\\console\\aava-ui-web\\projects\\shared\\pages\\libraries\\knowledge-base\\knowledge-base.component.html"], "sourcesContent": ["import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';\r\nimport { CommonModule, DatePipe } from '@angular/common';\r\nimport { Router } from '@angular/router';\r\nimport {\r\n  of,\r\n  map,\r\n  catchError,\r\n  Subject,\r\n  takeUntil,\r\n  debounceTime,\r\n  distinctUntilChanged,\r\n  startWith,\r\n} from 'rxjs';\r\nimport { PageFooterComponent } from '@shared/components/page-footer/page-footer.component';\r\nimport { PaginationService } from '@shared/services/pagination.service';\r\nimport { KnowledgeBaseService } from '@shared/services/knowledge-base.service';\r\nimport {\r\n  AvaTextboxComponent,\r\n  DropdownComponent,\r\n  DropdownOption,\r\n  IconComponent,\r\n  TextCardComponent,\r\n  PopupComponent,\r\n} from '@ava/play-comp-library';\r\nimport { LucideAngularModule } from 'lucide-angular';\r\nimport knowledgeBaseLabels from '../knowledge-base/constants/knowledge-base.json';\r\nimport { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';\r\nimport { ConsoleCardAction, ConsoleCardComponent } from \"@shared/components/console-card/console-card.component\";\r\nimport { TimeAgoPipe } from '@shared/pipes/time-ago.pipe';\r\n\r\n@Component({\r\n  selector: 'app-knowledge-base',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    PageFooterComponent,\r\n    TextCardComponent,\r\n    AvaTextboxComponent,\r\n    DropdownComponent,\r\n    LucideAngularModule,\r\n    IconComponent,\r\n    ReactiveFormsModule,\r\n    PopupComponent,\r\n    ConsoleCardComponent,\r\n    TimeAgoPipe,\r\n  ],\r\n  providers: [DatePipe],\r\n  templateUrl: './knowledge-base.component.html',\r\n  styleUrls: ['./knowledge-base.component.scss'],\r\n})\r\nexport class KnowledgeBaseComponent implements OnInit, OnDestroy {\r\n  searchForm!: FormGroup;\r\n  search: any;\r\n  public kbLabels = knowledgeBaseLabels.labels;\r\n  allKnowledgeBase: any[] = [];\r\n  filteredKnowledgeBase: any[] = [];\r\n  displayedKnowledgeBase: any[] = [];\r\n  isLoading: boolean = false;\r\n  error: string | null = null;\r\n  currentPage: number = 1;\r\n  itemsPerPage: number = 11;\r\n  totalPages: number = 1;\r\n  showInfoPopup: boolean = false;\r\n  iconName = '';\r\n  infoMessage = '';\r\n  private destroy$ = new Subject<void>();\r\n\r\n  knowledgeBaseOptions: DropdownOption[] = [\r\n    { name: 'All', value: 'all' },\r\n    { name: 'Type A', value: 'typeA' },\r\n    { name: 'Type B', value: 'typeB' },\r\n  ];\r\n  selectedData: any = null;\r\n\r\n  defaultActions: ConsoleCardAction[] = [\r\n    {\r\n      id: 'delete',\r\n      icon: 'trash',\r\n      label: 'Delete item',\r\n      tooltip: 'Delete',\r\n    },\r\n    {\r\n      id: 'edit',\r\n      icon: 'edit',\r\n      label: 'Edit item',\r\n      tooltip: 'Edit',\r\n    },\r\n  ];\r\n\r\n  showDeletePopup = false;\r\n  knowledgeBaseToDelete: any = null;\r\n  cardSkeletonPlaceholders = Array(11);\r\n\r\n  constructor(\r\n    private knowledgeBaseService: KnowledgeBaseService,\r\n    private paginationService: PaginationService,\r\n    private router: Router,\r\n    private datePipe: DatePipe,\r\n    private fb: FormBuilder,\r\n  ) {\r\n    this.searchForm = this.fb.group({\r\n      search: [''],\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.searchForm\r\n      .get('search')!\r\n      .valueChanges.pipe(\r\n        startWith(''),\r\n        debounceTime(300),\r\n        distinctUntilChanged(),\r\n        map((value) => value?.toLowerCase() ?? ''),\r\n      )\r\n      .subscribe((searchText) => {\r\n        this.filterKnowledgeBase(searchText);\r\n      });\r\n\r\n    this.fetchAllKnowledge();\r\n  }\r\n\r\n  fetchAllKnowledge() {\r\n    this.isLoading = true;\r\n    this.knowledgeBaseService\r\n      .fetchAllKnowledge()\r\n      .pipe(\r\n        map((response: any[]) => {\r\n          return response.map((item: any) => {\r\n            const formattedDate =\r\n              this.datePipe.transform(item.createdDate, 'MM/dd/yyyy') || '';\r\n            return {\r\n              ...item,\r\n              title: item.collectionName,\r\n              createdDate: formattedDate,\r\n              userCount: item.userCount || 0,\r\n            };\r\n          });\r\n        }),\r\n        catchError((error) => {\r\n          this.isLoading = false;\r\n          return of([]);\r\n        }),\r\n        takeUntil(this.destroy$),\r\n      )\r\n      .subscribe({\r\n        next: (res) => {\r\n          this.allKnowledgeBase = res;\r\n          this.filteredKnowledgeBase = [...this.allKnowledgeBase];\r\n          this.updateDisplayedKnowledgeBase();\r\n          this.isLoading = false;\r\n        },\r\n        error: (err) => {\r\n          this.isLoading = false;\r\n        },\r\n      });\r\n  }\r\n\r\n  updateDisplayedKnowledgeBase(): void {\r\n    this.itemsPerPage = this.currentPage === 1 ? 12 : 11;\r\n    const paginationResult = this.paginationService.getPaginatedItems(\r\n      this.filteredKnowledgeBase,\r\n      this.currentPage,\r\n      this.itemsPerPage,\r\n    );\r\n    this.displayedKnowledgeBase = paginationResult.displayedItems;\r\n    this.totalPages = paginationResult.totalPages;\r\n  }\r\n\r\n  filterKnowledgeBase(searchText: string): void {\r\n    this.filteredKnowledgeBase = this.allKnowledgeBase.filter((know) => {\r\n      const inTitle = know.title?.toLowerCase().includes(searchText);\r\n      const inDescription = know.description\r\n        ?.toLowerCase()\r\n        .includes(searchText);\r\n      const inTags =\r\n        Array.isArray(know.tags) &&\r\n        know.tags?.some((tag: any) =>\r\n          tag.label?.toLowerCase().includes(searchText),\r\n        );\r\n\r\n      return inTitle || inDescription || inTags;\r\n    });\r\n\r\n    this.updateDisplayedKnowledgeBase();\r\n  }\r\n\r\n  onCreateKnowledgeBase(): void {\r\n    this.router.navigate(['/libraries/knowledge-base/create']);\r\n  }\r\n\r\n  onCardClicked(knowledgeBaseId: string): void {\r\n    // Navigate to knowledge base details page\r\n    this.router.navigate([`/libraries/knowledge-base/edit/${knowledgeBaseId}`]);\r\n  }\r\n\r\n  onActionClick(\r\n    event: { actionId: string; action: ConsoleCardAction },\r\n    knowledgeBaseId: string,\r\n  ): void {\r\n    switch (event.actionId) {\r\n      case 'delete':\r\n        this.confirmDeleteKnowledgeBase(knowledgeBaseId);\r\n        break;\r\n      case 'edit':\r\n        this.editKnowledgeBase(knowledgeBaseId);\r\n        break;\r\n      default:\r\n        break;\r\n    }\r\n  }\r\n\r\n  editKnowledgeBase(knowledgeBaseId: string): void {\r\n    // Implement duplicate logic\r\n    this.router.navigate([`/libraries/knowledge-base/edit/${knowledgeBaseId}`]);\r\n  }\r\n\r\n  deleteKnowledgeBase(knowledgeBaseId: string): void {\r\n    const collectionObj = this.allKnowledgeBase.find(\r\n      (item) => item.id === knowledgeBaseId,\r\n    );\r\n    const collectionName = collectionObj?.title;\r\n\r\n    if (collectionName) {\r\n      this.knowledgeBaseService.deleteByCollection(collectionName).subscribe({\r\n        next: (res) => {\r\n          console.log('Knowledge base deleted:', res);\r\n          this.fetchAllKnowledge(); // Refresh the list\r\n        },\r\n        error: (err) => {\r\n          console.error('Failed to delete knowledge base:', err);\r\n        },\r\n      });\r\n    } else {\r\n      console.warn(`Knowledge base with ID ${knowledgeBaseId} not found.`);\r\n    }\r\n  }\r\n\r\n  confirmDeleteKnowledgeBase(knowledgeBaseId: string): void {\r\n    this.knowledgeBaseToDelete = this.allKnowledgeBase.find(\r\n      (item) => item.id === knowledgeBaseId,\r\n    );\r\n    this.showDeletePopup = true;\r\n  }\r\n\r\n  onConfirmDelete(): void {\r\n    const collectionName = this.knowledgeBaseToDelete?.title;\r\n    if (collectionName) {\r\n      this.knowledgeBaseService.deleteByCollection(collectionName).subscribe({\r\n        next: (res) => {\r\n          console.log('Knowledge base deleted:', res);\r\n          // Show success popup\r\n          this.iconName = 'check-circle';\r\n          this.infoMessage = 'Knowledge Base deleted successfully.';\r\n          this.showInfoPopup = true;\r\n          this.fetchAllKnowledge();\r\n          this.closeDeletePopup();\r\n        },\r\n        error: (err) => {\r\n          console.error('Failed to delete knowledge base:', err);\r\n          this.closeDeletePopup();\r\n        },\r\n      });\r\n    }\r\n  }\r\n\r\n  closeDeletePopup(): void {\r\n    this.showDeletePopup = false;\r\n    this.knowledgeBaseToDelete = null;\r\n  }\r\n\r\n  duplicateKnowledgeBase(knowledgeBaseId: string): void {\r\n    // Implement duplicate logic\r\n  }\r\n\r\n  onSelectionChange(data: any) {\r\n    this.selectedData = data;\r\n    // Implement filter logic if needed\r\n  }\r\n\r\n  onPageChange(page: number): void {\r\n    this.currentPage = page;\r\n    this.updateDisplayedKnowledgeBase();\r\n  }\r\n\r\n  get showCreateCard(): boolean {\r\n    return !this.isLoading && !this.error;\r\n  }\r\n\r\n  handleInfoPopupClose(): void {\r\n    this.showInfoPopup = false;\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.destroy$.next();\r\n    this.destroy$.complete();\r\n  }\r\n}\r\n", "<div id=\"knowledge-base-container\" class=\"container-fluid\">\r\n  <div id=\"search-filter-container\" class=\"row g-3\">\r\n    <div class=\"col-12 col-md-8 col-lg-9 col-xl-10 search-section\">\r\n      <form [formGroup]=\"searchForm\">\r\n        <ava-textbox\r\n          placeholder='Search \"Knowledge Base\"'\r\n          hoverEffect=\"glow\"\r\n          pressedEffect=\"solid\"\r\n          formControlName=\"search\"\r\n        >\r\n          <ava-icon\r\n            slot=\"icon-start\"\r\n            iconName=\"search\"\r\n            [iconSize]=\"16\"\r\n            iconColor=\"var(--color-brand-primary)\"\r\n          >\r\n          </ava-icon>\r\n        </ava-textbox>\r\n      </form>\r\n    </div>\r\n    <div class=\"col-12 col-md-4 col-lg-3 col-xl-2 action-buttons\">\r\n      <ava-dropdown\r\n        dropdownTitle=\"choose knowledge base\"\r\n        [options]=\"knowledgeBaseOptions\"\r\n        (selectionChange)=\"onSelectionChange($event)\"\r\n      >\r\n      </ava-dropdown>\r\n    </div>\r\n  </div>\r\n\r\n  <div id=\"prompts-card-container\" class=\"row g-3\">\r\n    <ava-text-card\r\n      class=\"col-12 col-sm-6 col-md-4 col-lg-3 col-xl-3 col-xxl-2 mt-5\"\r\n      [type]=\"'create'\"\r\n      [iconName]=\"'plus'\"\r\n      iconColor=\"#144692\"\r\n      [title]=\"kbLabels.createKnowledgeBase\"\r\n      (cardClick)=\"onCreateKnowledgeBase()\"\r\n      [isLoading]=\"isLoading\"\r\n    >\r\n    </ava-text-card>\r\n\r\n    <!-- No Results Message -->\r\n    <div\r\n      class=\"col-12 d-flex justify-content-center align-items-center py-5\"\r\n      *ngIf=\"!isLoading && filteredKnowledgeBase.length === 0\"\r\n    >\r\n      <div class=\"text-center\">\r\n        <h5 class=\"text-muted\">{{ kbLabels.noResults }}</h5>\r\n      </div>\r\n    </div>\r\n\r\n    <ng-container *ngFor=\"let knowledgeBase of isLoading && displayedKnowledgeBase.length === 0 ? cardSkeletonPlaceholders : displayedKnowledgeBase\">\r\n      <ava-console-card        \r\n        class=\"col-12 col-sm-6 col-md-4 col-lg-3 col-xl-3 col-xxl-2 mt-5\"\r\n        [title]=\"knowledgeBase?.title\"\r\n        [description]=\"knowledgeBase?.description\"\r\n         categoryIcon=\"book-open\"\r\n        categoryTitle=\"Models\"\r\n        categoryValue=\"42\"\r\n        [author]=\"knowledgeBase?.owner || 'AAVA'\"\r\n        [date]=\"knowledgeBase?.createdDate | timeAgo\"\r\n         [actions]=\"defaultActions\"\r\n        (actionClick)=\"onActionClick($event, knowledgeBase.id)\"\r\n        [skeleton]=\"isLoading\"\r\n      >\r\n      </ava-console-card>\r\n    </ng-container>\r\n  </div>\r\n\r\n  <!-- Pagination Footer -->\r\n  <div class=\"row\" *ngIf=\"filteredKnowledgeBase.length > 0\">\r\n    <div class=\"col-12 d-flex justify-content-center mt-4\">\r\n      <app-page-footer\r\n        [totalItems]=\"filteredKnowledgeBase.length + 1\"\r\n        [currentPage]=\"currentPage\"\r\n        [itemsPerPage]=\"itemsPerPage\"\r\n        (pageChange)=\"onPageChange($event)\"\r\n      ></app-page-footer>\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n"], "mappings": "AACA,SAASA,YAAY,EAAEC,QAAQ,QAAQ,iBAAiB;AAExD,SACEC,EAAE,EACFC,GAAG,EACHC,UAAU,EACVC,OAAO,EACPC,SAAS,EACTC,YAAY,EACZC,oBAAoB,EACpBC,SAAS,QACJ,MAAM;AACb,SAASC,mBAAmB,QAAQ,sDAAsD;AAG1F,SACEC,mBAAmB,EACnBC,iBAAiB,EAEjBC,aAAa,EACbC,iBAAiB,QAEZ,wBAAwB;AAC/B,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,OAAOC,mBAAmB,MAAM,iDAAiD;AACjF,SAAiCC,mBAAmB,QAAQ,gBAAgB;AAC5E,SAA4BC,oBAAoB,QAAQ,wDAAwD;AAChH,SAASC,WAAW,QAAQ,6BAA6B;;;;;;;;;ICoBjDC,EALJ,CAAAC,cAAA,cAGC,cAC0B,aACA;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAEnDF,EAFmD,CAAAG,YAAA,EAAK,EAChD,EACF;;;;IAFqBH,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,QAAA,CAAAC,SAAA,CAAwB;;;;;;IAInDR,EAAA,CAAAS,uBAAA,GAAiJ;IAC/IT,EAAA,CAAAC,cAAA,2BAYC;;IAFCD,EAAA,CAAAU,UAAA,yBAAAC,wFAAAC,MAAA;MAAA,MAAAC,gBAAA,GAAAb,EAAA,CAAAc,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAV,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAeZ,MAAA,CAAAa,aAAA,CAAAP,MAAA,EAAAC,gBAAA,CAAAO,EAAA,CAAuC;IAAA,EAAC;IAGzDpB,EAAA,CAAAG,YAAA,EAAmB;;;;;;IAXjBH,EAAA,CAAAI,SAAA,EAA8B;IAS9BJ,EATA,CAAAqB,UAAA,UAAAR,gBAAA,kBAAAA,gBAAA,CAAAS,KAAA,CAA8B,gBAAAT,gBAAA,kBAAAA,gBAAA,CAAAU,WAAA,CACY,YAAAV,gBAAA,kBAAAA,gBAAA,CAAAW,KAAA,YAID,SAAAxB,EAAA,CAAAyB,WAAA,OAAAZ,gBAAA,kBAAAA,gBAAA,CAAAa,WAAA,EACI,YAAApB,MAAA,CAAAqB,cAAA,CAClB,aAAArB,MAAA,CAAAsB,SAAA,CAEL;;;;;;IASxB5B,EAFJ,CAAAC,cAAA,cAA0D,cACD,0BAMpD;IADCD,EAAA,CAAAU,UAAA,wBAAAmB,6EAAAjB,MAAA;MAAAZ,EAAA,CAAAc,aAAA,CAAAgB,GAAA;MAAA,MAAAxB,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAcZ,MAAA,CAAAyB,YAAA,CAAAnB,MAAA,CAAoB;IAAA,EAAC;IAGzCZ,EAFK,CAAAG,YAAA,EAAkB,EACf,EACF;;;;IANAH,EAAA,CAAAI,SAAA,GAA+C;IAE/CJ,EAFA,CAAAqB,UAAA,eAAAf,MAAA,CAAA0B,qBAAA,CAAAC,MAAA,KAA+C,gBAAA3B,MAAA,CAAA4B,WAAA,CACpB,iBAAA5B,MAAA,CAAA6B,YAAA,CACE;;;AD1BrC,WAAaC,sBAAsB;EAA7B,MAAOA,sBAAsB;IA4CvBC,oBAAA;IACAC,iBAAA;IACAC,MAAA;IACAC,QAAA;IACAC,EAAA;IA/CVC,UAAU;IACVC,MAAM;IACCpC,QAAQ,GAAGX,mBAAmB,CAACgD,MAAM;IAC5CC,gBAAgB,GAAU,EAAE;IAC5Bb,qBAAqB,GAAU,EAAE;IACjCc,sBAAsB,GAAU,EAAE;IAClClB,SAAS,GAAY,KAAK;IAC1BmB,KAAK,GAAkB,IAAI;IAC3Bb,WAAW,GAAW,CAAC;IACvBC,YAAY,GAAW,EAAE;IACzBa,UAAU,GAAW,CAAC;IACtBC,aAAa,GAAY,KAAK;IAC9BC,QAAQ,GAAG,EAAE;IACbC,WAAW,GAAG,EAAE;IACRC,QAAQ,GAAG,IAAInE,OAAO,EAAQ;IAEtCoE,oBAAoB,GAAqB,CACvC;MAAEC,IAAI,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAK,CAAE,EAC7B;MAAED,IAAI,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAO,CAAE,EAClC;MAAED,IAAI,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAO,CAAE,CACnC;IACDC,YAAY,GAAQ,IAAI;IAExB7B,cAAc,GAAwB,CACpC;MACEP,EAAE,EAAE,QAAQ;MACZqC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,aAAa;MACpBC,OAAO,EAAE;KACV,EACD;MACEvC,EAAE,EAAE,MAAM;MACVqC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,WAAW;MAClBC,OAAO,EAAE;KACV,CACF;IAEDC,eAAe,GAAG,KAAK;IACvBC,qBAAqB,GAAQ,IAAI;IACjCC,wBAAwB,GAAGC,KAAK,CAAC,EAAE,CAAC;IAEpCC,YACU3B,oBAA0C,EAC1CC,iBAAoC,EACpCC,MAAc,EACdC,QAAkB,EAClBC,EAAe;MAJf,KAAAJ,oBAAoB,GAApBA,oBAAoB;MACpB,KAAAC,iBAAiB,GAAjBA,iBAAiB;MACjB,KAAAC,MAAM,GAANA,MAAM;MACN,KAAAC,QAAQ,GAARA,QAAQ;MACR,KAAAC,EAAE,GAAFA,EAAE;MAEV,IAAI,CAACC,UAAU,GAAG,IAAI,CAACD,EAAE,CAACwB,KAAK,CAAC;QAC9BtB,MAAM,EAAE,CAAC,EAAE;OACZ,CAAC;IACJ;IAEAuB,QAAQA,CAAA;MACN,IAAI,CAACxB,UAAU,CACZyB,GAAG,CAAC,QAAQ,CAAE,CACdC,YAAY,CAACC,IAAI,CAChBhF,SAAS,CAAC,EAAE,CAAC,EACbF,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBL,GAAG,CAAEwE,KAAK,IAAKA,KAAK,EAAEe,WAAW,EAAE,IAAI,EAAE,CAAC,CAC3C,CACAC,SAAS,CAAEC,UAAU,IAAI;QACxB,IAAI,CAACC,mBAAmB,CAACD,UAAU,CAAC;MACtC,CAAC,CAAC;MAEJ,IAAI,CAACE,iBAAiB,EAAE;IAC1B;IAEAA,iBAAiBA,CAAA;MACf,IAAI,CAAC9C,SAAS,GAAG,IAAI;MACrB,IAAI,CAACS,oBAAoB,CACtBqC,iBAAiB,EAAE,CACnBL,IAAI,CACHtF,GAAG,CAAE4F,QAAe,IAAI;QACtB,OAAOA,QAAQ,CAAC5F,GAAG,CAAE6F,IAAS,IAAI;UAChC,MAAMC,aAAa,GACjB,IAAI,CAACrC,QAAQ,CAACsC,SAAS,CAACF,IAAI,CAAClD,WAAW,EAAE,YAAY,CAAC,IAAI,EAAE;UAC/D,OAAO;YACL,GAAGkD,IAAI;YACPtD,KAAK,EAAEsD,IAAI,CAACG,cAAc;YAC1BrD,WAAW,EAAEmD,aAAa;YAC1BG,SAAS,EAAEJ,IAAI,CAACI,SAAS,IAAI;WAC9B;QACH,CAAC,CAAC;MACJ,CAAC,CAAC,EACFhG,UAAU,CAAE+D,KAAK,IAAI;QACnB,IAAI,CAACnB,SAAS,GAAG,KAAK;QACtB,OAAO9C,EAAE,CAAC,EAAE,CAAC;MACf,CAAC,CAAC,EACFI,SAAS,CAAC,IAAI,CAACkE,QAAQ,CAAC,CACzB,CACAmB,SAAS,CAAC;QACTU,IAAI,EAAGC,GAAG,IAAI;UACZ,IAAI,CAACrC,gBAAgB,GAAGqC,GAAG;UAC3B,IAAI,CAAClD,qBAAqB,GAAG,CAAC,GAAG,IAAI,CAACa,gBAAgB,CAAC;UACvD,IAAI,CAACsC,4BAA4B,EAAE;UACnC,IAAI,CAACvD,SAAS,GAAG,KAAK;QACxB,CAAC;QACDmB,KAAK,EAAGqC,GAAG,IAAI;UACb,IAAI,CAACxD,SAAS,GAAG,KAAK;QACxB;OACD,CAAC;IACN;IAEAuD,4BAA4BA,CAAA;MAC1B,IAAI,CAAChD,YAAY,GAAG,IAAI,CAACD,WAAW,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE;MACpD,MAAMmD,gBAAgB,GAAG,IAAI,CAAC/C,iBAAiB,CAACgD,iBAAiB,CAC/D,IAAI,CAACtD,qBAAqB,EAC1B,IAAI,CAACE,WAAW,EAChB,IAAI,CAACC,YAAY,CAClB;MACD,IAAI,CAACW,sBAAsB,GAAGuC,gBAAgB,CAACE,cAAc;MAC7D,IAAI,CAACvC,UAAU,GAAGqC,gBAAgB,CAACrC,UAAU;IAC/C;IAEAyB,mBAAmBA,CAACD,UAAkB;MACpC,IAAI,CAACxC,qBAAqB,GAAG,IAAI,CAACa,gBAAgB,CAAC2C,MAAM,CAAEC,IAAI,IAAI;QACjE,MAAMC,OAAO,GAAGD,IAAI,CAACnE,KAAK,EAAEgD,WAAW,EAAE,CAACqB,QAAQ,CAACnB,UAAU,CAAC;QAC9D,MAAMoB,aAAa,GAAGH,IAAI,CAAClE,WAAW,EAClC+C,WAAW,EAAE,CACdqB,QAAQ,CAACnB,UAAU,CAAC;QACvB,MAAMqB,MAAM,GACV9B,KAAK,CAAC+B,OAAO,CAACL,IAAI,CAACM,IAAI,CAAC,IACxBN,IAAI,CAACM,IAAI,EAAEC,IAAI,CAAEC,GAAQ,IACvBA,GAAG,CAACvC,KAAK,EAAEY,WAAW,EAAE,CAACqB,QAAQ,CAACnB,UAAU,CAAC,CAC9C;QAEH,OAAOkB,OAAO,IAAIE,aAAa,IAAIC,MAAM;MAC3C,CAAC,CAAC;MAEF,IAAI,CAACV,4BAA4B,EAAE;IACrC;IAEAe,qBAAqBA,CAAA;MACnB,IAAI,CAAC3D,MAAM,CAAC4D,QAAQ,CAAC,CAAC,kCAAkC,CAAC,CAAC;IAC5D;IAEAC,aAAaA,CAACC,eAAuB;MACnC;MACA,IAAI,CAAC9D,MAAM,CAAC4D,QAAQ,CAAC,CAAC,kCAAkCE,eAAe,EAAE,CAAC,CAAC;IAC7E;IAEAlF,aAAaA,CACXmF,KAAsD,EACtDD,eAAuB;MAEvB,QAAQC,KAAK,CAACC,QAAQ;QACpB,KAAK,QAAQ;UACX,IAAI,CAACC,0BAA0B,CAACH,eAAe,CAAC;UAChD;QACF,KAAK,MAAM;UACT,IAAI,CAACI,iBAAiB,CAACJ,eAAe,CAAC;UACvC;QACF;UACE;MACJ;IACF;IAEAI,iBAAiBA,CAACJ,eAAuB;MACvC;MACA,IAAI,CAAC9D,MAAM,CAAC4D,QAAQ,CAAC,CAAC,kCAAkCE,eAAe,EAAE,CAAC,CAAC;IAC7E;IAEAK,mBAAmBA,CAACL,eAAuB;MACzC,MAAMM,aAAa,GAAG,IAAI,CAAC9D,gBAAgB,CAAC+D,IAAI,CAC7ChC,IAAI,IAAKA,IAAI,CAACxD,EAAE,KAAKiF,eAAe,CACtC;MACD,MAAMtB,cAAc,GAAG4B,aAAa,EAAErF,KAAK;MAE3C,IAAIyD,cAAc,EAAE;QAClB,IAAI,CAAC1C,oBAAoB,CAACwE,kBAAkB,CAAC9B,cAAc,CAAC,CAACR,SAAS,CAAC;UACrEU,IAAI,EAAGC,GAAG,IAAI;YACZ4B,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE7B,GAAG,CAAC;YAC3C,IAAI,CAACR,iBAAiB,EAAE,CAAC,CAAC;UAC5B,CAAC;UACD3B,KAAK,EAAGqC,GAAG,IAAI;YACb0B,OAAO,CAAC/D,KAAK,CAAC,kCAAkC,EAAEqC,GAAG,CAAC;UACxD;SACD,CAAC;MACJ,CAAC,MAAM;QACL0B,OAAO,CAACE,IAAI,CAAC,0BAA0BX,eAAe,aAAa,CAAC;MACtE;IACF;IAEAG,0BAA0BA,CAACH,eAAuB;MAChD,IAAI,CAACxC,qBAAqB,GAAG,IAAI,CAAChB,gBAAgB,CAAC+D,IAAI,CACpDhC,IAAI,IAAKA,IAAI,CAACxD,EAAE,KAAKiF,eAAe,CACtC;MACD,IAAI,CAACzC,eAAe,GAAG,IAAI;IAC7B;IAEAqD,eAAeA,CAAA;MACb,MAAMlC,cAAc,GAAG,IAAI,CAAClB,qBAAqB,EAAEvC,KAAK;MACxD,IAAIyD,cAAc,EAAE;QAClB,IAAI,CAAC1C,oBAAoB,CAACwE,kBAAkB,CAAC9B,cAAc,CAAC,CAACR,SAAS,CAAC;UACrEU,IAAI,EAAGC,GAAG,IAAI;YACZ4B,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE7B,GAAG,CAAC;YAC3C;YACA,IAAI,CAAChC,QAAQ,GAAG,cAAc;YAC9B,IAAI,CAACC,WAAW,GAAG,sCAAsC;YACzD,IAAI,CAACF,aAAa,GAAG,IAAI;YACzB,IAAI,CAACyB,iBAAiB,EAAE;YACxB,IAAI,CAACwC,gBAAgB,EAAE;UACzB,CAAC;UACDnE,KAAK,EAAGqC,GAAG,IAAI;YACb0B,OAAO,CAAC/D,KAAK,CAAC,kCAAkC,EAAEqC,GAAG,CAAC;YACtD,IAAI,CAAC8B,gBAAgB,EAAE;UACzB;SACD,CAAC;MACJ;IACF;IAEAA,gBAAgBA,CAAA;MACd,IAAI,CAACtD,eAAe,GAAG,KAAK;MAC5B,IAAI,CAACC,qBAAqB,GAAG,IAAI;IACnC;IAEAsD,sBAAsBA,CAACd,eAAuB;MAC5C;IAAA;IAGFe,iBAAiBA,CAACC,IAAS;MACzB,IAAI,CAAC7D,YAAY,GAAG6D,IAAI;MACxB;IACF;IAEAtF,YAAYA,CAACuF,IAAY;MACvB,IAAI,CAACpF,WAAW,GAAGoF,IAAI;MACvB,IAAI,CAACnC,4BAA4B,EAAE;IACrC;IAEA,IAAIoC,cAAcA,CAAA;MAChB,OAAO,CAAC,IAAI,CAAC3F,SAAS,IAAI,CAAC,IAAI,CAACmB,KAAK;IACvC;IAEAyE,oBAAoBA,CAAA;MAClB,IAAI,CAACvE,aAAa,GAAG,KAAK;IAC5B;IAEAwE,WAAWA,CAAA;MACT,IAAI,CAACrE,QAAQ,CAAC6B,IAAI,EAAE;MACpB,IAAI,CAAC7B,QAAQ,CAACsE,QAAQ,EAAE;IAC1B;;uCArPWtF,sBAAsB,EAAApC,EAAA,CAAA2H,iBAAA,CAAAC,EAAA,CAAAC,oBAAA,GAAA7H,EAAA,CAAA2H,iBAAA,CAAAG,EAAA,CAAAC,iBAAA,GAAA/H,EAAA,CAAA2H,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAAjI,EAAA,CAAA2H,iBAAA,CAAAO,EAAA,CAAArJ,QAAA,GAAAmB,EAAA,CAAA2H,iBAAA,CAAAQ,EAAA,CAAAC,WAAA;IAAA;;YAAtBhG,sBAAsB;MAAAiG,SAAA;MAAAC,QAAA,GAAAtI,EAAA,CAAAuI,kBAAA,CAJtB,CAAC1J,QAAQ,CAAC;MAAA2J,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC1Cf7I,EAJR,CAAAC,cAAA,aAA2D,aACP,aACe,cAC9B,qBAM5B;UACCD,EAAA,CAAA+I,SAAA,kBAMW;UAGjB/I,EAFI,CAAAG,YAAA,EAAc,EACT,EACH;UAEJH,EADF,CAAAC,cAAA,aAA8D,sBAK3D;UADCD,EAAA,CAAAU,UAAA,6BAAAsI,wEAAApI,MAAA;YAAA,OAAmBkI,GAAA,CAAA1B,iBAAA,CAAAxG,MAAA,CAAyB;UAAA,EAAC;UAInDZ,EAFI,CAAAG,YAAA,EAAe,EACX,EACF;UAGJH,EADF,CAAAC,cAAA,aAAiD,uBAS9C;UAFCD,EAAA,CAAAU,UAAA,uBAAAuI,mEAAA;YAAA,OAAaH,GAAA,CAAA5C,qBAAA,EAAuB;UAAA,EAAC;UAGvClG,EAAA,CAAAG,YAAA,EAAgB;UAYhBH,EATA,CAAAkJ,UAAA,KAAAC,sCAAA,kBAGC,KAAAC,+CAAA,2BAMgJ;UAgBnJpJ,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAkJ,UAAA,KAAAG,sCAAA,kBAA0D;UAU5DrJ,EAAA,CAAAG,YAAA,EAAM;;;UA9EMH,EAAA,CAAAI,SAAA,GAAwB;UAAxBJ,EAAA,CAAAqB,UAAA,cAAAyH,GAAA,CAAApG,UAAA,CAAwB;UAUxB1C,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAAqB,UAAA,gBAAe;UAUnBrB,EAAA,CAAAI,SAAA,GAAgC;UAAhCJ,EAAA,CAAAqB,UAAA,YAAAyH,GAAA,CAAAzF,oBAAA,CAAgC;UAUlCrD,EAAA,CAAAI,SAAA,GAAiB;UAKjBJ,EALA,CAAAqB,UAAA,kBAAiB,oBACE,UAAAyH,GAAA,CAAAvI,QAAA,CAAA+I,mBAAA,CAEmB,cAAAR,GAAA,CAAAlH,SAAA,CAEf;UAOtB5B,EAAA,CAAAI,SAAA,EAAsD;UAAtDJ,EAAA,CAAAqB,UAAA,UAAAyH,GAAA,CAAAlH,SAAA,IAAAkH,GAAA,CAAA9G,qBAAA,CAAAC,MAAA,OAAsD;UAOjBjC,EAAA,CAAAI,SAAA,EAAuG;UAAvGJ,EAAA,CAAAqB,UAAA,YAAAyH,GAAA,CAAAlH,SAAA,IAAAkH,GAAA,CAAAhG,sBAAA,CAAAb,MAAA,SAAA6G,GAAA,CAAAhF,wBAAA,GAAAgF,GAAA,CAAAhG,sBAAA,CAAuG;UAmB/H9C,EAAA,CAAAI,SAAA,EAAsC;UAAtCJ,EAAA,CAAAqB,UAAA,SAAAyH,GAAA,CAAA9G,qBAAA,CAAAC,MAAA,KAAsC;;;qBDrCtDrD,YAAY,EAAAsJ,EAAA,CAAAqB,OAAA,EAAArB,EAAA,CAAAsB,IAAA,EACZlK,mBAAmB,EACnBI,iBAAiB,EACjBH,mBAAmB,EACnBC,iBAAiB,EACjBG,mBAAmB,EACnBF,aAAa,EACbI,mBAAmB,EAAAsI,EAAA,CAAAsB,aAAA,EAAAtB,EAAA,CAAAuB,eAAA,EAAAvB,EAAA,CAAAwB,oBAAA,EAAAxB,EAAA,CAAAyB,kBAAA,EAAAzB,EAAA,CAAA0B,eAAA,EAEnB/J,oBAAoB,EACpBC,WAAW;MAAA+J,MAAA;IAAA;;SAMF1H,sBAAsB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}