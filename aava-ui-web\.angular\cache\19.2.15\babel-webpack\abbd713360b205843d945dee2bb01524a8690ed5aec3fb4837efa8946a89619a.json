{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as strings from '../../../base/common/strings.js';\nclass PendingChanges {\n  constructor() {\n    this._hasPending = false;\n    this._inserts = [];\n    this._changes = [];\n    this._removes = [];\n  }\n  insert(x) {\n    this._hasPending = true;\n    this._inserts.push(x);\n  }\n  change(x) {\n    this._hasPending = true;\n    this._changes.push(x);\n  }\n  remove(x) {\n    this._hasPending = true;\n    this._removes.push(x);\n  }\n  mustCommit() {\n    return this._hasPending;\n  }\n  commit(linesLayout) {\n    if (!this._hasPending) {\n      return;\n    }\n    const inserts = this._inserts;\n    const changes = this._changes;\n    const removes = this._removes;\n    this._hasPending = false;\n    this._inserts = [];\n    this._changes = [];\n    this._removes = [];\n    linesLayout._commitPendingChanges(inserts, changes, removes);\n  }\n}\nexport class EditorWhitespace {\n  constructor(id, afterLineNumber, ordinal, height, minWidth) {\n    this.id = id;\n    this.afterLineNumber = afterLineNumber;\n    this.ordinal = ordinal;\n    this.height = height;\n    this.minWidth = minWidth;\n    this.prefixSum = 0;\n  }\n}\n/**\n * Layouting of objects that take vertical space (by having a height) and push down other objects.\n *\n * These objects are basically either text (lines) or spaces between those lines (whitespaces).\n * This provides commodity operations for working with lines that contain whitespace that pushes lines lower (vertically).\n */\nexport let LinesLayout = /*#__PURE__*/(() => {\n  class LinesLayout {\n    static {\n      this.INSTANCE_COUNT = 0;\n    }\n    constructor(lineCount, lineHeight, paddingTop, paddingBottom) {\n      this._instanceId = strings.singleLetterHash(++LinesLayout.INSTANCE_COUNT);\n      this._pendingChanges = new PendingChanges();\n      this._lastWhitespaceId = 0;\n      this._arr = [];\n      this._prefixSumValidIndex = -1;\n      this._minWidth = -1; /* marker for not being computed */\n      this._lineCount = lineCount;\n      this._lineHeight = lineHeight;\n      this._paddingTop = paddingTop;\n      this._paddingBottom = paddingBottom;\n    }\n    /**\n     * Find the insertion index for a new value inside a sorted array of values.\n     * If the value is already present in the sorted array, the insertion index will be after the already existing value.\n     */\n    static findInsertionIndex(arr, afterLineNumber, ordinal) {\n      let low = 0;\n      let high = arr.length;\n      while (low < high) {\n        const mid = low + high >>> 1;\n        if (afterLineNumber === arr[mid].afterLineNumber) {\n          if (ordinal < arr[mid].ordinal) {\n            high = mid;\n          } else {\n            low = mid + 1;\n          }\n        } else if (afterLineNumber < arr[mid].afterLineNumber) {\n          high = mid;\n        } else {\n          low = mid + 1;\n        }\n      }\n      return low;\n    }\n    /**\n     * Change the height of a line in pixels.\n     */\n    setLineHeight(lineHeight) {\n      this._checkPendingChanges();\n      this._lineHeight = lineHeight;\n    }\n    /**\n     * Changes the padding used to calculate vertical offsets.\n     */\n    setPadding(paddingTop, paddingBottom) {\n      this._paddingTop = paddingTop;\n      this._paddingBottom = paddingBottom;\n    }\n    /**\n     * Set the number of lines.\n     *\n     * @param lineCount New number of lines.\n     */\n    onFlushed(lineCount) {\n      this._checkPendingChanges();\n      this._lineCount = lineCount;\n    }\n    changeWhitespace(callback) {\n      let hadAChange = false;\n      try {\n        const accessor = {\n          insertWhitespace: (afterLineNumber, ordinal, heightInPx, minWidth) => {\n            hadAChange = true;\n            afterLineNumber = afterLineNumber | 0;\n            ordinal = ordinal | 0;\n            heightInPx = heightInPx | 0;\n            minWidth = minWidth | 0;\n            const id = this._instanceId + ++this._lastWhitespaceId;\n            this._pendingChanges.insert(new EditorWhitespace(id, afterLineNumber, ordinal, heightInPx, minWidth));\n            return id;\n          },\n          changeOneWhitespace: (id, newAfterLineNumber, newHeight) => {\n            hadAChange = true;\n            newAfterLineNumber = newAfterLineNumber | 0;\n            newHeight = newHeight | 0;\n            this._pendingChanges.change({\n              id,\n              newAfterLineNumber,\n              newHeight\n            });\n          },\n          removeWhitespace: id => {\n            hadAChange = true;\n            this._pendingChanges.remove({\n              id\n            });\n          }\n        };\n        callback(accessor);\n      } finally {\n        this._pendingChanges.commit(this);\n      }\n      return hadAChange;\n    }\n    _commitPendingChanges(inserts, changes, removes) {\n      if (inserts.length > 0 || removes.length > 0) {\n        this._minWidth = -1; /* marker for not being computed */\n      }\n      if (inserts.length + changes.length + removes.length <= 1) {\n        // when only one thing happened, handle it \"delicately\"\n        for (const insert of inserts) {\n          this._insertWhitespace(insert);\n        }\n        for (const change of changes) {\n          this._changeOneWhitespace(change.id, change.newAfterLineNumber, change.newHeight);\n        }\n        for (const remove of removes) {\n          const index = this._findWhitespaceIndex(remove.id);\n          if (index === -1) {\n            continue;\n          }\n          this._removeWhitespace(index);\n        }\n        return;\n      }\n      // simply rebuild the entire datastructure\n      const toRemove = new Set();\n      for (const remove of removes) {\n        toRemove.add(remove.id);\n      }\n      const toChange = new Map();\n      for (const change of changes) {\n        toChange.set(change.id, change);\n      }\n      const applyRemoveAndChange = whitespaces => {\n        const result = [];\n        for (const whitespace of whitespaces) {\n          if (toRemove.has(whitespace.id)) {\n            continue;\n          }\n          if (toChange.has(whitespace.id)) {\n            const change = toChange.get(whitespace.id);\n            whitespace.afterLineNumber = change.newAfterLineNumber;\n            whitespace.height = change.newHeight;\n          }\n          result.push(whitespace);\n        }\n        return result;\n      };\n      const result = applyRemoveAndChange(this._arr).concat(applyRemoveAndChange(inserts));\n      result.sort((a, b) => {\n        if (a.afterLineNumber === b.afterLineNumber) {\n          return a.ordinal - b.ordinal;\n        }\n        return a.afterLineNumber - b.afterLineNumber;\n      });\n      this._arr = result;\n      this._prefixSumValidIndex = -1;\n    }\n    _checkPendingChanges() {\n      if (this._pendingChanges.mustCommit()) {\n        this._pendingChanges.commit(this);\n      }\n    }\n    _insertWhitespace(whitespace) {\n      const insertIndex = LinesLayout.findInsertionIndex(this._arr, whitespace.afterLineNumber, whitespace.ordinal);\n      this._arr.splice(insertIndex, 0, whitespace);\n      this._prefixSumValidIndex = Math.min(this._prefixSumValidIndex, insertIndex - 1);\n    }\n    _findWhitespaceIndex(id) {\n      const arr = this._arr;\n      for (let i = 0, len = arr.length; i < len; i++) {\n        if (arr[i].id === id) {\n          return i;\n        }\n      }\n      return -1;\n    }\n    _changeOneWhitespace(id, newAfterLineNumber, newHeight) {\n      const index = this._findWhitespaceIndex(id);\n      if (index === -1) {\n        return;\n      }\n      if (this._arr[index].height !== newHeight) {\n        this._arr[index].height = newHeight;\n        this._prefixSumValidIndex = Math.min(this._prefixSumValidIndex, index - 1);\n      }\n      if (this._arr[index].afterLineNumber !== newAfterLineNumber) {\n        // `afterLineNumber` changed for this whitespace\n        // Record old whitespace\n        const whitespace = this._arr[index];\n        // Since changing `afterLineNumber` can trigger a reordering, we're gonna remove this whitespace\n        this._removeWhitespace(index);\n        whitespace.afterLineNumber = newAfterLineNumber;\n        // And add it again\n        this._insertWhitespace(whitespace);\n      }\n    }\n    _removeWhitespace(removeIndex) {\n      this._arr.splice(removeIndex, 1);\n      this._prefixSumValidIndex = Math.min(this._prefixSumValidIndex, removeIndex - 1);\n    }\n    /**\n     * Notify the layouter that lines have been deleted (a continuous zone of lines).\n     *\n     * @param fromLineNumber The line number at which the deletion started, inclusive\n     * @param toLineNumber The line number at which the deletion ended, inclusive\n     */\n    onLinesDeleted(fromLineNumber, toLineNumber) {\n      this._checkPendingChanges();\n      fromLineNumber = fromLineNumber | 0;\n      toLineNumber = toLineNumber | 0;\n      this._lineCount -= toLineNumber - fromLineNumber + 1;\n      for (let i = 0, len = this._arr.length; i < len; i++) {\n        const afterLineNumber = this._arr[i].afterLineNumber;\n        if (fromLineNumber <= afterLineNumber && afterLineNumber <= toLineNumber) {\n          // The line this whitespace was after has been deleted\n          //  => move whitespace to before first deleted line\n          this._arr[i].afterLineNumber = fromLineNumber - 1;\n        } else if (afterLineNumber > toLineNumber) {\n          // The line this whitespace was after has been moved up\n          //  => move whitespace up\n          this._arr[i].afterLineNumber -= toLineNumber - fromLineNumber + 1;\n        }\n      }\n    }\n    /**\n     * Notify the layouter that lines have been inserted (a continuous zone of lines).\n     *\n     * @param fromLineNumber The line number at which the insertion started, inclusive\n     * @param toLineNumber The line number at which the insertion ended, inclusive.\n     */\n    onLinesInserted(fromLineNumber, toLineNumber) {\n      this._checkPendingChanges();\n      fromLineNumber = fromLineNumber | 0;\n      toLineNumber = toLineNumber | 0;\n      this._lineCount += toLineNumber - fromLineNumber + 1;\n      for (let i = 0, len = this._arr.length; i < len; i++) {\n        const afterLineNumber = this._arr[i].afterLineNumber;\n        if (fromLineNumber <= afterLineNumber) {\n          this._arr[i].afterLineNumber += toLineNumber - fromLineNumber + 1;\n        }\n      }\n    }\n    /**\n     * Get the sum of all the whitespaces.\n     */\n    getWhitespacesTotalHeight() {\n      this._checkPendingChanges();\n      if (this._arr.length === 0) {\n        return 0;\n      }\n      return this.getWhitespacesAccumulatedHeight(this._arr.length - 1);\n    }\n    /**\n     * Return the sum of the heights of the whitespaces at [0..index].\n     * This includes the whitespace at `index`.\n     *\n     * @param index The index of the whitespace.\n     * @return The sum of the heights of all whitespaces before the one at `index`, including the one at `index`.\n     */\n    getWhitespacesAccumulatedHeight(index) {\n      this._checkPendingChanges();\n      index = index | 0;\n      let startIndex = Math.max(0, this._prefixSumValidIndex + 1);\n      if (startIndex === 0) {\n        this._arr[0].prefixSum = this._arr[0].height;\n        startIndex++;\n      }\n      for (let i = startIndex; i <= index; i++) {\n        this._arr[i].prefixSum = this._arr[i - 1].prefixSum + this._arr[i].height;\n      }\n      this._prefixSumValidIndex = Math.max(this._prefixSumValidIndex, index);\n      return this._arr[index].prefixSum;\n    }\n    /**\n     * Get the sum of heights for all objects.\n     *\n     * @return The sum of heights for all objects.\n     */\n    getLinesTotalHeight() {\n      this._checkPendingChanges();\n      const linesHeight = this._lineHeight * this._lineCount;\n      const whitespacesHeight = this.getWhitespacesTotalHeight();\n      return linesHeight + whitespacesHeight + this._paddingTop + this._paddingBottom;\n    }\n    /**\n     * Returns the accumulated height of whitespaces before the given line number.\n     *\n     * @param lineNumber The line number\n     */\n    getWhitespaceAccumulatedHeightBeforeLineNumber(lineNumber) {\n      this._checkPendingChanges();\n      lineNumber = lineNumber | 0;\n      const lastWhitespaceBeforeLineNumber = this._findLastWhitespaceBeforeLineNumber(lineNumber);\n      if (lastWhitespaceBeforeLineNumber === -1) {\n        return 0;\n      }\n      return this.getWhitespacesAccumulatedHeight(lastWhitespaceBeforeLineNumber);\n    }\n    _findLastWhitespaceBeforeLineNumber(lineNumber) {\n      lineNumber = lineNumber | 0;\n      // Find the whitespace before line number\n      const arr = this._arr;\n      let low = 0;\n      let high = arr.length - 1;\n      while (low <= high) {\n        const delta = high - low | 0;\n        const halfDelta = delta / 2 | 0;\n        const mid = low + halfDelta | 0;\n        if (arr[mid].afterLineNumber < lineNumber) {\n          if (mid + 1 >= arr.length || arr[mid + 1].afterLineNumber >= lineNumber) {\n            return mid;\n          } else {\n            low = mid + 1 | 0;\n          }\n        } else {\n          high = mid - 1 | 0;\n        }\n      }\n      return -1;\n    }\n    _findFirstWhitespaceAfterLineNumber(lineNumber) {\n      lineNumber = lineNumber | 0;\n      const lastWhitespaceBeforeLineNumber = this._findLastWhitespaceBeforeLineNumber(lineNumber);\n      const firstWhitespaceAfterLineNumber = lastWhitespaceBeforeLineNumber + 1;\n      if (firstWhitespaceAfterLineNumber < this._arr.length) {\n        return firstWhitespaceAfterLineNumber;\n      }\n      return -1;\n    }\n    /**\n     * Find the index of the first whitespace which has `afterLineNumber` >= `lineNumber`.\n     * @return The index of the first whitespace with `afterLineNumber` >= `lineNumber` or -1 if no whitespace is found.\n     */\n    getFirstWhitespaceIndexAfterLineNumber(lineNumber) {\n      this._checkPendingChanges();\n      lineNumber = lineNumber | 0;\n      return this._findFirstWhitespaceAfterLineNumber(lineNumber);\n    }\n    /**\n     * Get the vertical offset (the sum of heights for all objects above) a certain line number.\n     *\n     * @param lineNumber The line number\n     * @return The sum of heights for all objects above `lineNumber`.\n     */\n    getVerticalOffsetForLineNumber(lineNumber, includeViewZones = false) {\n      this._checkPendingChanges();\n      lineNumber = lineNumber | 0;\n      let previousLinesHeight;\n      if (lineNumber > 1) {\n        previousLinesHeight = this._lineHeight * (lineNumber - 1);\n      } else {\n        previousLinesHeight = 0;\n      }\n      const previousWhitespacesHeight = this.getWhitespaceAccumulatedHeightBeforeLineNumber(lineNumber - (includeViewZones ? 1 : 0));\n      return previousLinesHeight + previousWhitespacesHeight + this._paddingTop;\n    }\n    /**\n     * Get the vertical offset (the sum of heights for all objects above) a certain line number.\n     *\n     * @param lineNumber The line number\n     * @return The sum of heights for all objects above `lineNumber`.\n     */\n    getVerticalOffsetAfterLineNumber(lineNumber, includeViewZones = false) {\n      this._checkPendingChanges();\n      lineNumber = lineNumber | 0;\n      const previousLinesHeight = this._lineHeight * lineNumber;\n      const previousWhitespacesHeight = this.getWhitespaceAccumulatedHeightBeforeLineNumber(lineNumber + (includeViewZones ? 1 : 0));\n      return previousLinesHeight + previousWhitespacesHeight + this._paddingTop;\n    }\n    /**\n     * The maximum min width for all whitespaces.\n     */\n    getWhitespaceMinWidth() {\n      this._checkPendingChanges();\n      if (this._minWidth === -1) {\n        let minWidth = 0;\n        for (let i = 0, len = this._arr.length; i < len; i++) {\n          minWidth = Math.max(minWidth, this._arr[i].minWidth);\n        }\n        this._minWidth = minWidth;\n      }\n      return this._minWidth;\n    }\n    /**\n     * Check if `verticalOffset` is below all lines.\n     */\n    isAfterLines(verticalOffset) {\n      this._checkPendingChanges();\n      const totalHeight = this.getLinesTotalHeight();\n      return verticalOffset > totalHeight;\n    }\n    isInTopPadding(verticalOffset) {\n      if (this._paddingTop === 0) {\n        return false;\n      }\n      this._checkPendingChanges();\n      return verticalOffset < this._paddingTop;\n    }\n    isInBottomPadding(verticalOffset) {\n      if (this._paddingBottom === 0) {\n        return false;\n      }\n      this._checkPendingChanges();\n      const totalHeight = this.getLinesTotalHeight();\n      return verticalOffset >= totalHeight - this._paddingBottom;\n    }\n    /**\n     * Find the first line number that is at or after vertical offset `verticalOffset`.\n     * i.e. if getVerticalOffsetForLine(line) is x and getVerticalOffsetForLine(line + 1) is y, then\n     * getLineNumberAtOrAfterVerticalOffset(i) = line, x <= i < y.\n     *\n     * @param verticalOffset The vertical offset to search at.\n     * @return The line number at or after vertical offset `verticalOffset`.\n     */\n    getLineNumberAtOrAfterVerticalOffset(verticalOffset) {\n      this._checkPendingChanges();\n      verticalOffset = verticalOffset | 0;\n      if (verticalOffset < 0) {\n        return 1;\n      }\n      const linesCount = this._lineCount | 0;\n      const lineHeight = this._lineHeight;\n      let minLineNumber = 1;\n      let maxLineNumber = linesCount;\n      while (minLineNumber < maxLineNumber) {\n        const midLineNumber = (minLineNumber + maxLineNumber) / 2 | 0;\n        const midLineNumberVerticalOffset = this.getVerticalOffsetForLineNumber(midLineNumber) | 0;\n        if (verticalOffset >= midLineNumberVerticalOffset + lineHeight) {\n          // vertical offset is after mid line number\n          minLineNumber = midLineNumber + 1;\n        } else if (verticalOffset >= midLineNumberVerticalOffset) {\n          // Hit\n          return midLineNumber;\n        } else {\n          // vertical offset is before mid line number, but mid line number could still be what we're searching for\n          maxLineNumber = midLineNumber;\n        }\n      }\n      if (minLineNumber > linesCount) {\n        return linesCount;\n      }\n      return minLineNumber;\n    }\n    /**\n     * Get all the lines and their relative vertical offsets that are positioned between `verticalOffset1` and `verticalOffset2`.\n     *\n     * @param verticalOffset1 The beginning of the viewport.\n     * @param verticalOffset2 The end of the viewport.\n     * @return A structure describing the lines positioned between `verticalOffset1` and `verticalOffset2`.\n     */\n    getLinesViewportData(verticalOffset1, verticalOffset2) {\n      this._checkPendingChanges();\n      verticalOffset1 = verticalOffset1 | 0;\n      verticalOffset2 = verticalOffset2 | 0;\n      const lineHeight = this._lineHeight;\n      // Find first line number\n      // We don't live in a perfect world, so the line number might start before or after verticalOffset1\n      const startLineNumber = this.getLineNumberAtOrAfterVerticalOffset(verticalOffset1) | 0;\n      const startLineNumberVerticalOffset = this.getVerticalOffsetForLineNumber(startLineNumber) | 0;\n      let endLineNumber = this._lineCount | 0;\n      // Also keep track of what whitespace we've got\n      let whitespaceIndex = this.getFirstWhitespaceIndexAfterLineNumber(startLineNumber) | 0;\n      const whitespaceCount = this.getWhitespacesCount() | 0;\n      let currentWhitespaceHeight;\n      let currentWhitespaceAfterLineNumber;\n      if (whitespaceIndex === -1) {\n        whitespaceIndex = whitespaceCount;\n        currentWhitespaceAfterLineNumber = endLineNumber + 1;\n        currentWhitespaceHeight = 0;\n      } else {\n        currentWhitespaceAfterLineNumber = this.getAfterLineNumberForWhitespaceIndex(whitespaceIndex) | 0;\n        currentWhitespaceHeight = this.getHeightForWhitespaceIndex(whitespaceIndex) | 0;\n      }\n      let currentVerticalOffset = startLineNumberVerticalOffset;\n      let currentLineRelativeOffset = currentVerticalOffset;\n      // IE (all versions) cannot handle units above about 1,533,908 px, so every 500k pixels bring numbers down\n      const STEP_SIZE = 500000;\n      let bigNumbersDelta = 0;\n      if (startLineNumberVerticalOffset >= STEP_SIZE) {\n        // Compute a delta that guarantees that lines are positioned at `lineHeight` increments\n        bigNumbersDelta = Math.floor(startLineNumberVerticalOffset / STEP_SIZE) * STEP_SIZE;\n        bigNumbersDelta = Math.floor(bigNumbersDelta / lineHeight) * lineHeight;\n        currentLineRelativeOffset -= bigNumbersDelta;\n      }\n      const linesOffsets = [];\n      const verticalCenter = verticalOffset1 + (verticalOffset2 - verticalOffset1) / 2;\n      let centeredLineNumber = -1;\n      // Figure out how far the lines go\n      for (let lineNumber = startLineNumber; lineNumber <= endLineNumber; lineNumber++) {\n        if (centeredLineNumber === -1) {\n          const currentLineTop = currentVerticalOffset;\n          const currentLineBottom = currentVerticalOffset + lineHeight;\n          if (currentLineTop <= verticalCenter && verticalCenter < currentLineBottom || currentLineTop > verticalCenter) {\n            centeredLineNumber = lineNumber;\n          }\n        }\n        // Count current line height in the vertical offsets\n        currentVerticalOffset += lineHeight;\n        linesOffsets[lineNumber - startLineNumber] = currentLineRelativeOffset;\n        // Next line starts immediately after this one\n        currentLineRelativeOffset += lineHeight;\n        while (currentWhitespaceAfterLineNumber === lineNumber) {\n          // Push down next line with the height of the current whitespace\n          currentLineRelativeOffset += currentWhitespaceHeight;\n          // Count current whitespace in the vertical offsets\n          currentVerticalOffset += currentWhitespaceHeight;\n          whitespaceIndex++;\n          if (whitespaceIndex >= whitespaceCount) {\n            currentWhitespaceAfterLineNumber = endLineNumber + 1;\n          } else {\n            currentWhitespaceAfterLineNumber = this.getAfterLineNumberForWhitespaceIndex(whitespaceIndex) | 0;\n            currentWhitespaceHeight = this.getHeightForWhitespaceIndex(whitespaceIndex) | 0;\n          }\n        }\n        if (currentVerticalOffset >= verticalOffset2) {\n          // We have covered the entire viewport area, time to stop\n          endLineNumber = lineNumber;\n          break;\n        }\n      }\n      if (centeredLineNumber === -1) {\n        centeredLineNumber = endLineNumber;\n      }\n      const endLineNumberVerticalOffset = this.getVerticalOffsetForLineNumber(endLineNumber) | 0;\n      let completelyVisibleStartLineNumber = startLineNumber;\n      let completelyVisibleEndLineNumber = endLineNumber;\n      if (completelyVisibleStartLineNumber < completelyVisibleEndLineNumber) {\n        if (startLineNumberVerticalOffset < verticalOffset1) {\n          completelyVisibleStartLineNumber++;\n        }\n      }\n      if (completelyVisibleStartLineNumber < completelyVisibleEndLineNumber) {\n        if (endLineNumberVerticalOffset + lineHeight > verticalOffset2) {\n          completelyVisibleEndLineNumber--;\n        }\n      }\n      return {\n        bigNumbersDelta: bigNumbersDelta,\n        startLineNumber: startLineNumber,\n        endLineNumber: endLineNumber,\n        relativeVerticalOffset: linesOffsets,\n        centeredLineNumber: centeredLineNumber,\n        completelyVisibleStartLineNumber: completelyVisibleStartLineNumber,\n        completelyVisibleEndLineNumber: completelyVisibleEndLineNumber,\n        lineHeight: this._lineHeight\n      };\n    }\n    getVerticalOffsetForWhitespaceIndex(whitespaceIndex) {\n      this._checkPendingChanges();\n      whitespaceIndex = whitespaceIndex | 0;\n      const afterLineNumber = this.getAfterLineNumberForWhitespaceIndex(whitespaceIndex);\n      let previousLinesHeight;\n      if (afterLineNumber >= 1) {\n        previousLinesHeight = this._lineHeight * afterLineNumber;\n      } else {\n        previousLinesHeight = 0;\n      }\n      let previousWhitespacesHeight;\n      if (whitespaceIndex > 0) {\n        previousWhitespacesHeight = this.getWhitespacesAccumulatedHeight(whitespaceIndex - 1);\n      } else {\n        previousWhitespacesHeight = 0;\n      }\n      return previousLinesHeight + previousWhitespacesHeight + this._paddingTop;\n    }\n    getWhitespaceIndexAtOrAfterVerticallOffset(verticalOffset) {\n      this._checkPendingChanges();\n      verticalOffset = verticalOffset | 0;\n      let minWhitespaceIndex = 0;\n      let maxWhitespaceIndex = this.getWhitespacesCount() - 1;\n      if (maxWhitespaceIndex < 0) {\n        return -1;\n      }\n      // Special case: nothing to be found\n      const maxWhitespaceVerticalOffset = this.getVerticalOffsetForWhitespaceIndex(maxWhitespaceIndex);\n      const maxWhitespaceHeight = this.getHeightForWhitespaceIndex(maxWhitespaceIndex);\n      if (verticalOffset >= maxWhitespaceVerticalOffset + maxWhitespaceHeight) {\n        return -1;\n      }\n      while (minWhitespaceIndex < maxWhitespaceIndex) {\n        const midWhitespaceIndex = Math.floor((minWhitespaceIndex + maxWhitespaceIndex) / 2);\n        const midWhitespaceVerticalOffset = this.getVerticalOffsetForWhitespaceIndex(midWhitespaceIndex);\n        const midWhitespaceHeight = this.getHeightForWhitespaceIndex(midWhitespaceIndex);\n        if (verticalOffset >= midWhitespaceVerticalOffset + midWhitespaceHeight) {\n          // vertical offset is after whitespace\n          minWhitespaceIndex = midWhitespaceIndex + 1;\n        } else if (verticalOffset >= midWhitespaceVerticalOffset) {\n          // Hit\n          return midWhitespaceIndex;\n        } else {\n          // vertical offset is before whitespace, but midWhitespaceIndex might still be what we're searching for\n          maxWhitespaceIndex = midWhitespaceIndex;\n        }\n      }\n      return minWhitespaceIndex;\n    }\n    /**\n     * Get exactly the whitespace that is layouted at `verticalOffset`.\n     *\n     * @param verticalOffset The vertical offset.\n     * @return Precisely the whitespace that is layouted at `verticaloffset` or null.\n     */\n    getWhitespaceAtVerticalOffset(verticalOffset) {\n      this._checkPendingChanges();\n      verticalOffset = verticalOffset | 0;\n      const candidateIndex = this.getWhitespaceIndexAtOrAfterVerticallOffset(verticalOffset);\n      if (candidateIndex < 0) {\n        return null;\n      }\n      if (candidateIndex >= this.getWhitespacesCount()) {\n        return null;\n      }\n      const candidateTop = this.getVerticalOffsetForWhitespaceIndex(candidateIndex);\n      if (candidateTop > verticalOffset) {\n        return null;\n      }\n      const candidateHeight = this.getHeightForWhitespaceIndex(candidateIndex);\n      const candidateId = this.getIdForWhitespaceIndex(candidateIndex);\n      const candidateAfterLineNumber = this.getAfterLineNumberForWhitespaceIndex(candidateIndex);\n      return {\n        id: candidateId,\n        afterLineNumber: candidateAfterLineNumber,\n        verticalOffset: candidateTop,\n        height: candidateHeight\n      };\n    }\n    /**\n     * Get a list of whitespaces that are positioned between `verticalOffset1` and `verticalOffset2`.\n     *\n     * @param verticalOffset1 The beginning of the viewport.\n     * @param verticalOffset2 The end of the viewport.\n     * @return An array with all the whitespaces in the viewport. If no whitespace is in viewport, the array is empty.\n     */\n    getWhitespaceViewportData(verticalOffset1, verticalOffset2) {\n      this._checkPendingChanges();\n      verticalOffset1 = verticalOffset1 | 0;\n      verticalOffset2 = verticalOffset2 | 0;\n      const startIndex = this.getWhitespaceIndexAtOrAfterVerticallOffset(verticalOffset1);\n      const endIndex = this.getWhitespacesCount() - 1;\n      if (startIndex < 0) {\n        return [];\n      }\n      const result = [];\n      for (let i = startIndex; i <= endIndex; i++) {\n        const top = this.getVerticalOffsetForWhitespaceIndex(i);\n        const height = this.getHeightForWhitespaceIndex(i);\n        if (top >= verticalOffset2) {\n          break;\n        }\n        result.push({\n          id: this.getIdForWhitespaceIndex(i),\n          afterLineNumber: this.getAfterLineNumberForWhitespaceIndex(i),\n          verticalOffset: top,\n          height: height\n        });\n      }\n      return result;\n    }\n    /**\n     * Get all whitespaces.\n     */\n    getWhitespaces() {\n      this._checkPendingChanges();\n      return this._arr.slice(0);\n    }\n    /**\n     * The number of whitespaces.\n     */\n    getWhitespacesCount() {\n      this._checkPendingChanges();\n      return this._arr.length;\n    }\n    /**\n     * Get the `id` for whitespace at index `index`.\n     *\n     * @param index The index of the whitespace.\n     * @return `id` of whitespace at `index`.\n     */\n    getIdForWhitespaceIndex(index) {\n      this._checkPendingChanges();\n      index = index | 0;\n      return this._arr[index].id;\n    }\n    /**\n     * Get the `afterLineNumber` for whitespace at index `index`.\n     *\n     * @param index The index of the whitespace.\n     * @return `afterLineNumber` of whitespace at `index`.\n     */\n    getAfterLineNumberForWhitespaceIndex(index) {\n      this._checkPendingChanges();\n      index = index | 0;\n      return this._arr[index].afterLineNumber;\n    }\n    /**\n     * Get the `height` for whitespace at index `index`.\n     *\n     * @param index The index of the whitespace.\n     * @return `height` of whitespace at `index`.\n     */\n    getHeightForWhitespaceIndex(index) {\n      this._checkPendingChanges();\n      index = index | 0;\n      return this._arr[index].height;\n    }\n  }\n  return LinesLayout;\n})();", "map": {"version": 3, "names": ["strings", "PendingChanges", "constructor", "_hasPending", "_inserts", "_changes", "_removes", "insert", "x", "push", "change", "remove", "mustCommit", "commit", "linesLayout", "inserts", "changes", "removes", "_commitPendingChanges", "EditorWhitespace", "id", "afterLineNumber", "ordinal", "height", "min<PERSON><PERSON><PERSON>", "prefixSum", "LinesLayout", "INSTANCE_COUNT", "lineCount", "lineHeight", "paddingTop", "paddingBottom", "_instanceId", "singleLetterHash", "_pendingChanges", "_lastWhitespaceId", "_arr", "_prefixSumValidIndex", "_minWidth", "_lineCount", "_lineHeight", "_paddingTop", "_paddingBottom", "findInsertionIndex", "arr", "low", "high", "length", "mid", "setLineHeight", "_checkPendingChanges", "setPadding", "onFlushed", "changeWhitespace", "callback", "hadAC<PERSON>e", "accessor", "insertWhitespace", "heightInPx", "changeOneWhitespace", "newAfterLineNumber", "newHeight", "removeWhitespace", "_insertWhitespace", "_changeOneWhitespace", "index", "_findWhitespaceIndex", "_removeWhitespace", "toRemove", "Set", "add", "toChange", "Map", "set", "applyRemoveAndChange", "whitespaces", "result", "whitespace", "has", "get", "concat", "sort", "a", "b", "insertIndex", "splice", "Math", "min", "i", "len", "removeIndex", "onLinesDeleted", "fromLineNumber", "toLineNumber", "onLinesInserted", "getWhitespacesTotalHeight", "getWhitespacesAccumulatedHeight", "startIndex", "max", "getLinesTotalHeight", "linesHeight", "whitespacesHeight", "getWhitespaceAccumulatedHeightBeforeLineNumber", "lineNumber", "lastWhitespaceBeforeLineNumber", "_findLastWhitespaceBeforeLineNumber", "delta", "<PERSON><PERSON><PERSON><PERSON>", "_findFirstWhitespaceAfterLineNumber", "firstWhitespaceAfterLineNumber", "getFirstWhitespaceIndexAfterLineNumber", "getVerticalOffsetForLineNumber", "includeViewZones", "previousLinesHeight", "previousWhitespacesHeight", "getVerticalOffsetAfterLineNumber", "getWhitespaceMinWidth", "isAfterLines", "verticalOffset", "totalHeight", "isInTopPadding", "isInBottomPadding", "getLineNumberAtOrAfterVerticalOffset", "linesCount", "minLineNumber", "maxLineNumber", "midLineNumber", "midLineNumberVerticalOffset", "getLinesViewportData", "verticalOffset1", "verticalOffset2", "startLineNumber", "startLineNumberVerticalOffset", "endLineNumber", "whitespaceIndex", "whitespaceCount", "getWhitespacesCount", "currentWhitespaceHeight", "currentWhitespaceAfterLineNumber", "getAfterLineNumberForWhitespaceIndex", "getHeightForWhitespaceIndex", "currentVerticalOffset", "currentLineRelativeOffset", "STEP_SIZE", "bigNumbersDelta", "floor", "linesOffsets", "verticalCenter", "centeredLineNumber", "currentLineTop", "currentLineBottom", "endLineNumberVerticalOffset", "completelyVisibleStartLineNumber", "completelyVisibleEndLineNumber", "relativeVerticalOffset", "getVerticalOffsetForWhitespaceIndex", "getWhitespaceIndexAtOrAfterVerticallOffset", "minWhitespaceIndex", "maxWhitespaceIndex", "maxWhitespaceVerticalOffset", "maxWhitespaceHeight", "midWhitespaceIndex", "midWhitespaceVerticalOffset", "midWhitespaceHeight", "getWhitespaceAtVerticalOffset", "candidateIndex", "candidate<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "candidateId", "getIdForWhitespaceIndex", "candidateAfterLineNumber", "getWhitespaceViewportData", "endIndex", "top", "getWhitespaces", "slice"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/viewLayout/linesLayout.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as strings from '../../../base/common/strings.js';\nclass PendingChanges {\n    constructor() {\n        this._hasPending = false;\n        this._inserts = [];\n        this._changes = [];\n        this._removes = [];\n    }\n    insert(x) {\n        this._hasPending = true;\n        this._inserts.push(x);\n    }\n    change(x) {\n        this._hasPending = true;\n        this._changes.push(x);\n    }\n    remove(x) {\n        this._hasPending = true;\n        this._removes.push(x);\n    }\n    mustCommit() {\n        return this._hasPending;\n    }\n    commit(linesLayout) {\n        if (!this._hasPending) {\n            return;\n        }\n        const inserts = this._inserts;\n        const changes = this._changes;\n        const removes = this._removes;\n        this._hasPending = false;\n        this._inserts = [];\n        this._changes = [];\n        this._removes = [];\n        linesLayout._commitPendingChanges(inserts, changes, removes);\n    }\n}\nexport class EditorWhitespace {\n    constructor(id, afterLineNumber, ordinal, height, minWidth) {\n        this.id = id;\n        this.afterLineNumber = afterLineNumber;\n        this.ordinal = ordinal;\n        this.height = height;\n        this.minWidth = minWidth;\n        this.prefixSum = 0;\n    }\n}\n/**\n * Layouting of objects that take vertical space (by having a height) and push down other objects.\n *\n * These objects are basically either text (lines) or spaces between those lines (whitespaces).\n * This provides commodity operations for working with lines that contain whitespace that pushes lines lower (vertically).\n */\nexport class LinesLayout {\n    static { this.INSTANCE_COUNT = 0; }\n    constructor(lineCount, lineHeight, paddingTop, paddingBottom) {\n        this._instanceId = strings.singleLetterHash(++LinesLayout.INSTANCE_COUNT);\n        this._pendingChanges = new PendingChanges();\n        this._lastWhitespaceId = 0;\n        this._arr = [];\n        this._prefixSumValidIndex = -1;\n        this._minWidth = -1; /* marker for not being computed */\n        this._lineCount = lineCount;\n        this._lineHeight = lineHeight;\n        this._paddingTop = paddingTop;\n        this._paddingBottom = paddingBottom;\n    }\n    /**\n     * Find the insertion index for a new value inside a sorted array of values.\n     * If the value is already present in the sorted array, the insertion index will be after the already existing value.\n     */\n    static findInsertionIndex(arr, afterLineNumber, ordinal) {\n        let low = 0;\n        let high = arr.length;\n        while (low < high) {\n            const mid = ((low + high) >>> 1);\n            if (afterLineNumber === arr[mid].afterLineNumber) {\n                if (ordinal < arr[mid].ordinal) {\n                    high = mid;\n                }\n                else {\n                    low = mid + 1;\n                }\n            }\n            else if (afterLineNumber < arr[mid].afterLineNumber) {\n                high = mid;\n            }\n            else {\n                low = mid + 1;\n            }\n        }\n        return low;\n    }\n    /**\n     * Change the height of a line in pixels.\n     */\n    setLineHeight(lineHeight) {\n        this._checkPendingChanges();\n        this._lineHeight = lineHeight;\n    }\n    /**\n     * Changes the padding used to calculate vertical offsets.\n     */\n    setPadding(paddingTop, paddingBottom) {\n        this._paddingTop = paddingTop;\n        this._paddingBottom = paddingBottom;\n    }\n    /**\n     * Set the number of lines.\n     *\n     * @param lineCount New number of lines.\n     */\n    onFlushed(lineCount) {\n        this._checkPendingChanges();\n        this._lineCount = lineCount;\n    }\n    changeWhitespace(callback) {\n        let hadAChange = false;\n        try {\n            const accessor = {\n                insertWhitespace: (afterLineNumber, ordinal, heightInPx, minWidth) => {\n                    hadAChange = true;\n                    afterLineNumber = afterLineNumber | 0;\n                    ordinal = ordinal | 0;\n                    heightInPx = heightInPx | 0;\n                    minWidth = minWidth | 0;\n                    const id = this._instanceId + (++this._lastWhitespaceId);\n                    this._pendingChanges.insert(new EditorWhitespace(id, afterLineNumber, ordinal, heightInPx, minWidth));\n                    return id;\n                },\n                changeOneWhitespace: (id, newAfterLineNumber, newHeight) => {\n                    hadAChange = true;\n                    newAfterLineNumber = newAfterLineNumber | 0;\n                    newHeight = newHeight | 0;\n                    this._pendingChanges.change({ id, newAfterLineNumber, newHeight });\n                },\n                removeWhitespace: (id) => {\n                    hadAChange = true;\n                    this._pendingChanges.remove({ id });\n                }\n            };\n            callback(accessor);\n        }\n        finally {\n            this._pendingChanges.commit(this);\n        }\n        return hadAChange;\n    }\n    _commitPendingChanges(inserts, changes, removes) {\n        if (inserts.length > 0 || removes.length > 0) {\n            this._minWidth = -1; /* marker for not being computed */\n        }\n        if (inserts.length + changes.length + removes.length <= 1) {\n            // when only one thing happened, handle it \"delicately\"\n            for (const insert of inserts) {\n                this._insertWhitespace(insert);\n            }\n            for (const change of changes) {\n                this._changeOneWhitespace(change.id, change.newAfterLineNumber, change.newHeight);\n            }\n            for (const remove of removes) {\n                const index = this._findWhitespaceIndex(remove.id);\n                if (index === -1) {\n                    continue;\n                }\n                this._removeWhitespace(index);\n            }\n            return;\n        }\n        // simply rebuild the entire datastructure\n        const toRemove = new Set();\n        for (const remove of removes) {\n            toRemove.add(remove.id);\n        }\n        const toChange = new Map();\n        for (const change of changes) {\n            toChange.set(change.id, change);\n        }\n        const applyRemoveAndChange = (whitespaces) => {\n            const result = [];\n            for (const whitespace of whitespaces) {\n                if (toRemove.has(whitespace.id)) {\n                    continue;\n                }\n                if (toChange.has(whitespace.id)) {\n                    const change = toChange.get(whitespace.id);\n                    whitespace.afterLineNumber = change.newAfterLineNumber;\n                    whitespace.height = change.newHeight;\n                }\n                result.push(whitespace);\n            }\n            return result;\n        };\n        const result = applyRemoveAndChange(this._arr).concat(applyRemoveAndChange(inserts));\n        result.sort((a, b) => {\n            if (a.afterLineNumber === b.afterLineNumber) {\n                return a.ordinal - b.ordinal;\n            }\n            return a.afterLineNumber - b.afterLineNumber;\n        });\n        this._arr = result;\n        this._prefixSumValidIndex = -1;\n    }\n    _checkPendingChanges() {\n        if (this._pendingChanges.mustCommit()) {\n            this._pendingChanges.commit(this);\n        }\n    }\n    _insertWhitespace(whitespace) {\n        const insertIndex = LinesLayout.findInsertionIndex(this._arr, whitespace.afterLineNumber, whitespace.ordinal);\n        this._arr.splice(insertIndex, 0, whitespace);\n        this._prefixSumValidIndex = Math.min(this._prefixSumValidIndex, insertIndex - 1);\n    }\n    _findWhitespaceIndex(id) {\n        const arr = this._arr;\n        for (let i = 0, len = arr.length; i < len; i++) {\n            if (arr[i].id === id) {\n                return i;\n            }\n        }\n        return -1;\n    }\n    _changeOneWhitespace(id, newAfterLineNumber, newHeight) {\n        const index = this._findWhitespaceIndex(id);\n        if (index === -1) {\n            return;\n        }\n        if (this._arr[index].height !== newHeight) {\n            this._arr[index].height = newHeight;\n            this._prefixSumValidIndex = Math.min(this._prefixSumValidIndex, index - 1);\n        }\n        if (this._arr[index].afterLineNumber !== newAfterLineNumber) {\n            // `afterLineNumber` changed for this whitespace\n            // Record old whitespace\n            const whitespace = this._arr[index];\n            // Since changing `afterLineNumber` can trigger a reordering, we're gonna remove this whitespace\n            this._removeWhitespace(index);\n            whitespace.afterLineNumber = newAfterLineNumber;\n            // And add it again\n            this._insertWhitespace(whitespace);\n        }\n    }\n    _removeWhitespace(removeIndex) {\n        this._arr.splice(removeIndex, 1);\n        this._prefixSumValidIndex = Math.min(this._prefixSumValidIndex, removeIndex - 1);\n    }\n    /**\n     * Notify the layouter that lines have been deleted (a continuous zone of lines).\n     *\n     * @param fromLineNumber The line number at which the deletion started, inclusive\n     * @param toLineNumber The line number at which the deletion ended, inclusive\n     */\n    onLinesDeleted(fromLineNumber, toLineNumber) {\n        this._checkPendingChanges();\n        fromLineNumber = fromLineNumber | 0;\n        toLineNumber = toLineNumber | 0;\n        this._lineCount -= (toLineNumber - fromLineNumber + 1);\n        for (let i = 0, len = this._arr.length; i < len; i++) {\n            const afterLineNumber = this._arr[i].afterLineNumber;\n            if (fromLineNumber <= afterLineNumber && afterLineNumber <= toLineNumber) {\n                // The line this whitespace was after has been deleted\n                //  => move whitespace to before first deleted line\n                this._arr[i].afterLineNumber = fromLineNumber - 1;\n            }\n            else if (afterLineNumber > toLineNumber) {\n                // The line this whitespace was after has been moved up\n                //  => move whitespace up\n                this._arr[i].afterLineNumber -= (toLineNumber - fromLineNumber + 1);\n            }\n        }\n    }\n    /**\n     * Notify the layouter that lines have been inserted (a continuous zone of lines).\n     *\n     * @param fromLineNumber The line number at which the insertion started, inclusive\n     * @param toLineNumber The line number at which the insertion ended, inclusive.\n     */\n    onLinesInserted(fromLineNumber, toLineNumber) {\n        this._checkPendingChanges();\n        fromLineNumber = fromLineNumber | 0;\n        toLineNumber = toLineNumber | 0;\n        this._lineCount += (toLineNumber - fromLineNumber + 1);\n        for (let i = 0, len = this._arr.length; i < len; i++) {\n            const afterLineNumber = this._arr[i].afterLineNumber;\n            if (fromLineNumber <= afterLineNumber) {\n                this._arr[i].afterLineNumber += (toLineNumber - fromLineNumber + 1);\n            }\n        }\n    }\n    /**\n     * Get the sum of all the whitespaces.\n     */\n    getWhitespacesTotalHeight() {\n        this._checkPendingChanges();\n        if (this._arr.length === 0) {\n            return 0;\n        }\n        return this.getWhitespacesAccumulatedHeight(this._arr.length - 1);\n    }\n    /**\n     * Return the sum of the heights of the whitespaces at [0..index].\n     * This includes the whitespace at `index`.\n     *\n     * @param index The index of the whitespace.\n     * @return The sum of the heights of all whitespaces before the one at `index`, including the one at `index`.\n     */\n    getWhitespacesAccumulatedHeight(index) {\n        this._checkPendingChanges();\n        index = index | 0;\n        let startIndex = Math.max(0, this._prefixSumValidIndex + 1);\n        if (startIndex === 0) {\n            this._arr[0].prefixSum = this._arr[0].height;\n            startIndex++;\n        }\n        for (let i = startIndex; i <= index; i++) {\n            this._arr[i].prefixSum = this._arr[i - 1].prefixSum + this._arr[i].height;\n        }\n        this._prefixSumValidIndex = Math.max(this._prefixSumValidIndex, index);\n        return this._arr[index].prefixSum;\n    }\n    /**\n     * Get the sum of heights for all objects.\n     *\n     * @return The sum of heights for all objects.\n     */\n    getLinesTotalHeight() {\n        this._checkPendingChanges();\n        const linesHeight = this._lineHeight * this._lineCount;\n        const whitespacesHeight = this.getWhitespacesTotalHeight();\n        return linesHeight + whitespacesHeight + this._paddingTop + this._paddingBottom;\n    }\n    /**\n     * Returns the accumulated height of whitespaces before the given line number.\n     *\n     * @param lineNumber The line number\n     */\n    getWhitespaceAccumulatedHeightBeforeLineNumber(lineNumber) {\n        this._checkPendingChanges();\n        lineNumber = lineNumber | 0;\n        const lastWhitespaceBeforeLineNumber = this._findLastWhitespaceBeforeLineNumber(lineNumber);\n        if (lastWhitespaceBeforeLineNumber === -1) {\n            return 0;\n        }\n        return this.getWhitespacesAccumulatedHeight(lastWhitespaceBeforeLineNumber);\n    }\n    _findLastWhitespaceBeforeLineNumber(lineNumber) {\n        lineNumber = lineNumber | 0;\n        // Find the whitespace before line number\n        const arr = this._arr;\n        let low = 0;\n        let high = arr.length - 1;\n        while (low <= high) {\n            const delta = (high - low) | 0;\n            const halfDelta = (delta / 2) | 0;\n            const mid = (low + halfDelta) | 0;\n            if (arr[mid].afterLineNumber < lineNumber) {\n                if (mid + 1 >= arr.length || arr[mid + 1].afterLineNumber >= lineNumber) {\n                    return mid;\n                }\n                else {\n                    low = (mid + 1) | 0;\n                }\n            }\n            else {\n                high = (mid - 1) | 0;\n            }\n        }\n        return -1;\n    }\n    _findFirstWhitespaceAfterLineNumber(lineNumber) {\n        lineNumber = lineNumber | 0;\n        const lastWhitespaceBeforeLineNumber = this._findLastWhitespaceBeforeLineNumber(lineNumber);\n        const firstWhitespaceAfterLineNumber = lastWhitespaceBeforeLineNumber + 1;\n        if (firstWhitespaceAfterLineNumber < this._arr.length) {\n            return firstWhitespaceAfterLineNumber;\n        }\n        return -1;\n    }\n    /**\n     * Find the index of the first whitespace which has `afterLineNumber` >= `lineNumber`.\n     * @return The index of the first whitespace with `afterLineNumber` >= `lineNumber` or -1 if no whitespace is found.\n     */\n    getFirstWhitespaceIndexAfterLineNumber(lineNumber) {\n        this._checkPendingChanges();\n        lineNumber = lineNumber | 0;\n        return this._findFirstWhitespaceAfterLineNumber(lineNumber);\n    }\n    /**\n     * Get the vertical offset (the sum of heights for all objects above) a certain line number.\n     *\n     * @param lineNumber The line number\n     * @return The sum of heights for all objects above `lineNumber`.\n     */\n    getVerticalOffsetForLineNumber(lineNumber, includeViewZones = false) {\n        this._checkPendingChanges();\n        lineNumber = lineNumber | 0;\n        let previousLinesHeight;\n        if (lineNumber > 1) {\n            previousLinesHeight = this._lineHeight * (lineNumber - 1);\n        }\n        else {\n            previousLinesHeight = 0;\n        }\n        const previousWhitespacesHeight = this.getWhitespaceAccumulatedHeightBeforeLineNumber(lineNumber - (includeViewZones ? 1 : 0));\n        return previousLinesHeight + previousWhitespacesHeight + this._paddingTop;\n    }\n    /**\n     * Get the vertical offset (the sum of heights for all objects above) a certain line number.\n     *\n     * @param lineNumber The line number\n     * @return The sum of heights for all objects above `lineNumber`.\n     */\n    getVerticalOffsetAfterLineNumber(lineNumber, includeViewZones = false) {\n        this._checkPendingChanges();\n        lineNumber = lineNumber | 0;\n        const previousLinesHeight = this._lineHeight * lineNumber;\n        const previousWhitespacesHeight = this.getWhitespaceAccumulatedHeightBeforeLineNumber(lineNumber + (includeViewZones ? 1 : 0));\n        return previousLinesHeight + previousWhitespacesHeight + this._paddingTop;\n    }\n    /**\n     * The maximum min width for all whitespaces.\n     */\n    getWhitespaceMinWidth() {\n        this._checkPendingChanges();\n        if (this._minWidth === -1) {\n            let minWidth = 0;\n            for (let i = 0, len = this._arr.length; i < len; i++) {\n                minWidth = Math.max(minWidth, this._arr[i].minWidth);\n            }\n            this._minWidth = minWidth;\n        }\n        return this._minWidth;\n    }\n    /**\n     * Check if `verticalOffset` is below all lines.\n     */\n    isAfterLines(verticalOffset) {\n        this._checkPendingChanges();\n        const totalHeight = this.getLinesTotalHeight();\n        return verticalOffset > totalHeight;\n    }\n    isInTopPadding(verticalOffset) {\n        if (this._paddingTop === 0) {\n            return false;\n        }\n        this._checkPendingChanges();\n        return (verticalOffset < this._paddingTop);\n    }\n    isInBottomPadding(verticalOffset) {\n        if (this._paddingBottom === 0) {\n            return false;\n        }\n        this._checkPendingChanges();\n        const totalHeight = this.getLinesTotalHeight();\n        return (verticalOffset >= totalHeight - this._paddingBottom);\n    }\n    /**\n     * Find the first line number that is at or after vertical offset `verticalOffset`.\n     * i.e. if getVerticalOffsetForLine(line) is x and getVerticalOffsetForLine(line + 1) is y, then\n     * getLineNumberAtOrAfterVerticalOffset(i) = line, x <= i < y.\n     *\n     * @param verticalOffset The vertical offset to search at.\n     * @return The line number at or after vertical offset `verticalOffset`.\n     */\n    getLineNumberAtOrAfterVerticalOffset(verticalOffset) {\n        this._checkPendingChanges();\n        verticalOffset = verticalOffset | 0;\n        if (verticalOffset < 0) {\n            return 1;\n        }\n        const linesCount = this._lineCount | 0;\n        const lineHeight = this._lineHeight;\n        let minLineNumber = 1;\n        let maxLineNumber = linesCount;\n        while (minLineNumber < maxLineNumber) {\n            const midLineNumber = ((minLineNumber + maxLineNumber) / 2) | 0;\n            const midLineNumberVerticalOffset = this.getVerticalOffsetForLineNumber(midLineNumber) | 0;\n            if (verticalOffset >= midLineNumberVerticalOffset + lineHeight) {\n                // vertical offset is after mid line number\n                minLineNumber = midLineNumber + 1;\n            }\n            else if (verticalOffset >= midLineNumberVerticalOffset) {\n                // Hit\n                return midLineNumber;\n            }\n            else {\n                // vertical offset is before mid line number, but mid line number could still be what we're searching for\n                maxLineNumber = midLineNumber;\n            }\n        }\n        if (minLineNumber > linesCount) {\n            return linesCount;\n        }\n        return minLineNumber;\n    }\n    /**\n     * Get all the lines and their relative vertical offsets that are positioned between `verticalOffset1` and `verticalOffset2`.\n     *\n     * @param verticalOffset1 The beginning of the viewport.\n     * @param verticalOffset2 The end of the viewport.\n     * @return A structure describing the lines positioned between `verticalOffset1` and `verticalOffset2`.\n     */\n    getLinesViewportData(verticalOffset1, verticalOffset2) {\n        this._checkPendingChanges();\n        verticalOffset1 = verticalOffset1 | 0;\n        verticalOffset2 = verticalOffset2 | 0;\n        const lineHeight = this._lineHeight;\n        // Find first line number\n        // We don't live in a perfect world, so the line number might start before or after verticalOffset1\n        const startLineNumber = this.getLineNumberAtOrAfterVerticalOffset(verticalOffset1) | 0;\n        const startLineNumberVerticalOffset = this.getVerticalOffsetForLineNumber(startLineNumber) | 0;\n        let endLineNumber = this._lineCount | 0;\n        // Also keep track of what whitespace we've got\n        let whitespaceIndex = this.getFirstWhitespaceIndexAfterLineNumber(startLineNumber) | 0;\n        const whitespaceCount = this.getWhitespacesCount() | 0;\n        let currentWhitespaceHeight;\n        let currentWhitespaceAfterLineNumber;\n        if (whitespaceIndex === -1) {\n            whitespaceIndex = whitespaceCount;\n            currentWhitespaceAfterLineNumber = endLineNumber + 1;\n            currentWhitespaceHeight = 0;\n        }\n        else {\n            currentWhitespaceAfterLineNumber = this.getAfterLineNumberForWhitespaceIndex(whitespaceIndex) | 0;\n            currentWhitespaceHeight = this.getHeightForWhitespaceIndex(whitespaceIndex) | 0;\n        }\n        let currentVerticalOffset = startLineNumberVerticalOffset;\n        let currentLineRelativeOffset = currentVerticalOffset;\n        // IE (all versions) cannot handle units above about 1,533,908 px, so every 500k pixels bring numbers down\n        const STEP_SIZE = 500000;\n        let bigNumbersDelta = 0;\n        if (startLineNumberVerticalOffset >= STEP_SIZE) {\n            // Compute a delta that guarantees that lines are positioned at `lineHeight` increments\n            bigNumbersDelta = Math.floor(startLineNumberVerticalOffset / STEP_SIZE) * STEP_SIZE;\n            bigNumbersDelta = Math.floor(bigNumbersDelta / lineHeight) * lineHeight;\n            currentLineRelativeOffset -= bigNumbersDelta;\n        }\n        const linesOffsets = [];\n        const verticalCenter = verticalOffset1 + (verticalOffset2 - verticalOffset1) / 2;\n        let centeredLineNumber = -1;\n        // Figure out how far the lines go\n        for (let lineNumber = startLineNumber; lineNumber <= endLineNumber; lineNumber++) {\n            if (centeredLineNumber === -1) {\n                const currentLineTop = currentVerticalOffset;\n                const currentLineBottom = currentVerticalOffset + lineHeight;\n                if ((currentLineTop <= verticalCenter && verticalCenter < currentLineBottom) || currentLineTop > verticalCenter) {\n                    centeredLineNumber = lineNumber;\n                }\n            }\n            // Count current line height in the vertical offsets\n            currentVerticalOffset += lineHeight;\n            linesOffsets[lineNumber - startLineNumber] = currentLineRelativeOffset;\n            // Next line starts immediately after this one\n            currentLineRelativeOffset += lineHeight;\n            while (currentWhitespaceAfterLineNumber === lineNumber) {\n                // Push down next line with the height of the current whitespace\n                currentLineRelativeOffset += currentWhitespaceHeight;\n                // Count current whitespace in the vertical offsets\n                currentVerticalOffset += currentWhitespaceHeight;\n                whitespaceIndex++;\n                if (whitespaceIndex >= whitespaceCount) {\n                    currentWhitespaceAfterLineNumber = endLineNumber + 1;\n                }\n                else {\n                    currentWhitespaceAfterLineNumber = this.getAfterLineNumberForWhitespaceIndex(whitespaceIndex) | 0;\n                    currentWhitespaceHeight = this.getHeightForWhitespaceIndex(whitespaceIndex) | 0;\n                }\n            }\n            if (currentVerticalOffset >= verticalOffset2) {\n                // We have covered the entire viewport area, time to stop\n                endLineNumber = lineNumber;\n                break;\n            }\n        }\n        if (centeredLineNumber === -1) {\n            centeredLineNumber = endLineNumber;\n        }\n        const endLineNumberVerticalOffset = this.getVerticalOffsetForLineNumber(endLineNumber) | 0;\n        let completelyVisibleStartLineNumber = startLineNumber;\n        let completelyVisibleEndLineNumber = endLineNumber;\n        if (completelyVisibleStartLineNumber < completelyVisibleEndLineNumber) {\n            if (startLineNumberVerticalOffset < verticalOffset1) {\n                completelyVisibleStartLineNumber++;\n            }\n        }\n        if (completelyVisibleStartLineNumber < completelyVisibleEndLineNumber) {\n            if (endLineNumberVerticalOffset + lineHeight > verticalOffset2) {\n                completelyVisibleEndLineNumber--;\n            }\n        }\n        return {\n            bigNumbersDelta: bigNumbersDelta,\n            startLineNumber: startLineNumber,\n            endLineNumber: endLineNumber,\n            relativeVerticalOffset: linesOffsets,\n            centeredLineNumber: centeredLineNumber,\n            completelyVisibleStartLineNumber: completelyVisibleStartLineNumber,\n            completelyVisibleEndLineNumber: completelyVisibleEndLineNumber,\n            lineHeight: this._lineHeight,\n        };\n    }\n    getVerticalOffsetForWhitespaceIndex(whitespaceIndex) {\n        this._checkPendingChanges();\n        whitespaceIndex = whitespaceIndex | 0;\n        const afterLineNumber = this.getAfterLineNumberForWhitespaceIndex(whitespaceIndex);\n        let previousLinesHeight;\n        if (afterLineNumber >= 1) {\n            previousLinesHeight = this._lineHeight * afterLineNumber;\n        }\n        else {\n            previousLinesHeight = 0;\n        }\n        let previousWhitespacesHeight;\n        if (whitespaceIndex > 0) {\n            previousWhitespacesHeight = this.getWhitespacesAccumulatedHeight(whitespaceIndex - 1);\n        }\n        else {\n            previousWhitespacesHeight = 0;\n        }\n        return previousLinesHeight + previousWhitespacesHeight + this._paddingTop;\n    }\n    getWhitespaceIndexAtOrAfterVerticallOffset(verticalOffset) {\n        this._checkPendingChanges();\n        verticalOffset = verticalOffset | 0;\n        let minWhitespaceIndex = 0;\n        let maxWhitespaceIndex = this.getWhitespacesCount() - 1;\n        if (maxWhitespaceIndex < 0) {\n            return -1;\n        }\n        // Special case: nothing to be found\n        const maxWhitespaceVerticalOffset = this.getVerticalOffsetForWhitespaceIndex(maxWhitespaceIndex);\n        const maxWhitespaceHeight = this.getHeightForWhitespaceIndex(maxWhitespaceIndex);\n        if (verticalOffset >= maxWhitespaceVerticalOffset + maxWhitespaceHeight) {\n            return -1;\n        }\n        while (minWhitespaceIndex < maxWhitespaceIndex) {\n            const midWhitespaceIndex = Math.floor((minWhitespaceIndex + maxWhitespaceIndex) / 2);\n            const midWhitespaceVerticalOffset = this.getVerticalOffsetForWhitespaceIndex(midWhitespaceIndex);\n            const midWhitespaceHeight = this.getHeightForWhitespaceIndex(midWhitespaceIndex);\n            if (verticalOffset >= midWhitespaceVerticalOffset + midWhitespaceHeight) {\n                // vertical offset is after whitespace\n                minWhitespaceIndex = midWhitespaceIndex + 1;\n            }\n            else if (verticalOffset >= midWhitespaceVerticalOffset) {\n                // Hit\n                return midWhitespaceIndex;\n            }\n            else {\n                // vertical offset is before whitespace, but midWhitespaceIndex might still be what we're searching for\n                maxWhitespaceIndex = midWhitespaceIndex;\n            }\n        }\n        return minWhitespaceIndex;\n    }\n    /**\n     * Get exactly the whitespace that is layouted at `verticalOffset`.\n     *\n     * @param verticalOffset The vertical offset.\n     * @return Precisely the whitespace that is layouted at `verticaloffset` or null.\n     */\n    getWhitespaceAtVerticalOffset(verticalOffset) {\n        this._checkPendingChanges();\n        verticalOffset = verticalOffset | 0;\n        const candidateIndex = this.getWhitespaceIndexAtOrAfterVerticallOffset(verticalOffset);\n        if (candidateIndex < 0) {\n            return null;\n        }\n        if (candidateIndex >= this.getWhitespacesCount()) {\n            return null;\n        }\n        const candidateTop = this.getVerticalOffsetForWhitespaceIndex(candidateIndex);\n        if (candidateTop > verticalOffset) {\n            return null;\n        }\n        const candidateHeight = this.getHeightForWhitespaceIndex(candidateIndex);\n        const candidateId = this.getIdForWhitespaceIndex(candidateIndex);\n        const candidateAfterLineNumber = this.getAfterLineNumberForWhitespaceIndex(candidateIndex);\n        return {\n            id: candidateId,\n            afterLineNumber: candidateAfterLineNumber,\n            verticalOffset: candidateTop,\n            height: candidateHeight\n        };\n    }\n    /**\n     * Get a list of whitespaces that are positioned between `verticalOffset1` and `verticalOffset2`.\n     *\n     * @param verticalOffset1 The beginning of the viewport.\n     * @param verticalOffset2 The end of the viewport.\n     * @return An array with all the whitespaces in the viewport. If no whitespace is in viewport, the array is empty.\n     */\n    getWhitespaceViewportData(verticalOffset1, verticalOffset2) {\n        this._checkPendingChanges();\n        verticalOffset1 = verticalOffset1 | 0;\n        verticalOffset2 = verticalOffset2 | 0;\n        const startIndex = this.getWhitespaceIndexAtOrAfterVerticallOffset(verticalOffset1);\n        const endIndex = this.getWhitespacesCount() - 1;\n        if (startIndex < 0) {\n            return [];\n        }\n        const result = [];\n        for (let i = startIndex; i <= endIndex; i++) {\n            const top = this.getVerticalOffsetForWhitespaceIndex(i);\n            const height = this.getHeightForWhitespaceIndex(i);\n            if (top >= verticalOffset2) {\n                break;\n            }\n            result.push({\n                id: this.getIdForWhitespaceIndex(i),\n                afterLineNumber: this.getAfterLineNumberForWhitespaceIndex(i),\n                verticalOffset: top,\n                height: height\n            });\n        }\n        return result;\n    }\n    /**\n     * Get all whitespaces.\n     */\n    getWhitespaces() {\n        this._checkPendingChanges();\n        return this._arr.slice(0);\n    }\n    /**\n     * The number of whitespaces.\n     */\n    getWhitespacesCount() {\n        this._checkPendingChanges();\n        return this._arr.length;\n    }\n    /**\n     * Get the `id` for whitespace at index `index`.\n     *\n     * @param index The index of the whitespace.\n     * @return `id` of whitespace at `index`.\n     */\n    getIdForWhitespaceIndex(index) {\n        this._checkPendingChanges();\n        index = index | 0;\n        return this._arr[index].id;\n    }\n    /**\n     * Get the `afterLineNumber` for whitespace at index `index`.\n     *\n     * @param index The index of the whitespace.\n     * @return `afterLineNumber` of whitespace at `index`.\n     */\n    getAfterLineNumberForWhitespaceIndex(index) {\n        this._checkPendingChanges();\n        index = index | 0;\n        return this._arr[index].afterLineNumber;\n    }\n    /**\n     * Get the `height` for whitespace at index `index`.\n     *\n     * @param index The index of the whitespace.\n     * @return `height` of whitespace at `index`.\n     */\n    getHeightForWhitespaceIndex(index) {\n        this._checkPendingChanges();\n        index = index | 0;\n        return this._arr[index].height;\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,KAAKA,OAAO,MAAM,iCAAiC;AAC1D,MAAMC,cAAc,CAAC;EACjBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,QAAQ,GAAG,EAAE;EACtB;EACAC,MAAMA,CAACC,CAAC,EAAE;IACN,IAAI,CAACL,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,QAAQ,CAACK,IAAI,CAACD,CAAC,CAAC;EACzB;EACAE,MAAMA,CAACF,CAAC,EAAE;IACN,IAAI,CAACL,WAAW,GAAG,IAAI;IACvB,IAAI,CAACE,QAAQ,CAACI,IAAI,CAACD,CAAC,CAAC;EACzB;EACAG,MAAMA,CAACH,CAAC,EAAE;IACN,IAAI,CAACL,WAAW,GAAG,IAAI;IACvB,IAAI,CAACG,QAAQ,CAACG,IAAI,CAACD,CAAC,CAAC;EACzB;EACAI,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAACT,WAAW;EAC3B;EACAU,MAAMA,CAACC,WAAW,EAAE;IAChB,IAAI,CAAC,IAAI,CAACX,WAAW,EAAE;MACnB;IACJ;IACA,MAAMY,OAAO,GAAG,IAAI,CAACX,QAAQ;IAC7B,MAAMY,OAAO,GAAG,IAAI,CAACX,QAAQ;IAC7B,MAAMY,OAAO,GAAG,IAAI,CAACX,QAAQ;IAC7B,IAAI,CAACH,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClBQ,WAAW,CAACI,qBAAqB,CAACH,OAAO,EAAEC,OAAO,EAAEC,OAAO,CAAC;EAChE;AACJ;AACA,OAAO,MAAME,gBAAgB,CAAC;EAC1BjB,WAAWA,CAACkB,EAAE,EAAEC,eAAe,EAAEC,OAAO,EAAEC,MAAM,EAAEC,QAAQ,EAAE;IACxD,IAAI,CAACJ,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,SAAS,GAAG,CAAC;EACtB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAaC,WAAW;EAAjB,MAAMA,WAAW,CAAC;IACrB;MAAS,IAAI,CAACC,cAAc,GAAG,CAAC;IAAE;IAClCzB,WAAWA,CAAC0B,SAAS,EAAEC,UAAU,EAAEC,UAAU,EAAEC,aAAa,EAAE;MAC1D,IAAI,CAACC,WAAW,GAAGhC,OAAO,CAACiC,gBAAgB,CAAC,EAAEP,WAAW,CAACC,cAAc,CAAC;MACzE,IAAI,CAACO,eAAe,GAAG,IAAIjC,cAAc,CAAC,CAAC;MAC3C,IAAI,CAACkC,iBAAiB,GAAG,CAAC;MAC1B,IAAI,CAACC,IAAI,GAAG,EAAE;MACd,IAAI,CAACC,oBAAoB,GAAG,CAAC,CAAC;MAC9B,IAAI,CAACC,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC;MACrB,IAAI,CAACC,UAAU,GAAGX,SAAS;MAC3B,IAAI,CAACY,WAAW,GAAGX,UAAU;MAC7B,IAAI,CAACY,WAAW,GAAGX,UAAU;MAC7B,IAAI,CAACY,cAAc,GAAGX,aAAa;IACvC;IACA;AACJ;AACA;AACA;IACI,OAAOY,kBAAkBA,CAACC,GAAG,EAAEvB,eAAe,EAAEC,OAAO,EAAE;MACrD,IAAIuB,GAAG,GAAG,CAAC;MACX,IAAIC,IAAI,GAAGF,GAAG,CAACG,MAAM;MACrB,OAAOF,GAAG,GAAGC,IAAI,EAAE;QACf,MAAME,GAAG,GAAKH,GAAG,GAAGC,IAAI,KAAM,CAAE;QAChC,IAAIzB,eAAe,KAAKuB,GAAG,CAACI,GAAG,CAAC,CAAC3B,eAAe,EAAE;UAC9C,IAAIC,OAAO,GAAGsB,GAAG,CAACI,GAAG,CAAC,CAAC1B,OAAO,EAAE;YAC5BwB,IAAI,GAAGE,GAAG;UACd,CAAC,MACI;YACDH,GAAG,GAAGG,GAAG,GAAG,CAAC;UACjB;QACJ,CAAC,MACI,IAAI3B,eAAe,GAAGuB,GAAG,CAACI,GAAG,CAAC,CAAC3B,eAAe,EAAE;UACjDyB,IAAI,GAAGE,GAAG;QACd,CAAC,MACI;UACDH,GAAG,GAAGG,GAAG,GAAG,CAAC;QACjB;MACJ;MACA,OAAOH,GAAG;IACd;IACA;AACJ;AACA;IACII,aAAaA,CAACpB,UAAU,EAAE;MACtB,IAAI,CAACqB,oBAAoB,CAAC,CAAC;MAC3B,IAAI,CAACV,WAAW,GAAGX,UAAU;IACjC;IACA;AACJ;AACA;IACIsB,UAAUA,CAACrB,UAAU,EAAEC,aAAa,EAAE;MAClC,IAAI,CAACU,WAAW,GAAGX,UAAU;MAC7B,IAAI,CAACY,cAAc,GAAGX,aAAa;IACvC;IACA;AACJ;AACA;AACA;AACA;IACIqB,SAASA,CAACxB,SAAS,EAAE;MACjB,IAAI,CAACsB,oBAAoB,CAAC,CAAC;MAC3B,IAAI,CAACX,UAAU,GAAGX,SAAS;IAC/B;IACAyB,gBAAgBA,CAACC,QAAQ,EAAE;MACvB,IAAIC,UAAU,GAAG,KAAK;MACtB,IAAI;QACA,MAAMC,QAAQ,GAAG;UACbC,gBAAgB,EAAEA,CAACpC,eAAe,EAAEC,OAAO,EAAEoC,UAAU,EAAElC,QAAQ,KAAK;YAClE+B,UAAU,GAAG,IAAI;YACjBlC,eAAe,GAAGA,eAAe,GAAG,CAAC;YACrCC,OAAO,GAAGA,OAAO,GAAG,CAAC;YACrBoC,UAAU,GAAGA,UAAU,GAAG,CAAC;YAC3BlC,QAAQ,GAAGA,QAAQ,GAAG,CAAC;YACvB,MAAMJ,EAAE,GAAG,IAAI,CAACY,WAAW,GAAI,EAAE,IAAI,CAACG,iBAAkB;YACxD,IAAI,CAACD,eAAe,CAAC3B,MAAM,CAAC,IAAIY,gBAAgB,CAACC,EAAE,EAAEC,eAAe,EAAEC,OAAO,EAAEoC,UAAU,EAAElC,QAAQ,CAAC,CAAC;YACrG,OAAOJ,EAAE;UACb,CAAC;UACDuC,mBAAmB,EAAEA,CAACvC,EAAE,EAAEwC,kBAAkB,EAAEC,SAAS,KAAK;YACxDN,UAAU,GAAG,IAAI;YACjBK,kBAAkB,GAAGA,kBAAkB,GAAG,CAAC;YAC3CC,SAAS,GAAGA,SAAS,GAAG,CAAC;YACzB,IAAI,CAAC3B,eAAe,CAACxB,MAAM,CAAC;cAAEU,EAAE;cAAEwC,kBAAkB;cAAEC;YAAU,CAAC,CAAC;UACtE,CAAC;UACDC,gBAAgB,EAAG1C,EAAE,IAAK;YACtBmC,UAAU,GAAG,IAAI;YACjB,IAAI,CAACrB,eAAe,CAACvB,MAAM,CAAC;cAAES;YAAG,CAAC,CAAC;UACvC;QACJ,CAAC;QACDkC,QAAQ,CAACE,QAAQ,CAAC;MACtB,CAAC,SACO;QACJ,IAAI,CAACtB,eAAe,CAACrB,MAAM,CAAC,IAAI,CAAC;MACrC;MACA,OAAO0C,UAAU;IACrB;IACArC,qBAAqBA,CAACH,OAAO,EAAEC,OAAO,EAAEC,OAAO,EAAE;MAC7C,IAAIF,OAAO,CAACgC,MAAM,GAAG,CAAC,IAAI9B,OAAO,CAAC8B,MAAM,GAAG,CAAC,EAAE;QAC1C,IAAI,CAACT,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC;MACzB;MACA,IAAIvB,OAAO,CAACgC,MAAM,GAAG/B,OAAO,CAAC+B,MAAM,GAAG9B,OAAO,CAAC8B,MAAM,IAAI,CAAC,EAAE;QACvD;QACA,KAAK,MAAMxC,MAAM,IAAIQ,OAAO,EAAE;UAC1B,IAAI,CAACgD,iBAAiB,CAACxD,MAAM,CAAC;QAClC;QACA,KAAK,MAAMG,MAAM,IAAIM,OAAO,EAAE;UAC1B,IAAI,CAACgD,oBAAoB,CAACtD,MAAM,CAACU,EAAE,EAAEV,MAAM,CAACkD,kBAAkB,EAAElD,MAAM,CAACmD,SAAS,CAAC;QACrF;QACA,KAAK,MAAMlD,MAAM,IAAIM,OAAO,EAAE;UAC1B,MAAMgD,KAAK,GAAG,IAAI,CAACC,oBAAoB,CAACvD,MAAM,CAACS,EAAE,CAAC;UAClD,IAAI6C,KAAK,KAAK,CAAC,CAAC,EAAE;YACd;UACJ;UACA,IAAI,CAACE,iBAAiB,CAACF,KAAK,CAAC;QACjC;QACA;MACJ;MACA;MACA,MAAMG,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC;MAC1B,KAAK,MAAM1D,MAAM,IAAIM,OAAO,EAAE;QAC1BmD,QAAQ,CAACE,GAAG,CAAC3D,MAAM,CAACS,EAAE,CAAC;MAC3B;MACA,MAAMmD,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC;MAC1B,KAAK,MAAM9D,MAAM,IAAIM,OAAO,EAAE;QAC1BuD,QAAQ,CAACE,GAAG,CAAC/D,MAAM,CAACU,EAAE,EAAEV,MAAM,CAAC;MACnC;MACA,MAAMgE,oBAAoB,GAAIC,WAAW,IAAK;QAC1C,MAAMC,MAAM,GAAG,EAAE;QACjB,KAAK,MAAMC,UAAU,IAAIF,WAAW,EAAE;UAClC,IAAIP,QAAQ,CAACU,GAAG,CAACD,UAAU,CAACzD,EAAE,CAAC,EAAE;YAC7B;UACJ;UACA,IAAImD,QAAQ,CAACO,GAAG,CAACD,UAAU,CAACzD,EAAE,CAAC,EAAE;YAC7B,MAAMV,MAAM,GAAG6D,QAAQ,CAACQ,GAAG,CAACF,UAAU,CAACzD,EAAE,CAAC;YAC1CyD,UAAU,CAACxD,eAAe,GAAGX,MAAM,CAACkD,kBAAkB;YACtDiB,UAAU,CAACtD,MAAM,GAAGb,MAAM,CAACmD,SAAS;UACxC;UACAe,MAAM,CAACnE,IAAI,CAACoE,UAAU,CAAC;QAC3B;QACA,OAAOD,MAAM;MACjB,CAAC;MACD,MAAMA,MAAM,GAAGF,oBAAoB,CAAC,IAAI,CAACtC,IAAI,CAAC,CAAC4C,MAAM,CAACN,oBAAoB,CAAC3D,OAAO,CAAC,CAAC;MACpF6D,MAAM,CAACK,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QAClB,IAAID,CAAC,CAAC7D,eAAe,KAAK8D,CAAC,CAAC9D,eAAe,EAAE;UACzC,OAAO6D,CAAC,CAAC5D,OAAO,GAAG6D,CAAC,CAAC7D,OAAO;QAChC;QACA,OAAO4D,CAAC,CAAC7D,eAAe,GAAG8D,CAAC,CAAC9D,eAAe;MAChD,CAAC,CAAC;MACF,IAAI,CAACe,IAAI,GAAGwC,MAAM;MAClB,IAAI,CAACvC,oBAAoB,GAAG,CAAC,CAAC;IAClC;IACAa,oBAAoBA,CAAA,EAAG;MACnB,IAAI,IAAI,CAAChB,eAAe,CAACtB,UAAU,CAAC,CAAC,EAAE;QACnC,IAAI,CAACsB,eAAe,CAACrB,MAAM,CAAC,IAAI,CAAC;MACrC;IACJ;IACAkD,iBAAiBA,CAACc,UAAU,EAAE;MAC1B,MAAMO,WAAW,GAAG1D,WAAW,CAACiB,kBAAkB,CAAC,IAAI,CAACP,IAAI,EAAEyC,UAAU,CAACxD,eAAe,EAAEwD,UAAU,CAACvD,OAAO,CAAC;MAC7G,IAAI,CAACc,IAAI,CAACiD,MAAM,CAACD,WAAW,EAAE,CAAC,EAAEP,UAAU,CAAC;MAC5C,IAAI,CAACxC,oBAAoB,GAAGiD,IAAI,CAACC,GAAG,CAAC,IAAI,CAAClD,oBAAoB,EAAE+C,WAAW,GAAG,CAAC,CAAC;IACpF;IACAlB,oBAAoBA,CAAC9C,EAAE,EAAE;MACrB,MAAMwB,GAAG,GAAG,IAAI,CAACR,IAAI;MACrB,KAAK,IAAIoD,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAG7C,GAAG,CAACG,MAAM,EAAEyC,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;QAC5C,IAAI5C,GAAG,CAAC4C,CAAC,CAAC,CAACpE,EAAE,KAAKA,EAAE,EAAE;UAClB,OAAOoE,CAAC;QACZ;MACJ;MACA,OAAO,CAAC,CAAC;IACb;IACAxB,oBAAoBA,CAAC5C,EAAE,EAAEwC,kBAAkB,EAAEC,SAAS,EAAE;MACpD,MAAMI,KAAK,GAAG,IAAI,CAACC,oBAAoB,CAAC9C,EAAE,CAAC;MAC3C,IAAI6C,KAAK,KAAK,CAAC,CAAC,EAAE;QACd;MACJ;MACA,IAAI,IAAI,CAAC7B,IAAI,CAAC6B,KAAK,CAAC,CAAC1C,MAAM,KAAKsC,SAAS,EAAE;QACvC,IAAI,CAACzB,IAAI,CAAC6B,KAAK,CAAC,CAAC1C,MAAM,GAAGsC,SAAS;QACnC,IAAI,CAACxB,oBAAoB,GAAGiD,IAAI,CAACC,GAAG,CAAC,IAAI,CAAClD,oBAAoB,EAAE4B,KAAK,GAAG,CAAC,CAAC;MAC9E;MACA,IAAI,IAAI,CAAC7B,IAAI,CAAC6B,KAAK,CAAC,CAAC5C,eAAe,KAAKuC,kBAAkB,EAAE;QACzD;QACA;QACA,MAAMiB,UAAU,GAAG,IAAI,CAACzC,IAAI,CAAC6B,KAAK,CAAC;QACnC;QACA,IAAI,CAACE,iBAAiB,CAACF,KAAK,CAAC;QAC7BY,UAAU,CAACxD,eAAe,GAAGuC,kBAAkB;QAC/C;QACA,IAAI,CAACG,iBAAiB,CAACc,UAAU,CAAC;MACtC;IACJ;IACAV,iBAAiBA,CAACuB,WAAW,EAAE;MAC3B,IAAI,CAACtD,IAAI,CAACiD,MAAM,CAACK,WAAW,EAAE,CAAC,CAAC;MAChC,IAAI,CAACrD,oBAAoB,GAAGiD,IAAI,CAACC,GAAG,CAAC,IAAI,CAAClD,oBAAoB,EAAEqD,WAAW,GAAG,CAAC,CAAC;IACpF;IACA;AACJ;AACA;AACA;AACA;AACA;IACIC,cAAcA,CAACC,cAAc,EAAEC,YAAY,EAAE;MACzC,IAAI,CAAC3C,oBAAoB,CAAC,CAAC;MAC3B0C,cAAc,GAAGA,cAAc,GAAG,CAAC;MACnCC,YAAY,GAAGA,YAAY,GAAG,CAAC;MAC/B,IAAI,CAACtD,UAAU,IAAKsD,YAAY,GAAGD,cAAc,GAAG,CAAE;MACtD,KAAK,IAAIJ,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAG,IAAI,CAACrD,IAAI,CAACW,MAAM,EAAEyC,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;QAClD,MAAMnE,eAAe,GAAG,IAAI,CAACe,IAAI,CAACoD,CAAC,CAAC,CAACnE,eAAe;QACpD,IAAIuE,cAAc,IAAIvE,eAAe,IAAIA,eAAe,IAAIwE,YAAY,EAAE;UACtE;UACA;UACA,IAAI,CAACzD,IAAI,CAACoD,CAAC,CAAC,CAACnE,eAAe,GAAGuE,cAAc,GAAG,CAAC;QACrD,CAAC,MACI,IAAIvE,eAAe,GAAGwE,YAAY,EAAE;UACrC;UACA;UACA,IAAI,CAACzD,IAAI,CAACoD,CAAC,CAAC,CAACnE,eAAe,IAAKwE,YAAY,GAAGD,cAAc,GAAG,CAAE;QACvE;MACJ;IACJ;IACA;AACJ;AACA;AACA;AACA;AACA;IACIE,eAAeA,CAACF,cAAc,EAAEC,YAAY,EAAE;MAC1C,IAAI,CAAC3C,oBAAoB,CAAC,CAAC;MAC3B0C,cAAc,GAAGA,cAAc,GAAG,CAAC;MACnCC,YAAY,GAAGA,YAAY,GAAG,CAAC;MAC/B,IAAI,CAACtD,UAAU,IAAKsD,YAAY,GAAGD,cAAc,GAAG,CAAE;MACtD,KAAK,IAAIJ,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAG,IAAI,CAACrD,IAAI,CAACW,MAAM,EAAEyC,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;QAClD,MAAMnE,eAAe,GAAG,IAAI,CAACe,IAAI,CAACoD,CAAC,CAAC,CAACnE,eAAe;QACpD,IAAIuE,cAAc,IAAIvE,eAAe,EAAE;UACnC,IAAI,CAACe,IAAI,CAACoD,CAAC,CAAC,CAACnE,eAAe,IAAKwE,YAAY,GAAGD,cAAc,GAAG,CAAE;QACvE;MACJ;IACJ;IACA;AACJ;AACA;IACIG,yBAAyBA,CAAA,EAAG;MACxB,IAAI,CAAC7C,oBAAoB,CAAC,CAAC;MAC3B,IAAI,IAAI,CAACd,IAAI,CAACW,MAAM,KAAK,CAAC,EAAE;QACxB,OAAO,CAAC;MACZ;MACA,OAAO,IAAI,CAACiD,+BAA+B,CAAC,IAAI,CAAC5D,IAAI,CAACW,MAAM,GAAG,CAAC,CAAC;IACrE;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;IACIiD,+BAA+BA,CAAC/B,KAAK,EAAE;MACnC,IAAI,CAACf,oBAAoB,CAAC,CAAC;MAC3Be,KAAK,GAAGA,KAAK,GAAG,CAAC;MACjB,IAAIgC,UAAU,GAAGX,IAAI,CAACY,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC7D,oBAAoB,GAAG,CAAC,CAAC;MAC3D,IAAI4D,UAAU,KAAK,CAAC,EAAE;QAClB,IAAI,CAAC7D,IAAI,CAAC,CAAC,CAAC,CAACX,SAAS,GAAG,IAAI,CAACW,IAAI,CAAC,CAAC,CAAC,CAACb,MAAM;QAC5C0E,UAAU,EAAE;MAChB;MACA,KAAK,IAAIT,CAAC,GAAGS,UAAU,EAAET,CAAC,IAAIvB,KAAK,EAAEuB,CAAC,EAAE,EAAE;QACtC,IAAI,CAACpD,IAAI,CAACoD,CAAC,CAAC,CAAC/D,SAAS,GAAG,IAAI,CAACW,IAAI,CAACoD,CAAC,GAAG,CAAC,CAAC,CAAC/D,SAAS,GAAG,IAAI,CAACW,IAAI,CAACoD,CAAC,CAAC,CAACjE,MAAM;MAC7E;MACA,IAAI,CAACc,oBAAoB,GAAGiD,IAAI,CAACY,GAAG,CAAC,IAAI,CAAC7D,oBAAoB,EAAE4B,KAAK,CAAC;MACtE,OAAO,IAAI,CAAC7B,IAAI,CAAC6B,KAAK,CAAC,CAACxC,SAAS;IACrC;IACA;AACJ;AACA;AACA;AACA;IACI0E,mBAAmBA,CAAA,EAAG;MAClB,IAAI,CAACjD,oBAAoB,CAAC,CAAC;MAC3B,MAAMkD,WAAW,GAAG,IAAI,CAAC5D,WAAW,GAAG,IAAI,CAACD,UAAU;MACtD,MAAM8D,iBAAiB,GAAG,IAAI,CAACN,yBAAyB,CAAC,CAAC;MAC1D,OAAOK,WAAW,GAAGC,iBAAiB,GAAG,IAAI,CAAC5D,WAAW,GAAG,IAAI,CAACC,cAAc;IACnF;IACA;AACJ;AACA;AACA;AACA;IACI4D,8CAA8CA,CAACC,UAAU,EAAE;MACvD,IAAI,CAACrD,oBAAoB,CAAC,CAAC;MAC3BqD,UAAU,GAAGA,UAAU,GAAG,CAAC;MAC3B,MAAMC,8BAA8B,GAAG,IAAI,CAACC,mCAAmC,CAACF,UAAU,CAAC;MAC3F,IAAIC,8BAA8B,KAAK,CAAC,CAAC,EAAE;QACvC,OAAO,CAAC;MACZ;MACA,OAAO,IAAI,CAACR,+BAA+B,CAACQ,8BAA8B,CAAC;IAC/E;IACAC,mCAAmCA,CAACF,UAAU,EAAE;MAC5CA,UAAU,GAAGA,UAAU,GAAG,CAAC;MAC3B;MACA,MAAM3D,GAAG,GAAG,IAAI,CAACR,IAAI;MACrB,IAAIS,GAAG,GAAG,CAAC;MACX,IAAIC,IAAI,GAAGF,GAAG,CAACG,MAAM,GAAG,CAAC;MACzB,OAAOF,GAAG,IAAIC,IAAI,EAAE;QAChB,MAAM4D,KAAK,GAAI5D,IAAI,GAAGD,GAAG,GAAI,CAAC;QAC9B,MAAM8D,SAAS,GAAID,KAAK,GAAG,CAAC,GAAI,CAAC;QACjC,MAAM1D,GAAG,GAAIH,GAAG,GAAG8D,SAAS,GAAI,CAAC;QACjC,IAAI/D,GAAG,CAACI,GAAG,CAAC,CAAC3B,eAAe,GAAGkF,UAAU,EAAE;UACvC,IAAIvD,GAAG,GAAG,CAAC,IAAIJ,GAAG,CAACG,MAAM,IAAIH,GAAG,CAACI,GAAG,GAAG,CAAC,CAAC,CAAC3B,eAAe,IAAIkF,UAAU,EAAE;YACrE,OAAOvD,GAAG;UACd,CAAC,MACI;YACDH,GAAG,GAAIG,GAAG,GAAG,CAAC,GAAI,CAAC;UACvB;QACJ,CAAC,MACI;UACDF,IAAI,GAAIE,GAAG,GAAG,CAAC,GAAI,CAAC;QACxB;MACJ;MACA,OAAO,CAAC,CAAC;IACb;IACA4D,mCAAmCA,CAACL,UAAU,EAAE;MAC5CA,UAAU,GAAGA,UAAU,GAAG,CAAC;MAC3B,MAAMC,8BAA8B,GAAG,IAAI,CAACC,mCAAmC,CAACF,UAAU,CAAC;MAC3F,MAAMM,8BAA8B,GAAGL,8BAA8B,GAAG,CAAC;MACzE,IAAIK,8BAA8B,GAAG,IAAI,CAACzE,IAAI,CAACW,MAAM,EAAE;QACnD,OAAO8D,8BAA8B;MACzC;MACA,OAAO,CAAC,CAAC;IACb;IACA;AACJ;AACA;AACA;IACIC,sCAAsCA,CAACP,UAAU,EAAE;MAC/C,IAAI,CAACrD,oBAAoB,CAAC,CAAC;MAC3BqD,UAAU,GAAGA,UAAU,GAAG,CAAC;MAC3B,OAAO,IAAI,CAACK,mCAAmC,CAACL,UAAU,CAAC;IAC/D;IACA;AACJ;AACA;AACA;AACA;AACA;IACIQ,8BAA8BA,CAACR,UAAU,EAAES,gBAAgB,GAAG,KAAK,EAAE;MACjE,IAAI,CAAC9D,oBAAoB,CAAC,CAAC;MAC3BqD,UAAU,GAAGA,UAAU,GAAG,CAAC;MAC3B,IAAIU,mBAAmB;MACvB,IAAIV,UAAU,GAAG,CAAC,EAAE;QAChBU,mBAAmB,GAAG,IAAI,CAACzE,WAAW,IAAI+D,UAAU,GAAG,CAAC,CAAC;MAC7D,CAAC,MACI;QACDU,mBAAmB,GAAG,CAAC;MAC3B;MACA,MAAMC,yBAAyB,GAAG,IAAI,CAACZ,8CAA8C,CAACC,UAAU,IAAIS,gBAAgB,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;MAC9H,OAAOC,mBAAmB,GAAGC,yBAAyB,GAAG,IAAI,CAACzE,WAAW;IAC7E;IACA;AACJ;AACA;AACA;AACA;AACA;IACI0E,gCAAgCA,CAACZ,UAAU,EAAES,gBAAgB,GAAG,KAAK,EAAE;MACnE,IAAI,CAAC9D,oBAAoB,CAAC,CAAC;MAC3BqD,UAAU,GAAGA,UAAU,GAAG,CAAC;MAC3B,MAAMU,mBAAmB,GAAG,IAAI,CAACzE,WAAW,GAAG+D,UAAU;MACzD,MAAMW,yBAAyB,GAAG,IAAI,CAACZ,8CAA8C,CAACC,UAAU,IAAIS,gBAAgB,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;MAC9H,OAAOC,mBAAmB,GAAGC,yBAAyB,GAAG,IAAI,CAACzE,WAAW;IAC7E;IACA;AACJ;AACA;IACI2E,qBAAqBA,CAAA,EAAG;MACpB,IAAI,CAAClE,oBAAoB,CAAC,CAAC;MAC3B,IAAI,IAAI,CAACZ,SAAS,KAAK,CAAC,CAAC,EAAE;QACvB,IAAId,QAAQ,GAAG,CAAC;QAChB,KAAK,IAAIgE,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAG,IAAI,CAACrD,IAAI,CAACW,MAAM,EAAEyC,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;UAClDhE,QAAQ,GAAG8D,IAAI,CAACY,GAAG,CAAC1E,QAAQ,EAAE,IAAI,CAACY,IAAI,CAACoD,CAAC,CAAC,CAAChE,QAAQ,CAAC;QACxD;QACA,IAAI,CAACc,SAAS,GAAGd,QAAQ;MAC7B;MACA,OAAO,IAAI,CAACc,SAAS;IACzB;IACA;AACJ;AACA;IACI+E,YAAYA,CAACC,cAAc,EAAE;MACzB,IAAI,CAACpE,oBAAoB,CAAC,CAAC;MAC3B,MAAMqE,WAAW,GAAG,IAAI,CAACpB,mBAAmB,CAAC,CAAC;MAC9C,OAAOmB,cAAc,GAAGC,WAAW;IACvC;IACAC,cAAcA,CAACF,cAAc,EAAE;MAC3B,IAAI,IAAI,CAAC7E,WAAW,KAAK,CAAC,EAAE;QACxB,OAAO,KAAK;MAChB;MACA,IAAI,CAACS,oBAAoB,CAAC,CAAC;MAC3B,OAAQoE,cAAc,GAAG,IAAI,CAAC7E,WAAW;IAC7C;IACAgF,iBAAiBA,CAACH,cAAc,EAAE;MAC9B,IAAI,IAAI,CAAC5E,cAAc,KAAK,CAAC,EAAE;QAC3B,OAAO,KAAK;MAChB;MACA,IAAI,CAACQ,oBAAoB,CAAC,CAAC;MAC3B,MAAMqE,WAAW,GAAG,IAAI,CAACpB,mBAAmB,CAAC,CAAC;MAC9C,OAAQmB,cAAc,IAAIC,WAAW,GAAG,IAAI,CAAC7E,cAAc;IAC/D;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;IACIgF,oCAAoCA,CAACJ,cAAc,EAAE;MACjD,IAAI,CAACpE,oBAAoB,CAAC,CAAC;MAC3BoE,cAAc,GAAGA,cAAc,GAAG,CAAC;MACnC,IAAIA,cAAc,GAAG,CAAC,EAAE;QACpB,OAAO,CAAC;MACZ;MACA,MAAMK,UAAU,GAAG,IAAI,CAACpF,UAAU,GAAG,CAAC;MACtC,MAAMV,UAAU,GAAG,IAAI,CAACW,WAAW;MACnC,IAAIoF,aAAa,GAAG,CAAC;MACrB,IAAIC,aAAa,GAAGF,UAAU;MAC9B,OAAOC,aAAa,GAAGC,aAAa,EAAE;QAClC,MAAMC,aAAa,GAAI,CAACF,aAAa,GAAGC,aAAa,IAAI,CAAC,GAAI,CAAC;QAC/D,MAAME,2BAA2B,GAAG,IAAI,CAAChB,8BAA8B,CAACe,aAAa,CAAC,GAAG,CAAC;QAC1F,IAAIR,cAAc,IAAIS,2BAA2B,GAAGlG,UAAU,EAAE;UAC5D;UACA+F,aAAa,GAAGE,aAAa,GAAG,CAAC;QACrC,CAAC,MACI,IAAIR,cAAc,IAAIS,2BAA2B,EAAE;UACpD;UACA,OAAOD,aAAa;QACxB,CAAC,MACI;UACD;UACAD,aAAa,GAAGC,aAAa;QACjC;MACJ;MACA,IAAIF,aAAa,GAAGD,UAAU,EAAE;QAC5B,OAAOA,UAAU;MACrB;MACA,OAAOC,aAAa;IACxB;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;IACII,oBAAoBA,CAACC,eAAe,EAAEC,eAAe,EAAE;MACnD,IAAI,CAAChF,oBAAoB,CAAC,CAAC;MAC3B+E,eAAe,GAAGA,eAAe,GAAG,CAAC;MACrCC,eAAe,GAAGA,eAAe,GAAG,CAAC;MACrC,MAAMrG,UAAU,GAAG,IAAI,CAACW,WAAW;MACnC;MACA;MACA,MAAM2F,eAAe,GAAG,IAAI,CAACT,oCAAoC,CAACO,eAAe,CAAC,GAAG,CAAC;MACtF,MAAMG,6BAA6B,GAAG,IAAI,CAACrB,8BAA8B,CAACoB,eAAe,CAAC,GAAG,CAAC;MAC9F,IAAIE,aAAa,GAAG,IAAI,CAAC9F,UAAU,GAAG,CAAC;MACvC;MACA,IAAI+F,eAAe,GAAG,IAAI,CAACxB,sCAAsC,CAACqB,eAAe,CAAC,GAAG,CAAC;MACtF,MAAMI,eAAe,GAAG,IAAI,CAACC,mBAAmB,CAAC,CAAC,GAAG,CAAC;MACtD,IAAIC,uBAAuB;MAC3B,IAAIC,gCAAgC;MACpC,IAAIJ,eAAe,KAAK,CAAC,CAAC,EAAE;QACxBA,eAAe,GAAGC,eAAe;QACjCG,gCAAgC,GAAGL,aAAa,GAAG,CAAC;QACpDI,uBAAuB,GAAG,CAAC;MAC/B,CAAC,MACI;QACDC,gCAAgC,GAAG,IAAI,CAACC,oCAAoC,CAACL,eAAe,CAAC,GAAG,CAAC;QACjGG,uBAAuB,GAAG,IAAI,CAACG,2BAA2B,CAACN,eAAe,CAAC,GAAG,CAAC;MACnF;MACA,IAAIO,qBAAqB,GAAGT,6BAA6B;MACzD,IAAIU,yBAAyB,GAAGD,qBAAqB;MACrD;MACA,MAAME,SAAS,GAAG,MAAM;MACxB,IAAIC,eAAe,GAAG,CAAC;MACvB,IAAIZ,6BAA6B,IAAIW,SAAS,EAAE;QAC5C;QACAC,eAAe,GAAG1D,IAAI,CAAC2D,KAAK,CAACb,6BAA6B,GAAGW,SAAS,CAAC,GAAGA,SAAS;QACnFC,eAAe,GAAG1D,IAAI,CAAC2D,KAAK,CAACD,eAAe,GAAGnH,UAAU,CAAC,GAAGA,UAAU;QACvEiH,yBAAyB,IAAIE,eAAe;MAChD;MACA,MAAME,YAAY,GAAG,EAAE;MACvB,MAAMC,cAAc,GAAGlB,eAAe,GAAG,CAACC,eAAe,GAAGD,eAAe,IAAI,CAAC;MAChF,IAAImB,kBAAkB,GAAG,CAAC,CAAC;MAC3B;MACA,KAAK,IAAI7C,UAAU,GAAG4B,eAAe,EAAE5B,UAAU,IAAI8B,aAAa,EAAE9B,UAAU,EAAE,EAAE;QAC9E,IAAI6C,kBAAkB,KAAK,CAAC,CAAC,EAAE;UAC3B,MAAMC,cAAc,GAAGR,qBAAqB;UAC5C,MAAMS,iBAAiB,GAAGT,qBAAqB,GAAGhH,UAAU;UAC5D,IAAKwH,cAAc,IAAIF,cAAc,IAAIA,cAAc,GAAGG,iBAAiB,IAAKD,cAAc,GAAGF,cAAc,EAAE;YAC7GC,kBAAkB,GAAG7C,UAAU;UACnC;QACJ;QACA;QACAsC,qBAAqB,IAAIhH,UAAU;QACnCqH,YAAY,CAAC3C,UAAU,GAAG4B,eAAe,CAAC,GAAGW,yBAAyB;QACtE;QACAA,yBAAyB,IAAIjH,UAAU;QACvC,OAAO6G,gCAAgC,KAAKnC,UAAU,EAAE;UACpD;UACAuC,yBAAyB,IAAIL,uBAAuB;UACpD;UACAI,qBAAqB,IAAIJ,uBAAuB;UAChDH,eAAe,EAAE;UACjB,IAAIA,eAAe,IAAIC,eAAe,EAAE;YACpCG,gCAAgC,GAAGL,aAAa,GAAG,CAAC;UACxD,CAAC,MACI;YACDK,gCAAgC,GAAG,IAAI,CAACC,oCAAoC,CAACL,eAAe,CAAC,GAAG,CAAC;YACjGG,uBAAuB,GAAG,IAAI,CAACG,2BAA2B,CAACN,eAAe,CAAC,GAAG,CAAC;UACnF;QACJ;QACA,IAAIO,qBAAqB,IAAIX,eAAe,EAAE;UAC1C;UACAG,aAAa,GAAG9B,UAAU;UAC1B;QACJ;MACJ;MACA,IAAI6C,kBAAkB,KAAK,CAAC,CAAC,EAAE;QAC3BA,kBAAkB,GAAGf,aAAa;MACtC;MACA,MAAMkB,2BAA2B,GAAG,IAAI,CAACxC,8BAA8B,CAACsB,aAAa,CAAC,GAAG,CAAC;MAC1F,IAAImB,gCAAgC,GAAGrB,eAAe;MACtD,IAAIsB,8BAA8B,GAAGpB,aAAa;MAClD,IAAImB,gCAAgC,GAAGC,8BAA8B,EAAE;QACnE,IAAIrB,6BAA6B,GAAGH,eAAe,EAAE;UACjDuB,gCAAgC,EAAE;QACtC;MACJ;MACA,IAAIA,gCAAgC,GAAGC,8BAA8B,EAAE;QACnE,IAAIF,2BAA2B,GAAG1H,UAAU,GAAGqG,eAAe,EAAE;UAC5DuB,8BAA8B,EAAE;QACpC;MACJ;MACA,OAAO;QACHT,eAAe,EAAEA,eAAe;QAChCb,eAAe,EAAEA,eAAe;QAChCE,aAAa,EAAEA,aAAa;QAC5BqB,sBAAsB,EAAER,YAAY;QACpCE,kBAAkB,EAAEA,kBAAkB;QACtCI,gCAAgC,EAAEA,gCAAgC;QAClEC,8BAA8B,EAAEA,8BAA8B;QAC9D5H,UAAU,EAAE,IAAI,CAACW;MACrB,CAAC;IACL;IACAmH,mCAAmCA,CAACrB,eAAe,EAAE;MACjD,IAAI,CAACpF,oBAAoB,CAAC,CAAC;MAC3BoF,eAAe,GAAGA,eAAe,GAAG,CAAC;MACrC,MAAMjH,eAAe,GAAG,IAAI,CAACsH,oCAAoC,CAACL,eAAe,CAAC;MAClF,IAAIrB,mBAAmB;MACvB,IAAI5F,eAAe,IAAI,CAAC,EAAE;QACtB4F,mBAAmB,GAAG,IAAI,CAACzE,WAAW,GAAGnB,eAAe;MAC5D,CAAC,MACI;QACD4F,mBAAmB,GAAG,CAAC;MAC3B;MACA,IAAIC,yBAAyB;MAC7B,IAAIoB,eAAe,GAAG,CAAC,EAAE;QACrBpB,yBAAyB,GAAG,IAAI,CAAClB,+BAA+B,CAACsC,eAAe,GAAG,CAAC,CAAC;MACzF,CAAC,MACI;QACDpB,yBAAyB,GAAG,CAAC;MACjC;MACA,OAAOD,mBAAmB,GAAGC,yBAAyB,GAAG,IAAI,CAACzE,WAAW;IAC7E;IACAmH,0CAA0CA,CAACtC,cAAc,EAAE;MACvD,IAAI,CAACpE,oBAAoB,CAAC,CAAC;MAC3BoE,cAAc,GAAGA,cAAc,GAAG,CAAC;MACnC,IAAIuC,kBAAkB,GAAG,CAAC;MAC1B,IAAIC,kBAAkB,GAAG,IAAI,CAACtB,mBAAmB,CAAC,CAAC,GAAG,CAAC;MACvD,IAAIsB,kBAAkB,GAAG,CAAC,EAAE;QACxB,OAAO,CAAC,CAAC;MACb;MACA;MACA,MAAMC,2BAA2B,GAAG,IAAI,CAACJ,mCAAmC,CAACG,kBAAkB,CAAC;MAChG,MAAME,mBAAmB,GAAG,IAAI,CAACpB,2BAA2B,CAACkB,kBAAkB,CAAC;MAChF,IAAIxC,cAAc,IAAIyC,2BAA2B,GAAGC,mBAAmB,EAAE;QACrE,OAAO,CAAC,CAAC;MACb;MACA,OAAOH,kBAAkB,GAAGC,kBAAkB,EAAE;QAC5C,MAAMG,kBAAkB,GAAG3E,IAAI,CAAC2D,KAAK,CAAC,CAACY,kBAAkB,GAAGC,kBAAkB,IAAI,CAAC,CAAC;QACpF,MAAMI,2BAA2B,GAAG,IAAI,CAACP,mCAAmC,CAACM,kBAAkB,CAAC;QAChG,MAAME,mBAAmB,GAAG,IAAI,CAACvB,2BAA2B,CAACqB,kBAAkB,CAAC;QAChF,IAAI3C,cAAc,IAAI4C,2BAA2B,GAAGC,mBAAmB,EAAE;UACrE;UACAN,kBAAkB,GAAGI,kBAAkB,GAAG,CAAC;QAC/C,CAAC,MACI,IAAI3C,cAAc,IAAI4C,2BAA2B,EAAE;UACpD;UACA,OAAOD,kBAAkB;QAC7B,CAAC,MACI;UACD;UACAH,kBAAkB,GAAGG,kBAAkB;QAC3C;MACJ;MACA,OAAOJ,kBAAkB;IAC7B;IACA;AACJ;AACA;AACA;AACA;AACA;IACIO,6BAA6BA,CAAC9C,cAAc,EAAE;MAC1C,IAAI,CAACpE,oBAAoB,CAAC,CAAC;MAC3BoE,cAAc,GAAGA,cAAc,GAAG,CAAC;MACnC,MAAM+C,cAAc,GAAG,IAAI,CAACT,0CAA0C,CAACtC,cAAc,CAAC;MACtF,IAAI+C,cAAc,GAAG,CAAC,EAAE;QACpB,OAAO,IAAI;MACf;MACA,IAAIA,cAAc,IAAI,IAAI,CAAC7B,mBAAmB,CAAC,CAAC,EAAE;QAC9C,OAAO,IAAI;MACf;MACA,MAAM8B,YAAY,GAAG,IAAI,CAACX,mCAAmC,CAACU,cAAc,CAAC;MAC7E,IAAIC,YAAY,GAAGhD,cAAc,EAAE;QAC/B,OAAO,IAAI;MACf;MACA,MAAMiD,eAAe,GAAG,IAAI,CAAC3B,2BAA2B,CAACyB,cAAc,CAAC;MACxE,MAAMG,WAAW,GAAG,IAAI,CAACC,uBAAuB,CAACJ,cAAc,CAAC;MAChE,MAAMK,wBAAwB,GAAG,IAAI,CAAC/B,oCAAoC,CAAC0B,cAAc,CAAC;MAC1F,OAAO;QACHjJ,EAAE,EAAEoJ,WAAW;QACfnJ,eAAe,EAAEqJ,wBAAwB;QACzCpD,cAAc,EAAEgD,YAAY;QAC5B/I,MAAM,EAAEgJ;MACZ,CAAC;IACL;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;IACII,yBAAyBA,CAAC1C,eAAe,EAAEC,eAAe,EAAE;MACxD,IAAI,CAAChF,oBAAoB,CAAC,CAAC;MAC3B+E,eAAe,GAAGA,eAAe,GAAG,CAAC;MACrCC,eAAe,GAAGA,eAAe,GAAG,CAAC;MACrC,MAAMjC,UAAU,GAAG,IAAI,CAAC2D,0CAA0C,CAAC3B,eAAe,CAAC;MACnF,MAAM2C,QAAQ,GAAG,IAAI,CAACpC,mBAAmB,CAAC,CAAC,GAAG,CAAC;MAC/C,IAAIvC,UAAU,GAAG,CAAC,EAAE;QAChB,OAAO,EAAE;MACb;MACA,MAAMrB,MAAM,GAAG,EAAE;MACjB,KAAK,IAAIY,CAAC,GAAGS,UAAU,EAAET,CAAC,IAAIoF,QAAQ,EAAEpF,CAAC,EAAE,EAAE;QACzC,MAAMqF,GAAG,GAAG,IAAI,CAAClB,mCAAmC,CAACnE,CAAC,CAAC;QACvD,MAAMjE,MAAM,GAAG,IAAI,CAACqH,2BAA2B,CAACpD,CAAC,CAAC;QAClD,IAAIqF,GAAG,IAAI3C,eAAe,EAAE;UACxB;QACJ;QACAtD,MAAM,CAACnE,IAAI,CAAC;UACRW,EAAE,EAAE,IAAI,CAACqJ,uBAAuB,CAACjF,CAAC,CAAC;UACnCnE,eAAe,EAAE,IAAI,CAACsH,oCAAoC,CAACnD,CAAC,CAAC;UAC7D8B,cAAc,EAAEuD,GAAG;UACnBtJ,MAAM,EAAEA;QACZ,CAAC,CAAC;MACN;MACA,OAAOqD,MAAM;IACjB;IACA;AACJ;AACA;IACIkG,cAAcA,CAAA,EAAG;MACb,IAAI,CAAC5H,oBAAoB,CAAC,CAAC;MAC3B,OAAO,IAAI,CAACd,IAAI,CAAC2I,KAAK,CAAC,CAAC,CAAC;IAC7B;IACA;AACJ;AACA;IACIvC,mBAAmBA,CAAA,EAAG;MAClB,IAAI,CAACtF,oBAAoB,CAAC,CAAC;MAC3B,OAAO,IAAI,CAACd,IAAI,CAACW,MAAM;IAC3B;IACA;AACJ;AACA;AACA;AACA;AACA;IACI0H,uBAAuBA,CAACxG,KAAK,EAAE;MAC3B,IAAI,CAACf,oBAAoB,CAAC,CAAC;MAC3Be,KAAK,GAAGA,KAAK,GAAG,CAAC;MACjB,OAAO,IAAI,CAAC7B,IAAI,CAAC6B,KAAK,CAAC,CAAC7C,EAAE;IAC9B;IACA;AACJ;AACA;AACA;AACA;AACA;IACIuH,oCAAoCA,CAAC1E,KAAK,EAAE;MACxC,IAAI,CAACf,oBAAoB,CAAC,CAAC;MAC3Be,KAAK,GAAGA,KAAK,GAAG,CAAC;MACjB,OAAO,IAAI,CAAC7B,IAAI,CAAC6B,KAAK,CAAC,CAAC5C,eAAe;IAC3C;IACA;AACJ;AACA;AACA;AACA;AACA;IACIuH,2BAA2BA,CAAC3E,KAAK,EAAE;MAC/B,IAAI,CAACf,oBAAoB,CAAC,CAAC;MAC3Be,KAAK,GAAGA,KAAK,GAAG,CAAC;MACjB,OAAO,IAAI,CAAC7B,IAAI,CAAC6B,KAAK,CAAC,CAAC1C,MAAM;IAClC;EACJ;EAAC,OAtsBYG,WAAW;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}