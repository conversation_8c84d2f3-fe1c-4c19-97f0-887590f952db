{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { OffsetRange } from '../../../core/offsetRange.js';\nimport { DiffAlgorithmResult, InfiniteTimeout, SequenceDiff } from './diffAlgorithm.js';\n/**\n * An O(ND) diff algorithm that has a quadratic space worst-case complexity.\n*/\nexport class MyersDiffAlgorithm {\n  compute(seq1, seq2, timeout = InfiniteTimeout.instance) {\n    // These are common special cases.\n    // The early return improves performance dramatically.\n    if (seq1.length === 0 || seq2.length === 0) {\n      return DiffAlgorithmResult.trivial(seq1, seq2);\n    }\n    const seqX = seq1; // Text on the x axis\n    const seqY = seq2; // Text on the y axis\n    function getXAfterSnake(x, y) {\n      while (x < seqX.length && y < seqY.length && seqX.getElement(x) === seqY.getElement(y)) {\n        x++;\n        y++;\n      }\n      return x;\n    }\n    let d = 0;\n    // V[k]: X value of longest d-line that ends in diagonal k.\n    // d-line: path from (0,0) to (x,y) that uses exactly d non-diagonals.\n    // diagonal k: Set of points (x,y) with x-y = k.\n    // k=1 -> (1,0),(2,1)\n    const V = new FastInt32Array();\n    V.set(0, getXAfterSnake(0, 0));\n    const paths = new FastArrayNegativeIndices();\n    paths.set(0, V.get(0) === 0 ? null : new SnakePath(null, 0, 0, V.get(0)));\n    let k = 0;\n    loop: while (true) {\n      d++;\n      if (!timeout.isValid()) {\n        return DiffAlgorithmResult.trivialTimedOut(seqX, seqY);\n      }\n      // The paper has `for (k = -d; k <= d; k += 2)`, but we can ignore diagonals that cannot influence the result.\n      const lowerBound = -Math.min(d, seqY.length + d % 2);\n      const upperBound = Math.min(d, seqX.length + d % 2);\n      for (k = lowerBound; k <= upperBound; k += 2) {\n        let step = 0;\n        // We can use the X values of (d-1)-lines to compute X value of the longest d-lines.\n        const maxXofDLineTop = k === upperBound ? -1 : V.get(k + 1); // We take a vertical non-diagonal (add a symbol in seqX)\n        const maxXofDLineLeft = k === lowerBound ? -1 : V.get(k - 1) + 1; // We take a horizontal non-diagonal (+1 x) (delete a symbol in seqX)\n        step++;\n        const x = Math.min(Math.max(maxXofDLineTop, maxXofDLineLeft), seqX.length);\n        const y = x - k;\n        step++;\n        if (x > seqX.length || y > seqY.length) {\n          // This diagonal is irrelevant for the result.\n          // TODO: Don't pay the cost for this in the next iteration.\n          continue;\n        }\n        const newMaxX = getXAfterSnake(x, y);\n        V.set(k, newMaxX);\n        const lastPath = x === maxXofDLineTop ? paths.get(k + 1) : paths.get(k - 1);\n        paths.set(k, newMaxX !== x ? new SnakePath(lastPath, x, y, newMaxX - x) : lastPath);\n        if (V.get(k) === seqX.length && V.get(k) - k === seqY.length) {\n          break loop;\n        }\n      }\n    }\n    let path = paths.get(k);\n    const result = [];\n    let lastAligningPosS1 = seqX.length;\n    let lastAligningPosS2 = seqY.length;\n    while (true) {\n      const endX = path ? path.x + path.length : 0;\n      const endY = path ? path.y + path.length : 0;\n      if (endX !== lastAligningPosS1 || endY !== lastAligningPosS2) {\n        result.push(new SequenceDiff(new OffsetRange(endX, lastAligningPosS1), new OffsetRange(endY, lastAligningPosS2)));\n      }\n      if (!path) {\n        break;\n      }\n      lastAligningPosS1 = path.x;\n      lastAligningPosS2 = path.y;\n      path = path.prev;\n    }\n    result.reverse();\n    return new DiffAlgorithmResult(result, false);\n  }\n}\nclass SnakePath {\n  constructor(prev, x, y, length) {\n    this.prev = prev;\n    this.x = x;\n    this.y = y;\n    this.length = length;\n  }\n}\n/**\n * An array that supports fast negative indices.\n*/\nclass FastInt32Array {\n  constructor() {\n    this.positiveArr = new Int32Array(10);\n    this.negativeArr = new Int32Array(10);\n  }\n  get(idx) {\n    if (idx < 0) {\n      idx = -idx - 1;\n      return this.negativeArr[idx];\n    } else {\n      return this.positiveArr[idx];\n    }\n  }\n  set(idx, value) {\n    if (idx < 0) {\n      idx = -idx - 1;\n      if (idx >= this.negativeArr.length) {\n        const arr = this.negativeArr;\n        this.negativeArr = new Int32Array(arr.length * 2);\n        this.negativeArr.set(arr);\n      }\n      this.negativeArr[idx] = value;\n    } else {\n      if (idx >= this.positiveArr.length) {\n        const arr = this.positiveArr;\n        this.positiveArr = new Int32Array(arr.length * 2);\n        this.positiveArr.set(arr);\n      }\n      this.positiveArr[idx] = value;\n    }\n  }\n}\n/**\n * An array that supports fast negative indices.\n*/\nclass FastArrayNegativeIndices {\n  constructor() {\n    this.positiveArr = [];\n    this.negativeArr = [];\n  }\n  get(idx) {\n    if (idx < 0) {\n      idx = -idx - 1;\n      return this.negativeArr[idx];\n    } else {\n      return this.positiveArr[idx];\n    }\n  }\n  set(idx, value) {\n    if (idx < 0) {\n      idx = -idx - 1;\n      this.negativeArr[idx] = value;\n    } else {\n      this.positiveArr[idx] = value;\n    }\n  }\n}", "map": {"version": 3, "names": ["OffsetRange", "DiffAlgorithmResult", "InfiniteTimeout", "SequenceDiff", "MyersDiffAlgorithm", "compute", "seq1", "seq2", "timeout", "instance", "length", "trivial", "seqX", "seqY", "getXAfterSnake", "x", "y", "getElement", "d", "V", "FastInt32Array", "set", "paths", "FastArrayNegativeIndices", "get", "<PERSON><PERSON><PERSON>", "k", "loop", "<PERSON><PERSON><PERSON><PERSON>", "trivialTimedOut", "lowerBound", "Math", "min", "upperBound", "step", "maxXofDLineTop", "maxXofDLineLeft", "max", "newMaxX", "last<PERSON><PERSON>", "path", "result", "lastAligningPosS1", "lastAligningPosS2", "endX", "endY", "push", "prev", "reverse", "constructor", "positiveArr", "Int32Array", "negativeArr", "idx", "value", "arr"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/diff/defaultLinesDiffComputer/algorithms/myersDiffAlgorithm.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { OffsetRange } from '../../../core/offsetRange.js';\nimport { DiffAlgorithmResult, InfiniteTimeout, SequenceDiff } from './diffAlgorithm.js';\n/**\n * An O(ND) diff algorithm that has a quadratic space worst-case complexity.\n*/\nexport class MyersDiffAlgorithm {\n    compute(seq1, seq2, timeout = InfiniteTimeout.instance) {\n        // These are common special cases.\n        // The early return improves performance dramatically.\n        if (seq1.length === 0 || seq2.length === 0) {\n            return DiffAlgorithmResult.trivial(seq1, seq2);\n        }\n        const seqX = seq1; // Text on the x axis\n        const seqY = seq2; // Text on the y axis\n        function getXAfterSnake(x, y) {\n            while (x < seqX.length && y < seqY.length && seqX.getElement(x) === seqY.getElement(y)) {\n                x++;\n                y++;\n            }\n            return x;\n        }\n        let d = 0;\n        // V[k]: X value of longest d-line that ends in diagonal k.\n        // d-line: path from (0,0) to (x,y) that uses exactly d non-diagonals.\n        // diagonal k: Set of points (x,y) with x-y = k.\n        // k=1 -> (1,0),(2,1)\n        const V = new FastInt32Array();\n        V.set(0, getXAfterSnake(0, 0));\n        const paths = new FastArrayNegativeIndices();\n        paths.set(0, V.get(0) === 0 ? null : new SnakePath(null, 0, 0, V.get(0)));\n        let k = 0;\n        loop: while (true) {\n            d++;\n            if (!timeout.isValid()) {\n                return DiffAlgorithmResult.trivialTimedOut(seqX, seqY);\n            }\n            // The paper has `for (k = -d; k <= d; k += 2)`, but we can ignore diagonals that cannot influence the result.\n            const lowerBound = -Math.min(d, seqY.length + (d % 2));\n            const upperBound = Math.min(d, seqX.length + (d % 2));\n            for (k = lowerBound; k <= upperBound; k += 2) {\n                let step = 0;\n                // We can use the X values of (d-1)-lines to compute X value of the longest d-lines.\n                const maxXofDLineTop = k === upperBound ? -1 : V.get(k + 1); // We take a vertical non-diagonal (add a symbol in seqX)\n                const maxXofDLineLeft = k === lowerBound ? -1 : V.get(k - 1) + 1; // We take a horizontal non-diagonal (+1 x) (delete a symbol in seqX)\n                step++;\n                const x = Math.min(Math.max(maxXofDLineTop, maxXofDLineLeft), seqX.length);\n                const y = x - k;\n                step++;\n                if (x > seqX.length || y > seqY.length) {\n                    // This diagonal is irrelevant for the result.\n                    // TODO: Don't pay the cost for this in the next iteration.\n                    continue;\n                }\n                const newMaxX = getXAfterSnake(x, y);\n                V.set(k, newMaxX);\n                const lastPath = x === maxXofDLineTop ? paths.get(k + 1) : paths.get(k - 1);\n                paths.set(k, newMaxX !== x ? new SnakePath(lastPath, x, y, newMaxX - x) : lastPath);\n                if (V.get(k) === seqX.length && V.get(k) - k === seqY.length) {\n                    break loop;\n                }\n            }\n        }\n        let path = paths.get(k);\n        const result = [];\n        let lastAligningPosS1 = seqX.length;\n        let lastAligningPosS2 = seqY.length;\n        while (true) {\n            const endX = path ? path.x + path.length : 0;\n            const endY = path ? path.y + path.length : 0;\n            if (endX !== lastAligningPosS1 || endY !== lastAligningPosS2) {\n                result.push(new SequenceDiff(new OffsetRange(endX, lastAligningPosS1), new OffsetRange(endY, lastAligningPosS2)));\n            }\n            if (!path) {\n                break;\n            }\n            lastAligningPosS1 = path.x;\n            lastAligningPosS2 = path.y;\n            path = path.prev;\n        }\n        result.reverse();\n        return new DiffAlgorithmResult(result, false);\n    }\n}\nclass SnakePath {\n    constructor(prev, x, y, length) {\n        this.prev = prev;\n        this.x = x;\n        this.y = y;\n        this.length = length;\n    }\n}\n/**\n * An array that supports fast negative indices.\n*/\nclass FastInt32Array {\n    constructor() {\n        this.positiveArr = new Int32Array(10);\n        this.negativeArr = new Int32Array(10);\n    }\n    get(idx) {\n        if (idx < 0) {\n            idx = -idx - 1;\n            return this.negativeArr[idx];\n        }\n        else {\n            return this.positiveArr[idx];\n        }\n    }\n    set(idx, value) {\n        if (idx < 0) {\n            idx = -idx - 1;\n            if (idx >= this.negativeArr.length) {\n                const arr = this.negativeArr;\n                this.negativeArr = new Int32Array(arr.length * 2);\n                this.negativeArr.set(arr);\n            }\n            this.negativeArr[idx] = value;\n        }\n        else {\n            if (idx >= this.positiveArr.length) {\n                const arr = this.positiveArr;\n                this.positiveArr = new Int32Array(arr.length * 2);\n                this.positiveArr.set(arr);\n            }\n            this.positiveArr[idx] = value;\n        }\n    }\n}\n/**\n * An array that supports fast negative indices.\n*/\nclass FastArrayNegativeIndices {\n    constructor() {\n        this.positiveArr = [];\n        this.negativeArr = [];\n    }\n    get(idx) {\n        if (idx < 0) {\n            idx = -idx - 1;\n            return this.negativeArr[idx];\n        }\n        else {\n            return this.positiveArr[idx];\n        }\n    }\n    set(idx, value) {\n        if (idx < 0) {\n            idx = -idx - 1;\n            this.negativeArr[idx] = value;\n        }\n        else {\n            this.positiveArr[idx] = value;\n        }\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,WAAW,QAAQ,8BAA8B;AAC1D,SAASC,mBAAmB,EAAEC,eAAe,EAAEC,YAAY,QAAQ,oBAAoB;AACvF;AACA;AACA;AACA,OAAO,MAAMC,kBAAkB,CAAC;EAC5BC,OAAOA,CAACC,IAAI,EAAEC,IAAI,EAAEC,OAAO,GAAGN,eAAe,CAACO,QAAQ,EAAE;IACpD;IACA;IACA,IAAIH,IAAI,CAACI,MAAM,KAAK,CAAC,IAAIH,IAAI,CAACG,MAAM,KAAK,CAAC,EAAE;MACxC,OAAOT,mBAAmB,CAACU,OAAO,CAACL,IAAI,EAAEC,IAAI,CAAC;IAClD;IACA,MAAMK,IAAI,GAAGN,IAAI,CAAC,CAAC;IACnB,MAAMO,IAAI,GAAGN,IAAI,CAAC,CAAC;IACnB,SAASO,cAAcA,CAACC,CAAC,EAAEC,CAAC,EAAE;MAC1B,OAAOD,CAAC,GAAGH,IAAI,CAACF,MAAM,IAAIM,CAAC,GAAGH,IAAI,CAACH,MAAM,IAAIE,IAAI,CAACK,UAAU,CAACF,CAAC,CAAC,KAAKF,IAAI,CAACI,UAAU,CAACD,CAAC,CAAC,EAAE;QACpFD,CAAC,EAAE;QACHC,CAAC,EAAE;MACP;MACA,OAAOD,CAAC;IACZ;IACA,IAAIG,CAAC,GAAG,CAAC;IACT;IACA;IACA;IACA;IACA,MAAMC,CAAC,GAAG,IAAIC,cAAc,CAAC,CAAC;IAC9BD,CAAC,CAACE,GAAG,CAAC,CAAC,EAAEP,cAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC9B,MAAMQ,KAAK,GAAG,IAAIC,wBAAwB,CAAC,CAAC;IAC5CD,KAAK,CAACD,GAAG,CAAC,CAAC,EAAEF,CAAC,CAACK,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,IAAIC,SAAS,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAEN,CAAC,CAACK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACzE,IAAIE,CAAC,GAAG,CAAC;IACTC,IAAI,EAAE,OAAO,IAAI,EAAE;MACfT,CAAC,EAAE;MACH,IAAI,CAACV,OAAO,CAACoB,OAAO,CAAC,CAAC,EAAE;QACpB,OAAO3B,mBAAmB,CAAC4B,eAAe,CAACjB,IAAI,EAAEC,IAAI,CAAC;MAC1D;MACA;MACA,MAAMiB,UAAU,GAAG,CAACC,IAAI,CAACC,GAAG,CAACd,CAAC,EAAEL,IAAI,CAACH,MAAM,GAAIQ,CAAC,GAAG,CAAE,CAAC;MACtD,MAAMe,UAAU,GAAGF,IAAI,CAACC,GAAG,CAACd,CAAC,EAAEN,IAAI,CAACF,MAAM,GAAIQ,CAAC,GAAG,CAAE,CAAC;MACrD,KAAKQ,CAAC,GAAGI,UAAU,EAAEJ,CAAC,IAAIO,UAAU,EAAEP,CAAC,IAAI,CAAC,EAAE;QAC1C,IAAIQ,IAAI,GAAG,CAAC;QACZ;QACA,MAAMC,cAAc,GAAGT,CAAC,KAAKO,UAAU,GAAG,CAAC,CAAC,GAAGd,CAAC,CAACK,GAAG,CAACE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC7D,MAAMU,eAAe,GAAGV,CAAC,KAAKI,UAAU,GAAG,CAAC,CAAC,GAAGX,CAAC,CAACK,GAAG,CAACE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAClEQ,IAAI,EAAE;QACN,MAAMnB,CAAC,GAAGgB,IAAI,CAACC,GAAG,CAACD,IAAI,CAACM,GAAG,CAACF,cAAc,EAAEC,eAAe,CAAC,EAAExB,IAAI,CAACF,MAAM,CAAC;QAC1E,MAAMM,CAAC,GAAGD,CAAC,GAAGW,CAAC;QACfQ,IAAI,EAAE;QACN,IAAInB,CAAC,GAAGH,IAAI,CAACF,MAAM,IAAIM,CAAC,GAAGH,IAAI,CAACH,MAAM,EAAE;UACpC;UACA;UACA;QACJ;QACA,MAAM4B,OAAO,GAAGxB,cAAc,CAACC,CAAC,EAAEC,CAAC,CAAC;QACpCG,CAAC,CAACE,GAAG,CAACK,CAAC,EAAEY,OAAO,CAAC;QACjB,MAAMC,QAAQ,GAAGxB,CAAC,KAAKoB,cAAc,GAAGb,KAAK,CAACE,GAAG,CAACE,CAAC,GAAG,CAAC,CAAC,GAAGJ,KAAK,CAACE,GAAG,CAACE,CAAC,GAAG,CAAC,CAAC;QAC3EJ,KAAK,CAACD,GAAG,CAACK,CAAC,EAAEY,OAAO,KAAKvB,CAAC,GAAG,IAAIU,SAAS,CAACc,QAAQ,EAAExB,CAAC,EAAEC,CAAC,EAAEsB,OAAO,GAAGvB,CAAC,CAAC,GAAGwB,QAAQ,CAAC;QACnF,IAAIpB,CAAC,CAACK,GAAG,CAACE,CAAC,CAAC,KAAKd,IAAI,CAACF,MAAM,IAAIS,CAAC,CAACK,GAAG,CAACE,CAAC,CAAC,GAAGA,CAAC,KAAKb,IAAI,CAACH,MAAM,EAAE;UAC1D,MAAMiB,IAAI;QACd;MACJ;IACJ;IACA,IAAIa,IAAI,GAAGlB,KAAK,CAACE,GAAG,CAACE,CAAC,CAAC;IACvB,MAAMe,MAAM,GAAG,EAAE;IACjB,IAAIC,iBAAiB,GAAG9B,IAAI,CAACF,MAAM;IACnC,IAAIiC,iBAAiB,GAAG9B,IAAI,CAACH,MAAM;IACnC,OAAO,IAAI,EAAE;MACT,MAAMkC,IAAI,GAAGJ,IAAI,GAAGA,IAAI,CAACzB,CAAC,GAAGyB,IAAI,CAAC9B,MAAM,GAAG,CAAC;MAC5C,MAAMmC,IAAI,GAAGL,IAAI,GAAGA,IAAI,CAACxB,CAAC,GAAGwB,IAAI,CAAC9B,MAAM,GAAG,CAAC;MAC5C,IAAIkC,IAAI,KAAKF,iBAAiB,IAAIG,IAAI,KAAKF,iBAAiB,EAAE;QAC1DF,MAAM,CAACK,IAAI,CAAC,IAAI3C,YAAY,CAAC,IAAIH,WAAW,CAAC4C,IAAI,EAAEF,iBAAiB,CAAC,EAAE,IAAI1C,WAAW,CAAC6C,IAAI,EAAEF,iBAAiB,CAAC,CAAC,CAAC;MACrH;MACA,IAAI,CAACH,IAAI,EAAE;QACP;MACJ;MACAE,iBAAiB,GAAGF,IAAI,CAACzB,CAAC;MAC1B4B,iBAAiB,GAAGH,IAAI,CAACxB,CAAC;MAC1BwB,IAAI,GAAGA,IAAI,CAACO,IAAI;IACpB;IACAN,MAAM,CAACO,OAAO,CAAC,CAAC;IAChB,OAAO,IAAI/C,mBAAmB,CAACwC,MAAM,EAAE,KAAK,CAAC;EACjD;AACJ;AACA,MAAMhB,SAAS,CAAC;EACZwB,WAAWA,CAACF,IAAI,EAAEhC,CAAC,EAAEC,CAAC,EAAEN,MAAM,EAAE;IAC5B,IAAI,CAACqC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAAChC,CAAC,GAAGA,CAAC;IACV,IAAI,CAACC,CAAC,GAAGA,CAAC;IACV,IAAI,CAACN,MAAM,GAAGA,MAAM;EACxB;AACJ;AACA;AACA;AACA;AACA,MAAMU,cAAc,CAAC;EACjB6B,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,WAAW,GAAG,IAAIC,UAAU,CAAC,EAAE,CAAC;IACrC,IAAI,CAACC,WAAW,GAAG,IAAID,UAAU,CAAC,EAAE,CAAC;EACzC;EACA3B,GAAGA,CAAC6B,GAAG,EAAE;IACL,IAAIA,GAAG,GAAG,CAAC,EAAE;MACTA,GAAG,GAAG,CAACA,GAAG,GAAG,CAAC;MACd,OAAO,IAAI,CAACD,WAAW,CAACC,GAAG,CAAC;IAChC,CAAC,MACI;MACD,OAAO,IAAI,CAACH,WAAW,CAACG,GAAG,CAAC;IAChC;EACJ;EACAhC,GAAGA,CAACgC,GAAG,EAAEC,KAAK,EAAE;IACZ,IAAID,GAAG,GAAG,CAAC,EAAE;MACTA,GAAG,GAAG,CAACA,GAAG,GAAG,CAAC;MACd,IAAIA,GAAG,IAAI,IAAI,CAACD,WAAW,CAAC1C,MAAM,EAAE;QAChC,MAAM6C,GAAG,GAAG,IAAI,CAACH,WAAW;QAC5B,IAAI,CAACA,WAAW,GAAG,IAAID,UAAU,CAACI,GAAG,CAAC7C,MAAM,GAAG,CAAC,CAAC;QACjD,IAAI,CAAC0C,WAAW,CAAC/B,GAAG,CAACkC,GAAG,CAAC;MAC7B;MACA,IAAI,CAACH,WAAW,CAACC,GAAG,CAAC,GAAGC,KAAK;IACjC,CAAC,MACI;MACD,IAAID,GAAG,IAAI,IAAI,CAACH,WAAW,CAACxC,MAAM,EAAE;QAChC,MAAM6C,GAAG,GAAG,IAAI,CAACL,WAAW;QAC5B,IAAI,CAACA,WAAW,GAAG,IAAIC,UAAU,CAACI,GAAG,CAAC7C,MAAM,GAAG,CAAC,CAAC;QACjD,IAAI,CAACwC,WAAW,CAAC7B,GAAG,CAACkC,GAAG,CAAC;MAC7B;MACA,IAAI,CAACL,WAAW,CAACG,GAAG,CAAC,GAAGC,KAAK;IACjC;EACJ;AACJ;AACA;AACA;AACA;AACA,MAAM/B,wBAAwB,CAAC;EAC3B0B,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,WAAW,GAAG,EAAE;IACrB,IAAI,CAACE,WAAW,GAAG,EAAE;EACzB;EACA5B,GAAGA,CAAC6B,GAAG,EAAE;IACL,IAAIA,GAAG,GAAG,CAAC,EAAE;MACTA,GAAG,GAAG,CAACA,GAAG,GAAG,CAAC;MACd,OAAO,IAAI,CAACD,WAAW,CAACC,GAAG,CAAC;IAChC,CAAC,MACI;MACD,OAAO,IAAI,CAACH,WAAW,CAACG,GAAG,CAAC;IAChC;EACJ;EACAhC,GAAGA,CAACgC,GAAG,EAAEC,KAAK,EAAE;IACZ,IAAID,GAAG,GAAG,CAAC,EAAE;MACTA,GAAG,GAAG,CAACA,GAAG,GAAG,CAAC;MACd,IAAI,CAACD,WAAW,CAACC,GAAG,CAAC,GAAGC,KAAK;IACjC,CAAC,MACI;MACD,IAAI,CAACJ,WAAW,CAACG,GAAG,CAAC,GAAGC,KAAK;IACjC;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}