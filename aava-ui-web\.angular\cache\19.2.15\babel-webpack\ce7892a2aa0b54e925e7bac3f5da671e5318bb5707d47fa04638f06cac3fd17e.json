{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport {};", "map": {"version": 3, "names": [], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/services/textModelSync/textModelSync.protocol.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport {};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}