{"ast": null, "code": "import _asyncToGenerator from \"C:/console/aava-ui-web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = this && this.__param || function (paramIndex, decorator) {\n  return function (target, key) {\n    decorator(target, key, paramIndex);\n  };\n};\nvar MarkdownRenderer_1;\nimport { renderMarkdown } from '../../../../../base/browser/markdownRenderer.js';\nimport { createTrustedTypesPolicy } from '../../../../../base/browser/trustedTypes.js';\nimport { onUnexpectedError } from '../../../../../base/common/errors.js';\nimport { Emitter } from '../../../../../base/common/event.js';\nimport { DisposableStore } from '../../../../../base/common/lifecycle.js';\nimport './renderedMarkdown.css';\nimport { applyFontInfo } from '../../../config/domFontInfo.js';\nimport { ILanguageService } from '../../../../common/languages/language.js';\nimport { PLAINTEXT_LANGUAGE_ID } from '../../../../common/languages/modesRegistry.js';\nimport { tokenizeToString } from '../../../../common/languages/textToHtmlTokenizer.js';\nimport { IOpenerService } from '../../../../../platform/opener/common/opener.js';\n/**\n * Markdown renderer that can render codeblocks with the editor mechanics. This\n * renderer should always be preferred.\n */\nlet MarkdownRenderer = class MarkdownRenderer {\n  static {\n    MarkdownRenderer_1 = this;\n  }\n  static {\n    this._ttpTokenizer = createTrustedTypesPolicy('tokenizeToString', {\n      createHTML(html) {\n        return html;\n      }\n    });\n  }\n  constructor(_options, _languageService, _openerService) {\n    this._options = _options;\n    this._languageService = _languageService;\n    this._openerService = _openerService;\n    this._onDidRenderAsync = new Emitter();\n    this.onDidRenderAsync = this._onDidRenderAsync.event;\n  }\n  dispose() {\n    this._onDidRenderAsync.dispose();\n  }\n  render(markdown, options, markedOptions) {\n    if (!markdown) {\n      const element = document.createElement('span');\n      return {\n        element,\n        dispose: () => {}\n      };\n    }\n    const disposables = new DisposableStore();\n    const rendered = disposables.add(renderMarkdown(markdown, {\n      ...this._getRenderOptions(markdown, disposables),\n      ...options\n    }, markedOptions));\n    rendered.element.classList.add('rendered-markdown');\n    return {\n      element: rendered.element,\n      dispose: () => disposables.dispose()\n    };\n  }\n  _getRenderOptions(markdown, disposables) {\n    var _this = this;\n    return {\n      codeBlockRenderer: function () {\n        var _ref = _asyncToGenerator(function* (languageAlias, value) {\n          // In markdown,\n          // it is possible that we stumble upon language aliases (e.g.js instead of javascript)\n          // it is possible no alias is given in which case we fall back to the current editor lang\n          let languageId;\n          if (languageAlias) {\n            languageId = _this._languageService.getLanguageIdByLanguageName(languageAlias);\n          } else if (_this._options.editor) {\n            languageId = _this._options.editor.getModel()?.getLanguageId();\n          }\n          if (!languageId) {\n            languageId = PLAINTEXT_LANGUAGE_ID;\n          }\n          const html = yield tokenizeToString(_this._languageService, value, languageId);\n          const element = document.createElement('span');\n          element.innerHTML = MarkdownRenderer_1._ttpTokenizer?.createHTML(html) ?? html;\n          // use \"good\" font\n          if (_this._options.editor) {\n            const fontInfo = _this._options.editor.getOption(50 /* EditorOption.fontInfo */);\n            applyFontInfo(element, fontInfo);\n          } else if (_this._options.codeBlockFontFamily) {\n            element.style.fontFamily = _this._options.codeBlockFontFamily;\n          }\n          if (_this._options.codeBlockFontSize !== undefined) {\n            element.style.fontSize = _this._options.codeBlockFontSize;\n          }\n          return element;\n        });\n        return function codeBlockRenderer(_x, _x2) {\n          return _ref.apply(this, arguments);\n        };\n      }(),\n      asyncRenderCallback: () => this._onDidRenderAsync.fire(),\n      actionHandler: {\n        callback: link => openLinkFromMarkdown(this._openerService, link, markdown.isTrusted),\n        disposables: disposables\n      }\n    };\n  }\n};\nMarkdownRenderer = MarkdownRenderer_1 = __decorate([__param(1, ILanguageService), __param(2, IOpenerService)], MarkdownRenderer);\nexport { MarkdownRenderer };\nexport function openLinkFromMarkdown(_x3, _x4, _x5) {\n  return _openLinkFromMarkdown.apply(this, arguments);\n}\nfunction _openLinkFromMarkdown() {\n  _openLinkFromMarkdown = _asyncToGenerator(function* (openerService, link, isTrusted) {\n    try {\n      return yield openerService.open(link, {\n        fromUserGesture: true,\n        allowContributedOpeners: true,\n        allowCommands: toAllowCommandsOption(isTrusted)\n      });\n    } catch (e) {\n      onUnexpectedError(e);\n      return false;\n    }\n  });\n  return _openLinkFromMarkdown.apply(this, arguments);\n}\nfunction toAllowCommandsOption(isTrusted) {\n  if (isTrusted === true) {\n    return true; // Allow all commands\n  }\n  if (isTrusted && Array.isArray(isTrusted.enabledCommands)) {\n    return isTrusted.enabledCommands; // Allow subset of commands\n  }\n  return false; // Block commands\n}", "map": {"version": 3, "names": ["__decorate", "decorators", "target", "key", "desc", "c", "arguments", "length", "r", "Object", "getOwnPropertyDescriptor", "d", "Reflect", "decorate", "i", "defineProperty", "__param", "paramIndex", "decorator", "MarkdownRenderer_1", "renderMarkdown", "createTrustedTypesPolicy", "onUnexpectedError", "Emitter", "DisposableStore", "applyFontInfo", "ILanguageService", "PLAINTEXT_LANGUAGE_ID", "tokenizeToString", "IOpenerService", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_ttpTokenizer", "createHTML", "html", "constructor", "_options", "_languageService", "_openerService", "_onDidRenderAsync", "onDidRenderAsync", "event", "dispose", "render", "markdown", "options", "markedOptions", "element", "document", "createElement", "disposables", "rendered", "add", "_getRenderOptions", "classList", "_this", "codeBlock<PERSON><PERSON><PERSON>", "_ref", "_asyncToGenerator", "languageAlias", "value", "languageId", "getLanguageIdByLanguageName", "editor", "getModel", "getLanguageId", "innerHTML", "fontInfo", "getOption", "codeBlockFontFamily", "style", "fontFamily", "codeBlockFontSize", "undefined", "fontSize", "_x", "_x2", "apply", "asyncRender<PERSON><PERSON>back", "fire", "actionHandler", "callback", "link", "openLinkFromMarkdown", "isTrusted", "_x3", "_x4", "_x5", "_openLinkFromMarkdown", "openerService", "open", "fromUserGesture", "allowContributedOpeners", "allowCommands", "toAllowCommandsOption", "e", "Array", "isArray", "enabledCommands"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/browser/widget/markdownRenderer/browser/markdownRenderer.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = (this && this.__param) || function (paramIndex, decorator) {\n    return function (target, key) { decorator(target, key, paramIndex); }\n};\nvar MarkdownRenderer_1;\nimport { renderMarkdown } from '../../../../../base/browser/markdownRenderer.js';\nimport { createTrustedTypesPolicy } from '../../../../../base/browser/trustedTypes.js';\nimport { onUnexpectedError } from '../../../../../base/common/errors.js';\nimport { Emitter } from '../../../../../base/common/event.js';\nimport { DisposableStore } from '../../../../../base/common/lifecycle.js';\nimport './renderedMarkdown.css';\nimport { applyFontInfo } from '../../../config/domFontInfo.js';\nimport { ILanguageService } from '../../../../common/languages/language.js';\nimport { PLAINTEXT_LANGUAGE_ID } from '../../../../common/languages/modesRegistry.js';\nimport { tokenizeToString } from '../../../../common/languages/textToHtmlTokenizer.js';\nimport { IOpenerService } from '../../../../../platform/opener/common/opener.js';\n/**\n * Markdown renderer that can render codeblocks with the editor mechanics. This\n * renderer should always be preferred.\n */\nlet MarkdownRenderer = class MarkdownRenderer {\n    static { MarkdownRenderer_1 = this; }\n    static { this._ttpTokenizer = createTrustedTypesPolicy('tokenizeToString', {\n        createHTML(html) {\n            return html;\n        }\n    }); }\n    constructor(_options, _languageService, _openerService) {\n        this._options = _options;\n        this._languageService = _languageService;\n        this._openerService = _openerService;\n        this._onDidRenderAsync = new Emitter();\n        this.onDidRenderAsync = this._onDidRenderAsync.event;\n    }\n    dispose() {\n        this._onDidRenderAsync.dispose();\n    }\n    render(markdown, options, markedOptions) {\n        if (!markdown) {\n            const element = document.createElement('span');\n            return { element, dispose: () => { } };\n        }\n        const disposables = new DisposableStore();\n        const rendered = disposables.add(renderMarkdown(markdown, { ...this._getRenderOptions(markdown, disposables), ...options }, markedOptions));\n        rendered.element.classList.add('rendered-markdown');\n        return {\n            element: rendered.element,\n            dispose: () => disposables.dispose()\n        };\n    }\n    _getRenderOptions(markdown, disposables) {\n        return {\n            codeBlockRenderer: async (languageAlias, value) => {\n                // In markdown,\n                // it is possible that we stumble upon language aliases (e.g.js instead of javascript)\n                // it is possible no alias is given in which case we fall back to the current editor lang\n                let languageId;\n                if (languageAlias) {\n                    languageId = this._languageService.getLanguageIdByLanguageName(languageAlias);\n                }\n                else if (this._options.editor) {\n                    languageId = this._options.editor.getModel()?.getLanguageId();\n                }\n                if (!languageId) {\n                    languageId = PLAINTEXT_LANGUAGE_ID;\n                }\n                const html = await tokenizeToString(this._languageService, value, languageId);\n                const element = document.createElement('span');\n                element.innerHTML = (MarkdownRenderer_1._ttpTokenizer?.createHTML(html) ?? html);\n                // use \"good\" font\n                if (this._options.editor) {\n                    const fontInfo = this._options.editor.getOption(50 /* EditorOption.fontInfo */);\n                    applyFontInfo(element, fontInfo);\n                }\n                else if (this._options.codeBlockFontFamily) {\n                    element.style.fontFamily = this._options.codeBlockFontFamily;\n                }\n                if (this._options.codeBlockFontSize !== undefined) {\n                    element.style.fontSize = this._options.codeBlockFontSize;\n                }\n                return element;\n            },\n            asyncRenderCallback: () => this._onDidRenderAsync.fire(),\n            actionHandler: {\n                callback: (link) => openLinkFromMarkdown(this._openerService, link, markdown.isTrusted),\n                disposables: disposables\n            }\n        };\n    }\n};\nMarkdownRenderer = MarkdownRenderer_1 = __decorate([\n    __param(1, ILanguageService),\n    __param(2, IOpenerService)\n], MarkdownRenderer);\nexport { MarkdownRenderer };\nexport async function openLinkFromMarkdown(openerService, link, isTrusted) {\n    try {\n        return await openerService.open(link, {\n            fromUserGesture: true,\n            allowContributedOpeners: true,\n            allowCommands: toAllowCommandsOption(isTrusted),\n        });\n    }\n    catch (e) {\n        onUnexpectedError(e);\n        return false;\n    }\n}\nfunction toAllowCommandsOption(isTrusted) {\n    if (isTrusted === true) {\n        return true; // Allow all commands\n    }\n    if (isTrusted && Array.isArray(isTrusted.enabledCommands)) {\n        return isTrusted.enabledCommands; // Allow subset of commands\n    }\n    return false; // Block commands\n}\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA,IAAIA,UAAU,GAAI,IAAI,IAAI,IAAI,CAACA,UAAU,IAAK,UAAUC,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAE;EACnF,IAAIC,CAAC,GAAGC,SAAS,CAACC,MAAM;IAAEC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGH,MAAM,GAAGE,IAAI,KAAK,IAAI,GAAGA,IAAI,GAAGK,MAAM,CAACC,wBAAwB,CAACR,MAAM,EAAEC,GAAG,CAAC,GAAGC,IAAI;IAAEO,CAAC;EAC5H,IAAI,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,CAACC,QAAQ,KAAK,UAAU,EAAEL,CAAC,GAAGI,OAAO,CAACC,QAAQ,CAACZ,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,CAAC,CAAC,KAC1H,KAAK,IAAIU,CAAC,GAAGb,UAAU,CAACM,MAAM,GAAG,CAAC,EAAEO,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE,IAAIH,CAAC,GAAGV,UAAU,CAACa,CAAC,CAAC,EAAEN,CAAC,GAAG,CAACH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACH,CAAC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACT,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,GAAGG,CAAC,CAACT,MAAM,EAAEC,GAAG,CAAC,KAAKK,CAAC;EACjJ,OAAOH,CAAC,GAAG,CAAC,IAAIG,CAAC,IAAIC,MAAM,CAACM,cAAc,CAACb,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,EAAEA,CAAC;AACjE,CAAC;AACD,IAAIQ,OAAO,GAAI,IAAI,IAAI,IAAI,CAACA,OAAO,IAAK,UAAUC,UAAU,EAAEC,SAAS,EAAE;EACrE,OAAO,UAAUhB,MAAM,EAAEC,GAAG,EAAE;IAAEe,SAAS,CAAChB,MAAM,EAAEC,GAAG,EAAEc,UAAU,CAAC;EAAE,CAAC;AACzE,CAAC;AACD,IAAIE,kBAAkB;AACtB,SAASC,cAAc,QAAQ,iDAAiD;AAChF,SAASC,wBAAwB,QAAQ,6CAA6C;AACtF,SAASC,iBAAiB,QAAQ,sCAAsC;AACxE,SAASC,OAAO,QAAQ,qCAAqC;AAC7D,SAASC,eAAe,QAAQ,yCAAyC;AACzE,OAAO,wBAAwB;AAC/B,SAASC,aAAa,QAAQ,gCAAgC;AAC9D,SAASC,gBAAgB,QAAQ,0CAA0C;AAC3E,SAASC,qBAAqB,QAAQ,+CAA+C;AACrF,SAASC,gBAAgB,QAAQ,qDAAqD;AACtF,SAASC,cAAc,QAAQ,iDAAiD;AAChF;AACA;AACA;AACA;AACA,IAAIC,gBAAgB,GAAG,MAAMA,gBAAgB,CAAC;EAC1C;IAASX,kBAAkB,GAAG,IAAI;EAAE;EACpC;IAAS,IAAI,CAACY,aAAa,GAAGV,wBAAwB,CAAC,kBAAkB,EAAE;MACvEW,UAAUA,CAACC,IAAI,EAAE;QACb,OAAOA,IAAI;MACf;IACJ,CAAC,CAAC;EAAE;EACJC,WAAWA,CAACC,QAAQ,EAAEC,gBAAgB,EAAEC,cAAc,EAAE;IACpD,IAAI,CAACF,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,iBAAiB,GAAG,IAAIf,OAAO,CAAC,CAAC;IACtC,IAAI,CAACgB,gBAAgB,GAAG,IAAI,CAACD,iBAAiB,CAACE,KAAK;EACxD;EACAC,OAAOA,CAAA,EAAG;IACN,IAAI,CAACH,iBAAiB,CAACG,OAAO,CAAC,CAAC;EACpC;EACAC,MAAMA,CAACC,QAAQ,EAAEC,OAAO,EAAEC,aAAa,EAAE;IACrC,IAAI,CAACF,QAAQ,EAAE;MACX,MAAMG,OAAO,GAAGC,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;MAC9C,OAAO;QAAEF,OAAO;QAAEL,OAAO,EAAEA,CAAA,KAAM,CAAE;MAAE,CAAC;IAC1C;IACA,MAAMQ,WAAW,GAAG,IAAIzB,eAAe,CAAC,CAAC;IACzC,MAAM0B,QAAQ,GAAGD,WAAW,CAACE,GAAG,CAAC/B,cAAc,CAACuB,QAAQ,EAAE;MAAE,GAAG,IAAI,CAACS,iBAAiB,CAACT,QAAQ,EAAEM,WAAW,CAAC;MAAE,GAAGL;IAAQ,CAAC,EAAEC,aAAa,CAAC,CAAC;IAC3IK,QAAQ,CAACJ,OAAO,CAACO,SAAS,CAACF,GAAG,CAAC,mBAAmB,CAAC;IACnD,OAAO;MACHL,OAAO,EAAEI,QAAQ,CAACJ,OAAO;MACzBL,OAAO,EAAEA,CAAA,KAAMQ,WAAW,CAACR,OAAO,CAAC;IACvC,CAAC;EACL;EACAW,iBAAiBA,CAACT,QAAQ,EAAEM,WAAW,EAAE;IAAA,IAAAK,KAAA;IACrC,OAAO;MACHC,iBAAiB;QAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAE,WAAOC,aAAa,EAAEC,KAAK,EAAK;UAC/C;UACA;UACA;UACA,IAAIC,UAAU;UACd,IAAIF,aAAa,EAAE;YACfE,UAAU,GAAGN,KAAI,CAAClB,gBAAgB,CAACyB,2BAA2B,CAACH,aAAa,CAAC;UACjF,CAAC,MACI,IAAIJ,KAAI,CAACnB,QAAQ,CAAC2B,MAAM,EAAE;YAC3BF,UAAU,GAAGN,KAAI,CAACnB,QAAQ,CAAC2B,MAAM,CAACC,QAAQ,CAAC,CAAC,EAAEC,aAAa,CAAC,CAAC;UACjE;UACA,IAAI,CAACJ,UAAU,EAAE;YACbA,UAAU,GAAGjC,qBAAqB;UACtC;UACA,MAAMM,IAAI,SAASL,gBAAgB,CAAC0B,KAAI,CAAClB,gBAAgB,EAAEuB,KAAK,EAAEC,UAAU,CAAC;UAC7E,MAAMd,OAAO,GAAGC,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;UAC9CF,OAAO,CAACmB,SAAS,GAAI9C,kBAAkB,CAACY,aAAa,EAAEC,UAAU,CAACC,IAAI,CAAC,IAAIA,IAAK;UAChF;UACA,IAAIqB,KAAI,CAACnB,QAAQ,CAAC2B,MAAM,EAAE;YACtB,MAAMI,QAAQ,GAAGZ,KAAI,CAACnB,QAAQ,CAAC2B,MAAM,CAACK,SAAS,CAAC,EAAE,CAAC,2BAA2B,CAAC;YAC/E1C,aAAa,CAACqB,OAAO,EAAEoB,QAAQ,CAAC;UACpC,CAAC,MACI,IAAIZ,KAAI,CAACnB,QAAQ,CAACiC,mBAAmB,EAAE;YACxCtB,OAAO,CAACuB,KAAK,CAACC,UAAU,GAAGhB,KAAI,CAACnB,QAAQ,CAACiC,mBAAmB;UAChE;UACA,IAAId,KAAI,CAACnB,QAAQ,CAACoC,iBAAiB,KAAKC,SAAS,EAAE;YAC/C1B,OAAO,CAACuB,KAAK,CAACI,QAAQ,GAAGnB,KAAI,CAACnB,QAAQ,CAACoC,iBAAiB;UAC5D;UACA,OAAOzB,OAAO;QAClB,CAAC;QAAA,gBA7BDS,iBAAiBA,CAAAmB,EAAA,EAAAC,GAAA;UAAA,OAAAnB,IAAA,CAAAoB,KAAA,OAAAtE,SAAA;QAAA;MAAA,GA6BhB;MACDuE,mBAAmB,EAAEA,CAAA,KAAM,IAAI,CAACvC,iBAAiB,CAACwC,IAAI,CAAC,CAAC;MACxDC,aAAa,EAAE;QACXC,QAAQ,EAAGC,IAAI,IAAKC,oBAAoB,CAAC,IAAI,CAAC7C,cAAc,EAAE4C,IAAI,EAAEtC,QAAQ,CAACwC,SAAS,CAAC;QACvFlC,WAAW,EAAEA;MACjB;IACJ,CAAC;EACL;AACJ,CAAC;AACDnB,gBAAgB,GAAGX,kBAAkB,GAAGnB,UAAU,CAAC,CAC/CgB,OAAO,CAAC,CAAC,EAAEU,gBAAgB,CAAC,EAC5BV,OAAO,CAAC,CAAC,EAAEa,cAAc,CAAC,CAC7B,EAAEC,gBAAgB,CAAC;AACpB,SAASA,gBAAgB;AACzB,gBAAsBoD,oBAAoBA,CAAAE,GAAA,EAAAC,GAAA,EAAAC,GAAA;EAAA,OAAAC,qBAAA,CAAAX,KAAA,OAAAtE,SAAA;AAAA;AAYzC,SAAAiF,sBAAA;EAAAA,qBAAA,GAAA9B,iBAAA,CAZM,WAAoC+B,aAAa,EAAEP,IAAI,EAAEE,SAAS,EAAE;IACvE,IAAI;MACA,aAAaK,aAAa,CAACC,IAAI,CAACR,IAAI,EAAE;QAClCS,eAAe,EAAE,IAAI;QACrBC,uBAAuB,EAAE,IAAI;QAC7BC,aAAa,EAAEC,qBAAqB,CAACV,SAAS;MAClD,CAAC,CAAC;IACN,CAAC,CACD,OAAOW,CAAC,EAAE;MACNxE,iBAAiB,CAACwE,CAAC,CAAC;MACpB,OAAO,KAAK;IAChB;EACJ,CAAC;EAAA,OAAAP,qBAAA,CAAAX,KAAA,OAAAtE,SAAA;AAAA;AACD,SAASuF,qBAAqBA,CAACV,SAAS,EAAE;EACtC,IAAIA,SAAS,KAAK,IAAI,EAAE;IACpB,OAAO,IAAI,CAAC,CAAC;EACjB;EACA,IAAIA,SAAS,IAAIY,KAAK,CAACC,OAAO,CAACb,SAAS,CAACc,eAAe,CAAC,EAAE;IACvD,OAAOd,SAAS,CAACc,eAAe,CAAC,CAAC;EACtC;EACA,OAAO,KAAK,CAAC,CAAC;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}