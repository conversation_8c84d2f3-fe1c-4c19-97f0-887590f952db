{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { getDefaultHoverDelegate } from '../hover/hoverDelegateFactory.js';\nimport { Toggle } from '../toggle/toggle.js';\nimport { Codicon } from '../../../common/codicons.js';\nimport * as nls from '../../../../nls.js';\nconst NLS_CASE_SENSITIVE_TOGGLE_LABEL = nls.localize('caseDescription', \"Match Case\");\nconst NLS_WHOLE_WORD_TOGGLE_LABEL = nls.localize('wordsDescription', \"Match Whole Word\");\nconst NLS_REGEX_TOGGLE_LABEL = nls.localize('regexDescription', \"Use Regular Expression\");\nexport class CaseSensitiveToggle extends Toggle {\n  constructor(opts) {\n    super({\n      icon: Codicon.caseSensitive,\n      title: NLS_CASE_SENSITIVE_TOGGLE_LABEL + opts.appendTitle,\n      isChecked: opts.isChecked,\n      hoverDelegate: opts.hoverDelegate ?? getDefaultHoverDelegate('element'),\n      inputActiveOptionBorder: opts.inputActiveOptionBorder,\n      inputActiveOptionForeground: opts.inputActiveOptionForeground,\n      inputActiveOptionBackground: opts.inputActiveOptionBackground\n    });\n  }\n}\nexport class WholeWordsToggle extends Toggle {\n  constructor(opts) {\n    super({\n      icon: Codicon.wholeWord,\n      title: NLS_WHOLE_WORD_TOGGLE_LABEL + opts.appendTitle,\n      isChecked: opts.isChecked,\n      hoverDelegate: opts.hoverDelegate ?? getDefaultHoverDelegate('element'),\n      inputActiveOptionBorder: opts.inputActiveOptionBorder,\n      inputActiveOptionForeground: opts.inputActiveOptionForeground,\n      inputActiveOptionBackground: opts.inputActiveOptionBackground\n    });\n  }\n}\nexport class RegexToggle extends Toggle {\n  constructor(opts) {\n    super({\n      icon: Codicon.regex,\n      title: NLS_REGEX_TOGGLE_LABEL + opts.appendTitle,\n      isChecked: opts.isChecked,\n      hoverDelegate: opts.hoverDelegate ?? getDefaultHoverDelegate('element'),\n      inputActiveOptionBorder: opts.inputActiveOptionBorder,\n      inputActiveOptionForeground: opts.inputActiveOptionForeground,\n      inputActiveOptionBackground: opts.inputActiveOptionBackground\n    });\n  }\n}", "map": {"version": 3, "names": ["getDefaultHoverDelegate", "Toggle", "Codicon", "nls", "NLS_CASE_SENSITIVE_TOGGLE_LABEL", "localize", "NLS_WHOLE_WORD_TOGGLE_LABEL", "NLS_REGEX_TOGGLE_LABEL", "CaseSensitiveToggle", "constructor", "opts", "icon", "caseSensitive", "title", "appendTitle", "isChecked", "hoverDelegate", "inputActiveOptionBorder", "inputActiveOptionForeground", "inputActiveOptionBackground", "WholeWordsToggle", "wholeWord", "RegexToggle", "regex"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/browser/ui/findinput/findInputToggles.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { getDefaultHoverDelegate } from '../hover/hoverDelegateFactory.js';\nimport { Toggle } from '../toggle/toggle.js';\nimport { Codicon } from '../../../common/codicons.js';\nimport * as nls from '../../../../nls.js';\nconst NLS_CASE_SENSITIVE_TOGGLE_LABEL = nls.localize('caseDescription', \"Match Case\");\nconst NLS_WHOLE_WORD_TOGGLE_LABEL = nls.localize('wordsDescription', \"Match Whole Word\");\nconst NLS_REGEX_TOGGLE_LABEL = nls.localize('regexDescription', \"Use Regular Expression\");\nexport class CaseSensitiveToggle extends Toggle {\n    constructor(opts) {\n        super({\n            icon: Codicon.caseSensitive,\n            title: NLS_CASE_SENSITIVE_TOGGLE_LABEL + opts.appendTitle,\n            isChecked: opts.isChecked,\n            hoverDelegate: opts.hoverDelegate ?? getDefaultHoverDelegate('element'),\n            inputActiveOptionBorder: opts.inputActiveOptionBorder,\n            inputActiveOptionForeground: opts.inputActiveOptionForeground,\n            inputActiveOptionBackground: opts.inputActiveOptionBackground\n        });\n    }\n}\nexport class WholeWordsToggle extends Toggle {\n    constructor(opts) {\n        super({\n            icon: Codicon.wholeWord,\n            title: NLS_WHOLE_WORD_TOGGLE_LABEL + opts.appendTitle,\n            isChecked: opts.isChecked,\n            hoverDelegate: opts.hoverDelegate ?? getDefaultHoverDelegate('element'),\n            inputActiveOptionBorder: opts.inputActiveOptionBorder,\n            inputActiveOptionForeground: opts.inputActiveOptionForeground,\n            inputActiveOptionBackground: opts.inputActiveOptionBackground\n        });\n    }\n}\nexport class RegexToggle extends Toggle {\n    constructor(opts) {\n        super({\n            icon: Codicon.regex,\n            title: NLS_REGEX_TOGGLE_LABEL + opts.appendTitle,\n            isChecked: opts.isChecked,\n            hoverDelegate: opts.hoverDelegate ?? getDefaultHoverDelegate('element'),\n            inputActiveOptionBorder: opts.inputActiveOptionBorder,\n            inputActiveOptionForeground: opts.inputActiveOptionForeground,\n            inputActiveOptionBackground: opts.inputActiveOptionBackground\n        });\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,uBAAuB,QAAQ,kCAAkC;AAC1E,SAASC,MAAM,QAAQ,qBAAqB;AAC5C,SAASC,OAAO,QAAQ,6BAA6B;AACrD,OAAO,KAAKC,GAAG,MAAM,oBAAoB;AACzC,MAAMC,+BAA+B,GAAGD,GAAG,CAACE,QAAQ,CAAC,iBAAiB,EAAE,YAAY,CAAC;AACrF,MAAMC,2BAA2B,GAAGH,GAAG,CAACE,QAAQ,CAAC,kBAAkB,EAAE,kBAAkB,CAAC;AACxF,MAAME,sBAAsB,GAAGJ,GAAG,CAACE,QAAQ,CAAC,kBAAkB,EAAE,wBAAwB,CAAC;AACzF,OAAO,MAAMG,mBAAmB,SAASP,MAAM,CAAC;EAC5CQ,WAAWA,CAACC,IAAI,EAAE;IACd,KAAK,CAAC;MACFC,IAAI,EAAET,OAAO,CAACU,aAAa;MAC3BC,KAAK,EAAET,+BAA+B,GAAGM,IAAI,CAACI,WAAW;MACzDC,SAAS,EAAEL,IAAI,CAACK,SAAS;MACzBC,aAAa,EAAEN,IAAI,CAACM,aAAa,IAAIhB,uBAAuB,CAAC,SAAS,CAAC;MACvEiB,uBAAuB,EAAEP,IAAI,CAACO,uBAAuB;MACrDC,2BAA2B,EAAER,IAAI,CAACQ,2BAA2B;MAC7DC,2BAA2B,EAAET,IAAI,CAACS;IACtC,CAAC,CAAC;EACN;AACJ;AACA,OAAO,MAAMC,gBAAgB,SAASnB,MAAM,CAAC;EACzCQ,WAAWA,CAACC,IAAI,EAAE;IACd,KAAK,CAAC;MACFC,IAAI,EAAET,OAAO,CAACmB,SAAS;MACvBR,KAAK,EAAEP,2BAA2B,GAAGI,IAAI,CAACI,WAAW;MACrDC,SAAS,EAAEL,IAAI,CAACK,SAAS;MACzBC,aAAa,EAAEN,IAAI,CAACM,aAAa,IAAIhB,uBAAuB,CAAC,SAAS,CAAC;MACvEiB,uBAAuB,EAAEP,IAAI,CAACO,uBAAuB;MACrDC,2BAA2B,EAAER,IAAI,CAACQ,2BAA2B;MAC7DC,2BAA2B,EAAET,IAAI,CAACS;IACtC,CAAC,CAAC;EACN;AACJ;AACA,OAAO,MAAMG,WAAW,SAASrB,MAAM,CAAC;EACpCQ,WAAWA,CAACC,IAAI,EAAE;IACd,KAAK,CAAC;MACFC,IAAI,EAAET,OAAO,CAACqB,KAAK;MACnBV,KAAK,EAAEN,sBAAsB,GAAGG,IAAI,CAACI,WAAW;MAChDC,SAAS,EAAEL,IAAI,CAACK,SAAS;MACzBC,aAAa,EAAEN,IAAI,CAACM,aAAa,IAAIhB,uBAAuB,CAAC,SAAS,CAAC;MACvEiB,uBAAuB,EAAEP,IAAI,CAACO,uBAAuB;MACrDC,2BAA2B,EAAER,IAAI,CAACQ,2BAA2B;MAC7DC,2BAA2B,EAAET,IAAI,CAACS;IACtC,CAAC,CAAC;EACN;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}