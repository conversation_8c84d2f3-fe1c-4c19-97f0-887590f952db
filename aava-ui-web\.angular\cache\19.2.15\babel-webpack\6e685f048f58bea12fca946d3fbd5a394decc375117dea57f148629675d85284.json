{"ast": null, "code": "import { set } from \"./schedule.js\";\nfunction easeVarying(id, value) {\n  return function () {\n    var v = value.apply(this, arguments);\n    if (typeof v !== \"function\") throw new Error();\n    set(this, id).ease = v;\n  };\n}\nexport default function (value) {\n  if (typeof value !== \"function\") throw new Error();\n  return this.each(easeVarying(this._id, value));\n}", "map": {"version": 3, "names": ["set", "easeVarying", "id", "value", "v", "apply", "arguments", "Error", "ease", "each", "_id"], "sources": ["C:/console/aava-ui-web/node_modules/d3-transition/src/transition/easeVarying.js"], "sourcesContent": ["import {set} from \"./schedule.js\";\n\nfunction easeVarying(id, value) {\n  return function() {\n    var v = value.apply(this, arguments);\n    if (typeof v !== \"function\") throw new Error;\n    set(this, id).ease = v;\n  };\n}\n\nexport default function(value) {\n  if (typeof value !== \"function\") throw new Error;\n  return this.each(easeVarying(this._id, value));\n}\n"], "mappings": "AAAA,SAAQA,GAAG,QAAO,eAAe;AAEjC,SAASC,WAAWA,CAACC,EAAE,EAAEC,KAAK,EAAE;EAC9B,OAAO,YAAW;IAChB,IAAIC,CAAC,GAAGD,KAAK,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IACpC,IAAI,OAAOF,CAAC,KAAK,UAAU,EAAE,MAAM,IAAIG,KAAK,CAAD,CAAC;IAC5CP,GAAG,CAAC,IAAI,EAAEE,EAAE,CAAC,CAACM,IAAI,GAAGJ,CAAC;EACxB,CAAC;AACH;AAEA,eAAe,UAASD,KAAK,EAAE;EAC7B,IAAI,OAAOA,KAAK,KAAK,UAAU,EAAE,MAAM,IAAII,KAAK,CAAD,CAAC;EAChD,OAAO,IAAI,CAACE,IAAI,CAACR,WAAW,CAAC,IAAI,CAACS,GAAG,EAAEP,KAAK,CAAC,CAAC;AAChD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}