{"ast": null, "code": "export var epsilon = 1e-6;\nexport var epsilon2 = 1e-12;\nexport var pi = Math.PI;\nexport var halfPi = pi / 2;\nexport var quarterPi = pi / 4;\nexport var tau = pi * 2;\nexport var degrees = 180 / pi;\nexport var radians = pi / 180;\nexport var abs = Math.abs;\nexport var atan = Math.atan;\nexport var atan2 = Math.atan2;\nexport var cos = Math.cos;\nexport var ceil = Math.ceil;\nexport var exp = Math.exp;\nexport var floor = Math.floor;\nexport var hypot = Math.hypot;\nexport var log = Math.log;\nexport var pow = Math.pow;\nexport var sin = Math.sin;\nexport var sign = Math.sign || function (x) {\n  return x > 0 ? 1 : x < 0 ? -1 : 0;\n};\nexport var sqrt = Math.sqrt;\nexport var tan = Math.tan;\nexport function acos(x) {\n  return x > 1 ? 0 : x < -1 ? pi : Math.acos(x);\n}\nexport function asin(x) {\n  return x > 1 ? halfPi : x < -1 ? -halfPi : Math.asin(x);\n}\nexport function haversin(x) {\n  return (x = sin(x / 2)) * x;\n}", "map": {"version": 3, "names": ["epsilon", "epsilon2", "pi", "Math", "PI", "halfPi", "quarterPi", "tau", "degrees", "radians", "abs", "atan", "atan2", "cos", "ceil", "exp", "floor", "hypot", "log", "pow", "sin", "sign", "x", "sqrt", "tan", "acos", "asin", "haversin"], "sources": ["C:/console/aava-ui-web/node_modules/d3-geo/src/math.js"], "sourcesContent": ["export var epsilon = 1e-6;\nexport var epsilon2 = 1e-12;\nexport var pi = Math.PI;\nexport var halfPi = pi / 2;\nexport var quarterPi = pi / 4;\nexport var tau = pi * 2;\n\nexport var degrees = 180 / pi;\nexport var radians = pi / 180;\n\nexport var abs = Math.abs;\nexport var atan = Math.atan;\nexport var atan2 = Math.atan2;\nexport var cos = Math.cos;\nexport var ceil = Math.ceil;\nexport var exp = Math.exp;\nexport var floor = Math.floor;\nexport var hypot = Math.hypot;\nexport var log = Math.log;\nexport var pow = Math.pow;\nexport var sin = Math.sin;\nexport var sign = Math.sign || function(x) { return x > 0 ? 1 : x < 0 ? -1 : 0; };\nexport var sqrt = Math.sqrt;\nexport var tan = Math.tan;\n\nexport function acos(x) {\n  return x > 1 ? 0 : x < -1 ? pi : Math.acos(x);\n}\n\nexport function asin(x) {\n  return x > 1 ? halfPi : x < -1 ? -halfPi : Math.asin(x);\n}\n\nexport function haversin(x) {\n  return (x = sin(x / 2)) * x;\n}\n"], "mappings": "AAAA,OAAO,IAAIA,OAAO,GAAG,IAAI;AACzB,OAAO,IAAIC,QAAQ,GAAG,KAAK;AAC3B,OAAO,IAAIC,EAAE,GAAGC,IAAI,CAACC,EAAE;AACvB,OAAO,IAAIC,MAAM,GAAGH,EAAE,GAAG,CAAC;AAC1B,OAAO,IAAII,SAAS,GAAGJ,EAAE,GAAG,CAAC;AAC7B,OAAO,IAAIK,GAAG,GAAGL,EAAE,GAAG,CAAC;AAEvB,OAAO,IAAIM,OAAO,GAAG,GAAG,GAAGN,EAAE;AAC7B,OAAO,IAAIO,OAAO,GAAGP,EAAE,GAAG,GAAG;AAE7B,OAAO,IAAIQ,GAAG,GAAGP,IAAI,CAACO,GAAG;AACzB,OAAO,IAAIC,IAAI,GAAGR,IAAI,CAACQ,IAAI;AAC3B,OAAO,IAAIC,KAAK,GAAGT,IAAI,CAACS,KAAK;AAC7B,OAAO,IAAIC,GAAG,GAAGV,IAAI,CAACU,GAAG;AACzB,OAAO,IAAIC,IAAI,GAAGX,IAAI,CAACW,IAAI;AAC3B,OAAO,IAAIC,GAAG,GAAGZ,IAAI,CAACY,GAAG;AACzB,OAAO,IAAIC,KAAK,GAAGb,IAAI,CAACa,KAAK;AAC7B,OAAO,IAAIC,KAAK,GAAGd,IAAI,CAACc,KAAK;AAC7B,OAAO,IAAIC,GAAG,GAAGf,IAAI,CAACe,GAAG;AACzB,OAAO,IAAIC,GAAG,GAAGhB,IAAI,CAACgB,GAAG;AACzB,OAAO,IAAIC,GAAG,GAAGjB,IAAI,CAACiB,GAAG;AACzB,OAAO,IAAIC,IAAI,GAAGlB,IAAI,CAACkB,IAAI,IAAI,UAASC,CAAC,EAAE;EAAE,OAAOA,CAAC,GAAG,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;AAAE,CAAC;AACjF,OAAO,IAAIC,IAAI,GAAGpB,IAAI,CAACoB,IAAI;AAC3B,OAAO,IAAIC,GAAG,GAAGrB,IAAI,CAACqB,GAAG;AAEzB,OAAO,SAASC,IAAIA,CAACH,CAAC,EAAE;EACtB,OAAOA,CAAC,GAAG,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAG,CAAC,CAAC,GAAGpB,EAAE,GAAGC,IAAI,CAACsB,IAAI,CAACH,CAAC,CAAC;AAC/C;AAEA,OAAO,SAASI,IAAIA,CAACJ,CAAC,EAAE;EACtB,OAAOA,CAAC,GAAG,CAAC,GAAGjB,MAAM,GAAGiB,CAAC,GAAG,CAAC,CAAC,GAAG,CAACjB,MAAM,GAAGF,IAAI,CAACuB,IAAI,CAACJ,CAAC,CAAC;AACzD;AAEA,OAAO,SAASK,QAAQA,CAACL,CAAC,EAAE;EAC1B,OAAO,CAACA,CAAC,GAAGF,GAAG,CAACE,CAAC,GAAG,CAAC,CAAC,IAAIA,CAAC;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}