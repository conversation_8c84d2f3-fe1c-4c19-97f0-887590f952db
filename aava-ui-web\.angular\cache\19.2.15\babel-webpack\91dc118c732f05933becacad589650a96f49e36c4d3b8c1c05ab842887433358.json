{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { arrayInsert } from '../../../base/common/arrays.js';\nimport { toUint32 } from '../../../base/common/uint.js';\nexport class PrefixSumComputer {\n  constructor(values) {\n    this.values = values;\n    this.prefixSum = new Uint32Array(values.length);\n    this.prefixSumValidIndex = new Int32Array(1);\n    this.prefixSumValidIndex[0] = -1;\n  }\n  insertValues(insertIndex, insertValues) {\n    insertIndex = toUint32(insertIndex);\n    const oldValues = this.values;\n    const oldPrefixSum = this.prefixSum;\n    const insertValuesLen = insertValues.length;\n    if (insertValuesLen === 0) {\n      return false;\n    }\n    this.values = new Uint32Array(oldValues.length + insertValuesLen);\n    this.values.set(oldValues.subarray(0, insertIndex), 0);\n    this.values.set(oldValues.subarray(insertIndex), insertIndex + insertValuesLen);\n    this.values.set(insertValues, insertIndex);\n    if (insertIndex - 1 < this.prefixSumValidIndex[0]) {\n      this.prefixSumValidIndex[0] = insertIndex - 1;\n    }\n    this.prefixSum = new Uint32Array(this.values.length);\n    if (this.prefixSumValidIndex[0] >= 0) {\n      this.prefixSum.set(oldPrefixSum.subarray(0, this.prefixSumValidIndex[0] + 1));\n    }\n    return true;\n  }\n  setValue(index, value) {\n    index = toUint32(index);\n    value = toUint32(value);\n    if (this.values[index] === value) {\n      return false;\n    }\n    this.values[index] = value;\n    if (index - 1 < this.prefixSumValidIndex[0]) {\n      this.prefixSumValidIndex[0] = index - 1;\n    }\n    return true;\n  }\n  removeValues(startIndex, count) {\n    startIndex = toUint32(startIndex);\n    count = toUint32(count);\n    const oldValues = this.values;\n    const oldPrefixSum = this.prefixSum;\n    if (startIndex >= oldValues.length) {\n      return false;\n    }\n    const maxCount = oldValues.length - startIndex;\n    if (count >= maxCount) {\n      count = maxCount;\n    }\n    if (count === 0) {\n      return false;\n    }\n    this.values = new Uint32Array(oldValues.length - count);\n    this.values.set(oldValues.subarray(0, startIndex), 0);\n    this.values.set(oldValues.subarray(startIndex + count), startIndex);\n    this.prefixSum = new Uint32Array(this.values.length);\n    if (startIndex - 1 < this.prefixSumValidIndex[0]) {\n      this.prefixSumValidIndex[0] = startIndex - 1;\n    }\n    if (this.prefixSumValidIndex[0] >= 0) {\n      this.prefixSum.set(oldPrefixSum.subarray(0, this.prefixSumValidIndex[0] + 1));\n    }\n    return true;\n  }\n  getTotalSum() {\n    if (this.values.length === 0) {\n      return 0;\n    }\n    return this._getPrefixSum(this.values.length - 1);\n  }\n  /**\n   * Returns the sum of the first `index + 1` many items.\n   * @returns `SUM(0 <= j <= index, values[j])`.\n   */\n  getPrefixSum(index) {\n    if (index < 0) {\n      return 0;\n    }\n    index = toUint32(index);\n    return this._getPrefixSum(index);\n  }\n  _getPrefixSum(index) {\n    if (index <= this.prefixSumValidIndex[0]) {\n      return this.prefixSum[index];\n    }\n    let startIndex = this.prefixSumValidIndex[0] + 1;\n    if (startIndex === 0) {\n      this.prefixSum[0] = this.values[0];\n      startIndex++;\n    }\n    if (index >= this.values.length) {\n      index = this.values.length - 1;\n    }\n    for (let i = startIndex; i <= index; i++) {\n      this.prefixSum[i] = this.prefixSum[i - 1] + this.values[i];\n    }\n    this.prefixSumValidIndex[0] = Math.max(this.prefixSumValidIndex[0], index);\n    return this.prefixSum[index];\n  }\n  getIndexOf(sum) {\n    sum = Math.floor(sum);\n    // Compute all sums (to get a fully valid prefixSum)\n    this.getTotalSum();\n    let low = 0;\n    let high = this.values.length - 1;\n    let mid = 0;\n    let midStop = 0;\n    let midStart = 0;\n    while (low <= high) {\n      mid = low + (high - low) / 2 | 0;\n      midStop = this.prefixSum[mid];\n      midStart = midStop - this.values[mid];\n      if (sum < midStart) {\n        high = mid - 1;\n      } else if (sum >= midStop) {\n        low = mid + 1;\n      } else {\n        break;\n      }\n    }\n    return new PrefixSumIndexOfResult(mid, sum - midStart);\n  }\n}\n/**\n * {@link getIndexOf} has an amortized runtime complexity of O(1).\n *\n * ({@link PrefixSumComputer.getIndexOf} is just  O(log n))\n*/\nexport class ConstantTimePrefixSumComputer {\n  constructor(values) {\n    this._values = values;\n    this._isValid = false;\n    this._validEndIndex = -1;\n    this._prefixSum = [];\n    this._indexBySum = [];\n  }\n  /**\n   * @returns SUM(0 <= j < values.length, values[j])\n   */\n  getTotalSum() {\n    this._ensureValid();\n    return this._indexBySum.length;\n  }\n  /**\n   * Returns the sum of the first `count` many items.\n   * @returns `SUM(0 <= j < count, values[j])`.\n   */\n  getPrefixSum(count) {\n    this._ensureValid();\n    if (count === 0) {\n      return 0;\n    }\n    return this._prefixSum[count - 1];\n  }\n  /**\n   * @returns `result`, such that `getPrefixSum(result.index) + result.remainder = sum`\n   */\n  getIndexOf(sum) {\n    this._ensureValid();\n    const idx = this._indexBySum[sum];\n    const viewLinesAbove = idx > 0 ? this._prefixSum[idx - 1] : 0;\n    return new PrefixSumIndexOfResult(idx, sum - viewLinesAbove);\n  }\n  removeValues(start, deleteCount) {\n    this._values.splice(start, deleteCount);\n    this._invalidate(start);\n  }\n  insertValues(insertIndex, insertArr) {\n    this._values = arrayInsert(this._values, insertIndex, insertArr);\n    this._invalidate(insertIndex);\n  }\n  _invalidate(index) {\n    this._isValid = false;\n    this._validEndIndex = Math.min(this._validEndIndex, index - 1);\n  }\n  _ensureValid() {\n    if (this._isValid) {\n      return;\n    }\n    for (let i = this._validEndIndex + 1, len = this._values.length; i < len; i++) {\n      const value = this._values[i];\n      const sumAbove = i > 0 ? this._prefixSum[i - 1] : 0;\n      this._prefixSum[i] = sumAbove + value;\n      for (let j = 0; j < value; j++) {\n        this._indexBySum[sumAbove + j] = i;\n      }\n    }\n    // trim things\n    this._prefixSum.length = this._values.length;\n    this._indexBySum.length = this._prefixSum[this._prefixSum.length - 1];\n    // mark as valid\n    this._isValid = true;\n    this._validEndIndex = this._values.length - 1;\n  }\n  setValue(index, value) {\n    if (this._values[index] === value) {\n      // no change\n      return;\n    }\n    this._values[index] = value;\n    this._invalidate(index);\n  }\n}\nexport class PrefixSumIndexOfResult {\n  constructor(index, remainder) {\n    this.index = index;\n    this.remainder = remainder;\n    this._prefixSumIndexOfResultBrand = undefined;\n    this.index = index;\n    this.remainder = remainder;\n  }\n}", "map": {"version": 3, "names": ["arrayInsert", "toUint32", "PrefixSumComputer", "constructor", "values", "prefixSum", "Uint32Array", "length", "prefixSumValidIndex", "Int32Array", "insertValues", "insertIndex", "oldValues", "oldPrefixSum", "insertValuesLen", "set", "subarray", "setValue", "index", "value", "removeValues", "startIndex", "count", "maxCount", "getTotalSum", "_getPrefixSum", "getPrefixSum", "i", "Math", "max", "getIndexOf", "sum", "floor", "low", "high", "mid", "midStop", "midStart", "PrefixSumIndexOfResult", "ConstantTimePrefixSumComputer", "_values", "_isValid", "_validEndIndex", "_prefixSum", "_indexBySum", "_ensure<PERSON><PERSON>d", "idx", "viewLinesAbove", "start", "deleteCount", "splice", "_invalidate", "insertArr", "min", "len", "sumAbove", "j", "remainder", "_prefixSumIndexOfResultBrand", "undefined"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/model/prefixSumComputer.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { arrayInsert } from '../../../base/common/arrays.js';\nimport { toUint32 } from '../../../base/common/uint.js';\nexport class PrefixSumComputer {\n    constructor(values) {\n        this.values = values;\n        this.prefixSum = new Uint32Array(values.length);\n        this.prefixSumValidIndex = new Int32Array(1);\n        this.prefixSumValidIndex[0] = -1;\n    }\n    insertValues(insertIndex, insertValues) {\n        insertIndex = toUint32(insertIndex);\n        const oldValues = this.values;\n        const oldPrefixSum = this.prefixSum;\n        const insertValuesLen = insertValues.length;\n        if (insertValuesLen === 0) {\n            return false;\n        }\n        this.values = new Uint32Array(oldValues.length + insertValuesLen);\n        this.values.set(oldValues.subarray(0, insertIndex), 0);\n        this.values.set(oldValues.subarray(insertIndex), insertIndex + insertValuesLen);\n        this.values.set(insertValues, insertIndex);\n        if (insertIndex - 1 < this.prefixSumValidIndex[0]) {\n            this.prefixSumValidIndex[0] = insertIndex - 1;\n        }\n        this.prefixSum = new Uint32Array(this.values.length);\n        if (this.prefixSumValidIndex[0] >= 0) {\n            this.prefixSum.set(oldPrefixSum.subarray(0, this.prefixSumValidIndex[0] + 1));\n        }\n        return true;\n    }\n    setValue(index, value) {\n        index = toUint32(index);\n        value = toUint32(value);\n        if (this.values[index] === value) {\n            return false;\n        }\n        this.values[index] = value;\n        if (index - 1 < this.prefixSumValidIndex[0]) {\n            this.prefixSumValidIndex[0] = index - 1;\n        }\n        return true;\n    }\n    removeValues(startIndex, count) {\n        startIndex = toUint32(startIndex);\n        count = toUint32(count);\n        const oldValues = this.values;\n        const oldPrefixSum = this.prefixSum;\n        if (startIndex >= oldValues.length) {\n            return false;\n        }\n        const maxCount = oldValues.length - startIndex;\n        if (count >= maxCount) {\n            count = maxCount;\n        }\n        if (count === 0) {\n            return false;\n        }\n        this.values = new Uint32Array(oldValues.length - count);\n        this.values.set(oldValues.subarray(0, startIndex), 0);\n        this.values.set(oldValues.subarray(startIndex + count), startIndex);\n        this.prefixSum = new Uint32Array(this.values.length);\n        if (startIndex - 1 < this.prefixSumValidIndex[0]) {\n            this.prefixSumValidIndex[0] = startIndex - 1;\n        }\n        if (this.prefixSumValidIndex[0] >= 0) {\n            this.prefixSum.set(oldPrefixSum.subarray(0, this.prefixSumValidIndex[0] + 1));\n        }\n        return true;\n    }\n    getTotalSum() {\n        if (this.values.length === 0) {\n            return 0;\n        }\n        return this._getPrefixSum(this.values.length - 1);\n    }\n    /**\n     * Returns the sum of the first `index + 1` many items.\n     * @returns `SUM(0 <= j <= index, values[j])`.\n     */\n    getPrefixSum(index) {\n        if (index < 0) {\n            return 0;\n        }\n        index = toUint32(index);\n        return this._getPrefixSum(index);\n    }\n    _getPrefixSum(index) {\n        if (index <= this.prefixSumValidIndex[0]) {\n            return this.prefixSum[index];\n        }\n        let startIndex = this.prefixSumValidIndex[0] + 1;\n        if (startIndex === 0) {\n            this.prefixSum[0] = this.values[0];\n            startIndex++;\n        }\n        if (index >= this.values.length) {\n            index = this.values.length - 1;\n        }\n        for (let i = startIndex; i <= index; i++) {\n            this.prefixSum[i] = this.prefixSum[i - 1] + this.values[i];\n        }\n        this.prefixSumValidIndex[0] = Math.max(this.prefixSumValidIndex[0], index);\n        return this.prefixSum[index];\n    }\n    getIndexOf(sum) {\n        sum = Math.floor(sum);\n        // Compute all sums (to get a fully valid prefixSum)\n        this.getTotalSum();\n        let low = 0;\n        let high = this.values.length - 1;\n        let mid = 0;\n        let midStop = 0;\n        let midStart = 0;\n        while (low <= high) {\n            mid = low + ((high - low) / 2) | 0;\n            midStop = this.prefixSum[mid];\n            midStart = midStop - this.values[mid];\n            if (sum < midStart) {\n                high = mid - 1;\n            }\n            else if (sum >= midStop) {\n                low = mid + 1;\n            }\n            else {\n                break;\n            }\n        }\n        return new PrefixSumIndexOfResult(mid, sum - midStart);\n    }\n}\n/**\n * {@link getIndexOf} has an amortized runtime complexity of O(1).\n *\n * ({@link PrefixSumComputer.getIndexOf} is just  O(log n))\n*/\nexport class ConstantTimePrefixSumComputer {\n    constructor(values) {\n        this._values = values;\n        this._isValid = false;\n        this._validEndIndex = -1;\n        this._prefixSum = [];\n        this._indexBySum = [];\n    }\n    /**\n     * @returns SUM(0 <= j < values.length, values[j])\n     */\n    getTotalSum() {\n        this._ensureValid();\n        return this._indexBySum.length;\n    }\n    /**\n     * Returns the sum of the first `count` many items.\n     * @returns `SUM(0 <= j < count, values[j])`.\n     */\n    getPrefixSum(count) {\n        this._ensureValid();\n        if (count === 0) {\n            return 0;\n        }\n        return this._prefixSum[count - 1];\n    }\n    /**\n     * @returns `result`, such that `getPrefixSum(result.index) + result.remainder = sum`\n     */\n    getIndexOf(sum) {\n        this._ensureValid();\n        const idx = this._indexBySum[sum];\n        const viewLinesAbove = idx > 0 ? this._prefixSum[idx - 1] : 0;\n        return new PrefixSumIndexOfResult(idx, sum - viewLinesAbove);\n    }\n    removeValues(start, deleteCount) {\n        this._values.splice(start, deleteCount);\n        this._invalidate(start);\n    }\n    insertValues(insertIndex, insertArr) {\n        this._values = arrayInsert(this._values, insertIndex, insertArr);\n        this._invalidate(insertIndex);\n    }\n    _invalidate(index) {\n        this._isValid = false;\n        this._validEndIndex = Math.min(this._validEndIndex, index - 1);\n    }\n    _ensureValid() {\n        if (this._isValid) {\n            return;\n        }\n        for (let i = this._validEndIndex + 1, len = this._values.length; i < len; i++) {\n            const value = this._values[i];\n            const sumAbove = i > 0 ? this._prefixSum[i - 1] : 0;\n            this._prefixSum[i] = sumAbove + value;\n            for (let j = 0; j < value; j++) {\n                this._indexBySum[sumAbove + j] = i;\n            }\n        }\n        // trim things\n        this._prefixSum.length = this._values.length;\n        this._indexBySum.length = this._prefixSum[this._prefixSum.length - 1];\n        // mark as valid\n        this._isValid = true;\n        this._validEndIndex = this._values.length - 1;\n    }\n    setValue(index, value) {\n        if (this._values[index] === value) {\n            // no change\n            return;\n        }\n        this._values[index] = value;\n        this._invalidate(index);\n    }\n}\nexport class PrefixSumIndexOfResult {\n    constructor(index, remainder) {\n        this.index = index;\n        this.remainder = remainder;\n        this._prefixSumIndexOfResultBrand = undefined;\n        this.index = index;\n        this.remainder = remainder;\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,WAAW,QAAQ,gCAAgC;AAC5D,SAASC,QAAQ,QAAQ,8BAA8B;AACvD,OAAO,MAAMC,iBAAiB,CAAC;EAC3BC,WAAWA,CAACC,MAAM,EAAE;IAChB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,SAAS,GAAG,IAAIC,WAAW,CAACF,MAAM,CAACG,MAAM,CAAC;IAC/C,IAAI,CAACC,mBAAmB,GAAG,IAAIC,UAAU,CAAC,CAAC,CAAC;IAC5C,IAAI,CAACD,mBAAmB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACpC;EACAE,YAAYA,CAACC,WAAW,EAAED,YAAY,EAAE;IACpCC,WAAW,GAAGV,QAAQ,CAACU,WAAW,CAAC;IACnC,MAAMC,SAAS,GAAG,IAAI,CAACR,MAAM;IAC7B,MAAMS,YAAY,GAAG,IAAI,CAACR,SAAS;IACnC,MAAMS,eAAe,GAAGJ,YAAY,CAACH,MAAM;IAC3C,IAAIO,eAAe,KAAK,CAAC,EAAE;MACvB,OAAO,KAAK;IAChB;IACA,IAAI,CAACV,MAAM,GAAG,IAAIE,WAAW,CAACM,SAAS,CAACL,MAAM,GAAGO,eAAe,CAAC;IACjE,IAAI,CAACV,MAAM,CAACW,GAAG,CAACH,SAAS,CAACI,QAAQ,CAAC,CAAC,EAAEL,WAAW,CAAC,EAAE,CAAC,CAAC;IACtD,IAAI,CAACP,MAAM,CAACW,GAAG,CAACH,SAAS,CAACI,QAAQ,CAACL,WAAW,CAAC,EAAEA,WAAW,GAAGG,eAAe,CAAC;IAC/E,IAAI,CAACV,MAAM,CAACW,GAAG,CAACL,YAAY,EAAEC,WAAW,CAAC;IAC1C,IAAIA,WAAW,GAAG,CAAC,GAAG,IAAI,CAACH,mBAAmB,CAAC,CAAC,CAAC,EAAE;MAC/C,IAAI,CAACA,mBAAmB,CAAC,CAAC,CAAC,GAAGG,WAAW,GAAG,CAAC;IACjD;IACA,IAAI,CAACN,SAAS,GAAG,IAAIC,WAAW,CAAC,IAAI,CAACF,MAAM,CAACG,MAAM,CAAC;IACpD,IAAI,IAAI,CAACC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;MAClC,IAAI,CAACH,SAAS,CAACU,GAAG,CAACF,YAAY,CAACG,QAAQ,CAAC,CAAC,EAAE,IAAI,CAACR,mBAAmB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IACjF;IACA,OAAO,IAAI;EACf;EACAS,QAAQA,CAACC,KAAK,EAAEC,KAAK,EAAE;IACnBD,KAAK,GAAGjB,QAAQ,CAACiB,KAAK,CAAC;IACvBC,KAAK,GAAGlB,QAAQ,CAACkB,KAAK,CAAC;IACvB,IAAI,IAAI,CAACf,MAAM,CAACc,KAAK,CAAC,KAAKC,KAAK,EAAE;MAC9B,OAAO,KAAK;IAChB;IACA,IAAI,CAACf,MAAM,CAACc,KAAK,CAAC,GAAGC,KAAK;IAC1B,IAAID,KAAK,GAAG,CAAC,GAAG,IAAI,CAACV,mBAAmB,CAAC,CAAC,CAAC,EAAE;MACzC,IAAI,CAACA,mBAAmB,CAAC,CAAC,CAAC,GAAGU,KAAK,GAAG,CAAC;IAC3C;IACA,OAAO,IAAI;EACf;EACAE,YAAYA,CAACC,UAAU,EAAEC,KAAK,EAAE;IAC5BD,UAAU,GAAGpB,QAAQ,CAACoB,UAAU,CAAC;IACjCC,KAAK,GAAGrB,QAAQ,CAACqB,KAAK,CAAC;IACvB,MAAMV,SAAS,GAAG,IAAI,CAACR,MAAM;IAC7B,MAAMS,YAAY,GAAG,IAAI,CAACR,SAAS;IACnC,IAAIgB,UAAU,IAAIT,SAAS,CAACL,MAAM,EAAE;MAChC,OAAO,KAAK;IAChB;IACA,MAAMgB,QAAQ,GAAGX,SAAS,CAACL,MAAM,GAAGc,UAAU;IAC9C,IAAIC,KAAK,IAAIC,QAAQ,EAAE;MACnBD,KAAK,GAAGC,QAAQ;IACpB;IACA,IAAID,KAAK,KAAK,CAAC,EAAE;MACb,OAAO,KAAK;IAChB;IACA,IAAI,CAAClB,MAAM,GAAG,IAAIE,WAAW,CAACM,SAAS,CAACL,MAAM,GAAGe,KAAK,CAAC;IACvD,IAAI,CAAClB,MAAM,CAACW,GAAG,CAACH,SAAS,CAACI,QAAQ,CAAC,CAAC,EAAEK,UAAU,CAAC,EAAE,CAAC,CAAC;IACrD,IAAI,CAACjB,MAAM,CAACW,GAAG,CAACH,SAAS,CAACI,QAAQ,CAACK,UAAU,GAAGC,KAAK,CAAC,EAAED,UAAU,CAAC;IACnE,IAAI,CAAChB,SAAS,GAAG,IAAIC,WAAW,CAAC,IAAI,CAACF,MAAM,CAACG,MAAM,CAAC;IACpD,IAAIc,UAAU,GAAG,CAAC,GAAG,IAAI,CAACb,mBAAmB,CAAC,CAAC,CAAC,EAAE;MAC9C,IAAI,CAACA,mBAAmB,CAAC,CAAC,CAAC,GAAGa,UAAU,GAAG,CAAC;IAChD;IACA,IAAI,IAAI,CAACb,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;MAClC,IAAI,CAACH,SAAS,CAACU,GAAG,CAACF,YAAY,CAACG,QAAQ,CAAC,CAAC,EAAE,IAAI,CAACR,mBAAmB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IACjF;IACA,OAAO,IAAI;EACf;EACAgB,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACpB,MAAM,CAACG,MAAM,KAAK,CAAC,EAAE;MAC1B,OAAO,CAAC;IACZ;IACA,OAAO,IAAI,CAACkB,aAAa,CAAC,IAAI,CAACrB,MAAM,CAACG,MAAM,GAAG,CAAC,CAAC;EACrD;EACA;AACJ;AACA;AACA;EACImB,YAAYA,CAACR,KAAK,EAAE;IAChB,IAAIA,KAAK,GAAG,CAAC,EAAE;MACX,OAAO,CAAC;IACZ;IACAA,KAAK,GAAGjB,QAAQ,CAACiB,KAAK,CAAC;IACvB,OAAO,IAAI,CAACO,aAAa,CAACP,KAAK,CAAC;EACpC;EACAO,aAAaA,CAACP,KAAK,EAAE;IACjB,IAAIA,KAAK,IAAI,IAAI,CAACV,mBAAmB,CAAC,CAAC,CAAC,EAAE;MACtC,OAAO,IAAI,CAACH,SAAS,CAACa,KAAK,CAAC;IAChC;IACA,IAAIG,UAAU,GAAG,IAAI,CAACb,mBAAmB,CAAC,CAAC,CAAC,GAAG,CAAC;IAChD,IAAIa,UAAU,KAAK,CAAC,EAAE;MAClB,IAAI,CAAChB,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAACD,MAAM,CAAC,CAAC,CAAC;MAClCiB,UAAU,EAAE;IAChB;IACA,IAAIH,KAAK,IAAI,IAAI,CAACd,MAAM,CAACG,MAAM,EAAE;MAC7BW,KAAK,GAAG,IAAI,CAACd,MAAM,CAACG,MAAM,GAAG,CAAC;IAClC;IACA,KAAK,IAAIoB,CAAC,GAAGN,UAAU,EAAEM,CAAC,IAAIT,KAAK,EAAES,CAAC,EAAE,EAAE;MACtC,IAAI,CAACtB,SAAS,CAACsB,CAAC,CAAC,GAAG,IAAI,CAACtB,SAAS,CAACsB,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAACvB,MAAM,CAACuB,CAAC,CAAC;IAC9D;IACA,IAAI,CAACnB,mBAAmB,CAAC,CAAC,CAAC,GAAGoB,IAAI,CAACC,GAAG,CAAC,IAAI,CAACrB,mBAAmB,CAAC,CAAC,CAAC,EAAEU,KAAK,CAAC;IAC1E,OAAO,IAAI,CAACb,SAAS,CAACa,KAAK,CAAC;EAChC;EACAY,UAAUA,CAACC,GAAG,EAAE;IACZA,GAAG,GAAGH,IAAI,CAACI,KAAK,CAACD,GAAG,CAAC;IACrB;IACA,IAAI,CAACP,WAAW,CAAC,CAAC;IAClB,IAAIS,GAAG,GAAG,CAAC;IACX,IAAIC,IAAI,GAAG,IAAI,CAAC9B,MAAM,CAACG,MAAM,GAAG,CAAC;IACjC,IAAI4B,GAAG,GAAG,CAAC;IACX,IAAIC,OAAO,GAAG,CAAC;IACf,IAAIC,QAAQ,GAAG,CAAC;IAChB,OAAOJ,GAAG,IAAIC,IAAI,EAAE;MAChBC,GAAG,GAAGF,GAAG,GAAI,CAACC,IAAI,GAAGD,GAAG,IAAI,CAAE,GAAG,CAAC;MAClCG,OAAO,GAAG,IAAI,CAAC/B,SAAS,CAAC8B,GAAG,CAAC;MAC7BE,QAAQ,GAAGD,OAAO,GAAG,IAAI,CAAChC,MAAM,CAAC+B,GAAG,CAAC;MACrC,IAAIJ,GAAG,GAAGM,QAAQ,EAAE;QAChBH,IAAI,GAAGC,GAAG,GAAG,CAAC;MAClB,CAAC,MACI,IAAIJ,GAAG,IAAIK,OAAO,EAAE;QACrBH,GAAG,GAAGE,GAAG,GAAG,CAAC;MACjB,CAAC,MACI;QACD;MACJ;IACJ;IACA,OAAO,IAAIG,sBAAsB,CAACH,GAAG,EAAEJ,GAAG,GAAGM,QAAQ,CAAC;EAC1D;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAME,6BAA6B,CAAC;EACvCpC,WAAWA,CAACC,MAAM,EAAE;IAChB,IAAI,CAACoC,OAAO,GAAGpC,MAAM;IACrB,IAAI,CAACqC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,cAAc,GAAG,CAAC,CAAC;IACxB,IAAI,CAACC,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,WAAW,GAAG,EAAE;EACzB;EACA;AACJ;AACA;EACIpB,WAAWA,CAAA,EAAG;IACV,IAAI,CAACqB,YAAY,CAAC,CAAC;IACnB,OAAO,IAAI,CAACD,WAAW,CAACrC,MAAM;EAClC;EACA;AACJ;AACA;AACA;EACImB,YAAYA,CAACJ,KAAK,EAAE;IAChB,IAAI,CAACuB,YAAY,CAAC,CAAC;IACnB,IAAIvB,KAAK,KAAK,CAAC,EAAE;MACb,OAAO,CAAC;IACZ;IACA,OAAO,IAAI,CAACqB,UAAU,CAACrB,KAAK,GAAG,CAAC,CAAC;EACrC;EACA;AACJ;AACA;EACIQ,UAAUA,CAACC,GAAG,EAAE;IACZ,IAAI,CAACc,YAAY,CAAC,CAAC;IACnB,MAAMC,GAAG,GAAG,IAAI,CAACF,WAAW,CAACb,GAAG,CAAC;IACjC,MAAMgB,cAAc,GAAGD,GAAG,GAAG,CAAC,GAAG,IAAI,CAACH,UAAU,CAACG,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC;IAC7D,OAAO,IAAIR,sBAAsB,CAACQ,GAAG,EAAEf,GAAG,GAAGgB,cAAc,CAAC;EAChE;EACA3B,YAAYA,CAAC4B,KAAK,EAAEC,WAAW,EAAE;IAC7B,IAAI,CAACT,OAAO,CAACU,MAAM,CAACF,KAAK,EAAEC,WAAW,CAAC;IACvC,IAAI,CAACE,WAAW,CAACH,KAAK,CAAC;EAC3B;EACAtC,YAAYA,CAACC,WAAW,EAAEyC,SAAS,EAAE;IACjC,IAAI,CAACZ,OAAO,GAAGxC,WAAW,CAAC,IAAI,CAACwC,OAAO,EAAE7B,WAAW,EAAEyC,SAAS,CAAC;IAChE,IAAI,CAACD,WAAW,CAACxC,WAAW,CAAC;EACjC;EACAwC,WAAWA,CAACjC,KAAK,EAAE;IACf,IAAI,CAACuB,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,cAAc,GAAGd,IAAI,CAACyB,GAAG,CAAC,IAAI,CAACX,cAAc,EAAExB,KAAK,GAAG,CAAC,CAAC;EAClE;EACA2B,YAAYA,CAAA,EAAG;IACX,IAAI,IAAI,CAACJ,QAAQ,EAAE;MACf;IACJ;IACA,KAAK,IAAId,CAAC,GAAG,IAAI,CAACe,cAAc,GAAG,CAAC,EAAEY,GAAG,GAAG,IAAI,CAACd,OAAO,CAACjC,MAAM,EAAEoB,CAAC,GAAG2B,GAAG,EAAE3B,CAAC,EAAE,EAAE;MAC3E,MAAMR,KAAK,GAAG,IAAI,CAACqB,OAAO,CAACb,CAAC,CAAC;MAC7B,MAAM4B,QAAQ,GAAG5B,CAAC,GAAG,CAAC,GAAG,IAAI,CAACgB,UAAU,CAAChB,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;MACnD,IAAI,CAACgB,UAAU,CAAChB,CAAC,CAAC,GAAG4B,QAAQ,GAAGpC,KAAK;MACrC,KAAK,IAAIqC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrC,KAAK,EAAEqC,CAAC,EAAE,EAAE;QAC5B,IAAI,CAACZ,WAAW,CAACW,QAAQ,GAAGC,CAAC,CAAC,GAAG7B,CAAC;MACtC;IACJ;IACA;IACA,IAAI,CAACgB,UAAU,CAACpC,MAAM,GAAG,IAAI,CAACiC,OAAO,CAACjC,MAAM;IAC5C,IAAI,CAACqC,WAAW,CAACrC,MAAM,GAAG,IAAI,CAACoC,UAAU,CAAC,IAAI,CAACA,UAAU,CAACpC,MAAM,GAAG,CAAC,CAAC;IACrE;IACA,IAAI,CAACkC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,cAAc,GAAG,IAAI,CAACF,OAAO,CAACjC,MAAM,GAAG,CAAC;EACjD;EACAU,QAAQA,CAACC,KAAK,EAAEC,KAAK,EAAE;IACnB,IAAI,IAAI,CAACqB,OAAO,CAACtB,KAAK,CAAC,KAAKC,KAAK,EAAE;MAC/B;MACA;IACJ;IACA,IAAI,CAACqB,OAAO,CAACtB,KAAK,CAAC,GAAGC,KAAK;IAC3B,IAAI,CAACgC,WAAW,CAACjC,KAAK,CAAC;EAC3B;AACJ;AACA,OAAO,MAAMoB,sBAAsB,CAAC;EAChCnC,WAAWA,CAACe,KAAK,EAAEuC,SAAS,EAAE;IAC1B,IAAI,CAACvC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACuC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,4BAA4B,GAAGC,SAAS;IAC7C,IAAI,CAACzC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACuC,SAAS,GAAGA,SAAS;EAC9B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}