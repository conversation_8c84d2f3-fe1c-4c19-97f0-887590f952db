{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nconst markRegex = new RegExp('\\\\bMARK:\\\\s*(.*)$', 'd');\nconst trimDashesRegex = /^-+|-+$/g;\n/**\n * Find section headers in the model.\n *\n * @param model the text model to search in\n * @param options options to search with\n * @returns an array of section headers\n */\nexport function findSectionHeaders(model, options) {\n  let headers = [];\n  if (options.findRegionSectionHeaders && options.foldingRules?.markers) {\n    const regionHeaders = collectRegionHeaders(model, options);\n    headers = headers.concat(regionHeaders);\n  }\n  if (options.findMarkSectionHeaders) {\n    const markHeaders = collectMarkHeaders(model);\n    headers = headers.concat(markHeaders);\n  }\n  return headers;\n}\nfunction collectRegionHeaders(model, options) {\n  const regionHeaders = [];\n  const endLineNumber = model.getLineCount();\n  for (let lineNumber = 1; lineNumber <= endLineNumber; lineNumber++) {\n    const lineContent = model.getLineContent(lineNumber);\n    const match = lineContent.match(options.foldingRules.markers.start);\n    if (match) {\n      const range = {\n        startLineNumber: lineNumber,\n        startColumn: match[0].length + 1,\n        endLineNumber: lineNumber,\n        endColumn: lineContent.length + 1\n      };\n      if (range.endColumn > range.startColumn) {\n        const sectionHeader = {\n          range,\n          ...getHeaderText(lineContent.substring(match[0].length)),\n          shouldBeInComments: false\n        };\n        if (sectionHeader.text || sectionHeader.hasSeparatorLine) {\n          regionHeaders.push(sectionHeader);\n        }\n      }\n    }\n  }\n  return regionHeaders;\n}\nfunction collectMarkHeaders(model) {\n  const markHeaders = [];\n  const endLineNumber = model.getLineCount();\n  for (let lineNumber = 1; lineNumber <= endLineNumber; lineNumber++) {\n    const lineContent = model.getLineContent(lineNumber);\n    addMarkHeaderIfFound(lineContent, lineNumber, markHeaders);\n  }\n  return markHeaders;\n}\nfunction addMarkHeaderIfFound(lineContent, lineNumber, sectionHeaders) {\n  markRegex.lastIndex = 0;\n  const match = markRegex.exec(lineContent);\n  if (match) {\n    const column = match.indices[1][0] + 1;\n    const endColumn = match.indices[1][1] + 1;\n    const range = {\n      startLineNumber: lineNumber,\n      startColumn: column,\n      endLineNumber: lineNumber,\n      endColumn: endColumn\n    };\n    if (range.endColumn > range.startColumn) {\n      const sectionHeader = {\n        range,\n        ...getHeaderText(match[1]),\n        shouldBeInComments: true\n      };\n      if (sectionHeader.text || sectionHeader.hasSeparatorLine) {\n        sectionHeaders.push(sectionHeader);\n      }\n    }\n  }\n}\nfunction getHeaderText(text) {\n  text = text.trim();\n  const hasSeparatorLine = text.startsWith('-');\n  text = text.replace(trimDashesRegex, '');\n  return {\n    text,\n    hasSeparatorLine\n  };\n}", "map": {"version": 3, "names": ["markRegex", "RegExp", "trimDashesRegex", "findSectionHeaders", "model", "options", "headers", "findRegionSectionHeaders", "foldingRules", "markers", "regionHeaders", "collectRegionHeaders", "concat", "findMarkSectionHeaders", "markHeaders", "collectMarkHeaders", "endLineNumber", "getLineCount", "lineNumber", "lineContent", "get<PERSON>ineC<PERSON>nt", "match", "start", "range", "startLineNumber", "startColumn", "length", "endColumn", "section<PERSON><PERSON><PERSON>", "getHeaderText", "substring", "shouldBeInComments", "text", "hasSeparatorLine", "push", "addMarkHeaderIfFound", "sectionHeaders", "lastIndex", "exec", "column", "indices", "trim", "startsWith", "replace"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/services/findSectionHeaders.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nconst markRegex = new RegExp('\\\\bMARK:\\\\s*(.*)$', 'd');\nconst trimDashesRegex = /^-+|-+$/g;\n/**\n * Find section headers in the model.\n *\n * @param model the text model to search in\n * @param options options to search with\n * @returns an array of section headers\n */\nexport function findSectionHeaders(model, options) {\n    let headers = [];\n    if (options.findRegionSectionHeaders && options.foldingRules?.markers) {\n        const regionHeaders = collectRegionHeaders(model, options);\n        headers = headers.concat(regionHeaders);\n    }\n    if (options.findMarkSectionHeaders) {\n        const markHeaders = collectMarkHeaders(model);\n        headers = headers.concat(markHeaders);\n    }\n    return headers;\n}\nfunction collectRegionHeaders(model, options) {\n    const regionHeaders = [];\n    const endLineNumber = model.getLineCount();\n    for (let lineNumber = 1; lineNumber <= endLineNumber; lineNumber++) {\n        const lineContent = model.getLineContent(lineNumber);\n        const match = lineContent.match(options.foldingRules.markers.start);\n        if (match) {\n            const range = { startLineNumber: lineNumber, startColumn: match[0].length + 1, endLineNumber: lineNumber, endColumn: lineContent.length + 1 };\n            if (range.endColumn > range.startColumn) {\n                const sectionHeader = {\n                    range,\n                    ...getHeaderText(lineContent.substring(match[0].length)),\n                    shouldBeInComments: false\n                };\n                if (sectionHeader.text || sectionHeader.hasSeparatorLine) {\n                    regionHeaders.push(sectionHeader);\n                }\n            }\n        }\n    }\n    return regionHeaders;\n}\nfunction collectMarkHeaders(model) {\n    const markHeaders = [];\n    const endLineNumber = model.getLineCount();\n    for (let lineNumber = 1; lineNumber <= endLineNumber; lineNumber++) {\n        const lineContent = model.getLineContent(lineNumber);\n        addMarkHeaderIfFound(lineContent, lineNumber, markHeaders);\n    }\n    return markHeaders;\n}\nfunction addMarkHeaderIfFound(lineContent, lineNumber, sectionHeaders) {\n    markRegex.lastIndex = 0;\n    const match = markRegex.exec(lineContent);\n    if (match) {\n        const column = match.indices[1][0] + 1;\n        const endColumn = match.indices[1][1] + 1;\n        const range = { startLineNumber: lineNumber, startColumn: column, endLineNumber: lineNumber, endColumn: endColumn };\n        if (range.endColumn > range.startColumn) {\n            const sectionHeader = {\n                range,\n                ...getHeaderText(match[1]),\n                shouldBeInComments: true\n            };\n            if (sectionHeader.text || sectionHeader.hasSeparatorLine) {\n                sectionHeaders.push(sectionHeader);\n            }\n        }\n    }\n}\nfunction getHeaderText(text) {\n    text = text.trim();\n    const hasSeparatorLine = text.startsWith('-');\n    text = text.replace(trimDashesRegex, '');\n    return { text, hasSeparatorLine };\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,MAAMA,SAAS,GAAG,IAAIC,MAAM,CAAC,mBAAmB,EAAE,GAAG,CAAC;AACtD,MAAMC,eAAe,GAAG,UAAU;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,kBAAkBA,CAACC,KAAK,EAAEC,OAAO,EAAE;EAC/C,IAAIC,OAAO,GAAG,EAAE;EAChB,IAAID,OAAO,CAACE,wBAAwB,IAAIF,OAAO,CAACG,YAAY,EAAEC,OAAO,EAAE;IACnE,MAAMC,aAAa,GAAGC,oBAAoB,CAACP,KAAK,EAAEC,OAAO,CAAC;IAC1DC,OAAO,GAAGA,OAAO,CAACM,MAAM,CAACF,aAAa,CAAC;EAC3C;EACA,IAAIL,OAAO,CAACQ,sBAAsB,EAAE;IAChC,MAAMC,WAAW,GAAGC,kBAAkB,CAACX,KAAK,CAAC;IAC7CE,OAAO,GAAGA,OAAO,CAACM,MAAM,CAACE,WAAW,CAAC;EACzC;EACA,OAAOR,OAAO;AAClB;AACA,SAASK,oBAAoBA,CAACP,KAAK,EAAEC,OAAO,EAAE;EAC1C,MAAMK,aAAa,GAAG,EAAE;EACxB,MAAMM,aAAa,GAAGZ,KAAK,CAACa,YAAY,CAAC,CAAC;EAC1C,KAAK,IAAIC,UAAU,GAAG,CAAC,EAAEA,UAAU,IAAIF,aAAa,EAAEE,UAAU,EAAE,EAAE;IAChE,MAAMC,WAAW,GAAGf,KAAK,CAACgB,cAAc,CAACF,UAAU,CAAC;IACpD,MAAMG,KAAK,GAAGF,WAAW,CAACE,KAAK,CAAChB,OAAO,CAACG,YAAY,CAACC,OAAO,CAACa,KAAK,CAAC;IACnE,IAAID,KAAK,EAAE;MACP,MAAME,KAAK,GAAG;QAAEC,eAAe,EAAEN,UAAU;QAAEO,WAAW,EAAEJ,KAAK,CAAC,CAAC,CAAC,CAACK,MAAM,GAAG,CAAC;QAAEV,aAAa,EAAEE,UAAU;QAAES,SAAS,EAAER,WAAW,CAACO,MAAM,GAAG;MAAE,CAAC;MAC7I,IAAIH,KAAK,CAACI,SAAS,GAAGJ,KAAK,CAACE,WAAW,EAAE;QACrC,MAAMG,aAAa,GAAG;UAClBL,KAAK;UACL,GAAGM,aAAa,CAACV,WAAW,CAACW,SAAS,CAACT,KAAK,CAAC,CAAC,CAAC,CAACK,MAAM,CAAC,CAAC;UACxDK,kBAAkB,EAAE;QACxB,CAAC;QACD,IAAIH,aAAa,CAACI,IAAI,IAAIJ,aAAa,CAACK,gBAAgB,EAAE;UACtDvB,aAAa,CAACwB,IAAI,CAACN,aAAa,CAAC;QACrC;MACJ;IACJ;EACJ;EACA,OAAOlB,aAAa;AACxB;AACA,SAASK,kBAAkBA,CAACX,KAAK,EAAE;EAC/B,MAAMU,WAAW,GAAG,EAAE;EACtB,MAAME,aAAa,GAAGZ,KAAK,CAACa,YAAY,CAAC,CAAC;EAC1C,KAAK,IAAIC,UAAU,GAAG,CAAC,EAAEA,UAAU,IAAIF,aAAa,EAAEE,UAAU,EAAE,EAAE;IAChE,MAAMC,WAAW,GAAGf,KAAK,CAACgB,cAAc,CAACF,UAAU,CAAC;IACpDiB,oBAAoB,CAAChB,WAAW,EAAED,UAAU,EAAEJ,WAAW,CAAC;EAC9D;EACA,OAAOA,WAAW;AACtB;AACA,SAASqB,oBAAoBA,CAAChB,WAAW,EAAED,UAAU,EAAEkB,cAAc,EAAE;EACnEpC,SAAS,CAACqC,SAAS,GAAG,CAAC;EACvB,MAAMhB,KAAK,GAAGrB,SAAS,CAACsC,IAAI,CAACnB,WAAW,CAAC;EACzC,IAAIE,KAAK,EAAE;IACP,MAAMkB,MAAM,GAAGlB,KAAK,CAACmB,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;IACtC,MAAMb,SAAS,GAAGN,KAAK,CAACmB,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;IACzC,MAAMjB,KAAK,GAAG;MAAEC,eAAe,EAAEN,UAAU;MAAEO,WAAW,EAAEc,MAAM;MAAEvB,aAAa,EAAEE,UAAU;MAAES,SAAS,EAAEA;IAAU,CAAC;IACnH,IAAIJ,KAAK,CAACI,SAAS,GAAGJ,KAAK,CAACE,WAAW,EAAE;MACrC,MAAMG,aAAa,GAAG;QAClBL,KAAK;QACL,GAAGM,aAAa,CAACR,KAAK,CAAC,CAAC,CAAC,CAAC;QAC1BU,kBAAkB,EAAE;MACxB,CAAC;MACD,IAAIH,aAAa,CAACI,IAAI,IAAIJ,aAAa,CAACK,gBAAgB,EAAE;QACtDG,cAAc,CAACF,IAAI,CAACN,aAAa,CAAC;MACtC;IACJ;EACJ;AACJ;AACA,SAASC,aAAaA,CAACG,IAAI,EAAE;EACzBA,IAAI,GAAGA,IAAI,CAACS,IAAI,CAAC,CAAC;EAClB,MAAMR,gBAAgB,GAAGD,IAAI,CAACU,UAAU,CAAC,GAAG,CAAC;EAC7CV,IAAI,GAAGA,IAAI,CAACW,OAAO,CAACzC,eAAe,EAAE,EAAE,CAAC;EACxC,OAAO;IAAE8B,IAAI;IAAEC;EAAiB,CAAC;AACrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}