{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as strings from '../../../../base/common/strings.js';\nimport { createScopedLineTokens } from '../supports.js';\nimport { LineTokens } from '../../tokens/lineTokens.js';\n/**\n * This class is a wrapper class around {@link IndentRulesSupport}.\n * It processes the lines by removing the language configuration brackets from the regex, string and comment tokens.\n * It then calls into the {@link IndentRulesSupport} to validate the indentation conditions.\n */\nexport class ProcessedIndentRulesSupport {\n  constructor(model, indentRulesSupport, languageConfigurationService) {\n    this._indentRulesSupport = indentRulesSupport;\n    this._indentationLineProcessor = new IndentationLineProcessor(model, languageConfigurationService);\n  }\n  /**\n   * Apply the new indentation and return whether the indentation level should be increased after the given line number\n   */\n  shouldIncrease(lineNumber, newIndentation) {\n    const processedLine = this._indentationLineProcessor.getProcessedLine(lineNumber, newIndentation);\n    return this._indentRulesSupport.shouldIncrease(processedLine);\n  }\n  /**\n   * Apply the new indentation and return whether the indentation level should be decreased after the given line number\n   */\n  shouldDecrease(lineNumber, newIndentation) {\n    const processedLine = this._indentationLineProcessor.getProcessedLine(lineNumber, newIndentation);\n    return this._indentRulesSupport.shouldDecrease(processedLine);\n  }\n  /**\n   * Apply the new indentation and return whether the indentation level should remain unchanged at the given line number\n   */\n  shouldIgnore(lineNumber, newIndentation) {\n    const processedLine = this._indentationLineProcessor.getProcessedLine(lineNumber, newIndentation);\n    return this._indentRulesSupport.shouldIgnore(processedLine);\n  }\n  /**\n   * Apply the new indentation and return whether the indentation level should increase on the line after the given line number\n   */\n  shouldIndentNextLine(lineNumber, newIndentation) {\n    const processedLine = this._indentationLineProcessor.getProcessedLine(lineNumber, newIndentation);\n    return this._indentRulesSupport.shouldIndentNextLine(processedLine);\n  }\n}\n/**\n * This class fetches the processed text around a range which can be used for indentation evaluation.\n * It returns:\n * - The processed text before the given range and on the same start line\n * - The processed text after the given range and on the same end line\n * - The processed text on the previous line\n */\nexport class IndentationContextProcessor {\n  constructor(model, languageConfigurationService) {\n    this.model = model;\n    this.indentationLineProcessor = new IndentationLineProcessor(model, languageConfigurationService);\n  }\n  /**\n   * Returns the processed text, stripped from the language configuration brackets within the string, comment and regex tokens, around the given range\n   */\n  getProcessedTokenContextAroundRange(range) {\n    const beforeRangeProcessedTokens = this._getProcessedTokensBeforeRange(range);\n    const afterRangeProcessedTokens = this._getProcessedTokensAfterRange(range);\n    const previousLineProcessedTokens = this._getProcessedPreviousLineTokens(range);\n    return {\n      beforeRangeProcessedTokens,\n      afterRangeProcessedTokens,\n      previousLineProcessedTokens\n    };\n  }\n  _getProcessedTokensBeforeRange(range) {\n    this.model.tokenization.forceTokenization(range.startLineNumber);\n    const lineTokens = this.model.tokenization.getLineTokens(range.startLineNumber);\n    const scopedLineTokens = createScopedLineTokens(lineTokens, range.startColumn - 1);\n    let slicedTokens;\n    if (isLanguageDifferentFromLineStart(this.model, range.getStartPosition())) {\n      const columnIndexWithinScope = range.startColumn - 1 - scopedLineTokens.firstCharOffset;\n      const firstCharacterOffset = scopedLineTokens.firstCharOffset;\n      const lastCharacterOffset = firstCharacterOffset + columnIndexWithinScope;\n      slicedTokens = lineTokens.sliceAndInflate(firstCharacterOffset, lastCharacterOffset, 0);\n    } else {\n      const columnWithinLine = range.startColumn - 1;\n      slicedTokens = lineTokens.sliceAndInflate(0, columnWithinLine, 0);\n    }\n    const processedTokens = this.indentationLineProcessor.getProcessedTokens(slicedTokens);\n    return processedTokens;\n  }\n  _getProcessedTokensAfterRange(range) {\n    const position = range.isEmpty() ? range.getStartPosition() : range.getEndPosition();\n    this.model.tokenization.forceTokenization(position.lineNumber);\n    const lineTokens = this.model.tokenization.getLineTokens(position.lineNumber);\n    const scopedLineTokens = createScopedLineTokens(lineTokens, position.column - 1);\n    const columnIndexWithinScope = position.column - 1 - scopedLineTokens.firstCharOffset;\n    const firstCharacterOffset = scopedLineTokens.firstCharOffset + columnIndexWithinScope;\n    const lastCharacterOffset = scopedLineTokens.firstCharOffset + scopedLineTokens.getLineLength();\n    const slicedTokens = lineTokens.sliceAndInflate(firstCharacterOffset, lastCharacterOffset, 0);\n    const processedTokens = this.indentationLineProcessor.getProcessedTokens(slicedTokens);\n    return processedTokens;\n  }\n  _getProcessedPreviousLineTokens(range) {\n    const getScopedLineTokensAtEndColumnOfLine = lineNumber => {\n      this.model.tokenization.forceTokenization(lineNumber);\n      const lineTokens = this.model.tokenization.getLineTokens(lineNumber);\n      const endColumnOfLine = this.model.getLineMaxColumn(lineNumber) - 1;\n      const scopedLineTokensAtEndColumn = createScopedLineTokens(lineTokens, endColumnOfLine);\n      return scopedLineTokensAtEndColumn;\n    };\n    this.model.tokenization.forceTokenization(range.startLineNumber);\n    const lineTokens = this.model.tokenization.getLineTokens(range.startLineNumber);\n    const scopedLineTokens = createScopedLineTokens(lineTokens, range.startColumn - 1);\n    const emptyTokens = LineTokens.createEmpty('', scopedLineTokens.languageIdCodec);\n    const previousLineNumber = range.startLineNumber - 1;\n    const isFirstLine = previousLineNumber === 0;\n    if (isFirstLine) {\n      return emptyTokens;\n    }\n    const canScopeExtendOnPreviousLine = scopedLineTokens.firstCharOffset === 0;\n    if (!canScopeExtendOnPreviousLine) {\n      return emptyTokens;\n    }\n    const scopedLineTokensAtEndColumnOfPreviousLine = getScopedLineTokensAtEndColumnOfLine(previousLineNumber);\n    const doesLanguageContinueOnPreviousLine = scopedLineTokens.languageId === scopedLineTokensAtEndColumnOfPreviousLine.languageId;\n    if (!doesLanguageContinueOnPreviousLine) {\n      return emptyTokens;\n    }\n    const previousSlicedLineTokens = scopedLineTokensAtEndColumnOfPreviousLine.toIViewLineTokens();\n    const processedTokens = this.indentationLineProcessor.getProcessedTokens(previousSlicedLineTokens);\n    return processedTokens;\n  }\n}\n/**\n * This class performs the actual processing of the indentation lines.\n * The brackets of the language configuration are removed from the regex, string and comment tokens.\n */\nclass IndentationLineProcessor {\n  constructor(model, languageConfigurationService) {\n    this.model = model;\n    this.languageConfigurationService = languageConfigurationService;\n  }\n  /**\n   * Get the processed line for the given line number and potentially adjust the indentation level.\n   * Remove the language configuration brackets from the regex, string and comment tokens.\n   */\n  getProcessedLine(lineNumber, newIndentation) {\n    const replaceIndentation = (line, newIndentation) => {\n      const currentIndentation = strings.getLeadingWhitespace(line);\n      const adjustedLine = newIndentation + line.substring(currentIndentation.length);\n      return adjustedLine;\n    };\n    this.model.tokenization.forceTokenization?.(lineNumber);\n    const tokens = this.model.tokenization.getLineTokens(lineNumber);\n    let processedLine = this.getProcessedTokens(tokens).getLineContent();\n    if (newIndentation !== undefined) {\n      processedLine = replaceIndentation(processedLine, newIndentation);\n    }\n    return processedLine;\n  }\n  /**\n   * Process the line with the given tokens, remove the language configuration brackets from the regex, string and comment tokens.\n   */\n  getProcessedTokens(tokens) {\n    const shouldRemoveBracketsFromTokenType = tokenType => {\n      return tokenType === 2 /* StandardTokenType.String */ || tokenType === 3 /* StandardTokenType.RegEx */ || tokenType === 1 /* StandardTokenType.Comment */;\n    };\n    const languageId = tokens.getLanguageId(0);\n    const bracketsConfiguration = this.languageConfigurationService.getLanguageConfiguration(languageId).bracketsNew;\n    const bracketsRegExp = bracketsConfiguration.getBracketRegExp({\n      global: true\n    });\n    const textAndMetadata = [];\n    tokens.forEach(tokenIndex => {\n      const tokenType = tokens.getStandardTokenType(tokenIndex);\n      let text = tokens.getTokenText(tokenIndex);\n      if (shouldRemoveBracketsFromTokenType(tokenType)) {\n        text = text.replace(bracketsRegExp, '');\n      }\n      const metadata = tokens.getMetadata(tokenIndex);\n      textAndMetadata.push({\n        text,\n        metadata\n      });\n    });\n    const processedLineTokens = LineTokens.createFromTextAndMetadata(textAndMetadata, tokens.languageIdCodec);\n    return processedLineTokens;\n  }\n}\nexport function isLanguageDifferentFromLineStart(model, position) {\n  model.tokenization.forceTokenization(position.lineNumber);\n  const lineTokens = model.tokenization.getLineTokens(position.lineNumber);\n  const scopedLineTokens = createScopedLineTokens(lineTokens, position.column - 1);\n  const doesScopeStartAtOffsetZero = scopedLineTokens.firstCharOffset === 0;\n  const isScopedLanguageEqualToFirstLanguageOnLine = lineTokens.getLanguageId(0) === scopedLineTokens.languageId;\n  const languageIsDifferentFromLineStart = !doesScopeStartAtOffsetZero && !isScopedLanguageEqualToFirstLanguageOnLine;\n  return languageIsDifferentFromLineStart;\n}", "map": {"version": 3, "names": ["strings", "createScopedLineTokens", "LineTokens", "ProcessedIndentRulesSupport", "constructor", "model", "indentRulesSupport", "languageConfigurationService", "_indentRulesSupport", "_indentationLineProcessor", "IndentationLineProcessor", "shouldIncrease", "lineNumber", "newIndentation", "processedLine", "getProcessedLine", "shouldDecrease", "shouldIgnore", "shouldIndentNextLine", "IndentationContextProcessor", "indentationLineProcessor", "getProcessedTokenContextAroundRange", "range", "beforeRangeProcessedTokens", "_getProcessedTokensBeforeRange", "afterRangeProcessedTokens", "_getProcessedTokensAfterRange", "previousLineProcessedTokens", "_getProcessedPreviousLineTokens", "tokenization", "forceTokenization", "startLineNumber", "lineTokens", "getLineTokens", "scopedLineTokens", "startColumn", "slicedTokens", "isLanguageDifferentFromLineStart", "getStartPosition", "columnIndexWithinScope", "firstCharOffset", "firstCharacterOffset", "lastCharacterOffset", "sliceAndInflate", "columnWithinLine", "processedTokens", "getProcessedTokens", "position", "isEmpty", "getEndPosition", "column", "getLine<PERSON><PERSON>th", "getScopedLineTokensAtEndColumnOfLine", "endColumnOfLine", "getLineMaxColumn", "scopedLineTokensAtEndColumn", "emptyTokens", "createEmpty", "languageIdCodec", "previousLineNumber", "isFirstLine", "canScopeExtendOnPreviousLine", "scopedLineTokensAtEndColumnOfPreviousLine", "doesLanguageContinueOnPreviousLine", "languageId", "previousSlicedLineTokens", "toIViewLineTokens", "replaceIndentation", "line", "currentIndentation", "getLeadingWhitespace", "adjustedLine", "substring", "length", "tokens", "get<PERSON>ineC<PERSON>nt", "undefined", "shouldRemoveBracketsFromTokenType", "tokenType", "getLanguageId", "bracketsConfiguration", "getLanguageConfiguration", "bracketsNew", "bracketsRegExp", "getBracketRegExp", "global", "textAndMetadata", "for<PERSON>ach", "tokenIndex", "getStandardTokenType", "text", "getTokenText", "replace", "metadata", "getMetadata", "push", "processedLineTokens", "createFromTextAndMetadata", "doesScopeStartAtOffsetZero", "isScopedLanguageEqualToFirstLanguageOnLine", "languageIsDifferentFromLineStart"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/languages/supports/indentationLineProcessor.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as strings from '../../../../base/common/strings.js';\nimport { createScopedLineTokens } from '../supports.js';\nimport { LineTokens } from '../../tokens/lineTokens.js';\n/**\n * This class is a wrapper class around {@link IndentRulesSupport}.\n * It processes the lines by removing the language configuration brackets from the regex, string and comment tokens.\n * It then calls into the {@link IndentRulesSupport} to validate the indentation conditions.\n */\nexport class ProcessedIndentRulesSupport {\n    constructor(model, indentRulesSupport, languageConfigurationService) {\n        this._indentRulesSupport = indentRulesSupport;\n        this._indentationLineProcessor = new IndentationLineProcessor(model, languageConfigurationService);\n    }\n    /**\n     * Apply the new indentation and return whether the indentation level should be increased after the given line number\n     */\n    shouldIncrease(lineNumber, newIndentation) {\n        const processedLine = this._indentationLineProcessor.getProcessedLine(lineNumber, newIndentation);\n        return this._indentRulesSupport.shouldIncrease(processedLine);\n    }\n    /**\n     * Apply the new indentation and return whether the indentation level should be decreased after the given line number\n     */\n    shouldDecrease(lineNumber, newIndentation) {\n        const processedLine = this._indentationLineProcessor.getProcessedLine(lineNumber, newIndentation);\n        return this._indentRulesSupport.shouldDecrease(processedLine);\n    }\n    /**\n     * Apply the new indentation and return whether the indentation level should remain unchanged at the given line number\n     */\n    shouldIgnore(lineNumber, newIndentation) {\n        const processedLine = this._indentationLineProcessor.getProcessedLine(lineNumber, newIndentation);\n        return this._indentRulesSupport.shouldIgnore(processedLine);\n    }\n    /**\n     * Apply the new indentation and return whether the indentation level should increase on the line after the given line number\n     */\n    shouldIndentNextLine(lineNumber, newIndentation) {\n        const processedLine = this._indentationLineProcessor.getProcessedLine(lineNumber, newIndentation);\n        return this._indentRulesSupport.shouldIndentNextLine(processedLine);\n    }\n}\n/**\n * This class fetches the processed text around a range which can be used for indentation evaluation.\n * It returns:\n * - The processed text before the given range and on the same start line\n * - The processed text after the given range and on the same end line\n * - The processed text on the previous line\n */\nexport class IndentationContextProcessor {\n    constructor(model, languageConfigurationService) {\n        this.model = model;\n        this.indentationLineProcessor = new IndentationLineProcessor(model, languageConfigurationService);\n    }\n    /**\n     * Returns the processed text, stripped from the language configuration brackets within the string, comment and regex tokens, around the given range\n     */\n    getProcessedTokenContextAroundRange(range) {\n        const beforeRangeProcessedTokens = this._getProcessedTokensBeforeRange(range);\n        const afterRangeProcessedTokens = this._getProcessedTokensAfterRange(range);\n        const previousLineProcessedTokens = this._getProcessedPreviousLineTokens(range);\n        return { beforeRangeProcessedTokens, afterRangeProcessedTokens, previousLineProcessedTokens };\n    }\n    _getProcessedTokensBeforeRange(range) {\n        this.model.tokenization.forceTokenization(range.startLineNumber);\n        const lineTokens = this.model.tokenization.getLineTokens(range.startLineNumber);\n        const scopedLineTokens = createScopedLineTokens(lineTokens, range.startColumn - 1);\n        let slicedTokens;\n        if (isLanguageDifferentFromLineStart(this.model, range.getStartPosition())) {\n            const columnIndexWithinScope = (range.startColumn - 1) - scopedLineTokens.firstCharOffset;\n            const firstCharacterOffset = scopedLineTokens.firstCharOffset;\n            const lastCharacterOffset = firstCharacterOffset + columnIndexWithinScope;\n            slicedTokens = lineTokens.sliceAndInflate(firstCharacterOffset, lastCharacterOffset, 0);\n        }\n        else {\n            const columnWithinLine = range.startColumn - 1;\n            slicedTokens = lineTokens.sliceAndInflate(0, columnWithinLine, 0);\n        }\n        const processedTokens = this.indentationLineProcessor.getProcessedTokens(slicedTokens);\n        return processedTokens;\n    }\n    _getProcessedTokensAfterRange(range) {\n        const position = range.isEmpty() ? range.getStartPosition() : range.getEndPosition();\n        this.model.tokenization.forceTokenization(position.lineNumber);\n        const lineTokens = this.model.tokenization.getLineTokens(position.lineNumber);\n        const scopedLineTokens = createScopedLineTokens(lineTokens, position.column - 1);\n        const columnIndexWithinScope = position.column - 1 - scopedLineTokens.firstCharOffset;\n        const firstCharacterOffset = scopedLineTokens.firstCharOffset + columnIndexWithinScope;\n        const lastCharacterOffset = scopedLineTokens.firstCharOffset + scopedLineTokens.getLineLength();\n        const slicedTokens = lineTokens.sliceAndInflate(firstCharacterOffset, lastCharacterOffset, 0);\n        const processedTokens = this.indentationLineProcessor.getProcessedTokens(slicedTokens);\n        return processedTokens;\n    }\n    _getProcessedPreviousLineTokens(range) {\n        const getScopedLineTokensAtEndColumnOfLine = (lineNumber) => {\n            this.model.tokenization.forceTokenization(lineNumber);\n            const lineTokens = this.model.tokenization.getLineTokens(lineNumber);\n            const endColumnOfLine = this.model.getLineMaxColumn(lineNumber) - 1;\n            const scopedLineTokensAtEndColumn = createScopedLineTokens(lineTokens, endColumnOfLine);\n            return scopedLineTokensAtEndColumn;\n        };\n        this.model.tokenization.forceTokenization(range.startLineNumber);\n        const lineTokens = this.model.tokenization.getLineTokens(range.startLineNumber);\n        const scopedLineTokens = createScopedLineTokens(lineTokens, range.startColumn - 1);\n        const emptyTokens = LineTokens.createEmpty('', scopedLineTokens.languageIdCodec);\n        const previousLineNumber = range.startLineNumber - 1;\n        const isFirstLine = previousLineNumber === 0;\n        if (isFirstLine) {\n            return emptyTokens;\n        }\n        const canScopeExtendOnPreviousLine = scopedLineTokens.firstCharOffset === 0;\n        if (!canScopeExtendOnPreviousLine) {\n            return emptyTokens;\n        }\n        const scopedLineTokensAtEndColumnOfPreviousLine = getScopedLineTokensAtEndColumnOfLine(previousLineNumber);\n        const doesLanguageContinueOnPreviousLine = scopedLineTokens.languageId === scopedLineTokensAtEndColumnOfPreviousLine.languageId;\n        if (!doesLanguageContinueOnPreviousLine) {\n            return emptyTokens;\n        }\n        const previousSlicedLineTokens = scopedLineTokensAtEndColumnOfPreviousLine.toIViewLineTokens();\n        const processedTokens = this.indentationLineProcessor.getProcessedTokens(previousSlicedLineTokens);\n        return processedTokens;\n    }\n}\n/**\n * This class performs the actual processing of the indentation lines.\n * The brackets of the language configuration are removed from the regex, string and comment tokens.\n */\nclass IndentationLineProcessor {\n    constructor(model, languageConfigurationService) {\n        this.model = model;\n        this.languageConfigurationService = languageConfigurationService;\n    }\n    /**\n     * Get the processed line for the given line number and potentially adjust the indentation level.\n     * Remove the language configuration brackets from the regex, string and comment tokens.\n     */\n    getProcessedLine(lineNumber, newIndentation) {\n        const replaceIndentation = (line, newIndentation) => {\n            const currentIndentation = strings.getLeadingWhitespace(line);\n            const adjustedLine = newIndentation + line.substring(currentIndentation.length);\n            return adjustedLine;\n        };\n        this.model.tokenization.forceTokenization?.(lineNumber);\n        const tokens = this.model.tokenization.getLineTokens(lineNumber);\n        let processedLine = this.getProcessedTokens(tokens).getLineContent();\n        if (newIndentation !== undefined) {\n            processedLine = replaceIndentation(processedLine, newIndentation);\n        }\n        return processedLine;\n    }\n    /**\n     * Process the line with the given tokens, remove the language configuration brackets from the regex, string and comment tokens.\n     */\n    getProcessedTokens(tokens) {\n        const shouldRemoveBracketsFromTokenType = (tokenType) => {\n            return tokenType === 2 /* StandardTokenType.String */\n                || tokenType === 3 /* StandardTokenType.RegEx */\n                || tokenType === 1 /* StandardTokenType.Comment */;\n        };\n        const languageId = tokens.getLanguageId(0);\n        const bracketsConfiguration = this.languageConfigurationService.getLanguageConfiguration(languageId).bracketsNew;\n        const bracketsRegExp = bracketsConfiguration.getBracketRegExp({ global: true });\n        const textAndMetadata = [];\n        tokens.forEach((tokenIndex) => {\n            const tokenType = tokens.getStandardTokenType(tokenIndex);\n            let text = tokens.getTokenText(tokenIndex);\n            if (shouldRemoveBracketsFromTokenType(tokenType)) {\n                text = text.replace(bracketsRegExp, '');\n            }\n            const metadata = tokens.getMetadata(tokenIndex);\n            textAndMetadata.push({ text, metadata });\n        });\n        const processedLineTokens = LineTokens.createFromTextAndMetadata(textAndMetadata, tokens.languageIdCodec);\n        return processedLineTokens;\n    }\n}\nexport function isLanguageDifferentFromLineStart(model, position) {\n    model.tokenization.forceTokenization(position.lineNumber);\n    const lineTokens = model.tokenization.getLineTokens(position.lineNumber);\n    const scopedLineTokens = createScopedLineTokens(lineTokens, position.column - 1);\n    const doesScopeStartAtOffsetZero = scopedLineTokens.firstCharOffset === 0;\n    const isScopedLanguageEqualToFirstLanguageOnLine = lineTokens.getLanguageId(0) === scopedLineTokens.languageId;\n    const languageIsDifferentFromLineStart = !doesScopeStartAtOffsetZero && !isScopedLanguageEqualToFirstLanguageOnLine;\n    return languageIsDifferentFromLineStart;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,KAAKA,OAAO,MAAM,oCAAoC;AAC7D,SAASC,sBAAsB,QAAQ,gBAAgB;AACvD,SAASC,UAAU,QAAQ,4BAA4B;AACvD;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,2BAA2B,CAAC;EACrCC,WAAWA,CAACC,KAAK,EAAEC,kBAAkB,EAAEC,4BAA4B,EAAE;IACjE,IAAI,CAACC,mBAAmB,GAAGF,kBAAkB;IAC7C,IAAI,CAACG,yBAAyB,GAAG,IAAIC,wBAAwB,CAACL,KAAK,EAAEE,4BAA4B,CAAC;EACtG;EACA;AACJ;AACA;EACII,cAAcA,CAACC,UAAU,EAAEC,cAAc,EAAE;IACvC,MAAMC,aAAa,GAAG,IAAI,CAACL,yBAAyB,CAACM,gBAAgB,CAACH,UAAU,EAAEC,cAAc,CAAC;IACjG,OAAO,IAAI,CAACL,mBAAmB,CAACG,cAAc,CAACG,aAAa,CAAC;EACjE;EACA;AACJ;AACA;EACIE,cAAcA,CAACJ,UAAU,EAAEC,cAAc,EAAE;IACvC,MAAMC,aAAa,GAAG,IAAI,CAACL,yBAAyB,CAACM,gBAAgB,CAACH,UAAU,EAAEC,cAAc,CAAC;IACjG,OAAO,IAAI,CAACL,mBAAmB,CAACQ,cAAc,CAACF,aAAa,CAAC;EACjE;EACA;AACJ;AACA;EACIG,YAAYA,CAACL,UAAU,EAAEC,cAAc,EAAE;IACrC,MAAMC,aAAa,GAAG,IAAI,CAACL,yBAAyB,CAACM,gBAAgB,CAACH,UAAU,EAAEC,cAAc,CAAC;IACjG,OAAO,IAAI,CAACL,mBAAmB,CAACS,YAAY,CAACH,aAAa,CAAC;EAC/D;EACA;AACJ;AACA;EACII,oBAAoBA,CAACN,UAAU,EAAEC,cAAc,EAAE;IAC7C,MAAMC,aAAa,GAAG,IAAI,CAACL,yBAAyB,CAACM,gBAAgB,CAACH,UAAU,EAAEC,cAAc,CAAC;IACjG,OAAO,IAAI,CAACL,mBAAmB,CAACU,oBAAoB,CAACJ,aAAa,CAAC;EACvE;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMK,2BAA2B,CAAC;EACrCf,WAAWA,CAACC,KAAK,EAAEE,4BAA4B,EAAE;IAC7C,IAAI,CAACF,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACe,wBAAwB,GAAG,IAAIV,wBAAwB,CAACL,KAAK,EAAEE,4BAA4B,CAAC;EACrG;EACA;AACJ;AACA;EACIc,mCAAmCA,CAACC,KAAK,EAAE;IACvC,MAAMC,0BAA0B,GAAG,IAAI,CAACC,8BAA8B,CAACF,KAAK,CAAC;IAC7E,MAAMG,yBAAyB,GAAG,IAAI,CAACC,6BAA6B,CAACJ,KAAK,CAAC;IAC3E,MAAMK,2BAA2B,GAAG,IAAI,CAACC,+BAA+B,CAACN,KAAK,CAAC;IAC/E,OAAO;MAAEC,0BAA0B;MAAEE,yBAAyB;MAAEE;IAA4B,CAAC;EACjG;EACAH,8BAA8BA,CAACF,KAAK,EAAE;IAClC,IAAI,CAACjB,KAAK,CAACwB,YAAY,CAACC,iBAAiB,CAACR,KAAK,CAACS,eAAe,CAAC;IAChE,MAAMC,UAAU,GAAG,IAAI,CAAC3B,KAAK,CAACwB,YAAY,CAACI,aAAa,CAACX,KAAK,CAACS,eAAe,CAAC;IAC/E,MAAMG,gBAAgB,GAAGjC,sBAAsB,CAAC+B,UAAU,EAAEV,KAAK,CAACa,WAAW,GAAG,CAAC,CAAC;IAClF,IAAIC,YAAY;IAChB,IAAIC,gCAAgC,CAAC,IAAI,CAAChC,KAAK,EAAEiB,KAAK,CAACgB,gBAAgB,CAAC,CAAC,CAAC,EAAE;MACxE,MAAMC,sBAAsB,GAAIjB,KAAK,CAACa,WAAW,GAAG,CAAC,GAAID,gBAAgB,CAACM,eAAe;MACzF,MAAMC,oBAAoB,GAAGP,gBAAgB,CAACM,eAAe;MAC7D,MAAME,mBAAmB,GAAGD,oBAAoB,GAAGF,sBAAsB;MACzEH,YAAY,GAAGJ,UAAU,CAACW,eAAe,CAACF,oBAAoB,EAAEC,mBAAmB,EAAE,CAAC,CAAC;IAC3F,CAAC,MACI;MACD,MAAME,gBAAgB,GAAGtB,KAAK,CAACa,WAAW,GAAG,CAAC;MAC9CC,YAAY,GAAGJ,UAAU,CAACW,eAAe,CAAC,CAAC,EAAEC,gBAAgB,EAAE,CAAC,CAAC;IACrE;IACA,MAAMC,eAAe,GAAG,IAAI,CAACzB,wBAAwB,CAAC0B,kBAAkB,CAACV,YAAY,CAAC;IACtF,OAAOS,eAAe;EAC1B;EACAnB,6BAA6BA,CAACJ,KAAK,EAAE;IACjC,MAAMyB,QAAQ,GAAGzB,KAAK,CAAC0B,OAAO,CAAC,CAAC,GAAG1B,KAAK,CAACgB,gBAAgB,CAAC,CAAC,GAAGhB,KAAK,CAAC2B,cAAc,CAAC,CAAC;IACpF,IAAI,CAAC5C,KAAK,CAACwB,YAAY,CAACC,iBAAiB,CAACiB,QAAQ,CAACnC,UAAU,CAAC;IAC9D,MAAMoB,UAAU,GAAG,IAAI,CAAC3B,KAAK,CAACwB,YAAY,CAACI,aAAa,CAACc,QAAQ,CAACnC,UAAU,CAAC;IAC7E,MAAMsB,gBAAgB,GAAGjC,sBAAsB,CAAC+B,UAAU,EAAEe,QAAQ,CAACG,MAAM,GAAG,CAAC,CAAC;IAChF,MAAMX,sBAAsB,GAAGQ,QAAQ,CAACG,MAAM,GAAG,CAAC,GAAGhB,gBAAgB,CAACM,eAAe;IACrF,MAAMC,oBAAoB,GAAGP,gBAAgB,CAACM,eAAe,GAAGD,sBAAsB;IACtF,MAAMG,mBAAmB,GAAGR,gBAAgB,CAACM,eAAe,GAAGN,gBAAgB,CAACiB,aAAa,CAAC,CAAC;IAC/F,MAAMf,YAAY,GAAGJ,UAAU,CAACW,eAAe,CAACF,oBAAoB,EAAEC,mBAAmB,EAAE,CAAC,CAAC;IAC7F,MAAMG,eAAe,GAAG,IAAI,CAACzB,wBAAwB,CAAC0B,kBAAkB,CAACV,YAAY,CAAC;IACtF,OAAOS,eAAe;EAC1B;EACAjB,+BAA+BA,CAACN,KAAK,EAAE;IACnC,MAAM8B,oCAAoC,GAAIxC,UAAU,IAAK;MACzD,IAAI,CAACP,KAAK,CAACwB,YAAY,CAACC,iBAAiB,CAAClB,UAAU,CAAC;MACrD,MAAMoB,UAAU,GAAG,IAAI,CAAC3B,KAAK,CAACwB,YAAY,CAACI,aAAa,CAACrB,UAAU,CAAC;MACpE,MAAMyC,eAAe,GAAG,IAAI,CAAChD,KAAK,CAACiD,gBAAgB,CAAC1C,UAAU,CAAC,GAAG,CAAC;MACnE,MAAM2C,2BAA2B,GAAGtD,sBAAsB,CAAC+B,UAAU,EAAEqB,eAAe,CAAC;MACvF,OAAOE,2BAA2B;IACtC,CAAC;IACD,IAAI,CAAClD,KAAK,CAACwB,YAAY,CAACC,iBAAiB,CAACR,KAAK,CAACS,eAAe,CAAC;IAChE,MAAMC,UAAU,GAAG,IAAI,CAAC3B,KAAK,CAACwB,YAAY,CAACI,aAAa,CAACX,KAAK,CAACS,eAAe,CAAC;IAC/E,MAAMG,gBAAgB,GAAGjC,sBAAsB,CAAC+B,UAAU,EAAEV,KAAK,CAACa,WAAW,GAAG,CAAC,CAAC;IAClF,MAAMqB,WAAW,GAAGtD,UAAU,CAACuD,WAAW,CAAC,EAAE,EAAEvB,gBAAgB,CAACwB,eAAe,CAAC;IAChF,MAAMC,kBAAkB,GAAGrC,KAAK,CAACS,eAAe,GAAG,CAAC;IACpD,MAAM6B,WAAW,GAAGD,kBAAkB,KAAK,CAAC;IAC5C,IAAIC,WAAW,EAAE;MACb,OAAOJ,WAAW;IACtB;IACA,MAAMK,4BAA4B,GAAG3B,gBAAgB,CAACM,eAAe,KAAK,CAAC;IAC3E,IAAI,CAACqB,4BAA4B,EAAE;MAC/B,OAAOL,WAAW;IACtB;IACA,MAAMM,yCAAyC,GAAGV,oCAAoC,CAACO,kBAAkB,CAAC;IAC1G,MAAMI,kCAAkC,GAAG7B,gBAAgB,CAAC8B,UAAU,KAAKF,yCAAyC,CAACE,UAAU;IAC/H,IAAI,CAACD,kCAAkC,EAAE;MACrC,OAAOP,WAAW;IACtB;IACA,MAAMS,wBAAwB,GAAGH,yCAAyC,CAACI,iBAAiB,CAAC,CAAC;IAC9F,MAAMrB,eAAe,GAAG,IAAI,CAACzB,wBAAwB,CAAC0B,kBAAkB,CAACmB,wBAAwB,CAAC;IAClG,OAAOpB,eAAe;EAC1B;AACJ;AACA;AACA;AACA;AACA;AACA,MAAMnC,wBAAwB,CAAC;EAC3BN,WAAWA,CAACC,KAAK,EAAEE,4BAA4B,EAAE;IAC7C,IAAI,CAACF,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACE,4BAA4B,GAAGA,4BAA4B;EACpE;EACA;AACJ;AACA;AACA;EACIQ,gBAAgBA,CAACH,UAAU,EAAEC,cAAc,EAAE;IACzC,MAAMsD,kBAAkB,GAAGA,CAACC,IAAI,EAAEvD,cAAc,KAAK;MACjD,MAAMwD,kBAAkB,GAAGrE,OAAO,CAACsE,oBAAoB,CAACF,IAAI,CAAC;MAC7D,MAAMG,YAAY,GAAG1D,cAAc,GAAGuD,IAAI,CAACI,SAAS,CAACH,kBAAkB,CAACI,MAAM,CAAC;MAC/E,OAAOF,YAAY;IACvB,CAAC;IACD,IAAI,CAAClE,KAAK,CAACwB,YAAY,CAACC,iBAAiB,GAAGlB,UAAU,CAAC;IACvD,MAAM8D,MAAM,GAAG,IAAI,CAACrE,KAAK,CAACwB,YAAY,CAACI,aAAa,CAACrB,UAAU,CAAC;IAChE,IAAIE,aAAa,GAAG,IAAI,CAACgC,kBAAkB,CAAC4B,MAAM,CAAC,CAACC,cAAc,CAAC,CAAC;IACpE,IAAI9D,cAAc,KAAK+D,SAAS,EAAE;MAC9B9D,aAAa,GAAGqD,kBAAkB,CAACrD,aAAa,EAAED,cAAc,CAAC;IACrE;IACA,OAAOC,aAAa;EACxB;EACA;AACJ;AACA;EACIgC,kBAAkBA,CAAC4B,MAAM,EAAE;IACvB,MAAMG,iCAAiC,GAAIC,SAAS,IAAK;MACrD,OAAOA,SAAS,KAAK,CAAC,CAAC,kCAChBA,SAAS,KAAK,CAAC,CAAC,iCAChBA,SAAS,KAAK,CAAC,CAAC;IAC3B,CAAC;IACD,MAAMd,UAAU,GAAGU,MAAM,CAACK,aAAa,CAAC,CAAC,CAAC;IAC1C,MAAMC,qBAAqB,GAAG,IAAI,CAACzE,4BAA4B,CAAC0E,wBAAwB,CAACjB,UAAU,CAAC,CAACkB,WAAW;IAChH,MAAMC,cAAc,GAAGH,qBAAqB,CAACI,gBAAgB,CAAC;MAAEC,MAAM,EAAE;IAAK,CAAC,CAAC;IAC/E,MAAMC,eAAe,GAAG,EAAE;IAC1BZ,MAAM,CAACa,OAAO,CAAEC,UAAU,IAAK;MAC3B,MAAMV,SAAS,GAAGJ,MAAM,CAACe,oBAAoB,CAACD,UAAU,CAAC;MACzD,IAAIE,IAAI,GAAGhB,MAAM,CAACiB,YAAY,CAACH,UAAU,CAAC;MAC1C,IAAIX,iCAAiC,CAACC,SAAS,CAAC,EAAE;QAC9CY,IAAI,GAAGA,IAAI,CAACE,OAAO,CAACT,cAAc,EAAE,EAAE,CAAC;MAC3C;MACA,MAAMU,QAAQ,GAAGnB,MAAM,CAACoB,WAAW,CAACN,UAAU,CAAC;MAC/CF,eAAe,CAACS,IAAI,CAAC;QAAEL,IAAI;QAAEG;MAAS,CAAC,CAAC;IAC5C,CAAC,CAAC;IACF,MAAMG,mBAAmB,GAAG9F,UAAU,CAAC+F,yBAAyB,CAACX,eAAe,EAAEZ,MAAM,CAAChB,eAAe,CAAC;IACzG,OAAOsC,mBAAmB;EAC9B;AACJ;AACA,OAAO,SAAS3D,gCAAgCA,CAAChC,KAAK,EAAE0C,QAAQ,EAAE;EAC9D1C,KAAK,CAACwB,YAAY,CAACC,iBAAiB,CAACiB,QAAQ,CAACnC,UAAU,CAAC;EACzD,MAAMoB,UAAU,GAAG3B,KAAK,CAACwB,YAAY,CAACI,aAAa,CAACc,QAAQ,CAACnC,UAAU,CAAC;EACxE,MAAMsB,gBAAgB,GAAGjC,sBAAsB,CAAC+B,UAAU,EAAEe,QAAQ,CAACG,MAAM,GAAG,CAAC,CAAC;EAChF,MAAMgD,0BAA0B,GAAGhE,gBAAgB,CAACM,eAAe,KAAK,CAAC;EACzE,MAAM2D,0CAA0C,GAAGnE,UAAU,CAAC+C,aAAa,CAAC,CAAC,CAAC,KAAK7C,gBAAgB,CAAC8B,UAAU;EAC9G,MAAMoC,gCAAgC,GAAG,CAACF,0BAA0B,IAAI,CAACC,0CAA0C;EACnH,OAAOC,gCAAgC;AAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}