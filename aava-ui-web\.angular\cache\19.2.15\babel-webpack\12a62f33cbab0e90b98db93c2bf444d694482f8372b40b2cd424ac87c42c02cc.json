{"ast": null, "code": "import { ImageCardComponent, CardContentComponent, AvaTagComponent } from \"@ava/play-comp-library\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@shared/index\";\nimport * as i2 from \"@angular/router\";\nexport let DashboardImgCardComponent = /*#__PURE__*/(() => {\n  class DashboardImgCardComponent {\n    tokenStorage;\n    router;\n    userName = '';\n    desc = 'Let’s build Milestones!';\n    constructor(tokenStorage, router) {\n      this.tokenStorage = tokenStorage;\n      this.router = router;\n    }\n    ngOnInit() {\n      this.userName = this.tokenStorage.getDaName() || 'User';\n    }\n    navigateTo(type) {\n      const routes = {\n        agent: '/build/agents/individual',\n        workflow: '/build/workflows/create',\n        tool: '/libraries/tools/create',\n        prompt: '/libraries/prompts/create',\n        guardrail: '/libraries/guardrails/create'\n      };\n      const route = routes[type];\n      if (route) {\n        this.router.navigate([route]);\n      }\n    }\n    static ɵfac = function DashboardImgCardComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DashboardImgCardComponent)(i0.ɵɵdirectiveInject(i1.TokenStorageService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DashboardImgCardComponent,\n      selectors: [[\"app-dashboard-img-card\"]],\n      inputs: {\n        userName: \"userName\",\n        desc: \"desc\"\n      },\n      decls: 19,\n      vars: 2,\n      consts: [[\"id\", \"dashboard-img-card\"], [1, \"dashboard-img-card\"], [1, \"d-flex\"], [\"src\", \"assets/images/robot-dashboard.png\", \"alt\", \"Robot\"], [1, \"d-flex\", \"flex-column\", \"justify-content-between\", \"right-container\"], [1, \"txt-wrapper\"], [1, \"name\"], [1, \"desc\"], [1, \"tags\"], [1, \"left-wrapper\"], [\"label\", \"Create Agent\", 3, \"clicked\"], [\"label\", \"Create Workflow\", 3, \"clicked\"], [\"label\", \"Create Prompt\", 3, \"clicked\"], [1, \"right-wrapper\"], [\"label\", \"Create Tool\", 3, \"clicked\"], [\"label\", \"Create Guardrail\", 3, \"clicked\"]],\n      template: function DashboardImgCardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"ava-image-card\", 1)(2, \"ava-card-content\")(3, \"div\", 2);\n          i0.ɵɵelement(4, \"img\", 3);\n          i0.ɵɵelementStart(5, \"div\", 4)(6, \"div\", 5)(7, \"span\", 6);\n          i0.ɵɵtext(8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"span\", 7);\n          i0.ɵɵtext(10);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"div\", 8)(12, \"div\", 9)(13, \"ava-tag\", 10);\n          i0.ɵɵlistener(\"clicked\", function DashboardImgCardComponent_Template_ava_tag_clicked_13_listener() {\n            return ctx.navigateTo(\"agent\");\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"ava-tag\", 11);\n          i0.ɵɵlistener(\"clicked\", function DashboardImgCardComponent_Template_ava_tag_clicked_14_listener() {\n            return ctx.navigateTo(\"workflow\");\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"ava-tag\", 12);\n          i0.ɵɵlistener(\"clicked\", function DashboardImgCardComponent_Template_ava_tag_clicked_15_listener() {\n            return ctx.navigateTo(\"prompt\");\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"div\", 13)(17, \"ava-tag\", 14);\n          i0.ɵɵlistener(\"clicked\", function DashboardImgCardComponent_Template_ava_tag_clicked_17_listener() {\n            return ctx.navigateTo(\"tool\");\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"ava-tag\", 15);\n          i0.ɵɵlistener(\"clicked\", function DashboardImgCardComponent_Template_ava_tag_clicked_18_listener() {\n            return ctx.navigateTo(\"guardrail\");\n          });\n          i0.ɵɵelementEnd()()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵtextInterpolate1(\" Hi ! \", ctx.userName, \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.desc, \" \");\n        }\n      },\n      dependencies: [ImageCardComponent, CardContentComponent, AvaTagComponent],\n      styles: [\"#dashboard-img-card .dashboard-img-card {\\n  width: 100%;\\n  padding: 0rem;\\n}\\n  #dashboard-img-card .dashboard-img-card .ava-image-card-wrapper {\\n  width: 100%;\\n}\\n  #dashboard-img-card .dashboard-img-card .ava-image-card-wrapper .ava-default-card-container .ava-default-card {\\n  padding: 1rem;\\n  width: 100%;\\n  border: none !important;\\n  box-shadow: none;\\n}\\n  #dashboard-img-card .dashboard-img-card .ava-image-card-wrapper .ava-default-card-container .ava-default-card ava-card-content img {\\n  width: 220px;\\n  height: 265px;\\n}\\n  #dashboard-img-card .dashboard-img-card .ava-image-card-wrapper .ava-default-card-container .ava-default-card ava-card-content .right-container {\\n  margin: 1.5rem;\\n  row-gap: 1.5rem;\\n}\\n  #dashboard-img-card .dashboard-img-card .ava-image-card-wrapper .ava-default-card-container .ava-default-card ava-card-content .right-container .txt-wrapper {\\n  margin-top: 3.5625rem;\\n  display: flex;\\n  flex-direction: column;\\n  row-gap: 1.5rem;\\n}\\n  #dashboard-img-card .dashboard-img-card .ava-image-card-wrapper .ava-default-card-container .ava-default-card ava-card-content .right-container .txt-wrapper .name {\\n  font-family: Mulish;\\n  font-weight: 500;\\n  font-size: clamp(1.5rem, 2vw, 2rem);\\n  font-style: Medium;\\n  line-height: 100%;\\n  color: #3B3F46;\\n}\\n  #dashboard-img-card .dashboard-img-card .ava-image-card-wrapper .ava-default-card-container .ava-default-card ava-card-content .right-container .txt-wrapper .desc {\\n  font-family: Mulish;\\n  font-weight: 400;\\n  font-style: Regular;\\n  font-size: clamp(2.5rem, 4vw, 3rem);\\n  color: #3B3F46;\\n}\\n  #dashboard-img-card .dashboard-img-card .ava-image-card-wrapper .ava-default-card-container .ava-default-card ava-card-content .right-container .tags ava-tag .ava-tag.ava-tag--filled {\\n  background: transparent;\\n  border-color: 1px solid #BBBEC5;\\n  height: 2rem;\\n  border-radius: 1.25rem;\\n  border-width: 1px;\\n  padding-right: 0.75rem;\\n  padding-left: 0.75rem;\\n}\\n  #dashboard-img-card .dashboard-img-card .ava-image-card-wrapper .ava-default-card-container .ava-default-card ava-card-content .right-container .tags ava-tag .ava-tag.ava-tag--filled .ava-tag__label {\\n  font-family: Mulish;\\n  font-weight: 600;\\n  font-size: 20px;\\n  color: #3B3F46;\\n  opacity: 1;\\n}\\n  #dashboard-img-card .dashboard-img-card .ava-image-card-wrapper .ava-default-card-container .ava-default-card ava-card-content .right-container .tags, \\n  #dashboard-img-card .dashboard-img-card .ava-image-card-wrapper .ava-default-card-container .ava-default-card ava-card-content .right-container .left-wrapper, \\n  #dashboard-img-card .dashboard-img-card .ava-image-card-wrapper .ava-default-card-container .ava-default-card ava-card-content .right-container .right-wrapper {\\n  display: flex;\\n  height: 2rem;\\n}\\n  #dashboard-img-card .dashboard-img-card .ava-image-card-wrapper .ava-default-card-container .ava-default-card ava-card-content .right-container .tags, \\n  #dashboard-img-card .dashboard-img-card .ava-image-card-wrapper .ava-default-card-container .ava-default-card ava-card-content .right-container .right-wrapper {\\n  column-gap: 1.5rem;\\n}\\n  #dashboard-img-card .dashboard-img-card .ava-image-card-wrapper .ava-default-card-container .ava-default-card ava-card-content .right-container .left-wrapper {\\n  column-gap: 0.5rem;\\n}\\n  #dashboard-img-card .dashboard-img-card .ava-image-card-wrapper {\\n  transform: scaleX(1);\\n  transition: transform 0.4s ease;\\n}\\n  #dashboard-img-card .dashboard-img-card .ava-image-card-wrapper .ellips {\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n  #dashboard-img-card .dashboard-img-card .ava-image-card-wrapper:hover {\\n  transform: scaleX(1.02) scaleY(1.02);\\n}\\n  #dashboard-img-card .dashboard-img-card .ava-image-card-wrapper ava-card-header h1 {\\n  margin-top: 1rem;\\n}\\n  #dashboard-img-card .dashboard-img-card .ava-image-card-wrapper ava-card-header .ava-header img {\\n  border-radius: 12px;\\n}\\n  #dashboard-img-card .dashboard-img-card .ava-image-card-wrapper ava-card-header .right, \\n  #dashboard-img-card .dashboard-img-card .ava-image-card-wrapper ava-card-footer .right {\\n  display: flex;\\n  justify-content: flex-end;\\n  align-items: center;\\n  gap: 10px;\\n}\\n  #dashboard-img-card .dashboard-img-card .ava-image-card-wrapper ava-card-header .left, \\n  #dashboard-img-card .dashboard-img-card .ava-image-card-wrapper ava-card-footer .left {\\n  display: flex;\\n  gap: 10px;\\n}\\n  #dashboard-img-card .dashboard-img-card .ava-image-card-wrapper ava-card-content .image-wrapper {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n}\\n  #dashboard-img-card .dashboard-img-card .ava-image-card-wrapper ava-card-content p {\\n  flex: 1;\\n}\\n  #dashboard-img-card .dashboard-img-card .ava-image-card-wrapper ava-card-content img {\\n  width: 150px;\\n  object-fit: cover;\\n  border-radius: 12px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return DashboardImgCardComponent;\n})();", "map": {"version": 3, "names": ["ImageCardComponent", "CardContentComponent", "AvaTagComponent", "DashboardImgCardComponent", "tokenStorage", "router", "userName", "desc", "constructor", "ngOnInit", "getDaName", "navigateTo", "type", "routes", "agent", "workflow", "tool", "prompt", "guardrail", "route", "navigate", "i0", "ɵɵdirectiveInject", "i1", "TokenStorageService", "i2", "Router", "selectors", "inputs", "decls", "vars", "consts", "template", "DashboardImgCardComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "DashboardImgCardComponent_Template_ava_tag_clicked_13_listener", "DashboardImgCardComponent_Template_ava_tag_clicked_14_listener", "DashboardImgCardComponent_Template_ava_tag_clicked_15_listener", "DashboardImgCardComponent_Template_ava_tag_clicked_17_listener", "DashboardImgCardComponent_Template_ava_tag_clicked_18_listener", "ɵɵadvance", "ɵɵtextInterpolate1", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\console\\src\\app\\shared\\components\\dashboard-img-card\\dashboard-img-card.component.ts", "C:\\console\\aava-ui-web\\projects\\console\\src\\app\\shared\\components\\dashboard-img-card\\dashboard-img-card.component.html"], "sourcesContent": ["import { Component, Input, OnInit } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { ImageCardComponent, CardContentComponent, AvaTagComponent } from \"@ava/play-comp-library\";\r\nimport { TokenStorageService } from '@shared/index';\r\n\r\n@Component({\r\n  selector: 'app-dashboard-img-card',\r\n  imports: [ImageCardComponent, CardContentComponent, AvaTagComponent],\r\n  templateUrl: './dashboard-img-card.component.html',\r\n  styleUrl: './dashboard-img-card.component.scss'\r\n})\r\nexport class DashboardImgCardComponent implements OnInit {\r\n  @Input() userName = ''\r\n  @Input() desc = 'Let’s build Milestones!'\r\n\r\n  constructor(private tokenStorage: TokenStorageService, private router: Router) { }\r\n\r\n  ngOnInit(): void {\r\n    this.userName = this.tokenStorage.getDaName() || 'User';\r\n  }\r\n\r\n  navigateTo(type: string): void {\r\n    const routes: Record<string, string> = {\r\n      agent: '/build/agents/individual',\r\n      workflow: '/build/workflows/create',\r\n      tool: '/libraries/tools/create',\r\n      prompt: '/libraries/prompts/create',\r\n      guardrail: '/libraries/guardrails/create'\r\n    };\r\n\r\n    const route = routes[type];\r\n    if (route) {\r\n      this.router.navigate([route]);\r\n    }\r\n  }\r\n\r\n}\r\n", "<div id=\"dashboard-img-card\">\r\n    <ava-image-card class=\"dashboard-img-card\">\r\n        <ava-card-content>\r\n            <div class=\"d-flex\">\r\n                <img src=\"assets/images/robot-dashboard.png\" alt=\"Robot\">\r\n                <div class=\"d-flex flex-column justify-content-between right-container\">\r\n                    <div class=\"txt-wrapper\">\r\n                        <span class=\"name\">\r\n                            Hi ! {{userName}}\r\n                        </span>\r\n                        <span class=\"desc\">\r\n                            {{desc}}\r\n                        </span>\r\n                    </div>\r\n                    <div class=\"tags\">\r\n                        <div class=\"left-wrapper\">\r\n                            <ava-tag label=\"Create Agent\" (clicked)=\"navigateTo('agent')\"></ava-tag>\r\n                            <ava-tag label=\"Create Workflow\" (clicked)=\"navigateTo('workflow')\"></ava-tag>\r\n                            <ava-tag label=\"Create Prompt\" (clicked)=\"navigateTo('prompt')\"></ava-tag>\r\n                        </div>\r\n                        <div class=\"right-wrapper\">\r\n                            <ava-tag label=\"Create Tool\" (clicked)=\"navigateTo('tool')\"></ava-tag>\r\n                            <ava-tag label=\"Create Guardrail\" (clicked)=\"navigateTo('guardrail')\"></ava-tag>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </ava-card-content>\r\n    </ava-image-card>\r\n</div>"], "mappings": "AAEA,SAASA,kBAAkB,EAAEC,oBAAoB,EAAEC,eAAe,QAAQ,wBAAwB;;;;AASlG,WAAaC,yBAAyB;EAAhC,MAAOA,yBAAyB;IAIhBC,YAAA;IAA2CC,MAAA;IAHtDC,QAAQ,GAAG,EAAE;IACbC,IAAI,GAAG,yBAAyB;IAEzCC,YAAoBJ,YAAiC,EAAUC,MAAc;MAAzD,KAAAD,YAAY,GAAZA,YAAY;MAA+B,KAAAC,MAAM,GAANA,MAAM;IAAY;IAEjFI,QAAQA,CAAA;MACN,IAAI,CAACH,QAAQ,GAAG,IAAI,CAACF,YAAY,CAACM,SAAS,EAAE,IAAI,MAAM;IACzD;IAEAC,UAAUA,CAACC,IAAY;MACrB,MAAMC,MAAM,GAA2B;QACrCC,KAAK,EAAE,0BAA0B;QACjCC,QAAQ,EAAE,yBAAyB;QACnCC,IAAI,EAAE,yBAAyB;QAC/BC,MAAM,EAAE,2BAA2B;QACnCC,SAAS,EAAE;OACZ;MAED,MAAMC,KAAK,GAAGN,MAAM,CAACD,IAAI,CAAC;MAC1B,IAAIO,KAAK,EAAE;QACT,IAAI,CAACd,MAAM,CAACe,QAAQ,CAAC,CAACD,KAAK,CAAC,CAAC;MAC/B;IACF;;uCAvBWhB,yBAAyB,EAAAkB,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,mBAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;;YAAzBvB,yBAAyB;MAAAwB,SAAA;MAAAC,MAAA;QAAAtB,QAAA;QAAAC,IAAA;MAAA;MAAAsB,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCR1Bb,EAHZ,CAAAe,cAAA,aAA6B,wBACkB,uBACrB,aACM;UAChBf,EAAA,CAAAgB,SAAA,aAAyD;UAGjDhB,EAFR,CAAAe,cAAA,aAAwE,aAC3C,cACF;UACff,EAAA,CAAAiB,MAAA,GACJ;UAAAjB,EAAA,CAAAkB,YAAA,EAAO;UACPlB,EAAA,CAAAe,cAAA,cAAmB;UACff,EAAA,CAAAiB,MAAA,IACJ;UACJjB,EADI,CAAAkB,YAAA,EAAO,EACL;UAGElB,EAFR,CAAAe,cAAA,cAAkB,cACY,mBACwC;UAAhCf,EAAA,CAAAmB,UAAA,qBAAAC,+DAAA;YAAA,OAAWN,GAAA,CAAAxB,UAAA,CAAW,OAAO,CAAC;UAAA,EAAC;UAACU,EAAA,CAAAkB,YAAA,EAAU;UACxElB,EAAA,CAAAe,cAAA,mBAAoE;UAAnCf,EAAA,CAAAmB,UAAA,qBAAAE,+DAAA;YAAA,OAAWP,GAAA,CAAAxB,UAAA,CAAW,UAAU,CAAC;UAAA,EAAC;UAACU,EAAA,CAAAkB,YAAA,EAAU;UAC9ElB,EAAA,CAAAe,cAAA,mBAAgE;UAAjCf,EAAA,CAAAmB,UAAA,qBAAAG,+DAAA;YAAA,OAAWR,GAAA,CAAAxB,UAAA,CAAW,QAAQ,CAAC;UAAA,EAAC;UACnEU,EADoE,CAAAkB,YAAA,EAAU,EACxE;UAEFlB,EADJ,CAAAe,cAAA,eAA2B,mBACqC;UAA/Bf,EAAA,CAAAmB,UAAA,qBAAAI,+DAAA;YAAA,OAAWT,GAAA,CAAAxB,UAAA,CAAW,MAAM,CAAC;UAAA,EAAC;UAACU,EAAA,CAAAkB,YAAA,EAAU;UACtElB,EAAA,CAAAe,cAAA,mBAAsE;UAApCf,EAAA,CAAAmB,UAAA,qBAAAK,+DAAA;YAAA,OAAWV,GAAA,CAAAxB,UAAA,CAAW,WAAW,CAAC;UAAA,EAAC;UAOjGU,EAPkG,CAAAkB,YAAA,EAAU,EAC9E,EACJ,EACJ,EACJ,EACS,EACN,EACf;;;UArBsBlB,EAAA,CAAAyB,SAAA,GACJ;UADIzB,EAAA,CAAA0B,kBAAA,WAAAZ,GAAA,CAAA7B,QAAA,MACJ;UAEIe,EAAA,CAAAyB,SAAA,GACJ;UADIzB,EAAA,CAAA0B,kBAAA,MAAAZ,GAAA,CAAA5B,IAAA,MACJ;;;qBDLZP,kBAAkB,EAAEC,oBAAoB,EAAEC,eAAe;MAAA8C,MAAA;IAAA;;SAIxD7C,yBAAyB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}