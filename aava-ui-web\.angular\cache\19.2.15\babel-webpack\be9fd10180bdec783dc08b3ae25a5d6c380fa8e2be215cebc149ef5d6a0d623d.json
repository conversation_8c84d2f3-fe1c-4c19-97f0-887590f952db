{"ast": null, "code": "import { CommonModule, formatDate } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport { ApprovalCardComponent, IconComponent, AvaTextboxComponent, AvaTagComponent, ButtonComponent } from '@ava/play-comp-library';\nimport approvalText from '../constants/approval.json';\nimport { debounceTime, distinctUntilChanged, map, startWith } from 'rxjs';\nimport { AgentsPreviewPanelComponent } from './agents-preview-panel/agents-preview-panel.component';\nimport { ApprovalTxtCardComponent } from '../approval-text-card/approval-text-card.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../shared/services/approval.service\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"../../../shared/services/workflow.service\";\nimport * as i5 from \"../../../shared/services/drawer/drawer.service\";\nimport * as i6 from \"@ava/play-comp-library\";\nfunction ApprovalWorkflowsComponent_Conditional_17_For_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵlistener(\"click\", function ApprovalWorkflowsComponent_Conditional_17_For_1_Template_div_click_0_listener() {\n      const $index_r2 = i0.ɵɵrestoreView(_r1).$index;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onCardClick($index_r2));\n    });\n    i0.ɵɵelementStart(1, \"ava-approval-card\", 15)(2, \"ava-card-header\");\n    i0.ɵɵelement(3, \"ava-icon\", 16);\n    i0.ɵɵelementStart(4, \"div\", 17)(5, \"h2\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"ava-tag\", 18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"ava-card-content\")(9, \"div\", 19)(10, \"div\", 20);\n    i0.ɵɵelement(11, \"ava-tag\", 21)(12, \"ava-tag\", 22)(13, \"ava-tag\", 23)(14, \"ava-tag\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 25)(16, \"div\", 26);\n    i0.ɵɵelement(17, \"ava-icon\", 27);\n    i0.ɵɵelementStart(18, \"span\");\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 28);\n    i0.ɵɵelement(21, \"ava-icon\", 29);\n    i0.ɵɵelementStart(22, \"span\");\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(24, \"ava-card-footer\")(25, \"div\", 30)(26, \"div\", 31)(27, \"span\", 32);\n    i0.ɵɵtext(28, \"Execution Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\");\n    i0.ɵɵelement(30, \"ava-icon\", 33);\n    i0.ɵɵelementStart(31, \"span\");\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(33, \"div\", 34)(34, \"ava-button\", 35);\n    i0.ɵɵlistener(\"userClick\", function ApprovalWorkflowsComponent_Conditional_17_For_1_Template_ava_button_userClick_34_listener() {\n      const $index_r2 = i0.ɵɵrestoreView(_r1).$index;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.handleTesting($index_r2));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"ava-button\", 36);\n    i0.ɵɵlistener(\"userClick\", function ApprovalWorkflowsComponent_Conditional_17_For_1_Template_ava_button_userClick_35_listener() {\n      const $index_r2 = i0.ɵɵrestoreView(_r1).$index;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.rejectApproval($index_r2));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"ava-button\", 37);\n    i0.ɵɵlistener(\"userClick\", function ApprovalWorkflowsComponent_Conditional_17_For_1_Template_ava_button_userClick_36_listener() {\n      const $index_r2 = i0.ɵɵrestoreView(_r1).$index;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.approveApproval($index_r2));\n    });\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(item_r4.session1.title);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r2.currentTab);\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate(item_r4.session3[0].label);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(item_r4.session3[1].label);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(item_r4 == null ? null : item_r4.session4.status);\n  }\n}\nfunction ApprovalWorkflowsComponent_Conditional_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵrepeaterCreate(0, ApprovalWorkflowsComponent_Conditional_17_For_1_Template, 37, 5, \"div\", 13, i0.ɵɵrepeaterTrackByIndex);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵrepeater(ctx_r2.consoleApproval.contents);\n  }\n}\nfunction ApprovalWorkflowsComponent_Conditional_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" All \", ctx_r2.currentTab, \" have been successfully approved. No pending actions. \");\n  }\n}\nexport let ApprovalWorkflowsComponent = /*#__PURE__*/(() => {\n  class ApprovalWorkflowsComponent {\n    router;\n    approvalService;\n    fb;\n    workflowService;\n    drawerService;\n    dialogService;\n    appLabels = approvalText.labels;\n    totalApprovedApprovals = 20;\n    totalPendingApprovals = 15;\n    totalApprovals = 60;\n    isBasicCollapsed = false;\n    quickActionsExpanded = true;\n    consoleApproval = {};\n    options = [];\n    basicSidebarItems = [];\n    quickActions = [];\n    toolReviews = [];\n    workflowReviews = [];\n    filteredWorkflowReviews = [];\n    agentsReviews = [];\n    currentToolsPage = 1;\n    currentAgentsPage = 1;\n    currentWorkflowsPage = 1;\n    pageSize = 50;\n    totalRecords = 0;\n    isDeleted = false;\n    currentTab = 'Workflows';\n    selectedIndex = 0;\n    searchForm;\n    labels = approvalText.labels;\n    approvedAgentId = null;\n    previewData = null;\n    selectedWorkflowId = 0;\n    constructor(router, approvalService, fb, workflowService, drawerService, dialogService) {\n      this.router = router;\n      this.approvalService = approvalService;\n      this.fb = fb;\n      this.workflowService = workflowService;\n      this.drawerService = drawerService;\n      this.dialogService = dialogService;\n      this.labels = approvalText.labels;\n      this.options = [{\n        name: this.labels.electronics,\n        value: 'electronics'\n      }, {\n        name: this.labels.clothing,\n        value: 'clothing'\n      }, {\n        name: this.labels.books,\n        value: 'books'\n      }];\n      this.basicSidebarItems = [{\n        id: '1',\n        icon: 'hammer',\n        text: this.labels.agents,\n        route: '',\n        active: true\n      }, {\n        id: '2',\n        icon: 'circle-check',\n        text: this.labels.workflows,\n        route: ''\n      }, {\n        id: '3',\n        icon: 'bot',\n        text: this.labels.tools,\n        route: ''\n      }];\n      this.quickActions = [{\n        icon: 'awe_agents',\n        label: this.labels.agents,\n        route: ''\n      }, {\n        icon: 'awe_workflows',\n        label: this.labels.workflows,\n        route: ''\n      }, {\n        icon: 'awe_tools',\n        label: this.labels.tools,\n        route: ''\n      }];\n      this.searchForm = this.fb.group({\n        search: ['']\n      });\n    }\n    ngOnInit() {\n      this.searchList();\n      this.totalApprovals = 60;\n      this.loadWorkflowReviews();\n    }\n    searchList() {\n      console.log(this.searchForm.get('search')?.value);\n      this.searchForm.get('search').valueChanges.pipe(startWith(''), debounceTime(300), distinctUntilChanged(), map(value => value?.toLowerCase() ?? '')).subscribe(searchText => {\n        this.applyFilter(searchText);\n      });\n    }\n    applyFilter(text) {\n      const lower = text;\n      if (!text) {\n        this.updateConsoleApproval(this.workflowReviews, 'workflow');\n        return;\n      }\n      this.filteredWorkflowReviews = this.workflowReviews.filter(item => item.workflowName?.toLowerCase().includes(lower));\n      this.updateConsoleApproval(this.filteredWorkflowReviews, 'workflow');\n    }\n    onSelectionChange(data) {\n      console.log('Selection changed:', data);\n    }\n    uClick(i) {\n      console.log('log' + i);\n    }\n    toggleQuickActions() {\n      this.quickActionsExpanded = !this.quickActionsExpanded;\n    }\n    onBasicCollapseToggle(isCollapsed) {\n      this.isBasicCollapsed = isCollapsed;\n      console.log('Basic sidebar collapsed:', isCollapsed);\n    }\n    onBasicItemClick(item) {\n      this.basicSidebarItems.forEach(i => i.active = false);\n      item.active = true;\n      console.log(item);\n    }\n    toRequestStatus(value) {\n      return value === 'approved' || value === 'rejected' || value === 'review' ? value : 'review';\n    }\n    handleMetaDataApproval() {\n      this.showApprovalDialog();\n    }\n    handeMetaDataSendback() {\n      this.showFeedbackDialog();\n    }\n    loadWorkflowReviews() {\n      this.approvalService.getAllReviewWorkflows(this.currentWorkflowsPage, this.pageSize, this.isDeleted).subscribe(response => {\n        if (this.currentWorkflowsPage > 1) {\n          this.workflowReviews = [...this.workflowReviews, ...response.workflowReviewDetails];\n        } else {\n          this.workflowReviews = response?.workflowReviewDetails;\n        }\n        this.workflowReviews = this.workflowReviews.filter(r => r.status !== 'approved');\n        this.filteredWorkflowReviews = this.workflowReviews;\n        this.totalRecords = this.workflowReviews.length;\n        //   console.log('workflow reviews ', this.workflowReviews);\n        this.updateConsoleApproval(this.workflowReviews, 'workflow');\n      });\n    }\n    loadMoreWorkflows(page) {\n      this.currentWorkflowsPage = page;\n      this.loadWorkflowReviews();\n    }\n    loadReviews(name) {\n      this.currentTab = name;\n      this.loadWorkflowReviews();\n    }\n    rejectApproval(idx) {\n      console.log(idx);\n      this.selectedIndex = idx;\n    }\n    approveApproval(idx) {\n      console.log(idx);\n      this.selectedIndex = idx;\n      console.log(this.filteredWorkflowReviews[this.selectedIndex]);\n    }\n    showApprovalDialog() {\n      this.dialogService.confirmation({\n        title: this.labels.confirmApproval,\n        message: `${this.labels.youAreAboutToApproveThis} Workflow. ${this.labels.itWillBeActiveAndAvailableIn} ${this.currentTab} ${this.labels.catalogueForUsersToExecute}`,\n        confirmButtonText: this.labels.approve,\n        cancelButtonText: 'Cancel',\n        confirmButtonVariant: 'danger',\n        icon: 'circle-check'\n      }).then(result => {\n        if (result.confirmed) {\n          this.handleApproval();\n        }\n      });\n    }\n    showFeedbackDialog() {\n      const customButtons = [{\n        label: 'Cancel',\n        variant: 'secondary',\n        action: 'cancel'\n      }, {\n        label: 'Send Back',\n        variant: 'primary',\n        action: 'sendback'\n      }];\n      this.dialogService.feedback({\n        title: 'Confirm Send Back',\n        message: 'This Workflow will be send back for corrections and modification. Kindly comment what needs to be done.',\n        buttons: customButtons\n      }).then(result => {\n        if (result.confirmed && result.confirmed === true) {\n          this.handleRejection(result.data);\n        }\n      });\n    }\n    handleApproval() {\n      this.handleWorkflowApproval();\n    }\n    handleRejection(feedback) {\n      this.handleWorkflowRejection(feedback);\n    }\n    handleWorkflowApproval() {\n      const workflowDetails = this.filteredWorkflowReviews[this.selectedIndex];\n      const id = workflowDetails?.id;\n      const workflowId = workflowDetails?.workflowId;\n      const status = 'approved';\n      const reviewedBy = workflowDetails?.reviewedBy;\n      console.log(id, workflowId, status, reviewedBy);\n      // Show loading dialog\n      this.dialogService.loading({\n        title: 'Approving Workflow...',\n        message: 'Please wait while we approve the workflow.',\n        showProgress: false,\n        showCancelButton: false\n      });\n      this.approvalService.approveWorkflow(id, workflowId, status, reviewedBy).subscribe({\n        next: response => {\n          this.dialogService.close(); // Close loading dialog\n          const message = response?.message || this.labels.workflowSuccessApproveMessage;\n          this.dialogService.success({\n            title: 'Workflow Approved',\n            message: message\n          }).then(result => {\n            if (result.action === 'secondary') {\n              // Navigate to edit workflow screen\n              this.router.navigate(['/build/workflows/edit', workflowId]);\n            } else {\n              this.loadWorkflowReviews(); // Refresh the list\n            }\n          });\n        },\n        error: error => {\n          this.dialogService.close(); // Close loading dialog\n          const errorMessage = error?.error?.message || this.labels.defaultErrorMessage;\n          this.dialogService.error({\n            title: 'Approval Failed',\n            message: errorMessage,\n            showRetryButton: true,\n            retryButtonText: 'Retry'\n          }).then(result => {\n            if (result.action === 'retry') {\n              this.handleWorkflowApproval();\n            }\n          });\n        }\n      });\n    }\n    handleWorkflowRejection(feedback) {\n      const workflowDetails = this.workflowReviews[this.selectedIndex];\n      const id = workflowDetails?.id;\n      const workflowId = workflowDetails?.workflowId;\n      const status = 'rejected';\n      const reviewedBy = workflowDetails?.reviewedBy;\n      const message = feedback;\n      console.log(id, workflowId, status, reviewedBy, message);\n      // Show loading dialog\n      this.dialogService.loading({\n        title: 'Rejecting Workflow...',\n        message: 'Please wait while we reject the workflow.',\n        showProgress: false,\n        showCancelButton: false\n      });\n      this.approvalService.rejectWorkflow(id, workflowId, status, reviewedBy, message).subscribe({\n        next: response => {\n          this.dialogService.close(); // Close loading dialog\n          const message = response?.message || this.labels.workflowSuccessRejectMessage;\n          this.dialogService.success({\n            title: 'Workflow Rejected',\n            message: message\n          }).then(() => {\n            this.loadWorkflowReviews(); // Refresh the list\n          });\n        },\n        error: error => {\n          this.dialogService.close(); // Close loading dialog\n          const errorMessage = error?.error?.message || this.labels.defaultErrorMessage;\n          this.dialogService.error({\n            title: 'Rejection Failed',\n            message: errorMessage,\n            showRetryButton: true,\n            retryButtonText: 'Retry'\n          }).then(result => {\n            if (result.action === 'retry') {\n              this.handleWorkflowRejection(feedback);\n            }\n          });\n        }\n      });\n    }\n    handleTesting(index) {\n      console.log(index);\n      const workflowId = this.filteredWorkflowReviews[index].workflowId;\n      this.selectedWorkflowId = workflowId;\n    }\n    onCardClick(index) {\n      console.log('Selected card index:', index);\n      this.selectedIndex = index;\n      const selectedWorkflow = this.filteredWorkflowReviews[this.selectedIndex];\n      this.selectedWorkflowId = selectedWorkflow.workflowId;\n      this.loadPreviewData(selectedWorkflow);\n      console.log(selectedWorkflow);\n      this.drawerService.open(AgentsPreviewPanelComponent, {\n        previewData: this.previewData,\n        closePreview: () => this.drawerService.clear(),\n        editWorkflow: () => this.handleEditWorkflow(selectedWorkflow.workflowId),\n        rejectApproval: () => this.handeMetaDataSendback(),\n        approveApproval: () => this.handleMetaDataApproval(),\n        testApproval: () => this.redirectToWorkflowPlayground()\n      });\n    }\n    loadPreviewData(selectedWorkflow) {\n      this.previewData = {\n        type: 'workflow',\n        title: selectedWorkflow.workflowName,\n        data: selectedWorkflow,\n        loading: true,\n        error: null\n      };\n      this.workflowService.getWorkflowById(selectedWorkflow.workflowId).subscribe({\n        next: response => {\n          console.log('Workflow details', response);\n          this.previewData.data = response;\n          this.previewData.loading = false;\n          this.previewData.error = null;\n        },\n        error: error => {\n          console.error('Error:', error);\n        }\n      });\n    }\n    redirectToWorkflowPlayground() {\n      this.drawerService.clear();\n      this.router.navigate(['/build/workflows/execute', this.selectedWorkflowId]);\n    }\n    handleEditWorkflow(workflowId) {\n      console.log('Edit Workflow', workflowId);\n      this.drawerService.clear();\n      this.router.navigate(['/build/workflows/edit', workflowId]);\n    }\n    updateConsoleApproval(data, type) {\n      this.consoleApproval = {\n        contents: data?.map(req => {\n          const statusIcons = {\n            approved: 'circle-check-big',\n            rejected: 'circle-x',\n            review: 'clock'\n          };\n          const statusTexts = {\n            approved: this.labels.approved,\n            rejected: this.labels.rejected,\n            review: this.labels.review\n          };\n          const statusKey = this.toRequestStatus(req?.status);\n          const specificId = req.workflowId;\n          const title = req.workflowName;\n          return {\n            id: req.id,\n            refId: specificId,\n            type: type,\n            session1: {\n              title: title,\n              labels: [{\n                name: type,\n                color: 'success',\n                background: 'red',\n                type: 'normal'\n              }, {\n                name: req.changeRequestType,\n                color: req.changeRequestType === 'update' ? 'error' : 'info',\n                background: 'red',\n                type: 'pill'\n              }]\n            },\n            session2: [{\n              name: type,\n              color: 'default',\n              background: 'red',\n              type: 'normal'\n            }, {\n              name: req.status,\n              color: 'default',\n              background: 'red',\n              type: 'normal'\n            }],\n            session3: [{\n              iconName: 'user',\n              label: req.requestedBy\n            }, {\n              iconName: 'calendar-days',\n              label: formatDate(req?.requestedAt, 'dd MMM yyyy', 'en-IN')\n            }],\n            session4: {\n              status: statusTexts[statusKey],\n              iconName: statusIcons[statusKey]\n            }\n          };\n        }),\n        footer: {}\n      };\n    }\n    static ɵfac = function ApprovalWorkflowsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ApprovalWorkflowsComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.ApprovalService), i0.ɵɵdirectiveInject(i3.FormBuilder), i0.ɵɵdirectiveInject(i4.WorkflowService), i0.ɵɵdirectiveInject(i5.DrawerService), i0.ɵɵdirectiveInject(i6.DialogService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ApprovalWorkflowsComponent,\n      selectors: [[\"app-approval-workflows\"]],\n      decls: 19,\n      vars: 19,\n      consts: [[1, \"approval-right-screen\"], [1, \"approval-title-filter\"], [3, \"iconName\", \"title\", \"value\", \"subtitle\"], [1, \"filter-section\"], [1, \"search-bars\"], [2, \"font-size\", \"1.5rem\", \"font-weight\", \"bold\", \"color\", \"black\"], [1, \"approval-card-header\"], [1, \"textbox\", \"section\"], [3, \"formGroup\"], [\"formControlName\", \"search\", 3, \"placeholder\"], [\"slot\", \"icon-start\", \"iconName\", \"search\", \"iconColor\", \"var(--color-brand-primary)\", 3, \"iconSize\"], [1, \"approval-card-section\"], [1, \"no-pending-message\"], [1, \"approval-card-wrapper\"], [1, \"approval-card-wrapper\", 3, \"click\"], [\"height\", \"300\"], [\"iconSize\", \"20\", \"iconName\", \"ellipsis-vertical\"], [1, \"header\"], [\"color\", \"info\", \"size\", \"sm\", 3, \"label\"], [1, \"a-content\"], [1, \"box\", \"tag-wrapper\"], [\"label\", \"Individual\", \"size\", \"sm\"], [\"label\", \"Ascendion\", \"size\", \"sm\"], [\"label\", \"Digital Ascender\", \"size\", \"sm\"], [\"label\", \"Platform Engineering\", \"size\", \"sm\"], [1, \"box\", \"info-wrapper\"], [1, \"f\"], [\"iconSize\", \"13\", \"iconName\", \"user\"], [1, \"ml-auto\", \"s\"], [\"iconSize\", \"20\", \"iconName\", \"calendar-days\"], [1, \"footer-content\"], [1, \"footer-left\"], [1, \"ex\"], [\"iconSize\", \"20\", \"iconName\", \"circle-check-big\"], [1, \"footer-right\"], [\"label\", \"Test\", \"variant\", \"secondary\", \"size\", \"medium\", \"state\", \"default\", \"iconName\", \"play\", \"iconPosition\", \"left\", 3, \"userClick\"], [\"label\", \"Sendback\", \"variant\", \"secondary\", \"size\", \"medium\", \"state\", \"default\", \"iconName\", \"move-left\", \"iconPosition\", \"left\", 3, \"userClick\"], [\"label\", \"Approve\", \"variant\", \"primary\", \"size\", \"medium\", \"state\", \"default\", \"iconName\", \"Check\", \"iconPosition\", \"left\", 3, \"userClick\"]],\n      template: function ApprovalWorkflowsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"app-approval-txt-card\", 2)(3, \"app-approval-txt-card\", 2)(4, \"app-approval-txt-card\", 2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 3)(6, \"div\", 4)(7, \"div\", 5);\n          i0.ɵɵtext(8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"div\", 6);\n          i0.ɵɵtext(10);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"div\", 7)(12, \"div\")(13, \"form\", 8)(14, \"ava-textbox\", 9);\n          i0.ɵɵelement(15, \"ava-icon\", 10);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(16, \"div\", 11);\n          i0.ɵɵtemplate(17, ApprovalWorkflowsComponent_Conditional_17_Template, 2, 0)(18, ApprovalWorkflowsComponent_Conditional_18_Template, 2, 1, \"div\", 12);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"iconName\", \"hourglass\")(\"title\", ctx.labels.totalApprovals)(\"value\", ctx.totalApprovals)(\"subtitle\", ctx.currentTab + \" \" + ctx.labels.whichAreRequestedForApproval);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"iconName\", \"shield-alert\")(\"title\", ctx.labels.totalApprovedApprovals)(\"value\", ctx.totalApprovedApprovals)(\"subtitle\", ctx.currentTab + \" \" + ctx.labels.whichAreApproved);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"iconName\", \"hourglass\")(\"title\", ctx.labels.totalPendingApprovals)(\"value\", ctx.totalPendingApprovals)(\"subtitle\", ctx.labels.all + \" \" + ctx.currentTab + \" \" + ctx.labels.awaitingApproval);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", ctx.currentTab, \" Approvals \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate2(\" All - \", ctx.totalRecords, \" \", ctx.currentTab, \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"formGroup\", ctx.searchForm);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"placeholder\", ctx.labels.searchPlaceholder);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"iconSize\", 16);\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(ctx.totalRecords > 0 ? 17 : 18);\n        }\n      },\n      dependencies: [CommonModule, RouterModule, ApprovalCardComponent, IconComponent, AvaTextboxComponent, ReactiveFormsModule, i3.ɵNgNoValidate, i3.NgControlStatus, i3.NgControlStatusGroup, i3.FormGroupDirective, i3.FormControlName, AvaTagComponent, ButtonComponent, ApprovalTxtCardComponent],\n      styles: [\".approval[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: row;\\n  width: 100%;\\n  height: 100%;\\n}\\n\\n.approval-left-screen[_ngcontent-%COMP%] {\\n  flex: 0 0 70px; \\n\\n  width: 70px;\\n  transition: all var(--transition-speed) ease;\\n  height: 120vh; \\n\\n  overflow: hidden;\\n}\\n.approval-left-screen.quick-actions-expanded[_ngcontent-%COMP%] {\\n  flex: 0 0 250px;\\n  margin-right: 15px;\\n}\\n\\n\\n\\n.approval-right-screen[_ngcontent-%COMP%] {\\n  flex: 1; \\n\\n  padding: 1rem; \\n\\n  overflow-y: auto; \\n\\n}\\n\\n\\n\\n.approval-title-filter[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  margin-bottom: 1rem; \\n\\n}\\n\\n\\n\\n.approvals-title[_ngcontent-%COMP%] {\\n  font-weight: bold;\\n  font-size: 1.2rem;\\n  margin-bottom: 0.5rem;\\n}\\n\\n\\n\\n.filter-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 0 1rem; \\n\\n  margin-bottom: 1rem; \\n\\n}\\n\\n\\n\\n.search-bars[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 1rem; \\n\\n}\\n\\n\\n\\n.textbox.section[_ngcontent-%COMP%] {\\n  margin-left: 1rem;\\n}\\n\\n.textbox.section[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center; \\n\\n  gap: 8px; \\n\\n}\\n\\n.approval-card-header[_ngcontent-%COMP%] {\\n  font-size: 1.25rem; \\n\\n  font-weight: 600; \\n\\n  color: grey; \\n \\n\\n  margin-bottom: 1.5rem; \\n\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between; \\n\\n}\\n\\n.approval-title-filter[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: row;\\n  justify-content: space-between;\\n  align-items: center;\\n  gap: 1rem;\\n  flex-wrap: nowrap;\\n  width: 100%;\\n  padding: 1rem 0;\\n}\\n\\n.approval-title-filter[_ngcontent-%COMP%]    > ava-text-card[_ngcontent-%COMP%] {\\n  flex: 1 1 22%;\\n  min-width: 200px;\\n}\\n\\n.quick-actions-wrapper[_ngcontent-%COMP%] {\\n  grid-area: quick-actions;\\n  background-color: var(--dashboard-card-bg);\\n  border-radius: var(--border-radius-standard);\\n  display: flex;\\n  flex-direction: column;\\n  width: 55px;\\n  height: 250vh;\\n  transition: all 0.35s cubic-bezier(0.4, 0, 0.2, 1);\\n  overflow: hidden;\\n  box-shadow: var(--shadow-medium);\\n  border: var(--border-thin);\\n  position: relative;\\n  \\n\\n  \\n\\n  \\n\\n  \\n\\n}\\n.quick-actions-wrapper[_ngcontent-%COMP%]:hover {\\n  box-shadow: var(--shadow-hover);\\n}\\n.quick-actions-wrapper.expanded[_ngcontent-%COMP%] {\\n  width: 100%; \\n\\n}\\n@media (min-width: 1900px) and (max-width: 1930px) and (max-height: 1100px) {\\n  .quick-actions-wrapper[_ngcontent-%COMP%] {\\n    height: 595px;\\n  }\\n}\\n@media (min-width: 1200px) and (max-width: 1400px) {\\n  .quick-actions-wrapper[_ngcontent-%COMP%] {\\n    height: 580px !important;\\n    max-height: 580px !important;\\n  }\\n}\\n@media (max-width: 1200px) {\\n  .quick-actions-wrapper[_ngcontent-%COMP%] {\\n    height: 320px;\\n  }\\n}\\n@media (max-width: 992px) {\\n  .quick-actions-wrapper[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    width: 100%;\\n    height: 48px;\\n  }\\n  .quick-actions-wrapper.expanded[_ngcontent-%COMP%] {\\n    height: auto;\\n    max-height: 320px;\\n    width: 100%;\\n  }\\n}\\n@media (max-width: 576px) {\\n  .quick-actions-wrapper[_ngcontent-%COMP%] {\\n    height: 280px;\\n  }\\n}\\n@media (min-width: 1200px) and (max-width: 1366px) and (max-height: 800px) {\\n  .quick-actions-wrapper[_ngcontent-%COMP%] {\\n    height: 100%;\\n  }\\n  .quick-actions-wrapper[_ngcontent-%COMP%]     .card-container {\\n    height: 100%;\\n  }\\n}\\n.quick-actions-wrapper[_ngcontent-%COMP%]     .quick-actions-card {\\n  height: 100% !important;\\n  width: 100% !important;\\n  \\n\\n  \\n\\n}\\n.quick-actions-wrapper[_ngcontent-%COMP%]     .quick-actions-card .card-container {\\n  height: 100% !important;\\n  width: 100% !important;\\n  padding: 0 !important;\\n  overflow: hidden !important;\\n  display: flex !important;\\n  flex-direction: column !important;\\n}\\n.quick-actions-wrapper[_ngcontent-%COMP%]     .quick-actions-card .card-container .card-body {\\n  padding: 0 !important;\\n  height: 100% !important;\\n  display: flex !important;\\n  flex-direction: column !important;\\n}\\n.quick-actions-wrapper[_ngcontent-%COMP%]     .quick-actions-card .card-content {\\n  height: 100% !important;\\n  display: flex !important;\\n  flex-direction: column !important;\\n  padding: 0 !important;\\n}\\n@media (max-width: 992px) {\\n  .quick-actions-wrapper[_ngcontent-%COMP%]     .quick-actions-card .card-content {\\n    flex-direction: row !important;\\n  }\\n}\\n@media (max-width: 992px) {\\n  .quick-actions-wrapper[_ngcontent-%COMP%]     .quick-actions-card .card-container {\\n    height: 48px !important;\\n    width: 100% !important;\\n    flex-direction: row !important;\\n  }\\n  .quick-actions-wrapper[_ngcontent-%COMP%]     .quick-actions-card .card-container.expanded {\\n    height: auto !important;\\n  }\\n}\\n@media (min-width: 1200px) and (max-width: 1366px) and (max-height: 800px) {\\n  .quick-actions-wrapper[_ngcontent-%COMP%]     .quick-actions-card .card-container {\\n    width: 100% !important;\\n  }\\n}\\n\\n.quick-actions-content[_ngcontent-%COMP%] {\\n  padding: 20px 16px;\\n  overflow-y: auto;\\n  flex-grow: 1;\\n}\\n.quick-actions-content[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px; \\n\\n}\\n.quick-actions-content[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: flex-start;\\n  gap: 16px; \\n\\n  padding: 16px 20px; \\n\\n  border-radius: 12px; \\n\\n  border: none;\\n  border: 2px solid transparent;\\n  background: linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%);\\n  background-origin: border-box;\\n  background-clip: padding-box, border-box;\\n  --button-effect-color: 33, 90, 214;\\n  cursor: pointer;\\n  transition: all var(--transition-speed) ease;\\n  width: 100%;\\n  text-align: left;\\n  color: #fff;\\n}\\n.quick-actions-content[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%]   .action-icon[_ngcontent-%COMP%], \\n.quick-actions-content[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%]   .action-label[_ngcontent-%COMP%] {\\n  color: #fff;\\n}\\n.quick-actions-content[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%]:hover {\\n  opacity: 0.9;\\n  background: linear-gradient(103.35deg, #03BDD4 31.33%, #215AD6 100%);\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px var(--dashboard-shadow-hover);\\n}\\n.quick-actions-content[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%]   .action-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 24px;\\n  height: 24px;\\n}\\n.quick-actions-content[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%]   .action-icon[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  filter: brightness(0) invert(1); \\n\\n}\\n.quick-actions-content[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%]   .action-label[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 500;\\n  color: linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%); \\n\\n}\\n\\n.action-button.active-action[_ngcontent-%COMP%] {\\n  background: linear-gradient(103.35deg, #03BDD4 31.33%, #215AD6 100%) !important;\\n}\\n\\n.quick-actions-toggle[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n  padding: 20px 16px;\\n  padding-bottom: 0px;\\n  cursor: pointer;\\n  transition: background-color 0.35s cubic-bezier(0.4, 0, 0.2, 1);\\n  flex-shrink: 0;\\n  \\n\\n  \\n\\n}\\n.quick-actions-wrapper[_ngcontent-%COMP%]:not(.expanded)   .quick-actions-toggle[_ngcontent-%COMP%] {\\n  padding: 20px 0;\\n  justify-content: center;\\n}\\n.quick-actions-toggle[_ngcontent-%COMP%]   .toggle-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 36px;\\n  height: 36px;\\n  border-radius: 8px;\\n  background-color: transparent;\\n  position: relative;\\n  \\n\\n}\\n.quick-actions-toggle[_ngcontent-%COMP%]   .toggle-button[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  inset: 0;\\n  border-radius: 8px;\\n  padding: 1px;\\n  background: var(--dashboard-gradient);\\n  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);\\n  mask-composite: exclude;\\n  transition: opacity 0.35s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.quick-actions-toggle[_ngcontent-%COMP%]   .toggle-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  transition: transform 0.35s cubic-bezier(0.4, 0, 0.2, 1);\\n  width: 16px;\\n  height: 16px;\\n  stroke: var(--dashboard-toggle-stroke);\\n  z-index: 1;\\n}\\n.quick-actions-toggle[_ngcontent-%COMP%]   .toggle-button[_ngcontent-%COMP%]   svg.rotate[_ngcontent-%COMP%] {\\n  transform: rotate(180deg);\\n}\\n.quick-actions-toggle[_ngcontent-%COMP%]   .quick-actions-wrapper[_ngcontent-%COMP%]:not(.expanded)   .toggle-button[_ngcontent-%COMP%] {\\n  background: var(--dashboard-gradient);\\n  transition: background 0.35s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.quick-actions-toggle[_ngcontent-%COMP%]   .quick-actions-wrapper[_ngcontent-%COMP%]:not(.expanded)   .toggle-button[_ngcontent-%COMP%]::before {\\n  display: none;\\n}\\n.quick-actions-toggle[_ngcontent-%COMP%]   .quick-actions-wrapper[_ngcontent-%COMP%]:not(.expanded)   .toggle-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  stroke: var(--dashboard-toggle-stroke-collapsed);\\n  transition: stroke 0.35s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.quick-actions-toggle[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-weight: 580;\\n  font-size: 16px;\\n  color: var(--dashboard-text-primary);\\n  opacity: 1;\\n  transition: opacity 0.35s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n\\n.quick-actions-icons[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 16px; \\n\\n  padding: 20px 0; \\n\\n  height: 150vh;\\n}\\n@media (max-width: 992px) {\\n  .quick-actions-icons[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    justify-content: center;\\n    flex-wrap: wrap;\\n    padding: 8px;\\n  }\\n}\\n.quick-actions-icons[_ngcontent-%COMP%]   .icon-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 36px; \\n\\n  height: 36px; \\n\\n  border-radius: 8px; \\n\\n  border: none;\\n  background: linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%);\\n  cursor: pointer;\\n  transition: all var(--transition-speed) ease;\\n}\\n.quick-actions-icons[_ngcontent-%COMP%]   .icon-button[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(103.35deg, #03BDD4 31.33%, #215AD6 100%);\\n  opacity: 0.9;\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px var(--dashboard-shadow-hover);\\n}\\n.quick-actions-icons[_ngcontent-%COMP%]   .icon-button[_ngcontent-%COMP%]   .icon-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.quick-actions-icons[_ngcontent-%COMP%]   .icon-button[_ngcontent-%COMP%]   .icon-wrapper[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  filter: brightness(0) invert(1); \\n\\n}\\n\\n.icon-button.active-action[_ngcontent-%COMP%] {\\n  background: linear-gradient(103.35deg, #03BDD4 31.33%, #215AD6 100%) !important;\\n}\\n\\n.approval-card-section[_ngcontent-%COMP%] {\\n  height: 780px;\\n  overflow-y: auto;\\n}\\n\\n.approval-card-wrapper[_ngcontent-%COMP%] {\\n  margin: 10px;\\n}\\n\\n.no-pending-message[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin-top: 20%;\\n  font-size: 1.2rem;\\n  color: #000000;\\n  font-weight: 500;\\n  text-align: center;\\n  border-radius: 16px;\\n  min-height: 100px;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .filter-section[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: stretch;\\n  }\\n  .search-bars[_ngcontent-%COMP%], \\n   .textbox.section[_ngcontent-%COMP%] {\\n    width: 100%;\\n    margin: 0 0 0.5rem 0;\\n    justify-content: center;\\n  }\\n  .search-bars[_ngcontent-%COMP%] {\\n    flex-wrap: wrap;\\n    gap: 0.5rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return ApprovalWorkflowsComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "formatDate", "ReactiveFormsModule", "RouterModule", "ApprovalCardComponent", "IconComponent", "AvaTextboxComponent", "AvaTagComponent", "ButtonComponent", "approvalText", "debounceTime", "distinctUntilChanged", "map", "startWith", "AgentsPreviewPanelComponent", "ApprovalTxtCardComponent", "i0", "ɵɵelementStart", "ɵɵlistener", "ApprovalWorkflowsComponent_Conditional_17_For_1_Template_div_click_0_listener", "$index_r2", "ɵɵrestoreView", "_r1", "$index", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "onCardClick", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ApprovalWorkflowsComponent_Conditional_17_For_1_Template_ava_button_userClick_34_listener", "handleTesting", "ApprovalWorkflowsComponent_Conditional_17_For_1_Template_ava_button_userClick_35_listener", "rejectApproval", "ApprovalWorkflowsComponent_Conditional_17_For_1_Template_ava_button_userClick_36_listener", "approveApproval", "ɵɵadvance", "ɵɵtextInterpolate", "item_r4", "session1", "title", "ɵɵpropertyInterpolate", "currentTab", "session3", "label", "session4", "status", "ɵɵrepeaterCreate", "ApprovalWorkflowsComponent_Conditional_17_For_1_Template", "ɵɵrepeaterTrackByIndex", "ɵɵrepeater", "consoleApproval", "contents", "ɵɵtextInterpolate1", "ApprovalWorkflowsComponent", "router", "approvalService", "fb", "workflowService", "drawerService", "dialogService", "appLabels", "labels", "totalApprovedApprovals", "totalPendingApprovals", "totalApprovals", "isBasicCollapsed", "quickActionsExpanded", "options", "basicSidebarItems", "quickActions", "toolReviews", "workflowReviews", "filteredWorkflowReviews", "agentsReviews", "currentToolsPage", "currentAgentsPage", "currentWorkflowsPage", "pageSize", "totalRecords", "isDeleted", "selectedIndex", "searchForm", "approvedAgentId", "previewData", "selectedWorkflowId", "constructor", "name", "electronics", "value", "clothing", "books", "id", "icon", "text", "agents", "route", "active", "workflows", "tools", "group", "search", "ngOnInit", "searchList", "loadWorkflowReviews", "console", "log", "get", "valueChanges", "pipe", "toLowerCase", "subscribe", "searchText", "applyFilter", "lower", "updateConsoleApproval", "filter", "item", "workflowName", "includes", "onSelectionChange", "data", "uClick", "i", "toggleQuickActions", "onBasicCollapseToggle", "isCollapsed", "onBasicItemClick", "for<PERSON>ach", "toRequestStatus", "handleMetaDataApproval", "showApprovalDialog", "handeMetaDataSendback", "showFeedbackDialog", "getAllReviewWorkflows", "response", "workflowReviewDetails", "r", "length", "loadMoreWorkflows", "page", "loadReviews", "idx", "confirmation", "confirmApproval", "message", "youAreAboutToApproveThis", "itWillBeActiveAndAvailableIn", "catalogueForUsersToExecute", "confirmButtonText", "approve", "cancelButtonText", "confirmButtonVariant", "then", "result", "confirmed", "handleApproval", "customButtons", "variant", "action", "feedback", "buttons", "handleRejection", "handleWorkflowApproval", "handleWorkflowRejection", "workflowDetails", "workflowId", "reviewedBy", "loading", "showProgress", "showCancelButton", "approveWorkflow", "next", "close", "workflowSuccessApproveMessage", "success", "navigate", "error", "errorMessage", "defaultErrorMessage", "showRetryButton", "retryButtonText", "rejectWorkflow", "workflowSuccessRejectMessage", "index", "selectedWorkflow", "loadPreviewData", "open", "closePreview", "clear", "editWorkflow", "handleEditWorkflow", "testApproval", "redirectToWorkflowPlayground", "type", "getWorkflowById", "req", "statusIcons", "approved", "rejected", "review", "statusTexts", "statusKey", "specificId", "refId", "color", "background", "changeRequestType", "session2", "iconName", "requestedBy", "requestedAt", "footer", "ɵɵdirectiveInject", "i1", "Router", "i2", "ApprovalService", "i3", "FormBuilder", "i4", "WorkflowService", "i5", "DrawerService", "i6", "DialogService", "selectors", "decls", "vars", "consts", "template", "ApprovalWorkflowsComponent_Template", "rf", "ctx", "ɵɵtemplate", "ApprovalWorkflowsComponent_Conditional_17_Template", "ApprovalWorkflowsComponent_Conditional_18_Template", "ɵɵproperty", "whichAreRequestedForApproval", "whichAreApproved", "all", "awaitingApproval", "ɵɵtextInterpolate2", "searchPlaceholder", "ɵɵconditional", "ɵNgNoValidate", "NgControlStatus", "NgControlStatusGroup", "FormGroupDirective", "FormControlName", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\console\\src\\app\\pages\\approval\\approval-workflows\\approval-workflows.component.ts", "C:\\console\\aava-ui-web\\projects\\console\\src\\app\\pages\\approval\\approval-workflows\\approval-workflows.component.html"], "sourcesContent": ["import { CommonModule, formatDate } from '@angular/common';\r\nimport { Component, CUSTOM_ELEMENTS_SCHEMA, OnInit } from '@angular/core';\r\nimport { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';\r\nimport { Router, RouterModule } from '@angular/router';\r\nimport {\r\n  ApprovalCardComponent,\r\n  IconComponent,\r\n  AvaTextboxComponent,\r\n  TextCardComponent,\r\n  PopupComponent,\r\n  ConfirmationPopupComponent,\r\n  AvaTagComponent,\r\n  DropdownOption,\r\n  ButtonComponent,\r\n  DialogService,\r\n  DialogButton,\r\n} from '@ava/play-comp-library';\r\nimport approvalText from '../constants/approval.json';\r\nimport { ApprovalService } from '../../../shared/services/approval.service';\r\nimport { debounceTime, distinctUntilChanged, map, startWith } from 'rxjs';\r\nimport { DrawerService } from '../../../shared/services/drawer/drawer.service';\r\nimport { WorkflowService } from '../../../shared/services/workflow.service';\r\nimport { AgentsPreviewPanelComponent } from './agents-preview-panel/agents-preview-panel.component'; \r\nimport { ApprovalTxtCardComponent } from '../approval-text-card/approval-text-card.component';\r\n\r\ntype RequestStatus = 'approved' | 'rejected' | 'review';\r\n\r\n@Component({\r\n  selector: 'app-approval-workflows',\r\n  imports: [\r\n    CommonModule,\r\n    RouterModule,\r\n    ApprovalCardComponent,\r\n    IconComponent,\r\n    AvaTextboxComponent,\r\n    ReactiveFormsModule,\r\n    AvaTagComponent,\r\n    ButtonComponent,\r\n    PopupComponent,\r\n    ConfirmationPopupComponent,\r\n    ApprovalTxtCardComponent\r\n  ],\r\n  schemas: [CUSTOM_ELEMENTS_SCHEMA],\r\n  templateUrl: './approval-workflows.component.html',\r\n  styleUrls: ['./approval-workflows.component.scss'],\r\n})\r\nexport class ApprovalWorkflowsComponent implements OnInit {\r\n  appLabels = approvalText.labels;\r\n  public totalApprovedApprovals: number = 20;\r\n  public totalPendingApprovals: number = 15;\r\n  public totalApprovals: number = 60;\r\n  public isBasicCollapsed: boolean = false;\r\n  public quickActionsExpanded: boolean = true;\r\n  public consoleApproval: any = {};\r\n  public options: DropdownOption[] = [];\r\n  public basicSidebarItems: any[] = [];\r\n  public quickActions: any[] = [];\r\n  public toolReviews: any[] = [];\r\n  public workflowReviews: any[] = [];\r\n  public filteredWorkflowReviews: any[] = [];\r\n  public agentsReviews: any[] = [];\r\n  public currentToolsPage = 1;\r\n  public currentAgentsPage = 1;\r\n  public currentWorkflowsPage = 1;\r\n  public pageSize = 50;\r\n  public totalRecords = 0;\r\n  public isDeleted = false;\r\n  public currentTab = 'Workflows';\r\n  public selectedIndex = 0;\r\n  public searchForm!: FormGroup;\r\n  public labels: any = approvalText.labels;\r\n  public approvedAgentId: number | null = null;\r\n  public previewData: any = null;\r\n  public selectedWorkflowId: number = 0;\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private approvalService: ApprovalService,\r\n    private fb: FormBuilder,\r\n    private workflowService: WorkflowService,\r\n    private drawerService: DrawerService,\r\n    private dialogService: DialogService\r\n  ) {\r\n    this.labels = approvalText.labels;\r\n    this.options = [\r\n      { name: this.labels.electronics, value: 'electronics' },\r\n      { name: this.labels.clothing, value: 'clothing' },\r\n      { name: this.labels.books, value: 'books' },\r\n    ];\r\n    this.basicSidebarItems = [\r\n      {\r\n        id: '1',\r\n        icon: 'hammer',\r\n        text: this.labels.agents,\r\n        route: '',\r\n        active: true,\r\n      },\r\n      { id: '2', icon: 'circle-check', text: this.labels.workflows, route: '' },\r\n      { id: '3', icon: 'bot', text: this.labels.tools, route: '' },\r\n    ];\r\n    this.quickActions = [\r\n      {\r\n        icon: 'awe_agents',\r\n        label: this.labels.agents,\r\n        route: '',\r\n      },\r\n      {\r\n        icon: 'awe_workflows',\r\n        label: this.labels.workflows,\r\n        route: '',\r\n      },\r\n      {\r\n        icon: 'awe_tools',\r\n        label: this.labels.tools,\r\n        route: '',\r\n      },\r\n    ];\r\n    this.searchForm = this.fb.group({\r\n      search: [''],\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.searchList();\r\n    this.totalApprovals = 60;\r\n    this.loadWorkflowReviews();\r\n  }\r\n\r\n  public searchList() {\r\n    console.log(this.searchForm.get('search')?.value);\r\n    this.searchForm\r\n      .get('search')!\r\n      .valueChanges.pipe(\r\n        startWith(''),\r\n        debounceTime(300),\r\n        distinctUntilChanged(),\r\n        map((value) => value?.toLowerCase() ?? ''),\r\n      )\r\n      .subscribe((searchText) => {\r\n        this.applyFilter(searchText);\r\n      });\r\n  }\r\n\r\n  public applyFilter(text: string) {\r\n    const lower = text;\r\n    if (!text) {\r\n      this.updateConsoleApproval(this.workflowReviews, 'workflow');\r\n      return;\r\n    }\r\n\r\n    this.filteredWorkflowReviews = this.workflowReviews.filter((item) =>\r\n      item.workflowName?.toLowerCase().includes(lower),\r\n    );\r\n    this.updateConsoleApproval(this.filteredWorkflowReviews, 'workflow');\r\n  }\r\n\r\n  public onSelectionChange(data: any) {\r\n    console.log('Selection changed:', data);\r\n  }\r\n\r\n  public uClick(i: any) {\r\n    console.log('log' + i);\r\n  }\r\n\r\n  public toggleQuickActions(): void {\r\n    this.quickActionsExpanded = !this.quickActionsExpanded;\r\n  }\r\n\r\n  public onBasicCollapseToggle(isCollapsed: boolean): void {\r\n    this.isBasicCollapsed = isCollapsed;\r\n    console.log('Basic sidebar collapsed:', isCollapsed);\r\n  }\r\n\r\n  public onBasicItemClick(item: any): void {\r\n    this.basicSidebarItems.forEach((i) => (i.active = false));\r\n    item.active = true;\r\n    console.log(item);\r\n  }\r\n\r\n  public toRequestStatus(value: string | null | undefined): RequestStatus {\r\n    return value === 'approved' || value === 'rejected' || value === 'review'\r\n      ? value\r\n      : 'review';\r\n  }\r\n\r\n  public handleMetaDataApproval(){\r\n    this.showApprovalDialog();\r\n  }\r\n\r\n  public handeMetaDataSendback(){\r\n    this.showFeedbackDialog();\r\n  }\r\n\r\n  public loadWorkflowReviews() {\r\n    this.approvalService\r\n      .getAllReviewWorkflows(\r\n        this.currentWorkflowsPage,\r\n        this.pageSize,\r\n        this.isDeleted,\r\n      )\r\n      .subscribe((response) => {\r\n        if (this.currentWorkflowsPage > 1) {\r\n          this.workflowReviews = [\r\n            ...this.workflowReviews,\r\n            ...response.workflowReviewDetails,\r\n          ];\r\n        } else {\r\n          this.workflowReviews = response?.workflowReviewDetails;\r\n        }\r\n        this.workflowReviews = this.workflowReviews.filter(\r\n          (r) => r.status !== 'approved',\r\n        );\r\n        this.filteredWorkflowReviews = this.workflowReviews;\r\n        this.totalRecords = this.workflowReviews.length;\r\n        //   console.log('workflow reviews ', this.workflowReviews);\r\n        this.updateConsoleApproval(this.workflowReviews, 'workflow');\r\n      });\r\n  }\r\n\r\n  public loadMoreWorkflows(page: number) {\r\n    this.currentWorkflowsPage = page;\r\n    this.loadWorkflowReviews();\r\n  }\r\n\r\n  public loadReviews(name: string) {\r\n    this.currentTab = name;\r\n    this.loadWorkflowReviews();\r\n  }\r\n\r\n  public rejectApproval(idx: any) {\r\n    console.log(idx);\r\n    this.selectedIndex = idx;\r\n  }\r\n\r\n  public approveApproval(idx: any) {\r\n    console.log(idx);\r\n    this.selectedIndex = idx;\r\n    console.log(this.filteredWorkflowReviews[this.selectedIndex]);\r\n  }\r\n\r\n  private showApprovalDialog(): void {\r\n    this.dialogService.confirmation({\r\n      title: this.labels.confirmApproval,\r\n      message: `${this.labels.youAreAboutToApproveThis} Workflow. ${this.labels.itWillBeActiveAndAvailableIn} ${this.currentTab} ${this.labels.catalogueForUsersToExecute}`,\r\n      confirmButtonText: this.labels.approve,\r\n      cancelButtonText: 'Cancel',\r\n      confirmButtonVariant: 'danger',\r\n      icon:'circle-check'\r\n    }).then(result => {\r\n      if (result.confirmed) {\r\n        this.handleApproval();\r\n      }\r\n    });\r\n  }\r\n\r\n  private showFeedbackDialog(): void {\r\n     const customButtons: DialogButton[] = [\r\n          { label: 'Cancel', variant: 'secondary', action: 'cancel' },\r\n          { label: 'Send Back', variant: 'primary', action: 'sendback' }\r\n        ];\r\n    this.dialogService.feedback({\r\n      title: 'Confirm Send Back',\r\n      message: 'This Workflow will be send back for corrections and modification. Kindly comment what needs to be done.',\r\n      buttons:customButtons,\r\n    }).then(result => {\r\n      if (result.confirmed && result.confirmed === true) {\r\n        this.handleRejection(result.data);\r\n      }\r\n    });\r\n  }\r\n\r\n  public handleApproval() {\r\n    this.handleWorkflowApproval();\r\n  }\r\n\r\n  public handleRejection(feedback: any) {\r\n    this.handleWorkflowRejection(feedback);\r\n  }\r\n\r\n  public handleWorkflowApproval() {\r\n    const workflowDetails = this.filteredWorkflowReviews[this.selectedIndex];\r\n    const id = workflowDetails?.id;\r\n    const workflowId = workflowDetails?.workflowId;\r\n    const status = 'approved';\r\n    const reviewedBy = workflowDetails?.reviewedBy;\r\n    console.log(id, workflowId, status, reviewedBy);\r\n\r\n    // Show loading dialog\r\n    this.dialogService.loading({\r\n      title: 'Approving Workflow...',\r\n      message: 'Please wait while we approve the workflow.',\r\n      showProgress: false,\r\n      showCancelButton: false\r\n    });\r\n\r\n    this.approvalService\r\n      .approveWorkflow(id, workflowId, status, reviewedBy)\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.dialogService.close(); // Close loading dialog\r\n          \r\n          const message = response?.message || this.labels.workflowSuccessApproveMessage;\r\n          this.dialogService.success({\r\n            title: 'Workflow Approved',\r\n            message: message\r\n          }).then(result => {\r\n            if (result.action === 'secondary') {\r\n              // Navigate to edit workflow screen\r\n              this.router.navigate(['/build/workflows/edit', workflowId]);\r\n            } else {\r\n              this.loadWorkflowReviews(); // Refresh the list\r\n            }\r\n          });\r\n        },\r\n        error: (error) => {\r\n          this.dialogService.close(); // Close loading dialog\r\n          \r\n          const errorMessage = error?.error?.message || this.labels.defaultErrorMessage;\r\n          this.dialogService.error({\r\n            title: 'Approval Failed',\r\n            message: errorMessage,\r\n            showRetryButton: true,\r\n            retryButtonText: 'Retry'\r\n          }).then(result => {\r\n            if (result.action === 'retry') {\r\n              this.handleWorkflowApproval();\r\n            }\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  public handleWorkflowRejection(feedback: any) {\r\n    const workflowDetails = this.workflowReviews[this.selectedIndex];\r\n    const id = workflowDetails?.id;\r\n    const workflowId = workflowDetails?.workflowId;\r\n    const status = 'rejected';\r\n    const reviewedBy = workflowDetails?.reviewedBy;\r\n    const message = feedback;\r\n    console.log(id, workflowId, status, reviewedBy, message);\r\n\r\n    // Show loading dialog\r\n    this.dialogService.loading({\r\n      title: 'Rejecting Workflow...',\r\n      message: 'Please wait while we reject the workflow.',\r\n      showProgress: false,\r\n      showCancelButton: false\r\n    });\r\n\r\n    this.approvalService\r\n      .rejectWorkflow(id, workflowId, status, reviewedBy, message)\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.dialogService.close(); // Close loading dialog\r\n          \r\n          const message = response?.message || this.labels.workflowSuccessRejectMessage;\r\n          this.dialogService.success({\r\n            title: 'Workflow Rejected',\r\n            message: message\r\n          }).then(() => {\r\n            this.loadWorkflowReviews(); // Refresh the list\r\n          });\r\n        },\r\n        error: (error) => {\r\n          this.dialogService.close(); // Close loading dialog\r\n          \r\n          const errorMessage = error?.error?.message || this.labels.defaultErrorMessage;\r\n          this.dialogService.error({\r\n            title: 'Rejection Failed',\r\n            message: errorMessage,\r\n            showRetryButton: true,\r\n            retryButtonText: 'Retry'\r\n          }).then(result => {\r\n            if (result.action === 'retry') {\r\n              this.handleWorkflowRejection(feedback);\r\n            }\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  public handleTesting(index : any){\r\n    console.log(index);\r\n    const workflowId = this.filteredWorkflowReviews[index].workflowId;\r\n    this.selectedWorkflowId = workflowId;\r\n  }\r\n\r\n  public onCardClick(index: number): void {\r\n    console.log('Selected card index:', index);\r\n    this.selectedIndex = index;\r\n    const selectedWorkflow = this.filteredWorkflowReviews[this.selectedIndex];\r\n    this.selectedWorkflowId = selectedWorkflow.workflowId;\r\n    this.loadPreviewData(selectedWorkflow);\r\n    console.log(selectedWorkflow);\r\n    this.drawerService.open(AgentsPreviewPanelComponent, {\r\n      previewData: this.previewData,\r\n      closePreview: () => this.drawerService.clear(),\r\n      editWorkflow: () => this.handleEditWorkflow(selectedWorkflow.workflowId),\r\n      rejectApproval: () => this.handeMetaDataSendback(),\r\n      approveApproval: () => this.handleMetaDataApproval(),\r\n      testApproval: () => this.redirectToWorkflowPlayground(),\r\n    });\r\n  }\r\n\r\n  public loadPreviewData(selectedWorkflow: any){\r\n    this.previewData = {\r\n      type: 'workflow',\r\n      title: selectedWorkflow.workflowName,\r\n      data: selectedWorkflow,\r\n      loading: true,\r\n      error: null,\r\n    };\r\n    this.workflowService.getWorkflowById(selectedWorkflow.workflowId).subscribe({\r\n      next: (response) => {\r\n        console.log('Workflow details', response);\r\n        this.previewData.data = response;\r\n        this.previewData.loading = false;\r\n        this.previewData.error = null;\r\n      },\r\n      error: (error) => {\r\n        console.error('Error:', error);\r\n      },\r\n    });\r\n  }\r\n\r\n  public redirectToWorkflowPlayground(): void {\r\n    this.drawerService.clear();\r\n    this.router.navigate(['/build/workflows/execute', this.selectedWorkflowId]);\r\n  }\r\n\r\n  public handleEditWorkflow(workflowId: string) {\r\n    console.log('Edit Workflow', workflowId);\r\n    this.drawerService.clear();\r\n    this.router.navigate(['/build/workflows/edit', workflowId]);\r\n  }\r\n\r\n  public updateConsoleApproval(data: any[], type: string) {\r\n    this.consoleApproval = {\r\n      contents: data?.map((req: any) => {\r\n        const statusIcons: Record<RequestStatus, string> = {\r\n          approved: 'circle-check-big',\r\n          rejected: 'circle-x',\r\n          review: 'clock',\r\n        };\r\n        const statusTexts: Record<RequestStatus, string> = {\r\n          approved: this.labels.approved,\r\n          rejected: this.labels.rejected,\r\n          review: this.labels.review,\r\n        };\r\n        const statusKey = this.toRequestStatus(req?.status);\r\n        const specificId = req.workflowId;\r\n        const title = req.workflowName;\r\n\r\n        return {\r\n          id: req.id,\r\n          refId: specificId,\r\n          type: type,\r\n          session1: {\r\n            title: title,\r\n            labels: [\r\n              {\r\n                name: type,\r\n                color: 'success',\r\n                background: 'red',\r\n                type: 'normal',\r\n              },\r\n              {\r\n                name: req.changeRequestType,\r\n                color: req.changeRequestType === 'update' ? 'error' : 'info',\r\n                background: 'red',\r\n                type: 'pill',\r\n              },\r\n            ],\r\n          },\r\n          session2: [\r\n            {\r\n              name: type,\r\n              color: 'default',\r\n              background: 'red',\r\n              type: 'normal',\r\n            },\r\n            {\r\n              name: req.status,\r\n              color: 'default',\r\n              background: 'red',\r\n              type: 'normal',\r\n            },\r\n          ],\r\n          session3: [\r\n            {\r\n              iconName: 'user',\r\n              label: req.requestedBy,\r\n            },\r\n            {\r\n              iconName: 'calendar-days',\r\n              label: formatDate(req?.requestedAt, 'dd MMM yyyy', 'en-IN'),\r\n            },\r\n          ],\r\n          session4: {\r\n            status: statusTexts[statusKey],\r\n            iconName: statusIcons[statusKey],\r\n          },\r\n        };\r\n      }),\r\n      footer: {},\r\n    };\r\n  }\r\n}\r\n", "<div class=\"approval-right-screen\">\r\n    <div class=\"approval-title-filter\">\r\n        <app-approval-txt-card [iconName]=\"'hourglass'\" [title]=\"labels.totalApprovals\" [value]=\"totalApprovals\"\r\n            [subtitle]=\"currentTab + ' ' + labels.whichAreRequestedForApproval\"></app-approval-txt-card>\r\n        <app-approval-txt-card [iconName]=\"'shield-alert'\" [title]=\"labels.totalApprovedApprovals\"\r\n            [value]=\"totalApprovedApprovals\" [subtitle]=\"currentTab + ' ' + labels.whichAreApproved\"></app-approval-txt-card>\r\n        <app-approval-txt-card [iconName]=\"'hourglass'\" [title]=\"labels.totalPendingApprovals\" [value]=\"totalPendingApprovals\"\r\n            [subtitle]=\"labels.all + ' ' + currentTab + ' ' + labels.awaitingApproval\"></app-approval-txt-card>\r\n        <!-- <ava-text-card [type]=\"'default'\" [iconName]=\"'hourglass'\" [title]=\"labels.totalApprovals\" [value]=\"totalApprovals\"\r\n            [description]=\"currentTab + ' ' + labels.whichAreRequestedForApproval\">\r\n        </ava-text-card>\r\n        <ava-text-card [type]=\"'default'\" [iconName]=\"'shield-alert'\" [title]=\"labels.totalApprovedApprovals\" [value]=\"totalApprovedApprovals\"\r\n            [description]=\"currentTab + ' ' + labels.whichAreApproved\">\r\n        </ava-text-card>\r\n        <ava-text-card [type]=\"'default'\" [iconName]=\"'hourglass'\" [title]=\"labels.totalPendingApprovals\" [value]=\"totalPendingApprovals\"\r\n            [description]=\"labels.all + ' ' + currentTab + ' ' + labels.awaitingApproval\">\r\n        </ava-text-card> -->\r\n    </div>\r\n    \r\n<div class=\"filter-section\">\r\n    <div class=\"search-bars\">\r\n        <div style=\"font-size: 1.5rem; font-weight: bold; color: black;\">\r\n            {{currentTab}} Approvals\r\n        </div>\r\n        <div class=\"approval-card-header\">\r\n            All - {{totalRecords}} {{currentTab}}\r\n        </div>  \r\n    </div>\r\n    <div class=\"textbox section\">\r\n        <div>\r\n            <form [formGroup]=\"searchForm\">\r\n                <ava-textbox [placeholder]=\"labels.searchPlaceholder\" formControlName=\"search\">\r\n                    <ava-icon slot=\"icon-start\" iconName=\"search\" [iconSize]=\"16\" iconColor=\"var(--color-brand-primary)\"></ava-icon>\r\n                </ava-textbox>\r\n            </form>\r\n        </div>\r\n    </div>\r\n</div>\r\n    \r\n    <div class=\"approval-card-section\">\r\n        @if(totalRecords > 0){      \r\n            @for (item of consoleApproval.contents; track $index){\r\n            <div class=\"approval-card-wrapper\" (click)=\"onCardClick($index)\">\r\n                <ava-approval-card height=\"300\">\r\n                    <ava-card-header>\r\n                        <ava-icon iconSize=\"20\" iconName=\"ellipsis-vertical\"></ava-icon>\r\n                        <div class=\"header\">\r\n                            <h2>{{item.session1.title}}</h2>\r\n                            <ava-tag label=\"{{currentTab}}\" color=\"info\" size=\"sm\"></ava-tag>\r\n                        </div>\r\n                    </ava-card-header>\r\n            \r\n                    <ava-card-content>\r\n                        <div class=\"a-content\">\r\n                            <div class=\"box tag-wrapper\">\r\n                                <ava-tag label=\"Individual\" size=\"sm\"></ava-tag>\r\n                                <ava-tag label=\"Ascendion\" size=\"sm\"></ava-tag>\r\n                                <ava-tag label=\"Digital Ascender\" size=\"sm\"></ava-tag>\r\n                                <ava-tag label=\"Platform Engineering\" size=\"sm\"></ava-tag>\r\n                            </div>\r\n                            <div class=\"box info-wrapper\">\r\n                                <div class=\"f\">\r\n                                    <ava-icon iconSize=\"13\" iconName=\"user\"></ava-icon>\r\n                                    <span>{{item.session3[0].label}}</span>\r\n                                </div>\r\n                                <div class=\"ml-auto s\">\r\n                                    <ava-icon iconSize=\"20\" iconName=\"calendar-days\"></ava-icon>\r\n                                    <span>{{item.session3[1].label}}</span>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </ava-card-content>\r\n            \r\n                    <ava-card-footer>\r\n                        <div class=\"footer-content\">\r\n                            <div class=\"footer-left\">\r\n                                <span class=\"ex\">Execution Status</span>\r\n                                <div>\r\n                                    <ava-icon iconSize=\"20\" iconName=\"circle-check-big\"></ava-icon>\r\n                                    <span>{{item?.session4.status}}</span>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"footer-right\">\r\n                                <ava-button label=\"Test\" (userClick)=\"handleTesting($index)\" variant=\"secondary\" size=\"medium\"\r\n                                    state=\"default\" iconName=\"play\" iconPosition=\"left\"></ava-button>\r\n                                <ava-button label=\"Sendback\" (userClick)=\"rejectApproval($index)\" variant=\"secondary\" size=\"medium\"\r\n                                    state=\"default\" iconName=\"move-left\" iconPosition=\"left\"></ava-button>\r\n                                <ava-button label=\"Approve\" (userClick)=\"approveApproval($index)\" variant=\"primary\" size=\"medium\"\r\n                                    state=\"default\" iconName=\"Check\" iconPosition=\"left\"></ava-button>\r\n                            </div>\r\n                        </div>\r\n                    </ava-card-footer>\r\n                </ava-approval-card>\r\n            </div>\r\n            }\r\n        }\r\n        @else{\r\n            <div class=\"no-pending-message\">\r\n                All {{currentTab}} have been successfully approved. No pending actions.\r\n            </div>\r\n        }\r\n    </div>\r\n</div>\r\n\r\n"], "mappings": "AAAA,SAASA,YAAY,EAAEC,UAAU,QAAQ,iBAAiB;AAE1D,SAAiCC,mBAAmB,QAAQ,gBAAgB;AAC5E,SAAiBC,YAAY,QAAQ,iBAAiB;AACtD,SACEC,qBAAqB,EACrBC,aAAa,EACbC,mBAAmB,EAInBC,eAAe,EAEfC,eAAe,QAGV,wBAAwB;AAC/B,OAAOC,YAAY,MAAM,4BAA4B;AAErD,SAASC,YAAY,EAAEC,oBAAoB,EAAEC,GAAG,EAAEC,SAAS,QAAQ,MAAM;AAGzE,SAASC,2BAA2B,QAAQ,uDAAuD;AACnG,SAASC,wBAAwB,QAAQ,oDAAoD;;;;;;;;;;;ICmBjFC,EAAA,CAAAC,cAAA,cAAiE;IAA9BD,EAAA,CAAAE,UAAA,mBAAAC,8EAAA;MAAA,MAAAC,SAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA,EAAAC,MAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAG,WAAA,CAAAP,SAAA,CAAmB;IAAA,EAAC;IAExDJ,EADJ,CAAAC,cAAA,4BAAgC,sBACX;IACbD,EAAA,CAAAY,SAAA,mBAAgE;IAE5DZ,EADJ,CAAAC,cAAA,cAAoB,SACZ;IAAAD,EAAA,CAAAa,MAAA,GAAuB;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAChCd,EAAA,CAAAY,SAAA,kBAAiE;IAEzEZ,EADI,CAAAc,YAAA,EAAM,EACQ;IAIVd,EAFR,CAAAC,cAAA,uBAAkB,cACS,eACU;IAIzBD,EAHA,CAAAY,SAAA,mBAAgD,mBACD,mBACO,mBACI;IAC9DZ,EAAA,CAAAc,YAAA,EAAM;IAEFd,EADJ,CAAAC,cAAA,eAA8B,eACX;IACXD,EAAA,CAAAY,SAAA,oBAAmD;IACnDZ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAa,MAAA,IAA0B;IACpCb,EADoC,CAAAc,YAAA,EAAO,EACrC;IACNd,EAAA,CAAAC,cAAA,eAAuB;IACnBD,EAAA,CAAAY,SAAA,oBAA4D;IAC5DZ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAa,MAAA,IAA0B;IAIhDb,EAJgD,CAAAc,YAAA,EAAO,EACrC,EACJ,EACJ,EACS;IAKPd,EAHZ,CAAAC,cAAA,uBAAiB,eACe,eACC,gBACJ;IAAAD,EAAA,CAAAa,MAAA,wBAAgB;IAAAb,EAAA,CAAAc,YAAA,EAAO;IACxCd,EAAA,CAAAC,cAAA,WAAK;IACDD,EAAA,CAAAY,SAAA,oBAA+D;IAC/DZ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAa,MAAA,IAAyB;IAEvCb,EAFuC,CAAAc,YAAA,EAAO,EACpC,EACJ;IAEFd,EADJ,CAAAC,cAAA,eAA0B,sBAEkC;IAD/BD,EAAA,CAAAE,UAAA,uBAAAa,0FAAA;MAAA,MAAAX,SAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA,EAAAC,MAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAaF,MAAA,CAAAQ,aAAA,CAAAZ,SAAA,CAAqB;IAAA,EAAC;IACJJ,EAAA,CAAAc,YAAA,EAAa;IACrEd,EAAA,CAAAC,cAAA,sBAC6D;IADhCD,EAAA,CAAAE,UAAA,uBAAAe,0FAAA;MAAA,MAAAb,SAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA,EAAAC,MAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAaF,MAAA,CAAAU,cAAA,CAAAd,SAAA,CAAsB;IAAA,EAAC;IACJJ,EAAA,CAAAc,YAAA,EAAa;IAC1Ed,EAAA,CAAAC,cAAA,sBACyD;IAD7BD,EAAA,CAAAE,UAAA,uBAAAiB,0FAAA;MAAA,MAAAf,SAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA,EAAAC,MAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAaF,MAAA,CAAAY,eAAA,CAAAhB,SAAA,CAAuB;IAAA,EAAC;IAMrFJ,EAL6E,CAAAc,YAAA,EAAa,EACpE,EACJ,EACQ,EACF,EAClB;;;;;IA9Ccd,EAAA,CAAAqB,SAAA,GAAuB;IAAvBrB,EAAA,CAAAsB,iBAAA,CAAAC,OAAA,CAAAC,QAAA,CAAAC,KAAA,CAAuB;IAClBzB,EAAA,CAAAqB,SAAA,EAAsB;IAAtBrB,EAAA,CAAA0B,qBAAA,UAAAlB,MAAA,CAAAmB,UAAA,CAAsB;IAejB3B,EAAA,CAAAqB,SAAA,IAA0B;IAA1BrB,EAAA,CAAAsB,iBAAA,CAAAC,OAAA,CAAAK,QAAA,IAAAC,KAAA,CAA0B;IAI1B7B,EAAA,CAAAqB,SAAA,GAA0B;IAA1BrB,EAAA,CAAAsB,iBAAA,CAAAC,OAAA,CAAAK,QAAA,IAAAC,KAAA,CAA0B;IAY1B7B,EAAA,CAAAqB,SAAA,GAAyB;IAAzBrB,EAAA,CAAAsB,iBAAA,CAAAC,OAAA,kBAAAA,OAAA,CAAAO,QAAA,CAAAC,MAAA,CAAyB;;;;;IAtCvD/B,EAAA,CAAAgC,gBAAA,IAAAC,wDAAA,oBAAAjC,EAAA,CAAAkC,sBAAA,CAqDC;;;;IArDDlC,EAAA,CAAAmC,UAAA,CAAA3B,MAAA,CAAA4B,eAAA,CAAAC,QAAA,CAqDC;;;;;IAGDrC,EAAA,CAAAC,cAAA,cAAgC;IAC5BD,EAAA,CAAAa,MAAA,GACJ;IAAAb,EAAA,CAAAc,YAAA,EAAM;;;;IADFd,EAAA,CAAAqB,SAAA,EACJ;IADIrB,EAAA,CAAAsC,kBAAA,UAAA9B,MAAA,CAAAmB,UAAA,2DACJ;;;ADrDZ,WAAaY,0BAA0B;EAAjC,MAAOA,0BAA0B;IA8B3BC,MAAA;IACAC,eAAA;IACAC,EAAA;IACAC,eAAA;IACAC,aAAA;IACAC,aAAA;IAlCVC,SAAS,GAAGrD,YAAY,CAACsD,MAAM;IACxBC,sBAAsB,GAAW,EAAE;IACnCC,qBAAqB,GAAW,EAAE;IAClCC,cAAc,GAAW,EAAE;IAC3BC,gBAAgB,GAAY,KAAK;IACjCC,oBAAoB,GAAY,IAAI;IACpChB,eAAe,GAAQ,EAAE;IACzBiB,OAAO,GAAqB,EAAE;IAC9BC,iBAAiB,GAAU,EAAE;IAC7BC,YAAY,GAAU,EAAE;IACxBC,WAAW,GAAU,EAAE;IACvBC,eAAe,GAAU,EAAE;IAC3BC,uBAAuB,GAAU,EAAE;IACnCC,aAAa,GAAU,EAAE;IACzBC,gBAAgB,GAAG,CAAC;IACpBC,iBAAiB,GAAG,CAAC;IACrBC,oBAAoB,GAAG,CAAC;IACxBC,QAAQ,GAAG,EAAE;IACbC,YAAY,GAAG,CAAC;IAChBC,SAAS,GAAG,KAAK;IACjBtC,UAAU,GAAG,WAAW;IACxBuC,aAAa,GAAG,CAAC;IACjBC,UAAU;IACVpB,MAAM,GAAQtD,YAAY,CAACsD,MAAM;IACjCqB,eAAe,GAAkB,IAAI;IACrCC,WAAW,GAAQ,IAAI;IACvBC,kBAAkB,GAAW,CAAC;IAErCC,YACU/B,MAAc,EACdC,eAAgC,EAChCC,EAAe,EACfC,eAAgC,EAChCC,aAA4B,EAC5BC,aAA4B;MAL5B,KAAAL,MAAM,GAANA,MAAM;MACN,KAAAC,eAAe,GAAfA,eAAe;MACf,KAAAC,EAAE,GAAFA,EAAE;MACF,KAAAC,eAAe,GAAfA,eAAe;MACf,KAAAC,aAAa,GAAbA,aAAa;MACb,KAAAC,aAAa,GAAbA,aAAa;MAErB,IAAI,CAACE,MAAM,GAAGtD,YAAY,CAACsD,MAAM;MACjC,IAAI,CAACM,OAAO,GAAG,CACb;QAAEmB,IAAI,EAAE,IAAI,CAACzB,MAAM,CAAC0B,WAAW;QAAEC,KAAK,EAAE;MAAa,CAAE,EACvD;QAAEF,IAAI,EAAE,IAAI,CAACzB,MAAM,CAAC4B,QAAQ;QAAED,KAAK,EAAE;MAAU,CAAE,EACjD;QAAEF,IAAI,EAAE,IAAI,CAACzB,MAAM,CAAC6B,KAAK;QAAEF,KAAK,EAAE;MAAO,CAAE,CAC5C;MACD,IAAI,CAACpB,iBAAiB,GAAG,CACvB;QACEuB,EAAE,EAAE,GAAG;QACPC,IAAI,EAAE,QAAQ;QACdC,IAAI,EAAE,IAAI,CAAChC,MAAM,CAACiC,MAAM;QACxBC,KAAK,EAAE,EAAE;QACTC,MAAM,EAAE;OACT,EACD;QAAEL,EAAE,EAAE,GAAG;QAAEC,IAAI,EAAE,cAAc;QAAEC,IAAI,EAAE,IAAI,CAAChC,MAAM,CAACoC,SAAS;QAAEF,KAAK,EAAE;MAAE,CAAE,EACzE;QAAEJ,EAAE,EAAE,GAAG;QAAEC,IAAI,EAAE,KAAK;QAAEC,IAAI,EAAE,IAAI,CAAChC,MAAM,CAACqC,KAAK;QAAEH,KAAK,EAAE;MAAE,CAAE,CAC7D;MACD,IAAI,CAAC1B,YAAY,GAAG,CAClB;QACEuB,IAAI,EAAE,YAAY;QAClBjD,KAAK,EAAE,IAAI,CAACkB,MAAM,CAACiC,MAAM;QACzBC,KAAK,EAAE;OACR,EACD;QACEH,IAAI,EAAE,eAAe;QACrBjD,KAAK,EAAE,IAAI,CAACkB,MAAM,CAACoC,SAAS;QAC5BF,KAAK,EAAE;OACR,EACD;QACEH,IAAI,EAAE,WAAW;QACjBjD,KAAK,EAAE,IAAI,CAACkB,MAAM,CAACqC,KAAK;QACxBH,KAAK,EAAE;OACR,CACF;MACD,IAAI,CAACd,UAAU,GAAG,IAAI,CAACzB,EAAE,CAAC2C,KAAK,CAAC;QAC9BC,MAAM,EAAE,CAAC,EAAE;OACZ,CAAC;IACJ;IAEAC,QAAQA,CAAA;MACN,IAAI,CAACC,UAAU,EAAE;MACjB,IAAI,CAACtC,cAAc,GAAG,EAAE;MACxB,IAAI,CAACuC,mBAAmB,EAAE;IAC5B;IAEOD,UAAUA,CAAA;MACfE,OAAO,CAACC,GAAG,CAAC,IAAI,CAACxB,UAAU,CAACyB,GAAG,CAAC,QAAQ,CAAC,EAAElB,KAAK,CAAC;MACjD,IAAI,CAACP,UAAU,CACZyB,GAAG,CAAC,QAAQ,CAAE,CACdC,YAAY,CAACC,IAAI,CAChBjG,SAAS,CAAC,EAAE,CAAC,EACbH,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBC,GAAG,CAAE8E,KAAK,IAAKA,KAAK,EAAEqB,WAAW,EAAE,IAAI,EAAE,CAAC,CAC3C,CACAC,SAAS,CAAEC,UAAU,IAAI;QACxB,IAAI,CAACC,WAAW,CAACD,UAAU,CAAC;MAC9B,CAAC,CAAC;IACN;IAEOC,WAAWA,CAACnB,IAAY;MAC7B,MAAMoB,KAAK,GAAGpB,IAAI;MAClB,IAAI,CAACA,IAAI,EAAE;QACT,IAAI,CAACqB,qBAAqB,CAAC,IAAI,CAAC3C,eAAe,EAAE,UAAU,CAAC;QAC5D;MACF;MAEA,IAAI,CAACC,uBAAuB,GAAG,IAAI,CAACD,eAAe,CAAC4C,MAAM,CAAEC,IAAI,IAC9DA,IAAI,CAACC,YAAY,EAAER,WAAW,EAAE,CAACS,QAAQ,CAACL,KAAK,CAAC,CACjD;MACD,IAAI,CAACC,qBAAqB,CAAC,IAAI,CAAC1C,uBAAuB,EAAE,UAAU,CAAC;IACtE;IAEO+C,iBAAiBA,CAACC,IAAS;MAChChB,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEe,IAAI,CAAC;IACzC;IAEOC,MAAMA,CAACC,CAAM;MAClBlB,OAAO,CAACC,GAAG,CAAC,KAAK,GAAGiB,CAAC,CAAC;IACxB;IAEOC,kBAAkBA,CAAA;MACvB,IAAI,CAACzD,oBAAoB,GAAG,CAAC,IAAI,CAACA,oBAAoB;IACxD;IAEO0D,qBAAqBA,CAACC,WAAoB;MAC/C,IAAI,CAAC5D,gBAAgB,GAAG4D,WAAW;MACnCrB,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEoB,WAAW,CAAC;IACtD;IAEOC,gBAAgBA,CAACV,IAAS;MAC/B,IAAI,CAAChD,iBAAiB,CAAC2D,OAAO,CAAEL,CAAC,IAAMA,CAAC,CAAC1B,MAAM,GAAG,KAAM,CAAC;MACzDoB,IAAI,CAACpB,MAAM,GAAG,IAAI;MAClBQ,OAAO,CAACC,GAAG,CAACW,IAAI,CAAC;IACnB;IAEOY,eAAeA,CAACxC,KAAgC;MACrD,OAAOA,KAAK,KAAK,UAAU,IAAIA,KAAK,KAAK,UAAU,IAAIA,KAAK,KAAK,QAAQ,GACrEA,KAAK,GACL,QAAQ;IACd;IAEOyC,sBAAsBA,CAAA;MAC3B,IAAI,CAACC,kBAAkB,EAAE;IAC3B;IAEOC,qBAAqBA,CAAA;MAC1B,IAAI,CAACC,kBAAkB,EAAE;IAC3B;IAEO7B,mBAAmBA,CAAA;MACxB,IAAI,CAAChD,eAAe,CACjB8E,qBAAqB,CACpB,IAAI,CAACzD,oBAAoB,EACzB,IAAI,CAACC,QAAQ,EACb,IAAI,CAACE,SAAS,CACf,CACA+B,SAAS,CAAEwB,QAAQ,IAAI;QACtB,IAAI,IAAI,CAAC1D,oBAAoB,GAAG,CAAC,EAAE;UACjC,IAAI,CAACL,eAAe,GAAG,CACrB,GAAG,IAAI,CAACA,eAAe,EACvB,GAAG+D,QAAQ,CAACC,qBAAqB,CAClC;QACH,CAAC,MAAM;UACL,IAAI,CAAChE,eAAe,GAAG+D,QAAQ,EAAEC,qBAAqB;QACxD;QACA,IAAI,CAAChE,eAAe,GAAG,IAAI,CAACA,eAAe,CAAC4C,MAAM,CAC/CqB,CAAC,IAAKA,CAAC,CAAC3F,MAAM,KAAK,UAAU,CAC/B;QACD,IAAI,CAAC2B,uBAAuB,GAAG,IAAI,CAACD,eAAe;QACnD,IAAI,CAACO,YAAY,GAAG,IAAI,CAACP,eAAe,CAACkE,MAAM;QAC/C;QACA,IAAI,CAACvB,qBAAqB,CAAC,IAAI,CAAC3C,eAAe,EAAE,UAAU,CAAC;MAC9D,CAAC,CAAC;IACN;IAEOmE,iBAAiBA,CAACC,IAAY;MACnC,IAAI,CAAC/D,oBAAoB,GAAG+D,IAAI;MAChC,IAAI,CAACpC,mBAAmB,EAAE;IAC5B;IAEOqC,WAAWA,CAACtD,IAAY;MAC7B,IAAI,CAAC7C,UAAU,GAAG6C,IAAI;MACtB,IAAI,CAACiB,mBAAmB,EAAE;IAC5B;IAEOvE,cAAcA,CAAC6G,GAAQ;MAC5BrC,OAAO,CAACC,GAAG,CAACoC,GAAG,CAAC;MAChB,IAAI,CAAC7D,aAAa,GAAG6D,GAAG;IAC1B;IAEO3G,eAAeA,CAAC2G,GAAQ;MAC7BrC,OAAO,CAACC,GAAG,CAACoC,GAAG,CAAC;MAChB,IAAI,CAAC7D,aAAa,GAAG6D,GAAG;MACxBrC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACjC,uBAAuB,CAAC,IAAI,CAACQ,aAAa,CAAC,CAAC;IAC/D;IAEQkD,kBAAkBA,CAAA;MACxB,IAAI,CAACvE,aAAa,CAACmF,YAAY,CAAC;QAC9BvG,KAAK,EAAE,IAAI,CAACsB,MAAM,CAACkF,eAAe;QAClCC,OAAO,EAAE,GAAG,IAAI,CAACnF,MAAM,CAACoF,wBAAwB,cAAc,IAAI,CAACpF,MAAM,CAACqF,4BAA4B,IAAI,IAAI,CAACzG,UAAU,IAAI,IAAI,CAACoB,MAAM,CAACsF,0BAA0B,EAAE;QACrKC,iBAAiB,EAAE,IAAI,CAACvF,MAAM,CAACwF,OAAO;QACtCC,gBAAgB,EAAE,QAAQ;QAC1BC,oBAAoB,EAAE,QAAQ;QAC9B3D,IAAI,EAAC;OACN,CAAC,CAAC4D,IAAI,CAACC,MAAM,IAAG;QACf,IAAIA,MAAM,CAACC,SAAS,EAAE;UACpB,IAAI,CAACC,cAAc,EAAE;QACvB;MACF,CAAC,CAAC;IACJ;IAEQvB,kBAAkBA,CAAA;MACvB,MAAMwB,aAAa,GAAmB,CACjC;QAAEjH,KAAK,EAAE,QAAQ;QAAEkH,OAAO,EAAE,WAAW;QAAEC,MAAM,EAAE;MAAQ,CAAE,EAC3D;QAAEnH,KAAK,EAAE,WAAW;QAAEkH,OAAO,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAU,CAAE,CAC/D;MACL,IAAI,CAACnG,aAAa,CAACoG,QAAQ,CAAC;QAC1BxH,KAAK,EAAE,mBAAmB;QAC1ByG,OAAO,EAAE,yGAAyG;QAClHgB,OAAO,EAACJ;OACT,CAAC,CAACJ,IAAI,CAACC,MAAM,IAAG;QACf,IAAIA,MAAM,CAACC,SAAS,IAAID,MAAM,CAACC,SAAS,KAAK,IAAI,EAAE;UACjD,IAAI,CAACO,eAAe,CAACR,MAAM,CAACjC,IAAI,CAAC;QACnC;MACF,CAAC,CAAC;IACJ;IAEOmC,cAAcA,CAAA;MACnB,IAAI,CAACO,sBAAsB,EAAE;IAC/B;IAEOD,eAAeA,CAACF,QAAa;MAClC,IAAI,CAACI,uBAAuB,CAACJ,QAAQ,CAAC;IACxC;IAEOG,sBAAsBA,CAAA;MAC3B,MAAME,eAAe,GAAG,IAAI,CAAC5F,uBAAuB,CAAC,IAAI,CAACQ,aAAa,CAAC;MACxE,MAAMW,EAAE,GAAGyE,eAAe,EAAEzE,EAAE;MAC9B,MAAM0E,UAAU,GAAGD,eAAe,EAAEC,UAAU;MAC9C,MAAMxH,MAAM,GAAG,UAAU;MACzB,MAAMyH,UAAU,GAAGF,eAAe,EAAEE,UAAU;MAC9C9D,OAAO,CAACC,GAAG,CAACd,EAAE,EAAE0E,UAAU,EAAExH,MAAM,EAAEyH,UAAU,CAAC;MAE/C;MACA,IAAI,CAAC3G,aAAa,CAAC4G,OAAO,CAAC;QACzBhI,KAAK,EAAE,uBAAuB;QAC9ByG,OAAO,EAAE,4CAA4C;QACrDwB,YAAY,EAAE,KAAK;QACnBC,gBAAgB,EAAE;OACnB,CAAC;MAEF,IAAI,CAAClH,eAAe,CACjBmH,eAAe,CAAC/E,EAAE,EAAE0E,UAAU,EAAExH,MAAM,EAAEyH,UAAU,CAAC,CACnDxD,SAAS,CAAC;QACT6D,IAAI,EAAGrC,QAAa,IAAI;UACtB,IAAI,CAAC3E,aAAa,CAACiH,KAAK,EAAE,CAAC,CAAC;UAE5B,MAAM5B,OAAO,GAAGV,QAAQ,EAAEU,OAAO,IAAI,IAAI,CAACnF,MAAM,CAACgH,6BAA6B;UAC9E,IAAI,CAAClH,aAAa,CAACmH,OAAO,CAAC;YACzBvI,KAAK,EAAE,mBAAmB;YAC1ByG,OAAO,EAAEA;WACV,CAAC,CAACQ,IAAI,CAACC,MAAM,IAAG;YACf,IAAIA,MAAM,CAACK,MAAM,KAAK,WAAW,EAAE;cACjC;cACA,IAAI,CAACxG,MAAM,CAACyH,QAAQ,CAAC,CAAC,uBAAuB,EAAEV,UAAU,CAAC,CAAC;YAC7D,CAAC,MAAM;cACL,IAAI,CAAC9D,mBAAmB,EAAE,CAAC,CAAC;YAC9B;UACF,CAAC,CAAC;QACJ,CAAC;QACDyE,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACrH,aAAa,CAACiH,KAAK,EAAE,CAAC,CAAC;UAE5B,MAAMK,YAAY,GAAGD,KAAK,EAAEA,KAAK,EAAEhC,OAAO,IAAI,IAAI,CAACnF,MAAM,CAACqH,mBAAmB;UAC7E,IAAI,CAACvH,aAAa,CAACqH,KAAK,CAAC;YACvBzI,KAAK,EAAE,iBAAiB;YACxByG,OAAO,EAAEiC,YAAY;YACrBE,eAAe,EAAE,IAAI;YACrBC,eAAe,EAAE;WAClB,CAAC,CAAC5B,IAAI,CAACC,MAAM,IAAG;YACf,IAAIA,MAAM,CAACK,MAAM,KAAK,OAAO,EAAE;cAC7B,IAAI,CAACI,sBAAsB,EAAE;YAC/B;UACF,CAAC,CAAC;QACJ;OACD,CAAC;IACN;IAEOC,uBAAuBA,CAACJ,QAAa;MAC1C,MAAMK,eAAe,GAAG,IAAI,CAAC7F,eAAe,CAAC,IAAI,CAACS,aAAa,CAAC;MAChE,MAAMW,EAAE,GAAGyE,eAAe,EAAEzE,EAAE;MAC9B,MAAM0E,UAAU,GAAGD,eAAe,EAAEC,UAAU;MAC9C,MAAMxH,MAAM,GAAG,UAAU;MACzB,MAAMyH,UAAU,GAAGF,eAAe,EAAEE,UAAU;MAC9C,MAAMtB,OAAO,GAAGe,QAAQ;MACxBvD,OAAO,CAACC,GAAG,CAACd,EAAE,EAAE0E,UAAU,EAAExH,MAAM,EAAEyH,UAAU,EAAEtB,OAAO,CAAC;MAExD;MACA,IAAI,CAACrF,aAAa,CAAC4G,OAAO,CAAC;QACzBhI,KAAK,EAAE,uBAAuB;QAC9ByG,OAAO,EAAE,2CAA2C;QACpDwB,YAAY,EAAE,KAAK;QACnBC,gBAAgB,EAAE;OACnB,CAAC;MAEF,IAAI,CAAClH,eAAe,CACjB8H,cAAc,CAAC1F,EAAE,EAAE0E,UAAU,EAAExH,MAAM,EAAEyH,UAAU,EAAEtB,OAAO,CAAC,CAC3DlC,SAAS,CAAC;QACT6D,IAAI,EAAGrC,QAAa,IAAI;UACtB,IAAI,CAAC3E,aAAa,CAACiH,KAAK,EAAE,CAAC,CAAC;UAE5B,MAAM5B,OAAO,GAAGV,QAAQ,EAAEU,OAAO,IAAI,IAAI,CAACnF,MAAM,CAACyH,4BAA4B;UAC7E,IAAI,CAAC3H,aAAa,CAACmH,OAAO,CAAC;YACzBvI,KAAK,EAAE,mBAAmB;YAC1ByG,OAAO,EAAEA;WACV,CAAC,CAACQ,IAAI,CAAC,MAAK;YACX,IAAI,CAACjD,mBAAmB,EAAE,CAAC,CAAC;UAC9B,CAAC,CAAC;QACJ,CAAC;QACDyE,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACrH,aAAa,CAACiH,KAAK,EAAE,CAAC,CAAC;UAE5B,MAAMK,YAAY,GAAGD,KAAK,EAAEA,KAAK,EAAEhC,OAAO,IAAI,IAAI,CAACnF,MAAM,CAACqH,mBAAmB;UAC7E,IAAI,CAACvH,aAAa,CAACqH,KAAK,CAAC;YACvBzI,KAAK,EAAE,kBAAkB;YACzByG,OAAO,EAAEiC,YAAY;YACrBE,eAAe,EAAE,IAAI;YACrBC,eAAe,EAAE;WAClB,CAAC,CAAC5B,IAAI,CAACC,MAAM,IAAG;YACf,IAAIA,MAAM,CAACK,MAAM,KAAK,OAAO,EAAE;cAC7B,IAAI,CAACK,uBAAuB,CAACJ,QAAQ,CAAC;YACxC;UACF,CAAC,CAAC;QACJ;OACD,CAAC;IACN;IAEOjI,aAAaA,CAACyJ,KAAW;MAC9B/E,OAAO,CAACC,GAAG,CAAC8E,KAAK,CAAC;MAClB,MAAMlB,UAAU,GAAG,IAAI,CAAC7F,uBAAuB,CAAC+G,KAAK,CAAC,CAAClB,UAAU;MACjE,IAAI,CAACjF,kBAAkB,GAAGiF,UAAU;IACtC;IAEO5I,WAAWA,CAAC8J,KAAa;MAC9B/E,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE8E,KAAK,CAAC;MAC1C,IAAI,CAACvG,aAAa,GAAGuG,KAAK;MAC1B,MAAMC,gBAAgB,GAAG,IAAI,CAAChH,uBAAuB,CAAC,IAAI,CAACQ,aAAa,CAAC;MACzE,IAAI,CAACI,kBAAkB,GAAGoG,gBAAgB,CAACnB,UAAU;MACrD,IAAI,CAACoB,eAAe,CAACD,gBAAgB,CAAC;MACtChF,OAAO,CAACC,GAAG,CAAC+E,gBAAgB,CAAC;MAC7B,IAAI,CAAC9H,aAAa,CAACgI,IAAI,CAAC9K,2BAA2B,EAAE;QACnDuE,WAAW,EAAE,IAAI,CAACA,WAAW;QAC7BwG,YAAY,EAAEA,CAAA,KAAM,IAAI,CAACjI,aAAa,CAACkI,KAAK,EAAE;QAC9CC,YAAY,EAAEA,CAAA,KAAM,IAAI,CAACC,kBAAkB,CAACN,gBAAgB,CAACnB,UAAU,CAAC;QACxErI,cAAc,EAAEA,CAAA,KAAM,IAAI,CAACmG,qBAAqB,EAAE;QAClDjG,eAAe,EAAEA,CAAA,KAAM,IAAI,CAAC+F,sBAAsB,EAAE;QACpD8D,YAAY,EAAEA,CAAA,KAAM,IAAI,CAACC,4BAA4B;OACtD,CAAC;IACJ;IAEOP,eAAeA,CAACD,gBAAqB;MAC1C,IAAI,CAACrG,WAAW,GAAG;QACjB8G,IAAI,EAAE,UAAU;QAChB1J,KAAK,EAAEiJ,gBAAgB,CAACnE,YAAY;QACpCG,IAAI,EAAEgE,gBAAgB;QACtBjB,OAAO,EAAE,IAAI;QACbS,KAAK,EAAE;OACR;MACD,IAAI,CAACvH,eAAe,CAACyI,eAAe,CAACV,gBAAgB,CAACnB,UAAU,CAAC,CAACvD,SAAS,CAAC;QAC1E6D,IAAI,EAAGrC,QAAQ,IAAI;UACjB9B,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE6B,QAAQ,CAAC;UACzC,IAAI,CAACnD,WAAW,CAACqC,IAAI,GAAGc,QAAQ;UAChC,IAAI,CAACnD,WAAW,CAACoF,OAAO,GAAG,KAAK;UAChC,IAAI,CAACpF,WAAW,CAAC6F,KAAK,GAAG,IAAI;QAC/B,CAAC;QACDA,KAAK,EAAGA,KAAK,IAAI;UACfxE,OAAO,CAACwE,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;QAChC;OACD,CAAC;IACJ;IAEOgB,4BAA4BA,CAAA;MACjC,IAAI,CAACtI,aAAa,CAACkI,KAAK,EAAE;MAC1B,IAAI,CAACtI,MAAM,CAACyH,QAAQ,CAAC,CAAC,0BAA0B,EAAE,IAAI,CAAC3F,kBAAkB,CAAC,CAAC;IAC7E;IAEO0G,kBAAkBA,CAACzB,UAAkB;MAC1C7D,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE4D,UAAU,CAAC;MACxC,IAAI,CAAC3G,aAAa,CAACkI,KAAK,EAAE;MAC1B,IAAI,CAACtI,MAAM,CAACyH,QAAQ,CAAC,CAAC,uBAAuB,EAAEV,UAAU,CAAC,CAAC;IAC7D;IAEOnD,qBAAqBA,CAACM,IAAW,EAAEyE,IAAY;MACpD,IAAI,CAAC/I,eAAe,GAAG;QACrBC,QAAQ,EAAEqE,IAAI,EAAE9G,GAAG,CAAEyL,GAAQ,IAAI;UAC/B,MAAMC,WAAW,GAAkC;YACjDC,QAAQ,EAAE,kBAAkB;YAC5BC,QAAQ,EAAE,UAAU;YACpBC,MAAM,EAAE;WACT;UACD,MAAMC,WAAW,GAAkC;YACjDH,QAAQ,EAAE,IAAI,CAACxI,MAAM,CAACwI,QAAQ;YAC9BC,QAAQ,EAAE,IAAI,CAACzI,MAAM,CAACyI,QAAQ;YAC9BC,MAAM,EAAE,IAAI,CAAC1I,MAAM,CAAC0I;WACrB;UACD,MAAME,SAAS,GAAG,IAAI,CAACzE,eAAe,CAACmE,GAAG,EAAEtJ,MAAM,CAAC;UACnD,MAAM6J,UAAU,GAAGP,GAAG,CAAC9B,UAAU;UACjC,MAAM9H,KAAK,GAAG4J,GAAG,CAAC9E,YAAY;UAE9B,OAAO;YACL1B,EAAE,EAAEwG,GAAG,CAACxG,EAAE;YACVgH,KAAK,EAAED,UAAU;YACjBT,IAAI,EAAEA,IAAI;YACV3J,QAAQ,EAAE;cACRC,KAAK,EAAEA,KAAK;cACZsB,MAAM,EAAE,CACN;gBACEyB,IAAI,EAAE2G,IAAI;gBACVW,KAAK,EAAE,SAAS;gBAChBC,UAAU,EAAE,KAAK;gBACjBZ,IAAI,EAAE;eACP,EACD;gBACE3G,IAAI,EAAE6G,GAAG,CAACW,iBAAiB;gBAC3BF,KAAK,EAAET,GAAG,CAACW,iBAAiB,KAAK,QAAQ,GAAG,OAAO,GAAG,MAAM;gBAC5DD,UAAU,EAAE,KAAK;gBACjBZ,IAAI,EAAE;eACP;aAEJ;YACDc,QAAQ,EAAE,CACR;cACEzH,IAAI,EAAE2G,IAAI;cACVW,KAAK,EAAE,SAAS;cAChBC,UAAU,EAAE,KAAK;cACjBZ,IAAI,EAAE;aACP,EACD;cACE3G,IAAI,EAAE6G,GAAG,CAACtJ,MAAM;cAChB+J,KAAK,EAAE,SAAS;cAChBC,UAAU,EAAE,KAAK;cACjBZ,IAAI,EAAE;aACP,CACF;YACDvJ,QAAQ,EAAE,CACR;cACEsK,QAAQ,EAAE,MAAM;cAChBrK,KAAK,EAAEwJ,GAAG,CAACc;aACZ,EACD;cACED,QAAQ,EAAE,eAAe;cACzBrK,KAAK,EAAE5C,UAAU,CAACoM,GAAG,EAAEe,WAAW,EAAE,aAAa,EAAE,OAAO;aAC3D,CACF;YACDtK,QAAQ,EAAE;cACRC,MAAM,EAAE2J,WAAW,CAACC,SAAS,CAAC;cAC9BO,QAAQ,EAAEZ,WAAW,CAACK,SAAS;;WAElC;QACH,CAAC,CAAC;QACFU,MAAM,EAAE;OACT;IACH;;uCA5cW9J,0BAA0B,EAAAvC,EAAA,CAAAsM,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAxM,EAAA,CAAAsM,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAA1M,EAAA,CAAAsM,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAA5M,EAAA,CAAAsM,iBAAA,CAAAO,EAAA,CAAAC,eAAA,GAAA9M,EAAA,CAAAsM,iBAAA,CAAAS,EAAA,CAAAC,aAAA,GAAAhN,EAAA,CAAAsM,iBAAA,CAAAW,EAAA,CAAAC,aAAA;IAAA;;YAA1B3K,0BAA0B;MAAA4K,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC7CnCzN,EADJ,CAAAC,cAAA,aAAmC,aACI;UAK/BD,EAJA,CAAAY,SAAA,+BACgG,+BAEqB,+BAEd;UAU3GZ,EAAA,CAAAc,YAAA,EAAM;UAIFd,EAFR,CAAAC,cAAA,aAA4B,aACC,aAC4C;UAC7DD,EAAA,CAAAa,MAAA,GACJ;UAAAb,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAC,cAAA,aAAkC;UAC9BD,EAAA,CAAAa,MAAA,IACJ;UACJb,EADI,CAAAc,YAAA,EAAM,EACJ;UAIMd,EAHZ,CAAAC,cAAA,cAA6B,WACpB,eAC8B,sBACoD;UAC3ED,EAAA,CAAAY,SAAA,oBAAgH;UAKpIZ,EAJgB,CAAAc,YAAA,EAAc,EACX,EACL,EACJ,EACJ;UAEFd,EAAA,CAAAC,cAAA,eAAmC;UAyD/BD,EAxDA,CAAA2N,UAAA,KAAAC,kDAAA,OAAsB,KAAAC,kDAAA,kBAwDhB;UAMd7N,EADI,CAAAc,YAAA,EAAM,EACJ;;;UApGyBd,EAAA,CAAAqB,SAAA,GAAwB;UAC3CrB,EADmB,CAAA8N,UAAA,yBAAwB,UAAAJ,GAAA,CAAA3K,MAAA,CAAAG,cAAA,CAAgC,UAAAwK,GAAA,CAAAxK,cAAA,CAAyB,aAAAwK,GAAA,CAAA/L,UAAA,SAAA+L,GAAA,CAAA3K,MAAA,CAAAgL,4BAAA,CACjC;UAChD/N,EAAA,CAAAqB,SAAA,EAA2B;UACbrB,EADd,CAAA8N,UAAA,4BAA2B,UAAAJ,GAAA,CAAA3K,MAAA,CAAAC,sBAAA,CAAwC,UAAA0K,GAAA,CAAA1K,sBAAA,CACtD,aAAA0K,GAAA,CAAA/L,UAAA,SAAA+L,GAAA,CAAA3K,MAAA,CAAAiL,gBAAA,CAAwD;UACrEhO,EAAA,CAAAqB,SAAA,EAAwB;UAC3CrB,EADmB,CAAA8N,UAAA,yBAAwB,UAAAJ,GAAA,CAAA3K,MAAA,CAAAE,qBAAA,CAAuC,UAAAyK,GAAA,CAAAzK,qBAAA,CAAgC,aAAAyK,GAAA,CAAA3K,MAAA,CAAAkL,GAAA,SAAAP,GAAA,CAAA/L,UAAA,SAAA+L,GAAA,CAAA3K,MAAA,CAAAmL,gBAAA,CACxC;UAe1ElO,EAAA,CAAAqB,SAAA,GACJ;UADIrB,EAAA,CAAAsC,kBAAA,MAAAoL,GAAA,CAAA/L,UAAA,gBACJ;UAEI3B,EAAA,CAAAqB,SAAA,GACJ;UADIrB,EAAA,CAAAmO,kBAAA,YAAAT,GAAA,CAAA1J,YAAA,OAAA0J,GAAA,CAAA/L,UAAA,MACJ;UAIU3B,EAAA,CAAAqB,SAAA,GAAwB;UAAxBrB,EAAA,CAAA8N,UAAA,cAAAJ,GAAA,CAAAvJ,UAAA,CAAwB;UACbnE,EAAA,CAAAqB,SAAA,EAAwC;UAAxCrB,EAAA,CAAA8N,UAAA,gBAAAJ,GAAA,CAAA3K,MAAA,CAAAqL,iBAAA,CAAwC;UACHpO,EAAA,CAAAqB,SAAA,EAAe;UAAfrB,EAAA,CAAA8N,UAAA,gBAAe;UAQzE9N,EAAA,CAAAqB,SAAA,GA4DC;UA5DDrB,EAAA,CAAAqO,aAAA,CAAAX,GAAA,CAAA1J,YAAA,eA4DC;;;qBDtELhF,YAAY,EACZG,YAAY,EACZC,qBAAqB,EACrBC,aAAa,EACbC,mBAAmB,EACnBJ,mBAAmB,EAAAyN,EAAA,CAAA2B,aAAA,EAAA3B,EAAA,CAAA4B,eAAA,EAAA5B,EAAA,CAAA6B,oBAAA,EAAA7B,EAAA,CAAA8B,kBAAA,EAAA9B,EAAA,CAAA+B,eAAA,EACnBnP,eAAe,EACfC,eAAe,EAGfO,wBAAwB;MAAA4O,MAAA;IAAA;;SAMfpM,0BAA0B;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}