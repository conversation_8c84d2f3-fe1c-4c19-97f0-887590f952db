{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport './media/severityIcon.css';\nimport { Codicon } from '../../../base/common/codicons.js';\nimport { ThemeIcon } from '../../../base/common/themables.js';\nimport Severity from '../../../base/common/severity.js';\nexport var SeverityIcon;\n(function (SeverityIcon) {\n  function className(severity) {\n    switch (severity) {\n      case Severity.Ignore:\n        return 'severity-ignore ' + ThemeIcon.asClassName(Codicon.info);\n      case Severity.Info:\n        return ThemeIcon.asClassName(Codicon.info);\n      case Severity.Warning:\n        return ThemeIcon.asClassName(Codicon.warning);\n      case Severity.Error:\n        return ThemeIcon.asClassName(Codicon.error);\n      default:\n        return '';\n    }\n  }\n  SeverityIcon.className = className;\n})(SeverityIcon || (SeverityIcon = {}));", "map": {"version": 3, "names": ["Codicon", "ThemeIcon", "Severity", "SeverityIcon", "className", "severity", "Ignore", "asClassName", "info", "Info", "Warning", "warning", "Error", "error"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/platform/severityIcon/browser/severityIcon.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport './media/severityIcon.css';\nimport { Codicon } from '../../../base/common/codicons.js';\nimport { ThemeIcon } from '../../../base/common/themables.js';\nimport Severity from '../../../base/common/severity.js';\nexport var SeverityIcon;\n(function (SeverityIcon) {\n    function className(severity) {\n        switch (severity) {\n            case Severity.Ignore:\n                return 'severity-ignore ' + ThemeIcon.asClassName(Codicon.info);\n            case Severity.Info:\n                return ThemeIcon.asClassName(Codicon.info);\n            case Severity.Warning:\n                return ThemeIcon.asClassName(Codicon.warning);\n            case Severity.Error:\n                return ThemeIcon.asClassName(Codicon.error);\n            default:\n                return '';\n        }\n    }\n    SeverityIcon.className = className;\n})(SeverityIcon || (SeverityIcon = {}));\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,0BAA0B;AACjC,SAASA,OAAO,QAAQ,kCAAkC;AAC1D,SAASC,SAAS,QAAQ,mCAAmC;AAC7D,OAAOC,QAAQ,MAAM,kCAAkC;AACvD,OAAO,IAAIC,YAAY;AACvB,CAAC,UAAUA,YAAY,EAAE;EACrB,SAASC,SAASA,CAACC,QAAQ,EAAE;IACzB,QAAQA,QAAQ;MACZ,KAAKH,QAAQ,CAACI,MAAM;QAChB,OAAO,kBAAkB,GAAGL,SAAS,CAACM,WAAW,CAACP,OAAO,CAACQ,IAAI,CAAC;MACnE,KAAKN,QAAQ,CAACO,IAAI;QACd,OAAOR,SAAS,CAACM,WAAW,CAACP,OAAO,CAACQ,IAAI,CAAC;MAC9C,KAAKN,QAAQ,CAACQ,OAAO;QACjB,OAAOT,SAAS,CAACM,WAAW,CAACP,OAAO,CAACW,OAAO,CAAC;MACjD,KAAKT,QAAQ,CAACU,KAAK;QACf,OAAOX,SAAS,CAACM,WAAW,CAACP,OAAO,CAACa,KAAK,CAAC;MAC/C;QACI,OAAO,EAAE;IACjB;EACJ;EACAV,YAAY,CAACC,SAAS,GAAGA,SAAS;AACtC,CAAC,EAAED,YAAY,KAAKA,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}