{"ast": null, "code": "import _asyncToGenerator from \"C:/console/aava-ui-web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { isThenable } from './async.js';\nimport { isEqualOrParent } from './extpath.js';\nimport { LRUCache } from './map.js';\nimport { basename, extname, posix, sep } from './path.js';\nimport { isLinux } from './platform.js';\nimport { escapeRegExpCharacters, ltrim } from './strings.js';\nexport const GLOBSTAR = '**';\nexport const GLOB_SPLIT = '/';\nconst PATH_REGEX = '[/\\\\\\\\]'; // any slash or backslash\nconst NO_PATH_REGEX = '[^/\\\\\\\\]'; // any non-slash and non-backslash\nconst ALL_FORWARD_SLASHES = /\\//g;\nfunction starsToRegExp(starCount, isLastPattern) {\n  switch (starCount) {\n    case 0:\n      return '';\n    case 1:\n      return `${NO_PATH_REGEX}*?`;\n    // 1 star matches any number of characters except path separator (/ and \\) - non greedy (?)\n    default:\n      // Matches:  (Path Sep OR Path Val followed by Path Sep) 0-many times except when it's the last pattern\n      //           in which case also matches (Path Sep followed by Path Val)\n      // Group is non capturing because we don't need to capture at all (?:...)\n      // Overall we use non-greedy matching because it could be that we match too much\n      return `(?:${PATH_REGEX}|${NO_PATH_REGEX}+${PATH_REGEX}${isLastPattern ? `|${PATH_REGEX}${NO_PATH_REGEX}+` : ''})*?`;\n  }\n}\nexport function splitGlobAware(pattern, splitChar) {\n  if (!pattern) {\n    return [];\n  }\n  const segments = [];\n  let inBraces = false;\n  let inBrackets = false;\n  let curVal = '';\n  for (const char of pattern) {\n    switch (char) {\n      case splitChar:\n        if (!inBraces && !inBrackets) {\n          segments.push(curVal);\n          curVal = '';\n          continue;\n        }\n        break;\n      case '{':\n        inBraces = true;\n        break;\n      case '}':\n        inBraces = false;\n        break;\n      case '[':\n        inBrackets = true;\n        break;\n      case ']':\n        inBrackets = false;\n        break;\n    }\n    curVal += char;\n  }\n  // Tail\n  if (curVal) {\n    segments.push(curVal);\n  }\n  return segments;\n}\nfunction parseRegExp(pattern) {\n  if (!pattern) {\n    return '';\n  }\n  let regEx = '';\n  // Split up into segments for each slash found\n  const segments = splitGlobAware(pattern, GLOB_SPLIT);\n  // Special case where we only have globstars\n  if (segments.every(segment => segment === GLOBSTAR)) {\n    regEx = '.*';\n  }\n  // Build regex over segments\n  else {\n    let previousSegmentWasGlobStar = false;\n    segments.forEach((segment, index) => {\n      // Treat globstar specially\n      if (segment === GLOBSTAR) {\n        // if we have more than one globstar after another, just ignore it\n        if (previousSegmentWasGlobStar) {\n          return;\n        }\n        regEx += starsToRegExp(2, index === segments.length - 1);\n      }\n      // Anything else, not globstar\n      else {\n        // States\n        let inBraces = false;\n        let braceVal = '';\n        let inBrackets = false;\n        let bracketVal = '';\n        for (const char of segment) {\n          // Support brace expansion\n          if (char !== '}' && inBraces) {\n            braceVal += char;\n            continue;\n          }\n          // Support brackets\n          if (inBrackets && (char !== ']' || !bracketVal) /* ] is literally only allowed as first character in brackets to match it */) {\n            let res;\n            // range operator\n            if (char === '-') {\n              res = char;\n            }\n            // negation operator (only valid on first index in bracket)\n            else if ((char === '^' || char === '!') && !bracketVal) {\n              res = '^';\n            }\n            // glob split matching is not allowed within character ranges\n            // see http://man7.org/linux/man-pages/man7/glob.7.html\n            else if (char === GLOB_SPLIT) {\n              res = '';\n            }\n            // anything else gets escaped\n            else {\n              res = escapeRegExpCharacters(char);\n            }\n            bracketVal += res;\n            continue;\n          }\n          switch (char) {\n            case '{':\n              inBraces = true;\n              continue;\n            case '[':\n              inBrackets = true;\n              continue;\n            case '}':\n              {\n                const choices = splitGlobAware(braceVal, ',');\n                // Converts {foo,bar} => [foo|bar]\n                const braceRegExp = `(?:${choices.map(choice => parseRegExp(choice)).join('|')})`;\n                regEx += braceRegExp;\n                inBraces = false;\n                braceVal = '';\n                break;\n              }\n            case ']':\n              {\n                regEx += '[' + bracketVal + ']';\n                inBrackets = false;\n                bracketVal = '';\n                break;\n              }\n            case '?':\n              regEx += NO_PATH_REGEX; // 1 ? matches any single character except path separator (/ and \\)\n              continue;\n            case '*':\n              regEx += starsToRegExp(1);\n              continue;\n            default:\n              regEx += escapeRegExpCharacters(char);\n          }\n        }\n        // Tail: Add the slash we had split on if there is more to\n        // come and the remaining pattern is not a globstar\n        // For example if pattern: some/**/*.js we want the \"/\" after\n        // some to be included in the RegEx to prevent a folder called\n        // \"something\" to match as well.\n        if (index < segments.length - 1 && (\n        // more segments to come after this\n        segments[index + 1] !== GLOBSTAR ||\n        // next segment is not **, or...\n        index + 2 < segments.length // ...next segment is ** but there is more segments after that\n        )) {\n          regEx += PATH_REGEX;\n        }\n      }\n      // update globstar state\n      previousSegmentWasGlobStar = segment === GLOBSTAR;\n    });\n  }\n  return regEx;\n}\n// regexes to check for trivial glob patterns that just check for String#endsWith\nconst T1 = /^\\*\\*\\/\\*\\.[\\w\\.-]+$/; // **/*.something\nconst T2 = /^\\*\\*\\/([\\w\\.-]+)\\/?$/; // **/something\nconst T3 = /^{\\*\\*\\/\\*?[\\w\\.-]+\\/?(,\\*\\*\\/\\*?[\\w\\.-]+\\/?)*}$/; // {**/*.something,**/*.else} or {**/package.json,**/project.json}\nconst T3_2 = /^{\\*\\*\\/\\*?[\\w\\.-]+(\\/(\\*\\*)?)?(,\\*\\*\\/\\*?[\\w\\.-]+(\\/(\\*\\*)?)?)*}$/; // Like T3, with optional trailing /**\nconst T4 = /^\\*\\*((\\/[\\w\\.-]+)+)\\/?$/; // **/something/else\nconst T5 = /^([\\w\\.-]+(\\/[\\w\\.-]+)*)\\/?$/; // something/else\nconst CACHE = new LRUCache(10000); // bounded to 10000 elements\nconst FALSE = function () {\n  return false;\n};\nconst NULL = function () {\n  return null;\n};\nfunction parsePattern(arg1, options) {\n  if (!arg1) {\n    return NULL;\n  }\n  // Handle relative patterns\n  let pattern;\n  if (typeof arg1 !== 'string') {\n    pattern = arg1.pattern;\n  } else {\n    pattern = arg1;\n  }\n  // Whitespace trimming\n  pattern = pattern.trim();\n  // Check cache\n  const patternKey = `${pattern}_${!!options.trimForExclusions}`;\n  let parsedPattern = CACHE.get(patternKey);\n  if (parsedPattern) {\n    return wrapRelativePattern(parsedPattern, arg1);\n  }\n  // Check for Trivials\n  let match;\n  if (T1.test(pattern)) {\n    parsedPattern = trivia1(pattern.substr(4), pattern); // common pattern: **/*.txt just need endsWith check\n  } else if (match = T2.exec(trimForExclusions(pattern, options))) {\n    // common pattern: **/some.txt just need basename check\n    parsedPattern = trivia2(match[1], pattern);\n  } else if ((options.trimForExclusions ? T3_2 : T3).test(pattern)) {\n    // repetition of common patterns (see above) {**/*.txt,**/*.png}\n    parsedPattern = trivia3(pattern, options);\n  } else if (match = T4.exec(trimForExclusions(pattern, options))) {\n    // common pattern: **/something/else just need endsWith check\n    parsedPattern = trivia4and5(match[1].substr(1), pattern, true);\n  } else if (match = T5.exec(trimForExclusions(pattern, options))) {\n    // common pattern: something/else just need equals check\n    parsedPattern = trivia4and5(match[1], pattern, false);\n  }\n  // Otherwise convert to pattern\n  else {\n    parsedPattern = toRegExp(pattern);\n  }\n  // Cache\n  CACHE.set(patternKey, parsedPattern);\n  return wrapRelativePattern(parsedPattern, arg1);\n}\nfunction wrapRelativePattern(parsedPattern, arg2) {\n  if (typeof arg2 === 'string') {\n    return parsedPattern;\n  }\n  const wrappedPattern = function (path, basename) {\n    if (!isEqualOrParent(path, arg2.base, !isLinux)) {\n      // skip glob matching if `base` is not a parent of `path`\n      return null;\n    }\n    // Given we have checked `base` being a parent of `path`,\n    // we can now remove the `base` portion of the `path`\n    // and only match on the remaining path components\n    // For that we try to extract the portion of the `path`\n    // that comes after the `base` portion. We have to account\n    // for the fact that `base` might end in a path separator\n    // (https://github.com/microsoft/vscode/issues/162498)\n    return parsedPattern(ltrim(path.substr(arg2.base.length), sep), basename);\n  };\n  // Make sure to preserve associated metadata\n  wrappedPattern.allBasenames = parsedPattern.allBasenames;\n  wrappedPattern.allPaths = parsedPattern.allPaths;\n  wrappedPattern.basenames = parsedPattern.basenames;\n  wrappedPattern.patterns = parsedPattern.patterns;\n  return wrappedPattern;\n}\nfunction trimForExclusions(pattern, options) {\n  return options.trimForExclusions && pattern.endsWith('/**') ? pattern.substr(0, pattern.length - 2) : pattern; // dropping **, tailing / is dropped later\n}\n// common pattern: **/*.txt just need endsWith check\nfunction trivia1(base, pattern) {\n  return function (path, basename) {\n    return typeof path === 'string' && path.endsWith(base) ? pattern : null;\n  };\n}\n// common pattern: **/some.txt just need basename check\nfunction trivia2(base, pattern) {\n  const slashBase = `/${base}`;\n  const backslashBase = `\\\\${base}`;\n  const parsedPattern = function (path, basename) {\n    if (typeof path !== 'string') {\n      return null;\n    }\n    if (basename) {\n      return basename === base ? pattern : null;\n    }\n    return path === base || path.endsWith(slashBase) || path.endsWith(backslashBase) ? pattern : null;\n  };\n  const basenames = [base];\n  parsedPattern.basenames = basenames;\n  parsedPattern.patterns = [pattern];\n  parsedPattern.allBasenames = basenames;\n  return parsedPattern;\n}\n// repetition of common patterns (see above) {**/*.txt,**/*.png}\nfunction trivia3(pattern, options) {\n  const parsedPatterns = aggregateBasenameMatches(pattern.slice(1, -1).split(',').map(pattern => parsePattern(pattern, options)).filter(pattern => pattern !== NULL), pattern);\n  const patternsLength = parsedPatterns.length;\n  if (!patternsLength) {\n    return NULL;\n  }\n  if (patternsLength === 1) {\n    return parsedPatterns[0];\n  }\n  const parsedPattern = function (path, basename) {\n    for (let i = 0, n = parsedPatterns.length; i < n; i++) {\n      if (parsedPatterns[i](path, basename)) {\n        return pattern;\n      }\n    }\n    return null;\n  };\n  const withBasenames = parsedPatterns.find(pattern => !!pattern.allBasenames);\n  if (withBasenames) {\n    parsedPattern.allBasenames = withBasenames.allBasenames;\n  }\n  const allPaths = parsedPatterns.reduce((all, current) => current.allPaths ? all.concat(current.allPaths) : all, []);\n  if (allPaths.length) {\n    parsedPattern.allPaths = allPaths;\n  }\n  return parsedPattern;\n}\n// common patterns: **/something/else just need endsWith check, something/else just needs and equals check\nfunction trivia4and5(targetPath, pattern, matchPathEnds) {\n  const usingPosixSep = sep === posix.sep;\n  const nativePath = usingPosixSep ? targetPath : targetPath.replace(ALL_FORWARD_SLASHES, sep);\n  const nativePathEnd = sep + nativePath;\n  const targetPathEnd = posix.sep + targetPath;\n  let parsedPattern;\n  if (matchPathEnds) {\n    parsedPattern = function (path, basename) {\n      return typeof path === 'string' && (path === nativePath || path.endsWith(nativePathEnd) || !usingPosixSep && (path === targetPath || path.endsWith(targetPathEnd))) ? pattern : null;\n    };\n  } else {\n    parsedPattern = function (path, basename) {\n      return typeof path === 'string' && (path === nativePath || !usingPosixSep && path === targetPath) ? pattern : null;\n    };\n  }\n  parsedPattern.allPaths = [(matchPathEnds ? '*/' : './') + targetPath];\n  return parsedPattern;\n}\nfunction toRegExp(pattern) {\n  try {\n    const regExp = new RegExp(`^${parseRegExp(pattern)}$`);\n    return function (path) {\n      regExp.lastIndex = 0; // reset RegExp to its initial state to reuse it!\n      return typeof path === 'string' && regExp.test(path) ? pattern : null;\n    };\n  } catch (error) {\n    return NULL;\n  }\n}\nexport function match(arg1, path, hasSibling) {\n  if (!arg1 || typeof path !== 'string') {\n    return false;\n  }\n  return parse(arg1)(path, undefined, hasSibling);\n}\nexport function parse(arg1, options = {}) {\n  if (!arg1) {\n    return FALSE;\n  }\n  // Glob with String\n  if (typeof arg1 === 'string' || isRelativePattern(arg1)) {\n    const parsedPattern = parsePattern(arg1, options);\n    if (parsedPattern === NULL) {\n      return FALSE;\n    }\n    const resultPattern = function (path, basename) {\n      return !!parsedPattern(path, basename);\n    };\n    if (parsedPattern.allBasenames) {\n      resultPattern.allBasenames = parsedPattern.allBasenames;\n    }\n    if (parsedPattern.allPaths) {\n      resultPattern.allPaths = parsedPattern.allPaths;\n    }\n    return resultPattern;\n  }\n  // Glob with Expression\n  return parsedExpression(arg1, options);\n}\nexport function isRelativePattern(obj) {\n  const rp = obj;\n  if (!rp) {\n    return false;\n  }\n  return typeof rp.base === 'string' && typeof rp.pattern === 'string';\n}\nfunction parsedExpression(expression, options) {\n  const parsedPatterns = aggregateBasenameMatches(Object.getOwnPropertyNames(expression).map(pattern => parseExpressionPattern(pattern, expression[pattern], options)).filter(pattern => pattern !== NULL));\n  const patternsLength = parsedPatterns.length;\n  if (!patternsLength) {\n    return NULL;\n  }\n  if (!parsedPatterns.some(parsedPattern => !!parsedPattern.requiresSiblings)) {\n    if (patternsLength === 1) {\n      return parsedPatterns[0];\n    }\n    const resultExpression = function (path, basename) {\n      let resultPromises = undefined;\n      for (let i = 0, n = parsedPatterns.length; i < n; i++) {\n        const result = parsedPatterns[i](path, basename);\n        if (typeof result === 'string') {\n          return result; // immediately return as soon as the first expression matches\n        }\n        // If the result is a promise, we have to keep it for\n        // later processing and await the result properly.\n        if (isThenable(result)) {\n          if (!resultPromises) {\n            resultPromises = [];\n          }\n          resultPromises.push(result);\n        }\n      }\n      // With result promises, we have to loop over each and\n      // await the result before we can return any result.\n      if (resultPromises) {\n        return _asyncToGenerator(function* () {\n          for (const resultPromise of resultPromises) {\n            const result = yield resultPromise;\n            if (typeof result === 'string') {\n              return result;\n            }\n          }\n          return null;\n        })();\n      }\n      return null;\n    };\n    const withBasenames = parsedPatterns.find(pattern => !!pattern.allBasenames);\n    if (withBasenames) {\n      resultExpression.allBasenames = withBasenames.allBasenames;\n    }\n    const allPaths = parsedPatterns.reduce((all, current) => current.allPaths ? all.concat(current.allPaths) : all, []);\n    if (allPaths.length) {\n      resultExpression.allPaths = allPaths;\n    }\n    return resultExpression;\n  }\n  const resultExpression = function (path, base, hasSibling) {\n    let name = undefined;\n    let resultPromises = undefined;\n    for (let i = 0, n = parsedPatterns.length; i < n; i++) {\n      // Pattern matches path\n      const parsedPattern = parsedPatterns[i];\n      if (parsedPattern.requiresSiblings && hasSibling) {\n        if (!base) {\n          base = basename(path);\n        }\n        if (!name) {\n          name = base.substr(0, base.length - extname(path).length);\n        }\n      }\n      const result = parsedPattern(path, base, name, hasSibling);\n      if (typeof result === 'string') {\n        return result; // immediately return as soon as the first expression matches\n      }\n      // If the result is a promise, we have to keep it for\n      // later processing and await the result properly.\n      if (isThenable(result)) {\n        if (!resultPromises) {\n          resultPromises = [];\n        }\n        resultPromises.push(result);\n      }\n    }\n    // With result promises, we have to loop over each and\n    // await the result before we can return any result.\n    if (resultPromises) {\n      return _asyncToGenerator(function* () {\n        for (const resultPromise of resultPromises) {\n          const result = yield resultPromise;\n          if (typeof result === 'string') {\n            return result;\n          }\n        }\n        return null;\n      })();\n    }\n    return null;\n  };\n  const withBasenames = parsedPatterns.find(pattern => !!pattern.allBasenames);\n  if (withBasenames) {\n    resultExpression.allBasenames = withBasenames.allBasenames;\n  }\n  const allPaths = parsedPatterns.reduce((all, current) => current.allPaths ? all.concat(current.allPaths) : all, []);\n  if (allPaths.length) {\n    resultExpression.allPaths = allPaths;\n  }\n  return resultExpression;\n}\nfunction parseExpressionPattern(pattern, value, options) {\n  if (value === false) {\n    return NULL; // pattern is disabled\n  }\n  const parsedPattern = parsePattern(pattern, options);\n  if (parsedPattern === NULL) {\n    return NULL;\n  }\n  // Expression Pattern is <boolean>\n  if (typeof value === 'boolean') {\n    return parsedPattern;\n  }\n  // Expression Pattern is <SiblingClause>\n  if (value) {\n    const when = value.when;\n    if (typeof when === 'string') {\n      const result = (path, basename, name, hasSibling) => {\n        if (!hasSibling || !parsedPattern(path, basename)) {\n          return null;\n        }\n        const clausePattern = when.replace('$(basename)', () => name);\n        const matched = hasSibling(clausePattern);\n        return isThenable(matched) ? matched.then(match => match ? pattern : null) : matched ? pattern : null;\n      };\n      result.requiresSiblings = true;\n      return result;\n    }\n  }\n  // Expression is anything\n  return parsedPattern;\n}\nfunction aggregateBasenameMatches(parsedPatterns, result) {\n  const basenamePatterns = parsedPatterns.filter(parsedPattern => !!parsedPattern.basenames);\n  if (basenamePatterns.length < 2) {\n    return parsedPatterns;\n  }\n  const basenames = basenamePatterns.reduce((all, current) => {\n    const basenames = current.basenames;\n    return basenames ? all.concat(basenames) : all;\n  }, []);\n  let patterns;\n  if (result) {\n    patterns = [];\n    for (let i = 0, n = basenames.length; i < n; i++) {\n      patterns.push(result);\n    }\n  } else {\n    patterns = basenamePatterns.reduce((all, current) => {\n      const patterns = current.patterns;\n      return patterns ? all.concat(patterns) : all;\n    }, []);\n  }\n  const aggregate = function (path, basename) {\n    if (typeof path !== 'string') {\n      return null;\n    }\n    if (!basename) {\n      let i;\n      for (i = path.length; i > 0; i--) {\n        const ch = path.charCodeAt(i - 1);\n        if (ch === 47 /* CharCode.Slash */ || ch === 92 /* CharCode.Backslash */) {\n          break;\n        }\n      }\n      basename = path.substr(i);\n    }\n    const index = basenames.indexOf(basename);\n    return index !== -1 ? patterns[index] : null;\n  };\n  aggregate.basenames = basenames;\n  aggregate.patterns = patterns;\n  aggregate.allBasenames = basenames;\n  const aggregatedPatterns = parsedPatterns.filter(parsedPattern => !parsedPattern.basenames);\n  aggregatedPatterns.push(aggregate);\n  return aggregatedPatterns;\n}", "map": {"version": 3, "names": ["isThenable", "isEqualOrParent", "L<PERSON><PERSON><PERSON>", "basename", "extname", "posix", "sep", "isLinux", "escapeRegExpCharacters", "ltrim", "GLOBSTAR", "GLOB_SPLIT", "PATH_REGEX", "NO_PATH_REGEX", "ALL_FORWARD_SLASHES", "starsToRegExp", "starCount", "isLastPattern", "splitGlobAware", "pattern", "splitChar", "segments", "inBraces", "inBrackets", "curVal", "char", "push", "parseRegExp", "regEx", "every", "segment", "previousSegmentWasGlobStar", "for<PERSON>ach", "index", "length", "braceVal", "bracketVal", "res", "choices", "braceRegExp", "map", "choice", "join", "T1", "T2", "T3", "T3_2", "T4", "T5", "CACHE", "FALSE", "NULL", "parsePattern", "arg1", "options", "trim", "pattern<PERSON>ey", "trimForExclusions", "parsedPattern", "get", "wrapRelativePattern", "match", "test", "trivia1", "substr", "exec", "trivia2", "trivia3", "trivia4and5", "toRegExp", "set", "arg2", "wrappedPattern", "path", "base", "allBasenames", "allPaths", "basenames", "patterns", "endsWith", "slashBase", "backslashBase", "parsedPatterns", "aggregateBasenameMatches", "slice", "split", "filter", "<PERSON><PERSON><PERSON><PERSON>", "i", "n", "withBasenames", "find", "reduce", "all", "current", "concat", "targetPath", "matchPathEnds", "usingPosixSep", "nativePath", "replace", "nativePathEnd", "targetPathEnd", "regExp", "RegExp", "lastIndex", "error", "<PERSON><PERSON><PERSON><PERSON>", "parse", "undefined", "isRelativePattern", "resultPattern", "parsedExpression", "obj", "rp", "expression", "Object", "getOwnPropertyNames", "parseExpressionPattern", "some", "requiresSiblings", "resultExpression", "resultPromises", "result", "_asyncToGenerator", "resultPromise", "name", "value", "when", "clausePattern", "matched", "then", "basenamePatterns", "aggregate", "ch", "charCodeAt", "indexOf", "aggregatedPatterns"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/common/glob.js"], "sourcesContent": ["import { isThenable } from './async.js';\nimport { isEqualOrParent } from './extpath.js';\nimport { LRUCache } from './map.js';\nimport { basename, extname, posix, sep } from './path.js';\nimport { isLinux } from './platform.js';\nimport { escapeRegExpCharacters, ltrim } from './strings.js';\nexport const GLOBSTAR = '**';\nexport const GLOB_SPLIT = '/';\nconst PATH_REGEX = '[/\\\\\\\\]'; // any slash or backslash\nconst NO_PATH_REGEX = '[^/\\\\\\\\]'; // any non-slash and non-backslash\nconst ALL_FORWARD_SLASHES = /\\//g;\nfunction starsToRegExp(starCount, isLastPattern) {\n    switch (starCount) {\n        case 0:\n            return '';\n        case 1:\n            return `${NO_PATH_REGEX}*?`; // 1 star matches any number of characters except path separator (/ and \\) - non greedy (?)\n        default:\n            // Matches:  (Path Sep OR Path Val followed by Path Sep) 0-many times except when it's the last pattern\n            //           in which case also matches (Path Sep followed by Path Val)\n            // Group is non capturing because we don't need to capture at all (?:...)\n            // Overall we use non-greedy matching because it could be that we match too much\n            return `(?:${PATH_REGEX}|${NO_PATH_REGEX}+${PATH_REGEX}${isLastPattern ? `|${PATH_REGEX}${NO_PATH_REGEX}+` : ''})*?`;\n    }\n}\nexport function splitGlobAware(pattern, splitChar) {\n    if (!pattern) {\n        return [];\n    }\n    const segments = [];\n    let inBraces = false;\n    let inBrackets = false;\n    let curVal = '';\n    for (const char of pattern) {\n        switch (char) {\n            case splitChar:\n                if (!inBraces && !inBrackets) {\n                    segments.push(curVal);\n                    curVal = '';\n                    continue;\n                }\n                break;\n            case '{':\n                inBraces = true;\n                break;\n            case '}':\n                inBraces = false;\n                break;\n            case '[':\n                inBrackets = true;\n                break;\n            case ']':\n                inBrackets = false;\n                break;\n        }\n        curVal += char;\n    }\n    // Tail\n    if (curVal) {\n        segments.push(curVal);\n    }\n    return segments;\n}\nfunction parseRegExp(pattern) {\n    if (!pattern) {\n        return '';\n    }\n    let regEx = '';\n    // Split up into segments for each slash found\n    const segments = splitGlobAware(pattern, GLOB_SPLIT);\n    // Special case where we only have globstars\n    if (segments.every(segment => segment === GLOBSTAR)) {\n        regEx = '.*';\n    }\n    // Build regex over segments\n    else {\n        let previousSegmentWasGlobStar = false;\n        segments.forEach((segment, index) => {\n            // Treat globstar specially\n            if (segment === GLOBSTAR) {\n                // if we have more than one globstar after another, just ignore it\n                if (previousSegmentWasGlobStar) {\n                    return;\n                }\n                regEx += starsToRegExp(2, index === segments.length - 1);\n            }\n            // Anything else, not globstar\n            else {\n                // States\n                let inBraces = false;\n                let braceVal = '';\n                let inBrackets = false;\n                let bracketVal = '';\n                for (const char of segment) {\n                    // Support brace expansion\n                    if (char !== '}' && inBraces) {\n                        braceVal += char;\n                        continue;\n                    }\n                    // Support brackets\n                    if (inBrackets && (char !== ']' || !bracketVal) /* ] is literally only allowed as first character in brackets to match it */) {\n                        let res;\n                        // range operator\n                        if (char === '-') {\n                            res = char;\n                        }\n                        // negation operator (only valid on first index in bracket)\n                        else if ((char === '^' || char === '!') && !bracketVal) {\n                            res = '^';\n                        }\n                        // glob split matching is not allowed within character ranges\n                        // see http://man7.org/linux/man-pages/man7/glob.7.html\n                        else if (char === GLOB_SPLIT) {\n                            res = '';\n                        }\n                        // anything else gets escaped\n                        else {\n                            res = escapeRegExpCharacters(char);\n                        }\n                        bracketVal += res;\n                        continue;\n                    }\n                    switch (char) {\n                        case '{':\n                            inBraces = true;\n                            continue;\n                        case '[':\n                            inBrackets = true;\n                            continue;\n                        case '}': {\n                            const choices = splitGlobAware(braceVal, ',');\n                            // Converts {foo,bar} => [foo|bar]\n                            const braceRegExp = `(?:${choices.map(choice => parseRegExp(choice)).join('|')})`;\n                            regEx += braceRegExp;\n                            inBraces = false;\n                            braceVal = '';\n                            break;\n                        }\n                        case ']': {\n                            regEx += ('[' + bracketVal + ']');\n                            inBrackets = false;\n                            bracketVal = '';\n                            break;\n                        }\n                        case '?':\n                            regEx += NO_PATH_REGEX; // 1 ? matches any single character except path separator (/ and \\)\n                            continue;\n                        case '*':\n                            regEx += starsToRegExp(1);\n                            continue;\n                        default:\n                            regEx += escapeRegExpCharacters(char);\n                    }\n                }\n                // Tail: Add the slash we had split on if there is more to\n                // come and the remaining pattern is not a globstar\n                // For example if pattern: some/**/*.js we want the \"/\" after\n                // some to be included in the RegEx to prevent a folder called\n                // \"something\" to match as well.\n                if (index < segments.length - 1 && // more segments to come after this\n                    (segments[index + 1] !== GLOBSTAR || // next segment is not **, or...\n                        index + 2 < segments.length // ...next segment is ** but there is more segments after that\n                    )) {\n                    regEx += PATH_REGEX;\n                }\n            }\n            // update globstar state\n            previousSegmentWasGlobStar = (segment === GLOBSTAR);\n        });\n    }\n    return regEx;\n}\n// regexes to check for trivial glob patterns that just check for String#endsWith\nconst T1 = /^\\*\\*\\/\\*\\.[\\w\\.-]+$/; // **/*.something\nconst T2 = /^\\*\\*\\/([\\w\\.-]+)\\/?$/; // **/something\nconst T3 = /^{\\*\\*\\/\\*?[\\w\\.-]+\\/?(,\\*\\*\\/\\*?[\\w\\.-]+\\/?)*}$/; // {**/*.something,**/*.else} or {**/package.json,**/project.json}\nconst T3_2 = /^{\\*\\*\\/\\*?[\\w\\.-]+(\\/(\\*\\*)?)?(,\\*\\*\\/\\*?[\\w\\.-]+(\\/(\\*\\*)?)?)*}$/; // Like T3, with optional trailing /**\nconst T4 = /^\\*\\*((\\/[\\w\\.-]+)+)\\/?$/; // **/something/else\nconst T5 = /^([\\w\\.-]+(\\/[\\w\\.-]+)*)\\/?$/; // something/else\nconst CACHE = new LRUCache(10000); // bounded to 10000 elements\nconst FALSE = function () {\n    return false;\n};\nconst NULL = function () {\n    return null;\n};\nfunction parsePattern(arg1, options) {\n    if (!arg1) {\n        return NULL;\n    }\n    // Handle relative patterns\n    let pattern;\n    if (typeof arg1 !== 'string') {\n        pattern = arg1.pattern;\n    }\n    else {\n        pattern = arg1;\n    }\n    // Whitespace trimming\n    pattern = pattern.trim();\n    // Check cache\n    const patternKey = `${pattern}_${!!options.trimForExclusions}`;\n    let parsedPattern = CACHE.get(patternKey);\n    if (parsedPattern) {\n        return wrapRelativePattern(parsedPattern, arg1);\n    }\n    // Check for Trivials\n    let match;\n    if (T1.test(pattern)) {\n        parsedPattern = trivia1(pattern.substr(4), pattern); // common pattern: **/*.txt just need endsWith check\n    }\n    else if (match = T2.exec(trimForExclusions(pattern, options))) { // common pattern: **/some.txt just need basename check\n        parsedPattern = trivia2(match[1], pattern);\n    }\n    else if ((options.trimForExclusions ? T3_2 : T3).test(pattern)) { // repetition of common patterns (see above) {**/*.txt,**/*.png}\n        parsedPattern = trivia3(pattern, options);\n    }\n    else if (match = T4.exec(trimForExclusions(pattern, options))) { // common pattern: **/something/else just need endsWith check\n        parsedPattern = trivia4and5(match[1].substr(1), pattern, true);\n    }\n    else if (match = T5.exec(trimForExclusions(pattern, options))) { // common pattern: something/else just need equals check\n        parsedPattern = trivia4and5(match[1], pattern, false);\n    }\n    // Otherwise convert to pattern\n    else {\n        parsedPattern = toRegExp(pattern);\n    }\n    // Cache\n    CACHE.set(patternKey, parsedPattern);\n    return wrapRelativePattern(parsedPattern, arg1);\n}\nfunction wrapRelativePattern(parsedPattern, arg2) {\n    if (typeof arg2 === 'string') {\n        return parsedPattern;\n    }\n    const wrappedPattern = function (path, basename) {\n        if (!isEqualOrParent(path, arg2.base, !isLinux)) {\n            // skip glob matching if `base` is not a parent of `path`\n            return null;\n        }\n        // Given we have checked `base` being a parent of `path`,\n        // we can now remove the `base` portion of the `path`\n        // and only match on the remaining path components\n        // For that we try to extract the portion of the `path`\n        // that comes after the `base` portion. We have to account\n        // for the fact that `base` might end in a path separator\n        // (https://github.com/microsoft/vscode/issues/162498)\n        return parsedPattern(ltrim(path.substr(arg2.base.length), sep), basename);\n    };\n    // Make sure to preserve associated metadata\n    wrappedPattern.allBasenames = parsedPattern.allBasenames;\n    wrappedPattern.allPaths = parsedPattern.allPaths;\n    wrappedPattern.basenames = parsedPattern.basenames;\n    wrappedPattern.patterns = parsedPattern.patterns;\n    return wrappedPattern;\n}\nfunction trimForExclusions(pattern, options) {\n    return options.trimForExclusions && pattern.endsWith('/**') ? pattern.substr(0, pattern.length - 2) : pattern; // dropping **, tailing / is dropped later\n}\n// common pattern: **/*.txt just need endsWith check\nfunction trivia1(base, pattern) {\n    return function (path, basename) {\n        return typeof path === 'string' && path.endsWith(base) ? pattern : null;\n    };\n}\n// common pattern: **/some.txt just need basename check\nfunction trivia2(base, pattern) {\n    const slashBase = `/${base}`;\n    const backslashBase = `\\\\${base}`;\n    const parsedPattern = function (path, basename) {\n        if (typeof path !== 'string') {\n            return null;\n        }\n        if (basename) {\n            return basename === base ? pattern : null;\n        }\n        return path === base || path.endsWith(slashBase) || path.endsWith(backslashBase) ? pattern : null;\n    };\n    const basenames = [base];\n    parsedPattern.basenames = basenames;\n    parsedPattern.patterns = [pattern];\n    parsedPattern.allBasenames = basenames;\n    return parsedPattern;\n}\n// repetition of common patterns (see above) {**/*.txt,**/*.png}\nfunction trivia3(pattern, options) {\n    const parsedPatterns = aggregateBasenameMatches(pattern.slice(1, -1)\n        .split(',')\n        .map(pattern => parsePattern(pattern, options))\n        .filter(pattern => pattern !== NULL), pattern);\n    const patternsLength = parsedPatterns.length;\n    if (!patternsLength) {\n        return NULL;\n    }\n    if (patternsLength === 1) {\n        return parsedPatterns[0];\n    }\n    const parsedPattern = function (path, basename) {\n        for (let i = 0, n = parsedPatterns.length; i < n; i++) {\n            if (parsedPatterns[i](path, basename)) {\n                return pattern;\n            }\n        }\n        return null;\n    };\n    const withBasenames = parsedPatterns.find(pattern => !!pattern.allBasenames);\n    if (withBasenames) {\n        parsedPattern.allBasenames = withBasenames.allBasenames;\n    }\n    const allPaths = parsedPatterns.reduce((all, current) => current.allPaths ? all.concat(current.allPaths) : all, []);\n    if (allPaths.length) {\n        parsedPattern.allPaths = allPaths;\n    }\n    return parsedPattern;\n}\n// common patterns: **/something/else just need endsWith check, something/else just needs and equals check\nfunction trivia4and5(targetPath, pattern, matchPathEnds) {\n    const usingPosixSep = sep === posix.sep;\n    const nativePath = usingPosixSep ? targetPath : targetPath.replace(ALL_FORWARD_SLASHES, sep);\n    const nativePathEnd = sep + nativePath;\n    const targetPathEnd = posix.sep + targetPath;\n    let parsedPattern;\n    if (matchPathEnds) {\n        parsedPattern = function (path, basename) {\n            return typeof path === 'string' && ((path === nativePath || path.endsWith(nativePathEnd)) || !usingPosixSep && (path === targetPath || path.endsWith(targetPathEnd))) ? pattern : null;\n        };\n    }\n    else {\n        parsedPattern = function (path, basename) {\n            return typeof path === 'string' && (path === nativePath || (!usingPosixSep && path === targetPath)) ? pattern : null;\n        };\n    }\n    parsedPattern.allPaths = [(matchPathEnds ? '*/' : './') + targetPath];\n    return parsedPattern;\n}\nfunction toRegExp(pattern) {\n    try {\n        const regExp = new RegExp(`^${parseRegExp(pattern)}$`);\n        return function (path) {\n            regExp.lastIndex = 0; // reset RegExp to its initial state to reuse it!\n            return typeof path === 'string' && regExp.test(path) ? pattern : null;\n        };\n    }\n    catch (error) {\n        return NULL;\n    }\n}\nexport function match(arg1, path, hasSibling) {\n    if (!arg1 || typeof path !== 'string') {\n        return false;\n    }\n    return parse(arg1)(path, undefined, hasSibling);\n}\nexport function parse(arg1, options = {}) {\n    if (!arg1) {\n        return FALSE;\n    }\n    // Glob with String\n    if (typeof arg1 === 'string' || isRelativePattern(arg1)) {\n        const parsedPattern = parsePattern(arg1, options);\n        if (parsedPattern === NULL) {\n            return FALSE;\n        }\n        const resultPattern = function (path, basename) {\n            return !!parsedPattern(path, basename);\n        };\n        if (parsedPattern.allBasenames) {\n            resultPattern.allBasenames = parsedPattern.allBasenames;\n        }\n        if (parsedPattern.allPaths) {\n            resultPattern.allPaths = parsedPattern.allPaths;\n        }\n        return resultPattern;\n    }\n    // Glob with Expression\n    return parsedExpression(arg1, options);\n}\nexport function isRelativePattern(obj) {\n    const rp = obj;\n    if (!rp) {\n        return false;\n    }\n    return typeof rp.base === 'string' && typeof rp.pattern === 'string';\n}\nfunction parsedExpression(expression, options) {\n    const parsedPatterns = aggregateBasenameMatches(Object.getOwnPropertyNames(expression)\n        .map(pattern => parseExpressionPattern(pattern, expression[pattern], options))\n        .filter(pattern => pattern !== NULL));\n    const patternsLength = parsedPatterns.length;\n    if (!patternsLength) {\n        return NULL;\n    }\n    if (!parsedPatterns.some(parsedPattern => !!parsedPattern.requiresSiblings)) {\n        if (patternsLength === 1) {\n            return parsedPatterns[0];\n        }\n        const resultExpression = function (path, basename) {\n            let resultPromises = undefined;\n            for (let i = 0, n = parsedPatterns.length; i < n; i++) {\n                const result = parsedPatterns[i](path, basename);\n                if (typeof result === 'string') {\n                    return result; // immediately return as soon as the first expression matches\n                }\n                // If the result is a promise, we have to keep it for\n                // later processing and await the result properly.\n                if (isThenable(result)) {\n                    if (!resultPromises) {\n                        resultPromises = [];\n                    }\n                    resultPromises.push(result);\n                }\n            }\n            // With result promises, we have to loop over each and\n            // await the result before we can return any result.\n            if (resultPromises) {\n                return (async () => {\n                    for (const resultPromise of resultPromises) {\n                        const result = await resultPromise;\n                        if (typeof result === 'string') {\n                            return result;\n                        }\n                    }\n                    return null;\n                })();\n            }\n            return null;\n        };\n        const withBasenames = parsedPatterns.find(pattern => !!pattern.allBasenames);\n        if (withBasenames) {\n            resultExpression.allBasenames = withBasenames.allBasenames;\n        }\n        const allPaths = parsedPatterns.reduce((all, current) => current.allPaths ? all.concat(current.allPaths) : all, []);\n        if (allPaths.length) {\n            resultExpression.allPaths = allPaths;\n        }\n        return resultExpression;\n    }\n    const resultExpression = function (path, base, hasSibling) {\n        let name = undefined;\n        let resultPromises = undefined;\n        for (let i = 0, n = parsedPatterns.length; i < n; i++) {\n            // Pattern matches path\n            const parsedPattern = parsedPatterns[i];\n            if (parsedPattern.requiresSiblings && hasSibling) {\n                if (!base) {\n                    base = basename(path);\n                }\n                if (!name) {\n                    name = base.substr(0, base.length - extname(path).length);\n                }\n            }\n            const result = parsedPattern(path, base, name, hasSibling);\n            if (typeof result === 'string') {\n                return result; // immediately return as soon as the first expression matches\n            }\n            // If the result is a promise, we have to keep it for\n            // later processing and await the result properly.\n            if (isThenable(result)) {\n                if (!resultPromises) {\n                    resultPromises = [];\n                }\n                resultPromises.push(result);\n            }\n        }\n        // With result promises, we have to loop over each and\n        // await the result before we can return any result.\n        if (resultPromises) {\n            return (async () => {\n                for (const resultPromise of resultPromises) {\n                    const result = await resultPromise;\n                    if (typeof result === 'string') {\n                        return result;\n                    }\n                }\n                return null;\n            })();\n        }\n        return null;\n    };\n    const withBasenames = parsedPatterns.find(pattern => !!pattern.allBasenames);\n    if (withBasenames) {\n        resultExpression.allBasenames = withBasenames.allBasenames;\n    }\n    const allPaths = parsedPatterns.reduce((all, current) => current.allPaths ? all.concat(current.allPaths) : all, []);\n    if (allPaths.length) {\n        resultExpression.allPaths = allPaths;\n    }\n    return resultExpression;\n}\nfunction parseExpressionPattern(pattern, value, options) {\n    if (value === false) {\n        return NULL; // pattern is disabled\n    }\n    const parsedPattern = parsePattern(pattern, options);\n    if (parsedPattern === NULL) {\n        return NULL;\n    }\n    // Expression Pattern is <boolean>\n    if (typeof value === 'boolean') {\n        return parsedPattern;\n    }\n    // Expression Pattern is <SiblingClause>\n    if (value) {\n        const when = value.when;\n        if (typeof when === 'string') {\n            const result = (path, basename, name, hasSibling) => {\n                if (!hasSibling || !parsedPattern(path, basename)) {\n                    return null;\n                }\n                const clausePattern = when.replace('$(basename)', () => name);\n                const matched = hasSibling(clausePattern);\n                return isThenable(matched) ?\n                    matched.then(match => match ? pattern : null) :\n                    matched ? pattern : null;\n            };\n            result.requiresSiblings = true;\n            return result;\n        }\n    }\n    // Expression is anything\n    return parsedPattern;\n}\nfunction aggregateBasenameMatches(parsedPatterns, result) {\n    const basenamePatterns = parsedPatterns.filter(parsedPattern => !!parsedPattern.basenames);\n    if (basenamePatterns.length < 2) {\n        return parsedPatterns;\n    }\n    const basenames = basenamePatterns.reduce((all, current) => {\n        const basenames = current.basenames;\n        return basenames ? all.concat(basenames) : all;\n    }, []);\n    let patterns;\n    if (result) {\n        patterns = [];\n        for (let i = 0, n = basenames.length; i < n; i++) {\n            patterns.push(result);\n        }\n    }\n    else {\n        patterns = basenamePatterns.reduce((all, current) => {\n            const patterns = current.patterns;\n            return patterns ? all.concat(patterns) : all;\n        }, []);\n    }\n    const aggregate = function (path, basename) {\n        if (typeof path !== 'string') {\n            return null;\n        }\n        if (!basename) {\n            let i;\n            for (i = path.length; i > 0; i--) {\n                const ch = path.charCodeAt(i - 1);\n                if (ch === 47 /* CharCode.Slash */ || ch === 92 /* CharCode.Backslash */) {\n                    break;\n                }\n            }\n            basename = path.substr(i);\n        }\n        const index = basenames.indexOf(basename);\n        return index !== -1 ? patterns[index] : null;\n    };\n    aggregate.basenames = basenames;\n    aggregate.patterns = patterns;\n    aggregate.allBasenames = basenames;\n    const aggregatedPatterns = parsedPatterns.filter(parsedPattern => !parsedPattern.basenames);\n    aggregatedPatterns.push(aggregate);\n    return aggregatedPatterns;\n}\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,YAAY;AACvC,SAASC,eAAe,QAAQ,cAAc;AAC9C,SAASC,QAAQ,QAAQ,UAAU;AACnC,SAASC,QAAQ,EAAEC,OAAO,EAAEC,KAAK,EAAEC,GAAG,QAAQ,WAAW;AACzD,SAASC,OAAO,QAAQ,eAAe;AACvC,SAASC,sBAAsB,EAAEC,KAAK,QAAQ,cAAc;AAC5D,OAAO,MAAMC,QAAQ,GAAG,IAAI;AAC5B,OAAO,MAAMC,UAAU,GAAG,GAAG;AAC7B,MAAMC,UAAU,GAAG,SAAS,CAAC,CAAC;AAC9B,MAAMC,aAAa,GAAG,UAAU,CAAC,CAAC;AAClC,MAAMC,mBAAmB,GAAG,KAAK;AACjC,SAASC,aAAaA,CAACC,SAAS,EAAEC,aAAa,EAAE;EAC7C,QAAQD,SAAS;IACb,KAAK,CAAC;MACF,OAAO,EAAE;IACb,KAAK,CAAC;MACF,OAAO,GAAGH,aAAa,IAAI;IAAE;IACjC;MACI;MACA;MACA;MACA;MACA,OAAO,MAAMD,UAAU,IAAIC,aAAa,IAAID,UAAU,GAAGK,aAAa,GAAG,IAAIL,UAAU,GAAGC,aAAa,GAAG,GAAG,EAAE,KAAK;EAC5H;AACJ;AACA,OAAO,SAASK,cAAcA,CAACC,OAAO,EAAEC,SAAS,EAAE;EAC/C,IAAI,CAACD,OAAO,EAAE;IACV,OAAO,EAAE;EACb;EACA,MAAME,QAAQ,GAAG,EAAE;EACnB,IAAIC,QAAQ,GAAG,KAAK;EACpB,IAAIC,UAAU,GAAG,KAAK;EACtB,IAAIC,MAAM,GAAG,EAAE;EACf,KAAK,MAAMC,IAAI,IAAIN,OAAO,EAAE;IACxB,QAAQM,IAAI;MACR,KAAKL,SAAS;QACV,IAAI,CAACE,QAAQ,IAAI,CAACC,UAAU,EAAE;UAC1BF,QAAQ,CAACK,IAAI,CAACF,MAAM,CAAC;UACrBA,MAAM,GAAG,EAAE;UACX;QACJ;QACA;MACJ,KAAK,GAAG;QACJF,QAAQ,GAAG,IAAI;QACf;MACJ,KAAK,GAAG;QACJA,QAAQ,GAAG,KAAK;QAChB;MACJ,KAAK,GAAG;QACJC,UAAU,GAAG,IAAI;QACjB;MACJ,KAAK,GAAG;QACJA,UAAU,GAAG,KAAK;QAClB;IACR;IACAC,MAAM,IAAIC,IAAI;EAClB;EACA;EACA,IAAID,MAAM,EAAE;IACRH,QAAQ,CAACK,IAAI,CAACF,MAAM,CAAC;EACzB;EACA,OAAOH,QAAQ;AACnB;AACA,SAASM,WAAWA,CAACR,OAAO,EAAE;EAC1B,IAAI,CAACA,OAAO,EAAE;IACV,OAAO,EAAE;EACb;EACA,IAAIS,KAAK,GAAG,EAAE;EACd;EACA,MAAMP,QAAQ,GAAGH,cAAc,CAACC,OAAO,EAAER,UAAU,CAAC;EACpD;EACA,IAAIU,QAAQ,CAACQ,KAAK,CAACC,OAAO,IAAIA,OAAO,KAAKpB,QAAQ,CAAC,EAAE;IACjDkB,KAAK,GAAG,IAAI;EAChB;EACA;EAAA,KACK;IACD,IAAIG,0BAA0B,GAAG,KAAK;IACtCV,QAAQ,CAACW,OAAO,CAAC,CAACF,OAAO,EAAEG,KAAK,KAAK;MACjC;MACA,IAAIH,OAAO,KAAKpB,QAAQ,EAAE;QACtB;QACA,IAAIqB,0BAA0B,EAAE;UAC5B;QACJ;QACAH,KAAK,IAAIb,aAAa,CAAC,CAAC,EAAEkB,KAAK,KAAKZ,QAAQ,CAACa,MAAM,GAAG,CAAC,CAAC;MAC5D;MACA;MAAA,KACK;QACD;QACA,IAAIZ,QAAQ,GAAG,KAAK;QACpB,IAAIa,QAAQ,GAAG,EAAE;QACjB,IAAIZ,UAAU,GAAG,KAAK;QACtB,IAAIa,UAAU,GAAG,EAAE;QACnB,KAAK,MAAMX,IAAI,IAAIK,OAAO,EAAE;UACxB;UACA,IAAIL,IAAI,KAAK,GAAG,IAAIH,QAAQ,EAAE;YAC1Ba,QAAQ,IAAIV,IAAI;YAChB;UACJ;UACA;UACA,IAAIF,UAAU,KAAKE,IAAI,KAAK,GAAG,IAAI,CAACW,UAAU,CAAC,CAAC,8EAA8E;YAC1H,IAAIC,GAAG;YACP;YACA,IAAIZ,IAAI,KAAK,GAAG,EAAE;cACdY,GAAG,GAAGZ,IAAI;YACd;YACA;YAAA,KACK,IAAI,CAACA,IAAI,KAAK,GAAG,IAAIA,IAAI,KAAK,GAAG,KAAK,CAACW,UAAU,EAAE;cACpDC,GAAG,GAAG,GAAG;YACb;YACA;YACA;YAAA,KACK,IAAIZ,IAAI,KAAKd,UAAU,EAAE;cAC1B0B,GAAG,GAAG,EAAE;YACZ;YACA;YAAA,KACK;cACDA,GAAG,GAAG7B,sBAAsB,CAACiB,IAAI,CAAC;YACtC;YACAW,UAAU,IAAIC,GAAG;YACjB;UACJ;UACA,QAAQZ,IAAI;YACR,KAAK,GAAG;cACJH,QAAQ,GAAG,IAAI;cACf;YACJ,KAAK,GAAG;cACJC,UAAU,GAAG,IAAI;cACjB;YACJ,KAAK,GAAG;cAAE;gBACN,MAAMe,OAAO,GAAGpB,cAAc,CAACiB,QAAQ,EAAE,GAAG,CAAC;gBAC7C;gBACA,MAAMI,WAAW,GAAG,MAAMD,OAAO,CAACE,GAAG,CAACC,MAAM,IAAId,WAAW,CAACc,MAAM,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,GAAG;gBACjFd,KAAK,IAAIW,WAAW;gBACpBjB,QAAQ,GAAG,KAAK;gBAChBa,QAAQ,GAAG,EAAE;gBACb;cACJ;YACA,KAAK,GAAG;cAAE;gBACNP,KAAK,IAAK,GAAG,GAAGQ,UAAU,GAAG,GAAI;gBACjCb,UAAU,GAAG,KAAK;gBAClBa,UAAU,GAAG,EAAE;gBACf;cACJ;YACA,KAAK,GAAG;cACJR,KAAK,IAAIf,aAAa,CAAC,CAAC;cACxB;YACJ,KAAK,GAAG;cACJe,KAAK,IAAIb,aAAa,CAAC,CAAC,CAAC;cACzB;YACJ;cACIa,KAAK,IAAIpB,sBAAsB,CAACiB,IAAI,CAAC;UAC7C;QACJ;QACA;QACA;QACA;QACA;QACA;QACA,IAAIQ,KAAK,GAAGZ,QAAQ,CAACa,MAAM,GAAG,CAAC;QAAI;QAC9Bb,QAAQ,CAACY,KAAK,GAAG,CAAC,CAAC,KAAKvB,QAAQ;QAAI;QACjCuB,KAAK,GAAG,CAAC,GAAGZ,QAAQ,CAACa,MAAM,CAAC;QAAA,CAC/B,EAAE;UACHN,KAAK,IAAIhB,UAAU;QACvB;MACJ;MACA;MACAmB,0BAA0B,GAAID,OAAO,KAAKpB,QAAS;IACvD,CAAC,CAAC;EACN;EACA,OAAOkB,KAAK;AAChB;AACA;AACA,MAAMe,EAAE,GAAG,sBAAsB,CAAC,CAAC;AACnC,MAAMC,EAAE,GAAG,uBAAuB,CAAC,CAAC;AACpC,MAAMC,EAAE,GAAG,kDAAkD,CAAC,CAAC;AAC/D,MAAMC,IAAI,GAAG,oEAAoE,CAAC,CAAC;AACnF,MAAMC,EAAE,GAAG,0BAA0B,CAAC,CAAC;AACvC,MAAMC,EAAE,GAAG,8BAA8B,CAAC,CAAC;AAC3C,MAAMC,KAAK,GAAG,IAAI/C,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;AACnC,MAAMgD,KAAK,GAAG,SAAAA,CAAA,EAAY;EACtB,OAAO,KAAK;AAChB,CAAC;AACD,MAAMC,IAAI,GAAG,SAAAA,CAAA,EAAY;EACrB,OAAO,IAAI;AACf,CAAC;AACD,SAASC,YAAYA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACjC,IAAI,CAACD,IAAI,EAAE;IACP,OAAOF,IAAI;EACf;EACA;EACA,IAAIhC,OAAO;EACX,IAAI,OAAOkC,IAAI,KAAK,QAAQ,EAAE;IAC1BlC,OAAO,GAAGkC,IAAI,CAAClC,OAAO;EAC1B,CAAC,MACI;IACDA,OAAO,GAAGkC,IAAI;EAClB;EACA;EACAlC,OAAO,GAAGA,OAAO,CAACoC,IAAI,CAAC,CAAC;EACxB;EACA,MAAMC,UAAU,GAAG,GAAGrC,OAAO,IAAI,CAAC,CAACmC,OAAO,CAACG,iBAAiB,EAAE;EAC9D,IAAIC,aAAa,GAAGT,KAAK,CAACU,GAAG,CAACH,UAAU,CAAC;EACzC,IAAIE,aAAa,EAAE;IACf,OAAOE,mBAAmB,CAACF,aAAa,EAAEL,IAAI,CAAC;EACnD;EACA;EACA,IAAIQ,KAAK;EACT,IAAIlB,EAAE,CAACmB,IAAI,CAAC3C,OAAO,CAAC,EAAE;IAClBuC,aAAa,GAAGK,OAAO,CAAC5C,OAAO,CAAC6C,MAAM,CAAC,CAAC,CAAC,EAAE7C,OAAO,CAAC,CAAC,CAAC;EACzD,CAAC,MACI,IAAI0C,KAAK,GAAGjB,EAAE,CAACqB,IAAI,CAACR,iBAAiB,CAACtC,OAAO,EAAEmC,OAAO,CAAC,CAAC,EAAE;IAAE;IAC7DI,aAAa,GAAGQ,OAAO,CAACL,KAAK,CAAC,CAAC,CAAC,EAAE1C,OAAO,CAAC;EAC9C,CAAC,MACI,IAAI,CAACmC,OAAO,CAACG,iBAAiB,GAAGX,IAAI,GAAGD,EAAE,EAAEiB,IAAI,CAAC3C,OAAO,CAAC,EAAE;IAAE;IAC9DuC,aAAa,GAAGS,OAAO,CAAChD,OAAO,EAAEmC,OAAO,CAAC;EAC7C,CAAC,MACI,IAAIO,KAAK,GAAGd,EAAE,CAACkB,IAAI,CAACR,iBAAiB,CAACtC,OAAO,EAAEmC,OAAO,CAAC,CAAC,EAAE;IAAE;IAC7DI,aAAa,GAAGU,WAAW,CAACP,KAAK,CAAC,CAAC,CAAC,CAACG,MAAM,CAAC,CAAC,CAAC,EAAE7C,OAAO,EAAE,IAAI,CAAC;EAClE,CAAC,MACI,IAAI0C,KAAK,GAAGb,EAAE,CAACiB,IAAI,CAACR,iBAAiB,CAACtC,OAAO,EAAEmC,OAAO,CAAC,CAAC,EAAE;IAAE;IAC7DI,aAAa,GAAGU,WAAW,CAACP,KAAK,CAAC,CAAC,CAAC,EAAE1C,OAAO,EAAE,KAAK,CAAC;EACzD;EACA;EAAA,KACK;IACDuC,aAAa,GAAGW,QAAQ,CAAClD,OAAO,CAAC;EACrC;EACA;EACA8B,KAAK,CAACqB,GAAG,CAACd,UAAU,EAAEE,aAAa,CAAC;EACpC,OAAOE,mBAAmB,CAACF,aAAa,EAAEL,IAAI,CAAC;AACnD;AACA,SAASO,mBAAmBA,CAACF,aAAa,EAAEa,IAAI,EAAE;EAC9C,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC1B,OAAOb,aAAa;EACxB;EACA,MAAMc,cAAc,GAAG,SAAAA,CAAUC,IAAI,EAAEtE,QAAQ,EAAE;IAC7C,IAAI,CAACF,eAAe,CAACwE,IAAI,EAAEF,IAAI,CAACG,IAAI,EAAE,CAACnE,OAAO,CAAC,EAAE;MAC7C;MACA,OAAO,IAAI;IACf;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,OAAOmD,aAAa,CAACjD,KAAK,CAACgE,IAAI,CAACT,MAAM,CAACO,IAAI,CAACG,IAAI,CAACxC,MAAM,CAAC,EAAE5B,GAAG,CAAC,EAAEH,QAAQ,CAAC;EAC7E,CAAC;EACD;EACAqE,cAAc,CAACG,YAAY,GAAGjB,aAAa,CAACiB,YAAY;EACxDH,cAAc,CAACI,QAAQ,GAAGlB,aAAa,CAACkB,QAAQ;EAChDJ,cAAc,CAACK,SAAS,GAAGnB,aAAa,CAACmB,SAAS;EAClDL,cAAc,CAACM,QAAQ,GAAGpB,aAAa,CAACoB,QAAQ;EAChD,OAAON,cAAc;AACzB;AACA,SAASf,iBAAiBA,CAACtC,OAAO,EAAEmC,OAAO,EAAE;EACzC,OAAOA,OAAO,CAACG,iBAAiB,IAAItC,OAAO,CAAC4D,QAAQ,CAAC,KAAK,CAAC,GAAG5D,OAAO,CAAC6C,MAAM,CAAC,CAAC,EAAE7C,OAAO,CAACe,MAAM,GAAG,CAAC,CAAC,GAAGf,OAAO,CAAC,CAAC;AACnH;AACA;AACA,SAAS4C,OAAOA,CAACW,IAAI,EAAEvD,OAAO,EAAE;EAC5B,OAAO,UAAUsD,IAAI,EAAEtE,QAAQ,EAAE;IAC7B,OAAO,OAAOsE,IAAI,KAAK,QAAQ,IAAIA,IAAI,CAACM,QAAQ,CAACL,IAAI,CAAC,GAAGvD,OAAO,GAAG,IAAI;EAC3E,CAAC;AACL;AACA;AACA,SAAS+C,OAAOA,CAACQ,IAAI,EAAEvD,OAAO,EAAE;EAC5B,MAAM6D,SAAS,GAAG,IAAIN,IAAI,EAAE;EAC5B,MAAMO,aAAa,GAAG,KAAKP,IAAI,EAAE;EACjC,MAAMhB,aAAa,GAAG,SAAAA,CAAUe,IAAI,EAAEtE,QAAQ,EAAE;IAC5C,IAAI,OAAOsE,IAAI,KAAK,QAAQ,EAAE;MAC1B,OAAO,IAAI;IACf;IACA,IAAItE,QAAQ,EAAE;MACV,OAAOA,QAAQ,KAAKuE,IAAI,GAAGvD,OAAO,GAAG,IAAI;IAC7C;IACA,OAAOsD,IAAI,KAAKC,IAAI,IAAID,IAAI,CAACM,QAAQ,CAACC,SAAS,CAAC,IAAIP,IAAI,CAACM,QAAQ,CAACE,aAAa,CAAC,GAAG9D,OAAO,GAAG,IAAI;EACrG,CAAC;EACD,MAAM0D,SAAS,GAAG,CAACH,IAAI,CAAC;EACxBhB,aAAa,CAACmB,SAAS,GAAGA,SAAS;EACnCnB,aAAa,CAACoB,QAAQ,GAAG,CAAC3D,OAAO,CAAC;EAClCuC,aAAa,CAACiB,YAAY,GAAGE,SAAS;EACtC,OAAOnB,aAAa;AACxB;AACA;AACA,SAASS,OAAOA,CAAChD,OAAO,EAAEmC,OAAO,EAAE;EAC/B,MAAM4B,cAAc,GAAGC,wBAAwB,CAAChE,OAAO,CAACiE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAC/DC,KAAK,CAAC,GAAG,CAAC,CACV7C,GAAG,CAACrB,OAAO,IAAIiC,YAAY,CAACjC,OAAO,EAAEmC,OAAO,CAAC,CAAC,CAC9CgC,MAAM,CAACnE,OAAO,IAAIA,OAAO,KAAKgC,IAAI,CAAC,EAAEhC,OAAO,CAAC;EAClD,MAAMoE,cAAc,GAAGL,cAAc,CAAChD,MAAM;EAC5C,IAAI,CAACqD,cAAc,EAAE;IACjB,OAAOpC,IAAI;EACf;EACA,IAAIoC,cAAc,KAAK,CAAC,EAAE;IACtB,OAAOL,cAAc,CAAC,CAAC,CAAC;EAC5B;EACA,MAAMxB,aAAa,GAAG,SAAAA,CAAUe,IAAI,EAAEtE,QAAQ,EAAE;IAC5C,KAAK,IAAIqF,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGP,cAAc,CAAChD,MAAM,EAAEsD,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MACnD,IAAIN,cAAc,CAACM,CAAC,CAAC,CAACf,IAAI,EAAEtE,QAAQ,CAAC,EAAE;QACnC,OAAOgB,OAAO;MAClB;IACJ;IACA,OAAO,IAAI;EACf,CAAC;EACD,MAAMuE,aAAa,GAAGR,cAAc,CAACS,IAAI,CAACxE,OAAO,IAAI,CAAC,CAACA,OAAO,CAACwD,YAAY,CAAC;EAC5E,IAAIe,aAAa,EAAE;IACfhC,aAAa,CAACiB,YAAY,GAAGe,aAAa,CAACf,YAAY;EAC3D;EACA,MAAMC,QAAQ,GAAGM,cAAc,CAACU,MAAM,CAAC,CAACC,GAAG,EAAEC,OAAO,KAAKA,OAAO,CAAClB,QAAQ,GAAGiB,GAAG,CAACE,MAAM,CAACD,OAAO,CAAClB,QAAQ,CAAC,GAAGiB,GAAG,EAAE,EAAE,CAAC;EACnH,IAAIjB,QAAQ,CAAC1C,MAAM,EAAE;IACjBwB,aAAa,CAACkB,QAAQ,GAAGA,QAAQ;EACrC;EACA,OAAOlB,aAAa;AACxB;AACA;AACA,SAASU,WAAWA,CAAC4B,UAAU,EAAE7E,OAAO,EAAE8E,aAAa,EAAE;EACrD,MAAMC,aAAa,GAAG5F,GAAG,KAAKD,KAAK,CAACC,GAAG;EACvC,MAAM6F,UAAU,GAAGD,aAAa,GAAGF,UAAU,GAAGA,UAAU,CAACI,OAAO,CAACtF,mBAAmB,EAAER,GAAG,CAAC;EAC5F,MAAM+F,aAAa,GAAG/F,GAAG,GAAG6F,UAAU;EACtC,MAAMG,aAAa,GAAGjG,KAAK,CAACC,GAAG,GAAG0F,UAAU;EAC5C,IAAItC,aAAa;EACjB,IAAIuC,aAAa,EAAE;IACfvC,aAAa,GAAG,SAAAA,CAAUe,IAAI,EAAEtE,QAAQ,EAAE;MACtC,OAAO,OAAOsE,IAAI,KAAK,QAAQ,KAAMA,IAAI,KAAK0B,UAAU,IAAI1B,IAAI,CAACM,QAAQ,CAACsB,aAAa,CAAC,IAAK,CAACH,aAAa,KAAKzB,IAAI,KAAKuB,UAAU,IAAIvB,IAAI,CAACM,QAAQ,CAACuB,aAAa,CAAC,CAAC,CAAC,GAAGnF,OAAO,GAAG,IAAI;IAC1L,CAAC;EACL,CAAC,MACI;IACDuC,aAAa,GAAG,SAAAA,CAAUe,IAAI,EAAEtE,QAAQ,EAAE;MACtC,OAAO,OAAOsE,IAAI,KAAK,QAAQ,KAAKA,IAAI,KAAK0B,UAAU,IAAK,CAACD,aAAa,IAAIzB,IAAI,KAAKuB,UAAW,CAAC,GAAG7E,OAAO,GAAG,IAAI;IACxH,CAAC;EACL;EACAuC,aAAa,CAACkB,QAAQ,GAAG,CAAC,CAACqB,aAAa,GAAG,IAAI,GAAG,IAAI,IAAID,UAAU,CAAC;EACrE,OAAOtC,aAAa;AACxB;AACA,SAASW,QAAQA,CAAClD,OAAO,EAAE;EACvB,IAAI;IACA,MAAMoF,MAAM,GAAG,IAAIC,MAAM,CAAC,IAAI7E,WAAW,CAACR,OAAO,CAAC,GAAG,CAAC;IACtD,OAAO,UAAUsD,IAAI,EAAE;MACnB8B,MAAM,CAACE,SAAS,GAAG,CAAC,CAAC,CAAC;MACtB,OAAO,OAAOhC,IAAI,KAAK,QAAQ,IAAI8B,MAAM,CAACzC,IAAI,CAACW,IAAI,CAAC,GAAGtD,OAAO,GAAG,IAAI;IACzE,CAAC;EACL,CAAC,CACD,OAAOuF,KAAK,EAAE;IACV,OAAOvD,IAAI;EACf;AACJ;AACA,OAAO,SAASU,KAAKA,CAACR,IAAI,EAAEoB,IAAI,EAAEkC,UAAU,EAAE;EAC1C,IAAI,CAACtD,IAAI,IAAI,OAAOoB,IAAI,KAAK,QAAQ,EAAE;IACnC,OAAO,KAAK;EAChB;EACA,OAAOmC,KAAK,CAACvD,IAAI,CAAC,CAACoB,IAAI,EAAEoC,SAAS,EAAEF,UAAU,CAAC;AACnD;AACA,OAAO,SAASC,KAAKA,CAACvD,IAAI,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;EACtC,IAAI,CAACD,IAAI,EAAE;IACP,OAAOH,KAAK;EAChB;EACA;EACA,IAAI,OAAOG,IAAI,KAAK,QAAQ,IAAIyD,iBAAiB,CAACzD,IAAI,CAAC,EAAE;IACrD,MAAMK,aAAa,GAAGN,YAAY,CAACC,IAAI,EAAEC,OAAO,CAAC;IACjD,IAAII,aAAa,KAAKP,IAAI,EAAE;MACxB,OAAOD,KAAK;IAChB;IACA,MAAM6D,aAAa,GAAG,SAAAA,CAAUtC,IAAI,EAAEtE,QAAQ,EAAE;MAC5C,OAAO,CAAC,CAACuD,aAAa,CAACe,IAAI,EAAEtE,QAAQ,CAAC;IAC1C,CAAC;IACD,IAAIuD,aAAa,CAACiB,YAAY,EAAE;MAC5BoC,aAAa,CAACpC,YAAY,GAAGjB,aAAa,CAACiB,YAAY;IAC3D;IACA,IAAIjB,aAAa,CAACkB,QAAQ,EAAE;MACxBmC,aAAa,CAACnC,QAAQ,GAAGlB,aAAa,CAACkB,QAAQ;IACnD;IACA,OAAOmC,aAAa;EACxB;EACA;EACA,OAAOC,gBAAgB,CAAC3D,IAAI,EAAEC,OAAO,CAAC;AAC1C;AACA,OAAO,SAASwD,iBAAiBA,CAACG,GAAG,EAAE;EACnC,MAAMC,EAAE,GAAGD,GAAG;EACd,IAAI,CAACC,EAAE,EAAE;IACL,OAAO,KAAK;EAChB;EACA,OAAO,OAAOA,EAAE,CAACxC,IAAI,KAAK,QAAQ,IAAI,OAAOwC,EAAE,CAAC/F,OAAO,KAAK,QAAQ;AACxE;AACA,SAAS6F,gBAAgBA,CAACG,UAAU,EAAE7D,OAAO,EAAE;EAC3C,MAAM4B,cAAc,GAAGC,wBAAwB,CAACiC,MAAM,CAACC,mBAAmB,CAACF,UAAU,CAAC,CACjF3E,GAAG,CAACrB,OAAO,IAAImG,sBAAsB,CAACnG,OAAO,EAAEgG,UAAU,CAAChG,OAAO,CAAC,EAAEmC,OAAO,CAAC,CAAC,CAC7EgC,MAAM,CAACnE,OAAO,IAAIA,OAAO,KAAKgC,IAAI,CAAC,CAAC;EACzC,MAAMoC,cAAc,GAAGL,cAAc,CAAChD,MAAM;EAC5C,IAAI,CAACqD,cAAc,EAAE;IACjB,OAAOpC,IAAI;EACf;EACA,IAAI,CAAC+B,cAAc,CAACqC,IAAI,CAAC7D,aAAa,IAAI,CAAC,CAACA,aAAa,CAAC8D,gBAAgB,CAAC,EAAE;IACzE,IAAIjC,cAAc,KAAK,CAAC,EAAE;MACtB,OAAOL,cAAc,CAAC,CAAC,CAAC;IAC5B;IACA,MAAMuC,gBAAgB,GAAG,SAAAA,CAAUhD,IAAI,EAAEtE,QAAQ,EAAE;MAC/C,IAAIuH,cAAc,GAAGb,SAAS;MAC9B,KAAK,IAAIrB,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGP,cAAc,CAAChD,MAAM,EAAEsD,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;QACnD,MAAMmC,MAAM,GAAGzC,cAAc,CAACM,CAAC,CAAC,CAACf,IAAI,EAAEtE,QAAQ,CAAC;QAChD,IAAI,OAAOwH,MAAM,KAAK,QAAQ,EAAE;UAC5B,OAAOA,MAAM,CAAC,CAAC;QACnB;QACA;QACA;QACA,IAAI3H,UAAU,CAAC2H,MAAM,CAAC,EAAE;UACpB,IAAI,CAACD,cAAc,EAAE;YACjBA,cAAc,GAAG,EAAE;UACvB;UACAA,cAAc,CAAChG,IAAI,CAACiG,MAAM,CAAC;QAC/B;MACJ;MACA;MACA;MACA,IAAID,cAAc,EAAE;QAChB,OAAOE,iBAAA,CAAC,aAAY;UAChB,KAAK,MAAMC,aAAa,IAAIH,cAAc,EAAE;YACxC,MAAMC,MAAM,SAASE,aAAa;YAClC,IAAI,OAAOF,MAAM,KAAK,QAAQ,EAAE;cAC5B,OAAOA,MAAM;YACjB;UACJ;UACA,OAAO,IAAI;QACf,CAAC,EAAE,CAAC;MACR;MACA,OAAO,IAAI;IACf,CAAC;IACD,MAAMjC,aAAa,GAAGR,cAAc,CAACS,IAAI,CAACxE,OAAO,IAAI,CAAC,CAACA,OAAO,CAACwD,YAAY,CAAC;IAC5E,IAAIe,aAAa,EAAE;MACf+B,gBAAgB,CAAC9C,YAAY,GAAGe,aAAa,CAACf,YAAY;IAC9D;IACA,MAAMC,QAAQ,GAAGM,cAAc,CAACU,MAAM,CAAC,CAACC,GAAG,EAAEC,OAAO,KAAKA,OAAO,CAAClB,QAAQ,GAAGiB,GAAG,CAACE,MAAM,CAACD,OAAO,CAAClB,QAAQ,CAAC,GAAGiB,GAAG,EAAE,EAAE,CAAC;IACnH,IAAIjB,QAAQ,CAAC1C,MAAM,EAAE;MACjBuF,gBAAgB,CAAC7C,QAAQ,GAAGA,QAAQ;IACxC;IACA,OAAO6C,gBAAgB;EAC3B;EACA,MAAMA,gBAAgB,GAAG,SAAAA,CAAUhD,IAAI,EAAEC,IAAI,EAAEiC,UAAU,EAAE;IACvD,IAAImB,IAAI,GAAGjB,SAAS;IACpB,IAAIa,cAAc,GAAGb,SAAS;IAC9B,KAAK,IAAIrB,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGP,cAAc,CAAChD,MAAM,EAAEsD,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MACnD;MACA,MAAM9B,aAAa,GAAGwB,cAAc,CAACM,CAAC,CAAC;MACvC,IAAI9B,aAAa,CAAC8D,gBAAgB,IAAIb,UAAU,EAAE;QAC9C,IAAI,CAACjC,IAAI,EAAE;UACPA,IAAI,GAAGvE,QAAQ,CAACsE,IAAI,CAAC;QACzB;QACA,IAAI,CAACqD,IAAI,EAAE;UACPA,IAAI,GAAGpD,IAAI,CAACV,MAAM,CAAC,CAAC,EAAEU,IAAI,CAACxC,MAAM,GAAG9B,OAAO,CAACqE,IAAI,CAAC,CAACvC,MAAM,CAAC;QAC7D;MACJ;MACA,MAAMyF,MAAM,GAAGjE,aAAa,CAACe,IAAI,EAAEC,IAAI,EAAEoD,IAAI,EAAEnB,UAAU,CAAC;MAC1D,IAAI,OAAOgB,MAAM,KAAK,QAAQ,EAAE;QAC5B,OAAOA,MAAM,CAAC,CAAC;MACnB;MACA;MACA;MACA,IAAI3H,UAAU,CAAC2H,MAAM,CAAC,EAAE;QACpB,IAAI,CAACD,cAAc,EAAE;UACjBA,cAAc,GAAG,EAAE;QACvB;QACAA,cAAc,CAAChG,IAAI,CAACiG,MAAM,CAAC;MAC/B;IACJ;IACA;IACA;IACA,IAAID,cAAc,EAAE;MAChB,OAAOE,iBAAA,CAAC,aAAY;QAChB,KAAK,MAAMC,aAAa,IAAIH,cAAc,EAAE;UACxC,MAAMC,MAAM,SAASE,aAAa;UAClC,IAAI,OAAOF,MAAM,KAAK,QAAQ,EAAE;YAC5B,OAAOA,MAAM;UACjB;QACJ;QACA,OAAO,IAAI;MACf,CAAC,EAAE,CAAC;IACR;IACA,OAAO,IAAI;EACf,CAAC;EACD,MAAMjC,aAAa,GAAGR,cAAc,CAACS,IAAI,CAACxE,OAAO,IAAI,CAAC,CAACA,OAAO,CAACwD,YAAY,CAAC;EAC5E,IAAIe,aAAa,EAAE;IACf+B,gBAAgB,CAAC9C,YAAY,GAAGe,aAAa,CAACf,YAAY;EAC9D;EACA,MAAMC,QAAQ,GAAGM,cAAc,CAACU,MAAM,CAAC,CAACC,GAAG,EAAEC,OAAO,KAAKA,OAAO,CAAClB,QAAQ,GAAGiB,GAAG,CAACE,MAAM,CAACD,OAAO,CAAClB,QAAQ,CAAC,GAAGiB,GAAG,EAAE,EAAE,CAAC;EACnH,IAAIjB,QAAQ,CAAC1C,MAAM,EAAE;IACjBuF,gBAAgB,CAAC7C,QAAQ,GAAGA,QAAQ;EACxC;EACA,OAAO6C,gBAAgB;AAC3B;AACA,SAASH,sBAAsBA,CAACnG,OAAO,EAAE4G,KAAK,EAAEzE,OAAO,EAAE;EACrD,IAAIyE,KAAK,KAAK,KAAK,EAAE;IACjB,OAAO5E,IAAI,CAAC,CAAC;EACjB;EACA,MAAMO,aAAa,GAAGN,YAAY,CAACjC,OAAO,EAAEmC,OAAO,CAAC;EACpD,IAAII,aAAa,KAAKP,IAAI,EAAE;IACxB,OAAOA,IAAI;EACf;EACA;EACA,IAAI,OAAO4E,KAAK,KAAK,SAAS,EAAE;IAC5B,OAAOrE,aAAa;EACxB;EACA;EACA,IAAIqE,KAAK,EAAE;IACP,MAAMC,IAAI,GAAGD,KAAK,CAACC,IAAI;IACvB,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;MAC1B,MAAML,MAAM,GAAGA,CAAClD,IAAI,EAAEtE,QAAQ,EAAE2H,IAAI,EAAEnB,UAAU,KAAK;QACjD,IAAI,CAACA,UAAU,IAAI,CAACjD,aAAa,CAACe,IAAI,EAAEtE,QAAQ,CAAC,EAAE;UAC/C,OAAO,IAAI;QACf;QACA,MAAM8H,aAAa,GAAGD,IAAI,CAAC5B,OAAO,CAAC,aAAa,EAAE,MAAM0B,IAAI,CAAC;QAC7D,MAAMI,OAAO,GAAGvB,UAAU,CAACsB,aAAa,CAAC;QACzC,OAAOjI,UAAU,CAACkI,OAAO,CAAC,GACtBA,OAAO,CAACC,IAAI,CAACtE,KAAK,IAAIA,KAAK,GAAG1C,OAAO,GAAG,IAAI,CAAC,GAC7C+G,OAAO,GAAG/G,OAAO,GAAG,IAAI;MAChC,CAAC;MACDwG,MAAM,CAACH,gBAAgB,GAAG,IAAI;MAC9B,OAAOG,MAAM;IACjB;EACJ;EACA;EACA,OAAOjE,aAAa;AACxB;AACA,SAASyB,wBAAwBA,CAACD,cAAc,EAAEyC,MAAM,EAAE;EACtD,MAAMS,gBAAgB,GAAGlD,cAAc,CAACI,MAAM,CAAC5B,aAAa,IAAI,CAAC,CAACA,aAAa,CAACmB,SAAS,CAAC;EAC1F,IAAIuD,gBAAgB,CAAClG,MAAM,GAAG,CAAC,EAAE;IAC7B,OAAOgD,cAAc;EACzB;EACA,MAAML,SAAS,GAAGuD,gBAAgB,CAACxC,MAAM,CAAC,CAACC,GAAG,EAAEC,OAAO,KAAK;IACxD,MAAMjB,SAAS,GAAGiB,OAAO,CAACjB,SAAS;IACnC,OAAOA,SAAS,GAAGgB,GAAG,CAACE,MAAM,CAAClB,SAAS,CAAC,GAAGgB,GAAG;EAClD,CAAC,EAAE,EAAE,CAAC;EACN,IAAIf,QAAQ;EACZ,IAAI6C,MAAM,EAAE;IACR7C,QAAQ,GAAG,EAAE;IACb,KAAK,IAAIU,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGZ,SAAS,CAAC3C,MAAM,EAAEsD,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MAC9CV,QAAQ,CAACpD,IAAI,CAACiG,MAAM,CAAC;IACzB;EACJ,CAAC,MACI;IACD7C,QAAQ,GAAGsD,gBAAgB,CAACxC,MAAM,CAAC,CAACC,GAAG,EAAEC,OAAO,KAAK;MACjD,MAAMhB,QAAQ,GAAGgB,OAAO,CAAChB,QAAQ;MACjC,OAAOA,QAAQ,GAAGe,GAAG,CAACE,MAAM,CAACjB,QAAQ,CAAC,GAAGe,GAAG;IAChD,CAAC,EAAE,EAAE,CAAC;EACV;EACA,MAAMwC,SAAS,GAAG,SAAAA,CAAU5D,IAAI,EAAEtE,QAAQ,EAAE;IACxC,IAAI,OAAOsE,IAAI,KAAK,QAAQ,EAAE;MAC1B,OAAO,IAAI;IACf;IACA,IAAI,CAACtE,QAAQ,EAAE;MACX,IAAIqF,CAAC;MACL,KAAKA,CAAC,GAAGf,IAAI,CAACvC,MAAM,EAAEsD,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC9B,MAAM8C,EAAE,GAAG7D,IAAI,CAAC8D,UAAU,CAAC/C,CAAC,GAAG,CAAC,CAAC;QACjC,IAAI8C,EAAE,KAAK,EAAE,CAAC,wBAAwBA,EAAE,KAAK,EAAE,CAAC,0BAA0B;UACtE;QACJ;MACJ;MACAnI,QAAQ,GAAGsE,IAAI,CAACT,MAAM,CAACwB,CAAC,CAAC;IAC7B;IACA,MAAMvD,KAAK,GAAG4C,SAAS,CAAC2D,OAAO,CAACrI,QAAQ,CAAC;IACzC,OAAO8B,KAAK,KAAK,CAAC,CAAC,GAAG6C,QAAQ,CAAC7C,KAAK,CAAC,GAAG,IAAI;EAChD,CAAC;EACDoG,SAAS,CAACxD,SAAS,GAAGA,SAAS;EAC/BwD,SAAS,CAACvD,QAAQ,GAAGA,QAAQ;EAC7BuD,SAAS,CAAC1D,YAAY,GAAGE,SAAS;EAClC,MAAM4D,kBAAkB,GAAGvD,cAAc,CAACI,MAAM,CAAC5B,aAAa,IAAI,CAACA,aAAa,CAACmB,SAAS,CAAC;EAC3F4D,kBAAkB,CAAC/G,IAAI,CAAC2G,SAAS,CAAC;EAClC,OAAOI,kBAAkB;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}