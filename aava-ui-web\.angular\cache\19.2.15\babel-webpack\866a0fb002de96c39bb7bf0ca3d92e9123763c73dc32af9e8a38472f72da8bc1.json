{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { lengthAdd, lengthZero, lengthLessThan } from './length.js';\n/**\n * Allows to efficiently find a longest child at a given offset in a fixed node.\n * The requested offsets must increase monotonously.\n*/\nexport class NodeReader {\n  constructor(node) {\n    this.lastOffset = lengthZero;\n    this.nextNodes = [node];\n    this.offsets = [lengthZero];\n    this.idxs = [];\n  }\n  /**\n   * Returns the longest node at `offset` that satisfies the predicate.\n   * @param offset must be greater than or equal to the last offset this method has been called with!\n  */\n  readLongestNodeAt(offset, predicate) {\n    if (lengthLessThan(offset, this.lastOffset)) {\n      throw new Error('Invalid offset');\n    }\n    this.lastOffset = offset;\n    // Find the longest node of all those that are closest to the current offset.\n    while (true) {\n      const curNode = lastOrUndefined(this.nextNodes);\n      if (!curNode) {\n        return undefined;\n      }\n      const curNodeOffset = lastOrUndefined(this.offsets);\n      if (lengthLessThan(offset, curNodeOffset)) {\n        // The next best node is not here yet.\n        // The reader must advance before a cached node is hit.\n        return undefined;\n      }\n      if (lengthLessThan(curNodeOffset, offset)) {\n        // The reader is ahead of the current node.\n        if (lengthAdd(curNodeOffset, curNode.length) <= offset) {\n          // The reader is after the end of the current node.\n          this.nextNodeAfterCurrent();\n        } else {\n          // The reader is somewhere in the current node.\n          const nextChildIdx = getNextChildIdx(curNode);\n          if (nextChildIdx !== -1) {\n            // Go to the first child and repeat.\n            this.nextNodes.push(curNode.getChild(nextChildIdx));\n            this.offsets.push(curNodeOffset);\n            this.idxs.push(nextChildIdx);\n          } else {\n            // We don't have children\n            this.nextNodeAfterCurrent();\n          }\n        }\n      } else {\n        // readerOffsetBeforeChange === curNodeOffset\n        if (predicate(curNode)) {\n          this.nextNodeAfterCurrent();\n          return curNode;\n        } else {\n          const nextChildIdx = getNextChildIdx(curNode);\n          // look for shorter node\n          if (nextChildIdx === -1) {\n            // There is no shorter node.\n            this.nextNodeAfterCurrent();\n            return undefined;\n          } else {\n            // Descend into first child & repeat.\n            this.nextNodes.push(curNode.getChild(nextChildIdx));\n            this.offsets.push(curNodeOffset);\n            this.idxs.push(nextChildIdx);\n          }\n        }\n      }\n    }\n  }\n  // Navigates to the longest node that continues after the current node.\n  nextNodeAfterCurrent() {\n    while (true) {\n      const currentOffset = lastOrUndefined(this.offsets);\n      const currentNode = lastOrUndefined(this.nextNodes);\n      this.nextNodes.pop();\n      this.offsets.pop();\n      if (this.idxs.length === 0) {\n        // We just popped the root node, there is no next node.\n        break;\n      }\n      // Parent is not undefined, because idxs is not empty\n      const parent = lastOrUndefined(this.nextNodes);\n      const nextChildIdx = getNextChildIdx(parent, this.idxs[this.idxs.length - 1]);\n      if (nextChildIdx !== -1) {\n        this.nextNodes.push(parent.getChild(nextChildIdx));\n        this.offsets.push(lengthAdd(currentOffset, currentNode.length));\n        this.idxs[this.idxs.length - 1] = nextChildIdx;\n        break;\n      } else {\n        this.idxs.pop();\n      }\n      // We fully consumed the parent.\n      // Current node is now parent, so call nextNodeAfterCurrent again\n    }\n  }\n}\nfunction getNextChildIdx(node, curIdx = -1) {\n  while (true) {\n    curIdx++;\n    if (curIdx >= node.childrenLength) {\n      return -1;\n    }\n    if (node.getChild(curIdx)) {\n      return curIdx;\n    }\n  }\n}\nfunction lastOrUndefined(arr) {\n  return arr.length > 0 ? arr[arr.length - 1] : undefined;\n}", "map": {"version": 3, "names": ["lengthAdd", "lengthZero", "lengthLessThan", "NodeReader", "constructor", "node", "lastOffset", "nextNodes", "offsets", "idxs", "readLongestNodeAt", "offset", "predicate", "Error", "curNode", "lastOrUndefined", "undefined", "curNodeOffset", "length", "nextNodeAfterCurrent", "nextChildIdx", "getNextChildIdx", "push", "<PERSON><PERSON><PERSON><PERSON>", "currentOffset", "currentNode", "pop", "parent", "curIdx", "<PERSON><PERSON><PERSON><PERSON>", "arr"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/nodeReader.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { lengthAdd, lengthZero, lengthLessThan } from './length.js';\n/**\n * Allows to efficiently find a longest child at a given offset in a fixed node.\n * The requested offsets must increase monotonously.\n*/\nexport class NodeReader {\n    constructor(node) {\n        this.lastOffset = lengthZero;\n        this.nextNodes = [node];\n        this.offsets = [lengthZero];\n        this.idxs = [];\n    }\n    /**\n     * Returns the longest node at `offset` that satisfies the predicate.\n     * @param offset must be greater than or equal to the last offset this method has been called with!\n    */\n    readLongestNodeAt(offset, predicate) {\n        if (lengthLessThan(offset, this.lastOffset)) {\n            throw new Error('Invalid offset');\n        }\n        this.lastOffset = offset;\n        // Find the longest node of all those that are closest to the current offset.\n        while (true) {\n            const curNode = lastOrUndefined(this.nextNodes);\n            if (!curNode) {\n                return undefined;\n            }\n            const curNodeOffset = lastOrUndefined(this.offsets);\n            if (lengthLessThan(offset, curNodeOffset)) {\n                // The next best node is not here yet.\n                // The reader must advance before a cached node is hit.\n                return undefined;\n            }\n            if (lengthLessThan(curNodeOffset, offset)) {\n                // The reader is ahead of the current node.\n                if (lengthAdd(curNodeOffset, curNode.length) <= offset) {\n                    // The reader is after the end of the current node.\n                    this.nextNodeAfterCurrent();\n                }\n                else {\n                    // The reader is somewhere in the current node.\n                    const nextChildIdx = getNextChildIdx(curNode);\n                    if (nextChildIdx !== -1) {\n                        // Go to the first child and repeat.\n                        this.nextNodes.push(curNode.getChild(nextChildIdx));\n                        this.offsets.push(curNodeOffset);\n                        this.idxs.push(nextChildIdx);\n                    }\n                    else {\n                        // We don't have children\n                        this.nextNodeAfterCurrent();\n                    }\n                }\n            }\n            else {\n                // readerOffsetBeforeChange === curNodeOffset\n                if (predicate(curNode)) {\n                    this.nextNodeAfterCurrent();\n                    return curNode;\n                }\n                else {\n                    const nextChildIdx = getNextChildIdx(curNode);\n                    // look for shorter node\n                    if (nextChildIdx === -1) {\n                        // There is no shorter node.\n                        this.nextNodeAfterCurrent();\n                        return undefined;\n                    }\n                    else {\n                        // Descend into first child & repeat.\n                        this.nextNodes.push(curNode.getChild(nextChildIdx));\n                        this.offsets.push(curNodeOffset);\n                        this.idxs.push(nextChildIdx);\n                    }\n                }\n            }\n        }\n    }\n    // Navigates to the longest node that continues after the current node.\n    nextNodeAfterCurrent() {\n        while (true) {\n            const currentOffset = lastOrUndefined(this.offsets);\n            const currentNode = lastOrUndefined(this.nextNodes);\n            this.nextNodes.pop();\n            this.offsets.pop();\n            if (this.idxs.length === 0) {\n                // We just popped the root node, there is no next node.\n                break;\n            }\n            // Parent is not undefined, because idxs is not empty\n            const parent = lastOrUndefined(this.nextNodes);\n            const nextChildIdx = getNextChildIdx(parent, this.idxs[this.idxs.length - 1]);\n            if (nextChildIdx !== -1) {\n                this.nextNodes.push(parent.getChild(nextChildIdx));\n                this.offsets.push(lengthAdd(currentOffset, currentNode.length));\n                this.idxs[this.idxs.length - 1] = nextChildIdx;\n                break;\n            }\n            else {\n                this.idxs.pop();\n            }\n            // We fully consumed the parent.\n            // Current node is now parent, so call nextNodeAfterCurrent again\n        }\n    }\n}\nfunction getNextChildIdx(node, curIdx = -1) {\n    while (true) {\n        curIdx++;\n        if (curIdx >= node.childrenLength) {\n            return -1;\n        }\n        if (node.getChild(curIdx)) {\n            return curIdx;\n        }\n    }\n}\nfunction lastOrUndefined(arr) {\n    return arr.length > 0 ? arr[arr.length - 1] : undefined;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,SAAS,EAAEC,UAAU,EAAEC,cAAc,QAAQ,aAAa;AACnE;AACA;AACA;AACA;AACA,OAAO,MAAMC,UAAU,CAAC;EACpBC,WAAWA,CAACC,IAAI,EAAE;IACd,IAAI,CAACC,UAAU,GAAGL,UAAU;IAC5B,IAAI,CAACM,SAAS,GAAG,CAACF,IAAI,CAAC;IACvB,IAAI,CAACG,OAAO,GAAG,CAACP,UAAU,CAAC;IAC3B,IAAI,CAACQ,IAAI,GAAG,EAAE;EAClB;EACA;AACJ;AACA;AACA;EACIC,iBAAiBA,CAACC,MAAM,EAAEC,SAAS,EAAE;IACjC,IAAIV,cAAc,CAACS,MAAM,EAAE,IAAI,CAACL,UAAU,CAAC,EAAE;MACzC,MAAM,IAAIO,KAAK,CAAC,gBAAgB,CAAC;IACrC;IACA,IAAI,CAACP,UAAU,GAAGK,MAAM;IACxB;IACA,OAAO,IAAI,EAAE;MACT,MAAMG,OAAO,GAAGC,eAAe,CAAC,IAAI,CAACR,SAAS,CAAC;MAC/C,IAAI,CAACO,OAAO,EAAE;QACV,OAAOE,SAAS;MACpB;MACA,MAAMC,aAAa,GAAGF,eAAe,CAAC,IAAI,CAACP,OAAO,CAAC;MACnD,IAAIN,cAAc,CAACS,MAAM,EAAEM,aAAa,CAAC,EAAE;QACvC;QACA;QACA,OAAOD,SAAS;MACpB;MACA,IAAId,cAAc,CAACe,aAAa,EAAEN,MAAM,CAAC,EAAE;QACvC;QACA,IAAIX,SAAS,CAACiB,aAAa,EAAEH,OAAO,CAACI,MAAM,CAAC,IAAIP,MAAM,EAAE;UACpD;UACA,IAAI,CAACQ,oBAAoB,CAAC,CAAC;QAC/B,CAAC,MACI;UACD;UACA,MAAMC,YAAY,GAAGC,eAAe,CAACP,OAAO,CAAC;UAC7C,IAAIM,YAAY,KAAK,CAAC,CAAC,EAAE;YACrB;YACA,IAAI,CAACb,SAAS,CAACe,IAAI,CAACR,OAAO,CAACS,QAAQ,CAACH,YAAY,CAAC,CAAC;YACnD,IAAI,CAACZ,OAAO,CAACc,IAAI,CAACL,aAAa,CAAC;YAChC,IAAI,CAACR,IAAI,CAACa,IAAI,CAACF,YAAY,CAAC;UAChC,CAAC,MACI;YACD;YACA,IAAI,CAACD,oBAAoB,CAAC,CAAC;UAC/B;QACJ;MACJ,CAAC,MACI;QACD;QACA,IAAIP,SAAS,CAACE,OAAO,CAAC,EAAE;UACpB,IAAI,CAACK,oBAAoB,CAAC,CAAC;UAC3B,OAAOL,OAAO;QAClB,CAAC,MACI;UACD,MAAMM,YAAY,GAAGC,eAAe,CAACP,OAAO,CAAC;UAC7C;UACA,IAAIM,YAAY,KAAK,CAAC,CAAC,EAAE;YACrB;YACA,IAAI,CAACD,oBAAoB,CAAC,CAAC;YAC3B,OAAOH,SAAS;UACpB,CAAC,MACI;YACD;YACA,IAAI,CAACT,SAAS,CAACe,IAAI,CAACR,OAAO,CAACS,QAAQ,CAACH,YAAY,CAAC,CAAC;YACnD,IAAI,CAACZ,OAAO,CAACc,IAAI,CAACL,aAAa,CAAC;YAChC,IAAI,CAACR,IAAI,CAACa,IAAI,CAACF,YAAY,CAAC;UAChC;QACJ;MACJ;IACJ;EACJ;EACA;EACAD,oBAAoBA,CAAA,EAAG;IACnB,OAAO,IAAI,EAAE;MACT,MAAMK,aAAa,GAAGT,eAAe,CAAC,IAAI,CAACP,OAAO,CAAC;MACnD,MAAMiB,WAAW,GAAGV,eAAe,CAAC,IAAI,CAACR,SAAS,CAAC;MACnD,IAAI,CAACA,SAAS,CAACmB,GAAG,CAAC,CAAC;MACpB,IAAI,CAAClB,OAAO,CAACkB,GAAG,CAAC,CAAC;MAClB,IAAI,IAAI,CAACjB,IAAI,CAACS,MAAM,KAAK,CAAC,EAAE;QACxB;QACA;MACJ;MACA;MACA,MAAMS,MAAM,GAAGZ,eAAe,CAAC,IAAI,CAACR,SAAS,CAAC;MAC9C,MAAMa,YAAY,GAAGC,eAAe,CAACM,MAAM,EAAE,IAAI,CAAClB,IAAI,CAAC,IAAI,CAACA,IAAI,CAACS,MAAM,GAAG,CAAC,CAAC,CAAC;MAC7E,IAAIE,YAAY,KAAK,CAAC,CAAC,EAAE;QACrB,IAAI,CAACb,SAAS,CAACe,IAAI,CAACK,MAAM,CAACJ,QAAQ,CAACH,YAAY,CAAC,CAAC;QAClD,IAAI,CAACZ,OAAO,CAACc,IAAI,CAACtB,SAAS,CAACwB,aAAa,EAAEC,WAAW,CAACP,MAAM,CAAC,CAAC;QAC/D,IAAI,CAACT,IAAI,CAAC,IAAI,CAACA,IAAI,CAACS,MAAM,GAAG,CAAC,CAAC,GAAGE,YAAY;QAC9C;MACJ,CAAC,MACI;QACD,IAAI,CAACX,IAAI,CAACiB,GAAG,CAAC,CAAC;MACnB;MACA;MACA;IACJ;EACJ;AACJ;AACA,SAASL,eAAeA,CAAChB,IAAI,EAAEuB,MAAM,GAAG,CAAC,CAAC,EAAE;EACxC,OAAO,IAAI,EAAE;IACTA,MAAM,EAAE;IACR,IAAIA,MAAM,IAAIvB,IAAI,CAACwB,cAAc,EAAE;MAC/B,OAAO,CAAC,CAAC;IACb;IACA,IAAIxB,IAAI,CAACkB,QAAQ,CAACK,MAAM,CAAC,EAAE;MACvB,OAAOA,MAAM;IACjB;EACJ;AACJ;AACA,SAASb,eAAeA,CAACe,GAAG,EAAE;EAC1B,OAAOA,GAAG,CAACZ,MAAM,GAAG,CAAC,GAAGY,GAAG,CAACA,GAAG,CAACZ,MAAM,GAAG,CAAC,CAAC,GAAGF,SAAS;AAC3D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}