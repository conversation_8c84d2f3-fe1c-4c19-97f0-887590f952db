{"ast": null, "code": "import _asyncToGenerator from \"C:/console/aava-ui-web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as dom from '../../../../base/browser/dom.js';\nimport { Action, Separator } from '../../../../base/common/actions.js';\nimport { CancellationToken } from '../../../../base/common/cancellation.js';\nimport { generateUuid } from '../../../../base/common/uuid.js';\nimport { Range } from '../../../common/core/range.js';\nimport { ITextModelService } from '../../../common/services/resolverService.js';\nimport { DefinitionAction, SymbolNavigationAction, SymbolNavigationAnchor } from '../../gotoSymbol/browser/goToCommands.js';\nimport { PeekContext } from '../../peekView/browser/peekView.js';\nimport { isIMenuItem, MenuId, MenuItemAction, MenuRegistry } from '../../../../platform/actions/common/actions.js';\nimport { ICommandService } from '../../../../platform/commands/common/commands.js';\nimport { IContextKeyService } from '../../../../platform/contextkey/common/contextkey.js';\nimport { IContextMenuService } from '../../../../platform/contextview/browser/contextView.js';\nimport { IInstantiationService } from '../../../../platform/instantiation/common/instantiation.js';\nimport { INotificationService, Severity } from '../../../../platform/notification/common/notification.js';\nexport function showGoToContextMenu(_x, _x2, _x3, _x4) {\n  return _showGoToContextMenu.apply(this, arguments);\n}\nfunction _showGoToContextMenu() {\n  _showGoToContextMenu = _asyncToGenerator(function* (accessor, editor, anchor, part) {\n    const resolverService = accessor.get(ITextModelService);\n    const contextMenuService = accessor.get(IContextMenuService);\n    const commandService = accessor.get(ICommandService);\n    const instaService = accessor.get(IInstantiationService);\n    const notificationService = accessor.get(INotificationService);\n    yield part.item.resolve(CancellationToken.None);\n    if (!part.part.location) {\n      return;\n    }\n    const location = part.part.location;\n    const menuActions = [];\n    // from all registered (not active) context menu actions select those\n    // that are a symbol navigation actions\n    const filter = new Set(MenuRegistry.getMenuItems(MenuId.EditorContext).map(item => isIMenuItem(item) ? item.command.id : generateUuid()));\n    for (const delegate of SymbolNavigationAction.all()) {\n      if (filter.has(delegate.desc.id)) {\n        menuActions.push(new Action(delegate.desc.id, MenuItemAction.label(delegate.desc, {\n          renderShortTitle: true\n        }), undefined, true, /*#__PURE__*/_asyncToGenerator(function* () {\n          const ref = yield resolverService.createModelReference(location.uri);\n          try {\n            const symbolAnchor = new SymbolNavigationAnchor(ref.object.textEditorModel, Range.getStartPosition(location.range));\n            const range = part.item.anchor.range;\n            yield instaService.invokeFunction(delegate.runEditorCommand.bind(delegate), editor, symbolAnchor, range);\n          } finally {\n            ref.dispose();\n          }\n        })));\n      }\n    }\n    if (part.part.command) {\n      const {\n        command\n      } = part.part;\n      menuActions.push(new Separator());\n      menuActions.push(new Action(command.id, command.title, undefined, true, /*#__PURE__*/_asyncToGenerator(function* () {\n        try {\n          yield commandService.executeCommand(command.id, ...(command.arguments ?? []));\n        } catch (err) {\n          notificationService.notify({\n            severity: Severity.Error,\n            source: part.item.provider.displayName,\n            message: err\n          });\n        }\n      })));\n    }\n    // show context menu\n    const useShadowDOM = editor.getOption(128 /* EditorOption.useShadowDOM */);\n    contextMenuService.showContextMenu({\n      domForShadowRoot: useShadowDOM ? editor.getDomNode() ?? undefined : undefined,\n      getAnchor: () => {\n        const box = dom.getDomNodePagePosition(anchor);\n        return {\n          x: box.left,\n          y: box.top + box.height + 8\n        };\n      },\n      getActions: () => menuActions,\n      onHide: () => {\n        editor.focus();\n      },\n      autoSelectFirstItem: true\n    });\n  });\n  return _showGoToContextMenu.apply(this, arguments);\n}\nexport function goToDefinitionWithLocation(_x5, _x6, _x7, _x8) {\n  return _goToDefinitionWithLocation.apply(this, arguments);\n}\nfunction _goToDefinitionWithLocation() {\n  _goToDefinitionWithLocation = _asyncToGenerator(function* (accessor, event, editor, location) {\n    const resolverService = accessor.get(ITextModelService);\n    const ref = yield resolverService.createModelReference(location.uri);\n    yield editor.invokeWithinContext(/*#__PURE__*/function () {\n      var _ref3 = _asyncToGenerator(function* (accessor) {\n        const openToSide = event.hasSideBySideModifier;\n        const contextKeyService = accessor.get(IContextKeyService);\n        const isInPeek = PeekContext.inPeekEditor.getValue(contextKeyService);\n        const canPeek = !openToSide && editor.getOption(89 /* EditorOption.definitionLinkOpensInPeek */) && !isInPeek;\n        const action = new DefinitionAction({\n          openToSide,\n          openInPeek: canPeek,\n          muteMessage: true\n        }, {\n          title: {\n            value: '',\n            original: ''\n          },\n          id: '',\n          precondition: undefined\n        });\n        return action.run(accessor, new SymbolNavigationAnchor(ref.object.textEditorModel, Range.getStartPosition(location.range)), Range.lift(location.range));\n      });\n      return function (_x9) {\n        return _ref3.apply(this, arguments);\n      };\n    }());\n    ref.dispose();\n  });\n  return _goToDefinitionWithLocation.apply(this, arguments);\n}", "map": {"version": 3, "names": ["dom", "Action", "Separator", "CancellationToken", "generateUuid", "Range", "ITextModelService", "DefinitionAction", "SymbolNavigationAction", "SymbolNavigationAnchor", "PeekContext", "isIMenuItem", "MenuId", "MenuItemAction", "MenuRegistry", "ICommandService", "IContextKeyService", "IContextMenuService", "IInstantiationService", "INotificationService", "Severity", "showGoToContextMenu", "_x", "_x2", "_x3", "_x4", "_showGoToContextMenu", "apply", "arguments", "_asyncToGenerator", "accessor", "editor", "anchor", "part", "resolverService", "get", "contextMenuService", "commandService", "instaService", "notificationService", "item", "resolve", "None", "location", "menuActions", "filter", "Set", "getMenuItems", "EditorContext", "map", "command", "id", "delegate", "all", "has", "desc", "push", "label", "renderShortTitle", "undefined", "ref", "createModelReference", "uri", "symbolAnchor", "object", "textEditorModel", "getStartPosition", "range", "invokeFunction", "runEditorCommand", "bind", "dispose", "title", "executeCommand", "err", "notify", "severity", "Error", "source", "provider", "displayName", "message", "useShadowDOM", "getOption", "showContextMenu", "domForShadowRoot", "getDomNode", "getAnchor", "box", "getDomNodePagePosition", "x", "left", "y", "top", "height", "getActions", "onHide", "focus", "autoSelectFirstItem", "goToDefinitionWithLocation", "_x5", "_x6", "_x7", "_x8", "_goToDefinitionWithLocation", "event", "invokeWithinContext", "_ref3", "openToSide", "hasSideBySideModifier", "contextKeyService", "isInPeek", "inPeekEditor", "getValue", "canPeek", "action", "openInPeek", "muteMessage", "value", "original", "precondition", "run", "lift", "_x9"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/contrib/inlayHints/browser/inlayHintsLocations.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as dom from '../../../../base/browser/dom.js';\nimport { Action, Separator } from '../../../../base/common/actions.js';\nimport { CancellationToken } from '../../../../base/common/cancellation.js';\nimport { generateUuid } from '../../../../base/common/uuid.js';\nimport { Range } from '../../../common/core/range.js';\nimport { ITextModelService } from '../../../common/services/resolverService.js';\nimport { DefinitionAction, SymbolNavigationAction, SymbolNavigationAnchor } from '../../gotoSymbol/browser/goToCommands.js';\nimport { PeekContext } from '../../peekView/browser/peekView.js';\nimport { isIMenuItem, MenuId, MenuItemAction, MenuRegistry } from '../../../../platform/actions/common/actions.js';\nimport { ICommandService } from '../../../../platform/commands/common/commands.js';\nimport { IContextKeyService } from '../../../../platform/contextkey/common/contextkey.js';\nimport { IContextMenuService } from '../../../../platform/contextview/browser/contextView.js';\nimport { IInstantiationService } from '../../../../platform/instantiation/common/instantiation.js';\nimport { INotificationService, Severity } from '../../../../platform/notification/common/notification.js';\nexport async function showGoToContextMenu(accessor, editor, anchor, part) {\n    const resolverService = accessor.get(ITextModelService);\n    const contextMenuService = accessor.get(IContextMenuService);\n    const commandService = accessor.get(ICommandService);\n    const instaService = accessor.get(IInstantiationService);\n    const notificationService = accessor.get(INotificationService);\n    await part.item.resolve(CancellationToken.None);\n    if (!part.part.location) {\n        return;\n    }\n    const location = part.part.location;\n    const menuActions = [];\n    // from all registered (not active) context menu actions select those\n    // that are a symbol navigation actions\n    const filter = new Set(MenuRegistry.getMenuItems(MenuId.EditorContext)\n        .map(item => isIMenuItem(item) ? item.command.id : generateUuid()));\n    for (const delegate of SymbolNavigationAction.all()) {\n        if (filter.has(delegate.desc.id)) {\n            menuActions.push(new Action(delegate.desc.id, MenuItemAction.label(delegate.desc, { renderShortTitle: true }), undefined, true, async () => {\n                const ref = await resolverService.createModelReference(location.uri);\n                try {\n                    const symbolAnchor = new SymbolNavigationAnchor(ref.object.textEditorModel, Range.getStartPosition(location.range));\n                    const range = part.item.anchor.range;\n                    await instaService.invokeFunction(delegate.runEditorCommand.bind(delegate), editor, symbolAnchor, range);\n                }\n                finally {\n                    ref.dispose();\n                }\n            }));\n        }\n    }\n    if (part.part.command) {\n        const { command } = part.part;\n        menuActions.push(new Separator());\n        menuActions.push(new Action(command.id, command.title, undefined, true, async () => {\n            try {\n                await commandService.executeCommand(command.id, ...(command.arguments ?? []));\n            }\n            catch (err) {\n                notificationService.notify({\n                    severity: Severity.Error,\n                    source: part.item.provider.displayName,\n                    message: err\n                });\n            }\n        }));\n    }\n    // show context menu\n    const useShadowDOM = editor.getOption(128 /* EditorOption.useShadowDOM */);\n    contextMenuService.showContextMenu({\n        domForShadowRoot: useShadowDOM ? editor.getDomNode() ?? undefined : undefined,\n        getAnchor: () => {\n            const box = dom.getDomNodePagePosition(anchor);\n            return { x: box.left, y: box.top + box.height + 8 };\n        },\n        getActions: () => menuActions,\n        onHide: () => {\n            editor.focus();\n        },\n        autoSelectFirstItem: true,\n    });\n}\nexport async function goToDefinitionWithLocation(accessor, event, editor, location) {\n    const resolverService = accessor.get(ITextModelService);\n    const ref = await resolverService.createModelReference(location.uri);\n    await editor.invokeWithinContext(async (accessor) => {\n        const openToSide = event.hasSideBySideModifier;\n        const contextKeyService = accessor.get(IContextKeyService);\n        const isInPeek = PeekContext.inPeekEditor.getValue(contextKeyService);\n        const canPeek = !openToSide && editor.getOption(89 /* EditorOption.definitionLinkOpensInPeek */) && !isInPeek;\n        const action = new DefinitionAction({ openToSide, openInPeek: canPeek, muteMessage: true }, { title: { value: '', original: '' }, id: '', precondition: undefined });\n        return action.run(accessor, new SymbolNavigationAnchor(ref.object.textEditorModel, Range.getStartPosition(location.range)), Range.lift(location.range));\n    });\n    ref.dispose();\n}\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA,OAAO,KAAKA,GAAG,MAAM,iCAAiC;AACtD,SAASC,MAAM,EAAEC,SAAS,QAAQ,oCAAoC;AACtE,SAASC,iBAAiB,QAAQ,yCAAyC;AAC3E,SAASC,YAAY,QAAQ,iCAAiC;AAC9D,SAASC,KAAK,QAAQ,+BAA+B;AACrD,SAASC,iBAAiB,QAAQ,6CAA6C;AAC/E,SAASC,gBAAgB,EAAEC,sBAAsB,EAAEC,sBAAsB,QAAQ,0CAA0C;AAC3H,SAASC,WAAW,QAAQ,oCAAoC;AAChE,SAASC,WAAW,EAAEC,MAAM,EAAEC,cAAc,EAAEC,YAAY,QAAQ,gDAAgD;AAClH,SAASC,eAAe,QAAQ,kDAAkD;AAClF,SAASC,kBAAkB,QAAQ,sDAAsD;AACzF,SAASC,mBAAmB,QAAQ,yDAAyD;AAC7F,SAASC,qBAAqB,QAAQ,4DAA4D;AAClG,SAASC,oBAAoB,EAAEC,QAAQ,QAAQ,0DAA0D;AACzG,gBAAsBC,mBAAmBA,CAAAC,EAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;EAAA,OAAAC,oBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AA6DxC,SAAAF,qBAAA;EAAAA,oBAAA,GAAAG,iBAAA,CA7DM,WAAmCC,QAAQ,EAAEC,MAAM,EAAEC,MAAM,EAAEC,IAAI,EAAE;IACtE,MAAMC,eAAe,GAAGJ,QAAQ,CAACK,GAAG,CAAC7B,iBAAiB,CAAC;IACvD,MAAM8B,kBAAkB,GAAGN,QAAQ,CAACK,GAAG,CAAClB,mBAAmB,CAAC;IAC5D,MAAMoB,cAAc,GAAGP,QAAQ,CAACK,GAAG,CAACpB,eAAe,CAAC;IACpD,MAAMuB,YAAY,GAAGR,QAAQ,CAACK,GAAG,CAACjB,qBAAqB,CAAC;IACxD,MAAMqB,mBAAmB,GAAGT,QAAQ,CAACK,GAAG,CAAChB,oBAAoB,CAAC;IAC9D,MAAMc,IAAI,CAACO,IAAI,CAACC,OAAO,CAACtC,iBAAiB,CAACuC,IAAI,CAAC;IAC/C,IAAI,CAACT,IAAI,CAACA,IAAI,CAACU,QAAQ,EAAE;MACrB;IACJ;IACA,MAAMA,QAAQ,GAAGV,IAAI,CAACA,IAAI,CAACU,QAAQ;IACnC,MAAMC,WAAW,GAAG,EAAE;IACtB;IACA;IACA,MAAMC,MAAM,GAAG,IAAIC,GAAG,CAAChC,YAAY,CAACiC,YAAY,CAACnC,MAAM,CAACoC,aAAa,CAAC,CACjEC,GAAG,CAACT,IAAI,IAAI7B,WAAW,CAAC6B,IAAI,CAAC,GAAGA,IAAI,CAACU,OAAO,CAACC,EAAE,GAAG/C,YAAY,CAAC,CAAC,CAAC,CAAC;IACvE,KAAK,MAAMgD,QAAQ,IAAI5C,sBAAsB,CAAC6C,GAAG,CAAC,CAAC,EAAE;MACjD,IAAIR,MAAM,CAACS,GAAG,CAACF,QAAQ,CAACG,IAAI,CAACJ,EAAE,CAAC,EAAE;QAC9BP,WAAW,CAACY,IAAI,CAAC,IAAIvD,MAAM,CAACmD,QAAQ,CAACG,IAAI,CAACJ,EAAE,EAAEtC,cAAc,CAAC4C,KAAK,CAACL,QAAQ,CAACG,IAAI,EAAE;UAAEG,gBAAgB,EAAE;QAAK,CAAC,CAAC,EAAEC,SAAS,EAAE,IAAI,eAAA9B,iBAAA,CAAE,aAAY;UACxI,MAAM+B,GAAG,SAAS1B,eAAe,CAAC2B,oBAAoB,CAAClB,QAAQ,CAACmB,GAAG,CAAC;UACpE,IAAI;YACA,MAAMC,YAAY,GAAG,IAAItD,sBAAsB,CAACmD,GAAG,CAACI,MAAM,CAACC,eAAe,EAAE5D,KAAK,CAAC6D,gBAAgB,CAACvB,QAAQ,CAACwB,KAAK,CAAC,CAAC;YACnH,MAAMA,KAAK,GAAGlC,IAAI,CAACO,IAAI,CAACR,MAAM,CAACmC,KAAK;YACpC,MAAM7B,YAAY,CAAC8B,cAAc,CAAChB,QAAQ,CAACiB,gBAAgB,CAACC,IAAI,CAAClB,QAAQ,CAAC,EAAErB,MAAM,EAAEgC,YAAY,EAAEI,KAAK,CAAC;UAC5G,CAAC,SACO;YACJP,GAAG,CAACW,OAAO,CAAC,CAAC;UACjB;QACJ,CAAC,EAAC,CAAC;MACP;IACJ;IACA,IAAItC,IAAI,CAACA,IAAI,CAACiB,OAAO,EAAE;MACnB,MAAM;QAAEA;MAAQ,CAAC,GAAGjB,IAAI,CAACA,IAAI;MAC7BW,WAAW,CAACY,IAAI,CAAC,IAAItD,SAAS,CAAC,CAAC,CAAC;MACjC0C,WAAW,CAACY,IAAI,CAAC,IAAIvD,MAAM,CAACiD,OAAO,CAACC,EAAE,EAAED,OAAO,CAACsB,KAAK,EAAEb,SAAS,EAAE,IAAI,eAAA9B,iBAAA,CAAE,aAAY;QAChF,IAAI;UACA,MAAMQ,cAAc,CAACoC,cAAc,CAACvB,OAAO,CAACC,EAAE,EAAE,IAAID,OAAO,CAACtB,SAAS,IAAI,EAAE,CAAC,CAAC;QACjF,CAAC,CACD,OAAO8C,GAAG,EAAE;UACRnC,mBAAmB,CAACoC,MAAM,CAAC;YACvBC,QAAQ,EAAExD,QAAQ,CAACyD,KAAK;YACxBC,MAAM,EAAE7C,IAAI,CAACO,IAAI,CAACuC,QAAQ,CAACC,WAAW;YACtCC,OAAO,EAAEP;UACb,CAAC,CAAC;QACN;MACJ,CAAC,EAAC,CAAC;IACP;IACA;IACA,MAAMQ,YAAY,GAAGnD,MAAM,CAACoD,SAAS,CAAC,GAAG,CAAC,+BAA+B,CAAC;IAC1E/C,kBAAkB,CAACgD,eAAe,CAAC;MAC/BC,gBAAgB,EAAEH,YAAY,GAAGnD,MAAM,CAACuD,UAAU,CAAC,CAAC,IAAI3B,SAAS,GAAGA,SAAS;MAC7E4B,SAAS,EAAEA,CAAA,KAAM;QACb,MAAMC,GAAG,GAAGxF,GAAG,CAACyF,sBAAsB,CAACzD,MAAM,CAAC;QAC9C,OAAO;UAAE0D,CAAC,EAAEF,GAAG,CAACG,IAAI;UAAEC,CAAC,EAAEJ,GAAG,CAACK,GAAG,GAAGL,GAAG,CAACM,MAAM,GAAG;QAAE,CAAC;MACvD,CAAC;MACDC,UAAU,EAAEA,CAAA,KAAMnD,WAAW;MAC7BoD,MAAM,EAAEA,CAAA,KAAM;QACVjE,MAAM,CAACkE,KAAK,CAAC,CAAC;MAClB,CAAC;MACDC,mBAAmB,EAAE;IACzB,CAAC,CAAC;EACN,CAAC;EAAA,OAAAxE,oBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AACD,gBAAsBuE,0BAA0BA,CAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;EAAA,OAAAC,2BAAA,CAAA7E,KAAA,OAAAC,SAAA;AAAA;AAY/C,SAAA4E,4BAAA;EAAAA,2BAAA,GAAA3E,iBAAA,CAZM,WAA0CC,QAAQ,EAAE2E,KAAK,EAAE1E,MAAM,EAAEY,QAAQ,EAAE;IAChF,MAAMT,eAAe,GAAGJ,QAAQ,CAACK,GAAG,CAAC7B,iBAAiB,CAAC;IACvD,MAAMsD,GAAG,SAAS1B,eAAe,CAAC2B,oBAAoB,CAAClB,QAAQ,CAACmB,GAAG,CAAC;IACpE,MAAM/B,MAAM,CAAC2E,mBAAmB;MAAA,IAAAC,KAAA,GAAA9E,iBAAA,CAAC,WAAOC,QAAQ,EAAK;QACjD,MAAM8E,UAAU,GAAGH,KAAK,CAACI,qBAAqB;QAC9C,MAAMC,iBAAiB,GAAGhF,QAAQ,CAACK,GAAG,CAACnB,kBAAkB,CAAC;QAC1D,MAAM+F,QAAQ,GAAGrG,WAAW,CAACsG,YAAY,CAACC,QAAQ,CAACH,iBAAiB,CAAC;QACrE,MAAMI,OAAO,GAAG,CAACN,UAAU,IAAI7E,MAAM,CAACoD,SAAS,CAAC,EAAE,CAAC,4CAA4C,CAAC,IAAI,CAAC4B,QAAQ;QAC7G,MAAMI,MAAM,GAAG,IAAI5G,gBAAgB,CAAC;UAAEqG,UAAU;UAAEQ,UAAU,EAAEF,OAAO;UAAEG,WAAW,EAAE;QAAK,CAAC,EAAE;UAAE7C,KAAK,EAAE;YAAE8C,KAAK,EAAE,EAAE;YAAEC,QAAQ,EAAE;UAAG,CAAC;UAAEpE,EAAE,EAAE,EAAE;UAAEqE,YAAY,EAAE7D;QAAU,CAAC,CAAC;QACpK,OAAOwD,MAAM,CAACM,GAAG,CAAC3F,QAAQ,EAAE,IAAIrB,sBAAsB,CAACmD,GAAG,CAACI,MAAM,CAACC,eAAe,EAAE5D,KAAK,CAAC6D,gBAAgB,CAACvB,QAAQ,CAACwB,KAAK,CAAC,CAAC,EAAE9D,KAAK,CAACqH,IAAI,CAAC/E,QAAQ,CAACwB,KAAK,CAAC,CAAC;MAC3J,CAAC;MAAA,iBAAAwD,GAAA;QAAA,OAAAhB,KAAA,CAAAhF,KAAA,OAAAC,SAAA;MAAA;IAAA,IAAC;IACFgC,GAAG,CAACW,OAAO,CAAC,CAAC;EACjB,CAAC;EAAA,OAAAiC,2BAAA,CAAA7E,KAAA,OAAAC,SAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}