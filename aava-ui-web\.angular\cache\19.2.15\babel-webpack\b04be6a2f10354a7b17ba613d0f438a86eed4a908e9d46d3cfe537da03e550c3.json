{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as dom from '../../dom.js';\nimport { getBaseLayerHoverDelegate } from '../hover/hoverDelegate2.js';\nimport { getDefaultHoverDelegate } from '../hover/hoverDelegateFactory.js';\nimport { renderLabelWithIcons } from '../iconLabel/iconLabels.js';\nimport { Disposable } from '../../../common/lifecycle.js';\nimport * as objects from '../../../common/objects.js';\n/**\n * A widget which can render a label with substring highlights, often\n * originating from a filter function like the fuzzy matcher.\n */\nexport class HighlightedLabel extends Disposable {\n  /**\n   * Create a new {@link HighlightedLabel}.\n   *\n   * @param container The parent container to append to.\n   */\n  constructor(container, options) {\n    super();\n    this.options = options;\n    this.text = '';\n    this.title = '';\n    this.highlights = [];\n    this.didEverRender = false;\n    this.supportIcons = options?.supportIcons ?? false;\n    this.domNode = dom.append(container, dom.$('span.monaco-highlighted-label'));\n  }\n  /**\n   * The label's DOM node.\n   */\n  get element() {\n    return this.domNode;\n  }\n  /**\n   * Set the label and highlights.\n   *\n   * @param text The label to display.\n   * @param highlights The ranges to highlight.\n   * @param title An optional title for the hover tooltip.\n   * @param escapeNewLines Whether to escape new lines.\n   * @returns\n   */\n  set(text, highlights = [], title = '', escapeNewLines) {\n    if (!text) {\n      text = '';\n    }\n    if (escapeNewLines) {\n      // adjusts highlights inplace\n      text = HighlightedLabel.escapeNewLines(text, highlights);\n    }\n    if (this.didEverRender && this.text === text && this.title === title && objects.equals(this.highlights, highlights)) {\n      return;\n    }\n    this.text = text;\n    this.title = title;\n    this.highlights = highlights;\n    this.render();\n  }\n  render() {\n    const children = [];\n    let pos = 0;\n    for (const highlight of this.highlights) {\n      if (highlight.end === highlight.start) {\n        continue;\n      }\n      if (pos < highlight.start) {\n        const substring = this.text.substring(pos, highlight.start);\n        if (this.supportIcons) {\n          children.push(...renderLabelWithIcons(substring));\n        } else {\n          children.push(substring);\n        }\n        pos = highlight.start;\n      }\n      const substring = this.text.substring(pos, highlight.end);\n      const element = dom.$('span.highlight', undefined, ...(this.supportIcons ? renderLabelWithIcons(substring) : [substring]));\n      if (highlight.extraClasses) {\n        element.classList.add(...highlight.extraClasses);\n      }\n      children.push(element);\n      pos = highlight.end;\n    }\n    if (pos < this.text.length) {\n      const substring = this.text.substring(pos);\n      if (this.supportIcons) {\n        children.push(...renderLabelWithIcons(substring));\n      } else {\n        children.push(substring);\n      }\n    }\n    dom.reset(this.domNode, ...children);\n    if (this.options?.hoverDelegate?.showNativeHover) {\n      /* While custom hover is not inside custom hover */\n      this.domNode.title = this.title;\n    } else {\n      if (!this.customHover && this.title !== '') {\n        const hoverDelegate = this.options?.hoverDelegate ?? getDefaultHoverDelegate('mouse');\n        this.customHover = this._register(getBaseLayerHoverDelegate().setupManagedHover(hoverDelegate, this.domNode, this.title));\n      } else if (this.customHover) {\n        this.customHover.update(this.title);\n      }\n    }\n    this.didEverRender = true;\n  }\n  static escapeNewLines(text, highlights) {\n    let total = 0;\n    let extra = 0;\n    return text.replace(/\\r\\n|\\r|\\n/g, (match, offset) => {\n      extra = match === '\\r\\n' ? -1 : 0;\n      offset += total;\n      for (const highlight of highlights) {\n        if (highlight.end <= offset) {\n          continue;\n        }\n        if (highlight.start >= offset) {\n          highlight.start += extra;\n        }\n        if (highlight.end >= offset) {\n          highlight.end += extra;\n        }\n      }\n      total += extra;\n      return '\\u23CE';\n    });\n  }\n}", "map": {"version": 3, "names": ["dom", "getBaseLayerHoverDelegate", "getDefaultHoverDelegate", "renderLabelWithIcons", "Disposable", "objects", "HighlightedLabel", "constructor", "container", "options", "text", "title", "highlights", "did<PERSON><PERSON><PERSON><PERSON>", "supportIcons", "domNode", "append", "$", "element", "set", "escapeNewLines", "equals", "render", "children", "pos", "highlight", "end", "start", "substring", "push", "undefined", "extraClasses", "classList", "add", "length", "reset", "hoverDelegate", "showNativeHover", "customHover", "_register", "setupManagedHover", "update", "total", "extra", "replace", "match", "offset"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/browser/ui/highlightedlabel/highlightedLabel.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as dom from '../../dom.js';\nimport { getBaseLayerHoverDelegate } from '../hover/hoverDelegate2.js';\nimport { getDefaultHoverDelegate } from '../hover/hoverDelegateFactory.js';\nimport { renderLabelWithIcons } from '../iconLabel/iconLabels.js';\nimport { Disposable } from '../../../common/lifecycle.js';\nimport * as objects from '../../../common/objects.js';\n/**\n * A widget which can render a label with substring highlights, often\n * originating from a filter function like the fuzzy matcher.\n */\nexport class HighlightedLabel extends Disposable {\n    /**\n     * Create a new {@link HighlightedLabel}.\n     *\n     * @param container The parent container to append to.\n     */\n    constructor(container, options) {\n        super();\n        this.options = options;\n        this.text = '';\n        this.title = '';\n        this.highlights = [];\n        this.didEverRender = false;\n        this.supportIcons = options?.supportIcons ?? false;\n        this.domNode = dom.append(container, dom.$('span.monaco-highlighted-label'));\n    }\n    /**\n     * The label's DOM node.\n     */\n    get element() {\n        return this.domNode;\n    }\n    /**\n     * Set the label and highlights.\n     *\n     * @param text The label to display.\n     * @param highlights The ranges to highlight.\n     * @param title An optional title for the hover tooltip.\n     * @param escapeNewLines Whether to escape new lines.\n     * @returns\n     */\n    set(text, highlights = [], title = '', escapeNewLines) {\n        if (!text) {\n            text = '';\n        }\n        if (escapeNewLines) {\n            // adjusts highlights inplace\n            text = HighlightedLabel.escapeNewLines(text, highlights);\n        }\n        if (this.didEverRender && this.text === text && this.title === title && objects.equals(this.highlights, highlights)) {\n            return;\n        }\n        this.text = text;\n        this.title = title;\n        this.highlights = highlights;\n        this.render();\n    }\n    render() {\n        const children = [];\n        let pos = 0;\n        for (const highlight of this.highlights) {\n            if (highlight.end === highlight.start) {\n                continue;\n            }\n            if (pos < highlight.start) {\n                const substring = this.text.substring(pos, highlight.start);\n                if (this.supportIcons) {\n                    children.push(...renderLabelWithIcons(substring));\n                }\n                else {\n                    children.push(substring);\n                }\n                pos = highlight.start;\n            }\n            const substring = this.text.substring(pos, highlight.end);\n            const element = dom.$('span.highlight', undefined, ...this.supportIcons ? renderLabelWithIcons(substring) : [substring]);\n            if (highlight.extraClasses) {\n                element.classList.add(...highlight.extraClasses);\n            }\n            children.push(element);\n            pos = highlight.end;\n        }\n        if (pos < this.text.length) {\n            const substring = this.text.substring(pos);\n            if (this.supportIcons) {\n                children.push(...renderLabelWithIcons(substring));\n            }\n            else {\n                children.push(substring);\n            }\n        }\n        dom.reset(this.domNode, ...children);\n        if (this.options?.hoverDelegate?.showNativeHover) {\n            /* While custom hover is not inside custom hover */\n            this.domNode.title = this.title;\n        }\n        else {\n            if (!this.customHover && this.title !== '') {\n                const hoverDelegate = this.options?.hoverDelegate ?? getDefaultHoverDelegate('mouse');\n                this.customHover = this._register(getBaseLayerHoverDelegate().setupManagedHover(hoverDelegate, this.domNode, this.title));\n            }\n            else if (this.customHover) {\n                this.customHover.update(this.title);\n            }\n        }\n        this.didEverRender = true;\n    }\n    static escapeNewLines(text, highlights) {\n        let total = 0;\n        let extra = 0;\n        return text.replace(/\\r\\n|\\r|\\n/g, (match, offset) => {\n            extra = match === '\\r\\n' ? -1 : 0;\n            offset += total;\n            for (const highlight of highlights) {\n                if (highlight.end <= offset) {\n                    continue;\n                }\n                if (highlight.start >= offset) {\n                    highlight.start += extra;\n                }\n                if (highlight.end >= offset) {\n                    highlight.end += extra;\n                }\n            }\n            total += extra;\n            return '\\u23CE';\n        });\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,KAAKA,GAAG,MAAM,cAAc;AACnC,SAASC,yBAAyB,QAAQ,4BAA4B;AACtE,SAASC,uBAAuB,QAAQ,kCAAkC;AAC1E,SAASC,oBAAoB,QAAQ,4BAA4B;AACjE,SAASC,UAAU,QAAQ,8BAA8B;AACzD,OAAO,KAAKC,OAAO,MAAM,4BAA4B;AACrD;AACA;AACA;AACA;AACA,OAAO,MAAMC,gBAAgB,SAASF,UAAU,CAAC;EAC7C;AACJ;AACA;AACA;AACA;EACIG,WAAWA,CAACC,SAAS,EAAEC,OAAO,EAAE;IAC5B,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,IAAI,GAAG,EAAE;IACd,IAAI,CAACC,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACC,YAAY,GAAGL,OAAO,EAAEK,YAAY,IAAI,KAAK;IAClD,IAAI,CAACC,OAAO,GAAGf,GAAG,CAACgB,MAAM,CAACR,SAAS,EAAER,GAAG,CAACiB,CAAC,CAAC,+BAA+B,CAAC,CAAC;EAChF;EACA;AACJ;AACA;EACI,IAAIC,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACH,OAAO;EACvB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACII,GAAGA,CAACT,IAAI,EAAEE,UAAU,GAAG,EAAE,EAAED,KAAK,GAAG,EAAE,EAAES,cAAc,EAAE;IACnD,IAAI,CAACV,IAAI,EAAE;MACPA,IAAI,GAAG,EAAE;IACb;IACA,IAAIU,cAAc,EAAE;MAChB;MACAV,IAAI,GAAGJ,gBAAgB,CAACc,cAAc,CAACV,IAAI,EAAEE,UAAU,CAAC;IAC5D;IACA,IAAI,IAAI,CAACC,aAAa,IAAI,IAAI,CAACH,IAAI,KAAKA,IAAI,IAAI,IAAI,CAACC,KAAK,KAAKA,KAAK,IAAIN,OAAO,CAACgB,MAAM,CAAC,IAAI,CAACT,UAAU,EAAEA,UAAU,CAAC,EAAE;MACjH;IACJ;IACA,IAAI,CAACF,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACU,MAAM,CAAC,CAAC;EACjB;EACAA,MAAMA,CAAA,EAAG;IACL,MAAMC,QAAQ,GAAG,EAAE;IACnB,IAAIC,GAAG,GAAG,CAAC;IACX,KAAK,MAAMC,SAAS,IAAI,IAAI,CAACb,UAAU,EAAE;MACrC,IAAIa,SAAS,CAACC,GAAG,KAAKD,SAAS,CAACE,KAAK,EAAE;QACnC;MACJ;MACA,IAAIH,GAAG,GAAGC,SAAS,CAACE,KAAK,EAAE;QACvB,MAAMC,SAAS,GAAG,IAAI,CAAClB,IAAI,CAACkB,SAAS,CAACJ,GAAG,EAAEC,SAAS,CAACE,KAAK,CAAC;QAC3D,IAAI,IAAI,CAACb,YAAY,EAAE;UACnBS,QAAQ,CAACM,IAAI,CAAC,GAAG1B,oBAAoB,CAACyB,SAAS,CAAC,CAAC;QACrD,CAAC,MACI;UACDL,QAAQ,CAACM,IAAI,CAACD,SAAS,CAAC;QAC5B;QACAJ,GAAG,GAAGC,SAAS,CAACE,KAAK;MACzB;MACA,MAAMC,SAAS,GAAG,IAAI,CAAClB,IAAI,CAACkB,SAAS,CAACJ,GAAG,EAAEC,SAAS,CAACC,GAAG,CAAC;MACzD,MAAMR,OAAO,GAAGlB,GAAG,CAACiB,CAAC,CAAC,gBAAgB,EAAEa,SAAS,EAAE,IAAG,IAAI,CAAChB,YAAY,GAAGX,oBAAoB,CAACyB,SAAS,CAAC,GAAG,CAACA,SAAS,CAAC,EAAC;MACxH,IAAIH,SAAS,CAACM,YAAY,EAAE;QACxBb,OAAO,CAACc,SAAS,CAACC,GAAG,CAAC,GAAGR,SAAS,CAACM,YAAY,CAAC;MACpD;MACAR,QAAQ,CAACM,IAAI,CAACX,OAAO,CAAC;MACtBM,GAAG,GAAGC,SAAS,CAACC,GAAG;IACvB;IACA,IAAIF,GAAG,GAAG,IAAI,CAACd,IAAI,CAACwB,MAAM,EAAE;MACxB,MAAMN,SAAS,GAAG,IAAI,CAAClB,IAAI,CAACkB,SAAS,CAACJ,GAAG,CAAC;MAC1C,IAAI,IAAI,CAACV,YAAY,EAAE;QACnBS,QAAQ,CAACM,IAAI,CAAC,GAAG1B,oBAAoB,CAACyB,SAAS,CAAC,CAAC;MACrD,CAAC,MACI;QACDL,QAAQ,CAACM,IAAI,CAACD,SAAS,CAAC;MAC5B;IACJ;IACA5B,GAAG,CAACmC,KAAK,CAAC,IAAI,CAACpB,OAAO,EAAE,GAAGQ,QAAQ,CAAC;IACpC,IAAI,IAAI,CAACd,OAAO,EAAE2B,aAAa,EAAEC,eAAe,EAAE;MAC9C;MACA,IAAI,CAACtB,OAAO,CAACJ,KAAK,GAAG,IAAI,CAACA,KAAK;IACnC,CAAC,MACI;MACD,IAAI,CAAC,IAAI,CAAC2B,WAAW,IAAI,IAAI,CAAC3B,KAAK,KAAK,EAAE,EAAE;QACxC,MAAMyB,aAAa,GAAG,IAAI,CAAC3B,OAAO,EAAE2B,aAAa,IAAIlC,uBAAuB,CAAC,OAAO,CAAC;QACrF,IAAI,CAACoC,WAAW,GAAG,IAAI,CAACC,SAAS,CAACtC,yBAAyB,CAAC,CAAC,CAACuC,iBAAiB,CAACJ,aAAa,EAAE,IAAI,CAACrB,OAAO,EAAE,IAAI,CAACJ,KAAK,CAAC,CAAC;MAC7H,CAAC,MACI,IAAI,IAAI,CAAC2B,WAAW,EAAE;QACvB,IAAI,CAACA,WAAW,CAACG,MAAM,CAAC,IAAI,CAAC9B,KAAK,CAAC;MACvC;IACJ;IACA,IAAI,CAACE,aAAa,GAAG,IAAI;EAC7B;EACA,OAAOO,cAAcA,CAACV,IAAI,EAAEE,UAAU,EAAE;IACpC,IAAI8B,KAAK,GAAG,CAAC;IACb,IAAIC,KAAK,GAAG,CAAC;IACb,OAAOjC,IAAI,CAACkC,OAAO,CAAC,aAAa,EAAE,CAACC,KAAK,EAAEC,MAAM,KAAK;MAClDH,KAAK,GAAGE,KAAK,KAAK,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC;MACjCC,MAAM,IAAIJ,KAAK;MACf,KAAK,MAAMjB,SAAS,IAAIb,UAAU,EAAE;QAChC,IAAIa,SAAS,CAACC,GAAG,IAAIoB,MAAM,EAAE;UACzB;QACJ;QACA,IAAIrB,SAAS,CAACE,KAAK,IAAImB,MAAM,EAAE;UAC3BrB,SAAS,CAACE,KAAK,IAAIgB,KAAK;QAC5B;QACA,IAAIlB,SAAS,CAACC,GAAG,IAAIoB,MAAM,EAAE;UACzBrB,SAAS,CAACC,GAAG,IAAIiB,KAAK;QAC1B;MACJ;MACAD,KAAK,IAAIC,KAAK;MACd,OAAO,QAAQ;IACnB,CAAC,CAAC;EACN;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}