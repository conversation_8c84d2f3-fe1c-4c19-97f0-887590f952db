{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { LineTokens } from './lineTokens.js';\nexport const EMPTY_LINE_TOKENS = new Uint32Array(0).buffer;\nexport class ContiguousTokensEditing {\n  static deleteBeginning(lineTokens, toChIndex) {\n    if (lineTokens === null || lineTokens === EMPTY_LINE_TOKENS) {\n      return lineTokens;\n    }\n    return ContiguousTokensEditing.delete(lineTokens, 0, toChIndex);\n  }\n  static deleteEnding(lineTokens, fromChIndex) {\n    if (lineTokens === null || lineTokens === EMPTY_LINE_TOKENS) {\n      return lineTokens;\n    }\n    const tokens = toUint32Array(lineTokens);\n    const lineTextLength = tokens[tokens.length - 2];\n    return ContiguousTokensEditing.delete(lineTokens, fromChIndex, lineTextLength);\n  }\n  static delete(lineTokens, fromChIndex, toChIndex) {\n    if (lineTokens === null || lineTokens === EMPTY_LINE_TOKENS || fromChIndex === toChIndex) {\n      return lineTokens;\n    }\n    const tokens = toUint32Array(lineTokens);\n    const tokensCount = tokens.length >>> 1;\n    // special case: deleting everything\n    if (fromChIndex === 0 && tokens[tokens.length - 2] === toChIndex) {\n      return EMPTY_LINE_TOKENS;\n    }\n    const fromTokenIndex = LineTokens.findIndexInTokensArray(tokens, fromChIndex);\n    const fromTokenStartOffset = fromTokenIndex > 0 ? tokens[fromTokenIndex - 1 << 1] : 0;\n    const fromTokenEndOffset = tokens[fromTokenIndex << 1];\n    if (toChIndex < fromTokenEndOffset) {\n      // the delete range is inside a single token\n      const delta = toChIndex - fromChIndex;\n      for (let i = fromTokenIndex; i < tokensCount; i++) {\n        tokens[i << 1] -= delta;\n      }\n      return lineTokens;\n    }\n    let dest;\n    let lastEnd;\n    if (fromTokenStartOffset !== fromChIndex) {\n      tokens[fromTokenIndex << 1] = fromChIndex;\n      dest = fromTokenIndex + 1 << 1;\n      lastEnd = fromChIndex;\n    } else {\n      dest = fromTokenIndex << 1;\n      lastEnd = fromTokenStartOffset;\n    }\n    const delta = toChIndex - fromChIndex;\n    for (let tokenIndex = fromTokenIndex + 1; tokenIndex < tokensCount; tokenIndex++) {\n      const tokenEndOffset = tokens[tokenIndex << 1] - delta;\n      if (tokenEndOffset > lastEnd) {\n        tokens[dest++] = tokenEndOffset;\n        tokens[dest++] = tokens[(tokenIndex << 1) + 1];\n        lastEnd = tokenEndOffset;\n      }\n    }\n    if (dest === tokens.length) {\n      // nothing to trim\n      return lineTokens;\n    }\n    const tmp = new Uint32Array(dest);\n    tmp.set(tokens.subarray(0, dest), 0);\n    return tmp.buffer;\n  }\n  static append(lineTokens, _otherTokens) {\n    if (_otherTokens === EMPTY_LINE_TOKENS) {\n      return lineTokens;\n    }\n    if (lineTokens === EMPTY_LINE_TOKENS) {\n      return _otherTokens;\n    }\n    if (lineTokens === null) {\n      return lineTokens;\n    }\n    if (_otherTokens === null) {\n      // cannot determine combined line length...\n      return null;\n    }\n    const myTokens = toUint32Array(lineTokens);\n    const otherTokens = toUint32Array(_otherTokens);\n    const otherTokensCount = otherTokens.length >>> 1;\n    const result = new Uint32Array(myTokens.length + otherTokens.length);\n    result.set(myTokens, 0);\n    let dest = myTokens.length;\n    const delta = myTokens[myTokens.length - 2];\n    for (let i = 0; i < otherTokensCount; i++) {\n      result[dest++] = otherTokens[i << 1] + delta;\n      result[dest++] = otherTokens[(i << 1) + 1];\n    }\n    return result.buffer;\n  }\n  static insert(lineTokens, chIndex, textLength) {\n    if (lineTokens === null || lineTokens === EMPTY_LINE_TOKENS) {\n      // nothing to do\n      return lineTokens;\n    }\n    const tokens = toUint32Array(lineTokens);\n    const tokensCount = tokens.length >>> 1;\n    let fromTokenIndex = LineTokens.findIndexInTokensArray(tokens, chIndex);\n    if (fromTokenIndex > 0) {\n      const fromTokenStartOffset = tokens[fromTokenIndex - 1 << 1];\n      if (fromTokenStartOffset === chIndex) {\n        fromTokenIndex--;\n      }\n    }\n    for (let tokenIndex = fromTokenIndex; tokenIndex < tokensCount; tokenIndex++) {\n      tokens[tokenIndex << 1] += textLength;\n    }\n    return lineTokens;\n  }\n}\nexport function toUint32Array(arr) {\n  if (arr instanceof Uint32Array) {\n    return arr;\n  } else {\n    return new Uint32Array(arr);\n  }\n}", "map": {"version": 3, "names": ["LineTokens", "EMPTY_LINE_TOKENS", "Uint32Array", "buffer", "ContiguousTokensEditing", "deleteBeginning", "lineTokens", "toChIndex", "delete", "deleteEnding", "fromChIndex", "tokens", "toUint32Array", "lineTextLength", "length", "tokensCount", "fromTokenIndex", "findIndexInTokensArray", "fromTokenStartOffset", "fromTokenEndOffset", "delta", "i", "dest", "lastEnd", "tokenIndex", "tokenEndOffset", "tmp", "set", "subarray", "append", "_otherTokens", "myTokens", "otherTokens", "otherTokensCount", "result", "insert", "chIndex", "textLength", "arr"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/tokens/contiguousTokensEditing.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { LineTokens } from './lineTokens.js';\nexport const EMPTY_LINE_TOKENS = (new Uint32Array(0)).buffer;\nexport class ContiguousTokensEditing {\n    static deleteBeginning(lineTokens, toChIndex) {\n        if (lineTokens === null || lineTokens === EMPTY_LINE_TOKENS) {\n            return lineTokens;\n        }\n        return ContiguousTokensEditing.delete(lineTokens, 0, toChIndex);\n    }\n    static deleteEnding(lineTokens, fromChIndex) {\n        if (lineTokens === null || lineTokens === EMPTY_LINE_TOKENS) {\n            return lineTokens;\n        }\n        const tokens = toUint32Array(lineTokens);\n        const lineTextLength = tokens[tokens.length - 2];\n        return ContiguousTokensEditing.delete(lineTokens, fromChIndex, lineTextLength);\n    }\n    static delete(lineTokens, fromChIndex, toChIndex) {\n        if (lineTokens === null || lineTokens === EMPTY_LINE_TOKENS || fromChIndex === toChIndex) {\n            return lineTokens;\n        }\n        const tokens = toUint32Array(lineTokens);\n        const tokensCount = (tokens.length >>> 1);\n        // special case: deleting everything\n        if (fromChIndex === 0 && tokens[tokens.length - 2] === toChIndex) {\n            return EMPTY_LINE_TOKENS;\n        }\n        const fromTokenIndex = LineTokens.findIndexInTokensArray(tokens, fromChIndex);\n        const fromTokenStartOffset = (fromTokenIndex > 0 ? tokens[(fromTokenIndex - 1) << 1] : 0);\n        const fromTokenEndOffset = tokens[fromTokenIndex << 1];\n        if (toChIndex < fromTokenEndOffset) {\n            // the delete range is inside a single token\n            const delta = (toChIndex - fromChIndex);\n            for (let i = fromTokenIndex; i < tokensCount; i++) {\n                tokens[i << 1] -= delta;\n            }\n            return lineTokens;\n        }\n        let dest;\n        let lastEnd;\n        if (fromTokenStartOffset !== fromChIndex) {\n            tokens[fromTokenIndex << 1] = fromChIndex;\n            dest = ((fromTokenIndex + 1) << 1);\n            lastEnd = fromChIndex;\n        }\n        else {\n            dest = (fromTokenIndex << 1);\n            lastEnd = fromTokenStartOffset;\n        }\n        const delta = (toChIndex - fromChIndex);\n        for (let tokenIndex = fromTokenIndex + 1; tokenIndex < tokensCount; tokenIndex++) {\n            const tokenEndOffset = tokens[tokenIndex << 1] - delta;\n            if (tokenEndOffset > lastEnd) {\n                tokens[dest++] = tokenEndOffset;\n                tokens[dest++] = tokens[(tokenIndex << 1) + 1];\n                lastEnd = tokenEndOffset;\n            }\n        }\n        if (dest === tokens.length) {\n            // nothing to trim\n            return lineTokens;\n        }\n        const tmp = new Uint32Array(dest);\n        tmp.set(tokens.subarray(0, dest), 0);\n        return tmp.buffer;\n    }\n    static append(lineTokens, _otherTokens) {\n        if (_otherTokens === EMPTY_LINE_TOKENS) {\n            return lineTokens;\n        }\n        if (lineTokens === EMPTY_LINE_TOKENS) {\n            return _otherTokens;\n        }\n        if (lineTokens === null) {\n            return lineTokens;\n        }\n        if (_otherTokens === null) {\n            // cannot determine combined line length...\n            return null;\n        }\n        const myTokens = toUint32Array(lineTokens);\n        const otherTokens = toUint32Array(_otherTokens);\n        const otherTokensCount = (otherTokens.length >>> 1);\n        const result = new Uint32Array(myTokens.length + otherTokens.length);\n        result.set(myTokens, 0);\n        let dest = myTokens.length;\n        const delta = myTokens[myTokens.length - 2];\n        for (let i = 0; i < otherTokensCount; i++) {\n            result[dest++] = otherTokens[(i << 1)] + delta;\n            result[dest++] = otherTokens[(i << 1) + 1];\n        }\n        return result.buffer;\n    }\n    static insert(lineTokens, chIndex, textLength) {\n        if (lineTokens === null || lineTokens === EMPTY_LINE_TOKENS) {\n            // nothing to do\n            return lineTokens;\n        }\n        const tokens = toUint32Array(lineTokens);\n        const tokensCount = (tokens.length >>> 1);\n        let fromTokenIndex = LineTokens.findIndexInTokensArray(tokens, chIndex);\n        if (fromTokenIndex > 0) {\n            const fromTokenStartOffset = tokens[(fromTokenIndex - 1) << 1];\n            if (fromTokenStartOffset === chIndex) {\n                fromTokenIndex--;\n            }\n        }\n        for (let tokenIndex = fromTokenIndex; tokenIndex < tokensCount; tokenIndex++) {\n            tokens[tokenIndex << 1] += textLength;\n        }\n        return lineTokens;\n    }\n}\nexport function toUint32Array(arr) {\n    if (arr instanceof Uint32Array) {\n        return arr;\n    }\n    else {\n        return new Uint32Array(arr);\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,UAAU,QAAQ,iBAAiB;AAC5C,OAAO,MAAMC,iBAAiB,GAAI,IAAIC,WAAW,CAAC,CAAC,CAAC,CAAEC,MAAM;AAC5D,OAAO,MAAMC,uBAAuB,CAAC;EACjC,OAAOC,eAAeA,CAACC,UAAU,EAAEC,SAAS,EAAE;IAC1C,IAAID,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAKL,iBAAiB,EAAE;MACzD,OAAOK,UAAU;IACrB;IACA,OAAOF,uBAAuB,CAACI,MAAM,CAACF,UAAU,EAAE,CAAC,EAAEC,SAAS,CAAC;EACnE;EACA,OAAOE,YAAYA,CAACH,UAAU,EAAEI,WAAW,EAAE;IACzC,IAAIJ,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAKL,iBAAiB,EAAE;MACzD,OAAOK,UAAU;IACrB;IACA,MAAMK,MAAM,GAAGC,aAAa,CAACN,UAAU,CAAC;IACxC,MAAMO,cAAc,GAAGF,MAAM,CAACA,MAAM,CAACG,MAAM,GAAG,CAAC,CAAC;IAChD,OAAOV,uBAAuB,CAACI,MAAM,CAACF,UAAU,EAAEI,WAAW,EAAEG,cAAc,CAAC;EAClF;EACA,OAAOL,MAAMA,CAACF,UAAU,EAAEI,WAAW,EAAEH,SAAS,EAAE;IAC9C,IAAID,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAKL,iBAAiB,IAAIS,WAAW,KAAKH,SAAS,EAAE;MACtF,OAAOD,UAAU;IACrB;IACA,MAAMK,MAAM,GAAGC,aAAa,CAACN,UAAU,CAAC;IACxC,MAAMS,WAAW,GAAIJ,MAAM,CAACG,MAAM,KAAK,CAAE;IACzC;IACA,IAAIJ,WAAW,KAAK,CAAC,IAAIC,MAAM,CAACA,MAAM,CAACG,MAAM,GAAG,CAAC,CAAC,KAAKP,SAAS,EAAE;MAC9D,OAAON,iBAAiB;IAC5B;IACA,MAAMe,cAAc,GAAGhB,UAAU,CAACiB,sBAAsB,CAACN,MAAM,EAAED,WAAW,CAAC;IAC7E,MAAMQ,oBAAoB,GAAIF,cAAc,GAAG,CAAC,GAAGL,MAAM,CAAEK,cAAc,GAAG,CAAC,IAAK,CAAC,CAAC,GAAG,CAAE;IACzF,MAAMG,kBAAkB,GAAGR,MAAM,CAACK,cAAc,IAAI,CAAC,CAAC;IACtD,IAAIT,SAAS,GAAGY,kBAAkB,EAAE;MAChC;MACA,MAAMC,KAAK,GAAIb,SAAS,GAAGG,WAAY;MACvC,KAAK,IAAIW,CAAC,GAAGL,cAAc,EAAEK,CAAC,GAAGN,WAAW,EAAEM,CAAC,EAAE,EAAE;QAC/CV,MAAM,CAACU,CAAC,IAAI,CAAC,CAAC,IAAID,KAAK;MAC3B;MACA,OAAOd,UAAU;IACrB;IACA,IAAIgB,IAAI;IACR,IAAIC,OAAO;IACX,IAAIL,oBAAoB,KAAKR,WAAW,EAAE;MACtCC,MAAM,CAACK,cAAc,IAAI,CAAC,CAAC,GAAGN,WAAW;MACzCY,IAAI,GAAKN,cAAc,GAAG,CAAC,IAAK,CAAE;MAClCO,OAAO,GAAGb,WAAW;IACzB,CAAC,MACI;MACDY,IAAI,GAAIN,cAAc,IAAI,CAAE;MAC5BO,OAAO,GAAGL,oBAAoB;IAClC;IACA,MAAME,KAAK,GAAIb,SAAS,GAAGG,WAAY;IACvC,KAAK,IAAIc,UAAU,GAAGR,cAAc,GAAG,CAAC,EAAEQ,UAAU,GAAGT,WAAW,EAAES,UAAU,EAAE,EAAE;MAC9E,MAAMC,cAAc,GAAGd,MAAM,CAACa,UAAU,IAAI,CAAC,CAAC,GAAGJ,KAAK;MACtD,IAAIK,cAAc,GAAGF,OAAO,EAAE;QAC1BZ,MAAM,CAACW,IAAI,EAAE,CAAC,GAAGG,cAAc;QAC/Bd,MAAM,CAACW,IAAI,EAAE,CAAC,GAAGX,MAAM,CAAC,CAACa,UAAU,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9CD,OAAO,GAAGE,cAAc;MAC5B;IACJ;IACA,IAAIH,IAAI,KAAKX,MAAM,CAACG,MAAM,EAAE;MACxB;MACA,OAAOR,UAAU;IACrB;IACA,MAAMoB,GAAG,GAAG,IAAIxB,WAAW,CAACoB,IAAI,CAAC;IACjCI,GAAG,CAACC,GAAG,CAAChB,MAAM,CAACiB,QAAQ,CAAC,CAAC,EAAEN,IAAI,CAAC,EAAE,CAAC,CAAC;IACpC,OAAOI,GAAG,CAACvB,MAAM;EACrB;EACA,OAAO0B,MAAMA,CAACvB,UAAU,EAAEwB,YAAY,EAAE;IACpC,IAAIA,YAAY,KAAK7B,iBAAiB,EAAE;MACpC,OAAOK,UAAU;IACrB;IACA,IAAIA,UAAU,KAAKL,iBAAiB,EAAE;MAClC,OAAO6B,YAAY;IACvB;IACA,IAAIxB,UAAU,KAAK,IAAI,EAAE;MACrB,OAAOA,UAAU;IACrB;IACA,IAAIwB,YAAY,KAAK,IAAI,EAAE;MACvB;MACA,OAAO,IAAI;IACf;IACA,MAAMC,QAAQ,GAAGnB,aAAa,CAACN,UAAU,CAAC;IAC1C,MAAM0B,WAAW,GAAGpB,aAAa,CAACkB,YAAY,CAAC;IAC/C,MAAMG,gBAAgB,GAAID,WAAW,CAAClB,MAAM,KAAK,CAAE;IACnD,MAAMoB,MAAM,GAAG,IAAIhC,WAAW,CAAC6B,QAAQ,CAACjB,MAAM,GAAGkB,WAAW,CAAClB,MAAM,CAAC;IACpEoB,MAAM,CAACP,GAAG,CAACI,QAAQ,EAAE,CAAC,CAAC;IACvB,IAAIT,IAAI,GAAGS,QAAQ,CAACjB,MAAM;IAC1B,MAAMM,KAAK,GAAGW,QAAQ,CAACA,QAAQ,CAACjB,MAAM,GAAG,CAAC,CAAC;IAC3C,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,gBAAgB,EAAEZ,CAAC,EAAE,EAAE;MACvCa,MAAM,CAACZ,IAAI,EAAE,CAAC,GAAGU,WAAW,CAAEX,CAAC,IAAI,CAAC,CAAE,GAAGD,KAAK;MAC9Cc,MAAM,CAACZ,IAAI,EAAE,CAAC,GAAGU,WAAW,CAAC,CAACX,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC9C;IACA,OAAOa,MAAM,CAAC/B,MAAM;EACxB;EACA,OAAOgC,MAAMA,CAAC7B,UAAU,EAAE8B,OAAO,EAAEC,UAAU,EAAE;IAC3C,IAAI/B,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAKL,iBAAiB,EAAE;MACzD;MACA,OAAOK,UAAU;IACrB;IACA,MAAMK,MAAM,GAAGC,aAAa,CAACN,UAAU,CAAC;IACxC,MAAMS,WAAW,GAAIJ,MAAM,CAACG,MAAM,KAAK,CAAE;IACzC,IAAIE,cAAc,GAAGhB,UAAU,CAACiB,sBAAsB,CAACN,MAAM,EAAEyB,OAAO,CAAC;IACvE,IAAIpB,cAAc,GAAG,CAAC,EAAE;MACpB,MAAME,oBAAoB,GAAGP,MAAM,CAAEK,cAAc,GAAG,CAAC,IAAK,CAAC,CAAC;MAC9D,IAAIE,oBAAoB,KAAKkB,OAAO,EAAE;QAClCpB,cAAc,EAAE;MACpB;IACJ;IACA,KAAK,IAAIQ,UAAU,GAAGR,cAAc,EAAEQ,UAAU,GAAGT,WAAW,EAAES,UAAU,EAAE,EAAE;MAC1Eb,MAAM,CAACa,UAAU,IAAI,CAAC,CAAC,IAAIa,UAAU;IACzC;IACA,OAAO/B,UAAU;EACrB;AACJ;AACA,OAAO,SAASM,aAAaA,CAAC0B,GAAG,EAAE;EAC/B,IAAIA,GAAG,YAAYpC,WAAW,EAAE;IAC5B,OAAOoC,GAAG;EACd,CAAC,MACI;IACD,OAAO,IAAIpC,WAAW,CAACoC,GAAG,CAAC;EAC/B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}