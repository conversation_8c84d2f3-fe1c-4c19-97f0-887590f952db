{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as dom from '../../../../base/browser/dom.js';\nimport { createFastDomNode } from '../../../../base/browser/fastDomNode.js';\nimport * as strings from '../../../../base/common/strings.js';\nimport { applyFontInfo } from '../../config/domFontInfo.js';\nimport { TextEditorCursorStyle } from '../../../common/config/editorOptions.js';\nimport { Position } from '../../../common/core/position.js';\nimport { Range } from '../../../common/core/range.js';\nimport { MOUSE_CURSOR_TEXT_CSS_CLASS_NAME } from '../../../../base/browser/ui/mouseCursor/mouseCursor.js';\nclass ViewCursorRenderData {\n  constructor(top, left, paddingLeft, width, height, textContent, textContentClassName) {\n    this.top = top;\n    this.left = left;\n    this.paddingLeft = paddingLeft;\n    this.width = width;\n    this.height = height;\n    this.textContent = textContent;\n    this.textContentClassName = textContentClassName;\n  }\n}\nexport var CursorPlurality = /*#__PURE__*/function (CursorPlurality) {\n  CursorPlurality[CursorPlurality[\"Single\"] = 0] = \"Single\";\n  CursorPlurality[CursorPlurality[\"MultiPrimary\"] = 1] = \"MultiPrimary\";\n  CursorPlurality[CursorPlurality[\"MultiSecondary\"] = 2] = \"MultiSecondary\";\n  return CursorPlurality;\n}(CursorPlurality || {});\nexport class ViewCursor {\n  constructor(context, plurality) {\n    this._context = context;\n    const options = this._context.configuration.options;\n    const fontInfo = options.get(50 /* EditorOption.fontInfo */);\n    this._cursorStyle = options.get(28 /* EditorOption.cursorStyle */);\n    this._lineHeight = options.get(67 /* EditorOption.lineHeight */);\n    this._typicalHalfwidthCharacterWidth = fontInfo.typicalHalfwidthCharacterWidth;\n    this._lineCursorWidth = Math.min(options.get(31 /* EditorOption.cursorWidth */), this._typicalHalfwidthCharacterWidth);\n    this._isVisible = true;\n    // Create the dom node\n    this._domNode = createFastDomNode(document.createElement('div'));\n    this._domNode.setClassName(`cursor ${MOUSE_CURSOR_TEXT_CSS_CLASS_NAME}`);\n    this._domNode.setHeight(this._lineHeight);\n    this._domNode.setTop(0);\n    this._domNode.setLeft(0);\n    applyFontInfo(this._domNode, fontInfo);\n    this._domNode.setDisplay('none');\n    this._position = new Position(1, 1);\n    this._pluralityClass = '';\n    this.setPlurality(plurality);\n    this._lastRenderedContent = '';\n    this._renderData = null;\n  }\n  getDomNode() {\n    return this._domNode;\n  }\n  getPosition() {\n    return this._position;\n  }\n  setPlurality(plurality) {\n    switch (plurality) {\n      default:\n      case CursorPlurality.Single:\n        this._pluralityClass = '';\n        break;\n      case CursorPlurality.MultiPrimary:\n        this._pluralityClass = 'cursor-primary';\n        break;\n      case CursorPlurality.MultiSecondary:\n        this._pluralityClass = 'cursor-secondary';\n        break;\n    }\n  }\n  show() {\n    if (!this._isVisible) {\n      this._domNode.setVisibility('inherit');\n      this._isVisible = true;\n    }\n  }\n  hide() {\n    if (this._isVisible) {\n      this._domNode.setVisibility('hidden');\n      this._isVisible = false;\n    }\n  }\n  onConfigurationChanged(e) {\n    const options = this._context.configuration.options;\n    const fontInfo = options.get(50 /* EditorOption.fontInfo */);\n    this._cursorStyle = options.get(28 /* EditorOption.cursorStyle */);\n    this._lineHeight = options.get(67 /* EditorOption.lineHeight */);\n    this._typicalHalfwidthCharacterWidth = fontInfo.typicalHalfwidthCharacterWidth;\n    this._lineCursorWidth = Math.min(options.get(31 /* EditorOption.cursorWidth */), this._typicalHalfwidthCharacterWidth);\n    applyFontInfo(this._domNode, fontInfo);\n    return true;\n  }\n  onCursorPositionChanged(position, pauseAnimation) {\n    if (pauseAnimation) {\n      this._domNode.domNode.style.transitionProperty = 'none';\n    } else {\n      this._domNode.domNode.style.transitionProperty = '';\n    }\n    this._position = position;\n    return true;\n  }\n  /**\n   * If `this._position` is inside a grapheme, returns the position where the grapheme starts.\n   * Also returns the next grapheme.\n   */\n  _getGraphemeAwarePosition() {\n    const {\n      lineNumber,\n      column\n    } = this._position;\n    const lineContent = this._context.viewModel.getLineContent(lineNumber);\n    const [startOffset, endOffset] = strings.getCharContainingOffset(lineContent, column - 1);\n    return [new Position(lineNumber, startOffset + 1), lineContent.substring(startOffset, endOffset)];\n  }\n  _prepareRender(ctx) {\n    let textContent = '';\n    let textContentClassName = '';\n    const [position, nextGrapheme] = this._getGraphemeAwarePosition();\n    if (this._cursorStyle === TextEditorCursorStyle.Line || this._cursorStyle === TextEditorCursorStyle.LineThin) {\n      const visibleRange = ctx.visibleRangeForPosition(position);\n      if (!visibleRange || visibleRange.outsideRenderedLine) {\n        // Outside viewport\n        return null;\n      }\n      const window = dom.getWindow(this._domNode.domNode);\n      let width;\n      if (this._cursorStyle === TextEditorCursorStyle.Line) {\n        width = dom.computeScreenAwareSize(window, this._lineCursorWidth > 0 ? this._lineCursorWidth : 2);\n        if (width > 2) {\n          textContent = nextGrapheme;\n          textContentClassName = this._getTokenClassName(position);\n        }\n      } else {\n        width = dom.computeScreenAwareSize(window, 1);\n      }\n      let left = visibleRange.left;\n      let paddingLeft = 0;\n      if (width >= 2 && left >= 1) {\n        // shift the cursor a bit between the characters\n        paddingLeft = 1;\n        left -= paddingLeft;\n      }\n      const top = ctx.getVerticalOffsetForLineNumber(position.lineNumber) - ctx.bigNumbersDelta;\n      return new ViewCursorRenderData(top, left, paddingLeft, width, this._lineHeight, textContent, textContentClassName);\n    }\n    const visibleRangeForCharacter = ctx.linesVisibleRangesForRange(new Range(position.lineNumber, position.column, position.lineNumber, position.column + nextGrapheme.length), false);\n    if (!visibleRangeForCharacter || visibleRangeForCharacter.length === 0) {\n      // Outside viewport\n      return null;\n    }\n    const firstVisibleRangeForCharacter = visibleRangeForCharacter[0];\n    if (firstVisibleRangeForCharacter.outsideRenderedLine || firstVisibleRangeForCharacter.ranges.length === 0) {\n      // Outside viewport\n      return null;\n    }\n    const range = firstVisibleRangeForCharacter.ranges[0];\n    const width = nextGrapheme === '\\t' ? this._typicalHalfwidthCharacterWidth : range.width < 1 ? this._typicalHalfwidthCharacterWidth : range.width;\n    if (this._cursorStyle === TextEditorCursorStyle.Block) {\n      textContent = nextGrapheme;\n      textContentClassName = this._getTokenClassName(position);\n    }\n    let top = ctx.getVerticalOffsetForLineNumber(position.lineNumber) - ctx.bigNumbersDelta;\n    let height = this._lineHeight;\n    // Underline might interfere with clicking\n    if (this._cursorStyle === TextEditorCursorStyle.Underline || this._cursorStyle === TextEditorCursorStyle.UnderlineThin) {\n      top += this._lineHeight - 2;\n      height = 2;\n    }\n    return new ViewCursorRenderData(top, range.left, 0, width, height, textContent, textContentClassName);\n  }\n  _getTokenClassName(position) {\n    const lineData = this._context.viewModel.getViewLineData(position.lineNumber);\n    const tokenIndex = lineData.tokens.findTokenIndexAtOffset(position.column - 1);\n    return lineData.tokens.getClassName(tokenIndex);\n  }\n  prepareRender(ctx) {\n    this._renderData = this._prepareRender(ctx);\n  }\n  render(ctx) {\n    if (!this._renderData) {\n      this._domNode.setDisplay('none');\n      return null;\n    }\n    if (this._lastRenderedContent !== this._renderData.textContent) {\n      this._lastRenderedContent = this._renderData.textContent;\n      this._domNode.domNode.textContent = this._lastRenderedContent;\n    }\n    this._domNode.setClassName(`cursor ${this._pluralityClass} ${MOUSE_CURSOR_TEXT_CSS_CLASS_NAME} ${this._renderData.textContentClassName}`);\n    this._domNode.setDisplay('block');\n    this._domNode.setTop(this._renderData.top);\n    this._domNode.setLeft(this._renderData.left);\n    this._domNode.setPaddingLeft(this._renderData.paddingLeft);\n    this._domNode.setWidth(this._renderData.width);\n    this._domNode.setLineHeight(this._renderData.height);\n    this._domNode.setHeight(this._renderData.height);\n    return {\n      domNode: this._domNode.domNode,\n      position: this._position,\n      contentLeft: this._renderData.left,\n      height: this._renderData.height,\n      width: 2\n    };\n  }\n}", "map": {"version": 3, "names": ["dom", "createFastDomNode", "strings", "applyFontInfo", "TextEditorCursorStyle", "Position", "Range", "MOUSE_CURSOR_TEXT_CSS_CLASS_NAME", "ViewCursorRenderData", "constructor", "top", "left", "paddingLeft", "width", "height", "textContent", "textContentClassName", "CursorPlurality", "ViewCursor", "context", "plurality", "_context", "options", "configuration", "fontInfo", "get", "_cursorStyle", "_lineHeight", "_typicalHalfwidthCharacterWidth", "typicalHalfwidthCharacterWidth", "_lineCursorWidth", "Math", "min", "_isVisible", "_domNode", "document", "createElement", "setClassName", "setHeight", "setTop", "setLeft", "setDisplay", "_position", "_pluralityClass", "setPlurality", "_last<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_renderData", "getDomNode", "getPosition", "Single", "MultiPrimary", "MultiSecondary", "show", "setVisibility", "hide", "onConfigurationChanged", "e", "onCursorPositionChanged", "position", "pauseAnimation", "domNode", "style", "transitionProperty", "_getGraphemeAwarePosition", "lineNumber", "column", "lineContent", "viewModel", "get<PERSON>ineC<PERSON>nt", "startOffset", "endOffset", "getCharContainingOffset", "substring", "_prepareRender", "ctx", "nextGrapheme", "Line", "LineThin", "visibleRange", "visibleRangeForPosition", "outsideRenderedLine", "window", "getWindow", "computeScreenAwareSize", "_getTokenClassName", "getVerticalOffsetForLineNumber", "bigNumbersDelta", "visibleRangeForCharacter", "linesVisibleRangesForRange", "length", "firstVisibleRangeForCharacter", "ranges", "range", "Block", "Underline", "UnderlineThin", "lineData", "getViewLineData", "tokenIndex", "tokens", "findTokenIndexAtOffset", "getClassName", "prepareRender", "render", "setPaddingLeft", "<PERSON><PERSON><PERSON><PERSON>", "setLineHeight", "contentLeft"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/browser/viewParts/viewCursors/viewCursor.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as dom from '../../../../base/browser/dom.js';\nimport { createFastDomNode } from '../../../../base/browser/fastDomNode.js';\nimport * as strings from '../../../../base/common/strings.js';\nimport { applyFontInfo } from '../../config/domFontInfo.js';\nimport { TextEditorCursorStyle } from '../../../common/config/editorOptions.js';\nimport { Position } from '../../../common/core/position.js';\nimport { Range } from '../../../common/core/range.js';\nimport { MOUSE_CURSOR_TEXT_CSS_CLASS_NAME } from '../../../../base/browser/ui/mouseCursor/mouseCursor.js';\nclass ViewCursorRenderData {\n    constructor(top, left, paddingLeft, width, height, textContent, textContentClassName) {\n        this.top = top;\n        this.left = left;\n        this.paddingLeft = paddingLeft;\n        this.width = width;\n        this.height = height;\n        this.textContent = textContent;\n        this.textContentClassName = textContentClassName;\n    }\n}\nexport var CursorPlurality;\n(function (CursorPlurality) {\n    CursorPlurality[CursorPlurality[\"Single\"] = 0] = \"Single\";\n    CursorPlurality[CursorPlurality[\"MultiPrimary\"] = 1] = \"MultiPrimary\";\n    CursorPlurality[CursorPlurality[\"MultiSecondary\"] = 2] = \"MultiSecondary\";\n})(CursorPlurality || (CursorPlurality = {}));\nexport class ViewCursor {\n    constructor(context, plurality) {\n        this._context = context;\n        const options = this._context.configuration.options;\n        const fontInfo = options.get(50 /* EditorOption.fontInfo */);\n        this._cursorStyle = options.get(28 /* EditorOption.cursorStyle */);\n        this._lineHeight = options.get(67 /* EditorOption.lineHeight */);\n        this._typicalHalfwidthCharacterWidth = fontInfo.typicalHalfwidthCharacterWidth;\n        this._lineCursorWidth = Math.min(options.get(31 /* EditorOption.cursorWidth */), this._typicalHalfwidthCharacterWidth);\n        this._isVisible = true;\n        // Create the dom node\n        this._domNode = createFastDomNode(document.createElement('div'));\n        this._domNode.setClassName(`cursor ${MOUSE_CURSOR_TEXT_CSS_CLASS_NAME}`);\n        this._domNode.setHeight(this._lineHeight);\n        this._domNode.setTop(0);\n        this._domNode.setLeft(0);\n        applyFontInfo(this._domNode, fontInfo);\n        this._domNode.setDisplay('none');\n        this._position = new Position(1, 1);\n        this._pluralityClass = '';\n        this.setPlurality(plurality);\n        this._lastRenderedContent = '';\n        this._renderData = null;\n    }\n    getDomNode() {\n        return this._domNode;\n    }\n    getPosition() {\n        return this._position;\n    }\n    setPlurality(plurality) {\n        switch (plurality) {\n            default:\n            case CursorPlurality.Single:\n                this._pluralityClass = '';\n                break;\n            case CursorPlurality.MultiPrimary:\n                this._pluralityClass = 'cursor-primary';\n                break;\n            case CursorPlurality.MultiSecondary:\n                this._pluralityClass = 'cursor-secondary';\n                break;\n        }\n    }\n    show() {\n        if (!this._isVisible) {\n            this._domNode.setVisibility('inherit');\n            this._isVisible = true;\n        }\n    }\n    hide() {\n        if (this._isVisible) {\n            this._domNode.setVisibility('hidden');\n            this._isVisible = false;\n        }\n    }\n    onConfigurationChanged(e) {\n        const options = this._context.configuration.options;\n        const fontInfo = options.get(50 /* EditorOption.fontInfo */);\n        this._cursorStyle = options.get(28 /* EditorOption.cursorStyle */);\n        this._lineHeight = options.get(67 /* EditorOption.lineHeight */);\n        this._typicalHalfwidthCharacterWidth = fontInfo.typicalHalfwidthCharacterWidth;\n        this._lineCursorWidth = Math.min(options.get(31 /* EditorOption.cursorWidth */), this._typicalHalfwidthCharacterWidth);\n        applyFontInfo(this._domNode, fontInfo);\n        return true;\n    }\n    onCursorPositionChanged(position, pauseAnimation) {\n        if (pauseAnimation) {\n            this._domNode.domNode.style.transitionProperty = 'none';\n        }\n        else {\n            this._domNode.domNode.style.transitionProperty = '';\n        }\n        this._position = position;\n        return true;\n    }\n    /**\n     * If `this._position` is inside a grapheme, returns the position where the grapheme starts.\n     * Also returns the next grapheme.\n     */\n    _getGraphemeAwarePosition() {\n        const { lineNumber, column } = this._position;\n        const lineContent = this._context.viewModel.getLineContent(lineNumber);\n        const [startOffset, endOffset] = strings.getCharContainingOffset(lineContent, column - 1);\n        return [new Position(lineNumber, startOffset + 1), lineContent.substring(startOffset, endOffset)];\n    }\n    _prepareRender(ctx) {\n        let textContent = '';\n        let textContentClassName = '';\n        const [position, nextGrapheme] = this._getGraphemeAwarePosition();\n        if (this._cursorStyle === TextEditorCursorStyle.Line || this._cursorStyle === TextEditorCursorStyle.LineThin) {\n            const visibleRange = ctx.visibleRangeForPosition(position);\n            if (!visibleRange || visibleRange.outsideRenderedLine) {\n                // Outside viewport\n                return null;\n            }\n            const window = dom.getWindow(this._domNode.domNode);\n            let width;\n            if (this._cursorStyle === TextEditorCursorStyle.Line) {\n                width = dom.computeScreenAwareSize(window, this._lineCursorWidth > 0 ? this._lineCursorWidth : 2);\n                if (width > 2) {\n                    textContent = nextGrapheme;\n                    textContentClassName = this._getTokenClassName(position);\n                }\n            }\n            else {\n                width = dom.computeScreenAwareSize(window, 1);\n            }\n            let left = visibleRange.left;\n            let paddingLeft = 0;\n            if (width >= 2 && left >= 1) {\n                // shift the cursor a bit between the characters\n                paddingLeft = 1;\n                left -= paddingLeft;\n            }\n            const top = ctx.getVerticalOffsetForLineNumber(position.lineNumber) - ctx.bigNumbersDelta;\n            return new ViewCursorRenderData(top, left, paddingLeft, width, this._lineHeight, textContent, textContentClassName);\n        }\n        const visibleRangeForCharacter = ctx.linesVisibleRangesForRange(new Range(position.lineNumber, position.column, position.lineNumber, position.column + nextGrapheme.length), false);\n        if (!visibleRangeForCharacter || visibleRangeForCharacter.length === 0) {\n            // Outside viewport\n            return null;\n        }\n        const firstVisibleRangeForCharacter = visibleRangeForCharacter[0];\n        if (firstVisibleRangeForCharacter.outsideRenderedLine || firstVisibleRangeForCharacter.ranges.length === 0) {\n            // Outside viewport\n            return null;\n        }\n        const range = firstVisibleRangeForCharacter.ranges[0];\n        const width = (nextGrapheme === '\\t'\n            ? this._typicalHalfwidthCharacterWidth\n            : (range.width < 1\n                ? this._typicalHalfwidthCharacterWidth\n                : range.width));\n        if (this._cursorStyle === TextEditorCursorStyle.Block) {\n            textContent = nextGrapheme;\n            textContentClassName = this._getTokenClassName(position);\n        }\n        let top = ctx.getVerticalOffsetForLineNumber(position.lineNumber) - ctx.bigNumbersDelta;\n        let height = this._lineHeight;\n        // Underline might interfere with clicking\n        if (this._cursorStyle === TextEditorCursorStyle.Underline || this._cursorStyle === TextEditorCursorStyle.UnderlineThin) {\n            top += this._lineHeight - 2;\n            height = 2;\n        }\n        return new ViewCursorRenderData(top, range.left, 0, width, height, textContent, textContentClassName);\n    }\n    _getTokenClassName(position) {\n        const lineData = this._context.viewModel.getViewLineData(position.lineNumber);\n        const tokenIndex = lineData.tokens.findTokenIndexAtOffset(position.column - 1);\n        return lineData.tokens.getClassName(tokenIndex);\n    }\n    prepareRender(ctx) {\n        this._renderData = this._prepareRender(ctx);\n    }\n    render(ctx) {\n        if (!this._renderData) {\n            this._domNode.setDisplay('none');\n            return null;\n        }\n        if (this._lastRenderedContent !== this._renderData.textContent) {\n            this._lastRenderedContent = this._renderData.textContent;\n            this._domNode.domNode.textContent = this._lastRenderedContent;\n        }\n        this._domNode.setClassName(`cursor ${this._pluralityClass} ${MOUSE_CURSOR_TEXT_CSS_CLASS_NAME} ${this._renderData.textContentClassName}`);\n        this._domNode.setDisplay('block');\n        this._domNode.setTop(this._renderData.top);\n        this._domNode.setLeft(this._renderData.left);\n        this._domNode.setPaddingLeft(this._renderData.paddingLeft);\n        this._domNode.setWidth(this._renderData.width);\n        this._domNode.setLineHeight(this._renderData.height);\n        this._domNode.setHeight(this._renderData.height);\n        return {\n            domNode: this._domNode.domNode,\n            position: this._position,\n            contentLeft: this._renderData.left,\n            height: this._renderData.height,\n            width: 2\n        };\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,KAAKA,GAAG,MAAM,iCAAiC;AACtD,SAASC,iBAAiB,QAAQ,yCAAyC;AAC3E,OAAO,KAAKC,OAAO,MAAM,oCAAoC;AAC7D,SAASC,aAAa,QAAQ,6BAA6B;AAC3D,SAASC,qBAAqB,QAAQ,yCAAyC;AAC/E,SAASC,QAAQ,QAAQ,kCAAkC;AAC3D,SAASC,KAAK,QAAQ,+BAA+B;AACrD,SAASC,gCAAgC,QAAQ,wDAAwD;AACzG,MAAMC,oBAAoB,CAAC;EACvBC,WAAWA,CAACC,GAAG,EAAEC,IAAI,EAAEC,WAAW,EAAEC,KAAK,EAAEC,MAAM,EAAEC,WAAW,EAAEC,oBAAoB,EAAE;IAClF,IAAI,CAACN,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,oBAAoB,GAAGA,oBAAoB;EACpD;AACJ;AACA,OAAO,IAAIC,eAAe,gBACzB,UAAUA,eAAe,EAAE;EACxBA,eAAe,CAACA,eAAe,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ;EACzDA,eAAe,CAACA,eAAe,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,GAAG,cAAc;EACrEA,eAAe,CAACA,eAAe,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,GAAG,gBAAgB;EAAC,OAHnEA,eAAe;AAI1B,CAAC,CAAEA,eAAe,IAAuB,CAAC,CAAE,CALlB;AAM1B,OAAO,MAAMC,UAAU,CAAC;EACpBT,WAAWA,CAACU,OAAO,EAAEC,SAAS,EAAE;IAC5B,IAAI,CAACC,QAAQ,GAAGF,OAAO;IACvB,MAAMG,OAAO,GAAG,IAAI,CAACD,QAAQ,CAACE,aAAa,CAACD,OAAO;IACnD,MAAME,QAAQ,GAAGF,OAAO,CAACG,GAAG,CAAC,EAAE,CAAC,2BAA2B,CAAC;IAC5D,IAAI,CAACC,YAAY,GAAGJ,OAAO,CAACG,GAAG,CAAC,EAAE,CAAC,8BAA8B,CAAC;IAClE,IAAI,CAACE,WAAW,GAAGL,OAAO,CAACG,GAAG,CAAC,EAAE,CAAC,6BAA6B,CAAC;IAChE,IAAI,CAACG,+BAA+B,GAAGJ,QAAQ,CAACK,8BAA8B;IAC9E,IAAI,CAACC,gBAAgB,GAAGC,IAAI,CAACC,GAAG,CAACV,OAAO,CAACG,GAAG,CAAC,EAAE,CAAC,8BAA8B,CAAC,EAAE,IAAI,CAACG,+BAA+B,CAAC;IACtH,IAAI,CAACK,UAAU,GAAG,IAAI;IACtB;IACA,IAAI,CAACC,QAAQ,GAAGjC,iBAAiB,CAACkC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC,CAAC;IAChE,IAAI,CAACF,QAAQ,CAACG,YAAY,CAAC,UAAU9B,gCAAgC,EAAE,CAAC;IACxE,IAAI,CAAC2B,QAAQ,CAACI,SAAS,CAAC,IAAI,CAACX,WAAW,CAAC;IACzC,IAAI,CAACO,QAAQ,CAACK,MAAM,CAAC,CAAC,CAAC;IACvB,IAAI,CAACL,QAAQ,CAACM,OAAO,CAAC,CAAC,CAAC;IACxBrC,aAAa,CAAC,IAAI,CAAC+B,QAAQ,EAAEV,QAAQ,CAAC;IACtC,IAAI,CAACU,QAAQ,CAACO,UAAU,CAAC,MAAM,CAAC;IAChC,IAAI,CAACC,SAAS,GAAG,IAAIrC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;IACnC,IAAI,CAACsC,eAAe,GAAG,EAAE;IACzB,IAAI,CAACC,YAAY,CAACxB,SAAS,CAAC;IAC5B,IAAI,CAACyB,oBAAoB,GAAG,EAAE;IAC9B,IAAI,CAACC,WAAW,GAAG,IAAI;EAC3B;EACAC,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAACb,QAAQ;EACxB;EACAc,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACN,SAAS;EACzB;EACAE,YAAYA,CAACxB,SAAS,EAAE;IACpB,QAAQA,SAAS;MACb;MACA,KAAKH,eAAe,CAACgC,MAAM;QACvB,IAAI,CAACN,eAAe,GAAG,EAAE;QACzB;MACJ,KAAK1B,eAAe,CAACiC,YAAY;QAC7B,IAAI,CAACP,eAAe,GAAG,gBAAgB;QACvC;MACJ,KAAK1B,eAAe,CAACkC,cAAc;QAC/B,IAAI,CAACR,eAAe,GAAG,kBAAkB;QACzC;IACR;EACJ;EACAS,IAAIA,CAAA,EAAG;IACH,IAAI,CAAC,IAAI,CAACnB,UAAU,EAAE;MAClB,IAAI,CAACC,QAAQ,CAACmB,aAAa,CAAC,SAAS,CAAC;MACtC,IAAI,CAACpB,UAAU,GAAG,IAAI;IAC1B;EACJ;EACAqB,IAAIA,CAAA,EAAG;IACH,IAAI,IAAI,CAACrB,UAAU,EAAE;MACjB,IAAI,CAACC,QAAQ,CAACmB,aAAa,CAAC,QAAQ,CAAC;MACrC,IAAI,CAACpB,UAAU,GAAG,KAAK;IAC3B;EACJ;EACAsB,sBAAsBA,CAACC,CAAC,EAAE;IACtB,MAAMlC,OAAO,GAAG,IAAI,CAACD,QAAQ,CAACE,aAAa,CAACD,OAAO;IACnD,MAAME,QAAQ,GAAGF,OAAO,CAACG,GAAG,CAAC,EAAE,CAAC,2BAA2B,CAAC;IAC5D,IAAI,CAACC,YAAY,GAAGJ,OAAO,CAACG,GAAG,CAAC,EAAE,CAAC,8BAA8B,CAAC;IAClE,IAAI,CAACE,WAAW,GAAGL,OAAO,CAACG,GAAG,CAAC,EAAE,CAAC,6BAA6B,CAAC;IAChE,IAAI,CAACG,+BAA+B,GAAGJ,QAAQ,CAACK,8BAA8B;IAC9E,IAAI,CAACC,gBAAgB,GAAGC,IAAI,CAACC,GAAG,CAACV,OAAO,CAACG,GAAG,CAAC,EAAE,CAAC,8BAA8B,CAAC,EAAE,IAAI,CAACG,+BAA+B,CAAC;IACtHzB,aAAa,CAAC,IAAI,CAAC+B,QAAQ,EAAEV,QAAQ,CAAC;IACtC,OAAO,IAAI;EACf;EACAiC,uBAAuBA,CAACC,QAAQ,EAAEC,cAAc,EAAE;IAC9C,IAAIA,cAAc,EAAE;MAChB,IAAI,CAACzB,QAAQ,CAAC0B,OAAO,CAACC,KAAK,CAACC,kBAAkB,GAAG,MAAM;IAC3D,CAAC,MACI;MACD,IAAI,CAAC5B,QAAQ,CAAC0B,OAAO,CAACC,KAAK,CAACC,kBAAkB,GAAG,EAAE;IACvD;IACA,IAAI,CAACpB,SAAS,GAAGgB,QAAQ;IACzB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIK,yBAAyBA,CAAA,EAAG;IACxB,MAAM;MAAEC,UAAU;MAAEC;IAAO,CAAC,GAAG,IAAI,CAACvB,SAAS;IAC7C,MAAMwB,WAAW,GAAG,IAAI,CAAC7C,QAAQ,CAAC8C,SAAS,CAACC,cAAc,CAACJ,UAAU,CAAC;IACtE,MAAM,CAACK,WAAW,EAAEC,SAAS,CAAC,GAAGpE,OAAO,CAACqE,uBAAuB,CAACL,WAAW,EAAED,MAAM,GAAG,CAAC,CAAC;IACzF,OAAO,CAAC,IAAI5D,QAAQ,CAAC2D,UAAU,EAAEK,WAAW,GAAG,CAAC,CAAC,EAAEH,WAAW,CAACM,SAAS,CAACH,WAAW,EAAEC,SAAS,CAAC,CAAC;EACrG;EACAG,cAAcA,CAACC,GAAG,EAAE;IAChB,IAAI3D,WAAW,GAAG,EAAE;IACpB,IAAIC,oBAAoB,GAAG,EAAE;IAC7B,MAAM,CAAC0C,QAAQ,EAAEiB,YAAY,CAAC,GAAG,IAAI,CAACZ,yBAAyB,CAAC,CAAC;IACjE,IAAI,IAAI,CAACrC,YAAY,KAAKtB,qBAAqB,CAACwE,IAAI,IAAI,IAAI,CAAClD,YAAY,KAAKtB,qBAAqB,CAACyE,QAAQ,EAAE;MAC1G,MAAMC,YAAY,GAAGJ,GAAG,CAACK,uBAAuB,CAACrB,QAAQ,CAAC;MAC1D,IAAI,CAACoB,YAAY,IAAIA,YAAY,CAACE,mBAAmB,EAAE;QACnD;QACA,OAAO,IAAI;MACf;MACA,MAAMC,MAAM,GAAGjF,GAAG,CAACkF,SAAS,CAAC,IAAI,CAAChD,QAAQ,CAAC0B,OAAO,CAAC;MACnD,IAAI/C,KAAK;MACT,IAAI,IAAI,CAACa,YAAY,KAAKtB,qBAAqB,CAACwE,IAAI,EAAE;QAClD/D,KAAK,GAAGb,GAAG,CAACmF,sBAAsB,CAACF,MAAM,EAAE,IAAI,CAACnD,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACA,gBAAgB,GAAG,CAAC,CAAC;QACjG,IAAIjB,KAAK,GAAG,CAAC,EAAE;UACXE,WAAW,GAAG4D,YAAY;UAC1B3D,oBAAoB,GAAG,IAAI,CAACoE,kBAAkB,CAAC1B,QAAQ,CAAC;QAC5D;MACJ,CAAC,MACI;QACD7C,KAAK,GAAGb,GAAG,CAACmF,sBAAsB,CAACF,MAAM,EAAE,CAAC,CAAC;MACjD;MACA,IAAItE,IAAI,GAAGmE,YAAY,CAACnE,IAAI;MAC5B,IAAIC,WAAW,GAAG,CAAC;MACnB,IAAIC,KAAK,IAAI,CAAC,IAAIF,IAAI,IAAI,CAAC,EAAE;QACzB;QACAC,WAAW,GAAG,CAAC;QACfD,IAAI,IAAIC,WAAW;MACvB;MACA,MAAMF,GAAG,GAAGgE,GAAG,CAACW,8BAA8B,CAAC3B,QAAQ,CAACM,UAAU,CAAC,GAAGU,GAAG,CAACY,eAAe;MACzF,OAAO,IAAI9E,oBAAoB,CAACE,GAAG,EAAEC,IAAI,EAAEC,WAAW,EAAEC,KAAK,EAAE,IAAI,CAACc,WAAW,EAAEZ,WAAW,EAAEC,oBAAoB,CAAC;IACvH;IACA,MAAMuE,wBAAwB,GAAGb,GAAG,CAACc,0BAA0B,CAAC,IAAIlF,KAAK,CAACoD,QAAQ,CAACM,UAAU,EAAEN,QAAQ,CAACO,MAAM,EAAEP,QAAQ,CAACM,UAAU,EAAEN,QAAQ,CAACO,MAAM,GAAGU,YAAY,CAACc,MAAM,CAAC,EAAE,KAAK,CAAC;IACnL,IAAI,CAACF,wBAAwB,IAAIA,wBAAwB,CAACE,MAAM,KAAK,CAAC,EAAE;MACpE;MACA,OAAO,IAAI;IACf;IACA,MAAMC,6BAA6B,GAAGH,wBAAwB,CAAC,CAAC,CAAC;IACjE,IAAIG,6BAA6B,CAACV,mBAAmB,IAAIU,6BAA6B,CAACC,MAAM,CAACF,MAAM,KAAK,CAAC,EAAE;MACxG;MACA,OAAO,IAAI;IACf;IACA,MAAMG,KAAK,GAAGF,6BAA6B,CAACC,MAAM,CAAC,CAAC,CAAC;IACrD,MAAM9E,KAAK,GAAI8D,YAAY,KAAK,IAAI,GAC9B,IAAI,CAAC/C,+BAA+B,GACnCgE,KAAK,CAAC/E,KAAK,GAAG,CAAC,GACZ,IAAI,CAACe,+BAA+B,GACpCgE,KAAK,CAAC/E,KAAO;IACvB,IAAI,IAAI,CAACa,YAAY,KAAKtB,qBAAqB,CAACyF,KAAK,EAAE;MACnD9E,WAAW,GAAG4D,YAAY;MAC1B3D,oBAAoB,GAAG,IAAI,CAACoE,kBAAkB,CAAC1B,QAAQ,CAAC;IAC5D;IACA,IAAIhD,GAAG,GAAGgE,GAAG,CAACW,8BAA8B,CAAC3B,QAAQ,CAACM,UAAU,CAAC,GAAGU,GAAG,CAACY,eAAe;IACvF,IAAIxE,MAAM,GAAG,IAAI,CAACa,WAAW;IAC7B;IACA,IAAI,IAAI,CAACD,YAAY,KAAKtB,qBAAqB,CAAC0F,SAAS,IAAI,IAAI,CAACpE,YAAY,KAAKtB,qBAAqB,CAAC2F,aAAa,EAAE;MACpHrF,GAAG,IAAI,IAAI,CAACiB,WAAW,GAAG,CAAC;MAC3Bb,MAAM,GAAG,CAAC;IACd;IACA,OAAO,IAAIN,oBAAoB,CAACE,GAAG,EAAEkF,KAAK,CAACjF,IAAI,EAAE,CAAC,EAAEE,KAAK,EAAEC,MAAM,EAAEC,WAAW,EAAEC,oBAAoB,CAAC;EACzG;EACAoE,kBAAkBA,CAAC1B,QAAQ,EAAE;IACzB,MAAMsC,QAAQ,GAAG,IAAI,CAAC3E,QAAQ,CAAC8C,SAAS,CAAC8B,eAAe,CAACvC,QAAQ,CAACM,UAAU,CAAC;IAC7E,MAAMkC,UAAU,GAAGF,QAAQ,CAACG,MAAM,CAACC,sBAAsB,CAAC1C,QAAQ,CAACO,MAAM,GAAG,CAAC,CAAC;IAC9E,OAAO+B,QAAQ,CAACG,MAAM,CAACE,YAAY,CAACH,UAAU,CAAC;EACnD;EACAI,aAAaA,CAAC5B,GAAG,EAAE;IACf,IAAI,CAAC5B,WAAW,GAAG,IAAI,CAAC2B,cAAc,CAACC,GAAG,CAAC;EAC/C;EACA6B,MAAMA,CAAC7B,GAAG,EAAE;IACR,IAAI,CAAC,IAAI,CAAC5B,WAAW,EAAE;MACnB,IAAI,CAACZ,QAAQ,CAACO,UAAU,CAAC,MAAM,CAAC;MAChC,OAAO,IAAI;IACf;IACA,IAAI,IAAI,CAACI,oBAAoB,KAAK,IAAI,CAACC,WAAW,CAAC/B,WAAW,EAAE;MAC5D,IAAI,CAAC8B,oBAAoB,GAAG,IAAI,CAACC,WAAW,CAAC/B,WAAW;MACxD,IAAI,CAACmB,QAAQ,CAAC0B,OAAO,CAAC7C,WAAW,GAAG,IAAI,CAAC8B,oBAAoB;IACjE;IACA,IAAI,CAACX,QAAQ,CAACG,YAAY,CAAC,UAAU,IAAI,CAACM,eAAe,IAAIpC,gCAAgC,IAAI,IAAI,CAACuC,WAAW,CAAC9B,oBAAoB,EAAE,CAAC;IACzI,IAAI,CAACkB,QAAQ,CAACO,UAAU,CAAC,OAAO,CAAC;IACjC,IAAI,CAACP,QAAQ,CAACK,MAAM,CAAC,IAAI,CAACO,WAAW,CAACpC,GAAG,CAAC;IAC1C,IAAI,CAACwB,QAAQ,CAACM,OAAO,CAAC,IAAI,CAACM,WAAW,CAACnC,IAAI,CAAC;IAC5C,IAAI,CAACuB,QAAQ,CAACsE,cAAc,CAAC,IAAI,CAAC1D,WAAW,CAAClC,WAAW,CAAC;IAC1D,IAAI,CAACsB,QAAQ,CAACuE,QAAQ,CAAC,IAAI,CAAC3D,WAAW,CAACjC,KAAK,CAAC;IAC9C,IAAI,CAACqB,QAAQ,CAACwE,aAAa,CAAC,IAAI,CAAC5D,WAAW,CAAChC,MAAM,CAAC;IACpD,IAAI,CAACoB,QAAQ,CAACI,SAAS,CAAC,IAAI,CAACQ,WAAW,CAAChC,MAAM,CAAC;IAChD,OAAO;MACH8C,OAAO,EAAE,IAAI,CAAC1B,QAAQ,CAAC0B,OAAO;MAC9BF,QAAQ,EAAE,IAAI,CAAChB,SAAS;MACxBiE,WAAW,EAAE,IAAI,CAAC7D,WAAW,CAACnC,IAAI;MAClCG,MAAM,EAAE,IAAI,CAACgC,WAAW,CAAChC,MAAM;MAC/BD,KAAK,EAAE;IACX,CAAC;EACL;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}