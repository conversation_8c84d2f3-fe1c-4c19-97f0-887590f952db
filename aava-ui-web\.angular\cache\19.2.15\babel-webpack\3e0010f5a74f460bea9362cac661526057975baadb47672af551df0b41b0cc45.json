{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as dom from '../../../base/browser/dom.js';\nimport { FindInput } from '../../../base/browser/ui/findinput/findInput.js';\nimport { Disposable } from '../../../base/common/lifecycle.js';\nimport Severity from '../../../base/common/severity.js';\nimport './media/quickInput.css';\nconst $ = dom.$;\nexport class QuickInputBox extends Disposable {\n  constructor(parent, inputBoxStyles, toggleStyles) {\n    super();\n    this.parent = parent;\n    this.onKeyDown = handler => {\n      return dom.addStandardDisposableListener(this.findInput.inputBox.inputElement, dom.EventType.KEY_DOWN, handler);\n    };\n    this.onDidChange = handler => {\n      return this.findInput.onDidChange(handler);\n    };\n    this.container = dom.append(this.parent, $('.quick-input-box'));\n    this.findInput = this._register(new FindInput(this.container, undefined, {\n      label: '',\n      inputBoxStyles,\n      toggleStyles\n    }));\n    const input = this.findInput.inputBox.inputElement;\n    input.role = 'combobox';\n    input.ariaHasPopup = 'menu';\n    input.ariaAutoComplete = 'list';\n    input.ariaExpanded = 'true';\n  }\n  get value() {\n    return this.findInput.getValue();\n  }\n  set value(value) {\n    this.findInput.setValue(value);\n  }\n  select(range = null) {\n    this.findInput.inputBox.select(range);\n  }\n  getSelection() {\n    return this.findInput.inputBox.getSelection();\n  }\n  isSelectionAtEnd() {\n    return this.findInput.inputBox.isSelectionAtEnd();\n  }\n  get placeholder() {\n    return this.findInput.inputBox.inputElement.getAttribute('placeholder') || '';\n  }\n  set placeholder(placeholder) {\n    this.findInput.inputBox.setPlaceHolder(placeholder);\n  }\n  get password() {\n    return this.findInput.inputBox.inputElement.type === 'password';\n  }\n  set password(password) {\n    this.findInput.inputBox.inputElement.type = password ? 'password' : 'text';\n  }\n  set enabled(enabled) {\n    // We can't disable the input box because it is still used for\n    // navigating the list. Instead, we disable the list and the OK\n    // so that nothing can be selected.\n    // TODO: should this be what we do for all find inputs? Or maybe some _other_ API\n    // on findInput to change it to readonly?\n    this.findInput.inputBox.inputElement.toggleAttribute('readonly', !enabled);\n    // TODO: styles of the quick pick need to be moved to the CSS instead of being in line\n    // so things like this can be done in CSS\n    // this.findInput.inputBox.inputElement.classList.toggle('disabled', !enabled);\n  }\n  set toggles(toggles) {\n    this.findInput.setAdditionalToggles(toggles);\n  }\n  setAttribute(name, value) {\n    this.findInput.inputBox.inputElement.setAttribute(name, value);\n  }\n  showDecoration(decoration) {\n    if (decoration === Severity.Ignore) {\n      this.findInput.clearMessage();\n    } else {\n      this.findInput.showMessage({\n        type: decoration === Severity.Info ? 1 /* MessageType.INFO */ : decoration === Severity.Warning ? 2 /* MessageType.WARNING */ : 3 /* MessageType.ERROR */,\n        content: ''\n      });\n    }\n  }\n  stylesForType(decoration) {\n    return this.findInput.inputBox.stylesForType(decoration === Severity.Info ? 1 /* MessageType.INFO */ : decoration === Severity.Warning ? 2 /* MessageType.WARNING */ : 3 /* MessageType.ERROR */);\n  }\n  setFocus() {\n    this.findInput.focus();\n  }\n  layout() {\n    this.findInput.inputBox.layout();\n  }\n}", "map": {"version": 3, "names": ["dom", "FindInput", "Disposable", "Severity", "$", "QuickInputBox", "constructor", "parent", "inputBoxStyles", "toggleStyles", "onKeyDown", "handler", "addStandardDisposableListener", "findInput", "inputBox", "inputElement", "EventType", "KEY_DOWN", "onDidChange", "container", "append", "_register", "undefined", "label", "input", "role", "aria<PERSON>as<PERSON><PERSON><PERSON>", "ariaAutoComplete", "ariaExpanded", "value", "getValue", "setValue", "select", "range", "getSelection", "isSelectionAtEnd", "placeholder", "getAttribute", "setPlaceHolder", "password", "type", "enabled", "toggleAttribute", "toggles", "setAdditionalToggles", "setAttribute", "name", "showDecoration", "decoration", "Ignore", "clearMessage", "showMessage", "Info", "Warning", "content", "stylesForType", "setFocus", "focus", "layout"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/platform/quickinput/browser/quickInputBox.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as dom from '../../../base/browser/dom.js';\nimport { FindInput } from '../../../base/browser/ui/findinput/findInput.js';\nimport { Disposable } from '../../../base/common/lifecycle.js';\nimport Severity from '../../../base/common/severity.js';\nimport './media/quickInput.css';\nconst $ = dom.$;\nexport class QuickInputBox extends Disposable {\n    constructor(parent, inputBoxStyles, toggleStyles) {\n        super();\n        this.parent = parent;\n        this.onKeyDown = (handler) => {\n            return dom.addStandardDisposableListener(this.findInput.inputBox.inputElement, dom.EventType.KEY_DOWN, handler);\n        };\n        this.onDidChange = (handler) => {\n            return this.findInput.onDidChange(handler);\n        };\n        this.container = dom.append(this.parent, $('.quick-input-box'));\n        this.findInput = this._register(new FindInput(this.container, undefined, { label: '', inputBoxStyles, toggleStyles }));\n        const input = this.findInput.inputBox.inputElement;\n        input.role = 'combobox';\n        input.ariaHasPopup = 'menu';\n        input.ariaAutoComplete = 'list';\n        input.ariaExpanded = 'true';\n    }\n    get value() {\n        return this.findInput.getValue();\n    }\n    set value(value) {\n        this.findInput.setValue(value);\n    }\n    select(range = null) {\n        this.findInput.inputBox.select(range);\n    }\n    getSelection() {\n        return this.findInput.inputBox.getSelection();\n    }\n    isSelectionAtEnd() {\n        return this.findInput.inputBox.isSelectionAtEnd();\n    }\n    get placeholder() {\n        return this.findInput.inputBox.inputElement.getAttribute('placeholder') || '';\n    }\n    set placeholder(placeholder) {\n        this.findInput.inputBox.setPlaceHolder(placeholder);\n    }\n    get password() {\n        return this.findInput.inputBox.inputElement.type === 'password';\n    }\n    set password(password) {\n        this.findInput.inputBox.inputElement.type = password ? 'password' : 'text';\n    }\n    set enabled(enabled) {\n        // We can't disable the input box because it is still used for\n        // navigating the list. Instead, we disable the list and the OK\n        // so that nothing can be selected.\n        // TODO: should this be what we do for all find inputs? Or maybe some _other_ API\n        // on findInput to change it to readonly?\n        this.findInput.inputBox.inputElement.toggleAttribute('readonly', !enabled);\n        // TODO: styles of the quick pick need to be moved to the CSS instead of being in line\n        // so things like this can be done in CSS\n        // this.findInput.inputBox.inputElement.classList.toggle('disabled', !enabled);\n    }\n    set toggles(toggles) {\n        this.findInput.setAdditionalToggles(toggles);\n    }\n    setAttribute(name, value) {\n        this.findInput.inputBox.inputElement.setAttribute(name, value);\n    }\n    showDecoration(decoration) {\n        if (decoration === Severity.Ignore) {\n            this.findInput.clearMessage();\n        }\n        else {\n            this.findInput.showMessage({ type: decoration === Severity.Info ? 1 /* MessageType.INFO */ : decoration === Severity.Warning ? 2 /* MessageType.WARNING */ : 3 /* MessageType.ERROR */, content: '' });\n        }\n    }\n    stylesForType(decoration) {\n        return this.findInput.inputBox.stylesForType(decoration === Severity.Info ? 1 /* MessageType.INFO */ : decoration === Severity.Warning ? 2 /* MessageType.WARNING */ : 3 /* MessageType.ERROR */);\n    }\n    setFocus() {\n        this.findInput.focus();\n    }\n    layout() {\n        this.findInput.inputBox.layout();\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,KAAKA,GAAG,MAAM,8BAA8B;AACnD,SAASC,SAAS,QAAQ,iDAAiD;AAC3E,SAASC,UAAU,QAAQ,mCAAmC;AAC9D,OAAOC,QAAQ,MAAM,kCAAkC;AACvD,OAAO,wBAAwB;AAC/B,MAAMC,CAAC,GAAGJ,GAAG,CAACI,CAAC;AACf,OAAO,MAAMC,aAAa,SAASH,UAAU,CAAC;EAC1CI,WAAWA,CAACC,MAAM,EAAEC,cAAc,EAAEC,YAAY,EAAE;IAC9C,KAAK,CAAC,CAAC;IACP,IAAI,CAACF,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACG,SAAS,GAAIC,OAAO,IAAK;MAC1B,OAAOX,GAAG,CAACY,6BAA6B,CAAC,IAAI,CAACC,SAAS,CAACC,QAAQ,CAACC,YAAY,EAAEf,GAAG,CAACgB,SAAS,CAACC,QAAQ,EAAEN,OAAO,CAAC;IACnH,CAAC;IACD,IAAI,CAACO,WAAW,GAAIP,OAAO,IAAK;MAC5B,OAAO,IAAI,CAACE,SAAS,CAACK,WAAW,CAACP,OAAO,CAAC;IAC9C,CAAC;IACD,IAAI,CAACQ,SAAS,GAAGnB,GAAG,CAACoB,MAAM,CAAC,IAAI,CAACb,MAAM,EAAEH,CAAC,CAAC,kBAAkB,CAAC,CAAC;IAC/D,IAAI,CAACS,SAAS,GAAG,IAAI,CAACQ,SAAS,CAAC,IAAIpB,SAAS,CAAC,IAAI,CAACkB,SAAS,EAAEG,SAAS,EAAE;MAAEC,KAAK,EAAE,EAAE;MAAEf,cAAc;MAAEC;IAAa,CAAC,CAAC,CAAC;IACtH,MAAMe,KAAK,GAAG,IAAI,CAACX,SAAS,CAACC,QAAQ,CAACC,YAAY;IAClDS,KAAK,CAACC,IAAI,GAAG,UAAU;IACvBD,KAAK,CAACE,YAAY,GAAG,MAAM;IAC3BF,KAAK,CAACG,gBAAgB,GAAG,MAAM;IAC/BH,KAAK,CAACI,YAAY,GAAG,MAAM;EAC/B;EACA,IAAIC,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAAChB,SAAS,CAACiB,QAAQ,CAAC,CAAC;EACpC;EACA,IAAID,KAAKA,CAACA,KAAK,EAAE;IACb,IAAI,CAAChB,SAAS,CAACkB,QAAQ,CAACF,KAAK,CAAC;EAClC;EACAG,MAAMA,CAACC,KAAK,GAAG,IAAI,EAAE;IACjB,IAAI,CAACpB,SAAS,CAACC,QAAQ,CAACkB,MAAM,CAACC,KAAK,CAAC;EACzC;EACAC,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAACrB,SAAS,CAACC,QAAQ,CAACoB,YAAY,CAAC,CAAC;EACjD;EACAC,gBAAgBA,CAAA,EAAG;IACf,OAAO,IAAI,CAACtB,SAAS,CAACC,QAAQ,CAACqB,gBAAgB,CAAC,CAAC;EACrD;EACA,IAAIC,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACvB,SAAS,CAACC,QAAQ,CAACC,YAAY,CAACsB,YAAY,CAAC,aAAa,CAAC,IAAI,EAAE;EACjF;EACA,IAAID,WAAWA,CAACA,WAAW,EAAE;IACzB,IAAI,CAACvB,SAAS,CAACC,QAAQ,CAACwB,cAAc,CAACF,WAAW,CAAC;EACvD;EACA,IAAIG,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC1B,SAAS,CAACC,QAAQ,CAACC,YAAY,CAACyB,IAAI,KAAK,UAAU;EACnE;EACA,IAAID,QAAQA,CAACA,QAAQ,EAAE;IACnB,IAAI,CAAC1B,SAAS,CAACC,QAAQ,CAACC,YAAY,CAACyB,IAAI,GAAGD,QAAQ,GAAG,UAAU,GAAG,MAAM;EAC9E;EACA,IAAIE,OAAOA,CAACA,OAAO,EAAE;IACjB;IACA;IACA;IACA;IACA;IACA,IAAI,CAAC5B,SAAS,CAACC,QAAQ,CAACC,YAAY,CAAC2B,eAAe,CAAC,UAAU,EAAE,CAACD,OAAO,CAAC;IAC1E;IACA;IACA;EACJ;EACA,IAAIE,OAAOA,CAACA,OAAO,EAAE;IACjB,IAAI,CAAC9B,SAAS,CAAC+B,oBAAoB,CAACD,OAAO,CAAC;EAChD;EACAE,YAAYA,CAACC,IAAI,EAAEjB,KAAK,EAAE;IACtB,IAAI,CAAChB,SAAS,CAACC,QAAQ,CAACC,YAAY,CAAC8B,YAAY,CAACC,IAAI,EAAEjB,KAAK,CAAC;EAClE;EACAkB,cAAcA,CAACC,UAAU,EAAE;IACvB,IAAIA,UAAU,KAAK7C,QAAQ,CAAC8C,MAAM,EAAE;MAChC,IAAI,CAACpC,SAAS,CAACqC,YAAY,CAAC,CAAC;IACjC,CAAC,MACI;MACD,IAAI,CAACrC,SAAS,CAACsC,WAAW,CAAC;QAAEX,IAAI,EAAEQ,UAAU,KAAK7C,QAAQ,CAACiD,IAAI,GAAG,CAAC,CAAC,yBAAyBJ,UAAU,KAAK7C,QAAQ,CAACkD,OAAO,GAAG,CAAC,CAAC,4BAA4B,CAAC,CAAC;QAAyBC,OAAO,EAAE;MAAG,CAAC,CAAC;IAC1M;EACJ;EACAC,aAAaA,CAACP,UAAU,EAAE;IACtB,OAAO,IAAI,CAACnC,SAAS,CAACC,QAAQ,CAACyC,aAAa,CAACP,UAAU,KAAK7C,QAAQ,CAACiD,IAAI,GAAG,CAAC,CAAC,yBAAyBJ,UAAU,KAAK7C,QAAQ,CAACkD,OAAO,GAAG,CAAC,CAAC,4BAA4B,CAAC,CAAC,uBAAuB,CAAC;EACrM;EACAG,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC3C,SAAS,CAAC4C,KAAK,CAAC,CAAC;EAC1B;EACAC,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC7C,SAAS,CAACC,QAAQ,CAAC4C,MAAM,CAAC,CAAC;EACpC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}