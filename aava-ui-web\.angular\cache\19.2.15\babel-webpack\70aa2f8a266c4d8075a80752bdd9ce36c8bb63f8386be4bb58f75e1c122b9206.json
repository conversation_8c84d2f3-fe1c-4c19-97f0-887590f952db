{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { PageFooterComponent } from '../../components/page-footer/page-footer.component';\nimport agentsConfigData from './constants/agents.json';\nimport { combineLatest, debounceTime, distinctUntilChanged, startWith, Subject, takeUntil } from 'rxjs';\nimport { AvaTextboxComponent, DropdownComponent, IconComponent, TextCardComponent, ButtonComponent } from '@ava/play-comp-library';\nimport { LucideAngularModule } from 'lucide-angular';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { ConsoleCardComponent } from '../../components/console-card/console-card.component';\nimport { TimeAgoPipe } from '../../pipes/time-ago.pipe';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./services/agent-service.service\";\nimport * as i2 from \"../../services/debounced-search.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@angular/common\";\nfunction AgentsComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"ava-button\", 15);\n    i0.ɵɵlistener(\"userClick\", function AgentsComponent_div_8_Template_ava_button_userClick_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onFilterChange(\"individual\"));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"ava-button\", 16);\n    i0.ɵɵlistener(\"userClick\", function AgentsComponent_div_8_Template_ava_button_userClick_2_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onFilterChange(\"collaborative\"));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active\", ctx_r1.selectedFilter === \"individual\");\n    i0.ɵɵproperty(\"variant\", ctx_r1.selectedFilter === \"individual\" ? \"primary\" : \"secondary\")(\"pill\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active\", ctx_r1.selectedFilter === \"collaborative\");\n    i0.ɵɵproperty(\"variant\", ctx_r1.selectedFilter === \"collaborative\" ? \"primary\" : \"secondary\")(\"pill\", true);\n  }\n}\nfunction AgentsComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"div\", 18)(2, \"h5\", 19);\n    i0.ɵɵtext(3, \"No Agents found matching your criteria\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction AgentsComponent_ng_container_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"ava-console-card\", 20);\n    i0.ɵɵpipe(2, \"timeAgo\");\n    i0.ɵɵlistener(\"actionClick\", function AgentsComponent_ng_container_12_Template_ava_console_card_actionClick_1_listener($event) {\n      const agent_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onActionClick($event, agent_r4.id));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const agent_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"title\", agent_r4 == null ? null : agent_r4.title)(\"description\", agent_r4 == null ? null : agent_r4.description)(\"author\", (agent_r4 == null ? null : agent_r4.owner) || \"AAVA\")(\"date\", i0.ɵɵpipeBind1(2, 6, agent_r4 == null ? null : agent_r4.createdDate))(\"actions\", ctx_r1.defaultActions)(\"skeleton\", ctx_r1.isLoading);\n  }\n}\nfunction AgentsComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"div\", 22)(2, \"app-page-footer\", 23);\n    i0.ɵɵlistener(\"pageChange\", function AgentsComponent_div_13_Template_app_page_footer_pageChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onPageChange($event));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"totalItems\", ctx_r1.totalItemsForPagination)(\"currentPage\", ctx_r1.currentPage)(\"itemsPerPage\", ctx_r1.itemsPerPage);\n  }\n}\nexport let AgentsComponent = /*#__PURE__*/(() => {\n  class AgentsComponent {\n    agentService;\n    debouncedSearchService;\n    router;\n    fb;\n    defaultActions = [{\n      id: 'duplicate',\n      icon: 'copy',\n      label: 'Duplicate',\n      tooltip: 'Duplicate'\n    }, {\n      id: 'edit',\n      icon: 'edit',\n      label: 'Edit item',\n      tooltip: 'Edit'\n    }, {\n      id: 'delete',\n      icon: 'trash',\n      label: 'Delete item',\n      tooltip: 'Delete'\n    }, {\n      id: 'run',\n      icon: 'play',\n      label: 'Run application',\n      tooltip: 'Run',\n      isPrimary: true\n    }];\n    allAgents = [];\n    displayedAgents = [];\n    totalRecords = [];\n    allAgentsData = [];\n    individualAgentsData = [];\n    collaborativeAgentsData = [];\n    originalAllAgentsData = [];\n    originalIndividualAgentsData = [];\n    originalCollaborativeAgentsData = [];\n    agentsConfig = agentsConfigData;\n    isLoading = false;\n    error = null;\n    currentPage = 1;\n    itemsPerPage = agentsConfigData.pagination.itemsPerPage;\n    totalPages = 1;\n    collaborativeTotalPages = 1;\n    totalNoOfRecords = 0;\n    destroy$ = new Subject();\n    selectedFilterChange = new Subject();\n    agentsOptions = [{\n      name: 'All',\n      value: 'all'\n    }, {\n      name: 'Owned by me',\n      value: 'owned'\n    }, {\n      name: 'Experience',\n      value: 'experience'\n    }, {\n      name: 'Product',\n      value: 'product'\n    }, {\n      name: 'Data',\n      value: 'data'\n    }, {\n      name: 'Finops',\n      value: 'finops'\n    }, {\n      name: 'Quality Engineering',\n      value: 'quality'\n    }, {\n      name: 'Platform',\n      value: 'platform'\n    }];\n    showDeletePopup = false;\n    agentToDelete = null;\n    showSuccessPopup = false;\n    successPopupTitle = '';\n    successPopupMessage = '';\n    selectedFilter = 'individual';\n    selectedData = null;\n    searchForm;\n    cardSkeletonPlaceholders = Array(11);\n    constructor(agentService, debouncedSearchService, router, fb) {\n      this.agentService = agentService;\n      this.debouncedSearchService = debouncedSearchService;\n      this.router = router;\n      this.fb = fb;\n      this.searchForm = this.fb.group({\n        search: ['']\n      });\n    }\n    ngOnInit() {\n      if (this.selectedFilter === 'collaborative') {\n        this.fetchCollaborativeAgents();\n      } else {\n        this.fetchIndividualAgents();\n      }\n      combineLatest([this.searchForm.get('search').valueChanges.pipe(debounceTime(600), distinctUntilChanged(), startWith('')), this.selectedFilterChange.pipe(startWith(this.selectedFilter))]).pipe(takeUntil(this.destroy$)).subscribe(([searchTermRaw, filterType]) => {\n        const searchTerm = searchTermRaw?.trim().toLowerCase();\n        if (filterType === 'individual' || filterType === 'collaborative') {\n          if (!searchTerm) {\n            this.fetchDefaultDataFor(filterType);\n          } else {\n            this.isLoading = true;\n            this.debouncedSearchService.triggerSearch(searchTerm, 'agents', filterType);\n          }\n        }\n      });\n      this.debouncedSearchService.searchResults$.pipe(takeUntil(this.destroy$)).subscribe({\n        next: response => {\n          const results = this.extractAgentArray(response);\n          const formatted = results.map((agent, index) => ({\n            ...agent,\n            id: agent.useCaseId?.toString() || agent.id?.toString() || index.toString(),\n            title: agent.useCaseName || agent.name || 'Unnamed',\n            name: agent.useCaseName || agent.name || 'Unnamed',\n            description: agent.useCaseDetails || agent.description || agent.goal || 'No description',\n            createdDate: this.formatDate(agent.created_at || agent.createdAt || agent.modifiedAt),\n            userCount: agent.users || 0,\n            type: this.selectedFilter\n          }));\n          if (this.selectedFilter === 'individual') {\n            this.individualAgentsData = formatted;\n          } else {\n            this.collaborativeAgentsData = formatted;\n          }\n          this.totalRecords = formatted;\n          this.updateDisplayedAgents();\n          this.isLoading = false;\n        },\n        error: () => {\n          this.error = 'Something went wrong during search.';\n          this.isLoading = false;\n        }\n      });\n    }\n    fetchDefaultDataFor(filter) {\n      switch (filter) {\n        case 'individual':\n          this.fetchIndividualAgents();\n          break;\n        case 'collaborative':\n          this.fetchCollaborativeAgents();\n          break;\n      }\n    }\n    get shouldShowPagination() {\n      if (this.selectedFilter === 'collaborative') {\n        return this.totalNoOfRecords > 0;\n      }\n      return this.filteredAgents.length > 0;\n    }\n    get totalItemsForPagination() {\n      if (this.selectedFilter === 'collaborative') {\n        return this.totalNoOfRecords + 1;\n      }\n      return this.filteredAgents.length + 1;\n    }\n    get filteredAgents() {\n      switch (this.selectedFilter) {\n        case 'individual':\n          return this.individualAgentsData;\n        case 'collaborative':\n          return this.collaborativeAgentsData;\n        case 'all':\n          return this.allAgentsData;\n        default:\n          return this.totalRecords;\n      }\n    }\n    onActionClick(event, agentId) {\n      switch (event.actionId) {\n        case 'delete':\n          this.confirmDeleteAgent(agentId);\n          break;\n        case 'edit':\n          this.editAgent(agentId);\n          break;\n        case 'duplicate':\n          this.duplicateAgent(agentId);\n          break;\n        case 'run':\n          this.executeAgent(agentId);\n          break;\n        default:\n          break;\n      }\n    }\n    onConfirmDelete() {\n      if (this.agentToDelete) {\n        this.deleteAgent(this.agentToDelete.id);\n        this.closeDeletePopup();\n      }\n    }\n    closeDeletePopup() {\n      this.showDeletePopup = false;\n      this.agentToDelete = null;\n    }\n    closeSuccessPopup() {\n      this.showSuccessPopup = false;\n      this.successPopupTitle = '';\n      this.successPopupMessage = '';\n    }\n    onSuccessConfirm() {\n      this.closeSuccessPopup();\n    }\n    onFilterChange(filter) {\n      this.selectedFilter = filter;\n      this.selectedFilterChange.next(filter);\n      this.searchForm.get('search')?.setValue('');\n      this.currentPage = 1;\n      if (filter !== 'collaborative') {\n        this.totalNoOfRecords = 0;\n      }\n      this.selectedFilter === 'individual' ? this.fetchIndividualAgents() : this.fetchCollaborativeAgents();\n    }\n    onSelectionChange(data) {\n      this.selectedData = data;\n    }\n    onPageChange(page) {\n      this.currentPage = page;\n      this.selectedFilter === 'collaborative' ? this.fetchCollaborativeAgents() : this.updateDisplayedAgents();\n    }\n    onCreateAgent() {\n      this.router.navigate([agentsConfigData.navigation.createRoute]);\n    }\n    fetchCollaborativeAgents() {\n      this.isLoading = true;\n      this.agentService.getCollaborativeAgentsPaginated(this.currentPage, this.itemsPerPage).subscribe({\n        next: response => {\n          this.totalNoOfRecords = response?.totalNoOfRecords ?? 0;\n          const agentsList = this.extractAgentArray(response);\n          const mappedAgents = agentsList.map((agent, index) => ({\n            ...agent,\n            id: agent.id?.toString() || `collab-${index}`,\n            title: agent.name || agent.agentDetails || 'Unnamed Agent',\n            name: agent.name || agent.agentDetails || 'Unnamed Agent',\n            description: agent.description || agent.agentDetails || agent.goal || 'No description',\n            createdDate: this.formatDate(agent.modifiedAt || agent.createdAt || new Date()),\n            userCount: agent.users || 0,\n            type: 'collaborative',\n            createdBy: agent.createdBy || '',\n            role: agent.role || '',\n            backstory: agent.backstory || '',\n            expectedOutput: agent.expectedOutput || ''\n          }));\n          this.collaborativeTotalPages = Math.ceil(this.totalNoOfRecords / this.itemsPerPage);\n          this.totalPages = this.collaborativeTotalPages;\n          this.collaborativeAgentsData = mappedAgents;\n          this.displayedAgents = mappedAgents;\n          this.totalRecords = mappedAgents;\n          this.isLoading = false;\n        },\n        error: error => {\n          console.error('Error fetching collaborative agents:', error);\n          this.error = error.message || 'Failed to load collaborative agents';\n          this.isLoading = false;\n        }\n      });\n    }\n    fetchIndividualAgents() {\n      this.isLoading = true;\n      this.agentService.getAllIndividualAgents().subscribe({\n        next: response => {\n          const agentsList = response?.individualAgents ?? response ?? [];\n          const mappedAgents = agentsList.map(agent => ({\n            ...agent,\n            id: agent.useCaseId?.toString() || agent.id?.toString() || '0',\n            title: agent.useCaseName || agent.name || 'Unnamed Agent',\n            name: agent.useCaseName || agent.name || 'Unnamed Agent',\n            description: agent.useCaseDetails || agent.description || 'No description',\n            createdDate: this.formatDate(agent.created_at || agent.createdAt),\n            userCount: agent.users || 0,\n            type: 'individual'\n          }));\n          this.originalIndividualAgentsData = mappedAgents;\n          this.individualAgentsData = mappedAgents;\n          this.totalRecords = mappedAgents;\n          this.updateDisplayedAgents();\n          this.isLoading = false;\n        },\n        error: error => {\n          console.error('Error fetching individual agents:', error);\n          this.error = error.message || 'Failed to load individual agents';\n          this.isLoading = false;\n        }\n      });\n    }\n    formatDate(dateInput) {\n      const date = new Date(dateInput);\n      return `${date.getMonth() + 1}/${date.getDate()}/${date.getFullYear()}`;\n    }\n    extractAgentArray(response) {\n      if (!response) return [];\n      const possibleArrays = [response.agentDetails, response.data, response.agents, response.collaborativeAgents];\n      for (const arr of possibleArrays) {\n        if (Array.isArray(arr)) return arr;\n      }\n      if (Array.isArray(response)) return response;\n      const firstArrayKey = Object.keys(response).find(key => Array.isArray(response[key]));\n      return firstArrayKey ? response[firstArrayKey] : [];\n    }\n    confirmDeleteAgent(agentId) {\n      const dataMap = {\n        all: this.allAgentsData,\n        individual: this.individualAgentsData,\n        collaborative: this.collaborativeAgentsData\n      };\n      const filteredAgents = dataMap[this.selectedFilter] ?? [];\n      const agentObj = filteredAgents.find(agent => agent.id === agentId) || this.allAgents.find(agent => agent.id === agentId);\n      this.agentToDelete = agentObj;\n      this.showDeletePopup = true;\n    }\n    editAgent(agentId) {\n      const dataMap = {\n        all: this.allAgentsData,\n        individual: this.individualAgentsData,\n        collaborative: this.collaborativeAgentsData\n      };\n      const filteredAgents = dataMap[this.selectedFilter] ?? [];\n      let agent = filteredAgents.find(a => a.id === agentId) || this.allAgents.find(a => a.id === agentId);\n      if (!agent) {\n        return;\n      }\n      let agentType = agent.type;\n      const isCollaborative = this.selectedFilter === 'collaborative' || agent.role || agent.goal || agent.backstory || agent.tools;\n      agentType = agentType ?? (isCollaborative ? 'collaborative' : 'individual');\n      this.router.navigate(['/build/agents', agentType], {\n        queryParams: {\n          id: agentId,\n          mode: 'edit'\n        }\n      });\n    }\n    duplicateAgent(agentId) {\n      const dataMap = {\n        all: this.allAgentsData,\n        individual: this.individualAgentsData,\n        collaborative: this.collaborativeAgentsData\n      };\n      const filteredAgents = dataMap[this.selectedFilter] ?? [];\n      let agent = filteredAgents.find(a => a.id === agentId);\n      if (!agent && this.selectedFilter !== 'all') {\n        agent = this.allAgentsData.find(a => a.id === agentId);\n      }\n      if (!agent) {\n        return;\n      }\n      const isCollaborative = this.selectedFilter === 'collaborative' || agent.role || agent.goal || agent.backstory || agent.tools;\n      const agentType = isCollaborative ? 'collaborative' : 'individual';\n      this.router.navigate(['/build/agents', agentType], {\n        queryParams: {\n          id: agentId,\n          mode: 'duplicate'\n        }\n      });\n    }\n    executeAgent(agentId) {\n      const dataMap = {\n        all: this.allAgentsData,\n        individual: this.individualAgentsData,\n        collaborative: this.collaborativeAgentsData\n      };\n      const agent = dataMap[this.selectedFilter]?.find(a => a.id === agentId) || this.allAgents.find(a => a.id === agentId);\n      if (!agent) {\n        return;\n      }\n      const isCollaborative = this.isCollaborativeAgent(agent);\n      const agentType = isCollaborative ? 'collaborative' : 'individual';\n      this.router.navigate(['/build/agents', agentType, 'execute'], {\n        queryParams: {\n          id: agentId\n        }\n      });\n    }\n    isCollaborativeAgent(agent) {\n      return agent?.type === 'collaborative' || this.selectedFilter === 'collaborative' || agent?.tags?.some(tag => tag.label.toLowerCase() === 'collaborative') || agent?.userType === 'collaborative' || agent?.agentType === 'collaborative' || Boolean(agent?.role || agent?.goal || agent?.backstory || agent?.tools);\n    }\n    deleteAgent(agentId) {\n      const agent = this.getAgentById(agentId);\n      if (!agent) {\n        return;\n      }\n      const {\n        name: agentName,\n        type: agentType\n      } = agent;\n      const deleteApi$ = agentType === 'collaborative' ? this.agentService.deleteCollaborativeAgent(agentId) : this.agentService.deleteAgent(agentId);\n      deleteApi$.subscribe({\n        next: response => {\n          this.removeAgentFromData(agentId);\n          this.updateDisplayedAgents();\n          this.successPopupTitle = 'Success';\n          this.successPopupMessage = response?.message || `Agent \"${agentName}\" deleted successfully`;\n          this.showSuccessPopup = true;\n        },\n        error: error => {\n          console.error('Error deleting agent:', error);\n        }\n      });\n    }\n    updateDisplayedAgents() {\n      const startIndex = (this.currentPage - 1) * 11;\n      const endIndex = startIndex + 11;\n      this.displayedAgents = this.filteredAgents.slice(startIndex, endIndex);\n      this.totalPages = Math.ceil(this.filteredAgents.length / 11);\n    }\n    getAgentById(agentId) {\n      const dataMap = {\n        all: this.allAgentsData,\n        individual: this.individualAgentsData,\n        collaborative: this.collaborativeAgentsData\n      };\n      return dataMap[this.selectedFilter]?.find(a => a.id === agentId) || null;\n    }\n    removeAgentFromData(agentId) {\n      const removeFrom = list => list.filter(agent => agent.id !== agentId);\n      switch (this.selectedFilter) {\n        case 'all':\n          this.allAgentsData = removeFrom(this.allAgentsData);\n          this.originalAllAgentsData = removeFrom(this.originalAllAgentsData);\n          break;\n        case 'individual':\n          this.individualAgentsData = removeFrom(this.individualAgentsData);\n          this.originalIndividualAgentsData = removeFrom(this.originalIndividualAgentsData);\n          break;\n        case 'collaborative':\n          this.collaborativeAgentsData = removeFrom(this.collaborativeAgentsData);\n          this.originalCollaborativeAgentsData = removeFrom(this.originalCollaborativeAgentsData);\n          break;\n      }\n      this.allAgents = removeFrom(this.allAgents);\n      this.totalRecords = removeFrom(this.totalRecords);\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n    }\n    static ɵfac = function AgentsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AgentsComponent)(i0.ɵɵdirectiveInject(i1.AgentServiceService), i0.ɵɵdirectiveInject(i2.DebouncedSearchService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.FormBuilder));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AgentsComponent,\n      selectors: [[\"shared-agents\"]],\n      decls: 14,\n      vars: 11,\n      consts: [[\"id\", \"agents-container\", 1, \"container-fluid\"], [\"id\", \"search-filter-container\", 1, \"row\", \"g-3\"], [1, \"col-12\", \"col-md-8\", \"col-lg-9\", \"col-xl-10\", \"search-section\"], [3, \"formGroup\"], [\"placeholder\", \"Search \\\"Agents\\\"\", \"hoverEffect\", \"glow\", \"pressedEffect\", \"solid\", \"formControlName\", \"search\"], [\"slot\", \"icon-start\", \"iconName\", \"search\", \"iconColor\", \"var(--color-brand-primary)\", 3, \"iconSize\"], [1, \"col-12\", \"col-md-4\", \"col-lg-3\", \"col-xl-2\", \"action-buttons\"], [\"dropdownTitle\", \"choose agent\", 3, \"selectionChange\", \"options\"], [\"class\", \"filter-buttons-section\", 4, \"ngIf\"], [\"id\", \"prompts-card-container\", 1, \"row\", \"g-3\"], [\"iconColor\", \"#144692\", 1, \"col-12\", \"col-sm-6\", \"col-md-4\", \"col-lg-3\", \"col-xl-3\", \"col-xxl-2\", \"mt-5\", 3, \"cardClick\", \"type\", \"iconName\", \"title\", \"isLoading\"], [\"class\", \"col-12 d-flex justify-content-center align-items-center py-5\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"row\", 4, \"ngIf\"], [1, \"filter-buttons-section\"], [\"label\", \"Individual\", \"size\", \"small\", 1, \"filter-btn\", 3, \"userClick\", \"variant\", \"pill\"], [\"label\", \"Collaborative\", \"size\", \"small\", 1, \"filter-btn\", 3, \"userClick\", \"variant\", \"pill\"], [1, \"col-12\", \"d-flex\", \"justify-content-center\", \"align-items-center\", \"py-5\"], [1, \"text-center\"], [1, \"text-muted\"], [\"categoryIcon\", \"bot\", \"categoryTitle\", \"Agents\", \"categoryValue\", \"42\", 1, \"col-12\", \"col-sm-6\", \"col-md-4\", \"col-lg-3\", \"col-xl-3\", \"col-xxl-2\", \"mt-5\", 3, \"actionClick\", \"title\", \"description\", \"author\", \"date\", \"actions\", \"skeleton\"], [1, \"row\"], [1, \"col-12\", \"d-flex\", \"justify-content-center\", \"mt-4\"], [3, \"pageChange\", \"totalItems\", \"currentPage\", \"itemsPerPage\"]],\n      template: function AgentsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"form\", 3)(4, \"ava-textbox\", 4);\n          i0.ɵɵelement(5, \"ava-icon\", 5);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(6, \"div\", 6)(7, \"ava-dropdown\", 7);\n          i0.ɵɵlistener(\"selectionChange\", function AgentsComponent_Template_ava_dropdown_selectionChange_7_listener($event) {\n            return ctx.onSelectionChange($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(8, AgentsComponent_div_8_Template, 3, 8, \"div\", 8);\n          i0.ɵɵelementStart(9, \"div\", 9)(10, \"ava-text-card\", 10);\n          i0.ɵɵlistener(\"cardClick\", function AgentsComponent_Template_ava_text_card_cardClick_10_listener() {\n            return ctx.onCreateAgent();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(11, AgentsComponent_div_11_Template, 4, 0, \"div\", 11)(12, AgentsComponent_ng_container_12_Template, 3, 8, \"ng-container\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(13, AgentsComponent_div_13_Template, 3, 3, \"div\", 13);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"formGroup\", ctx.searchForm);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"iconSize\", 16);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"options\", ctx.agentsOptions);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"type\", \"create\")(\"iconName\", \"plus\")(\"title\", ctx.agentsConfig.labels.createAgent)(\"isLoading\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.displayedAgents.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.isLoading && ctx.displayedAgents.length === 0 ? ctx.cardSkeletonPlaceholders : ctx.displayedAgents);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.shouldShowPagination);\n        }\n      },\n      dependencies: [CommonModule, i5.NgForOf, i5.NgIf, PageFooterComponent, TextCardComponent, AvaTextboxComponent, DropdownComponent, LucideAngularModule, IconComponent, ReactiveFormsModule, i4.ɵNgNoValidate, i4.NgControlStatus, i4.NgControlStatusGroup, i4.FormGroupDirective, i4.FormControlName, ButtonComponent, ConsoleCardComponent, TimeAgoPipe],\n      styles: [\".ava-dropdown {\\n  width: 100% !important;\\n}\\n\\n.mt-5[_ngcontent-%COMP%] {\\n  margin-top: 2rem;\\n}\\n\\n.filter-buttons-section[_ngcontent-%COMP%] {\\n  padding-top: 10px;\\n}\\n\\n  .ava-text-card-container .create-type .ava-card-container .ava-card.card {\\n  position: relative;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n  height: 326px;\\n  border-radius: 16px;\\n  background: var(--card-background-gradient) !important;\\n  box-shadow: var(--card-box-shadow) !important;\\n  transition: transform 0.4s ease-out, box-shadow 0.4s ease-out;\\n  overflow: hidden;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3Byb2plY3RzL3NoYXJlZC9wYWdlcy9hZ2VudHMvYWdlbnRzLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0Usc0JBQUE7QUFDRjs7QUFFQTtFQUNFLGdCQUFBO0FBQ0Y7O0FBRUE7RUFDRSxpQkFBQTtBQUNGOztBQUVBO0VBQ0Usa0JBQUE7RUFDQSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSx1QkFBQTtFQUNBLG1CQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0Esc0RBQUE7RUFDQSw2Q0FBQTtFQUNBLDZEQUFBO0VBQ0EsZ0JBQUE7QUFDRiIsInNvdXJjZXNDb250ZW50IjpbIjo6bmctZGVlcCAuYXZhLWRyb3Bkb3duIHtcclxuICB3aWR0aDogMTAwJSAhaW1wb3J0YW50O1xyXG59XHJcblxyXG4ubXQtNSB7XHJcbiAgbWFyZ2luLXRvcDogMnJlbTtcclxufVxyXG5cclxuLmZpbHRlci1idXR0b25zLXNlY3Rpb24ge1xyXG4gIHBhZGRpbmctdG9wOiAxMHB4O1xyXG59XHJcblxyXG46Om5nLWRlZXAgLmF2YS10ZXh0LWNhcmQtY29udGFpbmVyIC5jcmVhdGUtdHlwZSAuYXZhLWNhcmQtY29udGFpbmVyIC5hdmEtY2FyZC5jYXJkIHtcclxuICBwb3NpdGlvbjogcmVsYXRpdmU7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xyXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgaGVpZ2h0OiAzMjZweDtcclxuICBib3JkZXItcmFkaXVzOiAxNnB4O1xyXG4gIGJhY2tncm91bmQ6IHZhcigtLWNhcmQtYmFja2dyb3VuZC1ncmFkaWVudCkgIWltcG9ydGFudDtcclxuICBib3gtc2hhZG93OiB2YXIoLS1jYXJkLWJveC1zaGFkb3cpICFpbXBvcnRhbnQ7XHJcbiAgdHJhbnNpdGlvbjogdHJhbnNmb3JtIC40cyBlYXNlLW91dCwgYm94LXNoYWRvdyAuNHMgZWFzZS1vdXQ7XHJcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcclxufVxyXG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n  return AgentsComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "PageFooterComponent", "agentsConfigData", "combineLatest", "debounceTime", "distinctUntilChanged", "startWith", "Subject", "takeUntil", "AvaTextboxComponent", "DropdownComponent", "IconComponent", "TextCardComponent", "ButtonComponent", "LucideAngularModule", "ReactiveFormsModule", "ConsoleCardComponent", "TimeAgoPipe", "i0", "ɵɵelementStart", "ɵɵlistener", "AgentsComponent_div_8_Template_ava_button_userClick_1_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onFilterChange", "ɵɵelementEnd", "AgentsComponent_div_8_Template_ava_button_userClick_2_listener", "ɵɵadvance", "ɵɵclassProp", "<PERSON><PERSON><PERSON><PERSON>", "ɵɵproperty", "ɵɵtext", "ɵɵelementContainerStart", "AgentsComponent_ng_container_12_Template_ava_console_card_actionClick_1_listener", "$event", "agent_r4", "_r3", "$implicit", "onActionClick", "id", "title", "description", "owner", "ɵɵpipeBind1", "createdDate", "defaultActions", "isLoading", "AgentsComponent_div_13_Template_app_page_footer_pageChange_2_listener", "_r5", "onPageChange", "totalItemsForPagination", "currentPage", "itemsPerPage", "AgentsComponent", "agentService", "debouncedSearchService", "router", "fb", "icon", "label", "tooltip", "isPrimary", "allAgents", "displayedAgents", "totalRecords", "allAgentsData", "individualAgentsData", "collaborativeAgentsData", "originalAllAgentsData", "originalIndividualAgentsData", "originalCollaborativeAgentsData", "agentsConfig", "error", "pagination", "totalPages", "collaborativeTotalPages", "totalNoOfRecords", "destroy$", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "agentsOptions", "name", "value", "showDeletePopup", "agentToDelete", "showSuccessPopup", "successPopupTitle", "successPopupMessage", "selectedData", "searchForm", "cardSkeletonPlaceholders", "Array", "constructor", "group", "search", "ngOnInit", "fetchCollaborativeAgents", "fetchIndividualAgents", "get", "valueChanges", "pipe", "subscribe", "searchTermRaw", "filterType", "searchTerm", "trim", "toLowerCase", "fetchDefaultDataFor", "triggerSearch", "searchResults$", "next", "response", "results", "extractAgentArray", "formatted", "map", "agent", "index", "useCaseId", "toString", "useCaseName", "useCaseDetails", "goal", "formatDate", "created_at", "createdAt", "modifiedAt", "userCount", "users", "type", "updateDisplayedAgents", "filter", "shouldShowPagination", "filteredAgents", "length", "event", "agentId", "actionId", "confirmDeleteAgent", "editAgent", "duplicateAgent", "executeAgent", "onConfirmDelete", "deleteAgent", "closeDeletePopup", "closeSuccessPopup", "onSuccessConfirm", "setValue", "onSelectionChange", "data", "page", "onCreateAgent", "navigate", "navigation", "createRoute", "getCollaborativeAgentsPaginated", "agentsList", "mappedAgents", "agentDetails", "Date", "created<PERSON>y", "role", "backstory", "expectedOutput", "Math", "ceil", "console", "message", "getAllIndividualAgents", "individualAgents", "dateInput", "date", "getMonth", "getDate", "getFullYear", "<PERSON><PERSON><PERSON><PERSON>", "agents", "collaborativeAgents", "arr", "isArray", "firstArrayKey", "Object", "keys", "find", "key", "dataMap", "all", "individual", "collaborative", "agent<PERSON><PERSON><PERSON>", "a", "agentType", "isCollaborative", "tools", "queryParams", "mode", "isCollaborativeAgent", "tags", "some", "tag", "userType", "Boolean", "getAgentById", "<PERSON><PERSON><PERSON>", "deleteApi$", "deleteCollaborativeAgent", "removeAgentFromData", "startIndex", "endIndex", "slice", "removeFrom", "list", "ngOnDestroy", "complete", "ɵɵdirectiveInject", "i1", "AgentServiceService", "i2", "DebouncedSearchService", "i3", "Router", "i4", "FormBuilder", "selectors", "decls", "vars", "consts", "template", "AgentsComponent_Template", "rf", "ctx", "ɵɵelement", "AgentsComponent_Template_ava_dropdown_selectionChange_7_listener", "ɵɵtemplate", "AgentsComponent_div_8_Template", "AgentsComponent_Template_ava_text_card_cardClick_10_listener", "AgentsComponent_div_11_Template", "AgentsComponent_ng_container_12_Template", "AgentsComponent_div_13_Template", "labels", "createAgent", "i5", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "ɵNgNoValidate", "NgControlStatus", "NgControlStatusGroup", "FormGroupDirective", "FormControlName", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\pages\\agents\\agents.component.ts", "C:\\console\\aava-ui-web\\projects\\shared\\pages\\agents\\agents.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { Router } from '@angular/router';\r\nimport { PageFooterComponent } from '../../components/page-footer/page-footer.component';\r\nimport { PaginationService } from '../../services/pagination.service';\r\nimport { AgentServiceService } from './services/agent-service.service';\r\nimport agentsConfigData from './constants/agents.json';\r\nimport { combineLatest, debounceTime, distinctUntilChanged, startWith, Subject, takeUntil } from 'rxjs';\r\n\r\nimport {\r\n  AvaTextboxComponent,\r\n  DropdownComponent,\r\n  DropdownOption,\r\n  IconComponent,\r\n  TextCardComponent,\r\n  PopupComponent,\r\n  ButtonComponent,\r\n} from '@ava/play-comp-library';\r\nimport { LucideAngularModule } from 'lucide-angular';\r\nimport { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';\r\nimport {\r\n  ConsoleCardAction,\r\n  ConsoleCardComponent,\r\n} from '../../components/console-card/console-card.component';\r\nimport { TimeAgoPipe } from '../../pipes/time-ago.pipe';\r\nimport { DebouncedSearchService } from '../../services/debounced-search.service';\r\n\r\n@Component({\r\n  selector: 'shared-agents',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    PageFooterComponent,\r\n    TextCardComponent,\r\n    AvaTextboxComponent,\r\n    DropdownComponent,\r\n    LucideAngularModule,\r\n    IconComponent,\r\n    ReactiveFormsModule,\r\n    PopupComponent,\r\n    ButtonComponent,\r\n    ConsoleCardComponent,\r\n    TimeAgoPipe,\r\n  ],\r\n  templateUrl: './agents.component.html',\r\n  styleUrl: './agents.component.scss',\r\n})\r\nexport class AgentsComponent implements OnInit {\r\n  defaultActions: ConsoleCardAction[] = [\r\n    {\r\n      id: 'duplicate',\r\n      icon: 'copy',\r\n      label: 'Duplicate',\r\n      tooltip: 'Duplicate',\r\n    },\r\n    {\r\n      id: 'edit',\r\n      icon: 'edit',\r\n      label: 'Edit item',\r\n      tooltip: 'Edit',\r\n    },\r\n    {\r\n      id: 'delete',\r\n      icon: 'trash',\r\n      label: 'Delete item',\r\n      tooltip: 'Delete',\r\n    },\r\n    {\r\n      id: 'run',\r\n      icon: 'play',\r\n      label: 'Run application',\r\n      tooltip: 'Run',\r\n      isPrimary: true,\r\n    },\r\n  ];\r\n  allAgents: any[] = [];\r\n  displayedAgents: any[] = [];\r\n  totalRecords: any[] = [];\r\n  allAgentsData: any[] = [];\r\n  individualAgentsData: any[] = [];\r\n  collaborativeAgentsData: any[] = [];\r\n  originalAllAgentsData: any[] = [];\r\n  originalIndividualAgentsData: any[] = [];\r\n  originalCollaborativeAgentsData: any[] = [];\r\n  agentsConfig = agentsConfigData;\r\n  isLoading: boolean = false;\r\n  error: string | null = null;\r\n  currentPage: number = 1;\r\n  itemsPerPage: number = agentsConfigData.pagination.itemsPerPage;\r\n  totalPages: number = 1;\r\n  collaborativeTotalPages: number = 1;\r\n  totalNoOfRecords: number = 0;\r\n  destroy$ = new Subject<void>();\r\n  selectedFilterChange = new Subject<'all' | 'individual' | 'collaborative'>();\r\n  agentsOptions: DropdownOption[] = [\r\n    { name: 'All', value: 'all' },\r\n    { name: 'Owned by me', value: 'owned' },\r\n    { name: 'Experience', value: 'experience' },\r\n    { name: 'Product', value: 'product' },\r\n    { name: 'Data', value: 'data' },\r\n    { name: 'Finops', value: 'finops' },\r\n    { name: 'Quality Engineering', value: 'quality' },\r\n    { name: 'Platform', value: 'platform' },\r\n  ];\r\n  showDeletePopup: boolean = false;\r\n  agentToDelete: any = null;\r\n  showSuccessPopup: boolean = false;\r\n  successPopupTitle: string = '';\r\n  successPopupMessage: string = '';\r\n  selectedFilter: 'all' | 'individual' | 'collaborative' = 'individual';\r\n  selectedData: any = null;\r\n  searchForm!: FormGroup;\r\n  cardSkeletonPlaceholders = Array(11);\r\n\r\n  constructor(\r\n    private agentService: AgentServiceService,\r\n    private debouncedSearchService: DebouncedSearchService,\r\n    private router: Router,\r\n    private fb: FormBuilder,\r\n  ) {\r\n    this.searchForm = this.fb.group({\r\n      search: [''],\r\n    });\r\n  }\r\n\r\n    ngOnInit(): void {\r\n    if (this.selectedFilter === 'collaborative') {\r\n      this.fetchCollaborativeAgents();\r\n    } else {\r\n      this.fetchIndividualAgents();\r\n    }\r\n\r\n    combineLatest([\r\n      this.searchForm.get('search')!.valueChanges.pipe(\r\n        debounceTime(600),\r\n        distinctUntilChanged(),\r\n        startWith('')\r\n      ),\r\n      this.selectedFilterChange.pipe(startWith(this.selectedFilter)),\r\n    ])\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe(([searchTermRaw, filterType]) => {\r\n        const searchTerm = searchTermRaw?.trim().toLowerCase();\r\n\r\n        if (filterType === 'individual' || filterType === 'collaborative') {\r\n          if (!searchTerm) {\r\n            this.fetchDefaultDataFor(filterType);\r\n          } else {\r\n            this.isLoading = true;\r\n            this.debouncedSearchService.triggerSearch(\r\n              searchTerm,\r\n              'agents',\r\n              filterType\r\n            );\r\n          }\r\n        }\r\n      });\r\n\r\n    this.debouncedSearchService.searchResults$\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe({\r\n        next: (response) => {\r\n          const results = this.extractAgentArray(response);\r\n          const formatted = results.map((agent: any, index: number) => ({\r\n            ...agent,\r\n            id: agent.useCaseId?.toString() || agent.id?.toString() || index.toString(),\r\n            title: agent.useCaseName || agent.name || 'Unnamed',\r\n            name: agent.useCaseName || agent.name || 'Unnamed',\r\n            description: agent.useCaseDetails || agent.description || agent.goal || 'No description',\r\n            createdDate: this.formatDate(agent.created_at || agent.createdAt || agent.modifiedAt),\r\n            userCount: agent.users || 0,\r\n            type: this.selectedFilter,\r\n          }));\r\n\r\n          if (this.selectedFilter === 'individual') {\r\n            this.individualAgentsData = formatted;\r\n          } else {\r\n            this.collaborativeAgentsData = formatted;\r\n          }\r\n\r\n          this.totalRecords = formatted;\r\n          this.updateDisplayedAgents();\r\n          this.isLoading = false;\r\n        },\r\n        error: () => {\r\n          this.error = 'Something went wrong during search.';\r\n          this.isLoading = false;\r\n        },\r\n      });\r\n  }\r\n\r\n  fetchDefaultDataFor(filter: string): void {\r\n    switch (filter) {\r\n      case 'individual':\r\n        this.fetchIndividualAgents();\r\n        break;\r\n      case 'collaborative':\r\n        this.fetchCollaborativeAgents();\r\n        break;\r\n    }\r\n  }\r\n\r\n  get shouldShowPagination(): boolean {\r\n    if (this.selectedFilter === 'collaborative') {\r\n      return this.totalNoOfRecords > 0;\r\n    }\r\n    return this.filteredAgents.length > 0;\r\n  }\r\n\r\n  get totalItemsForPagination(): number {\r\n    if (this.selectedFilter === 'collaborative') {\r\n      return this.totalNoOfRecords + 1;\r\n    }\r\n    return this.filteredAgents.length + 1;\r\n  }\r\n\r\n  get filteredAgents(): any[] {\r\n    switch (this.selectedFilter) {\r\n      case 'individual':\r\n        return this.individualAgentsData;\r\n      case 'collaborative':\r\n        return this.collaborativeAgentsData;\r\n      case 'all':\r\n        return this.allAgentsData;\r\n      default:\r\n        return this.totalRecords;\r\n    }\r\n  }\r\n\r\n  onActionClick(\r\n    event: { actionId: string; action: ConsoleCardAction },\r\n    agentId: string,\r\n  ): void {\r\n    switch (event.actionId) {\r\n      case 'delete':\r\n        this.confirmDeleteAgent(agentId);\r\n        break;\r\n      case 'edit':\r\n        this.editAgent(agentId);\r\n        break;\r\n      case 'duplicate':\r\n        this.duplicateAgent(agentId);\r\n        break;\r\n      case 'run':\r\n        this.executeAgent(agentId);\r\n        break;\r\n      default:\r\n        break;\r\n    }\r\n  }\r\n\r\n  onConfirmDelete(): void {\r\n    if (this.agentToDelete) {\r\n      this.deleteAgent(this.agentToDelete.id);\r\n      this.closeDeletePopup();\r\n    }\r\n  }\r\n\r\n  closeDeletePopup(): void {\r\n    this.showDeletePopup = false;\r\n    this.agentToDelete = null;\r\n  }\r\n\r\n  closeSuccessPopup(): void {\r\n    this.showSuccessPopup = false;\r\n    this.successPopupTitle = '';\r\n    this.successPopupMessage = '';\r\n  }\r\n\r\n  onSuccessConfirm(): void {\r\n    this.closeSuccessPopup();\r\n  }\r\n\r\n  onFilterChange(filter: 'all' | 'individual' | 'collaborative'): void {\r\n    this.selectedFilter = filter;\r\n        this.selectedFilterChange.next(filter);\r\n    this.searchForm.get('search')?.setValue('');\r\n\r\n    this.currentPage = 1;\r\n    if (filter !== 'collaborative') {\r\n      this.totalNoOfRecords = 0;\r\n    }\r\n\r\n    this.selectedFilter === 'individual'\r\n      ? this.fetchIndividualAgents()\r\n      : this.fetchCollaborativeAgents();\r\n  }\r\n\r\n  onSelectionChange(data: any) {\r\n    this.selectedData = data;\r\n  }\r\n\r\n  onPageChange(page: number): void {\r\n    this.currentPage = page;\r\n    this.selectedFilter === 'collaborative'\r\n      ? this.fetchCollaborativeAgents()\r\n      : this.updateDisplayedAgents();\r\n  }\r\n\r\n  onCreateAgent(): void {\r\n    this.router.navigate([agentsConfigData.navigation.createRoute]);\r\n  }\r\n\r\n  private fetchCollaborativeAgents(): void {\r\n    this.isLoading = true;\r\n    this.agentService\r\n      .getCollaborativeAgentsPaginated(this.currentPage, this.itemsPerPage)\r\n      .subscribe({\r\n        next: (response) => {\r\n          this.totalNoOfRecords = response?.totalNoOfRecords ?? 0;\r\n          const agentsList = this.extractAgentArray(response);\r\n          const mappedAgents = agentsList.map((agent: any, index: number) => ({\r\n            ...agent,\r\n            id: agent.id?.toString() || `collab-${index}`,\r\n            title: agent.name || agent.agentDetails || 'Unnamed Agent',\r\n            name: agent.name || agent.agentDetails || 'Unnamed Agent',\r\n            description:\r\n              agent.description ||\r\n              agent.agentDetails ||\r\n              agent.goal ||\r\n              'No description',\r\n            createdDate: this.formatDate(\r\n              agent.modifiedAt || agent.createdAt || new Date(),\r\n            ),\r\n            userCount: agent.users || 0,\r\n            type: 'collaborative',\r\n            createdBy: agent.createdBy || '',\r\n            role: agent.role || '',\r\n            backstory: agent.backstory || '',\r\n            expectedOutput: agent.expectedOutput || '',\r\n          }));\r\n          this.collaborativeTotalPages = Math.ceil(\r\n            this.totalNoOfRecords / this.itemsPerPage,\r\n          );\r\n          this.totalPages = this.collaborativeTotalPages;\r\n          this.collaborativeAgentsData = mappedAgents;\r\n          this.displayedAgents = mappedAgents;\r\n          this.totalRecords = mappedAgents;\r\n          this.isLoading = false;\r\n        },\r\n        error: (error) => {\r\n          console.error('Error fetching collaborative agents:', error);\r\n          this.error = error.message || 'Failed to load collaborative agents';\r\n          this.isLoading = false;\r\n        },\r\n      });\r\n  }\r\n\r\n  private fetchIndividualAgents(): void {\r\n    this.isLoading = true;\r\n    this.agentService.getAllIndividualAgents().subscribe({\r\n      next: (response) => {\r\n        const agentsList = response?.individualAgents ?? response ?? [];\r\n        const mappedAgents = agentsList.map((agent: any) => ({\r\n          ...agent,\r\n          id: agent.useCaseId?.toString() || agent.id?.toString() || '0',\r\n          title: agent.useCaseName || agent.name || 'Unnamed Agent',\r\n          name: agent.useCaseName || agent.name || 'Unnamed Agent',\r\n          description:\r\n            agent.useCaseDetails || agent.description || 'No description',\r\n          createdDate: this.formatDate(agent.created_at || agent.createdAt),\r\n          userCount: agent.users || 0,\r\n          type: 'individual',\r\n        }));\r\n        this.originalIndividualAgentsData = mappedAgents;\r\n        this.individualAgentsData = mappedAgents;\r\n        this.totalRecords = mappedAgents;\r\n        this.updateDisplayedAgents();\r\n        this.isLoading = false;\r\n      },\r\n      error: (error) => {\r\n        console.error('Error fetching individual agents:', error);\r\n        this.error = error.message || 'Failed to load individual agents';\r\n        this.isLoading = false;\r\n      },\r\n    });\r\n  }\r\n\r\n  private formatDate(dateInput: string | Date): string {\r\n    const date = new Date(dateInput);\r\n    return `${date.getMonth() + 1}/${date.getDate()}/${date.getFullYear()}`;\r\n  }\r\n\r\n  private extractAgentArray(response: any): any[] {\r\n    if (!response) return [];\r\n    const possibleArrays = [\r\n      response.agentDetails,\r\n      response.data,\r\n      response.agents,\r\n      response.collaborativeAgents,\r\n    ];\r\n    for (const arr of possibleArrays) {\r\n      if (Array.isArray(arr)) return arr;\r\n    }\r\n    if (Array.isArray(response)) return response;\r\n    const firstArrayKey = Object.keys(response).find((key) =>\r\n      Array.isArray(response[key]),\r\n    );\r\n    return firstArrayKey ? response[firstArrayKey] : [];\r\n  }\r\n\r\n  private confirmDeleteAgent(agentId: string): void {\r\n    const dataMap: Record<string, any[]> = {\r\n      all: this.allAgentsData,\r\n      individual: this.individualAgentsData,\r\n      collaborative: this.collaborativeAgentsData,\r\n    };\r\n    const filteredAgents = dataMap[this.selectedFilter] ?? [];\r\n    const agentObj =\r\n      filteredAgents.find((agent) => agent.id === agentId) ||\r\n      this.allAgents.find((agent: any) => agent.id === agentId);\r\n    this.agentToDelete = agentObj;\r\n    this.showDeletePopup = true;\r\n  }\r\n\r\n  private editAgent(agentId: string): void {\r\n    const dataMap: Record<string, any[]> = {\r\n      all: this.allAgentsData,\r\n      individual: this.individualAgentsData,\r\n      collaborative: this.collaborativeAgentsData,\r\n    };\r\n    const filteredAgents = dataMap[this.selectedFilter] ?? [];\r\n    let agent =\r\n      filteredAgents.find((a) => a.id === agentId) ||\r\n      this.allAgents.find((a) => a.id === agentId);\r\n    if (!agent) {\r\n      return;\r\n    }\r\n    let agentType = agent.type;\r\n    const isCollaborative =\r\n      this.selectedFilter === 'collaborative' ||\r\n      agent.role ||\r\n      agent.goal ||\r\n      agent.backstory ||\r\n      agent.tools;\r\n    agentType = agentType ?? (isCollaborative ? 'collaborative' : 'individual');\r\n    this.router.navigate(['/build/agents', agentType], {\r\n      queryParams: {\r\n        id: agentId,\r\n        mode: 'edit',\r\n      },\r\n    });\r\n  }\r\n\r\n  private duplicateAgent(agentId: string): void {\r\n    const dataMap: Record<string, any[]> = {\r\n      all: this.allAgentsData,\r\n      individual: this.individualAgentsData,\r\n      collaborative: this.collaborativeAgentsData,\r\n    };\r\n    const filteredAgents = dataMap[this.selectedFilter] ?? [];\r\n    let agent = filteredAgents.find((a) => a.id === agentId);\r\n    if (!agent && this.selectedFilter !== 'all') {\r\n      agent = this.allAgentsData.find((a) => a.id === agentId);\r\n    }\r\n    if (!agent) {\r\n      return;\r\n    }\r\n    const isCollaborative =\r\n      this.selectedFilter === 'collaborative' ||\r\n      agent.role ||\r\n      agent.goal ||\r\n      agent.backstory ||\r\n      agent.tools;\r\n    const agentType = isCollaborative ? 'collaborative' : 'individual';\r\n    this.router.navigate(['/build/agents', agentType], {\r\n      queryParams: {\r\n        id: agentId,\r\n        mode: 'duplicate',\r\n      },\r\n    });\r\n  }\r\n\r\n  private executeAgent(agentId: string): void {\r\n    const dataMap: Record<string, any[]> = {\r\n      all: this.allAgentsData,\r\n      individual: this.individualAgentsData,\r\n      collaborative: this.collaborativeAgentsData,\r\n    };\r\n    const agent =\r\n      dataMap[this.selectedFilter]?.find((a) => a.id === agentId) ||\r\n      this.allAgents.find((a) => a.id === agentId);\r\n    if (!agent) {\r\n      return;\r\n    }\r\n    const isCollaborative = this.isCollaborativeAgent(agent);\r\n    const agentType = isCollaborative ? 'collaborative' : 'individual';\r\n\r\n    this.router.navigate(['/build/agents', agentType, 'execute'], {\r\n      queryParams: {\r\n        id: agentId,\r\n      },\r\n    });\r\n  }\r\n\r\n  private isCollaborativeAgent(agent: any): boolean {\r\n    return (\r\n      agent?.type === 'collaborative' ||\r\n      this.selectedFilter === 'collaborative' ||\r\n      agent?.tags?.some(\r\n        (tag: { label: string }) => tag.label.toLowerCase() === 'collaborative',\r\n      ) ||\r\n      agent?.userType === 'collaborative' ||\r\n      agent?.agentType === 'collaborative' ||\r\n      Boolean(agent?.role || agent?.goal || agent?.backstory || agent?.tools)\r\n    );\r\n  }\r\n\r\n  private deleteAgent(agentId: string): void {\r\n    const agent = this.getAgentById(agentId);\r\n    if (!agent) {\r\n      return;\r\n    }\r\n    const { name: agentName, type: agentType } = agent;\r\n    const deleteApi$ =\r\n      agentType === 'collaborative'\r\n        ? this.agentService.deleteCollaborativeAgent(agentId)\r\n        : this.agentService.deleteAgent(agentId);\r\n\r\n    deleteApi$.subscribe({\r\n      next: (response: any) => {\r\n        this.removeAgentFromData(agentId);\r\n        this.updateDisplayedAgents();\r\n        this.successPopupTitle = 'Success';\r\n        this.successPopupMessage =\r\n          response?.message || `Agent \"${agentName}\" deleted successfully`;\r\n        this.showSuccessPopup = true;\r\n      },\r\n      error: (error: any) => {\r\n        console.error('Error deleting agent:', error);\r\n      },\r\n    });\r\n  }\r\n\r\n  private updateDisplayedAgents(): void {\r\n    const startIndex = (this.currentPage - 1) * 11;\r\n    const endIndex = startIndex + 11;\r\n    this.displayedAgents = this.filteredAgents.slice(startIndex, endIndex);\r\n    this.totalPages = Math.ceil(this.filteredAgents.length / 11);\r\n  }\r\n\r\n  private getAgentById(agentId: string): any {\r\n    const dataMap: Record<string, any[]> = {\r\n      all: this.allAgentsData,\r\n      individual: this.individualAgentsData,\r\n      collaborative: this.collaborativeAgentsData,\r\n    };\r\n    return dataMap[this.selectedFilter]?.find((a) => a.id === agentId) || null;\r\n  }\r\n\r\n  private removeAgentFromData(agentId: string): void {\r\n    const removeFrom = (list: any[]) =>\r\n      list.filter((agent) => agent.id !== agentId);\r\n    switch (this.selectedFilter) {\r\n      case 'all':\r\n        this.allAgentsData = removeFrom(this.allAgentsData);\r\n        this.originalAllAgentsData = removeFrom(this.originalAllAgentsData);\r\n        break;\r\n      case 'individual':\r\n        this.individualAgentsData = removeFrom(this.individualAgentsData);\r\n        this.originalIndividualAgentsData = removeFrom(\r\n          this.originalIndividualAgentsData,\r\n        );\r\n        break;\r\n      case 'collaborative':\r\n        this.collaborativeAgentsData = removeFrom(this.collaborativeAgentsData);\r\n        this.originalCollaborativeAgentsData = removeFrom(\r\n          this.originalCollaborativeAgentsData,\r\n        );\r\n        break;\r\n    }\r\n    this.allAgents = removeFrom(this.allAgents);\r\n    this.totalRecords = removeFrom(this.totalRecords);\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.destroy$.next();\r\n    this.destroy$.complete();\r\n  }\r\n\r\n}\r\n", "<div id=\"agents-container\" class=\"container-fluid\">\r\n  <div id=\"search-filter-container\" class=\"row g-3\">\r\n    <div class=\"col-12 col-md-8 col-lg-9 col-xl-10 search-section\">\r\n      <form [formGroup]=\"searchForm\">\r\n        <ava-textbox\r\n          placeholder='Search \"Agents\"'\r\n          hoverEffect=\"glow\"\r\n          pressedEffect=\"solid\"\r\n          formControlName=\"search\"\r\n        >\r\n          <ava-icon\r\n            slot=\"icon-start\"\r\n            iconName=\"search\"\r\n            [iconSize]=\"16\"\r\n            iconColor=\"var(--color-brand-primary)\"\r\n          >\r\n          </ava-icon>\r\n        </ava-textbox>\r\n      </form>\r\n    </div>\r\n    <div class=\"col-12 col-md-4 col-lg-3 col-xl-2 action-buttons\">\r\n      <ava-dropdown\r\n        dropdownTitle=\"choose agent\"\r\n        [options]=\"agentsOptions\"\r\n        (selectionChange)=\"onSelectionChange($event)\"\r\n      >\r\n      </ava-dropdown>\r\n    </div>\r\n  </div>\r\n\r\n  <div class=\"filter-buttons-section\" *ngIf=\"!isLoading\">\r\n    <ava-button\r\n      label=\"Individual\"\r\n      [variant]=\"selectedFilter === 'individual' ? 'primary' : 'secondary'\"\r\n      [pill]=\"true\"\r\n      size=\"small\"\r\n      [class.active]=\"selectedFilter === 'individual'\"\r\n      (userClick)=\"onFilterChange('individual')\"\r\n      class=\"filter-btn\"\r\n    >\r\n    </ava-button>\r\n    <ava-button\r\n      label=\"Collaborative\"\r\n      [variant]=\"selectedFilter === 'collaborative' ? 'primary' : 'secondary'\"\r\n      [pill]=\"true\"\r\n      size=\"small\"\r\n      [class.active]=\"selectedFilter === 'collaborative'\"\r\n      (userClick)=\"onFilterChange('collaborative')\"\r\n      class=\"filter-btn\"\r\n    >\r\n    </ava-button>\r\n  </div>\r\n\r\n  <div id=\"prompts-card-container\" class=\"row g-3\">\r\n    <ava-text-card\r\n      class=\"col-12 col-sm-6 col-md-4 col-lg-3 col-xl-3 col-xxl-2 mt-5\"\r\n      [type]=\"'create'\"\r\n      [iconName]=\"'plus'\"\r\n      iconColor=\"#144692\"\r\n      [title]=\"agentsConfig.labels.createAgent\"\r\n      (cardClick)=\"onCreateAgent()\"\r\n      [isLoading]=\"isLoading\"\r\n    >\r\n    </ava-text-card>\r\n\r\n    <!-- No Results Message -->\r\n    <div\r\n      class=\"col-12 d-flex justify-content-center align-items-center py-5\"\r\n      *ngIf=\"!isLoading && displayedAgents.length === 0\"\r\n    >\r\n      <div class=\"text-center\">\r\n        <h5 class=\"text-muted\">No Agents found matching your criteria</h5>\r\n      </div>\r\n    </div>\r\n\r\n    <ng-container\r\n      *ngFor=\"\r\n        let agent of isLoading && displayedAgents.length === 0\r\n          ? cardSkeletonPlaceholders\r\n          : displayedAgents\r\n      \"\r\n    >\r\n      <ava-console-card\r\n        class=\"col-12 col-sm-6 col-md-4 col-lg-3 col-xl-3 col-xxl-2 mt-5\"\r\n        [title]=\"agent?.title\"\r\n        [description]=\"agent?.description\"\r\n        categoryIcon=\"bot\"\r\n        categoryTitle=\"Agents\"\r\n        categoryValue=\"42\"\r\n        [author]=\"agent?.owner || 'AAVA'\"\r\n        [date]=\"agent?.createdDate | timeAgo\"\r\n        [actions]=\"defaultActions\"\r\n        (actionClick)=\"onActionClick($event, agent.id)\"\r\n        [skeleton]=\"isLoading\"\r\n      >\r\n      </ava-console-card>\r\n    </ng-container>\r\n  </div>\r\n\r\n  <!-- Pagination Footer -->\r\n  <div class=\"row\" *ngIf=\"shouldShowPagination\">\r\n    <div class=\"col-12 d-flex justify-content-center mt-4\">\r\n      <app-page-footer\r\n        [totalItems]=\"totalItemsForPagination\"\r\n        [currentPage]=\"currentPage\"\r\n        [itemsPerPage]=\"itemsPerPage\"\r\n        (pageChange)=\"onPageChange($event)\"\r\n      ></app-page-footer>\r\n    </div>\r\n  </div>\r\n</div>"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,mBAAmB,QAAQ,oDAAoD;AAGxF,OAAOC,gBAAgB,MAAM,yBAAyB;AACtD,SAASC,aAAa,EAAEC,YAAY,EAAEC,oBAAoB,EAAEC,SAAS,EAAEC,OAAO,EAAEC,SAAS,QAAQ,MAAM;AAEvG,SACEC,mBAAmB,EACnBC,iBAAiB,EAEjBC,aAAa,EACbC,iBAAiB,EAEjBC,eAAe,QACV,wBAAwB;AAC/B,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAAiCC,mBAAmB,QAAQ,gBAAgB;AAC5E,SAEEC,oBAAoB,QACf,sDAAsD;AAC7D,SAASC,WAAW,QAAQ,2BAA2B;;;;;;;;;;ICOnDC,EADF,CAAAC,cAAA,cAAuD,qBASpD;IAFCD,EAAA,CAAAE,UAAA,uBAAAC,+DAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAaF,MAAA,CAAAG,cAAA,CAAe,YAAY,CAAC;IAAA,EAAC;IAG5CT,EAAA,CAAAU,YAAA,EAAa;IACbV,EAAA,CAAAC,cAAA,qBAQC;IAFCD,EAAA,CAAAE,UAAA,uBAAAS,+DAAA;MAAAX,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAaF,MAAA,CAAAG,cAAA,CAAe,eAAe,CAAC;IAAA,EAAC;IAIjDT,EADE,CAAAU,YAAA,EAAa,EACT;;;;IAfFV,EAAA,CAAAY,SAAA,EAAgD;IAAhDZ,EAAA,CAAAa,WAAA,WAAAP,MAAA,CAAAQ,cAAA,kBAAgD;IAFhDd,EADA,CAAAe,UAAA,YAAAT,MAAA,CAAAQ,cAAA,4CAAqE,cACxD;IAYbd,EAAA,CAAAY,SAAA,EAAmD;IAAnDZ,EAAA,CAAAa,WAAA,WAAAP,MAAA,CAAAQ,cAAA,qBAAmD;IAFnDd,EADA,CAAAe,UAAA,YAAAT,MAAA,CAAAQ,cAAA,+CAAwE,cAC3D;;;;;IA2BXd,EALJ,CAAAC,cAAA,cAGC,cAC0B,aACA;IAAAD,EAAA,CAAAgB,MAAA,6CAAsC;IAEjEhB,EAFiE,CAAAU,YAAA,EAAK,EAC9D,EACF;;;;;;IAENV,EAAA,CAAAiB,uBAAA,GAMC;IACCjB,EAAA,CAAAC,cAAA,2BAYC;;IAFCD,EAAA,CAAAE,UAAA,yBAAAgB,iFAAAC,MAAA;MAAA,MAAAC,QAAA,GAAApB,EAAA,CAAAI,aAAA,CAAAiB,GAAA,EAAAC,SAAA;MAAA,MAAAhB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAeF,MAAA,CAAAiB,aAAA,CAAAJ,MAAA,EAAAC,QAAA,CAAAI,EAAA,CAA+B;IAAA,EAAC;IAGjDxB,EAAA,CAAAU,YAAA,EAAmB;;;;;;IAXjBV,EAAA,CAAAY,SAAA,EAAsB;IAStBZ,EATA,CAAAe,UAAA,UAAAK,QAAA,kBAAAA,QAAA,CAAAK,KAAA,CAAsB,gBAAAL,QAAA,kBAAAA,QAAA,CAAAM,WAAA,CACY,YAAAN,QAAA,kBAAAA,QAAA,CAAAO,KAAA,YAID,SAAA3B,EAAA,CAAA4B,WAAA,OAAAR,QAAA,kBAAAA,QAAA,CAAAS,WAAA,EACI,YAAAvB,MAAA,CAAAwB,cAAA,CACX,aAAAxB,MAAA,CAAAyB,SAAA,CAEJ;;;;;;IASxB/B,EAFJ,CAAAC,cAAA,cAA8C,cACW,0BAMpD;IADCD,EAAA,CAAAE,UAAA,wBAAA8B,sEAAAb,MAAA;MAAAnB,EAAA,CAAAI,aAAA,CAAA6B,GAAA;MAAA,MAAA3B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAcF,MAAA,CAAA4B,YAAA,CAAAf,MAAA,CAAoB;IAAA,EAAC;IAGzCnB,EAFK,CAAAU,YAAA,EAAkB,EACf,EACF;;;;IANAV,EAAA,CAAAY,SAAA,GAAsC;IAEtCZ,EAFA,CAAAe,UAAA,eAAAT,MAAA,CAAA6B,uBAAA,CAAsC,gBAAA7B,MAAA,CAAA8B,WAAA,CACX,iBAAA9B,MAAA,CAAA+B,YAAA,CACE;;;AD1DrC,WAAaC,eAAe;EAAtB,MAAOA,eAAe;IAoEhBC,YAAA;IACAC,sBAAA;IACAC,MAAA;IACAC,EAAA;IAtEVZ,cAAc,GAAwB,CACpC;MACEN,EAAE,EAAE,WAAW;MACfmB,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,WAAW;MAClBC,OAAO,EAAE;KACV,EACD;MACErB,EAAE,EAAE,MAAM;MACVmB,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,WAAW;MAClBC,OAAO,EAAE;KACV,EACD;MACErB,EAAE,EAAE,QAAQ;MACZmB,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,aAAa;MACpBC,OAAO,EAAE;KACV,EACD;MACErB,EAAE,EAAE,KAAK;MACTmB,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,iBAAiB;MACxBC,OAAO,EAAE,KAAK;MACdC,SAAS,EAAE;KACZ,CACF;IACDC,SAAS,GAAU,EAAE;IACrBC,eAAe,GAAU,EAAE;IAC3BC,YAAY,GAAU,EAAE;IACxBC,aAAa,GAAU,EAAE;IACzBC,oBAAoB,GAAU,EAAE;IAChCC,uBAAuB,GAAU,EAAE;IACnCC,qBAAqB,GAAU,EAAE;IACjCC,4BAA4B,GAAU,EAAE;IACxCC,+BAA+B,GAAU,EAAE;IAC3CC,YAAY,GAAGxE,gBAAgB;IAC/B+C,SAAS,GAAY,KAAK;IAC1B0B,KAAK,GAAkB,IAAI;IAC3BrB,WAAW,GAAW,CAAC;IACvBC,YAAY,GAAWrD,gBAAgB,CAAC0E,UAAU,CAACrB,YAAY;IAC/DsB,UAAU,GAAW,CAAC;IACtBC,uBAAuB,GAAW,CAAC;IACnCC,gBAAgB,GAAW,CAAC;IAC5BC,QAAQ,GAAG,IAAIzE,OAAO,EAAQ;IAC9B0E,oBAAoB,GAAG,IAAI1E,OAAO,EAA0C;IAC5E2E,aAAa,GAAqB,CAChC;MAAEC,IAAI,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAK,CAAE,EAC7B;MAAED,IAAI,EAAE,aAAa;MAAEC,KAAK,EAAE;IAAO,CAAE,EACvC;MAAED,IAAI,EAAE,YAAY;MAAEC,KAAK,EAAE;IAAY,CAAE,EAC3C;MAAED,IAAI,EAAE,SAAS;MAAEC,KAAK,EAAE;IAAS,CAAE,EACrC;MAAED,IAAI,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAM,CAAE,EAC/B;MAAED,IAAI,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAQ,CAAE,EACnC;MAAED,IAAI,EAAE,qBAAqB;MAAEC,KAAK,EAAE;IAAS,CAAE,EACjD;MAAED,IAAI,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAU,CAAE,CACxC;IACDC,eAAe,GAAY,KAAK;IAChCC,aAAa,GAAQ,IAAI;IACzBC,gBAAgB,GAAY,KAAK;IACjCC,iBAAiB,GAAW,EAAE;IAC9BC,mBAAmB,GAAW,EAAE;IAChCzD,cAAc,GAA2C,YAAY;IACrE0D,YAAY,GAAQ,IAAI;IACxBC,UAAU;IACVC,wBAAwB,GAAGC,KAAK,CAAC,EAAE,CAAC;IAEpCC,YACUrC,YAAiC,EACjCC,sBAA8C,EAC9CC,MAAc,EACdC,EAAe;MAHf,KAAAH,YAAY,GAAZA,YAAY;MACZ,KAAAC,sBAAsB,GAAtBA,sBAAsB;MACtB,KAAAC,MAAM,GAANA,MAAM;MACN,KAAAC,EAAE,GAAFA,EAAE;MAEV,IAAI,CAAC+B,UAAU,GAAG,IAAI,CAAC/B,EAAE,CAACmC,KAAK,CAAC;QAC9BC,MAAM,EAAE,CAAC,EAAE;OACZ,CAAC;IACJ;IAEEC,QAAQA,CAAA;MACR,IAAI,IAAI,CAACjE,cAAc,KAAK,eAAe,EAAE;QAC3C,IAAI,CAACkE,wBAAwB,EAAE;MACjC,CAAC,MAAM;QACL,IAAI,CAACC,qBAAqB,EAAE;MAC9B;MAEAhG,aAAa,CAAC,CACZ,IAAI,CAACwF,UAAU,CAACS,GAAG,CAAC,QAAQ,CAAE,CAACC,YAAY,CAACC,IAAI,CAC9ClG,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBC,SAAS,CAAC,EAAE,CAAC,CACd,EACD,IAAI,CAAC2E,oBAAoB,CAACqB,IAAI,CAAChG,SAAS,CAAC,IAAI,CAAC0B,cAAc,CAAC,CAAC,CAC/D,CAAC,CACCsE,IAAI,CAAC9F,SAAS,CAAC,IAAI,CAACwE,QAAQ,CAAC,CAAC,CAC9BuB,SAAS,CAAC,CAAC,CAACC,aAAa,EAAEC,UAAU,CAAC,KAAI;QACzC,MAAMC,UAAU,GAAGF,aAAa,EAAEG,IAAI,EAAE,CAACC,WAAW,EAAE;QAEtD,IAAIH,UAAU,KAAK,YAAY,IAAIA,UAAU,KAAK,eAAe,EAAE;UACjE,IAAI,CAACC,UAAU,EAAE;YACf,IAAI,CAACG,mBAAmB,CAACJ,UAAU,CAAC;UACtC,CAAC,MAAM;YACL,IAAI,CAACxD,SAAS,GAAG,IAAI;YACrB,IAAI,CAACS,sBAAsB,CAACoD,aAAa,CACvCJ,UAAU,EACV,QAAQ,EACRD,UAAU,CACX;UACH;QACF;MACF,CAAC,CAAC;MAEJ,IAAI,CAAC/C,sBAAsB,CAACqD,cAAc,CACvCT,IAAI,CAAC9F,SAAS,CAAC,IAAI,CAACwE,QAAQ,CAAC,CAAC,CAC9BuB,SAAS,CAAC;QACTS,IAAI,EAAGC,QAAQ,IAAI;UACjB,MAAMC,OAAO,GAAG,IAAI,CAACC,iBAAiB,CAACF,QAAQ,CAAC;UAChD,MAAMG,SAAS,GAAGF,OAAO,CAACG,GAAG,CAAC,CAACC,KAAU,EAAEC,KAAa,MAAM;YAC5D,GAAGD,KAAK;YACR5E,EAAE,EAAE4E,KAAK,CAACE,SAAS,EAAEC,QAAQ,EAAE,IAAIH,KAAK,CAAC5E,EAAE,EAAE+E,QAAQ,EAAE,IAAIF,KAAK,CAACE,QAAQ,EAAE;YAC3E9E,KAAK,EAAE2E,KAAK,CAACI,WAAW,IAAIJ,KAAK,CAACnC,IAAI,IAAI,SAAS;YACnDA,IAAI,EAAEmC,KAAK,CAACI,WAAW,IAAIJ,KAAK,CAACnC,IAAI,IAAI,SAAS;YAClDvC,WAAW,EAAE0E,KAAK,CAACK,cAAc,IAAIL,KAAK,CAAC1E,WAAW,IAAI0E,KAAK,CAACM,IAAI,IAAI,gBAAgB;YACxF7E,WAAW,EAAE,IAAI,CAAC8E,UAAU,CAACP,KAAK,CAACQ,UAAU,IAAIR,KAAK,CAACS,SAAS,IAAIT,KAAK,CAACU,UAAU,CAAC;YACrFC,SAAS,EAAEX,KAAK,CAACY,KAAK,IAAI,CAAC;YAC3BC,IAAI,EAAE,IAAI,CAACnG;WACZ,CAAC,CAAC;UAEH,IAAI,IAAI,CAACA,cAAc,KAAK,YAAY,EAAE;YACxC,IAAI,CAACqC,oBAAoB,GAAG+C,SAAS;UACvC,CAAC,MAAM;YACL,IAAI,CAAC9C,uBAAuB,GAAG8C,SAAS;UAC1C;UAEA,IAAI,CAACjD,YAAY,GAAGiD,SAAS;UAC7B,IAAI,CAACgB,qBAAqB,EAAE;UAC5B,IAAI,CAACnF,SAAS,GAAG,KAAK;QACxB,CAAC;QACD0B,KAAK,EAAEA,CAAA,KAAK;UACV,IAAI,CAACA,KAAK,GAAG,qCAAqC;UAClD,IAAI,CAAC1B,SAAS,GAAG,KAAK;QACxB;OACD,CAAC;IACN;IAEA4D,mBAAmBA,CAACwB,MAAc;MAChC,QAAQA,MAAM;QACZ,KAAK,YAAY;UACf,IAAI,CAAClC,qBAAqB,EAAE;UAC5B;QACF,KAAK,eAAe;UAClB,IAAI,CAACD,wBAAwB,EAAE;UAC/B;MACJ;IACF;IAEA,IAAIoC,oBAAoBA,CAAA;MACtB,IAAI,IAAI,CAACtG,cAAc,KAAK,eAAe,EAAE;QAC3C,OAAO,IAAI,CAAC+C,gBAAgB,GAAG,CAAC;MAClC;MACA,OAAO,IAAI,CAACwD,cAAc,CAACC,MAAM,GAAG,CAAC;IACvC;IAEA,IAAInF,uBAAuBA,CAAA;MACzB,IAAI,IAAI,CAACrB,cAAc,KAAK,eAAe,EAAE;QAC3C,OAAO,IAAI,CAAC+C,gBAAgB,GAAG,CAAC;MAClC;MACA,OAAO,IAAI,CAACwD,cAAc,CAACC,MAAM,GAAG,CAAC;IACvC;IAEA,IAAID,cAAcA,CAAA;MAChB,QAAQ,IAAI,CAACvG,cAAc;QACzB,KAAK,YAAY;UACf,OAAO,IAAI,CAACqC,oBAAoB;QAClC,KAAK,eAAe;UAClB,OAAO,IAAI,CAACC,uBAAuB;QACrC,KAAK,KAAK;UACR,OAAO,IAAI,CAACF,aAAa;QAC3B;UACE,OAAO,IAAI,CAACD,YAAY;MAC5B;IACF;IAEA1B,aAAaA,CACXgG,KAAsD,EACtDC,OAAe;MAEf,QAAQD,KAAK,CAACE,QAAQ;QACpB,KAAK,QAAQ;UACX,IAAI,CAACC,kBAAkB,CAACF,OAAO,CAAC;UAChC;QACF,KAAK,MAAM;UACT,IAAI,CAACG,SAAS,CAACH,OAAO,CAAC;UACvB;QACF,KAAK,WAAW;UACd,IAAI,CAACI,cAAc,CAACJ,OAAO,CAAC;UAC5B;QACF,KAAK,KAAK;UACR,IAAI,CAACK,YAAY,CAACL,OAAO,CAAC;UAC1B;QACF;UACE;MACJ;IACF;IAEAM,eAAeA,CAAA;MACb,IAAI,IAAI,CAAC1D,aAAa,EAAE;QACtB,IAAI,CAAC2D,WAAW,CAAC,IAAI,CAAC3D,aAAa,CAAC5C,EAAE,CAAC;QACvC,IAAI,CAACwG,gBAAgB,EAAE;MACzB;IACF;IAEAA,gBAAgBA,CAAA;MACd,IAAI,CAAC7D,eAAe,GAAG,KAAK;MAC5B,IAAI,CAACC,aAAa,GAAG,IAAI;IAC3B;IAEA6D,iBAAiBA,CAAA;MACf,IAAI,CAAC5D,gBAAgB,GAAG,KAAK;MAC7B,IAAI,CAACC,iBAAiB,GAAG,EAAE;MAC3B,IAAI,CAACC,mBAAmB,GAAG,EAAE;IAC/B;IAEA2D,gBAAgBA,CAAA;MACd,IAAI,CAACD,iBAAiB,EAAE;IAC1B;IAEAxH,cAAcA,CAAC0G,MAA8C;MAC3D,IAAI,CAACrG,cAAc,GAAGqG,MAAM;MACxB,IAAI,CAACpD,oBAAoB,CAAC+B,IAAI,CAACqB,MAAM,CAAC;MAC1C,IAAI,CAAC1C,UAAU,CAACS,GAAG,CAAC,QAAQ,CAAC,EAAEiD,QAAQ,CAAC,EAAE,CAAC;MAE3C,IAAI,CAAC/F,WAAW,GAAG,CAAC;MACpB,IAAI+E,MAAM,KAAK,eAAe,EAAE;QAC9B,IAAI,CAACtD,gBAAgB,GAAG,CAAC;MAC3B;MAEA,IAAI,CAAC/C,cAAc,KAAK,YAAY,GAChC,IAAI,CAACmE,qBAAqB,EAAE,GAC5B,IAAI,CAACD,wBAAwB,EAAE;IACrC;IAEAoD,iBAAiBA,CAACC,IAAS;MACzB,IAAI,CAAC7D,YAAY,GAAG6D,IAAI;IAC1B;IAEAnG,YAAYA,CAACoG,IAAY;MACvB,IAAI,CAAClG,WAAW,GAAGkG,IAAI;MACvB,IAAI,CAACxH,cAAc,KAAK,eAAe,GACnC,IAAI,CAACkE,wBAAwB,EAAE,GAC/B,IAAI,CAACkC,qBAAqB,EAAE;IAClC;IAEAqB,aAAaA,CAAA;MACX,IAAI,CAAC9F,MAAM,CAAC+F,QAAQ,CAAC,CAACxJ,gBAAgB,CAACyJ,UAAU,CAACC,WAAW,CAAC,CAAC;IACjE;IAEQ1D,wBAAwBA,CAAA;MAC9B,IAAI,CAACjD,SAAS,GAAG,IAAI;MACrB,IAAI,CAACQ,YAAY,CACdoG,+BAA+B,CAAC,IAAI,CAACvG,WAAW,EAAE,IAAI,CAACC,YAAY,CAAC,CACpEgD,SAAS,CAAC;QACTS,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAAClC,gBAAgB,GAAGkC,QAAQ,EAAElC,gBAAgB,IAAI,CAAC;UACvD,MAAM+E,UAAU,GAAG,IAAI,CAAC3C,iBAAiB,CAACF,QAAQ,CAAC;UACnD,MAAM8C,YAAY,GAAGD,UAAU,CAACzC,GAAG,CAAC,CAACC,KAAU,EAAEC,KAAa,MAAM;YAClE,GAAGD,KAAK;YACR5E,EAAE,EAAE4E,KAAK,CAAC5E,EAAE,EAAE+E,QAAQ,EAAE,IAAI,UAAUF,KAAK,EAAE;YAC7C5E,KAAK,EAAE2E,KAAK,CAACnC,IAAI,IAAImC,KAAK,CAAC0C,YAAY,IAAI,eAAe;YAC1D7E,IAAI,EAAEmC,KAAK,CAACnC,IAAI,IAAImC,KAAK,CAAC0C,YAAY,IAAI,eAAe;YACzDpH,WAAW,EACT0E,KAAK,CAAC1E,WAAW,IACjB0E,KAAK,CAAC0C,YAAY,IAClB1C,KAAK,CAACM,IAAI,IACV,gBAAgB;YAClB7E,WAAW,EAAE,IAAI,CAAC8E,UAAU,CAC1BP,KAAK,CAACU,UAAU,IAAIV,KAAK,CAACS,SAAS,IAAI,IAAIkC,IAAI,EAAE,CAClD;YACDhC,SAAS,EAAEX,KAAK,CAACY,KAAK,IAAI,CAAC;YAC3BC,IAAI,EAAE,eAAe;YACrB+B,SAAS,EAAE5C,KAAK,CAAC4C,SAAS,IAAI,EAAE;YAChCC,IAAI,EAAE7C,KAAK,CAAC6C,IAAI,IAAI,EAAE;YACtBC,SAAS,EAAE9C,KAAK,CAAC8C,SAAS,IAAI,EAAE;YAChCC,cAAc,EAAE/C,KAAK,CAAC+C,cAAc,IAAI;WACzC,CAAC,CAAC;UACH,IAAI,CAACvF,uBAAuB,GAAGwF,IAAI,CAACC,IAAI,CACtC,IAAI,CAACxF,gBAAgB,GAAG,IAAI,CAACxB,YAAY,CAC1C;UACD,IAAI,CAACsB,UAAU,GAAG,IAAI,CAACC,uBAAuB;UAC9C,IAAI,CAACR,uBAAuB,GAAGyF,YAAY;UAC3C,IAAI,CAAC7F,eAAe,GAAG6F,YAAY;UACnC,IAAI,CAAC5F,YAAY,GAAG4F,YAAY;UAChC,IAAI,CAAC9G,SAAS,GAAG,KAAK;QACxB,CAAC;QACD0B,KAAK,EAAGA,KAAK,IAAI;UACf6F,OAAO,CAAC7F,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;UAC5D,IAAI,CAACA,KAAK,GAAGA,KAAK,CAAC8F,OAAO,IAAI,qCAAqC;UACnE,IAAI,CAACxH,SAAS,GAAG,KAAK;QACxB;OACD,CAAC;IACN;IAEQkD,qBAAqBA,CAAA;MAC3B,IAAI,CAAClD,SAAS,GAAG,IAAI;MACrB,IAAI,CAACQ,YAAY,CAACiH,sBAAsB,EAAE,CAACnE,SAAS,CAAC;QACnDS,IAAI,EAAGC,QAAQ,IAAI;UACjB,MAAM6C,UAAU,GAAG7C,QAAQ,EAAE0D,gBAAgB,IAAI1D,QAAQ,IAAI,EAAE;UAC/D,MAAM8C,YAAY,GAAGD,UAAU,CAACzC,GAAG,CAAEC,KAAU,KAAM;YACnD,GAAGA,KAAK;YACR5E,EAAE,EAAE4E,KAAK,CAACE,SAAS,EAAEC,QAAQ,EAAE,IAAIH,KAAK,CAAC5E,EAAE,EAAE+E,QAAQ,EAAE,IAAI,GAAG;YAC9D9E,KAAK,EAAE2E,KAAK,CAACI,WAAW,IAAIJ,KAAK,CAACnC,IAAI,IAAI,eAAe;YACzDA,IAAI,EAAEmC,KAAK,CAACI,WAAW,IAAIJ,KAAK,CAACnC,IAAI,IAAI,eAAe;YACxDvC,WAAW,EACT0E,KAAK,CAACK,cAAc,IAAIL,KAAK,CAAC1E,WAAW,IAAI,gBAAgB;YAC/DG,WAAW,EAAE,IAAI,CAAC8E,UAAU,CAACP,KAAK,CAACQ,UAAU,IAAIR,KAAK,CAACS,SAAS,CAAC;YACjEE,SAAS,EAAEX,KAAK,CAACY,KAAK,IAAI,CAAC;YAC3BC,IAAI,EAAE;WACP,CAAC,CAAC;UACH,IAAI,CAAC3D,4BAA4B,GAAGuF,YAAY;UAChD,IAAI,CAAC1F,oBAAoB,GAAG0F,YAAY;UACxC,IAAI,CAAC5F,YAAY,GAAG4F,YAAY;UAChC,IAAI,CAAC3B,qBAAqB,EAAE;UAC5B,IAAI,CAACnF,SAAS,GAAG,KAAK;QACxB,CAAC;QACD0B,KAAK,EAAGA,KAAK,IAAI;UACf6F,OAAO,CAAC7F,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;UACzD,IAAI,CAACA,KAAK,GAAGA,KAAK,CAAC8F,OAAO,IAAI,kCAAkC;UAChE,IAAI,CAACxH,SAAS,GAAG,KAAK;QACxB;OACD,CAAC;IACJ;IAEQ4E,UAAUA,CAAC+C,SAAwB;MACzC,MAAMC,IAAI,GAAG,IAAIZ,IAAI,CAACW,SAAS,CAAC;MAChC,OAAO,GAAGC,IAAI,CAACC,QAAQ,EAAE,GAAG,CAAC,IAAID,IAAI,CAACE,OAAO,EAAE,IAAIF,IAAI,CAACG,WAAW,EAAE,EAAE;IACzE;IAEQ7D,iBAAiBA,CAACF,QAAa;MACrC,IAAI,CAACA,QAAQ,EAAE,OAAO,EAAE;MACxB,MAAMgE,cAAc,GAAG,CACrBhE,QAAQ,CAAC+C,YAAY,EACrB/C,QAAQ,CAACsC,IAAI,EACbtC,QAAQ,CAACiE,MAAM,EACfjE,QAAQ,CAACkE,mBAAmB,CAC7B;MACD,KAAK,MAAMC,GAAG,IAAIH,cAAc,EAAE;QAChC,IAAIpF,KAAK,CAACwF,OAAO,CAACD,GAAG,CAAC,EAAE,OAAOA,GAAG;MACpC;MACA,IAAIvF,KAAK,CAACwF,OAAO,CAACpE,QAAQ,CAAC,EAAE,OAAOA,QAAQ;MAC5C,MAAMqE,aAAa,GAAGC,MAAM,CAACC,IAAI,CAACvE,QAAQ,CAAC,CAACwE,IAAI,CAAEC,GAAG,IACnD7F,KAAK,CAACwF,OAAO,CAACpE,QAAQ,CAACyE,GAAG,CAAC,CAAC,CAC7B;MACD,OAAOJ,aAAa,GAAGrE,QAAQ,CAACqE,aAAa,CAAC,GAAG,EAAE;IACrD;IAEQ1C,kBAAkBA,CAACF,OAAe;MACxC,MAAMiD,OAAO,GAA0B;QACrCC,GAAG,EAAE,IAAI,CAACxH,aAAa;QACvByH,UAAU,EAAE,IAAI,CAACxH,oBAAoB;QACrCyH,aAAa,EAAE,IAAI,CAACxH;OACrB;MACD,MAAMiE,cAAc,GAAGoD,OAAO,CAAC,IAAI,CAAC3J,cAAc,CAAC,IAAI,EAAE;MACzD,MAAM+J,QAAQ,GACZxD,cAAc,CAACkD,IAAI,CAAEnE,KAAK,IAAKA,KAAK,CAAC5E,EAAE,KAAKgG,OAAO,CAAC,IACpD,IAAI,CAACzE,SAAS,CAACwH,IAAI,CAAEnE,KAAU,IAAKA,KAAK,CAAC5E,EAAE,KAAKgG,OAAO,CAAC;MAC3D,IAAI,CAACpD,aAAa,GAAGyG,QAAQ;MAC7B,IAAI,CAAC1G,eAAe,GAAG,IAAI;IAC7B;IAEQwD,SAASA,CAACH,OAAe;MAC/B,MAAMiD,OAAO,GAA0B;QACrCC,GAAG,EAAE,IAAI,CAACxH,aAAa;QACvByH,UAAU,EAAE,IAAI,CAACxH,oBAAoB;QACrCyH,aAAa,EAAE,IAAI,CAACxH;OACrB;MACD,MAAMiE,cAAc,GAAGoD,OAAO,CAAC,IAAI,CAAC3J,cAAc,CAAC,IAAI,EAAE;MACzD,IAAIsF,KAAK,GACPiB,cAAc,CAACkD,IAAI,CAAEO,CAAC,IAAKA,CAAC,CAACtJ,EAAE,KAAKgG,OAAO,CAAC,IAC5C,IAAI,CAACzE,SAAS,CAACwH,IAAI,CAAEO,CAAC,IAAKA,CAAC,CAACtJ,EAAE,KAAKgG,OAAO,CAAC;MAC9C,IAAI,CAACpB,KAAK,EAAE;QACV;MACF;MACA,IAAI2E,SAAS,GAAG3E,KAAK,CAACa,IAAI;MAC1B,MAAM+D,eAAe,GACnB,IAAI,CAAClK,cAAc,KAAK,eAAe,IACvCsF,KAAK,CAAC6C,IAAI,IACV7C,KAAK,CAACM,IAAI,IACVN,KAAK,CAAC8C,SAAS,IACf9C,KAAK,CAAC6E,KAAK;MACbF,SAAS,GAAGA,SAAS,KAAKC,eAAe,GAAG,eAAe,GAAG,YAAY,CAAC;MAC3E,IAAI,CAACvI,MAAM,CAAC+F,QAAQ,CAAC,CAAC,eAAe,EAAEuC,SAAS,CAAC,EAAE;QACjDG,WAAW,EAAE;UACX1J,EAAE,EAAEgG,OAAO;UACX2D,IAAI,EAAE;;OAET,CAAC;IACJ;IAEQvD,cAAcA,CAACJ,OAAe;MACpC,MAAMiD,OAAO,GAA0B;QACrCC,GAAG,EAAE,IAAI,CAACxH,aAAa;QACvByH,UAAU,EAAE,IAAI,CAACxH,oBAAoB;QACrCyH,aAAa,EAAE,IAAI,CAACxH;OACrB;MACD,MAAMiE,cAAc,GAAGoD,OAAO,CAAC,IAAI,CAAC3J,cAAc,CAAC,IAAI,EAAE;MACzD,IAAIsF,KAAK,GAAGiB,cAAc,CAACkD,IAAI,CAAEO,CAAC,IAAKA,CAAC,CAACtJ,EAAE,KAAKgG,OAAO,CAAC;MACxD,IAAI,CAACpB,KAAK,IAAI,IAAI,CAACtF,cAAc,KAAK,KAAK,EAAE;QAC3CsF,KAAK,GAAG,IAAI,CAAClD,aAAa,CAACqH,IAAI,CAAEO,CAAC,IAAKA,CAAC,CAACtJ,EAAE,KAAKgG,OAAO,CAAC;MAC1D;MACA,IAAI,CAACpB,KAAK,EAAE;QACV;MACF;MACA,MAAM4E,eAAe,GACnB,IAAI,CAAClK,cAAc,KAAK,eAAe,IACvCsF,KAAK,CAAC6C,IAAI,IACV7C,KAAK,CAACM,IAAI,IACVN,KAAK,CAAC8C,SAAS,IACf9C,KAAK,CAAC6E,KAAK;MACb,MAAMF,SAAS,GAAGC,eAAe,GAAG,eAAe,GAAG,YAAY;MAClE,IAAI,CAACvI,MAAM,CAAC+F,QAAQ,CAAC,CAAC,eAAe,EAAEuC,SAAS,CAAC,EAAE;QACjDG,WAAW,EAAE;UACX1J,EAAE,EAAEgG,OAAO;UACX2D,IAAI,EAAE;;OAET,CAAC;IACJ;IAEQtD,YAAYA,CAACL,OAAe;MAClC,MAAMiD,OAAO,GAA0B;QACrCC,GAAG,EAAE,IAAI,CAACxH,aAAa;QACvByH,UAAU,EAAE,IAAI,CAACxH,oBAAoB;QACrCyH,aAAa,EAAE,IAAI,CAACxH;OACrB;MACD,MAAMgD,KAAK,GACTqE,OAAO,CAAC,IAAI,CAAC3J,cAAc,CAAC,EAAEyJ,IAAI,CAAEO,CAAC,IAAKA,CAAC,CAACtJ,EAAE,KAAKgG,OAAO,CAAC,IAC3D,IAAI,CAACzE,SAAS,CAACwH,IAAI,CAAEO,CAAC,IAAKA,CAAC,CAACtJ,EAAE,KAAKgG,OAAO,CAAC;MAC9C,IAAI,CAACpB,KAAK,EAAE;QACV;MACF;MACA,MAAM4E,eAAe,GAAG,IAAI,CAACI,oBAAoB,CAAChF,KAAK,CAAC;MACxD,MAAM2E,SAAS,GAAGC,eAAe,GAAG,eAAe,GAAG,YAAY;MAElE,IAAI,CAACvI,MAAM,CAAC+F,QAAQ,CAAC,CAAC,eAAe,EAAEuC,SAAS,EAAE,SAAS,CAAC,EAAE;QAC5DG,WAAW,EAAE;UACX1J,EAAE,EAAEgG;;OAEP,CAAC;IACJ;IAEQ4D,oBAAoBA,CAAChF,KAAU;MACrC,OACEA,KAAK,EAAEa,IAAI,KAAK,eAAe,IAC/B,IAAI,CAACnG,cAAc,KAAK,eAAe,IACvCsF,KAAK,EAAEiF,IAAI,EAAEC,IAAI,CACdC,GAAsB,IAAKA,GAAG,CAAC3I,KAAK,CAAC8C,WAAW,EAAE,KAAK,eAAe,CACxE,IACDU,KAAK,EAAEoF,QAAQ,KAAK,eAAe,IACnCpF,KAAK,EAAE2E,SAAS,KAAK,eAAe,IACpCU,OAAO,CAACrF,KAAK,EAAE6C,IAAI,IAAI7C,KAAK,EAAEM,IAAI,IAAIN,KAAK,EAAE8C,SAAS,IAAI9C,KAAK,EAAE6E,KAAK,CAAC;IAE3E;IAEQlD,WAAWA,CAACP,OAAe;MACjC,MAAMpB,KAAK,GAAG,IAAI,CAACsF,YAAY,CAAClE,OAAO,CAAC;MACxC,IAAI,CAACpB,KAAK,EAAE;QACV;MACF;MACA,MAAM;QAAEnC,IAAI,EAAE0H,SAAS;QAAE1E,IAAI,EAAE8D;MAAS,CAAE,GAAG3E,KAAK;MAClD,MAAMwF,UAAU,GACdb,SAAS,KAAK,eAAe,GACzB,IAAI,CAACxI,YAAY,CAACsJ,wBAAwB,CAACrE,OAAO,CAAC,GACnD,IAAI,CAACjF,YAAY,CAACwF,WAAW,CAACP,OAAO,CAAC;MAE5CoE,UAAU,CAACvG,SAAS,CAAC;QACnBS,IAAI,EAAGC,QAAa,IAAI;UACtB,IAAI,CAAC+F,mBAAmB,CAACtE,OAAO,CAAC;UACjC,IAAI,CAACN,qBAAqB,EAAE;UAC5B,IAAI,CAAC5C,iBAAiB,GAAG,SAAS;UAClC,IAAI,CAACC,mBAAmB,GACtBwB,QAAQ,EAAEwD,OAAO,IAAI,UAAUoC,SAAS,wBAAwB;UAClE,IAAI,CAACtH,gBAAgB,GAAG,IAAI;QAC9B,CAAC;QACDZ,KAAK,EAAGA,KAAU,IAAI;UACpB6F,OAAO,CAAC7F,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC/C;OACD,CAAC;IACJ;IAEQyD,qBAAqBA,CAAA;MAC3B,MAAM6E,UAAU,GAAG,CAAC,IAAI,CAAC3J,WAAW,GAAG,CAAC,IAAI,EAAE;MAC9C,MAAM4J,QAAQ,GAAGD,UAAU,GAAG,EAAE;MAChC,IAAI,CAAC/I,eAAe,GAAG,IAAI,CAACqE,cAAc,CAAC4E,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;MACtE,IAAI,CAACrI,UAAU,GAAGyF,IAAI,CAACC,IAAI,CAAC,IAAI,CAAChC,cAAc,CAACC,MAAM,GAAG,EAAE,CAAC;IAC9D;IAEQoE,YAAYA,CAAClE,OAAe;MAClC,MAAMiD,OAAO,GAA0B;QACrCC,GAAG,EAAE,IAAI,CAACxH,aAAa;QACvByH,UAAU,EAAE,IAAI,CAACxH,oBAAoB;QACrCyH,aAAa,EAAE,IAAI,CAACxH;OACrB;MACD,OAAOqH,OAAO,CAAC,IAAI,CAAC3J,cAAc,CAAC,EAAEyJ,IAAI,CAAEO,CAAC,IAAKA,CAAC,CAACtJ,EAAE,KAAKgG,OAAO,CAAC,IAAI,IAAI;IAC5E;IAEQsE,mBAAmBA,CAACtE,OAAe;MACzC,MAAM0E,UAAU,GAAIC,IAAW,IAC7BA,IAAI,CAAChF,MAAM,CAAEf,KAAK,IAAKA,KAAK,CAAC5E,EAAE,KAAKgG,OAAO,CAAC;MAC9C,QAAQ,IAAI,CAAC1G,cAAc;QACzB,KAAK,KAAK;UACR,IAAI,CAACoC,aAAa,GAAGgJ,UAAU,CAAC,IAAI,CAAChJ,aAAa,CAAC;UACnD,IAAI,CAACG,qBAAqB,GAAG6I,UAAU,CAAC,IAAI,CAAC7I,qBAAqB,CAAC;UACnE;QACF,KAAK,YAAY;UACf,IAAI,CAACF,oBAAoB,GAAG+I,UAAU,CAAC,IAAI,CAAC/I,oBAAoB,CAAC;UACjE,IAAI,CAACG,4BAA4B,GAAG4I,UAAU,CAC5C,IAAI,CAAC5I,4BAA4B,CAClC;UACD;QACF,KAAK,eAAe;UAClB,IAAI,CAACF,uBAAuB,GAAG8I,UAAU,CAAC,IAAI,CAAC9I,uBAAuB,CAAC;UACvE,IAAI,CAACG,+BAA+B,GAAG2I,UAAU,CAC/C,IAAI,CAAC3I,+BAA+B,CACrC;UACD;MACJ;MACA,IAAI,CAACR,SAAS,GAAGmJ,UAAU,CAAC,IAAI,CAACnJ,SAAS,CAAC;MAC3C,IAAI,CAACE,YAAY,GAAGiJ,UAAU,CAAC,IAAI,CAACjJ,YAAY,CAAC;IACnD;IAEAmJ,WAAWA,CAAA;MACT,IAAI,CAACtI,QAAQ,CAACgC,IAAI,EAAE;MACpB,IAAI,CAAChC,QAAQ,CAACuI,QAAQ,EAAE;IAC1B;;uCAnhBW/J,eAAe,EAAAtC,EAAA,CAAAsM,iBAAA,CAAAC,EAAA,CAAAC,mBAAA,GAAAxM,EAAA,CAAAsM,iBAAA,CAAAG,EAAA,CAAAC,sBAAA,GAAA1M,EAAA,CAAAsM,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAA5M,EAAA,CAAAsM,iBAAA,CAAAO,EAAA,CAAAC,WAAA;IAAA;;YAAfxK,eAAe;MAAAyK,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC3CpBrN,EAJR,CAAAC,cAAA,aAAmD,aACC,aACe,cAC9B,qBAM5B;UACCD,EAAA,CAAAuN,SAAA,kBAMW;UAGjBvN,EAFI,CAAAU,YAAA,EAAc,EACT,EACH;UAEJV,EADF,CAAAC,cAAA,aAA8D,sBAK3D;UADCD,EAAA,CAAAE,UAAA,6BAAAsN,iEAAArM,MAAA;YAAA,OAAmBmM,GAAA,CAAAlF,iBAAA,CAAAjH,MAAA,CAAyB;UAAA,EAAC;UAInDnB,EAFI,CAAAU,YAAA,EAAe,EACX,EACF;UAENV,EAAA,CAAAyN,UAAA,IAAAC,8BAAA,iBAAuD;UAwBrD1N,EADF,CAAAC,cAAA,aAAiD,yBAS9C;UAFCD,EAAA,CAAAE,UAAA,uBAAAyN,6DAAA;YAAA,OAAaL,GAAA,CAAA/E,aAAA,EAAe;UAAA,EAAC;UAG/BvI,EAAA,CAAAU,YAAA,EAAgB;UAYhBV,EATA,CAAAyN,UAAA,KAAAG,+BAAA,kBAGC,KAAAC,wCAAA,2BAYA;UAgBH7N,EAAA,CAAAU,YAAA,EAAM;UAGNV,EAAA,CAAAyN,UAAA,KAAAK,+BAAA,kBAA8C;UAUhD9N,EAAA,CAAAU,YAAA,EAAM;;;UA3GMV,EAAA,CAAAY,SAAA,GAAwB;UAAxBZ,EAAA,CAAAe,UAAA,cAAAuM,GAAA,CAAA7I,UAAA,CAAwB;UAUxBzE,EAAA,CAAAY,SAAA,GAAe;UAAfZ,EAAA,CAAAe,UAAA,gBAAe;UAUnBf,EAAA,CAAAY,SAAA,GAAyB;UAAzBZ,EAAA,CAAAe,UAAA,YAAAuM,GAAA,CAAAtJ,aAAA,CAAyB;UAOMhE,EAAA,CAAAY,SAAA,EAAgB;UAAhBZ,EAAA,CAAAe,UAAA,UAAAuM,GAAA,CAAAvL,SAAA,CAAgB;UA0BjD/B,EAAA,CAAAY,SAAA,GAAiB;UAKjBZ,EALA,CAAAe,UAAA,kBAAiB,oBACE,UAAAuM,GAAA,CAAA9J,YAAA,CAAAuK,MAAA,CAAAC,WAAA,CAEsB,cAAAV,GAAA,CAAAvL,SAAA,CAElB;UAOtB/B,EAAA,CAAAY,SAAA,EAAgD;UAAhDZ,EAAA,CAAAe,UAAA,UAAAuM,GAAA,CAAAvL,SAAA,IAAAuL,GAAA,CAAAtK,eAAA,CAAAsE,MAAA,OAAgD;UAS5BtH,EAAA,CAAAY,SAAA,EAGtB;UAHsBZ,EAAA,CAAAe,UAAA,YAAAuM,GAAA,CAAAvL,SAAA,IAAAuL,GAAA,CAAAtK,eAAA,CAAAsE,MAAA,SAAAgG,GAAA,CAAA5I,wBAAA,GAAA4I,GAAA,CAAAtK,eAAA,CAGtB;UAoBehD,EAAA,CAAAY,SAAA,EAA0B;UAA1BZ,EAAA,CAAAe,UAAA,SAAAuM,GAAA,CAAAlG,oBAAA,CAA0B;;;qBDrE1CtI,YAAY,EAAAmP,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZpP,mBAAmB,EACnBW,iBAAiB,EACjBH,mBAAmB,EACnBC,iBAAiB,EACjBI,mBAAmB,EACnBH,aAAa,EACbI,mBAAmB,EAAAgN,EAAA,CAAAuB,aAAA,EAAAvB,EAAA,CAAAwB,eAAA,EAAAxB,EAAA,CAAAyB,oBAAA,EAAAzB,EAAA,CAAA0B,kBAAA,EAAA1B,EAAA,CAAA2B,eAAA,EAEnB7O,eAAe,EACfG,oBAAoB,EACpBC,WAAW;MAAA0O,MAAA;IAAA;;SAKFnM,eAAe;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}