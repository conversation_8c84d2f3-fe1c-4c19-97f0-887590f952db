{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as dom from '../../../base/browser/dom.js';\nimport { DomEmitter } from '../../../base/browser/event.js';\nimport { Event } from '../../../base/common/event.js';\nimport { StandardKeyboardEvent } from '../../../base/browser/keyboardEvent.js';\nimport { Gesture, EventType as GestureEventType } from '../../../base/browser/touch.js';\nimport { renderLabelWithIcons } from '../../../base/browser/ui/iconLabel/iconLabels.js';\nimport { IdGenerator } from '../../../base/common/idGenerator.js';\nimport { parseLinkedText } from '../../../base/common/linkedText.js';\nimport './media/quickInput.css';\nimport { localize } from '../../../nls.js';\nconst iconPathToClass = {};\nconst iconClassGenerator = new IdGenerator('quick-input-button-icon-');\nfunction getIconClass(iconPath) {\n  if (!iconPath) {\n    return undefined;\n  }\n  let iconClass;\n  const key = iconPath.dark.toString();\n  if (iconPathToClass[key]) {\n    iconClass = iconPathToClass[key];\n  } else {\n    iconClass = iconClassGenerator.nextId();\n    dom.createCSSRule(`.${iconClass}, .hc-light .${iconClass}`, `background-image: ${dom.asCSSUrl(iconPath.light || iconPath.dark)}`);\n    dom.createCSSRule(`.vs-dark .${iconClass}, .hc-black .${iconClass}`, `background-image: ${dom.asCSSUrl(iconPath.dark)}`);\n    iconPathToClass[key] = iconClass;\n  }\n  return iconClass;\n}\nexport function quickInputButtonToAction(button, id, run) {\n  let cssClasses = button.iconClass || getIconClass(button.iconPath);\n  if (button.alwaysVisible) {\n    cssClasses = cssClasses ? `${cssClasses} always-visible` : 'always-visible';\n  }\n  return {\n    id,\n    label: '',\n    tooltip: button.tooltip || '',\n    class: cssClasses,\n    enabled: true,\n    run\n  };\n}\nexport function renderQuickInputDescription(description, container, actionHandler) {\n  dom.reset(container);\n  const parsed = parseLinkedText(description);\n  let tabIndex = 0;\n  for (const node of parsed.nodes) {\n    if (typeof node === 'string') {\n      container.append(...renderLabelWithIcons(node));\n    } else {\n      let title = node.title;\n      if (!title && node.href.startsWith('command:')) {\n        title = localize('executeCommand', \"Click to execute command '{0}'\", node.href.substring('command:'.length));\n      } else if (!title) {\n        title = node.href;\n      }\n      const anchor = dom.$('a', {\n        href: node.href,\n        title,\n        tabIndex: tabIndex++\n      }, node.label);\n      anchor.style.textDecoration = 'underline';\n      const handleOpen = e => {\n        if (dom.isEventLike(e)) {\n          dom.EventHelper.stop(e, true);\n        }\n        actionHandler.callback(node.href);\n      };\n      const onClick = actionHandler.disposables.add(new DomEmitter(anchor, dom.EventType.CLICK)).event;\n      const onKeydown = actionHandler.disposables.add(new DomEmitter(anchor, dom.EventType.KEY_DOWN)).event;\n      const onSpaceOrEnter = Event.chain(onKeydown, $ => $.filter(e => {\n        const event = new StandardKeyboardEvent(e);\n        return event.equals(10 /* KeyCode.Space */) || event.equals(3 /* KeyCode.Enter */);\n      }));\n      actionHandler.disposables.add(Gesture.addTarget(anchor));\n      const onTap = actionHandler.disposables.add(new DomEmitter(anchor, GestureEventType.Tap)).event;\n      Event.any(onClick, onTap, onSpaceOrEnter)(handleOpen, null, actionHandler.disposables);\n      container.appendChild(anchor);\n    }\n  }\n}", "map": {"version": 3, "names": ["dom", "DomEmitter", "Event", "StandardKeyboardEvent", "Gesture", "EventType", "GestureEventType", "renderLabelWithIcons", "IdGenerator", "parseLinkedText", "localize", "iconPathToClass", "iconClassGenerator", "getIconClass", "iconPath", "undefined", "iconClass", "key", "dark", "toString", "nextId", "createCSSRule", "asCSSUrl", "light", "quickInputButtonToAction", "button", "id", "run", "cssClasses", "alwaysVisible", "label", "tooltip", "class", "enabled", "renderQuickInputDescription", "description", "container", "actionHandler", "reset", "parsed", "tabIndex", "node", "nodes", "append", "title", "href", "startsWith", "substring", "length", "anchor", "$", "style", "textDecoration", "handleOpen", "e", "isEventLike", "EventHelper", "stop", "callback", "onClick", "disposables", "add", "CLICK", "event", "onKeydown", "KEY_DOWN", "onSpaceOrEnter", "chain", "filter", "equals", "addTarget", "onTap", "Tap", "any", "append<PERSON><PERSON><PERSON>"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/platform/quickinput/browser/quickInputUtils.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as dom from '../../../base/browser/dom.js';\nimport { DomEmitter } from '../../../base/browser/event.js';\nimport { Event } from '../../../base/common/event.js';\nimport { StandardKeyboardEvent } from '../../../base/browser/keyboardEvent.js';\nimport { Gesture, EventType as GestureEventType } from '../../../base/browser/touch.js';\nimport { renderLabelWithIcons } from '../../../base/browser/ui/iconLabel/iconLabels.js';\nimport { IdGenerator } from '../../../base/common/idGenerator.js';\nimport { parseLinkedText } from '../../../base/common/linkedText.js';\nimport './media/quickInput.css';\nimport { localize } from '../../../nls.js';\nconst iconPathToClass = {};\nconst iconClassGenerator = new IdGenerator('quick-input-button-icon-');\nfunction getIconClass(iconPath) {\n    if (!iconPath) {\n        return undefined;\n    }\n    let iconClass;\n    const key = iconPath.dark.toString();\n    if (iconPathToClass[key]) {\n        iconClass = iconPathToClass[key];\n    }\n    else {\n        iconClass = iconClassGenerator.nextId();\n        dom.createCSSRule(`.${iconClass}, .hc-light .${iconClass}`, `background-image: ${dom.asCSSUrl(iconPath.light || iconPath.dark)}`);\n        dom.createCSSRule(`.vs-dark .${iconClass}, .hc-black .${iconClass}`, `background-image: ${dom.asCSSUrl(iconPath.dark)}`);\n        iconPathToClass[key] = iconClass;\n    }\n    return iconClass;\n}\nexport function quickInputButtonToAction(button, id, run) {\n    let cssClasses = button.iconClass || getIconClass(button.iconPath);\n    if (button.alwaysVisible) {\n        cssClasses = cssClasses ? `${cssClasses} always-visible` : 'always-visible';\n    }\n    return {\n        id,\n        label: '',\n        tooltip: button.tooltip || '',\n        class: cssClasses,\n        enabled: true,\n        run\n    };\n}\nexport function renderQuickInputDescription(description, container, actionHandler) {\n    dom.reset(container);\n    const parsed = parseLinkedText(description);\n    let tabIndex = 0;\n    for (const node of parsed.nodes) {\n        if (typeof node === 'string') {\n            container.append(...renderLabelWithIcons(node));\n        }\n        else {\n            let title = node.title;\n            if (!title && node.href.startsWith('command:')) {\n                title = localize('executeCommand', \"Click to execute command '{0}'\", node.href.substring('command:'.length));\n            }\n            else if (!title) {\n                title = node.href;\n            }\n            const anchor = dom.$('a', { href: node.href, title, tabIndex: tabIndex++ }, node.label);\n            anchor.style.textDecoration = 'underline';\n            const handleOpen = (e) => {\n                if (dom.isEventLike(e)) {\n                    dom.EventHelper.stop(e, true);\n                }\n                actionHandler.callback(node.href);\n            };\n            const onClick = actionHandler.disposables.add(new DomEmitter(anchor, dom.EventType.CLICK)).event;\n            const onKeydown = actionHandler.disposables.add(new DomEmitter(anchor, dom.EventType.KEY_DOWN)).event;\n            const onSpaceOrEnter = Event.chain(onKeydown, $ => $.filter(e => {\n                const event = new StandardKeyboardEvent(e);\n                return event.equals(10 /* KeyCode.Space */) || event.equals(3 /* KeyCode.Enter */);\n            }));\n            actionHandler.disposables.add(Gesture.addTarget(anchor));\n            const onTap = actionHandler.disposables.add(new DomEmitter(anchor, GestureEventType.Tap)).event;\n            Event.any(onClick, onTap, onSpaceOrEnter)(handleOpen, null, actionHandler.disposables);\n            container.appendChild(anchor);\n        }\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,KAAKA,GAAG,MAAM,8BAA8B;AACnD,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,KAAK,QAAQ,+BAA+B;AACrD,SAASC,qBAAqB,QAAQ,wCAAwC;AAC9E,SAASC,OAAO,EAAEC,SAAS,IAAIC,gBAAgB,QAAQ,gCAAgC;AACvF,SAASC,oBAAoB,QAAQ,kDAAkD;AACvF,SAASC,WAAW,QAAQ,qCAAqC;AACjE,SAASC,eAAe,QAAQ,oCAAoC;AACpE,OAAO,wBAAwB;AAC/B,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,MAAMC,eAAe,GAAG,CAAC,CAAC;AAC1B,MAAMC,kBAAkB,GAAG,IAAIJ,WAAW,CAAC,0BAA0B,CAAC;AACtE,SAASK,YAAYA,CAACC,QAAQ,EAAE;EAC5B,IAAI,CAACA,QAAQ,EAAE;IACX,OAAOC,SAAS;EACpB;EACA,IAAIC,SAAS;EACb,MAAMC,GAAG,GAAGH,QAAQ,CAACI,IAAI,CAACC,QAAQ,CAAC,CAAC;EACpC,IAAIR,eAAe,CAACM,GAAG,CAAC,EAAE;IACtBD,SAAS,GAAGL,eAAe,CAACM,GAAG,CAAC;EACpC,CAAC,MACI;IACDD,SAAS,GAAGJ,kBAAkB,CAACQ,MAAM,CAAC,CAAC;IACvCpB,GAAG,CAACqB,aAAa,CAAC,IAAIL,SAAS,gBAAgBA,SAAS,EAAE,EAAE,qBAAqBhB,GAAG,CAACsB,QAAQ,CAACR,QAAQ,CAACS,KAAK,IAAIT,QAAQ,CAACI,IAAI,CAAC,EAAE,CAAC;IACjIlB,GAAG,CAACqB,aAAa,CAAC,aAAaL,SAAS,gBAAgBA,SAAS,EAAE,EAAE,qBAAqBhB,GAAG,CAACsB,QAAQ,CAACR,QAAQ,CAACI,IAAI,CAAC,EAAE,CAAC;IACxHP,eAAe,CAACM,GAAG,CAAC,GAAGD,SAAS;EACpC;EACA,OAAOA,SAAS;AACpB;AACA,OAAO,SAASQ,wBAAwBA,CAACC,MAAM,EAAEC,EAAE,EAAEC,GAAG,EAAE;EACtD,IAAIC,UAAU,GAAGH,MAAM,CAACT,SAAS,IAAIH,YAAY,CAACY,MAAM,CAACX,QAAQ,CAAC;EAClE,IAAIW,MAAM,CAACI,aAAa,EAAE;IACtBD,UAAU,GAAGA,UAAU,GAAG,GAAGA,UAAU,iBAAiB,GAAG,gBAAgB;EAC/E;EACA,OAAO;IACHF,EAAE;IACFI,KAAK,EAAE,EAAE;IACTC,OAAO,EAAEN,MAAM,CAACM,OAAO,IAAI,EAAE;IAC7BC,KAAK,EAAEJ,UAAU;IACjBK,OAAO,EAAE,IAAI;IACbN;EACJ,CAAC;AACL;AACA,OAAO,SAASO,2BAA2BA,CAACC,WAAW,EAAEC,SAAS,EAAEC,aAAa,EAAE;EAC/ErC,GAAG,CAACsC,KAAK,CAACF,SAAS,CAAC;EACpB,MAAMG,MAAM,GAAG9B,eAAe,CAAC0B,WAAW,CAAC;EAC3C,IAAIK,QAAQ,GAAG,CAAC;EAChB,KAAK,MAAMC,IAAI,IAAIF,MAAM,CAACG,KAAK,EAAE;IAC7B,IAAI,OAAOD,IAAI,KAAK,QAAQ,EAAE;MAC1BL,SAAS,CAACO,MAAM,CAAC,GAAGpC,oBAAoB,CAACkC,IAAI,CAAC,CAAC;IACnD,CAAC,MACI;MACD,IAAIG,KAAK,GAAGH,IAAI,CAACG,KAAK;MACtB,IAAI,CAACA,KAAK,IAAIH,IAAI,CAACI,IAAI,CAACC,UAAU,CAAC,UAAU,CAAC,EAAE;QAC5CF,KAAK,GAAGlC,QAAQ,CAAC,gBAAgB,EAAE,gCAAgC,EAAE+B,IAAI,CAACI,IAAI,CAACE,SAAS,CAAC,UAAU,CAACC,MAAM,CAAC,CAAC;MAChH,CAAC,MACI,IAAI,CAACJ,KAAK,EAAE;QACbA,KAAK,GAAGH,IAAI,CAACI,IAAI;MACrB;MACA,MAAMI,MAAM,GAAGjD,GAAG,CAACkD,CAAC,CAAC,GAAG,EAAE;QAAEL,IAAI,EAAEJ,IAAI,CAACI,IAAI;QAAED,KAAK;QAAEJ,QAAQ,EAAEA,QAAQ;MAAG,CAAC,EAAEC,IAAI,CAACX,KAAK,CAAC;MACvFmB,MAAM,CAACE,KAAK,CAACC,cAAc,GAAG,WAAW;MACzC,MAAMC,UAAU,GAAIC,CAAC,IAAK;QACtB,IAAItD,GAAG,CAACuD,WAAW,CAACD,CAAC,CAAC,EAAE;UACpBtD,GAAG,CAACwD,WAAW,CAACC,IAAI,CAACH,CAAC,EAAE,IAAI,CAAC;QACjC;QACAjB,aAAa,CAACqB,QAAQ,CAACjB,IAAI,CAACI,IAAI,CAAC;MACrC,CAAC;MACD,MAAMc,OAAO,GAAGtB,aAAa,CAACuB,WAAW,CAACC,GAAG,CAAC,IAAI5D,UAAU,CAACgD,MAAM,EAAEjD,GAAG,CAACK,SAAS,CAACyD,KAAK,CAAC,CAAC,CAACC,KAAK;MAChG,MAAMC,SAAS,GAAG3B,aAAa,CAACuB,WAAW,CAACC,GAAG,CAAC,IAAI5D,UAAU,CAACgD,MAAM,EAAEjD,GAAG,CAACK,SAAS,CAAC4D,QAAQ,CAAC,CAAC,CAACF,KAAK;MACrG,MAAMG,cAAc,GAAGhE,KAAK,CAACiE,KAAK,CAACH,SAAS,EAAEd,CAAC,IAAIA,CAAC,CAACkB,MAAM,CAACd,CAAC,IAAI;QAC7D,MAAMS,KAAK,GAAG,IAAI5D,qBAAqB,CAACmD,CAAC,CAAC;QAC1C,OAAOS,KAAK,CAACM,MAAM,CAAC,EAAE,CAAC,mBAAmB,CAAC,IAAIN,KAAK,CAACM,MAAM,CAAC,CAAC,CAAC,mBAAmB,CAAC;MACtF,CAAC,CAAC,CAAC;MACHhC,aAAa,CAACuB,WAAW,CAACC,GAAG,CAACzD,OAAO,CAACkE,SAAS,CAACrB,MAAM,CAAC,CAAC;MACxD,MAAMsB,KAAK,GAAGlC,aAAa,CAACuB,WAAW,CAACC,GAAG,CAAC,IAAI5D,UAAU,CAACgD,MAAM,EAAE3C,gBAAgB,CAACkE,GAAG,CAAC,CAAC,CAACT,KAAK;MAC/F7D,KAAK,CAACuE,GAAG,CAACd,OAAO,EAAEY,KAAK,EAAEL,cAAc,CAAC,CAACb,UAAU,EAAE,IAAI,EAAEhB,aAAa,CAACuB,WAAW,CAAC;MACtFxB,SAAS,CAACsC,WAAW,CAACzB,MAAM,CAAC;IACjC;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}