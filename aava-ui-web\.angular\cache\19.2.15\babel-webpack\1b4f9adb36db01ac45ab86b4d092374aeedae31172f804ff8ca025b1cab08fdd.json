{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { parse } from '../../../base/common/glob.js';\nimport { Mimes } from '../../../base/common/mime.js';\nimport { Schemas } from '../../../base/common/network.js';\nimport { basename, posix } from '../../../base/common/path.js';\nimport { DataUri } from '../../../base/common/resources.js';\nimport { startsWithUTF8BOM } from '../../../base/common/strings.js';\nimport { PLAINTEXT_LANGUAGE_ID } from '../languages/modesRegistry.js';\nlet registeredAssociations = [];\nlet nonUserRegisteredAssociations = [];\nlet userRegisteredAssociations = [];\n/**\n * Associate a language to the registry (platform).\n * * **NOTE**: This association will lose over associations registered using `registerConfiguredLanguageAssociation`.\n * * **NOTE**: Use `clearPlatformLanguageAssociations` to remove all associations registered using this function.\n */\nexport function registerPlatformLanguageAssociation(association, warnOnOverwrite = false) {\n  _registerLanguageAssociation(association, false, warnOnOverwrite);\n}\nfunction _registerLanguageAssociation(association, userConfigured, warnOnOverwrite) {\n  // Register\n  const associationItem = toLanguageAssociationItem(association, userConfigured);\n  registeredAssociations.push(associationItem);\n  if (!associationItem.userConfigured) {\n    nonUserRegisteredAssociations.push(associationItem);\n  } else {\n    userRegisteredAssociations.push(associationItem);\n  }\n  // Check for conflicts unless this is a user configured association\n  if (warnOnOverwrite && !associationItem.userConfigured) {\n    registeredAssociations.forEach(a => {\n      if (a.mime === associationItem.mime || a.userConfigured) {\n        return; // same mime or userConfigured is ok\n      }\n      if (associationItem.extension && a.extension === associationItem.extension) {\n        console.warn(`Overwriting extension <<${associationItem.extension}>> to now point to mime <<${associationItem.mime}>>`);\n      }\n      if (associationItem.filename && a.filename === associationItem.filename) {\n        console.warn(`Overwriting filename <<${associationItem.filename}>> to now point to mime <<${associationItem.mime}>>`);\n      }\n      if (associationItem.filepattern && a.filepattern === associationItem.filepattern) {\n        console.warn(`Overwriting filepattern <<${associationItem.filepattern}>> to now point to mime <<${associationItem.mime}>>`);\n      }\n      if (associationItem.firstline && a.firstline === associationItem.firstline) {\n        console.warn(`Overwriting firstline <<${associationItem.firstline}>> to now point to mime <<${associationItem.mime}>>`);\n      }\n    });\n  }\n}\nfunction toLanguageAssociationItem(association, userConfigured) {\n  return {\n    id: association.id,\n    mime: association.mime,\n    filename: association.filename,\n    extension: association.extension,\n    filepattern: association.filepattern,\n    firstline: association.firstline,\n    userConfigured: userConfigured,\n    filenameLowercase: association.filename ? association.filename.toLowerCase() : undefined,\n    extensionLowercase: association.extension ? association.extension.toLowerCase() : undefined,\n    filepatternLowercase: association.filepattern ? parse(association.filepattern.toLowerCase()) : undefined,\n    filepatternOnPath: association.filepattern ? association.filepattern.indexOf(posix.sep) >= 0 : false\n  };\n}\n/**\n * Clear language associations from the registry (platform).\n */\nexport function clearPlatformLanguageAssociations() {\n  registeredAssociations = registeredAssociations.filter(a => a.userConfigured);\n  nonUserRegisteredAssociations = [];\n}\n/**\n * @see `getMimeTypes`\n */\nexport function getLanguageIds(resource, firstLine) {\n  return getAssociations(resource, firstLine).map(item => item.id);\n}\nfunction getAssociations(resource, firstLine) {\n  let path;\n  if (resource) {\n    switch (resource.scheme) {\n      case Schemas.file:\n        path = resource.fsPath;\n        break;\n      case Schemas.data:\n        {\n          const metadata = DataUri.parseMetaData(resource);\n          path = metadata.get(DataUri.META_DATA_LABEL);\n          break;\n        }\n      case Schemas.vscodeNotebookCell:\n        // File path not relevant for language detection of cell\n        path = undefined;\n        break;\n      default:\n        path = resource.path;\n    }\n  }\n  if (!path) {\n    return [{\n      id: 'unknown',\n      mime: Mimes.unknown\n    }];\n  }\n  path = path.toLowerCase();\n  const filename = basename(path);\n  // 1.) User configured mappings have highest priority\n  const configuredLanguage = getAssociationByPath(path, filename, userRegisteredAssociations);\n  if (configuredLanguage) {\n    return [configuredLanguage, {\n      id: PLAINTEXT_LANGUAGE_ID,\n      mime: Mimes.text\n    }];\n  }\n  // 2.) Registered mappings have middle priority\n  const registeredLanguage = getAssociationByPath(path, filename, nonUserRegisteredAssociations);\n  if (registeredLanguage) {\n    return [registeredLanguage, {\n      id: PLAINTEXT_LANGUAGE_ID,\n      mime: Mimes.text\n    }];\n  }\n  // 3.) Firstline has lowest priority\n  if (firstLine) {\n    const firstlineLanguage = getAssociationByFirstline(firstLine);\n    if (firstlineLanguage) {\n      return [firstlineLanguage, {\n        id: PLAINTEXT_LANGUAGE_ID,\n        mime: Mimes.text\n      }];\n    }\n  }\n  return [{\n    id: 'unknown',\n    mime: Mimes.unknown\n  }];\n}\nfunction getAssociationByPath(path, filename, associations) {\n  let filenameMatch = undefined;\n  let patternMatch = undefined;\n  let extensionMatch = undefined;\n  // We want to prioritize associations based on the order they are registered so that the last registered\n  // association wins over all other. This is for https://github.com/microsoft/vscode/issues/20074\n  for (let i = associations.length - 1; i >= 0; i--) {\n    const association = associations[i];\n    // First exact name match\n    if (filename === association.filenameLowercase) {\n      filenameMatch = association;\n      break; // take it!\n    }\n    // Longest pattern match\n    if (association.filepattern) {\n      if (!patternMatch || association.filepattern.length > patternMatch.filepattern.length) {\n        const target = association.filepatternOnPath ? path : filename; // match on full path if pattern contains path separator\n        if (association.filepatternLowercase?.(target)) {\n          patternMatch = association;\n        }\n      }\n    }\n    // Longest extension match\n    if (association.extension) {\n      if (!extensionMatch || association.extension.length > extensionMatch.extension.length) {\n        if (filename.endsWith(association.extensionLowercase)) {\n          extensionMatch = association;\n        }\n      }\n    }\n  }\n  // 1.) Exact name match has second highest priority\n  if (filenameMatch) {\n    return filenameMatch;\n  }\n  // 2.) Match on pattern\n  if (patternMatch) {\n    return patternMatch;\n  }\n  // 3.) Match on extension comes next\n  if (extensionMatch) {\n    return extensionMatch;\n  }\n  return undefined;\n}\nfunction getAssociationByFirstline(firstLine) {\n  if (startsWithUTF8BOM(firstLine)) {\n    firstLine = firstLine.substr(1);\n  }\n  if (firstLine.length > 0) {\n    // We want to prioritize associations based on the order they are registered so that the last registered\n    // association wins over all other. This is for https://github.com/microsoft/vscode/issues/20074\n    for (let i = registeredAssociations.length - 1; i >= 0; i--) {\n      const association = registeredAssociations[i];\n      if (!association.firstline) {\n        continue;\n      }\n      const matches = firstLine.match(association.firstline);\n      if (matches && matches.length > 0) {\n        return association;\n      }\n    }\n  }\n  return undefined;\n}", "map": {"version": 3, "names": ["parse", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "basename", "posix", "DataUri", "startsWithUTF8BOM", "PLAINTEXT_LANGUAGE_ID", "registeredAssociations", "nonUserRegisteredAssociations", "userRegisteredAssociations", "registerPlatformLanguageAssociation", "association", "warnOnOverwrite", "_registerLanguageAssociation", "userConfigured", "associationItem", "toLanguageAssociationItem", "push", "for<PERSON>ach", "a", "mime", "extension", "console", "warn", "filename", "filepattern", "firstline", "id", "filenameLowercase", "toLowerCase", "undefined", "extensionLowercase", "filepatternLowercase", "filepatternOnPath", "indexOf", "sep", "clearPlatformLanguageAssociations", "filter", "getLanguageIds", "resource", "firstLine", "getAssociations", "map", "item", "path", "scheme", "file", "fsPath", "data", "metadata", "parseMetaData", "get", "META_DATA_LABEL", "vscodeNotebookCell", "unknown", "configuredLanguage", "getAssociationByPath", "text", "registeredLanguage", "firstlineLanguage", "getAssociationByFirstline", "associations", "filenameMatch", "patternMatch", "extensionMatch", "i", "length", "target", "endsWith", "substr", "matches", "match"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/services/languagesAssociations.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { parse } from '../../../base/common/glob.js';\nimport { Mimes } from '../../../base/common/mime.js';\nimport { Schemas } from '../../../base/common/network.js';\nimport { basename, posix } from '../../../base/common/path.js';\nimport { DataUri } from '../../../base/common/resources.js';\nimport { startsWithUTF8BOM } from '../../../base/common/strings.js';\nimport { PLAINTEXT_LANGUAGE_ID } from '../languages/modesRegistry.js';\nlet registeredAssociations = [];\nlet nonUserRegisteredAssociations = [];\nlet userRegisteredAssociations = [];\n/**\n * Associate a language to the registry (platform).\n * * **NOTE**: This association will lose over associations registered using `registerConfiguredLanguageAssociation`.\n * * **NOTE**: Use `clearPlatformLanguageAssociations` to remove all associations registered using this function.\n */\nexport function registerPlatformLanguageAssociation(association, warnOnOverwrite = false) {\n    _registerLanguageAssociation(association, false, warnOnOverwrite);\n}\nfunction _registerLanguageAssociation(association, userConfigured, warnOnOverwrite) {\n    // Register\n    const associationItem = toLanguageAssociationItem(association, userConfigured);\n    registeredAssociations.push(associationItem);\n    if (!associationItem.userConfigured) {\n        nonUserRegisteredAssociations.push(associationItem);\n    }\n    else {\n        userRegisteredAssociations.push(associationItem);\n    }\n    // Check for conflicts unless this is a user configured association\n    if (warnOnOverwrite && !associationItem.userConfigured) {\n        registeredAssociations.forEach(a => {\n            if (a.mime === associationItem.mime || a.userConfigured) {\n                return; // same mime or userConfigured is ok\n            }\n            if (associationItem.extension && a.extension === associationItem.extension) {\n                console.warn(`Overwriting extension <<${associationItem.extension}>> to now point to mime <<${associationItem.mime}>>`);\n            }\n            if (associationItem.filename && a.filename === associationItem.filename) {\n                console.warn(`Overwriting filename <<${associationItem.filename}>> to now point to mime <<${associationItem.mime}>>`);\n            }\n            if (associationItem.filepattern && a.filepattern === associationItem.filepattern) {\n                console.warn(`Overwriting filepattern <<${associationItem.filepattern}>> to now point to mime <<${associationItem.mime}>>`);\n            }\n            if (associationItem.firstline && a.firstline === associationItem.firstline) {\n                console.warn(`Overwriting firstline <<${associationItem.firstline}>> to now point to mime <<${associationItem.mime}>>`);\n            }\n        });\n    }\n}\nfunction toLanguageAssociationItem(association, userConfigured) {\n    return {\n        id: association.id,\n        mime: association.mime,\n        filename: association.filename,\n        extension: association.extension,\n        filepattern: association.filepattern,\n        firstline: association.firstline,\n        userConfigured: userConfigured,\n        filenameLowercase: association.filename ? association.filename.toLowerCase() : undefined,\n        extensionLowercase: association.extension ? association.extension.toLowerCase() : undefined,\n        filepatternLowercase: association.filepattern ? parse(association.filepattern.toLowerCase()) : undefined,\n        filepatternOnPath: association.filepattern ? association.filepattern.indexOf(posix.sep) >= 0 : false\n    };\n}\n/**\n * Clear language associations from the registry (platform).\n */\nexport function clearPlatformLanguageAssociations() {\n    registeredAssociations = registeredAssociations.filter(a => a.userConfigured);\n    nonUserRegisteredAssociations = [];\n}\n/**\n * @see `getMimeTypes`\n */\nexport function getLanguageIds(resource, firstLine) {\n    return getAssociations(resource, firstLine).map(item => item.id);\n}\nfunction getAssociations(resource, firstLine) {\n    let path;\n    if (resource) {\n        switch (resource.scheme) {\n            case Schemas.file:\n                path = resource.fsPath;\n                break;\n            case Schemas.data: {\n                const metadata = DataUri.parseMetaData(resource);\n                path = metadata.get(DataUri.META_DATA_LABEL);\n                break;\n            }\n            case Schemas.vscodeNotebookCell:\n                // File path not relevant for language detection of cell\n                path = undefined;\n                break;\n            default:\n                path = resource.path;\n        }\n    }\n    if (!path) {\n        return [{ id: 'unknown', mime: Mimes.unknown }];\n    }\n    path = path.toLowerCase();\n    const filename = basename(path);\n    // 1.) User configured mappings have highest priority\n    const configuredLanguage = getAssociationByPath(path, filename, userRegisteredAssociations);\n    if (configuredLanguage) {\n        return [configuredLanguage, { id: PLAINTEXT_LANGUAGE_ID, mime: Mimes.text }];\n    }\n    // 2.) Registered mappings have middle priority\n    const registeredLanguage = getAssociationByPath(path, filename, nonUserRegisteredAssociations);\n    if (registeredLanguage) {\n        return [registeredLanguage, { id: PLAINTEXT_LANGUAGE_ID, mime: Mimes.text }];\n    }\n    // 3.) Firstline has lowest priority\n    if (firstLine) {\n        const firstlineLanguage = getAssociationByFirstline(firstLine);\n        if (firstlineLanguage) {\n            return [firstlineLanguage, { id: PLAINTEXT_LANGUAGE_ID, mime: Mimes.text }];\n        }\n    }\n    return [{ id: 'unknown', mime: Mimes.unknown }];\n}\nfunction getAssociationByPath(path, filename, associations) {\n    let filenameMatch = undefined;\n    let patternMatch = undefined;\n    let extensionMatch = undefined;\n    // We want to prioritize associations based on the order they are registered so that the last registered\n    // association wins over all other. This is for https://github.com/microsoft/vscode/issues/20074\n    for (let i = associations.length - 1; i >= 0; i--) {\n        const association = associations[i];\n        // First exact name match\n        if (filename === association.filenameLowercase) {\n            filenameMatch = association;\n            break; // take it!\n        }\n        // Longest pattern match\n        if (association.filepattern) {\n            if (!patternMatch || association.filepattern.length > patternMatch.filepattern.length) {\n                const target = association.filepatternOnPath ? path : filename; // match on full path if pattern contains path separator\n                if (association.filepatternLowercase?.(target)) {\n                    patternMatch = association;\n                }\n            }\n        }\n        // Longest extension match\n        if (association.extension) {\n            if (!extensionMatch || association.extension.length > extensionMatch.extension.length) {\n                if (filename.endsWith(association.extensionLowercase)) {\n                    extensionMatch = association;\n                }\n            }\n        }\n    }\n    // 1.) Exact name match has second highest priority\n    if (filenameMatch) {\n        return filenameMatch;\n    }\n    // 2.) Match on pattern\n    if (patternMatch) {\n        return patternMatch;\n    }\n    // 3.) Match on extension comes next\n    if (extensionMatch) {\n        return extensionMatch;\n    }\n    return undefined;\n}\nfunction getAssociationByFirstline(firstLine) {\n    if (startsWithUTF8BOM(firstLine)) {\n        firstLine = firstLine.substr(1);\n    }\n    if (firstLine.length > 0) {\n        // We want to prioritize associations based on the order they are registered so that the last registered\n        // association wins over all other. This is for https://github.com/microsoft/vscode/issues/20074\n        for (let i = registeredAssociations.length - 1; i >= 0; i--) {\n            const association = registeredAssociations[i];\n            if (!association.firstline) {\n                continue;\n            }\n            const matches = firstLine.match(association.firstline);\n            if (matches && matches.length > 0) {\n                return association;\n            }\n        }\n    }\n    return undefined;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,KAAK,QAAQ,8BAA8B;AACpD,SAASC,KAAK,QAAQ,8BAA8B;AACpD,SAASC,OAAO,QAAQ,iCAAiC;AACzD,SAASC,QAAQ,EAAEC,KAAK,QAAQ,8BAA8B;AAC9D,SAASC,OAAO,QAAQ,mCAAmC;AAC3D,SAASC,iBAAiB,QAAQ,iCAAiC;AACnE,SAASC,qBAAqB,QAAQ,+BAA+B;AACrE,IAAIC,sBAAsB,GAAG,EAAE;AAC/B,IAAIC,6BAA6B,GAAG,EAAE;AACtC,IAAIC,0BAA0B,GAAG,EAAE;AACnC;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,mCAAmCA,CAACC,WAAW,EAAEC,eAAe,GAAG,KAAK,EAAE;EACtFC,4BAA4B,CAACF,WAAW,EAAE,KAAK,EAAEC,eAAe,CAAC;AACrE;AACA,SAASC,4BAA4BA,CAACF,WAAW,EAAEG,cAAc,EAAEF,eAAe,EAAE;EAChF;EACA,MAAMG,eAAe,GAAGC,yBAAyB,CAACL,WAAW,EAAEG,cAAc,CAAC;EAC9EP,sBAAsB,CAACU,IAAI,CAACF,eAAe,CAAC;EAC5C,IAAI,CAACA,eAAe,CAACD,cAAc,EAAE;IACjCN,6BAA6B,CAACS,IAAI,CAACF,eAAe,CAAC;EACvD,CAAC,MACI;IACDN,0BAA0B,CAACQ,IAAI,CAACF,eAAe,CAAC;EACpD;EACA;EACA,IAAIH,eAAe,IAAI,CAACG,eAAe,CAACD,cAAc,EAAE;IACpDP,sBAAsB,CAACW,OAAO,CAACC,CAAC,IAAI;MAChC,IAAIA,CAAC,CAACC,IAAI,KAAKL,eAAe,CAACK,IAAI,IAAID,CAAC,CAACL,cAAc,EAAE;QACrD,OAAO,CAAC;MACZ;MACA,IAAIC,eAAe,CAACM,SAAS,IAAIF,CAAC,CAACE,SAAS,KAAKN,eAAe,CAACM,SAAS,EAAE;QACxEC,OAAO,CAACC,IAAI,CAAC,2BAA2BR,eAAe,CAACM,SAAS,6BAA6BN,eAAe,CAACK,IAAI,IAAI,CAAC;MAC3H;MACA,IAAIL,eAAe,CAACS,QAAQ,IAAIL,CAAC,CAACK,QAAQ,KAAKT,eAAe,CAACS,QAAQ,EAAE;QACrEF,OAAO,CAACC,IAAI,CAAC,0BAA0BR,eAAe,CAACS,QAAQ,6BAA6BT,eAAe,CAACK,IAAI,IAAI,CAAC;MACzH;MACA,IAAIL,eAAe,CAACU,WAAW,IAAIN,CAAC,CAACM,WAAW,KAAKV,eAAe,CAACU,WAAW,EAAE;QAC9EH,OAAO,CAACC,IAAI,CAAC,6BAA6BR,eAAe,CAACU,WAAW,6BAA6BV,eAAe,CAACK,IAAI,IAAI,CAAC;MAC/H;MACA,IAAIL,eAAe,CAACW,SAAS,IAAIP,CAAC,CAACO,SAAS,KAAKX,eAAe,CAACW,SAAS,EAAE;QACxEJ,OAAO,CAACC,IAAI,CAAC,2BAA2BR,eAAe,CAACW,SAAS,6BAA6BX,eAAe,CAACK,IAAI,IAAI,CAAC;MAC3H;IACJ,CAAC,CAAC;EACN;AACJ;AACA,SAASJ,yBAAyBA,CAACL,WAAW,EAAEG,cAAc,EAAE;EAC5D,OAAO;IACHa,EAAE,EAAEhB,WAAW,CAACgB,EAAE;IAClBP,IAAI,EAAET,WAAW,CAACS,IAAI;IACtBI,QAAQ,EAAEb,WAAW,CAACa,QAAQ;IAC9BH,SAAS,EAAEV,WAAW,CAACU,SAAS;IAChCI,WAAW,EAAEd,WAAW,CAACc,WAAW;IACpCC,SAAS,EAAEf,WAAW,CAACe,SAAS;IAChCZ,cAAc,EAAEA,cAAc;IAC9Bc,iBAAiB,EAAEjB,WAAW,CAACa,QAAQ,GAAGb,WAAW,CAACa,QAAQ,CAACK,WAAW,CAAC,CAAC,GAAGC,SAAS;IACxFC,kBAAkB,EAAEpB,WAAW,CAACU,SAAS,GAAGV,WAAW,CAACU,SAAS,CAACQ,WAAW,CAAC,CAAC,GAAGC,SAAS;IAC3FE,oBAAoB,EAAErB,WAAW,CAACc,WAAW,GAAG1B,KAAK,CAACY,WAAW,CAACc,WAAW,CAACI,WAAW,CAAC,CAAC,CAAC,GAAGC,SAAS;IACxGG,iBAAiB,EAAEtB,WAAW,CAACc,WAAW,GAAGd,WAAW,CAACc,WAAW,CAACS,OAAO,CAAC/B,KAAK,CAACgC,GAAG,CAAC,IAAI,CAAC,GAAG;EACnG,CAAC;AACL;AACA;AACA;AACA;AACA,OAAO,SAASC,iCAAiCA,CAAA,EAAG;EAChD7B,sBAAsB,GAAGA,sBAAsB,CAAC8B,MAAM,CAAClB,CAAC,IAAIA,CAAC,CAACL,cAAc,CAAC;EAC7EN,6BAA6B,GAAG,EAAE;AACtC;AACA;AACA;AACA;AACA,OAAO,SAAS8B,cAAcA,CAACC,QAAQ,EAAEC,SAAS,EAAE;EAChD,OAAOC,eAAe,CAACF,QAAQ,EAAEC,SAAS,CAAC,CAACE,GAAG,CAACC,IAAI,IAAIA,IAAI,CAAChB,EAAE,CAAC;AACpE;AACA,SAASc,eAAeA,CAACF,QAAQ,EAAEC,SAAS,EAAE;EAC1C,IAAII,IAAI;EACR,IAAIL,QAAQ,EAAE;IACV,QAAQA,QAAQ,CAACM,MAAM;MACnB,KAAK5C,OAAO,CAAC6C,IAAI;QACbF,IAAI,GAAGL,QAAQ,CAACQ,MAAM;QACtB;MACJ,KAAK9C,OAAO,CAAC+C,IAAI;QAAE;UACf,MAAMC,QAAQ,GAAG7C,OAAO,CAAC8C,aAAa,CAACX,QAAQ,CAAC;UAChDK,IAAI,GAAGK,QAAQ,CAACE,GAAG,CAAC/C,OAAO,CAACgD,eAAe,CAAC;UAC5C;QACJ;MACA,KAAKnD,OAAO,CAACoD,kBAAkB;QAC3B;QACAT,IAAI,GAAGd,SAAS;QAChB;MACJ;QACIc,IAAI,GAAGL,QAAQ,CAACK,IAAI;IAC5B;EACJ;EACA,IAAI,CAACA,IAAI,EAAE;IACP,OAAO,CAAC;MAAEjB,EAAE,EAAE,SAAS;MAAEP,IAAI,EAAEpB,KAAK,CAACsD;IAAQ,CAAC,CAAC;EACnD;EACAV,IAAI,GAAGA,IAAI,CAACf,WAAW,CAAC,CAAC;EACzB,MAAML,QAAQ,GAAGtB,QAAQ,CAAC0C,IAAI,CAAC;EAC/B;EACA,MAAMW,kBAAkB,GAAGC,oBAAoB,CAACZ,IAAI,EAAEpB,QAAQ,EAAEf,0BAA0B,CAAC;EAC3F,IAAI8C,kBAAkB,EAAE;IACpB,OAAO,CAACA,kBAAkB,EAAE;MAAE5B,EAAE,EAAErB,qBAAqB;MAAEc,IAAI,EAAEpB,KAAK,CAACyD;IAAK,CAAC,CAAC;EAChF;EACA;EACA,MAAMC,kBAAkB,GAAGF,oBAAoB,CAACZ,IAAI,EAAEpB,QAAQ,EAAEhB,6BAA6B,CAAC;EAC9F,IAAIkD,kBAAkB,EAAE;IACpB,OAAO,CAACA,kBAAkB,EAAE;MAAE/B,EAAE,EAAErB,qBAAqB;MAAEc,IAAI,EAAEpB,KAAK,CAACyD;IAAK,CAAC,CAAC;EAChF;EACA;EACA,IAAIjB,SAAS,EAAE;IACX,MAAMmB,iBAAiB,GAAGC,yBAAyB,CAACpB,SAAS,CAAC;IAC9D,IAAImB,iBAAiB,EAAE;MACnB,OAAO,CAACA,iBAAiB,EAAE;QAAEhC,EAAE,EAAErB,qBAAqB;QAAEc,IAAI,EAAEpB,KAAK,CAACyD;MAAK,CAAC,CAAC;IAC/E;EACJ;EACA,OAAO,CAAC;IAAE9B,EAAE,EAAE,SAAS;IAAEP,IAAI,EAAEpB,KAAK,CAACsD;EAAQ,CAAC,CAAC;AACnD;AACA,SAASE,oBAAoBA,CAACZ,IAAI,EAAEpB,QAAQ,EAAEqC,YAAY,EAAE;EACxD,IAAIC,aAAa,GAAGhC,SAAS;EAC7B,IAAIiC,YAAY,GAAGjC,SAAS;EAC5B,IAAIkC,cAAc,GAAGlC,SAAS;EAC9B;EACA;EACA,KAAK,IAAImC,CAAC,GAAGJ,YAAY,CAACK,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IAC/C,MAAMtD,WAAW,GAAGkD,YAAY,CAACI,CAAC,CAAC;IACnC;IACA,IAAIzC,QAAQ,KAAKb,WAAW,CAACiB,iBAAiB,EAAE;MAC5CkC,aAAa,GAAGnD,WAAW;MAC3B,MAAM,CAAC;IACX;IACA;IACA,IAAIA,WAAW,CAACc,WAAW,EAAE;MACzB,IAAI,CAACsC,YAAY,IAAIpD,WAAW,CAACc,WAAW,CAACyC,MAAM,GAAGH,YAAY,CAACtC,WAAW,CAACyC,MAAM,EAAE;QACnF,MAAMC,MAAM,GAAGxD,WAAW,CAACsB,iBAAiB,GAAGW,IAAI,GAAGpB,QAAQ,CAAC,CAAC;QAChE,IAAIb,WAAW,CAACqB,oBAAoB,GAAGmC,MAAM,CAAC,EAAE;UAC5CJ,YAAY,GAAGpD,WAAW;QAC9B;MACJ;IACJ;IACA;IACA,IAAIA,WAAW,CAACU,SAAS,EAAE;MACvB,IAAI,CAAC2C,cAAc,IAAIrD,WAAW,CAACU,SAAS,CAAC6C,MAAM,GAAGF,cAAc,CAAC3C,SAAS,CAAC6C,MAAM,EAAE;QACnF,IAAI1C,QAAQ,CAAC4C,QAAQ,CAACzD,WAAW,CAACoB,kBAAkB,CAAC,EAAE;UACnDiC,cAAc,GAAGrD,WAAW;QAChC;MACJ;IACJ;EACJ;EACA;EACA,IAAImD,aAAa,EAAE;IACf,OAAOA,aAAa;EACxB;EACA;EACA,IAAIC,YAAY,EAAE;IACd,OAAOA,YAAY;EACvB;EACA;EACA,IAAIC,cAAc,EAAE;IAChB,OAAOA,cAAc;EACzB;EACA,OAAOlC,SAAS;AACpB;AACA,SAAS8B,yBAAyBA,CAACpB,SAAS,EAAE;EAC1C,IAAInC,iBAAiB,CAACmC,SAAS,CAAC,EAAE;IAC9BA,SAAS,GAAGA,SAAS,CAAC6B,MAAM,CAAC,CAAC,CAAC;EACnC;EACA,IAAI7B,SAAS,CAAC0B,MAAM,GAAG,CAAC,EAAE;IACtB;IACA;IACA,KAAK,IAAID,CAAC,GAAG1D,sBAAsB,CAAC2D,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MACzD,MAAMtD,WAAW,GAAGJ,sBAAsB,CAAC0D,CAAC,CAAC;MAC7C,IAAI,CAACtD,WAAW,CAACe,SAAS,EAAE;QACxB;MACJ;MACA,MAAM4C,OAAO,GAAG9B,SAAS,CAAC+B,KAAK,CAAC5D,WAAW,CAACe,SAAS,CAAC;MACtD,IAAI4C,OAAO,IAAIA,OAAO,CAACJ,MAAM,GAAG,CAAC,EAAE;QAC/B,OAAOvD,WAAW;MACtB;IACJ;EACJ;EACA,OAAOmB,SAAS;AACpB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}