{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { BugIndicatingError } from '../../../../base/common/errors.js';\nimport { DisposableStore } from '../../../../base/common/lifecycle.js';\nimport { autorunOpts } from '../../../../base/common/observable.js';\nimport { Position } from '../../../common/core/position.js';\nimport { Range } from '../../../common/core/range.js';\nconst array = [];\nexport function getReadonlyEmptyArray() {\n  return array;\n}\nexport class ColumnRange {\n  constructor(startColumn, endColumnExclusive) {\n    this.startColumn = startColumn;\n    this.endColumnExclusive = endColumnExclusive;\n    if (startColumn > endColumnExclusive) {\n      throw new BugIndicatingError(`startColumn ${startColumn} cannot be after endColumnExclusive ${endColumnExclusive}`);\n    }\n  }\n  toRange(lineNumber) {\n    return new Range(lineNumber, this.startColumn, lineNumber, this.endColumnExclusive);\n  }\n  equals(other) {\n    return this.startColumn === other.startColumn && this.endColumnExclusive === other.endColumnExclusive;\n  }\n}\nexport function applyObservableDecorations(editor, decorations) {\n  const d = new DisposableStore();\n  const decorationsCollection = editor.createDecorationsCollection();\n  d.add(autorunOpts({\n    debugName: () => `Apply decorations from ${decorations.debugName}`\n  }, reader => {\n    const d = decorations.read(reader);\n    decorationsCollection.set(d);\n  }));\n  d.add({\n    dispose: () => {\n      decorationsCollection.clear();\n    }\n  });\n  return d;\n}\nexport function addPositions(pos1, pos2) {\n  return new Position(pos1.lineNumber + pos2.lineNumber - 1, pos2.lineNumber === 1 ? pos1.column + pos2.column - 1 : pos2.column);\n}\nexport function subtractPositions(pos1, pos2) {\n  return new Position(pos1.lineNumber - pos2.lineNumber + 1, pos1.lineNumber - pos2.lineNumber === 0 ? pos1.column - pos2.column + 1 : pos1.column);\n}", "map": {"version": 3, "names": ["BugIndicatingError", "DisposableStore", "autorunOpts", "Position", "Range", "array", "getReadonlyEmptyArray", "ColumnRange", "constructor", "startColumn", "endColumnExclusive", "to<PERSON><PERSON><PERSON>", "lineNumber", "equals", "other", "applyObservableDecorations", "editor", "decorations", "d", "decorationsCollection", "createDecorationsCollection", "add", "debugName", "reader", "read", "set", "dispose", "clear", "addPositions", "pos1", "pos2", "column", "subtractPositions"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/contrib/inlineCompletions/browser/utils.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { BugIndicatingError } from '../../../../base/common/errors.js';\nimport { DisposableStore } from '../../../../base/common/lifecycle.js';\nimport { autorunOpts } from '../../../../base/common/observable.js';\nimport { Position } from '../../../common/core/position.js';\nimport { Range } from '../../../common/core/range.js';\nconst array = [];\nexport function getReadonlyEmptyArray() {\n    return array;\n}\nexport class ColumnRange {\n    constructor(startColumn, endColumnExclusive) {\n        this.startColumn = startColumn;\n        this.endColumnExclusive = endColumnExclusive;\n        if (startColumn > endColumnExclusive) {\n            throw new BugIndicatingError(`startColumn ${startColumn} cannot be after endColumnExclusive ${endColumnExclusive}`);\n        }\n    }\n    toRange(lineNumber) {\n        return new Range(lineNumber, this.startColumn, lineNumber, this.endColumnExclusive);\n    }\n    equals(other) {\n        return this.startColumn === other.startColumn\n            && this.endColumnExclusive === other.endColumnExclusive;\n    }\n}\nexport function applyObservableDecorations(editor, decorations) {\n    const d = new DisposableStore();\n    const decorationsCollection = editor.createDecorationsCollection();\n    d.add(autorunOpts({ debugName: () => `Apply decorations from ${decorations.debugName}` }, reader => {\n        const d = decorations.read(reader);\n        decorationsCollection.set(d);\n    }));\n    d.add({\n        dispose: () => {\n            decorationsCollection.clear();\n        }\n    });\n    return d;\n}\nexport function addPositions(pos1, pos2) {\n    return new Position(pos1.lineNumber + pos2.lineNumber - 1, pos2.lineNumber === 1 ? pos1.column + pos2.column - 1 : pos2.column);\n}\nexport function subtractPositions(pos1, pos2) {\n    return new Position(pos1.lineNumber - pos2.lineNumber + 1, pos1.lineNumber - pos2.lineNumber === 0 ? pos1.column - pos2.column + 1 : pos1.column);\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,eAAe,QAAQ,sCAAsC;AACtE,SAASC,WAAW,QAAQ,uCAAuC;AACnE,SAASC,QAAQ,QAAQ,kCAAkC;AAC3D,SAASC,KAAK,QAAQ,+BAA+B;AACrD,MAAMC,KAAK,GAAG,EAAE;AAChB,OAAO,SAASC,qBAAqBA,CAAA,EAAG;EACpC,OAAOD,KAAK;AAChB;AACA,OAAO,MAAME,WAAW,CAAC;EACrBC,WAAWA,CAACC,WAAW,EAAEC,kBAAkB,EAAE;IACzC,IAAI,CAACD,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAID,WAAW,GAAGC,kBAAkB,EAAE;MAClC,MAAM,IAAIV,kBAAkB,CAAC,eAAeS,WAAW,uCAAuCC,kBAAkB,EAAE,CAAC;IACvH;EACJ;EACAC,OAAOA,CAACC,UAAU,EAAE;IAChB,OAAO,IAAIR,KAAK,CAACQ,UAAU,EAAE,IAAI,CAACH,WAAW,EAAEG,UAAU,EAAE,IAAI,CAACF,kBAAkB,CAAC;EACvF;EACAG,MAAMA,CAACC,KAAK,EAAE;IACV,OAAO,IAAI,CAACL,WAAW,KAAKK,KAAK,CAACL,WAAW,IACtC,IAAI,CAACC,kBAAkB,KAAKI,KAAK,CAACJ,kBAAkB;EAC/D;AACJ;AACA,OAAO,SAASK,0BAA0BA,CAACC,MAAM,EAAEC,WAAW,EAAE;EAC5D,MAAMC,CAAC,GAAG,IAAIjB,eAAe,CAAC,CAAC;EAC/B,MAAMkB,qBAAqB,GAAGH,MAAM,CAACI,2BAA2B,CAAC,CAAC;EAClEF,CAAC,CAACG,GAAG,CAACnB,WAAW,CAAC;IAAEoB,SAAS,EAAEA,CAAA,KAAM,0BAA0BL,WAAW,CAACK,SAAS;EAAG,CAAC,EAAEC,MAAM,IAAI;IAChG,MAAML,CAAC,GAAGD,WAAW,CAACO,IAAI,CAACD,MAAM,CAAC;IAClCJ,qBAAqB,CAACM,GAAG,CAACP,CAAC,CAAC;EAChC,CAAC,CAAC,CAAC;EACHA,CAAC,CAACG,GAAG,CAAC;IACFK,OAAO,EAAEA,CAAA,KAAM;MACXP,qBAAqB,CAACQ,KAAK,CAAC,CAAC;IACjC;EACJ,CAAC,CAAC;EACF,OAAOT,CAAC;AACZ;AACA,OAAO,SAASU,YAAYA,CAACC,IAAI,EAAEC,IAAI,EAAE;EACrC,OAAO,IAAI3B,QAAQ,CAAC0B,IAAI,CAACjB,UAAU,GAAGkB,IAAI,CAAClB,UAAU,GAAG,CAAC,EAAEkB,IAAI,CAAClB,UAAU,KAAK,CAAC,GAAGiB,IAAI,CAACE,MAAM,GAAGD,IAAI,CAACC,MAAM,GAAG,CAAC,GAAGD,IAAI,CAACC,MAAM,CAAC;AACnI;AACA,OAAO,SAASC,iBAAiBA,CAACH,IAAI,EAAEC,IAAI,EAAE;EAC1C,OAAO,IAAI3B,QAAQ,CAAC0B,IAAI,CAACjB,UAAU,GAAGkB,IAAI,CAAClB,UAAU,GAAG,CAAC,EAAEiB,IAAI,CAACjB,UAAU,GAAGkB,IAAI,CAAClB,UAAU,KAAK,CAAC,GAAGiB,IAAI,CAACE,MAAM,GAAGD,IAAI,CAACC,MAAM,GAAG,CAAC,GAAGF,IAAI,CAACE,MAAM,CAAC;AACrJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}