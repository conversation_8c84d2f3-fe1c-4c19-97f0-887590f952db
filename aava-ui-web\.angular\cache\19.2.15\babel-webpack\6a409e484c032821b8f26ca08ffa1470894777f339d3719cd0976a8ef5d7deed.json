{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Lazy } from './lazy.js';\nconst hasBuffer = typeof Buffer !== 'undefined';\nconst indexOfTable = new Lazy(() => new Uint8Array(256));\nlet textDecoder;\nexport class VSBuffer {\n  /**\n   * When running in a nodejs context, if `actual` is not a nodejs Buffer, the backing store for\n   * the returned `VSBuffer` instance might use a nodejs Buffer allocated from node's Buffer pool,\n   * which is not transferrable.\n   */\n  static wrap(actual) {\n    if (hasBuffer && !Buffer.isBuffer(actual)) {\n      // https://nodejs.org/dist/latest-v10.x/docs/api/buffer.html#buffer_class_method_buffer_from_arraybuffer_byteoffset_length\n      // Create a zero-copy Buffer wrapper around the ArrayBuffer pointed to by the Uint8Array\n      actual = Buffer.from(actual.buffer, actual.byteOffset, actual.byteLength);\n    }\n    return new VSBuffer(actual);\n  }\n  constructor(buffer) {\n    this.buffer = buffer;\n    this.byteLength = this.buffer.byteLength;\n  }\n  toString() {\n    if (hasBuffer) {\n      return this.buffer.toString();\n    } else {\n      if (!textDecoder) {\n        textDecoder = new TextDecoder();\n      }\n      return textDecoder.decode(this.buffer);\n    }\n  }\n}\nexport function readUInt16LE(source, offset) {\n  return source[offset + 0] << 0 >>> 0 | source[offset + 1] << 8 >>> 0;\n}\nexport function writeUInt16LE(destination, value, offset) {\n  destination[offset + 0] = value & 0b11111111;\n  value = value >>> 8;\n  destination[offset + 1] = value & 0b11111111;\n}\nexport function readUInt32BE(source, offset) {\n  return source[offset] * 2 ** 24 + source[offset + 1] * 2 ** 16 + source[offset + 2] * 2 ** 8 + source[offset + 3];\n}\nexport function writeUInt32BE(destination, value, offset) {\n  destination[offset + 3] = value;\n  value = value >>> 8;\n  destination[offset + 2] = value;\n  value = value >>> 8;\n  destination[offset + 1] = value;\n  value = value >>> 8;\n  destination[offset] = value;\n}\nexport function readUInt8(source, offset) {\n  return source[offset];\n}\nexport function writeUInt8(destination, value, offset) {\n  destination[offset] = value;\n}", "map": {"version": 3, "names": ["Lazy", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "indexOfTable", "Uint8Array", "textDecoder", "VSBuffer", "wrap", "actual", "<PERSON><PERSON><PERSON><PERSON>", "from", "buffer", "byteOffset", "byteLength", "constructor", "toString", "TextDecoder", "decode", "readUInt16LE", "source", "offset", "writeUInt16LE", "destination", "value", "readUInt32BE", "writeUInt32BE", "readUInt8", "writeUInt8"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/common/buffer.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Lazy } from './lazy.js';\nconst hasBuffer = (typeof Buffer !== 'undefined');\nconst indexOfTable = new Lazy(() => new Uint8Array(256));\nlet textDecoder;\nexport class VSBuffer {\n    /**\n     * When running in a nodejs context, if `actual` is not a nodejs Buffer, the backing store for\n     * the returned `VSBuffer` instance might use a nodejs Buffer allocated from node's Buffer pool,\n     * which is not transferrable.\n     */\n    static wrap(actual) {\n        if (hasBuffer && !(Buffer.isBuffer(actual))) {\n            // https://nodejs.org/dist/latest-v10.x/docs/api/buffer.html#buffer_class_method_buffer_from_arraybuffer_byteoffset_length\n            // Create a zero-copy Buffer wrapper around the ArrayBuffer pointed to by the Uint8Array\n            actual = Buffer.from(actual.buffer, actual.byteOffset, actual.byteLength);\n        }\n        return new VSBuffer(actual);\n    }\n    constructor(buffer) {\n        this.buffer = buffer;\n        this.byteLength = this.buffer.byteLength;\n    }\n    toString() {\n        if (hasBuffer) {\n            return this.buffer.toString();\n        }\n        else {\n            if (!textDecoder) {\n                textDecoder = new TextDecoder();\n            }\n            return textDecoder.decode(this.buffer);\n        }\n    }\n}\nexport function readUInt16LE(source, offset) {\n    return (((source[offset + 0] << 0) >>> 0) |\n        ((source[offset + 1] << 8) >>> 0));\n}\nexport function writeUInt16LE(destination, value, offset) {\n    destination[offset + 0] = (value & 0b11111111);\n    value = value >>> 8;\n    destination[offset + 1] = (value & 0b11111111);\n}\nexport function readUInt32BE(source, offset) {\n    return (source[offset] * 2 ** 24\n        + source[offset + 1] * 2 ** 16\n        + source[offset + 2] * 2 ** 8\n        + source[offset + 3]);\n}\nexport function writeUInt32BE(destination, value, offset) {\n    destination[offset + 3] = value;\n    value = value >>> 8;\n    destination[offset + 2] = value;\n    value = value >>> 8;\n    destination[offset + 1] = value;\n    value = value >>> 8;\n    destination[offset] = value;\n}\nexport function readUInt8(source, offset) {\n    return source[offset];\n}\nexport function writeUInt8(destination, value, offset) {\n    destination[offset] = value;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,IAAI,QAAQ,WAAW;AAChC,MAAMC,SAAS,GAAI,OAAOC,MAAM,KAAK,WAAY;AACjD,MAAMC,YAAY,GAAG,IAAIH,IAAI,CAAC,MAAM,IAAII,UAAU,CAAC,GAAG,CAAC,CAAC;AACxD,IAAIC,WAAW;AACf,OAAO,MAAMC,QAAQ,CAAC;EAClB;AACJ;AACA;AACA;AACA;EACI,OAAOC,IAAIA,CAACC,MAAM,EAAE;IAChB,IAAIP,SAAS,IAAI,CAAEC,MAAM,CAACO,QAAQ,CAACD,MAAM,CAAE,EAAE;MACzC;MACA;MACAA,MAAM,GAAGN,MAAM,CAACQ,IAAI,CAACF,MAAM,CAACG,MAAM,EAAEH,MAAM,CAACI,UAAU,EAAEJ,MAAM,CAACK,UAAU,CAAC;IAC7E;IACA,OAAO,IAAIP,QAAQ,CAACE,MAAM,CAAC;EAC/B;EACAM,WAAWA,CAACH,MAAM,EAAE;IAChB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACE,UAAU,GAAG,IAAI,CAACF,MAAM,CAACE,UAAU;EAC5C;EACAE,QAAQA,CAAA,EAAG;IACP,IAAId,SAAS,EAAE;MACX,OAAO,IAAI,CAACU,MAAM,CAACI,QAAQ,CAAC,CAAC;IACjC,CAAC,MACI;MACD,IAAI,CAACV,WAAW,EAAE;QACdA,WAAW,GAAG,IAAIW,WAAW,CAAC,CAAC;MACnC;MACA,OAAOX,WAAW,CAACY,MAAM,CAAC,IAAI,CAACN,MAAM,CAAC;IAC1C;EACJ;AACJ;AACA,OAAO,SAASO,YAAYA,CAACC,MAAM,EAAEC,MAAM,EAAE;EACzC,OAAUD,MAAM,CAACC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,KAAM,CAAC,GAClCD,MAAM,CAACC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,KAAM,CAAE;AACzC;AACA,OAAO,SAASC,aAAaA,CAACC,WAAW,EAAEC,KAAK,EAAEH,MAAM,EAAE;EACtDE,WAAW,CAACF,MAAM,GAAG,CAAC,CAAC,GAAIG,KAAK,GAAG,UAAW;EAC9CA,KAAK,GAAGA,KAAK,KAAK,CAAC;EACnBD,WAAW,CAACF,MAAM,GAAG,CAAC,CAAC,GAAIG,KAAK,GAAG,UAAW;AAClD;AACA,OAAO,SAASC,YAAYA,CAACL,MAAM,EAAEC,MAAM,EAAE;EACzC,OAAQD,MAAM,CAACC,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,GAC1BD,MAAM,CAACC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,GAC5BD,MAAM,CAACC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAC3BD,MAAM,CAACC,MAAM,GAAG,CAAC,CAAC;AAC5B;AACA,OAAO,SAASK,aAAaA,CAACH,WAAW,EAAEC,KAAK,EAAEH,MAAM,EAAE;EACtDE,WAAW,CAACF,MAAM,GAAG,CAAC,CAAC,GAAGG,KAAK;EAC/BA,KAAK,GAAGA,KAAK,KAAK,CAAC;EACnBD,WAAW,CAACF,MAAM,GAAG,CAAC,CAAC,GAAGG,KAAK;EAC/BA,KAAK,GAAGA,KAAK,KAAK,CAAC;EACnBD,WAAW,CAACF,MAAM,GAAG,CAAC,CAAC,GAAGG,KAAK;EAC/BA,KAAK,GAAGA,KAAK,KAAK,CAAC;EACnBD,WAAW,CAACF,MAAM,CAAC,GAAGG,KAAK;AAC/B;AACA,OAAO,SAASG,SAASA,CAACP,MAAM,EAAEC,MAAM,EAAE;EACtC,OAAOD,MAAM,CAACC,MAAM,CAAC;AACzB;AACA,OAAO,SAASO,UAAUA,CAACL,WAAW,EAAEC,KAAK,EAAEH,MAAM,EAAE;EACnDE,WAAW,CAACF,MAAM,CAAC,GAAGG,KAAK;AAC/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}