{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport class IdGenerator {\n  constructor(prefix) {\n    this._prefix = prefix;\n    this._lastId = 0;\n  }\n  nextId() {\n    return this._prefix + ++this._lastId;\n  }\n}\nexport const defaultGenerator = new IdGenerator('id#');", "map": {"version": 3, "names": ["IdGenerator", "constructor", "prefix", "_prefix", "_lastId", "nextId", "defaultGenerator"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/common/idGenerator.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport class IdGenerator {\n    constructor(prefix) {\n        this._prefix = prefix;\n        this._lastId = 0;\n    }\n    nextId() {\n        return this._prefix + (++this._lastId);\n    }\n}\nexport const defaultGenerator = new IdGenerator('id#');\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,MAAMA,WAAW,CAAC;EACrBC,WAAWA,CAACC,MAAM,EAAE;IAChB,IAAI,CAACC,OAAO,GAAGD,MAAM;IACrB,IAAI,CAACE,OAAO,GAAG,CAAC;EACpB;EACAC,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI,CAACF,OAAO,GAAI,EAAE,IAAI,CAACC,OAAQ;EAC1C;AACJ;AACA,OAAO,MAAME,gBAAgB,GAAG,IAAIN,WAAW,CAAC,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}