{"ast": null, "code": "import { default as geoStream } from \"../stream.js\";\nimport boundsStream from \"../path/bounds.js\";\nfunction fit(projection, fitBounds, object) {\n  var clip = projection.clipExtent && projection.clipExtent();\n  projection.scale(150).translate([0, 0]);\n  if (clip != null) projection.clipExtent(null);\n  geoStream(object, projection.stream(boundsStream));\n  fitBounds(boundsStream.result());\n  if (clip != null) projection.clipExtent(clip);\n  return projection;\n}\nexport function fitExtent(projection, extent, object) {\n  return fit(projection, function (b) {\n    var w = extent[1][0] - extent[0][0],\n      h = extent[1][1] - extent[0][1],\n      k = Math.min(w / (b[1][0] - b[0][0]), h / (b[1][1] - b[0][1])),\n      x = +extent[0][0] + (w - k * (b[1][0] + b[0][0])) / 2,\n      y = +extent[0][1] + (h - k * (b[1][1] + b[0][1])) / 2;\n    projection.scale(150 * k).translate([x, y]);\n  }, object);\n}\nexport function fitSize(projection, size, object) {\n  return fitExtent(projection, [[0, 0], size], object);\n}\nexport function fitWidth(projection, width, object) {\n  return fit(projection, function (b) {\n    var w = +width,\n      k = w / (b[1][0] - b[0][0]),\n      x = (w - k * (b[1][0] + b[0][0])) / 2,\n      y = -k * b[0][1];\n    projection.scale(150 * k).translate([x, y]);\n  }, object);\n}\nexport function fitHeight(projection, height, object) {\n  return fit(projection, function (b) {\n    var h = +height,\n      k = h / (b[1][1] - b[0][1]),\n      x = -k * b[0][0],\n      y = (h - k * (b[1][1] + b[0][1])) / 2;\n    projection.scale(150 * k).translate([x, y]);\n  }, object);\n}", "map": {"version": 3, "names": ["default", "geoStream", "boundsStream", "fit", "projection", "fitBounds", "object", "clip", "clipExtent", "scale", "translate", "stream", "result", "fitExtent", "extent", "b", "w", "h", "k", "Math", "min", "x", "y", "fitSize", "size", "fit<PERSON><PERSON><PERSON>", "width", "fitHeight", "height"], "sources": ["C:/console/aava-ui-web/node_modules/d3-geo/src/projection/fit.js"], "sourcesContent": ["import {default as geoStream} from \"../stream.js\";\nimport boundsStream from \"../path/bounds.js\";\n\nfunction fit(projection, fitBounds, object) {\n  var clip = projection.clipExtent && projection.clipExtent();\n  projection.scale(150).translate([0, 0]);\n  if (clip != null) projection.clipExtent(null);\n  geoStream(object, projection.stream(boundsStream));\n  fitBounds(boundsStream.result());\n  if (clip != null) projection.clipExtent(clip);\n  return projection;\n}\n\nexport function fitExtent(projection, extent, object) {\n  return fit(projection, function(b) {\n    var w = extent[1][0] - extent[0][0],\n        h = extent[1][1] - extent[0][1],\n        k = Math.min(w / (b[1][0] - b[0][0]), h / (b[1][1] - b[0][1])),\n        x = +extent[0][0] + (w - k * (b[1][0] + b[0][0])) / 2,\n        y = +extent[0][1] + (h - k * (b[1][1] + b[0][1])) / 2;\n    projection.scale(150 * k).translate([x, y]);\n  }, object);\n}\n\nexport function fitSize(projection, size, object) {\n  return fitExtent(projection, [[0, 0], size], object);\n}\n\nexport function fitWidth(projection, width, object) {\n  return fit(projection, function(b) {\n    var w = +width,\n        k = w / (b[1][0] - b[0][0]),\n        x = (w - k * (b[1][0] + b[0][0])) / 2,\n        y = -k * b[0][1];\n    projection.scale(150 * k).translate([x, y]);\n  }, object);\n}\n\nexport function fitHeight(projection, height, object) {\n  return fit(projection, function(b) {\n    var h = +height,\n        k = h / (b[1][1] - b[0][1]),\n        x = -k * b[0][0],\n        y = (h - k * (b[1][1] + b[0][1])) / 2;\n    projection.scale(150 * k).translate([x, y]);\n  }, object);\n}\n"], "mappings": "AAAA,SAAQA,OAAO,IAAIC,SAAS,QAAO,cAAc;AACjD,OAAOC,YAAY,MAAM,mBAAmB;AAE5C,SAASC,GAAGA,CAACC,UAAU,EAAEC,SAAS,EAAEC,MAAM,EAAE;EAC1C,IAAIC,IAAI,GAAGH,UAAU,CAACI,UAAU,IAAIJ,UAAU,CAACI,UAAU,CAAC,CAAC;EAC3DJ,UAAU,CAACK,KAAK,CAAC,GAAG,CAAC,CAACC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACvC,IAAIH,IAAI,IAAI,IAAI,EAAEH,UAAU,CAACI,UAAU,CAAC,IAAI,CAAC;EAC7CP,SAAS,CAACK,MAAM,EAAEF,UAAU,CAACO,MAAM,CAACT,YAAY,CAAC,CAAC;EAClDG,SAAS,CAACH,YAAY,CAACU,MAAM,CAAC,CAAC,CAAC;EAChC,IAAIL,IAAI,IAAI,IAAI,EAAEH,UAAU,CAACI,UAAU,CAACD,IAAI,CAAC;EAC7C,OAAOH,UAAU;AACnB;AAEA,OAAO,SAASS,SAASA,CAACT,UAAU,EAAEU,MAAM,EAAER,MAAM,EAAE;EACpD,OAAOH,GAAG,CAACC,UAAU,EAAE,UAASW,CAAC,EAAE;IACjC,IAAIC,CAAC,GAAGF,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/BG,CAAC,GAAGH,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/BI,CAAC,GAAGC,IAAI,CAACC,GAAG,CAACJ,CAAC,IAAID,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEE,CAAC,IAAIF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DM,CAAC,GAAG,CAACP,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAACE,CAAC,GAAGE,CAAC,IAAIH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;MACrDO,CAAC,GAAG,CAACR,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAACG,CAAC,GAAGC,CAAC,IAAIH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACzDX,UAAU,CAACK,KAAK,CAAC,GAAG,GAAGS,CAAC,CAAC,CAACR,SAAS,CAAC,CAACW,CAAC,EAAEC,CAAC,CAAC,CAAC;EAC7C,CAAC,EAAEhB,MAAM,CAAC;AACZ;AAEA,OAAO,SAASiB,OAAOA,CAACnB,UAAU,EAAEoB,IAAI,EAAElB,MAAM,EAAE;EAChD,OAAOO,SAAS,CAACT,UAAU,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAEoB,IAAI,CAAC,EAAElB,MAAM,CAAC;AACtD;AAEA,OAAO,SAASmB,QAAQA,CAACrB,UAAU,EAAEsB,KAAK,EAAEpB,MAAM,EAAE;EAClD,OAAOH,GAAG,CAACC,UAAU,EAAE,UAASW,CAAC,EAAE;IACjC,IAAIC,CAAC,GAAG,CAACU,KAAK;MACVR,CAAC,GAAGF,CAAC,IAAID,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3BM,CAAC,GAAG,CAACL,CAAC,GAAGE,CAAC,IAAIH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;MACrCO,CAAC,GAAG,CAACJ,CAAC,GAAGH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpBX,UAAU,CAACK,KAAK,CAAC,GAAG,GAAGS,CAAC,CAAC,CAACR,SAAS,CAAC,CAACW,CAAC,EAAEC,CAAC,CAAC,CAAC;EAC7C,CAAC,EAAEhB,MAAM,CAAC;AACZ;AAEA,OAAO,SAASqB,SAASA,CAACvB,UAAU,EAAEwB,MAAM,EAAEtB,MAAM,EAAE;EACpD,OAAOH,GAAG,CAACC,UAAU,EAAE,UAASW,CAAC,EAAE;IACjC,IAAIE,CAAC,GAAG,CAACW,MAAM;MACXV,CAAC,GAAGD,CAAC,IAAIF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3BM,CAAC,GAAG,CAACH,CAAC,GAAGH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChBO,CAAC,GAAG,CAACL,CAAC,GAAGC,CAAC,IAAIH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACzCX,UAAU,CAACK,KAAK,CAAC,GAAG,GAAGS,CAAC,CAAC,CAACR,SAAS,CAAC,CAACW,CAAC,EAAEC,CAAC,CAAC,CAAC;EAC7C,CAAC,EAAEhB,MAAM,CAAC;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}