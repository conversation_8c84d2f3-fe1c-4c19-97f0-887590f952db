{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Range } from '../../../common/range.js';\n/**\n * Returns the intersection between a ranged group and a range.\n * Returns `[]` if the intersection is empty.\n */\nexport function groupIntersect(range, groups) {\n  const result = [];\n  for (const r of groups) {\n    if (range.start >= r.range.end) {\n      continue;\n    }\n    if (range.end < r.range.start) {\n      break;\n    }\n    const intersection = Range.intersect(range, r.range);\n    if (Range.isEmpty(intersection)) {\n      continue;\n    }\n    result.push({\n      range: intersection,\n      size: r.size\n    });\n  }\n  return result;\n}\n/**\n * Shifts a range by that `much`.\n */\nexport function shift({\n  start,\n  end\n}, much) {\n  return {\n    start: start + much,\n    end: end + much\n  };\n}\n/**\n * Consolidates a collection of ranged groups.\n *\n * Consolidation is the process of merging consecutive ranged groups\n * that share the same `size`.\n */\nexport function consolidate(groups) {\n  const result = [];\n  let previousGroup = null;\n  for (const group of groups) {\n    const start = group.range.start;\n    const end = group.range.end;\n    const size = group.size;\n    if (previousGroup && size === previousGroup.size) {\n      previousGroup.range.end = end;\n      continue;\n    }\n    previousGroup = {\n      range: {\n        start,\n        end\n      },\n      size\n    };\n    result.push(previousGroup);\n  }\n  return result;\n}\n/**\n * Concatenates several collections of ranged groups into a single\n * collection.\n */\nfunction concat(...groups) {\n  return consolidate(groups.reduce((r, g) => r.concat(g), []));\n}\nexport class RangeMap {\n  get paddingTop() {\n    return this._paddingTop;\n  }\n  set paddingTop(paddingTop) {\n    this._size = this._size + paddingTop - this._paddingTop;\n    this._paddingTop = paddingTop;\n  }\n  constructor(topPadding) {\n    this.groups = [];\n    this._size = 0;\n    this._paddingTop = 0;\n    this._paddingTop = topPadding ?? 0;\n    this._size = this._paddingTop;\n  }\n  splice(index, deleteCount, items = []) {\n    const diff = items.length - deleteCount;\n    const before = groupIntersect({\n      start: 0,\n      end: index\n    }, this.groups);\n    const after = groupIntersect({\n      start: index + deleteCount,\n      end: Number.POSITIVE_INFINITY\n    }, this.groups).map(g => ({\n      range: shift(g.range, diff),\n      size: g.size\n    }));\n    const middle = items.map((item, i) => ({\n      range: {\n        start: index + i,\n        end: index + i + 1\n      },\n      size: item.size\n    }));\n    this.groups = concat(before, middle, after);\n    this._size = this._paddingTop + this.groups.reduce((t, g) => t + g.size * (g.range.end - g.range.start), 0);\n  }\n  /**\n   * Returns the number of items in the range map.\n   */\n  get count() {\n    const len = this.groups.length;\n    if (!len) {\n      return 0;\n    }\n    return this.groups[len - 1].range.end;\n  }\n  /**\n   * Returns the sum of the sizes of all items in the range map.\n   */\n  get size() {\n    return this._size;\n  }\n  /**\n   * Returns the index of the item at the given position.\n   */\n  indexAt(position) {\n    if (position < 0) {\n      return -1;\n    }\n    if (position < this._paddingTop) {\n      return 0;\n    }\n    let index = 0;\n    let size = this._paddingTop;\n    for (const group of this.groups) {\n      const count = group.range.end - group.range.start;\n      const newSize = size + count * group.size;\n      if (position < newSize) {\n        return index + Math.floor((position - size) / group.size);\n      }\n      index += count;\n      size = newSize;\n    }\n    return index;\n  }\n  /**\n   * Returns the index of the item right after the item at the\n   * index of the given position.\n   */\n  indexAfter(position) {\n    return Math.min(this.indexAt(position) + 1, this.count);\n  }\n  /**\n   * Returns the start position of the item at the given index.\n   */\n  positionAt(index) {\n    if (index < 0) {\n      return -1;\n    }\n    let position = 0;\n    let count = 0;\n    for (const group of this.groups) {\n      const groupCount = group.range.end - group.range.start;\n      const newCount = count + groupCount;\n      if (index < newCount) {\n        return this._paddingTop + position + (index - count) * group.size;\n      }\n      position += groupCount * group.size;\n      count = newCount;\n    }\n    return -1;\n  }\n}", "map": {"version": 3, "names": ["Range", "groupIntersect", "range", "groups", "result", "r", "start", "end", "intersection", "intersect", "isEmpty", "push", "size", "shift", "much", "consolidate", "previousGroup", "group", "concat", "reduce", "g", "RangeMap", "paddingTop", "_paddingTop", "_size", "constructor", "topPadding", "splice", "index", "deleteCount", "items", "diff", "length", "before", "after", "Number", "POSITIVE_INFINITY", "map", "middle", "item", "i", "t", "count", "len", "indexAt", "position", "newSize", "Math", "floor", "indexAfter", "min", "positionAt", "groupCount", "newCount"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/browser/ui/list/rangeMap.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Range } from '../../../common/range.js';\n/**\n * Returns the intersection between a ranged group and a range.\n * Returns `[]` if the intersection is empty.\n */\nexport function groupIntersect(range, groups) {\n    const result = [];\n    for (const r of groups) {\n        if (range.start >= r.range.end) {\n            continue;\n        }\n        if (range.end < r.range.start) {\n            break;\n        }\n        const intersection = Range.intersect(range, r.range);\n        if (Range.isEmpty(intersection)) {\n            continue;\n        }\n        result.push({\n            range: intersection,\n            size: r.size\n        });\n    }\n    return result;\n}\n/**\n * Shifts a range by that `much`.\n */\nexport function shift({ start, end }, much) {\n    return { start: start + much, end: end + much };\n}\n/**\n * Consolidates a collection of ranged groups.\n *\n * Consolidation is the process of merging consecutive ranged groups\n * that share the same `size`.\n */\nexport function consolidate(groups) {\n    const result = [];\n    let previousGroup = null;\n    for (const group of groups) {\n        const start = group.range.start;\n        const end = group.range.end;\n        const size = group.size;\n        if (previousGroup && size === previousGroup.size) {\n            previousGroup.range.end = end;\n            continue;\n        }\n        previousGroup = { range: { start, end }, size };\n        result.push(previousGroup);\n    }\n    return result;\n}\n/**\n * Concatenates several collections of ranged groups into a single\n * collection.\n */\nfunction concat(...groups) {\n    return consolidate(groups.reduce((r, g) => r.concat(g), []));\n}\nexport class RangeMap {\n    get paddingTop() {\n        return this._paddingTop;\n    }\n    set paddingTop(paddingTop) {\n        this._size = this._size + paddingTop - this._paddingTop;\n        this._paddingTop = paddingTop;\n    }\n    constructor(topPadding) {\n        this.groups = [];\n        this._size = 0;\n        this._paddingTop = 0;\n        this._paddingTop = topPadding ?? 0;\n        this._size = this._paddingTop;\n    }\n    splice(index, deleteCount, items = []) {\n        const diff = items.length - deleteCount;\n        const before = groupIntersect({ start: 0, end: index }, this.groups);\n        const after = groupIntersect({ start: index + deleteCount, end: Number.POSITIVE_INFINITY }, this.groups)\n            .map(g => ({ range: shift(g.range, diff), size: g.size }));\n        const middle = items.map((item, i) => ({\n            range: { start: index + i, end: index + i + 1 },\n            size: item.size\n        }));\n        this.groups = concat(before, middle, after);\n        this._size = this._paddingTop + this.groups.reduce((t, g) => t + (g.size * (g.range.end - g.range.start)), 0);\n    }\n    /**\n     * Returns the number of items in the range map.\n     */\n    get count() {\n        const len = this.groups.length;\n        if (!len) {\n            return 0;\n        }\n        return this.groups[len - 1].range.end;\n    }\n    /**\n     * Returns the sum of the sizes of all items in the range map.\n     */\n    get size() {\n        return this._size;\n    }\n    /**\n     * Returns the index of the item at the given position.\n     */\n    indexAt(position) {\n        if (position < 0) {\n            return -1;\n        }\n        if (position < this._paddingTop) {\n            return 0;\n        }\n        let index = 0;\n        let size = this._paddingTop;\n        for (const group of this.groups) {\n            const count = group.range.end - group.range.start;\n            const newSize = size + (count * group.size);\n            if (position < newSize) {\n                return index + Math.floor((position - size) / group.size);\n            }\n            index += count;\n            size = newSize;\n        }\n        return index;\n    }\n    /**\n     * Returns the index of the item right after the item at the\n     * index of the given position.\n     */\n    indexAfter(position) {\n        return Math.min(this.indexAt(position) + 1, this.count);\n    }\n    /**\n     * Returns the start position of the item at the given index.\n     */\n    positionAt(index) {\n        if (index < 0) {\n            return -1;\n        }\n        let position = 0;\n        let count = 0;\n        for (const group of this.groups) {\n            const groupCount = group.range.end - group.range.start;\n            const newCount = count + groupCount;\n            if (index < newCount) {\n                return this._paddingTop + position + ((index - count) * group.size);\n            }\n            position += groupCount * group.size;\n            count = newCount;\n        }\n        return -1;\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,KAAK,QAAQ,0BAA0B;AAChD;AACA;AACA;AACA;AACA,OAAO,SAASC,cAAcA,CAACC,KAAK,EAAEC,MAAM,EAAE;EAC1C,MAAMC,MAAM,GAAG,EAAE;EACjB,KAAK,MAAMC,CAAC,IAAIF,MAAM,EAAE;IACpB,IAAID,KAAK,CAACI,KAAK,IAAID,CAAC,CAACH,KAAK,CAACK,GAAG,EAAE;MAC5B;IACJ;IACA,IAAIL,KAAK,CAACK,GAAG,GAAGF,CAAC,CAACH,KAAK,CAACI,KAAK,EAAE;MAC3B;IACJ;IACA,MAAME,YAAY,GAAGR,KAAK,CAACS,SAAS,CAACP,KAAK,EAAEG,CAAC,CAACH,KAAK,CAAC;IACpD,IAAIF,KAAK,CAACU,OAAO,CAACF,YAAY,CAAC,EAAE;MAC7B;IACJ;IACAJ,MAAM,CAACO,IAAI,CAAC;MACRT,KAAK,EAAEM,YAAY;MACnBI,IAAI,EAAEP,CAAC,CAACO;IACZ,CAAC,CAAC;EACN;EACA,OAAOR,MAAM;AACjB;AACA;AACA;AACA;AACA,OAAO,SAASS,KAAKA,CAAC;EAAEP,KAAK;EAAEC;AAAI,CAAC,EAAEO,IAAI,EAAE;EACxC,OAAO;IAAER,KAAK,EAAEA,KAAK,GAAGQ,IAAI;IAAEP,GAAG,EAAEA,GAAG,GAAGO;EAAK,CAAC;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAACZ,MAAM,EAAE;EAChC,MAAMC,MAAM,GAAG,EAAE;EACjB,IAAIY,aAAa,GAAG,IAAI;EACxB,KAAK,MAAMC,KAAK,IAAId,MAAM,EAAE;IACxB,MAAMG,KAAK,GAAGW,KAAK,CAACf,KAAK,CAACI,KAAK;IAC/B,MAAMC,GAAG,GAAGU,KAAK,CAACf,KAAK,CAACK,GAAG;IAC3B,MAAMK,IAAI,GAAGK,KAAK,CAACL,IAAI;IACvB,IAAII,aAAa,IAAIJ,IAAI,KAAKI,aAAa,CAACJ,IAAI,EAAE;MAC9CI,aAAa,CAACd,KAAK,CAACK,GAAG,GAAGA,GAAG;MAC7B;IACJ;IACAS,aAAa,GAAG;MAAEd,KAAK,EAAE;QAAEI,KAAK;QAAEC;MAAI,CAAC;MAAEK;IAAK,CAAC;IAC/CR,MAAM,CAACO,IAAI,CAACK,aAAa,CAAC;EAC9B;EACA,OAAOZ,MAAM;AACjB;AACA;AACA;AACA;AACA;AACA,SAASc,MAAMA,CAAC,GAAGf,MAAM,EAAE;EACvB,OAAOY,WAAW,CAACZ,MAAM,CAACgB,MAAM,CAAC,CAACd,CAAC,EAAEe,CAAC,KAAKf,CAAC,CAACa,MAAM,CAACE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AAChE;AACA,OAAO,MAAMC,QAAQ,CAAC;EAClB,IAAIC,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACC,WAAW;EAC3B;EACA,IAAID,UAAUA,CAACA,UAAU,EAAE;IACvB,IAAI,CAACE,KAAK,GAAG,IAAI,CAACA,KAAK,GAAGF,UAAU,GAAG,IAAI,CAACC,WAAW;IACvD,IAAI,CAACA,WAAW,GAAGD,UAAU;EACjC;EACAG,WAAWA,CAACC,UAAU,EAAE;IACpB,IAAI,CAACvB,MAAM,GAAG,EAAE;IAChB,IAAI,CAACqB,KAAK,GAAG,CAAC;IACd,IAAI,CAACD,WAAW,GAAG,CAAC;IACpB,IAAI,CAACA,WAAW,GAAGG,UAAU,IAAI,CAAC;IAClC,IAAI,CAACF,KAAK,GAAG,IAAI,CAACD,WAAW;EACjC;EACAI,MAAMA,CAACC,KAAK,EAAEC,WAAW,EAAEC,KAAK,GAAG,EAAE,EAAE;IACnC,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,GAAGH,WAAW;IACvC,MAAMI,MAAM,GAAGhC,cAAc,CAAC;MAAEK,KAAK,EAAE,CAAC;MAAEC,GAAG,EAAEqB;IAAM,CAAC,EAAE,IAAI,CAACzB,MAAM,CAAC;IACpE,MAAM+B,KAAK,GAAGjC,cAAc,CAAC;MAAEK,KAAK,EAAEsB,KAAK,GAAGC,WAAW;MAAEtB,GAAG,EAAE4B,MAAM,CAACC;IAAkB,CAAC,EAAE,IAAI,CAACjC,MAAM,CAAC,CACnGkC,GAAG,CAACjB,CAAC,KAAK;MAAElB,KAAK,EAAEW,KAAK,CAACO,CAAC,CAAClB,KAAK,EAAE6B,IAAI,CAAC;MAAEnB,IAAI,EAAEQ,CAAC,CAACR;IAAK,CAAC,CAAC,CAAC;IAC9D,MAAM0B,MAAM,GAAGR,KAAK,CAACO,GAAG,CAAC,CAACE,IAAI,EAAEC,CAAC,MAAM;MACnCtC,KAAK,EAAE;QAAEI,KAAK,EAAEsB,KAAK,GAAGY,CAAC;QAAEjC,GAAG,EAAEqB,KAAK,GAAGY,CAAC,GAAG;MAAE,CAAC;MAC/C5B,IAAI,EAAE2B,IAAI,CAAC3B;IACf,CAAC,CAAC,CAAC;IACH,IAAI,CAACT,MAAM,GAAGe,MAAM,CAACe,MAAM,EAAEK,MAAM,EAAEJ,KAAK,CAAC;IAC3C,IAAI,CAACV,KAAK,GAAG,IAAI,CAACD,WAAW,GAAG,IAAI,CAACpB,MAAM,CAACgB,MAAM,CAAC,CAACsB,CAAC,EAAErB,CAAC,KAAKqB,CAAC,GAAIrB,CAAC,CAACR,IAAI,IAAIQ,CAAC,CAAClB,KAAK,CAACK,GAAG,GAAGa,CAAC,CAAClB,KAAK,CAACI,KAAK,CAAE,EAAE,CAAC,CAAC;EACjH;EACA;AACJ;AACA;EACI,IAAIoC,KAAKA,CAAA,EAAG;IACR,MAAMC,GAAG,GAAG,IAAI,CAACxC,MAAM,CAAC6B,MAAM;IAC9B,IAAI,CAACW,GAAG,EAAE;MACN,OAAO,CAAC;IACZ;IACA,OAAO,IAAI,CAACxC,MAAM,CAACwC,GAAG,GAAG,CAAC,CAAC,CAACzC,KAAK,CAACK,GAAG;EACzC;EACA;AACJ;AACA;EACI,IAAIK,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACY,KAAK;EACrB;EACA;AACJ;AACA;EACIoB,OAAOA,CAACC,QAAQ,EAAE;IACd,IAAIA,QAAQ,GAAG,CAAC,EAAE;MACd,OAAO,CAAC,CAAC;IACb;IACA,IAAIA,QAAQ,GAAG,IAAI,CAACtB,WAAW,EAAE;MAC7B,OAAO,CAAC;IACZ;IACA,IAAIK,KAAK,GAAG,CAAC;IACb,IAAIhB,IAAI,GAAG,IAAI,CAACW,WAAW;IAC3B,KAAK,MAAMN,KAAK,IAAI,IAAI,CAACd,MAAM,EAAE;MAC7B,MAAMuC,KAAK,GAAGzB,KAAK,CAACf,KAAK,CAACK,GAAG,GAAGU,KAAK,CAACf,KAAK,CAACI,KAAK;MACjD,MAAMwC,OAAO,GAAGlC,IAAI,GAAI8B,KAAK,GAAGzB,KAAK,CAACL,IAAK;MAC3C,IAAIiC,QAAQ,GAAGC,OAAO,EAAE;QACpB,OAAOlB,KAAK,GAAGmB,IAAI,CAACC,KAAK,CAAC,CAACH,QAAQ,GAAGjC,IAAI,IAAIK,KAAK,CAACL,IAAI,CAAC;MAC7D;MACAgB,KAAK,IAAIc,KAAK;MACd9B,IAAI,GAAGkC,OAAO;IAClB;IACA,OAAOlB,KAAK;EAChB;EACA;AACJ;AACA;AACA;EACIqB,UAAUA,CAACJ,QAAQ,EAAE;IACjB,OAAOE,IAAI,CAACG,GAAG,CAAC,IAAI,CAACN,OAAO,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAE,IAAI,CAACH,KAAK,CAAC;EAC3D;EACA;AACJ;AACA;EACIS,UAAUA,CAACvB,KAAK,EAAE;IACd,IAAIA,KAAK,GAAG,CAAC,EAAE;MACX,OAAO,CAAC,CAAC;IACb;IACA,IAAIiB,QAAQ,GAAG,CAAC;IAChB,IAAIH,KAAK,GAAG,CAAC;IACb,KAAK,MAAMzB,KAAK,IAAI,IAAI,CAACd,MAAM,EAAE;MAC7B,MAAMiD,UAAU,GAAGnC,KAAK,CAACf,KAAK,CAACK,GAAG,GAAGU,KAAK,CAACf,KAAK,CAACI,KAAK;MACtD,MAAM+C,QAAQ,GAAGX,KAAK,GAAGU,UAAU;MACnC,IAAIxB,KAAK,GAAGyB,QAAQ,EAAE;QAClB,OAAO,IAAI,CAAC9B,WAAW,GAAGsB,QAAQ,GAAI,CAACjB,KAAK,GAAGc,KAAK,IAAIzB,KAAK,CAACL,IAAK;MACvE;MACAiC,QAAQ,IAAIO,UAAU,GAAGnC,KAAK,CAACL,IAAI;MACnC8B,KAAK,GAAGW,QAAQ;IACpB;IACA,OAAO,CAAC,CAAC;EACb;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}