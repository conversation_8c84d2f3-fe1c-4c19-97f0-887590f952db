{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as dom from '../../dom.js';\nimport { createFastDomNode } from '../../fastDomNode.js';\nimport { GlobalPointerMoveMonitor } from '../../globalPointerMoveMonitor.js';\nimport { ScrollbarArrow } from './scrollbarArrow.js';\nimport { ScrollbarVisibilityController } from './scrollbarVisibilityController.js';\nimport { Widget } from '../widget.js';\nimport * as platform from '../../../common/platform.js';\n/**\n * The orthogonal distance to the slider at which dragging \"resets\". This implements \"snapping\"\n */\nconst POINTER_DRAG_RESET_DISTANCE = 140;\nexport class AbstractScrollbar extends Widget {\n  constructor(opts) {\n    super();\n    this._lazyRender = opts.lazyRender;\n    this._host = opts.host;\n    this._scrollable = opts.scrollable;\n    this._scrollByPage = opts.scrollByPage;\n    this._scrollbarState = opts.scrollbarState;\n    this._visibilityController = this._register(new ScrollbarVisibilityController(opts.visibility, 'visible scrollbar ' + opts.extraScrollbarClassName, 'invisible scrollbar ' + opts.extraScrollbarClassName));\n    this._visibilityController.setIsNeeded(this._scrollbarState.isNeeded());\n    this._pointerMoveMonitor = this._register(new GlobalPointerMoveMonitor());\n    this._shouldRender = true;\n    this.domNode = createFastDomNode(document.createElement('div'));\n    this.domNode.setAttribute('role', 'presentation');\n    this.domNode.setAttribute('aria-hidden', 'true');\n    this._visibilityController.setDomNode(this.domNode);\n    this.domNode.setPosition('absolute');\n    this._register(dom.addDisposableListener(this.domNode.domNode, dom.EventType.POINTER_DOWN, e => this._domNodePointerDown(e)));\n  }\n  // ----------------- creation\n  /**\n   * Creates the dom node for an arrow & adds it to the container\n   */\n  _createArrow(opts) {\n    const arrow = this._register(new ScrollbarArrow(opts));\n    this.domNode.domNode.appendChild(arrow.bgDomNode);\n    this.domNode.domNode.appendChild(arrow.domNode);\n  }\n  /**\n   * Creates the slider dom node, adds it to the container & hooks up the events\n   */\n  _createSlider(top, left, width, height) {\n    this.slider = createFastDomNode(document.createElement('div'));\n    this.slider.setClassName('slider');\n    this.slider.setPosition('absolute');\n    this.slider.setTop(top);\n    this.slider.setLeft(left);\n    if (typeof width === 'number') {\n      this.slider.setWidth(width);\n    }\n    if (typeof height === 'number') {\n      this.slider.setHeight(height);\n    }\n    this.slider.setLayerHinting(true);\n    this.slider.setContain('strict');\n    this.domNode.domNode.appendChild(this.slider.domNode);\n    this._register(dom.addDisposableListener(this.slider.domNode, dom.EventType.POINTER_DOWN, e => {\n      if (e.button === 0) {\n        e.preventDefault();\n        this._sliderPointerDown(e);\n      }\n    }));\n    this.onclick(this.slider.domNode, e => {\n      if (e.leftButton) {\n        e.stopPropagation();\n      }\n    });\n  }\n  // ----------------- Update state\n  _onElementSize(visibleSize) {\n    if (this._scrollbarState.setVisibleSize(visibleSize)) {\n      this._visibilityController.setIsNeeded(this._scrollbarState.isNeeded());\n      this._shouldRender = true;\n      if (!this._lazyRender) {\n        this.render();\n      }\n    }\n    return this._shouldRender;\n  }\n  _onElementScrollSize(elementScrollSize) {\n    if (this._scrollbarState.setScrollSize(elementScrollSize)) {\n      this._visibilityController.setIsNeeded(this._scrollbarState.isNeeded());\n      this._shouldRender = true;\n      if (!this._lazyRender) {\n        this.render();\n      }\n    }\n    return this._shouldRender;\n  }\n  _onElementScrollPosition(elementScrollPosition) {\n    if (this._scrollbarState.setScrollPosition(elementScrollPosition)) {\n      this._visibilityController.setIsNeeded(this._scrollbarState.isNeeded());\n      this._shouldRender = true;\n      if (!this._lazyRender) {\n        this.render();\n      }\n    }\n    return this._shouldRender;\n  }\n  // ----------------- rendering\n  beginReveal() {\n    this._visibilityController.setShouldBeVisible(true);\n  }\n  beginHide() {\n    this._visibilityController.setShouldBeVisible(false);\n  }\n  render() {\n    if (!this._shouldRender) {\n      return;\n    }\n    this._shouldRender = false;\n    this._renderDomNode(this._scrollbarState.getRectangleLargeSize(), this._scrollbarState.getRectangleSmallSize());\n    this._updateSlider(this._scrollbarState.getSliderSize(), this._scrollbarState.getArrowSize() + this._scrollbarState.getSliderPosition());\n  }\n  // ----------------- DOM events\n  _domNodePointerDown(e) {\n    if (e.target !== this.domNode.domNode) {\n      return;\n    }\n    this._onPointerDown(e);\n  }\n  delegatePointerDown(e) {\n    const domTop = this.domNode.domNode.getClientRects()[0].top;\n    const sliderStart = domTop + this._scrollbarState.getSliderPosition();\n    const sliderStop = domTop + this._scrollbarState.getSliderPosition() + this._scrollbarState.getSliderSize();\n    const pointerPos = this._sliderPointerPosition(e);\n    if (sliderStart <= pointerPos && pointerPos <= sliderStop) {\n      // Act as if it was a pointer down on the slider\n      if (e.button === 0) {\n        e.preventDefault();\n        this._sliderPointerDown(e);\n      }\n    } else {\n      // Act as if it was a pointer down on the scrollbar\n      this._onPointerDown(e);\n    }\n  }\n  _onPointerDown(e) {\n    let offsetX;\n    let offsetY;\n    if (e.target === this.domNode.domNode && typeof e.offsetX === 'number' && typeof e.offsetY === 'number') {\n      offsetX = e.offsetX;\n      offsetY = e.offsetY;\n    } else {\n      const domNodePosition = dom.getDomNodePagePosition(this.domNode.domNode);\n      offsetX = e.pageX - domNodePosition.left;\n      offsetY = e.pageY - domNodePosition.top;\n    }\n    const offset = this._pointerDownRelativePosition(offsetX, offsetY);\n    this._setDesiredScrollPositionNow(this._scrollByPage ? this._scrollbarState.getDesiredScrollPositionFromOffsetPaged(offset) : this._scrollbarState.getDesiredScrollPositionFromOffset(offset));\n    if (e.button === 0) {\n      // left button\n      e.preventDefault();\n      this._sliderPointerDown(e);\n    }\n  }\n  _sliderPointerDown(e) {\n    if (!e.target || !(e.target instanceof Element)) {\n      return;\n    }\n    const initialPointerPosition = this._sliderPointerPosition(e);\n    const initialPointerOrthogonalPosition = this._sliderOrthogonalPointerPosition(e);\n    const initialScrollbarState = this._scrollbarState.clone();\n    this.slider.toggleClassName('active', true);\n    this._pointerMoveMonitor.startMonitoring(e.target, e.pointerId, e.buttons, pointerMoveData => {\n      const pointerOrthogonalPosition = this._sliderOrthogonalPointerPosition(pointerMoveData);\n      const pointerOrthogonalDelta = Math.abs(pointerOrthogonalPosition - initialPointerOrthogonalPosition);\n      if (platform.isWindows && pointerOrthogonalDelta > POINTER_DRAG_RESET_DISTANCE) {\n        // The pointer has wondered away from the scrollbar => reset dragging\n        this._setDesiredScrollPositionNow(initialScrollbarState.getScrollPosition());\n        return;\n      }\n      const pointerPosition = this._sliderPointerPosition(pointerMoveData);\n      const pointerDelta = pointerPosition - initialPointerPosition;\n      this._setDesiredScrollPositionNow(initialScrollbarState.getDesiredScrollPositionFromDelta(pointerDelta));\n    }, () => {\n      this.slider.toggleClassName('active', false);\n      this._host.onDragEnd();\n    });\n    this._host.onDragStart();\n  }\n  _setDesiredScrollPositionNow(_desiredScrollPosition) {\n    const desiredScrollPosition = {};\n    this.writeScrollPosition(desiredScrollPosition, _desiredScrollPosition);\n    this._scrollable.setScrollPositionNow(desiredScrollPosition);\n  }\n  updateScrollbarSize(scrollbarSize) {\n    this._updateScrollbarSize(scrollbarSize);\n    this._scrollbarState.setScrollbarSize(scrollbarSize);\n    this._shouldRender = true;\n    if (!this._lazyRender) {\n      this.render();\n    }\n  }\n  isNeeded() {\n    return this._scrollbarState.isNeeded();\n  }\n}", "map": {"version": 3, "names": ["dom", "createFastDomNode", "GlobalPointerMoveMonitor", "ScrollbarArrow", "ScrollbarVisibilityController", "Widget", "platform", "POINTER_DRAG_RESET_DISTANCE", "AbstractScrollbar", "constructor", "opts", "_lazy<PERSON>ender", "lazy<PERSON>ender", "_host", "host", "_scrollable", "scrollable", "_scrollByPage", "scrollByPage", "_scrollbarState", "scrollbarState", "_visibilityController", "_register", "visibility", "extraScrollbarClassName", "setIsNeeded", "isNeeded", "_pointerMoveMonitor", "_shouldRender", "domNode", "document", "createElement", "setAttribute", "setDomNode", "setPosition", "addDisposableListener", "EventType", "POINTER_DOWN", "e", "_domNodePointerDown", "_createArrow", "arrow", "append<PERSON><PERSON><PERSON>", "bgDomNode", "_createSlider", "top", "left", "width", "height", "slider", "setClassName", "setTop", "setLeft", "<PERSON><PERSON><PERSON><PERSON>", "setHeight", "setLayerHinting", "setContain", "button", "preventDefault", "_sliderPointerDown", "onclick", "leftButton", "stopPropagation", "_onElementSize", "visibleSize", "setVisibleSize", "render", "_onElementScrollSize", "elementScrollSize", "setScrollSize", "_onElementScrollPosition", "elementScrollPosition", "setScrollPosition", "beginReveal", "setShouldBeVisible", "beginHide", "_renderDomNode", "getRectangleLargeSize", "getRectangleSmallSize", "_updateSlider", "getSliderSize", "getArrowSize", "getSliderPosition", "target", "_onPointerDown", "delegatePointerDown", "domTop", "getClientRects", "sliderStart", "sliderStop", "pointerPos", "_sliderPointerPosition", "offsetX", "offsetY", "domNodePosition", "getDomNodePagePosition", "pageX", "pageY", "offset", "_pointerDownRelativePosition", "_setDesiredScrollPositionNow", "getDesiredScrollPositionFromOffsetPaged", "getDesiredScrollPositionFromOffset", "Element", "initialPointerPosition", "initialPointerOrthogonalPosition", "_sliderOrthogonalPointerPosition", "initialScrollbarState", "clone", "toggleClassName", "startMonitoring", "pointerId", "buttons", "pointerMoveData", "pointerOrthogonalPosition", "pointer<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Math", "abs", "isWindows", "getScrollPosition", "pointerPosition", "pointer<PERSON><PERSON><PERSON>", "getDesiredScrollPositionFromDelta", "onDragEnd", "onDragStart", "_desiredScrollPosition", "desiredScrollPosition", "writeScrollPosition", "setScrollPositionNow", "updateScrollbarSize", "scrollbarSize", "_updateScrollbarSize", "setScrollbarSize"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/browser/ui/scrollbar/abstractScrollbar.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as dom from '../../dom.js';\nimport { createFastDomNode } from '../../fastDomNode.js';\nimport { GlobalPointerMoveMonitor } from '../../globalPointerMoveMonitor.js';\nimport { ScrollbarArrow } from './scrollbarArrow.js';\nimport { ScrollbarVisibilityController } from './scrollbarVisibilityController.js';\nimport { Widget } from '../widget.js';\nimport * as platform from '../../../common/platform.js';\n/**\n * The orthogonal distance to the slider at which dragging \"resets\". This implements \"snapping\"\n */\nconst POINTER_DRAG_RESET_DISTANCE = 140;\nexport class AbstractScrollbar extends Widget {\n    constructor(opts) {\n        super();\n        this._lazyRender = opts.lazyRender;\n        this._host = opts.host;\n        this._scrollable = opts.scrollable;\n        this._scrollByPage = opts.scrollByPage;\n        this._scrollbarState = opts.scrollbarState;\n        this._visibilityController = this._register(new ScrollbarVisibilityController(opts.visibility, 'visible scrollbar ' + opts.extraScrollbarClassName, 'invisible scrollbar ' + opts.extraScrollbarClassName));\n        this._visibilityController.setIsNeeded(this._scrollbarState.isNeeded());\n        this._pointerMoveMonitor = this._register(new GlobalPointerMoveMonitor());\n        this._shouldRender = true;\n        this.domNode = createFastDomNode(document.createElement('div'));\n        this.domNode.setAttribute('role', 'presentation');\n        this.domNode.setAttribute('aria-hidden', 'true');\n        this._visibilityController.setDomNode(this.domNode);\n        this.domNode.setPosition('absolute');\n        this._register(dom.addDisposableListener(this.domNode.domNode, dom.EventType.POINTER_DOWN, (e) => this._domNodePointerDown(e)));\n    }\n    // ----------------- creation\n    /**\n     * Creates the dom node for an arrow & adds it to the container\n     */\n    _createArrow(opts) {\n        const arrow = this._register(new ScrollbarArrow(opts));\n        this.domNode.domNode.appendChild(arrow.bgDomNode);\n        this.domNode.domNode.appendChild(arrow.domNode);\n    }\n    /**\n     * Creates the slider dom node, adds it to the container & hooks up the events\n     */\n    _createSlider(top, left, width, height) {\n        this.slider = createFastDomNode(document.createElement('div'));\n        this.slider.setClassName('slider');\n        this.slider.setPosition('absolute');\n        this.slider.setTop(top);\n        this.slider.setLeft(left);\n        if (typeof width === 'number') {\n            this.slider.setWidth(width);\n        }\n        if (typeof height === 'number') {\n            this.slider.setHeight(height);\n        }\n        this.slider.setLayerHinting(true);\n        this.slider.setContain('strict');\n        this.domNode.domNode.appendChild(this.slider.domNode);\n        this._register(dom.addDisposableListener(this.slider.domNode, dom.EventType.POINTER_DOWN, (e) => {\n            if (e.button === 0) {\n                e.preventDefault();\n                this._sliderPointerDown(e);\n            }\n        }));\n        this.onclick(this.slider.domNode, e => {\n            if (e.leftButton) {\n                e.stopPropagation();\n            }\n        });\n    }\n    // ----------------- Update state\n    _onElementSize(visibleSize) {\n        if (this._scrollbarState.setVisibleSize(visibleSize)) {\n            this._visibilityController.setIsNeeded(this._scrollbarState.isNeeded());\n            this._shouldRender = true;\n            if (!this._lazyRender) {\n                this.render();\n            }\n        }\n        return this._shouldRender;\n    }\n    _onElementScrollSize(elementScrollSize) {\n        if (this._scrollbarState.setScrollSize(elementScrollSize)) {\n            this._visibilityController.setIsNeeded(this._scrollbarState.isNeeded());\n            this._shouldRender = true;\n            if (!this._lazyRender) {\n                this.render();\n            }\n        }\n        return this._shouldRender;\n    }\n    _onElementScrollPosition(elementScrollPosition) {\n        if (this._scrollbarState.setScrollPosition(elementScrollPosition)) {\n            this._visibilityController.setIsNeeded(this._scrollbarState.isNeeded());\n            this._shouldRender = true;\n            if (!this._lazyRender) {\n                this.render();\n            }\n        }\n        return this._shouldRender;\n    }\n    // ----------------- rendering\n    beginReveal() {\n        this._visibilityController.setShouldBeVisible(true);\n    }\n    beginHide() {\n        this._visibilityController.setShouldBeVisible(false);\n    }\n    render() {\n        if (!this._shouldRender) {\n            return;\n        }\n        this._shouldRender = false;\n        this._renderDomNode(this._scrollbarState.getRectangleLargeSize(), this._scrollbarState.getRectangleSmallSize());\n        this._updateSlider(this._scrollbarState.getSliderSize(), this._scrollbarState.getArrowSize() + this._scrollbarState.getSliderPosition());\n    }\n    // ----------------- DOM events\n    _domNodePointerDown(e) {\n        if (e.target !== this.domNode.domNode) {\n            return;\n        }\n        this._onPointerDown(e);\n    }\n    delegatePointerDown(e) {\n        const domTop = this.domNode.domNode.getClientRects()[0].top;\n        const sliderStart = domTop + this._scrollbarState.getSliderPosition();\n        const sliderStop = domTop + this._scrollbarState.getSliderPosition() + this._scrollbarState.getSliderSize();\n        const pointerPos = this._sliderPointerPosition(e);\n        if (sliderStart <= pointerPos && pointerPos <= sliderStop) {\n            // Act as if it was a pointer down on the slider\n            if (e.button === 0) {\n                e.preventDefault();\n                this._sliderPointerDown(e);\n            }\n        }\n        else {\n            // Act as if it was a pointer down on the scrollbar\n            this._onPointerDown(e);\n        }\n    }\n    _onPointerDown(e) {\n        let offsetX;\n        let offsetY;\n        if (e.target === this.domNode.domNode && typeof e.offsetX === 'number' && typeof e.offsetY === 'number') {\n            offsetX = e.offsetX;\n            offsetY = e.offsetY;\n        }\n        else {\n            const domNodePosition = dom.getDomNodePagePosition(this.domNode.domNode);\n            offsetX = e.pageX - domNodePosition.left;\n            offsetY = e.pageY - domNodePosition.top;\n        }\n        const offset = this._pointerDownRelativePosition(offsetX, offsetY);\n        this._setDesiredScrollPositionNow(this._scrollByPage\n            ? this._scrollbarState.getDesiredScrollPositionFromOffsetPaged(offset)\n            : this._scrollbarState.getDesiredScrollPositionFromOffset(offset));\n        if (e.button === 0) {\n            // left button\n            e.preventDefault();\n            this._sliderPointerDown(e);\n        }\n    }\n    _sliderPointerDown(e) {\n        if (!e.target || !(e.target instanceof Element)) {\n            return;\n        }\n        const initialPointerPosition = this._sliderPointerPosition(e);\n        const initialPointerOrthogonalPosition = this._sliderOrthogonalPointerPosition(e);\n        const initialScrollbarState = this._scrollbarState.clone();\n        this.slider.toggleClassName('active', true);\n        this._pointerMoveMonitor.startMonitoring(e.target, e.pointerId, e.buttons, (pointerMoveData) => {\n            const pointerOrthogonalPosition = this._sliderOrthogonalPointerPosition(pointerMoveData);\n            const pointerOrthogonalDelta = Math.abs(pointerOrthogonalPosition - initialPointerOrthogonalPosition);\n            if (platform.isWindows && pointerOrthogonalDelta > POINTER_DRAG_RESET_DISTANCE) {\n                // The pointer has wondered away from the scrollbar => reset dragging\n                this._setDesiredScrollPositionNow(initialScrollbarState.getScrollPosition());\n                return;\n            }\n            const pointerPosition = this._sliderPointerPosition(pointerMoveData);\n            const pointerDelta = pointerPosition - initialPointerPosition;\n            this._setDesiredScrollPositionNow(initialScrollbarState.getDesiredScrollPositionFromDelta(pointerDelta));\n        }, () => {\n            this.slider.toggleClassName('active', false);\n            this._host.onDragEnd();\n        });\n        this._host.onDragStart();\n    }\n    _setDesiredScrollPositionNow(_desiredScrollPosition) {\n        const desiredScrollPosition = {};\n        this.writeScrollPosition(desiredScrollPosition, _desiredScrollPosition);\n        this._scrollable.setScrollPositionNow(desiredScrollPosition);\n    }\n    updateScrollbarSize(scrollbarSize) {\n        this._updateScrollbarSize(scrollbarSize);\n        this._scrollbarState.setScrollbarSize(scrollbarSize);\n        this._shouldRender = true;\n        if (!this._lazyRender) {\n            this.render();\n        }\n    }\n    isNeeded() {\n        return this._scrollbarState.isNeeded();\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,KAAKA,GAAG,MAAM,cAAc;AACnC,SAASC,iBAAiB,QAAQ,sBAAsB;AACxD,SAASC,wBAAwB,QAAQ,mCAAmC;AAC5E,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,6BAA6B,QAAQ,oCAAoC;AAClF,SAASC,MAAM,QAAQ,cAAc;AACrC,OAAO,KAAKC,QAAQ,MAAM,6BAA6B;AACvD;AACA;AACA;AACA,MAAMC,2BAA2B,GAAG,GAAG;AACvC,OAAO,MAAMC,iBAAiB,SAASH,MAAM,CAAC;EAC1CI,WAAWA,CAACC,IAAI,EAAE;IACd,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,WAAW,GAAGD,IAAI,CAACE,UAAU;IAClC,IAAI,CAACC,KAAK,GAAGH,IAAI,CAACI,IAAI;IACtB,IAAI,CAACC,WAAW,GAAGL,IAAI,CAACM,UAAU;IAClC,IAAI,CAACC,aAAa,GAAGP,IAAI,CAACQ,YAAY;IACtC,IAAI,CAACC,eAAe,GAAGT,IAAI,CAACU,cAAc;IAC1C,IAAI,CAACC,qBAAqB,GAAG,IAAI,CAACC,SAAS,CAAC,IAAIlB,6BAA6B,CAACM,IAAI,CAACa,UAAU,EAAE,oBAAoB,GAAGb,IAAI,CAACc,uBAAuB,EAAE,sBAAsB,GAAGd,IAAI,CAACc,uBAAuB,CAAC,CAAC;IAC3M,IAAI,CAACH,qBAAqB,CAACI,WAAW,CAAC,IAAI,CAACN,eAAe,CAACO,QAAQ,CAAC,CAAC,CAAC;IACvE,IAAI,CAACC,mBAAmB,GAAG,IAAI,CAACL,SAAS,CAAC,IAAIpB,wBAAwB,CAAC,CAAC,CAAC;IACzE,IAAI,CAAC0B,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,OAAO,GAAG5B,iBAAiB,CAAC6B,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/D,IAAI,CAACF,OAAO,CAACG,YAAY,CAAC,MAAM,EAAE,cAAc,CAAC;IACjD,IAAI,CAACH,OAAO,CAACG,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;IAChD,IAAI,CAACX,qBAAqB,CAACY,UAAU,CAAC,IAAI,CAACJ,OAAO,CAAC;IACnD,IAAI,CAACA,OAAO,CAACK,WAAW,CAAC,UAAU,CAAC;IACpC,IAAI,CAACZ,SAAS,CAACtB,GAAG,CAACmC,qBAAqB,CAAC,IAAI,CAACN,OAAO,CAACA,OAAO,EAAE7B,GAAG,CAACoC,SAAS,CAACC,YAAY,EAAGC,CAAC,IAAK,IAAI,CAACC,mBAAmB,CAACD,CAAC,CAAC,CAAC,CAAC;EACnI;EACA;EACA;AACJ;AACA;EACIE,YAAYA,CAAC9B,IAAI,EAAE;IACf,MAAM+B,KAAK,GAAG,IAAI,CAACnB,SAAS,CAAC,IAAInB,cAAc,CAACO,IAAI,CAAC,CAAC;IACtD,IAAI,CAACmB,OAAO,CAACA,OAAO,CAACa,WAAW,CAACD,KAAK,CAACE,SAAS,CAAC;IACjD,IAAI,CAACd,OAAO,CAACA,OAAO,CAACa,WAAW,CAACD,KAAK,CAACZ,OAAO,CAAC;EACnD;EACA;AACJ;AACA;EACIe,aAAaA,CAACC,GAAG,EAAEC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAE;IACpC,IAAI,CAACC,MAAM,GAAGhD,iBAAiB,CAAC6B,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC9D,IAAI,CAACkB,MAAM,CAACC,YAAY,CAAC,QAAQ,CAAC;IAClC,IAAI,CAACD,MAAM,CAACf,WAAW,CAAC,UAAU,CAAC;IACnC,IAAI,CAACe,MAAM,CAACE,MAAM,CAACN,GAAG,CAAC;IACvB,IAAI,CAACI,MAAM,CAACG,OAAO,CAACN,IAAI,CAAC;IACzB,IAAI,OAAOC,KAAK,KAAK,QAAQ,EAAE;MAC3B,IAAI,CAACE,MAAM,CAACI,QAAQ,CAACN,KAAK,CAAC;IAC/B;IACA,IAAI,OAAOC,MAAM,KAAK,QAAQ,EAAE;MAC5B,IAAI,CAACC,MAAM,CAACK,SAAS,CAACN,MAAM,CAAC;IACjC;IACA,IAAI,CAACC,MAAM,CAACM,eAAe,CAAC,IAAI,CAAC;IACjC,IAAI,CAACN,MAAM,CAACO,UAAU,CAAC,QAAQ,CAAC;IAChC,IAAI,CAAC3B,OAAO,CAACA,OAAO,CAACa,WAAW,CAAC,IAAI,CAACO,MAAM,CAACpB,OAAO,CAAC;IACrD,IAAI,CAACP,SAAS,CAACtB,GAAG,CAACmC,qBAAqB,CAAC,IAAI,CAACc,MAAM,CAACpB,OAAO,EAAE7B,GAAG,CAACoC,SAAS,CAACC,YAAY,EAAGC,CAAC,IAAK;MAC7F,IAAIA,CAAC,CAACmB,MAAM,KAAK,CAAC,EAAE;QAChBnB,CAAC,CAACoB,cAAc,CAAC,CAAC;QAClB,IAAI,CAACC,kBAAkB,CAACrB,CAAC,CAAC;MAC9B;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,CAACsB,OAAO,CAAC,IAAI,CAACX,MAAM,CAACpB,OAAO,EAAES,CAAC,IAAI;MACnC,IAAIA,CAAC,CAACuB,UAAU,EAAE;QACdvB,CAAC,CAACwB,eAAe,CAAC,CAAC;MACvB;IACJ,CAAC,CAAC;EACN;EACA;EACAC,cAAcA,CAACC,WAAW,EAAE;IACxB,IAAI,IAAI,CAAC7C,eAAe,CAAC8C,cAAc,CAACD,WAAW,CAAC,EAAE;MAClD,IAAI,CAAC3C,qBAAqB,CAACI,WAAW,CAAC,IAAI,CAACN,eAAe,CAACO,QAAQ,CAAC,CAAC,CAAC;MACvE,IAAI,CAACE,aAAa,GAAG,IAAI;MACzB,IAAI,CAAC,IAAI,CAACjB,WAAW,EAAE;QACnB,IAAI,CAACuD,MAAM,CAAC,CAAC;MACjB;IACJ;IACA,OAAO,IAAI,CAACtC,aAAa;EAC7B;EACAuC,oBAAoBA,CAACC,iBAAiB,EAAE;IACpC,IAAI,IAAI,CAACjD,eAAe,CAACkD,aAAa,CAACD,iBAAiB,CAAC,EAAE;MACvD,IAAI,CAAC/C,qBAAqB,CAACI,WAAW,CAAC,IAAI,CAACN,eAAe,CAACO,QAAQ,CAAC,CAAC,CAAC;MACvE,IAAI,CAACE,aAAa,GAAG,IAAI;MACzB,IAAI,CAAC,IAAI,CAACjB,WAAW,EAAE;QACnB,IAAI,CAACuD,MAAM,CAAC,CAAC;MACjB;IACJ;IACA,OAAO,IAAI,CAACtC,aAAa;EAC7B;EACA0C,wBAAwBA,CAACC,qBAAqB,EAAE;IAC5C,IAAI,IAAI,CAACpD,eAAe,CAACqD,iBAAiB,CAACD,qBAAqB,CAAC,EAAE;MAC/D,IAAI,CAAClD,qBAAqB,CAACI,WAAW,CAAC,IAAI,CAACN,eAAe,CAACO,QAAQ,CAAC,CAAC,CAAC;MACvE,IAAI,CAACE,aAAa,GAAG,IAAI;MACzB,IAAI,CAAC,IAAI,CAACjB,WAAW,EAAE;QACnB,IAAI,CAACuD,MAAM,CAAC,CAAC;MACjB;IACJ;IACA,OAAO,IAAI,CAACtC,aAAa;EAC7B;EACA;EACA6C,WAAWA,CAAA,EAAG;IACV,IAAI,CAACpD,qBAAqB,CAACqD,kBAAkB,CAAC,IAAI,CAAC;EACvD;EACAC,SAASA,CAAA,EAAG;IACR,IAAI,CAACtD,qBAAqB,CAACqD,kBAAkB,CAAC,KAAK,CAAC;EACxD;EACAR,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC,IAAI,CAACtC,aAAa,EAAE;MACrB;IACJ;IACA,IAAI,CAACA,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACgD,cAAc,CAAC,IAAI,CAACzD,eAAe,CAAC0D,qBAAqB,CAAC,CAAC,EAAE,IAAI,CAAC1D,eAAe,CAAC2D,qBAAqB,CAAC,CAAC,CAAC;IAC/G,IAAI,CAACC,aAAa,CAAC,IAAI,CAAC5D,eAAe,CAAC6D,aAAa,CAAC,CAAC,EAAE,IAAI,CAAC7D,eAAe,CAAC8D,YAAY,CAAC,CAAC,GAAG,IAAI,CAAC9D,eAAe,CAAC+D,iBAAiB,CAAC,CAAC,CAAC;EAC5I;EACA;EACA3C,mBAAmBA,CAACD,CAAC,EAAE;IACnB,IAAIA,CAAC,CAAC6C,MAAM,KAAK,IAAI,CAACtD,OAAO,CAACA,OAAO,EAAE;MACnC;IACJ;IACA,IAAI,CAACuD,cAAc,CAAC9C,CAAC,CAAC;EAC1B;EACA+C,mBAAmBA,CAAC/C,CAAC,EAAE;IACnB,MAAMgD,MAAM,GAAG,IAAI,CAACzD,OAAO,CAACA,OAAO,CAAC0D,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC1C,GAAG;IAC3D,MAAM2C,WAAW,GAAGF,MAAM,GAAG,IAAI,CAACnE,eAAe,CAAC+D,iBAAiB,CAAC,CAAC;IACrE,MAAMO,UAAU,GAAGH,MAAM,GAAG,IAAI,CAACnE,eAAe,CAAC+D,iBAAiB,CAAC,CAAC,GAAG,IAAI,CAAC/D,eAAe,CAAC6D,aAAa,CAAC,CAAC;IAC3G,MAAMU,UAAU,GAAG,IAAI,CAACC,sBAAsB,CAACrD,CAAC,CAAC;IACjD,IAAIkD,WAAW,IAAIE,UAAU,IAAIA,UAAU,IAAID,UAAU,EAAE;MACvD;MACA,IAAInD,CAAC,CAACmB,MAAM,KAAK,CAAC,EAAE;QAChBnB,CAAC,CAACoB,cAAc,CAAC,CAAC;QAClB,IAAI,CAACC,kBAAkB,CAACrB,CAAC,CAAC;MAC9B;IACJ,CAAC,MACI;MACD;MACA,IAAI,CAAC8C,cAAc,CAAC9C,CAAC,CAAC;IAC1B;EACJ;EACA8C,cAAcA,CAAC9C,CAAC,EAAE;IACd,IAAIsD,OAAO;IACX,IAAIC,OAAO;IACX,IAAIvD,CAAC,CAAC6C,MAAM,KAAK,IAAI,CAACtD,OAAO,CAACA,OAAO,IAAI,OAAOS,CAAC,CAACsD,OAAO,KAAK,QAAQ,IAAI,OAAOtD,CAAC,CAACuD,OAAO,KAAK,QAAQ,EAAE;MACrGD,OAAO,GAAGtD,CAAC,CAACsD,OAAO;MACnBC,OAAO,GAAGvD,CAAC,CAACuD,OAAO;IACvB,CAAC,MACI;MACD,MAAMC,eAAe,GAAG9F,GAAG,CAAC+F,sBAAsB,CAAC,IAAI,CAAClE,OAAO,CAACA,OAAO,CAAC;MACxE+D,OAAO,GAAGtD,CAAC,CAAC0D,KAAK,GAAGF,eAAe,CAAChD,IAAI;MACxC+C,OAAO,GAAGvD,CAAC,CAAC2D,KAAK,GAAGH,eAAe,CAACjD,GAAG;IAC3C;IACA,MAAMqD,MAAM,GAAG,IAAI,CAACC,4BAA4B,CAACP,OAAO,EAAEC,OAAO,CAAC;IAClE,IAAI,CAACO,4BAA4B,CAAC,IAAI,CAACnF,aAAa,GAC9C,IAAI,CAACE,eAAe,CAACkF,uCAAuC,CAACH,MAAM,CAAC,GACpE,IAAI,CAAC/E,eAAe,CAACmF,kCAAkC,CAACJ,MAAM,CAAC,CAAC;IACtE,IAAI5D,CAAC,CAACmB,MAAM,KAAK,CAAC,EAAE;MAChB;MACAnB,CAAC,CAACoB,cAAc,CAAC,CAAC;MAClB,IAAI,CAACC,kBAAkB,CAACrB,CAAC,CAAC;IAC9B;EACJ;EACAqB,kBAAkBA,CAACrB,CAAC,EAAE;IAClB,IAAI,CAACA,CAAC,CAAC6C,MAAM,IAAI,EAAE7C,CAAC,CAAC6C,MAAM,YAAYoB,OAAO,CAAC,EAAE;MAC7C;IACJ;IACA,MAAMC,sBAAsB,GAAG,IAAI,CAACb,sBAAsB,CAACrD,CAAC,CAAC;IAC7D,MAAMmE,gCAAgC,GAAG,IAAI,CAACC,gCAAgC,CAACpE,CAAC,CAAC;IACjF,MAAMqE,qBAAqB,GAAG,IAAI,CAACxF,eAAe,CAACyF,KAAK,CAAC,CAAC;IAC1D,IAAI,CAAC3D,MAAM,CAAC4D,eAAe,CAAC,QAAQ,EAAE,IAAI,CAAC;IAC3C,IAAI,CAAClF,mBAAmB,CAACmF,eAAe,CAACxE,CAAC,CAAC6C,MAAM,EAAE7C,CAAC,CAACyE,SAAS,EAAEzE,CAAC,CAAC0E,OAAO,EAAGC,eAAe,IAAK;MAC5F,MAAMC,yBAAyB,GAAG,IAAI,CAACR,gCAAgC,CAACO,eAAe,CAAC;MACxF,MAAME,sBAAsB,GAAGC,IAAI,CAACC,GAAG,CAACH,yBAAyB,GAAGT,gCAAgC,CAAC;MACrG,IAAInG,QAAQ,CAACgH,SAAS,IAAIH,sBAAsB,GAAG5G,2BAA2B,EAAE;QAC5E;QACA,IAAI,CAAC6F,4BAA4B,CAACO,qBAAqB,CAACY,iBAAiB,CAAC,CAAC,CAAC;QAC5E;MACJ;MACA,MAAMC,eAAe,GAAG,IAAI,CAAC7B,sBAAsB,CAACsB,eAAe,CAAC;MACpE,MAAMQ,YAAY,GAAGD,eAAe,GAAGhB,sBAAsB;MAC7D,IAAI,CAACJ,4BAA4B,CAACO,qBAAqB,CAACe,iCAAiC,CAACD,YAAY,CAAC,CAAC;IAC5G,CAAC,EAAE,MAAM;MACL,IAAI,CAACxE,MAAM,CAAC4D,eAAe,CAAC,QAAQ,EAAE,KAAK,CAAC;MAC5C,IAAI,CAAChG,KAAK,CAAC8G,SAAS,CAAC,CAAC;IAC1B,CAAC,CAAC;IACF,IAAI,CAAC9G,KAAK,CAAC+G,WAAW,CAAC,CAAC;EAC5B;EACAxB,4BAA4BA,CAACyB,sBAAsB,EAAE;IACjD,MAAMC,qBAAqB,GAAG,CAAC,CAAC;IAChC,IAAI,CAACC,mBAAmB,CAACD,qBAAqB,EAAED,sBAAsB,CAAC;IACvE,IAAI,CAAC9G,WAAW,CAACiH,oBAAoB,CAACF,qBAAqB,CAAC;EAChE;EACAG,mBAAmBA,CAACC,aAAa,EAAE;IAC/B,IAAI,CAACC,oBAAoB,CAACD,aAAa,CAAC;IACxC,IAAI,CAAC/G,eAAe,CAACiH,gBAAgB,CAACF,aAAa,CAAC;IACpD,IAAI,CAACtG,aAAa,GAAG,IAAI;IACzB,IAAI,CAAC,IAAI,CAACjB,WAAW,EAAE;MACnB,IAAI,CAACuD,MAAM,CAAC,CAAC;IACjB;EACJ;EACAxC,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACP,eAAe,CAACO,QAAQ,CAAC,CAAC;EAC1C;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}