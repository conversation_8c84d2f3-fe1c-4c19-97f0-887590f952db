{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Dimension } from '../../dom.js';\nimport { OrthogonalEdge, Sash } from '../sash/sash.js';\nimport { Emitter, Event } from '../../../common/event.js';\nimport { DisposableStore } from '../../../common/lifecycle.js';\nexport class ResizableHTMLElement {\n  constructor() {\n    this._onDidWillResize = new Emitter();\n    this.onDidWillResize = this._onDidWillResize.event;\n    this._onDidResize = new Emitter();\n    this.onDidResize = this._onDidResize.event;\n    this._sashListener = new DisposableStore();\n    this._size = new Dimension(0, 0);\n    this._minSize = new Dimension(0, 0);\n    this._maxSize = new Dimension(Number.MAX_SAFE_INTEGER, Number.MAX_SAFE_INTEGER);\n    this.domNode = document.createElement('div');\n    this._eastSash = new Sash(this.domNode, {\n      getVerticalSashLeft: () => this._size.width\n    }, {\n      orientation: 0 /* Orientation.VERTICAL */\n    });\n    this._westSash = new Sash(this.domNode, {\n      getVerticalSashLeft: () => 0\n    }, {\n      orientation: 0 /* Orientation.VERTICAL */\n    });\n    this._northSash = new Sash(this.domNode, {\n      getHorizontalSashTop: () => 0\n    }, {\n      orientation: 1 /* Orientation.HORIZONTAL */,\n      orthogonalEdge: OrthogonalEdge.North\n    });\n    this._southSash = new Sash(this.domNode, {\n      getHorizontalSashTop: () => this._size.height\n    }, {\n      orientation: 1 /* Orientation.HORIZONTAL */,\n      orthogonalEdge: OrthogonalEdge.South\n    });\n    this._northSash.orthogonalStartSash = this._westSash;\n    this._northSash.orthogonalEndSash = this._eastSash;\n    this._southSash.orthogonalStartSash = this._westSash;\n    this._southSash.orthogonalEndSash = this._eastSash;\n    let currentSize;\n    let deltaY = 0;\n    let deltaX = 0;\n    this._sashListener.add(Event.any(this._northSash.onDidStart, this._eastSash.onDidStart, this._southSash.onDidStart, this._westSash.onDidStart)(() => {\n      if (currentSize === undefined) {\n        this._onDidWillResize.fire();\n        currentSize = this._size;\n        deltaY = 0;\n        deltaX = 0;\n      }\n    }));\n    this._sashListener.add(Event.any(this._northSash.onDidEnd, this._eastSash.onDidEnd, this._southSash.onDidEnd, this._westSash.onDidEnd)(() => {\n      if (currentSize !== undefined) {\n        currentSize = undefined;\n        deltaY = 0;\n        deltaX = 0;\n        this._onDidResize.fire({\n          dimension: this._size,\n          done: true\n        });\n      }\n    }));\n    this._sashListener.add(this._eastSash.onDidChange(e => {\n      if (currentSize) {\n        deltaX = e.currentX - e.startX;\n        this.layout(currentSize.height + deltaY, currentSize.width + deltaX);\n        this._onDidResize.fire({\n          dimension: this._size,\n          done: false,\n          east: true\n        });\n      }\n    }));\n    this._sashListener.add(this._westSash.onDidChange(e => {\n      if (currentSize) {\n        deltaX = -(e.currentX - e.startX);\n        this.layout(currentSize.height + deltaY, currentSize.width + deltaX);\n        this._onDidResize.fire({\n          dimension: this._size,\n          done: false,\n          west: true\n        });\n      }\n    }));\n    this._sashListener.add(this._northSash.onDidChange(e => {\n      if (currentSize) {\n        deltaY = -(e.currentY - e.startY);\n        this.layout(currentSize.height + deltaY, currentSize.width + deltaX);\n        this._onDidResize.fire({\n          dimension: this._size,\n          done: false,\n          north: true\n        });\n      }\n    }));\n    this._sashListener.add(this._southSash.onDidChange(e => {\n      if (currentSize) {\n        deltaY = e.currentY - e.startY;\n        this.layout(currentSize.height + deltaY, currentSize.width + deltaX);\n        this._onDidResize.fire({\n          dimension: this._size,\n          done: false,\n          south: true\n        });\n      }\n    }));\n    this._sashListener.add(Event.any(this._eastSash.onDidReset, this._westSash.onDidReset)(e => {\n      if (this._preferredSize) {\n        this.layout(this._size.height, this._preferredSize.width);\n        this._onDidResize.fire({\n          dimension: this._size,\n          done: true\n        });\n      }\n    }));\n    this._sashListener.add(Event.any(this._northSash.onDidReset, this._southSash.onDidReset)(e => {\n      if (this._preferredSize) {\n        this.layout(this._preferredSize.height, this._size.width);\n        this._onDidResize.fire({\n          dimension: this._size,\n          done: true\n        });\n      }\n    }));\n  }\n  dispose() {\n    this._northSash.dispose();\n    this._southSash.dispose();\n    this._eastSash.dispose();\n    this._westSash.dispose();\n    this._sashListener.dispose();\n    this._onDidResize.dispose();\n    this._onDidWillResize.dispose();\n    this.domNode.remove();\n  }\n  enableSashes(north, east, south, west) {\n    this._northSash.state = north ? 3 /* SashState.Enabled */ : 0 /* SashState.Disabled */;\n    this._eastSash.state = east ? 3 /* SashState.Enabled */ : 0 /* SashState.Disabled */;\n    this._southSash.state = south ? 3 /* SashState.Enabled */ : 0 /* SashState.Disabled */;\n    this._westSash.state = west ? 3 /* SashState.Enabled */ : 0 /* SashState.Disabled */;\n  }\n  layout(height = this.size.height, width = this.size.width) {\n    const {\n      height: minHeight,\n      width: minWidth\n    } = this._minSize;\n    const {\n      height: maxHeight,\n      width: maxWidth\n    } = this._maxSize;\n    height = Math.max(minHeight, Math.min(maxHeight, height));\n    width = Math.max(minWidth, Math.min(maxWidth, width));\n    const newSize = new Dimension(width, height);\n    if (!Dimension.equals(newSize, this._size)) {\n      this.domNode.style.height = height + 'px';\n      this.domNode.style.width = width + 'px';\n      this._size = newSize;\n      this._northSash.layout();\n      this._eastSash.layout();\n      this._southSash.layout();\n      this._westSash.layout();\n    }\n  }\n  clearSashHoverState() {\n    this._eastSash.clearSashHoverState();\n    this._westSash.clearSashHoverState();\n    this._northSash.clearSashHoverState();\n    this._southSash.clearSashHoverState();\n  }\n  get size() {\n    return this._size;\n  }\n  set maxSize(value) {\n    this._maxSize = value;\n  }\n  get maxSize() {\n    return this._maxSize;\n  }\n  set minSize(value) {\n    this._minSize = value;\n  }\n  get minSize() {\n    return this._minSize;\n  }\n  set preferredSize(value) {\n    this._preferredSize = value;\n  }\n  get preferredSize() {\n    return this._preferredSize;\n  }\n}", "map": {"version": 3, "names": ["Dimension", "OrthogonalEdge", "<PERSON>sh", "Emitter", "Event", "DisposableStore", "ResizableHTMLElement", "constructor", "_onDidWillResize", "onDidWillResize", "event", "_onDidResize", "onDidResize", "_sashListener", "_size", "_minSize", "_maxSize", "Number", "MAX_SAFE_INTEGER", "domNode", "document", "createElement", "_eastSash", "getVerticalSashLeft", "width", "orientation", "_westSash", "_northSash", "getHorizontalSashTop", "orthogonalEdge", "North", "_southSash", "height", "South", "orthogonalStartSash", "orthogonalEndSash", "currentSize", "deltaY", "deltaX", "add", "any", "onDidStart", "undefined", "fire", "onDidEnd", "dimension", "done", "onDidChange", "e", "currentX", "startX", "layout", "east", "west", "currentY", "startY", "north", "south", "onDidReset", "_preferredSize", "dispose", "remove", "enableSashes", "state", "size", "minHeight", "min<PERSON><PERSON><PERSON>", "maxHeight", "max<PERSON><PERSON><PERSON>", "Math", "max", "min", "newSize", "equals", "style", "clearSashHoverState", "maxSize", "value", "minSize", "preferredSize"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/browser/ui/resizable/resizable.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Dimension } from '../../dom.js';\nimport { OrthogonalEdge, Sash } from '../sash/sash.js';\nimport { Emitter, Event } from '../../../common/event.js';\nimport { DisposableStore } from '../../../common/lifecycle.js';\nexport class ResizableHTMLElement {\n    constructor() {\n        this._onDidWillResize = new Emitter();\n        this.onDidWillResize = this._onDidWillResize.event;\n        this._onDidResize = new Emitter();\n        this.onDidResize = this._onDidResize.event;\n        this._sashListener = new DisposableStore();\n        this._size = new Dimension(0, 0);\n        this._minSize = new Dimension(0, 0);\n        this._maxSize = new Dimension(Number.MAX_SAFE_INTEGER, Number.MAX_SAFE_INTEGER);\n        this.domNode = document.createElement('div');\n        this._eastSash = new Sash(this.domNode, { getVerticalSashLeft: () => this._size.width }, { orientation: 0 /* Orientation.VERTICAL */ });\n        this._westSash = new Sash(this.domNode, { getVerticalSashLeft: () => 0 }, { orientation: 0 /* Orientation.VERTICAL */ });\n        this._northSash = new Sash(this.domNode, { getHorizontalSashTop: () => 0 }, { orientation: 1 /* Orientation.HORIZONTAL */, orthogonalEdge: OrthogonalEdge.North });\n        this._southSash = new Sash(this.domNode, { getHorizontalSashTop: () => this._size.height }, { orientation: 1 /* Orientation.HORIZONTAL */, orthogonalEdge: OrthogonalEdge.South });\n        this._northSash.orthogonalStartSash = this._westSash;\n        this._northSash.orthogonalEndSash = this._eastSash;\n        this._southSash.orthogonalStartSash = this._westSash;\n        this._southSash.orthogonalEndSash = this._eastSash;\n        let currentSize;\n        let deltaY = 0;\n        let deltaX = 0;\n        this._sashListener.add(Event.any(this._northSash.onDidStart, this._eastSash.onDidStart, this._southSash.onDidStart, this._westSash.onDidStart)(() => {\n            if (currentSize === undefined) {\n                this._onDidWillResize.fire();\n                currentSize = this._size;\n                deltaY = 0;\n                deltaX = 0;\n            }\n        }));\n        this._sashListener.add(Event.any(this._northSash.onDidEnd, this._eastSash.onDidEnd, this._southSash.onDidEnd, this._westSash.onDidEnd)(() => {\n            if (currentSize !== undefined) {\n                currentSize = undefined;\n                deltaY = 0;\n                deltaX = 0;\n                this._onDidResize.fire({ dimension: this._size, done: true });\n            }\n        }));\n        this._sashListener.add(this._eastSash.onDidChange(e => {\n            if (currentSize) {\n                deltaX = e.currentX - e.startX;\n                this.layout(currentSize.height + deltaY, currentSize.width + deltaX);\n                this._onDidResize.fire({ dimension: this._size, done: false, east: true });\n            }\n        }));\n        this._sashListener.add(this._westSash.onDidChange(e => {\n            if (currentSize) {\n                deltaX = -(e.currentX - e.startX);\n                this.layout(currentSize.height + deltaY, currentSize.width + deltaX);\n                this._onDidResize.fire({ dimension: this._size, done: false, west: true });\n            }\n        }));\n        this._sashListener.add(this._northSash.onDidChange(e => {\n            if (currentSize) {\n                deltaY = -(e.currentY - e.startY);\n                this.layout(currentSize.height + deltaY, currentSize.width + deltaX);\n                this._onDidResize.fire({ dimension: this._size, done: false, north: true });\n            }\n        }));\n        this._sashListener.add(this._southSash.onDidChange(e => {\n            if (currentSize) {\n                deltaY = e.currentY - e.startY;\n                this.layout(currentSize.height + deltaY, currentSize.width + deltaX);\n                this._onDidResize.fire({ dimension: this._size, done: false, south: true });\n            }\n        }));\n        this._sashListener.add(Event.any(this._eastSash.onDidReset, this._westSash.onDidReset)(e => {\n            if (this._preferredSize) {\n                this.layout(this._size.height, this._preferredSize.width);\n                this._onDidResize.fire({ dimension: this._size, done: true });\n            }\n        }));\n        this._sashListener.add(Event.any(this._northSash.onDidReset, this._southSash.onDidReset)(e => {\n            if (this._preferredSize) {\n                this.layout(this._preferredSize.height, this._size.width);\n                this._onDidResize.fire({ dimension: this._size, done: true });\n            }\n        }));\n    }\n    dispose() {\n        this._northSash.dispose();\n        this._southSash.dispose();\n        this._eastSash.dispose();\n        this._westSash.dispose();\n        this._sashListener.dispose();\n        this._onDidResize.dispose();\n        this._onDidWillResize.dispose();\n        this.domNode.remove();\n    }\n    enableSashes(north, east, south, west) {\n        this._northSash.state = north ? 3 /* SashState.Enabled */ : 0 /* SashState.Disabled */;\n        this._eastSash.state = east ? 3 /* SashState.Enabled */ : 0 /* SashState.Disabled */;\n        this._southSash.state = south ? 3 /* SashState.Enabled */ : 0 /* SashState.Disabled */;\n        this._westSash.state = west ? 3 /* SashState.Enabled */ : 0 /* SashState.Disabled */;\n    }\n    layout(height = this.size.height, width = this.size.width) {\n        const { height: minHeight, width: minWidth } = this._minSize;\n        const { height: maxHeight, width: maxWidth } = this._maxSize;\n        height = Math.max(minHeight, Math.min(maxHeight, height));\n        width = Math.max(minWidth, Math.min(maxWidth, width));\n        const newSize = new Dimension(width, height);\n        if (!Dimension.equals(newSize, this._size)) {\n            this.domNode.style.height = height + 'px';\n            this.domNode.style.width = width + 'px';\n            this._size = newSize;\n            this._northSash.layout();\n            this._eastSash.layout();\n            this._southSash.layout();\n            this._westSash.layout();\n        }\n    }\n    clearSashHoverState() {\n        this._eastSash.clearSashHoverState();\n        this._westSash.clearSashHoverState();\n        this._northSash.clearSashHoverState();\n        this._southSash.clearSashHoverState();\n    }\n    get size() {\n        return this._size;\n    }\n    set maxSize(value) {\n        this._maxSize = value;\n    }\n    get maxSize() {\n        return this._maxSize;\n    }\n    set minSize(value) {\n        this._minSize = value;\n    }\n    get minSize() {\n        return this._minSize;\n    }\n    set preferredSize(value) {\n        this._preferredSize = value;\n    }\n    get preferredSize() {\n        return this._preferredSize;\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,cAAc;AACxC,SAASC,cAAc,EAAEC,IAAI,QAAQ,iBAAiB;AACtD,SAASC,OAAO,EAAEC,KAAK,QAAQ,0BAA0B;AACzD,SAASC,eAAe,QAAQ,8BAA8B;AAC9D,OAAO,MAAMC,oBAAoB,CAAC;EAC9BC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,gBAAgB,GAAG,IAAIL,OAAO,CAAC,CAAC;IACrC,IAAI,CAACM,eAAe,GAAG,IAAI,CAACD,gBAAgB,CAACE,KAAK;IAClD,IAAI,CAACC,YAAY,GAAG,IAAIR,OAAO,CAAC,CAAC;IACjC,IAAI,CAACS,WAAW,GAAG,IAAI,CAACD,YAAY,CAACD,KAAK;IAC1C,IAAI,CAACG,aAAa,GAAG,IAAIR,eAAe,CAAC,CAAC;IAC1C,IAAI,CAACS,KAAK,GAAG,IAAId,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;IAChC,IAAI,CAACe,QAAQ,GAAG,IAAIf,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;IACnC,IAAI,CAACgB,QAAQ,GAAG,IAAIhB,SAAS,CAACiB,MAAM,CAACC,gBAAgB,EAAED,MAAM,CAACC,gBAAgB,CAAC;IAC/E,IAAI,CAACC,OAAO,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC5C,IAAI,CAACC,SAAS,GAAG,IAAIpB,IAAI,CAAC,IAAI,CAACiB,OAAO,EAAE;MAAEI,mBAAmB,EAAEA,CAAA,KAAM,IAAI,CAACT,KAAK,CAACU;IAAM,CAAC,EAAE;MAAEC,WAAW,EAAE,CAAC,CAAC;IAA2B,CAAC,CAAC;IACvI,IAAI,CAACC,SAAS,GAAG,IAAIxB,IAAI,CAAC,IAAI,CAACiB,OAAO,EAAE;MAAEI,mBAAmB,EAAEA,CAAA,KAAM;IAAE,CAAC,EAAE;MAAEE,WAAW,EAAE,CAAC,CAAC;IAA2B,CAAC,CAAC;IACxH,IAAI,CAACE,UAAU,GAAG,IAAIzB,IAAI,CAAC,IAAI,CAACiB,OAAO,EAAE;MAAES,oBAAoB,EAAEA,CAAA,KAAM;IAAE,CAAC,EAAE;MAAEH,WAAW,EAAE,CAAC,CAAC;MAA8BI,cAAc,EAAE5B,cAAc,CAAC6B;IAAM,CAAC,CAAC;IAClK,IAAI,CAACC,UAAU,GAAG,IAAI7B,IAAI,CAAC,IAAI,CAACiB,OAAO,EAAE;MAAES,oBAAoB,EAAEA,CAAA,KAAM,IAAI,CAACd,KAAK,CAACkB;IAAO,CAAC,EAAE;MAAEP,WAAW,EAAE,CAAC,CAAC;MAA8BI,cAAc,EAAE5B,cAAc,CAACgC;IAAM,CAAC,CAAC;IAClL,IAAI,CAACN,UAAU,CAACO,mBAAmB,GAAG,IAAI,CAACR,SAAS;IACpD,IAAI,CAACC,UAAU,CAACQ,iBAAiB,GAAG,IAAI,CAACb,SAAS;IAClD,IAAI,CAACS,UAAU,CAACG,mBAAmB,GAAG,IAAI,CAACR,SAAS;IACpD,IAAI,CAACK,UAAU,CAACI,iBAAiB,GAAG,IAAI,CAACb,SAAS;IAClD,IAAIc,WAAW;IACf,IAAIC,MAAM,GAAG,CAAC;IACd,IAAIC,MAAM,GAAG,CAAC;IACd,IAAI,CAACzB,aAAa,CAAC0B,GAAG,CAACnC,KAAK,CAACoC,GAAG,CAAC,IAAI,CAACb,UAAU,CAACc,UAAU,EAAE,IAAI,CAACnB,SAAS,CAACmB,UAAU,EAAE,IAAI,CAACV,UAAU,CAACU,UAAU,EAAE,IAAI,CAACf,SAAS,CAACe,UAAU,CAAC,CAAC,MAAM;MACjJ,IAAIL,WAAW,KAAKM,SAAS,EAAE;QAC3B,IAAI,CAAClC,gBAAgB,CAACmC,IAAI,CAAC,CAAC;QAC5BP,WAAW,GAAG,IAAI,CAACtB,KAAK;QACxBuB,MAAM,GAAG,CAAC;QACVC,MAAM,GAAG,CAAC;MACd;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,CAACzB,aAAa,CAAC0B,GAAG,CAACnC,KAAK,CAACoC,GAAG,CAAC,IAAI,CAACb,UAAU,CAACiB,QAAQ,EAAE,IAAI,CAACtB,SAAS,CAACsB,QAAQ,EAAE,IAAI,CAACb,UAAU,CAACa,QAAQ,EAAE,IAAI,CAAClB,SAAS,CAACkB,QAAQ,CAAC,CAAC,MAAM;MACzI,IAAIR,WAAW,KAAKM,SAAS,EAAE;QAC3BN,WAAW,GAAGM,SAAS;QACvBL,MAAM,GAAG,CAAC;QACVC,MAAM,GAAG,CAAC;QACV,IAAI,CAAC3B,YAAY,CAACgC,IAAI,CAAC;UAAEE,SAAS,EAAE,IAAI,CAAC/B,KAAK;UAAEgC,IAAI,EAAE;QAAK,CAAC,CAAC;MACjE;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,CAACjC,aAAa,CAAC0B,GAAG,CAAC,IAAI,CAACjB,SAAS,CAACyB,WAAW,CAACC,CAAC,IAAI;MACnD,IAAIZ,WAAW,EAAE;QACbE,MAAM,GAAGU,CAAC,CAACC,QAAQ,GAAGD,CAAC,CAACE,MAAM;QAC9B,IAAI,CAACC,MAAM,CAACf,WAAW,CAACJ,MAAM,GAAGK,MAAM,EAAED,WAAW,CAACZ,KAAK,GAAGc,MAAM,CAAC;QACpE,IAAI,CAAC3B,YAAY,CAACgC,IAAI,CAAC;UAAEE,SAAS,EAAE,IAAI,CAAC/B,KAAK;UAAEgC,IAAI,EAAE,KAAK;UAAEM,IAAI,EAAE;QAAK,CAAC,CAAC;MAC9E;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,CAACvC,aAAa,CAAC0B,GAAG,CAAC,IAAI,CAACb,SAAS,CAACqB,WAAW,CAACC,CAAC,IAAI;MACnD,IAAIZ,WAAW,EAAE;QACbE,MAAM,GAAG,EAAEU,CAAC,CAACC,QAAQ,GAAGD,CAAC,CAACE,MAAM,CAAC;QACjC,IAAI,CAACC,MAAM,CAACf,WAAW,CAACJ,MAAM,GAAGK,MAAM,EAAED,WAAW,CAACZ,KAAK,GAAGc,MAAM,CAAC;QACpE,IAAI,CAAC3B,YAAY,CAACgC,IAAI,CAAC;UAAEE,SAAS,EAAE,IAAI,CAAC/B,KAAK;UAAEgC,IAAI,EAAE,KAAK;UAAEO,IAAI,EAAE;QAAK,CAAC,CAAC;MAC9E;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,CAACxC,aAAa,CAAC0B,GAAG,CAAC,IAAI,CAACZ,UAAU,CAACoB,WAAW,CAACC,CAAC,IAAI;MACpD,IAAIZ,WAAW,EAAE;QACbC,MAAM,GAAG,EAAEW,CAAC,CAACM,QAAQ,GAAGN,CAAC,CAACO,MAAM,CAAC;QACjC,IAAI,CAACJ,MAAM,CAACf,WAAW,CAACJ,MAAM,GAAGK,MAAM,EAAED,WAAW,CAACZ,KAAK,GAAGc,MAAM,CAAC;QACpE,IAAI,CAAC3B,YAAY,CAACgC,IAAI,CAAC;UAAEE,SAAS,EAAE,IAAI,CAAC/B,KAAK;UAAEgC,IAAI,EAAE,KAAK;UAAEU,KAAK,EAAE;QAAK,CAAC,CAAC;MAC/E;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,CAAC3C,aAAa,CAAC0B,GAAG,CAAC,IAAI,CAACR,UAAU,CAACgB,WAAW,CAACC,CAAC,IAAI;MACpD,IAAIZ,WAAW,EAAE;QACbC,MAAM,GAAGW,CAAC,CAACM,QAAQ,GAAGN,CAAC,CAACO,MAAM;QAC9B,IAAI,CAACJ,MAAM,CAACf,WAAW,CAACJ,MAAM,GAAGK,MAAM,EAAED,WAAW,CAACZ,KAAK,GAAGc,MAAM,CAAC;QACpE,IAAI,CAAC3B,YAAY,CAACgC,IAAI,CAAC;UAAEE,SAAS,EAAE,IAAI,CAAC/B,KAAK;UAAEgC,IAAI,EAAE,KAAK;UAAEW,KAAK,EAAE;QAAK,CAAC,CAAC;MAC/E;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,CAAC5C,aAAa,CAAC0B,GAAG,CAACnC,KAAK,CAACoC,GAAG,CAAC,IAAI,CAAClB,SAAS,CAACoC,UAAU,EAAE,IAAI,CAAChC,SAAS,CAACgC,UAAU,CAAC,CAACV,CAAC,IAAI;MACxF,IAAI,IAAI,CAACW,cAAc,EAAE;QACrB,IAAI,CAACR,MAAM,CAAC,IAAI,CAACrC,KAAK,CAACkB,MAAM,EAAE,IAAI,CAAC2B,cAAc,CAACnC,KAAK,CAAC;QACzD,IAAI,CAACb,YAAY,CAACgC,IAAI,CAAC;UAAEE,SAAS,EAAE,IAAI,CAAC/B,KAAK;UAAEgC,IAAI,EAAE;QAAK,CAAC,CAAC;MACjE;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,CAACjC,aAAa,CAAC0B,GAAG,CAACnC,KAAK,CAACoC,GAAG,CAAC,IAAI,CAACb,UAAU,CAAC+B,UAAU,EAAE,IAAI,CAAC3B,UAAU,CAAC2B,UAAU,CAAC,CAACV,CAAC,IAAI;MAC1F,IAAI,IAAI,CAACW,cAAc,EAAE;QACrB,IAAI,CAACR,MAAM,CAAC,IAAI,CAACQ,cAAc,CAAC3B,MAAM,EAAE,IAAI,CAAClB,KAAK,CAACU,KAAK,CAAC;QACzD,IAAI,CAACb,YAAY,CAACgC,IAAI,CAAC;UAAEE,SAAS,EAAE,IAAI,CAAC/B,KAAK;UAAEgC,IAAI,EAAE;QAAK,CAAC,CAAC;MACjE;IACJ,CAAC,CAAC,CAAC;EACP;EACAc,OAAOA,CAAA,EAAG;IACN,IAAI,CAACjC,UAAU,CAACiC,OAAO,CAAC,CAAC;IACzB,IAAI,CAAC7B,UAAU,CAAC6B,OAAO,CAAC,CAAC;IACzB,IAAI,CAACtC,SAAS,CAACsC,OAAO,CAAC,CAAC;IACxB,IAAI,CAAClC,SAAS,CAACkC,OAAO,CAAC,CAAC;IACxB,IAAI,CAAC/C,aAAa,CAAC+C,OAAO,CAAC,CAAC;IAC5B,IAAI,CAACjD,YAAY,CAACiD,OAAO,CAAC,CAAC;IAC3B,IAAI,CAACpD,gBAAgB,CAACoD,OAAO,CAAC,CAAC;IAC/B,IAAI,CAACzC,OAAO,CAAC0C,MAAM,CAAC,CAAC;EACzB;EACAC,YAAYA,CAACN,KAAK,EAAEJ,IAAI,EAAEK,KAAK,EAAEJ,IAAI,EAAE;IACnC,IAAI,CAAC1B,UAAU,CAACoC,KAAK,GAAGP,KAAK,GAAG,CAAC,CAAC,0BAA0B,CAAC,CAAC;IAC9D,IAAI,CAAClC,SAAS,CAACyC,KAAK,GAAGX,IAAI,GAAG,CAAC,CAAC,0BAA0B,CAAC,CAAC;IAC5D,IAAI,CAACrB,UAAU,CAACgC,KAAK,GAAGN,KAAK,GAAG,CAAC,CAAC,0BAA0B,CAAC,CAAC;IAC9D,IAAI,CAAC/B,SAAS,CAACqC,KAAK,GAAGV,IAAI,GAAG,CAAC,CAAC,0BAA0B,CAAC,CAAC;EAChE;EACAF,MAAMA,CAACnB,MAAM,GAAG,IAAI,CAACgC,IAAI,CAAChC,MAAM,EAAER,KAAK,GAAG,IAAI,CAACwC,IAAI,CAACxC,KAAK,EAAE;IACvD,MAAM;MAAEQ,MAAM,EAAEiC,SAAS;MAAEzC,KAAK,EAAE0C;IAAS,CAAC,GAAG,IAAI,CAACnD,QAAQ;IAC5D,MAAM;MAAEiB,MAAM,EAAEmC,SAAS;MAAE3C,KAAK,EAAE4C;IAAS,CAAC,GAAG,IAAI,CAACpD,QAAQ;IAC5DgB,MAAM,GAAGqC,IAAI,CAACC,GAAG,CAACL,SAAS,EAAEI,IAAI,CAACE,GAAG,CAACJ,SAAS,EAAEnC,MAAM,CAAC,CAAC;IACzDR,KAAK,GAAG6C,IAAI,CAACC,GAAG,CAACJ,QAAQ,EAAEG,IAAI,CAACE,GAAG,CAACH,QAAQ,EAAE5C,KAAK,CAAC,CAAC;IACrD,MAAMgD,OAAO,GAAG,IAAIxE,SAAS,CAACwB,KAAK,EAAEQ,MAAM,CAAC;IAC5C,IAAI,CAAChC,SAAS,CAACyE,MAAM,CAACD,OAAO,EAAE,IAAI,CAAC1D,KAAK,CAAC,EAAE;MACxC,IAAI,CAACK,OAAO,CAACuD,KAAK,CAAC1C,MAAM,GAAGA,MAAM,GAAG,IAAI;MACzC,IAAI,CAACb,OAAO,CAACuD,KAAK,CAAClD,KAAK,GAAGA,KAAK,GAAG,IAAI;MACvC,IAAI,CAACV,KAAK,GAAG0D,OAAO;MACpB,IAAI,CAAC7C,UAAU,CAACwB,MAAM,CAAC,CAAC;MACxB,IAAI,CAAC7B,SAAS,CAAC6B,MAAM,CAAC,CAAC;MACvB,IAAI,CAACpB,UAAU,CAACoB,MAAM,CAAC,CAAC;MACxB,IAAI,CAACzB,SAAS,CAACyB,MAAM,CAAC,CAAC;IAC3B;EACJ;EACAwB,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAACrD,SAAS,CAACqD,mBAAmB,CAAC,CAAC;IACpC,IAAI,CAACjD,SAAS,CAACiD,mBAAmB,CAAC,CAAC;IACpC,IAAI,CAAChD,UAAU,CAACgD,mBAAmB,CAAC,CAAC;IACrC,IAAI,CAAC5C,UAAU,CAAC4C,mBAAmB,CAAC,CAAC;EACzC;EACA,IAAIX,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAAClD,KAAK;EACrB;EACA,IAAI8D,OAAOA,CAACC,KAAK,EAAE;IACf,IAAI,CAAC7D,QAAQ,GAAG6D,KAAK;EACzB;EACA,IAAID,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC5D,QAAQ;EACxB;EACA,IAAI8D,OAAOA,CAACD,KAAK,EAAE;IACf,IAAI,CAAC9D,QAAQ,GAAG8D,KAAK;EACzB;EACA,IAAIC,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC/D,QAAQ;EACxB;EACA,IAAIgE,aAAaA,CAACF,KAAK,EAAE;IACrB,IAAI,CAAClB,cAAc,GAAGkB,KAAK;EAC/B;EACA,IAAIE,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACpB,cAAc;EAC9B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}