{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { LanguageAgnosticBracketTokens } from './bracketPairsTree/brackets.js';\nimport { lengthAdd, lengthGetColumnCountIfZeroLineCount, lengthZero } from './bracketPairsTree/length.js';\nimport { parseDocument } from './bracketPairsTree/parser.js';\nimport { DenseKeyProvider } from './bracketPairsTree/smallImmutableSet.js';\nimport { TextBufferTokenizer } from './bracketPairsTree/tokenizer.js';\nexport function fixBracketsInLine(tokens, languageConfigurationService) {\n  const denseKeyProvider = new DenseKeyProvider();\n  const bracketTokens = new LanguageAgnosticBracketTokens(denseKeyProvider, languageId => languageConfigurationService.getLanguageConfiguration(languageId));\n  const tokenizer = new TextBufferTokenizer(new StaticTokenizerSource([tokens]), bracketTokens);\n  const node = parseDocument(tokenizer, [], undefined, true);\n  let str = '';\n  const line = tokens.getLineContent();\n  function processNode(node, offset) {\n    if (node.kind === 2 /* AstNodeKind.Pair */) {\n      processNode(node.openingBracket, offset);\n      offset = lengthAdd(offset, node.openingBracket.length);\n      if (node.child) {\n        processNode(node.child, offset);\n        offset = lengthAdd(offset, node.child.length);\n      }\n      if (node.closingBracket) {\n        processNode(node.closingBracket, offset);\n        offset = lengthAdd(offset, node.closingBracket.length);\n      } else {\n        const singleLangBracketTokens = bracketTokens.getSingleLanguageBracketTokens(node.openingBracket.languageId);\n        const closingTokenText = singleLangBracketTokens.findClosingTokenText(node.openingBracket.bracketIds);\n        str += closingTokenText;\n      }\n    } else if (node.kind === 3 /* AstNodeKind.UnexpectedClosingBracket */) {\n      // remove the bracket\n    } else if (node.kind === 0 /* AstNodeKind.Text */ || node.kind === 1 /* AstNodeKind.Bracket */) {\n      str += line.substring(lengthGetColumnCountIfZeroLineCount(offset), lengthGetColumnCountIfZeroLineCount(lengthAdd(offset, node.length)));\n    } else if (node.kind === 4 /* AstNodeKind.List */) {\n      for (const child of node.children) {\n        processNode(child, offset);\n        offset = lengthAdd(offset, child.length);\n      }\n    }\n  }\n  processNode(node, lengthZero);\n  return str;\n}\nclass StaticTokenizerSource {\n  constructor(lines) {\n    this.lines = lines;\n    this.tokenization = {\n      getLineTokens: lineNumber => {\n        return this.lines[lineNumber - 1];\n      }\n    };\n  }\n  getLineCount() {\n    return this.lines.length;\n  }\n  getLineLength(lineNumber) {\n    return this.lines[lineNumber - 1].getLineContent().length;\n  }\n}", "map": {"version": 3, "names": ["LanguageAgnosticBracketTokens", "lengthAdd", "lengthGetColumnCountIfZeroLineCount", "lengthZero", "parseDocument", "<PERSON><PERSON><PERSON><PERSON>", "TextBufferTokenizer", "fixBracketsInLine", "tokens", "languageConfigurationService", "denseKeyProvider", "bracketTokens", "languageId", "getLanguageConfiguration", "tokenizer", "StaticTokenizerSource", "node", "undefined", "str", "line", "get<PERSON>ineC<PERSON>nt", "processNode", "offset", "kind", "openingBracket", "length", "child", "closingBracket", "singleLangBracketTokens", "getSingleLanguageBracketTokens", "closingTokenText", "findClosingTokenText", "bracketIds", "substring", "children", "constructor", "lines", "tokenization", "getLineTokens", "lineNumber", "getLineCount", "getLine<PERSON><PERSON>th"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/model/bracketPairsTextModelPart/fixBrackets.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { LanguageAgnosticBracketTokens } from './bracketPairsTree/brackets.js';\nimport { lengthAdd, lengthGetColumnCountIfZeroLineCount, lengthZero } from './bracketPairsTree/length.js';\nimport { parseDocument } from './bracketPairsTree/parser.js';\nimport { DenseKeyProvider } from './bracketPairsTree/smallImmutableSet.js';\nimport { TextBufferTokenizer } from './bracketPairsTree/tokenizer.js';\nexport function fixBracketsInLine(tokens, languageConfigurationService) {\n    const denseKeyProvider = new DenseKeyProvider();\n    const bracketTokens = new LanguageAgnosticBracketTokens(denseKeyProvider, (languageId) => languageConfigurationService.getLanguageConfiguration(languageId));\n    const tokenizer = new TextBufferTokenizer(new StaticTokenizerSource([tokens]), bracketTokens);\n    const node = parseDocument(tokenizer, [], undefined, true);\n    let str = '';\n    const line = tokens.getLineContent();\n    function processNode(node, offset) {\n        if (node.kind === 2 /* AstNodeKind.Pair */) {\n            processNode(node.openingBracket, offset);\n            offset = lengthAdd(offset, node.openingBracket.length);\n            if (node.child) {\n                processNode(node.child, offset);\n                offset = lengthAdd(offset, node.child.length);\n            }\n            if (node.closingBracket) {\n                processNode(node.closingBracket, offset);\n                offset = lengthAdd(offset, node.closingBracket.length);\n            }\n            else {\n                const singleLangBracketTokens = bracketTokens.getSingleLanguageBracketTokens(node.openingBracket.languageId);\n                const closingTokenText = singleLangBracketTokens.findClosingTokenText(node.openingBracket.bracketIds);\n                str += closingTokenText;\n            }\n        }\n        else if (node.kind === 3 /* AstNodeKind.UnexpectedClosingBracket */) {\n            // remove the bracket\n        }\n        else if (node.kind === 0 /* AstNodeKind.Text */ || node.kind === 1 /* AstNodeKind.Bracket */) {\n            str += line.substring(lengthGetColumnCountIfZeroLineCount(offset), lengthGetColumnCountIfZeroLineCount(lengthAdd(offset, node.length)));\n        }\n        else if (node.kind === 4 /* AstNodeKind.List */) {\n            for (const child of node.children) {\n                processNode(child, offset);\n                offset = lengthAdd(offset, child.length);\n            }\n        }\n    }\n    processNode(node, lengthZero);\n    return str;\n}\nclass StaticTokenizerSource {\n    constructor(lines) {\n        this.lines = lines;\n        this.tokenization = {\n            getLineTokens: (lineNumber) => {\n                return this.lines[lineNumber - 1];\n            }\n        };\n    }\n    getLineCount() {\n        return this.lines.length;\n    }\n    getLineLength(lineNumber) {\n        return this.lines[lineNumber - 1].getLineContent().length;\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,6BAA6B,QAAQ,gCAAgC;AAC9E,SAASC,SAAS,EAAEC,mCAAmC,EAAEC,UAAU,QAAQ,8BAA8B;AACzG,SAASC,aAAa,QAAQ,8BAA8B;AAC5D,SAASC,gBAAgB,QAAQ,yCAAyC;AAC1E,SAASC,mBAAmB,QAAQ,iCAAiC;AACrE,OAAO,SAASC,iBAAiBA,CAACC,MAAM,EAAEC,4BAA4B,EAAE;EACpE,MAAMC,gBAAgB,GAAG,IAAIL,gBAAgB,CAAC,CAAC;EAC/C,MAAMM,aAAa,GAAG,IAAIX,6BAA6B,CAACU,gBAAgB,EAAGE,UAAU,IAAKH,4BAA4B,CAACI,wBAAwB,CAACD,UAAU,CAAC,CAAC;EAC5J,MAAME,SAAS,GAAG,IAAIR,mBAAmB,CAAC,IAAIS,qBAAqB,CAAC,CAACP,MAAM,CAAC,CAAC,EAAEG,aAAa,CAAC;EAC7F,MAAMK,IAAI,GAAGZ,aAAa,CAACU,SAAS,EAAE,EAAE,EAAEG,SAAS,EAAE,IAAI,CAAC;EAC1D,IAAIC,GAAG,GAAG,EAAE;EACZ,MAAMC,IAAI,GAAGX,MAAM,CAACY,cAAc,CAAC,CAAC;EACpC,SAASC,WAAWA,CAACL,IAAI,EAAEM,MAAM,EAAE;IAC/B,IAAIN,IAAI,CAACO,IAAI,KAAK,CAAC,CAAC,wBAAwB;MACxCF,WAAW,CAACL,IAAI,CAACQ,cAAc,EAAEF,MAAM,CAAC;MACxCA,MAAM,GAAGrB,SAAS,CAACqB,MAAM,EAAEN,IAAI,CAACQ,cAAc,CAACC,MAAM,CAAC;MACtD,IAAIT,IAAI,CAACU,KAAK,EAAE;QACZL,WAAW,CAACL,IAAI,CAACU,KAAK,EAAEJ,MAAM,CAAC;QAC/BA,MAAM,GAAGrB,SAAS,CAACqB,MAAM,EAAEN,IAAI,CAACU,KAAK,CAACD,MAAM,CAAC;MACjD;MACA,IAAIT,IAAI,CAACW,cAAc,EAAE;QACrBN,WAAW,CAACL,IAAI,CAACW,cAAc,EAAEL,MAAM,CAAC;QACxCA,MAAM,GAAGrB,SAAS,CAACqB,MAAM,EAAEN,IAAI,CAACW,cAAc,CAACF,MAAM,CAAC;MAC1D,CAAC,MACI;QACD,MAAMG,uBAAuB,GAAGjB,aAAa,CAACkB,8BAA8B,CAACb,IAAI,CAACQ,cAAc,CAACZ,UAAU,CAAC;QAC5G,MAAMkB,gBAAgB,GAAGF,uBAAuB,CAACG,oBAAoB,CAACf,IAAI,CAACQ,cAAc,CAACQ,UAAU,CAAC;QACrGd,GAAG,IAAIY,gBAAgB;MAC3B;IACJ,CAAC,MACI,IAAId,IAAI,CAACO,IAAI,KAAK,CAAC,CAAC,4CAA4C;MACjE;IAAA,CACH,MACI,IAAIP,IAAI,CAACO,IAAI,KAAK,CAAC,CAAC,0BAA0BP,IAAI,CAACO,IAAI,KAAK,CAAC,CAAC,2BAA2B;MAC1FL,GAAG,IAAIC,IAAI,CAACc,SAAS,CAAC/B,mCAAmC,CAACoB,MAAM,CAAC,EAAEpB,mCAAmC,CAACD,SAAS,CAACqB,MAAM,EAAEN,IAAI,CAACS,MAAM,CAAC,CAAC,CAAC;IAC3I,CAAC,MACI,IAAIT,IAAI,CAACO,IAAI,KAAK,CAAC,CAAC,wBAAwB;MAC7C,KAAK,MAAMG,KAAK,IAAIV,IAAI,CAACkB,QAAQ,EAAE;QAC/Bb,WAAW,CAACK,KAAK,EAAEJ,MAAM,CAAC;QAC1BA,MAAM,GAAGrB,SAAS,CAACqB,MAAM,EAAEI,KAAK,CAACD,MAAM,CAAC;MAC5C;IACJ;EACJ;EACAJ,WAAW,CAACL,IAAI,EAAEb,UAAU,CAAC;EAC7B,OAAOe,GAAG;AACd;AACA,MAAMH,qBAAqB,CAAC;EACxBoB,WAAWA,CAACC,KAAK,EAAE;IACf,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,YAAY,GAAG;MAChBC,aAAa,EAAGC,UAAU,IAAK;QAC3B,OAAO,IAAI,CAACH,KAAK,CAACG,UAAU,GAAG,CAAC,CAAC;MACrC;IACJ,CAAC;EACL;EACAC,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAACJ,KAAK,CAACX,MAAM;EAC5B;EACAgB,aAAaA,CAACF,UAAU,EAAE;IACtB,OAAO,IAAI,CAACH,KAAK,CAACG,UAAU,GAAG,CAAC,CAAC,CAACnB,cAAc,CAAC,CAAC,CAACK,MAAM;EAC7D;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}