<div class="workflow-execution-container">
  <!-- SVG Gradient Definitions for Icons -->
  <svg width="0" height="0" style="position: absolute">
    <defs>
      <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
        <stop offset="0%" stop-color="#6566CD" />
        <stop offset="100%" stop-color="#F96CAB" />
      </linearGradient>
    </defs>
  </svg>
  <div class="execution-content" role="main">
    <!-- Left Panel: Workflow Playground with Execute Button -->
    <div class="left-panel" [class.collapsed]="isPlaygroundCollapsed" role="region" aria-label="Workflow Input Panel">
      <app-workflow-playground
        [agents]="agents"
        [isCollapsed]="isPlaygroundCollapsed"
        [workflowName]="workflowName"
        (backClicked)="onPlaygroundBackClicked()"
        (collapseToggled)="onPlaygroundCollapseToggled($event)"
        (agentInputChanged)="onAgentInputChanged($event)"
        (agentFileSelected)="onAgentFileSelected($event)"
        (messageSent)="onMessageSent($event)"
        class="playground-component"
        role="region"
        aria-label="Workflow Playground">
      </app-workflow-playground>

      <!-- Execute Button -->
      <div class="execute-button-container" *ngIf="!isPlaygroundCollapsed">
        <ava-button
          label="Execute Workflow"
          variant="primary"
          size="large"
          [customStyles]="{
            width: '100%',}"
          (click)="executeWorkflow()"
        >
        </ava-button>
      </div>
    </div>

    <!-- Right Panel: Three Sections (Execution, Output, Configuration) -->
    <div class="right-panel" [class.expanded]="isPlaygroundCollapsed" role="region" aria-label="Workflow Results">
      <!-- Tab Navigation -->
      <div class="tabs-header">
        <div class="tabs-container">
          <ava-tabs
            [tabs]="navigationTabs"
            [activeTabId]="activeTabId"
            variant="button"
            [showContentPanels]="false"
            (tabChange)="onTabChange($event)"
            ariaLabel="Workflow sections navigation"
          ></ava-tabs>
        </div>
        <div class="header-actions">
          <ava-button
            label="Send for Approval"
            variant="primary"
            [customStyles]="{
              background: 'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',
              '--button-effect-color': '33, 90, 214',
              'border-radius': '8px',
              'box-shadow': 'none'
            }"
            size="medium"
          >
          </ava-button>
        </div>
      </div>

      <!-- Content Sections -->
      <div class="content-sections">
        <!-- Execution Section -->
        <div *ngIf="activeTabId === 'nav-home'" class="section-content execution-section">
          <!-- Execution Monitor Header -->
          <div class="execution-monitor-header">
            <h2 class="monitor-title">Execution Monitor</h2>
            <div class="progress-section">
              <span class="progress-label">Overall Progress</span>
              <div class="progress-container">
                <div class="progress-bar">
                  <div class="progress-fill" [style.width.%]="progress"></div>
                </div>
                <span class="progress-text">{{ progress }}%</span>
              </div>
            </div>
          </div>

          <!-- Two Column Layout: Pipeline Steps + Execution Logs -->
          <div class="execution-content-grid">
            <!-- Left Column: Pipeline Steps -->
            <div class="pipeline-steps-section">
              <h3>Pipeline Steps</h3>
              <div class="pipeline-agents-list">
                <div
                  *ngFor="let agent of agents; let i = index"
                  class="pipeline-agent-item"
                  [class.active]="i === 0"
                  [class.completed]="false">
                  <div class="agent-icon">
                    <ava-icon iconName="bot" [iconSize]="20" iconColor="#666"></ava-icon>
                  </div>
                  <div class="agent-info">
                    <span class="agent-name">{{ agent.name }}</span>
                    <span class="agent-status" *ngIf="i === 0">Agent 1 is currently working</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Right Column: Execution Logs -->
            <div class="execution-logs-section">
              <div class="logs-header">
                <h3>Execution Logs</h3>
                <span class="logs-subtitle">Agent 1's Output</span>
              </div>
              <div class="logs-content">
                <div class="log-entry" *ngFor="let log of displayedLogs; let i = index">
                  <span class="log-number">{{ (i + 1 < 10 ? '0' : '') + (i + 1) }}</span>
                  <span class="log-message" [style.color]="log.color">{{ log.content }}</span>
                </div>
                <div class="show-more" *ngIf="hasMoreLogs">
                  <button class="show-more-btn" (click)="toggleShowAllLogs()">
                    {{ showAllLogs ? 'Show Less' : 'Show More (' + (workflowLogs.length - displayedLogsCount) + ' more)' }}
                  </button>
                </div>
                <div class="show-less" *ngIf="showAllLogs && workflowLogs.length > displayedLogsCount">
                  <button class="show-more-btn" (click)="toggleShowAllLogs()">Show Less</button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Output Section -->
        <div *ngIf="activeTabId === 'nav-products'" class="section-content output-section">
          <app-agent-output
            [outputs]="taskMessage"
            (export)="exportResults('output')"
          ></app-agent-output>
        </div>

        <!-- Configuration Section -->
        <div *ngIf="activeTabId === 'nav-services'" class="section-content configuration-section">
          <div class="configuration-content">
            <p>Configuration settings will be displayed here.</p>
            <!-- Add configuration content as needed -->
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
