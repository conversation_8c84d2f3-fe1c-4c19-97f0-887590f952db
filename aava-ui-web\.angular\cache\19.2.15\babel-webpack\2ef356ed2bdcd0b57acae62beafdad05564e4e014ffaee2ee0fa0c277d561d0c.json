{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { BugIndicatingError } from '../../../../../base/common/errors.js';\nimport { CursorColumns } from '../../../core/cursorColumns.js';\nimport { lengthAdd, lengthGetLineCount, lengthToObj, lengthZero } from './length.js';\nimport { SmallImmutableSet } from './smallImmutableSet.js';\n/**\n * The base implementation for all AST nodes.\n*/\nclass BaseAstNode {\n  /**\n   * The length of the entire node, which should equal the sum of lengths of all children.\n  */\n  get length() {\n    return this._length;\n  }\n  constructor(length) {\n    this._length = length;\n  }\n}\n/**\n * Represents a bracket pair including its child (e.g. `{ ... }`).\n * Might be unclosed.\n * Immutable, if all children are immutable.\n*/\nexport class PairAstNode extends BaseAstNode {\n  static create(openingBracket, child, closingBracket) {\n    let length = openingBracket.length;\n    if (child) {\n      length = lengthAdd(length, child.length);\n    }\n    if (closingBracket) {\n      length = lengthAdd(length, closingBracket.length);\n    }\n    return new PairAstNode(length, openingBracket, child, closingBracket, child ? child.missingOpeningBracketIds : SmallImmutableSet.getEmpty());\n  }\n  get kind() {\n    return 2 /* AstNodeKind.Pair */;\n  }\n  get listHeight() {\n    return 0;\n  }\n  get childrenLength() {\n    return 3;\n  }\n  getChild(idx) {\n    switch (idx) {\n      case 0:\n        return this.openingBracket;\n      case 1:\n        return this.child;\n      case 2:\n        return this.closingBracket;\n    }\n    throw new Error('Invalid child index');\n  }\n  /**\n   * Avoid using this property, it allocates an array!\n  */\n  get children() {\n    const result = [];\n    result.push(this.openingBracket);\n    if (this.child) {\n      result.push(this.child);\n    }\n    if (this.closingBracket) {\n      result.push(this.closingBracket);\n    }\n    return result;\n  }\n  constructor(length, openingBracket, child, closingBracket, missingOpeningBracketIds) {\n    super(length);\n    this.openingBracket = openingBracket;\n    this.child = child;\n    this.closingBracket = closingBracket;\n    this.missingOpeningBracketIds = missingOpeningBracketIds;\n  }\n  canBeReused(openBracketIds) {\n    if (this.closingBracket === null) {\n      // Unclosed pair ast nodes only\n      // end at the end of the document\n      // or when a parent node is closed.\n      // This could be improved:\n      // Only return false if some next token is neither \"undefined\" nor a bracket that closes a parent.\n      return false;\n    }\n    if (openBracketIds.intersects(this.missingOpeningBracketIds)) {\n      return false;\n    }\n    return true;\n  }\n  deepClone() {\n    return new PairAstNode(this.length, this.openingBracket.deepClone(), this.child && this.child.deepClone(), this.closingBracket && this.closingBracket.deepClone(), this.missingOpeningBracketIds);\n  }\n  computeMinIndentation(offset, textModel) {\n    return this.child ? this.child.computeMinIndentation(lengthAdd(offset, this.openingBracket.length), textModel) : Number.MAX_SAFE_INTEGER;\n  }\n}\nexport class ListAstNode extends BaseAstNode {\n  /**\n   * This method uses more memory-efficient list nodes that can only store 2 or 3 children.\n  */\n  static create23(item1, item2, item3, immutable = false) {\n    let length = item1.length;\n    let missingBracketIds = item1.missingOpeningBracketIds;\n    if (item1.listHeight !== item2.listHeight) {\n      throw new Error('Invalid list heights');\n    }\n    length = lengthAdd(length, item2.length);\n    missingBracketIds = missingBracketIds.merge(item2.missingOpeningBracketIds);\n    if (item3) {\n      if (item1.listHeight !== item3.listHeight) {\n        throw new Error('Invalid list heights');\n      }\n      length = lengthAdd(length, item3.length);\n      missingBracketIds = missingBracketIds.merge(item3.missingOpeningBracketIds);\n    }\n    return immutable ? new Immutable23ListAstNode(length, item1.listHeight + 1, item1, item2, item3, missingBracketIds) : new TwoThreeListAstNode(length, item1.listHeight + 1, item1, item2, item3, missingBracketIds);\n  }\n  static getEmpty() {\n    return new ImmutableArrayListAstNode(lengthZero, 0, [], SmallImmutableSet.getEmpty());\n  }\n  get kind() {\n    return 4 /* AstNodeKind.List */;\n  }\n  get missingOpeningBracketIds() {\n    return this._missingOpeningBracketIds;\n  }\n  /**\n   * Use ListAstNode.create.\n  */\n  constructor(length, listHeight, _missingOpeningBracketIds) {\n    super(length);\n    this.listHeight = listHeight;\n    this._missingOpeningBracketIds = _missingOpeningBracketIds;\n    this.cachedMinIndentation = -1;\n  }\n  throwIfImmutable() {\n    // NOOP\n  }\n  makeLastElementMutable() {\n    this.throwIfImmutable();\n    const childCount = this.childrenLength;\n    if (childCount === 0) {\n      return undefined;\n    }\n    const lastChild = this.getChild(childCount - 1);\n    const mutable = lastChild.kind === 4 /* AstNodeKind.List */ ? lastChild.toMutable() : lastChild;\n    if (lastChild !== mutable) {\n      this.setChild(childCount - 1, mutable);\n    }\n    return mutable;\n  }\n  makeFirstElementMutable() {\n    this.throwIfImmutable();\n    const childCount = this.childrenLength;\n    if (childCount === 0) {\n      return undefined;\n    }\n    const firstChild = this.getChild(0);\n    const mutable = firstChild.kind === 4 /* AstNodeKind.List */ ? firstChild.toMutable() : firstChild;\n    if (firstChild !== mutable) {\n      this.setChild(0, mutable);\n    }\n    return mutable;\n  }\n  canBeReused(openBracketIds) {\n    if (openBracketIds.intersects(this.missingOpeningBracketIds)) {\n      return false;\n    }\n    if (this.childrenLength === 0) {\n      // Don't reuse empty lists.\n      return false;\n    }\n    let lastChild = this;\n    while (lastChild.kind === 4 /* AstNodeKind.List */) {\n      const lastLength = lastChild.childrenLength;\n      if (lastLength === 0) {\n        // Empty lists should never be contained in other lists.\n        throw new BugIndicatingError();\n      }\n      lastChild = lastChild.getChild(lastLength - 1);\n    }\n    return lastChild.canBeReused(openBracketIds);\n  }\n  handleChildrenChanged() {\n    this.throwIfImmutable();\n    const count = this.childrenLength;\n    let length = this.getChild(0).length;\n    let unopenedBrackets = this.getChild(0).missingOpeningBracketIds;\n    for (let i = 1; i < count; i++) {\n      const child = this.getChild(i);\n      length = lengthAdd(length, child.length);\n      unopenedBrackets = unopenedBrackets.merge(child.missingOpeningBracketIds);\n    }\n    this._length = length;\n    this._missingOpeningBracketIds = unopenedBrackets;\n    this.cachedMinIndentation = -1;\n  }\n  computeMinIndentation(offset, textModel) {\n    if (this.cachedMinIndentation !== -1) {\n      return this.cachedMinIndentation;\n    }\n    let minIndentation = Number.MAX_SAFE_INTEGER;\n    let childOffset = offset;\n    for (let i = 0; i < this.childrenLength; i++) {\n      const child = this.getChild(i);\n      if (child) {\n        minIndentation = Math.min(minIndentation, child.computeMinIndentation(childOffset, textModel));\n        childOffset = lengthAdd(childOffset, child.length);\n      }\n    }\n    this.cachedMinIndentation = minIndentation;\n    return minIndentation;\n  }\n}\nclass TwoThreeListAstNode extends ListAstNode {\n  get childrenLength() {\n    return this._item3 !== null ? 3 : 2;\n  }\n  getChild(idx) {\n    switch (idx) {\n      case 0:\n        return this._item1;\n      case 1:\n        return this._item2;\n      case 2:\n        return this._item3;\n    }\n    throw new Error('Invalid child index');\n  }\n  setChild(idx, node) {\n    switch (idx) {\n      case 0:\n        this._item1 = node;\n        return;\n      case 1:\n        this._item2 = node;\n        return;\n      case 2:\n        this._item3 = node;\n        return;\n    }\n    throw new Error('Invalid child index');\n  }\n  get children() {\n    return this._item3 ? [this._item1, this._item2, this._item3] : [this._item1, this._item2];\n  }\n  get item1() {\n    return this._item1;\n  }\n  get item2() {\n    return this._item2;\n  }\n  get item3() {\n    return this._item3;\n  }\n  constructor(length, listHeight, _item1, _item2, _item3, missingOpeningBracketIds) {\n    super(length, listHeight, missingOpeningBracketIds);\n    this._item1 = _item1;\n    this._item2 = _item2;\n    this._item3 = _item3;\n  }\n  deepClone() {\n    return new TwoThreeListAstNode(this.length, this.listHeight, this._item1.deepClone(), this._item2.deepClone(), this._item3 ? this._item3.deepClone() : null, this.missingOpeningBracketIds);\n  }\n  appendChildOfSameHeight(node) {\n    if (this._item3) {\n      throw new Error('Cannot append to a full (2,3) tree node');\n    }\n    this.throwIfImmutable();\n    this._item3 = node;\n    this.handleChildrenChanged();\n  }\n  unappendChild() {\n    if (!this._item3) {\n      throw new Error('Cannot remove from a non-full (2,3) tree node');\n    }\n    this.throwIfImmutable();\n    const result = this._item3;\n    this._item3 = null;\n    this.handleChildrenChanged();\n    return result;\n  }\n  prependChildOfSameHeight(node) {\n    if (this._item3) {\n      throw new Error('Cannot prepend to a full (2,3) tree node');\n    }\n    this.throwIfImmutable();\n    this._item3 = this._item2;\n    this._item2 = this._item1;\n    this._item1 = node;\n    this.handleChildrenChanged();\n  }\n  unprependChild() {\n    if (!this._item3) {\n      throw new Error('Cannot remove from a non-full (2,3) tree node');\n    }\n    this.throwIfImmutable();\n    const result = this._item1;\n    this._item1 = this._item2;\n    this._item2 = this._item3;\n    this._item3 = null;\n    this.handleChildrenChanged();\n    return result;\n  }\n  toMutable() {\n    return this;\n  }\n}\n/**\n * Immutable, if all children are immutable.\n*/\nclass Immutable23ListAstNode extends TwoThreeListAstNode {\n  toMutable() {\n    return new TwoThreeListAstNode(this.length, this.listHeight, this.item1, this.item2, this.item3, this.missingOpeningBracketIds);\n  }\n  throwIfImmutable() {\n    throw new Error('this instance is immutable');\n  }\n}\n/**\n * For debugging.\n*/\nclass ArrayListAstNode extends ListAstNode {\n  get childrenLength() {\n    return this._children.length;\n  }\n  getChild(idx) {\n    return this._children[idx];\n  }\n  setChild(idx, child) {\n    this._children[idx] = child;\n  }\n  get children() {\n    return this._children;\n  }\n  constructor(length, listHeight, _children, missingOpeningBracketIds) {\n    super(length, listHeight, missingOpeningBracketIds);\n    this._children = _children;\n  }\n  deepClone() {\n    const children = new Array(this._children.length);\n    for (let i = 0; i < this._children.length; i++) {\n      children[i] = this._children[i].deepClone();\n    }\n    return new ArrayListAstNode(this.length, this.listHeight, children, this.missingOpeningBracketIds);\n  }\n  appendChildOfSameHeight(node) {\n    this.throwIfImmutable();\n    this._children.push(node);\n    this.handleChildrenChanged();\n  }\n  unappendChild() {\n    this.throwIfImmutable();\n    const item = this._children.pop();\n    this.handleChildrenChanged();\n    return item;\n  }\n  prependChildOfSameHeight(node) {\n    this.throwIfImmutable();\n    this._children.unshift(node);\n    this.handleChildrenChanged();\n  }\n  unprependChild() {\n    this.throwIfImmutable();\n    const item = this._children.shift();\n    this.handleChildrenChanged();\n    return item;\n  }\n  toMutable() {\n    return this;\n  }\n}\n/**\n * Immutable, if all children are immutable.\n*/\nclass ImmutableArrayListAstNode extends ArrayListAstNode {\n  toMutable() {\n    return new ArrayListAstNode(this.length, this.listHeight, [...this.children], this.missingOpeningBracketIds);\n  }\n  throwIfImmutable() {\n    throw new Error('this instance is immutable');\n  }\n}\nconst emptyArray = [];\nclass ImmutableLeafAstNode extends BaseAstNode {\n  get listHeight() {\n    return 0;\n  }\n  get childrenLength() {\n    return 0;\n  }\n  getChild(idx) {\n    return null;\n  }\n  get children() {\n    return emptyArray;\n  }\n  deepClone() {\n    return this;\n  }\n}\nexport class TextAstNode extends ImmutableLeafAstNode {\n  get kind() {\n    return 0 /* AstNodeKind.Text */;\n  }\n  get missingOpeningBracketIds() {\n    return SmallImmutableSet.getEmpty();\n  }\n  canBeReused(_openedBracketIds) {\n    return true;\n  }\n  computeMinIndentation(offset, textModel) {\n    const start = lengthToObj(offset);\n    // Text ast nodes don't have partial indentation (ensured by the tokenizer).\n    // Thus, if this text node does not start at column 0, the first line cannot have any indentation at all.\n    const startLineNumber = (start.columnCount === 0 ? start.lineCount : start.lineCount + 1) + 1;\n    const endLineNumber = lengthGetLineCount(lengthAdd(offset, this.length)) + 1;\n    let result = Number.MAX_SAFE_INTEGER;\n    for (let lineNumber = startLineNumber; lineNumber <= endLineNumber; lineNumber++) {\n      const firstNonWsColumn = textModel.getLineFirstNonWhitespaceColumn(lineNumber);\n      const lineContent = textModel.getLineContent(lineNumber);\n      if (firstNonWsColumn === 0) {\n        continue;\n      }\n      const visibleColumn = CursorColumns.visibleColumnFromColumn(lineContent, firstNonWsColumn, textModel.getOptions().tabSize);\n      result = Math.min(result, visibleColumn);\n    }\n    return result;\n  }\n}\nexport class BracketAstNode extends ImmutableLeafAstNode {\n  static create(length, bracketInfo, bracketIds) {\n    const node = new BracketAstNode(length, bracketInfo, bracketIds);\n    return node;\n  }\n  get kind() {\n    return 1 /* AstNodeKind.Bracket */;\n  }\n  get missingOpeningBracketIds() {\n    return SmallImmutableSet.getEmpty();\n  }\n  constructor(length, bracketInfo,\n  /**\n   * In case of a opening bracket, this is the id of the opening bracket.\n   * In case of a closing bracket, this contains the ids of all opening brackets it can close.\n  */\n  bracketIds) {\n    super(length);\n    this.bracketInfo = bracketInfo;\n    this.bracketIds = bracketIds;\n  }\n  get text() {\n    return this.bracketInfo.bracketText;\n  }\n  get languageId() {\n    return this.bracketInfo.languageId;\n  }\n  canBeReused(_openedBracketIds) {\n    // These nodes could be reused,\n    // but not in a general way.\n    // Their parent may be reused.\n    return false;\n  }\n  computeMinIndentation(offset, textModel) {\n    return Number.MAX_SAFE_INTEGER;\n  }\n}\nexport class InvalidBracketAstNode extends ImmutableLeafAstNode {\n  get kind() {\n    return 3 /* AstNodeKind.UnexpectedClosingBracket */;\n  }\n  constructor(closingBrackets, length) {\n    super(length);\n    this.missingOpeningBracketIds = closingBrackets;\n  }\n  canBeReused(openedBracketIds) {\n    return !openedBracketIds.intersects(this.missingOpeningBracketIds);\n  }\n  computeMinIndentation(offset, textModel) {\n    return Number.MAX_SAFE_INTEGER;\n  }\n}", "map": {"version": 3, "names": ["BugIndicatingError", "CursorColumns", "lengthAdd", "lengthGetLineCount", "lengthToObj", "lengthZero", "SmallImmutableSet", "BaseAstNode", "length", "_length", "constructor", "PairAstNode", "create", "openingBracket", "child", "closingBracket", "missingOpeningBracketIds", "getEmpty", "kind", "listHeight", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "idx", "Error", "children", "result", "push", "canBeReused", "openBracketIds", "intersects", "deepClone", "computeMinIndentation", "offset", "textModel", "Number", "MAX_SAFE_INTEGER", "ListAstNode", "create23", "item1", "item2", "item3", "immutable", "missingBracketIds", "merge", "Immutable23ListAstNode", "TwoThreeListAstNode", "ImmutableArrayListAstNode", "_missingOpeningBracketIds", "cachedMinIndentation", "throwIfImmutable", "makeLastElementMutable", "childCount", "undefined", "<PERSON><PERSON><PERSON><PERSON>", "mutable", "toMutable", "<PERSON><PERSON><PERSON><PERSON>", "makeFirstElementMutable", "<PERSON><PERSON><PERSON><PERSON>", "last<PERSON><PERSON><PERSON>", "handleChildrenChanged", "count", "unopenedBrackets", "i", "minIndentation", "childOffset", "Math", "min", "_item3", "_item1", "_item2", "node", "appendChildOfSameHeight", "unappendChild", "prependChildOfSameHeight", "unprependChild", "ArrayListAstNode", "_children", "Array", "item", "pop", "unshift", "shift", "emptyArray", "ImmutableLeafAstNode", "TextAstNode", "_openedBracketIds", "start", "startLineNumber", "columnCount", "lineCount", "endLineNumber", "lineNumber", "firstNonWsColumn", "getLineFirstNonWhitespaceColumn", "lineContent", "get<PERSON>ineC<PERSON>nt", "visibleColumn", "visibleColumnFromColumn", "getOptions", "tabSize", "BracketAstNode", "bracketInfo", "bracketIds", "text", "bracketText", "languageId", "InvalidBracketAstNode", "closingBrackets", "openedBracketIds"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/ast.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { BugIndicatingError } from '../../../../../base/common/errors.js';\nimport { CursorColumns } from '../../../core/cursorColumns.js';\nimport { lengthAdd, lengthGetLineCount, lengthToObj, lengthZero } from './length.js';\nimport { SmallImmutableSet } from './smallImmutableSet.js';\n/**\n * The base implementation for all AST nodes.\n*/\nclass BaseAstNode {\n    /**\n     * The length of the entire node, which should equal the sum of lengths of all children.\n    */\n    get length() {\n        return this._length;\n    }\n    constructor(length) {\n        this._length = length;\n    }\n}\n/**\n * Represents a bracket pair including its child (e.g. `{ ... }`).\n * Might be unclosed.\n * Immutable, if all children are immutable.\n*/\nexport class PairAstNode extends BaseAstNode {\n    static create(openingBracket, child, closingBracket) {\n        let length = openingBracket.length;\n        if (child) {\n            length = lengthAdd(length, child.length);\n        }\n        if (closingBracket) {\n            length = lengthAdd(length, closingBracket.length);\n        }\n        return new PairAstNode(length, openingBracket, child, closingBracket, child ? child.missingOpeningBracketIds : SmallImmutableSet.getEmpty());\n    }\n    get kind() {\n        return 2 /* AstNodeKind.Pair */;\n    }\n    get listHeight() {\n        return 0;\n    }\n    get childrenLength() {\n        return 3;\n    }\n    getChild(idx) {\n        switch (idx) {\n            case 0: return this.openingBracket;\n            case 1: return this.child;\n            case 2: return this.closingBracket;\n        }\n        throw new Error('Invalid child index');\n    }\n    /**\n     * Avoid using this property, it allocates an array!\n    */\n    get children() {\n        const result = [];\n        result.push(this.openingBracket);\n        if (this.child) {\n            result.push(this.child);\n        }\n        if (this.closingBracket) {\n            result.push(this.closingBracket);\n        }\n        return result;\n    }\n    constructor(length, openingBracket, child, closingBracket, missingOpeningBracketIds) {\n        super(length);\n        this.openingBracket = openingBracket;\n        this.child = child;\n        this.closingBracket = closingBracket;\n        this.missingOpeningBracketIds = missingOpeningBracketIds;\n    }\n    canBeReused(openBracketIds) {\n        if (this.closingBracket === null) {\n            // Unclosed pair ast nodes only\n            // end at the end of the document\n            // or when a parent node is closed.\n            // This could be improved:\n            // Only return false if some next token is neither \"undefined\" nor a bracket that closes a parent.\n            return false;\n        }\n        if (openBracketIds.intersects(this.missingOpeningBracketIds)) {\n            return false;\n        }\n        return true;\n    }\n    deepClone() {\n        return new PairAstNode(this.length, this.openingBracket.deepClone(), this.child && this.child.deepClone(), this.closingBracket && this.closingBracket.deepClone(), this.missingOpeningBracketIds);\n    }\n    computeMinIndentation(offset, textModel) {\n        return this.child ? this.child.computeMinIndentation(lengthAdd(offset, this.openingBracket.length), textModel) : Number.MAX_SAFE_INTEGER;\n    }\n}\nexport class ListAstNode extends BaseAstNode {\n    /**\n     * This method uses more memory-efficient list nodes that can only store 2 or 3 children.\n    */\n    static create23(item1, item2, item3, immutable = false) {\n        let length = item1.length;\n        let missingBracketIds = item1.missingOpeningBracketIds;\n        if (item1.listHeight !== item2.listHeight) {\n            throw new Error('Invalid list heights');\n        }\n        length = lengthAdd(length, item2.length);\n        missingBracketIds = missingBracketIds.merge(item2.missingOpeningBracketIds);\n        if (item3) {\n            if (item1.listHeight !== item3.listHeight) {\n                throw new Error('Invalid list heights');\n            }\n            length = lengthAdd(length, item3.length);\n            missingBracketIds = missingBracketIds.merge(item3.missingOpeningBracketIds);\n        }\n        return immutable\n            ? new Immutable23ListAstNode(length, item1.listHeight + 1, item1, item2, item3, missingBracketIds)\n            : new TwoThreeListAstNode(length, item1.listHeight + 1, item1, item2, item3, missingBracketIds);\n    }\n    static getEmpty() {\n        return new ImmutableArrayListAstNode(lengthZero, 0, [], SmallImmutableSet.getEmpty());\n    }\n    get kind() {\n        return 4 /* AstNodeKind.List */;\n    }\n    get missingOpeningBracketIds() {\n        return this._missingOpeningBracketIds;\n    }\n    /**\n     * Use ListAstNode.create.\n    */\n    constructor(length, listHeight, _missingOpeningBracketIds) {\n        super(length);\n        this.listHeight = listHeight;\n        this._missingOpeningBracketIds = _missingOpeningBracketIds;\n        this.cachedMinIndentation = -1;\n    }\n    throwIfImmutable() {\n        // NOOP\n    }\n    makeLastElementMutable() {\n        this.throwIfImmutable();\n        const childCount = this.childrenLength;\n        if (childCount === 0) {\n            return undefined;\n        }\n        const lastChild = this.getChild(childCount - 1);\n        const mutable = lastChild.kind === 4 /* AstNodeKind.List */ ? lastChild.toMutable() : lastChild;\n        if (lastChild !== mutable) {\n            this.setChild(childCount - 1, mutable);\n        }\n        return mutable;\n    }\n    makeFirstElementMutable() {\n        this.throwIfImmutable();\n        const childCount = this.childrenLength;\n        if (childCount === 0) {\n            return undefined;\n        }\n        const firstChild = this.getChild(0);\n        const mutable = firstChild.kind === 4 /* AstNodeKind.List */ ? firstChild.toMutable() : firstChild;\n        if (firstChild !== mutable) {\n            this.setChild(0, mutable);\n        }\n        return mutable;\n    }\n    canBeReused(openBracketIds) {\n        if (openBracketIds.intersects(this.missingOpeningBracketIds)) {\n            return false;\n        }\n        if (this.childrenLength === 0) {\n            // Don't reuse empty lists.\n            return false;\n        }\n        let lastChild = this;\n        while (lastChild.kind === 4 /* AstNodeKind.List */) {\n            const lastLength = lastChild.childrenLength;\n            if (lastLength === 0) {\n                // Empty lists should never be contained in other lists.\n                throw new BugIndicatingError();\n            }\n            lastChild = lastChild.getChild(lastLength - 1);\n        }\n        return lastChild.canBeReused(openBracketIds);\n    }\n    handleChildrenChanged() {\n        this.throwIfImmutable();\n        const count = this.childrenLength;\n        let length = this.getChild(0).length;\n        let unopenedBrackets = this.getChild(0).missingOpeningBracketIds;\n        for (let i = 1; i < count; i++) {\n            const child = this.getChild(i);\n            length = lengthAdd(length, child.length);\n            unopenedBrackets = unopenedBrackets.merge(child.missingOpeningBracketIds);\n        }\n        this._length = length;\n        this._missingOpeningBracketIds = unopenedBrackets;\n        this.cachedMinIndentation = -1;\n    }\n    computeMinIndentation(offset, textModel) {\n        if (this.cachedMinIndentation !== -1) {\n            return this.cachedMinIndentation;\n        }\n        let minIndentation = Number.MAX_SAFE_INTEGER;\n        let childOffset = offset;\n        for (let i = 0; i < this.childrenLength; i++) {\n            const child = this.getChild(i);\n            if (child) {\n                minIndentation = Math.min(minIndentation, child.computeMinIndentation(childOffset, textModel));\n                childOffset = lengthAdd(childOffset, child.length);\n            }\n        }\n        this.cachedMinIndentation = minIndentation;\n        return minIndentation;\n    }\n}\nclass TwoThreeListAstNode extends ListAstNode {\n    get childrenLength() {\n        return this._item3 !== null ? 3 : 2;\n    }\n    getChild(idx) {\n        switch (idx) {\n            case 0: return this._item1;\n            case 1: return this._item2;\n            case 2: return this._item3;\n        }\n        throw new Error('Invalid child index');\n    }\n    setChild(idx, node) {\n        switch (idx) {\n            case 0:\n                this._item1 = node;\n                return;\n            case 1:\n                this._item2 = node;\n                return;\n            case 2:\n                this._item3 = node;\n                return;\n        }\n        throw new Error('Invalid child index');\n    }\n    get children() {\n        return this._item3 ? [this._item1, this._item2, this._item3] : [this._item1, this._item2];\n    }\n    get item1() {\n        return this._item1;\n    }\n    get item2() {\n        return this._item2;\n    }\n    get item3() {\n        return this._item3;\n    }\n    constructor(length, listHeight, _item1, _item2, _item3, missingOpeningBracketIds) {\n        super(length, listHeight, missingOpeningBracketIds);\n        this._item1 = _item1;\n        this._item2 = _item2;\n        this._item3 = _item3;\n    }\n    deepClone() {\n        return new TwoThreeListAstNode(this.length, this.listHeight, this._item1.deepClone(), this._item2.deepClone(), this._item3 ? this._item3.deepClone() : null, this.missingOpeningBracketIds);\n    }\n    appendChildOfSameHeight(node) {\n        if (this._item3) {\n            throw new Error('Cannot append to a full (2,3) tree node');\n        }\n        this.throwIfImmutable();\n        this._item3 = node;\n        this.handleChildrenChanged();\n    }\n    unappendChild() {\n        if (!this._item3) {\n            throw new Error('Cannot remove from a non-full (2,3) tree node');\n        }\n        this.throwIfImmutable();\n        const result = this._item3;\n        this._item3 = null;\n        this.handleChildrenChanged();\n        return result;\n    }\n    prependChildOfSameHeight(node) {\n        if (this._item3) {\n            throw new Error('Cannot prepend to a full (2,3) tree node');\n        }\n        this.throwIfImmutable();\n        this._item3 = this._item2;\n        this._item2 = this._item1;\n        this._item1 = node;\n        this.handleChildrenChanged();\n    }\n    unprependChild() {\n        if (!this._item3) {\n            throw new Error('Cannot remove from a non-full (2,3) tree node');\n        }\n        this.throwIfImmutable();\n        const result = this._item1;\n        this._item1 = this._item2;\n        this._item2 = this._item3;\n        this._item3 = null;\n        this.handleChildrenChanged();\n        return result;\n    }\n    toMutable() {\n        return this;\n    }\n}\n/**\n * Immutable, if all children are immutable.\n*/\nclass Immutable23ListAstNode extends TwoThreeListAstNode {\n    toMutable() {\n        return new TwoThreeListAstNode(this.length, this.listHeight, this.item1, this.item2, this.item3, this.missingOpeningBracketIds);\n    }\n    throwIfImmutable() {\n        throw new Error('this instance is immutable');\n    }\n}\n/**\n * For debugging.\n*/\nclass ArrayListAstNode extends ListAstNode {\n    get childrenLength() {\n        return this._children.length;\n    }\n    getChild(idx) {\n        return this._children[idx];\n    }\n    setChild(idx, child) {\n        this._children[idx] = child;\n    }\n    get children() {\n        return this._children;\n    }\n    constructor(length, listHeight, _children, missingOpeningBracketIds) {\n        super(length, listHeight, missingOpeningBracketIds);\n        this._children = _children;\n    }\n    deepClone() {\n        const children = new Array(this._children.length);\n        for (let i = 0; i < this._children.length; i++) {\n            children[i] = this._children[i].deepClone();\n        }\n        return new ArrayListAstNode(this.length, this.listHeight, children, this.missingOpeningBracketIds);\n    }\n    appendChildOfSameHeight(node) {\n        this.throwIfImmutable();\n        this._children.push(node);\n        this.handleChildrenChanged();\n    }\n    unappendChild() {\n        this.throwIfImmutable();\n        const item = this._children.pop();\n        this.handleChildrenChanged();\n        return item;\n    }\n    prependChildOfSameHeight(node) {\n        this.throwIfImmutable();\n        this._children.unshift(node);\n        this.handleChildrenChanged();\n    }\n    unprependChild() {\n        this.throwIfImmutable();\n        const item = this._children.shift();\n        this.handleChildrenChanged();\n        return item;\n    }\n    toMutable() {\n        return this;\n    }\n}\n/**\n * Immutable, if all children are immutable.\n*/\nclass ImmutableArrayListAstNode extends ArrayListAstNode {\n    toMutable() {\n        return new ArrayListAstNode(this.length, this.listHeight, [...this.children], this.missingOpeningBracketIds);\n    }\n    throwIfImmutable() {\n        throw new Error('this instance is immutable');\n    }\n}\nconst emptyArray = [];\nclass ImmutableLeafAstNode extends BaseAstNode {\n    get listHeight() {\n        return 0;\n    }\n    get childrenLength() {\n        return 0;\n    }\n    getChild(idx) {\n        return null;\n    }\n    get children() {\n        return emptyArray;\n    }\n    deepClone() {\n        return this;\n    }\n}\nexport class TextAstNode extends ImmutableLeafAstNode {\n    get kind() {\n        return 0 /* AstNodeKind.Text */;\n    }\n    get missingOpeningBracketIds() {\n        return SmallImmutableSet.getEmpty();\n    }\n    canBeReused(_openedBracketIds) {\n        return true;\n    }\n    computeMinIndentation(offset, textModel) {\n        const start = lengthToObj(offset);\n        // Text ast nodes don't have partial indentation (ensured by the tokenizer).\n        // Thus, if this text node does not start at column 0, the first line cannot have any indentation at all.\n        const startLineNumber = (start.columnCount === 0 ? start.lineCount : start.lineCount + 1) + 1;\n        const endLineNumber = lengthGetLineCount(lengthAdd(offset, this.length)) + 1;\n        let result = Number.MAX_SAFE_INTEGER;\n        for (let lineNumber = startLineNumber; lineNumber <= endLineNumber; lineNumber++) {\n            const firstNonWsColumn = textModel.getLineFirstNonWhitespaceColumn(lineNumber);\n            const lineContent = textModel.getLineContent(lineNumber);\n            if (firstNonWsColumn === 0) {\n                continue;\n            }\n            const visibleColumn = CursorColumns.visibleColumnFromColumn(lineContent, firstNonWsColumn, textModel.getOptions().tabSize);\n            result = Math.min(result, visibleColumn);\n        }\n        return result;\n    }\n}\nexport class BracketAstNode extends ImmutableLeafAstNode {\n    static create(length, bracketInfo, bracketIds) {\n        const node = new BracketAstNode(length, bracketInfo, bracketIds);\n        return node;\n    }\n    get kind() {\n        return 1 /* AstNodeKind.Bracket */;\n    }\n    get missingOpeningBracketIds() {\n        return SmallImmutableSet.getEmpty();\n    }\n    constructor(length, bracketInfo, \n    /**\n     * In case of a opening bracket, this is the id of the opening bracket.\n     * In case of a closing bracket, this contains the ids of all opening brackets it can close.\n    */\n    bracketIds) {\n        super(length);\n        this.bracketInfo = bracketInfo;\n        this.bracketIds = bracketIds;\n    }\n    get text() {\n        return this.bracketInfo.bracketText;\n    }\n    get languageId() {\n        return this.bracketInfo.languageId;\n    }\n    canBeReused(_openedBracketIds) {\n        // These nodes could be reused,\n        // but not in a general way.\n        // Their parent may be reused.\n        return false;\n    }\n    computeMinIndentation(offset, textModel) {\n        return Number.MAX_SAFE_INTEGER;\n    }\n}\nexport class InvalidBracketAstNode extends ImmutableLeafAstNode {\n    get kind() {\n        return 3 /* AstNodeKind.UnexpectedClosingBracket */;\n    }\n    constructor(closingBrackets, length) {\n        super(length);\n        this.missingOpeningBracketIds = closingBrackets;\n    }\n    canBeReused(openedBracketIds) {\n        return !openedBracketIds.intersects(this.missingOpeningBracketIds);\n    }\n    computeMinIndentation(offset, textModel) {\n        return Number.MAX_SAFE_INTEGER;\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,kBAAkB,QAAQ,sCAAsC;AACzE,SAASC,aAAa,QAAQ,gCAAgC;AAC9D,SAASC,SAAS,EAAEC,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,QAAQ,aAAa;AACpF,SAASC,iBAAiB,QAAQ,wBAAwB;AAC1D;AACA;AACA;AACA,MAAMC,WAAW,CAAC;EACd;AACJ;AACA;EACI,IAAIC,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACC,OAAO;EACvB;EACAC,WAAWA,CAACF,MAAM,EAAE;IAChB,IAAI,CAACC,OAAO,GAAGD,MAAM;EACzB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMG,WAAW,SAASJ,WAAW,CAAC;EACzC,OAAOK,MAAMA,CAACC,cAAc,EAAEC,KAAK,EAAEC,cAAc,EAAE;IACjD,IAAIP,MAAM,GAAGK,cAAc,CAACL,MAAM;IAClC,IAAIM,KAAK,EAAE;MACPN,MAAM,GAAGN,SAAS,CAACM,MAAM,EAAEM,KAAK,CAACN,MAAM,CAAC;IAC5C;IACA,IAAIO,cAAc,EAAE;MAChBP,MAAM,GAAGN,SAAS,CAACM,MAAM,EAAEO,cAAc,CAACP,MAAM,CAAC;IACrD;IACA,OAAO,IAAIG,WAAW,CAACH,MAAM,EAAEK,cAAc,EAAEC,KAAK,EAAEC,cAAc,EAAED,KAAK,GAAGA,KAAK,CAACE,wBAAwB,GAAGV,iBAAiB,CAACW,QAAQ,CAAC,CAAC,CAAC;EAChJ;EACA,IAAIC,IAAIA,CAAA,EAAG;IACP,OAAO,CAAC,CAAC;EACb;EACA,IAAIC,UAAUA,CAAA,EAAG;IACb,OAAO,CAAC;EACZ;EACA,IAAIC,cAAcA,CAAA,EAAG;IACjB,OAAO,CAAC;EACZ;EACAC,QAAQA,CAACC,GAAG,EAAE;IACV,QAAQA,GAAG;MACP,KAAK,CAAC;QAAE,OAAO,IAAI,CAACT,cAAc;MAClC,KAAK,CAAC;QAAE,OAAO,IAAI,CAACC,KAAK;MACzB,KAAK,CAAC;QAAE,OAAO,IAAI,CAACC,cAAc;IACtC;IACA,MAAM,IAAIQ,KAAK,CAAC,qBAAqB,CAAC;EAC1C;EACA;AACJ;AACA;EACI,IAAIC,QAAQA,CAAA,EAAG;IACX,MAAMC,MAAM,GAAG,EAAE;IACjBA,MAAM,CAACC,IAAI,CAAC,IAAI,CAACb,cAAc,CAAC;IAChC,IAAI,IAAI,CAACC,KAAK,EAAE;MACZW,MAAM,CAACC,IAAI,CAAC,IAAI,CAACZ,KAAK,CAAC;IAC3B;IACA,IAAI,IAAI,CAACC,cAAc,EAAE;MACrBU,MAAM,CAACC,IAAI,CAAC,IAAI,CAACX,cAAc,CAAC;IACpC;IACA,OAAOU,MAAM;EACjB;EACAf,WAAWA,CAACF,MAAM,EAAEK,cAAc,EAAEC,KAAK,EAAEC,cAAc,EAAEC,wBAAwB,EAAE;IACjF,KAAK,CAACR,MAAM,CAAC;IACb,IAAI,CAACK,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,wBAAwB,GAAGA,wBAAwB;EAC5D;EACAW,WAAWA,CAACC,cAAc,EAAE;IACxB,IAAI,IAAI,CAACb,cAAc,KAAK,IAAI,EAAE;MAC9B;MACA;MACA;MACA;MACA;MACA,OAAO,KAAK;IAChB;IACA,IAAIa,cAAc,CAACC,UAAU,CAAC,IAAI,CAACb,wBAAwB,CAAC,EAAE;MAC1D,OAAO,KAAK;IAChB;IACA,OAAO,IAAI;EACf;EACAc,SAASA,CAAA,EAAG;IACR,OAAO,IAAInB,WAAW,CAAC,IAAI,CAACH,MAAM,EAAE,IAAI,CAACK,cAAc,CAACiB,SAAS,CAAC,CAAC,EAAE,IAAI,CAAChB,KAAK,IAAI,IAAI,CAACA,KAAK,CAACgB,SAAS,CAAC,CAAC,EAAE,IAAI,CAACf,cAAc,IAAI,IAAI,CAACA,cAAc,CAACe,SAAS,CAAC,CAAC,EAAE,IAAI,CAACd,wBAAwB,CAAC;EACrM;EACAe,qBAAqBA,CAACC,MAAM,EAAEC,SAAS,EAAE;IACrC,OAAO,IAAI,CAACnB,KAAK,GAAG,IAAI,CAACA,KAAK,CAACiB,qBAAqB,CAAC7B,SAAS,CAAC8B,MAAM,EAAE,IAAI,CAACnB,cAAc,CAACL,MAAM,CAAC,EAAEyB,SAAS,CAAC,GAAGC,MAAM,CAACC,gBAAgB;EAC5I;AACJ;AACA,OAAO,MAAMC,WAAW,SAAS7B,WAAW,CAAC;EACzC;AACJ;AACA;EACI,OAAO8B,QAAQA,CAACC,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAEC,SAAS,GAAG,KAAK,EAAE;IACpD,IAAIjC,MAAM,GAAG8B,KAAK,CAAC9B,MAAM;IACzB,IAAIkC,iBAAiB,GAAGJ,KAAK,CAACtB,wBAAwB;IACtD,IAAIsB,KAAK,CAACnB,UAAU,KAAKoB,KAAK,CAACpB,UAAU,EAAE;MACvC,MAAM,IAAII,KAAK,CAAC,sBAAsB,CAAC;IAC3C;IACAf,MAAM,GAAGN,SAAS,CAACM,MAAM,EAAE+B,KAAK,CAAC/B,MAAM,CAAC;IACxCkC,iBAAiB,GAAGA,iBAAiB,CAACC,KAAK,CAACJ,KAAK,CAACvB,wBAAwB,CAAC;IAC3E,IAAIwB,KAAK,EAAE;MACP,IAAIF,KAAK,CAACnB,UAAU,KAAKqB,KAAK,CAACrB,UAAU,EAAE;QACvC,MAAM,IAAII,KAAK,CAAC,sBAAsB,CAAC;MAC3C;MACAf,MAAM,GAAGN,SAAS,CAACM,MAAM,EAAEgC,KAAK,CAAChC,MAAM,CAAC;MACxCkC,iBAAiB,GAAGA,iBAAiB,CAACC,KAAK,CAACH,KAAK,CAACxB,wBAAwB,CAAC;IAC/E;IACA,OAAOyB,SAAS,GACV,IAAIG,sBAAsB,CAACpC,MAAM,EAAE8B,KAAK,CAACnB,UAAU,GAAG,CAAC,EAAEmB,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAEE,iBAAiB,CAAC,GAChG,IAAIG,mBAAmB,CAACrC,MAAM,EAAE8B,KAAK,CAACnB,UAAU,GAAG,CAAC,EAAEmB,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAEE,iBAAiB,CAAC;EACvG;EACA,OAAOzB,QAAQA,CAAA,EAAG;IACd,OAAO,IAAI6B,yBAAyB,CAACzC,UAAU,EAAE,CAAC,EAAE,EAAE,EAAEC,iBAAiB,CAACW,QAAQ,CAAC,CAAC,CAAC;EACzF;EACA,IAAIC,IAAIA,CAAA,EAAG;IACP,OAAO,CAAC,CAAC;EACb;EACA,IAAIF,wBAAwBA,CAAA,EAAG;IAC3B,OAAO,IAAI,CAAC+B,yBAAyB;EACzC;EACA;AACJ;AACA;EACIrC,WAAWA,CAACF,MAAM,EAAEW,UAAU,EAAE4B,yBAAyB,EAAE;IACvD,KAAK,CAACvC,MAAM,CAAC;IACb,IAAI,CAACW,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAAC4B,yBAAyB,GAAGA,yBAAyB;IAC1D,IAAI,CAACC,oBAAoB,GAAG,CAAC,CAAC;EAClC;EACAC,gBAAgBA,CAAA,EAAG;IACf;EAAA;EAEJC,sBAAsBA,CAAA,EAAG;IACrB,IAAI,CAACD,gBAAgB,CAAC,CAAC;IACvB,MAAME,UAAU,GAAG,IAAI,CAAC/B,cAAc;IACtC,IAAI+B,UAAU,KAAK,CAAC,EAAE;MAClB,OAAOC,SAAS;IACpB;IACA,MAAMC,SAAS,GAAG,IAAI,CAAChC,QAAQ,CAAC8B,UAAU,GAAG,CAAC,CAAC;IAC/C,MAAMG,OAAO,GAAGD,SAAS,CAACnC,IAAI,KAAK,CAAC,CAAC,yBAAyBmC,SAAS,CAACE,SAAS,CAAC,CAAC,GAAGF,SAAS;IAC/F,IAAIA,SAAS,KAAKC,OAAO,EAAE;MACvB,IAAI,CAACE,QAAQ,CAACL,UAAU,GAAG,CAAC,EAAEG,OAAO,CAAC;IAC1C;IACA,OAAOA,OAAO;EAClB;EACAG,uBAAuBA,CAAA,EAAG;IACtB,IAAI,CAACR,gBAAgB,CAAC,CAAC;IACvB,MAAME,UAAU,GAAG,IAAI,CAAC/B,cAAc;IACtC,IAAI+B,UAAU,KAAK,CAAC,EAAE;MAClB,OAAOC,SAAS;IACpB;IACA,MAAMM,UAAU,GAAG,IAAI,CAACrC,QAAQ,CAAC,CAAC,CAAC;IACnC,MAAMiC,OAAO,GAAGI,UAAU,CAACxC,IAAI,KAAK,CAAC,CAAC,yBAAyBwC,UAAU,CAACH,SAAS,CAAC,CAAC,GAAGG,UAAU;IAClG,IAAIA,UAAU,KAAKJ,OAAO,EAAE;MACxB,IAAI,CAACE,QAAQ,CAAC,CAAC,EAAEF,OAAO,CAAC;IAC7B;IACA,OAAOA,OAAO;EAClB;EACA3B,WAAWA,CAACC,cAAc,EAAE;IACxB,IAAIA,cAAc,CAACC,UAAU,CAAC,IAAI,CAACb,wBAAwB,CAAC,EAAE;MAC1D,OAAO,KAAK;IAChB;IACA,IAAI,IAAI,CAACI,cAAc,KAAK,CAAC,EAAE;MAC3B;MACA,OAAO,KAAK;IAChB;IACA,IAAIiC,SAAS,GAAG,IAAI;IACpB,OAAOA,SAAS,CAACnC,IAAI,KAAK,CAAC,CAAC,wBAAwB;MAChD,MAAMyC,UAAU,GAAGN,SAAS,CAACjC,cAAc;MAC3C,IAAIuC,UAAU,KAAK,CAAC,EAAE;QAClB;QACA,MAAM,IAAI3D,kBAAkB,CAAC,CAAC;MAClC;MACAqD,SAAS,GAAGA,SAAS,CAAChC,QAAQ,CAACsC,UAAU,GAAG,CAAC,CAAC;IAClD;IACA,OAAON,SAAS,CAAC1B,WAAW,CAACC,cAAc,CAAC;EAChD;EACAgC,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAACX,gBAAgB,CAAC,CAAC;IACvB,MAAMY,KAAK,GAAG,IAAI,CAACzC,cAAc;IACjC,IAAIZ,MAAM,GAAG,IAAI,CAACa,QAAQ,CAAC,CAAC,CAAC,CAACb,MAAM;IACpC,IAAIsD,gBAAgB,GAAG,IAAI,CAACzC,QAAQ,CAAC,CAAC,CAAC,CAACL,wBAAwB;IAChE,KAAK,IAAI+C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,EAAEE,CAAC,EAAE,EAAE;MAC5B,MAAMjD,KAAK,GAAG,IAAI,CAACO,QAAQ,CAAC0C,CAAC,CAAC;MAC9BvD,MAAM,GAAGN,SAAS,CAACM,MAAM,EAAEM,KAAK,CAACN,MAAM,CAAC;MACxCsD,gBAAgB,GAAGA,gBAAgB,CAACnB,KAAK,CAAC7B,KAAK,CAACE,wBAAwB,CAAC;IAC7E;IACA,IAAI,CAACP,OAAO,GAAGD,MAAM;IACrB,IAAI,CAACuC,yBAAyB,GAAGe,gBAAgB;IACjD,IAAI,CAACd,oBAAoB,GAAG,CAAC,CAAC;EAClC;EACAjB,qBAAqBA,CAACC,MAAM,EAAEC,SAAS,EAAE;IACrC,IAAI,IAAI,CAACe,oBAAoB,KAAK,CAAC,CAAC,EAAE;MAClC,OAAO,IAAI,CAACA,oBAAoB;IACpC;IACA,IAAIgB,cAAc,GAAG9B,MAAM,CAACC,gBAAgB;IAC5C,IAAI8B,WAAW,GAAGjC,MAAM;IACxB,KAAK,IAAI+B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC3C,cAAc,EAAE2C,CAAC,EAAE,EAAE;MAC1C,MAAMjD,KAAK,GAAG,IAAI,CAACO,QAAQ,CAAC0C,CAAC,CAAC;MAC9B,IAAIjD,KAAK,EAAE;QACPkD,cAAc,GAAGE,IAAI,CAACC,GAAG,CAACH,cAAc,EAAElD,KAAK,CAACiB,qBAAqB,CAACkC,WAAW,EAAEhC,SAAS,CAAC,CAAC;QAC9FgC,WAAW,GAAG/D,SAAS,CAAC+D,WAAW,EAAEnD,KAAK,CAACN,MAAM,CAAC;MACtD;IACJ;IACA,IAAI,CAACwC,oBAAoB,GAAGgB,cAAc;IAC1C,OAAOA,cAAc;EACzB;AACJ;AACA,MAAMnB,mBAAmB,SAAST,WAAW,CAAC;EAC1C,IAAIhB,cAAcA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACgD,MAAM,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC;EACvC;EACA/C,QAAQA,CAACC,GAAG,EAAE;IACV,QAAQA,GAAG;MACP,KAAK,CAAC;QAAE,OAAO,IAAI,CAAC+C,MAAM;MAC1B,KAAK,CAAC;QAAE,OAAO,IAAI,CAACC,MAAM;MAC1B,KAAK,CAAC;QAAE,OAAO,IAAI,CAACF,MAAM;IAC9B;IACA,MAAM,IAAI7C,KAAK,CAAC,qBAAqB,CAAC;EAC1C;EACAiC,QAAQA,CAAClC,GAAG,EAAEiD,IAAI,EAAE;IAChB,QAAQjD,GAAG;MACP,KAAK,CAAC;QACF,IAAI,CAAC+C,MAAM,GAAGE,IAAI;QAClB;MACJ,KAAK,CAAC;QACF,IAAI,CAACD,MAAM,GAAGC,IAAI;QAClB;MACJ,KAAK,CAAC;QACF,IAAI,CAACH,MAAM,GAAGG,IAAI;QAClB;IACR;IACA,MAAM,IAAIhD,KAAK,CAAC,qBAAqB,CAAC;EAC1C;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC4C,MAAM,GAAG,CAAC,IAAI,CAACC,MAAM,EAAE,IAAI,CAACC,MAAM,EAAE,IAAI,CAACF,MAAM,CAAC,GAAG,CAAC,IAAI,CAACC,MAAM,EAAE,IAAI,CAACC,MAAM,CAAC;EAC7F;EACA,IAAIhC,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAAC+B,MAAM;EACtB;EACA,IAAI9B,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAAC+B,MAAM;EACtB;EACA,IAAI9B,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAAC4B,MAAM;EACtB;EACA1D,WAAWA,CAACF,MAAM,EAAEW,UAAU,EAAEkD,MAAM,EAAEC,MAAM,EAAEF,MAAM,EAAEpD,wBAAwB,EAAE;IAC9E,KAAK,CAACR,MAAM,EAAEW,UAAU,EAAEH,wBAAwB,CAAC;IACnD,IAAI,CAACqD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACF,MAAM,GAAGA,MAAM;EACxB;EACAtC,SAASA,CAAA,EAAG;IACR,OAAO,IAAIe,mBAAmB,CAAC,IAAI,CAACrC,MAAM,EAAE,IAAI,CAACW,UAAU,EAAE,IAAI,CAACkD,MAAM,CAACvC,SAAS,CAAC,CAAC,EAAE,IAAI,CAACwC,MAAM,CAACxC,SAAS,CAAC,CAAC,EAAE,IAAI,CAACsC,MAAM,GAAG,IAAI,CAACA,MAAM,CAACtC,SAAS,CAAC,CAAC,GAAG,IAAI,EAAE,IAAI,CAACd,wBAAwB,CAAC;EAC/L;EACAwD,uBAAuBA,CAACD,IAAI,EAAE;IAC1B,IAAI,IAAI,CAACH,MAAM,EAAE;MACb,MAAM,IAAI7C,KAAK,CAAC,yCAAyC,CAAC;IAC9D;IACA,IAAI,CAAC0B,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAACmB,MAAM,GAAGG,IAAI;IAClB,IAAI,CAACX,qBAAqB,CAAC,CAAC;EAChC;EACAa,aAAaA,CAAA,EAAG;IACZ,IAAI,CAAC,IAAI,CAACL,MAAM,EAAE;MACd,MAAM,IAAI7C,KAAK,CAAC,+CAA+C,CAAC;IACpE;IACA,IAAI,CAAC0B,gBAAgB,CAAC,CAAC;IACvB,MAAMxB,MAAM,GAAG,IAAI,CAAC2C,MAAM;IAC1B,IAAI,CAACA,MAAM,GAAG,IAAI;IAClB,IAAI,CAACR,qBAAqB,CAAC,CAAC;IAC5B,OAAOnC,MAAM;EACjB;EACAiD,wBAAwBA,CAACH,IAAI,EAAE;IAC3B,IAAI,IAAI,CAACH,MAAM,EAAE;MACb,MAAM,IAAI7C,KAAK,CAAC,0CAA0C,CAAC;IAC/D;IACA,IAAI,CAAC0B,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAACmB,MAAM,GAAG,IAAI,CAACE,MAAM;IACzB,IAAI,CAACA,MAAM,GAAG,IAAI,CAACD,MAAM;IACzB,IAAI,CAACA,MAAM,GAAGE,IAAI;IAClB,IAAI,CAACX,qBAAqB,CAAC,CAAC;EAChC;EACAe,cAAcA,CAAA,EAAG;IACb,IAAI,CAAC,IAAI,CAACP,MAAM,EAAE;MACd,MAAM,IAAI7C,KAAK,CAAC,+CAA+C,CAAC;IACpE;IACA,IAAI,CAAC0B,gBAAgB,CAAC,CAAC;IACvB,MAAMxB,MAAM,GAAG,IAAI,CAAC4C,MAAM;IAC1B,IAAI,CAACA,MAAM,GAAG,IAAI,CAACC,MAAM;IACzB,IAAI,CAACA,MAAM,GAAG,IAAI,CAACF,MAAM;IACzB,IAAI,CAACA,MAAM,GAAG,IAAI;IAClB,IAAI,CAACR,qBAAqB,CAAC,CAAC;IAC5B,OAAOnC,MAAM;EACjB;EACA8B,SAASA,CAAA,EAAG;IACR,OAAO,IAAI;EACf;AACJ;AACA;AACA;AACA;AACA,MAAMX,sBAAsB,SAASC,mBAAmB,CAAC;EACrDU,SAASA,CAAA,EAAG;IACR,OAAO,IAAIV,mBAAmB,CAAC,IAAI,CAACrC,MAAM,EAAE,IAAI,CAACW,UAAU,EAAE,IAAI,CAACmB,KAAK,EAAE,IAAI,CAACC,KAAK,EAAE,IAAI,CAACC,KAAK,EAAE,IAAI,CAACxB,wBAAwB,CAAC;EACnI;EACAiC,gBAAgBA,CAAA,EAAG;IACf,MAAM,IAAI1B,KAAK,CAAC,4BAA4B,CAAC;EACjD;AACJ;AACA;AACA;AACA;AACA,MAAMqD,gBAAgB,SAASxC,WAAW,CAAC;EACvC,IAAIhB,cAAcA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACyD,SAAS,CAACrE,MAAM;EAChC;EACAa,QAAQA,CAACC,GAAG,EAAE;IACV,OAAO,IAAI,CAACuD,SAAS,CAACvD,GAAG,CAAC;EAC9B;EACAkC,QAAQA,CAAClC,GAAG,EAAER,KAAK,EAAE;IACjB,IAAI,CAAC+D,SAAS,CAACvD,GAAG,CAAC,GAAGR,KAAK;EAC/B;EACA,IAAIU,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACqD,SAAS;EACzB;EACAnE,WAAWA,CAACF,MAAM,EAAEW,UAAU,EAAE0D,SAAS,EAAE7D,wBAAwB,EAAE;IACjE,KAAK,CAACR,MAAM,EAAEW,UAAU,EAAEH,wBAAwB,CAAC;IACnD,IAAI,CAAC6D,SAAS,GAAGA,SAAS;EAC9B;EACA/C,SAASA,CAAA,EAAG;IACR,MAAMN,QAAQ,GAAG,IAAIsD,KAAK,CAAC,IAAI,CAACD,SAAS,CAACrE,MAAM,CAAC;IACjD,KAAK,IAAIuD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACc,SAAS,CAACrE,MAAM,EAAEuD,CAAC,EAAE,EAAE;MAC5CvC,QAAQ,CAACuC,CAAC,CAAC,GAAG,IAAI,CAACc,SAAS,CAACd,CAAC,CAAC,CAACjC,SAAS,CAAC,CAAC;IAC/C;IACA,OAAO,IAAI8C,gBAAgB,CAAC,IAAI,CAACpE,MAAM,EAAE,IAAI,CAACW,UAAU,EAAEK,QAAQ,EAAE,IAAI,CAACR,wBAAwB,CAAC;EACtG;EACAwD,uBAAuBA,CAACD,IAAI,EAAE;IAC1B,IAAI,CAACtB,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAAC4B,SAAS,CAACnD,IAAI,CAAC6C,IAAI,CAAC;IACzB,IAAI,CAACX,qBAAqB,CAAC,CAAC;EAChC;EACAa,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACxB,gBAAgB,CAAC,CAAC;IACvB,MAAM8B,IAAI,GAAG,IAAI,CAACF,SAAS,CAACG,GAAG,CAAC,CAAC;IACjC,IAAI,CAACpB,qBAAqB,CAAC,CAAC;IAC5B,OAAOmB,IAAI;EACf;EACAL,wBAAwBA,CAACH,IAAI,EAAE;IAC3B,IAAI,CAACtB,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAAC4B,SAAS,CAACI,OAAO,CAACV,IAAI,CAAC;IAC5B,IAAI,CAACX,qBAAqB,CAAC,CAAC;EAChC;EACAe,cAAcA,CAAA,EAAG;IACb,IAAI,CAAC1B,gBAAgB,CAAC,CAAC;IACvB,MAAM8B,IAAI,GAAG,IAAI,CAACF,SAAS,CAACK,KAAK,CAAC,CAAC;IACnC,IAAI,CAACtB,qBAAqB,CAAC,CAAC;IAC5B,OAAOmB,IAAI;EACf;EACAxB,SAASA,CAAA,EAAG;IACR,OAAO,IAAI;EACf;AACJ;AACA;AACA;AACA;AACA,MAAMT,yBAAyB,SAAS8B,gBAAgB,CAAC;EACrDrB,SAASA,CAAA,EAAG;IACR,OAAO,IAAIqB,gBAAgB,CAAC,IAAI,CAACpE,MAAM,EAAE,IAAI,CAACW,UAAU,EAAE,CAAC,GAAG,IAAI,CAACK,QAAQ,CAAC,EAAE,IAAI,CAACR,wBAAwB,CAAC;EAChH;EACAiC,gBAAgBA,CAAA,EAAG;IACf,MAAM,IAAI1B,KAAK,CAAC,4BAA4B,CAAC;EACjD;AACJ;AACA,MAAM4D,UAAU,GAAG,EAAE;AACrB,MAAMC,oBAAoB,SAAS7E,WAAW,CAAC;EAC3C,IAAIY,UAAUA,CAAA,EAAG;IACb,OAAO,CAAC;EACZ;EACA,IAAIC,cAAcA,CAAA,EAAG;IACjB,OAAO,CAAC;EACZ;EACAC,QAAQA,CAACC,GAAG,EAAE;IACV,OAAO,IAAI;EACf;EACA,IAAIE,QAAQA,CAAA,EAAG;IACX,OAAO2D,UAAU;EACrB;EACArD,SAASA,CAAA,EAAG;IACR,OAAO,IAAI;EACf;AACJ;AACA,OAAO,MAAMuD,WAAW,SAASD,oBAAoB,CAAC;EAClD,IAAIlE,IAAIA,CAAA,EAAG;IACP,OAAO,CAAC,CAAC;EACb;EACA,IAAIF,wBAAwBA,CAAA,EAAG;IAC3B,OAAOV,iBAAiB,CAACW,QAAQ,CAAC,CAAC;EACvC;EACAU,WAAWA,CAAC2D,iBAAiB,EAAE;IAC3B,OAAO,IAAI;EACf;EACAvD,qBAAqBA,CAACC,MAAM,EAAEC,SAAS,EAAE;IACrC,MAAMsD,KAAK,GAAGnF,WAAW,CAAC4B,MAAM,CAAC;IACjC;IACA;IACA,MAAMwD,eAAe,GAAG,CAACD,KAAK,CAACE,WAAW,KAAK,CAAC,GAAGF,KAAK,CAACG,SAAS,GAAGH,KAAK,CAACG,SAAS,GAAG,CAAC,IAAI,CAAC;IAC7F,MAAMC,aAAa,GAAGxF,kBAAkB,CAACD,SAAS,CAAC8B,MAAM,EAAE,IAAI,CAACxB,MAAM,CAAC,CAAC,GAAG,CAAC;IAC5E,IAAIiB,MAAM,GAAGS,MAAM,CAACC,gBAAgB;IACpC,KAAK,IAAIyD,UAAU,GAAGJ,eAAe,EAAEI,UAAU,IAAID,aAAa,EAAEC,UAAU,EAAE,EAAE;MAC9E,MAAMC,gBAAgB,GAAG5D,SAAS,CAAC6D,+BAA+B,CAACF,UAAU,CAAC;MAC9E,MAAMG,WAAW,GAAG9D,SAAS,CAAC+D,cAAc,CAACJ,UAAU,CAAC;MACxD,IAAIC,gBAAgB,KAAK,CAAC,EAAE;QACxB;MACJ;MACA,MAAMI,aAAa,GAAGhG,aAAa,CAACiG,uBAAuB,CAACH,WAAW,EAAEF,gBAAgB,EAAE5D,SAAS,CAACkE,UAAU,CAAC,CAAC,CAACC,OAAO,CAAC;MAC1H3E,MAAM,GAAGyC,IAAI,CAACC,GAAG,CAAC1C,MAAM,EAAEwE,aAAa,CAAC;IAC5C;IACA,OAAOxE,MAAM;EACjB;AACJ;AACA,OAAO,MAAM4E,cAAc,SAASjB,oBAAoB,CAAC;EACrD,OAAOxE,MAAMA,CAACJ,MAAM,EAAE8F,WAAW,EAAEC,UAAU,EAAE;IAC3C,MAAMhC,IAAI,GAAG,IAAI8B,cAAc,CAAC7F,MAAM,EAAE8F,WAAW,EAAEC,UAAU,CAAC;IAChE,OAAOhC,IAAI;EACf;EACA,IAAIrD,IAAIA,CAAA,EAAG;IACP,OAAO,CAAC,CAAC;EACb;EACA,IAAIF,wBAAwBA,CAAA,EAAG;IAC3B,OAAOV,iBAAiB,CAACW,QAAQ,CAAC,CAAC;EACvC;EACAP,WAAWA,CAACF,MAAM,EAAE8F,WAAW;EAC/B;AACJ;AACA;AACA;EACIC,UAAU,EAAE;IACR,KAAK,CAAC/F,MAAM,CAAC;IACb,IAAI,CAAC8F,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,UAAU,GAAGA,UAAU;EAChC;EACA,IAAIC,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACF,WAAW,CAACG,WAAW;EACvC;EACA,IAAIC,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACJ,WAAW,CAACI,UAAU;EACtC;EACA/E,WAAWA,CAAC2D,iBAAiB,EAAE;IAC3B;IACA;IACA;IACA,OAAO,KAAK;EAChB;EACAvD,qBAAqBA,CAACC,MAAM,EAAEC,SAAS,EAAE;IACrC,OAAOC,MAAM,CAACC,gBAAgB;EAClC;AACJ;AACA,OAAO,MAAMwE,qBAAqB,SAASvB,oBAAoB,CAAC;EAC5D,IAAIlE,IAAIA,CAAA,EAAG;IACP,OAAO,CAAC,CAAC;EACb;EACAR,WAAWA,CAACkG,eAAe,EAAEpG,MAAM,EAAE;IACjC,KAAK,CAACA,MAAM,CAAC;IACb,IAAI,CAACQ,wBAAwB,GAAG4F,eAAe;EACnD;EACAjF,WAAWA,CAACkF,gBAAgB,EAAE;IAC1B,OAAO,CAACA,gBAAgB,CAAChF,UAAU,CAAC,IAAI,CAACb,wBAAwB,CAAC;EACtE;EACAe,qBAAqBA,CAACC,MAAM,EAAEC,SAAS,EAAE;IACrC,OAAOC,MAAM,CAACC,gBAAgB;EAClC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}