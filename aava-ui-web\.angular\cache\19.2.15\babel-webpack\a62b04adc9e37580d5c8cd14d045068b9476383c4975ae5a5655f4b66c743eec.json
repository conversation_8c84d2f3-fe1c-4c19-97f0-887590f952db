{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { autorun } from '../../../../../base/common/observable.js';\nimport { firstNonWhitespaceIndex } from '../../../../../base/common/strings.js';\nimport { CursorColumns } from '../../../../common/core/cursorColumns.js';\nimport { Raw<PERSON>ontextKey } from '../../../../../platform/contextkey/common/contextkey.js';\nimport { Disposable } from '../../../../../base/common/lifecycle.js';\nimport { localize } from '../../../../../nls.js';\nexport class InlineCompletionContextKeys extends Disposable {\n  static {\n    this.inlineSuggestionVisible = new RawContextKey('inlineSuggestionVisible', false, localize('inlineSuggestionVisible', \"Whether an inline suggestion is visible\"));\n  }\n  static {\n    this.inlineSuggestionHasIndentation = new RawContextKey('inlineSuggestionHasIndentation', false, localize('inlineSuggestionHasIndentation', \"Whether the inline suggestion starts with whitespace\"));\n  }\n  static {\n    this.inlineSuggestionHasIndentationLessThanTabSize = new RawContextKey('inlineSuggestionHasIndentationLessThanTabSize', true, localize('inlineSuggestionHasIndentationLessThanTabSize', \"Whether the inline suggestion starts with whitespace that is less than what would be inserted by tab\"));\n  }\n  static {\n    this.suppressSuggestions = new RawContextKey('inlineSuggestionSuppressSuggestions', undefined, localize('suppressSuggestions', \"Whether suggestions should be suppressed for the current suggestion\"));\n  }\n  constructor(contextKeyService, model) {\n    super();\n    this.contextKeyService = contextKeyService;\n    this.model = model;\n    this.inlineCompletionVisible = InlineCompletionContextKeys.inlineSuggestionVisible.bindTo(this.contextKeyService);\n    this.inlineCompletionSuggestsIndentation = InlineCompletionContextKeys.inlineSuggestionHasIndentation.bindTo(this.contextKeyService);\n    this.inlineCompletionSuggestsIndentationLessThanTabSize = InlineCompletionContextKeys.inlineSuggestionHasIndentationLessThanTabSize.bindTo(this.contextKeyService);\n    this.suppressSuggestions = InlineCompletionContextKeys.suppressSuggestions.bindTo(this.contextKeyService);\n    this._register(autorun(reader => {\n      /** @description update context key: inlineCompletionVisible, suppressSuggestions */\n      const model = this.model.read(reader);\n      const state = model?.state.read(reader);\n      const isInlineCompletionVisible = !!state?.inlineCompletion && state?.primaryGhostText !== undefined && !state?.primaryGhostText.isEmpty();\n      this.inlineCompletionVisible.set(isInlineCompletionVisible);\n      if (state?.primaryGhostText && state?.inlineCompletion) {\n        this.suppressSuggestions.set(state.inlineCompletion.inlineCompletion.source.inlineCompletions.suppressSuggestions);\n      }\n    }));\n    this._register(autorun(reader => {\n      /** @description update context key: inlineCompletionSuggestsIndentation, inlineCompletionSuggestsIndentationLessThanTabSize */\n      const model = this.model.read(reader);\n      let startsWithIndentation = false;\n      let startsWithIndentationLessThanTabSize = true;\n      const ghostText = model?.primaryGhostText.read(reader);\n      if (!!model?.selectedSuggestItem && ghostText && ghostText.parts.length > 0) {\n        const {\n          column,\n          lines\n        } = ghostText.parts[0];\n        const firstLine = lines[0];\n        const indentationEndColumn = model.textModel.getLineIndentColumn(ghostText.lineNumber);\n        const inIndentation = column <= indentationEndColumn;\n        if (inIndentation) {\n          let firstNonWsIdx = firstNonWhitespaceIndex(firstLine);\n          if (firstNonWsIdx === -1) {\n            firstNonWsIdx = firstLine.length - 1;\n          }\n          startsWithIndentation = firstNonWsIdx > 0;\n          const tabSize = model.textModel.getOptions().tabSize;\n          const visibleColumnIndentation = CursorColumns.visibleColumnFromColumn(firstLine, firstNonWsIdx + 1, tabSize);\n          startsWithIndentationLessThanTabSize = visibleColumnIndentation < tabSize;\n        }\n      }\n      this.inlineCompletionSuggestsIndentation.set(startsWithIndentation);\n      this.inlineCompletionSuggestsIndentationLessThanTabSize.set(startsWithIndentationLessThanTabSize);\n    }));\n  }\n}", "map": {"version": 3, "names": ["autorun", "firstNonWhitespaceIndex", "CursorColumns", "RawContextKey", "Disposable", "localize", "InlineCompletionContextKeys", "inlineSuggestionVisible", "inlineSuggestionHasIndentation", "inlineSuggestionHasIndentationLessThanTabSize", "suppressSuggestions", "undefined", "constructor", "contextKeyService", "model", "inlineCompletionVisible", "bindTo", "inlineCompletionSuggestsIndentation", "inlineCompletionSuggestsIndentationLessThanTabSize", "_register", "reader", "read", "state", "isInlineCompletionVisible", "inlineCompletion", "primaryGhostText", "isEmpty", "set", "source", "inlineCompletions", "startsWithIndentation", "startsWithIndentationLessThanTabSize", "ghostText", "selectedSuggestItem", "parts", "length", "column", "lines", "firstLine", "indentationEndColumn", "textModel", "getLineIndentColumn", "lineNumber", "inIndentation", "firstNonWsIdx", "tabSize", "getOptions", "visibleColumnIndentation", "visibleColumnFromColumn"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/contrib/inlineCompletions/browser/controller/inlineCompletionContextKeys.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { autorun } from '../../../../../base/common/observable.js';\nimport { firstNonWhitespaceIndex } from '../../../../../base/common/strings.js';\nimport { CursorColumns } from '../../../../common/core/cursorColumns.js';\nimport { Raw<PERSON>ontextKey } from '../../../../../platform/contextkey/common/contextkey.js';\nimport { Disposable } from '../../../../../base/common/lifecycle.js';\nimport { localize } from '../../../../../nls.js';\nexport class InlineCompletionContextKeys extends Disposable {\n    static { this.inlineSuggestionVisible = new RawContextKey('inlineSuggestionVisible', false, localize('inlineSuggestionVisible', \"Whether an inline suggestion is visible\")); }\n    static { this.inlineSuggestionHasIndentation = new RawContextKey('inlineSuggestionHasIndentation', false, localize('inlineSuggestionHasIndentation', \"Whether the inline suggestion starts with whitespace\")); }\n    static { this.inlineSuggestionHasIndentationLessThanTabSize = new RawContextKey('inlineSuggestionHasIndentationLessThanTabSize', true, localize('inlineSuggestionHasIndentationLessThanTabSize', \"Whether the inline suggestion starts with whitespace that is less than what would be inserted by tab\")); }\n    static { this.suppressSuggestions = new RawContextKey('inlineSuggestionSuppressSuggestions', undefined, localize('suppressSuggestions', \"Whether suggestions should be suppressed for the current suggestion\")); }\n    constructor(contextKeyService, model) {\n        super();\n        this.contextKeyService = contextKeyService;\n        this.model = model;\n        this.inlineCompletionVisible = InlineCompletionContextKeys.inlineSuggestionVisible.bindTo(this.contextKeyService);\n        this.inlineCompletionSuggestsIndentation = InlineCompletionContextKeys.inlineSuggestionHasIndentation.bindTo(this.contextKeyService);\n        this.inlineCompletionSuggestsIndentationLessThanTabSize = InlineCompletionContextKeys.inlineSuggestionHasIndentationLessThanTabSize.bindTo(this.contextKeyService);\n        this.suppressSuggestions = InlineCompletionContextKeys.suppressSuggestions.bindTo(this.contextKeyService);\n        this._register(autorun(reader => {\n            /** @description update context key: inlineCompletionVisible, suppressSuggestions */\n            const model = this.model.read(reader);\n            const state = model?.state.read(reader);\n            const isInlineCompletionVisible = !!state?.inlineCompletion && state?.primaryGhostText !== undefined && !state?.primaryGhostText.isEmpty();\n            this.inlineCompletionVisible.set(isInlineCompletionVisible);\n            if (state?.primaryGhostText && state?.inlineCompletion) {\n                this.suppressSuggestions.set(state.inlineCompletion.inlineCompletion.source.inlineCompletions.suppressSuggestions);\n            }\n        }));\n        this._register(autorun(reader => {\n            /** @description update context key: inlineCompletionSuggestsIndentation, inlineCompletionSuggestsIndentationLessThanTabSize */\n            const model = this.model.read(reader);\n            let startsWithIndentation = false;\n            let startsWithIndentationLessThanTabSize = true;\n            const ghostText = model?.primaryGhostText.read(reader);\n            if (!!model?.selectedSuggestItem && ghostText && ghostText.parts.length > 0) {\n                const { column, lines } = ghostText.parts[0];\n                const firstLine = lines[0];\n                const indentationEndColumn = model.textModel.getLineIndentColumn(ghostText.lineNumber);\n                const inIndentation = column <= indentationEndColumn;\n                if (inIndentation) {\n                    let firstNonWsIdx = firstNonWhitespaceIndex(firstLine);\n                    if (firstNonWsIdx === -1) {\n                        firstNonWsIdx = firstLine.length - 1;\n                    }\n                    startsWithIndentation = firstNonWsIdx > 0;\n                    const tabSize = model.textModel.getOptions().tabSize;\n                    const visibleColumnIndentation = CursorColumns.visibleColumnFromColumn(firstLine, firstNonWsIdx + 1, tabSize);\n                    startsWithIndentationLessThanTabSize = visibleColumnIndentation < tabSize;\n                }\n            }\n            this.inlineCompletionSuggestsIndentation.set(startsWithIndentation);\n            this.inlineCompletionSuggestsIndentationLessThanTabSize.set(startsWithIndentationLessThanTabSize);\n        }));\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,OAAO,QAAQ,0CAA0C;AAClE,SAASC,uBAAuB,QAAQ,uCAAuC;AAC/E,SAASC,aAAa,QAAQ,0CAA0C;AACxE,SAASC,aAAa,QAAQ,yDAAyD;AACvF,SAASC,UAAU,QAAQ,yCAAyC;AACpE,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,OAAO,MAAMC,2BAA2B,SAASF,UAAU,CAAC;EACxD;IAAS,IAAI,CAACG,uBAAuB,GAAG,IAAIJ,aAAa,CAAC,yBAAyB,EAAE,KAAK,EAAEE,QAAQ,CAAC,yBAAyB,EAAE,yCAAyC,CAAC,CAAC;EAAE;EAC7K;IAAS,IAAI,CAACG,8BAA8B,GAAG,IAAIL,aAAa,CAAC,gCAAgC,EAAE,KAAK,EAAEE,QAAQ,CAAC,gCAAgC,EAAE,sDAAsD,CAAC,CAAC;EAAE;EAC/M;IAAS,IAAI,CAACI,6CAA6C,GAAG,IAAIN,aAAa,CAAC,+CAA+C,EAAE,IAAI,EAAEE,QAAQ,CAAC,+CAA+C,EAAE,sGAAsG,CAAC,CAAC;EAAE;EAC3S;IAAS,IAAI,CAACK,mBAAmB,GAAG,IAAIP,aAAa,CAAC,qCAAqC,EAAEQ,SAAS,EAAEN,QAAQ,CAAC,qBAAqB,EAAE,qEAAqE,CAAC,CAAC;EAAE;EACjNO,WAAWA,CAACC,iBAAiB,EAAEC,KAAK,EAAE;IAClC,KAAK,CAAC,CAAC;IACP,IAAI,CAACD,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,uBAAuB,GAAGT,2BAA2B,CAACC,uBAAuB,CAACS,MAAM,CAAC,IAAI,CAACH,iBAAiB,CAAC;IACjH,IAAI,CAACI,mCAAmC,GAAGX,2BAA2B,CAACE,8BAA8B,CAACQ,MAAM,CAAC,IAAI,CAACH,iBAAiB,CAAC;IACpI,IAAI,CAACK,kDAAkD,GAAGZ,2BAA2B,CAACG,6CAA6C,CAACO,MAAM,CAAC,IAAI,CAACH,iBAAiB,CAAC;IAClK,IAAI,CAACH,mBAAmB,GAAGJ,2BAA2B,CAACI,mBAAmB,CAACM,MAAM,CAAC,IAAI,CAACH,iBAAiB,CAAC;IACzG,IAAI,CAACM,SAAS,CAACnB,OAAO,CAACoB,MAAM,IAAI;MAC7B;MACA,MAAMN,KAAK,GAAG,IAAI,CAACA,KAAK,CAACO,IAAI,CAACD,MAAM,CAAC;MACrC,MAAME,KAAK,GAAGR,KAAK,EAAEQ,KAAK,CAACD,IAAI,CAACD,MAAM,CAAC;MACvC,MAAMG,yBAAyB,GAAG,CAAC,CAACD,KAAK,EAAEE,gBAAgB,IAAIF,KAAK,EAAEG,gBAAgB,KAAKd,SAAS,IAAI,CAACW,KAAK,EAAEG,gBAAgB,CAACC,OAAO,CAAC,CAAC;MAC1I,IAAI,CAACX,uBAAuB,CAACY,GAAG,CAACJ,yBAAyB,CAAC;MAC3D,IAAID,KAAK,EAAEG,gBAAgB,IAAIH,KAAK,EAAEE,gBAAgB,EAAE;QACpD,IAAI,CAACd,mBAAmB,CAACiB,GAAG,CAACL,KAAK,CAACE,gBAAgB,CAACA,gBAAgB,CAACI,MAAM,CAACC,iBAAiB,CAACnB,mBAAmB,CAAC;MACtH;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,CAACS,SAAS,CAACnB,OAAO,CAACoB,MAAM,IAAI;MAC7B;MACA,MAAMN,KAAK,GAAG,IAAI,CAACA,KAAK,CAACO,IAAI,CAACD,MAAM,CAAC;MACrC,IAAIU,qBAAqB,GAAG,KAAK;MACjC,IAAIC,oCAAoC,GAAG,IAAI;MAC/C,MAAMC,SAAS,GAAGlB,KAAK,EAAEW,gBAAgB,CAACJ,IAAI,CAACD,MAAM,CAAC;MACtD,IAAI,CAAC,CAACN,KAAK,EAAEmB,mBAAmB,IAAID,SAAS,IAAIA,SAAS,CAACE,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;QACzE,MAAM;UAAEC,MAAM;UAAEC;QAAM,CAAC,GAAGL,SAAS,CAACE,KAAK,CAAC,CAAC,CAAC;QAC5C,MAAMI,SAAS,GAAGD,KAAK,CAAC,CAAC,CAAC;QAC1B,MAAME,oBAAoB,GAAGzB,KAAK,CAAC0B,SAAS,CAACC,mBAAmB,CAACT,SAAS,CAACU,UAAU,CAAC;QACtF,MAAMC,aAAa,GAAGP,MAAM,IAAIG,oBAAoB;QACpD,IAAII,aAAa,EAAE;UACf,IAAIC,aAAa,GAAG3C,uBAAuB,CAACqC,SAAS,CAAC;UACtD,IAAIM,aAAa,KAAK,CAAC,CAAC,EAAE;YACtBA,aAAa,GAAGN,SAAS,CAACH,MAAM,GAAG,CAAC;UACxC;UACAL,qBAAqB,GAAGc,aAAa,GAAG,CAAC;UACzC,MAAMC,OAAO,GAAG/B,KAAK,CAAC0B,SAAS,CAACM,UAAU,CAAC,CAAC,CAACD,OAAO;UACpD,MAAME,wBAAwB,GAAG7C,aAAa,CAAC8C,uBAAuB,CAACV,SAAS,EAAEM,aAAa,GAAG,CAAC,EAAEC,OAAO,CAAC;UAC7Gd,oCAAoC,GAAGgB,wBAAwB,GAAGF,OAAO;QAC7E;MACJ;MACA,IAAI,CAAC5B,mCAAmC,CAACU,GAAG,CAACG,qBAAqB,CAAC;MACnE,IAAI,CAACZ,kDAAkD,CAACS,GAAG,CAACI,oCAAoC,CAAC;IACrG,CAAC,CAAC,CAAC;EACP;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}