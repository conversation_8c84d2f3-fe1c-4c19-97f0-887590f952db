<div class="workflow-playground-container" [class.collapsed]="isCollapsed">
  <!-- Header Section -->
  <div class="playground-header">
    <div class="header-left">
      <button class="back-btn" (click)="onBackClick()" title="Go Back">
        <ava-icon iconName="arrow-left" [iconSize]="20" iconColor="var(--color-brand-primary)"></ava-icon>
      </button>
      <!-- <h3 class="workflow-title">{{ workflowName }}</h3> -->
    </div>
    
    <button class="collapse-btn" (click)="onCollapseToggle()" [title]="isCollapsed ? 'Expand Panel' : 'Collapse Panel'">
      <ava-icon 
        [iconName]="isCollapsed ? 'panel-right' : 'panel-left'" 
        [iconSize]="20" 
        iconColor="var(--color-brand-primary)">
      </ava-icon>
    </button>
  </div>

  <!-- Content Section -->
  <div class="playground-content" *ngIf="!isCollapsed">
    <div class="agents-container">
      <app-agent-stepper-card
        *ngFor="let agent of agents; let i = index; trackBy: trackByAgentId"
        [agent]="agent"
        [stepNumber]="i + 1"
        [isFirst]="i === 0"
        [isLast]="i === agents.length - 1"
        [isActive]="isStepActive(i)"
        [isCompleted]="isStepCompleted(i)"
        (inputChanged)="onAgentInputChange(agent.id, $event.inputIndex, $event.value)"
        (fileSelected)="onAgentFileSelect(agent.id, $event.inputIndex, $event.files)"
        (messageSent)="onMessageSent(i, $event)"
        (stepCompleted)="onStepCompleted(i)">
      </app-agent-stepper-card>
    </div>
  </div>

  <!-- Collapsed State -->
  <div class="collapsed-content" *ngIf="isCollapsed">
    <div class="collapsed-info">
      <ava-icon iconName="workflow" [iconSize]="24" iconColor="var(--color-brand-primary)"></ava-icon>
      <span class="collapsed-text">{{ agents.length }} Agents</span>
    </div>
  </div>
</div>
