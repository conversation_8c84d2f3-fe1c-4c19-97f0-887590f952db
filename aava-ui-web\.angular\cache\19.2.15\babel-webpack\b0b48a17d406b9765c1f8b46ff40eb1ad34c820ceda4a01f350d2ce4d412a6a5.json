{"ast": null, "code": "import _asyncToGenerator from \"C:/console/aava-ui-web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { isHTMLElement } from '../../../../base/browser/dom.js';\nimport { CancellationTokenSource } from '../../../../base/common/cancellation.js';\nimport { isMarkdownString } from '../../../../base/common/htmlContent.js';\nimport { isFunction, isString } from '../../../../base/common/types.js';\nimport { localize } from '../../../../nls.js';\nexport class ManagedHoverWidget {\n  constructor(hoverDelegate, target, fadeInAnimation) {\n    this.hoverDelegate = hoverDelegate;\n    this.target = target;\n    this.fadeInAnimation = fadeInAnimation;\n  }\n  update(content, focus, options) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (_this._cancellationTokenSource) {\n        // there's an computation ongoing, cancel it\n        _this._cancellationTokenSource.dispose(true);\n        _this._cancellationTokenSource = undefined;\n      }\n      if (_this.isDisposed) {\n        return;\n      }\n      let resolvedContent;\n      if (content === undefined || isString(content) || isHTMLElement(content)) {\n        resolvedContent = content;\n      } else if (!isFunction(content.markdown)) {\n        resolvedContent = content.markdown ?? content.markdownNotSupportedFallback;\n      } else {\n        // compute the content, potentially long-running\n        // show 'Loading' if no hover is up yet\n        if (!_this._hoverWidget) {\n          _this.show(localize('iconLabel.loading', \"Loading...\"), focus, options);\n        }\n        // compute the content\n        _this._cancellationTokenSource = new CancellationTokenSource();\n        const token = _this._cancellationTokenSource.token;\n        resolvedContent = yield content.markdown(token);\n        if (resolvedContent === undefined) {\n          resolvedContent = content.markdownNotSupportedFallback;\n        }\n        if (_this.isDisposed || token.isCancellationRequested) {\n          // either the widget has been closed in the meantime\n          // or there has been a new call to `update`\n          return;\n        }\n      }\n      _this.show(resolvedContent, focus, options);\n    })();\n  }\n  show(content, focus, options) {\n    const oldHoverWidget = this._hoverWidget;\n    if (this.hasContent(content)) {\n      const hoverOptions = {\n        content,\n        target: this.target,\n        actions: options?.actions,\n        linkHandler: options?.linkHandler,\n        trapFocus: options?.trapFocus,\n        appearance: {\n          showPointer: this.hoverDelegate.placement === 'element',\n          skipFadeInAnimation: !this.fadeInAnimation || !!oldHoverWidget,\n          // do not fade in if the hover is already showing\n          showHoverHint: options?.appearance?.showHoverHint\n        },\n        position: {\n          hoverPosition: 2 /* HoverPosition.BELOW */\n        }\n      };\n      this._hoverWidget = this.hoverDelegate.showHover(hoverOptions, focus);\n    }\n    oldHoverWidget?.dispose();\n  }\n  hasContent(content) {\n    if (!content) {\n      return false;\n    }\n    if (isMarkdownString(content)) {\n      return !!content.value;\n    }\n    return true;\n  }\n  get isDisposed() {\n    return this._hoverWidget?.isDisposed;\n  }\n  dispose() {\n    this._hoverWidget?.dispose();\n    this._cancellationTokenSource?.dispose(true);\n    this._cancellationTokenSource = undefined;\n  }\n}", "map": {"version": 3, "names": ["isHTMLElement", "CancellationTokenSource", "isMarkdownString", "isFunction", "isString", "localize", "ManagedHoverWidget", "constructor", "hoverDelegate", "target", "fadeInAnimation", "update", "content", "focus", "options", "_this", "_asyncToGenerator", "_cancellationTokenSource", "dispose", "undefined", "isDisposed", "resolvedContent", "markdown", "markdownNotSupportedFallback", "_hoverWidget", "show", "token", "isCancellationRequested", "oldHoverWidget", "<PERSON><PERSON><PERSON><PERSON>", "hoverOptions", "actions", "linkHandler", "trapFocus", "appearance", "showPointer", "placement", "skipFadeInAnimation", "showHoverHint", "position", "hoverPosition", "showHover", "value"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/browser/services/hoverService/updatableHoverWidget.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { isHTMLElement } from '../../../../base/browser/dom.js';\nimport { CancellationTokenSource } from '../../../../base/common/cancellation.js';\nimport { isMarkdownString } from '../../../../base/common/htmlContent.js';\nimport { isFunction, isString } from '../../../../base/common/types.js';\nimport { localize } from '../../../../nls.js';\nexport class ManagedHoverWidget {\n    constructor(hoverDelegate, target, fadeInAnimation) {\n        this.hoverDelegate = hoverDelegate;\n        this.target = target;\n        this.fadeInAnimation = fadeInAnimation;\n    }\n    async update(content, focus, options) {\n        if (this._cancellationTokenSource) {\n            // there's an computation ongoing, cancel it\n            this._cancellationTokenSource.dispose(true);\n            this._cancellationTokenSource = undefined;\n        }\n        if (this.isDisposed) {\n            return;\n        }\n        let resolvedContent;\n        if (content === undefined || isString(content) || isHTMLElement(content)) {\n            resolvedContent = content;\n        }\n        else if (!isFunction(content.markdown)) {\n            resolvedContent = content.markdown ?? content.markdownNotSupportedFallback;\n        }\n        else {\n            // compute the content, potentially long-running\n            // show 'Loading' if no hover is up yet\n            if (!this._hoverWidget) {\n                this.show(localize('iconLabel.loading', \"Loading...\"), focus, options);\n            }\n            // compute the content\n            this._cancellationTokenSource = new CancellationTokenSource();\n            const token = this._cancellationTokenSource.token;\n            resolvedContent = await content.markdown(token);\n            if (resolvedContent === undefined) {\n                resolvedContent = content.markdownNotSupportedFallback;\n            }\n            if (this.isDisposed || token.isCancellationRequested) {\n                // either the widget has been closed in the meantime\n                // or there has been a new call to `update`\n                return;\n            }\n        }\n        this.show(resolvedContent, focus, options);\n    }\n    show(content, focus, options) {\n        const oldHoverWidget = this._hoverWidget;\n        if (this.hasContent(content)) {\n            const hoverOptions = {\n                content,\n                target: this.target,\n                actions: options?.actions,\n                linkHandler: options?.linkHandler,\n                trapFocus: options?.trapFocus,\n                appearance: {\n                    showPointer: this.hoverDelegate.placement === 'element',\n                    skipFadeInAnimation: !this.fadeInAnimation || !!oldHoverWidget, // do not fade in if the hover is already showing\n                    showHoverHint: options?.appearance?.showHoverHint,\n                },\n                position: {\n                    hoverPosition: 2 /* HoverPosition.BELOW */,\n                },\n            };\n            this._hoverWidget = this.hoverDelegate.showHover(hoverOptions, focus);\n        }\n        oldHoverWidget?.dispose();\n    }\n    hasContent(content) {\n        if (!content) {\n            return false;\n        }\n        if (isMarkdownString(content)) {\n            return !!content.value;\n        }\n        return true;\n    }\n    get isDisposed() {\n        return this._hoverWidget?.isDisposed;\n    }\n    dispose() {\n        this._hoverWidget?.dispose();\n        this._cancellationTokenSource?.dispose(true);\n        this._cancellationTokenSource = undefined;\n    }\n}\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA,SAASA,aAAa,QAAQ,iCAAiC;AAC/D,SAASC,uBAAuB,QAAQ,yCAAyC;AACjF,SAASC,gBAAgB,QAAQ,wCAAwC;AACzE,SAASC,UAAU,EAAEC,QAAQ,QAAQ,kCAAkC;AACvE,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,OAAO,MAAMC,kBAAkB,CAAC;EAC5BC,WAAWA,CAACC,aAAa,EAAEC,MAAM,EAAEC,eAAe,EAAE;IAChD,IAAI,CAACF,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,eAAe,GAAGA,eAAe;EAC1C;EACMC,MAAMA,CAACC,OAAO,EAAEC,KAAK,EAAEC,OAAO,EAAE;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAClC,IAAID,KAAI,CAACE,wBAAwB,EAAE;QAC/B;QACAF,KAAI,CAACE,wBAAwB,CAACC,OAAO,CAAC,IAAI,CAAC;QAC3CH,KAAI,CAACE,wBAAwB,GAAGE,SAAS;MAC7C;MACA,IAAIJ,KAAI,CAACK,UAAU,EAAE;QACjB;MACJ;MACA,IAAIC,eAAe;MACnB,IAAIT,OAAO,KAAKO,SAAS,IAAIf,QAAQ,CAACQ,OAAO,CAAC,IAAIZ,aAAa,CAACY,OAAO,CAAC,EAAE;QACtES,eAAe,GAAGT,OAAO;MAC7B,CAAC,MACI,IAAI,CAACT,UAAU,CAACS,OAAO,CAACU,QAAQ,CAAC,EAAE;QACpCD,eAAe,GAAGT,OAAO,CAACU,QAAQ,IAAIV,OAAO,CAACW,4BAA4B;MAC9E,CAAC,MACI;QACD;QACA;QACA,IAAI,CAACR,KAAI,CAACS,YAAY,EAAE;UACpBT,KAAI,CAACU,IAAI,CAACpB,QAAQ,CAAC,mBAAmB,EAAE,YAAY,CAAC,EAAEQ,KAAK,EAAEC,OAAO,CAAC;QAC1E;QACA;QACAC,KAAI,CAACE,wBAAwB,GAAG,IAAIhB,uBAAuB,CAAC,CAAC;QAC7D,MAAMyB,KAAK,GAAGX,KAAI,CAACE,wBAAwB,CAACS,KAAK;QACjDL,eAAe,SAAST,OAAO,CAACU,QAAQ,CAACI,KAAK,CAAC;QAC/C,IAAIL,eAAe,KAAKF,SAAS,EAAE;UAC/BE,eAAe,GAAGT,OAAO,CAACW,4BAA4B;QAC1D;QACA,IAAIR,KAAI,CAACK,UAAU,IAAIM,KAAK,CAACC,uBAAuB,EAAE;UAClD;UACA;UACA;QACJ;MACJ;MACAZ,KAAI,CAACU,IAAI,CAACJ,eAAe,EAAER,KAAK,EAAEC,OAAO,CAAC;IAAC;EAC/C;EACAW,IAAIA,CAACb,OAAO,EAAEC,KAAK,EAAEC,OAAO,EAAE;IAC1B,MAAMc,cAAc,GAAG,IAAI,CAACJ,YAAY;IACxC,IAAI,IAAI,CAACK,UAAU,CAACjB,OAAO,CAAC,EAAE;MAC1B,MAAMkB,YAAY,GAAG;QACjBlB,OAAO;QACPH,MAAM,EAAE,IAAI,CAACA,MAAM;QACnBsB,OAAO,EAAEjB,OAAO,EAAEiB,OAAO;QACzBC,WAAW,EAAElB,OAAO,EAAEkB,WAAW;QACjCC,SAAS,EAAEnB,OAAO,EAAEmB,SAAS;QAC7BC,UAAU,EAAE;UACRC,WAAW,EAAE,IAAI,CAAC3B,aAAa,CAAC4B,SAAS,KAAK,SAAS;UACvDC,mBAAmB,EAAE,CAAC,IAAI,CAAC3B,eAAe,IAAI,CAAC,CAACkB,cAAc;UAAE;UAChEU,aAAa,EAAExB,OAAO,EAAEoB,UAAU,EAAEI;QACxC,CAAC;QACDC,QAAQ,EAAE;UACNC,aAAa,EAAE,CAAC,CAAC;QACrB;MACJ,CAAC;MACD,IAAI,CAAChB,YAAY,GAAG,IAAI,CAAChB,aAAa,CAACiC,SAAS,CAACX,YAAY,EAAEjB,KAAK,CAAC;IACzE;IACAe,cAAc,EAAEV,OAAO,CAAC,CAAC;EAC7B;EACAW,UAAUA,CAACjB,OAAO,EAAE;IAChB,IAAI,CAACA,OAAO,EAAE;MACV,OAAO,KAAK;IAChB;IACA,IAAIV,gBAAgB,CAACU,OAAO,CAAC,EAAE;MAC3B,OAAO,CAAC,CAACA,OAAO,CAAC8B,KAAK;IAC1B;IACA,OAAO,IAAI;EACf;EACA,IAAItB,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACI,YAAY,EAAEJ,UAAU;EACxC;EACAF,OAAOA,CAAA,EAAG;IACN,IAAI,CAACM,YAAY,EAAEN,OAAO,CAAC,CAAC;IAC5B,IAAI,CAACD,wBAAwB,EAAEC,OAAO,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACD,wBAAwB,GAAGE,SAAS;EAC7C;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}