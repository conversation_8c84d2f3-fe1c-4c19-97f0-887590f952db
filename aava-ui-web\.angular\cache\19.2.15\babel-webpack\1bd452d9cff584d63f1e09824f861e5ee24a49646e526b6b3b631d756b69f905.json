{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { memoize } from './decorators.js';\nexport class LinkedText {\n  constructor(nodes) {\n    this.nodes = nodes;\n  }\n  toString() {\n    return this.nodes.map(node => typeof node === 'string' ? node : node.label).join('');\n  }\n}\n__decorate([memoize], LinkedText.prototype, \"toString\", null);\nconst LINK_REGEX = /\\[([^\\]]+)\\]\\(((?:https?:\\/\\/|command:|file:)[^\\)\\s]+)(?: ([\"'])(.+?)(\\3))?\\)/gi;\nexport function parseLinkedText(text) {\n  const result = [];\n  let index = 0;\n  let match;\n  while (match = LINK_REGEX.exec(text)) {\n    if (match.index - index > 0) {\n      result.push(text.substring(index, match.index));\n    }\n    const [, label, href,, title] = match;\n    if (title) {\n      result.push({\n        label,\n        href,\n        title\n      });\n    } else {\n      result.push({\n        label,\n        href\n      });\n    }\n    index = match.index + match[0].length;\n  }\n  if (index < text.length) {\n    result.push(text.substring(index));\n  }\n  return new LinkedText(result);\n}", "map": {"version": 3, "names": ["__decorate", "decorators", "target", "key", "desc", "c", "arguments", "length", "r", "Object", "getOwnPropertyDescriptor", "d", "Reflect", "decorate", "i", "defineProperty", "memoize", "LinkedText", "constructor", "nodes", "toString", "map", "node", "label", "join", "prototype", "LINK_REGEX", "parseLinkedText", "text", "result", "index", "match", "exec", "push", "substring", "href", "title"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/common/linkedText.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { memoize } from './decorators.js';\nexport class LinkedText {\n    constructor(nodes) {\n        this.nodes = nodes;\n    }\n    toString() {\n        return this.nodes.map(node => typeof node === 'string' ? node : node.label).join('');\n    }\n}\n__decorate([\n    memoize\n], LinkedText.prototype, \"toString\", null);\nconst LINK_REGEX = /\\[([^\\]]+)\\]\\(((?:https?:\\/\\/|command:|file:)[^\\)\\s]+)(?: ([\"'])(.+?)(\\3))?\\)/gi;\nexport function parseLinkedText(text) {\n    const result = [];\n    let index = 0;\n    let match;\n    while (match = LINK_REGEX.exec(text)) {\n        if (match.index - index > 0) {\n            result.push(text.substring(index, match.index));\n        }\n        const [, label, href, , title] = match;\n        if (title) {\n            result.push({ label, href, title });\n        }\n        else {\n            result.push({ label, href });\n        }\n        index = match.index + match[0].length;\n    }\n    if (index < text.length) {\n        result.push(text.substring(index));\n    }\n    return new LinkedText(result);\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,IAAIA,UAAU,GAAI,IAAI,IAAI,IAAI,CAACA,UAAU,IAAK,UAAUC,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAE;EACnF,IAAIC,CAAC,GAAGC,SAAS,CAACC,MAAM;IAAEC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGH,MAAM,GAAGE,IAAI,KAAK,IAAI,GAAGA,IAAI,GAAGK,MAAM,CAACC,wBAAwB,CAACR,MAAM,EAAEC,GAAG,CAAC,GAAGC,IAAI;IAAEO,CAAC;EAC5H,IAAI,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,CAACC,QAAQ,KAAK,UAAU,EAAEL,CAAC,GAAGI,OAAO,CAACC,QAAQ,CAACZ,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,CAAC,CAAC,KAC1H,KAAK,IAAIU,CAAC,GAAGb,UAAU,CAACM,MAAM,GAAG,CAAC,EAAEO,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE,IAAIH,CAAC,GAAGV,UAAU,CAACa,CAAC,CAAC,EAAEN,CAAC,GAAG,CAACH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACH,CAAC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACT,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,GAAGG,CAAC,CAACT,MAAM,EAAEC,GAAG,CAAC,KAAKK,CAAC;EACjJ,OAAOH,CAAC,GAAG,CAAC,IAAIG,CAAC,IAAIC,MAAM,CAACM,cAAc,CAACb,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,EAAEA,CAAC;AACjE,CAAC;AACD,SAASQ,OAAO,QAAQ,iBAAiB;AACzC,OAAO,MAAMC,UAAU,CAAC;EACpBC,WAAWA,CAACC,KAAK,EAAE;IACf,IAAI,CAACA,KAAK,GAAGA,KAAK;EACtB;EACAC,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACD,KAAK,CAACE,GAAG,CAACC,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,GAAGA,IAAI,GAAGA,IAAI,CAACC,KAAK,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;EACxF;AACJ;AACAxB,UAAU,CAAC,CACPgB,OAAO,CACV,EAAEC,UAAU,CAACQ,SAAS,EAAE,UAAU,EAAE,IAAI,CAAC;AAC1C,MAAMC,UAAU,GAAG,iFAAiF;AACpG,OAAO,SAASC,eAAeA,CAACC,IAAI,EAAE;EAClC,MAAMC,MAAM,GAAG,EAAE;EACjB,IAAIC,KAAK,GAAG,CAAC;EACb,IAAIC,KAAK;EACT,OAAOA,KAAK,GAAGL,UAAU,CAACM,IAAI,CAACJ,IAAI,CAAC,EAAE;IAClC,IAAIG,KAAK,CAACD,KAAK,GAAGA,KAAK,GAAG,CAAC,EAAE;MACzBD,MAAM,CAACI,IAAI,CAACL,IAAI,CAACM,SAAS,CAACJ,KAAK,EAAEC,KAAK,CAACD,KAAK,CAAC,CAAC;IACnD;IACA,MAAM,GAAGP,KAAK,EAAEY,IAAI,GAAIC,KAAK,CAAC,GAAGL,KAAK;IACtC,IAAIK,KAAK,EAAE;MACPP,MAAM,CAACI,IAAI,CAAC;QAAEV,KAAK;QAAEY,IAAI;QAAEC;MAAM,CAAC,CAAC;IACvC,CAAC,MACI;MACDP,MAAM,CAACI,IAAI,CAAC;QAAEV,KAAK;QAAEY;MAAK,CAAC,CAAC;IAChC;IACAL,KAAK,GAAGC,KAAK,CAACD,KAAK,GAAGC,KAAK,CAAC,CAAC,CAAC,CAACxB,MAAM;EACzC;EACA,IAAIuB,KAAK,GAAGF,IAAI,CAACrB,MAAM,EAAE;IACrBsB,MAAM,CAACI,IAAI,CAACL,IAAI,CAACM,SAAS,CAACJ,KAAK,CAAC,CAAC;EACtC;EACA,OAAO,IAAIb,UAAU,CAACY,MAAM,CAAC;AACjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}