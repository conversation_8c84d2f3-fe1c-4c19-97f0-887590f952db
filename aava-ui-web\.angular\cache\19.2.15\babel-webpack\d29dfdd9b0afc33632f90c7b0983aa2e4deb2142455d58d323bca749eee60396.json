{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { localize } from '../../../../nls.js';\nimport { RawContextKey } from '../../../../platform/contextkey/common/contextkey.js';\nexport const inlineEditAcceptId = 'editor.action.inlineEdits.accept';\nexport const showPreviousInlineEditActionId = 'editor.action.inlineEdits.showPrevious';\nexport const showNextInlineEditActionId = 'editor.action.inlineEdits.showNext';\nexport const inlineEditVisible = new RawContextKey('inlineEditsVisible', false, localize('inlineEditsVisible', \"Whether an inline edit is visible\"));\nexport const isPinnedContextKey = new RawContextKey('inlineEditsIsPinned', false, localize('isPinned', \"Whether an inline edit is visible\"));", "map": {"version": 3, "names": ["localize", "RawContextKey", "inlineEditAcceptId", "showPreviousInlineEditActionId", "showNextInlineEditActionId", "inlineEditVisible", "isPinnedContextKey"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/contrib/inlineEdits/browser/consts.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { localize } from '../../../../nls.js';\nimport { RawContextKey } from '../../../../platform/contextkey/common/contextkey.js';\nexport const inlineEditAcceptId = 'editor.action.inlineEdits.accept';\nexport const showPreviousInlineEditActionId = 'editor.action.inlineEdits.showPrevious';\nexport const showNextInlineEditActionId = 'editor.action.inlineEdits.showNext';\nexport const inlineEditVisible = new RawContextKey('inlineEditsVisible', false, localize('inlineEditsVisible', \"Whether an inline edit is visible\"));\nexport const isPinnedContextKey = new RawContextKey('inlineEditsIsPinned', false, localize('isPinned', \"Whether an inline edit is visible\"));\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,QAAQ,QAAQ,oBAAoB;AAC7C,SAASC,aAAa,QAAQ,sDAAsD;AACpF,OAAO,MAAMC,kBAAkB,GAAG,kCAAkC;AACpE,OAAO,MAAMC,8BAA8B,GAAG,wCAAwC;AACtF,OAAO,MAAMC,0BAA0B,GAAG,oCAAoC;AAC9E,OAAO,MAAMC,iBAAiB,GAAG,IAAIJ,aAAa,CAAC,oBAAoB,EAAE,KAAK,EAAED,QAAQ,CAAC,oBAAoB,EAAE,mCAAmC,CAAC,CAAC;AACpJ,OAAO,MAAMM,kBAAkB,GAAG,IAAIL,aAAa,CAAC,qBAAqB,EAAE,KAAK,EAAED,QAAQ,CAAC,UAAU,EAAE,mCAAmC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}