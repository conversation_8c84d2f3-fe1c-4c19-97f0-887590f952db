{"ast": null, "code": "import { CommonModule, DatePipe } from '@angular/common';\nimport { PageFooterComponent } from '@shared/components/page-footer/page-footer.component';\nimport { AvaTextboxComponent, DropdownComponent, TextCardComponent, ButtonComponent, IconComponent } from '@ava/play-comp-library';\nimport { LucideAngularModule } from 'lucide-angular';\nimport toolsText from './constants/tools.json';\nimport { TOOLS_DATA } from './constants/builtInTools';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { startWith, debounceTime, distinctUntilChanged } from 'rxjs';\nimport { ToolsPreviewPanelComponent } from './tools-preview-panel/tools-preview-panel.component';\nimport { ConsoleCardComponent } from \"@shared/components/console-card/console-card.component\";\nimport { TimeAgoPipe } from '@shared/pipes/time-ago.pipe';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@shared/services/pagination.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@shared/services/tools.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@shared/services/drawer/drawer.service\";\nimport * as i6 from \"@angular/forms\";\nimport * as i7 from \"@shared/auth/services/token-storage.service\";\nimport * as i8 from \"@shared/services/debounced-search.service\";\nfunction ToolsComponent_ava_text_card_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ava-text-card\", 17);\n    i0.ɵɵlistener(\"cardClick\", function ToolsComponent_ava_text_card_13_Template_ava_text_card_cardClick_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onCreateTool());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"type\", \"create\")(\"iconName\", \"plus\")(\"title\", ctx_r1.labels.createNew)(\"isLoading\", ctx_r1.isLoading);\n  }\n}\nfunction ToolsComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 19)(2, \"h5\", 20);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.labels.noResults);\n  }\n}\nfunction ToolsComponent_ng_container_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"ava-console-card\", 21);\n    i0.ɵɵpipe(2, \"timeAgo\");\n    i0.ɵɵlistener(\"actionClick\", function ToolsComponent_ng_container_15_Template_ava_console_card_actionClick_1_listener($event) {\n      const tool_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onActionClick($event, tool_r4.id));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const tool_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"built-in-card\", !ctx_r1.isLoading && tool_r4.isBuiltIn)(\"user-defined-card\", !ctx_r1.isLoading && !tool_r4.isBuiltIn);\n    i0.ɵɵpropertyInterpolate(\"categoryTitle\", (tool_r4 == null ? null : tool_r4.isBuiltIn) ? \"Built-in\" : \"User-defined\");\n    i0.ɵɵproperty(\"title\", (tool_r4 == null ? null : tool_r4.title) || \"No Title\")(\"description\", (tool_r4 == null ? null : tool_r4.description) || \"No Description\")(\"author\", (tool_r4 == null ? null : tool_r4.owner) || \"AAVA\")(\"date\", i0.ɵɵpipeBind1(2, 11, tool_r4 == null ? null : tool_r4.createdDate))(\"actions\", (tool_r4 == null ? null : tool_r4.isBuiltIn) ? ctx_r1.builtInActions : ctx_r1.userDefinedActions)(\"skeleton\", ctx_r1.isLoading);\n  }\n}\nfunction ToolsComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 23)(2, \"app-page-footer\", 24);\n    i0.ɵɵlistener(\"pageChange\", function ToolsComponent_div_16_Template_app_page_footer_pageChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onPageChange($event));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"totalItems\", ctx_r1.getTotalItemsForPagination())(\"currentPage\", ctx_r1.currentPage)(\"itemsPerPage\", ctx_r1.itemsPerPage);\n  }\n}\nexport let ToolsComponent = /*#__PURE__*/(() => {\n  class ToolsComponent {\n    paginationService;\n    router;\n    route;\n    toolsService;\n    datePipe;\n    drawerService;\n    fb;\n    tokenStorage;\n    debouncedSearch;\n    allTools = [];\n    builtInTools = [];\n    userDefinedTools = [];\n    filteredTools = [];\n    displayedTools = [];\n    isLoading = false;\n    error = null;\n    currentPage = 1;\n    itemsPerPage = 12;\n    totalUserDefinedTools = 0; // Total count from API for pagination\n    // Delete popup properties\n    showDeletePopup = false;\n    toolToDelete = null;\n    currentUserSignature = '';\n    totalPages = 1;\n    labels = toolsText.labels;\n    toolsOptions = [{\n      name: 'All',\n      value: 'all'\n    }, {\n      name: 'Type A',\n      value: 'typeA'\n    }, {\n      name: 'Type B',\n      value: 'typeB'\n    }];\n    selectedData = null;\n    // Actions for user-defined tools (all actions)\n    userDefinedActions = [{\n      id: 'duplicate',\n      icon: 'copy',\n      label: 'Duplicate',\n      tooltip: 'Duplicate'\n    }, {\n      id: 'edit',\n      icon: 'edit',\n      label: 'Edit item',\n      tooltip: 'Edit'\n    }, {\n      id: 'delete',\n      icon: 'trash',\n      label: 'Delete item',\n      tooltip: 'Delete'\n    }, {\n      id: 'run',\n      icon: 'play',\n      label: 'Run application',\n      tooltip: 'Run',\n      isPrimary: true\n    }];\n    // Actions for built-in tools (only run action)\n    builtInActions = [{\n      id: 'run',\n      icon: 'play',\n      label: 'Run application',\n      tooltip: 'Run',\n      isPrimary: true\n    }];\n    searchForm;\n    search;\n    playIconList = [{\n      name: 'play',\n      iconName: 'play',\n      cursor: true\n    }];\n    // Tool filter states\n    currentFilter = 'all';\n    selectedToolFilter = 'all';\n    // Remove all preview panel related logic and state from this file\n    cardSkeletonPlaceholders = Array(11);\n    constructor(paginationService, router, route, toolsService, datePipe, drawerService, fb, tokenStorage, debouncedSearch) {\n      this.paginationService = paginationService;\n      this.router = router;\n      this.route = route;\n      this.toolsService = toolsService;\n      this.datePipe = datePipe;\n      this.drawerService = drawerService;\n      this.fb = fb;\n      this.tokenStorage = tokenStorage;\n      this.debouncedSearch = debouncedSearch;\n      this.searchForm = this.fb.group({\n        search: ['']\n      });\n    }\n    getUserSignature() {\n      const userSignature = this.tokenStorage.getDaUsername() || '<EMAIL>';\n      return userSignature;\n    }\n    ngOnInit() {\n      this.loadBuiltInTools();\n      this.getAllTools();\n      // Initialize total count to ensure pagination shows up\n      this.totalUserDefinedTools = 50; // Initial estimate, will be updated by API\n      this.searchList();\n    }\n    loadBuiltInTools() {\n      // Load built-in tools from constant file\n      this.builtInTools = TOOLS_DATA.map(tool => ({\n        ...tool,\n        id: `builtin-${tool.id}`,\n        title: tool.title,\n        name: tool.title,\n        description: tool.description,\n        isBuiltIn: true,\n        toolType: 'Built-in Tool',\n        owner: 'AAVA',\n        createdDate: '05/12/2025'\n      }));\n    }\n    getAllTools(page = 1) {\n      // Prevent multiple simultaneous API calls\n      if (this.isLoading) {\n        return;\n      }\n      this.isLoading = true;\n      this.toolsService.getUserToolsList(page).subscribe({\n        next: response => {\n          this.userDefinedTools = (response.userToolDetails || []).map(tool => ({\n            ...tool,\n            id: tool.id.toString(),\n            title: tool.name,\n            name: tool.name || 'AAVA',\n            description: tool.description,\n            createdDate: this.datePipe.transform(tool.createdAt, 'MM/dd/yyyy') || '',\n            isBuiltIn: false,\n            toolType: 'User-defined Tool',\n            isApproved: true // Assuming all returned tools are approved since isDeleted=false in API\n          }));\n          // Store total count for pagination\n          if (response.totalCount || response.total) {\n            this.totalUserDefinedTools = response.totalCount || response.total;\n          } else {\n            // Estimate total count: if we got 11 items, there might be more pages\n            const currentPageItems = this.userDefinedTools.length;\n            if (currentPageItems === 11) {\n              this.totalUserDefinedTools = Math.max(this.totalUserDefinedTools, page * 11 + 11);\n            } else {\n              this.totalUserDefinedTools = (page - 1) * 11 + currentPageItems;\n            }\n          }\n          this.allTools = [...this.builtInTools, ...this.userDefinedTools];\n          switch (this.currentFilter) {\n            case 'all':\n              this.filteredTools = [...this.builtInTools, ...this.userDefinedTools];\n              break;\n            case 'builtin':\n              this.filteredTools = [...this.builtInTools];\n              break;\n            case 'userdefined':\n              this.filteredTools = [...this.userDefinedTools];\n              break;\n          }\n          this.updateDisplayedTools();\n          this.isLoading = false;\n        },\n        error: error => {\n          console.error('Error loading tools:', error);\n          this.isLoading = false;\n        }\n      });\n      const pageParam = this.route.snapshot.queryParamMap.get('page');\n      if (pageParam) {\n        this.currentPage = parseInt(pageParam, 10);\n      }\n    }\n    searchList() {\n      this.isLoading = true;\n      this.searchForm.get('search')?.valueChanges.pipe(startWith(''), debounceTime(300), distinctUntilChanged()).subscribe(searchText => {\n        const trimmed = searchText?.trim();\n        if (!trimmed) {\n          // If search is empty, restore full tool list\n          this.filteredTools = [...this.builtInTools, ...this.userDefinedTools];\n          this.updateDisplayedTools();\n          return;\n        }\n        this.debouncedSearch.triggerSearch(trimmed, 'tools');\n      });\n      this.debouncedSearch.searchResults$.subscribe(results => {\n        const toolsArray = Array.isArray(results?.userToolDetails) ? results.userToolDetails : [];\n        this.filteredTools = toolsArray.map(tool => ({\n          id: tool.id?.toString() || '',\n          title: tool.name || 'Untitled',\n          name: tool.name || 'AAVA',\n          description: tool.description || 'No description',\n          createdDate: this.datePipe.transform(tool.createdAt, 'MM/dd/yyyy') || 'N/A',\n          isBuiltIn: tool.isBuiltIn || false,\n          toolType: tool.isBuiltIn ? 'Built-in Tool' : 'User-defined Tool',\n          owner: tool.createdBy || 'AAVA'\n        }));\n        this.displayedTools = this.filteredTools;\n        this.isLoading = false;\n      });\n    }\n    filterTools(searchText) {\n      // Reset to first page when searching\n      this.currentPage = 1;\n      this.filteredTools = this.allTools.filter(tool => {\n        const inTitle = tool.title?.toLowerCase().includes(searchText);\n        const inDescription = tool.description?.toLowerCase().includes(searchText);\n        const inTags = Array.isArray(tool.tags) && tool.tags?.some(tag => tag.label?.toLowerCase().includes(searchText));\n        return inTitle || inDescription || inTags;\n      });\n      this.updateDisplayedTools();\n    }\n    updateFilteredTools() {\n      switch (this.currentFilter) {\n        case 'all':\n          this.filteredTools = [...this.builtInTools, ...this.userDefinedTools];\n          break;\n        case 'builtin':\n          this.filteredTools = [...this.builtInTools];\n          break;\n        case 'userdefined':\n          this.filteredTools = [...this.userDefinedTools];\n          break;\n      }\n      this.updateDisplayedTools();\n    }\n    updateDisplayedTools() {\n      if (this.currentFilter === 'builtin') {\n        const startIndex = (this.currentPage - 1) * this.itemsPerPage;\n        const endIndex = startIndex + this.itemsPerPage;\n        this.displayedTools = this.filteredTools.slice(startIndex, endIndex);\n        this.totalPages = Math.ceil(this.filteredTools.length / this.itemsPerPage);\n      } else {\n        // For user-defined tools or all tools, display current page data\n        this.displayedTools = this.filteredTools;\n        // Calculate total pages based on server-side pagination\n        if (this.currentFilter === 'userdefined') {\n          this.totalPages = Math.ceil(this.totalUserDefinedTools / 11); // 11 is the API records per page\n        } else if (this.currentFilter === 'all') {\n          // For 'all', combine built-in tools count with server-side user-defined tools count\n          const totalItems = this.builtInTools.length + this.totalUserDefinedTools;\n          this.totalPages = Math.ceil(totalItems / this.itemsPerPage);\n        }\n      }\n    }\n    onCreateTool() {\n      this.router.navigate(['/libraries/tools/create']);\n    }\n    onCardClicked(toolId) {\n      const tool = this.filteredTools.find(t => t.id === toolId);\n      if (tool && tool.isBuiltIn) {\n        // For built-in tools, open the drawer (preview panel) instead of navigating\n        const builtInTool = TOOLS_DATA.find(t => t.id === tool.id.replace('builtin-', ''));\n        this.drawerService.open(ToolsPreviewPanelComponent, {\n          selectedTool: builtInTool,\n          closePreview: () => this.drawerService.clear()\n        });\n        return;\n      } else if (tool) {\n        this.router.navigate(['/libraries/tools/edit', tool.id], {\n          queryParams: {\n            returnPage: this.currentPage\n          }\n        });\n      }\n    }\n    // Add a method for viewing a tool, similar to edit\n    onViewTool(toolId) {\n      const tool = this.filteredTools.find(t => t.id === toolId);\n      if (tool) {\n        this.router.navigate(['/libraries/tools/view', tool.id], {\n          queryParams: {\n            returnPage: this.currentPage\n          }\n        });\n      }\n    }\n    getHeaderIcons(tool) {\n      // Always return at least one icon for all tool types\n      const icons = [{\n        iconName: 'wrench',\n        title: tool.toolType || 'Tool'\n      }, {\n        iconName: 'users',\n        title: tool.userCount ? String(tool.userCount) : '120'\n      }];\n      return icons;\n    }\n    getFooterIcons(tool) {\n      // Always return at least one icon for all tool types\n      const icons = [{\n        iconName: 'user',\n        title: tool.owner || 'AAVA'\n      }, {\n        iconName: 'calendar-days',\n        title: tool.createdDate || '05/12/2025'\n      }];\n      return icons;\n    }\n    onActionClick(event, toolId) {\n      let tool = this.filteredTools.find(t => t.id === toolId);\n      if (event.actionId === 'run') {\n        if (tool && tool.isBuiltIn) {\n          // Only open drawer for built-in tools\n          const builtInTool = TOOLS_DATA.find(t => t.id === tool.id.replace('builtin-', ''));\n          this.drawerService.open(ToolsPreviewPanelComponent, {\n            selectedTool: builtInTool,\n            closePreview: () => this.drawerService.clear()\n          });\n        } else {\n          // For user-defined tools, navigate to execute page\n          this.executeTool(toolId);\n        }\n        return;\n      }\n      if (!tool || tool.isBuiltIn) return; // Only user-defined tools can be deleted or copied\n      switch (event.actionId) {\n        case 'delete':\n          this.confirmDeleteTool(toolId);\n          break;\n        case 'run':\n          this.executeTool(toolId);\n          break;\n        case 'duplicate':\n          this.cloneTool(toolId);\n          break;\n        case 'edit':\n          this.edittool(toolId);\n          break;\n        default:\n          break;\n      }\n    }\n    edittool(toolId) {\n      this.router.navigate(['/libraries/tools/edit', toolId], {\n        queryParams: {\n          returnPage: this.currentPage\n        }\n      });\n    }\n    confirmDeleteTool(toolId) {\n      this.showDeletePopup = true;\n      this.toolToDelete = this.allTools.find(item => item.id === toolId);\n      this.currentUserSignature = this.getUserSignature();\n    }\n    onConfirmDelete() {\n      if (this.toolToDelete) {\n        const userSignature = this.getUserSignature();\n        const toolId = typeof this.toolToDelete.id === 'string' ? parseInt(this.toolToDelete.id, 10) : this.toolToDelete.id;\n        this.toolsService.deleteTool(toolId, userSignature).subscribe({\n          next: res => {\n            // Store the ID before closing the popup (which may clear toolToDelete)\n            const deletedId = this.toolToDelete ? this.toolToDelete.id : null;\n            this.closeDeletePopup();\n            if (deletedId !== null) {\n              this.allTools = this.allTools.filter(tool => tool && tool.id !== deletedId);\n              this.userDefinedTools = this.userDefinedTools.filter(tool => tool && tool.id !== deletedId);\n            }\n            this.getAllTools(this.currentPage);\n          },\n          error: err => {\n            alert('Failed to delete tool. Please try again.');\n            this.closeDeletePopup();\n          }\n        });\n      }\n    }\n    closeDeletePopup() {\n      this.showDeletePopup = false;\n      this.toolToDelete = null;\n    }\n    duplicateTool(toolId) {\n      // Implement duplicate logic\n    }\n    executeTool(toolId) {\n      this.router.navigate(['/libraries/tools/execute', toolId], {\n        queryParams: {\n          returnPage: this.currentPage\n        }\n      });\n    }\n    cloneTool(toolId) {\n      this.router.navigate(['/libraries/tools/clone', toolId]);\n    }\n    onSelectionChange(data) {\n      this.selectedData = data;\n      // Implement filter logic if needed\n    }\n    onPageChange(page) {\n      this.currentPage = page;\n      // For user-defined tools or all tools, fetch new data from API\n      if (this.currentFilter === 'userdefined' || this.currentFilter === 'all') {\n        this.getAllTools(page);\n      } else {\n        // For built-in tools only, use client-side pagination\n        this.updateDisplayedTools();\n      }\n    }\n    get showCreateCard() {\n      // Only show create card for user-defined tools or all tools on first page\n      return this.currentPage === 1 && !this.isLoading && !this.error && (this.currentFilter === 'userdefined' || this.currentFilter === 'all');\n    }\n    getTotalItemsForPagination() {\n      let totalItems;\n      switch (this.currentFilter) {\n        case 'builtin':\n          totalItems = this.builtInTools.length; // No create card for built-in tools\n          break;\n        case 'userdefined':\n          // For user-defined tools, if we have exactly 11 items on current page, assume more pages exist\n          if (this.userDefinedTools.length === 11) {\n            totalItems = Math.max(this.totalUserDefinedTools, this.currentPage * 11 + 11) + 1;\n          } else {\n            totalItems = this.totalUserDefinedTools + 1;\n          }\n          break;\n        case 'all':\n          totalItems = this.builtInTools.length + this.totalUserDefinedTools + 1; // +1 for create card\n          break;\n        default:\n          totalItems = this.filteredTools.length + 1;\n      }\n      return totalItems;\n    }\n    // Filter methods for the three buttons\n    showAllTools() {\n      this.selectedToolFilter = 'all';\n      this.currentFilter = 'all';\n      this.currentPage = 1; // Reset to first page\n      this.filteredTools = [...this.builtInTools, ...this.userDefinedTools];\n      this.updateDisplayedTools();\n    }\n    showBuiltInTools() {\n      this.selectedToolFilter = 'built-in';\n      this.currentFilter = 'builtin';\n      this.currentPage = 1; // Reset to first page\n      this.filteredTools = [...this.builtInTools];\n      this.updateDisplayedTools();\n    }\n    showUserDefinedTools() {\n      this.selectedToolFilter = 'user-defined';\n      this.currentFilter = 'userdefined';\n      this.currentPage = 1; // Reset to first page\n      this.filteredTools = [...this.userDefinedTools];\n      this.updateDisplayedTools();\n    }\n    // Legacy method for backward compatibility\n    showbuiltinTools() {\n      this.showBuiltInTools();\n    }\n    onButtonClick(event) {\n      //Implement your logic here\n    }\n    static ɵfac = function ToolsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ToolsComponent)(i0.ɵɵdirectiveInject(i1.PaginationService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.ToolsService), i0.ɵɵdirectiveInject(i4.DatePipe), i0.ɵɵdirectiveInject(i5.DrawerService), i0.ɵɵdirectiveInject(i6.FormBuilder), i0.ɵɵdirectiveInject(i7.TokenStorageService), i0.ɵɵdirectiveInject(i8.DebouncedSearchService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ToolsComponent,\n      selectors: [[\"app-tools\"]],\n      features: [i0.ɵɵProvidersFeature([DatePipe])],\n      decls: 17,\n      vars: 13,\n      consts: [[\"id\", \"tools-container\", 1, \"container-fluid\"], [\"id\", \"search-filter-container\", 1, \"row\", \"g-3\"], [1, \"col-12\", \"col-md-8\", \"col-lg-9\", \"col-xl-10\", \"search-section\"], [3, \"formGroup\"], [\"placeholder\", \"Search \\\"Tools\\\"\", \"hoverEffect\", \"glow\", \"pressedEffect\", \"solid\", \"formControlName\", \"search\"], [\"slot\", \"icon-start\", \"iconName\", \"search\", \"iconColor\", \"var(--color-brand-primary)\", 3, \"iconSize\"], [1, \"col-12\", \"col-md-4\", \"col-lg-3\", \"col-xl-2\", \"action-buttons\"], [\"dropdownTitle\", \"choose tool\", 3, \"selectionChange\", \"options\"], [1, \"build-in-tools\"], [\"label\", \"All\", \"size\", \"small\", 3, \"userClick\", \"variant\", \"pill\"], [\"label\", \"Built In\", \"size\", \"small\", 3, \"userClick\", \"variant\", \"pill\"], [\"label\", \"User Defined\", \"size\", \"small\", 3, \"userClick\", \"variant\", \"pill\"], [\"id\", \"prompts-card-container\", 1, \"row\", \"g-3\"], [\"class\", \"col-12 col-sm-6 col-md-4 col-lg-3 col-xl-3 col-xxl-2 mt-5\", \"iconColor\", \"#144692\", 3, \"type\", \"iconName\", \"title\", \"isLoading\", \"cardClick\", 4, \"ngIf\"], [\"class\", \"col-12 d-flex justify-content-center align-items-center py-5\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"row\", 4, \"ngIf\"], [\"iconColor\", \"#144692\", 1, \"col-12\", \"col-sm-6\", \"col-md-4\", \"col-lg-3\", \"col-xl-3\", \"col-xxl-2\", \"mt-5\", 3, \"cardClick\", \"type\", \"iconName\", \"title\", \"isLoading\"], [1, \"col-12\", \"d-flex\", \"justify-content-center\", \"align-items-center\", \"py-5\"], [1, \"text-center\"], [1, \"text-muted\"], [\"categoryIcon\", \"wrench\", \"categoryValue\", \"42\", 1, \"col-12\", \"col-sm-6\", \"col-md-4\", \"col-lg-3\", \"col-xl-3\", \"col-xxl-2\", \"mt-5\", 3, \"actionClick\", \"title\", \"description\", \"categoryTitle\", \"author\", \"date\", \"actions\", \"skeleton\"], [1, \"row\"], [1, \"col-12\", \"d-flex\", \"justify-content-center\", \"mt-4\"], [3, \"pageChange\", \"totalItems\", \"currentPage\", \"itemsPerPage\"]],\n      template: function ToolsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"form\", 3)(4, \"ava-textbox\", 4);\n          i0.ɵɵelement(5, \"ava-icon\", 5);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(6, \"div\", 6)(7, \"ava-dropdown\", 7);\n          i0.ɵɵlistener(\"selectionChange\", function ToolsComponent_Template_ava_dropdown_selectionChange_7_listener($event) {\n            return ctx.onSelectionChange($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(8, \"div\", 8)(9, \"ava-button\", 9);\n          i0.ɵɵlistener(\"userClick\", function ToolsComponent_Template_ava_button_userClick_9_listener() {\n            return ctx.showAllTools();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"ava-button\", 10);\n          i0.ɵɵlistener(\"userClick\", function ToolsComponent_Template_ava_button_userClick_10_listener() {\n            return ctx.showBuiltInTools();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"ava-button\", 11);\n          i0.ɵɵlistener(\"userClick\", function ToolsComponent_Template_ava_button_userClick_11_listener() {\n            return ctx.showUserDefinedTools();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(12, \"div\", 12);\n          i0.ɵɵtemplate(13, ToolsComponent_ava_text_card_13_Template, 1, 4, \"ava-text-card\", 13)(14, ToolsComponent_div_14_Template, 4, 1, \"div\", 14)(15, ToolsComponent_ng_container_15_Template, 3, 13, \"ng-container\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(16, ToolsComponent_div_16_Template, 3, 3, \"div\", 16);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"formGroup\", ctx.searchForm);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"iconSize\", 16);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"options\", ctx.toolsOptions);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"variant\", ctx.selectedToolFilter === \"all\" ? \"primary\" : \"secondary\")(\"pill\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"variant\", ctx.selectedToolFilter === \"built-in\" ? \"primary\" : \"secondary\")(\"pill\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"variant\", ctx.selectedToolFilter === \"user-defined\" ? \"primary\" : \"secondary\")(\"pill\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedToolFilter !== \"built-in\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.filteredTools.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.isLoading && ctx.displayedTools.length === 0 ? ctx.cardSkeletonPlaceholders : ctx.displayedTools);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.filteredTools.length > 0);\n        }\n      },\n      dependencies: [CommonModule, i4.NgForOf, i4.NgIf, PageFooterComponent, TextCardComponent, AvaTextboxComponent, DropdownComponent, LucideAngularModule, ButtonComponent, IconComponent, ReactiveFormsModule, i6.ɵNgNoValidate, i6.NgControlStatus, i6.NgControlStatusGroup, i6.FormGroupDirective, i6.FormControlName, ConsoleCardComponent, TimeAgoPipe],\n      styles: [\".ava-dropdown {\\n  width: 100% !important;\\n}\\n\\n.mt-5[_ngcontent-%COMP%] {\\n  margin-top: 2rem;\\n}\\n\\n.build-in-tools[_ngcontent-%COMP%] {\\n  padding-top: 10px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3Byb2plY3RzL3NoYXJlZC9wYWdlcy9saWJyYXJpZXMvdG9vbHMvdG9vbHMuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxzQkFBQTtBQUNGOztBQUVBO0VBQ0UsZ0JBQUE7QUFDRjs7QUFDQTtFQUNFLGlCQUFBO0FBRUYiLCJzb3VyY2VzQ29udGVudCI6WyI6Om5nLWRlZXAgLmF2YS1kcm9wZG93biB7XHJcbiAgd2lkdGg6IDEwMCUgIWltcG9ydGFudDtcclxufVxyXG5cclxuLm10LTUge1xyXG4gIG1hcmdpbi10b3A6IDJyZW07XHJcbn1cclxuLmJ1aWxkLWluLXRvb2xze1xyXG4gIHBhZGRpbmctdG9wOiAxMHB4O1xyXG59Il0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n  return ToolsComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "DatePipe", "PageFooterComponent", "AvaTextboxComponent", "DropdownComponent", "TextCardComponent", "ButtonComponent", "IconComponent", "LucideAngularModule", "toolsText", "TOOLS_DATA", "ReactiveFormsModule", "startWith", "debounceTime", "distinctUntilChanged", "ToolsPreviewPanelComponent", "ConsoleCardComponent", "TimeAgoPipe", "i0", "ɵɵelementStart", "ɵɵlistener", "ToolsComponent_ava_text_card_13_Template_ava_text_card_cardClick_0_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onCreateTool", "ɵɵelementEnd", "ɵɵproperty", "labels", "createNew", "isLoading", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate", "noResults", "ɵɵelementContainerStart", "ToolsComponent_ng_container_15_Template_ava_console_card_actionClick_1_listener", "$event", "tool_r4", "_r3", "$implicit", "onActionClick", "id", "ɵɵclassProp", "isBuiltIn", "ɵɵpropertyInterpolate", "title", "description", "owner", "ɵɵpipeBind1", "createdDate", "builtInActions", "userDefinedActions", "ToolsComponent_div_16_Template_app_page_footer_pageChange_2_listener", "_r5", "onPageChange", "getTotalItemsForPagination", "currentPage", "itemsPerPage", "ToolsComponent", "paginationService", "router", "route", "toolsService", "datePipe", "drawerService", "fb", "tokenStorage", "debouncedSearch", "allTools", "builtInTools", "userDefinedTools", "filteredTools", "displayedTools", "error", "totalUserDefinedTools", "showDeletePopup", "toolToDelete", "currentUserSignature", "totalPages", "toolsOptions", "name", "value", "selectedData", "icon", "label", "tooltip", "isPrimary", "searchForm", "search", "playIconList", "iconName", "cursor", "currentFilter", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cardSkeletonPlaceholders", "Array", "constructor", "group", "getUserSignature", "userSignature", "getDaUsername", "ngOnInit", "loadBuiltInTools", "getAllTools", "searchList", "map", "tool", "toolType", "page", "getUserToolsList", "subscribe", "next", "response", "userToolDetails", "toString", "transform", "createdAt", "isApproved", "totalCount", "total", "currentPageItems", "length", "Math", "max", "updateDisplayedTools", "console", "pageParam", "snapshot", "queryParamMap", "get", "parseInt", "valueChanges", "pipe", "searchText", "trimmed", "trim", "triggerSearch", "searchResults$", "results", "toolsArray", "isArray", "created<PERSON>y", "filterTools", "filter", "inTitle", "toLowerCase", "includes", "inDescription", "inTags", "tags", "some", "tag", "updateFilteredTools", "startIndex", "endIndex", "slice", "ceil", "totalItems", "navigate", "onCardClicked", "toolId", "find", "t", "builtInTool", "replace", "open", "selectedTool", "closePreview", "clear", "queryParams", "returnPage", "onViewTool", "getHeaderIcons", "icons", "userCount", "String", "getFooterIcons", "event", "actionId", "executeTool", "confirmDeleteTool", "cloneTool", "edittool", "item", "onConfirmDelete", "deleteTool", "res", "deletedId", "closeDeletePopup", "err", "alert", "duplicateTool", "onSelectionChange", "data", "showCreateCard", "showAllTools", "showBuiltInTools", "showUserDefinedTools", "showbuiltinTools", "onButtonClick", "ɵɵdirectiveInject", "i1", "PaginationService", "i2", "Router", "ActivatedRoute", "i3", "ToolsService", "i4", "i5", "DrawerService", "i6", "FormBuilder", "i7", "TokenStorageService", "i8", "DebouncedSearchService", "selectors", "features", "ɵɵProvidersFeature", "decls", "vars", "consts", "template", "ToolsComponent_Template", "rf", "ctx", "ɵɵelement", "ToolsComponent_Template_ava_dropdown_selectionChange_7_listener", "ToolsComponent_Template_ava_button_userClick_9_listener", "ToolsComponent_Template_ava_button_userClick_10_listener", "ToolsComponent_Template_ava_button_userClick_11_listener", "ɵɵtemplate", "ToolsComponent_ava_text_card_13_Template", "ToolsComponent_div_14_Template", "ToolsComponent_ng_container_15_Template", "ToolsComponent_div_16_Template", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "ɵNgNoValidate", "NgControlStatus", "NgControlStatusGroup", "FormGroupDirective", "FormControlName", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\pages\\libraries\\tools\\tools.component.ts", "C:\\console\\aava-ui-web\\projects\\shared\\pages\\libraries\\tools\\tools.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule, DatePipe } from '@angular/common';\r\nimport { Router, ActivatedRoute } from '@angular/router';\r\nimport { PageFooterComponent } from '@shared/components/page-footer/page-footer.component';\r\nimport { PaginationService } from '@shared/services/pagination.service';\r\nimport { ToolsService } from '@shared/services/tools.service';\r\nimport {\r\n  AvaTextboxComponent,\r\n  DropdownComponent,\r\n  DropdownOption,\r\n  TextCardComponent,\r\n  ButtonComponent,\r\n  IconComponent,\r\n  PopupComponent,\r\n} from '@ava/play-comp-library';\r\nimport { LucideAngularModule } from 'lucide-angular';\r\nimport toolsText from './constants/tools.json';\r\nimport { TOOLS_DATA } from './constants/builtInTools';\r\nimport { DrawerService } from '@shared/services/drawer/drawer.service';\r\nimport { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';\r\nimport { startWith, debounceTime, distinctUntilChanged, map } from 'rxjs';\r\nimport { ToolsPreviewPanelComponent } from './tools-preview-panel/tools-preview-panel.component';\r\nimport { TokenStorageService } from '@shared/auth/services/token-storage.service';\r\nimport { ConsoleCardAction, ConsoleCardComponent } from \"@shared/components/console-card/console-card.component\";\r\nimport { TimeAgoPipe } from '@shared/pipes/time-ago.pipe';\r\nimport { DebouncedSearchService } from '@shared/services/debounced-search.service';\r\n\r\n@Component({\r\n  selector: 'app-tools',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    PageFooterComponent,\r\n    TextCardComponent,\r\n    AvaTextboxComponent,\r\n    DropdownComponent,\r\n    LucideAngularModule,\r\n    ButtonComponent,\r\n    IconComponent,\r\n    PopupComponent,\r\n    ReactiveFormsModule,\r\n    ConsoleCardComponent,\r\n    TimeAgoPipe,\r\n  ],\r\n  providers: [DatePipe],\r\n  templateUrl: './tools.component.html',\r\n  styleUrl: './tools.component.scss',\r\n})\r\nexport class ToolsComponent implements OnInit {\r\n  allTools: any[] = [];\r\n  builtInTools: any[] = [];\r\n  userDefinedTools: any[] = [];\r\n  filteredTools: any[] = [];\r\n  displayedTools: any[] = [];\r\n  isLoading: boolean = false;\r\n  error: string | null = null;\r\n  currentPage: number = 1;\r\n  itemsPerPage: number = 12;\r\n  totalUserDefinedTools: number = 0; // Total count from API for pagination\r\n\r\n  // Delete popup properties\r\n  showDeletePopup: boolean = false;\r\n  toolToDelete: any = null;\r\n  currentUserSignature: string = '';\r\n  totalPages: number = 1;\r\n  public labels: any = toolsText.labels;\r\n  toolsOptions: DropdownOption[] = [\r\n    { name: 'All', value: 'all' },\r\n    { name: 'Type A', value: 'typeA' },\r\n    { name: 'Type B', value: 'typeB' },\r\n  ];\r\n  selectedData: any = null;\r\n  // Actions for user-defined tools (all actions)\r\n  userDefinedActions: ConsoleCardAction[] = [\r\n    {\r\n      id: 'duplicate',\r\n      icon: 'copy',\r\n      label: 'Duplicate',\r\n      tooltip: 'Duplicate',\r\n    },\r\n    {\r\n      id: 'edit',\r\n      icon: 'edit',\r\n      label: 'Edit item',\r\n      tooltip: 'Edit',\r\n    },\r\n    {\r\n      id: 'delete',\r\n      icon: 'trash',\r\n      label: 'Delete item',\r\n      tooltip: 'Delete',\r\n    },\r\n    {\r\n      id: 'run',\r\n      icon: 'play',\r\n      label: 'Run application',\r\n      tooltip: 'Run',\r\n      isPrimary: true,\r\n    },\r\n  ];\r\n\r\n  // Actions for built-in tools (only run action)\r\n  builtInActions: ConsoleCardAction[] = [\r\n    {\r\n      id: 'run',\r\n      icon: 'play',\r\n      label: 'Run application',\r\n      tooltip: 'Run',\r\n      isPrimary: true,\r\n    },\r\n  ];\r\n  searchForm!: FormGroup;\r\n  search: any;\r\n  playIconList = [{ name: 'play', iconName: 'play', cursor: true }];\r\n\r\n  // Tool filter states\r\n  currentFilter: 'all' | 'builtin' | 'userdefined' = 'all';\r\n  selectedToolFilter: 'all' | 'built-in' | 'user-defined' = 'all';\r\n  // Remove all preview panel related logic and state from this file\r\n  cardSkeletonPlaceholders = Array(11);\r\n\r\n  constructor(\r\n    private paginationService: PaginationService,\r\n    private router: Router,\r\n    private route: ActivatedRoute,\r\n    private toolsService: ToolsService,\r\n    private datePipe: DatePipe,\r\n    private drawerService: DrawerService,\r\n    private fb: FormBuilder,\r\n    private tokenStorage: TokenStorageService,\r\n    private debouncedSearch: DebouncedSearchService,\r\n  ) {\r\n    this.searchForm = this.fb.group({\r\n      search: [''],\r\n    });\r\n  }\r\n\r\n  private getUserSignature(): string {\r\n    const userSignature = this.tokenStorage.getDaUsername() || '<EMAIL>';\r\n    return userSignature;\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.loadBuiltInTools();\r\n    this.getAllTools();\r\n    // Initialize total count to ensure pagination shows up\r\n    this.totalUserDefinedTools = 50; // Initial estimate, will be updated by API\r\n    this.searchList();\r\n  }\r\n\r\n  loadBuiltInTools(): void {\r\n    // Load built-in tools from constant file\r\n    this.builtInTools = TOOLS_DATA.map((tool: any) => ({\r\n      ...tool,\r\n      id: `builtin-${tool.id}`,\r\n      title: tool.title,\r\n      name: tool.title,\r\n      description: tool.description,\r\n      isBuiltIn: true,\r\n      toolType: 'Built-in Tool',\r\n      owner: 'AAVA',\r\n      createdDate: '05/12/2025',\r\n    }));\r\n  }\r\n\r\n  getAllTools(page: number = 1): void {\r\n    // Prevent multiple simultaneous API calls\r\n    if (this.isLoading) {\r\n      return;\r\n    }\r\n    this.isLoading = true;\r\n    this.toolsService.getUserToolsList(page).subscribe({\r\n      next: (response: any) => {\r\n        this.userDefinedTools = (response.userToolDetails || []).map((tool: any) => ({\r\n          ...tool,\r\n          id: tool.id.toString(),\r\n          title: tool.name,\r\n          name: tool.name || 'AAVA',\r\n          description: tool.description,\r\n          createdDate:\r\n            this.datePipe.transform(tool.createdAt, 'MM/dd/yyyy') || '',\r\n          isBuiltIn: false,\r\n          toolType: 'User-defined Tool',\r\n          isApproved: true, // Assuming all returned tools are approved since isDeleted=false in API\r\n        }));\r\n        // Store total count for pagination\r\n        if (response.totalCount || response.total) {\r\n          this.totalUserDefinedTools = response.totalCount || response.total;\r\n        } else {\r\n          // Estimate total count: if we got 11 items, there might be more pages\r\n          const currentPageItems = this.userDefinedTools.length;\r\n          if (currentPageItems === 11) {\r\n            this.totalUserDefinedTools = Math.max(this.totalUserDefinedTools, page * 11 + 11);\r\n          } else {\r\n            this.totalUserDefinedTools = (page - 1) * 11 + currentPageItems;\r\n          }\r\n        }\r\n        this.allTools = [...this.builtInTools, ...this.userDefinedTools];\r\n        switch (this.currentFilter) {\r\n          case 'all':\r\n            this.filteredTools = [...this.builtInTools, ...this.userDefinedTools];\r\n            break;\r\n          case 'builtin':\r\n            this.filteredTools = [...this.builtInTools];\r\n            break;\r\n          case 'userdefined':\r\n            this.filteredTools = [...this.userDefinedTools];\r\n            break;\r\n        }\r\n        this.updateDisplayedTools();\r\n        this.isLoading = false;\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading tools:', error);\r\n        this.isLoading = false;\r\n      }\r\n    });\r\n    const pageParam = this.route.snapshot.queryParamMap.get('page');\r\n    if (pageParam) {\r\n      this.currentPage = parseInt(pageParam, 10);\r\n    }\r\n  }\r\n\r\n  searchList(): void {\r\n    this.isLoading = true;\r\n    this.searchForm.get('search')?.valueChanges.pipe(\r\n      startWith(''),\r\n      debounceTime(300),\r\n      distinctUntilChanged()\r\n    ).subscribe((searchText: string) => {\r\n      const trimmed = searchText?.trim();\r\n\r\n      if (!trimmed) {\r\n        // If search is empty, restore full tool list\r\n        this.filteredTools = [...this.builtInTools, ...this.userDefinedTools];\r\n        this.updateDisplayedTools();\r\n        return;\r\n      }\r\n\r\n      this.debouncedSearch.triggerSearch(trimmed, 'tools');\r\n    });\r\n\r\n    this.debouncedSearch.searchResults$.subscribe((results: any) => {\r\n\r\n      const toolsArray = Array.isArray(results?.userToolDetails) ? results.userToolDetails : [];\r\n\r\n      this.filteredTools = toolsArray.map((tool: any) => ({\r\n        id: tool.id?.toString() || '',\r\n        title: tool.name || 'Untitled',\r\n        name: tool.name || 'AAVA',\r\n        description: tool.description || 'No description',\r\n        createdDate: this.datePipe.transform(tool.createdAt, 'MM/dd/yyyy') || 'N/A',\r\n        isBuiltIn: tool.isBuiltIn || false,\r\n        toolType: tool.isBuiltIn ? 'Built-in Tool' : 'User-defined Tool',\r\n        owner: tool.createdBy || 'AAVA',\r\n      }));\r\n\r\n      this.displayedTools = this.filteredTools;\r\n      this.isLoading = false;\r\n    });\r\n  }\r\n\r\n  filterTools(searchText: string): void {\r\n    // Reset to first page when searching\r\n    this.currentPage = 1;\r\n\r\n    this.filteredTools = this.allTools.filter((tool) => {\r\n      const inTitle = tool.title?.toLowerCase().includes(searchText);\r\n      const inDescription = tool.description\r\n        ?.toLowerCase()\r\n        .includes(searchText);\r\n      const inTags =\r\n        Array.isArray(tool.tags) &&\r\n        tool.tags?.some((tag: any) =>\r\n          tag.label?.toLowerCase().includes(searchText),\r\n        );\r\n\r\n      return inTitle || inDescription || inTags;\r\n    });\r\n\r\n    this.updateDisplayedTools();\r\n  }\r\n\r\n  updateFilteredTools(): void {\r\n    switch (this.currentFilter) {\r\n      case 'all':\r\n        this.filteredTools = [...this.builtInTools, ...this.userDefinedTools];\r\n        break;\r\n      case 'builtin':\r\n        this.filteredTools = [...this.builtInTools];\r\n        break;\r\n      case 'userdefined':\r\n        this.filteredTools = [...this.userDefinedTools];\r\n        break;\r\n    }\r\n    this.updateDisplayedTools();\r\n  }\r\n\r\n  updateDisplayedTools(): void {\r\n    if (this.currentFilter === 'builtin') {\r\n      const startIndex = (this.currentPage - 1) * this.itemsPerPage;\r\n      const endIndex = startIndex + this.itemsPerPage;\r\n      this.displayedTools = this.filteredTools.slice(startIndex, endIndex);\r\n      this.totalPages = Math.ceil(this.filteredTools.length / this.itemsPerPage);\r\n    } else {\r\n      // For user-defined tools or all tools, display current page data\r\n      this.displayedTools = this.filteredTools;\r\n      // Calculate total pages based on server-side pagination\r\n      if (this.currentFilter === 'userdefined') {\r\n        this.totalPages = Math.ceil(this.totalUserDefinedTools / 11); // 11 is the API records per page\r\n      } else if (this.currentFilter === 'all') {\r\n        // For 'all', combine built-in tools count with server-side user-defined tools count\r\n        const totalItems = this.builtInTools.length + this.totalUserDefinedTools;\r\n        this.totalPages = Math.ceil(totalItems / this.itemsPerPage);\r\n      }\r\n    }\r\n  }\r\n\r\n  onCreateTool(): void {\r\n    this.router.navigate(['/libraries/tools/create']);\r\n  }\r\n\r\n  onCardClicked(toolId: string): void {\r\n    const tool = this.filteredTools.find((t) => t.id === toolId);\r\n    if (tool && tool.isBuiltIn) {\r\n      // For built-in tools, open the drawer (preview panel) instead of navigating\r\n      const builtInTool = TOOLS_DATA.find(\r\n        (t) => t.id === tool.id.replace('builtin-', ''),\r\n      );\r\n      this.drawerService.open(ToolsPreviewPanelComponent, {\r\n        selectedTool: builtInTool,\r\n        closePreview: () => this.drawerService.clear(),\r\n      });\r\n      return;\r\n    } else if (tool) {\r\n      this.router.navigate(['/libraries/tools/edit', tool.id], {\r\n        queryParams: { returnPage: this.currentPage }\r\n      });\r\n    }\r\n  }\r\n\r\n  // Add a method for viewing a tool, similar to edit\r\n  onViewTool(toolId: string): void {\r\n    const tool = this.filteredTools.find((t) => t.id === toolId);\r\n    if (tool) {\r\n      this.router.navigate(['/libraries/tools/view', tool.id], {\r\n        queryParams: { returnPage: this.currentPage }\r\n      });\r\n    }\r\n  }\r\n\r\n  getHeaderIcons(tool: any): { iconName: string; title: string }[] {\r\n    // Always return at least one icon for all tool types\r\n    const icons = [\r\n      { iconName: 'wrench', title: tool.toolType || 'Tool' },\r\n      {\r\n        iconName: 'users',\r\n        title: tool.userCount ? String(tool.userCount) : '120',\r\n      },\r\n    ];\r\n    return icons;\r\n  }\r\n\r\n  getFooterIcons(tool: any): { iconName: string; title: string }[] {\r\n    // Always return at least one icon for all tool types\r\n    const icons = [\r\n      { iconName: 'user', title: tool.owner || 'AAVA' },\r\n      {\r\n        iconName: 'calendar-days',\r\n        title: tool.createdDate || '05/12/2025',\r\n      },\r\n    ];\r\n    return icons;\r\n  }\r\n\r\n  onActionClick(\r\n    event: { actionId: string; action: ConsoleCardAction },\r\n    toolId: string,\r\n  ): void {\r\n    let tool = this.filteredTools.find((t) => t.id === toolId);\r\n    if ((event.actionId) === 'run') {\r\n      if (tool && tool.isBuiltIn) {\r\n        // Only open drawer for built-in tools\r\n        const builtInTool = TOOLS_DATA.find(\r\n          (t) => t.id === tool.id.replace('builtin-', ''),\r\n        );\r\n        this.drawerService.open(ToolsPreviewPanelComponent, {\r\n          selectedTool: builtInTool,\r\n          closePreview: () => this.drawerService.clear(),\r\n        });\r\n      } else {\r\n        // For user-defined tools, navigate to execute page\r\n        this.executeTool(toolId);\r\n      }\r\n      return;\r\n    }\r\n    if (!tool || tool.isBuiltIn) return; // Only user-defined tools can be deleted or copied\r\n    switch (event.actionId) {\r\n      case 'delete':\r\n        this.confirmDeleteTool(toolId);\r\n        break;\r\n      case 'run':\r\n        this.executeTool(toolId);\r\n        break;\r\n      case 'duplicate':\r\n        this.cloneTool(toolId);\r\n        break;\r\n      case 'edit':\r\n        this.edittool(toolId);\r\n        break;\r\n      default:\r\n        break;\r\n    }\r\n  }\r\n\r\n  edittool(toolId: string): void {\r\n    this.router.navigate(['/libraries/tools/edit', toolId], {\r\n      queryParams: { returnPage: this.currentPage },\r\n    });\r\n  }\r\n\r\n  confirmDeleteTool(toolId: string): void {\r\n    this.showDeletePopup = true;\r\n    this.toolToDelete = this.allTools.find(\r\n      (item) => item.id === toolId\r\n    );\r\n    this.currentUserSignature = this.getUserSignature();\r\n  }\r\n\r\n  onConfirmDelete(): void {\r\n    if (this.toolToDelete) {\r\n      const userSignature = this.getUserSignature();\r\n      const toolId = typeof this.toolToDelete.id === 'string'\r\n        ? parseInt(this.toolToDelete.id, 10)\r\n        : this.toolToDelete.id;\r\n      this.toolsService.deleteTool(toolId, userSignature).subscribe({\r\n        next: (res) => {\r\n          // Store the ID before closing the popup (which may clear toolToDelete)\r\n          const deletedId = this.toolToDelete ? this.toolToDelete.id : null;\r\n          this.closeDeletePopup();\r\n          if (deletedId !== null) {\r\n            this.allTools = this.allTools.filter(tool => tool && tool.id !== deletedId);\r\n            this.userDefinedTools = this.userDefinedTools.filter(tool => tool && tool.id !== deletedId);\r\n          }\r\n          this.getAllTools(this.currentPage);\r\n        },\r\n        error: (err) => {\r\n          alert('Failed to delete tool. Please try again.');\r\n          this.closeDeletePopup();\r\n        },\r\n      });\r\n    }\r\n  }\r\n\r\n  closeDeletePopup(): void {\r\n    this.showDeletePopup = false;\r\n    this.toolToDelete = null;\r\n  }\r\n\r\n  duplicateTool(toolId: string): void {\r\n    // Implement duplicate logic\r\n  }\r\n\r\n  executeTool(toolId: string): void {\r\n    this.router.navigate(['/libraries/tools/execute', toolId], {\r\n      queryParams: { returnPage: this.currentPage },\r\n    });\r\n  }\r\n\r\n  cloneTool(toolId: string): void {\r\n    this.router.navigate(['/libraries/tools/clone', toolId]);\r\n  }\r\n\r\n  onSelectionChange(data: any) {\r\n    this.selectedData = data;\r\n    // Implement filter logic if needed\r\n  }\r\n\r\n  onPageChange(page: number): void {\r\n    this.currentPage = page;\r\n\r\n    // For user-defined tools or all tools, fetch new data from API\r\n    if (this.currentFilter === 'userdefined' || this.currentFilter === 'all') {\r\n      this.getAllTools(page);\r\n    } else {\r\n      // For built-in tools only, use client-side pagination\r\n      this.updateDisplayedTools();\r\n    }\r\n  }\r\n\r\n  get showCreateCard(): boolean {\r\n    // Only show create card for user-defined tools or all tools on first page\r\n    return (\r\n      this.currentPage === 1 &&\r\n      !this.isLoading &&\r\n      !this.error &&\r\n      (this.currentFilter === 'userdefined' || this.currentFilter === 'all')\r\n    );\r\n  }\r\n\r\n  getTotalItemsForPagination(): number {\r\n    let totalItems: number;\r\n    switch (this.currentFilter) {\r\n      case 'builtin':\r\n        totalItems = this.builtInTools.length; // No create card for built-in tools\r\n        break;\r\n      case 'userdefined':\r\n        // For user-defined tools, if we have exactly 11 items on current page, assume more pages exist\r\n        if (this.userDefinedTools.length === 11) {\r\n          totalItems = Math.max(this.totalUserDefinedTools, this.currentPage * 11 + 11) + 1;\r\n        } else {\r\n          totalItems = this.totalUserDefinedTools + 1;\r\n        }\r\n        break;\r\n      case 'all':\r\n        totalItems = this.builtInTools.length + this.totalUserDefinedTools + 1; // +1 for create card\r\n        break;\r\n      default:\r\n        totalItems = this.filteredTools.length + 1;\r\n    }\r\n\r\n    return totalItems;\r\n  }\r\n\r\n  // Filter methods for the three buttons\r\n  showAllTools(): void {\r\n    this.selectedToolFilter = 'all';\r\n    this.currentFilter = 'all';\r\n    this.currentPage = 1; // Reset to first page\r\n    this.filteredTools = [...this.builtInTools, ...this.userDefinedTools];\r\n    this.updateDisplayedTools();\r\n  }\r\n\r\n  showBuiltInTools(): void {\r\n    this.selectedToolFilter = 'built-in';\r\n    this.currentFilter = 'builtin';\r\n    this.currentPage = 1; // Reset to first page\r\n    this.filteredTools = [...this.builtInTools];\r\n    this.updateDisplayedTools();\r\n  }\r\n\r\n  showUserDefinedTools(): void {\r\n    this.selectedToolFilter = 'user-defined';\r\n    this.currentFilter = 'userdefined';\r\n    this.currentPage = 1; // Reset to first page\r\n    this.filteredTools = [...this.userDefinedTools];\r\n    this.updateDisplayedTools();\r\n  }\r\n\r\n  // Legacy method for backward compatibility\r\n  showbuiltinTools(): void {\r\n    this.showBuiltInTools();\r\n  }\r\n\r\n  onButtonClick(event: any) {\r\n    //Implement your logic here\r\n  }\r\n}\r\n", "<div id=\"tools-container\" class=\"container-fluid\">\r\n  <div id=\"search-filter-container\" class=\"row g-3\">\r\n    <div class=\"col-12 col-md-8 col-lg-9 col-xl-10 search-section\">\r\n      <form [formGroup]=\"searchForm\">\r\n        <ava-textbox\r\n          placeholder='Search \"Tools\"'\r\n          hoverEffect=\"glow\"\r\n          pressedEffect=\"solid\"\r\n          formControlName=\"search\"\r\n        >\r\n          <ava-icon\r\n            slot=\"icon-start\"\r\n            iconName=\"search\"\r\n            [iconSize]=\"16\"\r\n            iconColor=\"var(--color-brand-primary)\"\r\n          >\r\n          </ava-icon>\r\n        </ava-textbox>\r\n      </form>\r\n    </div>\r\n    <div class=\"col-12 col-md-4 col-lg-3 col-xl-2 action-buttons\">\r\n      <ava-dropdown\r\n        dropdownTitle=\"choose tool\"\r\n        [options]=\"toolsOptions\"\r\n        (selectionChange)=\"onSelectionChange($event)\"\r\n      >\r\n      </ava-dropdown>\r\n    </div>\r\n  </div>\r\n\r\n  <div class=\"build-in-tools\">\r\n    <ava-button\r\n      label=\"All\"\r\n      [variant]=\"selectedToolFilter === 'all' ? 'primary' : 'secondary'\"\r\n      [pill]=\"true\"\r\n      size=\"small\"\r\n      (userClick)=\"showAllTools()\"\r\n    ></ava-button>\r\n    <ava-button\r\n      label=\"Built In\"\r\n      [variant]=\"selectedToolFilter === 'built-in' ? 'primary' : 'secondary'\"\r\n      [pill]=\"true\"\r\n      size=\"small\"\r\n      (userClick)=\"showBuiltInTools()\"\r\n    ></ava-button>\r\n    <ava-button\r\n      label=\"User Defined\"\r\n      [variant]=\"selectedToolFilter === 'user-defined' ? 'primary' : 'secondary'\"\r\n      [pill]=\"true\"\r\n      size=\"small\"\r\n      (userClick)=\"showUserDefinedTools()\"\r\n    ></ava-button>\r\n  </div>\r\n\r\n  <div id=\"prompts-card-container\" class=\"row g-3\">\r\n    <!-- Hide Create New card when viewing Built In tools -->\r\n    <ava-text-card\r\n      *ngIf=\"selectedToolFilter !== 'built-in'\"\r\n      class=\"col-12 col-sm-6 col-md-4 col-lg-3 col-xl-3 col-xxl-2 mt-5\"\r\n      [type]=\"'create'\"\r\n      [iconName]=\"'plus'\"\r\n      iconColor=\"#144692\"\r\n      [title]=\"labels.createNew\"\r\n      (cardClick)=\"onCreateTool()\"\r\n      [isLoading]=\"isLoading\"\r\n    >\r\n    </ava-text-card>\r\n\r\n    <!-- No Results Message -->\r\n    <div\r\n      class=\"col-12 d-flex justify-content-center align-items-center py-5\"\r\n      *ngIf=\" !isLoading && filteredTools.length === 0\"\r\n    >\r\n      <div class=\"text-center\">\r\n        <h5 class=\"text-muted\">{{ labels.noResults }}</h5>\r\n      </div>\r\n    </div>\r\n\r\n  <ng-container *ngFor=\"let tool of isLoading && displayedTools.length === 0 ? cardSkeletonPlaceholders : displayedTools\">\r\n    <ava-console-card\r\n      class=\"col-12 col-sm-6 col-md-4 col-lg-3 col-xl-3 col-xxl-2 mt-5\"\r\n      [title]=\"tool?.title || 'No Title'\"\r\n      [description]=\"tool?.description || 'No Description'\"\r\n      categoryIcon=\"wrench\"\r\n      categoryTitle=\"{{tool?.isBuiltIn ? 'Built-in' : 'User-defined'}}\"\r\n      categoryValue=\"42\"\r\n      [author]=\"tool?.owner || 'AAVA'\"\r\n      [date]=\"tool?.createdDate | timeAgo\"\r\n      [actions]=\"tool?.isBuiltIn ? builtInActions : userDefinedActions\"\r\n      (actionClick)=\"onActionClick($event, tool.id)\"\r\n      [skeleton]=\"isLoading\"\r\n      [class.built-in-card]=\"!isLoading && tool.isBuiltIn\"\r\n      [class.user-defined-card]=\"!isLoading && !tool.isBuiltIn\"\r\n    >\r\n    </ava-console-card>\r\n  </ng-container>\r\n  </div>\r\n\r\n  <!-- Pagination Footer -->\r\n  <div class=\"row\" *ngIf=\"filteredTools.length > 0\">\r\n    <div class=\"col-12 d-flex justify-content-center mt-4\">\r\n      <app-page-footer\r\n        [totalItems]=\"getTotalItemsForPagination()\"\r\n        [currentPage]=\"currentPage\"\r\n        [itemsPerPage]=\"itemsPerPage\"\r\n        (pageChange)=\"onPageChange($event)\"\r\n      ></app-page-footer>\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n"], "mappings": "AACA,SAASA,YAAY,EAAEC,QAAQ,QAAQ,iBAAiB;AAExD,SAASC,mBAAmB,QAAQ,sDAAsD;AAG1F,SACEC,mBAAmB,EACnBC,iBAAiB,EAEjBC,iBAAiB,EACjBC,eAAe,EACfC,aAAa,QAER,wBAAwB;AAC/B,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,SAASC,UAAU,QAAQ,0BAA0B;AAErD,SAAiCC,mBAAmB,QAAQ,gBAAgB;AAC5E,SAASC,SAAS,EAAEC,YAAY,EAAEC,oBAAoB,QAAa,MAAM;AACzE,SAASC,0BAA0B,QAAQ,qDAAqD;AAEhG,SAA4BC,oBAAoB,QAAQ,wDAAwD;AAChH,SAASC,WAAW,QAAQ,6BAA6B;;;;;;;;;;;;;ICgCrDC,EAAA,CAAAC,cAAA,wBASC;IAFCD,EAAA,CAAAE,UAAA,uBAAAC,4EAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAaF,MAAA,CAAAG,YAAA,EAAc;IAAA,EAAC;IAG9BT,EAAA,CAAAU,YAAA,EAAgB;;;;IAFdV,EALA,CAAAW,UAAA,kBAAiB,oBACE,UAAAL,MAAA,CAAAM,MAAA,CAAAC,SAAA,CAEO,cAAAP,MAAA,CAAAQ,SAAA,CAEH;;;;;IAUrBd,EALJ,CAAAC,cAAA,cAGC,cAC0B,aACA;IAAAD,EAAA,CAAAe,MAAA,GAAsB;IAEjDf,EAFiD,CAAAU,YAAA,EAAK,EAC9C,EACF;;;;IAFqBV,EAAA,CAAAgB,SAAA,GAAsB;IAAtBhB,EAAA,CAAAiB,iBAAA,CAAAX,MAAA,CAAAM,MAAA,CAAAM,SAAA,CAAsB;;;;;;IAInDlB,EAAA,CAAAmB,uBAAA,GAAwH;IACtHnB,EAAA,CAAAC,cAAA,2BAcC;;IAJCD,EAAA,CAAAE,UAAA,yBAAAkB,gFAAAC,MAAA;MAAA,MAAAC,OAAA,GAAAtB,EAAA,CAAAI,aAAA,CAAAmB,GAAA,EAAAC,SAAA;MAAA,MAAAlB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAeF,MAAA,CAAAmB,aAAA,CAAAJ,MAAA,EAAAC,OAAA,CAAAI,EAAA,CAA8B;IAAA,EAAC;IAKhD1B,EAAA,CAAAU,YAAA,EAAmB;;;;;;IAHjBV,EAAA,CAAAgB,SAAA,EAAoD;IACpDhB,EADA,CAAA2B,WAAA,mBAAArB,MAAA,CAAAQ,SAAA,IAAAQ,OAAA,CAAAM,SAAA,CAAoD,uBAAAtB,MAAA,CAAAQ,SAAA,KAAAQ,OAAA,CAAAM,SAAA,CACK;IARzD5B,EAAA,CAAA6B,qBAAA,mBAAAP,OAAA,kBAAAA,OAAA,CAAAM,SAAA,gCAAiE;IAMjE5B,EATA,CAAAW,UAAA,WAAAW,OAAA,kBAAAA,OAAA,CAAAQ,KAAA,gBAAmC,iBAAAR,OAAA,kBAAAA,OAAA,CAAAS,WAAA,sBACkB,YAAAT,OAAA,kBAAAA,OAAA,CAAAU,KAAA,YAIrB,SAAAhC,EAAA,CAAAiC,WAAA,QAAAX,OAAA,kBAAAA,OAAA,CAAAY,WAAA,EACI,aAAAZ,OAAA,kBAAAA,OAAA,CAAAM,SAAA,IAAAtB,MAAA,CAAA6B,cAAA,GAAA7B,MAAA,CAAA8B,kBAAA,CAC6B,aAAA9B,MAAA,CAAAQ,SAAA,CAE3C;;;;;;IAWtBd,EAFJ,CAAAC,cAAA,cAAkD,cACO,0BAMpD;IADCD,EAAA,CAAAE,UAAA,wBAAAmC,qEAAAhB,MAAA;MAAArB,EAAA,CAAAI,aAAA,CAAAkC,GAAA;MAAA,MAAAhC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAcF,MAAA,CAAAiC,YAAA,CAAAlB,MAAA,CAAoB;IAAA,EAAC;IAGzCrB,EAFK,CAAAU,YAAA,EAAkB,EACf,EACF;;;;IANAV,EAAA,CAAAgB,SAAA,GAA2C;IAE3ChB,EAFA,CAAAW,UAAA,eAAAL,MAAA,CAAAkC,0BAAA,GAA2C,gBAAAlC,MAAA,CAAAmC,WAAA,CAChB,iBAAAnC,MAAA,CAAAoC,YAAA,CACE;;;ADxDrC,WAAaC,cAAc;EAArB,MAAOA,cAAc;IA0EfC,iBAAA;IACAC,MAAA;IACAC,KAAA;IACAC,YAAA;IACAC,QAAA;IACAC,aAAA;IACAC,EAAA;IACAC,YAAA;IACAC,eAAA;IAjFVC,QAAQ,GAAU,EAAE;IACpBC,YAAY,GAAU,EAAE;IACxBC,gBAAgB,GAAU,EAAE;IAC5BC,aAAa,GAAU,EAAE;IACzBC,cAAc,GAAU,EAAE;IAC1B3C,SAAS,GAAY,KAAK;IAC1B4C,KAAK,GAAkB,IAAI;IAC3BjB,WAAW,GAAW,CAAC;IACvBC,YAAY,GAAW,EAAE;IACzBiB,qBAAqB,GAAW,CAAC,CAAC,CAAC;IAEnC;IACAC,eAAe,GAAY,KAAK;IAChCC,YAAY,GAAQ,IAAI;IACxBC,oBAAoB,GAAW,EAAE;IACjCC,UAAU,GAAW,CAAC;IACfnD,MAAM,GAAQrB,SAAS,CAACqB,MAAM;IACrCoD,YAAY,GAAqB,CAC/B;MAAEC,IAAI,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAK,CAAE,EAC7B;MAAED,IAAI,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAO,CAAE,EAClC;MAAED,IAAI,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAO,CAAE,CACnC;IACDC,YAAY,GAAQ,IAAI;IACxB;IACA/B,kBAAkB,GAAwB,CACxC;MACEV,EAAE,EAAE,WAAW;MACf0C,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,WAAW;MAClBC,OAAO,EAAE;KACV,EACD;MACE5C,EAAE,EAAE,MAAM;MACV0C,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,WAAW;MAClBC,OAAO,EAAE;KACV,EACD;MACE5C,EAAE,EAAE,QAAQ;MACZ0C,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,aAAa;MACpBC,OAAO,EAAE;KACV,EACD;MACE5C,EAAE,EAAE,KAAK;MACT0C,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,iBAAiB;MACxBC,OAAO,EAAE,KAAK;MACdC,SAAS,EAAE;KACZ,CACF;IAED;IACApC,cAAc,GAAwB,CACpC;MACET,EAAE,EAAE,KAAK;MACT0C,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,iBAAiB;MACxBC,OAAO,EAAE,KAAK;MACdC,SAAS,EAAE;KACZ,CACF;IACDC,UAAU;IACVC,MAAM;IACNC,YAAY,GAAG,CAAC;MAAET,IAAI,EAAE,MAAM;MAAEU,QAAQ,EAAE,MAAM;MAAEC,MAAM,EAAE;IAAI,CAAE,CAAC;IAEjE;IACAC,aAAa,GAAsC,KAAK;IACxDC,kBAAkB,GAAwC,KAAK;IAC/D;IACAC,wBAAwB,GAAGC,KAAK,CAAC,EAAE,CAAC;IAEpCC,YACUrC,iBAAoC,EACpCC,MAAc,EACdC,KAAqB,EACrBC,YAA0B,EAC1BC,QAAkB,EAClBC,aAA4B,EAC5BC,EAAe,EACfC,YAAiC,EACjCC,eAAuC;MARvC,KAAAR,iBAAiB,GAAjBA,iBAAiB;MACjB,KAAAC,MAAM,GAANA,MAAM;MACN,KAAAC,KAAK,GAALA,KAAK;MACL,KAAAC,YAAY,GAAZA,YAAY;MACZ,KAAAC,QAAQ,GAARA,QAAQ;MACR,KAAAC,aAAa,GAAbA,aAAa;MACb,KAAAC,EAAE,GAAFA,EAAE;MACF,KAAAC,YAAY,GAAZA,YAAY;MACZ,KAAAC,eAAe,GAAfA,eAAe;MAEvB,IAAI,CAACoB,UAAU,GAAG,IAAI,CAACtB,EAAE,CAACgC,KAAK,CAAC;QAC9BT,MAAM,EAAE,CAAC,EAAE;OACZ,CAAC;IACJ;IAEQU,gBAAgBA,CAAA;MACtB,MAAMC,aAAa,GAAG,IAAI,CAACjC,YAAY,CAACkC,aAAa,EAAE,IAAI,uBAAuB;MAClF,OAAOD,aAAa;IACtB;IAEAE,QAAQA,CAAA;MACN,IAAI,CAACC,gBAAgB,EAAE;MACvB,IAAI,CAACC,WAAW,EAAE;MAClB;MACA,IAAI,CAAC7B,qBAAqB,GAAG,EAAE,CAAC,CAAC;MACjC,IAAI,CAAC8B,UAAU,EAAE;IACnB;IAEAF,gBAAgBA,CAAA;MACd;MACA,IAAI,CAACjC,YAAY,GAAG9D,UAAU,CAACkG,GAAG,CAAEC,IAAS,KAAM;QACjD,GAAGA,IAAI;QACPjE,EAAE,EAAE,WAAWiE,IAAI,CAACjE,EAAE,EAAE;QACxBI,KAAK,EAAE6D,IAAI,CAAC7D,KAAK;QACjBmC,IAAI,EAAE0B,IAAI,CAAC7D,KAAK;QAChBC,WAAW,EAAE4D,IAAI,CAAC5D,WAAW;QAC7BH,SAAS,EAAE,IAAI;QACfgE,QAAQ,EAAE,eAAe;QACzB5D,KAAK,EAAE,MAAM;QACbE,WAAW,EAAE;OACd,CAAC,CAAC;IACL;IAEAsD,WAAWA,CAACK,IAAA,GAAe,CAAC;MAC1B;MACA,IAAI,IAAI,CAAC/E,SAAS,EAAE;QAClB;MACF;MACA,IAAI,CAACA,SAAS,GAAG,IAAI;MACrB,IAAI,CAACiC,YAAY,CAAC+C,gBAAgB,CAACD,IAAI,CAAC,CAACE,SAAS,CAAC;QACjDC,IAAI,EAAGC,QAAa,IAAI;UACtB,IAAI,CAAC1C,gBAAgB,GAAG,CAAC0C,QAAQ,CAACC,eAAe,IAAI,EAAE,EAAER,GAAG,CAAEC,IAAS,KAAM;YAC3E,GAAGA,IAAI;YACPjE,EAAE,EAAEiE,IAAI,CAACjE,EAAE,CAACyE,QAAQ,EAAE;YACtBrE,KAAK,EAAE6D,IAAI,CAAC1B,IAAI;YAChBA,IAAI,EAAE0B,IAAI,CAAC1B,IAAI,IAAI,MAAM;YACzBlC,WAAW,EAAE4D,IAAI,CAAC5D,WAAW;YAC7BG,WAAW,EACT,IAAI,CAACc,QAAQ,CAACoD,SAAS,CAACT,IAAI,CAACU,SAAS,EAAE,YAAY,CAAC,IAAI,EAAE;YAC7DzE,SAAS,EAAE,KAAK;YAChBgE,QAAQ,EAAE,mBAAmB;YAC7BU,UAAU,EAAE,IAAI,CAAE;WACnB,CAAC,CAAC;UACH;UACA,IAAIL,QAAQ,CAACM,UAAU,IAAIN,QAAQ,CAACO,KAAK,EAAE;YACzC,IAAI,CAAC7C,qBAAqB,GAAGsC,QAAQ,CAACM,UAAU,IAAIN,QAAQ,CAACO,KAAK;UACpE,CAAC,MAAM;YACL;YACA,MAAMC,gBAAgB,GAAG,IAAI,CAAClD,gBAAgB,CAACmD,MAAM;YACrD,IAAID,gBAAgB,KAAK,EAAE,EAAE;cAC3B,IAAI,CAAC9C,qBAAqB,GAAGgD,IAAI,CAACC,GAAG,CAAC,IAAI,CAACjD,qBAAqB,EAAEkC,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;YACnF,CAAC,MAAM;cACL,IAAI,CAAClC,qBAAqB,GAAG,CAACkC,IAAI,GAAG,CAAC,IAAI,EAAE,GAAGY,gBAAgB;YACjE;UACF;UACA,IAAI,CAACpD,QAAQ,GAAG,CAAC,GAAG,IAAI,CAACC,YAAY,EAAE,GAAG,IAAI,CAACC,gBAAgB,CAAC;UAChE,QAAQ,IAAI,CAACsB,aAAa;YACxB,KAAK,KAAK;cACR,IAAI,CAACrB,aAAa,GAAG,CAAC,GAAG,IAAI,CAACF,YAAY,EAAE,GAAG,IAAI,CAACC,gBAAgB,CAAC;cACrE;YACF,KAAK,SAAS;cACZ,IAAI,CAACC,aAAa,GAAG,CAAC,GAAG,IAAI,CAACF,YAAY,CAAC;cAC3C;YACF,KAAK,aAAa;cAChB,IAAI,CAACE,aAAa,GAAG,CAAC,GAAG,IAAI,CAACD,gBAAgB,CAAC;cAC/C;UACJ;UACA,IAAI,CAACsD,oBAAoB,EAAE;UAC3B,IAAI,CAAC/F,SAAS,GAAG,KAAK;QACxB,CAAC;QACD4C,KAAK,EAAGA,KAAK,IAAI;UACfoD,OAAO,CAACpD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAC5C,IAAI,CAAC5C,SAAS,GAAG,KAAK;QACxB;OACD,CAAC;MACF,MAAMiG,SAAS,GAAG,IAAI,CAACjE,KAAK,CAACkE,QAAQ,CAACC,aAAa,CAACC,GAAG,CAAC,MAAM,CAAC;MAC/D,IAAIH,SAAS,EAAE;QACb,IAAI,CAACtE,WAAW,GAAG0E,QAAQ,CAACJ,SAAS,EAAE,EAAE,CAAC;MAC5C;IACF;IAEAtB,UAAUA,CAAA;MACR,IAAI,CAAC3E,SAAS,GAAG,IAAI;MACrB,IAAI,CAAC0D,UAAU,CAAC0C,GAAG,CAAC,QAAQ,CAAC,EAAEE,YAAY,CAACC,IAAI,CAC9C3H,SAAS,CAAC,EAAE,CAAC,EACbC,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,CACvB,CAACmG,SAAS,CAAEuB,UAAkB,IAAI;QACjC,MAAMC,OAAO,GAAGD,UAAU,EAAEE,IAAI,EAAE;QAElC,IAAI,CAACD,OAAO,EAAE;UACZ;UACA,IAAI,CAAC/D,aAAa,GAAG,CAAC,GAAG,IAAI,CAACF,YAAY,EAAE,GAAG,IAAI,CAACC,gBAAgB,CAAC;UACrE,IAAI,CAACsD,oBAAoB,EAAE;UAC3B;QACF;QAEA,IAAI,CAACzD,eAAe,CAACqE,aAAa,CAACF,OAAO,EAAE,OAAO,CAAC;MACtD,CAAC,CAAC;MAEF,IAAI,CAACnE,eAAe,CAACsE,cAAc,CAAC3B,SAAS,CAAE4B,OAAY,IAAI;QAE7D,MAAMC,UAAU,GAAG5C,KAAK,CAAC6C,OAAO,CAACF,OAAO,EAAEzB,eAAe,CAAC,GAAGyB,OAAO,CAACzB,eAAe,GAAG,EAAE;QAEzF,IAAI,CAAC1C,aAAa,GAAGoE,UAAU,CAAClC,GAAG,CAAEC,IAAS,KAAM;UAClDjE,EAAE,EAAEiE,IAAI,CAACjE,EAAE,EAAEyE,QAAQ,EAAE,IAAI,EAAE;UAC7BrE,KAAK,EAAE6D,IAAI,CAAC1B,IAAI,IAAI,UAAU;UAC9BA,IAAI,EAAE0B,IAAI,CAAC1B,IAAI,IAAI,MAAM;UACzBlC,WAAW,EAAE4D,IAAI,CAAC5D,WAAW,IAAI,gBAAgB;UACjDG,WAAW,EAAE,IAAI,CAACc,QAAQ,CAACoD,SAAS,CAACT,IAAI,CAACU,SAAS,EAAE,YAAY,CAAC,IAAI,KAAK;UAC3EzE,SAAS,EAAE+D,IAAI,CAAC/D,SAAS,IAAI,KAAK;UAClCgE,QAAQ,EAAED,IAAI,CAAC/D,SAAS,GAAG,eAAe,GAAG,mBAAmB;UAChEI,KAAK,EAAE2D,IAAI,CAACmC,SAAS,IAAI;SAC1B,CAAC,CAAC;QAEH,IAAI,CAACrE,cAAc,GAAG,IAAI,CAACD,aAAa;QACxC,IAAI,CAAC1C,SAAS,GAAG,KAAK;MACxB,CAAC,CAAC;IACJ;IAEAiH,WAAWA,CAACT,UAAkB;MAC5B;MACA,IAAI,CAAC7E,WAAW,GAAG,CAAC;MAEpB,IAAI,CAACe,aAAa,GAAG,IAAI,CAACH,QAAQ,CAAC2E,MAAM,CAAErC,IAAI,IAAI;QACjD,MAAMsC,OAAO,GAAGtC,IAAI,CAAC7D,KAAK,EAAEoG,WAAW,EAAE,CAACC,QAAQ,CAACb,UAAU,CAAC;QAC9D,MAAMc,aAAa,GAAGzC,IAAI,CAAC5D,WAAW,EAClCmG,WAAW,EAAE,CACdC,QAAQ,CAACb,UAAU,CAAC;QACvB,MAAMe,MAAM,GACVrD,KAAK,CAAC6C,OAAO,CAAClC,IAAI,CAAC2C,IAAI,CAAC,IACxB3C,IAAI,CAAC2C,IAAI,EAAEC,IAAI,CAAEC,GAAQ,IACvBA,GAAG,CAACnE,KAAK,EAAE6D,WAAW,EAAE,CAACC,QAAQ,CAACb,UAAU,CAAC,CAC9C;QAEH,OAAOW,OAAO,IAAIG,aAAa,IAAIC,MAAM;MAC3C,CAAC,CAAC;MAEF,IAAI,CAACxB,oBAAoB,EAAE;IAC7B;IAEA4B,mBAAmBA,CAAA;MACjB,QAAQ,IAAI,CAAC5D,aAAa;QACxB,KAAK,KAAK;UACR,IAAI,CAACrB,aAAa,GAAG,CAAC,GAAG,IAAI,CAACF,YAAY,EAAE,GAAG,IAAI,CAACC,gBAAgB,CAAC;UACrE;QACF,KAAK,SAAS;UACZ,IAAI,CAACC,aAAa,GAAG,CAAC,GAAG,IAAI,CAACF,YAAY,CAAC;UAC3C;QACF,KAAK,aAAa;UAChB,IAAI,CAACE,aAAa,GAAG,CAAC,GAAG,IAAI,CAACD,gBAAgB,CAAC;UAC/C;MACJ;MACA,IAAI,CAACsD,oBAAoB,EAAE;IAC7B;IAEAA,oBAAoBA,CAAA;MAClB,IAAI,IAAI,CAAChC,aAAa,KAAK,SAAS,EAAE;QACpC,MAAM6D,UAAU,GAAG,CAAC,IAAI,CAACjG,WAAW,GAAG,CAAC,IAAI,IAAI,CAACC,YAAY;QAC7D,MAAMiG,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAAChG,YAAY;QAC/C,IAAI,CAACe,cAAc,GAAG,IAAI,CAACD,aAAa,CAACoF,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;QACpE,IAAI,CAAC5E,UAAU,GAAG4C,IAAI,CAACkC,IAAI,CAAC,IAAI,CAACrF,aAAa,CAACkD,MAAM,GAAG,IAAI,CAAChE,YAAY,CAAC;MAC5E,CAAC,MAAM;QACL;QACA,IAAI,CAACe,cAAc,GAAG,IAAI,CAACD,aAAa;QACxC;QACA,IAAI,IAAI,CAACqB,aAAa,KAAK,aAAa,EAAE;UACxC,IAAI,CAACd,UAAU,GAAG4C,IAAI,CAACkC,IAAI,CAAC,IAAI,CAAClF,qBAAqB,GAAG,EAAE,CAAC,CAAC,CAAC;QAChE,CAAC,MAAM,IAAI,IAAI,CAACkB,aAAa,KAAK,KAAK,EAAE;UACvC;UACA,MAAMiE,UAAU,GAAG,IAAI,CAACxF,YAAY,CAACoD,MAAM,GAAG,IAAI,CAAC/C,qBAAqB;UACxE,IAAI,CAACI,UAAU,GAAG4C,IAAI,CAACkC,IAAI,CAACC,UAAU,GAAG,IAAI,CAACpG,YAAY,CAAC;QAC7D;MACF;IACF;IAEAjC,YAAYA,CAAA;MACV,IAAI,CAACoC,MAAM,CAACkG,QAAQ,CAAC,CAAC,yBAAyB,CAAC,CAAC;IACnD;IAEAC,aAAaA,CAACC,MAAc;MAC1B,MAAMtD,IAAI,GAAG,IAAI,CAACnC,aAAa,CAAC0F,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACzH,EAAE,KAAKuH,MAAM,CAAC;MAC5D,IAAItD,IAAI,IAAIA,IAAI,CAAC/D,SAAS,EAAE;QAC1B;QACA,MAAMwH,WAAW,GAAG5J,UAAU,CAAC0J,IAAI,CAChCC,CAAC,IAAKA,CAAC,CAACzH,EAAE,KAAKiE,IAAI,CAACjE,EAAE,CAAC2H,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAChD;QACD,IAAI,CAACpG,aAAa,CAACqG,IAAI,CAACzJ,0BAA0B,EAAE;UAClD0J,YAAY,EAAEH,WAAW;UACzBI,YAAY,EAAEA,CAAA,KAAM,IAAI,CAACvG,aAAa,CAACwG,KAAK;SAC7C,CAAC;QACF;MACF,CAAC,MAAM,IAAI9D,IAAI,EAAE;QACf,IAAI,CAAC9C,MAAM,CAACkG,QAAQ,CAAC,CAAC,uBAAuB,EAAEpD,IAAI,CAACjE,EAAE,CAAC,EAAE;UACvDgI,WAAW,EAAE;YAAEC,UAAU,EAAE,IAAI,CAAClH;UAAW;SAC5C,CAAC;MACJ;IACF;IAEA;IACAmH,UAAUA,CAACX,MAAc;MACvB,MAAMtD,IAAI,GAAG,IAAI,CAACnC,aAAa,CAAC0F,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACzH,EAAE,KAAKuH,MAAM,CAAC;MAC5D,IAAItD,IAAI,EAAE;QACR,IAAI,CAAC9C,MAAM,CAACkG,QAAQ,CAAC,CAAC,uBAAuB,EAAEpD,IAAI,CAACjE,EAAE,CAAC,EAAE;UACvDgI,WAAW,EAAE;YAAEC,UAAU,EAAE,IAAI,CAAClH;UAAW;SAC5C,CAAC;MACJ;IACF;IAEAoH,cAAcA,CAAClE,IAAS;MACtB;MACA,MAAMmE,KAAK,GAAG,CACZ;QAAEnF,QAAQ,EAAE,QAAQ;QAAE7C,KAAK,EAAE6D,IAAI,CAACC,QAAQ,IAAI;MAAM,CAAE,EACtD;QACEjB,QAAQ,EAAE,OAAO;QACjB7C,KAAK,EAAE6D,IAAI,CAACoE,SAAS,GAAGC,MAAM,CAACrE,IAAI,CAACoE,SAAS,CAAC,GAAG;OAClD,CACF;MACD,OAAOD,KAAK;IACd;IAEAG,cAAcA,CAACtE,IAAS;MACtB;MACA,MAAMmE,KAAK,GAAG,CACZ;QAAEnF,QAAQ,EAAE,MAAM;QAAE7C,KAAK,EAAE6D,IAAI,CAAC3D,KAAK,IAAI;MAAM,CAAE,EACjD;QACE2C,QAAQ,EAAE,eAAe;QACzB7C,KAAK,EAAE6D,IAAI,CAACzD,WAAW,IAAI;OAC5B,CACF;MACD,OAAO4H,KAAK;IACd;IAEArI,aAAaA,CACXyI,KAAsD,EACtDjB,MAAc;MAEd,IAAItD,IAAI,GAAG,IAAI,CAACnC,aAAa,CAAC0F,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACzH,EAAE,KAAKuH,MAAM,CAAC;MAC1D,IAAKiB,KAAK,CAACC,QAAQ,KAAM,KAAK,EAAE;QAC9B,IAAIxE,IAAI,IAAIA,IAAI,CAAC/D,SAAS,EAAE;UAC1B;UACA,MAAMwH,WAAW,GAAG5J,UAAU,CAAC0J,IAAI,CAChCC,CAAC,IAAKA,CAAC,CAACzH,EAAE,KAAKiE,IAAI,CAACjE,EAAE,CAAC2H,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAChD;UACD,IAAI,CAACpG,aAAa,CAACqG,IAAI,CAACzJ,0BAA0B,EAAE;YAClD0J,YAAY,EAAEH,WAAW;YACzBI,YAAY,EAAEA,CAAA,KAAM,IAAI,CAACvG,aAAa,CAACwG,KAAK;WAC7C,CAAC;QACJ,CAAC,MAAM;UACL;UACA,IAAI,CAACW,WAAW,CAACnB,MAAM,CAAC;QAC1B;QACA;MACF;MACA,IAAI,CAACtD,IAAI,IAAIA,IAAI,CAAC/D,SAAS,EAAE,OAAO,CAAC;MACrC,QAAQsI,KAAK,CAACC,QAAQ;QACpB,KAAK,QAAQ;UACX,IAAI,CAACE,iBAAiB,CAACpB,MAAM,CAAC;UAC9B;QACF,KAAK,KAAK;UACR,IAAI,CAACmB,WAAW,CAACnB,MAAM,CAAC;UACxB;QACF,KAAK,WAAW;UACd,IAAI,CAACqB,SAAS,CAACrB,MAAM,CAAC;UACtB;QACF,KAAK,MAAM;UACT,IAAI,CAACsB,QAAQ,CAACtB,MAAM,CAAC;UACrB;QACF;UACE;MACJ;IACF;IAEAsB,QAAQA,CAACtB,MAAc;MACrB,IAAI,CAACpG,MAAM,CAACkG,QAAQ,CAAC,CAAC,uBAAuB,EAAEE,MAAM,CAAC,EAAE;QACtDS,WAAW,EAAE;UAAEC,UAAU,EAAE,IAAI,CAAClH;QAAW;OAC5C,CAAC;IACJ;IAEA4H,iBAAiBA,CAACpB,MAAc;MAC9B,IAAI,CAACrF,eAAe,GAAG,IAAI;MAC3B,IAAI,CAACC,YAAY,GAAG,IAAI,CAACR,QAAQ,CAAC6F,IAAI,CACnCsB,IAAI,IAAKA,IAAI,CAAC9I,EAAE,KAAKuH,MAAM,CAC7B;MACD,IAAI,CAACnF,oBAAoB,GAAG,IAAI,CAACqB,gBAAgB,EAAE;IACrD;IAEAsF,eAAeA,CAAA;MACb,IAAI,IAAI,CAAC5G,YAAY,EAAE;QACrB,MAAMuB,aAAa,GAAG,IAAI,CAACD,gBAAgB,EAAE;QAC7C,MAAM8D,MAAM,GAAG,OAAO,IAAI,CAACpF,YAAY,CAACnC,EAAE,KAAK,QAAQ,GACnDyF,QAAQ,CAAC,IAAI,CAACtD,YAAY,CAACnC,EAAE,EAAE,EAAE,CAAC,GAClC,IAAI,CAACmC,YAAY,CAACnC,EAAE;QACxB,IAAI,CAACqB,YAAY,CAAC2H,UAAU,CAACzB,MAAM,EAAE7D,aAAa,CAAC,CAACW,SAAS,CAAC;UAC5DC,IAAI,EAAG2E,GAAG,IAAI;YACZ;YACA,MAAMC,SAAS,GAAG,IAAI,CAAC/G,YAAY,GAAG,IAAI,CAACA,YAAY,CAACnC,EAAE,GAAG,IAAI;YACjE,IAAI,CAACmJ,gBAAgB,EAAE;YACvB,IAAID,SAAS,KAAK,IAAI,EAAE;cACtB,IAAI,CAACvH,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC2E,MAAM,CAACrC,IAAI,IAAIA,IAAI,IAAIA,IAAI,CAACjE,EAAE,KAAKkJ,SAAS,CAAC;cAC3E,IAAI,CAACrH,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAACyE,MAAM,CAACrC,IAAI,IAAIA,IAAI,IAAIA,IAAI,CAACjE,EAAE,KAAKkJ,SAAS,CAAC;YAC7F;YACA,IAAI,CAACpF,WAAW,CAAC,IAAI,CAAC/C,WAAW,CAAC;UACpC,CAAC;UACDiB,KAAK,EAAGoH,GAAG,IAAI;YACbC,KAAK,CAAC,0CAA0C,CAAC;YACjD,IAAI,CAACF,gBAAgB,EAAE;UACzB;SACD,CAAC;MACJ;IACF;IAEAA,gBAAgBA,CAAA;MACd,IAAI,CAACjH,eAAe,GAAG,KAAK;MAC5B,IAAI,CAACC,YAAY,GAAG,IAAI;IAC1B;IAEAmH,aAAaA,CAAC/B,MAAc;MAC1B;IAAA;IAGFmB,WAAWA,CAACnB,MAAc;MACxB,IAAI,CAACpG,MAAM,CAACkG,QAAQ,CAAC,CAAC,0BAA0B,EAAEE,MAAM,CAAC,EAAE;QACzDS,WAAW,EAAE;UAAEC,UAAU,EAAE,IAAI,CAAClH;QAAW;OAC5C,CAAC;IACJ;IAEA6H,SAASA,CAACrB,MAAc;MACtB,IAAI,CAACpG,MAAM,CAACkG,QAAQ,CAAC,CAAC,wBAAwB,EAAEE,MAAM,CAAC,CAAC;IAC1D;IAEAgC,iBAAiBA,CAACC,IAAS;MACzB,IAAI,CAAC/G,YAAY,GAAG+G,IAAI;MACxB;IACF;IAEA3I,YAAYA,CAACsD,IAAY;MACvB,IAAI,CAACpD,WAAW,GAAGoD,IAAI;MAEvB;MACA,IAAI,IAAI,CAAChB,aAAa,KAAK,aAAa,IAAI,IAAI,CAACA,aAAa,KAAK,KAAK,EAAE;QACxE,IAAI,CAACW,WAAW,CAACK,IAAI,CAAC;MACxB,CAAC,MAAM;QACL;QACA,IAAI,CAACgB,oBAAoB,EAAE;MAC7B;IACF;IAEA,IAAIsE,cAAcA,CAAA;MAChB;MACA,OACE,IAAI,CAAC1I,WAAW,KAAK,CAAC,IACtB,CAAC,IAAI,CAAC3B,SAAS,IACf,CAAC,IAAI,CAAC4C,KAAK,KACV,IAAI,CAACmB,aAAa,KAAK,aAAa,IAAI,IAAI,CAACA,aAAa,KAAK,KAAK,CAAC;IAE1E;IAEArC,0BAA0BA,CAAA;MACxB,IAAIsG,UAAkB;MACtB,QAAQ,IAAI,CAACjE,aAAa;QACxB,KAAK,SAAS;UACZiE,UAAU,GAAG,IAAI,CAACxF,YAAY,CAACoD,MAAM,CAAC,CAAC;UACvC;QACF,KAAK,aAAa;UAChB;UACA,IAAI,IAAI,CAACnD,gBAAgB,CAACmD,MAAM,KAAK,EAAE,EAAE;YACvCoC,UAAU,GAAGnC,IAAI,CAACC,GAAG,CAAC,IAAI,CAACjD,qBAAqB,EAAE,IAAI,CAAClB,WAAW,GAAG,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC;UACnF,CAAC,MAAM;YACLqG,UAAU,GAAG,IAAI,CAACnF,qBAAqB,GAAG,CAAC;UAC7C;UACA;QACF,KAAK,KAAK;UACRmF,UAAU,GAAG,IAAI,CAACxF,YAAY,CAACoD,MAAM,GAAG,IAAI,CAAC/C,qBAAqB,GAAG,CAAC,CAAC,CAAC;UACxE;QACF;UACEmF,UAAU,GAAG,IAAI,CAACtF,aAAa,CAACkD,MAAM,GAAG,CAAC;MAC9C;MAEA,OAAOoC,UAAU;IACnB;IAEA;IACAsC,YAAYA,CAAA;MACV,IAAI,CAACtG,kBAAkB,GAAG,KAAK;MAC/B,IAAI,CAACD,aAAa,GAAG,KAAK;MAC1B,IAAI,CAACpC,WAAW,GAAG,CAAC,CAAC,CAAC;MACtB,IAAI,CAACe,aAAa,GAAG,CAAC,GAAG,IAAI,CAACF,YAAY,EAAE,GAAG,IAAI,CAACC,gBAAgB,CAAC;MACrE,IAAI,CAACsD,oBAAoB,EAAE;IAC7B;IAEAwE,gBAAgBA,CAAA;MACd,IAAI,CAACvG,kBAAkB,GAAG,UAAU;MACpC,IAAI,CAACD,aAAa,GAAG,SAAS;MAC9B,IAAI,CAACpC,WAAW,GAAG,CAAC,CAAC,CAAC;MACtB,IAAI,CAACe,aAAa,GAAG,CAAC,GAAG,IAAI,CAACF,YAAY,CAAC;MAC3C,IAAI,CAACuD,oBAAoB,EAAE;IAC7B;IAEAyE,oBAAoBA,CAAA;MAClB,IAAI,CAACxG,kBAAkB,GAAG,cAAc;MACxC,IAAI,CAACD,aAAa,GAAG,aAAa;MAClC,IAAI,CAACpC,WAAW,GAAG,CAAC,CAAC,CAAC;MACtB,IAAI,CAACe,aAAa,GAAG,CAAC,GAAG,IAAI,CAACD,gBAAgB,CAAC;MAC/C,IAAI,CAACsD,oBAAoB,EAAE;IAC7B;IAEA;IACA0E,gBAAgBA,CAAA;MACd,IAAI,CAACF,gBAAgB,EAAE;IACzB;IAEAG,aAAaA,CAACtB,KAAU;MACtB;IAAA;;uCA3fSvH,cAAc,EAAA3C,EAAA,CAAAyL,iBAAA,CAAAC,EAAA,CAAAC,iBAAA,GAAA3L,EAAA,CAAAyL,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAA7L,EAAA,CAAAyL,iBAAA,CAAAG,EAAA,CAAAE,cAAA,GAAA9L,EAAA,CAAAyL,iBAAA,CAAAM,EAAA,CAAAC,YAAA,GAAAhM,EAAA,CAAAyL,iBAAA,CAAAQ,EAAA,CAAAlN,QAAA,GAAAiB,EAAA,CAAAyL,iBAAA,CAAAS,EAAA,CAAAC,aAAA,GAAAnM,EAAA,CAAAyL,iBAAA,CAAAW,EAAA,CAAAC,WAAA,GAAArM,EAAA,CAAAyL,iBAAA,CAAAa,EAAA,CAAAC,mBAAA,GAAAvM,EAAA,CAAAyL,iBAAA,CAAAe,EAAA,CAAAC,sBAAA;IAAA;;YAAd9J,cAAc;MAAA+J,SAAA;MAAAC,QAAA,GAAA3M,EAAA,CAAA4M,kBAAA,CAJd,CAAC7N,QAAQ,CAAC;MAAA8N,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCxCflN,EAJR,CAAAC,cAAA,aAAkD,aACE,aACe,cAC9B,qBAM5B;UACCD,EAAA,CAAAoN,SAAA,kBAMW;UAGjBpN,EAFI,CAAAU,YAAA,EAAc,EACT,EACH;UAEJV,EADF,CAAAC,cAAA,aAA8D,sBAK3D;UADCD,EAAA,CAAAE,UAAA,6BAAAmN,gEAAAhM,MAAA;YAAA,OAAmB8L,GAAA,CAAAlC,iBAAA,CAAA5J,MAAA,CAAyB;UAAA,EAAC;UAInDrB,EAFI,CAAAU,YAAA,EAAe,EACX,EACF;UAGJV,EADF,CAAAC,cAAA,aAA4B,oBAOzB;UADCD,EAAA,CAAAE,UAAA,uBAAAoN,wDAAA;YAAA,OAAaH,GAAA,CAAA/B,YAAA,EAAc;UAAA,EAAC;UAC7BpL,EAAA,CAAAU,YAAA,EAAa;UACdV,EAAA,CAAAC,cAAA,sBAMC;UADCD,EAAA,CAAAE,UAAA,uBAAAqN,yDAAA;YAAA,OAAaJ,GAAA,CAAA9B,gBAAA,EAAkB;UAAA,EAAC;UACjCrL,EAAA,CAAAU,YAAA,EAAa;UACdV,EAAA,CAAAC,cAAA,sBAMC;UADCD,EAAA,CAAAE,UAAA,uBAAAsN,yDAAA;YAAA,OAAaL,GAAA,CAAA7B,oBAAA,EAAsB;UAAA,EAAC;UAExCtL,EADG,CAAAU,YAAA,EAAa,EACV;UAENV,EAAA,CAAAC,cAAA,eAAiD;UAwBjDD,EAtBE,CAAAyN,UAAA,KAAAC,wCAAA,4BASC,KAAAC,8BAAA,kBAOA,KAAAC,uCAAA,4BAMqH;UAkBxH5N,EAAA,CAAAU,YAAA,EAAM;UAGNV,EAAA,CAAAyN,UAAA,KAAAI,8BAAA,kBAAkD;UAUpD7N,EAAA,CAAAU,YAAA,EAAM;;;UA1GMV,EAAA,CAAAgB,SAAA,GAAwB;UAAxBhB,EAAA,CAAAW,UAAA,cAAAwM,GAAA,CAAA3I,UAAA,CAAwB;UAUxBxE,EAAA,CAAAgB,SAAA,GAAe;UAAfhB,EAAA,CAAAW,UAAA,gBAAe;UAUnBX,EAAA,CAAAgB,SAAA,GAAwB;UAAxBhB,EAAA,CAAAW,UAAA,YAAAwM,GAAA,CAAAnJ,YAAA,CAAwB;UAU1BhE,EAAA,CAAAgB,SAAA,GAAkE;UAClEhB,EADA,CAAAW,UAAA,YAAAwM,GAAA,CAAArI,kBAAA,qCAAkE,cACrD;UAMb9E,EAAA,CAAAgB,SAAA,EAAuE;UACvEhB,EADA,CAAAW,UAAA,YAAAwM,GAAA,CAAArI,kBAAA,0CAAuE,cAC1D;UAMb9E,EAAA,CAAAgB,SAAA,EAA2E;UAC3EhB,EADA,CAAAW,UAAA,YAAAwM,GAAA,CAAArI,kBAAA,8CAA2E,cAC9D;UASZ9E,EAAA,CAAAgB,SAAA,GAAuC;UAAvChB,EAAA,CAAAW,UAAA,SAAAwM,GAAA,CAAArI,kBAAA,gBAAuC;UAcvC9E,EAAA,CAAAgB,SAAA,EAAgD;UAAhDhB,EAAA,CAAAW,UAAA,UAAAwM,GAAA,CAAArM,SAAA,IAAAqM,GAAA,CAAA3J,aAAA,CAAAkD,MAAA,OAAgD;UAOtB1G,EAAA,CAAAgB,SAAA,EAAuF;UAAvFhB,EAAA,CAAAW,UAAA,YAAAwM,GAAA,CAAArM,SAAA,IAAAqM,GAAA,CAAA1J,cAAA,CAAAiD,MAAA,SAAAyG,GAAA,CAAApI,wBAAA,GAAAoI,GAAA,CAAA1J,cAAA,CAAuF;UAqBpGzD,EAAA,CAAAgB,SAAA,EAA8B;UAA9BhB,EAAA,CAAAW,UAAA,SAAAwM,GAAA,CAAA3J,aAAA,CAAAkD,MAAA,KAA8B;;;qBDpE9C5H,YAAY,EAAAmN,EAAA,CAAA6B,OAAA,EAAA7B,EAAA,CAAA8B,IAAA,EACZ/O,mBAAmB,EACnBG,iBAAiB,EACjBF,mBAAmB,EACnBC,iBAAiB,EACjBI,mBAAmB,EACnBF,eAAe,EACfC,aAAa,EAEbI,mBAAmB,EAAA2M,EAAA,CAAA4B,aAAA,EAAA5B,EAAA,CAAA6B,eAAA,EAAA7B,EAAA,CAAA8B,oBAAA,EAAA9B,EAAA,CAAA+B,kBAAA,EAAA/B,EAAA,CAAAgC,eAAA,EACnBtO,oBAAoB,EACpBC,WAAW;MAAAsO,MAAA;IAAA;;SAMF1L,cAAc;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}