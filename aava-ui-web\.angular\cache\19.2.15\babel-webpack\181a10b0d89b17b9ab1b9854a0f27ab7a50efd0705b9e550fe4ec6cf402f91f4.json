{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = this && this.__param || function (paramIndex, decorator) {\n  return function (target, key) {\n    decorator(target, key, paramIndex);\n  };\n};\nimport './hover.css';\nimport { DisposableStore } from '../../../../base/common/lifecycle.js';\nimport { Emitter } from '../../../../base/common/event.js';\nimport * as dom from '../../../../base/browser/dom.js';\nimport { IKeybindingService } from '../../../../platform/keybinding/common/keybinding.js';\nimport { IConfigurationService } from '../../../../platform/configuration/common/configuration.js';\nimport { EDITOR_FONT_DEFAULTS } from '../../../common/config/editorOptions.js';\nimport { HoverAction, HoverWidget as BaseHoverWidget, getHoverAccessibleViewHint } from '../../../../base/browser/ui/hover/hoverWidget.js';\nimport { Widget } from '../../../../base/browser/ui/widget.js';\nimport { IOpenerService } from '../../../../platform/opener/common/opener.js';\nimport { IInstantiationService } from '../../../../platform/instantiation/common/instantiation.js';\nimport { MarkdownRenderer, openLinkFromMarkdown } from '../../widget/markdownRenderer/browser/markdownRenderer.js';\nimport { isMarkdownString } from '../../../../base/common/htmlContent.js';\nimport { localize } from '../../../../nls.js';\nimport { isMacintosh } from '../../../../base/common/platform.js';\nimport { IAccessibilityService } from '../../../../platform/accessibility/common/accessibility.js';\nimport { status } from '../../../../base/browser/ui/aria/aria.js';\nconst $ = dom.$;\nlet HoverWidget = class HoverWidget extends Widget {\n  get _targetWindow() {\n    return dom.getWindow(this._target.targetElements[0]);\n  }\n  get _targetDocumentElement() {\n    return dom.getWindow(this._target.targetElements[0]).document.documentElement;\n  }\n  get isDisposed() {\n    return this._isDisposed;\n  }\n  get isMouseIn() {\n    return this._lockMouseTracker.isMouseIn;\n  }\n  get domNode() {\n    return this._hover.containerDomNode;\n  }\n  get onDispose() {\n    return this._onDispose.event;\n  }\n  get onRequestLayout() {\n    return this._onRequestLayout.event;\n  }\n  get anchor() {\n    return this._hoverPosition === 2 /* HoverPosition.BELOW */ ? 0 /* AnchorPosition.BELOW */ : 1 /* AnchorPosition.ABOVE */;\n  }\n  get x() {\n    return this._x;\n  }\n  get y() {\n    return this._y;\n  }\n  /**\n   * Whether the hover is \"locked\" by holding the alt/option key. When locked, the hover will not\n   * hide and can be hovered regardless of whether the `hideOnHover` hover option is set.\n   */\n  get isLocked() {\n    return this._isLocked;\n  }\n  set isLocked(value) {\n    if (this._isLocked === value) {\n      return;\n    }\n    this._isLocked = value;\n    this._hoverContainer.classList.toggle('locked', this._isLocked);\n  }\n  constructor(options, _keybindingService, _configurationService, _openerService, _instantiationService, _accessibilityService) {\n    super();\n    this._keybindingService = _keybindingService;\n    this._configurationService = _configurationService;\n    this._openerService = _openerService;\n    this._instantiationService = _instantiationService;\n    this._accessibilityService = _accessibilityService;\n    this._messageListeners = new DisposableStore();\n    this._isDisposed = false;\n    this._forcePosition = false;\n    this._x = 0;\n    this._y = 0;\n    this._isLocked = false;\n    this._enableFocusTraps = false;\n    this._addedFocusTrap = false;\n    this._onDispose = this._register(new Emitter());\n    this._onRequestLayout = this._register(new Emitter());\n    this._linkHandler = options.linkHandler || (url => {\n      return openLinkFromMarkdown(this._openerService, url, isMarkdownString(options.content) ? options.content.isTrusted : undefined);\n    });\n    this._target = 'targetElements' in options.target ? options.target : new ElementHoverTarget(options.target);\n    this._hoverPointer = options.appearance?.showPointer ? $('div.workbench-hover-pointer') : undefined;\n    this._hover = this._register(new BaseHoverWidget());\n    this._hover.containerDomNode.classList.add('workbench-hover', 'fadeIn');\n    if (options.appearance?.compact) {\n      this._hover.containerDomNode.classList.add('workbench-hover', 'compact');\n    }\n    if (options.appearance?.skipFadeInAnimation) {\n      this._hover.containerDomNode.classList.add('skip-fade-in');\n    }\n    if (options.additionalClasses) {\n      this._hover.containerDomNode.classList.add(...options.additionalClasses);\n    }\n    if (options.position?.forcePosition) {\n      this._forcePosition = true;\n    }\n    if (options.trapFocus) {\n      this._enableFocusTraps = true;\n    }\n    this._hoverPosition = options.position?.hoverPosition ?? 3 /* HoverPosition.ABOVE */;\n    // Don't allow mousedown out of the widget, otherwise preventDefault will call and text will\n    // not be selected.\n    this.onmousedown(this._hover.containerDomNode, e => e.stopPropagation());\n    // Hide hover on escape\n    this.onkeydown(this._hover.containerDomNode, e => {\n      if (e.equals(9 /* KeyCode.Escape */)) {\n        this.dispose();\n      }\n    });\n    // Hide when the window loses focus\n    this._register(dom.addDisposableListener(this._targetWindow, 'blur', () => this.dispose()));\n    const rowElement = $('div.hover-row.markdown-hover');\n    const contentsElement = $('div.hover-contents');\n    if (typeof options.content === 'string') {\n      contentsElement.textContent = options.content;\n      contentsElement.style.whiteSpace = 'pre-wrap';\n    } else if (dom.isHTMLElement(options.content)) {\n      contentsElement.appendChild(options.content);\n      contentsElement.classList.add('html-hover-contents');\n    } else {\n      const markdown = options.content;\n      const mdRenderer = this._instantiationService.createInstance(MarkdownRenderer, {\n        codeBlockFontFamily: this._configurationService.getValue('editor').fontFamily || EDITOR_FONT_DEFAULTS.fontFamily\n      });\n      const {\n        element\n      } = mdRenderer.render(markdown, {\n        actionHandler: {\n          callback: content => this._linkHandler(content),\n          disposables: this._messageListeners\n        },\n        asyncRenderCallback: () => {\n          contentsElement.classList.add('code-hover-contents');\n          this.layout();\n          // This changes the dimensions of the hover so trigger a layout\n          this._onRequestLayout.fire();\n        }\n      });\n      contentsElement.appendChild(element);\n    }\n    rowElement.appendChild(contentsElement);\n    this._hover.contentsDomNode.appendChild(rowElement);\n    if (options.actions && options.actions.length > 0) {\n      const statusBarElement = $('div.hover-row.status-bar');\n      const actionsElement = $('div.actions');\n      options.actions.forEach(action => {\n        const keybinding = this._keybindingService.lookupKeybinding(action.commandId);\n        const keybindingLabel = keybinding ? keybinding.getLabel() : null;\n        HoverAction.render(actionsElement, {\n          label: action.label,\n          commandId: action.commandId,\n          run: e => {\n            action.run(e);\n            this.dispose();\n          },\n          iconClass: action.iconClass\n        }, keybindingLabel);\n      });\n      statusBarElement.appendChild(actionsElement);\n      this._hover.containerDomNode.appendChild(statusBarElement);\n    }\n    this._hoverContainer = $('div.workbench-hover-container');\n    if (this._hoverPointer) {\n      this._hoverContainer.appendChild(this._hoverPointer);\n    }\n    this._hoverContainer.appendChild(this._hover.containerDomNode);\n    // Determine whether to hide on hover\n    let hideOnHover;\n    if (options.actions && options.actions.length > 0) {\n      // If there are actions, require hover so they can be accessed\n      hideOnHover = false;\n    } else {\n      if (options.persistence?.hideOnHover === undefined) {\n        // When unset, will default to true when it's a string or when it's markdown that\n        // appears to have a link using a naive check for '](' and '</a>'\n        hideOnHover = typeof options.content === 'string' || isMarkdownString(options.content) && !options.content.value.includes('](') && !options.content.value.includes('</a>');\n      } else {\n        // It's set explicitly\n        hideOnHover = options.persistence.hideOnHover;\n      }\n    }\n    // Show the hover hint if needed\n    if (options.appearance?.showHoverHint) {\n      const statusBarElement = $('div.hover-row.status-bar');\n      const infoElement = $('div.info');\n      infoElement.textContent = localize('hoverhint', 'Hold {0} key to mouse over', isMacintosh ? 'Option' : 'Alt');\n      statusBarElement.appendChild(infoElement);\n      this._hover.containerDomNode.appendChild(statusBarElement);\n    }\n    const mouseTrackerTargets = [...this._target.targetElements];\n    if (!hideOnHover) {\n      mouseTrackerTargets.push(this._hoverContainer);\n    }\n    const mouseTracker = this._register(new CompositeMouseTracker(mouseTrackerTargets));\n    this._register(mouseTracker.onMouseOut(() => {\n      if (!this._isLocked) {\n        this.dispose();\n      }\n    }));\n    // Setup another mouse tracker when hideOnHover is set in order to track the hover as well\n    // when it is locked. This ensures the hover will hide on mouseout after alt has been\n    // released to unlock the element.\n    if (hideOnHover) {\n      const mouseTracker2Targets = [...this._target.targetElements, this._hoverContainer];\n      this._lockMouseTracker = this._register(new CompositeMouseTracker(mouseTracker2Targets));\n      this._register(this._lockMouseTracker.onMouseOut(() => {\n        if (!this._isLocked) {\n          this.dispose();\n        }\n      }));\n    } else {\n      this._lockMouseTracker = mouseTracker;\n    }\n  }\n  addFocusTrap() {\n    if (!this._enableFocusTraps || this._addedFocusTrap) {\n      return;\n    }\n    this._addedFocusTrap = true;\n    // Add a hover tab loop if the hover has at least one element with a valid tabIndex\n    const firstContainerFocusElement = this._hover.containerDomNode;\n    const lastContainerFocusElement = this.findLastFocusableChild(this._hover.containerDomNode);\n    if (lastContainerFocusElement) {\n      const beforeContainerFocusElement = dom.prepend(this._hoverContainer, $('div'));\n      const afterContainerFocusElement = dom.append(this._hoverContainer, $('div'));\n      beforeContainerFocusElement.tabIndex = 0;\n      afterContainerFocusElement.tabIndex = 0;\n      this._register(dom.addDisposableListener(afterContainerFocusElement, 'focus', e => {\n        firstContainerFocusElement.focus();\n        e.preventDefault();\n      }));\n      this._register(dom.addDisposableListener(beforeContainerFocusElement, 'focus', e => {\n        lastContainerFocusElement.focus();\n        e.preventDefault();\n      }));\n    }\n  }\n  findLastFocusableChild(root) {\n    if (root.hasChildNodes()) {\n      for (let i = 0; i < root.childNodes.length; i++) {\n        const node = root.childNodes.item(root.childNodes.length - i - 1);\n        if (node.nodeType === node.ELEMENT_NODE) {\n          const parsedNode = node;\n          if (typeof parsedNode.tabIndex === 'number' && parsedNode.tabIndex >= 0) {\n            return parsedNode;\n          }\n        }\n        const recursivelyFoundElement = this.findLastFocusableChild(node);\n        if (recursivelyFoundElement) {\n          return recursivelyFoundElement;\n        }\n      }\n    }\n    return undefined;\n  }\n  render(container) {\n    container.appendChild(this._hoverContainer);\n    const hoverFocused = this._hoverContainer.contains(this._hoverContainer.ownerDocument.activeElement);\n    const accessibleViewHint = hoverFocused && getHoverAccessibleViewHint(this._configurationService.getValue('accessibility.verbosity.hover') === true && this._accessibilityService.isScreenReaderOptimized(), this._keybindingService.lookupKeybinding('editor.action.accessibleView')?.getAriaLabel());\n    if (accessibleViewHint) {\n      status(accessibleViewHint);\n    }\n    this.layout();\n    this.addFocusTrap();\n  }\n  layout() {\n    this._hover.containerDomNode.classList.remove('right-aligned');\n    this._hover.contentsDomNode.style.maxHeight = '';\n    const getZoomAccountedBoundingClientRect = e => {\n      const zoom = dom.getDomNodeZoomLevel(e);\n      const boundingRect = e.getBoundingClientRect();\n      return {\n        top: boundingRect.top * zoom,\n        bottom: boundingRect.bottom * zoom,\n        right: boundingRect.right * zoom,\n        left: boundingRect.left * zoom\n      };\n    };\n    const targetBounds = this._target.targetElements.map(e => getZoomAccountedBoundingClientRect(e));\n    const {\n      top,\n      right,\n      bottom,\n      left\n    } = targetBounds[0];\n    const width = right - left;\n    const height = bottom - top;\n    const targetRect = {\n      top,\n      right,\n      bottom,\n      left,\n      width,\n      height,\n      center: {\n        x: left + width / 2,\n        y: top + height / 2\n      }\n    };\n    // These calls adjust the position depending on spacing.\n    this.adjustHorizontalHoverPosition(targetRect);\n    this.adjustVerticalHoverPosition(targetRect);\n    // This call limits the maximum height of the hover.\n    this.adjustHoverMaxHeight(targetRect);\n    // Offset the hover position if there is a pointer so it aligns with the target element\n    this._hoverContainer.style.padding = '';\n    this._hoverContainer.style.margin = '';\n    if (this._hoverPointer) {\n      switch (this._hoverPosition) {\n        case 1 /* HoverPosition.RIGHT */:\n          targetRect.left += 3 /* Constants.PointerSize */;\n          targetRect.right += 3 /* Constants.PointerSize */;\n          this._hoverContainer.style.paddingLeft = `${3 /* Constants.PointerSize */}px`;\n          this._hoverContainer.style.marginLeft = `${-3 /* Constants.PointerSize */}px`;\n          break;\n        case 0 /* HoverPosition.LEFT */:\n          targetRect.left -= 3 /* Constants.PointerSize */;\n          targetRect.right -= 3 /* Constants.PointerSize */;\n          this._hoverContainer.style.paddingRight = `${3 /* Constants.PointerSize */}px`;\n          this._hoverContainer.style.marginRight = `${-3 /* Constants.PointerSize */}px`;\n          break;\n        case 2 /* HoverPosition.BELOW */:\n          targetRect.top += 3 /* Constants.PointerSize */;\n          targetRect.bottom += 3 /* Constants.PointerSize */;\n          this._hoverContainer.style.paddingTop = `${3 /* Constants.PointerSize */}px`;\n          this._hoverContainer.style.marginTop = `${-3 /* Constants.PointerSize */}px`;\n          break;\n        case 3 /* HoverPosition.ABOVE */:\n          targetRect.top -= 3 /* Constants.PointerSize */;\n          targetRect.bottom -= 3 /* Constants.PointerSize */;\n          this._hoverContainer.style.paddingBottom = `${3 /* Constants.PointerSize */}px`;\n          this._hoverContainer.style.marginBottom = `${-3 /* Constants.PointerSize */}px`;\n          break;\n      }\n      targetRect.center.x = targetRect.left + width / 2;\n      targetRect.center.y = targetRect.top + height / 2;\n    }\n    this.computeXCordinate(targetRect);\n    this.computeYCordinate(targetRect);\n    if (this._hoverPointer) {\n      // reset\n      this._hoverPointer.classList.remove('top');\n      this._hoverPointer.classList.remove('left');\n      this._hoverPointer.classList.remove('right');\n      this._hoverPointer.classList.remove('bottom');\n      this.setHoverPointerPosition(targetRect);\n    }\n    this._hover.onContentsChanged();\n  }\n  computeXCordinate(target) {\n    const hoverWidth = this._hover.containerDomNode.clientWidth + 2 /* Constants.HoverBorderWidth */;\n    if (this._target.x !== undefined) {\n      this._x = this._target.x;\n    } else if (this._hoverPosition === 1 /* HoverPosition.RIGHT */) {\n      this._x = target.right;\n    } else if (this._hoverPosition === 0 /* HoverPosition.LEFT */) {\n      this._x = target.left - hoverWidth;\n    } else {\n      if (this._hoverPointer) {\n        this._x = target.center.x - this._hover.containerDomNode.clientWidth / 2;\n      } else {\n        this._x = target.left;\n      }\n      // Hover is going beyond window towards right end\n      if (this._x + hoverWidth >= this._targetDocumentElement.clientWidth) {\n        this._hover.containerDomNode.classList.add('right-aligned');\n        this._x = Math.max(this._targetDocumentElement.clientWidth - hoverWidth - 2 /* Constants.HoverWindowEdgeMargin */, this._targetDocumentElement.clientLeft);\n      }\n    }\n    // Hover is going beyond window towards left end\n    if (this._x < this._targetDocumentElement.clientLeft) {\n      this._x = target.left + 2 /* Constants.HoverWindowEdgeMargin */;\n    }\n  }\n  computeYCordinate(target) {\n    if (this._target.y !== undefined) {\n      this._y = this._target.y;\n    } else if (this._hoverPosition === 3 /* HoverPosition.ABOVE */) {\n      this._y = target.top;\n    } else if (this._hoverPosition === 2 /* HoverPosition.BELOW */) {\n      this._y = target.bottom - 2;\n    } else {\n      if (this._hoverPointer) {\n        this._y = target.center.y + this._hover.containerDomNode.clientHeight / 2;\n      } else {\n        this._y = target.bottom;\n      }\n    }\n    // Hover on bottom is going beyond window\n    if (this._y > this._targetWindow.innerHeight) {\n      this._y = target.bottom;\n    }\n  }\n  adjustHorizontalHoverPosition(target) {\n    // Do not adjust horizontal hover position if x cordiante is provided\n    if (this._target.x !== undefined) {\n      return;\n    }\n    const hoverPointerOffset = this._hoverPointer ? 3 /* Constants.PointerSize */ : 0;\n    // When force position is enabled, restrict max width\n    if (this._forcePosition) {\n      const padding = hoverPointerOffset + 2 /* Constants.HoverBorderWidth */;\n      if (this._hoverPosition === 1 /* HoverPosition.RIGHT */) {\n        this._hover.containerDomNode.style.maxWidth = `${this._targetDocumentElement.clientWidth - target.right - padding}px`;\n      } else if (this._hoverPosition === 0 /* HoverPosition.LEFT */) {\n        this._hover.containerDomNode.style.maxWidth = `${target.left - padding}px`;\n      }\n      return;\n    }\n    // Position hover on right to target\n    if (this._hoverPosition === 1 /* HoverPosition.RIGHT */) {\n      const roomOnRight = this._targetDocumentElement.clientWidth - target.right;\n      // Hover on the right is going beyond window.\n      if (roomOnRight < this._hover.containerDomNode.clientWidth + hoverPointerOffset) {\n        const roomOnLeft = target.left;\n        // There's enough room on the left, flip the hover position\n        if (roomOnLeft >= this._hover.containerDomNode.clientWidth + hoverPointerOffset) {\n          this._hoverPosition = 0 /* HoverPosition.LEFT */;\n        }\n        // Hover on the left would go beyond window too\n        else {\n          this._hoverPosition = 2 /* HoverPosition.BELOW */;\n        }\n      }\n    }\n    // Position hover on left to target\n    else if (this._hoverPosition === 0 /* HoverPosition.LEFT */) {\n      const roomOnLeft = target.left;\n      // Hover on the left is going beyond window.\n      if (roomOnLeft < this._hover.containerDomNode.clientWidth + hoverPointerOffset) {\n        const roomOnRight = this._targetDocumentElement.clientWidth - target.right;\n        // There's enough room on the right, flip the hover position\n        if (roomOnRight >= this._hover.containerDomNode.clientWidth + hoverPointerOffset) {\n          this._hoverPosition = 1 /* HoverPosition.RIGHT */;\n        }\n        // Hover on the right would go beyond window too\n        else {\n          this._hoverPosition = 2 /* HoverPosition.BELOW */;\n        }\n      }\n      // Hover on the left is going beyond window.\n      if (target.left - this._hover.containerDomNode.clientWidth - hoverPointerOffset <= this._targetDocumentElement.clientLeft) {\n        this._hoverPosition = 1 /* HoverPosition.RIGHT */;\n      }\n    }\n  }\n  adjustVerticalHoverPosition(target) {\n    // Do not adjust vertical hover position if the y coordinate is provided\n    // or the position is forced\n    if (this._target.y !== undefined || this._forcePosition) {\n      return;\n    }\n    const hoverPointerOffset = this._hoverPointer ? 3 /* Constants.PointerSize */ : 0;\n    // Position hover on top of the target\n    if (this._hoverPosition === 3 /* HoverPosition.ABOVE */) {\n      // Hover on top is going beyond window\n      if (target.top - this._hover.containerDomNode.clientHeight - hoverPointerOffset < 0) {\n        this._hoverPosition = 2 /* HoverPosition.BELOW */;\n      }\n    }\n    // Position hover below the target\n    else if (this._hoverPosition === 2 /* HoverPosition.BELOW */) {\n      // Hover on bottom is going beyond window\n      if (target.bottom + this._hover.containerDomNode.clientHeight + hoverPointerOffset > this._targetWindow.innerHeight) {\n        this._hoverPosition = 3 /* HoverPosition.ABOVE */;\n      }\n    }\n  }\n  adjustHoverMaxHeight(target) {\n    let maxHeight = this._targetWindow.innerHeight / 2;\n    // When force position is enabled, restrict max height\n    if (this._forcePosition) {\n      const padding = (this._hoverPointer ? 3 /* Constants.PointerSize */ : 0) + 2 /* Constants.HoverBorderWidth */;\n      if (this._hoverPosition === 3 /* HoverPosition.ABOVE */) {\n        maxHeight = Math.min(maxHeight, target.top - padding);\n      } else if (this._hoverPosition === 2 /* HoverPosition.BELOW */) {\n        maxHeight = Math.min(maxHeight, this._targetWindow.innerHeight - target.bottom - padding);\n      }\n    }\n    this._hover.containerDomNode.style.maxHeight = `${maxHeight}px`;\n    if (this._hover.contentsDomNode.clientHeight < this._hover.contentsDomNode.scrollHeight) {\n      // Add padding for a vertical scrollbar\n      const extraRightPadding = `${this._hover.scrollbar.options.verticalScrollbarSize}px`;\n      if (this._hover.contentsDomNode.style.paddingRight !== extraRightPadding) {\n        this._hover.contentsDomNode.style.paddingRight = extraRightPadding;\n      }\n    }\n  }\n  setHoverPointerPosition(target) {\n    if (!this._hoverPointer) {\n      return;\n    }\n    switch (this._hoverPosition) {\n      case 0 /* HoverPosition.LEFT */:\n      case 1 /* HoverPosition.RIGHT */:\n        {\n          this._hoverPointer.classList.add(this._hoverPosition === 0 /* HoverPosition.LEFT */ ? 'right' : 'left');\n          const hoverHeight = this._hover.containerDomNode.clientHeight;\n          // If hover is taller than target, then show the pointer at the center of target\n          if (hoverHeight > target.height) {\n            this._hoverPointer.style.top = `${target.center.y - (this._y - hoverHeight) - 3 /* Constants.PointerSize */}px`;\n          }\n          // Otherwise show the pointer at the center of hover\n          else {\n            this._hoverPointer.style.top = `${Math.round(hoverHeight / 2) - 3 /* Constants.PointerSize */}px`;\n          }\n          break;\n        }\n      case 3 /* HoverPosition.ABOVE */:\n      case 2 /* HoverPosition.BELOW */:\n        {\n          this._hoverPointer.classList.add(this._hoverPosition === 3 /* HoverPosition.ABOVE */ ? 'bottom' : 'top');\n          const hoverWidth = this._hover.containerDomNode.clientWidth;\n          // Position pointer at the center of the hover\n          let pointerLeftPosition = Math.round(hoverWidth / 2) - 3 /* Constants.PointerSize */;\n          // If pointer goes beyond target then position it at the center of the target\n          const pointerX = this._x + pointerLeftPosition;\n          if (pointerX < target.left || pointerX > target.right) {\n            pointerLeftPosition = target.center.x - this._x - 3 /* Constants.PointerSize */;\n          }\n          this._hoverPointer.style.left = `${pointerLeftPosition}px`;\n          break;\n        }\n    }\n  }\n  focus() {\n    this._hover.containerDomNode.focus();\n  }\n  dispose() {\n    if (!this._isDisposed) {\n      this._onDispose.fire();\n      this._hoverContainer.remove();\n      this._messageListeners.dispose();\n      this._target.dispose();\n      super.dispose();\n    }\n    this._isDisposed = true;\n  }\n};\nHoverWidget = __decorate([__param(1, IKeybindingService), __param(2, IConfigurationService), __param(3, IOpenerService), __param(4, IInstantiationService), __param(5, IAccessibilityService)], HoverWidget);\nexport { HoverWidget };\nclass CompositeMouseTracker extends Widget {\n  get onMouseOut() {\n    return this._onMouseOut.event;\n  }\n  get isMouseIn() {\n    return this._isMouseIn;\n  }\n  constructor(_elements) {\n    super();\n    this._elements = _elements;\n    this._isMouseIn = true;\n    this._onMouseOut = this._register(new Emitter());\n    this._elements.forEach(n => this.onmouseover(n, () => this._onTargetMouseOver(n)));\n    this._elements.forEach(n => this.onmouseleave(n, () => this._onTargetMouseLeave(n)));\n  }\n  _onTargetMouseOver(target) {\n    this._isMouseIn = true;\n    this._clearEvaluateMouseStateTimeout(target);\n  }\n  _onTargetMouseLeave(target) {\n    this._isMouseIn = false;\n    this._evaluateMouseState(target);\n  }\n  _evaluateMouseState(target) {\n    this._clearEvaluateMouseStateTimeout(target);\n    // Evaluate whether the mouse is still outside asynchronously such that other mouse targets\n    // have the opportunity to first their mouse in event.\n    this._mouseTimeout = dom.getWindow(target).setTimeout(() => this._fireIfMouseOutside(), 0);\n  }\n  _clearEvaluateMouseStateTimeout(target) {\n    if (this._mouseTimeout) {\n      dom.getWindow(target).clearTimeout(this._mouseTimeout);\n      this._mouseTimeout = undefined;\n    }\n  }\n  _fireIfMouseOutside() {\n    if (!this._isMouseIn) {\n      this._onMouseOut.fire();\n    }\n  }\n}\nclass ElementHoverTarget {\n  constructor(_element) {\n    this._element = _element;\n    this.targetElements = [this._element];\n  }\n  dispose() {}\n}", "map": {"version": 3, "names": ["__decorate", "decorators", "target", "key", "desc", "c", "arguments", "length", "r", "Object", "getOwnPropertyDescriptor", "d", "Reflect", "decorate", "i", "defineProperty", "__param", "paramIndex", "decorator", "DisposableStore", "Emitter", "dom", "IKeybindingService", "IConfigurationService", "EDITOR_FONT_DEFAULTS", "HoverAction", "HoverWidget", "BaseHoverWidget", "getHoverAccessibleViewHint", "Widget", "IOpenerService", "IInstantiationService", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "openLinkFromMarkdown", "isMarkdownString", "localize", "isMacintosh", "IAccessibilityService", "status", "$", "_targetWindow", "getWindow", "_target", "targetElements", "_targetDocumentElement", "document", "documentElement", "isDisposed", "_isDisposed", "isMouseIn", "_lockMouseTracker", "domNode", "_hover", "containerDomNode", "onDispose", "_onDispose", "event", "onRequestLayout", "_onRequestLayout", "anchor", "_hoverPosition", "x", "_x", "y", "_y", "isLocked", "_isLocked", "value", "_hoverContainer", "classList", "toggle", "constructor", "options", "_keybindingService", "_configurationService", "_openerService", "_instantiationService", "_accessibilityService", "_messageListeners", "_forcePosition", "_enableFocusTraps", "_addedFocusTrap", "_register", "_link<PERSON><PERSON><PERSON>", "linkHandler", "url", "content", "isTrusted", "undefined", "ElementHoverTarget", "_hoverPointer", "appearance", "showPointer", "add", "compact", "skipFadeInAnimation", "additionalClasses", "position", "forcePosition", "trapFocus", "hoverPosition", "onmousedown", "e", "stopPropagation", "onkeydown", "equals", "dispose", "addDisposableListener", "rowElement", "contentsElement", "textContent", "style", "whiteSpace", "isHTMLElement", "append<PERSON><PERSON><PERSON>", "markdown", "md<PERSON><PERSON><PERSON>", "createInstance", "codeBlockFontFamily", "getValue", "fontFamily", "element", "render", "actionHandler", "callback", "disposables", "asyncRender<PERSON><PERSON>back", "layout", "fire", "contentsDomNode", "actions", "statusBarElement", "actionsElement", "for<PERSON>ach", "action", "keybinding", "lookupKeybinding", "commandId", "keybinding<PERSON>abel", "get<PERSON><PERSON><PERSON>", "label", "run", "iconClass", "hideOnHover", "persistence", "includes", "showHoverHint", "infoElement", "mouseTrackerTargets", "push", "mouseTracker", "CompositeMouseTracker", "onMouseOut", "mouseTracker2Targets", "addFocusTrap", "firstContainerFocusElement", "lastContainerFocusElement", "findLastFocusableChild", "beforeContainerFocusElement", "prepend", "afterContainerFocusElement", "append", "tabIndex", "focus", "preventDefault", "root", "hasChildNodes", "childNodes", "node", "item", "nodeType", "ELEMENT_NODE", "parsedNode", "recursivelyFoundElement", "container", "hoverFocused", "contains", "ownerDocument", "activeElement", "accessibleViewHint", "isScreenReaderOptimized", "getAriaLabel", "remove", "maxHeight", "getZoomAccountedBoundingClientRect", "zoom", "getDomNodeZoomLevel", "boundingRect", "getBoundingClientRect", "top", "bottom", "right", "left", "targetBounds", "map", "width", "height", "targetRect", "center", "adjustHorizontalHoverPosition", "adjustVerticalHoverPosition", "adjustHoverMaxHeight", "padding", "margin", "paddingLeft", "marginLeft", "paddingRight", "marginRight", "paddingTop", "marginTop", "paddingBottom", "marginBottom", "computeXCordinate", "computeYCordinate", "setHoverPointerPosition", "onContentsChanged", "hoverWidth", "clientWidth", "Math", "max", "clientLeft", "clientHeight", "innerHeight", "hoverPointerOffset", "max<PERSON><PERSON><PERSON>", "roomOnRight", "roomOnLeft", "min", "scrollHeight", "extraRightPadding", "scrollbar", "verticalScrollbarSize", "hoverHeight", "round", "pointerLeftPosition", "pointerX", "_onMouseOut", "_isMouseIn", "_elements", "n", "on<PERSON><PERSON>ver", "_onTargetMouseOver", "onmouseleave", "_onTargetMouseLeave", "_clearEvaluateMouseStateTimeout", "_evaluateMouseState", "_mouseTimeout", "setTimeout", "_fireIfMouseOutside", "clearTimeout", "_element"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/browser/services/hoverService/hoverWidget.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = (this && this.__param) || function (paramIndex, decorator) {\n    return function (target, key) { decorator(target, key, paramIndex); }\n};\nimport './hover.css';\nimport { DisposableStore } from '../../../../base/common/lifecycle.js';\nimport { Emitter } from '../../../../base/common/event.js';\nimport * as dom from '../../../../base/browser/dom.js';\nimport { IKeybindingService } from '../../../../platform/keybinding/common/keybinding.js';\nimport { IConfigurationService } from '../../../../platform/configuration/common/configuration.js';\nimport { EDITOR_FONT_DEFAULTS } from '../../../common/config/editorOptions.js';\nimport { HoverAction, HoverWidget as BaseHoverWidget, getHoverAccessibleViewHint } from '../../../../base/browser/ui/hover/hoverWidget.js';\nimport { Widget } from '../../../../base/browser/ui/widget.js';\nimport { IOpenerService } from '../../../../platform/opener/common/opener.js';\nimport { IInstantiationService } from '../../../../platform/instantiation/common/instantiation.js';\nimport { MarkdownRenderer, openLinkFromMarkdown } from '../../widget/markdownRenderer/browser/markdownRenderer.js';\nimport { isMarkdownString } from '../../../../base/common/htmlContent.js';\nimport { localize } from '../../../../nls.js';\nimport { isMacintosh } from '../../../../base/common/platform.js';\nimport { IAccessibilityService } from '../../../../platform/accessibility/common/accessibility.js';\nimport { status } from '../../../../base/browser/ui/aria/aria.js';\nconst $ = dom.$;\nlet HoverWidget = class HoverWidget extends Widget {\n    get _targetWindow() {\n        return dom.getWindow(this._target.targetElements[0]);\n    }\n    get _targetDocumentElement() {\n        return dom.getWindow(this._target.targetElements[0]).document.documentElement;\n    }\n    get isDisposed() { return this._isDisposed; }\n    get isMouseIn() { return this._lockMouseTracker.isMouseIn; }\n    get domNode() { return this._hover.containerDomNode; }\n    get onDispose() { return this._onDispose.event; }\n    get onRequestLayout() { return this._onRequestLayout.event; }\n    get anchor() { return this._hoverPosition === 2 /* HoverPosition.BELOW */ ? 0 /* AnchorPosition.BELOW */ : 1 /* AnchorPosition.ABOVE */; }\n    get x() { return this._x; }\n    get y() { return this._y; }\n    /**\n     * Whether the hover is \"locked\" by holding the alt/option key. When locked, the hover will not\n     * hide and can be hovered regardless of whether the `hideOnHover` hover option is set.\n     */\n    get isLocked() { return this._isLocked; }\n    set isLocked(value) {\n        if (this._isLocked === value) {\n            return;\n        }\n        this._isLocked = value;\n        this._hoverContainer.classList.toggle('locked', this._isLocked);\n    }\n    constructor(options, _keybindingService, _configurationService, _openerService, _instantiationService, _accessibilityService) {\n        super();\n        this._keybindingService = _keybindingService;\n        this._configurationService = _configurationService;\n        this._openerService = _openerService;\n        this._instantiationService = _instantiationService;\n        this._accessibilityService = _accessibilityService;\n        this._messageListeners = new DisposableStore();\n        this._isDisposed = false;\n        this._forcePosition = false;\n        this._x = 0;\n        this._y = 0;\n        this._isLocked = false;\n        this._enableFocusTraps = false;\n        this._addedFocusTrap = false;\n        this._onDispose = this._register(new Emitter());\n        this._onRequestLayout = this._register(new Emitter());\n        this._linkHandler = options.linkHandler || (url => {\n            return openLinkFromMarkdown(this._openerService, url, isMarkdownString(options.content) ? options.content.isTrusted : undefined);\n        });\n        this._target = 'targetElements' in options.target ? options.target : new ElementHoverTarget(options.target);\n        this._hoverPointer = options.appearance?.showPointer ? $('div.workbench-hover-pointer') : undefined;\n        this._hover = this._register(new BaseHoverWidget());\n        this._hover.containerDomNode.classList.add('workbench-hover', 'fadeIn');\n        if (options.appearance?.compact) {\n            this._hover.containerDomNode.classList.add('workbench-hover', 'compact');\n        }\n        if (options.appearance?.skipFadeInAnimation) {\n            this._hover.containerDomNode.classList.add('skip-fade-in');\n        }\n        if (options.additionalClasses) {\n            this._hover.containerDomNode.classList.add(...options.additionalClasses);\n        }\n        if (options.position?.forcePosition) {\n            this._forcePosition = true;\n        }\n        if (options.trapFocus) {\n            this._enableFocusTraps = true;\n        }\n        this._hoverPosition = options.position?.hoverPosition ?? 3 /* HoverPosition.ABOVE */;\n        // Don't allow mousedown out of the widget, otherwise preventDefault will call and text will\n        // not be selected.\n        this.onmousedown(this._hover.containerDomNode, e => e.stopPropagation());\n        // Hide hover on escape\n        this.onkeydown(this._hover.containerDomNode, e => {\n            if (e.equals(9 /* KeyCode.Escape */)) {\n                this.dispose();\n            }\n        });\n        // Hide when the window loses focus\n        this._register(dom.addDisposableListener(this._targetWindow, 'blur', () => this.dispose()));\n        const rowElement = $('div.hover-row.markdown-hover');\n        const contentsElement = $('div.hover-contents');\n        if (typeof options.content === 'string') {\n            contentsElement.textContent = options.content;\n            contentsElement.style.whiteSpace = 'pre-wrap';\n        }\n        else if (dom.isHTMLElement(options.content)) {\n            contentsElement.appendChild(options.content);\n            contentsElement.classList.add('html-hover-contents');\n        }\n        else {\n            const markdown = options.content;\n            const mdRenderer = this._instantiationService.createInstance(MarkdownRenderer, { codeBlockFontFamily: this._configurationService.getValue('editor').fontFamily || EDITOR_FONT_DEFAULTS.fontFamily });\n            const { element } = mdRenderer.render(markdown, {\n                actionHandler: {\n                    callback: (content) => this._linkHandler(content),\n                    disposables: this._messageListeners\n                },\n                asyncRenderCallback: () => {\n                    contentsElement.classList.add('code-hover-contents');\n                    this.layout();\n                    // This changes the dimensions of the hover so trigger a layout\n                    this._onRequestLayout.fire();\n                }\n            });\n            contentsElement.appendChild(element);\n        }\n        rowElement.appendChild(contentsElement);\n        this._hover.contentsDomNode.appendChild(rowElement);\n        if (options.actions && options.actions.length > 0) {\n            const statusBarElement = $('div.hover-row.status-bar');\n            const actionsElement = $('div.actions');\n            options.actions.forEach(action => {\n                const keybinding = this._keybindingService.lookupKeybinding(action.commandId);\n                const keybindingLabel = keybinding ? keybinding.getLabel() : null;\n                HoverAction.render(actionsElement, {\n                    label: action.label,\n                    commandId: action.commandId,\n                    run: e => {\n                        action.run(e);\n                        this.dispose();\n                    },\n                    iconClass: action.iconClass\n                }, keybindingLabel);\n            });\n            statusBarElement.appendChild(actionsElement);\n            this._hover.containerDomNode.appendChild(statusBarElement);\n        }\n        this._hoverContainer = $('div.workbench-hover-container');\n        if (this._hoverPointer) {\n            this._hoverContainer.appendChild(this._hoverPointer);\n        }\n        this._hoverContainer.appendChild(this._hover.containerDomNode);\n        // Determine whether to hide on hover\n        let hideOnHover;\n        if (options.actions && options.actions.length > 0) {\n            // If there are actions, require hover so they can be accessed\n            hideOnHover = false;\n        }\n        else {\n            if (options.persistence?.hideOnHover === undefined) {\n                // When unset, will default to true when it's a string or when it's markdown that\n                // appears to have a link using a naive check for '](' and '</a>'\n                hideOnHover = typeof options.content === 'string' ||\n                    isMarkdownString(options.content) && !options.content.value.includes('](') && !options.content.value.includes('</a>');\n            }\n            else {\n                // It's set explicitly\n                hideOnHover = options.persistence.hideOnHover;\n            }\n        }\n        // Show the hover hint if needed\n        if (options.appearance?.showHoverHint) {\n            const statusBarElement = $('div.hover-row.status-bar');\n            const infoElement = $('div.info');\n            infoElement.textContent = localize('hoverhint', 'Hold {0} key to mouse over', isMacintosh ? 'Option' : 'Alt');\n            statusBarElement.appendChild(infoElement);\n            this._hover.containerDomNode.appendChild(statusBarElement);\n        }\n        const mouseTrackerTargets = [...this._target.targetElements];\n        if (!hideOnHover) {\n            mouseTrackerTargets.push(this._hoverContainer);\n        }\n        const mouseTracker = this._register(new CompositeMouseTracker(mouseTrackerTargets));\n        this._register(mouseTracker.onMouseOut(() => {\n            if (!this._isLocked) {\n                this.dispose();\n            }\n        }));\n        // Setup another mouse tracker when hideOnHover is set in order to track the hover as well\n        // when it is locked. This ensures the hover will hide on mouseout after alt has been\n        // released to unlock the element.\n        if (hideOnHover) {\n            const mouseTracker2Targets = [...this._target.targetElements, this._hoverContainer];\n            this._lockMouseTracker = this._register(new CompositeMouseTracker(mouseTracker2Targets));\n            this._register(this._lockMouseTracker.onMouseOut(() => {\n                if (!this._isLocked) {\n                    this.dispose();\n                }\n            }));\n        }\n        else {\n            this._lockMouseTracker = mouseTracker;\n        }\n    }\n    addFocusTrap() {\n        if (!this._enableFocusTraps || this._addedFocusTrap) {\n            return;\n        }\n        this._addedFocusTrap = true;\n        // Add a hover tab loop if the hover has at least one element with a valid tabIndex\n        const firstContainerFocusElement = this._hover.containerDomNode;\n        const lastContainerFocusElement = this.findLastFocusableChild(this._hover.containerDomNode);\n        if (lastContainerFocusElement) {\n            const beforeContainerFocusElement = dom.prepend(this._hoverContainer, $('div'));\n            const afterContainerFocusElement = dom.append(this._hoverContainer, $('div'));\n            beforeContainerFocusElement.tabIndex = 0;\n            afterContainerFocusElement.tabIndex = 0;\n            this._register(dom.addDisposableListener(afterContainerFocusElement, 'focus', (e) => {\n                firstContainerFocusElement.focus();\n                e.preventDefault();\n            }));\n            this._register(dom.addDisposableListener(beforeContainerFocusElement, 'focus', (e) => {\n                lastContainerFocusElement.focus();\n                e.preventDefault();\n            }));\n        }\n    }\n    findLastFocusableChild(root) {\n        if (root.hasChildNodes()) {\n            for (let i = 0; i < root.childNodes.length; i++) {\n                const node = root.childNodes.item(root.childNodes.length - i - 1);\n                if (node.nodeType === node.ELEMENT_NODE) {\n                    const parsedNode = node;\n                    if (typeof parsedNode.tabIndex === 'number' && parsedNode.tabIndex >= 0) {\n                        return parsedNode;\n                    }\n                }\n                const recursivelyFoundElement = this.findLastFocusableChild(node);\n                if (recursivelyFoundElement) {\n                    return recursivelyFoundElement;\n                }\n            }\n        }\n        return undefined;\n    }\n    render(container) {\n        container.appendChild(this._hoverContainer);\n        const hoverFocused = this._hoverContainer.contains(this._hoverContainer.ownerDocument.activeElement);\n        const accessibleViewHint = hoverFocused && getHoverAccessibleViewHint(this._configurationService.getValue('accessibility.verbosity.hover') === true && this._accessibilityService.isScreenReaderOptimized(), this._keybindingService.lookupKeybinding('editor.action.accessibleView')?.getAriaLabel());\n        if (accessibleViewHint) {\n            status(accessibleViewHint);\n        }\n        this.layout();\n        this.addFocusTrap();\n    }\n    layout() {\n        this._hover.containerDomNode.classList.remove('right-aligned');\n        this._hover.contentsDomNode.style.maxHeight = '';\n        const getZoomAccountedBoundingClientRect = (e) => {\n            const zoom = dom.getDomNodeZoomLevel(e);\n            const boundingRect = e.getBoundingClientRect();\n            return {\n                top: boundingRect.top * zoom,\n                bottom: boundingRect.bottom * zoom,\n                right: boundingRect.right * zoom,\n                left: boundingRect.left * zoom,\n            };\n        };\n        const targetBounds = this._target.targetElements.map(e => getZoomAccountedBoundingClientRect(e));\n        const { top, right, bottom, left } = targetBounds[0];\n        const width = right - left;\n        const height = bottom - top;\n        const targetRect = {\n            top, right, bottom, left, width, height,\n            center: {\n                x: left + (width / 2),\n                y: top + (height / 2)\n            }\n        };\n        // These calls adjust the position depending on spacing.\n        this.adjustHorizontalHoverPosition(targetRect);\n        this.adjustVerticalHoverPosition(targetRect);\n        // This call limits the maximum height of the hover.\n        this.adjustHoverMaxHeight(targetRect);\n        // Offset the hover position if there is a pointer so it aligns with the target element\n        this._hoverContainer.style.padding = '';\n        this._hoverContainer.style.margin = '';\n        if (this._hoverPointer) {\n            switch (this._hoverPosition) {\n                case 1 /* HoverPosition.RIGHT */:\n                    targetRect.left += 3 /* Constants.PointerSize */;\n                    targetRect.right += 3 /* Constants.PointerSize */;\n                    this._hoverContainer.style.paddingLeft = `${3 /* Constants.PointerSize */}px`;\n                    this._hoverContainer.style.marginLeft = `${-3 /* Constants.PointerSize */}px`;\n                    break;\n                case 0 /* HoverPosition.LEFT */:\n                    targetRect.left -= 3 /* Constants.PointerSize */;\n                    targetRect.right -= 3 /* Constants.PointerSize */;\n                    this._hoverContainer.style.paddingRight = `${3 /* Constants.PointerSize */}px`;\n                    this._hoverContainer.style.marginRight = `${-3 /* Constants.PointerSize */}px`;\n                    break;\n                case 2 /* HoverPosition.BELOW */:\n                    targetRect.top += 3 /* Constants.PointerSize */;\n                    targetRect.bottom += 3 /* Constants.PointerSize */;\n                    this._hoverContainer.style.paddingTop = `${3 /* Constants.PointerSize */}px`;\n                    this._hoverContainer.style.marginTop = `${-3 /* Constants.PointerSize */}px`;\n                    break;\n                case 3 /* HoverPosition.ABOVE */:\n                    targetRect.top -= 3 /* Constants.PointerSize */;\n                    targetRect.bottom -= 3 /* Constants.PointerSize */;\n                    this._hoverContainer.style.paddingBottom = `${3 /* Constants.PointerSize */}px`;\n                    this._hoverContainer.style.marginBottom = `${-3 /* Constants.PointerSize */}px`;\n                    break;\n            }\n            targetRect.center.x = targetRect.left + (width / 2);\n            targetRect.center.y = targetRect.top + (height / 2);\n        }\n        this.computeXCordinate(targetRect);\n        this.computeYCordinate(targetRect);\n        if (this._hoverPointer) {\n            // reset\n            this._hoverPointer.classList.remove('top');\n            this._hoverPointer.classList.remove('left');\n            this._hoverPointer.classList.remove('right');\n            this._hoverPointer.classList.remove('bottom');\n            this.setHoverPointerPosition(targetRect);\n        }\n        this._hover.onContentsChanged();\n    }\n    computeXCordinate(target) {\n        const hoverWidth = this._hover.containerDomNode.clientWidth + 2 /* Constants.HoverBorderWidth */;\n        if (this._target.x !== undefined) {\n            this._x = this._target.x;\n        }\n        else if (this._hoverPosition === 1 /* HoverPosition.RIGHT */) {\n            this._x = target.right;\n        }\n        else if (this._hoverPosition === 0 /* HoverPosition.LEFT */) {\n            this._x = target.left - hoverWidth;\n        }\n        else {\n            if (this._hoverPointer) {\n                this._x = target.center.x - (this._hover.containerDomNode.clientWidth / 2);\n            }\n            else {\n                this._x = target.left;\n            }\n            // Hover is going beyond window towards right end\n            if (this._x + hoverWidth >= this._targetDocumentElement.clientWidth) {\n                this._hover.containerDomNode.classList.add('right-aligned');\n                this._x = Math.max(this._targetDocumentElement.clientWidth - hoverWidth - 2 /* Constants.HoverWindowEdgeMargin */, this._targetDocumentElement.clientLeft);\n            }\n        }\n        // Hover is going beyond window towards left end\n        if (this._x < this._targetDocumentElement.clientLeft) {\n            this._x = target.left + 2 /* Constants.HoverWindowEdgeMargin */;\n        }\n    }\n    computeYCordinate(target) {\n        if (this._target.y !== undefined) {\n            this._y = this._target.y;\n        }\n        else if (this._hoverPosition === 3 /* HoverPosition.ABOVE */) {\n            this._y = target.top;\n        }\n        else if (this._hoverPosition === 2 /* HoverPosition.BELOW */) {\n            this._y = target.bottom - 2;\n        }\n        else {\n            if (this._hoverPointer) {\n                this._y = target.center.y + (this._hover.containerDomNode.clientHeight / 2);\n            }\n            else {\n                this._y = target.bottom;\n            }\n        }\n        // Hover on bottom is going beyond window\n        if (this._y > this._targetWindow.innerHeight) {\n            this._y = target.bottom;\n        }\n    }\n    adjustHorizontalHoverPosition(target) {\n        // Do not adjust horizontal hover position if x cordiante is provided\n        if (this._target.x !== undefined) {\n            return;\n        }\n        const hoverPointerOffset = (this._hoverPointer ? 3 /* Constants.PointerSize */ : 0);\n        // When force position is enabled, restrict max width\n        if (this._forcePosition) {\n            const padding = hoverPointerOffset + 2 /* Constants.HoverBorderWidth */;\n            if (this._hoverPosition === 1 /* HoverPosition.RIGHT */) {\n                this._hover.containerDomNode.style.maxWidth = `${this._targetDocumentElement.clientWidth - target.right - padding}px`;\n            }\n            else if (this._hoverPosition === 0 /* HoverPosition.LEFT */) {\n                this._hover.containerDomNode.style.maxWidth = `${target.left - padding}px`;\n            }\n            return;\n        }\n        // Position hover on right to target\n        if (this._hoverPosition === 1 /* HoverPosition.RIGHT */) {\n            const roomOnRight = this._targetDocumentElement.clientWidth - target.right;\n            // Hover on the right is going beyond window.\n            if (roomOnRight < this._hover.containerDomNode.clientWidth + hoverPointerOffset) {\n                const roomOnLeft = target.left;\n                // There's enough room on the left, flip the hover position\n                if (roomOnLeft >= this._hover.containerDomNode.clientWidth + hoverPointerOffset) {\n                    this._hoverPosition = 0 /* HoverPosition.LEFT */;\n                }\n                // Hover on the left would go beyond window too\n                else {\n                    this._hoverPosition = 2 /* HoverPosition.BELOW */;\n                }\n            }\n        }\n        // Position hover on left to target\n        else if (this._hoverPosition === 0 /* HoverPosition.LEFT */) {\n            const roomOnLeft = target.left;\n            // Hover on the left is going beyond window.\n            if (roomOnLeft < this._hover.containerDomNode.clientWidth + hoverPointerOffset) {\n                const roomOnRight = this._targetDocumentElement.clientWidth - target.right;\n                // There's enough room on the right, flip the hover position\n                if (roomOnRight >= this._hover.containerDomNode.clientWidth + hoverPointerOffset) {\n                    this._hoverPosition = 1 /* HoverPosition.RIGHT */;\n                }\n                // Hover on the right would go beyond window too\n                else {\n                    this._hoverPosition = 2 /* HoverPosition.BELOW */;\n                }\n            }\n            // Hover on the left is going beyond window.\n            if (target.left - this._hover.containerDomNode.clientWidth - hoverPointerOffset <= this._targetDocumentElement.clientLeft) {\n                this._hoverPosition = 1 /* HoverPosition.RIGHT */;\n            }\n        }\n    }\n    adjustVerticalHoverPosition(target) {\n        // Do not adjust vertical hover position if the y coordinate is provided\n        // or the position is forced\n        if (this._target.y !== undefined || this._forcePosition) {\n            return;\n        }\n        const hoverPointerOffset = (this._hoverPointer ? 3 /* Constants.PointerSize */ : 0);\n        // Position hover on top of the target\n        if (this._hoverPosition === 3 /* HoverPosition.ABOVE */) {\n            // Hover on top is going beyond window\n            if (target.top - this._hover.containerDomNode.clientHeight - hoverPointerOffset < 0) {\n                this._hoverPosition = 2 /* HoverPosition.BELOW */;\n            }\n        }\n        // Position hover below the target\n        else if (this._hoverPosition === 2 /* HoverPosition.BELOW */) {\n            // Hover on bottom is going beyond window\n            if (target.bottom + this._hover.containerDomNode.clientHeight + hoverPointerOffset > this._targetWindow.innerHeight) {\n                this._hoverPosition = 3 /* HoverPosition.ABOVE */;\n            }\n        }\n    }\n    adjustHoverMaxHeight(target) {\n        let maxHeight = this._targetWindow.innerHeight / 2;\n        // When force position is enabled, restrict max height\n        if (this._forcePosition) {\n            const padding = (this._hoverPointer ? 3 /* Constants.PointerSize */ : 0) + 2 /* Constants.HoverBorderWidth */;\n            if (this._hoverPosition === 3 /* HoverPosition.ABOVE */) {\n                maxHeight = Math.min(maxHeight, target.top - padding);\n            }\n            else if (this._hoverPosition === 2 /* HoverPosition.BELOW */) {\n                maxHeight = Math.min(maxHeight, this._targetWindow.innerHeight - target.bottom - padding);\n            }\n        }\n        this._hover.containerDomNode.style.maxHeight = `${maxHeight}px`;\n        if (this._hover.contentsDomNode.clientHeight < this._hover.contentsDomNode.scrollHeight) {\n            // Add padding for a vertical scrollbar\n            const extraRightPadding = `${this._hover.scrollbar.options.verticalScrollbarSize}px`;\n            if (this._hover.contentsDomNode.style.paddingRight !== extraRightPadding) {\n                this._hover.contentsDomNode.style.paddingRight = extraRightPadding;\n            }\n        }\n    }\n    setHoverPointerPosition(target) {\n        if (!this._hoverPointer) {\n            return;\n        }\n        switch (this._hoverPosition) {\n            case 0 /* HoverPosition.LEFT */:\n            case 1 /* HoverPosition.RIGHT */: {\n                this._hoverPointer.classList.add(this._hoverPosition === 0 /* HoverPosition.LEFT */ ? 'right' : 'left');\n                const hoverHeight = this._hover.containerDomNode.clientHeight;\n                // If hover is taller than target, then show the pointer at the center of target\n                if (hoverHeight > target.height) {\n                    this._hoverPointer.style.top = `${target.center.y - (this._y - hoverHeight) - 3 /* Constants.PointerSize */}px`;\n                }\n                // Otherwise show the pointer at the center of hover\n                else {\n                    this._hoverPointer.style.top = `${Math.round((hoverHeight / 2)) - 3 /* Constants.PointerSize */}px`;\n                }\n                break;\n            }\n            case 3 /* HoverPosition.ABOVE */:\n            case 2 /* HoverPosition.BELOW */: {\n                this._hoverPointer.classList.add(this._hoverPosition === 3 /* HoverPosition.ABOVE */ ? 'bottom' : 'top');\n                const hoverWidth = this._hover.containerDomNode.clientWidth;\n                // Position pointer at the center of the hover\n                let pointerLeftPosition = Math.round((hoverWidth / 2)) - 3 /* Constants.PointerSize */;\n                // If pointer goes beyond target then position it at the center of the target\n                const pointerX = this._x + pointerLeftPosition;\n                if (pointerX < target.left || pointerX > target.right) {\n                    pointerLeftPosition = target.center.x - this._x - 3 /* Constants.PointerSize */;\n                }\n                this._hoverPointer.style.left = `${pointerLeftPosition}px`;\n                break;\n            }\n        }\n    }\n    focus() {\n        this._hover.containerDomNode.focus();\n    }\n    dispose() {\n        if (!this._isDisposed) {\n            this._onDispose.fire();\n            this._hoverContainer.remove();\n            this._messageListeners.dispose();\n            this._target.dispose();\n            super.dispose();\n        }\n        this._isDisposed = true;\n    }\n};\nHoverWidget = __decorate([\n    __param(1, IKeybindingService),\n    __param(2, IConfigurationService),\n    __param(3, IOpenerService),\n    __param(4, IInstantiationService),\n    __param(5, IAccessibilityService)\n], HoverWidget);\nexport { HoverWidget };\nclass CompositeMouseTracker extends Widget {\n    get onMouseOut() { return this._onMouseOut.event; }\n    get isMouseIn() { return this._isMouseIn; }\n    constructor(_elements) {\n        super();\n        this._elements = _elements;\n        this._isMouseIn = true;\n        this._onMouseOut = this._register(new Emitter());\n        this._elements.forEach(n => this.onmouseover(n, () => this._onTargetMouseOver(n)));\n        this._elements.forEach(n => this.onmouseleave(n, () => this._onTargetMouseLeave(n)));\n    }\n    _onTargetMouseOver(target) {\n        this._isMouseIn = true;\n        this._clearEvaluateMouseStateTimeout(target);\n    }\n    _onTargetMouseLeave(target) {\n        this._isMouseIn = false;\n        this._evaluateMouseState(target);\n    }\n    _evaluateMouseState(target) {\n        this._clearEvaluateMouseStateTimeout(target);\n        // Evaluate whether the mouse is still outside asynchronously such that other mouse targets\n        // have the opportunity to first their mouse in event.\n        this._mouseTimeout = dom.getWindow(target).setTimeout(() => this._fireIfMouseOutside(), 0);\n    }\n    _clearEvaluateMouseStateTimeout(target) {\n        if (this._mouseTimeout) {\n            dom.getWindow(target).clearTimeout(this._mouseTimeout);\n            this._mouseTimeout = undefined;\n        }\n    }\n    _fireIfMouseOutside() {\n        if (!this._isMouseIn) {\n            this._onMouseOut.fire();\n        }\n    }\n}\nclass ElementHoverTarget {\n    constructor(_element) {\n        this._element = _element;\n        this.targetElements = [this._element];\n    }\n    dispose() {\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,IAAIA,UAAU,GAAI,IAAI,IAAI,IAAI,CAACA,UAAU,IAAK,UAAUC,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAE;EACnF,IAAIC,CAAC,GAAGC,SAAS,CAACC,MAAM;IAAEC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGH,MAAM,GAAGE,IAAI,KAAK,IAAI,GAAGA,IAAI,GAAGK,MAAM,CAACC,wBAAwB,CAACR,MAAM,EAAEC,GAAG,CAAC,GAAGC,IAAI;IAAEO,CAAC;EAC5H,IAAI,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,CAACC,QAAQ,KAAK,UAAU,EAAEL,CAAC,GAAGI,OAAO,CAACC,QAAQ,CAACZ,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,CAAC,CAAC,KAC1H,KAAK,IAAIU,CAAC,GAAGb,UAAU,CAACM,MAAM,GAAG,CAAC,EAAEO,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE,IAAIH,CAAC,GAAGV,UAAU,CAACa,CAAC,CAAC,EAAEN,CAAC,GAAG,CAACH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACH,CAAC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACT,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,GAAGG,CAAC,CAACT,MAAM,EAAEC,GAAG,CAAC,KAAKK,CAAC;EACjJ,OAAOH,CAAC,GAAG,CAAC,IAAIG,CAAC,IAAIC,MAAM,CAACM,cAAc,CAACb,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,EAAEA,CAAC;AACjE,CAAC;AACD,IAAIQ,OAAO,GAAI,IAAI,IAAI,IAAI,CAACA,OAAO,IAAK,UAAUC,UAAU,EAAEC,SAAS,EAAE;EACrE,OAAO,UAAUhB,MAAM,EAAEC,GAAG,EAAE;IAAEe,SAAS,CAAChB,MAAM,EAAEC,GAAG,EAAEc,UAAU,CAAC;EAAE,CAAC;AACzE,CAAC;AACD,OAAO,aAAa;AACpB,SAASE,eAAe,QAAQ,sCAAsC;AACtE,SAASC,OAAO,QAAQ,kCAAkC;AAC1D,OAAO,KAAKC,GAAG,MAAM,iCAAiC;AACtD,SAASC,kBAAkB,QAAQ,sDAAsD;AACzF,SAASC,qBAAqB,QAAQ,4DAA4D;AAClG,SAASC,oBAAoB,QAAQ,yCAAyC;AAC9E,SAASC,WAAW,EAAEC,WAAW,IAAIC,eAAe,EAAEC,0BAA0B,QAAQ,kDAAkD;AAC1I,SAASC,MAAM,QAAQ,uCAAuC;AAC9D,SAASC,cAAc,QAAQ,8CAA8C;AAC7E,SAASC,qBAAqB,QAAQ,4DAA4D;AAClG,SAASC,gBAAgB,EAAEC,oBAAoB,QAAQ,2DAA2D;AAClH,SAASC,gBAAgB,QAAQ,wCAAwC;AACzE,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,SAASC,WAAW,QAAQ,qCAAqC;AACjE,SAASC,qBAAqB,QAAQ,4DAA4D;AAClG,SAASC,MAAM,QAAQ,0CAA0C;AACjE,MAAMC,CAAC,GAAGlB,GAAG,CAACkB,CAAC;AACf,IAAIb,WAAW,GAAG,MAAMA,WAAW,SAASG,MAAM,CAAC;EAC/C,IAAIW,aAAaA,CAAA,EAAG;IAChB,OAAOnB,GAAG,CAACoB,SAAS,CAAC,IAAI,CAACC,OAAO,CAACC,cAAc,CAAC,CAAC,CAAC,CAAC;EACxD;EACA,IAAIC,sBAAsBA,CAAA,EAAG;IACzB,OAAOvB,GAAG,CAACoB,SAAS,CAAC,IAAI,CAACC,OAAO,CAACC,cAAc,CAAC,CAAC,CAAC,CAAC,CAACE,QAAQ,CAACC,eAAe;EACjF;EACA,IAAIC,UAAUA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACC,WAAW;EAAE;EAC5C,IAAIC,SAASA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACC,iBAAiB,CAACD,SAAS;EAAE;EAC3D,IAAIE,OAAOA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACC,MAAM,CAACC,gBAAgB;EAAE;EACrD,IAAIC,SAASA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACC,UAAU,CAACC,KAAK;EAAE;EAChD,IAAIC,eAAeA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACC,gBAAgB,CAACF,KAAK;EAAE;EAC5D,IAAIG,MAAMA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACC,cAAc,KAAK,CAAC,CAAC,4BAA4B,CAAC,CAAC,6BAA6B,CAAC,CAAC;EAA4B;EACzI,IAAIC,CAACA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACC,EAAE;EAAE;EAC1B,IAAIC,CAACA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACC,EAAE;EAAE;EAC1B;AACJ;AACA;AACA;EACI,IAAIC,QAAQA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACC,SAAS;EAAE;EACxC,IAAID,QAAQA,CAACE,KAAK,EAAE;IAChB,IAAI,IAAI,CAACD,SAAS,KAAKC,KAAK,EAAE;MAC1B;IACJ;IACA,IAAI,CAACD,SAAS,GAAGC,KAAK;IACtB,IAAI,CAACC,eAAe,CAACC,SAAS,CAACC,MAAM,CAAC,QAAQ,EAAE,IAAI,CAACJ,SAAS,CAAC;EACnE;EACAK,WAAWA,CAACC,OAAO,EAAEC,kBAAkB,EAAEC,qBAAqB,EAAEC,cAAc,EAAEC,qBAAqB,EAAEC,qBAAqB,EAAE;IAC1H,KAAK,CAAC,CAAC;IACP,IAAI,CAACJ,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,qBAAqB,GAAGA,qBAAqB;IAClD,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,qBAAqB,GAAGA,qBAAqB;IAClD,IAAI,CAACC,qBAAqB,GAAGA,qBAAqB;IAClD,IAAI,CAACC,iBAAiB,GAAG,IAAI3D,eAAe,CAAC,CAAC;IAC9C,IAAI,CAAC6B,WAAW,GAAG,KAAK;IACxB,IAAI,CAAC+B,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACjB,EAAE,GAAG,CAAC;IACX,IAAI,CAACE,EAAE,GAAG,CAAC;IACX,IAAI,CAACE,SAAS,GAAG,KAAK;IACtB,IAAI,CAACc,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAAC1B,UAAU,GAAG,IAAI,CAAC2B,SAAS,CAAC,IAAI9D,OAAO,CAAC,CAAC,CAAC;IAC/C,IAAI,CAACsC,gBAAgB,GAAG,IAAI,CAACwB,SAAS,CAAC,IAAI9D,OAAO,CAAC,CAAC,CAAC;IACrD,IAAI,CAAC+D,YAAY,GAAGX,OAAO,CAACY,WAAW,KAAKC,GAAG,IAAI;MAC/C,OAAOpD,oBAAoB,CAAC,IAAI,CAAC0C,cAAc,EAAEU,GAAG,EAAEnD,gBAAgB,CAACsC,OAAO,CAACc,OAAO,CAAC,GAAGd,OAAO,CAACc,OAAO,CAACC,SAAS,GAAGC,SAAS,CAAC;IACpI,CAAC,CAAC;IACF,IAAI,CAAC9C,OAAO,GAAG,gBAAgB,IAAI8B,OAAO,CAACtE,MAAM,GAAGsE,OAAO,CAACtE,MAAM,GAAG,IAAIuF,kBAAkB,CAACjB,OAAO,CAACtE,MAAM,CAAC;IAC3G,IAAI,CAACwF,aAAa,GAAGlB,OAAO,CAACmB,UAAU,EAAEC,WAAW,GAAGrD,CAAC,CAAC,6BAA6B,CAAC,GAAGiD,SAAS;IACnG,IAAI,CAACpC,MAAM,GAAG,IAAI,CAAC8B,SAAS,CAAC,IAAIvD,eAAe,CAAC,CAAC,CAAC;IACnD,IAAI,CAACyB,MAAM,CAACC,gBAAgB,CAACgB,SAAS,CAACwB,GAAG,CAAC,iBAAiB,EAAE,QAAQ,CAAC;IACvE,IAAIrB,OAAO,CAACmB,UAAU,EAAEG,OAAO,EAAE;MAC7B,IAAI,CAAC1C,MAAM,CAACC,gBAAgB,CAACgB,SAAS,CAACwB,GAAG,CAAC,iBAAiB,EAAE,SAAS,CAAC;IAC5E;IACA,IAAIrB,OAAO,CAACmB,UAAU,EAAEI,mBAAmB,EAAE;MACzC,IAAI,CAAC3C,MAAM,CAACC,gBAAgB,CAACgB,SAAS,CAACwB,GAAG,CAAC,cAAc,CAAC;IAC9D;IACA,IAAIrB,OAAO,CAACwB,iBAAiB,EAAE;MAC3B,IAAI,CAAC5C,MAAM,CAACC,gBAAgB,CAACgB,SAAS,CAACwB,GAAG,CAAC,GAAGrB,OAAO,CAACwB,iBAAiB,CAAC;IAC5E;IACA,IAAIxB,OAAO,CAACyB,QAAQ,EAAEC,aAAa,EAAE;MACjC,IAAI,CAACnB,cAAc,GAAG,IAAI;IAC9B;IACA,IAAIP,OAAO,CAAC2B,SAAS,EAAE;MACnB,IAAI,CAACnB,iBAAiB,GAAG,IAAI;IACjC;IACA,IAAI,CAACpB,cAAc,GAAGY,OAAO,CAACyB,QAAQ,EAAEG,aAAa,IAAI,CAAC,CAAC;IAC3D;IACA;IACA,IAAI,CAACC,WAAW,CAAC,IAAI,CAACjD,MAAM,CAACC,gBAAgB,EAAEiD,CAAC,IAAIA,CAAC,CAACC,eAAe,CAAC,CAAC,CAAC;IACxE;IACA,IAAI,CAACC,SAAS,CAAC,IAAI,CAACpD,MAAM,CAACC,gBAAgB,EAAEiD,CAAC,IAAI;MAC9C,IAAIA,CAAC,CAACG,MAAM,CAAC,CAAC,CAAC,oBAAoB,CAAC,EAAE;QAClC,IAAI,CAACC,OAAO,CAAC,CAAC;MAClB;IACJ,CAAC,CAAC;IACF;IACA,IAAI,CAACxB,SAAS,CAAC7D,GAAG,CAACsF,qBAAqB,CAAC,IAAI,CAACnE,aAAa,EAAE,MAAM,EAAE,MAAM,IAAI,CAACkE,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3F,MAAME,UAAU,GAAGrE,CAAC,CAAC,8BAA8B,CAAC;IACpD,MAAMsE,eAAe,GAAGtE,CAAC,CAAC,oBAAoB,CAAC;IAC/C,IAAI,OAAOiC,OAAO,CAACc,OAAO,KAAK,QAAQ,EAAE;MACrCuB,eAAe,CAACC,WAAW,GAAGtC,OAAO,CAACc,OAAO;MAC7CuB,eAAe,CAACE,KAAK,CAACC,UAAU,GAAG,UAAU;IACjD,CAAC,MACI,IAAI3F,GAAG,CAAC4F,aAAa,CAACzC,OAAO,CAACc,OAAO,CAAC,EAAE;MACzCuB,eAAe,CAACK,WAAW,CAAC1C,OAAO,CAACc,OAAO,CAAC;MAC5CuB,eAAe,CAACxC,SAAS,CAACwB,GAAG,CAAC,qBAAqB,CAAC;IACxD,CAAC,MACI;MACD,MAAMsB,QAAQ,GAAG3C,OAAO,CAACc,OAAO;MAChC,MAAM8B,UAAU,GAAG,IAAI,CAACxC,qBAAqB,CAACyC,cAAc,CAACrF,gBAAgB,EAAE;QAAEsF,mBAAmB,EAAE,IAAI,CAAC5C,qBAAqB,CAAC6C,QAAQ,CAAC,QAAQ,CAAC,CAACC,UAAU,IAAIhG,oBAAoB,CAACgG;MAAW,CAAC,CAAC;MACpM,MAAM;QAAEC;MAAQ,CAAC,GAAGL,UAAU,CAACM,MAAM,CAACP,QAAQ,EAAE;QAC5CQ,aAAa,EAAE;UACXC,QAAQ,EAAGtC,OAAO,IAAK,IAAI,CAACH,YAAY,CAACG,OAAO,CAAC;UACjDuC,WAAW,EAAE,IAAI,CAAC/C;QACtB,CAAC;QACDgD,mBAAmB,EAAEA,CAAA,KAAM;UACvBjB,eAAe,CAACxC,SAAS,CAACwB,GAAG,CAAC,qBAAqB,CAAC;UACpD,IAAI,CAACkC,MAAM,CAAC,CAAC;UACb;UACA,IAAI,CAACrE,gBAAgB,CAACsE,IAAI,CAAC,CAAC;QAChC;MACJ,CAAC,CAAC;MACFnB,eAAe,CAACK,WAAW,CAACO,OAAO,CAAC;IACxC;IACAb,UAAU,CAACM,WAAW,CAACL,eAAe,CAAC;IACvC,IAAI,CAACzD,MAAM,CAAC6E,eAAe,CAACf,WAAW,CAACN,UAAU,CAAC;IACnD,IAAIpC,OAAO,CAAC0D,OAAO,IAAI1D,OAAO,CAAC0D,OAAO,CAAC3H,MAAM,GAAG,CAAC,EAAE;MAC/C,MAAM4H,gBAAgB,GAAG5F,CAAC,CAAC,0BAA0B,CAAC;MACtD,MAAM6F,cAAc,GAAG7F,CAAC,CAAC,aAAa,CAAC;MACvCiC,OAAO,CAAC0D,OAAO,CAACG,OAAO,CAACC,MAAM,IAAI;QAC9B,MAAMC,UAAU,GAAG,IAAI,CAAC9D,kBAAkB,CAAC+D,gBAAgB,CAACF,MAAM,CAACG,SAAS,CAAC;QAC7E,MAAMC,eAAe,GAAGH,UAAU,GAAGA,UAAU,CAACI,QAAQ,CAAC,CAAC,GAAG,IAAI;QACjElH,WAAW,CAACiG,MAAM,CAACU,cAAc,EAAE;UAC/BQ,KAAK,EAAEN,MAAM,CAACM,KAAK;UACnBH,SAAS,EAAEH,MAAM,CAACG,SAAS;UAC3BI,GAAG,EAAEvC,CAAC,IAAI;YACNgC,MAAM,CAACO,GAAG,CAACvC,CAAC,CAAC;YACb,IAAI,CAACI,OAAO,CAAC,CAAC;UAClB,CAAC;UACDoC,SAAS,EAAER,MAAM,CAACQ;QACtB,CAAC,EAAEJ,eAAe,CAAC;MACvB,CAAC,CAAC;MACFP,gBAAgB,CAACjB,WAAW,CAACkB,cAAc,CAAC;MAC5C,IAAI,CAAChF,MAAM,CAACC,gBAAgB,CAAC6D,WAAW,CAACiB,gBAAgB,CAAC;IAC9D;IACA,IAAI,CAAC/D,eAAe,GAAG7B,CAAC,CAAC,+BAA+B,CAAC;IACzD,IAAI,IAAI,CAACmD,aAAa,EAAE;MACpB,IAAI,CAACtB,eAAe,CAAC8C,WAAW,CAAC,IAAI,CAACxB,aAAa,CAAC;IACxD;IACA,IAAI,CAACtB,eAAe,CAAC8C,WAAW,CAAC,IAAI,CAAC9D,MAAM,CAACC,gBAAgB,CAAC;IAC9D;IACA,IAAI0F,WAAW;IACf,IAAIvE,OAAO,CAAC0D,OAAO,IAAI1D,OAAO,CAAC0D,OAAO,CAAC3H,MAAM,GAAG,CAAC,EAAE;MAC/C;MACAwI,WAAW,GAAG,KAAK;IACvB,CAAC,MACI;MACD,IAAIvE,OAAO,CAACwE,WAAW,EAAED,WAAW,KAAKvD,SAAS,EAAE;QAChD;QACA;QACAuD,WAAW,GAAG,OAAOvE,OAAO,CAACc,OAAO,KAAK,QAAQ,IAC7CpD,gBAAgB,CAACsC,OAAO,CAACc,OAAO,CAAC,IAAI,CAACd,OAAO,CAACc,OAAO,CAACnB,KAAK,CAAC8E,QAAQ,CAAC,IAAI,CAAC,IAAI,CAACzE,OAAO,CAACc,OAAO,CAACnB,KAAK,CAAC8E,QAAQ,CAAC,MAAM,CAAC;MAC7H,CAAC,MACI;QACD;QACAF,WAAW,GAAGvE,OAAO,CAACwE,WAAW,CAACD,WAAW;MACjD;IACJ;IACA;IACA,IAAIvE,OAAO,CAACmB,UAAU,EAAEuD,aAAa,EAAE;MACnC,MAAMf,gBAAgB,GAAG5F,CAAC,CAAC,0BAA0B,CAAC;MACtD,MAAM4G,WAAW,GAAG5G,CAAC,CAAC,UAAU,CAAC;MACjC4G,WAAW,CAACrC,WAAW,GAAG3E,QAAQ,CAAC,WAAW,EAAE,4BAA4B,EAAEC,WAAW,GAAG,QAAQ,GAAG,KAAK,CAAC;MAC7G+F,gBAAgB,CAACjB,WAAW,CAACiC,WAAW,CAAC;MACzC,IAAI,CAAC/F,MAAM,CAACC,gBAAgB,CAAC6D,WAAW,CAACiB,gBAAgB,CAAC;IAC9D;IACA,MAAMiB,mBAAmB,GAAG,CAAC,GAAG,IAAI,CAAC1G,OAAO,CAACC,cAAc,CAAC;IAC5D,IAAI,CAACoG,WAAW,EAAE;MACdK,mBAAmB,CAACC,IAAI,CAAC,IAAI,CAACjF,eAAe,CAAC;IAClD;IACA,MAAMkF,YAAY,GAAG,IAAI,CAACpE,SAAS,CAAC,IAAIqE,qBAAqB,CAACH,mBAAmB,CAAC,CAAC;IACnF,IAAI,CAAClE,SAAS,CAACoE,YAAY,CAACE,UAAU,CAAC,MAAM;MACzC,IAAI,CAAC,IAAI,CAACtF,SAAS,EAAE;QACjB,IAAI,CAACwC,OAAO,CAAC,CAAC;MAClB;IACJ,CAAC,CAAC,CAAC;IACH;IACA;IACA;IACA,IAAIqC,WAAW,EAAE;MACb,MAAMU,oBAAoB,GAAG,CAAC,GAAG,IAAI,CAAC/G,OAAO,CAACC,cAAc,EAAE,IAAI,CAACyB,eAAe,CAAC;MACnF,IAAI,CAAClB,iBAAiB,GAAG,IAAI,CAACgC,SAAS,CAAC,IAAIqE,qBAAqB,CAACE,oBAAoB,CAAC,CAAC;MACxF,IAAI,CAACvE,SAAS,CAAC,IAAI,CAAChC,iBAAiB,CAACsG,UAAU,CAAC,MAAM;QACnD,IAAI,CAAC,IAAI,CAACtF,SAAS,EAAE;UACjB,IAAI,CAACwC,OAAO,CAAC,CAAC;QAClB;MACJ,CAAC,CAAC,CAAC;IACP,CAAC,MACI;MACD,IAAI,CAACxD,iBAAiB,GAAGoG,YAAY;IACzC;EACJ;EACAI,YAAYA,CAAA,EAAG;IACX,IAAI,CAAC,IAAI,CAAC1E,iBAAiB,IAAI,IAAI,CAACC,eAAe,EAAE;MACjD;IACJ;IACA,IAAI,CAACA,eAAe,GAAG,IAAI;IAC3B;IACA,MAAM0E,0BAA0B,GAAG,IAAI,CAACvG,MAAM,CAACC,gBAAgB;IAC/D,MAAMuG,yBAAyB,GAAG,IAAI,CAACC,sBAAsB,CAAC,IAAI,CAACzG,MAAM,CAACC,gBAAgB,CAAC;IAC3F,IAAIuG,yBAAyB,EAAE;MAC3B,MAAME,2BAA2B,GAAGzI,GAAG,CAAC0I,OAAO,CAAC,IAAI,CAAC3F,eAAe,EAAE7B,CAAC,CAAC,KAAK,CAAC,CAAC;MAC/E,MAAMyH,0BAA0B,GAAG3I,GAAG,CAAC4I,MAAM,CAAC,IAAI,CAAC7F,eAAe,EAAE7B,CAAC,CAAC,KAAK,CAAC,CAAC;MAC7EuH,2BAA2B,CAACI,QAAQ,GAAG,CAAC;MACxCF,0BAA0B,CAACE,QAAQ,GAAG,CAAC;MACvC,IAAI,CAAChF,SAAS,CAAC7D,GAAG,CAACsF,qBAAqB,CAACqD,0BAA0B,EAAE,OAAO,EAAG1D,CAAC,IAAK;QACjFqD,0BAA0B,CAACQ,KAAK,CAAC,CAAC;QAClC7D,CAAC,CAAC8D,cAAc,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC;MACH,IAAI,CAAClF,SAAS,CAAC7D,GAAG,CAACsF,qBAAqB,CAACmD,2BAA2B,EAAE,OAAO,EAAGxD,CAAC,IAAK;QAClFsD,yBAAyB,CAACO,KAAK,CAAC,CAAC;QACjC7D,CAAC,CAAC8D,cAAc,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC;IACP;EACJ;EACAP,sBAAsBA,CAACQ,IAAI,EAAE;IACzB,IAAIA,IAAI,CAACC,aAAa,CAAC,CAAC,EAAE;MACtB,KAAK,IAAIxJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuJ,IAAI,CAACE,UAAU,CAAChK,MAAM,EAAEO,CAAC,EAAE,EAAE;QAC7C,MAAM0J,IAAI,GAAGH,IAAI,CAACE,UAAU,CAACE,IAAI,CAACJ,IAAI,CAACE,UAAU,CAAChK,MAAM,GAAGO,CAAC,GAAG,CAAC,CAAC;QACjE,IAAI0J,IAAI,CAACE,QAAQ,KAAKF,IAAI,CAACG,YAAY,EAAE;UACrC,MAAMC,UAAU,GAAGJ,IAAI;UACvB,IAAI,OAAOI,UAAU,CAACV,QAAQ,KAAK,QAAQ,IAAIU,UAAU,CAACV,QAAQ,IAAI,CAAC,EAAE;YACrE,OAAOU,UAAU;UACrB;QACJ;QACA,MAAMC,uBAAuB,GAAG,IAAI,CAAChB,sBAAsB,CAACW,IAAI,CAAC;QACjE,IAAIK,uBAAuB,EAAE;UACzB,OAAOA,uBAAuB;QAClC;MACJ;IACJ;IACA,OAAOrF,SAAS;EACpB;EACAkC,MAAMA,CAACoD,SAAS,EAAE;IACdA,SAAS,CAAC5D,WAAW,CAAC,IAAI,CAAC9C,eAAe,CAAC;IAC3C,MAAM2G,YAAY,GAAG,IAAI,CAAC3G,eAAe,CAAC4G,QAAQ,CAAC,IAAI,CAAC5G,eAAe,CAAC6G,aAAa,CAACC,aAAa,CAAC;IACpG,MAAMC,kBAAkB,GAAGJ,YAAY,IAAInJ,0BAA0B,CAAC,IAAI,CAAC8C,qBAAqB,CAAC6C,QAAQ,CAAC,+BAA+B,CAAC,KAAK,IAAI,IAAI,IAAI,CAAC1C,qBAAqB,CAACuG,uBAAuB,CAAC,CAAC,EAAE,IAAI,CAAC3G,kBAAkB,CAAC+D,gBAAgB,CAAC,8BAA8B,CAAC,EAAE6C,YAAY,CAAC,CAAC,CAAC;IACtS,IAAIF,kBAAkB,EAAE;MACpB7I,MAAM,CAAC6I,kBAAkB,CAAC;IAC9B;IACA,IAAI,CAACpD,MAAM,CAAC,CAAC;IACb,IAAI,CAAC2B,YAAY,CAAC,CAAC;EACvB;EACA3B,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC3E,MAAM,CAACC,gBAAgB,CAACgB,SAAS,CAACiH,MAAM,CAAC,eAAe,CAAC;IAC9D,IAAI,CAAClI,MAAM,CAAC6E,eAAe,CAAClB,KAAK,CAACwE,SAAS,GAAG,EAAE;IAChD,MAAMC,kCAAkC,GAAIlF,CAAC,IAAK;MAC9C,MAAMmF,IAAI,GAAGpK,GAAG,CAACqK,mBAAmB,CAACpF,CAAC,CAAC;MACvC,MAAMqF,YAAY,GAAGrF,CAAC,CAACsF,qBAAqB,CAAC,CAAC;MAC9C,OAAO;QACHC,GAAG,EAAEF,YAAY,CAACE,GAAG,GAAGJ,IAAI;QAC5BK,MAAM,EAAEH,YAAY,CAACG,MAAM,GAAGL,IAAI;QAClCM,KAAK,EAAEJ,YAAY,CAACI,KAAK,GAAGN,IAAI;QAChCO,IAAI,EAAEL,YAAY,CAACK,IAAI,GAAGP;MAC9B,CAAC;IACL,CAAC;IACD,MAAMQ,YAAY,GAAG,IAAI,CAACvJ,OAAO,CAACC,cAAc,CAACuJ,GAAG,CAAC5F,CAAC,IAAIkF,kCAAkC,CAAClF,CAAC,CAAC,CAAC;IAChG,MAAM;MAAEuF,GAAG;MAAEE,KAAK;MAAED,MAAM;MAAEE;IAAK,CAAC,GAAGC,YAAY,CAAC,CAAC,CAAC;IACpD,MAAME,KAAK,GAAGJ,KAAK,GAAGC,IAAI;IAC1B,MAAMI,MAAM,GAAGN,MAAM,GAAGD,GAAG;IAC3B,MAAMQ,UAAU,GAAG;MACfR,GAAG;MAAEE,KAAK;MAAED,MAAM;MAAEE,IAAI;MAAEG,KAAK;MAAEC,MAAM;MACvCE,MAAM,EAAE;QACJzI,CAAC,EAAEmI,IAAI,GAAIG,KAAK,GAAG,CAAE;QACrBpI,CAAC,EAAE8H,GAAG,GAAIO,MAAM,GAAG;MACvB;IACJ,CAAC;IACD;IACA,IAAI,CAACG,6BAA6B,CAACF,UAAU,CAAC;IAC9C,IAAI,CAACG,2BAA2B,CAACH,UAAU,CAAC;IAC5C;IACA,IAAI,CAACI,oBAAoB,CAACJ,UAAU,CAAC;IACrC;IACA,IAAI,CAACjI,eAAe,CAAC2C,KAAK,CAAC2F,OAAO,GAAG,EAAE;IACvC,IAAI,CAACtI,eAAe,CAAC2C,KAAK,CAAC4F,MAAM,GAAG,EAAE;IACtC,IAAI,IAAI,CAACjH,aAAa,EAAE;MACpB,QAAQ,IAAI,CAAC9B,cAAc;QACvB,KAAK,CAAC,CAAC;UACHyI,UAAU,CAACL,IAAI,IAAI,CAAC,CAAC;UACrBK,UAAU,CAACN,KAAK,IAAI,CAAC,CAAC;UACtB,IAAI,CAAC3H,eAAe,CAAC2C,KAAK,CAAC6F,WAAW,GAAG,GAAG,CAAC,CAAC,+BAA+B;UAC7E,IAAI,CAACxI,eAAe,CAAC2C,KAAK,CAAC8F,UAAU,GAAG,GAAG,CAAC,CAAC,CAAC,+BAA+B;UAC7E;QACJ,KAAK,CAAC,CAAC;UACHR,UAAU,CAACL,IAAI,IAAI,CAAC,CAAC;UACrBK,UAAU,CAACN,KAAK,IAAI,CAAC,CAAC;UACtB,IAAI,CAAC3H,eAAe,CAAC2C,KAAK,CAAC+F,YAAY,GAAG,GAAG,CAAC,CAAC,+BAA+B;UAC9E,IAAI,CAAC1I,eAAe,CAAC2C,KAAK,CAACgG,WAAW,GAAG,GAAG,CAAC,CAAC,CAAC,+BAA+B;UAC9E;QACJ,KAAK,CAAC,CAAC;UACHV,UAAU,CAACR,GAAG,IAAI,CAAC,CAAC;UACpBQ,UAAU,CAACP,MAAM,IAAI,CAAC,CAAC;UACvB,IAAI,CAAC1H,eAAe,CAAC2C,KAAK,CAACiG,UAAU,GAAG,GAAG,CAAC,CAAC,+BAA+B;UAC5E,IAAI,CAAC5I,eAAe,CAAC2C,KAAK,CAACkG,SAAS,GAAG,GAAG,CAAC,CAAC,CAAC,+BAA+B;UAC5E;QACJ,KAAK,CAAC,CAAC;UACHZ,UAAU,CAACR,GAAG,IAAI,CAAC,CAAC;UACpBQ,UAAU,CAACP,MAAM,IAAI,CAAC,CAAC;UACvB,IAAI,CAAC1H,eAAe,CAAC2C,KAAK,CAACmG,aAAa,GAAG,GAAG,CAAC,CAAC,+BAA+B;UAC/E,IAAI,CAAC9I,eAAe,CAAC2C,KAAK,CAACoG,YAAY,GAAG,GAAG,CAAC,CAAC,CAAC,+BAA+B;UAC/E;MACR;MACAd,UAAU,CAACC,MAAM,CAACzI,CAAC,GAAGwI,UAAU,CAACL,IAAI,GAAIG,KAAK,GAAG,CAAE;MACnDE,UAAU,CAACC,MAAM,CAACvI,CAAC,GAAGsI,UAAU,CAACR,GAAG,GAAIO,MAAM,GAAG,CAAE;IACvD;IACA,IAAI,CAACgB,iBAAiB,CAACf,UAAU,CAAC;IAClC,IAAI,CAACgB,iBAAiB,CAAChB,UAAU,CAAC;IAClC,IAAI,IAAI,CAAC3G,aAAa,EAAE;MACpB;MACA,IAAI,CAACA,aAAa,CAACrB,SAAS,CAACiH,MAAM,CAAC,KAAK,CAAC;MAC1C,IAAI,CAAC5F,aAAa,CAACrB,SAAS,CAACiH,MAAM,CAAC,MAAM,CAAC;MAC3C,IAAI,CAAC5F,aAAa,CAACrB,SAAS,CAACiH,MAAM,CAAC,OAAO,CAAC;MAC5C,IAAI,CAAC5F,aAAa,CAACrB,SAAS,CAACiH,MAAM,CAAC,QAAQ,CAAC;MAC7C,IAAI,CAACgC,uBAAuB,CAACjB,UAAU,CAAC;IAC5C;IACA,IAAI,CAACjJ,MAAM,CAACmK,iBAAiB,CAAC,CAAC;EACnC;EACAH,iBAAiBA,CAAClN,MAAM,EAAE;IACtB,MAAMsN,UAAU,GAAG,IAAI,CAACpK,MAAM,CAACC,gBAAgB,CAACoK,WAAW,GAAG,CAAC,CAAC;IAChE,IAAI,IAAI,CAAC/K,OAAO,CAACmB,CAAC,KAAK2B,SAAS,EAAE;MAC9B,IAAI,CAAC1B,EAAE,GAAG,IAAI,CAACpB,OAAO,CAACmB,CAAC;IAC5B,CAAC,MACI,IAAI,IAAI,CAACD,cAAc,KAAK,CAAC,CAAC,2BAA2B;MAC1D,IAAI,CAACE,EAAE,GAAG5D,MAAM,CAAC6L,KAAK;IAC1B,CAAC,MACI,IAAI,IAAI,CAACnI,cAAc,KAAK,CAAC,CAAC,0BAA0B;MACzD,IAAI,CAACE,EAAE,GAAG5D,MAAM,CAAC8L,IAAI,GAAGwB,UAAU;IACtC,CAAC,MACI;MACD,IAAI,IAAI,CAAC9H,aAAa,EAAE;QACpB,IAAI,CAAC5B,EAAE,GAAG5D,MAAM,CAACoM,MAAM,CAACzI,CAAC,GAAI,IAAI,CAACT,MAAM,CAACC,gBAAgB,CAACoK,WAAW,GAAG,CAAE;MAC9E,CAAC,MACI;QACD,IAAI,CAAC3J,EAAE,GAAG5D,MAAM,CAAC8L,IAAI;MACzB;MACA;MACA,IAAI,IAAI,CAAClI,EAAE,GAAG0J,UAAU,IAAI,IAAI,CAAC5K,sBAAsB,CAAC6K,WAAW,EAAE;QACjE,IAAI,CAACrK,MAAM,CAACC,gBAAgB,CAACgB,SAAS,CAACwB,GAAG,CAAC,eAAe,CAAC;QAC3D,IAAI,CAAC/B,EAAE,GAAG4J,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC/K,sBAAsB,CAAC6K,WAAW,GAAGD,UAAU,GAAG,CAAC,CAAC,uCAAuC,IAAI,CAAC5K,sBAAsB,CAACgL,UAAU,CAAC;MAC9J;IACJ;IACA;IACA,IAAI,IAAI,CAAC9J,EAAE,GAAG,IAAI,CAAClB,sBAAsB,CAACgL,UAAU,EAAE;MAClD,IAAI,CAAC9J,EAAE,GAAG5D,MAAM,CAAC8L,IAAI,GAAG,CAAC,CAAC;IAC9B;EACJ;EACAqB,iBAAiBA,CAACnN,MAAM,EAAE;IACtB,IAAI,IAAI,CAACwC,OAAO,CAACqB,CAAC,KAAKyB,SAAS,EAAE;MAC9B,IAAI,CAACxB,EAAE,GAAG,IAAI,CAACtB,OAAO,CAACqB,CAAC;IAC5B,CAAC,MACI,IAAI,IAAI,CAACH,cAAc,KAAK,CAAC,CAAC,2BAA2B;MAC1D,IAAI,CAACI,EAAE,GAAG9D,MAAM,CAAC2L,GAAG;IACxB,CAAC,MACI,IAAI,IAAI,CAACjI,cAAc,KAAK,CAAC,CAAC,2BAA2B;MAC1D,IAAI,CAACI,EAAE,GAAG9D,MAAM,CAAC4L,MAAM,GAAG,CAAC;IAC/B,CAAC,MACI;MACD,IAAI,IAAI,CAACpG,aAAa,EAAE;QACpB,IAAI,CAAC1B,EAAE,GAAG9D,MAAM,CAACoM,MAAM,CAACvI,CAAC,GAAI,IAAI,CAACX,MAAM,CAACC,gBAAgB,CAACwK,YAAY,GAAG,CAAE;MAC/E,CAAC,MACI;QACD,IAAI,CAAC7J,EAAE,GAAG9D,MAAM,CAAC4L,MAAM;MAC3B;IACJ;IACA;IACA,IAAI,IAAI,CAAC9H,EAAE,GAAG,IAAI,CAACxB,aAAa,CAACsL,WAAW,EAAE;MAC1C,IAAI,CAAC9J,EAAE,GAAG9D,MAAM,CAAC4L,MAAM;IAC3B;EACJ;EACAS,6BAA6BA,CAACrM,MAAM,EAAE;IAClC;IACA,IAAI,IAAI,CAACwC,OAAO,CAACmB,CAAC,KAAK2B,SAAS,EAAE;MAC9B;IACJ;IACA,MAAMuI,kBAAkB,GAAI,IAAI,CAACrI,aAAa,GAAG,CAAC,CAAC,8BAA8B,CAAE;IACnF;IACA,IAAI,IAAI,CAACX,cAAc,EAAE;MACrB,MAAM2H,OAAO,GAAGqB,kBAAkB,GAAG,CAAC,CAAC;MACvC,IAAI,IAAI,CAACnK,cAAc,KAAK,CAAC,CAAC,2BAA2B;QACrD,IAAI,CAACR,MAAM,CAACC,gBAAgB,CAAC0D,KAAK,CAACiH,QAAQ,GAAG,GAAG,IAAI,CAACpL,sBAAsB,CAAC6K,WAAW,GAAGvN,MAAM,CAAC6L,KAAK,GAAGW,OAAO,IAAI;MACzH,CAAC,MACI,IAAI,IAAI,CAAC9I,cAAc,KAAK,CAAC,CAAC,0BAA0B;QACzD,IAAI,CAACR,MAAM,CAACC,gBAAgB,CAAC0D,KAAK,CAACiH,QAAQ,GAAG,GAAG9N,MAAM,CAAC8L,IAAI,GAAGU,OAAO,IAAI;MAC9E;MACA;IACJ;IACA;IACA,IAAI,IAAI,CAAC9I,cAAc,KAAK,CAAC,CAAC,2BAA2B;MACrD,MAAMqK,WAAW,GAAG,IAAI,CAACrL,sBAAsB,CAAC6K,WAAW,GAAGvN,MAAM,CAAC6L,KAAK;MAC1E;MACA,IAAIkC,WAAW,GAAG,IAAI,CAAC7K,MAAM,CAACC,gBAAgB,CAACoK,WAAW,GAAGM,kBAAkB,EAAE;QAC7E,MAAMG,UAAU,GAAGhO,MAAM,CAAC8L,IAAI;QAC9B;QACA,IAAIkC,UAAU,IAAI,IAAI,CAAC9K,MAAM,CAACC,gBAAgB,CAACoK,WAAW,GAAGM,kBAAkB,EAAE;UAC7E,IAAI,CAACnK,cAAc,GAAG,CAAC,CAAC;QAC5B;QACA;QAAA,KACK;UACD,IAAI,CAACA,cAAc,GAAG,CAAC,CAAC;QAC5B;MACJ;IACJ;IACA;IAAA,KACK,IAAI,IAAI,CAACA,cAAc,KAAK,CAAC,CAAC,0BAA0B;MACzD,MAAMsK,UAAU,GAAGhO,MAAM,CAAC8L,IAAI;MAC9B;MACA,IAAIkC,UAAU,GAAG,IAAI,CAAC9K,MAAM,CAACC,gBAAgB,CAACoK,WAAW,GAAGM,kBAAkB,EAAE;QAC5E,MAAME,WAAW,GAAG,IAAI,CAACrL,sBAAsB,CAAC6K,WAAW,GAAGvN,MAAM,CAAC6L,KAAK;QAC1E;QACA,IAAIkC,WAAW,IAAI,IAAI,CAAC7K,MAAM,CAACC,gBAAgB,CAACoK,WAAW,GAAGM,kBAAkB,EAAE;UAC9E,IAAI,CAACnK,cAAc,GAAG,CAAC,CAAC;QAC5B;QACA;QAAA,KACK;UACD,IAAI,CAACA,cAAc,GAAG,CAAC,CAAC;QAC5B;MACJ;MACA;MACA,IAAI1D,MAAM,CAAC8L,IAAI,GAAG,IAAI,CAAC5I,MAAM,CAACC,gBAAgB,CAACoK,WAAW,GAAGM,kBAAkB,IAAI,IAAI,CAACnL,sBAAsB,CAACgL,UAAU,EAAE;QACvH,IAAI,CAAChK,cAAc,GAAG,CAAC,CAAC;MAC5B;IACJ;EACJ;EACA4I,2BAA2BA,CAACtM,MAAM,EAAE;IAChC;IACA;IACA,IAAI,IAAI,CAACwC,OAAO,CAACqB,CAAC,KAAKyB,SAAS,IAAI,IAAI,CAACT,cAAc,EAAE;MACrD;IACJ;IACA,MAAMgJ,kBAAkB,GAAI,IAAI,CAACrI,aAAa,GAAG,CAAC,CAAC,8BAA8B,CAAE;IACnF;IACA,IAAI,IAAI,CAAC9B,cAAc,KAAK,CAAC,CAAC,2BAA2B;MACrD;MACA,IAAI1D,MAAM,CAAC2L,GAAG,GAAG,IAAI,CAACzI,MAAM,CAACC,gBAAgB,CAACwK,YAAY,GAAGE,kBAAkB,GAAG,CAAC,EAAE;QACjF,IAAI,CAACnK,cAAc,GAAG,CAAC,CAAC;MAC5B;IACJ;IACA;IAAA,KACK,IAAI,IAAI,CAACA,cAAc,KAAK,CAAC,CAAC,2BAA2B;MAC1D;MACA,IAAI1D,MAAM,CAAC4L,MAAM,GAAG,IAAI,CAAC1I,MAAM,CAACC,gBAAgB,CAACwK,YAAY,GAAGE,kBAAkB,GAAG,IAAI,CAACvL,aAAa,CAACsL,WAAW,EAAE;QACjH,IAAI,CAAClK,cAAc,GAAG,CAAC,CAAC;MAC5B;IACJ;EACJ;EACA6I,oBAAoBA,CAACvM,MAAM,EAAE;IACzB,IAAIqL,SAAS,GAAG,IAAI,CAAC/I,aAAa,CAACsL,WAAW,GAAG,CAAC;IAClD;IACA,IAAI,IAAI,CAAC/I,cAAc,EAAE;MACrB,MAAM2H,OAAO,GAAG,CAAC,IAAI,CAAChH,aAAa,GAAG,CAAC,CAAC,8BAA8B,CAAC,IAAI,CAAC,CAAC;MAC7E,IAAI,IAAI,CAAC9B,cAAc,KAAK,CAAC,CAAC,2BAA2B;QACrD2H,SAAS,GAAGmC,IAAI,CAACS,GAAG,CAAC5C,SAAS,EAAErL,MAAM,CAAC2L,GAAG,GAAGa,OAAO,CAAC;MACzD,CAAC,MACI,IAAI,IAAI,CAAC9I,cAAc,KAAK,CAAC,CAAC,2BAA2B;QAC1D2H,SAAS,GAAGmC,IAAI,CAACS,GAAG,CAAC5C,SAAS,EAAE,IAAI,CAAC/I,aAAa,CAACsL,WAAW,GAAG5N,MAAM,CAAC4L,MAAM,GAAGY,OAAO,CAAC;MAC7F;IACJ;IACA,IAAI,CAACtJ,MAAM,CAACC,gBAAgB,CAAC0D,KAAK,CAACwE,SAAS,GAAG,GAAGA,SAAS,IAAI;IAC/D,IAAI,IAAI,CAACnI,MAAM,CAAC6E,eAAe,CAAC4F,YAAY,GAAG,IAAI,CAACzK,MAAM,CAAC6E,eAAe,CAACmG,YAAY,EAAE;MACrF;MACA,MAAMC,iBAAiB,GAAG,GAAG,IAAI,CAACjL,MAAM,CAACkL,SAAS,CAAC9J,OAAO,CAAC+J,qBAAqB,IAAI;MACpF,IAAI,IAAI,CAACnL,MAAM,CAAC6E,eAAe,CAAClB,KAAK,CAAC+F,YAAY,KAAKuB,iBAAiB,EAAE;QACtE,IAAI,CAACjL,MAAM,CAAC6E,eAAe,CAAClB,KAAK,CAAC+F,YAAY,GAAGuB,iBAAiB;MACtE;IACJ;EACJ;EACAf,uBAAuBA,CAACpN,MAAM,EAAE;IAC5B,IAAI,CAAC,IAAI,CAACwF,aAAa,EAAE;MACrB;IACJ;IACA,QAAQ,IAAI,CAAC9B,cAAc;MACvB,KAAK,CAAC,CAAC;MACP,KAAK,CAAC,CAAC;QAA2B;UAC9B,IAAI,CAAC8B,aAAa,CAACrB,SAAS,CAACwB,GAAG,CAAC,IAAI,CAACjC,cAAc,KAAK,CAAC,CAAC,2BAA2B,OAAO,GAAG,MAAM,CAAC;UACvG,MAAM4K,WAAW,GAAG,IAAI,CAACpL,MAAM,CAACC,gBAAgB,CAACwK,YAAY;UAC7D;UACA,IAAIW,WAAW,GAAGtO,MAAM,CAACkM,MAAM,EAAE;YAC7B,IAAI,CAAC1G,aAAa,CAACqB,KAAK,CAAC8E,GAAG,GAAG,GAAG3L,MAAM,CAACoM,MAAM,CAACvI,CAAC,IAAI,IAAI,CAACC,EAAE,GAAGwK,WAAW,CAAC,GAAG,CAAC,CAAC,+BAA+B;UACnH;UACA;UAAA,KACK;YACD,IAAI,CAAC9I,aAAa,CAACqB,KAAK,CAAC8E,GAAG,GAAG,GAAG6B,IAAI,CAACe,KAAK,CAAED,WAAW,GAAG,CAAE,CAAC,GAAG,CAAC,CAAC,+BAA+B;UACvG;UACA;QACJ;MACA,KAAK,CAAC,CAAC;MACP,KAAK,CAAC,CAAC;QAA2B;UAC9B,IAAI,CAAC9I,aAAa,CAACrB,SAAS,CAACwB,GAAG,CAAC,IAAI,CAACjC,cAAc,KAAK,CAAC,CAAC,4BAA4B,QAAQ,GAAG,KAAK,CAAC;UACxG,MAAM4J,UAAU,GAAG,IAAI,CAACpK,MAAM,CAACC,gBAAgB,CAACoK,WAAW;UAC3D;UACA,IAAIiB,mBAAmB,GAAGhB,IAAI,CAACe,KAAK,CAAEjB,UAAU,GAAG,CAAE,CAAC,GAAG,CAAC,CAAC;UAC3D;UACA,MAAMmB,QAAQ,GAAG,IAAI,CAAC7K,EAAE,GAAG4K,mBAAmB;UAC9C,IAAIC,QAAQ,GAAGzO,MAAM,CAAC8L,IAAI,IAAI2C,QAAQ,GAAGzO,MAAM,CAAC6L,KAAK,EAAE;YACnD2C,mBAAmB,GAAGxO,MAAM,CAACoM,MAAM,CAACzI,CAAC,GAAG,IAAI,CAACC,EAAE,GAAG,CAAC,CAAC;UACxD;UACA,IAAI,CAAC4B,aAAa,CAACqB,KAAK,CAACiF,IAAI,GAAG,GAAG0C,mBAAmB,IAAI;UAC1D;QACJ;IACJ;EACJ;EACAvE,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAC/G,MAAM,CAACC,gBAAgB,CAAC8G,KAAK,CAAC,CAAC;EACxC;EACAzD,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC,IAAI,CAAC1D,WAAW,EAAE;MACnB,IAAI,CAACO,UAAU,CAACyE,IAAI,CAAC,CAAC;MACtB,IAAI,CAAC5D,eAAe,CAACkH,MAAM,CAAC,CAAC;MAC7B,IAAI,CAACxG,iBAAiB,CAAC4B,OAAO,CAAC,CAAC;MAChC,IAAI,CAAChE,OAAO,CAACgE,OAAO,CAAC,CAAC;MACtB,KAAK,CAACA,OAAO,CAAC,CAAC;IACnB;IACA,IAAI,CAAC1D,WAAW,GAAG,IAAI;EAC3B;AACJ,CAAC;AACDtB,WAAW,GAAG1B,UAAU,CAAC,CACrBgB,OAAO,CAAC,CAAC,EAAEM,kBAAkB,CAAC,EAC9BN,OAAO,CAAC,CAAC,EAAEO,qBAAqB,CAAC,EACjCP,OAAO,CAAC,CAAC,EAAEc,cAAc,CAAC,EAC1Bd,OAAO,CAAC,CAAC,EAAEe,qBAAqB,CAAC,EACjCf,OAAO,CAAC,CAAC,EAAEqB,qBAAqB,CAAC,CACpC,EAAEX,WAAW,CAAC;AACf,SAASA,WAAW;AACpB,MAAM6H,qBAAqB,SAAS1H,MAAM,CAAC;EACvC,IAAI2H,UAAUA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACoF,WAAW,CAACpL,KAAK;EAAE;EAClD,IAAIP,SAASA,CAAA,EAAG;IAAE,OAAO,IAAI,CAAC4L,UAAU;EAAE;EAC1CtK,WAAWA,CAACuK,SAAS,EAAE;IACnB,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACD,UAAU,GAAG,IAAI;IACtB,IAAI,CAACD,WAAW,GAAG,IAAI,CAAC1J,SAAS,CAAC,IAAI9D,OAAO,CAAC,CAAC,CAAC;IAChD,IAAI,CAAC0N,SAAS,CAACzG,OAAO,CAAC0G,CAAC,IAAI,IAAI,CAACC,WAAW,CAACD,CAAC,EAAE,MAAM,IAAI,CAACE,kBAAkB,CAACF,CAAC,CAAC,CAAC,CAAC;IAClF,IAAI,CAACD,SAAS,CAACzG,OAAO,CAAC0G,CAAC,IAAI,IAAI,CAACG,YAAY,CAACH,CAAC,EAAE,MAAM,IAAI,CAACI,mBAAmB,CAACJ,CAAC,CAAC,CAAC,CAAC;EACxF;EACAE,kBAAkBA,CAAC/O,MAAM,EAAE;IACvB,IAAI,CAAC2O,UAAU,GAAG,IAAI;IACtB,IAAI,CAACO,+BAA+B,CAAClP,MAAM,CAAC;EAChD;EACAiP,mBAAmBA,CAACjP,MAAM,EAAE;IACxB,IAAI,CAAC2O,UAAU,GAAG,KAAK;IACvB,IAAI,CAACQ,mBAAmB,CAACnP,MAAM,CAAC;EACpC;EACAmP,mBAAmBA,CAACnP,MAAM,EAAE;IACxB,IAAI,CAACkP,+BAA+B,CAAClP,MAAM,CAAC;IAC5C;IACA;IACA,IAAI,CAACoP,aAAa,GAAGjO,GAAG,CAACoB,SAAS,CAACvC,MAAM,CAAC,CAACqP,UAAU,CAAC,MAAM,IAAI,CAACC,mBAAmB,CAAC,CAAC,EAAE,CAAC,CAAC;EAC9F;EACAJ,+BAA+BA,CAAClP,MAAM,EAAE;IACpC,IAAI,IAAI,CAACoP,aAAa,EAAE;MACpBjO,GAAG,CAACoB,SAAS,CAACvC,MAAM,CAAC,CAACuP,YAAY,CAAC,IAAI,CAACH,aAAa,CAAC;MACtD,IAAI,CAACA,aAAa,GAAG9J,SAAS;IAClC;EACJ;EACAgK,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAAC,IAAI,CAACX,UAAU,EAAE;MAClB,IAAI,CAACD,WAAW,CAAC5G,IAAI,CAAC,CAAC;IAC3B;EACJ;AACJ;AACA,MAAMvC,kBAAkB,CAAC;EACrBlB,WAAWA,CAACmL,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAAC/M,cAAc,GAAG,CAAC,IAAI,CAAC+M,QAAQ,CAAC;EACzC;EACAhJ,OAAOA,CAAA,EAAG,CACV;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}