{"ast": null, "code": "import { CommonModule, formatDate } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport { ApprovalCardComponent, IconComponent, AvaTextboxComponent, AvaTagComponent, ButtonComponent } from '@ava/play-comp-library';\nimport approvalText from '../constants/approval.json';\nimport { debounceTime, distinctUntilChanged, map, startWith } from 'rxjs';\nimport { AgentsPreviewPanelComponent } from './agents-preview-panel/agents-preview-panel.component';\nimport { ApprovalTxtCardComponent } from '../approval-text-card/approval-text-card.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../shared/services/shared-api-service.service\";\nimport * as i3 from \"../../../shared/services/approval.service\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"../../../shared/services/drawer/drawer.service\";\nimport * as i6 from \"@shared/index\";\nimport * as i7 from \"@ava/play-comp-library\";\nfunction ApprovalAgentsComponent_Conditional_17_For_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵlistener(\"click\", function ApprovalAgentsComponent_Conditional_17_For_1_Template_div_click_0_listener() {\n      const $index_r2 = i0.ɵɵrestoreView(_r1).$index;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onCardClick($index_r2));\n    });\n    i0.ɵɵelementStart(1, \"ava-approval-card\", 15)(2, \"ava-card-header\");\n    i0.ɵɵelement(3, \"ava-icon\", 16);\n    i0.ɵɵelementStart(4, \"div\", 17)(5, \"h2\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"ava-tag\", 18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"ava-card-content\")(9, \"div\", 19)(10, \"div\", 20);\n    i0.ɵɵelement(11, \"ava-tag\", 21)(12, \"ava-tag\", 22)(13, \"ava-tag\", 23)(14, \"ava-tag\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 25)(16, \"div\", 26);\n    i0.ɵɵelement(17, \"ava-icon\", 27);\n    i0.ɵɵelementStart(18, \"span\");\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 28);\n    i0.ɵɵelement(21, \"ava-icon\", 29);\n    i0.ɵɵelementStart(22, \"span\");\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(24, \"ava-card-footer\")(25, \"div\", 30)(26, \"div\", 31)(27, \"span\", 32);\n    i0.ɵɵtext(28, \"Execution Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\");\n    i0.ɵɵelement(30, \"ava-icon\", 33);\n    i0.ɵɵelementStart(31, \"span\");\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(33, \"div\", 34)(34, \"ava-button\", 35);\n    i0.ɵɵlistener(\"userClick\", function ApprovalAgentsComponent_Conditional_17_For_1_Template_ava_button_userClick_34_listener() {\n      const $index_r2 = i0.ɵɵrestoreView(_r1).$index;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.handleTesting($index_r2));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"ava-button\", 36);\n    i0.ɵɵlistener(\"userClick\", function ApprovalAgentsComponent_Conditional_17_For_1_Template_ava_button_userClick_35_listener() {\n      const $index_r2 = i0.ɵɵrestoreView(_r1).$index;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.rejectApproval($index_r2));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"ava-button\", 37);\n    i0.ɵɵlistener(\"userClick\", function ApprovalAgentsComponent_Conditional_17_For_1_Template_ava_button_userClick_36_listener() {\n      const $index_r2 = i0.ɵɵrestoreView(_r1).$index;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.approveApproval($index_r2));\n    });\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(item_r4.session1.title);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r2.currentTab);\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate(item_r4.session3[0].label);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(item_r4.session3[1].label);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(item_r4 == null ? null : item_r4.session4.status);\n  }\n}\nfunction ApprovalAgentsComponent_Conditional_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵrepeaterCreate(0, ApprovalAgentsComponent_Conditional_17_For_1_Template, 37, 5, \"div\", 13, i0.ɵɵrepeaterTrackByIndex);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵrepeater(ctx_r2.consoleApproval.contents);\n  }\n}\nfunction ApprovalAgentsComponent_Conditional_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" All \", ctx_r2.currentTab, \" have been successfully approved. No pending actions. \");\n  }\n}\nexport let ApprovalAgentsComponent = /*#__PURE__*/(() => {\n  class ApprovalAgentsComponent {\n    router;\n    apiService;\n    approvalService;\n    fb;\n    drawerService;\n    agentService;\n    dialogService;\n    appLabels = approvalText.labels;\n    searchValue = '';\n    totalApprovedApprovals = 20;\n    totalPendingApprovals = 15;\n    totalApprovals = 60;\n    isBasicCollapsed = false;\n    quickActionsExpanded = true;\n    consoleApproval = {};\n    options = [];\n    basicSidebarItems = [];\n    quickActions = [];\n    toolReviews = [];\n    workflowReviews = [];\n    filteredAgentsReviews = [];\n    agentsReviews = [];\n    currentToolsPage = 1;\n    currentAgentsPage = 1;\n    currentWorkflowsPage = 1;\n    pageSize = 50;\n    totalRecords = 0;\n    isDeleted = false;\n    currentTab = 'Agents';\n    showToolApprovalPopup = false;\n    showInfoPopup = false;\n    showErrorPopup = false;\n    infoMessage = '';\n    selectedIndex = 0;\n    searchForm;\n    labels = approvalText.labels;\n    approvedAgentId = null;\n    previewData = null;\n    selectedAgentId = 0;\n    constructor(router, apiService, approvalService, fb, drawerService, agentService, dialogService) {\n      this.router = router;\n      this.apiService = apiService;\n      this.approvalService = approvalService;\n      this.fb = fb;\n      this.drawerService = drawerService;\n      this.agentService = agentService;\n      this.dialogService = dialogService;\n      this.labels = approvalText.labels;\n      this.options = [{\n        name: this.labels.electronics,\n        value: 'electronics'\n      }, {\n        name: this.labels.clothing,\n        value: 'clothing'\n      }, {\n        name: this.labels.books,\n        value: 'books'\n      }];\n      this.basicSidebarItems = [{\n        id: '1',\n        icon: 'hammer',\n        text: this.labels.agents,\n        route: '',\n        active: true\n      }, {\n        id: '2',\n        icon: 'circle-check',\n        text: this.labels.workflows,\n        route: ''\n      }, {\n        id: '3',\n        icon: 'bot',\n        text: this.labels.tools,\n        route: ''\n      }];\n      this.quickActions = [{\n        icon: 'awe_agents',\n        label: this.labels.agents,\n        route: ''\n      }, {\n        icon: 'awe_workflows',\n        label: this.labels.workflows,\n        route: ''\n      }, {\n        icon: 'awe_tools',\n        label: this.labels.tools,\n        route: ''\n      }];\n      this.searchForm = this.fb.group({\n        search: ['']\n      });\n    }\n    ngOnInit() {\n      this.searchList();\n      this.totalApprovals = 60;\n      this.loadAgentsReviews();\n    }\n    searchList() {\n      console.log(this.searchForm.get('search')?.value);\n      this.searchForm.get('search').valueChanges.pipe(startWith(''), debounceTime(300), distinctUntilChanged(), map(value => value?.toLowerCase() ?? '')).subscribe(searchText => {\n        this.applyFilter(searchText);\n      });\n    }\n    applyFilter(text) {\n      const lower = text;\n      if (!text) {\n        this.updateConsoleApproval(this.agentsReviews, 'agent');\n        return;\n      }\n      this.filteredAgentsReviews = this.agentsReviews.filter(item => item.agentName?.toLowerCase().includes(lower));\n      this.updateConsoleApproval(this.filteredAgentsReviews, 'agent');\n    }\n    onSelectionChange(data) {\n      console.log('Selection changed:', data);\n    }\n    uClick(i) {\n      console.log('log' + i);\n    }\n    toggleQuickActions() {\n      this.quickActionsExpanded = !this.quickActionsExpanded;\n    }\n    onBasicCollapseToggle(isCollapsed) {\n      this.isBasicCollapsed = isCollapsed;\n      console.log('Basic sidebar collapsed:', isCollapsed);\n    }\n    onBasicItemClick(item) {\n      this.basicSidebarItems.forEach(i => i.active = false);\n      item.active = true;\n      console.log(item);\n    }\n    toRequestStatus(value) {\n      return value === 'approved' || value === 'rejected' || value === 'review' ? value : 'review';\n    }\n    loadAgentsReviews() {\n      this.approvalService.getAllReviewAgents(this.currentAgentsPage, this.pageSize, this.isDeleted).subscribe(response => {\n        if (this.currentAgentsPage > 1) {\n          this.agentsReviews = [...this.agentsReviews, ...response.agentReviewDetails];\n        } else {\n          this.agentsReviews = response?.agentReviewDetails;\n        }\n        this.filteredAgentsReviews = this.agentsReviews;\n        this.agentsReviews = this.agentsReviews.filter(r => r.status !== 'approved');\n        // console.log('agents reviews ', this.agentsReviews);\n        this.totalRecords = this.agentsReviews.length;\n        this.updateConsoleApproval(this.agentsReviews, 'agent');\n      });\n    }\n    loadMoreAgents(page) {\n      this.currentAgentsPage = page;\n      this.loadAgentsReviews();\n    }\n    loadReviews(name) {\n      this.loadAgentsReviews();\n    }\n    rejectApproval(idx) {\n      console.log(idx);\n      this.selectedIndex = idx;\n    }\n    approveApproval(idx) {\n      console.log(idx);\n      this.selectedIndex = idx;\n      console.log(this.filteredAgentsReviews[this.selectedIndex]);\n    }\n    showApprovalDialog() {\n      this.dialogService.confirmation({\n        title: this.labels.confirmApproval,\n        message: `${this.labels.youAreAboutToApproveThis} Agent. ${this.labels.itWillBeActiveAndAvailableIn} Agents ${this.labels.catalogueForUsersToExecute}`,\n        confirmButtonText: this.labels.approve,\n        cancelButtonText: 'Cancel',\n        confirmButtonVariant: 'danger',\n        icon: 'circle-check'\n      }).then(result => {\n        if (result.confirmed) {\n          this.handleApproval();\n        }\n      });\n    }\n    showFeedbackDialog() {\n      const customButtons = [{\n        label: 'Cancel',\n        variant: 'secondary',\n        action: 'cancel'\n      }, {\n        label: 'Send Back',\n        variant: 'primary',\n        action: 'sendback'\n      }];\n      this.dialogService.feedback({\n        title: 'Confirm Send Back',\n        message: 'This Agent will be send back for corrections and modification. Kindly comment what needs to be done.',\n        buttons: customButtons,\n        variant: 'info'\n      }).then(result => {\n        if (result.confirmed && result.confirmed === true) {\n          this.handleRejection(result.data);\n        }\n      });\n    }\n    handleApproval() {\n      this.handleAgentApproval();\n    }\n    handleRejection(feedback) {\n      console.log(\"Clicked on confirmation popup\");\n      this.handleAgentRejection(feedback);\n    }\n    onCardClick(index) {\n      console.log('Selected card index:', index);\n      this.selectedIndex = index;\n      const selectedAgent = this.filteredAgentsReviews[this.selectedIndex];\n      this.selectedAgentId = selectedAgent.agentId;\n      this.loadPreviewData(selectedAgent);\n      this.drawerService.open(AgentsPreviewPanelComponent, {\n        previewData: this.previewData,\n        closePreview: () => this.drawerService.clear(),\n        editAgent: () => this.handleEditAgent(selectedAgent.agentId),\n        rejectApproval: () => this.handeMetaDataSendback(),\n        approveApproval: () => this.handleMetaDataApproval(),\n        testApproval: () => this.redirectToAgentsPlayground()\n      });\n      console.log(selectedAgent);\n    }\n    handleMetaDataApproval() {\n      this.showApprovalDialog();\n    }\n    handeMetaDataSendback() {\n      this.showFeedbackDialog();\n    }\n    handleEditAgent(agentId) {\n      console.log('Edit Agent', agentId);\n      this.drawerService.clear();\n      this.router.navigate(['/build/agents/collaborative'], {\n        queryParams: {\n          id: agentId,\n          mode: 'edit'\n        }\n      });\n    }\n    handleAgentApproval() {\n      const agentDetails = this.filteredAgentsReviews[this.selectedIndex];\n      const id = agentDetails.id;\n      const agentId = agentDetails.agentId;\n      const status = 'approved';\n      const reviewedBy = agentDetails.reviewedBy;\n      // Show loading dialog\n      this.dialogService.loading({\n        title: 'Approving Agent...',\n        message: 'Please wait while we approve the agent.',\n        showProgress: false,\n        showCancelButton: false\n      });\n      this.approvalService.approveAgent(id, agentId, status, reviewedBy).subscribe({\n        next: response => {\n          this.dialogService.close(); // Close loading dialog\n          const message = response?.message || this.labels.agentSuccessApproveMessage;\n          // Store agent ID for navigation after popup confirmation\n          this.approvedAgentId = agentId;\n          this.dialogService.success({\n            title: 'Agent Approved',\n            message: message\n          }).then(result => {\n            if (result.action === 'secondary') {\n              // Navigate to build agent screen with the approved agent ID\n              this.router.navigate(['/build/agents/collaborative'], {\n                queryParams: {\n                  id: this.approvedAgentId,\n                  mode: 'edit'\n                }\n              });\n            } else {\n              this.loadAgentsReviews(); // Refresh the list\n            }\n            // Reset the approved agent ID\n            this.approvedAgentId = null;\n          });\n        },\n        error: error => {\n          this.dialogService.close(); // Close loading dialog\n          const errorMessage = error?.error?.message || this.labels.defaultErrorMessage;\n          this.dialogService.error({\n            title: 'Approval Failed',\n            message: errorMessage,\n            showRetryButton: true,\n            retryButtonText: 'Retry'\n          }).then(result => {\n            if (result.action === 'retry') {\n              this.handleAgentApproval();\n            }\n          });\n        }\n      });\n    }\n    loadPreviewData(selectedAgent) {\n      this.previewData = {\n        type: 'agent',\n        title: selectedAgent.agentName,\n        data: selectedAgent,\n        loading: true,\n        error: null\n      };\n      console.log('Load preview data', selectedAgent.agentId);\n      this.agentService.getCollaborativeAgentDetailsById(selectedAgent.agentId).subscribe({\n        next: response => {\n          // console.log('Collaborative agent details', response);\n          this.previewData.data = response.agentDetail;\n          this.previewData.loading = false;\n          this.previewData.error = null;\n        },\n        error: error => {\n          console.error('Error:', error);\n        }\n      });\n    }\n    handleAgentRejection(feedback) {\n      const agentDetails = this.filteredAgentsReviews[this.selectedIndex];\n      const id = agentDetails.id;\n      const agentId = agentDetails.agentId;\n      const status = 'rejected';\n      const reviewedBy = agentDetails.reviewedBy;\n      const message = feedback;\n      // Show loading dialog\n      this.dialogService.loading({\n        title: 'Rejecting Agent...',\n        message: 'Please wait while we reject the agent.',\n        showProgress: false,\n        showCancelButton: false\n      });\n      this.approvalService.rejectAgent(id, agentId, status, reviewedBy, message).subscribe({\n        next: response => {\n          this.dialogService.close(); // Close loading dialog\n          const message = response?.message || this.labels.agentSuccessRejectMessage;\n          this.dialogService.success({\n            title: 'Agent Rejected',\n            message: message\n          }).then(() => {\n            this.loadAgentsReviews(); // Refresh the list\n          });\n        },\n        error: error => {\n          this.dialogService.close(); // Close loading dialog\n          const errorMessage = error?.error?.message || this.labels.defaultErrorMessage;\n          this.dialogService.error({\n            title: 'Rejection Failed',\n            message: errorMessage,\n            showRetryButton: true,\n            retryButtonText: 'Retry'\n          }).then(result => {\n            if (result.action === 'retry') {\n              this.handleAgentRejection(feedback);\n            }\n          });\n        }\n      });\n    }\n    handleTesting(index) {\n      this.selectedAgentId = this.filteredAgentsReviews[index].agentId;\n      console.log('Selected agent id', this.selectedAgentId);\n    }\n    redirectToAgentsPlayground() {\n      this.router.navigate([`/build/agents/collaborative/execute`], {\n        queryParams: {\n          id: this.selectedAgentId\n        }\n      });\n    }\n    updateConsoleApproval(data, type) {\n      this.consoleApproval = {\n        contents: data?.map(req => {\n          const statusIcons = {\n            approved: 'circle-check-big',\n            rejected: 'circle-x',\n            review: 'clock'\n          };\n          const statusTexts = {\n            approved: this.labels.approved,\n            rejected: this.labels.rejected,\n            review: this.labels.review\n          };\n          const statusKey = this.toRequestStatus(req?.status);\n          const specificId = req.agentId;\n          const title = req.agentName;\n          return {\n            id: req.id,\n            refId: specificId,\n            type: type,\n            session1: {\n              title: title,\n              labels: [{\n                name: type,\n                color: 'success',\n                background: 'red',\n                type: 'normal'\n              }, {\n                name: req.changeRequestType,\n                color: req.changeRequestType === 'update' ? 'error' : 'info',\n                background: 'red',\n                type: 'pill'\n              }]\n            },\n            session2: [{\n              name: type,\n              color: 'default',\n              background: 'red',\n              type: 'normal'\n            }, {\n              name: req.status,\n              color: 'default',\n              background: 'red',\n              type: 'normal'\n            }],\n            session3: [{\n              iconName: 'user',\n              label: req.requestedBy\n            }, {\n              iconName: 'calendar-days',\n              label: formatDate(req?.requestedAt, 'dd MMM yyyy', 'en-IN')\n            }],\n            session4: {\n              status: statusTexts[statusKey],\n              iconName: statusIcons[statusKey]\n            }\n          };\n        }),\n        footer: {}\n      };\n    }\n    static ɵfac = function ApprovalAgentsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ApprovalAgentsComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.SharedApiServiceService), i0.ɵɵdirectiveInject(i3.ApprovalService), i0.ɵɵdirectiveInject(i4.FormBuilder), i0.ɵɵdirectiveInject(i5.DrawerService), i0.ɵɵdirectiveInject(i6.AgentServiceService), i0.ɵɵdirectiveInject(i7.DialogService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ApprovalAgentsComponent,\n      selectors: [[\"app-approval-agents\"]],\n      decls: 19,\n      vars: 19,\n      consts: [[1, \"approval-right-screen\"], [1, \"approval-title-filter\"], [3, \"iconName\", \"title\", \"value\", \"subtitle\"], [1, \"filter-section\"], [1, \"search-bars\"], [2, \"font-size\", \"1.5rem\", \"font-weight\", \"bold\", \"color\", \"black\"], [1, \"approval-card-header\"], [1, \"textbox\", \"section\"], [3, \"formGroup\"], [\"formControlName\", \"search\", 3, \"placeholder\"], [\"slot\", \"icon-start\", \"iconName\", \"search\", \"iconColor\", \"var(--color-brand-primary)\", 3, \"iconSize\"], [1, \"approval-card-section\"], [1, \"no-pending-message\"], [1, \"approval-card-wrapper\"], [1, \"approval-card-wrapper\", 3, \"click\"], [\"height\", \"300\"], [\"iconSize\", \"20\", \"iconName\", \"ellipsis-vertical\"], [1, \"header\"], [\"color\", \"info\", \"size\", \"sm\", 3, \"label\"], [1, \"a-content\"], [1, \"box\", \"tag-wrapper\"], [\"label\", \"Individual\", \"size\", \"sm\"], [\"label\", \"Ascendion\", \"size\", \"sm\"], [\"label\", \"Digital Ascender\", \"size\", \"sm\"], [\"label\", \"Platform Engineering\", \"size\", \"sm\"], [1, \"box\", \"info-wrapper\"], [1, \"f\"], [\"iconSize\", \"13\", \"iconName\", \"user\"], [1, \"ml-auto\", \"s\"], [\"iconSize\", \"20\", \"iconName\", \"calendar-days\"], [1, \"footer-content\"], [1, \"footer-left\"], [1, \"ex\"], [\"iconSize\", \"20\", \"iconName\", \"circle-check-big\"], [1, \"footer-right\"], [\"label\", \"Test\", \"variant\", \"secondary\", \"size\", \"medium\", \"state\", \"default\", \"iconName\", \"play\", \"iconPosition\", \"left\", 3, \"userClick\"], [\"label\", \"Sendback\", \"variant\", \"secondary\", \"size\", \"medium\", \"state\", \"default\", \"iconName\", \"move-left\", \"iconPosition\", \"left\", 3, \"userClick\"], [\"label\", \"Approve\", \"variant\", \"primary\", \"size\", \"medium\", \"state\", \"default\", \"iconName\", \"Check\", \"iconPosition\", \"left\", 3, \"userClick\"]],\n      template: function ApprovalAgentsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"app-approval-txt-card\", 2)(3, \"app-approval-txt-card\", 2)(4, \"app-approval-txt-card\", 2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 3)(6, \"div\", 4)(7, \"div\", 5);\n          i0.ɵɵtext(8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"div\", 6);\n          i0.ɵɵtext(10);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"div\", 7)(12, \"div\")(13, \"form\", 8)(14, \"ava-textbox\", 9);\n          i0.ɵɵelement(15, \"ava-icon\", 10);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(16, \"div\", 11);\n          i0.ɵɵtemplate(17, ApprovalAgentsComponent_Conditional_17_Template, 2, 0)(18, ApprovalAgentsComponent_Conditional_18_Template, 2, 1, \"div\", 12);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"iconName\", \"hourglass\")(\"title\", ctx.labels.totalApprovals)(\"value\", ctx.totalApprovals)(\"subtitle\", ctx.currentTab + \" \" + ctx.labels.whichAreRequestedForApproval);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"iconName\", \"shield-alert\")(\"title\", ctx.labels.totalApprovedApprovals)(\"value\", ctx.totalApprovedApprovals)(\"subtitle\", ctx.currentTab + \" \" + ctx.labels.whichAreApproved);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"iconName\", \"hourglass\")(\"title\", ctx.labels.totalPendingApprovals)(\"value\", ctx.totalPendingApprovals)(\"subtitle\", ctx.labels.all + \" \" + ctx.currentTab + \" \" + ctx.labels.awaitingApproval);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", ctx.currentTab, \" Approvals \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate2(\" All - \", ctx.totalRecords, \" \", ctx.currentTab, \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"formGroup\", ctx.searchForm);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"placeholder\", ctx.labels.searchPlaceholder);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"iconSize\", 16);\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(ctx.totalRecords > 0 ? 17 : 18);\n        }\n      },\n      dependencies: [CommonModule, RouterModule, ApprovalCardComponent, IconComponent, AvaTextboxComponent, ReactiveFormsModule, i4.ɵNgNoValidate, i4.NgControlStatus, i4.NgControlStatusGroup, i4.FormGroupDirective, i4.FormControlName, AvaTagComponent, ButtonComponent, ApprovalTxtCardComponent],\n      styles: [\".approval[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: row;\\n  width: 100%;\\n  height: 100%;\\n}\\n\\n.approval-left-screen[_ngcontent-%COMP%] {\\n  flex: 0 0 70px; \\n\\n  width: 70px;\\n  transition: all var(--transition-speed) ease;\\n  height: 120vh; \\n\\n  overflow: hidden;\\n}\\n.approval-left-screen.quick-actions-expanded[_ngcontent-%COMP%] {\\n  flex: 0 0 250px;\\n  margin-right: 15px;\\n}\\n\\n\\n\\n.approval-right-screen[_ngcontent-%COMP%] {\\n  flex: 1; \\n\\n  padding: 1rem; \\n\\n  overflow-y: auto; \\n\\n}\\n\\n\\n\\n.approval-title-filter[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  margin-bottom: 1rem; \\n\\n}\\n\\n\\n\\n.approvals-title[_ngcontent-%COMP%] {\\n  font-weight: bold;\\n  font-size: 1.2rem;\\n  margin-bottom: 0.5rem;\\n}\\n\\n\\n\\n.filter-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 0 1rem; \\n\\n  margin-bottom: 1rem; \\n\\n}\\n\\n\\n\\n.search-bars[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 1rem; \\n\\n}\\n\\n\\n\\n.textbox.section[_ngcontent-%COMP%] {\\n  margin-left: 1rem;\\n}\\n\\n.textbox.section[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center; \\n\\n  gap: 8px; \\n\\n}\\n\\n.approval-card-header[_ngcontent-%COMP%] {\\n  font-size: 1.25rem; \\n\\n  font-weight: 600; \\n\\n  color: grey; \\n\\n  margin-bottom: 1.5rem; \\n\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between; \\n\\n}\\n\\n.approval-title-filter[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: row;\\n  justify-content: space-between;\\n  align-items: center;\\n  gap: 1rem;\\n  flex-wrap: nowrap;\\n  width: 100%;\\n  padding: 1rem 0;\\n}\\n\\n.approval-title-filter[_ngcontent-%COMP%]    > ava-text-card[_ngcontent-%COMP%] {\\n  flex: 1 1 22%;\\n  min-width: 200px;\\n}\\n\\n.quick-actions-wrapper[_ngcontent-%COMP%] {\\n  grid-area: quick-actions;\\n  background-color: var(--dashboard-card-bg);\\n  border-radius: var(--border-radius-standard);\\n  display: flex;\\n  flex-direction: column;\\n  width: 55px;\\n  height: 250vh;\\n  transition: all 0.35s cubic-bezier(0.4, 0, 0.2, 1);\\n  overflow: hidden;\\n  box-shadow: var(--shadow-medium);\\n  border: var(--border-thin);\\n  position: relative;\\n  \\n\\n  \\n\\n  \\n\\n  \\n\\n}\\n.quick-actions-wrapper[_ngcontent-%COMP%]:hover {\\n  box-shadow: var(--shadow-hover);\\n}\\n.quick-actions-wrapper.expanded[_ngcontent-%COMP%] {\\n  width: 100%; \\n\\n}\\n@media (min-width: 1900px) and (max-width: 1930px) and (max-height: 1100px) {\\n  .quick-actions-wrapper[_ngcontent-%COMP%] {\\n    height: 595px;\\n  }\\n}\\n@media (min-width: 1200px) and (max-width: 1400px) {\\n  .quick-actions-wrapper[_ngcontent-%COMP%] {\\n    height: 580px !important;\\n    max-height: 580px !important;\\n  }\\n}\\n@media (max-width: 1200px) {\\n  .quick-actions-wrapper[_ngcontent-%COMP%] {\\n    height: 320px;\\n  }\\n}\\n@media (max-width: 992px) {\\n  .quick-actions-wrapper[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    width: 100%;\\n    height: 48px;\\n  }\\n  .quick-actions-wrapper.expanded[_ngcontent-%COMP%] {\\n    height: auto;\\n    max-height: 320px;\\n    width: 100%;\\n  }\\n}\\n@media (max-width: 576px) {\\n  .quick-actions-wrapper[_ngcontent-%COMP%] {\\n    height: 280px;\\n  }\\n}\\n@media (min-width: 1200px) and (max-width: 1366px) and (max-height: 800px) {\\n  .quick-actions-wrapper[_ngcontent-%COMP%] {\\n    height: 100%;\\n  }\\n  .quick-actions-wrapper[_ngcontent-%COMP%]     .card-container {\\n    height: 100%;\\n  }\\n}\\n.quick-actions-wrapper[_ngcontent-%COMP%]     .quick-actions-card {\\n  height: 100% !important;\\n  width: 100% !important;\\n  \\n\\n  \\n\\n}\\n.quick-actions-wrapper[_ngcontent-%COMP%]     .quick-actions-card .card-container {\\n  height: 100% !important;\\n  width: 100% !important;\\n  padding: 0 !important;\\n  overflow: hidden !important;\\n  display: flex !important;\\n  flex-direction: column !important;\\n}\\n.quick-actions-wrapper[_ngcontent-%COMP%]     .quick-actions-card .card-container .card-body {\\n  padding: 0 !important;\\n  height: 100% !important;\\n  display: flex !important;\\n  flex-direction: column !important;\\n}\\n.quick-actions-wrapper[_ngcontent-%COMP%]     .quick-actions-card .card-content {\\n  height: 100% !important;\\n  display: flex !important;\\n  flex-direction: column !important;\\n  padding: 0 !important;\\n}\\n@media (max-width: 992px) {\\n  .quick-actions-wrapper[_ngcontent-%COMP%]     .quick-actions-card .card-content {\\n    flex-direction: row !important;\\n  }\\n}\\n@media (max-width: 992px) {\\n  .quick-actions-wrapper[_ngcontent-%COMP%]     .quick-actions-card .card-container {\\n    height: 48px !important;\\n    width: 100% !important;\\n    flex-direction: row !important;\\n  }\\n  .quick-actions-wrapper[_ngcontent-%COMP%]     .quick-actions-card .card-container.expanded {\\n    height: auto !important;\\n  }\\n}\\n@media (min-width: 1200px) and (max-width: 1366px) and (max-height: 800px) {\\n  .quick-actions-wrapper[_ngcontent-%COMP%]     .quick-actions-card .card-container {\\n    width: 100% !important;\\n  }\\n}\\n\\n.quick-actions-content[_ngcontent-%COMP%] {\\n  padding: 20px 16px;\\n  overflow-y: auto;\\n  flex-grow: 1;\\n}\\n.quick-actions-content[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px; \\n\\n}\\n.quick-actions-content[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: flex-start;\\n  gap: 16px; \\n\\n  padding: 16px 20px; \\n\\n  border-radius: 12px; \\n\\n  border: none;\\n  border: 2px solid transparent;\\n  background: linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%);\\n  background-origin: border-box;\\n  background-clip: padding-box, border-box;\\n  --button-effect-color: 33, 90, 214;\\n  cursor: pointer;\\n  transition: all var(--transition-speed) ease;\\n  width: 100%;\\n  text-align: left;\\n  color: #fff;\\n}\\n.quick-actions-content[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%]   .action-icon[_ngcontent-%COMP%], \\n.quick-actions-content[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%]   .action-label[_ngcontent-%COMP%] {\\n  color: #fff;\\n}\\n.quick-actions-content[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%]:hover {\\n  opacity: 0.9;\\n  background: linear-gradient(103.35deg, #03BDD4 31.33%, #215AD6 100%);\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px var(--dashboard-shadow-hover);\\n}\\n.quick-actions-content[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%]   .action-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 24px;\\n  height: 24px;\\n}\\n.quick-actions-content[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%]   .action-icon[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  filter: brightness(0) invert(1); \\n\\n}\\n.quick-actions-content[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%]   .action-label[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 500;\\n  color: linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%); \\n\\n}\\n\\n.action-button.active-action[_ngcontent-%COMP%] {\\n  background: linear-gradient(103.35deg, #03BDD4 31.33%, #215AD6 100%) !important;\\n}\\n\\n.quick-actions-toggle[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n  padding: 20px 16px;\\n  padding-bottom: 0px;\\n  cursor: pointer;\\n  transition: background-color 0.35s cubic-bezier(0.4, 0, 0.2, 1);\\n  flex-shrink: 0;\\n  \\n\\n  \\n\\n}\\n.quick-actions-wrapper[_ngcontent-%COMP%]:not(.expanded)   .quick-actions-toggle[_ngcontent-%COMP%] {\\n  padding: 20px 0;\\n  justify-content: center;\\n}\\n.quick-actions-toggle[_ngcontent-%COMP%]   .toggle-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 36px;\\n  height: 36px;\\n  border-radius: 8px;\\n  background-color: transparent;\\n  position: relative;\\n  \\n\\n}\\n.quick-actions-toggle[_ngcontent-%COMP%]   .toggle-button[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  inset: 0;\\n  border-radius: 8px;\\n  padding: 1px;\\n  background: var(--dashboard-gradient);\\n  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);\\n  mask-composite: exclude;\\n  transition: opacity 0.35s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.quick-actions-toggle[_ngcontent-%COMP%]   .toggle-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  transition: transform 0.35s cubic-bezier(0.4, 0, 0.2, 1);\\n  width: 16px;\\n  height: 16px;\\n  stroke: var(--dashboard-toggle-stroke);\\n  z-index: 1;\\n}\\n.quick-actions-toggle[_ngcontent-%COMP%]   .toggle-button[_ngcontent-%COMP%]   svg.rotate[_ngcontent-%COMP%] {\\n  transform: rotate(180deg);\\n}\\n.quick-actions-toggle[_ngcontent-%COMP%]   .quick-actions-wrapper[_ngcontent-%COMP%]:not(.expanded)   .toggle-button[_ngcontent-%COMP%] {\\n  background: var(--dashboard-gradient);\\n  transition: background 0.35s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.quick-actions-toggle[_ngcontent-%COMP%]   .quick-actions-wrapper[_ngcontent-%COMP%]:not(.expanded)   .toggle-button[_ngcontent-%COMP%]::before {\\n  display: none;\\n}\\n.quick-actions-toggle[_ngcontent-%COMP%]   .quick-actions-wrapper[_ngcontent-%COMP%]:not(.expanded)   .toggle-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  stroke: var(--dashboard-toggle-stroke-collapsed);\\n  transition: stroke 0.35s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.quick-actions-toggle[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-weight: 580;\\n  font-size: 16px;\\n  color: var(--dashboard-text-primary);\\n  opacity: 1;\\n  transition: opacity 0.35s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n\\n.quick-actions-icons[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 16px; \\n\\n  padding: 20px 0; \\n\\n  height: 150vh;\\n}\\n@media (max-width: 992px) {\\n  .quick-actions-icons[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    justify-content: center;\\n    flex-wrap: wrap;\\n    padding: 8px;\\n  }\\n}\\n.quick-actions-icons[_ngcontent-%COMP%]   .icon-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 36px; \\n\\n  height: 36px; \\n\\n  border-radius: 8px; \\n\\n  border: none;\\n  background: linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%);\\n  cursor: pointer;\\n  transition: all var(--transition-speed) ease;\\n}\\n.quick-actions-icons[_ngcontent-%COMP%]   .icon-button[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(103.35deg, #03BDD4 31.33%, #215AD6 100%);\\n  opacity: 0.9;\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px var(--dashboard-shadow-hover);\\n}\\n.quick-actions-icons[_ngcontent-%COMP%]   .icon-button[_ngcontent-%COMP%]   .icon-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.quick-actions-icons[_ngcontent-%COMP%]   .icon-button[_ngcontent-%COMP%]   .icon-wrapper[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  filter: brightness(0) invert(1); \\n\\n}\\n\\n.icon-button.active-action[_ngcontent-%COMP%] {\\n  background: linear-gradient(103.35deg, #03BDD4 31.33%, #215AD6 100%) !important;\\n}\\n\\n.approval-card-section[_ngcontent-%COMP%] {\\n  height: 780px;\\n  overflow-y: auto;\\n}\\n\\n.approval-card-wrapper[_ngcontent-%COMP%] {\\n  margin: 10px;\\n}\\n\\n.no-pending-message[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin-top: 20%;\\n  font-size: 1.2rem;\\n  color: #000000;\\n  font-weight: 500;\\n  text-align: center;\\n  border-radius: 16px;\\n  min-height: 100px;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .filter-section[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: stretch;\\n  }\\n  .search-bars[_ngcontent-%COMP%], \\n   .textbox.section[_ngcontent-%COMP%] {\\n    width: 100%;\\n    margin: 0 0 0.5rem 0;\\n    justify-content: center;\\n  }\\n  .search-bars[_ngcontent-%COMP%] {\\n    flex-wrap: wrap;\\n    gap: 0.5rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return ApprovalAgentsComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "formatDate", "ReactiveFormsModule", "RouterModule", "ApprovalCardComponent", "IconComponent", "AvaTextboxComponent", "AvaTagComponent", "ButtonComponent", "approvalText", "debounceTime", "distinctUntilChanged", "map", "startWith", "AgentsPreviewPanelComponent", "ApprovalTxtCardComponent", "i0", "ɵɵelementStart", "ɵɵlistener", "ApprovalAgentsComponent_Conditional_17_For_1_Template_div_click_0_listener", "$index_r2", "ɵɵrestoreView", "_r1", "$index", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "onCardClick", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ApprovalAgentsComponent_Conditional_17_For_1_Template_ava_button_userClick_34_listener", "handleTesting", "ApprovalAgentsComponent_Conditional_17_For_1_Template_ava_button_userClick_35_listener", "rejectApproval", "ApprovalAgentsComponent_Conditional_17_For_1_Template_ava_button_userClick_36_listener", "approveApproval", "ɵɵadvance", "ɵɵtextInterpolate", "item_r4", "session1", "title", "ɵɵpropertyInterpolate", "currentTab", "session3", "label", "session4", "status", "ɵɵrepeaterCreate", "ApprovalAgentsComponent_Conditional_17_For_1_Template", "ɵɵrepeaterTrackByIndex", "ɵɵrepeater", "consoleApproval", "contents", "ɵɵtextInterpolate1", "ApprovalAgentsComponent", "router", "apiService", "approvalService", "fb", "drawerService", "agentService", "dialogService", "appLabels", "labels", "searchValue", "totalApprovedApprovals", "totalPendingApprovals", "totalApprovals", "isBasicCollapsed", "quickActionsExpanded", "options", "basicSidebarItems", "quickActions", "toolReviews", "workflowReviews", "filteredAgentsReviews", "agentsReviews", "currentToolsPage", "currentAgentsPage", "currentWorkflowsPage", "pageSize", "totalRecords", "isDeleted", "showToolApprovalPopup", "showInfoPopup", "showErrorPopup", "infoMessage", "selectedIndex", "searchForm", "approvedAgentId", "previewData", "selectedAgentId", "constructor", "name", "electronics", "value", "clothing", "books", "id", "icon", "text", "agents", "route", "active", "workflows", "tools", "group", "search", "ngOnInit", "searchList", "loadAgentsReviews", "console", "log", "get", "valueChanges", "pipe", "toLowerCase", "subscribe", "searchText", "applyFilter", "lower", "updateConsoleApproval", "filter", "item", "<PERSON><PERSON><PERSON>", "includes", "onSelectionChange", "data", "uClick", "i", "toggleQuickActions", "onBasicCollapseToggle", "isCollapsed", "onBasicItemClick", "for<PERSON>ach", "toRequestStatus", "getAllReviewAgents", "response", "agentReviewDetails", "r", "length", "loadMoreAgents", "page", "loadReviews", "idx", "showApprovalDialog", "confirmation", "confirmApproval", "message", "youAreAboutToApproveThis", "itWillBeActiveAndAvailableIn", "catalogueForUsersToExecute", "confirmButtonText", "approve", "cancelButtonText", "confirmButtonVariant", "then", "result", "confirmed", "handleApproval", "showFeedbackDialog", "customButtons", "variant", "action", "feedback", "buttons", "handleRejection", "handleAgentApproval", "handleAgentRejection", "index", "selectedAgent", "agentId", "loadPreviewData", "open", "closePreview", "clear", "editAgent", "handleEditAgent", "handeMetaDataSendback", "handleMetaDataApproval", "testApproval", "redirectToAgentsPlayground", "navigate", "queryParams", "mode", "agentDetails", "reviewedBy", "loading", "showProgress", "showCancelButton", "approveAgent", "next", "close", "agentSuccessApproveMessage", "success", "error", "errorMessage", "defaultErrorMessage", "showRetryButton", "retryButtonText", "type", "getCollaborativeAgentDetailsById", "agentDetail", "rejectAgent", "agentSuccessRejectMessage", "req", "statusIcons", "approved", "rejected", "review", "statusTexts", "statusKey", "specificId", "refId", "color", "background", "changeRequestType", "session2", "iconName", "requestedBy", "requestedAt", "footer", "ɵɵdirectiveInject", "i1", "Router", "i2", "SharedApiServiceService", "i3", "ApprovalService", "i4", "FormBuilder", "i5", "DrawerService", "i6", "AgentServiceService", "i7", "DialogService", "selectors", "decls", "vars", "consts", "template", "ApprovalAgentsComponent_Template", "rf", "ctx", "ɵɵtemplate", "ApprovalAgentsComponent_Conditional_17_Template", "ApprovalAgentsComponent_Conditional_18_Template", "ɵɵproperty", "whichAreRequestedForApproval", "whichAreApproved", "all", "awaitingApproval", "ɵɵtextInterpolate2", "searchPlaceholder", "ɵɵconditional", "ɵNgNoValidate", "NgControlStatus", "NgControlStatusGroup", "FormGroupDirective", "FormControlName", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\console\\src\\app\\pages\\approval\\approval-agents\\approval-agents.component.ts", "C:\\console\\aava-ui-web\\projects\\console\\src\\app\\pages\\approval\\approval-agents\\approval-agents.component.html"], "sourcesContent": ["import { CommonModule, formatDate } from '@angular/common';\r\nimport { Component, CUSTOM_ELEMENTS_SCHEMA, OnInit } from '@angular/core';\r\nimport { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';\r\nimport { Router, RouterModule } from '@angular/router';\r\nimport {\r\n  ApprovalCardComponent,\r\n  IconComponent,\r\n  AvaTextboxComponent,\r\n  TextCardComponent,\r\n  PopupComponent,\r\n  ConfirmationPopupComponent,\r\n  AvaTagComponent,\r\n  DropdownOption,\r\n  ButtonComponent,\r\n  DialogService,\r\n  DialogButton,\r\n} from '@ava/play-comp-library';\r\nimport approvalText from '../constants/approval.json';\r\nimport { SharedApiServiceService } from '../../../shared/services/shared-api-service.service';\r\nimport { ApprovalService } from '../../../shared/services/approval.service';\r\nimport { debounceTime, distinctUntilChanged, map, startWith } from 'rxjs';\r\nimport { DrawerService } from '../../../shared/services/drawer/drawer.service';\r\nimport { AgentsPreviewPanelComponent } from './agents-preview-panel/agents-preview-panel.component';\r\nimport { AgentServiceService } from '@shared/index';\r\nimport { ApprovalTxtCardComponent } from '../approval-text-card/approval-text-card.component';\r\n\r\ntype RequestStatus = 'approved' | 'rejected' | 'review';\r\n\r\n@Component({\r\n  selector: 'app-approval-agents',\r\n  imports: [\r\n    CommonModule,\r\n    RouterModule,\r\n    ApprovalCardComponent,\r\n    IconComponent,\r\n    AvaTextboxComponent,\r\n    ReactiveFormsModule,\r\n    AvaTagComponent,\r\n    ButtonComponent,\r\n    PopupComponent,\r\n    ConfirmationPopupComponent,\r\n    ApprovalTxtCardComponent\r\n  ],\r\n  schemas: [CUSTOM_ELEMENTS_SCHEMA],\r\n  templateUrl: './approval-agents.component.html',\r\n  styleUrls: ['./approval-agents.component.scss'],\r\n})\r\nexport class ApprovalAgentsComponent implements OnInit {\r\n  appLabels = approvalText.labels;\r\n\r\n  public searchValue: string = '';\r\n  public totalApprovedApprovals: number = 20;\r\n  public totalPendingApprovals: number = 15;\r\n  public totalApprovals: number = 60;\r\n  public isBasicCollapsed: boolean = false;\r\n  public quickActionsExpanded: boolean = true;\r\n  public consoleApproval: any = {};\r\n  public options: DropdownOption[] = [];\r\n  public basicSidebarItems: any[] = [];\r\n  public quickActions: any[] = [];\r\n  public toolReviews: any[] = [];\r\n  public workflowReviews: any[] = [];\r\n  public filteredAgentsReviews: any[] = [];\r\n  public agentsReviews: any[] = [];\r\n  public currentToolsPage = 1;\r\n  public currentAgentsPage = 1;\r\n  public currentWorkflowsPage = 1;\r\n  public pageSize = 50;\r\n  public totalRecords = 0;\r\n  public isDeleted = false;\r\n  public currentTab = 'Agents';\r\n  public showToolApprovalPopup = false;\r\n  public showInfoPopup = false;\r\n  public showErrorPopup = false;\r\n  public infoMessage = '';\r\n  public selectedIndex = 0;\r\n  public searchForm!: FormGroup;\r\n  public labels: any = approvalText.labels;\r\n  public approvedAgentId: number | null = null;\r\n  public previewData: any = null;\r\n  public selectedAgentId : number = 0;\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private apiService: SharedApiServiceService,\r\n    private approvalService: ApprovalService,\r\n    private fb: FormBuilder,\r\n    private drawerService: DrawerService,\r\n    private agentService: AgentServiceService,\r\n    private dialogService: DialogService\r\n  ) {\r\n    this.labels = approvalText.labels;\r\n    this.options = [\r\n      { name: this.labels.electronics, value: 'electronics' },\r\n      { name: this.labels.clothing, value: 'clothing' },\r\n      { name: this.labels.books, value: 'books' },\r\n    ];\r\n    this.basicSidebarItems = [\r\n      {\r\n        id: '1',\r\n        icon: 'hammer',\r\n        text: this.labels.agents,\r\n        route: '',\r\n        active: true,\r\n      },\r\n      { id: '2', icon: 'circle-check', text: this.labels.workflows, route: '' },\r\n      { id: '3', icon: 'bot', text: this.labels.tools, route: '' },\r\n    ];\r\n    this.quickActions = [\r\n      {\r\n        icon: 'awe_agents',\r\n        label: this.labels.agents,\r\n        route: '',\r\n      },\r\n      {\r\n        icon: 'awe_workflows',\r\n        label: this.labels.workflows,\r\n        route: '',\r\n      },\r\n      {\r\n        icon: 'awe_tools',\r\n        label: this.labels.tools,\r\n        route: '',\r\n      },\r\n    ];\r\n    this.searchForm = this.fb.group({\r\n      search: [''],\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.searchList();\r\n    this.totalApprovals = 60;\r\n    this.loadAgentsReviews();\r\n  }\r\n\r\n  public searchList() {\r\n    console.log(this.searchForm.get('search')?.value);\r\n    this.searchForm\r\n      .get('search')!\r\n      .valueChanges.pipe(\r\n        startWith(''),\r\n        debounceTime(300),\r\n        distinctUntilChanged(),\r\n        map((value) => value?.toLowerCase() ?? ''),\r\n      )\r\n      .subscribe((searchText) => {\r\n        this.applyFilter(searchText);\r\n      });\r\n  }\r\n\r\n  public applyFilter(text: string) {\r\n    const lower = text;\r\n\r\n    if (!text) {\r\n      this.updateConsoleApproval(this.agentsReviews, 'agent');\r\n      return;\r\n    }\r\n\r\n    this.filteredAgentsReviews = this.agentsReviews.filter((item) =>\r\n      item.agentName?.toLowerCase().includes(lower),\r\n    );\r\n    this.updateConsoleApproval(this.filteredAgentsReviews, 'agent');\r\n  }\r\n\r\n  public onSelectionChange(data: any) {\r\n    console.log('Selection changed:', data);\r\n  }\r\n\r\n  public uClick(i: any) {\r\n    console.log('log' + i);\r\n  }\r\n\r\n  public toggleQuickActions(): void {\r\n    this.quickActionsExpanded = !this.quickActionsExpanded;\r\n  }\r\n\r\n  public onBasicCollapseToggle(isCollapsed: boolean): void {\r\n    this.isBasicCollapsed = isCollapsed;\r\n    console.log('Basic sidebar collapsed:', isCollapsed);\r\n  }\r\n\r\n  public onBasicItemClick(item: any): void {\r\n    this.basicSidebarItems.forEach((i) => (i.active = false));\r\n    item.active = true;\r\n    console.log(item);\r\n  }\r\n\r\n  public toRequestStatus(value: string | null | undefined): RequestStatus {\r\n    return value === 'approved' || value === 'rejected' || value === 'review'\r\n      ? value\r\n      : 'review';\r\n  }\r\n\r\n  public loadAgentsReviews() {\r\n    this.approvalService\r\n      .getAllReviewAgents(this.currentAgentsPage, this.pageSize, this.isDeleted)\r\n      .subscribe((response) => {\r\n        if (this.currentAgentsPage > 1) {\r\n          this.agentsReviews = [\r\n            ...this.agentsReviews,\r\n            ...response.agentReviewDetails,\r\n          ];\r\n        } else {\r\n          this.agentsReviews = response?.agentReviewDetails;\r\n        }\r\n        this.filteredAgentsReviews = this.agentsReviews;\r\n        this.agentsReviews = this.agentsReviews.filter(\r\n          (r) => r.status !== 'approved',\r\n        );\r\n        // console.log('agents reviews ', this.agentsReviews);\r\n        this.totalRecords = this.agentsReviews.length;\r\n        this.updateConsoleApproval(this.agentsReviews, 'agent');\r\n      });\r\n  }\r\n\r\n  public loadMoreAgents(page: number) {\r\n    this.currentAgentsPage = page;\r\n    this.loadAgentsReviews();\r\n  }\r\n\r\n  public loadReviews(name: string) {\r\n    this.loadAgentsReviews();\r\n  }\r\n\r\n  public rejectApproval(idx: any) {\r\n    console.log(idx);\r\n    this.selectedIndex = idx;\r\n  }\r\n\r\n  public approveApproval(idx: any){\r\n    console.log(idx);\r\n    this.selectedIndex = idx;\r\n    console.log(this.filteredAgentsReviews[this.selectedIndex]);\r\n  }\r\n\r\n  private showApprovalDialog(): void {\r\n    this.dialogService.confirmation({\r\n      title: this.labels.confirmApproval,\r\n      message: `${this.labels.youAreAboutToApproveThis} Agent. ${this.labels.itWillBeActiveAndAvailableIn} Agents ${this.labels.catalogueForUsersToExecute}`,\r\n      confirmButtonText: this.labels.approve,\r\n      cancelButtonText: 'Cancel',\r\n      confirmButtonVariant: 'danger',\r\n      icon:'circle-check'\r\n    }).then(result => {\r\n      if (result.confirmed) {\r\n        this.handleApproval();\r\n      }\r\n    });\r\n  }\r\n\r\n  private showFeedbackDialog(): void {\r\n    const customButtons: DialogButton[] = [\r\n          { label: 'Cancel', variant: 'secondary', action: 'cancel' },\r\n          { label: 'Send Back', variant: 'primary', action: 'sendback' }\r\n        ];\r\n    this.dialogService.feedback({\r\n      title: 'Confirm Send Back',\r\n      message: 'This Agent will be send back for corrections and modification. Kindly comment what needs to be done.',\r\n      buttons:customButtons,\r\n      variant: 'info'\r\n    }).then(result => {\r\n      if (result.confirmed && result.confirmed === true) {\r\n        this.handleRejection(result.data);\r\n      }\r\n    });\r\n  }\r\n\r\n  public handleApproval() {\r\n    this.handleAgentApproval();\r\n  }\r\n\r\n  public handleRejection(feedback: any) {\r\n    console.log(\"Clicked on confirmation popup\");\r\n    this.handleAgentRejection(feedback);\r\n  }\r\n\r\n  public onCardClick(index: number): void {\r\n    console.log('Selected card index:', index);\r\n    this.selectedIndex = index;\r\n    const selectedAgent = this.filteredAgentsReviews[this.selectedIndex];\r\n    this.selectedAgentId = selectedAgent.agentId;\r\n    this.loadPreviewData(selectedAgent);\r\n\r\n    this.drawerService.open(AgentsPreviewPanelComponent, {\r\n      previewData: this.previewData,\r\n      closePreview: () => this.drawerService.clear(),\r\n      editAgent: () => this.handleEditAgent(selectedAgent.agentId),\r\n      rejectApproval: () => this.handeMetaDataSendback(),\r\n      approveApproval: () => this.handleMetaDataApproval(),\r\n      testApproval: () => this.redirectToAgentsPlayground(),\r\n    });\r\n    console.log(selectedAgent);\r\n  }\r\n\r\n  public handleMetaDataApproval(){\r\n    this.showApprovalDialog();\r\n  }\r\n\r\n  public handeMetaDataSendback(){\r\n    this.showFeedbackDialog();\r\n  }\r\n\r\n  public handleEditAgent(agentId: string) {\r\n    console.log('Edit Agent', agentId);\r\n    this.drawerService.clear();\r\n    this.router.navigate(\r\n      ['/build/agents/collaborative'],\r\n      { queryParams: { id: agentId, mode: 'edit' } }\r\n    );\r\n  }\r\n\r\n  public handleAgentApproval() {\r\n    const agentDetails = this.filteredAgentsReviews[this.selectedIndex];\r\n    const id = agentDetails.id;\r\n    const agentId = agentDetails.agentId;\r\n    const status = 'approved';\r\n    const reviewedBy = agentDetails.reviewedBy;\r\n\r\n    // Show loading dialog\r\n    this.dialogService.loading({\r\n      title: 'Approving Agent...',\r\n      message: 'Please wait while we approve the agent.',\r\n      showProgress: false,\r\n      showCancelButton: false\r\n    });\r\n\r\n    this.approvalService\r\n      .approveAgent(id, agentId, status, reviewedBy)\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.dialogService.close(); // Close loading dialog\r\n          \r\n          const message = response?.message || this.labels.agentSuccessApproveMessage;\r\n          \r\n          // Store agent ID for navigation after popup confirmation\r\n          this.approvedAgentId = agentId;\r\n          \r\n          this.dialogService.success({\r\n            title: 'Agent Approved',\r\n            message: message,\r\n          }).then(result => {\r\n            if (result.action === 'secondary') {\r\n              // Navigate to build agent screen with the approved agent ID\r\n              this.router.navigate(['/build/agents/collaborative'], {\r\n                queryParams: {\r\n                  id: this.approvedAgentId,\r\n                  mode: 'edit',\r\n                },\r\n              });\r\n            } else {\r\n              this.loadAgentsReviews(); // Refresh the list\r\n            }\r\n            // Reset the approved agent ID\r\n            this.approvedAgentId = null;\r\n          });\r\n        },\r\n        error: (error) => {\r\n          this.dialogService.close(); // Close loading dialog\r\n          \r\n          const errorMessage = error?.error?.message || this.labels.defaultErrorMessage;\r\n          this.dialogService.error({\r\n            title: 'Approval Failed',\r\n            message: errorMessage,\r\n            showRetryButton: true,\r\n            retryButtonText: 'Retry'\r\n          }).then(result => {\r\n            if (result.action === 'retry') {\r\n              this.handleAgentApproval();\r\n            }\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  public loadPreviewData(selectedAgent : any){\r\n    this.previewData = {\r\n      type: 'agent',\r\n      title: selectedAgent.agentName,\r\n      data: selectedAgent,\r\n      loading: true,\r\n      error: null,\r\n    };\r\n\r\n    console.log('Load preview data', selectedAgent.agentId);\r\n    this.agentService.getCollaborativeAgentDetailsById(selectedAgent.agentId).subscribe({\r\n      next: (response) => {\r\n        // console.log('Collaborative agent details', response);\r\n        this.previewData.data = response.agentDetail;\r\n        this.previewData.loading = false;\r\n        this.previewData.error = null;\r\n      },\r\n      error: (error) => {\r\n        console.error('Error:', error);\r\n      },\r\n    });\r\n  }\r\n\r\n  public handleAgentRejection(feedback: any) {\r\n    const agentDetails = this.filteredAgentsReviews[this.selectedIndex];\r\n    const id = agentDetails.id;\r\n    const agentId = agentDetails.agentId;\r\n    const status = 'rejected';\r\n    const reviewedBy = agentDetails.reviewedBy;\r\n    const message = feedback;\r\n\r\n    // Show loading dialog\r\n    this.dialogService.loading({\r\n      title: 'Rejecting Agent...',\r\n      message: 'Please wait while we reject the agent.',\r\n      showProgress: false,\r\n      showCancelButton: false\r\n    });\r\n\r\n    this.approvalService\r\n      .rejectAgent(id, agentId, status, reviewedBy, message)\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.dialogService.close(); // Close loading dialog\r\n          \r\n          const message = response?.message || this.labels.agentSuccessRejectMessage;\r\n          this.dialogService.success({\r\n            title: 'Agent Rejected',\r\n            message: message\r\n          }).then(() => {\r\n            this.loadAgentsReviews(); // Refresh the list\r\n          });\r\n        },\r\n        error: (error) => {\r\n          this.dialogService.close(); // Close loading dialog\r\n          \r\n          const errorMessage = error?.error?.message || this.labels.defaultErrorMessage;\r\n          this.dialogService.error({\r\n            title: 'Rejection Failed',\r\n            message: errorMessage,\r\n            showRetryButton: true,\r\n            retryButtonText: 'Retry'\r\n          }).then(result => {\r\n            if (result.action === 'retry') {\r\n              this.handleAgentRejection(feedback);\r\n            }\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  public handleTesting(index: any) {\r\n    this.selectedAgentId = this.filteredAgentsReviews[index].agentId; \r\n    console.log('Selected agent id', this.selectedAgentId);\r\n  }\r\n\r\n  public redirectToAgentsPlayground(): void {\r\n    this.router.navigate([`/build/agents/collaborative/execute`], {\r\n      queryParams: { id: this.selectedAgentId },\r\n    });\r\n  }\r\n\r\n  public updateConsoleApproval(data: any[], type: string) {\r\n    this.consoleApproval = {\r\n      contents: data?.map((req: any) => {\r\n        const statusIcons: Record<RequestStatus, string> = {\r\n          approved: 'circle-check-big',\r\n          rejected: 'circle-x',\r\n          review: 'clock',\r\n        };\r\n        const statusTexts: Record<RequestStatus, string> = {\r\n          approved: this.labels.approved,\r\n          rejected: this.labels.rejected,\r\n          review: this.labels.review,\r\n        };\r\n        const statusKey = this.toRequestStatus(req?.status);\r\n        const specificId = req.agentId;\r\n        const title = req.agentName;\r\n\r\n        return {\r\n          id: req.id,\r\n          refId: specificId,\r\n          type: type,\r\n          session1: {\r\n            title: title,\r\n            labels: [\r\n              {\r\n                name: type,\r\n                color: 'success',\r\n                background: 'red',\r\n                type: 'normal',\r\n              },\r\n              {\r\n                name: req.changeRequestType,\r\n                color: req.changeRequestType === 'update' ? 'error' : 'info',\r\n                background: 'red',\r\n                type: 'pill',\r\n              },\r\n            ],\r\n          },\r\n          session2: [\r\n            {\r\n              name: type,\r\n              color: 'default',\r\n              background: 'red',\r\n              type: 'normal',\r\n            },\r\n            {\r\n              name: req.status,\r\n              color: 'default',\r\n              background: 'red',\r\n              type: 'normal',\r\n            },\r\n          ],\r\n          session3: [\r\n            {\r\n              iconName: 'user',\r\n              label: req.requestedBy,\r\n            },\r\n            {\r\n              iconName: 'calendar-days',\r\n              label: formatDate(req?.requestedAt, 'dd MMM yyyy', 'en-IN'),\r\n            },\r\n          ],\r\n          session4: {\r\n            status: statusTexts[statusKey],\r\n            iconName: statusIcons[statusKey],\r\n          },\r\n        };\r\n      }),\r\n      footer: {},\r\n    };\r\n  }\r\n}\r\n", "<div class=\"approval-right-screen\">\r\n    <div class=\"approval-title-filter\">\r\n        <app-approval-txt-card [iconName]=\"'hourglass'\" [title]=\"labels.totalApprovals\" [value]=\"totalApprovals\"\r\n            [subtitle]=\"currentTab + ' ' + labels.whichAreRequestedForApproval\"></app-approval-txt-card>\r\n        <app-approval-txt-card [iconName]=\"'shield-alert'\" [title]=\"labels.totalApprovedApprovals\"\r\n            [value]=\"totalApprovedApprovals\"\r\n            [subtitle]=\"currentTab + ' ' + labels.whichAreApproved\"></app-approval-txt-card>\r\n        <app-approval-txt-card [iconName]=\"'hourglass'\" [title]=\"labels.totalPendingApprovals\"\r\n            [value]=\"totalPendingApprovals\"\r\n            [subtitle]=\"labels.all + ' ' + currentTab + ' ' + labels.awaitingApproval\"></app-approval-txt-card>\r\n    </div>\r\n    \r\n<div class=\"filter-section\">\r\n    <div class=\"search-bars\">\r\n        <div style=\"font-size: 1.5rem; font-weight: bold; color: black;\">\r\n            {{currentTab}} Approvals\r\n        </div>\r\n        <div class=\"approval-card-header\">\r\n            All - {{totalRecords}} {{currentTab}}\r\n        </div>\r\n    </div>\r\n    <div class=\"textbox section\">\r\n        <div>\r\n            <form [formGroup]=\"searchForm\">\r\n                <ava-textbox [placeholder]=\"labels.searchPlaceholder\" formControlName=\"search\">\r\n                    <ava-icon slot=\"icon-start\" iconName=\"search\" [iconSize]=\"16\" iconColor=\"var(--color-brand-primary)\"></ava-icon>\r\n                </ava-textbox>\r\n            </form>\r\n        </div>\r\n    </div>\r\n</div>\r\n    \r\n    <div class=\"approval-card-section\">\r\n        @if(totalRecords > 0){        \r\n            @for (item of consoleApproval.contents; track $index){\r\n            <div class=\"approval-card-wrapper\" (click)=\"onCardClick($index)\">\r\n                <ava-approval-card height=\"300\">\r\n                    <ava-card-header>\r\n                        <ava-icon iconSize=\"20\" iconName=\"ellipsis-vertical\"></ava-icon>\r\n                        <div class=\"header\">\r\n                            <h2>{{item.session1.title}}</h2>\r\n                            <ava-tag label=\"{{currentTab}}\" color=\"info\" size=\"sm\"></ava-tag>\r\n                        </div>\r\n                    </ava-card-header>\r\n            \r\n                    <ava-card-content>\r\n                        <div class=\"a-content\">\r\n                            <div class=\"box tag-wrapper\">\r\n                                <ava-tag label=\"Individual\" size=\"sm\"></ava-tag>\r\n                                <ava-tag label=\"Ascendion\" size=\"sm\"></ava-tag>\r\n                                <ava-tag label=\"Digital Ascender\" size=\"sm\"></ava-tag>\r\n                                <ava-tag label=\"Platform Engineering\" size=\"sm\"></ava-tag>\r\n                            </div>\r\n                            <div class=\"box info-wrapper\">\r\n                                <div class=\"f\">\r\n                                    <ava-icon iconSize=\"13\" iconName=\"user\"></ava-icon>\r\n                                    <span>{{item.session3[0].label}}</span>\r\n                                </div>\r\n                                <div class=\"ml-auto s\">\r\n                                    <ava-icon iconSize=\"20\" iconName=\"calendar-days\"></ava-icon>\r\n                                    <span>{{item.session3[1].label}}</span>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </ava-card-content>\r\n            \r\n                    <ava-card-footer>\r\n                        <div class=\"footer-content\">\r\n                            <div class=\"footer-left\">\r\n                                <span class=\"ex\">Execution Status</span>\r\n                                <div>\r\n                                    <ava-icon iconSize=\"20\" iconName=\"circle-check-big\"></ava-icon>\r\n                                    <span>{{item?.session4.status}}</span>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"footer-right\">\r\n                                <ava-button label=\"Test\" (userClick)=\"handleTesting($index)\" variant=\"secondary\" size=\"medium\"\r\n                                    state=\"default\" iconName=\"play\" iconPosition=\"left\"></ava-button>\r\n                                <ava-button label=\"Sendback\" (userClick)=\"rejectApproval($index)\" variant=\"secondary\" size=\"medium\"\r\n                                    state=\"default\" iconName=\"move-left\" iconPosition=\"left\"></ava-button>\r\n                                <ava-button label=\"Approve\" (userClick)=\"approveApproval($index)\" variant=\"primary\" size=\"medium\"\r\n                                    state=\"default\" iconName=\"Check\" iconPosition=\"left\"></ava-button>\r\n                            </div>\r\n                        </div>\r\n                    </ava-card-footer>\r\n                </ava-approval-card>\r\n            </div>\r\n            }\r\n        }\r\n        @else{\r\n            <div class=\"no-pending-message\">\r\n                All {{currentTab}} have been successfully approved. No pending actions.\r\n            </div>\r\n        }\r\n    </div>\r\n</div>\r\n\r\n"], "mappings": "AAAA,SAASA,YAAY,EAAEC,UAAU,QAAQ,iBAAiB;AAE1D,SAAiCC,mBAAmB,QAAQ,gBAAgB;AAC5E,SAAiBC,YAAY,QAAQ,iBAAiB;AACtD,SACEC,qBAAqB,EACrBC,aAAa,EACbC,mBAAmB,EAInBC,eAAe,EAEfC,eAAe,QAGV,wBAAwB;AAC/B,OAAOC,YAAY,MAAM,4BAA4B;AAGrD,SAASC,YAAY,EAAEC,oBAAoB,EAAEC,GAAG,EAAEC,SAAS,QAAQ,MAAM;AAEzE,SAASC,2BAA2B,QAAQ,uDAAuD;AAEnG,SAASC,wBAAwB,QAAQ,oDAAoD;;;;;;;;;;;;ICWjFC,EAAA,CAAAC,cAAA,cAAiE;IAA9BD,EAAA,CAAAE,UAAA,mBAAAC,2EAAA;MAAA,MAAAC,SAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA,EAAAC,MAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAG,WAAA,CAAAP,SAAA,CAAmB;IAAA,EAAC;IAExDJ,EADJ,CAAAC,cAAA,4BAAgC,sBACX;IACbD,EAAA,CAAAY,SAAA,mBAAgE;IAE5DZ,EADJ,CAAAC,cAAA,cAAoB,SACZ;IAAAD,EAAA,CAAAa,MAAA,GAAuB;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAChCd,EAAA,CAAAY,SAAA,kBAAiE;IAEzEZ,EADI,CAAAc,YAAA,EAAM,EACQ;IAIVd,EAFR,CAAAC,cAAA,uBAAkB,cACS,eACU;IAIzBD,EAHA,CAAAY,SAAA,mBAAgD,mBACD,mBACO,mBACI;IAC9DZ,EAAA,CAAAc,YAAA,EAAM;IAEFd,EADJ,CAAAC,cAAA,eAA8B,eACX;IACXD,EAAA,CAAAY,SAAA,oBAAmD;IACnDZ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAa,MAAA,IAA0B;IACpCb,EADoC,CAAAc,YAAA,EAAO,EACrC;IACNd,EAAA,CAAAC,cAAA,eAAuB;IACnBD,EAAA,CAAAY,SAAA,oBAA4D;IAC5DZ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAa,MAAA,IAA0B;IAIhDb,EAJgD,CAAAc,YAAA,EAAO,EACrC,EACJ,EACJ,EACS;IAKPd,EAHZ,CAAAC,cAAA,uBAAiB,eACe,eACC,gBACJ;IAAAD,EAAA,CAAAa,MAAA,wBAAgB;IAAAb,EAAA,CAAAc,YAAA,EAAO;IACxCd,EAAA,CAAAC,cAAA,WAAK;IACDD,EAAA,CAAAY,SAAA,oBAA+D;IAC/DZ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAa,MAAA,IAAyB;IAEvCb,EAFuC,CAAAc,YAAA,EAAO,EACpC,EACJ;IAEFd,EADJ,CAAAC,cAAA,eAA0B,sBAEkC;IAD/BD,EAAA,CAAAE,UAAA,uBAAAa,uFAAA;MAAA,MAAAX,SAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA,EAAAC,MAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAaF,MAAA,CAAAQ,aAAA,CAAAZ,SAAA,CAAqB;IAAA,EAAC;IACJJ,EAAA,CAAAc,YAAA,EAAa;IACrEd,EAAA,CAAAC,cAAA,sBAC6D;IADhCD,EAAA,CAAAE,UAAA,uBAAAe,uFAAA;MAAA,MAAAb,SAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA,EAAAC,MAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAaF,MAAA,CAAAU,cAAA,CAAAd,SAAA,CAAsB;IAAA,EAAC;IACJJ,EAAA,CAAAc,YAAA,EAAa;IAC1Ed,EAAA,CAAAC,cAAA,sBACyD;IAD7BD,EAAA,CAAAE,UAAA,uBAAAiB,uFAAA;MAAA,MAAAf,SAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA,EAAAC,MAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAaF,MAAA,CAAAY,eAAA,CAAAhB,SAAA,CAAuB;IAAA,EAAC;IAMrFJ,EAL6E,CAAAc,YAAA,EAAa,EACpE,EACJ,EACQ,EACF,EAClB;;;;;IA9Ccd,EAAA,CAAAqB,SAAA,GAAuB;IAAvBrB,EAAA,CAAAsB,iBAAA,CAAAC,OAAA,CAAAC,QAAA,CAAAC,KAAA,CAAuB;IAClBzB,EAAA,CAAAqB,SAAA,EAAsB;IAAtBrB,EAAA,CAAA0B,qBAAA,UAAAlB,MAAA,CAAAmB,UAAA,CAAsB;IAejB3B,EAAA,CAAAqB,SAAA,IAA0B;IAA1BrB,EAAA,CAAAsB,iBAAA,CAAAC,OAAA,CAAAK,QAAA,IAAAC,KAAA,CAA0B;IAI1B7B,EAAA,CAAAqB,SAAA,GAA0B;IAA1BrB,EAAA,CAAAsB,iBAAA,CAAAC,OAAA,CAAAK,QAAA,IAAAC,KAAA,CAA0B;IAY1B7B,EAAA,CAAAqB,SAAA,GAAyB;IAAzBrB,EAAA,CAAAsB,iBAAA,CAAAC,OAAA,kBAAAA,OAAA,CAAAO,QAAA,CAAAC,MAAA,CAAyB;;;;;IAtCvD/B,EAAA,CAAAgC,gBAAA,IAAAC,qDAAA,oBAAAjC,EAAA,CAAAkC,sBAAA,CAqDC;;;;IArDDlC,EAAA,CAAAmC,UAAA,CAAA3B,MAAA,CAAA4B,eAAA,CAAAC,QAAA,CAqDC;;;;;IAGDrC,EAAA,CAAAC,cAAA,cAAgC;IAC5BD,EAAA,CAAAa,MAAA,GACJ;IAAAb,EAAA,CAAAc,YAAA,EAAM;;;;IADFd,EAAA,CAAAqB,SAAA,EACJ;IADIrB,EAAA,CAAAsC,kBAAA,UAAA9B,MAAA,CAAAmB,UAAA,2DACJ;;;AD7CZ,WAAaY,uBAAuB;EAA9B,MAAOA,uBAAuB;IAoCxBC,MAAA;IACAC,UAAA;IACAC,eAAA;IACAC,EAAA;IACAC,aAAA;IACAC,YAAA;IACAC,aAAA;IAzCVC,SAAS,GAAGtD,YAAY,CAACuD,MAAM;IAExBC,WAAW,GAAW,EAAE;IACxBC,sBAAsB,GAAW,EAAE;IACnCC,qBAAqB,GAAW,EAAE;IAClCC,cAAc,GAAW,EAAE;IAC3BC,gBAAgB,GAAY,KAAK;IACjCC,oBAAoB,GAAY,IAAI;IACpClB,eAAe,GAAQ,EAAE;IACzBmB,OAAO,GAAqB,EAAE;IAC9BC,iBAAiB,GAAU,EAAE;IAC7BC,YAAY,GAAU,EAAE;IACxBC,WAAW,GAAU,EAAE;IACvBC,eAAe,GAAU,EAAE;IAC3BC,qBAAqB,GAAU,EAAE;IACjCC,aAAa,GAAU,EAAE;IACzBC,gBAAgB,GAAG,CAAC;IACpBC,iBAAiB,GAAG,CAAC;IACrBC,oBAAoB,GAAG,CAAC;IACxBC,QAAQ,GAAG,EAAE;IACbC,YAAY,GAAG,CAAC;IAChBC,SAAS,GAAG,KAAK;IACjBxC,UAAU,GAAG,QAAQ;IACrByC,qBAAqB,GAAG,KAAK;IAC7BC,aAAa,GAAG,KAAK;IACrBC,cAAc,GAAG,KAAK;IACtBC,WAAW,GAAG,EAAE;IAChBC,aAAa,GAAG,CAAC;IACjBC,UAAU;IACVzB,MAAM,GAAQvD,YAAY,CAACuD,MAAM;IACjC0B,eAAe,GAAkB,IAAI;IACrCC,WAAW,GAAQ,IAAI;IACvBC,eAAe,GAAY,CAAC;IAEnCC,YACUrC,MAAc,EACdC,UAAmC,EACnCC,eAAgC,EAChCC,EAAe,EACfC,aAA4B,EAC5BC,YAAiC,EACjCC,aAA4B;MAN5B,KAAAN,MAAM,GAANA,MAAM;MACN,KAAAC,UAAU,GAAVA,UAAU;MACV,KAAAC,eAAe,GAAfA,eAAe;MACf,KAAAC,EAAE,GAAFA,EAAE;MACF,KAAAC,aAAa,GAAbA,aAAa;MACb,KAAAC,YAAY,GAAZA,YAAY;MACZ,KAAAC,aAAa,GAAbA,aAAa;MAErB,IAAI,CAACE,MAAM,GAAGvD,YAAY,CAACuD,MAAM;MACjC,IAAI,CAACO,OAAO,GAAG,CACb;QAAEuB,IAAI,EAAE,IAAI,CAAC9B,MAAM,CAAC+B,WAAW;QAAEC,KAAK,EAAE;MAAa,CAAE,EACvD;QAAEF,IAAI,EAAE,IAAI,CAAC9B,MAAM,CAACiC,QAAQ;QAAED,KAAK,EAAE;MAAU,CAAE,EACjD;QAAEF,IAAI,EAAE,IAAI,CAAC9B,MAAM,CAACkC,KAAK;QAAEF,KAAK,EAAE;MAAO,CAAE,CAC5C;MACD,IAAI,CAACxB,iBAAiB,GAAG,CACvB;QACE2B,EAAE,EAAE,GAAG;QACPC,IAAI,EAAE,QAAQ;QACdC,IAAI,EAAE,IAAI,CAACrC,MAAM,CAACsC,MAAM;QACxBC,KAAK,EAAE,EAAE;QACTC,MAAM,EAAE;OACT,EACD;QAAEL,EAAE,EAAE,GAAG;QAAEC,IAAI,EAAE,cAAc;QAAEC,IAAI,EAAE,IAAI,CAACrC,MAAM,CAACyC,SAAS;QAAEF,KAAK,EAAE;MAAE,CAAE,EACzE;QAAEJ,EAAE,EAAE,GAAG;QAAEC,IAAI,EAAE,KAAK;QAAEC,IAAI,EAAE,IAAI,CAACrC,MAAM,CAAC0C,KAAK;QAAEH,KAAK,EAAE;MAAE,CAAE,CAC7D;MACD,IAAI,CAAC9B,YAAY,GAAG,CAClB;QACE2B,IAAI,EAAE,YAAY;QAClBvD,KAAK,EAAE,IAAI,CAACmB,MAAM,CAACsC,MAAM;QACzBC,KAAK,EAAE;OACR,EACD;QACEH,IAAI,EAAE,eAAe;QACrBvD,KAAK,EAAE,IAAI,CAACmB,MAAM,CAACyC,SAAS;QAC5BF,KAAK,EAAE;OACR,EACD;QACEH,IAAI,EAAE,WAAW;QACjBvD,KAAK,EAAE,IAAI,CAACmB,MAAM,CAAC0C,KAAK;QACxBH,KAAK,EAAE;OACR,CACF;MACD,IAAI,CAACd,UAAU,GAAG,IAAI,CAAC9B,EAAE,CAACgD,KAAK,CAAC;QAC9BC,MAAM,EAAE,CAAC,EAAE;OACZ,CAAC;IACJ;IAEAC,QAAQA,CAAA;MACN,IAAI,CAACC,UAAU,EAAE;MACjB,IAAI,CAAC1C,cAAc,GAAG,EAAE;MACxB,IAAI,CAAC2C,iBAAiB,EAAE;IAC1B;IAEOD,UAAUA,CAAA;MACfE,OAAO,CAACC,GAAG,CAAC,IAAI,CAACxB,UAAU,CAACyB,GAAG,CAAC,QAAQ,CAAC,EAAElB,KAAK,CAAC;MACjD,IAAI,CAACP,UAAU,CACZyB,GAAG,CAAC,QAAQ,CAAE,CACdC,YAAY,CAACC,IAAI,CAChBvG,SAAS,CAAC,EAAE,CAAC,EACbH,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBC,GAAG,CAAEoF,KAAK,IAAKA,KAAK,EAAEqB,WAAW,EAAE,IAAI,EAAE,CAAC,CAC3C,CACAC,SAAS,CAAEC,UAAU,IAAI;QACxB,IAAI,CAACC,WAAW,CAACD,UAAU,CAAC;MAC9B,CAAC,CAAC;IACN;IAEOC,WAAWA,CAACnB,IAAY;MAC7B,MAAMoB,KAAK,GAAGpB,IAAI;MAElB,IAAI,CAACA,IAAI,EAAE;QACT,IAAI,CAACqB,qBAAqB,CAAC,IAAI,CAAC7C,aAAa,EAAE,OAAO,CAAC;QACvD;MACF;MAEA,IAAI,CAACD,qBAAqB,GAAG,IAAI,CAACC,aAAa,CAAC8C,MAAM,CAAEC,IAAI,IAC1DA,IAAI,CAACC,SAAS,EAAER,WAAW,EAAE,CAACS,QAAQ,CAACL,KAAK,CAAC,CAC9C;MACD,IAAI,CAACC,qBAAqB,CAAC,IAAI,CAAC9C,qBAAqB,EAAE,OAAO,CAAC;IACjE;IAEOmD,iBAAiBA,CAACC,IAAS;MAChChB,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEe,IAAI,CAAC;IACzC;IAEOC,MAAMA,CAACC,CAAM;MAClBlB,OAAO,CAACC,GAAG,CAAC,KAAK,GAAGiB,CAAC,CAAC;IACxB;IAEOC,kBAAkBA,CAAA;MACvB,IAAI,CAAC7D,oBAAoB,GAAG,CAAC,IAAI,CAACA,oBAAoB;IACxD;IAEO8D,qBAAqBA,CAACC,WAAoB;MAC/C,IAAI,CAAChE,gBAAgB,GAAGgE,WAAW;MACnCrB,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEoB,WAAW,CAAC;IACtD;IAEOC,gBAAgBA,CAACV,IAAS;MAC/B,IAAI,CAACpD,iBAAiB,CAAC+D,OAAO,CAAEL,CAAC,IAAMA,CAAC,CAAC1B,MAAM,GAAG,KAAM,CAAC;MACzDoB,IAAI,CAACpB,MAAM,GAAG,IAAI;MAClBQ,OAAO,CAACC,GAAG,CAACW,IAAI,CAAC;IACnB;IAEOY,eAAeA,CAACxC,KAAgC;MACrD,OAAOA,KAAK,KAAK,UAAU,IAAIA,KAAK,KAAK,UAAU,IAAIA,KAAK,KAAK,QAAQ,GACrEA,KAAK,GACL,QAAQ;IACd;IAEOe,iBAAiBA,CAAA;MACtB,IAAI,CAACrD,eAAe,CACjB+E,kBAAkB,CAAC,IAAI,CAAC1D,iBAAiB,EAAE,IAAI,CAACE,QAAQ,EAAE,IAAI,CAACE,SAAS,CAAC,CACzEmC,SAAS,CAAEoB,QAAQ,IAAI;QACtB,IAAI,IAAI,CAAC3D,iBAAiB,GAAG,CAAC,EAAE;UAC9B,IAAI,CAACF,aAAa,GAAG,CACnB,GAAG,IAAI,CAACA,aAAa,EACrB,GAAG6D,QAAQ,CAACC,kBAAkB,CAC/B;QACH,CAAC,MAAM;UACL,IAAI,CAAC9D,aAAa,GAAG6D,QAAQ,EAAEC,kBAAkB;QACnD;QACA,IAAI,CAAC/D,qBAAqB,GAAG,IAAI,CAACC,aAAa;QAC/C,IAAI,CAACA,aAAa,GAAG,IAAI,CAACA,aAAa,CAAC8C,MAAM,CAC3CiB,CAAC,IAAKA,CAAC,CAAC7F,MAAM,KAAK,UAAU,CAC/B;QACD;QACA,IAAI,CAACmC,YAAY,GAAG,IAAI,CAACL,aAAa,CAACgE,MAAM;QAC7C,IAAI,CAACnB,qBAAqB,CAAC,IAAI,CAAC7C,aAAa,EAAE,OAAO,CAAC;MACzD,CAAC,CAAC;IACN;IAEOiE,cAAcA,CAACC,IAAY;MAChC,IAAI,CAAChE,iBAAiB,GAAGgE,IAAI;MAC7B,IAAI,CAAChC,iBAAiB,EAAE;IAC1B;IAEOiC,WAAWA,CAAClD,IAAY;MAC7B,IAAI,CAACiB,iBAAiB,EAAE;IAC1B;IAEO7E,cAAcA,CAAC+G,GAAQ;MAC5BjC,OAAO,CAACC,GAAG,CAACgC,GAAG,CAAC;MAChB,IAAI,CAACzD,aAAa,GAAGyD,GAAG;IAC1B;IAEO7G,eAAeA,CAAC6G,GAAQ;MAC7BjC,OAAO,CAACC,GAAG,CAACgC,GAAG,CAAC;MAChB,IAAI,CAACzD,aAAa,GAAGyD,GAAG;MACxBjC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACrC,qBAAqB,CAAC,IAAI,CAACY,aAAa,CAAC,CAAC;IAC7D;IAEQ0D,kBAAkBA,CAAA;MACxB,IAAI,CAACpF,aAAa,CAACqF,YAAY,CAAC;QAC9B1G,KAAK,EAAE,IAAI,CAACuB,MAAM,CAACoF,eAAe;QAClCC,OAAO,EAAE,GAAG,IAAI,CAACrF,MAAM,CAACsF,wBAAwB,WAAW,IAAI,CAACtF,MAAM,CAACuF,4BAA4B,WAAW,IAAI,CAACvF,MAAM,CAACwF,0BAA0B,EAAE;QACtJC,iBAAiB,EAAE,IAAI,CAACzF,MAAM,CAAC0F,OAAO;QACtCC,gBAAgB,EAAE,QAAQ;QAC1BC,oBAAoB,EAAE,QAAQ;QAC9BxD,IAAI,EAAC;OACN,CAAC,CAACyD,IAAI,CAACC,MAAM,IAAG;QACf,IAAIA,MAAM,CAACC,SAAS,EAAE;UACpB,IAAI,CAACC,cAAc,EAAE;QACvB;MACF,CAAC,CAAC;IACJ;IAEQC,kBAAkBA,CAAA;MACxB,MAAMC,aAAa,GAAmB,CAChC;QAAErH,KAAK,EAAE,QAAQ;QAAEsH,OAAO,EAAE,WAAW;QAAEC,MAAM,EAAE;MAAQ,CAAE,EAC3D;QAAEvH,KAAK,EAAE,WAAW;QAAEsH,OAAO,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAU,CAAE,CAC/D;MACL,IAAI,CAACtG,aAAa,CAACuG,QAAQ,CAAC;QAC1B5H,KAAK,EAAE,mBAAmB;QAC1B4G,OAAO,EAAE,sGAAsG;QAC/GiB,OAAO,EAACJ,aAAa;QACrBC,OAAO,EAAE;OACV,CAAC,CAACN,IAAI,CAACC,MAAM,IAAG;QACf,IAAIA,MAAM,CAACC,SAAS,IAAID,MAAM,CAACC,SAAS,KAAK,IAAI,EAAE;UACjD,IAAI,CAACQ,eAAe,CAACT,MAAM,CAAC9B,IAAI,CAAC;QACnC;MACF,CAAC,CAAC;IACJ;IAEOgC,cAAcA,CAAA;MACnB,IAAI,CAACQ,mBAAmB,EAAE;IAC5B;IAEOD,eAAeA,CAACF,QAAa;MAClCrD,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;MAC5C,IAAI,CAACwD,oBAAoB,CAACJ,QAAQ,CAAC;IACrC;IAEO1I,WAAWA,CAAC+I,KAAa;MAC9B1D,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEyD,KAAK,CAAC;MAC1C,IAAI,CAAClF,aAAa,GAAGkF,KAAK;MAC1B,MAAMC,aAAa,GAAG,IAAI,CAAC/F,qBAAqB,CAAC,IAAI,CAACY,aAAa,CAAC;MACpE,IAAI,CAACI,eAAe,GAAG+E,aAAa,CAACC,OAAO;MAC5C,IAAI,CAACC,eAAe,CAACF,aAAa,CAAC;MAEnC,IAAI,CAAC/G,aAAa,CAACkH,IAAI,CAAChK,2BAA2B,EAAE;QACnD6E,WAAW,EAAE,IAAI,CAACA,WAAW;QAC7BoF,YAAY,EAAEA,CAAA,KAAM,IAAI,CAACnH,aAAa,CAACoH,KAAK,EAAE;QAC9CC,SAAS,EAAEA,CAAA,KAAM,IAAI,CAACC,eAAe,CAACP,aAAa,CAACC,OAAO,CAAC;QAC5D1I,cAAc,EAAEA,CAAA,KAAM,IAAI,CAACiJ,qBAAqB,EAAE;QAClD/I,eAAe,EAAEA,CAAA,KAAM,IAAI,CAACgJ,sBAAsB,EAAE;QACpDC,YAAY,EAAEA,CAAA,KAAM,IAAI,CAACC,0BAA0B;OACpD,CAAC;MACFtE,OAAO,CAACC,GAAG,CAAC0D,aAAa,CAAC;IAC5B;IAEOS,sBAAsBA,CAAA;MAC3B,IAAI,CAAClC,kBAAkB,EAAE;IAC3B;IAEOiC,qBAAqBA,CAAA;MAC1B,IAAI,CAAClB,kBAAkB,EAAE;IAC3B;IAEOiB,eAAeA,CAACN,OAAe;MACpC5D,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE2D,OAAO,CAAC;MAClC,IAAI,CAAChH,aAAa,CAACoH,KAAK,EAAE;MAC1B,IAAI,CAACxH,MAAM,CAAC+H,QAAQ,CAClB,CAAC,6BAA6B,CAAC,EAC/B;QAAEC,WAAW,EAAE;UAAErF,EAAE,EAAEyE,OAAO;UAAEa,IAAI,EAAE;QAAM;MAAE,CAAE,CAC/C;IACH;IAEOjB,mBAAmBA,CAAA;MACxB,MAAMkB,YAAY,GAAG,IAAI,CAAC9G,qBAAqB,CAAC,IAAI,CAACY,aAAa,CAAC;MACnE,MAAMW,EAAE,GAAGuF,YAAY,CAACvF,EAAE;MAC1B,MAAMyE,OAAO,GAAGc,YAAY,CAACd,OAAO;MACpC,MAAM7H,MAAM,GAAG,UAAU;MACzB,MAAM4I,UAAU,GAAGD,YAAY,CAACC,UAAU;MAE1C;MACA,IAAI,CAAC7H,aAAa,CAAC8H,OAAO,CAAC;QACzBnJ,KAAK,EAAE,oBAAoB;QAC3B4G,OAAO,EAAE,yCAAyC;QAClDwC,YAAY,EAAE,KAAK;QACnBC,gBAAgB,EAAE;OACnB,CAAC;MAEF,IAAI,CAACpI,eAAe,CACjBqI,YAAY,CAAC5F,EAAE,EAAEyE,OAAO,EAAE7H,MAAM,EAAE4I,UAAU,CAAC,CAC7CrE,SAAS,CAAC;QACT0E,IAAI,EAAGtD,QAAa,IAAI;UACtB,IAAI,CAAC5E,aAAa,CAACmI,KAAK,EAAE,CAAC,CAAC;UAE5B,MAAM5C,OAAO,GAAGX,QAAQ,EAAEW,OAAO,IAAI,IAAI,CAACrF,MAAM,CAACkI,0BAA0B;UAE3E;UACA,IAAI,CAACxG,eAAe,GAAGkF,OAAO;UAE9B,IAAI,CAAC9G,aAAa,CAACqI,OAAO,CAAC;YACzB1J,KAAK,EAAE,gBAAgB;YACvB4G,OAAO,EAAEA;WACV,CAAC,CAACQ,IAAI,CAACC,MAAM,IAAG;YACf,IAAIA,MAAM,CAACM,MAAM,KAAK,WAAW,EAAE;cACjC;cACA,IAAI,CAAC5G,MAAM,CAAC+H,QAAQ,CAAC,CAAC,6BAA6B,CAAC,EAAE;gBACpDC,WAAW,EAAE;kBACXrF,EAAE,EAAE,IAAI,CAACT,eAAe;kBACxB+F,IAAI,EAAE;;eAET,CAAC;YACJ,CAAC,MAAM;cACL,IAAI,CAAC1E,iBAAiB,EAAE,CAAC,CAAC;YAC5B;YACA;YACA,IAAI,CAACrB,eAAe,GAAG,IAAI;UAC7B,CAAC,CAAC;QACJ,CAAC;QACD0G,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACtI,aAAa,CAACmI,KAAK,EAAE,CAAC,CAAC;UAE5B,MAAMI,YAAY,GAAGD,KAAK,EAAEA,KAAK,EAAE/C,OAAO,IAAI,IAAI,CAACrF,MAAM,CAACsI,mBAAmB;UAC7E,IAAI,CAACxI,aAAa,CAACsI,KAAK,CAAC;YACvB3J,KAAK,EAAE,iBAAiB;YACxB4G,OAAO,EAAEgD,YAAY;YACrBE,eAAe,EAAE,IAAI;YACrBC,eAAe,EAAE;WAClB,CAAC,CAAC3C,IAAI,CAACC,MAAM,IAAG;YACf,IAAIA,MAAM,CAACM,MAAM,KAAK,OAAO,EAAE;cAC7B,IAAI,CAACI,mBAAmB,EAAE;YAC5B;UACF,CAAC,CAAC;QACJ;OACD,CAAC;IACN;IAEOK,eAAeA,CAACF,aAAmB;MACxC,IAAI,CAAChF,WAAW,GAAG;QACjB8G,IAAI,EAAE,OAAO;QACbhK,KAAK,EAAEkI,aAAa,CAAC9C,SAAS;QAC9BG,IAAI,EAAE2C,aAAa;QACnBiB,OAAO,EAAE,IAAI;QACbQ,KAAK,EAAE;OACR;MAEDpF,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE0D,aAAa,CAACC,OAAO,CAAC;MACvD,IAAI,CAAC/G,YAAY,CAAC6I,gCAAgC,CAAC/B,aAAa,CAACC,OAAO,CAAC,CAACtD,SAAS,CAAC;QAClF0E,IAAI,EAAGtD,QAAQ,IAAI;UACjB;UACA,IAAI,CAAC/C,WAAW,CAACqC,IAAI,GAAGU,QAAQ,CAACiE,WAAW;UAC5C,IAAI,CAAChH,WAAW,CAACiG,OAAO,GAAG,KAAK;UAChC,IAAI,CAACjG,WAAW,CAACyG,KAAK,GAAG,IAAI;QAC/B,CAAC;QACDA,KAAK,EAAGA,KAAK,IAAI;UACfpF,OAAO,CAACoF,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;QAChC;OACD,CAAC;IACJ;IAEO3B,oBAAoBA,CAACJ,QAAa;MACvC,MAAMqB,YAAY,GAAG,IAAI,CAAC9G,qBAAqB,CAAC,IAAI,CAACY,aAAa,CAAC;MACnE,MAAMW,EAAE,GAAGuF,YAAY,CAACvF,EAAE;MAC1B,MAAMyE,OAAO,GAAGc,YAAY,CAACd,OAAO;MACpC,MAAM7H,MAAM,GAAG,UAAU;MACzB,MAAM4I,UAAU,GAAGD,YAAY,CAACC,UAAU;MAC1C,MAAMtC,OAAO,GAAGgB,QAAQ;MAExB;MACA,IAAI,CAACvG,aAAa,CAAC8H,OAAO,CAAC;QACzBnJ,KAAK,EAAE,oBAAoB;QAC3B4G,OAAO,EAAE,wCAAwC;QACjDwC,YAAY,EAAE,KAAK;QACnBC,gBAAgB,EAAE;OACnB,CAAC;MAEF,IAAI,CAACpI,eAAe,CACjBkJ,WAAW,CAACzG,EAAE,EAAEyE,OAAO,EAAE7H,MAAM,EAAE4I,UAAU,EAAEtC,OAAO,CAAC,CACrD/B,SAAS,CAAC;QACT0E,IAAI,EAAGtD,QAAa,IAAI;UACtB,IAAI,CAAC5E,aAAa,CAACmI,KAAK,EAAE,CAAC,CAAC;UAE5B,MAAM5C,OAAO,GAAGX,QAAQ,EAAEW,OAAO,IAAI,IAAI,CAACrF,MAAM,CAAC6I,yBAAyB;UAC1E,IAAI,CAAC/I,aAAa,CAACqI,OAAO,CAAC;YACzB1J,KAAK,EAAE,gBAAgB;YACvB4G,OAAO,EAAEA;WACV,CAAC,CAACQ,IAAI,CAAC,MAAK;YACX,IAAI,CAAC9C,iBAAiB,EAAE,CAAC,CAAC;UAC5B,CAAC,CAAC;QACJ,CAAC;QACDqF,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACtI,aAAa,CAACmI,KAAK,EAAE,CAAC,CAAC;UAE5B,MAAMI,YAAY,GAAGD,KAAK,EAAEA,KAAK,EAAE/C,OAAO,IAAI,IAAI,CAACrF,MAAM,CAACsI,mBAAmB;UAC7E,IAAI,CAACxI,aAAa,CAACsI,KAAK,CAAC;YACvB3J,KAAK,EAAE,kBAAkB;YACzB4G,OAAO,EAAEgD,YAAY;YACrBE,eAAe,EAAE,IAAI;YACrBC,eAAe,EAAE;WAClB,CAAC,CAAC3C,IAAI,CAACC,MAAM,IAAG;YACf,IAAIA,MAAM,CAACM,MAAM,KAAK,OAAO,EAAE;cAC7B,IAAI,CAACK,oBAAoB,CAACJ,QAAQ,CAAC;YACrC;UACF,CAAC,CAAC;QACJ;OACD,CAAC;IACN;IAEOrI,aAAaA,CAAC0I,KAAU;MAC7B,IAAI,CAAC9E,eAAe,GAAG,IAAI,CAAChB,qBAAqB,CAAC8F,KAAK,CAAC,CAACE,OAAO;MAChE5D,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAACrB,eAAe,CAAC;IACxD;IAEO0F,0BAA0BA,CAAA;MAC/B,IAAI,CAAC9H,MAAM,CAAC+H,QAAQ,CAAC,CAAC,qCAAqC,CAAC,EAAE;QAC5DC,WAAW,EAAE;UAAErF,EAAE,EAAE,IAAI,CAACP;QAAe;OACxC,CAAC;IACJ;IAEO8B,qBAAqBA,CAACM,IAAW,EAAEyE,IAAY;MACpD,IAAI,CAACrJ,eAAe,GAAG;QACrBC,QAAQ,EAAE2E,IAAI,EAAEpH,GAAG,CAAEkM,GAAQ,IAAI;UAC/B,MAAMC,WAAW,GAAkC;YACjDC,QAAQ,EAAE,kBAAkB;YAC5BC,QAAQ,EAAE,UAAU;YACpBC,MAAM,EAAE;WACT;UACD,MAAMC,WAAW,GAAkC;YACjDH,QAAQ,EAAE,IAAI,CAAChJ,MAAM,CAACgJ,QAAQ;YAC9BC,QAAQ,EAAE,IAAI,CAACjJ,MAAM,CAACiJ,QAAQ;YAC9BC,MAAM,EAAE,IAAI,CAAClJ,MAAM,CAACkJ;WACrB;UACD,MAAME,SAAS,GAAG,IAAI,CAAC5E,eAAe,CAACsE,GAAG,EAAE/J,MAAM,CAAC;UACnD,MAAMsK,UAAU,GAAGP,GAAG,CAAClC,OAAO;UAC9B,MAAMnI,KAAK,GAAGqK,GAAG,CAACjF,SAAS;UAE3B,OAAO;YACL1B,EAAE,EAAE2G,GAAG,CAAC3G,EAAE;YACVmH,KAAK,EAAED,UAAU;YACjBZ,IAAI,EAAEA,IAAI;YACVjK,QAAQ,EAAE;cACRC,KAAK,EAAEA,KAAK;cACZuB,MAAM,EAAE,CACN;gBACE8B,IAAI,EAAE2G,IAAI;gBACVc,KAAK,EAAE,SAAS;gBAChBC,UAAU,EAAE,KAAK;gBACjBf,IAAI,EAAE;eACP,EACD;gBACE3G,IAAI,EAAEgH,GAAG,CAACW,iBAAiB;gBAC3BF,KAAK,EAAET,GAAG,CAACW,iBAAiB,KAAK,QAAQ,GAAG,OAAO,GAAG,MAAM;gBAC5DD,UAAU,EAAE,KAAK;gBACjBf,IAAI,EAAE;eACP;aAEJ;YACDiB,QAAQ,EAAE,CACR;cACE5H,IAAI,EAAE2G,IAAI;cACVc,KAAK,EAAE,SAAS;cAChBC,UAAU,EAAE,KAAK;cACjBf,IAAI,EAAE;aACP,EACD;cACE3G,IAAI,EAAEgH,GAAG,CAAC/J,MAAM;cAChBwK,KAAK,EAAE,SAAS;cAChBC,UAAU,EAAE,KAAK;cACjBf,IAAI,EAAE;aACP,CACF;YACD7J,QAAQ,EAAE,CACR;cACE+K,QAAQ,EAAE,MAAM;cAChB9K,KAAK,EAAEiK,GAAG,CAACc;aACZ,EACD;cACED,QAAQ,EAAE,eAAe;cACzB9K,KAAK,EAAE5C,UAAU,CAAC6M,GAAG,EAAEe,WAAW,EAAE,aAAa,EAAE,OAAO;aAC3D,CACF;YACD/K,QAAQ,EAAE;cACRC,MAAM,EAAEoK,WAAW,CAACC,SAAS,CAAC;cAC9BO,QAAQ,EAAEZ,WAAW,CAACK,SAAS;;WAElC;QACH,CAAC,CAAC;QACFU,MAAM,EAAE;OACT;IACH;;uCAheWvK,uBAAuB,EAAAvC,EAAA,CAAA+M,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAjN,EAAA,CAAA+M,iBAAA,CAAAG,EAAA,CAAAC,uBAAA,GAAAnN,EAAA,CAAA+M,iBAAA,CAAAK,EAAA,CAAAC,eAAA,GAAArN,EAAA,CAAA+M,iBAAA,CAAAO,EAAA,CAAAC,WAAA,GAAAvN,EAAA,CAAA+M,iBAAA,CAAAS,EAAA,CAAAC,aAAA,GAAAzN,EAAA,CAAA+M,iBAAA,CAAAW,EAAA,CAAAC,mBAAA,GAAA3N,EAAA,CAAA+M,iBAAA,CAAAa,EAAA,CAAAC,aAAA;IAAA;;YAAvBtL,uBAAuB;MAAAuL,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC9ChCpO,EADJ,CAAAC,cAAA,aAAmC,aACI;UAM/BD,EALA,CAAAY,SAAA,+BACgG,+BAGZ,+BAGmB;UAC3GZ,EAAA,CAAAc,YAAA,EAAM;UAIFd,EAFR,CAAAC,cAAA,aAA4B,aACC,aAC4C;UAC7DD,EAAA,CAAAa,MAAA,GACJ;UAAAb,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAC,cAAA,aAAkC;UAC9BD,EAAA,CAAAa,MAAA,IACJ;UACJb,EADI,CAAAc,YAAA,EAAM,EACJ;UAIMd,EAHZ,CAAAC,cAAA,cAA6B,WACpB,eAC8B,sBACoD;UAC3ED,EAAA,CAAAY,SAAA,oBAAgH;UAKpIZ,EAJgB,CAAAc,YAAA,EAAc,EACX,EACL,EACJ,EACJ;UAEFd,EAAA,CAAAC,cAAA,eAAmC;UAyD/BD,EAxDA,CAAAsO,UAAA,KAAAC,+CAAA,OAAsB,KAAAC,+CAAA,kBAwDhB;UAMdxO,EADI,CAAAc,YAAA,EAAM,EACJ;;;UA7FyBd,EAAA,CAAAqB,SAAA,GAAwB;UAC3CrB,EADmB,CAAAyO,UAAA,yBAAwB,UAAAJ,GAAA,CAAArL,MAAA,CAAAI,cAAA,CAAgC,UAAAiL,GAAA,CAAAjL,cAAA,CAAyB,aAAAiL,GAAA,CAAA1M,UAAA,SAAA0M,GAAA,CAAArL,MAAA,CAAA0L,4BAAA,CACjC;UAChD1O,EAAA,CAAAqB,SAAA,EAA2B;UAE9CrB,EAFmB,CAAAyO,UAAA,4BAA2B,UAAAJ,GAAA,CAAArL,MAAA,CAAAE,sBAAA,CAAwC,UAAAmL,GAAA,CAAAnL,sBAAA,CACtD,aAAAmL,GAAA,CAAA1M,UAAA,SAAA0M,GAAA,CAAArL,MAAA,CAAA2L,gBAAA,CACuB;UACpC3O,EAAA,CAAAqB,SAAA,EAAwB;UAE3CrB,EAFmB,CAAAyO,UAAA,yBAAwB,UAAAJ,GAAA,CAAArL,MAAA,CAAAG,qBAAA,CAAuC,UAAAkL,GAAA,CAAAlL,qBAAA,CACnD,aAAAkL,GAAA,CAAArL,MAAA,CAAA4L,GAAA,SAAAP,GAAA,CAAA1M,UAAA,SAAA0M,GAAA,CAAArL,MAAA,CAAA6L,gBAAA,CAC2C;UAM1E7O,EAAA,CAAAqB,SAAA,GACJ;UADIrB,EAAA,CAAAsC,kBAAA,MAAA+L,GAAA,CAAA1M,UAAA,gBACJ;UAEI3B,EAAA,CAAAqB,SAAA,GACJ;UADIrB,EAAA,CAAA8O,kBAAA,YAAAT,GAAA,CAAAnK,YAAA,OAAAmK,GAAA,CAAA1M,UAAA,MACJ;UAIU3B,EAAA,CAAAqB,SAAA,GAAwB;UAAxBrB,EAAA,CAAAyO,UAAA,cAAAJ,GAAA,CAAA5J,UAAA,CAAwB;UACbzE,EAAA,CAAAqB,SAAA,EAAwC;UAAxCrB,EAAA,CAAAyO,UAAA,gBAAAJ,GAAA,CAAArL,MAAA,CAAA+L,iBAAA,CAAwC;UACH/O,EAAA,CAAAqB,SAAA,EAAe;UAAfrB,EAAA,CAAAyO,UAAA,gBAAe;UAQzEzO,EAAA,CAAAqB,SAAA,GA4DC;UA5DDrB,EAAA,CAAAgP,aAAA,CAAAX,GAAA,CAAAnK,YAAA,eA4DC;;;qBD9DLlF,YAAY,EACZG,YAAY,EACZC,qBAAqB,EACrBC,aAAa,EACbC,mBAAmB,EACnBJ,mBAAmB,EAAAoO,EAAA,CAAA2B,aAAA,EAAA3B,EAAA,CAAA4B,eAAA,EAAA5B,EAAA,CAAA6B,oBAAA,EAAA7B,EAAA,CAAA8B,kBAAA,EAAA9B,EAAA,CAAA+B,eAAA,EACnB9P,eAAe,EACfC,eAAe,EAGfO,wBAAwB;MAAAuP,MAAA;IAAA;;SAMf/M,uBAAuB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}