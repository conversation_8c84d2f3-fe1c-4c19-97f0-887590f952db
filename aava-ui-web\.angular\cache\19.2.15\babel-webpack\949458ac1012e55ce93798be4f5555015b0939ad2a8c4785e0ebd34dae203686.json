{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { PixelRatio } from '../../../../base/browser/pixelRatio.js';\nimport * as dom from '../../../../base/browser/dom.js';\nimport { GlobalPointerMoveMonitor } from '../../../../base/browser/globalPointerMoveMonitor.js';\nimport { Widget } from '../../../../base/browser/ui/widget.js';\nimport { Codicon } from '../../../../base/common/codicons.js';\nimport { Color, HSVA, RGBA } from '../../../../base/common/color.js';\nimport { Emitter } from '../../../../base/common/event.js';\nimport { Disposable } from '../../../../base/common/lifecycle.js';\nimport { ThemeIcon } from '../../../../base/common/themables.js';\nimport './colorPicker.css';\nimport { localize } from '../../../../nls.js';\nimport { editorHoverBackground } from '../../../../platform/theme/common/colorRegistry.js';\nimport { registerIcon } from '../../../../platform/theme/common/iconRegistry.js';\nconst $ = dom.$;\nexport class ColorPickerHeader extends Disposable {\n  constructor(container, model, themeService, showingStandaloneColorPicker = false) {\n    super();\n    this.model = model;\n    this.showingStandaloneColorPicker = showingStandaloneColorPicker;\n    this._closeButton = null;\n    this._domNode = $('.colorpicker-header');\n    dom.append(container, this._domNode);\n    this._pickedColorNode = dom.append(this._domNode, $('.picked-color'));\n    dom.append(this._pickedColorNode, $('span.codicon.codicon-color-mode'));\n    this._pickedColorPresentation = dom.append(this._pickedColorNode, document.createElement('span'));\n    this._pickedColorPresentation.classList.add('picked-color-presentation');\n    const tooltip = localize('clickToToggleColorOptions', \"Click to toggle color options (rgb/hsl/hex)\");\n    this._pickedColorNode.setAttribute('title', tooltip);\n    this._originalColorNode = dom.append(this._domNode, $('.original-color'));\n    this._originalColorNode.style.backgroundColor = Color.Format.CSS.format(this.model.originalColor) || '';\n    this.backgroundColor = themeService.getColorTheme().getColor(editorHoverBackground) || Color.white;\n    this._register(themeService.onDidColorThemeChange(theme => {\n      this.backgroundColor = theme.getColor(editorHoverBackground) || Color.white;\n    }));\n    this._register(dom.addDisposableListener(this._pickedColorNode, dom.EventType.CLICK, () => this.model.selectNextColorPresentation()));\n    this._register(dom.addDisposableListener(this._originalColorNode, dom.EventType.CLICK, () => {\n      this.model.color = this.model.originalColor;\n      this.model.flushColor();\n    }));\n    this._register(model.onDidChangeColor(this.onDidChangeColor, this));\n    this._register(model.onDidChangePresentation(this.onDidChangePresentation, this));\n    this._pickedColorNode.style.backgroundColor = Color.Format.CSS.format(model.color) || '';\n    this._pickedColorNode.classList.toggle('light', model.color.rgba.a < 0.5 ? this.backgroundColor.isLighter() : model.color.isLighter());\n    this.onDidChangeColor(this.model.color);\n    // When the color picker widget is a standalone color picker widget, then add a close button\n    if (this.showingStandaloneColorPicker) {\n      this._domNode.classList.add('standalone-colorpicker');\n      this._closeButton = this._register(new CloseButton(this._domNode));\n    }\n  }\n  get closeButton() {\n    return this._closeButton;\n  }\n  get pickedColorNode() {\n    return this._pickedColorNode;\n  }\n  get originalColorNode() {\n    return this._originalColorNode;\n  }\n  onDidChangeColor(color) {\n    this._pickedColorNode.style.backgroundColor = Color.Format.CSS.format(color) || '';\n    this._pickedColorNode.classList.toggle('light', color.rgba.a < 0.5 ? this.backgroundColor.isLighter() : color.isLighter());\n    this.onDidChangePresentation();\n  }\n  onDidChangePresentation() {\n    this._pickedColorPresentation.textContent = this.model.presentation ? this.model.presentation.label : '';\n  }\n}\nclass CloseButton extends Disposable {\n  constructor(container) {\n    super();\n    this._onClicked = this._register(new Emitter());\n    this.onClicked = this._onClicked.event;\n    this._button = document.createElement('div');\n    this._button.classList.add('close-button');\n    dom.append(container, this._button);\n    const innerDiv = document.createElement('div');\n    innerDiv.classList.add('close-button-inner-div');\n    dom.append(this._button, innerDiv);\n    const closeButton = dom.append(innerDiv, $('.button' + ThemeIcon.asCSSSelector(registerIcon('color-picker-close', Codicon.close, localize('closeIcon', 'Icon to close the color picker')))));\n    closeButton.classList.add('close-icon');\n    this._register(dom.addDisposableListener(this._button, dom.EventType.CLICK, () => {\n      this._onClicked.fire();\n    }));\n  }\n}\nexport class ColorPickerBody extends Disposable {\n  constructor(container, model, pixelRatio, isStandaloneColorPicker = false) {\n    super();\n    this.model = model;\n    this.pixelRatio = pixelRatio;\n    this._insertButton = null;\n    this._domNode = $('.colorpicker-body');\n    dom.append(container, this._domNode);\n    this._saturationBox = new SaturationBox(this._domNode, this.model, this.pixelRatio);\n    this._register(this._saturationBox);\n    this._register(this._saturationBox.onDidChange(this.onDidSaturationValueChange, this));\n    this._register(this._saturationBox.onColorFlushed(this.flushColor, this));\n    this._opacityStrip = new OpacityStrip(this._domNode, this.model, isStandaloneColorPicker);\n    this._register(this._opacityStrip);\n    this._register(this._opacityStrip.onDidChange(this.onDidOpacityChange, this));\n    this._register(this._opacityStrip.onColorFlushed(this.flushColor, this));\n    this._hueStrip = new HueStrip(this._domNode, this.model, isStandaloneColorPicker);\n    this._register(this._hueStrip);\n    this._register(this._hueStrip.onDidChange(this.onDidHueChange, this));\n    this._register(this._hueStrip.onColorFlushed(this.flushColor, this));\n    if (isStandaloneColorPicker) {\n      this._insertButton = this._register(new InsertButton(this._domNode));\n      this._domNode.classList.add('standalone-colorpicker');\n    }\n  }\n  flushColor() {\n    this.model.flushColor();\n  }\n  onDidSaturationValueChange({\n    s,\n    v\n  }) {\n    const hsva = this.model.color.hsva;\n    this.model.color = new Color(new HSVA(hsva.h, s, v, hsva.a));\n  }\n  onDidOpacityChange(a) {\n    const hsva = this.model.color.hsva;\n    this.model.color = new Color(new HSVA(hsva.h, hsva.s, hsva.v, a));\n  }\n  onDidHueChange(value) {\n    const hsva = this.model.color.hsva;\n    const h = (1 - value) * 360;\n    this.model.color = new Color(new HSVA(h === 360 ? 0 : h, hsva.s, hsva.v, hsva.a));\n  }\n  get domNode() {\n    return this._domNode;\n  }\n  get saturationBox() {\n    return this._saturationBox;\n  }\n  get enterButton() {\n    return this._insertButton;\n  }\n  layout() {\n    this._saturationBox.layout();\n    this._opacityStrip.layout();\n    this._hueStrip.layout();\n  }\n}\nclass SaturationBox extends Disposable {\n  constructor(container, model, pixelRatio) {\n    super();\n    this.model = model;\n    this.pixelRatio = pixelRatio;\n    this._onDidChange = new Emitter();\n    this.onDidChange = this._onDidChange.event;\n    this._onColorFlushed = new Emitter();\n    this.onColorFlushed = this._onColorFlushed.event;\n    this._domNode = $('.saturation-wrap');\n    dom.append(container, this._domNode);\n    // Create canvas, draw selected color\n    this._canvas = document.createElement('canvas');\n    this._canvas.className = 'saturation-box';\n    dom.append(this._domNode, this._canvas);\n    // Add selection circle\n    this.selection = $('.saturation-selection');\n    dom.append(this._domNode, this.selection);\n    this.layout();\n    this._register(dom.addDisposableListener(this._domNode, dom.EventType.POINTER_DOWN, e => this.onPointerDown(e)));\n    this._register(this.model.onDidChangeColor(this.onDidChangeColor, this));\n    this.monitor = null;\n  }\n  get domNode() {\n    return this._domNode;\n  }\n  onPointerDown(e) {\n    if (!e.target || !(e.target instanceof Element)) {\n      return;\n    }\n    this.monitor = this._register(new GlobalPointerMoveMonitor());\n    const origin = dom.getDomNodePagePosition(this._domNode);\n    if (e.target !== this.selection) {\n      this.onDidChangePosition(e.offsetX, e.offsetY);\n    }\n    this.monitor.startMonitoring(e.target, e.pointerId, e.buttons, event => this.onDidChangePosition(event.pageX - origin.left, event.pageY - origin.top), () => null);\n    const pointerUpListener = dom.addDisposableListener(e.target.ownerDocument, dom.EventType.POINTER_UP, () => {\n      this._onColorFlushed.fire();\n      pointerUpListener.dispose();\n      if (this.monitor) {\n        this.monitor.stopMonitoring(true);\n        this.monitor = null;\n      }\n    }, true);\n  }\n  onDidChangePosition(left, top) {\n    const s = Math.max(0, Math.min(1, left / this.width));\n    const v = Math.max(0, Math.min(1, 1 - top / this.height));\n    this.paintSelection(s, v);\n    this._onDidChange.fire({\n      s,\n      v\n    });\n  }\n  layout() {\n    this.width = this._domNode.offsetWidth;\n    this.height = this._domNode.offsetHeight;\n    this._canvas.width = this.width * this.pixelRatio;\n    this._canvas.height = this.height * this.pixelRatio;\n    this.paint();\n    const hsva = this.model.color.hsva;\n    this.paintSelection(hsva.s, hsva.v);\n  }\n  paint() {\n    const hsva = this.model.color.hsva;\n    const saturatedColor = new Color(new HSVA(hsva.h, 1, 1, 1));\n    const ctx = this._canvas.getContext('2d');\n    const whiteGradient = ctx.createLinearGradient(0, 0, this._canvas.width, 0);\n    whiteGradient.addColorStop(0, 'rgba(255, 255, 255, 1)');\n    whiteGradient.addColorStop(0.5, 'rgba(255, 255, 255, 0.5)');\n    whiteGradient.addColorStop(1, 'rgba(255, 255, 255, 0)');\n    const blackGradient = ctx.createLinearGradient(0, 0, 0, this._canvas.height);\n    blackGradient.addColorStop(0, 'rgba(0, 0, 0, 0)');\n    blackGradient.addColorStop(1, 'rgba(0, 0, 0, 1)');\n    ctx.rect(0, 0, this._canvas.width, this._canvas.height);\n    ctx.fillStyle = Color.Format.CSS.format(saturatedColor);\n    ctx.fill();\n    ctx.fillStyle = whiteGradient;\n    ctx.fill();\n    ctx.fillStyle = blackGradient;\n    ctx.fill();\n  }\n  paintSelection(s, v) {\n    this.selection.style.left = `${s * this.width}px`;\n    this.selection.style.top = `${this.height - v * this.height}px`;\n  }\n  onDidChangeColor(color) {\n    if (this.monitor && this.monitor.isMonitoring()) {\n      return;\n    }\n    this.paint();\n    const hsva = color.hsva;\n    this.paintSelection(hsva.s, hsva.v);\n  }\n}\nclass Strip extends Disposable {\n  constructor(container, model, showingStandaloneColorPicker = false) {\n    super();\n    this.model = model;\n    this._onDidChange = new Emitter();\n    this.onDidChange = this._onDidChange.event;\n    this._onColorFlushed = new Emitter();\n    this.onColorFlushed = this._onColorFlushed.event;\n    if (showingStandaloneColorPicker) {\n      this.domNode = dom.append(container, $('.standalone-strip'));\n      this.overlay = dom.append(this.domNode, $('.standalone-overlay'));\n    } else {\n      this.domNode = dom.append(container, $('.strip'));\n      this.overlay = dom.append(this.domNode, $('.overlay'));\n    }\n    this.slider = dom.append(this.domNode, $('.slider'));\n    this.slider.style.top = `0px`;\n    this._register(dom.addDisposableListener(this.domNode, dom.EventType.POINTER_DOWN, e => this.onPointerDown(e)));\n    this._register(model.onDidChangeColor(this.onDidChangeColor, this));\n    this.layout();\n  }\n  layout() {\n    this.height = this.domNode.offsetHeight - this.slider.offsetHeight;\n    const value = this.getValue(this.model.color);\n    this.updateSliderPosition(value);\n  }\n  onDidChangeColor(color) {\n    const value = this.getValue(color);\n    this.updateSliderPosition(value);\n  }\n  onPointerDown(e) {\n    if (!e.target || !(e.target instanceof Element)) {\n      return;\n    }\n    const monitor = this._register(new GlobalPointerMoveMonitor());\n    const origin = dom.getDomNodePagePosition(this.domNode);\n    this.domNode.classList.add('grabbing');\n    if (e.target !== this.slider) {\n      this.onDidChangeTop(e.offsetY);\n    }\n    monitor.startMonitoring(e.target, e.pointerId, e.buttons, event => this.onDidChangeTop(event.pageY - origin.top), () => null);\n    const pointerUpListener = dom.addDisposableListener(e.target.ownerDocument, dom.EventType.POINTER_UP, () => {\n      this._onColorFlushed.fire();\n      pointerUpListener.dispose();\n      monitor.stopMonitoring(true);\n      this.domNode.classList.remove('grabbing');\n    }, true);\n  }\n  onDidChangeTop(top) {\n    const value = Math.max(0, Math.min(1, 1 - top / this.height));\n    this.updateSliderPosition(value);\n    this._onDidChange.fire(value);\n  }\n  updateSliderPosition(value) {\n    this.slider.style.top = `${(1 - value) * this.height}px`;\n  }\n}\nclass OpacityStrip extends Strip {\n  constructor(container, model, showingStandaloneColorPicker = false) {\n    super(container, model, showingStandaloneColorPicker);\n    this.domNode.classList.add('opacity-strip');\n    this.onDidChangeColor(this.model.color);\n  }\n  onDidChangeColor(color) {\n    super.onDidChangeColor(color);\n    const {\n      r,\n      g,\n      b\n    } = color.rgba;\n    const opaque = new Color(new RGBA(r, g, b, 1));\n    const transparent = new Color(new RGBA(r, g, b, 0));\n    this.overlay.style.background = `linear-gradient(to bottom, ${opaque} 0%, ${transparent} 100%)`;\n  }\n  getValue(color) {\n    return color.hsva.a;\n  }\n}\nclass HueStrip extends Strip {\n  constructor(container, model, showingStandaloneColorPicker = false) {\n    super(container, model, showingStandaloneColorPicker);\n    this.domNode.classList.add('hue-strip');\n  }\n  getValue(color) {\n    return 1 - color.hsva.h / 360;\n  }\n}\nexport class InsertButton extends Disposable {\n  constructor(container) {\n    super();\n    this._onClicked = this._register(new Emitter());\n    this.onClicked = this._onClicked.event;\n    this._button = dom.append(container, document.createElement('button'));\n    this._button.classList.add('insert-button');\n    this._button.textContent = 'Insert';\n    this._register(dom.addDisposableListener(this._button, dom.EventType.CLICK, () => {\n      this._onClicked.fire();\n    }));\n  }\n  get button() {\n    return this._button;\n  }\n}\nexport class ColorPickerWidget extends Widget {\n  constructor(container, model, pixelRatio, themeService, standaloneColorPicker = false) {\n    super();\n    this.model = model;\n    this.pixelRatio = pixelRatio;\n    this._register(PixelRatio.getInstance(dom.getWindow(container)).onDidChange(() => this.layout()));\n    this._domNode = $('.colorpicker-widget');\n    container.appendChild(this._domNode);\n    this.header = this._register(new ColorPickerHeader(this._domNode, this.model, themeService, standaloneColorPicker));\n    this.body = this._register(new ColorPickerBody(this._domNode, this.model, this.pixelRatio, standaloneColorPicker));\n  }\n  layout() {\n    this.body.layout();\n  }\n  get domNode() {\n    return this._domNode;\n  }\n}", "map": {"version": 3, "names": ["PixelRatio", "dom", "GlobalPointerMoveMonitor", "Widget", "Codicon", "Color", "HSVA", "RGBA", "Emitter", "Disposable", "ThemeIcon", "localize", "editorHoverBackground", "registerIcon", "$", "ColorPickerHeader", "constructor", "container", "model", "themeService", "showingStandaloneColorPicker", "_close<PERSON><PERSON>on", "_domNode", "append", "_pickedColorNode", "_pickedColorPresentation", "document", "createElement", "classList", "add", "tooltip", "setAttribute", "_originalColorNode", "style", "backgroundColor", "Format", "CSS", "format", "originalColor", "getColorTheme", "getColor", "white", "_register", "onDidColorThemeChange", "theme", "addDisposableListener", "EventType", "CLICK", "selectNextColorPresentation", "color", "flushColor", "onDidChangeColor", "onDidChangePresentation", "toggle", "rgba", "a", "<PERSON><PERSON><PERSON>er", "CloseButton", "closeButton", "pickedColorNode", "originalColorNode", "textContent", "presentation", "label", "_onClicked", "onClicked", "event", "_button", "innerDiv", "asCSSSelector", "close", "fire", "ColorPickerBody", "pixelRatio", "isStandaloneColorPicker", "_insertButton", "_saturationBox", "SaturationBox", "onDidChange", "onDidSaturationValueChange", "onColorFlushed", "_opacityStrip", "OpacityStrip", "onDidOpacityChange", "_hueStrip", "HueStrip", "onDidHueChange", "InsertButton", "s", "v", "hsva", "h", "value", "domNode", "saturationBox", "enterButton", "layout", "_onDidChange", "_onColorFlushed", "_canvas", "className", "selection", "POINTER_DOWN", "e", "onPointerDown", "monitor", "target", "Element", "origin", "getDomNodePagePosition", "onDidChangePosition", "offsetX", "offsetY", "startMonitoring", "pointerId", "buttons", "pageX", "left", "pageY", "top", "pointerUpListener", "ownerDocument", "POINTER_UP", "dispose", "stopMonitoring", "Math", "max", "min", "width", "height", "paintSelection", "offsetWidth", "offsetHeight", "paint", "saturatedColor", "ctx", "getContext", "whiteGradient", "createLinearGradient", "addColorStop", "blackGradient", "rect", "fillStyle", "fill", "isMonitoring", "Strip", "overlay", "slider", "getValue", "updateSliderPosition", "onDidChangeTop", "remove", "r", "g", "b", "opaque", "transparent", "background", "button", "ColorPickerWidget", "standaloneColorPicker", "getInstance", "getWindow", "append<PERSON><PERSON><PERSON>", "header", "body"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/contrib/colorPicker/browser/colorPickerWidget.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { PixelRatio } from '../../../../base/browser/pixelRatio.js';\nimport * as dom from '../../../../base/browser/dom.js';\nimport { GlobalPointerMoveMonitor } from '../../../../base/browser/globalPointerMoveMonitor.js';\nimport { Widget } from '../../../../base/browser/ui/widget.js';\nimport { Codicon } from '../../../../base/common/codicons.js';\nimport { Color, HSVA, RGBA } from '../../../../base/common/color.js';\nimport { Emitter } from '../../../../base/common/event.js';\nimport { Disposable } from '../../../../base/common/lifecycle.js';\nimport { ThemeIcon } from '../../../../base/common/themables.js';\nimport './colorPicker.css';\nimport { localize } from '../../../../nls.js';\nimport { editorHoverBackground } from '../../../../platform/theme/common/colorRegistry.js';\nimport { registerIcon } from '../../../../platform/theme/common/iconRegistry.js';\nconst $ = dom.$;\nexport class ColorPickerHeader extends Disposable {\n    constructor(container, model, themeService, showingStandaloneColorPicker = false) {\n        super();\n        this.model = model;\n        this.showingStandaloneColorPicker = showingStandaloneColorPicker;\n        this._closeButton = null;\n        this._domNode = $('.colorpicker-header');\n        dom.append(container, this._domNode);\n        this._pickedColorNode = dom.append(this._domNode, $('.picked-color'));\n        dom.append(this._pickedColorNode, $('span.codicon.codicon-color-mode'));\n        this._pickedColorPresentation = dom.append(this._pickedColorNode, document.createElement('span'));\n        this._pickedColorPresentation.classList.add('picked-color-presentation');\n        const tooltip = localize('clickToToggleColorOptions', \"Click to toggle color options (rgb/hsl/hex)\");\n        this._pickedColorNode.setAttribute('title', tooltip);\n        this._originalColorNode = dom.append(this._domNode, $('.original-color'));\n        this._originalColorNode.style.backgroundColor = Color.Format.CSS.format(this.model.originalColor) || '';\n        this.backgroundColor = themeService.getColorTheme().getColor(editorHoverBackground) || Color.white;\n        this._register(themeService.onDidColorThemeChange(theme => {\n            this.backgroundColor = theme.getColor(editorHoverBackground) || Color.white;\n        }));\n        this._register(dom.addDisposableListener(this._pickedColorNode, dom.EventType.CLICK, () => this.model.selectNextColorPresentation()));\n        this._register(dom.addDisposableListener(this._originalColorNode, dom.EventType.CLICK, () => {\n            this.model.color = this.model.originalColor;\n            this.model.flushColor();\n        }));\n        this._register(model.onDidChangeColor(this.onDidChangeColor, this));\n        this._register(model.onDidChangePresentation(this.onDidChangePresentation, this));\n        this._pickedColorNode.style.backgroundColor = Color.Format.CSS.format(model.color) || '';\n        this._pickedColorNode.classList.toggle('light', model.color.rgba.a < 0.5 ? this.backgroundColor.isLighter() : model.color.isLighter());\n        this.onDidChangeColor(this.model.color);\n        // When the color picker widget is a standalone color picker widget, then add a close button\n        if (this.showingStandaloneColorPicker) {\n            this._domNode.classList.add('standalone-colorpicker');\n            this._closeButton = this._register(new CloseButton(this._domNode));\n        }\n    }\n    get closeButton() {\n        return this._closeButton;\n    }\n    get pickedColorNode() {\n        return this._pickedColorNode;\n    }\n    get originalColorNode() {\n        return this._originalColorNode;\n    }\n    onDidChangeColor(color) {\n        this._pickedColorNode.style.backgroundColor = Color.Format.CSS.format(color) || '';\n        this._pickedColorNode.classList.toggle('light', color.rgba.a < 0.5 ? this.backgroundColor.isLighter() : color.isLighter());\n        this.onDidChangePresentation();\n    }\n    onDidChangePresentation() {\n        this._pickedColorPresentation.textContent = this.model.presentation ? this.model.presentation.label : '';\n    }\n}\nclass CloseButton extends Disposable {\n    constructor(container) {\n        super();\n        this._onClicked = this._register(new Emitter());\n        this.onClicked = this._onClicked.event;\n        this._button = document.createElement('div');\n        this._button.classList.add('close-button');\n        dom.append(container, this._button);\n        const innerDiv = document.createElement('div');\n        innerDiv.classList.add('close-button-inner-div');\n        dom.append(this._button, innerDiv);\n        const closeButton = dom.append(innerDiv, $('.button' + ThemeIcon.asCSSSelector(registerIcon('color-picker-close', Codicon.close, localize('closeIcon', 'Icon to close the color picker')))));\n        closeButton.classList.add('close-icon');\n        this._register(dom.addDisposableListener(this._button, dom.EventType.CLICK, () => {\n            this._onClicked.fire();\n        }));\n    }\n}\nexport class ColorPickerBody extends Disposable {\n    constructor(container, model, pixelRatio, isStandaloneColorPicker = false) {\n        super();\n        this.model = model;\n        this.pixelRatio = pixelRatio;\n        this._insertButton = null;\n        this._domNode = $('.colorpicker-body');\n        dom.append(container, this._domNode);\n        this._saturationBox = new SaturationBox(this._domNode, this.model, this.pixelRatio);\n        this._register(this._saturationBox);\n        this._register(this._saturationBox.onDidChange(this.onDidSaturationValueChange, this));\n        this._register(this._saturationBox.onColorFlushed(this.flushColor, this));\n        this._opacityStrip = new OpacityStrip(this._domNode, this.model, isStandaloneColorPicker);\n        this._register(this._opacityStrip);\n        this._register(this._opacityStrip.onDidChange(this.onDidOpacityChange, this));\n        this._register(this._opacityStrip.onColorFlushed(this.flushColor, this));\n        this._hueStrip = new HueStrip(this._domNode, this.model, isStandaloneColorPicker);\n        this._register(this._hueStrip);\n        this._register(this._hueStrip.onDidChange(this.onDidHueChange, this));\n        this._register(this._hueStrip.onColorFlushed(this.flushColor, this));\n        if (isStandaloneColorPicker) {\n            this._insertButton = this._register(new InsertButton(this._domNode));\n            this._domNode.classList.add('standalone-colorpicker');\n        }\n    }\n    flushColor() {\n        this.model.flushColor();\n    }\n    onDidSaturationValueChange({ s, v }) {\n        const hsva = this.model.color.hsva;\n        this.model.color = new Color(new HSVA(hsva.h, s, v, hsva.a));\n    }\n    onDidOpacityChange(a) {\n        const hsva = this.model.color.hsva;\n        this.model.color = new Color(new HSVA(hsva.h, hsva.s, hsva.v, a));\n    }\n    onDidHueChange(value) {\n        const hsva = this.model.color.hsva;\n        const h = (1 - value) * 360;\n        this.model.color = new Color(new HSVA(h === 360 ? 0 : h, hsva.s, hsva.v, hsva.a));\n    }\n    get domNode() {\n        return this._domNode;\n    }\n    get saturationBox() {\n        return this._saturationBox;\n    }\n    get enterButton() {\n        return this._insertButton;\n    }\n    layout() {\n        this._saturationBox.layout();\n        this._opacityStrip.layout();\n        this._hueStrip.layout();\n    }\n}\nclass SaturationBox extends Disposable {\n    constructor(container, model, pixelRatio) {\n        super();\n        this.model = model;\n        this.pixelRatio = pixelRatio;\n        this._onDidChange = new Emitter();\n        this.onDidChange = this._onDidChange.event;\n        this._onColorFlushed = new Emitter();\n        this.onColorFlushed = this._onColorFlushed.event;\n        this._domNode = $('.saturation-wrap');\n        dom.append(container, this._domNode);\n        // Create canvas, draw selected color\n        this._canvas = document.createElement('canvas');\n        this._canvas.className = 'saturation-box';\n        dom.append(this._domNode, this._canvas);\n        // Add selection circle\n        this.selection = $('.saturation-selection');\n        dom.append(this._domNode, this.selection);\n        this.layout();\n        this._register(dom.addDisposableListener(this._domNode, dom.EventType.POINTER_DOWN, e => this.onPointerDown(e)));\n        this._register(this.model.onDidChangeColor(this.onDidChangeColor, this));\n        this.monitor = null;\n    }\n    get domNode() {\n        return this._domNode;\n    }\n    onPointerDown(e) {\n        if (!e.target || !(e.target instanceof Element)) {\n            return;\n        }\n        this.monitor = this._register(new GlobalPointerMoveMonitor());\n        const origin = dom.getDomNodePagePosition(this._domNode);\n        if (e.target !== this.selection) {\n            this.onDidChangePosition(e.offsetX, e.offsetY);\n        }\n        this.monitor.startMonitoring(e.target, e.pointerId, e.buttons, event => this.onDidChangePosition(event.pageX - origin.left, event.pageY - origin.top), () => null);\n        const pointerUpListener = dom.addDisposableListener(e.target.ownerDocument, dom.EventType.POINTER_UP, () => {\n            this._onColorFlushed.fire();\n            pointerUpListener.dispose();\n            if (this.monitor) {\n                this.monitor.stopMonitoring(true);\n                this.monitor = null;\n            }\n        }, true);\n    }\n    onDidChangePosition(left, top) {\n        const s = Math.max(0, Math.min(1, left / this.width));\n        const v = Math.max(0, Math.min(1, 1 - (top / this.height)));\n        this.paintSelection(s, v);\n        this._onDidChange.fire({ s, v });\n    }\n    layout() {\n        this.width = this._domNode.offsetWidth;\n        this.height = this._domNode.offsetHeight;\n        this._canvas.width = this.width * this.pixelRatio;\n        this._canvas.height = this.height * this.pixelRatio;\n        this.paint();\n        const hsva = this.model.color.hsva;\n        this.paintSelection(hsva.s, hsva.v);\n    }\n    paint() {\n        const hsva = this.model.color.hsva;\n        const saturatedColor = new Color(new HSVA(hsva.h, 1, 1, 1));\n        const ctx = this._canvas.getContext('2d');\n        const whiteGradient = ctx.createLinearGradient(0, 0, this._canvas.width, 0);\n        whiteGradient.addColorStop(0, 'rgba(255, 255, 255, 1)');\n        whiteGradient.addColorStop(0.5, 'rgba(255, 255, 255, 0.5)');\n        whiteGradient.addColorStop(1, 'rgba(255, 255, 255, 0)');\n        const blackGradient = ctx.createLinearGradient(0, 0, 0, this._canvas.height);\n        blackGradient.addColorStop(0, 'rgba(0, 0, 0, 0)');\n        blackGradient.addColorStop(1, 'rgba(0, 0, 0, 1)');\n        ctx.rect(0, 0, this._canvas.width, this._canvas.height);\n        ctx.fillStyle = Color.Format.CSS.format(saturatedColor);\n        ctx.fill();\n        ctx.fillStyle = whiteGradient;\n        ctx.fill();\n        ctx.fillStyle = blackGradient;\n        ctx.fill();\n    }\n    paintSelection(s, v) {\n        this.selection.style.left = `${s * this.width}px`;\n        this.selection.style.top = `${this.height - v * this.height}px`;\n    }\n    onDidChangeColor(color) {\n        if (this.monitor && this.monitor.isMonitoring()) {\n            return;\n        }\n        this.paint();\n        const hsva = color.hsva;\n        this.paintSelection(hsva.s, hsva.v);\n    }\n}\nclass Strip extends Disposable {\n    constructor(container, model, showingStandaloneColorPicker = false) {\n        super();\n        this.model = model;\n        this._onDidChange = new Emitter();\n        this.onDidChange = this._onDidChange.event;\n        this._onColorFlushed = new Emitter();\n        this.onColorFlushed = this._onColorFlushed.event;\n        if (showingStandaloneColorPicker) {\n            this.domNode = dom.append(container, $('.standalone-strip'));\n            this.overlay = dom.append(this.domNode, $('.standalone-overlay'));\n        }\n        else {\n            this.domNode = dom.append(container, $('.strip'));\n            this.overlay = dom.append(this.domNode, $('.overlay'));\n        }\n        this.slider = dom.append(this.domNode, $('.slider'));\n        this.slider.style.top = `0px`;\n        this._register(dom.addDisposableListener(this.domNode, dom.EventType.POINTER_DOWN, e => this.onPointerDown(e)));\n        this._register(model.onDidChangeColor(this.onDidChangeColor, this));\n        this.layout();\n    }\n    layout() {\n        this.height = this.domNode.offsetHeight - this.slider.offsetHeight;\n        const value = this.getValue(this.model.color);\n        this.updateSliderPosition(value);\n    }\n    onDidChangeColor(color) {\n        const value = this.getValue(color);\n        this.updateSliderPosition(value);\n    }\n    onPointerDown(e) {\n        if (!e.target || !(e.target instanceof Element)) {\n            return;\n        }\n        const monitor = this._register(new GlobalPointerMoveMonitor());\n        const origin = dom.getDomNodePagePosition(this.domNode);\n        this.domNode.classList.add('grabbing');\n        if (e.target !== this.slider) {\n            this.onDidChangeTop(e.offsetY);\n        }\n        monitor.startMonitoring(e.target, e.pointerId, e.buttons, event => this.onDidChangeTop(event.pageY - origin.top), () => null);\n        const pointerUpListener = dom.addDisposableListener(e.target.ownerDocument, dom.EventType.POINTER_UP, () => {\n            this._onColorFlushed.fire();\n            pointerUpListener.dispose();\n            monitor.stopMonitoring(true);\n            this.domNode.classList.remove('grabbing');\n        }, true);\n    }\n    onDidChangeTop(top) {\n        const value = Math.max(0, Math.min(1, 1 - (top / this.height)));\n        this.updateSliderPosition(value);\n        this._onDidChange.fire(value);\n    }\n    updateSliderPosition(value) {\n        this.slider.style.top = `${(1 - value) * this.height}px`;\n    }\n}\nclass OpacityStrip extends Strip {\n    constructor(container, model, showingStandaloneColorPicker = false) {\n        super(container, model, showingStandaloneColorPicker);\n        this.domNode.classList.add('opacity-strip');\n        this.onDidChangeColor(this.model.color);\n    }\n    onDidChangeColor(color) {\n        super.onDidChangeColor(color);\n        const { r, g, b } = color.rgba;\n        const opaque = new Color(new RGBA(r, g, b, 1));\n        const transparent = new Color(new RGBA(r, g, b, 0));\n        this.overlay.style.background = `linear-gradient(to bottom, ${opaque} 0%, ${transparent} 100%)`;\n    }\n    getValue(color) {\n        return color.hsva.a;\n    }\n}\nclass HueStrip extends Strip {\n    constructor(container, model, showingStandaloneColorPicker = false) {\n        super(container, model, showingStandaloneColorPicker);\n        this.domNode.classList.add('hue-strip');\n    }\n    getValue(color) {\n        return 1 - (color.hsva.h / 360);\n    }\n}\nexport class InsertButton extends Disposable {\n    constructor(container) {\n        super();\n        this._onClicked = this._register(new Emitter());\n        this.onClicked = this._onClicked.event;\n        this._button = dom.append(container, document.createElement('button'));\n        this._button.classList.add('insert-button');\n        this._button.textContent = 'Insert';\n        this._register(dom.addDisposableListener(this._button, dom.EventType.CLICK, () => {\n            this._onClicked.fire();\n        }));\n    }\n    get button() {\n        return this._button;\n    }\n}\nexport class ColorPickerWidget extends Widget {\n    constructor(container, model, pixelRatio, themeService, standaloneColorPicker = false) {\n        super();\n        this.model = model;\n        this.pixelRatio = pixelRatio;\n        this._register(PixelRatio.getInstance(dom.getWindow(container)).onDidChange(() => this.layout()));\n        this._domNode = $('.colorpicker-widget');\n        container.appendChild(this._domNode);\n        this.header = this._register(new ColorPickerHeader(this._domNode, this.model, themeService, standaloneColorPicker));\n        this.body = this._register(new ColorPickerBody(this._domNode, this.model, this.pixelRatio, standaloneColorPicker));\n    }\n    layout() {\n        this.body.layout();\n    }\n    get domNode() {\n        return this._domNode;\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,UAAU,QAAQ,wCAAwC;AACnE,OAAO,KAAKC,GAAG,MAAM,iCAAiC;AACtD,SAASC,wBAAwB,QAAQ,sDAAsD;AAC/F,SAASC,MAAM,QAAQ,uCAAuC;AAC9D,SAASC,OAAO,QAAQ,qCAAqC;AAC7D,SAASC,KAAK,EAAEC,IAAI,EAAEC,IAAI,QAAQ,kCAAkC;AACpE,SAASC,OAAO,QAAQ,kCAAkC;AAC1D,SAASC,UAAU,QAAQ,sCAAsC;AACjE,SAASC,SAAS,QAAQ,sCAAsC;AAChE,OAAO,mBAAmB;AAC1B,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,SAASC,qBAAqB,QAAQ,oDAAoD;AAC1F,SAASC,YAAY,QAAQ,mDAAmD;AAChF,MAAMC,CAAC,GAAGb,GAAG,CAACa,CAAC;AACf,OAAO,MAAMC,iBAAiB,SAASN,UAAU,CAAC;EAC9CO,WAAWA,CAACC,SAAS,EAAEC,KAAK,EAAEC,YAAY,EAAEC,4BAA4B,GAAG,KAAK,EAAE;IAC9E,KAAK,CAAC,CAAC;IACP,IAAI,CAACF,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACE,4BAA4B,GAAGA,4BAA4B;IAChE,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,QAAQ,GAAGR,CAAC,CAAC,qBAAqB,CAAC;IACxCb,GAAG,CAACsB,MAAM,CAACN,SAAS,EAAE,IAAI,CAACK,QAAQ,CAAC;IACpC,IAAI,CAACE,gBAAgB,GAAGvB,GAAG,CAACsB,MAAM,CAAC,IAAI,CAACD,QAAQ,EAAER,CAAC,CAAC,eAAe,CAAC,CAAC;IACrEb,GAAG,CAACsB,MAAM,CAAC,IAAI,CAACC,gBAAgB,EAAEV,CAAC,CAAC,iCAAiC,CAAC,CAAC;IACvE,IAAI,CAACW,wBAAwB,GAAGxB,GAAG,CAACsB,MAAM,CAAC,IAAI,CAACC,gBAAgB,EAAEE,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC,CAAC;IACjG,IAAI,CAACF,wBAAwB,CAACG,SAAS,CAACC,GAAG,CAAC,2BAA2B,CAAC;IACxE,MAAMC,OAAO,GAAGnB,QAAQ,CAAC,2BAA2B,EAAE,6CAA6C,CAAC;IACpG,IAAI,CAACa,gBAAgB,CAACO,YAAY,CAAC,OAAO,EAAED,OAAO,CAAC;IACpD,IAAI,CAACE,kBAAkB,GAAG/B,GAAG,CAACsB,MAAM,CAAC,IAAI,CAACD,QAAQ,EAAER,CAAC,CAAC,iBAAiB,CAAC,CAAC;IACzE,IAAI,CAACkB,kBAAkB,CAACC,KAAK,CAACC,eAAe,GAAG7B,KAAK,CAAC8B,MAAM,CAACC,GAAG,CAACC,MAAM,CAAC,IAAI,CAACnB,KAAK,CAACoB,aAAa,CAAC,IAAI,EAAE;IACvG,IAAI,CAACJ,eAAe,GAAGf,YAAY,CAACoB,aAAa,CAAC,CAAC,CAACC,QAAQ,CAAC5B,qBAAqB,CAAC,IAAIP,KAAK,CAACoC,KAAK;IAClG,IAAI,CAACC,SAAS,CAACvB,YAAY,CAACwB,qBAAqB,CAACC,KAAK,IAAI;MACvD,IAAI,CAACV,eAAe,GAAGU,KAAK,CAACJ,QAAQ,CAAC5B,qBAAqB,CAAC,IAAIP,KAAK,CAACoC,KAAK;IAC/E,CAAC,CAAC,CAAC;IACH,IAAI,CAACC,SAAS,CAACzC,GAAG,CAAC4C,qBAAqB,CAAC,IAAI,CAACrB,gBAAgB,EAAEvB,GAAG,CAAC6C,SAAS,CAACC,KAAK,EAAE,MAAM,IAAI,CAAC7B,KAAK,CAAC8B,2BAA2B,CAAC,CAAC,CAAC,CAAC;IACrI,IAAI,CAACN,SAAS,CAACzC,GAAG,CAAC4C,qBAAqB,CAAC,IAAI,CAACb,kBAAkB,EAAE/B,GAAG,CAAC6C,SAAS,CAACC,KAAK,EAAE,MAAM;MACzF,IAAI,CAAC7B,KAAK,CAAC+B,KAAK,GAAG,IAAI,CAAC/B,KAAK,CAACoB,aAAa;MAC3C,IAAI,CAACpB,KAAK,CAACgC,UAAU,CAAC,CAAC;IAC3B,CAAC,CAAC,CAAC;IACH,IAAI,CAACR,SAAS,CAACxB,KAAK,CAACiC,gBAAgB,CAAC,IAAI,CAACA,gBAAgB,EAAE,IAAI,CAAC,CAAC;IACnE,IAAI,CAACT,SAAS,CAACxB,KAAK,CAACkC,uBAAuB,CAAC,IAAI,CAACA,uBAAuB,EAAE,IAAI,CAAC,CAAC;IACjF,IAAI,CAAC5B,gBAAgB,CAACS,KAAK,CAACC,eAAe,GAAG7B,KAAK,CAAC8B,MAAM,CAACC,GAAG,CAACC,MAAM,CAACnB,KAAK,CAAC+B,KAAK,CAAC,IAAI,EAAE;IACxF,IAAI,CAACzB,gBAAgB,CAACI,SAAS,CAACyB,MAAM,CAAC,OAAO,EAAEnC,KAAK,CAAC+B,KAAK,CAACK,IAAI,CAACC,CAAC,GAAG,GAAG,GAAG,IAAI,CAACrB,eAAe,CAACsB,SAAS,CAAC,CAAC,GAAGtC,KAAK,CAAC+B,KAAK,CAACO,SAAS,CAAC,CAAC,CAAC;IACtI,IAAI,CAACL,gBAAgB,CAAC,IAAI,CAACjC,KAAK,CAAC+B,KAAK,CAAC;IACvC;IACA,IAAI,IAAI,CAAC7B,4BAA4B,EAAE;MACnC,IAAI,CAACE,QAAQ,CAACM,SAAS,CAACC,GAAG,CAAC,wBAAwB,CAAC;MACrD,IAAI,CAACR,YAAY,GAAG,IAAI,CAACqB,SAAS,CAAC,IAAIe,WAAW,CAAC,IAAI,CAACnC,QAAQ,CAAC,CAAC;IACtE;EACJ;EACA,IAAIoC,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACrC,YAAY;EAC5B;EACA,IAAIsC,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACnC,gBAAgB;EAChC;EACA,IAAIoC,iBAAiBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAAC5B,kBAAkB;EAClC;EACAmB,gBAAgBA,CAACF,KAAK,EAAE;IACpB,IAAI,CAACzB,gBAAgB,CAACS,KAAK,CAACC,eAAe,GAAG7B,KAAK,CAAC8B,MAAM,CAACC,GAAG,CAACC,MAAM,CAACY,KAAK,CAAC,IAAI,EAAE;IAClF,IAAI,CAACzB,gBAAgB,CAACI,SAAS,CAACyB,MAAM,CAAC,OAAO,EAAEJ,KAAK,CAACK,IAAI,CAACC,CAAC,GAAG,GAAG,GAAG,IAAI,CAACrB,eAAe,CAACsB,SAAS,CAAC,CAAC,GAAGP,KAAK,CAACO,SAAS,CAAC,CAAC,CAAC;IAC1H,IAAI,CAACJ,uBAAuB,CAAC,CAAC;EAClC;EACAA,uBAAuBA,CAAA,EAAG;IACtB,IAAI,CAAC3B,wBAAwB,CAACoC,WAAW,GAAG,IAAI,CAAC3C,KAAK,CAAC4C,YAAY,GAAG,IAAI,CAAC5C,KAAK,CAAC4C,YAAY,CAACC,KAAK,GAAG,EAAE;EAC5G;AACJ;AACA,MAAMN,WAAW,SAAShD,UAAU,CAAC;EACjCO,WAAWA,CAACC,SAAS,EAAE;IACnB,KAAK,CAAC,CAAC;IACP,IAAI,CAAC+C,UAAU,GAAG,IAAI,CAACtB,SAAS,CAAC,IAAIlC,OAAO,CAAC,CAAC,CAAC;IAC/C,IAAI,CAACyD,SAAS,GAAG,IAAI,CAACD,UAAU,CAACE,KAAK;IACtC,IAAI,CAACC,OAAO,GAAGzC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC5C,IAAI,CAACwC,OAAO,CAACvC,SAAS,CAACC,GAAG,CAAC,cAAc,CAAC;IAC1C5B,GAAG,CAACsB,MAAM,CAACN,SAAS,EAAE,IAAI,CAACkD,OAAO,CAAC;IACnC,MAAMC,QAAQ,GAAG1C,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC9CyC,QAAQ,CAACxC,SAAS,CAACC,GAAG,CAAC,wBAAwB,CAAC;IAChD5B,GAAG,CAACsB,MAAM,CAAC,IAAI,CAAC4C,OAAO,EAAEC,QAAQ,CAAC;IAClC,MAAMV,WAAW,GAAGzD,GAAG,CAACsB,MAAM,CAAC6C,QAAQ,EAAEtD,CAAC,CAAC,SAAS,GAAGJ,SAAS,CAAC2D,aAAa,CAACxD,YAAY,CAAC,oBAAoB,EAAET,OAAO,CAACkE,KAAK,EAAE3D,QAAQ,CAAC,WAAW,EAAE,gCAAgC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5L+C,WAAW,CAAC9B,SAAS,CAACC,GAAG,CAAC,YAAY,CAAC;IACvC,IAAI,CAACa,SAAS,CAACzC,GAAG,CAAC4C,qBAAqB,CAAC,IAAI,CAACsB,OAAO,EAAElE,GAAG,CAAC6C,SAAS,CAACC,KAAK,EAAE,MAAM;MAC9E,IAAI,CAACiB,UAAU,CAACO,IAAI,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC;EACP;AACJ;AACA,OAAO,MAAMC,eAAe,SAAS/D,UAAU,CAAC;EAC5CO,WAAWA,CAACC,SAAS,EAAEC,KAAK,EAAEuD,UAAU,EAAEC,uBAAuB,GAAG,KAAK,EAAE;IACvE,KAAK,CAAC,CAAC;IACP,IAAI,CAACxD,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACuD,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACE,aAAa,GAAG,IAAI;IACzB,IAAI,CAACrD,QAAQ,GAAGR,CAAC,CAAC,mBAAmB,CAAC;IACtCb,GAAG,CAACsB,MAAM,CAACN,SAAS,EAAE,IAAI,CAACK,QAAQ,CAAC;IACpC,IAAI,CAACsD,cAAc,GAAG,IAAIC,aAAa,CAAC,IAAI,CAACvD,QAAQ,EAAE,IAAI,CAACJ,KAAK,EAAE,IAAI,CAACuD,UAAU,CAAC;IACnF,IAAI,CAAC/B,SAAS,CAAC,IAAI,CAACkC,cAAc,CAAC;IACnC,IAAI,CAAClC,SAAS,CAAC,IAAI,CAACkC,cAAc,CAACE,WAAW,CAAC,IAAI,CAACC,0BAA0B,EAAE,IAAI,CAAC,CAAC;IACtF,IAAI,CAACrC,SAAS,CAAC,IAAI,CAACkC,cAAc,CAACI,cAAc,CAAC,IAAI,CAAC9B,UAAU,EAAE,IAAI,CAAC,CAAC;IACzE,IAAI,CAAC+B,aAAa,GAAG,IAAIC,YAAY,CAAC,IAAI,CAAC5D,QAAQ,EAAE,IAAI,CAACJ,KAAK,EAAEwD,uBAAuB,CAAC;IACzF,IAAI,CAAChC,SAAS,CAAC,IAAI,CAACuC,aAAa,CAAC;IAClC,IAAI,CAACvC,SAAS,CAAC,IAAI,CAACuC,aAAa,CAACH,WAAW,CAAC,IAAI,CAACK,kBAAkB,EAAE,IAAI,CAAC,CAAC;IAC7E,IAAI,CAACzC,SAAS,CAAC,IAAI,CAACuC,aAAa,CAACD,cAAc,CAAC,IAAI,CAAC9B,UAAU,EAAE,IAAI,CAAC,CAAC;IACxE,IAAI,CAACkC,SAAS,GAAG,IAAIC,QAAQ,CAAC,IAAI,CAAC/D,QAAQ,EAAE,IAAI,CAACJ,KAAK,EAAEwD,uBAAuB,CAAC;IACjF,IAAI,CAAChC,SAAS,CAAC,IAAI,CAAC0C,SAAS,CAAC;IAC9B,IAAI,CAAC1C,SAAS,CAAC,IAAI,CAAC0C,SAAS,CAACN,WAAW,CAAC,IAAI,CAACQ,cAAc,EAAE,IAAI,CAAC,CAAC;IACrE,IAAI,CAAC5C,SAAS,CAAC,IAAI,CAAC0C,SAAS,CAACJ,cAAc,CAAC,IAAI,CAAC9B,UAAU,EAAE,IAAI,CAAC,CAAC;IACpE,IAAIwB,uBAAuB,EAAE;MACzB,IAAI,CAACC,aAAa,GAAG,IAAI,CAACjC,SAAS,CAAC,IAAI6C,YAAY,CAAC,IAAI,CAACjE,QAAQ,CAAC,CAAC;MACpE,IAAI,CAACA,QAAQ,CAACM,SAAS,CAACC,GAAG,CAAC,wBAAwB,CAAC;IACzD;EACJ;EACAqB,UAAUA,CAAA,EAAG;IACT,IAAI,CAAChC,KAAK,CAACgC,UAAU,CAAC,CAAC;EAC3B;EACA6B,0BAA0BA,CAAC;IAAES,CAAC;IAAEC;EAAE,CAAC,EAAE;IACjC,MAAMC,IAAI,GAAG,IAAI,CAACxE,KAAK,CAAC+B,KAAK,CAACyC,IAAI;IAClC,IAAI,CAACxE,KAAK,CAAC+B,KAAK,GAAG,IAAI5C,KAAK,CAAC,IAAIC,IAAI,CAACoF,IAAI,CAACC,CAAC,EAAEH,CAAC,EAAEC,CAAC,EAAEC,IAAI,CAACnC,CAAC,CAAC,CAAC;EAChE;EACA4B,kBAAkBA,CAAC5B,CAAC,EAAE;IAClB,MAAMmC,IAAI,GAAG,IAAI,CAACxE,KAAK,CAAC+B,KAAK,CAACyC,IAAI;IAClC,IAAI,CAACxE,KAAK,CAAC+B,KAAK,GAAG,IAAI5C,KAAK,CAAC,IAAIC,IAAI,CAACoF,IAAI,CAACC,CAAC,EAAED,IAAI,CAACF,CAAC,EAAEE,IAAI,CAACD,CAAC,EAAElC,CAAC,CAAC,CAAC;EACrE;EACA+B,cAAcA,CAACM,KAAK,EAAE;IAClB,MAAMF,IAAI,GAAG,IAAI,CAACxE,KAAK,CAAC+B,KAAK,CAACyC,IAAI;IAClC,MAAMC,CAAC,GAAG,CAAC,CAAC,GAAGC,KAAK,IAAI,GAAG;IAC3B,IAAI,CAAC1E,KAAK,CAAC+B,KAAK,GAAG,IAAI5C,KAAK,CAAC,IAAIC,IAAI,CAACqF,CAAC,KAAK,GAAG,GAAG,CAAC,GAAGA,CAAC,EAAED,IAAI,CAACF,CAAC,EAAEE,IAAI,CAACD,CAAC,EAAEC,IAAI,CAACnC,CAAC,CAAC,CAAC;EACrF;EACA,IAAIsC,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACvE,QAAQ;EACxB;EACA,IAAIwE,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAAClB,cAAc;EAC9B;EACA,IAAImB,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACpB,aAAa;EAC7B;EACAqB,MAAMA,CAAA,EAAG;IACL,IAAI,CAACpB,cAAc,CAACoB,MAAM,CAAC,CAAC;IAC5B,IAAI,CAACf,aAAa,CAACe,MAAM,CAAC,CAAC;IAC3B,IAAI,CAACZ,SAAS,CAACY,MAAM,CAAC,CAAC;EAC3B;AACJ;AACA,MAAMnB,aAAa,SAASpE,UAAU,CAAC;EACnCO,WAAWA,CAACC,SAAS,EAAEC,KAAK,EAAEuD,UAAU,EAAE;IACtC,KAAK,CAAC,CAAC;IACP,IAAI,CAACvD,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACuD,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACwB,YAAY,GAAG,IAAIzF,OAAO,CAAC,CAAC;IACjC,IAAI,CAACsE,WAAW,GAAG,IAAI,CAACmB,YAAY,CAAC/B,KAAK;IAC1C,IAAI,CAACgC,eAAe,GAAG,IAAI1F,OAAO,CAAC,CAAC;IACpC,IAAI,CAACwE,cAAc,GAAG,IAAI,CAACkB,eAAe,CAAChC,KAAK;IAChD,IAAI,CAAC5C,QAAQ,GAAGR,CAAC,CAAC,kBAAkB,CAAC;IACrCb,GAAG,CAACsB,MAAM,CAACN,SAAS,EAAE,IAAI,CAACK,QAAQ,CAAC;IACpC;IACA,IAAI,CAAC6E,OAAO,GAAGzE,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IAC/C,IAAI,CAACwE,OAAO,CAACC,SAAS,GAAG,gBAAgB;IACzCnG,GAAG,CAACsB,MAAM,CAAC,IAAI,CAACD,QAAQ,EAAE,IAAI,CAAC6E,OAAO,CAAC;IACvC;IACA,IAAI,CAACE,SAAS,GAAGvF,CAAC,CAAC,uBAAuB,CAAC;IAC3Cb,GAAG,CAACsB,MAAM,CAAC,IAAI,CAACD,QAAQ,EAAE,IAAI,CAAC+E,SAAS,CAAC;IACzC,IAAI,CAACL,MAAM,CAAC,CAAC;IACb,IAAI,CAACtD,SAAS,CAACzC,GAAG,CAAC4C,qBAAqB,CAAC,IAAI,CAACvB,QAAQ,EAAErB,GAAG,CAAC6C,SAAS,CAACwD,YAAY,EAAEC,CAAC,IAAI,IAAI,CAACC,aAAa,CAACD,CAAC,CAAC,CAAC,CAAC;IAChH,IAAI,CAAC7D,SAAS,CAAC,IAAI,CAACxB,KAAK,CAACiC,gBAAgB,CAAC,IAAI,CAACA,gBAAgB,EAAE,IAAI,CAAC,CAAC;IACxE,IAAI,CAACsD,OAAO,GAAG,IAAI;EACvB;EACA,IAAIZ,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACvE,QAAQ;EACxB;EACAkF,aAAaA,CAACD,CAAC,EAAE;IACb,IAAI,CAACA,CAAC,CAACG,MAAM,IAAI,EAAEH,CAAC,CAACG,MAAM,YAAYC,OAAO,CAAC,EAAE;MAC7C;IACJ;IACA,IAAI,CAACF,OAAO,GAAG,IAAI,CAAC/D,SAAS,CAAC,IAAIxC,wBAAwB,CAAC,CAAC,CAAC;IAC7D,MAAM0G,MAAM,GAAG3G,GAAG,CAAC4G,sBAAsB,CAAC,IAAI,CAACvF,QAAQ,CAAC;IACxD,IAAIiF,CAAC,CAACG,MAAM,KAAK,IAAI,CAACL,SAAS,EAAE;MAC7B,IAAI,CAACS,mBAAmB,CAACP,CAAC,CAACQ,OAAO,EAAER,CAAC,CAACS,OAAO,CAAC;IAClD;IACA,IAAI,CAACP,OAAO,CAACQ,eAAe,CAACV,CAAC,CAACG,MAAM,EAAEH,CAAC,CAACW,SAAS,EAAEX,CAAC,CAACY,OAAO,EAAEjD,KAAK,IAAI,IAAI,CAAC4C,mBAAmB,CAAC5C,KAAK,CAACkD,KAAK,GAAGR,MAAM,CAACS,IAAI,EAAEnD,KAAK,CAACoD,KAAK,GAAGV,MAAM,CAACW,GAAG,CAAC,EAAE,MAAM,IAAI,CAAC;IAClK,MAAMC,iBAAiB,GAAGvH,GAAG,CAAC4C,qBAAqB,CAAC0D,CAAC,CAACG,MAAM,CAACe,aAAa,EAAExH,GAAG,CAAC6C,SAAS,CAAC4E,UAAU,EAAE,MAAM;MACxG,IAAI,CAACxB,eAAe,CAAC3B,IAAI,CAAC,CAAC;MAC3BiD,iBAAiB,CAACG,OAAO,CAAC,CAAC;MAC3B,IAAI,IAAI,CAAClB,OAAO,EAAE;QACd,IAAI,CAACA,OAAO,CAACmB,cAAc,CAAC,IAAI,CAAC;QACjC,IAAI,CAACnB,OAAO,GAAG,IAAI;MACvB;IACJ,CAAC,EAAE,IAAI,CAAC;EACZ;EACAK,mBAAmBA,CAACO,IAAI,EAAEE,GAAG,EAAE;IAC3B,MAAM/B,CAAC,GAAGqC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEV,IAAI,GAAG,IAAI,CAACW,KAAK,CAAC,CAAC;IACrD,MAAMvC,CAAC,GAAGoC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAE,CAAC,GAAIR,GAAG,GAAG,IAAI,CAACU,MAAO,CAAC,CAAC;IAC3D,IAAI,CAACC,cAAc,CAAC1C,CAAC,EAAEC,CAAC,CAAC;IACzB,IAAI,CAACQ,YAAY,CAAC1B,IAAI,CAAC;MAAEiB,CAAC;MAAEC;IAAE,CAAC,CAAC;EACpC;EACAO,MAAMA,CAAA,EAAG;IACL,IAAI,CAACgC,KAAK,GAAG,IAAI,CAAC1G,QAAQ,CAAC6G,WAAW;IACtC,IAAI,CAACF,MAAM,GAAG,IAAI,CAAC3G,QAAQ,CAAC8G,YAAY;IACxC,IAAI,CAACjC,OAAO,CAAC6B,KAAK,GAAG,IAAI,CAACA,KAAK,GAAG,IAAI,CAACvD,UAAU;IACjD,IAAI,CAAC0B,OAAO,CAAC8B,MAAM,GAAG,IAAI,CAACA,MAAM,GAAG,IAAI,CAACxD,UAAU;IACnD,IAAI,CAAC4D,KAAK,CAAC,CAAC;IACZ,MAAM3C,IAAI,GAAG,IAAI,CAACxE,KAAK,CAAC+B,KAAK,CAACyC,IAAI;IAClC,IAAI,CAACwC,cAAc,CAACxC,IAAI,CAACF,CAAC,EAAEE,IAAI,CAACD,CAAC,CAAC;EACvC;EACA4C,KAAKA,CAAA,EAAG;IACJ,MAAM3C,IAAI,GAAG,IAAI,CAACxE,KAAK,CAAC+B,KAAK,CAACyC,IAAI;IAClC,MAAM4C,cAAc,GAAG,IAAIjI,KAAK,CAAC,IAAIC,IAAI,CAACoF,IAAI,CAACC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAC3D,MAAM4C,GAAG,GAAG,IAAI,CAACpC,OAAO,CAACqC,UAAU,CAAC,IAAI,CAAC;IACzC,MAAMC,aAAa,GAAGF,GAAG,CAACG,oBAAoB,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAACvC,OAAO,CAAC6B,KAAK,EAAE,CAAC,CAAC;IAC3ES,aAAa,CAACE,YAAY,CAAC,CAAC,EAAE,wBAAwB,CAAC;IACvDF,aAAa,CAACE,YAAY,CAAC,GAAG,EAAE,0BAA0B,CAAC;IAC3DF,aAAa,CAACE,YAAY,CAAC,CAAC,EAAE,wBAAwB,CAAC;IACvD,MAAMC,aAAa,GAAGL,GAAG,CAACG,oBAAoB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAACvC,OAAO,CAAC8B,MAAM,CAAC;IAC5EW,aAAa,CAACD,YAAY,CAAC,CAAC,EAAE,kBAAkB,CAAC;IACjDC,aAAa,CAACD,YAAY,CAAC,CAAC,EAAE,kBAAkB,CAAC;IACjDJ,GAAG,CAACM,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC1C,OAAO,CAAC6B,KAAK,EAAE,IAAI,CAAC7B,OAAO,CAAC8B,MAAM,CAAC;IACvDM,GAAG,CAACO,SAAS,GAAGzI,KAAK,CAAC8B,MAAM,CAACC,GAAG,CAACC,MAAM,CAACiG,cAAc,CAAC;IACvDC,GAAG,CAACQ,IAAI,CAAC,CAAC;IACVR,GAAG,CAACO,SAAS,GAAGL,aAAa;IAC7BF,GAAG,CAACQ,IAAI,CAAC,CAAC;IACVR,GAAG,CAACO,SAAS,GAAGF,aAAa;IAC7BL,GAAG,CAACQ,IAAI,CAAC,CAAC;EACd;EACAb,cAAcA,CAAC1C,CAAC,EAAEC,CAAC,EAAE;IACjB,IAAI,CAACY,SAAS,CAACpE,KAAK,CAACoF,IAAI,GAAG,GAAG7B,CAAC,GAAG,IAAI,CAACwC,KAAK,IAAI;IACjD,IAAI,CAAC3B,SAAS,CAACpE,KAAK,CAACsF,GAAG,GAAG,GAAG,IAAI,CAACU,MAAM,GAAGxC,CAAC,GAAG,IAAI,CAACwC,MAAM,IAAI;EACnE;EACA9E,gBAAgBA,CAACF,KAAK,EAAE;IACpB,IAAI,IAAI,CAACwD,OAAO,IAAI,IAAI,CAACA,OAAO,CAACuC,YAAY,CAAC,CAAC,EAAE;MAC7C;IACJ;IACA,IAAI,CAACX,KAAK,CAAC,CAAC;IACZ,MAAM3C,IAAI,GAAGzC,KAAK,CAACyC,IAAI;IACvB,IAAI,CAACwC,cAAc,CAACxC,IAAI,CAACF,CAAC,EAAEE,IAAI,CAACD,CAAC,CAAC;EACvC;AACJ;AACA,MAAMwD,KAAK,SAASxI,UAAU,CAAC;EAC3BO,WAAWA,CAACC,SAAS,EAAEC,KAAK,EAAEE,4BAA4B,GAAG,KAAK,EAAE;IAChE,KAAK,CAAC,CAAC;IACP,IAAI,CAACF,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC+E,YAAY,GAAG,IAAIzF,OAAO,CAAC,CAAC;IACjC,IAAI,CAACsE,WAAW,GAAG,IAAI,CAACmB,YAAY,CAAC/B,KAAK;IAC1C,IAAI,CAACgC,eAAe,GAAG,IAAI1F,OAAO,CAAC,CAAC;IACpC,IAAI,CAACwE,cAAc,GAAG,IAAI,CAACkB,eAAe,CAAChC,KAAK;IAChD,IAAI9C,4BAA4B,EAAE;MAC9B,IAAI,CAACyE,OAAO,GAAG5F,GAAG,CAACsB,MAAM,CAACN,SAAS,EAAEH,CAAC,CAAC,mBAAmB,CAAC,CAAC;MAC5D,IAAI,CAACoI,OAAO,GAAGjJ,GAAG,CAACsB,MAAM,CAAC,IAAI,CAACsE,OAAO,EAAE/E,CAAC,CAAC,qBAAqB,CAAC,CAAC;IACrE,CAAC,MACI;MACD,IAAI,CAAC+E,OAAO,GAAG5F,GAAG,CAACsB,MAAM,CAACN,SAAS,EAAEH,CAAC,CAAC,QAAQ,CAAC,CAAC;MACjD,IAAI,CAACoI,OAAO,GAAGjJ,GAAG,CAACsB,MAAM,CAAC,IAAI,CAACsE,OAAO,EAAE/E,CAAC,CAAC,UAAU,CAAC,CAAC;IAC1D;IACA,IAAI,CAACqI,MAAM,GAAGlJ,GAAG,CAACsB,MAAM,CAAC,IAAI,CAACsE,OAAO,EAAE/E,CAAC,CAAC,SAAS,CAAC,CAAC;IACpD,IAAI,CAACqI,MAAM,CAAClH,KAAK,CAACsF,GAAG,GAAG,KAAK;IAC7B,IAAI,CAAC7E,SAAS,CAACzC,GAAG,CAAC4C,qBAAqB,CAAC,IAAI,CAACgD,OAAO,EAAE5F,GAAG,CAAC6C,SAAS,CAACwD,YAAY,EAAEC,CAAC,IAAI,IAAI,CAACC,aAAa,CAACD,CAAC,CAAC,CAAC,CAAC;IAC/G,IAAI,CAAC7D,SAAS,CAACxB,KAAK,CAACiC,gBAAgB,CAAC,IAAI,CAACA,gBAAgB,EAAE,IAAI,CAAC,CAAC;IACnE,IAAI,CAAC6C,MAAM,CAAC,CAAC;EACjB;EACAA,MAAMA,CAAA,EAAG;IACL,IAAI,CAACiC,MAAM,GAAG,IAAI,CAACpC,OAAO,CAACuC,YAAY,GAAG,IAAI,CAACe,MAAM,CAACf,YAAY;IAClE,MAAMxC,KAAK,GAAG,IAAI,CAACwD,QAAQ,CAAC,IAAI,CAAClI,KAAK,CAAC+B,KAAK,CAAC;IAC7C,IAAI,CAACoG,oBAAoB,CAACzD,KAAK,CAAC;EACpC;EACAzC,gBAAgBA,CAACF,KAAK,EAAE;IACpB,MAAM2C,KAAK,GAAG,IAAI,CAACwD,QAAQ,CAACnG,KAAK,CAAC;IAClC,IAAI,CAACoG,oBAAoB,CAACzD,KAAK,CAAC;EACpC;EACAY,aAAaA,CAACD,CAAC,EAAE;IACb,IAAI,CAACA,CAAC,CAACG,MAAM,IAAI,EAAEH,CAAC,CAACG,MAAM,YAAYC,OAAO,CAAC,EAAE;MAC7C;IACJ;IACA,MAAMF,OAAO,GAAG,IAAI,CAAC/D,SAAS,CAAC,IAAIxC,wBAAwB,CAAC,CAAC,CAAC;IAC9D,MAAM0G,MAAM,GAAG3G,GAAG,CAAC4G,sBAAsB,CAAC,IAAI,CAAChB,OAAO,CAAC;IACvD,IAAI,CAACA,OAAO,CAACjE,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC;IACtC,IAAI0E,CAAC,CAACG,MAAM,KAAK,IAAI,CAACyC,MAAM,EAAE;MAC1B,IAAI,CAACG,cAAc,CAAC/C,CAAC,CAACS,OAAO,CAAC;IAClC;IACAP,OAAO,CAACQ,eAAe,CAACV,CAAC,CAACG,MAAM,EAAEH,CAAC,CAACW,SAAS,EAAEX,CAAC,CAACY,OAAO,EAAEjD,KAAK,IAAI,IAAI,CAACoF,cAAc,CAACpF,KAAK,CAACoD,KAAK,GAAGV,MAAM,CAACW,GAAG,CAAC,EAAE,MAAM,IAAI,CAAC;IAC7H,MAAMC,iBAAiB,GAAGvH,GAAG,CAAC4C,qBAAqB,CAAC0D,CAAC,CAACG,MAAM,CAACe,aAAa,EAAExH,GAAG,CAAC6C,SAAS,CAAC4E,UAAU,EAAE,MAAM;MACxG,IAAI,CAACxB,eAAe,CAAC3B,IAAI,CAAC,CAAC;MAC3BiD,iBAAiB,CAACG,OAAO,CAAC,CAAC;MAC3BlB,OAAO,CAACmB,cAAc,CAAC,IAAI,CAAC;MAC5B,IAAI,CAAC/B,OAAO,CAACjE,SAAS,CAAC2H,MAAM,CAAC,UAAU,CAAC;IAC7C,CAAC,EAAE,IAAI,CAAC;EACZ;EACAD,cAAcA,CAAC/B,GAAG,EAAE;IAChB,MAAM3B,KAAK,GAAGiC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAE,CAAC,GAAIR,GAAG,GAAG,IAAI,CAACU,MAAO,CAAC,CAAC;IAC/D,IAAI,CAACoB,oBAAoB,CAACzD,KAAK,CAAC;IAChC,IAAI,CAACK,YAAY,CAAC1B,IAAI,CAACqB,KAAK,CAAC;EACjC;EACAyD,oBAAoBA,CAACzD,KAAK,EAAE;IACxB,IAAI,CAACuD,MAAM,CAAClH,KAAK,CAACsF,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG3B,KAAK,IAAI,IAAI,CAACqC,MAAM,IAAI;EAC5D;AACJ;AACA,MAAM/C,YAAY,SAAS+D,KAAK,CAAC;EAC7BjI,WAAWA,CAACC,SAAS,EAAEC,KAAK,EAAEE,4BAA4B,GAAG,KAAK,EAAE;IAChE,KAAK,CAACH,SAAS,EAAEC,KAAK,EAAEE,4BAA4B,CAAC;IACrD,IAAI,CAACyE,OAAO,CAACjE,SAAS,CAACC,GAAG,CAAC,eAAe,CAAC;IAC3C,IAAI,CAACsB,gBAAgB,CAAC,IAAI,CAACjC,KAAK,CAAC+B,KAAK,CAAC;EAC3C;EACAE,gBAAgBA,CAACF,KAAK,EAAE;IACpB,KAAK,CAACE,gBAAgB,CAACF,KAAK,CAAC;IAC7B,MAAM;MAAEuG,CAAC;MAAEC,CAAC;MAAEC;IAAE,CAAC,GAAGzG,KAAK,CAACK,IAAI;IAC9B,MAAMqG,MAAM,GAAG,IAAItJ,KAAK,CAAC,IAAIE,IAAI,CAACiJ,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC9C,MAAME,WAAW,GAAG,IAAIvJ,KAAK,CAAC,IAAIE,IAAI,CAACiJ,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE,CAAC,CAAC,CAAC;IACnD,IAAI,CAACR,OAAO,CAACjH,KAAK,CAAC4H,UAAU,GAAG,8BAA8BF,MAAM,QAAQC,WAAW,QAAQ;EACnG;EACAR,QAAQA,CAACnG,KAAK,EAAE;IACZ,OAAOA,KAAK,CAACyC,IAAI,CAACnC,CAAC;EACvB;AACJ;AACA,MAAM8B,QAAQ,SAAS4D,KAAK,CAAC;EACzBjI,WAAWA,CAACC,SAAS,EAAEC,KAAK,EAAEE,4BAA4B,GAAG,KAAK,EAAE;IAChE,KAAK,CAACH,SAAS,EAAEC,KAAK,EAAEE,4BAA4B,CAAC;IACrD,IAAI,CAACyE,OAAO,CAACjE,SAAS,CAACC,GAAG,CAAC,WAAW,CAAC;EAC3C;EACAuH,QAAQA,CAACnG,KAAK,EAAE;IACZ,OAAO,CAAC,GAAIA,KAAK,CAACyC,IAAI,CAACC,CAAC,GAAG,GAAI;EACnC;AACJ;AACA,OAAO,MAAMJ,YAAY,SAAS9E,UAAU,CAAC;EACzCO,WAAWA,CAACC,SAAS,EAAE;IACnB,KAAK,CAAC,CAAC;IACP,IAAI,CAAC+C,UAAU,GAAG,IAAI,CAACtB,SAAS,CAAC,IAAIlC,OAAO,CAAC,CAAC,CAAC;IAC/C,IAAI,CAACyD,SAAS,GAAG,IAAI,CAACD,UAAU,CAACE,KAAK;IACtC,IAAI,CAACC,OAAO,GAAGlE,GAAG,CAACsB,MAAM,CAACN,SAAS,EAAES,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC,CAAC;IACtE,IAAI,CAACwC,OAAO,CAACvC,SAAS,CAACC,GAAG,CAAC,eAAe,CAAC;IAC3C,IAAI,CAACsC,OAAO,CAACN,WAAW,GAAG,QAAQ;IACnC,IAAI,CAACnB,SAAS,CAACzC,GAAG,CAAC4C,qBAAqB,CAAC,IAAI,CAACsB,OAAO,EAAElE,GAAG,CAAC6C,SAAS,CAACC,KAAK,EAAE,MAAM;MAC9E,IAAI,CAACiB,UAAU,CAACO,IAAI,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC;EACP;EACA,IAAIuF,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAAC3F,OAAO;EACvB;AACJ;AACA,OAAO,MAAM4F,iBAAiB,SAAS5J,MAAM,CAAC;EAC1Ca,WAAWA,CAACC,SAAS,EAAEC,KAAK,EAAEuD,UAAU,EAAEtD,YAAY,EAAE6I,qBAAqB,GAAG,KAAK,EAAE;IACnF,KAAK,CAAC,CAAC;IACP,IAAI,CAAC9I,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACuD,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAAC/B,SAAS,CAAC1C,UAAU,CAACiK,WAAW,CAAChK,GAAG,CAACiK,SAAS,CAACjJ,SAAS,CAAC,CAAC,CAAC6D,WAAW,CAAC,MAAM,IAAI,CAACkB,MAAM,CAAC,CAAC,CAAC,CAAC;IACjG,IAAI,CAAC1E,QAAQ,GAAGR,CAAC,CAAC,qBAAqB,CAAC;IACxCG,SAAS,CAACkJ,WAAW,CAAC,IAAI,CAAC7I,QAAQ,CAAC;IACpC,IAAI,CAAC8I,MAAM,GAAG,IAAI,CAAC1H,SAAS,CAAC,IAAI3B,iBAAiB,CAAC,IAAI,CAACO,QAAQ,EAAE,IAAI,CAACJ,KAAK,EAAEC,YAAY,EAAE6I,qBAAqB,CAAC,CAAC;IACnH,IAAI,CAACK,IAAI,GAAG,IAAI,CAAC3H,SAAS,CAAC,IAAI8B,eAAe,CAAC,IAAI,CAAClD,QAAQ,EAAE,IAAI,CAACJ,KAAK,EAAE,IAAI,CAACuD,UAAU,EAAEuF,qBAAqB,CAAC,CAAC;EACtH;EACAhE,MAAMA,CAAA,EAAG;IACL,IAAI,CAACqE,IAAI,CAACrE,MAAM,CAAC,CAAC;EACtB;EACA,IAAIH,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACvE,QAAQ;EACxB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}