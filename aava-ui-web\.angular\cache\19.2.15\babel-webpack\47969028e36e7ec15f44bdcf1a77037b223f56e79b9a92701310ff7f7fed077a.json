{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Emitter } from '../../../base/common/event.js';\nimport { Disposable } from '../../../base/common/lifecycle.js';\nimport { regExpLeadsToEndlessLoop } from '../../../base/common/strings.js';\nimport { clearPlatformLanguageAssociations, getLanguageIds, registerPlatformLanguageAssociation } from './languagesAssociations.js';\nimport { ModesRegistry, PLAINTEXT_LANGUAGE_ID } from '../languages/modesRegistry.js';\nimport { Extensions } from '../../../platform/configuration/common/configurationRegistry.js';\nimport { Registry } from '../../../platform/registry/common/platform.js';\nconst hasOwnProperty = Object.prototype.hasOwnProperty;\nconst NULL_LANGUAGE_ID = 'vs.editor.nullLanguage';\nexport class LanguageIdCodec {\n  constructor() {\n    this._languageIdToLanguage = [];\n    this._languageToLanguageId = new Map();\n    this._register(NULL_LANGUAGE_ID, 0 /* LanguageId.Null */);\n    this._register(PLAINTEXT_LANGUAGE_ID, 1 /* LanguageId.PlainText */);\n    this._nextLanguageId = 2;\n  }\n  _register(language, languageId) {\n    this._languageIdToLanguage[languageId] = language;\n    this._languageToLanguageId.set(language, languageId);\n  }\n  register(language) {\n    if (this._languageToLanguageId.has(language)) {\n      return;\n    }\n    const languageId = this._nextLanguageId++;\n    this._register(language, languageId);\n  }\n  encodeLanguageId(languageId) {\n    return this._languageToLanguageId.get(languageId) || 0 /* LanguageId.Null */;\n  }\n  decodeLanguageId(languageId) {\n    return this._languageIdToLanguage[languageId] || NULL_LANGUAGE_ID;\n  }\n}\nexport let LanguagesRegistry = /*#__PURE__*/(() => {\n  class LanguagesRegistry extends Disposable {\n    static {\n      this.instanceCount = 0;\n    }\n    constructor(useModesRegistry = true, warnOnOverwrite = false) {\n      super();\n      this._onDidChange = this._register(new Emitter());\n      this.onDidChange = this._onDidChange.event;\n      LanguagesRegistry.instanceCount++;\n      this._warnOnOverwrite = warnOnOverwrite;\n      this.languageIdCodec = new LanguageIdCodec();\n      this._dynamicLanguages = [];\n      this._languages = {};\n      this._mimeTypesMap = {};\n      this._nameMap = {};\n      this._lowercaseNameMap = {};\n      if (useModesRegistry) {\n        this._initializeFromRegistry();\n        this._register(ModesRegistry.onDidChangeLanguages(m => {\n          this._initializeFromRegistry();\n        }));\n      }\n    }\n    dispose() {\n      LanguagesRegistry.instanceCount--;\n      super.dispose();\n    }\n    _initializeFromRegistry() {\n      this._languages = {};\n      this._mimeTypesMap = {};\n      this._nameMap = {};\n      this._lowercaseNameMap = {};\n      clearPlatformLanguageAssociations();\n      const desc = [].concat(ModesRegistry.getLanguages()).concat(this._dynamicLanguages);\n      this._registerLanguages(desc);\n    }\n    _registerLanguages(desc) {\n      for (const d of desc) {\n        this._registerLanguage(d);\n      }\n      // Rebuild fast path maps\n      this._mimeTypesMap = {};\n      this._nameMap = {};\n      this._lowercaseNameMap = {};\n      Object.keys(this._languages).forEach(langId => {\n        const language = this._languages[langId];\n        if (language.name) {\n          this._nameMap[language.name] = language.identifier;\n        }\n        language.aliases.forEach(alias => {\n          this._lowercaseNameMap[alias.toLowerCase()] = language.identifier;\n        });\n        language.mimetypes.forEach(mimetype => {\n          this._mimeTypesMap[mimetype] = language.identifier;\n        });\n      });\n      Registry.as(Extensions.Configuration).registerOverrideIdentifiers(this.getRegisteredLanguageIds());\n      this._onDidChange.fire();\n    }\n    _registerLanguage(lang) {\n      const langId = lang.id;\n      let resolvedLanguage;\n      if (hasOwnProperty.call(this._languages, langId)) {\n        resolvedLanguage = this._languages[langId];\n      } else {\n        this.languageIdCodec.register(langId);\n        resolvedLanguage = {\n          identifier: langId,\n          name: null,\n          mimetypes: [],\n          aliases: [],\n          extensions: [],\n          filenames: [],\n          configurationFiles: [],\n          icons: []\n        };\n        this._languages[langId] = resolvedLanguage;\n      }\n      this._mergeLanguage(resolvedLanguage, lang);\n    }\n    _mergeLanguage(resolvedLanguage, lang) {\n      const langId = lang.id;\n      let primaryMime = null;\n      if (Array.isArray(lang.mimetypes) && lang.mimetypes.length > 0) {\n        resolvedLanguage.mimetypes.push(...lang.mimetypes);\n        primaryMime = lang.mimetypes[0];\n      }\n      if (!primaryMime) {\n        primaryMime = `text/x-${langId}`;\n        resolvedLanguage.mimetypes.push(primaryMime);\n      }\n      if (Array.isArray(lang.extensions)) {\n        if (lang.configuration) {\n          // insert first as this appears to be the 'primary' language definition\n          resolvedLanguage.extensions = lang.extensions.concat(resolvedLanguage.extensions);\n        } else {\n          resolvedLanguage.extensions = resolvedLanguage.extensions.concat(lang.extensions);\n        }\n        for (const extension of lang.extensions) {\n          registerPlatformLanguageAssociation({\n            id: langId,\n            mime: primaryMime,\n            extension: extension\n          }, this._warnOnOverwrite);\n        }\n      }\n      if (Array.isArray(lang.filenames)) {\n        for (const filename of lang.filenames) {\n          registerPlatformLanguageAssociation({\n            id: langId,\n            mime: primaryMime,\n            filename: filename\n          }, this._warnOnOverwrite);\n          resolvedLanguage.filenames.push(filename);\n        }\n      }\n      if (Array.isArray(lang.filenamePatterns)) {\n        for (const filenamePattern of lang.filenamePatterns) {\n          registerPlatformLanguageAssociation({\n            id: langId,\n            mime: primaryMime,\n            filepattern: filenamePattern\n          }, this._warnOnOverwrite);\n        }\n      }\n      if (typeof lang.firstLine === 'string' && lang.firstLine.length > 0) {\n        let firstLineRegexStr = lang.firstLine;\n        if (firstLineRegexStr.charAt(0) !== '^') {\n          firstLineRegexStr = '^' + firstLineRegexStr;\n        }\n        try {\n          const firstLineRegex = new RegExp(firstLineRegexStr);\n          if (!regExpLeadsToEndlessLoop(firstLineRegex)) {\n            registerPlatformLanguageAssociation({\n              id: langId,\n              mime: primaryMime,\n              firstline: firstLineRegex\n            }, this._warnOnOverwrite);\n          }\n        } catch (err) {\n          // Most likely, the regex was bad\n          console.warn(`[${lang.id}]: Invalid regular expression \\`${firstLineRegexStr}\\`: `, err);\n        }\n      }\n      resolvedLanguage.aliases.push(langId);\n      let langAliases = null;\n      if (typeof lang.aliases !== 'undefined' && Array.isArray(lang.aliases)) {\n        if (lang.aliases.length === 0) {\n          // signal that this language should not get a name\n          langAliases = [null];\n        } else {\n          langAliases = lang.aliases;\n        }\n      }\n      if (langAliases !== null) {\n        for (const langAlias of langAliases) {\n          if (!langAlias || langAlias.length === 0) {\n            continue;\n          }\n          resolvedLanguage.aliases.push(langAlias);\n        }\n      }\n      const containsAliases = langAliases !== null && langAliases.length > 0;\n      if (containsAliases && langAliases[0] === null) {\n        // signal that this language should not get a name\n      } else {\n        const bestName = (containsAliases ? langAliases[0] : null) || langId;\n        if (containsAliases || !resolvedLanguage.name) {\n          resolvedLanguage.name = bestName;\n        }\n      }\n      if (lang.configuration) {\n        resolvedLanguage.configurationFiles.push(lang.configuration);\n      }\n      if (lang.icon) {\n        resolvedLanguage.icons.push(lang.icon);\n      }\n    }\n    isRegisteredLanguageId(languageId) {\n      if (!languageId) {\n        return false;\n      }\n      return hasOwnProperty.call(this._languages, languageId);\n    }\n    getRegisteredLanguageIds() {\n      return Object.keys(this._languages);\n    }\n    getLanguageIdByLanguageName(languageName) {\n      const languageNameLower = languageName.toLowerCase();\n      if (!hasOwnProperty.call(this._lowercaseNameMap, languageNameLower)) {\n        return null;\n      }\n      return this._lowercaseNameMap[languageNameLower];\n    }\n    getLanguageIdByMimeType(mimeType) {\n      if (!mimeType) {\n        return null;\n      }\n      if (hasOwnProperty.call(this._mimeTypesMap, mimeType)) {\n        return this._mimeTypesMap[mimeType];\n      }\n      return null;\n    }\n    guessLanguageIdByFilepathOrFirstLine(resource, firstLine) {\n      if (!resource && !firstLine) {\n        return [];\n      }\n      return getLanguageIds(resource, firstLine);\n    }\n  }\n  return LanguagesRegistry;\n})();", "map": {"version": 3, "names": ["Emitter", "Disposable", "regExpLeadsToEndlessLoop", "clearPlatformLanguageAssociations", "getLanguageIds", "registerPlatformLanguageAssociation", "ModesRegistry", "PLAINTEXT_LANGUAGE_ID", "Extensions", "Registry", "hasOwnProperty", "Object", "prototype", "NULL_LANGUAGE_ID", "LanguageIdCodec", "constructor", "_languageIdToLanguage", "_languageToLanguageId", "Map", "_register", "_nextLanguageId", "language", "languageId", "set", "register", "has", "encodeLanguageId", "get", "decodeLanguageId", "LanguagesRegistry", "instanceCount", "useModesRegistry", "warnOnOverwrite", "_onDidChange", "onDidChange", "event", "_warnOnOverwrite", "languageIdCodec", "_dynamicLanguages", "_languages", "_mimeTypesMap", "_nameMap", "_lowercaseNameMap", "_initializeFromRegistry", "onDidChangeLanguages", "m", "dispose", "desc", "concat", "getLanguages", "_registerLanguages", "d", "_registerLanguage", "keys", "for<PERSON>ach", "langId", "name", "identifier", "aliases", "alias", "toLowerCase", "mimetypes", "mimetype", "as", "Configuration", "registerOverrideIdentifiers", "getRegisteredLanguageIds", "fire", "lang", "id", "resolvedLanguage", "call", "extensions", "filenames", "configurationFiles", "icons", "_mergeLanguage", "primaryMime", "Array", "isArray", "length", "push", "configuration", "extension", "mime", "filename", "filenamePatterns", "filenamePattern", "filepattern", "firstLine", "firstLineRegexStr", "char<PERSON>t", "firstLineRegex", "RegExp", "firstline", "err", "console", "warn", "lang<PERSON><PERSON><PERSON>", "<PERSON>ng<PERSON><PERSON><PERSON>", "containsAliases", "bestName", "icon", "isRegisteredLanguageId", "getLanguageIdByLanguageName", "languageName", "languageNameLower", "getLanguageIdByMimeType", "mimeType", "guessLanguageIdByFilepathOrFirstLine", "resource"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/services/languagesRegistry.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Emitter } from '../../../base/common/event.js';\nimport { Disposable } from '../../../base/common/lifecycle.js';\nimport { regExpLeadsToEndlessLoop } from '../../../base/common/strings.js';\nimport { clearPlatformLanguageAssociations, getLanguageIds, registerPlatformLanguageAssociation } from './languagesAssociations.js';\nimport { ModesRegistry, PLAINTEXT_LANGUAGE_ID } from '../languages/modesRegistry.js';\nimport { Extensions } from '../../../platform/configuration/common/configurationRegistry.js';\nimport { Registry } from '../../../platform/registry/common/platform.js';\nconst hasOwnProperty = Object.prototype.hasOwnProperty;\nconst NULL_LANGUAGE_ID = 'vs.editor.nullLanguage';\nexport class LanguageIdCodec {\n    constructor() {\n        this._languageIdToLanguage = [];\n        this._languageToLanguageId = new Map();\n        this._register(NULL_LANGUAGE_ID, 0 /* LanguageId.Null */);\n        this._register(PLAINTEXT_LANGUAGE_ID, 1 /* LanguageId.PlainText */);\n        this._nextLanguageId = 2;\n    }\n    _register(language, languageId) {\n        this._languageIdToLanguage[languageId] = language;\n        this._languageToLanguageId.set(language, languageId);\n    }\n    register(language) {\n        if (this._languageToLanguageId.has(language)) {\n            return;\n        }\n        const languageId = this._nextLanguageId++;\n        this._register(language, languageId);\n    }\n    encodeLanguageId(languageId) {\n        return this._languageToLanguageId.get(languageId) || 0 /* LanguageId.Null */;\n    }\n    decodeLanguageId(languageId) {\n        return this._languageIdToLanguage[languageId] || NULL_LANGUAGE_ID;\n    }\n}\nexport class LanguagesRegistry extends Disposable {\n    static { this.instanceCount = 0; }\n    constructor(useModesRegistry = true, warnOnOverwrite = false) {\n        super();\n        this._onDidChange = this._register(new Emitter());\n        this.onDidChange = this._onDidChange.event;\n        LanguagesRegistry.instanceCount++;\n        this._warnOnOverwrite = warnOnOverwrite;\n        this.languageIdCodec = new LanguageIdCodec();\n        this._dynamicLanguages = [];\n        this._languages = {};\n        this._mimeTypesMap = {};\n        this._nameMap = {};\n        this._lowercaseNameMap = {};\n        if (useModesRegistry) {\n            this._initializeFromRegistry();\n            this._register(ModesRegistry.onDidChangeLanguages((m) => {\n                this._initializeFromRegistry();\n            }));\n        }\n    }\n    dispose() {\n        LanguagesRegistry.instanceCount--;\n        super.dispose();\n    }\n    _initializeFromRegistry() {\n        this._languages = {};\n        this._mimeTypesMap = {};\n        this._nameMap = {};\n        this._lowercaseNameMap = {};\n        clearPlatformLanguageAssociations();\n        const desc = [].concat(ModesRegistry.getLanguages()).concat(this._dynamicLanguages);\n        this._registerLanguages(desc);\n    }\n    _registerLanguages(desc) {\n        for (const d of desc) {\n            this._registerLanguage(d);\n        }\n        // Rebuild fast path maps\n        this._mimeTypesMap = {};\n        this._nameMap = {};\n        this._lowercaseNameMap = {};\n        Object.keys(this._languages).forEach((langId) => {\n            const language = this._languages[langId];\n            if (language.name) {\n                this._nameMap[language.name] = language.identifier;\n            }\n            language.aliases.forEach((alias) => {\n                this._lowercaseNameMap[alias.toLowerCase()] = language.identifier;\n            });\n            language.mimetypes.forEach((mimetype) => {\n                this._mimeTypesMap[mimetype] = language.identifier;\n            });\n        });\n        Registry.as(Extensions.Configuration).registerOverrideIdentifiers(this.getRegisteredLanguageIds());\n        this._onDidChange.fire();\n    }\n    _registerLanguage(lang) {\n        const langId = lang.id;\n        let resolvedLanguage;\n        if (hasOwnProperty.call(this._languages, langId)) {\n            resolvedLanguage = this._languages[langId];\n        }\n        else {\n            this.languageIdCodec.register(langId);\n            resolvedLanguage = {\n                identifier: langId,\n                name: null,\n                mimetypes: [],\n                aliases: [],\n                extensions: [],\n                filenames: [],\n                configurationFiles: [],\n                icons: []\n            };\n            this._languages[langId] = resolvedLanguage;\n        }\n        this._mergeLanguage(resolvedLanguage, lang);\n    }\n    _mergeLanguage(resolvedLanguage, lang) {\n        const langId = lang.id;\n        let primaryMime = null;\n        if (Array.isArray(lang.mimetypes) && lang.mimetypes.length > 0) {\n            resolvedLanguage.mimetypes.push(...lang.mimetypes);\n            primaryMime = lang.mimetypes[0];\n        }\n        if (!primaryMime) {\n            primaryMime = `text/x-${langId}`;\n            resolvedLanguage.mimetypes.push(primaryMime);\n        }\n        if (Array.isArray(lang.extensions)) {\n            if (lang.configuration) {\n                // insert first as this appears to be the 'primary' language definition\n                resolvedLanguage.extensions = lang.extensions.concat(resolvedLanguage.extensions);\n            }\n            else {\n                resolvedLanguage.extensions = resolvedLanguage.extensions.concat(lang.extensions);\n            }\n            for (const extension of lang.extensions) {\n                registerPlatformLanguageAssociation({ id: langId, mime: primaryMime, extension: extension }, this._warnOnOverwrite);\n            }\n        }\n        if (Array.isArray(lang.filenames)) {\n            for (const filename of lang.filenames) {\n                registerPlatformLanguageAssociation({ id: langId, mime: primaryMime, filename: filename }, this._warnOnOverwrite);\n                resolvedLanguage.filenames.push(filename);\n            }\n        }\n        if (Array.isArray(lang.filenamePatterns)) {\n            for (const filenamePattern of lang.filenamePatterns) {\n                registerPlatformLanguageAssociation({ id: langId, mime: primaryMime, filepattern: filenamePattern }, this._warnOnOverwrite);\n            }\n        }\n        if (typeof lang.firstLine === 'string' && lang.firstLine.length > 0) {\n            let firstLineRegexStr = lang.firstLine;\n            if (firstLineRegexStr.charAt(0) !== '^') {\n                firstLineRegexStr = '^' + firstLineRegexStr;\n            }\n            try {\n                const firstLineRegex = new RegExp(firstLineRegexStr);\n                if (!regExpLeadsToEndlessLoop(firstLineRegex)) {\n                    registerPlatformLanguageAssociation({ id: langId, mime: primaryMime, firstline: firstLineRegex }, this._warnOnOverwrite);\n                }\n            }\n            catch (err) {\n                // Most likely, the regex was bad\n                console.warn(`[${lang.id}]: Invalid regular expression \\`${firstLineRegexStr}\\`: `, err);\n            }\n        }\n        resolvedLanguage.aliases.push(langId);\n        let langAliases = null;\n        if (typeof lang.aliases !== 'undefined' && Array.isArray(lang.aliases)) {\n            if (lang.aliases.length === 0) {\n                // signal that this language should not get a name\n                langAliases = [null];\n            }\n            else {\n                langAliases = lang.aliases;\n            }\n        }\n        if (langAliases !== null) {\n            for (const langAlias of langAliases) {\n                if (!langAlias || langAlias.length === 0) {\n                    continue;\n                }\n                resolvedLanguage.aliases.push(langAlias);\n            }\n        }\n        const containsAliases = (langAliases !== null && langAliases.length > 0);\n        if (containsAliases && langAliases[0] === null) {\n            // signal that this language should not get a name\n        }\n        else {\n            const bestName = (containsAliases ? langAliases[0] : null) || langId;\n            if (containsAliases || !resolvedLanguage.name) {\n                resolvedLanguage.name = bestName;\n            }\n        }\n        if (lang.configuration) {\n            resolvedLanguage.configurationFiles.push(lang.configuration);\n        }\n        if (lang.icon) {\n            resolvedLanguage.icons.push(lang.icon);\n        }\n    }\n    isRegisteredLanguageId(languageId) {\n        if (!languageId) {\n            return false;\n        }\n        return hasOwnProperty.call(this._languages, languageId);\n    }\n    getRegisteredLanguageIds() {\n        return Object.keys(this._languages);\n    }\n    getLanguageIdByLanguageName(languageName) {\n        const languageNameLower = languageName.toLowerCase();\n        if (!hasOwnProperty.call(this._lowercaseNameMap, languageNameLower)) {\n            return null;\n        }\n        return this._lowercaseNameMap[languageNameLower];\n    }\n    getLanguageIdByMimeType(mimeType) {\n        if (!mimeType) {\n            return null;\n        }\n        if (hasOwnProperty.call(this._mimeTypesMap, mimeType)) {\n            return this._mimeTypesMap[mimeType];\n        }\n        return null;\n    }\n    guessLanguageIdByFilepathOrFirstLine(resource, firstLine) {\n        if (!resource && !firstLine) {\n            return [];\n        }\n        return getLanguageIds(resource, firstLine);\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,OAAO,QAAQ,+BAA+B;AACvD,SAASC,UAAU,QAAQ,mCAAmC;AAC9D,SAASC,wBAAwB,QAAQ,iCAAiC;AAC1E,SAASC,iCAAiC,EAAEC,cAAc,EAAEC,mCAAmC,QAAQ,4BAA4B;AACnI,SAASC,aAAa,EAAEC,qBAAqB,QAAQ,+BAA+B;AACpF,SAASC,UAAU,QAAQ,iEAAiE;AAC5F,SAASC,QAAQ,QAAQ,+CAA+C;AACxE,MAAMC,cAAc,GAAGC,MAAM,CAACC,SAAS,CAACF,cAAc;AACtD,MAAMG,gBAAgB,GAAG,wBAAwB;AACjD,OAAO,MAAMC,eAAe,CAAC;EACzBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,qBAAqB,GAAG,EAAE;IAC/B,IAAI,CAACC,qBAAqB,GAAG,IAAIC,GAAG,CAAC,CAAC;IACtC,IAAI,CAACC,SAAS,CAACN,gBAAgB,EAAE,CAAC,CAAC,qBAAqB,CAAC;IACzD,IAAI,CAACM,SAAS,CAACZ,qBAAqB,EAAE,CAAC,CAAC,0BAA0B,CAAC;IACnE,IAAI,CAACa,eAAe,GAAG,CAAC;EAC5B;EACAD,SAASA,CAACE,QAAQ,EAAEC,UAAU,EAAE;IAC5B,IAAI,CAACN,qBAAqB,CAACM,UAAU,CAAC,GAAGD,QAAQ;IACjD,IAAI,CAACJ,qBAAqB,CAACM,GAAG,CAACF,QAAQ,EAAEC,UAAU,CAAC;EACxD;EACAE,QAAQA,CAACH,QAAQ,EAAE;IACf,IAAI,IAAI,CAACJ,qBAAqB,CAACQ,GAAG,CAACJ,QAAQ,CAAC,EAAE;MAC1C;IACJ;IACA,MAAMC,UAAU,GAAG,IAAI,CAACF,eAAe,EAAE;IACzC,IAAI,CAACD,SAAS,CAACE,QAAQ,EAAEC,UAAU,CAAC;EACxC;EACAI,gBAAgBA,CAACJ,UAAU,EAAE;IACzB,OAAO,IAAI,CAACL,qBAAqB,CAACU,GAAG,CAACL,UAAU,CAAC,IAAI,CAAC,CAAC;EAC3D;EACAM,gBAAgBA,CAACN,UAAU,EAAE;IACzB,OAAO,IAAI,CAACN,qBAAqB,CAACM,UAAU,CAAC,IAAIT,gBAAgB;EACrE;AACJ;AACA,WAAagB,iBAAiB;EAAvB,MAAMA,iBAAiB,SAAS5B,UAAU,CAAC;IAC9C;MAAS,IAAI,CAAC6B,aAAa,GAAG,CAAC;IAAE;IACjCf,WAAWA,CAACgB,gBAAgB,GAAG,IAAI,EAAEC,eAAe,GAAG,KAAK,EAAE;MAC1D,KAAK,CAAC,CAAC;MACP,IAAI,CAACC,YAAY,GAAG,IAAI,CAACd,SAAS,CAAC,IAAInB,OAAO,CAAC,CAAC,CAAC;MACjD,IAAI,CAACkC,WAAW,GAAG,IAAI,CAACD,YAAY,CAACE,KAAK;MAC1CN,iBAAiB,CAACC,aAAa,EAAE;MACjC,IAAI,CAACM,gBAAgB,GAAGJ,eAAe;MACvC,IAAI,CAACK,eAAe,GAAG,IAAIvB,eAAe,CAAC,CAAC;MAC5C,IAAI,CAACwB,iBAAiB,GAAG,EAAE;MAC3B,IAAI,CAACC,UAAU,GAAG,CAAC,CAAC;MACpB,IAAI,CAACC,aAAa,GAAG,CAAC,CAAC;MACvB,IAAI,CAACC,QAAQ,GAAG,CAAC,CAAC;MAClB,IAAI,CAACC,iBAAiB,GAAG,CAAC,CAAC;MAC3B,IAAIX,gBAAgB,EAAE;QAClB,IAAI,CAACY,uBAAuB,CAAC,CAAC;QAC9B,IAAI,CAACxB,SAAS,CAACb,aAAa,CAACsC,oBAAoB,CAAEC,CAAC,IAAK;UACrD,IAAI,CAACF,uBAAuB,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;MACP;IACJ;IACAG,OAAOA,CAAA,EAAG;MACNjB,iBAAiB,CAACC,aAAa,EAAE;MACjC,KAAK,CAACgB,OAAO,CAAC,CAAC;IACnB;IACAH,uBAAuBA,CAAA,EAAG;MACtB,IAAI,CAACJ,UAAU,GAAG,CAAC,CAAC;MACpB,IAAI,CAACC,aAAa,GAAG,CAAC,CAAC;MACvB,IAAI,CAACC,QAAQ,GAAG,CAAC,CAAC;MAClB,IAAI,CAACC,iBAAiB,GAAG,CAAC,CAAC;MAC3BvC,iCAAiC,CAAC,CAAC;MACnC,MAAM4C,IAAI,GAAG,EAAE,CAACC,MAAM,CAAC1C,aAAa,CAAC2C,YAAY,CAAC,CAAC,CAAC,CAACD,MAAM,CAAC,IAAI,CAACV,iBAAiB,CAAC;MACnF,IAAI,CAACY,kBAAkB,CAACH,IAAI,CAAC;IACjC;IACAG,kBAAkBA,CAACH,IAAI,EAAE;MACrB,KAAK,MAAMI,CAAC,IAAIJ,IAAI,EAAE;QAClB,IAAI,CAACK,iBAAiB,CAACD,CAAC,CAAC;MAC7B;MACA;MACA,IAAI,CAACX,aAAa,GAAG,CAAC,CAAC;MACvB,IAAI,CAACC,QAAQ,GAAG,CAAC,CAAC;MAClB,IAAI,CAACC,iBAAiB,GAAG,CAAC,CAAC;MAC3B/B,MAAM,CAAC0C,IAAI,CAAC,IAAI,CAACd,UAAU,CAAC,CAACe,OAAO,CAAEC,MAAM,IAAK;QAC7C,MAAMlC,QAAQ,GAAG,IAAI,CAACkB,UAAU,CAACgB,MAAM,CAAC;QACxC,IAAIlC,QAAQ,CAACmC,IAAI,EAAE;UACf,IAAI,CAACf,QAAQ,CAACpB,QAAQ,CAACmC,IAAI,CAAC,GAAGnC,QAAQ,CAACoC,UAAU;QACtD;QACApC,QAAQ,CAACqC,OAAO,CAACJ,OAAO,CAAEK,KAAK,IAAK;UAChC,IAAI,CAACjB,iBAAiB,CAACiB,KAAK,CAACC,WAAW,CAAC,CAAC,CAAC,GAAGvC,QAAQ,CAACoC,UAAU;QACrE,CAAC,CAAC;QACFpC,QAAQ,CAACwC,SAAS,CAACP,OAAO,CAAEQ,QAAQ,IAAK;UACrC,IAAI,CAACtB,aAAa,CAACsB,QAAQ,CAAC,GAAGzC,QAAQ,CAACoC,UAAU;QACtD,CAAC,CAAC;MACN,CAAC,CAAC;MACFhD,QAAQ,CAACsD,EAAE,CAACvD,UAAU,CAACwD,aAAa,CAAC,CAACC,2BAA2B,CAAC,IAAI,CAACC,wBAAwB,CAAC,CAAC,CAAC;MAClG,IAAI,CAACjC,YAAY,CAACkC,IAAI,CAAC,CAAC;IAC5B;IACAf,iBAAiBA,CAACgB,IAAI,EAAE;MACpB,MAAMb,MAAM,GAAGa,IAAI,CAACC,EAAE;MACtB,IAAIC,gBAAgB;MACpB,IAAI5D,cAAc,CAAC6D,IAAI,CAAC,IAAI,CAAChC,UAAU,EAAEgB,MAAM,CAAC,EAAE;QAC9Ce,gBAAgB,GAAG,IAAI,CAAC/B,UAAU,CAACgB,MAAM,CAAC;MAC9C,CAAC,MACI;QACD,IAAI,CAAClB,eAAe,CAACb,QAAQ,CAAC+B,MAAM,CAAC;QACrCe,gBAAgB,GAAG;UACfb,UAAU,EAAEF,MAAM;UAClBC,IAAI,EAAE,IAAI;UACVK,SAAS,EAAE,EAAE;UACbH,OAAO,EAAE,EAAE;UACXc,UAAU,EAAE,EAAE;UACdC,SAAS,EAAE,EAAE;UACbC,kBAAkB,EAAE,EAAE;UACtBC,KAAK,EAAE;QACX,CAAC;QACD,IAAI,CAACpC,UAAU,CAACgB,MAAM,CAAC,GAAGe,gBAAgB;MAC9C;MACA,IAAI,CAACM,cAAc,CAACN,gBAAgB,EAAEF,IAAI,CAAC;IAC/C;IACAQ,cAAcA,CAACN,gBAAgB,EAAEF,IAAI,EAAE;MACnC,MAAMb,MAAM,GAAGa,IAAI,CAACC,EAAE;MACtB,IAAIQ,WAAW,GAAG,IAAI;MACtB,IAAIC,KAAK,CAACC,OAAO,CAACX,IAAI,CAACP,SAAS,CAAC,IAAIO,IAAI,CAACP,SAAS,CAACmB,MAAM,GAAG,CAAC,EAAE;QAC5DV,gBAAgB,CAACT,SAAS,CAACoB,IAAI,CAAC,GAAGb,IAAI,CAACP,SAAS,CAAC;QAClDgB,WAAW,GAAGT,IAAI,CAACP,SAAS,CAAC,CAAC,CAAC;MACnC;MACA,IAAI,CAACgB,WAAW,EAAE;QACdA,WAAW,GAAG,UAAUtB,MAAM,EAAE;QAChCe,gBAAgB,CAACT,SAAS,CAACoB,IAAI,CAACJ,WAAW,CAAC;MAChD;MACA,IAAIC,KAAK,CAACC,OAAO,CAACX,IAAI,CAACI,UAAU,CAAC,EAAE;QAChC,IAAIJ,IAAI,CAACc,aAAa,EAAE;UACpB;UACAZ,gBAAgB,CAACE,UAAU,GAAGJ,IAAI,CAACI,UAAU,CAACxB,MAAM,CAACsB,gBAAgB,CAACE,UAAU,CAAC;QACrF,CAAC,MACI;UACDF,gBAAgB,CAACE,UAAU,GAAGF,gBAAgB,CAACE,UAAU,CAACxB,MAAM,CAACoB,IAAI,CAACI,UAAU,CAAC;QACrF;QACA,KAAK,MAAMW,SAAS,IAAIf,IAAI,CAACI,UAAU,EAAE;UACrCnE,mCAAmC,CAAC;YAAEgE,EAAE,EAAEd,MAAM;YAAE6B,IAAI,EAAEP,WAAW;YAAEM,SAAS,EAAEA;UAAU,CAAC,EAAE,IAAI,CAAC/C,gBAAgB,CAAC;QACvH;MACJ;MACA,IAAI0C,KAAK,CAACC,OAAO,CAACX,IAAI,CAACK,SAAS,CAAC,EAAE;QAC/B,KAAK,MAAMY,QAAQ,IAAIjB,IAAI,CAACK,SAAS,EAAE;UACnCpE,mCAAmC,CAAC;YAAEgE,EAAE,EAAEd,MAAM;YAAE6B,IAAI,EAAEP,WAAW;YAAEQ,QAAQ,EAAEA;UAAS,CAAC,EAAE,IAAI,CAACjD,gBAAgB,CAAC;UACjHkC,gBAAgB,CAACG,SAAS,CAACQ,IAAI,CAACI,QAAQ,CAAC;QAC7C;MACJ;MACA,IAAIP,KAAK,CAACC,OAAO,CAACX,IAAI,CAACkB,gBAAgB,CAAC,EAAE;QACtC,KAAK,MAAMC,eAAe,IAAInB,IAAI,CAACkB,gBAAgB,EAAE;UACjDjF,mCAAmC,CAAC;YAAEgE,EAAE,EAAEd,MAAM;YAAE6B,IAAI,EAAEP,WAAW;YAAEW,WAAW,EAAED;UAAgB,CAAC,EAAE,IAAI,CAACnD,gBAAgB,CAAC;QAC/H;MACJ;MACA,IAAI,OAAOgC,IAAI,CAACqB,SAAS,KAAK,QAAQ,IAAIrB,IAAI,CAACqB,SAAS,CAACT,MAAM,GAAG,CAAC,EAAE;QACjE,IAAIU,iBAAiB,GAAGtB,IAAI,CAACqB,SAAS;QACtC,IAAIC,iBAAiB,CAACC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;UACrCD,iBAAiB,GAAG,GAAG,GAAGA,iBAAiB;QAC/C;QACA,IAAI;UACA,MAAME,cAAc,GAAG,IAAIC,MAAM,CAACH,iBAAiB,CAAC;UACpD,IAAI,CAACxF,wBAAwB,CAAC0F,cAAc,CAAC,EAAE;YAC3CvF,mCAAmC,CAAC;cAAEgE,EAAE,EAAEd,MAAM;cAAE6B,IAAI,EAAEP,WAAW;cAAEiB,SAAS,EAAEF;YAAe,CAAC,EAAE,IAAI,CAACxD,gBAAgB,CAAC;UAC5H;QACJ,CAAC,CACD,OAAO2D,GAAG,EAAE;UACR;UACAC,OAAO,CAACC,IAAI,CAAC,IAAI7B,IAAI,CAACC,EAAE,mCAAmCqB,iBAAiB,MAAM,EAAEK,GAAG,CAAC;QAC5F;MACJ;MACAzB,gBAAgB,CAACZ,OAAO,CAACuB,IAAI,CAAC1B,MAAM,CAAC;MACrC,IAAI2C,WAAW,GAAG,IAAI;MACtB,IAAI,OAAO9B,IAAI,CAACV,OAAO,KAAK,WAAW,IAAIoB,KAAK,CAACC,OAAO,CAACX,IAAI,CAACV,OAAO,CAAC,EAAE;QACpE,IAAIU,IAAI,CAACV,OAAO,CAACsB,MAAM,KAAK,CAAC,EAAE;UAC3B;UACAkB,WAAW,GAAG,CAAC,IAAI,CAAC;QACxB,CAAC,MACI;UACDA,WAAW,GAAG9B,IAAI,CAACV,OAAO;QAC9B;MACJ;MACA,IAAIwC,WAAW,KAAK,IAAI,EAAE;QACtB,KAAK,MAAMC,SAAS,IAAID,WAAW,EAAE;UACjC,IAAI,CAACC,SAAS,IAAIA,SAAS,CAACnB,MAAM,KAAK,CAAC,EAAE;YACtC;UACJ;UACAV,gBAAgB,CAACZ,OAAO,CAACuB,IAAI,CAACkB,SAAS,CAAC;QAC5C;MACJ;MACA,MAAMC,eAAe,GAAIF,WAAW,KAAK,IAAI,IAAIA,WAAW,CAAClB,MAAM,GAAG,CAAE;MACxE,IAAIoB,eAAe,IAAIF,WAAW,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;QAC5C;MAAA,CACH,MACI;QACD,MAAMG,QAAQ,GAAG,CAACD,eAAe,GAAGF,WAAW,CAAC,CAAC,CAAC,GAAG,IAAI,KAAK3C,MAAM;QACpE,IAAI6C,eAAe,IAAI,CAAC9B,gBAAgB,CAACd,IAAI,EAAE;UAC3Cc,gBAAgB,CAACd,IAAI,GAAG6C,QAAQ;QACpC;MACJ;MACA,IAAIjC,IAAI,CAACc,aAAa,EAAE;QACpBZ,gBAAgB,CAACI,kBAAkB,CAACO,IAAI,CAACb,IAAI,CAACc,aAAa,CAAC;MAChE;MACA,IAAId,IAAI,CAACkC,IAAI,EAAE;QACXhC,gBAAgB,CAACK,KAAK,CAACM,IAAI,CAACb,IAAI,CAACkC,IAAI,CAAC;MAC1C;IACJ;IACAC,sBAAsBA,CAACjF,UAAU,EAAE;MAC/B,IAAI,CAACA,UAAU,EAAE;QACb,OAAO,KAAK;MAChB;MACA,OAAOZ,cAAc,CAAC6D,IAAI,CAAC,IAAI,CAAChC,UAAU,EAAEjB,UAAU,CAAC;IAC3D;IACA4C,wBAAwBA,CAAA,EAAG;MACvB,OAAOvD,MAAM,CAAC0C,IAAI,CAAC,IAAI,CAACd,UAAU,CAAC;IACvC;IACAiE,2BAA2BA,CAACC,YAAY,EAAE;MACtC,MAAMC,iBAAiB,GAAGD,YAAY,CAAC7C,WAAW,CAAC,CAAC;MACpD,IAAI,CAAClD,cAAc,CAAC6D,IAAI,CAAC,IAAI,CAAC7B,iBAAiB,EAAEgE,iBAAiB,CAAC,EAAE;QACjE,OAAO,IAAI;MACf;MACA,OAAO,IAAI,CAAChE,iBAAiB,CAACgE,iBAAiB,CAAC;IACpD;IACAC,uBAAuBA,CAACC,QAAQ,EAAE;MAC9B,IAAI,CAACA,QAAQ,EAAE;QACX,OAAO,IAAI;MACf;MACA,IAAIlG,cAAc,CAAC6D,IAAI,CAAC,IAAI,CAAC/B,aAAa,EAAEoE,QAAQ,CAAC,EAAE;QACnD,OAAO,IAAI,CAACpE,aAAa,CAACoE,QAAQ,CAAC;MACvC;MACA,OAAO,IAAI;IACf;IACAC,oCAAoCA,CAACC,QAAQ,EAAErB,SAAS,EAAE;MACtD,IAAI,CAACqB,QAAQ,IAAI,CAACrB,SAAS,EAAE;QACzB,OAAO,EAAE;MACb;MACA,OAAOrF,cAAc,CAAC0G,QAAQ,EAAErB,SAAS,CAAC;IAC9C;EACJ;EAAC,OApMY5D,iBAAiB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}