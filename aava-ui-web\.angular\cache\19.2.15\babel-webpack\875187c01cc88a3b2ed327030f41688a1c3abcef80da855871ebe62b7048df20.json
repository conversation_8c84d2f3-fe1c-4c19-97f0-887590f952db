{"ast": null, "code": "import { CommonModule, formatDate } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport { ApprovalCardComponent, IconComponent, AvaTextboxComponent, AvaTagComponent, ButtonComponent } from '@ava/play-comp-library';\nimport approvalText from '../constants/approval.json';\nimport { debounceTime, distinctUntilChanged, map, startWith } from 'rxjs';\nimport { AgentsPreviewPanelComponent } from './agents-preview-panel/agents-preview-panel.component';\nimport { ApprovalTxtCardComponent } from '../approval-text-card/approval-text-card.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../shared/services/shared-api-service.service\";\nimport * as i3 from \"../../../shared/services/approval.service\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@shared/services/tools.service\";\nimport * as i6 from \"../../../shared/services/drawer/drawer.service\";\nimport * as i7 from \"@ava/play-comp-library\";\nfunction ApprovalToolsComponent_Conditional_17_For_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵlistener(\"click\", function ApprovalToolsComponent_Conditional_17_For_1_Template_div_click_0_listener() {\n      const $index_r2 = i0.ɵɵrestoreView(_r1).$index;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onCardClick($index_r2));\n    });\n    i0.ɵɵelementStart(1, \"ava-approval-card\", 15)(2, \"ava-card-header\");\n    i0.ɵɵelement(3, \"ava-icon\", 16);\n    i0.ɵɵelementStart(4, \"div\", 17)(5, \"h2\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"ava-tag\", 18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"ava-card-content\")(9, \"div\", 19)(10, \"div\", 20);\n    i0.ɵɵelement(11, \"ava-tag\", 21)(12, \"ava-tag\", 22)(13, \"ava-tag\", 23)(14, \"ava-tag\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 25)(16, \"div\", 26);\n    i0.ɵɵelement(17, \"ava-icon\", 27);\n    i0.ɵɵelementStart(18, \"span\");\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 28);\n    i0.ɵɵelement(21, \"ava-icon\", 29);\n    i0.ɵɵelementStart(22, \"span\");\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(24, \"ava-card-footer\")(25, \"div\", 30)(26, \"div\", 31)(27, \"span\", 32);\n    i0.ɵɵtext(28, \"Execution Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\");\n    i0.ɵɵelement(30, \"ava-icon\", 33);\n    i0.ɵɵelementStart(31, \"span\");\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(33, \"div\", 34)(34, \"ava-button\", 35);\n    i0.ɵɵlistener(\"userClick\", function ApprovalToolsComponent_Conditional_17_For_1_Template_ava_button_userClick_34_listener() {\n      const $index_r2 = i0.ɵɵrestoreView(_r1).$index;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.handleTesting($index_r2));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"ava-button\", 36);\n    i0.ɵɵlistener(\"userClick\", function ApprovalToolsComponent_Conditional_17_For_1_Template_ava_button_userClick_35_listener() {\n      const $index_r2 = i0.ɵɵrestoreView(_r1).$index;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.rejectApproval($index_r2));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"ava-button\", 37);\n    i0.ɵɵlistener(\"userClick\", function ApprovalToolsComponent_Conditional_17_For_1_Template_ava_button_userClick_36_listener() {\n      const $index_r2 = i0.ɵɵrestoreView(_r1).$index;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.approveApproval($index_r2));\n    });\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(item_r4.session1.title);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r2.currentTab);\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate(item_r4.session3[0].label);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(item_r4.session3[1].label);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(item_r4 == null ? null : item_r4.session4.status);\n  }\n}\nfunction ApprovalToolsComponent_Conditional_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵrepeaterCreate(0, ApprovalToolsComponent_Conditional_17_For_1_Template, 37, 5, \"div\", 13, i0.ɵɵrepeaterTrackByIndex);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵrepeater(ctx_r2.consoleApproval.contents);\n  }\n}\nfunction ApprovalToolsComponent_Conditional_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" All \", ctx_r2.currentTab, \" have been successfully approved. No pending actions. \");\n  }\n}\nexport let ApprovalToolsComponent = /*#__PURE__*/(() => {\n  class ApprovalToolsComponent {\n    router;\n    apiService;\n    approvalService;\n    fb;\n    toolsService;\n    drawerService;\n    dialogService;\n    appLabels = approvalText.labels;\n    totalApprovedApprovals = 20;\n    totalPendingApprovals = 15;\n    totalApprovals = 60;\n    isBasicCollapsed = false;\n    quickActionsExpanded = true;\n    consoleApproval = {};\n    options = [];\n    basicSidebarItems = [];\n    quickActions = [];\n    toolReviews = [];\n    filteredToolReviews = [];\n    workflowReviews = [];\n    agentsReviews = [];\n    currentToolsPage = 1;\n    currentAgentsPage = 1;\n    currentWorkflowsPage = 1;\n    pageSize = 50;\n    totalRecords = 0;\n    isDeleted = false;\n    currentTab = 'Tools';\n    showToolApprovalPopup = false;\n    showInfoPopup = false;\n    showErrorPopup = false;\n    infoMessage = '';\n    selectedIndex = 0;\n    showFeedbackPopup = false;\n    searchForm;\n    labels = approvalText.labels;\n    approvedAgentId = null;\n    previewData = null;\n    selectedToolId = '';\n    feedbackMessage = '';\n    constructor(router, apiService, approvalService, fb, toolsService, drawerService, dialogService) {\n      this.router = router;\n      this.apiService = apiService;\n      this.approvalService = approvalService;\n      this.fb = fb;\n      this.toolsService = toolsService;\n      this.drawerService = drawerService;\n      this.dialogService = dialogService;\n      this.labels = approvalText.labels;\n      this.options = [{\n        name: this.labels.electronics,\n        value: 'electronics'\n      }, {\n        name: this.labels.clothing,\n        value: 'clothing'\n      }, {\n        name: this.labels.books,\n        value: 'books'\n      }];\n      this.basicSidebarItems = [{\n        id: '1',\n        icon: 'hammer',\n        text: this.labels.agents,\n        route: '',\n        active: true\n      }, {\n        id: '2',\n        icon: 'circle-check',\n        text: this.labels.workflows,\n        route: ''\n      }, {\n        id: '3',\n        icon: 'bot',\n        text: this.labels.tools,\n        route: ''\n      }];\n      this.quickActions = [{\n        icon: 'awe_agents',\n        label: this.labels.agents,\n        route: ''\n      }, {\n        icon: 'awe_workflows',\n        label: this.labels.workflows,\n        route: ''\n      }, {\n        icon: 'awe_tools',\n        label: this.labels.tools,\n        route: ''\n      }];\n      this.searchForm = this.fb.group({\n        search: ['']\n      });\n    }\n    ngOnInit() {\n      this.searchList();\n      this.totalApprovals = 60;\n      this.loadToolReviews();\n    }\n    searchList() {\n      console.log(this.searchForm.get('search')?.value);\n      this.searchForm.get('search').valueChanges.pipe(startWith(''), debounceTime(300), distinctUntilChanged(), map(value => value?.toLowerCase() ?? '')).subscribe(searchText => {\n        this.applyFilter(searchText);\n      });\n    }\n    applyFilter(text) {\n      const lower = text;\n      if (!text) {\n        this.updateConsoleApproval(this.toolReviews, 'tool');\n        return;\n      }\n      this.filteredToolReviews = this.toolReviews.filter(item => item.toolName?.toLowerCase().includes(lower));\n      this.updateConsoleApproval(this.filteredToolReviews, 'tool');\n    }\n    onSelectionChange(data) {\n      console.log('Selection changed:', data);\n    }\n    uClick(i) {\n      console.log('log' + i);\n    }\n    toggleQuickActions() {\n      this.quickActionsExpanded = !this.quickActionsExpanded;\n    }\n    onBasicCollapseToggle(isCollapsed) {\n      this.isBasicCollapsed = isCollapsed;\n      console.log('Basic sidebar collapsed:', isCollapsed);\n    }\n    onBasicItemClick(item) {\n      this.basicSidebarItems.forEach(i => i.active = false);\n      item.active = true;\n      console.log(item);\n    }\n    toRequestStatus(value) {\n      return value === 'approved' || value === 'rejected' || value === 'review' ? value : 'review';\n    }\n    loadToolReviews() {\n      this.approvalService.getAllReviewTools(this.currentToolsPage, this.pageSize, this.isDeleted).subscribe(response => {\n        if (this.currentToolsPage > 1) {\n          this.toolReviews = [...this.toolReviews, ...response.userToolReviewDetails];\n        } else {\n          this.toolReviews = response?.userToolReviewDetails;\n        }\n        this.toolReviews = this.toolReviews.filter(r => r.status !== 'approved');\n        this.filteredToolReviews = this.toolReviews;\n        // console.log('tool reviews ', this.toolReviews);\n        this.totalRecords = this.toolReviews.length;\n        this.updateConsoleApproval(this.toolReviews, 'tool');\n      });\n    }\n    loadMoreTools(page) {\n      this.currentToolsPage = page;\n      this.loadToolReviews();\n    }\n    loadReviews(name) {\n      this.currentTab = name;\n      this.loadToolReviews();\n    }\n    rejectApproval(idx) {\n      console.log(idx);\n      this.selectedIndex = idx;\n    }\n    approveApproval(idx) {\n      console.log(idx);\n      this.selectedIndex = idx;\n      console.log(this.filteredToolReviews[this.selectedIndex]);\n    }\n    handleApproval() {\n      this.handleToolApproval();\n    }\n    handleRejection(feedback) {\n      console.log(this.selectedIndex);\n      this.handleToolRejection(feedback);\n    }\n    showApprovalDialog() {\n      this.dialogService.confirmation({\n        title: this.labels.confirmApproval,\n        message: `${this.labels.youAreAboutToApproveThis} Tool. ${this.labels.itWillBeActiveAndAvailableIn} ${this.currentTab} ${this.labels.catalogueForUsersToExecute}`,\n        confirmButtonText: this.labels.approve,\n        cancelButtonText: 'Cancel',\n        confirmButtonVariant: 'danger',\n        icon: 'circle-check'\n      }).then(result => {\n        if (result.confirmed) {\n          this.handleApproval();\n        }\n      });\n    }\n    showFeedbackDialog() {\n      const customButtons = [{\n        label: 'Cancel',\n        variant: 'secondary',\n        action: 'cancel'\n      }, {\n        label: 'Send Back',\n        variant: 'primary',\n        action: 'sendback'\n      }];\n      this.dialogService.feedback({\n        title: 'Confirm Send Back',\n        message: 'This Tool will be send back for corrections and modification. Kindly comment what needs to be done.',\n        buttons: customButtons\n      }).then(result => {\n        if (result.confirmed && result.confirmed === true) {\n          this.handleRejection(result.data);\n        }\n      });\n    }\n    handleMetaDataApproval() {\n      this.showApprovalDialog();\n    }\n    handeMetaDataSendback() {\n      this.showFeedbackDialog();\n    }\n    onCardClick(index) {\n      console.log('Selected card index:', index);\n      this.selectedIndex = index;\n      const selectedTool = this.filteredToolReviews[this.selectedIndex];\n      this.loadPreviewData(selectedTool);\n      this.selectedToolId = selectedTool.toolId;\n      this.drawerService.open(AgentsPreviewPanelComponent, {\n        previewData: this.previewData,\n        closePreview: () => this.drawerService.clear(),\n        editTool: () => this.handleEditTool(selectedTool?.toolId),\n        rejectApproval: () => this.handeMetaDataSendback(),\n        approveApproval: () => this.handleMetaDataApproval(),\n        testApproval: () => this.redirectToToolPlayground()\n      });\n      console.log(selectedTool);\n    }\n    loadPreviewData(item) {\n      this.previewData = {\n        type: 'tool',\n        title: item.toolName,\n        data: {\n          ...item\n        },\n        loading: true,\n        error: null\n      };\n      console.log(this.previewData);\n      this.loadToolDetails(item);\n    }\n    loadToolDetails(item) {\n      if (item.id && this.toolsService.getUserToolDetails) {\n        // Extract numeric ID from string like \"user-423\"\n        let toolId;\n        if (typeof item.id === 'string' && item.id.startsWith('user-')) {\n          // Extract number from \"user-423\" format\n          const numericPart = item.id.replace('user-', '');\n          toolId = Number(numericPart);\n        } else {\n          toolId = Number(item.id);\n        }\n        if (isNaN(toolId)) {\n          console.warn('Invalid tool ID:', item.id, 'Using fallback data');\n          this.previewData.loading = false;\n          return;\n        }\n        this.toolsService.getUserToolDetails(toolId).subscribe({\n          next: response => {\n            console.log('Tool details response:', response);\n            let toolDetail, toolConfigs;\n            if (response.userToolDetail) {\n              toolDetail = response.userToolDetail;\n              toolConfigs = toolDetail.toolConfigs || {};\n            } else if (response.tools && response.tools[0]) {\n              const tool = response.tools[0];\n              toolDetail = {\n                id: tool.toolId,\n                name: tool.toolName,\n                description: tool.toolDescription,\n                createdBy: tool.createdBy,\n                createdAt: tool.createTimestamp,\n                modifiedAt: tool.updateTimestamp,\n                isDeleted: !tool.isApproved\n              };\n              toolConfigs = {\n                tool_class_name: [tool.toolClassName],\n                tool_class_def: [tool.toolClassDef]\n              };\n            } else {\n              // Fallback\n              toolDetail = response;\n              toolConfigs = {};\n            }\n            // Extract functionality/code from various possible fields\n            let functionality = 'Tool code not found';\n            if (toolConfigs.tool_class_def && toolConfigs.tool_class_def[0]) {\n              functionality = toolConfigs.tool_class_def[0];\n            } else if (toolDetail.toolClassDef) {\n              functionality = toolDetail.toolClassDef;\n            } else if (toolDetail.code) {\n              functionality = toolDetail.code;\n            } else if (toolDetail.definition) {\n              functionality = toolDetail.definition;\n            }\n            this.previewData.data = {\n              ...this.previewData.data,\n              id: toolDetail.id,\n              name: toolDetail.name || item.name,\n              description: toolDetail.description || item.description,\n              className: toolConfigs.tool_class_name?.[0] || toolDetail.toolClassName || 'Unknown',\n              functionality: functionality,\n              isApproved: !toolDetail.isDeleted,\n              createdBy: toolDetail.createdBy || 'Unknown',\n              createdOn: toolDetail.createdAt || toolDetail.createTimestamp || new Date().toISOString(),\n              modifiedBy: toolDetail.modifiedBy,\n              modifiedAt: toolDetail.modifiedAt || toolDetail.updateTimestamp,\n              isDeleted: toolDetail.isDeleted || false\n            };\n            this.previewData.loading = false;\n          },\n          error: error => {\n            console.error('Error loading tool details:', error);\n            this.previewData.error = 'Failed to load tool details';\n            this.previewData.loading = false;\n          }\n        });\n      } else {\n        this.previewData.loading = false;\n      }\n    }\n    handleEditTool(toolId) {\n      console.log('Edit tool', toolId);\n      // this.drawerService.clear();\n      // this.router.navigate(['/libraries/tools/edit', toolId]);\n    }\n    handleToolApproval() {\n      const toolDetails = this.filteredToolReviews[this.selectedIndex];\n      const id = toolDetails.id;\n      const toolId = toolDetails.toolId;\n      const status = 'approved';\n      const reviewedBy = toolDetails.reviewedBy;\n      // Show loading dialog\n      this.dialogService.loading({\n        title: 'Approving Tool...',\n        message: 'Please wait while we approve the tool.',\n        showProgress: false,\n        showCancelButton: false\n      });\n      this.approvalService.approveTool(id, toolId, status, reviewedBy).subscribe({\n        next: response => {\n          this.dialogService.close(); // Close loading dialog\n          const message = response?.message || this.labels.toolSuccessApproveMessage;\n          this.dialogService.success({\n            title: 'Tool Approved',\n            message: message\n          }).then(result => {\n            if (result.action === 'secondary') {\n              // Navigate to edit tool screen\n              this.router.navigate(['/libraries/tools/edit', toolId]);\n            } else {\n              this.loadToolReviews(); // Refresh the list\n            }\n          });\n        },\n        error: error => {\n          this.dialogService.close(); // Close loading dialog\n          const errorMessage = error?.error?.message || this.labels.defaultErrorMessage;\n          this.dialogService.error({\n            title: 'Approval Failed',\n            message: errorMessage,\n            showRetryButton: true,\n            retryButtonText: 'Retry'\n          }).then(result => {\n            if (result.action === 'retry') {\n              this.handleToolApproval();\n            }\n          });\n        }\n      });\n    }\n    handleToolRejection(feedback) {\n      const toolDetails = this.filteredToolReviews[this.selectedIndex];\n      const id = toolDetails.id;\n      const toolId = toolDetails.toolId;\n      const status = 'rejected';\n      const reviewedBy = toolDetails.reviewedBy;\n      const message = feedback;\n      // Show loading dialog\n      this.dialogService.loading({\n        title: 'Rejecting Tool...',\n        message: 'Please wait while we reject the tool.',\n        showProgress: false,\n        showCancelButton: false\n      });\n      this.approvalService.rejectTool(id, toolId, status, reviewedBy, message).subscribe({\n        next: response => {\n          this.dialogService.close(); // Close loading dialog\n          const message = response?.message || this.labels.toolSuccessRejectMessage;\n          this.dialogService.success({\n            title: 'Tool Rejected',\n            message: message\n          }).then(() => {\n            this.loadToolReviews(); // Refresh the list\n          });\n        },\n        error: error => {\n          this.dialogService.close(); // Close loading dialog\n          const errorMessage = error?.error?.message || this.labels.defaultErrorMessage;\n          this.dialogService.error({\n            title: 'Rejection Failed',\n            message: errorMessage,\n            showRetryButton: true,\n            retryButtonText: 'Retry'\n          }).then(result => {\n            if (result.action === 'retry') {\n              this.handleToolRejection(feedback);\n            }\n          });\n        }\n      });\n    }\n    handleTesting(index) {\n      console.log(index);\n      this.selectedToolId = this.filteredToolReviews[index].toolId;\n    }\n    redirectToToolPlayground() {\n      this.drawerService.clear();\n      this.router.navigate(['/libraries/tools/execute', this.selectedToolId]);\n    }\n    redirectToTool() {\n      this.router.navigate(['/libraries/tools/edit', this.selectedToolId]);\n    }\n    updateConsoleApproval(data, type) {\n      this.consoleApproval = {\n        contents: data?.map(req => {\n          const statusIcons = {\n            approved: 'circle-check-big',\n            rejected: 'circle-x',\n            review: 'clock'\n          };\n          const statusTexts = {\n            approved: this.labels.approved,\n            rejected: this.labels.rejected,\n            review: this.labels.review\n          };\n          const statusKey = this.toRequestStatus(req?.status);\n          const specificId = req.toolId;\n          const title = req.toolName;\n          return {\n            id: req.id,\n            refId: specificId,\n            type: type,\n            session1: {\n              title: title,\n              labels: [{\n                name: type,\n                color: 'success',\n                background: 'red',\n                type: 'normal'\n              }, {\n                name: req.changeRequestType,\n                color: req.changeRequestType === 'update' ? 'error' : 'info',\n                background: 'red',\n                type: 'pill'\n              }]\n            },\n            session2: [{\n              name: type,\n              color: 'default',\n              background: 'red',\n              type: 'normal'\n            }, {\n              name: req.status,\n              color: 'default',\n              background: 'red',\n              type: 'normal'\n            }],\n            session3: [{\n              iconName: 'user',\n              label: req.requestedBy\n            }, {\n              iconName: 'calendar-days',\n              label: formatDate(req?.requestedAt, 'dd MMM yyyy', 'en-IN')\n            }],\n            session4: {\n              status: statusTexts[statusKey],\n              iconName: statusIcons[statusKey]\n            }\n          };\n        }),\n        footer: {}\n      };\n    }\n    static ɵfac = function ApprovalToolsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ApprovalToolsComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.SharedApiServiceService), i0.ɵɵdirectiveInject(i3.ApprovalService), i0.ɵɵdirectiveInject(i4.FormBuilder), i0.ɵɵdirectiveInject(i5.ToolsService), i0.ɵɵdirectiveInject(i6.DrawerService), i0.ɵɵdirectiveInject(i7.DialogService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ApprovalToolsComponent,\n      selectors: [[\"app-approval-tools\"]],\n      decls: 19,\n      vars: 19,\n      consts: [[1, \"approval-right-screen\"], [1, \"approval-title-filter\"], [3, \"iconName\", \"title\", \"value\", \"subtitle\"], [1, \"filter-section\"], [1, \"search-bars\"], [2, \"font-size\", \"1.5rem\", \"font-weight\", \"bold\", \"color\", \"black\"], [1, \"approval-card-header\"], [1, \"textbox\", \"section\"], [3, \"formGroup\"], [\"formControlName\", \"search\", 3, \"placeholder\"], [\"slot\", \"icon-start\", \"iconName\", \"search\", \"iconColor\", \"var(--color-brand-primary)\", 3, \"iconSize\"], [1, \"approval-card-section\"], [1, \"no-pending-message\"], [1, \"approval-card-wrapper\"], [1, \"approval-card-wrapper\", 3, \"click\"], [\"height\", \"300\"], [\"iconSize\", \"20\", \"iconName\", \"ellipsis-vertical\"], [1, \"header\"], [\"color\", \"info\", \"size\", \"sm\", 3, \"label\"], [1, \"a-content\"], [1, \"box\", \"tag-wrapper\"], [\"label\", \"Individual\", \"size\", \"sm\"], [\"label\", \"Ascendion\", \"size\", \"sm\"], [\"label\", \"Digital Ascender\", \"size\", \"sm\"], [\"label\", \"Platform Engineering\", \"size\", \"sm\"], [1, \"box\", \"info-wrapper\"], [1, \"f\"], [\"iconSize\", \"13\", \"iconName\", \"user\"], [1, \"ml-auto\", \"s\"], [\"iconSize\", \"20\", \"iconName\", \"calendar-days\"], [1, \"footer-content\"], [1, \"footer-left\"], [1, \"ex\"], [\"iconSize\", \"20\", \"iconName\", \"circle-check-big\"], [1, \"footer-right\"], [\"label\", \"Test\", \"variant\", \"secondary\", \"size\", \"medium\", \"state\", \"default\", \"iconName\", \"play\", \"iconPosition\", \"left\", 3, \"userClick\"], [\"label\", \"Sendback\", \"variant\", \"secondary\", \"size\", \"medium\", \"state\", \"default\", \"iconName\", \"move-left\", \"iconPosition\", \"left\", 3, \"userClick\"], [\"label\", \"Approve\", \"variant\", \"primary\", \"size\", \"medium\", \"state\", \"default\", \"iconName\", \"Check\", \"iconPosition\", \"left\", 3, \"userClick\"]],\n      template: function ApprovalToolsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"app-approval-txt-card\", 2)(3, \"app-approval-txt-card\", 2)(4, \"app-approval-txt-card\", 2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 3)(6, \"div\", 4)(7, \"div\", 5);\n          i0.ɵɵtext(8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"div\", 6);\n          i0.ɵɵtext(10);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"div\", 7)(12, \"div\")(13, \"form\", 8)(14, \"ava-textbox\", 9);\n          i0.ɵɵelement(15, \"ava-icon\", 10);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(16, \"div\", 11);\n          i0.ɵɵtemplate(17, ApprovalToolsComponent_Conditional_17_Template, 2, 0)(18, ApprovalToolsComponent_Conditional_18_Template, 2, 1, \"div\", 12);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"iconName\", \"hourglass\")(\"title\", ctx.labels.totalApprovals)(\"value\", ctx.totalApprovals)(\"subtitle\", ctx.currentTab + \" \" + ctx.labels.whichAreRequestedForApproval);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"iconName\", \"shield-alert\")(\"title\", ctx.labels.totalApprovedApprovals)(\"value\", ctx.totalApprovedApprovals)(\"subtitle\", ctx.currentTab + \" \" + ctx.labels.whichAreApproved);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"iconName\", \"hourglass\")(\"title\", ctx.labels.totalPendingApprovals)(\"value\", ctx.totalPendingApprovals)(\"subtitle\", ctx.labels.all + \" \" + ctx.currentTab + \" \" + ctx.labels.awaitingApproval);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", ctx.currentTab, \" Approvals \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate2(\" All - \", ctx.totalRecords, \" \", ctx.currentTab, \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"formGroup\", ctx.searchForm);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"placeholder\", ctx.labels.searchPlaceholder);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"iconSize\", 16);\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(ctx.totalRecords > 0 ? 17 : 18);\n        }\n      },\n      dependencies: [CommonModule, RouterModule, ApprovalCardComponent, IconComponent, AvaTextboxComponent, ReactiveFormsModule, i4.ɵNgNoValidate, i4.NgControlStatus, i4.NgControlStatusGroup, i4.FormGroupDirective, i4.FormControlName, AvaTagComponent, ButtonComponent, ApprovalTxtCardComponent],\n      styles: [\".approval[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: row;\\n  width: 100%;\\n  height: 100%;\\n}\\n\\n.approval-left-screen[_ngcontent-%COMP%] {\\n  flex: 0 0 70px; \\n\\n  width: 70px;\\n  transition: all var(--transition-speed) ease;\\n  height: 100%; \\n\\n  overflow: hidden;\\n}\\n.approval-left-screen.quick-actions-expanded[_ngcontent-%COMP%] {\\n  flex: 0 0 250px;\\n  margin-right: 15px;\\n}\\n\\n\\n\\n.approval-right-screen[_ngcontent-%COMP%] {\\n  flex: 1; \\n\\n  padding: 1rem; \\n\\n  overflow-y: auto; \\n\\n}\\n\\n\\n\\n.approval-title-filter[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  margin-bottom: 1rem; \\n\\n}\\n\\n\\n\\n.approvals-title[_ngcontent-%COMP%] {\\n  font-weight: bold;\\n  font-size: 1.2rem;\\n  margin-bottom: 0.5rem;\\n}\\n\\n\\n\\n.filter-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 0 1rem; \\n\\n  margin-bottom: 1rem; \\n\\n}\\n\\n\\n\\n.search-bars[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 1rem; \\n\\n}\\n\\n\\n\\n.textbox.section[_ngcontent-%COMP%] {\\n  margin-left: 1rem;\\n}\\n\\n.textbox.section[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center; \\n\\n  gap: 8px; \\n\\n}\\n\\n.approval-card-header[_ngcontent-%COMP%] {\\n  font-size: 1.25rem; \\n\\n  font-weight: 600; \\n\\n  color: grey; \\n\\n  margin-bottom: 1.5rem; \\n\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between; \\n\\n}\\n\\n.approval-title-filter[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: row;\\n  justify-content: space-between;\\n  align-items: center;\\n  gap: 1rem;\\n  flex-wrap: nowrap;\\n  width: 100%;\\n  padding: 1rem 0;\\n}\\n\\n.approval-title-filter[_ngcontent-%COMP%]    > ava-text-card[_ngcontent-%COMP%] {\\n  flex: 1 1 22%;\\n  min-width: 200px;\\n}\\n\\n.quick-actions-wrapper[_ngcontent-%COMP%] {\\n  grid-area: quick-actions;\\n  background-color: var(--dashboard-card-bg);\\n  border-radius: var(--border-radius-standard);\\n  display: flex;\\n  flex-direction: column;\\n  width: 55px;\\n  height: 100%;\\n  transition: all 0.35s cubic-bezier(0.4, 0, 0.2, 1);\\n  overflow: hidden;\\n  box-shadow: var(--shadow-medium);\\n  border: var(--border-thin);\\n  position: relative;\\n  \\n\\n  \\n\\n  \\n\\n  \\n\\n}\\n.quick-actions-wrapper[_ngcontent-%COMP%]:hover {\\n  box-shadow: var(--shadow-hover);\\n}\\n.quick-actions-wrapper.expanded[_ngcontent-%COMP%] {\\n  width: 100%; \\n\\n}\\n@media (min-width: 1900px) and (max-width: 1930px) and (max-height: 1100px) {\\n  .quick-actions-wrapper[_ngcontent-%COMP%] {\\n    height: 595px;\\n  }\\n}\\n@media (min-width: 1200px) and (max-width: 1400px) {\\n  .quick-actions-wrapper[_ngcontent-%COMP%] {\\n    height: 580px !important;\\n    max-height: 580px !important;\\n  }\\n}\\n@media (max-width: 1200px) {\\n  .quick-actions-wrapper[_ngcontent-%COMP%] {\\n    height: 320px;\\n  }\\n}\\n@media (max-width: 992px) {\\n  .quick-actions-wrapper[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    width: 100%;\\n    height: 48px;\\n  }\\n  .quick-actions-wrapper.expanded[_ngcontent-%COMP%] {\\n    height: auto;\\n    max-height: 320px;\\n    width: 100%;\\n  }\\n}\\n@media (max-width: 576px) {\\n  .quick-actions-wrapper[_ngcontent-%COMP%] {\\n    height: 280px;\\n  }\\n}\\n@media (min-width: 1200px) and (max-width: 1366px) and (max-height: 800px) {\\n  .quick-actions-wrapper[_ngcontent-%COMP%] {\\n    height: 100%;\\n  }\\n  .quick-actions-wrapper[_ngcontent-%COMP%]     .card-container {\\n    height: 100%;\\n  }\\n}\\n.quick-actions-wrapper[_ngcontent-%COMP%]     .quick-actions-card {\\n  height: 100% !important;\\n  width: 100% !important;\\n  \\n\\n  \\n\\n}\\n.quick-actions-wrapper[_ngcontent-%COMP%]     .quick-actions-card .card-container {\\n  height: 100% !important;\\n  width: 100% !important;\\n  padding: 0 !important;\\n  overflow: hidden !important;\\n  display: flex !important;\\n  flex-direction: column !important;\\n}\\n.quick-actions-wrapper[_ngcontent-%COMP%]     .quick-actions-card .card-container .card-body {\\n  padding: 0 !important;\\n  height: 100% !important;\\n  display: flex !important;\\n  flex-direction: column !important;\\n}\\n.quick-actions-wrapper[_ngcontent-%COMP%]     .quick-actions-card .card-content {\\n  height: 100% !important;\\n  display: flex !important;\\n  flex-direction: column !important;\\n  padding: 0 !important;\\n}\\n@media (max-width: 992px) {\\n  .quick-actions-wrapper[_ngcontent-%COMP%]     .quick-actions-card .card-content {\\n    flex-direction: row !important;\\n  }\\n}\\n@media (max-width: 992px) {\\n  .quick-actions-wrapper[_ngcontent-%COMP%]     .quick-actions-card .card-container {\\n    height: 48px !important;\\n    width: 100% !important;\\n    flex-direction: row !important;\\n  }\\n  .quick-actions-wrapper[_ngcontent-%COMP%]     .quick-actions-card .card-container.expanded {\\n    height: auto !important;\\n  }\\n}\\n@media (min-width: 1200px) and (max-width: 1366px) and (max-height: 800px) {\\n  .quick-actions-wrapper[_ngcontent-%COMP%]     .quick-actions-card .card-container {\\n    width: 100% !important;\\n  }\\n}\\n\\n.quick-actions-content[_ngcontent-%COMP%] {\\n  padding: 20px 16px;\\n  overflow-y: auto;\\n  flex-grow: 1;\\n}\\n.quick-actions-content[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px; \\n\\n}\\n.quick-actions-content[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: flex-start;\\n  gap: 16px; \\n\\n  padding: 16px 20px; \\n\\n  border-radius: 12px; \\n\\n  border: none;\\n  border: 2px solid transparent;\\n  background: linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%);\\n  background-origin: border-box;\\n  background-clip: padding-box, border-box;\\n  --button-effect-color: 33, 90, 214;\\n  cursor: pointer;\\n  transition: all var(--transition-speed) ease;\\n  width: 100%;\\n  text-align: left;\\n  color: #fff;\\n}\\n.quick-actions-content[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%]   .action-icon[_ngcontent-%COMP%], \\n.quick-actions-content[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%]   .action-label[_ngcontent-%COMP%] {\\n  color: #fff;\\n}\\n.quick-actions-content[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%]:hover {\\n  opacity: 0.9;\\n  background: linear-gradient(103.35deg, #03BDD4 31.33%, #215AD6 100%);\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px var(--dashboard-shadow-hover);\\n}\\n.quick-actions-content[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%]   .action-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 24px;\\n  height: 24px;\\n}\\n.quick-actions-content[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%]   .action-icon[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  filter: brightness(0) invert(1); \\n\\n}\\n.quick-actions-content[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%]   .action-label[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 500;\\n  color: linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%); \\n\\n}\\n\\n.action-button.active-action[_ngcontent-%COMP%] {\\n  background: linear-gradient(103.35deg, #03BDD4 31.33%, #215AD6 100%) !important;\\n}\\n\\n.quick-actions-toggle[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n  padding: 20px 16px;\\n  padding-bottom: 0px;\\n  cursor: pointer;\\n  transition: background-color 0.35s cubic-bezier(0.4, 0, 0.2, 1);\\n  flex-shrink: 0;\\n  \\n\\n  \\n\\n}\\n.quick-actions-wrapper[_ngcontent-%COMP%]:not(.expanded)   .quick-actions-toggle[_ngcontent-%COMP%] {\\n  padding: 20px 0;\\n  justify-content: center;\\n}\\n.quick-actions-toggle[_ngcontent-%COMP%]   .toggle-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 36px;\\n  height: 36px;\\n  border-radius: 8px;\\n  background-color: transparent;\\n  position: relative;\\n  \\n\\n}\\n.quick-actions-toggle[_ngcontent-%COMP%]   .toggle-button[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  inset: 0;\\n  border-radius: 8px;\\n  padding: 1px;\\n  background: var(--dashboard-gradient);\\n  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);\\n  mask-composite: exclude;\\n  transition: opacity 0.35s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.quick-actions-toggle[_ngcontent-%COMP%]   .toggle-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  transition: transform 0.35s cubic-bezier(0.4, 0, 0.2, 1);\\n  width: 16px;\\n  height: 16px;\\n  stroke: var(--dashboard-toggle-stroke);\\n  z-index: 1;\\n}\\n.quick-actions-toggle[_ngcontent-%COMP%]   .toggle-button[_ngcontent-%COMP%]   svg.rotate[_ngcontent-%COMP%] {\\n  transform: rotate(180deg);\\n}\\n.quick-actions-toggle[_ngcontent-%COMP%]   .quick-actions-wrapper[_ngcontent-%COMP%]:not(.expanded)   .toggle-button[_ngcontent-%COMP%] {\\n  background: var(--dashboard-gradient);\\n  transition: background 0.35s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.quick-actions-toggle[_ngcontent-%COMP%]   .quick-actions-wrapper[_ngcontent-%COMP%]:not(.expanded)   .toggle-button[_ngcontent-%COMP%]::before {\\n  display: none;\\n}\\n.quick-actions-toggle[_ngcontent-%COMP%]   .quick-actions-wrapper[_ngcontent-%COMP%]:not(.expanded)   .toggle-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  stroke: var(--dashboard-toggle-stroke-collapsed);\\n  transition: stroke 0.35s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.quick-actions-toggle[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-weight: 580;\\n  font-size: 16px;\\n  color: var(--dashboard-text-primary);\\n  opacity: 1;\\n  transition: opacity 0.35s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n\\n.quick-actions-icons[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 16px; \\n\\n  padding: 20px 0; \\n\\n  height: 100%;\\n}\\n@media (max-width: 992px) {\\n  .quick-actions-icons[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    justify-content: center;\\n    flex-wrap: wrap;\\n    padding: 8px;\\n  }\\n}\\n.quick-actions-icons[_ngcontent-%COMP%]   .icon-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 36px; \\n\\n  height: 36px; \\n\\n  border-radius: 8px; \\n\\n  border: none;\\n  background: linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%);\\n  cursor: pointer;\\n  transition: all var(--transition-speed) ease;\\n}\\n.quick-actions-icons[_ngcontent-%COMP%]   .icon-button[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(103.35deg, #03BDD4 31.33%, #215AD6 100%);\\n  opacity: 0.9;\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px var(--dashboard-shadow-hover);\\n}\\n.quick-actions-icons[_ngcontent-%COMP%]   .icon-button[_ngcontent-%COMP%]   .icon-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.quick-actions-icons[_ngcontent-%COMP%]   .icon-button[_ngcontent-%COMP%]   .icon-wrapper[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  filter: brightness(0) invert(1); \\n\\n}\\n\\n.icon-button.active-action[_ngcontent-%COMP%] {\\n  background: linear-gradient(103.35deg, #03BDD4 31.33%, #215AD6 100%) !important;\\n}\\n\\n.approval-card-section[_ngcontent-%COMP%] {\\n  height: 780px;\\n  overflow-y: auto;\\n}\\n\\n.approval-card-wrapper[_ngcontent-%COMP%] {\\n  margin: 10px;\\n}\\n\\n.no-pending-message[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin-top: 20%;\\n  font-size: 1.2rem;\\n  color: #000000;\\n  font-weight: 500;\\n  text-align: center;\\n  border-radius: 16px;\\n  min-height: 100px;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .filter-section[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: stretch;\\n  }\\n  .search-bars[_ngcontent-%COMP%], \\n   .textbox.section[_ngcontent-%COMP%] {\\n    width: 100%;\\n    margin: 0 0 0.5rem 0;\\n    justify-content: center;\\n  }\\n  .search-bars[_ngcontent-%COMP%] {\\n    flex-wrap: wrap;\\n    gap: 0.5rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return ApprovalToolsComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "formatDate", "ReactiveFormsModule", "RouterModule", "ApprovalCardComponent", "IconComponent", "AvaTextboxComponent", "AvaTagComponent", "ButtonComponent", "approvalText", "debounceTime", "distinctUntilChanged", "map", "startWith", "AgentsPreviewPanelComponent", "ApprovalTxtCardComponent", "i0", "ɵɵelementStart", "ɵɵlistener", "ApprovalToolsComponent_Conditional_17_For_1_Template_div_click_0_listener", "$index_r2", "ɵɵrestoreView", "_r1", "$index", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "onCardClick", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ApprovalToolsComponent_Conditional_17_For_1_Template_ava_button_userClick_34_listener", "handleTesting", "ApprovalToolsComponent_Conditional_17_For_1_Template_ava_button_userClick_35_listener", "rejectApproval", "ApprovalToolsComponent_Conditional_17_For_1_Template_ava_button_userClick_36_listener", "approveApproval", "ɵɵadvance", "ɵɵtextInterpolate", "item_r4", "session1", "title", "ɵɵpropertyInterpolate", "currentTab", "session3", "label", "session4", "status", "ɵɵrepeaterCreate", "ApprovalToolsComponent_Conditional_17_For_1_Template", "ɵɵrepeaterTrackByIndex", "ɵɵrepeater", "consoleApproval", "contents", "ɵɵtextInterpolate1", "ApprovalToolsComponent", "router", "apiService", "approvalService", "fb", "toolsService", "drawerService", "dialogService", "appLabels", "labels", "totalApprovedApprovals", "totalPendingApprovals", "totalApprovals", "isBasicCollapsed", "quickActionsExpanded", "options", "basicSidebarItems", "quickActions", "toolReviews", "filteredToolReviews", "workflowReviews", "agentsReviews", "currentToolsPage", "currentAgentsPage", "currentWorkflowsPage", "pageSize", "totalRecords", "isDeleted", "showToolApprovalPopup", "showInfoPopup", "showErrorPopup", "infoMessage", "selectedIndex", "showFeedbackPopup", "searchForm", "approvedAgentId", "previewData", "selectedToolId", "feedbackMessage", "constructor", "name", "electronics", "value", "clothing", "books", "id", "icon", "text", "agents", "route", "active", "workflows", "tools", "group", "search", "ngOnInit", "searchList", "loadToolReviews", "console", "log", "get", "valueChanges", "pipe", "toLowerCase", "subscribe", "searchText", "applyFilter", "lower", "updateConsoleApproval", "filter", "item", "toolName", "includes", "onSelectionChange", "data", "uClick", "i", "toggleQuickActions", "onBasicCollapseToggle", "isCollapsed", "onBasicItemClick", "for<PERSON>ach", "toRequestStatus", "getAllReviewTools", "response", "userToolReviewDetails", "r", "length", "loadMoreTools", "page", "loadReviews", "idx", "handleApproval", "handleToolApproval", "handleRejection", "feedback", "handleToolRejection", "showApprovalDialog", "confirmation", "confirmApproval", "message", "youAreAboutToApproveThis", "itWillBeActiveAndAvailableIn", "catalogueForUsersToExecute", "confirmButtonText", "approve", "cancelButtonText", "confirmButtonVariant", "then", "result", "confirmed", "showFeedbackDialog", "customButtons", "variant", "action", "buttons", "handleMetaDataApproval", "handeMetaDataSendback", "index", "selectedTool", "loadPreviewData", "toolId", "open", "closePreview", "clear", "editTool", "handleEditTool", "testApproval", "redirectToToolPlayground", "type", "loading", "error", "loadToolDetails", "getUserToolDetails", "startsWith", "numericPart", "replace", "Number", "isNaN", "warn", "next", "toolDetail", "toolConfigs", "userToolDetail", "tool", "description", "toolDescription", "created<PERSON>y", "createdAt", "createTimestamp", "modifiedAt", "updateTimestamp", "isApproved", "tool_class_name", "toolClassName", "tool_class_def", "toolClassDef", "functionality", "code", "definition", "className", "createdOn", "Date", "toISOString", "modifiedBy", "toolDetails", "reviewedBy", "showProgress", "showCancelButton", "approveTool", "close", "toolSuccessApproveMessage", "success", "navigate", "errorMessage", "defaultErrorMessage", "showRetryButton", "retryButtonText", "rejectTool", "toolSuccessRejectMessage", "redirectToTool", "req", "statusIcons", "approved", "rejected", "review", "statusTexts", "statusKey", "specificId", "refId", "color", "background", "changeRequestType", "session2", "iconName", "requestedBy", "requestedAt", "footer", "ɵɵdirectiveInject", "i1", "Router", "i2", "SharedApiServiceService", "i3", "ApprovalService", "i4", "FormBuilder", "i5", "ToolsService", "i6", "DrawerService", "i7", "DialogService", "selectors", "decls", "vars", "consts", "template", "ApprovalToolsComponent_Template", "rf", "ctx", "ɵɵtemplate", "ApprovalToolsComponent_Conditional_17_Template", "ApprovalToolsComponent_Conditional_18_Template", "ɵɵproperty", "whichAreRequestedForApproval", "whichAreApproved", "all", "awaitingApproval", "ɵɵtextInterpolate2", "searchPlaceholder", "ɵɵconditional", "ɵNgNoValidate", "NgControlStatus", "NgControlStatusGroup", "FormGroupDirective", "FormControlName", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\console\\src\\app\\pages\\approval\\approval-tools\\approval-tools.component.ts", "C:\\console\\aava-ui-web\\projects\\console\\src\\app\\pages\\approval\\approval-tools\\approval-tools.component.html"], "sourcesContent": ["import { CommonModule, formatDate } from '@angular/common';\r\nimport { Component, CUSTOM_ELEMENTS_SCHEMA, OnInit } from '@angular/core';\r\nimport { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';\r\nimport { Router, RouterModule } from '@angular/router';\r\nimport { ApprovalCardComponent, IconComponent, AvaTextboxComponent, TextCardComponent, PopupComponent, ConfirmationPopupComponent, AvaTagComponent, DropdownOption, ButtonComponent, DialogService, DialogButton } from '@ava/play-comp-library';\r\nimport approvalText  from '../constants/approval.json';\r\nimport { SharedApiServiceService } from '../../../shared/services/shared-api-service.service';\r\nimport { ApprovalService } from '../../../shared/services/approval.service';\r\nimport { debounceTime, distinctUntilChanged, map, startWith } from 'rxjs';\r\nimport { ToolsService } from '@shared/services/tools.service';\r\nimport { DrawerService } from '../../../shared/services/drawer/drawer.service';\r\nimport { AgentsPreviewPanelComponent } from './agents-preview-panel/agents-preview-panel.component';\r\nimport { ApprovalTxtCardComponent } from '../approval-text-card/approval-text-card.component';\r\n\r\ntype RequestStatus = 'approved' | 'rejected' | 'review';\r\n\r\n@Component({\r\n  selector: 'app-approval-tools',\r\n  imports: [\r\n    CommonModule,\r\n    RouterModule,\r\n    ApprovalCardComponent,\r\n    IconComponent,\r\n    AvaTextboxComponent,\r\n    ReactiveFormsModule,\r\n    AvaTagComponent,\r\n    ButtonComponent,\r\n    PopupComponent,\r\n    ConfirmationPopupComponent,\r\n    ApprovalTxtCardComponent\r\n  ],\r\n  schemas: [CUSTOM_ELEMENTS_SCHEMA],\r\n  templateUrl: './approval-tools.component.html',\r\n  styleUrls: ['./approval-tools.component.scss']\r\n})\r\n\r\nexport class ApprovalToolsComponent implements OnInit {\r\n    appLabels = approvalText.labels;\r\n\r\n    public totalApprovedApprovals: number = 20;\r\n    public totalPendingApprovals: number = 15;\r\n    public totalApprovals: number = 60;\r\n    public isBasicCollapsed: boolean = false;\r\n    public quickActionsExpanded: boolean = true;\r\n    public consoleApproval: any = {};\r\n    public options: DropdownOption[] = [];\r\n    public basicSidebarItems: any[] = [];\r\n    public quickActions: any[] = [];\r\n    public toolReviews: any[] = [];\r\n    public filteredToolReviews: any[] = [];\r\n    public workflowReviews: any[] = [];\r\n    public agentsReviews: any[] = [];\r\n    public currentToolsPage = 1;\r\n    public currentAgentsPage = 1;\r\n    public currentWorkflowsPage = 1;\r\n    public pageSize = 50;\r\n    public totalRecords = 0;\r\n    public isDeleted = false;\r\n    public currentTab = 'Tools';\r\n    public showToolApprovalPopup = false;\r\n    public showInfoPopup = false;\r\n    public showErrorPopup = false;\r\n    public infoMessage = '';\r\n    public selectedIndex = 0;\r\n    public showFeedbackPopup = false;\r\n    public searchForm!: FormGroup;\r\n    public labels: any = approvalText.labels;\r\n    public approvedAgentId: number | null = null;\r\n    public previewData: any = null;\r\n    public selectedToolId: string = '';\r\n    public feedbackMessage: string = '';\r\n\r\n    constructor(\r\n      private router: Router,\r\n      private apiService: SharedApiServiceService,\r\n      private approvalService: ApprovalService,\r\n      private fb: FormBuilder,\r\n      private toolsService: ToolsService,\r\n      private drawerService: DrawerService,\r\n      private dialogService: DialogService\r\n    ) {\r\n      this.labels = approvalText.labels;\r\n      this.options = [\r\n        { name: this.labels.electronics, value: 'electronics' },\r\n        { name: this.labels.clothing, value: 'clothing' },\r\n        { name: this.labels.books, value: 'books' },\r\n      ];\r\n      this.basicSidebarItems = [\r\n        { id: '1', icon: 'hammer', text: this.labels.agents, route: '', active: true },\r\n        { id: '2', icon: 'circle-check', text: this.labels.workflows, route: '' },\r\n        { id: '3', icon: 'bot', text: this.labels.tools, route: '' },\r\n      ];\r\n      this.quickActions = [\r\n        {\r\n          icon: 'awe_agents',\r\n          label: this.labels.agents,\r\n          route: '',\r\n        },\r\n        {\r\n          icon: 'awe_workflows',\r\n          label: this.labels.workflows,\r\n          route: '',\r\n        },\r\n        {\r\n          icon: 'awe_tools',\r\n          label: this.labels.tools,\r\n          route: '',\r\n        },\r\n      ];\r\n      this.searchForm = this.fb.group({\r\n        search: [''],\r\n      });\r\n    }\r\n  \r\n    ngOnInit(): void {\r\n      this.searchList();\r\n      this.totalApprovals = 60;\r\n      this.loadToolReviews();\r\n    }\r\n  \r\n    public searchList() {\r\n      console.log(this.searchForm.get('search')?.value);\r\n      this.searchForm\r\n        .get('search')!\r\n        .valueChanges.pipe(\r\n          startWith(''),\r\n          debounceTime(300),\r\n          distinctUntilChanged(),\r\n          map((value) => value?.toLowerCase() ?? ''),\r\n        )\r\n        .subscribe((searchText) => {\r\n          this.applyFilter(searchText);\r\n        });\r\n    }\r\n  \r\n    public applyFilter(text: string) {\r\n      const lower = text;\r\n  \r\n      if(!text){\r\n        this.updateConsoleApproval(this.toolReviews, 'tool');\r\n        return;\r\n      }\r\n  \r\n      \r\n      this.filteredToolReviews = this.toolReviews.filter((item) =>\r\n          item.toolName?.toLowerCase().includes(lower),\r\n      );\r\n\r\n      this.updateConsoleApproval(this.filteredToolReviews, 'tool');\r\n    }\r\n  \r\n    public onSelectionChange(data: any) {\r\n      console.log('Selection changed:', data);\r\n    }\r\n  \r\n    public uClick(i: any) {\r\n      console.log('log' + i);\r\n    }\r\n  \r\n    public toggleQuickActions(): void {\r\n      this.quickActionsExpanded = !this.quickActionsExpanded;\r\n    }\r\n  \r\n    public onBasicCollapseToggle(isCollapsed: boolean): void {\r\n      this.isBasicCollapsed = isCollapsed;\r\n      console.log('Basic sidebar collapsed:', isCollapsed);\r\n    }\r\n  \r\n    public onBasicItemClick(item: any): void {\r\n      this.basicSidebarItems.forEach((i) => (i.active = false));\r\n      item.active = true;\r\n      console.log(item);\r\n    }\r\n  \r\n    public toRequestStatus(value: string | null | undefined): RequestStatus {\r\n      return value === 'approved' || value === 'rejected' || value === 'review'\r\n        ? value\r\n        : 'review';\r\n    }\r\n  \r\n    public loadToolReviews() {\r\n      this.approvalService\r\n        .getAllReviewTools(this.currentToolsPage, this.pageSize, this.isDeleted)\r\n        .subscribe((response) => {\r\n          if (this.currentToolsPage > 1) {\r\n            this.toolReviews = [\r\n              ...this.toolReviews,\r\n              ...response.userToolReviewDetails,\r\n            ];\r\n          } else {\r\n            this.toolReviews = response?.userToolReviewDetails;\r\n          }\r\n          this.toolReviews = this.toolReviews.filter(\r\n            (r) => r.status !== 'approved',\r\n          );\r\n          this.filteredToolReviews = this.toolReviews;\r\n          // console.log('tool reviews ', this.toolReviews);\r\n          this.totalRecords = this.toolReviews.length;\r\n          this.updateConsoleApproval(this.toolReviews, 'tool');\r\n        });\r\n    }\r\n  \r\n    public loadMoreTools(page: number) {\r\n      this.currentToolsPage = page;\r\n      this.loadToolReviews();\r\n    }\r\n  \r\n  \r\n    public loadReviews(name: string) {\r\n      this.currentTab = name;\r\n      this.loadToolReviews();\r\n    }\r\n  \r\n    public rejectApproval(idx: any) {\r\n      console.log(idx);\r\n      this.selectedIndex = idx;\r\n    }\r\n\r\n    public approveApproval(idx: any) {\r\n      console.log(idx);\r\n      this.selectedIndex = idx;\r\n      console.log(this.filteredToolReviews[this.selectedIndex]);\r\n    }\r\n  \r\n    public handleApproval() {\r\n      this.handleToolApproval();\r\n    }\r\n  \r\n    public handleRejection(feedback: any) {\r\n      console.log(this.selectedIndex);\r\n      this.handleToolRejection(feedback);\r\n    }\r\n\r\n    private showApprovalDialog(): void {\r\n      this.dialogService.confirmation({\r\n        title: this.labels.confirmApproval,\r\n        message: `${this.labels.youAreAboutToApproveThis} Tool. ${this.labels.itWillBeActiveAndAvailableIn} ${this.currentTab} ${this.labels.catalogueForUsersToExecute}`,\r\n        confirmButtonText: this.labels.approve,\r\n        cancelButtonText: 'Cancel',\r\n        confirmButtonVariant: 'danger',\r\n      icon:'circle-check'\r\n      }).then(result => {\r\n        if (result.confirmed) {\r\n          this.handleApproval();\r\n        }\r\n      });\r\n    }\r\n\r\n    private showFeedbackDialog(): void {\r\n      const customButtons: DialogButton[] = [\r\n                { label: 'Cancel', variant: 'secondary', action: 'cancel' },\r\n                { label: 'Send Back', variant: 'primary', action: 'sendback' }\r\n              ];\r\n      this.dialogService.feedback({\r\n        title: 'Confirm Send Back',\r\n        message: 'This Tool will be send back for corrections and modification. Kindly comment what needs to be done.',\r\n        buttons:customButtons\r\n      }).then(result => {\r\n        if (result.confirmed && result.confirmed === true) {\r\n          this.handleRejection(result.data);\r\n        }\r\n      });\r\n    }\r\n\r\n    public handleMetaDataApproval(){\r\n      this.showApprovalDialog();\r\n    }\r\n\r\n    public handeMetaDataSendback(){\r\n      this.showFeedbackDialog();\r\n    }\r\n\r\n    public onCardClick(index: number): void {\r\n      console.log('Selected card index:', index);\r\n      this.selectedIndex = index;\r\n      const selectedTool = this.filteredToolReviews[this.selectedIndex];\r\n      this.loadPreviewData(selectedTool);\r\n      this.selectedToolId = selectedTool.toolId;\r\n\r\n      this.drawerService.open(AgentsPreviewPanelComponent, {\r\n        previewData: this.previewData,\r\n        closePreview: () => this.drawerService.clear(),\r\n        editTool: () => this.handleEditTool(selectedTool?.toolId),\r\n        rejectApproval: () => this.handeMetaDataSendback(),\r\n        approveApproval: () => this.handleMetaDataApproval(),\r\n        testApproval: () => this.redirectToToolPlayground(),\r\n      });\r\n      console.log(selectedTool);\r\n    }\r\n\r\n    private loadPreviewData(item: any): void {\r\n      this.previewData = {\r\n        type: 'tool',\r\n        title: item.toolName,\r\n        data: { ...item },\r\n        loading: true,\r\n        error: null,\r\n      };\r\n      console.log(this.previewData);\r\n      this.loadToolDetails(item);\r\n    }\r\n\r\n    private loadToolDetails(item: any): void {\r\n      if (item.id && this.toolsService.getUserToolDetails) {\r\n        // Extract numeric ID from string like \"user-423\"\r\n        let toolId: number;\r\n  \r\n        if (typeof item.id === 'string' && item.id.startsWith('user-')) {\r\n          // Extract number from \"user-423\" format\r\n          const numericPart = item.id.replace('user-', '');\r\n          toolId = Number(numericPart);\r\n        } else {\r\n          toolId = Number(item.id);\r\n        }\r\n  \r\n        if (isNaN(toolId)) {\r\n          console.warn('Invalid tool ID:', item.id, 'Using fallback data');\r\n          this.previewData.loading = false;\r\n          return;\r\n        }\r\n  \r\n        this.toolsService.getUserToolDetails(toolId).subscribe({\r\n          next: (response: any) => {\r\n            console.log('Tool details response:', response); \r\n            let toolDetail, toolConfigs;\r\n  \r\n            if (response.userToolDetail) {\r\n              toolDetail = response.userToolDetail;\r\n              toolConfigs = toolDetail.toolConfigs || {};\r\n            } else if (response.tools && response.tools[0]) {\r\n              const tool = response.tools[0];\r\n              toolDetail = {\r\n                id: tool.toolId,\r\n                name: tool.toolName,\r\n                description: tool.toolDescription,\r\n                createdBy: tool.createdBy,\r\n                createdAt: tool.createTimestamp,\r\n                modifiedAt: tool.updateTimestamp,\r\n                isDeleted: !tool.isApproved,\r\n              };\r\n              toolConfigs = {\r\n                tool_class_name: [tool.toolClassName],\r\n                tool_class_def: [tool.toolClassDef],\r\n              };\r\n            } else {\r\n              // Fallback\r\n              toolDetail = response;\r\n              toolConfigs = {};\r\n            }\r\n  \r\n            // Extract functionality/code from various possible fields\r\n            let functionality = 'Tool code not found';\r\n            if (toolConfigs.tool_class_def && toolConfigs.tool_class_def[0]) {\r\n              functionality = toolConfigs.tool_class_def[0];\r\n            } else if (toolDetail.toolClassDef) {\r\n              functionality = toolDetail.toolClassDef;\r\n            } else if (toolDetail.code) {\r\n              functionality = toolDetail.code;\r\n            } else if (toolDetail.definition) {\r\n              functionality = toolDetail.definition;\r\n            }\r\n  \r\n            this.previewData.data = {\r\n              ...this.previewData.data,\r\n              id: toolDetail.id,\r\n              name: toolDetail.name || item.name,\r\n              description: toolDetail.description || item.description,\r\n              className:\r\n                toolConfigs.tool_class_name?.[0] ||\r\n                toolDetail.toolClassName ||\r\n                'Unknown',\r\n              functionality: functionality,\r\n              isApproved: !toolDetail.isDeleted,\r\n              createdBy: toolDetail.createdBy || 'Unknown',\r\n              createdOn:\r\n                toolDetail.createdAt ||\r\n                toolDetail.createTimestamp ||\r\n                new Date().toISOString(),\r\n              modifiedBy: toolDetail.modifiedBy,\r\n              modifiedAt: toolDetail.modifiedAt || toolDetail.updateTimestamp,\r\n              isDeleted: toolDetail.isDeleted || false,\r\n            };\r\n            this.previewData.loading = false;\r\n          },\r\n          error: (error: any) => {\r\n            console.error('Error loading tool details:', error);\r\n            this.previewData.error = 'Failed to load tool details';\r\n            this.previewData.loading = false;\r\n          },\r\n        });\r\n      } else {\r\n        this.previewData.loading = false;\r\n      }\r\n    }\r\n\r\n    public handleEditTool(toolId: number) {\r\n      console.log('Edit tool', toolId);\r\n      // this.drawerService.clear();\r\n      // this.router.navigate(['/libraries/tools/edit', toolId]);\r\n    }\r\n  \r\n    public handleToolApproval() {\r\n      const toolDetails = this.filteredToolReviews[this.selectedIndex];\r\n      const id = toolDetails.id;\r\n      const toolId = toolDetails.toolId;\r\n      const status = 'approved';\r\n      const reviewedBy = toolDetails.reviewedBy;\r\n\r\n      // Show loading dialog\r\n      this.dialogService.loading({\r\n        title: 'Approving Tool...',\r\n        message: 'Please wait while we approve the tool.',\r\n        showProgress: false,\r\n        showCancelButton: false\r\n      });\r\n\r\n      this.approvalService.approveTool(id, toolId, status, reviewedBy).subscribe({\r\n        next: (response: any) => {\r\n          this.dialogService.close(); // Close loading dialog\r\n          \r\n          const message = response?.message || this.labels.toolSuccessApproveMessage;\r\n          this.dialogService.success({\r\n            title: 'Tool Approved',\r\n            message: message,\r\n          }).then(result => {\r\n            if (result.action === 'secondary') {\r\n              // Navigate to edit tool screen\r\n              this.router.navigate(['/libraries/tools/edit', toolId]);\r\n            } else {\r\n              this.loadToolReviews(); // Refresh the list\r\n            }\r\n          });\r\n        },\r\n        error: (error) => {\r\n          this.dialogService.close(); // Close loading dialog\r\n          \r\n          const errorMessage = error?.error?.message || this.labels.defaultErrorMessage;\r\n          this.dialogService.error({\r\n            title: 'Approval Failed',\r\n            message: errorMessage,\r\n            showRetryButton: true,\r\n            retryButtonText: 'Retry'\r\n          }).then(result => {\r\n            if (result.action === 'retry') {\r\n              this.handleToolApproval();\r\n            }\r\n          });\r\n        },\r\n      });\r\n    }\r\n  \r\n    public handleToolRejection(feedback: any) {\r\n      const toolDetails = this.filteredToolReviews[this.selectedIndex];\r\n      const id = toolDetails.id;\r\n      const toolId = toolDetails.toolId;\r\n      const status = 'rejected';\r\n      const reviewedBy = toolDetails.reviewedBy;\r\n      const message = feedback;\r\n\r\n      // Show loading dialog\r\n      this.dialogService.loading({\r\n        title: 'Rejecting Tool...',\r\n        message: 'Please wait while we reject the tool.',\r\n        showProgress: false,\r\n        showCancelButton: false\r\n      });\r\n      \r\n      this.approvalService\r\n        .rejectTool(id, toolId, status, reviewedBy, message)\r\n        .subscribe({\r\n          next: (response: any) => {\r\n            this.dialogService.close(); // Close loading dialog\r\n            const message = response?.message || this.labels.toolSuccessRejectMessage;\r\n            this.dialogService.success({\r\n              title: 'Tool Rejected',\r\n              message: message\r\n            }).then(() => {\r\n              this.loadToolReviews(); // Refresh the list\r\n            });\r\n          },\r\n          error: (error) => {\r\n            this.dialogService.close(); // Close loading dialog\r\n            const errorMessage = error?.error?.message || this.labels.defaultErrorMessage;\r\n            this.dialogService.error({\r\n              title: 'Rejection Failed',\r\n              message: errorMessage,\r\n              showRetryButton: true,\r\n              retryButtonText: 'Retry'\r\n            }).then(result => {\r\n              if (result.action === 'retry') {\r\n                this.handleToolRejection(feedback);\r\n              }\r\n            });\r\n          },\r\n        });\r\n    }\r\n  \r\n    public handleTesting(index : any){\r\n      console.log(index);\r\n      this.selectedToolId = this.filteredToolReviews[index].toolId;\r\n    }\r\n  \r\n    public redirectToToolPlayground(): void {\r\n      this.drawerService.clear();\r\n      this.router.navigate(['/libraries/tools/execute', this.selectedToolId]);\r\n    }\r\n\r\n    public redirectToTool(): void {\r\n      this.router.navigate(['/libraries/tools/edit', this.selectedToolId]);\r\n    }\r\n  \r\n    public updateConsoleApproval(data: any[], type: string) {\r\n      this.consoleApproval = {\r\n        contents: data?.map((req: any) => {\r\n          const statusIcons: Record<RequestStatus, string> = {\r\n            approved: 'circle-check-big',\r\n            rejected: 'circle-x',\r\n            review: 'clock',\r\n          };\r\n          const statusTexts: Record<RequestStatus, string> = {\r\n            approved: this.labels.approved,\r\n            rejected: this.labels.rejected,\r\n            review: this.labels.review,\r\n          };\r\n          const statusKey = this.toRequestStatus(req?.status);\r\n          const specificId = req.toolId;\r\n          const title = req.toolName;\r\n  \r\n          return {\r\n            id: req.id,\r\n            refId: specificId,\r\n            type: type,\r\n            session1: {\r\n              title: title,\r\n              labels: [\r\n                {\r\n                  name: type,\r\n                  color: 'success',\r\n                  background: 'red',\r\n                  type: 'normal',\r\n                },\r\n                {\r\n                  name: req.changeRequestType,\r\n                  color: req.changeRequestType === 'update' ? 'error' : 'info',\r\n                  background: 'red',\r\n                  type: 'pill',\r\n                },\r\n              ],\r\n            },\r\n            session2: [\r\n              {\r\n                name: type,\r\n                color: 'default',\r\n                background: 'red',\r\n                type: 'normal',\r\n              },\r\n              {\r\n                name: req.status,\r\n                color: 'default',\r\n                background: 'red',\r\n                type: 'normal',\r\n              },\r\n            ],\r\n            session3: [\r\n              {\r\n                iconName: 'user',\r\n                label: req.requestedBy,\r\n              },\r\n              {\r\n                iconName: 'calendar-days',\r\n                label: formatDate(req?.requestedAt, 'dd MMM yyyy', 'en-IN'), \r\n              },\r\n            ],\r\n            session4: {\r\n              status: statusTexts[statusKey],\r\n              iconName: statusIcons[statusKey],\r\n            },\r\n          };\r\n        }),\r\n        footer: {},\r\n      };\r\n    }\r\n}\r\n", "<div class=\"approval-right-screen\">\r\n    <div class=\"approval-title-filter\">\r\n        <app-approval-txt-card [iconName]=\"'hourglass'\" [title]=\"labels.totalApprovals\" [value]=\"totalApprovals\"\r\n            [subtitle]=\"currentTab + ' ' + labels.whichAreRequestedForApproval\"></app-approval-txt-card>\r\n        <app-approval-txt-card [iconName]=\"'shield-alert'\" [title]=\"labels.totalApprovedApprovals\"\r\n            [value]=\"totalApprovedApprovals\"\r\n            [subtitle]=\"currentTab + ' ' + labels.whichAreApproved\"></app-approval-txt-card>\r\n        <app-approval-txt-card [iconName]=\"'hourglass'\" [title]=\"labels.totalPendingApprovals\"\r\n            [value]=\"totalPendingApprovals\"\r\n            [subtitle]=\"labels.all + ' ' + currentTab + ' ' + labels.awaitingApproval\"></app-approval-txt-card>\r\n    </div>\r\n<div class=\"filter-section\">\r\n    <div class=\"search-bars\">\r\n        <div style=\"font-size: 1.5rem; font-weight: bold; color: black;\">\r\n            {{ currentTab }} Approvals\r\n        </div>          \r\n        <div class=\"approval-card-header\">\r\n            All - {{totalRecords}} {{currentTab}}\r\n        </div>\r\n    </div>\r\n    <div class=\"textbox section\">\r\n        <div>\r\n            <form [formGroup]=\"searchForm\">\r\n                <ava-textbox [placeholder]=\"labels.searchPlaceholder\" formControlName=\"search\">\r\n                    <ava-icon slot=\"icon-start\" iconName=\"search\" [iconSize]=\"16\" iconColor=\"var(--color-brand-primary)\"></ava-icon>\r\n                </ava-textbox>\r\n            </form>\r\n        </div>\r\n    </div>\r\n</div>\r\n    \r\n    <div class=\"approval-card-section\">\r\n        @if(totalRecords > 0){        \r\n            @for (item of consoleApproval.contents; track $index){\r\n            <div class=\"approval-card-wrapper\" (click)=\"onCardClick($index)\">\r\n                <ava-approval-card height=\"300\">\r\n                    <ava-card-header>\r\n                        <ava-icon iconSize=\"20\" iconName=\"ellipsis-vertical\"></ava-icon>\r\n                        <div class=\"header\">\r\n                            <h2>{{item.session1.title}}</h2>\r\n                            <ava-tag label=\"{{currentTab}}\" color=\"info\" size=\"sm\"></ava-tag>\r\n                        </div>\r\n                    </ava-card-header>\r\n            \r\n                    <ava-card-content>\r\n                        <div class=\"a-content\">\r\n                            <div class=\"box tag-wrapper\">\r\n                                <ava-tag label=\"Individual\" size=\"sm\"></ava-tag>\r\n                                <ava-tag label=\"Ascendion\" size=\"sm\"></ava-tag>\r\n                                <ava-tag label=\"Digital Ascender\" size=\"sm\"></ava-tag>\r\n                                <ava-tag label=\"Platform Engineering\" size=\"sm\"></ava-tag>\r\n                            </div>\r\n                            <div class=\"box info-wrapper\">\r\n                                <div class=\"f\">\r\n                                    <ava-icon iconSize=\"13\" iconName=\"user\"></ava-icon>\r\n                                    <span>{{item.session3[0].label}}</span>\r\n                                </div>\r\n                                <div class=\"ml-auto s\">\r\n                                    <ava-icon iconSize=\"20\" iconName=\"calendar-days\"></ava-icon>\r\n                                    <span>{{item.session3[1].label}}</span>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </ava-card-content>\r\n            \r\n                    <ava-card-footer>\r\n                        <div class=\"footer-content\">\r\n                            <div class=\"footer-left\">\r\n                                <span class=\"ex\">Execution Status</span>\r\n                                <div>\r\n                                    <ava-icon iconSize=\"20\" iconName=\"circle-check-big\"></ava-icon>\r\n                                    <span>{{item?.session4.status}}</span>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"footer-right\">\r\n                                <ava-button label=\"Test\" (userClick)=\"handleTesting($index)\" variant=\"secondary\" size=\"medium\"\r\n                                    state=\"default\" iconName=\"play\" iconPosition=\"left\"></ava-button>\r\n                                <ava-button label=\"Sendback\" (userClick)=\"rejectApproval($index)\" variant=\"secondary\" size=\"medium\"\r\n                                    state=\"default\" iconName=\"move-left\" iconPosition=\"left\"></ava-button>\r\n                                <ava-button label=\"Approve\" (userClick)=\"approveApproval($index)\" variant=\"primary\" size=\"medium\"\r\n                                    state=\"default\" iconName=\"Check\" iconPosition=\"left\"></ava-button>\r\n                            </div>\r\n                        </div>\r\n                    </ava-card-footer>\r\n                </ava-approval-card>\r\n            </div>\r\n            }\r\n        }\r\n        @else{\r\n            <div class=\"no-pending-message\">\r\n                All {{currentTab}} have been successfully approved. No pending actions.\r\n            </div>\r\n        }\r\n    </div>\r\n</div>\r\n\r\n"], "mappings": "AAAA,SAASA,YAAY,EAAEC,UAAU,QAAQ,iBAAiB;AAE1D,SAAiCC,mBAAmB,QAAQ,gBAAgB;AAC5E,SAAiBC,YAAY,QAAQ,iBAAiB;AACtD,SAASC,qBAAqB,EAAEC,aAAa,EAAEC,mBAAmB,EAAiEC,eAAe,EAAkBC,eAAe,QAAqC,wBAAwB;AAChP,OAAOC,YAAY,MAAO,4BAA4B;AAGtD,SAASC,YAAY,EAAEC,oBAAoB,EAAEC,GAAG,EAAEC,SAAS,QAAQ,MAAM;AAGzE,SAASC,2BAA2B,QAAQ,uDAAuD;AACnG,SAASC,wBAAwB,QAAQ,oDAAoD;;;;;;;;;;;;ICsBjFC,EAAA,CAAAC,cAAA,cAAiE;IAA9BD,EAAA,CAAAE,UAAA,mBAAAC,0EAAA;MAAA,MAAAC,SAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA,EAAAC,MAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAG,WAAA,CAAAP,SAAA,CAAmB;IAAA,EAAC;IAExDJ,EADJ,CAAAC,cAAA,4BAAgC,sBACX;IACbD,EAAA,CAAAY,SAAA,mBAAgE;IAE5DZ,EADJ,CAAAC,cAAA,cAAoB,SACZ;IAAAD,EAAA,CAAAa,MAAA,GAAuB;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAChCd,EAAA,CAAAY,SAAA,kBAAiE;IAEzEZ,EADI,CAAAc,YAAA,EAAM,EACQ;IAIVd,EAFR,CAAAC,cAAA,uBAAkB,cACS,eACU;IAIzBD,EAHA,CAAAY,SAAA,mBAAgD,mBACD,mBACO,mBACI;IAC9DZ,EAAA,CAAAc,YAAA,EAAM;IAEFd,EADJ,CAAAC,cAAA,eAA8B,eACX;IACXD,EAAA,CAAAY,SAAA,oBAAmD;IACnDZ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAa,MAAA,IAA0B;IACpCb,EADoC,CAAAc,YAAA,EAAO,EACrC;IACNd,EAAA,CAAAC,cAAA,eAAuB;IACnBD,EAAA,CAAAY,SAAA,oBAA4D;IAC5DZ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAa,MAAA,IAA0B;IAIhDb,EAJgD,CAAAc,YAAA,EAAO,EACrC,EACJ,EACJ,EACS;IAKPd,EAHZ,CAAAC,cAAA,uBAAiB,eACe,eACC,gBACJ;IAAAD,EAAA,CAAAa,MAAA,wBAAgB;IAAAb,EAAA,CAAAc,YAAA,EAAO;IACxCd,EAAA,CAAAC,cAAA,WAAK;IACDD,EAAA,CAAAY,SAAA,oBAA+D;IAC/DZ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAa,MAAA,IAAyB;IAEvCb,EAFuC,CAAAc,YAAA,EAAO,EACpC,EACJ;IAEFd,EADJ,CAAAC,cAAA,eAA0B,sBAEkC;IAD/BD,EAAA,CAAAE,UAAA,uBAAAa,sFAAA;MAAA,MAAAX,SAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA,EAAAC,MAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAaF,MAAA,CAAAQ,aAAA,CAAAZ,SAAA,CAAqB;IAAA,EAAC;IACJJ,EAAA,CAAAc,YAAA,EAAa;IACrEd,EAAA,CAAAC,cAAA,sBAC6D;IADhCD,EAAA,CAAAE,UAAA,uBAAAe,sFAAA;MAAA,MAAAb,SAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA,EAAAC,MAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAaF,MAAA,CAAAU,cAAA,CAAAd,SAAA,CAAsB;IAAA,EAAC;IACJJ,EAAA,CAAAc,YAAA,EAAa;IAC1Ed,EAAA,CAAAC,cAAA,sBACyD;IAD7BD,EAAA,CAAAE,UAAA,uBAAAiB,sFAAA;MAAA,MAAAf,SAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA,EAAAC,MAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAaF,MAAA,CAAAY,eAAA,CAAAhB,SAAA,CAAuB;IAAA,EAAC;IAMrFJ,EAL6E,CAAAc,YAAA,EAAa,EACpE,EACJ,EACQ,EACF,EAClB;;;;;IA9Ccd,EAAA,CAAAqB,SAAA,GAAuB;IAAvBrB,EAAA,CAAAsB,iBAAA,CAAAC,OAAA,CAAAC,QAAA,CAAAC,KAAA,CAAuB;IAClBzB,EAAA,CAAAqB,SAAA,EAAsB;IAAtBrB,EAAA,CAAA0B,qBAAA,UAAAlB,MAAA,CAAAmB,UAAA,CAAsB;IAejB3B,EAAA,CAAAqB,SAAA,IAA0B;IAA1BrB,EAAA,CAAAsB,iBAAA,CAAAC,OAAA,CAAAK,QAAA,IAAAC,KAAA,CAA0B;IAI1B7B,EAAA,CAAAqB,SAAA,GAA0B;IAA1BrB,EAAA,CAAAsB,iBAAA,CAAAC,OAAA,CAAAK,QAAA,IAAAC,KAAA,CAA0B;IAY1B7B,EAAA,CAAAqB,SAAA,GAAyB;IAAzBrB,EAAA,CAAAsB,iBAAA,CAAAC,OAAA,kBAAAA,OAAA,CAAAO,QAAA,CAAAC,MAAA,CAAyB;;;;;IAtCvD/B,EAAA,CAAAgC,gBAAA,IAAAC,oDAAA,oBAAAjC,EAAA,CAAAkC,sBAAA,CAqDC;;;;IArDDlC,EAAA,CAAAmC,UAAA,CAAA3B,MAAA,CAAA4B,eAAA,CAAAC,QAAA,CAqDC;;;;;IAGDrC,EAAA,CAAAC,cAAA,cAAgC;IAC5BD,EAAA,CAAAa,MAAA,GACJ;IAAAb,EAAA,CAAAc,YAAA,EAAM;;;;IADFd,EAAA,CAAAqB,SAAA,EACJ;IADIrB,EAAA,CAAAsC,kBAAA,UAAA9B,MAAA,CAAAmB,UAAA,2DACJ;;;ADvDZ,WAAaY,sBAAsB;EAA7B,MAAOA,sBAAsB;IAqCrBC,MAAA;IACAC,UAAA;IACAC,eAAA;IACAC,EAAA;IACAC,YAAA;IACAC,aAAA;IACAC,aAAA;IA1CVC,SAAS,GAAGtD,YAAY,CAACuD,MAAM;IAExBC,sBAAsB,GAAW,EAAE;IACnCC,qBAAqB,GAAW,EAAE;IAClCC,cAAc,GAAW,EAAE;IAC3BC,gBAAgB,GAAY,KAAK;IACjCC,oBAAoB,GAAY,IAAI;IACpCjB,eAAe,GAAQ,EAAE;IACzBkB,OAAO,GAAqB,EAAE;IAC9BC,iBAAiB,GAAU,EAAE;IAC7BC,YAAY,GAAU,EAAE;IACxBC,WAAW,GAAU,EAAE;IACvBC,mBAAmB,GAAU,EAAE;IAC/BC,eAAe,GAAU,EAAE;IAC3BC,aAAa,GAAU,EAAE;IACzBC,gBAAgB,GAAG,CAAC;IACpBC,iBAAiB,GAAG,CAAC;IACrBC,oBAAoB,GAAG,CAAC;IACxBC,QAAQ,GAAG,EAAE;IACbC,YAAY,GAAG,CAAC;IAChBC,SAAS,GAAG,KAAK;IACjBvC,UAAU,GAAG,OAAO;IACpBwC,qBAAqB,GAAG,KAAK;IAC7BC,aAAa,GAAG,KAAK;IACrBC,cAAc,GAAG,KAAK;IACtBC,WAAW,GAAG,EAAE;IAChBC,aAAa,GAAG,CAAC;IACjBC,iBAAiB,GAAG,KAAK;IACzBC,UAAU;IACVzB,MAAM,GAAQvD,YAAY,CAACuD,MAAM;IACjC0B,eAAe,GAAkB,IAAI;IACrCC,WAAW,GAAQ,IAAI;IACvBC,cAAc,GAAW,EAAE;IAC3BC,eAAe,GAAW,EAAE;IAEnCC,YACUtC,MAAc,EACdC,UAAmC,EACnCC,eAAgC,EAChCC,EAAe,EACfC,YAA0B,EAC1BC,aAA4B,EAC5BC,aAA4B;MAN5B,KAAAN,MAAM,GAANA,MAAM;MACN,KAAAC,UAAU,GAAVA,UAAU;MACV,KAAAC,eAAe,GAAfA,eAAe;MACf,KAAAC,EAAE,GAAFA,EAAE;MACF,KAAAC,YAAY,GAAZA,YAAY;MACZ,KAAAC,aAAa,GAAbA,aAAa;MACb,KAAAC,aAAa,GAAbA,aAAa;MAErB,IAAI,CAACE,MAAM,GAAGvD,YAAY,CAACuD,MAAM;MACjC,IAAI,CAACM,OAAO,GAAG,CACb;QAAEyB,IAAI,EAAE,IAAI,CAAC/B,MAAM,CAACgC,WAAW;QAAEC,KAAK,EAAE;MAAa,CAAE,EACvD;QAAEF,IAAI,EAAE,IAAI,CAAC/B,MAAM,CAACkC,QAAQ;QAAED,KAAK,EAAE;MAAU,CAAE,EACjD;QAAEF,IAAI,EAAE,IAAI,CAAC/B,MAAM,CAACmC,KAAK;QAAEF,KAAK,EAAE;MAAO,CAAE,CAC5C;MACD,IAAI,CAAC1B,iBAAiB,GAAG,CACvB;QAAE6B,EAAE,EAAE,GAAG;QAAEC,IAAI,EAAE,QAAQ;QAAEC,IAAI,EAAE,IAAI,CAACtC,MAAM,CAACuC,MAAM;QAAEC,KAAK,EAAE,EAAE;QAAEC,MAAM,EAAE;MAAI,CAAE,EAC9E;QAAEL,EAAE,EAAE,GAAG;QAAEC,IAAI,EAAE,cAAc;QAAEC,IAAI,EAAE,IAAI,CAACtC,MAAM,CAAC0C,SAAS;QAAEF,KAAK,EAAE;MAAE,CAAE,EACzE;QAAEJ,EAAE,EAAE,GAAG;QAAEC,IAAI,EAAE,KAAK;QAAEC,IAAI,EAAE,IAAI,CAACtC,MAAM,CAAC2C,KAAK;QAAEH,KAAK,EAAE;MAAE,CAAE,CAC7D;MACD,IAAI,CAAChC,YAAY,GAAG,CAClB;QACE6B,IAAI,EAAE,YAAY;QAClBxD,KAAK,EAAE,IAAI,CAACmB,MAAM,CAACuC,MAAM;QACzBC,KAAK,EAAE;OACR,EACD;QACEH,IAAI,EAAE,eAAe;QACrBxD,KAAK,EAAE,IAAI,CAACmB,MAAM,CAAC0C,SAAS;QAC5BF,KAAK,EAAE;OACR,EACD;QACEH,IAAI,EAAE,WAAW;QACjBxD,KAAK,EAAE,IAAI,CAACmB,MAAM,CAAC2C,KAAK;QACxBH,KAAK,EAAE;OACR,CACF;MACD,IAAI,CAACf,UAAU,GAAG,IAAI,CAAC9B,EAAE,CAACiD,KAAK,CAAC;QAC9BC,MAAM,EAAE,CAAC,EAAE;OACZ,CAAC;IACJ;IAEAC,QAAQA,CAAA;MACN,IAAI,CAACC,UAAU,EAAE;MACjB,IAAI,CAAC5C,cAAc,GAAG,EAAE;MACxB,IAAI,CAAC6C,eAAe,EAAE;IACxB;IAEOD,UAAUA,CAAA;MACfE,OAAO,CAACC,GAAG,CAAC,IAAI,CAACzB,UAAU,CAAC0B,GAAG,CAAC,QAAQ,CAAC,EAAElB,KAAK,CAAC;MACjD,IAAI,CAACR,UAAU,CACZ0B,GAAG,CAAC,QAAQ,CAAE,CACdC,YAAY,CAACC,IAAI,CAChBxG,SAAS,CAAC,EAAE,CAAC,EACbH,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBC,GAAG,CAAEqF,KAAK,IAAKA,KAAK,EAAEqB,WAAW,EAAE,IAAI,EAAE,CAAC,CAC3C,CACAC,SAAS,CAAEC,UAAU,IAAI;QACxB,IAAI,CAACC,WAAW,CAACD,UAAU,CAAC;MAC9B,CAAC,CAAC;IACN;IAEOC,WAAWA,CAACnB,IAAY;MAC7B,MAAMoB,KAAK,GAAGpB,IAAI;MAElB,IAAG,CAACA,IAAI,EAAC;QACP,IAAI,CAACqB,qBAAqB,CAAC,IAAI,CAAClD,WAAW,EAAE,MAAM,CAAC;QACpD;MACF;MAGA,IAAI,CAACC,mBAAmB,GAAG,IAAI,CAACD,WAAW,CAACmD,MAAM,CAAEC,IAAI,IACpDA,IAAI,CAACC,QAAQ,EAAER,WAAW,EAAE,CAACS,QAAQ,CAACL,KAAK,CAAC,CAC/C;MAED,IAAI,CAACC,qBAAqB,CAAC,IAAI,CAACjD,mBAAmB,EAAE,MAAM,CAAC;IAC9D;IAEOsD,iBAAiBA,CAACC,IAAS;MAChChB,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEe,IAAI,CAAC;IACzC;IAEOC,MAAMA,CAACC,CAAM;MAClBlB,OAAO,CAACC,GAAG,CAAC,KAAK,GAAGiB,CAAC,CAAC;IACxB;IAEOC,kBAAkBA,CAAA;MACvB,IAAI,CAAC/D,oBAAoB,GAAG,CAAC,IAAI,CAACA,oBAAoB;IACxD;IAEOgE,qBAAqBA,CAACC,WAAoB;MAC/C,IAAI,CAAClE,gBAAgB,GAAGkE,WAAW;MACnCrB,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEoB,WAAW,CAAC;IACtD;IAEOC,gBAAgBA,CAACV,IAAS;MAC/B,IAAI,CAACtD,iBAAiB,CAACiE,OAAO,CAAEL,CAAC,IAAMA,CAAC,CAAC1B,MAAM,GAAG,KAAM,CAAC;MACzDoB,IAAI,CAACpB,MAAM,GAAG,IAAI;MAClBQ,OAAO,CAACC,GAAG,CAACW,IAAI,CAAC;IACnB;IAEOY,eAAeA,CAACxC,KAAgC;MACrD,OAAOA,KAAK,KAAK,UAAU,IAAIA,KAAK,KAAK,UAAU,IAAIA,KAAK,KAAK,QAAQ,GACrEA,KAAK,GACL,QAAQ;IACd;IAEOe,eAAeA,CAAA;MACpB,IAAI,CAACtD,eAAe,CACjBgF,iBAAiB,CAAC,IAAI,CAAC7D,gBAAgB,EAAE,IAAI,CAACG,QAAQ,EAAE,IAAI,CAACE,SAAS,CAAC,CACvEqC,SAAS,CAAEoB,QAAQ,IAAI;QACtB,IAAI,IAAI,CAAC9D,gBAAgB,GAAG,CAAC,EAAE;UAC7B,IAAI,CAACJ,WAAW,GAAG,CACjB,GAAG,IAAI,CAACA,WAAW,EACnB,GAAGkE,QAAQ,CAACC,qBAAqB,CAClC;QACH,CAAC,MAAM;UACL,IAAI,CAACnE,WAAW,GAAGkE,QAAQ,EAAEC,qBAAqB;QACpD;QACA,IAAI,CAACnE,WAAW,GAAG,IAAI,CAACA,WAAW,CAACmD,MAAM,CACvCiB,CAAC,IAAKA,CAAC,CAAC9F,MAAM,KAAK,UAAU,CAC/B;QACD,IAAI,CAAC2B,mBAAmB,GAAG,IAAI,CAACD,WAAW;QAC3C;QACA,IAAI,CAACQ,YAAY,GAAG,IAAI,CAACR,WAAW,CAACqE,MAAM;QAC3C,IAAI,CAACnB,qBAAqB,CAAC,IAAI,CAAClD,WAAW,EAAE,MAAM,CAAC;MACtD,CAAC,CAAC;IACN;IAEOsE,aAAaA,CAACC,IAAY;MAC/B,IAAI,CAACnE,gBAAgB,GAAGmE,IAAI;MAC5B,IAAI,CAAChC,eAAe,EAAE;IACxB;IAGOiC,WAAWA,CAAClD,IAAY;MAC7B,IAAI,CAACpD,UAAU,GAAGoD,IAAI;MACtB,IAAI,CAACiB,eAAe,EAAE;IACxB;IAEO9E,cAAcA,CAACgH,GAAQ;MAC5BjC,OAAO,CAACC,GAAG,CAACgC,GAAG,CAAC;MAChB,IAAI,CAAC3D,aAAa,GAAG2D,GAAG;IAC1B;IAEO9G,eAAeA,CAAC8G,GAAQ;MAC7BjC,OAAO,CAACC,GAAG,CAACgC,GAAG,CAAC;MAChB,IAAI,CAAC3D,aAAa,GAAG2D,GAAG;MACxBjC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACxC,mBAAmB,CAAC,IAAI,CAACa,aAAa,CAAC,CAAC;IAC3D;IAEO4D,cAAcA,CAAA;MACnB,IAAI,CAACC,kBAAkB,EAAE;IAC3B;IAEOC,eAAeA,CAACC,QAAa;MAClCrC,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC3B,aAAa,CAAC;MAC/B,IAAI,CAACgE,mBAAmB,CAACD,QAAQ,CAAC;IACpC;IAEQE,kBAAkBA,CAAA;MACxB,IAAI,CAAC1F,aAAa,CAAC2F,YAAY,CAAC;QAC9BhH,KAAK,EAAE,IAAI,CAACuB,MAAM,CAAC0F,eAAe;QAClCC,OAAO,EAAE,GAAG,IAAI,CAAC3F,MAAM,CAAC4F,wBAAwB,UAAU,IAAI,CAAC5F,MAAM,CAAC6F,4BAA4B,IAAI,IAAI,CAAClH,UAAU,IAAI,IAAI,CAACqB,MAAM,CAAC8F,0BAA0B,EAAE;QACjKC,iBAAiB,EAAE,IAAI,CAAC/F,MAAM,CAACgG,OAAO;QACtCC,gBAAgB,EAAE,QAAQ;QAC1BC,oBAAoB,EAAE,QAAQ;QAChC7D,IAAI,EAAC;OACJ,CAAC,CAAC8D,IAAI,CAACC,MAAM,IAAG;QACf,IAAIA,MAAM,CAACC,SAAS,EAAE;UACpB,IAAI,CAAClB,cAAc,EAAE;QACvB;MACF,CAAC,CAAC;IACJ;IAEQmB,kBAAkBA,CAAA;MACxB,MAAMC,aAAa,GAAmB,CAC5B;QAAE1H,KAAK,EAAE,QAAQ;QAAE2H,OAAO,EAAE,WAAW;QAAEC,MAAM,EAAE;MAAQ,CAAE,EAC3D;QAAE5H,KAAK,EAAE,WAAW;QAAE2H,OAAO,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAU,CAAE,CAC/D;MACT,IAAI,CAAC3G,aAAa,CAACwF,QAAQ,CAAC;QAC1B7G,KAAK,EAAE,mBAAmB;QAC1BkH,OAAO,EAAE,qGAAqG;QAC9Ge,OAAO,EAACH;OACT,CAAC,CAACJ,IAAI,CAACC,MAAM,IAAG;QACf,IAAIA,MAAM,CAACC,SAAS,IAAID,MAAM,CAACC,SAAS,KAAK,IAAI,EAAE;UACjD,IAAI,CAAChB,eAAe,CAACe,MAAM,CAACnC,IAAI,CAAC;QACnC;MACF,CAAC,CAAC;IACJ;IAEO0C,sBAAsBA,CAAA;MAC3B,IAAI,CAACnB,kBAAkB,EAAE;IAC3B;IAEOoB,qBAAqBA,CAAA;MAC1B,IAAI,CAACN,kBAAkB,EAAE;IAC3B;IAEO3I,WAAWA,CAACkJ,KAAa;MAC9B5D,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE2D,KAAK,CAAC;MAC1C,IAAI,CAACtF,aAAa,GAAGsF,KAAK;MAC1B,MAAMC,YAAY,GAAG,IAAI,CAACpG,mBAAmB,CAAC,IAAI,CAACa,aAAa,CAAC;MACjE,IAAI,CAACwF,eAAe,CAACD,YAAY,CAAC;MAClC,IAAI,CAAClF,cAAc,GAAGkF,YAAY,CAACE,MAAM;MAEzC,IAAI,CAACnH,aAAa,CAACoH,IAAI,CAACnK,2BAA2B,EAAE;QACnD6E,WAAW,EAAE,IAAI,CAACA,WAAW;QAC7BuF,YAAY,EAAEA,CAAA,KAAM,IAAI,CAACrH,aAAa,CAACsH,KAAK,EAAE;QAC9CC,QAAQ,EAAEA,CAAA,KAAM,IAAI,CAACC,cAAc,CAACP,YAAY,EAAEE,MAAM,CAAC;QACzD9I,cAAc,EAAEA,CAAA,KAAM,IAAI,CAAC0I,qBAAqB,EAAE;QAClDxI,eAAe,EAAEA,CAAA,KAAM,IAAI,CAACuI,sBAAsB,EAAE;QACpDW,YAAY,EAAEA,CAAA,KAAM,IAAI,CAACC,wBAAwB;OAClD,CAAC;MACFtE,OAAO,CAACC,GAAG,CAAC4D,YAAY,CAAC;IAC3B;IAEQC,eAAeA,CAAClD,IAAS;MAC/B,IAAI,CAAClC,WAAW,GAAG;QACjB6F,IAAI,EAAE,MAAM;QACZ/I,KAAK,EAAEoF,IAAI,CAACC,QAAQ;QACpBG,IAAI,EAAE;UAAE,GAAGJ;QAAI,CAAE;QACjB4D,OAAO,EAAE,IAAI;QACbC,KAAK,EAAE;OACR;MACDzE,OAAO,CAACC,GAAG,CAAC,IAAI,CAACvB,WAAW,CAAC;MAC7B,IAAI,CAACgG,eAAe,CAAC9D,IAAI,CAAC;IAC5B;IAEQ8D,eAAeA,CAAC9D,IAAS;MAC/B,IAAIA,IAAI,CAACzB,EAAE,IAAI,IAAI,CAACxC,YAAY,CAACgI,kBAAkB,EAAE;QACnD;QACA,IAAIZ,MAAc;QAElB,IAAI,OAAOnD,IAAI,CAACzB,EAAE,KAAK,QAAQ,IAAIyB,IAAI,CAACzB,EAAE,CAACyF,UAAU,CAAC,OAAO,CAAC,EAAE;UAC9D;UACA,MAAMC,WAAW,GAAGjE,IAAI,CAACzB,EAAE,CAAC2F,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;UAChDf,MAAM,GAAGgB,MAAM,CAACF,WAAW,CAAC;QAC9B,CAAC,MAAM;UACLd,MAAM,GAAGgB,MAAM,CAACnE,IAAI,CAACzB,EAAE,CAAC;QAC1B;QAEA,IAAI6F,KAAK,CAACjB,MAAM,CAAC,EAAE;UACjB/D,OAAO,CAACiF,IAAI,CAAC,kBAAkB,EAAErE,IAAI,CAACzB,EAAE,EAAE,qBAAqB,CAAC;UAChE,IAAI,CAACT,WAAW,CAAC8F,OAAO,GAAG,KAAK;UAChC;QACF;QAEA,IAAI,CAAC7H,YAAY,CAACgI,kBAAkB,CAACZ,MAAM,CAAC,CAACzD,SAAS,CAAC;UACrD4E,IAAI,EAAGxD,QAAa,IAAI;YACtB1B,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEyB,QAAQ,CAAC;YAC/C,IAAIyD,UAAU,EAAEC,WAAW;YAE3B,IAAI1D,QAAQ,CAAC2D,cAAc,EAAE;cAC3BF,UAAU,GAAGzD,QAAQ,CAAC2D,cAAc;cACpCD,WAAW,GAAGD,UAAU,CAACC,WAAW,IAAI,EAAE;YAC5C,CAAC,MAAM,IAAI1D,QAAQ,CAAChC,KAAK,IAAIgC,QAAQ,CAAChC,KAAK,CAAC,CAAC,CAAC,EAAE;cAC9C,MAAM4F,IAAI,GAAG5D,QAAQ,CAAChC,KAAK,CAAC,CAAC,CAAC;cAC9ByF,UAAU,GAAG;gBACXhG,EAAE,EAAEmG,IAAI,CAACvB,MAAM;gBACfjF,IAAI,EAAEwG,IAAI,CAACzE,QAAQ;gBACnB0E,WAAW,EAAED,IAAI,CAACE,eAAe;gBACjCC,SAAS,EAAEH,IAAI,CAACG,SAAS;gBACzBC,SAAS,EAAEJ,IAAI,CAACK,eAAe;gBAC/BC,UAAU,EAAEN,IAAI,CAACO,eAAe;gBAChC5H,SAAS,EAAE,CAACqH,IAAI,CAACQ;eAClB;cACDV,WAAW,GAAG;gBACZW,eAAe,EAAE,CAACT,IAAI,CAACU,aAAa,CAAC;gBACrCC,cAAc,EAAE,CAACX,IAAI,CAACY,YAAY;eACnC;YACH,CAAC,MAAM;cACL;cACAf,UAAU,GAAGzD,QAAQ;cACrB0D,WAAW,GAAG,EAAE;YAClB;YAEA;YACA,IAAIe,aAAa,GAAG,qBAAqB;YACzC,IAAIf,WAAW,CAACa,cAAc,IAAIb,WAAW,CAACa,cAAc,CAAC,CAAC,CAAC,EAAE;cAC/DE,aAAa,GAAGf,WAAW,CAACa,cAAc,CAAC,CAAC,CAAC;YAC/C,CAAC,MAAM,IAAId,UAAU,CAACe,YAAY,EAAE;cAClCC,aAAa,GAAGhB,UAAU,CAACe,YAAY;YACzC,CAAC,MAAM,IAAIf,UAAU,CAACiB,IAAI,EAAE;cAC1BD,aAAa,GAAGhB,UAAU,CAACiB,IAAI;YACjC,CAAC,MAAM,IAAIjB,UAAU,CAACkB,UAAU,EAAE;cAChCF,aAAa,GAAGhB,UAAU,CAACkB,UAAU;YACvC;YAEA,IAAI,CAAC3H,WAAW,CAACsC,IAAI,GAAG;cACtB,GAAG,IAAI,CAACtC,WAAW,CAACsC,IAAI;cACxB7B,EAAE,EAAEgG,UAAU,CAAChG,EAAE;cACjBL,IAAI,EAAEqG,UAAU,CAACrG,IAAI,IAAI8B,IAAI,CAAC9B,IAAI;cAClCyG,WAAW,EAAEJ,UAAU,CAACI,WAAW,IAAI3E,IAAI,CAAC2E,WAAW;cACvDe,SAAS,EACPlB,WAAW,CAACW,eAAe,GAAG,CAAC,CAAC,IAChCZ,UAAU,CAACa,aAAa,IACxB,SAAS;cACXG,aAAa,EAAEA,aAAa;cAC5BL,UAAU,EAAE,CAACX,UAAU,CAAClH,SAAS;cACjCwH,SAAS,EAAEN,UAAU,CAACM,SAAS,IAAI,SAAS;cAC5Cc,SAAS,EACPpB,UAAU,CAACO,SAAS,IACpBP,UAAU,CAACQ,eAAe,IAC1B,IAAIa,IAAI,EAAE,CAACC,WAAW,EAAE;cAC1BC,UAAU,EAAEvB,UAAU,CAACuB,UAAU;cACjCd,UAAU,EAAET,UAAU,CAACS,UAAU,IAAIT,UAAU,CAACU,eAAe;cAC/D5H,SAAS,EAAEkH,UAAU,CAAClH,SAAS,IAAI;aACpC;YACD,IAAI,CAACS,WAAW,CAAC8F,OAAO,GAAG,KAAK;UAClC,CAAC;UACDC,KAAK,EAAGA,KAAU,IAAI;YACpBzE,OAAO,CAACyE,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;YACnD,IAAI,CAAC/F,WAAW,CAAC+F,KAAK,GAAG,6BAA6B;YACtD,IAAI,CAAC/F,WAAW,CAAC8F,OAAO,GAAG,KAAK;UAClC;SACD,CAAC;MACJ,CAAC,MAAM;QACL,IAAI,CAAC9F,WAAW,CAAC8F,OAAO,GAAG,KAAK;MAClC;IACF;IAEOJ,cAAcA,CAACL,MAAc;MAClC/D,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE8D,MAAM,CAAC;MAChC;MACA;IACF;IAEO5B,kBAAkBA,CAAA;MACvB,MAAMwE,WAAW,GAAG,IAAI,CAAClJ,mBAAmB,CAAC,IAAI,CAACa,aAAa,CAAC;MAChE,MAAMa,EAAE,GAAGwH,WAAW,CAACxH,EAAE;MACzB,MAAM4E,MAAM,GAAG4C,WAAW,CAAC5C,MAAM;MACjC,MAAMjI,MAAM,GAAG,UAAU;MACzB,MAAM8K,UAAU,GAAGD,WAAW,CAACC,UAAU;MAEzC;MACA,IAAI,CAAC/J,aAAa,CAAC2H,OAAO,CAAC;QACzBhJ,KAAK,EAAE,mBAAmB;QAC1BkH,OAAO,EAAE,wCAAwC;QACjDmE,YAAY,EAAE,KAAK;QACnBC,gBAAgB,EAAE;OACnB,CAAC;MAEF,IAAI,CAACrK,eAAe,CAACsK,WAAW,CAAC5H,EAAE,EAAE4E,MAAM,EAAEjI,MAAM,EAAE8K,UAAU,CAAC,CAACtG,SAAS,CAAC;QACzE4E,IAAI,EAAGxD,QAAa,IAAI;UACtB,IAAI,CAAC7E,aAAa,CAACmK,KAAK,EAAE,CAAC,CAAC;UAE5B,MAAMtE,OAAO,GAAGhB,QAAQ,EAAEgB,OAAO,IAAI,IAAI,CAAC3F,MAAM,CAACkK,yBAAyB;UAC1E,IAAI,CAACpK,aAAa,CAACqK,OAAO,CAAC;YACzB1L,KAAK,EAAE,eAAe;YACtBkH,OAAO,EAAEA;WACV,CAAC,CAACQ,IAAI,CAACC,MAAM,IAAG;YACf,IAAIA,MAAM,CAACK,MAAM,KAAK,WAAW,EAAE;cACjC;cACA,IAAI,CAACjH,MAAM,CAAC4K,QAAQ,CAAC,CAAC,uBAAuB,EAAEpD,MAAM,CAAC,CAAC;YACzD,CAAC,MAAM;cACL,IAAI,CAAChE,eAAe,EAAE,CAAC,CAAC;YAC1B;UACF,CAAC,CAAC;QACJ,CAAC;QACD0E,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAAC5H,aAAa,CAACmK,KAAK,EAAE,CAAC,CAAC;UAE5B,MAAMI,YAAY,GAAG3C,KAAK,EAAEA,KAAK,EAAE/B,OAAO,IAAI,IAAI,CAAC3F,MAAM,CAACsK,mBAAmB;UAC7E,IAAI,CAACxK,aAAa,CAAC4H,KAAK,CAAC;YACvBjJ,KAAK,EAAE,iBAAiB;YACxBkH,OAAO,EAAE0E,YAAY;YACrBE,eAAe,EAAE,IAAI;YACrBC,eAAe,EAAE;WAClB,CAAC,CAACrE,IAAI,CAACC,MAAM,IAAG;YACf,IAAIA,MAAM,CAACK,MAAM,KAAK,OAAO,EAAE;cAC7B,IAAI,CAACrB,kBAAkB,EAAE;YAC3B;UACF,CAAC,CAAC;QACJ;OACD,CAAC;IACJ;IAEOG,mBAAmBA,CAACD,QAAa;MACtC,MAAMsE,WAAW,GAAG,IAAI,CAAClJ,mBAAmB,CAAC,IAAI,CAACa,aAAa,CAAC;MAChE,MAAMa,EAAE,GAAGwH,WAAW,CAACxH,EAAE;MACzB,MAAM4E,MAAM,GAAG4C,WAAW,CAAC5C,MAAM;MACjC,MAAMjI,MAAM,GAAG,UAAU;MACzB,MAAM8K,UAAU,GAAGD,WAAW,CAACC,UAAU;MACzC,MAAMlE,OAAO,GAAGL,QAAQ;MAExB;MACA,IAAI,CAACxF,aAAa,CAAC2H,OAAO,CAAC;QACzBhJ,KAAK,EAAE,mBAAmB;QAC1BkH,OAAO,EAAE,uCAAuC;QAChDmE,YAAY,EAAE,KAAK;QACnBC,gBAAgB,EAAE;OACnB,CAAC;MAEF,IAAI,CAACrK,eAAe,CACjB+K,UAAU,CAACrI,EAAE,EAAE4E,MAAM,EAAEjI,MAAM,EAAE8K,UAAU,EAAElE,OAAO,CAAC,CACnDpC,SAAS,CAAC;QACT4E,IAAI,EAAGxD,QAAa,IAAI;UACtB,IAAI,CAAC7E,aAAa,CAACmK,KAAK,EAAE,CAAC,CAAC;UAC5B,MAAMtE,OAAO,GAAGhB,QAAQ,EAAEgB,OAAO,IAAI,IAAI,CAAC3F,MAAM,CAAC0K,wBAAwB;UACzE,IAAI,CAAC5K,aAAa,CAACqK,OAAO,CAAC;YACzB1L,KAAK,EAAE,eAAe;YACtBkH,OAAO,EAAEA;WACV,CAAC,CAACQ,IAAI,CAAC,MAAK;YACX,IAAI,CAACnD,eAAe,EAAE,CAAC,CAAC;UAC1B,CAAC,CAAC;QACJ,CAAC;QACD0E,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAAC5H,aAAa,CAACmK,KAAK,EAAE,CAAC,CAAC;UAC5B,MAAMI,YAAY,GAAG3C,KAAK,EAAEA,KAAK,EAAE/B,OAAO,IAAI,IAAI,CAAC3F,MAAM,CAACsK,mBAAmB;UAC7E,IAAI,CAACxK,aAAa,CAAC4H,KAAK,CAAC;YACvBjJ,KAAK,EAAE,kBAAkB;YACzBkH,OAAO,EAAE0E,YAAY;YACrBE,eAAe,EAAE,IAAI;YACrBC,eAAe,EAAE;WAClB,CAAC,CAACrE,IAAI,CAACC,MAAM,IAAG;YACf,IAAIA,MAAM,CAACK,MAAM,KAAK,OAAO,EAAE;cAC7B,IAAI,CAAClB,mBAAmB,CAACD,QAAQ,CAAC;YACpC;UACF,CAAC,CAAC;QACJ;OACD,CAAC;IACN;IAEOtH,aAAaA,CAAC6I,KAAW;MAC9B5D,OAAO,CAACC,GAAG,CAAC2D,KAAK,CAAC;MAClB,IAAI,CAACjF,cAAc,GAAG,IAAI,CAAClB,mBAAmB,CAACmG,KAAK,CAAC,CAACG,MAAM;IAC9D;IAEOO,wBAAwBA,CAAA;MAC7B,IAAI,CAAC1H,aAAa,CAACsH,KAAK,EAAE;MAC1B,IAAI,CAAC3H,MAAM,CAAC4K,QAAQ,CAAC,CAAC,0BAA0B,EAAE,IAAI,CAACxI,cAAc,CAAC,CAAC;IACzE;IAEO+I,cAAcA,CAAA;MACnB,IAAI,CAACnL,MAAM,CAAC4K,QAAQ,CAAC,CAAC,uBAAuB,EAAE,IAAI,CAACxI,cAAc,CAAC,CAAC;IACtE;IAEO+B,qBAAqBA,CAACM,IAAW,EAAEuD,IAAY;MACpD,IAAI,CAACpI,eAAe,GAAG;QACrBC,QAAQ,EAAE4E,IAAI,EAAErH,GAAG,CAAEgO,GAAQ,IAAI;UAC/B,MAAMC,WAAW,GAAkC;YACjDC,QAAQ,EAAE,kBAAkB;YAC5BC,QAAQ,EAAE,UAAU;YACpBC,MAAM,EAAE;WACT;UACD,MAAMC,WAAW,GAAkC;YACjDH,QAAQ,EAAE,IAAI,CAAC9K,MAAM,CAAC8K,QAAQ;YAC9BC,QAAQ,EAAE,IAAI,CAAC/K,MAAM,CAAC+K,QAAQ;YAC9BC,MAAM,EAAE,IAAI,CAAChL,MAAM,CAACgL;WACrB;UACD,MAAME,SAAS,GAAG,IAAI,CAACzG,eAAe,CAACmG,GAAG,EAAE7L,MAAM,CAAC;UACnD,MAAMoM,UAAU,GAAGP,GAAG,CAAC5D,MAAM;UAC7B,MAAMvI,KAAK,GAAGmM,GAAG,CAAC9G,QAAQ;UAE1B,OAAO;YACL1B,EAAE,EAAEwI,GAAG,CAACxI,EAAE;YACVgJ,KAAK,EAAED,UAAU;YACjB3D,IAAI,EAAEA,IAAI;YACVhJ,QAAQ,EAAE;cACRC,KAAK,EAAEA,KAAK;cACZuB,MAAM,EAAE,CACN;gBACE+B,IAAI,EAAEyF,IAAI;gBACV6D,KAAK,EAAE,SAAS;gBAChBC,UAAU,EAAE,KAAK;gBACjB9D,IAAI,EAAE;eACP,EACD;gBACEzF,IAAI,EAAE6I,GAAG,CAACW,iBAAiB;gBAC3BF,KAAK,EAAET,GAAG,CAACW,iBAAiB,KAAK,QAAQ,GAAG,OAAO,GAAG,MAAM;gBAC5DD,UAAU,EAAE,KAAK;gBACjB9D,IAAI,EAAE;eACP;aAEJ;YACDgE,QAAQ,EAAE,CACR;cACEzJ,IAAI,EAAEyF,IAAI;cACV6D,KAAK,EAAE,SAAS;cAChBC,UAAU,EAAE,KAAK;cACjB9D,IAAI,EAAE;aACP,EACD;cACEzF,IAAI,EAAE6I,GAAG,CAAC7L,MAAM;cAChBsM,KAAK,EAAE,SAAS;cAChBC,UAAU,EAAE,KAAK;cACjB9D,IAAI,EAAE;aACP,CACF;YACD5I,QAAQ,EAAE,CACR;cACE6M,QAAQ,EAAE,MAAM;cAChB5M,KAAK,EAAE+L,GAAG,CAACc;aACZ,EACD;cACED,QAAQ,EAAE,eAAe;cACzB5M,KAAK,EAAE5C,UAAU,CAAC2O,GAAG,EAAEe,WAAW,EAAE,aAAa,EAAE,OAAO;aAC3D,CACF;YACD7M,QAAQ,EAAE;cACRC,MAAM,EAAEkM,WAAW,CAACC,SAAS,CAAC;cAC9BO,QAAQ,EAAEZ,WAAW,CAACK,SAAS;;WAElC;QACH,CAAC,CAAC;QACFU,MAAM,EAAE;OACT;IACH;;uCAjiBSrM,sBAAsB,EAAAvC,EAAA,CAAA6O,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAA/O,EAAA,CAAA6O,iBAAA,CAAAG,EAAA,CAAAC,uBAAA,GAAAjP,EAAA,CAAA6O,iBAAA,CAAAK,EAAA,CAAAC,eAAA,GAAAnP,EAAA,CAAA6O,iBAAA,CAAAO,EAAA,CAAAC,WAAA,GAAArP,EAAA,CAAA6O,iBAAA,CAAAS,EAAA,CAAAC,YAAA,GAAAvP,EAAA,CAAA6O,iBAAA,CAAAW,EAAA,CAAAC,aAAA,GAAAzP,EAAA,CAAA6O,iBAAA,CAAAa,EAAA,CAAAC,aAAA;IAAA;;YAAtBpN,sBAAsB;MAAAqN,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCnC/BlQ,EADJ,CAAAC,cAAA,aAAmC,aACI;UAM/BD,EALA,CAAAY,SAAA,+BACgG,+BAGZ,+BAGmB;UAC3GZ,EAAA,CAAAc,YAAA,EAAM;UAGFd,EAFR,CAAAC,cAAA,aAA4B,aACC,aAC4C;UAC7DD,EAAA,CAAAa,MAAA,GACJ;UAAAb,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAC,cAAA,aAAkC;UAC9BD,EAAA,CAAAa,MAAA,IACJ;UACJb,EADI,CAAAc,YAAA,EAAM,EACJ;UAIMd,EAHZ,CAAAC,cAAA,cAA6B,WACpB,eAC8B,sBACoD;UAC3ED,EAAA,CAAAY,SAAA,oBAAgH;UAKpIZ,EAJgB,CAAAc,YAAA,EAAc,EACX,EACL,EACJ,EACJ;UAEFd,EAAA,CAAAC,cAAA,eAAmC;UAyD/BD,EAxDA,CAAAoQ,UAAA,KAAAC,8CAAA,OAAsB,KAAAC,8CAAA,kBAwDhB;UAMdtQ,EADI,CAAAc,YAAA,EAAM,EACJ;;;UA5FyBd,EAAA,CAAAqB,SAAA,GAAwB;UAC3CrB,EADmB,CAAAuQ,UAAA,yBAAwB,UAAAJ,GAAA,CAAAnN,MAAA,CAAAG,cAAA,CAAgC,UAAAgN,GAAA,CAAAhN,cAAA,CAAyB,aAAAgN,GAAA,CAAAxO,UAAA,SAAAwO,GAAA,CAAAnN,MAAA,CAAAwN,4BAAA,CACjC;UAChDxQ,EAAA,CAAAqB,SAAA,EAA2B;UAE9CrB,EAFmB,CAAAuQ,UAAA,4BAA2B,UAAAJ,GAAA,CAAAnN,MAAA,CAAAC,sBAAA,CAAwC,UAAAkN,GAAA,CAAAlN,sBAAA,CACtD,aAAAkN,GAAA,CAAAxO,UAAA,SAAAwO,GAAA,CAAAnN,MAAA,CAAAyN,gBAAA,CACuB;UACpCzQ,EAAA,CAAAqB,SAAA,EAAwB;UAE3CrB,EAFmB,CAAAuQ,UAAA,yBAAwB,UAAAJ,GAAA,CAAAnN,MAAA,CAAAE,qBAAA,CAAuC,UAAAiN,GAAA,CAAAjN,qBAAA,CACnD,aAAAiN,GAAA,CAAAnN,MAAA,CAAA0N,GAAA,SAAAP,GAAA,CAAAxO,UAAA,SAAAwO,GAAA,CAAAnN,MAAA,CAAA2N,gBAAA,CAC2C;UAK1E3Q,EAAA,CAAAqB,SAAA,GACJ;UADIrB,EAAA,CAAAsC,kBAAA,MAAA6N,GAAA,CAAAxO,UAAA,gBACJ;UAEI3B,EAAA,CAAAqB,SAAA,GACJ;UADIrB,EAAA,CAAA4Q,kBAAA,YAAAT,GAAA,CAAAlM,YAAA,OAAAkM,GAAA,CAAAxO,UAAA,MACJ;UAIU3B,EAAA,CAAAqB,SAAA,GAAwB;UAAxBrB,EAAA,CAAAuQ,UAAA,cAAAJ,GAAA,CAAA1L,UAAA,CAAwB;UACbzE,EAAA,CAAAqB,SAAA,EAAwC;UAAxCrB,EAAA,CAAAuQ,UAAA,gBAAAJ,GAAA,CAAAnN,MAAA,CAAA6N,iBAAA,CAAwC;UACH7Q,EAAA,CAAAqB,SAAA,EAAe;UAAfrB,EAAA,CAAAuQ,UAAA,gBAAe;UAQzEvQ,EAAA,CAAAqB,SAAA,GA4DC;UA5DDrB,EAAA,CAAA8Q,aAAA,CAAAX,GAAA,CAAAlM,YAAA,eA4DC;;;qBDzELjF,YAAY,EACZG,YAAY,EACZC,qBAAqB,EACrBC,aAAa,EACbC,mBAAmB,EACnBJ,mBAAmB,EAAAkQ,EAAA,CAAA2B,aAAA,EAAA3B,EAAA,CAAA4B,eAAA,EAAA5B,EAAA,CAAA6B,oBAAA,EAAA7B,EAAA,CAAA8B,kBAAA,EAAA9B,EAAA,CAAA+B,eAAA,EACnB5R,eAAe,EACfC,eAAe,EAGfO,wBAAwB;MAAAqR,MAAA;IAAA;;SAOf7O,sBAAsB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}