{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { Subject, takeUntil } from 'rxjs';\nimport { FormsModule, Validators } from '@angular/forms';\n// Import child components\nimport { ChatInterfaceComponent } from '@shared/components/chat-interface/chat-interface.component';\nimport { AgentOutputComponent } from './components/agent-output/agent-output.component';\nimport { ButtonComponent, IconComponent, TabsComponent } from '@ava/play-comp-library';\nimport { environment } from '@shared/environments/environment';\nimport workflowConstants from './../constants/workflows.json';\nimport { ExecutionStatus } from '@shared/models/execution.model';\nimport { WorkflowPlaygroundComponent } from './components/workflow-playground/workflow-playground.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@shared/services/workflow.service\";\nimport * as i3 from \"@shared/services/workflow-input-extractor.service\";\nimport * as i4 from \"@shared/index\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"@angular/common\";\nconst _c0 = () => ({\n  background: \"linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)\",\n  \"--button-effect-color\": \"33, 90, 214\",\n  \"border-radius\": \"8px\",\n  \"box-shadow\": \"none\"\n});\nconst _c1 = () => ({\n  width: \"100%\",\n  background: \"linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)\",\n  \"--button-effect-color\": \"33, 90, 214\",\n  \"border-radius\": \"8px\",\n  \"box-shadow\": \"none\"\n});\nfunction WorkflowExecutionComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"ava-button\", 20);\n    i0.ɵɵlistener(\"click\", function WorkflowExecutionComponent_div_9_Template_ava_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.executeWorkflow());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.isInputValid() || ctx_r1.isRunning)(\"customStyles\", i0.ɵɵpureFunction0(2, _c1));\n  }\n}\nfunction WorkflowExecutionComponent_div_17_div_17_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 45);\n    i0.ɵɵtext(1, \"Agent 1 is currently working\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction WorkflowExecutionComponent_div_17_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"div\", 40);\n    i0.ɵɵelement(2, \"ava-icon\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 42)(4, \"span\", 43);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, WorkflowExecutionComponent_div_17_div_17_span_6_Template, 2, 0, \"span\", 44);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const agent_r3 = ctx.$implicit;\n    const i_r4 = ctx.index;\n    i0.ɵɵclassProp(\"active\", i_r4 === 0)(\"completed\", false);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"iconSize\", 20);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(agent_r3.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r4 === 0);\n  }\n}\nfunction WorkflowExecutionComponent_div_17_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"span\", 47);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 48);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const log_r5 = ctx.$implicit;\n    const i_r6 = ctx.index;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate((i_r6 + 1 < 10 ? \"0\" : \"\") + (i_r6 + 1));\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"color\", log_r5.color);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(log_r5.content);\n  }\n}\nfunction WorkflowExecutionComponent_div_17_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"button\", 50);\n    i0.ɵɵtext(2, \"Show More\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction WorkflowExecutionComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"div\", 22)(2, \"h2\");\n    i0.ɵɵtext(3, \"Execution Monitor\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 23)(5, \"span\", 24);\n    i0.ɵɵtext(6, \"Overall Progress\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 25)(8, \"div\", 26);\n    i0.ɵɵelement(9, \"div\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\", 28);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(12, \"div\", 29)(13, \"div\", 30)(14, \"h3\");\n    i0.ɵɵtext(15, \"Pipeline Steps\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 31);\n    i0.ɵɵtemplate(17, WorkflowExecutionComponent_div_17_div_17_Template, 7, 7, \"div\", 32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 33)(19, \"div\", 34)(20, \"h3\");\n    i0.ɵɵtext(21, \"Execution Logs\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"span\", 35);\n    i0.ɵɵtext(23, \"Agent 1's Output\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 36);\n    i0.ɵɵtemplate(25, WorkflowExecutionComponent_div_17_div_25_Template, 5, 4, \"div\", 37)(26, WorkflowExecutionComponent_div_17_div_26_Template, 3, 0, \"div\", 38);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵstyleProp(\"width\", ctx_r1.progress, \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.progress, \"%\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.agents);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.workflowLogs);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.workflowLogs.length > 10);\n  }\n}\nfunction WorkflowExecutionComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 51)(1, \"app-agent-output\", 52);\n    i0.ɵɵlistener(\"export\", function WorkflowExecutionComponent_div_18_Template_app_agent_output_export_1_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.exportResults(\"output\"));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"outputs\", ctx_r1.taskMessage);\n  }\n}\nfunction WorkflowExecutionComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53)(1, \"div\", 54)(2, \"p\");\n    i0.ɵɵtext(3, \"Configuration settings will be displayed here.\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nexport let WorkflowExecutionComponent = /*#__PURE__*/(() => {\n  class WorkflowExecutionComponent {\n    route;\n    router;\n    workflowService;\n    inputExtractorService;\n    tokenStorage;\n    loaderService;\n    formBuilder;\n    navigationTabs = [{\n      id: 'nav-home',\n      label: 'Execution'\n    }, {\n      id: 'nav-products',\n      label: 'Output'\n    }, {\n      id: 'nav-services',\n      label: 'Configuration'\n    }];\n    // Workflow details\n    workflowId = null;\n    workflowName = 'Workflow';\n    constants = workflowConstants;\n    chatInterfaceComp;\n    // Activity logs\n    activityLogs = [];\n    activityProgress = 0;\n    executionDetails;\n    isRunning = false;\n    status = ExecutionStatus.notStarted;\n    // Chat messages\n    chatMessages = [];\n    isProcessingChat = false;\n    inputText = '';\n    // Agent outputs\n    agentOutputs = [];\n    workflowForm;\n    fileType = '.zip';\n    // Execution state\n    executionStartTime = null;\n    executionCompleted = false;\n    executionId;\n    workflowLogs = [];\n    enableStreamingLog = environment.enableLogStreaming || 'all';\n    isExecutionComplete = false;\n    progressInterval;\n    // Component lifecycle\n    destroy$ = new Subject();\n    selectedTab = 'Agent Activity';\n    demoTabs = [{\n      id: 'activity',\n      label: 'Agent Activity'\n    }, {\n      id: 'agents',\n      label: 'Agent Output'\n    }, {\n      id: 'preview',\n      label: 'Preview',\n      disabled: true\n    }];\n    errorMsg = false;\n    resMessage;\n    taskMessage = [];\n    isJsonValid = false;\n    disableChat = false;\n    selectedFiles = [];\n    workflowAgents = [];\n    userInputList = [];\n    progress = 0;\n    isLoading = false;\n    loaderColor = '';\n    inputFieldOrder = [];\n    currentInputIndex = 0;\n    activeTabId = 'nav-home';\n    // New properties for workflow playground\n    agents = [];\n    isPlaygroundCollapsed = false;\n    pipelineAgents = [];\n    constructor(route, router, workflowService, inputExtractorService, tokenStorage, loaderService, formBuilder) {\n      this.route = route;\n      this.router = router;\n      this.workflowService = workflowService;\n      this.inputExtractorService = inputExtractorService;\n      this.tokenStorage = tokenStorage;\n      this.loaderService = loaderService;\n      this.formBuilder = formBuilder;\n    }\n    ngOnInit() {\n      this.loaderService.disableLoader();\n      this.selectedTab = 'Agent Activity';\n      this.executionId = crypto.randomUUID();\n      // Get workflow ID from route params\n      this.route.paramMap.pipe(takeUntil(this.destroy$)).subscribe(params => {\n        this.workflowId = params.get('id');\n        if (this.workflowId) {\n          this.loadWorkflow(this.workflowId);\n        } else {\n          // No workflow ID, redirect back to workflows page\n          this.router.navigate(['/build/workflows']);\n        }\n      });\n      // this.executeWorkflow()\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n      this.loaderService.enableLoader();\n    }\n    onTabChange(event) {\n      this.selectedTab = event.label;\n      this.activeTabId = event.id;\n      console.log('Tab changed:', event);\n    }\n    // Load workflow data\n    loadWorkflow(id) {\n      // In a real app, this would fetch the workflow from a service\n      console.log(`Loading workflow with ID: ${id}`);\n      this.chatMessages = [{\n        from: 'ai',\n        text: 'I am your workflow assistant. I will help you in executing this workflow.'\n      }];\n      this.workflowForm = this.formBuilder.group({});\n      // Load workflow data for chat interface\n      this.workflowService.getWorkflowById(id).subscribe({\n        next: res => {\n          this.workflowAgents = res.workflowAgents;\n          this.userInputList = this.extractInputField(this.workflowAgents);\n          if (this.userInputList.length === 0) {\n            this.disableChat = true;\n          }\n          this.workflowName = res.name;\n          this.initializeForm();\n          this.startInputCollection();\n        },\n        error: err => {\n          this.disableChat = true;\n          this.chatInterfaceComp.addAiResponse('No input is required for this workflow. Click on send button to execute the workflow.');\n          console.log(err);\n        }\n      });\n      // Also load workflow details for the new playground component\n      this.getWorkflowDetails(id);\n    }\n    // New method to get workflow details using workflow service\n    getWorkflowDetails(id) {\n      this.workflowService.getOneWorkflow(id).subscribe({\n        next: res => {\n          if (res?.pipeLineAgents?.length) {\n            this.pipelineAgents = res.pipeLineAgents;\n            this.workflowName = res.name;\n            // Convert pipeline agents to AgentData format\n            this.agents = this.inputExtractorService.convertToAgentData(this.pipelineAgents);\n            // Extract input fields for form initialization\n            this.userInputList = this.extractInputField(res.pipeLineAgents);\n            this.initializeForm();\n          }\n        },\n        error: e => console.error(e)\n      });\n    }\n    extractInputField(pipeLineAgents) {\n      const PLACEHOLDER_PATTERNS = /(%\\d+\\$s)|\\{\\{([a-zA-Z0-9-_]+)\\}\\}/g;\n      const placeholderMap = {};\n      pipeLineAgents.forEach(pipelineAgent => {\n        const agentName = pipelineAgent?.agent?.name;\n        const agentDescription = pipelineAgent?.agent?.task?.description;\n        const matches = agentDescription?.matchAll(PLACEHOLDER_PATTERNS) || [];\n        for (const match of matches) {\n          const placeholder = match[1] || match[2];\n          const placeholderInput = match[0];\n          if (!placeholderMap[placeholder]) {\n            placeholderMap[placeholder] = {\n              agents: new Set(),\n              inputs: new Set()\n            };\n            ;\n          }\n          placeholderMap[placeholder].agents.add(agentName);\n          placeholderMap[placeholder].inputs.add(placeholderInput);\n        }\n      });\n      return Object.entries(placeholderMap).map(([placeholder, {\n        agents,\n        inputs\n      }]) => ({\n        name: [...agents].length > 2 ? `${[...agents].slice(0, -1).join(\", \")} and ${[...agents].at(-1)}` : [...agents].join(\" and \"),\n        placeholder,\n        input: [...inputs][0]\n      }));\n    }\n    isImageInput(input) {\n      const match = input.match(/{{(.*?)}}/);\n      if (match && match[1]) {\n        const variableName = match[1].trim();\n        return variableName.startsWith('image') || variableName.startsWith('Image');\n      }\n      return false;\n    }\n    initializeForm() {\n      this.userInputList.forEach(label => {\n        this.workflowForm.addControl(label.input, this.formBuilder.control('', Validators.required));\n      });\n    }\n    isInputValid() {\n      return this.workflowForm.valid && this.workflowId;\n    }\n    startFakeProgress() {\n      this.progress = 0;\n      this.progressInterval = setInterval(() => {\n        if (this.progress < 90) {\n          this.progress += 5; // Increase slowly\n        }\n      }, 200); // Adjust speed\n    }\n    stopFakeProgress() {\n      clearInterval(this.progressInterval);\n      this.progress = 100;\n      setTimeout(() => {\n        this.isLoading = false;\n      }, 500); // Small delay to let user see 100%\n    }\n    // Handle new chat message from user\n    handleChatMessage(message) {\n      // console.log('message ', message, 'is blank', message.trim() === '');\n      this.isProcessingChat = true;\n      if (message.trim() === '') {\n        if (this.inputFieldOrder.length === 0) {\n          this.chatInterfaceComp.addAiResponse('Executing the workflow...');\n          this.executeWorkflow();\n        }\n        return;\n      }\n      if (this.isExecutionComplete || this.currentInputIndex === this.inputFieldOrder.length) {\n        this.chatInterfaceComp.addAiResponse('Executing the workflow...');\n        this.executeWorkflow();\n        return;\n      }\n      const field = this.inputFieldOrder[this.currentInputIndex];\n      if (this.isImageInput(field)) {\n        // Ignore text input, wait for file input\n        this.chatInterfaceComp.addAiResponse(`Please upload an image file for ${field}`);\n        return;\n      }\n      this.workflowForm.get(field)?.setValue(message);\n      this.currentInputIndex++;\n      if (this.currentInputIndex < this.inputFieldOrder.length) {\n        this.promptForCurrentField();\n      } else {\n        this.chatInterfaceComp.addAiResponse('Thank you for the input! Executing the workflow...');\n        this.executeWorkflow();\n      }\n    }\n    // Save execution logs\n    saveLogs() {\n      console.log('Saving execution logs...');\n      // This would typically save to a service\n    }\n    // Export results\n    exportResults(section) {\n      console.log(`Exporting ${section} data...`);\n      if (section === 'activity') {\n        const data = this.activityLogs.map(log => `[${log.timestamp}] ${log.message}`).join('\\n');\n        this.downloadAsFile(data, 'workflow-activity-logs.txt', 'text/plain');\n      } else {\n        const data = JSON.stringify(this.agentOutputs, null, 2);\n        this.downloadAsFile(data, 'workflow-outputs.json', 'application/json');\n      }\n    }\n    // Helper method to download data as a file\n    downloadAsFile(data, filename, type) {\n      const blob = new Blob([data], {\n        type\n      });\n      const url = URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = filename;\n      link.click();\n      URL.revokeObjectURL(url);\n    }\n    // Handle controls for execution\n    handleControlAction(action) {\n      console.log(`Control action: ${action}`);\n      // In a real app, this would control the workflow execution\n      if (action === 'play') {\n        this.isRunning = true;\n      } else if (action === 'pause' || action === 'stop') {\n        this.isRunning = false;\n      }\n    }\n    // Navigate back to workflow listing\n    navigateBack() {\n      this.router.navigate(['/build/workflows']);\n    }\n    // Navigate to edit workflow\n    editWorkflow() {\n      if (this.workflowId) {\n        this.router.navigate(['/build/workflows/edit', this.workflowId]);\n      }\n    }\n    logExecutionStatus(delay = 2000) {\n      setTimeout(() => {\n        if (!this.isExecutionComplete) {\n          console.log(this.constants);\n          console.log(this.constants['labels'].workflowExecProcessing);\n          this.workflowLogs.push({\n            content: this.constants['labels'].workflowExecProcessing,\n            color: '#F9DB24'\n          });\n        }\n      }, delay);\n    }\n    // public parseAnsiString(ansiString: string) {\n    //   const regex = ansiRegex();\n    //   const parts = ansiString.split(regex);\n    //   const matches = [...ansiString.matchAll(regex)];\n    //   parts.forEach((part, index) => {\n    //     if (part.trim() !== '') {\n    //       let colorCode = matches[index - 1][0];\n    //       if (index - 2 >= 0 && matches[index - 2]?.includes('\\u001b[1m')) {\n    //         colorCode = `\\u001b[1m${colorCode}`;\n    //       }\n    //       this.workflowLogs.push({\n    //         content: part,\n    //         color: this.colorMap[colorCode] || 'white',\n    //       });\n    //     }\n    //   });\n    // }\n    getWorkflowLogs(executionId) {\n      this.workflowService.workflowLogConnect(executionId).pipe(takeUntil(this.destroy$)).subscribe({\n        next: message => {\n          console.log('message: ', message);\n          const {\n            content,\n            color\n          } = message;\n          if (color) {\n            this.workflowLogs.push({\n              content,\n              color\n            });\n          } else if (this.enableStreamingLog === 'all') {\n            // this.parseAnsiString(content);\n          }\n        },\n        error: err => {\n          this.workflowLogs.push({\n            content: this.constants['workflowLog'],\n            color: 'red'\n          });\n          console.error('WebSocket error:', err);\n        },\n        complete: () => {\n          this.logExecutionStatus();\n          console.log('WebSocket connection closed');\n        }\n      });\n    }\n    // public parseAnsiString(ansiString: string) {\n    //   const regex = ansiRegex();\n    //   const parts = ansiString.split(regex);\n    //   const matches = [...ansiString.matchAll(regex)];\n    //   parts.forEach((part, index) => {\n    //     if (part.trim() !== '') {\n    //       let colorCode = matches[index-1][0];\n    //       if(index - 2 >= 0 && matches[index-2]?.includes('\\u001b[1m')) {\n    //         colorCode = `\\u001b[1m${colorCode}`;\n    //       }\n    //       this.workflowLogs.push({\n    //         content: part, \n    //         color: this.colorMap[colorCode] || 'white', \n    //       });\n    //     }\n    //   });\n    // }\n    validateJson(output) {\n      this.isJsonValid = false;\n      try {\n        const parsedOutput = JSON.parse(output);\n        this.isJsonValid = true;\n        return parsedOutput;\n      } catch (e) {\n        return null;\n      }\n    }\n    executeWorkflow() {\n      let payload = new FormData();\n      let queryString = '';\n      this.status = ExecutionStatus.running;\n      if (this.selectedFiles.length) {\n        this.selectedFiles.forEach(file => {\n          payload.append('files', file);\n        });\n        payload.append('workflowId', this.workflowId);\n        payload.append('userInputs', JSON.stringify(this.workflowForm.value));\n        payload.append('user', this.tokenStorage.getDaUsername());\n        payload.append('executionId', this.executionId);\n        queryString = '/files';\n      } else {\n        payload = {\n          pipeLineId: this.workflowId,\n          userInputs: this.workflowForm.value,\n          executionId: this.executionId,\n          user: this.tokenStorage.getDaUsername()\n        };\n      }\n      this.getWorkflowLogs(this.executionId);\n      this.startFakeProgress();\n      this.workflowService.executeWorkflow(payload, queryString).pipe(takeUntil(this.destroy$)).subscribe({\n        next: res => {\n          this.isProcessingChat = false;\n          this.isRunning = false;\n          this.chatInterfaceComp.addAiResponse(res?.message || \"Workflow execution completed successfully!\");\n          if (res?.workflowResponse?.pipeline?.output) {\n            this.isExecutionComplete = true;\n            // console.log(this.constants['labels'].workflowExecComplete);\n            this.workflowLogs.push({\n              content: this.constants['labels'].workflowExecComplete,\n              color: '#0F8251'\n            });\n            this.errorMsg = false;\n            this.resMessage = res?.workflowResponse?.pipeline?.output;\n            this.agentOutputs = res?.workflowResponse?.pipeline?.tasksOutputs.map(task => {\n              return {\n                id: task?.id || '',\n                title: task?.title || '',\n                content: task?.content || '',\n                agentName: task?.agentName || '',\n                timestamp: task?.timestamp || '',\n                type: task?.type || '',\n                description: task?.description || '',\n                expected_output: task?.expected_output || '',\n                summary: task?.summary || '',\n                raw: task?.raw || ''\n              };\n            });\n            this.taskMessage = res?.workflowResponse?.pipeline?.tasksOutputs.map(task => {\n              return {\n                description: task.description,\n                summary: task.summary,\n                raw: task.raw,\n                expected_output: task.expected_output\n              };\n            });\n            // if(\"file_download_url\" in res?.pipeline){\n            //   this.isFileWriter = true;\n            //   this.fileDownloadLink = res?.pipeline?.file_download_url;\n            //   if(!this.fileDownloadLink){\n            //     this.fileDownloadUrlError = [];\n            //     this.fileDownloadUrlError.push(\"Output file is not generated yet!\")\n            //   }\n            // }\n            // this.isAccordian = true\n          }\n          this.validateJson(this.resMessage);\n          this.status = ExecutionStatus.completed;\n          this.stopFakeProgress();\n          this.selectedFiles = [];\n        },\n        error: error => {\n          this.isExecutionComplete = true;\n          this.isProcessingChat = false;\n          this.errorMsg = true;\n          this.resMessage = error?.error?.detail;\n          this.workflowService.workflowLogDisconnect();\n          this.workflowLogs.push({\n            content: this.constants['labels'].workflowLogFailed,\n            color: 'red'\n          });\n          this.chatInterfaceComp.addAiResponse('Something went wrong, Workflow execution has failed.');\n          this.selectedFiles = [];\n          this.stopFakeProgress();\n          console.log('error is', error.message);\n        }\n      });\n    }\n    // public asyncExecutePipeline() {\n    //   const payload: FormData = new FormData();\n    //   if (this.selectedFiles?.length) {\n    //     for (const element of this.selectedFiles) {\n    //       payload.append('files', element)\n    //     }\n    //   }\n    //   payload.append('pipeLineId', String(this.workflowId));\n    //   payload.append('userInputs', JSON.stringify(this.workflowForm.value));\n    //   payload.append('user', this.tokenStorage.getDaUsername() || '');\n    //   payload.append('executionId', this.executionId);\n    //   this.workflowService.asyncExecutePipeline(payload).pipe().subscribe({\n    //     next: (res: any) => {\n    //       if(res) {\n    //         // res handling\n    //         console.log(res);\n    //       }\n    //     },\n    //     error: e => {\n    //       // error handling\n    //       console.log(e);\n    //     }\n    //   })\n    // }\n    handleAttachment() {\n      console.log('handleAttachment');\n    }\n    onAttachmentsSelected(files) {\n      if (this.currentInputIndex === this.inputFieldOrder.length || this.inputFieldOrder.length === 0) {\n        this.selectedFiles = files;\n        return;\n      }\n      const field = this.inputFieldOrder[this.currentInputIndex];\n      if (this.isImageInput(field)) {\n        if (files && files.length > 0) {\n          this.onImageSelected(files[0]);\n        }\n      } else {\n        this.selectedFiles = files;\n      }\n    }\n    startInputCollection() {\n      this.inputFieldOrder = Object.keys(this.workflowForm.controls);\n      this.currentInputIndex = 0;\n      if (this.inputFieldOrder.length > 0) {\n        this.promptForCurrentField();\n      } else {\n        this.disableChat = true;\n        this.chatInterfaceComp.addAiResponse('No input is required for this workflow. Click on send button to execute the workflow.');\n      }\n    }\n    promptForCurrentField() {\n      const field = this.inputFieldOrder[this.currentInputIndex];\n      if (this.isImageInput(field)) {\n        this.fileType = '.jpeg,.png,.jpg,.svg';\n        this.chatInterfaceComp.addAiResponse(`Please upload an image for ${field}`);\n        // UI should now show a file input for the user\n      } else {\n        this.fileType = '.zip'; // or whatever default you want for non-image\n        this.chatInterfaceComp.addAiResponse(`Please enter the value of ${field}`);\n      }\n    }\n    onImageSelected(file) {\n      const field = this.inputFieldOrder[this.currentInputIndex];\n      if (!this.isImageInput(field)) return;\n      const reader = new FileReader();\n      reader.onload = () => {\n        const base64String = reader.result;\n        this.workflowForm.get(field)?.setValue(base64String);\n        this.currentInputIndex++;\n        if (this.currentInputIndex < this.inputFieldOrder.length) {\n          this.promptForCurrentField();\n        } else {\n          this.chatInterfaceComp.addAiResponse('Thank you! Running the workflow...');\n          this.executeWorkflow();\n        }\n      };\n      reader.readAsDataURL(file);\n    }\n    // Event handlers for workflow playground\n    onPlaygroundBackClicked() {\n      this.navigateBack();\n    }\n    onPlaygroundCollapseToggled(isCollapsed) {\n      this.isPlaygroundCollapsed = isCollapsed;\n    }\n    onAgentInputChanged(event) {\n      // Find the agent and update the input value\n      const agent = this.agents.find(a => a.id === event.agentId);\n      if (agent && agent.inputs && agent.inputs[event.inputIndex]) {\n        agent.inputs[event.inputIndex].value = event.value;\n        // Update the form control if it exists\n        const placeholder = agent.inputs[event.inputIndex].placeholder;\n        if (this.workflowForm.get(placeholder)) {\n          this.workflowForm.get(placeholder)?.setValue(event.value);\n        }\n      }\n    }\n    onAgentFileSelected(event) {\n      // Find the agent and update the files\n      const agent = this.agents.find(a => a.id === event.agentId);\n      if (agent && agent.inputs && agent.inputs[event.inputIndex]) {\n        agent.inputs[event.inputIndex].files = event.files;\n        // Add files to selectedFiles array for execution\n        this.selectedFiles = [...this.selectedFiles, ...event.files];\n      }\n    }\n    static ɵfac = function WorkflowExecutionComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || WorkflowExecutionComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.WorkflowService), i0.ɵɵdirectiveInject(i3.WorkflowInputExtractorService), i0.ɵɵdirectiveInject(i4.TokenStorageService), i0.ɵɵdirectiveInject(i4.LoaderService), i0.ɵɵdirectiveInject(i5.FormBuilder));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: WorkflowExecutionComponent,\n      selectors: [[\"app-workflow-execution\"]],\n      viewQuery: function WorkflowExecutionComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(ChatInterfaceComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.chatInterfaceComp = _t.first);\n        }\n      },\n      decls: 20,\n      vars: 12,\n      consts: [[1, \"workflow-execution-container\"], [\"width\", \"0\", \"height\", \"0\", 2, \"position\", \"absolute\"], [\"id\", \"gradient\", \"x1\", \"0%\", \"y1\", \"0%\", \"x2\", \"100%\", \"y2\", \"0%\"], [\"offset\", \"0%\", \"stop-color\", \"#6566CD\"], [\"offset\", \"100%\", \"stop-color\", \"#F96CAB\"], [\"role\", \"main\", 1, \"execution-content\"], [\"role\", \"region\", \"aria-label\", \"Workflow Input Panel\", 1, \"left-panel\"], [\"role\", \"region\", \"aria-label\", \"Workflow Playground\", 1, \"playground-component\", 3, \"backClicked\", \"collapseToggled\", \"agentInputChanged\", \"agentFileSelected\", \"agents\", \"isCollapsed\", \"workflowName\"], [\"class\", \"execute-button-container\", 4, \"ngIf\"], [\"role\", \"region\", \"aria-label\", \"Workflow Results\", 1, \"right-panel\"], [1, \"tabs-header\"], [1, \"tabs-container\"], [\"variant\", \"button\", \"buttonShape\", \"pill\", \"ariaLabel\", \"Workflow sections navigation\", 3, \"tabChange\", \"tabs\", \"activeTabId\", \"showContentPanels\"], [1, \"header-actions\"], [\"label\", \"Send for Approval\", \"variant\", \"primary\", \"size\", \"medium\", 3, \"customStyles\"], [1, \"content-sections\"], [\"class\", \"section-content execution-section\", 4, \"ngIf\"], [\"class\", \"section-content output-section\", 4, \"ngIf\"], [\"class\", \"section-content configuration-section\", 4, \"ngIf\"], [1, \"execute-button-container\"], [\"label\", \"Execute Workflow\", \"variant\", \"primary\", \"size\", \"large\", 3, \"click\", \"disabled\", \"customStyles\"], [1, \"section-content\", \"execution-section\"], [1, \"execution-monitor-header\"], [1, \"progress-section\"], [1, \"progress-label\"], [1, \"progress-container\"], [1, \"progress-bar\"], [1, \"progress-fill\"], [1, \"progress-text\"], [1, \"execution-content-grid\"], [1, \"pipeline-steps-section\"], [1, \"pipeline-agents-list\"], [\"class\", \"pipeline-agent-item\", 3, \"active\", \"completed\", 4, \"ngFor\", \"ngForOf\"], [1, \"execution-logs-section\"], [1, \"logs-header\"], [1, \"logs-subtitle\"], [1, \"logs-content\"], [\"class\", \"log-entry\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"show-more\", 4, \"ngIf\"], [1, \"pipeline-agent-item\"], [1, \"agent-icon\"], [\"iconName\", \"bot\", \"iconColor\", \"#666\", 3, \"iconSize\"], [1, \"agent-info\"], [1, \"agent-name\"], [\"class\", \"agent-status\", 4, \"ngIf\"], [1, \"agent-status\"], [1, \"log-entry\"], [1, \"log-number\"], [1, \"log-message\"], [1, \"show-more\"], [1, \"show-more-btn\"], [1, \"section-content\", \"output-section\"], [3, \"export\", \"outputs\"], [1, \"section-content\", \"configuration-section\"], [1, \"configuration-content\"]],\n      template: function WorkflowExecutionComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(1, \"svg\", 1)(2, \"defs\")(3, \"linearGradient\", 2);\n          i0.ɵɵelement(4, \"stop\", 3)(5, \"stop\", 4);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(6, \"div\", 5)(7, \"div\", 6)(8, \"app-workflow-playground\", 7);\n          i0.ɵɵlistener(\"backClicked\", function WorkflowExecutionComponent_Template_app_workflow_playground_backClicked_8_listener() {\n            return ctx.onPlaygroundBackClicked();\n          })(\"collapseToggled\", function WorkflowExecutionComponent_Template_app_workflow_playground_collapseToggled_8_listener($event) {\n            return ctx.onPlaygroundCollapseToggled($event);\n          })(\"agentInputChanged\", function WorkflowExecutionComponent_Template_app_workflow_playground_agentInputChanged_8_listener($event) {\n            return ctx.onAgentInputChanged($event);\n          })(\"agentFileSelected\", function WorkflowExecutionComponent_Template_app_workflow_playground_agentFileSelected_8_listener($event) {\n            return ctx.onAgentFileSelected($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(9, WorkflowExecutionComponent_div_9_Template, 2, 3, \"div\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"div\", 9)(11, \"div\", 10)(12, \"div\", 11)(13, \"ava-tabs\", 12);\n          i0.ɵɵlistener(\"tabChange\", function WorkflowExecutionComponent_Template_ava_tabs_tabChange_13_listener($event) {\n            return ctx.onTabChange($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(14, \"div\", 13);\n          i0.ɵɵelement(15, \"ava-button\", 14);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"div\", 15);\n          i0.ɵɵtemplate(17, WorkflowExecutionComponent_div_17_Template, 27, 6, \"div\", 16)(18, WorkflowExecutionComponent_div_18_Template, 2, 1, \"div\", 17)(19, WorkflowExecutionComponent_div_19_Template, 4, 0, \"div\", 18);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"agents\", ctx.agents)(\"isCollapsed\", ctx.isPlaygroundCollapsed)(\"workflowName\", ctx.workflowName);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isPlaygroundCollapsed);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"tabs\", ctx.navigationTabs)(\"activeTabId\", ctx.activeTabId)(\"showContentPanels\", false);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"customStyles\", i0.ɵɵpureFunction0(11, _c0));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.activeTabId === \"nav-home\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.activeTabId === \"nav-products\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.activeTabId === \"nav-services\");\n        }\n      },\n      dependencies: [CommonModule, i6.NgForOf, i6.NgIf, FormsModule, AgentOutputComponent, TabsComponent, ButtonComponent, IconComponent, WorkflowPlaygroundComponent],\n      styles: [\".ava-tabs {\\n  background: none !important;\\n}\\n\\n  .ava-tabs__container {\\n  border-radius: none !important;\\n  border: none !important;\\n  padding: 0 !important;\\n  box-shadow: none !important;\\n  background: none !important;\\n}\\n\\n  .ava-tabs__list {\\n  padding: 0 !important;\\n}\\n\\n.workflow-execution-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  height: 100vh;\\n  overflow: hidden;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 20px 24px;\\n  border-bottom: 1px solid var(--border-color, #e0e0e0);\\n  flex-shrink: 0;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-title[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 24px;\\n  font-weight: 600;\\n  color: var(--text-color, #333);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-title[_ngcontent-%COMP%]   .header-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%], \\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .edit-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 8px 16px;\\n  border-radius: 5px;\\n  font-weight: 500;\\n  font-size: 14px;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%], \\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .edit-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  margin-right: 6px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%] {\\n  background-color: var(--bg-muted, #f5f5f5);\\n  border: 1px solid var(--border-color, #e0e0e0);\\n  color: var(--text-color, #333);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]:hover {\\n  background-color: var(--bg-muted-hover, #e9e9e9);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .edit-button[_ngcontent-%COMP%] {\\n  background-color: var(--card-bg, #fff);\\n  border: 1px solid var(--border-color, #e0e0e0);\\n  color: var(--text-color, #333);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .edit-button[_ngcontent-%COMP%]:hover {\\n  background-color: var(--card-bg-hover, #f9f9f9);\\n  border-color: var(--border-color-dark, #d0d0d0);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex: 1;\\n  min-height: 0;\\n  overflow: hidden;\\n  gap: 20px;\\n  padding: 20px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .left-panel[_ngcontent-%COMP%] {\\n  flex: 2.5;\\n  display: flex;\\n  flex-direction: column;\\n  min-width: 0;\\n  background: transparent;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .left-panel[_ngcontent-%COMP%]   .playground-component[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  min-height: 0;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .left-panel[_ngcontent-%COMP%]   .execute-button-container[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  background-color: var(--card-bg, white);\\n  border-radius: 0 0 8px 8px;\\n  border-top: 1px solid var(--border-color, #e0e0e0);\\n  flex-shrink: 0;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .left-panel[_ngcontent-%COMP%]   .execute-button-container[_ngcontent-%COMP%]     .ava-button {\\n  width: 100% !important;\\n  min-height: 48px;\\n  font-weight: 600;\\n  font-size: 16px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .left-panel[_ngcontent-%COMP%]   .execute-button-container[_ngcontent-%COMP%]     .ava-button:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%] {\\n  flex: 5.5;\\n  display: flex;\\n  flex-direction: column;\\n  background-color: var(--card-bg, white);\\n  border-radius: 8px;\\n  box-shadow: 0 4px 15px var(--card-shadow, rgba(0, 0, 0, 0.05));\\n  overflow: hidden;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .tabs-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 8px 16px;\\n  background-color: #e9effd;\\n  height: 64px;\\n  flex-shrink: 0;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .tabs-header[_ngcontent-%COMP%]   .tabs-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .tabs-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n  margin-left: 16px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-monitor-header[_ngcontent-%COMP%] {\\n  padding: 20px 24px;\\n  border-bottom: 1px solid var(--border-color, #e0e0e0);\\n  background-color: var(--card-bg, white);\\n  flex-shrink: 0;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-monitor-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0 0 16px 0;\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: var(--text-color, #333);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-monitor-header[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-label[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: var(--text-muted, #666);\\n  margin-bottom: 8px;\\n  display: block;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-monitor-header[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-monitor-header[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 8px;\\n  background-color: var(--bg-muted, #f0f0f0);\\n  border-radius: 4px;\\n  overflow: hidden;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-monitor-header[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: linear-gradient(90deg, #6566cd 0%, #f96cab 100%);\\n  border-radius: 4px;\\n  transition: width 0.3s ease;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-monitor-header[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%]   .progress-text[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: var(--text-color, #333);\\n  min-width: 40px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex: 1;\\n  min-height: 0;\\n  gap: 16px;\\n  padding: 20px;\\n  background-color: var(--card-bg, white);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .pipeline-steps-section[_ngcontent-%COMP%], \\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .execution-logs-section[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-height: 0;\\n  background-color: #F7F8F9;\\n  border-radius: 16px;\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .pipeline-steps-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding: 20px 24px 16px 24px;\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: var(--text-color, #333);\\n  background-color: #F7F8F9;\\n  flex-shrink: 0;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .pipeline-steps-section[_ngcontent-%COMP%]   .pipeline-agents-list[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-height: 0;\\n  padding: 0 24px 24px 24px;\\n  background-color: #F7F8F9;\\n  overflow-y: auto;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .pipeline-steps-section[_ngcontent-%COMP%]   .pipeline-agents-list[_ngcontent-%COMP%]   .pipeline-agent-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n  padding: 16px 20px;\\n  margin-bottom: 12px;\\n  border: 1px solid #E5E7EB;\\n  border-radius: 16px;\\n  background-color: white;\\n  transition: all 0.2s ease;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .pipeline-steps-section[_ngcontent-%COMP%]   .pipeline-agents-list[_ngcontent-%COMP%]   .pipeline-agent-item.active[_ngcontent-%COMP%] {\\n  border-color: #6566cd;\\n  background-color: white;\\n  box-shadow: 0 2px 8px rgba(101, 102, 205, 0.1);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .pipeline-steps-section[_ngcontent-%COMP%]   .pipeline-agents-list[_ngcontent-%COMP%]   .pipeline-agent-item.completed[_ngcontent-%COMP%] {\\n  border-color: #10b981;\\n  background-color: white;\\n  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.1);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .pipeline-steps-section[_ngcontent-%COMP%]   .pipeline-agents-list[_ngcontent-%COMP%]   .pipeline-agent-item[_ngcontent-%COMP%]   .agent-icon[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  background-color: #F3F4F6;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  flex-shrink: 0;\\n  border: 2px solid white;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .pipeline-steps-section[_ngcontent-%COMP%]   .pipeline-agents-list[_ngcontent-%COMP%]   .pipeline-agent-item[_ngcontent-%COMP%]   .agent-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .pipeline-steps-section[_ngcontent-%COMP%]   .pipeline-agents-list[_ngcontent-%COMP%]   .pipeline-agent-item[_ngcontent-%COMP%]   .agent-info[_ngcontent-%COMP%]   .agent-name[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: var(--text-color, #333);\\n  margin-bottom: 4px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .pipeline-steps-section[_ngcontent-%COMP%]   .pipeline-agents-list[_ngcontent-%COMP%]   .pipeline-agent-item[_ngcontent-%COMP%]   .agent-info[_ngcontent-%COMP%]   .agent-status[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #6B7280;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .execution-logs-section[_ngcontent-%COMP%]   .logs-header[_ngcontent-%COMP%] {\\n  padding: 20px 24px 16px 24px;\\n  background-color: #F7F8F9;\\n  flex-shrink: 0;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .execution-logs-section[_ngcontent-%COMP%]   .logs-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 4px 0;\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: var(--text-color, #333);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .execution-logs-section[_ngcontent-%COMP%]   .logs-header[_ngcontent-%COMP%]   .logs-subtitle[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #6B7280;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .execution-logs-section[_ngcontent-%COMP%]   .logs-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-height: 0;\\n  padding: 0 24px 24px 24px;\\n  overflow-y: auto;\\n  font-family: \\\"SF Mono\\\", \\\"Monaco\\\", \\\"Inconsolata\\\", \\\"Roboto Mono\\\", monospace;\\n  background-color: #F7F8F9;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .execution-logs-section[_ngcontent-%COMP%]   .logs-content[_ngcontent-%COMP%]   .log-entry[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  margin-bottom: 6px;\\n  font-size: 13px;\\n  line-height: 1.5;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .execution-logs-section[_ngcontent-%COMP%]   .logs-content[_ngcontent-%COMP%]   .log-entry[_ngcontent-%COMP%]   .log-number[_ngcontent-%COMP%] {\\n  color: #6B7280;\\n  font-weight: 600;\\n  min-width: 28px;\\n  flex-shrink: 0;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .execution-logs-section[_ngcontent-%COMP%]   .logs-content[_ngcontent-%COMP%]   .log-entry[_ngcontent-%COMP%]   .log-message[_ngcontent-%COMP%] {\\n  flex: 1;\\n  word-break: break-word;\\n  color: #374151;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .execution-logs-section[_ngcontent-%COMP%]   .logs-content[_ngcontent-%COMP%]   .show-more[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-top: 20px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .execution-logs-section[_ngcontent-%COMP%]   .logs-content[_ngcontent-%COMP%]   .show-more[_ngcontent-%COMP%]   .show-more-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #6566cd;\\n  font-size: 14px;\\n  cursor: pointer;\\n  text-decoration: underline;\\n  padding: 8px 16px;\\n  border-radius: 8px;\\n  transition: all 0.2s ease;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.execution-section[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .execution-logs-section[_ngcontent-%COMP%]   .logs-content[_ngcontent-%COMP%]   .show-more[_ngcontent-%COMP%]   .show-more-btn[_ngcontent-%COMP%]:hover {\\n  color: #5555bb;\\n  background-color: rgba(101, 102, 205, 0.05);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.output-section[_ngcontent-%COMP%]   app-agent-output[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.configuration-section[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  overflow-y: auto;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.configuration-section[_ngcontent-%COMP%]   .configuration-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  height: 100%;\\n  text-align: center;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .content-sections[_ngcontent-%COMP%]   .section-content.configuration-section[_ngcontent-%COMP%]   .configuration-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: var(--text-color, #666);\\n  font-size: 16px;\\n  margin: 0;\\n  font-style: italic;\\n}\\n\\n  nav.ava-tabs__list {\\n  background: #e9effd;\\n  padding: 4px;\\n}\\n\\n  button.ava-button.primary.active {\\n  background: #616161;\\n  color: #fff;\\n}\\n\\n  .column-header .ava-tabs[data-variant=button] .ava-tabs__tab--pill {\\n  border-radius: 8px !important;\\n  padding: 12px 16px !important;\\n  font-family: \\\"Mulish\\\";\\n}\\n\\n  .ava-tabs[data-variant=button] .ava-tabs__tab--active .ava-tabs__tab-text {\\n  color: white;\\n}\\n\\n  .ava-tabs__tab-text {\\n  color: #4c515b;\\n  font-family: \\\"Mulish\\\";\\n  font-weight: 600;\\n}\\n\\n  .right-section-header .ava-button.secondary {\\n  color: #1a46a7;\\n  border: none;\\n}\\n\\n  .right-section-header .ava-button.secondary:hover {\\n  color: #1a46a7;\\n  border: none;\\n}\\n\\n.right-section-header[_ngcontent-%COMP%] {\\n  text-align: end;\\n}\\n\\n@media (max-width: 1024px) {\\n  .workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%] {\\n    gap: 16px;\\n    height: 94vh;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%] {\\n    padding: 16px 20px;\\n  }\\n  .workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-title[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 20px;\\n  }\\n  .workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 12px;\\n    padding: 12px;\\n  }\\n  .workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .left-panel[_ngcontent-%COMP%] {\\n    flex: 1;\\n    min-height: 400px;\\n  }\\n  .workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%] {\\n    flex: 1;\\n    min-height: 300px;\\n  }\\n  .workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 12px;\\n    padding: 16px;\\n  }\\n  .workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .pipeline-steps-section[_ngcontent-%COMP%], \\n   .workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .execution-logs-section[_ngcontent-%COMP%] {\\n    flex: 1;\\n    min-height: 200px;\\n    border-radius: 16px;\\n    background-color: #F7F8F9;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%] {\\n    padding: 12px 16px;\\n  }\\n  .workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-title[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n  }\\n  .workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%] {\\n    gap: 8px;\\n  }\\n  .workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%], \\n   .workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .edit-button[_ngcontent-%COMP%] {\\n    padding: 8px 12px;\\n    font-size: 14px;\\n  }\\n  .workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%] {\\n    gap: 8px;\\n    padding: 8px;\\n  }\\n  .workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .left-panel[_ngcontent-%COMP%] {\\n    flex: 1;\\n    min-height: 350px;\\n  }\\n  .workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%] {\\n    flex: 1;\\n    min-height: 250px;\\n  }\\n  .workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 12px;\\n    padding: 12px;\\n  }\\n  .workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .pipeline-steps-section[_ngcontent-%COMP%], \\n   .workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .execution-logs-section[_ngcontent-%COMP%] {\\n    flex: 1;\\n    min-height: 180px;\\n    border-radius: 16px;\\n    background-color: #F7F8F9;\\n  }\\n  .workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .pipeline-steps-section[_ngcontent-%COMP%]   .pipeline-agents-list[_ngcontent-%COMP%], \\n   .workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .pipeline-steps-section[_ngcontent-%COMP%]   .logs-content[_ngcontent-%COMP%], \\n   .workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .execution-logs-section[_ngcontent-%COMP%]   .pipeline-agents-list[_ngcontent-%COMP%], \\n   .workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .execution-logs-section[_ngcontent-%COMP%]   .logs-content[_ngcontent-%COMP%] {\\n    padding: 12px 16px;\\n  }\\n  .workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .pipeline-steps-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], \\n   .workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .execution-content-grid[_ngcontent-%COMP%]   .execution-logs-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n    padding: 16px 20px 12px 20px;\\n  }\\n  .workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .right-panel[_ngcontent-%COMP%]   .execution-monitor-header[_ngcontent-%COMP%] {\\n    padding: 16px 20px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return WorkflowExecutionComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "Subject", "takeUntil", "FormsModule", "Validators", "ChatInterfaceComponent", "AgentOutputComponent", "ButtonComponent", "IconComponent", "TabsComponent", "environment", "workflowConstants", "ExecutionStatus", "WorkflowPlaygroundComponent", "i0", "ɵɵelementStart", "ɵɵlistener", "WorkflowExecutionComponent_div_9_Template_ava_button_click_1_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "executeWorkflow", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "isInputValid", "isRunning", "ɵɵpureFunction0", "_c1", "ɵɵtext", "ɵɵelement", "ɵɵtemplate", "WorkflowExecutionComponent_div_17_div_17_span_6_Template", "ɵɵclassProp", "i_r4", "ɵɵtextInterpolate", "agent_r3", "name", "i_r6", "ɵɵstyleProp", "log_r5", "color", "content", "WorkflowExecutionComponent_div_17_div_17_Template", "WorkflowExecutionComponent_div_17_div_25_Template", "WorkflowExecutionComponent_div_17_div_26_Template", "progress", "ɵɵtextInterpolate1", "agents", "workflowLogs", "length", "WorkflowExecutionComponent_div_18_Template_app_agent_output_export_1_listener", "_r7", "exportResults", "taskMessage", "WorkflowExecutionComponent", "route", "router", "workflowService", "inputExtractorService", "tokenStorage", "loaderService", "formBuilder", "navigationTabs", "id", "label", "workflowId", "workflowName", "constants", "chatInterfaceComp", "activityLogs", "activityProgress", "executionDetails", "status", "notStarted", "chatMessages", "isProcessingChat", "inputText", "agentOutputs", "workflowForm", "fileType", "executionStartTime", "executionCompleted", "executionId", "enableStreamingLog", "enableLogStreaming", "isExecutionComplete", "progressInterval", "destroy$", "selectedTab", "demoTabs", "disabled", "errorMsg", "resMessage", "isJsonValid", "disable<PERSON>hat", "selectedFiles", "workflowAgents", "userInputList", "isLoading", "loaderColor", "inputFieldOrder", "currentInputIndex", "activeTabId", "isPlaygroundCollapsed", "pipelineAgents", "constructor", "ngOnInit", "disable<PERSON><PERSON><PERSON>", "crypto", "randomUUID", "paramMap", "pipe", "subscribe", "params", "get", "loadWorkflow", "navigate", "ngOnDestroy", "next", "complete", "<PERSON><PERSON><PERSON><PERSON>", "onTabChange", "event", "console", "log", "from", "text", "group", "getWorkflowById", "res", "extractInputField", "initializeForm", "startInputCollection", "error", "err", "addAiResponse", "getWorkflowDetails", "getOneWorkflow", "pipeLineAgents", "convertToAgentData", "e", "PLACEHOLDER_PATTERNS", "placeholderM<PERSON>", "for<PERSON>ach", "pipelineAgent", "<PERSON><PERSON><PERSON>", "agent", "agentDescription", "task", "description", "matches", "matchAll", "match", "placeholder", "placeholderInput", "Set", "inputs", "add", "Object", "entries", "map", "slice", "join", "at", "input", "isImageInput", "variableName", "trim", "startsWith", "addControl", "control", "required", "valid", "startFakeProgress", "setInterval", "stopFakeProgress", "clearInterval", "setTimeout", "handleChatMessage", "message", "field", "setValue", "promptForCurrentField", "saveLogs", "section", "data", "timestamp", "downloadAsFile", "JSON", "stringify", "filename", "type", "blob", "Blob", "url", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "click", "revokeObjectURL", "handleControlAction", "action", "navigateBack", "editWorkflow", "logExecutionStatus", "delay", "workflowExecProcessing", "push", "getWorkflowLogs", "workflowLogConnect", "validate<PERSON><PERSON>", "output", "parsedOutput", "parse", "payload", "FormData", "queryString", "running", "file", "append", "value", "getDaUsername", "pipeLineId", "userInputs", "user", "workflowResponse", "pipeline", "workflowExecComplete", "tasksOutputs", "title", "expected_output", "summary", "raw", "completed", "detail", "workflowLogDisconnect", "workflowLogFailed", "handleAttachment", "onAttachmentsSelected", "files", "onImageSelected", "keys", "controls", "reader", "FileReader", "onload", "base64String", "result", "readAsDataURL", "onPlaygroundBackClicked", "onPlaygroundCollapseToggled", "isCollapsed", "onAgentInputChanged", "find", "a", "agentId", "inputIndex", "onAgentFileSelected", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "WorkflowService", "i3", "WorkflowInputExtractorService", "i4", "TokenStorageService", "LoaderService", "i5", "FormBuilder", "selectors", "viewQuery", "WorkflowExecutionComponent_Query", "rf", "ctx", "WorkflowExecutionComponent_Template_app_workflow_playground_backClicked_8_listener", "WorkflowExecutionComponent_Template_app_workflow_playground_collapseToggled_8_listener", "$event", "WorkflowExecutionComponent_Template_app_workflow_playground_agentInputChanged_8_listener", "WorkflowExecutionComponent_Template_app_workflow_playground_agentFileSelected_8_listener", "WorkflowExecutionComponent_div_9_Template", "WorkflowExecutionComponent_Template_ava_tabs_tabChange_13_listener", "WorkflowExecutionComponent_div_17_Template", "WorkflowExecutionComponent_div_18_Template", "WorkflowExecutionComponent_div_19_Template", "_c0", "i6", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\pages\\workflows\\workflow-execution\\workflow-execution.component.ts", "C:\\console\\aava-ui-web\\projects\\shared\\pages\\workflows\\workflow-execution\\workflow-execution.component.html"], "sourcesContent": ["import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { CommonModule } from '@angular/common';\r\nimport { Subject, takeUntil } from 'rxjs';\r\nimport { FormBuilder, FormGroup, FormsModule, Validators } from '@angular/forms';\r\n\r\n// Import child components\r\nimport { ChatInterfaceComponent } from '@shared/components/chat-interface/chat-interface.component';\r\nimport { ChatMessage } from '@shared/components/chat-window/chat-window.component';\r\nimport {\r\n  AgentActivityComponent,\r\n} from './components/agent-activity/agent-activity.component';\r\nimport {\r\n  AgentOutputComponent,\r\n  AgentOutput as OutputItem,\r\n} from './components/agent-output/agent-output.component';\r\nimport {\r\n  ButtonComponent,\r\n  IconComponent,\r\n  TabItem,\r\n  TabsComponent,\r\n} from '@ava/play-comp-library';\r\nimport { WorkflowService } from '@shared/services/workflow.service';\r\nimport { WorkflowInputExtractorService } from '@shared/services/workflow-input-extractor.service';\r\nimport { environment } from '@shared/environments/environment';\r\nimport workflowConstants from './../constants/workflows.json';\r\nimport { TokenStorageService, LoaderService } from '@shared/index';\r\nimport { AvaTab } from '@shared/models/tab.model';\r\nimport { ExecutionStatus, ActivityLog } from '@shared/models/execution.model';\r\nimport { AgentActivityExecutionDetails } from './components/agent-activity/agent-activity.component';\r\nimport { WorkflowPlaygroundComponent, AgentData } from './components/workflow-playground/workflow-playground.component';\r\n\r\n\r\n@Component({\r\n  selector: 'app-workflow-execution',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    ChatInterfaceComponent,\r\n    AgentActivityComponent,\r\n    AgentOutputComponent,\r\n    TabsComponent,\r\n    ButtonComponent,\r\n    IconComponent,\r\n    WorkflowPlaygroundComponent,\r\n  ],\r\n  templateUrl: './workflow-execution.component.html',\r\n  styleUrls: ['./workflow-execution.component.scss'],\r\n})\r\nexport class WorkflowExecutionComponent implements OnInit, OnDestroy {\r\n  navigationTabs: TabItem[] = [\r\n    { id: 'nav-home', label: 'Execution' },\r\n    { id: 'nav-products', label: 'Output' },\r\n    { id: 'nav-services', label: 'Configuration' },\r\n  ];\r\n  // Workflow details\r\n  workflowId: string | null = null;\r\n  workflowName: string = 'Workflow';\r\n\r\n  constants = workflowConstants as Record<string, any>;\r\n\r\n  @ViewChild(ChatInterfaceComponent, { static: false })\r\n  chatInterfaceComp!: ChatInterfaceComponent;\r\n\r\n  // Activity logs\r\n  activityLogs: ActivityLog[] = [];\r\n  activityProgress: number = 0;\r\n  executionDetails?: AgentActivityExecutionDetails;\r\n  isRunning: boolean = false;\r\n  status: ExecutionStatus = ExecutionStatus.notStarted;\r\n\r\n  // Chat messages\r\n  chatMessages: ChatMessage[] = [];\r\n  isProcessingChat: boolean = false;\r\n  inputText = '';\r\n\r\n  // Agent outputs\r\n  agentOutputs: OutputItem[] = [];\r\n  public workflowForm!: FormGroup;\r\n  public fileType : string = '.zip';\r\n\r\n  // Execution state\r\n  executionStartTime: Date | null = null;\r\n  executionCompleted: boolean = false;\r\n  executionId!: string;\r\n\r\n  public workflowLogs: any[] = [];\r\n  enableStreamingLog = environment.enableLogStreaming || 'all';\r\n\r\n  public isExecutionComplete: boolean = false;\r\n  progressInterval: any;\r\n\r\n  // Component lifecycle\r\n  private destroy$ = new Subject<void>();\r\n  selectedTab: string = 'Agent Activity';\r\n  demoTabs: AvaTab[] = [\r\n    { id: 'activity', label: 'Agent Activity' },\r\n    { id: 'agents', label: 'Agent Output' },\r\n    { id: 'preview', label: 'Preview', disabled: true },\r\n  ];\r\n  errorMsg = false;\r\n  resMessage: any;\r\n  taskMessage = [];\r\n  isJsonValid = false;\r\n  disableChat : boolean = false;\r\n  selectedFiles: File[] = [];\r\n  workflowAgents: any[] = [];\r\n  userInputList: any[] = [];\r\n  progress = 0;\r\n  isLoading = false;\r\n  loaderColor: string = '';\r\n\r\n  inputFieldOrder: string[] = [];\r\n  currentInputIndex: number = 0;\r\n  activeTabId: string = 'nav-home';\r\n  \r\n\r\n  // New properties for workflow playground\r\n  agents: AgentData[] = [];\r\n  isPlaygroundCollapsed: boolean = false;\r\n  pipelineAgents: any[] = [];\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private workflowService: WorkflowService,\r\n    private inputExtractorService: WorkflowInputExtractorService,\r\n    private tokenStorage: TokenStorageService,\r\n    private loaderService: LoaderService,\r\n    private formBuilder: FormBuilder,\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.loaderService.disableLoader();\r\n    this.selectedTab = 'Agent Activity';\r\n    this.executionId = crypto.randomUUID();\r\n    // Get workflow ID from route params\r\n    this.route.paramMap.pipe(takeUntil(this.destroy$)).subscribe((params) => {\r\n      this.workflowId = params.get('id');\r\n      if (this.workflowId) {\r\n        this.loadWorkflow(this.workflowId);\r\n      } else {\r\n        // No workflow ID, redirect back to workflows page\r\n        this.router.navigate(['/build/workflows']);\r\n      }\r\n    });\r\n    // this.executeWorkflow()\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.destroy$.next();\r\n    this.destroy$.complete();\r\n    this.loaderService.enableLoader();\r\n  }\r\n  onTabChange(event: { id: string, label: string }) {\r\n    this.selectedTab = event.label;\r\n    this.activeTabId = event.id;\r\n    console.log('Tab changed:', event);\r\n  }\r\n\r\n  // Load workflow data\r\n  loadWorkflow(id: string): void {\r\n    // In a real app, this would fetch the workflow from a service\r\n    console.log(`Loading workflow with ID: ${id}`);\r\n    this.chatMessages = [\r\n      {\r\n        from: 'ai',\r\n        text: 'I am your workflow assistant. I will help you in executing this workflow.',\r\n      } as ChatMessage,\r\n    ]\r\n    this.workflowForm = this.formBuilder.group({});\r\n    \r\n    // Load workflow data for chat interface\r\n    this.workflowService.getWorkflowById(id).subscribe({\r\n        next: (res) => {\r\n        this.workflowAgents = res.workflowAgents;\r\n        this.userInputList = this.extractInputField(this.workflowAgents);\r\n        if(this.userInputList.length === 0){\r\n          this.disableChat = true;\r\n        }\r\n        this.workflowName = res.name;\r\n        this.initializeForm();\r\n        this.startInputCollection();\r\n      },\r\n        error: (err) => {\r\n          this.disableChat = true;\r\n          this.chatInterfaceComp.addAiResponse('No input is required for this workflow. Click on send button to execute the workflow.');\r\n          console.log(err);\r\n        }\r\n    });\r\n\r\n    // Also load workflow details for the new playground component\r\n    this.getWorkflowDetails(id);\r\n\r\n  }\r\n\r\n  // New method to get workflow details using workflow service\r\n  public getWorkflowDetails(id: string) {\r\n    this.workflowService.getOneWorkflow(id).subscribe({\r\n      next: (res: any) => {\r\n        if (res?.pipeLineAgents?.length) {\r\n          this.pipelineAgents = res.pipeLineAgents;\r\n          this.workflowName = res.name;\r\n\r\n          // Convert pipeline agents to AgentData format\r\n          this.agents = this.inputExtractorService.convertToAgentData(this.pipelineAgents);\r\n\r\n          // Extract input fields for form initialization\r\n          this.userInputList = this.extractInputField(res.pipeLineAgents);\r\n          this.initializeForm();\r\n        }\r\n      },\r\n      error: (e: any) => console.error(e)\r\n    });\r\n  }\r\n\r\n  public extractInputField(pipeLineAgents: any) {\r\n    const PLACEHOLDER_PATTERNS =  /(%\\d+\\$s)|\\{\\{([a-zA-Z0-9-_]+)\\}\\}/g;\r\n    const placeholderMap: { [key: string]: { agents: Set<string>; inputs: Set<string> } } = {};\r\n\r\n    pipeLineAgents.forEach((pipelineAgent: any) => {\r\n      const agentName = pipelineAgent?.agent?.name;\r\n      const agentDescription = pipelineAgent?.agent?.task?.description;\r\n      const matches = agentDescription?.matchAll(PLACEHOLDER_PATTERNS) || [];\r\n\r\n      for (const match of matches) { \r\n        const placeholder = match[1] || match[2];\r\n        const placeholderInput = match[0];\r\n        if (!placeholderMap[placeholder]) {\r\n          placeholderMap[placeholder] = { agents: new Set(), inputs: new Set() };;\r\n        }\r\n        placeholderMap[placeholder].agents.add(agentName);\r\n        placeholderMap[placeholder].inputs.add(placeholderInput);\r\n      }\r\n    })\r\n\r\n    return Object.entries(placeholderMap).map(([placeholder, { agents, inputs }]) => ({\r\n      name: [...agents].length > 2\r\n        ? `${[...agents].slice(0, -1).join(\", \")} and ${[...agents].at(-1)}`\r\n        : [...agents].join(\" and \"),\r\n      placeholder,\r\n      input: [...inputs][0],\r\n    }));\r\n  }\r\n\r\n  public isImageInput(input: string): boolean {\r\n    const match = input.match(/{{(.*?)}}/);\r\n    if (match && match[1]) {\r\n      const variableName = match[1].trim();\r\n      return variableName.startsWith('image') || variableName.startsWith('Image');\r\n    }\r\n    return false;\r\n  }\r\n\r\n  public initializeForm() {   \r\n    this.userInputList.forEach((label: any) => {\r\n      this.workflowForm.addControl(label.input, this.formBuilder.control('', Validators.required));\r\n    })\r\n  }\r\n\r\n  public isInputValid() {\r\n    return this.workflowForm.valid && this.workflowId;\r\n  }\r\n\r\n  startFakeProgress() {\r\n    this.progress = 0;\r\n    this.progressInterval = setInterval(() => {\r\n      if (this.progress < 90) {\r\n        this.progress += 5; // Increase slowly\r\n      }\r\n    }, 200); // Adjust speed\r\n  }\r\n\r\n  stopFakeProgress() {\r\n    clearInterval(this.progressInterval);\r\n    this.progress = 100;\r\n\r\n    setTimeout(() => {\r\n      this.isLoading = false;\r\n    }, 500); // Small delay to let user see 100%\r\n  }\r\n\r\n  // Handle new chat message from user\r\n  handleChatMessage(message: string): void {\r\n    // console.log('message ', message, 'is blank', message.trim() === '');\r\n    this.isProcessingChat = true;\r\n    if(message.trim() === ''){\r\n      if(this.inputFieldOrder.length === 0){\r\n        this.chatInterfaceComp.addAiResponse('Executing the workflow...');\r\n        this.executeWorkflow();\r\n      }\r\n      return;\r\n    }\r\n\r\n    if(this.isExecutionComplete || this.currentInputIndex===this.inputFieldOrder.length){\r\n        this.chatInterfaceComp.addAiResponse('Executing the workflow...');\r\n        this.executeWorkflow();\r\n      return;\r\n    }\r\n\r\n    const field = this.inputFieldOrder[this.currentInputIndex];\r\n    if (this.isImageInput(field)) {\r\n      // Ignore text input, wait for file input\r\n      this.chatInterfaceComp.addAiResponse(`Please upload an image file for ${field}`);\r\n      return;\r\n    }\r\n\r\n    this.workflowForm.get(field)?.setValue(message);\r\n    this.currentInputIndex++;\r\n\r\n    if (this.currentInputIndex < this.inputFieldOrder.length) {\r\n      this.promptForCurrentField();\r\n    } else {\r\n      this.chatInterfaceComp.addAiResponse('Thank you for the input! Executing the workflow...');\r\n      this.executeWorkflow();\r\n    }\r\n  }\r\n\r\n  // Save execution logs\r\n  saveLogs(): void {\r\n    console.log('Saving execution logs...');\r\n    // This would typically save to a service\r\n  }\r\n\r\n  // Export results\r\n  exportResults(section: 'activity' | 'output'): void {\r\n    console.log(`Exporting ${section} data...`);\r\n\r\n    if (section === 'activity') {\r\n      const data = this.activityLogs\r\n        .map((log) => `[${log.timestamp}] ${log.message}`)\r\n        .join('\\n');\r\n      this.downloadAsFile(data, 'workflow-activity-logs.txt', 'text/plain');\r\n    } else {\r\n      const data = JSON.stringify(this.agentOutputs, null, 2);\r\n      this.downloadAsFile(data, 'workflow-outputs.json', 'application/json');\r\n    }\r\n  }\r\n\r\n  // Helper method to download data as a file\r\n  private downloadAsFile(data: string, filename: string, type: string): void {\r\n    const blob = new Blob([data], { type });\r\n    const url = URL.createObjectURL(blob);\r\n    const link = document.createElement('a');\r\n    link.href = url;\r\n    link.download = filename;\r\n    link.click();\r\n    URL.revokeObjectURL(url);\r\n  }\r\n\r\n  // Handle controls for execution\r\n  handleControlAction(action: 'play' | 'pause' | 'stop'): void {\r\n    console.log(`Control action: ${action}`);\r\n    // In a real app, this would control the workflow execution\r\n\r\n    if (action === 'play') {\r\n      this.isRunning = true;\r\n    } else if (action === 'pause' || action === 'stop') {\r\n      this.isRunning = false;\r\n    }\r\n  }\r\n\r\n  // Navigate back to workflow listing\r\n  navigateBack(): void {\r\n    this.router.navigate(['/build/workflows']);\r\n  }\r\n\r\n  // Navigate to edit workflow\r\n  editWorkflow(): void {\r\n    if (this.workflowId) {\r\n      this.router.navigate(['/build/workflows/edit', this.workflowId]);\r\n    }\r\n  }\r\n\r\n  public logExecutionStatus(delay: number = 2000) {\r\n    setTimeout(() => {\r\n      if (!this.isExecutionComplete) {\r\n        console.log(this.constants);\r\n        console.log(this.constants['labels'].workflowExecProcessing);\r\n        this.workflowLogs.push({\r\n          content: this.constants['labels'].workflowExecProcessing,\r\n          color: '#F9DB24',\r\n        });\r\n      }\r\n    }, delay);\r\n  }\r\n\r\n  // public parseAnsiString(ansiString: string) {\r\n  //   const regex = ansiRegex();\r\n  //   const parts = ansiString.split(regex);\r\n  //   const matches = [...ansiString.matchAll(regex)];\r\n  //   parts.forEach((part, index) => {\r\n  //     if (part.trim() !== '') {\r\n  //       let colorCode = matches[index - 1][0];\r\n  //       if (index - 2 >= 0 && matches[index - 2]?.includes('\\u001b[1m')) {\r\n  //         colorCode = `\\u001b[1m${colorCode}`;\r\n  //       }\r\n  //       this.workflowLogs.push({\r\n  //         content: part,\r\n  //         color: this.colorMap[colorCode] || 'white',\r\n  //       });\r\n  //     }\r\n  //   });\r\n  // }\r\n\r\n  public getWorkflowLogs(executionId: string) {\r\n    this.workflowService\r\n      .workflowLogConnect(executionId)\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe({\r\n        next: (message) => {\r\n          console.log('message: ', message);\r\n          const { content, color } = message;\r\n          if (color) {\r\n            this.workflowLogs.push({ content, color });\r\n          } else if (this.enableStreamingLog === 'all') {\r\n            // this.parseAnsiString(content);\r\n          }\r\n        },\r\n        error: (err) => {\r\n          this.workflowLogs.push({\r\n            content: this.constants['workflowLog'],\r\n            color: 'red',\r\n          });\r\n          console.error('WebSocket error:', err);\r\n        },\r\n        complete: () => {\r\n          this.logExecutionStatus();\r\n          console.log('WebSocket connection closed');\r\n        },\r\n      });\r\n  }\r\n\r\n  // public parseAnsiString(ansiString: string) {\r\n  //   const regex = ansiRegex();\r\n  //   const parts = ansiString.split(regex);\r\n  //   const matches = [...ansiString.matchAll(regex)];\r\n  //   parts.forEach((part, index) => {\r\n  //     if (part.trim() !== '') {\r\n  //       let colorCode = matches[index-1][0];\r\n  //       if(index - 2 >= 0 && matches[index-2]?.includes('\\u001b[1m')) {\r\n  //         colorCode = `\\u001b[1m${colorCode}`;\r\n  //       }\r\n  //       this.workflowLogs.push({\r\n  //         content: part, \r\n  //         color: this.colorMap[colorCode] || 'white', \r\n  //       });\r\n  //     }\r\n  //   });\r\n  // }\r\n\r\n  public validateJson(output: string): any | null {\r\n    this.isJsonValid = false;\r\n    try {\r\n      const parsedOutput = JSON.parse(output);\r\n      this.isJsonValid = true;\r\n      return parsedOutput;\r\n    } catch (e) {\r\n      return null;\r\n    }\r\n  }\r\n\r\n  public executeWorkflow() {\r\n    let payload: FormData | Record<string, any> = new FormData();\r\n    let queryString = '';\r\n\r\n\r\n    this.status = ExecutionStatus.running;\r\n    if (this.selectedFiles.length) {\r\n      this.selectedFiles.forEach((file) => {\r\n        payload.append('files', file);\r\n      });\r\n      payload.append('workflowId', this.workflowId);\r\n      payload.append('userInputs', JSON.stringify(this.workflowForm.value));\r\n      payload.append('user', this.tokenStorage.getDaUsername());\r\n      payload.append('executionId', this.executionId);\r\n      queryString = '/files';\r\n    } else {\r\n      payload = {\r\n        pipeLineId: this.workflowId,\r\n        userInputs: this.workflowForm.value,\r\n        executionId: this.executionId,\r\n        user: this.tokenStorage.getDaUsername(),\r\n      };\r\n    }\r\n\r\n    this.getWorkflowLogs(this.executionId);\r\n    this.startFakeProgress();\r\n\r\n    this.workflowService\r\n      .executeWorkflow(payload, queryString)\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe({\r\n        next: (res) => {\r\n          this.isProcessingChat = false;\r\n          this.isRunning = false;\r\n          this.chatInterfaceComp.addAiResponse(res?.message || \"Workflow execution completed successfully!\");\r\n\r\n          if (res?.workflowResponse?.pipeline?.output) {\r\n            this.isExecutionComplete = true;\r\n            // console.log(this.constants['labels'].workflowExecComplete);\r\n            this.workflowLogs.push({\r\n              content: this.constants['labels'].workflowExecComplete,\r\n              color: '#0F8251',\r\n            });\r\n            this.errorMsg = false;\r\n            this.resMessage = res?.workflowResponse?.pipeline?.output;\r\n            this.agentOutputs = res?.workflowResponse?.pipeline?.tasksOutputs.map((task: any) => {\r\n              return {\r\n                id: task?.id || '',\r\n                title: task?.title || '',\r\n                content: task?.content || '',\r\n                agentName: task?.agentName || '',\r\n                timestamp: task?.timestamp || '',\r\n                type: task?.type || '',\r\n                description: task?.description || '',\r\n                expected_output: task?.expected_output || '',\r\n                summary: task?.summary || '',\r\n                raw: task?.raw || '',\r\n              };\r\n            })\r\n\r\n            this.taskMessage = res?.workflowResponse?.pipeline?.tasksOutputs.map(\r\n              (task: {\r\n                description: any;\r\n                summary: any;\r\n                raw: any;\r\n                expected_output: any;\r\n              }) => {\r\n                return {\r\n                  description: task.description,\r\n                  summary: task.summary,\r\n                  raw: task.raw,\r\n                  expected_output: task.expected_output,\r\n                };\r\n              },\r\n            );\r\n\r\n            // if(\"file_download_url\" in res?.pipeline){\r\n            //   this.isFileWriter = true;\r\n            //   this.fileDownloadLink = res?.pipeline?.file_download_url;\r\n\r\n            //   if(!this.fileDownloadLink){\r\n            //     this.fileDownloadUrlError = [];\r\n            //     this.fileDownloadUrlError.push(\"Output file is not generated yet!\")\r\n            //   }\r\n            // }\r\n            // this.isAccordian = true\r\n          }\r\n          this.validateJson(this.resMessage);\r\n          this.status = ExecutionStatus.completed;\r\n          this.stopFakeProgress();\r\n          this.selectedFiles = [];\r\n        },\r\n        error: (error) => {\r\n          this.isExecutionComplete = true;\r\n          this.isProcessingChat = false;\r\n          this.errorMsg = true;\r\n          this.resMessage = error?.error?.detail;\r\n          this.workflowService.workflowLogDisconnect();\r\n          this.workflowLogs.push({\r\n            content: this.constants['labels'].workflowLogFailed,\r\n            color: 'red',\r\n          });\r\n          this.chatInterfaceComp.addAiResponse(\r\n            'Something went wrong, Workflow execution has failed.',\r\n          );\r\n          this.selectedFiles = [];\r\n          this.stopFakeProgress();\r\n          console.log('error is', error.message);\r\n        },\r\n      });\r\n  }\r\n\r\n  // public asyncExecutePipeline() {\r\n  //   const payload: FormData = new FormData();\r\n  //   if (this.selectedFiles?.length) {\r\n  //     for (const element of this.selectedFiles) {\r\n  //       payload.append('files', element)\r\n  //     }\r\n  //   }\r\n  //   payload.append('pipeLineId', String(this.workflowId));\r\n  //   payload.append('userInputs', JSON.stringify(this.workflowForm.value));\r\n  //   payload.append('user', this.tokenStorage.getDaUsername() || '');\r\n  //   payload.append('executionId', this.executionId);\r\n\r\n  //   this.workflowService.asyncExecutePipeline(payload).pipe().subscribe({\r\n  //     next: (res: any) => {\r\n  //       if(res) {\r\n  //         // res handling\r\n  //         console.log(res);\r\n  //       }\r\n  //     },\r\n  //     error: e => {\r\n  //       // error handling\r\n  //       console.log(e);\r\n  //     }\r\n  //   })\r\n  // }\r\n\r\n  handleAttachment() {\r\n    console.log('handleAttachment');\r\n  }\r\n\r\n  onAttachmentsSelected(files: File[]) {\r\n    if(this.currentInputIndex===this.inputFieldOrder.length || this.inputFieldOrder.length===0){\r\n      this.selectedFiles = files;\r\n      return;\r\n    }\r\n\r\n    const field = this.inputFieldOrder[this.currentInputIndex];\r\n    if(this.isImageInput(field)){\r\n      if (files && files.length > 0) {\r\n        this.onImageSelected(files[0]);\r\n      }\r\n    } else {\r\n      this.selectedFiles = files;\r\n    }\r\n  }\r\n\r\n  startInputCollection(){\r\n    this.inputFieldOrder = Object.keys(this.workflowForm.controls);\r\n    this.currentInputIndex = 0;\r\n    if (this.inputFieldOrder.length > 0) {\r\n      this.promptForCurrentField();\r\n    }\r\n    else{\r\n      this.disableChat = true;\r\n      this.chatInterfaceComp.addAiResponse('No input is required for this workflow. Click on send button to execute the workflow.');\r\n    }\r\n  }\r\n\r\n  promptForCurrentField() {\r\n    const field = this.inputFieldOrder[this.currentInputIndex];\r\n    if (this.isImageInput(field)) {\r\n      this.fileType = '.jpeg,.png,.jpg,.svg';\r\n      this.chatInterfaceComp.addAiResponse(`Please upload an image for ${field}`);\r\n      // UI should now show a file input for the user\r\n    } else {\r\n      this.fileType = '.zip'; // or whatever default you want for non-image\r\n      this.chatInterfaceComp.addAiResponse(`Please enter the value of ${field}`);\r\n    }\r\n  }\r\n\r\n  onImageSelected(file: File) {\r\n    const field = this.inputFieldOrder[this.currentInputIndex];\r\n    if (!this.isImageInput(field)) return;\r\n\r\n    const reader = new FileReader();\r\n    reader.onload = () => {\r\n      const base64String = (reader.result as string); \r\n      this.workflowForm.get(field)?.setValue(base64String);\r\n      this.currentInputIndex++;\r\n      if (this.currentInputIndex < this.inputFieldOrder.length) {\r\n        this.promptForCurrentField();\r\n      } else {\r\n        this.chatInterfaceComp.addAiResponse('Thank you! Running the workflow...');\r\n        this.executeWorkflow();\r\n      }\r\n    };\r\n    reader.readAsDataURL(file);\r\n  }\r\n\r\n  // Event handlers for workflow playground\r\n  onPlaygroundBackClicked(): void {\r\n    this.navigateBack();\r\n  }\r\n\r\n  onPlaygroundCollapseToggled(isCollapsed: boolean): void {\r\n    this.isPlaygroundCollapsed = isCollapsed;\r\n  }\r\n\r\n  onAgentInputChanged(event: {agentId: number, inputIndex: number, value: string}): void {\r\n    // Find the agent and update the input value\r\n    const agent = this.agents.find(a => a.id === event.agentId);\r\n    if (agent && agent.inputs && agent.inputs[event.inputIndex]) {\r\n      agent.inputs[event.inputIndex].value = event.value;\r\n\r\n      // Update the form control if it exists\r\n      const placeholder = agent.inputs[event.inputIndex].placeholder;\r\n      if (this.workflowForm.get(placeholder)) {\r\n        this.workflowForm.get(placeholder)?.setValue(event.value);\r\n      }\r\n    }\r\n  }\r\n\r\n  onAgentFileSelected(event: {agentId: number, inputIndex: number, files: File[]}): void {\r\n    // Find the agent and update the files\r\n    const agent = this.agents.find(a => a.id === event.agentId);\r\n    if (agent && agent.inputs && agent.inputs[event.inputIndex]) {\r\n      agent.inputs[event.inputIndex].files = event.files;\r\n\r\n      // Add files to selectedFiles array for execution\r\n      this.selectedFiles = [...this.selectedFiles, ...event.files];\r\n    }\r\n  }\r\n}\r\n", "<div class=\"workflow-execution-container\">\r\n  <!-- SVG Gradient Definitions for Icons -->\r\n  <svg width=\"0\" height=\"0\" style=\"position: absolute\">\r\n    <defs>\r\n      <linearGradient id=\"gradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"0%\">\r\n        <stop offset=\"0%\" stop-color=\"#6566CD\" />\r\n        <stop offset=\"100%\" stop-color=\"#F96CAB\" />\r\n      </linearGradient>\r\n    </defs>\r\n  </svg>\r\n  <div class=\"execution-content\" role=\"main\">\r\n    <!-- Left Panel: Workflow Playground with Execute Button -->\r\n    <div class=\"left-panel\" role=\"region\" aria-label=\"Workflow Input Panel\">\r\n      <app-workflow-playground\r\n        [agents]=\"agents\"\r\n        [isCollapsed]=\"isPlaygroundCollapsed\"\r\n        [workflowName]=\"workflowName\"\r\n        (backClicked)=\"onPlaygroundBackClicked()\"\r\n        (collapseToggled)=\"onPlaygroundCollapseToggled($event)\"\r\n        (agentInputChanged)=\"onAgentInputChanged($event)\"\r\n        (agentFileSelected)=\"onAgentFileSelected($event)\"\r\n        class=\"playground-component\"\r\n        role=\"region\"\r\n        aria-label=\"Workflow Playground\">\r\n      </app-workflow-playground>\r\n\r\n      <!-- Execute Button -->\r\n      <div class=\"execute-button-container\" *ngIf=\"!isPlaygroundCollapsed\">\r\n        <ava-button\r\n          label=\"Execute Workflow\"\r\n          variant=\"primary\"\r\n          size=\"large\"\r\n          [disabled]=\"!isInputValid() || isRunning\"\r\n          [customStyles]=\"{\r\n            width: '100%',\r\n            background: 'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',\r\n            '--button-effect-color': '33, 90, 214',\r\n            'border-radius': '8px',\r\n            'box-shadow': 'none'\r\n          }\"\r\n          (click)=\"executeWorkflow()\"\r\n        >\r\n        </ava-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Right Panel: Three Sections (Execution, Output, Configuration) -->\r\n    <div class=\"right-panel\" role=\"region\" aria-label=\"Workflow Results\">\r\n      <!-- Tab Navigation -->\r\n      <div class=\"tabs-header\">\r\n        <div class=\"tabs-container\">\r\n          <ava-tabs\r\n            [tabs]=\"navigationTabs\"\r\n            [activeTabId]=\"activeTabId\"\r\n            variant=\"button\"\r\n            buttonShape=\"pill\"\r\n            [showContentPanels]=\"false\"\r\n            (tabChange)=\"onTabChange($event)\"\r\n            ariaLabel=\"Workflow sections navigation\"\r\n          ></ava-tabs>\r\n        </div>\r\n        <div class=\"header-actions\">\r\n          <ava-button\r\n            label=\"Send for Approval\"\r\n            variant=\"primary\"\r\n            [customStyles]=\"{\r\n              background: 'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',\r\n              '--button-effect-color': '33, 90, 214',\r\n              'border-radius': '8px',\r\n              'box-shadow': 'none'\r\n            }\"\r\n            size=\"medium\"\r\n          >\r\n          </ava-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Content Sections -->\r\n      <div class=\"content-sections\">\r\n        <!-- Execution Section -->\r\n        <div *ngIf=\"activeTabId === 'nav-home'\" class=\"section-content execution-section\">\r\n          <!-- Execution Monitor Header -->\r\n          <div class=\"execution-monitor-header\">\r\n            <h2>Execution Monitor</h2>\r\n            <div class=\"progress-section\">\r\n              <span class=\"progress-label\">Overall Progress</span>\r\n              <div class=\"progress-container\">\r\n                <div class=\"progress-bar\">\r\n                  <div class=\"progress-fill\" [style.width.%]=\"progress\"></div>\r\n                </div>\r\n                <span class=\"progress-text\">{{ progress }}%</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Two Column Layout: Pipeline Steps + Execution Logs -->\r\n          <div class=\"execution-content-grid\">\r\n            <!-- Left Column: Pipeline Steps -->\r\n            <div class=\"pipeline-steps-section\">\r\n              <h3>Pipeline Steps</h3>\r\n              <div class=\"pipeline-agents-list\">\r\n                <div\r\n                  *ngFor=\"let agent of agents; let i = index\"\r\n                  class=\"pipeline-agent-item\"\r\n                  [class.active]=\"i === 0\"\r\n                  [class.completed]=\"false\">\r\n                  <div class=\"agent-icon\">\r\n                    <ava-icon iconName=\"bot\" [iconSize]=\"20\" iconColor=\"#666\"></ava-icon>\r\n                  </div>\r\n                  <div class=\"agent-info\">\r\n                    <span class=\"agent-name\">{{ agent.name }}</span>\r\n                    <span class=\"agent-status\" *ngIf=\"i === 0\">Agent 1 is currently working</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Right Column: Execution Logs -->\r\n            <div class=\"execution-logs-section\">\r\n              <div class=\"logs-header\">\r\n                <h3>Execution Logs</h3>\r\n                <span class=\"logs-subtitle\">Agent 1's Output</span>\r\n              </div>\r\n              <div class=\"logs-content\">\r\n                <div class=\"log-entry\" *ngFor=\"let log of workflowLogs; let i = index\">\r\n                  <span class=\"log-number\">{{ (i + 1 < 10 ? '0' : '') + (i + 1) }}</span>\r\n                  <span class=\"log-message\" [style.color]=\"log.color\">{{ log.content }}</span>\r\n                </div>\r\n                <div class=\"show-more\" *ngIf=\"workflowLogs.length > 10\">\r\n                  <button class=\"show-more-btn\">Show More</button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Output Section -->\r\n        <div *ngIf=\"activeTabId === 'nav-products'\" class=\"section-content output-section\">\r\n          <app-agent-output\r\n            [outputs]=\"taskMessage\"\r\n            (export)=\"exportResults('output')\"\r\n          ></app-agent-output>\r\n        </div>\r\n\r\n        <!-- Configuration Section -->\r\n        <div *ngIf=\"activeTabId === 'nav-services'\" class=\"section-content configuration-section\">\r\n          <div class=\"configuration-content\">\r\n            <p>Configuration settings will be displayed here.</p>\r\n            <!-- Add configuration content as needed -->\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,OAAO,EAAEC,SAAS,QAAQ,MAAM;AACzC,SAAiCC,WAAW,EAAEC,UAAU,QAAQ,gBAAgB;AAEhF;AACA,SAASC,sBAAsB,QAAQ,4DAA4D;AAKnG,SACEC,oBAAoB,QAEf,kDAAkD;AACzD,SACEC,eAAe,EACfC,aAAa,EAEbC,aAAa,QACR,wBAAwB;AAG/B,SAASC,WAAW,QAAQ,kCAAkC;AAC9D,OAAOC,iBAAiB,MAAM,+BAA+B;AAG7D,SAASC,eAAe,QAAqB,gCAAgC;AAE7E,SAASC,2BAA2B,QAAmB,gEAAgE;;;;;;;;;;;;;;;;;;;;;;;;ICF/GC,EADF,CAAAC,cAAA,cAAqE,qBAclE;IADCD,EAAA,CAAAE,UAAA,mBAAAC,sEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,eAAA,EAAiB;IAAA,EAAC;IAG/BT,EADE,CAAAU,YAAA,EAAa,EACT;;;;IAXFV,EAAA,CAAAW,SAAA,EAAyC;IACzCX,EADA,CAAAY,UAAA,cAAAN,MAAA,CAAAO,YAAA,MAAAP,MAAA,CAAAQ,SAAA,CAAyC,iBAAAd,EAAA,CAAAe,eAAA,IAAAC,GAAA,EAOvC;;;;;IAwEQhB,EAAA,CAAAC,cAAA,eAA2C;IAAAD,EAAA,CAAAiB,MAAA,mCAA4B;IAAAjB,EAAA,CAAAU,YAAA,EAAO;;;;;IALhFV,EALF,CAAAC,cAAA,cAI4B,cACF;IACtBD,EAAA,CAAAkB,SAAA,mBAAqE;IACvElB,EAAA,CAAAU,YAAA,EAAM;IAEJV,EADF,CAAAC,cAAA,cAAwB,eACG;IAAAD,EAAA,CAAAiB,MAAA,GAAgB;IAAAjB,EAAA,CAAAU,YAAA,EAAO;IAChDV,EAAA,CAAAmB,UAAA,IAAAC,wDAAA,mBAA2C;IAE/CpB,EADE,CAAAU,YAAA,EAAM,EACF;;;;;IARJV,EADA,CAAAqB,WAAA,WAAAC,IAAA,OAAwB,oBACC;IAEEtB,EAAA,CAAAW,SAAA,GAAe;IAAfX,EAAA,CAAAY,UAAA,gBAAe;IAGfZ,EAAA,CAAAW,SAAA,GAAgB;IAAhBX,EAAA,CAAAuB,iBAAA,CAAAC,QAAA,CAAAC,IAAA,CAAgB;IACbzB,EAAA,CAAAW,SAAA,EAAa;IAAbX,EAAA,CAAAY,UAAA,SAAAU,IAAA,OAAa;;;;;IAc3CtB,EADF,CAAAC,cAAA,cAAuE,eAC5C;IAAAD,EAAA,CAAAiB,MAAA,GAAuC;IAAAjB,EAAA,CAAAU,YAAA,EAAO;IACvEV,EAAA,CAAAC,cAAA,eAAoD;IAAAD,EAAA,CAAAiB,MAAA,GAAiB;IACvEjB,EADuE,CAAAU,YAAA,EAAO,EACxE;;;;;IAFqBV,EAAA,CAAAW,SAAA,GAAuC;IAAvCX,EAAA,CAAAuB,iBAAA,EAAAG,IAAA,yBAAAA,IAAA,MAAuC;IACtC1B,EAAA,CAAAW,SAAA,EAAyB;IAAzBX,EAAA,CAAA2B,WAAA,UAAAC,MAAA,CAAAC,KAAA,CAAyB;IAAC7B,EAAA,CAAAW,SAAA,EAAiB;IAAjBX,EAAA,CAAAuB,iBAAA,CAAAK,MAAA,CAAAE,OAAA,CAAiB;;;;;IAGrE9B,EADF,CAAAC,cAAA,cAAwD,iBACxB;IAAAD,EAAA,CAAAiB,MAAA,gBAAS;IACzCjB,EADyC,CAAAU,YAAA,EAAS,EAC5C;;;;;IA/CVV,EAHJ,CAAAC,cAAA,cAAkF,cAE1C,SAChC;IAAAD,EAAA,CAAAiB,MAAA,wBAAiB;IAAAjB,EAAA,CAAAU,YAAA,EAAK;IAExBV,EADF,CAAAC,cAAA,cAA8B,eACC;IAAAD,EAAA,CAAAiB,MAAA,uBAAgB;IAAAjB,EAAA,CAAAU,YAAA,EAAO;IAElDV,EADF,CAAAC,cAAA,cAAgC,cACJ;IACxBD,EAAA,CAAAkB,SAAA,cAA4D;IAC9DlB,EAAA,CAAAU,YAAA,EAAM;IACNV,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAiB,MAAA,IAAe;IAGjDjB,EAHiD,CAAAU,YAAA,EAAO,EAC9C,EACF,EACF;IAMFV,EAHJ,CAAAC,cAAA,eAAoC,eAEE,UAC9B;IAAAD,EAAA,CAAAiB,MAAA,sBAAc;IAAAjB,EAAA,CAAAU,YAAA,EAAK;IACvBV,EAAA,CAAAC,cAAA,eAAkC;IAChCD,EAAA,CAAAmB,UAAA,KAAAY,iDAAA,kBAI4B;IAUhC/B,EADE,CAAAU,YAAA,EAAM,EACF;IAKFV,EAFJ,CAAAC,cAAA,eAAoC,eACT,UACnB;IAAAD,EAAA,CAAAiB,MAAA,sBAAc;IAAAjB,EAAA,CAAAU,YAAA,EAAK;IACvBV,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAiB,MAAA,wBAAgB;IAC9CjB,EAD8C,CAAAU,YAAA,EAAO,EAC/C;IACNV,EAAA,CAAAC,cAAA,eAA0B;IAKxBD,EAJA,CAAAmB,UAAA,KAAAa,iDAAA,kBAAuE,KAAAC,iDAAA,kBAIf;IAMhEjC,EAHM,CAAAU,YAAA,EAAM,EACF,EACF,EACF;;;;IA9C+BV,EAAA,CAAAW,SAAA,GAA0B;IAA1BX,EAAA,CAAA2B,WAAA,UAAArB,MAAA,CAAA4B,QAAA,MAA0B;IAE3BlC,EAAA,CAAAW,SAAA,GAAe;IAAfX,EAAA,CAAAmC,kBAAA,KAAA7B,MAAA,CAAA4B,QAAA,MAAe;IAYvBlC,EAAA,CAAAW,SAAA,GAAW;IAAXX,EAAA,CAAAY,UAAA,YAAAN,MAAA,CAAA8B,MAAA,CAAW;IAsBQpC,EAAA,CAAAW,SAAA,GAAiB;IAAjBX,EAAA,CAAAY,UAAA,YAAAN,MAAA,CAAA+B,YAAA,CAAiB;IAIhCrC,EAAA,CAAAW,SAAA,EAA8B;IAA9BX,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAA+B,YAAA,CAAAC,MAAA,MAA8B;;;;;;IAU5DtC,EADF,CAAAC,cAAA,cAAmF,2BAIhF;IADCD,EAAA,CAAAE,UAAA,oBAAAqC,8EAAA;MAAAvC,EAAA,CAAAI,aAAA,CAAAoC,GAAA;MAAA,MAAAlC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAUF,MAAA,CAAAmC,aAAA,CAAc,QAAQ,CAAC;IAAA,EAAC;IAEtCzC,EADG,CAAAU,YAAA,EAAmB,EAChB;;;;IAHFV,EAAA,CAAAW,SAAA,EAAuB;IAAvBX,EAAA,CAAAY,UAAA,YAAAN,MAAA,CAAAoC,WAAA,CAAuB;;;;;IAQvB1C,EAFJ,CAAAC,cAAA,cAA0F,cACrD,QAC9B;IAAAD,EAAA,CAAAiB,MAAA,qDAA8C;IAGrDjB,EAHqD,CAAAU,YAAA,EAAI,EAEjD,EACF;;;ADpGd,WAAaiC,0BAA0B;EAAjC,MAAOA,0BAA0B;IA0E3BC,KAAA;IACAC,MAAA;IACAC,eAAA;IACAC,qBAAA;IACAC,YAAA;IACAC,aAAA;IACAC,WAAA;IA/EVC,cAAc,GAAc,CAC1B;MAAEC,EAAE,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAW,CAAE,EACtC;MAAED,EAAE,EAAE,cAAc;MAAEC,KAAK,EAAE;IAAQ,CAAE,EACvC;MAAED,EAAE,EAAE,cAAc;MAAEC,KAAK,EAAE;IAAe,CAAE,CAC/C;IACD;IACAC,UAAU,GAAkB,IAAI;IAChCC,YAAY,GAAW,UAAU;IAEjCC,SAAS,GAAG3D,iBAAwC;IAGpD4D,iBAAiB;IAEjB;IACAC,YAAY,GAAkB,EAAE;IAChCC,gBAAgB,GAAW,CAAC;IAC5BC,gBAAgB;IAChB9C,SAAS,GAAY,KAAK;IAC1B+C,MAAM,GAAoB/D,eAAe,CAACgE,UAAU;IAEpD;IACAC,YAAY,GAAkB,EAAE;IAChCC,gBAAgB,GAAY,KAAK;IACjCC,SAAS,GAAG,EAAE;IAEd;IACAC,YAAY,GAAiB,EAAE;IACxBC,YAAY;IACZC,QAAQ,GAAY,MAAM;IAEjC;IACAC,kBAAkB,GAAgB,IAAI;IACtCC,kBAAkB,GAAY,KAAK;IACnCC,WAAW;IAEJlC,YAAY,GAAU,EAAE;IAC/BmC,kBAAkB,GAAG5E,WAAW,CAAC6E,kBAAkB,IAAI,KAAK;IAErDC,mBAAmB,GAAY,KAAK;IAC3CC,gBAAgB;IAEhB;IACQC,QAAQ,GAAG,IAAIzF,OAAO,EAAQ;IACtC0F,WAAW,GAAW,gBAAgB;IACtCC,QAAQ,GAAa,CACnB;MAAE1B,EAAE,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAgB,CAAE,EAC3C;MAAED,EAAE,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAc,CAAE,EACvC;MAAED,EAAE,EAAE,SAAS;MAAEC,KAAK,EAAE,SAAS;MAAE0B,QAAQ,EAAE;IAAI,CAAE,CACpD;IACDC,QAAQ,GAAG,KAAK;IAChBC,UAAU;IACVvC,WAAW,GAAG,EAAE;IAChBwC,WAAW,GAAG,KAAK;IACnBC,WAAW,GAAa,KAAK;IAC7BC,aAAa,GAAW,EAAE;IAC1BC,cAAc,GAAU,EAAE;IAC1BC,aAAa,GAAU,EAAE;IACzBpD,QAAQ,GAAG,CAAC;IACZqD,SAAS,GAAG,KAAK;IACjBC,WAAW,GAAW,EAAE;IAExBC,eAAe,GAAa,EAAE;IAC9BC,iBAAiB,GAAW,CAAC;IAC7BC,WAAW,GAAW,UAAU;IAGhC;IACAvD,MAAM,GAAgB,EAAE;IACxBwD,qBAAqB,GAAY,KAAK;IACtCC,cAAc,GAAU,EAAE;IAE1BC,YACUlD,KAAqB,EACrBC,MAAc,EACdC,eAAgC,EAChCC,qBAAoD,EACpDC,YAAiC,EACjCC,aAA4B,EAC5BC,WAAwB;MANxB,KAAAN,KAAK,GAALA,KAAK;MACL,KAAAC,MAAM,GAANA,MAAM;MACN,KAAAC,eAAe,GAAfA,eAAe;MACf,KAAAC,qBAAqB,GAArBA,qBAAqB;MACrB,KAAAC,YAAY,GAAZA,YAAY;MACZ,KAAAC,aAAa,GAAbA,aAAa;MACb,KAAAC,WAAW,GAAXA,WAAW;IAClB;IAEH6C,QAAQA,CAAA;MACN,IAAI,CAAC9C,aAAa,CAAC+C,aAAa,EAAE;MAClC,IAAI,CAACnB,WAAW,GAAG,gBAAgB;MACnC,IAAI,CAACN,WAAW,GAAG0B,MAAM,CAACC,UAAU,EAAE;MACtC;MACA,IAAI,CAACtD,KAAK,CAACuD,QAAQ,CAACC,IAAI,CAAChH,SAAS,CAAC,IAAI,CAACwF,QAAQ,CAAC,CAAC,CAACyB,SAAS,CAAEC,MAAM,IAAI;QACtE,IAAI,CAAChD,UAAU,GAAGgD,MAAM,CAACC,GAAG,CAAC,IAAI,CAAC;QAClC,IAAI,IAAI,CAACjD,UAAU,EAAE;UACnB,IAAI,CAACkD,YAAY,CAAC,IAAI,CAAClD,UAAU,CAAC;QACpC,CAAC,MAAM;UACL;UACA,IAAI,CAACT,MAAM,CAAC4D,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;QAC5C;MACF,CAAC,CAAC;MACF;IACF;IAEAC,WAAWA,CAAA;MACT,IAAI,CAAC9B,QAAQ,CAAC+B,IAAI,EAAE;MACpB,IAAI,CAAC/B,QAAQ,CAACgC,QAAQ,EAAE;MACxB,IAAI,CAAC3D,aAAa,CAAC4D,YAAY,EAAE;IACnC;IACAC,WAAWA,CAACC,KAAoC;MAC9C,IAAI,CAAClC,WAAW,GAAGkC,KAAK,CAAC1D,KAAK;MAC9B,IAAI,CAACsC,WAAW,GAAGoB,KAAK,CAAC3D,EAAE;MAC3B4D,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEF,KAAK,CAAC;IACpC;IAEA;IACAP,YAAYA,CAACpD,EAAU;MACrB;MACA4D,OAAO,CAACC,GAAG,CAAC,6BAA6B7D,EAAE,EAAE,CAAC;MAC9C,IAAI,CAACW,YAAY,GAAG,CAClB;QACEmD,IAAI,EAAE,IAAI;QACVC,IAAI,EAAE;OACQ,CACjB;MACD,IAAI,CAAChD,YAAY,GAAG,IAAI,CAACjB,WAAW,CAACkE,KAAK,CAAC,EAAE,CAAC;MAE9C;MACA,IAAI,CAACtE,eAAe,CAACuE,eAAe,CAACjE,EAAE,CAAC,CAACiD,SAAS,CAAC;QAC/CM,IAAI,EAAGW,GAAG,IAAI;UACd,IAAI,CAACjC,cAAc,GAAGiC,GAAG,CAACjC,cAAc;UACxC,IAAI,CAACC,aAAa,GAAG,IAAI,CAACiC,iBAAiB,CAAC,IAAI,CAAClC,cAAc,CAAC;UAChE,IAAG,IAAI,CAACC,aAAa,CAAChD,MAAM,KAAK,CAAC,EAAC;YACjC,IAAI,CAAC6C,WAAW,GAAG,IAAI;UACzB;UACA,IAAI,CAAC5B,YAAY,GAAG+D,GAAG,CAAC7F,IAAI;UAC5B,IAAI,CAAC+F,cAAc,EAAE;UACrB,IAAI,CAACC,oBAAoB,EAAE;QAC7B,CAAC;QACCC,KAAK,EAAGC,GAAG,IAAI;UACb,IAAI,CAACxC,WAAW,GAAG,IAAI;UACvB,IAAI,CAAC1B,iBAAiB,CAACmE,aAAa,CAAC,uFAAuF,CAAC;UAC7HZ,OAAO,CAACC,GAAG,CAACU,GAAG,CAAC;QAClB;OACH,CAAC;MAEF;MACA,IAAI,CAACE,kBAAkB,CAACzE,EAAE,CAAC;IAE7B;IAEA;IACOyE,kBAAkBA,CAACzE,EAAU;MAClC,IAAI,CAACN,eAAe,CAACgF,cAAc,CAAC1E,EAAE,CAAC,CAACiD,SAAS,CAAC;QAChDM,IAAI,EAAGW,GAAQ,IAAI;UACjB,IAAIA,GAAG,EAAES,cAAc,EAAEzF,MAAM,EAAE;YAC/B,IAAI,CAACuD,cAAc,GAAGyB,GAAG,CAACS,cAAc;YACxC,IAAI,CAACxE,YAAY,GAAG+D,GAAG,CAAC7F,IAAI;YAE5B;YACA,IAAI,CAACW,MAAM,GAAG,IAAI,CAACW,qBAAqB,CAACiF,kBAAkB,CAAC,IAAI,CAACnC,cAAc,CAAC;YAEhF;YACA,IAAI,CAACP,aAAa,GAAG,IAAI,CAACiC,iBAAiB,CAACD,GAAG,CAACS,cAAc,CAAC;YAC/D,IAAI,CAACP,cAAc,EAAE;UACvB;QACF,CAAC;QACDE,KAAK,EAAGO,CAAM,IAAKjB,OAAO,CAACU,KAAK,CAACO,CAAC;OACnC,CAAC;IACJ;IAEOV,iBAAiBA,CAACQ,cAAmB;MAC1C,MAAMG,oBAAoB,GAAI,qCAAqC;MACnE,MAAMC,cAAc,GAAoE,EAAE;MAE1FJ,cAAc,CAACK,OAAO,CAAEC,aAAkB,IAAI;QAC5C,MAAMC,SAAS,GAAGD,aAAa,EAAEE,KAAK,EAAE9G,IAAI;QAC5C,MAAM+G,gBAAgB,GAAGH,aAAa,EAAEE,KAAK,EAAEE,IAAI,EAAEC,WAAW;QAChE,MAAMC,OAAO,GAAGH,gBAAgB,EAAEI,QAAQ,CAACV,oBAAoB,CAAC,IAAI,EAAE;QAEtE,KAAK,MAAMW,KAAK,IAAIF,OAAO,EAAE;UAC3B,MAAMG,WAAW,GAAGD,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC;UACxC,MAAME,gBAAgB,GAAGF,KAAK,CAAC,CAAC,CAAC;UACjC,IAAI,CAACV,cAAc,CAACW,WAAW,CAAC,EAAE;YAChCX,cAAc,CAACW,WAAW,CAAC,GAAG;cAAE1G,MAAM,EAAE,IAAI4G,GAAG,EAAE;cAAEC,MAAM,EAAE,IAAID,GAAG;YAAE,CAAE;YAAC;UACzE;UACAb,cAAc,CAACW,WAAW,CAAC,CAAC1G,MAAM,CAAC8G,GAAG,CAACZ,SAAS,CAAC;UACjDH,cAAc,CAACW,WAAW,CAAC,CAACG,MAAM,CAACC,GAAG,CAACH,gBAAgB,CAAC;QAC1D;MACF,CAAC,CAAC;MAEF,OAAOI,MAAM,CAACC,OAAO,CAACjB,cAAc,CAAC,CAACkB,GAAG,CAAC,CAAC,CAACP,WAAW,EAAE;QAAE1G,MAAM;QAAE6G;MAAM,CAAE,CAAC,MAAM;QAChFxH,IAAI,EAAE,CAAC,GAAGW,MAAM,CAAC,CAACE,MAAM,GAAG,CAAC,GACxB,GAAG,CAAC,GAAGF,MAAM,CAAC,CAACkH,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAGnH,MAAM,CAAC,CAACoH,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,GAClE,CAAC,GAAGpH,MAAM,CAAC,CAACmH,IAAI,CAAC,OAAO,CAAC;QAC7BT,WAAW;QACXW,KAAK,EAAE,CAAC,GAAGR,MAAM,CAAC,CAAC,CAAC;OACrB,CAAC,CAAC;IACL;IAEOS,YAAYA,CAACD,KAAa;MAC/B,MAAMZ,KAAK,GAAGY,KAAK,CAACZ,KAAK,CAAC,WAAW,CAAC;MACtC,IAAIA,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC,EAAE;QACrB,MAAMc,YAAY,GAAGd,KAAK,CAAC,CAAC,CAAC,CAACe,IAAI,EAAE;QACpC,OAAOD,YAAY,CAACE,UAAU,CAAC,OAAO,CAAC,IAAIF,YAAY,CAACE,UAAU,CAAC,OAAO,CAAC;MAC7E;MACA,OAAO,KAAK;IACd;IAEOrC,cAAcA,CAAA;MACnB,IAAI,CAAClC,aAAa,CAAC8C,OAAO,CAAE/E,KAAU,IAAI;QACxC,IAAI,CAACc,YAAY,CAAC2F,UAAU,CAACzG,KAAK,CAACoG,KAAK,EAAE,IAAI,CAACvG,WAAW,CAAC6G,OAAO,CAAC,EAAE,EAAEzK,UAAU,CAAC0K,QAAQ,CAAC,CAAC;MAC9F,CAAC,CAAC;IACJ;IAEOnJ,YAAYA,CAAA;MACjB,OAAO,IAAI,CAACsD,YAAY,CAAC8F,KAAK,IAAI,IAAI,CAAC3G,UAAU;IACnD;IAEA4G,iBAAiBA,CAAA;MACf,IAAI,CAAChI,QAAQ,GAAG,CAAC;MACjB,IAAI,CAACyC,gBAAgB,GAAGwF,WAAW,CAAC,MAAK;QACvC,IAAI,IAAI,CAACjI,QAAQ,GAAG,EAAE,EAAE;UACtB,IAAI,CAACA,QAAQ,IAAI,CAAC,CAAC,CAAC;QACtB;MACF,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IACX;IAEAkI,gBAAgBA,CAAA;MACdC,aAAa,CAAC,IAAI,CAAC1F,gBAAgB,CAAC;MACpC,IAAI,CAACzC,QAAQ,GAAG,GAAG;MAEnBoI,UAAU,CAAC,MAAK;QACd,IAAI,CAAC/E,SAAS,GAAG,KAAK;MACxB,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IACX;IAEA;IACAgF,iBAAiBA,CAACC,OAAe;MAC/B;MACA,IAAI,CAACxG,gBAAgB,GAAG,IAAI;MAC5B,IAAGwG,OAAO,CAACZ,IAAI,EAAE,KAAK,EAAE,EAAC;QACvB,IAAG,IAAI,CAACnE,eAAe,CAACnD,MAAM,KAAK,CAAC,EAAC;UACnC,IAAI,CAACmB,iBAAiB,CAACmE,aAAa,CAAC,2BAA2B,CAAC;UACjE,IAAI,CAACnH,eAAe,EAAE;QACxB;QACA;MACF;MAEA,IAAG,IAAI,CAACiE,mBAAmB,IAAI,IAAI,CAACgB,iBAAiB,KAAG,IAAI,CAACD,eAAe,CAACnD,MAAM,EAAC;QAChF,IAAI,CAACmB,iBAAiB,CAACmE,aAAa,CAAC,2BAA2B,CAAC;QACjE,IAAI,CAACnH,eAAe,EAAE;QACxB;MACF;MAEA,MAAMgK,KAAK,GAAG,IAAI,CAAChF,eAAe,CAAC,IAAI,CAACC,iBAAiB,CAAC;MAC1D,IAAI,IAAI,CAACgE,YAAY,CAACe,KAAK,CAAC,EAAE;QAC5B;QACA,IAAI,CAAChH,iBAAiB,CAACmE,aAAa,CAAC,mCAAmC6C,KAAK,EAAE,CAAC;QAChF;MACF;MAEA,IAAI,CAACtG,YAAY,CAACoC,GAAG,CAACkE,KAAK,CAAC,EAAEC,QAAQ,CAACF,OAAO,CAAC;MAC/C,IAAI,CAAC9E,iBAAiB,EAAE;MAExB,IAAI,IAAI,CAACA,iBAAiB,GAAG,IAAI,CAACD,eAAe,CAACnD,MAAM,EAAE;QACxD,IAAI,CAACqI,qBAAqB,EAAE;MAC9B,CAAC,MAAM;QACL,IAAI,CAAClH,iBAAiB,CAACmE,aAAa,CAAC,oDAAoD,CAAC;QAC1F,IAAI,CAACnH,eAAe,EAAE;MACxB;IACF;IAEA;IACAmK,QAAQA,CAAA;MACN5D,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;MACvC;IACF;IAEA;IACAxE,aAAaA,CAACoI,OAA8B;MAC1C7D,OAAO,CAACC,GAAG,CAAC,aAAa4D,OAAO,UAAU,CAAC;MAE3C,IAAIA,OAAO,KAAK,UAAU,EAAE;QAC1B,MAAMC,IAAI,GAAG,IAAI,CAACpH,YAAY,CAC3B2F,GAAG,CAAEpC,GAAG,IAAK,IAAIA,GAAG,CAAC8D,SAAS,KAAK9D,GAAG,CAACuD,OAAO,EAAE,CAAC,CACjDjB,IAAI,CAAC,IAAI,CAAC;QACb,IAAI,CAACyB,cAAc,CAACF,IAAI,EAAE,4BAA4B,EAAE,YAAY,CAAC;MACvE,CAAC,MAAM;QACL,MAAMA,IAAI,GAAGG,IAAI,CAACC,SAAS,CAAC,IAAI,CAAChH,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC;QACvD,IAAI,CAAC8G,cAAc,CAACF,IAAI,EAAE,uBAAuB,EAAE,kBAAkB,CAAC;MACxE;IACF;IAEA;IACQE,cAAcA,CAACF,IAAY,EAAEK,QAAgB,EAAEC,IAAY;MACjE,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACR,IAAI,CAAC,EAAE;QAAEM;MAAI,CAAE,CAAC;MACvC,MAAMG,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;MACrC,MAAMK,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGN,GAAG;MACfG,IAAI,CAACI,QAAQ,GAAGX,QAAQ;MACxBO,IAAI,CAACK,KAAK,EAAE;MACZP,GAAG,CAACQ,eAAe,CAACT,GAAG,CAAC;IAC1B;IAEA;IACAU,mBAAmBA,CAACC,MAAiC;MACnDlF,OAAO,CAACC,GAAG,CAAC,mBAAmBiF,MAAM,EAAE,CAAC;MACxC;MAEA,IAAIA,MAAM,KAAK,MAAM,EAAE;QACrB,IAAI,CAACpL,SAAS,GAAG,IAAI;MACvB,CAAC,MAAM,IAAIoL,MAAM,KAAK,OAAO,IAAIA,MAAM,KAAK,MAAM,EAAE;QAClD,IAAI,CAACpL,SAAS,GAAG,KAAK;MACxB;IACF;IAEA;IACAqL,YAAYA,CAAA;MACV,IAAI,CAACtJ,MAAM,CAAC4D,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;IAC5C;IAEA;IACA2F,YAAYA,CAAA;MACV,IAAI,IAAI,CAAC9I,UAAU,EAAE;QACnB,IAAI,CAACT,MAAM,CAAC4D,QAAQ,CAAC,CAAC,uBAAuB,EAAE,IAAI,CAACnD,UAAU,CAAC,CAAC;MAClE;IACF;IAEO+I,kBAAkBA,CAACC,KAAA,GAAgB,IAAI;MAC5ChC,UAAU,CAAC,MAAK;QACd,IAAI,CAAC,IAAI,CAAC5F,mBAAmB,EAAE;UAC7BsC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACzD,SAAS,CAAC;UAC3BwD,OAAO,CAACC,GAAG,CAAC,IAAI,CAACzD,SAAS,CAAC,QAAQ,CAAC,CAAC+I,sBAAsB,CAAC;UAC5D,IAAI,CAAClK,YAAY,CAACmK,IAAI,CAAC;YACrB1K,OAAO,EAAE,IAAI,CAAC0B,SAAS,CAAC,QAAQ,CAAC,CAAC+I,sBAAsB;YACxD1K,KAAK,EAAE;WACR,CAAC;QACJ;MACF,CAAC,EAAEyK,KAAK,CAAC;IACX;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEOG,eAAeA,CAAClI,WAAmB;MACxC,IAAI,CAACzB,eAAe,CACjB4J,kBAAkB,CAACnI,WAAW,CAAC,CAC/B6B,IAAI,CAAChH,SAAS,CAAC,IAAI,CAACwF,QAAQ,CAAC,CAAC,CAC9ByB,SAAS,CAAC;QACTM,IAAI,EAAG6D,OAAO,IAAI;UAChBxD,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEuD,OAAO,CAAC;UACjC,MAAM;YAAE1I,OAAO;YAAED;UAAK,CAAE,GAAG2I,OAAO;UAClC,IAAI3I,KAAK,EAAE;YACT,IAAI,CAACQ,YAAY,CAACmK,IAAI,CAAC;cAAE1K,OAAO;cAAED;YAAK,CAAE,CAAC;UAC5C,CAAC,MAAM,IAAI,IAAI,CAAC2C,kBAAkB,KAAK,KAAK,EAAE;YAC5C;UAAA;QAEJ,CAAC;QACDkD,KAAK,EAAGC,GAAG,IAAI;UACb,IAAI,CAACtF,YAAY,CAACmK,IAAI,CAAC;YACrB1K,OAAO,EAAE,IAAI,CAAC0B,SAAS,CAAC,aAAa,CAAC;YACtC3B,KAAK,EAAE;WACR,CAAC;UACFmF,OAAO,CAACU,KAAK,CAAC,kBAAkB,EAAEC,GAAG,CAAC;QACxC,CAAC;QACDf,QAAQ,EAAEA,CAAA,KAAK;UACb,IAAI,CAACyF,kBAAkB,EAAE;UACzBrF,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;QAC5C;OACD,CAAC;IACN;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEO0F,YAAYA,CAACC,MAAc;MAChC,IAAI,CAAC1H,WAAW,GAAG,KAAK;MACxB,IAAI;QACF,MAAM2H,YAAY,GAAG5B,IAAI,CAAC6B,KAAK,CAACF,MAAM,CAAC;QACvC,IAAI,CAAC1H,WAAW,GAAG,IAAI;QACvB,OAAO2H,YAAY;MACrB,CAAC,CAAC,OAAO5E,CAAC,EAAE;QACV,OAAO,IAAI;MACb;IACF;IAEOxH,eAAeA,CAAA;MACpB,IAAIsM,OAAO,GAAmC,IAAIC,QAAQ,EAAE;MAC5D,IAAIC,WAAW,GAAG,EAAE;MAGpB,IAAI,CAACpJ,MAAM,GAAG/D,eAAe,CAACoN,OAAO;MACrC,IAAI,IAAI,CAAC9H,aAAa,CAAC9C,MAAM,EAAE;QAC7B,IAAI,CAAC8C,aAAa,CAACgD,OAAO,CAAE+E,IAAI,IAAI;UAClCJ,OAAO,CAACK,MAAM,CAAC,OAAO,EAAED,IAAI,CAAC;QAC/B,CAAC,CAAC;QACFJ,OAAO,CAACK,MAAM,CAAC,YAAY,EAAE,IAAI,CAAC9J,UAAU,CAAC;QAC7CyJ,OAAO,CAACK,MAAM,CAAC,YAAY,EAAEnC,IAAI,CAACC,SAAS,CAAC,IAAI,CAAC/G,YAAY,CAACkJ,KAAK,CAAC,CAAC;QACrEN,OAAO,CAACK,MAAM,CAAC,MAAM,EAAE,IAAI,CAACpK,YAAY,CAACsK,aAAa,EAAE,CAAC;QACzDP,OAAO,CAACK,MAAM,CAAC,aAAa,EAAE,IAAI,CAAC7I,WAAW,CAAC;QAC/C0I,WAAW,GAAG,QAAQ;MACxB,CAAC,MAAM;QACLF,OAAO,GAAG;UACRQ,UAAU,EAAE,IAAI,CAACjK,UAAU;UAC3BkK,UAAU,EAAE,IAAI,CAACrJ,YAAY,CAACkJ,KAAK;UACnC9I,WAAW,EAAE,IAAI,CAACA,WAAW;UAC7BkJ,IAAI,EAAE,IAAI,CAACzK,YAAY,CAACsK,aAAa;SACtC;MACH;MAEA,IAAI,CAACb,eAAe,CAAC,IAAI,CAAClI,WAAW,CAAC;MACtC,IAAI,CAAC2F,iBAAiB,EAAE;MAExB,IAAI,CAACpH,eAAe,CACjBrC,eAAe,CAACsM,OAAO,EAAEE,WAAW,CAAC,CACrC7G,IAAI,CAAChH,SAAS,CAAC,IAAI,CAACwF,QAAQ,CAAC,CAAC,CAC9ByB,SAAS,CAAC;QACTM,IAAI,EAAGW,GAAG,IAAI;UACZ,IAAI,CAACtD,gBAAgB,GAAG,KAAK;UAC7B,IAAI,CAAClD,SAAS,GAAG,KAAK;UACtB,IAAI,CAAC2C,iBAAiB,CAACmE,aAAa,CAACN,GAAG,EAAEkD,OAAO,IAAI,4CAA4C,CAAC;UAElG,IAAIlD,GAAG,EAAEoG,gBAAgB,EAAEC,QAAQ,EAAEf,MAAM,EAAE;YAC3C,IAAI,CAAClI,mBAAmB,GAAG,IAAI;YAC/B;YACA,IAAI,CAACrC,YAAY,CAACmK,IAAI,CAAC;cACrB1K,OAAO,EAAE,IAAI,CAAC0B,SAAS,CAAC,QAAQ,CAAC,CAACoK,oBAAoB;cACtD/L,KAAK,EAAE;aACR,CAAC;YACF,IAAI,CAACmD,QAAQ,GAAG,KAAK;YACrB,IAAI,CAACC,UAAU,GAAGqC,GAAG,EAAEoG,gBAAgB,EAAEC,QAAQ,EAAEf,MAAM;YACzD,IAAI,CAAC1I,YAAY,GAAGoD,GAAG,EAAEoG,gBAAgB,EAAEC,QAAQ,EAAEE,YAAY,CAACxE,GAAG,CAAEZ,IAAS,IAAI;cAClF,OAAO;gBACLrF,EAAE,EAAEqF,IAAI,EAAErF,EAAE,IAAI,EAAE;gBAClB0K,KAAK,EAAErF,IAAI,EAAEqF,KAAK,IAAI,EAAE;gBACxBhM,OAAO,EAAE2G,IAAI,EAAE3G,OAAO,IAAI,EAAE;gBAC5BwG,SAAS,EAAEG,IAAI,EAAEH,SAAS,IAAI,EAAE;gBAChCyC,SAAS,EAAEtC,IAAI,EAAEsC,SAAS,IAAI,EAAE;gBAChCK,IAAI,EAAE3C,IAAI,EAAE2C,IAAI,IAAI,EAAE;gBACtB1C,WAAW,EAAED,IAAI,EAAEC,WAAW,IAAI,EAAE;gBACpCqF,eAAe,EAAEtF,IAAI,EAAEsF,eAAe,IAAI,EAAE;gBAC5CC,OAAO,EAAEvF,IAAI,EAAEuF,OAAO,IAAI,EAAE;gBAC5BC,GAAG,EAAExF,IAAI,EAAEwF,GAAG,IAAI;eACnB;YACH,CAAC,CAAC;YAEF,IAAI,CAACvL,WAAW,GAAG4E,GAAG,EAAEoG,gBAAgB,EAAEC,QAAQ,EAAEE,YAAY,CAACxE,GAAG,CACjEZ,IAKA,IAAI;cACH,OAAO;gBACLC,WAAW,EAAED,IAAI,CAACC,WAAW;gBAC7BsF,OAAO,EAAEvF,IAAI,CAACuF,OAAO;gBACrBC,GAAG,EAAExF,IAAI,CAACwF,GAAG;gBACbF,eAAe,EAAEtF,IAAI,CAACsF;eACvB;YACH,CAAC,CACF;YAED;YACA;YACA;YAEA;YACA;YACA;YACA;YACA;YACA;UACF;UACA,IAAI,CAACpB,YAAY,CAAC,IAAI,CAAC1H,UAAU,CAAC;UAClC,IAAI,CAACpB,MAAM,GAAG/D,eAAe,CAACoO,SAAS;UACvC,IAAI,CAAC9D,gBAAgB,EAAE;UACvB,IAAI,CAAChF,aAAa,GAAG,EAAE;QACzB,CAAC;QACDsC,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAAChD,mBAAmB,GAAG,IAAI;UAC/B,IAAI,CAACV,gBAAgB,GAAG,KAAK;UAC7B,IAAI,CAACgB,QAAQ,GAAG,IAAI;UACpB,IAAI,CAACC,UAAU,GAAGyC,KAAK,EAAEA,KAAK,EAAEyG,MAAM;UACtC,IAAI,CAACrL,eAAe,CAACsL,qBAAqB,EAAE;UAC5C,IAAI,CAAC/L,YAAY,CAACmK,IAAI,CAAC;YACrB1K,OAAO,EAAE,IAAI,CAAC0B,SAAS,CAAC,QAAQ,CAAC,CAAC6K,iBAAiB;YACnDxM,KAAK,EAAE;WACR,CAAC;UACF,IAAI,CAAC4B,iBAAiB,CAACmE,aAAa,CAClC,sDAAsD,CACvD;UACD,IAAI,CAACxC,aAAa,GAAG,EAAE;UACvB,IAAI,CAACgF,gBAAgB,EAAE;UACvBpD,OAAO,CAACC,GAAG,CAAC,UAAU,EAAES,KAAK,CAAC8C,OAAO,CAAC;QACxC;OACD,CAAC;IACN;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA8D,gBAAgBA,CAAA;MACdtH,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;IACjC;IAEAsH,qBAAqBA,CAACC,KAAa;MACjC,IAAG,IAAI,CAAC9I,iBAAiB,KAAG,IAAI,CAACD,eAAe,CAACnD,MAAM,IAAI,IAAI,CAACmD,eAAe,CAACnD,MAAM,KAAG,CAAC,EAAC;QACzF,IAAI,CAAC8C,aAAa,GAAGoJ,KAAK;QAC1B;MACF;MAEA,MAAM/D,KAAK,GAAG,IAAI,CAAChF,eAAe,CAAC,IAAI,CAACC,iBAAiB,CAAC;MAC1D,IAAG,IAAI,CAACgE,YAAY,CAACe,KAAK,CAAC,EAAC;QAC1B,IAAI+D,KAAK,IAAIA,KAAK,CAAClM,MAAM,GAAG,CAAC,EAAE;UAC7B,IAAI,CAACmM,eAAe,CAACD,KAAK,CAAC,CAAC,CAAC,CAAC;QAChC;MACF,CAAC,MAAM;QACL,IAAI,CAACpJ,aAAa,GAAGoJ,KAAK;MAC5B;IACF;IAEA/G,oBAAoBA,CAAA;MAClB,IAAI,CAAChC,eAAe,GAAG0D,MAAM,CAACuF,IAAI,CAAC,IAAI,CAACvK,YAAY,CAACwK,QAAQ,CAAC;MAC9D,IAAI,CAACjJ,iBAAiB,GAAG,CAAC;MAC1B,IAAI,IAAI,CAACD,eAAe,CAACnD,MAAM,GAAG,CAAC,EAAE;QACnC,IAAI,CAACqI,qBAAqB,EAAE;MAC9B,CAAC,MACG;QACF,IAAI,CAACxF,WAAW,GAAG,IAAI;QACvB,IAAI,CAAC1B,iBAAiB,CAACmE,aAAa,CAAC,uFAAuF,CAAC;MAC/H;IACF;IAEA+C,qBAAqBA,CAAA;MACnB,MAAMF,KAAK,GAAG,IAAI,CAAChF,eAAe,CAAC,IAAI,CAACC,iBAAiB,CAAC;MAC1D,IAAI,IAAI,CAACgE,YAAY,CAACe,KAAK,CAAC,EAAE;QAC5B,IAAI,CAACrG,QAAQ,GAAG,sBAAsB;QACtC,IAAI,CAACX,iBAAiB,CAACmE,aAAa,CAAC,8BAA8B6C,KAAK,EAAE,CAAC;QAC3E;MACF,CAAC,MAAM;QACL,IAAI,CAACrG,QAAQ,GAAG,MAAM,CAAC,CAAC;QACxB,IAAI,CAACX,iBAAiB,CAACmE,aAAa,CAAC,6BAA6B6C,KAAK,EAAE,CAAC;MAC5E;IACF;IAEAgE,eAAeA,CAACtB,IAAU;MACxB,MAAM1C,KAAK,GAAG,IAAI,CAAChF,eAAe,CAAC,IAAI,CAACC,iBAAiB,CAAC;MAC1D,IAAI,CAAC,IAAI,CAACgE,YAAY,CAACe,KAAK,CAAC,EAAE;MAE/B,MAAMmE,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC/BD,MAAM,CAACE,MAAM,GAAG,MAAK;QACnB,MAAMC,YAAY,GAAIH,MAAM,CAACI,MAAiB;QAC9C,IAAI,CAAC7K,YAAY,CAACoC,GAAG,CAACkE,KAAK,CAAC,EAAEC,QAAQ,CAACqE,YAAY,CAAC;QACpD,IAAI,CAACrJ,iBAAiB,EAAE;QACxB,IAAI,IAAI,CAACA,iBAAiB,GAAG,IAAI,CAACD,eAAe,CAACnD,MAAM,EAAE;UACxD,IAAI,CAACqI,qBAAqB,EAAE;QAC9B,CAAC,MAAM;UACL,IAAI,CAAClH,iBAAiB,CAACmE,aAAa,CAAC,oCAAoC,CAAC;UAC1E,IAAI,CAACnH,eAAe,EAAE;QACxB;MACF,CAAC;MACDmO,MAAM,CAACK,aAAa,CAAC9B,IAAI,CAAC;IAC5B;IAEA;IACA+B,uBAAuBA,CAAA;MACrB,IAAI,CAAC/C,YAAY,EAAE;IACrB;IAEAgD,2BAA2BA,CAACC,WAAoB;MAC9C,IAAI,CAACxJ,qBAAqB,GAAGwJ,WAAW;IAC1C;IAEAC,mBAAmBA,CAACtI,KAA2D;MAC7E;MACA,MAAMwB,KAAK,GAAG,IAAI,CAACnG,MAAM,CAACkN,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACnM,EAAE,KAAK2D,KAAK,CAACyI,OAAO,CAAC;MAC3D,IAAIjH,KAAK,IAAIA,KAAK,CAACU,MAAM,IAAIV,KAAK,CAACU,MAAM,CAAClC,KAAK,CAAC0I,UAAU,CAAC,EAAE;QAC3DlH,KAAK,CAACU,MAAM,CAAClC,KAAK,CAAC0I,UAAU,CAAC,CAACpC,KAAK,GAAGtG,KAAK,CAACsG,KAAK;QAElD;QACA,MAAMvE,WAAW,GAAGP,KAAK,CAACU,MAAM,CAAClC,KAAK,CAAC0I,UAAU,CAAC,CAAC3G,WAAW;QAC9D,IAAI,IAAI,CAAC3E,YAAY,CAACoC,GAAG,CAACuC,WAAW,CAAC,EAAE;UACtC,IAAI,CAAC3E,YAAY,CAACoC,GAAG,CAACuC,WAAW,CAAC,EAAE4B,QAAQ,CAAC3D,KAAK,CAACsG,KAAK,CAAC;QAC3D;MACF;IACF;IAEAqC,mBAAmBA,CAAC3I,KAA2D;MAC7E;MACA,MAAMwB,KAAK,GAAG,IAAI,CAACnG,MAAM,CAACkN,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACnM,EAAE,KAAK2D,KAAK,CAACyI,OAAO,CAAC;MAC3D,IAAIjH,KAAK,IAAIA,KAAK,CAACU,MAAM,IAAIV,KAAK,CAACU,MAAM,CAAClC,KAAK,CAAC0I,UAAU,CAAC,EAAE;QAC3DlH,KAAK,CAACU,MAAM,CAAClC,KAAK,CAAC0I,UAAU,CAAC,CAACjB,KAAK,GAAGzH,KAAK,CAACyH,KAAK;QAElD;QACA,IAAI,CAACpJ,aAAa,GAAG,CAAC,GAAG,IAAI,CAACA,aAAa,EAAE,GAAG2B,KAAK,CAACyH,KAAK,CAAC;MAC9D;IACF;;uCAtoBW7L,0BAA0B,EAAA3C,EAAA,CAAA2P,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA7P,EAAA,CAAA2P,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAA9P,EAAA,CAAA2P,iBAAA,CAAAI,EAAA,CAAAC,eAAA,GAAAhQ,EAAA,CAAA2P,iBAAA,CAAAM,EAAA,CAAAC,6BAAA,GAAAlQ,EAAA,CAAA2P,iBAAA,CAAAQ,EAAA,CAAAC,mBAAA,GAAApQ,EAAA,CAAA2P,iBAAA,CAAAQ,EAAA,CAAAE,aAAA,GAAArQ,EAAA,CAAA2P,iBAAA,CAAAW,EAAA,CAAAC,WAAA;IAAA;;YAA1B5N,0BAA0B;MAAA6N,SAAA;MAAAC,SAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;yBAY1BpR,sBAAsB;;;;;;;;;;;;UC9DnCS,EAAA,CAAAC,cAAA,aAA0C;;UAIpCD,EAFJ,CAAAC,cAAA,aAAqD,WAC7C,wBAC4D;UAE9DD,EADA,CAAAkB,SAAA,cAAyC,cACE;UAGjDlB,EAFI,CAAAU,YAAA,EAAiB,EACZ,EACH;;UAIFV,EAHJ,CAAAC,cAAA,aAA2C,aAE+B,iCAWnC;UAHjCD,EAHA,CAAAE,UAAA,yBAAA2Q,mFAAA;YAAA,OAAeD,GAAA,CAAA1B,uBAAA,EAAyB;UAAA,EAAC,6BAAA4B,uFAAAC,MAAA;YAAA,OACtBH,GAAA,CAAAzB,2BAAA,CAAA4B,MAAA,CAAmC;UAAA,EAAC,+BAAAC,yFAAAD,MAAA;YAAA,OAClCH,GAAA,CAAAvB,mBAAA,CAAA0B,MAAA,CAA2B;UAAA,EAAC,+BAAAE,yFAAAF,MAAA;YAAA,OAC5BH,GAAA,CAAAlB,mBAAA,CAAAqB,MAAA,CAA2B;UAAA,EAAC;UAInD/Q,EAAA,CAAAU,YAAA,EAA0B;UAG1BV,EAAA,CAAAmB,UAAA,IAAA+P,yCAAA,iBAAqE;UAiBvElR,EAAA,CAAAU,YAAA,EAAM;UAOAV,EAJN,CAAAC,cAAA,cAAqE,eAE1C,eACK,oBASzB;UAFCD,EAAA,CAAAE,UAAA,uBAAAiR,mEAAAJ,MAAA;YAAA,OAAaH,GAAA,CAAA9J,WAAA,CAAAiK,MAAA,CAAmB;UAAA,EAAC;UAGrC/Q,EADG,CAAAU,YAAA,EAAW,EACR;UACNV,EAAA,CAAAC,cAAA,eAA4B;UAC1BD,EAAA,CAAAkB,SAAA,sBAWa;UAEjBlB,EADE,CAAAU,YAAA,EAAM,EACF;UAGNV,EAAA,CAAAC,cAAA,eAA8B;UAmE5BD,EAjEA,CAAAmB,UAAA,KAAAiQ,0CAAA,mBAAkF,KAAAC,0CAAA,kBAyDC,KAAAC,0CAAA,kBAQO;UASlGtR,EAHM,CAAAU,YAAA,EAAM,EACF,EACF,EACF;;;UA5IEV,EAAA,CAAAW,SAAA,GAAiB;UAEjBX,EAFA,CAAAY,UAAA,WAAAgQ,GAAA,CAAAxO,MAAA,CAAiB,gBAAAwO,GAAA,CAAAhL,qBAAA,CACoB,iBAAAgL,GAAA,CAAArN,YAAA,CACR;UAWQvD,EAAA,CAAAW,SAAA,EAA4B;UAA5BX,EAAA,CAAAY,UAAA,UAAAgQ,GAAA,CAAAhL,qBAAA,CAA4B;UAyB7D5F,EAAA,CAAAW,SAAA,GAAuB;UAIvBX,EAJA,CAAAY,UAAA,SAAAgQ,GAAA,CAAAzN,cAAA,CAAuB,gBAAAyN,GAAA,CAAAjL,WAAA,CACI,4BAGA;UAS3B3F,EAAA,CAAAW,SAAA,GAKE;UALFX,EAAA,CAAAY,UAAA,iBAAAZ,EAAA,CAAAe,eAAA,KAAAwQ,GAAA,EAKE;UAUAvR,EAAA,CAAAW,SAAA,GAAgC;UAAhCX,EAAA,CAAAY,UAAA,SAAAgQ,GAAA,CAAAjL,WAAA,gBAAgC;UAyDhC3F,EAAA,CAAAW,SAAA,EAAoC;UAApCX,EAAA,CAAAY,UAAA,SAAAgQ,GAAA,CAAAjL,WAAA,oBAAoC;UAQpC3F,EAAA,CAAAW,SAAA,EAAoC;UAApCX,EAAA,CAAAY,UAAA,SAAAgQ,GAAA,CAAAjL,WAAA,oBAAoC;;;qBD5G9CzG,YAAY,EAAAsS,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZrS,WAAW,EAGXG,oBAAoB,EACpBG,aAAa,EACbF,eAAe,EACfC,aAAa,EACbK,2BAA2B;MAAA4R,MAAA;IAAA;;SAKlBhP,0BAA0B;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}