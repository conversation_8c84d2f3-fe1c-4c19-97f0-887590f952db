{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { ListAstNode } from './ast.js';\n/**\n * Concatenates a list of (2,3) AstNode's into a single (2,3) AstNode.\n * This mutates the items of the input array!\n * If all items have the same height, this method has runtime O(items.length).\n * Otherwise, it has runtime O(items.length * max(log(items.length), items.max(i => i.height))).\n*/\nexport function concat23Trees(items) {\n  if (items.length === 0) {\n    return null;\n  }\n  if (items.length === 1) {\n    return items[0];\n  }\n  let i = 0;\n  /**\n   * Reads nodes of same height and concatenates them to a single node.\n  */\n  function readNode() {\n    if (i >= items.length) {\n      return null;\n    }\n    const start = i;\n    const height = items[start].listHeight;\n    i++;\n    while (i < items.length && items[i].listHeight === height) {\n      i++;\n    }\n    if (i - start >= 2) {\n      return concat23TreesOfSameHeight(start === 0 && i === items.length ? items : items.slice(start, i), false);\n    } else {\n      return items[start];\n    }\n  }\n  // The items might not have the same height.\n  // We merge all items by using a binary concat operator.\n  let first = readNode(); // There must be a first item\n  let second = readNode();\n  if (!second) {\n    return first;\n  }\n  for (let item = readNode(); item; item = readNode()) {\n    // Prefer concatenating smaller trees, as the runtime of concat depends on the tree height.\n    if (heightDiff(first, second) <= heightDiff(second, item)) {\n      first = concat(first, second);\n      second = item;\n    } else {\n      second = concat(second, item);\n    }\n  }\n  const result = concat(first, second);\n  return result;\n}\nexport function concat23TreesOfSameHeight(items, createImmutableLists = false) {\n  if (items.length === 0) {\n    return null;\n  }\n  if (items.length === 1) {\n    return items[0];\n  }\n  let length = items.length;\n  // All trees have same height, just create parent nodes.\n  while (length > 3) {\n    const newLength = length >> 1;\n    for (let i = 0; i < newLength; i++) {\n      const j = i << 1;\n      items[i] = ListAstNode.create23(items[j], items[j + 1], j + 3 === length ? items[j + 2] : null, createImmutableLists);\n    }\n    length = newLength;\n  }\n  return ListAstNode.create23(items[0], items[1], length >= 3 ? items[2] : null, createImmutableLists);\n}\nfunction heightDiff(node1, node2) {\n  return Math.abs(node1.listHeight - node2.listHeight);\n}\nfunction concat(node1, node2) {\n  if (node1.listHeight === node2.listHeight) {\n    return ListAstNode.create23(node1, node2, null, false);\n  } else if (node1.listHeight > node2.listHeight) {\n    // node1 is the tree we want to insert into\n    return append(node1, node2);\n  } else {\n    return prepend(node2, node1);\n  }\n}\n/**\n * Appends the given node to the end of this (2,3) tree.\n * Returns the new root.\n*/\nfunction append(list, nodeToAppend) {\n  list = list.toMutable();\n  let curNode = list;\n  const parents = [];\n  let nodeToAppendOfCorrectHeight;\n  while (true) {\n    // assert nodeToInsert.listHeight <= curNode.listHeight\n    if (nodeToAppend.listHeight === curNode.listHeight) {\n      nodeToAppendOfCorrectHeight = nodeToAppend;\n      break;\n    }\n    // assert 0 <= nodeToInsert.listHeight < curNode.listHeight\n    if (curNode.kind !== 4 /* AstNodeKind.List */) {\n      throw new Error('unexpected');\n    }\n    parents.push(curNode);\n    // assert 2 <= curNode.childrenLength <= 3\n    curNode = curNode.makeLastElementMutable();\n  }\n  // assert nodeToAppendOfCorrectHeight!.listHeight === curNode.listHeight\n  for (let i = parents.length - 1; i >= 0; i--) {\n    const parent = parents[i];\n    if (nodeToAppendOfCorrectHeight) {\n      // Can we take the element?\n      if (parent.childrenLength >= 3) {\n        // assert parent.childrenLength === 3 && parent.listHeight === nodeToAppendOfCorrectHeight.listHeight + 1\n        // we need to split to maintain (2,3)-tree property.\n        // Send the third element + the new element to the parent.\n        nodeToAppendOfCorrectHeight = ListAstNode.create23(parent.unappendChild(), nodeToAppendOfCorrectHeight, null, false);\n      } else {\n        parent.appendChildOfSameHeight(nodeToAppendOfCorrectHeight);\n        nodeToAppendOfCorrectHeight = undefined;\n      }\n    } else {\n      parent.handleChildrenChanged();\n    }\n  }\n  if (nodeToAppendOfCorrectHeight) {\n    return ListAstNode.create23(list, nodeToAppendOfCorrectHeight, null, false);\n  } else {\n    return list;\n  }\n}\n/**\n * Prepends the given node to the end of this (2,3) tree.\n * Returns the new root.\n*/\nfunction prepend(list, nodeToAppend) {\n  list = list.toMutable();\n  let curNode = list;\n  const parents = [];\n  // assert nodeToInsert.listHeight <= curNode.listHeight\n  while (nodeToAppend.listHeight !== curNode.listHeight) {\n    // assert 0 <= nodeToInsert.listHeight < curNode.listHeight\n    if (curNode.kind !== 4 /* AstNodeKind.List */) {\n      throw new Error('unexpected');\n    }\n    parents.push(curNode);\n    // assert 2 <= curNode.childrenFast.length <= 3\n    curNode = curNode.makeFirstElementMutable();\n  }\n  let nodeToPrependOfCorrectHeight = nodeToAppend;\n  // assert nodeToAppendOfCorrectHeight!.listHeight === curNode.listHeight\n  for (let i = parents.length - 1; i >= 0; i--) {\n    const parent = parents[i];\n    if (nodeToPrependOfCorrectHeight) {\n      // Can we take the element?\n      if (parent.childrenLength >= 3) {\n        // assert parent.childrenLength === 3 && parent.listHeight === nodeToAppendOfCorrectHeight.listHeight + 1\n        // we need to split to maintain (2,3)-tree property.\n        // Send the third element + the new element to the parent.\n        nodeToPrependOfCorrectHeight = ListAstNode.create23(nodeToPrependOfCorrectHeight, parent.unprependChild(), null, false);\n      } else {\n        parent.prependChildOfSameHeight(nodeToPrependOfCorrectHeight);\n        nodeToPrependOfCorrectHeight = undefined;\n      }\n    } else {\n      parent.handleChildrenChanged();\n    }\n  }\n  if (nodeToPrependOfCorrectHeight) {\n    return ListAstNode.create23(nodeToPrependOfCorrectHeight, list, null, false);\n  } else {\n    return list;\n  }\n}", "map": {"version": 3, "names": ["ListAstNode", "concat23Trees", "items", "length", "i", "readNode", "start", "height", "listHeight", "concat23TreesOfSameHeight", "slice", "first", "second", "item", "heightDiff", "concat", "result", "createImmutableLists", "<PERSON><PERSON><PERSON><PERSON>", "j", "create23", "node1", "node2", "Math", "abs", "append", "prepend", "list", "nodeToAppend", "toMutable", "curNode", "parents", "nodeToAppendOfCorrectHeight", "kind", "Error", "push", "makeLastElementMutable", "parent", "<PERSON><PERSON><PERSON><PERSON>", "unappendChild", "appendChildOfSameHeight", "undefined", "handleChildrenChanged", "makeFirstElementMutable", "nodeToPrependOfCorrectHeight", "unprependChild", "prependChildOfSameHeight"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/concat23Trees.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { ListAstNode } from './ast.js';\n/**\n * Concatenates a list of (2,3) AstNode's into a single (2,3) AstNode.\n * This mutates the items of the input array!\n * If all items have the same height, this method has runtime O(items.length).\n * Otherwise, it has runtime O(items.length * max(log(items.length), items.max(i => i.height))).\n*/\nexport function concat23Trees(items) {\n    if (items.length === 0) {\n        return null;\n    }\n    if (items.length === 1) {\n        return items[0];\n    }\n    let i = 0;\n    /**\n     * Reads nodes of same height and concatenates them to a single node.\n    */\n    function readNode() {\n        if (i >= items.length) {\n            return null;\n        }\n        const start = i;\n        const height = items[start].listHeight;\n        i++;\n        while (i < items.length && items[i].listHeight === height) {\n            i++;\n        }\n        if (i - start >= 2) {\n            return concat23TreesOfSameHeight(start === 0 && i === items.length ? items : items.slice(start, i), false);\n        }\n        else {\n            return items[start];\n        }\n    }\n    // The items might not have the same height.\n    // We merge all items by using a binary concat operator.\n    let first = readNode(); // There must be a first item\n    let second = readNode();\n    if (!second) {\n        return first;\n    }\n    for (let item = readNode(); item; item = readNode()) {\n        // Prefer concatenating smaller trees, as the runtime of concat depends on the tree height.\n        if (heightDiff(first, second) <= heightDiff(second, item)) {\n            first = concat(first, second);\n            second = item;\n        }\n        else {\n            second = concat(second, item);\n        }\n    }\n    const result = concat(first, second);\n    return result;\n}\nexport function concat23TreesOfSameHeight(items, createImmutableLists = false) {\n    if (items.length === 0) {\n        return null;\n    }\n    if (items.length === 1) {\n        return items[0];\n    }\n    let length = items.length;\n    // All trees have same height, just create parent nodes.\n    while (length > 3) {\n        const newLength = length >> 1;\n        for (let i = 0; i < newLength; i++) {\n            const j = i << 1;\n            items[i] = ListAstNode.create23(items[j], items[j + 1], j + 3 === length ? items[j + 2] : null, createImmutableLists);\n        }\n        length = newLength;\n    }\n    return ListAstNode.create23(items[0], items[1], length >= 3 ? items[2] : null, createImmutableLists);\n}\nfunction heightDiff(node1, node2) {\n    return Math.abs(node1.listHeight - node2.listHeight);\n}\nfunction concat(node1, node2) {\n    if (node1.listHeight === node2.listHeight) {\n        return ListAstNode.create23(node1, node2, null, false);\n    }\n    else if (node1.listHeight > node2.listHeight) {\n        // node1 is the tree we want to insert into\n        return append(node1, node2);\n    }\n    else {\n        return prepend(node2, node1);\n    }\n}\n/**\n * Appends the given node to the end of this (2,3) tree.\n * Returns the new root.\n*/\nfunction append(list, nodeToAppend) {\n    list = list.toMutable();\n    let curNode = list;\n    const parents = [];\n    let nodeToAppendOfCorrectHeight;\n    while (true) {\n        // assert nodeToInsert.listHeight <= curNode.listHeight\n        if (nodeToAppend.listHeight === curNode.listHeight) {\n            nodeToAppendOfCorrectHeight = nodeToAppend;\n            break;\n        }\n        // assert 0 <= nodeToInsert.listHeight < curNode.listHeight\n        if (curNode.kind !== 4 /* AstNodeKind.List */) {\n            throw new Error('unexpected');\n        }\n        parents.push(curNode);\n        // assert 2 <= curNode.childrenLength <= 3\n        curNode = curNode.makeLastElementMutable();\n    }\n    // assert nodeToAppendOfCorrectHeight!.listHeight === curNode.listHeight\n    for (let i = parents.length - 1; i >= 0; i--) {\n        const parent = parents[i];\n        if (nodeToAppendOfCorrectHeight) {\n            // Can we take the element?\n            if (parent.childrenLength >= 3) {\n                // assert parent.childrenLength === 3 && parent.listHeight === nodeToAppendOfCorrectHeight.listHeight + 1\n                // we need to split to maintain (2,3)-tree property.\n                // Send the third element + the new element to the parent.\n                nodeToAppendOfCorrectHeight = ListAstNode.create23(parent.unappendChild(), nodeToAppendOfCorrectHeight, null, false);\n            }\n            else {\n                parent.appendChildOfSameHeight(nodeToAppendOfCorrectHeight);\n                nodeToAppendOfCorrectHeight = undefined;\n            }\n        }\n        else {\n            parent.handleChildrenChanged();\n        }\n    }\n    if (nodeToAppendOfCorrectHeight) {\n        return ListAstNode.create23(list, nodeToAppendOfCorrectHeight, null, false);\n    }\n    else {\n        return list;\n    }\n}\n/**\n * Prepends the given node to the end of this (2,3) tree.\n * Returns the new root.\n*/\nfunction prepend(list, nodeToAppend) {\n    list = list.toMutable();\n    let curNode = list;\n    const parents = [];\n    // assert nodeToInsert.listHeight <= curNode.listHeight\n    while (nodeToAppend.listHeight !== curNode.listHeight) {\n        // assert 0 <= nodeToInsert.listHeight < curNode.listHeight\n        if (curNode.kind !== 4 /* AstNodeKind.List */) {\n            throw new Error('unexpected');\n        }\n        parents.push(curNode);\n        // assert 2 <= curNode.childrenFast.length <= 3\n        curNode = curNode.makeFirstElementMutable();\n    }\n    let nodeToPrependOfCorrectHeight = nodeToAppend;\n    // assert nodeToAppendOfCorrectHeight!.listHeight === curNode.listHeight\n    for (let i = parents.length - 1; i >= 0; i--) {\n        const parent = parents[i];\n        if (nodeToPrependOfCorrectHeight) {\n            // Can we take the element?\n            if (parent.childrenLength >= 3) {\n                // assert parent.childrenLength === 3 && parent.listHeight === nodeToAppendOfCorrectHeight.listHeight + 1\n                // we need to split to maintain (2,3)-tree property.\n                // Send the third element + the new element to the parent.\n                nodeToPrependOfCorrectHeight = ListAstNode.create23(nodeToPrependOfCorrectHeight, parent.unprependChild(), null, false);\n            }\n            else {\n                parent.prependChildOfSameHeight(nodeToPrependOfCorrectHeight);\n                nodeToPrependOfCorrectHeight = undefined;\n            }\n        }\n        else {\n            parent.handleChildrenChanged();\n        }\n    }\n    if (nodeToPrependOfCorrectHeight) {\n        return ListAstNode.create23(nodeToPrependOfCorrectHeight, list, null, false);\n    }\n    else {\n        return list;\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,WAAW,QAAQ,UAAU;AACtC;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,aAAaA,CAACC,KAAK,EAAE;EACjC,IAAIA,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;IACpB,OAAO,IAAI;EACf;EACA,IAAID,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;IACpB,OAAOD,KAAK,CAAC,CAAC,CAAC;EACnB;EACA,IAAIE,CAAC,GAAG,CAAC;EACT;AACJ;AACA;EACI,SAASC,QAAQA,CAAA,EAAG;IAChB,IAAID,CAAC,IAAIF,KAAK,CAACC,MAAM,EAAE;MACnB,OAAO,IAAI;IACf;IACA,MAAMG,KAAK,GAAGF,CAAC;IACf,MAAMG,MAAM,GAAGL,KAAK,CAACI,KAAK,CAAC,CAACE,UAAU;IACtCJ,CAAC,EAAE;IACH,OAAOA,CAAC,GAAGF,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,CAAC,CAAC,CAACI,UAAU,KAAKD,MAAM,EAAE;MACvDH,CAAC,EAAE;IACP;IACA,IAAIA,CAAC,GAAGE,KAAK,IAAI,CAAC,EAAE;MAChB,OAAOG,yBAAyB,CAACH,KAAK,KAAK,CAAC,IAAIF,CAAC,KAAKF,KAAK,CAACC,MAAM,GAAGD,KAAK,GAAGA,KAAK,CAACQ,KAAK,CAACJ,KAAK,EAAEF,CAAC,CAAC,EAAE,KAAK,CAAC;IAC9G,CAAC,MACI;MACD,OAAOF,KAAK,CAACI,KAAK,CAAC;IACvB;EACJ;EACA;EACA;EACA,IAAIK,KAAK,GAAGN,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxB,IAAIO,MAAM,GAAGP,QAAQ,CAAC,CAAC;EACvB,IAAI,CAACO,MAAM,EAAE;IACT,OAAOD,KAAK;EAChB;EACA,KAAK,IAAIE,IAAI,GAAGR,QAAQ,CAAC,CAAC,EAAEQ,IAAI,EAAEA,IAAI,GAAGR,QAAQ,CAAC,CAAC,EAAE;IACjD;IACA,IAAIS,UAAU,CAACH,KAAK,EAAEC,MAAM,CAAC,IAAIE,UAAU,CAACF,MAAM,EAAEC,IAAI,CAAC,EAAE;MACvDF,KAAK,GAAGI,MAAM,CAACJ,KAAK,EAAEC,MAAM,CAAC;MAC7BA,MAAM,GAAGC,IAAI;IACjB,CAAC,MACI;MACDD,MAAM,GAAGG,MAAM,CAACH,MAAM,EAAEC,IAAI,CAAC;IACjC;EACJ;EACA,MAAMG,MAAM,GAAGD,MAAM,CAACJ,KAAK,EAAEC,MAAM,CAAC;EACpC,OAAOI,MAAM;AACjB;AACA,OAAO,SAASP,yBAAyBA,CAACP,KAAK,EAAEe,oBAAoB,GAAG,KAAK,EAAE;EAC3E,IAAIf,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;IACpB,OAAO,IAAI;EACf;EACA,IAAID,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;IACpB,OAAOD,KAAK,CAAC,CAAC,CAAC;EACnB;EACA,IAAIC,MAAM,GAAGD,KAAK,CAACC,MAAM;EACzB;EACA,OAAOA,MAAM,GAAG,CAAC,EAAE;IACf,MAAMe,SAAS,GAAGf,MAAM,IAAI,CAAC;IAC7B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGc,SAAS,EAAEd,CAAC,EAAE,EAAE;MAChC,MAAMe,CAAC,GAAGf,CAAC,IAAI,CAAC;MAChBF,KAAK,CAACE,CAAC,CAAC,GAAGJ,WAAW,CAACoB,QAAQ,CAAClB,KAAK,CAACiB,CAAC,CAAC,EAAEjB,KAAK,CAACiB,CAAC,GAAG,CAAC,CAAC,EAAEA,CAAC,GAAG,CAAC,KAAKhB,MAAM,GAAGD,KAAK,CAACiB,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,EAAEF,oBAAoB,CAAC;IACzH;IACAd,MAAM,GAAGe,SAAS;EACtB;EACA,OAAOlB,WAAW,CAACoB,QAAQ,CAAClB,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,EAAEC,MAAM,IAAI,CAAC,GAAGD,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,EAAEe,oBAAoB,CAAC;AACxG;AACA,SAASH,UAAUA,CAACO,KAAK,EAAEC,KAAK,EAAE;EAC9B,OAAOC,IAAI,CAACC,GAAG,CAACH,KAAK,CAACb,UAAU,GAAGc,KAAK,CAACd,UAAU,CAAC;AACxD;AACA,SAASO,MAAMA,CAACM,KAAK,EAAEC,KAAK,EAAE;EAC1B,IAAID,KAAK,CAACb,UAAU,KAAKc,KAAK,CAACd,UAAU,EAAE;IACvC,OAAOR,WAAW,CAACoB,QAAQ,CAACC,KAAK,EAAEC,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC;EAC1D,CAAC,MACI,IAAID,KAAK,CAACb,UAAU,GAAGc,KAAK,CAACd,UAAU,EAAE;IAC1C;IACA,OAAOiB,MAAM,CAACJ,KAAK,EAAEC,KAAK,CAAC;EAC/B,CAAC,MACI;IACD,OAAOI,OAAO,CAACJ,KAAK,EAAED,KAAK,CAAC;EAChC;AACJ;AACA;AACA;AACA;AACA;AACA,SAASI,MAAMA,CAACE,IAAI,EAAEC,YAAY,EAAE;EAChCD,IAAI,GAAGA,IAAI,CAACE,SAAS,CAAC,CAAC;EACvB,IAAIC,OAAO,GAAGH,IAAI;EAClB,MAAMI,OAAO,GAAG,EAAE;EAClB,IAAIC,2BAA2B;EAC/B,OAAO,IAAI,EAAE;IACT;IACA,IAAIJ,YAAY,CAACpB,UAAU,KAAKsB,OAAO,CAACtB,UAAU,EAAE;MAChDwB,2BAA2B,GAAGJ,YAAY;MAC1C;IACJ;IACA;IACA,IAAIE,OAAO,CAACG,IAAI,KAAK,CAAC,CAAC,wBAAwB;MAC3C,MAAM,IAAIC,KAAK,CAAC,YAAY,CAAC;IACjC;IACAH,OAAO,CAACI,IAAI,CAACL,OAAO,CAAC;IACrB;IACAA,OAAO,GAAGA,OAAO,CAACM,sBAAsB,CAAC,CAAC;EAC9C;EACA;EACA,KAAK,IAAIhC,CAAC,GAAG2B,OAAO,CAAC5B,MAAM,GAAG,CAAC,EAAEC,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IAC1C,MAAMiC,MAAM,GAAGN,OAAO,CAAC3B,CAAC,CAAC;IACzB,IAAI4B,2BAA2B,EAAE;MAC7B;MACA,IAAIK,MAAM,CAACC,cAAc,IAAI,CAAC,EAAE;QAC5B;QACA;QACA;QACAN,2BAA2B,GAAGhC,WAAW,CAACoB,QAAQ,CAACiB,MAAM,CAACE,aAAa,CAAC,CAAC,EAAEP,2BAA2B,EAAE,IAAI,EAAE,KAAK,CAAC;MACxH,CAAC,MACI;QACDK,MAAM,CAACG,uBAAuB,CAACR,2BAA2B,CAAC;QAC3DA,2BAA2B,GAAGS,SAAS;MAC3C;IACJ,CAAC,MACI;MACDJ,MAAM,CAACK,qBAAqB,CAAC,CAAC;IAClC;EACJ;EACA,IAAIV,2BAA2B,EAAE;IAC7B,OAAOhC,WAAW,CAACoB,QAAQ,CAACO,IAAI,EAAEK,2BAA2B,EAAE,IAAI,EAAE,KAAK,CAAC;EAC/E,CAAC,MACI;IACD,OAAOL,IAAI;EACf;AACJ;AACA;AACA;AACA;AACA;AACA,SAASD,OAAOA,CAACC,IAAI,EAAEC,YAAY,EAAE;EACjCD,IAAI,GAAGA,IAAI,CAACE,SAAS,CAAC,CAAC;EACvB,IAAIC,OAAO,GAAGH,IAAI;EAClB,MAAMI,OAAO,GAAG,EAAE;EAClB;EACA,OAAOH,YAAY,CAACpB,UAAU,KAAKsB,OAAO,CAACtB,UAAU,EAAE;IACnD;IACA,IAAIsB,OAAO,CAACG,IAAI,KAAK,CAAC,CAAC,wBAAwB;MAC3C,MAAM,IAAIC,KAAK,CAAC,YAAY,CAAC;IACjC;IACAH,OAAO,CAACI,IAAI,CAACL,OAAO,CAAC;IACrB;IACAA,OAAO,GAAGA,OAAO,CAACa,uBAAuB,CAAC,CAAC;EAC/C;EACA,IAAIC,4BAA4B,GAAGhB,YAAY;EAC/C;EACA,KAAK,IAAIxB,CAAC,GAAG2B,OAAO,CAAC5B,MAAM,GAAG,CAAC,EAAEC,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IAC1C,MAAMiC,MAAM,GAAGN,OAAO,CAAC3B,CAAC,CAAC;IACzB,IAAIwC,4BAA4B,EAAE;MAC9B;MACA,IAAIP,MAAM,CAACC,cAAc,IAAI,CAAC,EAAE;QAC5B;QACA;QACA;QACAM,4BAA4B,GAAG5C,WAAW,CAACoB,QAAQ,CAACwB,4BAA4B,EAAEP,MAAM,CAACQ,cAAc,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC;MAC3H,CAAC,MACI;QACDR,MAAM,CAACS,wBAAwB,CAACF,4BAA4B,CAAC;QAC7DA,4BAA4B,GAAGH,SAAS;MAC5C;IACJ,CAAC,MACI;MACDJ,MAAM,CAACK,qBAAqB,CAAC,CAAC;IAClC;EACJ;EACA,IAAIE,4BAA4B,EAAE;IAC9B,OAAO5C,WAAW,CAACoB,QAAQ,CAACwB,4BAA4B,EAAEjB,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC;EAChF,CAAC,MACI;IACD,OAAOA,IAAI;EACf;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}