{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { DiffChange } from './diffChange.js';\nimport { stringHash } from '../hash.js';\nexport class StringDiffSequence {\n  constructor(source) {\n    this.source = source;\n  }\n  getElements() {\n    const source = this.source;\n    const characters = new Int32Array(source.length);\n    for (let i = 0, len = source.length; i < len; i++) {\n      characters[i] = source.charCodeAt(i);\n    }\n    return characters;\n  }\n}\nexport function stringDiff(original, modified, pretty) {\n  return new LcsDiff(new StringDiffSequence(original), new StringDiffSequence(modified)).ComputeDiff(pretty).changes;\n}\n//\n// The code below has been ported from a C# implementation in VS\n//\nclass Debug {\n  static Assert(condition, message) {\n    if (!condition) {\n      throw new Error(message);\n    }\n  }\n}\nclass MyArray {\n  /**\n   * Copies a range of elements from an Array starting at the specified source index and pastes\n   * them to another Array starting at the specified destination index. The length and the indexes\n   * are specified as 64-bit integers.\n   * sourceArray:\n   *\t\tThe Array that contains the data to copy.\n   * sourceIndex:\n   *\t\tA 64-bit integer that represents the index in the sourceArray at which copying begins.\n   * destinationArray:\n   *\t\tThe Array that receives the data.\n   * destinationIndex:\n   *\t\tA 64-bit integer that represents the index in the destinationArray at which storing begins.\n   * length:\n   *\t\tA 64-bit integer that represents the number of elements to copy.\n   */\n  static Copy(sourceArray, sourceIndex, destinationArray, destinationIndex, length) {\n    for (let i = 0; i < length; i++) {\n      destinationArray[destinationIndex + i] = sourceArray[sourceIndex + i];\n    }\n  }\n  static Copy2(sourceArray, sourceIndex, destinationArray, destinationIndex, length) {\n    for (let i = 0; i < length; i++) {\n      destinationArray[destinationIndex + i] = sourceArray[sourceIndex + i];\n    }\n  }\n}\n/**\n * A utility class which helps to create the set of DiffChanges from\n * a difference operation. This class accepts original DiffElements and\n * modified DiffElements that are involved in a particular change. The\n * MarkNextChange() method can be called to mark the separation between\n * distinct changes. At the end, the Changes property can be called to retrieve\n * the constructed changes.\n */\nclass DiffChangeHelper {\n  /**\n   * Constructs a new DiffChangeHelper for the given DiffSequences.\n   */\n  constructor() {\n    this.m_changes = [];\n    this.m_originalStart = 1073741824 /* Constants.MAX_SAFE_SMALL_INTEGER */;\n    this.m_modifiedStart = 1073741824 /* Constants.MAX_SAFE_SMALL_INTEGER */;\n    this.m_originalCount = 0;\n    this.m_modifiedCount = 0;\n  }\n  /**\n   * Marks the beginning of the next change in the set of differences.\n   */\n  MarkNextChange() {\n    // Only add to the list if there is something to add\n    if (this.m_originalCount > 0 || this.m_modifiedCount > 0) {\n      // Add the new change to our list\n      this.m_changes.push(new DiffChange(this.m_originalStart, this.m_originalCount, this.m_modifiedStart, this.m_modifiedCount));\n    }\n    // Reset for the next change\n    this.m_originalCount = 0;\n    this.m_modifiedCount = 0;\n    this.m_originalStart = 1073741824 /* Constants.MAX_SAFE_SMALL_INTEGER */;\n    this.m_modifiedStart = 1073741824 /* Constants.MAX_SAFE_SMALL_INTEGER */;\n  }\n  /**\n   * Adds the original element at the given position to the elements\n   * affected by the current change. The modified index gives context\n   * to the change position with respect to the original sequence.\n   * @param originalIndex The index of the original element to add.\n   * @param modifiedIndex The index of the modified element that provides corresponding position in the modified sequence.\n   */\n  AddOriginalElement(originalIndex, modifiedIndex) {\n    // The 'true' start index is the smallest of the ones we've seen\n    this.m_originalStart = Math.min(this.m_originalStart, originalIndex);\n    this.m_modifiedStart = Math.min(this.m_modifiedStart, modifiedIndex);\n    this.m_originalCount++;\n  }\n  /**\n   * Adds the modified element at the given position to the elements\n   * affected by the current change. The original index gives context\n   * to the change position with respect to the modified sequence.\n   * @param originalIndex The index of the original element that provides corresponding position in the original sequence.\n   * @param modifiedIndex The index of the modified element to add.\n   */\n  AddModifiedElement(originalIndex, modifiedIndex) {\n    // The 'true' start index is the smallest of the ones we've seen\n    this.m_originalStart = Math.min(this.m_originalStart, originalIndex);\n    this.m_modifiedStart = Math.min(this.m_modifiedStart, modifiedIndex);\n    this.m_modifiedCount++;\n  }\n  /**\n   * Retrieves all of the changes marked by the class.\n   */\n  getChanges() {\n    if (this.m_originalCount > 0 || this.m_modifiedCount > 0) {\n      // Finish up on whatever is left\n      this.MarkNextChange();\n    }\n    return this.m_changes;\n  }\n  /**\n   * Retrieves all of the changes marked by the class in the reverse order\n   */\n  getReverseChanges() {\n    if (this.m_originalCount > 0 || this.m_modifiedCount > 0) {\n      // Finish up on whatever is left\n      this.MarkNextChange();\n    }\n    this.m_changes.reverse();\n    return this.m_changes;\n  }\n}\n/**\n * An implementation of the difference algorithm described in\n * \"An O(ND) Difference Algorithm and its variations\" by Eugene W. Myers\n */\nexport class LcsDiff {\n  /**\n   * Constructs the DiffFinder\n   */\n  constructor(originalSequence, modifiedSequence, continueProcessingPredicate = null) {\n    this.ContinueProcessingPredicate = continueProcessingPredicate;\n    this._originalSequence = originalSequence;\n    this._modifiedSequence = modifiedSequence;\n    const [originalStringElements, originalElementsOrHash, originalHasStrings] = LcsDiff._getElements(originalSequence);\n    const [modifiedStringElements, modifiedElementsOrHash, modifiedHasStrings] = LcsDiff._getElements(modifiedSequence);\n    this._hasStrings = originalHasStrings && modifiedHasStrings;\n    this._originalStringElements = originalStringElements;\n    this._originalElementsOrHash = originalElementsOrHash;\n    this._modifiedStringElements = modifiedStringElements;\n    this._modifiedElementsOrHash = modifiedElementsOrHash;\n    this.m_forwardHistory = [];\n    this.m_reverseHistory = [];\n  }\n  static _isStringArray(arr) {\n    return arr.length > 0 && typeof arr[0] === 'string';\n  }\n  static _getElements(sequence) {\n    const elements = sequence.getElements();\n    if (LcsDiff._isStringArray(elements)) {\n      const hashes = new Int32Array(elements.length);\n      for (let i = 0, len = elements.length; i < len; i++) {\n        hashes[i] = stringHash(elements[i], 0);\n      }\n      return [elements, hashes, true];\n    }\n    if (elements instanceof Int32Array) {\n      return [[], elements, false];\n    }\n    return [[], new Int32Array(elements), false];\n  }\n  ElementsAreEqual(originalIndex, newIndex) {\n    if (this._originalElementsOrHash[originalIndex] !== this._modifiedElementsOrHash[newIndex]) {\n      return false;\n    }\n    return this._hasStrings ? this._originalStringElements[originalIndex] === this._modifiedStringElements[newIndex] : true;\n  }\n  ElementsAreStrictEqual(originalIndex, newIndex) {\n    if (!this.ElementsAreEqual(originalIndex, newIndex)) {\n      return false;\n    }\n    const originalElement = LcsDiff._getStrictElement(this._originalSequence, originalIndex);\n    const modifiedElement = LcsDiff._getStrictElement(this._modifiedSequence, newIndex);\n    return originalElement === modifiedElement;\n  }\n  static _getStrictElement(sequence, index) {\n    if (typeof sequence.getStrictElement === 'function') {\n      return sequence.getStrictElement(index);\n    }\n    return null;\n  }\n  OriginalElementsAreEqual(index1, index2) {\n    if (this._originalElementsOrHash[index1] !== this._originalElementsOrHash[index2]) {\n      return false;\n    }\n    return this._hasStrings ? this._originalStringElements[index1] === this._originalStringElements[index2] : true;\n  }\n  ModifiedElementsAreEqual(index1, index2) {\n    if (this._modifiedElementsOrHash[index1] !== this._modifiedElementsOrHash[index2]) {\n      return false;\n    }\n    return this._hasStrings ? this._modifiedStringElements[index1] === this._modifiedStringElements[index2] : true;\n  }\n  ComputeDiff(pretty) {\n    return this._ComputeDiff(0, this._originalElementsOrHash.length - 1, 0, this._modifiedElementsOrHash.length - 1, pretty);\n  }\n  /**\n   * Computes the differences between the original and modified input\n   * sequences on the bounded range.\n   * @returns An array of the differences between the two input sequences.\n   */\n  _ComputeDiff(originalStart, originalEnd, modifiedStart, modifiedEnd, pretty) {\n    const quitEarlyArr = [false];\n    let changes = this.ComputeDiffRecursive(originalStart, originalEnd, modifiedStart, modifiedEnd, quitEarlyArr);\n    if (pretty) {\n      // We have to clean up the computed diff to be more intuitive\n      // but it turns out this cannot be done correctly until the entire set\n      // of diffs have been computed\n      changes = this.PrettifyChanges(changes);\n    }\n    return {\n      quitEarly: quitEarlyArr[0],\n      changes: changes\n    };\n  }\n  /**\n   * Private helper method which computes the differences on the bounded range\n   * recursively.\n   * @returns An array of the differences between the two input sequences.\n   */\n  ComputeDiffRecursive(originalStart, originalEnd, modifiedStart, modifiedEnd, quitEarlyArr) {\n    quitEarlyArr[0] = false;\n    // Find the start of the differences\n    while (originalStart <= originalEnd && modifiedStart <= modifiedEnd && this.ElementsAreEqual(originalStart, modifiedStart)) {\n      originalStart++;\n      modifiedStart++;\n    }\n    // Find the end of the differences\n    while (originalEnd >= originalStart && modifiedEnd >= modifiedStart && this.ElementsAreEqual(originalEnd, modifiedEnd)) {\n      originalEnd--;\n      modifiedEnd--;\n    }\n    // In the special case where we either have all insertions or all deletions or the sequences are identical\n    if (originalStart > originalEnd || modifiedStart > modifiedEnd) {\n      let changes;\n      if (modifiedStart <= modifiedEnd) {\n        Debug.Assert(originalStart === originalEnd + 1, 'originalStart should only be one more than originalEnd');\n        // All insertions\n        changes = [new DiffChange(originalStart, 0, modifiedStart, modifiedEnd - modifiedStart + 1)];\n      } else if (originalStart <= originalEnd) {\n        Debug.Assert(modifiedStart === modifiedEnd + 1, 'modifiedStart should only be one more than modifiedEnd');\n        // All deletions\n        changes = [new DiffChange(originalStart, originalEnd - originalStart + 1, modifiedStart, 0)];\n      } else {\n        Debug.Assert(originalStart === originalEnd + 1, 'originalStart should only be one more than originalEnd');\n        Debug.Assert(modifiedStart === modifiedEnd + 1, 'modifiedStart should only be one more than modifiedEnd');\n        // Identical sequences - No differences\n        changes = [];\n      }\n      return changes;\n    }\n    // This problem can be solved using the Divide-And-Conquer technique.\n    const midOriginalArr = [0];\n    const midModifiedArr = [0];\n    const result = this.ComputeRecursionPoint(originalStart, originalEnd, modifiedStart, modifiedEnd, midOriginalArr, midModifiedArr, quitEarlyArr);\n    const midOriginal = midOriginalArr[0];\n    const midModified = midModifiedArr[0];\n    if (result !== null) {\n      // Result is not-null when there was enough memory to compute the changes while\n      // searching for the recursion point\n      return result;\n    } else if (!quitEarlyArr[0]) {\n      // We can break the problem down recursively by finding the changes in the\n      // First Half:   (originalStart, modifiedStart) to (midOriginal, midModified)\n      // Second Half:  (midOriginal + 1, minModified + 1) to (originalEnd, modifiedEnd)\n      // NOTE: ComputeDiff() is inclusive, therefore the second range starts on the next point\n      const leftChanges = this.ComputeDiffRecursive(originalStart, midOriginal, modifiedStart, midModified, quitEarlyArr);\n      let rightChanges = [];\n      if (!quitEarlyArr[0]) {\n        rightChanges = this.ComputeDiffRecursive(midOriginal + 1, originalEnd, midModified + 1, modifiedEnd, quitEarlyArr);\n      } else {\n        // We didn't have time to finish the first half, so we don't have time to compute this half.\n        // Consider the entire rest of the sequence different.\n        rightChanges = [new DiffChange(midOriginal + 1, originalEnd - (midOriginal + 1) + 1, midModified + 1, modifiedEnd - (midModified + 1) + 1)];\n      }\n      return this.ConcatenateChanges(leftChanges, rightChanges);\n    }\n    // If we hit here, we quit early, and so can't return anything meaningful\n    return [new DiffChange(originalStart, originalEnd - originalStart + 1, modifiedStart, modifiedEnd - modifiedStart + 1)];\n  }\n  WALKTRACE(diagonalForwardBase, diagonalForwardStart, diagonalForwardEnd, diagonalForwardOffset, diagonalReverseBase, diagonalReverseStart, diagonalReverseEnd, diagonalReverseOffset, forwardPoints, reversePoints, originalIndex, originalEnd, midOriginalArr, modifiedIndex, modifiedEnd, midModifiedArr, deltaIsEven, quitEarlyArr) {\n    let forwardChanges = null;\n    let reverseChanges = null;\n    // First, walk backward through the forward diagonals history\n    let changeHelper = new DiffChangeHelper();\n    let diagonalMin = diagonalForwardStart;\n    let diagonalMax = diagonalForwardEnd;\n    let diagonalRelative = midOriginalArr[0] - midModifiedArr[0] - diagonalForwardOffset;\n    let lastOriginalIndex = -1073741824 /* Constants.MIN_SAFE_SMALL_INTEGER */;\n    let historyIndex = this.m_forwardHistory.length - 1;\n    do {\n      // Get the diagonal index from the relative diagonal number\n      const diagonal = diagonalRelative + diagonalForwardBase;\n      // Figure out where we came from\n      if (diagonal === diagonalMin || diagonal < diagonalMax && forwardPoints[diagonal - 1] < forwardPoints[diagonal + 1]) {\n        // Vertical line (the element is an insert)\n        originalIndex = forwardPoints[diagonal + 1];\n        modifiedIndex = originalIndex - diagonalRelative - diagonalForwardOffset;\n        if (originalIndex < lastOriginalIndex) {\n          changeHelper.MarkNextChange();\n        }\n        lastOriginalIndex = originalIndex;\n        changeHelper.AddModifiedElement(originalIndex + 1, modifiedIndex);\n        diagonalRelative = diagonal + 1 - diagonalForwardBase; //Setup for the next iteration\n      } else {\n        // Horizontal line (the element is a deletion)\n        originalIndex = forwardPoints[diagonal - 1] + 1;\n        modifiedIndex = originalIndex - diagonalRelative - diagonalForwardOffset;\n        if (originalIndex < lastOriginalIndex) {\n          changeHelper.MarkNextChange();\n        }\n        lastOriginalIndex = originalIndex - 1;\n        changeHelper.AddOriginalElement(originalIndex, modifiedIndex + 1);\n        diagonalRelative = diagonal - 1 - diagonalForwardBase; //Setup for the next iteration\n      }\n      if (historyIndex >= 0) {\n        forwardPoints = this.m_forwardHistory[historyIndex];\n        diagonalForwardBase = forwardPoints[0]; //We stored this in the first spot\n        diagonalMin = 1;\n        diagonalMax = forwardPoints.length - 1;\n      }\n    } while (--historyIndex >= -1);\n    // Ironically, we get the forward changes as the reverse of the\n    // order we added them since we technically added them backwards\n    forwardChanges = changeHelper.getReverseChanges();\n    if (quitEarlyArr[0]) {\n      // TODO: Calculate a partial from the reverse diagonals.\n      //       For now, just assume everything after the midOriginal/midModified point is a diff\n      let originalStartPoint = midOriginalArr[0] + 1;\n      let modifiedStartPoint = midModifiedArr[0] + 1;\n      if (forwardChanges !== null && forwardChanges.length > 0) {\n        const lastForwardChange = forwardChanges[forwardChanges.length - 1];\n        originalStartPoint = Math.max(originalStartPoint, lastForwardChange.getOriginalEnd());\n        modifiedStartPoint = Math.max(modifiedStartPoint, lastForwardChange.getModifiedEnd());\n      }\n      reverseChanges = [new DiffChange(originalStartPoint, originalEnd - originalStartPoint + 1, modifiedStartPoint, modifiedEnd - modifiedStartPoint + 1)];\n    } else {\n      // Now walk backward through the reverse diagonals history\n      changeHelper = new DiffChangeHelper();\n      diagonalMin = diagonalReverseStart;\n      diagonalMax = diagonalReverseEnd;\n      diagonalRelative = midOriginalArr[0] - midModifiedArr[0] - diagonalReverseOffset;\n      lastOriginalIndex = 1073741824 /* Constants.MAX_SAFE_SMALL_INTEGER */;\n      historyIndex = deltaIsEven ? this.m_reverseHistory.length - 1 : this.m_reverseHistory.length - 2;\n      do {\n        // Get the diagonal index from the relative diagonal number\n        const diagonal = diagonalRelative + diagonalReverseBase;\n        // Figure out where we came from\n        if (diagonal === diagonalMin || diagonal < diagonalMax && reversePoints[diagonal - 1] >= reversePoints[diagonal + 1]) {\n          // Horizontal line (the element is a deletion))\n          originalIndex = reversePoints[diagonal + 1] - 1;\n          modifiedIndex = originalIndex - diagonalRelative - diagonalReverseOffset;\n          if (originalIndex > lastOriginalIndex) {\n            changeHelper.MarkNextChange();\n          }\n          lastOriginalIndex = originalIndex + 1;\n          changeHelper.AddOriginalElement(originalIndex + 1, modifiedIndex + 1);\n          diagonalRelative = diagonal + 1 - diagonalReverseBase; //Setup for the next iteration\n        } else {\n          // Vertical line (the element is an insertion)\n          originalIndex = reversePoints[diagonal - 1];\n          modifiedIndex = originalIndex - diagonalRelative - diagonalReverseOffset;\n          if (originalIndex > lastOriginalIndex) {\n            changeHelper.MarkNextChange();\n          }\n          lastOriginalIndex = originalIndex;\n          changeHelper.AddModifiedElement(originalIndex + 1, modifiedIndex + 1);\n          diagonalRelative = diagonal - 1 - diagonalReverseBase; //Setup for the next iteration\n        }\n        if (historyIndex >= 0) {\n          reversePoints = this.m_reverseHistory[historyIndex];\n          diagonalReverseBase = reversePoints[0]; //We stored this in the first spot\n          diagonalMin = 1;\n          diagonalMax = reversePoints.length - 1;\n        }\n      } while (--historyIndex >= -1);\n      // There are cases where the reverse history will find diffs that\n      // are correct, but not intuitive, so we need shift them.\n      reverseChanges = changeHelper.getChanges();\n    }\n    return this.ConcatenateChanges(forwardChanges, reverseChanges);\n  }\n  /**\n   * Given the range to compute the diff on, this method finds the point:\n   * (midOriginal, midModified)\n   * that exists in the middle of the LCS of the two sequences and\n   * is the point at which the LCS problem may be broken down recursively.\n   * This method will try to keep the LCS trace in memory. If the LCS recursion\n   * point is calculated and the full trace is available in memory, then this method\n   * will return the change list.\n   * @param originalStart The start bound of the original sequence range\n   * @param originalEnd The end bound of the original sequence range\n   * @param modifiedStart The start bound of the modified sequence range\n   * @param modifiedEnd The end bound of the modified sequence range\n   * @param midOriginal The middle point of the original sequence range\n   * @param midModified The middle point of the modified sequence range\n   * @returns The diff changes, if available, otherwise null\n   */\n  ComputeRecursionPoint(originalStart, originalEnd, modifiedStart, modifiedEnd, midOriginalArr, midModifiedArr, quitEarlyArr) {\n    let originalIndex = 0,\n      modifiedIndex = 0;\n    let diagonalForwardStart = 0,\n      diagonalForwardEnd = 0;\n    let diagonalReverseStart = 0,\n      diagonalReverseEnd = 0;\n    // To traverse the edit graph and produce the proper LCS, our actual\n    // start position is just outside the given boundary\n    originalStart--;\n    modifiedStart--;\n    // We set these up to make the compiler happy, but they will\n    // be replaced before we return with the actual recursion point\n    midOriginalArr[0] = 0;\n    midModifiedArr[0] = 0;\n    // Clear out the history\n    this.m_forwardHistory = [];\n    this.m_reverseHistory = [];\n    // Each cell in the two arrays corresponds to a diagonal in the edit graph.\n    // The integer value in the cell represents the originalIndex of the furthest\n    // reaching point found so far that ends in that diagonal.\n    // The modifiedIndex can be computed mathematically from the originalIndex and the diagonal number.\n    const maxDifferences = originalEnd - originalStart + (modifiedEnd - modifiedStart);\n    const numDiagonals = maxDifferences + 1;\n    const forwardPoints = new Int32Array(numDiagonals);\n    const reversePoints = new Int32Array(numDiagonals);\n    // diagonalForwardBase: Index into forwardPoints of the diagonal which passes through (originalStart, modifiedStart)\n    // diagonalReverseBase: Index into reversePoints of the diagonal which passes through (originalEnd, modifiedEnd)\n    const diagonalForwardBase = modifiedEnd - modifiedStart;\n    const diagonalReverseBase = originalEnd - originalStart;\n    // diagonalForwardOffset: Geometric offset which allows modifiedIndex to be computed from originalIndex and the\n    //    diagonal number (relative to diagonalForwardBase)\n    // diagonalReverseOffset: Geometric offset which allows modifiedIndex to be computed from originalIndex and the\n    //    diagonal number (relative to diagonalReverseBase)\n    const diagonalForwardOffset = originalStart - modifiedStart;\n    const diagonalReverseOffset = originalEnd - modifiedEnd;\n    // delta: The difference between the end diagonal and the start diagonal. This is used to relate diagonal numbers\n    //   relative to the start diagonal with diagonal numbers relative to the end diagonal.\n    // The Even/Oddn-ness of this delta is important for determining when we should check for overlap\n    const delta = diagonalReverseBase - diagonalForwardBase;\n    const deltaIsEven = delta % 2 === 0;\n    // Here we set up the start and end points as the furthest points found so far\n    // in both the forward and reverse directions, respectively\n    forwardPoints[diagonalForwardBase] = originalStart;\n    reversePoints[diagonalReverseBase] = originalEnd;\n    // Remember if we quit early, and thus need to do a best-effort result instead of a real result.\n    quitEarlyArr[0] = false;\n    // A couple of points:\n    // --With this method, we iterate on the number of differences between the two sequences.\n    //   The more differences there actually are, the longer this will take.\n    // --Also, as the number of differences increases, we have to search on diagonals further\n    //   away from the reference diagonal (which is diagonalForwardBase for forward, diagonalReverseBase for reverse).\n    // --We extend on even diagonals (relative to the reference diagonal) only when numDifferences\n    //   is even and odd diagonals only when numDifferences is odd.\n    for (let numDifferences = 1; numDifferences <= maxDifferences / 2 + 1; numDifferences++) {\n      let furthestOriginalIndex = 0;\n      let furthestModifiedIndex = 0;\n      // Run the algorithm in the forward direction\n      diagonalForwardStart = this.ClipDiagonalBound(diagonalForwardBase - numDifferences, numDifferences, diagonalForwardBase, numDiagonals);\n      diagonalForwardEnd = this.ClipDiagonalBound(diagonalForwardBase + numDifferences, numDifferences, diagonalForwardBase, numDiagonals);\n      for (let diagonal = diagonalForwardStart; diagonal <= diagonalForwardEnd; diagonal += 2) {\n        // STEP 1: We extend the furthest reaching point in the present diagonal\n        // by looking at the diagonals above and below and picking the one whose point\n        // is further away from the start point (originalStart, modifiedStart)\n        if (diagonal === diagonalForwardStart || diagonal < diagonalForwardEnd && forwardPoints[diagonal - 1] < forwardPoints[diagonal + 1]) {\n          originalIndex = forwardPoints[diagonal + 1];\n        } else {\n          originalIndex = forwardPoints[diagonal - 1] + 1;\n        }\n        modifiedIndex = originalIndex - (diagonal - diagonalForwardBase) - diagonalForwardOffset;\n        // Save the current originalIndex so we can test for false overlap in step 3\n        const tempOriginalIndex = originalIndex;\n        // STEP 2: We can continue to extend the furthest reaching point in the present diagonal\n        // so long as the elements are equal.\n        while (originalIndex < originalEnd && modifiedIndex < modifiedEnd && this.ElementsAreEqual(originalIndex + 1, modifiedIndex + 1)) {\n          originalIndex++;\n          modifiedIndex++;\n        }\n        forwardPoints[diagonal] = originalIndex;\n        if (originalIndex + modifiedIndex > furthestOriginalIndex + furthestModifiedIndex) {\n          furthestOriginalIndex = originalIndex;\n          furthestModifiedIndex = modifiedIndex;\n        }\n        // STEP 3: If delta is odd (overlap first happens on forward when delta is odd)\n        // and diagonal is in the range of reverse diagonals computed for numDifferences-1\n        // (the previous iteration; we haven't computed reverse diagonals for numDifferences yet)\n        // then check for overlap.\n        if (!deltaIsEven && Math.abs(diagonal - diagonalReverseBase) <= numDifferences - 1) {\n          if (originalIndex >= reversePoints[diagonal]) {\n            midOriginalArr[0] = originalIndex;\n            midModifiedArr[0] = modifiedIndex;\n            if (tempOriginalIndex <= reversePoints[diagonal] && 1447 /* LocalConstants.MaxDifferencesHistory */ > 0 && numDifferences <= 1447 /* LocalConstants.MaxDifferencesHistory */ + 1) {\n              // BINGO! We overlapped, and we have the full trace in memory!\n              return this.WALKTRACE(diagonalForwardBase, diagonalForwardStart, diagonalForwardEnd, diagonalForwardOffset, diagonalReverseBase, diagonalReverseStart, diagonalReverseEnd, diagonalReverseOffset, forwardPoints, reversePoints, originalIndex, originalEnd, midOriginalArr, modifiedIndex, modifiedEnd, midModifiedArr, deltaIsEven, quitEarlyArr);\n            } else {\n              // Either false overlap, or we didn't have enough memory for the full trace\n              // Just return the recursion point\n              return null;\n            }\n          }\n        }\n      }\n      // Check to see if we should be quitting early, before moving on to the next iteration.\n      const matchLengthOfLongest = (furthestOriginalIndex - originalStart + (furthestModifiedIndex - modifiedStart) - numDifferences) / 2;\n      if (this.ContinueProcessingPredicate !== null && !this.ContinueProcessingPredicate(furthestOriginalIndex, matchLengthOfLongest)) {\n        // We can't finish, so skip ahead to generating a result from what we have.\n        quitEarlyArr[0] = true;\n        // Use the furthest distance we got in the forward direction.\n        midOriginalArr[0] = furthestOriginalIndex;\n        midModifiedArr[0] = furthestModifiedIndex;\n        if (matchLengthOfLongest > 0 && 1447 /* LocalConstants.MaxDifferencesHistory */ > 0 && numDifferences <= 1447 /* LocalConstants.MaxDifferencesHistory */ + 1) {\n          // Enough of the history is in memory to walk it backwards\n          return this.WALKTRACE(diagonalForwardBase, diagonalForwardStart, diagonalForwardEnd, diagonalForwardOffset, diagonalReverseBase, diagonalReverseStart, diagonalReverseEnd, diagonalReverseOffset, forwardPoints, reversePoints, originalIndex, originalEnd, midOriginalArr, modifiedIndex, modifiedEnd, midModifiedArr, deltaIsEven, quitEarlyArr);\n        } else {\n          // We didn't actually remember enough of the history.\n          //Since we are quitting the diff early, we need to shift back the originalStart and modified start\n          //back into the boundary limits since we decremented their value above beyond the boundary limit.\n          originalStart++;\n          modifiedStart++;\n          return [new DiffChange(originalStart, originalEnd - originalStart + 1, modifiedStart, modifiedEnd - modifiedStart + 1)];\n        }\n      }\n      // Run the algorithm in the reverse direction\n      diagonalReverseStart = this.ClipDiagonalBound(diagonalReverseBase - numDifferences, numDifferences, diagonalReverseBase, numDiagonals);\n      diagonalReverseEnd = this.ClipDiagonalBound(diagonalReverseBase + numDifferences, numDifferences, diagonalReverseBase, numDiagonals);\n      for (let diagonal = diagonalReverseStart; diagonal <= diagonalReverseEnd; diagonal += 2) {\n        // STEP 1: We extend the furthest reaching point in the present diagonal\n        // by looking at the diagonals above and below and picking the one whose point\n        // is further away from the start point (originalEnd, modifiedEnd)\n        if (diagonal === diagonalReverseStart || diagonal < diagonalReverseEnd && reversePoints[diagonal - 1] >= reversePoints[diagonal + 1]) {\n          originalIndex = reversePoints[diagonal + 1] - 1;\n        } else {\n          originalIndex = reversePoints[diagonal - 1];\n        }\n        modifiedIndex = originalIndex - (diagonal - diagonalReverseBase) - diagonalReverseOffset;\n        // Save the current originalIndex so we can test for false overlap\n        const tempOriginalIndex = originalIndex;\n        // STEP 2: We can continue to extend the furthest reaching point in the present diagonal\n        // as long as the elements are equal.\n        while (originalIndex > originalStart && modifiedIndex > modifiedStart && this.ElementsAreEqual(originalIndex, modifiedIndex)) {\n          originalIndex--;\n          modifiedIndex--;\n        }\n        reversePoints[diagonal] = originalIndex;\n        // STEP 4: If delta is even (overlap first happens on reverse when delta is even)\n        // and diagonal is in the range of forward diagonals computed for numDifferences\n        // then check for overlap.\n        if (deltaIsEven && Math.abs(diagonal - diagonalForwardBase) <= numDifferences) {\n          if (originalIndex <= forwardPoints[diagonal]) {\n            midOriginalArr[0] = originalIndex;\n            midModifiedArr[0] = modifiedIndex;\n            if (tempOriginalIndex >= forwardPoints[diagonal] && 1447 /* LocalConstants.MaxDifferencesHistory */ > 0 && numDifferences <= 1447 /* LocalConstants.MaxDifferencesHistory */ + 1) {\n              // BINGO! We overlapped, and we have the full trace in memory!\n              return this.WALKTRACE(diagonalForwardBase, diagonalForwardStart, diagonalForwardEnd, diagonalForwardOffset, diagonalReverseBase, diagonalReverseStart, diagonalReverseEnd, diagonalReverseOffset, forwardPoints, reversePoints, originalIndex, originalEnd, midOriginalArr, modifiedIndex, modifiedEnd, midModifiedArr, deltaIsEven, quitEarlyArr);\n            } else {\n              // Either false overlap, or we didn't have enough memory for the full trace\n              // Just return the recursion point\n              return null;\n            }\n          }\n        }\n      }\n      // Save current vectors to history before the next iteration\n      if (numDifferences <= 1447 /* LocalConstants.MaxDifferencesHistory */) {\n        // We are allocating space for one extra int, which we fill with\n        // the index of the diagonal base index\n        let temp = new Int32Array(diagonalForwardEnd - diagonalForwardStart + 2);\n        temp[0] = diagonalForwardBase - diagonalForwardStart + 1;\n        MyArray.Copy2(forwardPoints, diagonalForwardStart, temp, 1, diagonalForwardEnd - diagonalForwardStart + 1);\n        this.m_forwardHistory.push(temp);\n        temp = new Int32Array(diagonalReverseEnd - diagonalReverseStart + 2);\n        temp[0] = diagonalReverseBase - diagonalReverseStart + 1;\n        MyArray.Copy2(reversePoints, diagonalReverseStart, temp, 1, diagonalReverseEnd - diagonalReverseStart + 1);\n        this.m_reverseHistory.push(temp);\n      }\n    }\n    // If we got here, then we have the full trace in history. We just have to convert it to a change list\n    // NOTE: This part is a bit messy\n    return this.WALKTRACE(diagonalForwardBase, diagonalForwardStart, diagonalForwardEnd, diagonalForwardOffset, diagonalReverseBase, diagonalReverseStart, diagonalReverseEnd, diagonalReverseOffset, forwardPoints, reversePoints, originalIndex, originalEnd, midOriginalArr, modifiedIndex, modifiedEnd, midModifiedArr, deltaIsEven, quitEarlyArr);\n  }\n  /**\n   * Shifts the given changes to provide a more intuitive diff.\n   * While the first element in a diff matches the first element after the diff,\n   * we shift the diff down.\n   *\n   * @param changes The list of changes to shift\n   * @returns The shifted changes\n   */\n  PrettifyChanges(changes) {\n    // Shift all the changes down first\n    for (let i = 0; i < changes.length; i++) {\n      const change = changes[i];\n      const originalStop = i < changes.length - 1 ? changes[i + 1].originalStart : this._originalElementsOrHash.length;\n      const modifiedStop = i < changes.length - 1 ? changes[i + 1].modifiedStart : this._modifiedElementsOrHash.length;\n      const checkOriginal = change.originalLength > 0;\n      const checkModified = change.modifiedLength > 0;\n      while (change.originalStart + change.originalLength < originalStop && change.modifiedStart + change.modifiedLength < modifiedStop && (!checkOriginal || this.OriginalElementsAreEqual(change.originalStart, change.originalStart + change.originalLength)) && (!checkModified || this.ModifiedElementsAreEqual(change.modifiedStart, change.modifiedStart + change.modifiedLength))) {\n        const startStrictEqual = this.ElementsAreStrictEqual(change.originalStart, change.modifiedStart);\n        const endStrictEqual = this.ElementsAreStrictEqual(change.originalStart + change.originalLength, change.modifiedStart + change.modifiedLength);\n        if (endStrictEqual && !startStrictEqual) {\n          // moving the change down would create an equal change, but the elements are not strict equal\n          break;\n        }\n        change.originalStart++;\n        change.modifiedStart++;\n      }\n      const mergedChangeArr = [null];\n      if (i < changes.length - 1 && this.ChangesOverlap(changes[i], changes[i + 1], mergedChangeArr)) {\n        changes[i] = mergedChangeArr[0];\n        changes.splice(i + 1, 1);\n        i--;\n        continue;\n      }\n    }\n    // Shift changes back up until we hit empty or whitespace-only lines\n    for (let i = changes.length - 1; i >= 0; i--) {\n      const change = changes[i];\n      let originalStop = 0;\n      let modifiedStop = 0;\n      if (i > 0) {\n        const prevChange = changes[i - 1];\n        originalStop = prevChange.originalStart + prevChange.originalLength;\n        modifiedStop = prevChange.modifiedStart + prevChange.modifiedLength;\n      }\n      const checkOriginal = change.originalLength > 0;\n      const checkModified = change.modifiedLength > 0;\n      let bestDelta = 0;\n      let bestScore = this._boundaryScore(change.originalStart, change.originalLength, change.modifiedStart, change.modifiedLength);\n      for (let delta = 1;; delta++) {\n        const originalStart = change.originalStart - delta;\n        const modifiedStart = change.modifiedStart - delta;\n        if (originalStart < originalStop || modifiedStart < modifiedStop) {\n          break;\n        }\n        if (checkOriginal && !this.OriginalElementsAreEqual(originalStart, originalStart + change.originalLength)) {\n          break;\n        }\n        if (checkModified && !this.ModifiedElementsAreEqual(modifiedStart, modifiedStart + change.modifiedLength)) {\n          break;\n        }\n        const touchingPreviousChange = originalStart === originalStop && modifiedStart === modifiedStop;\n        const score = (touchingPreviousChange ? 5 : 0) + this._boundaryScore(originalStart, change.originalLength, modifiedStart, change.modifiedLength);\n        if (score > bestScore) {\n          bestScore = score;\n          bestDelta = delta;\n        }\n      }\n      change.originalStart -= bestDelta;\n      change.modifiedStart -= bestDelta;\n      const mergedChangeArr = [null];\n      if (i > 0 && this.ChangesOverlap(changes[i - 1], changes[i], mergedChangeArr)) {\n        changes[i - 1] = mergedChangeArr[0];\n        changes.splice(i, 1);\n        i++;\n        continue;\n      }\n    }\n    // There could be multiple longest common substrings.\n    // Give preference to the ones containing longer lines\n    if (this._hasStrings) {\n      for (let i = 1, len = changes.length; i < len; i++) {\n        const aChange = changes[i - 1];\n        const bChange = changes[i];\n        const matchedLength = bChange.originalStart - aChange.originalStart - aChange.originalLength;\n        const aOriginalStart = aChange.originalStart;\n        const bOriginalEnd = bChange.originalStart + bChange.originalLength;\n        const abOriginalLength = bOriginalEnd - aOriginalStart;\n        const aModifiedStart = aChange.modifiedStart;\n        const bModifiedEnd = bChange.modifiedStart + bChange.modifiedLength;\n        const abModifiedLength = bModifiedEnd - aModifiedStart;\n        // Avoid wasting a lot of time with these searches\n        if (matchedLength < 5 && abOriginalLength < 20 && abModifiedLength < 20) {\n          const t = this._findBetterContiguousSequence(aOriginalStart, abOriginalLength, aModifiedStart, abModifiedLength, matchedLength);\n          if (t) {\n            const [originalMatchStart, modifiedMatchStart] = t;\n            if (originalMatchStart !== aChange.originalStart + aChange.originalLength || modifiedMatchStart !== aChange.modifiedStart + aChange.modifiedLength) {\n              // switch to another sequence that has a better score\n              aChange.originalLength = originalMatchStart - aChange.originalStart;\n              aChange.modifiedLength = modifiedMatchStart - aChange.modifiedStart;\n              bChange.originalStart = originalMatchStart + matchedLength;\n              bChange.modifiedStart = modifiedMatchStart + matchedLength;\n              bChange.originalLength = bOriginalEnd - bChange.originalStart;\n              bChange.modifiedLength = bModifiedEnd - bChange.modifiedStart;\n            }\n          }\n        }\n      }\n    }\n    return changes;\n  }\n  _findBetterContiguousSequence(originalStart, originalLength, modifiedStart, modifiedLength, desiredLength) {\n    if (originalLength < desiredLength || modifiedLength < desiredLength) {\n      return null;\n    }\n    const originalMax = originalStart + originalLength - desiredLength + 1;\n    const modifiedMax = modifiedStart + modifiedLength - desiredLength + 1;\n    let bestScore = 0;\n    let bestOriginalStart = 0;\n    let bestModifiedStart = 0;\n    for (let i = originalStart; i < originalMax; i++) {\n      for (let j = modifiedStart; j < modifiedMax; j++) {\n        const score = this._contiguousSequenceScore(i, j, desiredLength);\n        if (score > 0 && score > bestScore) {\n          bestScore = score;\n          bestOriginalStart = i;\n          bestModifiedStart = j;\n        }\n      }\n    }\n    if (bestScore > 0) {\n      return [bestOriginalStart, bestModifiedStart];\n    }\n    return null;\n  }\n  _contiguousSequenceScore(originalStart, modifiedStart, length) {\n    let score = 0;\n    for (let l = 0; l < length; l++) {\n      if (!this.ElementsAreEqual(originalStart + l, modifiedStart + l)) {\n        return 0;\n      }\n      score += this._originalStringElements[originalStart + l].length;\n    }\n    return score;\n  }\n  _OriginalIsBoundary(index) {\n    if (index <= 0 || index >= this._originalElementsOrHash.length - 1) {\n      return true;\n    }\n    return this._hasStrings && /^\\s*$/.test(this._originalStringElements[index]);\n  }\n  _OriginalRegionIsBoundary(originalStart, originalLength) {\n    if (this._OriginalIsBoundary(originalStart) || this._OriginalIsBoundary(originalStart - 1)) {\n      return true;\n    }\n    if (originalLength > 0) {\n      const originalEnd = originalStart + originalLength;\n      if (this._OriginalIsBoundary(originalEnd - 1) || this._OriginalIsBoundary(originalEnd)) {\n        return true;\n      }\n    }\n    return false;\n  }\n  _ModifiedIsBoundary(index) {\n    if (index <= 0 || index >= this._modifiedElementsOrHash.length - 1) {\n      return true;\n    }\n    return this._hasStrings && /^\\s*$/.test(this._modifiedStringElements[index]);\n  }\n  _ModifiedRegionIsBoundary(modifiedStart, modifiedLength) {\n    if (this._ModifiedIsBoundary(modifiedStart) || this._ModifiedIsBoundary(modifiedStart - 1)) {\n      return true;\n    }\n    if (modifiedLength > 0) {\n      const modifiedEnd = modifiedStart + modifiedLength;\n      if (this._ModifiedIsBoundary(modifiedEnd - 1) || this._ModifiedIsBoundary(modifiedEnd)) {\n        return true;\n      }\n    }\n    return false;\n  }\n  _boundaryScore(originalStart, originalLength, modifiedStart, modifiedLength) {\n    const originalScore = this._OriginalRegionIsBoundary(originalStart, originalLength) ? 1 : 0;\n    const modifiedScore = this._ModifiedRegionIsBoundary(modifiedStart, modifiedLength) ? 1 : 0;\n    return originalScore + modifiedScore;\n  }\n  /**\n   * Concatenates the two input DiffChange lists and returns the resulting\n   * list.\n   * @param The left changes\n   * @param The right changes\n   * @returns The concatenated list\n   */\n  ConcatenateChanges(left, right) {\n    const mergedChangeArr = [];\n    if (left.length === 0 || right.length === 0) {\n      return right.length > 0 ? right : left;\n    } else if (this.ChangesOverlap(left[left.length - 1], right[0], mergedChangeArr)) {\n      // Since we break the problem down recursively, it is possible that we\n      // might recurse in the middle of a change thereby splitting it into\n      // two changes. Here in the combining stage, we detect and fuse those\n      // changes back together\n      const result = new Array(left.length + right.length - 1);\n      MyArray.Copy(left, 0, result, 0, left.length - 1);\n      result[left.length - 1] = mergedChangeArr[0];\n      MyArray.Copy(right, 1, result, left.length, right.length - 1);\n      return result;\n    } else {\n      const result = new Array(left.length + right.length);\n      MyArray.Copy(left, 0, result, 0, left.length);\n      MyArray.Copy(right, 0, result, left.length, right.length);\n      return result;\n    }\n  }\n  /**\n   * Returns true if the two changes overlap and can be merged into a single\n   * change\n   * @param left The left change\n   * @param right The right change\n   * @param mergedChange The merged change if the two overlap, null otherwise\n   * @returns True if the two changes overlap\n   */\n  ChangesOverlap(left, right, mergedChangeArr) {\n    Debug.Assert(left.originalStart <= right.originalStart, 'Left change is not less than or equal to right change');\n    Debug.Assert(left.modifiedStart <= right.modifiedStart, 'Left change is not less than or equal to right change');\n    if (left.originalStart + left.originalLength >= right.originalStart || left.modifiedStart + left.modifiedLength >= right.modifiedStart) {\n      const originalStart = left.originalStart;\n      let originalLength = left.originalLength;\n      const modifiedStart = left.modifiedStart;\n      let modifiedLength = left.modifiedLength;\n      if (left.originalStart + left.originalLength >= right.originalStart) {\n        originalLength = right.originalStart + right.originalLength - left.originalStart;\n      }\n      if (left.modifiedStart + left.modifiedLength >= right.modifiedStart) {\n        modifiedLength = right.modifiedStart + right.modifiedLength - left.modifiedStart;\n      }\n      mergedChangeArr[0] = new DiffChange(originalStart, originalLength, modifiedStart, modifiedLength);\n      return true;\n    } else {\n      mergedChangeArr[0] = null;\n      return false;\n    }\n  }\n  /**\n   * Helper method used to clip a diagonal index to the range of valid\n   * diagonals. This also decides whether or not the diagonal index,\n   * if it exceeds the boundary, should be clipped to the boundary or clipped\n   * one inside the boundary depending on the Even/Odd status of the boundary\n   * and numDifferences.\n   * @param diagonal The index of the diagonal to clip.\n   * @param numDifferences The current number of differences being iterated upon.\n   * @param diagonalBaseIndex The base reference diagonal.\n   * @param numDiagonals The total number of diagonals.\n   * @returns The clipped diagonal index.\n   */\n  ClipDiagonalBound(diagonal, numDifferences, diagonalBaseIndex, numDiagonals) {\n    if (diagonal >= 0 && diagonal < numDiagonals) {\n      // Nothing to clip, its in range\n      return diagonal;\n    }\n    // diagonalsBelow: The number of diagonals below the reference diagonal\n    // diagonalsAbove: The number of diagonals above the reference diagonal\n    const diagonalsBelow = diagonalBaseIndex;\n    const diagonalsAbove = numDiagonals - diagonalBaseIndex - 1;\n    const diffEven = numDifferences % 2 === 0;\n    if (diagonal < 0) {\n      const lowerBoundEven = diagonalsBelow % 2 === 0;\n      return diffEven === lowerBoundEven ? 0 : 1;\n    } else {\n      const upperBoundEven = diagonalsAbove % 2 === 0;\n      return diffEven === upperBoundEven ? numDiagonals - 1 : numDiagonals - 2;\n    }\n  }\n}", "map": {"version": 3, "names": ["DiffChange", "stringHash", "StringDiffSequence", "constructor", "source", "getElements", "characters", "Int32Array", "length", "i", "len", "charCodeAt", "stringDiff", "original", "modified", "pretty", "LcsDiff", "ComputeDiff", "changes", "Debug", "Assert", "condition", "message", "Error", "MyArray", "Copy", "sourceArray", "sourceIndex", "destinationArray", "destinationIndex", "Copy2", "DiffChangeHelper", "m_changes", "m_originalStart", "m_modifiedStart", "m_originalCount", "m_modifiedCount", "MarkNextChange", "push", "AddOriginalElement", "originalIndex", "modifiedIndex", "Math", "min", "AddModifiedElement", "getChanges", "getReverseChanges", "reverse", "originalSequence", "modifiedSequence", "continueProcessingPredicate", "ContinueProcessingPredicate", "_originalSequence", "_modifiedSequence", "originalStringElements", "originalElementsOrHash", "originalHasStrings", "_getElements", "modifiedStringElements", "modifiedElementsOrHash", "modifiedHasStrings", "_hasStrings", "_originalStringElements", "_originalElementsOrHash", "_modifiedStringElements", "_modifiedElementsOrHash", "m_forwardHistory", "m_reverseHistory", "_isStringArray", "arr", "sequence", "elements", "hashes", "ElementsAreEqual", "newIndex", "ElementsAreStrictEqual", "originalElement", "_getStrictElement", "modifiedElement", "index", "getStrictElement", "OriginalElementsAreEqual", "index1", "index2", "ModifiedElementsAreEqual", "_ComputeDiff", "originalStart", "originalEnd", "modifiedStart", "modifiedEnd", "quitEarlyArr", "ComputeDiffRecursive", "PrettifyChanges", "quit<PERSON>arly", "midOriginalArr", "midModifiedArr", "result", "ComputeRecursionPoint", "midOriginal", "midModified", "leftChanges", "rightChanges", "ConcatenateChanges", "WALKTRACE", "diagonalForwardBase", "diagonalForwardStart", "diagonalForwardEnd", "diagonalForwardOffset", "diagonalReverseBase", "diagonalReverseStart", "diagonalReverseEnd", "diagonalReverseOffset", "forwardPoints", "reversePoints", "deltaIsEven", "<PERSON><PERSON><PERSON><PERSON>", "reverseChanges", "changeHelper", "diagonalMin", "diagonalMax", "diagonalRelative", "lastOriginalIndex", "historyIndex", "diagonal", "originalStartPoint", "modifiedStartPoint", "lastF<PERSON><PERSON><PERSON><PERSON><PERSON>", "max", "getOriginalEnd", "getModifiedEnd", "maxDifferences", "numDiagonals", "delta", "numDifferences", "furthestOriginalIndex", "furthestModifiedIndex", "ClipDiagonalBound", "tempOriginalIndex", "abs", "matchLengthOfLongest", "temp", "change", "originalStop", "modifiedStop", "checkOriginal", "original<PERSON>ength", "checkModified", "<PERSON><PERSON><PERSON><PERSON>", "startStrictEqual", "endStrictEqual", "mergedChangeArr", "ChangesOverlap", "splice", "prevChange", "best<PERSON><PERSON><PERSON>", "bestScore", "_boundaryScore", "touchingPreviousChange", "score", "aChange", "b<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "aOriginalStart", "bOriginalEnd", "ab<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aModifiedStart", "bModifiedEnd", "abModifiedLength", "t", "_findBetterContiguousSequence", "originalMatchStart", "modifiedMatchStart", "<PERSON><PERSON><PERSON><PERSON>", "originalMax", "modifiedMax", "bestOriginalStart", "bestModifiedStart", "j", "_contiguousSequenceScore", "l", "_OriginalIsBoundary", "test", "_OriginalRegionIsBoundary", "_ModifiedIsBoundary", "_ModifiedRegionIsBoundary", "originalScore", "modifiedScore", "left", "right", "Array", "diagonalBaseIndex", "diagonals<PERSON><PERSON><PERSON>", "diagonalsAbove", "diffEven", "lowerBoundEven", "upperBoundEven"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/common/diff/diff.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { DiffChange } from './diffChange.js';\nimport { stringHash } from '../hash.js';\nexport class StringDiffSequence {\n    constructor(source) {\n        this.source = source;\n    }\n    getElements() {\n        const source = this.source;\n        const characters = new Int32Array(source.length);\n        for (let i = 0, len = source.length; i < len; i++) {\n            characters[i] = source.charCodeAt(i);\n        }\n        return characters;\n    }\n}\nexport function stringDiff(original, modified, pretty) {\n    return new LcsDiff(new StringDiffSequence(original), new StringDiffSequence(modified)).ComputeDiff(pretty).changes;\n}\n//\n// The code below has been ported from a C# implementation in VS\n//\nclass Debug {\n    static Assert(condition, message) {\n        if (!condition) {\n            throw new Error(message);\n        }\n    }\n}\nclass MyArray {\n    /**\n     * Copies a range of elements from an Array starting at the specified source index and pastes\n     * them to another Array starting at the specified destination index. The length and the indexes\n     * are specified as 64-bit integers.\n     * sourceArray:\n     *\t\tThe Array that contains the data to copy.\n     * sourceIndex:\n     *\t\tA 64-bit integer that represents the index in the sourceArray at which copying begins.\n     * destinationArray:\n     *\t\tThe Array that receives the data.\n     * destinationIndex:\n     *\t\tA 64-bit integer that represents the index in the destinationArray at which storing begins.\n     * length:\n     *\t\tA 64-bit integer that represents the number of elements to copy.\n     */\n    static Copy(sourceArray, sourceIndex, destinationArray, destinationIndex, length) {\n        for (let i = 0; i < length; i++) {\n            destinationArray[destinationIndex + i] = sourceArray[sourceIndex + i];\n        }\n    }\n    static Copy2(sourceArray, sourceIndex, destinationArray, destinationIndex, length) {\n        for (let i = 0; i < length; i++) {\n            destinationArray[destinationIndex + i] = sourceArray[sourceIndex + i];\n        }\n    }\n}\n/**\n * A utility class which helps to create the set of DiffChanges from\n * a difference operation. This class accepts original DiffElements and\n * modified DiffElements that are involved in a particular change. The\n * MarkNextChange() method can be called to mark the separation between\n * distinct changes. At the end, the Changes property can be called to retrieve\n * the constructed changes.\n */\nclass DiffChangeHelper {\n    /**\n     * Constructs a new DiffChangeHelper for the given DiffSequences.\n     */\n    constructor() {\n        this.m_changes = [];\n        this.m_originalStart = 1073741824 /* Constants.MAX_SAFE_SMALL_INTEGER */;\n        this.m_modifiedStart = 1073741824 /* Constants.MAX_SAFE_SMALL_INTEGER */;\n        this.m_originalCount = 0;\n        this.m_modifiedCount = 0;\n    }\n    /**\n     * Marks the beginning of the next change in the set of differences.\n     */\n    MarkNextChange() {\n        // Only add to the list if there is something to add\n        if (this.m_originalCount > 0 || this.m_modifiedCount > 0) {\n            // Add the new change to our list\n            this.m_changes.push(new DiffChange(this.m_originalStart, this.m_originalCount, this.m_modifiedStart, this.m_modifiedCount));\n        }\n        // Reset for the next change\n        this.m_originalCount = 0;\n        this.m_modifiedCount = 0;\n        this.m_originalStart = 1073741824 /* Constants.MAX_SAFE_SMALL_INTEGER */;\n        this.m_modifiedStart = 1073741824 /* Constants.MAX_SAFE_SMALL_INTEGER */;\n    }\n    /**\n     * Adds the original element at the given position to the elements\n     * affected by the current change. The modified index gives context\n     * to the change position with respect to the original sequence.\n     * @param originalIndex The index of the original element to add.\n     * @param modifiedIndex The index of the modified element that provides corresponding position in the modified sequence.\n     */\n    AddOriginalElement(originalIndex, modifiedIndex) {\n        // The 'true' start index is the smallest of the ones we've seen\n        this.m_originalStart = Math.min(this.m_originalStart, originalIndex);\n        this.m_modifiedStart = Math.min(this.m_modifiedStart, modifiedIndex);\n        this.m_originalCount++;\n    }\n    /**\n     * Adds the modified element at the given position to the elements\n     * affected by the current change. The original index gives context\n     * to the change position with respect to the modified sequence.\n     * @param originalIndex The index of the original element that provides corresponding position in the original sequence.\n     * @param modifiedIndex The index of the modified element to add.\n     */\n    AddModifiedElement(originalIndex, modifiedIndex) {\n        // The 'true' start index is the smallest of the ones we've seen\n        this.m_originalStart = Math.min(this.m_originalStart, originalIndex);\n        this.m_modifiedStart = Math.min(this.m_modifiedStart, modifiedIndex);\n        this.m_modifiedCount++;\n    }\n    /**\n     * Retrieves all of the changes marked by the class.\n     */\n    getChanges() {\n        if (this.m_originalCount > 0 || this.m_modifiedCount > 0) {\n            // Finish up on whatever is left\n            this.MarkNextChange();\n        }\n        return this.m_changes;\n    }\n    /**\n     * Retrieves all of the changes marked by the class in the reverse order\n     */\n    getReverseChanges() {\n        if (this.m_originalCount > 0 || this.m_modifiedCount > 0) {\n            // Finish up on whatever is left\n            this.MarkNextChange();\n        }\n        this.m_changes.reverse();\n        return this.m_changes;\n    }\n}\n/**\n * An implementation of the difference algorithm described in\n * \"An O(ND) Difference Algorithm and its variations\" by Eugene W. Myers\n */\nexport class LcsDiff {\n    /**\n     * Constructs the DiffFinder\n     */\n    constructor(originalSequence, modifiedSequence, continueProcessingPredicate = null) {\n        this.ContinueProcessingPredicate = continueProcessingPredicate;\n        this._originalSequence = originalSequence;\n        this._modifiedSequence = modifiedSequence;\n        const [originalStringElements, originalElementsOrHash, originalHasStrings] = LcsDiff._getElements(originalSequence);\n        const [modifiedStringElements, modifiedElementsOrHash, modifiedHasStrings] = LcsDiff._getElements(modifiedSequence);\n        this._hasStrings = (originalHasStrings && modifiedHasStrings);\n        this._originalStringElements = originalStringElements;\n        this._originalElementsOrHash = originalElementsOrHash;\n        this._modifiedStringElements = modifiedStringElements;\n        this._modifiedElementsOrHash = modifiedElementsOrHash;\n        this.m_forwardHistory = [];\n        this.m_reverseHistory = [];\n    }\n    static _isStringArray(arr) {\n        return (arr.length > 0 && typeof arr[0] === 'string');\n    }\n    static _getElements(sequence) {\n        const elements = sequence.getElements();\n        if (LcsDiff._isStringArray(elements)) {\n            const hashes = new Int32Array(elements.length);\n            for (let i = 0, len = elements.length; i < len; i++) {\n                hashes[i] = stringHash(elements[i], 0);\n            }\n            return [elements, hashes, true];\n        }\n        if (elements instanceof Int32Array) {\n            return [[], elements, false];\n        }\n        return [[], new Int32Array(elements), false];\n    }\n    ElementsAreEqual(originalIndex, newIndex) {\n        if (this._originalElementsOrHash[originalIndex] !== this._modifiedElementsOrHash[newIndex]) {\n            return false;\n        }\n        return (this._hasStrings ? this._originalStringElements[originalIndex] === this._modifiedStringElements[newIndex] : true);\n    }\n    ElementsAreStrictEqual(originalIndex, newIndex) {\n        if (!this.ElementsAreEqual(originalIndex, newIndex)) {\n            return false;\n        }\n        const originalElement = LcsDiff._getStrictElement(this._originalSequence, originalIndex);\n        const modifiedElement = LcsDiff._getStrictElement(this._modifiedSequence, newIndex);\n        return (originalElement === modifiedElement);\n    }\n    static _getStrictElement(sequence, index) {\n        if (typeof sequence.getStrictElement === 'function') {\n            return sequence.getStrictElement(index);\n        }\n        return null;\n    }\n    OriginalElementsAreEqual(index1, index2) {\n        if (this._originalElementsOrHash[index1] !== this._originalElementsOrHash[index2]) {\n            return false;\n        }\n        return (this._hasStrings ? this._originalStringElements[index1] === this._originalStringElements[index2] : true);\n    }\n    ModifiedElementsAreEqual(index1, index2) {\n        if (this._modifiedElementsOrHash[index1] !== this._modifiedElementsOrHash[index2]) {\n            return false;\n        }\n        return (this._hasStrings ? this._modifiedStringElements[index1] === this._modifiedStringElements[index2] : true);\n    }\n    ComputeDiff(pretty) {\n        return this._ComputeDiff(0, this._originalElementsOrHash.length - 1, 0, this._modifiedElementsOrHash.length - 1, pretty);\n    }\n    /**\n     * Computes the differences between the original and modified input\n     * sequences on the bounded range.\n     * @returns An array of the differences between the two input sequences.\n     */\n    _ComputeDiff(originalStart, originalEnd, modifiedStart, modifiedEnd, pretty) {\n        const quitEarlyArr = [false];\n        let changes = this.ComputeDiffRecursive(originalStart, originalEnd, modifiedStart, modifiedEnd, quitEarlyArr);\n        if (pretty) {\n            // We have to clean up the computed diff to be more intuitive\n            // but it turns out this cannot be done correctly until the entire set\n            // of diffs have been computed\n            changes = this.PrettifyChanges(changes);\n        }\n        return {\n            quitEarly: quitEarlyArr[0],\n            changes: changes\n        };\n    }\n    /**\n     * Private helper method which computes the differences on the bounded range\n     * recursively.\n     * @returns An array of the differences between the two input sequences.\n     */\n    ComputeDiffRecursive(originalStart, originalEnd, modifiedStart, modifiedEnd, quitEarlyArr) {\n        quitEarlyArr[0] = false;\n        // Find the start of the differences\n        while (originalStart <= originalEnd && modifiedStart <= modifiedEnd && this.ElementsAreEqual(originalStart, modifiedStart)) {\n            originalStart++;\n            modifiedStart++;\n        }\n        // Find the end of the differences\n        while (originalEnd >= originalStart && modifiedEnd >= modifiedStart && this.ElementsAreEqual(originalEnd, modifiedEnd)) {\n            originalEnd--;\n            modifiedEnd--;\n        }\n        // In the special case where we either have all insertions or all deletions or the sequences are identical\n        if (originalStart > originalEnd || modifiedStart > modifiedEnd) {\n            let changes;\n            if (modifiedStart <= modifiedEnd) {\n                Debug.Assert(originalStart === originalEnd + 1, 'originalStart should only be one more than originalEnd');\n                // All insertions\n                changes = [\n                    new DiffChange(originalStart, 0, modifiedStart, modifiedEnd - modifiedStart + 1)\n                ];\n            }\n            else if (originalStart <= originalEnd) {\n                Debug.Assert(modifiedStart === modifiedEnd + 1, 'modifiedStart should only be one more than modifiedEnd');\n                // All deletions\n                changes = [\n                    new DiffChange(originalStart, originalEnd - originalStart + 1, modifiedStart, 0)\n                ];\n            }\n            else {\n                Debug.Assert(originalStart === originalEnd + 1, 'originalStart should only be one more than originalEnd');\n                Debug.Assert(modifiedStart === modifiedEnd + 1, 'modifiedStart should only be one more than modifiedEnd');\n                // Identical sequences - No differences\n                changes = [];\n            }\n            return changes;\n        }\n        // This problem can be solved using the Divide-And-Conquer technique.\n        const midOriginalArr = [0];\n        const midModifiedArr = [0];\n        const result = this.ComputeRecursionPoint(originalStart, originalEnd, modifiedStart, modifiedEnd, midOriginalArr, midModifiedArr, quitEarlyArr);\n        const midOriginal = midOriginalArr[0];\n        const midModified = midModifiedArr[0];\n        if (result !== null) {\n            // Result is not-null when there was enough memory to compute the changes while\n            // searching for the recursion point\n            return result;\n        }\n        else if (!quitEarlyArr[0]) {\n            // We can break the problem down recursively by finding the changes in the\n            // First Half:   (originalStart, modifiedStart) to (midOriginal, midModified)\n            // Second Half:  (midOriginal + 1, minModified + 1) to (originalEnd, modifiedEnd)\n            // NOTE: ComputeDiff() is inclusive, therefore the second range starts on the next point\n            const leftChanges = this.ComputeDiffRecursive(originalStart, midOriginal, modifiedStart, midModified, quitEarlyArr);\n            let rightChanges = [];\n            if (!quitEarlyArr[0]) {\n                rightChanges = this.ComputeDiffRecursive(midOriginal + 1, originalEnd, midModified + 1, modifiedEnd, quitEarlyArr);\n            }\n            else {\n                // We didn't have time to finish the first half, so we don't have time to compute this half.\n                // Consider the entire rest of the sequence different.\n                rightChanges = [\n                    new DiffChange(midOriginal + 1, originalEnd - (midOriginal + 1) + 1, midModified + 1, modifiedEnd - (midModified + 1) + 1)\n                ];\n            }\n            return this.ConcatenateChanges(leftChanges, rightChanges);\n        }\n        // If we hit here, we quit early, and so can't return anything meaningful\n        return [\n            new DiffChange(originalStart, originalEnd - originalStart + 1, modifiedStart, modifiedEnd - modifiedStart + 1)\n        ];\n    }\n    WALKTRACE(diagonalForwardBase, diagonalForwardStart, diagonalForwardEnd, diagonalForwardOffset, diagonalReverseBase, diagonalReverseStart, diagonalReverseEnd, diagonalReverseOffset, forwardPoints, reversePoints, originalIndex, originalEnd, midOriginalArr, modifiedIndex, modifiedEnd, midModifiedArr, deltaIsEven, quitEarlyArr) {\n        let forwardChanges = null;\n        let reverseChanges = null;\n        // First, walk backward through the forward diagonals history\n        let changeHelper = new DiffChangeHelper();\n        let diagonalMin = diagonalForwardStart;\n        let diagonalMax = diagonalForwardEnd;\n        let diagonalRelative = (midOriginalArr[0] - midModifiedArr[0]) - diagonalForwardOffset;\n        let lastOriginalIndex = -1073741824 /* Constants.MIN_SAFE_SMALL_INTEGER */;\n        let historyIndex = this.m_forwardHistory.length - 1;\n        do {\n            // Get the diagonal index from the relative diagonal number\n            const diagonal = diagonalRelative + diagonalForwardBase;\n            // Figure out where we came from\n            if (diagonal === diagonalMin || (diagonal < diagonalMax && forwardPoints[diagonal - 1] < forwardPoints[diagonal + 1])) {\n                // Vertical line (the element is an insert)\n                originalIndex = forwardPoints[diagonal + 1];\n                modifiedIndex = originalIndex - diagonalRelative - diagonalForwardOffset;\n                if (originalIndex < lastOriginalIndex) {\n                    changeHelper.MarkNextChange();\n                }\n                lastOriginalIndex = originalIndex;\n                changeHelper.AddModifiedElement(originalIndex + 1, modifiedIndex);\n                diagonalRelative = (diagonal + 1) - diagonalForwardBase; //Setup for the next iteration\n            }\n            else {\n                // Horizontal line (the element is a deletion)\n                originalIndex = forwardPoints[diagonal - 1] + 1;\n                modifiedIndex = originalIndex - diagonalRelative - diagonalForwardOffset;\n                if (originalIndex < lastOriginalIndex) {\n                    changeHelper.MarkNextChange();\n                }\n                lastOriginalIndex = originalIndex - 1;\n                changeHelper.AddOriginalElement(originalIndex, modifiedIndex + 1);\n                diagonalRelative = (diagonal - 1) - diagonalForwardBase; //Setup for the next iteration\n            }\n            if (historyIndex >= 0) {\n                forwardPoints = this.m_forwardHistory[historyIndex];\n                diagonalForwardBase = forwardPoints[0]; //We stored this in the first spot\n                diagonalMin = 1;\n                diagonalMax = forwardPoints.length - 1;\n            }\n        } while (--historyIndex >= -1);\n        // Ironically, we get the forward changes as the reverse of the\n        // order we added them since we technically added them backwards\n        forwardChanges = changeHelper.getReverseChanges();\n        if (quitEarlyArr[0]) {\n            // TODO: Calculate a partial from the reverse diagonals.\n            //       For now, just assume everything after the midOriginal/midModified point is a diff\n            let originalStartPoint = midOriginalArr[0] + 1;\n            let modifiedStartPoint = midModifiedArr[0] + 1;\n            if (forwardChanges !== null && forwardChanges.length > 0) {\n                const lastForwardChange = forwardChanges[forwardChanges.length - 1];\n                originalStartPoint = Math.max(originalStartPoint, lastForwardChange.getOriginalEnd());\n                modifiedStartPoint = Math.max(modifiedStartPoint, lastForwardChange.getModifiedEnd());\n            }\n            reverseChanges = [\n                new DiffChange(originalStartPoint, originalEnd - originalStartPoint + 1, modifiedStartPoint, modifiedEnd - modifiedStartPoint + 1)\n            ];\n        }\n        else {\n            // Now walk backward through the reverse diagonals history\n            changeHelper = new DiffChangeHelper();\n            diagonalMin = diagonalReverseStart;\n            diagonalMax = diagonalReverseEnd;\n            diagonalRelative = (midOriginalArr[0] - midModifiedArr[0]) - diagonalReverseOffset;\n            lastOriginalIndex = 1073741824 /* Constants.MAX_SAFE_SMALL_INTEGER */;\n            historyIndex = (deltaIsEven) ? this.m_reverseHistory.length - 1 : this.m_reverseHistory.length - 2;\n            do {\n                // Get the diagonal index from the relative diagonal number\n                const diagonal = diagonalRelative + diagonalReverseBase;\n                // Figure out where we came from\n                if (diagonal === diagonalMin || (diagonal < diagonalMax && reversePoints[diagonal - 1] >= reversePoints[diagonal + 1])) {\n                    // Horizontal line (the element is a deletion))\n                    originalIndex = reversePoints[diagonal + 1] - 1;\n                    modifiedIndex = originalIndex - diagonalRelative - diagonalReverseOffset;\n                    if (originalIndex > lastOriginalIndex) {\n                        changeHelper.MarkNextChange();\n                    }\n                    lastOriginalIndex = originalIndex + 1;\n                    changeHelper.AddOriginalElement(originalIndex + 1, modifiedIndex + 1);\n                    diagonalRelative = (diagonal + 1) - diagonalReverseBase; //Setup for the next iteration\n                }\n                else {\n                    // Vertical line (the element is an insertion)\n                    originalIndex = reversePoints[diagonal - 1];\n                    modifiedIndex = originalIndex - diagonalRelative - diagonalReverseOffset;\n                    if (originalIndex > lastOriginalIndex) {\n                        changeHelper.MarkNextChange();\n                    }\n                    lastOriginalIndex = originalIndex;\n                    changeHelper.AddModifiedElement(originalIndex + 1, modifiedIndex + 1);\n                    diagonalRelative = (diagonal - 1) - diagonalReverseBase; //Setup for the next iteration\n                }\n                if (historyIndex >= 0) {\n                    reversePoints = this.m_reverseHistory[historyIndex];\n                    diagonalReverseBase = reversePoints[0]; //We stored this in the first spot\n                    diagonalMin = 1;\n                    diagonalMax = reversePoints.length - 1;\n                }\n            } while (--historyIndex >= -1);\n            // There are cases where the reverse history will find diffs that\n            // are correct, but not intuitive, so we need shift them.\n            reverseChanges = changeHelper.getChanges();\n        }\n        return this.ConcatenateChanges(forwardChanges, reverseChanges);\n    }\n    /**\n     * Given the range to compute the diff on, this method finds the point:\n     * (midOriginal, midModified)\n     * that exists in the middle of the LCS of the two sequences and\n     * is the point at which the LCS problem may be broken down recursively.\n     * This method will try to keep the LCS trace in memory. If the LCS recursion\n     * point is calculated and the full trace is available in memory, then this method\n     * will return the change list.\n     * @param originalStart The start bound of the original sequence range\n     * @param originalEnd The end bound of the original sequence range\n     * @param modifiedStart The start bound of the modified sequence range\n     * @param modifiedEnd The end bound of the modified sequence range\n     * @param midOriginal The middle point of the original sequence range\n     * @param midModified The middle point of the modified sequence range\n     * @returns The diff changes, if available, otherwise null\n     */\n    ComputeRecursionPoint(originalStart, originalEnd, modifiedStart, modifiedEnd, midOriginalArr, midModifiedArr, quitEarlyArr) {\n        let originalIndex = 0, modifiedIndex = 0;\n        let diagonalForwardStart = 0, diagonalForwardEnd = 0;\n        let diagonalReverseStart = 0, diagonalReverseEnd = 0;\n        // To traverse the edit graph and produce the proper LCS, our actual\n        // start position is just outside the given boundary\n        originalStart--;\n        modifiedStart--;\n        // We set these up to make the compiler happy, but they will\n        // be replaced before we return with the actual recursion point\n        midOriginalArr[0] = 0;\n        midModifiedArr[0] = 0;\n        // Clear out the history\n        this.m_forwardHistory = [];\n        this.m_reverseHistory = [];\n        // Each cell in the two arrays corresponds to a diagonal in the edit graph.\n        // The integer value in the cell represents the originalIndex of the furthest\n        // reaching point found so far that ends in that diagonal.\n        // The modifiedIndex can be computed mathematically from the originalIndex and the diagonal number.\n        const maxDifferences = (originalEnd - originalStart) + (modifiedEnd - modifiedStart);\n        const numDiagonals = maxDifferences + 1;\n        const forwardPoints = new Int32Array(numDiagonals);\n        const reversePoints = new Int32Array(numDiagonals);\n        // diagonalForwardBase: Index into forwardPoints of the diagonal which passes through (originalStart, modifiedStart)\n        // diagonalReverseBase: Index into reversePoints of the diagonal which passes through (originalEnd, modifiedEnd)\n        const diagonalForwardBase = (modifiedEnd - modifiedStart);\n        const diagonalReverseBase = (originalEnd - originalStart);\n        // diagonalForwardOffset: Geometric offset which allows modifiedIndex to be computed from originalIndex and the\n        //    diagonal number (relative to diagonalForwardBase)\n        // diagonalReverseOffset: Geometric offset which allows modifiedIndex to be computed from originalIndex and the\n        //    diagonal number (relative to diagonalReverseBase)\n        const diagonalForwardOffset = (originalStart - modifiedStart);\n        const diagonalReverseOffset = (originalEnd - modifiedEnd);\n        // delta: The difference between the end diagonal and the start diagonal. This is used to relate diagonal numbers\n        //   relative to the start diagonal with diagonal numbers relative to the end diagonal.\n        // The Even/Oddn-ness of this delta is important for determining when we should check for overlap\n        const delta = diagonalReverseBase - diagonalForwardBase;\n        const deltaIsEven = (delta % 2 === 0);\n        // Here we set up the start and end points as the furthest points found so far\n        // in both the forward and reverse directions, respectively\n        forwardPoints[diagonalForwardBase] = originalStart;\n        reversePoints[diagonalReverseBase] = originalEnd;\n        // Remember if we quit early, and thus need to do a best-effort result instead of a real result.\n        quitEarlyArr[0] = false;\n        // A couple of points:\n        // --With this method, we iterate on the number of differences between the two sequences.\n        //   The more differences there actually are, the longer this will take.\n        // --Also, as the number of differences increases, we have to search on diagonals further\n        //   away from the reference diagonal (which is diagonalForwardBase for forward, diagonalReverseBase for reverse).\n        // --We extend on even diagonals (relative to the reference diagonal) only when numDifferences\n        //   is even and odd diagonals only when numDifferences is odd.\n        for (let numDifferences = 1; numDifferences <= (maxDifferences / 2) + 1; numDifferences++) {\n            let furthestOriginalIndex = 0;\n            let furthestModifiedIndex = 0;\n            // Run the algorithm in the forward direction\n            diagonalForwardStart = this.ClipDiagonalBound(diagonalForwardBase - numDifferences, numDifferences, diagonalForwardBase, numDiagonals);\n            diagonalForwardEnd = this.ClipDiagonalBound(diagonalForwardBase + numDifferences, numDifferences, diagonalForwardBase, numDiagonals);\n            for (let diagonal = diagonalForwardStart; diagonal <= diagonalForwardEnd; diagonal += 2) {\n                // STEP 1: We extend the furthest reaching point in the present diagonal\n                // by looking at the diagonals above and below and picking the one whose point\n                // is further away from the start point (originalStart, modifiedStart)\n                if (diagonal === diagonalForwardStart || (diagonal < diagonalForwardEnd && forwardPoints[diagonal - 1] < forwardPoints[diagonal + 1])) {\n                    originalIndex = forwardPoints[diagonal + 1];\n                }\n                else {\n                    originalIndex = forwardPoints[diagonal - 1] + 1;\n                }\n                modifiedIndex = originalIndex - (diagonal - diagonalForwardBase) - diagonalForwardOffset;\n                // Save the current originalIndex so we can test for false overlap in step 3\n                const tempOriginalIndex = originalIndex;\n                // STEP 2: We can continue to extend the furthest reaching point in the present diagonal\n                // so long as the elements are equal.\n                while (originalIndex < originalEnd && modifiedIndex < modifiedEnd && this.ElementsAreEqual(originalIndex + 1, modifiedIndex + 1)) {\n                    originalIndex++;\n                    modifiedIndex++;\n                }\n                forwardPoints[diagonal] = originalIndex;\n                if (originalIndex + modifiedIndex > furthestOriginalIndex + furthestModifiedIndex) {\n                    furthestOriginalIndex = originalIndex;\n                    furthestModifiedIndex = modifiedIndex;\n                }\n                // STEP 3: If delta is odd (overlap first happens on forward when delta is odd)\n                // and diagonal is in the range of reverse diagonals computed for numDifferences-1\n                // (the previous iteration; we haven't computed reverse diagonals for numDifferences yet)\n                // then check for overlap.\n                if (!deltaIsEven && Math.abs(diagonal - diagonalReverseBase) <= (numDifferences - 1)) {\n                    if (originalIndex >= reversePoints[diagonal]) {\n                        midOriginalArr[0] = originalIndex;\n                        midModifiedArr[0] = modifiedIndex;\n                        if (tempOriginalIndex <= reversePoints[diagonal] && 1447 /* LocalConstants.MaxDifferencesHistory */ > 0 && numDifferences <= (1447 /* LocalConstants.MaxDifferencesHistory */ + 1)) {\n                            // BINGO! We overlapped, and we have the full trace in memory!\n                            return this.WALKTRACE(diagonalForwardBase, diagonalForwardStart, diagonalForwardEnd, diagonalForwardOffset, diagonalReverseBase, diagonalReverseStart, diagonalReverseEnd, diagonalReverseOffset, forwardPoints, reversePoints, originalIndex, originalEnd, midOriginalArr, modifiedIndex, modifiedEnd, midModifiedArr, deltaIsEven, quitEarlyArr);\n                        }\n                        else {\n                            // Either false overlap, or we didn't have enough memory for the full trace\n                            // Just return the recursion point\n                            return null;\n                        }\n                    }\n                }\n            }\n            // Check to see if we should be quitting early, before moving on to the next iteration.\n            const matchLengthOfLongest = ((furthestOriginalIndex - originalStart) + (furthestModifiedIndex - modifiedStart) - numDifferences) / 2;\n            if (this.ContinueProcessingPredicate !== null && !this.ContinueProcessingPredicate(furthestOriginalIndex, matchLengthOfLongest)) {\n                // We can't finish, so skip ahead to generating a result from what we have.\n                quitEarlyArr[0] = true;\n                // Use the furthest distance we got in the forward direction.\n                midOriginalArr[0] = furthestOriginalIndex;\n                midModifiedArr[0] = furthestModifiedIndex;\n                if (matchLengthOfLongest > 0 && 1447 /* LocalConstants.MaxDifferencesHistory */ > 0 && numDifferences <= (1447 /* LocalConstants.MaxDifferencesHistory */ + 1)) {\n                    // Enough of the history is in memory to walk it backwards\n                    return this.WALKTRACE(diagonalForwardBase, diagonalForwardStart, diagonalForwardEnd, diagonalForwardOffset, diagonalReverseBase, diagonalReverseStart, diagonalReverseEnd, diagonalReverseOffset, forwardPoints, reversePoints, originalIndex, originalEnd, midOriginalArr, modifiedIndex, modifiedEnd, midModifiedArr, deltaIsEven, quitEarlyArr);\n                }\n                else {\n                    // We didn't actually remember enough of the history.\n                    //Since we are quitting the diff early, we need to shift back the originalStart and modified start\n                    //back into the boundary limits since we decremented their value above beyond the boundary limit.\n                    originalStart++;\n                    modifiedStart++;\n                    return [\n                        new DiffChange(originalStart, originalEnd - originalStart + 1, modifiedStart, modifiedEnd - modifiedStart + 1)\n                    ];\n                }\n            }\n            // Run the algorithm in the reverse direction\n            diagonalReverseStart = this.ClipDiagonalBound(diagonalReverseBase - numDifferences, numDifferences, diagonalReverseBase, numDiagonals);\n            diagonalReverseEnd = this.ClipDiagonalBound(diagonalReverseBase + numDifferences, numDifferences, diagonalReverseBase, numDiagonals);\n            for (let diagonal = diagonalReverseStart; diagonal <= diagonalReverseEnd; diagonal += 2) {\n                // STEP 1: We extend the furthest reaching point in the present diagonal\n                // by looking at the diagonals above and below and picking the one whose point\n                // is further away from the start point (originalEnd, modifiedEnd)\n                if (diagonal === diagonalReverseStart || (diagonal < diagonalReverseEnd && reversePoints[diagonal - 1] >= reversePoints[diagonal + 1])) {\n                    originalIndex = reversePoints[diagonal + 1] - 1;\n                }\n                else {\n                    originalIndex = reversePoints[diagonal - 1];\n                }\n                modifiedIndex = originalIndex - (diagonal - diagonalReverseBase) - diagonalReverseOffset;\n                // Save the current originalIndex so we can test for false overlap\n                const tempOriginalIndex = originalIndex;\n                // STEP 2: We can continue to extend the furthest reaching point in the present diagonal\n                // as long as the elements are equal.\n                while (originalIndex > originalStart && modifiedIndex > modifiedStart && this.ElementsAreEqual(originalIndex, modifiedIndex)) {\n                    originalIndex--;\n                    modifiedIndex--;\n                }\n                reversePoints[diagonal] = originalIndex;\n                // STEP 4: If delta is even (overlap first happens on reverse when delta is even)\n                // and diagonal is in the range of forward diagonals computed for numDifferences\n                // then check for overlap.\n                if (deltaIsEven && Math.abs(diagonal - diagonalForwardBase) <= numDifferences) {\n                    if (originalIndex <= forwardPoints[diagonal]) {\n                        midOriginalArr[0] = originalIndex;\n                        midModifiedArr[0] = modifiedIndex;\n                        if (tempOriginalIndex >= forwardPoints[diagonal] && 1447 /* LocalConstants.MaxDifferencesHistory */ > 0 && numDifferences <= (1447 /* LocalConstants.MaxDifferencesHistory */ + 1)) {\n                            // BINGO! We overlapped, and we have the full trace in memory!\n                            return this.WALKTRACE(diagonalForwardBase, diagonalForwardStart, diagonalForwardEnd, diagonalForwardOffset, diagonalReverseBase, diagonalReverseStart, diagonalReverseEnd, diagonalReverseOffset, forwardPoints, reversePoints, originalIndex, originalEnd, midOriginalArr, modifiedIndex, modifiedEnd, midModifiedArr, deltaIsEven, quitEarlyArr);\n                        }\n                        else {\n                            // Either false overlap, or we didn't have enough memory for the full trace\n                            // Just return the recursion point\n                            return null;\n                        }\n                    }\n                }\n            }\n            // Save current vectors to history before the next iteration\n            if (numDifferences <= 1447 /* LocalConstants.MaxDifferencesHistory */) {\n                // We are allocating space for one extra int, which we fill with\n                // the index of the diagonal base index\n                let temp = new Int32Array(diagonalForwardEnd - diagonalForwardStart + 2);\n                temp[0] = diagonalForwardBase - diagonalForwardStart + 1;\n                MyArray.Copy2(forwardPoints, diagonalForwardStart, temp, 1, diagonalForwardEnd - diagonalForwardStart + 1);\n                this.m_forwardHistory.push(temp);\n                temp = new Int32Array(diagonalReverseEnd - diagonalReverseStart + 2);\n                temp[0] = diagonalReverseBase - diagonalReverseStart + 1;\n                MyArray.Copy2(reversePoints, diagonalReverseStart, temp, 1, diagonalReverseEnd - diagonalReverseStart + 1);\n                this.m_reverseHistory.push(temp);\n            }\n        }\n        // If we got here, then we have the full trace in history. We just have to convert it to a change list\n        // NOTE: This part is a bit messy\n        return this.WALKTRACE(diagonalForwardBase, diagonalForwardStart, diagonalForwardEnd, diagonalForwardOffset, diagonalReverseBase, diagonalReverseStart, diagonalReverseEnd, diagonalReverseOffset, forwardPoints, reversePoints, originalIndex, originalEnd, midOriginalArr, modifiedIndex, modifiedEnd, midModifiedArr, deltaIsEven, quitEarlyArr);\n    }\n    /**\n     * Shifts the given changes to provide a more intuitive diff.\n     * While the first element in a diff matches the first element after the diff,\n     * we shift the diff down.\n     *\n     * @param changes The list of changes to shift\n     * @returns The shifted changes\n     */\n    PrettifyChanges(changes) {\n        // Shift all the changes down first\n        for (let i = 0; i < changes.length; i++) {\n            const change = changes[i];\n            const originalStop = (i < changes.length - 1) ? changes[i + 1].originalStart : this._originalElementsOrHash.length;\n            const modifiedStop = (i < changes.length - 1) ? changes[i + 1].modifiedStart : this._modifiedElementsOrHash.length;\n            const checkOriginal = change.originalLength > 0;\n            const checkModified = change.modifiedLength > 0;\n            while (change.originalStart + change.originalLength < originalStop\n                && change.modifiedStart + change.modifiedLength < modifiedStop\n                && (!checkOriginal || this.OriginalElementsAreEqual(change.originalStart, change.originalStart + change.originalLength))\n                && (!checkModified || this.ModifiedElementsAreEqual(change.modifiedStart, change.modifiedStart + change.modifiedLength))) {\n                const startStrictEqual = this.ElementsAreStrictEqual(change.originalStart, change.modifiedStart);\n                const endStrictEqual = this.ElementsAreStrictEqual(change.originalStart + change.originalLength, change.modifiedStart + change.modifiedLength);\n                if (endStrictEqual && !startStrictEqual) {\n                    // moving the change down would create an equal change, but the elements are not strict equal\n                    break;\n                }\n                change.originalStart++;\n                change.modifiedStart++;\n            }\n            const mergedChangeArr = [null];\n            if (i < changes.length - 1 && this.ChangesOverlap(changes[i], changes[i + 1], mergedChangeArr)) {\n                changes[i] = mergedChangeArr[0];\n                changes.splice(i + 1, 1);\n                i--;\n                continue;\n            }\n        }\n        // Shift changes back up until we hit empty or whitespace-only lines\n        for (let i = changes.length - 1; i >= 0; i--) {\n            const change = changes[i];\n            let originalStop = 0;\n            let modifiedStop = 0;\n            if (i > 0) {\n                const prevChange = changes[i - 1];\n                originalStop = prevChange.originalStart + prevChange.originalLength;\n                modifiedStop = prevChange.modifiedStart + prevChange.modifiedLength;\n            }\n            const checkOriginal = change.originalLength > 0;\n            const checkModified = change.modifiedLength > 0;\n            let bestDelta = 0;\n            let bestScore = this._boundaryScore(change.originalStart, change.originalLength, change.modifiedStart, change.modifiedLength);\n            for (let delta = 1;; delta++) {\n                const originalStart = change.originalStart - delta;\n                const modifiedStart = change.modifiedStart - delta;\n                if (originalStart < originalStop || modifiedStart < modifiedStop) {\n                    break;\n                }\n                if (checkOriginal && !this.OriginalElementsAreEqual(originalStart, originalStart + change.originalLength)) {\n                    break;\n                }\n                if (checkModified && !this.ModifiedElementsAreEqual(modifiedStart, modifiedStart + change.modifiedLength)) {\n                    break;\n                }\n                const touchingPreviousChange = (originalStart === originalStop && modifiedStart === modifiedStop);\n                const score = ((touchingPreviousChange ? 5 : 0)\n                    + this._boundaryScore(originalStart, change.originalLength, modifiedStart, change.modifiedLength));\n                if (score > bestScore) {\n                    bestScore = score;\n                    bestDelta = delta;\n                }\n            }\n            change.originalStart -= bestDelta;\n            change.modifiedStart -= bestDelta;\n            const mergedChangeArr = [null];\n            if (i > 0 && this.ChangesOverlap(changes[i - 1], changes[i], mergedChangeArr)) {\n                changes[i - 1] = mergedChangeArr[0];\n                changes.splice(i, 1);\n                i++;\n                continue;\n            }\n        }\n        // There could be multiple longest common substrings.\n        // Give preference to the ones containing longer lines\n        if (this._hasStrings) {\n            for (let i = 1, len = changes.length; i < len; i++) {\n                const aChange = changes[i - 1];\n                const bChange = changes[i];\n                const matchedLength = bChange.originalStart - aChange.originalStart - aChange.originalLength;\n                const aOriginalStart = aChange.originalStart;\n                const bOriginalEnd = bChange.originalStart + bChange.originalLength;\n                const abOriginalLength = bOriginalEnd - aOriginalStart;\n                const aModifiedStart = aChange.modifiedStart;\n                const bModifiedEnd = bChange.modifiedStart + bChange.modifiedLength;\n                const abModifiedLength = bModifiedEnd - aModifiedStart;\n                // Avoid wasting a lot of time with these searches\n                if (matchedLength < 5 && abOriginalLength < 20 && abModifiedLength < 20) {\n                    const t = this._findBetterContiguousSequence(aOriginalStart, abOriginalLength, aModifiedStart, abModifiedLength, matchedLength);\n                    if (t) {\n                        const [originalMatchStart, modifiedMatchStart] = t;\n                        if (originalMatchStart !== aChange.originalStart + aChange.originalLength || modifiedMatchStart !== aChange.modifiedStart + aChange.modifiedLength) {\n                            // switch to another sequence that has a better score\n                            aChange.originalLength = originalMatchStart - aChange.originalStart;\n                            aChange.modifiedLength = modifiedMatchStart - aChange.modifiedStart;\n                            bChange.originalStart = originalMatchStart + matchedLength;\n                            bChange.modifiedStart = modifiedMatchStart + matchedLength;\n                            bChange.originalLength = bOriginalEnd - bChange.originalStart;\n                            bChange.modifiedLength = bModifiedEnd - bChange.modifiedStart;\n                        }\n                    }\n                }\n            }\n        }\n        return changes;\n    }\n    _findBetterContiguousSequence(originalStart, originalLength, modifiedStart, modifiedLength, desiredLength) {\n        if (originalLength < desiredLength || modifiedLength < desiredLength) {\n            return null;\n        }\n        const originalMax = originalStart + originalLength - desiredLength + 1;\n        const modifiedMax = modifiedStart + modifiedLength - desiredLength + 1;\n        let bestScore = 0;\n        let bestOriginalStart = 0;\n        let bestModifiedStart = 0;\n        for (let i = originalStart; i < originalMax; i++) {\n            for (let j = modifiedStart; j < modifiedMax; j++) {\n                const score = this._contiguousSequenceScore(i, j, desiredLength);\n                if (score > 0 && score > bestScore) {\n                    bestScore = score;\n                    bestOriginalStart = i;\n                    bestModifiedStart = j;\n                }\n            }\n        }\n        if (bestScore > 0) {\n            return [bestOriginalStart, bestModifiedStart];\n        }\n        return null;\n    }\n    _contiguousSequenceScore(originalStart, modifiedStart, length) {\n        let score = 0;\n        for (let l = 0; l < length; l++) {\n            if (!this.ElementsAreEqual(originalStart + l, modifiedStart + l)) {\n                return 0;\n            }\n            score += this._originalStringElements[originalStart + l].length;\n        }\n        return score;\n    }\n    _OriginalIsBoundary(index) {\n        if (index <= 0 || index >= this._originalElementsOrHash.length - 1) {\n            return true;\n        }\n        return (this._hasStrings && /^\\s*$/.test(this._originalStringElements[index]));\n    }\n    _OriginalRegionIsBoundary(originalStart, originalLength) {\n        if (this._OriginalIsBoundary(originalStart) || this._OriginalIsBoundary(originalStart - 1)) {\n            return true;\n        }\n        if (originalLength > 0) {\n            const originalEnd = originalStart + originalLength;\n            if (this._OriginalIsBoundary(originalEnd - 1) || this._OriginalIsBoundary(originalEnd)) {\n                return true;\n            }\n        }\n        return false;\n    }\n    _ModifiedIsBoundary(index) {\n        if (index <= 0 || index >= this._modifiedElementsOrHash.length - 1) {\n            return true;\n        }\n        return (this._hasStrings && /^\\s*$/.test(this._modifiedStringElements[index]));\n    }\n    _ModifiedRegionIsBoundary(modifiedStart, modifiedLength) {\n        if (this._ModifiedIsBoundary(modifiedStart) || this._ModifiedIsBoundary(modifiedStart - 1)) {\n            return true;\n        }\n        if (modifiedLength > 0) {\n            const modifiedEnd = modifiedStart + modifiedLength;\n            if (this._ModifiedIsBoundary(modifiedEnd - 1) || this._ModifiedIsBoundary(modifiedEnd)) {\n                return true;\n            }\n        }\n        return false;\n    }\n    _boundaryScore(originalStart, originalLength, modifiedStart, modifiedLength) {\n        const originalScore = (this._OriginalRegionIsBoundary(originalStart, originalLength) ? 1 : 0);\n        const modifiedScore = (this._ModifiedRegionIsBoundary(modifiedStart, modifiedLength) ? 1 : 0);\n        return (originalScore + modifiedScore);\n    }\n    /**\n     * Concatenates the two input DiffChange lists and returns the resulting\n     * list.\n     * @param The left changes\n     * @param The right changes\n     * @returns The concatenated list\n     */\n    ConcatenateChanges(left, right) {\n        const mergedChangeArr = [];\n        if (left.length === 0 || right.length === 0) {\n            return (right.length > 0) ? right : left;\n        }\n        else if (this.ChangesOverlap(left[left.length - 1], right[0], mergedChangeArr)) {\n            // Since we break the problem down recursively, it is possible that we\n            // might recurse in the middle of a change thereby splitting it into\n            // two changes. Here in the combining stage, we detect and fuse those\n            // changes back together\n            const result = new Array(left.length + right.length - 1);\n            MyArray.Copy(left, 0, result, 0, left.length - 1);\n            result[left.length - 1] = mergedChangeArr[0];\n            MyArray.Copy(right, 1, result, left.length, right.length - 1);\n            return result;\n        }\n        else {\n            const result = new Array(left.length + right.length);\n            MyArray.Copy(left, 0, result, 0, left.length);\n            MyArray.Copy(right, 0, result, left.length, right.length);\n            return result;\n        }\n    }\n    /**\n     * Returns true if the two changes overlap and can be merged into a single\n     * change\n     * @param left The left change\n     * @param right The right change\n     * @param mergedChange The merged change if the two overlap, null otherwise\n     * @returns True if the two changes overlap\n     */\n    ChangesOverlap(left, right, mergedChangeArr) {\n        Debug.Assert(left.originalStart <= right.originalStart, 'Left change is not less than or equal to right change');\n        Debug.Assert(left.modifiedStart <= right.modifiedStart, 'Left change is not less than or equal to right change');\n        if (left.originalStart + left.originalLength >= right.originalStart || left.modifiedStart + left.modifiedLength >= right.modifiedStart) {\n            const originalStart = left.originalStart;\n            let originalLength = left.originalLength;\n            const modifiedStart = left.modifiedStart;\n            let modifiedLength = left.modifiedLength;\n            if (left.originalStart + left.originalLength >= right.originalStart) {\n                originalLength = right.originalStart + right.originalLength - left.originalStart;\n            }\n            if (left.modifiedStart + left.modifiedLength >= right.modifiedStart) {\n                modifiedLength = right.modifiedStart + right.modifiedLength - left.modifiedStart;\n            }\n            mergedChangeArr[0] = new DiffChange(originalStart, originalLength, modifiedStart, modifiedLength);\n            return true;\n        }\n        else {\n            mergedChangeArr[0] = null;\n            return false;\n        }\n    }\n    /**\n     * Helper method used to clip a diagonal index to the range of valid\n     * diagonals. This also decides whether or not the diagonal index,\n     * if it exceeds the boundary, should be clipped to the boundary or clipped\n     * one inside the boundary depending on the Even/Odd status of the boundary\n     * and numDifferences.\n     * @param diagonal The index of the diagonal to clip.\n     * @param numDifferences The current number of differences being iterated upon.\n     * @param diagonalBaseIndex The base reference diagonal.\n     * @param numDiagonals The total number of diagonals.\n     * @returns The clipped diagonal index.\n     */\n    ClipDiagonalBound(diagonal, numDifferences, diagonalBaseIndex, numDiagonals) {\n        if (diagonal >= 0 && diagonal < numDiagonals) {\n            // Nothing to clip, its in range\n            return diagonal;\n        }\n        // diagonalsBelow: The number of diagonals below the reference diagonal\n        // diagonalsAbove: The number of diagonals above the reference diagonal\n        const diagonalsBelow = diagonalBaseIndex;\n        const diagonalsAbove = numDiagonals - diagonalBaseIndex - 1;\n        const diffEven = (numDifferences % 2 === 0);\n        if (diagonal < 0) {\n            const lowerBoundEven = (diagonalsBelow % 2 === 0);\n            return (diffEven === lowerBoundEven) ? 0 : 1;\n        }\n        else {\n            const upperBoundEven = (diagonalsAbove % 2 === 0);\n            return (diffEven === upperBoundEven) ? numDiagonals - 1 : numDiagonals - 2;\n        }\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,UAAU,QAAQ,iBAAiB;AAC5C,SAASC,UAAU,QAAQ,YAAY;AACvC,OAAO,MAAMC,kBAAkB,CAAC;EAC5BC,WAAWA,CAACC,MAAM,EAAE;IAChB,IAAI,CAACA,MAAM,GAAGA,MAAM;EACxB;EACAC,WAAWA,CAAA,EAAG;IACV,MAAMD,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAME,UAAU,GAAG,IAAIC,UAAU,CAACH,MAAM,CAACI,MAAM,CAAC;IAChD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGN,MAAM,CAACI,MAAM,EAAEC,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;MAC/CH,UAAU,CAACG,CAAC,CAAC,GAAGL,MAAM,CAACO,UAAU,CAACF,CAAC,CAAC;IACxC;IACA,OAAOH,UAAU;EACrB;AACJ;AACA,OAAO,SAASM,UAAUA,CAACC,QAAQ,EAAEC,QAAQ,EAAEC,MAAM,EAAE;EACnD,OAAO,IAAIC,OAAO,CAAC,IAAId,kBAAkB,CAACW,QAAQ,CAAC,EAAE,IAAIX,kBAAkB,CAACY,QAAQ,CAAC,CAAC,CAACG,WAAW,CAACF,MAAM,CAAC,CAACG,OAAO;AACtH;AACA;AACA;AACA;AACA,MAAMC,KAAK,CAAC;EACR,OAAOC,MAAMA,CAACC,SAAS,EAAEC,OAAO,EAAE;IAC9B,IAAI,CAACD,SAAS,EAAE;MACZ,MAAM,IAAIE,KAAK,CAACD,OAAO,CAAC;IAC5B;EACJ;AACJ;AACA,MAAME,OAAO,CAAC;EACV;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOC,IAAIA,CAACC,WAAW,EAAEC,WAAW,EAAEC,gBAAgB,EAAEC,gBAAgB,EAAErB,MAAM,EAAE;IAC9E,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,MAAM,EAAEC,CAAC,EAAE,EAAE;MAC7BmB,gBAAgB,CAACC,gBAAgB,GAAGpB,CAAC,CAAC,GAAGiB,WAAW,CAACC,WAAW,GAAGlB,CAAC,CAAC;IACzE;EACJ;EACA,OAAOqB,KAAKA,CAACJ,WAAW,EAAEC,WAAW,EAAEC,gBAAgB,EAAEC,gBAAgB,EAAErB,MAAM,EAAE;IAC/E,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,MAAM,EAAEC,CAAC,EAAE,EAAE;MAC7BmB,gBAAgB,CAACC,gBAAgB,GAAGpB,CAAC,CAAC,GAAGiB,WAAW,CAACC,WAAW,GAAGlB,CAAC,CAAC;IACzE;EACJ;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMsB,gBAAgB,CAAC;EACnB;AACJ;AACA;EACI5B,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC6B,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,eAAe,GAAG,UAAU,CAAC;IAClC,IAAI,CAACC,eAAe,GAAG,UAAU,CAAC;IAClC,IAAI,CAACC,eAAe,GAAG,CAAC;IACxB,IAAI,CAACC,eAAe,GAAG,CAAC;EAC5B;EACA;AACJ;AACA;EACIC,cAAcA,CAAA,EAAG;IACb;IACA,IAAI,IAAI,CAACF,eAAe,GAAG,CAAC,IAAI,IAAI,CAACC,eAAe,GAAG,CAAC,EAAE;MACtD;MACA,IAAI,CAACJ,SAAS,CAACM,IAAI,CAAC,IAAItC,UAAU,CAAC,IAAI,CAACiC,eAAe,EAAE,IAAI,CAACE,eAAe,EAAE,IAAI,CAACD,eAAe,EAAE,IAAI,CAACE,eAAe,CAAC,CAAC;IAC/H;IACA;IACA,IAAI,CAACD,eAAe,GAAG,CAAC;IACxB,IAAI,CAACC,eAAe,GAAG,CAAC;IACxB,IAAI,CAACH,eAAe,GAAG,UAAU,CAAC;IAClC,IAAI,CAACC,eAAe,GAAG,UAAU,CAAC;EACtC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIK,kBAAkBA,CAACC,aAAa,EAAEC,aAAa,EAAE;IAC7C;IACA,IAAI,CAACR,eAAe,GAAGS,IAAI,CAACC,GAAG,CAAC,IAAI,CAACV,eAAe,EAAEO,aAAa,CAAC;IACpE,IAAI,CAACN,eAAe,GAAGQ,IAAI,CAACC,GAAG,CAAC,IAAI,CAACT,eAAe,EAAEO,aAAa,CAAC;IACpE,IAAI,CAACN,eAAe,EAAE;EAC1B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIS,kBAAkBA,CAACJ,aAAa,EAAEC,aAAa,EAAE;IAC7C;IACA,IAAI,CAACR,eAAe,GAAGS,IAAI,CAACC,GAAG,CAAC,IAAI,CAACV,eAAe,EAAEO,aAAa,CAAC;IACpE,IAAI,CAACN,eAAe,GAAGQ,IAAI,CAACC,GAAG,CAAC,IAAI,CAACT,eAAe,EAAEO,aAAa,CAAC;IACpE,IAAI,CAACL,eAAe,EAAE;EAC1B;EACA;AACJ;AACA;EACIS,UAAUA,CAAA,EAAG;IACT,IAAI,IAAI,CAACV,eAAe,GAAG,CAAC,IAAI,IAAI,CAACC,eAAe,GAAG,CAAC,EAAE;MACtD;MACA,IAAI,CAACC,cAAc,CAAC,CAAC;IACzB;IACA,OAAO,IAAI,CAACL,SAAS;EACzB;EACA;AACJ;AACA;EACIc,iBAAiBA,CAAA,EAAG;IAChB,IAAI,IAAI,CAACX,eAAe,GAAG,CAAC,IAAI,IAAI,CAACC,eAAe,GAAG,CAAC,EAAE;MACtD;MACA,IAAI,CAACC,cAAc,CAAC,CAAC;IACzB;IACA,IAAI,CAACL,SAAS,CAACe,OAAO,CAAC,CAAC;IACxB,OAAO,IAAI,CAACf,SAAS;EACzB;AACJ;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMhB,OAAO,CAAC;EACjB;AACJ;AACA;EACIb,WAAWA,CAAC6C,gBAAgB,EAAEC,gBAAgB,EAAEC,2BAA2B,GAAG,IAAI,EAAE;IAChF,IAAI,CAACC,2BAA2B,GAAGD,2BAA2B;IAC9D,IAAI,CAACE,iBAAiB,GAAGJ,gBAAgB;IACzC,IAAI,CAACK,iBAAiB,GAAGJ,gBAAgB;IACzC,MAAM,CAACK,sBAAsB,EAAEC,sBAAsB,EAAEC,kBAAkB,CAAC,GAAGxC,OAAO,CAACyC,YAAY,CAACT,gBAAgB,CAAC;IACnH,MAAM,CAACU,sBAAsB,EAAEC,sBAAsB,EAAEC,kBAAkB,CAAC,GAAG5C,OAAO,CAACyC,YAAY,CAACR,gBAAgB,CAAC;IACnH,IAAI,CAACY,WAAW,GAAIL,kBAAkB,IAAII,kBAAmB;IAC7D,IAAI,CAACE,uBAAuB,GAAGR,sBAAsB;IACrD,IAAI,CAACS,uBAAuB,GAAGR,sBAAsB;IACrD,IAAI,CAACS,uBAAuB,GAAGN,sBAAsB;IACrD,IAAI,CAACO,uBAAuB,GAAGN,sBAAsB;IACrD,IAAI,CAACO,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACC,gBAAgB,GAAG,EAAE;EAC9B;EACA,OAAOC,cAAcA,CAACC,GAAG,EAAE;IACvB,OAAQA,GAAG,CAAC7D,MAAM,GAAG,CAAC,IAAI,OAAO6D,GAAG,CAAC,CAAC,CAAC,KAAK,QAAQ;EACxD;EACA,OAAOZ,YAAYA,CAACa,QAAQ,EAAE;IAC1B,MAAMC,QAAQ,GAAGD,QAAQ,CAACjE,WAAW,CAAC,CAAC;IACvC,IAAIW,OAAO,CAACoD,cAAc,CAACG,QAAQ,CAAC,EAAE;MAClC,MAAMC,MAAM,GAAG,IAAIjE,UAAU,CAACgE,QAAQ,CAAC/D,MAAM,CAAC;MAC9C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAG6D,QAAQ,CAAC/D,MAAM,EAAEC,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;QACjD+D,MAAM,CAAC/D,CAAC,CAAC,GAAGR,UAAU,CAACsE,QAAQ,CAAC9D,CAAC,CAAC,EAAE,CAAC,CAAC;MAC1C;MACA,OAAO,CAAC8D,QAAQ,EAAEC,MAAM,EAAE,IAAI,CAAC;IACnC;IACA,IAAID,QAAQ,YAAYhE,UAAU,EAAE;MAChC,OAAO,CAAC,EAAE,EAAEgE,QAAQ,EAAE,KAAK,CAAC;IAChC;IACA,OAAO,CAAC,EAAE,EAAE,IAAIhE,UAAU,CAACgE,QAAQ,CAAC,EAAE,KAAK,CAAC;EAChD;EACAE,gBAAgBA,CAACjC,aAAa,EAAEkC,QAAQ,EAAE;IACtC,IAAI,IAAI,CAACX,uBAAuB,CAACvB,aAAa,CAAC,KAAK,IAAI,CAACyB,uBAAuB,CAACS,QAAQ,CAAC,EAAE;MACxF,OAAO,KAAK;IAChB;IACA,OAAQ,IAAI,CAACb,WAAW,GAAG,IAAI,CAACC,uBAAuB,CAACtB,aAAa,CAAC,KAAK,IAAI,CAACwB,uBAAuB,CAACU,QAAQ,CAAC,GAAG,IAAI;EAC5H;EACAC,sBAAsBA,CAACnC,aAAa,EAAEkC,QAAQ,EAAE;IAC5C,IAAI,CAAC,IAAI,CAACD,gBAAgB,CAACjC,aAAa,EAAEkC,QAAQ,CAAC,EAAE;MACjD,OAAO,KAAK;IAChB;IACA,MAAME,eAAe,GAAG5D,OAAO,CAAC6D,iBAAiB,CAAC,IAAI,CAACzB,iBAAiB,EAAEZ,aAAa,CAAC;IACxF,MAAMsC,eAAe,GAAG9D,OAAO,CAAC6D,iBAAiB,CAAC,IAAI,CAACxB,iBAAiB,EAAEqB,QAAQ,CAAC;IACnF,OAAQE,eAAe,KAAKE,eAAe;EAC/C;EACA,OAAOD,iBAAiBA,CAACP,QAAQ,EAAES,KAAK,EAAE;IACtC,IAAI,OAAOT,QAAQ,CAACU,gBAAgB,KAAK,UAAU,EAAE;MACjD,OAAOV,QAAQ,CAACU,gBAAgB,CAACD,KAAK,CAAC;IAC3C;IACA,OAAO,IAAI;EACf;EACAE,wBAAwBA,CAACC,MAAM,EAAEC,MAAM,EAAE;IACrC,IAAI,IAAI,CAACpB,uBAAuB,CAACmB,MAAM,CAAC,KAAK,IAAI,CAACnB,uBAAuB,CAACoB,MAAM,CAAC,EAAE;MAC/E,OAAO,KAAK;IAChB;IACA,OAAQ,IAAI,CAACtB,WAAW,GAAG,IAAI,CAACC,uBAAuB,CAACoB,MAAM,CAAC,KAAK,IAAI,CAACpB,uBAAuB,CAACqB,MAAM,CAAC,GAAG,IAAI;EACnH;EACAC,wBAAwBA,CAACF,MAAM,EAAEC,MAAM,EAAE;IACrC,IAAI,IAAI,CAAClB,uBAAuB,CAACiB,MAAM,CAAC,KAAK,IAAI,CAACjB,uBAAuB,CAACkB,MAAM,CAAC,EAAE;MAC/E,OAAO,KAAK;IAChB;IACA,OAAQ,IAAI,CAACtB,WAAW,GAAG,IAAI,CAACG,uBAAuB,CAACkB,MAAM,CAAC,KAAK,IAAI,CAAClB,uBAAuB,CAACmB,MAAM,CAAC,GAAG,IAAI;EACnH;EACAlE,WAAWA,CAACF,MAAM,EAAE;IAChB,OAAO,IAAI,CAACsE,YAAY,CAAC,CAAC,EAAE,IAAI,CAACtB,uBAAuB,CAACvD,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,IAAI,CAACyD,uBAAuB,CAACzD,MAAM,GAAG,CAAC,EAAEO,MAAM,CAAC;EAC5H;EACA;AACJ;AACA;AACA;AACA;EACIsE,YAAYA,CAACC,aAAa,EAAEC,WAAW,EAAEC,aAAa,EAAEC,WAAW,EAAE1E,MAAM,EAAE;IACzE,MAAM2E,YAAY,GAAG,CAAC,KAAK,CAAC;IAC5B,IAAIxE,OAAO,GAAG,IAAI,CAACyE,oBAAoB,CAACL,aAAa,EAAEC,WAAW,EAAEC,aAAa,EAAEC,WAAW,EAAEC,YAAY,CAAC;IAC7G,IAAI3E,MAAM,EAAE;MACR;MACA;MACA;MACAG,OAAO,GAAG,IAAI,CAAC0E,eAAe,CAAC1E,OAAO,CAAC;IAC3C;IACA,OAAO;MACH2E,SAAS,EAAEH,YAAY,CAAC,CAAC,CAAC;MAC1BxE,OAAO,EAAEA;IACb,CAAC;EACL;EACA;AACJ;AACA;AACA;AACA;EACIyE,oBAAoBA,CAACL,aAAa,EAAEC,WAAW,EAAEC,aAAa,EAAEC,WAAW,EAAEC,YAAY,EAAE;IACvFA,YAAY,CAAC,CAAC,CAAC,GAAG,KAAK;IACvB;IACA,OAAOJ,aAAa,IAAIC,WAAW,IAAIC,aAAa,IAAIC,WAAW,IAAI,IAAI,CAAChB,gBAAgB,CAACa,aAAa,EAAEE,aAAa,CAAC,EAAE;MACxHF,aAAa,EAAE;MACfE,aAAa,EAAE;IACnB;IACA;IACA,OAAOD,WAAW,IAAID,aAAa,IAAIG,WAAW,IAAID,aAAa,IAAI,IAAI,CAACf,gBAAgB,CAACc,WAAW,EAAEE,WAAW,CAAC,EAAE;MACpHF,WAAW,EAAE;MACbE,WAAW,EAAE;IACjB;IACA;IACA,IAAIH,aAAa,GAAGC,WAAW,IAAIC,aAAa,GAAGC,WAAW,EAAE;MAC5D,IAAIvE,OAAO;MACX,IAAIsE,aAAa,IAAIC,WAAW,EAAE;QAC9BtE,KAAK,CAACC,MAAM,CAACkE,aAAa,KAAKC,WAAW,GAAG,CAAC,EAAE,wDAAwD,CAAC;QACzG;QACArE,OAAO,GAAG,CACN,IAAIlB,UAAU,CAACsF,aAAa,EAAE,CAAC,EAAEE,aAAa,EAAEC,WAAW,GAAGD,aAAa,GAAG,CAAC,CAAC,CACnF;MACL,CAAC,MACI,IAAIF,aAAa,IAAIC,WAAW,EAAE;QACnCpE,KAAK,CAACC,MAAM,CAACoE,aAAa,KAAKC,WAAW,GAAG,CAAC,EAAE,wDAAwD,CAAC;QACzG;QACAvE,OAAO,GAAG,CACN,IAAIlB,UAAU,CAACsF,aAAa,EAAEC,WAAW,GAAGD,aAAa,GAAG,CAAC,EAAEE,aAAa,EAAE,CAAC,CAAC,CACnF;MACL,CAAC,MACI;QACDrE,KAAK,CAACC,MAAM,CAACkE,aAAa,KAAKC,WAAW,GAAG,CAAC,EAAE,wDAAwD,CAAC;QACzGpE,KAAK,CAACC,MAAM,CAACoE,aAAa,KAAKC,WAAW,GAAG,CAAC,EAAE,wDAAwD,CAAC;QACzG;QACAvE,OAAO,GAAG,EAAE;MAChB;MACA,OAAOA,OAAO;IAClB;IACA;IACA,MAAM4E,cAAc,GAAG,CAAC,CAAC,CAAC;IAC1B,MAAMC,cAAc,GAAG,CAAC,CAAC,CAAC;IAC1B,MAAMC,MAAM,GAAG,IAAI,CAACC,qBAAqB,CAACX,aAAa,EAAEC,WAAW,EAAEC,aAAa,EAAEC,WAAW,EAAEK,cAAc,EAAEC,cAAc,EAAEL,YAAY,CAAC;IAC/I,MAAMQ,WAAW,GAAGJ,cAAc,CAAC,CAAC,CAAC;IACrC,MAAMK,WAAW,GAAGJ,cAAc,CAAC,CAAC,CAAC;IACrC,IAAIC,MAAM,KAAK,IAAI,EAAE;MACjB;MACA;MACA,OAAOA,MAAM;IACjB,CAAC,MACI,IAAI,CAACN,YAAY,CAAC,CAAC,CAAC,EAAE;MACvB;MACA;MACA;MACA;MACA,MAAMU,WAAW,GAAG,IAAI,CAACT,oBAAoB,CAACL,aAAa,EAAEY,WAAW,EAAEV,aAAa,EAAEW,WAAW,EAAET,YAAY,CAAC;MACnH,IAAIW,YAAY,GAAG,EAAE;MACrB,IAAI,CAACX,YAAY,CAAC,CAAC,CAAC,EAAE;QAClBW,YAAY,GAAG,IAAI,CAACV,oBAAoB,CAACO,WAAW,GAAG,CAAC,EAAEX,WAAW,EAAEY,WAAW,GAAG,CAAC,EAAEV,WAAW,EAAEC,YAAY,CAAC;MACtH,CAAC,MACI;QACD;QACA;QACAW,YAAY,GAAG,CACX,IAAIrG,UAAU,CAACkG,WAAW,GAAG,CAAC,EAAEX,WAAW,IAAIW,WAAW,GAAG,CAAC,CAAC,GAAG,CAAC,EAAEC,WAAW,GAAG,CAAC,EAAEV,WAAW,IAAIU,WAAW,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAC7H;MACL;MACA,OAAO,IAAI,CAACG,kBAAkB,CAACF,WAAW,EAAEC,YAAY,CAAC;IAC7D;IACA;IACA,OAAO,CACH,IAAIrG,UAAU,CAACsF,aAAa,EAAEC,WAAW,GAAGD,aAAa,GAAG,CAAC,EAAEE,aAAa,EAAEC,WAAW,GAAGD,aAAa,GAAG,CAAC,CAAC,CACjH;EACL;EACAe,SAASA,CAACC,mBAAmB,EAAEC,oBAAoB,EAAEC,kBAAkB,EAAEC,qBAAqB,EAAEC,mBAAmB,EAAEC,oBAAoB,EAAEC,kBAAkB,EAAEC,qBAAqB,EAAEC,aAAa,EAAEC,aAAa,EAAEzE,aAAa,EAAE+C,WAAW,EAAEO,cAAc,EAAErD,aAAa,EAAEgD,WAAW,EAAEM,cAAc,EAAEmB,WAAW,EAAExB,YAAY,EAAE;IACnU,IAAIyB,cAAc,GAAG,IAAI;IACzB,IAAIC,cAAc,GAAG,IAAI;IACzB;IACA,IAAIC,YAAY,GAAG,IAAItF,gBAAgB,CAAC,CAAC;IACzC,IAAIuF,WAAW,GAAGb,oBAAoB;IACtC,IAAIc,WAAW,GAAGb,kBAAkB;IACpC,IAAIc,gBAAgB,GAAI1B,cAAc,CAAC,CAAC,CAAC,GAAGC,cAAc,CAAC,CAAC,CAAC,GAAIY,qBAAqB;IACtF,IAAIc,iBAAiB,GAAG,CAAC,UAAU,CAAC;IACpC,IAAIC,YAAY,GAAG,IAAI,CAACxD,gBAAgB,CAAC1D,MAAM,GAAG,CAAC;IACnD,GAAG;MACC;MACA,MAAMmH,QAAQ,GAAGH,gBAAgB,GAAGhB,mBAAmB;MACvD;MACA,IAAImB,QAAQ,KAAKL,WAAW,IAAKK,QAAQ,GAAGJ,WAAW,IAAIP,aAAa,CAACW,QAAQ,GAAG,CAAC,CAAC,GAAGX,aAAa,CAACW,QAAQ,GAAG,CAAC,CAAE,EAAE;QACnH;QACAnF,aAAa,GAAGwE,aAAa,CAACW,QAAQ,GAAG,CAAC,CAAC;QAC3ClF,aAAa,GAAGD,aAAa,GAAGgF,gBAAgB,GAAGb,qBAAqB;QACxE,IAAInE,aAAa,GAAGiF,iBAAiB,EAAE;UACnCJ,YAAY,CAAChF,cAAc,CAAC,CAAC;QACjC;QACAoF,iBAAiB,GAAGjF,aAAa;QACjC6E,YAAY,CAACzE,kBAAkB,CAACJ,aAAa,GAAG,CAAC,EAAEC,aAAa,CAAC;QACjE+E,gBAAgB,GAAIG,QAAQ,GAAG,CAAC,GAAInB,mBAAmB,CAAC,CAAC;MAC7D,CAAC,MACI;QACD;QACAhE,aAAa,GAAGwE,aAAa,CAACW,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC;QAC/ClF,aAAa,GAAGD,aAAa,GAAGgF,gBAAgB,GAAGb,qBAAqB;QACxE,IAAInE,aAAa,GAAGiF,iBAAiB,EAAE;UACnCJ,YAAY,CAAChF,cAAc,CAAC,CAAC;QACjC;QACAoF,iBAAiB,GAAGjF,aAAa,GAAG,CAAC;QACrC6E,YAAY,CAAC9E,kBAAkB,CAACC,aAAa,EAAEC,aAAa,GAAG,CAAC,CAAC;QACjE+E,gBAAgB,GAAIG,QAAQ,GAAG,CAAC,GAAInB,mBAAmB,CAAC,CAAC;MAC7D;MACA,IAAIkB,YAAY,IAAI,CAAC,EAAE;QACnBV,aAAa,GAAG,IAAI,CAAC9C,gBAAgB,CAACwD,YAAY,CAAC;QACnDlB,mBAAmB,GAAGQ,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;QACxCM,WAAW,GAAG,CAAC;QACfC,WAAW,GAAGP,aAAa,CAACxG,MAAM,GAAG,CAAC;MAC1C;IACJ,CAAC,QAAQ,EAAEkH,YAAY,IAAI,CAAC,CAAC;IAC7B;IACA;IACAP,cAAc,GAAGE,YAAY,CAACvE,iBAAiB,CAAC,CAAC;IACjD,IAAI4C,YAAY,CAAC,CAAC,CAAC,EAAE;MACjB;MACA;MACA,IAAIkC,kBAAkB,GAAG9B,cAAc,CAAC,CAAC,CAAC,GAAG,CAAC;MAC9C,IAAI+B,kBAAkB,GAAG9B,cAAc,CAAC,CAAC,CAAC,GAAG,CAAC;MAC9C,IAAIoB,cAAc,KAAK,IAAI,IAAIA,cAAc,CAAC3G,MAAM,GAAG,CAAC,EAAE;QACtD,MAAMsH,iBAAiB,GAAGX,cAAc,CAACA,cAAc,CAAC3G,MAAM,GAAG,CAAC,CAAC;QACnEoH,kBAAkB,GAAGlF,IAAI,CAACqF,GAAG,CAACH,kBAAkB,EAAEE,iBAAiB,CAACE,cAAc,CAAC,CAAC,CAAC;QACrFH,kBAAkB,GAAGnF,IAAI,CAACqF,GAAG,CAACF,kBAAkB,EAAEC,iBAAiB,CAACG,cAAc,CAAC,CAAC,CAAC;MACzF;MACAb,cAAc,GAAG,CACb,IAAIpH,UAAU,CAAC4H,kBAAkB,EAAErC,WAAW,GAAGqC,kBAAkB,GAAG,CAAC,EAAEC,kBAAkB,EAAEpC,WAAW,GAAGoC,kBAAkB,GAAG,CAAC,CAAC,CACrI;IACL,CAAC,MACI;MACD;MACAR,YAAY,GAAG,IAAItF,gBAAgB,CAAC,CAAC;MACrCuF,WAAW,GAAGT,oBAAoB;MAClCU,WAAW,GAAGT,kBAAkB;MAChCU,gBAAgB,GAAI1B,cAAc,CAAC,CAAC,CAAC,GAAGC,cAAc,CAAC,CAAC,CAAC,GAAIgB,qBAAqB;MAClFU,iBAAiB,GAAG,UAAU,CAAC;MAC/BC,YAAY,GAAIR,WAAW,GAAI,IAAI,CAAC/C,gBAAgB,CAAC3D,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC2D,gBAAgB,CAAC3D,MAAM,GAAG,CAAC;MAClG,GAAG;QACC;QACA,MAAMmH,QAAQ,GAAGH,gBAAgB,GAAGZ,mBAAmB;QACvD;QACA,IAAIe,QAAQ,KAAKL,WAAW,IAAKK,QAAQ,GAAGJ,WAAW,IAAIN,aAAa,CAACU,QAAQ,GAAG,CAAC,CAAC,IAAIV,aAAa,CAACU,QAAQ,GAAG,CAAC,CAAE,EAAE;UACpH;UACAnF,aAAa,GAAGyE,aAAa,CAACU,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC;UAC/ClF,aAAa,GAAGD,aAAa,GAAGgF,gBAAgB,GAAGT,qBAAqB;UACxE,IAAIvE,aAAa,GAAGiF,iBAAiB,EAAE;YACnCJ,YAAY,CAAChF,cAAc,CAAC,CAAC;UACjC;UACAoF,iBAAiB,GAAGjF,aAAa,GAAG,CAAC;UACrC6E,YAAY,CAAC9E,kBAAkB,CAACC,aAAa,GAAG,CAAC,EAAEC,aAAa,GAAG,CAAC,CAAC;UACrE+E,gBAAgB,GAAIG,QAAQ,GAAG,CAAC,GAAIf,mBAAmB,CAAC,CAAC;QAC7D,CAAC,MACI;UACD;UACApE,aAAa,GAAGyE,aAAa,CAACU,QAAQ,GAAG,CAAC,CAAC;UAC3ClF,aAAa,GAAGD,aAAa,GAAGgF,gBAAgB,GAAGT,qBAAqB;UACxE,IAAIvE,aAAa,GAAGiF,iBAAiB,EAAE;YACnCJ,YAAY,CAAChF,cAAc,CAAC,CAAC;UACjC;UACAoF,iBAAiB,GAAGjF,aAAa;UACjC6E,YAAY,CAACzE,kBAAkB,CAACJ,aAAa,GAAG,CAAC,EAAEC,aAAa,GAAG,CAAC,CAAC;UACrE+E,gBAAgB,GAAIG,QAAQ,GAAG,CAAC,GAAIf,mBAAmB,CAAC,CAAC;QAC7D;QACA,IAAIc,YAAY,IAAI,CAAC,EAAE;UACnBT,aAAa,GAAG,IAAI,CAAC9C,gBAAgB,CAACuD,YAAY,CAAC;UACnDd,mBAAmB,GAAGK,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;UACxCK,WAAW,GAAG,CAAC;UACfC,WAAW,GAAGN,aAAa,CAACzG,MAAM,GAAG,CAAC;QAC1C;MACJ,CAAC,QAAQ,EAAEkH,YAAY,IAAI,CAAC,CAAC;MAC7B;MACA;MACAN,cAAc,GAAGC,YAAY,CAACxE,UAAU,CAAC,CAAC;IAC9C;IACA,OAAO,IAAI,CAACyD,kBAAkB,CAACa,cAAc,EAAEC,cAAc,CAAC;EAClE;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACInB,qBAAqBA,CAACX,aAAa,EAAEC,WAAW,EAAEC,aAAa,EAAEC,WAAW,EAAEK,cAAc,EAAEC,cAAc,EAAEL,YAAY,EAAE;IACxH,IAAIlD,aAAa,GAAG,CAAC;MAAEC,aAAa,GAAG,CAAC;IACxC,IAAIgE,oBAAoB,GAAG,CAAC;MAAEC,kBAAkB,GAAG,CAAC;IACpD,IAAIG,oBAAoB,GAAG,CAAC;MAAEC,kBAAkB,GAAG,CAAC;IACpD;IACA;IACAxB,aAAa,EAAE;IACfE,aAAa,EAAE;IACf;IACA;IACAM,cAAc,CAAC,CAAC,CAAC,GAAG,CAAC;IACrBC,cAAc,CAAC,CAAC,CAAC,GAAG,CAAC;IACrB;IACA,IAAI,CAAC7B,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACC,gBAAgB,GAAG,EAAE;IAC1B;IACA;IACA;IACA;IACA,MAAM+D,cAAc,GAAI3C,WAAW,GAAGD,aAAa,IAAKG,WAAW,GAAGD,aAAa,CAAC;IACpF,MAAM2C,YAAY,GAAGD,cAAc,GAAG,CAAC;IACvC,MAAMlB,aAAa,GAAG,IAAIzG,UAAU,CAAC4H,YAAY,CAAC;IAClD,MAAMlB,aAAa,GAAG,IAAI1G,UAAU,CAAC4H,YAAY,CAAC;IAClD;IACA;IACA,MAAM3B,mBAAmB,GAAIf,WAAW,GAAGD,aAAc;IACzD,MAAMoB,mBAAmB,GAAIrB,WAAW,GAAGD,aAAc;IACzD;IACA;IACA;IACA;IACA,MAAMqB,qBAAqB,GAAIrB,aAAa,GAAGE,aAAc;IAC7D,MAAMuB,qBAAqB,GAAIxB,WAAW,GAAGE,WAAY;IACzD;IACA;IACA;IACA,MAAM2C,KAAK,GAAGxB,mBAAmB,GAAGJ,mBAAmB;IACvD,MAAMU,WAAW,GAAIkB,KAAK,GAAG,CAAC,KAAK,CAAE;IACrC;IACA;IACApB,aAAa,CAACR,mBAAmB,CAAC,GAAGlB,aAAa;IAClD2B,aAAa,CAACL,mBAAmB,CAAC,GAAGrB,WAAW;IAChD;IACAG,YAAY,CAAC,CAAC,CAAC,GAAG,KAAK;IACvB;IACA;IACA;IACA;IACA;IACA;IACA;IACA,KAAK,IAAI2C,cAAc,GAAG,CAAC,EAAEA,cAAc,IAAKH,cAAc,GAAG,CAAC,GAAI,CAAC,EAAEG,cAAc,EAAE,EAAE;MACvF,IAAIC,qBAAqB,GAAG,CAAC;MAC7B,IAAIC,qBAAqB,GAAG,CAAC;MAC7B;MACA9B,oBAAoB,GAAG,IAAI,CAAC+B,iBAAiB,CAAChC,mBAAmB,GAAG6B,cAAc,EAAEA,cAAc,EAAE7B,mBAAmB,EAAE2B,YAAY,CAAC;MACtIzB,kBAAkB,GAAG,IAAI,CAAC8B,iBAAiB,CAAChC,mBAAmB,GAAG6B,cAAc,EAAEA,cAAc,EAAE7B,mBAAmB,EAAE2B,YAAY,CAAC;MACpI,KAAK,IAAIR,QAAQ,GAAGlB,oBAAoB,EAAEkB,QAAQ,IAAIjB,kBAAkB,EAAEiB,QAAQ,IAAI,CAAC,EAAE;QACrF;QACA;QACA;QACA,IAAIA,QAAQ,KAAKlB,oBAAoB,IAAKkB,QAAQ,GAAGjB,kBAAkB,IAAIM,aAAa,CAACW,QAAQ,GAAG,CAAC,CAAC,GAAGX,aAAa,CAACW,QAAQ,GAAG,CAAC,CAAE,EAAE;UACnInF,aAAa,GAAGwE,aAAa,CAACW,QAAQ,GAAG,CAAC,CAAC;QAC/C,CAAC,MACI;UACDnF,aAAa,GAAGwE,aAAa,CAACW,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC;QACnD;QACAlF,aAAa,GAAGD,aAAa,IAAImF,QAAQ,GAAGnB,mBAAmB,CAAC,GAAGG,qBAAqB;QACxF;QACA,MAAM8B,iBAAiB,GAAGjG,aAAa;QACvC;QACA;QACA,OAAOA,aAAa,GAAG+C,WAAW,IAAI9C,aAAa,GAAGgD,WAAW,IAAI,IAAI,CAAChB,gBAAgB,CAACjC,aAAa,GAAG,CAAC,EAAEC,aAAa,GAAG,CAAC,CAAC,EAAE;UAC9HD,aAAa,EAAE;UACfC,aAAa,EAAE;QACnB;QACAuE,aAAa,CAACW,QAAQ,CAAC,GAAGnF,aAAa;QACvC,IAAIA,aAAa,GAAGC,aAAa,GAAG6F,qBAAqB,GAAGC,qBAAqB,EAAE;UAC/ED,qBAAqB,GAAG9F,aAAa;UACrC+F,qBAAqB,GAAG9F,aAAa;QACzC;QACA;QACA;QACA;QACA;QACA,IAAI,CAACyE,WAAW,IAAIxE,IAAI,CAACgG,GAAG,CAACf,QAAQ,GAAGf,mBAAmB,CAAC,IAAKyB,cAAc,GAAG,CAAE,EAAE;UAClF,IAAI7F,aAAa,IAAIyE,aAAa,CAACU,QAAQ,CAAC,EAAE;YAC1C7B,cAAc,CAAC,CAAC,CAAC,GAAGtD,aAAa;YACjCuD,cAAc,CAAC,CAAC,CAAC,GAAGtD,aAAa;YACjC,IAAIgG,iBAAiB,IAAIxB,aAAa,CAACU,QAAQ,CAAC,IAAI,IAAI,CAAC,6CAA6C,CAAC,IAAIU,cAAc,IAAK,IAAI,CAAC,6CAA6C,CAAE,EAAE;cAChL;cACA,OAAO,IAAI,CAAC9B,SAAS,CAACC,mBAAmB,EAAEC,oBAAoB,EAAEC,kBAAkB,EAAEC,qBAAqB,EAAEC,mBAAmB,EAAEC,oBAAoB,EAAEC,kBAAkB,EAAEC,qBAAqB,EAAEC,aAAa,EAAEC,aAAa,EAAEzE,aAAa,EAAE+C,WAAW,EAAEO,cAAc,EAAErD,aAAa,EAAEgD,WAAW,EAAEM,cAAc,EAAEmB,WAAW,EAAExB,YAAY,CAAC;YACtV,CAAC,MACI;cACD;cACA;cACA,OAAO,IAAI;YACf;UACJ;QACJ;MACJ;MACA;MACA,MAAMiD,oBAAoB,GAAG,CAAEL,qBAAqB,GAAGhD,aAAa,IAAKiD,qBAAqB,GAAG/C,aAAa,CAAC,GAAG6C,cAAc,IAAI,CAAC;MACrI,IAAI,IAAI,CAAClF,2BAA2B,KAAK,IAAI,IAAI,CAAC,IAAI,CAACA,2BAA2B,CAACmF,qBAAqB,EAAEK,oBAAoB,CAAC,EAAE;QAC7H;QACAjD,YAAY,CAAC,CAAC,CAAC,GAAG,IAAI;QACtB;QACAI,cAAc,CAAC,CAAC,CAAC,GAAGwC,qBAAqB;QACzCvC,cAAc,CAAC,CAAC,CAAC,GAAGwC,qBAAqB;QACzC,IAAII,oBAAoB,GAAG,CAAC,IAAI,IAAI,CAAC,6CAA6C,CAAC,IAAIN,cAAc,IAAK,IAAI,CAAC,6CAA6C,CAAE,EAAE;UAC5J;UACA,OAAO,IAAI,CAAC9B,SAAS,CAACC,mBAAmB,EAAEC,oBAAoB,EAAEC,kBAAkB,EAAEC,qBAAqB,EAAEC,mBAAmB,EAAEC,oBAAoB,EAAEC,kBAAkB,EAAEC,qBAAqB,EAAEC,aAAa,EAAEC,aAAa,EAAEzE,aAAa,EAAE+C,WAAW,EAAEO,cAAc,EAAErD,aAAa,EAAEgD,WAAW,EAAEM,cAAc,EAAEmB,WAAW,EAAExB,YAAY,CAAC;QACtV,CAAC,MACI;UACD;UACA;UACA;UACAJ,aAAa,EAAE;UACfE,aAAa,EAAE;UACf,OAAO,CACH,IAAIxF,UAAU,CAACsF,aAAa,EAAEC,WAAW,GAAGD,aAAa,GAAG,CAAC,EAAEE,aAAa,EAAEC,WAAW,GAAGD,aAAa,GAAG,CAAC,CAAC,CACjH;QACL;MACJ;MACA;MACAqB,oBAAoB,GAAG,IAAI,CAAC2B,iBAAiB,CAAC5B,mBAAmB,GAAGyB,cAAc,EAAEA,cAAc,EAAEzB,mBAAmB,EAAEuB,YAAY,CAAC;MACtIrB,kBAAkB,GAAG,IAAI,CAAC0B,iBAAiB,CAAC5B,mBAAmB,GAAGyB,cAAc,EAAEA,cAAc,EAAEzB,mBAAmB,EAAEuB,YAAY,CAAC;MACpI,KAAK,IAAIR,QAAQ,GAAGd,oBAAoB,EAAEc,QAAQ,IAAIb,kBAAkB,EAAEa,QAAQ,IAAI,CAAC,EAAE;QACrF;QACA;QACA;QACA,IAAIA,QAAQ,KAAKd,oBAAoB,IAAKc,QAAQ,GAAGb,kBAAkB,IAAIG,aAAa,CAACU,QAAQ,GAAG,CAAC,CAAC,IAAIV,aAAa,CAACU,QAAQ,GAAG,CAAC,CAAE,EAAE;UACpInF,aAAa,GAAGyE,aAAa,CAACU,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC;QACnD,CAAC,MACI;UACDnF,aAAa,GAAGyE,aAAa,CAACU,QAAQ,GAAG,CAAC,CAAC;QAC/C;QACAlF,aAAa,GAAGD,aAAa,IAAImF,QAAQ,GAAGf,mBAAmB,CAAC,GAAGG,qBAAqB;QACxF;QACA,MAAM0B,iBAAiB,GAAGjG,aAAa;QACvC;QACA;QACA,OAAOA,aAAa,GAAG8C,aAAa,IAAI7C,aAAa,GAAG+C,aAAa,IAAI,IAAI,CAACf,gBAAgB,CAACjC,aAAa,EAAEC,aAAa,CAAC,EAAE;UAC1HD,aAAa,EAAE;UACfC,aAAa,EAAE;QACnB;QACAwE,aAAa,CAACU,QAAQ,CAAC,GAAGnF,aAAa;QACvC;QACA;QACA;QACA,IAAI0E,WAAW,IAAIxE,IAAI,CAACgG,GAAG,CAACf,QAAQ,GAAGnB,mBAAmB,CAAC,IAAI6B,cAAc,EAAE;UAC3E,IAAI7F,aAAa,IAAIwE,aAAa,CAACW,QAAQ,CAAC,EAAE;YAC1C7B,cAAc,CAAC,CAAC,CAAC,GAAGtD,aAAa;YACjCuD,cAAc,CAAC,CAAC,CAAC,GAAGtD,aAAa;YACjC,IAAIgG,iBAAiB,IAAIzB,aAAa,CAACW,QAAQ,CAAC,IAAI,IAAI,CAAC,6CAA6C,CAAC,IAAIU,cAAc,IAAK,IAAI,CAAC,6CAA6C,CAAE,EAAE;cAChL;cACA,OAAO,IAAI,CAAC9B,SAAS,CAACC,mBAAmB,EAAEC,oBAAoB,EAAEC,kBAAkB,EAAEC,qBAAqB,EAAEC,mBAAmB,EAAEC,oBAAoB,EAAEC,kBAAkB,EAAEC,qBAAqB,EAAEC,aAAa,EAAEC,aAAa,EAAEzE,aAAa,EAAE+C,WAAW,EAAEO,cAAc,EAAErD,aAAa,EAAEgD,WAAW,EAAEM,cAAc,EAAEmB,WAAW,EAAExB,YAAY,CAAC;YACtV,CAAC,MACI;cACD;cACA;cACA,OAAO,IAAI;YACf;UACJ;QACJ;MACJ;MACA;MACA,IAAI2C,cAAc,IAAI,IAAI,CAAC,4CAA4C;QACnE;QACA;QACA,IAAIO,IAAI,GAAG,IAAIrI,UAAU,CAACmG,kBAAkB,GAAGD,oBAAoB,GAAG,CAAC,CAAC;QACxEmC,IAAI,CAAC,CAAC,CAAC,GAAGpC,mBAAmB,GAAGC,oBAAoB,GAAG,CAAC;QACxDjF,OAAO,CAACM,KAAK,CAACkF,aAAa,EAAEP,oBAAoB,EAAEmC,IAAI,EAAE,CAAC,EAAElC,kBAAkB,GAAGD,oBAAoB,GAAG,CAAC,CAAC;QAC1G,IAAI,CAACvC,gBAAgB,CAAC5B,IAAI,CAACsG,IAAI,CAAC;QAChCA,IAAI,GAAG,IAAIrI,UAAU,CAACuG,kBAAkB,GAAGD,oBAAoB,GAAG,CAAC,CAAC;QACpE+B,IAAI,CAAC,CAAC,CAAC,GAAGhC,mBAAmB,GAAGC,oBAAoB,GAAG,CAAC;QACxDrF,OAAO,CAACM,KAAK,CAACmF,aAAa,EAAEJ,oBAAoB,EAAE+B,IAAI,EAAE,CAAC,EAAE9B,kBAAkB,GAAGD,oBAAoB,GAAG,CAAC,CAAC;QAC1G,IAAI,CAAC1C,gBAAgB,CAAC7B,IAAI,CAACsG,IAAI,CAAC;MACpC;IACJ;IACA;IACA;IACA,OAAO,IAAI,CAACrC,SAAS,CAACC,mBAAmB,EAAEC,oBAAoB,EAAEC,kBAAkB,EAAEC,qBAAqB,EAAEC,mBAAmB,EAAEC,oBAAoB,EAAEC,kBAAkB,EAAEC,qBAAqB,EAAEC,aAAa,EAAEC,aAAa,EAAEzE,aAAa,EAAE+C,WAAW,EAAEO,cAAc,EAAErD,aAAa,EAAEgD,WAAW,EAAEM,cAAc,EAAEmB,WAAW,EAAExB,YAAY,CAAC;EACtV;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIE,eAAeA,CAAC1E,OAAO,EAAE;IACrB;IACA,KAAK,IAAIT,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGS,OAAO,CAACV,MAAM,EAAEC,CAAC,EAAE,EAAE;MACrC,MAAMoI,MAAM,GAAG3H,OAAO,CAACT,CAAC,CAAC;MACzB,MAAMqI,YAAY,GAAIrI,CAAC,GAAGS,OAAO,CAACV,MAAM,GAAG,CAAC,GAAIU,OAAO,CAACT,CAAC,GAAG,CAAC,CAAC,CAAC6E,aAAa,GAAG,IAAI,CAACvB,uBAAuB,CAACvD,MAAM;MAClH,MAAMuI,YAAY,GAAItI,CAAC,GAAGS,OAAO,CAACV,MAAM,GAAG,CAAC,GAAIU,OAAO,CAACT,CAAC,GAAG,CAAC,CAAC,CAAC+E,aAAa,GAAG,IAAI,CAACvB,uBAAuB,CAACzD,MAAM;MAClH,MAAMwI,aAAa,GAAGH,MAAM,CAACI,cAAc,GAAG,CAAC;MAC/C,MAAMC,aAAa,GAAGL,MAAM,CAACM,cAAc,GAAG,CAAC;MAC/C,OAAON,MAAM,CAACvD,aAAa,GAAGuD,MAAM,CAACI,cAAc,GAAGH,YAAY,IAC3DD,MAAM,CAACrD,aAAa,GAAGqD,MAAM,CAACM,cAAc,GAAGJ,YAAY,KAC1D,CAACC,aAAa,IAAI,IAAI,CAAC/D,wBAAwB,CAAC4D,MAAM,CAACvD,aAAa,EAAEuD,MAAM,CAACvD,aAAa,GAAGuD,MAAM,CAACI,cAAc,CAAC,CAAC,KACpH,CAACC,aAAa,IAAI,IAAI,CAAC9D,wBAAwB,CAACyD,MAAM,CAACrD,aAAa,EAAEqD,MAAM,CAACrD,aAAa,GAAGqD,MAAM,CAACM,cAAc,CAAC,CAAC,EAAE;QAC1H,MAAMC,gBAAgB,GAAG,IAAI,CAACzE,sBAAsB,CAACkE,MAAM,CAACvD,aAAa,EAAEuD,MAAM,CAACrD,aAAa,CAAC;QAChG,MAAM6D,cAAc,GAAG,IAAI,CAAC1E,sBAAsB,CAACkE,MAAM,CAACvD,aAAa,GAAGuD,MAAM,CAACI,cAAc,EAAEJ,MAAM,CAACrD,aAAa,GAAGqD,MAAM,CAACM,cAAc,CAAC;QAC9I,IAAIE,cAAc,IAAI,CAACD,gBAAgB,EAAE;UACrC;UACA;QACJ;QACAP,MAAM,CAACvD,aAAa,EAAE;QACtBuD,MAAM,CAACrD,aAAa,EAAE;MAC1B;MACA,MAAM8D,eAAe,GAAG,CAAC,IAAI,CAAC;MAC9B,IAAI7I,CAAC,GAAGS,OAAO,CAACV,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC+I,cAAc,CAACrI,OAAO,CAACT,CAAC,CAAC,EAAES,OAAO,CAACT,CAAC,GAAG,CAAC,CAAC,EAAE6I,eAAe,CAAC,EAAE;QAC5FpI,OAAO,CAACT,CAAC,CAAC,GAAG6I,eAAe,CAAC,CAAC,CAAC;QAC/BpI,OAAO,CAACsI,MAAM,CAAC/I,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QACxBA,CAAC,EAAE;QACH;MACJ;IACJ;IACA;IACA,KAAK,IAAIA,CAAC,GAAGS,OAAO,CAACV,MAAM,GAAG,CAAC,EAAEC,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC1C,MAAMoI,MAAM,GAAG3H,OAAO,CAACT,CAAC,CAAC;MACzB,IAAIqI,YAAY,GAAG,CAAC;MACpB,IAAIC,YAAY,GAAG,CAAC;MACpB,IAAItI,CAAC,GAAG,CAAC,EAAE;QACP,MAAMgJ,UAAU,GAAGvI,OAAO,CAACT,CAAC,GAAG,CAAC,CAAC;QACjCqI,YAAY,GAAGW,UAAU,CAACnE,aAAa,GAAGmE,UAAU,CAACR,cAAc;QACnEF,YAAY,GAAGU,UAAU,CAACjE,aAAa,GAAGiE,UAAU,CAACN,cAAc;MACvE;MACA,MAAMH,aAAa,GAAGH,MAAM,CAACI,cAAc,GAAG,CAAC;MAC/C,MAAMC,aAAa,GAAGL,MAAM,CAACM,cAAc,GAAG,CAAC;MAC/C,IAAIO,SAAS,GAAG,CAAC;MACjB,IAAIC,SAAS,GAAG,IAAI,CAACC,cAAc,CAACf,MAAM,CAACvD,aAAa,EAAEuD,MAAM,CAACI,cAAc,EAAEJ,MAAM,CAACrD,aAAa,EAAEqD,MAAM,CAACM,cAAc,CAAC;MAC7H,KAAK,IAAIf,KAAK,GAAG,CAAC,GAAGA,KAAK,EAAE,EAAE;QAC1B,MAAM9C,aAAa,GAAGuD,MAAM,CAACvD,aAAa,GAAG8C,KAAK;QAClD,MAAM5C,aAAa,GAAGqD,MAAM,CAACrD,aAAa,GAAG4C,KAAK;QAClD,IAAI9C,aAAa,GAAGwD,YAAY,IAAItD,aAAa,GAAGuD,YAAY,EAAE;UAC9D;QACJ;QACA,IAAIC,aAAa,IAAI,CAAC,IAAI,CAAC/D,wBAAwB,CAACK,aAAa,EAAEA,aAAa,GAAGuD,MAAM,CAACI,cAAc,CAAC,EAAE;UACvG;QACJ;QACA,IAAIC,aAAa,IAAI,CAAC,IAAI,CAAC9D,wBAAwB,CAACI,aAAa,EAAEA,aAAa,GAAGqD,MAAM,CAACM,cAAc,CAAC,EAAE;UACvG;QACJ;QACA,MAAMU,sBAAsB,GAAIvE,aAAa,KAAKwD,YAAY,IAAItD,aAAa,KAAKuD,YAAa;QACjG,MAAMe,KAAK,GAAI,CAACD,sBAAsB,GAAG,CAAC,GAAG,CAAC,IACxC,IAAI,CAACD,cAAc,CAACtE,aAAa,EAAEuD,MAAM,CAACI,cAAc,EAAEzD,aAAa,EAAEqD,MAAM,CAACM,cAAc,CAAE;QACtG,IAAIW,KAAK,GAAGH,SAAS,EAAE;UACnBA,SAAS,GAAGG,KAAK;UACjBJ,SAAS,GAAGtB,KAAK;QACrB;MACJ;MACAS,MAAM,CAACvD,aAAa,IAAIoE,SAAS;MACjCb,MAAM,CAACrD,aAAa,IAAIkE,SAAS;MACjC,MAAMJ,eAAe,GAAG,CAAC,IAAI,CAAC;MAC9B,IAAI7I,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC8I,cAAc,CAACrI,OAAO,CAACT,CAAC,GAAG,CAAC,CAAC,EAAES,OAAO,CAACT,CAAC,CAAC,EAAE6I,eAAe,CAAC,EAAE;QAC3EpI,OAAO,CAACT,CAAC,GAAG,CAAC,CAAC,GAAG6I,eAAe,CAAC,CAAC,CAAC;QACnCpI,OAAO,CAACsI,MAAM,CAAC/I,CAAC,EAAE,CAAC,CAAC;QACpBA,CAAC,EAAE;QACH;MACJ;IACJ;IACA;IACA;IACA,IAAI,IAAI,CAACoD,WAAW,EAAE;MAClB,KAAK,IAAIpD,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGQ,OAAO,CAACV,MAAM,EAAEC,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;QAChD,MAAMsJ,OAAO,GAAG7I,OAAO,CAACT,CAAC,GAAG,CAAC,CAAC;QAC9B,MAAMuJ,OAAO,GAAG9I,OAAO,CAACT,CAAC,CAAC;QAC1B,MAAMwJ,aAAa,GAAGD,OAAO,CAAC1E,aAAa,GAAGyE,OAAO,CAACzE,aAAa,GAAGyE,OAAO,CAACd,cAAc;QAC5F,MAAMiB,cAAc,GAAGH,OAAO,CAACzE,aAAa;QAC5C,MAAM6E,YAAY,GAAGH,OAAO,CAAC1E,aAAa,GAAG0E,OAAO,CAACf,cAAc;QACnE,MAAMmB,gBAAgB,GAAGD,YAAY,GAAGD,cAAc;QACtD,MAAMG,cAAc,GAAGN,OAAO,CAACvE,aAAa;QAC5C,MAAM8E,YAAY,GAAGN,OAAO,CAACxE,aAAa,GAAGwE,OAAO,CAACb,cAAc;QACnE,MAAMoB,gBAAgB,GAAGD,YAAY,GAAGD,cAAc;QACtD;QACA,IAAIJ,aAAa,GAAG,CAAC,IAAIG,gBAAgB,GAAG,EAAE,IAAIG,gBAAgB,GAAG,EAAE,EAAE;UACrE,MAAMC,CAAC,GAAG,IAAI,CAACC,6BAA6B,CAACP,cAAc,EAAEE,gBAAgB,EAAEC,cAAc,EAAEE,gBAAgB,EAAEN,aAAa,CAAC;UAC/H,IAAIO,CAAC,EAAE;YACH,MAAM,CAACE,kBAAkB,EAAEC,kBAAkB,CAAC,GAAGH,CAAC;YAClD,IAAIE,kBAAkB,KAAKX,OAAO,CAACzE,aAAa,GAAGyE,OAAO,CAACd,cAAc,IAAI0B,kBAAkB,KAAKZ,OAAO,CAACvE,aAAa,GAAGuE,OAAO,CAACZ,cAAc,EAAE;cAChJ;cACAY,OAAO,CAACd,cAAc,GAAGyB,kBAAkB,GAAGX,OAAO,CAACzE,aAAa;cACnEyE,OAAO,CAACZ,cAAc,GAAGwB,kBAAkB,GAAGZ,OAAO,CAACvE,aAAa;cACnEwE,OAAO,CAAC1E,aAAa,GAAGoF,kBAAkB,GAAGT,aAAa;cAC1DD,OAAO,CAACxE,aAAa,GAAGmF,kBAAkB,GAAGV,aAAa;cAC1DD,OAAO,CAACf,cAAc,GAAGkB,YAAY,GAAGH,OAAO,CAAC1E,aAAa;cAC7D0E,OAAO,CAACb,cAAc,GAAGmB,YAAY,GAAGN,OAAO,CAACxE,aAAa;YACjE;UACJ;QACJ;MACJ;IACJ;IACA,OAAOtE,OAAO;EAClB;EACAuJ,6BAA6BA,CAACnF,aAAa,EAAE2D,cAAc,EAAEzD,aAAa,EAAE2D,cAAc,EAAEyB,aAAa,EAAE;IACvG,IAAI3B,cAAc,GAAG2B,aAAa,IAAIzB,cAAc,GAAGyB,aAAa,EAAE;MAClE,OAAO,IAAI;IACf;IACA,MAAMC,WAAW,GAAGvF,aAAa,GAAG2D,cAAc,GAAG2B,aAAa,GAAG,CAAC;IACtE,MAAME,WAAW,GAAGtF,aAAa,GAAG2D,cAAc,GAAGyB,aAAa,GAAG,CAAC;IACtE,IAAIjB,SAAS,GAAG,CAAC;IACjB,IAAIoB,iBAAiB,GAAG,CAAC;IACzB,IAAIC,iBAAiB,GAAG,CAAC;IACzB,KAAK,IAAIvK,CAAC,GAAG6E,aAAa,EAAE7E,CAAC,GAAGoK,WAAW,EAAEpK,CAAC,EAAE,EAAE;MAC9C,KAAK,IAAIwK,CAAC,GAAGzF,aAAa,EAAEyF,CAAC,GAAGH,WAAW,EAAEG,CAAC,EAAE,EAAE;QAC9C,MAAMnB,KAAK,GAAG,IAAI,CAACoB,wBAAwB,CAACzK,CAAC,EAAEwK,CAAC,EAAEL,aAAa,CAAC;QAChE,IAAId,KAAK,GAAG,CAAC,IAAIA,KAAK,GAAGH,SAAS,EAAE;UAChCA,SAAS,GAAGG,KAAK;UACjBiB,iBAAiB,GAAGtK,CAAC;UACrBuK,iBAAiB,GAAGC,CAAC;QACzB;MACJ;IACJ;IACA,IAAItB,SAAS,GAAG,CAAC,EAAE;MACf,OAAO,CAACoB,iBAAiB,EAAEC,iBAAiB,CAAC;IACjD;IACA,OAAO,IAAI;EACf;EACAE,wBAAwBA,CAAC5F,aAAa,EAAEE,aAAa,EAAEhF,MAAM,EAAE;IAC3D,IAAIsJ,KAAK,GAAG,CAAC;IACb,KAAK,IAAIqB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3K,MAAM,EAAE2K,CAAC,EAAE,EAAE;MAC7B,IAAI,CAAC,IAAI,CAAC1G,gBAAgB,CAACa,aAAa,GAAG6F,CAAC,EAAE3F,aAAa,GAAG2F,CAAC,CAAC,EAAE;QAC9D,OAAO,CAAC;MACZ;MACArB,KAAK,IAAI,IAAI,CAAChG,uBAAuB,CAACwB,aAAa,GAAG6F,CAAC,CAAC,CAAC3K,MAAM;IACnE;IACA,OAAOsJ,KAAK;EAChB;EACAsB,mBAAmBA,CAACrG,KAAK,EAAE;IACvB,IAAIA,KAAK,IAAI,CAAC,IAAIA,KAAK,IAAI,IAAI,CAAChB,uBAAuB,CAACvD,MAAM,GAAG,CAAC,EAAE;MAChE,OAAO,IAAI;IACf;IACA,OAAQ,IAAI,CAACqD,WAAW,IAAI,OAAO,CAACwH,IAAI,CAAC,IAAI,CAACvH,uBAAuB,CAACiB,KAAK,CAAC,CAAC;EACjF;EACAuG,yBAAyBA,CAAChG,aAAa,EAAE2D,cAAc,EAAE;IACrD,IAAI,IAAI,CAACmC,mBAAmB,CAAC9F,aAAa,CAAC,IAAI,IAAI,CAAC8F,mBAAmB,CAAC9F,aAAa,GAAG,CAAC,CAAC,EAAE;MACxF,OAAO,IAAI;IACf;IACA,IAAI2D,cAAc,GAAG,CAAC,EAAE;MACpB,MAAM1D,WAAW,GAAGD,aAAa,GAAG2D,cAAc;MAClD,IAAI,IAAI,CAACmC,mBAAmB,CAAC7F,WAAW,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC6F,mBAAmB,CAAC7F,WAAW,CAAC,EAAE;QACpF,OAAO,IAAI;MACf;IACJ;IACA,OAAO,KAAK;EAChB;EACAgG,mBAAmBA,CAACxG,KAAK,EAAE;IACvB,IAAIA,KAAK,IAAI,CAAC,IAAIA,KAAK,IAAI,IAAI,CAACd,uBAAuB,CAACzD,MAAM,GAAG,CAAC,EAAE;MAChE,OAAO,IAAI;IACf;IACA,OAAQ,IAAI,CAACqD,WAAW,IAAI,OAAO,CAACwH,IAAI,CAAC,IAAI,CAACrH,uBAAuB,CAACe,KAAK,CAAC,CAAC;EACjF;EACAyG,yBAAyBA,CAAChG,aAAa,EAAE2D,cAAc,EAAE;IACrD,IAAI,IAAI,CAACoC,mBAAmB,CAAC/F,aAAa,CAAC,IAAI,IAAI,CAAC+F,mBAAmB,CAAC/F,aAAa,GAAG,CAAC,CAAC,EAAE;MACxF,OAAO,IAAI;IACf;IACA,IAAI2D,cAAc,GAAG,CAAC,EAAE;MACpB,MAAM1D,WAAW,GAAGD,aAAa,GAAG2D,cAAc;MAClD,IAAI,IAAI,CAACoC,mBAAmB,CAAC9F,WAAW,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC8F,mBAAmB,CAAC9F,WAAW,CAAC,EAAE;QACpF,OAAO,IAAI;MACf;IACJ;IACA,OAAO,KAAK;EAChB;EACAmE,cAAcA,CAACtE,aAAa,EAAE2D,cAAc,EAAEzD,aAAa,EAAE2D,cAAc,EAAE;IACzE,MAAMsC,aAAa,GAAI,IAAI,CAACH,yBAAyB,CAAChG,aAAa,EAAE2D,cAAc,CAAC,GAAG,CAAC,GAAG,CAAE;IAC7F,MAAMyC,aAAa,GAAI,IAAI,CAACF,yBAAyB,CAAChG,aAAa,EAAE2D,cAAc,CAAC,GAAG,CAAC,GAAG,CAAE;IAC7F,OAAQsC,aAAa,GAAGC,aAAa;EACzC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIpF,kBAAkBA,CAACqF,IAAI,EAAEC,KAAK,EAAE;IAC5B,MAAMtC,eAAe,GAAG,EAAE;IAC1B,IAAIqC,IAAI,CAACnL,MAAM,KAAK,CAAC,IAAIoL,KAAK,CAACpL,MAAM,KAAK,CAAC,EAAE;MACzC,OAAQoL,KAAK,CAACpL,MAAM,GAAG,CAAC,GAAIoL,KAAK,GAAGD,IAAI;IAC5C,CAAC,MACI,IAAI,IAAI,CAACpC,cAAc,CAACoC,IAAI,CAACA,IAAI,CAACnL,MAAM,GAAG,CAAC,CAAC,EAAEoL,KAAK,CAAC,CAAC,CAAC,EAAEtC,eAAe,CAAC,EAAE;MAC5E;MACA;MACA;MACA;MACA,MAAMtD,MAAM,GAAG,IAAI6F,KAAK,CAACF,IAAI,CAACnL,MAAM,GAAGoL,KAAK,CAACpL,MAAM,GAAG,CAAC,CAAC;MACxDgB,OAAO,CAACC,IAAI,CAACkK,IAAI,EAAE,CAAC,EAAE3F,MAAM,EAAE,CAAC,EAAE2F,IAAI,CAACnL,MAAM,GAAG,CAAC,CAAC;MACjDwF,MAAM,CAAC2F,IAAI,CAACnL,MAAM,GAAG,CAAC,CAAC,GAAG8I,eAAe,CAAC,CAAC,CAAC;MAC5C9H,OAAO,CAACC,IAAI,CAACmK,KAAK,EAAE,CAAC,EAAE5F,MAAM,EAAE2F,IAAI,CAACnL,MAAM,EAAEoL,KAAK,CAACpL,MAAM,GAAG,CAAC,CAAC;MAC7D,OAAOwF,MAAM;IACjB,CAAC,MACI;MACD,MAAMA,MAAM,GAAG,IAAI6F,KAAK,CAACF,IAAI,CAACnL,MAAM,GAAGoL,KAAK,CAACpL,MAAM,CAAC;MACpDgB,OAAO,CAACC,IAAI,CAACkK,IAAI,EAAE,CAAC,EAAE3F,MAAM,EAAE,CAAC,EAAE2F,IAAI,CAACnL,MAAM,CAAC;MAC7CgB,OAAO,CAACC,IAAI,CAACmK,KAAK,EAAE,CAAC,EAAE5F,MAAM,EAAE2F,IAAI,CAACnL,MAAM,EAAEoL,KAAK,CAACpL,MAAM,CAAC;MACzD,OAAOwF,MAAM;IACjB;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIuD,cAAcA,CAACoC,IAAI,EAAEC,KAAK,EAAEtC,eAAe,EAAE;IACzCnI,KAAK,CAACC,MAAM,CAACuK,IAAI,CAACrG,aAAa,IAAIsG,KAAK,CAACtG,aAAa,EAAE,uDAAuD,CAAC;IAChHnE,KAAK,CAACC,MAAM,CAACuK,IAAI,CAACnG,aAAa,IAAIoG,KAAK,CAACpG,aAAa,EAAE,uDAAuD,CAAC;IAChH,IAAImG,IAAI,CAACrG,aAAa,GAAGqG,IAAI,CAAC1C,cAAc,IAAI2C,KAAK,CAACtG,aAAa,IAAIqG,IAAI,CAACnG,aAAa,GAAGmG,IAAI,CAACxC,cAAc,IAAIyC,KAAK,CAACpG,aAAa,EAAE;MACpI,MAAMF,aAAa,GAAGqG,IAAI,CAACrG,aAAa;MACxC,IAAI2D,cAAc,GAAG0C,IAAI,CAAC1C,cAAc;MACxC,MAAMzD,aAAa,GAAGmG,IAAI,CAACnG,aAAa;MACxC,IAAI2D,cAAc,GAAGwC,IAAI,CAACxC,cAAc;MACxC,IAAIwC,IAAI,CAACrG,aAAa,GAAGqG,IAAI,CAAC1C,cAAc,IAAI2C,KAAK,CAACtG,aAAa,EAAE;QACjE2D,cAAc,GAAG2C,KAAK,CAACtG,aAAa,GAAGsG,KAAK,CAAC3C,cAAc,GAAG0C,IAAI,CAACrG,aAAa;MACpF;MACA,IAAIqG,IAAI,CAACnG,aAAa,GAAGmG,IAAI,CAACxC,cAAc,IAAIyC,KAAK,CAACpG,aAAa,EAAE;QACjE2D,cAAc,GAAGyC,KAAK,CAACpG,aAAa,GAAGoG,KAAK,CAACzC,cAAc,GAAGwC,IAAI,CAACnG,aAAa;MACpF;MACA8D,eAAe,CAAC,CAAC,CAAC,GAAG,IAAItJ,UAAU,CAACsF,aAAa,EAAE2D,cAAc,EAAEzD,aAAa,EAAE2D,cAAc,CAAC;MACjG,OAAO,IAAI;IACf,CAAC,MACI;MACDG,eAAe,CAAC,CAAC,CAAC,GAAG,IAAI;MACzB,OAAO,KAAK;IAChB;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACId,iBAAiBA,CAACb,QAAQ,EAAEU,cAAc,EAAEyD,iBAAiB,EAAE3D,YAAY,EAAE;IACzE,IAAIR,QAAQ,IAAI,CAAC,IAAIA,QAAQ,GAAGQ,YAAY,EAAE;MAC1C;MACA,OAAOR,QAAQ;IACnB;IACA;IACA;IACA,MAAMoE,cAAc,GAAGD,iBAAiB;IACxC,MAAME,cAAc,GAAG7D,YAAY,GAAG2D,iBAAiB,GAAG,CAAC;IAC3D,MAAMG,QAAQ,GAAI5D,cAAc,GAAG,CAAC,KAAK,CAAE;IAC3C,IAAIV,QAAQ,GAAG,CAAC,EAAE;MACd,MAAMuE,cAAc,GAAIH,cAAc,GAAG,CAAC,KAAK,CAAE;MACjD,OAAQE,QAAQ,KAAKC,cAAc,GAAI,CAAC,GAAG,CAAC;IAChD,CAAC,MACI;MACD,MAAMC,cAAc,GAAIH,cAAc,GAAG,CAAC,KAAK,CAAE;MACjD,OAAQC,QAAQ,KAAKE,cAAc,GAAIhE,YAAY,GAAG,CAAC,GAAGA,YAAY,GAAG,CAAC;IAC9E;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}