{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { DataTransfers } from '../../dnd.js';\nimport { $, addDisposableListener, animate, getContentHeight, getContentWidth, getTopLeftOffset, getWindow, isAncestor, isHTMLElement, isSVGElement, scheduleAtNextAnimationFrame } from '../../dom.js';\nimport { DomEmitter } from '../../event.js';\nimport { EventType as TouchEventType, Gesture } from '../../touch.js';\nimport { SmoothScrollableElement } from '../scrollbar/scrollableElement.js';\nimport { distinct, equals } from '../../../common/arrays.js';\nimport { Delayer, disposableTimeout } from '../../../common/async.js';\nimport { memoize } from '../../../common/decorators.js';\nimport { Emitter, Event } from '../../../common/event.js';\nimport { Disposable, DisposableStore, toDisposable } from '../../../common/lifecycle.js';\nimport { Range } from '../../../common/range.js';\nimport { Scrollable } from '../../../common/scrollable.js';\nimport { RangeMap, shift } from './rangeMap.js';\nimport { RowCache } from './rowCache.js';\nimport { BugIndicatingError } from '../../../common/errors.js';\nimport { clamp } from '../../../common/numbers.js';\nconst StaticDND = {\n  CurrentDragAndDropData: undefined\n};\nconst DefaultOptions = {\n  useShadows: true,\n  verticalScrollMode: 1 /* ScrollbarVisibility.Auto */,\n  setRowLineHeight: true,\n  setRowHeight: true,\n  supportDynamicHeights: false,\n  dnd: {\n    getDragElements(e) {\n      return [e];\n    },\n    getDragURI() {\n      return null;\n    },\n    onDragStart() {},\n    onDragOver() {\n      return false;\n    },\n    drop() {},\n    dispose() {}\n  },\n  horizontalScrolling: false,\n  transformOptimization: true,\n  alwaysConsumeMouseWheel: true\n};\nexport class ElementsDragAndDropData {\n  constructor(elements) {\n    this.elements = elements;\n  }\n  update() {}\n  getData() {\n    return this.elements;\n  }\n}\nexport class ExternalElementsDragAndDropData {\n  constructor(elements) {\n    this.elements = elements;\n  }\n  update() {}\n  getData() {\n    return this.elements;\n  }\n}\nexport class NativeDragAndDropData {\n  constructor() {\n    this.types = [];\n    this.files = [];\n  }\n  update(dataTransfer) {\n    if (dataTransfer.types) {\n      this.types.splice(0, this.types.length, ...dataTransfer.types);\n    }\n    if (dataTransfer.files) {\n      this.files.splice(0, this.files.length);\n      for (let i = 0; i < dataTransfer.files.length; i++) {\n        const file = dataTransfer.files.item(i);\n        if (file && (file.size || file.type)) {\n          this.files.push(file);\n        }\n      }\n    }\n  }\n  getData() {\n    return {\n      types: this.types,\n      files: this.files\n    };\n  }\n}\nfunction equalsDragFeedback(f1, f2) {\n  if (Array.isArray(f1) && Array.isArray(f2)) {\n    return equals(f1, f2);\n  }\n  return f1 === f2;\n}\nclass ListViewAccessibilityProvider {\n  constructor(accessibilityProvider) {\n    if (accessibilityProvider?.getSetSize) {\n      this.getSetSize = accessibilityProvider.getSetSize.bind(accessibilityProvider);\n    } else {\n      this.getSetSize = (e, i, l) => l;\n    }\n    if (accessibilityProvider?.getPosInSet) {\n      this.getPosInSet = accessibilityProvider.getPosInSet.bind(accessibilityProvider);\n    } else {\n      this.getPosInSet = (e, i) => i + 1;\n    }\n    if (accessibilityProvider?.getRole) {\n      this.getRole = accessibilityProvider.getRole.bind(accessibilityProvider);\n    } else {\n      this.getRole = _ => 'listitem';\n    }\n    if (accessibilityProvider?.isChecked) {\n      this.isChecked = accessibilityProvider.isChecked.bind(accessibilityProvider);\n    } else {\n      this.isChecked = _ => undefined;\n    }\n  }\n}\n/**\n * The {@link ListView} is a virtual scrolling engine.\n *\n * Given that it only renders elements within its viewport, it can hold large\n * collections of elements and stay very performant. The performance bottleneck\n * usually lies within the user's rendering code for each element.\n *\n * @remarks It is a low-level widget, not meant to be used directly. Refer to the\n * List widget instead.\n */\nexport class ListView {\n  static {\n    this.InstanceCount = 0;\n  }\n  get contentHeight() {\n    return this.rangeMap.size;\n  }\n  get onDidScroll() {\n    return this.scrollableElement.onScroll;\n  }\n  get scrollableElementDomNode() {\n    return this.scrollableElement.getDomNode();\n  }\n  get horizontalScrolling() {\n    return this._horizontalScrolling;\n  }\n  set horizontalScrolling(value) {\n    if (value === this._horizontalScrolling) {\n      return;\n    }\n    if (value && this.supportDynamicHeights) {\n      throw new Error('Horizontal scrolling and dynamic heights not supported simultaneously');\n    }\n    this._horizontalScrolling = value;\n    this.domNode.classList.toggle('horizontal-scrolling', this._horizontalScrolling);\n    if (this._horizontalScrolling) {\n      for (const item of this.items) {\n        this.measureItemWidth(item);\n      }\n      this.updateScrollWidth();\n      this.scrollableElement.setScrollDimensions({\n        width: getContentWidth(this.domNode)\n      });\n      this.rowsContainer.style.width = `${Math.max(this.scrollWidth || 0, this.renderWidth)}px`;\n    } else {\n      this.scrollableElementWidthDelayer.cancel();\n      this.scrollableElement.setScrollDimensions({\n        width: this.renderWidth,\n        scrollWidth: this.renderWidth\n      });\n      this.rowsContainer.style.width = '';\n    }\n  }\n  constructor(container, virtualDelegate, renderers, options = DefaultOptions) {\n    this.virtualDelegate = virtualDelegate;\n    this.domId = `list_id_${++ListView.InstanceCount}`;\n    this.renderers = new Map();\n    this.renderWidth = 0;\n    this._scrollHeight = 0;\n    this.scrollableElementUpdateDisposable = null;\n    this.scrollableElementWidthDelayer = new Delayer(50);\n    this.splicing = false;\n    this.dragOverAnimationStopDisposable = Disposable.None;\n    this.dragOverMouseY = 0;\n    this.canDrop = false;\n    this.currentDragFeedbackDisposable = Disposable.None;\n    this.onDragLeaveTimeout = Disposable.None;\n    this.disposables = new DisposableStore();\n    this._onDidChangeContentHeight = new Emitter();\n    this._onDidChangeContentWidth = new Emitter();\n    this.onDidChangeContentHeight = Event.latch(this._onDidChangeContentHeight.event, undefined, this.disposables);\n    this._horizontalScrolling = false;\n    if (options.horizontalScrolling && options.supportDynamicHeights) {\n      throw new Error('Horizontal scrolling and dynamic heights not supported simultaneously');\n    }\n    this.items = [];\n    this.itemId = 0;\n    this.rangeMap = this.createRangeMap(options.paddingTop ?? 0);\n    for (const renderer of renderers) {\n      this.renderers.set(renderer.templateId, renderer);\n    }\n    this.cache = this.disposables.add(new RowCache(this.renderers));\n    this.lastRenderTop = 0;\n    this.lastRenderHeight = 0;\n    this.domNode = document.createElement('div');\n    this.domNode.className = 'monaco-list';\n    this.domNode.classList.add(this.domId);\n    this.domNode.tabIndex = 0;\n    this.domNode.classList.toggle('mouse-support', typeof options.mouseSupport === 'boolean' ? options.mouseSupport : true);\n    this._horizontalScrolling = options.horizontalScrolling ?? DefaultOptions.horizontalScrolling;\n    this.domNode.classList.toggle('horizontal-scrolling', this._horizontalScrolling);\n    this.paddingBottom = typeof options.paddingBottom === 'undefined' ? 0 : options.paddingBottom;\n    this.accessibilityProvider = new ListViewAccessibilityProvider(options.accessibilityProvider);\n    this.rowsContainer = document.createElement('div');\n    this.rowsContainer.className = 'monaco-list-rows';\n    const transformOptimization = options.transformOptimization ?? DefaultOptions.transformOptimization;\n    if (transformOptimization) {\n      this.rowsContainer.style.transform = 'translate3d(0px, 0px, 0px)';\n      this.rowsContainer.style.overflow = 'hidden';\n      this.rowsContainer.style.contain = 'strict';\n    }\n    this.disposables.add(Gesture.addTarget(this.rowsContainer));\n    this.scrollable = this.disposables.add(new Scrollable({\n      forceIntegerValues: true,\n      smoothScrollDuration: options.smoothScrolling ?? false ? 125 : 0,\n      scheduleAtNextAnimationFrame: cb => scheduleAtNextAnimationFrame(getWindow(this.domNode), cb)\n    }));\n    this.scrollableElement = this.disposables.add(new SmoothScrollableElement(this.rowsContainer, {\n      alwaysConsumeMouseWheel: options.alwaysConsumeMouseWheel ?? DefaultOptions.alwaysConsumeMouseWheel,\n      horizontal: 1 /* ScrollbarVisibility.Auto */,\n      vertical: options.verticalScrollMode ?? DefaultOptions.verticalScrollMode,\n      useShadows: options.useShadows ?? DefaultOptions.useShadows,\n      mouseWheelScrollSensitivity: options.mouseWheelScrollSensitivity,\n      fastScrollSensitivity: options.fastScrollSensitivity,\n      scrollByPage: options.scrollByPage\n    }, this.scrollable));\n    this.domNode.appendChild(this.scrollableElement.getDomNode());\n    container.appendChild(this.domNode);\n    this.scrollableElement.onScroll(this.onScroll, this, this.disposables);\n    this.disposables.add(addDisposableListener(this.rowsContainer, TouchEventType.Change, e => this.onTouchChange(e)));\n    // Prevent the monaco-scrollable-element from scrolling\n    // https://github.com/microsoft/vscode/issues/44181\n    this.disposables.add(addDisposableListener(this.scrollableElement.getDomNode(), 'scroll', e => e.target.scrollTop = 0));\n    this.disposables.add(addDisposableListener(this.domNode, 'dragover', e => this.onDragOver(this.toDragEvent(e))));\n    this.disposables.add(addDisposableListener(this.domNode, 'drop', e => this.onDrop(this.toDragEvent(e))));\n    this.disposables.add(addDisposableListener(this.domNode, 'dragleave', e => this.onDragLeave(this.toDragEvent(e))));\n    this.disposables.add(addDisposableListener(this.domNode, 'dragend', e => this.onDragEnd(e)));\n    this.setRowLineHeight = options.setRowLineHeight ?? DefaultOptions.setRowLineHeight;\n    this.setRowHeight = options.setRowHeight ?? DefaultOptions.setRowHeight;\n    this.supportDynamicHeights = options.supportDynamicHeights ?? DefaultOptions.supportDynamicHeights;\n    this.dnd = options.dnd ?? this.disposables.add(DefaultOptions.dnd);\n    this.layout(options.initialSize?.height, options.initialSize?.width);\n  }\n  updateOptions(options) {\n    if (options.paddingBottom !== undefined) {\n      this.paddingBottom = options.paddingBottom;\n      this.scrollableElement.setScrollDimensions({\n        scrollHeight: this.scrollHeight\n      });\n    }\n    if (options.smoothScrolling !== undefined) {\n      this.scrollable.setSmoothScrollDuration(options.smoothScrolling ? 125 : 0);\n    }\n    if (options.horizontalScrolling !== undefined) {\n      this.horizontalScrolling = options.horizontalScrolling;\n    }\n    let scrollableOptions;\n    if (options.scrollByPage !== undefined) {\n      scrollableOptions = {\n        ...(scrollableOptions ?? {}),\n        scrollByPage: options.scrollByPage\n      };\n    }\n    if (options.mouseWheelScrollSensitivity !== undefined) {\n      scrollableOptions = {\n        ...(scrollableOptions ?? {}),\n        mouseWheelScrollSensitivity: options.mouseWheelScrollSensitivity\n      };\n    }\n    if (options.fastScrollSensitivity !== undefined) {\n      scrollableOptions = {\n        ...(scrollableOptions ?? {}),\n        fastScrollSensitivity: options.fastScrollSensitivity\n      };\n    }\n    if (scrollableOptions) {\n      this.scrollableElement.updateOptions(scrollableOptions);\n    }\n    if (options.paddingTop !== undefined && options.paddingTop !== this.rangeMap.paddingTop) {\n      // trigger a rerender\n      const lastRenderRange = this.getRenderRange(this.lastRenderTop, this.lastRenderHeight);\n      const offset = options.paddingTop - this.rangeMap.paddingTop;\n      this.rangeMap.paddingTop = options.paddingTop;\n      this.render(lastRenderRange, Math.max(0, this.lastRenderTop + offset), this.lastRenderHeight, undefined, undefined, true);\n      this.setScrollTop(this.lastRenderTop);\n      this.eventuallyUpdateScrollDimensions();\n      if (this.supportDynamicHeights) {\n        this._rerender(this.lastRenderTop, this.lastRenderHeight);\n      }\n    }\n  }\n  createRangeMap(paddingTop) {\n    return new RangeMap(paddingTop);\n  }\n  splice(start, deleteCount, elements = []) {\n    if (this.splicing) {\n      throw new Error('Can\\'t run recursive splices.');\n    }\n    this.splicing = true;\n    try {\n      return this._splice(start, deleteCount, elements);\n    } finally {\n      this.splicing = false;\n      this._onDidChangeContentHeight.fire(this.contentHeight);\n    }\n  }\n  _splice(start, deleteCount, elements = []) {\n    const previousRenderRange = this.getRenderRange(this.lastRenderTop, this.lastRenderHeight);\n    const deleteRange = {\n      start,\n      end: start + deleteCount\n    };\n    const removeRange = Range.intersect(previousRenderRange, deleteRange);\n    // try to reuse rows, avoid removing them from DOM\n    const rowsToDispose = new Map();\n    for (let i = removeRange.end - 1; i >= removeRange.start; i--) {\n      const item = this.items[i];\n      item.dragStartDisposable.dispose();\n      item.checkedDisposable.dispose();\n      if (item.row) {\n        let rows = rowsToDispose.get(item.templateId);\n        if (!rows) {\n          rows = [];\n          rowsToDispose.set(item.templateId, rows);\n        }\n        const renderer = this.renderers.get(item.templateId);\n        if (renderer && renderer.disposeElement) {\n          renderer.disposeElement(item.element, i, item.row.templateData, item.size);\n        }\n        rows.unshift(item.row);\n      }\n      item.row = null;\n      item.stale = true;\n    }\n    const previousRestRange = {\n      start: start + deleteCount,\n      end: this.items.length\n    };\n    const previousRenderedRestRange = Range.intersect(previousRestRange, previousRenderRange);\n    const previousUnrenderedRestRanges = Range.relativeComplement(previousRestRange, previousRenderRange);\n    const inserted = elements.map(element => ({\n      id: String(this.itemId++),\n      element,\n      templateId: this.virtualDelegate.getTemplateId(element),\n      size: this.virtualDelegate.getHeight(element),\n      width: undefined,\n      hasDynamicHeight: !!this.virtualDelegate.hasDynamicHeight && this.virtualDelegate.hasDynamicHeight(element),\n      lastDynamicHeightWidth: undefined,\n      row: null,\n      uri: undefined,\n      dropTarget: false,\n      dragStartDisposable: Disposable.None,\n      checkedDisposable: Disposable.None,\n      stale: false\n    }));\n    let deleted;\n    // TODO@joao: improve this optimization to catch even more cases\n    if (start === 0 && deleteCount >= this.items.length) {\n      this.rangeMap = this.createRangeMap(this.rangeMap.paddingTop);\n      this.rangeMap.splice(0, 0, inserted);\n      deleted = this.items;\n      this.items = inserted;\n    } else {\n      this.rangeMap.splice(start, deleteCount, inserted);\n      deleted = this.items.splice(start, deleteCount, ...inserted);\n    }\n    const delta = elements.length - deleteCount;\n    const renderRange = this.getRenderRange(this.lastRenderTop, this.lastRenderHeight);\n    const renderedRestRange = shift(previousRenderedRestRange, delta);\n    const updateRange = Range.intersect(renderRange, renderedRestRange);\n    for (let i = updateRange.start; i < updateRange.end; i++) {\n      this.updateItemInDOM(this.items[i], i);\n    }\n    const removeRanges = Range.relativeComplement(renderedRestRange, renderRange);\n    for (const range of removeRanges) {\n      for (let i = range.start; i < range.end; i++) {\n        this.removeItemFromDOM(i);\n      }\n    }\n    const unrenderedRestRanges = previousUnrenderedRestRanges.map(r => shift(r, delta));\n    const elementsRange = {\n      start,\n      end: start + elements.length\n    };\n    const insertRanges = [elementsRange, ...unrenderedRestRanges].map(r => Range.intersect(renderRange, r)).reverse();\n    for (const range of insertRanges) {\n      for (let i = range.end - 1; i >= range.start; i--) {\n        const item = this.items[i];\n        const rows = rowsToDispose.get(item.templateId);\n        const row = rows?.pop();\n        this.insertItemInDOM(i, row);\n      }\n    }\n    for (const rows of rowsToDispose.values()) {\n      for (const row of rows) {\n        this.cache.release(row);\n      }\n    }\n    this.eventuallyUpdateScrollDimensions();\n    if (this.supportDynamicHeights) {\n      this._rerender(this.scrollTop, this.renderHeight);\n    }\n    return deleted.map(i => i.element);\n  }\n  eventuallyUpdateScrollDimensions() {\n    this._scrollHeight = this.contentHeight;\n    this.rowsContainer.style.height = `${this._scrollHeight}px`;\n    if (!this.scrollableElementUpdateDisposable) {\n      this.scrollableElementUpdateDisposable = scheduleAtNextAnimationFrame(getWindow(this.domNode), () => {\n        this.scrollableElement.setScrollDimensions({\n          scrollHeight: this.scrollHeight\n        });\n        this.updateScrollWidth();\n        this.scrollableElementUpdateDisposable = null;\n      });\n    }\n  }\n  eventuallyUpdateScrollWidth() {\n    if (!this.horizontalScrolling) {\n      this.scrollableElementWidthDelayer.cancel();\n      return;\n    }\n    this.scrollableElementWidthDelayer.trigger(() => this.updateScrollWidth());\n  }\n  updateScrollWidth() {\n    if (!this.horizontalScrolling) {\n      return;\n    }\n    let scrollWidth = 0;\n    for (const item of this.items) {\n      if (typeof item.width !== 'undefined') {\n        scrollWidth = Math.max(scrollWidth, item.width);\n      }\n    }\n    this.scrollWidth = scrollWidth;\n    this.scrollableElement.setScrollDimensions({\n      scrollWidth: scrollWidth === 0 ? 0 : scrollWidth + 10\n    });\n    this._onDidChangeContentWidth.fire(this.scrollWidth);\n  }\n  rerender() {\n    if (!this.supportDynamicHeights) {\n      return;\n    }\n    for (const item of this.items) {\n      item.lastDynamicHeightWidth = undefined;\n    }\n    this._rerender(this.lastRenderTop, this.lastRenderHeight);\n  }\n  get length() {\n    return this.items.length;\n  }\n  get renderHeight() {\n    const scrollDimensions = this.scrollableElement.getScrollDimensions();\n    return scrollDimensions.height;\n  }\n  get firstVisibleIndex() {\n    const range = this.getRenderRange(this.lastRenderTop, this.lastRenderHeight);\n    return range.start;\n  }\n  element(index) {\n    return this.items[index].element;\n  }\n  indexOf(element) {\n    return this.items.findIndex(item => item.element === element);\n  }\n  domElement(index) {\n    const row = this.items[index].row;\n    return row && row.domNode;\n  }\n  elementHeight(index) {\n    return this.items[index].size;\n  }\n  elementTop(index) {\n    return this.rangeMap.positionAt(index);\n  }\n  indexAt(position) {\n    return this.rangeMap.indexAt(position);\n  }\n  indexAfter(position) {\n    return this.rangeMap.indexAfter(position);\n  }\n  layout(height, width) {\n    const scrollDimensions = {\n      height: typeof height === 'number' ? height : getContentHeight(this.domNode)\n    };\n    if (this.scrollableElementUpdateDisposable) {\n      this.scrollableElementUpdateDisposable.dispose();\n      this.scrollableElementUpdateDisposable = null;\n      scrollDimensions.scrollHeight = this.scrollHeight;\n    }\n    this.scrollableElement.setScrollDimensions(scrollDimensions);\n    if (typeof width !== 'undefined') {\n      this.renderWidth = width;\n      if (this.supportDynamicHeights) {\n        this._rerender(this.scrollTop, this.renderHeight);\n      }\n    }\n    if (this.horizontalScrolling) {\n      this.scrollableElement.setScrollDimensions({\n        width: typeof width === 'number' ? width : getContentWidth(this.domNode)\n      });\n    }\n  }\n  // Render\n  render(previousRenderRange, renderTop, renderHeight, renderLeft, scrollWidth, updateItemsInDOM = false) {\n    const renderRange = this.getRenderRange(renderTop, renderHeight);\n    const rangesToInsert = Range.relativeComplement(renderRange, previousRenderRange).reverse();\n    const rangesToRemove = Range.relativeComplement(previousRenderRange, renderRange);\n    if (updateItemsInDOM) {\n      const rangesToUpdate = Range.intersect(previousRenderRange, renderRange);\n      for (let i = rangesToUpdate.start; i < rangesToUpdate.end; i++) {\n        this.updateItemInDOM(this.items[i], i);\n      }\n    }\n    this.cache.transact(() => {\n      for (const range of rangesToRemove) {\n        for (let i = range.start; i < range.end; i++) {\n          this.removeItemFromDOM(i);\n        }\n      }\n      for (const range of rangesToInsert) {\n        for (let i = range.end - 1; i >= range.start; i--) {\n          this.insertItemInDOM(i);\n        }\n      }\n    });\n    if (renderLeft !== undefined) {\n      this.rowsContainer.style.left = `-${renderLeft}px`;\n    }\n    this.rowsContainer.style.top = `-${renderTop}px`;\n    if (this.horizontalScrolling && scrollWidth !== undefined) {\n      this.rowsContainer.style.width = `${Math.max(scrollWidth, this.renderWidth)}px`;\n    }\n    this.lastRenderTop = renderTop;\n    this.lastRenderHeight = renderHeight;\n  }\n  // DOM operations\n  insertItemInDOM(index, row) {\n    const item = this.items[index];\n    if (!item.row) {\n      if (row) {\n        item.row = row;\n        item.stale = true;\n      } else {\n        const result = this.cache.alloc(item.templateId);\n        item.row = result.row;\n        item.stale ||= result.isReusingConnectedDomNode;\n      }\n    }\n    const role = this.accessibilityProvider.getRole(item.element) || 'listitem';\n    item.row.domNode.setAttribute('role', role);\n    const checked = this.accessibilityProvider.isChecked(item.element);\n    if (typeof checked === 'boolean') {\n      item.row.domNode.setAttribute('aria-checked', String(!!checked));\n    } else if (checked) {\n      const update = checked => item.row.domNode.setAttribute('aria-checked', String(!!checked));\n      update(checked.value);\n      item.checkedDisposable = checked.onDidChange(() => update(checked.value));\n    }\n    if (item.stale || !item.row.domNode.parentElement) {\n      const referenceNode = this.items.at(index + 1)?.row?.domNode ?? null;\n      if (item.row.domNode.parentElement !== this.rowsContainer || item.row.domNode.nextElementSibling !== referenceNode) {\n        this.rowsContainer.insertBefore(item.row.domNode, referenceNode);\n      }\n      item.stale = false;\n    }\n    this.updateItemInDOM(item, index);\n    const renderer = this.renderers.get(item.templateId);\n    if (!renderer) {\n      throw new Error(`No renderer found for template id ${item.templateId}`);\n    }\n    renderer?.renderElement(item.element, index, item.row.templateData, item.size);\n    const uri = this.dnd.getDragURI(item.element);\n    item.dragStartDisposable.dispose();\n    item.row.domNode.draggable = !!uri;\n    if (uri) {\n      item.dragStartDisposable = addDisposableListener(item.row.domNode, 'dragstart', event => this.onDragStart(item.element, uri, event));\n    }\n    if (this.horizontalScrolling) {\n      this.measureItemWidth(item);\n      this.eventuallyUpdateScrollWidth();\n    }\n  }\n  measureItemWidth(item) {\n    if (!item.row || !item.row.domNode) {\n      return;\n    }\n    item.row.domNode.style.width = 'fit-content';\n    item.width = getContentWidth(item.row.domNode);\n    const style = getWindow(item.row.domNode).getComputedStyle(item.row.domNode);\n    if (style.paddingLeft) {\n      item.width += parseFloat(style.paddingLeft);\n    }\n    if (style.paddingRight) {\n      item.width += parseFloat(style.paddingRight);\n    }\n    item.row.domNode.style.width = '';\n  }\n  updateItemInDOM(item, index) {\n    item.row.domNode.style.top = `${this.elementTop(index)}px`;\n    if (this.setRowHeight) {\n      item.row.domNode.style.height = `${item.size}px`;\n    }\n    if (this.setRowLineHeight) {\n      item.row.domNode.style.lineHeight = `${item.size}px`;\n    }\n    item.row.domNode.setAttribute('data-index', `${index}`);\n    item.row.domNode.setAttribute('data-last-element', index === this.length - 1 ? 'true' : 'false');\n    item.row.domNode.setAttribute('data-parity', index % 2 === 0 ? 'even' : 'odd');\n    item.row.domNode.setAttribute('aria-setsize', String(this.accessibilityProvider.getSetSize(item.element, index, this.length)));\n    item.row.domNode.setAttribute('aria-posinset', String(this.accessibilityProvider.getPosInSet(item.element, index)));\n    item.row.domNode.setAttribute('id', this.getElementDomId(index));\n    item.row.domNode.classList.toggle('drop-target', item.dropTarget);\n  }\n  removeItemFromDOM(index) {\n    const item = this.items[index];\n    item.dragStartDisposable.dispose();\n    item.checkedDisposable.dispose();\n    if (item.row) {\n      const renderer = this.renderers.get(item.templateId);\n      if (renderer && renderer.disposeElement) {\n        renderer.disposeElement(item.element, index, item.row.templateData, item.size);\n      }\n      this.cache.release(item.row);\n      item.row = null;\n    }\n    if (this.horizontalScrolling) {\n      this.eventuallyUpdateScrollWidth();\n    }\n  }\n  getScrollTop() {\n    const scrollPosition = this.scrollableElement.getScrollPosition();\n    return scrollPosition.scrollTop;\n  }\n  setScrollTop(scrollTop, reuseAnimation) {\n    if (this.scrollableElementUpdateDisposable) {\n      this.scrollableElementUpdateDisposable.dispose();\n      this.scrollableElementUpdateDisposable = null;\n      this.scrollableElement.setScrollDimensions({\n        scrollHeight: this.scrollHeight\n      });\n    }\n    this.scrollableElement.setScrollPosition({\n      scrollTop,\n      reuseAnimation\n    });\n  }\n  get scrollTop() {\n    return this.getScrollTop();\n  }\n  set scrollTop(scrollTop) {\n    this.setScrollTop(scrollTop);\n  }\n  get scrollHeight() {\n    return this._scrollHeight + (this.horizontalScrolling ? 10 : 0) + this.paddingBottom;\n  }\n  // Events\n  get onMouseClick() {\n    return Event.map(this.disposables.add(new DomEmitter(this.domNode, 'click')).event, e => this.toMouseEvent(e), this.disposables);\n  }\n  get onMouseDblClick() {\n    return Event.map(this.disposables.add(new DomEmitter(this.domNode, 'dblclick')).event, e => this.toMouseEvent(e), this.disposables);\n  }\n  get onMouseMiddleClick() {\n    return Event.filter(Event.map(this.disposables.add(new DomEmitter(this.domNode, 'auxclick')).event, e => this.toMouseEvent(e), this.disposables), e => e.browserEvent.button === 1, this.disposables);\n  }\n  get onMouseDown() {\n    return Event.map(this.disposables.add(new DomEmitter(this.domNode, 'mousedown')).event, e => this.toMouseEvent(e), this.disposables);\n  }\n  get onMouseOver() {\n    return Event.map(this.disposables.add(new DomEmitter(this.domNode, 'mouseover')).event, e => this.toMouseEvent(e), this.disposables);\n  }\n  get onMouseOut() {\n    return Event.map(this.disposables.add(new DomEmitter(this.domNode, 'mouseout')).event, e => this.toMouseEvent(e), this.disposables);\n  }\n  get onContextMenu() {\n    return Event.any(Event.map(this.disposables.add(new DomEmitter(this.domNode, 'contextmenu')).event, e => this.toMouseEvent(e), this.disposables), Event.map(this.disposables.add(new DomEmitter(this.domNode, TouchEventType.Contextmenu)).event, e => this.toGestureEvent(e), this.disposables));\n  }\n  get onTouchStart() {\n    return Event.map(this.disposables.add(new DomEmitter(this.domNode, 'touchstart')).event, e => this.toTouchEvent(e), this.disposables);\n  }\n  get onTap() {\n    return Event.map(this.disposables.add(new DomEmitter(this.rowsContainer, TouchEventType.Tap)).event, e => this.toGestureEvent(e), this.disposables);\n  }\n  toMouseEvent(browserEvent) {\n    const index = this.getItemIndexFromEventTarget(browserEvent.target || null);\n    const item = typeof index === 'undefined' ? undefined : this.items[index];\n    const element = item && item.element;\n    return {\n      browserEvent,\n      index,\n      element\n    };\n  }\n  toTouchEvent(browserEvent) {\n    const index = this.getItemIndexFromEventTarget(browserEvent.target || null);\n    const item = typeof index === 'undefined' ? undefined : this.items[index];\n    const element = item && item.element;\n    return {\n      browserEvent,\n      index,\n      element\n    };\n  }\n  toGestureEvent(browserEvent) {\n    const index = this.getItemIndexFromEventTarget(browserEvent.initialTarget || null);\n    const item = typeof index === 'undefined' ? undefined : this.items[index];\n    const element = item && item.element;\n    return {\n      browserEvent,\n      index,\n      element\n    };\n  }\n  toDragEvent(browserEvent) {\n    const index = this.getItemIndexFromEventTarget(browserEvent.target || null);\n    const item = typeof index === 'undefined' ? undefined : this.items[index];\n    const element = item && item.element;\n    const sector = this.getTargetSector(browserEvent, index);\n    return {\n      browserEvent,\n      index,\n      element,\n      sector\n    };\n  }\n  onScroll(e) {\n    try {\n      const previousRenderRange = this.getRenderRange(this.lastRenderTop, this.lastRenderHeight);\n      this.render(previousRenderRange, e.scrollTop, e.height, e.scrollLeft, e.scrollWidth);\n      if (this.supportDynamicHeights) {\n        this._rerender(e.scrollTop, e.height, e.inSmoothScrolling);\n      }\n    } catch (err) {\n      console.error('Got bad scroll event:', e);\n      throw err;\n    }\n  }\n  onTouchChange(event) {\n    event.preventDefault();\n    event.stopPropagation();\n    this.scrollTop -= event.translationY;\n  }\n  // DND\n  onDragStart(element, uri, event) {\n    if (!event.dataTransfer) {\n      return;\n    }\n    const elements = this.dnd.getDragElements(element);\n    event.dataTransfer.effectAllowed = 'copyMove';\n    event.dataTransfer.setData(DataTransfers.TEXT, uri);\n    if (event.dataTransfer.setDragImage) {\n      let label;\n      if (this.dnd.getDragLabel) {\n        label = this.dnd.getDragLabel(elements, event);\n      }\n      if (typeof label === 'undefined') {\n        label = String(elements.length);\n      }\n      const dragImage = $('.monaco-drag-image');\n      dragImage.textContent = label;\n      const getDragImageContainer = e => {\n        while (e && !e.classList.contains('monaco-workbench')) {\n          e = e.parentElement;\n        }\n        return e || this.domNode.ownerDocument;\n      };\n      const container = getDragImageContainer(this.domNode);\n      container.appendChild(dragImage);\n      event.dataTransfer.setDragImage(dragImage, -10, -10);\n      setTimeout(() => dragImage.remove(), 0);\n    }\n    this.domNode.classList.add('dragging');\n    this.currentDragData = new ElementsDragAndDropData(elements);\n    StaticDND.CurrentDragAndDropData = new ExternalElementsDragAndDropData(elements);\n    this.dnd.onDragStart?.(this.currentDragData, event);\n  }\n  onDragOver(event) {\n    event.browserEvent.preventDefault(); // needed so that the drop event fires (https://stackoverflow.com/questions/21339924/drop-event-not-firing-in-chrome)\n    this.onDragLeaveTimeout.dispose();\n    if (StaticDND.CurrentDragAndDropData && StaticDND.CurrentDragAndDropData.getData() === 'vscode-ui') {\n      return false;\n    }\n    this.setupDragAndDropScrollTopAnimation(event.browserEvent);\n    if (!event.browserEvent.dataTransfer) {\n      return false;\n    }\n    // Drag over from outside\n    if (!this.currentDragData) {\n      if (StaticDND.CurrentDragAndDropData) {\n        // Drag over from another list\n        this.currentDragData = StaticDND.CurrentDragAndDropData;\n      } else {\n        // Drag over from the desktop\n        if (!event.browserEvent.dataTransfer.types) {\n          return false;\n        }\n        this.currentDragData = new NativeDragAndDropData();\n      }\n    }\n    const result = this.dnd.onDragOver(this.currentDragData, event.element, event.index, event.sector, event.browserEvent);\n    this.canDrop = typeof result === 'boolean' ? result : result.accept;\n    if (!this.canDrop) {\n      this.currentDragFeedback = undefined;\n      this.currentDragFeedbackDisposable.dispose();\n      return false;\n    }\n    event.browserEvent.dataTransfer.dropEffect = typeof result !== 'boolean' && result.effect?.type === 0 /* ListDragOverEffectType.Copy */ ? 'copy' : 'move';\n    let feedback;\n    if (typeof result !== 'boolean' && result.feedback) {\n      feedback = result.feedback;\n    } else {\n      if (typeof event.index === 'undefined') {\n        feedback = [-1];\n      } else {\n        feedback = [event.index];\n      }\n    }\n    // sanitize feedback list\n    feedback = distinct(feedback).filter(i => i >= -1 && i < this.length).sort((a, b) => a - b);\n    feedback = feedback[0] === -1 ? [-1] : feedback;\n    let dragOverEffectPosition = typeof result !== 'boolean' && result.effect && result.effect.position ? result.effect.position : \"drop-target\" /* ListDragOverEffectPosition.Over */;\n    if (equalsDragFeedback(this.currentDragFeedback, feedback) && this.currentDragFeedbackPosition === dragOverEffectPosition) {\n      return true;\n    }\n    this.currentDragFeedback = feedback;\n    this.currentDragFeedbackPosition = dragOverEffectPosition;\n    this.currentDragFeedbackDisposable.dispose();\n    if (feedback[0] === -1) {\n      // entire list feedback\n      this.domNode.classList.add(dragOverEffectPosition);\n      this.rowsContainer.classList.add(dragOverEffectPosition);\n      this.currentDragFeedbackDisposable = toDisposable(() => {\n        this.domNode.classList.remove(dragOverEffectPosition);\n        this.rowsContainer.classList.remove(dragOverEffectPosition);\n      });\n    } else {\n      if (feedback.length > 1 && dragOverEffectPosition !== \"drop-target\" /* ListDragOverEffectPosition.Over */) {\n        throw new Error('Can\\'t use multiple feedbacks with position different than \\'over\\'');\n      }\n      // Make sure there is no flicker when moving between two items\n      // Always use the before feedback if possible\n      if (dragOverEffectPosition === \"drop-target-after\" /* ListDragOverEffectPosition.After */) {\n        if (feedback[0] < this.length - 1) {\n          feedback[0] += 1;\n          dragOverEffectPosition = \"drop-target-before\" /* ListDragOverEffectPosition.Before */;\n        }\n      }\n      for (const index of feedback) {\n        const item = this.items[index];\n        item.dropTarget = true;\n        item.row?.domNode.classList.add(dragOverEffectPosition);\n      }\n      this.currentDragFeedbackDisposable = toDisposable(() => {\n        for (const index of feedback) {\n          const item = this.items[index];\n          item.dropTarget = false;\n          item.row?.domNode.classList.remove(dragOverEffectPosition);\n        }\n      });\n    }\n    return true;\n  }\n  onDragLeave(event) {\n    this.onDragLeaveTimeout.dispose();\n    this.onDragLeaveTimeout = disposableTimeout(() => this.clearDragOverFeedback(), 100, this.disposables);\n    if (this.currentDragData) {\n      this.dnd.onDragLeave?.(this.currentDragData, event.element, event.index, event.browserEvent);\n    }\n  }\n  onDrop(event) {\n    if (!this.canDrop) {\n      return;\n    }\n    const dragData = this.currentDragData;\n    this.teardownDragAndDropScrollTopAnimation();\n    this.clearDragOverFeedback();\n    this.domNode.classList.remove('dragging');\n    this.currentDragData = undefined;\n    StaticDND.CurrentDragAndDropData = undefined;\n    if (!dragData || !event.browserEvent.dataTransfer) {\n      return;\n    }\n    event.browserEvent.preventDefault();\n    dragData.update(event.browserEvent.dataTransfer);\n    this.dnd.drop(dragData, event.element, event.index, event.sector, event.browserEvent);\n  }\n  onDragEnd(event) {\n    this.canDrop = false;\n    this.teardownDragAndDropScrollTopAnimation();\n    this.clearDragOverFeedback();\n    this.domNode.classList.remove('dragging');\n    this.currentDragData = undefined;\n    StaticDND.CurrentDragAndDropData = undefined;\n    this.dnd.onDragEnd?.(event);\n  }\n  clearDragOverFeedback() {\n    this.currentDragFeedback = undefined;\n    this.currentDragFeedbackPosition = undefined;\n    this.currentDragFeedbackDisposable.dispose();\n    this.currentDragFeedbackDisposable = Disposable.None;\n  }\n  // DND scroll top animation\n  setupDragAndDropScrollTopAnimation(event) {\n    if (!this.dragOverAnimationDisposable) {\n      const viewTop = getTopLeftOffset(this.domNode).top;\n      this.dragOverAnimationDisposable = animate(getWindow(this.domNode), this.animateDragAndDropScrollTop.bind(this, viewTop));\n    }\n    this.dragOverAnimationStopDisposable.dispose();\n    this.dragOverAnimationStopDisposable = disposableTimeout(() => {\n      if (this.dragOverAnimationDisposable) {\n        this.dragOverAnimationDisposable.dispose();\n        this.dragOverAnimationDisposable = undefined;\n      }\n    }, 1000, this.disposables);\n    this.dragOverMouseY = event.pageY;\n  }\n  animateDragAndDropScrollTop(viewTop) {\n    if (this.dragOverMouseY === undefined) {\n      return;\n    }\n    const diff = this.dragOverMouseY - viewTop;\n    const upperLimit = this.renderHeight - 35;\n    if (diff < 35) {\n      this.scrollTop += Math.max(-14, Math.floor(0.3 * (diff - 35)));\n    } else if (diff > upperLimit) {\n      this.scrollTop += Math.min(14, Math.floor(0.3 * (diff - upperLimit)));\n    }\n  }\n  teardownDragAndDropScrollTopAnimation() {\n    this.dragOverAnimationStopDisposable.dispose();\n    if (this.dragOverAnimationDisposable) {\n      this.dragOverAnimationDisposable.dispose();\n      this.dragOverAnimationDisposable = undefined;\n    }\n  }\n  // Util\n  getTargetSector(browserEvent, targetIndex) {\n    if (targetIndex === undefined) {\n      return undefined;\n    }\n    const relativePosition = browserEvent.offsetY / this.items[targetIndex].size;\n    const sector = Math.floor(relativePosition / 0.25);\n    return clamp(sector, 0, 3);\n  }\n  getItemIndexFromEventTarget(target) {\n    const scrollableElement = this.scrollableElement.getDomNode();\n    let element = target;\n    while ((isHTMLElement(element) || isSVGElement(element)) && element !== this.rowsContainer && scrollableElement.contains(element)) {\n      const rawIndex = element.getAttribute('data-index');\n      if (rawIndex) {\n        const index = Number(rawIndex);\n        if (!isNaN(index)) {\n          return index;\n        }\n      }\n      element = element.parentElement;\n    }\n    return undefined;\n  }\n  getRenderRange(renderTop, renderHeight) {\n    return {\n      start: this.rangeMap.indexAt(renderTop),\n      end: this.rangeMap.indexAfter(renderTop + renderHeight - 1)\n    };\n  }\n  /**\n   * Given a stable rendered state, checks every rendered element whether it needs\n   * to be probed for dynamic height. Adjusts scroll height and top if necessary.\n   */\n  _rerender(renderTop, renderHeight, inSmoothScrolling) {\n    const previousRenderRange = this.getRenderRange(renderTop, renderHeight);\n    // Let's remember the second element's position, this helps in scrolling up\n    // and preserving a linear upwards scroll movement\n    let anchorElementIndex;\n    let anchorElementTopDelta;\n    if (renderTop === this.elementTop(previousRenderRange.start)) {\n      anchorElementIndex = previousRenderRange.start;\n      anchorElementTopDelta = 0;\n    } else if (previousRenderRange.end - previousRenderRange.start > 1) {\n      anchorElementIndex = previousRenderRange.start + 1;\n      anchorElementTopDelta = this.elementTop(anchorElementIndex) - renderTop;\n    }\n    let heightDiff = 0;\n    while (true) {\n      const renderRange = this.getRenderRange(renderTop, renderHeight);\n      let didChange = false;\n      for (let i = renderRange.start; i < renderRange.end; i++) {\n        const diff = this.probeDynamicHeight(i);\n        if (diff !== 0) {\n          this.rangeMap.splice(i, 1, [this.items[i]]);\n        }\n        heightDiff += diff;\n        didChange = didChange || diff !== 0;\n      }\n      if (!didChange) {\n        if (heightDiff !== 0) {\n          this.eventuallyUpdateScrollDimensions();\n        }\n        const unrenderRanges = Range.relativeComplement(previousRenderRange, renderRange);\n        for (const range of unrenderRanges) {\n          for (let i = range.start; i < range.end; i++) {\n            if (this.items[i].row) {\n              this.removeItemFromDOM(i);\n            }\n          }\n        }\n        const renderRanges = Range.relativeComplement(renderRange, previousRenderRange).reverse();\n        for (const range of renderRanges) {\n          for (let i = range.end - 1; i >= range.start; i--) {\n            this.insertItemInDOM(i);\n          }\n        }\n        for (let i = renderRange.start; i < renderRange.end; i++) {\n          if (this.items[i].row) {\n            this.updateItemInDOM(this.items[i], i);\n          }\n        }\n        if (typeof anchorElementIndex === 'number') {\n          // To compute a destination scroll top, we need to take into account the current smooth scrolling\n          // animation, and then reuse it with a new target (to avoid prolonging the scroll)\n          // See https://github.com/microsoft/vscode/issues/104144\n          // See https://github.com/microsoft/vscode/pull/104284\n          // See https://github.com/microsoft/vscode/issues/107704\n          const deltaScrollTop = this.scrollable.getFutureScrollPosition().scrollTop - renderTop;\n          const newScrollTop = this.elementTop(anchorElementIndex) - anchorElementTopDelta + deltaScrollTop;\n          this.setScrollTop(newScrollTop, inSmoothScrolling);\n        }\n        this._onDidChangeContentHeight.fire(this.contentHeight);\n        return;\n      }\n    }\n  }\n  probeDynamicHeight(index) {\n    const item = this.items[index];\n    if (!!this.virtualDelegate.getDynamicHeight) {\n      const newSize = this.virtualDelegate.getDynamicHeight(item.element);\n      if (newSize !== null) {\n        const size = item.size;\n        item.size = newSize;\n        item.lastDynamicHeightWidth = this.renderWidth;\n        return newSize - size;\n      }\n    }\n    if (!item.hasDynamicHeight || item.lastDynamicHeightWidth === this.renderWidth) {\n      return 0;\n    }\n    if (!!this.virtualDelegate.hasDynamicHeight && !this.virtualDelegate.hasDynamicHeight(item.element)) {\n      return 0;\n    }\n    const size = item.size;\n    if (item.row) {\n      item.row.domNode.style.height = '';\n      item.size = item.row.domNode.offsetHeight;\n      if (item.size === 0 && !isAncestor(item.row.domNode, getWindow(item.row.domNode).document.body)) {\n        console.warn('Measuring item node that is not in DOM! Add ListView to the DOM before measuring row height!', new Error().stack);\n      }\n      item.lastDynamicHeightWidth = this.renderWidth;\n      return item.size - size;\n    }\n    const {\n      row\n    } = this.cache.alloc(item.templateId);\n    row.domNode.style.height = '';\n    this.rowsContainer.appendChild(row.domNode);\n    const renderer = this.renderers.get(item.templateId);\n    if (!renderer) {\n      throw new BugIndicatingError('Missing renderer for templateId: ' + item.templateId);\n    }\n    renderer.renderElement(item.element, index, row.templateData, undefined);\n    item.size = row.domNode.offsetHeight;\n    renderer.disposeElement?.(item.element, index, row.templateData, undefined);\n    this.virtualDelegate.setDynamicHeight?.(item.element, item.size);\n    item.lastDynamicHeightWidth = this.renderWidth;\n    row.domNode.remove();\n    this.cache.release(row);\n    return item.size - size;\n  }\n  getElementDomId(index) {\n    return `${this.domId}_${index}`;\n  }\n  // Dispose\n  dispose() {\n    for (const item of this.items) {\n      item.dragStartDisposable.dispose();\n      item.checkedDisposable.dispose();\n      if (item.row) {\n        const renderer = this.renderers.get(item.row.templateId);\n        if (renderer) {\n          renderer.disposeElement?.(item.element, -1, item.row.templateData, undefined);\n          renderer.disposeTemplate(item.row.templateData);\n        }\n      }\n    }\n    this.items = [];\n    this.domNode?.remove();\n    this.dragOverAnimationDisposable?.dispose();\n    this.disposables.dispose();\n  }\n}\n__decorate([memoize], ListView.prototype, \"onMouseClick\", null);\n__decorate([memoize], ListView.prototype, \"onMouseDblClick\", null);\n__decorate([memoize], ListView.prototype, \"onMouseMiddleClick\", null);\n__decorate([memoize], ListView.prototype, \"onMouseDown\", null);\n__decorate([memoize], ListView.prototype, \"onMouseOver\", null);\n__decorate([memoize], ListView.prototype, \"onMouseOut\", null);\n__decorate([memoize], ListView.prototype, \"onContextMenu\", null);\n__decorate([memoize], ListView.prototype, \"onTouchStart\", null);\n__decorate([memoize], ListView.prototype, \"onTap\", null);", "map": {"version": 3, "names": ["__decorate", "decorators", "target", "key", "desc", "c", "arguments", "length", "r", "Object", "getOwnPropertyDescriptor", "d", "Reflect", "decorate", "i", "defineProperty", "DataTransfers", "$", "addDisposableListener", "animate", "getContentHeight", "getContentWidth", "getTopLeftOffset", "getWindow", "isAncestor", "isHTMLElement", "isSVGElement", "scheduleAtNextAnimationFrame", "DomEmitter", "EventType", "TouchEventType", "Gesture", "SmoothScrollableElement", "distinct", "equals", "<PERSON><PERSON><PERSON>", "disposableTimeout", "memoize", "Emitter", "Event", "Disposable", "DisposableStore", "toDisposable", "Range", "Scrollable", "RangeMap", "shift", "<PERSON><PERSON><PERSON>", "BugIndicatingError", "clamp", "StaticDND", "CurrentDragAndDropData", "undefined", "DefaultOptions", "useShadows", "verticalScrollMode", "setRowLineHeight", "setRowHeight", "supportDynamicHeights", "dnd", "getDragElements", "e", "getDragURI", "onDragStart", "onDragOver", "drop", "dispose", "horizontalScrolling", "transformOptimization", "alwaysConsumeMouseWheel", "ElementsDragAndDropData", "constructor", "elements", "update", "getData", "ExternalElementsDragAndDropData", "NativeDragAndDropData", "types", "files", "dataTransfer", "splice", "file", "item", "size", "type", "push", "equalsDragFeedback", "f1", "f2", "Array", "isArray", "ListViewAccessibilityProvider", "accessibilityProvider", "getSetSize", "bind", "l", "getPosInSet", "getRole", "_", "isChecked", "ListView", "InstanceCount", "contentHeight", "rangeMap", "onDidScroll", "scrollableElement", "onScroll", "scrollableElementDomNode", "getDomNode", "_horizontalScrolling", "value", "Error", "domNode", "classList", "toggle", "items", "measureItemWidth", "updateScrollWidth", "setScrollDimensions", "width", "rowsContainer", "style", "Math", "max", "scrollWidth", "render<PERSON>idth", "scrollableElementWidthDelayer", "cancel", "container", "virtualDelegate", "renderers", "options", "domId", "Map", "_scrollHeight", "scrollableElementUpdateDisposable", "splicing", "dragOverAnimationStopDisposable", "None", "dragOverMouseY", "canDrop", "currentDragFeedbackDisposable", "onDragLeaveTimeout", "disposables", "_onDidChangeContentHeight", "_onDidChangeContentWidth", "onDidChangeContentHeight", "latch", "event", "itemId", "createRangeMap", "paddingTop", "renderer", "set", "templateId", "cache", "add", "lastRenderTop", "lastRenderHeight", "document", "createElement", "className", "tabIndex", "mouseSupport", "paddingBottom", "transform", "overflow", "contain", "addTarget", "scrollable", "forceIntegerValues", "smoothScrollDuration", "smoothScrolling", "cb", "horizontal", "vertical", "mouseWheelScrollSensitivity", "fastScrollSensitivity", "scrollByPage", "append<PERSON><PERSON><PERSON>", "Change", "onTouchChange", "scrollTop", "toDragEvent", "onDrop", "onDragLeave", "onDragEnd", "layout", "initialSize", "height", "updateOptions", "scrollHeight", "setSmoothScrollDuration", "scrollableOptions", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getRenderRange", "offset", "render", "setScrollTop", "eventuallyUpdateScrollDimensions", "_rerender", "start", "deleteCount", "_splice", "fire", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deleteRange", "end", "<PERSON><PERSON><PERSON><PERSON>", "intersect", "rowsToDispose", "dragStartDisposable", "checkedDisposable", "row", "rows", "get", "disposeElement", "element", "templateData", "unshift", "stale", "previousRestRange", "previousRenderedRestRange", "previousUnrenderedRestRanges", "relativeComplement", "inserted", "map", "id", "String", "getTemplateId", "getHeight", "hasDynamicHeight", "lastDynamicHeightWidth", "uri", "drop<PERSON>ar<PERSON>", "deleted", "delta", "renderRange", "renderedRestRange", "updateRange", "updateItemInDOM", "removeRanges", "range", "removeItemFromDOM", "unrenderedRestRanges", "elementsRange", "insertRanges", "reverse", "pop", "insertItemInDOM", "values", "release", "renderHeight", "eventuallyUpdateScrollWidth", "trigger", "rerender", "scrollDimensions", "getScrollDimensions", "firstVisibleIndex", "index", "indexOf", "findIndex", "dom<PERSON>lement", "elementHeight", "elementTop", "positionAt", "indexAt", "position", "indexAfter", "renderTop", "renderLeft", "updateItemsInDOM", "rangesToInsert", "rangesToRemove", "rangesToUpdate", "transact", "left", "top", "result", "alloc", "isReusingConnectedDomNode", "role", "setAttribute", "checked", "onDidChange", "parentElement", "referenceNode", "at", "nextElement<PERSON><PERSON>ling", "insertBefore", "renderElement", "draggable", "getComputedStyle", "paddingLeft", "parseFloat", "paddingRight", "lineHeight", "getElementDomId", "getScrollTop", "scrollPosition", "getScrollPosition", "reuseAnimation", "setScrollPosition", "onMouseClick", "toMouseEvent", "onMouseDblClick", "onMouseMiddleClick", "filter", "browserEvent", "button", "onMouseDown", "onMouseOver", "onMouseOut", "onContextMenu", "any", "Contextmenu", "toGestureEvent", "onTouchStart", "toTouchEvent", "onTap", "Tap", "getItemIndexFromEventTarget", "initialTarget", "sector", "getTargetSector", "scrollLeft", "inSmoothScrolling", "err", "console", "error", "preventDefault", "stopPropagation", "translationY", "effectAllowed", "setData", "TEXT", "setDragImage", "label", "getDragLabel", "dragImage", "textContent", "getDragImageContainer", "contains", "ownerDocument", "setTimeout", "remove", "currentDragData", "setupDragAndDropScrollTopAnimation", "accept", "currentDragFeedback", "dropEffect", "effect", "feedback", "sort", "a", "b", "dragOverEffectPosition", "currentDragFeedbackPosition", "clearDragOverFeedback", "dragData", "teardownDragAndDropScrollTopAnimation", "dragOverAnimationDisposable", "viewTop", "animateDragAndDropScrollTop", "pageY", "diff", "upperLimit", "floor", "min", "targetIndex", "relativePosition", "offsetY", "rawIndex", "getAttribute", "Number", "isNaN", "anchorElementIndex", "anchorElementTopDelta", "heightDiff", "<PERSON><PERSON><PERSON><PERSON>", "probeDynamicHeight", "unrenderRanges", "renderRanges", "deltaScrollTop", "getFutureScrollPosition", "newScrollTop", "getDynamicHeight", "newSize", "offsetHeight", "body", "warn", "stack", "setDynamicHeight", "disposeTemplate", "prototype"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/browser/ui/list/listView.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport { DataTransfers } from '../../dnd.js';\nimport { $, addDisposableListener, animate, getContentHeight, getContentWidth, getTopLeftOffset, getWindow, isAncestor, isHTMLElement, isSVGElement, scheduleAtNextAnimationFrame } from '../../dom.js';\nimport { DomEmitter } from '../../event.js';\nimport { EventType as TouchEventType, Gesture } from '../../touch.js';\nimport { SmoothScrollableElement } from '../scrollbar/scrollableElement.js';\nimport { distinct, equals } from '../../../common/arrays.js';\nimport { Delayer, disposableTimeout } from '../../../common/async.js';\nimport { memoize } from '../../../common/decorators.js';\nimport { Emitter, Event } from '../../../common/event.js';\nimport { Disposable, DisposableStore, toDisposable } from '../../../common/lifecycle.js';\nimport { Range } from '../../../common/range.js';\nimport { Scrollable } from '../../../common/scrollable.js';\nimport { RangeMap, shift } from './rangeMap.js';\nimport { RowCache } from './rowCache.js';\nimport { BugIndicatingError } from '../../../common/errors.js';\nimport { clamp } from '../../../common/numbers.js';\nconst StaticDND = {\n    CurrentDragAndDropData: undefined\n};\nconst DefaultOptions = {\n    useShadows: true,\n    verticalScrollMode: 1 /* ScrollbarVisibility.Auto */,\n    setRowLineHeight: true,\n    setRowHeight: true,\n    supportDynamicHeights: false,\n    dnd: {\n        getDragElements(e) { return [e]; },\n        getDragURI() { return null; },\n        onDragStart() { },\n        onDragOver() { return false; },\n        drop() { },\n        dispose() { }\n    },\n    horizontalScrolling: false,\n    transformOptimization: true,\n    alwaysConsumeMouseWheel: true,\n};\nexport class ElementsDragAndDropData {\n    constructor(elements) {\n        this.elements = elements;\n    }\n    update() { }\n    getData() {\n        return this.elements;\n    }\n}\nexport class ExternalElementsDragAndDropData {\n    constructor(elements) {\n        this.elements = elements;\n    }\n    update() { }\n    getData() {\n        return this.elements;\n    }\n}\nexport class NativeDragAndDropData {\n    constructor() {\n        this.types = [];\n        this.files = [];\n    }\n    update(dataTransfer) {\n        if (dataTransfer.types) {\n            this.types.splice(0, this.types.length, ...dataTransfer.types);\n        }\n        if (dataTransfer.files) {\n            this.files.splice(0, this.files.length);\n            for (let i = 0; i < dataTransfer.files.length; i++) {\n                const file = dataTransfer.files.item(i);\n                if (file && (file.size || file.type)) {\n                    this.files.push(file);\n                }\n            }\n        }\n    }\n    getData() {\n        return {\n            types: this.types,\n            files: this.files\n        };\n    }\n}\nfunction equalsDragFeedback(f1, f2) {\n    if (Array.isArray(f1) && Array.isArray(f2)) {\n        return equals(f1, f2);\n    }\n    return f1 === f2;\n}\nclass ListViewAccessibilityProvider {\n    constructor(accessibilityProvider) {\n        if (accessibilityProvider?.getSetSize) {\n            this.getSetSize = accessibilityProvider.getSetSize.bind(accessibilityProvider);\n        }\n        else {\n            this.getSetSize = (e, i, l) => l;\n        }\n        if (accessibilityProvider?.getPosInSet) {\n            this.getPosInSet = accessibilityProvider.getPosInSet.bind(accessibilityProvider);\n        }\n        else {\n            this.getPosInSet = (e, i) => i + 1;\n        }\n        if (accessibilityProvider?.getRole) {\n            this.getRole = accessibilityProvider.getRole.bind(accessibilityProvider);\n        }\n        else {\n            this.getRole = _ => 'listitem';\n        }\n        if (accessibilityProvider?.isChecked) {\n            this.isChecked = accessibilityProvider.isChecked.bind(accessibilityProvider);\n        }\n        else {\n            this.isChecked = _ => undefined;\n        }\n    }\n}\n/**\n * The {@link ListView} is a virtual scrolling engine.\n *\n * Given that it only renders elements within its viewport, it can hold large\n * collections of elements and stay very performant. The performance bottleneck\n * usually lies within the user's rendering code for each element.\n *\n * @remarks It is a low-level widget, not meant to be used directly. Refer to the\n * List widget instead.\n */\nexport class ListView {\n    static { this.InstanceCount = 0; }\n    get contentHeight() { return this.rangeMap.size; }\n    get onDidScroll() { return this.scrollableElement.onScroll; }\n    get scrollableElementDomNode() { return this.scrollableElement.getDomNode(); }\n    get horizontalScrolling() { return this._horizontalScrolling; }\n    set horizontalScrolling(value) {\n        if (value === this._horizontalScrolling) {\n            return;\n        }\n        if (value && this.supportDynamicHeights) {\n            throw new Error('Horizontal scrolling and dynamic heights not supported simultaneously');\n        }\n        this._horizontalScrolling = value;\n        this.domNode.classList.toggle('horizontal-scrolling', this._horizontalScrolling);\n        if (this._horizontalScrolling) {\n            for (const item of this.items) {\n                this.measureItemWidth(item);\n            }\n            this.updateScrollWidth();\n            this.scrollableElement.setScrollDimensions({ width: getContentWidth(this.domNode) });\n            this.rowsContainer.style.width = `${Math.max(this.scrollWidth || 0, this.renderWidth)}px`;\n        }\n        else {\n            this.scrollableElementWidthDelayer.cancel();\n            this.scrollableElement.setScrollDimensions({ width: this.renderWidth, scrollWidth: this.renderWidth });\n            this.rowsContainer.style.width = '';\n        }\n    }\n    constructor(container, virtualDelegate, renderers, options = DefaultOptions) {\n        this.virtualDelegate = virtualDelegate;\n        this.domId = `list_id_${++ListView.InstanceCount}`;\n        this.renderers = new Map();\n        this.renderWidth = 0;\n        this._scrollHeight = 0;\n        this.scrollableElementUpdateDisposable = null;\n        this.scrollableElementWidthDelayer = new Delayer(50);\n        this.splicing = false;\n        this.dragOverAnimationStopDisposable = Disposable.None;\n        this.dragOverMouseY = 0;\n        this.canDrop = false;\n        this.currentDragFeedbackDisposable = Disposable.None;\n        this.onDragLeaveTimeout = Disposable.None;\n        this.disposables = new DisposableStore();\n        this._onDidChangeContentHeight = new Emitter();\n        this._onDidChangeContentWidth = new Emitter();\n        this.onDidChangeContentHeight = Event.latch(this._onDidChangeContentHeight.event, undefined, this.disposables);\n        this._horizontalScrolling = false;\n        if (options.horizontalScrolling && options.supportDynamicHeights) {\n            throw new Error('Horizontal scrolling and dynamic heights not supported simultaneously');\n        }\n        this.items = [];\n        this.itemId = 0;\n        this.rangeMap = this.createRangeMap(options.paddingTop ?? 0);\n        for (const renderer of renderers) {\n            this.renderers.set(renderer.templateId, renderer);\n        }\n        this.cache = this.disposables.add(new RowCache(this.renderers));\n        this.lastRenderTop = 0;\n        this.lastRenderHeight = 0;\n        this.domNode = document.createElement('div');\n        this.domNode.className = 'monaco-list';\n        this.domNode.classList.add(this.domId);\n        this.domNode.tabIndex = 0;\n        this.domNode.classList.toggle('mouse-support', typeof options.mouseSupport === 'boolean' ? options.mouseSupport : true);\n        this._horizontalScrolling = options.horizontalScrolling ?? DefaultOptions.horizontalScrolling;\n        this.domNode.classList.toggle('horizontal-scrolling', this._horizontalScrolling);\n        this.paddingBottom = typeof options.paddingBottom === 'undefined' ? 0 : options.paddingBottom;\n        this.accessibilityProvider = new ListViewAccessibilityProvider(options.accessibilityProvider);\n        this.rowsContainer = document.createElement('div');\n        this.rowsContainer.className = 'monaco-list-rows';\n        const transformOptimization = options.transformOptimization ?? DefaultOptions.transformOptimization;\n        if (transformOptimization) {\n            this.rowsContainer.style.transform = 'translate3d(0px, 0px, 0px)';\n            this.rowsContainer.style.overflow = 'hidden';\n            this.rowsContainer.style.contain = 'strict';\n        }\n        this.disposables.add(Gesture.addTarget(this.rowsContainer));\n        this.scrollable = this.disposables.add(new Scrollable({\n            forceIntegerValues: true,\n            smoothScrollDuration: (options.smoothScrolling ?? false) ? 125 : 0,\n            scheduleAtNextAnimationFrame: cb => scheduleAtNextAnimationFrame(getWindow(this.domNode), cb)\n        }));\n        this.scrollableElement = this.disposables.add(new SmoothScrollableElement(this.rowsContainer, {\n            alwaysConsumeMouseWheel: options.alwaysConsumeMouseWheel ?? DefaultOptions.alwaysConsumeMouseWheel,\n            horizontal: 1 /* ScrollbarVisibility.Auto */,\n            vertical: options.verticalScrollMode ?? DefaultOptions.verticalScrollMode,\n            useShadows: options.useShadows ?? DefaultOptions.useShadows,\n            mouseWheelScrollSensitivity: options.mouseWheelScrollSensitivity,\n            fastScrollSensitivity: options.fastScrollSensitivity,\n            scrollByPage: options.scrollByPage\n        }, this.scrollable));\n        this.domNode.appendChild(this.scrollableElement.getDomNode());\n        container.appendChild(this.domNode);\n        this.scrollableElement.onScroll(this.onScroll, this, this.disposables);\n        this.disposables.add(addDisposableListener(this.rowsContainer, TouchEventType.Change, e => this.onTouchChange(e)));\n        // Prevent the monaco-scrollable-element from scrolling\n        // https://github.com/microsoft/vscode/issues/44181\n        this.disposables.add(addDisposableListener(this.scrollableElement.getDomNode(), 'scroll', e => e.target.scrollTop = 0));\n        this.disposables.add(addDisposableListener(this.domNode, 'dragover', e => this.onDragOver(this.toDragEvent(e))));\n        this.disposables.add(addDisposableListener(this.domNode, 'drop', e => this.onDrop(this.toDragEvent(e))));\n        this.disposables.add(addDisposableListener(this.domNode, 'dragleave', e => this.onDragLeave(this.toDragEvent(e))));\n        this.disposables.add(addDisposableListener(this.domNode, 'dragend', e => this.onDragEnd(e)));\n        this.setRowLineHeight = options.setRowLineHeight ?? DefaultOptions.setRowLineHeight;\n        this.setRowHeight = options.setRowHeight ?? DefaultOptions.setRowHeight;\n        this.supportDynamicHeights = options.supportDynamicHeights ?? DefaultOptions.supportDynamicHeights;\n        this.dnd = options.dnd ?? this.disposables.add(DefaultOptions.dnd);\n        this.layout(options.initialSize?.height, options.initialSize?.width);\n    }\n    updateOptions(options) {\n        if (options.paddingBottom !== undefined) {\n            this.paddingBottom = options.paddingBottom;\n            this.scrollableElement.setScrollDimensions({ scrollHeight: this.scrollHeight });\n        }\n        if (options.smoothScrolling !== undefined) {\n            this.scrollable.setSmoothScrollDuration(options.smoothScrolling ? 125 : 0);\n        }\n        if (options.horizontalScrolling !== undefined) {\n            this.horizontalScrolling = options.horizontalScrolling;\n        }\n        let scrollableOptions;\n        if (options.scrollByPage !== undefined) {\n            scrollableOptions = { ...(scrollableOptions ?? {}), scrollByPage: options.scrollByPage };\n        }\n        if (options.mouseWheelScrollSensitivity !== undefined) {\n            scrollableOptions = { ...(scrollableOptions ?? {}), mouseWheelScrollSensitivity: options.mouseWheelScrollSensitivity };\n        }\n        if (options.fastScrollSensitivity !== undefined) {\n            scrollableOptions = { ...(scrollableOptions ?? {}), fastScrollSensitivity: options.fastScrollSensitivity };\n        }\n        if (scrollableOptions) {\n            this.scrollableElement.updateOptions(scrollableOptions);\n        }\n        if (options.paddingTop !== undefined && options.paddingTop !== this.rangeMap.paddingTop) {\n            // trigger a rerender\n            const lastRenderRange = this.getRenderRange(this.lastRenderTop, this.lastRenderHeight);\n            const offset = options.paddingTop - this.rangeMap.paddingTop;\n            this.rangeMap.paddingTop = options.paddingTop;\n            this.render(lastRenderRange, Math.max(0, this.lastRenderTop + offset), this.lastRenderHeight, undefined, undefined, true);\n            this.setScrollTop(this.lastRenderTop);\n            this.eventuallyUpdateScrollDimensions();\n            if (this.supportDynamicHeights) {\n                this._rerender(this.lastRenderTop, this.lastRenderHeight);\n            }\n        }\n    }\n    createRangeMap(paddingTop) {\n        return new RangeMap(paddingTop);\n    }\n    splice(start, deleteCount, elements = []) {\n        if (this.splicing) {\n            throw new Error('Can\\'t run recursive splices.');\n        }\n        this.splicing = true;\n        try {\n            return this._splice(start, deleteCount, elements);\n        }\n        finally {\n            this.splicing = false;\n            this._onDidChangeContentHeight.fire(this.contentHeight);\n        }\n    }\n    _splice(start, deleteCount, elements = []) {\n        const previousRenderRange = this.getRenderRange(this.lastRenderTop, this.lastRenderHeight);\n        const deleteRange = { start, end: start + deleteCount };\n        const removeRange = Range.intersect(previousRenderRange, deleteRange);\n        // try to reuse rows, avoid removing them from DOM\n        const rowsToDispose = new Map();\n        for (let i = removeRange.end - 1; i >= removeRange.start; i--) {\n            const item = this.items[i];\n            item.dragStartDisposable.dispose();\n            item.checkedDisposable.dispose();\n            if (item.row) {\n                let rows = rowsToDispose.get(item.templateId);\n                if (!rows) {\n                    rows = [];\n                    rowsToDispose.set(item.templateId, rows);\n                }\n                const renderer = this.renderers.get(item.templateId);\n                if (renderer && renderer.disposeElement) {\n                    renderer.disposeElement(item.element, i, item.row.templateData, item.size);\n                }\n                rows.unshift(item.row);\n            }\n            item.row = null;\n            item.stale = true;\n        }\n        const previousRestRange = { start: start + deleteCount, end: this.items.length };\n        const previousRenderedRestRange = Range.intersect(previousRestRange, previousRenderRange);\n        const previousUnrenderedRestRanges = Range.relativeComplement(previousRestRange, previousRenderRange);\n        const inserted = elements.map(element => ({\n            id: String(this.itemId++),\n            element,\n            templateId: this.virtualDelegate.getTemplateId(element),\n            size: this.virtualDelegate.getHeight(element),\n            width: undefined,\n            hasDynamicHeight: !!this.virtualDelegate.hasDynamicHeight && this.virtualDelegate.hasDynamicHeight(element),\n            lastDynamicHeightWidth: undefined,\n            row: null,\n            uri: undefined,\n            dropTarget: false,\n            dragStartDisposable: Disposable.None,\n            checkedDisposable: Disposable.None,\n            stale: false\n        }));\n        let deleted;\n        // TODO@joao: improve this optimization to catch even more cases\n        if (start === 0 && deleteCount >= this.items.length) {\n            this.rangeMap = this.createRangeMap(this.rangeMap.paddingTop);\n            this.rangeMap.splice(0, 0, inserted);\n            deleted = this.items;\n            this.items = inserted;\n        }\n        else {\n            this.rangeMap.splice(start, deleteCount, inserted);\n            deleted = this.items.splice(start, deleteCount, ...inserted);\n        }\n        const delta = elements.length - deleteCount;\n        const renderRange = this.getRenderRange(this.lastRenderTop, this.lastRenderHeight);\n        const renderedRestRange = shift(previousRenderedRestRange, delta);\n        const updateRange = Range.intersect(renderRange, renderedRestRange);\n        for (let i = updateRange.start; i < updateRange.end; i++) {\n            this.updateItemInDOM(this.items[i], i);\n        }\n        const removeRanges = Range.relativeComplement(renderedRestRange, renderRange);\n        for (const range of removeRanges) {\n            for (let i = range.start; i < range.end; i++) {\n                this.removeItemFromDOM(i);\n            }\n        }\n        const unrenderedRestRanges = previousUnrenderedRestRanges.map(r => shift(r, delta));\n        const elementsRange = { start, end: start + elements.length };\n        const insertRanges = [elementsRange, ...unrenderedRestRanges].map(r => Range.intersect(renderRange, r)).reverse();\n        for (const range of insertRanges) {\n            for (let i = range.end - 1; i >= range.start; i--) {\n                const item = this.items[i];\n                const rows = rowsToDispose.get(item.templateId);\n                const row = rows?.pop();\n                this.insertItemInDOM(i, row);\n            }\n        }\n        for (const rows of rowsToDispose.values()) {\n            for (const row of rows) {\n                this.cache.release(row);\n            }\n        }\n        this.eventuallyUpdateScrollDimensions();\n        if (this.supportDynamicHeights) {\n            this._rerender(this.scrollTop, this.renderHeight);\n        }\n        return deleted.map(i => i.element);\n    }\n    eventuallyUpdateScrollDimensions() {\n        this._scrollHeight = this.contentHeight;\n        this.rowsContainer.style.height = `${this._scrollHeight}px`;\n        if (!this.scrollableElementUpdateDisposable) {\n            this.scrollableElementUpdateDisposable = scheduleAtNextAnimationFrame(getWindow(this.domNode), () => {\n                this.scrollableElement.setScrollDimensions({ scrollHeight: this.scrollHeight });\n                this.updateScrollWidth();\n                this.scrollableElementUpdateDisposable = null;\n            });\n        }\n    }\n    eventuallyUpdateScrollWidth() {\n        if (!this.horizontalScrolling) {\n            this.scrollableElementWidthDelayer.cancel();\n            return;\n        }\n        this.scrollableElementWidthDelayer.trigger(() => this.updateScrollWidth());\n    }\n    updateScrollWidth() {\n        if (!this.horizontalScrolling) {\n            return;\n        }\n        let scrollWidth = 0;\n        for (const item of this.items) {\n            if (typeof item.width !== 'undefined') {\n                scrollWidth = Math.max(scrollWidth, item.width);\n            }\n        }\n        this.scrollWidth = scrollWidth;\n        this.scrollableElement.setScrollDimensions({ scrollWidth: scrollWidth === 0 ? 0 : (scrollWidth + 10) });\n        this._onDidChangeContentWidth.fire(this.scrollWidth);\n    }\n    rerender() {\n        if (!this.supportDynamicHeights) {\n            return;\n        }\n        for (const item of this.items) {\n            item.lastDynamicHeightWidth = undefined;\n        }\n        this._rerender(this.lastRenderTop, this.lastRenderHeight);\n    }\n    get length() {\n        return this.items.length;\n    }\n    get renderHeight() {\n        const scrollDimensions = this.scrollableElement.getScrollDimensions();\n        return scrollDimensions.height;\n    }\n    get firstVisibleIndex() {\n        const range = this.getRenderRange(this.lastRenderTop, this.lastRenderHeight);\n        return range.start;\n    }\n    element(index) {\n        return this.items[index].element;\n    }\n    indexOf(element) {\n        return this.items.findIndex(item => item.element === element);\n    }\n    domElement(index) {\n        const row = this.items[index].row;\n        return row && row.domNode;\n    }\n    elementHeight(index) {\n        return this.items[index].size;\n    }\n    elementTop(index) {\n        return this.rangeMap.positionAt(index);\n    }\n    indexAt(position) {\n        return this.rangeMap.indexAt(position);\n    }\n    indexAfter(position) {\n        return this.rangeMap.indexAfter(position);\n    }\n    layout(height, width) {\n        const scrollDimensions = {\n            height: typeof height === 'number' ? height : getContentHeight(this.domNode)\n        };\n        if (this.scrollableElementUpdateDisposable) {\n            this.scrollableElementUpdateDisposable.dispose();\n            this.scrollableElementUpdateDisposable = null;\n            scrollDimensions.scrollHeight = this.scrollHeight;\n        }\n        this.scrollableElement.setScrollDimensions(scrollDimensions);\n        if (typeof width !== 'undefined') {\n            this.renderWidth = width;\n            if (this.supportDynamicHeights) {\n                this._rerender(this.scrollTop, this.renderHeight);\n            }\n        }\n        if (this.horizontalScrolling) {\n            this.scrollableElement.setScrollDimensions({\n                width: typeof width === 'number' ? width : getContentWidth(this.domNode)\n            });\n        }\n    }\n    // Render\n    render(previousRenderRange, renderTop, renderHeight, renderLeft, scrollWidth, updateItemsInDOM = false) {\n        const renderRange = this.getRenderRange(renderTop, renderHeight);\n        const rangesToInsert = Range.relativeComplement(renderRange, previousRenderRange).reverse();\n        const rangesToRemove = Range.relativeComplement(previousRenderRange, renderRange);\n        if (updateItemsInDOM) {\n            const rangesToUpdate = Range.intersect(previousRenderRange, renderRange);\n            for (let i = rangesToUpdate.start; i < rangesToUpdate.end; i++) {\n                this.updateItemInDOM(this.items[i], i);\n            }\n        }\n        this.cache.transact(() => {\n            for (const range of rangesToRemove) {\n                for (let i = range.start; i < range.end; i++) {\n                    this.removeItemFromDOM(i);\n                }\n            }\n            for (const range of rangesToInsert) {\n                for (let i = range.end - 1; i >= range.start; i--) {\n                    this.insertItemInDOM(i);\n                }\n            }\n        });\n        if (renderLeft !== undefined) {\n            this.rowsContainer.style.left = `-${renderLeft}px`;\n        }\n        this.rowsContainer.style.top = `-${renderTop}px`;\n        if (this.horizontalScrolling && scrollWidth !== undefined) {\n            this.rowsContainer.style.width = `${Math.max(scrollWidth, this.renderWidth)}px`;\n        }\n        this.lastRenderTop = renderTop;\n        this.lastRenderHeight = renderHeight;\n    }\n    // DOM operations\n    insertItemInDOM(index, row) {\n        const item = this.items[index];\n        if (!item.row) {\n            if (row) {\n                item.row = row;\n                item.stale = true;\n            }\n            else {\n                const result = this.cache.alloc(item.templateId);\n                item.row = result.row;\n                item.stale ||= result.isReusingConnectedDomNode;\n            }\n        }\n        const role = this.accessibilityProvider.getRole(item.element) || 'listitem';\n        item.row.domNode.setAttribute('role', role);\n        const checked = this.accessibilityProvider.isChecked(item.element);\n        if (typeof checked === 'boolean') {\n            item.row.domNode.setAttribute('aria-checked', String(!!checked));\n        }\n        else if (checked) {\n            const update = (checked) => item.row.domNode.setAttribute('aria-checked', String(!!checked));\n            update(checked.value);\n            item.checkedDisposable = checked.onDidChange(() => update(checked.value));\n        }\n        if (item.stale || !item.row.domNode.parentElement) {\n            const referenceNode = this.items.at(index + 1)?.row?.domNode ?? null;\n            if (item.row.domNode.parentElement !== this.rowsContainer || item.row.domNode.nextElementSibling !== referenceNode) {\n                this.rowsContainer.insertBefore(item.row.domNode, referenceNode);\n            }\n            item.stale = false;\n        }\n        this.updateItemInDOM(item, index);\n        const renderer = this.renderers.get(item.templateId);\n        if (!renderer) {\n            throw new Error(`No renderer found for template id ${item.templateId}`);\n        }\n        renderer?.renderElement(item.element, index, item.row.templateData, item.size);\n        const uri = this.dnd.getDragURI(item.element);\n        item.dragStartDisposable.dispose();\n        item.row.domNode.draggable = !!uri;\n        if (uri) {\n            item.dragStartDisposable = addDisposableListener(item.row.domNode, 'dragstart', event => this.onDragStart(item.element, uri, event));\n        }\n        if (this.horizontalScrolling) {\n            this.measureItemWidth(item);\n            this.eventuallyUpdateScrollWidth();\n        }\n    }\n    measureItemWidth(item) {\n        if (!item.row || !item.row.domNode) {\n            return;\n        }\n        item.row.domNode.style.width = 'fit-content';\n        item.width = getContentWidth(item.row.domNode);\n        const style = getWindow(item.row.domNode).getComputedStyle(item.row.domNode);\n        if (style.paddingLeft) {\n            item.width += parseFloat(style.paddingLeft);\n        }\n        if (style.paddingRight) {\n            item.width += parseFloat(style.paddingRight);\n        }\n        item.row.domNode.style.width = '';\n    }\n    updateItemInDOM(item, index) {\n        item.row.domNode.style.top = `${this.elementTop(index)}px`;\n        if (this.setRowHeight) {\n            item.row.domNode.style.height = `${item.size}px`;\n        }\n        if (this.setRowLineHeight) {\n            item.row.domNode.style.lineHeight = `${item.size}px`;\n        }\n        item.row.domNode.setAttribute('data-index', `${index}`);\n        item.row.domNode.setAttribute('data-last-element', index === this.length - 1 ? 'true' : 'false');\n        item.row.domNode.setAttribute('data-parity', index % 2 === 0 ? 'even' : 'odd');\n        item.row.domNode.setAttribute('aria-setsize', String(this.accessibilityProvider.getSetSize(item.element, index, this.length)));\n        item.row.domNode.setAttribute('aria-posinset', String(this.accessibilityProvider.getPosInSet(item.element, index)));\n        item.row.domNode.setAttribute('id', this.getElementDomId(index));\n        item.row.domNode.classList.toggle('drop-target', item.dropTarget);\n    }\n    removeItemFromDOM(index) {\n        const item = this.items[index];\n        item.dragStartDisposable.dispose();\n        item.checkedDisposable.dispose();\n        if (item.row) {\n            const renderer = this.renderers.get(item.templateId);\n            if (renderer && renderer.disposeElement) {\n                renderer.disposeElement(item.element, index, item.row.templateData, item.size);\n            }\n            this.cache.release(item.row);\n            item.row = null;\n        }\n        if (this.horizontalScrolling) {\n            this.eventuallyUpdateScrollWidth();\n        }\n    }\n    getScrollTop() {\n        const scrollPosition = this.scrollableElement.getScrollPosition();\n        return scrollPosition.scrollTop;\n    }\n    setScrollTop(scrollTop, reuseAnimation) {\n        if (this.scrollableElementUpdateDisposable) {\n            this.scrollableElementUpdateDisposable.dispose();\n            this.scrollableElementUpdateDisposable = null;\n            this.scrollableElement.setScrollDimensions({ scrollHeight: this.scrollHeight });\n        }\n        this.scrollableElement.setScrollPosition({ scrollTop, reuseAnimation });\n    }\n    get scrollTop() {\n        return this.getScrollTop();\n    }\n    set scrollTop(scrollTop) {\n        this.setScrollTop(scrollTop);\n    }\n    get scrollHeight() {\n        return this._scrollHeight + (this.horizontalScrolling ? 10 : 0) + this.paddingBottom;\n    }\n    // Events\n    get onMouseClick() { return Event.map(this.disposables.add(new DomEmitter(this.domNode, 'click')).event, e => this.toMouseEvent(e), this.disposables); }\n    get onMouseDblClick() { return Event.map(this.disposables.add(new DomEmitter(this.domNode, 'dblclick')).event, e => this.toMouseEvent(e), this.disposables); }\n    get onMouseMiddleClick() { return Event.filter(Event.map(this.disposables.add(new DomEmitter(this.domNode, 'auxclick')).event, e => this.toMouseEvent(e), this.disposables), e => e.browserEvent.button === 1, this.disposables); }\n    get onMouseDown() { return Event.map(this.disposables.add(new DomEmitter(this.domNode, 'mousedown')).event, e => this.toMouseEvent(e), this.disposables); }\n    get onMouseOver() { return Event.map(this.disposables.add(new DomEmitter(this.domNode, 'mouseover')).event, e => this.toMouseEvent(e), this.disposables); }\n    get onMouseOut() { return Event.map(this.disposables.add(new DomEmitter(this.domNode, 'mouseout')).event, e => this.toMouseEvent(e), this.disposables); }\n    get onContextMenu() { return Event.any(Event.map(this.disposables.add(new DomEmitter(this.domNode, 'contextmenu')).event, e => this.toMouseEvent(e), this.disposables), Event.map(this.disposables.add(new DomEmitter(this.domNode, TouchEventType.Contextmenu)).event, e => this.toGestureEvent(e), this.disposables)); }\n    get onTouchStart() { return Event.map(this.disposables.add(new DomEmitter(this.domNode, 'touchstart')).event, e => this.toTouchEvent(e), this.disposables); }\n    get onTap() { return Event.map(this.disposables.add(new DomEmitter(this.rowsContainer, TouchEventType.Tap)).event, e => this.toGestureEvent(e), this.disposables); }\n    toMouseEvent(browserEvent) {\n        const index = this.getItemIndexFromEventTarget(browserEvent.target || null);\n        const item = typeof index === 'undefined' ? undefined : this.items[index];\n        const element = item && item.element;\n        return { browserEvent, index, element };\n    }\n    toTouchEvent(browserEvent) {\n        const index = this.getItemIndexFromEventTarget(browserEvent.target || null);\n        const item = typeof index === 'undefined' ? undefined : this.items[index];\n        const element = item && item.element;\n        return { browserEvent, index, element };\n    }\n    toGestureEvent(browserEvent) {\n        const index = this.getItemIndexFromEventTarget(browserEvent.initialTarget || null);\n        const item = typeof index === 'undefined' ? undefined : this.items[index];\n        const element = item && item.element;\n        return { browserEvent, index, element };\n    }\n    toDragEvent(browserEvent) {\n        const index = this.getItemIndexFromEventTarget(browserEvent.target || null);\n        const item = typeof index === 'undefined' ? undefined : this.items[index];\n        const element = item && item.element;\n        const sector = this.getTargetSector(browserEvent, index);\n        return { browserEvent, index, element, sector };\n    }\n    onScroll(e) {\n        try {\n            const previousRenderRange = this.getRenderRange(this.lastRenderTop, this.lastRenderHeight);\n            this.render(previousRenderRange, e.scrollTop, e.height, e.scrollLeft, e.scrollWidth);\n            if (this.supportDynamicHeights) {\n                this._rerender(e.scrollTop, e.height, e.inSmoothScrolling);\n            }\n        }\n        catch (err) {\n            console.error('Got bad scroll event:', e);\n            throw err;\n        }\n    }\n    onTouchChange(event) {\n        event.preventDefault();\n        event.stopPropagation();\n        this.scrollTop -= event.translationY;\n    }\n    // DND\n    onDragStart(element, uri, event) {\n        if (!event.dataTransfer) {\n            return;\n        }\n        const elements = this.dnd.getDragElements(element);\n        event.dataTransfer.effectAllowed = 'copyMove';\n        event.dataTransfer.setData(DataTransfers.TEXT, uri);\n        if (event.dataTransfer.setDragImage) {\n            let label;\n            if (this.dnd.getDragLabel) {\n                label = this.dnd.getDragLabel(elements, event);\n            }\n            if (typeof label === 'undefined') {\n                label = String(elements.length);\n            }\n            const dragImage = $('.monaco-drag-image');\n            dragImage.textContent = label;\n            const getDragImageContainer = (e) => {\n                while (e && !e.classList.contains('monaco-workbench')) {\n                    e = e.parentElement;\n                }\n                return e || this.domNode.ownerDocument;\n            };\n            const container = getDragImageContainer(this.domNode);\n            container.appendChild(dragImage);\n            event.dataTransfer.setDragImage(dragImage, -10, -10);\n            setTimeout(() => dragImage.remove(), 0);\n        }\n        this.domNode.classList.add('dragging');\n        this.currentDragData = new ElementsDragAndDropData(elements);\n        StaticDND.CurrentDragAndDropData = new ExternalElementsDragAndDropData(elements);\n        this.dnd.onDragStart?.(this.currentDragData, event);\n    }\n    onDragOver(event) {\n        event.browserEvent.preventDefault(); // needed so that the drop event fires (https://stackoverflow.com/questions/21339924/drop-event-not-firing-in-chrome)\n        this.onDragLeaveTimeout.dispose();\n        if (StaticDND.CurrentDragAndDropData && StaticDND.CurrentDragAndDropData.getData() === 'vscode-ui') {\n            return false;\n        }\n        this.setupDragAndDropScrollTopAnimation(event.browserEvent);\n        if (!event.browserEvent.dataTransfer) {\n            return false;\n        }\n        // Drag over from outside\n        if (!this.currentDragData) {\n            if (StaticDND.CurrentDragAndDropData) {\n                // Drag over from another list\n                this.currentDragData = StaticDND.CurrentDragAndDropData;\n            }\n            else {\n                // Drag over from the desktop\n                if (!event.browserEvent.dataTransfer.types) {\n                    return false;\n                }\n                this.currentDragData = new NativeDragAndDropData();\n            }\n        }\n        const result = this.dnd.onDragOver(this.currentDragData, event.element, event.index, event.sector, event.browserEvent);\n        this.canDrop = typeof result === 'boolean' ? result : result.accept;\n        if (!this.canDrop) {\n            this.currentDragFeedback = undefined;\n            this.currentDragFeedbackDisposable.dispose();\n            return false;\n        }\n        event.browserEvent.dataTransfer.dropEffect = (typeof result !== 'boolean' && result.effect?.type === 0 /* ListDragOverEffectType.Copy */) ? 'copy' : 'move';\n        let feedback;\n        if (typeof result !== 'boolean' && result.feedback) {\n            feedback = result.feedback;\n        }\n        else {\n            if (typeof event.index === 'undefined') {\n                feedback = [-1];\n            }\n            else {\n                feedback = [event.index];\n            }\n        }\n        // sanitize feedback list\n        feedback = distinct(feedback).filter(i => i >= -1 && i < this.length).sort((a, b) => a - b);\n        feedback = feedback[0] === -1 ? [-1] : feedback;\n        let dragOverEffectPosition = typeof result !== 'boolean' && result.effect && result.effect.position ? result.effect.position : \"drop-target\" /* ListDragOverEffectPosition.Over */;\n        if (equalsDragFeedback(this.currentDragFeedback, feedback) && this.currentDragFeedbackPosition === dragOverEffectPosition) {\n            return true;\n        }\n        this.currentDragFeedback = feedback;\n        this.currentDragFeedbackPosition = dragOverEffectPosition;\n        this.currentDragFeedbackDisposable.dispose();\n        if (feedback[0] === -1) { // entire list feedback\n            this.domNode.classList.add(dragOverEffectPosition);\n            this.rowsContainer.classList.add(dragOverEffectPosition);\n            this.currentDragFeedbackDisposable = toDisposable(() => {\n                this.domNode.classList.remove(dragOverEffectPosition);\n                this.rowsContainer.classList.remove(dragOverEffectPosition);\n            });\n        }\n        else {\n            if (feedback.length > 1 && dragOverEffectPosition !== \"drop-target\" /* ListDragOverEffectPosition.Over */) {\n                throw new Error('Can\\'t use multiple feedbacks with position different than \\'over\\'');\n            }\n            // Make sure there is no flicker when moving between two items\n            // Always use the before feedback if possible\n            if (dragOverEffectPosition === \"drop-target-after\" /* ListDragOverEffectPosition.After */) {\n                if (feedback[0] < this.length - 1) {\n                    feedback[0] += 1;\n                    dragOverEffectPosition = \"drop-target-before\" /* ListDragOverEffectPosition.Before */;\n                }\n            }\n            for (const index of feedback) {\n                const item = this.items[index];\n                item.dropTarget = true;\n                item.row?.domNode.classList.add(dragOverEffectPosition);\n            }\n            this.currentDragFeedbackDisposable = toDisposable(() => {\n                for (const index of feedback) {\n                    const item = this.items[index];\n                    item.dropTarget = false;\n                    item.row?.domNode.classList.remove(dragOverEffectPosition);\n                }\n            });\n        }\n        return true;\n    }\n    onDragLeave(event) {\n        this.onDragLeaveTimeout.dispose();\n        this.onDragLeaveTimeout = disposableTimeout(() => this.clearDragOverFeedback(), 100, this.disposables);\n        if (this.currentDragData) {\n            this.dnd.onDragLeave?.(this.currentDragData, event.element, event.index, event.browserEvent);\n        }\n    }\n    onDrop(event) {\n        if (!this.canDrop) {\n            return;\n        }\n        const dragData = this.currentDragData;\n        this.teardownDragAndDropScrollTopAnimation();\n        this.clearDragOverFeedback();\n        this.domNode.classList.remove('dragging');\n        this.currentDragData = undefined;\n        StaticDND.CurrentDragAndDropData = undefined;\n        if (!dragData || !event.browserEvent.dataTransfer) {\n            return;\n        }\n        event.browserEvent.preventDefault();\n        dragData.update(event.browserEvent.dataTransfer);\n        this.dnd.drop(dragData, event.element, event.index, event.sector, event.browserEvent);\n    }\n    onDragEnd(event) {\n        this.canDrop = false;\n        this.teardownDragAndDropScrollTopAnimation();\n        this.clearDragOverFeedback();\n        this.domNode.classList.remove('dragging');\n        this.currentDragData = undefined;\n        StaticDND.CurrentDragAndDropData = undefined;\n        this.dnd.onDragEnd?.(event);\n    }\n    clearDragOverFeedback() {\n        this.currentDragFeedback = undefined;\n        this.currentDragFeedbackPosition = undefined;\n        this.currentDragFeedbackDisposable.dispose();\n        this.currentDragFeedbackDisposable = Disposable.None;\n    }\n    // DND scroll top animation\n    setupDragAndDropScrollTopAnimation(event) {\n        if (!this.dragOverAnimationDisposable) {\n            const viewTop = getTopLeftOffset(this.domNode).top;\n            this.dragOverAnimationDisposable = animate(getWindow(this.domNode), this.animateDragAndDropScrollTop.bind(this, viewTop));\n        }\n        this.dragOverAnimationStopDisposable.dispose();\n        this.dragOverAnimationStopDisposable = disposableTimeout(() => {\n            if (this.dragOverAnimationDisposable) {\n                this.dragOverAnimationDisposable.dispose();\n                this.dragOverAnimationDisposable = undefined;\n            }\n        }, 1000, this.disposables);\n        this.dragOverMouseY = event.pageY;\n    }\n    animateDragAndDropScrollTop(viewTop) {\n        if (this.dragOverMouseY === undefined) {\n            return;\n        }\n        const diff = this.dragOverMouseY - viewTop;\n        const upperLimit = this.renderHeight - 35;\n        if (diff < 35) {\n            this.scrollTop += Math.max(-14, Math.floor(0.3 * (diff - 35)));\n        }\n        else if (diff > upperLimit) {\n            this.scrollTop += Math.min(14, Math.floor(0.3 * (diff - upperLimit)));\n        }\n    }\n    teardownDragAndDropScrollTopAnimation() {\n        this.dragOverAnimationStopDisposable.dispose();\n        if (this.dragOverAnimationDisposable) {\n            this.dragOverAnimationDisposable.dispose();\n            this.dragOverAnimationDisposable = undefined;\n        }\n    }\n    // Util\n    getTargetSector(browserEvent, targetIndex) {\n        if (targetIndex === undefined) {\n            return undefined;\n        }\n        const relativePosition = browserEvent.offsetY / this.items[targetIndex].size;\n        const sector = Math.floor(relativePosition / 0.25);\n        return clamp(sector, 0, 3);\n    }\n    getItemIndexFromEventTarget(target) {\n        const scrollableElement = this.scrollableElement.getDomNode();\n        let element = target;\n        while ((isHTMLElement(element) || isSVGElement(element)) && element !== this.rowsContainer && scrollableElement.contains(element)) {\n            const rawIndex = element.getAttribute('data-index');\n            if (rawIndex) {\n                const index = Number(rawIndex);\n                if (!isNaN(index)) {\n                    return index;\n                }\n            }\n            element = element.parentElement;\n        }\n        return undefined;\n    }\n    getRenderRange(renderTop, renderHeight) {\n        return {\n            start: this.rangeMap.indexAt(renderTop),\n            end: this.rangeMap.indexAfter(renderTop + renderHeight - 1)\n        };\n    }\n    /**\n     * Given a stable rendered state, checks every rendered element whether it needs\n     * to be probed for dynamic height. Adjusts scroll height and top if necessary.\n     */\n    _rerender(renderTop, renderHeight, inSmoothScrolling) {\n        const previousRenderRange = this.getRenderRange(renderTop, renderHeight);\n        // Let's remember the second element's position, this helps in scrolling up\n        // and preserving a linear upwards scroll movement\n        let anchorElementIndex;\n        let anchorElementTopDelta;\n        if (renderTop === this.elementTop(previousRenderRange.start)) {\n            anchorElementIndex = previousRenderRange.start;\n            anchorElementTopDelta = 0;\n        }\n        else if (previousRenderRange.end - previousRenderRange.start > 1) {\n            anchorElementIndex = previousRenderRange.start + 1;\n            anchorElementTopDelta = this.elementTop(anchorElementIndex) - renderTop;\n        }\n        let heightDiff = 0;\n        while (true) {\n            const renderRange = this.getRenderRange(renderTop, renderHeight);\n            let didChange = false;\n            for (let i = renderRange.start; i < renderRange.end; i++) {\n                const diff = this.probeDynamicHeight(i);\n                if (diff !== 0) {\n                    this.rangeMap.splice(i, 1, [this.items[i]]);\n                }\n                heightDiff += diff;\n                didChange = didChange || diff !== 0;\n            }\n            if (!didChange) {\n                if (heightDiff !== 0) {\n                    this.eventuallyUpdateScrollDimensions();\n                }\n                const unrenderRanges = Range.relativeComplement(previousRenderRange, renderRange);\n                for (const range of unrenderRanges) {\n                    for (let i = range.start; i < range.end; i++) {\n                        if (this.items[i].row) {\n                            this.removeItemFromDOM(i);\n                        }\n                    }\n                }\n                const renderRanges = Range.relativeComplement(renderRange, previousRenderRange).reverse();\n                for (const range of renderRanges) {\n                    for (let i = range.end - 1; i >= range.start; i--) {\n                        this.insertItemInDOM(i);\n                    }\n                }\n                for (let i = renderRange.start; i < renderRange.end; i++) {\n                    if (this.items[i].row) {\n                        this.updateItemInDOM(this.items[i], i);\n                    }\n                }\n                if (typeof anchorElementIndex === 'number') {\n                    // To compute a destination scroll top, we need to take into account the current smooth scrolling\n                    // animation, and then reuse it with a new target (to avoid prolonging the scroll)\n                    // See https://github.com/microsoft/vscode/issues/104144\n                    // See https://github.com/microsoft/vscode/pull/104284\n                    // See https://github.com/microsoft/vscode/issues/107704\n                    const deltaScrollTop = this.scrollable.getFutureScrollPosition().scrollTop - renderTop;\n                    const newScrollTop = this.elementTop(anchorElementIndex) - anchorElementTopDelta + deltaScrollTop;\n                    this.setScrollTop(newScrollTop, inSmoothScrolling);\n                }\n                this._onDidChangeContentHeight.fire(this.contentHeight);\n                return;\n            }\n        }\n    }\n    probeDynamicHeight(index) {\n        const item = this.items[index];\n        if (!!this.virtualDelegate.getDynamicHeight) {\n            const newSize = this.virtualDelegate.getDynamicHeight(item.element);\n            if (newSize !== null) {\n                const size = item.size;\n                item.size = newSize;\n                item.lastDynamicHeightWidth = this.renderWidth;\n                return newSize - size;\n            }\n        }\n        if (!item.hasDynamicHeight || item.lastDynamicHeightWidth === this.renderWidth) {\n            return 0;\n        }\n        if (!!this.virtualDelegate.hasDynamicHeight && !this.virtualDelegate.hasDynamicHeight(item.element)) {\n            return 0;\n        }\n        const size = item.size;\n        if (item.row) {\n            item.row.domNode.style.height = '';\n            item.size = item.row.domNode.offsetHeight;\n            if (item.size === 0 && !isAncestor(item.row.domNode, getWindow(item.row.domNode).document.body)) {\n                console.warn('Measuring item node that is not in DOM! Add ListView to the DOM before measuring row height!', new Error().stack);\n            }\n            item.lastDynamicHeightWidth = this.renderWidth;\n            return item.size - size;\n        }\n        const { row } = this.cache.alloc(item.templateId);\n        row.domNode.style.height = '';\n        this.rowsContainer.appendChild(row.domNode);\n        const renderer = this.renderers.get(item.templateId);\n        if (!renderer) {\n            throw new BugIndicatingError('Missing renderer for templateId: ' + item.templateId);\n        }\n        renderer.renderElement(item.element, index, row.templateData, undefined);\n        item.size = row.domNode.offsetHeight;\n        renderer.disposeElement?.(item.element, index, row.templateData, undefined);\n        this.virtualDelegate.setDynamicHeight?.(item.element, item.size);\n        item.lastDynamicHeightWidth = this.renderWidth;\n        row.domNode.remove();\n        this.cache.release(row);\n        return item.size - size;\n    }\n    getElementDomId(index) {\n        return `${this.domId}_${index}`;\n    }\n    // Dispose\n    dispose() {\n        for (const item of this.items) {\n            item.dragStartDisposable.dispose();\n            item.checkedDisposable.dispose();\n            if (item.row) {\n                const renderer = this.renderers.get(item.row.templateId);\n                if (renderer) {\n                    renderer.disposeElement?.(item.element, -1, item.row.templateData, undefined);\n                    renderer.disposeTemplate(item.row.templateData);\n                }\n            }\n        }\n        this.items = [];\n        this.domNode?.remove();\n        this.dragOverAnimationDisposable?.dispose();\n        this.disposables.dispose();\n    }\n}\n__decorate([\n    memoize\n], ListView.prototype, \"onMouseClick\", null);\n__decorate([\n    memoize\n], ListView.prototype, \"onMouseDblClick\", null);\n__decorate([\n    memoize\n], ListView.prototype, \"onMouseMiddleClick\", null);\n__decorate([\n    memoize\n], ListView.prototype, \"onMouseDown\", null);\n__decorate([\n    memoize\n], ListView.prototype, \"onMouseOver\", null);\n__decorate([\n    memoize\n], ListView.prototype, \"onMouseOut\", null);\n__decorate([\n    memoize\n], ListView.prototype, \"onContextMenu\", null);\n__decorate([\n    memoize\n], ListView.prototype, \"onTouchStart\", null);\n__decorate([\n    memoize\n], ListView.prototype, \"onTap\", null);\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,IAAIA,UAAU,GAAI,IAAI,IAAI,IAAI,CAACA,UAAU,IAAK,UAAUC,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAE;EACnF,IAAIC,CAAC,GAAGC,SAAS,CAACC,MAAM;IAAEC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGH,MAAM,GAAGE,IAAI,KAAK,IAAI,GAAGA,IAAI,GAAGK,MAAM,CAACC,wBAAwB,CAACR,MAAM,EAAEC,GAAG,CAAC,GAAGC,IAAI;IAAEO,CAAC;EAC5H,IAAI,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,CAACC,QAAQ,KAAK,UAAU,EAAEL,CAAC,GAAGI,OAAO,CAACC,QAAQ,CAACZ,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,CAAC,CAAC,KAC1H,KAAK,IAAIU,CAAC,GAAGb,UAAU,CAACM,MAAM,GAAG,CAAC,EAAEO,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE,IAAIH,CAAC,GAAGV,UAAU,CAACa,CAAC,CAAC,EAAEN,CAAC,GAAG,CAACH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACH,CAAC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACT,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,GAAGG,CAAC,CAACT,MAAM,EAAEC,GAAG,CAAC,KAAKK,CAAC;EACjJ,OAAOH,CAAC,GAAG,CAAC,IAAIG,CAAC,IAAIC,MAAM,CAACM,cAAc,CAACb,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,EAAEA,CAAC;AACjE,CAAC;AACD,SAASQ,aAAa,QAAQ,cAAc;AAC5C,SAASC,CAAC,EAAEC,qBAAqB,EAAEC,OAAO,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,UAAU,EAAEC,aAAa,EAAEC,YAAY,EAAEC,4BAA4B,QAAQ,cAAc;AACvM,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,SAASC,SAAS,IAAIC,cAAc,EAAEC,OAAO,QAAQ,gBAAgB;AACrE,SAASC,uBAAuB,QAAQ,mCAAmC;AAC3E,SAASC,QAAQ,EAAEC,MAAM,QAAQ,2BAA2B;AAC5D,SAASC,OAAO,EAAEC,iBAAiB,QAAQ,0BAA0B;AACrE,SAASC,OAAO,QAAQ,+BAA+B;AACvD,SAASC,OAAO,EAAEC,KAAK,QAAQ,0BAA0B;AACzD,SAASC,UAAU,EAAEC,eAAe,EAAEC,YAAY,QAAQ,8BAA8B;AACxF,SAASC,KAAK,QAAQ,0BAA0B;AAChD,SAASC,UAAU,QAAQ,+BAA+B;AAC1D,SAASC,QAAQ,EAAEC,KAAK,QAAQ,eAAe;AAC/C,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,kBAAkB,QAAQ,2BAA2B;AAC9D,SAASC,KAAK,QAAQ,4BAA4B;AAClD,MAAMC,SAAS,GAAG;EACdC,sBAAsB,EAAEC;AAC5B,CAAC;AACD,MAAMC,cAAc,GAAG;EACnBC,UAAU,EAAE,IAAI;EAChBC,kBAAkB,EAAE,CAAC,CAAC;EACtBC,gBAAgB,EAAE,IAAI;EACtBC,YAAY,EAAE,IAAI;EAClBC,qBAAqB,EAAE,KAAK;EAC5BC,GAAG,EAAE;IACDC,eAAeA,CAACC,CAAC,EAAE;MAAE,OAAO,CAACA,CAAC,CAAC;IAAE,CAAC;IAClCC,UAAUA,CAAA,EAAG;MAAE,OAAO,IAAI;IAAE,CAAC;IAC7BC,WAAWA,CAAA,EAAG,CAAE,CAAC;IACjBC,UAAUA,CAAA,EAAG;MAAE,OAAO,KAAK;IAAE,CAAC;IAC9BC,IAAIA,CAAA,EAAG,CAAE,CAAC;IACVC,OAAOA,CAAA,EAAG,CAAE;EAChB,CAAC;EACDC,mBAAmB,EAAE,KAAK;EAC1BC,qBAAqB,EAAE,IAAI;EAC3BC,uBAAuB,EAAE;AAC7B,CAAC;AACD,OAAO,MAAMC,uBAAuB,CAAC;EACjCC,WAAWA,CAACC,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACAC,MAAMA,CAAA,EAAG,CAAE;EACXC,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACF,QAAQ;EACxB;AACJ;AACA,OAAO,MAAMG,+BAA+B,CAAC;EACzCJ,WAAWA,CAACC,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACAC,MAAMA,CAAA,EAAG,CAAE;EACXC,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACF,QAAQ;EACxB;AACJ;AACA,OAAO,MAAMI,qBAAqB,CAAC;EAC/BL,WAAWA,CAAA,EAAG;IACV,IAAI,CAACM,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,KAAK,GAAG,EAAE;EACnB;EACAL,MAAMA,CAACM,YAAY,EAAE;IACjB,IAAIA,YAAY,CAACF,KAAK,EAAE;MACpB,IAAI,CAACA,KAAK,CAACG,MAAM,CAAC,CAAC,EAAE,IAAI,CAACH,KAAK,CAACtE,MAAM,EAAE,GAAGwE,YAAY,CAACF,KAAK,CAAC;IAClE;IACA,IAAIE,YAAY,CAACD,KAAK,EAAE;MACpB,IAAI,CAACA,KAAK,CAACE,MAAM,CAAC,CAAC,EAAE,IAAI,CAACF,KAAK,CAACvE,MAAM,CAAC;MACvC,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiE,YAAY,CAACD,KAAK,CAACvE,MAAM,EAAEO,CAAC,EAAE,EAAE;QAChD,MAAMmE,IAAI,GAAGF,YAAY,CAACD,KAAK,CAACI,IAAI,CAACpE,CAAC,CAAC;QACvC,IAAImE,IAAI,KAAKA,IAAI,CAACE,IAAI,IAAIF,IAAI,CAACG,IAAI,CAAC,EAAE;UAClC,IAAI,CAACN,KAAK,CAACO,IAAI,CAACJ,IAAI,CAAC;QACzB;MACJ;IACJ;EACJ;EACAP,OAAOA,CAAA,EAAG;IACN,OAAO;MACHG,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBC,KAAK,EAAE,IAAI,CAACA;IAChB,CAAC;EACL;AACJ;AACA,SAASQ,kBAAkBA,CAACC,EAAE,EAAEC,EAAE,EAAE;EAChC,IAAIC,KAAK,CAACC,OAAO,CAACH,EAAE,CAAC,IAAIE,KAAK,CAACC,OAAO,CAACF,EAAE,CAAC,EAAE;IACxC,OAAOtD,MAAM,CAACqD,EAAE,EAAEC,EAAE,CAAC;EACzB;EACA,OAAOD,EAAE,KAAKC,EAAE;AACpB;AACA,MAAMG,6BAA6B,CAAC;EAChCpB,WAAWA,CAACqB,qBAAqB,EAAE;IAC/B,IAAIA,qBAAqB,EAAEC,UAAU,EAAE;MACnC,IAAI,CAACA,UAAU,GAAGD,qBAAqB,CAACC,UAAU,CAACC,IAAI,CAACF,qBAAqB,CAAC;IAClF,CAAC,MACI;MACD,IAAI,CAACC,UAAU,GAAG,CAAChC,CAAC,EAAE/C,CAAC,EAAEiF,CAAC,KAAKA,CAAC;IACpC;IACA,IAAIH,qBAAqB,EAAEI,WAAW,EAAE;MACpC,IAAI,CAACA,WAAW,GAAGJ,qBAAqB,CAACI,WAAW,CAACF,IAAI,CAACF,qBAAqB,CAAC;IACpF,CAAC,MACI;MACD,IAAI,CAACI,WAAW,GAAG,CAACnC,CAAC,EAAE/C,CAAC,KAAKA,CAAC,GAAG,CAAC;IACtC;IACA,IAAI8E,qBAAqB,EAAEK,OAAO,EAAE;MAChC,IAAI,CAACA,OAAO,GAAGL,qBAAqB,CAACK,OAAO,CAACH,IAAI,CAACF,qBAAqB,CAAC;IAC5E,CAAC,MACI;MACD,IAAI,CAACK,OAAO,GAAGC,CAAC,IAAI,UAAU;IAClC;IACA,IAAIN,qBAAqB,EAAEO,SAAS,EAAE;MAClC,IAAI,CAACA,SAAS,GAAGP,qBAAqB,CAACO,SAAS,CAACL,IAAI,CAACF,qBAAqB,CAAC;IAChF,CAAC,MACI;MACD,IAAI,CAACO,SAAS,GAAGD,CAAC,IAAI9C,SAAS;IACnC;EACJ;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMgD,QAAQ,CAAC;EAClB;IAAS,IAAI,CAACC,aAAa,GAAG,CAAC;EAAE;EACjC,IAAIC,aAAaA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACC,QAAQ,CAACpB,IAAI;EAAE;EACjD,IAAIqB,WAAWA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACC,iBAAiB,CAACC,QAAQ;EAAE;EAC5D,IAAIC,wBAAwBA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACF,iBAAiB,CAACG,UAAU,CAAC,CAAC;EAAE;EAC7E,IAAIzC,mBAAmBA,CAAA,EAAG;IAAE,OAAO,IAAI,CAAC0C,oBAAoB;EAAE;EAC9D,IAAI1C,mBAAmBA,CAAC2C,KAAK,EAAE;IAC3B,IAAIA,KAAK,KAAK,IAAI,CAACD,oBAAoB,EAAE;MACrC;IACJ;IACA,IAAIC,KAAK,IAAI,IAAI,CAACpD,qBAAqB,EAAE;MACrC,MAAM,IAAIqD,KAAK,CAAC,uEAAuE,CAAC;IAC5F;IACA,IAAI,CAACF,oBAAoB,GAAGC,KAAK;IACjC,IAAI,CAACE,OAAO,CAACC,SAAS,CAACC,MAAM,CAAC,sBAAsB,EAAE,IAAI,CAACL,oBAAoB,CAAC;IAChF,IAAI,IAAI,CAACA,oBAAoB,EAAE;MAC3B,KAAK,MAAM3B,IAAI,IAAI,IAAI,CAACiC,KAAK,EAAE;QAC3B,IAAI,CAACC,gBAAgB,CAAClC,IAAI,CAAC;MAC/B;MACA,IAAI,CAACmC,iBAAiB,CAAC,CAAC;MACxB,IAAI,CAACZ,iBAAiB,CAACa,mBAAmB,CAAC;QAAEC,KAAK,EAAElG,eAAe,CAAC,IAAI,CAAC2F,OAAO;MAAE,CAAC,CAAC;MACpF,IAAI,CAACQ,aAAa,CAACC,KAAK,CAACF,KAAK,GAAG,GAAGG,IAAI,CAACC,GAAG,CAAC,IAAI,CAACC,WAAW,IAAI,CAAC,EAAE,IAAI,CAACC,WAAW,CAAC,IAAI;IAC7F,CAAC,MACI;MACD,IAAI,CAACC,6BAA6B,CAACC,MAAM,CAAC,CAAC;MAC3C,IAAI,CAACtB,iBAAiB,CAACa,mBAAmB,CAAC;QAAEC,KAAK,EAAE,IAAI,CAACM,WAAW;QAAED,WAAW,EAAE,IAAI,CAACC;MAAY,CAAC,CAAC;MACtG,IAAI,CAACL,aAAa,CAACC,KAAK,CAACF,KAAK,GAAG,EAAE;IACvC;EACJ;EACAhD,WAAWA,CAACyD,SAAS,EAAEC,eAAe,EAAEC,SAAS,EAAEC,OAAO,GAAG9E,cAAc,EAAE;IACzE,IAAI,CAAC4E,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACG,KAAK,GAAG,WAAW,EAAEhC,QAAQ,CAACC,aAAa,EAAE;IAClD,IAAI,CAAC6B,SAAS,GAAG,IAAIG,GAAG,CAAC,CAAC;IAC1B,IAAI,CAACR,WAAW,GAAG,CAAC;IACpB,IAAI,CAACS,aAAa,GAAG,CAAC;IACtB,IAAI,CAACC,iCAAiC,GAAG,IAAI;IAC7C,IAAI,CAACT,6BAA6B,GAAG,IAAI3F,OAAO,CAAC,EAAE,CAAC;IACpD,IAAI,CAACqG,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,+BAA+B,GAAGjG,UAAU,CAACkG,IAAI;IACtD,IAAI,CAACC,cAAc,GAAG,CAAC;IACvB,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,6BAA6B,GAAGrG,UAAU,CAACkG,IAAI;IACpD,IAAI,CAACI,kBAAkB,GAAGtG,UAAU,CAACkG,IAAI;IACzC,IAAI,CAACK,WAAW,GAAG,IAAItG,eAAe,CAAC,CAAC;IACxC,IAAI,CAACuG,yBAAyB,GAAG,IAAI1G,OAAO,CAAC,CAAC;IAC9C,IAAI,CAAC2G,wBAAwB,GAAG,IAAI3G,OAAO,CAAC,CAAC;IAC7C,IAAI,CAAC4G,wBAAwB,GAAG3G,KAAK,CAAC4G,KAAK,CAAC,IAAI,CAACH,yBAAyB,CAACI,KAAK,EAAEhG,SAAS,EAAE,IAAI,CAAC2F,WAAW,CAAC;IAC9G,IAAI,CAAClC,oBAAoB,GAAG,KAAK;IACjC,IAAIsB,OAAO,CAAChE,mBAAmB,IAAIgE,OAAO,CAACzE,qBAAqB,EAAE;MAC9D,MAAM,IAAIqD,KAAK,CAAC,uEAAuE,CAAC;IAC5F;IACA,IAAI,CAACI,KAAK,GAAG,EAAE;IACf,IAAI,CAACkC,MAAM,GAAG,CAAC;IACf,IAAI,CAAC9C,QAAQ,GAAG,IAAI,CAAC+C,cAAc,CAACnB,OAAO,CAACoB,UAAU,IAAI,CAAC,CAAC;IAC5D,KAAK,MAAMC,QAAQ,IAAItB,SAAS,EAAE;MAC9B,IAAI,CAACA,SAAS,CAACuB,GAAG,CAACD,QAAQ,CAACE,UAAU,EAAEF,QAAQ,CAAC;IACrD;IACA,IAAI,CAACG,KAAK,GAAG,IAAI,CAACZ,WAAW,CAACa,GAAG,CAAC,IAAI7G,QAAQ,CAAC,IAAI,CAACmF,SAAS,CAAC,CAAC;IAC/D,IAAI,CAAC2B,aAAa,GAAG,CAAC;IACtB,IAAI,CAACC,gBAAgB,GAAG,CAAC;IACzB,IAAI,CAAC9C,OAAO,GAAG+C,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC5C,IAAI,CAAChD,OAAO,CAACiD,SAAS,GAAG,aAAa;IACtC,IAAI,CAACjD,OAAO,CAACC,SAAS,CAAC2C,GAAG,CAAC,IAAI,CAACxB,KAAK,CAAC;IACtC,IAAI,CAACpB,OAAO,CAACkD,QAAQ,GAAG,CAAC;IACzB,IAAI,CAAClD,OAAO,CAACC,SAAS,CAACC,MAAM,CAAC,eAAe,EAAE,OAAOiB,OAAO,CAACgC,YAAY,KAAK,SAAS,GAAGhC,OAAO,CAACgC,YAAY,GAAG,IAAI,CAAC;IACvH,IAAI,CAACtD,oBAAoB,GAAGsB,OAAO,CAAChE,mBAAmB,IAAId,cAAc,CAACc,mBAAmB;IAC7F,IAAI,CAAC6C,OAAO,CAACC,SAAS,CAACC,MAAM,CAAC,sBAAsB,EAAE,IAAI,CAACL,oBAAoB,CAAC;IAChF,IAAI,CAACuD,aAAa,GAAG,OAAOjC,OAAO,CAACiC,aAAa,KAAK,WAAW,GAAG,CAAC,GAAGjC,OAAO,CAACiC,aAAa;IAC7F,IAAI,CAACxE,qBAAqB,GAAG,IAAID,6BAA6B,CAACwC,OAAO,CAACvC,qBAAqB,CAAC;IAC7F,IAAI,CAAC4B,aAAa,GAAGuC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAClD,IAAI,CAACxC,aAAa,CAACyC,SAAS,GAAG,kBAAkB;IACjD,MAAM7F,qBAAqB,GAAG+D,OAAO,CAAC/D,qBAAqB,IAAIf,cAAc,CAACe,qBAAqB;IACnG,IAAIA,qBAAqB,EAAE;MACvB,IAAI,CAACoD,aAAa,CAACC,KAAK,CAAC4C,SAAS,GAAG,4BAA4B;MACjE,IAAI,CAAC7C,aAAa,CAACC,KAAK,CAAC6C,QAAQ,GAAG,QAAQ;MAC5C,IAAI,CAAC9C,aAAa,CAACC,KAAK,CAAC8C,OAAO,GAAG,QAAQ;IAC/C;IACA,IAAI,CAACxB,WAAW,CAACa,GAAG,CAAC7H,OAAO,CAACyI,SAAS,CAAC,IAAI,CAAChD,aAAa,CAAC,CAAC;IAC3D,IAAI,CAACiD,UAAU,GAAG,IAAI,CAAC1B,WAAW,CAACa,GAAG,CAAC,IAAIhH,UAAU,CAAC;MAClD8H,kBAAkB,EAAE,IAAI;MACxBC,oBAAoB,EAAGxC,OAAO,CAACyC,eAAe,IAAI,KAAK,GAAI,GAAG,GAAG,CAAC;MAClEjJ,4BAA4B,EAAEkJ,EAAE,IAAIlJ,4BAA4B,CAACJ,SAAS,CAAC,IAAI,CAACyF,OAAO,CAAC,EAAE6D,EAAE;IAChG,CAAC,CAAC,CAAC;IACH,IAAI,CAACpE,iBAAiB,GAAG,IAAI,CAACsC,WAAW,CAACa,GAAG,CAAC,IAAI5H,uBAAuB,CAAC,IAAI,CAACwF,aAAa,EAAE;MAC1FnD,uBAAuB,EAAE8D,OAAO,CAAC9D,uBAAuB,IAAIhB,cAAc,CAACgB,uBAAuB;MAClGyG,UAAU,EAAE,CAAC,CAAC;MACdC,QAAQ,EAAE5C,OAAO,CAAC5E,kBAAkB,IAAIF,cAAc,CAACE,kBAAkB;MACzED,UAAU,EAAE6E,OAAO,CAAC7E,UAAU,IAAID,cAAc,CAACC,UAAU;MAC3D0H,2BAA2B,EAAE7C,OAAO,CAAC6C,2BAA2B;MAChEC,qBAAqB,EAAE9C,OAAO,CAAC8C,qBAAqB;MACpDC,YAAY,EAAE/C,OAAO,CAAC+C;IAC1B,CAAC,EAAE,IAAI,CAACT,UAAU,CAAC,CAAC;IACpB,IAAI,CAACzD,OAAO,CAACmE,WAAW,CAAC,IAAI,CAAC1E,iBAAiB,CAACG,UAAU,CAAC,CAAC,CAAC;IAC7DoB,SAAS,CAACmD,WAAW,CAAC,IAAI,CAACnE,OAAO,CAAC;IACnC,IAAI,CAACP,iBAAiB,CAACC,QAAQ,CAAC,IAAI,CAACA,QAAQ,EAAE,IAAI,EAAE,IAAI,CAACqC,WAAW,CAAC;IACtE,IAAI,CAACA,WAAW,CAACa,GAAG,CAAC1I,qBAAqB,CAAC,IAAI,CAACsG,aAAa,EAAE1F,cAAc,CAACsJ,MAAM,EAAEvH,CAAC,IAAI,IAAI,CAACwH,aAAa,CAACxH,CAAC,CAAC,CAAC,CAAC;IAClH;IACA;IACA,IAAI,CAACkF,WAAW,CAACa,GAAG,CAAC1I,qBAAqB,CAAC,IAAI,CAACuF,iBAAiB,CAACG,UAAU,CAAC,CAAC,EAAE,QAAQ,EAAE/C,CAAC,IAAIA,CAAC,CAAC3D,MAAM,CAACoL,SAAS,GAAG,CAAC,CAAC,CAAC;IACvH,IAAI,CAACvC,WAAW,CAACa,GAAG,CAAC1I,qBAAqB,CAAC,IAAI,CAAC8F,OAAO,EAAE,UAAU,EAAEnD,CAAC,IAAI,IAAI,CAACG,UAAU,CAAC,IAAI,CAACuH,WAAW,CAAC1H,CAAC,CAAC,CAAC,CAAC,CAAC;IAChH,IAAI,CAACkF,WAAW,CAACa,GAAG,CAAC1I,qBAAqB,CAAC,IAAI,CAAC8F,OAAO,EAAE,MAAM,EAAEnD,CAAC,IAAI,IAAI,CAAC2H,MAAM,CAAC,IAAI,CAACD,WAAW,CAAC1H,CAAC,CAAC,CAAC,CAAC,CAAC;IACxG,IAAI,CAACkF,WAAW,CAACa,GAAG,CAAC1I,qBAAqB,CAAC,IAAI,CAAC8F,OAAO,EAAE,WAAW,EAAEnD,CAAC,IAAI,IAAI,CAAC4H,WAAW,CAAC,IAAI,CAACF,WAAW,CAAC1H,CAAC,CAAC,CAAC,CAAC,CAAC;IAClH,IAAI,CAACkF,WAAW,CAACa,GAAG,CAAC1I,qBAAqB,CAAC,IAAI,CAAC8F,OAAO,EAAE,SAAS,EAAEnD,CAAC,IAAI,IAAI,CAAC6H,SAAS,CAAC7H,CAAC,CAAC,CAAC,CAAC;IAC5F,IAAI,CAACL,gBAAgB,GAAG2E,OAAO,CAAC3E,gBAAgB,IAAIH,cAAc,CAACG,gBAAgB;IACnF,IAAI,CAACC,YAAY,GAAG0E,OAAO,CAAC1E,YAAY,IAAIJ,cAAc,CAACI,YAAY;IACvE,IAAI,CAACC,qBAAqB,GAAGyE,OAAO,CAACzE,qBAAqB,IAAIL,cAAc,CAACK,qBAAqB;IAClG,IAAI,CAACC,GAAG,GAAGwE,OAAO,CAACxE,GAAG,IAAI,IAAI,CAACoF,WAAW,CAACa,GAAG,CAACvG,cAAc,CAACM,GAAG,CAAC;IAClE,IAAI,CAACgI,MAAM,CAACxD,OAAO,CAACyD,WAAW,EAAEC,MAAM,EAAE1D,OAAO,CAACyD,WAAW,EAAErE,KAAK,CAAC;EACxE;EACAuE,aAAaA,CAAC3D,OAAO,EAAE;IACnB,IAAIA,OAAO,CAACiC,aAAa,KAAKhH,SAAS,EAAE;MACrC,IAAI,CAACgH,aAAa,GAAGjC,OAAO,CAACiC,aAAa;MAC1C,IAAI,CAAC3D,iBAAiB,CAACa,mBAAmB,CAAC;QAAEyE,YAAY,EAAE,IAAI,CAACA;MAAa,CAAC,CAAC;IACnF;IACA,IAAI5D,OAAO,CAACyC,eAAe,KAAKxH,SAAS,EAAE;MACvC,IAAI,CAACqH,UAAU,CAACuB,uBAAuB,CAAC7D,OAAO,CAACyC,eAAe,GAAG,GAAG,GAAG,CAAC,CAAC;IAC9E;IACA,IAAIzC,OAAO,CAAChE,mBAAmB,KAAKf,SAAS,EAAE;MAC3C,IAAI,CAACe,mBAAmB,GAAGgE,OAAO,CAAChE,mBAAmB;IAC1D;IACA,IAAI8H,iBAAiB;IACrB,IAAI9D,OAAO,CAAC+C,YAAY,KAAK9H,SAAS,EAAE;MACpC6I,iBAAiB,GAAG;QAAE,IAAIA,iBAAiB,IAAI,CAAC,CAAC,CAAC;QAAEf,YAAY,EAAE/C,OAAO,CAAC+C;MAAa,CAAC;IAC5F;IACA,IAAI/C,OAAO,CAAC6C,2BAA2B,KAAK5H,SAAS,EAAE;MACnD6I,iBAAiB,GAAG;QAAE,IAAIA,iBAAiB,IAAI,CAAC,CAAC,CAAC;QAAEjB,2BAA2B,EAAE7C,OAAO,CAAC6C;MAA4B,CAAC;IAC1H;IACA,IAAI7C,OAAO,CAAC8C,qBAAqB,KAAK7H,SAAS,EAAE;MAC7C6I,iBAAiB,GAAG;QAAE,IAAIA,iBAAiB,IAAI,CAAC,CAAC,CAAC;QAAEhB,qBAAqB,EAAE9C,OAAO,CAAC8C;MAAsB,CAAC;IAC9G;IACA,IAAIgB,iBAAiB,EAAE;MACnB,IAAI,CAACxF,iBAAiB,CAACqF,aAAa,CAACG,iBAAiB,CAAC;IAC3D;IACA,IAAI9D,OAAO,CAACoB,UAAU,KAAKnG,SAAS,IAAI+E,OAAO,CAACoB,UAAU,KAAK,IAAI,CAAChD,QAAQ,CAACgD,UAAU,EAAE;MACrF;MACA,MAAM2C,eAAe,GAAG,IAAI,CAACC,cAAc,CAAC,IAAI,CAACtC,aAAa,EAAE,IAAI,CAACC,gBAAgB,CAAC;MACtF,MAAMsC,MAAM,GAAGjE,OAAO,CAACoB,UAAU,GAAG,IAAI,CAAChD,QAAQ,CAACgD,UAAU;MAC5D,IAAI,CAAChD,QAAQ,CAACgD,UAAU,GAAGpB,OAAO,CAACoB,UAAU;MAC7C,IAAI,CAAC8C,MAAM,CAACH,eAAe,EAAExE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAACkC,aAAa,GAAGuC,MAAM,CAAC,EAAE,IAAI,CAACtC,gBAAgB,EAAE1G,SAAS,EAAEA,SAAS,EAAE,IAAI,CAAC;MACzH,IAAI,CAACkJ,YAAY,CAAC,IAAI,CAACzC,aAAa,CAAC;MACrC,IAAI,CAAC0C,gCAAgC,CAAC,CAAC;MACvC,IAAI,IAAI,CAAC7I,qBAAqB,EAAE;QAC5B,IAAI,CAAC8I,SAAS,CAAC,IAAI,CAAC3C,aAAa,EAAE,IAAI,CAACC,gBAAgB,CAAC;MAC7D;IACJ;EACJ;EACAR,cAAcA,CAACC,UAAU,EAAE;IACvB,OAAO,IAAI1G,QAAQ,CAAC0G,UAAU,CAAC;EACnC;EACAvE,MAAMA,CAACyH,KAAK,EAAEC,WAAW,EAAElI,QAAQ,GAAG,EAAE,EAAE;IACtC,IAAI,IAAI,CAACgE,QAAQ,EAAE;MACf,MAAM,IAAIzB,KAAK,CAAC,+BAA+B,CAAC;IACpD;IACA,IAAI,CAACyB,QAAQ,GAAG,IAAI;IACpB,IAAI;MACA,OAAO,IAAI,CAACmE,OAAO,CAACF,KAAK,EAAEC,WAAW,EAAElI,QAAQ,CAAC;IACrD,CAAC,SACO;MACJ,IAAI,CAACgE,QAAQ,GAAG,KAAK;MACrB,IAAI,CAACQ,yBAAyB,CAAC4D,IAAI,CAAC,IAAI,CAACtG,aAAa,CAAC;IAC3D;EACJ;EACAqG,OAAOA,CAACF,KAAK,EAAEC,WAAW,EAAElI,QAAQ,GAAG,EAAE,EAAE;IACvC,MAAMqI,mBAAmB,GAAG,IAAI,CAACV,cAAc,CAAC,IAAI,CAACtC,aAAa,EAAE,IAAI,CAACC,gBAAgB,CAAC;IAC1F,MAAMgD,WAAW,GAAG;MAAEL,KAAK;MAAEM,GAAG,EAAEN,KAAK,GAAGC;IAAY,CAAC;IACvD,MAAMM,WAAW,GAAGrK,KAAK,CAACsK,SAAS,CAACJ,mBAAmB,EAAEC,WAAW,CAAC;IACrE;IACA,MAAMI,aAAa,GAAG,IAAI7E,GAAG,CAAC,CAAC;IAC/B,KAAK,IAAIvH,CAAC,GAAGkM,WAAW,CAACD,GAAG,GAAG,CAAC,EAAEjM,CAAC,IAAIkM,WAAW,CAACP,KAAK,EAAE3L,CAAC,EAAE,EAAE;MAC3D,MAAMoE,IAAI,GAAG,IAAI,CAACiC,KAAK,CAACrG,CAAC,CAAC;MAC1BoE,IAAI,CAACiI,mBAAmB,CAACjJ,OAAO,CAAC,CAAC;MAClCgB,IAAI,CAACkI,iBAAiB,CAAClJ,OAAO,CAAC,CAAC;MAChC,IAAIgB,IAAI,CAACmI,GAAG,EAAE;QACV,IAAIC,IAAI,GAAGJ,aAAa,CAACK,GAAG,CAACrI,IAAI,CAACwE,UAAU,CAAC;QAC7C,IAAI,CAAC4D,IAAI,EAAE;UACPA,IAAI,GAAG,EAAE;UACTJ,aAAa,CAACzD,GAAG,CAACvE,IAAI,CAACwE,UAAU,EAAE4D,IAAI,CAAC;QAC5C;QACA,MAAM9D,QAAQ,GAAG,IAAI,CAACtB,SAAS,CAACqF,GAAG,CAACrI,IAAI,CAACwE,UAAU,CAAC;QACpD,IAAIF,QAAQ,IAAIA,QAAQ,CAACgE,cAAc,EAAE;UACrChE,QAAQ,CAACgE,cAAc,CAACtI,IAAI,CAACuI,OAAO,EAAE3M,CAAC,EAAEoE,IAAI,CAACmI,GAAG,CAACK,YAAY,EAAExI,IAAI,CAACC,IAAI,CAAC;QAC9E;QACAmI,IAAI,CAACK,OAAO,CAACzI,IAAI,CAACmI,GAAG,CAAC;MAC1B;MACAnI,IAAI,CAACmI,GAAG,GAAG,IAAI;MACfnI,IAAI,CAAC0I,KAAK,GAAG,IAAI;IACrB;IACA,MAAMC,iBAAiB,GAAG;MAAEpB,KAAK,EAAEA,KAAK,GAAGC,WAAW;MAAEK,GAAG,EAAE,IAAI,CAAC5F,KAAK,CAAC5G;IAAO,CAAC;IAChF,MAAMuN,yBAAyB,GAAGnL,KAAK,CAACsK,SAAS,CAACY,iBAAiB,EAAEhB,mBAAmB,CAAC;IACzF,MAAMkB,4BAA4B,GAAGpL,KAAK,CAACqL,kBAAkB,CAACH,iBAAiB,EAAEhB,mBAAmB,CAAC;IACrG,MAAMoB,QAAQ,GAAGzJ,QAAQ,CAAC0J,GAAG,CAACT,OAAO,KAAK;MACtCU,EAAE,EAAEC,MAAM,CAAC,IAAI,CAAC/E,MAAM,EAAE,CAAC;MACzBoE,OAAO;MACP/D,UAAU,EAAE,IAAI,CAACzB,eAAe,CAACoG,aAAa,CAACZ,OAAO,CAAC;MACvDtI,IAAI,EAAE,IAAI,CAAC8C,eAAe,CAACqG,SAAS,CAACb,OAAO,CAAC;MAC7ClG,KAAK,EAAEnE,SAAS;MAChBmL,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAACtG,eAAe,CAACsG,gBAAgB,IAAI,IAAI,CAACtG,eAAe,CAACsG,gBAAgB,CAACd,OAAO,CAAC;MAC3Ge,sBAAsB,EAAEpL,SAAS;MACjCiK,GAAG,EAAE,IAAI;MACToB,GAAG,EAAErL,SAAS;MACdsL,UAAU,EAAE,KAAK;MACjBvB,mBAAmB,EAAE3K,UAAU,CAACkG,IAAI;MACpC0E,iBAAiB,EAAE5K,UAAU,CAACkG,IAAI;MAClCkF,KAAK,EAAE;IACX,CAAC,CAAC,CAAC;IACH,IAAIe,OAAO;IACX;IACA,IAAIlC,KAAK,KAAK,CAAC,IAAIC,WAAW,IAAI,IAAI,CAACvF,KAAK,CAAC5G,MAAM,EAAE;MACjD,IAAI,CAACgG,QAAQ,GAAG,IAAI,CAAC+C,cAAc,CAAC,IAAI,CAAC/C,QAAQ,CAACgD,UAAU,CAAC;MAC7D,IAAI,CAAChD,QAAQ,CAACvB,MAAM,CAAC,CAAC,EAAE,CAAC,EAAEiJ,QAAQ,CAAC;MACpCU,OAAO,GAAG,IAAI,CAACxH,KAAK;MACpB,IAAI,CAACA,KAAK,GAAG8G,QAAQ;IACzB,CAAC,MACI;MACD,IAAI,CAAC1H,QAAQ,CAACvB,MAAM,CAACyH,KAAK,EAAEC,WAAW,EAAEuB,QAAQ,CAAC;MAClDU,OAAO,GAAG,IAAI,CAACxH,KAAK,CAACnC,MAAM,CAACyH,KAAK,EAAEC,WAAW,EAAE,GAAGuB,QAAQ,CAAC;IAChE;IACA,MAAMW,KAAK,GAAGpK,QAAQ,CAACjE,MAAM,GAAGmM,WAAW;IAC3C,MAAMmC,WAAW,GAAG,IAAI,CAAC1C,cAAc,CAAC,IAAI,CAACtC,aAAa,EAAE,IAAI,CAACC,gBAAgB,CAAC;IAClF,MAAMgF,iBAAiB,GAAGhM,KAAK,CAACgL,yBAAyB,EAAEc,KAAK,CAAC;IACjE,MAAMG,WAAW,GAAGpM,KAAK,CAACsK,SAAS,CAAC4B,WAAW,EAAEC,iBAAiB,CAAC;IACnE,KAAK,IAAIhO,CAAC,GAAGiO,WAAW,CAACtC,KAAK,EAAE3L,CAAC,GAAGiO,WAAW,CAAChC,GAAG,EAAEjM,CAAC,EAAE,EAAE;MACtD,IAAI,CAACkO,eAAe,CAAC,IAAI,CAAC7H,KAAK,CAACrG,CAAC,CAAC,EAAEA,CAAC,CAAC;IAC1C;IACA,MAAMmO,YAAY,GAAGtM,KAAK,CAACqL,kBAAkB,CAACc,iBAAiB,EAAED,WAAW,CAAC;IAC7E,KAAK,MAAMK,KAAK,IAAID,YAAY,EAAE;MAC9B,KAAK,IAAInO,CAAC,GAAGoO,KAAK,CAACzC,KAAK,EAAE3L,CAAC,GAAGoO,KAAK,CAACnC,GAAG,EAAEjM,CAAC,EAAE,EAAE;QAC1C,IAAI,CAACqO,iBAAiB,CAACrO,CAAC,CAAC;MAC7B;IACJ;IACA,MAAMsO,oBAAoB,GAAGrB,4BAA4B,CAACG,GAAG,CAAC1N,CAAC,IAAIsC,KAAK,CAACtC,CAAC,EAAEoO,KAAK,CAAC,CAAC;IACnF,MAAMS,aAAa,GAAG;MAAE5C,KAAK;MAAEM,GAAG,EAAEN,KAAK,GAAGjI,QAAQ,CAACjE;IAAO,CAAC;IAC7D,MAAM+O,YAAY,GAAG,CAACD,aAAa,EAAE,GAAGD,oBAAoB,CAAC,CAAClB,GAAG,CAAC1N,CAAC,IAAImC,KAAK,CAACsK,SAAS,CAAC4B,WAAW,EAAErO,CAAC,CAAC,CAAC,CAAC+O,OAAO,CAAC,CAAC;IACjH,KAAK,MAAML,KAAK,IAAII,YAAY,EAAE;MAC9B,KAAK,IAAIxO,CAAC,GAAGoO,KAAK,CAACnC,GAAG,GAAG,CAAC,EAAEjM,CAAC,IAAIoO,KAAK,CAACzC,KAAK,EAAE3L,CAAC,EAAE,EAAE;QAC/C,MAAMoE,IAAI,GAAG,IAAI,CAACiC,KAAK,CAACrG,CAAC,CAAC;QAC1B,MAAMwM,IAAI,GAAGJ,aAAa,CAACK,GAAG,CAACrI,IAAI,CAACwE,UAAU,CAAC;QAC/C,MAAM2D,GAAG,GAAGC,IAAI,EAAEkC,GAAG,CAAC,CAAC;QACvB,IAAI,CAACC,eAAe,CAAC3O,CAAC,EAAEuM,GAAG,CAAC;MAChC;IACJ;IACA,KAAK,MAAMC,IAAI,IAAIJ,aAAa,CAACwC,MAAM,CAAC,CAAC,EAAE;MACvC,KAAK,MAAMrC,GAAG,IAAIC,IAAI,EAAE;QACpB,IAAI,CAAC3D,KAAK,CAACgG,OAAO,CAACtC,GAAG,CAAC;MAC3B;IACJ;IACA,IAAI,CAACd,gCAAgC,CAAC,CAAC;IACvC,IAAI,IAAI,CAAC7I,qBAAqB,EAAE;MAC5B,IAAI,CAAC8I,SAAS,CAAC,IAAI,CAAClB,SAAS,EAAE,IAAI,CAACsE,YAAY,CAAC;IACrD;IACA,OAAOjB,OAAO,CAACT,GAAG,CAACpN,CAAC,IAAIA,CAAC,CAAC2M,OAAO,CAAC;EACtC;EACAlB,gCAAgCA,CAAA,EAAG;IAC/B,IAAI,CAACjE,aAAa,GAAG,IAAI,CAAChC,aAAa;IACvC,IAAI,CAACkB,aAAa,CAACC,KAAK,CAACoE,MAAM,GAAG,GAAG,IAAI,CAACvD,aAAa,IAAI;IAC3D,IAAI,CAAC,IAAI,CAACC,iCAAiC,EAAE;MACzC,IAAI,CAACA,iCAAiC,GAAG5G,4BAA4B,CAACJ,SAAS,CAAC,IAAI,CAACyF,OAAO,CAAC,EAAE,MAAM;QACjG,IAAI,CAACP,iBAAiB,CAACa,mBAAmB,CAAC;UAAEyE,YAAY,EAAE,IAAI,CAACA;QAAa,CAAC,CAAC;QAC/E,IAAI,CAAC1E,iBAAiB,CAAC,CAAC;QACxB,IAAI,CAACkB,iCAAiC,GAAG,IAAI;MACjD,CAAC,CAAC;IACN;EACJ;EACAsH,2BAA2BA,CAAA,EAAG;IAC1B,IAAI,CAAC,IAAI,CAAC1L,mBAAmB,EAAE;MAC3B,IAAI,CAAC2D,6BAA6B,CAACC,MAAM,CAAC,CAAC;MAC3C;IACJ;IACA,IAAI,CAACD,6BAA6B,CAACgI,OAAO,CAAC,MAAM,IAAI,CAACzI,iBAAiB,CAAC,CAAC,CAAC;EAC9E;EACAA,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC,IAAI,CAAClD,mBAAmB,EAAE;MAC3B;IACJ;IACA,IAAIyD,WAAW,GAAG,CAAC;IACnB,KAAK,MAAM1C,IAAI,IAAI,IAAI,CAACiC,KAAK,EAAE;MAC3B,IAAI,OAAOjC,IAAI,CAACqC,KAAK,KAAK,WAAW,EAAE;QACnCK,WAAW,GAAGF,IAAI,CAACC,GAAG,CAACC,WAAW,EAAE1C,IAAI,CAACqC,KAAK,CAAC;MACnD;IACJ;IACA,IAAI,CAACK,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACnB,iBAAiB,CAACa,mBAAmB,CAAC;MAAEM,WAAW,EAAEA,WAAW,KAAK,CAAC,GAAG,CAAC,GAAIA,WAAW,GAAG;IAAI,CAAC,CAAC;IACvG,IAAI,CAACqB,wBAAwB,CAAC2D,IAAI,CAAC,IAAI,CAAChF,WAAW,CAAC;EACxD;EACAmI,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC,IAAI,CAACrM,qBAAqB,EAAE;MAC7B;IACJ;IACA,KAAK,MAAMwB,IAAI,IAAI,IAAI,CAACiC,KAAK,EAAE;MAC3BjC,IAAI,CAACsJ,sBAAsB,GAAGpL,SAAS;IAC3C;IACA,IAAI,CAACoJ,SAAS,CAAC,IAAI,CAAC3C,aAAa,EAAE,IAAI,CAACC,gBAAgB,CAAC;EAC7D;EACA,IAAIvJ,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAAC4G,KAAK,CAAC5G,MAAM;EAC5B;EACA,IAAIqP,YAAYA,CAAA,EAAG;IACf,MAAMI,gBAAgB,GAAG,IAAI,CAACvJ,iBAAiB,CAACwJ,mBAAmB,CAAC,CAAC;IACrE,OAAOD,gBAAgB,CAACnE,MAAM;EAClC;EACA,IAAIqE,iBAAiBA,CAAA,EAAG;IACpB,MAAMhB,KAAK,GAAG,IAAI,CAAC/C,cAAc,CAAC,IAAI,CAACtC,aAAa,EAAE,IAAI,CAACC,gBAAgB,CAAC;IAC5E,OAAOoF,KAAK,CAACzC,KAAK;EACtB;EACAgB,OAAOA,CAAC0C,KAAK,EAAE;IACX,OAAO,IAAI,CAAChJ,KAAK,CAACgJ,KAAK,CAAC,CAAC1C,OAAO;EACpC;EACA2C,OAAOA,CAAC3C,OAAO,EAAE;IACb,OAAO,IAAI,CAACtG,KAAK,CAACkJ,SAAS,CAACnL,IAAI,IAAIA,IAAI,CAACuI,OAAO,KAAKA,OAAO,CAAC;EACjE;EACA6C,UAAUA,CAACH,KAAK,EAAE;IACd,MAAM9C,GAAG,GAAG,IAAI,CAAClG,KAAK,CAACgJ,KAAK,CAAC,CAAC9C,GAAG;IACjC,OAAOA,GAAG,IAAIA,GAAG,CAACrG,OAAO;EAC7B;EACAuJ,aAAaA,CAACJ,KAAK,EAAE;IACjB,OAAO,IAAI,CAAChJ,KAAK,CAACgJ,KAAK,CAAC,CAAChL,IAAI;EACjC;EACAqL,UAAUA,CAACL,KAAK,EAAE;IACd,OAAO,IAAI,CAAC5J,QAAQ,CAACkK,UAAU,CAACN,KAAK,CAAC;EAC1C;EACAO,OAAOA,CAACC,QAAQ,EAAE;IACd,OAAO,IAAI,CAACpK,QAAQ,CAACmK,OAAO,CAACC,QAAQ,CAAC;EAC1C;EACAC,UAAUA,CAACD,QAAQ,EAAE;IACjB,OAAO,IAAI,CAACpK,QAAQ,CAACqK,UAAU,CAACD,QAAQ,CAAC;EAC7C;EACAhF,MAAMA,CAACE,MAAM,EAAEtE,KAAK,EAAE;IAClB,MAAMyI,gBAAgB,GAAG;MACrBnE,MAAM,EAAE,OAAOA,MAAM,KAAK,QAAQ,GAAGA,MAAM,GAAGzK,gBAAgB,CAAC,IAAI,CAAC4F,OAAO;IAC/E,CAAC;IACD,IAAI,IAAI,CAACuB,iCAAiC,EAAE;MACxC,IAAI,CAACA,iCAAiC,CAACrE,OAAO,CAAC,CAAC;MAChD,IAAI,CAACqE,iCAAiC,GAAG,IAAI;MAC7CyH,gBAAgB,CAACjE,YAAY,GAAG,IAAI,CAACA,YAAY;IACrD;IACA,IAAI,CAACtF,iBAAiB,CAACa,mBAAmB,CAAC0I,gBAAgB,CAAC;IAC5D,IAAI,OAAOzI,KAAK,KAAK,WAAW,EAAE;MAC9B,IAAI,CAACM,WAAW,GAAGN,KAAK;MACxB,IAAI,IAAI,CAAC7D,qBAAqB,EAAE;QAC5B,IAAI,CAAC8I,SAAS,CAAC,IAAI,CAAClB,SAAS,EAAE,IAAI,CAACsE,YAAY,CAAC;MACrD;IACJ;IACA,IAAI,IAAI,CAACzL,mBAAmB,EAAE;MAC1B,IAAI,CAACsC,iBAAiB,CAACa,mBAAmB,CAAC;QACvCC,KAAK,EAAE,OAAOA,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAGlG,eAAe,CAAC,IAAI,CAAC2F,OAAO;MAC3E,CAAC,CAAC;IACN;EACJ;EACA;EACAqF,MAAMA,CAACQ,mBAAmB,EAAEgE,SAAS,EAAEjB,YAAY,EAAEkB,UAAU,EAAElJ,WAAW,EAAEmJ,gBAAgB,GAAG,KAAK,EAAE;IACpG,MAAMlC,WAAW,GAAG,IAAI,CAAC1C,cAAc,CAAC0E,SAAS,EAAEjB,YAAY,CAAC;IAChE,MAAMoB,cAAc,GAAGrO,KAAK,CAACqL,kBAAkB,CAACa,WAAW,EAAEhC,mBAAmB,CAAC,CAAC0C,OAAO,CAAC,CAAC;IAC3F,MAAM0B,cAAc,GAAGtO,KAAK,CAACqL,kBAAkB,CAACnB,mBAAmB,EAAEgC,WAAW,CAAC;IACjF,IAAIkC,gBAAgB,EAAE;MAClB,MAAMG,cAAc,GAAGvO,KAAK,CAACsK,SAAS,CAACJ,mBAAmB,EAAEgC,WAAW,CAAC;MACxE,KAAK,IAAI/N,CAAC,GAAGoQ,cAAc,CAACzE,KAAK,EAAE3L,CAAC,GAAGoQ,cAAc,CAACnE,GAAG,EAAEjM,CAAC,EAAE,EAAE;QAC5D,IAAI,CAACkO,eAAe,CAAC,IAAI,CAAC7H,KAAK,CAACrG,CAAC,CAAC,EAAEA,CAAC,CAAC;MAC1C;IACJ;IACA,IAAI,CAAC6I,KAAK,CAACwH,QAAQ,CAAC,MAAM;MACtB,KAAK,MAAMjC,KAAK,IAAI+B,cAAc,EAAE;QAChC,KAAK,IAAInQ,CAAC,GAAGoO,KAAK,CAACzC,KAAK,EAAE3L,CAAC,GAAGoO,KAAK,CAACnC,GAAG,EAAEjM,CAAC,EAAE,EAAE;UAC1C,IAAI,CAACqO,iBAAiB,CAACrO,CAAC,CAAC;QAC7B;MACJ;MACA,KAAK,MAAMoO,KAAK,IAAI8B,cAAc,EAAE;QAChC,KAAK,IAAIlQ,CAAC,GAAGoO,KAAK,CAACnC,GAAG,GAAG,CAAC,EAAEjM,CAAC,IAAIoO,KAAK,CAACzC,KAAK,EAAE3L,CAAC,EAAE,EAAE;UAC/C,IAAI,CAAC2O,eAAe,CAAC3O,CAAC,CAAC;QAC3B;MACJ;IACJ,CAAC,CAAC;IACF,IAAIgQ,UAAU,KAAK1N,SAAS,EAAE;MAC1B,IAAI,CAACoE,aAAa,CAACC,KAAK,CAAC2J,IAAI,GAAG,IAAIN,UAAU,IAAI;IACtD;IACA,IAAI,CAACtJ,aAAa,CAACC,KAAK,CAAC4J,GAAG,GAAG,IAAIR,SAAS,IAAI;IAChD,IAAI,IAAI,CAAC1M,mBAAmB,IAAIyD,WAAW,KAAKxE,SAAS,EAAE;MACvD,IAAI,CAACoE,aAAa,CAACC,KAAK,CAACF,KAAK,GAAG,GAAGG,IAAI,CAACC,GAAG,CAACC,WAAW,EAAE,IAAI,CAACC,WAAW,CAAC,IAAI;IACnF;IACA,IAAI,CAACgC,aAAa,GAAGgH,SAAS;IAC9B,IAAI,CAAC/G,gBAAgB,GAAG8F,YAAY;EACxC;EACA;EACAH,eAAeA,CAACU,KAAK,EAAE9C,GAAG,EAAE;IACxB,MAAMnI,IAAI,GAAG,IAAI,CAACiC,KAAK,CAACgJ,KAAK,CAAC;IAC9B,IAAI,CAACjL,IAAI,CAACmI,GAAG,EAAE;MACX,IAAIA,GAAG,EAAE;QACLnI,IAAI,CAACmI,GAAG,GAAGA,GAAG;QACdnI,IAAI,CAAC0I,KAAK,GAAG,IAAI;MACrB,CAAC,MACI;QACD,MAAM0D,MAAM,GAAG,IAAI,CAAC3H,KAAK,CAAC4H,KAAK,CAACrM,IAAI,CAACwE,UAAU,CAAC;QAChDxE,IAAI,CAACmI,GAAG,GAAGiE,MAAM,CAACjE,GAAG;QACrBnI,IAAI,CAAC0I,KAAK,KAAK0D,MAAM,CAACE,yBAAyB;MACnD;IACJ;IACA,MAAMC,IAAI,GAAG,IAAI,CAAC7L,qBAAqB,CAACK,OAAO,CAACf,IAAI,CAACuI,OAAO,CAAC,IAAI,UAAU;IAC3EvI,IAAI,CAACmI,GAAG,CAACrG,OAAO,CAAC0K,YAAY,CAAC,MAAM,EAAED,IAAI,CAAC;IAC3C,MAAME,OAAO,GAAG,IAAI,CAAC/L,qBAAqB,CAACO,SAAS,CAACjB,IAAI,CAACuI,OAAO,CAAC;IAClE,IAAI,OAAOkE,OAAO,KAAK,SAAS,EAAE;MAC9BzM,IAAI,CAACmI,GAAG,CAACrG,OAAO,CAAC0K,YAAY,CAAC,cAAc,EAAEtD,MAAM,CAAC,CAAC,CAACuD,OAAO,CAAC,CAAC;IACpE,CAAC,MACI,IAAIA,OAAO,EAAE;MACd,MAAMlN,MAAM,GAAIkN,OAAO,IAAKzM,IAAI,CAACmI,GAAG,CAACrG,OAAO,CAAC0K,YAAY,CAAC,cAAc,EAAEtD,MAAM,CAAC,CAAC,CAACuD,OAAO,CAAC,CAAC;MAC5FlN,MAAM,CAACkN,OAAO,CAAC7K,KAAK,CAAC;MACrB5B,IAAI,CAACkI,iBAAiB,GAAGuE,OAAO,CAACC,WAAW,CAAC,MAAMnN,MAAM,CAACkN,OAAO,CAAC7K,KAAK,CAAC,CAAC;IAC7E;IACA,IAAI5B,IAAI,CAAC0I,KAAK,IAAI,CAAC1I,IAAI,CAACmI,GAAG,CAACrG,OAAO,CAAC6K,aAAa,EAAE;MAC/C,MAAMC,aAAa,GAAG,IAAI,CAAC3K,KAAK,CAAC4K,EAAE,CAAC5B,KAAK,GAAG,CAAC,CAAC,EAAE9C,GAAG,EAAErG,OAAO,IAAI,IAAI;MACpE,IAAI9B,IAAI,CAACmI,GAAG,CAACrG,OAAO,CAAC6K,aAAa,KAAK,IAAI,CAACrK,aAAa,IAAItC,IAAI,CAACmI,GAAG,CAACrG,OAAO,CAACgL,kBAAkB,KAAKF,aAAa,EAAE;QAChH,IAAI,CAACtK,aAAa,CAACyK,YAAY,CAAC/M,IAAI,CAACmI,GAAG,CAACrG,OAAO,EAAE8K,aAAa,CAAC;MACpE;MACA5M,IAAI,CAAC0I,KAAK,GAAG,KAAK;IACtB;IACA,IAAI,CAACoB,eAAe,CAAC9J,IAAI,EAAEiL,KAAK,CAAC;IACjC,MAAM3G,QAAQ,GAAG,IAAI,CAACtB,SAAS,CAACqF,GAAG,CAACrI,IAAI,CAACwE,UAAU,CAAC;IACpD,IAAI,CAACF,QAAQ,EAAE;MACX,MAAM,IAAIzC,KAAK,CAAC,qCAAqC7B,IAAI,CAACwE,UAAU,EAAE,CAAC;IAC3E;IACAF,QAAQ,EAAE0I,aAAa,CAAChN,IAAI,CAACuI,OAAO,EAAE0C,KAAK,EAAEjL,IAAI,CAACmI,GAAG,CAACK,YAAY,EAAExI,IAAI,CAACC,IAAI,CAAC;IAC9E,MAAMsJ,GAAG,GAAG,IAAI,CAAC9K,GAAG,CAACG,UAAU,CAACoB,IAAI,CAACuI,OAAO,CAAC;IAC7CvI,IAAI,CAACiI,mBAAmB,CAACjJ,OAAO,CAAC,CAAC;IAClCgB,IAAI,CAACmI,GAAG,CAACrG,OAAO,CAACmL,SAAS,GAAG,CAAC,CAAC1D,GAAG;IAClC,IAAIA,GAAG,EAAE;MACLvJ,IAAI,CAACiI,mBAAmB,GAAGjM,qBAAqB,CAACgE,IAAI,CAACmI,GAAG,CAACrG,OAAO,EAAE,WAAW,EAAEoC,KAAK,IAAI,IAAI,CAACrF,WAAW,CAACmB,IAAI,CAACuI,OAAO,EAAEgB,GAAG,EAAErF,KAAK,CAAC,CAAC;IACxI;IACA,IAAI,IAAI,CAACjF,mBAAmB,EAAE;MAC1B,IAAI,CAACiD,gBAAgB,CAAClC,IAAI,CAAC;MAC3B,IAAI,CAAC2K,2BAA2B,CAAC,CAAC;IACtC;EACJ;EACAzI,gBAAgBA,CAAClC,IAAI,EAAE;IACnB,IAAI,CAACA,IAAI,CAACmI,GAAG,IAAI,CAACnI,IAAI,CAACmI,GAAG,CAACrG,OAAO,EAAE;MAChC;IACJ;IACA9B,IAAI,CAACmI,GAAG,CAACrG,OAAO,CAACS,KAAK,CAACF,KAAK,GAAG,aAAa;IAC5CrC,IAAI,CAACqC,KAAK,GAAGlG,eAAe,CAAC6D,IAAI,CAACmI,GAAG,CAACrG,OAAO,CAAC;IAC9C,MAAMS,KAAK,GAAGlG,SAAS,CAAC2D,IAAI,CAACmI,GAAG,CAACrG,OAAO,CAAC,CAACoL,gBAAgB,CAAClN,IAAI,CAACmI,GAAG,CAACrG,OAAO,CAAC;IAC5E,IAAIS,KAAK,CAAC4K,WAAW,EAAE;MACnBnN,IAAI,CAACqC,KAAK,IAAI+K,UAAU,CAAC7K,KAAK,CAAC4K,WAAW,CAAC;IAC/C;IACA,IAAI5K,KAAK,CAAC8K,YAAY,EAAE;MACpBrN,IAAI,CAACqC,KAAK,IAAI+K,UAAU,CAAC7K,KAAK,CAAC8K,YAAY,CAAC;IAChD;IACArN,IAAI,CAACmI,GAAG,CAACrG,OAAO,CAACS,KAAK,CAACF,KAAK,GAAG,EAAE;EACrC;EACAyH,eAAeA,CAAC9J,IAAI,EAAEiL,KAAK,EAAE;IACzBjL,IAAI,CAACmI,GAAG,CAACrG,OAAO,CAACS,KAAK,CAAC4J,GAAG,GAAG,GAAG,IAAI,CAACb,UAAU,CAACL,KAAK,CAAC,IAAI;IAC1D,IAAI,IAAI,CAAC1M,YAAY,EAAE;MACnByB,IAAI,CAACmI,GAAG,CAACrG,OAAO,CAACS,KAAK,CAACoE,MAAM,GAAG,GAAG3G,IAAI,CAACC,IAAI,IAAI;IACpD;IACA,IAAI,IAAI,CAAC3B,gBAAgB,EAAE;MACvB0B,IAAI,CAACmI,GAAG,CAACrG,OAAO,CAACS,KAAK,CAAC+K,UAAU,GAAG,GAAGtN,IAAI,CAACC,IAAI,IAAI;IACxD;IACAD,IAAI,CAACmI,GAAG,CAACrG,OAAO,CAAC0K,YAAY,CAAC,YAAY,EAAE,GAAGvB,KAAK,EAAE,CAAC;IACvDjL,IAAI,CAACmI,GAAG,CAACrG,OAAO,CAAC0K,YAAY,CAAC,mBAAmB,EAAEvB,KAAK,KAAK,IAAI,CAAC5P,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,OAAO,CAAC;IAChG2E,IAAI,CAACmI,GAAG,CAACrG,OAAO,CAAC0K,YAAY,CAAC,aAAa,EAAEvB,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM,GAAG,KAAK,CAAC;IAC9EjL,IAAI,CAACmI,GAAG,CAACrG,OAAO,CAAC0K,YAAY,CAAC,cAAc,EAAEtD,MAAM,CAAC,IAAI,CAACxI,qBAAqB,CAACC,UAAU,CAACX,IAAI,CAACuI,OAAO,EAAE0C,KAAK,EAAE,IAAI,CAAC5P,MAAM,CAAC,CAAC,CAAC;IAC9H2E,IAAI,CAACmI,GAAG,CAACrG,OAAO,CAAC0K,YAAY,CAAC,eAAe,EAAEtD,MAAM,CAAC,IAAI,CAACxI,qBAAqB,CAACI,WAAW,CAACd,IAAI,CAACuI,OAAO,EAAE0C,KAAK,CAAC,CAAC,CAAC;IACnHjL,IAAI,CAACmI,GAAG,CAACrG,OAAO,CAAC0K,YAAY,CAAC,IAAI,EAAE,IAAI,CAACe,eAAe,CAACtC,KAAK,CAAC,CAAC;IAChEjL,IAAI,CAACmI,GAAG,CAACrG,OAAO,CAACC,SAAS,CAACC,MAAM,CAAC,aAAa,EAAEhC,IAAI,CAACwJ,UAAU,CAAC;EACrE;EACAS,iBAAiBA,CAACgB,KAAK,EAAE;IACrB,MAAMjL,IAAI,GAAG,IAAI,CAACiC,KAAK,CAACgJ,KAAK,CAAC;IAC9BjL,IAAI,CAACiI,mBAAmB,CAACjJ,OAAO,CAAC,CAAC;IAClCgB,IAAI,CAACkI,iBAAiB,CAAClJ,OAAO,CAAC,CAAC;IAChC,IAAIgB,IAAI,CAACmI,GAAG,EAAE;MACV,MAAM7D,QAAQ,GAAG,IAAI,CAACtB,SAAS,CAACqF,GAAG,CAACrI,IAAI,CAACwE,UAAU,CAAC;MACpD,IAAIF,QAAQ,IAAIA,QAAQ,CAACgE,cAAc,EAAE;QACrChE,QAAQ,CAACgE,cAAc,CAACtI,IAAI,CAACuI,OAAO,EAAE0C,KAAK,EAAEjL,IAAI,CAACmI,GAAG,CAACK,YAAY,EAAExI,IAAI,CAACC,IAAI,CAAC;MAClF;MACA,IAAI,CAACwE,KAAK,CAACgG,OAAO,CAACzK,IAAI,CAACmI,GAAG,CAAC;MAC5BnI,IAAI,CAACmI,GAAG,GAAG,IAAI;IACnB;IACA,IAAI,IAAI,CAAClJ,mBAAmB,EAAE;MAC1B,IAAI,CAAC0L,2BAA2B,CAAC,CAAC;IACtC;EACJ;EACA6C,YAAYA,CAAA,EAAG;IACX,MAAMC,cAAc,GAAG,IAAI,CAAClM,iBAAiB,CAACmM,iBAAiB,CAAC,CAAC;IACjE,OAAOD,cAAc,CAACrH,SAAS;EACnC;EACAgB,YAAYA,CAAChB,SAAS,EAAEuH,cAAc,EAAE;IACpC,IAAI,IAAI,CAACtK,iCAAiC,EAAE;MACxC,IAAI,CAACA,iCAAiC,CAACrE,OAAO,CAAC,CAAC;MAChD,IAAI,CAACqE,iCAAiC,GAAG,IAAI;MAC7C,IAAI,CAAC9B,iBAAiB,CAACa,mBAAmB,CAAC;QAAEyE,YAAY,EAAE,IAAI,CAACA;MAAa,CAAC,CAAC;IACnF;IACA,IAAI,CAACtF,iBAAiB,CAACqM,iBAAiB,CAAC;MAAExH,SAAS;MAAEuH;IAAe,CAAC,CAAC;EAC3E;EACA,IAAIvH,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACoH,YAAY,CAAC,CAAC;EAC9B;EACA,IAAIpH,SAASA,CAACA,SAAS,EAAE;IACrB,IAAI,CAACgB,YAAY,CAAChB,SAAS,CAAC;EAChC;EACA,IAAIS,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACzD,aAAa,IAAI,IAAI,CAACnE,mBAAmB,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,CAACiG,aAAa;EACxF;EACA;EACA,IAAI2I,YAAYA,CAAA,EAAG;IAAE,OAAOxQ,KAAK,CAAC2L,GAAG,CAAC,IAAI,CAACnF,WAAW,CAACa,GAAG,CAAC,IAAIhI,UAAU,CAAC,IAAI,CAACoF,OAAO,EAAE,OAAO,CAAC,CAAC,CAACoC,KAAK,EAAEvF,CAAC,IAAI,IAAI,CAACmP,YAAY,CAACnP,CAAC,CAAC,EAAE,IAAI,CAACkF,WAAW,CAAC;EAAE;EACvJ,IAAIkK,eAAeA,CAAA,EAAG;IAAE,OAAO1Q,KAAK,CAAC2L,GAAG,CAAC,IAAI,CAACnF,WAAW,CAACa,GAAG,CAAC,IAAIhI,UAAU,CAAC,IAAI,CAACoF,OAAO,EAAE,UAAU,CAAC,CAAC,CAACoC,KAAK,EAAEvF,CAAC,IAAI,IAAI,CAACmP,YAAY,CAACnP,CAAC,CAAC,EAAE,IAAI,CAACkF,WAAW,CAAC;EAAE;EAC7J,IAAImK,kBAAkBA,CAAA,EAAG;IAAE,OAAO3Q,KAAK,CAAC4Q,MAAM,CAAC5Q,KAAK,CAAC2L,GAAG,CAAC,IAAI,CAACnF,WAAW,CAACa,GAAG,CAAC,IAAIhI,UAAU,CAAC,IAAI,CAACoF,OAAO,EAAE,UAAU,CAAC,CAAC,CAACoC,KAAK,EAAEvF,CAAC,IAAI,IAAI,CAACmP,YAAY,CAACnP,CAAC,CAAC,EAAE,IAAI,CAACkF,WAAW,CAAC,EAAElF,CAAC,IAAIA,CAAC,CAACuP,YAAY,CAACC,MAAM,KAAK,CAAC,EAAE,IAAI,CAACtK,WAAW,CAAC;EAAE;EAClO,IAAIuK,WAAWA,CAAA,EAAG;IAAE,OAAO/Q,KAAK,CAAC2L,GAAG,CAAC,IAAI,CAACnF,WAAW,CAACa,GAAG,CAAC,IAAIhI,UAAU,CAAC,IAAI,CAACoF,OAAO,EAAE,WAAW,CAAC,CAAC,CAACoC,KAAK,EAAEvF,CAAC,IAAI,IAAI,CAACmP,YAAY,CAACnP,CAAC,CAAC,EAAE,IAAI,CAACkF,WAAW,CAAC;EAAE;EAC1J,IAAIwK,WAAWA,CAAA,EAAG;IAAE,OAAOhR,KAAK,CAAC2L,GAAG,CAAC,IAAI,CAACnF,WAAW,CAACa,GAAG,CAAC,IAAIhI,UAAU,CAAC,IAAI,CAACoF,OAAO,EAAE,WAAW,CAAC,CAAC,CAACoC,KAAK,EAAEvF,CAAC,IAAI,IAAI,CAACmP,YAAY,CAACnP,CAAC,CAAC,EAAE,IAAI,CAACkF,WAAW,CAAC;EAAE;EAC1J,IAAIyK,UAAUA,CAAA,EAAG;IAAE,OAAOjR,KAAK,CAAC2L,GAAG,CAAC,IAAI,CAACnF,WAAW,CAACa,GAAG,CAAC,IAAIhI,UAAU,CAAC,IAAI,CAACoF,OAAO,EAAE,UAAU,CAAC,CAAC,CAACoC,KAAK,EAAEvF,CAAC,IAAI,IAAI,CAACmP,YAAY,CAACnP,CAAC,CAAC,EAAE,IAAI,CAACkF,WAAW,CAAC;EAAE;EACxJ,IAAI0K,aAAaA,CAAA,EAAG;IAAE,OAAOlR,KAAK,CAACmR,GAAG,CAACnR,KAAK,CAAC2L,GAAG,CAAC,IAAI,CAACnF,WAAW,CAACa,GAAG,CAAC,IAAIhI,UAAU,CAAC,IAAI,CAACoF,OAAO,EAAE,aAAa,CAAC,CAAC,CAACoC,KAAK,EAAEvF,CAAC,IAAI,IAAI,CAACmP,YAAY,CAACnP,CAAC,CAAC,EAAE,IAAI,CAACkF,WAAW,CAAC,EAAExG,KAAK,CAAC2L,GAAG,CAAC,IAAI,CAACnF,WAAW,CAACa,GAAG,CAAC,IAAIhI,UAAU,CAAC,IAAI,CAACoF,OAAO,EAAElF,cAAc,CAAC6R,WAAW,CAAC,CAAC,CAACvK,KAAK,EAAEvF,CAAC,IAAI,IAAI,CAAC+P,cAAc,CAAC/P,CAAC,CAAC,EAAE,IAAI,CAACkF,WAAW,CAAC,CAAC;EAAE;EACzT,IAAI8K,YAAYA,CAAA,EAAG;IAAE,OAAOtR,KAAK,CAAC2L,GAAG,CAAC,IAAI,CAACnF,WAAW,CAACa,GAAG,CAAC,IAAIhI,UAAU,CAAC,IAAI,CAACoF,OAAO,EAAE,YAAY,CAAC,CAAC,CAACoC,KAAK,EAAEvF,CAAC,IAAI,IAAI,CAACiQ,YAAY,CAACjQ,CAAC,CAAC,EAAE,IAAI,CAACkF,WAAW,CAAC;EAAE;EAC5J,IAAIgL,KAAKA,CAAA,EAAG;IAAE,OAAOxR,KAAK,CAAC2L,GAAG,CAAC,IAAI,CAACnF,WAAW,CAACa,GAAG,CAAC,IAAIhI,UAAU,CAAC,IAAI,CAAC4F,aAAa,EAAE1F,cAAc,CAACkS,GAAG,CAAC,CAAC,CAAC5K,KAAK,EAAEvF,CAAC,IAAI,IAAI,CAAC+P,cAAc,CAAC/P,CAAC,CAAC,EAAE,IAAI,CAACkF,WAAW,CAAC;EAAE;EACnKiK,YAAYA,CAACI,YAAY,EAAE;IACvB,MAAMjD,KAAK,GAAG,IAAI,CAAC8D,2BAA2B,CAACb,YAAY,CAAClT,MAAM,IAAI,IAAI,CAAC;IAC3E,MAAMgF,IAAI,GAAG,OAAOiL,KAAK,KAAK,WAAW,GAAG/M,SAAS,GAAG,IAAI,CAAC+D,KAAK,CAACgJ,KAAK,CAAC;IACzE,MAAM1C,OAAO,GAAGvI,IAAI,IAAIA,IAAI,CAACuI,OAAO;IACpC,OAAO;MAAE2F,YAAY;MAAEjD,KAAK;MAAE1C;IAAQ,CAAC;EAC3C;EACAqG,YAAYA,CAACV,YAAY,EAAE;IACvB,MAAMjD,KAAK,GAAG,IAAI,CAAC8D,2BAA2B,CAACb,YAAY,CAAClT,MAAM,IAAI,IAAI,CAAC;IAC3E,MAAMgF,IAAI,GAAG,OAAOiL,KAAK,KAAK,WAAW,GAAG/M,SAAS,GAAG,IAAI,CAAC+D,KAAK,CAACgJ,KAAK,CAAC;IACzE,MAAM1C,OAAO,GAAGvI,IAAI,IAAIA,IAAI,CAACuI,OAAO;IACpC,OAAO;MAAE2F,YAAY;MAAEjD,KAAK;MAAE1C;IAAQ,CAAC;EAC3C;EACAmG,cAAcA,CAACR,YAAY,EAAE;IACzB,MAAMjD,KAAK,GAAG,IAAI,CAAC8D,2BAA2B,CAACb,YAAY,CAACc,aAAa,IAAI,IAAI,CAAC;IAClF,MAAMhP,IAAI,GAAG,OAAOiL,KAAK,KAAK,WAAW,GAAG/M,SAAS,GAAG,IAAI,CAAC+D,KAAK,CAACgJ,KAAK,CAAC;IACzE,MAAM1C,OAAO,GAAGvI,IAAI,IAAIA,IAAI,CAACuI,OAAO;IACpC,OAAO;MAAE2F,YAAY;MAAEjD,KAAK;MAAE1C;IAAQ,CAAC;EAC3C;EACAlC,WAAWA,CAAC6H,YAAY,EAAE;IACtB,MAAMjD,KAAK,GAAG,IAAI,CAAC8D,2BAA2B,CAACb,YAAY,CAAClT,MAAM,IAAI,IAAI,CAAC;IAC3E,MAAMgF,IAAI,GAAG,OAAOiL,KAAK,KAAK,WAAW,GAAG/M,SAAS,GAAG,IAAI,CAAC+D,KAAK,CAACgJ,KAAK,CAAC;IACzE,MAAM1C,OAAO,GAAGvI,IAAI,IAAIA,IAAI,CAACuI,OAAO;IACpC,MAAM0G,MAAM,GAAG,IAAI,CAACC,eAAe,CAAChB,YAAY,EAAEjD,KAAK,CAAC;IACxD,OAAO;MAAEiD,YAAY;MAAEjD,KAAK;MAAE1C,OAAO;MAAE0G;IAAO,CAAC;EACnD;EACAzN,QAAQA,CAAC7C,CAAC,EAAE;IACR,IAAI;MACA,MAAMgJ,mBAAmB,GAAG,IAAI,CAACV,cAAc,CAAC,IAAI,CAACtC,aAAa,EAAE,IAAI,CAACC,gBAAgB,CAAC;MAC1F,IAAI,CAACuC,MAAM,CAACQ,mBAAmB,EAAEhJ,CAAC,CAACyH,SAAS,EAAEzH,CAAC,CAACgI,MAAM,EAAEhI,CAAC,CAACwQ,UAAU,EAAExQ,CAAC,CAAC+D,WAAW,CAAC;MACpF,IAAI,IAAI,CAAClE,qBAAqB,EAAE;QAC5B,IAAI,CAAC8I,SAAS,CAAC3I,CAAC,CAACyH,SAAS,EAAEzH,CAAC,CAACgI,MAAM,EAAEhI,CAAC,CAACyQ,iBAAiB,CAAC;MAC9D;IACJ,CAAC,CACD,OAAOC,GAAG,EAAE;MACRC,OAAO,CAACC,KAAK,CAAC,uBAAuB,EAAE5Q,CAAC,CAAC;MACzC,MAAM0Q,GAAG;IACb;EACJ;EACAlJ,aAAaA,CAACjC,KAAK,EAAE;IACjBA,KAAK,CAACsL,cAAc,CAAC,CAAC;IACtBtL,KAAK,CAACuL,eAAe,CAAC,CAAC;IACvB,IAAI,CAACrJ,SAAS,IAAIlC,KAAK,CAACwL,YAAY;EACxC;EACA;EACA7Q,WAAWA,CAAC0J,OAAO,EAAEgB,GAAG,EAAErF,KAAK,EAAE;IAC7B,IAAI,CAACA,KAAK,CAACrE,YAAY,EAAE;MACrB;IACJ;IACA,MAAMP,QAAQ,GAAG,IAAI,CAACb,GAAG,CAACC,eAAe,CAAC6J,OAAO,CAAC;IAClDrE,KAAK,CAACrE,YAAY,CAAC8P,aAAa,GAAG,UAAU;IAC7CzL,KAAK,CAACrE,YAAY,CAAC+P,OAAO,CAAC9T,aAAa,CAAC+T,IAAI,EAAEtG,GAAG,CAAC;IACnD,IAAIrF,KAAK,CAACrE,YAAY,CAACiQ,YAAY,EAAE;MACjC,IAAIC,KAAK;MACT,IAAI,IAAI,CAACtR,GAAG,CAACuR,YAAY,EAAE;QACvBD,KAAK,GAAG,IAAI,CAACtR,GAAG,CAACuR,YAAY,CAAC1Q,QAAQ,EAAE4E,KAAK,CAAC;MAClD;MACA,IAAI,OAAO6L,KAAK,KAAK,WAAW,EAAE;QAC9BA,KAAK,GAAG7G,MAAM,CAAC5J,QAAQ,CAACjE,MAAM,CAAC;MACnC;MACA,MAAM4U,SAAS,GAAGlU,CAAC,CAAC,oBAAoB,CAAC;MACzCkU,SAAS,CAACC,WAAW,GAAGH,KAAK;MAC7B,MAAMI,qBAAqB,GAAIxR,CAAC,IAAK;QACjC,OAAOA,CAAC,IAAI,CAACA,CAAC,CAACoD,SAAS,CAACqO,QAAQ,CAAC,kBAAkB,CAAC,EAAE;UACnDzR,CAAC,GAAGA,CAAC,CAACgO,aAAa;QACvB;QACA,OAAOhO,CAAC,IAAI,IAAI,CAACmD,OAAO,CAACuO,aAAa;MAC1C,CAAC;MACD,MAAMvN,SAAS,GAAGqN,qBAAqB,CAAC,IAAI,CAACrO,OAAO,CAAC;MACrDgB,SAAS,CAACmD,WAAW,CAACgK,SAAS,CAAC;MAChC/L,KAAK,CAACrE,YAAY,CAACiQ,YAAY,CAACG,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC;MACpDK,UAAU,CAAC,MAAML,SAAS,CAACM,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;IAC3C;IACA,IAAI,CAACzO,OAAO,CAACC,SAAS,CAAC2C,GAAG,CAAC,UAAU,CAAC;IACtC,IAAI,CAAC8L,eAAe,GAAG,IAAIpR,uBAAuB,CAACE,QAAQ,CAAC;IAC5DtB,SAAS,CAACC,sBAAsB,GAAG,IAAIwB,+BAA+B,CAACH,QAAQ,CAAC;IAChF,IAAI,CAACb,GAAG,CAACI,WAAW,GAAG,IAAI,CAAC2R,eAAe,EAAEtM,KAAK,CAAC;EACvD;EACApF,UAAUA,CAACoF,KAAK,EAAE;IACdA,KAAK,CAACgK,YAAY,CAACsB,cAAc,CAAC,CAAC,CAAC,CAAC;IACrC,IAAI,CAAC5L,kBAAkB,CAAC5E,OAAO,CAAC,CAAC;IACjC,IAAIhB,SAAS,CAACC,sBAAsB,IAAID,SAAS,CAACC,sBAAsB,CAACuB,OAAO,CAAC,CAAC,KAAK,WAAW,EAAE;MAChG,OAAO,KAAK;IAChB;IACA,IAAI,CAACiR,kCAAkC,CAACvM,KAAK,CAACgK,YAAY,CAAC;IAC3D,IAAI,CAAChK,KAAK,CAACgK,YAAY,CAACrO,YAAY,EAAE;MAClC,OAAO,KAAK;IAChB;IACA;IACA,IAAI,CAAC,IAAI,CAAC2Q,eAAe,EAAE;MACvB,IAAIxS,SAAS,CAACC,sBAAsB,EAAE;QAClC;QACA,IAAI,CAACuS,eAAe,GAAGxS,SAAS,CAACC,sBAAsB;MAC3D,CAAC,MACI;QACD;QACA,IAAI,CAACiG,KAAK,CAACgK,YAAY,CAACrO,YAAY,CAACF,KAAK,EAAE;UACxC,OAAO,KAAK;QAChB;QACA,IAAI,CAAC6Q,eAAe,GAAG,IAAI9Q,qBAAqB,CAAC,CAAC;MACtD;IACJ;IACA,MAAM0M,MAAM,GAAG,IAAI,CAAC3N,GAAG,CAACK,UAAU,CAAC,IAAI,CAAC0R,eAAe,EAAEtM,KAAK,CAACqE,OAAO,EAAErE,KAAK,CAAC+G,KAAK,EAAE/G,KAAK,CAAC+K,MAAM,EAAE/K,KAAK,CAACgK,YAAY,CAAC;IACtH,IAAI,CAACxK,OAAO,GAAG,OAAO0I,MAAM,KAAK,SAAS,GAAGA,MAAM,GAAGA,MAAM,CAACsE,MAAM;IACnE,IAAI,CAAC,IAAI,CAAChN,OAAO,EAAE;MACf,IAAI,CAACiN,mBAAmB,GAAGzS,SAAS;MACpC,IAAI,CAACyF,6BAA6B,CAAC3E,OAAO,CAAC,CAAC;MAC5C,OAAO,KAAK;IAChB;IACAkF,KAAK,CAACgK,YAAY,CAACrO,YAAY,CAAC+Q,UAAU,GAAI,OAAOxE,MAAM,KAAK,SAAS,IAAIA,MAAM,CAACyE,MAAM,EAAE3Q,IAAI,KAAK,CAAC,CAAC,oCAAqC,MAAM,GAAG,MAAM;IAC3J,IAAI4Q,QAAQ;IACZ,IAAI,OAAO1E,MAAM,KAAK,SAAS,IAAIA,MAAM,CAAC0E,QAAQ,EAAE;MAChDA,QAAQ,GAAG1E,MAAM,CAAC0E,QAAQ;IAC9B,CAAC,MACI;MACD,IAAI,OAAO5M,KAAK,CAAC+G,KAAK,KAAK,WAAW,EAAE;QACpC6F,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,MACI;QACDA,QAAQ,GAAG,CAAC5M,KAAK,CAAC+G,KAAK,CAAC;MAC5B;IACJ;IACA;IACA6F,QAAQ,GAAG/T,QAAQ,CAAC+T,QAAQ,CAAC,CAAC7C,MAAM,CAACrS,CAAC,IAAIA,CAAC,IAAI,CAAC,CAAC,IAAIA,CAAC,GAAG,IAAI,CAACP,MAAM,CAAC,CAAC0V,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC;IAC3FH,QAAQ,GAAGA,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGA,QAAQ;IAC/C,IAAII,sBAAsB,GAAG,OAAO9E,MAAM,KAAK,SAAS,IAAIA,MAAM,CAACyE,MAAM,IAAIzE,MAAM,CAACyE,MAAM,CAACpF,QAAQ,GAAGW,MAAM,CAACyE,MAAM,CAACpF,QAAQ,GAAG,aAAa,CAAC;IAC7I,IAAIrL,kBAAkB,CAAC,IAAI,CAACuQ,mBAAmB,EAAEG,QAAQ,CAAC,IAAI,IAAI,CAACK,2BAA2B,KAAKD,sBAAsB,EAAE;MACvH,OAAO,IAAI;IACf;IACA,IAAI,CAACP,mBAAmB,GAAGG,QAAQ;IACnC,IAAI,CAACK,2BAA2B,GAAGD,sBAAsB;IACzD,IAAI,CAACvN,6BAA6B,CAAC3E,OAAO,CAAC,CAAC;IAC5C,IAAI8R,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;MAAE;MACtB,IAAI,CAAChP,OAAO,CAACC,SAAS,CAAC2C,GAAG,CAACwM,sBAAsB,CAAC;MAClD,IAAI,CAAC5O,aAAa,CAACP,SAAS,CAAC2C,GAAG,CAACwM,sBAAsB,CAAC;MACxD,IAAI,CAACvN,6BAA6B,GAAGnG,YAAY,CAAC,MAAM;QACpD,IAAI,CAACsE,OAAO,CAACC,SAAS,CAACwO,MAAM,CAACW,sBAAsB,CAAC;QACrD,IAAI,CAAC5O,aAAa,CAACP,SAAS,CAACwO,MAAM,CAACW,sBAAsB,CAAC;MAC/D,CAAC,CAAC;IACN,CAAC,MACI;MACD,IAAIJ,QAAQ,CAACzV,MAAM,GAAG,CAAC,IAAI6V,sBAAsB,KAAK,aAAa,CAAC,uCAAuC;QACvG,MAAM,IAAIrP,KAAK,CAAC,qEAAqE,CAAC;MAC1F;MACA;MACA;MACA,IAAIqP,sBAAsB,KAAK,mBAAmB,CAAC,wCAAwC;QACvF,IAAIJ,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,CAACzV,MAAM,GAAG,CAAC,EAAE;UAC/ByV,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC;UAChBI,sBAAsB,GAAG,oBAAoB,CAAC;QAClD;MACJ;MACA,KAAK,MAAMjG,KAAK,IAAI6F,QAAQ,EAAE;QAC1B,MAAM9Q,IAAI,GAAG,IAAI,CAACiC,KAAK,CAACgJ,KAAK,CAAC;QAC9BjL,IAAI,CAACwJ,UAAU,GAAG,IAAI;QACtBxJ,IAAI,CAACmI,GAAG,EAAErG,OAAO,CAACC,SAAS,CAAC2C,GAAG,CAACwM,sBAAsB,CAAC;MAC3D;MACA,IAAI,CAACvN,6BAA6B,GAAGnG,YAAY,CAAC,MAAM;QACpD,KAAK,MAAMyN,KAAK,IAAI6F,QAAQ,EAAE;UAC1B,MAAM9Q,IAAI,GAAG,IAAI,CAACiC,KAAK,CAACgJ,KAAK,CAAC;UAC9BjL,IAAI,CAACwJ,UAAU,GAAG,KAAK;UACvBxJ,IAAI,CAACmI,GAAG,EAAErG,OAAO,CAACC,SAAS,CAACwO,MAAM,CAACW,sBAAsB,CAAC;QAC9D;MACJ,CAAC,CAAC;IACN;IACA,OAAO,IAAI;EACf;EACA3K,WAAWA,CAACrC,KAAK,EAAE;IACf,IAAI,CAACN,kBAAkB,CAAC5E,OAAO,CAAC,CAAC;IACjC,IAAI,CAAC4E,kBAAkB,GAAG1G,iBAAiB,CAAC,MAAM,IAAI,CAACkU,qBAAqB,CAAC,CAAC,EAAE,GAAG,EAAE,IAAI,CAACvN,WAAW,CAAC;IACtG,IAAI,IAAI,CAAC2M,eAAe,EAAE;MACtB,IAAI,CAAC/R,GAAG,CAAC8H,WAAW,GAAG,IAAI,CAACiK,eAAe,EAAEtM,KAAK,CAACqE,OAAO,EAAErE,KAAK,CAAC+G,KAAK,EAAE/G,KAAK,CAACgK,YAAY,CAAC;IAChG;EACJ;EACA5H,MAAMA,CAACpC,KAAK,EAAE;IACV,IAAI,CAAC,IAAI,CAACR,OAAO,EAAE;MACf;IACJ;IACA,MAAM2N,QAAQ,GAAG,IAAI,CAACb,eAAe;IACrC,IAAI,CAACc,qCAAqC,CAAC,CAAC;IAC5C,IAAI,CAACF,qBAAqB,CAAC,CAAC;IAC5B,IAAI,CAACtP,OAAO,CAACC,SAAS,CAACwO,MAAM,CAAC,UAAU,CAAC;IACzC,IAAI,CAACC,eAAe,GAAGtS,SAAS;IAChCF,SAAS,CAACC,sBAAsB,GAAGC,SAAS;IAC5C,IAAI,CAACmT,QAAQ,IAAI,CAACnN,KAAK,CAACgK,YAAY,CAACrO,YAAY,EAAE;MAC/C;IACJ;IACAqE,KAAK,CAACgK,YAAY,CAACsB,cAAc,CAAC,CAAC;IACnC6B,QAAQ,CAAC9R,MAAM,CAAC2E,KAAK,CAACgK,YAAY,CAACrO,YAAY,CAAC;IAChD,IAAI,CAACpB,GAAG,CAACM,IAAI,CAACsS,QAAQ,EAAEnN,KAAK,CAACqE,OAAO,EAAErE,KAAK,CAAC+G,KAAK,EAAE/G,KAAK,CAAC+K,MAAM,EAAE/K,KAAK,CAACgK,YAAY,CAAC;EACzF;EACA1H,SAASA,CAACtC,KAAK,EAAE;IACb,IAAI,CAACR,OAAO,GAAG,KAAK;IACpB,IAAI,CAAC4N,qCAAqC,CAAC,CAAC;IAC5C,IAAI,CAACF,qBAAqB,CAAC,CAAC;IAC5B,IAAI,CAACtP,OAAO,CAACC,SAAS,CAACwO,MAAM,CAAC,UAAU,CAAC;IACzC,IAAI,CAACC,eAAe,GAAGtS,SAAS;IAChCF,SAAS,CAACC,sBAAsB,GAAGC,SAAS;IAC5C,IAAI,CAACO,GAAG,CAAC+H,SAAS,GAAGtC,KAAK,CAAC;EAC/B;EACAkN,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAACT,mBAAmB,GAAGzS,SAAS;IACpC,IAAI,CAACiT,2BAA2B,GAAGjT,SAAS;IAC5C,IAAI,CAACyF,6BAA6B,CAAC3E,OAAO,CAAC,CAAC;IAC5C,IAAI,CAAC2E,6BAA6B,GAAGrG,UAAU,CAACkG,IAAI;EACxD;EACA;EACAiN,kCAAkCA,CAACvM,KAAK,EAAE;IACtC,IAAI,CAAC,IAAI,CAACqN,2BAA2B,EAAE;MACnC,MAAMC,OAAO,GAAGpV,gBAAgB,CAAC,IAAI,CAAC0F,OAAO,CAAC,CAACqK,GAAG;MAClD,IAAI,CAACoF,2BAA2B,GAAGtV,OAAO,CAACI,SAAS,CAAC,IAAI,CAACyF,OAAO,CAAC,EAAE,IAAI,CAAC2P,2BAA2B,CAAC7Q,IAAI,CAAC,IAAI,EAAE4Q,OAAO,CAAC,CAAC;IAC7H;IACA,IAAI,CAACjO,+BAA+B,CAACvE,OAAO,CAAC,CAAC;IAC9C,IAAI,CAACuE,+BAA+B,GAAGrG,iBAAiB,CAAC,MAAM;MAC3D,IAAI,IAAI,CAACqU,2BAA2B,EAAE;QAClC,IAAI,CAACA,2BAA2B,CAACvS,OAAO,CAAC,CAAC;QAC1C,IAAI,CAACuS,2BAA2B,GAAGrT,SAAS;MAChD;IACJ,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC2F,WAAW,CAAC;IAC1B,IAAI,CAACJ,cAAc,GAAGS,KAAK,CAACwN,KAAK;EACrC;EACAD,2BAA2BA,CAACD,OAAO,EAAE;IACjC,IAAI,IAAI,CAAC/N,cAAc,KAAKvF,SAAS,EAAE;MACnC;IACJ;IACA,MAAMyT,IAAI,GAAG,IAAI,CAAClO,cAAc,GAAG+N,OAAO;IAC1C,MAAMI,UAAU,GAAG,IAAI,CAAClH,YAAY,GAAG,EAAE;IACzC,IAAIiH,IAAI,GAAG,EAAE,EAAE;MACX,IAAI,CAACvL,SAAS,IAAI5D,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,EAAED,IAAI,CAACqP,KAAK,CAAC,GAAG,IAAIF,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC;IAClE,CAAC,MACI,IAAIA,IAAI,GAAGC,UAAU,EAAE;MACxB,IAAI,CAACxL,SAAS,IAAI5D,IAAI,CAACsP,GAAG,CAAC,EAAE,EAAEtP,IAAI,CAACqP,KAAK,CAAC,GAAG,IAAIF,IAAI,GAAGC,UAAU,CAAC,CAAC,CAAC;IACzE;EACJ;EACAN,qCAAqCA,CAAA,EAAG;IACpC,IAAI,CAAC/N,+BAA+B,CAACvE,OAAO,CAAC,CAAC;IAC9C,IAAI,IAAI,CAACuS,2BAA2B,EAAE;MAClC,IAAI,CAACA,2BAA2B,CAACvS,OAAO,CAAC,CAAC;MAC1C,IAAI,CAACuS,2BAA2B,GAAGrT,SAAS;IAChD;EACJ;EACA;EACAgR,eAAeA,CAAChB,YAAY,EAAE6D,WAAW,EAAE;IACvC,IAAIA,WAAW,KAAK7T,SAAS,EAAE;MAC3B,OAAOA,SAAS;IACpB;IACA,MAAM8T,gBAAgB,GAAG9D,YAAY,CAAC+D,OAAO,GAAG,IAAI,CAAChQ,KAAK,CAAC8P,WAAW,CAAC,CAAC9R,IAAI;IAC5E,MAAMgP,MAAM,GAAGzM,IAAI,CAACqP,KAAK,CAACG,gBAAgB,GAAG,IAAI,CAAC;IAClD,OAAOjU,KAAK,CAACkR,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;EAC9B;EACAF,2BAA2BA,CAAC/T,MAAM,EAAE;IAChC,MAAMuG,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACG,UAAU,CAAC,CAAC;IAC7D,IAAI6G,OAAO,GAAGvN,MAAM;IACpB,OAAO,CAACuB,aAAa,CAACgM,OAAO,CAAC,IAAI/L,YAAY,CAAC+L,OAAO,CAAC,KAAKA,OAAO,KAAK,IAAI,CAACjG,aAAa,IAAIf,iBAAiB,CAAC6O,QAAQ,CAAC7H,OAAO,CAAC,EAAE;MAC/H,MAAM2J,QAAQ,GAAG3J,OAAO,CAAC4J,YAAY,CAAC,YAAY,CAAC;MACnD,IAAID,QAAQ,EAAE;QACV,MAAMjH,KAAK,GAAGmH,MAAM,CAACF,QAAQ,CAAC;QAC9B,IAAI,CAACG,KAAK,CAACpH,KAAK,CAAC,EAAE;UACf,OAAOA,KAAK;QAChB;MACJ;MACA1C,OAAO,GAAGA,OAAO,CAACoE,aAAa;IACnC;IACA,OAAOzO,SAAS;EACpB;EACA+I,cAAcA,CAAC0E,SAAS,EAAEjB,YAAY,EAAE;IACpC,OAAO;MACHnD,KAAK,EAAE,IAAI,CAAClG,QAAQ,CAACmK,OAAO,CAACG,SAAS,CAAC;MACvC9D,GAAG,EAAE,IAAI,CAACxG,QAAQ,CAACqK,UAAU,CAACC,SAAS,GAAGjB,YAAY,GAAG,CAAC;IAC9D,CAAC;EACL;EACA;AACJ;AACA;AACA;EACIpD,SAASA,CAACqE,SAAS,EAAEjB,YAAY,EAAE0E,iBAAiB,EAAE;IAClD,MAAMzH,mBAAmB,GAAG,IAAI,CAACV,cAAc,CAAC0E,SAAS,EAAEjB,YAAY,CAAC;IACxE;IACA;IACA,IAAI4H,kBAAkB;IACtB,IAAIC,qBAAqB;IACzB,IAAI5G,SAAS,KAAK,IAAI,CAACL,UAAU,CAAC3D,mBAAmB,CAACJ,KAAK,CAAC,EAAE;MAC1D+K,kBAAkB,GAAG3K,mBAAmB,CAACJ,KAAK;MAC9CgL,qBAAqB,GAAG,CAAC;IAC7B,CAAC,MACI,IAAI5K,mBAAmB,CAACE,GAAG,GAAGF,mBAAmB,CAACJ,KAAK,GAAG,CAAC,EAAE;MAC9D+K,kBAAkB,GAAG3K,mBAAmB,CAACJ,KAAK,GAAG,CAAC;MAClDgL,qBAAqB,GAAG,IAAI,CAACjH,UAAU,CAACgH,kBAAkB,CAAC,GAAG3G,SAAS;IAC3E;IACA,IAAI6G,UAAU,GAAG,CAAC;IAClB,OAAO,IAAI,EAAE;MACT,MAAM7I,WAAW,GAAG,IAAI,CAAC1C,cAAc,CAAC0E,SAAS,EAAEjB,YAAY,CAAC;MAChE,IAAI+H,SAAS,GAAG,KAAK;MACrB,KAAK,IAAI7W,CAAC,GAAG+N,WAAW,CAACpC,KAAK,EAAE3L,CAAC,GAAG+N,WAAW,CAAC9B,GAAG,EAAEjM,CAAC,EAAE,EAAE;QACtD,MAAM+V,IAAI,GAAG,IAAI,CAACe,kBAAkB,CAAC9W,CAAC,CAAC;QACvC,IAAI+V,IAAI,KAAK,CAAC,EAAE;UACZ,IAAI,CAACtQ,QAAQ,CAACvB,MAAM,CAAClE,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAACqG,KAAK,CAACrG,CAAC,CAAC,CAAC,CAAC;QAC/C;QACA4W,UAAU,IAAIb,IAAI;QAClBc,SAAS,GAAGA,SAAS,IAAId,IAAI,KAAK,CAAC;MACvC;MACA,IAAI,CAACc,SAAS,EAAE;QACZ,IAAID,UAAU,KAAK,CAAC,EAAE;UAClB,IAAI,CAACnL,gCAAgC,CAAC,CAAC;QAC3C;QACA,MAAMsL,cAAc,GAAGlV,KAAK,CAACqL,kBAAkB,CAACnB,mBAAmB,EAAEgC,WAAW,CAAC;QACjF,KAAK,MAAMK,KAAK,IAAI2I,cAAc,EAAE;UAChC,KAAK,IAAI/W,CAAC,GAAGoO,KAAK,CAACzC,KAAK,EAAE3L,CAAC,GAAGoO,KAAK,CAACnC,GAAG,EAAEjM,CAAC,EAAE,EAAE;YAC1C,IAAI,IAAI,CAACqG,KAAK,CAACrG,CAAC,CAAC,CAACuM,GAAG,EAAE;cACnB,IAAI,CAAC8B,iBAAiB,CAACrO,CAAC,CAAC;YAC7B;UACJ;QACJ;QACA,MAAMgX,YAAY,GAAGnV,KAAK,CAACqL,kBAAkB,CAACa,WAAW,EAAEhC,mBAAmB,CAAC,CAAC0C,OAAO,CAAC,CAAC;QACzF,KAAK,MAAML,KAAK,IAAI4I,YAAY,EAAE;UAC9B,KAAK,IAAIhX,CAAC,GAAGoO,KAAK,CAACnC,GAAG,GAAG,CAAC,EAAEjM,CAAC,IAAIoO,KAAK,CAACzC,KAAK,EAAE3L,CAAC,EAAE,EAAE;YAC/C,IAAI,CAAC2O,eAAe,CAAC3O,CAAC,CAAC;UAC3B;QACJ;QACA,KAAK,IAAIA,CAAC,GAAG+N,WAAW,CAACpC,KAAK,EAAE3L,CAAC,GAAG+N,WAAW,CAAC9B,GAAG,EAAEjM,CAAC,EAAE,EAAE;UACtD,IAAI,IAAI,CAACqG,KAAK,CAACrG,CAAC,CAAC,CAACuM,GAAG,EAAE;YACnB,IAAI,CAAC2B,eAAe,CAAC,IAAI,CAAC7H,KAAK,CAACrG,CAAC,CAAC,EAAEA,CAAC,CAAC;UAC1C;QACJ;QACA,IAAI,OAAO0W,kBAAkB,KAAK,QAAQ,EAAE;UACxC;UACA;UACA;UACA;UACA;UACA,MAAMO,cAAc,GAAG,IAAI,CAACtN,UAAU,CAACuN,uBAAuB,CAAC,CAAC,CAAC1M,SAAS,GAAGuF,SAAS;UACtF,MAAMoH,YAAY,GAAG,IAAI,CAACzH,UAAU,CAACgH,kBAAkB,CAAC,GAAGC,qBAAqB,GAAGM,cAAc;UACjG,IAAI,CAACzL,YAAY,CAAC2L,YAAY,EAAE3D,iBAAiB,CAAC;QACtD;QACA,IAAI,CAACtL,yBAAyB,CAAC4D,IAAI,CAAC,IAAI,CAACtG,aAAa,CAAC;QACvD;MACJ;IACJ;EACJ;EACAsR,kBAAkBA,CAACzH,KAAK,EAAE;IACtB,MAAMjL,IAAI,GAAG,IAAI,CAACiC,KAAK,CAACgJ,KAAK,CAAC;IAC9B,IAAI,CAAC,CAAC,IAAI,CAAClI,eAAe,CAACiQ,gBAAgB,EAAE;MACzC,MAAMC,OAAO,GAAG,IAAI,CAAClQ,eAAe,CAACiQ,gBAAgB,CAAChT,IAAI,CAACuI,OAAO,CAAC;MACnE,IAAI0K,OAAO,KAAK,IAAI,EAAE;QAClB,MAAMhT,IAAI,GAAGD,IAAI,CAACC,IAAI;QACtBD,IAAI,CAACC,IAAI,GAAGgT,OAAO;QACnBjT,IAAI,CAACsJ,sBAAsB,GAAG,IAAI,CAAC3G,WAAW;QAC9C,OAAOsQ,OAAO,GAAGhT,IAAI;MACzB;IACJ;IACA,IAAI,CAACD,IAAI,CAACqJ,gBAAgB,IAAIrJ,IAAI,CAACsJ,sBAAsB,KAAK,IAAI,CAAC3G,WAAW,EAAE;MAC5E,OAAO,CAAC;IACZ;IACA,IAAI,CAAC,CAAC,IAAI,CAACI,eAAe,CAACsG,gBAAgB,IAAI,CAAC,IAAI,CAACtG,eAAe,CAACsG,gBAAgB,CAACrJ,IAAI,CAACuI,OAAO,CAAC,EAAE;MACjG,OAAO,CAAC;IACZ;IACA,MAAMtI,IAAI,GAAGD,IAAI,CAACC,IAAI;IACtB,IAAID,IAAI,CAACmI,GAAG,EAAE;MACVnI,IAAI,CAACmI,GAAG,CAACrG,OAAO,CAACS,KAAK,CAACoE,MAAM,GAAG,EAAE;MAClC3G,IAAI,CAACC,IAAI,GAAGD,IAAI,CAACmI,GAAG,CAACrG,OAAO,CAACoR,YAAY;MACzC,IAAIlT,IAAI,CAACC,IAAI,KAAK,CAAC,IAAI,CAAC3D,UAAU,CAAC0D,IAAI,CAACmI,GAAG,CAACrG,OAAO,EAAEzF,SAAS,CAAC2D,IAAI,CAACmI,GAAG,CAACrG,OAAO,CAAC,CAAC+C,QAAQ,CAACsO,IAAI,CAAC,EAAE;QAC7F7D,OAAO,CAAC8D,IAAI,CAAC,8FAA8F,EAAE,IAAIvR,KAAK,CAAC,CAAC,CAACwR,KAAK,CAAC;MACnI;MACArT,IAAI,CAACsJ,sBAAsB,GAAG,IAAI,CAAC3G,WAAW;MAC9C,OAAO3C,IAAI,CAACC,IAAI,GAAGA,IAAI;IAC3B;IACA,MAAM;MAAEkI;IAAI,CAAC,GAAG,IAAI,CAAC1D,KAAK,CAAC4H,KAAK,CAACrM,IAAI,CAACwE,UAAU,CAAC;IACjD2D,GAAG,CAACrG,OAAO,CAACS,KAAK,CAACoE,MAAM,GAAG,EAAE;IAC7B,IAAI,CAACrE,aAAa,CAAC2D,WAAW,CAACkC,GAAG,CAACrG,OAAO,CAAC;IAC3C,MAAMwC,QAAQ,GAAG,IAAI,CAACtB,SAAS,CAACqF,GAAG,CAACrI,IAAI,CAACwE,UAAU,CAAC;IACpD,IAAI,CAACF,QAAQ,EAAE;MACX,MAAM,IAAIxG,kBAAkB,CAAC,mCAAmC,GAAGkC,IAAI,CAACwE,UAAU,CAAC;IACvF;IACAF,QAAQ,CAAC0I,aAAa,CAAChN,IAAI,CAACuI,OAAO,EAAE0C,KAAK,EAAE9C,GAAG,CAACK,YAAY,EAAEtK,SAAS,CAAC;IACxE8B,IAAI,CAACC,IAAI,GAAGkI,GAAG,CAACrG,OAAO,CAACoR,YAAY;IACpC5O,QAAQ,CAACgE,cAAc,GAAGtI,IAAI,CAACuI,OAAO,EAAE0C,KAAK,EAAE9C,GAAG,CAACK,YAAY,EAAEtK,SAAS,CAAC;IAC3E,IAAI,CAAC6E,eAAe,CAACuQ,gBAAgB,GAAGtT,IAAI,CAACuI,OAAO,EAAEvI,IAAI,CAACC,IAAI,CAAC;IAChED,IAAI,CAACsJ,sBAAsB,GAAG,IAAI,CAAC3G,WAAW;IAC9CwF,GAAG,CAACrG,OAAO,CAACyO,MAAM,CAAC,CAAC;IACpB,IAAI,CAAC9L,KAAK,CAACgG,OAAO,CAACtC,GAAG,CAAC;IACvB,OAAOnI,IAAI,CAACC,IAAI,GAAGA,IAAI;EAC3B;EACAsN,eAAeA,CAACtC,KAAK,EAAE;IACnB,OAAO,GAAG,IAAI,CAAC/H,KAAK,IAAI+H,KAAK,EAAE;EACnC;EACA;EACAjM,OAAOA,CAAA,EAAG;IACN,KAAK,MAAMgB,IAAI,IAAI,IAAI,CAACiC,KAAK,EAAE;MAC3BjC,IAAI,CAACiI,mBAAmB,CAACjJ,OAAO,CAAC,CAAC;MAClCgB,IAAI,CAACkI,iBAAiB,CAAClJ,OAAO,CAAC,CAAC;MAChC,IAAIgB,IAAI,CAACmI,GAAG,EAAE;QACV,MAAM7D,QAAQ,GAAG,IAAI,CAACtB,SAAS,CAACqF,GAAG,CAACrI,IAAI,CAACmI,GAAG,CAAC3D,UAAU,CAAC;QACxD,IAAIF,QAAQ,EAAE;UACVA,QAAQ,CAACgE,cAAc,GAAGtI,IAAI,CAACuI,OAAO,EAAE,CAAC,CAAC,EAAEvI,IAAI,CAACmI,GAAG,CAACK,YAAY,EAAEtK,SAAS,CAAC;UAC7EoG,QAAQ,CAACiP,eAAe,CAACvT,IAAI,CAACmI,GAAG,CAACK,YAAY,CAAC;QACnD;MACJ;IACJ;IACA,IAAI,CAACvG,KAAK,GAAG,EAAE;IACf,IAAI,CAACH,OAAO,EAAEyO,MAAM,CAAC,CAAC;IACtB,IAAI,CAACgB,2BAA2B,EAAEvS,OAAO,CAAC,CAAC;IAC3C,IAAI,CAAC6E,WAAW,CAAC7E,OAAO,CAAC,CAAC;EAC9B;AACJ;AACAlE,UAAU,CAAC,CACPqC,OAAO,CACV,EAAE+D,QAAQ,CAACsS,SAAS,EAAE,cAAc,EAAE,IAAI,CAAC;AAC5C1Y,UAAU,CAAC,CACPqC,OAAO,CACV,EAAE+D,QAAQ,CAACsS,SAAS,EAAE,iBAAiB,EAAE,IAAI,CAAC;AAC/C1Y,UAAU,CAAC,CACPqC,OAAO,CACV,EAAE+D,QAAQ,CAACsS,SAAS,EAAE,oBAAoB,EAAE,IAAI,CAAC;AAClD1Y,UAAU,CAAC,CACPqC,OAAO,CACV,EAAE+D,QAAQ,CAACsS,SAAS,EAAE,aAAa,EAAE,IAAI,CAAC;AAC3C1Y,UAAU,CAAC,CACPqC,OAAO,CACV,EAAE+D,QAAQ,CAACsS,SAAS,EAAE,aAAa,EAAE,IAAI,CAAC;AAC3C1Y,UAAU,CAAC,CACPqC,OAAO,CACV,EAAE+D,QAAQ,CAACsS,SAAS,EAAE,YAAY,EAAE,IAAI,CAAC;AAC1C1Y,UAAU,CAAC,CACPqC,OAAO,CACV,EAAE+D,QAAQ,CAACsS,SAAS,EAAE,eAAe,EAAE,IAAI,CAAC;AAC7C1Y,UAAU,CAAC,CACPqC,OAAO,CACV,EAAE+D,QAAQ,CAACsS,SAAS,EAAE,cAAc,EAAE,IAAI,CAAC;AAC5C1Y,UAAU,CAAC,CACPqC,OAAO,CACV,EAAE+D,QAAQ,CAACsS,SAAS,EAAE,OAAO,EAAE,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}