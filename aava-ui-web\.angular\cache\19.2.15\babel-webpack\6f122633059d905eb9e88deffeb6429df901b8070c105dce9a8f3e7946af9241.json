{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { addDisposableListener, h, EventType } from '../../../../../base/browser/dom.js';\nimport { renderIcon } from '../../../../../base/browser/ui/iconLabel/iconLabels.js';\nimport { Codicon } from '../../../../../base/common/codicons.js';\nimport { Disposable, toDisposable } from '../../../../../base/common/lifecycle.js';\nimport { autorunWithStore, derived } from '../../../../../base/common/observable.js';\nimport { LineRange, LineRangeSet } from '../../../../common/core/lineRange.js';\nimport { Range } from '../../../../common/core/range.js';\nimport { LineRangeMapping } from '../../../../common/diff/rangeMapping.js';\nimport { GlyphMarginLane } from '../../../../common/model.js';\nimport { localize } from '../../../../../nls.js';\nconst emptyArr = [];\nexport class RevertButtonsFeature extends Disposable {\n  constructor(_editors, _diffModel, _options, _widget) {\n    super();\n    this._editors = _editors;\n    this._diffModel = _diffModel;\n    this._options = _options;\n    this._widget = _widget;\n    this._selectedDiffs = derived(this, reader => {\n      /** @description selectedDiffs */\n      const model = this._diffModel.read(reader);\n      const diff = model?.diff.read(reader);\n      // Return `emptyArr` because it is a constant. [] is always a new array and would trigger a change.\n      if (!diff) {\n        return emptyArr;\n      }\n      const selections = this._editors.modifiedSelections.read(reader);\n      if (selections.every(s => s.isEmpty())) {\n        return emptyArr;\n      }\n      const selectedLineNumbers = new LineRangeSet(selections.map(s => LineRange.fromRangeInclusive(s)));\n      const selectedMappings = diff.mappings.filter(m => m.lineRangeMapping.innerChanges && selectedLineNumbers.intersects(m.lineRangeMapping.modified));\n      const result = selectedMappings.map(mapping => ({\n        mapping,\n        rangeMappings: mapping.lineRangeMapping.innerChanges.filter(c => selections.some(s => Range.areIntersecting(c.modifiedRange, s)))\n      }));\n      if (result.length === 0 || result.every(r => r.rangeMappings.length === 0)) {\n        return emptyArr;\n      }\n      return result;\n    });\n    this._register(autorunWithStore((reader, store) => {\n      if (!this._options.shouldRenderOldRevertArrows.read(reader)) {\n        return;\n      }\n      const model = this._diffModel.read(reader);\n      const diff = model?.diff.read(reader);\n      if (!model || !diff) {\n        return;\n      }\n      if (model.movedTextToCompare.read(reader)) {\n        return;\n      }\n      const glyphWidgetsModified = [];\n      const selectedDiffs = this._selectedDiffs.read(reader);\n      const selectedDiffsSet = new Set(selectedDiffs.map(d => d.mapping));\n      if (selectedDiffs.length > 0) {\n        // The button to revert the selection\n        const selections = this._editors.modifiedSelections.read(reader);\n        const btn = store.add(new RevertButton(selections[selections.length - 1].positionLineNumber, this._widget, selectedDiffs.flatMap(d => d.rangeMappings), true));\n        this._editors.modified.addGlyphMarginWidget(btn);\n        glyphWidgetsModified.push(btn);\n      }\n      for (const m of diff.mappings) {\n        if (selectedDiffsSet.has(m)) {\n          continue;\n        }\n        if (!m.lineRangeMapping.modified.isEmpty && m.lineRangeMapping.innerChanges) {\n          const btn = store.add(new RevertButton(m.lineRangeMapping.modified.startLineNumber, this._widget, m.lineRangeMapping, false));\n          this._editors.modified.addGlyphMarginWidget(btn);\n          glyphWidgetsModified.push(btn);\n        }\n      }\n      store.add(toDisposable(() => {\n        for (const w of glyphWidgetsModified) {\n          this._editors.modified.removeGlyphMarginWidget(w);\n        }\n      }));\n    }));\n  }\n}\nexport let RevertButton = /*#__PURE__*/(() => {\n  class RevertButton extends Disposable {\n    static {\n      this.counter = 0;\n    }\n    getId() {\n      return this._id;\n    }\n    constructor(_lineNumber, _widget, _diffs, _revertSelection) {\n      super();\n      this._lineNumber = _lineNumber;\n      this._widget = _widget;\n      this._diffs = _diffs;\n      this._revertSelection = _revertSelection;\n      this._id = `revertButton${RevertButton.counter++}`;\n      this._domNode = h('div.revertButton', {\n        title: this._revertSelection ? localize('revertSelectedChanges', 'Revert Selected Changes') : localize('revertChange', 'Revert Change')\n      }, [renderIcon(Codicon.arrowRight)]).root;\n      this._register(addDisposableListener(this._domNode, EventType.MOUSE_DOWN, e => {\n        // don't prevent context menu from showing up\n        if (e.button !== 2) {\n          e.stopPropagation();\n          e.preventDefault();\n        }\n      }));\n      this._register(addDisposableListener(this._domNode, EventType.MOUSE_UP, e => {\n        e.stopPropagation();\n        e.preventDefault();\n      }));\n      this._register(addDisposableListener(this._domNode, EventType.CLICK, e => {\n        if (this._diffs instanceof LineRangeMapping) {\n          this._widget.revert(this._diffs);\n        } else {\n          this._widget.revertRangeMappings(this._diffs);\n        }\n        e.stopPropagation();\n        e.preventDefault();\n      }));\n    }\n    /**\n     * Get the dom node of the glyph widget.\n     */\n    getDomNode() {\n      return this._domNode;\n    }\n    /**\n     * Get the placement of the glyph widget.\n     */\n    getPosition() {\n      return {\n        lane: GlyphMarginLane.Right,\n        range: {\n          startColumn: 1,\n          startLineNumber: this._lineNumber,\n          endColumn: 1,\n          endLineNumber: this._lineNumber\n        },\n        zIndex: 10001\n      };\n    }\n  }\n  return RevertButton;\n})();", "map": {"version": 3, "names": ["addDisposableListener", "h", "EventType", "renderIcon", "Codicon", "Disposable", "toDisposable", "autorunWithStore", "derived", "LineRange", "LineRangeSet", "Range", "LineRangeMapping", "GlyphMarginLane", "localize", "emptyArr", "RevertButtonsFeature", "constructor", "_editors", "_diffModel", "_options", "_widget", "_selectedDiffs", "reader", "model", "read", "diff", "selections", "modifiedSelections", "every", "s", "isEmpty", "selectedLineNumbers", "map", "fromRangeInclusive", "selectedMappings", "mappings", "filter", "m", "lineRangeMapping", "innerChanges", "intersects", "modified", "result", "mapping", "rangeMappings", "c", "some", "areIntersecting", "modifiedRange", "length", "r", "_register", "store", "shouldRenderOldRevertArrows", "movedTextToCompare", "glyphWidgetsModified", "selectedDiffs", "selectedDiffsSet", "Set", "d", "btn", "add", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "positionLineNumber", "flatMap", "addGlyphMarginWidget", "push", "has", "startLineNumber", "w", "removeGlyphMarginWidget", "counter", "getId", "_id", "_lineNumber", "_diffs", "_revertSelection", "_domNode", "title", "arrowRight", "root", "MOUSE_DOWN", "e", "button", "stopPropagation", "preventDefault", "MOUSE_UP", "CLICK", "revert", "revertRangeMappings", "getDomNode", "getPosition", "lane", "Right", "range", "startColumn", "endColumn", "endLineNumber", "zIndex"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/browser/widget/diffEditor/features/revertButtonsFeature.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { addDisposableListener, h, EventType } from '../../../../../base/browser/dom.js';\nimport { renderIcon } from '../../../../../base/browser/ui/iconLabel/iconLabels.js';\nimport { Codicon } from '../../../../../base/common/codicons.js';\nimport { Disposable, toDisposable } from '../../../../../base/common/lifecycle.js';\nimport { autorunWithStore, derived } from '../../../../../base/common/observable.js';\nimport { LineRange, LineRangeSet } from '../../../../common/core/lineRange.js';\nimport { Range } from '../../../../common/core/range.js';\nimport { LineRangeMapping } from '../../../../common/diff/rangeMapping.js';\nimport { GlyphMarginLane } from '../../../../common/model.js';\nimport { localize } from '../../../../../nls.js';\nconst emptyArr = [];\nexport class RevertButtonsFeature extends Disposable {\n    constructor(_editors, _diffModel, _options, _widget) {\n        super();\n        this._editors = _editors;\n        this._diffModel = _diffModel;\n        this._options = _options;\n        this._widget = _widget;\n        this._selectedDiffs = derived(this, (reader) => {\n            /** @description selectedDiffs */\n            const model = this._diffModel.read(reader);\n            const diff = model?.diff.read(reader);\n            // Return `emptyArr` because it is a constant. [] is always a new array and would trigger a change.\n            if (!diff) {\n                return emptyArr;\n            }\n            const selections = this._editors.modifiedSelections.read(reader);\n            if (selections.every(s => s.isEmpty())) {\n                return emptyArr;\n            }\n            const selectedLineNumbers = new LineRangeSet(selections.map(s => LineRange.fromRangeInclusive(s)));\n            const selectedMappings = diff.mappings.filter(m => m.lineRangeMapping.innerChanges && selectedLineNumbers.intersects(m.lineRangeMapping.modified));\n            const result = selectedMappings.map(mapping => ({\n                mapping,\n                rangeMappings: mapping.lineRangeMapping.innerChanges.filter(c => selections.some(s => Range.areIntersecting(c.modifiedRange, s)))\n            }));\n            if (result.length === 0 || result.every(r => r.rangeMappings.length === 0)) {\n                return emptyArr;\n            }\n            return result;\n        });\n        this._register(autorunWithStore((reader, store) => {\n            if (!this._options.shouldRenderOldRevertArrows.read(reader)) {\n                return;\n            }\n            const model = this._diffModel.read(reader);\n            const diff = model?.diff.read(reader);\n            if (!model || !diff) {\n                return;\n            }\n            if (model.movedTextToCompare.read(reader)) {\n                return;\n            }\n            const glyphWidgetsModified = [];\n            const selectedDiffs = this._selectedDiffs.read(reader);\n            const selectedDiffsSet = new Set(selectedDiffs.map(d => d.mapping));\n            if (selectedDiffs.length > 0) {\n                // The button to revert the selection\n                const selections = this._editors.modifiedSelections.read(reader);\n                const btn = store.add(new RevertButton(selections[selections.length - 1].positionLineNumber, this._widget, selectedDiffs.flatMap(d => d.rangeMappings), true));\n                this._editors.modified.addGlyphMarginWidget(btn);\n                glyphWidgetsModified.push(btn);\n            }\n            for (const m of diff.mappings) {\n                if (selectedDiffsSet.has(m)) {\n                    continue;\n                }\n                if (!m.lineRangeMapping.modified.isEmpty && m.lineRangeMapping.innerChanges) {\n                    const btn = store.add(new RevertButton(m.lineRangeMapping.modified.startLineNumber, this._widget, m.lineRangeMapping, false));\n                    this._editors.modified.addGlyphMarginWidget(btn);\n                    glyphWidgetsModified.push(btn);\n                }\n            }\n            store.add(toDisposable(() => {\n                for (const w of glyphWidgetsModified) {\n                    this._editors.modified.removeGlyphMarginWidget(w);\n                }\n            }));\n        }));\n    }\n}\nexport class RevertButton extends Disposable {\n    static { this.counter = 0; }\n    getId() { return this._id; }\n    constructor(_lineNumber, _widget, _diffs, _revertSelection) {\n        super();\n        this._lineNumber = _lineNumber;\n        this._widget = _widget;\n        this._diffs = _diffs;\n        this._revertSelection = _revertSelection;\n        this._id = `revertButton${RevertButton.counter++}`;\n        this._domNode = h('div.revertButton', {\n            title: this._revertSelection\n                ? localize('revertSelectedChanges', 'Revert Selected Changes')\n                : localize('revertChange', 'Revert Change')\n        }, [renderIcon(Codicon.arrowRight)]).root;\n        this._register(addDisposableListener(this._domNode, EventType.MOUSE_DOWN, e => {\n            // don't prevent context menu from showing up\n            if (e.button !== 2) {\n                e.stopPropagation();\n                e.preventDefault();\n            }\n        }));\n        this._register(addDisposableListener(this._domNode, EventType.MOUSE_UP, e => {\n            e.stopPropagation();\n            e.preventDefault();\n        }));\n        this._register(addDisposableListener(this._domNode, EventType.CLICK, (e) => {\n            if (this._diffs instanceof LineRangeMapping) {\n                this._widget.revert(this._diffs);\n            }\n            else {\n                this._widget.revertRangeMappings(this._diffs);\n            }\n            e.stopPropagation();\n            e.preventDefault();\n        }));\n    }\n    /**\n     * Get the dom node of the glyph widget.\n     */\n    getDomNode() {\n        return this._domNode;\n    }\n    /**\n     * Get the placement of the glyph widget.\n     */\n    getPosition() {\n        return {\n            lane: GlyphMarginLane.Right,\n            range: {\n                startColumn: 1,\n                startLineNumber: this._lineNumber,\n                endColumn: 1,\n                endLineNumber: this._lineNumber,\n            },\n            zIndex: 10001,\n        };\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,qBAAqB,EAAEC,CAAC,EAAEC,SAAS,QAAQ,oCAAoC;AACxF,SAASC,UAAU,QAAQ,wDAAwD;AACnF,SAASC,OAAO,QAAQ,wCAAwC;AAChE,SAASC,UAAU,EAAEC,YAAY,QAAQ,yCAAyC;AAClF,SAASC,gBAAgB,EAAEC,OAAO,QAAQ,0CAA0C;AACpF,SAASC,SAAS,EAAEC,YAAY,QAAQ,sCAAsC;AAC9E,SAASC,KAAK,QAAQ,kCAAkC;AACxD,SAASC,gBAAgB,QAAQ,yCAAyC;AAC1E,SAASC,eAAe,QAAQ,6BAA6B;AAC7D,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,MAAMC,QAAQ,GAAG,EAAE;AACnB,OAAO,MAAMC,oBAAoB,SAASX,UAAU,CAAC;EACjDY,WAAWA,CAACC,QAAQ,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,OAAO,EAAE;IACjD,KAAK,CAAC,CAAC;IACP,IAAI,CAACH,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,cAAc,GAAGd,OAAO,CAAC,IAAI,EAAGe,MAAM,IAAK;MAC5C;MACA,MAAMC,KAAK,GAAG,IAAI,CAACL,UAAU,CAACM,IAAI,CAACF,MAAM,CAAC;MAC1C,MAAMG,IAAI,GAAGF,KAAK,EAAEE,IAAI,CAACD,IAAI,CAACF,MAAM,CAAC;MACrC;MACA,IAAI,CAACG,IAAI,EAAE;QACP,OAAOX,QAAQ;MACnB;MACA,MAAMY,UAAU,GAAG,IAAI,CAACT,QAAQ,CAACU,kBAAkB,CAACH,IAAI,CAACF,MAAM,CAAC;MAChE,IAAII,UAAU,CAACE,KAAK,CAACC,CAAC,IAAIA,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,EAAE;QACpC,OAAOhB,QAAQ;MACnB;MACA,MAAMiB,mBAAmB,GAAG,IAAItB,YAAY,CAACiB,UAAU,CAACM,GAAG,CAACH,CAAC,IAAIrB,SAAS,CAACyB,kBAAkB,CAACJ,CAAC,CAAC,CAAC,CAAC;MAClG,MAAMK,gBAAgB,GAAGT,IAAI,CAACU,QAAQ,CAACC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,gBAAgB,CAACC,YAAY,IAAIR,mBAAmB,CAACS,UAAU,CAACH,CAAC,CAACC,gBAAgB,CAACG,QAAQ,CAAC,CAAC;MAClJ,MAAMC,MAAM,GAAGR,gBAAgB,CAACF,GAAG,CAACW,OAAO,KAAK;QAC5CA,OAAO;QACPC,aAAa,EAAED,OAAO,CAACL,gBAAgB,CAACC,YAAY,CAACH,MAAM,CAACS,CAAC,IAAInB,UAAU,CAACoB,IAAI,CAACjB,CAAC,IAAInB,KAAK,CAACqC,eAAe,CAACF,CAAC,CAACG,aAAa,EAAEnB,CAAC,CAAC,CAAC;MACpI,CAAC,CAAC,CAAC;MACH,IAAIa,MAAM,CAACO,MAAM,KAAK,CAAC,IAAIP,MAAM,CAACd,KAAK,CAACsB,CAAC,IAAIA,CAAC,CAACN,aAAa,CAACK,MAAM,KAAK,CAAC,CAAC,EAAE;QACxE,OAAOnC,QAAQ;MACnB;MACA,OAAO4B,MAAM;IACjB,CAAC,CAAC;IACF,IAAI,CAACS,SAAS,CAAC7C,gBAAgB,CAAC,CAACgB,MAAM,EAAE8B,KAAK,KAAK;MAC/C,IAAI,CAAC,IAAI,CAACjC,QAAQ,CAACkC,2BAA2B,CAAC7B,IAAI,CAACF,MAAM,CAAC,EAAE;QACzD;MACJ;MACA,MAAMC,KAAK,GAAG,IAAI,CAACL,UAAU,CAACM,IAAI,CAACF,MAAM,CAAC;MAC1C,MAAMG,IAAI,GAAGF,KAAK,EAAEE,IAAI,CAACD,IAAI,CAACF,MAAM,CAAC;MACrC,IAAI,CAACC,KAAK,IAAI,CAACE,IAAI,EAAE;QACjB;MACJ;MACA,IAAIF,KAAK,CAAC+B,kBAAkB,CAAC9B,IAAI,CAACF,MAAM,CAAC,EAAE;QACvC;MACJ;MACA,MAAMiC,oBAAoB,GAAG,EAAE;MAC/B,MAAMC,aAAa,GAAG,IAAI,CAACnC,cAAc,CAACG,IAAI,CAACF,MAAM,CAAC;MACtD,MAAMmC,gBAAgB,GAAG,IAAIC,GAAG,CAACF,aAAa,CAACxB,GAAG,CAAC2B,CAAC,IAAIA,CAAC,CAAChB,OAAO,CAAC,CAAC;MACnE,IAAIa,aAAa,CAACP,MAAM,GAAG,CAAC,EAAE;QAC1B;QACA,MAAMvB,UAAU,GAAG,IAAI,CAACT,QAAQ,CAACU,kBAAkB,CAACH,IAAI,CAACF,MAAM,CAAC;QAChE,MAAMsC,GAAG,GAAGR,KAAK,CAACS,GAAG,CAAC,IAAIC,YAAY,CAACpC,UAAU,CAACA,UAAU,CAACuB,MAAM,GAAG,CAAC,CAAC,CAACc,kBAAkB,EAAE,IAAI,CAAC3C,OAAO,EAAEoC,aAAa,CAACQ,OAAO,CAACL,CAAC,IAAIA,CAAC,CAACf,aAAa,CAAC,EAAE,IAAI,CAAC,CAAC;QAC9J,IAAI,CAAC3B,QAAQ,CAACwB,QAAQ,CAACwB,oBAAoB,CAACL,GAAG,CAAC;QAChDL,oBAAoB,CAACW,IAAI,CAACN,GAAG,CAAC;MAClC;MACA,KAAK,MAAMvB,CAAC,IAAIZ,IAAI,CAACU,QAAQ,EAAE;QAC3B,IAAIsB,gBAAgB,CAACU,GAAG,CAAC9B,CAAC,CAAC,EAAE;UACzB;QACJ;QACA,IAAI,CAACA,CAAC,CAACC,gBAAgB,CAACG,QAAQ,CAACX,OAAO,IAAIO,CAAC,CAACC,gBAAgB,CAACC,YAAY,EAAE;UACzE,MAAMqB,GAAG,GAAGR,KAAK,CAACS,GAAG,CAAC,IAAIC,YAAY,CAACzB,CAAC,CAACC,gBAAgB,CAACG,QAAQ,CAAC2B,eAAe,EAAE,IAAI,CAAChD,OAAO,EAAEiB,CAAC,CAACC,gBAAgB,EAAE,KAAK,CAAC,CAAC;UAC7H,IAAI,CAACrB,QAAQ,CAACwB,QAAQ,CAACwB,oBAAoB,CAACL,GAAG,CAAC;UAChDL,oBAAoB,CAACW,IAAI,CAACN,GAAG,CAAC;QAClC;MACJ;MACAR,KAAK,CAACS,GAAG,CAACxD,YAAY,CAAC,MAAM;QACzB,KAAK,MAAMgE,CAAC,IAAId,oBAAoB,EAAE;UAClC,IAAI,CAACtC,QAAQ,CAACwB,QAAQ,CAAC6B,uBAAuB,CAACD,CAAC,CAAC;QACrD;MACJ,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC;EACP;AACJ;AACA,WAAaP,YAAY;EAAlB,MAAMA,YAAY,SAAS1D,UAAU,CAAC;IACzC;MAAS,IAAI,CAACmE,OAAO,GAAG,CAAC;IAAE;IAC3BC,KAAKA,CAAA,EAAG;MAAE,OAAO,IAAI,CAACC,GAAG;IAAE;IAC3BzD,WAAWA,CAAC0D,WAAW,EAAEtD,OAAO,EAAEuD,MAAM,EAAEC,gBAAgB,EAAE;MACxD,KAAK,CAAC,CAAC;MACP,IAAI,CAACF,WAAW,GAAGA,WAAW;MAC9B,IAAI,CAACtD,OAAO,GAAGA,OAAO;MACtB,IAAI,CAACuD,MAAM,GAAGA,MAAM;MACpB,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;MACxC,IAAI,CAACH,GAAG,GAAG,eAAeX,YAAY,CAACS,OAAO,EAAE,EAAE;MAClD,IAAI,CAACM,QAAQ,GAAG7E,CAAC,CAAC,kBAAkB,EAAE;QAClC8E,KAAK,EAAE,IAAI,CAACF,gBAAgB,GACtB/D,QAAQ,CAAC,uBAAuB,EAAE,yBAAyB,CAAC,GAC5DA,QAAQ,CAAC,cAAc,EAAE,eAAe;MAClD,CAAC,EAAE,CAACX,UAAU,CAACC,OAAO,CAAC4E,UAAU,CAAC,CAAC,CAAC,CAACC,IAAI;MACzC,IAAI,CAAC7B,SAAS,CAACpD,qBAAqB,CAAC,IAAI,CAAC8E,QAAQ,EAAE5E,SAAS,CAACgF,UAAU,EAAEC,CAAC,IAAI;QAC3E;QACA,IAAIA,CAAC,CAACC,MAAM,KAAK,CAAC,EAAE;UAChBD,CAAC,CAACE,eAAe,CAAC,CAAC;UACnBF,CAAC,CAACG,cAAc,CAAC,CAAC;QACtB;MACJ,CAAC,CAAC,CAAC;MACH,IAAI,CAAClC,SAAS,CAACpD,qBAAqB,CAAC,IAAI,CAAC8E,QAAQ,EAAE5E,SAAS,CAACqF,QAAQ,EAAEJ,CAAC,IAAI;QACzEA,CAAC,CAACE,eAAe,CAAC,CAAC;QACnBF,CAAC,CAACG,cAAc,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC;MACH,IAAI,CAAClC,SAAS,CAACpD,qBAAqB,CAAC,IAAI,CAAC8E,QAAQ,EAAE5E,SAAS,CAACsF,KAAK,EAAGL,CAAC,IAAK;QACxE,IAAI,IAAI,CAACP,MAAM,YAAYhE,gBAAgB,EAAE;UACzC,IAAI,CAACS,OAAO,CAACoE,MAAM,CAAC,IAAI,CAACb,MAAM,CAAC;QACpC,CAAC,MACI;UACD,IAAI,CAACvD,OAAO,CAACqE,mBAAmB,CAAC,IAAI,CAACd,MAAM,CAAC;QACjD;QACAO,CAAC,CAACE,eAAe,CAAC,CAAC;QACnBF,CAAC,CAACG,cAAc,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC;IACP;IACA;AACJ;AACA;IACIK,UAAUA,CAAA,EAAG;MACT,OAAO,IAAI,CAACb,QAAQ;IACxB;IACA;AACJ;AACA;IACIc,WAAWA,CAAA,EAAG;MACV,OAAO;QACHC,IAAI,EAAEhF,eAAe,CAACiF,KAAK;QAC3BC,KAAK,EAAE;UACHC,WAAW,EAAE,CAAC;UACd3B,eAAe,EAAE,IAAI,CAACM,WAAW;UACjCsB,SAAS,EAAE,CAAC;UACZC,aAAa,EAAE,IAAI,CAACvB;QACxB,CAAC;QACDwB,MAAM,EAAE;MACZ,CAAC;IACL;EACJ;EAAC,OA1DYpC,YAAY;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}