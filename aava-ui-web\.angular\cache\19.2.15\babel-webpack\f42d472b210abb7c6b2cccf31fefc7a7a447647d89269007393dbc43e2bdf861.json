{"ast": null, "code": "import _asyncToGenerator from \"C:/console/aava-ui-web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { ActionBar } from '../actionbar/actionbar.js';\nimport { DropdownMenuActionViewItem } from '../dropdown/dropdownActionViewItem.js';\nimport { Action, SubmenuAction } from '../../../common/actions.js';\nimport { Codicon } from '../../../common/codicons.js';\nimport { ThemeIcon } from '../../../common/themables.js';\nimport { EventMultiplexer } from '../../../common/event.js';\nimport { Disposable, DisposableStore } from '../../../common/lifecycle.js';\nimport './toolbar.css';\nimport * as nls from '../../../../nls.js';\nimport { createInstantHoverDelegate } from '../hover/hoverDelegateFactory.js';\n/**\n * A widget that combines an action bar for primary actions and a dropdown for secondary actions.\n */\nexport class ToolBar extends Disposable {\n  constructor(container, contextMenuProvider, options = {\n    orientation: 0 /* ActionsOrientation.HORIZONTAL */\n  }) {\n    super();\n    this.submenuActionViewItems = [];\n    this.hasSecondaryActions = false;\n    this._onDidChangeDropdownVisibility = this._register(new EventMultiplexer());\n    this.onDidChangeDropdownVisibility = this._onDidChangeDropdownVisibility.event;\n    this.disposables = this._register(new DisposableStore());\n    options.hoverDelegate = options.hoverDelegate ?? this._register(createInstantHoverDelegate());\n    this.options = options;\n    this.toggleMenuAction = this._register(new ToggleMenuAction(() => this.toggleMenuActionViewItem?.show(), options.toggleMenuTitle));\n    this.element = document.createElement('div');\n    this.element.className = 'monaco-toolbar';\n    container.appendChild(this.element);\n    this.actionBar = this._register(new ActionBar(this.element, {\n      orientation: options.orientation,\n      ariaLabel: options.ariaLabel,\n      actionRunner: options.actionRunner,\n      allowContextMenu: options.allowContextMenu,\n      highlightToggledItems: options.highlightToggledItems,\n      hoverDelegate: options.hoverDelegate,\n      actionViewItemProvider: (action, viewItemOptions) => {\n        if (action.id === ToggleMenuAction.ID) {\n          this.toggleMenuActionViewItem = new DropdownMenuActionViewItem(action, action.menuActions, contextMenuProvider, {\n            actionViewItemProvider: this.options.actionViewItemProvider,\n            actionRunner: this.actionRunner,\n            keybindingProvider: this.options.getKeyBinding,\n            classNames: ThemeIcon.asClassNameArray(options.moreIcon ?? Codicon.toolBarMore),\n            anchorAlignmentProvider: this.options.anchorAlignmentProvider,\n            menuAsChild: !!this.options.renderDropdownAsChildElement,\n            skipTelemetry: this.options.skipTelemetry,\n            isMenu: true,\n            hoverDelegate: this.options.hoverDelegate\n          });\n          this.toggleMenuActionViewItem.setActionContext(this.actionBar.context);\n          this.disposables.add(this._onDidChangeDropdownVisibility.add(this.toggleMenuActionViewItem.onDidChangeVisibility));\n          return this.toggleMenuActionViewItem;\n        }\n        if (options.actionViewItemProvider) {\n          const result = options.actionViewItemProvider(action, viewItemOptions);\n          if (result) {\n            return result;\n          }\n        }\n        if (action instanceof SubmenuAction) {\n          const result = new DropdownMenuActionViewItem(action, action.actions, contextMenuProvider, {\n            actionViewItemProvider: this.options.actionViewItemProvider,\n            actionRunner: this.actionRunner,\n            keybindingProvider: this.options.getKeyBinding,\n            classNames: action.class,\n            anchorAlignmentProvider: this.options.anchorAlignmentProvider,\n            menuAsChild: !!this.options.renderDropdownAsChildElement,\n            skipTelemetry: this.options.skipTelemetry,\n            hoverDelegate: this.options.hoverDelegate\n          });\n          result.setActionContext(this.actionBar.context);\n          this.submenuActionViewItems.push(result);\n          this.disposables.add(this._onDidChangeDropdownVisibility.add(result.onDidChangeVisibility));\n          return result;\n        }\n        return undefined;\n      }\n    }));\n  }\n  set actionRunner(actionRunner) {\n    this.actionBar.actionRunner = actionRunner;\n  }\n  get actionRunner() {\n    return this.actionBar.actionRunner;\n  }\n  getElement() {\n    return this.element;\n  }\n  getItemAction(indexOrElement) {\n    return this.actionBar.getAction(indexOrElement);\n  }\n  setActions(primaryActions, secondaryActions) {\n    this.clear();\n    const primaryActionsToSet = primaryActions ? primaryActions.slice(0) : [];\n    // Inject additional action to open secondary actions if present\n    this.hasSecondaryActions = !!(secondaryActions && secondaryActions.length > 0);\n    if (this.hasSecondaryActions && secondaryActions) {\n      this.toggleMenuAction.menuActions = secondaryActions.slice(0);\n      primaryActionsToSet.push(this.toggleMenuAction);\n    }\n    primaryActionsToSet.forEach(action => {\n      this.actionBar.push(action, {\n        icon: this.options.icon ?? true,\n        label: this.options.label ?? false,\n        keybinding: this.getKeybindingLabel(action)\n      });\n    });\n  }\n  getKeybindingLabel(action) {\n    const key = this.options.getKeyBinding?.(action);\n    return key?.getLabel() ?? undefined;\n  }\n  clear() {\n    this.submenuActionViewItems = [];\n    this.disposables.clear();\n    this.actionBar.clear();\n  }\n  dispose() {\n    this.clear();\n    this.disposables.dispose();\n    super.dispose();\n  }\n}\nexport let ToggleMenuAction = /*#__PURE__*/(() => {\n  class ToggleMenuAction extends Action {\n    static {\n      this.ID = 'toolbar.toggle.more';\n    }\n    constructor(toggleDropdownMenu, title) {\n      title = title || nls.localize('moreActions', \"More Actions...\");\n      super(ToggleMenuAction.ID, title, undefined, true);\n      this._menuActions = [];\n      this.toggleDropdownMenu = toggleDropdownMenu;\n    }\n    run() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        _this.toggleDropdownMenu();\n      })();\n    }\n    get menuActions() {\n      return this._menuActions;\n    }\n    set menuActions(actions) {\n      this._menuActions = actions;\n    }\n  }\n  return ToggleMenuAction;\n})();", "map": {"version": 3, "names": ["ActionBar", "DropdownMenuActionViewItem", "Action", "SubmenuAction", "Codicon", "ThemeIcon", "EventMultiplexer", "Disposable", "DisposableStore", "nls", "createInstantHoverDelegate", "<PERSON><PERSON><PERSON><PERSON>", "constructor", "container", "contextMenuProvider", "options", "orientation", "submenuActionViewItems", "hasSecondaryActions", "_onDidChangeDropdownVisibility", "_register", "onDidChangeDropdownVisibility", "event", "disposables", "hoverDelegate", "toggleMenuAction", "ToggleMenuAction", "toggleMenuActionViewItem", "show", "toggleMenuTitle", "element", "document", "createElement", "className", "append<PERSON><PERSON><PERSON>", "actionBar", "aria<PERSON><PERSON><PERSON>", "actionRunner", "allowContextMenu", "highlightToggledItems", "actionViewItemProvider", "action", "viewItemOptions", "id", "ID", "menuActions", "keybindingProvider", "getKeyBinding", "classNames", "asClassNameArray", "moreIcon", "toolBarMore", "anchorAlignmentProvider", "menuAsChild", "renderDropdownAsChildElement", "skipTelemetry", "isMenu", "setActionContext", "context", "add", "onDidChangeVisibility", "result", "actions", "class", "push", "undefined", "getElement", "getItemAction", "indexOrElement", "getAction", "setActions", "primaryActions", "secondaryActions", "clear", "primaryActionsToSet", "slice", "length", "for<PERSON>ach", "icon", "label", "keybinding", "getKeybindingLabel", "key", "get<PERSON><PERSON><PERSON>", "dispose", "toggleDropdownMenu", "title", "localize", "_menuActions", "run", "_this", "_asyncToGenerator"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/browser/ui/toolbar/toolbar.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { ActionBar } from '../actionbar/actionbar.js';\nimport { DropdownMenuActionViewItem } from '../dropdown/dropdownActionViewItem.js';\nimport { Action, SubmenuAction } from '../../../common/actions.js';\nimport { Codicon } from '../../../common/codicons.js';\nimport { ThemeIcon } from '../../../common/themables.js';\nimport { EventMultiplexer } from '../../../common/event.js';\nimport { Disposable, DisposableStore } from '../../../common/lifecycle.js';\nimport './toolbar.css';\nimport * as nls from '../../../../nls.js';\nimport { createInstantHoverDelegate } from '../hover/hoverDelegateFactory.js';\n/**\n * A widget that combines an action bar for primary actions and a dropdown for secondary actions.\n */\nexport class ToolBar extends Disposable {\n    constructor(container, contextMenuProvider, options = { orientation: 0 /* ActionsOrientation.HORIZONTAL */ }) {\n        super();\n        this.submenuActionViewItems = [];\n        this.hasSecondaryActions = false;\n        this._onDidChangeDropdownVisibility = this._register(new EventMultiplexer());\n        this.onDidChangeDropdownVisibility = this._onDidChangeDropdownVisibility.event;\n        this.disposables = this._register(new DisposableStore());\n        options.hoverDelegate = options.hoverDelegate ?? this._register(createInstantHoverDelegate());\n        this.options = options;\n        this.toggleMenuAction = this._register(new ToggleMenuAction(() => this.toggleMenuActionViewItem?.show(), options.toggleMenuTitle));\n        this.element = document.createElement('div');\n        this.element.className = 'monaco-toolbar';\n        container.appendChild(this.element);\n        this.actionBar = this._register(new ActionBar(this.element, {\n            orientation: options.orientation,\n            ariaLabel: options.ariaLabel,\n            actionRunner: options.actionRunner,\n            allowContextMenu: options.allowContextMenu,\n            highlightToggledItems: options.highlightToggledItems,\n            hoverDelegate: options.hoverDelegate,\n            actionViewItemProvider: (action, viewItemOptions) => {\n                if (action.id === ToggleMenuAction.ID) {\n                    this.toggleMenuActionViewItem = new DropdownMenuActionViewItem(action, action.menuActions, contextMenuProvider, {\n                        actionViewItemProvider: this.options.actionViewItemProvider,\n                        actionRunner: this.actionRunner,\n                        keybindingProvider: this.options.getKeyBinding,\n                        classNames: ThemeIcon.asClassNameArray(options.moreIcon ?? Codicon.toolBarMore),\n                        anchorAlignmentProvider: this.options.anchorAlignmentProvider,\n                        menuAsChild: !!this.options.renderDropdownAsChildElement,\n                        skipTelemetry: this.options.skipTelemetry,\n                        isMenu: true,\n                        hoverDelegate: this.options.hoverDelegate\n                    });\n                    this.toggleMenuActionViewItem.setActionContext(this.actionBar.context);\n                    this.disposables.add(this._onDidChangeDropdownVisibility.add(this.toggleMenuActionViewItem.onDidChangeVisibility));\n                    return this.toggleMenuActionViewItem;\n                }\n                if (options.actionViewItemProvider) {\n                    const result = options.actionViewItemProvider(action, viewItemOptions);\n                    if (result) {\n                        return result;\n                    }\n                }\n                if (action instanceof SubmenuAction) {\n                    const result = new DropdownMenuActionViewItem(action, action.actions, contextMenuProvider, {\n                        actionViewItemProvider: this.options.actionViewItemProvider,\n                        actionRunner: this.actionRunner,\n                        keybindingProvider: this.options.getKeyBinding,\n                        classNames: action.class,\n                        anchorAlignmentProvider: this.options.anchorAlignmentProvider,\n                        menuAsChild: !!this.options.renderDropdownAsChildElement,\n                        skipTelemetry: this.options.skipTelemetry,\n                        hoverDelegate: this.options.hoverDelegate\n                    });\n                    result.setActionContext(this.actionBar.context);\n                    this.submenuActionViewItems.push(result);\n                    this.disposables.add(this._onDidChangeDropdownVisibility.add(result.onDidChangeVisibility));\n                    return result;\n                }\n                return undefined;\n            }\n        }));\n    }\n    set actionRunner(actionRunner) {\n        this.actionBar.actionRunner = actionRunner;\n    }\n    get actionRunner() {\n        return this.actionBar.actionRunner;\n    }\n    getElement() {\n        return this.element;\n    }\n    getItemAction(indexOrElement) {\n        return this.actionBar.getAction(indexOrElement);\n    }\n    setActions(primaryActions, secondaryActions) {\n        this.clear();\n        const primaryActionsToSet = primaryActions ? primaryActions.slice(0) : [];\n        // Inject additional action to open secondary actions if present\n        this.hasSecondaryActions = !!(secondaryActions && secondaryActions.length > 0);\n        if (this.hasSecondaryActions && secondaryActions) {\n            this.toggleMenuAction.menuActions = secondaryActions.slice(0);\n            primaryActionsToSet.push(this.toggleMenuAction);\n        }\n        primaryActionsToSet.forEach(action => {\n            this.actionBar.push(action, { icon: this.options.icon ?? true, label: this.options.label ?? false, keybinding: this.getKeybindingLabel(action) });\n        });\n    }\n    getKeybindingLabel(action) {\n        const key = this.options.getKeyBinding?.(action);\n        return key?.getLabel() ?? undefined;\n    }\n    clear() {\n        this.submenuActionViewItems = [];\n        this.disposables.clear();\n        this.actionBar.clear();\n    }\n    dispose() {\n        this.clear();\n        this.disposables.dispose();\n        super.dispose();\n    }\n}\nexport class ToggleMenuAction extends Action {\n    static { this.ID = 'toolbar.toggle.more'; }\n    constructor(toggleDropdownMenu, title) {\n        title = title || nls.localize('moreActions', \"More Actions...\");\n        super(ToggleMenuAction.ID, title, undefined, true);\n        this._menuActions = [];\n        this.toggleDropdownMenu = toggleDropdownMenu;\n    }\n    async run() {\n        this.toggleDropdownMenu();\n    }\n    get menuActions() {\n        return this._menuActions;\n    }\n    set menuActions(actions) {\n        this._menuActions = actions;\n    }\n}\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,2BAA2B;AACrD,SAASC,0BAA0B,QAAQ,uCAAuC;AAClF,SAASC,MAAM,EAAEC,aAAa,QAAQ,4BAA4B;AAClE,SAASC,OAAO,QAAQ,6BAA6B;AACrD,SAASC,SAAS,QAAQ,8BAA8B;AACxD,SAASC,gBAAgB,QAAQ,0BAA0B;AAC3D,SAASC,UAAU,EAAEC,eAAe,QAAQ,8BAA8B;AAC1E,OAAO,eAAe;AACtB,OAAO,KAAKC,GAAG,MAAM,oBAAoB;AACzC,SAASC,0BAA0B,QAAQ,kCAAkC;AAC7E;AACA;AACA;AACA,OAAO,MAAMC,OAAO,SAASJ,UAAU,CAAC;EACpCK,WAAWA,CAACC,SAAS,EAAEC,mBAAmB,EAAEC,OAAO,GAAG;IAAEC,WAAW,EAAE,CAAC,CAAC;EAAoC,CAAC,EAAE;IAC1G,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,sBAAsB,GAAG,EAAE;IAChC,IAAI,CAACC,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACC,8BAA8B,GAAG,IAAI,CAACC,SAAS,CAAC,IAAId,gBAAgB,CAAC,CAAC,CAAC;IAC5E,IAAI,CAACe,6BAA6B,GAAG,IAAI,CAACF,8BAA8B,CAACG,KAAK;IAC9E,IAAI,CAACC,WAAW,GAAG,IAAI,CAACH,SAAS,CAAC,IAAIZ,eAAe,CAAC,CAAC,CAAC;IACxDO,OAAO,CAACS,aAAa,GAAGT,OAAO,CAACS,aAAa,IAAI,IAAI,CAACJ,SAAS,CAACV,0BAA0B,CAAC,CAAC,CAAC;IAC7F,IAAI,CAACK,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACU,gBAAgB,GAAG,IAAI,CAACL,SAAS,CAAC,IAAIM,gBAAgB,CAAC,MAAM,IAAI,CAACC,wBAAwB,EAAEC,IAAI,CAAC,CAAC,EAAEb,OAAO,CAACc,eAAe,CAAC,CAAC;IAClI,IAAI,CAACC,OAAO,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC5C,IAAI,CAACF,OAAO,CAACG,SAAS,GAAG,gBAAgB;IACzCpB,SAAS,CAACqB,WAAW,CAAC,IAAI,CAACJ,OAAO,CAAC;IACnC,IAAI,CAACK,SAAS,GAAG,IAAI,CAACf,SAAS,CAAC,IAAIpB,SAAS,CAAC,IAAI,CAAC8B,OAAO,EAAE;MACxDd,WAAW,EAAED,OAAO,CAACC,WAAW;MAChCoB,SAAS,EAAErB,OAAO,CAACqB,SAAS;MAC5BC,YAAY,EAAEtB,OAAO,CAACsB,YAAY;MAClCC,gBAAgB,EAAEvB,OAAO,CAACuB,gBAAgB;MAC1CC,qBAAqB,EAAExB,OAAO,CAACwB,qBAAqB;MACpDf,aAAa,EAAET,OAAO,CAACS,aAAa;MACpCgB,sBAAsB,EAAEA,CAACC,MAAM,EAAEC,eAAe,KAAK;QACjD,IAAID,MAAM,CAACE,EAAE,KAAKjB,gBAAgB,CAACkB,EAAE,EAAE;UACnC,IAAI,CAACjB,wBAAwB,GAAG,IAAI1B,0BAA0B,CAACwC,MAAM,EAAEA,MAAM,CAACI,WAAW,EAAE/B,mBAAmB,EAAE;YAC5G0B,sBAAsB,EAAE,IAAI,CAACzB,OAAO,CAACyB,sBAAsB;YAC3DH,YAAY,EAAE,IAAI,CAACA,YAAY;YAC/BS,kBAAkB,EAAE,IAAI,CAAC/B,OAAO,CAACgC,aAAa;YAC9CC,UAAU,EAAE3C,SAAS,CAAC4C,gBAAgB,CAAClC,OAAO,CAACmC,QAAQ,IAAI9C,OAAO,CAAC+C,WAAW,CAAC;YAC/EC,uBAAuB,EAAE,IAAI,CAACrC,OAAO,CAACqC,uBAAuB;YAC7DC,WAAW,EAAE,CAAC,CAAC,IAAI,CAACtC,OAAO,CAACuC,4BAA4B;YACxDC,aAAa,EAAE,IAAI,CAACxC,OAAO,CAACwC,aAAa;YACzCC,MAAM,EAAE,IAAI;YACZhC,aAAa,EAAE,IAAI,CAACT,OAAO,CAACS;UAChC,CAAC,CAAC;UACF,IAAI,CAACG,wBAAwB,CAAC8B,gBAAgB,CAAC,IAAI,CAACtB,SAAS,CAACuB,OAAO,CAAC;UACtE,IAAI,CAACnC,WAAW,CAACoC,GAAG,CAAC,IAAI,CAACxC,8BAA8B,CAACwC,GAAG,CAAC,IAAI,CAAChC,wBAAwB,CAACiC,qBAAqB,CAAC,CAAC;UAClH,OAAO,IAAI,CAACjC,wBAAwB;QACxC;QACA,IAAIZ,OAAO,CAACyB,sBAAsB,EAAE;UAChC,MAAMqB,MAAM,GAAG9C,OAAO,CAACyB,sBAAsB,CAACC,MAAM,EAAEC,eAAe,CAAC;UACtE,IAAImB,MAAM,EAAE;YACR,OAAOA,MAAM;UACjB;QACJ;QACA,IAAIpB,MAAM,YAAYtC,aAAa,EAAE;UACjC,MAAM0D,MAAM,GAAG,IAAI5D,0BAA0B,CAACwC,MAAM,EAAEA,MAAM,CAACqB,OAAO,EAAEhD,mBAAmB,EAAE;YACvF0B,sBAAsB,EAAE,IAAI,CAACzB,OAAO,CAACyB,sBAAsB;YAC3DH,YAAY,EAAE,IAAI,CAACA,YAAY;YAC/BS,kBAAkB,EAAE,IAAI,CAAC/B,OAAO,CAACgC,aAAa;YAC9CC,UAAU,EAAEP,MAAM,CAACsB,KAAK;YACxBX,uBAAuB,EAAE,IAAI,CAACrC,OAAO,CAACqC,uBAAuB;YAC7DC,WAAW,EAAE,CAAC,CAAC,IAAI,CAACtC,OAAO,CAACuC,4BAA4B;YACxDC,aAAa,EAAE,IAAI,CAACxC,OAAO,CAACwC,aAAa;YACzC/B,aAAa,EAAE,IAAI,CAACT,OAAO,CAACS;UAChC,CAAC,CAAC;UACFqC,MAAM,CAACJ,gBAAgB,CAAC,IAAI,CAACtB,SAAS,CAACuB,OAAO,CAAC;UAC/C,IAAI,CAACzC,sBAAsB,CAAC+C,IAAI,CAACH,MAAM,CAAC;UACxC,IAAI,CAACtC,WAAW,CAACoC,GAAG,CAAC,IAAI,CAACxC,8BAA8B,CAACwC,GAAG,CAACE,MAAM,CAACD,qBAAqB,CAAC,CAAC;UAC3F,OAAOC,MAAM;QACjB;QACA,OAAOI,SAAS;MACpB;IACJ,CAAC,CAAC,CAAC;EACP;EACA,IAAI5B,YAAYA,CAACA,YAAY,EAAE;IAC3B,IAAI,CAACF,SAAS,CAACE,YAAY,GAAGA,YAAY;EAC9C;EACA,IAAIA,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACF,SAAS,CAACE,YAAY;EACtC;EACA6B,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAACpC,OAAO;EACvB;EACAqC,aAAaA,CAACC,cAAc,EAAE;IAC1B,OAAO,IAAI,CAACjC,SAAS,CAACkC,SAAS,CAACD,cAAc,CAAC;EACnD;EACAE,UAAUA,CAACC,cAAc,EAAEC,gBAAgB,EAAE;IACzC,IAAI,CAACC,KAAK,CAAC,CAAC;IACZ,MAAMC,mBAAmB,GAAGH,cAAc,GAAGA,cAAc,CAACI,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE;IACzE;IACA,IAAI,CAACzD,mBAAmB,GAAG,CAAC,EAAEsD,gBAAgB,IAAIA,gBAAgB,CAACI,MAAM,GAAG,CAAC,CAAC;IAC9E,IAAI,IAAI,CAAC1D,mBAAmB,IAAIsD,gBAAgB,EAAE;MAC9C,IAAI,CAAC/C,gBAAgB,CAACoB,WAAW,GAAG2B,gBAAgB,CAACG,KAAK,CAAC,CAAC,CAAC;MAC7DD,mBAAmB,CAACV,IAAI,CAAC,IAAI,CAACvC,gBAAgB,CAAC;IACnD;IACAiD,mBAAmB,CAACG,OAAO,CAACpC,MAAM,IAAI;MAClC,IAAI,CAACN,SAAS,CAAC6B,IAAI,CAACvB,MAAM,EAAE;QAAEqC,IAAI,EAAE,IAAI,CAAC/D,OAAO,CAAC+D,IAAI,IAAI,IAAI;QAAEC,KAAK,EAAE,IAAI,CAAChE,OAAO,CAACgE,KAAK,IAAI,KAAK;QAAEC,UAAU,EAAE,IAAI,CAACC,kBAAkB,CAACxC,MAAM;MAAE,CAAC,CAAC;IACrJ,CAAC,CAAC;EACN;EACAwC,kBAAkBA,CAACxC,MAAM,EAAE;IACvB,MAAMyC,GAAG,GAAG,IAAI,CAACnE,OAAO,CAACgC,aAAa,GAAGN,MAAM,CAAC;IAChD,OAAOyC,GAAG,EAAEC,QAAQ,CAAC,CAAC,IAAIlB,SAAS;EACvC;EACAQ,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACxD,sBAAsB,GAAG,EAAE;IAChC,IAAI,CAACM,WAAW,CAACkD,KAAK,CAAC,CAAC;IACxB,IAAI,CAACtC,SAAS,CAACsC,KAAK,CAAC,CAAC;EAC1B;EACAW,OAAOA,CAAA,EAAG;IACN,IAAI,CAACX,KAAK,CAAC,CAAC;IACZ,IAAI,CAAClD,WAAW,CAAC6D,OAAO,CAAC,CAAC;IAC1B,KAAK,CAACA,OAAO,CAAC,CAAC;EACnB;AACJ;AACA,WAAa1D,gBAAgB;EAAtB,MAAMA,gBAAgB,SAASxB,MAAM,CAAC;IACzC;MAAS,IAAI,CAAC0C,EAAE,GAAG,qBAAqB;IAAE;IAC1ChC,WAAWA,CAACyE,kBAAkB,EAAEC,KAAK,EAAE;MACnCA,KAAK,GAAGA,KAAK,IAAI7E,GAAG,CAAC8E,QAAQ,CAAC,aAAa,EAAE,iBAAiB,CAAC;MAC/D,KAAK,CAAC7D,gBAAgB,CAACkB,EAAE,EAAE0C,KAAK,EAAErB,SAAS,EAAE,IAAI,CAAC;MAClD,IAAI,CAACuB,YAAY,GAAG,EAAE;MACtB,IAAI,CAACH,kBAAkB,GAAGA,kBAAkB;IAChD;IACMI,GAAGA,CAAA,EAAG;MAAA,IAAAC,KAAA;MAAA,OAAAC,iBAAA;QACRD,KAAI,CAACL,kBAAkB,CAAC,CAAC;MAAC;IAC9B;IACA,IAAIxC,WAAWA,CAAA,EAAG;MACd,OAAO,IAAI,CAAC2C,YAAY;IAC5B;IACA,IAAI3C,WAAWA,CAACiB,OAAO,EAAE;MACrB,IAAI,CAAC0B,YAAY,GAAG1B,OAAO;IAC/B;EACJ;EAAC,OAjBYpC,gBAAgB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}