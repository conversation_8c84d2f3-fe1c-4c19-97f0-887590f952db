{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport var ObjectTreeElementCollapseState = /*#__PURE__*/function (ObjectTreeElementCollapseState) {\n  ObjectTreeElementCollapseState[ObjectTreeElementCollapseState[\"Expanded\"] = 0] = \"Expanded\";\n  ObjectTreeElementCollapseState[ObjectTreeElementCollapseState[\"Collapsed\"] = 1] = \"Collapsed\";\n  /**\n   * If the element is already in the tree, preserve its current state. Else, expand it.\n   */\n  ObjectTreeElementCollapseState[ObjectTreeElementCollapseState[\"PreserveOrExpanded\"] = 2] = \"PreserveOrExpanded\";\n  /**\n   * If the element is already in the tree, preserve its current state. Else, collapse it.\n   */\n  ObjectTreeElementCollapseState[ObjectTreeElementCollapseState[\"PreserveOrCollapsed\"] = 3] = \"PreserveOrCollapsed\";\n  return ObjectTreeElementCollapseState;\n}(ObjectTreeElementCollapseState || {});\nexport var TreeMouseEventTarget = /*#__PURE__*/function (TreeMouseEventTarget) {\n  TreeMouseEventTarget[TreeMouseEventTarget[\"Unknown\"] = 0] = \"Unknown\";\n  TreeMouseEventTarget[TreeMouseEventTarget[\"Twistie\"] = 1] = \"Twistie\";\n  TreeMouseEventTarget[TreeMouseEventTarget[\"Element\"] = 2] = \"Element\";\n  TreeMouseEventTarget[TreeMouseEventTarget[\"Filter\"] = 3] = \"Filter\";\n  return TreeMouseEventTarget;\n}(TreeMouseEventTarget || {});\nexport class TreeError extends Error {\n  constructor(user, message) {\n    super(`TreeError [${user}] ${message}`);\n  }\n}\nexport class WeakMapper {\n  constructor(fn) {\n    this.fn = fn;\n    this._map = new WeakMap();\n  }\n  map(key) {\n    let result = this._map.get(key);\n    if (!result) {\n      result = this.fn(key);\n      this._map.set(key, result);\n    }\n    return result;\n  }\n}", "map": {"version": 3, "names": ["ObjectTreeElementCollapseState", "TreeMouseEventTarget", "TreeError", "Error", "constructor", "user", "message", "WeakMapper", "fn", "_map", "WeakMap", "map", "key", "result", "get", "set"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/browser/ui/tree/tree.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport var ObjectTreeElementCollapseState;\n(function (ObjectTreeElementCollapseState) {\n    ObjectTreeElementCollapseState[ObjectTreeElementCollapseState[\"Expanded\"] = 0] = \"Expanded\";\n    ObjectTreeElementCollapseState[ObjectTreeElementCollapseState[\"Collapsed\"] = 1] = \"Collapsed\";\n    /**\n     * If the element is already in the tree, preserve its current state. Else, expand it.\n     */\n    ObjectTreeElementCollapseState[ObjectTreeElementCollapseState[\"PreserveOrExpanded\"] = 2] = \"PreserveOrExpanded\";\n    /**\n     * If the element is already in the tree, preserve its current state. Else, collapse it.\n     */\n    ObjectTreeElementCollapseState[ObjectTreeElementCollapseState[\"PreserveOrCollapsed\"] = 3] = \"PreserveOrCollapsed\";\n})(ObjectTreeElementCollapseState || (ObjectTreeElementCollapseState = {}));\nexport var TreeMouseEventTarget;\n(function (TreeMouseEventTarget) {\n    TreeMouseEventTarget[TreeMouseEventTarget[\"Unknown\"] = 0] = \"Unknown\";\n    TreeMouseEventTarget[TreeMouseEventTarget[\"Twistie\"] = 1] = \"Twistie\";\n    TreeMouseEventTarget[TreeMouseEventTarget[\"Element\"] = 2] = \"Element\";\n    TreeMouseEventTarget[TreeMouseEventTarget[\"Filter\"] = 3] = \"Filter\";\n})(TreeMouseEventTarget || (TreeMouseEventTarget = {}));\nexport class TreeError extends Error {\n    constructor(user, message) {\n        super(`TreeError [${user}] ${message}`);\n    }\n}\nexport class WeakMapper {\n    constructor(fn) {\n        this.fn = fn;\n        this._map = new WeakMap();\n    }\n    map(key) {\n        let result = this._map.get(key);\n        if (!result) {\n            result = this.fn(key);\n            this._map.set(key, result);\n        }\n        return result;\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,IAAIA,8BAA8B,gBACxC,UAAUA,8BAA8B,EAAE;EACvCA,8BAA8B,CAACA,8BAA8B,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU;EAC3FA,8BAA8B,CAACA,8BAA8B,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW;EAC7F;AACJ;AACA;EACIA,8BAA8B,CAACA,8BAA8B,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC,GAAG,oBAAoB;EAC/G;AACJ;AACA;EACIA,8BAA8B,CAACA,8BAA8B,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC,GAAG,qBAAqB;EAAC,OAV3GA,8BAA8B;AAWzC,CAAC,CAAEA,8BAA8B,IAAsC,CAAC,CAAE,CAZjC;AAazC,OAAO,IAAIC,oBAAoB,gBAC9B,UAAUA,oBAAoB,EAAE;EAC7BA,oBAAoB,CAACA,oBAAoB,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS;EACrEA,oBAAoB,CAACA,oBAAoB,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS;EACrEA,oBAAoB,CAACA,oBAAoB,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS;EACrEA,oBAAoB,CAACA,oBAAoB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ;EAAC,OAJ7DA,oBAAoB;AAK/B,CAAC,CAAEA,oBAAoB,IAA4B,CAAC,CAAE,CANvB;AAO/B,OAAO,MAAMC,SAAS,SAASC,KAAK,CAAC;EACjCC,WAAWA,CAACC,IAAI,EAAEC,OAAO,EAAE;IACvB,KAAK,CAAC,cAAcD,IAAI,KAAKC,OAAO,EAAE,CAAC;EAC3C;AACJ;AACA,OAAO,MAAMC,UAAU,CAAC;EACpBH,WAAWA,CAACI,EAAE,EAAE;IACZ,IAAI,CAACA,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,IAAI,GAAG,IAAIC,OAAO,CAAC,CAAC;EAC7B;EACAC,GAAGA,CAACC,GAAG,EAAE;IACL,IAAIC,MAAM,GAAG,IAAI,CAACJ,IAAI,CAACK,GAAG,CAACF,GAAG,CAAC;IAC/B,IAAI,CAACC,MAAM,EAAE;MACTA,MAAM,GAAG,IAAI,CAACL,EAAE,CAACI,GAAG,CAAC;MACrB,IAAI,CAACH,IAAI,CAACM,GAAG,CAACH,GAAG,EAAEC,MAAM,CAAC;IAC9B;IACA,OAAOA,MAAM;EACjB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}