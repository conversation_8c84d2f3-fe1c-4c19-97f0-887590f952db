{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { getZoomFactor, isChrome } from '../../browser.js';\nimport * as dom from '../../dom.js';\nimport { createFastDomNode } from '../../fastDomNode.js';\nimport { StandardWheelEvent } from '../../mouseEvent.js';\nimport { HorizontalScrollbar } from './horizontalScrollbar.js';\nimport { VerticalScrollbar } from './verticalScrollbar.js';\nimport { Widget } from '../widget.js';\nimport { TimeoutTimer } from '../../../common/async.js';\nimport { Emitter } from '../../../common/event.js';\nimport { dispose } from '../../../common/lifecycle.js';\nimport * as platform from '../../../common/platform.js';\nimport { Scrollable } from '../../../common/scrollable.js';\nimport './media/scrollbars.css';\nconst HIDE_TIMEOUT = 500;\nconst SCROLL_WHEEL_SENSITIVITY = 50;\nconst SCROLL_WHEEL_SMOOTH_SCROLL_ENABLED = true;\nclass MouseWheelClassifierItem {\n  constructor(timestamp, deltaX, deltaY) {\n    this.timestamp = timestamp;\n    this.deltaX = deltaX;\n    this.deltaY = deltaY;\n    this.score = 0;\n  }\n}\nexport class MouseWheelClassifier {\n  static {\n    this.INSTANCE = new MouseWheelClassifier();\n  }\n  constructor() {\n    this._capacity = 5;\n    this._memory = [];\n    this._front = -1;\n    this._rear = -1;\n  }\n  isPhysicalMouseWheel() {\n    if (this._front === -1 && this._rear === -1) {\n      // no elements\n      return false;\n    }\n    // 0.5 * last + 0.25 * 2nd last + 0.125 * 3rd last + ...\n    let remainingInfluence = 1;\n    let score = 0;\n    let iteration = 1;\n    let index = this._rear;\n    do {\n      const influence = index === this._front ? remainingInfluence : Math.pow(2, -iteration);\n      remainingInfluence -= influence;\n      score += this._memory[index].score * influence;\n      if (index === this._front) {\n        break;\n      }\n      index = (this._capacity + index - 1) % this._capacity;\n      iteration++;\n    } while (true);\n    return score <= 0.5;\n  }\n  acceptStandardWheelEvent(e) {\n    if (isChrome) {\n      const targetWindow = dom.getWindow(e.browserEvent);\n      const pageZoomFactor = getZoomFactor(targetWindow);\n      // On Chrome, the incoming delta events are multiplied with the OS zoom factor.\n      // The OS zoom factor can be reverse engineered by using the device pixel ratio and the configured zoom factor into account.\n      this.accept(Date.now(), e.deltaX * pageZoomFactor, e.deltaY * pageZoomFactor);\n    } else {\n      this.accept(Date.now(), e.deltaX, e.deltaY);\n    }\n  }\n  accept(timestamp, deltaX, deltaY) {\n    let previousItem = null;\n    const item = new MouseWheelClassifierItem(timestamp, deltaX, deltaY);\n    if (this._front === -1 && this._rear === -1) {\n      this._memory[0] = item;\n      this._front = 0;\n      this._rear = 0;\n    } else {\n      previousItem = this._memory[this._rear];\n      this._rear = (this._rear + 1) % this._capacity;\n      if (this._rear === this._front) {\n        // Drop oldest\n        this._front = (this._front + 1) % this._capacity;\n      }\n      this._memory[this._rear] = item;\n    }\n    item.score = this._computeScore(item, previousItem);\n  }\n  /**\n   * A score between 0 and 1 for `item`.\n   *  - a score towards 0 indicates that the source appears to be a physical mouse wheel\n   *  - a score towards 1 indicates that the source appears to be a touchpad or magic mouse, etc.\n   */\n  _computeScore(item, previousItem) {\n    if (Math.abs(item.deltaX) > 0 && Math.abs(item.deltaY) > 0) {\n      // both axes exercised => definitely not a physical mouse wheel\n      return 1;\n    }\n    let score = 0.5;\n    if (!this._isAlmostInt(item.deltaX) || !this._isAlmostInt(item.deltaY)) {\n      // non-integer deltas => indicator that this is not a physical mouse wheel\n      score += 0.25;\n    }\n    // Non-accelerating scroll => indicator that this is a physical mouse wheel\n    // These can be identified by seeing whether they are the module of one another.\n    if (previousItem) {\n      const absDeltaX = Math.abs(item.deltaX);\n      const absDeltaY = Math.abs(item.deltaY);\n      const absPreviousDeltaX = Math.abs(previousItem.deltaX);\n      const absPreviousDeltaY = Math.abs(previousItem.deltaY);\n      // Min 1 to avoid division by zero, module 1 will still be 0.\n      const minDeltaX = Math.max(Math.min(absDeltaX, absPreviousDeltaX), 1);\n      const minDeltaY = Math.max(Math.min(absDeltaY, absPreviousDeltaY), 1);\n      const maxDeltaX = Math.max(absDeltaX, absPreviousDeltaX);\n      const maxDeltaY = Math.max(absDeltaY, absPreviousDeltaY);\n      const isSameModulo = maxDeltaX % minDeltaX === 0 && maxDeltaY % minDeltaY === 0;\n      if (isSameModulo) {\n        score -= 0.5;\n      }\n    }\n    return Math.min(Math.max(score, 0), 1);\n  }\n  _isAlmostInt(value) {\n    const delta = Math.abs(Math.round(value) - value);\n    return delta < 0.01;\n  }\n}\nexport class AbstractScrollableElement extends Widget {\n  get options() {\n    return this._options;\n  }\n  constructor(element, options, scrollable) {\n    super();\n    this._onScroll = this._register(new Emitter());\n    this.onScroll = this._onScroll.event;\n    this._onWillScroll = this._register(new Emitter());\n    element.style.overflow = 'hidden';\n    this._options = resolveOptions(options);\n    this._scrollable = scrollable;\n    this._register(this._scrollable.onScroll(e => {\n      this._onWillScroll.fire(e);\n      this._onDidScroll(e);\n      this._onScroll.fire(e);\n    }));\n    const scrollbarHost = {\n      onMouseWheel: mouseWheelEvent => this._onMouseWheel(mouseWheelEvent),\n      onDragStart: () => this._onDragStart(),\n      onDragEnd: () => this._onDragEnd()\n    };\n    this._verticalScrollbar = this._register(new VerticalScrollbar(this._scrollable, this._options, scrollbarHost));\n    this._horizontalScrollbar = this._register(new HorizontalScrollbar(this._scrollable, this._options, scrollbarHost));\n    this._domNode = document.createElement('div');\n    this._domNode.className = 'monaco-scrollable-element ' + this._options.className;\n    this._domNode.setAttribute('role', 'presentation');\n    this._domNode.style.position = 'relative';\n    this._domNode.style.overflow = 'hidden';\n    this._domNode.appendChild(element);\n    this._domNode.appendChild(this._horizontalScrollbar.domNode.domNode);\n    this._domNode.appendChild(this._verticalScrollbar.domNode.domNode);\n    if (this._options.useShadows) {\n      this._leftShadowDomNode = createFastDomNode(document.createElement('div'));\n      this._leftShadowDomNode.setClassName('shadow');\n      this._domNode.appendChild(this._leftShadowDomNode.domNode);\n      this._topShadowDomNode = createFastDomNode(document.createElement('div'));\n      this._topShadowDomNode.setClassName('shadow');\n      this._domNode.appendChild(this._topShadowDomNode.domNode);\n      this._topLeftShadowDomNode = createFastDomNode(document.createElement('div'));\n      this._topLeftShadowDomNode.setClassName('shadow');\n      this._domNode.appendChild(this._topLeftShadowDomNode.domNode);\n    } else {\n      this._leftShadowDomNode = null;\n      this._topShadowDomNode = null;\n      this._topLeftShadowDomNode = null;\n    }\n    this._listenOnDomNode = this._options.listenOnDomNode || this._domNode;\n    this._mouseWheelToDispose = [];\n    this._setListeningToMouseWheel(this._options.handleMouseWheel);\n    this.onmouseover(this._listenOnDomNode, e => this._onMouseOver(e));\n    this.onmouseleave(this._listenOnDomNode, e => this._onMouseLeave(e));\n    this._hideTimeout = this._register(new TimeoutTimer());\n    this._isDragging = false;\n    this._mouseIsOver = false;\n    this._shouldRender = true;\n    this._revealOnScroll = true;\n  }\n  dispose() {\n    this._mouseWheelToDispose = dispose(this._mouseWheelToDispose);\n    super.dispose();\n  }\n  /**\n   * Get the generated 'scrollable' dom node\n   */\n  getDomNode() {\n    return this._domNode;\n  }\n  getOverviewRulerLayoutInfo() {\n    return {\n      parent: this._domNode,\n      insertBefore: this._verticalScrollbar.domNode.domNode\n    };\n  }\n  /**\n   * Delegate a pointer down event to the vertical scrollbar.\n   * This is to help with clicking somewhere else and having the scrollbar react.\n   */\n  delegateVerticalScrollbarPointerDown(browserEvent) {\n    this._verticalScrollbar.delegatePointerDown(browserEvent);\n  }\n  getScrollDimensions() {\n    return this._scrollable.getScrollDimensions();\n  }\n  setScrollDimensions(dimensions) {\n    this._scrollable.setScrollDimensions(dimensions, false);\n  }\n  /**\n   * Update the class name of the scrollable element.\n   */\n  updateClassName(newClassName) {\n    this._options.className = newClassName;\n    // Defaults are different on Macs\n    if (platform.isMacintosh) {\n      this._options.className += ' mac';\n    }\n    this._domNode.className = 'monaco-scrollable-element ' + this._options.className;\n  }\n  /**\n   * Update configuration options for the scrollbar.\n   */\n  updateOptions(newOptions) {\n    if (typeof newOptions.handleMouseWheel !== 'undefined') {\n      this._options.handleMouseWheel = newOptions.handleMouseWheel;\n      this._setListeningToMouseWheel(this._options.handleMouseWheel);\n    }\n    if (typeof newOptions.mouseWheelScrollSensitivity !== 'undefined') {\n      this._options.mouseWheelScrollSensitivity = newOptions.mouseWheelScrollSensitivity;\n    }\n    if (typeof newOptions.fastScrollSensitivity !== 'undefined') {\n      this._options.fastScrollSensitivity = newOptions.fastScrollSensitivity;\n    }\n    if (typeof newOptions.scrollPredominantAxis !== 'undefined') {\n      this._options.scrollPredominantAxis = newOptions.scrollPredominantAxis;\n    }\n    if (typeof newOptions.horizontal !== 'undefined') {\n      this._options.horizontal = newOptions.horizontal;\n    }\n    if (typeof newOptions.vertical !== 'undefined') {\n      this._options.vertical = newOptions.vertical;\n    }\n    if (typeof newOptions.horizontalScrollbarSize !== 'undefined') {\n      this._options.horizontalScrollbarSize = newOptions.horizontalScrollbarSize;\n    }\n    if (typeof newOptions.verticalScrollbarSize !== 'undefined') {\n      this._options.verticalScrollbarSize = newOptions.verticalScrollbarSize;\n    }\n    if (typeof newOptions.scrollByPage !== 'undefined') {\n      this._options.scrollByPage = newOptions.scrollByPage;\n    }\n    this._horizontalScrollbar.updateOptions(this._options);\n    this._verticalScrollbar.updateOptions(this._options);\n    if (!this._options.lazyRender) {\n      this._render();\n    }\n  }\n  delegateScrollFromMouseWheelEvent(browserEvent) {\n    this._onMouseWheel(new StandardWheelEvent(browserEvent));\n  }\n  // -------------------- mouse wheel scrolling --------------------\n  _setListeningToMouseWheel(shouldListen) {\n    const isListening = this._mouseWheelToDispose.length > 0;\n    if (isListening === shouldListen) {\n      // No change\n      return;\n    }\n    // Stop listening (if necessary)\n    this._mouseWheelToDispose = dispose(this._mouseWheelToDispose);\n    // Start listening (if necessary)\n    if (shouldListen) {\n      const onMouseWheel = browserEvent => {\n        this._onMouseWheel(new StandardWheelEvent(browserEvent));\n      };\n      this._mouseWheelToDispose.push(dom.addDisposableListener(this._listenOnDomNode, dom.EventType.MOUSE_WHEEL, onMouseWheel, {\n        passive: false\n      }));\n    }\n  }\n  _onMouseWheel(e) {\n    if (e.browserEvent?.defaultPrevented) {\n      return;\n    }\n    const classifier = MouseWheelClassifier.INSTANCE;\n    if (SCROLL_WHEEL_SMOOTH_SCROLL_ENABLED) {\n      classifier.acceptStandardWheelEvent(e);\n    }\n    // useful for creating unit tests:\n    // console.log(`${Date.now()}, ${e.deltaY}, ${e.deltaX}`);\n    let didScroll = false;\n    if (e.deltaY || e.deltaX) {\n      let deltaY = e.deltaY * this._options.mouseWheelScrollSensitivity;\n      let deltaX = e.deltaX * this._options.mouseWheelScrollSensitivity;\n      if (this._options.scrollPredominantAxis) {\n        if (this._options.scrollYToX && deltaX + deltaY === 0) {\n          // when configured to map Y to X and we both see\n          // no dominant axis and X and Y are competing with\n          // identical values into opposite directions, we\n          // ignore the delta as we cannot make a decision then\n          deltaX = deltaY = 0;\n        } else if (Math.abs(deltaY) >= Math.abs(deltaX)) {\n          deltaX = 0;\n        } else {\n          deltaY = 0;\n        }\n      }\n      if (this._options.flipAxes) {\n        [deltaY, deltaX] = [deltaX, deltaY];\n      }\n      // Convert vertical scrolling to horizontal if shift is held, this\n      // is handled at a higher level on Mac\n      const shiftConvert = !platform.isMacintosh && e.browserEvent && e.browserEvent.shiftKey;\n      if ((this._options.scrollYToX || shiftConvert) && !deltaX) {\n        deltaX = deltaY;\n        deltaY = 0;\n      }\n      if (e.browserEvent && e.browserEvent.altKey) {\n        // fastScrolling\n        deltaX = deltaX * this._options.fastScrollSensitivity;\n        deltaY = deltaY * this._options.fastScrollSensitivity;\n      }\n      const futureScrollPosition = this._scrollable.getFutureScrollPosition();\n      let desiredScrollPosition = {};\n      if (deltaY) {\n        const deltaScrollTop = SCROLL_WHEEL_SENSITIVITY * deltaY;\n        // Here we convert values such as -0.3 to -1 or 0.3 to 1, otherwise low speed scrolling will never scroll\n        const desiredScrollTop = futureScrollPosition.scrollTop - (deltaScrollTop < 0 ? Math.floor(deltaScrollTop) : Math.ceil(deltaScrollTop));\n        this._verticalScrollbar.writeScrollPosition(desiredScrollPosition, desiredScrollTop);\n      }\n      if (deltaX) {\n        const deltaScrollLeft = SCROLL_WHEEL_SENSITIVITY * deltaX;\n        // Here we convert values such as -0.3 to -1 or 0.3 to 1, otherwise low speed scrolling will never scroll\n        const desiredScrollLeft = futureScrollPosition.scrollLeft - (deltaScrollLeft < 0 ? Math.floor(deltaScrollLeft) : Math.ceil(deltaScrollLeft));\n        this._horizontalScrollbar.writeScrollPosition(desiredScrollPosition, desiredScrollLeft);\n      }\n      // Check that we are scrolling towards a location which is valid\n      desiredScrollPosition = this._scrollable.validateScrollPosition(desiredScrollPosition);\n      if (futureScrollPosition.scrollLeft !== desiredScrollPosition.scrollLeft || futureScrollPosition.scrollTop !== desiredScrollPosition.scrollTop) {\n        const canPerformSmoothScroll = SCROLL_WHEEL_SMOOTH_SCROLL_ENABLED && this._options.mouseWheelSmoothScroll && classifier.isPhysicalMouseWheel();\n        if (canPerformSmoothScroll) {\n          this._scrollable.setScrollPositionSmooth(desiredScrollPosition);\n        } else {\n          this._scrollable.setScrollPositionNow(desiredScrollPosition);\n        }\n        didScroll = true;\n      }\n    }\n    let consumeMouseWheel = didScroll;\n    if (!consumeMouseWheel && this._options.alwaysConsumeMouseWheel) {\n      consumeMouseWheel = true;\n    }\n    if (!consumeMouseWheel && this._options.consumeMouseWheelIfScrollbarIsNeeded && (this._verticalScrollbar.isNeeded() || this._horizontalScrollbar.isNeeded())) {\n      consumeMouseWheel = true;\n    }\n    if (consumeMouseWheel) {\n      e.preventDefault();\n      e.stopPropagation();\n    }\n  }\n  _onDidScroll(e) {\n    this._shouldRender = this._horizontalScrollbar.onDidScroll(e) || this._shouldRender;\n    this._shouldRender = this._verticalScrollbar.onDidScroll(e) || this._shouldRender;\n    if (this._options.useShadows) {\n      this._shouldRender = true;\n    }\n    if (this._revealOnScroll) {\n      this._reveal();\n    }\n    if (!this._options.lazyRender) {\n      this._render();\n    }\n  }\n  /**\n   * Render / mutate the DOM now.\n   * Should be used together with the ctor option `lazyRender`.\n   */\n  renderNow() {\n    if (!this._options.lazyRender) {\n      throw new Error('Please use `lazyRender` together with `renderNow`!');\n    }\n    this._render();\n  }\n  _render() {\n    if (!this._shouldRender) {\n      return;\n    }\n    this._shouldRender = false;\n    this._horizontalScrollbar.render();\n    this._verticalScrollbar.render();\n    if (this._options.useShadows) {\n      const scrollState = this._scrollable.getCurrentScrollPosition();\n      const enableTop = scrollState.scrollTop > 0;\n      const enableLeft = scrollState.scrollLeft > 0;\n      const leftClassName = enableLeft ? ' left' : '';\n      const topClassName = enableTop ? ' top' : '';\n      const topLeftClassName = enableLeft || enableTop ? ' top-left-corner' : '';\n      this._leftShadowDomNode.setClassName(`shadow${leftClassName}`);\n      this._topShadowDomNode.setClassName(`shadow${topClassName}`);\n      this._topLeftShadowDomNode.setClassName(`shadow${topLeftClassName}${topClassName}${leftClassName}`);\n    }\n  }\n  // -------------------- fade in / fade out --------------------\n  _onDragStart() {\n    this._isDragging = true;\n    this._reveal();\n  }\n  _onDragEnd() {\n    this._isDragging = false;\n    this._hide();\n  }\n  _onMouseLeave(e) {\n    this._mouseIsOver = false;\n    this._hide();\n  }\n  _onMouseOver(e) {\n    this._mouseIsOver = true;\n    this._reveal();\n  }\n  _reveal() {\n    this._verticalScrollbar.beginReveal();\n    this._horizontalScrollbar.beginReveal();\n    this._scheduleHide();\n  }\n  _hide() {\n    if (!this._mouseIsOver && !this._isDragging) {\n      this._verticalScrollbar.beginHide();\n      this._horizontalScrollbar.beginHide();\n    }\n  }\n  _scheduleHide() {\n    if (!this._mouseIsOver && !this._isDragging) {\n      this._hideTimeout.cancelAndSet(() => this._hide(), HIDE_TIMEOUT);\n    }\n  }\n}\nexport class ScrollableElement extends AbstractScrollableElement {\n  constructor(element, options) {\n    options = options || {};\n    options.mouseWheelSmoothScroll = false;\n    const scrollable = new Scrollable({\n      forceIntegerValues: true,\n      smoothScrollDuration: 0,\n      scheduleAtNextAnimationFrame: callback => dom.scheduleAtNextAnimationFrame(dom.getWindow(element), callback)\n    });\n    super(element, options, scrollable);\n    this._register(scrollable);\n  }\n  setScrollPosition(update) {\n    this._scrollable.setScrollPositionNow(update);\n  }\n}\nexport class SmoothScrollableElement extends AbstractScrollableElement {\n  constructor(element, options, scrollable) {\n    super(element, options, scrollable);\n  }\n  setScrollPosition(update) {\n    if (update.reuseAnimation) {\n      this._scrollable.setScrollPositionSmooth(update, update.reuseAnimation);\n    } else {\n      this._scrollable.setScrollPositionNow(update);\n    }\n  }\n  getScrollPosition() {\n    return this._scrollable.getCurrentScrollPosition();\n  }\n}\nexport class DomScrollableElement extends AbstractScrollableElement {\n  constructor(element, options) {\n    options = options || {};\n    options.mouseWheelSmoothScroll = false;\n    const scrollable = new Scrollable({\n      forceIntegerValues: false,\n      // See https://github.com/microsoft/vscode/issues/139877\n      smoothScrollDuration: 0,\n      scheduleAtNextAnimationFrame: callback => dom.scheduleAtNextAnimationFrame(dom.getWindow(element), callback)\n    });\n    super(element, options, scrollable);\n    this._register(scrollable);\n    this._element = element;\n    this._register(this.onScroll(e => {\n      if (e.scrollTopChanged) {\n        this._element.scrollTop = e.scrollTop;\n      }\n      if (e.scrollLeftChanged) {\n        this._element.scrollLeft = e.scrollLeft;\n      }\n    }));\n    this.scanDomNode();\n  }\n  setScrollPosition(update) {\n    this._scrollable.setScrollPositionNow(update);\n  }\n  getScrollPosition() {\n    return this._scrollable.getCurrentScrollPosition();\n  }\n  scanDomNode() {\n    // width, scrollLeft, scrollWidth, height, scrollTop, scrollHeight\n    this.setScrollDimensions({\n      width: this._element.clientWidth,\n      scrollWidth: this._element.scrollWidth,\n      height: this._element.clientHeight,\n      scrollHeight: this._element.scrollHeight\n    });\n    this.setScrollPosition({\n      scrollLeft: this._element.scrollLeft,\n      scrollTop: this._element.scrollTop\n    });\n  }\n}\nfunction resolveOptions(opts) {\n  const result = {\n    lazyRender: typeof opts.lazyRender !== 'undefined' ? opts.lazyRender : false,\n    className: typeof opts.className !== 'undefined' ? opts.className : '',\n    useShadows: typeof opts.useShadows !== 'undefined' ? opts.useShadows : true,\n    handleMouseWheel: typeof opts.handleMouseWheel !== 'undefined' ? opts.handleMouseWheel : true,\n    flipAxes: typeof opts.flipAxes !== 'undefined' ? opts.flipAxes : false,\n    consumeMouseWheelIfScrollbarIsNeeded: typeof opts.consumeMouseWheelIfScrollbarIsNeeded !== 'undefined' ? opts.consumeMouseWheelIfScrollbarIsNeeded : false,\n    alwaysConsumeMouseWheel: typeof opts.alwaysConsumeMouseWheel !== 'undefined' ? opts.alwaysConsumeMouseWheel : false,\n    scrollYToX: typeof opts.scrollYToX !== 'undefined' ? opts.scrollYToX : false,\n    mouseWheelScrollSensitivity: typeof opts.mouseWheelScrollSensitivity !== 'undefined' ? opts.mouseWheelScrollSensitivity : 1,\n    fastScrollSensitivity: typeof opts.fastScrollSensitivity !== 'undefined' ? opts.fastScrollSensitivity : 5,\n    scrollPredominantAxis: typeof opts.scrollPredominantAxis !== 'undefined' ? opts.scrollPredominantAxis : true,\n    mouseWheelSmoothScroll: typeof opts.mouseWheelSmoothScroll !== 'undefined' ? opts.mouseWheelSmoothScroll : true,\n    arrowSize: typeof opts.arrowSize !== 'undefined' ? opts.arrowSize : 11,\n    listenOnDomNode: typeof opts.listenOnDomNode !== 'undefined' ? opts.listenOnDomNode : null,\n    horizontal: typeof opts.horizontal !== 'undefined' ? opts.horizontal : 1 /* ScrollbarVisibility.Auto */,\n    horizontalScrollbarSize: typeof opts.horizontalScrollbarSize !== 'undefined' ? opts.horizontalScrollbarSize : 10,\n    horizontalSliderSize: typeof opts.horizontalSliderSize !== 'undefined' ? opts.horizontalSliderSize : 0,\n    horizontalHasArrows: typeof opts.horizontalHasArrows !== 'undefined' ? opts.horizontalHasArrows : false,\n    vertical: typeof opts.vertical !== 'undefined' ? opts.vertical : 1 /* ScrollbarVisibility.Auto */,\n    verticalScrollbarSize: typeof opts.verticalScrollbarSize !== 'undefined' ? opts.verticalScrollbarSize : 10,\n    verticalHasArrows: typeof opts.verticalHasArrows !== 'undefined' ? opts.verticalHasArrows : false,\n    verticalSliderSize: typeof opts.verticalSliderSize !== 'undefined' ? opts.verticalSliderSize : 0,\n    scrollByPage: typeof opts.scrollByPage !== 'undefined' ? opts.scrollByPage : false\n  };\n  result.horizontalSliderSize = typeof opts.horizontalSliderSize !== 'undefined' ? opts.horizontalSliderSize : result.horizontalScrollbarSize;\n  result.verticalSliderSize = typeof opts.verticalSliderSize !== 'undefined' ? opts.verticalSliderSize : result.verticalScrollbarSize;\n  // Defaults are different on Macs\n  if (platform.isMacintosh) {\n    result.className += ' mac';\n  }\n  return result;\n}", "map": {"version": 3, "names": ["getZoomFactor", "isChrome", "dom", "createFastDomNode", "StandardWheelEvent", "HorizontalScrollbar", "VerticalScrollbar", "Widget", "TimeoutTimer", "Emitter", "dispose", "platform", "Scrollable", "HIDE_TIMEOUT", "SCROLL_WHEEL_SENSITIVITY", "SCROLL_WHEEL_SMOOTH_SCROLL_ENABLED", "MouseWheelClassifierItem", "constructor", "timestamp", "deltaX", "deltaY", "score", "MouseWheelClassifier", "INSTANCE", "_capacity", "_memory", "_front", "_rear", "isPhysicalMouseWheel", "remainingInfluence", "iteration", "index", "influence", "Math", "pow", "acceptStandardWheelEvent", "e", "targetWindow", "getWindow", "browserEvent", "pageZoomFactor", "accept", "Date", "now", "previousItem", "item", "_computeScore", "abs", "_isAlmostInt", "absDeltaX", "absDeltaY", "absPreviousDeltaX", "absPreviousDeltaY", "minDeltaX", "max", "min", "minDeltaY", "maxDeltaX", "maxDeltaY", "isSameModulo", "value", "delta", "round", "AbstractScrollableElement", "options", "_options", "element", "scrollable", "_onScroll", "_register", "onScroll", "event", "_onWillScroll", "style", "overflow", "resolveOptions", "_scrollable", "fire", "_onDidScroll", "scrollbarHost", "onMouseWheel", "mouseWheelEvent", "_onMouseWheel", "onDragStart", "_onDragStart", "onDragEnd", "_onDragEnd", "_verticalScrollbar", "_horizontalScrollbar", "_domNode", "document", "createElement", "className", "setAttribute", "position", "append<PERSON><PERSON><PERSON>", "domNode", "useShadows", "_leftShadowDomNode", "setClassName", "_topShadowDomNode", "_topLeftShadowDomNode", "_listenOnDomNode", "listenOnDomNode", "_mouseWheelToDispose", "_setListeningToMouseWheel", "handleMouseWheel", "on<PERSON><PERSON>ver", "_onMouseOver", "onmouseleave", "_onMouseLeave", "_hideTimeout", "_isDragging", "_mouseIsOver", "_shouldRender", "_revealOnScroll", "getDomNode", "getOverviewRulerLayoutInfo", "parent", "insertBefore", "delegateVerticalScrollbarPointerDown", "delegatePointerDown", "getScrollDimensions", "setScrollDimensions", "dimensions", "updateClassName", "newClassName", "isMacintosh", "updateOptions", "newOptions", "mouseWheelScrollSensitivity", "fastScrollSensitivity", "scrollPredominantAxis", "horizontal", "vertical", "horizontalScrollbarSize", "verticalScrollbarSize", "scrollByPage", "lazy<PERSON>ender", "_render", "delegateScrollFromMouseWheelEvent", "<PERSON><PERSON><PERSON><PERSON>", "isListening", "length", "push", "addDisposableListener", "EventType", "MOUSE_WHEEL", "passive", "defaultPrevented", "classifier", "didScroll", "scrollYToX", "flipAxes", "shiftConvert", "shift<PERSON>ey", "altKey", "futureScrollPosition", "getFutureScrollPosition", "desiredScrollPosition", "deltaScrollTop", "desiredScrollTop", "scrollTop", "floor", "ceil", "writeScrollPosition", "deltaScrollLeft", "desiredScrollLeft", "scrollLeft", "validateScrollPosition", "canPerformSmoothScroll", "mouseWheelSmoothScroll", "setScrollPositionSmooth", "setScrollPositionNow", "consumeMouseWheel", "alwaysConsumeMouseWheel", "consumeMouseWheelIfScrollbarIsNeeded", "isNeeded", "preventDefault", "stopPropagation", "onDidScroll", "_reveal", "renderNow", "Error", "render", "scrollState", "getCurrentScrollPosition", "enableTop", "enableLeft", "leftClassName", "topClassName", "topLeftClassName", "_hide", "beginReveal", "_scheduleHide", "beginHide", "cancelAndSet", "ScrollableElement", "forceIntegerValues", "smoothScrollDuration", "scheduleAtNextAnimationFrame", "callback", "setScrollPosition", "update", "SmoothScrollableElement", "reuseAnimation", "getScrollPosition", "DomScrollableElement", "_element", "scrollTopChanged", "scrollLeftChanged", "scanDomNode", "width", "clientWidth", "scrollWidth", "height", "clientHeight", "scrollHeight", "opts", "result", "arrowSize", "horizontalSliderSize", "horizontalHasArrows", "verticalHasArrows", "verticalSliderSize"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/browser/ui/scrollbar/scrollableElement.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { getZoomFactor, isChrome } from '../../browser.js';\nimport * as dom from '../../dom.js';\nimport { createFastDomNode } from '../../fastDomNode.js';\nimport { StandardWheelEvent } from '../../mouseEvent.js';\nimport { HorizontalScrollbar } from './horizontalScrollbar.js';\nimport { VerticalScrollbar } from './verticalScrollbar.js';\nimport { Widget } from '../widget.js';\nimport { TimeoutTimer } from '../../../common/async.js';\nimport { Emitter } from '../../../common/event.js';\nimport { dispose } from '../../../common/lifecycle.js';\nimport * as platform from '../../../common/platform.js';\nimport { Scrollable } from '../../../common/scrollable.js';\nimport './media/scrollbars.css';\nconst HIDE_TIMEOUT = 500;\nconst SCROLL_WHEEL_SENSITIVITY = 50;\nconst SCROLL_WHEEL_SMOOTH_SCROLL_ENABLED = true;\nclass MouseWheelClassifierItem {\n    constructor(timestamp, deltaX, deltaY) {\n        this.timestamp = timestamp;\n        this.deltaX = deltaX;\n        this.deltaY = deltaY;\n        this.score = 0;\n    }\n}\nexport class MouseWheelClassifier {\n    static { this.INSTANCE = new MouseWheelClassifier(); }\n    constructor() {\n        this._capacity = 5;\n        this._memory = [];\n        this._front = -1;\n        this._rear = -1;\n    }\n    isPhysicalMouseWheel() {\n        if (this._front === -1 && this._rear === -1) {\n            // no elements\n            return false;\n        }\n        // 0.5 * last + 0.25 * 2nd last + 0.125 * 3rd last + ...\n        let remainingInfluence = 1;\n        let score = 0;\n        let iteration = 1;\n        let index = this._rear;\n        do {\n            const influence = (index === this._front ? remainingInfluence : Math.pow(2, -iteration));\n            remainingInfluence -= influence;\n            score += this._memory[index].score * influence;\n            if (index === this._front) {\n                break;\n            }\n            index = (this._capacity + index - 1) % this._capacity;\n            iteration++;\n        } while (true);\n        return (score <= 0.5);\n    }\n    acceptStandardWheelEvent(e) {\n        if (isChrome) {\n            const targetWindow = dom.getWindow(e.browserEvent);\n            const pageZoomFactor = getZoomFactor(targetWindow);\n            // On Chrome, the incoming delta events are multiplied with the OS zoom factor.\n            // The OS zoom factor can be reverse engineered by using the device pixel ratio and the configured zoom factor into account.\n            this.accept(Date.now(), e.deltaX * pageZoomFactor, e.deltaY * pageZoomFactor);\n        }\n        else {\n            this.accept(Date.now(), e.deltaX, e.deltaY);\n        }\n    }\n    accept(timestamp, deltaX, deltaY) {\n        let previousItem = null;\n        const item = new MouseWheelClassifierItem(timestamp, deltaX, deltaY);\n        if (this._front === -1 && this._rear === -1) {\n            this._memory[0] = item;\n            this._front = 0;\n            this._rear = 0;\n        }\n        else {\n            previousItem = this._memory[this._rear];\n            this._rear = (this._rear + 1) % this._capacity;\n            if (this._rear === this._front) {\n                // Drop oldest\n                this._front = (this._front + 1) % this._capacity;\n            }\n            this._memory[this._rear] = item;\n        }\n        item.score = this._computeScore(item, previousItem);\n    }\n    /**\n     * A score between 0 and 1 for `item`.\n     *  - a score towards 0 indicates that the source appears to be a physical mouse wheel\n     *  - a score towards 1 indicates that the source appears to be a touchpad or magic mouse, etc.\n     */\n    _computeScore(item, previousItem) {\n        if (Math.abs(item.deltaX) > 0 && Math.abs(item.deltaY) > 0) {\n            // both axes exercised => definitely not a physical mouse wheel\n            return 1;\n        }\n        let score = 0.5;\n        if (!this._isAlmostInt(item.deltaX) || !this._isAlmostInt(item.deltaY)) {\n            // non-integer deltas => indicator that this is not a physical mouse wheel\n            score += 0.25;\n        }\n        // Non-accelerating scroll => indicator that this is a physical mouse wheel\n        // These can be identified by seeing whether they are the module of one another.\n        if (previousItem) {\n            const absDeltaX = Math.abs(item.deltaX);\n            const absDeltaY = Math.abs(item.deltaY);\n            const absPreviousDeltaX = Math.abs(previousItem.deltaX);\n            const absPreviousDeltaY = Math.abs(previousItem.deltaY);\n            // Min 1 to avoid division by zero, module 1 will still be 0.\n            const minDeltaX = Math.max(Math.min(absDeltaX, absPreviousDeltaX), 1);\n            const minDeltaY = Math.max(Math.min(absDeltaY, absPreviousDeltaY), 1);\n            const maxDeltaX = Math.max(absDeltaX, absPreviousDeltaX);\n            const maxDeltaY = Math.max(absDeltaY, absPreviousDeltaY);\n            const isSameModulo = (maxDeltaX % minDeltaX === 0 && maxDeltaY % minDeltaY === 0);\n            if (isSameModulo) {\n                score -= 0.5;\n            }\n        }\n        return Math.min(Math.max(score, 0), 1);\n    }\n    _isAlmostInt(value) {\n        const delta = Math.abs(Math.round(value) - value);\n        return (delta < 0.01);\n    }\n}\nexport class AbstractScrollableElement extends Widget {\n    get options() {\n        return this._options;\n    }\n    constructor(element, options, scrollable) {\n        super();\n        this._onScroll = this._register(new Emitter());\n        this.onScroll = this._onScroll.event;\n        this._onWillScroll = this._register(new Emitter());\n        element.style.overflow = 'hidden';\n        this._options = resolveOptions(options);\n        this._scrollable = scrollable;\n        this._register(this._scrollable.onScroll((e) => {\n            this._onWillScroll.fire(e);\n            this._onDidScroll(e);\n            this._onScroll.fire(e);\n        }));\n        const scrollbarHost = {\n            onMouseWheel: (mouseWheelEvent) => this._onMouseWheel(mouseWheelEvent),\n            onDragStart: () => this._onDragStart(),\n            onDragEnd: () => this._onDragEnd(),\n        };\n        this._verticalScrollbar = this._register(new VerticalScrollbar(this._scrollable, this._options, scrollbarHost));\n        this._horizontalScrollbar = this._register(new HorizontalScrollbar(this._scrollable, this._options, scrollbarHost));\n        this._domNode = document.createElement('div');\n        this._domNode.className = 'monaco-scrollable-element ' + this._options.className;\n        this._domNode.setAttribute('role', 'presentation');\n        this._domNode.style.position = 'relative';\n        this._domNode.style.overflow = 'hidden';\n        this._domNode.appendChild(element);\n        this._domNode.appendChild(this._horizontalScrollbar.domNode.domNode);\n        this._domNode.appendChild(this._verticalScrollbar.domNode.domNode);\n        if (this._options.useShadows) {\n            this._leftShadowDomNode = createFastDomNode(document.createElement('div'));\n            this._leftShadowDomNode.setClassName('shadow');\n            this._domNode.appendChild(this._leftShadowDomNode.domNode);\n            this._topShadowDomNode = createFastDomNode(document.createElement('div'));\n            this._topShadowDomNode.setClassName('shadow');\n            this._domNode.appendChild(this._topShadowDomNode.domNode);\n            this._topLeftShadowDomNode = createFastDomNode(document.createElement('div'));\n            this._topLeftShadowDomNode.setClassName('shadow');\n            this._domNode.appendChild(this._topLeftShadowDomNode.domNode);\n        }\n        else {\n            this._leftShadowDomNode = null;\n            this._topShadowDomNode = null;\n            this._topLeftShadowDomNode = null;\n        }\n        this._listenOnDomNode = this._options.listenOnDomNode || this._domNode;\n        this._mouseWheelToDispose = [];\n        this._setListeningToMouseWheel(this._options.handleMouseWheel);\n        this.onmouseover(this._listenOnDomNode, (e) => this._onMouseOver(e));\n        this.onmouseleave(this._listenOnDomNode, (e) => this._onMouseLeave(e));\n        this._hideTimeout = this._register(new TimeoutTimer());\n        this._isDragging = false;\n        this._mouseIsOver = false;\n        this._shouldRender = true;\n        this._revealOnScroll = true;\n    }\n    dispose() {\n        this._mouseWheelToDispose = dispose(this._mouseWheelToDispose);\n        super.dispose();\n    }\n    /**\n     * Get the generated 'scrollable' dom node\n     */\n    getDomNode() {\n        return this._domNode;\n    }\n    getOverviewRulerLayoutInfo() {\n        return {\n            parent: this._domNode,\n            insertBefore: this._verticalScrollbar.domNode.domNode,\n        };\n    }\n    /**\n     * Delegate a pointer down event to the vertical scrollbar.\n     * This is to help with clicking somewhere else and having the scrollbar react.\n     */\n    delegateVerticalScrollbarPointerDown(browserEvent) {\n        this._verticalScrollbar.delegatePointerDown(browserEvent);\n    }\n    getScrollDimensions() {\n        return this._scrollable.getScrollDimensions();\n    }\n    setScrollDimensions(dimensions) {\n        this._scrollable.setScrollDimensions(dimensions, false);\n    }\n    /**\n     * Update the class name of the scrollable element.\n     */\n    updateClassName(newClassName) {\n        this._options.className = newClassName;\n        // Defaults are different on Macs\n        if (platform.isMacintosh) {\n            this._options.className += ' mac';\n        }\n        this._domNode.className = 'monaco-scrollable-element ' + this._options.className;\n    }\n    /**\n     * Update configuration options for the scrollbar.\n     */\n    updateOptions(newOptions) {\n        if (typeof newOptions.handleMouseWheel !== 'undefined') {\n            this._options.handleMouseWheel = newOptions.handleMouseWheel;\n            this._setListeningToMouseWheel(this._options.handleMouseWheel);\n        }\n        if (typeof newOptions.mouseWheelScrollSensitivity !== 'undefined') {\n            this._options.mouseWheelScrollSensitivity = newOptions.mouseWheelScrollSensitivity;\n        }\n        if (typeof newOptions.fastScrollSensitivity !== 'undefined') {\n            this._options.fastScrollSensitivity = newOptions.fastScrollSensitivity;\n        }\n        if (typeof newOptions.scrollPredominantAxis !== 'undefined') {\n            this._options.scrollPredominantAxis = newOptions.scrollPredominantAxis;\n        }\n        if (typeof newOptions.horizontal !== 'undefined') {\n            this._options.horizontal = newOptions.horizontal;\n        }\n        if (typeof newOptions.vertical !== 'undefined') {\n            this._options.vertical = newOptions.vertical;\n        }\n        if (typeof newOptions.horizontalScrollbarSize !== 'undefined') {\n            this._options.horizontalScrollbarSize = newOptions.horizontalScrollbarSize;\n        }\n        if (typeof newOptions.verticalScrollbarSize !== 'undefined') {\n            this._options.verticalScrollbarSize = newOptions.verticalScrollbarSize;\n        }\n        if (typeof newOptions.scrollByPage !== 'undefined') {\n            this._options.scrollByPage = newOptions.scrollByPage;\n        }\n        this._horizontalScrollbar.updateOptions(this._options);\n        this._verticalScrollbar.updateOptions(this._options);\n        if (!this._options.lazyRender) {\n            this._render();\n        }\n    }\n    delegateScrollFromMouseWheelEvent(browserEvent) {\n        this._onMouseWheel(new StandardWheelEvent(browserEvent));\n    }\n    // -------------------- mouse wheel scrolling --------------------\n    _setListeningToMouseWheel(shouldListen) {\n        const isListening = (this._mouseWheelToDispose.length > 0);\n        if (isListening === shouldListen) {\n            // No change\n            return;\n        }\n        // Stop listening (if necessary)\n        this._mouseWheelToDispose = dispose(this._mouseWheelToDispose);\n        // Start listening (if necessary)\n        if (shouldListen) {\n            const onMouseWheel = (browserEvent) => {\n                this._onMouseWheel(new StandardWheelEvent(browserEvent));\n            };\n            this._mouseWheelToDispose.push(dom.addDisposableListener(this._listenOnDomNode, dom.EventType.MOUSE_WHEEL, onMouseWheel, { passive: false }));\n        }\n    }\n    _onMouseWheel(e) {\n        if (e.browserEvent?.defaultPrevented) {\n            return;\n        }\n        const classifier = MouseWheelClassifier.INSTANCE;\n        if (SCROLL_WHEEL_SMOOTH_SCROLL_ENABLED) {\n            classifier.acceptStandardWheelEvent(e);\n        }\n        // useful for creating unit tests:\n        // console.log(`${Date.now()}, ${e.deltaY}, ${e.deltaX}`);\n        let didScroll = false;\n        if (e.deltaY || e.deltaX) {\n            let deltaY = e.deltaY * this._options.mouseWheelScrollSensitivity;\n            let deltaX = e.deltaX * this._options.mouseWheelScrollSensitivity;\n            if (this._options.scrollPredominantAxis) {\n                if (this._options.scrollYToX && deltaX + deltaY === 0) {\n                    // when configured to map Y to X and we both see\n                    // no dominant axis and X and Y are competing with\n                    // identical values into opposite directions, we\n                    // ignore the delta as we cannot make a decision then\n                    deltaX = deltaY = 0;\n                }\n                else if (Math.abs(deltaY) >= Math.abs(deltaX)) {\n                    deltaX = 0;\n                }\n                else {\n                    deltaY = 0;\n                }\n            }\n            if (this._options.flipAxes) {\n                [deltaY, deltaX] = [deltaX, deltaY];\n            }\n            // Convert vertical scrolling to horizontal if shift is held, this\n            // is handled at a higher level on Mac\n            const shiftConvert = !platform.isMacintosh && e.browserEvent && e.browserEvent.shiftKey;\n            if ((this._options.scrollYToX || shiftConvert) && !deltaX) {\n                deltaX = deltaY;\n                deltaY = 0;\n            }\n            if (e.browserEvent && e.browserEvent.altKey) {\n                // fastScrolling\n                deltaX = deltaX * this._options.fastScrollSensitivity;\n                deltaY = deltaY * this._options.fastScrollSensitivity;\n            }\n            const futureScrollPosition = this._scrollable.getFutureScrollPosition();\n            let desiredScrollPosition = {};\n            if (deltaY) {\n                const deltaScrollTop = SCROLL_WHEEL_SENSITIVITY * deltaY;\n                // Here we convert values such as -0.3 to -1 or 0.3 to 1, otherwise low speed scrolling will never scroll\n                const desiredScrollTop = futureScrollPosition.scrollTop - (deltaScrollTop < 0 ? Math.floor(deltaScrollTop) : Math.ceil(deltaScrollTop));\n                this._verticalScrollbar.writeScrollPosition(desiredScrollPosition, desiredScrollTop);\n            }\n            if (deltaX) {\n                const deltaScrollLeft = SCROLL_WHEEL_SENSITIVITY * deltaX;\n                // Here we convert values such as -0.3 to -1 or 0.3 to 1, otherwise low speed scrolling will never scroll\n                const desiredScrollLeft = futureScrollPosition.scrollLeft - (deltaScrollLeft < 0 ? Math.floor(deltaScrollLeft) : Math.ceil(deltaScrollLeft));\n                this._horizontalScrollbar.writeScrollPosition(desiredScrollPosition, desiredScrollLeft);\n            }\n            // Check that we are scrolling towards a location which is valid\n            desiredScrollPosition = this._scrollable.validateScrollPosition(desiredScrollPosition);\n            if (futureScrollPosition.scrollLeft !== desiredScrollPosition.scrollLeft || futureScrollPosition.scrollTop !== desiredScrollPosition.scrollTop) {\n                const canPerformSmoothScroll = (SCROLL_WHEEL_SMOOTH_SCROLL_ENABLED\n                    && this._options.mouseWheelSmoothScroll\n                    && classifier.isPhysicalMouseWheel());\n                if (canPerformSmoothScroll) {\n                    this._scrollable.setScrollPositionSmooth(desiredScrollPosition);\n                }\n                else {\n                    this._scrollable.setScrollPositionNow(desiredScrollPosition);\n                }\n                didScroll = true;\n            }\n        }\n        let consumeMouseWheel = didScroll;\n        if (!consumeMouseWheel && this._options.alwaysConsumeMouseWheel) {\n            consumeMouseWheel = true;\n        }\n        if (!consumeMouseWheel && this._options.consumeMouseWheelIfScrollbarIsNeeded && (this._verticalScrollbar.isNeeded() || this._horizontalScrollbar.isNeeded())) {\n            consumeMouseWheel = true;\n        }\n        if (consumeMouseWheel) {\n            e.preventDefault();\n            e.stopPropagation();\n        }\n    }\n    _onDidScroll(e) {\n        this._shouldRender = this._horizontalScrollbar.onDidScroll(e) || this._shouldRender;\n        this._shouldRender = this._verticalScrollbar.onDidScroll(e) || this._shouldRender;\n        if (this._options.useShadows) {\n            this._shouldRender = true;\n        }\n        if (this._revealOnScroll) {\n            this._reveal();\n        }\n        if (!this._options.lazyRender) {\n            this._render();\n        }\n    }\n    /**\n     * Render / mutate the DOM now.\n     * Should be used together with the ctor option `lazyRender`.\n     */\n    renderNow() {\n        if (!this._options.lazyRender) {\n            throw new Error('Please use `lazyRender` together with `renderNow`!');\n        }\n        this._render();\n    }\n    _render() {\n        if (!this._shouldRender) {\n            return;\n        }\n        this._shouldRender = false;\n        this._horizontalScrollbar.render();\n        this._verticalScrollbar.render();\n        if (this._options.useShadows) {\n            const scrollState = this._scrollable.getCurrentScrollPosition();\n            const enableTop = scrollState.scrollTop > 0;\n            const enableLeft = scrollState.scrollLeft > 0;\n            const leftClassName = (enableLeft ? ' left' : '');\n            const topClassName = (enableTop ? ' top' : '');\n            const topLeftClassName = (enableLeft || enableTop ? ' top-left-corner' : '');\n            this._leftShadowDomNode.setClassName(`shadow${leftClassName}`);\n            this._topShadowDomNode.setClassName(`shadow${topClassName}`);\n            this._topLeftShadowDomNode.setClassName(`shadow${topLeftClassName}${topClassName}${leftClassName}`);\n        }\n    }\n    // -------------------- fade in / fade out --------------------\n    _onDragStart() {\n        this._isDragging = true;\n        this._reveal();\n    }\n    _onDragEnd() {\n        this._isDragging = false;\n        this._hide();\n    }\n    _onMouseLeave(e) {\n        this._mouseIsOver = false;\n        this._hide();\n    }\n    _onMouseOver(e) {\n        this._mouseIsOver = true;\n        this._reveal();\n    }\n    _reveal() {\n        this._verticalScrollbar.beginReveal();\n        this._horizontalScrollbar.beginReveal();\n        this._scheduleHide();\n    }\n    _hide() {\n        if (!this._mouseIsOver && !this._isDragging) {\n            this._verticalScrollbar.beginHide();\n            this._horizontalScrollbar.beginHide();\n        }\n    }\n    _scheduleHide() {\n        if (!this._mouseIsOver && !this._isDragging) {\n            this._hideTimeout.cancelAndSet(() => this._hide(), HIDE_TIMEOUT);\n        }\n    }\n}\nexport class ScrollableElement extends AbstractScrollableElement {\n    constructor(element, options) {\n        options = options || {};\n        options.mouseWheelSmoothScroll = false;\n        const scrollable = new Scrollable({\n            forceIntegerValues: true,\n            smoothScrollDuration: 0,\n            scheduleAtNextAnimationFrame: (callback) => dom.scheduleAtNextAnimationFrame(dom.getWindow(element), callback)\n        });\n        super(element, options, scrollable);\n        this._register(scrollable);\n    }\n    setScrollPosition(update) {\n        this._scrollable.setScrollPositionNow(update);\n    }\n}\nexport class SmoothScrollableElement extends AbstractScrollableElement {\n    constructor(element, options, scrollable) {\n        super(element, options, scrollable);\n    }\n    setScrollPosition(update) {\n        if (update.reuseAnimation) {\n            this._scrollable.setScrollPositionSmooth(update, update.reuseAnimation);\n        }\n        else {\n            this._scrollable.setScrollPositionNow(update);\n        }\n    }\n    getScrollPosition() {\n        return this._scrollable.getCurrentScrollPosition();\n    }\n}\nexport class DomScrollableElement extends AbstractScrollableElement {\n    constructor(element, options) {\n        options = options || {};\n        options.mouseWheelSmoothScroll = false;\n        const scrollable = new Scrollable({\n            forceIntegerValues: false, // See https://github.com/microsoft/vscode/issues/139877\n            smoothScrollDuration: 0,\n            scheduleAtNextAnimationFrame: (callback) => dom.scheduleAtNextAnimationFrame(dom.getWindow(element), callback)\n        });\n        super(element, options, scrollable);\n        this._register(scrollable);\n        this._element = element;\n        this._register(this.onScroll((e) => {\n            if (e.scrollTopChanged) {\n                this._element.scrollTop = e.scrollTop;\n            }\n            if (e.scrollLeftChanged) {\n                this._element.scrollLeft = e.scrollLeft;\n            }\n        }));\n        this.scanDomNode();\n    }\n    setScrollPosition(update) {\n        this._scrollable.setScrollPositionNow(update);\n    }\n    getScrollPosition() {\n        return this._scrollable.getCurrentScrollPosition();\n    }\n    scanDomNode() {\n        // width, scrollLeft, scrollWidth, height, scrollTop, scrollHeight\n        this.setScrollDimensions({\n            width: this._element.clientWidth,\n            scrollWidth: this._element.scrollWidth,\n            height: this._element.clientHeight,\n            scrollHeight: this._element.scrollHeight\n        });\n        this.setScrollPosition({\n            scrollLeft: this._element.scrollLeft,\n            scrollTop: this._element.scrollTop,\n        });\n    }\n}\nfunction resolveOptions(opts) {\n    const result = {\n        lazyRender: (typeof opts.lazyRender !== 'undefined' ? opts.lazyRender : false),\n        className: (typeof opts.className !== 'undefined' ? opts.className : ''),\n        useShadows: (typeof opts.useShadows !== 'undefined' ? opts.useShadows : true),\n        handleMouseWheel: (typeof opts.handleMouseWheel !== 'undefined' ? opts.handleMouseWheel : true),\n        flipAxes: (typeof opts.flipAxes !== 'undefined' ? opts.flipAxes : false),\n        consumeMouseWheelIfScrollbarIsNeeded: (typeof opts.consumeMouseWheelIfScrollbarIsNeeded !== 'undefined' ? opts.consumeMouseWheelIfScrollbarIsNeeded : false),\n        alwaysConsumeMouseWheel: (typeof opts.alwaysConsumeMouseWheel !== 'undefined' ? opts.alwaysConsumeMouseWheel : false),\n        scrollYToX: (typeof opts.scrollYToX !== 'undefined' ? opts.scrollYToX : false),\n        mouseWheelScrollSensitivity: (typeof opts.mouseWheelScrollSensitivity !== 'undefined' ? opts.mouseWheelScrollSensitivity : 1),\n        fastScrollSensitivity: (typeof opts.fastScrollSensitivity !== 'undefined' ? opts.fastScrollSensitivity : 5),\n        scrollPredominantAxis: (typeof opts.scrollPredominantAxis !== 'undefined' ? opts.scrollPredominantAxis : true),\n        mouseWheelSmoothScroll: (typeof opts.mouseWheelSmoothScroll !== 'undefined' ? opts.mouseWheelSmoothScroll : true),\n        arrowSize: (typeof opts.arrowSize !== 'undefined' ? opts.arrowSize : 11),\n        listenOnDomNode: (typeof opts.listenOnDomNode !== 'undefined' ? opts.listenOnDomNode : null),\n        horizontal: (typeof opts.horizontal !== 'undefined' ? opts.horizontal : 1 /* ScrollbarVisibility.Auto */),\n        horizontalScrollbarSize: (typeof opts.horizontalScrollbarSize !== 'undefined' ? opts.horizontalScrollbarSize : 10),\n        horizontalSliderSize: (typeof opts.horizontalSliderSize !== 'undefined' ? opts.horizontalSliderSize : 0),\n        horizontalHasArrows: (typeof opts.horizontalHasArrows !== 'undefined' ? opts.horizontalHasArrows : false),\n        vertical: (typeof opts.vertical !== 'undefined' ? opts.vertical : 1 /* ScrollbarVisibility.Auto */),\n        verticalScrollbarSize: (typeof opts.verticalScrollbarSize !== 'undefined' ? opts.verticalScrollbarSize : 10),\n        verticalHasArrows: (typeof opts.verticalHasArrows !== 'undefined' ? opts.verticalHasArrows : false),\n        verticalSliderSize: (typeof opts.verticalSliderSize !== 'undefined' ? opts.verticalSliderSize : 0),\n        scrollByPage: (typeof opts.scrollByPage !== 'undefined' ? opts.scrollByPage : false)\n    };\n    result.horizontalSliderSize = (typeof opts.horizontalSliderSize !== 'undefined' ? opts.horizontalSliderSize : result.horizontalScrollbarSize);\n    result.verticalSliderSize = (typeof opts.verticalSliderSize !== 'undefined' ? opts.verticalSliderSize : result.verticalScrollbarSize);\n    // Defaults are different on Macs\n    if (platform.isMacintosh) {\n        result.className += ' mac';\n    }\n    return result;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,aAAa,EAAEC,QAAQ,QAAQ,kBAAkB;AAC1D,OAAO,KAAKC,GAAG,MAAM,cAAc;AACnC,SAASC,iBAAiB,QAAQ,sBAAsB;AACxD,SAASC,kBAAkB,QAAQ,qBAAqB;AACxD,SAASC,mBAAmB,QAAQ,0BAA0B;AAC9D,SAASC,iBAAiB,QAAQ,wBAAwB;AAC1D,SAASC,MAAM,QAAQ,cAAc;AACrC,SAASC,YAAY,QAAQ,0BAA0B;AACvD,SAASC,OAAO,QAAQ,0BAA0B;AAClD,SAASC,OAAO,QAAQ,8BAA8B;AACtD,OAAO,KAAKC,QAAQ,MAAM,6BAA6B;AACvD,SAASC,UAAU,QAAQ,+BAA+B;AAC1D,OAAO,wBAAwB;AAC/B,MAAMC,YAAY,GAAG,GAAG;AACxB,MAAMC,wBAAwB,GAAG,EAAE;AACnC,MAAMC,kCAAkC,GAAG,IAAI;AAC/C,MAAMC,wBAAwB,CAAC;EAC3BC,WAAWA,CAACC,SAAS,EAAEC,MAAM,EAAEC,MAAM,EAAE;IACnC,IAAI,CAACF,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,KAAK,GAAG,CAAC;EAClB;AACJ;AACA,OAAO,MAAMC,oBAAoB,CAAC;EAC9B;IAAS,IAAI,CAACC,QAAQ,GAAG,IAAID,oBAAoB,CAAC,CAAC;EAAE;EACrDL,WAAWA,CAAA,EAAG;IACV,IAAI,CAACO,SAAS,GAAG,CAAC;IAClB,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACC,MAAM,GAAG,CAAC,CAAC;IAChB,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;EACnB;EACAC,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACF,MAAM,KAAK,CAAC,CAAC,IAAI,IAAI,CAACC,KAAK,KAAK,CAAC,CAAC,EAAE;MACzC;MACA,OAAO,KAAK;IAChB;IACA;IACA,IAAIE,kBAAkB,GAAG,CAAC;IAC1B,IAAIR,KAAK,GAAG,CAAC;IACb,IAAIS,SAAS,GAAG,CAAC;IACjB,IAAIC,KAAK,GAAG,IAAI,CAACJ,KAAK;IACtB,GAAG;MACC,MAAMK,SAAS,GAAID,KAAK,KAAK,IAAI,CAACL,MAAM,GAAGG,kBAAkB,GAAGI,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAACJ,SAAS,CAAE;MACxFD,kBAAkB,IAAIG,SAAS;MAC/BX,KAAK,IAAI,IAAI,CAACI,OAAO,CAACM,KAAK,CAAC,CAACV,KAAK,GAAGW,SAAS;MAC9C,IAAID,KAAK,KAAK,IAAI,CAACL,MAAM,EAAE;QACvB;MACJ;MACAK,KAAK,GAAG,CAAC,IAAI,CAACP,SAAS,GAAGO,KAAK,GAAG,CAAC,IAAI,IAAI,CAACP,SAAS;MACrDM,SAAS,EAAE;IACf,CAAC,QAAQ,IAAI;IACb,OAAQT,KAAK,IAAI,GAAG;EACxB;EACAc,wBAAwBA,CAACC,CAAC,EAAE;IACxB,IAAInC,QAAQ,EAAE;MACV,MAAMoC,YAAY,GAAGnC,GAAG,CAACoC,SAAS,CAACF,CAAC,CAACG,YAAY,CAAC;MAClD,MAAMC,cAAc,GAAGxC,aAAa,CAACqC,YAAY,CAAC;MAClD;MACA;MACA,IAAI,CAACI,MAAM,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEP,CAAC,CAACjB,MAAM,GAAGqB,cAAc,EAAEJ,CAAC,CAAChB,MAAM,GAAGoB,cAAc,CAAC;IACjF,CAAC,MACI;MACD,IAAI,CAACC,MAAM,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEP,CAAC,CAACjB,MAAM,EAAEiB,CAAC,CAAChB,MAAM,CAAC;IAC/C;EACJ;EACAqB,MAAMA,CAACvB,SAAS,EAAEC,MAAM,EAAEC,MAAM,EAAE;IAC9B,IAAIwB,YAAY,GAAG,IAAI;IACvB,MAAMC,IAAI,GAAG,IAAI7B,wBAAwB,CAACE,SAAS,EAAEC,MAAM,EAAEC,MAAM,CAAC;IACpE,IAAI,IAAI,CAACM,MAAM,KAAK,CAAC,CAAC,IAAI,IAAI,CAACC,KAAK,KAAK,CAAC,CAAC,EAAE;MACzC,IAAI,CAACF,OAAO,CAAC,CAAC,CAAC,GAAGoB,IAAI;MACtB,IAAI,CAACnB,MAAM,GAAG,CAAC;MACf,IAAI,CAACC,KAAK,GAAG,CAAC;IAClB,CAAC,MACI;MACDiB,YAAY,GAAG,IAAI,CAACnB,OAAO,CAAC,IAAI,CAACE,KAAK,CAAC;MACvC,IAAI,CAACA,KAAK,GAAG,CAAC,IAAI,CAACA,KAAK,GAAG,CAAC,IAAI,IAAI,CAACH,SAAS;MAC9C,IAAI,IAAI,CAACG,KAAK,KAAK,IAAI,CAACD,MAAM,EAAE;QAC5B;QACA,IAAI,CAACA,MAAM,GAAG,CAAC,IAAI,CAACA,MAAM,GAAG,CAAC,IAAI,IAAI,CAACF,SAAS;MACpD;MACA,IAAI,CAACC,OAAO,CAAC,IAAI,CAACE,KAAK,CAAC,GAAGkB,IAAI;IACnC;IACAA,IAAI,CAACxB,KAAK,GAAG,IAAI,CAACyB,aAAa,CAACD,IAAI,EAAED,YAAY,CAAC;EACvD;EACA;AACJ;AACA;AACA;AACA;EACIE,aAAaA,CAACD,IAAI,EAAED,YAAY,EAAE;IAC9B,IAAIX,IAAI,CAACc,GAAG,CAACF,IAAI,CAAC1B,MAAM,CAAC,GAAG,CAAC,IAAIc,IAAI,CAACc,GAAG,CAACF,IAAI,CAACzB,MAAM,CAAC,GAAG,CAAC,EAAE;MACxD;MACA,OAAO,CAAC;IACZ;IACA,IAAIC,KAAK,GAAG,GAAG;IACf,IAAI,CAAC,IAAI,CAAC2B,YAAY,CAACH,IAAI,CAAC1B,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC6B,YAAY,CAACH,IAAI,CAACzB,MAAM,CAAC,EAAE;MACpE;MACAC,KAAK,IAAI,IAAI;IACjB;IACA;IACA;IACA,IAAIuB,YAAY,EAAE;MACd,MAAMK,SAAS,GAAGhB,IAAI,CAACc,GAAG,CAACF,IAAI,CAAC1B,MAAM,CAAC;MACvC,MAAM+B,SAAS,GAAGjB,IAAI,CAACc,GAAG,CAACF,IAAI,CAACzB,MAAM,CAAC;MACvC,MAAM+B,iBAAiB,GAAGlB,IAAI,CAACc,GAAG,CAACH,YAAY,CAACzB,MAAM,CAAC;MACvD,MAAMiC,iBAAiB,GAAGnB,IAAI,CAACc,GAAG,CAACH,YAAY,CAACxB,MAAM,CAAC;MACvD;MACA,MAAMiC,SAAS,GAAGpB,IAAI,CAACqB,GAAG,CAACrB,IAAI,CAACsB,GAAG,CAACN,SAAS,EAAEE,iBAAiB,CAAC,EAAE,CAAC,CAAC;MACrE,MAAMK,SAAS,GAAGvB,IAAI,CAACqB,GAAG,CAACrB,IAAI,CAACsB,GAAG,CAACL,SAAS,EAAEE,iBAAiB,CAAC,EAAE,CAAC,CAAC;MACrE,MAAMK,SAAS,GAAGxB,IAAI,CAACqB,GAAG,CAACL,SAAS,EAAEE,iBAAiB,CAAC;MACxD,MAAMO,SAAS,GAAGzB,IAAI,CAACqB,GAAG,CAACJ,SAAS,EAAEE,iBAAiB,CAAC;MACxD,MAAMO,YAAY,GAAIF,SAAS,GAAGJ,SAAS,KAAK,CAAC,IAAIK,SAAS,GAAGF,SAAS,KAAK,CAAE;MACjF,IAAIG,YAAY,EAAE;QACdtC,KAAK,IAAI,GAAG;MAChB;IACJ;IACA,OAAOY,IAAI,CAACsB,GAAG,CAACtB,IAAI,CAACqB,GAAG,CAACjC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;EAC1C;EACA2B,YAAYA,CAACY,KAAK,EAAE;IAChB,MAAMC,KAAK,GAAG5B,IAAI,CAACc,GAAG,CAACd,IAAI,CAAC6B,KAAK,CAACF,KAAK,CAAC,GAAGA,KAAK,CAAC;IACjD,OAAQC,KAAK,GAAG,IAAI;EACxB;AACJ;AACA,OAAO,MAAME,yBAAyB,SAASxD,MAAM,CAAC;EAClD,IAAIyD,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACC,QAAQ;EACxB;EACAhD,WAAWA,CAACiD,OAAO,EAAEF,OAAO,EAAEG,UAAU,EAAE;IACtC,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,SAAS,GAAG,IAAI,CAACC,SAAS,CAAC,IAAI5D,OAAO,CAAC,CAAC,CAAC;IAC9C,IAAI,CAAC6D,QAAQ,GAAG,IAAI,CAACF,SAAS,CAACG,KAAK;IACpC,IAAI,CAACC,aAAa,GAAG,IAAI,CAACH,SAAS,CAAC,IAAI5D,OAAO,CAAC,CAAC,CAAC;IAClDyD,OAAO,CAACO,KAAK,CAACC,QAAQ,GAAG,QAAQ;IACjC,IAAI,CAACT,QAAQ,GAAGU,cAAc,CAACX,OAAO,CAAC;IACvC,IAAI,CAACY,WAAW,GAAGT,UAAU;IAC7B,IAAI,CAACE,SAAS,CAAC,IAAI,CAACO,WAAW,CAACN,QAAQ,CAAElC,CAAC,IAAK;MAC5C,IAAI,CAACoC,aAAa,CAACK,IAAI,CAACzC,CAAC,CAAC;MAC1B,IAAI,CAAC0C,YAAY,CAAC1C,CAAC,CAAC;MACpB,IAAI,CAACgC,SAAS,CAACS,IAAI,CAACzC,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC;IACH,MAAM2C,aAAa,GAAG;MAClBC,YAAY,EAAGC,eAAe,IAAK,IAAI,CAACC,aAAa,CAACD,eAAe,CAAC;MACtEE,WAAW,EAAEA,CAAA,KAAM,IAAI,CAACC,YAAY,CAAC,CAAC;MACtCC,SAAS,EAAEA,CAAA,KAAM,IAAI,CAACC,UAAU,CAAC;IACrC,CAAC;IACD,IAAI,CAACC,kBAAkB,GAAG,IAAI,CAAClB,SAAS,CAAC,IAAI/D,iBAAiB,CAAC,IAAI,CAACsE,WAAW,EAAE,IAAI,CAACX,QAAQ,EAAEc,aAAa,CAAC,CAAC;IAC/G,IAAI,CAACS,oBAAoB,GAAG,IAAI,CAACnB,SAAS,CAAC,IAAIhE,mBAAmB,CAAC,IAAI,CAACuE,WAAW,EAAE,IAAI,CAACX,QAAQ,EAAEc,aAAa,CAAC,CAAC;IACnH,IAAI,CAACU,QAAQ,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC7C,IAAI,CAACF,QAAQ,CAACG,SAAS,GAAG,4BAA4B,GAAG,IAAI,CAAC3B,QAAQ,CAAC2B,SAAS;IAChF,IAAI,CAACH,QAAQ,CAACI,YAAY,CAAC,MAAM,EAAE,cAAc,CAAC;IAClD,IAAI,CAACJ,QAAQ,CAAChB,KAAK,CAACqB,QAAQ,GAAG,UAAU;IACzC,IAAI,CAACL,QAAQ,CAAChB,KAAK,CAACC,QAAQ,GAAG,QAAQ;IACvC,IAAI,CAACe,QAAQ,CAACM,WAAW,CAAC7B,OAAO,CAAC;IAClC,IAAI,CAACuB,QAAQ,CAACM,WAAW,CAAC,IAAI,CAACP,oBAAoB,CAACQ,OAAO,CAACA,OAAO,CAAC;IACpE,IAAI,CAACP,QAAQ,CAACM,WAAW,CAAC,IAAI,CAACR,kBAAkB,CAACS,OAAO,CAACA,OAAO,CAAC;IAClE,IAAI,IAAI,CAAC/B,QAAQ,CAACgC,UAAU,EAAE;MAC1B,IAAI,CAACC,kBAAkB,GAAG/F,iBAAiB,CAACuF,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC,CAAC;MAC1E,IAAI,CAACO,kBAAkB,CAACC,YAAY,CAAC,QAAQ,CAAC;MAC9C,IAAI,CAACV,QAAQ,CAACM,WAAW,CAAC,IAAI,CAACG,kBAAkB,CAACF,OAAO,CAAC;MAC1D,IAAI,CAACI,iBAAiB,GAAGjG,iBAAiB,CAACuF,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC,CAAC;MACzE,IAAI,CAACS,iBAAiB,CAACD,YAAY,CAAC,QAAQ,CAAC;MAC7C,IAAI,CAACV,QAAQ,CAACM,WAAW,CAAC,IAAI,CAACK,iBAAiB,CAACJ,OAAO,CAAC;MACzD,IAAI,CAACK,qBAAqB,GAAGlG,iBAAiB,CAACuF,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC,CAAC;MAC7E,IAAI,CAACU,qBAAqB,CAACF,YAAY,CAAC,QAAQ,CAAC;MACjD,IAAI,CAACV,QAAQ,CAACM,WAAW,CAAC,IAAI,CAACM,qBAAqB,CAACL,OAAO,CAAC;IACjE,CAAC,MACI;MACD,IAAI,CAACE,kBAAkB,GAAG,IAAI;MAC9B,IAAI,CAACE,iBAAiB,GAAG,IAAI;MAC7B,IAAI,CAACC,qBAAqB,GAAG,IAAI;IACrC;IACA,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACrC,QAAQ,CAACsC,eAAe,IAAI,IAAI,CAACd,QAAQ;IACtE,IAAI,CAACe,oBAAoB,GAAG,EAAE;IAC9B,IAAI,CAACC,yBAAyB,CAAC,IAAI,CAACxC,QAAQ,CAACyC,gBAAgB,CAAC;IAC9D,IAAI,CAACC,WAAW,CAAC,IAAI,CAACL,gBAAgB,EAAGlE,CAAC,IAAK,IAAI,CAACwE,YAAY,CAACxE,CAAC,CAAC,CAAC;IACpE,IAAI,CAACyE,YAAY,CAAC,IAAI,CAACP,gBAAgB,EAAGlE,CAAC,IAAK,IAAI,CAAC0E,aAAa,CAAC1E,CAAC,CAAC,CAAC;IACtE,IAAI,CAAC2E,YAAY,GAAG,IAAI,CAAC1C,SAAS,CAAC,IAAI7D,YAAY,CAAC,CAAC,CAAC;IACtD,IAAI,CAACwG,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,eAAe,GAAG,IAAI;EAC/B;EACAzG,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC8F,oBAAoB,GAAG9F,OAAO,CAAC,IAAI,CAAC8F,oBAAoB,CAAC;IAC9D,KAAK,CAAC9F,OAAO,CAAC,CAAC;EACnB;EACA;AACJ;AACA;EACI0G,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAAC3B,QAAQ;EACxB;EACA4B,0BAA0BA,CAAA,EAAG;IACzB,OAAO;MACHC,MAAM,EAAE,IAAI,CAAC7B,QAAQ;MACrB8B,YAAY,EAAE,IAAI,CAAChC,kBAAkB,CAACS,OAAO,CAACA;IAClD,CAAC;EACL;EACA;AACJ;AACA;AACA;EACIwB,oCAAoCA,CAACjF,YAAY,EAAE;IAC/C,IAAI,CAACgD,kBAAkB,CAACkC,mBAAmB,CAAClF,YAAY,CAAC;EAC7D;EACAmF,mBAAmBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAAC9C,WAAW,CAAC8C,mBAAmB,CAAC,CAAC;EACjD;EACAC,mBAAmBA,CAACC,UAAU,EAAE;IAC5B,IAAI,CAAChD,WAAW,CAAC+C,mBAAmB,CAACC,UAAU,EAAE,KAAK,CAAC;EAC3D;EACA;AACJ;AACA;EACIC,eAAeA,CAACC,YAAY,EAAE;IAC1B,IAAI,CAAC7D,QAAQ,CAAC2B,SAAS,GAAGkC,YAAY;IACtC;IACA,IAAInH,QAAQ,CAACoH,WAAW,EAAE;MACtB,IAAI,CAAC9D,QAAQ,CAAC2B,SAAS,IAAI,MAAM;IACrC;IACA,IAAI,CAACH,QAAQ,CAACG,SAAS,GAAG,4BAA4B,GAAG,IAAI,CAAC3B,QAAQ,CAAC2B,SAAS;EACpF;EACA;AACJ;AACA;EACIoC,aAAaA,CAACC,UAAU,EAAE;IACtB,IAAI,OAAOA,UAAU,CAACvB,gBAAgB,KAAK,WAAW,EAAE;MACpD,IAAI,CAACzC,QAAQ,CAACyC,gBAAgB,GAAGuB,UAAU,CAACvB,gBAAgB;MAC5D,IAAI,CAACD,yBAAyB,CAAC,IAAI,CAACxC,QAAQ,CAACyC,gBAAgB,CAAC;IAClE;IACA,IAAI,OAAOuB,UAAU,CAACC,2BAA2B,KAAK,WAAW,EAAE;MAC/D,IAAI,CAACjE,QAAQ,CAACiE,2BAA2B,GAAGD,UAAU,CAACC,2BAA2B;IACtF;IACA,IAAI,OAAOD,UAAU,CAACE,qBAAqB,KAAK,WAAW,EAAE;MACzD,IAAI,CAAClE,QAAQ,CAACkE,qBAAqB,GAAGF,UAAU,CAACE,qBAAqB;IAC1E;IACA,IAAI,OAAOF,UAAU,CAACG,qBAAqB,KAAK,WAAW,EAAE;MACzD,IAAI,CAACnE,QAAQ,CAACmE,qBAAqB,GAAGH,UAAU,CAACG,qBAAqB;IAC1E;IACA,IAAI,OAAOH,UAAU,CAACI,UAAU,KAAK,WAAW,EAAE;MAC9C,IAAI,CAACpE,QAAQ,CAACoE,UAAU,GAAGJ,UAAU,CAACI,UAAU;IACpD;IACA,IAAI,OAAOJ,UAAU,CAACK,QAAQ,KAAK,WAAW,EAAE;MAC5C,IAAI,CAACrE,QAAQ,CAACqE,QAAQ,GAAGL,UAAU,CAACK,QAAQ;IAChD;IACA,IAAI,OAAOL,UAAU,CAACM,uBAAuB,KAAK,WAAW,EAAE;MAC3D,IAAI,CAACtE,QAAQ,CAACsE,uBAAuB,GAAGN,UAAU,CAACM,uBAAuB;IAC9E;IACA,IAAI,OAAON,UAAU,CAACO,qBAAqB,KAAK,WAAW,EAAE;MACzD,IAAI,CAACvE,QAAQ,CAACuE,qBAAqB,GAAGP,UAAU,CAACO,qBAAqB;IAC1E;IACA,IAAI,OAAOP,UAAU,CAACQ,YAAY,KAAK,WAAW,EAAE;MAChD,IAAI,CAACxE,QAAQ,CAACwE,YAAY,GAAGR,UAAU,CAACQ,YAAY;IACxD;IACA,IAAI,CAACjD,oBAAoB,CAACwC,aAAa,CAAC,IAAI,CAAC/D,QAAQ,CAAC;IACtD,IAAI,CAACsB,kBAAkB,CAACyC,aAAa,CAAC,IAAI,CAAC/D,QAAQ,CAAC;IACpD,IAAI,CAAC,IAAI,CAACA,QAAQ,CAACyE,UAAU,EAAE;MAC3B,IAAI,CAACC,OAAO,CAAC,CAAC;IAClB;EACJ;EACAC,iCAAiCA,CAACrG,YAAY,EAAE;IAC5C,IAAI,CAAC2C,aAAa,CAAC,IAAI9E,kBAAkB,CAACmC,YAAY,CAAC,CAAC;EAC5D;EACA;EACAkE,yBAAyBA,CAACoC,YAAY,EAAE;IACpC,MAAMC,WAAW,GAAI,IAAI,CAACtC,oBAAoB,CAACuC,MAAM,GAAG,CAAE;IAC1D,IAAID,WAAW,KAAKD,YAAY,EAAE;MAC9B;MACA;IACJ;IACA;IACA,IAAI,CAACrC,oBAAoB,GAAG9F,OAAO,CAAC,IAAI,CAAC8F,oBAAoB,CAAC;IAC9D;IACA,IAAIqC,YAAY,EAAE;MACd,MAAM7D,YAAY,GAAIzC,YAAY,IAAK;QACnC,IAAI,CAAC2C,aAAa,CAAC,IAAI9E,kBAAkB,CAACmC,YAAY,CAAC,CAAC;MAC5D,CAAC;MACD,IAAI,CAACiE,oBAAoB,CAACwC,IAAI,CAAC9I,GAAG,CAAC+I,qBAAqB,CAAC,IAAI,CAAC3C,gBAAgB,EAAEpG,GAAG,CAACgJ,SAAS,CAACC,WAAW,EAAEnE,YAAY,EAAE;QAAEoE,OAAO,EAAE;MAAM,CAAC,CAAC,CAAC;IACjJ;EACJ;EACAlE,aAAaA,CAAC9C,CAAC,EAAE;IACb,IAAIA,CAAC,CAACG,YAAY,EAAE8G,gBAAgB,EAAE;MAClC;IACJ;IACA,MAAMC,UAAU,GAAGhI,oBAAoB,CAACC,QAAQ;IAChD,IAAIR,kCAAkC,EAAE;MACpCuI,UAAU,CAACnH,wBAAwB,CAACC,CAAC,CAAC;IAC1C;IACA;IACA;IACA,IAAImH,SAAS,GAAG,KAAK;IACrB,IAAInH,CAAC,CAAChB,MAAM,IAAIgB,CAAC,CAACjB,MAAM,EAAE;MACtB,IAAIC,MAAM,GAAGgB,CAAC,CAAChB,MAAM,GAAG,IAAI,CAAC6C,QAAQ,CAACiE,2BAA2B;MACjE,IAAI/G,MAAM,GAAGiB,CAAC,CAACjB,MAAM,GAAG,IAAI,CAAC8C,QAAQ,CAACiE,2BAA2B;MACjE,IAAI,IAAI,CAACjE,QAAQ,CAACmE,qBAAqB,EAAE;QACrC,IAAI,IAAI,CAACnE,QAAQ,CAACuF,UAAU,IAAIrI,MAAM,GAAGC,MAAM,KAAK,CAAC,EAAE;UACnD;UACA;UACA;UACA;UACAD,MAAM,GAAGC,MAAM,GAAG,CAAC;QACvB,CAAC,MACI,IAAIa,IAAI,CAACc,GAAG,CAAC3B,MAAM,CAAC,IAAIa,IAAI,CAACc,GAAG,CAAC5B,MAAM,CAAC,EAAE;UAC3CA,MAAM,GAAG,CAAC;QACd,CAAC,MACI;UACDC,MAAM,GAAG,CAAC;QACd;MACJ;MACA,IAAI,IAAI,CAAC6C,QAAQ,CAACwF,QAAQ,EAAE;QACxB,CAACrI,MAAM,EAAED,MAAM,CAAC,GAAG,CAACA,MAAM,EAAEC,MAAM,CAAC;MACvC;MACA;MACA;MACA,MAAMsI,YAAY,GAAG,CAAC/I,QAAQ,CAACoH,WAAW,IAAI3F,CAAC,CAACG,YAAY,IAAIH,CAAC,CAACG,YAAY,CAACoH,QAAQ;MACvF,IAAI,CAAC,IAAI,CAAC1F,QAAQ,CAACuF,UAAU,IAAIE,YAAY,KAAK,CAACvI,MAAM,EAAE;QACvDA,MAAM,GAAGC,MAAM;QACfA,MAAM,GAAG,CAAC;MACd;MACA,IAAIgB,CAAC,CAACG,YAAY,IAAIH,CAAC,CAACG,YAAY,CAACqH,MAAM,EAAE;QACzC;QACAzI,MAAM,GAAGA,MAAM,GAAG,IAAI,CAAC8C,QAAQ,CAACkE,qBAAqB;QACrD/G,MAAM,GAAGA,MAAM,GAAG,IAAI,CAAC6C,QAAQ,CAACkE,qBAAqB;MACzD;MACA,MAAM0B,oBAAoB,GAAG,IAAI,CAACjF,WAAW,CAACkF,uBAAuB,CAAC,CAAC;MACvE,IAAIC,qBAAqB,GAAG,CAAC,CAAC;MAC9B,IAAI3I,MAAM,EAAE;QACR,MAAM4I,cAAc,GAAGlJ,wBAAwB,GAAGM,MAAM;QACxD;QACA,MAAM6I,gBAAgB,GAAGJ,oBAAoB,CAACK,SAAS,IAAIF,cAAc,GAAG,CAAC,GAAG/H,IAAI,CAACkI,KAAK,CAACH,cAAc,CAAC,GAAG/H,IAAI,CAACmI,IAAI,CAACJ,cAAc,CAAC,CAAC;QACvI,IAAI,CAACzE,kBAAkB,CAAC8E,mBAAmB,CAACN,qBAAqB,EAAEE,gBAAgB,CAAC;MACxF;MACA,IAAI9I,MAAM,EAAE;QACR,MAAMmJ,eAAe,GAAGxJ,wBAAwB,GAAGK,MAAM;QACzD;QACA,MAAMoJ,iBAAiB,GAAGV,oBAAoB,CAACW,UAAU,IAAIF,eAAe,GAAG,CAAC,GAAGrI,IAAI,CAACkI,KAAK,CAACG,eAAe,CAAC,GAAGrI,IAAI,CAACmI,IAAI,CAACE,eAAe,CAAC,CAAC;QAC5I,IAAI,CAAC9E,oBAAoB,CAAC6E,mBAAmB,CAACN,qBAAqB,EAAEQ,iBAAiB,CAAC;MAC3F;MACA;MACAR,qBAAqB,GAAG,IAAI,CAACnF,WAAW,CAAC6F,sBAAsB,CAACV,qBAAqB,CAAC;MACtF,IAAIF,oBAAoB,CAACW,UAAU,KAAKT,qBAAqB,CAACS,UAAU,IAAIX,oBAAoB,CAACK,SAAS,KAAKH,qBAAqB,CAACG,SAAS,EAAE;QAC5I,MAAMQ,sBAAsB,GAAI3J,kCAAkC,IAC3D,IAAI,CAACkD,QAAQ,CAAC0G,sBAAsB,IACpCrB,UAAU,CAAC1H,oBAAoB,CAAC,CAAE;QACzC,IAAI8I,sBAAsB,EAAE;UACxB,IAAI,CAAC9F,WAAW,CAACgG,uBAAuB,CAACb,qBAAqB,CAAC;QACnE,CAAC,MACI;UACD,IAAI,CAACnF,WAAW,CAACiG,oBAAoB,CAACd,qBAAqB,CAAC;QAChE;QACAR,SAAS,GAAG,IAAI;MACpB;IACJ;IACA,IAAIuB,iBAAiB,GAAGvB,SAAS;IACjC,IAAI,CAACuB,iBAAiB,IAAI,IAAI,CAAC7G,QAAQ,CAAC8G,uBAAuB,EAAE;MAC7DD,iBAAiB,GAAG,IAAI;IAC5B;IACA,IAAI,CAACA,iBAAiB,IAAI,IAAI,CAAC7G,QAAQ,CAAC+G,oCAAoC,KAAK,IAAI,CAACzF,kBAAkB,CAAC0F,QAAQ,CAAC,CAAC,IAAI,IAAI,CAACzF,oBAAoB,CAACyF,QAAQ,CAAC,CAAC,CAAC,EAAE;MAC1JH,iBAAiB,GAAG,IAAI;IAC5B;IACA,IAAIA,iBAAiB,EAAE;MACnB1I,CAAC,CAAC8I,cAAc,CAAC,CAAC;MAClB9I,CAAC,CAAC+I,eAAe,CAAC,CAAC;IACvB;EACJ;EACArG,YAAYA,CAAC1C,CAAC,EAAE;IACZ,IAAI,CAAC8E,aAAa,GAAG,IAAI,CAAC1B,oBAAoB,CAAC4F,WAAW,CAAChJ,CAAC,CAAC,IAAI,IAAI,CAAC8E,aAAa;IACnF,IAAI,CAACA,aAAa,GAAG,IAAI,CAAC3B,kBAAkB,CAAC6F,WAAW,CAAChJ,CAAC,CAAC,IAAI,IAAI,CAAC8E,aAAa;IACjF,IAAI,IAAI,CAACjD,QAAQ,CAACgC,UAAU,EAAE;MAC1B,IAAI,CAACiB,aAAa,GAAG,IAAI;IAC7B;IACA,IAAI,IAAI,CAACC,eAAe,EAAE;MACtB,IAAI,CAACkE,OAAO,CAAC,CAAC;IAClB;IACA,IAAI,CAAC,IAAI,CAACpH,QAAQ,CAACyE,UAAU,EAAE;MAC3B,IAAI,CAACC,OAAO,CAAC,CAAC;IAClB;EACJ;EACA;AACJ;AACA;AACA;EACI2C,SAASA,CAAA,EAAG;IACR,IAAI,CAAC,IAAI,CAACrH,QAAQ,CAACyE,UAAU,EAAE;MAC3B,MAAM,IAAI6C,KAAK,CAAC,oDAAoD,CAAC;IACzE;IACA,IAAI,CAAC5C,OAAO,CAAC,CAAC;EAClB;EACAA,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC,IAAI,CAACzB,aAAa,EAAE;MACrB;IACJ;IACA,IAAI,CAACA,aAAa,GAAG,KAAK;IAC1B,IAAI,CAAC1B,oBAAoB,CAACgG,MAAM,CAAC,CAAC;IAClC,IAAI,CAACjG,kBAAkB,CAACiG,MAAM,CAAC,CAAC;IAChC,IAAI,IAAI,CAACvH,QAAQ,CAACgC,UAAU,EAAE;MAC1B,MAAMwF,WAAW,GAAG,IAAI,CAAC7G,WAAW,CAAC8G,wBAAwB,CAAC,CAAC;MAC/D,MAAMC,SAAS,GAAGF,WAAW,CAACvB,SAAS,GAAG,CAAC;MAC3C,MAAM0B,UAAU,GAAGH,WAAW,CAACjB,UAAU,GAAG,CAAC;MAC7C,MAAMqB,aAAa,GAAID,UAAU,GAAG,OAAO,GAAG,EAAG;MACjD,MAAME,YAAY,GAAIH,SAAS,GAAG,MAAM,GAAG,EAAG;MAC9C,MAAMI,gBAAgB,GAAIH,UAAU,IAAID,SAAS,GAAG,kBAAkB,GAAG,EAAG;MAC5E,IAAI,CAACzF,kBAAkB,CAACC,YAAY,CAAC,SAAS0F,aAAa,EAAE,CAAC;MAC9D,IAAI,CAACzF,iBAAiB,CAACD,YAAY,CAAC,SAAS2F,YAAY,EAAE,CAAC;MAC5D,IAAI,CAACzF,qBAAqB,CAACF,YAAY,CAAC,SAAS4F,gBAAgB,GAAGD,YAAY,GAAGD,aAAa,EAAE,CAAC;IACvG;EACJ;EACA;EACAzG,YAAYA,CAAA,EAAG;IACX,IAAI,CAAC4B,WAAW,GAAG,IAAI;IACvB,IAAI,CAACqE,OAAO,CAAC,CAAC;EAClB;EACA/F,UAAUA,CAAA,EAAG;IACT,IAAI,CAAC0B,WAAW,GAAG,KAAK;IACxB,IAAI,CAACgF,KAAK,CAAC,CAAC;EAChB;EACAlF,aAAaA,CAAC1E,CAAC,EAAE;IACb,IAAI,CAAC6E,YAAY,GAAG,KAAK;IACzB,IAAI,CAAC+E,KAAK,CAAC,CAAC;EAChB;EACApF,YAAYA,CAACxE,CAAC,EAAE;IACZ,IAAI,CAAC6E,YAAY,GAAG,IAAI;IACxB,IAAI,CAACoE,OAAO,CAAC,CAAC;EAClB;EACAA,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC9F,kBAAkB,CAAC0G,WAAW,CAAC,CAAC;IACrC,IAAI,CAACzG,oBAAoB,CAACyG,WAAW,CAAC,CAAC;IACvC,IAAI,CAACC,aAAa,CAAC,CAAC;EACxB;EACAF,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAC,IAAI,CAAC/E,YAAY,IAAI,CAAC,IAAI,CAACD,WAAW,EAAE;MACzC,IAAI,CAACzB,kBAAkB,CAAC4G,SAAS,CAAC,CAAC;MACnC,IAAI,CAAC3G,oBAAoB,CAAC2G,SAAS,CAAC,CAAC;IACzC;EACJ;EACAD,aAAaA,CAAA,EAAG;IACZ,IAAI,CAAC,IAAI,CAACjF,YAAY,IAAI,CAAC,IAAI,CAACD,WAAW,EAAE;MACzC,IAAI,CAACD,YAAY,CAACqF,YAAY,CAAC,MAAM,IAAI,CAACJ,KAAK,CAAC,CAAC,EAAEnL,YAAY,CAAC;IACpE;EACJ;AACJ;AACA,OAAO,MAAMwL,iBAAiB,SAAStI,yBAAyB,CAAC;EAC7D9C,WAAWA,CAACiD,OAAO,EAAEF,OAAO,EAAE;IAC1BA,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;IACvBA,OAAO,CAAC2G,sBAAsB,GAAG,KAAK;IACtC,MAAMxG,UAAU,GAAG,IAAIvD,UAAU,CAAC;MAC9B0L,kBAAkB,EAAE,IAAI;MACxBC,oBAAoB,EAAE,CAAC;MACvBC,4BAA4B,EAAGC,QAAQ,IAAKvM,GAAG,CAACsM,4BAA4B,CAACtM,GAAG,CAACoC,SAAS,CAAC4B,OAAO,CAAC,EAAEuI,QAAQ;IACjH,CAAC,CAAC;IACF,KAAK,CAACvI,OAAO,EAAEF,OAAO,EAAEG,UAAU,CAAC;IACnC,IAAI,CAACE,SAAS,CAACF,UAAU,CAAC;EAC9B;EACAuI,iBAAiBA,CAACC,MAAM,EAAE;IACtB,IAAI,CAAC/H,WAAW,CAACiG,oBAAoB,CAAC8B,MAAM,CAAC;EACjD;AACJ;AACA,OAAO,MAAMC,uBAAuB,SAAS7I,yBAAyB,CAAC;EACnE9C,WAAWA,CAACiD,OAAO,EAAEF,OAAO,EAAEG,UAAU,EAAE;IACtC,KAAK,CAACD,OAAO,EAAEF,OAAO,EAAEG,UAAU,CAAC;EACvC;EACAuI,iBAAiBA,CAACC,MAAM,EAAE;IACtB,IAAIA,MAAM,CAACE,cAAc,EAAE;MACvB,IAAI,CAACjI,WAAW,CAACgG,uBAAuB,CAAC+B,MAAM,EAAEA,MAAM,CAACE,cAAc,CAAC;IAC3E,CAAC,MACI;MACD,IAAI,CAACjI,WAAW,CAACiG,oBAAoB,CAAC8B,MAAM,CAAC;IACjD;EACJ;EACAG,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAAClI,WAAW,CAAC8G,wBAAwB,CAAC,CAAC;EACtD;AACJ;AACA,OAAO,MAAMqB,oBAAoB,SAAShJ,yBAAyB,CAAC;EAChE9C,WAAWA,CAACiD,OAAO,EAAEF,OAAO,EAAE;IAC1BA,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;IACvBA,OAAO,CAAC2G,sBAAsB,GAAG,KAAK;IACtC,MAAMxG,UAAU,GAAG,IAAIvD,UAAU,CAAC;MAC9B0L,kBAAkB,EAAE,KAAK;MAAE;MAC3BC,oBAAoB,EAAE,CAAC;MACvBC,4BAA4B,EAAGC,QAAQ,IAAKvM,GAAG,CAACsM,4BAA4B,CAACtM,GAAG,CAACoC,SAAS,CAAC4B,OAAO,CAAC,EAAEuI,QAAQ;IACjH,CAAC,CAAC;IACF,KAAK,CAACvI,OAAO,EAAEF,OAAO,EAAEG,UAAU,CAAC;IACnC,IAAI,CAACE,SAAS,CAACF,UAAU,CAAC;IAC1B,IAAI,CAAC6I,QAAQ,GAAG9I,OAAO;IACvB,IAAI,CAACG,SAAS,CAAC,IAAI,CAACC,QAAQ,CAAElC,CAAC,IAAK;MAChC,IAAIA,CAAC,CAAC6K,gBAAgB,EAAE;QACpB,IAAI,CAACD,QAAQ,CAAC9C,SAAS,GAAG9H,CAAC,CAAC8H,SAAS;MACzC;MACA,IAAI9H,CAAC,CAAC8K,iBAAiB,EAAE;QACrB,IAAI,CAACF,QAAQ,CAACxC,UAAU,GAAGpI,CAAC,CAACoI,UAAU;MAC3C;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,CAAC2C,WAAW,CAAC,CAAC;EACtB;EACAT,iBAAiBA,CAACC,MAAM,EAAE;IACtB,IAAI,CAAC/H,WAAW,CAACiG,oBAAoB,CAAC8B,MAAM,CAAC;EACjD;EACAG,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAAClI,WAAW,CAAC8G,wBAAwB,CAAC,CAAC;EACtD;EACAyB,WAAWA,CAAA,EAAG;IACV;IACA,IAAI,CAACxF,mBAAmB,CAAC;MACrByF,KAAK,EAAE,IAAI,CAACJ,QAAQ,CAACK,WAAW;MAChCC,WAAW,EAAE,IAAI,CAACN,QAAQ,CAACM,WAAW;MACtCC,MAAM,EAAE,IAAI,CAACP,QAAQ,CAACQ,YAAY;MAClCC,YAAY,EAAE,IAAI,CAACT,QAAQ,CAACS;IAChC,CAAC,CAAC;IACF,IAAI,CAACf,iBAAiB,CAAC;MACnBlC,UAAU,EAAE,IAAI,CAACwC,QAAQ,CAACxC,UAAU;MACpCN,SAAS,EAAE,IAAI,CAAC8C,QAAQ,CAAC9C;IAC7B,CAAC,CAAC;EACN;AACJ;AACA,SAASvF,cAAcA,CAAC+I,IAAI,EAAE;EAC1B,MAAMC,MAAM,GAAG;IACXjF,UAAU,EAAG,OAAOgF,IAAI,CAAChF,UAAU,KAAK,WAAW,GAAGgF,IAAI,CAAChF,UAAU,GAAG,KAAM;IAC9E9C,SAAS,EAAG,OAAO8H,IAAI,CAAC9H,SAAS,KAAK,WAAW,GAAG8H,IAAI,CAAC9H,SAAS,GAAG,EAAG;IACxEK,UAAU,EAAG,OAAOyH,IAAI,CAACzH,UAAU,KAAK,WAAW,GAAGyH,IAAI,CAACzH,UAAU,GAAG,IAAK;IAC7ES,gBAAgB,EAAG,OAAOgH,IAAI,CAAChH,gBAAgB,KAAK,WAAW,GAAGgH,IAAI,CAAChH,gBAAgB,GAAG,IAAK;IAC/F+C,QAAQ,EAAG,OAAOiE,IAAI,CAACjE,QAAQ,KAAK,WAAW,GAAGiE,IAAI,CAACjE,QAAQ,GAAG,KAAM;IACxEuB,oCAAoC,EAAG,OAAO0C,IAAI,CAAC1C,oCAAoC,KAAK,WAAW,GAAG0C,IAAI,CAAC1C,oCAAoC,GAAG,KAAM;IAC5JD,uBAAuB,EAAG,OAAO2C,IAAI,CAAC3C,uBAAuB,KAAK,WAAW,GAAG2C,IAAI,CAAC3C,uBAAuB,GAAG,KAAM;IACrHvB,UAAU,EAAG,OAAOkE,IAAI,CAAClE,UAAU,KAAK,WAAW,GAAGkE,IAAI,CAAClE,UAAU,GAAG,KAAM;IAC9EtB,2BAA2B,EAAG,OAAOwF,IAAI,CAACxF,2BAA2B,KAAK,WAAW,GAAGwF,IAAI,CAACxF,2BAA2B,GAAG,CAAE;IAC7HC,qBAAqB,EAAG,OAAOuF,IAAI,CAACvF,qBAAqB,KAAK,WAAW,GAAGuF,IAAI,CAACvF,qBAAqB,GAAG,CAAE;IAC3GC,qBAAqB,EAAG,OAAOsF,IAAI,CAACtF,qBAAqB,KAAK,WAAW,GAAGsF,IAAI,CAACtF,qBAAqB,GAAG,IAAK;IAC9GuC,sBAAsB,EAAG,OAAO+C,IAAI,CAAC/C,sBAAsB,KAAK,WAAW,GAAG+C,IAAI,CAAC/C,sBAAsB,GAAG,IAAK;IACjHiD,SAAS,EAAG,OAAOF,IAAI,CAACE,SAAS,KAAK,WAAW,GAAGF,IAAI,CAACE,SAAS,GAAG,EAAG;IACxErH,eAAe,EAAG,OAAOmH,IAAI,CAACnH,eAAe,KAAK,WAAW,GAAGmH,IAAI,CAACnH,eAAe,GAAG,IAAK;IAC5F8B,UAAU,EAAG,OAAOqF,IAAI,CAACrF,UAAU,KAAK,WAAW,GAAGqF,IAAI,CAACrF,UAAU,GAAG,CAAC,CAAC,8BAA+B;IACzGE,uBAAuB,EAAG,OAAOmF,IAAI,CAACnF,uBAAuB,KAAK,WAAW,GAAGmF,IAAI,CAACnF,uBAAuB,GAAG,EAAG;IAClHsF,oBAAoB,EAAG,OAAOH,IAAI,CAACG,oBAAoB,KAAK,WAAW,GAAGH,IAAI,CAACG,oBAAoB,GAAG,CAAE;IACxGC,mBAAmB,EAAG,OAAOJ,IAAI,CAACI,mBAAmB,KAAK,WAAW,GAAGJ,IAAI,CAACI,mBAAmB,GAAG,KAAM;IACzGxF,QAAQ,EAAG,OAAOoF,IAAI,CAACpF,QAAQ,KAAK,WAAW,GAAGoF,IAAI,CAACpF,QAAQ,GAAG,CAAC,CAAC,8BAA+B;IACnGE,qBAAqB,EAAG,OAAOkF,IAAI,CAAClF,qBAAqB,KAAK,WAAW,GAAGkF,IAAI,CAAClF,qBAAqB,GAAG,EAAG;IAC5GuF,iBAAiB,EAAG,OAAOL,IAAI,CAACK,iBAAiB,KAAK,WAAW,GAAGL,IAAI,CAACK,iBAAiB,GAAG,KAAM;IACnGC,kBAAkB,EAAG,OAAON,IAAI,CAACM,kBAAkB,KAAK,WAAW,GAAGN,IAAI,CAACM,kBAAkB,GAAG,CAAE;IAClGvF,YAAY,EAAG,OAAOiF,IAAI,CAACjF,YAAY,KAAK,WAAW,GAAGiF,IAAI,CAACjF,YAAY,GAAG;EAClF,CAAC;EACDkF,MAAM,CAACE,oBAAoB,GAAI,OAAOH,IAAI,CAACG,oBAAoB,KAAK,WAAW,GAAGH,IAAI,CAACG,oBAAoB,GAAGF,MAAM,CAACpF,uBAAwB;EAC7IoF,MAAM,CAACK,kBAAkB,GAAI,OAAON,IAAI,CAACM,kBAAkB,KAAK,WAAW,GAAGN,IAAI,CAACM,kBAAkB,GAAGL,MAAM,CAACnF,qBAAsB;EACrI;EACA,IAAI7H,QAAQ,CAACoH,WAAW,EAAE;IACtB4F,MAAM,CAAC/H,SAAS,IAAI,MAAM;EAC9B;EACA,OAAO+H,MAAM;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}