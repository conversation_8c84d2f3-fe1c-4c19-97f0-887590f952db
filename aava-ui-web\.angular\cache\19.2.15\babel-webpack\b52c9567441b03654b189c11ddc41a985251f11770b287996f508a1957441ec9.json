{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Position } from './position.js';\nimport { Range } from './range.js';\n/**\n * Represents a non-negative length of text in terms of line and column count.\n*/\nexport class TextLength {\n  static {\n    this.zero = new TextLength(0, 0);\n  }\n  static betweenPositions(position1, position2) {\n    if (position1.lineNumber === position2.lineNumber) {\n      return new TextLength(0, position2.column - position1.column);\n    } else {\n      return new TextLength(position2.lineNumber - position1.lineNumber, position2.column - 1);\n    }\n  }\n  static ofRange(range) {\n    return TextLength.betweenPositions(range.getStartPosition(), range.getEndPosition());\n  }\n  static ofText(text) {\n    let line = 0;\n    let column = 0;\n    for (const c of text) {\n      if (c === '\\n') {\n        line++;\n        column = 0;\n      } else {\n        column++;\n      }\n    }\n    return new TextLength(line, column);\n  }\n  constructor(lineCount, columnCount) {\n    this.lineCount = lineCount;\n    this.columnCount = columnCount;\n  }\n  isGreaterThanOrEqualTo(other) {\n    if (this.lineCount !== other.lineCount) {\n      return this.lineCount > other.lineCount;\n    }\n    return this.columnCount >= other.columnCount;\n  }\n  createRange(startPosition) {\n    if (this.lineCount === 0) {\n      return new Range(startPosition.lineNumber, startPosition.column, startPosition.lineNumber, startPosition.column + this.columnCount);\n    } else {\n      return new Range(startPosition.lineNumber, startPosition.column, startPosition.lineNumber + this.lineCount, this.columnCount + 1);\n    }\n  }\n  addToPosition(position) {\n    if (this.lineCount === 0) {\n      return new Position(position.lineNumber, position.column + this.columnCount);\n    } else {\n      return new Position(position.lineNumber + this.lineCount, this.columnCount + 1);\n    }\n  }\n  toString() {\n    return `${this.lineCount},${this.columnCount}`;\n  }\n}", "map": {"version": 3, "names": ["Position", "Range", "TextLength", "zero", "betweenPositions", "position1", "position2", "lineNumber", "column", "ofRange", "range", "getStartPosition", "getEndPosition", "ofText", "text", "line", "c", "constructor", "lineCount", "columnCount", "isGreaterThanOrEqualTo", "other", "createRange", "startPosition", "addToPosition", "position", "toString"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/core/textLength.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Position } from './position.js';\nimport { Range } from './range.js';\n/**\n * Represents a non-negative length of text in terms of line and column count.\n*/\nexport class TextLength {\n    static { this.zero = new TextLength(0, 0); }\n    static betweenPositions(position1, position2) {\n        if (position1.lineNumber === position2.lineNumber) {\n            return new TextLength(0, position2.column - position1.column);\n        }\n        else {\n            return new TextLength(position2.lineNumber - position1.lineNumber, position2.column - 1);\n        }\n    }\n    static ofRange(range) {\n        return TextLength.betweenPositions(range.getStartPosition(), range.getEndPosition());\n    }\n    static ofText(text) {\n        let line = 0;\n        let column = 0;\n        for (const c of text) {\n            if (c === '\\n') {\n                line++;\n                column = 0;\n            }\n            else {\n                column++;\n            }\n        }\n        return new TextLength(line, column);\n    }\n    constructor(lineCount, columnCount) {\n        this.lineCount = lineCount;\n        this.columnCount = columnCount;\n    }\n    isGreaterThanOrEqualTo(other) {\n        if (this.lineCount !== other.lineCount) {\n            return this.lineCount > other.lineCount;\n        }\n        return this.columnCount >= other.columnCount;\n    }\n    createRange(startPosition) {\n        if (this.lineCount === 0) {\n            return new Range(startPosition.lineNumber, startPosition.column, startPosition.lineNumber, startPosition.column + this.columnCount);\n        }\n        else {\n            return new Range(startPosition.lineNumber, startPosition.column, startPosition.lineNumber + this.lineCount, this.columnCount + 1);\n        }\n    }\n    addToPosition(position) {\n        if (this.lineCount === 0) {\n            return new Position(position.lineNumber, position.column + this.columnCount);\n        }\n        else {\n            return new Position(position.lineNumber + this.lineCount, this.columnCount + 1);\n        }\n    }\n    toString() {\n        return `${this.lineCount},${this.columnCount}`;\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,KAAK,QAAQ,YAAY;AAClC;AACA;AACA;AACA,OAAO,MAAMC,UAAU,CAAC;EACpB;IAAS,IAAI,CAACC,IAAI,GAAG,IAAID,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC;EAAE;EAC3C,OAAOE,gBAAgBA,CAACC,SAAS,EAAEC,SAAS,EAAE;IAC1C,IAAID,SAAS,CAACE,UAAU,KAAKD,SAAS,CAACC,UAAU,EAAE;MAC/C,OAAO,IAAIL,UAAU,CAAC,CAAC,EAAEI,SAAS,CAACE,MAAM,GAAGH,SAAS,CAACG,MAAM,CAAC;IACjE,CAAC,MACI;MACD,OAAO,IAAIN,UAAU,CAACI,SAAS,CAACC,UAAU,GAAGF,SAAS,CAACE,UAAU,EAAED,SAAS,CAACE,MAAM,GAAG,CAAC,CAAC;IAC5F;EACJ;EACA,OAAOC,OAAOA,CAACC,KAAK,EAAE;IAClB,OAAOR,UAAU,CAACE,gBAAgB,CAACM,KAAK,CAACC,gBAAgB,CAAC,CAAC,EAAED,KAAK,CAACE,cAAc,CAAC,CAAC,CAAC;EACxF;EACA,OAAOC,MAAMA,CAACC,IAAI,EAAE;IAChB,IAAIC,IAAI,GAAG,CAAC;IACZ,IAAIP,MAAM,GAAG,CAAC;IACd,KAAK,MAAMQ,CAAC,IAAIF,IAAI,EAAE;MAClB,IAAIE,CAAC,KAAK,IAAI,EAAE;QACZD,IAAI,EAAE;QACNP,MAAM,GAAG,CAAC;MACd,CAAC,MACI;QACDA,MAAM,EAAE;MACZ;IACJ;IACA,OAAO,IAAIN,UAAU,CAACa,IAAI,EAAEP,MAAM,CAAC;EACvC;EACAS,WAAWA,CAACC,SAAS,EAAEC,WAAW,EAAE;IAChC,IAAI,CAACD,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,WAAW,GAAGA,WAAW;EAClC;EACAC,sBAAsBA,CAACC,KAAK,EAAE;IAC1B,IAAI,IAAI,CAACH,SAAS,KAAKG,KAAK,CAACH,SAAS,EAAE;MACpC,OAAO,IAAI,CAACA,SAAS,GAAGG,KAAK,CAACH,SAAS;IAC3C;IACA,OAAO,IAAI,CAACC,WAAW,IAAIE,KAAK,CAACF,WAAW;EAChD;EACAG,WAAWA,CAACC,aAAa,EAAE;IACvB,IAAI,IAAI,CAACL,SAAS,KAAK,CAAC,EAAE;MACtB,OAAO,IAAIjB,KAAK,CAACsB,aAAa,CAAChB,UAAU,EAAEgB,aAAa,CAACf,MAAM,EAAEe,aAAa,CAAChB,UAAU,EAAEgB,aAAa,CAACf,MAAM,GAAG,IAAI,CAACW,WAAW,CAAC;IACvI,CAAC,MACI;MACD,OAAO,IAAIlB,KAAK,CAACsB,aAAa,CAAChB,UAAU,EAAEgB,aAAa,CAACf,MAAM,EAAEe,aAAa,CAAChB,UAAU,GAAG,IAAI,CAACW,SAAS,EAAE,IAAI,CAACC,WAAW,GAAG,CAAC,CAAC;IACrI;EACJ;EACAK,aAAaA,CAACC,QAAQ,EAAE;IACpB,IAAI,IAAI,CAACP,SAAS,KAAK,CAAC,EAAE;MACtB,OAAO,IAAIlB,QAAQ,CAACyB,QAAQ,CAAClB,UAAU,EAAEkB,QAAQ,CAACjB,MAAM,GAAG,IAAI,CAACW,WAAW,CAAC;IAChF,CAAC,MACI;MACD,OAAO,IAAInB,QAAQ,CAACyB,QAAQ,CAAClB,UAAU,GAAG,IAAI,CAACW,SAAS,EAAE,IAAI,CAACC,WAAW,GAAG,CAAC,CAAC;IACnF;EACJ;EACAO,QAAQA,CAAA,EAAG;IACP,OAAO,GAAG,IAAI,CAACR,SAAS,IAAI,IAAI,CAACC,WAAW,EAAE;EAClD;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}