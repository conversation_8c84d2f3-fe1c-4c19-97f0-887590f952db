{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = this && this.__param || function (paramIndex, decorator) {\n  return function (target, key) {\n    decorator(target, key, paramIndex);\n  };\n};\nimport { FindInput } from '../../../base/browser/ui/findinput/findInput.js';\nimport { ReplaceInput } from '../../../base/browser/ui/findinput/replaceInput.js';\nimport { ContextKeyExpr, IContextKeyService, RawContextKey } from '../../contextkey/common/contextkey.js';\nimport { KeybindingsRegistry } from '../../keybinding/common/keybindingsRegistry.js';\nimport { localize } from '../../../nls.js';\nimport { DisposableStore, toDisposable } from '../../../base/common/lifecycle.js';\nimport { isActiveElement } from '../../../base/browser/dom.js';\nexport const historyNavigationVisible = new RawContextKey('suggestWidgetVisible', false, localize('suggestWidgetVisible', \"Whether suggestion are visible\"));\nconst HistoryNavigationWidgetFocusContext = 'historyNavigationWidgetFocus';\nconst HistoryNavigationForwardsEnablementContext = 'historyNavigationForwardsEnabled';\nconst HistoryNavigationBackwardsEnablementContext = 'historyNavigationBackwardsEnabled';\nlet lastFocusedWidget = undefined;\nconst widgets = [];\nexport function registerAndCreateHistoryNavigationContext(scopedContextKeyService, widget) {\n  if (widgets.includes(widget)) {\n    throw new Error('Cannot register the same widget multiple times');\n  }\n  widgets.push(widget);\n  const disposableStore = new DisposableStore();\n  const historyNavigationWidgetFocus = new RawContextKey(HistoryNavigationWidgetFocusContext, false).bindTo(scopedContextKeyService);\n  const historyNavigationForwardsEnablement = new RawContextKey(HistoryNavigationForwardsEnablementContext, true).bindTo(scopedContextKeyService);\n  const historyNavigationBackwardsEnablement = new RawContextKey(HistoryNavigationBackwardsEnablementContext, true).bindTo(scopedContextKeyService);\n  const onDidFocus = () => {\n    historyNavigationWidgetFocus.set(true);\n    lastFocusedWidget = widget;\n  };\n  const onDidBlur = () => {\n    historyNavigationWidgetFocus.set(false);\n    if (lastFocusedWidget === widget) {\n      lastFocusedWidget = undefined;\n    }\n  };\n  // Check for currently being focused\n  if (isActiveElement(widget.element)) {\n    onDidFocus();\n  }\n  disposableStore.add(widget.onDidFocus(() => onDidFocus()));\n  disposableStore.add(widget.onDidBlur(() => onDidBlur()));\n  disposableStore.add(toDisposable(() => {\n    widgets.splice(widgets.indexOf(widget), 1);\n    onDidBlur();\n  }));\n  return {\n    historyNavigationForwardsEnablement,\n    historyNavigationBackwardsEnablement,\n    dispose() {\n      disposableStore.dispose();\n    }\n  };\n}\nlet ContextScopedFindInput = class ContextScopedFindInput extends FindInput {\n  constructor(container, contextViewProvider, options, contextKeyService) {\n    super(container, contextViewProvider, options);\n    const scopedContextKeyService = this._register(contextKeyService.createScoped(this.inputBox.element));\n    this._register(registerAndCreateHistoryNavigationContext(scopedContextKeyService, this.inputBox));\n  }\n};\nContextScopedFindInput = __decorate([__param(3, IContextKeyService)], ContextScopedFindInput);\nexport { ContextScopedFindInput };\nlet ContextScopedReplaceInput = class ContextScopedReplaceInput extends ReplaceInput {\n  constructor(container, contextViewProvider, options, contextKeyService, showReplaceOptions = false) {\n    super(container, contextViewProvider, showReplaceOptions, options);\n    const scopedContextKeyService = this._register(contextKeyService.createScoped(this.inputBox.element));\n    this._register(registerAndCreateHistoryNavigationContext(scopedContextKeyService, this.inputBox));\n  }\n};\nContextScopedReplaceInput = __decorate([__param(3, IContextKeyService)], ContextScopedReplaceInput);\nexport { ContextScopedReplaceInput };\nKeybindingsRegistry.registerCommandAndKeybindingRule({\n  id: 'history.showPrevious',\n  weight: 200 /* KeybindingWeight.WorkbenchContrib */,\n  when: ContextKeyExpr.and(ContextKeyExpr.has(HistoryNavigationWidgetFocusContext), ContextKeyExpr.equals(HistoryNavigationBackwardsEnablementContext, true), ContextKeyExpr.not('isComposing'), historyNavigationVisible.isEqualTo(false)),\n  primary: 16 /* KeyCode.UpArrow */,\n  secondary: [512 /* KeyMod.Alt */ | 16 /* KeyCode.UpArrow */],\n  handler: accessor => {\n    lastFocusedWidget?.showPreviousValue();\n  }\n});\nKeybindingsRegistry.registerCommandAndKeybindingRule({\n  id: 'history.showNext',\n  weight: 200 /* KeybindingWeight.WorkbenchContrib */,\n  when: ContextKeyExpr.and(ContextKeyExpr.has(HistoryNavigationWidgetFocusContext), ContextKeyExpr.equals(HistoryNavigationForwardsEnablementContext, true), ContextKeyExpr.not('isComposing'), historyNavigationVisible.isEqualTo(false)),\n  primary: 18 /* KeyCode.DownArrow */,\n  secondary: [512 /* KeyMod.Alt */ | 18 /* KeyCode.DownArrow */],\n  handler: accessor => {\n    lastFocusedWidget?.showNextValue();\n  }\n});", "map": {"version": 3, "names": ["__decorate", "decorators", "target", "key", "desc", "c", "arguments", "length", "r", "Object", "getOwnPropertyDescriptor", "d", "Reflect", "decorate", "i", "defineProperty", "__param", "paramIndex", "decorator", "FindInput", "ReplaceInput", "ContextKeyExpr", "IContextKeyService", "RawContextKey", "KeybindingsRegistry", "localize", "DisposableStore", "toDisposable", "isActiveElement", "historyNavigationVisible", "HistoryNavigationWidgetFocusContext", "HistoryNavigationForwardsEnablementContext", "HistoryNavigationBackwardsEnablementContext", "lastFocusedWidget", "undefined", "widgets", "registerAndCreateHistoryNavigationContext", "scopedContextKeyService", "widget", "includes", "Error", "push", "disposableStore", "historyNavigationWidgetFocus", "bindTo", "historyNavigationForwardsEnablement", "historyNavigationBackwardsEnablement", "onDidFocus", "set", "onDidBlur", "element", "add", "splice", "indexOf", "dispose", "ContextScopedFindInput", "constructor", "container", "contextView<PERSON>rovider", "options", "contextKeyService", "_register", "createScoped", "inputBox", "ContextScopedReplaceInput", "showReplaceOptions", "registerCommandAndKeybindingRule", "id", "weight", "when", "and", "has", "equals", "not", "isEqualTo", "primary", "secondary", "handler", "accessor", "showPreviousValue", "showNextValue"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/platform/history/browser/contextScopedHistoryWidget.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = (this && this.__param) || function (paramIndex, decorator) {\n    return function (target, key) { decorator(target, key, paramIndex); }\n};\nimport { FindInput } from '../../../base/browser/ui/findinput/findInput.js';\nimport { ReplaceInput } from '../../../base/browser/ui/findinput/replaceInput.js';\nimport { ContextKeyExpr, IContextKeyService, RawContextKey } from '../../contextkey/common/contextkey.js';\nimport { KeybindingsRegistry } from '../../keybinding/common/keybindingsRegistry.js';\nimport { localize } from '../../../nls.js';\nimport { DisposableStore, toDisposable } from '../../../base/common/lifecycle.js';\nimport { isActiveElement } from '../../../base/browser/dom.js';\nexport const historyNavigationVisible = new RawContextKey('suggestWidgetVisible', false, localize('suggestWidgetVisible', \"Whether suggestion are visible\"));\nconst HistoryNavigationWidgetFocusContext = 'historyNavigationWidgetFocus';\nconst HistoryNavigationForwardsEnablementContext = 'historyNavigationForwardsEnabled';\nconst HistoryNavigationBackwardsEnablementContext = 'historyNavigationBackwardsEnabled';\nlet lastFocusedWidget = undefined;\nconst widgets = [];\nexport function registerAndCreateHistoryNavigationContext(scopedContextKeyService, widget) {\n    if (widgets.includes(widget)) {\n        throw new Error('Cannot register the same widget multiple times');\n    }\n    widgets.push(widget);\n    const disposableStore = new DisposableStore();\n    const historyNavigationWidgetFocus = new RawContextKey(HistoryNavigationWidgetFocusContext, false).bindTo(scopedContextKeyService);\n    const historyNavigationForwardsEnablement = new RawContextKey(HistoryNavigationForwardsEnablementContext, true).bindTo(scopedContextKeyService);\n    const historyNavigationBackwardsEnablement = new RawContextKey(HistoryNavigationBackwardsEnablementContext, true).bindTo(scopedContextKeyService);\n    const onDidFocus = () => {\n        historyNavigationWidgetFocus.set(true);\n        lastFocusedWidget = widget;\n    };\n    const onDidBlur = () => {\n        historyNavigationWidgetFocus.set(false);\n        if (lastFocusedWidget === widget) {\n            lastFocusedWidget = undefined;\n        }\n    };\n    // Check for currently being focused\n    if (isActiveElement(widget.element)) {\n        onDidFocus();\n    }\n    disposableStore.add(widget.onDidFocus(() => onDidFocus()));\n    disposableStore.add(widget.onDidBlur(() => onDidBlur()));\n    disposableStore.add(toDisposable(() => {\n        widgets.splice(widgets.indexOf(widget), 1);\n        onDidBlur();\n    }));\n    return {\n        historyNavigationForwardsEnablement,\n        historyNavigationBackwardsEnablement,\n        dispose() {\n            disposableStore.dispose();\n        }\n    };\n}\nlet ContextScopedFindInput = class ContextScopedFindInput extends FindInput {\n    constructor(container, contextViewProvider, options, contextKeyService) {\n        super(container, contextViewProvider, options);\n        const scopedContextKeyService = this._register(contextKeyService.createScoped(this.inputBox.element));\n        this._register(registerAndCreateHistoryNavigationContext(scopedContextKeyService, this.inputBox));\n    }\n};\nContextScopedFindInput = __decorate([\n    __param(3, IContextKeyService)\n], ContextScopedFindInput);\nexport { ContextScopedFindInput };\nlet ContextScopedReplaceInput = class ContextScopedReplaceInput extends ReplaceInput {\n    constructor(container, contextViewProvider, options, contextKeyService, showReplaceOptions = false) {\n        super(container, contextViewProvider, showReplaceOptions, options);\n        const scopedContextKeyService = this._register(contextKeyService.createScoped(this.inputBox.element));\n        this._register(registerAndCreateHistoryNavigationContext(scopedContextKeyService, this.inputBox));\n    }\n};\nContextScopedReplaceInput = __decorate([\n    __param(3, IContextKeyService)\n], ContextScopedReplaceInput);\nexport { ContextScopedReplaceInput };\nKeybindingsRegistry.registerCommandAndKeybindingRule({\n    id: 'history.showPrevious',\n    weight: 200 /* KeybindingWeight.WorkbenchContrib */,\n    when: ContextKeyExpr.and(ContextKeyExpr.has(HistoryNavigationWidgetFocusContext), ContextKeyExpr.equals(HistoryNavigationBackwardsEnablementContext, true), ContextKeyExpr.not('isComposing'), historyNavigationVisible.isEqualTo(false)),\n    primary: 16 /* KeyCode.UpArrow */,\n    secondary: [512 /* KeyMod.Alt */ | 16 /* KeyCode.UpArrow */],\n    handler: (accessor) => {\n        lastFocusedWidget?.showPreviousValue();\n    }\n});\nKeybindingsRegistry.registerCommandAndKeybindingRule({\n    id: 'history.showNext',\n    weight: 200 /* KeybindingWeight.WorkbenchContrib */,\n    when: ContextKeyExpr.and(ContextKeyExpr.has(HistoryNavigationWidgetFocusContext), ContextKeyExpr.equals(HistoryNavigationForwardsEnablementContext, true), ContextKeyExpr.not('isComposing'), historyNavigationVisible.isEqualTo(false)),\n    primary: 18 /* KeyCode.DownArrow */,\n    secondary: [512 /* KeyMod.Alt */ | 18 /* KeyCode.DownArrow */],\n    handler: (accessor) => {\n        lastFocusedWidget?.showNextValue();\n    }\n});\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,IAAIA,UAAU,GAAI,IAAI,IAAI,IAAI,CAACA,UAAU,IAAK,UAAUC,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAE;EACnF,IAAIC,CAAC,GAAGC,SAAS,CAACC,MAAM;IAAEC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGH,MAAM,GAAGE,IAAI,KAAK,IAAI,GAAGA,IAAI,GAAGK,MAAM,CAACC,wBAAwB,CAACR,MAAM,EAAEC,GAAG,CAAC,GAAGC,IAAI;IAAEO,CAAC;EAC5H,IAAI,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,CAACC,QAAQ,KAAK,UAAU,EAAEL,CAAC,GAAGI,OAAO,CAACC,QAAQ,CAACZ,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,CAAC,CAAC,KAC1H,KAAK,IAAIU,CAAC,GAAGb,UAAU,CAACM,MAAM,GAAG,CAAC,EAAEO,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE,IAAIH,CAAC,GAAGV,UAAU,CAACa,CAAC,CAAC,EAAEN,CAAC,GAAG,CAACH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACH,CAAC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACT,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,GAAGG,CAAC,CAACT,MAAM,EAAEC,GAAG,CAAC,KAAKK,CAAC;EACjJ,OAAOH,CAAC,GAAG,CAAC,IAAIG,CAAC,IAAIC,MAAM,CAACM,cAAc,CAACb,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,EAAEA,CAAC;AACjE,CAAC;AACD,IAAIQ,OAAO,GAAI,IAAI,IAAI,IAAI,CAACA,OAAO,IAAK,UAAUC,UAAU,EAAEC,SAAS,EAAE;EACrE,OAAO,UAAUhB,MAAM,EAAEC,GAAG,EAAE;IAAEe,SAAS,CAAChB,MAAM,EAAEC,GAAG,EAAEc,UAAU,CAAC;EAAE,CAAC;AACzE,CAAC;AACD,SAASE,SAAS,QAAQ,iDAAiD;AAC3E,SAASC,YAAY,QAAQ,oDAAoD;AACjF,SAASC,cAAc,EAAEC,kBAAkB,EAAEC,aAAa,QAAQ,uCAAuC;AACzG,SAASC,mBAAmB,QAAQ,gDAAgD;AACpF,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,eAAe,EAAEC,YAAY,QAAQ,mCAAmC;AACjF,SAASC,eAAe,QAAQ,8BAA8B;AAC9D,OAAO,MAAMC,wBAAwB,GAAG,IAAIN,aAAa,CAAC,sBAAsB,EAAE,KAAK,EAAEE,QAAQ,CAAC,sBAAsB,EAAE,gCAAgC,CAAC,CAAC;AAC5J,MAAMK,mCAAmC,GAAG,8BAA8B;AAC1E,MAAMC,0CAA0C,GAAG,kCAAkC;AACrF,MAAMC,2CAA2C,GAAG,mCAAmC;AACvF,IAAIC,iBAAiB,GAAGC,SAAS;AACjC,MAAMC,OAAO,GAAG,EAAE;AAClB,OAAO,SAASC,yCAAyCA,CAACC,uBAAuB,EAAEC,MAAM,EAAE;EACvF,IAAIH,OAAO,CAACI,QAAQ,CAACD,MAAM,CAAC,EAAE;IAC1B,MAAM,IAAIE,KAAK,CAAC,gDAAgD,CAAC;EACrE;EACAL,OAAO,CAACM,IAAI,CAACH,MAAM,CAAC;EACpB,MAAMI,eAAe,GAAG,IAAIhB,eAAe,CAAC,CAAC;EAC7C,MAAMiB,4BAA4B,GAAG,IAAIpB,aAAa,CAACO,mCAAmC,EAAE,KAAK,CAAC,CAACc,MAAM,CAACP,uBAAuB,CAAC;EAClI,MAAMQ,mCAAmC,GAAG,IAAItB,aAAa,CAACQ,0CAA0C,EAAE,IAAI,CAAC,CAACa,MAAM,CAACP,uBAAuB,CAAC;EAC/I,MAAMS,oCAAoC,GAAG,IAAIvB,aAAa,CAACS,2CAA2C,EAAE,IAAI,CAAC,CAACY,MAAM,CAACP,uBAAuB,CAAC;EACjJ,MAAMU,UAAU,GAAGA,CAAA,KAAM;IACrBJ,4BAA4B,CAACK,GAAG,CAAC,IAAI,CAAC;IACtCf,iBAAiB,GAAGK,MAAM;EAC9B,CAAC;EACD,MAAMW,SAAS,GAAGA,CAAA,KAAM;IACpBN,4BAA4B,CAACK,GAAG,CAAC,KAAK,CAAC;IACvC,IAAIf,iBAAiB,KAAKK,MAAM,EAAE;MAC9BL,iBAAiB,GAAGC,SAAS;IACjC;EACJ,CAAC;EACD;EACA,IAAIN,eAAe,CAACU,MAAM,CAACY,OAAO,CAAC,EAAE;IACjCH,UAAU,CAAC,CAAC;EAChB;EACAL,eAAe,CAACS,GAAG,CAACb,MAAM,CAACS,UAAU,CAAC,MAAMA,UAAU,CAAC,CAAC,CAAC,CAAC;EAC1DL,eAAe,CAACS,GAAG,CAACb,MAAM,CAACW,SAAS,CAAC,MAAMA,SAAS,CAAC,CAAC,CAAC,CAAC;EACxDP,eAAe,CAACS,GAAG,CAACxB,YAAY,CAAC,MAAM;IACnCQ,OAAO,CAACiB,MAAM,CAACjB,OAAO,CAACkB,OAAO,CAACf,MAAM,CAAC,EAAE,CAAC,CAAC;IAC1CW,SAAS,CAAC,CAAC;EACf,CAAC,CAAC,CAAC;EACH,OAAO;IACHJ,mCAAmC;IACnCC,oCAAoC;IACpCQ,OAAOA,CAAA,EAAG;MACNZ,eAAe,CAACY,OAAO,CAAC,CAAC;IAC7B;EACJ,CAAC;AACL;AACA,IAAIC,sBAAsB,GAAG,MAAMA,sBAAsB,SAASpC,SAAS,CAAC;EACxEqC,WAAWA,CAACC,SAAS,EAAEC,mBAAmB,EAAEC,OAAO,EAAEC,iBAAiB,EAAE;IACpE,KAAK,CAACH,SAAS,EAAEC,mBAAmB,EAAEC,OAAO,CAAC;IAC9C,MAAMtB,uBAAuB,GAAG,IAAI,CAACwB,SAAS,CAACD,iBAAiB,CAACE,YAAY,CAAC,IAAI,CAACC,QAAQ,CAACb,OAAO,CAAC,CAAC;IACrG,IAAI,CAACW,SAAS,CAACzB,yCAAyC,CAACC,uBAAuB,EAAE,IAAI,CAAC0B,QAAQ,CAAC,CAAC;EACrG;AACJ,CAAC;AACDR,sBAAsB,GAAGvD,UAAU,CAAC,CAChCgB,OAAO,CAAC,CAAC,EAAEM,kBAAkB,CAAC,CACjC,EAAEiC,sBAAsB,CAAC;AAC1B,SAASA,sBAAsB;AAC/B,IAAIS,yBAAyB,GAAG,MAAMA,yBAAyB,SAAS5C,YAAY,CAAC;EACjFoC,WAAWA,CAACC,SAAS,EAAEC,mBAAmB,EAAEC,OAAO,EAAEC,iBAAiB,EAAEK,kBAAkB,GAAG,KAAK,EAAE;IAChG,KAAK,CAACR,SAAS,EAAEC,mBAAmB,EAAEO,kBAAkB,EAAEN,OAAO,CAAC;IAClE,MAAMtB,uBAAuB,GAAG,IAAI,CAACwB,SAAS,CAACD,iBAAiB,CAACE,YAAY,CAAC,IAAI,CAACC,QAAQ,CAACb,OAAO,CAAC,CAAC;IACrG,IAAI,CAACW,SAAS,CAACzB,yCAAyC,CAACC,uBAAuB,EAAE,IAAI,CAAC0B,QAAQ,CAAC,CAAC;EACrG;AACJ,CAAC;AACDC,yBAAyB,GAAGhE,UAAU,CAAC,CACnCgB,OAAO,CAAC,CAAC,EAAEM,kBAAkB,CAAC,CACjC,EAAE0C,yBAAyB,CAAC;AAC7B,SAASA,yBAAyB;AAClCxC,mBAAmB,CAAC0C,gCAAgC,CAAC;EACjDC,EAAE,EAAE,sBAAsB;EAC1BC,MAAM,EAAE,GAAG,CAAC;EACZC,IAAI,EAAEhD,cAAc,CAACiD,GAAG,CAACjD,cAAc,CAACkD,GAAG,CAACzC,mCAAmC,CAAC,EAAET,cAAc,CAACmD,MAAM,CAACxC,2CAA2C,EAAE,IAAI,CAAC,EAAEX,cAAc,CAACoD,GAAG,CAAC,aAAa,CAAC,EAAE5C,wBAAwB,CAAC6C,SAAS,CAAC,KAAK,CAAC,CAAC;EACzOC,OAAO,EAAE,EAAE,CAAC;EACZC,SAAS,EAAE,CAAC,GAAG,CAAC,mBAAmB,EAAE,CAAC,sBAAsB;EAC5DC,OAAO,EAAGC,QAAQ,IAAK;IACnB7C,iBAAiB,EAAE8C,iBAAiB,CAAC,CAAC;EAC1C;AACJ,CAAC,CAAC;AACFvD,mBAAmB,CAAC0C,gCAAgC,CAAC;EACjDC,EAAE,EAAE,kBAAkB;EACtBC,MAAM,EAAE,GAAG,CAAC;EACZC,IAAI,EAAEhD,cAAc,CAACiD,GAAG,CAACjD,cAAc,CAACkD,GAAG,CAACzC,mCAAmC,CAAC,EAAET,cAAc,CAACmD,MAAM,CAACzC,0CAA0C,EAAE,IAAI,CAAC,EAAEV,cAAc,CAACoD,GAAG,CAAC,aAAa,CAAC,EAAE5C,wBAAwB,CAAC6C,SAAS,CAAC,KAAK,CAAC,CAAC;EACxOC,OAAO,EAAE,EAAE,CAAC;EACZC,SAAS,EAAE,CAAC,GAAG,CAAC,mBAAmB,EAAE,CAAC,wBAAwB;EAC9DC,OAAO,EAAGC,QAAQ,IAAK;IACnB7C,iBAAiB,EAAE+C,aAAa,CAAC,CAAC;EACtC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}