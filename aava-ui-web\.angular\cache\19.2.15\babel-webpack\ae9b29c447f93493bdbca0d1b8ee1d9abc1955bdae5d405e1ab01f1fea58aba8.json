{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Range } from '../../../core/range.js';\nimport { lengthAdd, lengthDiffNonNegative, lengthLessThanEqual, lengthOfString, lengthToObj, positionToLength, toLength } from './length.js';\nexport class TextEditInfo {\n  static fromModelContentChanges(changes) {\n    // Must be sorted in ascending order\n    const edits = changes.map(c => {\n      const range = Range.lift(c.range);\n      return new TextEditInfo(positionToLength(range.getStartPosition()), positionToLength(range.getEndPosition()), lengthOfString(c.text));\n    }).reverse();\n    return edits;\n  }\n  constructor(startOffset, endOffset, newLength) {\n    this.startOffset = startOffset;\n    this.endOffset = endOffset;\n    this.newLength = newLength;\n  }\n  toString() {\n    return `[${lengthToObj(this.startOffset)}...${lengthToObj(this.endOffset)}) -> ${lengthToObj(this.newLength)}`;\n  }\n}\nexport class BeforeEditPositionMapper {\n  /**\n   * @param edits Must be sorted by offset in ascending order.\n  */\n  constructor(edits) {\n    this.nextEditIdx = 0;\n    this.deltaOldToNewLineCount = 0;\n    this.deltaOldToNewColumnCount = 0;\n    this.deltaLineIdxInOld = -1;\n    this.edits = edits.map(edit => TextEditInfoCache.from(edit));\n  }\n  /**\n   * @param offset Must be equal to or greater than the last offset this method has been called with.\n  */\n  getOffsetBeforeChange(offset) {\n    this.adjustNextEdit(offset);\n    return this.translateCurToOld(offset);\n  }\n  /**\n   * @param offset Must be equal to or greater than the last offset this method has been called with.\n   * Returns null if there is no edit anymore.\n  */\n  getDistanceToNextChange(offset) {\n    this.adjustNextEdit(offset);\n    const nextEdit = this.edits[this.nextEditIdx];\n    const nextChangeOffset = nextEdit ? this.translateOldToCur(nextEdit.offsetObj) : null;\n    if (nextChangeOffset === null) {\n      return null;\n    }\n    return lengthDiffNonNegative(offset, nextChangeOffset);\n  }\n  translateOldToCur(oldOffsetObj) {\n    if (oldOffsetObj.lineCount === this.deltaLineIdxInOld) {\n      return toLength(oldOffsetObj.lineCount + this.deltaOldToNewLineCount, oldOffsetObj.columnCount + this.deltaOldToNewColumnCount);\n    } else {\n      return toLength(oldOffsetObj.lineCount + this.deltaOldToNewLineCount, oldOffsetObj.columnCount);\n    }\n  }\n  translateCurToOld(newOffset) {\n    const offsetObj = lengthToObj(newOffset);\n    if (offsetObj.lineCount - this.deltaOldToNewLineCount === this.deltaLineIdxInOld) {\n      return toLength(offsetObj.lineCount - this.deltaOldToNewLineCount, offsetObj.columnCount - this.deltaOldToNewColumnCount);\n    } else {\n      return toLength(offsetObj.lineCount - this.deltaOldToNewLineCount, offsetObj.columnCount);\n    }\n  }\n  adjustNextEdit(offset) {\n    while (this.nextEditIdx < this.edits.length) {\n      const nextEdit = this.edits[this.nextEditIdx];\n      // After applying the edit, what is its end offset (considering all previous edits)?\n      const nextEditEndOffsetInCur = this.translateOldToCur(nextEdit.endOffsetAfterObj);\n      if (lengthLessThanEqual(nextEditEndOffsetInCur, offset)) {\n        // We are after the edit, skip it\n        this.nextEditIdx++;\n        const nextEditEndOffsetInCurObj = lengthToObj(nextEditEndOffsetInCur);\n        // Before applying the edit, what is its end offset (considering all previous edits)?\n        const nextEditEndOffsetBeforeInCurObj = lengthToObj(this.translateOldToCur(nextEdit.endOffsetBeforeObj));\n        const lineDelta = nextEditEndOffsetInCurObj.lineCount - nextEditEndOffsetBeforeInCurObj.lineCount;\n        this.deltaOldToNewLineCount += lineDelta;\n        const previousColumnDelta = this.deltaLineIdxInOld === nextEdit.endOffsetBeforeObj.lineCount ? this.deltaOldToNewColumnCount : 0;\n        const columnDelta = nextEditEndOffsetInCurObj.columnCount - nextEditEndOffsetBeforeInCurObj.columnCount;\n        this.deltaOldToNewColumnCount = previousColumnDelta + columnDelta;\n        this.deltaLineIdxInOld = nextEdit.endOffsetBeforeObj.lineCount;\n      } else {\n        // We are in or before the edit.\n        break;\n      }\n    }\n  }\n}\nclass TextEditInfoCache {\n  static from(edit) {\n    return new TextEditInfoCache(edit.startOffset, edit.endOffset, edit.newLength);\n  }\n  constructor(startOffset, endOffset, textLength) {\n    this.endOffsetBeforeObj = lengthToObj(endOffset);\n    this.endOffsetAfterObj = lengthToObj(lengthAdd(startOffset, textLength));\n    this.offsetObj = lengthToObj(startOffset);\n  }\n}", "map": {"version": 3, "names": ["Range", "lengthAdd", "lengthDiffNonNegative", "lengthLessThanEqual", "lengthOfString", "lengthToObj", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "to<PERSON><PERSON><PERSON>", "TextEditInfo", "fromModelContentChanges", "changes", "edits", "map", "c", "range", "lift", "getStartPosition", "getEndPosition", "text", "reverse", "constructor", "startOffset", "endOffset", "<PERSON><PERSON><PERSON><PERSON>", "toString", "BeforeEditPositionMapper", "nextEditIdx", "deltaOldToNewLineCount", "deltaOldToNewColumnCount", "deltaLineIdxInOld", "edit", "TextEditInfoCache", "from", "getOffsetBeforeChange", "offset", "adjustNextEdit", "translateCurToOld", "getDistanceToNextChange", "nextEdit", "nextChangeOffset", "translateOldToCur", "offsetObj", "oldOffsetObj", "lineCount", "columnCount", "newOffset", "length", "nextEditEndOffsetInCur", "endOffsetAfterObj", "nextEditEndOffsetInCurObj", "nextEditEndOffsetBeforeInCurObj", "endOffsetBeforeObj", "lineDelta", "previousColumnDelta", "columnDel<PERSON>", "textLength"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/beforeEditPositionMapper.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Range } from '../../../core/range.js';\nimport { lengthAdd, lengthDiffNonNegative, lengthLessThanEqual, lengthOfString, lengthToObj, positionToLength, toLength } from './length.js';\nexport class TextEditInfo {\n    static fromModelContentChanges(changes) {\n        // Must be sorted in ascending order\n        const edits = changes.map(c => {\n            const range = Range.lift(c.range);\n            return new TextEditInfo(positionToLength(range.getStartPosition()), positionToLength(range.getEndPosition()), lengthOfString(c.text));\n        }).reverse();\n        return edits;\n    }\n    constructor(startOffset, endOffset, newLength) {\n        this.startOffset = startOffset;\n        this.endOffset = endOffset;\n        this.newLength = newLength;\n    }\n    toString() {\n        return `[${lengthToObj(this.startOffset)}...${lengthToObj(this.endOffset)}) -> ${lengthToObj(this.newLength)}`;\n    }\n}\nexport class BeforeEditPositionMapper {\n    /**\n     * @param edits Must be sorted by offset in ascending order.\n    */\n    constructor(edits) {\n        this.nextEditIdx = 0;\n        this.deltaOldToNewLineCount = 0;\n        this.deltaOldToNewColumnCount = 0;\n        this.deltaLineIdxInOld = -1;\n        this.edits = edits.map(edit => TextEditInfoCache.from(edit));\n    }\n    /**\n     * @param offset Must be equal to or greater than the last offset this method has been called with.\n    */\n    getOffsetBeforeChange(offset) {\n        this.adjustNextEdit(offset);\n        return this.translateCurToOld(offset);\n    }\n    /**\n     * @param offset Must be equal to or greater than the last offset this method has been called with.\n     * Returns null if there is no edit anymore.\n    */\n    getDistanceToNextChange(offset) {\n        this.adjustNextEdit(offset);\n        const nextEdit = this.edits[this.nextEditIdx];\n        const nextChangeOffset = nextEdit ? this.translateOldToCur(nextEdit.offsetObj) : null;\n        if (nextChangeOffset === null) {\n            return null;\n        }\n        return lengthDiffNonNegative(offset, nextChangeOffset);\n    }\n    translateOldToCur(oldOffsetObj) {\n        if (oldOffsetObj.lineCount === this.deltaLineIdxInOld) {\n            return toLength(oldOffsetObj.lineCount + this.deltaOldToNewLineCount, oldOffsetObj.columnCount + this.deltaOldToNewColumnCount);\n        }\n        else {\n            return toLength(oldOffsetObj.lineCount + this.deltaOldToNewLineCount, oldOffsetObj.columnCount);\n        }\n    }\n    translateCurToOld(newOffset) {\n        const offsetObj = lengthToObj(newOffset);\n        if (offsetObj.lineCount - this.deltaOldToNewLineCount === this.deltaLineIdxInOld) {\n            return toLength(offsetObj.lineCount - this.deltaOldToNewLineCount, offsetObj.columnCount - this.deltaOldToNewColumnCount);\n        }\n        else {\n            return toLength(offsetObj.lineCount - this.deltaOldToNewLineCount, offsetObj.columnCount);\n        }\n    }\n    adjustNextEdit(offset) {\n        while (this.nextEditIdx < this.edits.length) {\n            const nextEdit = this.edits[this.nextEditIdx];\n            // After applying the edit, what is its end offset (considering all previous edits)?\n            const nextEditEndOffsetInCur = this.translateOldToCur(nextEdit.endOffsetAfterObj);\n            if (lengthLessThanEqual(nextEditEndOffsetInCur, offset)) {\n                // We are after the edit, skip it\n                this.nextEditIdx++;\n                const nextEditEndOffsetInCurObj = lengthToObj(nextEditEndOffsetInCur);\n                // Before applying the edit, what is its end offset (considering all previous edits)?\n                const nextEditEndOffsetBeforeInCurObj = lengthToObj(this.translateOldToCur(nextEdit.endOffsetBeforeObj));\n                const lineDelta = nextEditEndOffsetInCurObj.lineCount - nextEditEndOffsetBeforeInCurObj.lineCount;\n                this.deltaOldToNewLineCount += lineDelta;\n                const previousColumnDelta = this.deltaLineIdxInOld === nextEdit.endOffsetBeforeObj.lineCount ? this.deltaOldToNewColumnCount : 0;\n                const columnDelta = nextEditEndOffsetInCurObj.columnCount - nextEditEndOffsetBeforeInCurObj.columnCount;\n                this.deltaOldToNewColumnCount = previousColumnDelta + columnDelta;\n                this.deltaLineIdxInOld = nextEdit.endOffsetBeforeObj.lineCount;\n            }\n            else {\n                // We are in or before the edit.\n                break;\n            }\n        }\n    }\n}\nclass TextEditInfoCache {\n    static from(edit) {\n        return new TextEditInfoCache(edit.startOffset, edit.endOffset, edit.newLength);\n    }\n    constructor(startOffset, endOffset, textLength) {\n        this.endOffsetBeforeObj = lengthToObj(endOffset);\n        this.endOffsetAfterObj = lengthToObj(lengthAdd(startOffset, textLength));\n        this.offsetObj = lengthToObj(startOffset);\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,KAAK,QAAQ,wBAAwB;AAC9C,SAASC,SAAS,EAAEC,qBAAqB,EAAEC,mBAAmB,EAAEC,cAAc,EAAEC,WAAW,EAAEC,gBAAgB,EAAEC,QAAQ,QAAQ,aAAa;AAC5I,OAAO,MAAMC,YAAY,CAAC;EACtB,OAAOC,uBAAuBA,CAACC,OAAO,EAAE;IACpC;IACA,MAAMC,KAAK,GAAGD,OAAO,CAACE,GAAG,CAACC,CAAC,IAAI;MAC3B,MAAMC,KAAK,GAAGd,KAAK,CAACe,IAAI,CAACF,CAAC,CAACC,KAAK,CAAC;MACjC,OAAO,IAAIN,YAAY,CAACF,gBAAgB,CAACQ,KAAK,CAACE,gBAAgB,CAAC,CAAC,CAAC,EAAEV,gBAAgB,CAACQ,KAAK,CAACG,cAAc,CAAC,CAAC,CAAC,EAAEb,cAAc,CAACS,CAAC,CAACK,IAAI,CAAC,CAAC;IACzI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;IACZ,OAAOR,KAAK;EAChB;EACAS,WAAWA,CAACC,WAAW,EAAEC,SAAS,EAAEC,SAAS,EAAE;IAC3C,IAAI,CAACF,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,SAAS,GAAGA,SAAS;EAC9B;EACAC,QAAQA,CAAA,EAAG;IACP,OAAO,IAAInB,WAAW,CAAC,IAAI,CAACgB,WAAW,CAAC,MAAMhB,WAAW,CAAC,IAAI,CAACiB,SAAS,CAAC,QAAQjB,WAAW,CAAC,IAAI,CAACkB,SAAS,CAAC,EAAE;EAClH;AACJ;AACA,OAAO,MAAME,wBAAwB,CAAC;EAClC;AACJ;AACA;EACIL,WAAWA,CAACT,KAAK,EAAE;IACf,IAAI,CAACe,WAAW,GAAG,CAAC;IACpB,IAAI,CAACC,sBAAsB,GAAG,CAAC;IAC/B,IAAI,CAACC,wBAAwB,GAAG,CAAC;IACjC,IAAI,CAACC,iBAAiB,GAAG,CAAC,CAAC;IAC3B,IAAI,CAAClB,KAAK,GAAGA,KAAK,CAACC,GAAG,CAACkB,IAAI,IAAIC,iBAAiB,CAACC,IAAI,CAACF,IAAI,CAAC,CAAC;EAChE;EACA;AACJ;AACA;EACIG,qBAAqBA,CAACC,MAAM,EAAE;IAC1B,IAAI,CAACC,cAAc,CAACD,MAAM,CAAC;IAC3B,OAAO,IAAI,CAACE,iBAAiB,CAACF,MAAM,CAAC;EACzC;EACA;AACJ;AACA;AACA;EACIG,uBAAuBA,CAACH,MAAM,EAAE;IAC5B,IAAI,CAACC,cAAc,CAACD,MAAM,CAAC;IAC3B,MAAMI,QAAQ,GAAG,IAAI,CAAC3B,KAAK,CAAC,IAAI,CAACe,WAAW,CAAC;IAC7C,MAAMa,gBAAgB,GAAGD,QAAQ,GAAG,IAAI,CAACE,iBAAiB,CAACF,QAAQ,CAACG,SAAS,CAAC,GAAG,IAAI;IACrF,IAAIF,gBAAgB,KAAK,IAAI,EAAE;MAC3B,OAAO,IAAI;IACf;IACA,OAAOrC,qBAAqB,CAACgC,MAAM,EAAEK,gBAAgB,CAAC;EAC1D;EACAC,iBAAiBA,CAACE,YAAY,EAAE;IAC5B,IAAIA,YAAY,CAACC,SAAS,KAAK,IAAI,CAACd,iBAAiB,EAAE;MACnD,OAAOtB,QAAQ,CAACmC,YAAY,CAACC,SAAS,GAAG,IAAI,CAAChB,sBAAsB,EAAEe,YAAY,CAACE,WAAW,GAAG,IAAI,CAAChB,wBAAwB,CAAC;IACnI,CAAC,MACI;MACD,OAAOrB,QAAQ,CAACmC,YAAY,CAACC,SAAS,GAAG,IAAI,CAAChB,sBAAsB,EAAEe,YAAY,CAACE,WAAW,CAAC;IACnG;EACJ;EACAR,iBAAiBA,CAACS,SAAS,EAAE;IACzB,MAAMJ,SAAS,GAAGpC,WAAW,CAACwC,SAAS,CAAC;IACxC,IAAIJ,SAAS,CAACE,SAAS,GAAG,IAAI,CAAChB,sBAAsB,KAAK,IAAI,CAACE,iBAAiB,EAAE;MAC9E,OAAOtB,QAAQ,CAACkC,SAAS,CAACE,SAAS,GAAG,IAAI,CAAChB,sBAAsB,EAAEc,SAAS,CAACG,WAAW,GAAG,IAAI,CAAChB,wBAAwB,CAAC;IAC7H,CAAC,MACI;MACD,OAAOrB,QAAQ,CAACkC,SAAS,CAACE,SAAS,GAAG,IAAI,CAAChB,sBAAsB,EAAEc,SAAS,CAACG,WAAW,CAAC;IAC7F;EACJ;EACAT,cAAcA,CAACD,MAAM,EAAE;IACnB,OAAO,IAAI,CAACR,WAAW,GAAG,IAAI,CAACf,KAAK,CAACmC,MAAM,EAAE;MACzC,MAAMR,QAAQ,GAAG,IAAI,CAAC3B,KAAK,CAAC,IAAI,CAACe,WAAW,CAAC;MAC7C;MACA,MAAMqB,sBAAsB,GAAG,IAAI,CAACP,iBAAiB,CAACF,QAAQ,CAACU,iBAAiB,CAAC;MACjF,IAAI7C,mBAAmB,CAAC4C,sBAAsB,EAAEb,MAAM,CAAC,EAAE;QACrD;QACA,IAAI,CAACR,WAAW,EAAE;QAClB,MAAMuB,yBAAyB,GAAG5C,WAAW,CAAC0C,sBAAsB,CAAC;QACrE;QACA,MAAMG,+BAA+B,GAAG7C,WAAW,CAAC,IAAI,CAACmC,iBAAiB,CAACF,QAAQ,CAACa,kBAAkB,CAAC,CAAC;QACxG,MAAMC,SAAS,GAAGH,yBAAyB,CAACN,SAAS,GAAGO,+BAA+B,CAACP,SAAS;QACjG,IAAI,CAAChB,sBAAsB,IAAIyB,SAAS;QACxC,MAAMC,mBAAmB,GAAG,IAAI,CAACxB,iBAAiB,KAAKS,QAAQ,CAACa,kBAAkB,CAACR,SAAS,GAAG,IAAI,CAACf,wBAAwB,GAAG,CAAC;QAChI,MAAM0B,WAAW,GAAGL,yBAAyB,CAACL,WAAW,GAAGM,+BAA+B,CAACN,WAAW;QACvG,IAAI,CAAChB,wBAAwB,GAAGyB,mBAAmB,GAAGC,WAAW;QACjE,IAAI,CAACzB,iBAAiB,GAAGS,QAAQ,CAACa,kBAAkB,CAACR,SAAS;MAClE,CAAC,MACI;QACD;QACA;MACJ;IACJ;EACJ;AACJ;AACA,MAAMZ,iBAAiB,CAAC;EACpB,OAAOC,IAAIA,CAACF,IAAI,EAAE;IACd,OAAO,IAAIC,iBAAiB,CAACD,IAAI,CAACT,WAAW,EAAES,IAAI,CAACR,SAAS,EAAEQ,IAAI,CAACP,SAAS,CAAC;EAClF;EACAH,WAAWA,CAACC,WAAW,EAAEC,SAAS,EAAEiC,UAAU,EAAE;IAC5C,IAAI,CAACJ,kBAAkB,GAAG9C,WAAW,CAACiB,SAAS,CAAC;IAChD,IAAI,CAAC0B,iBAAiB,GAAG3C,WAAW,CAACJ,SAAS,CAACoB,WAAW,EAAEkC,UAAU,CAAC,CAAC;IACxE,IAAI,CAACd,SAAS,GAAGpC,WAAW,CAACgB,WAAW,CAAC;EAC7C;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}