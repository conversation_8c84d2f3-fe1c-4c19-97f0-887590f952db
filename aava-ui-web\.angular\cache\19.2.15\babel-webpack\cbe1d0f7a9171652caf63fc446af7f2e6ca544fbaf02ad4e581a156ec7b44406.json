{"ast": null, "code": "import { Registry } from '../../registry/common/platform.js';\n//#region Editor / Resources DND\nexport const CodeDataTransfers = {\n  EDITORS: 'CodeEditors',\n  FILES: 'CodeFiles'\n};\nclass DragAndDropContributionRegistry {}\nexport const Extensions = {\n  DragAndDropContribution: 'workbench.contributions.dragAndDrop'\n};\nRegistry.add(Extensions.DragAndDropContribution, new DragAndDropContributionRegistry());\n//#endregion\n//#region DND Utilities\n/**\n * A singleton to store transfer data during drag & drop operations that are only valid within the application.\n */\nexport class LocalSelectionTransfer {\n  static {\n    this.INSTANCE = new LocalSelectionTransfer();\n  }\n  constructor() {\n    // protect against external instantiation\n  }\n  static getInstance() {\n    return LocalSelectionTransfer.INSTANCE;\n  }\n  hasData(proto) {\n    return proto && proto === this.proto;\n  }\n  getData(proto) {\n    if (this.hasData(proto)) {\n      return this.data;\n    }\n    return undefined;\n  }\n}\n//#endregion", "map": {"version": 3, "names": ["Registry", "CodeDataTransfers", "EDITORS", "FILES", "DragAndDropContributionRegistry", "Extensions", "DragAndDropContribution", "add", "LocalSelectionTransfer", "INSTANCE", "constructor", "getInstance", "hasData", "proto", "getData", "data", "undefined"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/platform/dnd/browser/dnd.js"], "sourcesContent": ["import { Registry } from '../../registry/common/platform.js';\n//#region Editor / Resources DND\nexport const CodeDataTransfers = {\n    EDITORS: 'CodeEditors',\n    FILES: 'CodeFiles'\n};\nclass DragAndDropContributionRegistry {\n}\nexport const Extensions = {\n    DragAndDropContribution: 'workbench.contributions.dragAndDrop'\n};\nRegistry.add(Extensions.DragAndDropContribution, new DragAndDropContributionRegistry());\n//#endregion\n//#region DND Utilities\n/**\n * A singleton to store transfer data during drag & drop operations that are only valid within the application.\n */\nexport class LocalSelectionTransfer {\n    static { this.INSTANCE = new LocalSelectionTransfer(); }\n    constructor() {\n        // protect against external instantiation\n    }\n    static getInstance() {\n        return LocalSelectionTransfer.INSTANCE;\n    }\n    hasData(proto) {\n        return proto && proto === this.proto;\n    }\n    getData(proto) {\n        if (this.hasData(proto)) {\n            return this.data;\n        }\n        return undefined;\n    }\n}\n//#endregion\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,mCAAmC;AAC5D;AACA,OAAO,MAAMC,iBAAiB,GAAG;EAC7BC,OAAO,EAAE,aAAa;EACtBC,KAAK,EAAE;AACX,CAAC;AACD,MAAMC,+BAA+B,CAAC;AAEtC,OAAO,MAAMC,UAAU,GAAG;EACtBC,uBAAuB,EAAE;AAC7B,CAAC;AACDN,QAAQ,CAACO,GAAG,CAACF,UAAU,CAACC,uBAAuB,EAAE,IAAIF,+BAA+B,CAAC,CAAC,CAAC;AACvF;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMI,sBAAsB,CAAC;EAChC;IAAS,IAAI,CAACC,QAAQ,GAAG,IAAID,sBAAsB,CAAC,CAAC;EAAE;EACvDE,WAAWA,CAAA,EAAG;IACV;EAAA;EAEJ,OAAOC,WAAWA,CAAA,EAAG;IACjB,OAAOH,sBAAsB,CAACC,QAAQ;EAC1C;EACAG,OAAOA,CAACC,KAAK,EAAE;IACX,OAAOA,KAAK,IAAIA,KAAK,KAAK,IAAI,CAACA,KAAK;EACxC;EACAC,OAAOA,CAACD,KAAK,EAAE;IACX,IAAI,IAAI,CAACD,OAAO,CAACC,KAAK,CAAC,EAAE;MACrB,OAAO,IAAI,CAACE,IAAI;IACpB;IACA,OAAOC,SAAS;EACpB;AACJ;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}