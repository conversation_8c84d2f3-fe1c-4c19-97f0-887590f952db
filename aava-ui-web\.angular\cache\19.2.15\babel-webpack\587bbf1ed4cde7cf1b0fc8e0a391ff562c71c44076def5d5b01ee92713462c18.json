{"ast": null, "code": "import { addDisposableListener, EventHelper, EventType, reset, trackFocus } from '../../dom.js';\nimport { sanitize } from '../../dompurify/dompurify.js';\nimport { StandardKeyboardEvent } from '../../keyboardEvent.js';\nimport { renderMarkdown, renderStringAsPlaintext } from '../../markdownRenderer.js';\nimport { Gesture, EventType as TouchEventType } from '../../touch.js';\nimport { getDefaultHoverDelegate } from '../hover/hoverDelegateFactory.js';\nimport { renderLabelWithIcons } from '../iconLabel/iconLabels.js';\nimport { Color } from '../../../common/color.js';\nimport { Emitter } from '../../../common/event.js';\nimport { isMarkdownString, markdownStringEqual } from '../../../common/htmlContent.js';\nimport { Disposable } from '../../../common/lifecycle.js';\nimport { ThemeIcon } from '../../../common/themables.js';\nimport './button.css';\nimport { getBaseLayerHoverDelegate } from '../hover/hoverDelegate2.js';\nexport const unthemedButtonStyles = {\n  buttonBackground: '#0E639C',\n  buttonHoverBackground: '#006BB3',\n  buttonSeparator: Color.white.toString(),\n  buttonForeground: Color.white.toString(),\n  buttonBorder: undefined,\n  buttonSecondaryBackground: undefined,\n  buttonSecondaryForeground: undefined,\n  buttonSecondaryHoverBackground: undefined\n};\nexport class Button extends Disposable {\n  get onDidClick() {\n    return this._onDidClick.event;\n  }\n  constructor(container, options) {\n    super();\n    this._label = '';\n    this._onDidClick = this._register(new Emitter());\n    this._onDidEscape = this._register(new Emitter());\n    this.options = options;\n    this._element = document.createElement('a');\n    this._element.classList.add('monaco-button');\n    this._element.tabIndex = 0;\n    this._element.setAttribute('role', 'button');\n    this._element.classList.toggle('secondary', !!options.secondary);\n    const background = options.secondary ? options.buttonSecondaryBackground : options.buttonBackground;\n    const foreground = options.secondary ? options.buttonSecondaryForeground : options.buttonForeground;\n    this._element.style.color = foreground || '';\n    this._element.style.backgroundColor = background || '';\n    if (options.supportShortLabel) {\n      this._labelShortElement = document.createElement('div');\n      this._labelShortElement.classList.add('monaco-button-label-short');\n      this._element.appendChild(this._labelShortElement);\n      this._labelElement = document.createElement('div');\n      this._labelElement.classList.add('monaco-button-label');\n      this._element.appendChild(this._labelElement);\n      this._element.classList.add('monaco-text-button-with-short-label');\n    }\n    if (typeof options.title === 'string') {\n      this.setTitle(options.title);\n    }\n    if (typeof options.ariaLabel === 'string') {\n      this._element.setAttribute('aria-label', options.ariaLabel);\n    }\n    container.appendChild(this._element);\n    this._register(Gesture.addTarget(this._element));\n    [EventType.CLICK, TouchEventType.Tap].forEach(eventType => {\n      this._register(addDisposableListener(this._element, eventType, e => {\n        if (!this.enabled) {\n          EventHelper.stop(e);\n          return;\n        }\n        this._onDidClick.fire(e);\n      }));\n    });\n    this._register(addDisposableListener(this._element, EventType.KEY_DOWN, e => {\n      const event = new StandardKeyboardEvent(e);\n      let eventHandled = false;\n      if (this.enabled && (event.equals(3 /* KeyCode.Enter */) || event.equals(10 /* KeyCode.Space */))) {\n        this._onDidClick.fire(e);\n        eventHandled = true;\n      } else if (event.equals(9 /* KeyCode.Escape */)) {\n        this._onDidEscape.fire(e);\n        this._element.blur();\n        eventHandled = true;\n      }\n      if (eventHandled) {\n        EventHelper.stop(event, true);\n      }\n    }));\n    this._register(addDisposableListener(this._element, EventType.MOUSE_OVER, e => {\n      if (!this._element.classList.contains('disabled')) {\n        this.updateBackground(true);\n      }\n    }));\n    this._register(addDisposableListener(this._element, EventType.MOUSE_OUT, e => {\n      this.updateBackground(false); // restore standard styles\n    }));\n    // Also set hover background when button is focused for feedback\n    this.focusTracker = this._register(trackFocus(this._element));\n    this._register(this.focusTracker.onDidFocus(() => {\n      if (this.enabled) {\n        this.updateBackground(true);\n      }\n    }));\n    this._register(this.focusTracker.onDidBlur(() => {\n      if (this.enabled) {\n        this.updateBackground(false);\n      }\n    }));\n  }\n  dispose() {\n    super.dispose();\n    this._element.remove();\n  }\n  getContentElements(content) {\n    const elements = [];\n    for (let segment of renderLabelWithIcons(content)) {\n      if (typeof segment === 'string') {\n        segment = segment.trim();\n        // Ignore empty segment\n        if (segment === '') {\n          continue;\n        }\n        // Convert string segments to <span> nodes\n        const node = document.createElement('span');\n        node.textContent = segment;\n        elements.push(node);\n      } else {\n        elements.push(segment);\n      }\n    }\n    return elements;\n  }\n  updateBackground(hover) {\n    let background;\n    if (this.options.secondary) {\n      background = hover ? this.options.buttonSecondaryHoverBackground : this.options.buttonSecondaryBackground;\n    } else {\n      background = hover ? this.options.buttonHoverBackground : this.options.buttonBackground;\n    }\n    if (background) {\n      this._element.style.backgroundColor = background;\n    }\n  }\n  get element() {\n    return this._element;\n  }\n  set label(value) {\n    if (this._label === value) {\n      return;\n    }\n    if (isMarkdownString(this._label) && isMarkdownString(value) && markdownStringEqual(this._label, value)) {\n      return;\n    }\n    this._element.classList.add('monaco-text-button');\n    const labelElement = this.options.supportShortLabel ? this._labelElement : this._element;\n    if (isMarkdownString(value)) {\n      const rendered = renderMarkdown(value, {\n        inline: true\n      });\n      rendered.dispose();\n      // Don't include outer `<p>`\n      const root = rendered.element.querySelector('p')?.innerHTML;\n      if (root) {\n        // Only allow a very limited set of inline html tags\n        const sanitized = sanitize(root, {\n          ADD_TAGS: ['b', 'i', 'u', 'code', 'span'],\n          ALLOWED_ATTR: ['class'],\n          RETURN_TRUSTED_TYPE: true\n        });\n        labelElement.innerHTML = sanitized;\n      } else {\n        reset(labelElement);\n      }\n    } else {\n      if (this.options.supportIcons) {\n        reset(labelElement, ...this.getContentElements(value));\n      } else {\n        labelElement.textContent = value;\n      }\n    }\n    let title = '';\n    if (typeof this.options.title === 'string') {\n      title = this.options.title;\n    } else if (this.options.title) {\n      title = renderStringAsPlaintext(value);\n    }\n    this.setTitle(title);\n    if (typeof this.options.ariaLabel === 'string') {\n      this._element.setAttribute('aria-label', this.options.ariaLabel);\n    } else if (this.options.ariaLabel) {\n      this._element.setAttribute('aria-label', title);\n    }\n    this._label = value;\n  }\n  get label() {\n    return this._label;\n  }\n  set icon(icon) {\n    this._element.classList.add(...ThemeIcon.asClassNameArray(icon));\n  }\n  set enabled(value) {\n    if (value) {\n      this._element.classList.remove('disabled');\n      this._element.setAttribute('aria-disabled', String(false));\n      this._element.tabIndex = 0;\n    } else {\n      this._element.classList.add('disabled');\n      this._element.setAttribute('aria-disabled', String(true));\n    }\n  }\n  get enabled() {\n    return !this._element.classList.contains('disabled');\n  }\n  setTitle(title) {\n    if (!this._hover && title !== '') {\n      this._hover = this._register(getBaseLayerHoverDelegate().setupManagedHover(this.options.hoverDelegate ?? getDefaultHoverDelegate('mouse'), this._element, title));\n    } else if (this._hover) {\n      this._hover.update(title);\n    }\n  }\n}", "map": {"version": 3, "names": ["addDisposableListener", "EventHelper", "EventType", "reset", "trackFocus", "sanitize", "StandardKeyboardEvent", "renderMarkdown", "renderStringAsPlaintext", "Gesture", "TouchEventType", "getDefaultHoverDelegate", "renderLabelWithIcons", "Color", "Emitter", "isMarkdownString", "markdownStringEqual", "Disposable", "ThemeIcon", "getBaseLayerHoverDelegate", "unthemedButtonStyles", "buttonBackground", "buttonHoverBackground", "buttonSeparator", "white", "toString", "buttonForeground", "buttonBorder", "undefined", "buttonSecondaryBackground", "buttonSecondaryForeground", "buttonSecondaryHoverBackground", "<PERSON><PERSON>", "onDidClick", "_onDidClick", "event", "constructor", "container", "options", "_label", "_register", "_onDidEscape", "_element", "document", "createElement", "classList", "add", "tabIndex", "setAttribute", "toggle", "secondary", "background", "foreground", "style", "color", "backgroundColor", "supportShortLabel", "_labelShortElement", "append<PERSON><PERSON><PERSON>", "_labelElement", "title", "setTitle", "aria<PERSON><PERSON><PERSON>", "addTarget", "CLICK", "Tap", "for<PERSON>ach", "eventType", "e", "enabled", "stop", "fire", "KEY_DOWN", "eventHandled", "equals", "blur", "MOUSE_OVER", "contains", "updateBackground", "MOUSE_OUT", "focusTracker", "onDidFocus", "onDidBlur", "dispose", "remove", "getContentElements", "content", "elements", "segment", "trim", "node", "textContent", "push", "hover", "element", "label", "value", "labelElement", "rendered", "inline", "root", "querySelector", "innerHTML", "sanitized", "ADD_TAGS", "ALLOWED_ATTR", "RETURN_TRUSTED_TYPE", "supportIcons", "icon", "asClassNameArray", "String", "_hover", "setupManagedHover", "hoverDelegate", "update"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/browser/ui/button/button.js"], "sourcesContent": ["import { addDisposableListener, EventHelper, EventType, reset, trackFocus } from '../../dom.js';\nimport { sanitize } from '../../dompurify/dompurify.js';\nimport { StandardKeyboardEvent } from '../../keyboardEvent.js';\nimport { renderMarkdown, renderStringAsPlaintext } from '../../markdownRenderer.js';\nimport { Gesture, EventType as TouchEventType } from '../../touch.js';\nimport { getDefaultHoverDelegate } from '../hover/hoverDelegateFactory.js';\nimport { renderLabelWithIcons } from '../iconLabel/iconLabels.js';\nimport { Color } from '../../../common/color.js';\nimport { Emitter } from '../../../common/event.js';\nimport { isMarkdownString, markdownStringEqual } from '../../../common/htmlContent.js';\nimport { Disposable } from '../../../common/lifecycle.js';\nimport { ThemeIcon } from '../../../common/themables.js';\nimport './button.css';\nimport { getBaseLayerHoverDelegate } from '../hover/hoverDelegate2.js';\nexport const unthemedButtonStyles = {\n    buttonBackground: '#0E639C',\n    buttonHoverBackground: '#006BB3',\n    buttonSeparator: Color.white.toString(),\n    buttonForeground: Color.white.toString(),\n    buttonBorder: undefined,\n    buttonSecondaryBackground: undefined,\n    buttonSecondaryForeground: undefined,\n    buttonSecondaryHoverBackground: undefined\n};\nexport class Button extends Disposable {\n    get onDidClick() { return this._onDidClick.event; }\n    constructor(container, options) {\n        super();\n        this._label = '';\n        this._onDidClick = this._register(new Emitter());\n        this._onDidEscape = this._register(new Emitter());\n        this.options = options;\n        this._element = document.createElement('a');\n        this._element.classList.add('monaco-button');\n        this._element.tabIndex = 0;\n        this._element.setAttribute('role', 'button');\n        this._element.classList.toggle('secondary', !!options.secondary);\n        const background = options.secondary ? options.buttonSecondaryBackground : options.buttonBackground;\n        const foreground = options.secondary ? options.buttonSecondaryForeground : options.buttonForeground;\n        this._element.style.color = foreground || '';\n        this._element.style.backgroundColor = background || '';\n        if (options.supportShortLabel) {\n            this._labelShortElement = document.createElement('div');\n            this._labelShortElement.classList.add('monaco-button-label-short');\n            this._element.appendChild(this._labelShortElement);\n            this._labelElement = document.createElement('div');\n            this._labelElement.classList.add('monaco-button-label');\n            this._element.appendChild(this._labelElement);\n            this._element.classList.add('monaco-text-button-with-short-label');\n        }\n        if (typeof options.title === 'string') {\n            this.setTitle(options.title);\n        }\n        if (typeof options.ariaLabel === 'string') {\n            this._element.setAttribute('aria-label', options.ariaLabel);\n        }\n        container.appendChild(this._element);\n        this._register(Gesture.addTarget(this._element));\n        [EventType.CLICK, TouchEventType.Tap].forEach(eventType => {\n            this._register(addDisposableListener(this._element, eventType, e => {\n                if (!this.enabled) {\n                    EventHelper.stop(e);\n                    return;\n                }\n                this._onDidClick.fire(e);\n            }));\n        });\n        this._register(addDisposableListener(this._element, EventType.KEY_DOWN, e => {\n            const event = new StandardKeyboardEvent(e);\n            let eventHandled = false;\n            if (this.enabled && (event.equals(3 /* KeyCode.Enter */) || event.equals(10 /* KeyCode.Space */))) {\n                this._onDidClick.fire(e);\n                eventHandled = true;\n            }\n            else if (event.equals(9 /* KeyCode.Escape */)) {\n                this._onDidEscape.fire(e);\n                this._element.blur();\n                eventHandled = true;\n            }\n            if (eventHandled) {\n                EventHelper.stop(event, true);\n            }\n        }));\n        this._register(addDisposableListener(this._element, EventType.MOUSE_OVER, e => {\n            if (!this._element.classList.contains('disabled')) {\n                this.updateBackground(true);\n            }\n        }));\n        this._register(addDisposableListener(this._element, EventType.MOUSE_OUT, e => {\n            this.updateBackground(false); // restore standard styles\n        }));\n        // Also set hover background when button is focused for feedback\n        this.focusTracker = this._register(trackFocus(this._element));\n        this._register(this.focusTracker.onDidFocus(() => { if (this.enabled) {\n            this.updateBackground(true);\n        } }));\n        this._register(this.focusTracker.onDidBlur(() => { if (this.enabled) {\n            this.updateBackground(false);\n        } }));\n    }\n    dispose() {\n        super.dispose();\n        this._element.remove();\n    }\n    getContentElements(content) {\n        const elements = [];\n        for (let segment of renderLabelWithIcons(content)) {\n            if (typeof (segment) === 'string') {\n                segment = segment.trim();\n                // Ignore empty segment\n                if (segment === '') {\n                    continue;\n                }\n                // Convert string segments to <span> nodes\n                const node = document.createElement('span');\n                node.textContent = segment;\n                elements.push(node);\n            }\n            else {\n                elements.push(segment);\n            }\n        }\n        return elements;\n    }\n    updateBackground(hover) {\n        let background;\n        if (this.options.secondary) {\n            background = hover ? this.options.buttonSecondaryHoverBackground : this.options.buttonSecondaryBackground;\n        }\n        else {\n            background = hover ? this.options.buttonHoverBackground : this.options.buttonBackground;\n        }\n        if (background) {\n            this._element.style.backgroundColor = background;\n        }\n    }\n    get element() {\n        return this._element;\n    }\n    set label(value) {\n        if (this._label === value) {\n            return;\n        }\n        if (isMarkdownString(this._label) && isMarkdownString(value) && markdownStringEqual(this._label, value)) {\n            return;\n        }\n        this._element.classList.add('monaco-text-button');\n        const labelElement = this.options.supportShortLabel ? this._labelElement : this._element;\n        if (isMarkdownString(value)) {\n            const rendered = renderMarkdown(value, { inline: true });\n            rendered.dispose();\n            // Don't include outer `<p>`\n            const root = rendered.element.querySelector('p')?.innerHTML;\n            if (root) {\n                // Only allow a very limited set of inline html tags\n                const sanitized = sanitize(root, { ADD_TAGS: ['b', 'i', 'u', 'code', 'span'], ALLOWED_ATTR: ['class'], RETURN_TRUSTED_TYPE: true });\n                labelElement.innerHTML = sanitized;\n            }\n            else {\n                reset(labelElement);\n            }\n        }\n        else {\n            if (this.options.supportIcons) {\n                reset(labelElement, ...this.getContentElements(value));\n            }\n            else {\n                labelElement.textContent = value;\n            }\n        }\n        let title = '';\n        if (typeof this.options.title === 'string') {\n            title = this.options.title;\n        }\n        else if (this.options.title) {\n            title = renderStringAsPlaintext(value);\n        }\n        this.setTitle(title);\n        if (typeof this.options.ariaLabel === 'string') {\n            this._element.setAttribute('aria-label', this.options.ariaLabel);\n        }\n        else if (this.options.ariaLabel) {\n            this._element.setAttribute('aria-label', title);\n        }\n        this._label = value;\n    }\n    get label() {\n        return this._label;\n    }\n    set icon(icon) {\n        this._element.classList.add(...ThemeIcon.asClassNameArray(icon));\n    }\n    set enabled(value) {\n        if (value) {\n            this._element.classList.remove('disabled');\n            this._element.setAttribute('aria-disabled', String(false));\n            this._element.tabIndex = 0;\n        }\n        else {\n            this._element.classList.add('disabled');\n            this._element.setAttribute('aria-disabled', String(true));\n        }\n    }\n    get enabled() {\n        return !this._element.classList.contains('disabled');\n    }\n    setTitle(title) {\n        if (!this._hover && title !== '') {\n            this._hover = this._register(getBaseLayerHoverDelegate().setupManagedHover(this.options.hoverDelegate ?? getDefaultHoverDelegate('mouse'), this._element, title));\n        }\n        else if (this._hover) {\n            this._hover.update(title);\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,qBAAqB,EAAEC,WAAW,EAAEC,SAAS,EAAEC,KAAK,EAAEC,UAAU,QAAQ,cAAc;AAC/F,SAASC,QAAQ,QAAQ,8BAA8B;AACvD,SAASC,qBAAqB,QAAQ,wBAAwB;AAC9D,SAASC,cAAc,EAAEC,uBAAuB,QAAQ,2BAA2B;AACnF,SAASC,OAAO,EAAEP,SAAS,IAAIQ,cAAc,QAAQ,gBAAgB;AACrE,SAASC,uBAAuB,QAAQ,kCAAkC;AAC1E,SAASC,oBAAoB,QAAQ,4BAA4B;AACjE,SAASC,KAAK,QAAQ,0BAA0B;AAChD,SAASC,OAAO,QAAQ,0BAA0B;AAClD,SAASC,gBAAgB,EAAEC,mBAAmB,QAAQ,gCAAgC;AACtF,SAASC,UAAU,QAAQ,8BAA8B;AACzD,SAASC,SAAS,QAAQ,8BAA8B;AACxD,OAAO,cAAc;AACrB,SAASC,yBAAyB,QAAQ,4BAA4B;AACtE,OAAO,MAAMC,oBAAoB,GAAG;EAChCC,gBAAgB,EAAE,SAAS;EAC3BC,qBAAqB,EAAE,SAAS;EAChCC,eAAe,EAAEV,KAAK,CAACW,KAAK,CAACC,QAAQ,CAAC,CAAC;EACvCC,gBAAgB,EAAEb,KAAK,CAACW,KAAK,CAACC,QAAQ,CAAC,CAAC;EACxCE,YAAY,EAAEC,SAAS;EACvBC,yBAAyB,EAAED,SAAS;EACpCE,yBAAyB,EAAEF,SAAS;EACpCG,8BAA8B,EAAEH;AACpC,CAAC;AACD,OAAO,MAAMI,MAAM,SAASf,UAAU,CAAC;EACnC,IAAIgB,UAAUA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACC,WAAW,CAACC,KAAK;EAAE;EAClDC,WAAWA,CAACC,SAAS,EAAEC,OAAO,EAAE;IAC5B,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,MAAM,GAAG,EAAE;IAChB,IAAI,CAACL,WAAW,GAAG,IAAI,CAACM,SAAS,CAAC,IAAI1B,OAAO,CAAC,CAAC,CAAC;IAChD,IAAI,CAAC2B,YAAY,GAAG,IAAI,CAACD,SAAS,CAAC,IAAI1B,OAAO,CAAC,CAAC,CAAC;IACjD,IAAI,CAACwB,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACI,QAAQ,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IAC3C,IAAI,CAACF,QAAQ,CAACG,SAAS,CAACC,GAAG,CAAC,eAAe,CAAC;IAC5C,IAAI,CAACJ,QAAQ,CAACK,QAAQ,GAAG,CAAC;IAC1B,IAAI,CAACL,QAAQ,CAACM,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC;IAC5C,IAAI,CAACN,QAAQ,CAACG,SAAS,CAACI,MAAM,CAAC,WAAW,EAAE,CAAC,CAACX,OAAO,CAACY,SAAS,CAAC;IAChE,MAAMC,UAAU,GAAGb,OAAO,CAACY,SAAS,GAAGZ,OAAO,CAACT,yBAAyB,GAAGS,OAAO,CAACjB,gBAAgB;IACnG,MAAM+B,UAAU,GAAGd,OAAO,CAACY,SAAS,GAAGZ,OAAO,CAACR,yBAAyB,GAAGQ,OAAO,CAACZ,gBAAgB;IACnG,IAAI,CAACgB,QAAQ,CAACW,KAAK,CAACC,KAAK,GAAGF,UAAU,IAAI,EAAE;IAC5C,IAAI,CAACV,QAAQ,CAACW,KAAK,CAACE,eAAe,GAAGJ,UAAU,IAAI,EAAE;IACtD,IAAIb,OAAO,CAACkB,iBAAiB,EAAE;MAC3B,IAAI,CAACC,kBAAkB,GAAGd,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MACvD,IAAI,CAACa,kBAAkB,CAACZ,SAAS,CAACC,GAAG,CAAC,2BAA2B,CAAC;MAClE,IAAI,CAACJ,QAAQ,CAACgB,WAAW,CAAC,IAAI,CAACD,kBAAkB,CAAC;MAClD,IAAI,CAACE,aAAa,GAAGhB,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAClD,IAAI,CAACe,aAAa,CAACd,SAAS,CAACC,GAAG,CAAC,qBAAqB,CAAC;MACvD,IAAI,CAACJ,QAAQ,CAACgB,WAAW,CAAC,IAAI,CAACC,aAAa,CAAC;MAC7C,IAAI,CAACjB,QAAQ,CAACG,SAAS,CAACC,GAAG,CAAC,qCAAqC,CAAC;IACtE;IACA,IAAI,OAAOR,OAAO,CAACsB,KAAK,KAAK,QAAQ,EAAE;MACnC,IAAI,CAACC,QAAQ,CAACvB,OAAO,CAACsB,KAAK,CAAC;IAChC;IACA,IAAI,OAAOtB,OAAO,CAACwB,SAAS,KAAK,QAAQ,EAAE;MACvC,IAAI,CAACpB,QAAQ,CAACM,YAAY,CAAC,YAAY,EAAEV,OAAO,CAACwB,SAAS,CAAC;IAC/D;IACAzB,SAAS,CAACqB,WAAW,CAAC,IAAI,CAAChB,QAAQ,CAAC;IACpC,IAAI,CAACF,SAAS,CAAC/B,OAAO,CAACsD,SAAS,CAAC,IAAI,CAACrB,QAAQ,CAAC,CAAC;IAChD,CAACxC,SAAS,CAAC8D,KAAK,EAAEtD,cAAc,CAACuD,GAAG,CAAC,CAACC,OAAO,CAACC,SAAS,IAAI;MACvD,IAAI,CAAC3B,SAAS,CAACxC,qBAAqB,CAAC,IAAI,CAAC0C,QAAQ,EAAEyB,SAAS,EAAEC,CAAC,IAAI;QAChE,IAAI,CAAC,IAAI,CAACC,OAAO,EAAE;UACfpE,WAAW,CAACqE,IAAI,CAACF,CAAC,CAAC;UACnB;QACJ;QACA,IAAI,CAAClC,WAAW,CAACqC,IAAI,CAACH,CAAC,CAAC;MAC5B,CAAC,CAAC,CAAC;IACP,CAAC,CAAC;IACF,IAAI,CAAC5B,SAAS,CAACxC,qBAAqB,CAAC,IAAI,CAAC0C,QAAQ,EAAExC,SAAS,CAACsE,QAAQ,EAAEJ,CAAC,IAAI;MACzE,MAAMjC,KAAK,GAAG,IAAI7B,qBAAqB,CAAC8D,CAAC,CAAC;MAC1C,IAAIK,YAAY,GAAG,KAAK;MACxB,IAAI,IAAI,CAACJ,OAAO,KAAKlC,KAAK,CAACuC,MAAM,CAAC,CAAC,CAAC,mBAAmB,CAAC,IAAIvC,KAAK,CAACuC,MAAM,CAAC,EAAE,CAAC,mBAAmB,CAAC,CAAC,EAAE;QAC/F,IAAI,CAACxC,WAAW,CAACqC,IAAI,CAACH,CAAC,CAAC;QACxBK,YAAY,GAAG,IAAI;MACvB,CAAC,MACI,IAAItC,KAAK,CAACuC,MAAM,CAAC,CAAC,CAAC,oBAAoB,CAAC,EAAE;QAC3C,IAAI,CAACjC,YAAY,CAAC8B,IAAI,CAACH,CAAC,CAAC;QACzB,IAAI,CAAC1B,QAAQ,CAACiC,IAAI,CAAC,CAAC;QACpBF,YAAY,GAAG,IAAI;MACvB;MACA,IAAIA,YAAY,EAAE;QACdxE,WAAW,CAACqE,IAAI,CAACnC,KAAK,EAAE,IAAI,CAAC;MACjC;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,CAACK,SAAS,CAACxC,qBAAqB,CAAC,IAAI,CAAC0C,QAAQ,EAAExC,SAAS,CAAC0E,UAAU,EAAER,CAAC,IAAI;MAC3E,IAAI,CAAC,IAAI,CAAC1B,QAAQ,CAACG,SAAS,CAACgC,QAAQ,CAAC,UAAU,CAAC,EAAE;QAC/C,IAAI,CAACC,gBAAgB,CAAC,IAAI,CAAC;MAC/B;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,CAACtC,SAAS,CAACxC,qBAAqB,CAAC,IAAI,CAAC0C,QAAQ,EAAExC,SAAS,CAAC6E,SAAS,EAAEX,CAAC,IAAI;MAC1E,IAAI,CAACU,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC;IAClC,CAAC,CAAC,CAAC;IACH;IACA,IAAI,CAACE,YAAY,GAAG,IAAI,CAACxC,SAAS,CAACpC,UAAU,CAAC,IAAI,CAACsC,QAAQ,CAAC,CAAC;IAC7D,IAAI,CAACF,SAAS,CAAC,IAAI,CAACwC,YAAY,CAACC,UAAU,CAAC,MAAM;MAAE,IAAI,IAAI,CAACZ,OAAO,EAAE;QAClE,IAAI,CAACS,gBAAgB,CAAC,IAAI,CAAC;MAC/B;IAAE,CAAC,CAAC,CAAC;IACL,IAAI,CAACtC,SAAS,CAAC,IAAI,CAACwC,YAAY,CAACE,SAAS,CAAC,MAAM;MAAE,IAAI,IAAI,CAACb,OAAO,EAAE;QACjE,IAAI,CAACS,gBAAgB,CAAC,KAAK,CAAC;MAChC;IAAE,CAAC,CAAC,CAAC;EACT;EACAK,OAAOA,CAAA,EAAG;IACN,KAAK,CAACA,OAAO,CAAC,CAAC;IACf,IAAI,CAACzC,QAAQ,CAAC0C,MAAM,CAAC,CAAC;EAC1B;EACAC,kBAAkBA,CAACC,OAAO,EAAE;IACxB,MAAMC,QAAQ,GAAG,EAAE;IACnB,KAAK,IAAIC,OAAO,IAAI5E,oBAAoB,CAAC0E,OAAO,CAAC,EAAE;MAC/C,IAAI,OAAQE,OAAQ,KAAK,QAAQ,EAAE;QAC/BA,OAAO,GAAGA,OAAO,CAACC,IAAI,CAAC,CAAC;QACxB;QACA,IAAID,OAAO,KAAK,EAAE,EAAE;UAChB;QACJ;QACA;QACA,MAAME,IAAI,GAAG/C,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;QAC3C8C,IAAI,CAACC,WAAW,GAAGH,OAAO;QAC1BD,QAAQ,CAACK,IAAI,CAACF,IAAI,CAAC;MACvB,CAAC,MACI;QACDH,QAAQ,CAACK,IAAI,CAACJ,OAAO,CAAC;MAC1B;IACJ;IACA,OAAOD,QAAQ;EACnB;EACAT,gBAAgBA,CAACe,KAAK,EAAE;IACpB,IAAI1C,UAAU;IACd,IAAI,IAAI,CAACb,OAAO,CAACY,SAAS,EAAE;MACxBC,UAAU,GAAG0C,KAAK,GAAG,IAAI,CAACvD,OAAO,CAACP,8BAA8B,GAAG,IAAI,CAACO,OAAO,CAACT,yBAAyB;IAC7G,CAAC,MACI;MACDsB,UAAU,GAAG0C,KAAK,GAAG,IAAI,CAACvD,OAAO,CAAChB,qBAAqB,GAAG,IAAI,CAACgB,OAAO,CAACjB,gBAAgB;IAC3F;IACA,IAAI8B,UAAU,EAAE;MACZ,IAAI,CAACT,QAAQ,CAACW,KAAK,CAACE,eAAe,GAAGJ,UAAU;IACpD;EACJ;EACA,IAAI2C,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACpD,QAAQ;EACxB;EACA,IAAIqD,KAAKA,CAACC,KAAK,EAAE;IACb,IAAI,IAAI,CAACzD,MAAM,KAAKyD,KAAK,EAAE;MACvB;IACJ;IACA,IAAIjF,gBAAgB,CAAC,IAAI,CAACwB,MAAM,CAAC,IAAIxB,gBAAgB,CAACiF,KAAK,CAAC,IAAIhF,mBAAmB,CAAC,IAAI,CAACuB,MAAM,EAAEyD,KAAK,CAAC,EAAE;MACrG;IACJ;IACA,IAAI,CAACtD,QAAQ,CAACG,SAAS,CAACC,GAAG,CAAC,oBAAoB,CAAC;IACjD,MAAMmD,YAAY,GAAG,IAAI,CAAC3D,OAAO,CAACkB,iBAAiB,GAAG,IAAI,CAACG,aAAa,GAAG,IAAI,CAACjB,QAAQ;IACxF,IAAI3B,gBAAgB,CAACiF,KAAK,CAAC,EAAE;MACzB,MAAME,QAAQ,GAAG3F,cAAc,CAACyF,KAAK,EAAE;QAAEG,MAAM,EAAE;MAAK,CAAC,CAAC;MACxDD,QAAQ,CAACf,OAAO,CAAC,CAAC;MAClB;MACA,MAAMiB,IAAI,GAAGF,QAAQ,CAACJ,OAAO,CAACO,aAAa,CAAC,GAAG,CAAC,EAAEC,SAAS;MAC3D,IAAIF,IAAI,EAAE;QACN;QACA,MAAMG,SAAS,GAAGlG,QAAQ,CAAC+F,IAAI,EAAE;UAAEI,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,CAAC;UAAEC,YAAY,EAAE,CAAC,OAAO,CAAC;UAAEC,mBAAmB,EAAE;QAAK,CAAC,CAAC;QACnIT,YAAY,CAACK,SAAS,GAAGC,SAAS;MACtC,CAAC,MACI;QACDpG,KAAK,CAAC8F,YAAY,CAAC;MACvB;IACJ,CAAC,MACI;MACD,IAAI,IAAI,CAAC3D,OAAO,CAACqE,YAAY,EAAE;QAC3BxG,KAAK,CAAC8F,YAAY,EAAE,GAAG,IAAI,CAACZ,kBAAkB,CAACW,KAAK,CAAC,CAAC;MAC1D,CAAC,MACI;QACDC,YAAY,CAACN,WAAW,GAAGK,KAAK;MACpC;IACJ;IACA,IAAIpC,KAAK,GAAG,EAAE;IACd,IAAI,OAAO,IAAI,CAACtB,OAAO,CAACsB,KAAK,KAAK,QAAQ,EAAE;MACxCA,KAAK,GAAG,IAAI,CAACtB,OAAO,CAACsB,KAAK;IAC9B,CAAC,MACI,IAAI,IAAI,CAACtB,OAAO,CAACsB,KAAK,EAAE;MACzBA,KAAK,GAAGpD,uBAAuB,CAACwF,KAAK,CAAC;IAC1C;IACA,IAAI,CAACnC,QAAQ,CAACD,KAAK,CAAC;IACpB,IAAI,OAAO,IAAI,CAACtB,OAAO,CAACwB,SAAS,KAAK,QAAQ,EAAE;MAC5C,IAAI,CAACpB,QAAQ,CAACM,YAAY,CAAC,YAAY,EAAE,IAAI,CAACV,OAAO,CAACwB,SAAS,CAAC;IACpE,CAAC,MACI,IAAI,IAAI,CAACxB,OAAO,CAACwB,SAAS,EAAE;MAC7B,IAAI,CAACpB,QAAQ,CAACM,YAAY,CAAC,YAAY,EAAEY,KAAK,CAAC;IACnD;IACA,IAAI,CAACrB,MAAM,GAAGyD,KAAK;EACvB;EACA,IAAID,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACxD,MAAM;EACtB;EACA,IAAIqE,IAAIA,CAACA,IAAI,EAAE;IACX,IAAI,CAAClE,QAAQ,CAACG,SAAS,CAACC,GAAG,CAAC,GAAG5B,SAAS,CAAC2F,gBAAgB,CAACD,IAAI,CAAC,CAAC;EACpE;EACA,IAAIvC,OAAOA,CAAC2B,KAAK,EAAE;IACf,IAAIA,KAAK,EAAE;MACP,IAAI,CAACtD,QAAQ,CAACG,SAAS,CAACuC,MAAM,CAAC,UAAU,CAAC;MAC1C,IAAI,CAAC1C,QAAQ,CAACM,YAAY,CAAC,eAAe,EAAE8D,MAAM,CAAC,KAAK,CAAC,CAAC;MAC1D,IAAI,CAACpE,QAAQ,CAACK,QAAQ,GAAG,CAAC;IAC9B,CAAC,MACI;MACD,IAAI,CAACL,QAAQ,CAACG,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC;MACvC,IAAI,CAACJ,QAAQ,CAACM,YAAY,CAAC,eAAe,EAAE8D,MAAM,CAAC,IAAI,CAAC,CAAC;IAC7D;EACJ;EACA,IAAIzC,OAAOA,CAAA,EAAG;IACV,OAAO,CAAC,IAAI,CAAC3B,QAAQ,CAACG,SAAS,CAACgC,QAAQ,CAAC,UAAU,CAAC;EACxD;EACAhB,QAAQA,CAACD,KAAK,EAAE;IACZ,IAAI,CAAC,IAAI,CAACmD,MAAM,IAAInD,KAAK,KAAK,EAAE,EAAE;MAC9B,IAAI,CAACmD,MAAM,GAAG,IAAI,CAACvE,SAAS,CAACrB,yBAAyB,CAAC,CAAC,CAAC6F,iBAAiB,CAAC,IAAI,CAAC1E,OAAO,CAAC2E,aAAa,IAAItG,uBAAuB,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC+B,QAAQ,EAAEkB,KAAK,CAAC,CAAC;IACrK,CAAC,MACI,IAAI,IAAI,CAACmD,MAAM,EAAE;MAClB,IAAI,CAACA,MAAM,CAACG,MAAM,CAACtD,KAAK,CAAC;IAC7B;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}