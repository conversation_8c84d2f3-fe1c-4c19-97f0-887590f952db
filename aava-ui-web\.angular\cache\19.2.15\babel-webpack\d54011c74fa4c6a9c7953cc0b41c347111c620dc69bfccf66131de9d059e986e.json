{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = this && this.__param || function (paramIndex, decorator) {\n  return function (target, key) {\n    decorator(target, key, paramIndex);\n  };\n};\nimport { EventType, addDisposableListener, h } from '../../../../../base/browser/dom.js';\nimport { Disposable } from '../../../../../base/common/lifecycle.js';\nimport { autorun, autorunWithStore, derived, observableFromEvent, observableValue } from '../../../../../base/common/observable.js';\nimport { derivedDisposable, derivedWithSetter } from '../../../../../base/common/observableInternal/derived.js';\nimport { DiffEditorSash } from '../components/diffEditorSash.js';\nimport { appendRemoveOnDispose, applyStyle, prependRemoveOnDispose } from '../utils.js';\nimport { EditorGutter } from '../utils/editorGutter.js';\nimport { ActionRunnerWithContext } from '../../multiDiffEditor/utils.js';\nimport { LineRange, LineRangeSet } from '../../../../common/core/lineRange.js';\nimport { OffsetRange } from '../../../../common/core/offsetRange.js';\nimport { Range } from '../../../../common/core/range.js';\nimport { TextEdit } from '../../../../common/core/textEdit.js';\nimport { DetailedLineRangeMapping } from '../../../../common/diff/rangeMapping.js';\nimport { TextModelText } from '../../../../common/model/textModelText.js';\nimport { MenuWorkbenchToolBar } from '../../../../../platform/actions/browser/toolbar.js';\nimport { IMenuService, MenuId } from '../../../../../platform/actions/common/actions.js';\nimport { IContextKeyService } from '../../../../../platform/contextkey/common/contextkey.js';\nimport { WorkbenchHoverDelegate } from '../../../../../platform/hover/browser/hover.js';\nimport { IInstantiationService } from '../../../../../platform/instantiation/common/instantiation.js';\nconst emptyArr = [];\nconst width = 35;\nlet DiffEditorGutter = class DiffEditorGutter extends Disposable {\n  constructor(diffEditorRoot, _diffModel, _editors, _options, _sashLayout, _boundarySashes, _instantiationService, _contextKeyService, _menuService) {\n    super();\n    this._diffModel = _diffModel;\n    this._editors = _editors;\n    this._options = _options;\n    this._sashLayout = _sashLayout;\n    this._boundarySashes = _boundarySashes;\n    this._instantiationService = _instantiationService;\n    this._contextKeyService = _contextKeyService;\n    this._menuService = _menuService;\n    this._menu = this._register(this._menuService.createMenu(MenuId.DiffEditorHunkToolbar, this._contextKeyService));\n    this._actions = observableFromEvent(this, this._menu.onDidChange, () => this._menu.getActions());\n    this._hasActions = this._actions.map(a => a.length > 0);\n    this._showSash = derived(this, reader => this._options.renderSideBySide.read(reader) && this._hasActions.read(reader));\n    this.width = derived(this, reader => this._hasActions.read(reader) ? width : 0);\n    this.elements = h('div.gutter@gutter', {\n      style: {\n        position: 'absolute',\n        height: '100%',\n        width: width + 'px'\n      }\n    }, []);\n    this._currentDiff = derived(this, reader => {\n      const model = this._diffModel.read(reader);\n      if (!model) {\n        return undefined;\n      }\n      const mappings = model.diff.read(reader)?.mappings;\n      const cursorPosition = this._editors.modifiedCursor.read(reader);\n      if (!cursorPosition) {\n        return undefined;\n      }\n      return mappings?.find(m => m.lineRangeMapping.modified.contains(cursorPosition.lineNumber));\n    });\n    this._selectedDiffs = derived(this, reader => {\n      /** @description selectedDiffs */\n      const model = this._diffModel.read(reader);\n      const diff = model?.diff.read(reader);\n      // Return `emptyArr` because it is a constant. [] is always a new array and would trigger a change.\n      if (!diff) {\n        return emptyArr;\n      }\n      const selections = this._editors.modifiedSelections.read(reader);\n      if (selections.every(s => s.isEmpty())) {\n        return emptyArr;\n      }\n      const selectedLineNumbers = new LineRangeSet(selections.map(s => LineRange.fromRangeInclusive(s)));\n      const selectedMappings = diff.mappings.filter(m => m.lineRangeMapping.innerChanges && selectedLineNumbers.intersects(m.lineRangeMapping.modified));\n      const result = selectedMappings.map(mapping => ({\n        mapping,\n        rangeMappings: mapping.lineRangeMapping.innerChanges.filter(c => selections.some(s => Range.areIntersecting(c.modifiedRange, s)))\n      }));\n      if (result.length === 0 || result.every(r => r.rangeMappings.length === 0)) {\n        return emptyArr;\n      }\n      return result;\n    });\n    this._register(prependRemoveOnDispose(diffEditorRoot, this.elements.root));\n    this._register(addDisposableListener(this.elements.root, 'click', () => {\n      this._editors.modified.focus();\n    }));\n    this._register(applyStyle(this.elements.root, {\n      display: this._hasActions.map(a => a ? 'block' : 'none')\n    }));\n    derivedDisposable(this, reader => {\n      const showSash = this._showSash.read(reader);\n      return !showSash ? undefined : new DiffEditorSash(diffEditorRoot, this._sashLayout.dimensions, this._options.enableSplitViewResizing, this._boundarySashes, derivedWithSetter(this, reader => this._sashLayout.sashLeft.read(reader) - width, (v, tx) => this._sashLayout.sashLeft.set(v + width, tx)), () => this._sashLayout.resetSash());\n    }).recomputeInitiallyAndOnChange(this._store);\n    this._register(new EditorGutter(this._editors.modified, this.elements.root, {\n      getIntersectingGutterItems: (range, reader) => {\n        const model = this._diffModel.read(reader);\n        if (!model) {\n          return [];\n        }\n        const diffs = model.diff.read(reader);\n        if (!diffs) {\n          return [];\n        }\n        const selection = this._selectedDiffs.read(reader);\n        if (selection.length > 0) {\n          const m = DetailedLineRangeMapping.fromRangeMappings(selection.flatMap(s => s.rangeMappings));\n          return [new DiffGutterItem(m, true, MenuId.DiffEditorSelectionToolbar, undefined, model.model.original.uri, model.model.modified.uri)];\n        }\n        const currentDiff = this._currentDiff.read(reader);\n        return diffs.mappings.map(m => new DiffGutterItem(m.lineRangeMapping.withInnerChangesFromLineRanges(), m.lineRangeMapping === currentDiff?.lineRangeMapping, MenuId.DiffEditorHunkToolbar, undefined, model.model.original.uri, model.model.modified.uri));\n      },\n      createView: (item, target) => {\n        return this._instantiationService.createInstance(DiffToolBar, item, target, this);\n      }\n    }));\n    this._register(addDisposableListener(this.elements.gutter, EventType.MOUSE_WHEEL, e => {\n      if (this._editors.modified.getOption(104 /* EditorOption.scrollbar */).handleMouseWheel) {\n        this._editors.modified.delegateScrollFromMouseWheelEvent(e);\n      }\n    }, {\n      passive: false\n    }));\n  }\n  computeStagedValue(mapping) {\n    const c = mapping.innerChanges ?? [];\n    const modified = new TextModelText(this._editors.modifiedModel.get());\n    const original = new TextModelText(this._editors.original.getModel());\n    const edit = new TextEdit(c.map(c => c.toTextEdit(modified)));\n    const value = edit.apply(original);\n    return value;\n  }\n  layout(left) {\n    this.elements.gutter.style.left = left + 'px';\n  }\n};\nDiffEditorGutter = __decorate([__param(6, IInstantiationService), __param(7, IContextKeyService), __param(8, IMenuService)], DiffEditorGutter);\nexport { DiffEditorGutter };\nclass DiffGutterItem {\n  constructor(mapping, showAlways, menuId, rangeOverride, originalUri, modifiedUri) {\n    this.mapping = mapping;\n    this.showAlways = showAlways;\n    this.menuId = menuId;\n    this.rangeOverride = rangeOverride;\n    this.originalUri = originalUri;\n    this.modifiedUri = modifiedUri;\n  }\n  get id() {\n    return this.mapping.modified.toString();\n  }\n  get range() {\n    return this.rangeOverride ?? this.mapping.modified;\n  }\n}\nlet DiffToolBar = class DiffToolBar extends Disposable {\n  constructor(_item, target, gutter, instantiationService) {\n    super();\n    this._item = _item;\n    this._elements = h('div.gutterItem', {\n      style: {\n        height: '20px',\n        width: '34px'\n      }\n    }, [h('div.background@background', {}, []), h('div.buttons@buttons', {}, [])]);\n    this._showAlways = this._item.map(this, item => item.showAlways);\n    this._menuId = this._item.map(this, item => item.menuId);\n    this._isSmall = observableValue(this, false);\n    this._lastItemRange = undefined;\n    this._lastViewRange = undefined;\n    const hoverDelegate = this._register(instantiationService.createInstance(WorkbenchHoverDelegate, 'element', true, {\n      position: {\n        hoverPosition: 1 /* HoverPosition.RIGHT */\n      }\n    }));\n    this._register(appendRemoveOnDispose(target, this._elements.root));\n    this._register(autorun(reader => {\n      /** @description update showAlways */\n      const showAlways = this._showAlways.read(reader);\n      this._elements.root.classList.toggle('noTransition', true);\n      this._elements.root.classList.toggle('showAlways', showAlways);\n      setTimeout(() => {\n        this._elements.root.classList.toggle('noTransition', false);\n      }, 0);\n    }));\n    this._register(autorunWithStore((reader, store) => {\n      this._elements.buttons.replaceChildren();\n      const i = store.add(instantiationService.createInstance(MenuWorkbenchToolBar, this._elements.buttons, this._menuId.read(reader), {\n        orientation: 1 /* ActionsOrientation.VERTICAL */,\n        hoverDelegate,\n        toolbarOptions: {\n          primaryGroup: g => g.startsWith('primary')\n        },\n        overflowBehavior: {\n          maxItems: this._isSmall.read(reader) ? 1 : 3\n        },\n        hiddenItemStrategy: 0 /* HiddenItemStrategy.Ignore */,\n        actionRunner: new ActionRunnerWithContext(() => {\n          const item = this._item.get();\n          const mapping = item.mapping;\n          return {\n            mapping,\n            originalWithModifiedChanges: gutter.computeStagedValue(mapping),\n            originalUri: item.originalUri,\n            modifiedUri: item.modifiedUri\n          };\n        }),\n        menuOptions: {\n          shouldForwardArgs: true\n        }\n      }));\n      store.add(i.onDidChangeMenuItems(() => {\n        if (this._lastItemRange) {\n          this.layout(this._lastItemRange, this._lastViewRange);\n        }\n      }));\n    }));\n  }\n  layout(itemRange, viewRange) {\n    this._lastItemRange = itemRange;\n    this._lastViewRange = viewRange;\n    let itemHeight = this._elements.buttons.clientHeight;\n    this._isSmall.set(this._item.get().mapping.original.startLineNumber === 1 && itemRange.length < 30, undefined);\n    // Item might have changed\n    itemHeight = this._elements.buttons.clientHeight;\n    const middleHeight = itemRange.length / 2 - itemHeight / 2;\n    const margin = itemHeight;\n    let effectiveCheckboxTop = itemRange.start + middleHeight;\n    const preferredViewPortRange = OffsetRange.tryCreate(margin, viewRange.endExclusive - margin - itemHeight);\n    const preferredParentRange = OffsetRange.tryCreate(itemRange.start + margin, itemRange.endExclusive - itemHeight - margin);\n    if (preferredParentRange && preferredViewPortRange && preferredParentRange.start < preferredParentRange.endExclusive) {\n      effectiveCheckboxTop = preferredViewPortRange.clip(effectiveCheckboxTop);\n      effectiveCheckboxTop = preferredParentRange.clip(effectiveCheckboxTop);\n    }\n    this._elements.buttons.style.top = `${effectiveCheckboxTop - itemRange.start}px`;\n  }\n};\nDiffToolBar = __decorate([__param(3, IInstantiationService)], DiffToolBar);", "map": {"version": 3, "names": ["__decorate", "decorators", "target", "key", "desc", "c", "arguments", "length", "r", "Object", "getOwnPropertyDescriptor", "d", "Reflect", "decorate", "i", "defineProperty", "__param", "paramIndex", "decorator", "EventType", "addDisposableListener", "h", "Disposable", "autorun", "autorunWithStore", "derived", "observableFromEvent", "observableValue", "derivedDisposable", "derivedWithSetter", "DiffEditorSash", "appendRemoveOnDispose", "applyStyle", "prependRemoveOnDispose", "Editor<PERSON><PERSON>", "ActionRunnerWithContext", "LineRange", "LineRangeSet", "OffsetRange", "Range", "TextEdit", "DetailedLineRangeMapping", "TextModelText", "MenuWorkbenchToolBar", "IMenuService", "MenuId", "IContextKeyService", "WorkbenchHoverDelegate", "IInstantiationService", "emptyArr", "width", "Diff<PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "diffEditorRoot", "_diffModel", "_editors", "_options", "_sashLayout", "_boundarySashes", "_instantiationService", "_contextKeyService", "_menuService", "_menu", "_register", "createMenu", "DiffEditorHunkToolbar", "_actions", "onDidChange", "getActions", "_hasActions", "map", "a", "_showSash", "reader", "renderSideBySide", "read", "elements", "style", "position", "height", "_currentDiff", "model", "undefined", "mappings", "diff", "cursorPosition", "modifiedCursor", "find", "m", "lineRangeMapping", "modified", "contains", "lineNumber", "_selectedDiffs", "selections", "modifiedSelections", "every", "s", "isEmpty", "selectedLineNumbers", "fromRangeInclusive", "selectedMappings", "filter", "innerChanges", "intersects", "result", "mapping", "rangeMappings", "some", "areIntersecting", "modifiedRange", "root", "focus", "display", "showSash", "dimensions", "enableSplitViewResizing", "sashLeft", "v", "tx", "set", "resetSash", "recomputeInitiallyAndOnChange", "_store", "getIntersectingGutterItems", "range", "diffs", "selection", "fromRangeMappings", "flatMap", "DiffGutterItem", "DiffEditorSelectionToolbar", "original", "uri", "currentDiff", "withInnerChangesFromLineRanges", "createView", "item", "createInstance", "DiffToolBar", "gutter", "MOUSE_WHEEL", "e", "getOption", "handleMouseWheel", "delegateScrollFromMouseWheelEvent", "passive", "computeStagedValue", "modifiedModel", "get", "getModel", "edit", "toTextEdit", "value", "apply", "layout", "left", "showAlways", "menuId", "rangeOverride", "originalUri", "modifiedUri", "id", "toString", "_item", "instantiationService", "_elements", "_showAlways", "_menuId", "_isSmall", "_lastItemRange", "_last<PERSON>iewRang<PERSON>", "hoverDelegate", "hoverPosition", "classList", "toggle", "setTimeout", "store", "buttons", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "add", "orientation", "toolbarOptions", "primaryGroup", "g", "startsWith", "overflowBehavior", "maxItems", "hiddenItemStrategy", "actionRunner", "originalWithModifiedChanges", "menuOptions", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onDidChangeMenuItems", "itemRange", "viewRange", "itemHeight", "clientHeight", "startLineNumber", "middleHeight", "margin", "effectiveCheckboxTop", "start", "preferredViewPortRange", "tryCreate", "endExclusive", "preferredParentRange", "clip", "top"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/browser/widget/diffEditor/features/gutterFeature.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = (this && this.__param) || function (paramIndex, decorator) {\n    return function (target, key) { decorator(target, key, paramIndex); }\n};\nimport { EventType, addDisposableListener, h } from '../../../../../base/browser/dom.js';\nimport { Disposable } from '../../../../../base/common/lifecycle.js';\nimport { autorun, autorunWithStore, derived, observableFromEvent, observableValue } from '../../../../../base/common/observable.js';\nimport { derivedDisposable, derivedWithSetter } from '../../../../../base/common/observableInternal/derived.js';\nimport { DiffEditorSash } from '../components/diffEditorSash.js';\nimport { appendRemoveOnDispose, applyStyle, prependRemoveOnDispose } from '../utils.js';\nimport { EditorGutter } from '../utils/editorGutter.js';\nimport { ActionRunnerWithContext } from '../../multiDiffEditor/utils.js';\nimport { LineRange, LineRangeSet } from '../../../../common/core/lineRange.js';\nimport { OffsetRange } from '../../../../common/core/offsetRange.js';\nimport { Range } from '../../../../common/core/range.js';\nimport { TextEdit } from '../../../../common/core/textEdit.js';\nimport { DetailedLineRangeMapping } from '../../../../common/diff/rangeMapping.js';\nimport { TextModelText } from '../../../../common/model/textModelText.js';\nimport { MenuWorkbenchToolBar } from '../../../../../platform/actions/browser/toolbar.js';\nimport { IMenuService, MenuId } from '../../../../../platform/actions/common/actions.js';\nimport { IContextKeyService } from '../../../../../platform/contextkey/common/contextkey.js';\nimport { WorkbenchHoverDelegate } from '../../../../../platform/hover/browser/hover.js';\nimport { IInstantiationService } from '../../../../../platform/instantiation/common/instantiation.js';\nconst emptyArr = [];\nconst width = 35;\nlet DiffEditorGutter = class DiffEditorGutter extends Disposable {\n    constructor(diffEditorRoot, _diffModel, _editors, _options, _sashLayout, _boundarySashes, _instantiationService, _contextKeyService, _menuService) {\n        super();\n        this._diffModel = _diffModel;\n        this._editors = _editors;\n        this._options = _options;\n        this._sashLayout = _sashLayout;\n        this._boundarySashes = _boundarySashes;\n        this._instantiationService = _instantiationService;\n        this._contextKeyService = _contextKeyService;\n        this._menuService = _menuService;\n        this._menu = this._register(this._menuService.createMenu(MenuId.DiffEditorHunkToolbar, this._contextKeyService));\n        this._actions = observableFromEvent(this, this._menu.onDidChange, () => this._menu.getActions());\n        this._hasActions = this._actions.map(a => a.length > 0);\n        this._showSash = derived(this, reader => this._options.renderSideBySide.read(reader) && this._hasActions.read(reader));\n        this.width = derived(this, reader => this._hasActions.read(reader) ? width : 0);\n        this.elements = h('div.gutter@gutter', { style: { position: 'absolute', height: '100%', width: width + 'px' } }, []);\n        this._currentDiff = derived(this, (reader) => {\n            const model = this._diffModel.read(reader);\n            if (!model) {\n                return undefined;\n            }\n            const mappings = model.diff.read(reader)?.mappings;\n            const cursorPosition = this._editors.modifiedCursor.read(reader);\n            if (!cursorPosition) {\n                return undefined;\n            }\n            return mappings?.find(m => m.lineRangeMapping.modified.contains(cursorPosition.lineNumber));\n        });\n        this._selectedDiffs = derived(this, (reader) => {\n            /** @description selectedDiffs */\n            const model = this._diffModel.read(reader);\n            const diff = model?.diff.read(reader);\n            // Return `emptyArr` because it is a constant. [] is always a new array and would trigger a change.\n            if (!diff) {\n                return emptyArr;\n            }\n            const selections = this._editors.modifiedSelections.read(reader);\n            if (selections.every(s => s.isEmpty())) {\n                return emptyArr;\n            }\n            const selectedLineNumbers = new LineRangeSet(selections.map(s => LineRange.fromRangeInclusive(s)));\n            const selectedMappings = diff.mappings.filter(m => m.lineRangeMapping.innerChanges && selectedLineNumbers.intersects(m.lineRangeMapping.modified));\n            const result = selectedMappings.map(mapping => ({\n                mapping,\n                rangeMappings: mapping.lineRangeMapping.innerChanges.filter(c => selections.some(s => Range.areIntersecting(c.modifiedRange, s)))\n            }));\n            if (result.length === 0 || result.every(r => r.rangeMappings.length === 0)) {\n                return emptyArr;\n            }\n            return result;\n        });\n        this._register(prependRemoveOnDispose(diffEditorRoot, this.elements.root));\n        this._register(addDisposableListener(this.elements.root, 'click', () => {\n            this._editors.modified.focus();\n        }));\n        this._register(applyStyle(this.elements.root, { display: this._hasActions.map(a => a ? 'block' : 'none') }));\n        derivedDisposable(this, reader => {\n            const showSash = this._showSash.read(reader);\n            return !showSash ? undefined : new DiffEditorSash(diffEditorRoot, this._sashLayout.dimensions, this._options.enableSplitViewResizing, this._boundarySashes, derivedWithSetter(this, reader => this._sashLayout.sashLeft.read(reader) - width, (v, tx) => this._sashLayout.sashLeft.set(v + width, tx)), () => this._sashLayout.resetSash());\n        }).recomputeInitiallyAndOnChange(this._store);\n        this._register(new EditorGutter(this._editors.modified, this.elements.root, {\n            getIntersectingGutterItems: (range, reader) => {\n                const model = this._diffModel.read(reader);\n                if (!model) {\n                    return [];\n                }\n                const diffs = model.diff.read(reader);\n                if (!diffs) {\n                    return [];\n                }\n                const selection = this._selectedDiffs.read(reader);\n                if (selection.length > 0) {\n                    const m = DetailedLineRangeMapping.fromRangeMappings(selection.flatMap(s => s.rangeMappings));\n                    return [\n                        new DiffGutterItem(m, true, MenuId.DiffEditorSelectionToolbar, undefined, model.model.original.uri, model.model.modified.uri)\n                    ];\n                }\n                const currentDiff = this._currentDiff.read(reader);\n                return diffs.mappings.map(m => new DiffGutterItem(m.lineRangeMapping.withInnerChangesFromLineRanges(), m.lineRangeMapping === currentDiff?.lineRangeMapping, MenuId.DiffEditorHunkToolbar, undefined, model.model.original.uri, model.model.modified.uri));\n            },\n            createView: (item, target) => {\n                return this._instantiationService.createInstance(DiffToolBar, item, target, this);\n            },\n        }));\n        this._register(addDisposableListener(this.elements.gutter, EventType.MOUSE_WHEEL, (e) => {\n            if (this._editors.modified.getOption(104 /* EditorOption.scrollbar */).handleMouseWheel) {\n                this._editors.modified.delegateScrollFromMouseWheelEvent(e);\n            }\n        }, { passive: false }));\n    }\n    computeStagedValue(mapping) {\n        const c = mapping.innerChanges ?? [];\n        const modified = new TextModelText(this._editors.modifiedModel.get());\n        const original = new TextModelText(this._editors.original.getModel());\n        const edit = new TextEdit(c.map(c => c.toTextEdit(modified)));\n        const value = edit.apply(original);\n        return value;\n    }\n    layout(left) {\n        this.elements.gutter.style.left = left + 'px';\n    }\n};\nDiffEditorGutter = __decorate([\n    __param(6, IInstantiationService),\n    __param(7, IContextKeyService),\n    __param(8, IMenuService)\n], DiffEditorGutter);\nexport { DiffEditorGutter };\nclass DiffGutterItem {\n    constructor(mapping, showAlways, menuId, rangeOverride, originalUri, modifiedUri) {\n        this.mapping = mapping;\n        this.showAlways = showAlways;\n        this.menuId = menuId;\n        this.rangeOverride = rangeOverride;\n        this.originalUri = originalUri;\n        this.modifiedUri = modifiedUri;\n    }\n    get id() { return this.mapping.modified.toString(); }\n    get range() { return this.rangeOverride ?? this.mapping.modified; }\n}\nlet DiffToolBar = class DiffToolBar extends Disposable {\n    constructor(_item, target, gutter, instantiationService) {\n        super();\n        this._item = _item;\n        this._elements = h('div.gutterItem', { style: { height: '20px', width: '34px' } }, [\n            h('div.background@background', {}, []),\n            h('div.buttons@buttons', {}, []),\n        ]);\n        this._showAlways = this._item.map(this, item => item.showAlways);\n        this._menuId = this._item.map(this, item => item.menuId);\n        this._isSmall = observableValue(this, false);\n        this._lastItemRange = undefined;\n        this._lastViewRange = undefined;\n        const hoverDelegate = this._register(instantiationService.createInstance(WorkbenchHoverDelegate, 'element', true, { position: { hoverPosition: 1 /* HoverPosition.RIGHT */ } }));\n        this._register(appendRemoveOnDispose(target, this._elements.root));\n        this._register(autorun(reader => {\n            /** @description update showAlways */\n            const showAlways = this._showAlways.read(reader);\n            this._elements.root.classList.toggle('noTransition', true);\n            this._elements.root.classList.toggle('showAlways', showAlways);\n            setTimeout(() => {\n                this._elements.root.classList.toggle('noTransition', false);\n            }, 0);\n        }));\n        this._register(autorunWithStore((reader, store) => {\n            this._elements.buttons.replaceChildren();\n            const i = store.add(instantiationService.createInstance(MenuWorkbenchToolBar, this._elements.buttons, this._menuId.read(reader), {\n                orientation: 1 /* ActionsOrientation.VERTICAL */,\n                hoverDelegate,\n                toolbarOptions: {\n                    primaryGroup: g => g.startsWith('primary'),\n                },\n                overflowBehavior: { maxItems: this._isSmall.read(reader) ? 1 : 3 },\n                hiddenItemStrategy: 0 /* HiddenItemStrategy.Ignore */,\n                actionRunner: new ActionRunnerWithContext(() => {\n                    const item = this._item.get();\n                    const mapping = item.mapping;\n                    return {\n                        mapping,\n                        originalWithModifiedChanges: gutter.computeStagedValue(mapping),\n                        originalUri: item.originalUri,\n                        modifiedUri: item.modifiedUri,\n                    };\n                }),\n                menuOptions: {\n                    shouldForwardArgs: true,\n                },\n            }));\n            store.add(i.onDidChangeMenuItems(() => {\n                if (this._lastItemRange) {\n                    this.layout(this._lastItemRange, this._lastViewRange);\n                }\n            }));\n        }));\n    }\n    layout(itemRange, viewRange) {\n        this._lastItemRange = itemRange;\n        this._lastViewRange = viewRange;\n        let itemHeight = this._elements.buttons.clientHeight;\n        this._isSmall.set(this._item.get().mapping.original.startLineNumber === 1 && itemRange.length < 30, undefined);\n        // Item might have changed\n        itemHeight = this._elements.buttons.clientHeight;\n        const middleHeight = itemRange.length / 2 - itemHeight / 2;\n        const margin = itemHeight;\n        let effectiveCheckboxTop = itemRange.start + middleHeight;\n        const preferredViewPortRange = OffsetRange.tryCreate(margin, viewRange.endExclusive - margin - itemHeight);\n        const preferredParentRange = OffsetRange.tryCreate(itemRange.start + margin, itemRange.endExclusive - itemHeight - margin);\n        if (preferredParentRange && preferredViewPortRange && preferredParentRange.start < preferredParentRange.endExclusive) {\n            effectiveCheckboxTop = preferredViewPortRange.clip(effectiveCheckboxTop);\n            effectiveCheckboxTop = preferredParentRange.clip(effectiveCheckboxTop);\n        }\n        this._elements.buttons.style.top = `${effectiveCheckboxTop - itemRange.start}px`;\n    }\n};\nDiffToolBar = __decorate([\n    __param(3, IInstantiationService)\n], DiffToolBar);\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,IAAIA,UAAU,GAAI,IAAI,IAAI,IAAI,CAACA,UAAU,IAAK,UAAUC,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAE;EACnF,IAAIC,CAAC,GAAGC,SAAS,CAACC,MAAM;IAAEC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGH,MAAM,GAAGE,IAAI,KAAK,IAAI,GAAGA,IAAI,GAAGK,MAAM,CAACC,wBAAwB,CAACR,MAAM,EAAEC,GAAG,CAAC,GAAGC,IAAI;IAAEO,CAAC;EAC5H,IAAI,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,CAACC,QAAQ,KAAK,UAAU,EAAEL,CAAC,GAAGI,OAAO,CAACC,QAAQ,CAACZ,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,CAAC,CAAC,KAC1H,KAAK,IAAIU,CAAC,GAAGb,UAAU,CAACM,MAAM,GAAG,CAAC,EAAEO,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE,IAAIH,CAAC,GAAGV,UAAU,CAACa,CAAC,CAAC,EAAEN,CAAC,GAAG,CAACH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACH,CAAC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACT,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,GAAGG,CAAC,CAACT,MAAM,EAAEC,GAAG,CAAC,KAAKK,CAAC;EACjJ,OAAOH,CAAC,GAAG,CAAC,IAAIG,CAAC,IAAIC,MAAM,CAACM,cAAc,CAACb,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,EAAEA,CAAC;AACjE,CAAC;AACD,IAAIQ,OAAO,GAAI,IAAI,IAAI,IAAI,CAACA,OAAO,IAAK,UAAUC,UAAU,EAAEC,SAAS,EAAE;EACrE,OAAO,UAAUhB,MAAM,EAAEC,GAAG,EAAE;IAAEe,SAAS,CAAChB,MAAM,EAAEC,GAAG,EAAEc,UAAU,CAAC;EAAE,CAAC;AACzE,CAAC;AACD,SAASE,SAAS,EAAEC,qBAAqB,EAAEC,CAAC,QAAQ,oCAAoC;AACxF,SAASC,UAAU,QAAQ,yCAAyC;AACpE,SAASC,OAAO,EAAEC,gBAAgB,EAAEC,OAAO,EAAEC,mBAAmB,EAAEC,eAAe,QAAQ,0CAA0C;AACnI,SAASC,iBAAiB,EAAEC,iBAAiB,QAAQ,0DAA0D;AAC/G,SAASC,cAAc,QAAQ,iCAAiC;AAChE,SAASC,qBAAqB,EAAEC,UAAU,EAAEC,sBAAsB,QAAQ,aAAa;AACvF,SAASC,YAAY,QAAQ,0BAA0B;AACvD,SAASC,uBAAuB,QAAQ,gCAAgC;AACxE,SAASC,SAAS,EAAEC,YAAY,QAAQ,sCAAsC;AAC9E,SAASC,WAAW,QAAQ,wCAAwC;AACpE,SAASC,KAAK,QAAQ,kCAAkC;AACxD,SAASC,QAAQ,QAAQ,qCAAqC;AAC9D,SAASC,wBAAwB,QAAQ,yCAAyC;AAClF,SAASC,aAAa,QAAQ,2CAA2C;AACzE,SAASC,oBAAoB,QAAQ,oDAAoD;AACzF,SAASC,YAAY,EAAEC,MAAM,QAAQ,mDAAmD;AACxF,SAASC,kBAAkB,QAAQ,yDAAyD;AAC5F,SAASC,sBAAsB,QAAQ,gDAAgD;AACvF,SAASC,qBAAqB,QAAQ,+DAA+D;AACrG,MAAMC,QAAQ,GAAG,EAAE;AACnB,MAAMC,KAAK,GAAG,EAAE;AAChB,IAAIC,gBAAgB,GAAG,MAAMA,gBAAgB,SAAS7B,UAAU,CAAC;EAC7D8B,WAAWA,CAACC,cAAc,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,eAAe,EAAEC,qBAAqB,EAAEC,kBAAkB,EAAEC,YAAY,EAAE;IAC/I,KAAK,CAAC,CAAC;IACP,IAAI,CAACP,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,qBAAqB,GAAGA,qBAAqB;IAClD,IAAI,CAACC,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,KAAK,GAAG,IAAI,CAACC,SAAS,CAAC,IAAI,CAACF,YAAY,CAACG,UAAU,CAACnB,MAAM,CAACoB,qBAAqB,EAAE,IAAI,CAACL,kBAAkB,CAAC,CAAC;IAChH,IAAI,CAACM,QAAQ,GAAGxC,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAACoC,KAAK,CAACK,WAAW,EAAE,MAAM,IAAI,CAACL,KAAK,CAACM,UAAU,CAAC,CAAC,CAAC;IAChG,IAAI,CAACC,WAAW,GAAG,IAAI,CAACH,QAAQ,CAACI,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAChE,MAAM,GAAG,CAAC,CAAC;IACvD,IAAI,CAACiE,SAAS,GAAG/C,OAAO,CAAC,IAAI,EAAEgD,MAAM,IAAI,IAAI,CAACjB,QAAQ,CAACkB,gBAAgB,CAACC,IAAI,CAACF,MAAM,CAAC,IAAI,IAAI,CAACJ,WAAW,CAACM,IAAI,CAACF,MAAM,CAAC,CAAC;IACtH,IAAI,CAACvB,KAAK,GAAGzB,OAAO,CAAC,IAAI,EAAEgD,MAAM,IAAI,IAAI,CAACJ,WAAW,CAACM,IAAI,CAACF,MAAM,CAAC,GAAGvB,KAAK,GAAG,CAAC,CAAC;IAC/E,IAAI,CAAC0B,QAAQ,GAAGvD,CAAC,CAAC,mBAAmB,EAAE;MAAEwD,KAAK,EAAE;QAAEC,QAAQ,EAAE,UAAU;QAAEC,MAAM,EAAE,MAAM;QAAE7B,KAAK,EAAEA,KAAK,GAAG;MAAK;IAAE,CAAC,EAAE,EAAE,CAAC;IACpH,IAAI,CAAC8B,YAAY,GAAGvD,OAAO,CAAC,IAAI,EAAGgD,MAAM,IAAK;MAC1C,MAAMQ,KAAK,GAAG,IAAI,CAAC3B,UAAU,CAACqB,IAAI,CAACF,MAAM,CAAC;MAC1C,IAAI,CAACQ,KAAK,EAAE;QACR,OAAOC,SAAS;MACpB;MACA,MAAMC,QAAQ,GAAGF,KAAK,CAACG,IAAI,CAACT,IAAI,CAACF,MAAM,CAAC,EAAEU,QAAQ;MAClD,MAAME,cAAc,GAAG,IAAI,CAAC9B,QAAQ,CAAC+B,cAAc,CAACX,IAAI,CAACF,MAAM,CAAC;MAChE,IAAI,CAACY,cAAc,EAAE;QACjB,OAAOH,SAAS;MACpB;MACA,OAAOC,QAAQ,EAAEI,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,gBAAgB,CAACC,QAAQ,CAACC,QAAQ,CAACN,cAAc,CAACO,UAAU,CAAC,CAAC;IAC/F,CAAC,CAAC;IACF,IAAI,CAACC,cAAc,GAAGpE,OAAO,CAAC,IAAI,EAAGgD,MAAM,IAAK;MAC5C;MACA,MAAMQ,KAAK,GAAG,IAAI,CAAC3B,UAAU,CAACqB,IAAI,CAACF,MAAM,CAAC;MAC1C,MAAMW,IAAI,GAAGH,KAAK,EAAEG,IAAI,CAACT,IAAI,CAACF,MAAM,CAAC;MACrC;MACA,IAAI,CAACW,IAAI,EAAE;QACP,OAAOnC,QAAQ;MACnB;MACA,MAAM6C,UAAU,GAAG,IAAI,CAACvC,QAAQ,CAACwC,kBAAkB,CAACpB,IAAI,CAACF,MAAM,CAAC;MAChE,IAAIqB,UAAU,CAACE,KAAK,CAACC,CAAC,IAAIA,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,EAAE;QACpC,OAAOjD,QAAQ;MACnB;MACA,MAAMkD,mBAAmB,GAAG,IAAI9D,YAAY,CAACyD,UAAU,CAACxB,GAAG,CAAC2B,CAAC,IAAI7D,SAAS,CAACgE,kBAAkB,CAACH,CAAC,CAAC,CAAC,CAAC;MAClG,MAAMI,gBAAgB,GAAGjB,IAAI,CAACD,QAAQ,CAACmB,MAAM,CAACd,CAAC,IAAIA,CAAC,CAACC,gBAAgB,CAACc,YAAY,IAAIJ,mBAAmB,CAACK,UAAU,CAAChB,CAAC,CAACC,gBAAgB,CAACC,QAAQ,CAAC,CAAC;MAClJ,MAAMe,MAAM,GAAGJ,gBAAgB,CAAC/B,GAAG,CAACoC,OAAO,KAAK;QAC5CA,OAAO;QACPC,aAAa,EAAED,OAAO,CAACjB,gBAAgB,CAACc,YAAY,CAACD,MAAM,CAACjG,CAAC,IAAIyF,UAAU,CAACc,IAAI,CAACX,CAAC,IAAI1D,KAAK,CAACsE,eAAe,CAACxG,CAAC,CAACyG,aAAa,EAAEb,CAAC,CAAC,CAAC;MACpI,CAAC,CAAC,CAAC;MACH,IAAIQ,MAAM,CAAClG,MAAM,KAAK,CAAC,IAAIkG,MAAM,CAACT,KAAK,CAACxF,CAAC,IAAIA,CAAC,CAACmG,aAAa,CAACpG,MAAM,KAAK,CAAC,CAAC,EAAE;QACxE,OAAO0C,QAAQ;MACnB;MACA,OAAOwD,MAAM;IACjB,CAAC,CAAC;IACF,IAAI,CAAC1C,SAAS,CAAC9B,sBAAsB,CAACoB,cAAc,EAAE,IAAI,CAACuB,QAAQ,CAACmC,IAAI,CAAC,CAAC;IAC1E,IAAI,CAAChD,SAAS,CAAC3C,qBAAqB,CAAC,IAAI,CAACwD,QAAQ,CAACmC,IAAI,EAAE,OAAO,EAAE,MAAM;MACpE,IAAI,CAACxD,QAAQ,CAACmC,QAAQ,CAACsB,KAAK,CAAC,CAAC;IAClC,CAAC,CAAC,CAAC;IACH,IAAI,CAACjD,SAAS,CAAC/B,UAAU,CAAC,IAAI,CAAC4C,QAAQ,CAACmC,IAAI,EAAE;MAAEE,OAAO,EAAE,IAAI,CAAC5C,WAAW,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,GAAG,OAAO,GAAG,MAAM;IAAE,CAAC,CAAC,CAAC;IAC5G3C,iBAAiB,CAAC,IAAI,EAAE6C,MAAM,IAAI;MAC9B,MAAMyC,QAAQ,GAAG,IAAI,CAAC1C,SAAS,CAACG,IAAI,CAACF,MAAM,CAAC;MAC5C,OAAO,CAACyC,QAAQ,GAAGhC,SAAS,GAAG,IAAIpD,cAAc,CAACuB,cAAc,EAAE,IAAI,CAACI,WAAW,CAAC0D,UAAU,EAAE,IAAI,CAAC3D,QAAQ,CAAC4D,uBAAuB,EAAE,IAAI,CAAC1D,eAAe,EAAE7B,iBAAiB,CAAC,IAAI,EAAE4C,MAAM,IAAI,IAAI,CAAChB,WAAW,CAAC4D,QAAQ,CAAC1C,IAAI,CAACF,MAAM,CAAC,GAAGvB,KAAK,EAAE,CAACoE,CAAC,EAAEC,EAAE,KAAK,IAAI,CAAC9D,WAAW,CAAC4D,QAAQ,CAACG,GAAG,CAACF,CAAC,GAAGpE,KAAK,EAAEqE,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC9D,WAAW,CAACgE,SAAS,CAAC,CAAC,CAAC;IAC/U,CAAC,CAAC,CAACC,6BAA6B,CAAC,IAAI,CAACC,MAAM,CAAC;IAC7C,IAAI,CAAC5D,SAAS,CAAC,IAAI7B,YAAY,CAAC,IAAI,CAACqB,QAAQ,CAACmC,QAAQ,EAAE,IAAI,CAACd,QAAQ,CAACmC,IAAI,EAAE;MACxEa,0BAA0B,EAAEA,CAACC,KAAK,EAAEpD,MAAM,KAAK;QAC3C,MAAMQ,KAAK,GAAG,IAAI,CAAC3B,UAAU,CAACqB,IAAI,CAACF,MAAM,CAAC;QAC1C,IAAI,CAACQ,KAAK,EAAE;UACR,OAAO,EAAE;QACb;QACA,MAAM6C,KAAK,GAAG7C,KAAK,CAACG,IAAI,CAACT,IAAI,CAACF,MAAM,CAAC;QACrC,IAAI,CAACqD,KAAK,EAAE;UACR,OAAO,EAAE;QACb;QACA,MAAMC,SAAS,GAAG,IAAI,CAAClC,cAAc,CAAClB,IAAI,CAACF,MAAM,CAAC;QAClD,IAAIsD,SAAS,CAACxH,MAAM,GAAG,CAAC,EAAE;UACtB,MAAMiF,CAAC,GAAG/C,wBAAwB,CAACuF,iBAAiB,CAACD,SAAS,CAACE,OAAO,CAAChC,CAAC,IAAIA,CAAC,CAACU,aAAa,CAAC,CAAC;UAC7F,OAAO,CACH,IAAIuB,cAAc,CAAC1C,CAAC,EAAE,IAAI,EAAE3C,MAAM,CAACsF,0BAA0B,EAAEjD,SAAS,EAAED,KAAK,CAACA,KAAK,CAACmD,QAAQ,CAACC,GAAG,EAAEpD,KAAK,CAACA,KAAK,CAACS,QAAQ,CAAC2C,GAAG,CAAC,CAChI;QACL;QACA,MAAMC,WAAW,GAAG,IAAI,CAACtD,YAAY,CAACL,IAAI,CAACF,MAAM,CAAC;QAClD,OAAOqD,KAAK,CAAC3C,QAAQ,CAACb,GAAG,CAACkB,CAAC,IAAI,IAAI0C,cAAc,CAAC1C,CAAC,CAACC,gBAAgB,CAAC8C,8BAA8B,CAAC,CAAC,EAAE/C,CAAC,CAACC,gBAAgB,KAAK6C,WAAW,EAAE7C,gBAAgB,EAAE5C,MAAM,CAACoB,qBAAqB,EAAEiB,SAAS,EAAED,KAAK,CAACA,KAAK,CAACmD,QAAQ,CAACC,GAAG,EAAEpD,KAAK,CAACA,KAAK,CAACS,QAAQ,CAAC2C,GAAG,CAAC,CAAC;MAC9P,CAAC;MACDG,UAAU,EAAEA,CAACC,IAAI,EAAEvI,MAAM,KAAK;QAC1B,OAAO,IAAI,CAACyD,qBAAqB,CAAC+E,cAAc,CAACC,WAAW,EAAEF,IAAI,EAAEvI,MAAM,EAAE,IAAI,CAAC;MACrF;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,CAAC6D,SAAS,CAAC3C,qBAAqB,CAAC,IAAI,CAACwD,QAAQ,CAACgE,MAAM,EAAEzH,SAAS,CAAC0H,WAAW,EAAGC,CAAC,IAAK;MACrF,IAAI,IAAI,CAACvF,QAAQ,CAACmC,QAAQ,CAACqD,SAAS,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAACC,gBAAgB,EAAE;QACrF,IAAI,CAACzF,QAAQ,CAACmC,QAAQ,CAACuD,iCAAiC,CAACH,CAAC,CAAC;MAC/D;IACJ,CAAC,EAAE;MAAEI,OAAO,EAAE;IAAM,CAAC,CAAC,CAAC;EAC3B;EACAC,kBAAkBA,CAACzC,OAAO,EAAE;IACxB,MAAMrG,CAAC,GAAGqG,OAAO,CAACH,YAAY,IAAI,EAAE;IACpC,MAAMb,QAAQ,GAAG,IAAIhD,aAAa,CAAC,IAAI,CAACa,QAAQ,CAAC6F,aAAa,CAACC,GAAG,CAAC,CAAC,CAAC;IACrE,MAAMjB,QAAQ,GAAG,IAAI1F,aAAa,CAAC,IAAI,CAACa,QAAQ,CAAC6E,QAAQ,CAACkB,QAAQ,CAAC,CAAC,CAAC;IACrE,MAAMC,IAAI,GAAG,IAAI/G,QAAQ,CAACnC,CAAC,CAACiE,GAAG,CAACjE,CAAC,IAAIA,CAAC,CAACmJ,UAAU,CAAC9D,QAAQ,CAAC,CAAC,CAAC;IAC7D,MAAM+D,KAAK,GAAGF,IAAI,CAACG,KAAK,CAACtB,QAAQ,CAAC;IAClC,OAAOqB,KAAK;EAChB;EACAE,MAAMA,CAACC,IAAI,EAAE;IACT,IAAI,CAAChF,QAAQ,CAACgE,MAAM,CAAC/D,KAAK,CAAC+E,IAAI,GAAGA,IAAI,GAAG,IAAI;EACjD;AACJ,CAAC;AACDzG,gBAAgB,GAAGnD,UAAU,CAAC,CAC1BgB,OAAO,CAAC,CAAC,EAAEgC,qBAAqB,CAAC,EACjChC,OAAO,CAAC,CAAC,EAAE8B,kBAAkB,CAAC,EAC9B9B,OAAO,CAAC,CAAC,EAAE4B,YAAY,CAAC,CAC3B,EAAEO,gBAAgB,CAAC;AACpB,SAASA,gBAAgB;AACzB,MAAM+E,cAAc,CAAC;EACjB9E,WAAWA,CAACsD,OAAO,EAAEmD,UAAU,EAAEC,MAAM,EAAEC,aAAa,EAAEC,WAAW,EAAEC,WAAW,EAAE;IAC9E,IAAI,CAACvD,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACmD,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,WAAW,GAAGA,WAAW;EAClC;EACA,IAAIC,EAAEA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACxD,OAAO,CAAChB,QAAQ,CAACyE,QAAQ,CAAC,CAAC;EAAE;EACpD,IAAItC,KAAKA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACkC,aAAa,IAAI,IAAI,CAACrD,OAAO,CAAChB,QAAQ;EAAE;AACtE;AACA,IAAIiD,WAAW,GAAG,MAAMA,WAAW,SAASrH,UAAU,CAAC;EACnD8B,WAAWA,CAACgH,KAAK,EAAElK,MAAM,EAAE0I,MAAM,EAAEyB,oBAAoB,EAAE;IACrD,KAAK,CAAC,CAAC;IACP,IAAI,CAACD,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACE,SAAS,GAAGjJ,CAAC,CAAC,gBAAgB,EAAE;MAAEwD,KAAK,EAAE;QAAEE,MAAM,EAAE,MAAM;QAAE7B,KAAK,EAAE;MAAO;IAAE,CAAC,EAAE,CAC/E7B,CAAC,CAAC,2BAA2B,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EACtCA,CAAC,CAAC,qBAAqB,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CACnC,CAAC;IACF,IAAI,CAACkJ,WAAW,GAAG,IAAI,CAACH,KAAK,CAAC9F,GAAG,CAAC,IAAI,EAAEmE,IAAI,IAAIA,IAAI,CAACoB,UAAU,CAAC;IAChE,IAAI,CAACW,OAAO,GAAG,IAAI,CAACJ,KAAK,CAAC9F,GAAG,CAAC,IAAI,EAAEmE,IAAI,IAAIA,IAAI,CAACqB,MAAM,CAAC;IACxD,IAAI,CAACW,QAAQ,GAAG9I,eAAe,CAAC,IAAI,EAAE,KAAK,CAAC;IAC5C,IAAI,CAAC+I,cAAc,GAAGxF,SAAS;IAC/B,IAAI,CAACyF,cAAc,GAAGzF,SAAS;IAC/B,MAAM0F,aAAa,GAAG,IAAI,CAAC7G,SAAS,CAACsG,oBAAoB,CAAC3B,cAAc,CAAC3F,sBAAsB,EAAE,SAAS,EAAE,IAAI,EAAE;MAAE+B,QAAQ,EAAE;QAAE+F,aAAa,EAAE,CAAC,CAAC;MAA0B;IAAE,CAAC,CAAC,CAAC;IAChL,IAAI,CAAC9G,SAAS,CAAChC,qBAAqB,CAAC7B,MAAM,EAAE,IAAI,CAACoK,SAAS,CAACvD,IAAI,CAAC,CAAC;IAClE,IAAI,CAAChD,SAAS,CAACxC,OAAO,CAACkD,MAAM,IAAI;MAC7B;MACA,MAAMoF,UAAU,GAAG,IAAI,CAACU,WAAW,CAAC5F,IAAI,CAACF,MAAM,CAAC;MAChD,IAAI,CAAC6F,SAAS,CAACvD,IAAI,CAAC+D,SAAS,CAACC,MAAM,CAAC,cAAc,EAAE,IAAI,CAAC;MAC1D,IAAI,CAACT,SAAS,CAACvD,IAAI,CAAC+D,SAAS,CAACC,MAAM,CAAC,YAAY,EAAElB,UAAU,CAAC;MAC9DmB,UAAU,CAAC,MAAM;QACb,IAAI,CAACV,SAAS,CAACvD,IAAI,CAAC+D,SAAS,CAACC,MAAM,CAAC,cAAc,EAAE,KAAK,CAAC;MAC/D,CAAC,EAAE,CAAC,CAAC;IACT,CAAC,CAAC,CAAC;IACH,IAAI,CAAChH,SAAS,CAACvC,gBAAgB,CAAC,CAACiD,MAAM,EAAEwG,KAAK,KAAK;MAC/C,IAAI,CAACX,SAAS,CAACY,OAAO,CAACC,eAAe,CAAC,CAAC;MACxC,MAAMrK,CAAC,GAAGmK,KAAK,CAACG,GAAG,CAACf,oBAAoB,CAAC3B,cAAc,CAAC/F,oBAAoB,EAAE,IAAI,CAAC2H,SAAS,CAACY,OAAO,EAAE,IAAI,CAACV,OAAO,CAAC7F,IAAI,CAACF,MAAM,CAAC,EAAE;QAC7H4G,WAAW,EAAE,CAAC,CAAC;QACfT,aAAa;QACbU,cAAc,EAAE;UACZC,YAAY,EAAEC,CAAC,IAAIA,CAAC,CAACC,UAAU,CAAC,SAAS;QAC7C,CAAC;QACDC,gBAAgB,EAAE;UAAEC,QAAQ,EAAE,IAAI,CAAClB,QAAQ,CAAC9F,IAAI,CAACF,MAAM,CAAC,GAAG,CAAC,GAAG;QAAE,CAAC;QAClEmH,kBAAkB,EAAE,CAAC,CAAC;QACtBC,YAAY,EAAE,IAAI1J,uBAAuB,CAAC,MAAM;UAC5C,MAAMsG,IAAI,GAAG,IAAI,CAAC2B,KAAK,CAACf,GAAG,CAAC,CAAC;UAC7B,MAAM3C,OAAO,GAAG+B,IAAI,CAAC/B,OAAO;UAC5B,OAAO;YACHA,OAAO;YACPoF,2BAA2B,EAAElD,MAAM,CAACO,kBAAkB,CAACzC,OAAO,CAAC;YAC/DsD,WAAW,EAAEvB,IAAI,CAACuB,WAAW;YAC7BC,WAAW,EAAExB,IAAI,CAACwB;UACtB,CAAC;QACL,CAAC,CAAC;QACF8B,WAAW,EAAE;UACTC,iBAAiB,EAAE;QACvB;MACJ,CAAC,CAAC,CAAC;MACHf,KAAK,CAACG,GAAG,CAACtK,CAAC,CAACmL,oBAAoB,CAAC,MAAM;QACnC,IAAI,IAAI,CAACvB,cAAc,EAAE;UACrB,IAAI,CAACf,MAAM,CAAC,IAAI,CAACe,cAAc,EAAE,IAAI,CAACC,cAAc,CAAC;QACzD;MACJ,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC;EACP;EACAhB,MAAMA,CAACuC,SAAS,EAAEC,SAAS,EAAE;IACzB,IAAI,CAACzB,cAAc,GAAGwB,SAAS;IAC/B,IAAI,CAACvB,cAAc,GAAGwB,SAAS;IAC/B,IAAIC,UAAU,GAAG,IAAI,CAAC9B,SAAS,CAACY,OAAO,CAACmB,YAAY;IACpD,IAAI,CAAC5B,QAAQ,CAACjD,GAAG,CAAC,IAAI,CAAC4C,KAAK,CAACf,GAAG,CAAC,CAAC,CAAC3C,OAAO,CAAC0B,QAAQ,CAACkE,eAAe,KAAK,CAAC,IAAIJ,SAAS,CAAC3L,MAAM,GAAG,EAAE,EAAE2E,SAAS,CAAC;IAC9G;IACAkH,UAAU,GAAG,IAAI,CAAC9B,SAAS,CAACY,OAAO,CAACmB,YAAY;IAChD,MAAME,YAAY,GAAGL,SAAS,CAAC3L,MAAM,GAAG,CAAC,GAAG6L,UAAU,GAAG,CAAC;IAC1D,MAAMI,MAAM,GAAGJ,UAAU;IACzB,IAAIK,oBAAoB,GAAGP,SAAS,CAACQ,KAAK,GAAGH,YAAY;IACzD,MAAMI,sBAAsB,GAAGrK,WAAW,CAACsK,SAAS,CAACJ,MAAM,EAAEL,SAAS,CAACU,YAAY,GAAGL,MAAM,GAAGJ,UAAU,CAAC;IAC1G,MAAMU,oBAAoB,GAAGxK,WAAW,CAACsK,SAAS,CAACV,SAAS,CAACQ,KAAK,GAAGF,MAAM,EAAEN,SAAS,CAACW,YAAY,GAAGT,UAAU,GAAGI,MAAM,CAAC;IAC1H,IAAIM,oBAAoB,IAAIH,sBAAsB,IAAIG,oBAAoB,CAACJ,KAAK,GAAGI,oBAAoB,CAACD,YAAY,EAAE;MAClHJ,oBAAoB,GAAGE,sBAAsB,CAACI,IAAI,CAACN,oBAAoB,CAAC;MACxEA,oBAAoB,GAAGK,oBAAoB,CAACC,IAAI,CAACN,oBAAoB,CAAC;IAC1E;IACA,IAAI,CAACnC,SAAS,CAACY,OAAO,CAACrG,KAAK,CAACmI,GAAG,GAAG,GAAGP,oBAAoB,GAAGP,SAAS,CAACQ,KAAK,IAAI;EACpF;AACJ,CAAC;AACD/D,WAAW,GAAG3I,UAAU,CAAC,CACrBgB,OAAO,CAAC,CAAC,EAAEgC,qBAAqB,CAAC,CACpC,EAAE2F,WAAW,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}