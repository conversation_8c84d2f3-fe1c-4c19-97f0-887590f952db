{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as DOM from './dom.js';\nimport * as dompurify from './dompurify/dompurify.js';\nimport { DomEmitter } from './event.js';\nimport { createElement } from './formattedTextRenderer.js';\nimport { StandardKeyboardEvent } from './keyboardEvent.js';\nimport { StandardMouseEvent } from './mouseEvent.js';\nimport { renderLabelWithIcons } from './ui/iconLabel/iconLabels.js';\nimport { onUnexpectedError } from '../common/errors.js';\nimport { Event } from '../common/event.js';\nimport { escapeDoubleQuotes, parseHrefAndDimensions, removeMarkdownEscapes } from '../common/htmlContent.js';\nimport { markdownEscapeEscapedIcons } from '../common/iconLabels.js';\nimport { defaultGenerator } from '../common/idGenerator.js';\nimport { Lazy } from '../common/lazy.js';\nimport { DisposableStore, toDisposable } from '../common/lifecycle.js';\nimport * as marked from '../common/marked/marked.js';\nimport { parse } from '../common/marshalling.js';\nimport { FileAccess, Schemas } from '../common/network.js';\nimport { cloneAndChange } from '../common/objects.js';\nimport { dirname, resolvePath } from '../common/resources.js';\nimport { escape } from '../common/strings.js';\nimport { URI } from '../common/uri.js';\nconst defaultMarkedRenderers = Object.freeze({\n  image: ({\n    href,\n    title,\n    text\n  }) => {\n    let dimensions = [];\n    let attributes = [];\n    if (href) {\n      ({\n        href,\n        dimensions\n      } = parseHrefAndDimensions(href));\n      attributes.push(`src=\"${escapeDoubleQuotes(href)}\"`);\n    }\n    if (text) {\n      attributes.push(`alt=\"${escapeDoubleQuotes(text)}\"`);\n    }\n    if (title) {\n      attributes.push(`title=\"${escapeDoubleQuotes(title)}\"`);\n    }\n    if (dimensions.length) {\n      attributes = attributes.concat(dimensions);\n    }\n    return '<img ' + attributes.join(' ') + '>';\n  },\n  paragraph({\n    tokens\n  }) {\n    return `<p>${this.parser.parseInline(tokens)}</p>`;\n  },\n  link({\n    href,\n    title,\n    tokens\n  }) {\n    let text = this.parser.parseInline(tokens);\n    if (typeof href !== 'string') {\n      return '';\n    }\n    // Remove markdown escapes. Workaround for https://github.com/chjj/marked/issues/829\n    if (href === text) {\n      // raw link case\n      text = removeMarkdownEscapes(text);\n    }\n    title = typeof title === 'string' ? escapeDoubleQuotes(removeMarkdownEscapes(title)) : '';\n    href = removeMarkdownEscapes(href);\n    // HTML Encode href\n    href = href.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/\"/g, '&quot;').replace(/'/g, '&#39;');\n    return `<a href=\"${href}\" title=\"${title || href}\" draggable=\"false\">${text}</a>`;\n  }\n});\n/**\n * Low-level way create a html element from a markdown string.\n *\n * **Note** that for most cases you should be using [`MarkdownRenderer`](./src/vs/editor/contrib/markdownRenderer/browser/markdownRenderer.ts)\n * which comes with support for pretty code block rendering and which uses the default way of handling links.\n */\nexport function renderMarkdown(markdown, options = {}, markedOptions = {}) {\n  const disposables = new DisposableStore();\n  let isDisposed = false;\n  const element = createElement(options);\n  const _uriMassage = function (part) {\n    let data;\n    try {\n      data = parse(decodeURIComponent(part));\n    } catch (e) {\n      // ignore\n    }\n    if (!data) {\n      return part;\n    }\n    data = cloneAndChange(data, value => {\n      if (markdown.uris && markdown.uris[value]) {\n        return URI.revive(markdown.uris[value]);\n      } else {\n        return undefined;\n      }\n    });\n    return encodeURIComponent(JSON.stringify(data));\n  };\n  const _href = function (href, isDomUri) {\n    const data = markdown.uris && markdown.uris[href];\n    let uri = URI.revive(data);\n    if (isDomUri) {\n      if (href.startsWith(Schemas.data + ':')) {\n        return href;\n      }\n      if (!uri) {\n        uri = URI.parse(href);\n      }\n      // this URI will end up as \"src\"-attribute of a dom node\n      // and because of that special rewriting needs to be done\n      // so that the URI uses a protocol that's understood by\n      // browsers (like http or https)\n      return FileAccess.uriToBrowserUri(uri).toString(true);\n    }\n    if (!uri) {\n      return href;\n    }\n    if (URI.parse(href).toString() === uri.toString()) {\n      return href; // no transformation performed\n    }\n    if (uri.query) {\n      uri = uri.with({\n        query: _uriMassage(uri.query)\n      });\n    }\n    return uri.toString();\n  };\n  const renderer = new marked.Renderer();\n  renderer.image = defaultMarkedRenderers.image;\n  renderer.link = defaultMarkedRenderers.link;\n  renderer.paragraph = defaultMarkedRenderers.paragraph;\n  // Will collect [id, renderedElement] tuples\n  const codeBlocks = [];\n  const syncCodeBlocks = [];\n  if (options.codeBlockRendererSync) {\n    renderer.code = ({\n      text,\n      lang\n    }) => {\n      const id = defaultGenerator.nextId();\n      const value = options.codeBlockRendererSync(postProcessCodeBlockLanguageId(lang), text);\n      syncCodeBlocks.push([id, value]);\n      return `<div class=\"code\" data-code=\"${id}\">${escape(text)}</div>`;\n    };\n  } else if (options.codeBlockRenderer) {\n    renderer.code = ({\n      text,\n      lang\n    }) => {\n      const id = defaultGenerator.nextId();\n      const value = options.codeBlockRenderer(postProcessCodeBlockLanguageId(lang), text);\n      codeBlocks.push(value.then(element => [id, element]));\n      return `<div class=\"code\" data-code=\"${id}\">${escape(text)}</div>`;\n    };\n  }\n  if (options.actionHandler) {\n    const _activateLink = function (event) {\n      let target = event.target;\n      if (target.tagName !== 'A') {\n        target = target.parentElement;\n        if (!target || target.tagName !== 'A') {\n          return;\n        }\n      }\n      try {\n        let href = target.dataset['href'];\n        if (href) {\n          if (markdown.baseUri) {\n            href = resolveWithBaseUri(URI.from(markdown.baseUri), href);\n          }\n          options.actionHandler.callback(href, event);\n        }\n      } catch (err) {\n        onUnexpectedError(err);\n      } finally {\n        event.preventDefault();\n      }\n    };\n    const onClick = options.actionHandler.disposables.add(new DomEmitter(element, 'click'));\n    const onAuxClick = options.actionHandler.disposables.add(new DomEmitter(element, 'auxclick'));\n    options.actionHandler.disposables.add(Event.any(onClick.event, onAuxClick.event)(e => {\n      const mouseEvent = new StandardMouseEvent(DOM.getWindow(element), e);\n      if (!mouseEvent.leftButton && !mouseEvent.middleButton) {\n        return;\n      }\n      _activateLink(mouseEvent);\n    }));\n    options.actionHandler.disposables.add(DOM.addDisposableListener(element, 'keydown', e => {\n      const keyboardEvent = new StandardKeyboardEvent(e);\n      if (!keyboardEvent.equals(10 /* KeyCode.Space */) && !keyboardEvent.equals(3 /* KeyCode.Enter */)) {\n        return;\n      }\n      _activateLink(keyboardEvent);\n    }));\n  }\n  if (!markdown.supportHtml) {\n    // Note: we always pass the output through dompurify after this so that we don't rely on\n    // marked for real sanitization.\n    renderer.html = ({\n      text\n    }) => {\n      if (options.sanitizerOptions?.replaceWithPlaintext) {\n        return escape(text);\n      }\n      const match = markdown.isTrusted ? text.match(/^(<span[^>]+>)|(<\\/\\s*span>)$/) : undefined;\n      return match ? text : '';\n    };\n  }\n  markedOptions.renderer = renderer;\n  // values that are too long will freeze the UI\n  let value = markdown.value ?? '';\n  if (value.length > 100_000) {\n    value = `${value.substr(0, 100_000)}…`;\n  }\n  // escape theme icons\n  if (markdown.supportThemeIcons) {\n    value = markdownEscapeEscapedIcons(value);\n  }\n  let renderedMarkdown;\n  if (options.fillInIncompleteTokens) {\n    // The defaults are applied by parse but not lexer()/parser(), and they need to be present\n    const opts = {\n      ...marked.defaults,\n      ...markedOptions\n    };\n    const tokens = marked.lexer(value, opts);\n    const newTokens = fillInIncompleteTokens(tokens);\n    renderedMarkdown = marked.parser(newTokens, opts);\n  } else {\n    renderedMarkdown = marked.parse(value, {\n      ...markedOptions,\n      async: false\n    });\n  }\n  // Rewrite theme icons\n  if (markdown.supportThemeIcons) {\n    const elements = renderLabelWithIcons(renderedMarkdown);\n    renderedMarkdown = elements.map(e => typeof e === 'string' ? e : e.outerHTML).join('');\n  }\n  const htmlParser = new DOMParser();\n  const markdownHtmlDoc = htmlParser.parseFromString(sanitizeRenderedMarkdown({\n    isTrusted: markdown.isTrusted,\n    ...options.sanitizerOptions\n  }, renderedMarkdown), 'text/html');\n  markdownHtmlDoc.body.querySelectorAll('img, audio, video, source').forEach(img => {\n    const src = img.getAttribute('src'); // Get the raw 'src' attribute value as text, not the resolved 'src'\n    if (src) {\n      let href = src;\n      try {\n        if (markdown.baseUri) {\n          // absolute or relative local path, or file: uri\n          href = resolveWithBaseUri(URI.from(markdown.baseUri), href);\n        }\n      } catch (err) {}\n      img.setAttribute('src', _href(href, true));\n      if (options.remoteImageIsAllowed) {\n        const uri = URI.parse(href);\n        if (uri.scheme !== Schemas.file && uri.scheme !== Schemas.data && !options.remoteImageIsAllowed(uri)) {\n          img.replaceWith(DOM.$('', undefined, img.outerHTML));\n        }\n      }\n    }\n  });\n  markdownHtmlDoc.body.querySelectorAll('a').forEach(a => {\n    const href = a.getAttribute('href'); // Get the raw 'href' attribute value as text, not the resolved 'href'\n    a.setAttribute('href', ''); // Clear out href. We use the `data-href` for handling clicks instead\n    if (!href || /^data:|javascript:/i.test(href) || /^command:/i.test(href) && !markdown.isTrusted || /^command:(\\/\\/\\/)?_workbench\\.downloadResource/i.test(href)) {\n      // drop the link\n      a.replaceWith(...a.childNodes);\n    } else {\n      let resolvedHref = _href(href, false);\n      if (markdown.baseUri) {\n        resolvedHref = resolveWithBaseUri(URI.from(markdown.baseUri), href);\n      }\n      a.dataset.href = resolvedHref;\n    }\n  });\n  element.innerHTML = sanitizeRenderedMarkdown({\n    isTrusted: markdown.isTrusted,\n    ...options.sanitizerOptions\n  }, markdownHtmlDoc.body.innerHTML);\n  if (codeBlocks.length > 0) {\n    Promise.all(codeBlocks).then(tuples => {\n      if (isDisposed) {\n        return;\n      }\n      const renderedElements = new Map(tuples);\n      const placeholderElements = element.querySelectorAll(`div[data-code]`);\n      for (const placeholderElement of placeholderElements) {\n        const renderedElement = renderedElements.get(placeholderElement.dataset['code'] ?? '');\n        if (renderedElement) {\n          DOM.reset(placeholderElement, renderedElement);\n        }\n      }\n      options.asyncRenderCallback?.();\n    });\n  } else if (syncCodeBlocks.length > 0) {\n    const renderedElements = new Map(syncCodeBlocks);\n    const placeholderElements = element.querySelectorAll(`div[data-code]`);\n    for (const placeholderElement of placeholderElements) {\n      const renderedElement = renderedElements.get(placeholderElement.dataset['code'] ?? '');\n      if (renderedElement) {\n        DOM.reset(placeholderElement, renderedElement);\n      }\n    }\n  }\n  // signal size changes for image tags\n  if (options.asyncRenderCallback) {\n    for (const img of element.getElementsByTagName('img')) {\n      const listener = disposables.add(DOM.addDisposableListener(img, 'load', () => {\n        listener.dispose();\n        options.asyncRenderCallback();\n      }));\n    }\n  }\n  return {\n    element,\n    dispose: () => {\n      isDisposed = true;\n      disposables.dispose();\n    }\n  };\n}\nfunction postProcessCodeBlockLanguageId(lang) {\n  if (!lang) {\n    return '';\n  }\n  const parts = lang.split(/[\\s+|:|,|\\{|\\?]/, 1);\n  if (parts.length) {\n    return parts[0];\n  }\n  return lang;\n}\nfunction resolveWithBaseUri(baseUri, href) {\n  const hasScheme = /^\\w[\\w\\d+.-]*:/.test(href);\n  if (hasScheme) {\n    return href;\n  }\n  if (baseUri.path.endsWith('/')) {\n    return resolvePath(baseUri, href).toString();\n  } else {\n    return resolvePath(dirname(baseUri), href).toString();\n  }\n}\nconst selfClosingTags = ['area', 'base', 'br', 'col', 'command', 'embed', 'hr', 'img', 'input', 'keygen', 'link', 'meta', 'param', 'source', 'track', 'wbr'];\nfunction sanitizeRenderedMarkdown(options, renderedMarkdown) {\n  const {\n    config,\n    allowedSchemes\n  } = getSanitizerOptions(options);\n  const store = new DisposableStore();\n  store.add(addDompurifyHook('uponSanitizeAttribute', (element, e) => {\n    if (e.attrName === 'style' || e.attrName === 'class') {\n      if (element.tagName === 'SPAN') {\n        if (e.attrName === 'style') {\n          e.keepAttr = /^(color\\:(#[0-9a-fA-F]+|var\\(--vscode(-[a-zA-Z]+)+\\));)?(background-color\\:(#[0-9a-fA-F]+|var\\(--vscode(-[a-zA-Z]+)+\\));)?(border-radius:[0-9]+px;)?$/.test(e.attrValue);\n          return;\n        } else if (e.attrName === 'class') {\n          e.keepAttr = /^codicon codicon-[a-z\\-]+( codicon-modifier-[a-z\\-]+)?$/.test(e.attrValue);\n          return;\n        }\n      }\n      e.keepAttr = false;\n      return;\n    } else if (element.tagName === 'INPUT' && element.attributes.getNamedItem('type')?.value === 'checkbox') {\n      if (e.attrName === 'type' && e.attrValue === 'checkbox' || e.attrName === 'disabled' || e.attrName === 'checked') {\n        e.keepAttr = true;\n        return;\n      }\n      e.keepAttr = false;\n    }\n  }));\n  store.add(addDompurifyHook('uponSanitizeElement', (element, e) => {\n    if (e.tagName === 'input') {\n      if (element.attributes.getNamedItem('type')?.value === 'checkbox') {\n        element.setAttribute('disabled', '');\n      } else if (!options.replaceWithPlaintext) {\n        element.remove();\n      }\n    }\n    if (options.replaceWithPlaintext && !e.allowedTags[e.tagName] && e.tagName !== 'body') {\n      if (element.parentElement) {\n        let startTagText;\n        let endTagText;\n        if (e.tagName === '#comment') {\n          startTagText = `<!--${element.textContent}-->`;\n        } else {\n          const isSelfClosing = selfClosingTags.includes(e.tagName);\n          const attrString = element.attributes.length ? ' ' + Array.from(element.attributes).map(attr => `${attr.name}=\"${attr.value}\"`).join(' ') : '';\n          startTagText = `<${e.tagName}${attrString}>`;\n          if (!isSelfClosing) {\n            endTagText = `</${e.tagName}>`;\n          }\n        }\n        const fragment = document.createDocumentFragment();\n        const textNode = element.parentElement.ownerDocument.createTextNode(startTagText);\n        fragment.appendChild(textNode);\n        const endTagTextNode = endTagText ? element.parentElement.ownerDocument.createTextNode(endTagText) : undefined;\n        while (element.firstChild) {\n          fragment.appendChild(element.firstChild);\n        }\n        if (endTagTextNode) {\n          fragment.appendChild(endTagTextNode);\n        }\n        if (element.nodeType === Node.COMMENT_NODE) {\n          // Workaround for https://github.com/cure53/DOMPurify/issues/1005\n          // The comment will be deleted in the next phase. However if we try to remove it now, it will cause\n          // an exception. Instead we insert the text node before the comment.\n          element.parentElement.insertBefore(fragment, element);\n        } else {\n          element.parentElement.replaceChild(fragment, element);\n        }\n      }\n    }\n  }));\n  store.add(DOM.hookDomPurifyHrefAndSrcSanitizer(allowedSchemes));\n  try {\n    return dompurify.sanitize(renderedMarkdown, {\n      ...config,\n      RETURN_TRUSTED_TYPE: true\n    });\n  } finally {\n    store.dispose();\n  }\n}\nexport const allowedMarkdownAttr = ['align', 'autoplay', 'alt', 'checked', 'class', 'colspan', 'controls', 'data-code', 'data-href', 'disabled', 'draggable', 'height', 'href', 'loop', 'muted', 'playsinline', 'poster', 'rowspan', 'src', 'style', 'target', 'title', 'type', 'width', 'start'];\nfunction getSanitizerOptions(options) {\n  const allowedSchemes = [Schemas.http, Schemas.https, Schemas.mailto, Schemas.data, Schemas.file, Schemas.vscodeFileResource, Schemas.vscodeRemote, Schemas.vscodeRemoteResource];\n  if (options.isTrusted) {\n    allowedSchemes.push(Schemas.command);\n  }\n  return {\n    config: {\n      // allowedTags should included everything that markdown renders to.\n      // Since we have our own sanitize function for marked, it's possible we missed some tag so let dompurify make sure.\n      // HTML tags that can result from markdown are from reading https://spec.commonmark.org/0.29/\n      // HTML table tags that can result from markdown are from https://github.github.com/gfm/#tables-extension-\n      ALLOWED_TAGS: options.allowedTags ?? [...DOM.basicMarkupHtmlTags],\n      ALLOWED_ATTR: allowedMarkdownAttr,\n      ALLOW_UNKNOWN_PROTOCOLS: true\n    },\n    allowedSchemes\n  };\n}\n/**\n * Strips all markdown from `string`, if it's an IMarkdownString. For example\n * `# Header` would be output as `Header`. If it's not, the string is returned.\n */\nexport function renderStringAsPlaintext(string) {\n  return typeof string === 'string' ? string : renderMarkdownAsPlaintext(string);\n}\n/**\n * Strips all markdown from `markdown`. For example `# Header` would be output as `Header`.\n * provide @param withCodeBlocks to retain code blocks\n */\nexport function renderMarkdownAsPlaintext(markdown, withCodeBlocks) {\n  // values that are too long will freeze the UI\n  let value = markdown.value ?? '';\n  if (value.length > 100_000) {\n    value = `${value.substr(0, 100_000)}…`;\n  }\n  const html = marked.parse(value, {\n    async: false,\n    renderer: withCodeBlocks ? plainTextWithCodeBlocksRenderer.value : plainTextRenderer.value\n  }).replace(/&(#\\d+|[a-zA-Z]+);/g, m => unescapeInfo.get(m) ?? m);\n  return sanitizeRenderedMarkdown({\n    isTrusted: false\n  }, html).toString();\n}\nconst unescapeInfo = new Map([['&quot;', '\"'], ['&nbsp;', ' '], ['&amp;', '&'], ['&#39;', '\\''], ['&lt;', '<'], ['&gt;', '>']]);\nfunction createRenderer() {\n  const renderer = new marked.Renderer();\n  renderer.code = ({\n    text\n  }) => {\n    return text;\n  };\n  renderer.blockquote = ({\n    text\n  }) => {\n    return text + '\\n';\n  };\n  renderer.html = _ => {\n    return '';\n  };\n  renderer.heading = function ({\n    tokens\n  }) {\n    return this.parser.parseInline(tokens) + '\\n';\n  };\n  renderer.hr = () => {\n    return '';\n  };\n  renderer.list = function ({\n    items\n  }) {\n    return items.map(x => this.listitem(x)).join('\\n') + '\\n';\n  };\n  renderer.listitem = ({\n    text\n  }) => {\n    return text + '\\n';\n  };\n  renderer.paragraph = function ({\n    tokens\n  }) {\n    return this.parser.parseInline(tokens) + '\\n';\n  };\n  renderer.table = function ({\n    header,\n    rows\n  }) {\n    return header.map(cell => this.tablecell(cell)).join(' ') + '\\n' + rows.map(cells => cells.map(cell => this.tablecell(cell)).join(' ')).join('\\n') + '\\n';\n  };\n  renderer.tablerow = ({\n    text\n  }) => {\n    return text;\n  };\n  renderer.tablecell = function ({\n    tokens\n  }) {\n    return this.parser.parseInline(tokens);\n  };\n  renderer.strong = ({\n    text\n  }) => {\n    return text;\n  };\n  renderer.em = ({\n    text\n  }) => {\n    return text;\n  };\n  renderer.codespan = ({\n    text\n  }) => {\n    return text;\n  };\n  renderer.br = _ => {\n    return '\\n';\n  };\n  renderer.del = ({\n    text\n  }) => {\n    return text;\n  };\n  renderer.image = _ => {\n    return '';\n  };\n  renderer.text = ({\n    text\n  }) => {\n    return text;\n  };\n  renderer.link = ({\n    text\n  }) => {\n    return text;\n  };\n  return renderer;\n}\nconst plainTextRenderer = new Lazy(withCodeBlocks => createRenderer());\nconst plainTextWithCodeBlocksRenderer = new Lazy(() => {\n  const renderer = createRenderer();\n  renderer.code = ({\n    text\n  }) => {\n    return `\\n\\`\\`\\`\\n${text}\\n\\`\\`\\`\\n`;\n  };\n  return renderer;\n});\nfunction mergeRawTokenText(tokens) {\n  let mergedTokenText = '';\n  tokens.forEach(token => {\n    mergedTokenText += token.raw;\n  });\n  return mergedTokenText;\n}\nfunction completeSingleLinePattern(token) {\n  if (!token.tokens) {\n    return undefined;\n  }\n  for (let i = token.tokens.length - 1; i >= 0; i--) {\n    const subtoken = token.tokens[i];\n    if (subtoken.type === 'text') {\n      const lines = subtoken.raw.split('\\n');\n      const lastLine = lines[lines.length - 1];\n      if (lastLine.includes('`')) {\n        return completeCodespan(token);\n      } else if (lastLine.includes('**')) {\n        return completeDoublestar(token);\n      } else if (lastLine.match(/\\*\\w/)) {\n        return completeStar(token);\n      } else if (lastLine.match(/(^|\\s)__\\w/)) {\n        return completeDoubleUnderscore(token);\n      } else if (lastLine.match(/(^|\\s)_\\w/)) {\n        return completeUnderscore(token);\n      } else if (\n      // Text with start of link target\n      hasLinkTextAndStartOfLinkTarget(lastLine) ||\n      // This token doesn't have the link text, eg if it contains other markdown constructs that are in other subtokens.\n      // But some preceding token does have an unbalanced [ at least\n      hasStartOfLinkTargetAndNoLinkText(lastLine) && token.tokens.slice(0, i).some(t => t.type === 'text' && t.raw.match(/\\[[^\\]]*$/))) {\n        const nextTwoSubTokens = token.tokens.slice(i + 1);\n        // A markdown link can look like\n        // [link text](https://microsoft.com \"more text\")\n        // Where \"more text\" is a title for the link or an argument to a vscode command link\n        if (\n        // If the link was parsed as a link, then look for a link token and a text token with a quote\n        nextTwoSubTokens[0]?.type === 'link' && nextTwoSubTokens[1]?.type === 'text' && nextTwoSubTokens[1].raw.match(/^ *\"[^\"]*$/) ||\n        // And if the link was not parsed as a link (eg command link), just look for a single quote in this token\n        lastLine.match(/^[^\"]* +\"[^\"]*$/)) {\n          return completeLinkTargetArg(token);\n        }\n        return completeLinkTarget(token);\n      }\n      // Contains the start of link text, and no following tokens contain the link target\n      else if (lastLine.match(/(^|\\s)\\[\\w*/)) {\n        return completeLinkText(token);\n      }\n    }\n  }\n  return undefined;\n}\nfunction hasLinkTextAndStartOfLinkTarget(str) {\n  return !!str.match(/(^|\\s)\\[.*\\]\\(\\w*/);\n}\nfunction hasStartOfLinkTargetAndNoLinkText(str) {\n  return !!str.match(/^[^\\[]*\\]\\([^\\)]*$/);\n}\nfunction completeListItemPattern(list) {\n  // Patch up this one list item\n  const lastListItem = list.items[list.items.length - 1];\n  const lastListSubToken = lastListItem.tokens ? lastListItem.tokens[lastListItem.tokens.length - 1] : undefined;\n  /*\n  Example list token structures:\n   list\n      list_item\n          text\n              text\n              codespan\n              link\n      list_item\n          text\n          code // Complete indented codeblock\n      list_item\n          text\n          space\n          text\n              text // Incomplete indented codeblock\n      list_item\n          text\n          list // Nested list\n              list_item\n                  text\n                      text\n   Contrast with paragraph:\n  paragraph\n      text\n      codespan\n  */\n  let newToken;\n  if (lastListSubToken?.type === 'text' && !('inRawBlock' in lastListItem)) {\n    // Why does Tag have a type of 'text'\n    newToken = completeSingleLinePattern(lastListSubToken);\n  }\n  if (!newToken || newToken.type !== 'paragraph') {\n    // 'text' item inside the list item turns into paragraph\n    // Nothing to fix, or not a pattern we were expecting\n    return;\n  }\n  const previousListItemsText = mergeRawTokenText(list.items.slice(0, -1));\n  // Grabbing the `- ` or `1. ` or `* ` off the list item because I can't find a better way to do this\n  const lastListItemLead = lastListItem.raw.match(/^(\\s*(-|\\d+\\.|\\*) +)/)?.[0];\n  if (!lastListItemLead) {\n    // Is badly formatted\n    return;\n  }\n  const newListItemText = lastListItemLead + mergeRawTokenText(lastListItem.tokens.slice(0, -1)) + newToken.raw;\n  const newList = marked.lexer(previousListItemsText + newListItemText)[0];\n  if (newList.type !== 'list') {\n    // Something went wrong\n    return;\n  }\n  return newList;\n}\nconst maxIncompleteTokensFixRounds = 3;\nexport function fillInIncompleteTokens(tokens) {\n  for (let i = 0; i < maxIncompleteTokensFixRounds; i++) {\n    const newTokens = fillInIncompleteTokensOnce(tokens);\n    if (newTokens) {\n      tokens = newTokens;\n    } else {\n      break;\n    }\n  }\n  return tokens;\n}\nfunction fillInIncompleteTokensOnce(tokens) {\n  let i;\n  let newTokens;\n  for (i = 0; i < tokens.length; i++) {\n    const token = tokens[i];\n    if (token.type === 'paragraph' && token.raw.match(/(\\n|^)\\|/)) {\n      newTokens = completeTable(tokens.slice(i));\n      break;\n    }\n    if (i === tokens.length - 1 && token.type === 'list') {\n      const newListToken = completeListItemPattern(token);\n      if (newListToken) {\n        newTokens = [newListToken];\n        break;\n      }\n    }\n    if (i === tokens.length - 1 && token.type === 'paragraph') {\n      // Only operates on a single token, because any newline that follows this should break these patterns\n      const newToken = completeSingleLinePattern(token);\n      if (newToken) {\n        newTokens = [newToken];\n        break;\n      }\n    }\n  }\n  if (newTokens) {\n    const newTokensList = [...tokens.slice(0, i), ...newTokens];\n    newTokensList.links = tokens.links;\n    return newTokensList;\n  }\n  return null;\n}\nfunction completeCodespan(token) {\n  return completeWithString(token, '`');\n}\nfunction completeStar(tokens) {\n  return completeWithString(tokens, '*');\n}\nfunction completeUnderscore(tokens) {\n  return completeWithString(tokens, '_');\n}\nfunction completeLinkTarget(tokens) {\n  return completeWithString(tokens, ')');\n}\nfunction completeLinkTargetArg(tokens) {\n  return completeWithString(tokens, '\")');\n}\nfunction completeLinkText(tokens) {\n  return completeWithString(tokens, '](https://microsoft.com)');\n}\nfunction completeDoublestar(tokens) {\n  return completeWithString(tokens, '**');\n}\nfunction completeDoubleUnderscore(tokens) {\n  return completeWithString(tokens, '__');\n}\nfunction completeWithString(tokens, closingString) {\n  const mergedRawText = mergeRawTokenText(Array.isArray(tokens) ? tokens : [tokens]);\n  // If it was completed correctly, this should be a single token.\n  // Expecting either a Paragraph or a List\n  return marked.lexer(mergedRawText + closingString)[0];\n}\nfunction completeTable(tokens) {\n  const mergedRawText = mergeRawTokenText(tokens);\n  const lines = mergedRawText.split('\\n');\n  let numCols; // The number of line1 col headers\n  let hasSeparatorRow = false;\n  for (let i = 0; i < lines.length; i++) {\n    const line = lines[i].trim();\n    if (typeof numCols === 'undefined' && line.match(/^\\s*\\|/)) {\n      const line1Matches = line.match(/(\\|[^\\|]+)(?=\\||$)/g);\n      if (line1Matches) {\n        numCols = line1Matches.length;\n      }\n    } else if (typeof numCols === 'number') {\n      if (line.match(/^\\s*\\|/)) {\n        if (i !== lines.length - 1) {\n          // We got the line1 header row, and the line2 separator row, but there are more lines, and it wasn't parsed as a table!\n          // That's strange and means that the table is probably malformed in the source, so I won't try to patch it up.\n          return undefined;\n        }\n        // Got a line2 separator row- partial or complete, doesn't matter, we'll replace it with a correct one\n        hasSeparatorRow = true;\n      } else {\n        // The line after the header row isn't a valid separator row, so the table is malformed, don't fix it up\n        return undefined;\n      }\n    }\n  }\n  if (typeof numCols === 'number' && numCols > 0) {\n    const prefixText = hasSeparatorRow ? lines.slice(0, -1).join('\\n') : mergedRawText;\n    const line1EndsInPipe = !!prefixText.match(/\\|\\s*$/);\n    const newRawText = prefixText + (line1EndsInPipe ? '' : '|') + `\\n|${' --- |'.repeat(numCols)}`;\n    return marked.lexer(newRawText);\n  }\n  return undefined;\n}\nfunction addDompurifyHook(hook, cb) {\n  dompurify.addHook(hook, cb);\n  return toDisposable(() => dompurify.removeHook(hook));\n}", "map": {"version": 3, "names": ["DOM", "dompurify", "DomEmitter", "createElement", "StandardKeyboardEvent", "StandardMouseEvent", "renderLabelWithIcons", "onUnexpectedError", "Event", "escapeDoubleQuotes", "parseHrefAndDimensions", "removeMarkdownEscapes", "markdownEscapeEscapedIcons", "defaultGenerator", "Lazy", "DisposableStore", "toDisposable", "marked", "parse", "FileAccess", "<PERSON><PERSON><PERSON>", "cloneAndChange", "dirname", "<PERSON><PERSON><PERSON>", "escape", "URI", "defaultMarkedRenderers", "Object", "freeze", "image", "href", "title", "text", "dimensions", "attributes", "push", "length", "concat", "join", "paragraph", "tokens", "parser", "parseInline", "link", "replace", "renderMarkdown", "markdown", "options", "markedOptions", "disposables", "isDisposed", "element", "_uriMassage", "part", "data", "decodeURIComponent", "e", "value", "uris", "revive", "undefined", "encodeURIComponent", "JSON", "stringify", "_href", "isDomUri", "uri", "startsWith", "uriToBrowserUri", "toString", "query", "with", "renderer", "<PERSON><PERSON><PERSON>", "codeBlocks", "syncCodeBlocks", "codeBlockRendererSync", "code", "lang", "id", "nextId", "postProcessCodeBlockLanguageId", "codeBlock<PERSON><PERSON><PERSON>", "then", "actionHandler", "_activateLink", "event", "target", "tagName", "parentElement", "dataset", "baseUri", "resolveWithBaseUri", "from", "callback", "err", "preventDefault", "onClick", "add", "onAuxClick", "any", "mouseEvent", "getWindow", "leftButton", "middleButton", "addDisposableListener", "keyboardEvent", "equals", "supportHtml", "html", "sanitizerOptions", "replaceWithPlaintext", "match", "isTrusted", "substr", "supportThemeIcons", "renderedMarkdown", "fillInIncompleteTokens", "opts", "defaults", "lexer", "newTokens", "async", "elements", "map", "outerHTML", "htmlParser", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "markdownHtmlDoc", "parseFromString", "sanitizeRenderedMarkdown", "body", "querySelectorAll", "for<PERSON>ach", "img", "src", "getAttribute", "setAttribute", "remoteImageIsAllowed", "scheme", "file", "replaceWith", "$", "a", "test", "childNodes", "resolvedHref", "innerHTML", "Promise", "all", "tuples", "renderedElements", "Map", "placeholderElements", "placeholderElement", "renderedElement", "get", "reset", "asyncRender<PERSON><PERSON>back", "getElementsByTagName", "listener", "dispose", "parts", "split", "hasScheme", "path", "endsWith", "selfClosingTags", "config", "allowedSchemes", "getSanitizerOptions", "store", "addDompurifyHook", "attrName", "keepAttr", "attrValue", "getNamedItem", "remove", "allowedTags", "startTagText", "endTagText", "textContent", "isSelfClosing", "includes", "attrString", "Array", "attr", "name", "fragment", "document", "createDocumentFragment", "textNode", "ownerDocument", "createTextNode", "append<PERSON><PERSON><PERSON>", "endTagTextNode", "<PERSON><PERSON><PERSON><PERSON>", "nodeType", "Node", "COMMENT_NODE", "insertBefore", "<PERSON><PERSON><PERSON><PERSON>", "hookDomPurifyHrefAndSrcSanitizer", "sanitize", "RETURN_TRUSTED_TYPE", "allowedMarkdownAttr", "http", "https", "mailto", "vscodeFileResource", "vscodeRemote", "vscodeRemoteResource", "command", "ALLOWED_TAGS", "basicMarkupHtmlTags", "ALLOWED_ATTR", "ALLOW_UNKNOWN_PROTOCOLS", "renderStringAsPlaintext", "string", "renderMarkdownAsPlaintext", "withCodeBlocks", "plainTextWithCodeBlocksRenderer", "plainTextRenderer", "m", "unescapeInfo", "<PERSON><PERSON><PERSON><PERSON>", "blockquote", "_", "heading", "hr", "list", "items", "x", "listitem", "table", "header", "rows", "cell", "tablecell", "cells", "tablerow", "strong", "em", "codespan", "br", "del", "mergeRawTokenText", "mergedTokenText", "token", "raw", "completeSingleLinePattern", "i", "subtoken", "type", "lines", "lastLine", "completeCodespan", "completeDoublestar", "completeStar", "completeDoubleUnderscore", "completeUnderscore", "hasLinkTextAndStartOfLinkTarget", "hasStartOfLinkTargetAndNoLinkText", "slice", "some", "t", "nextTwoSubTokens", "completeLinkTargetArg", "completeLinkTarget", "completeLinkText", "str", "completeListItemPattern", "lastListItem", "lastListSubToken", "newToken", "previousListItemsText", "lastListItemLead", "newListItemText", "newList", "maxIncompleteTokensFixRounds", "fillInIncompleteTokensOnce", "completeTable", "newListToken", "newTokensList", "links", "completeWithString", "closingString", "mergedRawText", "isArray", "numCols", "hasSeparatorRow", "line", "trim", "line1Matches", "prefixText", "line1EndsInPipe", "newRawText", "repeat", "hook", "cb", "addHook", "removeH<PERSON>"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/browser/markdownRenderer.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as DOM from './dom.js';\nimport * as dompurify from './dompurify/dompurify.js';\nimport { DomEmitter } from './event.js';\nimport { createElement } from './formattedTextRenderer.js';\nimport { StandardKeyboardEvent } from './keyboardEvent.js';\nimport { StandardMouseEvent } from './mouseEvent.js';\nimport { renderLabelWithIcons } from './ui/iconLabel/iconLabels.js';\nimport { onUnexpectedError } from '../common/errors.js';\nimport { Event } from '../common/event.js';\nimport { escapeDoubleQuotes, parseHrefAndDimensions, removeMarkdownEscapes } from '../common/htmlContent.js';\nimport { markdownEscapeEscapedIcons } from '../common/iconLabels.js';\nimport { defaultGenerator } from '../common/idGenerator.js';\nimport { Lazy } from '../common/lazy.js';\nimport { DisposableStore, toDisposable } from '../common/lifecycle.js';\nimport * as marked from '../common/marked/marked.js';\nimport { parse } from '../common/marshalling.js';\nimport { FileAccess, Schemas } from '../common/network.js';\nimport { cloneAndChange } from '../common/objects.js';\nimport { dirname, resolvePath } from '../common/resources.js';\nimport { escape } from '../common/strings.js';\nimport { URI } from '../common/uri.js';\nconst defaultMarkedRenderers = Object.freeze({\n    image: ({ href, title, text }) => {\n        let dimensions = [];\n        let attributes = [];\n        if (href) {\n            ({ href, dimensions } = parseHrefAndDimensions(href));\n            attributes.push(`src=\"${escapeDoubleQuotes(href)}\"`);\n        }\n        if (text) {\n            attributes.push(`alt=\"${escapeDoubleQuotes(text)}\"`);\n        }\n        if (title) {\n            attributes.push(`title=\"${escapeDoubleQuotes(title)}\"`);\n        }\n        if (dimensions.length) {\n            attributes = attributes.concat(dimensions);\n        }\n        return '<img ' + attributes.join(' ') + '>';\n    },\n    paragraph({ tokens }) {\n        return `<p>${this.parser.parseInline(tokens)}</p>`;\n    },\n    link({ href, title, tokens }) {\n        let text = this.parser.parseInline(tokens);\n        if (typeof href !== 'string') {\n            return '';\n        }\n        // Remove markdown escapes. Workaround for https://github.com/chjj/marked/issues/829\n        if (href === text) { // raw link case\n            text = removeMarkdownEscapes(text);\n        }\n        title = typeof title === 'string' ? escapeDoubleQuotes(removeMarkdownEscapes(title)) : '';\n        href = removeMarkdownEscapes(href);\n        // HTML Encode href\n        href = href.replace(/&/g, '&amp;')\n            .replace(/</g, '&lt;')\n            .replace(/>/g, '&gt;')\n            .replace(/\"/g, '&quot;')\n            .replace(/'/g, '&#39;');\n        return `<a href=\"${href}\" title=\"${title || href}\" draggable=\"false\">${text}</a>`;\n    },\n});\n/**\n * Low-level way create a html element from a markdown string.\n *\n * **Note** that for most cases you should be using [`MarkdownRenderer`](./src/vs/editor/contrib/markdownRenderer/browser/markdownRenderer.ts)\n * which comes with support for pretty code block rendering and which uses the default way of handling links.\n */\nexport function renderMarkdown(markdown, options = {}, markedOptions = {}) {\n    const disposables = new DisposableStore();\n    let isDisposed = false;\n    const element = createElement(options);\n    const _uriMassage = function (part) {\n        let data;\n        try {\n            data = parse(decodeURIComponent(part));\n        }\n        catch (e) {\n            // ignore\n        }\n        if (!data) {\n            return part;\n        }\n        data = cloneAndChange(data, value => {\n            if (markdown.uris && markdown.uris[value]) {\n                return URI.revive(markdown.uris[value]);\n            }\n            else {\n                return undefined;\n            }\n        });\n        return encodeURIComponent(JSON.stringify(data));\n    };\n    const _href = function (href, isDomUri) {\n        const data = markdown.uris && markdown.uris[href];\n        let uri = URI.revive(data);\n        if (isDomUri) {\n            if (href.startsWith(Schemas.data + ':')) {\n                return href;\n            }\n            if (!uri) {\n                uri = URI.parse(href);\n            }\n            // this URI will end up as \"src\"-attribute of a dom node\n            // and because of that special rewriting needs to be done\n            // so that the URI uses a protocol that's understood by\n            // browsers (like http or https)\n            return FileAccess.uriToBrowserUri(uri).toString(true);\n        }\n        if (!uri) {\n            return href;\n        }\n        if (URI.parse(href).toString() === uri.toString()) {\n            return href; // no transformation performed\n        }\n        if (uri.query) {\n            uri = uri.with({ query: _uriMassage(uri.query) });\n        }\n        return uri.toString();\n    };\n    const renderer = new marked.Renderer();\n    renderer.image = defaultMarkedRenderers.image;\n    renderer.link = defaultMarkedRenderers.link;\n    renderer.paragraph = defaultMarkedRenderers.paragraph;\n    // Will collect [id, renderedElement] tuples\n    const codeBlocks = [];\n    const syncCodeBlocks = [];\n    if (options.codeBlockRendererSync) {\n        renderer.code = ({ text, lang }) => {\n            const id = defaultGenerator.nextId();\n            const value = options.codeBlockRendererSync(postProcessCodeBlockLanguageId(lang), text);\n            syncCodeBlocks.push([id, value]);\n            return `<div class=\"code\" data-code=\"${id}\">${escape(text)}</div>`;\n        };\n    }\n    else if (options.codeBlockRenderer) {\n        renderer.code = ({ text, lang }) => {\n            const id = defaultGenerator.nextId();\n            const value = options.codeBlockRenderer(postProcessCodeBlockLanguageId(lang), text);\n            codeBlocks.push(value.then(element => [id, element]));\n            return `<div class=\"code\" data-code=\"${id}\">${escape(text)}</div>`;\n        };\n    }\n    if (options.actionHandler) {\n        const _activateLink = function (event) {\n            let target = event.target;\n            if (target.tagName !== 'A') {\n                target = target.parentElement;\n                if (!target || target.tagName !== 'A') {\n                    return;\n                }\n            }\n            try {\n                let href = target.dataset['href'];\n                if (href) {\n                    if (markdown.baseUri) {\n                        href = resolveWithBaseUri(URI.from(markdown.baseUri), href);\n                    }\n                    options.actionHandler.callback(href, event);\n                }\n            }\n            catch (err) {\n                onUnexpectedError(err);\n            }\n            finally {\n                event.preventDefault();\n            }\n        };\n        const onClick = options.actionHandler.disposables.add(new DomEmitter(element, 'click'));\n        const onAuxClick = options.actionHandler.disposables.add(new DomEmitter(element, 'auxclick'));\n        options.actionHandler.disposables.add(Event.any(onClick.event, onAuxClick.event)(e => {\n            const mouseEvent = new StandardMouseEvent(DOM.getWindow(element), e);\n            if (!mouseEvent.leftButton && !mouseEvent.middleButton) {\n                return;\n            }\n            _activateLink(mouseEvent);\n        }));\n        options.actionHandler.disposables.add(DOM.addDisposableListener(element, 'keydown', (e) => {\n            const keyboardEvent = new StandardKeyboardEvent(e);\n            if (!keyboardEvent.equals(10 /* KeyCode.Space */) && !keyboardEvent.equals(3 /* KeyCode.Enter */)) {\n                return;\n            }\n            _activateLink(keyboardEvent);\n        }));\n    }\n    if (!markdown.supportHtml) {\n        // Note: we always pass the output through dompurify after this so that we don't rely on\n        // marked for real sanitization.\n        renderer.html = ({ text }) => {\n            if (options.sanitizerOptions?.replaceWithPlaintext) {\n                return escape(text);\n            }\n            const match = markdown.isTrusted ? text.match(/^(<span[^>]+>)|(<\\/\\s*span>)$/) : undefined;\n            return match ? text : '';\n        };\n    }\n    markedOptions.renderer = renderer;\n    // values that are too long will freeze the UI\n    let value = markdown.value ?? '';\n    if (value.length > 100_000) {\n        value = `${value.substr(0, 100_000)}…`;\n    }\n    // escape theme icons\n    if (markdown.supportThemeIcons) {\n        value = markdownEscapeEscapedIcons(value);\n    }\n    let renderedMarkdown;\n    if (options.fillInIncompleteTokens) {\n        // The defaults are applied by parse but not lexer()/parser(), and they need to be present\n        const opts = {\n            ...marked.defaults,\n            ...markedOptions\n        };\n        const tokens = marked.lexer(value, opts);\n        const newTokens = fillInIncompleteTokens(tokens);\n        renderedMarkdown = marked.parser(newTokens, opts);\n    }\n    else {\n        renderedMarkdown = marked.parse(value, { ...markedOptions, async: false });\n    }\n    // Rewrite theme icons\n    if (markdown.supportThemeIcons) {\n        const elements = renderLabelWithIcons(renderedMarkdown);\n        renderedMarkdown = elements.map(e => typeof e === 'string' ? e : e.outerHTML).join('');\n    }\n    const htmlParser = new DOMParser();\n    const markdownHtmlDoc = htmlParser.parseFromString(sanitizeRenderedMarkdown({ isTrusted: markdown.isTrusted, ...options.sanitizerOptions }, renderedMarkdown), 'text/html');\n    markdownHtmlDoc.body.querySelectorAll('img, audio, video, source')\n        .forEach(img => {\n        const src = img.getAttribute('src'); // Get the raw 'src' attribute value as text, not the resolved 'src'\n        if (src) {\n            let href = src;\n            try {\n                if (markdown.baseUri) { // absolute or relative local path, or file: uri\n                    href = resolveWithBaseUri(URI.from(markdown.baseUri), href);\n                }\n            }\n            catch (err) { }\n            img.setAttribute('src', _href(href, true));\n            if (options.remoteImageIsAllowed) {\n                const uri = URI.parse(href);\n                if (uri.scheme !== Schemas.file && uri.scheme !== Schemas.data && !options.remoteImageIsAllowed(uri)) {\n                    img.replaceWith(DOM.$('', undefined, img.outerHTML));\n                }\n            }\n        }\n    });\n    markdownHtmlDoc.body.querySelectorAll('a')\n        .forEach(a => {\n        const href = a.getAttribute('href'); // Get the raw 'href' attribute value as text, not the resolved 'href'\n        a.setAttribute('href', ''); // Clear out href. We use the `data-href` for handling clicks instead\n        if (!href\n            || /^data:|javascript:/i.test(href)\n            || (/^command:/i.test(href) && !markdown.isTrusted)\n            || /^command:(\\/\\/\\/)?_workbench\\.downloadResource/i.test(href)) {\n            // drop the link\n            a.replaceWith(...a.childNodes);\n        }\n        else {\n            let resolvedHref = _href(href, false);\n            if (markdown.baseUri) {\n                resolvedHref = resolveWithBaseUri(URI.from(markdown.baseUri), href);\n            }\n            a.dataset.href = resolvedHref;\n        }\n    });\n    element.innerHTML = sanitizeRenderedMarkdown({ isTrusted: markdown.isTrusted, ...options.sanitizerOptions }, markdownHtmlDoc.body.innerHTML);\n    if (codeBlocks.length > 0) {\n        Promise.all(codeBlocks).then((tuples) => {\n            if (isDisposed) {\n                return;\n            }\n            const renderedElements = new Map(tuples);\n            const placeholderElements = element.querySelectorAll(`div[data-code]`);\n            for (const placeholderElement of placeholderElements) {\n                const renderedElement = renderedElements.get(placeholderElement.dataset['code'] ?? '');\n                if (renderedElement) {\n                    DOM.reset(placeholderElement, renderedElement);\n                }\n            }\n            options.asyncRenderCallback?.();\n        });\n    }\n    else if (syncCodeBlocks.length > 0) {\n        const renderedElements = new Map(syncCodeBlocks);\n        const placeholderElements = element.querySelectorAll(`div[data-code]`);\n        for (const placeholderElement of placeholderElements) {\n            const renderedElement = renderedElements.get(placeholderElement.dataset['code'] ?? '');\n            if (renderedElement) {\n                DOM.reset(placeholderElement, renderedElement);\n            }\n        }\n    }\n    // signal size changes for image tags\n    if (options.asyncRenderCallback) {\n        for (const img of element.getElementsByTagName('img')) {\n            const listener = disposables.add(DOM.addDisposableListener(img, 'load', () => {\n                listener.dispose();\n                options.asyncRenderCallback();\n            }));\n        }\n    }\n    return {\n        element,\n        dispose: () => {\n            isDisposed = true;\n            disposables.dispose();\n        }\n    };\n}\nfunction postProcessCodeBlockLanguageId(lang) {\n    if (!lang) {\n        return '';\n    }\n    const parts = lang.split(/[\\s+|:|,|\\{|\\?]/, 1);\n    if (parts.length) {\n        return parts[0];\n    }\n    return lang;\n}\nfunction resolveWithBaseUri(baseUri, href) {\n    const hasScheme = /^\\w[\\w\\d+.-]*:/.test(href);\n    if (hasScheme) {\n        return href;\n    }\n    if (baseUri.path.endsWith('/')) {\n        return resolvePath(baseUri, href).toString();\n    }\n    else {\n        return resolvePath(dirname(baseUri), href).toString();\n    }\n}\nconst selfClosingTags = ['area', 'base', 'br', 'col', 'command', 'embed', 'hr', 'img', 'input', 'keygen', 'link', 'meta', 'param', 'source', 'track', 'wbr'];\nfunction sanitizeRenderedMarkdown(options, renderedMarkdown) {\n    const { config, allowedSchemes } = getSanitizerOptions(options);\n    const store = new DisposableStore();\n    store.add(addDompurifyHook('uponSanitizeAttribute', (element, e) => {\n        if (e.attrName === 'style' || e.attrName === 'class') {\n            if (element.tagName === 'SPAN') {\n                if (e.attrName === 'style') {\n                    e.keepAttr = /^(color\\:(#[0-9a-fA-F]+|var\\(--vscode(-[a-zA-Z]+)+\\));)?(background-color\\:(#[0-9a-fA-F]+|var\\(--vscode(-[a-zA-Z]+)+\\));)?(border-radius:[0-9]+px;)?$/.test(e.attrValue);\n                    return;\n                }\n                else if (e.attrName === 'class') {\n                    e.keepAttr = /^codicon codicon-[a-z\\-]+( codicon-modifier-[a-z\\-]+)?$/.test(e.attrValue);\n                    return;\n                }\n            }\n            e.keepAttr = false;\n            return;\n        }\n        else if (element.tagName === 'INPUT' && element.attributes.getNamedItem('type')?.value === 'checkbox') {\n            if ((e.attrName === 'type' && e.attrValue === 'checkbox') || e.attrName === 'disabled' || e.attrName === 'checked') {\n                e.keepAttr = true;\n                return;\n            }\n            e.keepAttr = false;\n        }\n    }));\n    store.add(addDompurifyHook('uponSanitizeElement', (element, e) => {\n        if (e.tagName === 'input') {\n            if (element.attributes.getNamedItem('type')?.value === 'checkbox') {\n                element.setAttribute('disabled', '');\n            }\n            else if (!options.replaceWithPlaintext) {\n                element.remove();\n            }\n        }\n        if (options.replaceWithPlaintext && !e.allowedTags[e.tagName] && e.tagName !== 'body') {\n            if (element.parentElement) {\n                let startTagText;\n                let endTagText;\n                if (e.tagName === '#comment') {\n                    startTagText = `<!--${element.textContent}-->`;\n                }\n                else {\n                    const isSelfClosing = selfClosingTags.includes(e.tagName);\n                    const attrString = element.attributes.length ?\n                        ' ' + Array.from(element.attributes)\n                            .map(attr => `${attr.name}=\"${attr.value}\"`)\n                            .join(' ')\n                        : '';\n                    startTagText = `<${e.tagName}${attrString}>`;\n                    if (!isSelfClosing) {\n                        endTagText = `</${e.tagName}>`;\n                    }\n                }\n                const fragment = document.createDocumentFragment();\n                const textNode = element.parentElement.ownerDocument.createTextNode(startTagText);\n                fragment.appendChild(textNode);\n                const endTagTextNode = endTagText ? element.parentElement.ownerDocument.createTextNode(endTagText) : undefined;\n                while (element.firstChild) {\n                    fragment.appendChild(element.firstChild);\n                }\n                if (endTagTextNode) {\n                    fragment.appendChild(endTagTextNode);\n                }\n                if (element.nodeType === Node.COMMENT_NODE) {\n                    // Workaround for https://github.com/cure53/DOMPurify/issues/1005\n                    // The comment will be deleted in the next phase. However if we try to remove it now, it will cause\n                    // an exception. Instead we insert the text node before the comment.\n                    element.parentElement.insertBefore(fragment, element);\n                }\n                else {\n                    element.parentElement.replaceChild(fragment, element);\n                }\n            }\n        }\n    }));\n    store.add(DOM.hookDomPurifyHrefAndSrcSanitizer(allowedSchemes));\n    try {\n        return dompurify.sanitize(renderedMarkdown, { ...config, RETURN_TRUSTED_TYPE: true });\n    }\n    finally {\n        store.dispose();\n    }\n}\nexport const allowedMarkdownAttr = [\n    'align',\n    'autoplay',\n    'alt',\n    'checked',\n    'class',\n    'colspan',\n    'controls',\n    'data-code',\n    'data-href',\n    'disabled',\n    'draggable',\n    'height',\n    'href',\n    'loop',\n    'muted',\n    'playsinline',\n    'poster',\n    'rowspan',\n    'src',\n    'style',\n    'target',\n    'title',\n    'type',\n    'width',\n    'start',\n];\nfunction getSanitizerOptions(options) {\n    const allowedSchemes = [\n        Schemas.http,\n        Schemas.https,\n        Schemas.mailto,\n        Schemas.data,\n        Schemas.file,\n        Schemas.vscodeFileResource,\n        Schemas.vscodeRemote,\n        Schemas.vscodeRemoteResource,\n    ];\n    if (options.isTrusted) {\n        allowedSchemes.push(Schemas.command);\n    }\n    return {\n        config: {\n            // allowedTags should included everything that markdown renders to.\n            // Since we have our own sanitize function for marked, it's possible we missed some tag so let dompurify make sure.\n            // HTML tags that can result from markdown are from reading https://spec.commonmark.org/0.29/\n            // HTML table tags that can result from markdown are from https://github.github.com/gfm/#tables-extension-\n            ALLOWED_TAGS: options.allowedTags ?? [...DOM.basicMarkupHtmlTags],\n            ALLOWED_ATTR: allowedMarkdownAttr,\n            ALLOW_UNKNOWN_PROTOCOLS: true,\n        },\n        allowedSchemes\n    };\n}\n/**\n * Strips all markdown from `string`, if it's an IMarkdownString. For example\n * `# Header` would be output as `Header`. If it's not, the string is returned.\n */\nexport function renderStringAsPlaintext(string) {\n    return typeof string === 'string' ? string : renderMarkdownAsPlaintext(string);\n}\n/**\n * Strips all markdown from `markdown`. For example `# Header` would be output as `Header`.\n * provide @param withCodeBlocks to retain code blocks\n */\nexport function renderMarkdownAsPlaintext(markdown, withCodeBlocks) {\n    // values that are too long will freeze the UI\n    let value = markdown.value ?? '';\n    if (value.length > 100_000) {\n        value = `${value.substr(0, 100_000)}…`;\n    }\n    const html = marked.parse(value, { async: false, renderer: withCodeBlocks ? plainTextWithCodeBlocksRenderer.value : plainTextRenderer.value }).replace(/&(#\\d+|[a-zA-Z]+);/g, m => unescapeInfo.get(m) ?? m);\n    return sanitizeRenderedMarkdown({ isTrusted: false }, html).toString();\n}\nconst unescapeInfo = new Map([\n    ['&quot;', '\"'],\n    ['&nbsp;', ' '],\n    ['&amp;', '&'],\n    ['&#39;', '\\''],\n    ['&lt;', '<'],\n    ['&gt;', '>'],\n]);\nfunction createRenderer() {\n    const renderer = new marked.Renderer();\n    renderer.code = ({ text }) => {\n        return text;\n    };\n    renderer.blockquote = ({ text }) => {\n        return text + '\\n';\n    };\n    renderer.html = (_) => {\n        return '';\n    };\n    renderer.heading = function ({ tokens }) {\n        return this.parser.parseInline(tokens) + '\\n';\n    };\n    renderer.hr = () => {\n        return '';\n    };\n    renderer.list = function ({ items }) {\n        return items.map(x => this.listitem(x)).join('\\n') + '\\n';\n    };\n    renderer.listitem = ({ text }) => {\n        return text + '\\n';\n    };\n    renderer.paragraph = function ({ tokens }) {\n        return this.parser.parseInline(tokens) + '\\n';\n    };\n    renderer.table = function ({ header, rows }) {\n        return header.map(cell => this.tablecell(cell)).join(' ') + '\\n' + rows.map(cells => cells.map(cell => this.tablecell(cell)).join(' ')).join('\\n') + '\\n';\n    };\n    renderer.tablerow = ({ text }) => {\n        return text;\n    };\n    renderer.tablecell = function ({ tokens }) {\n        return this.parser.parseInline(tokens);\n    };\n    renderer.strong = ({ text }) => {\n        return text;\n    };\n    renderer.em = ({ text }) => {\n        return text;\n    };\n    renderer.codespan = ({ text }) => {\n        return text;\n    };\n    renderer.br = (_) => {\n        return '\\n';\n    };\n    renderer.del = ({ text }) => {\n        return text;\n    };\n    renderer.image = (_) => {\n        return '';\n    };\n    renderer.text = ({ text }) => {\n        return text;\n    };\n    renderer.link = ({ text }) => {\n        return text;\n    };\n    return renderer;\n}\nconst plainTextRenderer = new Lazy((withCodeBlocks) => createRenderer());\nconst plainTextWithCodeBlocksRenderer = new Lazy(() => {\n    const renderer = createRenderer();\n    renderer.code = ({ text }) => {\n        return `\\n\\`\\`\\`\\n${text}\\n\\`\\`\\`\\n`;\n    };\n    return renderer;\n});\nfunction mergeRawTokenText(tokens) {\n    let mergedTokenText = '';\n    tokens.forEach(token => {\n        mergedTokenText += token.raw;\n    });\n    return mergedTokenText;\n}\nfunction completeSingleLinePattern(token) {\n    if (!token.tokens) {\n        return undefined;\n    }\n    for (let i = token.tokens.length - 1; i >= 0; i--) {\n        const subtoken = token.tokens[i];\n        if (subtoken.type === 'text') {\n            const lines = subtoken.raw.split('\\n');\n            const lastLine = lines[lines.length - 1];\n            if (lastLine.includes('`')) {\n                return completeCodespan(token);\n            }\n            else if (lastLine.includes('**')) {\n                return completeDoublestar(token);\n            }\n            else if (lastLine.match(/\\*\\w/)) {\n                return completeStar(token);\n            }\n            else if (lastLine.match(/(^|\\s)__\\w/)) {\n                return completeDoubleUnderscore(token);\n            }\n            else if (lastLine.match(/(^|\\s)_\\w/)) {\n                return completeUnderscore(token);\n            }\n            else if (\n            // Text with start of link target\n            hasLinkTextAndStartOfLinkTarget(lastLine) ||\n                // This token doesn't have the link text, eg if it contains other markdown constructs that are in other subtokens.\n                // But some preceding token does have an unbalanced [ at least\n                hasStartOfLinkTargetAndNoLinkText(lastLine) && token.tokens.slice(0, i).some(t => t.type === 'text' && t.raw.match(/\\[[^\\]]*$/))) {\n                const nextTwoSubTokens = token.tokens.slice(i + 1);\n                // A markdown link can look like\n                // [link text](https://microsoft.com \"more text\")\n                // Where \"more text\" is a title for the link or an argument to a vscode command link\n                if (\n                // If the link was parsed as a link, then look for a link token and a text token with a quote\n                nextTwoSubTokens[0]?.type === 'link' && nextTwoSubTokens[1]?.type === 'text' && nextTwoSubTokens[1].raw.match(/^ *\"[^\"]*$/) ||\n                    // And if the link was not parsed as a link (eg command link), just look for a single quote in this token\n                    lastLine.match(/^[^\"]* +\"[^\"]*$/)) {\n                    return completeLinkTargetArg(token);\n                }\n                return completeLinkTarget(token);\n            }\n            // Contains the start of link text, and no following tokens contain the link target\n            else if (lastLine.match(/(^|\\s)\\[\\w*/)) {\n                return completeLinkText(token);\n            }\n        }\n    }\n    return undefined;\n}\nfunction hasLinkTextAndStartOfLinkTarget(str) {\n    return !!str.match(/(^|\\s)\\[.*\\]\\(\\w*/);\n}\nfunction hasStartOfLinkTargetAndNoLinkText(str) {\n    return !!str.match(/^[^\\[]*\\]\\([^\\)]*$/);\n}\nfunction completeListItemPattern(list) {\n    // Patch up this one list item\n    const lastListItem = list.items[list.items.length - 1];\n    const lastListSubToken = lastListItem.tokens ? lastListItem.tokens[lastListItem.tokens.length - 1] : undefined;\n    /*\n    Example list token structures:\n\n    list\n        list_item\n            text\n                text\n                codespan\n                link\n        list_item\n            text\n            code // Complete indented codeblock\n        list_item\n            text\n            space\n            text\n                text // Incomplete indented codeblock\n        list_item\n            text\n            list // Nested list\n                list_item\n                    text\n                        text\n\n    Contrast with paragraph:\n    paragraph\n        text\n        codespan\n    */\n    let newToken;\n    if (lastListSubToken?.type === 'text' && !('inRawBlock' in lastListItem)) { // Why does Tag have a type of 'text'\n        newToken = completeSingleLinePattern(lastListSubToken);\n    }\n    if (!newToken || newToken.type !== 'paragraph') { // 'text' item inside the list item turns into paragraph\n        // Nothing to fix, or not a pattern we were expecting\n        return;\n    }\n    const previousListItemsText = mergeRawTokenText(list.items.slice(0, -1));\n    // Grabbing the `- ` or `1. ` or `* ` off the list item because I can't find a better way to do this\n    const lastListItemLead = lastListItem.raw.match(/^(\\s*(-|\\d+\\.|\\*) +)/)?.[0];\n    if (!lastListItemLead) {\n        // Is badly formatted\n        return;\n    }\n    const newListItemText = lastListItemLead +\n        mergeRawTokenText(lastListItem.tokens.slice(0, -1)) +\n        newToken.raw;\n    const newList = marked.lexer(previousListItemsText + newListItemText)[0];\n    if (newList.type !== 'list') {\n        // Something went wrong\n        return;\n    }\n    return newList;\n}\nconst maxIncompleteTokensFixRounds = 3;\nexport function fillInIncompleteTokens(tokens) {\n    for (let i = 0; i < maxIncompleteTokensFixRounds; i++) {\n        const newTokens = fillInIncompleteTokensOnce(tokens);\n        if (newTokens) {\n            tokens = newTokens;\n        }\n        else {\n            break;\n        }\n    }\n    return tokens;\n}\nfunction fillInIncompleteTokensOnce(tokens) {\n    let i;\n    let newTokens;\n    for (i = 0; i < tokens.length; i++) {\n        const token = tokens[i];\n        if (token.type === 'paragraph' && token.raw.match(/(\\n|^)\\|/)) {\n            newTokens = completeTable(tokens.slice(i));\n            break;\n        }\n        if (i === tokens.length - 1 && token.type === 'list') {\n            const newListToken = completeListItemPattern(token);\n            if (newListToken) {\n                newTokens = [newListToken];\n                break;\n            }\n        }\n        if (i === tokens.length - 1 && token.type === 'paragraph') {\n            // Only operates on a single token, because any newline that follows this should break these patterns\n            const newToken = completeSingleLinePattern(token);\n            if (newToken) {\n                newTokens = [newToken];\n                break;\n            }\n        }\n    }\n    if (newTokens) {\n        const newTokensList = [\n            ...tokens.slice(0, i),\n            ...newTokens\n        ];\n        newTokensList.links = tokens.links;\n        return newTokensList;\n    }\n    return null;\n}\nfunction completeCodespan(token) {\n    return completeWithString(token, '`');\n}\nfunction completeStar(tokens) {\n    return completeWithString(tokens, '*');\n}\nfunction completeUnderscore(tokens) {\n    return completeWithString(tokens, '_');\n}\nfunction completeLinkTarget(tokens) {\n    return completeWithString(tokens, ')');\n}\nfunction completeLinkTargetArg(tokens) {\n    return completeWithString(tokens, '\")');\n}\nfunction completeLinkText(tokens) {\n    return completeWithString(tokens, '](https://microsoft.com)');\n}\nfunction completeDoublestar(tokens) {\n    return completeWithString(tokens, '**');\n}\nfunction completeDoubleUnderscore(tokens) {\n    return completeWithString(tokens, '__');\n}\nfunction completeWithString(tokens, closingString) {\n    const mergedRawText = mergeRawTokenText(Array.isArray(tokens) ? tokens : [tokens]);\n    // If it was completed correctly, this should be a single token.\n    // Expecting either a Paragraph or a List\n    return marked.lexer(mergedRawText + closingString)[0];\n}\nfunction completeTable(tokens) {\n    const mergedRawText = mergeRawTokenText(tokens);\n    const lines = mergedRawText.split('\\n');\n    let numCols; // The number of line1 col headers\n    let hasSeparatorRow = false;\n    for (let i = 0; i < lines.length; i++) {\n        const line = lines[i].trim();\n        if (typeof numCols === 'undefined' && line.match(/^\\s*\\|/)) {\n            const line1Matches = line.match(/(\\|[^\\|]+)(?=\\||$)/g);\n            if (line1Matches) {\n                numCols = line1Matches.length;\n            }\n        }\n        else if (typeof numCols === 'number') {\n            if (line.match(/^\\s*\\|/)) {\n                if (i !== lines.length - 1) {\n                    // We got the line1 header row, and the line2 separator row, but there are more lines, and it wasn't parsed as a table!\n                    // That's strange and means that the table is probably malformed in the source, so I won't try to patch it up.\n                    return undefined;\n                }\n                // Got a line2 separator row- partial or complete, doesn't matter, we'll replace it with a correct one\n                hasSeparatorRow = true;\n            }\n            else {\n                // The line after the header row isn't a valid separator row, so the table is malformed, don't fix it up\n                return undefined;\n            }\n        }\n    }\n    if (typeof numCols === 'number' && numCols > 0) {\n        const prefixText = hasSeparatorRow ? lines.slice(0, -1).join('\\n') : mergedRawText;\n        const line1EndsInPipe = !!prefixText.match(/\\|\\s*$/);\n        const newRawText = prefixText + (line1EndsInPipe ? '' : '|') + `\\n|${' --- |'.repeat(numCols)}`;\n        return marked.lexer(newRawText);\n    }\n    return undefined;\n}\nfunction addDompurifyHook(hook, cb) {\n    dompurify.addHook(hook, cb);\n    return toDisposable(() => dompurify.removeHook(hook));\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,KAAKA,GAAG,MAAM,UAAU;AAC/B,OAAO,KAAKC,SAAS,MAAM,0BAA0B;AACrD,SAASC,UAAU,QAAQ,YAAY;AACvC,SAASC,aAAa,QAAQ,4BAA4B;AAC1D,SAASC,qBAAqB,QAAQ,oBAAoB;AAC1D,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,oBAAoB,QAAQ,8BAA8B;AACnE,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,kBAAkB,EAAEC,sBAAsB,EAAEC,qBAAqB,QAAQ,0BAA0B;AAC5G,SAASC,0BAA0B,QAAQ,yBAAyB;AACpE,SAASC,gBAAgB,QAAQ,0BAA0B;AAC3D,SAASC,IAAI,QAAQ,mBAAmB;AACxC,SAASC,eAAe,EAAEC,YAAY,QAAQ,wBAAwB;AACtE,OAAO,KAAKC,MAAM,MAAM,4BAA4B;AACpD,SAASC,KAAK,QAAQ,0BAA0B;AAChD,SAASC,UAAU,EAAEC,OAAO,QAAQ,sBAAsB;AAC1D,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,OAAO,EAAEC,WAAW,QAAQ,wBAAwB;AAC7D,SAASC,MAAM,QAAQ,sBAAsB;AAC7C,SAASC,GAAG,QAAQ,kBAAkB;AACtC,MAAMC,sBAAsB,GAAGC,MAAM,CAACC,MAAM,CAAC;EACzCC,KAAK,EAAEA,CAAC;IAAEC,IAAI;IAAEC,KAAK;IAAEC;EAAK,CAAC,KAAK;IAC9B,IAAIC,UAAU,GAAG,EAAE;IACnB,IAAIC,UAAU,GAAG,EAAE;IACnB,IAAIJ,IAAI,EAAE;MACN,CAAC;QAAEA,IAAI;QAAEG;MAAW,CAAC,GAAGvB,sBAAsB,CAACoB,IAAI,CAAC;MACpDI,UAAU,CAACC,IAAI,CAAC,QAAQ1B,kBAAkB,CAACqB,IAAI,CAAC,GAAG,CAAC;IACxD;IACA,IAAIE,IAAI,EAAE;MACNE,UAAU,CAACC,IAAI,CAAC,QAAQ1B,kBAAkB,CAACuB,IAAI,CAAC,GAAG,CAAC;IACxD;IACA,IAAID,KAAK,EAAE;MACPG,UAAU,CAACC,IAAI,CAAC,UAAU1B,kBAAkB,CAACsB,KAAK,CAAC,GAAG,CAAC;IAC3D;IACA,IAAIE,UAAU,CAACG,MAAM,EAAE;MACnBF,UAAU,GAAGA,UAAU,CAACG,MAAM,CAACJ,UAAU,CAAC;IAC9C;IACA,OAAO,OAAO,GAAGC,UAAU,CAACI,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG;EAC/C,CAAC;EACDC,SAASA,CAAC;IAAEC;EAAO,CAAC,EAAE;IAClB,OAAO,MAAM,IAAI,CAACC,MAAM,CAACC,WAAW,CAACF,MAAM,CAAC,MAAM;EACtD,CAAC;EACDG,IAAIA,CAAC;IAAEb,IAAI;IAAEC,KAAK;IAAES;EAAO,CAAC,EAAE;IAC1B,IAAIR,IAAI,GAAG,IAAI,CAACS,MAAM,CAACC,WAAW,CAACF,MAAM,CAAC;IAC1C,IAAI,OAAOV,IAAI,KAAK,QAAQ,EAAE;MAC1B,OAAO,EAAE;IACb;IACA;IACA,IAAIA,IAAI,KAAKE,IAAI,EAAE;MAAE;MACjBA,IAAI,GAAGrB,qBAAqB,CAACqB,IAAI,CAAC;IACtC;IACAD,KAAK,GAAG,OAAOA,KAAK,KAAK,QAAQ,GAAGtB,kBAAkB,CAACE,qBAAqB,CAACoB,KAAK,CAAC,CAAC,GAAG,EAAE;IACzFD,IAAI,GAAGnB,qBAAqB,CAACmB,IAAI,CAAC;IAClC;IACAA,IAAI,GAAGA,IAAI,CAACc,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAC7BA,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CACrBA,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CACrBA,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,CACvBA,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC;IAC3B,OAAO,YAAYd,IAAI,YAAYC,KAAK,IAAID,IAAI,uBAAuBE,IAAI,MAAM;EACrF;AACJ,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASa,cAAcA,CAACC,QAAQ,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAEC,aAAa,GAAG,CAAC,CAAC,EAAE;EACvE,MAAMC,WAAW,GAAG,IAAIlC,eAAe,CAAC,CAAC;EACzC,IAAImC,UAAU,GAAG,KAAK;EACtB,MAAMC,OAAO,GAAGhD,aAAa,CAAC4C,OAAO,CAAC;EACtC,MAAMK,WAAW,GAAG,SAAAA,CAAUC,IAAI,EAAE;IAChC,IAAIC,IAAI;IACR,IAAI;MACAA,IAAI,GAAGpC,KAAK,CAACqC,kBAAkB,CAACF,IAAI,CAAC,CAAC;IAC1C,CAAC,CACD,OAAOG,CAAC,EAAE;MACN;IAAA;IAEJ,IAAI,CAACF,IAAI,EAAE;MACP,OAAOD,IAAI;IACf;IACAC,IAAI,GAAGjC,cAAc,CAACiC,IAAI,EAAEG,KAAK,IAAI;MACjC,IAAIX,QAAQ,CAACY,IAAI,IAAIZ,QAAQ,CAACY,IAAI,CAACD,KAAK,CAAC,EAAE;QACvC,OAAOhC,GAAG,CAACkC,MAAM,CAACb,QAAQ,CAACY,IAAI,CAACD,KAAK,CAAC,CAAC;MAC3C,CAAC,MACI;QACD,OAAOG,SAAS;MACpB;IACJ,CAAC,CAAC;IACF,OAAOC,kBAAkB,CAACC,IAAI,CAACC,SAAS,CAACT,IAAI,CAAC,CAAC;EACnD,CAAC;EACD,MAAMU,KAAK,GAAG,SAAAA,CAAUlC,IAAI,EAAEmC,QAAQ,EAAE;IACpC,MAAMX,IAAI,GAAGR,QAAQ,CAACY,IAAI,IAAIZ,QAAQ,CAACY,IAAI,CAAC5B,IAAI,CAAC;IACjD,IAAIoC,GAAG,GAAGzC,GAAG,CAACkC,MAAM,CAACL,IAAI,CAAC;IAC1B,IAAIW,QAAQ,EAAE;MACV,IAAInC,IAAI,CAACqC,UAAU,CAAC/C,OAAO,CAACkC,IAAI,GAAG,GAAG,CAAC,EAAE;QACrC,OAAOxB,IAAI;MACf;MACA,IAAI,CAACoC,GAAG,EAAE;QACNA,GAAG,GAAGzC,GAAG,CAACP,KAAK,CAACY,IAAI,CAAC;MACzB;MACA;MACA;MACA;MACA;MACA,OAAOX,UAAU,CAACiD,eAAe,CAACF,GAAG,CAAC,CAACG,QAAQ,CAAC,IAAI,CAAC;IACzD;IACA,IAAI,CAACH,GAAG,EAAE;MACN,OAAOpC,IAAI;IACf;IACA,IAAIL,GAAG,CAACP,KAAK,CAACY,IAAI,CAAC,CAACuC,QAAQ,CAAC,CAAC,KAAKH,GAAG,CAACG,QAAQ,CAAC,CAAC,EAAE;MAC/C,OAAOvC,IAAI,CAAC,CAAC;IACjB;IACA,IAAIoC,GAAG,CAACI,KAAK,EAAE;MACXJ,GAAG,GAAGA,GAAG,CAACK,IAAI,CAAC;QAAED,KAAK,EAAElB,WAAW,CAACc,GAAG,CAACI,KAAK;MAAE,CAAC,CAAC;IACrD;IACA,OAAOJ,GAAG,CAACG,QAAQ,CAAC,CAAC;EACzB,CAAC;EACD,MAAMG,QAAQ,GAAG,IAAIvD,MAAM,CAACwD,QAAQ,CAAC,CAAC;EACtCD,QAAQ,CAAC3C,KAAK,GAAGH,sBAAsB,CAACG,KAAK;EAC7C2C,QAAQ,CAAC7B,IAAI,GAAGjB,sBAAsB,CAACiB,IAAI;EAC3C6B,QAAQ,CAACjC,SAAS,GAAGb,sBAAsB,CAACa,SAAS;EACrD;EACA,MAAMmC,UAAU,GAAG,EAAE;EACrB,MAAMC,cAAc,GAAG,EAAE;EACzB,IAAI5B,OAAO,CAAC6B,qBAAqB,EAAE;IAC/BJ,QAAQ,CAACK,IAAI,GAAG,CAAC;MAAE7C,IAAI;MAAE8C;IAAK,CAAC,KAAK;MAChC,MAAMC,EAAE,GAAGlE,gBAAgB,CAACmE,MAAM,CAAC,CAAC;MACpC,MAAMvB,KAAK,GAAGV,OAAO,CAAC6B,qBAAqB,CAACK,8BAA8B,CAACH,IAAI,CAAC,EAAE9C,IAAI,CAAC;MACvF2C,cAAc,CAACxC,IAAI,CAAC,CAAC4C,EAAE,EAAEtB,KAAK,CAAC,CAAC;MAChC,OAAO,gCAAgCsB,EAAE,KAAKvD,MAAM,CAACQ,IAAI,CAAC,QAAQ;IACtE,CAAC;EACL,CAAC,MACI,IAAIe,OAAO,CAACmC,iBAAiB,EAAE;IAChCV,QAAQ,CAACK,IAAI,GAAG,CAAC;MAAE7C,IAAI;MAAE8C;IAAK,CAAC,KAAK;MAChC,MAAMC,EAAE,GAAGlE,gBAAgB,CAACmE,MAAM,CAAC,CAAC;MACpC,MAAMvB,KAAK,GAAGV,OAAO,CAACmC,iBAAiB,CAACD,8BAA8B,CAACH,IAAI,CAAC,EAAE9C,IAAI,CAAC;MACnF0C,UAAU,CAACvC,IAAI,CAACsB,KAAK,CAAC0B,IAAI,CAAChC,OAAO,IAAI,CAAC4B,EAAE,EAAE5B,OAAO,CAAC,CAAC,CAAC;MACrD,OAAO,gCAAgC4B,EAAE,KAAKvD,MAAM,CAACQ,IAAI,CAAC,QAAQ;IACtE,CAAC;EACL;EACA,IAAIe,OAAO,CAACqC,aAAa,EAAE;IACvB,MAAMC,aAAa,GAAG,SAAAA,CAAUC,KAAK,EAAE;MACnC,IAAIC,MAAM,GAAGD,KAAK,CAACC,MAAM;MACzB,IAAIA,MAAM,CAACC,OAAO,KAAK,GAAG,EAAE;QACxBD,MAAM,GAAGA,MAAM,CAACE,aAAa;QAC7B,IAAI,CAACF,MAAM,IAAIA,MAAM,CAACC,OAAO,KAAK,GAAG,EAAE;UACnC;QACJ;MACJ;MACA,IAAI;QACA,IAAI1D,IAAI,GAAGyD,MAAM,CAACG,OAAO,CAAC,MAAM,CAAC;QACjC,IAAI5D,IAAI,EAAE;UACN,IAAIgB,QAAQ,CAAC6C,OAAO,EAAE;YAClB7D,IAAI,GAAG8D,kBAAkB,CAACnE,GAAG,CAACoE,IAAI,CAAC/C,QAAQ,CAAC6C,OAAO,CAAC,EAAE7D,IAAI,CAAC;UAC/D;UACAiB,OAAO,CAACqC,aAAa,CAACU,QAAQ,CAAChE,IAAI,EAAEwD,KAAK,CAAC;QAC/C;MACJ,CAAC,CACD,OAAOS,GAAG,EAAE;QACRxF,iBAAiB,CAACwF,GAAG,CAAC;MAC1B,CAAC,SACO;QACJT,KAAK,CAACU,cAAc,CAAC,CAAC;MAC1B;IACJ,CAAC;IACD,MAAMC,OAAO,GAAGlD,OAAO,CAACqC,aAAa,CAACnC,WAAW,CAACiD,GAAG,CAAC,IAAIhG,UAAU,CAACiD,OAAO,EAAE,OAAO,CAAC,CAAC;IACvF,MAAMgD,UAAU,GAAGpD,OAAO,CAACqC,aAAa,CAACnC,WAAW,CAACiD,GAAG,CAAC,IAAIhG,UAAU,CAACiD,OAAO,EAAE,UAAU,CAAC,CAAC;IAC7FJ,OAAO,CAACqC,aAAa,CAACnC,WAAW,CAACiD,GAAG,CAAC1F,KAAK,CAAC4F,GAAG,CAACH,OAAO,CAACX,KAAK,EAAEa,UAAU,CAACb,KAAK,CAAC,CAAC9B,CAAC,IAAI;MAClF,MAAM6C,UAAU,GAAG,IAAIhG,kBAAkB,CAACL,GAAG,CAACsG,SAAS,CAACnD,OAAO,CAAC,EAAEK,CAAC,CAAC;MACpE,IAAI,CAAC6C,UAAU,CAACE,UAAU,IAAI,CAACF,UAAU,CAACG,YAAY,EAAE;QACpD;MACJ;MACAnB,aAAa,CAACgB,UAAU,CAAC;IAC7B,CAAC,CAAC,CAAC;IACHtD,OAAO,CAACqC,aAAa,CAACnC,WAAW,CAACiD,GAAG,CAAClG,GAAG,CAACyG,qBAAqB,CAACtD,OAAO,EAAE,SAAS,EAAGK,CAAC,IAAK;MACvF,MAAMkD,aAAa,GAAG,IAAItG,qBAAqB,CAACoD,CAAC,CAAC;MAClD,IAAI,CAACkD,aAAa,CAACC,MAAM,CAAC,EAAE,CAAC,mBAAmB,CAAC,IAAI,CAACD,aAAa,CAACC,MAAM,CAAC,CAAC,CAAC,mBAAmB,CAAC,EAAE;QAC/F;MACJ;MACAtB,aAAa,CAACqB,aAAa,CAAC;IAChC,CAAC,CAAC,CAAC;EACP;EACA,IAAI,CAAC5D,QAAQ,CAAC8D,WAAW,EAAE;IACvB;IACA;IACApC,QAAQ,CAACqC,IAAI,GAAG,CAAC;MAAE7E;IAAK,CAAC,KAAK;MAC1B,IAAIe,OAAO,CAAC+D,gBAAgB,EAAEC,oBAAoB,EAAE;QAChD,OAAOvF,MAAM,CAACQ,IAAI,CAAC;MACvB;MACA,MAAMgF,KAAK,GAAGlE,QAAQ,CAACmE,SAAS,GAAGjF,IAAI,CAACgF,KAAK,CAAC,+BAA+B,CAAC,GAAGpD,SAAS;MAC1F,OAAOoD,KAAK,GAAGhF,IAAI,GAAG,EAAE;IAC5B,CAAC;EACL;EACAgB,aAAa,CAACwB,QAAQ,GAAGA,QAAQ;EACjC;EACA,IAAIf,KAAK,GAAGX,QAAQ,CAACW,KAAK,IAAI,EAAE;EAChC,IAAIA,KAAK,CAACrB,MAAM,GAAG,OAAO,EAAE;IACxBqB,KAAK,GAAG,GAAGA,KAAK,CAACyD,MAAM,CAAC,CAAC,EAAE,OAAO,CAAC,GAAG;EAC1C;EACA;EACA,IAAIpE,QAAQ,CAACqE,iBAAiB,EAAE;IAC5B1D,KAAK,GAAG7C,0BAA0B,CAAC6C,KAAK,CAAC;EAC7C;EACA,IAAI2D,gBAAgB;EACpB,IAAIrE,OAAO,CAACsE,sBAAsB,EAAE;IAChC;IACA,MAAMC,IAAI,GAAG;MACT,GAAGrG,MAAM,CAACsG,QAAQ;MAClB,GAAGvE;IACP,CAAC;IACD,MAAMR,MAAM,GAAGvB,MAAM,CAACuG,KAAK,CAAC/D,KAAK,EAAE6D,IAAI,CAAC;IACxC,MAAMG,SAAS,GAAGJ,sBAAsB,CAAC7E,MAAM,CAAC;IAChD4E,gBAAgB,GAAGnG,MAAM,CAACwB,MAAM,CAACgF,SAAS,EAAEH,IAAI,CAAC;EACrD,CAAC,MACI;IACDF,gBAAgB,GAAGnG,MAAM,CAACC,KAAK,CAACuC,KAAK,EAAE;MAAE,GAAGT,aAAa;MAAE0E,KAAK,EAAE;IAAM,CAAC,CAAC;EAC9E;EACA;EACA,IAAI5E,QAAQ,CAACqE,iBAAiB,EAAE;IAC5B,MAAMQ,QAAQ,GAAGrH,oBAAoB,CAAC8G,gBAAgB,CAAC;IACvDA,gBAAgB,GAAGO,QAAQ,CAACC,GAAG,CAACpE,CAAC,IAAI,OAAOA,CAAC,KAAK,QAAQ,GAAGA,CAAC,GAAGA,CAAC,CAACqE,SAAS,CAAC,CAACvF,IAAI,CAAC,EAAE,CAAC;EAC1F;EACA,MAAMwF,UAAU,GAAG,IAAIC,SAAS,CAAC,CAAC;EAClC,MAAMC,eAAe,GAAGF,UAAU,CAACG,eAAe,CAACC,wBAAwB,CAAC;IAAEjB,SAAS,EAAEnE,QAAQ,CAACmE,SAAS;IAAE,GAAGlE,OAAO,CAAC+D;EAAiB,CAAC,EAAEM,gBAAgB,CAAC,EAAE,WAAW,CAAC;EAC3KY,eAAe,CAACG,IAAI,CAACC,gBAAgB,CAAC,2BAA2B,CAAC,CAC7DC,OAAO,CAACC,GAAG,IAAI;IAChB,MAAMC,GAAG,GAAGD,GAAG,CAACE,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;IACrC,IAAID,GAAG,EAAE;MACL,IAAIzG,IAAI,GAAGyG,GAAG;MACd,IAAI;QACA,IAAIzF,QAAQ,CAAC6C,OAAO,EAAE;UAAE;UACpB7D,IAAI,GAAG8D,kBAAkB,CAACnE,GAAG,CAACoE,IAAI,CAAC/C,QAAQ,CAAC6C,OAAO,CAAC,EAAE7D,IAAI,CAAC;QAC/D;MACJ,CAAC,CACD,OAAOiE,GAAG,EAAE,CAAE;MACduC,GAAG,CAACG,YAAY,CAAC,KAAK,EAAEzE,KAAK,CAAClC,IAAI,EAAE,IAAI,CAAC,CAAC;MAC1C,IAAIiB,OAAO,CAAC2F,oBAAoB,EAAE;QAC9B,MAAMxE,GAAG,GAAGzC,GAAG,CAACP,KAAK,CAACY,IAAI,CAAC;QAC3B,IAAIoC,GAAG,CAACyE,MAAM,KAAKvH,OAAO,CAACwH,IAAI,IAAI1E,GAAG,CAACyE,MAAM,KAAKvH,OAAO,CAACkC,IAAI,IAAI,CAACP,OAAO,CAAC2F,oBAAoB,CAACxE,GAAG,CAAC,EAAE;UAClGoE,GAAG,CAACO,WAAW,CAAC7I,GAAG,CAAC8I,CAAC,CAAC,EAAE,EAAElF,SAAS,EAAE0E,GAAG,CAACT,SAAS,CAAC,CAAC;QACxD;MACJ;IACJ;EACJ,CAAC,CAAC;EACFG,eAAe,CAACG,IAAI,CAACC,gBAAgB,CAAC,GAAG,CAAC,CACrCC,OAAO,CAACU,CAAC,IAAI;IACd,MAAMjH,IAAI,GAAGiH,CAAC,CAACP,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC;IACrCO,CAAC,CAACN,YAAY,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,CAAC;IAC5B,IAAI,CAAC3G,IAAI,IACF,qBAAqB,CAACkH,IAAI,CAAClH,IAAI,CAAC,IAC/B,YAAY,CAACkH,IAAI,CAAClH,IAAI,CAAC,IAAI,CAACgB,QAAQ,CAACmE,SAAU,IAChD,iDAAiD,CAAC+B,IAAI,CAAClH,IAAI,CAAC,EAAE;MACjE;MACAiH,CAAC,CAACF,WAAW,CAAC,GAAGE,CAAC,CAACE,UAAU,CAAC;IAClC,CAAC,MACI;MACD,IAAIC,YAAY,GAAGlF,KAAK,CAAClC,IAAI,EAAE,KAAK,CAAC;MACrC,IAAIgB,QAAQ,CAAC6C,OAAO,EAAE;QAClBuD,YAAY,GAAGtD,kBAAkB,CAACnE,GAAG,CAACoE,IAAI,CAAC/C,QAAQ,CAAC6C,OAAO,CAAC,EAAE7D,IAAI,CAAC;MACvE;MACAiH,CAAC,CAACrD,OAAO,CAAC5D,IAAI,GAAGoH,YAAY;IACjC;EACJ,CAAC,CAAC;EACF/F,OAAO,CAACgG,SAAS,GAAGjB,wBAAwB,CAAC;IAAEjB,SAAS,EAAEnE,QAAQ,CAACmE,SAAS;IAAE,GAAGlE,OAAO,CAAC+D;EAAiB,CAAC,EAAEkB,eAAe,CAACG,IAAI,CAACgB,SAAS,CAAC;EAC5I,IAAIzE,UAAU,CAACtC,MAAM,GAAG,CAAC,EAAE;IACvBgH,OAAO,CAACC,GAAG,CAAC3E,UAAU,CAAC,CAACS,IAAI,CAAEmE,MAAM,IAAK;MACrC,IAAIpG,UAAU,EAAE;QACZ;MACJ;MACA,MAAMqG,gBAAgB,GAAG,IAAIC,GAAG,CAACF,MAAM,CAAC;MACxC,MAAMG,mBAAmB,GAAGtG,OAAO,CAACiF,gBAAgB,CAAC,gBAAgB,CAAC;MACtE,KAAK,MAAMsB,kBAAkB,IAAID,mBAAmB,EAAE;QAClD,MAAME,eAAe,GAAGJ,gBAAgB,CAACK,GAAG,CAACF,kBAAkB,CAAChE,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;QACtF,IAAIiE,eAAe,EAAE;UACjB3J,GAAG,CAAC6J,KAAK,CAACH,kBAAkB,EAAEC,eAAe,CAAC;QAClD;MACJ;MACA5G,OAAO,CAAC+G,mBAAmB,GAAG,CAAC;IACnC,CAAC,CAAC;EACN,CAAC,MACI,IAAInF,cAAc,CAACvC,MAAM,GAAG,CAAC,EAAE;IAChC,MAAMmH,gBAAgB,GAAG,IAAIC,GAAG,CAAC7E,cAAc,CAAC;IAChD,MAAM8E,mBAAmB,GAAGtG,OAAO,CAACiF,gBAAgB,CAAC,gBAAgB,CAAC;IACtE,KAAK,MAAMsB,kBAAkB,IAAID,mBAAmB,EAAE;MAClD,MAAME,eAAe,GAAGJ,gBAAgB,CAACK,GAAG,CAACF,kBAAkB,CAAChE,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;MACtF,IAAIiE,eAAe,EAAE;QACjB3J,GAAG,CAAC6J,KAAK,CAACH,kBAAkB,EAAEC,eAAe,CAAC;MAClD;IACJ;EACJ;EACA;EACA,IAAI5G,OAAO,CAAC+G,mBAAmB,EAAE;IAC7B,KAAK,MAAMxB,GAAG,IAAInF,OAAO,CAAC4G,oBAAoB,CAAC,KAAK,CAAC,EAAE;MACnD,MAAMC,QAAQ,GAAG/G,WAAW,CAACiD,GAAG,CAAClG,GAAG,CAACyG,qBAAqB,CAAC6B,GAAG,EAAE,MAAM,EAAE,MAAM;QAC1E0B,QAAQ,CAACC,OAAO,CAAC,CAAC;QAClBlH,OAAO,CAAC+G,mBAAmB,CAAC,CAAC;MACjC,CAAC,CAAC,CAAC;IACP;EACJ;EACA,OAAO;IACH3G,OAAO;IACP8G,OAAO,EAAEA,CAAA,KAAM;MACX/G,UAAU,GAAG,IAAI;MACjBD,WAAW,CAACgH,OAAO,CAAC,CAAC;IACzB;EACJ,CAAC;AACL;AACA,SAAShF,8BAA8BA,CAACH,IAAI,EAAE;EAC1C,IAAI,CAACA,IAAI,EAAE;IACP,OAAO,EAAE;EACb;EACA,MAAMoF,KAAK,GAAGpF,IAAI,CAACqF,KAAK,CAAC,iBAAiB,EAAE,CAAC,CAAC;EAC9C,IAAID,KAAK,CAAC9H,MAAM,EAAE;IACd,OAAO8H,KAAK,CAAC,CAAC,CAAC;EACnB;EACA,OAAOpF,IAAI;AACf;AACA,SAASc,kBAAkBA,CAACD,OAAO,EAAE7D,IAAI,EAAE;EACvC,MAAMsI,SAAS,GAAG,gBAAgB,CAACpB,IAAI,CAAClH,IAAI,CAAC;EAC7C,IAAIsI,SAAS,EAAE;IACX,OAAOtI,IAAI;EACf;EACA,IAAI6D,OAAO,CAAC0E,IAAI,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAE;IAC5B,OAAO/I,WAAW,CAACoE,OAAO,EAAE7D,IAAI,CAAC,CAACuC,QAAQ,CAAC,CAAC;EAChD,CAAC,MACI;IACD,OAAO9C,WAAW,CAACD,OAAO,CAACqE,OAAO,CAAC,EAAE7D,IAAI,CAAC,CAACuC,QAAQ,CAAC,CAAC;EACzD;AACJ;AACA,MAAMkG,eAAe,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,CAAC;AAC5J,SAASrC,wBAAwBA,CAACnF,OAAO,EAAEqE,gBAAgB,EAAE;EACzD,MAAM;IAAEoD,MAAM;IAAEC;EAAe,CAAC,GAAGC,mBAAmB,CAAC3H,OAAO,CAAC;EAC/D,MAAM4H,KAAK,GAAG,IAAI5J,eAAe,CAAC,CAAC;EACnC4J,KAAK,CAACzE,GAAG,CAAC0E,gBAAgB,CAAC,uBAAuB,EAAE,CAACzH,OAAO,EAAEK,CAAC,KAAK;IAChE,IAAIA,CAAC,CAACqH,QAAQ,KAAK,OAAO,IAAIrH,CAAC,CAACqH,QAAQ,KAAK,OAAO,EAAE;MAClD,IAAI1H,OAAO,CAACqC,OAAO,KAAK,MAAM,EAAE;QAC5B,IAAIhC,CAAC,CAACqH,QAAQ,KAAK,OAAO,EAAE;UACxBrH,CAAC,CAACsH,QAAQ,GAAG,uJAAuJ,CAAC9B,IAAI,CAACxF,CAAC,CAACuH,SAAS,CAAC;UACtL;QACJ,CAAC,MACI,IAAIvH,CAAC,CAACqH,QAAQ,KAAK,OAAO,EAAE;UAC7BrH,CAAC,CAACsH,QAAQ,GAAG,yDAAyD,CAAC9B,IAAI,CAACxF,CAAC,CAACuH,SAAS,CAAC;UACxF;QACJ;MACJ;MACAvH,CAAC,CAACsH,QAAQ,GAAG,KAAK;MAClB;IACJ,CAAC,MACI,IAAI3H,OAAO,CAACqC,OAAO,KAAK,OAAO,IAAIrC,OAAO,CAACjB,UAAU,CAAC8I,YAAY,CAAC,MAAM,CAAC,EAAEvH,KAAK,KAAK,UAAU,EAAE;MACnG,IAAKD,CAAC,CAACqH,QAAQ,KAAK,MAAM,IAAIrH,CAAC,CAACuH,SAAS,KAAK,UAAU,IAAKvH,CAAC,CAACqH,QAAQ,KAAK,UAAU,IAAIrH,CAAC,CAACqH,QAAQ,KAAK,SAAS,EAAE;QAChHrH,CAAC,CAACsH,QAAQ,GAAG,IAAI;QACjB;MACJ;MACAtH,CAAC,CAACsH,QAAQ,GAAG,KAAK;IACtB;EACJ,CAAC,CAAC,CAAC;EACHH,KAAK,CAACzE,GAAG,CAAC0E,gBAAgB,CAAC,qBAAqB,EAAE,CAACzH,OAAO,EAAEK,CAAC,KAAK;IAC9D,IAAIA,CAAC,CAACgC,OAAO,KAAK,OAAO,EAAE;MACvB,IAAIrC,OAAO,CAACjB,UAAU,CAAC8I,YAAY,CAAC,MAAM,CAAC,EAAEvH,KAAK,KAAK,UAAU,EAAE;QAC/DN,OAAO,CAACsF,YAAY,CAAC,UAAU,EAAE,EAAE,CAAC;MACxC,CAAC,MACI,IAAI,CAAC1F,OAAO,CAACgE,oBAAoB,EAAE;QACpC5D,OAAO,CAAC8H,MAAM,CAAC,CAAC;MACpB;IACJ;IACA,IAAIlI,OAAO,CAACgE,oBAAoB,IAAI,CAACvD,CAAC,CAAC0H,WAAW,CAAC1H,CAAC,CAACgC,OAAO,CAAC,IAAIhC,CAAC,CAACgC,OAAO,KAAK,MAAM,EAAE;MACnF,IAAIrC,OAAO,CAACsC,aAAa,EAAE;QACvB,IAAI0F,YAAY;QAChB,IAAIC,UAAU;QACd,IAAI5H,CAAC,CAACgC,OAAO,KAAK,UAAU,EAAE;UAC1B2F,YAAY,GAAG,OAAOhI,OAAO,CAACkI,WAAW,KAAK;QAClD,CAAC,MACI;UACD,MAAMC,aAAa,GAAGf,eAAe,CAACgB,QAAQ,CAAC/H,CAAC,CAACgC,OAAO,CAAC;UACzD,MAAMgG,UAAU,GAAGrI,OAAO,CAACjB,UAAU,CAACE,MAAM,GACxC,GAAG,GAAGqJ,KAAK,CAAC5F,IAAI,CAAC1C,OAAO,CAACjB,UAAU,CAAC,CAC/B0F,GAAG,CAAC8D,IAAI,IAAI,GAAGA,IAAI,CAACC,IAAI,KAAKD,IAAI,CAACjI,KAAK,GAAG,CAAC,CAC3CnB,IAAI,CAAC,GAAG,CAAC,GACZ,EAAE;UACR6I,YAAY,GAAG,IAAI3H,CAAC,CAACgC,OAAO,GAAGgG,UAAU,GAAG;UAC5C,IAAI,CAACF,aAAa,EAAE;YAChBF,UAAU,GAAG,KAAK5H,CAAC,CAACgC,OAAO,GAAG;UAClC;QACJ;QACA,MAAMoG,QAAQ,GAAGC,QAAQ,CAACC,sBAAsB,CAAC,CAAC;QAClD,MAAMC,QAAQ,GAAG5I,OAAO,CAACsC,aAAa,CAACuG,aAAa,CAACC,cAAc,CAACd,YAAY,CAAC;QACjFS,QAAQ,CAACM,WAAW,CAACH,QAAQ,CAAC;QAC9B,MAAMI,cAAc,GAAGf,UAAU,GAAGjI,OAAO,CAACsC,aAAa,CAACuG,aAAa,CAACC,cAAc,CAACb,UAAU,CAAC,GAAGxH,SAAS;QAC9G,OAAOT,OAAO,CAACiJ,UAAU,EAAE;UACvBR,QAAQ,CAACM,WAAW,CAAC/I,OAAO,CAACiJ,UAAU,CAAC;QAC5C;QACA,IAAID,cAAc,EAAE;UAChBP,QAAQ,CAACM,WAAW,CAACC,cAAc,CAAC;QACxC;QACA,IAAIhJ,OAAO,CAACkJ,QAAQ,KAAKC,IAAI,CAACC,YAAY,EAAE;UACxC;UACA;UACA;UACApJ,OAAO,CAACsC,aAAa,CAAC+G,YAAY,CAACZ,QAAQ,EAAEzI,OAAO,CAAC;QACzD,CAAC,MACI;UACDA,OAAO,CAACsC,aAAa,CAACgH,YAAY,CAACb,QAAQ,EAAEzI,OAAO,CAAC;QACzD;MACJ;IACJ;EACJ,CAAC,CAAC,CAAC;EACHwH,KAAK,CAACzE,GAAG,CAAClG,GAAG,CAAC0M,gCAAgC,CAACjC,cAAc,CAAC,CAAC;EAC/D,IAAI;IACA,OAAOxK,SAAS,CAAC0M,QAAQ,CAACvF,gBAAgB,EAAE;MAAE,GAAGoD,MAAM;MAAEoC,mBAAmB,EAAE;IAAK,CAAC,CAAC;EACzF,CAAC,SACO;IACJjC,KAAK,CAACV,OAAO,CAAC,CAAC;EACnB;AACJ;AACA,OAAO,MAAM4C,mBAAmB,GAAG,CAC/B,OAAO,EACP,UAAU,EACV,KAAK,EACL,SAAS,EACT,OAAO,EACP,SAAS,EACT,UAAU,EACV,WAAW,EACX,WAAW,EACX,UAAU,EACV,WAAW,EACX,QAAQ,EACR,MAAM,EACN,MAAM,EACN,OAAO,EACP,aAAa,EACb,QAAQ,EACR,SAAS,EACT,KAAK,EACL,OAAO,EACP,QAAQ,EACR,OAAO,EACP,MAAM,EACN,OAAO,EACP,OAAO,CACV;AACD,SAASnC,mBAAmBA,CAAC3H,OAAO,EAAE;EAClC,MAAM0H,cAAc,GAAG,CACnBrJ,OAAO,CAAC0L,IAAI,EACZ1L,OAAO,CAAC2L,KAAK,EACb3L,OAAO,CAAC4L,MAAM,EACd5L,OAAO,CAACkC,IAAI,EACZlC,OAAO,CAACwH,IAAI,EACZxH,OAAO,CAAC6L,kBAAkB,EAC1B7L,OAAO,CAAC8L,YAAY,EACpB9L,OAAO,CAAC+L,oBAAoB,CAC/B;EACD,IAAIpK,OAAO,CAACkE,SAAS,EAAE;IACnBwD,cAAc,CAACtI,IAAI,CAACf,OAAO,CAACgM,OAAO,CAAC;EACxC;EACA,OAAO;IACH5C,MAAM,EAAE;MACJ;MACA;MACA;MACA;MACA6C,YAAY,EAAEtK,OAAO,CAACmI,WAAW,IAAI,CAAC,GAAGlL,GAAG,CAACsN,mBAAmB,CAAC;MACjEC,YAAY,EAAEV,mBAAmB;MACjCW,uBAAuB,EAAE;IAC7B,CAAC;IACD/C;EACJ,CAAC;AACL;AACA;AACA;AACA;AACA;AACA,OAAO,SAASgD,uBAAuBA,CAACC,MAAM,EAAE;EAC5C,OAAO,OAAOA,MAAM,KAAK,QAAQ,GAAGA,MAAM,GAAGC,yBAAyB,CAACD,MAAM,CAAC;AAClF;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,yBAAyBA,CAAC7K,QAAQ,EAAE8K,cAAc,EAAE;EAChE;EACA,IAAInK,KAAK,GAAGX,QAAQ,CAACW,KAAK,IAAI,EAAE;EAChC,IAAIA,KAAK,CAACrB,MAAM,GAAG,OAAO,EAAE;IACxBqB,KAAK,GAAG,GAAGA,KAAK,CAACyD,MAAM,CAAC,CAAC,EAAE,OAAO,CAAC,GAAG;EAC1C;EACA,MAAML,IAAI,GAAG5F,MAAM,CAACC,KAAK,CAACuC,KAAK,EAAE;IAAEiE,KAAK,EAAE,KAAK;IAAElD,QAAQ,EAAEoJ,cAAc,GAAGC,+BAA+B,CAACpK,KAAK,GAAGqK,iBAAiB,CAACrK;EAAM,CAAC,CAAC,CAACb,OAAO,CAAC,qBAAqB,EAAEmL,CAAC,IAAIC,YAAY,CAACpE,GAAG,CAACmE,CAAC,CAAC,IAAIA,CAAC,CAAC;EAC5M,OAAO7F,wBAAwB,CAAC;IAAEjB,SAAS,EAAE;EAAM,CAAC,EAAEJ,IAAI,CAAC,CAACxC,QAAQ,CAAC,CAAC;AAC1E;AACA,MAAM2J,YAAY,GAAG,IAAIxE,GAAG,CAAC,CACzB,CAAC,QAAQ,EAAE,GAAG,CAAC,EACf,CAAC,QAAQ,EAAE,GAAG,CAAC,EACf,CAAC,OAAO,EAAE,GAAG,CAAC,EACd,CAAC,OAAO,EAAE,IAAI,CAAC,EACf,CAAC,MAAM,EAAE,GAAG,CAAC,EACb,CAAC,MAAM,EAAE,GAAG,CAAC,CAChB,CAAC;AACF,SAASyE,cAAcA,CAAA,EAAG;EACtB,MAAMzJ,QAAQ,GAAG,IAAIvD,MAAM,CAACwD,QAAQ,CAAC,CAAC;EACtCD,QAAQ,CAACK,IAAI,GAAG,CAAC;IAAE7C;EAAK,CAAC,KAAK;IAC1B,OAAOA,IAAI;EACf,CAAC;EACDwC,QAAQ,CAAC0J,UAAU,GAAG,CAAC;IAAElM;EAAK,CAAC,KAAK;IAChC,OAAOA,IAAI,GAAG,IAAI;EACtB,CAAC;EACDwC,QAAQ,CAACqC,IAAI,GAAIsH,CAAC,IAAK;IACnB,OAAO,EAAE;EACb,CAAC;EACD3J,QAAQ,CAAC4J,OAAO,GAAG,UAAU;IAAE5L;EAAO,CAAC,EAAE;IACrC,OAAO,IAAI,CAACC,MAAM,CAACC,WAAW,CAACF,MAAM,CAAC,GAAG,IAAI;EACjD,CAAC;EACDgC,QAAQ,CAAC6J,EAAE,GAAG,MAAM;IAChB,OAAO,EAAE;EACb,CAAC;EACD7J,QAAQ,CAAC8J,IAAI,GAAG,UAAU;IAAEC;EAAM,CAAC,EAAE;IACjC,OAAOA,KAAK,CAAC3G,GAAG,CAAC4G,CAAC,IAAI,IAAI,CAACC,QAAQ,CAACD,CAAC,CAAC,CAAC,CAAClM,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI;EAC7D,CAAC;EACDkC,QAAQ,CAACiK,QAAQ,GAAG,CAAC;IAAEzM;EAAK,CAAC,KAAK;IAC9B,OAAOA,IAAI,GAAG,IAAI;EACtB,CAAC;EACDwC,QAAQ,CAACjC,SAAS,GAAG,UAAU;IAAEC;EAAO,CAAC,EAAE;IACvC,OAAO,IAAI,CAACC,MAAM,CAACC,WAAW,CAACF,MAAM,CAAC,GAAG,IAAI;EACjD,CAAC;EACDgC,QAAQ,CAACkK,KAAK,GAAG,UAAU;IAAEC,MAAM;IAAEC;EAAK,CAAC,EAAE;IACzC,OAAOD,MAAM,CAAC/G,GAAG,CAACiH,IAAI,IAAI,IAAI,CAACC,SAAS,CAACD,IAAI,CAAC,CAAC,CAACvM,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,GAAGsM,IAAI,CAAChH,GAAG,CAACmH,KAAK,IAAIA,KAAK,CAACnH,GAAG,CAACiH,IAAI,IAAI,IAAI,CAACC,SAAS,CAACD,IAAI,CAAC,CAAC,CAACvM,IAAI,CAAC,GAAG,CAAC,CAAC,CAACA,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI;EAC7J,CAAC;EACDkC,QAAQ,CAACwK,QAAQ,GAAG,CAAC;IAAEhN;EAAK,CAAC,KAAK;IAC9B,OAAOA,IAAI;EACf,CAAC;EACDwC,QAAQ,CAACsK,SAAS,GAAG,UAAU;IAAEtM;EAAO,CAAC,EAAE;IACvC,OAAO,IAAI,CAACC,MAAM,CAACC,WAAW,CAACF,MAAM,CAAC;EAC1C,CAAC;EACDgC,QAAQ,CAACyK,MAAM,GAAG,CAAC;IAAEjN;EAAK,CAAC,KAAK;IAC5B,OAAOA,IAAI;EACf,CAAC;EACDwC,QAAQ,CAAC0K,EAAE,GAAG,CAAC;IAAElN;EAAK,CAAC,KAAK;IACxB,OAAOA,IAAI;EACf,CAAC;EACDwC,QAAQ,CAAC2K,QAAQ,GAAG,CAAC;IAAEnN;EAAK,CAAC,KAAK;IAC9B,OAAOA,IAAI;EACf,CAAC;EACDwC,QAAQ,CAAC4K,EAAE,GAAIjB,CAAC,IAAK;IACjB,OAAO,IAAI;EACf,CAAC;EACD3J,QAAQ,CAAC6K,GAAG,GAAG,CAAC;IAAErN;EAAK,CAAC,KAAK;IACzB,OAAOA,IAAI;EACf,CAAC;EACDwC,QAAQ,CAAC3C,KAAK,GAAIsM,CAAC,IAAK;IACpB,OAAO,EAAE;EACb,CAAC;EACD3J,QAAQ,CAACxC,IAAI,GAAG,CAAC;IAAEA;EAAK,CAAC,KAAK;IAC1B,OAAOA,IAAI;EACf,CAAC;EACDwC,QAAQ,CAAC7B,IAAI,GAAG,CAAC;IAAEX;EAAK,CAAC,KAAK;IAC1B,OAAOA,IAAI;EACf,CAAC;EACD,OAAOwC,QAAQ;AACnB;AACA,MAAMsJ,iBAAiB,GAAG,IAAIhN,IAAI,CAAE8M,cAAc,IAAKK,cAAc,CAAC,CAAC,CAAC;AACxE,MAAMJ,+BAA+B,GAAG,IAAI/M,IAAI,CAAC,MAAM;EACnD,MAAM0D,QAAQ,GAAGyJ,cAAc,CAAC,CAAC;EACjCzJ,QAAQ,CAACK,IAAI,GAAG,CAAC;IAAE7C;EAAK,CAAC,KAAK;IAC1B,OAAO,aAAaA,IAAI,YAAY;EACxC,CAAC;EACD,OAAOwC,QAAQ;AACnB,CAAC,CAAC;AACF,SAAS8K,iBAAiBA,CAAC9M,MAAM,EAAE;EAC/B,IAAI+M,eAAe,GAAG,EAAE;EACxB/M,MAAM,CAAC6F,OAAO,CAACmH,KAAK,IAAI;IACpBD,eAAe,IAAIC,KAAK,CAACC,GAAG;EAChC,CAAC,CAAC;EACF,OAAOF,eAAe;AAC1B;AACA,SAASG,yBAAyBA,CAACF,KAAK,EAAE;EACtC,IAAI,CAACA,KAAK,CAAChN,MAAM,EAAE;IACf,OAAOoB,SAAS;EACpB;EACA,KAAK,IAAI+L,CAAC,GAAGH,KAAK,CAAChN,MAAM,CAACJ,MAAM,GAAG,CAAC,EAAEuN,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IAC/C,MAAMC,QAAQ,GAAGJ,KAAK,CAAChN,MAAM,CAACmN,CAAC,CAAC;IAChC,IAAIC,QAAQ,CAACC,IAAI,KAAK,MAAM,EAAE;MAC1B,MAAMC,KAAK,GAAGF,QAAQ,CAACH,GAAG,CAACtF,KAAK,CAAC,IAAI,CAAC;MACtC,MAAM4F,QAAQ,GAAGD,KAAK,CAACA,KAAK,CAAC1N,MAAM,GAAG,CAAC,CAAC;MACxC,IAAI2N,QAAQ,CAACxE,QAAQ,CAAC,GAAG,CAAC,EAAE;QACxB,OAAOyE,gBAAgB,CAACR,KAAK,CAAC;MAClC,CAAC,MACI,IAAIO,QAAQ,CAACxE,QAAQ,CAAC,IAAI,CAAC,EAAE;QAC9B,OAAO0E,kBAAkB,CAACT,KAAK,CAAC;MACpC,CAAC,MACI,IAAIO,QAAQ,CAAC/I,KAAK,CAAC,MAAM,CAAC,EAAE;QAC7B,OAAOkJ,YAAY,CAACV,KAAK,CAAC;MAC9B,CAAC,MACI,IAAIO,QAAQ,CAAC/I,KAAK,CAAC,YAAY,CAAC,EAAE;QACnC,OAAOmJ,wBAAwB,CAACX,KAAK,CAAC;MAC1C,CAAC,MACI,IAAIO,QAAQ,CAAC/I,KAAK,CAAC,WAAW,CAAC,EAAE;QAClC,OAAOoJ,kBAAkB,CAACZ,KAAK,CAAC;MACpC,CAAC,MACI;MACL;MACAa,+BAA+B,CAACN,QAAQ,CAAC;MACrC;MACA;MACAO,iCAAiC,CAACP,QAAQ,CAAC,IAAIP,KAAK,CAAChN,MAAM,CAAC+N,KAAK,CAAC,CAAC,EAAEZ,CAAC,CAAC,CAACa,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACZ,IAAI,KAAK,MAAM,IAAIY,CAAC,CAAChB,GAAG,CAACzI,KAAK,CAAC,WAAW,CAAC,CAAC,EAAE;QAClI,MAAM0J,gBAAgB,GAAGlB,KAAK,CAAChN,MAAM,CAAC+N,KAAK,CAACZ,CAAC,GAAG,CAAC,CAAC;QAClD;QACA;QACA;QACA;QACA;QACAe,gBAAgB,CAAC,CAAC,CAAC,EAAEb,IAAI,KAAK,MAAM,IAAIa,gBAAgB,CAAC,CAAC,CAAC,EAAEb,IAAI,KAAK,MAAM,IAAIa,gBAAgB,CAAC,CAAC,CAAC,CAACjB,GAAG,CAACzI,KAAK,CAAC,YAAY,CAAC;QACvH;QACA+I,QAAQ,CAAC/I,KAAK,CAAC,iBAAiB,CAAC,EAAE;UACnC,OAAO2J,qBAAqB,CAACnB,KAAK,CAAC;QACvC;QACA,OAAOoB,kBAAkB,CAACpB,KAAK,CAAC;MACpC;MACA;MAAA,KACK,IAAIO,QAAQ,CAAC/I,KAAK,CAAC,aAAa,CAAC,EAAE;QACpC,OAAO6J,gBAAgB,CAACrB,KAAK,CAAC;MAClC;IACJ;EACJ;EACA,OAAO5L,SAAS;AACpB;AACA,SAASyM,+BAA+BA,CAACS,GAAG,EAAE;EAC1C,OAAO,CAAC,CAACA,GAAG,CAAC9J,KAAK,CAAC,mBAAmB,CAAC;AAC3C;AACA,SAASsJ,iCAAiCA,CAACQ,GAAG,EAAE;EAC5C,OAAO,CAAC,CAACA,GAAG,CAAC9J,KAAK,CAAC,oBAAoB,CAAC;AAC5C;AACA,SAAS+J,uBAAuBA,CAACzC,IAAI,EAAE;EACnC;EACA,MAAM0C,YAAY,GAAG1C,IAAI,CAACC,KAAK,CAACD,IAAI,CAACC,KAAK,CAACnM,MAAM,GAAG,CAAC,CAAC;EACtD,MAAM6O,gBAAgB,GAAGD,YAAY,CAACxO,MAAM,GAAGwO,YAAY,CAACxO,MAAM,CAACwO,YAAY,CAACxO,MAAM,CAACJ,MAAM,GAAG,CAAC,CAAC,GAAGwB,SAAS;EAC9G;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAGI,IAAIsN,QAAQ;EACZ,IAAID,gBAAgB,EAAEpB,IAAI,KAAK,MAAM,IAAI,EAAE,YAAY,IAAImB,YAAY,CAAC,EAAE;IAAE;IACxEE,QAAQ,GAAGxB,yBAAyB,CAACuB,gBAAgB,CAAC;EAC1D;EACA,IAAI,CAACC,QAAQ,IAAIA,QAAQ,CAACrB,IAAI,KAAK,WAAW,EAAE;IAAE;IAC9C;IACA;EACJ;EACA,MAAMsB,qBAAqB,GAAG7B,iBAAiB,CAAChB,IAAI,CAACC,KAAK,CAACgC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACxE;EACA,MAAMa,gBAAgB,GAAGJ,YAAY,CAACvB,GAAG,CAACzI,KAAK,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC;EAC5E,IAAI,CAACoK,gBAAgB,EAAE;IACnB;IACA;EACJ;EACA,MAAMC,eAAe,GAAGD,gBAAgB,GACpC9B,iBAAiB,CAAC0B,YAAY,CAACxO,MAAM,CAAC+N,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GACnDW,QAAQ,CAACzB,GAAG;EAChB,MAAM6B,OAAO,GAAGrQ,MAAM,CAACuG,KAAK,CAAC2J,qBAAqB,GAAGE,eAAe,CAAC,CAAC,CAAC,CAAC;EACxE,IAAIC,OAAO,CAACzB,IAAI,KAAK,MAAM,EAAE;IACzB;IACA;EACJ;EACA,OAAOyB,OAAO;AAClB;AACA,MAAMC,4BAA4B,GAAG,CAAC;AACtC,OAAO,SAASlK,sBAAsBA,CAAC7E,MAAM,EAAE;EAC3C,KAAK,IAAImN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4B,4BAA4B,EAAE5B,CAAC,EAAE,EAAE;IACnD,MAAMlI,SAAS,GAAG+J,0BAA0B,CAAChP,MAAM,CAAC;IACpD,IAAIiF,SAAS,EAAE;MACXjF,MAAM,GAAGiF,SAAS;IACtB,CAAC,MACI;MACD;IACJ;EACJ;EACA,OAAOjF,MAAM;AACjB;AACA,SAASgP,0BAA0BA,CAAChP,MAAM,EAAE;EACxC,IAAImN,CAAC;EACL,IAAIlI,SAAS;EACb,KAAKkI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnN,MAAM,CAACJ,MAAM,EAAEuN,CAAC,EAAE,EAAE;IAChC,MAAMH,KAAK,GAAGhN,MAAM,CAACmN,CAAC,CAAC;IACvB,IAAIH,KAAK,CAACK,IAAI,KAAK,WAAW,IAAIL,KAAK,CAACC,GAAG,CAACzI,KAAK,CAAC,UAAU,CAAC,EAAE;MAC3DS,SAAS,GAAGgK,aAAa,CAACjP,MAAM,CAAC+N,KAAK,CAACZ,CAAC,CAAC,CAAC;MAC1C;IACJ;IACA,IAAIA,CAAC,KAAKnN,MAAM,CAACJ,MAAM,GAAG,CAAC,IAAIoN,KAAK,CAACK,IAAI,KAAK,MAAM,EAAE;MAClD,MAAM6B,YAAY,GAAGX,uBAAuB,CAACvB,KAAK,CAAC;MACnD,IAAIkC,YAAY,EAAE;QACdjK,SAAS,GAAG,CAACiK,YAAY,CAAC;QAC1B;MACJ;IACJ;IACA,IAAI/B,CAAC,KAAKnN,MAAM,CAACJ,MAAM,GAAG,CAAC,IAAIoN,KAAK,CAACK,IAAI,KAAK,WAAW,EAAE;MACvD;MACA,MAAMqB,QAAQ,GAAGxB,yBAAyB,CAACF,KAAK,CAAC;MACjD,IAAI0B,QAAQ,EAAE;QACVzJ,SAAS,GAAG,CAACyJ,QAAQ,CAAC;QACtB;MACJ;IACJ;EACJ;EACA,IAAIzJ,SAAS,EAAE;IACX,MAAMkK,aAAa,GAAG,CAClB,GAAGnP,MAAM,CAAC+N,KAAK,CAAC,CAAC,EAAEZ,CAAC,CAAC,EACrB,GAAGlI,SAAS,CACf;IACDkK,aAAa,CAACC,KAAK,GAAGpP,MAAM,CAACoP,KAAK;IAClC,OAAOD,aAAa;EACxB;EACA,OAAO,IAAI;AACf;AACA,SAAS3B,gBAAgBA,CAACR,KAAK,EAAE;EAC7B,OAAOqC,kBAAkB,CAACrC,KAAK,EAAE,GAAG,CAAC;AACzC;AACA,SAASU,YAAYA,CAAC1N,MAAM,EAAE;EAC1B,OAAOqP,kBAAkB,CAACrP,MAAM,EAAE,GAAG,CAAC;AAC1C;AACA,SAAS4N,kBAAkBA,CAAC5N,MAAM,EAAE;EAChC,OAAOqP,kBAAkB,CAACrP,MAAM,EAAE,GAAG,CAAC;AAC1C;AACA,SAASoO,kBAAkBA,CAACpO,MAAM,EAAE;EAChC,OAAOqP,kBAAkB,CAACrP,MAAM,EAAE,GAAG,CAAC;AAC1C;AACA,SAASmO,qBAAqBA,CAACnO,MAAM,EAAE;EACnC,OAAOqP,kBAAkB,CAACrP,MAAM,EAAE,IAAI,CAAC;AAC3C;AACA,SAASqO,gBAAgBA,CAACrO,MAAM,EAAE;EAC9B,OAAOqP,kBAAkB,CAACrP,MAAM,EAAE,0BAA0B,CAAC;AACjE;AACA,SAASyN,kBAAkBA,CAACzN,MAAM,EAAE;EAChC,OAAOqP,kBAAkB,CAACrP,MAAM,EAAE,IAAI,CAAC;AAC3C;AACA,SAAS2N,wBAAwBA,CAAC3N,MAAM,EAAE;EACtC,OAAOqP,kBAAkB,CAACrP,MAAM,EAAE,IAAI,CAAC;AAC3C;AACA,SAASqP,kBAAkBA,CAACrP,MAAM,EAAEsP,aAAa,EAAE;EAC/C,MAAMC,aAAa,GAAGzC,iBAAiB,CAAC7D,KAAK,CAACuG,OAAO,CAACxP,MAAM,CAAC,GAAGA,MAAM,GAAG,CAACA,MAAM,CAAC,CAAC;EAClF;EACA;EACA,OAAOvB,MAAM,CAACuG,KAAK,CAACuK,aAAa,GAAGD,aAAa,CAAC,CAAC,CAAC,CAAC;AACzD;AACA,SAASL,aAAaA,CAACjP,MAAM,EAAE;EAC3B,MAAMuP,aAAa,GAAGzC,iBAAiB,CAAC9M,MAAM,CAAC;EAC/C,MAAMsN,KAAK,GAAGiC,aAAa,CAAC5H,KAAK,CAAC,IAAI,CAAC;EACvC,IAAI8H,OAAO,CAAC,CAAC;EACb,IAAIC,eAAe,GAAG,KAAK;EAC3B,KAAK,IAAIvC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGG,KAAK,CAAC1N,MAAM,EAAEuN,CAAC,EAAE,EAAE;IACnC,MAAMwC,IAAI,GAAGrC,KAAK,CAACH,CAAC,CAAC,CAACyC,IAAI,CAAC,CAAC;IAC5B,IAAI,OAAOH,OAAO,KAAK,WAAW,IAAIE,IAAI,CAACnL,KAAK,CAAC,QAAQ,CAAC,EAAE;MACxD,MAAMqL,YAAY,GAAGF,IAAI,CAACnL,KAAK,CAAC,qBAAqB,CAAC;MACtD,IAAIqL,YAAY,EAAE;QACdJ,OAAO,GAAGI,YAAY,CAACjQ,MAAM;MACjC;IACJ,CAAC,MACI,IAAI,OAAO6P,OAAO,KAAK,QAAQ,EAAE;MAClC,IAAIE,IAAI,CAACnL,KAAK,CAAC,QAAQ,CAAC,EAAE;QACtB,IAAI2I,CAAC,KAAKG,KAAK,CAAC1N,MAAM,GAAG,CAAC,EAAE;UACxB;UACA;UACA,OAAOwB,SAAS;QACpB;QACA;QACAsO,eAAe,GAAG,IAAI;MAC1B,CAAC,MACI;QACD;QACA,OAAOtO,SAAS;MACpB;IACJ;EACJ;EACA,IAAI,OAAOqO,OAAO,KAAK,QAAQ,IAAIA,OAAO,GAAG,CAAC,EAAE;IAC5C,MAAMK,UAAU,GAAGJ,eAAe,GAAGpC,KAAK,CAACS,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACjO,IAAI,CAAC,IAAI,CAAC,GAAGyP,aAAa;IAClF,MAAMQ,eAAe,GAAG,CAAC,CAACD,UAAU,CAACtL,KAAK,CAAC,QAAQ,CAAC;IACpD,MAAMwL,UAAU,GAAGF,UAAU,IAAIC,eAAe,GAAG,EAAE,GAAG,GAAG,CAAC,GAAG,MAAM,QAAQ,CAACE,MAAM,CAACR,OAAO,CAAC,EAAE;IAC/F,OAAOhR,MAAM,CAACuG,KAAK,CAACgL,UAAU,CAAC;EACnC;EACA,OAAO5O,SAAS;AACpB;AACA,SAASgH,gBAAgBA,CAAC8H,IAAI,EAAEC,EAAE,EAAE;EAChC1S,SAAS,CAAC2S,OAAO,CAACF,IAAI,EAAEC,EAAE,CAAC;EAC3B,OAAO3R,YAAY,CAAC,MAAMf,SAAS,CAAC4S,UAAU,CAACH,IAAI,CAAC,CAAC;AACzD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}