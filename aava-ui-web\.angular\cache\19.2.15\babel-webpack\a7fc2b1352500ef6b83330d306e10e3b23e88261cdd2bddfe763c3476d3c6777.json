{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { show } from '../../dom.js';\nimport { RunOnceScheduler } from '../../../common/async.js';\nimport { Disposable, MutableDisposable } from '../../../common/lifecycle.js';\nimport './progressbar.css';\nconst CSS_DONE = 'done';\nconst CSS_ACTIVE = 'active';\nconst CSS_INFINITE = 'infinite';\nconst CSS_INFINITE_LONG_RUNNING = 'infinite-long-running';\nconst CSS_DISCRETE = 'discrete';\n/**\n * A progress bar with support for infinite or discrete progress.\n */\nexport let ProgressBar = /*#__PURE__*/(() => {\n  class ProgressBar extends Disposable {\n    /**\n     * After a certain time of showing the progress bar, switch\n     * to long-running mode and throttle animations to reduce\n     * the pressure on the GPU process.\n     *\n     * https://github.com/microsoft/vscode/issues/97900\n     * https://github.com/microsoft/vscode/issues/138396\n     */\n    static {\n      this.LONG_RUNNING_INFINITE_THRESHOLD = 10000;\n    }\n    constructor(container, options) {\n      super();\n      this.progressSignal = this._register(new MutableDisposable());\n      this.workedVal = 0;\n      this.showDelayedScheduler = this._register(new RunOnceScheduler(() => show(this.element), 0));\n      this.longRunningScheduler = this._register(new RunOnceScheduler(() => this.infiniteLongRunning(), ProgressBar.LONG_RUNNING_INFINITE_THRESHOLD));\n      this.create(container, options);\n    }\n    create(container, options) {\n      this.element = document.createElement('div');\n      this.element.classList.add('monaco-progress-container');\n      this.element.setAttribute('role', 'progressbar');\n      this.element.setAttribute('aria-valuemin', '0');\n      container.appendChild(this.element);\n      this.bit = document.createElement('div');\n      this.bit.classList.add('progress-bit');\n      this.bit.style.backgroundColor = options?.progressBarBackground || '#0E70C0';\n      this.element.appendChild(this.bit);\n    }\n    off() {\n      this.bit.style.width = 'inherit';\n      this.bit.style.opacity = '1';\n      this.element.classList.remove(CSS_ACTIVE, CSS_INFINITE, CSS_INFINITE_LONG_RUNNING, CSS_DISCRETE);\n      this.workedVal = 0;\n      this.totalWork = undefined;\n      this.longRunningScheduler.cancel();\n      this.progressSignal.clear();\n    }\n    /**\n     * Stops the progressbar from showing any progress instantly without fading out.\n     */\n    stop() {\n      return this.doDone(false);\n    }\n    doDone(delayed) {\n      this.element.classList.add(CSS_DONE);\n      // discrete: let it grow to 100% width and hide afterwards\n      if (!this.element.classList.contains(CSS_INFINITE)) {\n        this.bit.style.width = 'inherit';\n        if (delayed) {\n          setTimeout(() => this.off(), 200);\n        } else {\n          this.off();\n        }\n      }\n      // infinite: let it fade out and hide afterwards\n      else {\n        this.bit.style.opacity = '0';\n        if (delayed) {\n          setTimeout(() => this.off(), 200);\n        } else {\n          this.off();\n        }\n      }\n      return this;\n    }\n    /**\n     * Use this mode to indicate progress that has no total number of work units.\n     */\n    infinite() {\n      this.bit.style.width = '2%';\n      this.bit.style.opacity = '1';\n      this.element.classList.remove(CSS_DISCRETE, CSS_DONE, CSS_INFINITE_LONG_RUNNING);\n      this.element.classList.add(CSS_ACTIVE, CSS_INFINITE);\n      this.longRunningScheduler.schedule();\n      return this;\n    }\n    infiniteLongRunning() {\n      this.element.classList.add(CSS_INFINITE_LONG_RUNNING);\n    }\n    getContainer() {\n      return this.element;\n    }\n  }\n  return ProgressBar;\n})();", "map": {"version": 3, "names": ["show", "RunOnceScheduler", "Disposable", "MutableDisposable", "CSS_DONE", "CSS_ACTIVE", "CSS_INFINITE", "CSS_INFINITE_LONG_RUNNING", "CSS_DISCRETE", "ProgressBar", "LONG_RUNNING_INFINITE_THRESHOLD", "constructor", "container", "options", "progressSignal", "_register", "workedVal", "showDelayedScheduler", "element", "longRunningScheduler", "infiniteLongRunning", "create", "document", "createElement", "classList", "add", "setAttribute", "append<PERSON><PERSON><PERSON>", "bit", "style", "backgroundColor", "progressBarBackground", "off", "width", "opacity", "remove", "totalWork", "undefined", "cancel", "clear", "stop", "doDone", "delayed", "contains", "setTimeout", "infinite", "schedule", "getContainer"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/browser/ui/progressbar/progressbar.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { show } from '../../dom.js';\nimport { RunOnceScheduler } from '../../../common/async.js';\nimport { Disposable, MutableDisposable } from '../../../common/lifecycle.js';\nimport './progressbar.css';\nconst CSS_DONE = 'done';\nconst CSS_ACTIVE = 'active';\nconst CSS_INFINITE = 'infinite';\nconst CSS_INFINITE_LONG_RUNNING = 'infinite-long-running';\nconst CSS_DISCRETE = 'discrete';\n/**\n * A progress bar with support for infinite or discrete progress.\n */\nexport class ProgressBar extends Disposable {\n    /**\n     * After a certain time of showing the progress bar, switch\n     * to long-running mode and throttle animations to reduce\n     * the pressure on the GPU process.\n     *\n     * https://github.com/microsoft/vscode/issues/97900\n     * https://github.com/microsoft/vscode/issues/138396\n     */\n    static { this.LONG_RUNNING_INFINITE_THRESHOLD = 10000; }\n    constructor(container, options) {\n        super();\n        this.progressSignal = this._register(new MutableDisposable());\n        this.workedVal = 0;\n        this.showDelayedScheduler = this._register(new RunOnceScheduler(() => show(this.element), 0));\n        this.longRunningScheduler = this._register(new RunOnceScheduler(() => this.infiniteLongRunning(), ProgressBar.LONG_RUNNING_INFINITE_THRESHOLD));\n        this.create(container, options);\n    }\n    create(container, options) {\n        this.element = document.createElement('div');\n        this.element.classList.add('monaco-progress-container');\n        this.element.setAttribute('role', 'progressbar');\n        this.element.setAttribute('aria-valuemin', '0');\n        container.appendChild(this.element);\n        this.bit = document.createElement('div');\n        this.bit.classList.add('progress-bit');\n        this.bit.style.backgroundColor = options?.progressBarBackground || '#0E70C0';\n        this.element.appendChild(this.bit);\n    }\n    off() {\n        this.bit.style.width = 'inherit';\n        this.bit.style.opacity = '1';\n        this.element.classList.remove(CSS_ACTIVE, CSS_INFINITE, CSS_INFINITE_LONG_RUNNING, CSS_DISCRETE);\n        this.workedVal = 0;\n        this.totalWork = undefined;\n        this.longRunningScheduler.cancel();\n        this.progressSignal.clear();\n    }\n    /**\n     * Stops the progressbar from showing any progress instantly without fading out.\n     */\n    stop() {\n        return this.doDone(false);\n    }\n    doDone(delayed) {\n        this.element.classList.add(CSS_DONE);\n        // discrete: let it grow to 100% width and hide afterwards\n        if (!this.element.classList.contains(CSS_INFINITE)) {\n            this.bit.style.width = 'inherit';\n            if (delayed) {\n                setTimeout(() => this.off(), 200);\n            }\n            else {\n                this.off();\n            }\n        }\n        // infinite: let it fade out and hide afterwards\n        else {\n            this.bit.style.opacity = '0';\n            if (delayed) {\n                setTimeout(() => this.off(), 200);\n            }\n            else {\n                this.off();\n            }\n        }\n        return this;\n    }\n    /**\n     * Use this mode to indicate progress that has no total number of work units.\n     */\n    infinite() {\n        this.bit.style.width = '2%';\n        this.bit.style.opacity = '1';\n        this.element.classList.remove(CSS_DISCRETE, CSS_DONE, CSS_INFINITE_LONG_RUNNING);\n        this.element.classList.add(CSS_ACTIVE, CSS_INFINITE);\n        this.longRunningScheduler.schedule();\n        return this;\n    }\n    infiniteLongRunning() {\n        this.element.classList.add(CSS_INFINITE_LONG_RUNNING);\n    }\n    getContainer() {\n        return this.element;\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,IAAI,QAAQ,cAAc;AACnC,SAASC,gBAAgB,QAAQ,0BAA0B;AAC3D,SAASC,UAAU,EAAEC,iBAAiB,QAAQ,8BAA8B;AAC5E,OAAO,mBAAmB;AAC1B,MAAMC,QAAQ,GAAG,MAAM;AACvB,MAAMC,UAAU,GAAG,QAAQ;AAC3B,MAAMC,YAAY,GAAG,UAAU;AAC/B,MAAMC,yBAAyB,GAAG,uBAAuB;AACzD,MAAMC,YAAY,GAAG,UAAU;AAC/B;AACA;AACA;AACA,WAAaC,WAAW;EAAjB,MAAMA,WAAW,SAASP,UAAU,CAAC;IACxC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;IACI;MAAS,IAAI,CAACQ,+BAA+B,GAAG,KAAK;IAAE;IACvDC,WAAWA,CAACC,SAAS,EAAEC,OAAO,EAAE;MAC5B,KAAK,CAAC,CAAC;MACP,IAAI,CAACC,cAAc,GAAG,IAAI,CAACC,SAAS,CAAC,IAAIZ,iBAAiB,CAAC,CAAC,CAAC;MAC7D,IAAI,CAACa,SAAS,GAAG,CAAC;MAClB,IAAI,CAACC,oBAAoB,GAAG,IAAI,CAACF,SAAS,CAAC,IAAId,gBAAgB,CAAC,MAAMD,IAAI,CAAC,IAAI,CAACkB,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;MAC7F,IAAI,CAACC,oBAAoB,GAAG,IAAI,CAACJ,SAAS,CAAC,IAAId,gBAAgB,CAAC,MAAM,IAAI,CAACmB,mBAAmB,CAAC,CAAC,EAAEX,WAAW,CAACC,+BAA+B,CAAC,CAAC;MAC/I,IAAI,CAACW,MAAM,CAACT,SAAS,EAAEC,OAAO,CAAC;IACnC;IACAQ,MAAMA,CAACT,SAAS,EAAEC,OAAO,EAAE;MACvB,IAAI,CAACK,OAAO,GAAGI,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAC5C,IAAI,CAACL,OAAO,CAACM,SAAS,CAACC,GAAG,CAAC,2BAA2B,CAAC;MACvD,IAAI,CAACP,OAAO,CAACQ,YAAY,CAAC,MAAM,EAAE,aAAa,CAAC;MAChD,IAAI,CAACR,OAAO,CAACQ,YAAY,CAAC,eAAe,EAAE,GAAG,CAAC;MAC/Cd,SAAS,CAACe,WAAW,CAAC,IAAI,CAACT,OAAO,CAAC;MACnC,IAAI,CAACU,GAAG,GAAGN,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MACxC,IAAI,CAACK,GAAG,CAACJ,SAAS,CAACC,GAAG,CAAC,cAAc,CAAC;MACtC,IAAI,CAACG,GAAG,CAACC,KAAK,CAACC,eAAe,GAAGjB,OAAO,EAAEkB,qBAAqB,IAAI,SAAS;MAC5E,IAAI,CAACb,OAAO,CAACS,WAAW,CAAC,IAAI,CAACC,GAAG,CAAC;IACtC;IACAI,GAAGA,CAAA,EAAG;MACF,IAAI,CAACJ,GAAG,CAACC,KAAK,CAACI,KAAK,GAAG,SAAS;MAChC,IAAI,CAACL,GAAG,CAACC,KAAK,CAACK,OAAO,GAAG,GAAG;MAC5B,IAAI,CAAChB,OAAO,CAACM,SAAS,CAACW,MAAM,CAAC9B,UAAU,EAAEC,YAAY,EAAEC,yBAAyB,EAAEC,YAAY,CAAC;MAChG,IAAI,CAACQ,SAAS,GAAG,CAAC;MAClB,IAAI,CAACoB,SAAS,GAAGC,SAAS;MAC1B,IAAI,CAAClB,oBAAoB,CAACmB,MAAM,CAAC,CAAC;MAClC,IAAI,CAACxB,cAAc,CAACyB,KAAK,CAAC,CAAC;IAC/B;IACA;AACJ;AACA;IACIC,IAAIA,CAAA,EAAG;MACH,OAAO,IAAI,CAACC,MAAM,CAAC,KAAK,CAAC;IAC7B;IACAA,MAAMA,CAACC,OAAO,EAAE;MACZ,IAAI,CAACxB,OAAO,CAACM,SAAS,CAACC,GAAG,CAACrB,QAAQ,CAAC;MACpC;MACA,IAAI,CAAC,IAAI,CAACc,OAAO,CAACM,SAAS,CAACmB,QAAQ,CAACrC,YAAY,CAAC,EAAE;QAChD,IAAI,CAACsB,GAAG,CAACC,KAAK,CAACI,KAAK,GAAG,SAAS;QAChC,IAAIS,OAAO,EAAE;UACTE,UAAU,CAAC,MAAM,IAAI,CAACZ,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC;QACrC,CAAC,MACI;UACD,IAAI,CAACA,GAAG,CAAC,CAAC;QACd;MACJ;MACA;MAAA,KACK;QACD,IAAI,CAACJ,GAAG,CAACC,KAAK,CAACK,OAAO,GAAG,GAAG;QAC5B,IAAIQ,OAAO,EAAE;UACTE,UAAU,CAAC,MAAM,IAAI,CAACZ,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC;QACrC,CAAC,MACI;UACD,IAAI,CAACA,GAAG,CAAC,CAAC;QACd;MACJ;MACA,OAAO,IAAI;IACf;IACA;AACJ;AACA;IACIa,QAAQA,CAAA,EAAG;MACP,IAAI,CAACjB,GAAG,CAACC,KAAK,CAACI,KAAK,GAAG,IAAI;MAC3B,IAAI,CAACL,GAAG,CAACC,KAAK,CAACK,OAAO,GAAG,GAAG;MAC5B,IAAI,CAAChB,OAAO,CAACM,SAAS,CAACW,MAAM,CAAC3B,YAAY,EAAEJ,QAAQ,EAAEG,yBAAyB,CAAC;MAChF,IAAI,CAACW,OAAO,CAACM,SAAS,CAACC,GAAG,CAACpB,UAAU,EAAEC,YAAY,CAAC;MACpD,IAAI,CAACa,oBAAoB,CAAC2B,QAAQ,CAAC,CAAC;MACpC,OAAO,IAAI;IACf;IACA1B,mBAAmBA,CAAA,EAAG;MAClB,IAAI,CAACF,OAAO,CAACM,SAAS,CAACC,GAAG,CAAClB,yBAAyB,CAAC;IACzD;IACAwC,YAAYA,CAAA,EAAG;MACX,OAAO,IAAI,CAAC7B,OAAO;IACvB;EACJ;EAAC,OArFYT,WAAW;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}