{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as strings from '../../../base/common/strings.js';\nimport { Range } from '../../common/core/range.js';\nexport const _debugComposition = false;\nexport class TextAreaState {\n  static {\n    this.EMPTY = new TextAreaState('', 0, 0, null, undefined);\n  }\n  constructor(value, /** the offset where selection starts inside `value` */\n  selectionStart, /** the offset where selection ends inside `value` */\n  selectionEnd, /** the editor range in the view coordinate system that matches the selection inside `value` */\n  selection, /** the visible line count (wrapped, not necessarily matching \\n characters) for the text in `value` before `selectionStart` */\n  newlineCountBeforeSelection) {\n    this.value = value;\n    this.selectionStart = selectionStart;\n    this.selectionEnd = selectionEnd;\n    this.selection = selection;\n    this.newlineCountBeforeSelection = newlineCountBeforeSelection;\n  }\n  toString() {\n    return `[ <${this.value}>, selectionStart: ${this.selectionStart}, selectionEnd: ${this.selectionEnd}]`;\n  }\n  static readFromTextArea(textArea, previousState) {\n    const value = textArea.getValue();\n    const selectionStart = textArea.getSelectionStart();\n    const selectionEnd = textArea.getSelectionEnd();\n    let newlineCountBeforeSelection = undefined;\n    if (previousState) {\n      const valueBeforeSelectionStart = value.substring(0, selectionStart);\n      const previousValueBeforeSelectionStart = previousState.value.substring(0, previousState.selectionStart);\n      if (valueBeforeSelectionStart === previousValueBeforeSelectionStart) {\n        newlineCountBeforeSelection = previousState.newlineCountBeforeSelection;\n      }\n    }\n    return new TextAreaState(value, selectionStart, selectionEnd, null, newlineCountBeforeSelection);\n  }\n  collapseSelection() {\n    if (this.selectionStart === this.value.length) {\n      return this;\n    }\n    return new TextAreaState(this.value, this.value.length, this.value.length, null, undefined);\n  }\n  writeToTextArea(reason, textArea, select) {\n    if (_debugComposition) {\n      console.log(`writeToTextArea ${reason}: ${this.toString()}`);\n    }\n    textArea.setValue(reason, this.value);\n    if (select) {\n      textArea.setSelectionRange(reason, this.selectionStart, this.selectionEnd);\n    }\n  }\n  deduceEditorPosition(offset) {\n    if (offset <= this.selectionStart) {\n      const str = this.value.substring(offset, this.selectionStart);\n      return this._finishDeduceEditorPosition(this.selection?.getStartPosition() ?? null, str, -1);\n    }\n    if (offset >= this.selectionEnd) {\n      const str = this.value.substring(this.selectionEnd, offset);\n      return this._finishDeduceEditorPosition(this.selection?.getEndPosition() ?? null, str, 1);\n    }\n    const str1 = this.value.substring(this.selectionStart, offset);\n    if (str1.indexOf(String.fromCharCode(8230)) === -1) {\n      return this._finishDeduceEditorPosition(this.selection?.getStartPosition() ?? null, str1, 1);\n    }\n    const str2 = this.value.substring(offset, this.selectionEnd);\n    return this._finishDeduceEditorPosition(this.selection?.getEndPosition() ?? null, str2, -1);\n  }\n  _finishDeduceEditorPosition(anchor, deltaText, signum) {\n    let lineFeedCnt = 0;\n    let lastLineFeedIndex = -1;\n    while ((lastLineFeedIndex = deltaText.indexOf('\\n', lastLineFeedIndex + 1)) !== -1) {\n      lineFeedCnt++;\n    }\n    return [anchor, signum * deltaText.length, lineFeedCnt];\n  }\n  static deduceInput(previousState, currentState, couldBeEmojiInput) {\n    if (!previousState) {\n      // This is the EMPTY state\n      return {\n        text: '',\n        replacePrevCharCnt: 0,\n        replaceNextCharCnt: 0,\n        positionDelta: 0\n      };\n    }\n    if (_debugComposition) {\n      console.log('------------------------deduceInput');\n      console.log(`PREVIOUS STATE: ${previousState.toString()}`);\n      console.log(`CURRENT STATE: ${currentState.toString()}`);\n    }\n    const prefixLength = Math.min(strings.commonPrefixLength(previousState.value, currentState.value), previousState.selectionStart, currentState.selectionStart);\n    const suffixLength = Math.min(strings.commonSuffixLength(previousState.value, currentState.value), previousState.value.length - previousState.selectionEnd, currentState.value.length - currentState.selectionEnd);\n    const previousValue = previousState.value.substring(prefixLength, previousState.value.length - suffixLength);\n    const currentValue = currentState.value.substring(prefixLength, currentState.value.length - suffixLength);\n    const previousSelectionStart = previousState.selectionStart - prefixLength;\n    const previousSelectionEnd = previousState.selectionEnd - prefixLength;\n    const currentSelectionStart = currentState.selectionStart - prefixLength;\n    const currentSelectionEnd = currentState.selectionEnd - prefixLength;\n    if (_debugComposition) {\n      console.log(`AFTER DIFFING PREVIOUS STATE: <${previousValue}>, selectionStart: ${previousSelectionStart}, selectionEnd: ${previousSelectionEnd}`);\n      console.log(`AFTER DIFFING CURRENT STATE: <${currentValue}>, selectionStart: ${currentSelectionStart}, selectionEnd: ${currentSelectionEnd}`);\n    }\n    if (currentSelectionStart === currentSelectionEnd) {\n      // no current selection\n      const replacePreviousCharacters = previousState.selectionStart - prefixLength;\n      if (_debugComposition) {\n        console.log(`REMOVE PREVIOUS: ${replacePreviousCharacters} chars`);\n      }\n      return {\n        text: currentValue,\n        replacePrevCharCnt: replacePreviousCharacters,\n        replaceNextCharCnt: 0,\n        positionDelta: 0\n      };\n    }\n    // there is a current selection => composition case\n    const replacePreviousCharacters = previousSelectionEnd - previousSelectionStart;\n    return {\n      text: currentValue,\n      replacePrevCharCnt: replacePreviousCharacters,\n      replaceNextCharCnt: 0,\n      positionDelta: 0\n    };\n  }\n  static deduceAndroidCompositionInput(previousState, currentState) {\n    if (!previousState) {\n      // This is the EMPTY state\n      return {\n        text: '',\n        replacePrevCharCnt: 0,\n        replaceNextCharCnt: 0,\n        positionDelta: 0\n      };\n    }\n    if (_debugComposition) {\n      console.log('------------------------deduceAndroidCompositionInput');\n      console.log(`PREVIOUS STATE: ${previousState.toString()}`);\n      console.log(`CURRENT STATE: ${currentState.toString()}`);\n    }\n    if (previousState.value === currentState.value) {\n      return {\n        text: '',\n        replacePrevCharCnt: 0,\n        replaceNextCharCnt: 0,\n        positionDelta: currentState.selectionEnd - previousState.selectionEnd\n      };\n    }\n    const prefixLength = Math.min(strings.commonPrefixLength(previousState.value, currentState.value), previousState.selectionEnd);\n    const suffixLength = Math.min(strings.commonSuffixLength(previousState.value, currentState.value), previousState.value.length - previousState.selectionEnd);\n    const previousValue = previousState.value.substring(prefixLength, previousState.value.length - suffixLength);\n    const currentValue = currentState.value.substring(prefixLength, currentState.value.length - suffixLength);\n    const previousSelectionStart = previousState.selectionStart - prefixLength;\n    const previousSelectionEnd = previousState.selectionEnd - prefixLength;\n    const currentSelectionStart = currentState.selectionStart - prefixLength;\n    const currentSelectionEnd = currentState.selectionEnd - prefixLength;\n    if (_debugComposition) {\n      console.log(`AFTER DIFFING PREVIOUS STATE: <${previousValue}>, selectionStart: ${previousSelectionStart}, selectionEnd: ${previousSelectionEnd}`);\n      console.log(`AFTER DIFFING CURRENT STATE: <${currentValue}>, selectionStart: ${currentSelectionStart}, selectionEnd: ${currentSelectionEnd}`);\n    }\n    return {\n      text: currentValue,\n      replacePrevCharCnt: previousSelectionEnd,\n      replaceNextCharCnt: previousValue.length - previousSelectionEnd,\n      positionDelta: currentSelectionEnd - currentValue.length\n    };\n  }\n}\nexport class PagedScreenReaderStrategy {\n  static _getPageOfLine(lineNumber, linesPerPage) {\n    return Math.floor((lineNumber - 1) / linesPerPage);\n  }\n  static _getRangeForPage(page, linesPerPage) {\n    const offset = page * linesPerPage;\n    const startLineNumber = offset + 1;\n    const endLineNumber = offset + linesPerPage;\n    return new Range(startLineNumber, 1, endLineNumber + 1, 1);\n  }\n  static fromEditorSelection(model, selection, linesPerPage, trimLongText) {\n    // Chromium handles very poorly text even of a few thousand chars\n    // Cut text to avoid stalling the entire UI\n    const LIMIT_CHARS = 500;\n    const selectionStartPage = PagedScreenReaderStrategy._getPageOfLine(selection.startLineNumber, linesPerPage);\n    const selectionStartPageRange = PagedScreenReaderStrategy._getRangeForPage(selectionStartPage, linesPerPage);\n    const selectionEndPage = PagedScreenReaderStrategy._getPageOfLine(selection.endLineNumber, linesPerPage);\n    const selectionEndPageRange = PagedScreenReaderStrategy._getRangeForPage(selectionEndPage, linesPerPage);\n    let pretextRange = selectionStartPageRange.intersectRanges(new Range(1, 1, selection.startLineNumber, selection.startColumn));\n    if (trimLongText && model.getValueLengthInRange(pretextRange, 1 /* EndOfLinePreference.LF */) > LIMIT_CHARS) {\n      const pretextStart = model.modifyPosition(pretextRange.getEndPosition(), -LIMIT_CHARS);\n      pretextRange = Range.fromPositions(pretextStart, pretextRange.getEndPosition());\n    }\n    const pretext = model.getValueInRange(pretextRange, 1 /* EndOfLinePreference.LF */);\n    const lastLine = model.getLineCount();\n    const lastLineMaxColumn = model.getLineMaxColumn(lastLine);\n    let posttextRange = selectionEndPageRange.intersectRanges(new Range(selection.endLineNumber, selection.endColumn, lastLine, lastLineMaxColumn));\n    if (trimLongText && model.getValueLengthInRange(posttextRange, 1 /* EndOfLinePreference.LF */) > LIMIT_CHARS) {\n      const posttextEnd = model.modifyPosition(posttextRange.getStartPosition(), LIMIT_CHARS);\n      posttextRange = Range.fromPositions(posttextRange.getStartPosition(), posttextEnd);\n    }\n    const posttext = model.getValueInRange(posttextRange, 1 /* EndOfLinePreference.LF */);\n    let text;\n    if (selectionStartPage === selectionEndPage || selectionStartPage + 1 === selectionEndPage) {\n      // take full selection\n      text = model.getValueInRange(selection, 1 /* EndOfLinePreference.LF */);\n    } else {\n      const selectionRange1 = selectionStartPageRange.intersectRanges(selection);\n      const selectionRange2 = selectionEndPageRange.intersectRanges(selection);\n      text = model.getValueInRange(selectionRange1, 1 /* EndOfLinePreference.LF */) + String.fromCharCode(8230) + model.getValueInRange(selectionRange2, 1 /* EndOfLinePreference.LF */);\n    }\n    if (trimLongText && text.length > 2 * LIMIT_CHARS) {\n      text = text.substring(0, LIMIT_CHARS) + String.fromCharCode(8230) + text.substring(text.length - LIMIT_CHARS, text.length);\n    }\n    return new TextAreaState(pretext + text + posttext, pretext.length, pretext.length + text.length, selection, pretextRange.endLineNumber - pretextRange.startLineNumber);\n  }\n}", "map": {"version": 3, "names": ["strings", "Range", "_debugComposition", "TextAreaState", "EMPTY", "undefined", "constructor", "value", "selectionStart", "selectionEnd", "selection", "newlineCountBeforeSelection", "toString", "readFromTextArea", "textArea", "previousState", "getValue", "getSelectionStart", "getSelectionEnd", "valueBeforeSelectionStart", "substring", "previousValueBeforeSelectionStart", "collapseSelection", "length", "writeToTextArea", "reason", "select", "console", "log", "setValue", "setSelectionRange", "deduceEditorPosition", "offset", "str", "_finishDeduceEditorPosition", "getStartPosition", "getEndPosition", "str1", "indexOf", "String", "fromCharCode", "str2", "anchor", "deltaText", "signum", "lineFeedCnt", "lastLineFeedIndex", "deduceInput", "currentState", "couldBeEmojiInput", "text", "replacePrevCharCnt", "replaceNextCharCnt", "<PERSON><PERSON><PERSON><PERSON>", "prefixLength", "Math", "min", "commonPrefixLength", "suffixLength", "commonSuffixLength", "previousValue", "currentValue", "previousSelectionStart", "previousSelectionEnd", "currentSelectionStart", "currentSelectionEnd", "replacePreviousCharacters", "deduceAndroidCompositionInput", "PagedScreenReaderStrategy", "_getPageOfLine", "lineNumber", "linesPerPage", "floor", "_getRangeForPage", "page", "startLineNumber", "endLineNumber", "fromEditorSelection", "model", "trimLongText", "LIMIT_CHARS", "selectionStartPage", "selectionStartPageRange", "selectionEndPage", "selectionEndPageRange", "pretextR<PERSON>e", "intersectRanges", "startColumn", "getValueLengthInRange", "pretextStart", "modifyPosition", "fromPositions", "pretext", "getValueInRange", "lastLine", "getLineCount", "lastLineMaxColumn", "getLineMaxColumn", "posttextRange", "endColumn", "posttextEnd", "posttext", "selectionRange1", "selectionRange2"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/browser/controller/textAreaState.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as strings from '../../../base/common/strings.js';\nimport { Range } from '../../common/core/range.js';\nexport const _debugComposition = false;\nexport class TextAreaState {\n    static { this.EMPTY = new TextAreaState('', 0, 0, null, undefined); }\n    constructor(value, \n    /** the offset where selection starts inside `value` */\n    selectionStart, \n    /** the offset where selection ends inside `value` */\n    selectionEnd, \n    /** the editor range in the view coordinate system that matches the selection inside `value` */\n    selection, \n    /** the visible line count (wrapped, not necessarily matching \\n characters) for the text in `value` before `selectionStart` */\n    newlineCountBeforeSelection) {\n        this.value = value;\n        this.selectionStart = selectionStart;\n        this.selectionEnd = selectionEnd;\n        this.selection = selection;\n        this.newlineCountBeforeSelection = newlineCountBeforeSelection;\n    }\n    toString() {\n        return `[ <${this.value}>, selectionStart: ${this.selectionStart}, selectionEnd: ${this.selectionEnd}]`;\n    }\n    static readFromTextArea(textArea, previousState) {\n        const value = textArea.getValue();\n        const selectionStart = textArea.getSelectionStart();\n        const selectionEnd = textArea.getSelectionEnd();\n        let newlineCountBeforeSelection = undefined;\n        if (previousState) {\n            const valueBeforeSelectionStart = value.substring(0, selectionStart);\n            const previousValueBeforeSelectionStart = previousState.value.substring(0, previousState.selectionStart);\n            if (valueBeforeSelectionStart === previousValueBeforeSelectionStart) {\n                newlineCountBeforeSelection = previousState.newlineCountBeforeSelection;\n            }\n        }\n        return new TextAreaState(value, selectionStart, selectionEnd, null, newlineCountBeforeSelection);\n    }\n    collapseSelection() {\n        if (this.selectionStart === this.value.length) {\n            return this;\n        }\n        return new TextAreaState(this.value, this.value.length, this.value.length, null, undefined);\n    }\n    writeToTextArea(reason, textArea, select) {\n        if (_debugComposition) {\n            console.log(`writeToTextArea ${reason}: ${this.toString()}`);\n        }\n        textArea.setValue(reason, this.value);\n        if (select) {\n            textArea.setSelectionRange(reason, this.selectionStart, this.selectionEnd);\n        }\n    }\n    deduceEditorPosition(offset) {\n        if (offset <= this.selectionStart) {\n            const str = this.value.substring(offset, this.selectionStart);\n            return this._finishDeduceEditorPosition(this.selection?.getStartPosition() ?? null, str, -1);\n        }\n        if (offset >= this.selectionEnd) {\n            const str = this.value.substring(this.selectionEnd, offset);\n            return this._finishDeduceEditorPosition(this.selection?.getEndPosition() ?? null, str, 1);\n        }\n        const str1 = this.value.substring(this.selectionStart, offset);\n        if (str1.indexOf(String.fromCharCode(8230)) === -1) {\n            return this._finishDeduceEditorPosition(this.selection?.getStartPosition() ?? null, str1, 1);\n        }\n        const str2 = this.value.substring(offset, this.selectionEnd);\n        return this._finishDeduceEditorPosition(this.selection?.getEndPosition() ?? null, str2, -1);\n    }\n    _finishDeduceEditorPosition(anchor, deltaText, signum) {\n        let lineFeedCnt = 0;\n        let lastLineFeedIndex = -1;\n        while ((lastLineFeedIndex = deltaText.indexOf('\\n', lastLineFeedIndex + 1)) !== -1) {\n            lineFeedCnt++;\n        }\n        return [anchor, signum * deltaText.length, lineFeedCnt];\n    }\n    static deduceInput(previousState, currentState, couldBeEmojiInput) {\n        if (!previousState) {\n            // This is the EMPTY state\n            return {\n                text: '',\n                replacePrevCharCnt: 0,\n                replaceNextCharCnt: 0,\n                positionDelta: 0\n            };\n        }\n        if (_debugComposition) {\n            console.log('------------------------deduceInput');\n            console.log(`PREVIOUS STATE: ${previousState.toString()}`);\n            console.log(`CURRENT STATE: ${currentState.toString()}`);\n        }\n        const prefixLength = Math.min(strings.commonPrefixLength(previousState.value, currentState.value), previousState.selectionStart, currentState.selectionStart);\n        const suffixLength = Math.min(strings.commonSuffixLength(previousState.value, currentState.value), previousState.value.length - previousState.selectionEnd, currentState.value.length - currentState.selectionEnd);\n        const previousValue = previousState.value.substring(prefixLength, previousState.value.length - suffixLength);\n        const currentValue = currentState.value.substring(prefixLength, currentState.value.length - suffixLength);\n        const previousSelectionStart = previousState.selectionStart - prefixLength;\n        const previousSelectionEnd = previousState.selectionEnd - prefixLength;\n        const currentSelectionStart = currentState.selectionStart - prefixLength;\n        const currentSelectionEnd = currentState.selectionEnd - prefixLength;\n        if (_debugComposition) {\n            console.log(`AFTER DIFFING PREVIOUS STATE: <${previousValue}>, selectionStart: ${previousSelectionStart}, selectionEnd: ${previousSelectionEnd}`);\n            console.log(`AFTER DIFFING CURRENT STATE: <${currentValue}>, selectionStart: ${currentSelectionStart}, selectionEnd: ${currentSelectionEnd}`);\n        }\n        if (currentSelectionStart === currentSelectionEnd) {\n            // no current selection\n            const replacePreviousCharacters = (previousState.selectionStart - prefixLength);\n            if (_debugComposition) {\n                console.log(`REMOVE PREVIOUS: ${replacePreviousCharacters} chars`);\n            }\n            return {\n                text: currentValue,\n                replacePrevCharCnt: replacePreviousCharacters,\n                replaceNextCharCnt: 0,\n                positionDelta: 0\n            };\n        }\n        // there is a current selection => composition case\n        const replacePreviousCharacters = previousSelectionEnd - previousSelectionStart;\n        return {\n            text: currentValue,\n            replacePrevCharCnt: replacePreviousCharacters,\n            replaceNextCharCnt: 0,\n            positionDelta: 0\n        };\n    }\n    static deduceAndroidCompositionInput(previousState, currentState) {\n        if (!previousState) {\n            // This is the EMPTY state\n            return {\n                text: '',\n                replacePrevCharCnt: 0,\n                replaceNextCharCnt: 0,\n                positionDelta: 0\n            };\n        }\n        if (_debugComposition) {\n            console.log('------------------------deduceAndroidCompositionInput');\n            console.log(`PREVIOUS STATE: ${previousState.toString()}`);\n            console.log(`CURRENT STATE: ${currentState.toString()}`);\n        }\n        if (previousState.value === currentState.value) {\n            return {\n                text: '',\n                replacePrevCharCnt: 0,\n                replaceNextCharCnt: 0,\n                positionDelta: currentState.selectionEnd - previousState.selectionEnd\n            };\n        }\n        const prefixLength = Math.min(strings.commonPrefixLength(previousState.value, currentState.value), previousState.selectionEnd);\n        const suffixLength = Math.min(strings.commonSuffixLength(previousState.value, currentState.value), previousState.value.length - previousState.selectionEnd);\n        const previousValue = previousState.value.substring(prefixLength, previousState.value.length - suffixLength);\n        const currentValue = currentState.value.substring(prefixLength, currentState.value.length - suffixLength);\n        const previousSelectionStart = previousState.selectionStart - prefixLength;\n        const previousSelectionEnd = previousState.selectionEnd - prefixLength;\n        const currentSelectionStart = currentState.selectionStart - prefixLength;\n        const currentSelectionEnd = currentState.selectionEnd - prefixLength;\n        if (_debugComposition) {\n            console.log(`AFTER DIFFING PREVIOUS STATE: <${previousValue}>, selectionStart: ${previousSelectionStart}, selectionEnd: ${previousSelectionEnd}`);\n            console.log(`AFTER DIFFING CURRENT STATE: <${currentValue}>, selectionStart: ${currentSelectionStart}, selectionEnd: ${currentSelectionEnd}`);\n        }\n        return {\n            text: currentValue,\n            replacePrevCharCnt: previousSelectionEnd,\n            replaceNextCharCnt: previousValue.length - previousSelectionEnd,\n            positionDelta: currentSelectionEnd - currentValue.length\n        };\n    }\n}\nexport class PagedScreenReaderStrategy {\n    static _getPageOfLine(lineNumber, linesPerPage) {\n        return Math.floor((lineNumber - 1) / linesPerPage);\n    }\n    static _getRangeForPage(page, linesPerPage) {\n        const offset = page * linesPerPage;\n        const startLineNumber = offset + 1;\n        const endLineNumber = offset + linesPerPage;\n        return new Range(startLineNumber, 1, endLineNumber + 1, 1);\n    }\n    static fromEditorSelection(model, selection, linesPerPage, trimLongText) {\n        // Chromium handles very poorly text even of a few thousand chars\n        // Cut text to avoid stalling the entire UI\n        const LIMIT_CHARS = 500;\n        const selectionStartPage = PagedScreenReaderStrategy._getPageOfLine(selection.startLineNumber, linesPerPage);\n        const selectionStartPageRange = PagedScreenReaderStrategy._getRangeForPage(selectionStartPage, linesPerPage);\n        const selectionEndPage = PagedScreenReaderStrategy._getPageOfLine(selection.endLineNumber, linesPerPage);\n        const selectionEndPageRange = PagedScreenReaderStrategy._getRangeForPage(selectionEndPage, linesPerPage);\n        let pretextRange = selectionStartPageRange.intersectRanges(new Range(1, 1, selection.startLineNumber, selection.startColumn));\n        if (trimLongText && model.getValueLengthInRange(pretextRange, 1 /* EndOfLinePreference.LF */) > LIMIT_CHARS) {\n            const pretextStart = model.modifyPosition(pretextRange.getEndPosition(), -LIMIT_CHARS);\n            pretextRange = Range.fromPositions(pretextStart, pretextRange.getEndPosition());\n        }\n        const pretext = model.getValueInRange(pretextRange, 1 /* EndOfLinePreference.LF */);\n        const lastLine = model.getLineCount();\n        const lastLineMaxColumn = model.getLineMaxColumn(lastLine);\n        let posttextRange = selectionEndPageRange.intersectRanges(new Range(selection.endLineNumber, selection.endColumn, lastLine, lastLineMaxColumn));\n        if (trimLongText && model.getValueLengthInRange(posttextRange, 1 /* EndOfLinePreference.LF */) > LIMIT_CHARS) {\n            const posttextEnd = model.modifyPosition(posttextRange.getStartPosition(), LIMIT_CHARS);\n            posttextRange = Range.fromPositions(posttextRange.getStartPosition(), posttextEnd);\n        }\n        const posttext = model.getValueInRange(posttextRange, 1 /* EndOfLinePreference.LF */);\n        let text;\n        if (selectionStartPage === selectionEndPage || selectionStartPage + 1 === selectionEndPage) {\n            // take full selection\n            text = model.getValueInRange(selection, 1 /* EndOfLinePreference.LF */);\n        }\n        else {\n            const selectionRange1 = selectionStartPageRange.intersectRanges(selection);\n            const selectionRange2 = selectionEndPageRange.intersectRanges(selection);\n            text = (model.getValueInRange(selectionRange1, 1 /* EndOfLinePreference.LF */)\n                + String.fromCharCode(8230)\n                + model.getValueInRange(selectionRange2, 1 /* EndOfLinePreference.LF */));\n        }\n        if (trimLongText && text.length > 2 * LIMIT_CHARS) {\n            text = text.substring(0, LIMIT_CHARS) + String.fromCharCode(8230) + text.substring(text.length - LIMIT_CHARS, text.length);\n        }\n        return new TextAreaState(pretext + text + posttext, pretext.length, pretext.length + text.length, selection, pretextRange.endLineNumber - pretextRange.startLineNumber);\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,KAAKA,OAAO,MAAM,iCAAiC;AAC1D,SAASC,KAAK,QAAQ,4BAA4B;AAClD,OAAO,MAAMC,iBAAiB,GAAG,KAAK;AACtC,OAAO,MAAMC,aAAa,CAAC;EACvB;IAAS,IAAI,CAACC,KAAK,GAAG,IAAID,aAAa,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAEE,SAAS,CAAC;EAAE;EACpEC,WAAWA,CAACC,KAAK,EACjB;EACAC,cAAc,EACd;EACAC,YAAY,EACZ;EACAC,SAAS,EACT;EACAC,2BAA2B,EAAE;IACzB,IAAI,CAACJ,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,2BAA2B,GAAGA,2BAA2B;EAClE;EACAC,QAAQA,CAAA,EAAG;IACP,OAAO,MAAM,IAAI,CAACL,KAAK,sBAAsB,IAAI,CAACC,cAAc,mBAAmB,IAAI,CAACC,YAAY,GAAG;EAC3G;EACA,OAAOI,gBAAgBA,CAACC,QAAQ,EAAEC,aAAa,EAAE;IAC7C,MAAMR,KAAK,GAAGO,QAAQ,CAACE,QAAQ,CAAC,CAAC;IACjC,MAAMR,cAAc,GAAGM,QAAQ,CAACG,iBAAiB,CAAC,CAAC;IACnD,MAAMR,YAAY,GAAGK,QAAQ,CAACI,eAAe,CAAC,CAAC;IAC/C,IAAIP,2BAA2B,GAAGN,SAAS;IAC3C,IAAIU,aAAa,EAAE;MACf,MAAMI,yBAAyB,GAAGZ,KAAK,CAACa,SAAS,CAAC,CAAC,EAAEZ,cAAc,CAAC;MACpE,MAAMa,iCAAiC,GAAGN,aAAa,CAACR,KAAK,CAACa,SAAS,CAAC,CAAC,EAAEL,aAAa,CAACP,cAAc,CAAC;MACxG,IAAIW,yBAAyB,KAAKE,iCAAiC,EAAE;QACjEV,2BAA2B,GAAGI,aAAa,CAACJ,2BAA2B;MAC3E;IACJ;IACA,OAAO,IAAIR,aAAa,CAACI,KAAK,EAAEC,cAAc,EAAEC,YAAY,EAAE,IAAI,EAAEE,2BAA2B,CAAC;EACpG;EACAW,iBAAiBA,CAAA,EAAG;IAChB,IAAI,IAAI,CAACd,cAAc,KAAK,IAAI,CAACD,KAAK,CAACgB,MAAM,EAAE;MAC3C,OAAO,IAAI;IACf;IACA,OAAO,IAAIpB,aAAa,CAAC,IAAI,CAACI,KAAK,EAAE,IAAI,CAACA,KAAK,CAACgB,MAAM,EAAE,IAAI,CAAChB,KAAK,CAACgB,MAAM,EAAE,IAAI,EAAElB,SAAS,CAAC;EAC/F;EACAmB,eAAeA,CAACC,MAAM,EAAEX,QAAQ,EAAEY,MAAM,EAAE;IACtC,IAAIxB,iBAAiB,EAAE;MACnByB,OAAO,CAACC,GAAG,CAAC,mBAAmBH,MAAM,KAAK,IAAI,CAACb,QAAQ,CAAC,CAAC,EAAE,CAAC;IAChE;IACAE,QAAQ,CAACe,QAAQ,CAACJ,MAAM,EAAE,IAAI,CAAClB,KAAK,CAAC;IACrC,IAAImB,MAAM,EAAE;MACRZ,QAAQ,CAACgB,iBAAiB,CAACL,MAAM,EAAE,IAAI,CAACjB,cAAc,EAAE,IAAI,CAACC,YAAY,CAAC;IAC9E;EACJ;EACAsB,oBAAoBA,CAACC,MAAM,EAAE;IACzB,IAAIA,MAAM,IAAI,IAAI,CAACxB,cAAc,EAAE;MAC/B,MAAMyB,GAAG,GAAG,IAAI,CAAC1B,KAAK,CAACa,SAAS,CAACY,MAAM,EAAE,IAAI,CAACxB,cAAc,CAAC;MAC7D,OAAO,IAAI,CAAC0B,2BAA2B,CAAC,IAAI,CAACxB,SAAS,EAAEyB,gBAAgB,CAAC,CAAC,IAAI,IAAI,EAAEF,GAAG,EAAE,CAAC,CAAC,CAAC;IAChG;IACA,IAAID,MAAM,IAAI,IAAI,CAACvB,YAAY,EAAE;MAC7B,MAAMwB,GAAG,GAAG,IAAI,CAAC1B,KAAK,CAACa,SAAS,CAAC,IAAI,CAACX,YAAY,EAAEuB,MAAM,CAAC;MAC3D,OAAO,IAAI,CAACE,2BAA2B,CAAC,IAAI,CAACxB,SAAS,EAAE0B,cAAc,CAAC,CAAC,IAAI,IAAI,EAAEH,GAAG,EAAE,CAAC,CAAC;IAC7F;IACA,MAAMI,IAAI,GAAG,IAAI,CAAC9B,KAAK,CAACa,SAAS,CAAC,IAAI,CAACZ,cAAc,EAAEwB,MAAM,CAAC;IAC9D,IAAIK,IAAI,CAACC,OAAO,CAACC,MAAM,CAACC,YAAY,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;MAChD,OAAO,IAAI,CAACN,2BAA2B,CAAC,IAAI,CAACxB,SAAS,EAAEyB,gBAAgB,CAAC,CAAC,IAAI,IAAI,EAAEE,IAAI,EAAE,CAAC,CAAC;IAChG;IACA,MAAMI,IAAI,GAAG,IAAI,CAAClC,KAAK,CAACa,SAAS,CAACY,MAAM,EAAE,IAAI,CAACvB,YAAY,CAAC;IAC5D,OAAO,IAAI,CAACyB,2BAA2B,CAAC,IAAI,CAACxB,SAAS,EAAE0B,cAAc,CAAC,CAAC,IAAI,IAAI,EAAEK,IAAI,EAAE,CAAC,CAAC,CAAC;EAC/F;EACAP,2BAA2BA,CAACQ,MAAM,EAAEC,SAAS,EAAEC,MAAM,EAAE;IACnD,IAAIC,WAAW,GAAG,CAAC;IACnB,IAAIC,iBAAiB,GAAG,CAAC,CAAC;IAC1B,OAAO,CAACA,iBAAiB,GAAGH,SAAS,CAACL,OAAO,CAAC,IAAI,EAAEQ,iBAAiB,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE;MAChFD,WAAW,EAAE;IACjB;IACA,OAAO,CAACH,MAAM,EAAEE,MAAM,GAAGD,SAAS,CAACpB,MAAM,EAAEsB,WAAW,CAAC;EAC3D;EACA,OAAOE,WAAWA,CAAChC,aAAa,EAAEiC,YAAY,EAAEC,iBAAiB,EAAE;IAC/D,IAAI,CAAClC,aAAa,EAAE;MAChB;MACA,OAAO;QACHmC,IAAI,EAAE,EAAE;QACRC,kBAAkB,EAAE,CAAC;QACrBC,kBAAkB,EAAE,CAAC;QACrBC,aAAa,EAAE;MACnB,CAAC;IACL;IACA,IAAInD,iBAAiB,EAAE;MACnByB,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;MAClDD,OAAO,CAACC,GAAG,CAAC,mBAAmBb,aAAa,CAACH,QAAQ,CAAC,CAAC,EAAE,CAAC;MAC1De,OAAO,CAACC,GAAG,CAAC,kBAAkBoB,YAAY,CAACpC,QAAQ,CAAC,CAAC,EAAE,CAAC;IAC5D;IACA,MAAM0C,YAAY,GAAGC,IAAI,CAACC,GAAG,CAACxD,OAAO,CAACyD,kBAAkB,CAAC1C,aAAa,CAACR,KAAK,EAAEyC,YAAY,CAACzC,KAAK,CAAC,EAAEQ,aAAa,CAACP,cAAc,EAAEwC,YAAY,CAACxC,cAAc,CAAC;IAC7J,MAAMkD,YAAY,GAAGH,IAAI,CAACC,GAAG,CAACxD,OAAO,CAAC2D,kBAAkB,CAAC5C,aAAa,CAACR,KAAK,EAAEyC,YAAY,CAACzC,KAAK,CAAC,EAAEQ,aAAa,CAACR,KAAK,CAACgB,MAAM,GAAGR,aAAa,CAACN,YAAY,EAAEuC,YAAY,CAACzC,KAAK,CAACgB,MAAM,GAAGyB,YAAY,CAACvC,YAAY,CAAC;IAClN,MAAMmD,aAAa,GAAG7C,aAAa,CAACR,KAAK,CAACa,SAAS,CAACkC,YAAY,EAAEvC,aAAa,CAACR,KAAK,CAACgB,MAAM,GAAGmC,YAAY,CAAC;IAC5G,MAAMG,YAAY,GAAGb,YAAY,CAACzC,KAAK,CAACa,SAAS,CAACkC,YAAY,EAAEN,YAAY,CAACzC,KAAK,CAACgB,MAAM,GAAGmC,YAAY,CAAC;IACzG,MAAMI,sBAAsB,GAAG/C,aAAa,CAACP,cAAc,GAAG8C,YAAY;IAC1E,MAAMS,oBAAoB,GAAGhD,aAAa,CAACN,YAAY,GAAG6C,YAAY;IACtE,MAAMU,qBAAqB,GAAGhB,YAAY,CAACxC,cAAc,GAAG8C,YAAY;IACxE,MAAMW,mBAAmB,GAAGjB,YAAY,CAACvC,YAAY,GAAG6C,YAAY;IACpE,IAAIpD,iBAAiB,EAAE;MACnByB,OAAO,CAACC,GAAG,CAAC,kCAAkCgC,aAAa,sBAAsBE,sBAAsB,mBAAmBC,oBAAoB,EAAE,CAAC;MACjJpC,OAAO,CAACC,GAAG,CAAC,iCAAiCiC,YAAY,sBAAsBG,qBAAqB,mBAAmBC,mBAAmB,EAAE,CAAC;IACjJ;IACA,IAAID,qBAAqB,KAAKC,mBAAmB,EAAE;MAC/C;MACA,MAAMC,yBAAyB,GAAInD,aAAa,CAACP,cAAc,GAAG8C,YAAa;MAC/E,IAAIpD,iBAAiB,EAAE;QACnByB,OAAO,CAACC,GAAG,CAAC,oBAAoBsC,yBAAyB,QAAQ,CAAC;MACtE;MACA,OAAO;QACHhB,IAAI,EAAEW,YAAY;QAClBV,kBAAkB,EAAEe,yBAAyB;QAC7Cd,kBAAkB,EAAE,CAAC;QACrBC,aAAa,EAAE;MACnB,CAAC;IACL;IACA;IACA,MAAMa,yBAAyB,GAAGH,oBAAoB,GAAGD,sBAAsB;IAC/E,OAAO;MACHZ,IAAI,EAAEW,YAAY;MAClBV,kBAAkB,EAAEe,yBAAyB;MAC7Cd,kBAAkB,EAAE,CAAC;MACrBC,aAAa,EAAE;IACnB,CAAC;EACL;EACA,OAAOc,6BAA6BA,CAACpD,aAAa,EAAEiC,YAAY,EAAE;IAC9D,IAAI,CAACjC,aAAa,EAAE;MAChB;MACA,OAAO;QACHmC,IAAI,EAAE,EAAE;QACRC,kBAAkB,EAAE,CAAC;QACrBC,kBAAkB,EAAE,CAAC;QACrBC,aAAa,EAAE;MACnB,CAAC;IACL;IACA,IAAInD,iBAAiB,EAAE;MACnByB,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;MACpED,OAAO,CAACC,GAAG,CAAC,mBAAmBb,aAAa,CAACH,QAAQ,CAAC,CAAC,EAAE,CAAC;MAC1De,OAAO,CAACC,GAAG,CAAC,kBAAkBoB,YAAY,CAACpC,QAAQ,CAAC,CAAC,EAAE,CAAC;IAC5D;IACA,IAAIG,aAAa,CAACR,KAAK,KAAKyC,YAAY,CAACzC,KAAK,EAAE;MAC5C,OAAO;QACH2C,IAAI,EAAE,EAAE;QACRC,kBAAkB,EAAE,CAAC;QACrBC,kBAAkB,EAAE,CAAC;QACrBC,aAAa,EAAEL,YAAY,CAACvC,YAAY,GAAGM,aAAa,CAACN;MAC7D,CAAC;IACL;IACA,MAAM6C,YAAY,GAAGC,IAAI,CAACC,GAAG,CAACxD,OAAO,CAACyD,kBAAkB,CAAC1C,aAAa,CAACR,KAAK,EAAEyC,YAAY,CAACzC,KAAK,CAAC,EAAEQ,aAAa,CAACN,YAAY,CAAC;IAC9H,MAAMiD,YAAY,GAAGH,IAAI,CAACC,GAAG,CAACxD,OAAO,CAAC2D,kBAAkB,CAAC5C,aAAa,CAACR,KAAK,EAAEyC,YAAY,CAACzC,KAAK,CAAC,EAAEQ,aAAa,CAACR,KAAK,CAACgB,MAAM,GAAGR,aAAa,CAACN,YAAY,CAAC;IAC3J,MAAMmD,aAAa,GAAG7C,aAAa,CAACR,KAAK,CAACa,SAAS,CAACkC,YAAY,EAAEvC,aAAa,CAACR,KAAK,CAACgB,MAAM,GAAGmC,YAAY,CAAC;IAC5G,MAAMG,YAAY,GAAGb,YAAY,CAACzC,KAAK,CAACa,SAAS,CAACkC,YAAY,EAAEN,YAAY,CAACzC,KAAK,CAACgB,MAAM,GAAGmC,YAAY,CAAC;IACzG,MAAMI,sBAAsB,GAAG/C,aAAa,CAACP,cAAc,GAAG8C,YAAY;IAC1E,MAAMS,oBAAoB,GAAGhD,aAAa,CAACN,YAAY,GAAG6C,YAAY;IACtE,MAAMU,qBAAqB,GAAGhB,YAAY,CAACxC,cAAc,GAAG8C,YAAY;IACxE,MAAMW,mBAAmB,GAAGjB,YAAY,CAACvC,YAAY,GAAG6C,YAAY;IACpE,IAAIpD,iBAAiB,EAAE;MACnByB,OAAO,CAACC,GAAG,CAAC,kCAAkCgC,aAAa,sBAAsBE,sBAAsB,mBAAmBC,oBAAoB,EAAE,CAAC;MACjJpC,OAAO,CAACC,GAAG,CAAC,iCAAiCiC,YAAY,sBAAsBG,qBAAqB,mBAAmBC,mBAAmB,EAAE,CAAC;IACjJ;IACA,OAAO;MACHf,IAAI,EAAEW,YAAY;MAClBV,kBAAkB,EAAEY,oBAAoB;MACxCX,kBAAkB,EAAEQ,aAAa,CAACrC,MAAM,GAAGwC,oBAAoB;MAC/DV,aAAa,EAAEY,mBAAmB,GAAGJ,YAAY,CAACtC;IACtD,CAAC;EACL;AACJ;AACA,OAAO,MAAM6C,yBAAyB,CAAC;EACnC,OAAOC,cAAcA,CAACC,UAAU,EAAEC,YAAY,EAAE;IAC5C,OAAOhB,IAAI,CAACiB,KAAK,CAAC,CAACF,UAAU,GAAG,CAAC,IAAIC,YAAY,CAAC;EACtD;EACA,OAAOE,gBAAgBA,CAACC,IAAI,EAAEH,YAAY,EAAE;IACxC,MAAMvC,MAAM,GAAG0C,IAAI,GAAGH,YAAY;IAClC,MAAMI,eAAe,GAAG3C,MAAM,GAAG,CAAC;IAClC,MAAM4C,aAAa,GAAG5C,MAAM,GAAGuC,YAAY;IAC3C,OAAO,IAAItE,KAAK,CAAC0E,eAAe,EAAE,CAAC,EAAEC,aAAa,GAAG,CAAC,EAAE,CAAC,CAAC;EAC9D;EACA,OAAOC,mBAAmBA,CAACC,KAAK,EAAEpE,SAAS,EAAE6D,YAAY,EAAEQ,YAAY,EAAE;IACrE;IACA;IACA,MAAMC,WAAW,GAAG,GAAG;IACvB,MAAMC,kBAAkB,GAAGb,yBAAyB,CAACC,cAAc,CAAC3D,SAAS,CAACiE,eAAe,EAAEJ,YAAY,CAAC;IAC5G,MAAMW,uBAAuB,GAAGd,yBAAyB,CAACK,gBAAgB,CAACQ,kBAAkB,EAAEV,YAAY,CAAC;IAC5G,MAAMY,gBAAgB,GAAGf,yBAAyB,CAACC,cAAc,CAAC3D,SAAS,CAACkE,aAAa,EAAEL,YAAY,CAAC;IACxG,MAAMa,qBAAqB,GAAGhB,yBAAyB,CAACK,gBAAgB,CAACU,gBAAgB,EAAEZ,YAAY,CAAC;IACxG,IAAIc,YAAY,GAAGH,uBAAuB,CAACI,eAAe,CAAC,IAAIrF,KAAK,CAAC,CAAC,EAAE,CAAC,EAAES,SAAS,CAACiE,eAAe,EAAEjE,SAAS,CAAC6E,WAAW,CAAC,CAAC;IAC7H,IAAIR,YAAY,IAAID,KAAK,CAACU,qBAAqB,CAACH,YAAY,EAAE,CAAC,CAAC,4BAA4B,CAAC,GAAGL,WAAW,EAAE;MACzG,MAAMS,YAAY,GAAGX,KAAK,CAACY,cAAc,CAACL,YAAY,CAACjD,cAAc,CAAC,CAAC,EAAE,CAAC4C,WAAW,CAAC;MACtFK,YAAY,GAAGpF,KAAK,CAAC0F,aAAa,CAACF,YAAY,EAAEJ,YAAY,CAACjD,cAAc,CAAC,CAAC,CAAC;IACnF;IACA,MAAMwD,OAAO,GAAGd,KAAK,CAACe,eAAe,CAACR,YAAY,EAAE,CAAC,CAAC,4BAA4B,CAAC;IACnF,MAAMS,QAAQ,GAAGhB,KAAK,CAACiB,YAAY,CAAC,CAAC;IACrC,MAAMC,iBAAiB,GAAGlB,KAAK,CAACmB,gBAAgB,CAACH,QAAQ,CAAC;IAC1D,IAAII,aAAa,GAAGd,qBAAqB,CAACE,eAAe,CAAC,IAAIrF,KAAK,CAACS,SAAS,CAACkE,aAAa,EAAElE,SAAS,CAACyF,SAAS,EAAEL,QAAQ,EAAEE,iBAAiB,CAAC,CAAC;IAC/I,IAAIjB,YAAY,IAAID,KAAK,CAACU,qBAAqB,CAACU,aAAa,EAAE,CAAC,CAAC,4BAA4B,CAAC,GAAGlB,WAAW,EAAE;MAC1G,MAAMoB,WAAW,GAAGtB,KAAK,CAACY,cAAc,CAACQ,aAAa,CAAC/D,gBAAgB,CAAC,CAAC,EAAE6C,WAAW,CAAC;MACvFkB,aAAa,GAAGjG,KAAK,CAAC0F,aAAa,CAACO,aAAa,CAAC/D,gBAAgB,CAAC,CAAC,EAAEiE,WAAW,CAAC;IACtF;IACA,MAAMC,QAAQ,GAAGvB,KAAK,CAACe,eAAe,CAACK,aAAa,EAAE,CAAC,CAAC,4BAA4B,CAAC;IACrF,IAAIhD,IAAI;IACR,IAAI+B,kBAAkB,KAAKE,gBAAgB,IAAIF,kBAAkB,GAAG,CAAC,KAAKE,gBAAgB,EAAE;MACxF;MACAjC,IAAI,GAAG4B,KAAK,CAACe,eAAe,CAACnF,SAAS,EAAE,CAAC,CAAC,4BAA4B,CAAC;IAC3E,CAAC,MACI;MACD,MAAM4F,eAAe,GAAGpB,uBAAuB,CAACI,eAAe,CAAC5E,SAAS,CAAC;MAC1E,MAAM6F,eAAe,GAAGnB,qBAAqB,CAACE,eAAe,CAAC5E,SAAS,CAAC;MACxEwC,IAAI,GAAI4B,KAAK,CAACe,eAAe,CAACS,eAAe,EAAE,CAAC,CAAC,4BAA4B,CAAC,GACxE/D,MAAM,CAACC,YAAY,CAAC,IAAI,CAAC,GACzBsC,KAAK,CAACe,eAAe,CAACU,eAAe,EAAE,CAAC,CAAC,4BAA4B,CAAE;IACjF;IACA,IAAIxB,YAAY,IAAI7B,IAAI,CAAC3B,MAAM,GAAG,CAAC,GAAGyD,WAAW,EAAE;MAC/C9B,IAAI,GAAGA,IAAI,CAAC9B,SAAS,CAAC,CAAC,EAAE4D,WAAW,CAAC,GAAGzC,MAAM,CAACC,YAAY,CAAC,IAAI,CAAC,GAAGU,IAAI,CAAC9B,SAAS,CAAC8B,IAAI,CAAC3B,MAAM,GAAGyD,WAAW,EAAE9B,IAAI,CAAC3B,MAAM,CAAC;IAC9H;IACA,OAAO,IAAIpB,aAAa,CAACyF,OAAO,GAAG1C,IAAI,GAAGmD,QAAQ,EAAET,OAAO,CAACrE,MAAM,EAAEqE,OAAO,CAACrE,MAAM,GAAG2B,IAAI,CAAC3B,MAAM,EAAEb,SAAS,EAAE2E,YAAY,CAACT,aAAa,GAAGS,YAAY,CAACV,eAAe,CAAC;EAC3K;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}