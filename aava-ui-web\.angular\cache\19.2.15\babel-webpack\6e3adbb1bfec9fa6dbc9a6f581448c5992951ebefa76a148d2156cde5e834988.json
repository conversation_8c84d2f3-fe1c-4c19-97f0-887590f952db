{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Emitter } from '../common/event.js';\nexport class DomEmitter {\n  get event() {\n    return this.emitter.event;\n  }\n  constructor(element, type, useCapture) {\n    const fn = e => this.emitter.fire(e);\n    this.emitter = new Emitter({\n      onWillAddFirstListener: () => element.addEventListener(type, fn, useCapture),\n      onDidRemoveLastListener: () => element.removeEventListener(type, fn, useCapture)\n    });\n  }\n  dispose() {\n    this.emitter.dispose();\n  }\n}", "map": {"version": 3, "names": ["Emitter", "DomEmitter", "event", "emitter", "constructor", "element", "type", "useCapture", "fn", "e", "fire", "onWillAddFirstListener", "addEventListener", "onDidRemoveLastListener", "removeEventListener", "dispose"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/browser/event.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Emitter } from '../common/event.js';\nexport class DomEmitter {\n    get event() {\n        return this.emitter.event;\n    }\n    constructor(element, type, useCapture) {\n        const fn = (e) => this.emitter.fire(e);\n        this.emitter = new Emitter({\n            onWillAddFirstListener: () => element.addEventListener(type, fn, useCapture),\n            onDidRemoveLastListener: () => element.removeEventListener(type, fn, useCapture)\n        });\n    }\n    dispose() {\n        this.emitter.dispose();\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,OAAO,QAAQ,oBAAoB;AAC5C,OAAO,MAAMC,UAAU,CAAC;EACpB,IAAIC,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACC,OAAO,CAACD,KAAK;EAC7B;EACAE,WAAWA,CAACC,OAAO,EAAEC,IAAI,EAAEC,UAAU,EAAE;IACnC,MAAMC,EAAE,GAAIC,CAAC,IAAK,IAAI,CAACN,OAAO,CAACO,IAAI,CAACD,CAAC,CAAC;IACtC,IAAI,CAACN,OAAO,GAAG,IAAIH,OAAO,CAAC;MACvBW,sBAAsB,EAAEA,CAAA,KAAMN,OAAO,CAACO,gBAAgB,CAACN,IAAI,EAAEE,EAAE,EAAED,UAAU,CAAC;MAC5EM,uBAAuB,EAAEA,CAAA,KAAMR,OAAO,CAACS,mBAAmB,CAACR,IAAI,EAAEE,EAAE,EAAED,UAAU;IACnF,CAAC,CAAC;EACN;EACAQ,OAAOA,CAAA,EAAG;IACN,IAAI,CAACZ,OAAO,CAACY,OAAO,CAAC,CAAC;EAC1B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}