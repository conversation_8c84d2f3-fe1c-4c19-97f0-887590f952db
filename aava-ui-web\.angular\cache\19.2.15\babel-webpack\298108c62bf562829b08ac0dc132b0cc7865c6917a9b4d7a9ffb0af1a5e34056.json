{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as strings from '../../../base/common/strings.js';\nexport class LineDecoration {\n  constructor(startColumn, endColumn, className, type) {\n    this.startColumn = startColumn;\n    this.endColumn = endColumn;\n    this.className = className;\n    this.type = type;\n    this._lineDecorationBrand = undefined;\n  }\n  static _equals(a, b) {\n    return a.startColumn === b.startColumn && a.endColumn === b.endColumn && a.className === b.className && a.type === b.type;\n  }\n  static equalsArr(a, b) {\n    const aLen = a.length;\n    const bLen = b.length;\n    if (aLen !== bLen) {\n      return false;\n    }\n    for (let i = 0; i < aLen; i++) {\n      if (!LineDecoration._equals(a[i], b[i])) {\n        return false;\n      }\n    }\n    return true;\n  }\n  static extractWrapped(arr, startOffset, endOffset) {\n    if (arr.length === 0) {\n      return arr;\n    }\n    const startColumn = startOffset + 1;\n    const endColumn = endOffset + 1;\n    const lineLength = endOffset - startOffset;\n    const r = [];\n    let rLength = 0;\n    for (const dec of arr) {\n      if (dec.endColumn <= startColumn || dec.startColumn >= endColumn) {\n        continue;\n      }\n      r[rLength++] = new LineDecoration(Math.max(1, dec.startColumn - startColumn + 1), Math.min(lineLength + 1, dec.endColumn - startColumn + 1), dec.className, dec.type);\n    }\n    return r;\n  }\n  static filter(lineDecorations, lineNumber, minLineColumn, maxLineColumn) {\n    if (lineDecorations.length === 0) {\n      return [];\n    }\n    const result = [];\n    let resultLen = 0;\n    for (let i = 0, len = lineDecorations.length; i < len; i++) {\n      const d = lineDecorations[i];\n      const range = d.range;\n      if (range.endLineNumber < lineNumber || range.startLineNumber > lineNumber) {\n        // Ignore decorations that sit outside this line\n        continue;\n      }\n      if (range.isEmpty() && (d.type === 0 /* InlineDecorationType.Regular */ || d.type === 3 /* InlineDecorationType.RegularAffectingLetterSpacing */)) {\n        // Ignore empty range decorations\n        continue;\n      }\n      const startColumn = range.startLineNumber === lineNumber ? range.startColumn : minLineColumn;\n      const endColumn = range.endLineNumber === lineNumber ? range.endColumn : maxLineColumn;\n      result[resultLen++] = new LineDecoration(startColumn, endColumn, d.inlineClassName, d.type);\n    }\n    return result;\n  }\n  static _typeCompare(a, b) {\n    const ORDER = [2, 0, 1, 3];\n    return ORDER[a] - ORDER[b];\n  }\n  static compare(a, b) {\n    if (a.startColumn !== b.startColumn) {\n      return a.startColumn - b.startColumn;\n    }\n    if (a.endColumn !== b.endColumn) {\n      return a.endColumn - b.endColumn;\n    }\n    const typeCmp = LineDecoration._typeCompare(a.type, b.type);\n    if (typeCmp !== 0) {\n      return typeCmp;\n    }\n    if (a.className !== b.className) {\n      return a.className < b.className ? -1 : 1;\n    }\n    return 0;\n  }\n}\nexport class DecorationSegment {\n  constructor(startOffset, endOffset, className, metadata) {\n    this.startOffset = startOffset;\n    this.endOffset = endOffset;\n    this.className = className;\n    this.metadata = metadata;\n  }\n}\nclass Stack {\n  constructor() {\n    this.stopOffsets = [];\n    this.classNames = [];\n    this.metadata = [];\n    this.count = 0;\n  }\n  static _metadata(metadata) {\n    let result = 0;\n    for (let i = 0, len = metadata.length; i < len; i++) {\n      result |= metadata[i];\n    }\n    return result;\n  }\n  consumeLowerThan(maxStopOffset, nextStartOffset, result) {\n    while (this.count > 0 && this.stopOffsets[0] < maxStopOffset) {\n      let i = 0;\n      // Take all equal stopping offsets\n      while (i + 1 < this.count && this.stopOffsets[i] === this.stopOffsets[i + 1]) {\n        i++;\n      }\n      // Basically we are consuming the first i + 1 elements of the stack\n      result.push(new DecorationSegment(nextStartOffset, this.stopOffsets[i], this.classNames.join(' '), Stack._metadata(this.metadata)));\n      nextStartOffset = this.stopOffsets[i] + 1;\n      // Consume them\n      this.stopOffsets.splice(0, i + 1);\n      this.classNames.splice(0, i + 1);\n      this.metadata.splice(0, i + 1);\n      this.count -= i + 1;\n    }\n    if (this.count > 0 && nextStartOffset < maxStopOffset) {\n      result.push(new DecorationSegment(nextStartOffset, maxStopOffset - 1, this.classNames.join(' '), Stack._metadata(this.metadata)));\n      nextStartOffset = maxStopOffset;\n    }\n    return nextStartOffset;\n  }\n  insert(stopOffset, className, metadata) {\n    if (this.count === 0 || this.stopOffsets[this.count - 1] <= stopOffset) {\n      // Insert at the end\n      this.stopOffsets.push(stopOffset);\n      this.classNames.push(className);\n      this.metadata.push(metadata);\n    } else {\n      // Find the insertion position for `stopOffset`\n      for (let i = 0; i < this.count; i++) {\n        if (this.stopOffsets[i] >= stopOffset) {\n          this.stopOffsets.splice(i, 0, stopOffset);\n          this.classNames.splice(i, 0, className);\n          this.metadata.splice(i, 0, metadata);\n          break;\n        }\n      }\n    }\n    this.count++;\n    return;\n  }\n}\nexport class LineDecorationsNormalizer {\n  /**\n   * Normalize line decorations. Overlapping decorations will generate multiple segments\n   */\n  static normalize(lineContent, lineDecorations) {\n    if (lineDecorations.length === 0) {\n      return [];\n    }\n    const result = [];\n    const stack = new Stack();\n    let nextStartOffset = 0;\n    for (let i = 0, len = lineDecorations.length; i < len; i++) {\n      const d = lineDecorations[i];\n      let startColumn = d.startColumn;\n      let endColumn = d.endColumn;\n      const className = d.className;\n      const metadata = d.type === 1 /* InlineDecorationType.Before */ ? 2 /* LinePartMetadata.PSEUDO_BEFORE */ : d.type === 2 /* InlineDecorationType.After */ ? 4 /* LinePartMetadata.PSEUDO_AFTER */ : 0;\n      // If the position would end up in the middle of a high-low surrogate pair, we move it to before the pair\n      if (startColumn > 1) {\n        const charCodeBefore = lineContent.charCodeAt(startColumn - 2);\n        if (strings.isHighSurrogate(charCodeBefore)) {\n          startColumn--;\n        }\n      }\n      if (endColumn > 1) {\n        const charCodeBefore = lineContent.charCodeAt(endColumn - 2);\n        if (strings.isHighSurrogate(charCodeBefore)) {\n          endColumn--;\n        }\n      }\n      const currentStartOffset = startColumn - 1;\n      const currentEndOffset = endColumn - 2;\n      nextStartOffset = stack.consumeLowerThan(currentStartOffset, nextStartOffset, result);\n      if (stack.count === 0) {\n        nextStartOffset = currentStartOffset;\n      }\n      stack.insert(currentEndOffset, className, metadata);\n    }\n    stack.consumeLowerThan(1073741824 /* Constants.MAX_SAFE_SMALL_INTEGER */, nextStartOffset, result);\n    return result;\n  }\n}", "map": {"version": 3, "names": ["strings", "LineDecoration", "constructor", "startColumn", "endColumn", "className", "type", "_lineDecorationBrand", "undefined", "_equals", "a", "b", "equalsArr", "aLen", "length", "bLen", "i", "extractWrapped", "arr", "startOffset", "endOffset", "lineLength", "r", "r<PERSON><PERSON><PERSON>", "dec", "Math", "max", "min", "filter", "lineDecorations", "lineNumber", "minLineColumn", "maxLineColumn", "result", "resultLen", "len", "d", "range", "endLineNumber", "startLineNumber", "isEmpty", "inlineClassName", "_typeCompare", "ORDER", "compare", "typeCmp", "DecorationSegment", "metadata", "<PERSON><PERSON>", "stopOffsets", "classNames", "count", "_metadata", "consumeLowerThan", "maxStopOffset", "nextStartOffset", "push", "join", "splice", "insert", "stopOffset", "LineDecorationsNormalizer", "normalize", "lineContent", "stack", "charCodeBefore", "charCodeAt", "isHighSurrogate", "currentStartOffset", "currentEndOffset"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/viewLayout/lineDecorations.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as strings from '../../../base/common/strings.js';\nexport class LineDecoration {\n    constructor(startColumn, endColumn, className, type) {\n        this.startColumn = startColumn;\n        this.endColumn = endColumn;\n        this.className = className;\n        this.type = type;\n        this._lineDecorationBrand = undefined;\n    }\n    static _equals(a, b) {\n        return (a.startColumn === b.startColumn\n            && a.endColumn === b.endColumn\n            && a.className === b.className\n            && a.type === b.type);\n    }\n    static equalsArr(a, b) {\n        const aLen = a.length;\n        const bLen = b.length;\n        if (aLen !== bLen) {\n            return false;\n        }\n        for (let i = 0; i < aLen; i++) {\n            if (!LineDecoration._equals(a[i], b[i])) {\n                return false;\n            }\n        }\n        return true;\n    }\n    static extractWrapped(arr, startOffset, endOffset) {\n        if (arr.length === 0) {\n            return arr;\n        }\n        const startColumn = startOffset + 1;\n        const endColumn = endOffset + 1;\n        const lineLength = endOffset - startOffset;\n        const r = [];\n        let rLength = 0;\n        for (const dec of arr) {\n            if (dec.endColumn <= startColumn || dec.startColumn >= endColumn) {\n                continue;\n            }\n            r[rLength++] = new LineDecoration(Math.max(1, dec.startColumn - startColumn + 1), Math.min(lineLength + 1, dec.endColumn - startColumn + 1), dec.className, dec.type);\n        }\n        return r;\n    }\n    static filter(lineDecorations, lineNumber, minLineColumn, maxLineColumn) {\n        if (lineDecorations.length === 0) {\n            return [];\n        }\n        const result = [];\n        let resultLen = 0;\n        for (let i = 0, len = lineDecorations.length; i < len; i++) {\n            const d = lineDecorations[i];\n            const range = d.range;\n            if (range.endLineNumber < lineNumber || range.startLineNumber > lineNumber) {\n                // Ignore decorations that sit outside this line\n                continue;\n            }\n            if (range.isEmpty() && (d.type === 0 /* InlineDecorationType.Regular */ || d.type === 3 /* InlineDecorationType.RegularAffectingLetterSpacing */)) {\n                // Ignore empty range decorations\n                continue;\n            }\n            const startColumn = (range.startLineNumber === lineNumber ? range.startColumn : minLineColumn);\n            const endColumn = (range.endLineNumber === lineNumber ? range.endColumn : maxLineColumn);\n            result[resultLen++] = new LineDecoration(startColumn, endColumn, d.inlineClassName, d.type);\n        }\n        return result;\n    }\n    static _typeCompare(a, b) {\n        const ORDER = [2, 0, 1, 3];\n        return ORDER[a] - ORDER[b];\n    }\n    static compare(a, b) {\n        if (a.startColumn !== b.startColumn) {\n            return a.startColumn - b.startColumn;\n        }\n        if (a.endColumn !== b.endColumn) {\n            return a.endColumn - b.endColumn;\n        }\n        const typeCmp = LineDecoration._typeCompare(a.type, b.type);\n        if (typeCmp !== 0) {\n            return typeCmp;\n        }\n        if (a.className !== b.className) {\n            return a.className < b.className ? -1 : 1;\n        }\n        return 0;\n    }\n}\nexport class DecorationSegment {\n    constructor(startOffset, endOffset, className, metadata) {\n        this.startOffset = startOffset;\n        this.endOffset = endOffset;\n        this.className = className;\n        this.metadata = metadata;\n    }\n}\nclass Stack {\n    constructor() {\n        this.stopOffsets = [];\n        this.classNames = [];\n        this.metadata = [];\n        this.count = 0;\n    }\n    static _metadata(metadata) {\n        let result = 0;\n        for (let i = 0, len = metadata.length; i < len; i++) {\n            result |= metadata[i];\n        }\n        return result;\n    }\n    consumeLowerThan(maxStopOffset, nextStartOffset, result) {\n        while (this.count > 0 && this.stopOffsets[0] < maxStopOffset) {\n            let i = 0;\n            // Take all equal stopping offsets\n            while (i + 1 < this.count && this.stopOffsets[i] === this.stopOffsets[i + 1]) {\n                i++;\n            }\n            // Basically we are consuming the first i + 1 elements of the stack\n            result.push(new DecorationSegment(nextStartOffset, this.stopOffsets[i], this.classNames.join(' '), Stack._metadata(this.metadata)));\n            nextStartOffset = this.stopOffsets[i] + 1;\n            // Consume them\n            this.stopOffsets.splice(0, i + 1);\n            this.classNames.splice(0, i + 1);\n            this.metadata.splice(0, i + 1);\n            this.count -= (i + 1);\n        }\n        if (this.count > 0 && nextStartOffset < maxStopOffset) {\n            result.push(new DecorationSegment(nextStartOffset, maxStopOffset - 1, this.classNames.join(' '), Stack._metadata(this.metadata)));\n            nextStartOffset = maxStopOffset;\n        }\n        return nextStartOffset;\n    }\n    insert(stopOffset, className, metadata) {\n        if (this.count === 0 || this.stopOffsets[this.count - 1] <= stopOffset) {\n            // Insert at the end\n            this.stopOffsets.push(stopOffset);\n            this.classNames.push(className);\n            this.metadata.push(metadata);\n        }\n        else {\n            // Find the insertion position for `stopOffset`\n            for (let i = 0; i < this.count; i++) {\n                if (this.stopOffsets[i] >= stopOffset) {\n                    this.stopOffsets.splice(i, 0, stopOffset);\n                    this.classNames.splice(i, 0, className);\n                    this.metadata.splice(i, 0, metadata);\n                    break;\n                }\n            }\n        }\n        this.count++;\n        return;\n    }\n}\nexport class LineDecorationsNormalizer {\n    /**\n     * Normalize line decorations. Overlapping decorations will generate multiple segments\n     */\n    static normalize(lineContent, lineDecorations) {\n        if (lineDecorations.length === 0) {\n            return [];\n        }\n        const result = [];\n        const stack = new Stack();\n        let nextStartOffset = 0;\n        for (let i = 0, len = lineDecorations.length; i < len; i++) {\n            const d = lineDecorations[i];\n            let startColumn = d.startColumn;\n            let endColumn = d.endColumn;\n            const className = d.className;\n            const metadata = (d.type === 1 /* InlineDecorationType.Before */\n                ? 2 /* LinePartMetadata.PSEUDO_BEFORE */\n                : d.type === 2 /* InlineDecorationType.After */\n                    ? 4 /* LinePartMetadata.PSEUDO_AFTER */\n                    : 0);\n            // If the position would end up in the middle of a high-low surrogate pair, we move it to before the pair\n            if (startColumn > 1) {\n                const charCodeBefore = lineContent.charCodeAt(startColumn - 2);\n                if (strings.isHighSurrogate(charCodeBefore)) {\n                    startColumn--;\n                }\n            }\n            if (endColumn > 1) {\n                const charCodeBefore = lineContent.charCodeAt(endColumn - 2);\n                if (strings.isHighSurrogate(charCodeBefore)) {\n                    endColumn--;\n                }\n            }\n            const currentStartOffset = startColumn - 1;\n            const currentEndOffset = endColumn - 2;\n            nextStartOffset = stack.consumeLowerThan(currentStartOffset, nextStartOffset, result);\n            if (stack.count === 0) {\n                nextStartOffset = currentStartOffset;\n            }\n            stack.insert(currentEndOffset, className, metadata);\n        }\n        stack.consumeLowerThan(1073741824 /* Constants.MAX_SAFE_SMALL_INTEGER */, nextStartOffset, result);\n        return result;\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,KAAKA,OAAO,MAAM,iCAAiC;AAC1D,OAAO,MAAMC,cAAc,CAAC;EACxBC,WAAWA,CAACC,WAAW,EAAEC,SAAS,EAAEC,SAAS,EAAEC,IAAI,EAAE;IACjD,IAAI,CAACH,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,oBAAoB,GAAGC,SAAS;EACzC;EACA,OAAOC,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;IACjB,OAAQD,CAAC,CAACP,WAAW,KAAKQ,CAAC,CAACR,WAAW,IAChCO,CAAC,CAACN,SAAS,KAAKO,CAAC,CAACP,SAAS,IAC3BM,CAAC,CAACL,SAAS,KAAKM,CAAC,CAACN,SAAS,IAC3BK,CAAC,CAACJ,IAAI,KAAKK,CAAC,CAACL,IAAI;EAC5B;EACA,OAAOM,SAASA,CAACF,CAAC,EAAEC,CAAC,EAAE;IACnB,MAAME,IAAI,GAAGH,CAAC,CAACI,MAAM;IACrB,MAAMC,IAAI,GAAGJ,CAAC,CAACG,MAAM;IACrB,IAAID,IAAI,KAAKE,IAAI,EAAE;MACf,OAAO,KAAK;IAChB;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,IAAI,EAAEG,CAAC,EAAE,EAAE;MAC3B,IAAI,CAACf,cAAc,CAACQ,OAAO,CAACC,CAAC,CAACM,CAAC,CAAC,EAAEL,CAAC,CAACK,CAAC,CAAC,CAAC,EAAE;QACrC,OAAO,KAAK;MAChB;IACJ;IACA,OAAO,IAAI;EACf;EACA,OAAOC,cAAcA,CAACC,GAAG,EAAEC,WAAW,EAAEC,SAAS,EAAE;IAC/C,IAAIF,GAAG,CAACJ,MAAM,KAAK,CAAC,EAAE;MAClB,OAAOI,GAAG;IACd;IACA,MAAMf,WAAW,GAAGgB,WAAW,GAAG,CAAC;IACnC,MAAMf,SAAS,GAAGgB,SAAS,GAAG,CAAC;IAC/B,MAAMC,UAAU,GAAGD,SAAS,GAAGD,WAAW;IAC1C,MAAMG,CAAC,GAAG,EAAE;IACZ,IAAIC,OAAO,GAAG,CAAC;IACf,KAAK,MAAMC,GAAG,IAAIN,GAAG,EAAE;MACnB,IAAIM,GAAG,CAACpB,SAAS,IAAID,WAAW,IAAIqB,GAAG,CAACrB,WAAW,IAAIC,SAAS,EAAE;QAC9D;MACJ;MACAkB,CAAC,CAACC,OAAO,EAAE,CAAC,GAAG,IAAItB,cAAc,CAACwB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEF,GAAG,CAACrB,WAAW,GAAGA,WAAW,GAAG,CAAC,CAAC,EAAEsB,IAAI,CAACE,GAAG,CAACN,UAAU,GAAG,CAAC,EAAEG,GAAG,CAACpB,SAAS,GAAGD,WAAW,GAAG,CAAC,CAAC,EAAEqB,GAAG,CAACnB,SAAS,EAAEmB,GAAG,CAAClB,IAAI,CAAC;IACzK;IACA,OAAOgB,CAAC;EACZ;EACA,OAAOM,MAAMA,CAACC,eAAe,EAAEC,UAAU,EAAEC,aAAa,EAAEC,aAAa,EAAE;IACrE,IAAIH,eAAe,CAACf,MAAM,KAAK,CAAC,EAAE;MAC9B,OAAO,EAAE;IACb;IACA,MAAMmB,MAAM,GAAG,EAAE;IACjB,IAAIC,SAAS,GAAG,CAAC;IACjB,KAAK,IAAIlB,CAAC,GAAG,CAAC,EAAEmB,GAAG,GAAGN,eAAe,CAACf,MAAM,EAAEE,CAAC,GAAGmB,GAAG,EAAEnB,CAAC,EAAE,EAAE;MACxD,MAAMoB,CAAC,GAAGP,eAAe,CAACb,CAAC,CAAC;MAC5B,MAAMqB,KAAK,GAAGD,CAAC,CAACC,KAAK;MACrB,IAAIA,KAAK,CAACC,aAAa,GAAGR,UAAU,IAAIO,KAAK,CAACE,eAAe,GAAGT,UAAU,EAAE;QACxE;QACA;MACJ;MACA,IAAIO,KAAK,CAACG,OAAO,CAAC,CAAC,KAAKJ,CAAC,CAAC9B,IAAI,KAAK,CAAC,CAAC,sCAAsC8B,CAAC,CAAC9B,IAAI,KAAK,CAAC,CAAC,yDAAyD,EAAE;QAC/I;QACA;MACJ;MACA,MAAMH,WAAW,GAAIkC,KAAK,CAACE,eAAe,KAAKT,UAAU,GAAGO,KAAK,CAAClC,WAAW,GAAG4B,aAAc;MAC9F,MAAM3B,SAAS,GAAIiC,KAAK,CAACC,aAAa,KAAKR,UAAU,GAAGO,KAAK,CAACjC,SAAS,GAAG4B,aAAc;MACxFC,MAAM,CAACC,SAAS,EAAE,CAAC,GAAG,IAAIjC,cAAc,CAACE,WAAW,EAAEC,SAAS,EAAEgC,CAAC,CAACK,eAAe,EAAEL,CAAC,CAAC9B,IAAI,CAAC;IAC/F;IACA,OAAO2B,MAAM;EACjB;EACA,OAAOS,YAAYA,CAAChC,CAAC,EAAEC,CAAC,EAAE;IACtB,MAAMgC,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC1B,OAAOA,KAAK,CAACjC,CAAC,CAAC,GAAGiC,KAAK,CAAChC,CAAC,CAAC;EAC9B;EACA,OAAOiC,OAAOA,CAAClC,CAAC,EAAEC,CAAC,EAAE;IACjB,IAAID,CAAC,CAACP,WAAW,KAAKQ,CAAC,CAACR,WAAW,EAAE;MACjC,OAAOO,CAAC,CAACP,WAAW,GAAGQ,CAAC,CAACR,WAAW;IACxC;IACA,IAAIO,CAAC,CAACN,SAAS,KAAKO,CAAC,CAACP,SAAS,EAAE;MAC7B,OAAOM,CAAC,CAACN,SAAS,GAAGO,CAAC,CAACP,SAAS;IACpC;IACA,MAAMyC,OAAO,GAAG5C,cAAc,CAACyC,YAAY,CAAChC,CAAC,CAACJ,IAAI,EAAEK,CAAC,CAACL,IAAI,CAAC;IAC3D,IAAIuC,OAAO,KAAK,CAAC,EAAE;MACf,OAAOA,OAAO;IAClB;IACA,IAAInC,CAAC,CAACL,SAAS,KAAKM,CAAC,CAACN,SAAS,EAAE;MAC7B,OAAOK,CAAC,CAACL,SAAS,GAAGM,CAAC,CAACN,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC;IAC7C;IACA,OAAO,CAAC;EACZ;AACJ;AACA,OAAO,MAAMyC,iBAAiB,CAAC;EAC3B5C,WAAWA,CAACiB,WAAW,EAAEC,SAAS,EAAEf,SAAS,EAAE0C,QAAQ,EAAE;IACrD,IAAI,CAAC5B,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACf,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAAC0C,QAAQ,GAAGA,QAAQ;EAC5B;AACJ;AACA,MAAMC,KAAK,CAAC;EACR9C,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC+C,WAAW,GAAG,EAAE;IACrB,IAAI,CAACC,UAAU,GAAG,EAAE;IACpB,IAAI,CAACH,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACI,KAAK,GAAG,CAAC;EAClB;EACA,OAAOC,SAASA,CAACL,QAAQ,EAAE;IACvB,IAAId,MAAM,GAAG,CAAC;IACd,KAAK,IAAIjB,CAAC,GAAG,CAAC,EAAEmB,GAAG,GAAGY,QAAQ,CAACjC,MAAM,EAAEE,CAAC,GAAGmB,GAAG,EAAEnB,CAAC,EAAE,EAAE;MACjDiB,MAAM,IAAIc,QAAQ,CAAC/B,CAAC,CAAC;IACzB;IACA,OAAOiB,MAAM;EACjB;EACAoB,gBAAgBA,CAACC,aAAa,EAAEC,eAAe,EAAEtB,MAAM,EAAE;IACrD,OAAO,IAAI,CAACkB,KAAK,GAAG,CAAC,IAAI,IAAI,CAACF,WAAW,CAAC,CAAC,CAAC,GAAGK,aAAa,EAAE;MAC1D,IAAItC,CAAC,GAAG,CAAC;MACT;MACA,OAAOA,CAAC,GAAG,CAAC,GAAG,IAAI,CAACmC,KAAK,IAAI,IAAI,CAACF,WAAW,CAACjC,CAAC,CAAC,KAAK,IAAI,CAACiC,WAAW,CAACjC,CAAC,GAAG,CAAC,CAAC,EAAE;QAC1EA,CAAC,EAAE;MACP;MACA;MACAiB,MAAM,CAACuB,IAAI,CAAC,IAAIV,iBAAiB,CAACS,eAAe,EAAE,IAAI,CAACN,WAAW,CAACjC,CAAC,CAAC,EAAE,IAAI,CAACkC,UAAU,CAACO,IAAI,CAAC,GAAG,CAAC,EAAET,KAAK,CAACI,SAAS,CAAC,IAAI,CAACL,QAAQ,CAAC,CAAC,CAAC;MACnIQ,eAAe,GAAG,IAAI,CAACN,WAAW,CAACjC,CAAC,CAAC,GAAG,CAAC;MACzC;MACA,IAAI,CAACiC,WAAW,CAACS,MAAM,CAAC,CAAC,EAAE1C,CAAC,GAAG,CAAC,CAAC;MACjC,IAAI,CAACkC,UAAU,CAACQ,MAAM,CAAC,CAAC,EAAE1C,CAAC,GAAG,CAAC,CAAC;MAChC,IAAI,CAAC+B,QAAQ,CAACW,MAAM,CAAC,CAAC,EAAE1C,CAAC,GAAG,CAAC,CAAC;MAC9B,IAAI,CAACmC,KAAK,IAAKnC,CAAC,GAAG,CAAE;IACzB;IACA,IAAI,IAAI,CAACmC,KAAK,GAAG,CAAC,IAAII,eAAe,GAAGD,aAAa,EAAE;MACnDrB,MAAM,CAACuB,IAAI,CAAC,IAAIV,iBAAiB,CAACS,eAAe,EAAED,aAAa,GAAG,CAAC,EAAE,IAAI,CAACJ,UAAU,CAACO,IAAI,CAAC,GAAG,CAAC,EAAET,KAAK,CAACI,SAAS,CAAC,IAAI,CAACL,QAAQ,CAAC,CAAC,CAAC;MACjIQ,eAAe,GAAGD,aAAa;IACnC;IACA,OAAOC,eAAe;EAC1B;EACAI,MAAMA,CAACC,UAAU,EAAEvD,SAAS,EAAE0C,QAAQ,EAAE;IACpC,IAAI,IAAI,CAACI,KAAK,KAAK,CAAC,IAAI,IAAI,CAACF,WAAW,CAAC,IAAI,CAACE,KAAK,GAAG,CAAC,CAAC,IAAIS,UAAU,EAAE;MACpE;MACA,IAAI,CAACX,WAAW,CAACO,IAAI,CAACI,UAAU,CAAC;MACjC,IAAI,CAACV,UAAU,CAACM,IAAI,CAACnD,SAAS,CAAC;MAC/B,IAAI,CAAC0C,QAAQ,CAACS,IAAI,CAACT,QAAQ,CAAC;IAChC,CAAC,MACI;MACD;MACA,KAAK,IAAI/B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACmC,KAAK,EAAEnC,CAAC,EAAE,EAAE;QACjC,IAAI,IAAI,CAACiC,WAAW,CAACjC,CAAC,CAAC,IAAI4C,UAAU,EAAE;UACnC,IAAI,CAACX,WAAW,CAACS,MAAM,CAAC1C,CAAC,EAAE,CAAC,EAAE4C,UAAU,CAAC;UACzC,IAAI,CAACV,UAAU,CAACQ,MAAM,CAAC1C,CAAC,EAAE,CAAC,EAAEX,SAAS,CAAC;UACvC,IAAI,CAAC0C,QAAQ,CAACW,MAAM,CAAC1C,CAAC,EAAE,CAAC,EAAE+B,QAAQ,CAAC;UACpC;QACJ;MACJ;IACJ;IACA,IAAI,CAACI,KAAK,EAAE;IACZ;EACJ;AACJ;AACA,OAAO,MAAMU,yBAAyB,CAAC;EACnC;AACJ;AACA;EACI,OAAOC,SAASA,CAACC,WAAW,EAAElC,eAAe,EAAE;IAC3C,IAAIA,eAAe,CAACf,MAAM,KAAK,CAAC,EAAE;MAC9B,OAAO,EAAE;IACb;IACA,MAAMmB,MAAM,GAAG,EAAE;IACjB,MAAM+B,KAAK,GAAG,IAAIhB,KAAK,CAAC,CAAC;IACzB,IAAIO,eAAe,GAAG,CAAC;IACvB,KAAK,IAAIvC,CAAC,GAAG,CAAC,EAAEmB,GAAG,GAAGN,eAAe,CAACf,MAAM,EAAEE,CAAC,GAAGmB,GAAG,EAAEnB,CAAC,EAAE,EAAE;MACxD,MAAMoB,CAAC,GAAGP,eAAe,CAACb,CAAC,CAAC;MAC5B,IAAIb,WAAW,GAAGiC,CAAC,CAACjC,WAAW;MAC/B,IAAIC,SAAS,GAAGgC,CAAC,CAAChC,SAAS;MAC3B,MAAMC,SAAS,GAAG+B,CAAC,CAAC/B,SAAS;MAC7B,MAAM0C,QAAQ,GAAIX,CAAC,CAAC9B,IAAI,KAAK,CAAC,CAAC,oCACzB,CAAC,CAAC,uCACF8B,CAAC,CAAC9B,IAAI,KAAK,CAAC,CAAC,mCACT,CAAC,CAAC,sCACF,CAAE;MACZ;MACA,IAAIH,WAAW,GAAG,CAAC,EAAE;QACjB,MAAM8D,cAAc,GAAGF,WAAW,CAACG,UAAU,CAAC/D,WAAW,GAAG,CAAC,CAAC;QAC9D,IAAIH,OAAO,CAACmE,eAAe,CAACF,cAAc,CAAC,EAAE;UACzC9D,WAAW,EAAE;QACjB;MACJ;MACA,IAAIC,SAAS,GAAG,CAAC,EAAE;QACf,MAAM6D,cAAc,GAAGF,WAAW,CAACG,UAAU,CAAC9D,SAAS,GAAG,CAAC,CAAC;QAC5D,IAAIJ,OAAO,CAACmE,eAAe,CAACF,cAAc,CAAC,EAAE;UACzC7D,SAAS,EAAE;QACf;MACJ;MACA,MAAMgE,kBAAkB,GAAGjE,WAAW,GAAG,CAAC;MAC1C,MAAMkE,gBAAgB,GAAGjE,SAAS,GAAG,CAAC;MACtCmD,eAAe,GAAGS,KAAK,CAACX,gBAAgB,CAACe,kBAAkB,EAAEb,eAAe,EAAEtB,MAAM,CAAC;MACrF,IAAI+B,KAAK,CAACb,KAAK,KAAK,CAAC,EAAE;QACnBI,eAAe,GAAGa,kBAAkB;MACxC;MACAJ,KAAK,CAACL,MAAM,CAACU,gBAAgB,EAAEhE,SAAS,EAAE0C,QAAQ,CAAC;IACvD;IACAiB,KAAK,CAACX,gBAAgB,CAAC,UAAU,CAAC,wCAAwCE,eAAe,EAAEtB,MAAM,CAAC;IAClG,OAAOA,MAAM;EACjB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}