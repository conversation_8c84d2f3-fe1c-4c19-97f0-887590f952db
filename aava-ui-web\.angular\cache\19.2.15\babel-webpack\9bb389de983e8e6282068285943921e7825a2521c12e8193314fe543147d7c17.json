{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { escapeRegExpCharacters } from '../../../../../base/common/strings.js';\nimport { BracketAstNode } from './ast.js';\nimport { toLength } from './length.js';\nimport { identityKeyProvider, SmallImmutableSet } from './smallImmutableSet.js';\nimport { Token } from './tokenizer.js';\nexport class BracketTokens {\n  static createFromLanguage(configuration, denseKeyProvider) {\n    function getId(bracketInfo) {\n      return denseKeyProvider.getKey(`${bracketInfo.languageId}:::${bracketInfo.bracketText}`);\n    }\n    const map = new Map();\n    for (const openingBracket of configuration.bracketsNew.openingBrackets) {\n      const length = toLength(0, openingBracket.bracketText.length);\n      const openingTextId = getId(openingBracket);\n      const bracketIds = SmallImmutableSet.getEmpty().add(openingTextId, identityKeyProvider);\n      map.set(openingBracket.bracketText, new Token(length, 1 /* TokenKind.OpeningBracket */, openingTextId, bracketIds, BracketAstNode.create(length, openingBracket, bracketIds)));\n    }\n    for (const closingBracket of configuration.bracketsNew.closingBrackets) {\n      const length = toLength(0, closingBracket.bracketText.length);\n      let bracketIds = SmallImmutableSet.getEmpty();\n      const closingBrackets = closingBracket.getOpeningBrackets();\n      for (const bracket of closingBrackets) {\n        bracketIds = bracketIds.add(getId(bracket), identityKeyProvider);\n      }\n      map.set(closingBracket.bracketText, new Token(length, 2 /* TokenKind.ClosingBracket */, getId(closingBrackets[0]), bracketIds, BracketAstNode.create(length, closingBracket, bracketIds)));\n    }\n    return new BracketTokens(map);\n  }\n  constructor(map) {\n    this.map = map;\n    this.hasRegExp = false;\n    this._regExpGlobal = null;\n  }\n  getRegExpStr() {\n    if (this.isEmpty) {\n      return null;\n    } else {\n      const keys = [...this.map.keys()];\n      keys.sort();\n      keys.reverse();\n      return keys.map(k => prepareBracketForRegExp(k)).join('|');\n    }\n  }\n  /**\n   * Returns null if there is no such regexp (because there are no brackets).\n  */\n  get regExpGlobal() {\n    if (!this.hasRegExp) {\n      const regExpStr = this.getRegExpStr();\n      this._regExpGlobal = regExpStr ? new RegExp(regExpStr, 'gi') : null;\n      this.hasRegExp = true;\n    }\n    return this._regExpGlobal;\n  }\n  getToken(value) {\n    return this.map.get(value.toLowerCase());\n  }\n  findClosingTokenText(openingBracketIds) {\n    for (const [closingText, info] of this.map) {\n      if (info.kind === 2 /* TokenKind.ClosingBracket */ && info.bracketIds.intersects(openingBracketIds)) {\n        return closingText;\n      }\n    }\n    return undefined;\n  }\n  get isEmpty() {\n    return this.map.size === 0;\n  }\n}\nfunction prepareBracketForRegExp(str) {\n  let escaped = escapeRegExpCharacters(str);\n  // These bracket pair delimiters start or end with letters\n  // see https://github.com/microsoft/vscode/issues/132162 https://github.com/microsoft/vscode/issues/150440\n  if (/^[\\w ]+/.test(str)) {\n    escaped = `\\\\b${escaped}`;\n  }\n  if (/[\\w ]+$/.test(str)) {\n    escaped = `${escaped}\\\\b`;\n  }\n  return escaped;\n}\nexport class LanguageAgnosticBracketTokens {\n  constructor(denseKeyProvider, getLanguageConfiguration) {\n    this.denseKeyProvider = denseKeyProvider;\n    this.getLanguageConfiguration = getLanguageConfiguration;\n    this.languageIdToBracketTokens = new Map();\n  }\n  didLanguageChange(languageId) {\n    // Report a change whenever the language configuration updates.\n    return this.languageIdToBracketTokens.has(languageId);\n  }\n  getSingleLanguageBracketTokens(languageId) {\n    let singleLanguageBracketTokens = this.languageIdToBracketTokens.get(languageId);\n    if (!singleLanguageBracketTokens) {\n      singleLanguageBracketTokens = BracketTokens.createFromLanguage(this.getLanguageConfiguration(languageId), this.denseKeyProvider);\n      this.languageIdToBracketTokens.set(languageId, singleLanguageBracketTokens);\n    }\n    return singleLanguageBracketTokens;\n  }\n}", "map": {"version": 3, "names": ["escapeRegExpCharacters", "BracketAstNode", "to<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SmallImmutableSet", "Token", "BracketTokens", "createFromLanguage", "configuration", "denseKeyProvider", "getId", "bracketInfo", "<PERSON><PERSON><PERSON>", "languageId", "bracketText", "map", "Map", "openingBracket", "bracketsNew", "openingBrackets", "length", "openingTextId", "bracketIds", "getEmpty", "add", "set", "create", "closingBracket", "closingBrackets", "getOpeningBrackets", "bracket", "constructor", "hasRegExp", "_regExpGlobal", "getRegExpStr", "isEmpty", "keys", "sort", "reverse", "k", "prepareBracketForRegExp", "join", "regExpGlobal", "regExpStr", "RegExp", "getToken", "value", "get", "toLowerCase", "findClosingTokenText", "openingBracketIds", "closingText", "info", "kind", "intersects", "undefined", "size", "str", "escaped", "test", "LanguageAgnosticBracketTokens", "getLanguageConfiguration", "languageIdToBracketTokens", "didLanguageChange", "has", "getSingleLanguageBracketTokens", "singleLanguageBracketTokens"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/brackets.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { escapeRegExpCharacters } from '../../../../../base/common/strings.js';\nimport { BracketAstNode } from './ast.js';\nimport { toLength } from './length.js';\nimport { identityKeyProvider, SmallImmutableSet } from './smallImmutableSet.js';\nimport { Token } from './tokenizer.js';\nexport class BracketTokens {\n    static createFromLanguage(configuration, denseKeyProvider) {\n        function getId(bracketInfo) {\n            return denseKeyProvider.getKey(`${bracketInfo.languageId}:::${bracketInfo.bracketText}`);\n        }\n        const map = new Map();\n        for (const openingBracket of configuration.bracketsNew.openingBrackets) {\n            const length = toLength(0, openingBracket.bracketText.length);\n            const openingTextId = getId(openingBracket);\n            const bracketIds = SmallImmutableSet.getEmpty().add(openingTextId, identityKeyProvider);\n            map.set(openingBracket.bracketText, new Token(length, 1 /* TokenKind.OpeningBracket */, openingTextId, bracketIds, BracketAstNode.create(length, openingBracket, bracketIds)));\n        }\n        for (const closingBracket of configuration.bracketsNew.closingBrackets) {\n            const length = toLength(0, closingBracket.bracketText.length);\n            let bracketIds = SmallImmutableSet.getEmpty();\n            const closingBrackets = closingBracket.getOpeningBrackets();\n            for (const bracket of closingBrackets) {\n                bracketIds = bracketIds.add(getId(bracket), identityKeyProvider);\n            }\n            map.set(closingBracket.bracketText, new Token(length, 2 /* TokenKind.ClosingBracket */, getId(closingBrackets[0]), bracketIds, BracketAstNode.create(length, closingBracket, bracketIds)));\n        }\n        return new BracketTokens(map);\n    }\n    constructor(map) {\n        this.map = map;\n        this.hasRegExp = false;\n        this._regExpGlobal = null;\n    }\n    getRegExpStr() {\n        if (this.isEmpty) {\n            return null;\n        }\n        else {\n            const keys = [...this.map.keys()];\n            keys.sort();\n            keys.reverse();\n            return keys.map(k => prepareBracketForRegExp(k)).join('|');\n        }\n    }\n    /**\n     * Returns null if there is no such regexp (because there are no brackets).\n    */\n    get regExpGlobal() {\n        if (!this.hasRegExp) {\n            const regExpStr = this.getRegExpStr();\n            this._regExpGlobal = regExpStr ? new RegExp(regExpStr, 'gi') : null;\n            this.hasRegExp = true;\n        }\n        return this._regExpGlobal;\n    }\n    getToken(value) {\n        return this.map.get(value.toLowerCase());\n    }\n    findClosingTokenText(openingBracketIds) {\n        for (const [closingText, info] of this.map) {\n            if (info.kind === 2 /* TokenKind.ClosingBracket */ && info.bracketIds.intersects(openingBracketIds)) {\n                return closingText;\n            }\n        }\n        return undefined;\n    }\n    get isEmpty() {\n        return this.map.size === 0;\n    }\n}\nfunction prepareBracketForRegExp(str) {\n    let escaped = escapeRegExpCharacters(str);\n    // These bracket pair delimiters start or end with letters\n    // see https://github.com/microsoft/vscode/issues/132162 https://github.com/microsoft/vscode/issues/150440\n    if (/^[\\w ]+/.test(str)) {\n        escaped = `\\\\b${escaped}`;\n    }\n    if (/[\\w ]+$/.test(str)) {\n        escaped = `${escaped}\\\\b`;\n    }\n    return escaped;\n}\nexport class LanguageAgnosticBracketTokens {\n    constructor(denseKeyProvider, getLanguageConfiguration) {\n        this.denseKeyProvider = denseKeyProvider;\n        this.getLanguageConfiguration = getLanguageConfiguration;\n        this.languageIdToBracketTokens = new Map();\n    }\n    didLanguageChange(languageId) {\n        // Report a change whenever the language configuration updates.\n        return this.languageIdToBracketTokens.has(languageId);\n    }\n    getSingleLanguageBracketTokens(languageId) {\n        let singleLanguageBracketTokens = this.languageIdToBracketTokens.get(languageId);\n        if (!singleLanguageBracketTokens) {\n            singleLanguageBracketTokens = BracketTokens.createFromLanguage(this.getLanguageConfiguration(languageId), this.denseKeyProvider);\n            this.languageIdToBracketTokens.set(languageId, singleLanguageBracketTokens);\n        }\n        return singleLanguageBracketTokens;\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,sBAAsB,QAAQ,uCAAuC;AAC9E,SAASC,cAAc,QAAQ,UAAU;AACzC,SAASC,QAAQ,QAAQ,aAAa;AACtC,SAASC,mBAAmB,EAAEC,iBAAiB,QAAQ,wBAAwB;AAC/E,SAASC,KAAK,QAAQ,gBAAgB;AACtC,OAAO,MAAMC,aAAa,CAAC;EACvB,OAAOC,kBAAkBA,CAACC,aAAa,EAAEC,gBAAgB,EAAE;IACvD,SAASC,KAAKA,CAACC,WAAW,EAAE;MACxB,OAAOF,gBAAgB,CAACG,MAAM,CAAC,GAAGD,WAAW,CAACE,UAAU,MAAMF,WAAW,CAACG,WAAW,EAAE,CAAC;IAC5F;IACA,MAAMC,GAAG,GAAG,IAAIC,GAAG,CAAC,CAAC;IACrB,KAAK,MAAMC,cAAc,IAAIT,aAAa,CAACU,WAAW,CAACC,eAAe,EAAE;MACpE,MAAMC,MAAM,GAAGlB,QAAQ,CAAC,CAAC,EAAEe,cAAc,CAACH,WAAW,CAACM,MAAM,CAAC;MAC7D,MAAMC,aAAa,GAAGX,KAAK,CAACO,cAAc,CAAC;MAC3C,MAAMK,UAAU,GAAGlB,iBAAiB,CAACmB,QAAQ,CAAC,CAAC,CAACC,GAAG,CAACH,aAAa,EAAElB,mBAAmB,CAAC;MACvFY,GAAG,CAACU,GAAG,CAACR,cAAc,CAACH,WAAW,EAAE,IAAIT,KAAK,CAACe,MAAM,EAAE,CAAC,CAAC,gCAAgCC,aAAa,EAAEC,UAAU,EAAErB,cAAc,CAACyB,MAAM,CAACN,MAAM,EAAEH,cAAc,EAAEK,UAAU,CAAC,CAAC,CAAC;IAClL;IACA,KAAK,MAAMK,cAAc,IAAInB,aAAa,CAACU,WAAW,CAACU,eAAe,EAAE;MACpE,MAAMR,MAAM,GAAGlB,QAAQ,CAAC,CAAC,EAAEyB,cAAc,CAACb,WAAW,CAACM,MAAM,CAAC;MAC7D,IAAIE,UAAU,GAAGlB,iBAAiB,CAACmB,QAAQ,CAAC,CAAC;MAC7C,MAAMK,eAAe,GAAGD,cAAc,CAACE,kBAAkB,CAAC,CAAC;MAC3D,KAAK,MAAMC,OAAO,IAAIF,eAAe,EAAE;QACnCN,UAAU,GAAGA,UAAU,CAACE,GAAG,CAACd,KAAK,CAACoB,OAAO,CAAC,EAAE3B,mBAAmB,CAAC;MACpE;MACAY,GAAG,CAACU,GAAG,CAACE,cAAc,CAACb,WAAW,EAAE,IAAIT,KAAK,CAACe,MAAM,EAAE,CAAC,CAAC,gCAAgCV,KAAK,CAACkB,eAAe,CAAC,CAAC,CAAC,CAAC,EAAEN,UAAU,EAAErB,cAAc,CAACyB,MAAM,CAACN,MAAM,EAAEO,cAAc,EAAEL,UAAU,CAAC,CAAC,CAAC;IAC9L;IACA,OAAO,IAAIhB,aAAa,CAACS,GAAG,CAAC;EACjC;EACAgB,WAAWA,CAAChB,GAAG,EAAE;IACb,IAAI,CAACA,GAAG,GAAGA,GAAG;IACd,IAAI,CAACiB,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,aAAa,GAAG,IAAI;EAC7B;EACAC,YAAYA,CAAA,EAAG;IACX,IAAI,IAAI,CAACC,OAAO,EAAE;MACd,OAAO,IAAI;IACf,CAAC,MACI;MACD,MAAMC,IAAI,GAAG,CAAC,GAAG,IAAI,CAACrB,GAAG,CAACqB,IAAI,CAAC,CAAC,CAAC;MACjCA,IAAI,CAACC,IAAI,CAAC,CAAC;MACXD,IAAI,CAACE,OAAO,CAAC,CAAC;MACd,OAAOF,IAAI,CAACrB,GAAG,CAACwB,CAAC,IAAIC,uBAAuB,CAACD,CAAC,CAAC,CAAC,CAACE,IAAI,CAAC,GAAG,CAAC;IAC9D;EACJ;EACA;AACJ;AACA;EACI,IAAIC,YAAYA,CAAA,EAAG;IACf,IAAI,CAAC,IAAI,CAACV,SAAS,EAAE;MACjB,MAAMW,SAAS,GAAG,IAAI,CAACT,YAAY,CAAC,CAAC;MACrC,IAAI,CAACD,aAAa,GAAGU,SAAS,GAAG,IAAIC,MAAM,CAACD,SAAS,EAAE,IAAI,CAAC,GAAG,IAAI;MACnE,IAAI,CAACX,SAAS,GAAG,IAAI;IACzB;IACA,OAAO,IAAI,CAACC,aAAa;EAC7B;EACAY,QAAQA,CAACC,KAAK,EAAE;IACZ,OAAO,IAAI,CAAC/B,GAAG,CAACgC,GAAG,CAACD,KAAK,CAACE,WAAW,CAAC,CAAC,CAAC;EAC5C;EACAC,oBAAoBA,CAACC,iBAAiB,EAAE;IACpC,KAAK,MAAM,CAACC,WAAW,EAAEC,IAAI,CAAC,IAAI,IAAI,CAACrC,GAAG,EAAE;MACxC,IAAIqC,IAAI,CAACC,IAAI,KAAK,CAAC,CAAC,kCAAkCD,IAAI,CAAC9B,UAAU,CAACgC,UAAU,CAACJ,iBAAiB,CAAC,EAAE;QACjG,OAAOC,WAAW;MACtB;IACJ;IACA,OAAOI,SAAS;EACpB;EACA,IAAIpB,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACpB,GAAG,CAACyC,IAAI,KAAK,CAAC;EAC9B;AACJ;AACA,SAAShB,uBAAuBA,CAACiB,GAAG,EAAE;EAClC,IAAIC,OAAO,GAAG1D,sBAAsB,CAACyD,GAAG,CAAC;EACzC;EACA;EACA,IAAI,SAAS,CAACE,IAAI,CAACF,GAAG,CAAC,EAAE;IACrBC,OAAO,GAAG,MAAMA,OAAO,EAAE;EAC7B;EACA,IAAI,SAAS,CAACC,IAAI,CAACF,GAAG,CAAC,EAAE;IACrBC,OAAO,GAAG,GAAGA,OAAO,KAAK;EAC7B;EACA,OAAOA,OAAO;AAClB;AACA,OAAO,MAAME,6BAA6B,CAAC;EACvC7B,WAAWA,CAACtB,gBAAgB,EAAEoD,wBAAwB,EAAE;IACpD,IAAI,CAACpD,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACoD,wBAAwB,GAAGA,wBAAwB;IACxD,IAAI,CAACC,yBAAyB,GAAG,IAAI9C,GAAG,CAAC,CAAC;EAC9C;EACA+C,iBAAiBA,CAAClD,UAAU,EAAE;IAC1B;IACA,OAAO,IAAI,CAACiD,yBAAyB,CAACE,GAAG,CAACnD,UAAU,CAAC;EACzD;EACAoD,8BAA8BA,CAACpD,UAAU,EAAE;IACvC,IAAIqD,2BAA2B,GAAG,IAAI,CAACJ,yBAAyB,CAACf,GAAG,CAAClC,UAAU,CAAC;IAChF,IAAI,CAACqD,2BAA2B,EAAE;MAC9BA,2BAA2B,GAAG5D,aAAa,CAACC,kBAAkB,CAAC,IAAI,CAACsD,wBAAwB,CAAChD,UAAU,CAAC,EAAE,IAAI,CAACJ,gBAAgB,CAAC;MAChI,IAAI,CAACqD,yBAAyB,CAACrC,GAAG,CAACZ,UAAU,EAAEqD,2BAA2B,CAAC;IAC/E;IACA,OAAOA,2BAA2B;EACtC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}