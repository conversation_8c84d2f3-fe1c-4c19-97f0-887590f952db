{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport * as DomUtils from './dom.js';\nimport { mainWindow } from './window.js';\nimport * as arrays from '../common/arrays.js';\nimport { memoize } from '../common/decorators.js';\nimport { Event as EventUtils } from '../common/event.js';\nimport { Disposable, markAsSingleton, toDisposable } from '../common/lifecycle.js';\nimport { LinkedList } from '../common/linkedList.js';\nexport var EventType = /*#__PURE__*/function (EventType) {\n  EventType.Tap = '-monaco-gesturetap';\n  EventType.Change = '-monaco-gesturechange';\n  EventType.Start = '-monaco-gesturestart';\n  EventType.End = '-monaco-gesturesend';\n  EventType.Contextmenu = '-monaco-gesturecontextmenu';\n  return EventType;\n}(EventType || {});\nexport class Gesture extends Disposable {\n  static {\n    this.SCROLL_FRICTION = -0.005;\n  }\n  static {\n    this.HOLD_DELAY = 700;\n  }\n  static {\n    this.CLEAR_TAP_COUNT_TIME = 400;\n  } // ms\n  constructor() {\n    super();\n    this.dispatched = false;\n    this.targets = new LinkedList();\n    this.ignoreTargets = new LinkedList();\n    this.activeTouches = {};\n    this.handle = null;\n    this._lastSetTapCountTime = 0;\n    this._register(EventUtils.runAndSubscribe(DomUtils.onDidRegisterWindow, ({\n      window,\n      disposables\n    }) => {\n      disposables.add(DomUtils.addDisposableListener(window.document, 'touchstart', e => this.onTouchStart(e), {\n        passive: false\n      }));\n      disposables.add(DomUtils.addDisposableListener(window.document, 'touchend', e => this.onTouchEnd(window, e)));\n      disposables.add(DomUtils.addDisposableListener(window.document, 'touchmove', e => this.onTouchMove(e), {\n        passive: false\n      }));\n    }, {\n      window: mainWindow,\n      disposables: this._store\n    }));\n  }\n  static addTarget(element) {\n    if (!Gesture.isTouchDevice()) {\n      return Disposable.None;\n    }\n    if (!Gesture.INSTANCE) {\n      Gesture.INSTANCE = markAsSingleton(new Gesture());\n    }\n    const remove = Gesture.INSTANCE.targets.push(element);\n    return toDisposable(remove);\n  }\n  static ignoreTarget(element) {\n    if (!Gesture.isTouchDevice()) {\n      return Disposable.None;\n    }\n    if (!Gesture.INSTANCE) {\n      Gesture.INSTANCE = markAsSingleton(new Gesture());\n    }\n    const remove = Gesture.INSTANCE.ignoreTargets.push(element);\n    return toDisposable(remove);\n  }\n  static isTouchDevice() {\n    // `'ontouchstart' in window` always evaluates to true with typescript's modern typings. This causes `window` to be\n    // `never` later in `window.navigator`. That's why we need the explicit `window as Window` cast\n    return 'ontouchstart' in mainWindow || navigator.maxTouchPoints > 0;\n  }\n  dispose() {\n    if (this.handle) {\n      this.handle.dispose();\n      this.handle = null;\n    }\n    super.dispose();\n  }\n  onTouchStart(e) {\n    const timestamp = Date.now(); // use Date.now() because on FF e.timeStamp is not epoch based.\n    if (this.handle) {\n      this.handle.dispose();\n      this.handle = null;\n    }\n    for (let i = 0, len = e.targetTouches.length; i < len; i++) {\n      const touch = e.targetTouches.item(i);\n      this.activeTouches[touch.identifier] = {\n        id: touch.identifier,\n        initialTarget: touch.target,\n        initialTimeStamp: timestamp,\n        initialPageX: touch.pageX,\n        initialPageY: touch.pageY,\n        rollingTimestamps: [timestamp],\n        rollingPageX: [touch.pageX],\n        rollingPageY: [touch.pageY]\n      };\n      const evt = this.newGestureEvent(EventType.Start, touch.target);\n      evt.pageX = touch.pageX;\n      evt.pageY = touch.pageY;\n      this.dispatchEvent(evt);\n    }\n    if (this.dispatched) {\n      e.preventDefault();\n      e.stopPropagation();\n      this.dispatched = false;\n    }\n  }\n  onTouchEnd(targetWindow, e) {\n    const timestamp = Date.now(); // use Date.now() because on FF e.timeStamp is not epoch based.\n    const activeTouchCount = Object.keys(this.activeTouches).length;\n    for (let i = 0, len = e.changedTouches.length; i < len; i++) {\n      const touch = e.changedTouches.item(i);\n      if (!this.activeTouches.hasOwnProperty(String(touch.identifier))) {\n        console.warn('move of an UNKNOWN touch', touch);\n        continue;\n      }\n      const data = this.activeTouches[touch.identifier],\n        holdTime = Date.now() - data.initialTimeStamp;\n      if (holdTime < Gesture.HOLD_DELAY && Math.abs(data.initialPageX - arrays.tail(data.rollingPageX)) < 30 && Math.abs(data.initialPageY - arrays.tail(data.rollingPageY)) < 30) {\n        const evt = this.newGestureEvent(EventType.Tap, data.initialTarget);\n        evt.pageX = arrays.tail(data.rollingPageX);\n        evt.pageY = arrays.tail(data.rollingPageY);\n        this.dispatchEvent(evt);\n      } else if (holdTime >= Gesture.HOLD_DELAY && Math.abs(data.initialPageX - arrays.tail(data.rollingPageX)) < 30 && Math.abs(data.initialPageY - arrays.tail(data.rollingPageY)) < 30) {\n        const evt = this.newGestureEvent(EventType.Contextmenu, data.initialTarget);\n        evt.pageX = arrays.tail(data.rollingPageX);\n        evt.pageY = arrays.tail(data.rollingPageY);\n        this.dispatchEvent(evt);\n      } else if (activeTouchCount === 1) {\n        const finalX = arrays.tail(data.rollingPageX);\n        const finalY = arrays.tail(data.rollingPageY);\n        const deltaT = arrays.tail(data.rollingTimestamps) - data.rollingTimestamps[0];\n        const deltaX = finalX - data.rollingPageX[0];\n        const deltaY = finalY - data.rollingPageY[0];\n        // We need to get all the dispatch targets on the start of the inertia event\n        const dispatchTo = [...this.targets].filter(t => data.initialTarget instanceof Node && t.contains(data.initialTarget));\n        this.inertia(targetWindow, dispatchTo, timestamp,\n        // time now\n        Math.abs(deltaX) / deltaT,\n        // speed\n        deltaX > 0 ? 1 : -1,\n        // x direction\n        finalX,\n        // x now\n        Math.abs(deltaY) / deltaT,\n        // y speed\n        deltaY > 0 ? 1 : -1,\n        // y direction\n        finalY // y now\n        );\n      }\n      this.dispatchEvent(this.newGestureEvent(EventType.End, data.initialTarget));\n      // forget about this touch\n      delete this.activeTouches[touch.identifier];\n    }\n    if (this.dispatched) {\n      e.preventDefault();\n      e.stopPropagation();\n      this.dispatched = false;\n    }\n  }\n  newGestureEvent(type, initialTarget) {\n    const event = document.createEvent('CustomEvent');\n    event.initEvent(type, false, true);\n    event.initialTarget = initialTarget;\n    event.tapCount = 0;\n    return event;\n  }\n  dispatchEvent(event) {\n    if (event.type === EventType.Tap) {\n      const currentTime = new Date().getTime();\n      let setTapCount = 0;\n      if (currentTime - this._lastSetTapCountTime > Gesture.CLEAR_TAP_COUNT_TIME) {\n        setTapCount = 1;\n      } else {\n        setTapCount = 2;\n      }\n      this._lastSetTapCountTime = currentTime;\n      event.tapCount = setTapCount;\n    } else if (event.type === EventType.Change || event.type === EventType.Contextmenu) {\n      // tap is canceled by scrolling or context menu\n      this._lastSetTapCountTime = 0;\n    }\n    if (event.initialTarget instanceof Node) {\n      for (const ignoreTarget of this.ignoreTargets) {\n        if (ignoreTarget.contains(event.initialTarget)) {\n          return;\n        }\n      }\n      const targets = [];\n      for (const target of this.targets) {\n        if (target.contains(event.initialTarget)) {\n          let depth = 0;\n          let now = event.initialTarget;\n          while (now && now !== target) {\n            depth++;\n            now = now.parentElement;\n          }\n          targets.push([depth, target]);\n        }\n      }\n      targets.sort((a, b) => a[0] - b[0]);\n      for (const [_, target] of targets) {\n        target.dispatchEvent(event);\n        this.dispatched = true;\n      }\n    }\n  }\n  inertia(targetWindow, dispatchTo, t1, vX, dirX, x, vY, dirY, y) {\n    this.handle = DomUtils.scheduleAtNextAnimationFrame(targetWindow, () => {\n      const now = Date.now();\n      // velocity: old speed + accel_over_time\n      const deltaT = now - t1;\n      let delta_pos_x = 0,\n        delta_pos_y = 0;\n      let stopped = true;\n      vX += Gesture.SCROLL_FRICTION * deltaT;\n      vY += Gesture.SCROLL_FRICTION * deltaT;\n      if (vX > 0) {\n        stopped = false;\n        delta_pos_x = dirX * vX * deltaT;\n      }\n      if (vY > 0) {\n        stopped = false;\n        delta_pos_y = dirY * vY * deltaT;\n      }\n      // dispatch translation event\n      const evt = this.newGestureEvent(EventType.Change);\n      evt.translationX = delta_pos_x;\n      evt.translationY = delta_pos_y;\n      dispatchTo.forEach(d => d.dispatchEvent(evt));\n      if (!stopped) {\n        this.inertia(targetWindow, dispatchTo, now, vX, dirX, x + delta_pos_x, vY, dirY, y + delta_pos_y);\n      }\n    });\n  }\n  onTouchMove(e) {\n    const timestamp = Date.now(); // use Date.now() because on FF e.timeStamp is not epoch based.\n    for (let i = 0, len = e.changedTouches.length; i < len; i++) {\n      const touch = e.changedTouches.item(i);\n      if (!this.activeTouches.hasOwnProperty(String(touch.identifier))) {\n        console.warn('end of an UNKNOWN touch', touch);\n        continue;\n      }\n      const data = this.activeTouches[touch.identifier];\n      const evt = this.newGestureEvent(EventType.Change, data.initialTarget);\n      evt.translationX = touch.pageX - arrays.tail(data.rollingPageX);\n      evt.translationY = touch.pageY - arrays.tail(data.rollingPageY);\n      evt.pageX = touch.pageX;\n      evt.pageY = touch.pageY;\n      this.dispatchEvent(evt);\n      // only keep a few data points, to average the final speed\n      if (data.rollingPageX.length > 3) {\n        data.rollingPageX.shift();\n        data.rollingPageY.shift();\n        data.rollingTimestamps.shift();\n      }\n      data.rollingPageX.push(touch.pageX);\n      data.rollingPageY.push(touch.pageY);\n      data.rollingTimestamps.push(timestamp);\n    }\n    if (this.dispatched) {\n      e.preventDefault();\n      e.stopPropagation();\n      this.dispatched = false;\n    }\n  }\n}\n__decorate([memoize], Gesture, \"isTouchDevice\", null);", "map": {"version": 3, "names": ["__decorate", "decorators", "target", "key", "desc", "c", "arguments", "length", "r", "Object", "getOwnPropertyDescriptor", "d", "Reflect", "decorate", "i", "defineProperty", "<PERSON><PERSON><PERSON><PERSON>", "mainWindow", "arrays", "memoize", "Event", "EventUtils", "Disposable", "<PERSON><PERSON><PERSON><PERSON>", "toDisposable", "LinkedList", "EventType", "Tap", "Change", "Start", "End", "Contextmenu", "Gesture", "SCROLL_FRICTION", "HOLD_DELAY", "CLEAR_TAP_COUNT_TIME", "constructor", "dispatched", "targets", "ignoreTargets", "activeTouches", "handle", "_lastSetTapCountTime", "_register", "runAndSubscribe", "onDidRegisterWindow", "window", "disposables", "add", "addDisposableListener", "document", "e", "onTouchStart", "passive", "onTouchEnd", "onTouchMove", "_store", "addTarget", "element", "isTouchDevice", "None", "INSTANCE", "remove", "push", "<PERSON><PERSON><PERSON><PERSON>", "navigator", "maxTouchPoints", "dispose", "timestamp", "Date", "now", "len", "targetTouches", "touch", "item", "identifier", "id", "initialTarget", "initialTimeStamp", "initialPageX", "pageX", "initialPageY", "pageY", "rollingTimestamps", "rollingPageX", "rollingPageY", "evt", "newGestureEvent", "dispatchEvent", "preventDefault", "stopPropagation", "targetWindow", "activeTouchCount", "keys", "changedTouches", "hasOwnProperty", "String", "console", "warn", "data", "holdTime", "Math", "abs", "tail", "finalX", "finalY", "deltaT", "deltaX", "deltaY", "dispatchTo", "filter", "t", "Node", "contains", "inertia", "type", "event", "createEvent", "initEvent", "tapCount", "currentTime", "getTime", "setTapCount", "depth", "parentElement", "sort", "a", "b", "_", "t1", "vX", "dirX", "x", "vY", "dirY", "y", "scheduleAtNextAnimationFrame", "delta_pos_x", "delta_pos_y", "stopped", "translationX", "translationY", "for<PERSON>ach", "shift"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/browser/touch.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport * as DomUtils from './dom.js';\nimport { mainWindow } from './window.js';\nimport * as arrays from '../common/arrays.js';\nimport { memoize } from '../common/decorators.js';\nimport { Event as EventUtils } from '../common/event.js';\nimport { Disposable, markAsSingleton, toDisposable } from '../common/lifecycle.js';\nimport { LinkedList } from '../common/linkedList.js';\nexport var EventType;\n(function (EventType) {\n    EventType.Tap = '-monaco-gesturetap';\n    EventType.Change = '-monaco-gesturechange';\n    EventType.Start = '-monaco-gesturestart';\n    EventType.End = '-monaco-gesturesend';\n    EventType.Contextmenu = '-monaco-gesturecontextmenu';\n})(EventType || (EventType = {}));\nexport class Gesture extends Disposable {\n    static { this.SCROLL_FRICTION = -0.005; }\n    static { this.HOLD_DELAY = 700; }\n    static { this.CLEAR_TAP_COUNT_TIME = 400; } // ms\n    constructor() {\n        super();\n        this.dispatched = false;\n        this.targets = new LinkedList();\n        this.ignoreTargets = new LinkedList();\n        this.activeTouches = {};\n        this.handle = null;\n        this._lastSetTapCountTime = 0;\n        this._register(EventUtils.runAndSubscribe(DomUtils.onDidRegisterWindow, ({ window, disposables }) => {\n            disposables.add(DomUtils.addDisposableListener(window.document, 'touchstart', (e) => this.onTouchStart(e), { passive: false }));\n            disposables.add(DomUtils.addDisposableListener(window.document, 'touchend', (e) => this.onTouchEnd(window, e)));\n            disposables.add(DomUtils.addDisposableListener(window.document, 'touchmove', (e) => this.onTouchMove(e), { passive: false }));\n        }, { window: mainWindow, disposables: this._store }));\n    }\n    static addTarget(element) {\n        if (!Gesture.isTouchDevice()) {\n            return Disposable.None;\n        }\n        if (!Gesture.INSTANCE) {\n            Gesture.INSTANCE = markAsSingleton(new Gesture());\n        }\n        const remove = Gesture.INSTANCE.targets.push(element);\n        return toDisposable(remove);\n    }\n    static ignoreTarget(element) {\n        if (!Gesture.isTouchDevice()) {\n            return Disposable.None;\n        }\n        if (!Gesture.INSTANCE) {\n            Gesture.INSTANCE = markAsSingleton(new Gesture());\n        }\n        const remove = Gesture.INSTANCE.ignoreTargets.push(element);\n        return toDisposable(remove);\n    }\n    static isTouchDevice() {\n        // `'ontouchstart' in window` always evaluates to true with typescript's modern typings. This causes `window` to be\n        // `never` later in `window.navigator`. That's why we need the explicit `window as Window` cast\n        return 'ontouchstart' in mainWindow || navigator.maxTouchPoints > 0;\n    }\n    dispose() {\n        if (this.handle) {\n            this.handle.dispose();\n            this.handle = null;\n        }\n        super.dispose();\n    }\n    onTouchStart(e) {\n        const timestamp = Date.now(); // use Date.now() because on FF e.timeStamp is not epoch based.\n        if (this.handle) {\n            this.handle.dispose();\n            this.handle = null;\n        }\n        for (let i = 0, len = e.targetTouches.length; i < len; i++) {\n            const touch = e.targetTouches.item(i);\n            this.activeTouches[touch.identifier] = {\n                id: touch.identifier,\n                initialTarget: touch.target,\n                initialTimeStamp: timestamp,\n                initialPageX: touch.pageX,\n                initialPageY: touch.pageY,\n                rollingTimestamps: [timestamp],\n                rollingPageX: [touch.pageX],\n                rollingPageY: [touch.pageY]\n            };\n            const evt = this.newGestureEvent(EventType.Start, touch.target);\n            evt.pageX = touch.pageX;\n            evt.pageY = touch.pageY;\n            this.dispatchEvent(evt);\n        }\n        if (this.dispatched) {\n            e.preventDefault();\n            e.stopPropagation();\n            this.dispatched = false;\n        }\n    }\n    onTouchEnd(targetWindow, e) {\n        const timestamp = Date.now(); // use Date.now() because on FF e.timeStamp is not epoch based.\n        const activeTouchCount = Object.keys(this.activeTouches).length;\n        for (let i = 0, len = e.changedTouches.length; i < len; i++) {\n            const touch = e.changedTouches.item(i);\n            if (!this.activeTouches.hasOwnProperty(String(touch.identifier))) {\n                console.warn('move of an UNKNOWN touch', touch);\n                continue;\n            }\n            const data = this.activeTouches[touch.identifier], holdTime = Date.now() - data.initialTimeStamp;\n            if (holdTime < Gesture.HOLD_DELAY\n                && Math.abs(data.initialPageX - arrays.tail(data.rollingPageX)) < 30\n                && Math.abs(data.initialPageY - arrays.tail(data.rollingPageY)) < 30) {\n                const evt = this.newGestureEvent(EventType.Tap, data.initialTarget);\n                evt.pageX = arrays.tail(data.rollingPageX);\n                evt.pageY = arrays.tail(data.rollingPageY);\n                this.dispatchEvent(evt);\n            }\n            else if (holdTime >= Gesture.HOLD_DELAY\n                && Math.abs(data.initialPageX - arrays.tail(data.rollingPageX)) < 30\n                && Math.abs(data.initialPageY - arrays.tail(data.rollingPageY)) < 30) {\n                const evt = this.newGestureEvent(EventType.Contextmenu, data.initialTarget);\n                evt.pageX = arrays.tail(data.rollingPageX);\n                evt.pageY = arrays.tail(data.rollingPageY);\n                this.dispatchEvent(evt);\n            }\n            else if (activeTouchCount === 1) {\n                const finalX = arrays.tail(data.rollingPageX);\n                const finalY = arrays.tail(data.rollingPageY);\n                const deltaT = arrays.tail(data.rollingTimestamps) - data.rollingTimestamps[0];\n                const deltaX = finalX - data.rollingPageX[0];\n                const deltaY = finalY - data.rollingPageY[0];\n                // We need to get all the dispatch targets on the start of the inertia event\n                const dispatchTo = [...this.targets].filter(t => data.initialTarget instanceof Node && t.contains(data.initialTarget));\n                this.inertia(targetWindow, dispatchTo, timestamp, // time now\n                Math.abs(deltaX) / deltaT, // speed\n                deltaX > 0 ? 1 : -1, // x direction\n                finalX, // x now\n                Math.abs(deltaY) / deltaT, // y speed\n                deltaY > 0 ? 1 : -1, // y direction\n                finalY // y now\n                );\n            }\n            this.dispatchEvent(this.newGestureEvent(EventType.End, data.initialTarget));\n            // forget about this touch\n            delete this.activeTouches[touch.identifier];\n        }\n        if (this.dispatched) {\n            e.preventDefault();\n            e.stopPropagation();\n            this.dispatched = false;\n        }\n    }\n    newGestureEvent(type, initialTarget) {\n        const event = document.createEvent('CustomEvent');\n        event.initEvent(type, false, true);\n        event.initialTarget = initialTarget;\n        event.tapCount = 0;\n        return event;\n    }\n    dispatchEvent(event) {\n        if (event.type === EventType.Tap) {\n            const currentTime = (new Date()).getTime();\n            let setTapCount = 0;\n            if (currentTime - this._lastSetTapCountTime > Gesture.CLEAR_TAP_COUNT_TIME) {\n                setTapCount = 1;\n            }\n            else {\n                setTapCount = 2;\n            }\n            this._lastSetTapCountTime = currentTime;\n            event.tapCount = setTapCount;\n        }\n        else if (event.type === EventType.Change || event.type === EventType.Contextmenu) {\n            // tap is canceled by scrolling or context menu\n            this._lastSetTapCountTime = 0;\n        }\n        if (event.initialTarget instanceof Node) {\n            for (const ignoreTarget of this.ignoreTargets) {\n                if (ignoreTarget.contains(event.initialTarget)) {\n                    return;\n                }\n            }\n            const targets = [];\n            for (const target of this.targets) {\n                if (target.contains(event.initialTarget)) {\n                    let depth = 0;\n                    let now = event.initialTarget;\n                    while (now && now !== target) {\n                        depth++;\n                        now = now.parentElement;\n                    }\n                    targets.push([depth, target]);\n                }\n            }\n            targets.sort((a, b) => a[0] - b[0]);\n            for (const [_, target] of targets) {\n                target.dispatchEvent(event);\n                this.dispatched = true;\n            }\n        }\n    }\n    inertia(targetWindow, dispatchTo, t1, vX, dirX, x, vY, dirY, y) {\n        this.handle = DomUtils.scheduleAtNextAnimationFrame(targetWindow, () => {\n            const now = Date.now();\n            // velocity: old speed + accel_over_time\n            const deltaT = now - t1;\n            let delta_pos_x = 0, delta_pos_y = 0;\n            let stopped = true;\n            vX += Gesture.SCROLL_FRICTION * deltaT;\n            vY += Gesture.SCROLL_FRICTION * deltaT;\n            if (vX > 0) {\n                stopped = false;\n                delta_pos_x = dirX * vX * deltaT;\n            }\n            if (vY > 0) {\n                stopped = false;\n                delta_pos_y = dirY * vY * deltaT;\n            }\n            // dispatch translation event\n            const evt = this.newGestureEvent(EventType.Change);\n            evt.translationX = delta_pos_x;\n            evt.translationY = delta_pos_y;\n            dispatchTo.forEach(d => d.dispatchEvent(evt));\n            if (!stopped) {\n                this.inertia(targetWindow, dispatchTo, now, vX, dirX, x + delta_pos_x, vY, dirY, y + delta_pos_y);\n            }\n        });\n    }\n    onTouchMove(e) {\n        const timestamp = Date.now(); // use Date.now() because on FF e.timeStamp is not epoch based.\n        for (let i = 0, len = e.changedTouches.length; i < len; i++) {\n            const touch = e.changedTouches.item(i);\n            if (!this.activeTouches.hasOwnProperty(String(touch.identifier))) {\n                console.warn('end of an UNKNOWN touch', touch);\n                continue;\n            }\n            const data = this.activeTouches[touch.identifier];\n            const evt = this.newGestureEvent(EventType.Change, data.initialTarget);\n            evt.translationX = touch.pageX - arrays.tail(data.rollingPageX);\n            evt.translationY = touch.pageY - arrays.tail(data.rollingPageY);\n            evt.pageX = touch.pageX;\n            evt.pageY = touch.pageY;\n            this.dispatchEvent(evt);\n            // only keep a few data points, to average the final speed\n            if (data.rollingPageX.length > 3) {\n                data.rollingPageX.shift();\n                data.rollingPageY.shift();\n                data.rollingTimestamps.shift();\n            }\n            data.rollingPageX.push(touch.pageX);\n            data.rollingPageY.push(touch.pageY);\n            data.rollingTimestamps.push(timestamp);\n        }\n        if (this.dispatched) {\n            e.preventDefault();\n            e.stopPropagation();\n            this.dispatched = false;\n        }\n    }\n}\n__decorate([\n    memoize\n], Gesture, \"isTouchDevice\", null);\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,IAAIA,UAAU,GAAI,IAAI,IAAI,IAAI,CAACA,UAAU,IAAK,UAAUC,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAE;EACnF,IAAIC,CAAC,GAAGC,SAAS,CAACC,MAAM;IAAEC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGH,MAAM,GAAGE,IAAI,KAAK,IAAI,GAAGA,IAAI,GAAGK,MAAM,CAACC,wBAAwB,CAACR,MAAM,EAAEC,GAAG,CAAC,GAAGC,IAAI;IAAEO,CAAC;EAC5H,IAAI,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,CAACC,QAAQ,KAAK,UAAU,EAAEL,CAAC,GAAGI,OAAO,CAACC,QAAQ,CAACZ,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,CAAC,CAAC,KAC1H,KAAK,IAAIU,CAAC,GAAGb,UAAU,CAACM,MAAM,GAAG,CAAC,EAAEO,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE,IAAIH,CAAC,GAAGV,UAAU,CAACa,CAAC,CAAC,EAAEN,CAAC,GAAG,CAACH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACH,CAAC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACT,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,GAAGG,CAAC,CAACT,MAAM,EAAEC,GAAG,CAAC,KAAKK,CAAC;EACjJ,OAAOH,CAAC,GAAG,CAAC,IAAIG,CAAC,IAAIC,MAAM,CAACM,cAAc,CAACb,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,EAAEA,CAAC;AACjE,CAAC;AACD,OAAO,KAAKQ,QAAQ,MAAM,UAAU;AACpC,SAASC,UAAU,QAAQ,aAAa;AACxC,OAAO,KAAKC,MAAM,MAAM,qBAAqB;AAC7C,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,KAAK,IAAIC,UAAU,QAAQ,oBAAoB;AACxD,SAASC,UAAU,EAAEC,eAAe,EAAEC,YAAY,QAAQ,wBAAwB;AAClF,SAASC,UAAU,QAAQ,yBAAyB;AACpD,OAAO,IAAIC,SAAS,gBACnB,UAAUA,SAAS,EAAE;EAClBA,SAAS,CAACC,GAAG,GAAG,oBAAoB;EACpCD,SAAS,CAACE,MAAM,GAAG,uBAAuB;EAC1CF,SAAS,CAACG,KAAK,GAAG,sBAAsB;EACxCH,SAAS,CAACI,GAAG,GAAG,qBAAqB;EACrCJ,SAAS,CAACK,WAAW,GAAG,4BAA4B;EAAC,OAL9CL,SAAS;AAMpB,CAAC,CAAEA,SAAS,IAAiB,CAAC,CAAE,CAPZ;AAQpB,OAAO,MAAMM,OAAO,SAASV,UAAU,CAAC;EACpC;IAAS,IAAI,CAACW,eAAe,GAAG,CAAC,KAAK;EAAE;EACxC;IAAS,IAAI,CAACC,UAAU,GAAG,GAAG;EAAE;EAChC;IAAS,IAAI,CAACC,oBAAoB,GAAG,GAAG;EAAE,CAAC,CAAC;EAC5CC,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,OAAO,GAAG,IAAIb,UAAU,CAAC,CAAC;IAC/B,IAAI,CAACc,aAAa,GAAG,IAAId,UAAU,CAAC,CAAC;IACrC,IAAI,CAACe,aAAa,GAAG,CAAC,CAAC;IACvB,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,oBAAoB,GAAG,CAAC;IAC7B,IAAI,CAACC,SAAS,CAACtB,UAAU,CAACuB,eAAe,CAAC5B,QAAQ,CAAC6B,mBAAmB,EAAE,CAAC;MAAEC,MAAM;MAAEC;IAAY,CAAC,KAAK;MACjGA,WAAW,CAACC,GAAG,CAAChC,QAAQ,CAACiC,qBAAqB,CAACH,MAAM,CAACI,QAAQ,EAAE,YAAY,EAAGC,CAAC,IAAK,IAAI,CAACC,YAAY,CAACD,CAAC,CAAC,EAAE;QAAEE,OAAO,EAAE;MAAM,CAAC,CAAC,CAAC;MAC/HN,WAAW,CAACC,GAAG,CAAChC,QAAQ,CAACiC,qBAAqB,CAACH,MAAM,CAACI,QAAQ,EAAE,UAAU,EAAGC,CAAC,IAAK,IAAI,CAACG,UAAU,CAACR,MAAM,EAAEK,CAAC,CAAC,CAAC,CAAC;MAC/GJ,WAAW,CAACC,GAAG,CAAChC,QAAQ,CAACiC,qBAAqB,CAACH,MAAM,CAACI,QAAQ,EAAE,WAAW,EAAGC,CAAC,IAAK,IAAI,CAACI,WAAW,CAACJ,CAAC,CAAC,EAAE;QAAEE,OAAO,EAAE;MAAM,CAAC,CAAC,CAAC;IACjI,CAAC,EAAE;MAAEP,MAAM,EAAE7B,UAAU;MAAE8B,WAAW,EAAE,IAAI,CAACS;IAAO,CAAC,CAAC,CAAC;EACzD;EACA,OAAOC,SAASA,CAACC,OAAO,EAAE;IACtB,IAAI,CAAC1B,OAAO,CAAC2B,aAAa,CAAC,CAAC,EAAE;MAC1B,OAAOrC,UAAU,CAACsC,IAAI;IAC1B;IACA,IAAI,CAAC5B,OAAO,CAAC6B,QAAQ,EAAE;MACnB7B,OAAO,CAAC6B,QAAQ,GAAGtC,eAAe,CAAC,IAAIS,OAAO,CAAC,CAAC,CAAC;IACrD;IACA,MAAM8B,MAAM,GAAG9B,OAAO,CAAC6B,QAAQ,CAACvB,OAAO,CAACyB,IAAI,CAACL,OAAO,CAAC;IACrD,OAAOlC,YAAY,CAACsC,MAAM,CAAC;EAC/B;EACA,OAAOE,YAAYA,CAACN,OAAO,EAAE;IACzB,IAAI,CAAC1B,OAAO,CAAC2B,aAAa,CAAC,CAAC,EAAE;MAC1B,OAAOrC,UAAU,CAACsC,IAAI;IAC1B;IACA,IAAI,CAAC5B,OAAO,CAAC6B,QAAQ,EAAE;MACnB7B,OAAO,CAAC6B,QAAQ,GAAGtC,eAAe,CAAC,IAAIS,OAAO,CAAC,CAAC,CAAC;IACrD;IACA,MAAM8B,MAAM,GAAG9B,OAAO,CAAC6B,QAAQ,CAACtB,aAAa,CAACwB,IAAI,CAACL,OAAO,CAAC;IAC3D,OAAOlC,YAAY,CAACsC,MAAM,CAAC;EAC/B;EACA,OAAOH,aAAaA,CAAA,EAAG;IACnB;IACA;IACA,OAAO,cAAc,IAAI1C,UAAU,IAAIgD,SAAS,CAACC,cAAc,GAAG,CAAC;EACvE;EACAC,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAAC1B,MAAM,EAAE;MACb,IAAI,CAACA,MAAM,CAAC0B,OAAO,CAAC,CAAC;MACrB,IAAI,CAAC1B,MAAM,GAAG,IAAI;IACtB;IACA,KAAK,CAAC0B,OAAO,CAAC,CAAC;EACnB;EACAf,YAAYA,CAACD,CAAC,EAAE;IACZ,MAAMiB,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC9B,IAAI,IAAI,CAAC7B,MAAM,EAAE;MACb,IAAI,CAACA,MAAM,CAAC0B,OAAO,CAAC,CAAC;MACrB,IAAI,CAAC1B,MAAM,GAAG,IAAI;IACtB;IACA,KAAK,IAAI3B,CAAC,GAAG,CAAC,EAAEyD,GAAG,GAAGpB,CAAC,CAACqB,aAAa,CAACjE,MAAM,EAAEO,CAAC,GAAGyD,GAAG,EAAEzD,CAAC,EAAE,EAAE;MACxD,MAAM2D,KAAK,GAAGtB,CAAC,CAACqB,aAAa,CAACE,IAAI,CAAC5D,CAAC,CAAC;MACrC,IAAI,CAAC0B,aAAa,CAACiC,KAAK,CAACE,UAAU,CAAC,GAAG;QACnCC,EAAE,EAAEH,KAAK,CAACE,UAAU;QACpBE,aAAa,EAAEJ,KAAK,CAACvE,MAAM;QAC3B4E,gBAAgB,EAAEV,SAAS;QAC3BW,YAAY,EAAEN,KAAK,CAACO,KAAK;QACzBC,YAAY,EAAER,KAAK,CAACS,KAAK;QACzBC,iBAAiB,EAAE,CAACf,SAAS,CAAC;QAC9BgB,YAAY,EAAE,CAACX,KAAK,CAACO,KAAK,CAAC;QAC3BK,YAAY,EAAE,CAACZ,KAAK,CAACS,KAAK;MAC9B,CAAC;MACD,MAAMI,GAAG,GAAG,IAAI,CAACC,eAAe,CAAC7D,SAAS,CAACG,KAAK,EAAE4C,KAAK,CAACvE,MAAM,CAAC;MAC/DoF,GAAG,CAACN,KAAK,GAAGP,KAAK,CAACO,KAAK;MACvBM,GAAG,CAACJ,KAAK,GAAGT,KAAK,CAACS,KAAK;MACvB,IAAI,CAACM,aAAa,CAACF,GAAG,CAAC;IAC3B;IACA,IAAI,IAAI,CAACjD,UAAU,EAAE;MACjBc,CAAC,CAACsC,cAAc,CAAC,CAAC;MAClBtC,CAAC,CAACuC,eAAe,CAAC,CAAC;MACnB,IAAI,CAACrD,UAAU,GAAG,KAAK;IAC3B;EACJ;EACAiB,UAAUA,CAACqC,YAAY,EAAExC,CAAC,EAAE;IACxB,MAAMiB,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC9B,MAAMsB,gBAAgB,GAAGnF,MAAM,CAACoF,IAAI,CAAC,IAAI,CAACrD,aAAa,CAAC,CAACjC,MAAM;IAC/D,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEyD,GAAG,GAAGpB,CAAC,CAAC2C,cAAc,CAACvF,MAAM,EAAEO,CAAC,GAAGyD,GAAG,EAAEzD,CAAC,EAAE,EAAE;MACzD,MAAM2D,KAAK,GAAGtB,CAAC,CAAC2C,cAAc,CAACpB,IAAI,CAAC5D,CAAC,CAAC;MACtC,IAAI,CAAC,IAAI,CAAC0B,aAAa,CAACuD,cAAc,CAACC,MAAM,CAACvB,KAAK,CAACE,UAAU,CAAC,CAAC,EAAE;QAC9DsB,OAAO,CAACC,IAAI,CAAC,0BAA0B,EAAEzB,KAAK,CAAC;QAC/C;MACJ;MACA,MAAM0B,IAAI,GAAG,IAAI,CAAC3D,aAAa,CAACiC,KAAK,CAACE,UAAU,CAAC;QAAEyB,QAAQ,GAAG/B,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG6B,IAAI,CAACrB,gBAAgB;MAChG,IAAIsB,QAAQ,GAAGpE,OAAO,CAACE,UAAU,IAC1BmE,IAAI,CAACC,GAAG,CAACH,IAAI,CAACpB,YAAY,GAAG7D,MAAM,CAACqF,IAAI,CAACJ,IAAI,CAACf,YAAY,CAAC,CAAC,GAAG,EAAE,IACjEiB,IAAI,CAACC,GAAG,CAACH,IAAI,CAAClB,YAAY,GAAG/D,MAAM,CAACqF,IAAI,CAACJ,IAAI,CAACd,YAAY,CAAC,CAAC,GAAG,EAAE,EAAE;QACtE,MAAMC,GAAG,GAAG,IAAI,CAACC,eAAe,CAAC7D,SAAS,CAACC,GAAG,EAAEwE,IAAI,CAACtB,aAAa,CAAC;QACnES,GAAG,CAACN,KAAK,GAAG9D,MAAM,CAACqF,IAAI,CAACJ,IAAI,CAACf,YAAY,CAAC;QAC1CE,GAAG,CAACJ,KAAK,GAAGhE,MAAM,CAACqF,IAAI,CAACJ,IAAI,CAACd,YAAY,CAAC;QAC1C,IAAI,CAACG,aAAa,CAACF,GAAG,CAAC;MAC3B,CAAC,MACI,IAAIc,QAAQ,IAAIpE,OAAO,CAACE,UAAU,IAChCmE,IAAI,CAACC,GAAG,CAACH,IAAI,CAACpB,YAAY,GAAG7D,MAAM,CAACqF,IAAI,CAACJ,IAAI,CAACf,YAAY,CAAC,CAAC,GAAG,EAAE,IACjEiB,IAAI,CAACC,GAAG,CAACH,IAAI,CAAClB,YAAY,GAAG/D,MAAM,CAACqF,IAAI,CAACJ,IAAI,CAACd,YAAY,CAAC,CAAC,GAAG,EAAE,EAAE;QACtE,MAAMC,GAAG,GAAG,IAAI,CAACC,eAAe,CAAC7D,SAAS,CAACK,WAAW,EAAEoE,IAAI,CAACtB,aAAa,CAAC;QAC3ES,GAAG,CAACN,KAAK,GAAG9D,MAAM,CAACqF,IAAI,CAACJ,IAAI,CAACf,YAAY,CAAC;QAC1CE,GAAG,CAACJ,KAAK,GAAGhE,MAAM,CAACqF,IAAI,CAACJ,IAAI,CAACd,YAAY,CAAC;QAC1C,IAAI,CAACG,aAAa,CAACF,GAAG,CAAC;MAC3B,CAAC,MACI,IAAIM,gBAAgB,KAAK,CAAC,EAAE;QAC7B,MAAMY,MAAM,GAAGtF,MAAM,CAACqF,IAAI,CAACJ,IAAI,CAACf,YAAY,CAAC;QAC7C,MAAMqB,MAAM,GAAGvF,MAAM,CAACqF,IAAI,CAACJ,IAAI,CAACd,YAAY,CAAC;QAC7C,MAAMqB,MAAM,GAAGxF,MAAM,CAACqF,IAAI,CAACJ,IAAI,CAAChB,iBAAiB,CAAC,GAAGgB,IAAI,CAAChB,iBAAiB,CAAC,CAAC,CAAC;QAC9E,MAAMwB,MAAM,GAAGH,MAAM,GAAGL,IAAI,CAACf,YAAY,CAAC,CAAC,CAAC;QAC5C,MAAMwB,MAAM,GAAGH,MAAM,GAAGN,IAAI,CAACd,YAAY,CAAC,CAAC,CAAC;QAC5C;QACA,MAAMwB,UAAU,GAAG,CAAC,GAAG,IAAI,CAACvE,OAAO,CAAC,CAACwE,MAAM,CAACC,CAAC,IAAIZ,IAAI,CAACtB,aAAa,YAAYmC,IAAI,IAAID,CAAC,CAACE,QAAQ,CAACd,IAAI,CAACtB,aAAa,CAAC,CAAC;QACtH,IAAI,CAACqC,OAAO,CAACvB,YAAY,EAAEkB,UAAU,EAAEzC,SAAS;QAAE;QAClDiC,IAAI,CAACC,GAAG,CAACK,MAAM,CAAC,GAAGD,MAAM;QAAE;QAC3BC,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAAE;QACrBH,MAAM;QAAE;QACRH,IAAI,CAACC,GAAG,CAACM,MAAM,CAAC,GAAGF,MAAM;QAAE;QAC3BE,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAAE;QACrBH,MAAM,CAAC;QACP,CAAC;MACL;MACA,IAAI,CAACjB,aAAa,CAAC,IAAI,CAACD,eAAe,CAAC7D,SAAS,CAACI,GAAG,EAAEqE,IAAI,CAACtB,aAAa,CAAC,CAAC;MAC3E;MACA,OAAO,IAAI,CAACrC,aAAa,CAACiC,KAAK,CAACE,UAAU,CAAC;IAC/C;IACA,IAAI,IAAI,CAACtC,UAAU,EAAE;MACjBc,CAAC,CAACsC,cAAc,CAAC,CAAC;MAClBtC,CAAC,CAACuC,eAAe,CAAC,CAAC;MACnB,IAAI,CAACrD,UAAU,GAAG,KAAK;IAC3B;EACJ;EACAkD,eAAeA,CAAC4B,IAAI,EAAEtC,aAAa,EAAE;IACjC,MAAMuC,KAAK,GAAGlE,QAAQ,CAACmE,WAAW,CAAC,aAAa,CAAC;IACjDD,KAAK,CAACE,SAAS,CAACH,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC;IAClCC,KAAK,CAACvC,aAAa,GAAGA,aAAa;IACnCuC,KAAK,CAACG,QAAQ,GAAG,CAAC;IAClB,OAAOH,KAAK;EAChB;EACA5B,aAAaA,CAAC4B,KAAK,EAAE;IACjB,IAAIA,KAAK,CAACD,IAAI,KAAKzF,SAAS,CAACC,GAAG,EAAE;MAC9B,MAAM6F,WAAW,GAAI,IAAInD,IAAI,CAAC,CAAC,CAAEoD,OAAO,CAAC,CAAC;MAC1C,IAAIC,WAAW,GAAG,CAAC;MACnB,IAAIF,WAAW,GAAG,IAAI,CAAC9E,oBAAoB,GAAGV,OAAO,CAACG,oBAAoB,EAAE;QACxEuF,WAAW,GAAG,CAAC;MACnB,CAAC,MACI;QACDA,WAAW,GAAG,CAAC;MACnB;MACA,IAAI,CAAChF,oBAAoB,GAAG8E,WAAW;MACvCJ,KAAK,CAACG,QAAQ,GAAGG,WAAW;IAChC,CAAC,MACI,IAAIN,KAAK,CAACD,IAAI,KAAKzF,SAAS,CAACE,MAAM,IAAIwF,KAAK,CAACD,IAAI,KAAKzF,SAAS,CAACK,WAAW,EAAE;MAC9E;MACA,IAAI,CAACW,oBAAoB,GAAG,CAAC;IACjC;IACA,IAAI0E,KAAK,CAACvC,aAAa,YAAYmC,IAAI,EAAE;MACrC,KAAK,MAAMhD,YAAY,IAAI,IAAI,CAACzB,aAAa,EAAE;QAC3C,IAAIyB,YAAY,CAACiD,QAAQ,CAACG,KAAK,CAACvC,aAAa,CAAC,EAAE;UAC5C;QACJ;MACJ;MACA,MAAMvC,OAAO,GAAG,EAAE;MAClB,KAAK,MAAMpC,MAAM,IAAI,IAAI,CAACoC,OAAO,EAAE;QAC/B,IAAIpC,MAAM,CAAC+G,QAAQ,CAACG,KAAK,CAACvC,aAAa,CAAC,EAAE;UACtC,IAAI8C,KAAK,GAAG,CAAC;UACb,IAAIrD,GAAG,GAAG8C,KAAK,CAACvC,aAAa;UAC7B,OAAOP,GAAG,IAAIA,GAAG,KAAKpE,MAAM,EAAE;YAC1ByH,KAAK,EAAE;YACPrD,GAAG,GAAGA,GAAG,CAACsD,aAAa;UAC3B;UACAtF,OAAO,CAACyB,IAAI,CAAC,CAAC4D,KAAK,EAAEzH,MAAM,CAAC,CAAC;QACjC;MACJ;MACAoC,OAAO,CAACuF,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnC,KAAK,MAAM,CAACC,CAAC,EAAE9H,MAAM,CAAC,IAAIoC,OAAO,EAAE;QAC/BpC,MAAM,CAACsF,aAAa,CAAC4B,KAAK,CAAC;QAC3B,IAAI,CAAC/E,UAAU,GAAG,IAAI;MAC1B;IACJ;EACJ;EACA6E,OAAOA,CAACvB,YAAY,EAAEkB,UAAU,EAAEoB,EAAE,EAAEC,EAAE,EAAEC,IAAI,EAAEC,CAAC,EAAEC,EAAE,EAAEC,IAAI,EAAEC,CAAC,EAAE;IAC5D,IAAI,CAAC9F,MAAM,GAAGzB,QAAQ,CAACwH,4BAA4B,CAAC7C,YAAY,EAAE,MAAM;MACpE,MAAMrB,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;MACtB;MACA,MAAMoC,MAAM,GAAGpC,GAAG,GAAG2D,EAAE;MACvB,IAAIQ,WAAW,GAAG,CAAC;QAAEC,WAAW,GAAG,CAAC;MACpC,IAAIC,OAAO,GAAG,IAAI;MAClBT,EAAE,IAAIlG,OAAO,CAACC,eAAe,GAAGyE,MAAM;MACtC2B,EAAE,IAAIrG,OAAO,CAACC,eAAe,GAAGyE,MAAM;MACtC,IAAIwB,EAAE,GAAG,CAAC,EAAE;QACRS,OAAO,GAAG,KAAK;QACfF,WAAW,GAAGN,IAAI,GAAGD,EAAE,GAAGxB,MAAM;MACpC;MACA,IAAI2B,EAAE,GAAG,CAAC,EAAE;QACRM,OAAO,GAAG,KAAK;QACfD,WAAW,GAAGJ,IAAI,GAAGD,EAAE,GAAG3B,MAAM;MACpC;MACA;MACA,MAAMpB,GAAG,GAAG,IAAI,CAACC,eAAe,CAAC7D,SAAS,CAACE,MAAM,CAAC;MAClD0D,GAAG,CAACsD,YAAY,GAAGH,WAAW;MAC9BnD,GAAG,CAACuD,YAAY,GAAGH,WAAW;MAC9B7B,UAAU,CAACiC,OAAO,CAACnI,CAAC,IAAIA,CAAC,CAAC6E,aAAa,CAACF,GAAG,CAAC,CAAC;MAC7C,IAAI,CAACqD,OAAO,EAAE;QACV,IAAI,CAACzB,OAAO,CAACvB,YAAY,EAAEkB,UAAU,EAAEvC,GAAG,EAAE4D,EAAE,EAAEC,IAAI,EAAEC,CAAC,GAAGK,WAAW,EAAEJ,EAAE,EAAEC,IAAI,EAAEC,CAAC,GAAGG,WAAW,CAAC;MACrG;IACJ,CAAC,CAAC;EACN;EACAnF,WAAWA,CAACJ,CAAC,EAAE;IACX,MAAMiB,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC9B,KAAK,IAAIxD,CAAC,GAAG,CAAC,EAAEyD,GAAG,GAAGpB,CAAC,CAAC2C,cAAc,CAACvF,MAAM,EAAEO,CAAC,GAAGyD,GAAG,EAAEzD,CAAC,EAAE,EAAE;MACzD,MAAM2D,KAAK,GAAGtB,CAAC,CAAC2C,cAAc,CAACpB,IAAI,CAAC5D,CAAC,CAAC;MACtC,IAAI,CAAC,IAAI,CAAC0B,aAAa,CAACuD,cAAc,CAACC,MAAM,CAACvB,KAAK,CAACE,UAAU,CAAC,CAAC,EAAE;QAC9DsB,OAAO,CAACC,IAAI,CAAC,yBAAyB,EAAEzB,KAAK,CAAC;QAC9C;MACJ;MACA,MAAM0B,IAAI,GAAG,IAAI,CAAC3D,aAAa,CAACiC,KAAK,CAACE,UAAU,CAAC;MACjD,MAAMW,GAAG,GAAG,IAAI,CAACC,eAAe,CAAC7D,SAAS,CAACE,MAAM,EAAEuE,IAAI,CAACtB,aAAa,CAAC;MACtES,GAAG,CAACsD,YAAY,GAAGnE,KAAK,CAACO,KAAK,GAAG9D,MAAM,CAACqF,IAAI,CAACJ,IAAI,CAACf,YAAY,CAAC;MAC/DE,GAAG,CAACuD,YAAY,GAAGpE,KAAK,CAACS,KAAK,GAAGhE,MAAM,CAACqF,IAAI,CAACJ,IAAI,CAACd,YAAY,CAAC;MAC/DC,GAAG,CAACN,KAAK,GAAGP,KAAK,CAACO,KAAK;MACvBM,GAAG,CAACJ,KAAK,GAAGT,KAAK,CAACS,KAAK;MACvB,IAAI,CAACM,aAAa,CAACF,GAAG,CAAC;MACvB;MACA,IAAIa,IAAI,CAACf,YAAY,CAAC7E,MAAM,GAAG,CAAC,EAAE;QAC9B4F,IAAI,CAACf,YAAY,CAAC2D,KAAK,CAAC,CAAC;QACzB5C,IAAI,CAACd,YAAY,CAAC0D,KAAK,CAAC,CAAC;QACzB5C,IAAI,CAAChB,iBAAiB,CAAC4D,KAAK,CAAC,CAAC;MAClC;MACA5C,IAAI,CAACf,YAAY,CAACrB,IAAI,CAACU,KAAK,CAACO,KAAK,CAAC;MACnCmB,IAAI,CAACd,YAAY,CAACtB,IAAI,CAACU,KAAK,CAACS,KAAK,CAAC;MACnCiB,IAAI,CAAChB,iBAAiB,CAACpB,IAAI,CAACK,SAAS,CAAC;IAC1C;IACA,IAAI,IAAI,CAAC/B,UAAU,EAAE;MACjBc,CAAC,CAACsC,cAAc,CAAC,CAAC;MAClBtC,CAAC,CAACuC,eAAe,CAAC,CAAC;MACnB,IAAI,CAACrD,UAAU,GAAG,KAAK;IAC3B;EACJ;AACJ;AACArC,UAAU,CAAC,CACPmB,OAAO,CACV,EAAEa,OAAO,EAAE,eAAe,EAAE,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}