{"ast": null, "code": "import _asyncToGenerator from \"C:/console/aava-ui-web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { takeUntil, of, map, catchError, Subject, debounceTime, distinctUntilChanged, startWith } from 'rxjs';\nimport { PageFooterComponent } from '@shared/components/page-footer/page-footer.component';\nimport { formatToDisplayDate } from '@shared/utils/date-utils';\nimport promptsLabels from './constants/prompts.json';\nimport { PROMPTS_BASE_ACTIONS } from './prompts-actions';\nimport { AvaTextboxComponent, TextCardComponent, DropdownComponent, IconComponent } from '@ava/play-comp-library';\nimport { LucideAngularModule } from 'lucide-angular';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { ConsoleCardComponent } from '@shared/components/console-card/console-card.component';\nimport { TimeAgoPipe } from '@shared/pipes/time-ago.pipe';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@shared/services/pagination.service\";\nimport * as i2 from \"@shared/services/prompts.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@angular/common\";\nfunction PromptsComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"div\", 14)(2, \"h5\", 15);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.promptLabels.noResults);\n  }\n}\nfunction PromptsComponent_ng_container_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"ava-console-card\", 16);\n    i0.ɵɵpipe(2, \"timeAgo\");\n    i0.ɵɵlistener(\"actionClick\", function PromptsComponent_ng_container_11_Template_ava_console_card_actionClick_1_listener($event) {\n      const prompt_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onActionClick($event, prompt_r3.id));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const prompt_r3 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"title\", prompt_r3 == null ? null : prompt_r3.title)(\"description\", prompt_r3 == null ? null : prompt_r3.description)(\"author\", (prompt_r3 == null ? null : prompt_r3.owner) || \"AAVA\")(\"date\", i0.ɵɵpipeBind1(2, 6, prompt_r3 == null ? null : prompt_r3.createdDate))(\"actions\", ctx_r0.defaultActions)(\"skeleton\", ctx_r0.isLoading);\n  }\n}\nfunction PromptsComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"div\", 18)(2, \"app-page-footer\", 19);\n    i0.ɵɵlistener(\"pageChange\", function PromptsComponent_div_12_Template_app_page_footer_pageChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onPageChange($event));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"totalItems\", ctx_r0.filteredPrompts.length + 1)(\"currentPage\", ctx_r0.currentPage)(\"itemsPerPage\", ctx_r0.itemsPerPage);\n  }\n}\nexport let PromptsComponent = /*#__PURE__*/(() => {\n  class PromptsComponent {\n    paginationService;\n    promptsService;\n    router;\n    route;\n    fb;\n    // Popup state for success messages\n    showSuccessPopup = false;\n    popupMessage = '';\n    popupTitle = '';\n    iconName = 'info';\n    submissionSuccess = false;\n    // popup for delete confirmation\n    showDeletePopup = false;\n    promptToDelete = null;\n    simpleOptions = [{\n      name: 'Option 1',\n      value: '1'\n    }, {\n      name: 'Option 2',\n      value: '2'\n    }, {\n      name: 'Option 3',\n      value: '3'\n    }, {\n      name: 'Option 4',\n      value: '4'\n    }, {\n      name: 'Option 5',\n      value: '5'\n    }];\n    defaultActions = [{\n      id: 'delete',\n      icon: 'trash',\n      label: 'Delete item',\n      tooltip: 'Delete'\n    }, {\n      id: 'edit',\n      icon: 'edit',\n      label: 'Edit item',\n      tooltip: 'Edit'\n    }, {\n      id: 'view',\n      icon: 'eye',\n      label: 'View Prompts',\n      tooltip: 'View',\n      isPrimary: true\n    }];\n    promptLabels = promptsLabels.labels;\n    allPrompts = [];\n    filteredPrompts = [];\n    displayedPrompts = [];\n    searchForm;\n    isLoading = false;\n    currentPage = 1;\n    itemsPerPage = 11;\n    totalPages = 1;\n    destroy$ = new Subject();\n    selectedData = null;\n    cardSkeletonPlaceholders = Array(11);\n    constructor(paginationService, promptsService, router, route, fb) {\n      this.paginationService = paginationService;\n      this.promptsService = promptsService;\n      this.router = router;\n      this.route = route;\n      this.fb = fb;\n      this.searchForm = this.fb.group({\n        search: ['']\n      });\n    }\n    ngOnInit() {\n      this.isLoading = true;\n      this.initSearchListener();\n      this.promptsService.fetchAllPrompts().pipe(takeUntil(this.destroy$), map(this.transformResponseToCardData.bind(this)), catchError(error => {\n        console.error('Error fetching prompts:', error);\n        this.isLoading = false;\n        return of({\n          prompts: []\n        });\n      })).subscribe({\n        next: ({\n          prompts\n        }) => {\n          this.allPrompts = prompts;\n          this.filteredPrompts = [...prompts];\n          this.updateDisplayedPrompts();\n          this.setInitialPageFromQueryParam();\n        },\n        error: err => console.error('Subscription error:', err.message),\n        complete: () => {\n          this.isLoading = false;\n        }\n      });\n    }\n    onSelectionChange(data) {\n      this.selectedData = data;\n    }\n    onCreatePrompt() {\n      this.router.navigate(['/libraries/prompts/create']);\n    }\n    getHeaderIcons(prompt) {\n      return [{\n        iconName: 'NotebookText',\n        title: prompt.toolType || 'Prompt'\n      }, {\n        iconName: 'users',\n        title: `${prompt.userCount || 30}`\n      }];\n    }\n    getFooterIcons(prompt) {\n      return [{\n        iconName: 'user',\n        title: prompt.owner || 'AAVA'\n      }, {\n        iconName: 'calendar-days',\n        title: prompt.createdDate\n      }];\n    }\n    onPageChange(page) {\n      this.currentPage = page;\n      this.updateDisplayedPrompts();\n      this.router.navigate([], {\n        relativeTo: this.route,\n        queryParams: {\n          page: this.currentPage\n        },\n        queryParamsHandling: 'merge'\n      });\n    }\n    onActionClick(event, promptId) {\n      switch (event.actionId) {\n        case 'delete':\n          this.deletePrompt(promptId);\n          break;\n        case 'edit':\n          this.executePrompt(promptId);\n          break;\n        case 'view':\n          this.viewPrompt(promptId);\n          break;\n        default:\n          break;\n      }\n    }\n    viewPrompt(promptId) {\n      this.router.navigate(['/libraries/prompts/edit', promptId], {\n        queryParams: {\n          view: 'true',\n          returnPage: this.currentPage\n        }\n      });\n    }\n    transformResponseToCardData(response) {\n      const prompts = 'prompts' in response ? response.prompts : response;\n      return {\n        prompts: prompts.map(this.formatPromptCard.bind(this))\n      };\n    }\n    formatPromptCard(item) {\n      const {\n        name,\n        updatedAt,\n        categoryName,\n        domainName,\n        tags = [],\n        ...rest\n      } = item;\n      const customTags = this.getCustomTags(categoryName, domainName);\n      const allTags = [...tags, ...customTags];\n      const tagSummary = this.getTagSummary(allTags);\n      const createdDate = formatToDisplayDate(updatedAt);\n      return {\n        title: name,\n        createdDate,\n        tags: allTags,\n        tagSummary,\n        actions: PROMPTS_BASE_ACTIONS,\n        ...rest\n      };\n    }\n    // Step 1: User clicks trash icon → open delete confirmation\n    deletePrompt(promptId) {\n      const prompt = this.allPrompts.find(p => p.id === promptId);\n      if (!prompt) return;\n      this.promptToDelete = prompt;\n      this.showDeletePopup = true;\n    }\n    // Step 2: User confirms delete in popup\n    onConfirmDelete() {\n      if (!this.promptToDelete?.id) return;\n      const promptId = this.promptToDelete.id;\n      this.promptsService.deletePrompt(promptId).subscribe({\n        next: res => {\n          if (res && res.success !== false) {\n            // Update local prompt lists\n            this.allPrompts = this.allPrompts.filter(p => p.id !== promptId);\n            this.filteredPrompts = this.filteredPrompts.filter(p => p.id !== promptId);\n            this.updateDisplayedPrompts();\n            // Show success popup\n            this.iconName = 'check-circle';\n            this.popupTitle = 'Success';\n            this.popupMessage = 'Prompt deleted successfully.';\n            this.submissionSuccess = true;\n            this.showSuccessPopup = true;\n          } else {\n            this.iconName = 'alert-circle';\n            this.popupTitle = 'Error';\n            this.popupMessage = 'Failed to delete prompt.';\n            this.submissionSuccess = false;\n            this.showSuccessPopup = true;\n          }\n          this.closeDeletePopup();\n        },\n        error: err => {\n          console.error('Failed to delete prompt:', err);\n          this.iconName = 'alert-circle';\n          this.popupTitle = 'Error';\n          this.popupMessage = 'An unexpected error occurred.';\n          this.submissionSuccess = false;\n          this.showSuccessPopup = true;\n          this.closeDeletePopup();\n        }\n      });\n    }\n    // Step 3: User cancels or closes delete popup\n    closeDeletePopup() {\n      this.showDeletePopup = false;\n      this.promptToDelete = null;\n    }\n    // Success popup confirm handler\n    onSuccessConfirm() {\n      this.closeSuccessPopup();\n    }\n    // Close success popup manually or when user clicks close icon\n    closeSuccessPopup() {\n      this.showSuccessPopup = false;\n      this.popupTitle = '';\n      this.popupMessage = '';\n      this.iconName = 'info';\n    }\n    executePrompt(promptId) {\n      this.router.navigate(['/libraries/prompts/edit', promptId], {\n        queryParams: {\n          execute: 'true',\n          returnPage: this.currentPage\n        }\n      });\n    }\n    copyPrompt(promptId) {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        if (!promptId) return;\n        const prompt = _this.allPrompts.find(p => p.id === promptId);\n        if (prompt) {\n          try {\n            yield navigator.clipboard.writeText(JSON.stringify(prompt, null, 2));\n          } catch (err) {\n            // Optionally handle error silently\n          }\n        }\n      })();\n    }\n    getCustomTags(categoryName, domainName) {\n      const tags = [];\n      if (categoryName) tags.push({\n        label: categoryName,\n        type: 'primary'\n      });\n      if (domainName) tags.push({\n        label: domainName,\n        type: 'secondary'\n      });\n      return tags;\n    }\n    getTagSummary(tags) {\n      return tags.map(tag => tag.label).join(', ');\n    }\n    setInitialPageFromQueryParam() {\n      const pageParam = this.route.snapshot.queryParamMap.get('page');\n      if (pageParam) {\n        const page = parseInt(pageParam, 10);\n        if (!isNaN(page)) this.currentPage = page;\n      }\n    }\n    initSearchListener() {\n      this.searchForm.get('search').valueChanges.pipe(startWith(''), debounceTime(300), distinctUntilChanged(), map(value => value?.toLowerCase() ?? ''), takeUntil(this.destroy$)).subscribe(searchText => {\n        this.filterPrompts(searchText);\n      });\n    }\n    updateDisplayedPrompts() {\n      this.itemsPerPage = this.currentPage === 1 ? 12 : 11;\n      const {\n        displayedItems,\n        totalPages\n      } = this.paginationService.getPaginatedItems(this.filteredPrompts, this.currentPage, this.itemsPerPage);\n      this.displayedPrompts = displayedItems;\n      this.totalPages = totalPages;\n    }\n    filterPrompts(searchText) {\n      this.filteredPrompts = this.allPrompts.filter(prompt => {\n        const titleMatch = prompt.title?.toLowerCase().includes(searchText);\n        const descriptionMatch = prompt.description?.toLowerCase().includes(searchText);\n        const tagMatch = prompt.tags?.some(tag => tag.label?.toLowerCase().includes(searchText));\n        return titleMatch || descriptionMatch || tagMatch;\n      });\n      this.currentPage = 1;\n      this.updateDisplayedPrompts();\n    }\n    static ɵfac = function PromptsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PromptsComponent)(i0.ɵɵdirectiveInject(i1.PaginationService), i0.ɵɵdirectiveInject(i2.PromptsService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i4.FormBuilder));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PromptsComponent,\n      selectors: [[\"app-prompts\"]],\n      decls: 13,\n      vars: 10,\n      consts: [[\"id\", \"prompts-container\", 1, \"container-fluid\"], [\"id\", \"search-filter-container\", 1, \"row\", \"g-3\"], [1, \"col-12\", \"col-md-8\", \"col-lg-9\", \"col-xl-10\", \"search-section\"], [3, \"formGroup\"], [\"placeholder\", \"Search \\\"Prompts\\\"\", \"hoverEffect\", \"glow\", \"pressedEffect\", \"solid\", \"formControlName\", \"search\"], [\"slot\", \"icon-start\", \"iconName\", \"search\", \"iconColor\", \"var(--color-brand-primary)\", 3, \"iconSize\"], [1, \"col-12\", \"col-md-4\", \"col-lg-3\", \"col-xl-2\", \"action-buttons\"], [\"dropdownTitle\", \"choose prompt\", 3, \"selectionChange\", \"options\"], [\"id\", \"prompts-card-container\", 1, \"row\", \"g-3\"], [\"iconColor\", \"#144692\", 1, \"col-12\", \"col-sm-6\", \"col-md-4\", \"col-lg-3\", \"col-xl-3\", \"col-xxl-2\", \"mt-5\", 3, \"cardClick\", \"type\", \"iconName\", \"title\", \"isLoading\"], [\"class\", \"col-12 d-flex justify-content-center align-items-center py-5\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"row\", 4, \"ngIf\"], [1, \"col-12\", \"d-flex\", \"justify-content-center\", \"align-items-center\", \"py-5\"], [1, \"text-center\"], [1, \"text-muted\"], [\"categoryIcon\", \"plus\", \"categoryTitle\", \"Prompts\", \"categoryValue\", \"42\", 1, \"col-12\", \"col-sm-6\", \"col-md-4\", \"col-lg-3\", \"col-xl-3\", \"col-xxl-2\", \"mt-5\", 3, \"actionClick\", \"title\", \"description\", \"author\", \"date\", \"actions\", \"skeleton\"], [1, \"row\"], [1, \"col-12\", \"d-flex\", \"justify-content-center\", \"mt-4\"], [3, \"pageChange\", \"totalItems\", \"currentPage\", \"itemsPerPage\"]],\n      template: function PromptsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"form\", 3)(4, \"ava-textbox\", 4);\n          i0.ɵɵelement(5, \"ava-icon\", 5);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(6, \"div\", 6)(7, \"ava-dropdown\", 7);\n          i0.ɵɵlistener(\"selectionChange\", function PromptsComponent_Template_ava_dropdown_selectionChange_7_listener($event) {\n            return ctx.onSelectionChange($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(8, \"div\", 8)(9, \"ava-text-card\", 9);\n          i0.ɵɵlistener(\"cardClick\", function PromptsComponent_Template_ava_text_card_cardClick_9_listener() {\n            return ctx.onCreatePrompt();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(10, PromptsComponent_div_10_Template, 4, 1, \"div\", 10)(11, PromptsComponent_ng_container_11_Template, 3, 8, \"ng-container\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(12, PromptsComponent_div_12_Template, 3, 3, \"div\", 12);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"formGroup\", ctx.searchForm);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"iconSize\", 16);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"options\", ctx.simpleOptions);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"type\", \"create\")(\"iconName\", \"plus\")(\"title\", ctx.promptLabels.createPrompt)(\"isLoading\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.filteredPrompts.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.isLoading && ctx.displayedPrompts.length === 0 ? ctx.cardSkeletonPlaceholders : ctx.displayedPrompts);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.filteredPrompts.length > 0);\n        }\n      },\n      dependencies: [CommonModule, i5.NgForOf, i5.NgIf, PageFooterComponent, TextCardComponent, AvaTextboxComponent, DropdownComponent, LucideAngularModule, IconComponent, ReactiveFormsModule, i4.ɵNgNoValidate, i4.NgControlStatus, i4.NgControlStatusGroup, i4.FormGroupDirective, i4.FormControlName, ConsoleCardComponent, TimeAgoPipe],\n      styles: [\".ava-dropdown {\\n  width: 100% !important;\\n}\\n\\n.mt-5[_ngcontent-%COMP%] {\\n  margin-top: 2rem;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3Byb2plY3RzL3NoYXJlZC9wYWdlcy9saWJyYXJpZXMvcHJvbXB0cy9wcm9tcHRzLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0Usc0JBQUE7QUFDRjs7QUFFQTtFQUNFLGdCQUFBO0FBQ0YiLCJzb3VyY2VzQ29udGVudCI6WyI6Om5nLWRlZXAgLmF2YS1kcm9wZG93biB7XHJcbiAgd2lkdGg6IDEwMCUgIWltcG9ydGFudDtcclxufVxyXG5cclxuLm10LTUge1xyXG4gIG1hcmdpbi10b3A6IDJyZW07XHJcbn1cclxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n  return PromptsComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "takeUntil", "of", "map", "catchError", "Subject", "debounceTime", "distinctUntilChanged", "startWith", "PageFooterComponent", "formatToDisplayDate", "prompts<PERSON><PERSON><PERSON>", "PROMPTS_BASE_ACTIONS", "AvaTextboxComponent", "TextCardComponent", "DropdownComponent", "IconComponent", "LucideAngularModule", "ReactiveFormsModule", "ConsoleCardComponent", "TimeAgoPipe", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "prompt<PERSON><PERSON><PERSON>", "noResults", "ɵɵelementContainerStart", "ɵɵlistener", "PromptsComponent_ng_container_11_Template_ava_console_card_actionClick_1_listener", "$event", "prompt_r3", "ɵɵrestoreView", "_r2", "$implicit", "ɵɵnextContext", "ɵɵresetView", "onActionClick", "id", "ɵɵproperty", "title", "description", "owner", "ɵɵpipeBind1", "createdDate", "defaultActions", "isLoading", "PromptsComponent_div_12_Template_app_page_footer_pageChange_2_listener", "_r4", "onPageChange", "filteredPrompts", "length", "currentPage", "itemsPerPage", "PromptsComponent", "paginationService", "promptsService", "router", "route", "fb", "showSuccessPopup", "popupMessage", "popupTitle", "iconName", "submissionSuccess", "showDeletePopup", "promptToDelete", "simpleOptions", "name", "value", "icon", "label", "tooltip", "isPrimary", "labels", "allPrompts", "displayedPrompts", "searchForm", "totalPages", "destroy$", "selectedData", "cardSkeletonPlaceholders", "Array", "constructor", "group", "search", "ngOnInit", "initSearchListener", "fetchAllPrompts", "pipe", "transformResponseToCardData", "bind", "error", "console", "prompts", "subscribe", "next", "updateDisplayedPrompts", "setInitialPageFromQueryParam", "err", "message", "complete", "onSelectionChange", "data", "onCreatePrompt", "navigate", "getHeaderIcons", "prompt", "toolType", "userCount", "getFooterIcons", "page", "relativeTo", "queryParams", "queryParamsHandling", "event", "promptId", "actionId", "deletePrompt", "executePrompt", "viewPrompt", "view", "returnPage", "response", "formatPromptCard", "item", "updatedAt", "categoryName", "domainName", "tags", "rest", "customTags", "getCustomTags", "allTags", "tagSummary", "getTagSummary", "actions", "find", "p", "onConfirmDelete", "res", "success", "filter", "closeDeletePopup", "onSuccessConfirm", "closeSuccessPopup", "execute", "copyPrompt", "_this", "_asyncToGenerator", "navigator", "clipboard", "writeText", "JSON", "stringify", "push", "type", "tag", "join", "pageParam", "snapshot", "queryParamMap", "get", "parseInt", "isNaN", "valueChanges", "toLowerCase", "searchText", "filterPrompts", "displayedItems", "getPaginatedItems", "titleMatch", "includes", "descriptionMatch", "tagMatch", "some", "ɵɵdirectiveInject", "i1", "PaginationService", "i2", "PromptsService", "i3", "Router", "ActivatedRoute", "i4", "FormBuilder", "selectors", "decls", "vars", "consts", "template", "PromptsComponent_Template", "rf", "ctx", "ɵɵelement", "PromptsComponent_Template_ava_dropdown_selectionChange_7_listener", "PromptsComponent_Template_ava_text_card_cardClick_9_listener", "ɵɵtemplate", "PromptsComponent_div_10_Template", "PromptsComponent_ng_container_11_Template", "PromptsComponent_div_12_Template", "createPrompt", "i5", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "ɵNgNoValidate", "NgControlStatus", "NgControlStatusGroup", "FormGroupDirective", "FormControlName", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\pages\\libraries\\prompts\\prompts.component.ts", "C:\\console\\aava-ui-web\\projects\\shared\\pages\\libraries\\prompts\\prompts.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { Router, ActivatedRoute } from '@angular/router';\r\nimport {\r\n  takeUntil,\r\n  of,\r\n  map,\r\n  catchError,\r\n  Subject,\r\n  debounceTime,\r\n  distinctUntilChanged,\r\n  startWith,\r\n} from 'rxjs';\r\nimport { CardData, CardTag } from '@shared/models/card.model';\r\nimport { PageFooterComponent } from '@shared/components/page-footer/page-footer.component';\r\nimport { PaginationService } from '@shared/services/pagination.service';\r\nimport { PromptsService } from '@shared/services/prompts.service';\r\nimport { formatToDisplayDate } from '@shared/utils/date-utils';\r\nimport promptsLabels from './constants/prompts.json';\r\nimport { PROMPTS_BASE_ACTIONS } from './prompts-actions';\r\nimport {\r\n  AvaTextboxComponent,\r\n  TextCardComponent,\r\n  DropdownComponent,\r\n  DropdownOption,\r\n  IconComponent,\r\n  PopupComponent,\r\n} from '@ava/play-comp-library';\r\nimport { LucideAngularModule } from 'lucide-angular';\r\nimport { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';\r\nimport {\r\n  ConsoleCardAction,\r\n  ConsoleCardComponent,\r\n} from '@shared/components/console-card/console-card.component';\r\nimport { TimeAgoPipe } from '@shared/pipes/time-ago.pipe';\r\n\r\n@Component({\r\n  selector: 'app-prompts',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    PageFooterComponent,\r\n    TextCardComponent,\r\n    AvaTextboxComponent,\r\n    DropdownComponent,\r\n    LucideAngularModule,\r\n    IconComponent,\r\n    PopupComponent,\r\n    ReactiveFormsModule,\r\n    ConsoleCardComponent,\r\n    TimeAgoPipe,\r\n  ],\r\n  templateUrl: './prompts.component.html',\r\n  styleUrl: './prompts.component.scss',\r\n})\r\nexport class PromptsComponent implements OnInit {\r\n  // Popup state for success messages\r\n  showSuccessPopup = false;\r\n  popupMessage = '';\r\n  popupTitle = '';\r\n  iconName = 'info';\r\n  submissionSuccess = false;\r\n  // popup for delete confirmation\r\n  showDeletePopup: boolean = false;\r\n  promptToDelete: CardData | null = null;\r\n\r\n  simpleOptions: DropdownOption[] = [\r\n    { name: 'Option 1', value: '1' },\r\n    { name: 'Option 2', value: '2' },\r\n    { name: 'Option 3', value: '3' },\r\n    { name: 'Option 4', value: '4' },\r\n    { name: 'Option 5', value: '5' },\r\n  ];\r\n\r\n  defaultActions: ConsoleCardAction[] = [\r\n    {\r\n      id: 'delete',\r\n      icon: 'trash',\r\n      label: 'Delete item',\r\n      tooltip: 'Delete',\r\n    },\r\n    {\r\n      id: 'edit',\r\n      icon: 'edit',\r\n      label: 'Edit item',\r\n      tooltip: 'Edit',\r\n    },\r\n\r\n    {\r\n      id: 'view',\r\n      icon: 'eye',\r\n      label: 'View Prompts',\r\n      tooltip: 'View',\r\n      isPrimary: true,\r\n    },\r\n  ];\r\n  public promptLabels = promptsLabels.labels;\r\n  allPrompts: CardData[] = [];\r\n  filteredPrompts: CardData[] = [];\r\n  displayedPrompts: CardData[] = [];\r\n  searchForm!: FormGroup;\r\n  isLoading = false;\r\n  currentPage: number = 1;\r\n  itemsPerPage: number = 11;\r\n  totalPages: number = 1;\r\n  protected destroy$ = new Subject<void>();\r\n  selectedData: any = null;\r\n  cardSkeletonPlaceholders = Array(11);\r\n\r\n  constructor(\r\n    private paginationService: PaginationService,\r\n    private promptsService: PromptsService,\r\n    private router: Router,\r\n    private route: ActivatedRoute,\r\n    private fb: FormBuilder,\r\n  ) {\r\n    this.searchForm = this.fb.group({\r\n      search: [''],\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.isLoading = true;\r\n    this.initSearchListener();\r\n    this.promptsService\r\n      .fetchAllPrompts()\r\n      .pipe(\r\n        takeUntil(this.destroy$),\r\n        map(this.transformResponseToCardData.bind(this)),\r\n        catchError((error) => {\r\n          console.error('Error fetching prompts:', error);\r\n          this.isLoading = false;\r\n          return of({ prompts: [] });\r\n        }),\r\n      )\r\n      .subscribe({\r\n        next: ({ prompts }) => {\r\n          this.allPrompts = prompts;\r\n          this.filteredPrompts = [...prompts];\r\n          this.updateDisplayedPrompts();\r\n          this.setInitialPageFromQueryParam();\r\n        },\r\n        error: (err) => console.error('Subscription error:', err.message),\r\n        complete: () => {\r\n          this.isLoading = false;\r\n        },\r\n      });\r\n  }\r\n\r\n  onSelectionChange(data: any) {\r\n    this.selectedData = data;\r\n  }\r\n\r\n  onCreatePrompt(): void {\r\n    this.router.navigate(['/libraries/prompts/create']);\r\n  }\r\n\r\n  getHeaderIcons(prompt: any): { iconName: string; title: string }[] {\r\n    return [\r\n      { iconName: 'NotebookText', title: prompt.toolType || 'Prompt' },\r\n      { iconName: 'users', title: `${prompt.userCount || 30}` },\r\n    ];\r\n  }\r\n\r\n  getFooterIcons(prompt: any): { iconName: string; title: string }[] {\r\n    return [\r\n      { iconName: 'user', title: prompt.owner || 'AAVA' },\r\n      { iconName: 'calendar-days', title: prompt.createdDate },\r\n    ];\r\n  }\r\n\r\n  onPageChange(page: number): void {\r\n    this.currentPage = page;\r\n    this.updateDisplayedPrompts();\r\n    this.router.navigate([], {\r\n      relativeTo: this.route,\r\n      queryParams: { page: this.currentPage },\r\n      queryParamsHandling: 'merge',\r\n    });\r\n  }\r\n\r\n  onActionClick(\r\n    event: { actionId: string; action: ConsoleCardAction },\r\n    promptId: string,\r\n  ): void {\r\n    switch (event.actionId) {\r\n      case 'delete':\r\n        this.deletePrompt(promptId);\r\n        break;\r\n      case 'edit':\r\n        this.executePrompt(promptId);\r\n        break;\r\n      case 'view':\r\n        this.viewPrompt(promptId);\r\n        break;\r\n      default:\r\n        break;\r\n    }\r\n  }\r\n\r\n  private viewPrompt(promptId: string): void {\r\n    this.router.navigate(['/libraries/prompts/edit', promptId], {\r\n      queryParams: { view: 'true', returnPage: this.currentPage },\r\n    });\r\n  }\r\n\r\n  private transformResponseToCardData(\r\n    response: CardData[] | { prompts: CardData[] },\r\n  ): { prompts: (CardData & { tagSummary: string })[] } {\r\n    const prompts = 'prompts' in response ? response.prompts : response;\r\n    return {\r\n      prompts: prompts.map(this.formatPromptCard.bind(this)),\r\n    };\r\n  }\r\n\r\n  private formatPromptCard(item: any): CardData & { tagSummary: string } {\r\n    const {\r\n      name,\r\n      updatedAt,\r\n      categoryName,\r\n      domainName,\r\n      tags = [],\r\n      ...rest\r\n    } = item;\r\n    const customTags = this.getCustomTags(categoryName, domainName);\r\n    const allTags = [...tags, ...customTags];\r\n    const tagSummary = this.getTagSummary(allTags);\r\n    const createdDate = formatToDisplayDate(updatedAt);\r\n    return {\r\n      title: name,\r\n      createdDate,\r\n      tags: allTags,\r\n      tagSummary,\r\n      actions: PROMPTS_BASE_ACTIONS,\r\n      ...rest,\r\n    };\r\n  }\r\n\r\n  // Step 1: User clicks trash icon → open delete confirmation\r\n  private deletePrompt(promptId: string): void {\r\n    const prompt = this.allPrompts.find((p) => p.id === promptId);\r\n    if (!prompt) return;\r\n\r\n    this.promptToDelete = prompt;\r\n    this.showDeletePopup = true;\r\n  }\r\n\r\n  // Step 2: User confirms delete in popup\r\n  onConfirmDelete(): void {\r\n    if (!this.promptToDelete?.id) return;\r\n\r\n    const promptId = this.promptToDelete.id;\r\n\r\n    this.promptsService.deletePrompt(promptId).subscribe({\r\n      next: (res) => {\r\n        if (res && res.success !== false) {\r\n          // Update local prompt lists\r\n          this.allPrompts = this.allPrompts.filter((p) => p.id !== promptId);\r\n          this.filteredPrompts = this.filteredPrompts.filter(\r\n            (p) => p.id !== promptId,\r\n          );\r\n          this.updateDisplayedPrompts();\r\n\r\n          // Show success popup\r\n          this.iconName = 'check-circle';\r\n          this.popupTitle = 'Success';\r\n          this.popupMessage = 'Prompt deleted successfully.';\r\n          this.submissionSuccess = true;\r\n          this.showSuccessPopup = true;\r\n        } else {\r\n          this.iconName = 'alert-circle';\r\n          this.popupTitle = 'Error';\r\n          this.popupMessage = 'Failed to delete prompt.';\r\n          this.submissionSuccess = false;\r\n          this.showSuccessPopup = true;\r\n        }\r\n\r\n        this.closeDeletePopup();\r\n      },\r\n      error: (err) => {\r\n        console.error('Failed to delete prompt:', err);\r\n        this.iconName = 'alert-circle';\r\n        this.popupTitle = 'Error';\r\n        this.popupMessage = 'An unexpected error occurred.';\r\n        this.submissionSuccess = false;\r\n        this.showSuccessPopup = true;\r\n        this.closeDeletePopup();\r\n      },\r\n    });\r\n  }\r\n\r\n  // Step 3: User cancels or closes delete popup\r\n  closeDeletePopup(): void {\r\n    this.showDeletePopup = false;\r\n    this.promptToDelete = null;\r\n  }\r\n\r\n  // Success popup confirm handler\r\n  onSuccessConfirm(): void {\r\n    this.closeSuccessPopup();\r\n  }\r\n\r\n  // Close success popup manually or when user clicks close icon\r\n  closeSuccessPopup(): void {\r\n    this.showSuccessPopup = false;\r\n    this.popupTitle = '';\r\n    this.popupMessage = '';\r\n    this.iconName = 'info';\r\n  }\r\n\r\n  private executePrompt(promptId: string): void {\r\n    this.router.navigate(['/libraries/prompts/edit', promptId], {\r\n      queryParams: { execute: 'true', returnPage: this.currentPage },\r\n    });\r\n  }\r\n\r\n  private async copyPrompt(promptId: string): Promise<void> {\r\n    if (!promptId) return;\r\n    const prompt = this.allPrompts.find((p) => p.id === promptId);\r\n    if (prompt) {\r\n      try {\r\n        await navigator.clipboard.writeText(JSON.stringify(prompt, null, 2));\r\n      } catch (err) {\r\n        // Optionally handle error silently\r\n      }\r\n    }\r\n  }\r\n\r\n  private getCustomTags(categoryName?: string, domainName?: string): CardTag[] {\r\n    const tags: CardTag[] = [];\r\n    if (categoryName) tags.push({ label: categoryName, type: 'primary' });\r\n    if (domainName) tags.push({ label: domainName, type: 'secondary' });\r\n    return tags;\r\n  }\r\n\r\n  private getTagSummary(tags: CardTag[]): string {\r\n    return tags.map((tag) => tag.label).join(', ');\r\n  }\r\n\r\n  private setInitialPageFromQueryParam(): void {\r\n    const pageParam = this.route.snapshot.queryParamMap.get('page');\r\n    if (pageParam) {\r\n      const page = parseInt(pageParam, 10);\r\n      if (!isNaN(page)) this.currentPage = page;\r\n    }\r\n  }\r\n\r\n  private initSearchListener(): void {\r\n    this.searchForm\r\n      .get('search')!\r\n      .valueChanges.pipe(\r\n        startWith(''),\r\n        debounceTime(300),\r\n        distinctUntilChanged(),\r\n        map((value) => value?.toLowerCase() ?? ''),\r\n        takeUntil(this.destroy$),\r\n      )\r\n      .subscribe((searchText) => {\r\n        this.filterPrompts(searchText);\r\n      });\r\n  }\r\n\r\n  private updateDisplayedPrompts(): void {\r\n    this.itemsPerPage = this.currentPage === 1 ? 12 : 11;\r\n    const { displayedItems, totalPages } =\r\n      this.paginationService.getPaginatedItems(\r\n        this.filteredPrompts,\r\n        this.currentPage,\r\n        this.itemsPerPage,\r\n      );\r\n    this.displayedPrompts = displayedItems;\r\n    this.totalPages = totalPages;\r\n  }\r\n\r\n  private filterPrompts(searchText: string): void {\r\n    this.filteredPrompts = this.allPrompts.filter((prompt) => {\r\n      const titleMatch = prompt.title?.toLowerCase().includes(searchText);\r\n      const descriptionMatch = prompt.description\r\n        ?.toLowerCase()\r\n        .includes(searchText);\r\n      const tagMatch = prompt.tags?.some((tag) =>\r\n        tag.label?.toLowerCase().includes(searchText),\r\n      );\r\n      return titleMatch || descriptionMatch || tagMatch;\r\n    });\r\n    this.currentPage = 1;\r\n    this.updateDisplayedPrompts();\r\n  }\r\n}\r\n", "<div id=\"prompts-container\" class=\"container-fluid\">\r\n  <div id=\"search-filter-container\" class=\"row g-3\">\r\n    <div class=\"col-12 col-md-8 col-lg-9 col-xl-10 search-section\">\r\n      <form [formGroup]=\"searchForm\">\r\n        <ava-textbox\r\n          placeholder='Search \"Prompts\"'\r\n          hoverEffect=\"glow\"\r\n          pressedEffect=\"solid\"\r\n          formControlName=\"search\"\r\n        >\r\n          <ava-icon\r\n            slot=\"icon-start\"\r\n            iconName=\"search\"\r\n            [iconSize]=\"16\"\r\n            iconColor=\"var(--color-brand-primary)\"\r\n          >\r\n          </ava-icon>\r\n        </ava-textbox>\r\n      </form>\r\n    </div>\r\n    <div class=\"col-12 col-md-4 col-lg-3 col-xl-2 action-buttons\">\r\n      <ava-dropdown\r\n        dropdownTitle=\"choose prompt\"\r\n        [options]=\"simpleOptions\"\r\n        (selectionChange)=\"onSelectionChange($event)\"\r\n      >\r\n      </ava-dropdown>\r\n    </div>\r\n  </div>\r\n\r\n  <div id=\"prompts-card-container\" class=\"row g-3\">\r\n    <ava-text-card\r\n      class=\"col-12 col-sm-6 col-md-4 col-lg-3 col-xl-3 col-xxl-2 mt-5\"\r\n      [type]=\"'create'\"\r\n      [iconName]=\"'plus'\"\r\n      iconColor=\"#144692\"\r\n      [title]=\"promptLabels.createPrompt\"\r\n      (cardClick)=\"onCreatePrompt()\"\r\n      [isLoading]=\"isLoading\"\r\n    >\r\n    </ava-text-card>\r\n\r\n    <!-- No Results Message -->\r\n    <div\r\n      class=\"col-12 d-flex justify-content-center align-items-center py-5\"\r\n      *ngIf=\"!isLoading && filteredPrompts.length === 0\"\r\n    >\r\n      <div class=\"text-center\">\r\n        <h5 class=\"text-muted\">{{ promptLabels.noResults }}</h5>\r\n      </div>\r\n    </div>\r\n\r\n    <ng-container\r\n      *ngFor=\"\r\n        let prompt of isLoading && displayedPrompts.length === 0\r\n          ? cardSkeletonPlaceholders\r\n          : displayedPrompts\r\n      \"\r\n    >\r\n      <ava-console-card\r\n        class=\"col-12 col-sm-6 col-md-4 col-lg-3 col-xl-3 col-xxl-2 mt-5\"\r\n        [title]=\"prompt?.title\"\r\n        [description]=\"prompt?.description\"\r\n        categoryIcon=\"plus\"\r\n        categoryTitle=\"Prompts\"\r\n        categoryValue=\"42\"\r\n        [author]=\"prompt?.owner || 'AAVA'\"\r\n        [date]=\"prompt?.createdDate | timeAgo\"\r\n        [actions]=\"defaultActions\"\r\n        (actionClick)=\"onActionClick($event, prompt.id)\"\r\n        [skeleton]=\"isLoading\"\r\n      >\r\n      </ava-console-card>\r\n    </ng-container>\r\n  </div>\r\n\r\n  <!-- Pagination Footer -->\r\n  <div class=\"row\" *ngIf=\"filteredPrompts.length > 0\">\r\n    <div class=\"col-12 d-flex justify-content-center mt-4\">\r\n      <app-page-footer\r\n        [totalItems]=\"filteredPrompts.length + 1\"\r\n        [currentPage]=\"currentPage\"\r\n        [itemsPerPage]=\"itemsPerPage\"\r\n        (pageChange)=\"onPageChange($event)\"\r\n      ></app-page-footer>\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n\r\n"], "mappings": ";AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SACEC,SAAS,EACTC,EAAE,EACFC,GAAG,EACHC,UAAU,EACVC,OAAO,EACPC,YAAY,EACZC,oBAAoB,EACpBC,SAAS,QACJ,MAAM;AAEb,SAASC,mBAAmB,QAAQ,sDAAsD;AAG1F,SAASC,mBAAmB,QAAQ,0BAA0B;AAC9D,OAAOC,aAAa,MAAM,0BAA0B;AACpD,SAASC,oBAAoB,QAAQ,mBAAmB;AACxD,SACEC,mBAAmB,EACnBC,iBAAiB,EACjBC,iBAAiB,EAEjBC,aAAa,QAER,wBAAwB;AAC/B,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAAiCC,mBAAmB,QAAQ,gBAAgB;AAC5E,SAEEC,oBAAoB,QACf,wDAAwD;AAC/D,SAASC,WAAW,QAAQ,6BAA6B;;;;;;;;;ICcjDC,EALJ,CAAAC,cAAA,cAGC,cAC0B,aACA;IAAAD,EAAA,CAAAE,MAAA,GAA4B;IAEvDF,EAFuD,CAAAG,YAAA,EAAK,EACpD,EACF;;;;IAFqBH,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,YAAA,CAAAC,SAAA,CAA4B;;;;;;IAIvDR,EAAA,CAAAS,uBAAA,GAMC;IACCT,EAAA,CAAAC,cAAA,2BAYC;;IAFCD,EAAA,CAAAU,UAAA,yBAAAC,kFAAAC,MAAA;MAAA,MAAAC,SAAA,GAAAb,EAAA,CAAAc,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAV,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAeZ,MAAA,CAAAa,aAAA,CAAAP,MAAA,EAAAC,SAAA,CAAAO,EAAA,CAAgC;IAAA,EAAC;IAGlDpB,EAAA,CAAAG,YAAA,EAAmB;;;;;;IAXjBH,EAAA,CAAAI,SAAA,EAAuB;IASvBJ,EATA,CAAAqB,UAAA,UAAAR,SAAA,kBAAAA,SAAA,CAAAS,KAAA,CAAuB,gBAAAT,SAAA,kBAAAA,SAAA,CAAAU,WAAA,CACY,YAAAV,SAAA,kBAAAA,SAAA,CAAAW,KAAA,YAID,SAAAxB,EAAA,CAAAyB,WAAA,OAAAZ,SAAA,kBAAAA,SAAA,CAAAa,WAAA,EACI,YAAApB,MAAA,CAAAqB,cAAA,CACZ,aAAArB,MAAA,CAAAsB,SAAA,CAEJ;;;;;;IASxB5B,EAFJ,CAAAC,cAAA,cAAoD,cACK,0BAMpD;IADCD,EAAA,CAAAU,UAAA,wBAAAmB,uEAAAjB,MAAA;MAAAZ,EAAA,CAAAc,aAAA,CAAAgB,GAAA;MAAA,MAAAxB,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAcZ,MAAA,CAAAyB,YAAA,CAAAnB,MAAA,CAAoB;IAAA,EAAC;IAGzCZ,EAFK,CAAAG,YAAA,EAAkB,EACf,EACF;;;;IANAH,EAAA,CAAAI,SAAA,GAAyC;IAEzCJ,EAFA,CAAAqB,UAAA,eAAAf,MAAA,CAAA0B,eAAA,CAAAC,MAAA,KAAyC,gBAAA3B,MAAA,CAAA4B,WAAA,CACd,iBAAA5B,MAAA,CAAA6B,YAAA,CACE;;;AD3BrC,WAAaC,gBAAgB;EAAvB,MAAOA,gBAAgB;IAuDjBC,iBAAA;IACAC,cAAA;IACAC,MAAA;IACAC,KAAA;IACAC,EAAA;IA1DV;IACAC,gBAAgB,GAAG,KAAK;IACxBC,YAAY,GAAG,EAAE;IACjBC,UAAU,GAAG,EAAE;IACfC,QAAQ,GAAG,MAAM;IACjBC,iBAAiB,GAAG,KAAK;IACzB;IACAC,eAAe,GAAY,KAAK;IAChCC,cAAc,GAAoB,IAAI;IAEtCC,aAAa,GAAqB,CAChC;MAAEC,IAAI,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAG,CAAE,EAChC;MAAED,IAAI,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAG,CAAE,EAChC;MAAED,IAAI,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAG,CAAE,EAChC;MAAED,IAAI,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAG,CAAE,EAChC;MAAED,IAAI,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAG,CAAE,CACjC;IAEDxB,cAAc,GAAwB,CACpC;MACEP,EAAE,EAAE,QAAQ;MACZgC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,aAAa;MACpBC,OAAO,EAAE;KACV,EACD;MACElC,EAAE,EAAE,MAAM;MACVgC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,WAAW;MAClBC,OAAO,EAAE;KACV,EAED;MACElC,EAAE,EAAE,MAAM;MACVgC,IAAI,EAAE,KAAK;MACXC,KAAK,EAAE,cAAc;MACrBC,OAAO,EAAE,MAAM;MACfC,SAAS,EAAE;KACZ,CACF;IACMhD,YAAY,GAAGjB,aAAa,CAACkE,MAAM;IAC1CC,UAAU,GAAe,EAAE;IAC3BzB,eAAe,GAAe,EAAE;IAChC0B,gBAAgB,GAAe,EAAE;IACjCC,UAAU;IACV/B,SAAS,GAAG,KAAK;IACjBM,WAAW,GAAW,CAAC;IACvBC,YAAY,GAAW,EAAE;IACzByB,UAAU,GAAW,CAAC;IACZC,QAAQ,GAAG,IAAI7E,OAAO,EAAQ;IACxC8E,YAAY,GAAQ,IAAI;IACxBC,wBAAwB,GAAGC,KAAK,CAAC,EAAE,CAAC;IAEpCC,YACU5B,iBAAoC,EACpCC,cAA8B,EAC9BC,MAAc,EACdC,KAAqB,EACrBC,EAAe;MAJf,KAAAJ,iBAAiB,GAAjBA,iBAAiB;MACjB,KAAAC,cAAc,GAAdA,cAAc;MACd,KAAAC,MAAM,GAANA,MAAM;MACN,KAAAC,KAAK,GAALA,KAAK;MACL,KAAAC,EAAE,GAAFA,EAAE;MAEV,IAAI,CAACkB,UAAU,GAAG,IAAI,CAAClB,EAAE,CAACyB,KAAK,CAAC;QAC9BC,MAAM,EAAE,CAAC,EAAE;OACZ,CAAC;IACJ;IAEAC,QAAQA,CAAA;MACN,IAAI,CAACxC,SAAS,GAAG,IAAI;MACrB,IAAI,CAACyC,kBAAkB,EAAE;MACzB,IAAI,CAAC/B,cAAc,CAChBgC,eAAe,EAAE,CACjBC,IAAI,CACH3F,SAAS,CAAC,IAAI,CAACiF,QAAQ,CAAC,EACxB/E,GAAG,CAAC,IAAI,CAAC0F,2BAA2B,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC,EAChD1F,UAAU,CAAE2F,KAAK,IAAI;QACnBC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAAC9C,SAAS,GAAG,KAAK;QACtB,OAAO/C,EAAE,CAAC;UAAE+F,OAAO,EAAE;QAAE,CAAE,CAAC;MAC5B,CAAC,CAAC,CACH,CACAC,SAAS,CAAC;QACTC,IAAI,EAAEA,CAAC;UAAEF;QAAO,CAAE,KAAI;UACpB,IAAI,CAACnB,UAAU,GAAGmB,OAAO;UACzB,IAAI,CAAC5C,eAAe,GAAG,CAAC,GAAG4C,OAAO,CAAC;UACnC,IAAI,CAACG,sBAAsB,EAAE;UAC7B,IAAI,CAACC,4BAA4B,EAAE;QACrC,CAAC;QACDN,KAAK,EAAGO,GAAG,IAAKN,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEO,GAAG,CAACC,OAAO,CAAC;QACjEC,QAAQ,EAAEA,CAAA,KAAK;UACb,IAAI,CAACvD,SAAS,GAAG,KAAK;QACxB;OACD,CAAC;IACN;IAEAwD,iBAAiBA,CAACC,IAAS;MACzB,IAAI,CAACvB,YAAY,GAAGuB,IAAI;IAC1B;IAEAC,cAAcA,CAAA;MACZ,IAAI,CAAC/C,MAAM,CAACgD,QAAQ,CAAC,CAAC,2BAA2B,CAAC,CAAC;IACrD;IAEAC,cAAcA,CAACC,MAAW;MACxB,OAAO,CACL;QAAE5C,QAAQ,EAAE,cAAc;QAAEvB,KAAK,EAAEmE,MAAM,CAACC,QAAQ,IAAI;MAAQ,CAAE,EAChE;QAAE7C,QAAQ,EAAE,OAAO;QAAEvB,KAAK,EAAE,GAAGmE,MAAM,CAACE,SAAS,IAAI,EAAE;MAAE,CAAE,CAC1D;IACH;IAEAC,cAAcA,CAACH,MAAW;MACxB,OAAO,CACL;QAAE5C,QAAQ,EAAE,MAAM;QAAEvB,KAAK,EAAEmE,MAAM,CAACjE,KAAK,IAAI;MAAM,CAAE,EACnD;QAAEqB,QAAQ,EAAE,eAAe;QAAEvB,KAAK,EAAEmE,MAAM,CAAC/D;MAAW,CAAE,CACzD;IACH;IAEAK,YAAYA,CAAC8D,IAAY;MACvB,IAAI,CAAC3D,WAAW,GAAG2D,IAAI;MACvB,IAAI,CAACd,sBAAsB,EAAE;MAC7B,IAAI,CAACxC,MAAM,CAACgD,QAAQ,CAAC,EAAE,EAAE;QACvBO,UAAU,EAAE,IAAI,CAACtD,KAAK;QACtBuD,WAAW,EAAE;UAAEF,IAAI,EAAE,IAAI,CAAC3D;QAAW,CAAE;QACvC8D,mBAAmB,EAAE;OACtB,CAAC;IACJ;IAEA7E,aAAaA,CACX8E,KAAsD,EACtDC,QAAgB;MAEhB,QAAQD,KAAK,CAACE,QAAQ;QACpB,KAAK,QAAQ;UACX,IAAI,CAACC,YAAY,CAACF,QAAQ,CAAC;UAC3B;QACF,KAAK,MAAM;UACT,IAAI,CAACG,aAAa,CAACH,QAAQ,CAAC;UAC5B;QACF,KAAK,MAAM;UACT,IAAI,CAACI,UAAU,CAACJ,QAAQ,CAAC;UACzB;QACF;UACE;MACJ;IACF;IAEQI,UAAUA,CAACJ,QAAgB;MACjC,IAAI,CAAC3D,MAAM,CAACgD,QAAQ,CAAC,CAAC,yBAAyB,EAAEW,QAAQ,CAAC,EAAE;QAC1DH,WAAW,EAAE;UAAEQ,IAAI,EAAE,MAAM;UAAEC,UAAU,EAAE,IAAI,CAACtE;QAAW;OAC1D,CAAC;IACJ;IAEQsC,2BAA2BA,CACjCiC,QAA8C;MAE9C,MAAM7B,OAAO,GAAG,SAAS,IAAI6B,QAAQ,GAAGA,QAAQ,CAAC7B,OAAO,GAAG6B,QAAQ;MACnE,OAAO;QACL7B,OAAO,EAAEA,OAAO,CAAC9F,GAAG,CAAC,IAAI,CAAC4H,gBAAgB,CAACjC,IAAI,CAAC,IAAI,CAAC;OACtD;IACH;IAEQiC,gBAAgBA,CAACC,IAAS;MAChC,MAAM;QACJzD,IAAI;QACJ0D,SAAS;QACTC,YAAY;QACZC,UAAU;QACVC,IAAI,GAAG,EAAE;QACT,GAAGC;MAAI,CACR,GAAGL,IAAI;MACR,MAAMM,UAAU,GAAG,IAAI,CAACC,aAAa,CAACL,YAAY,EAAEC,UAAU,CAAC;MAC/D,MAAMK,OAAO,GAAG,CAAC,GAAGJ,IAAI,EAAE,GAAGE,UAAU,CAAC;MACxC,MAAMG,UAAU,GAAG,IAAI,CAACC,aAAa,CAACF,OAAO,CAAC;MAC9C,MAAMzF,WAAW,GAAGrC,mBAAmB,CAACuH,SAAS,CAAC;MAClD,OAAO;QACLtF,KAAK,EAAE4B,IAAI;QACXxB,WAAW;QACXqF,IAAI,EAAEI,OAAO;QACbC,UAAU;QACVE,OAAO,EAAE/H,oBAAoB;QAC7B,GAAGyH;OACJ;IACH;IAEA;IACQZ,YAAYA,CAACF,QAAgB;MACnC,MAAMT,MAAM,GAAG,IAAI,CAAChC,UAAU,CAAC8D,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACpG,EAAE,KAAK8E,QAAQ,CAAC;MAC7D,IAAI,CAACT,MAAM,EAAE;MAEb,IAAI,CAACzC,cAAc,GAAGyC,MAAM;MAC5B,IAAI,CAAC1C,eAAe,GAAG,IAAI;IAC7B;IAEA;IACA0E,eAAeA,CAAA;MACb,IAAI,CAAC,IAAI,CAACzE,cAAc,EAAE5B,EAAE,EAAE;MAE9B,MAAM8E,QAAQ,GAAG,IAAI,CAAClD,cAAc,CAAC5B,EAAE;MAEvC,IAAI,CAACkB,cAAc,CAAC8D,YAAY,CAACF,QAAQ,CAAC,CAACrB,SAAS,CAAC;QACnDC,IAAI,EAAG4C,GAAG,IAAI;UACZ,IAAIA,GAAG,IAAIA,GAAG,CAACC,OAAO,KAAK,KAAK,EAAE;YAChC;YACA,IAAI,CAAClE,UAAU,GAAG,IAAI,CAACA,UAAU,CAACmE,MAAM,CAAEJ,CAAC,IAAKA,CAAC,CAACpG,EAAE,KAAK8E,QAAQ,CAAC;YAClE,IAAI,CAAClE,eAAe,GAAG,IAAI,CAACA,eAAe,CAAC4F,MAAM,CAC/CJ,CAAC,IAAKA,CAAC,CAACpG,EAAE,KAAK8E,QAAQ,CACzB;YACD,IAAI,CAACnB,sBAAsB,EAAE;YAE7B;YACA,IAAI,CAAClC,QAAQ,GAAG,cAAc;YAC9B,IAAI,CAACD,UAAU,GAAG,SAAS;YAC3B,IAAI,CAACD,YAAY,GAAG,8BAA8B;YAClD,IAAI,CAACG,iBAAiB,GAAG,IAAI;YAC7B,IAAI,CAACJ,gBAAgB,GAAG,IAAI;UAC9B,CAAC,MAAM;YACL,IAAI,CAACG,QAAQ,GAAG,cAAc;YAC9B,IAAI,CAACD,UAAU,GAAG,OAAO;YACzB,IAAI,CAACD,YAAY,GAAG,0BAA0B;YAC9C,IAAI,CAACG,iBAAiB,GAAG,KAAK;YAC9B,IAAI,CAACJ,gBAAgB,GAAG,IAAI;UAC9B;UAEA,IAAI,CAACmF,gBAAgB,EAAE;QACzB,CAAC;QACDnD,KAAK,EAAGO,GAAG,IAAI;UACbN,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEO,GAAG,CAAC;UAC9C,IAAI,CAACpC,QAAQ,GAAG,cAAc;UAC9B,IAAI,CAACD,UAAU,GAAG,OAAO;UACzB,IAAI,CAACD,YAAY,GAAG,+BAA+B;UACnD,IAAI,CAACG,iBAAiB,GAAG,KAAK;UAC9B,IAAI,CAACJ,gBAAgB,GAAG,IAAI;UAC5B,IAAI,CAACmF,gBAAgB,EAAE;QACzB;OACD,CAAC;IACJ;IAEA;IACAA,gBAAgBA,CAAA;MACd,IAAI,CAAC9E,eAAe,GAAG,KAAK;MAC5B,IAAI,CAACC,cAAc,GAAG,IAAI;IAC5B;IAEA;IACA8E,gBAAgBA,CAAA;MACd,IAAI,CAACC,iBAAiB,EAAE;IAC1B;IAEA;IACAA,iBAAiBA,CAAA;MACf,IAAI,CAACrF,gBAAgB,GAAG,KAAK;MAC7B,IAAI,CAACE,UAAU,GAAG,EAAE;MACpB,IAAI,CAACD,YAAY,GAAG,EAAE;MACtB,IAAI,CAACE,QAAQ,GAAG,MAAM;IACxB;IAEQwD,aAAaA,CAACH,QAAgB;MACpC,IAAI,CAAC3D,MAAM,CAACgD,QAAQ,CAAC,CAAC,yBAAyB,EAAEW,QAAQ,CAAC,EAAE;QAC1DH,WAAW,EAAE;UAAEiC,OAAO,EAAE,MAAM;UAAExB,UAAU,EAAE,IAAI,CAACtE;QAAW;OAC7D,CAAC;IACJ;IAEc+F,UAAUA,CAAC/B,QAAgB;MAAA,IAAAgC,KAAA;MAAA,OAAAC,iBAAA;QACvC,IAAI,CAACjC,QAAQ,EAAE;QACf,MAAMT,MAAM,GAAGyC,KAAI,CAACzE,UAAU,CAAC8D,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACpG,EAAE,KAAK8E,QAAQ,CAAC;QAC7D,IAAIT,MAAM,EAAE;UACV,IAAI;YACF,MAAM2C,SAAS,CAACC,SAAS,CAACC,SAAS,CAACC,IAAI,CAACC,SAAS,CAAC/C,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;UACtE,CAAC,CAAC,OAAOR,GAAG,EAAE;YACZ;UAAA;QAEJ;MAAC;IACH;IAEQiC,aAAaA,CAACL,YAAqB,EAAEC,UAAmB;MAC9D,MAAMC,IAAI,GAAc,EAAE;MAC1B,IAAIF,YAAY,EAAEE,IAAI,CAAC0B,IAAI,CAAC;QAAEpF,KAAK,EAAEwD,YAAY;QAAE6B,IAAI,EAAE;MAAS,CAAE,CAAC;MACrE,IAAI5B,UAAU,EAAEC,IAAI,CAAC0B,IAAI,CAAC;QAAEpF,KAAK,EAAEyD,UAAU;QAAE4B,IAAI,EAAE;MAAW,CAAE,CAAC;MACnE,OAAO3B,IAAI;IACb;IAEQM,aAAaA,CAACN,IAAe;MACnC,OAAOA,IAAI,CAACjI,GAAG,CAAE6J,GAAG,IAAKA,GAAG,CAACtF,KAAK,CAAC,CAACuF,IAAI,CAAC,IAAI,CAAC;IAChD;IAEQ5D,4BAA4BA,CAAA;MAClC,MAAM6D,SAAS,GAAG,IAAI,CAACrG,KAAK,CAACsG,QAAQ,CAACC,aAAa,CAACC,GAAG,CAAC,MAAM,CAAC;MAC/D,IAAIH,SAAS,EAAE;QACb,MAAMhD,IAAI,GAAGoD,QAAQ,CAACJ,SAAS,EAAE,EAAE,CAAC;QACpC,IAAI,CAACK,KAAK,CAACrD,IAAI,CAAC,EAAE,IAAI,CAAC3D,WAAW,GAAG2D,IAAI;MAC3C;IACF;IAEQxB,kBAAkBA,CAAA;MACxB,IAAI,CAACV,UAAU,CACZqF,GAAG,CAAC,QAAQ,CAAE,CACdG,YAAY,CAAC5E,IAAI,CAChBpF,SAAS,CAAC,EAAE,CAAC,EACbF,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBJ,GAAG,CAAEqE,KAAK,IAAKA,KAAK,EAAEiG,WAAW,EAAE,IAAI,EAAE,CAAC,EAC1CxK,SAAS,CAAC,IAAI,CAACiF,QAAQ,CAAC,CACzB,CACAgB,SAAS,CAAEwE,UAAU,IAAI;QACxB,IAAI,CAACC,aAAa,CAACD,UAAU,CAAC;MAChC,CAAC,CAAC;IACN;IAEQtE,sBAAsBA,CAAA;MAC5B,IAAI,CAAC5C,YAAY,GAAG,IAAI,CAACD,WAAW,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE;MACpD,MAAM;QAAEqH,cAAc;QAAE3F;MAAU,CAAE,GAClC,IAAI,CAACvB,iBAAiB,CAACmH,iBAAiB,CACtC,IAAI,CAACxH,eAAe,EACpB,IAAI,CAACE,WAAW,EAChB,IAAI,CAACC,YAAY,CAClB;MACH,IAAI,CAACuB,gBAAgB,GAAG6F,cAAc;MACtC,IAAI,CAAC3F,UAAU,GAAGA,UAAU;IAC9B;IAEQ0F,aAAaA,CAACD,UAAkB;MACtC,IAAI,CAACrH,eAAe,GAAG,IAAI,CAACyB,UAAU,CAACmE,MAAM,CAAEnC,MAAM,IAAI;QACvD,MAAMgE,UAAU,GAAGhE,MAAM,CAACnE,KAAK,EAAE8H,WAAW,EAAE,CAACM,QAAQ,CAACL,UAAU,CAAC;QACnE,MAAMM,gBAAgB,GAAGlE,MAAM,CAAClE,WAAW,EACvC6H,WAAW,EAAE,CACdM,QAAQ,CAACL,UAAU,CAAC;QACvB,MAAMO,QAAQ,GAAGnE,MAAM,CAACsB,IAAI,EAAE8C,IAAI,CAAElB,GAAG,IACrCA,GAAG,CAACtF,KAAK,EAAE+F,WAAW,EAAE,CAACM,QAAQ,CAACL,UAAU,CAAC,CAC9C;QACD,OAAOI,UAAU,IAAIE,gBAAgB,IAAIC,QAAQ;MACnD,CAAC,CAAC;MACF,IAAI,CAAC1H,WAAW,GAAG,CAAC;MACpB,IAAI,CAAC6C,sBAAsB,EAAE;IAC/B;;uCA5UW3C,gBAAgB,EAAApC,EAAA,CAAA8J,iBAAA,CAAAC,EAAA,CAAAC,iBAAA,GAAAhK,EAAA,CAAA8J,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAlK,EAAA,CAAA8J,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAApK,EAAA,CAAA8J,iBAAA,CAAAK,EAAA,CAAAE,cAAA,GAAArK,EAAA,CAAA8J,iBAAA,CAAAQ,EAAA,CAAAC,WAAA;IAAA;;YAAhBnI,gBAAgB;MAAAoI,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCnDrB9K,EAJR,CAAAC,cAAA,aAAoD,aACA,aACe,cAC9B,qBAM5B;UACCD,EAAA,CAAAgL,SAAA,kBAMW;UAGjBhL,EAFI,CAAAG,YAAA,EAAc,EACT,EACH;UAEJH,EADF,CAAAC,cAAA,aAA8D,sBAK3D;UADCD,EAAA,CAAAU,UAAA,6BAAAuK,kEAAArK,MAAA;YAAA,OAAmBmK,GAAA,CAAA3F,iBAAA,CAAAxE,MAAA,CAAyB;UAAA,EAAC;UAInDZ,EAFI,CAAAG,YAAA,EAAe,EACX,EACF;UAGJH,EADF,CAAAC,cAAA,aAAiD,uBAS9C;UAFCD,EAAA,CAAAU,UAAA,uBAAAwK,6DAAA;YAAA,OAAaH,GAAA,CAAAzF,cAAA,EAAgB;UAAA,EAAC;UAGhCtF,EAAA,CAAAG,YAAA,EAAgB;UAYhBH,EATA,CAAAmL,UAAA,KAAAC,gCAAA,kBAGC,KAAAC,yCAAA,2BAYA;UAgBHrL,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAmL,UAAA,KAAAG,gCAAA,kBAAoD;UAUtDtL,EAAA,CAAAG,YAAA,EAAM;;;UApFMH,EAAA,CAAAI,SAAA,GAAwB;UAAxBJ,EAAA,CAAAqB,UAAA,cAAA0J,GAAA,CAAApH,UAAA,CAAwB;UAUxB3D,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAAqB,UAAA,gBAAe;UAUnBrB,EAAA,CAAAI,SAAA,GAAyB;UAAzBJ,EAAA,CAAAqB,UAAA,YAAA0J,GAAA,CAAA9H,aAAA,CAAyB;UAU3BjD,EAAA,CAAAI,SAAA,GAAiB;UAKjBJ,EALA,CAAAqB,UAAA,kBAAiB,oBACE,UAAA0J,GAAA,CAAAxK,YAAA,CAAAgL,YAAA,CAEgB,cAAAR,GAAA,CAAAnJ,SAAA,CAEZ;UAOtB5B,EAAA,CAAAI,SAAA,EAAgD;UAAhDJ,EAAA,CAAAqB,UAAA,UAAA0J,GAAA,CAAAnJ,SAAA,IAAAmJ,GAAA,CAAA/I,eAAA,CAAAC,MAAA,OAAgD;UAS3BjC,EAAA,CAAAI,SAAA,EAGvB;UAHuBJ,EAAA,CAAAqB,UAAA,YAAA0J,GAAA,CAAAnJ,SAAA,IAAAmJ,GAAA,CAAArH,gBAAA,CAAAzB,MAAA,SAAA8I,GAAA,CAAAhH,wBAAA,GAAAgH,GAAA,CAAArH,gBAAA,CAGvB;UAoBe1D,EAAA,CAAAI,SAAA,EAAgC;UAAhCJ,EAAA,CAAAqB,UAAA,SAAA0J,GAAA,CAAA/I,eAAA,CAAAC,MAAA,KAAgC;;;qBDrChDtD,YAAY,EAAA6M,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZtM,mBAAmB,EACnBK,iBAAiB,EACjBD,mBAAmB,EACnBE,iBAAiB,EACjBE,mBAAmB,EACnBD,aAAa,EAEbE,mBAAmB,EAAAyK,EAAA,CAAAqB,aAAA,EAAArB,EAAA,CAAAsB,eAAA,EAAAtB,EAAA,CAAAuB,oBAAA,EAAAvB,EAAA,CAAAwB,kBAAA,EAAAxB,EAAA,CAAAyB,eAAA,EACnBjM,oBAAoB,EACpBC,WAAW;MAAAiM,MAAA;IAAA;;SAKF5J,gBAAgB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}