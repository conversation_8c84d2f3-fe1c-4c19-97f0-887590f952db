{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { VSBuffer } from '../../../base/common/buffer.js';\nimport * as platform from '../../../base/common/platform.js';\nfunction reverseEndianness(arr) {\n  for (let i = 0, len = arr.length; i < len; i += 4) {\n    // flip bytes 0<->3 and 1<->2\n    const b0 = arr[i + 0];\n    const b1 = arr[i + 1];\n    const b2 = arr[i + 2];\n    const b3 = arr[i + 3];\n    arr[i + 0] = b3;\n    arr[i + 1] = b2;\n    arr[i + 2] = b1;\n    arr[i + 3] = b0;\n  }\n}\nfunction toLittleEndianBuffer(arr) {\n  const uint8Arr = new Uint8Array(arr.buffer, arr.byteOffset, arr.length * 4);\n  if (!platform.isLittleEndian()) {\n    // the byte order must be changed\n    reverseEndianness(uint8Arr);\n  }\n  return VSBuffer.wrap(uint8Arr);\n}\nexport function encodeSemanticTokensDto(semanticTokens) {\n  const dest = new Uint32Array(encodeSemanticTokensDtoSize(semanticTokens));\n  let offset = 0;\n  dest[offset++] = semanticTokens.id;\n  if (semanticTokens.type === 'full') {\n    dest[offset++] = 1 /* EncodedSemanticTokensType.Full */;\n    dest[offset++] = semanticTokens.data.length;\n    dest.set(semanticTokens.data, offset);\n    offset += semanticTokens.data.length;\n  } else {\n    dest[offset++] = 2 /* EncodedSemanticTokensType.Delta */;\n    dest[offset++] = semanticTokens.deltas.length;\n    for (const delta of semanticTokens.deltas) {\n      dest[offset++] = delta.start;\n      dest[offset++] = delta.deleteCount;\n      if (delta.data) {\n        dest[offset++] = delta.data.length;\n        dest.set(delta.data, offset);\n        offset += delta.data.length;\n      } else {\n        dest[offset++] = 0;\n      }\n    }\n  }\n  return toLittleEndianBuffer(dest);\n}\nfunction encodeSemanticTokensDtoSize(semanticTokens) {\n  let result = 0;\n  result += +1 // id\n  + 1 // type\n  ;\n  if (semanticTokens.type === 'full') {\n    result += +1 // data length\n    + semanticTokens.data.length;\n  } else {\n    result += +1 // delta count\n    ;\n    result += (+1 // start\n    + 1 // deleteCount\n    + 1 // data length\n    ) * semanticTokens.deltas.length;\n    for (const delta of semanticTokens.deltas) {\n      if (delta.data) {\n        result += delta.data.length;\n      }\n    }\n  }\n  return result;\n}", "map": {"version": 3, "names": ["VSBuffer", "platform", "reverseEndianness", "arr", "i", "len", "length", "b0", "b1", "b2", "b3", "to<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uint8Arr", "Uint8Array", "buffer", "byteOffset", "isLittleEndian", "wrap", "encodeSemanticTokensDto", "semanticTokens", "dest", "Uint32Array", "encodeSemanticTokensDtoSize", "offset", "id", "type", "data", "set", "deltas", "delta", "start", "deleteCount", "result"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/services/semanticTokensDto.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { VSBuffer } from '../../../base/common/buffer.js';\nimport * as platform from '../../../base/common/platform.js';\nfunction reverseEndianness(arr) {\n    for (let i = 0, len = arr.length; i < len; i += 4) {\n        // flip bytes 0<->3 and 1<->2\n        const b0 = arr[i + 0];\n        const b1 = arr[i + 1];\n        const b2 = arr[i + 2];\n        const b3 = arr[i + 3];\n        arr[i + 0] = b3;\n        arr[i + 1] = b2;\n        arr[i + 2] = b1;\n        arr[i + 3] = b0;\n    }\n}\nfunction toLittleEndianBuffer(arr) {\n    const uint8Arr = new Uint8Array(arr.buffer, arr.byteOffset, arr.length * 4);\n    if (!platform.isLittleEndian()) {\n        // the byte order must be changed\n        reverseEndianness(uint8Arr);\n    }\n    return VSBuffer.wrap(uint8Arr);\n}\nexport function encodeSemanticTokensDto(semanticTokens) {\n    const dest = new Uint32Array(encodeSemanticTokensDtoSize(semanticTokens));\n    let offset = 0;\n    dest[offset++] = semanticTokens.id;\n    if (semanticTokens.type === 'full') {\n        dest[offset++] = 1 /* EncodedSemanticTokensType.Full */;\n        dest[offset++] = semanticTokens.data.length;\n        dest.set(semanticTokens.data, offset);\n        offset += semanticTokens.data.length;\n    }\n    else {\n        dest[offset++] = 2 /* EncodedSemanticTokensType.Delta */;\n        dest[offset++] = semanticTokens.deltas.length;\n        for (const delta of semanticTokens.deltas) {\n            dest[offset++] = delta.start;\n            dest[offset++] = delta.deleteCount;\n            if (delta.data) {\n                dest[offset++] = delta.data.length;\n                dest.set(delta.data, offset);\n                offset += delta.data.length;\n            }\n            else {\n                dest[offset++] = 0;\n            }\n        }\n    }\n    return toLittleEndianBuffer(dest);\n}\nfunction encodeSemanticTokensDtoSize(semanticTokens) {\n    let result = 0;\n    result += (+1 // id\n        + 1 // type\n    );\n    if (semanticTokens.type === 'full') {\n        result += (+1 // data length\n            + semanticTokens.data.length);\n    }\n    else {\n        result += (+1 // delta count\n        );\n        result += (+1 // start\n            + 1 // deleteCount\n            + 1 // data length\n        ) * semanticTokens.deltas.length;\n        for (const delta of semanticTokens.deltas) {\n            if (delta.data) {\n                result += delta.data.length;\n            }\n        }\n    }\n    return result;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,QAAQ,QAAQ,gCAAgC;AACzD,OAAO,KAAKC,QAAQ,MAAM,kCAAkC;AAC5D,SAASC,iBAAiBA,CAACC,GAAG,EAAE;EAC5B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGF,GAAG,CAACG,MAAM,EAAEF,CAAC,GAAGC,GAAG,EAAED,CAAC,IAAI,CAAC,EAAE;IAC/C;IACA,MAAMG,EAAE,GAAGJ,GAAG,CAACC,CAAC,GAAG,CAAC,CAAC;IACrB,MAAMI,EAAE,GAAGL,GAAG,CAACC,CAAC,GAAG,CAAC,CAAC;IACrB,MAAMK,EAAE,GAAGN,GAAG,CAACC,CAAC,GAAG,CAAC,CAAC;IACrB,MAAMM,EAAE,GAAGP,GAAG,CAACC,CAAC,GAAG,CAAC,CAAC;IACrBD,GAAG,CAACC,CAAC,GAAG,CAAC,CAAC,GAAGM,EAAE;IACfP,GAAG,CAACC,CAAC,GAAG,CAAC,CAAC,GAAGK,EAAE;IACfN,GAAG,CAACC,CAAC,GAAG,CAAC,CAAC,GAAGI,EAAE;IACfL,GAAG,CAACC,CAAC,GAAG,CAAC,CAAC,GAAGG,EAAE;EACnB;AACJ;AACA,SAASI,oBAAoBA,CAACR,GAAG,EAAE;EAC/B,MAAMS,QAAQ,GAAG,IAAIC,UAAU,CAACV,GAAG,CAACW,MAAM,EAAEX,GAAG,CAACY,UAAU,EAAEZ,GAAG,CAACG,MAAM,GAAG,CAAC,CAAC;EAC3E,IAAI,CAACL,QAAQ,CAACe,cAAc,CAAC,CAAC,EAAE;IAC5B;IACAd,iBAAiB,CAACU,QAAQ,CAAC;EAC/B;EACA,OAAOZ,QAAQ,CAACiB,IAAI,CAACL,QAAQ,CAAC;AAClC;AACA,OAAO,SAASM,uBAAuBA,CAACC,cAAc,EAAE;EACpD,MAAMC,IAAI,GAAG,IAAIC,WAAW,CAACC,2BAA2B,CAACH,cAAc,CAAC,CAAC;EACzE,IAAII,MAAM,GAAG,CAAC;EACdH,IAAI,CAACG,MAAM,EAAE,CAAC,GAAGJ,cAAc,CAACK,EAAE;EAClC,IAAIL,cAAc,CAACM,IAAI,KAAK,MAAM,EAAE;IAChCL,IAAI,CAACG,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC;IACnBH,IAAI,CAACG,MAAM,EAAE,CAAC,GAAGJ,cAAc,CAACO,IAAI,CAACpB,MAAM;IAC3Cc,IAAI,CAACO,GAAG,CAACR,cAAc,CAACO,IAAI,EAAEH,MAAM,CAAC;IACrCA,MAAM,IAAIJ,cAAc,CAACO,IAAI,CAACpB,MAAM;EACxC,CAAC,MACI;IACDc,IAAI,CAACG,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC;IACnBH,IAAI,CAACG,MAAM,EAAE,CAAC,GAAGJ,cAAc,CAACS,MAAM,CAACtB,MAAM;IAC7C,KAAK,MAAMuB,KAAK,IAAIV,cAAc,CAACS,MAAM,EAAE;MACvCR,IAAI,CAACG,MAAM,EAAE,CAAC,GAAGM,KAAK,CAACC,KAAK;MAC5BV,IAAI,CAACG,MAAM,EAAE,CAAC,GAAGM,KAAK,CAACE,WAAW;MAClC,IAAIF,KAAK,CAACH,IAAI,EAAE;QACZN,IAAI,CAACG,MAAM,EAAE,CAAC,GAAGM,KAAK,CAACH,IAAI,CAACpB,MAAM;QAClCc,IAAI,CAACO,GAAG,CAACE,KAAK,CAACH,IAAI,EAAEH,MAAM,CAAC;QAC5BA,MAAM,IAAIM,KAAK,CAACH,IAAI,CAACpB,MAAM;MAC/B,CAAC,MACI;QACDc,IAAI,CAACG,MAAM,EAAE,CAAC,GAAG,CAAC;MACtB;IACJ;EACJ;EACA,OAAOZ,oBAAoB,CAACS,IAAI,CAAC;AACrC;AACA,SAASE,2BAA2BA,CAACH,cAAc,EAAE;EACjD,IAAIa,MAAM,GAAG,CAAC;EACdA,MAAM,IAAK,CAAC,CAAC,CAAC;EAAA,EACR,CAAC,CAAC;EACP;EACD,IAAIb,cAAc,CAACM,IAAI,KAAK,MAAM,EAAE;IAChCO,MAAM,IAAK,CAAC,CAAC,CAAC;IAAA,EACRb,cAAc,CAACO,IAAI,CAACpB,MAAO;EACrC,CAAC,MACI;IACD0B,MAAM,IAAK,CAAC,CAAC,CAAC;IACb;IACDA,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC;IAAA,EACR,CAAC,CAAC;IAAA,EACF,CAAC,CAAC;IAAA,IACJb,cAAc,CAACS,MAAM,CAACtB,MAAM;IAChC,KAAK,MAAMuB,KAAK,IAAIV,cAAc,CAACS,MAAM,EAAE;MACvC,IAAIC,KAAK,CAACH,IAAI,EAAE;QACZM,MAAM,IAAIH,KAAK,CAACH,IAAI,CAACpB,MAAM;MAC/B;IACJ;EACJ;EACA,OAAO0B,MAAM;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}