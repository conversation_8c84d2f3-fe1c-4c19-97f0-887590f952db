{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { compareBy } from '../../../base/common/arrays.js';\nimport { findLastMax, findFirstMin } from '../../../base/common/arraysFind.js';\nimport { CursorState } from '../cursorCommon.js';\nimport { Cursor } from './oneCursor.js';\nimport { Position } from '../core/position.js';\nimport { Range } from '../core/range.js';\nimport { Selection } from '../core/selection.js';\nexport class CursorCollection {\n  constructor(context) {\n    this.context = context;\n    this.cursors = [new Cursor(context)];\n    this.lastAddedCursorIndex = 0;\n  }\n  dispose() {\n    for (const cursor of this.cursors) {\n      cursor.dispose(this.context);\n    }\n  }\n  startTrackingSelections() {\n    for (const cursor of this.cursors) {\n      cursor.startTrackingSelection(this.context);\n    }\n  }\n  stopTrackingSelections() {\n    for (const cursor of this.cursors) {\n      cursor.stopTrackingSelection(this.context);\n    }\n  }\n  updateContext(context) {\n    this.context = context;\n  }\n  ensureValidState() {\n    for (const cursor of this.cursors) {\n      cursor.ensureValidState(this.context);\n    }\n  }\n  readSelectionFromMarkers() {\n    return this.cursors.map(c => c.readSelectionFromMarkers(this.context));\n  }\n  getAll() {\n    return this.cursors.map(c => c.asCursorState());\n  }\n  getViewPositions() {\n    return this.cursors.map(c => c.viewState.position);\n  }\n  getTopMostViewPosition() {\n    return findFirstMin(this.cursors, compareBy(c => c.viewState.position, Position.compare)).viewState.position;\n  }\n  getBottomMostViewPosition() {\n    return findLastMax(this.cursors, compareBy(c => c.viewState.position, Position.compare)).viewState.position;\n  }\n  getSelections() {\n    return this.cursors.map(c => c.modelState.selection);\n  }\n  getViewSelections() {\n    return this.cursors.map(c => c.viewState.selection);\n  }\n  setSelections(selections) {\n    this.setStates(CursorState.fromModelSelections(selections));\n  }\n  getPrimaryCursor() {\n    return this.cursors[0].asCursorState();\n  }\n  setStates(states) {\n    if (states === null) {\n      return;\n    }\n    this.cursors[0].setState(this.context, states[0].modelState, states[0].viewState);\n    this._setSecondaryStates(states.slice(1));\n  }\n  /**\n   * Creates or disposes secondary cursors as necessary to match the number of `secondarySelections`.\n   */\n  _setSecondaryStates(secondaryStates) {\n    const secondaryCursorsLength = this.cursors.length - 1;\n    const secondaryStatesLength = secondaryStates.length;\n    if (secondaryCursorsLength < secondaryStatesLength) {\n      const createCnt = secondaryStatesLength - secondaryCursorsLength;\n      for (let i = 0; i < createCnt; i++) {\n        this._addSecondaryCursor();\n      }\n    } else if (secondaryCursorsLength > secondaryStatesLength) {\n      const removeCnt = secondaryCursorsLength - secondaryStatesLength;\n      for (let i = 0; i < removeCnt; i++) {\n        this._removeSecondaryCursor(this.cursors.length - 2);\n      }\n    }\n    for (let i = 0; i < secondaryStatesLength; i++) {\n      this.cursors[i + 1].setState(this.context, secondaryStates[i].modelState, secondaryStates[i].viewState);\n    }\n  }\n  killSecondaryCursors() {\n    this._setSecondaryStates([]);\n  }\n  _addSecondaryCursor() {\n    this.cursors.push(new Cursor(this.context));\n    this.lastAddedCursorIndex = this.cursors.length - 1;\n  }\n  getLastAddedCursorIndex() {\n    if (this.cursors.length === 1 || this.lastAddedCursorIndex === 0) {\n      return 0;\n    }\n    return this.lastAddedCursorIndex;\n  }\n  _removeSecondaryCursor(removeIndex) {\n    if (this.lastAddedCursorIndex >= removeIndex + 1) {\n      this.lastAddedCursorIndex--;\n    }\n    this.cursors[removeIndex + 1].dispose(this.context);\n    this.cursors.splice(removeIndex + 1, 1);\n  }\n  normalize() {\n    if (this.cursors.length === 1) {\n      return;\n    }\n    const cursors = this.cursors.slice(0);\n    const sortedCursors = [];\n    for (let i = 0, len = cursors.length; i < len; i++) {\n      sortedCursors.push({\n        index: i,\n        selection: cursors[i].modelState.selection\n      });\n    }\n    sortedCursors.sort(compareBy(s => s.selection, Range.compareRangesUsingStarts));\n    for (let sortedCursorIndex = 0; sortedCursorIndex < sortedCursors.length - 1; sortedCursorIndex++) {\n      const current = sortedCursors[sortedCursorIndex];\n      const next = sortedCursors[sortedCursorIndex + 1];\n      const currentSelection = current.selection;\n      const nextSelection = next.selection;\n      if (!this.context.cursorConfig.multiCursorMergeOverlapping) {\n        continue;\n      }\n      let shouldMergeCursors;\n      if (nextSelection.isEmpty() || currentSelection.isEmpty()) {\n        // Merge touching cursors if one of them is collapsed\n        shouldMergeCursors = nextSelection.getStartPosition().isBeforeOrEqual(currentSelection.getEndPosition());\n      } else {\n        // Merge only overlapping cursors (i.e. allow touching ranges)\n        shouldMergeCursors = nextSelection.getStartPosition().isBefore(currentSelection.getEndPosition());\n      }\n      if (shouldMergeCursors) {\n        const winnerSortedCursorIndex = current.index < next.index ? sortedCursorIndex : sortedCursorIndex + 1;\n        const looserSortedCursorIndex = current.index < next.index ? sortedCursorIndex + 1 : sortedCursorIndex;\n        const looserIndex = sortedCursors[looserSortedCursorIndex].index;\n        const winnerIndex = sortedCursors[winnerSortedCursorIndex].index;\n        const looserSelection = sortedCursors[looserSortedCursorIndex].selection;\n        const winnerSelection = sortedCursors[winnerSortedCursorIndex].selection;\n        if (!looserSelection.equalsSelection(winnerSelection)) {\n          const resultingRange = looserSelection.plusRange(winnerSelection);\n          const looserSelectionIsLTR = looserSelection.selectionStartLineNumber === looserSelection.startLineNumber && looserSelection.selectionStartColumn === looserSelection.startColumn;\n          const winnerSelectionIsLTR = winnerSelection.selectionStartLineNumber === winnerSelection.startLineNumber && winnerSelection.selectionStartColumn === winnerSelection.startColumn;\n          // Give more importance to the last added cursor (think Ctrl-dragging + hitting another cursor)\n          let resultingSelectionIsLTR;\n          if (looserIndex === this.lastAddedCursorIndex) {\n            resultingSelectionIsLTR = looserSelectionIsLTR;\n            this.lastAddedCursorIndex = winnerIndex;\n          } else {\n            // Winner takes it all\n            resultingSelectionIsLTR = winnerSelectionIsLTR;\n          }\n          let resultingSelection;\n          if (resultingSelectionIsLTR) {\n            resultingSelection = new Selection(resultingRange.startLineNumber, resultingRange.startColumn, resultingRange.endLineNumber, resultingRange.endColumn);\n          } else {\n            resultingSelection = new Selection(resultingRange.endLineNumber, resultingRange.endColumn, resultingRange.startLineNumber, resultingRange.startColumn);\n          }\n          sortedCursors[winnerSortedCursorIndex].selection = resultingSelection;\n          const resultingState = CursorState.fromModelSelection(resultingSelection);\n          cursors[winnerIndex].setState(this.context, resultingState.modelState, resultingState.viewState);\n        }\n        for (const sortedCursor of sortedCursors) {\n          if (sortedCursor.index > looserIndex) {\n            sortedCursor.index--;\n          }\n        }\n        cursors.splice(looserIndex, 1);\n        sortedCursors.splice(looserSortedCursorIndex, 1);\n        this._removeSecondaryCursor(looserIndex - 1);\n        sortedCursorIndex--;\n      }\n    }\n  }\n}", "map": {"version": 3, "names": ["compareBy", "findLastMax", "findFirstMin", "CursorState", "<PERSON><PERSON><PERSON>", "Position", "Range", "Selection", "CursorCollection", "constructor", "context", "cursors", "lastAddedCursorIndex", "dispose", "cursor", "startTrackingSelections", "startTrackingSelection", "stopTrackingSelections", "stopTrackingSelection", "updateContext", "ensureValidState", "readSelectionFromMarkers", "map", "c", "getAll", "asCursorState", "getViewPositions", "viewState", "position", "getTopMostViewPosition", "compare", "getBottomMostViewPosition", "getSelections", "modelState", "selection", "getViewSelections", "setSelections", "selections", "setStates", "fromModelSelections", "getPrimaryCursor", "states", "setState", "_setSecondaryStates", "slice", "secondaryStates", "secondaryCursorsLength", "length", "secondaryStatesLength", "createCnt", "i", "_addSecondaryCursor", "removeCnt", "_removeSecondaryCursor", "killSecondaryCursors", "push", "getLastAddedCursorIndex", "removeIndex", "splice", "normalize", "sortedCursors", "len", "index", "sort", "s", "compareRangesUsingStarts", "sortedCursorIndex", "current", "next", "currentSelection", "nextSelection", "cursorConfig", "multiCursorMergeOverlapping", "shouldMergeCursors", "isEmpty", "getStartPosition", "isBeforeOrEqual", "getEndPosition", "isBefore", "winnerSortedCursorIndex", "looserSortedCursorIndex", "looserIndex", "winnerIndex", "looserSelection", "winnerSelection", "equalsSelection", "resultingRange", "plusRange", "looserSelectionIsLTR", "selectionStartLineNumber", "startLineNumber", "selectionStartColumn", "startColumn", "winnerSelectionIsLTR", "resultingSelectionIsLTR", "resultingSelection", "endLineNumber", "endColumn", "resultingState", "fromModelSelection", "sortedCursor"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/cursor/cursorCollection.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { compareBy } from '../../../base/common/arrays.js';\nimport { findLastMax, findFirstMin } from '../../../base/common/arraysFind.js';\nimport { CursorState } from '../cursorCommon.js';\nimport { Cursor } from './oneCursor.js';\nimport { Position } from '../core/position.js';\nimport { Range } from '../core/range.js';\nimport { Selection } from '../core/selection.js';\nexport class CursorCollection {\n    constructor(context) {\n        this.context = context;\n        this.cursors = [new Cursor(context)];\n        this.lastAddedCursorIndex = 0;\n    }\n    dispose() {\n        for (const cursor of this.cursors) {\n            cursor.dispose(this.context);\n        }\n    }\n    startTrackingSelections() {\n        for (const cursor of this.cursors) {\n            cursor.startTrackingSelection(this.context);\n        }\n    }\n    stopTrackingSelections() {\n        for (const cursor of this.cursors) {\n            cursor.stopTrackingSelection(this.context);\n        }\n    }\n    updateContext(context) {\n        this.context = context;\n    }\n    ensureValidState() {\n        for (const cursor of this.cursors) {\n            cursor.ensureValidState(this.context);\n        }\n    }\n    readSelectionFromMarkers() {\n        return this.cursors.map(c => c.readSelectionFromMarkers(this.context));\n    }\n    getAll() {\n        return this.cursors.map(c => c.asCursorState());\n    }\n    getViewPositions() {\n        return this.cursors.map(c => c.viewState.position);\n    }\n    getTopMostViewPosition() {\n        return findFirstMin(this.cursors, compareBy(c => c.viewState.position, Position.compare)).viewState.position;\n    }\n    getBottomMostViewPosition() {\n        return findLastMax(this.cursors, compareBy(c => c.viewState.position, Position.compare)).viewState.position;\n    }\n    getSelections() {\n        return this.cursors.map(c => c.modelState.selection);\n    }\n    getViewSelections() {\n        return this.cursors.map(c => c.viewState.selection);\n    }\n    setSelections(selections) {\n        this.setStates(CursorState.fromModelSelections(selections));\n    }\n    getPrimaryCursor() {\n        return this.cursors[0].asCursorState();\n    }\n    setStates(states) {\n        if (states === null) {\n            return;\n        }\n        this.cursors[0].setState(this.context, states[0].modelState, states[0].viewState);\n        this._setSecondaryStates(states.slice(1));\n    }\n    /**\n     * Creates or disposes secondary cursors as necessary to match the number of `secondarySelections`.\n     */\n    _setSecondaryStates(secondaryStates) {\n        const secondaryCursorsLength = this.cursors.length - 1;\n        const secondaryStatesLength = secondaryStates.length;\n        if (secondaryCursorsLength < secondaryStatesLength) {\n            const createCnt = secondaryStatesLength - secondaryCursorsLength;\n            for (let i = 0; i < createCnt; i++) {\n                this._addSecondaryCursor();\n            }\n        }\n        else if (secondaryCursorsLength > secondaryStatesLength) {\n            const removeCnt = secondaryCursorsLength - secondaryStatesLength;\n            for (let i = 0; i < removeCnt; i++) {\n                this._removeSecondaryCursor(this.cursors.length - 2);\n            }\n        }\n        for (let i = 0; i < secondaryStatesLength; i++) {\n            this.cursors[i + 1].setState(this.context, secondaryStates[i].modelState, secondaryStates[i].viewState);\n        }\n    }\n    killSecondaryCursors() {\n        this._setSecondaryStates([]);\n    }\n    _addSecondaryCursor() {\n        this.cursors.push(new Cursor(this.context));\n        this.lastAddedCursorIndex = this.cursors.length - 1;\n    }\n    getLastAddedCursorIndex() {\n        if (this.cursors.length === 1 || this.lastAddedCursorIndex === 0) {\n            return 0;\n        }\n        return this.lastAddedCursorIndex;\n    }\n    _removeSecondaryCursor(removeIndex) {\n        if (this.lastAddedCursorIndex >= removeIndex + 1) {\n            this.lastAddedCursorIndex--;\n        }\n        this.cursors[removeIndex + 1].dispose(this.context);\n        this.cursors.splice(removeIndex + 1, 1);\n    }\n    normalize() {\n        if (this.cursors.length === 1) {\n            return;\n        }\n        const cursors = this.cursors.slice(0);\n        const sortedCursors = [];\n        for (let i = 0, len = cursors.length; i < len; i++) {\n            sortedCursors.push({\n                index: i,\n                selection: cursors[i].modelState.selection,\n            });\n        }\n        sortedCursors.sort(compareBy(s => s.selection, Range.compareRangesUsingStarts));\n        for (let sortedCursorIndex = 0; sortedCursorIndex < sortedCursors.length - 1; sortedCursorIndex++) {\n            const current = sortedCursors[sortedCursorIndex];\n            const next = sortedCursors[sortedCursorIndex + 1];\n            const currentSelection = current.selection;\n            const nextSelection = next.selection;\n            if (!this.context.cursorConfig.multiCursorMergeOverlapping) {\n                continue;\n            }\n            let shouldMergeCursors;\n            if (nextSelection.isEmpty() || currentSelection.isEmpty()) {\n                // Merge touching cursors if one of them is collapsed\n                shouldMergeCursors = nextSelection.getStartPosition().isBeforeOrEqual(currentSelection.getEndPosition());\n            }\n            else {\n                // Merge only overlapping cursors (i.e. allow touching ranges)\n                shouldMergeCursors = nextSelection.getStartPosition().isBefore(currentSelection.getEndPosition());\n            }\n            if (shouldMergeCursors) {\n                const winnerSortedCursorIndex = current.index < next.index ? sortedCursorIndex : sortedCursorIndex + 1;\n                const looserSortedCursorIndex = current.index < next.index ? sortedCursorIndex + 1 : sortedCursorIndex;\n                const looserIndex = sortedCursors[looserSortedCursorIndex].index;\n                const winnerIndex = sortedCursors[winnerSortedCursorIndex].index;\n                const looserSelection = sortedCursors[looserSortedCursorIndex].selection;\n                const winnerSelection = sortedCursors[winnerSortedCursorIndex].selection;\n                if (!looserSelection.equalsSelection(winnerSelection)) {\n                    const resultingRange = looserSelection.plusRange(winnerSelection);\n                    const looserSelectionIsLTR = (looserSelection.selectionStartLineNumber === looserSelection.startLineNumber && looserSelection.selectionStartColumn === looserSelection.startColumn);\n                    const winnerSelectionIsLTR = (winnerSelection.selectionStartLineNumber === winnerSelection.startLineNumber && winnerSelection.selectionStartColumn === winnerSelection.startColumn);\n                    // Give more importance to the last added cursor (think Ctrl-dragging + hitting another cursor)\n                    let resultingSelectionIsLTR;\n                    if (looserIndex === this.lastAddedCursorIndex) {\n                        resultingSelectionIsLTR = looserSelectionIsLTR;\n                        this.lastAddedCursorIndex = winnerIndex;\n                    }\n                    else {\n                        // Winner takes it all\n                        resultingSelectionIsLTR = winnerSelectionIsLTR;\n                    }\n                    let resultingSelection;\n                    if (resultingSelectionIsLTR) {\n                        resultingSelection = new Selection(resultingRange.startLineNumber, resultingRange.startColumn, resultingRange.endLineNumber, resultingRange.endColumn);\n                    }\n                    else {\n                        resultingSelection = new Selection(resultingRange.endLineNumber, resultingRange.endColumn, resultingRange.startLineNumber, resultingRange.startColumn);\n                    }\n                    sortedCursors[winnerSortedCursorIndex].selection = resultingSelection;\n                    const resultingState = CursorState.fromModelSelection(resultingSelection);\n                    cursors[winnerIndex].setState(this.context, resultingState.modelState, resultingState.viewState);\n                }\n                for (const sortedCursor of sortedCursors) {\n                    if (sortedCursor.index > looserIndex) {\n                        sortedCursor.index--;\n                    }\n                }\n                cursors.splice(looserIndex, 1);\n                sortedCursors.splice(looserSortedCursorIndex, 1);\n                this._removeSecondaryCursor(looserIndex - 1);\n                sortedCursorIndex--;\n            }\n        }\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,gCAAgC;AAC1D,SAASC,WAAW,EAAEC,YAAY,QAAQ,oCAAoC;AAC9E,SAASC,WAAW,QAAQ,oBAAoB;AAChD,SAASC,MAAM,QAAQ,gBAAgB;AACvC,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,SAAS,QAAQ,sBAAsB;AAChD,OAAO,MAAMC,gBAAgB,CAAC;EAC1BC,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,OAAO,GAAG,CAAC,IAAIP,MAAM,CAACM,OAAO,CAAC,CAAC;IACpC,IAAI,CAACE,oBAAoB,GAAG,CAAC;EACjC;EACAC,OAAOA,CAAA,EAAG;IACN,KAAK,MAAMC,MAAM,IAAI,IAAI,CAACH,OAAO,EAAE;MAC/BG,MAAM,CAACD,OAAO,CAAC,IAAI,CAACH,OAAO,CAAC;IAChC;EACJ;EACAK,uBAAuBA,CAAA,EAAG;IACtB,KAAK,MAAMD,MAAM,IAAI,IAAI,CAACH,OAAO,EAAE;MAC/BG,MAAM,CAACE,sBAAsB,CAAC,IAAI,CAACN,OAAO,CAAC;IAC/C;EACJ;EACAO,sBAAsBA,CAAA,EAAG;IACrB,KAAK,MAAMH,MAAM,IAAI,IAAI,CAACH,OAAO,EAAE;MAC/BG,MAAM,CAACI,qBAAqB,CAAC,IAAI,CAACR,OAAO,CAAC;IAC9C;EACJ;EACAS,aAAaA,CAACT,OAAO,EAAE;IACnB,IAAI,CAACA,OAAO,GAAGA,OAAO;EAC1B;EACAU,gBAAgBA,CAAA,EAAG;IACf,KAAK,MAAMN,MAAM,IAAI,IAAI,CAACH,OAAO,EAAE;MAC/BG,MAAM,CAACM,gBAAgB,CAAC,IAAI,CAACV,OAAO,CAAC;IACzC;EACJ;EACAW,wBAAwBA,CAAA,EAAG;IACvB,OAAO,IAAI,CAACV,OAAO,CAACW,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACF,wBAAwB,CAAC,IAAI,CAACX,OAAO,CAAC,CAAC;EAC1E;EACAc,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI,CAACb,OAAO,CAACW,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACE,aAAa,CAAC,CAAC,CAAC;EACnD;EACAC,gBAAgBA,CAAA,EAAG;IACf,OAAO,IAAI,CAACf,OAAO,CAACW,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACI,SAAS,CAACC,QAAQ,CAAC;EACtD;EACAC,sBAAsBA,CAAA,EAAG;IACrB,OAAO3B,YAAY,CAAC,IAAI,CAACS,OAAO,EAAEX,SAAS,CAACuB,CAAC,IAAIA,CAAC,CAACI,SAAS,CAACC,QAAQ,EAAEvB,QAAQ,CAACyB,OAAO,CAAC,CAAC,CAACH,SAAS,CAACC,QAAQ;EAChH;EACAG,yBAAyBA,CAAA,EAAG;IACxB,OAAO9B,WAAW,CAAC,IAAI,CAACU,OAAO,EAAEX,SAAS,CAACuB,CAAC,IAAIA,CAAC,CAACI,SAAS,CAACC,QAAQ,EAAEvB,QAAQ,CAACyB,OAAO,CAAC,CAAC,CAACH,SAAS,CAACC,QAAQ;EAC/G;EACAI,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACrB,OAAO,CAACW,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACU,UAAU,CAACC,SAAS,CAAC;EACxD;EACAC,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACxB,OAAO,CAACW,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACI,SAAS,CAACO,SAAS,CAAC;EACvD;EACAE,aAAaA,CAACC,UAAU,EAAE;IACtB,IAAI,CAACC,SAAS,CAACnC,WAAW,CAACoC,mBAAmB,CAACF,UAAU,CAAC,CAAC;EAC/D;EACAG,gBAAgBA,CAAA,EAAG;IACf,OAAO,IAAI,CAAC7B,OAAO,CAAC,CAAC,CAAC,CAACc,aAAa,CAAC,CAAC;EAC1C;EACAa,SAASA,CAACG,MAAM,EAAE;IACd,IAAIA,MAAM,KAAK,IAAI,EAAE;MACjB;IACJ;IACA,IAAI,CAAC9B,OAAO,CAAC,CAAC,CAAC,CAAC+B,QAAQ,CAAC,IAAI,CAAChC,OAAO,EAAE+B,MAAM,CAAC,CAAC,CAAC,CAACR,UAAU,EAAEQ,MAAM,CAAC,CAAC,CAAC,CAACd,SAAS,CAAC;IACjF,IAAI,CAACgB,mBAAmB,CAACF,MAAM,CAACG,KAAK,CAAC,CAAC,CAAC,CAAC;EAC7C;EACA;AACJ;AACA;EACID,mBAAmBA,CAACE,eAAe,EAAE;IACjC,MAAMC,sBAAsB,GAAG,IAAI,CAACnC,OAAO,CAACoC,MAAM,GAAG,CAAC;IACtD,MAAMC,qBAAqB,GAAGH,eAAe,CAACE,MAAM;IACpD,IAAID,sBAAsB,GAAGE,qBAAqB,EAAE;MAChD,MAAMC,SAAS,GAAGD,qBAAqB,GAAGF,sBAAsB;MAChE,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,SAAS,EAAEC,CAAC,EAAE,EAAE;QAChC,IAAI,CAACC,mBAAmB,CAAC,CAAC;MAC9B;IACJ,CAAC,MACI,IAAIL,sBAAsB,GAAGE,qBAAqB,EAAE;MACrD,MAAMI,SAAS,GAAGN,sBAAsB,GAAGE,qBAAqB;MAChE,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGE,SAAS,EAAEF,CAAC,EAAE,EAAE;QAChC,IAAI,CAACG,sBAAsB,CAAC,IAAI,CAAC1C,OAAO,CAACoC,MAAM,GAAG,CAAC,CAAC;MACxD;IACJ;IACA,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,qBAAqB,EAAEE,CAAC,EAAE,EAAE;MAC5C,IAAI,CAACvC,OAAO,CAACuC,CAAC,GAAG,CAAC,CAAC,CAACR,QAAQ,CAAC,IAAI,CAAChC,OAAO,EAAEmC,eAAe,CAACK,CAAC,CAAC,CAACjB,UAAU,EAAEY,eAAe,CAACK,CAAC,CAAC,CAACvB,SAAS,CAAC;IAC3G;EACJ;EACA2B,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACX,mBAAmB,CAAC,EAAE,CAAC;EAChC;EACAQ,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAACxC,OAAO,CAAC4C,IAAI,CAAC,IAAInD,MAAM,CAAC,IAAI,CAACM,OAAO,CAAC,CAAC;IAC3C,IAAI,CAACE,oBAAoB,GAAG,IAAI,CAACD,OAAO,CAACoC,MAAM,GAAG,CAAC;EACvD;EACAS,uBAAuBA,CAAA,EAAG;IACtB,IAAI,IAAI,CAAC7C,OAAO,CAACoC,MAAM,KAAK,CAAC,IAAI,IAAI,CAACnC,oBAAoB,KAAK,CAAC,EAAE;MAC9D,OAAO,CAAC;IACZ;IACA,OAAO,IAAI,CAACA,oBAAoB;EACpC;EACAyC,sBAAsBA,CAACI,WAAW,EAAE;IAChC,IAAI,IAAI,CAAC7C,oBAAoB,IAAI6C,WAAW,GAAG,CAAC,EAAE;MAC9C,IAAI,CAAC7C,oBAAoB,EAAE;IAC/B;IACA,IAAI,CAACD,OAAO,CAAC8C,WAAW,GAAG,CAAC,CAAC,CAAC5C,OAAO,CAAC,IAAI,CAACH,OAAO,CAAC;IACnD,IAAI,CAACC,OAAO,CAAC+C,MAAM,CAACD,WAAW,GAAG,CAAC,EAAE,CAAC,CAAC;EAC3C;EACAE,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAAChD,OAAO,CAACoC,MAAM,KAAK,CAAC,EAAE;MAC3B;IACJ;IACA,MAAMpC,OAAO,GAAG,IAAI,CAACA,OAAO,CAACiC,KAAK,CAAC,CAAC,CAAC;IACrC,MAAMgB,aAAa,GAAG,EAAE;IACxB,KAAK,IAAIV,CAAC,GAAG,CAAC,EAAEW,GAAG,GAAGlD,OAAO,CAACoC,MAAM,EAAEG,CAAC,GAAGW,GAAG,EAAEX,CAAC,EAAE,EAAE;MAChDU,aAAa,CAACL,IAAI,CAAC;QACfO,KAAK,EAAEZ,CAAC;QACRhB,SAAS,EAAEvB,OAAO,CAACuC,CAAC,CAAC,CAACjB,UAAU,CAACC;MACrC,CAAC,CAAC;IACN;IACA0B,aAAa,CAACG,IAAI,CAAC/D,SAAS,CAACgE,CAAC,IAAIA,CAAC,CAAC9B,SAAS,EAAE5B,KAAK,CAAC2D,wBAAwB,CAAC,CAAC;IAC/E,KAAK,IAAIC,iBAAiB,GAAG,CAAC,EAAEA,iBAAiB,GAAGN,aAAa,CAACb,MAAM,GAAG,CAAC,EAAEmB,iBAAiB,EAAE,EAAE;MAC/F,MAAMC,OAAO,GAAGP,aAAa,CAACM,iBAAiB,CAAC;MAChD,MAAME,IAAI,GAAGR,aAAa,CAACM,iBAAiB,GAAG,CAAC,CAAC;MACjD,MAAMG,gBAAgB,GAAGF,OAAO,CAACjC,SAAS;MAC1C,MAAMoC,aAAa,GAAGF,IAAI,CAAClC,SAAS;MACpC,IAAI,CAAC,IAAI,CAACxB,OAAO,CAAC6D,YAAY,CAACC,2BAA2B,EAAE;QACxD;MACJ;MACA,IAAIC,kBAAkB;MACtB,IAAIH,aAAa,CAACI,OAAO,CAAC,CAAC,IAAIL,gBAAgB,CAACK,OAAO,CAAC,CAAC,EAAE;QACvD;QACAD,kBAAkB,GAAGH,aAAa,CAACK,gBAAgB,CAAC,CAAC,CAACC,eAAe,CAACP,gBAAgB,CAACQ,cAAc,CAAC,CAAC,CAAC;MAC5G,CAAC,MACI;QACD;QACAJ,kBAAkB,GAAGH,aAAa,CAACK,gBAAgB,CAAC,CAAC,CAACG,QAAQ,CAACT,gBAAgB,CAACQ,cAAc,CAAC,CAAC,CAAC;MACrG;MACA,IAAIJ,kBAAkB,EAAE;QACpB,MAAMM,uBAAuB,GAAGZ,OAAO,CAACL,KAAK,GAAGM,IAAI,CAACN,KAAK,GAAGI,iBAAiB,GAAGA,iBAAiB,GAAG,CAAC;QACtG,MAAMc,uBAAuB,GAAGb,OAAO,CAACL,KAAK,GAAGM,IAAI,CAACN,KAAK,GAAGI,iBAAiB,GAAG,CAAC,GAAGA,iBAAiB;QACtG,MAAMe,WAAW,GAAGrB,aAAa,CAACoB,uBAAuB,CAAC,CAAClB,KAAK;QAChE,MAAMoB,WAAW,GAAGtB,aAAa,CAACmB,uBAAuB,CAAC,CAACjB,KAAK;QAChE,MAAMqB,eAAe,GAAGvB,aAAa,CAACoB,uBAAuB,CAAC,CAAC9C,SAAS;QACxE,MAAMkD,eAAe,GAAGxB,aAAa,CAACmB,uBAAuB,CAAC,CAAC7C,SAAS;QACxE,IAAI,CAACiD,eAAe,CAACE,eAAe,CAACD,eAAe,CAAC,EAAE;UACnD,MAAME,cAAc,GAAGH,eAAe,CAACI,SAAS,CAACH,eAAe,CAAC;UACjE,MAAMI,oBAAoB,GAAIL,eAAe,CAACM,wBAAwB,KAAKN,eAAe,CAACO,eAAe,IAAIP,eAAe,CAACQ,oBAAoB,KAAKR,eAAe,CAACS,WAAY;UACnL,MAAMC,oBAAoB,GAAIT,eAAe,CAACK,wBAAwB,KAAKL,eAAe,CAACM,eAAe,IAAIN,eAAe,CAACO,oBAAoB,KAAKP,eAAe,CAACQ,WAAY;UACnL;UACA,IAAIE,uBAAuB;UAC3B,IAAIb,WAAW,KAAK,IAAI,CAACrE,oBAAoB,EAAE;YAC3CkF,uBAAuB,GAAGN,oBAAoB;YAC9C,IAAI,CAAC5E,oBAAoB,GAAGsE,WAAW;UAC3C,CAAC,MACI;YACD;YACAY,uBAAuB,GAAGD,oBAAoB;UAClD;UACA,IAAIE,kBAAkB;UACtB,IAAID,uBAAuB,EAAE;YACzBC,kBAAkB,GAAG,IAAIxF,SAAS,CAAC+E,cAAc,CAACI,eAAe,EAAEJ,cAAc,CAACM,WAAW,EAAEN,cAAc,CAACU,aAAa,EAAEV,cAAc,CAACW,SAAS,CAAC;UAC1J,CAAC,MACI;YACDF,kBAAkB,GAAG,IAAIxF,SAAS,CAAC+E,cAAc,CAACU,aAAa,EAAEV,cAAc,CAACW,SAAS,EAAEX,cAAc,CAACI,eAAe,EAAEJ,cAAc,CAACM,WAAW,CAAC;UAC1J;UACAhC,aAAa,CAACmB,uBAAuB,CAAC,CAAC7C,SAAS,GAAG6D,kBAAkB;UACrE,MAAMG,cAAc,GAAG/F,WAAW,CAACgG,kBAAkB,CAACJ,kBAAkB,CAAC;UACzEpF,OAAO,CAACuE,WAAW,CAAC,CAACxC,QAAQ,CAAC,IAAI,CAAChC,OAAO,EAAEwF,cAAc,CAACjE,UAAU,EAAEiE,cAAc,CAACvE,SAAS,CAAC;QACpG;QACA,KAAK,MAAMyE,YAAY,IAAIxC,aAAa,EAAE;UACtC,IAAIwC,YAAY,CAACtC,KAAK,GAAGmB,WAAW,EAAE;YAClCmB,YAAY,CAACtC,KAAK,EAAE;UACxB;QACJ;QACAnD,OAAO,CAAC+C,MAAM,CAACuB,WAAW,EAAE,CAAC,CAAC;QAC9BrB,aAAa,CAACF,MAAM,CAACsB,uBAAuB,EAAE,CAAC,CAAC;QAChD,IAAI,CAAC3B,sBAAsB,CAAC4B,WAAW,GAAG,CAAC,CAAC;QAC5Cf,iBAAiB,EAAE;MACvB;IACJ;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}