{"ast": null, "code": "import _asyncToGenerator from \"C:/console/aava-ui-web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { assertNever } from '../../../../../base/common/assert.js';\nimport { DeferredPromise } from '../../../../../base/common/async.js';\nimport { CancellationToken } from '../../../../../base/common/cancellation.js';\nimport { SetMap } from '../../../../../base/common/map.js';\nimport { onUnexpectedExternalError } from '../../../../../base/common/errors.js';\nimport { Position } from '../../../../common/core/position.js';\nimport { Range } from '../../../../common/core/range.js';\nimport { fixBracketsInLine } from '../../../../common/model/bracketPairsTextModelPart/fixBrackets.js';\nimport { SingleTextEdit } from '../../../../common/core/textEdit.js';\nimport { getReadonlyEmptyArray } from '../utils.js';\nimport { SnippetParser, Text } from '../../../snippet/browser/snippetParser.js';\nexport function provideInlineCompletions(_x, _x2, _x3, _x4) {\n  return _provideInlineCompletions.apply(this, arguments);\n}\nfunction _provideInlineCompletions() {\n  _provideInlineCompletions = _asyncToGenerator(function* (registry, positionOrRange, model, context, token = CancellationToken.None, languageConfigurationService) {\n    // Important: Don't use position after the await calls, as the model could have been changed in the meantime!\n    const defaultReplaceRange = positionOrRange instanceof Position ? getDefaultRange(positionOrRange, model) : positionOrRange;\n    const providers = registry.all(model);\n    const multiMap = new SetMap();\n    for (const provider of providers) {\n      if (provider.groupId) {\n        multiMap.add(provider.groupId, provider);\n      }\n    }\n    function getPreferredProviders(provider) {\n      if (!provider.yieldsToGroupIds) {\n        return [];\n      }\n      const result = [];\n      for (const groupId of provider.yieldsToGroupIds || []) {\n        const providers = multiMap.get(groupId);\n        for (const p of providers) {\n          result.push(p);\n        }\n      }\n      return result;\n    }\n    const states = new Map();\n    const seen = new Set();\n    function findPreferredProviderCircle(provider, stack) {\n      stack = [...stack, provider];\n      if (seen.has(provider)) {\n        return stack;\n      }\n      seen.add(provider);\n      try {\n        const preferred = getPreferredProviders(provider);\n        for (const p of preferred) {\n          const c = findPreferredProviderCircle(p, stack);\n          if (c) {\n            return c;\n          }\n        }\n      } finally {\n        seen.delete(provider);\n      }\n      return undefined;\n    }\n    function processProvider(provider) {\n      const state = states.get(provider);\n      if (state) {\n        return state;\n      }\n      const circle = findPreferredProviderCircle(provider, []);\n      if (circle) {\n        onUnexpectedExternalError(new Error(`Inline completions: cyclic yield-to dependency detected. Path: ${circle.map(s => s.toString ? s.toString() : '' + s).join(' -> ')}`));\n      }\n      const deferredPromise = new DeferredPromise();\n      states.set(provider, deferredPromise.p);\n      _asyncToGenerator(function* () {\n        if (!circle) {\n          const preferred = getPreferredProviders(provider);\n          for (const p of preferred) {\n            const result = yield processProvider(p);\n            if (result && result.items.length > 0) {\n              // Skip provider\n              return undefined;\n            }\n          }\n        }\n        try {\n          if (positionOrRange instanceof Position) {\n            const completions = yield provider.provideInlineCompletions(model, positionOrRange, context, token);\n            return completions;\n          } else {\n            const completions = yield provider.provideInlineEdits?.(model, positionOrRange, context, token);\n            return completions;\n          }\n        } catch (e) {\n          onUnexpectedExternalError(e);\n          return undefined;\n        }\n      })().then(c => deferredPromise.complete(c), e => deferredPromise.error(e));\n      return deferredPromise.p;\n    }\n    const providerResults = yield Promise.all(providers.map(/*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(function* (provider) {\n        return {\n          provider,\n          completions: yield processProvider(provider)\n        };\n      });\n      return function (_x5) {\n        return _ref2.apply(this, arguments);\n      };\n    }()));\n    const itemsByHash = new Map();\n    const lists = [];\n    for (const result of providerResults) {\n      const completions = result.completions;\n      if (!completions) {\n        continue;\n      }\n      const list = new InlineCompletionList(completions, result.provider);\n      lists.push(list);\n      for (const item of completions.items) {\n        const inlineCompletionItem = InlineCompletionItem.from(item, list, defaultReplaceRange, model, languageConfigurationService);\n        itemsByHash.set(inlineCompletionItem.hash(), inlineCompletionItem);\n      }\n    }\n    return new InlineCompletionProviderResult(Array.from(itemsByHash.values()), new Set(itemsByHash.keys()), lists);\n  });\n  return _provideInlineCompletions.apply(this, arguments);\n}\nexport class InlineCompletionProviderResult {\n  constructor(\n  /**\n   * Free of duplicates.\n   */\n  completions, hashs, providerResults) {\n    this.completions = completions;\n    this.hashs = hashs;\n    this.providerResults = providerResults;\n  }\n  has(item) {\n    return this.hashs.has(item.hash());\n  }\n  dispose() {\n    for (const result of this.providerResults) {\n      result.removeRef();\n    }\n  }\n}\n/**\n * A ref counted pointer to the computed `InlineCompletions` and the `InlineCompletionsProvider` that\n * computed them.\n */\nexport class InlineCompletionList {\n  constructor(inlineCompletions, provider) {\n    this.inlineCompletions = inlineCompletions;\n    this.provider = provider;\n    this.refCount = 1;\n  }\n  addRef() {\n    this.refCount++;\n  }\n  removeRef() {\n    this.refCount--;\n    if (this.refCount === 0) {\n      this.provider.freeInlineCompletions(this.inlineCompletions);\n    }\n  }\n}\nexport class InlineCompletionItem {\n  static from(inlineCompletion, source, defaultReplaceRange, textModel, languageConfigurationService) {\n    let insertText;\n    let snippetInfo;\n    let range = inlineCompletion.range ? Range.lift(inlineCompletion.range) : defaultReplaceRange;\n    if (typeof inlineCompletion.insertText === 'string') {\n      insertText = inlineCompletion.insertText;\n      if (languageConfigurationService && inlineCompletion.completeBracketPairs) {\n        insertText = closeBrackets(insertText, range.getStartPosition(), textModel, languageConfigurationService);\n        // Modify range depending on if brackets are added or removed\n        const diff = insertText.length - inlineCompletion.insertText.length;\n        if (diff !== 0) {\n          range = new Range(range.startLineNumber, range.startColumn, range.endLineNumber, range.endColumn + diff);\n        }\n      }\n      snippetInfo = undefined;\n    } else if ('snippet' in inlineCompletion.insertText) {\n      const preBracketCompletionLength = inlineCompletion.insertText.snippet.length;\n      if (languageConfigurationService && inlineCompletion.completeBracketPairs) {\n        inlineCompletion.insertText.snippet = closeBrackets(inlineCompletion.insertText.snippet, range.getStartPosition(), textModel, languageConfigurationService);\n        // Modify range depending on if brackets are added or removed\n        const diff = inlineCompletion.insertText.snippet.length - preBracketCompletionLength;\n        if (diff !== 0) {\n          range = new Range(range.startLineNumber, range.startColumn, range.endLineNumber, range.endColumn + diff);\n        }\n      }\n      const snippet = new SnippetParser().parse(inlineCompletion.insertText.snippet);\n      if (snippet.children.length === 1 && snippet.children[0] instanceof Text) {\n        insertText = snippet.children[0].value;\n        snippetInfo = undefined;\n      } else {\n        insertText = snippet.toString();\n        snippetInfo = {\n          snippet: inlineCompletion.insertText.snippet,\n          range: range\n        };\n      }\n    } else {\n      assertNever(inlineCompletion.insertText);\n    }\n    return new InlineCompletionItem(insertText, inlineCompletion.command, range, insertText, snippetInfo, inlineCompletion.additionalTextEdits || getReadonlyEmptyArray(), inlineCompletion, source);\n  }\n  constructor(filterText, command, range, insertText, snippetInfo, additionalTextEdits,\n  /**\n   * A reference to the original inline completion this inline completion has been constructed from.\n   * Used for event data to ensure referential equality.\n  */\n  sourceInlineCompletion,\n  /**\n   * A reference to the original inline completion list this inline completion has been constructed from.\n   * Used for event data to ensure referential equality.\n  */\n  source) {\n    this.filterText = filterText;\n    this.command = command;\n    this.range = range;\n    this.insertText = insertText;\n    this.snippetInfo = snippetInfo;\n    this.additionalTextEdits = additionalTextEdits;\n    this.sourceInlineCompletion = sourceInlineCompletion;\n    this.source = source;\n    filterText = filterText.replace(/\\r\\n|\\r/g, '\\n');\n    insertText = filterText.replace(/\\r\\n|\\r/g, '\\n');\n  }\n  withRange(updatedRange) {\n    return new InlineCompletionItem(this.filterText, this.command, updatedRange, this.insertText, this.snippetInfo, this.additionalTextEdits, this.sourceInlineCompletion, this.source);\n  }\n  hash() {\n    return JSON.stringify({\n      insertText: this.insertText,\n      range: this.range.toString()\n    });\n  }\n  toSingleTextEdit() {\n    return new SingleTextEdit(this.range, this.insertText);\n  }\n}\nfunction getDefaultRange(position, model) {\n  const word = model.getWordAtPosition(position);\n  const maxColumn = model.getLineMaxColumn(position.lineNumber);\n  // By default, always replace up until the end of the current line.\n  // This default might be subject to change!\n  return word ? new Range(position.lineNumber, word.startColumn, position.lineNumber, maxColumn) : Range.fromPositions(position, position.with(undefined, maxColumn));\n}\nfunction closeBrackets(text, position, model, languageConfigurationService) {\n  const lineStart = model.getLineContent(position.lineNumber).substring(0, position.column - 1);\n  const newLine = lineStart + text;\n  const newTokens = model.tokenization.tokenizeLineWithEdit(position, newLine.length - (position.column - 1), text);\n  const slicedTokens = newTokens?.sliceAndInflate(position.column - 1, newLine.length, 0);\n  if (!slicedTokens) {\n    return text;\n  }\n  const newText = fixBracketsInLine(slicedTokens, languageConfigurationService);\n  return newText;\n}", "map": {"version": 3, "names": ["assertNever", "DeferredPromise", "CancellationToken", "SetMap", "onUnexpectedExternalError", "Position", "Range", "fixBracketsInLine", "SingleTextEdit", "getReadonlyEmptyArray", "<PERSON>ni<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text", "provideInlineCompletions", "_x", "_x2", "_x3", "_x4", "_provideInlineCompletions", "apply", "arguments", "_asyncToGenerator", "registry", "position<PERSON>r<PERSON>ang<PERSON>", "model", "context", "token", "None", "languageConfigurationService", "defaultReplaceRange", "getDefaultRange", "providers", "all", "multiMap", "provider", "groupId", "add", "getPreferredProviders", "yieldsToGroupIds", "result", "get", "p", "push", "states", "Map", "seen", "Set", "findPreferredProviderCircle", "stack", "has", "preferred", "c", "delete", "undefined", "processProvider", "state", "circle", "Error", "map", "s", "toString", "join", "deferred<PERSON><PERSON><PERSON>", "set", "items", "length", "completions", "provideInlineEdits", "e", "then", "complete", "error", "providerResults", "Promise", "_ref2", "_x5", "itemsByHash", "lists", "list", "InlineCompletionList", "item", "inlineCompletionItem", "InlineCompletionItem", "from", "hash", "InlineCompletionProviderResult", "Array", "values", "keys", "constructor", "hashs", "dispose", "removeRef", "inlineCompletions", "refCount", "addRef", "freeInlineCompletions", "inlineCompletion", "source", "textModel", "insertText", "snippetInfo", "range", "lift", "completeBracketPairs", "closeBrackets", "getStartPosition", "diff", "startLineNumber", "startColumn", "endLineNumber", "endColumn", "preBracketCompletionLength", "snippet", "parse", "children", "value", "command", "additionalTextEdits", "filterText", "sourceInlineCompletion", "replace", "<PERSON><PERSON><PERSON><PERSON>", "updatedRange", "JSON", "stringify", "toSingleTextEdit", "position", "word", "getWordAtPosition", "maxColumn", "getLineMaxColumn", "lineNumber", "fromPositions", "with", "text", "lineStart", "get<PERSON>ineC<PERSON>nt", "substring", "column", "newLine", "newTokens", "tokenization", "tokenizeLineWithEdit", "slicedTokens", "sliceAndInflate", "newText"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/contrib/inlineCompletions/browser/model/provideInlineCompletions.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { assertNever } from '../../../../../base/common/assert.js';\nimport { DeferredPromise } from '../../../../../base/common/async.js';\nimport { CancellationToken } from '../../../../../base/common/cancellation.js';\nimport { SetMap } from '../../../../../base/common/map.js';\nimport { onUnexpectedExternalError } from '../../../../../base/common/errors.js';\nimport { Position } from '../../../../common/core/position.js';\nimport { Range } from '../../../../common/core/range.js';\nimport { fixBracketsInLine } from '../../../../common/model/bracketPairsTextModelPart/fixBrackets.js';\nimport { SingleTextEdit } from '../../../../common/core/textEdit.js';\nimport { getReadonlyEmptyArray } from '../utils.js';\nimport { SnippetParser, Text } from '../../../snippet/browser/snippetParser.js';\nexport async function provideInlineCompletions(registry, positionOrRange, model, context, token = CancellationToken.None, languageConfigurationService) {\n    // Important: Don't use position after the await calls, as the model could have been changed in the meantime!\n    const defaultReplaceRange = positionOrRange instanceof Position ? getDefaultRange(positionOrRange, model) : positionOrRange;\n    const providers = registry.all(model);\n    const multiMap = new SetMap();\n    for (const provider of providers) {\n        if (provider.groupId) {\n            multiMap.add(provider.groupId, provider);\n        }\n    }\n    function getPreferredProviders(provider) {\n        if (!provider.yieldsToGroupIds) {\n            return [];\n        }\n        const result = [];\n        for (const groupId of provider.yieldsToGroupIds || []) {\n            const providers = multiMap.get(groupId);\n            for (const p of providers) {\n                result.push(p);\n            }\n        }\n        return result;\n    }\n    const states = new Map();\n    const seen = new Set();\n    function findPreferredProviderCircle(provider, stack) {\n        stack = [...stack, provider];\n        if (seen.has(provider)) {\n            return stack;\n        }\n        seen.add(provider);\n        try {\n            const preferred = getPreferredProviders(provider);\n            for (const p of preferred) {\n                const c = findPreferredProviderCircle(p, stack);\n                if (c) {\n                    return c;\n                }\n            }\n        }\n        finally {\n            seen.delete(provider);\n        }\n        return undefined;\n    }\n    function processProvider(provider) {\n        const state = states.get(provider);\n        if (state) {\n            return state;\n        }\n        const circle = findPreferredProviderCircle(provider, []);\n        if (circle) {\n            onUnexpectedExternalError(new Error(`Inline completions: cyclic yield-to dependency detected. Path: ${circle.map(s => s.toString ? s.toString() : ('' + s)).join(' -> ')}`));\n        }\n        const deferredPromise = new DeferredPromise();\n        states.set(provider, deferredPromise.p);\n        (async () => {\n            if (!circle) {\n                const preferred = getPreferredProviders(provider);\n                for (const p of preferred) {\n                    const result = await processProvider(p);\n                    if (result && result.items.length > 0) {\n                        // Skip provider\n                        return undefined;\n                    }\n                }\n            }\n            try {\n                if (positionOrRange instanceof Position) {\n                    const completions = await provider.provideInlineCompletions(model, positionOrRange, context, token);\n                    return completions;\n                }\n                else {\n                    const completions = await provider.provideInlineEdits?.(model, positionOrRange, context, token);\n                    return completions;\n                }\n            }\n            catch (e) {\n                onUnexpectedExternalError(e);\n                return undefined;\n            }\n        })().then(c => deferredPromise.complete(c), e => deferredPromise.error(e));\n        return deferredPromise.p;\n    }\n    const providerResults = await Promise.all(providers.map(async (provider) => ({ provider, completions: await processProvider(provider) })));\n    const itemsByHash = new Map();\n    const lists = [];\n    for (const result of providerResults) {\n        const completions = result.completions;\n        if (!completions) {\n            continue;\n        }\n        const list = new InlineCompletionList(completions, result.provider);\n        lists.push(list);\n        for (const item of completions.items) {\n            const inlineCompletionItem = InlineCompletionItem.from(item, list, defaultReplaceRange, model, languageConfigurationService);\n            itemsByHash.set(inlineCompletionItem.hash(), inlineCompletionItem);\n        }\n    }\n    return new InlineCompletionProviderResult(Array.from(itemsByHash.values()), new Set(itemsByHash.keys()), lists);\n}\nexport class InlineCompletionProviderResult {\n    constructor(\n    /**\n     * Free of duplicates.\n     */\n    completions, hashs, providerResults) {\n        this.completions = completions;\n        this.hashs = hashs;\n        this.providerResults = providerResults;\n    }\n    has(item) {\n        return this.hashs.has(item.hash());\n    }\n    dispose() {\n        for (const result of this.providerResults) {\n            result.removeRef();\n        }\n    }\n}\n/**\n * A ref counted pointer to the computed `InlineCompletions` and the `InlineCompletionsProvider` that\n * computed them.\n */\nexport class InlineCompletionList {\n    constructor(inlineCompletions, provider) {\n        this.inlineCompletions = inlineCompletions;\n        this.provider = provider;\n        this.refCount = 1;\n    }\n    addRef() {\n        this.refCount++;\n    }\n    removeRef() {\n        this.refCount--;\n        if (this.refCount === 0) {\n            this.provider.freeInlineCompletions(this.inlineCompletions);\n        }\n    }\n}\nexport class InlineCompletionItem {\n    static from(inlineCompletion, source, defaultReplaceRange, textModel, languageConfigurationService) {\n        let insertText;\n        let snippetInfo;\n        let range = inlineCompletion.range ? Range.lift(inlineCompletion.range) : defaultReplaceRange;\n        if (typeof inlineCompletion.insertText === 'string') {\n            insertText = inlineCompletion.insertText;\n            if (languageConfigurationService && inlineCompletion.completeBracketPairs) {\n                insertText = closeBrackets(insertText, range.getStartPosition(), textModel, languageConfigurationService);\n                // Modify range depending on if brackets are added or removed\n                const diff = insertText.length - inlineCompletion.insertText.length;\n                if (diff !== 0) {\n                    range = new Range(range.startLineNumber, range.startColumn, range.endLineNumber, range.endColumn + diff);\n                }\n            }\n            snippetInfo = undefined;\n        }\n        else if ('snippet' in inlineCompletion.insertText) {\n            const preBracketCompletionLength = inlineCompletion.insertText.snippet.length;\n            if (languageConfigurationService && inlineCompletion.completeBracketPairs) {\n                inlineCompletion.insertText.snippet = closeBrackets(inlineCompletion.insertText.snippet, range.getStartPosition(), textModel, languageConfigurationService);\n                // Modify range depending on if brackets are added or removed\n                const diff = inlineCompletion.insertText.snippet.length - preBracketCompletionLength;\n                if (diff !== 0) {\n                    range = new Range(range.startLineNumber, range.startColumn, range.endLineNumber, range.endColumn + diff);\n                }\n            }\n            const snippet = new SnippetParser().parse(inlineCompletion.insertText.snippet);\n            if (snippet.children.length === 1 && snippet.children[0] instanceof Text) {\n                insertText = snippet.children[0].value;\n                snippetInfo = undefined;\n            }\n            else {\n                insertText = snippet.toString();\n                snippetInfo = {\n                    snippet: inlineCompletion.insertText.snippet,\n                    range: range\n                };\n            }\n        }\n        else {\n            assertNever(inlineCompletion.insertText);\n        }\n        return new InlineCompletionItem(insertText, inlineCompletion.command, range, insertText, snippetInfo, inlineCompletion.additionalTextEdits || getReadonlyEmptyArray(), inlineCompletion, source);\n    }\n    constructor(filterText, command, range, insertText, snippetInfo, additionalTextEdits, \n    /**\n     * A reference to the original inline completion this inline completion has been constructed from.\n     * Used for event data to ensure referential equality.\n    */\n    sourceInlineCompletion, \n    /**\n     * A reference to the original inline completion list this inline completion has been constructed from.\n     * Used for event data to ensure referential equality.\n    */\n    source) {\n        this.filterText = filterText;\n        this.command = command;\n        this.range = range;\n        this.insertText = insertText;\n        this.snippetInfo = snippetInfo;\n        this.additionalTextEdits = additionalTextEdits;\n        this.sourceInlineCompletion = sourceInlineCompletion;\n        this.source = source;\n        filterText = filterText.replace(/\\r\\n|\\r/g, '\\n');\n        insertText = filterText.replace(/\\r\\n|\\r/g, '\\n');\n    }\n    withRange(updatedRange) {\n        return new InlineCompletionItem(this.filterText, this.command, updatedRange, this.insertText, this.snippetInfo, this.additionalTextEdits, this.sourceInlineCompletion, this.source);\n    }\n    hash() {\n        return JSON.stringify({ insertText: this.insertText, range: this.range.toString() });\n    }\n    toSingleTextEdit() {\n        return new SingleTextEdit(this.range, this.insertText);\n    }\n}\nfunction getDefaultRange(position, model) {\n    const word = model.getWordAtPosition(position);\n    const maxColumn = model.getLineMaxColumn(position.lineNumber);\n    // By default, always replace up until the end of the current line.\n    // This default might be subject to change!\n    return word\n        ? new Range(position.lineNumber, word.startColumn, position.lineNumber, maxColumn)\n        : Range.fromPositions(position, position.with(undefined, maxColumn));\n}\nfunction closeBrackets(text, position, model, languageConfigurationService) {\n    const lineStart = model.getLineContent(position.lineNumber).substring(0, position.column - 1);\n    const newLine = lineStart + text;\n    const newTokens = model.tokenization.tokenizeLineWithEdit(position, newLine.length - (position.column - 1), text);\n    const slicedTokens = newTokens?.sliceAndInflate(position.column - 1, newLine.length, 0);\n    if (!slicedTokens) {\n        return text;\n    }\n    const newText = fixBracketsInLine(slicedTokens, languageConfigurationService);\n    return newText;\n}\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA,SAASA,WAAW,QAAQ,sCAAsC;AAClE,SAASC,eAAe,QAAQ,qCAAqC;AACrE,SAASC,iBAAiB,QAAQ,4CAA4C;AAC9E,SAASC,MAAM,QAAQ,mCAAmC;AAC1D,SAASC,yBAAyB,QAAQ,sCAAsC;AAChF,SAASC,QAAQ,QAAQ,qCAAqC;AAC9D,SAASC,KAAK,QAAQ,kCAAkC;AACxD,SAASC,iBAAiB,QAAQ,mEAAmE;AACrG,SAASC,cAAc,QAAQ,qCAAqC;AACpE,SAASC,qBAAqB,QAAQ,aAAa;AACnD,SAASC,aAAa,EAAEC,IAAI,QAAQ,2CAA2C;AAC/E,gBAAsBC,wBAAwBA,CAAAC,EAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;EAAA,OAAAC,yBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAoG7C,SAAAF,0BAAA;EAAAA,yBAAA,GAAAG,iBAAA,CApGM,WAAwCC,QAAQ,EAAEC,eAAe,EAAEC,KAAK,EAAEC,OAAO,EAAEC,KAAK,GAAGvB,iBAAiB,CAACwB,IAAI,EAAEC,4BAA4B,EAAE;IACpJ;IACA,MAAMC,mBAAmB,GAAGN,eAAe,YAAYjB,QAAQ,GAAGwB,eAAe,CAACP,eAAe,EAAEC,KAAK,CAAC,GAAGD,eAAe;IAC3H,MAAMQ,SAAS,GAAGT,QAAQ,CAACU,GAAG,CAACR,KAAK,CAAC;IACrC,MAAMS,QAAQ,GAAG,IAAI7B,MAAM,CAAC,CAAC;IAC7B,KAAK,MAAM8B,QAAQ,IAAIH,SAAS,EAAE;MAC9B,IAAIG,QAAQ,CAACC,OAAO,EAAE;QAClBF,QAAQ,CAACG,GAAG,CAACF,QAAQ,CAACC,OAAO,EAAED,QAAQ,CAAC;MAC5C;IACJ;IACA,SAASG,qBAAqBA,CAACH,QAAQ,EAAE;MACrC,IAAI,CAACA,QAAQ,CAACI,gBAAgB,EAAE;QAC5B,OAAO,EAAE;MACb;MACA,MAAMC,MAAM,GAAG,EAAE;MACjB,KAAK,MAAMJ,OAAO,IAAID,QAAQ,CAACI,gBAAgB,IAAI,EAAE,EAAE;QACnD,MAAMP,SAAS,GAAGE,QAAQ,CAACO,GAAG,CAACL,OAAO,CAAC;QACvC,KAAK,MAAMM,CAAC,IAAIV,SAAS,EAAE;UACvBQ,MAAM,CAACG,IAAI,CAACD,CAAC,CAAC;QAClB;MACJ;MACA,OAAOF,MAAM;IACjB;IACA,MAAMI,MAAM,GAAG,IAAIC,GAAG,CAAC,CAAC;IACxB,MAAMC,IAAI,GAAG,IAAIC,GAAG,CAAC,CAAC;IACtB,SAASC,2BAA2BA,CAACb,QAAQ,EAAEc,KAAK,EAAE;MAClDA,KAAK,GAAG,CAAC,GAAGA,KAAK,EAAEd,QAAQ,CAAC;MAC5B,IAAIW,IAAI,CAACI,GAAG,CAACf,QAAQ,CAAC,EAAE;QACpB,OAAOc,KAAK;MAChB;MACAH,IAAI,CAACT,GAAG,CAACF,QAAQ,CAAC;MAClB,IAAI;QACA,MAAMgB,SAAS,GAAGb,qBAAqB,CAACH,QAAQ,CAAC;QACjD,KAAK,MAAMO,CAAC,IAAIS,SAAS,EAAE;UACvB,MAAMC,CAAC,GAAGJ,2BAA2B,CAACN,CAAC,EAAEO,KAAK,CAAC;UAC/C,IAAIG,CAAC,EAAE;YACH,OAAOA,CAAC;UACZ;QACJ;MACJ,CAAC,SACO;QACJN,IAAI,CAACO,MAAM,CAAClB,QAAQ,CAAC;MACzB;MACA,OAAOmB,SAAS;IACpB;IACA,SAASC,eAAeA,CAACpB,QAAQ,EAAE;MAC/B,MAAMqB,KAAK,GAAGZ,MAAM,CAACH,GAAG,CAACN,QAAQ,CAAC;MAClC,IAAIqB,KAAK,EAAE;QACP,OAAOA,KAAK;MAChB;MACA,MAAMC,MAAM,GAAGT,2BAA2B,CAACb,QAAQ,EAAE,EAAE,CAAC;MACxD,IAAIsB,MAAM,EAAE;QACRnD,yBAAyB,CAAC,IAAIoD,KAAK,CAAC,kEAAkED,MAAM,CAACE,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,QAAQ,GAAGD,CAAC,CAACC,QAAQ,CAAC,CAAC,GAAI,EAAE,GAAGD,CAAE,CAAC,CAACE,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;MAChL;MACA,MAAMC,eAAe,GAAG,IAAI5D,eAAe,CAAC,CAAC;MAC7CyC,MAAM,CAACoB,GAAG,CAAC7B,QAAQ,EAAE4B,eAAe,CAACrB,CAAC,CAAC;MACvCpB,iBAAA,CAAC,aAAY;QACT,IAAI,CAACmC,MAAM,EAAE;UACT,MAAMN,SAAS,GAAGb,qBAAqB,CAACH,QAAQ,CAAC;UACjD,KAAK,MAAMO,CAAC,IAAIS,SAAS,EAAE;YACvB,MAAMX,MAAM,SAASe,eAAe,CAACb,CAAC,CAAC;YACvC,IAAIF,MAAM,IAAIA,MAAM,CAACyB,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;cACnC;cACA,OAAOZ,SAAS;YACpB;UACJ;QACJ;QACA,IAAI;UACA,IAAI9B,eAAe,YAAYjB,QAAQ,EAAE;YACrC,MAAM4D,WAAW,SAAShC,QAAQ,CAACrB,wBAAwB,CAACW,KAAK,EAAED,eAAe,EAAEE,OAAO,EAAEC,KAAK,CAAC;YACnG,OAAOwC,WAAW;UACtB,CAAC,MACI;YACD,MAAMA,WAAW,SAAShC,QAAQ,CAACiC,kBAAkB,GAAG3C,KAAK,EAAED,eAAe,EAAEE,OAAO,EAAEC,KAAK,CAAC;YAC/F,OAAOwC,WAAW;UACtB;QACJ,CAAC,CACD,OAAOE,CAAC,EAAE;UACN/D,yBAAyB,CAAC+D,CAAC,CAAC;UAC5B,OAAOf,SAAS;QACpB;MACJ,CAAC,EAAE,CAAC,CAACgB,IAAI,CAAClB,CAAC,IAAIW,eAAe,CAACQ,QAAQ,CAACnB,CAAC,CAAC,EAAEiB,CAAC,IAAIN,eAAe,CAACS,KAAK,CAACH,CAAC,CAAC,CAAC;MAC1E,OAAON,eAAe,CAACrB,CAAC;IAC5B;IACA,MAAM+B,eAAe,SAASC,OAAO,CAACzC,GAAG,CAACD,SAAS,CAAC2B,GAAG;MAAA,IAAAgB,KAAA,GAAArD,iBAAA,CAAC,WAAOa,QAAQ;QAAA,OAAM;UAAEA,QAAQ;UAAEgC,WAAW,QAAQZ,eAAe,CAACpB,QAAQ;QAAE,CAAC;MAAA,CAAC;MAAA,iBAAAyC,GAAA;QAAA,OAAAD,KAAA,CAAAvD,KAAA,OAAAC,SAAA;MAAA;IAAA,IAAC,CAAC;IAC1I,MAAMwD,WAAW,GAAG,IAAIhC,GAAG,CAAC,CAAC;IAC7B,MAAMiC,KAAK,GAAG,EAAE;IAChB,KAAK,MAAMtC,MAAM,IAAIiC,eAAe,EAAE;MAClC,MAAMN,WAAW,GAAG3B,MAAM,CAAC2B,WAAW;MACtC,IAAI,CAACA,WAAW,EAAE;QACd;MACJ;MACA,MAAMY,IAAI,GAAG,IAAIC,oBAAoB,CAACb,WAAW,EAAE3B,MAAM,CAACL,QAAQ,CAAC;MACnE2C,KAAK,CAACnC,IAAI,CAACoC,IAAI,CAAC;MAChB,KAAK,MAAME,IAAI,IAAId,WAAW,CAACF,KAAK,EAAE;QAClC,MAAMiB,oBAAoB,GAAGC,oBAAoB,CAACC,IAAI,CAACH,IAAI,EAAEF,IAAI,EAAEjD,mBAAmB,EAAEL,KAAK,EAAEI,4BAA4B,CAAC;QAC5HgD,WAAW,CAACb,GAAG,CAACkB,oBAAoB,CAACG,IAAI,CAAC,CAAC,EAAEH,oBAAoB,CAAC;MACtE;IACJ;IACA,OAAO,IAAII,8BAA8B,CAACC,KAAK,CAACH,IAAI,CAACP,WAAW,CAACW,MAAM,CAAC,CAAC,CAAC,EAAE,IAAIzC,GAAG,CAAC8B,WAAW,CAACY,IAAI,CAAC,CAAC,CAAC,EAAEX,KAAK,CAAC;EACnH,CAAC;EAAA,OAAA3D,yBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AACD,OAAO,MAAMiE,8BAA8B,CAAC;EACxCI,WAAWA;EACX;AACJ;AACA;EACIvB,WAAW,EAAEwB,KAAK,EAAElB,eAAe,EAAE;IACjC,IAAI,CAACN,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACwB,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAClB,eAAe,GAAGA,eAAe;EAC1C;EACAvB,GAAGA,CAAC+B,IAAI,EAAE;IACN,OAAO,IAAI,CAACU,KAAK,CAACzC,GAAG,CAAC+B,IAAI,CAACI,IAAI,CAAC,CAAC,CAAC;EACtC;EACAO,OAAOA,CAAA,EAAG;IACN,KAAK,MAAMpD,MAAM,IAAI,IAAI,CAACiC,eAAe,EAAE;MACvCjC,MAAM,CAACqD,SAAS,CAAC,CAAC;IACtB;EACJ;AACJ;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMb,oBAAoB,CAAC;EAC9BU,WAAWA,CAACI,iBAAiB,EAAE3D,QAAQ,EAAE;IACrC,IAAI,CAAC2D,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAAC3D,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAAC4D,QAAQ,GAAG,CAAC;EACrB;EACAC,MAAMA,CAAA,EAAG;IACL,IAAI,CAACD,QAAQ,EAAE;EACnB;EACAF,SAASA,CAAA,EAAG;IACR,IAAI,CAACE,QAAQ,EAAE;IACf,IAAI,IAAI,CAACA,QAAQ,KAAK,CAAC,EAAE;MACrB,IAAI,CAAC5D,QAAQ,CAAC8D,qBAAqB,CAAC,IAAI,CAACH,iBAAiB,CAAC;IAC/D;EACJ;AACJ;AACA,OAAO,MAAMX,oBAAoB,CAAC;EAC9B,OAAOC,IAAIA,CAACc,gBAAgB,EAAEC,MAAM,EAAErE,mBAAmB,EAAEsE,SAAS,EAAEvE,4BAA4B,EAAE;IAChG,IAAIwE,UAAU;IACd,IAAIC,WAAW;IACf,IAAIC,KAAK,GAAGL,gBAAgB,CAACK,KAAK,GAAG/F,KAAK,CAACgG,IAAI,CAACN,gBAAgB,CAACK,KAAK,CAAC,GAAGzE,mBAAmB;IAC7F,IAAI,OAAOoE,gBAAgB,CAACG,UAAU,KAAK,QAAQ,EAAE;MACjDA,UAAU,GAAGH,gBAAgB,CAACG,UAAU;MACxC,IAAIxE,4BAA4B,IAAIqE,gBAAgB,CAACO,oBAAoB,EAAE;QACvEJ,UAAU,GAAGK,aAAa,CAACL,UAAU,EAAEE,KAAK,CAACI,gBAAgB,CAAC,CAAC,EAAEP,SAAS,EAAEvE,4BAA4B,CAAC;QACzG;QACA,MAAM+E,IAAI,GAAGP,UAAU,CAACnC,MAAM,GAAGgC,gBAAgB,CAACG,UAAU,CAACnC,MAAM;QACnE,IAAI0C,IAAI,KAAK,CAAC,EAAE;UACZL,KAAK,GAAG,IAAI/F,KAAK,CAAC+F,KAAK,CAACM,eAAe,EAAEN,KAAK,CAACO,WAAW,EAAEP,KAAK,CAACQ,aAAa,EAAER,KAAK,CAACS,SAAS,GAAGJ,IAAI,CAAC;QAC5G;MACJ;MACAN,WAAW,GAAGhD,SAAS;IAC3B,CAAC,MACI,IAAI,SAAS,IAAI4C,gBAAgB,CAACG,UAAU,EAAE;MAC/C,MAAMY,0BAA0B,GAAGf,gBAAgB,CAACG,UAAU,CAACa,OAAO,CAAChD,MAAM;MAC7E,IAAIrC,4BAA4B,IAAIqE,gBAAgB,CAACO,oBAAoB,EAAE;QACvEP,gBAAgB,CAACG,UAAU,CAACa,OAAO,GAAGR,aAAa,CAACR,gBAAgB,CAACG,UAAU,CAACa,OAAO,EAAEX,KAAK,CAACI,gBAAgB,CAAC,CAAC,EAAEP,SAAS,EAAEvE,4BAA4B,CAAC;QAC3J;QACA,MAAM+E,IAAI,GAAGV,gBAAgB,CAACG,UAAU,CAACa,OAAO,CAAChD,MAAM,GAAG+C,0BAA0B;QACpF,IAAIL,IAAI,KAAK,CAAC,EAAE;UACZL,KAAK,GAAG,IAAI/F,KAAK,CAAC+F,KAAK,CAACM,eAAe,EAAEN,KAAK,CAACO,WAAW,EAAEP,KAAK,CAACQ,aAAa,EAAER,KAAK,CAACS,SAAS,GAAGJ,IAAI,CAAC;QAC5G;MACJ;MACA,MAAMM,OAAO,GAAG,IAAItG,aAAa,CAAC,CAAC,CAACuG,KAAK,CAACjB,gBAAgB,CAACG,UAAU,CAACa,OAAO,CAAC;MAC9E,IAAIA,OAAO,CAACE,QAAQ,CAAClD,MAAM,KAAK,CAAC,IAAIgD,OAAO,CAACE,QAAQ,CAAC,CAAC,CAAC,YAAYvG,IAAI,EAAE;QACtEwF,UAAU,GAAGa,OAAO,CAACE,QAAQ,CAAC,CAAC,CAAC,CAACC,KAAK;QACtCf,WAAW,GAAGhD,SAAS;MAC3B,CAAC,MACI;QACD+C,UAAU,GAAGa,OAAO,CAACrD,QAAQ,CAAC,CAAC;QAC/ByC,WAAW,GAAG;UACVY,OAAO,EAAEhB,gBAAgB,CAACG,UAAU,CAACa,OAAO;UAC5CX,KAAK,EAAEA;QACX,CAAC;MACL;IACJ,CAAC,MACI;MACDrG,WAAW,CAACgG,gBAAgB,CAACG,UAAU,CAAC;IAC5C;IACA,OAAO,IAAIlB,oBAAoB,CAACkB,UAAU,EAAEH,gBAAgB,CAACoB,OAAO,EAAEf,KAAK,EAAEF,UAAU,EAAEC,WAAW,EAAEJ,gBAAgB,CAACqB,mBAAmB,IAAI5G,qBAAqB,CAAC,CAAC,EAAEuF,gBAAgB,EAAEC,MAAM,CAAC;EACpM;EACAT,WAAWA,CAAC8B,UAAU,EAAEF,OAAO,EAAEf,KAAK,EAAEF,UAAU,EAAEC,WAAW,EAAEiB,mBAAmB;EACpF;AACJ;AACA;AACA;EACIE,sBAAsB;EACtB;AACJ;AACA;AACA;EACItB,MAAM,EAAE;IACJ,IAAI,CAACqB,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACF,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACf,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACF,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACiB,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACE,sBAAsB,GAAGA,sBAAsB;IACpD,IAAI,CAACtB,MAAM,GAAGA,MAAM;IACpBqB,UAAU,GAAGA,UAAU,CAACE,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC;IACjDrB,UAAU,GAAGmB,UAAU,CAACE,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC;EACrD;EACAC,SAASA,CAACC,YAAY,EAAE;IACpB,OAAO,IAAIzC,oBAAoB,CAAC,IAAI,CAACqC,UAAU,EAAE,IAAI,CAACF,OAAO,EAAEM,YAAY,EAAE,IAAI,CAACvB,UAAU,EAAE,IAAI,CAACC,WAAW,EAAE,IAAI,CAACiB,mBAAmB,EAAE,IAAI,CAACE,sBAAsB,EAAE,IAAI,CAACtB,MAAM,CAAC;EACvL;EACAd,IAAIA,CAAA,EAAG;IACH,OAAOwC,IAAI,CAACC,SAAS,CAAC;MAAEzB,UAAU,EAAE,IAAI,CAACA,UAAU;MAAEE,KAAK,EAAE,IAAI,CAACA,KAAK,CAAC1C,QAAQ,CAAC;IAAE,CAAC,CAAC;EACxF;EACAkE,gBAAgBA,CAAA,EAAG;IACf,OAAO,IAAIrH,cAAc,CAAC,IAAI,CAAC6F,KAAK,EAAE,IAAI,CAACF,UAAU,CAAC;EAC1D;AACJ;AACA,SAAStE,eAAeA,CAACiG,QAAQ,EAAEvG,KAAK,EAAE;EACtC,MAAMwG,IAAI,GAAGxG,KAAK,CAACyG,iBAAiB,CAACF,QAAQ,CAAC;EAC9C,MAAMG,SAAS,GAAG1G,KAAK,CAAC2G,gBAAgB,CAACJ,QAAQ,CAACK,UAAU,CAAC;EAC7D;EACA;EACA,OAAOJ,IAAI,GACL,IAAIzH,KAAK,CAACwH,QAAQ,CAACK,UAAU,EAAEJ,IAAI,CAACnB,WAAW,EAAEkB,QAAQ,CAACK,UAAU,EAAEF,SAAS,CAAC,GAChF3H,KAAK,CAAC8H,aAAa,CAACN,QAAQ,EAAEA,QAAQ,CAACO,IAAI,CAACjF,SAAS,EAAE6E,SAAS,CAAC,CAAC;AAC5E;AACA,SAASzB,aAAaA,CAAC8B,IAAI,EAAER,QAAQ,EAAEvG,KAAK,EAAEI,4BAA4B,EAAE;EACxE,MAAM4G,SAAS,GAAGhH,KAAK,CAACiH,cAAc,CAACV,QAAQ,CAACK,UAAU,CAAC,CAACM,SAAS,CAAC,CAAC,EAAEX,QAAQ,CAACY,MAAM,GAAG,CAAC,CAAC;EAC7F,MAAMC,OAAO,GAAGJ,SAAS,GAAGD,IAAI;EAChC,MAAMM,SAAS,GAAGrH,KAAK,CAACsH,YAAY,CAACC,oBAAoB,CAAChB,QAAQ,EAAEa,OAAO,CAAC3E,MAAM,IAAI8D,QAAQ,CAACY,MAAM,GAAG,CAAC,CAAC,EAAEJ,IAAI,CAAC;EACjH,MAAMS,YAAY,GAAGH,SAAS,EAAEI,eAAe,CAAClB,QAAQ,CAACY,MAAM,GAAG,CAAC,EAAEC,OAAO,CAAC3E,MAAM,EAAE,CAAC,CAAC;EACvF,IAAI,CAAC+E,YAAY,EAAE;IACf,OAAOT,IAAI;EACf;EACA,MAAMW,OAAO,GAAG1I,iBAAiB,CAACwI,YAAY,EAAEpH,4BAA4B,CAAC;EAC7E,OAAOsH,OAAO;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}