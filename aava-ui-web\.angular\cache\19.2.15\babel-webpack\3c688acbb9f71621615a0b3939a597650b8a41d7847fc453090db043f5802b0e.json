{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { DecimalPipe } from '@angular/common';\nimport { ReactiveFormsModule, FormsModule, Validators } from '@angular/forms';\nimport knowledgeBaseLabels from '../constants/knowledge-base.json';\nimport { <PERSON>liderComponent, DropdownComponent, AvaTextboxComponent, AvaTextareaComponent, FileUploadComponent, ButtonComponent, TableComponent } from '@ava/play-comp-library';\nimport { LucideAngularModule } from 'lucide-angular';\nimport { map } from 'rxjs/internal/operators/map';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@shared/services/knowledge-base.service\";\nimport * as i5 from \"lucide-angular\";\nconst _c0 = () => ({\n  background: \"linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)\",\n  \"--button-effect-color\": \"33, 90, 214\"\n});\nconst _c1 = () => ({\n  background: \"linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)\",\n  \"--button-effect-color\": \"33, 90, 214\",\n  color: \"#fff\",\n  border: \"none\"\n});\nconst _c2 = () => ({});\nfunction CreateKnowledgeBaseComponent_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 20);\n    i0.ɵɵtext(1, \"Knowledge Base Description\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CreateKnowledgeBaseComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵelement(1, \"ava-textbox\", 21)(2, \"ava-textarea\", 22);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"placeholder\", ctx_r0.kbLabels.placeholderKnowledgeBase);\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r0.kbLabels.knowledgeBaseName);\n    i0.ɵɵproperty(\"control\", ctx_r0.getControl(\"name\"))(\"error\", ctx_r0.getFieldError(\"name\"))(\"fullWidth\", true)(\"required\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r0.kbLabels.description);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", ctx_r0.kbLabels.placeholderDescription);\n    i0.ɵɵproperty(\"control\", ctx_r0.getControl(\"description\"))(\"error\", ctx_r0.getFieldError(\"description\"))(\"fullWidth\", true)(\"required\", true);\n  }\n}\nfunction CreateKnowledgeBaseComponent_ng_container_18_ava_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ava-button\", 28);\n    i0.ɵɵlistener(\"userClick\", function CreateKnowledgeBaseComponent_ng_container_18_ava_button_5_Template_ava_button_userClick_0_listener() {\n      const retriever_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.selectRetriever(retriever_r3));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const retriever_r3 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"label\", retriever_r3)(\"variant\", ctx_r0.selectedRetriever === retriever_r3 ? \"primary\" : \"secondary\")(\"state\", ctx_r0.selectedRetriever === retriever_r3 ? \"active\" : \"default\")(\"disabled\", ctx_r0.isEditMode)(\"customStyles\", ctx_r0.selectedRetriever === retriever_r3 ? i0.ɵɵpureFunction0(5, _c1) : i0.ɵɵpureFunction0(6, _c2));\n  }\n}\nfunction CreateKnowledgeBaseComponent_ng_container_18_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"h3\", 23);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 29)(4, \"ava-slider\", 30);\n    i0.ɵɵlistener(\"valueChange\", function CreateKnowledgeBaseComponent_ng_container_18_div_6_Template_ava_slider_valueChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.splitSizeChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 31)(6, \"ava-textbox\", 32);\n    i0.ɵɵlistener(\"change\", function CreateKnowledgeBaseComponent_ng_container_18_div_6_Template_ava_textbox_change_6_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.onSliderInputChange($event, \"splitSize\"));\n    });\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.kbLabels.splitSize);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isEditMode)(\"value\", ctx_r0.splitSize)(\"min\", 100)(\"max\", 20000)(\"step\", 1)(\"showTooltip\", false);\n  }\n}\nfunction CreateKnowledgeBaseComponent_ng_container_18_div_7_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.kbLabels.parentChildSplitValidation, \" \");\n  }\n}\nfunction CreateKnowledgeBaseComponent_ng_container_18_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"div\", 33)(2, \"h3\", 23);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 29)(5, \"ava-slider\", 34);\n    i0.ɵɵlistener(\"valueChange\", function CreateKnowledgeBaseComponent_ng_container_18_div_7_Template_ava_slider_valueChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.onParentSplitSizeChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 31)(7, \"ava-textbox\", 35);\n    i0.ɵɵlistener(\"change\", function CreateKnowledgeBaseComponent_ng_container_18_div_7_Template_ava_textbox_change_7_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.onSliderInputChange($event, \"parentSplitSize\"));\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(8, \"div\", 33)(9, \"h3\", 23);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 29)(12, \"ava-slider\", 36);\n    i0.ɵɵlistener(\"valueChange\", function CreateKnowledgeBaseComponent_ng_container_18_div_7_Template_ava_slider_valueChange_12_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.onChildSplitSizeChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 31)(14, \"ava-textbox\", 37);\n    i0.ɵɵlistener(\"change\", function CreateKnowledgeBaseComponent_ng_container_18_div_7_Template_ava_textbox_change_14_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.onSliderInputChange($event, \"childSplitSize\"));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(15, CreateKnowledgeBaseComponent_ng_container_18_div_7_div_15_Template, 2, 1, \"div\", 18);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.kbLabels.parentSplitSize);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isEditMode)(\"value\", ctx_r0.parentSplitSize)(\"min\", 100)(\"max\", 20000)(\"step\", 1)(\"showTooltip\", false);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r0.kbLabels.childSplitSize);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isEditMode)(\"value\", ctx_r0.childSplitSize)(\"min\", 100)(\"max\", 20000)(\"step\", 1)(\"showTooltip\", false);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.selectedRetriever === ctx_r0.kbLabels.parentDoc && ctx_r0.parentSplitSize <= ctx_r0.childSplitSize);\n  }\n}\nfunction CreateKnowledgeBaseComponent_ng_container_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 11)(2, \"h3\", 23);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 24);\n    i0.ɵɵtemplate(5, CreateKnowledgeBaseComponent_ng_container_18_ava_button_5_Template, 1, 7, \"ava-button\", 25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, CreateKnowledgeBaseComponent_ng_container_18_div_6_Template, 7, 7, \"div\", 26)(7, CreateKnowledgeBaseComponent_ng_container_18_div_7_Template, 16, 15, \"div\", 26);\n    i0.ɵɵelement(8, \"ava-dropdown\", 27);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.kbLabels.selectRetriever);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.retrieverOptions);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.selectedRetriever === \"Default\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.selectedRetriever === ctx_r0.kbLabels.parentDoc);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"dropdownTitle\", ctx_r0.kbLabels.selectEmbeddingModel);\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r0.kbLabels.embeddingModel);\n    i0.ɵɵproperty(\"options\", ctx_r0.embeddingModelOptions)(\"formControl\", ctx_r0.getControl(\"embeddingModel\"))(\"error\", ctx_r0.getFieldError(\"embeddingModel\"))(\"required\", true);\n  }\n}\nfunction CreateKnowledgeBaseComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"ava-table\", 39);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"columns\", ctx_r0.tableColumns)(\"data\", ctx_r0.tableData);\n  }\n}\nfunction CreateKnowledgeBaseComponent_hr_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"hr\");\n  }\n}\nfunction CreateKnowledgeBaseComponent_ng_container_23_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.kbLabels.zeroKbFileError, \" \");\n  }\n}\nfunction CreateKnowledgeBaseComponent_ng_container_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 40)(2, \"ava-file-upload\", 41);\n    i0.ɵɵlistener(\"filesListChanged\", function CreateKnowledgeBaseComponent_ng_container_23_Template_ava_file_upload_filesListChanged_2_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.filesListChanged($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(3, CreateKnowledgeBaseComponent_ng_container_23_div_3_Template, 2, 1, \"div\", 18);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"allowedFormats\", ctx_r0.allowedFormats)(\"showUploadButton\", ctx_r0.showUploadButton)(\"componentTitle\", ctx_r0.componentTitle)(\"showDialogCloseIcon\", ctx_r0.showDialogCloseIcon)(\"maxFileSize\", ctx_r0.maxFileSize)(\"preview\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.hasZeroKbFile);\n  }\n}\nfunction CreateKnowledgeBaseComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.kbLabels.fileUploadIsRequired, \" \");\n  }\n}\nfunction CreateKnowledgeBaseComponent_div_25_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"h3\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"ava-textbox\", 43)(4, \"ava-textbox\", 44)(5, \"ava-textbox\", 45);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.kbLabels.configSource);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r0.kbLabels.containerName);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", ctx_r0.kbLabels.placeholderContainerName);\n    i0.ɵɵproperty(\"control\", ctx_r0.getControl(\"containerName\"))(\"error\", ctx_r0.getFieldError(\"containerName\"))(\"required\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r0.kbLabels.accountName);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", ctx_r0.kbLabels.placeholderAccountName);\n    i0.ɵɵproperty(\"control\", ctx_r0.getControl(\"accountName\"))(\"error\", ctx_r0.getFieldError(\"accountName\"))(\"required\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r0.kbLabels.accountKey);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", ctx_r0.kbLabels.placeholderAccountKey);\n    i0.ɵɵproperty(\"control\", ctx_r0.getControl(\"accountKey\"))(\"error\", ctx_r0.getFieldError(\"accountKey\"))(\"required\", true);\n  }\n}\nfunction CreateKnowledgeBaseComponent_div_25_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"h3\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"ava-textbox\", 46)(4, \"ava-textbox\", 47)(5, \"ava-textbox\", 48)(6, \"ava-textbox\", 49);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.kbLabels.configSource);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r0.kbLabels.githubKey);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", ctx_r0.kbLabels.placeholderGithubKey);\n    i0.ɵɵproperty(\"control\", ctx_r0.getControl(\"githubKey\"))(\"error\", ctx_r0.getFieldError(\"githubKey\"))(\"required\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r0.kbLabels.githubAccount);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", ctx_r0.kbLabels.placeholderGithubAccount);\n    i0.ɵɵproperty(\"control\", ctx_r0.getControl(\"githubAccount\"))(\"error\", ctx_r0.getFieldError(\"githubAccount\"))(\"required\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r0.kbLabels.githubRepository);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", ctx_r0.kbLabels.placeholderGithubRepository);\n    i0.ɵɵproperty(\"control\", ctx_r0.getControl(\"githubRepo\"))(\"error\", ctx_r0.getFieldError(\"githubRepo\"))(\"required\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r0.kbLabels.githubBranch);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", ctx_r0.kbLabels.placeholderGithubBranch);\n    i0.ɵɵproperty(\"control\", ctx_r0.getControl(\"githubBranch\"))(\"error\", ctx_r0.getFieldError(\"githubBranch\"))(\"required\", true);\n  }\n}\nfunction CreateKnowledgeBaseComponent_div_25_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"h3\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"ava-textbox\", 50)(4, \"ava-textbox\", 51)(5, \"ava-textbox\", 52)(6, \"ava-textbox\", 53)(7, \"ava-textbox\", 54);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.kbLabels.configSource);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r0.kbLabels.clientId);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", ctx_r0.kbLabels.placeholderClientId);\n    i0.ɵɵproperty(\"control\", ctx_r0.getControl(\"clientId\"))(\"error\", ctx_r0.getFieldError(\"clientId\"))(\"required\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r0.kbLabels.clientSecret);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", ctx_r0.kbLabels.placeholderClientSecret);\n    i0.ɵɵproperty(\"control\", ctx_r0.getControl(\"clientSecret\"))(\"error\", ctx_r0.getFieldError(\"clientSecret\"))(\"required\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r0.kbLabels.tenantId);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", ctx_r0.kbLabels.placeholderTenantId);\n    i0.ɵɵproperty(\"control\", ctx_r0.getControl(\"tenantId\"))(\"error\", ctx_r0.getFieldError(\"tenantId\"))(\"required\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r0.kbLabels.siteName);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", ctx_r0.kbLabels.placeholderSiteName);\n    i0.ɵɵproperty(\"control\", ctx_r0.getControl(\"share pointSiteName\"))(\"error\", ctx_r0.getFieldError(\"share pointSiteName\"))(\"required\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r0.kbLabels.folderPath);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", ctx_r0.kbLabels.placeholderFolderPath);\n    i0.ɵɵproperty(\"control\", ctx_r0.getControl(\"sharepointFolderPath\"))(\"error\", ctx_r0.getFieldError(\"sharepointFolderPath\"))(\"required\", true);\n  }\n}\nfunction CreateKnowledgeBaseComponent_div_25_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"h3\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"ava-textbox\", 55)(4, \"ava-textbox\", 50)(5, \"ava-textbox\", 51)(6, \"ava-textbox\", 56)(7, \"ava-textbox\", 57)(8, \"ava-textbox\", 58)(9, \"ava-textbox\", 59);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.kbLabels.configSource);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r0.kbLabels.emailId);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", ctx_r0.kbLabels.placeholderEmailId);\n    i0.ɵɵproperty(\"control\", ctx_r0.getControl(\"email\"))(\"error\", ctx_r0.getFieldError(\"email\"))(\"required\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r0.kbLabels.confluenceClientId);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", ctx_r0.kbLabels.placeholderConfluenceClientId);\n    i0.ɵɵproperty(\"control\", ctx_r0.getControl(\"clientId\"))(\"error\", ctx_r0.getFieldError(\"clientId\"))(\"required\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r0.kbLabels.confluenceClientSecret);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", ctx_r0.kbLabels.placeholderConfluenceClientSecret);\n    i0.ɵɵproperty(\"control\", ctx_r0.getControl(\"clientSecret\"))(\"error\", ctx_r0.getFieldError(\"clientSecret\"))(\"required\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r0.kbLabels.apiToken);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", ctx_r0.kbLabels.placeholderApiToken);\n    i0.ɵɵproperty(\"control\", ctx_r0.getControl(\"apiToken\"))(\"error\", ctx_r0.getFieldError(\"apiToken\"))(\"required\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r0.kbLabels.baseUrl);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", ctx_r0.kbLabels.placeholderBaseUrl);\n    i0.ɵɵproperty(\"control\", ctx_r0.getControl(\"baseUrl\"))(\"error\", ctx_r0.getFieldError(\"baseUrl\"))(\"required\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r0.kbLabels.spaceKey);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", ctx_r0.kbLabels.placeholderSpaceKey);\n    i0.ɵɵproperty(\"control\", ctx_r0.getControl(\"spaceKey\"))(\"error\", ctx_r0.getFieldError(\"spaceKey\"))(\"required\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r0.kbLabels.overlap);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", ctx_r0.kbLabels.placeholderOverlap);\n    i0.ɵɵproperty(\"control\", ctx_r0.getControl(\"overlap\"))(\"error\", ctx_r0.getFieldError(\"overlap\"))(\"required\", true);\n  }\n}\nfunction CreateKnowledgeBaseComponent_div_25_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"h3\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 60);\n    i0.ɵɵelement(4, \"ava-dropdown\", 61);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"ava-textbox\", 62)(6, \"ava-textbox\", 63)(7, \"ava-textbox\", 64)(8, \"ava-textbox\", 65)(9, \"ava-textbox\", 66)(10, \"ava-textbox\", 67)(11, \"ava-textbox\", 59);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.kbLabels.configSource);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r0.kbLabels.selectScheme);\n    i0.ɵɵpropertyInterpolate(\"dropdownTitle\", ctx_r0.kbLabels.selectScheme);\n    i0.ɵɵproperty(\"options\", ctx_r0.schemeOptions)(\"control\", ctx_r0.getControl(\"scheme\"))(\"error\", ctx_r0.getFieldError(\"scheme\"))(\"required\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r0.kbLabels.dbHost);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", ctx_r0.kbLabels.placeholderHost);\n    i0.ɵɵproperty(\"control\", ctx_r0.getControl(\"host\"))(\"error\", ctx_r0.getFieldError(\"host\"))(\"required\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r0.kbLabels.dbPort);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", ctx_r0.kbLabels.placeholderPort);\n    i0.ɵɵproperty(\"control\", ctx_r0.getControl(\"port\"))(\"error\", ctx_r0.getFieldError(\"port\"))(\"required\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r0.kbLabels.dbUser);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", ctx_r0.kbLabels.placeholderUser);\n    i0.ɵɵproperty(\"control\", ctx_r0.getControl(\"user\"))(\"error\", ctx_r0.getFieldError(\"user\"))(\"required\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r0.kbLabels.dbPassword);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", ctx_r0.kbLabels.placeholderPassword);\n    i0.ɵɵproperty(\"control\", ctx_r0.getControl(\"password\"))(\"error\", ctx_r0.getFieldError(\"password\"))(\"required\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r0.kbLabels.dbName);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", ctx_r0.kbLabels.placeholderDbName);\n    i0.ɵɵproperty(\"control\", ctx_r0.getControl(\"dbname\"))(\"error\", ctx_r0.getFieldError(\"dbname\"))(\"required\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r0.kbLabels.dbQuery);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", ctx_r0.kbLabels.placeholderDbQuery);\n    i0.ɵɵproperty(\"control\", ctx_r0.getControl(\"query\"))(\"error\", ctx_r0.getFieldError(\"query\"))(\"required\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r0.kbLabels.overlap);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", ctx_r0.kbLabels.placeholderOverlap);\n    i0.ɵɵproperty(\"control\", ctx_r0.getControl(\"overlap\"))(\"error\", ctx_r0.getFieldError(\"overlap\"))(\"required\", true);\n  }\n}\nfunction CreateKnowledgeBaseComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵtemplate(1, CreateKnowledgeBaseComponent_div_25_ng_container_1_Template, 6, 16, \"ng-container\", 16)(2, CreateKnowledgeBaseComponent_div_25_ng_container_2_Template, 7, 21, \"ng-container\", 16)(3, CreateKnowledgeBaseComponent_div_25_ng_container_3_Template, 8, 26, \"ng-container\", 16)(4, CreateKnowledgeBaseComponent_div_25_ng_container_4_Template, 10, 36, \"ng-container\", 16)(5, CreateKnowledgeBaseComponent_div_25_ng_container_5_Template, 12, 42, \"ng-container\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.selectedUploadType === \"azure-blob\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.selectedUploadType === \"github\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.selectedUploadType === \"share-point\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.selectedUploadType === \"confluence-wiki\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.selectedUploadType === \"database\");\n  }\n}\nexport let CreateKnowledgeBaseComponent = /*#__PURE__*/(() => {\n  class CreateKnowledgeBaseComponent {\n    fb;\n    router;\n    _decimalPipe;\n    knowledgeBaseService;\n    route;\n    // Form group for the knowledge base creation form\n    knowledgeBaseForm;\n    // Stores route param for edit mode\n    knowledgeBaseId = null;\n    // Popup & submission state\n    submissionMessage = null;\n    submissionSuccess = false;\n    showInfoPopup = false;\n    fileUploadRequired = false;\n    // UI mode tracking\n    isEditMode = false;\n    //maxFile size is 15MB \n    maxFileSize = 15 * 1024 * 1024;\n    // Allowed file formats for upload\n    allowedFormats = knowledgeBaseLabels.allowedFormats;\n    // UI text constants\n    componentTitle = 'Upload File Here';\n    showUploadButton = true;\n    showDialogCloseIcon = false;\n    // Selected options\n    selectedRetriever = knowledgeBaseLabels.labels.default;\n    selectedUploadType = 'upload-files';\n    uploadPlaceholder = 'Upload Files';\n    iconName = 'circle-check';\n    iconColor = 'red';\n    // Model search input text (currently unused)\n    searchModelText = '';\n    // Uploaded files\n    uploadedFiles = [];\n    hasZeroKbFile = false;\n    // Split size fields\n    splitSize = 5000;\n    parentSplitSize = 5000;\n    childSplitSize = 2000;\n    // Dropdown data\n    embeddingModelOptions = [];\n    schemeOptions = [];\n    uploadOptions = [];\n    // Label map\n    kbLabels = knowledgeBaseLabels.labels;\n    // Retriever options for toggle\n    retrieverOptions = [this.kbLabels.default, this.kbLabels.parentDoc];\n    // Submit button label (Save / Update)\n    saveOrUpdateLabel = this.kbLabels.save;\n    // Controls required per upload type\n    selectedControls = {\n      'upload-files': [],\n      'Azure Blob': knowledgeBaseLabels.azureBlob,\n      'GitHub': knowledgeBaseLabels.github,\n      'Share Point': knowledgeBaseLabels.sharePoint,\n      'Confluence Wiki': knowledgeBaseLabels.confluenceWiki,\n      'Database': knowledgeBaseLabels.database\n    };\n    // API path suffix map based on upload source\n    pathSuffix = {\n      'Azure Blob': '/blob',\n      'GitHub': '/github',\n      'Share Point': '/sharepoint',\n      'Confluence Wiki': '/confluence',\n      'Database': '/database'\n    };\n    tableColumns = [{\n      key: 'fileName',\n      label: 'File Name',\n      type: 'text'\n    }, {\n      key: 'fileSizeFormatted',\n      label: 'File Size',\n      type: 'text'\n    }, {\n      key: 'uploadDateFormatted',\n      label: 'Upload Date',\n      type: 'text'\n    }, {\n      key: 'retrieverType',\n      label: 'Retriever Type',\n      type: 'text'\n    }];\n    tableData = [];\n    isLeftCollapsed = false;\n    toggleLeftPanel() {\n      this.isLeftCollapsed = !this.isLeftCollapsed;\n    }\n    // Constructor injecting services and initializing form group\n    constructor(fb, router, _decimalPipe, knowledgeBaseService, route) {\n      this.fb = fb;\n      this.router = router;\n      this._decimalPipe = _decimalPipe;\n      this.knowledgeBaseService = knowledgeBaseService;\n      this.route = route;\n      // Define form controls and validators\n      this.knowledgeBaseForm = this.fb.group({\n        name: ['', [Validators.required, Validators.minLength(3)]],\n        description: ['', Validators.required],\n        retriever: [this.kbLabels.default],\n        splitSize: [5000],\n        parentSplitSize: [5000],\n        childSplitSize: [2000],\n        embeddingModel: [null, Validators.required],\n        uploadType: ['', Validators.required],\n        containerName: [''],\n        accountName: [''],\n        accountKey: [''],\n        githubKey: [''],\n        githubAccount: [''],\n        githubRepo: [''],\n        githubBranch: [''],\n        tenantId: [''],\n        sharepointSiteName: [''],\n        sharepointFolderPath: [''],\n        email: [''],\n        clientId: [''],\n        clientSecret: [''],\n        apiToken: [''],\n        baseUrl: [''],\n        spaceKey: [''],\n        overlap: [''],\n        scheme: [''],\n        host: [''],\n        port: [''],\n        user: [''],\n        password: [''],\n        dbname: [''],\n        query: ['']\n      });\n    }\n    // Lifecycle hook: on component init\n    ngOnInit() {\n      this.loadEmbeddingModelOptions();\n      this.loadUploadOptionsAndSetDefault();\n      this.loadSchemeDropdownOptions();\n      // Check if editing an existing knowledge base\n      this.knowledgeBaseId = this.route.snapshot.paramMap.get('id');\n      this.isEditMode = !!this.knowledgeBaseId;\n      if (this.isEditMode) {\n        this.loadEditModeData();\n      }\n      this.saveOrUpdateLabel = this.isEditMode ? this.kbLabels.update : this.kbLabels.save;\n    }\n    // Fetch available embedding models\n    loadEmbeddingModelOptions() {\n      this.knowledgeBaseService.getEmbeddingModelOptions().subscribe(options => {\n        this.embeddingModelOptions = options.map(opt => ({\n          name: opt.modelDeploymentName,\n          value: String(opt.id)\n        }));\n      });\n    }\n    // Scheme Dropdown values\n    loadSchemeDropdownOptions() {\n      this.knowledgeBaseService.getSchemeDropdowns().subscribe(response => {\n        if (response && response.value) {\n          try {\n            const parsedValue = JSON.parse(response.value); // parse string to object\n            this.schemeOptions = Object.keys(parsedValue).map(key => ({\n              name: key,\n              value: parsedValue[key]\n            }));\n          } catch (error) {\n            console.error('Failed to parse scheme options:', error);\n            this.schemeOptions = [];\n          }\n        } else {\n          this.schemeOptions = [];\n        }\n      });\n    }\n    // Triggered when upload type changes\n    onUploadTypeChange(event) {\n      const selected = event?.selectedOptions?.[0]?.value;\n      this.selectedUploadType = selected || '';\n      this.uploadPlaceholder = '';\n      if (this.selectedUploadType !== 'upload-files') {\n        this.uploadedFiles = [];\n      }\n      // Clear all old validators AND values\n      Object.values(this.selectedControls).flat().forEach(ctrlName => {\n        const ctrl = this.getControl(ctrlName);\n        if (ctrl) {\n          ctrl.clearValidators();\n          ctrl.setValue(null); // or ''\n          ctrl.markAsPristine();\n          ctrl.markAsUntouched();\n        }\n      });\n      // Apply required validators to new controls\n      this.selectedControls[this.selectedUploadType]?.forEach(ctrl => {\n        this.getControl(ctrl)?.setValidators(Validators.required);\n      });\n      // Reapply validator to uploadType itself\n      this.getControl('uploadType')?.setValidators(Validators.required);\n      // Mark uploadType as interacted\n      this.getControl('uploadType')?.markAsTouched();\n      this.getControl('uploadType')?.markAsDirty();\n      // Recalculate form status\n      this.knowledgeBaseForm.updateValueAndValidity();\n    }\n    // Submit form handler\n    onSave() {\n      this.submissionSuccess = false;\n      this.fileUploadRequired = false;\n      // Basic form validation\n      if (this.knowledgeBaseForm.invalid && this.uploadedFiles.length === 0) {\n        this.knowledgeBaseForm.markAllAsTouched();\n        this.fileUploadRequired = true;\n        return;\n      }\n      const type = this.knowledgeBaseForm.value.uploadType;\n      const suffix = this.pathSuffix[type] || '';\n      const retrieverType = this.selectedRetriever === this.kbLabels.parentDoc ? 'parent_doc_retriever' : 'normal';\n      const knowledgeBase = this.getControl('name').value;\n      const description = this.getControl('description').value;\n      const splitSize = this.getControl('splitSize').value;\n      const parentSplitSize = this.getControl('parentSplitSize')?.value;\n      const childSplitSize = this.getControl('childSplitSize')?.value;\n      // Get selected model ID\n      const selectedModelValue = this.knowledgeBaseForm.value['embeddingModel'];\n      const selectedModel = this.embeddingModelOptions.find(opt => opt.name === selectedModelValue || opt.value === selectedModelValue);\n      const selectedModelId = selectedModel ? selectedModel.value : null;\n      // Construct base payload\n      const payload = {};\n      if (this.isEditMode && this.knowledgeBaseId) {\n        // Only send masterId during edit\n        payload['masterId'] = this.knowledgeBaseId;\n      } else {\n        // Full payload during create\n        payload['knowledgeBase'] = knowledgeBase;\n        payload['description'] = description;\n        payload['model-ref'] = selectedModelId;\n        payload['type'] = retrieverType;\n        if (retrieverType === 'parent_doc_retriever') {\n          payload['parentSplitSize'] = parentSplitSize;\n          payload['splitSize'] = childSplitSize;\n        } else {\n          payload['splitSize'] = splitSize;\n        }\n      }\n      // Handle file upload case\n      if (type === 'upload-files' || type == 'Upload Files') {\n        const file = this.uploadedFiles;\n        if (!file) return;\n        const formData = new FormData();\n        this.uploadedFiles.forEach(file => {\n          formData.append('files', file);\n        });\n        // Submit form with files\n        this.knowledgeBaseService.submitUpload(formData, payload, suffix).subscribe({\n          next: info => {\n            this.iconName = 'circle-check';\n            this.submissionMessage = info?.info?.message || info.message;\n            this.submissionSuccess = true;\n            this.showInfoPopup = true;\n            this.iconColor = 'green';\n          },\n          error: err => {\n            this.iconName = 'alert-circle';\n            this.submissionMessage = err?.error?.message || err.message || 'Knowledge Base creation or update failed. Please try again.';\n            this.submissionSuccess = false;\n            this.showInfoPopup = true;\n            this.iconColor = 'red';\n          }\n        });\n        // Handle non-file (external source) upload case\n      } else {\n        const data = {};\n        // Add selected controls to both body and query params\n        this.selectedControls[type]?.forEach(key => {\n          const value = this.getControl(key)?.value;\n          data[key] = value;\n          // If edit mode, also include these as query params (i.e., in payload)\n          if (this.isEditMode) {\n            payload[key] = value;\n          }\n        });\n        // Also add masterId for edit mode\n        this.knowledgeBaseService.submitUpload(data, payload, suffix).subscribe({\n          next: info => {\n            this.iconName = 'circle-check';\n            this.submissionMessage = info?.info?.message || info.message;\n            this.submissionSuccess = true;\n            this.showInfoPopup = true;\n            this.iconColor = 'green';\n          },\n          error: err => {\n            this.iconName = 'alert-circle';\n            this.submissionMessage = err?.error?.message || err.message || 'Knowledge Base creation or update failed. Please try again.';\n            this.submissionSuccess = false;\n            this.showInfoPopup = true;\n            this.iconColor = 'red';\n          }\n        });\n      }\n    }\n    // Get a typed form control\n    getControl(name) {\n      return this.knowledgeBaseForm.get(name);\n    }\n    // Utility to return readable field validation error\n    getFieldError(fieldName) {\n      const field = this.knowledgeBaseForm.get(fieldName);\n      // Capitalize only if first letter is not already uppercase\n      const formattedFieldName = /^[A-Z]/.test(fieldName) ? fieldName : fieldName.charAt(0).toUpperCase() + fieldName.slice(1);\n      if (field && field.invalid && (field.touched || field.dirty)) {\n        if (field.errors?.['required']) {\n          return `${formattedFieldName} is required`;\n        }\n        if (field.errors?.['email']) {\n          return 'Please enter a valid email address';\n        }\n        if (field.errors?.['minlength']) {\n          return `${formattedFieldName} must be at least ${field.errors['minlength'].requiredLength} characters long`;\n        }\n      } else if (fieldName == 'scheme' || fieldName == 'embeddingModel') {\n        if (field && field.errors?.['required']) {\n          return `${formattedFieldName} is required`;\n        }\n      }\n      return '';\n    }\n    // Navigate back to listing\n    onExit() {\n      this.router.navigate(['/libraries/knowledge-base']);\n    }\n    // Load upload options and set first one as default\n    loadUploadOptionsAndSetDefault() {\n      this.knowledgeBaseService.getUploadDropdowns().subscribe({\n        next: res => {\n          if (res?.value) {\n            this.uploadOptions = this.getUploadTypeOptions(res.value);\n            // Safely assign default value and apply\n            const firstOption = this.uploadOptions[0];\n            if (firstOption && firstOption.value) {\n              const defaultValue = firstOption.value;\n              // Set form control\n              this.knowledgeBaseForm.get('uploadType')?.setValue(defaultValue);\n              // Set component state\n              this.selectedUploadType = defaultValue;\n              // Trigger validators and placeholder update\n              this.onUploadTypeChange({\n                selectedOptions: [{\n                  value: defaultValue\n                }]\n              });\n            }\n          }\n        },\n        error: () => {}\n      });\n    }\n    // Converts string labels into slug format\n    getUploadTypeOptions(rawValue) {\n      const parsed = JSON.parse(rawValue);\n      return Object.keys(parsed).map(label => ({\n        name: label,\n        value: this.toSlug(label)\n      }));\n    }\n    // Utility for slug formatting\n    toSlug(label) {\n      return label.toLowerCase().replace(/\\s+/g, '-');\n    }\n    // Handle retriever mode toggle\n    selectRetriever(retriever) {\n      this.selectedRetriever = retriever;\n      this.knowledgeBaseForm.get('retriever')?.setValue(retriever);\n    }\n    onParentSplitSizeChange(event) {\n      this.parentSplitSize = event;\n      this.knowledgeBaseForm.get('parentSplitSize')?.setValue(this.parentSplitSize);\n    }\n    onChildSplitSizeChange(event) {\n      this.childSplitSize = event;\n      this.knowledgeBaseForm.get('childSplitSize')?.setValue(this.childSplitSize);\n    }\n    // splitSize input setter\n    splitSizeChange(event) {\n      this.splitSize = event;\n      this.knowledgeBaseForm.get('splitSize')?.setValue(this.splitSize);\n    }\n    // Update file list after change\n    filesListChanged(file) {\n      this.uploadedFiles = [...file];\n      this.hasZeroKbFile = this.uploadedFiles.some(file => file.size === 0);\n    }\n    // Disable submit based on form or file state\n    isSubmitDisabled() {\n      const isFormInvalid = this.knowledgeBaseForm.invalid;\n      const isUploadTypeFile = this.selectedUploadType === 'upload-files';\n      const isFileEmpty = this.uploadedFiles.length === 0;\n      const isParentDoc = this.selectedRetriever === this.kbLabels.parentDoc;\n      const splitSize = Number(this.knowledgeBaseForm.get('splitSize')?.value);\n      const parentSplitSize = Number(this.knowledgeBaseForm.get('parentSplitSize')?.value);\n      const childSplitSize = Number(this.knowledgeBaseForm.get('childSplitSize')?.value);\n      //  Normal condition: splitSize must be at least 100\n      const isNormalSplitSizeInvalid = !isParentDoc && splitSize < 100;\n      //  Parent-child condition:\n      // 1. parent must be > child\n      // 2. both must be >= 100\n      const isParentChildInvalid = isParentDoc && (parentSplitSize <= childSplitSize || parentSplitSize < 100 || childSplitSize < 100);\n      return isFormInvalid || isUploadTypeFile && isFileEmpty || isParentChildInvalid || isNormalSplitSizeInvalid || this.hasZeroKbFile;\n    }\n    // Close popup and navigate on success\n    handlePopupClose() {\n      this.showInfoPopup = false;\n      if (this.submissionSuccess) {\n        this.router.navigate(['/libraries/knowledge-base']);\n      }\n    }\n    // Format file size from bytes to MB string\n    formatDocumentSize(size) {\n      if (size < 102400) {\n        return this._decimalPipe.transform(size / 1024, '1.0-2') + ' KB';\n      } else {\n        return this._decimalPipe.transform(size / 1048576, '1.2-2') + ' MB';\n      }\n    }\n    // Format rows\n    formatRows(row) {\n      return row > 1 ? `${row} rows` : `${row} row`;\n    }\n    // Format date to dd/mm/yy, hh:mm format\n    formatUploadDate(dateStr) {\n      const date = new Date(dateStr);\n      const day = String(date.getDate()).padStart(2, '0');\n      const month = String(date.getMonth() + 1).padStart(2, '0');\n      const year = String(date.getFullYear()).slice(-2);\n      const hours = String(date.getHours()).padStart(2, '0');\n      const minutes = String(date.getMinutes()).padStart(2, '0');\n      return `${day}/${month}/${year}, ${hours}:${minutes}`;\n    }\n    // Returns a user-friendly label for the given retriever type\n    getRetrieverLabel(type) {\n      const map = {\n        normal: 'Default',\n        parent_doc_retriever: 'Parent Doc'\n      };\n      this.selectedRetriever = map[type];\n      return map[type] || 'Unknown';\n    }\n    loadEditModeData() {\n      const kbId = Number(this.knowledgeBaseId);\n      // Fetch files and other details\n      this.knowledgeBaseService.getKnowledgeBaseById(kbId).subscribe({\n        next: response => {\n          if (response?.files?.length > 0) {\n            const retrieverType = response.retrieverType || 'normal';\n            this.tableData = response.files.map(file => ({\n              fileName: file.fileName,\n              fileSizeFormatted: file.source === 'database' ? this.formatRows(file.fileSizeBytes) : this.formatDocumentSize(file.fileSizeBytes),\n              uploadDateFormatted: this.formatUploadDate(file.uploadDate),\n              retrieverType: this.getRetrieverLabel(retrieverType)\n            }));\n            // Disable fields in edit mode\n            ['name', 'description', 'splitSize', 'embeddingModel'].forEach(field => this.knowledgeBaseForm.get(field)?.disable());\n          } else {\n            this.tableData = [];\n          }\n        },\n        error: err => {\n          console.error('Error fetching knowledge base data', err);\n          this.tableData = [];\n        }\n      });\n      // Fetch and patch name/description\n      this.knowledgeBaseService.fetchAllKnowledge().pipe(map(response => {\n        const match = response.find(item => item.id === kbId);\n        if (match) {\n          this.knowledgeBaseForm.patchValue({\n            name: match.collectionName,\n            description: match.description\n          });\n        }\n        return response;\n      })).subscribe({\n        next: () => {\n          // Successfully patched the form (already done in map)\n        },\n        error: () => {}\n      });\n    }\n    onSliderInputChange(event, controlName) {\n      let inputValue;\n      // Handle both native event and direct value\n      if (event?.target) {\n        inputValue = event.target.valueAsNumber;\n      } else if (typeof event === 'number') {\n        inputValue = event;\n      } else {\n        inputValue = Number(event);\n      }\n      // Cap to max value (you can also parametrize this if needed)\n      const cappedValue = Math.min(inputValue, 20000);\n      // Update local variable based on controlName\n      if (controlName === 'parentSplitSize') {\n        this.parentSplitSize = cappedValue;\n      } else if (controlName === 'childSplitSize') {\n        this.childSplitSize = cappedValue;\n      } else if (controlName === 'splitSize') {\n        this.splitSize = cappedValue;\n      }\n      // Update the corresponding form control\n      this.knowledgeBaseForm.get(controlName)?.setValue(cappedValue);\n    }\n    static ɵfac = function CreateKnowledgeBaseComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CreateKnowledgeBaseComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.DecimalPipe), i0.ɵɵdirectiveInject(i4.KnowledgeBaseService), i0.ɵɵdirectiveInject(i2.ActivatedRoute));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CreateKnowledgeBaseComponent,\n      selectors: [[\"app-create-knowledge-base\"]],\n      features: [i0.ɵɵProvidersFeature([DecimalPipe])],\n      decls: 26,\n      vars: 23,\n      consts: [[1, \"page-title\"], [1, \"create-knowledge-base-container\"], [3, \"formGroup\"], [1, \"form-layout\"], [1, \"left-column\"], [1, \"left-header\"], [\"class\", \"left-title\", 4, \"ngIf\"], [\"name\", \"panel-left\", 1, \"collapse-icon\", 3, \"click\"], [\"class\", \"card-content\", 4, \"ngIf\"], [1, \"right-column\"], [1, \"card-content\"], [1, \"section\"], [1, \"knowledge-base-head\"], [1, \"knowledge-base-title\"], [1, \"right-column-buttons\"], [\"variant\", \"primary\", \"size\", \"medium\", \"state\", \"default\", 3, \"userClick\", \"label\", \"customStyles\", \"disabled\"], [4, \"ngIf\"], [\"id\", \"uploadType\", \"formControlName\", \"uploadType\", \"variant\", \"primary\", \"size\", \"md\", 1, \"dropdown-medium\", 3, \"valueChange\", \"label\", \"options\", \"selectedValue\", \"error\", \"fullWidth\", \"required\"], [\"class\", \"field-error\", 4, \"ngIf\"], [\"class\", \"upload-fields-grid\", 4, \"ngIf\"], [1, \"left-title\"], [\"id\", \"knowledgeBaseName\", \"formControlName\", \"name\", \"size\", \"md\", 3, \"placeholder\", \"label\", \"control\", \"error\", \"fullWidth\", \"required\"], [\"formControlName\", \"description\", \"size\", \"md\", 3, \"label\", \"placeholder\", \"control\", \"error\", \"fullWidth\", \"required\"], [1, \"section-title\"], [1, \"retriever-options\"], [\"size\", \"small\", 3, \"label\", \"variant\", \"state\", \"disabled\", \"customStyles\", \"userClick\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"section\", 4, \"ngIf\"], [\"id\", \"embeddingModel\", 1, \"dropdown-medium\", 3, \"dropdownTitle\", \"label\", \"options\", \"formControl\", \"error\", \"required\"], [\"size\", \"small\", 3, \"userClick\", \"label\", \"variant\", \"state\", \"disabled\", \"customStyles\"], [1, \"split-size-container\"], [\"formControlName\", \"splitSize\", 3, \"valueChange\", \"disabled\", \"value\", \"min\", \"max\", \"step\", \"showTooltip\"], [1, \"value-box\"], [\"type\", \"number\", \"formControlName\", \"splitSize\", 3, \"change\"], [1, \"split-section\"], [\"formControlName\", \"parentSplitSize\", 3, \"valueChange\", \"disabled\", \"value\", \"min\", \"max\", \"step\", \"showTooltip\"], [\"type\", \"number\", \"formControlName\", \"parentSplitSize\", 3, \"change\"], [\"formControlName\", \"childSplitSize\", 3, \"valueChange\", \"disabled\", \"value\", \"min\", \"max\", \"step\", \"showTooltip\"], [\"type\", \"number\", \"formControlName\", \"childSplitSize\", 3, \"change\"], [1, \"field-error\"], [3, \"columns\", \"data\"], [1, \"ava-file-upload-wrapper\"], [\"uploaderId\", \"light-uploader\", 3, \"filesListChanged\", \"allowedFormats\", \"showUploadButton\", \"componentTitle\", \"showDialogCloseIcon\", \"maxFileSize\", \"preview\"], [1, \"upload-fields-grid\"], [\"formControlName\", \"containerName\", 3, \"label\", \"placeholder\", \"control\", \"error\", \"required\"], [\"formControlName\", \"accountName\", 3, \"label\", \"placeholder\", \"control\", \"error\", \"required\"], [\"formControlName\", \"accountKey\", 3, \"label\", \"placeholder\", \"control\", \"error\", \"required\"], [\"formControlName\", \"githubKey\", 3, \"label\", \"placeholder\", \"control\", \"error\", \"required\"], [\"formControlName\", \"githubAccount\", 3, \"label\", \"placeholder\", \"control\", \"error\", \"required\"], [\"formControlName\", \"githubRepo\", 3, \"label\", \"placeholder\", \"control\", \"error\", \"required\"], [\"formControlName\", \"githubBranch\", 3, \"label\", \"placeholder\", \"control\", \"error\", \"required\"], [\"formControlName\", \"clientId\", 3, \"label\", \"placeholder\", \"control\", \"error\", \"required\"], [\"formControlName\", \"clientSecret\", 3, \"label\", \"placeholder\", \"control\", \"error\", \"required\"], [\"formControlName\", \"tenantId\", 3, \"label\", \"placeholder\", \"control\", \"error\", \"required\"], [\"formControlName\", \"sharepointSiteName\", 3, \"label\", \"placeholder\", \"control\", \"error\", \"required\"], [\"formControlName\", \"sharepointFolderPath\", 3, \"label\", \"placeholder\", \"control\", \"error\", \"required\"], [\"type\", \"email\", \"formControlName\", \"email\", 3, \"label\", \"placeholder\", \"control\", \"error\", \"required\"], [\"formControlName\", \"apiToken\", 3, \"label\", \"placeholder\", \"control\", \"error\", \"required\"], [\"formControlName\", \"baseUrl\", 3, \"label\", \"placeholder\", \"control\", \"error\", \"required\"], [\"formControlName\", \"spaceKey\", 3, \"label\", \"placeholder\", \"control\", \"error\", \"required\"], [\"type\", \"number\", \"formControlName\", \"overlap\", 3, \"label\", \"placeholder\", \"control\", \"error\", \"required\"], [1, \"schema-dropdown\"], [\"formControlName\", \"scheme\", 3, \"label\", \"dropdownTitle\", \"options\", \"control\", \"error\", \"required\"], [\"formControlName\", \"host\", 3, \"label\", \"placeholder\", \"control\", \"error\", \"required\"], [\"formControlName\", \"port\", 3, \"label\", \"placeholder\", \"control\", \"error\", \"required\"], [\"formControlName\", \"user\", 3, \"label\", \"placeholder\", \"control\", \"error\", \"required\"], [\"formControlName\", \"password\", 3, \"label\", \"placeholder\", \"control\", \"error\", \"required\"], [\"formControlName\", \"dbname\", 3, \"label\", \"placeholder\", \"control\", \"error\", \"required\"], [\"formControlName\", \"query\", 3, \"label\", \"placeholder\", \"control\", \"error\", \"required\"]],\n      template: function CreateKnowledgeBaseComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"p\", 0);\n          i0.ɵɵtext(1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(2, \"div\", 1)(3, \"form\", 2)(4, \"div\", 3)(5, \"div\", 4)(6, \"div\", 5);\n          i0.ɵɵtemplate(7, CreateKnowledgeBaseComponent_span_7_Template, 2, 0, \"span\", 6);\n          i0.ɵɵelementStart(8, \"lucide-icon\", 7);\n          i0.ɵɵlistener(\"click\", function CreateKnowledgeBaseComponent_Template_lucide_icon_click_8_listener() {\n            return ctx.toggleLeftPanel();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(9, CreateKnowledgeBaseComponent_div_9_Template, 3, 12, \"div\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"div\", 9)(11, \"div\", 10)(12, \"div\", 11)(13, \"div\", 12)(14, \"div\", 13);\n          i0.ɵɵtext(15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"div\", 14)(17, \"ava-button\", 15);\n          i0.ɵɵlistener(\"userClick\", function CreateKnowledgeBaseComponent_Template_ava_button_userClick_17_listener() {\n            return ctx.isEditMode ? ctx.confirmUpdate() : ctx.confirmSave();\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(18, CreateKnowledgeBaseComponent_ng_container_18_Template, 9, 10, \"ng-container\", 16)(19, CreateKnowledgeBaseComponent_div_19_Template, 2, 2, \"div\", 16)(20, CreateKnowledgeBaseComponent_hr_20_Template, 1, 0, \"hr\", 16);\n          i0.ɵɵelementStart(21, \"div\", 11)(22, \"ava-dropdown\", 17);\n          i0.ɵɵlistener(\"valueChange\", function CreateKnowledgeBaseComponent_Template_ava_dropdown_valueChange_22_listener($event) {\n            return ctx.onUploadTypeChange($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(23, CreateKnowledgeBaseComponent_ng_container_23_Template, 4, 7, \"ng-container\", 16)(24, CreateKnowledgeBaseComponent_div_24_Template, 2, 1, \"div\", 18)(25, CreateKnowledgeBaseComponent_div_25_Template, 6, 5, \"div\", 19);\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate(ctx.kbLabels.pageTitle);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.knowledgeBaseForm);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"collapsed\", ctx.isLeftCollapsed);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLeftCollapsed);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLeftCollapsed);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate1(\" \", ctx.kbLabels.knowledgeBaseConfiguration, \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵpropertyInterpolate(\"label\", ctx.saveOrUpdateLabel);\n          i0.ɵɵproperty(\"customStyles\", i0.ɵɵpureFunction0(22, _c0))(\"disabled\", ctx.isSubmitDisabled());\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isEditMode);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isEditMode);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isEditMode);\n          i0.ɵɵadvance(2);\n          i0.ɵɵpropertyInterpolate(\"label\", ctx.kbLabels.uploadType);\n          i0.ɵɵproperty(\"options\", ctx.uploadOptions)(\"selectedValue\", ctx.uploadPlaceholder)(\"error\", ctx.getFieldError(\"uploadType\"))(\"fullWidth\", true)(\"required\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedUploadType === \"upload-files\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.fileUploadRequired && ctx.uploadedFiles.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedUploadType);\n        }\n      },\n      dependencies: [CommonModule, i3.NgForOf, i3.NgIf, ReactiveFormsModule, i1.ɵNgNoValidate, i1.NgControlStatus, i1.NgControlStatusGroup, i1.RequiredValidator, i1.FormControlDirective, i1.FormGroupDirective, i1.FormControlName, FormsModule, LucideAngularModule, i5.LucideAngularComponent, DropdownComponent, AvaTextboxComponent, AvaTextareaComponent, SliderComponent, FileUploadComponent, ButtonComponent, TableComponent],\n      styles: [\".create-knowledge-base-container[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n  background-color: transparent;\\n}\\n\\n.knowledge-base-head[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n}\\n\\n.knowledge-base-title[_ngcontent-%COMP%] {\\n  font-size: 1.5rem; \\n\\n  font-weight: bold; \\n\\n  color: #333333; \\n\\n  padding-bottom: 4px;\\n}\\n\\nform[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%;\\n  overflow: hidden;\\n}\\n\\n.form-layout[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: row;\\n  gap: 0px;\\n  flex: 1;\\n  overflow: hidden;\\n  align-items: stretch;\\n  height: 100%;\\n  border: 1px solid #e1e4e8;\\n  background: #ffffff;\\n  margin-bottom: 20px;\\n}\\n@media (max-width: 1400px) {\\n  .form-layout[_ngcontent-%COMP%] {\\n    gap: 20px;\\n  }\\n}\\n@media (max-width: 1200px) {\\n  .form-layout[_ngcontent-%COMP%] {\\n    gap: 16px;\\n    padding: 16px;\\n  }\\n}\\n@media (max-width: 992px) {\\n  .form-layout[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n}\\n@media (max-width: 576px) {\\n  .form-layout[_ngcontent-%COMP%] {\\n    gap: 12px;\\n    padding: 12px;\\n  }\\n}\\n.form-layout[_ngcontent-%COMP%]   .left-column[_ngcontent-%COMP%], \\n.form-layout[_ngcontent-%COMP%]   .right-column[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%;\\n  gap: 20px;\\n  max-height: 800px;\\n}\\n@media (max-width: 1200px) {\\n  .form-layout[_ngcontent-%COMP%]   .left-column[_ngcontent-%COMP%], \\n   .form-layout[_ngcontent-%COMP%]   .right-column[_ngcontent-%COMP%] {\\n    gap: 16px;\\n    height: calc(100vh - 160px);\\n  }\\n}\\n@media (max-width: 992px) {\\n  .form-layout[_ngcontent-%COMP%]   .left-column[_ngcontent-%COMP%], \\n   .form-layout[_ngcontent-%COMP%]   .right-column[_ngcontent-%COMP%] {\\n    width: 100%;\\n    height: auto;\\n    max-height: none;\\n  }\\n}\\n@media (max-width: 576px) {\\n  .form-layout[_ngcontent-%COMP%]   .left-column[_ngcontent-%COMP%], \\n   .form-layout[_ngcontent-%COMP%]   .right-column[_ngcontent-%COMP%] {\\n    gap: 12px;\\n  }\\n}\\n.form-layout[_ngcontent-%COMP%]   .left-column[_ngcontent-%COMP%] {\\n  width: 340px;\\n  min-width: 60px;\\n  max-width: 340px;\\n  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  background: #f8f9fa;\\n  border-right: 1px solid #e1e4e8;\\n  display: flex;\\n  flex-direction: column;\\n  height: 100vh;\\n  min-height: 0;\\n  position: relative;\\n}\\n@media (max-width: 1400px) {\\n  .form-layout[_ngcontent-%COMP%]   .left-column[_ngcontent-%COMP%] {\\n    width: 40%;\\n  }\\n}\\n@media (max-width: 1200px) {\\n  .form-layout[_ngcontent-%COMP%]   .left-column[_ngcontent-%COMP%] {\\n    width: 40%;\\n  }\\n}\\n.form-layout[_ngcontent-%COMP%]   .left-column[_ngcontent-%COMP%]   app-card[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n}\\n.form-layout[_ngcontent-%COMP%]   .left-column[_ngcontent-%COMP%]   app-card[_ngcontent-%COMP%]:first-of-type {\\n  flex: 0 0 auto;\\n}\\n.form-layout[_ngcontent-%COMP%]   .left-column[_ngcontent-%COMP%]   app-card[_ngcontent-%COMP%]:last-of-type {\\n  flex: 1;\\n}\\n.form-layout[_ngcontent-%COMP%]   .right-column[_ngcontent-%COMP%] {\\n  width: 100%;\\n  display: flex;\\n  flex-direction: column;\\n  overflow-y: auto;\\n}\\n@media (max-width: 1400px) {\\n  .form-layout[_ngcontent-%COMP%]   .right-column[_ngcontent-%COMP%] {\\n    width: 60%;\\n  }\\n}\\n@media (max-width: 1200px) {\\n  .form-layout[_ngcontent-%COMP%]   .right-column[_ngcontent-%COMP%] {\\n    width: 60%;\\n  }\\n}\\n\\n.card-content[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n  flex: 1;\\n}\\n@media (max-width: 576px) {\\n  .card-content[_ngcontent-%COMP%] {\\n    padding: 12px;\\n    gap: 12px;\\n  }\\n}\\n\\n.left-column[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%] {\\n  flex: 1 1 0;\\n  min-height: 0;\\n  overflow-y: auto;\\n  padding: 1rem;\\n}\\n\\n.section-title[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  margin: 0 0 8px;\\n  color: var(--text-color);\\n}\\n@media (max-width: 576px) {\\n  .section-title[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n    margin-bottom: 6px;\\n  }\\n}\\n\\n.section[_ngcontent-%COMP%] {\\n  margin-bottom: 8px;\\n}\\n.section[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n\\n.split-section[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n.split-section[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n\\n.retriever-options[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n\\n.retriever-button[_ngcontent-%COMP%] {\\n  padding: 8px 16px;\\n  font-size: 14px;\\n  border-radius: 6px;\\n  background-color: white;\\n  border: 1px solid #e1e4e8;\\n  color: black;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.retriever-button.selected[_ngcontent-%COMP%] {\\n  background-color: black;\\n  color: white;\\n  border-color: black;\\n}\\n.retriever-button[_ngcontent-%COMP%]:hover:not(.selected) {\\n  background-color: #f0f0f0;\\n}\\n\\n.split-size-container[_ngcontent-%COMP%] {\\n  float: left;\\n  width: 600px;\\n}\\n\\n.split-size-slider[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 4px;\\n  appearance: none;\\n  background-color: var(--agent-slider-bg);\\n  border-radius: 2px;\\n  outline: none;\\n}\\n.split-size-slider[_ngcontent-%COMP%]::-webkit-slider-thumb {\\n  appearance: none;\\n  width: 16px;\\n  height: 16px;\\n  background-color: var(--agent-slider-thumb-bg);\\n  border-radius: 50%;\\n  cursor: pointer;\\n  box-shadow: var(--agent-slider-thumb-shadow);\\n}\\n.split-size-slider[_ngcontent-%COMP%]::-moz-range-thumb {\\n  width: 16px;\\n  height: 16px;\\n  background-color: var(--agent-slider-thumb-bg);\\n  border-radius: 50%;\\n  cursor: pointer;\\n  border: none;\\n  box-shadow: var(--agent-slider-thumb-shadow);\\n}\\n\\n.split-size-input[_ngcontent-%COMP%] {\\n  width: 50px;\\n  padding: 6px;\\n  border: 1px solid var(--agent-slider-input-border);\\n  border-radius: 6px;\\n  text-align: center;\\n  background-color: var(--agent-slider-input-bg);\\n  color: var(--agent-slider-input-text);\\n}\\n\\n.split-size-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n  width: 100%;\\n  padding-bottom: 10px;\\n}\\n.split-size-container[_ngcontent-%COMP%]   ava-slider[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 200px;\\n}\\n.split-size-container[_ngcontent-%COMP%]   .split-size-input[_ngcontent-%COMP%] {\\n  width: 100px;\\n  padding: 6px 8px;\\n  font-size: 14px;\\n  border: 1px solid #ccc;\\n  border-radius: 6px;\\n}\\n\\n.field-error[_ngcontent-%COMP%] {\\n  color: #e53935;\\n  font-size: 13px;\\n  margin-top: 4px;\\n}\\n\\n.right-column-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  gap: 16px;\\n  padding: 0px 0 4px;\\n  margin-bottom: 0;\\n  flex-shrink: 0;\\n}\\n@media (max-width: 576px) {\\n  .right-column-buttons[_ngcontent-%COMP%] {\\n    gap: 12px;\\n    padding: 12px 0 0;\\n    margin-top: 8px;\\n    margin-bottom: 0;\\n  }\\n}\\n.right-column-buttons[_ngcontent-%COMP%]   .exit-button[_ngcontent-%COMP%], \\n.right-column-buttons[_ngcontent-%COMP%]   .save-button[_ngcontent-%COMP%] {\\n  padding: 10px 24px;\\n  border-radius: 6px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n@media (max-width: 576px) {\\n  .right-column-buttons[_ngcontent-%COMP%]   .exit-button[_ngcontent-%COMP%], \\n   .right-column-buttons[_ngcontent-%COMP%]   .save-button[_ngcontent-%COMP%] {\\n    padding: 8px 16px;\\n    font-size: 13px;\\n  }\\n}\\n.right-column-buttons[_ngcontent-%COMP%]   .exit-button[_ngcontent-%COMP%] {\\n  background-color: transparent;\\n  border: 1px solid var(--button-secondary-border);\\n  color: var(--button-secondary-text);\\n}\\n.right-column-buttons[_ngcontent-%COMP%]   .exit-button[_ngcontent-%COMP%]:hover {\\n  background-color: var(--button-secondary-hover-bg);\\n}\\n.right-column-buttons[_ngcontent-%COMP%]   .save-button[_ngcontent-%COMP%] {\\n  background: var(--button-gradient);\\n  border: none;\\n  color: var(--button-primary-text);\\n}\\n.right-column-buttons[_ngcontent-%COMP%]   .save-button[_ngcontent-%COMP%]:hover {\\n  opacity: var(--button-hover-opacity);\\n}\\n\\n.upload-fields-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(2, 1fr);\\n  gap: 16px;\\n}\\n.upload-fields-grid[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  grid-column: 1/-1;\\n  margin: 0;\\n  font-size: 16px;\\n  font-weight: 600;\\n}\\n\\n.value-box[_ngcontent-%COMP%] {\\n  width: 70px;\\n  height: 49px;\\n  border: 1px solid #4B507F;\\n  border-radius: 8px;\\n  font-size: 16px;\\n  font-family: Inter, sans-serif;\\n  text-align: center;\\n  line-height: 30px;\\n  color: #4B507F;\\n  background-color: white;\\n  padding: 0;\\n}\\n\\n  ava-file-upload .upload-container .file-actions, \\n  ava-file-upload .upload-container .ava-icon-container {\\n  display: none;\\n}\\n\\n.ava-file-upload-left[_ngcontent-%COMP%] {\\n  width: 60%;\\n  float: left;\\n}\\n\\n.ava-file-upload-wrapper[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.page-title[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  font-size: 20px;\\n}\\n\\n.schema-dropdown[_ngcontent-%COMP%] {\\n  margin-top: 10px;\\n}\\n\\n.dropdown-medium[_ngcontent-%COMP%] {\\n  width: 250px;\\n  display: inline-block;\\n}\\n\\n.left-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  height: 48px;\\n  padding: 0 16px;\\n  background: #fff;\\n  border-bottom: 1px solid #e1e4e8;\\n  z-index: 2;\\n}\\n\\n.collapse-icon[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background: #fff;\\n  border-radius: 50%;\\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);\\n  cursor: pointer;\\n  z-index: 2;\\n  font-size: 18px;\\n  border: 1px solid #e1e4e8;\\n  margin-right: 0;\\n}\\n\\n.left-title[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  font-size: 16px;\\n  color: #23272E;\\n}\\n\\n.left-column.collapsed[_ngcontent-%COMP%] {\\n  width: 48px !important;\\n  min-width: 48px !important;\\n  max-width: 48px !important;\\n}\\n\\n.left-column.collapsed[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return CreateKnowledgeBaseComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "DecimalPipe", "ReactiveFormsModule", "FormsModule", "Validators", "knowledgeBaseLabels", "SliderComponent", "DropdownComponent", "AvaTextboxComponent", "AvaTextareaComponent", "FileUploadComponent", "ButtonComponent", "TableComponent", "LucideAngularModule", "map", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵpropertyInterpolate", "ctx_r0", "kbLabels", "placeholderKnowledgeBase", "knowledgeBaseName", "ɵɵproperty", "getControl", "getFieldError", "description", "placeholderDescription", "ɵɵlistener", "CreateKnowledgeBaseComponent_ng_container_18_ava_button_5_Template_ava_button_userClick_0_listener", "retriever_r3", "ɵɵrestoreView", "_r2", "$implicit", "ɵɵnextContext", "ɵɵresetView", "selectRetriever", "selected<PERSON>et<PERSON>r", "isEditMode", "ɵɵpureFunction0", "_c1", "_c2", "CreateKnowledgeBaseComponent_ng_container_18_div_6_Template_ava_slider_valueChange_4_listener", "$event", "_r4", "splitSizeChange", "CreateKnowledgeBaseComponent_ng_container_18_div_6_Template_ava_textbox_change_6_listener", "onSliderInputChange", "ɵɵtextInterpolate", "splitSize", "ɵɵtextInterpolate1", "parentChildSplitValidation", "CreateKnowledgeBaseComponent_ng_container_18_div_7_Template_ava_slider_valueChange_5_listener", "_r5", "onParentSplitSizeChange", "CreateKnowledgeBaseComponent_ng_container_18_div_7_Template_ava_textbox_change_7_listener", "CreateKnowledgeBaseComponent_ng_container_18_div_7_Template_ava_slider_valueChange_12_listener", "onChildSplitSizeChange", "CreateKnowledgeBaseComponent_ng_container_18_div_7_Template_ava_textbox_change_14_listener", "ɵɵtemplate", "CreateKnowledgeBaseComponent_ng_container_18_div_7_div_15_Template", "parentSplitSize", "childSplitSize", "parentDoc", "ɵɵelementContainerStart", "CreateKnowledgeBaseComponent_ng_container_18_ava_button_5_Template", "CreateKnowledgeBaseComponent_ng_container_18_div_6_Template", "CreateKnowledgeBaseComponent_ng_container_18_div_7_Template", "retrieverOptions", "selectEmbeddingModel", "embeddingModel", "embeddingModelOptions", "tableColumns", "tableData", "zeroKbFileError", "CreateKnowledgeBaseComponent_ng_container_23_Template_ava_file_upload_filesListChanged_2_listener", "_r6", "filesListChanged", "CreateKnowledgeBaseComponent_ng_container_23_div_3_Template", "allowedFormats", "showUploadButton", "componentTitle", "showDialogCloseIcon", "maxFileSize", "hasZeroKbFile", "fileUploadIsRequired", "configSource", "containerName", "placeholder<PERSON><PERSON><PERSON><PERSON><PERSON>", "accountName", "placeholder<PERSON><PERSON><PERSON><PERSON><PERSON>", "accountKey", "placeholder<PERSON><PERSON><PERSON><PERSON><PERSON>", "gith<PERSON><PERSON><PERSON>", "placeholder<PERSON><PERSON><PERSON><PERSON><PERSON>", "gith<PERSON><PERSON><PERSON>unt", "placeholder<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "githubRepository", "placeholderGithubRepository", "githubBranch", "placeholder<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clientId", "placeholderClientId", "clientSecret", "placeholderClientSecret", "tenantId", "placeholder<PERSON><PERSON>tId", "siteName", "placeholderSiteName", "folderPath", "placeholder<PERSON><PERSON>er<PERSON>ath", "emailId", "placeholderEmailId", "confluenceClientId", "placeholderConfluenceClientId", "confluenceClientSecret", "placeholderConfluenceClientSecret", "apiToken", "placeholderApiToken", "baseUrl", "placeholderBaseUrl", "spaceKey", "placeholderSpaceKey", "overlap", "placeholder<PERSON><PERSON><PERSON>", "selectScheme", "schemeOptions", "dbHost", "placeholderHost", "dbPort", "placeholderPort", "dbUser", "placeholderUser", "dbPass<PERSON>", "placeholderPassword", "dbN<PERSON>", "placeholderDbName", "db<PERSON><PERSON><PERSON>", "placeholder<PERSON><PERSON><PERSON><PERSON><PERSON>", "CreateKnowledgeBaseComponent_div_25_ng_container_1_Template", "CreateKnowledgeBaseComponent_div_25_ng_container_2_Template", "CreateKnowledgeBaseComponent_div_25_ng_container_3_Template", "CreateKnowledgeBaseComponent_div_25_ng_container_4_Template", "CreateKnowledgeBaseComponent_div_25_ng_container_5_Template", "selectedUploadType", "CreateKnowledgeBaseComponent", "fb", "router", "_decimalPipe", "knowledgeBaseService", "route", "knowledgeBaseForm", "knowledgeBaseId", "submissionMessage", "submissionSuccess", "showInfoPopup", "fileUploadRequired", "labels", "default", "uploadPlaceholder", "iconName", "iconColor", "searchModelText", "uploadedFiles", "uploadOptions", "saveOrUpdateLabel", "save", "selectedControls", "azureBlob", "github", "sharePoint", "confluenceWiki", "database", "pathSuffix", "key", "label", "type", "isLeftCollapsed", "toggleLeftPanel", "constructor", "group", "name", "required", "<PERSON><PERSON><PERSON><PERSON>", "retriever", "uploadType", "githubRepo", "sharepointSiteName", "sharepointFolderPath", "email", "scheme", "host", "port", "user", "password", "dbname", "query", "ngOnInit", "loadEmbeddingModelOptions", "loadUploadOptionsAndSetDefault", "loadSchemeDropdownOptions", "snapshot", "paramMap", "get", "loadEditModeData", "update", "getEmbeddingModelOptions", "subscribe", "options", "opt", "modelDeploymentName", "value", "String", "id", "getSchemeDropdowns", "response", "parsedValue", "JSON", "parse", "Object", "keys", "error", "console", "onUploadTypeChange", "event", "selected", "selectedOptions", "values", "flat", "for<PERSON>ach", "ctrlName", "ctrl", "clearValidators", "setValue", "mark<PERSON><PERSON>ristine", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setValidators", "<PERSON><PERSON><PERSON><PERSON>ched", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "updateValueAndValidity", "onSave", "invalid", "length", "mark<PERSON>llAsTouched", "suffix", "retrieverType", "knowledgeBase", "selectedModelValue", "selected<PERSON><PERSON>l", "find", "selectedModelId", "payload", "file", "formData", "FormData", "append", "submitUpload", "next", "info", "message", "err", "data", "fieldName", "field", "formattedFieldName", "test", "char<PERSON>t", "toUpperCase", "slice", "touched", "dirty", "errors", "<PERSON><PERSON><PERSON><PERSON>", "onExit", "navigate", "getUploadDropdowns", "res", "getUploadTypeOptions", "firstOption", "defaultValue", "rawValue", "parsed", "to<PERSON><PERSON>", "toLowerCase", "replace", "some", "size", "isSubmitDisabled", "isFormInvalid", "isUploadTypeFile", "isFileEmpty", "isParentDoc", "Number", "isNormalSplitSizeInvalid", "isParentChildInvalid", "handlePopupClose", "formatDocumentSize", "transform", "formatRows", "row", "formatUploadDate", "dateStr", "date", "Date", "day", "getDate", "padStart", "month", "getMonth", "year", "getFullYear", "hours", "getHours", "minutes", "getMinutes", "getRetrieverLabel", "normal", "parent_doc_retriever", "kbId", "getKnowledgeBaseById", "files", "fileName", "fileSizeFormatted", "source", "fileSizeBytes", "uploadDateFormatted", "uploadDate", "disable", "fetchAllKnowledge", "pipe", "match", "item", "patchValue", "collectionName", "controlName", "inputValue", "target", "valueAsNumber", "cappedValue", "Math", "min", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "Router", "i3", "i4", "KnowledgeBaseService", "ActivatedRoute", "selectors", "features", "ɵɵProvidersFeature", "decls", "vars", "consts", "template", "CreateKnowledgeBaseComponent_Template", "rf", "ctx", "CreateKnowledgeBaseComponent_span_7_Template", "CreateKnowledgeBaseComponent_Template_lucide_icon_click_8_listener", "CreateKnowledgeBaseComponent_div_9_Template", "CreateKnowledgeBaseComponent_Template_ava_button_userClick_17_listener", "confirmUpdate", "confirmSave", "CreateKnowledgeBaseComponent_ng_container_18_Template", "CreateKnowledgeBaseComponent_div_19_Template", "CreateKnowledgeBaseComponent_hr_20_Template", "CreateKnowledgeBaseComponent_Template_ava_dropdown_valueChange_22_listener", "CreateKnowledgeBaseComponent_ng_container_23_Template", "CreateKnowledgeBaseComponent_div_24_Template", "CreateKnowledgeBaseComponent_div_25_Template", "pageTitle", "ɵɵclassProp", "knowledgeBaseConfiguration", "_c0", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "ɵNgNoValidate", "NgControlStatus", "NgControlStatusGroup", "RequiredValidator", "FormControlDirective", "FormGroupDirective", "FormControlName", "i5", "LucideAngularComponent", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\pages\\libraries\\knowledge-base\\create-knowledge-base\\create-knowledge-base.component.ts", "C:\\console\\aava-ui-web\\projects\\shared\\pages\\libraries\\knowledge-base\\create-knowledge-base\\create-knowledge-base.component.html"], "sourcesContent": ["import { Component, CUSTOM_ELEMENTS_SCHEMA, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { DecimalPipe } from '@angular/common';\r\nimport {\r\n  FormBuilder,\r\n  FormGroup,\r\n  ReactiveFormsModule,\r\n  FormControl,\r\n  FormsModule,\r\n  Validators,\r\n} from '@angular/forms';\r\nimport { Router, ActivatedRoute } from '@angular/router';\r\nimport knowledgeBaseLabels from '../constants/knowledge-base.json';\r\nimport {\r\n  SliderComponent,\r\n  DropdownComponent,\r\n  AvaTextboxComponent,\r\n  AvaTextareaComponent,\r\n  FileUploadComponent,\r\n  ButtonComponent,\r\n  TableComponent,\r\n} from '@ava/play-comp-library';\r\nimport { KnowledgeBaseService } from '@shared/services/knowledge-base.service';\r\nimport { PopupComponent } from '@ava/play-comp-library';\r\nimport { LucideAngularModule } from 'lucide-angular';\r\nimport { map } from 'rxjs/internal/operators/map';\r\n\r\n@Component({\r\n  selector: 'app-create-knowledge-base',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    ReactiveFormsModule,\r\n    FormsModule,\r\n    LucideAngularModule,\r\n    DropdownComponent,\r\n    AvaTextboxComponent,\r\n    AvaTextareaComponent,\r\n    SliderComponent,\r\n    FileUploadComponent,\r\n    PopupComponent,\r\n    ButtonComponent,\r\n    TableComponent,\r\n  ],\r\n  providers: [DecimalPipe],\r\n  templateUrl: './create-knowledge-base.component.html',\r\n  styleUrls: ['./create-knowledge-base.component.scss'],\r\n  schemas: [CUSTOM_ELEMENTS_SCHEMA],\r\n})\r\nexport class CreateKnowledgeBaseComponent implements OnInit {\r\n  // Form group for the knowledge base creation form\r\n  knowledgeBaseForm: FormGroup;\r\n\r\n  // Stores route param for edit mode\r\n  knowledgeBaseId: string | null = null;\r\n\r\n  // Popup & submission state\r\n  submissionMessage: string | null = null;\r\n  submissionSuccess = false;\r\n  showInfoPopup = false;\r\n  fileUploadRequired = false;\r\n\r\n  // UI mode tracking\r\n  isEditMode = false;\r\n\r\n  //maxFile size is 15MB \r\n  maxFileSize = 15 * 1024 * 1024;\r\n\r\n  // Allowed file formats for upload\r\n  allowedFormats: string[] = knowledgeBaseLabels.allowedFormats;\r\n\r\n  // UI text constants\r\n  componentTitle: string = 'Upload File Here';\r\n  showUploadButton = true;\r\n  showDialogCloseIcon = false;\r\n\r\n  // Selected options\r\n  selectedRetriever = knowledgeBaseLabels.labels.default;\r\n  selectedUploadType = 'upload-files';\r\n  uploadPlaceholder = 'Upload Files';\r\n  iconName = 'circle-check';\r\n  iconColor = 'red';\r\n\r\n  // Model search input text (currently unused)\r\n  searchModelText = '';\r\n\r\n  // Uploaded files\r\n  uploadedFiles: File[] = [];\r\n\r\n  hasZeroKbFile = false;\r\n\r\n  // Split size fields\r\n  splitSize: number = 5000;\r\n  parentSplitSize: number = 5000;\r\n  childSplitSize: number = 2000;\r\n\r\n  // Dropdown data\r\n  embeddingModelOptions: { name: string; value: string }[] = [];\r\n  schemeOptions: { name: string; value: string }[] = [];\r\n\r\n  uploadOptions: any[] = [];\r\n\r\n  // Label map\r\n  kbLabels = knowledgeBaseLabels.labels;\r\n\r\n  // Retriever options for toggle\r\n  retrieverOptions: string[] = [this.kbLabels.default, this.kbLabels.parentDoc];\r\n\r\n  // Submit button label (Save / Update)\r\n  saveOrUpdateLabel: string = this.kbLabels.save;\r\n\r\n  // Controls required per upload type\r\n  private selectedControls: Record<string, string[]> = {\r\n    'upload-files': [],\r\n    'Azure Blob': knowledgeBaseLabels.azureBlob,\r\n    'GitHub': knowledgeBaseLabels.github,\r\n    'Share Point': knowledgeBaseLabels.sharePoint,\r\n    'Confluence Wiki': knowledgeBaseLabels.confluenceWiki,\r\n    'Database': knowledgeBaseLabels.database\r\n  };\r\n\r\n  // API path suffix map based on upload source\r\n  private pathSuffix: Record<string, string> = {\r\n    'Azure Blob': '/blob',\r\n    'GitHub': '/github',\r\n    'Share Point': '/sharepoint',\r\n    'Confluence Wiki': '/confluence',\r\n    'Database': '/database',\r\n  };\r\n\r\n  tableColumns = [\r\n    { key: 'fileName', label: 'File Name', type: 'text' },\r\n    { key: 'fileSizeFormatted', label: 'File Size', type: 'text' },\r\n    { key: 'uploadDateFormatted', label: 'Upload Date', type: 'text' },\r\n    { key: 'retrieverType', label: 'Retriever Type', type: 'text' },\r\n  ];\r\n\r\n  tableData: any[] = [];\r\n\r\n  isLeftCollapsed = false;\r\n\r\n  toggleLeftPanel() {\r\n    this.isLeftCollapsed = !this.isLeftCollapsed;\r\n  }\r\n\r\n  // Constructor injecting services and initializing form group\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private router: Router,\r\n    public _decimalPipe: DecimalPipe,\r\n    private knowledgeBaseService: KnowledgeBaseService,\r\n    private route: ActivatedRoute,\r\n  ) {\r\n    // Define form controls and validators\r\n    this.knowledgeBaseForm = this.fb.group({\r\n      name: ['', [Validators.required, Validators.minLength(3)]],\r\n      description: ['', Validators.required],\r\n      retriever: [this.kbLabels.default],\r\n      splitSize: [5000],\r\n      parentSplitSize: [5000],\r\n      childSplitSize: [2000],\r\n      embeddingModel: [null, Validators.required],\r\n      uploadType: ['', Validators.required],\r\n      containerName: [''],\r\n      accountName: [''],\r\n      accountKey: [''],\r\n      githubKey: [''],\r\n      githubAccount: [''],\r\n      githubRepo: [''],\r\n      githubBranch: [''],\r\n      tenantId: [''],\r\n      sharepointSiteName: [''],\r\n      sharepointFolderPath: [''],\r\n      email: [''],\r\n      clientId: [''],\r\n      clientSecret: [''],\r\n      apiToken: [''],\r\n      baseUrl: [''],\r\n      spaceKey: [''],\r\n      overlap: [''],\r\n      scheme: [''],\r\n      host: [''],\r\n      port: [''],\r\n      user: [''],\r\n      password: [''],\r\n      dbname: [''],\r\n      query: [''],\r\n    });\r\n  }\r\n\r\n  // Lifecycle hook: on component init\r\n  ngOnInit(): void {\r\n    this.loadEmbeddingModelOptions();\r\n    this.loadUploadOptionsAndSetDefault();\r\n    this.loadSchemeDropdownOptions();\r\n    // Check if editing an existing knowledge base\r\n    this.knowledgeBaseId = this.route.snapshot.paramMap.get('id');\r\n    this.isEditMode = !!this.knowledgeBaseId;\r\n    if (this.isEditMode) {\r\n      this.loadEditModeData();\r\n    }\r\n\r\n    this.saveOrUpdateLabel = this.isEditMode\r\n      ? this.kbLabels.update\r\n      : this.kbLabels.save;\r\n  }\r\n\r\n  // Fetch available embedding models\r\n  loadEmbeddingModelOptions(): void {\r\n    this.knowledgeBaseService\r\n      .getEmbeddingModelOptions()\r\n      .subscribe((options) => {\r\n        this.embeddingModelOptions = options.map((opt: any) => ({\r\n          name: opt.modelDeploymentName,\r\n          value: String(opt.id),\r\n        }));\r\n      });\r\n  }\r\n\r\n\r\n  // Scheme Dropdown values\r\n  loadSchemeDropdownOptions(): void {\r\n    this.knowledgeBaseService.getSchemeDropdowns().subscribe((response) => {\r\n      if (response && response.value) {\r\n        try {\r\n          const parsedValue = JSON.parse(response.value); // parse string to object\r\n          this.schemeOptions = Object.keys(parsedValue).map((key) => ({\r\n            name: key,\r\n            value: parsedValue[key]\r\n          }));\r\n        } catch (error) {\r\n          console.error('Failed to parse scheme options:', error);\r\n          this.schemeOptions = [];\r\n        }\r\n      } else {\r\n        this.schemeOptions = [];\r\n      }\r\n    });\r\n  }\r\n\r\n  // Triggered when upload type changes\r\n  onUploadTypeChange(event: any): void {\r\n    const selected = event?.selectedOptions?.[0]?.value;\r\n    this.selectedUploadType = selected || '';\r\n    this.uploadPlaceholder = '';\r\n\r\n    if (this.selectedUploadType !== 'upload-files') {\r\n      this.uploadedFiles = [];\r\n    }\r\n    // Clear all old validators AND values\r\n    Object.values(this.selectedControls)\r\n      .flat()\r\n      .forEach((ctrlName) => {\r\n        const ctrl = this.getControl(ctrlName);\r\n        if (ctrl) {\r\n          ctrl.clearValidators();\r\n          ctrl.setValue(null); // or ''\r\n          ctrl.markAsPristine();\r\n          ctrl.markAsUntouched();\r\n        }\r\n      });\r\n\r\n\r\n    // Apply required validators to new controls\r\n    this.selectedControls[this.selectedUploadType]?.forEach((ctrl) => {\r\n      this.getControl(ctrl)?.setValidators(Validators.required);\r\n    });\r\n\r\n    // Reapply validator to uploadType itself\r\n    this.getControl('uploadType')?.setValidators(Validators.required);\r\n\r\n    // Mark uploadType as interacted\r\n    this.getControl('uploadType')?.markAsTouched();\r\n    this.getControl('uploadType')?.markAsDirty();\r\n\r\n    // Recalculate form status\r\n    this.knowledgeBaseForm.updateValueAndValidity();\r\n  }\r\n\r\n  // Submit form handler\r\n  onSave(): void {\r\n    this.submissionSuccess = false;\r\n    this.fileUploadRequired = false;\r\n\r\n    // Basic form validation\r\n    if (this.knowledgeBaseForm.invalid && this.uploadedFiles.length === 0) {\r\n      this.knowledgeBaseForm.markAllAsTouched();\r\n      this.fileUploadRequired = true;\r\n      return;\r\n    }\r\n\r\n    const type = this.knowledgeBaseForm.value.uploadType;\r\n    const suffix = this.pathSuffix[type] || '';\r\n    const retrieverType =\r\n      this.selectedRetriever === this.kbLabels.parentDoc\r\n        ? 'parent_doc_retriever'\r\n        : 'normal';\r\n\r\n    const knowledgeBase = this.getControl('name').value;\r\n    const description = this.getControl('description').value;\r\n    const splitSize = this.getControl('splitSize').value;\r\n    const parentSplitSize = this.getControl('parentSplitSize')?.value;\r\n    const childSplitSize = this.getControl('childSplitSize')?.value;\r\n\r\n    // Get selected model ID\r\n    const selectedModelValue = this.knowledgeBaseForm.value['embeddingModel'];\r\n    const selectedModel = this.embeddingModelOptions.find(\r\n      (opt) =>\r\n        opt.name === selectedModelValue || opt.value === selectedModelValue,\r\n    );\r\n    const selectedModelId = selectedModel ? selectedModel.value : null;\r\n\r\n    // Construct base payload\r\n    const payload: Record<string, any> = {};\r\n\r\n    if (this.isEditMode && this.knowledgeBaseId) {\r\n      // Only send masterId during edit\r\n      payload['masterId'] = this.knowledgeBaseId;\r\n    } else {\r\n      // Full payload during create\r\n      payload['knowledgeBase'] = knowledgeBase;\r\n      payload['description'] = description;\r\n      payload['model-ref'] = selectedModelId;\r\n      payload['type'] = retrieverType;\r\n\r\n      if (retrieverType === 'parent_doc_retriever') {\r\n        payload['parentSplitSize'] = parentSplitSize;\r\n        payload['splitSize'] = childSplitSize;\r\n      } else {\r\n        payload['splitSize'] = splitSize;\r\n      }\r\n    }\r\n\r\n    // Handle file upload case\r\n    if (type === 'upload-files' || type == 'Upload Files') {\r\n      const file = this.uploadedFiles;\r\n      if (!file) return;\r\n\r\n      const formData = new FormData();\r\n      this.uploadedFiles.forEach((file: File) => {\r\n        formData.append('files', file);\r\n      });\r\n\r\n      // Submit form with files\r\n      this.knowledgeBaseService\r\n        .submitUpload(formData, payload, suffix)\r\n        .subscribe({\r\n          next: (info) => {\r\n            this.iconName = 'circle-check';\r\n            this.submissionMessage = info?.info?.message || info.message;\r\n            this.submissionSuccess = true;\r\n            this.showInfoPopup = true;\r\n            this.iconColor = 'green';\r\n          },\r\n          error: (err) => {\r\n            this.iconName = 'alert-circle';\r\n            this.submissionMessage =\r\n              err?.error?.message || err.message || 'Knowledge Base creation or update failed. Please try again.';\r\n            this.submissionSuccess = false;\r\n            this.showInfoPopup = true;\r\n            this.iconColor = 'red';\r\n          },\r\n        });\r\n\r\n      // Handle non-file (external source) upload case\r\n    } else {\r\n      const data: Record<string, any> = {};\r\n\r\n      // Add selected controls to both body and query params\r\n\r\n      this.selectedControls[type]?.forEach((key) => {\r\n        const value = this.getControl(key)?.value;\r\n        data[key] = value;\r\n\r\n        // If edit mode, also include these as query params (i.e., in payload)\r\n        if (this.isEditMode) {\r\n          payload[key] = value;\r\n        }\r\n      });\r\n\r\n      // Also add masterId for edit mode\r\n\r\n      this.knowledgeBaseService.submitUpload(data, payload, suffix).subscribe({\r\n        next: (info) => {\r\n          this.iconName = 'circle-check';\r\n          this.submissionMessage = info?.info?.message || info.message;\r\n          this.submissionSuccess = true;\r\n          this.showInfoPopup = true;\r\n          this.iconColor = 'green';\r\n        },\r\n        error: (err) => {\r\n          this.iconName = 'alert-circle';\r\n          this.submissionMessage =\r\n            err?.error?.message || err.message || 'Knowledge Base creation or update failed. Please try again.';\r\n          this.submissionSuccess = false;\r\n          this.showInfoPopup = true;\r\n          this.iconColor = 'red';\r\n        },\r\n      });\r\n    }\r\n  }\r\n\r\n  // Get a typed form control\r\n  getControl(name: string): FormControl {\r\n    return this.knowledgeBaseForm.get(name) as FormControl;\r\n  }\r\n\r\n  // Utility to return readable field validation error\r\n  getFieldError(fieldName: string): string {\r\n    const field = this.knowledgeBaseForm.get(fieldName);\r\n    // Capitalize only if first letter is not already uppercase\r\n    const formattedFieldName = /^[A-Z]/.test(fieldName)\r\n      ? fieldName\r\n      : fieldName.charAt(0).toUpperCase() + fieldName.slice(1);\r\n    if (field && field.invalid && (field.touched || field.dirty)) {\r\n      if (field.errors?.['required']) {\r\n        return `${formattedFieldName} is required`;\r\n      }\r\n      if (field.errors?.['email']) {\r\n        return 'Please enter a valid email address';\r\n      }\r\n      if (field.errors?.['minlength']) {\r\n        return `${formattedFieldName} must be at least ${field.errors['minlength'].requiredLength} characters long`;\r\n      }\r\n    } else if (fieldName == 'scheme' || fieldName == 'embeddingModel') {\r\n      if (field && field.errors?.['required']) {\r\n        return `${formattedFieldName} is required`;\r\n      }\r\n    }\r\n    return '';\r\n  }\r\n\r\n  // Navigate back to listing\r\n  onExit(): void {\r\n    this.router.navigate(['/libraries/knowledge-base']);\r\n  }\r\n\r\n  // Load upload options and set first one as default\r\n  loadUploadOptionsAndSetDefault(): void {\r\n    this.knowledgeBaseService.getUploadDropdowns().subscribe({\r\n      next: (res) => {\r\n        if (res?.value) {\r\n          this.uploadOptions = this.getUploadTypeOptions(res.value);\r\n\r\n          // Safely assign default value and apply\r\n          const firstOption = this.uploadOptions[0];\r\n          if (firstOption && firstOption.value) {\r\n            const defaultValue = firstOption.value;\r\n\r\n            // Set form control\r\n            this.knowledgeBaseForm.get('uploadType')?.setValue(defaultValue);\r\n\r\n            // Set component state\r\n            this.selectedUploadType = defaultValue;\r\n\r\n            // Trigger validators and placeholder update\r\n            this.onUploadTypeChange({\r\n              selectedOptions: [{ value: defaultValue }],\r\n            });\r\n          }\r\n        }\r\n      },\r\n      error: () => { },\r\n    });\r\n  }\r\n\r\n  // Converts string labels into slug format\r\n  getUploadTypeOptions(rawValue: string): { name: string; value: string }[] {\r\n    const parsed = JSON.parse(rawValue);\r\n    return Object.keys(parsed).map((label) => ({\r\n      name: label,\r\n      value: this.toSlug(label),\r\n    }));\r\n  }\r\n\r\n  // Utility for slug formatting\r\n  toSlug(label: string): string {\r\n    return label.toLowerCase().replace(/\\s+/g, '-');\r\n  }\r\n\r\n  // Handle retriever mode toggle\r\n  selectRetriever(retriever: string): void {\r\n    this.selectedRetriever = retriever;\r\n    this.knowledgeBaseForm.get('retriever')?.setValue(retriever);\r\n  }\r\n\r\n  onParentSplitSizeChange(event: any): void {\r\n    this.parentSplitSize = event;\r\n    this.knowledgeBaseForm\r\n      .get('parentSplitSize')\r\n      ?.setValue(this.parentSplitSize);\r\n  }\r\n\r\n  onChildSplitSizeChange(event: any): void {\r\n    this.childSplitSize = event;\r\n    this.knowledgeBaseForm.get('childSplitSize')?.setValue(this.childSplitSize);\r\n  }\r\n\r\n  // splitSize input setter\r\n  splitSizeChange(event: any) {\r\n    this.splitSize = event;\r\n    this.knowledgeBaseForm\r\n      .get('splitSize')\r\n      ?.setValue(this.splitSize);\r\n  }\r\n\r\n  \r\n  // Update file list after change\r\n  filesListChanged(file: File[]) {\r\n    this.uploadedFiles = [...file];\r\n    this.hasZeroKbFile = this.uploadedFiles.some(file => file.size === 0);\r\n  }\r\n\r\n  // Disable submit based on form or file state\r\n  isSubmitDisabled(): boolean {\r\n    const isFormInvalid = this.knowledgeBaseForm.invalid;\r\n    const isUploadTypeFile = this.selectedUploadType === 'upload-files';\r\n    const isFileEmpty = this.uploadedFiles.length === 0;\r\n    const isParentDoc = this.selectedRetriever === this.kbLabels.parentDoc;\r\n\r\n    const splitSize = Number(this.knowledgeBaseForm.get('splitSize')?.value);\r\n    const parentSplitSize = Number(this.knowledgeBaseForm.get('parentSplitSize')?.value);\r\n    const childSplitSize = Number(this.knowledgeBaseForm.get('childSplitSize')?.value);\r\n\r\n    //  Normal condition: splitSize must be at least 100\r\n    const isNormalSplitSizeInvalid =\r\n      !isParentDoc && splitSize < 100;\r\n\r\n    //  Parent-child condition:\r\n    // 1. parent must be > child\r\n    // 2. both must be >= 100\r\n    const isParentChildInvalid =\r\n      isParentDoc &&\r\n      (parentSplitSize <= childSplitSize ||\r\n        parentSplitSize < 100 ||\r\n        childSplitSize < 100);\r\n\r\n    return (\r\n      isFormInvalid ||\r\n      (isUploadTypeFile && isFileEmpty) ||\r\n      isParentChildInvalid ||\r\n      isNormalSplitSizeInvalid ||\r\n      this.hasZeroKbFile\r\n    );\r\n  }\r\n\r\n\r\n  // Close popup and navigate on success\r\n  handlePopupClose(): void {\r\n    this.showInfoPopup = false;\r\n    if (this.submissionSuccess) {\r\n      this.router.navigate(['/libraries/knowledge-base']);\r\n    }\r\n  }\r\n\r\n  // Format file size from bytes to MB string\r\n  formatDocumentSize(size: number): string {\r\n    if (size < 102400) {\r\n      return this._decimalPipe.transform(size / 1024, '1.0-2') + ' KB';\r\n    } else {\r\n      return this._decimalPipe.transform(size / 1048576, '1.2-2') + ' MB';\r\n    }\r\n  }\r\n\r\n  // Format rows\r\n  formatRows(row: number) {\r\n    return row > 1 ? `${row} rows` : `${row} row`;\r\n  }\r\n  \r\n  // Format date to dd/mm/yy, hh:mm format\r\n  formatUploadDate(dateStr: string): string {\r\n    const date = new Date(dateStr);\r\n    const day = String(date.getDate()).padStart(2, '0');\r\n    const month = String(date.getMonth() + 1).padStart(2, '0');\r\n    const year = String(date.getFullYear()).slice(-2);\r\n    const hours = String(date.getHours()).padStart(2, '0');\r\n    const minutes = String(date.getMinutes()).padStart(2, '0');\r\n    return `${day}/${month}/${year}, ${hours}:${minutes}`;\r\n  }\r\n\r\n  // Returns a user-friendly label for the given retriever type\r\n  getRetrieverLabel(type: string): string {\r\n    const map: Record<string, string> = {\r\n      normal: 'Default',\r\n      parent_doc_retriever: 'Parent Doc',\r\n    };\r\n    this.selectedRetriever = map[type];\r\n    return map[type] || 'Unknown';\r\n  }\r\n\r\n  private loadEditModeData(): void {\r\n    const kbId = Number(this.knowledgeBaseId);\r\n\r\n    // Fetch files and other details\r\n    this.knowledgeBaseService.getKnowledgeBaseById(kbId).subscribe({\r\n      next: (response) => {\r\n        if (response?.files?.length > 0) {\r\n          const retrieverType = response.retrieverType || 'normal';\r\n          this.tableData = response.files.map((file: any) => ({\r\n            fileName: file.fileName,\r\n            fileSizeFormatted: file.source === 'database'\r\n              ? this.formatRows(file.fileSizeBytes)\r\n              : this.formatDocumentSize(file.fileSizeBytes),\r\n            uploadDateFormatted: this.formatUploadDate(file.uploadDate),\r\n            retrieverType: this.getRetrieverLabel(retrieverType)\r\n          }));\r\n\r\n          // Disable fields in edit mode\r\n          ['name', 'description', 'splitSize', 'embeddingModel'].forEach(field =>\r\n            this.knowledgeBaseForm.get(field)?.disable()\r\n          );\r\n        } else {\r\n          this.tableData = [];\r\n        }\r\n      },\r\n      error: (err) => {\r\n        console.error('Error fetching knowledge base data', err);\r\n        this.tableData = [];\r\n      }\r\n    });\r\n\r\n    // Fetch and patch name/description\r\n    this.knowledgeBaseService.fetchAllKnowledge()\r\n      .pipe(\r\n        map((response: any[]) => {\r\n          const match = response.find(item => item.id === kbId);\r\n          if (match) {\r\n            this.knowledgeBaseForm.patchValue({\r\n              name: match.collectionName,\r\n              description: match.description,\r\n            });\r\n          }\r\n          return response;\r\n        })\r\n      )\r\n      .subscribe({\r\n        next: () => {\r\n          // Successfully patched the form (already done in map)\r\n        },\r\n        error: () => {\r\n        }\r\n      });\r\n  }\r\n\r\n  onSliderInputChange(event: any, controlName: string): void {\r\n    let inputValue: number;\r\n\r\n    // Handle both native event and direct value\r\n    if (event?.target) {\r\n      inputValue = (event.target as HTMLInputElement).valueAsNumber;\r\n    } else if (typeof event === 'number') {\r\n      inputValue = event;\r\n    } else {\r\n      inputValue = Number(event);\r\n    }\r\n\r\n    // Cap to max value (you can also parametrize this if needed)\r\n    const cappedValue = Math.min(inputValue, 20000);\r\n\r\n    // Update local variable based on controlName\r\n    if (controlName === 'parentSplitSize') {\r\n      this.parentSplitSize = cappedValue;\r\n    } else if (controlName === 'childSplitSize') {\r\n      this.childSplitSize = cappedValue;\r\n    } else if (controlName === 'splitSize') {\r\n      this.splitSize = cappedValue;\r\n    }\r\n    // Update the corresponding form control\r\n    this.knowledgeBaseForm.get(controlName)?.setValue(cappedValue);\r\n  }\r\n}\r\n", "<p class=\"page-title\">{{ kbLabels.pageTitle }}</p>\r\n<div class=\"create-knowledge-base-container\">\r\n  <form [formGroup]=\"knowledgeBaseForm\">\r\n    <div class=\"form-layout\">\r\n      <!-- Left Column -->\r\n      <div class=\"left-column\" [class.collapsed]=\"isLeftCollapsed\">\r\n        <div class=\"left-header\">\r\n          <span class=\"left-title\" *ngIf=\"!isLeftCollapsed\">Knowledge Base Description</span>\r\n          <lucide-icon name=\"panel-left\" class=\"collapse-icon\" (click)=\"toggleLeftPanel()\"></lucide-icon>\r\n        </div>\r\n\r\n        <!-- Knowledge Base Details Card -->\r\n        <div class=\"card-content\" *ngIf=\"!isLeftCollapsed\">\r\n          <ava-textbox id=\"knowledgeBaseName\" placeholder=\"{{kbLabels.placeholderKnowledgeBase}}\"\r\n            label=\"{{kbLabels.knowledgeBaseName}}\" formControlName=\"name\" [control]=\"getControl('name')\"\r\n            [error]=\"getFieldError('name')\" size=\"md\" [fullWidth]=\"true\" [required]=\"true\">\r\n          </ava-textbox>\r\n          <ava-textarea label=\"{{kbLabels.description}}\" placeholder=\"{{kbLabels.placeholderDescription}}\"\r\n            formControlName=\"description\" [control]=\"getControl('description')\" [error]=\"getFieldError('description')\"\r\n            size=\"md\" [fullWidth]=\"true\" [required]=\"true\"></ava-textarea>\r\n        </div>\r\n      </div>\r\n      <!-- Right Column -->\r\n      <div class=\"right-column\">\r\n        <div class=\"card-content\">\r\n          <div class=\"section\">\r\n            <div class=\"knowledge-base-head\">\r\n              <div class=\"knowledge-base-title\">\r\n                {{kbLabels.knowledgeBaseConfiguration}}\r\n              </div>\r\n              <!-- Buttons at bottom of right column -->\r\n              <div class=\"right-column-buttons\">\r\n                <!-- <ava-button label=\"Exit\" (userClick)=\"onExit()\" variant=\"secondary\" size=\"medium\" state=\"default\">\r\n                  </ava-button>  [disabled]=\"isSubmitDisabled()\" -->\r\n                <ava-button label=\"{{saveOrUpdateLabel}}\" (userClick)=\"isEditMode ? confirmUpdate() : confirmSave()\" variant=\"primary\" size=\"medium\"\r\n                  [customStyles]=\"{\r\n                  background:\r\n                    'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',\r\n                  '--button-effect-color': '33, 90, 214',\r\n                }\" state=\"default\" [disabled]=\"isSubmitDisabled()\">\r\n                </ava-button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Retriever Selection -->\r\n          <ng-container *ngIf=\"!isEditMode\">\r\n            <div class=\"section\">\r\n              <h3 class=\"section-title\">{{kbLabels.selectRetriever}}</h3>\r\n              <div class=\"retriever-options\">\r\n                <ava-button *ngFor=\"let retriever of retrieverOptions\" [label]=\"retriever\" size=\"small\"\r\n                  [variant]=\"selectedRetriever === retriever ? 'primary' : 'secondary'\"\r\n                  [state]=\"selectedRetriever === retriever ? 'active' : 'default'\" [disabled]=\"isEditMode\"\r\n                  [customStyles]=\"\r\n                    selectedRetriever === retriever\r\n                      ? {\r\n                          background: 'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',\r\n                          '--button-effect-color': '33, 90, 214',\r\n                          color: '#fff',\r\n                          border: 'none'\r\n                        }\r\n                      : {}\r\n                  \" (userClick)=\"selectRetriever(retriever)\">\r\n                </ava-button>\r\n              </div>\r\n            </div>\r\n            <!-- Split Size Configuration -->\r\n            <div class=\"section\" *ngIf=\"selectedRetriever === 'Default'\">\r\n              <h3 class=\"section-title\">{{kbLabels.splitSize}}</h3>\r\n              <div class=\"split-size-container\">\r\n                <ava-slider [disabled]=\"isEditMode\" [value]=\"splitSize\" [min]=\"100\" [max]=\"20000\" [step]=\"1\"\r\n                  [showTooltip]=\"false\" (valueChange)=\"splitSizeChange($event)\" formControlName=\"splitSize\">\r\n                </ava-slider>\r\n                <div class=\"value-box\">\r\n                  <ava-textbox type=\"number\" (change)=\"onSliderInputChange($event, 'splitSize')\"\r\n                    formControlName=\"splitSize\"></ava-textbox>\r\n                </div>\r\n              </div>\r\n              <!-- Embedding Model Selection Dropdown for Default retriever -->\r\n            </div>\r\n            <!-- Parent Doc Split Size Configuration -->\r\n            <div class=\"section\" *ngIf=\"selectedRetriever === kbLabels.parentDoc\">\r\n              <div class=\"split-section\">\r\n                <h3 class=\"section-title\">{{kbLabels.parentSplitSize}}</h3>\r\n                <div class=\"split-size-container\">\r\n                  <ava-slider [disabled]=\"isEditMode\" [value]=\"parentSplitSize\" [min]=\"100\" [max]=\"20000\" [step]=\"1\"\r\n                    [showTooltip]=\"false\" (valueChange)=\"onParentSplitSizeChange($event)\"\r\n                    formControlName=\"parentSplitSize\">\r\n                  </ava-slider>\r\n                  <div class=\"value-box\">\r\n                    <ava-textbox type=\"number\" (change)=\"onSliderInputChange($event, 'parentSplitSize')\"\r\n                      formControlName=\"parentSplitSize\"></ava-textbox>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div class=\"split-section\">\r\n                <h3 class=\"section-title\">{{kbLabels.childSplitSize}}</h3>\r\n                <div class=\"split-size-container\">\r\n                  <ava-slider [disabled]=\"isEditMode\" [value]=\"childSplitSize\" [min]=\"100\" [max]=\"20000\" [step]=\"1\"\r\n                    [showTooltip]=\"false\" (valueChange)=\"onChildSplitSizeChange($event)\"\r\n                    formControlName=\"childSplitSize\">\r\n                  </ava-slider>\r\n                  <div class=\"value-box\">\r\n                    <ava-textbox type=\"number\" (change)=\"onSliderInputChange($event, 'childSplitSize')\"\r\n                      formControlName=\"childSplitSize\"></ava-textbox>\r\n                  </div>\r\n                </div>\r\n                <div *ngIf=\"selectedRetriever === kbLabels.parentDoc && parentSplitSize <= childSplitSize\"\r\n                  class=\"field-error\">\r\n                  {{kbLabels.parentChildSplitValidation}}\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <ava-dropdown class=\"dropdown-medium\" dropdownTitle=\"{{kbLabels.selectEmbeddingModel}}\" id=\"embeddingModel\"\r\n              label=\"{{kbLabels.embeddingModel}}\" [options]=\"embeddingModelOptions\"\r\n              [formControl]=\"getControl('embeddingModel')\" [error]=\"getFieldError('embeddingModel')\"\r\n              [required]=\"true\"></ava-dropdown>\r\n          </ng-container>\r\n\r\n          <div *ngIf=\"isEditMode\">\r\n            <ava-table [columns]=\"tableColumns\" [data]=\"tableData\">\r\n            </ava-table>\r\n          </div>\r\n          <hr *ngIf=\"!isEditMode\">\r\n          <div class=\"section\">\r\n            <ava-dropdown class=\"dropdown-medium\" label=\"{{kbLabels.uploadType}}\" id=\"uploadType\"\r\n              formControlName=\"uploadType\" [options]=\"uploadOptions\" [selectedValue]=\"uploadPlaceholder\"\r\n              (valueChange)=\"onUploadTypeChange($event)\" [error]=\"getFieldError('uploadType')\" variant=\"primary\"\r\n              size=\"md\" [fullWidth]=\"true\" [required]=\"true\">\r\n            </ava-dropdown>\r\n          </div>\r\n          <ng-container *ngIf=\"selectedUploadType === 'upload-files'\">\r\n            <div class=\"ava-file-upload-wrapper\">\r\n              <ava-file-upload (filesListChanged)=\"filesListChanged($event)\" [allowedFormats]=\"allowedFormats\"\r\n                [showUploadButton]=\"showUploadButton\" [componentTitle]=\"componentTitle\"\r\n                [showDialogCloseIcon]=\"showDialogCloseIcon\" [maxFileSize]=\"maxFileSize\" uploaderId=\"light-uploader\"\r\n                [preview]=\"true\">\r\n              </ava-file-upload>\r\n              <!-- Optional Error Display -->\r\n            </div>\r\n            <div *ngIf=\"hasZeroKbFile\" class=\"field-error\">\r\n              {{kbLabels.zeroKbFileError}}\r\n            </div>\r\n          </ng-container>\r\n          <div *ngIf=\"fileUploadRequired && uploadedFiles.length === 0\" class=\"field-error\">\r\n            {{kbLabels.fileUploadIsRequired}}\r\n          </div>\r\n\r\n          <div *ngIf=\"selectedUploadType\" class=\"upload-fields-grid\">\r\n            <!-- Azure Blob -->\r\n            <ng-container *ngIf=\"selectedUploadType === 'azure-blob'\">\r\n              <h3>{{kbLabels.configSource}}</h3>\r\n              <ava-textbox label=\"{{kbLabels.containerName}}\" placeholder=\"{{kbLabels.placeholderContainerName}}\"\r\n                formControlName=\"containerName\" [control]=\"getControl('containerName')\"\r\n                [error]=\"getFieldError('containerName')\" [required]=\"true\"></ava-textbox>\r\n              <ava-textbox label=\"{{kbLabels.accountName}}\" placeholder=\"{{kbLabels.placeholderAccountName}}\"\r\n                formControlName=\"accountName\" [control]=\"getControl('accountName')\"\r\n                [error]=\"getFieldError('accountName')\" [required]=\"true\"></ava-textbox>\r\n              <ava-textbox label=\"{{kbLabels.accountKey}}\" placeholder=\"{{kbLabels.placeholderAccountKey}}\"\r\n                formControlName=\"accountKey\" [control]=\"getControl('accountKey')\" [error]=\"getFieldError('accountKey')\"\r\n                [required]=\"true\"></ava-textbox>\r\n            </ng-container>\r\n            <!-- GitHub -->\r\n            <ng-container *ngIf=\"selectedUploadType === 'github'\">\r\n              <h3>{{kbLabels.configSource}}</h3>\r\n              <ava-textbox label=\"{{kbLabels.githubKey}}\" formControlName=\"githubKey\"\r\n                placeholder=\"{{kbLabels.placeholderGithubKey}}\" [control]=\"getControl('githubKey')\"\r\n                [error]=\"getFieldError('githubKey')\" [required]=\"true\"></ava-textbox>\r\n              <ava-textbox label=\"{{kbLabels.githubAccount}}\" placeholder=\"{{kbLabels.placeholderGithubAccount}}\"\r\n                formControlName=\"githubAccount\" [control]=\"getControl('githubAccount')\"\r\n                [error]=\"getFieldError('githubAccount')\" [required]=\"true\"></ava-textbox>\r\n              <ava-textbox label=\"{{kbLabels.githubRepository}}\" formControlName=\"githubRepo\"\r\n                placeholder=\"{{kbLabels.placeholderGithubRepository}}\" [control]=\"getControl('githubRepo')\"\r\n                [error]=\"getFieldError('githubRepo')\" [required]=\"true\"></ava-textbox>\r\n              <ava-textbox label=\"{{kbLabels.githubBranch}}\" placeholder=\"{{kbLabels.placeholderGithubBranch}}\"\r\n                formControlName=\"githubBranch\" [control]=\"getControl('githubBranch')\"\r\n                [error]=\"getFieldError('githubBranch')\" [required]=\"true\"></ava-textbox>\r\n            </ng-container>\r\n            <!-- Share Point -->\r\n            <ng-container *ngIf=\"selectedUploadType === 'share-point'\">\r\n              <h3>{{kbLabels.configSource}}</h3>\r\n              <ava-textbox label=\"{{kbLabels.clientId}}\" placeholder=\"{{kbLabels.placeholderClientId}}\"\r\n                formControlName=\"clientId\" [control]=\"getControl('clientId')\" [error]=\"getFieldError('clientId')\"\r\n                [required]=\"true\"></ava-textbox>\r\n              <ava-textbox label=\"{{kbLabels.clientSecret}}\" placeholder=\"{{kbLabels.placeholderClientSecret}}\"\r\n                formControlName=\"clientSecret\" [control]=\"getControl('clientSecret')\"\r\n                [error]=\"getFieldError('clientSecret')\" [required]=\"true\"></ava-textbox>\r\n              <ava-textbox label=\"{{kbLabels.tenantId}}\" placeholder=\"{{kbLabels.placeholderTenantId}}\"\r\n                formControlName=\"tenantId\" [control]=\"getControl('tenantId')\" [error]=\"getFieldError('tenantId')\"\r\n                [required]=\"true\"></ava-textbox>\r\n              <ava-textbox label=\"{{kbLabels.siteName}}\" placeholder=\"{{kbLabels.placeholderSiteName}}\"\r\n                formControlName=\"sharepointSiteName\" [control]=\"getControl('share pointSiteName')\"\r\n                [error]=\"getFieldError('share pointSiteName')\" [required]=\"true\"></ava-textbox>\r\n              <ava-textbox label=\"{{kbLabels.folderPath}}\" placeholder=\"{{kbLabels.placeholderFolderPath}}\"\r\n                formControlName=\"sharepointFolderPath\" [control]=\"getControl('sharepointFolderPath')\"\r\n                [error]=\"getFieldError('sharepointFolderPath')\" [required]=\"true\"></ava-textbox>\r\n            </ng-container>\r\n            <!-- Confluence Wiki -->\r\n            <ng-container *ngIf=\"selectedUploadType === 'confluence-wiki'\">\r\n              <h3>{{kbLabels.configSource}}</h3>\r\n              <ava-textbox label=\"{{kbLabels.emailId}}\" type=\"email\" placeholder=\"{{kbLabels.placeholderEmailId}}\"\r\n                formControlName=\"email\" [control]=\"getControl('email')\" [error]=\"getFieldError('email')\"\r\n                [required]=\"true\"></ava-textbox>\r\n              <ava-textbox label=\"{{kbLabels.confluenceClientId}}\"\r\n                placeholder=\"{{kbLabels.placeholderConfluenceClientId}}\" formControlName=\"clientId\"\r\n                [control]=\"getControl('clientId')\" [error]=\"getFieldError('clientId')\" [required]=\"true\"></ava-textbox>\r\n              <ava-textbox label=\"{{kbLabels.confluenceClientSecret}}\"\r\n                placeholder=\"{{kbLabels.placeholderConfluenceClientSecret}}\" formControlName=\"clientSecret\"\r\n                [control]=\"getControl('clientSecret')\" [error]=\"getFieldError('clientSecret')\"\r\n                [required]=\"true\"></ava-textbox>\r\n              <ava-textbox label=\"{{kbLabels.apiToken}}\" placeholder=\"{{kbLabels.placeholderApiToken}}\"\r\n                formControlName=\"apiToken\" [control]=\"getControl('apiToken')\" [error]=\"getFieldError('apiToken')\"\r\n                [required]=\"true\"></ava-textbox>\r\n              <ava-textbox label=\"{{kbLabels.baseUrl}}\" placeholder=\"{{kbLabels.placeholderBaseUrl}}\"\r\n                formControlName=\"baseUrl\" [control]=\"getControl('baseUrl')\" [error]=\"getFieldError('baseUrl')\"\r\n                [required]=\"true\"></ava-textbox>\r\n              <ava-textbox label=\"{{kbLabels.spaceKey}}\" placeholder=\"{{kbLabels.placeholderSpaceKey}}\"\r\n                formControlName=\"spaceKey\" [control]=\"getControl('spaceKey')\" [error]=\"getFieldError('spaceKey')\"\r\n                [required]=\"true\"></ava-textbox>\r\n              <ava-textbox type=\"number\" label=\"{{kbLabels.overlap}}\" placeholder=\"{{kbLabels.placeholderOverlap}}\"\r\n                formControlName=\"overlap\" [control]=\"getControl('overlap')\" [error]=\"getFieldError('overlap')\"\r\n                [required]=\"true\"></ava-textbox>\r\n            </ng-container>\r\n            <!-- Database -->\r\n            <ng-container *ngIf=\"selectedUploadType === 'database'\">\r\n              <h3>{{kbLabels.configSource}}</h3>\r\n              <div class=\"schema-dropdown\">\r\n                <ava-dropdown label=\"{{kbLabels.selectScheme}}\" dropdownTitle=\"{{kbLabels.selectScheme}}\"\r\n                  [options]=\"schemeOptions\" formControlName=\"scheme\" [control]=\"getControl('scheme')\"\r\n                  [error]=\"getFieldError('scheme')\" [required]=\"true\">\r\n                </ava-dropdown>\r\n              </div>\r\n              <ava-textbox label=\"{{kbLabels.dbHost}}\" placeholder=\"{{kbLabels.placeholderHost}}\" formControlName=\"host\"\r\n                [control]=\"getControl('host')\" [error]=\"getFieldError('host')\" [required]=\"true\"></ava-textbox>\r\n              <ava-textbox label=\"{{kbLabels.dbPort}}\" placeholder=\"{{kbLabels.placeholderPort}}\" formControlName=\"port\"\r\n                [control]=\"getControl('port')\" [error]=\"getFieldError('port')\" [required]=\"true\"></ava-textbox>\r\n              <ava-textbox label=\"{{kbLabels.dbUser}}\" placeholder=\"{{kbLabels.placeholderUser}}\" formControlName=\"user\"\r\n                [control]=\"getControl('user')\" [error]=\"getFieldError('user')\" [required]=\"true\"></ava-textbox>\r\n              <ava-textbox label=\"{{kbLabels.dbPassword}}\" placeholder=\"{{kbLabels.placeholderPassword}}\"\r\n                formControlName=\"password\" [control]=\"getControl('password')\" [error]=\"getFieldError('password')\"\r\n                [required]=\"true\"></ava-textbox>\r\n              <ava-textbox label=\"{{kbLabels.dbName}}\" placeholder=\"{{kbLabels.placeholderDbName}}\"\r\n                formControlName=\"dbname\" [control]=\"getControl('dbname')\" [error]=\"getFieldError('dbname')\"\r\n                [required]=\"true\"></ava-textbox>\r\n              <ava-textbox label=\"{{kbLabels.dbQuery}}\" placeholder=\"{{kbLabels.placeholderDbQuery}}\"\r\n                formControlName=\"query\" [control]=\"getControl('query')\" [error]=\"getFieldError('query')\"\r\n                [required]=\"true\"></ava-textbox>\r\n              <ava-textbox type=\"number\" label=\"{{kbLabels.overlap}}\" placeholder=\"{{kbLabels.placeholderOverlap}}\"\r\n                formControlName=\"overlap\" [control]=\"getControl('overlap')\" [error]=\"getFieldError('overlap')\"\r\n                [required]=\"true\"></ava-textbox>\r\n            </ng-container>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </form>\r\n</div>"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,iBAAiB;AAC7C,SAGEC,mBAAmB,EAEnBC,WAAW,EACXC,UAAU,QACL,gBAAgB;AAEvB,OAAOC,mBAAmB,MAAM,kCAAkC;AAClE,SACEC,eAAe,EACfC,iBAAiB,EACjBC,mBAAmB,EACnBC,oBAAoB,EACpBC,mBAAmB,EACnBC,eAAe,EACfC,cAAc,QACT,wBAAwB;AAG/B,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,GAAG,QAAQ,6BAA6B;;;;;;;;;;;;;;;;;;;;IClBvCC,EAAA,CAAAC,cAAA,eAAkD;IAAAD,EAAA,CAAAE,MAAA,iCAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAKrFH,EAAA,CAAAC,cAAA,cAAmD;IAKjDD,EAJA,CAAAI,SAAA,sBAGc,uBAGkD;IAClEJ,EAAA,CAAAG,YAAA,EAAM;;;;IAPgCH,EAAA,CAAAK,SAAA,EAAmD;IAAnDL,EAAA,CAAAM,qBAAA,gBAAAC,MAAA,CAAAC,QAAA,CAAAC,wBAAA,CAAmD;IACrFT,EAAA,CAAAM,qBAAA,UAAAC,MAAA,CAAAC,QAAA,CAAAE,iBAAA,CAAsC;IACuBV,EADC,CAAAW,UAAA,YAAAJ,MAAA,CAAAK,UAAA,SAA8B,UAAAL,MAAA,CAAAM,aAAA,SAC7D,mBAA6B,kBAAkB;IAElEb,EAAA,CAAAK,SAAA,EAAgC;IAAhCL,EAAA,CAAAM,qBAAA,UAAAC,MAAA,CAAAC,QAAA,CAAAM,WAAA,CAAgC;IAACd,EAAA,CAAAM,qBAAA,gBAAAC,MAAA,CAAAC,QAAA,CAAAO,sBAAA,CAAiD;IAEjEf,EADC,CAAAW,UAAA,YAAAJ,MAAA,CAAAK,UAAA,gBAAqC,UAAAL,MAAA,CAAAM,aAAA,gBAAuC,mBAC9E,kBAAkB;;;;;;IA+B1Cb,EAAA,CAAAC,cAAA,qBAY6C;IAAzCD,EAAA,CAAAgB,UAAA,uBAAAC,mGAAA;MAAA,MAAAC,YAAA,GAAAlB,EAAA,CAAAmB,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAd,MAAA,GAAAP,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAahB,MAAA,CAAAiB,eAAA,CAAAN,YAAA,CAA0B;IAAA,EAAC;IAC5ClB,EAAA,CAAAG,YAAA,EAAa;;;;;IAVXH,EAHqD,CAAAW,UAAA,UAAAO,YAAA,CAAmB,YAAAX,MAAA,CAAAkB,iBAAA,KAAAP,YAAA,2BACH,UAAAX,MAAA,CAAAkB,iBAAA,KAAAP,YAAA,wBACL,aAAAX,MAAA,CAAAmB,UAAA,CAAwB,iBAAAnB,MAAA,CAAAkB,iBAAA,KAAAP,YAAA,GAAAlB,EAAA,CAAA2B,eAAA,IAAAC,GAAA,IAAA5B,EAAA,CAAA2B,eAAA,IAAAE,GAAA,EAUvF;;;;;;IAML7B,EADF,CAAAC,cAAA,cAA6D,aACjC;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEnDH,EADF,CAAAC,cAAA,cAAkC,qBAE4D;IAApED,EAAA,CAAAgB,UAAA,yBAAAc,8FAAAC,MAAA;MAAA/B,EAAA,CAAAmB,aAAA,CAAAa,GAAA;MAAA,MAAAzB,MAAA,GAAAP,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAehB,MAAA,CAAA0B,eAAA,CAAAF,MAAA,CAAuB;IAAA,EAAC;IAC/D/B,EAAA,CAAAG,YAAA,EAAa;IAEXH,EADF,CAAAC,cAAA,cAAuB,sBAES;IADHD,EAAA,CAAAgB,UAAA,oBAAAkB,0FAAAH,MAAA;MAAA/B,EAAA,CAAAmB,aAAA,CAAAa,GAAA;MAAA,MAAAzB,MAAA,GAAAP,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAUhB,MAAA,CAAA4B,mBAAA,CAAAJ,MAAA,EAA4B,WAAW,CAAC;IAAA,EAAC;IAKpF/B,EAJoC,CAAAG,YAAA,EAAc,EACxC,EACF,EAEF;;;;IAXsBH,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAAoC,iBAAA,CAAA7B,MAAA,CAAAC,QAAA,CAAA6B,SAAA,CAAsB;IAElCrC,EAAA,CAAAK,SAAA,GAAuB;IACjCL,EADU,CAAAW,UAAA,aAAAJ,MAAA,CAAAmB,UAAA,CAAuB,UAAAnB,MAAA,CAAA8B,SAAA,CAAoB,YAAY,cAAc,WAAW,sBACrE;;;;;IAoCvBrC,EAAA,CAAAC,cAAA,cACsB;IACpBD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAK,SAAA,EACF;IADEL,EAAA,CAAAsC,kBAAA,MAAA/B,MAAA,CAAAC,QAAA,CAAA+B,0BAAA,MACF;;;;;;IA3BAvC,EAFJ,CAAAC,cAAA,cAAsE,cACzC,aACC;IAAAD,EAAA,CAAAE,MAAA,GAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEzDH,EADF,CAAAC,cAAA,cAAkC,qBAGI;IADZD,EAAA,CAAAgB,UAAA,yBAAAwB,8FAAAT,MAAA;MAAA/B,EAAA,CAAAmB,aAAA,CAAAsB,GAAA;MAAA,MAAAlC,MAAA,GAAAP,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAehB,MAAA,CAAAmC,uBAAA,CAAAX,MAAA,CAA+B;IAAA,EAAC;IAEvE/B,EAAA,CAAAG,YAAA,EAAa;IAEXH,EADF,CAAAC,cAAA,cAAuB,sBAEe;IADTD,EAAA,CAAAgB,UAAA,oBAAA2B,0FAAAZ,MAAA;MAAA/B,EAAA,CAAAmB,aAAA,CAAAsB,GAAA;MAAA,MAAAlC,MAAA,GAAAP,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAUhB,MAAA,CAAA4B,mBAAA,CAAAJ,MAAA,EAA4B,iBAAiB,CAAC;IAAA,EAAC;IAI1F/B,EAH0C,CAAAG,YAAA,EAAc,EAC9C,EACF,EACF;IAEJH,EADF,CAAAC,cAAA,cAA2B,aACC;IAAAD,EAAA,CAAAE,MAAA,IAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAExDH,EADF,CAAAC,cAAA,eAAkC,sBAGG;IADXD,EAAA,CAAAgB,UAAA,yBAAA4B,+FAAAb,MAAA;MAAA/B,EAAA,CAAAmB,aAAA,CAAAsB,GAAA;MAAA,MAAAlC,MAAA,GAAAP,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAehB,MAAA,CAAAsC,sBAAA,CAAAd,MAAA,CAA8B;IAAA,EAAC;IAEtE/B,EAAA,CAAAG,YAAA,EAAa;IAEXH,EADF,CAAAC,cAAA,eAAuB,uBAEc;IADRD,EAAA,CAAAgB,UAAA,oBAAA8B,2FAAAf,MAAA;MAAA/B,EAAA,CAAAmB,aAAA,CAAAsB,GAAA;MAAA,MAAAlC,MAAA,GAAAP,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAUhB,MAAA,CAAA4B,mBAAA,CAAAJ,MAAA,EAA4B,gBAAgB,CAAC;IAAA,EAAC;IAGvF/B,EAFuC,CAAAG,YAAA,EAAc,EAC7C,EACF;IACNH,EAAA,CAAA+C,UAAA,KAAAC,kEAAA,kBACsB;IAI1BhD,EADE,CAAAG,YAAA,EAAM,EACF;;;;IA7BwBH,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAAoC,iBAAA,CAAA7B,MAAA,CAAAC,QAAA,CAAAyC,eAAA,CAA4B;IAExCjD,EAAA,CAAAK,SAAA,GAAuB;IACjCL,EADU,CAAAW,UAAA,aAAAJ,MAAA,CAAAmB,UAAA,CAAuB,UAAAnB,MAAA,CAAA0C,eAAA,CAA0B,YAAY,cAAc,WAAW,sBAC3E;IAUCjD,EAAA,CAAAK,SAAA,GAA2B;IAA3BL,EAAA,CAAAoC,iBAAA,CAAA7B,MAAA,CAAAC,QAAA,CAAA0C,cAAA,CAA2B;IAEvClD,EAAA,CAAAK,SAAA,GAAuB;IACjCL,EADU,CAAAW,UAAA,aAAAJ,MAAA,CAAAmB,UAAA,CAAuB,UAAAnB,MAAA,CAAA2C,cAAA,CAAyB,YAAY,cAAc,WAAW,sBAC1E;IAQnBlD,EAAA,CAAAK,SAAA,GAAmF;IAAnFL,EAAA,CAAAW,UAAA,SAAAJ,MAAA,CAAAkB,iBAAA,KAAAlB,MAAA,CAAAC,QAAA,CAAA2C,SAAA,IAAA5C,MAAA,CAAA0C,eAAA,IAAA1C,MAAA,CAAA2C,cAAA,CAAmF;;;;;IA7D/FlD,EAAA,CAAAoD,uBAAA,GAAkC;IAE9BpD,EADF,CAAAC,cAAA,cAAqB,aACO;IAAAD,EAAA,CAAAE,MAAA,GAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3DH,EAAA,CAAAC,cAAA,cAA+B;IAC7BD,EAAA,CAAA+C,UAAA,IAAAM,kEAAA,yBAY6C;IAGjDrD,EADE,CAAAG,YAAA,EAAM,EACF;IAgBNH,EAdA,CAAA+C,UAAA,IAAAO,2DAAA,kBAA6D,IAAAC,2DAAA,oBAcS;IAgCtEvD,EAAA,CAAAI,SAAA,uBAGmC;;;;;IApEPJ,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAAoC,iBAAA,CAAA7B,MAAA,CAAAC,QAAA,CAAAgB,eAAA,CAA4B;IAElBxB,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAW,UAAA,YAAAJ,MAAA,CAAAiD,gBAAA,CAAmB;IAiBnCxD,EAAA,CAAAK,SAAA,EAAqC;IAArCL,EAAA,CAAAW,UAAA,SAAAJ,MAAA,CAAAkB,iBAAA,eAAqC;IAcrCzB,EAAA,CAAAK,SAAA,EAA8C;IAA9CL,EAAA,CAAAW,UAAA,SAAAJ,MAAA,CAAAkB,iBAAA,KAAAlB,MAAA,CAAAC,QAAA,CAAA2C,SAAA,CAA8C;IAgC9BnD,EAAA,CAAAK,SAAA,EAAiD;IAAjDL,EAAA,CAAAM,qBAAA,kBAAAC,MAAA,CAAAC,QAAA,CAAAiD,oBAAA,CAAiD;IACrFzD,EAAA,CAAAM,qBAAA,UAAAC,MAAA,CAAAC,QAAA,CAAAkD,cAAA,CAAmC;IAEnC1D,EAFoC,CAAAW,UAAA,YAAAJ,MAAA,CAAAoD,qBAAA,CAAiC,gBAAApD,MAAA,CAAAK,UAAA,mBACzB,UAAAL,MAAA,CAAAM,aAAA,mBAA0C,kBACrE;;;;;IAGrBb,EAAA,CAAAC,cAAA,UAAwB;IACtBD,EAAA,CAAAI,SAAA,oBACY;IACdJ,EAAA,CAAAG,YAAA,EAAM;;;;IAFOH,EAAA,CAAAK,SAAA,EAAwB;IAACL,EAAzB,CAAAW,UAAA,YAAAJ,MAAA,CAAAqD,YAAA,CAAwB,SAAArD,MAAA,CAAAsD,SAAA,CAAmB;;;;;IAGxD7D,EAAA,CAAAI,SAAA,SAAwB;;;;;IAiBtBJ,EAAA,CAAAC,cAAA,cAA+C;IAC7CD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAK,SAAA,EACF;IADEL,EAAA,CAAAsC,kBAAA,MAAA/B,MAAA,CAAAC,QAAA,CAAAsD,eAAA,MACF;;;;;;IAXF9D,EAAA,CAAAoD,uBAAA,GAA4D;IAExDpD,EADF,CAAAC,cAAA,cAAqC,0BAIhB;IAHFD,EAAA,CAAAgB,UAAA,8BAAA+C,kGAAAhC,MAAA;MAAA/B,EAAA,CAAAmB,aAAA,CAAA6C,GAAA;MAAA,MAAAzD,MAAA,GAAAP,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAoBhB,MAAA,CAAA0D,gBAAA,CAAAlC,MAAA,CAAwB;IAAA,EAAC;IAMhE/B,EAFE,CAAAG,YAAA,EAAkB,EAEd;IACNH,EAAA,CAAA+C,UAAA,IAAAmB,2DAAA,kBAA+C;;;;;IAPkBlE,EAAA,CAAAK,SAAA,GAAiC;IAG9FL,EAH6D,CAAAW,UAAA,mBAAAJ,MAAA,CAAA4D,cAAA,CAAiC,qBAAA5D,MAAA,CAAA6D,gBAAA,CACzD,mBAAA7D,MAAA,CAAA8D,cAAA,CAAkC,wBAAA9D,MAAA,CAAA+D,mBAAA,CAC5B,gBAAA/D,MAAA,CAAAgE,WAAA,CAA4B,iBACvD;IAIdvE,EAAA,CAAAK,SAAA,EAAmB;IAAnBL,EAAA,CAAAW,UAAA,SAAAJ,MAAA,CAAAiE,aAAA,CAAmB;;;;;IAI3BxE,EAAA,CAAAC,cAAA,cAAkF;IAChFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAK,SAAA,EACF;IADEL,EAAA,CAAAsC,kBAAA,MAAA/B,MAAA,CAAAC,QAAA,CAAAiE,oBAAA,MACF;;;;;IAIEzE,EAAA,CAAAoD,uBAAA,GAA0D;IACxDpD,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAOlCH,EANA,CAAAI,SAAA,sBAE2E,sBAGF,sBAGvC;;;;;IAT9BJ,EAAA,CAAAK,SAAA,GAAyB;IAAzBL,EAAA,CAAAoC,iBAAA,CAAA7B,MAAA,CAAAC,QAAA,CAAAkE,YAAA,CAAyB;IAChB1E,EAAA,CAAAK,SAAA,EAAkC;IAAlCL,EAAA,CAAAM,qBAAA,UAAAC,MAAA,CAAAC,QAAA,CAAAmE,aAAA,CAAkC;IAAC3E,EAAA,CAAAM,qBAAA,gBAAAC,MAAA,CAAAC,QAAA,CAAAoE,wBAAA,CAAmD;IAExD5E,EADT,CAAAW,UAAA,YAAAJ,MAAA,CAAAK,UAAA,kBAAuC,UAAAL,MAAA,CAAAM,aAAA,kBAC/B,kBAAkB;IAC/Cb,EAAA,CAAAK,SAAA,EAAgC;IAAhCL,EAAA,CAAAM,qBAAA,UAAAC,MAAA,CAAAC,QAAA,CAAAqE,WAAA,CAAgC;IAAC7E,EAAA,CAAAM,qBAAA,gBAAAC,MAAA,CAAAC,QAAA,CAAAsE,sBAAA,CAAiD;IAEtD9E,EADT,CAAAW,UAAA,YAAAJ,MAAA,CAAAK,UAAA,gBAAqC,UAAAL,MAAA,CAAAM,aAAA,gBAC7B,kBAAkB;IAC7Cb,EAAA,CAAAK,SAAA,EAA+B;IAA/BL,EAAA,CAAAM,qBAAA,UAAAC,MAAA,CAAAC,QAAA,CAAAuE,UAAA,CAA+B;IAAC/E,EAAA,CAAAM,qBAAA,gBAAAC,MAAA,CAAAC,QAAA,CAAAwE,qBAAA,CAAgD;IAE3FhF,EAD6B,CAAAW,UAAA,YAAAJ,MAAA,CAAAK,UAAA,eAAoC,UAAAL,MAAA,CAAAM,aAAA,eAAsC,kBACtF;;;;;IAGrBb,EAAA,CAAAoD,uBAAA,GAAsD;IACpDpD,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAUlCH,EATA,CAAAI,SAAA,sBAEuE,sBAGI,sBAGH,sBAGE;;;;;IAZtEJ,EAAA,CAAAK,SAAA,GAAyB;IAAzBL,EAAA,CAAAoC,iBAAA,CAAA7B,MAAA,CAAAC,QAAA,CAAAkE,YAAA,CAAyB;IAChB1E,EAAA,CAAAK,SAAA,EAA8B;IAA9BL,EAAA,CAAAM,qBAAA,UAAAC,MAAA,CAAAC,QAAA,CAAAyE,SAAA,CAA8B;IACzCjF,EAAA,CAAAM,qBAAA,gBAAAC,MAAA,CAAAC,QAAA,CAAA0E,oBAAA,CAA+C;IACVlF,EADW,CAAAW,UAAA,YAAAJ,MAAA,CAAAK,UAAA,cAAmC,UAAAL,MAAA,CAAAM,aAAA,cAC/C,kBAAkB;IAC3Cb,EAAA,CAAAK,SAAA,EAAkC;IAAlCL,EAAA,CAAAM,qBAAA,UAAAC,MAAA,CAAAC,QAAA,CAAA2E,aAAA,CAAkC;IAACnF,EAAA,CAAAM,qBAAA,gBAAAC,MAAA,CAAAC,QAAA,CAAA4E,wBAAA,CAAmD;IAExDpF,EADT,CAAAW,UAAA,YAAAJ,MAAA,CAAAK,UAAA,kBAAuC,UAAAL,MAAA,CAAAM,aAAA,kBAC/B,kBAAkB;IAC/Cb,EAAA,CAAAK,SAAA,EAAqC;IAArCL,EAAA,CAAAM,qBAAA,UAAAC,MAAA,CAAAC,QAAA,CAAA6E,gBAAA,CAAqC;IAChDrF,EAAA,CAAAM,qBAAA,gBAAAC,MAAA,CAAAC,QAAA,CAAA8E,2BAAA,CAAsD;IAChBtF,EADiB,CAAAW,UAAA,YAAAJ,MAAA,CAAAK,UAAA,eAAoC,UAAAL,MAAA,CAAAM,aAAA,eACtD,kBAAkB;IAC5Cb,EAAA,CAAAK,SAAA,EAAiC;IAAjCL,EAAA,CAAAM,qBAAA,UAAAC,MAAA,CAAAC,QAAA,CAAA+E,YAAA,CAAiC;IAACvF,EAAA,CAAAM,qBAAA,gBAAAC,MAAA,CAAAC,QAAA,CAAAgF,uBAAA,CAAkD;IAEvDxF,EADT,CAAAW,UAAA,YAAAJ,MAAA,CAAAK,UAAA,iBAAsC,UAAAL,MAAA,CAAAM,aAAA,iBAC9B,kBAAkB;;;;;IAG7Db,EAAA,CAAAoD,uBAAA,GAA2D;IACzDpD,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAalCH,EAZA,CAAAI,SAAA,sBAEkC,sBAGwC,sBAGxC,sBAG+C,sBAGC;;;;;IAf9EJ,EAAA,CAAAK,SAAA,GAAyB;IAAzBL,EAAA,CAAAoC,iBAAA,CAAA7B,MAAA,CAAAC,QAAA,CAAAkE,YAAA,CAAyB;IAChB1E,EAAA,CAAAK,SAAA,EAA6B;IAA7BL,EAAA,CAAAM,qBAAA,UAAAC,MAAA,CAAAC,QAAA,CAAAiF,QAAA,CAA6B;IAACzF,EAAA,CAAAM,qBAAA,gBAAAC,MAAA,CAAAC,QAAA,CAAAkF,mBAAA,CAA8C;IAEvF1F,EAD2B,CAAAW,UAAA,YAAAJ,MAAA,CAAAK,UAAA,aAAkC,UAAAL,MAAA,CAAAM,aAAA,aAAoC,kBAChF;IACNb,EAAA,CAAAK,SAAA,EAAiC;IAAjCL,EAAA,CAAAM,qBAAA,UAAAC,MAAA,CAAAC,QAAA,CAAAmF,YAAA,CAAiC;IAAC3F,EAAA,CAAAM,qBAAA,gBAAAC,MAAA,CAAAC,QAAA,CAAAoF,uBAAA,CAAkD;IAEvD5F,EADT,CAAAW,UAAA,YAAAJ,MAAA,CAAAK,UAAA,iBAAsC,UAAAL,MAAA,CAAAM,aAAA,iBAC9B,kBAAkB;IAC9Cb,EAAA,CAAAK,SAAA,EAA6B;IAA7BL,EAAA,CAAAM,qBAAA,UAAAC,MAAA,CAAAC,QAAA,CAAAqF,QAAA,CAA6B;IAAC7F,EAAA,CAAAM,qBAAA,gBAAAC,MAAA,CAAAC,QAAA,CAAAsF,mBAAA,CAA8C;IAEvF9F,EAD2B,CAAAW,UAAA,YAAAJ,MAAA,CAAAK,UAAA,aAAkC,UAAAL,MAAA,CAAAM,aAAA,aAAoC,kBAChF;IACNb,EAAA,CAAAK,SAAA,EAA6B;IAA7BL,EAAA,CAAAM,qBAAA,UAAAC,MAAA,CAAAC,QAAA,CAAAuF,QAAA,CAA6B;IAAC/F,EAAA,CAAAM,qBAAA,gBAAAC,MAAA,CAAAC,QAAA,CAAAwF,mBAAA,CAA8C;IAExChG,EADV,CAAAW,UAAA,YAAAJ,MAAA,CAAAK,UAAA,wBAA6C,UAAAL,MAAA,CAAAM,aAAA,wBACpC,kBAAkB;IACrDb,EAAA,CAAAK,SAAA,EAA+B;IAA/BL,EAAA,CAAAM,qBAAA,UAAAC,MAAA,CAAAC,QAAA,CAAAyF,UAAA,CAA+B;IAACjG,EAAA,CAAAM,qBAAA,gBAAAC,MAAA,CAAAC,QAAA,CAAA0F,qBAAA,CAAgD;IAE3ClG,EADT,CAAAW,UAAA,YAAAJ,MAAA,CAAAK,UAAA,yBAA8C,UAAAL,MAAA,CAAAM,aAAA,yBACtC,kBAAkB;;;;;IAGrEb,EAAA,CAAAoD,uBAAA,GAA+D;IAC7DpD,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAoBlCH,EAnBA,CAAAI,SAAA,sBAEkC,sBAGuE,sBAIvE,sBAGA,sBAGA,sBAGA,sBAGA;;;;;IAtB9BJ,EAAA,CAAAK,SAAA,GAAyB;IAAzBL,EAAA,CAAAoC,iBAAA,CAAA7B,MAAA,CAAAC,QAAA,CAAAkE,YAAA,CAAyB;IAChB1E,EAAA,CAAAK,SAAA,EAA4B;IAA5BL,EAAA,CAAAM,qBAAA,UAAAC,MAAA,CAAAC,QAAA,CAAA2F,OAAA,CAA4B;IAAcnG,EAAA,CAAAM,qBAAA,gBAAAC,MAAA,CAAAC,QAAA,CAAA4F,kBAAA,CAA6C;IAElGpG,EADwB,CAAAW,UAAA,YAAAJ,MAAA,CAAAK,UAAA,UAA+B,UAAAL,MAAA,CAAAM,aAAA,UAAiC,kBACvE;IACNb,EAAA,CAAAK,SAAA,EAAuC;IAAvCL,EAAA,CAAAM,qBAAA,UAAAC,MAAA,CAAAC,QAAA,CAAA6F,kBAAA,CAAuC;IAClDrG,EAAA,CAAAM,qBAAA,gBAAAC,MAAA,CAAAC,QAAA,CAAA8F,6BAAA,CAAwD;IACetG,EAAvE,CAAAW,UAAA,YAAAJ,MAAA,CAAAK,UAAA,aAAkC,UAAAL,MAAA,CAAAM,aAAA,aAAoC,kBAAkB;IAC7Eb,EAAA,CAAAK,SAAA,EAA2C;IAA3CL,EAAA,CAAAM,qBAAA,UAAAC,MAAA,CAAAC,QAAA,CAAA+F,sBAAA,CAA2C;IACtDvG,EAAA,CAAAM,qBAAA,gBAAAC,MAAA,CAAAC,QAAA,CAAAgG,iCAAA,CAA4D;IAE5DxG,EADA,CAAAW,UAAA,YAAAJ,MAAA,CAAAK,UAAA,iBAAsC,UAAAL,MAAA,CAAAM,aAAA,iBAAwC,kBAC7D;IACNb,EAAA,CAAAK,SAAA,EAA6B;IAA7BL,EAAA,CAAAM,qBAAA,UAAAC,MAAA,CAAAC,QAAA,CAAAiG,QAAA,CAA6B;IAACzG,EAAA,CAAAM,qBAAA,gBAAAC,MAAA,CAAAC,QAAA,CAAAkG,mBAAA,CAA8C;IAEvF1G,EAD2B,CAAAW,UAAA,YAAAJ,MAAA,CAAAK,UAAA,aAAkC,UAAAL,MAAA,CAAAM,aAAA,aAAoC,kBAChF;IACNb,EAAA,CAAAK,SAAA,EAA4B;IAA5BL,EAAA,CAAAM,qBAAA,UAAAC,MAAA,CAAAC,QAAA,CAAAmG,OAAA,CAA4B;IAAC3G,EAAA,CAAAM,qBAAA,gBAAAC,MAAA,CAAAC,QAAA,CAAAoG,kBAAA,CAA6C;IAErF5G,EAD0B,CAAAW,UAAA,YAAAJ,MAAA,CAAAK,UAAA,YAAiC,UAAAL,MAAA,CAAAM,aAAA,YAAmC,kBAC7E;IACNb,EAAA,CAAAK,SAAA,EAA6B;IAA7BL,EAAA,CAAAM,qBAAA,UAAAC,MAAA,CAAAC,QAAA,CAAAqG,QAAA,CAA6B;IAAC7G,EAAA,CAAAM,qBAAA,gBAAAC,MAAA,CAAAC,QAAA,CAAAsG,mBAAA,CAA8C;IAEvF9G,EAD2B,CAAAW,UAAA,YAAAJ,MAAA,CAAAK,UAAA,aAAkC,UAAAL,MAAA,CAAAM,aAAA,aAAoC,kBAChF;IACQb,EAAA,CAAAK,SAAA,EAA4B;IAA5BL,EAAA,CAAAM,qBAAA,UAAAC,MAAA,CAAAC,QAAA,CAAAuG,OAAA,CAA4B;IAAC/G,EAAA,CAAAM,qBAAA,gBAAAC,MAAA,CAAAC,QAAA,CAAAwG,kBAAA,CAA6C;IAEnGhH,EAD0B,CAAAW,UAAA,YAAAJ,MAAA,CAAAK,UAAA,YAAiC,UAAAL,MAAA,CAAAM,aAAA,YAAmC,kBAC7E;;;;;IAGrBb,EAAA,CAAAoD,uBAAA,GAAwD;IACtDpD,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClCH,EAAA,CAAAC,cAAA,cAA6B;IAC3BD,EAAA,CAAAI,SAAA,uBAGe;IACjBJ,EAAA,CAAAG,YAAA,EAAM;IAgBNH,EAfA,CAAAI,SAAA,sBACiG,sBAEA,sBAEA,sBAG/D,sBAGA,uBAGA,uBAGA;;;;;IAxB9BJ,EAAA,CAAAK,SAAA,GAAyB;IAAzBL,EAAA,CAAAoC,iBAAA,CAAA7B,MAAA,CAAAC,QAAA,CAAAkE,YAAA,CAAyB;IAEb1E,EAAA,CAAAK,SAAA,GAAiC;IAAjCL,EAAA,CAAAM,qBAAA,UAAAC,MAAA,CAAAC,QAAA,CAAAyG,YAAA,CAAiC;IAACjH,EAAA,CAAAM,qBAAA,kBAAAC,MAAA,CAAAC,QAAA,CAAAyG,YAAA,CAAyC;IAErDjH,EADlC,CAAAW,UAAA,YAAAJ,MAAA,CAAA2G,aAAA,CAAyB,YAAA3G,MAAA,CAAAK,UAAA,WAA0D,UAAAL,MAAA,CAAAM,aAAA,WAClD,kBAAkB;IAG1Cb,EAAA,CAAAK,SAAA,EAA2B;IAA3BL,EAAA,CAAAM,qBAAA,UAAAC,MAAA,CAAAC,QAAA,CAAA2G,MAAA,CAA2B;IAACnH,EAAA,CAAAM,qBAAA,gBAAAC,MAAA,CAAAC,QAAA,CAAA4G,eAAA,CAA0C;IAClBpH,EAA/D,CAAAW,UAAA,YAAAJ,MAAA,CAAAK,UAAA,SAA8B,UAAAL,MAAA,CAAAM,aAAA,SAAgC,kBAAkB;IACrEb,EAAA,CAAAK,SAAA,EAA2B;IAA3BL,EAAA,CAAAM,qBAAA,UAAAC,MAAA,CAAAC,QAAA,CAAA6G,MAAA,CAA2B;IAACrH,EAAA,CAAAM,qBAAA,gBAAAC,MAAA,CAAAC,QAAA,CAAA8G,eAAA,CAA0C;IAClBtH,EAA/D,CAAAW,UAAA,YAAAJ,MAAA,CAAAK,UAAA,SAA8B,UAAAL,MAAA,CAAAM,aAAA,SAAgC,kBAAkB;IACrEb,EAAA,CAAAK,SAAA,EAA2B;IAA3BL,EAAA,CAAAM,qBAAA,UAAAC,MAAA,CAAAC,QAAA,CAAA+G,MAAA,CAA2B;IAACvH,EAAA,CAAAM,qBAAA,gBAAAC,MAAA,CAAAC,QAAA,CAAAgH,eAAA,CAA0C;IAClBxH,EAA/D,CAAAW,UAAA,YAAAJ,MAAA,CAAAK,UAAA,SAA8B,UAAAL,MAAA,CAAAM,aAAA,SAAgC,kBAAkB;IACrEb,EAAA,CAAAK,SAAA,EAA+B;IAA/BL,EAAA,CAAAM,qBAAA,UAAAC,MAAA,CAAAC,QAAA,CAAAiH,UAAA,CAA+B;IAACzH,EAAA,CAAAM,qBAAA,gBAAAC,MAAA,CAAAC,QAAA,CAAAkH,mBAAA,CAA8C;IAEzF1H,EAD2B,CAAAW,UAAA,YAAAJ,MAAA,CAAAK,UAAA,aAAkC,UAAAL,MAAA,CAAAM,aAAA,aAAoC,kBAChF;IACNb,EAAA,CAAAK,SAAA,EAA2B;IAA3BL,EAAA,CAAAM,qBAAA,UAAAC,MAAA,CAAAC,QAAA,CAAAmH,MAAA,CAA2B;IAAC3H,EAAA,CAAAM,qBAAA,gBAAAC,MAAA,CAAAC,QAAA,CAAAoH,iBAAA,CAA4C;IAEnF5H,EADyB,CAAAW,UAAA,YAAAJ,MAAA,CAAAK,UAAA,WAAgC,UAAAL,MAAA,CAAAM,aAAA,WAAkC,kBAC1E;IACNb,EAAA,CAAAK,SAAA,EAA4B;IAA5BL,EAAA,CAAAM,qBAAA,UAAAC,MAAA,CAAAC,QAAA,CAAAqH,OAAA,CAA4B;IAAC7H,EAAA,CAAAM,qBAAA,gBAAAC,MAAA,CAAAC,QAAA,CAAAsH,kBAAA,CAA6C;IAErF9H,EADwB,CAAAW,UAAA,YAAAJ,MAAA,CAAAK,UAAA,UAA+B,UAAAL,MAAA,CAAAM,aAAA,UAAiC,kBACvE;IACQb,EAAA,CAAAK,SAAA,EAA4B;IAA5BL,EAAA,CAAAM,qBAAA,UAAAC,MAAA,CAAAC,QAAA,CAAAuG,OAAA,CAA4B;IAAC/G,EAAA,CAAAM,qBAAA,gBAAAC,MAAA,CAAAC,QAAA,CAAAwG,kBAAA,CAA6C;IAEnGhH,EAD0B,CAAAW,UAAA,YAAAJ,MAAA,CAAAK,UAAA,YAAiC,UAAAL,MAAA,CAAAM,aAAA,YAAmC,kBAC7E;;;;;IArGvBb,EAAA,CAAAC,cAAA,cAA2D;IA4EzDD,EA1EA,CAAA+C,UAAA,IAAAgF,2DAAA,4BAA0D,IAAAC,2DAAA,4BAaJ,IAAAC,2DAAA,4BAgBK,IAAAC,2DAAA,6BAmBI,IAAAC,2DAAA,6BA0BP;IA2B1DnI,EAAA,CAAAG,YAAA,EAAM;;;;IArGWH,EAAA,CAAAK,SAAA,EAAyC;IAAzCL,EAAA,CAAAW,UAAA,SAAAJ,MAAA,CAAA6H,kBAAA,kBAAyC;IAazCpI,EAAA,CAAAK,SAAA,EAAqC;IAArCL,EAAA,CAAAW,UAAA,SAAAJ,MAAA,CAAA6H,kBAAA,cAAqC;IAgBrCpI,EAAA,CAAAK,SAAA,EAA0C;IAA1CL,EAAA,CAAAW,UAAA,SAAAJ,MAAA,CAAA6H,kBAAA,mBAA0C;IAmB1CpI,EAAA,CAAAK,SAAA,EAA8C;IAA9CL,EAAA,CAAAW,UAAA,SAAAJ,MAAA,CAAA6H,kBAAA,uBAA8C;IA0B9CpI,EAAA,CAAAK,SAAA,EAAuC;IAAvCL,EAAA,CAAAW,UAAA,SAAAJ,MAAA,CAAA6H,kBAAA,gBAAuC;;;AD/KlE,WAAaC,4BAA4B;EAAnC,MAAOA,4BAA4B;IAkG7BC,EAAA;IACAC,MAAA;IACDC,YAAA;IACCC,oBAAA;IACAC,KAAA;IArGV;IACAC,iBAAiB;IAEjB;IACAC,eAAe,GAAkB,IAAI;IAErC;IACAC,iBAAiB,GAAkB,IAAI;IACvCC,iBAAiB,GAAG,KAAK;IACzBC,aAAa,GAAG,KAAK;IACrBC,kBAAkB,GAAG,KAAK;IAE1B;IACAtH,UAAU,GAAG,KAAK;IAElB;IACA6C,WAAW,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI;IAE9B;IACAJ,cAAc,GAAa7E,mBAAmB,CAAC6E,cAAc;IAE7D;IACAE,cAAc,GAAW,kBAAkB;IAC3CD,gBAAgB,GAAG,IAAI;IACvBE,mBAAmB,GAAG,KAAK;IAE3B;IACA7C,iBAAiB,GAAGnC,mBAAmB,CAAC2J,MAAM,CAACC,OAAO;IACtDd,kBAAkB,GAAG,cAAc;IACnCe,iBAAiB,GAAG,cAAc;IAClCC,QAAQ,GAAG,cAAc;IACzBC,SAAS,GAAG,KAAK;IAEjB;IACAC,eAAe,GAAG,EAAE;IAEpB;IACAC,aAAa,GAAW,EAAE;IAE1B/E,aAAa,GAAG,KAAK;IAErB;IACAnC,SAAS,GAAW,IAAI;IACxBY,eAAe,GAAW,IAAI;IAC9BC,cAAc,GAAW,IAAI;IAE7B;IACAS,qBAAqB,GAAsC,EAAE;IAC7DuD,aAAa,GAAsC,EAAE;IAErDsC,aAAa,GAAU,EAAE;IAEzB;IACAhJ,QAAQ,GAAGlB,mBAAmB,CAAC2J,MAAM;IAErC;IACAzF,gBAAgB,GAAa,CAAC,IAAI,CAAChD,QAAQ,CAAC0I,OAAO,EAAE,IAAI,CAAC1I,QAAQ,CAAC2C,SAAS,CAAC;IAE7E;IACAsG,iBAAiB,GAAW,IAAI,CAACjJ,QAAQ,CAACkJ,IAAI;IAE9C;IACQC,gBAAgB,GAA6B;MACnD,cAAc,EAAE,EAAE;MAClB,YAAY,EAAErK,mBAAmB,CAACsK,SAAS;MAC3C,QAAQ,EAAEtK,mBAAmB,CAACuK,MAAM;MACpC,aAAa,EAAEvK,mBAAmB,CAACwK,UAAU;MAC7C,iBAAiB,EAAExK,mBAAmB,CAACyK,cAAc;MACrD,UAAU,EAAEzK,mBAAmB,CAAC0K;KACjC;IAED;IACQC,UAAU,GAA2B;MAC3C,YAAY,EAAE,OAAO;MACrB,QAAQ,EAAE,SAAS;MACnB,aAAa,EAAE,aAAa;MAC5B,iBAAiB,EAAE,aAAa;MAChC,UAAU,EAAE;KACb;IAEDrG,YAAY,GAAG,CACb;MAAEsG,GAAG,EAAE,UAAU;MAAEC,KAAK,EAAE,WAAW;MAAEC,IAAI,EAAE;IAAM,CAAE,EACrD;MAAEF,GAAG,EAAE,mBAAmB;MAAEC,KAAK,EAAE,WAAW;MAAEC,IAAI,EAAE;IAAM,CAAE,EAC9D;MAAEF,GAAG,EAAE,qBAAqB;MAAEC,KAAK,EAAE,aAAa;MAAEC,IAAI,EAAE;IAAM,CAAE,EAClE;MAAEF,GAAG,EAAE,eAAe;MAAEC,KAAK,EAAE,gBAAgB;MAAEC,IAAI,EAAE;IAAM,CAAE,CAChE;IAEDvG,SAAS,GAAU,EAAE;IAErBwG,eAAe,GAAG,KAAK;IAEvBC,eAAeA,CAAA;MACb,IAAI,CAACD,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;IAC9C;IAEA;IACAE,YACUjC,EAAe,EACfC,MAAc,EACfC,YAAyB,EACxBC,oBAA0C,EAC1CC,KAAqB;MAJrB,KAAAJ,EAAE,GAAFA,EAAE;MACF,KAAAC,MAAM,GAANA,MAAM;MACP,KAAAC,YAAY,GAAZA,YAAY;MACX,KAAAC,oBAAoB,GAApBA,oBAAoB;MACpB,KAAAC,KAAK,GAALA,KAAK;MAEb;MACA,IAAI,CAACC,iBAAiB,GAAG,IAAI,CAACL,EAAE,CAACkC,KAAK,CAAC;QACrCC,IAAI,EAAE,CAAC,EAAE,EAAE,CAACpL,UAAU,CAACqL,QAAQ,EAAErL,UAAU,CAACsL,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1D7J,WAAW,EAAE,CAAC,EAAE,EAAEzB,UAAU,CAACqL,QAAQ,CAAC;QACtCE,SAAS,EAAE,CAAC,IAAI,CAACpK,QAAQ,CAAC0I,OAAO,CAAC;QAClC7G,SAAS,EAAE,CAAC,IAAI,CAAC;QACjBY,eAAe,EAAE,CAAC,IAAI,CAAC;QACvBC,cAAc,EAAE,CAAC,IAAI,CAAC;QACtBQ,cAAc,EAAE,CAAC,IAAI,EAAErE,UAAU,CAACqL,QAAQ,CAAC;QAC3CG,UAAU,EAAE,CAAC,EAAE,EAAExL,UAAU,CAACqL,QAAQ,CAAC;QACrC/F,aAAa,EAAE,CAAC,EAAE,CAAC;QACnBE,WAAW,EAAE,CAAC,EAAE,CAAC;QACjBE,UAAU,EAAE,CAAC,EAAE,CAAC;QAChBE,SAAS,EAAE,CAAC,EAAE,CAAC;QACfE,aAAa,EAAE,CAAC,EAAE,CAAC;QACnB2F,UAAU,EAAE,CAAC,EAAE,CAAC;QAChBvF,YAAY,EAAE,CAAC,EAAE,CAAC;QAClBM,QAAQ,EAAE,CAAC,EAAE,CAAC;QACdkF,kBAAkB,EAAE,CAAC,EAAE,CAAC;QACxBC,oBAAoB,EAAE,CAAC,EAAE,CAAC;QAC1BC,KAAK,EAAE,CAAC,EAAE,CAAC;QACXxF,QAAQ,EAAE,CAAC,EAAE,CAAC;QACdE,YAAY,EAAE,CAAC,EAAE,CAAC;QAClBc,QAAQ,EAAE,CAAC,EAAE,CAAC;QACdE,OAAO,EAAE,CAAC,EAAE,CAAC;QACbE,QAAQ,EAAE,CAAC,EAAE,CAAC;QACdE,OAAO,EAAE,CAAC,EAAE,CAAC;QACbmE,MAAM,EAAE,CAAC,EAAE,CAAC;QACZC,IAAI,EAAE,CAAC,EAAE,CAAC;QACVC,IAAI,EAAE,CAAC,EAAE,CAAC;QACVC,IAAI,EAAE,CAAC,EAAE,CAAC;QACVC,QAAQ,EAAE,CAAC,EAAE,CAAC;QACdC,MAAM,EAAE,CAAC,EAAE,CAAC;QACZC,KAAK,EAAE,CAAC,EAAE;OACX,CAAC;IACJ;IAEA;IACAC,QAAQA,CAAA;MACN,IAAI,CAACC,yBAAyB,EAAE;MAChC,IAAI,CAACC,8BAA8B,EAAE;MACrC,IAAI,CAACC,yBAAyB,EAAE;MAChC;MACA,IAAI,CAAChD,eAAe,GAAG,IAAI,CAACF,KAAK,CAACmD,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC;MAC7D,IAAI,CAACrK,UAAU,GAAG,CAAC,CAAC,IAAI,CAACkH,eAAe;MACxC,IAAI,IAAI,CAAClH,UAAU,EAAE;QACnB,IAAI,CAACsK,gBAAgB,EAAE;MACzB;MAEA,IAAI,CAACvC,iBAAiB,GAAG,IAAI,CAAC/H,UAAU,GACpC,IAAI,CAAClB,QAAQ,CAACyL,MAAM,GACpB,IAAI,CAACzL,QAAQ,CAACkJ,IAAI;IACxB;IAEA;IACAgC,yBAAyBA,CAAA;MACvB,IAAI,CAACjD,oBAAoB,CACtByD,wBAAwB,EAAE,CAC1BC,SAAS,CAAEC,OAAO,IAAI;QACrB,IAAI,CAACzI,qBAAqB,GAAGyI,OAAO,CAACrM,GAAG,CAAEsM,GAAQ,KAAM;UACtD5B,IAAI,EAAE4B,GAAG,CAACC,mBAAmB;UAC7BC,KAAK,EAAEC,MAAM,CAACH,GAAG,CAACI,EAAE;SACrB,CAAC,CAAC;MACL,CAAC,CAAC;IACN;IAGA;IACAb,yBAAyBA,CAAA;MACvB,IAAI,CAACnD,oBAAoB,CAACiE,kBAAkB,EAAE,CAACP,SAAS,CAAEQ,QAAQ,IAAI;QACpE,IAAIA,QAAQ,IAAIA,QAAQ,CAACJ,KAAK,EAAE;UAC9B,IAAI;YACF,MAAMK,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACH,QAAQ,CAACJ,KAAK,CAAC,CAAC,CAAC;YAChD,IAAI,CAACrF,aAAa,GAAG6F,MAAM,CAACC,IAAI,CAACJ,WAAW,CAAC,CAAC7M,GAAG,CAAEmK,GAAG,KAAM;cAC1DO,IAAI,EAAEP,GAAG;cACTqC,KAAK,EAAEK,WAAW,CAAC1C,GAAG;aACvB,CAAC,CAAC;UACL,CAAC,CAAC,OAAO+C,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;YACvD,IAAI,CAAC/F,aAAa,GAAG,EAAE;UACzB;QACF,CAAC,MAAM;UACL,IAAI,CAACA,aAAa,GAAG,EAAE;QACzB;MACF,CAAC,CAAC;IACJ;IAEA;IACAiG,kBAAkBA,CAACC,KAAU;MAC3B,MAAMC,QAAQ,GAAGD,KAAK,EAAEE,eAAe,GAAG,CAAC,CAAC,EAAEf,KAAK;MACnD,IAAI,CAACnE,kBAAkB,GAAGiF,QAAQ,IAAI,EAAE;MACxC,IAAI,CAAClE,iBAAiB,GAAG,EAAE;MAE3B,IAAI,IAAI,CAACf,kBAAkB,KAAK,cAAc,EAAE;QAC9C,IAAI,CAACmB,aAAa,GAAG,EAAE;MACzB;MACA;MACAwD,MAAM,CAACQ,MAAM,CAAC,IAAI,CAAC5D,gBAAgB,CAAC,CACjC6D,IAAI,EAAE,CACNC,OAAO,CAAEC,QAAQ,IAAI;QACpB,MAAMC,IAAI,GAAG,IAAI,CAAC/M,UAAU,CAAC8M,QAAQ,CAAC;QACtC,IAAIC,IAAI,EAAE;UACRA,IAAI,CAACC,eAAe,EAAE;UACtBD,IAAI,CAACE,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;UACrBF,IAAI,CAACG,cAAc,EAAE;UACrBH,IAAI,CAACI,eAAe,EAAE;QACxB;MACF,CAAC,CAAC;MAGJ;MACA,IAAI,CAACpE,gBAAgB,CAAC,IAAI,CAACvB,kBAAkB,CAAC,EAAEqF,OAAO,CAAEE,IAAI,IAAI;QAC/D,IAAI,CAAC/M,UAAU,CAAC+M,IAAI,CAAC,EAAEK,aAAa,CAAC3O,UAAU,CAACqL,QAAQ,CAAC;MAC3D,CAAC,CAAC;MAEF;MACA,IAAI,CAAC9J,UAAU,CAAC,YAAY,CAAC,EAAEoN,aAAa,CAAC3O,UAAU,CAACqL,QAAQ,CAAC;MAEjE;MACA,IAAI,CAAC9J,UAAU,CAAC,YAAY,CAAC,EAAEqN,aAAa,EAAE;MAC9C,IAAI,CAACrN,UAAU,CAAC,YAAY,CAAC,EAAEsN,WAAW,EAAE;MAE5C;MACA,IAAI,CAACvF,iBAAiB,CAACwF,sBAAsB,EAAE;IACjD;IAEA;IACAC,MAAMA,CAAA;MACJ,IAAI,CAACtF,iBAAiB,GAAG,KAAK;MAC9B,IAAI,CAACE,kBAAkB,GAAG,KAAK;MAE/B;MACA,IAAI,IAAI,CAACL,iBAAiB,CAAC0F,OAAO,IAAI,IAAI,CAAC9E,aAAa,CAAC+E,MAAM,KAAK,CAAC,EAAE;QACrE,IAAI,CAAC3F,iBAAiB,CAAC4F,gBAAgB,EAAE;QACzC,IAAI,CAACvF,kBAAkB,GAAG,IAAI;QAC9B;MACF;MAEA,MAAMoB,IAAI,GAAG,IAAI,CAACzB,iBAAiB,CAAC4D,KAAK,CAAC1B,UAAU;MACpD,MAAM2D,MAAM,GAAG,IAAI,CAACvE,UAAU,CAACG,IAAI,CAAC,IAAI,EAAE;MAC1C,MAAMqE,aAAa,GACjB,IAAI,CAAChN,iBAAiB,KAAK,IAAI,CAACjB,QAAQ,CAAC2C,SAAS,GAC9C,sBAAsB,GACtB,QAAQ;MAEd,MAAMuL,aAAa,GAAG,IAAI,CAAC9N,UAAU,CAAC,MAAM,CAAC,CAAC2L,KAAK;MACnD,MAAMzL,WAAW,GAAG,IAAI,CAACF,UAAU,CAAC,aAAa,CAAC,CAAC2L,KAAK;MACxD,MAAMlK,SAAS,GAAG,IAAI,CAACzB,UAAU,CAAC,WAAW,CAAC,CAAC2L,KAAK;MACpD,MAAMtJ,eAAe,GAAG,IAAI,CAACrC,UAAU,CAAC,iBAAiB,CAAC,EAAE2L,KAAK;MACjE,MAAMrJ,cAAc,GAAG,IAAI,CAACtC,UAAU,CAAC,gBAAgB,CAAC,EAAE2L,KAAK;MAE/D;MACA,MAAMoC,kBAAkB,GAAG,IAAI,CAAChG,iBAAiB,CAAC4D,KAAK,CAAC,gBAAgB,CAAC;MACzE,MAAMqC,aAAa,GAAG,IAAI,CAACjL,qBAAqB,CAACkL,IAAI,CAClDxC,GAAG,IACFA,GAAG,CAAC5B,IAAI,KAAKkE,kBAAkB,IAAItC,GAAG,CAACE,KAAK,KAAKoC,kBAAkB,CACtE;MACD,MAAMG,eAAe,GAAGF,aAAa,GAAGA,aAAa,CAACrC,KAAK,GAAG,IAAI;MAElE;MACA,MAAMwC,OAAO,GAAwB,EAAE;MAEvC,IAAI,IAAI,CAACrN,UAAU,IAAI,IAAI,CAACkH,eAAe,EAAE;QAC3C;QACAmG,OAAO,CAAC,UAAU,CAAC,GAAG,IAAI,CAACnG,eAAe;MAC5C,CAAC,MAAM;QACL;QACAmG,OAAO,CAAC,eAAe,CAAC,GAAGL,aAAa;QACxCK,OAAO,CAAC,aAAa,CAAC,GAAGjO,WAAW;QACpCiO,OAAO,CAAC,WAAW,CAAC,GAAGD,eAAe;QACtCC,OAAO,CAAC,MAAM,CAAC,GAAGN,aAAa;QAE/B,IAAIA,aAAa,KAAK,sBAAsB,EAAE;UAC5CM,OAAO,CAAC,iBAAiB,CAAC,GAAG9L,eAAe;UAC5C8L,OAAO,CAAC,WAAW,CAAC,GAAG7L,cAAc;QACvC,CAAC,MAAM;UACL6L,OAAO,CAAC,WAAW,CAAC,GAAG1M,SAAS;QAClC;MACF;MAEA;MACA,IAAI+H,IAAI,KAAK,cAAc,IAAIA,IAAI,IAAI,cAAc,EAAE;QACrD,MAAM4E,IAAI,GAAG,IAAI,CAACzF,aAAa;QAC/B,IAAI,CAACyF,IAAI,EAAE;QAEX,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,EAAE;QAC/B,IAAI,CAAC3F,aAAa,CAACkE,OAAO,CAAEuB,IAAU,IAAI;UACxCC,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEH,IAAI,CAAC;QAChC,CAAC,CAAC;QAEF;QACA,IAAI,CAACvG,oBAAoB,CACtB2G,YAAY,CAACH,QAAQ,EAAEF,OAAO,EAAEP,MAAM,CAAC,CACvCrC,SAAS,CAAC;UACTkD,IAAI,EAAGC,IAAI,IAAI;YACb,IAAI,CAAClG,QAAQ,GAAG,cAAc;YAC9B,IAAI,CAACP,iBAAiB,GAAGyG,IAAI,EAAEA,IAAI,EAAEC,OAAO,IAAID,IAAI,CAACC,OAAO;YAC5D,IAAI,CAACzG,iBAAiB,GAAG,IAAI;YAC7B,IAAI,CAACC,aAAa,GAAG,IAAI;YACzB,IAAI,CAACM,SAAS,GAAG,OAAO;UAC1B,CAAC;UACD4D,KAAK,EAAGuC,GAAG,IAAI;YACb,IAAI,CAACpG,QAAQ,GAAG,cAAc;YAC9B,IAAI,CAACP,iBAAiB,GACpB2G,GAAG,EAAEvC,KAAK,EAAEsC,OAAO,IAAIC,GAAG,CAACD,OAAO,IAAI,6DAA6D;YACrG,IAAI,CAACzG,iBAAiB,GAAG,KAAK;YAC9B,IAAI,CAACC,aAAa,GAAG,IAAI;YACzB,IAAI,CAACM,SAAS,GAAG,KAAK;UACxB;SACD,CAAC;QAEJ;MACF,CAAC,MAAM;QACL,MAAMoG,IAAI,GAAwB,EAAE;QAEpC;QAEA,IAAI,CAAC9F,gBAAgB,CAACS,IAAI,CAAC,EAAEqD,OAAO,CAAEvD,GAAG,IAAI;UAC3C,MAAMqC,KAAK,GAAG,IAAI,CAAC3L,UAAU,CAACsJ,GAAG,CAAC,EAAEqC,KAAK;UACzCkD,IAAI,CAACvF,GAAG,CAAC,GAAGqC,KAAK;UAEjB;UACA,IAAI,IAAI,CAAC7K,UAAU,EAAE;YACnBqN,OAAO,CAAC7E,GAAG,CAAC,GAAGqC,KAAK;UACtB;QACF,CAAC,CAAC;QAEF;QAEA,IAAI,CAAC9D,oBAAoB,CAAC2G,YAAY,CAACK,IAAI,EAAEV,OAAO,EAAEP,MAAM,CAAC,CAACrC,SAAS,CAAC;UACtEkD,IAAI,EAAGC,IAAI,IAAI;YACb,IAAI,CAAClG,QAAQ,GAAG,cAAc;YAC9B,IAAI,CAACP,iBAAiB,GAAGyG,IAAI,EAAEA,IAAI,EAAEC,OAAO,IAAID,IAAI,CAACC,OAAO;YAC5D,IAAI,CAACzG,iBAAiB,GAAG,IAAI;YAC7B,IAAI,CAACC,aAAa,GAAG,IAAI;YACzB,IAAI,CAACM,SAAS,GAAG,OAAO;UAC1B,CAAC;UACD4D,KAAK,EAAGuC,GAAG,IAAI;YACb,IAAI,CAACpG,QAAQ,GAAG,cAAc;YAC9B,IAAI,CAACP,iBAAiB,GACpB2G,GAAG,EAAEvC,KAAK,EAAEsC,OAAO,IAAIC,GAAG,CAACD,OAAO,IAAI,6DAA6D;YACrG,IAAI,CAACzG,iBAAiB,GAAG,KAAK;YAC9B,IAAI,CAACC,aAAa,GAAG,IAAI;YACzB,IAAI,CAACM,SAAS,GAAG,KAAK;UACxB;SACD,CAAC;MACJ;IACF;IAEA;IACAzI,UAAUA,CAAC6J,IAAY;MACrB,OAAO,IAAI,CAAC9B,iBAAiB,CAACoD,GAAG,CAACtB,IAAI,CAAgB;IACxD;IAEA;IACA5J,aAAaA,CAAC6O,SAAiB;MAC7B,MAAMC,KAAK,GAAG,IAAI,CAAChH,iBAAiB,CAACoD,GAAG,CAAC2D,SAAS,CAAC;MACnD;MACA,MAAME,kBAAkB,GAAG,QAAQ,CAACC,IAAI,CAACH,SAAS,CAAC,GAC/CA,SAAS,GACTA,SAAS,CAACI,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE,GAAGL,SAAS,CAACM,KAAK,CAAC,CAAC,CAAC;MAC1D,IAAIL,KAAK,IAAIA,KAAK,CAACtB,OAAO,KAAKsB,KAAK,CAACM,OAAO,IAAIN,KAAK,CAACO,KAAK,CAAC,EAAE;QAC5D,IAAIP,KAAK,CAACQ,MAAM,GAAG,UAAU,CAAC,EAAE;UAC9B,OAAO,GAAGP,kBAAkB,cAAc;QAC5C;QACA,IAAID,KAAK,CAACQ,MAAM,GAAG,OAAO,CAAC,EAAE;UAC3B,OAAO,oCAAoC;QAC7C;QACA,IAAIR,KAAK,CAACQ,MAAM,GAAG,WAAW,CAAC,EAAE;UAC/B,OAAO,GAAGP,kBAAkB,qBAAqBD,KAAK,CAACQ,MAAM,CAAC,WAAW,CAAC,CAACC,cAAc,kBAAkB;QAC7G;MACF,CAAC,MAAM,IAAIV,SAAS,IAAI,QAAQ,IAAIA,SAAS,IAAI,gBAAgB,EAAE;QACjE,IAAIC,KAAK,IAAIA,KAAK,CAACQ,MAAM,GAAG,UAAU,CAAC,EAAE;UACvC,OAAO,GAAGP,kBAAkB,cAAc;QAC5C;MACF;MACA,OAAO,EAAE;IACX;IAEA;IACAS,MAAMA,CAAA;MACJ,IAAI,CAAC9H,MAAM,CAAC+H,QAAQ,CAAC,CAAC,2BAA2B,CAAC,CAAC;IACrD;IAEA;IACA3E,8BAA8BA,CAAA;MAC5B,IAAI,CAAClD,oBAAoB,CAAC8H,kBAAkB,EAAE,CAACpE,SAAS,CAAC;QACvDkD,IAAI,EAAGmB,GAAG,IAAI;UACZ,IAAIA,GAAG,EAAEjE,KAAK,EAAE;YACd,IAAI,CAAC/C,aAAa,GAAG,IAAI,CAACiH,oBAAoB,CAACD,GAAG,CAACjE,KAAK,CAAC;YAEzD;YACA,MAAMmE,WAAW,GAAG,IAAI,CAAClH,aAAa,CAAC,CAAC,CAAC;YACzC,IAAIkH,WAAW,IAAIA,WAAW,CAACnE,KAAK,EAAE;cACpC,MAAMoE,YAAY,GAAGD,WAAW,CAACnE,KAAK;cAEtC;cACA,IAAI,CAAC5D,iBAAiB,CAACoD,GAAG,CAAC,YAAY,CAAC,EAAE8B,QAAQ,CAAC8C,YAAY,CAAC;cAEhE;cACA,IAAI,CAACvI,kBAAkB,GAAGuI,YAAY;cAEtC;cACA,IAAI,CAACxD,kBAAkB,CAAC;gBACtBG,eAAe,EAAE,CAAC;kBAAEf,KAAK,EAAEoE;gBAAY,CAAE;eAC1C,CAAC;YACJ;UACF;QACF,CAAC;QACD1D,KAAK,EAAEA,CAAA,KAAK,CAAG;OAChB,CAAC;IACJ;IAEA;IACAwD,oBAAoBA,CAACG,QAAgB;MACnC,MAAMC,MAAM,GAAGhE,IAAI,CAACC,KAAK,CAAC8D,QAAQ,CAAC;MACnC,OAAO7D,MAAM,CAACC,IAAI,CAAC6D,MAAM,CAAC,CAAC9Q,GAAG,CAAEoK,KAAK,KAAM;QACzCM,IAAI,EAAEN,KAAK;QACXoC,KAAK,EAAE,IAAI,CAACuE,MAAM,CAAC3G,KAAK;OACzB,CAAC,CAAC;IACL;IAEA;IACA2G,MAAMA,CAAC3G,KAAa;MAClB,OAAOA,KAAK,CAAC4G,WAAW,EAAE,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;IACjD;IAEA;IACAxP,eAAeA,CAACoJ,SAAiB;MAC/B,IAAI,CAACnJ,iBAAiB,GAAGmJ,SAAS;MAClC,IAAI,CAACjC,iBAAiB,CAACoD,GAAG,CAAC,WAAW,CAAC,EAAE8B,QAAQ,CAACjD,SAAS,CAAC;IAC9D;IAEAlI,uBAAuBA,CAAC0K,KAAU;MAChC,IAAI,CAACnK,eAAe,GAAGmK,KAAK;MAC5B,IAAI,CAACzE,iBAAiB,CACnBoD,GAAG,CAAC,iBAAiB,CAAC,EACrB8B,QAAQ,CAAC,IAAI,CAAC5K,eAAe,CAAC;IACpC;IAEAJ,sBAAsBA,CAACuK,KAAU;MAC/B,IAAI,CAAClK,cAAc,GAAGkK,KAAK;MAC3B,IAAI,CAACzE,iBAAiB,CAACoD,GAAG,CAAC,gBAAgB,CAAC,EAAE8B,QAAQ,CAAC,IAAI,CAAC3K,cAAc,CAAC;IAC7E;IAEA;IACAjB,eAAeA,CAACmL,KAAU;MACxB,IAAI,CAAC/K,SAAS,GAAG+K,KAAK;MACtB,IAAI,CAACzE,iBAAiB,CACnBoD,GAAG,CAAC,WAAW,CAAC,EACf8B,QAAQ,CAAC,IAAI,CAACxL,SAAS,CAAC;IAC9B;IAGA;IACA4B,gBAAgBA,CAAC+K,IAAY;MAC3B,IAAI,CAACzF,aAAa,GAAG,CAAC,GAAGyF,IAAI,CAAC;MAC9B,IAAI,CAACxK,aAAa,GAAG,IAAI,CAAC+E,aAAa,CAAC0H,IAAI,CAACjC,IAAI,IAAIA,IAAI,CAACkC,IAAI,KAAK,CAAC,CAAC;IACvE;IAEA;IACAC,gBAAgBA,CAAA;MACd,MAAMC,aAAa,GAAG,IAAI,CAACzI,iBAAiB,CAAC0F,OAAO;MACpD,MAAMgD,gBAAgB,GAAG,IAAI,CAACjJ,kBAAkB,KAAK,cAAc;MACnE,MAAMkJ,WAAW,GAAG,IAAI,CAAC/H,aAAa,CAAC+E,MAAM,KAAK,CAAC;MACnD,MAAMiD,WAAW,GAAG,IAAI,CAAC9P,iBAAiB,KAAK,IAAI,CAACjB,QAAQ,CAAC2C,SAAS;MAEtE,MAAMd,SAAS,GAAGmP,MAAM,CAAC,IAAI,CAAC7I,iBAAiB,CAACoD,GAAG,CAAC,WAAW,CAAC,EAAEQ,KAAK,CAAC;MACxE,MAAMtJ,eAAe,GAAGuO,MAAM,CAAC,IAAI,CAAC7I,iBAAiB,CAACoD,GAAG,CAAC,iBAAiB,CAAC,EAAEQ,KAAK,CAAC;MACpF,MAAMrJ,cAAc,GAAGsO,MAAM,CAAC,IAAI,CAAC7I,iBAAiB,CAACoD,GAAG,CAAC,gBAAgB,CAAC,EAAEQ,KAAK,CAAC;MAElF;MACA,MAAMkF,wBAAwB,GAC5B,CAACF,WAAW,IAAIlP,SAAS,GAAG,GAAG;MAEjC;MACA;MACA;MACA,MAAMqP,oBAAoB,GACxBH,WAAW,KACVtO,eAAe,IAAIC,cAAc,IAChCD,eAAe,GAAG,GAAG,IACrBC,cAAc,GAAG,GAAG,CAAC;MAEzB,OACEkO,aAAa,IACZC,gBAAgB,IAAIC,WAAY,IACjCI,oBAAoB,IACpBD,wBAAwB,IACxB,IAAI,CAACjN,aAAa;IAEtB;IAGA;IACAmN,gBAAgBA,CAAA;MACd,IAAI,CAAC5I,aAAa,GAAG,KAAK;MAC1B,IAAI,IAAI,CAACD,iBAAiB,EAAE;QAC1B,IAAI,CAACP,MAAM,CAAC+H,QAAQ,CAAC,CAAC,2BAA2B,CAAC,CAAC;MACrD;IACF;IAEA;IACAsB,kBAAkBA,CAACV,IAAY;MAC7B,IAAIA,IAAI,GAAG,MAAM,EAAE;QACjB,OAAO,IAAI,CAAC1I,YAAY,CAACqJ,SAAS,CAACX,IAAI,GAAG,IAAI,EAAE,OAAO,CAAC,GAAG,KAAK;MAClE,CAAC,MAAM;QACL,OAAO,IAAI,CAAC1I,YAAY,CAACqJ,SAAS,CAACX,IAAI,GAAG,OAAO,EAAE,OAAO,CAAC,GAAG,KAAK;MACrE;IACF;IAEA;IACAY,UAAUA,CAACC,GAAW;MACpB,OAAOA,GAAG,GAAG,CAAC,GAAG,GAAGA,GAAG,OAAO,GAAG,GAAGA,GAAG,MAAM;IAC/C;IAEA;IACAC,gBAAgBA,CAACC,OAAe;MAC9B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,OAAO,CAAC;MAC9B,MAAMG,GAAG,GAAG5F,MAAM,CAAC0F,IAAI,CAACG,OAAO,EAAE,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MACnD,MAAMC,KAAK,GAAG/F,MAAM,CAAC0F,IAAI,CAACM,QAAQ,EAAE,GAAG,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MAC1D,MAAMG,IAAI,GAAGjG,MAAM,CAAC0F,IAAI,CAACQ,WAAW,EAAE,CAAC,CAAC1C,KAAK,CAAC,CAAC,CAAC,CAAC;MACjD,MAAM2C,KAAK,GAAGnG,MAAM,CAAC0F,IAAI,CAACU,QAAQ,EAAE,CAAC,CAACN,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MACtD,MAAMO,OAAO,GAAGrG,MAAM,CAAC0F,IAAI,CAACY,UAAU,EAAE,CAAC,CAACR,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MAC1D,OAAO,GAAGF,GAAG,IAAIG,KAAK,IAAIE,IAAI,KAAKE,KAAK,IAAIE,OAAO,EAAE;IACvD;IAEA;IACAE,iBAAiBA,CAAC3I,IAAY;MAC5B,MAAMrK,GAAG,GAA2B;QAClCiT,MAAM,EAAE,SAAS;QACjBC,oBAAoB,EAAE;OACvB;MACD,IAAI,CAACxR,iBAAiB,GAAG1B,GAAG,CAACqK,IAAI,CAAC;MAClC,OAAOrK,GAAG,CAACqK,IAAI,CAAC,IAAI,SAAS;IAC/B;IAEQ4B,gBAAgBA,CAAA;MACtB,MAAMkH,IAAI,GAAG1B,MAAM,CAAC,IAAI,CAAC5I,eAAe,CAAC;MAEzC;MACA,IAAI,CAACH,oBAAoB,CAAC0K,oBAAoB,CAACD,IAAI,CAAC,CAAC/G,SAAS,CAAC;QAC7DkD,IAAI,EAAG1C,QAAQ,IAAI;UACjB,IAAIA,QAAQ,EAAEyG,KAAK,EAAE9E,MAAM,GAAG,CAAC,EAAE;YAC/B,MAAMG,aAAa,GAAG9B,QAAQ,CAAC8B,aAAa,IAAI,QAAQ;YACxD,IAAI,CAAC5K,SAAS,GAAG8I,QAAQ,CAACyG,KAAK,CAACrT,GAAG,CAAEiP,IAAS,KAAM;cAClDqE,QAAQ,EAAErE,IAAI,CAACqE,QAAQ;cACvBC,iBAAiB,EAAEtE,IAAI,CAACuE,MAAM,KAAK,UAAU,GACzC,IAAI,CAACzB,UAAU,CAAC9C,IAAI,CAACwE,aAAa,CAAC,GACnC,IAAI,CAAC5B,kBAAkB,CAAC5C,IAAI,CAACwE,aAAa,CAAC;cAC/CC,mBAAmB,EAAE,IAAI,CAACzB,gBAAgB,CAAChD,IAAI,CAAC0E,UAAU,CAAC;cAC3DjF,aAAa,EAAE,IAAI,CAACsE,iBAAiB,CAACtE,aAAa;aACpD,CAAC,CAAC;YAEH;YACA,CAAC,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,gBAAgB,CAAC,CAAChB,OAAO,CAACkC,KAAK,IAClE,IAAI,CAAChH,iBAAiB,CAACoD,GAAG,CAAC4D,KAAK,CAAC,EAAEgE,OAAO,EAAE,CAC7C;UACH,CAAC,MAAM;YACL,IAAI,CAAC9P,SAAS,GAAG,EAAE;UACrB;QACF,CAAC;QACDoJ,KAAK,EAAGuC,GAAG,IAAI;UACbtC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEuC,GAAG,CAAC;UACxD,IAAI,CAAC3L,SAAS,GAAG,EAAE;QACrB;OACD,CAAC;MAEF;MACA,IAAI,CAAC4E,oBAAoB,CAACmL,iBAAiB,EAAE,CAC1CC,IAAI,CACH9T,GAAG,CAAE4M,QAAe,IAAI;QACtB,MAAMmH,KAAK,GAAGnH,QAAQ,CAACkC,IAAI,CAACkF,IAAI,IAAIA,IAAI,CAACtH,EAAE,KAAKyG,IAAI,CAAC;QACrD,IAAIY,KAAK,EAAE;UACT,IAAI,CAACnL,iBAAiB,CAACqL,UAAU,CAAC;YAChCvJ,IAAI,EAAEqJ,KAAK,CAACG,cAAc;YAC1BnT,WAAW,EAAEgT,KAAK,CAAChT;WACpB,CAAC;QACJ;QACA,OAAO6L,QAAQ;MACjB,CAAC,CAAC,CACH,CACAR,SAAS,CAAC;QACTkD,IAAI,EAAEA,CAAA,KAAK;UACT;QAAA,CACD;QACDpC,KAAK,EAAEA,CAAA,KAAK,CACZ;OACD,CAAC;IACN;IAEA9K,mBAAmBA,CAACiL,KAAU,EAAE8G,WAAmB;MACjD,IAAIC,UAAkB;MAEtB;MACA,IAAI/G,KAAK,EAAEgH,MAAM,EAAE;QACjBD,UAAU,GAAI/G,KAAK,CAACgH,MAA2B,CAACC,aAAa;MAC/D,CAAC,MAAM,IAAI,OAAOjH,KAAK,KAAK,QAAQ,EAAE;QACpC+G,UAAU,GAAG/G,KAAK;MACpB,CAAC,MAAM;QACL+G,UAAU,GAAG3C,MAAM,CAACpE,KAAK,CAAC;MAC5B;MAEA;MACA,MAAMkH,WAAW,GAAGC,IAAI,CAACC,GAAG,CAACL,UAAU,EAAE,KAAK,CAAC;MAE/C;MACA,IAAID,WAAW,KAAK,iBAAiB,EAAE;QACrC,IAAI,CAACjR,eAAe,GAAGqR,WAAW;MACpC,CAAC,MAAM,IAAIJ,WAAW,KAAK,gBAAgB,EAAE;QAC3C,IAAI,CAAChR,cAAc,GAAGoR,WAAW;MACnC,CAAC,MAAM,IAAIJ,WAAW,KAAK,WAAW,EAAE;QACtC,IAAI,CAAC7R,SAAS,GAAGiS,WAAW;MAC9B;MACA;MACA,IAAI,CAAC3L,iBAAiB,CAACoD,GAAG,CAACmI,WAAW,CAAC,EAAErG,QAAQ,CAACyG,WAAW,CAAC;IAChE;;uCA5mBWjM,4BAA4B,EAAArI,EAAA,CAAAyU,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA3U,EAAA,CAAAyU,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAA7U,EAAA,CAAAyU,iBAAA,CAAAK,EAAA,CAAA5V,WAAA,GAAAc,EAAA,CAAAyU,iBAAA,CAAAM,EAAA,CAAAC,oBAAA,GAAAhV,EAAA,CAAAyU,iBAAA,CAAAG,EAAA,CAAAK,cAAA;IAAA;;YAA5B5M,4BAA4B;MAAA6M,SAAA;MAAAC,QAAA,GAAAnV,EAAA,CAAAoV,kBAAA,CAL5B,CAAClW,WAAW,CAAC;MAAAmW,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC5C1B1V,EAAA,CAAAC,cAAA,WAAsB;UAAAD,EAAA,CAAAE,MAAA,GAAwB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAM1CH,EALR,CAAAC,cAAA,aAA6C,cACL,aACX,aAEsC,aAClC;UACvBD,EAAA,CAAA+C,UAAA,IAAA6S,4CAAA,kBAAkD;UAClD5V,EAAA,CAAAC,cAAA,qBAAiF;UAA5BD,EAAA,CAAAgB,UAAA,mBAAA6U,mEAAA;YAAA,OAASF,GAAA,CAAArL,eAAA,EAAiB;UAAA,EAAC;UAClFtK,EADmF,CAAAG,YAAA,EAAc,EAC3F;UAGNH,EAAA,CAAA+C,UAAA,IAAA+S,2CAAA,kBAAmD;UASrD9V,EAAA,CAAAG,YAAA,EAAM;UAMEH,EAJR,CAAAC,cAAA,cAA0B,eACE,eACH,eACc,eACG;UAChCD,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAKJH,EAHF,CAAAC,cAAA,eAAkC,sBAQmB;UALTD,EAAA,CAAAgB,UAAA,uBAAA+U,uEAAA;YAAA,OAAAJ,GAAA,CAAAjU,UAAA,GAA0BiU,GAAA,CAAAK,aAAA,EAAe,GAAGL,GAAA,CAAAM,WAAA,EAAa;UAAA,EAAC;UAS1GjW,EAHM,CAAAG,YAAA,EAAa,EACT,EACF,EACF;UAgFNH,EA7EA,CAAA+C,UAAA,KAAAmT,qDAAA,4BAAkC,KAAAC,4CAAA,kBAyEV,KAAAC,2CAAA,iBAIA;UAEtBpW,EADF,CAAAC,cAAA,eAAqB,wBAI8B;UAD/CD,EAAA,CAAAgB,UAAA,yBAAAqV,2EAAAtU,MAAA;YAAA,OAAe4T,GAAA,CAAAxI,kBAAA,CAAApL,MAAA,CAA0B;UAAA,EAAC;UAG9C/B,EADE,CAAAG,YAAA,EAAe,EACX;UAkBNH,EAjBA,CAAA+C,UAAA,KAAAuT,qDAAA,2BAA4D,KAAAC,4CAAA,kBAasB,KAAAC,4CAAA,kBAIvB;UA4GrExW,EAJQ,CAAAG,YAAA,EAAM,EACF,EACF,EACD,EACH;;;UAhQgBH,EAAA,CAAAK,SAAA,EAAwB;UAAxBL,EAAA,CAAAoC,iBAAA,CAAAuT,GAAA,CAAAnV,QAAA,CAAAiW,SAAA,CAAwB;UAEtCzW,EAAA,CAAAK,SAAA,GAA+B;UAA/BL,EAAA,CAAAW,UAAA,cAAAgV,GAAA,CAAAhN,iBAAA,CAA+B;UAGR3I,EAAA,CAAAK,SAAA,GAAmC;UAAnCL,EAAA,CAAA0W,WAAA,cAAAf,GAAA,CAAAtL,eAAA,CAAmC;UAE9BrK,EAAA,CAAAK,SAAA,GAAsB;UAAtBL,EAAA,CAAAW,UAAA,UAAAgV,GAAA,CAAAtL,eAAA,CAAsB;UAKvBrK,EAAA,CAAAK,SAAA,GAAsB;UAAtBL,EAAA,CAAAW,UAAA,UAAAgV,GAAA,CAAAtL,eAAA,CAAsB;UAgBzCrK,EAAA,CAAAK,SAAA,GACF;UADEL,EAAA,CAAAsC,kBAAA,MAAAqT,GAAA,CAAAnV,QAAA,CAAAmW,0BAAA,MACF;UAKc3W,EAAA,CAAAK,SAAA,GAA6B;UAA7BL,EAAA,CAAAM,qBAAA,UAAAqV,GAAA,CAAAlM,iBAAA,CAA6B;UAKtBzJ,EAJjB,CAAAW,UAAA,iBAAAX,EAAA,CAAA2B,eAAA,KAAAiV,GAAA,EAIA,aAAAjB,GAAA,CAAAxE,gBAAA,GAAgD;UAOzCnR,EAAA,CAAAK,SAAA,EAAiB;UAAjBL,EAAA,CAAAW,UAAA,UAAAgV,GAAA,CAAAjU,UAAA,CAAiB;UAyE1B1B,EAAA,CAAAK,SAAA,EAAgB;UAAhBL,EAAA,CAAAW,UAAA,SAAAgV,GAAA,CAAAjU,UAAA,CAAgB;UAIjB1B,EAAA,CAAAK,SAAA,EAAiB;UAAjBL,EAAA,CAAAW,UAAA,UAAAgV,GAAA,CAAAjU,UAAA,CAAiB;UAEkB1B,EAAA,CAAAK,SAAA,GAA+B;UAA/BL,EAAA,CAAAM,qBAAA,UAAAqV,GAAA,CAAAnV,QAAA,CAAAqK,UAAA,CAA+B;UAGtC7K,EAFA,CAAAW,UAAA,YAAAgV,GAAA,CAAAnM,aAAA,CAAyB,kBAAAmM,GAAA,CAAAxM,iBAAA,CAAoC,UAAAwM,GAAA,CAAA9U,aAAA,eACV,mBACpD,kBAAkB;UAGnCb,EAAA,CAAAK,SAAA,EAA2C;UAA3CL,EAAA,CAAAW,UAAA,SAAAgV,GAAA,CAAAvN,kBAAA,oBAA2C;UAapDpI,EAAA,CAAAK,SAAA,EAAsD;UAAtDL,EAAA,CAAAW,UAAA,SAAAgV,GAAA,CAAA3M,kBAAA,IAAA2M,GAAA,CAAApM,aAAA,CAAA+E,MAAA,OAAsD;UAItDtO,EAAA,CAAAK,SAAA,EAAwB;UAAxBL,EAAA,CAAAW,UAAA,SAAAgV,GAAA,CAAAvN,kBAAA,CAAwB;;;qBDrHpCnJ,YAAY,EAAA6V,EAAA,CAAA+B,OAAA,EAAA/B,EAAA,CAAAgC,IAAA,EACZ3X,mBAAmB,EAAAuV,EAAA,CAAAqC,aAAA,EAAArC,EAAA,CAAAsC,eAAA,EAAAtC,EAAA,CAAAuC,oBAAA,EAAAvC,EAAA,CAAAwC,iBAAA,EAAAxC,EAAA,CAAAyC,oBAAA,EAAAzC,EAAA,CAAA0C,kBAAA,EAAA1C,EAAA,CAAA2C,eAAA,EACnBjY,WAAW,EACXU,mBAAmB,EAAAwX,EAAA,CAAAC,sBAAA,EACnB/X,iBAAiB,EACjBC,mBAAmB,EACnBC,oBAAoB,EACpBH,eAAe,EACfI,mBAAmB,EAEnBC,eAAe,EACfC,cAAc;MAAA2X,MAAA;IAAA;;SAOLnP,4BAA4B;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}