{"ast": null, "code": "import _asyncToGenerator from \"C:/console/aava-ui-web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = this && this.__param || function (paramIndex, decorator) {\n  return function (target, key) {\n    decorator(target, key, paramIndex);\n  };\n};\nimport { CancellationTokenSource } from '../../../../../base/common/cancellation.js';\nimport { equalsIfDefined, itemEquals } from '../../../../../base/common/equals.js';\nimport { matchesSubString } from '../../../../../base/common/filters.js';\nimport { Disposable, MutableDisposable } from '../../../../../base/common/lifecycle.js';\nimport { derivedOpts, disposableObservableValue, transaction } from '../../../../../base/common/observable.js';\nimport { Range } from '../../../../common/core/range.js';\nimport { SingleTextEdit } from '../../../../common/core/textEdit.js';\nimport { TextLength } from '../../../../common/core/textLength.js';\nimport { InlineCompletionTriggerKind } from '../../../../common/languages.js';\nimport { ILanguageConfigurationService } from '../../../../common/languages/languageConfigurationRegistry.js';\nimport { ILanguageFeaturesService } from '../../../../common/services/languageFeatures.js';\nimport { provideInlineCompletions } from './provideInlineCompletions.js';\nimport { singleTextRemoveCommonPrefix } from './singleTextEdit.js';\nlet InlineCompletionsSource = class InlineCompletionsSource extends Disposable {\n  constructor(textModel, versionId, _debounceValue, languageFeaturesService, languageConfigurationService) {\n    super();\n    this.textModel = textModel;\n    this.versionId = versionId;\n    this._debounceValue = _debounceValue;\n    this.languageFeaturesService = languageFeaturesService;\n    this.languageConfigurationService = languageConfigurationService;\n    this._updateOperation = this._register(new MutableDisposable());\n    this.inlineCompletions = disposableObservableValue('inlineCompletions', undefined);\n    this.suggestWidgetInlineCompletions = disposableObservableValue('suggestWidgetInlineCompletions', undefined);\n    this._register(this.textModel.onDidChangeContent(() => {\n      this._updateOperation.clear();\n    }));\n  }\n  fetch(position, context, activeInlineCompletion) {\n    var _this = this;\n    const request = new UpdateRequest(position, context, this.textModel.getVersionId());\n    const target = context.selectedSuggestionInfo ? this.suggestWidgetInlineCompletions : this.inlineCompletions;\n    if (this._updateOperation.value?.request.satisfies(request)) {\n      return this._updateOperation.value.promise;\n    } else if (target.get()?.request.satisfies(request)) {\n      return Promise.resolve(true);\n    }\n    const updateOngoing = !!this._updateOperation.value;\n    this._updateOperation.clear();\n    const source = new CancellationTokenSource();\n    const promise = _asyncToGenerator(function* () {\n      const shouldDebounce = updateOngoing || context.triggerKind === InlineCompletionTriggerKind.Automatic;\n      if (shouldDebounce) {\n        // This debounces the operation\n        yield wait(_this._debounceValue.get(_this.textModel), source.token);\n      }\n      if (source.token.isCancellationRequested || _this._store.isDisposed || _this.textModel.getVersionId() !== request.versionId) {\n        return false;\n      }\n      const startTime = new Date();\n      const updatedCompletions = yield provideInlineCompletions(_this.languageFeaturesService.inlineCompletionsProvider, position, _this.textModel, context, source.token, _this.languageConfigurationService);\n      if (source.token.isCancellationRequested || _this._store.isDisposed || _this.textModel.getVersionId() !== request.versionId) {\n        return false;\n      }\n      const endTime = new Date();\n      _this._debounceValue.update(_this.textModel, endTime.getTime() - startTime.getTime());\n      const completions = new UpToDateInlineCompletions(updatedCompletions, request, _this.textModel, _this.versionId);\n      if (activeInlineCompletion) {\n        const asInlineCompletion = activeInlineCompletion.toInlineCompletion(undefined);\n        if (activeInlineCompletion.canBeReused(_this.textModel, position) && !updatedCompletions.has(asInlineCompletion)) {\n          completions.prepend(activeInlineCompletion.inlineCompletion, asInlineCompletion.range, true);\n        }\n      }\n      _this._updateOperation.clear();\n      transaction(tx => {\n        /** @description Update completions with provider result */\n        target.set(completions, tx);\n      });\n      return true;\n    })();\n    const updateOperation = new UpdateOperation(request, source, promise);\n    this._updateOperation.value = updateOperation;\n    return promise;\n  }\n  clear(tx) {\n    this._updateOperation.clear();\n    this.inlineCompletions.set(undefined, tx);\n    this.suggestWidgetInlineCompletions.set(undefined, tx);\n  }\n  clearSuggestWidgetInlineCompletions(tx) {\n    if (this._updateOperation.value?.request.context.selectedSuggestionInfo) {\n      this._updateOperation.clear();\n    }\n    this.suggestWidgetInlineCompletions.set(undefined, tx);\n  }\n  cancelUpdate() {\n    this._updateOperation.clear();\n  }\n};\nInlineCompletionsSource = __decorate([__param(3, ILanguageFeaturesService), __param(4, ILanguageConfigurationService)], InlineCompletionsSource);\nexport { InlineCompletionsSource };\nfunction wait(ms, cancellationToken) {\n  return new Promise(resolve => {\n    let d = undefined;\n    const handle = setTimeout(() => {\n      if (d) {\n        d.dispose();\n      }\n      resolve();\n    }, ms);\n    if (cancellationToken) {\n      d = cancellationToken.onCancellationRequested(() => {\n        clearTimeout(handle);\n        if (d) {\n          d.dispose();\n        }\n        resolve();\n      });\n    }\n  });\n}\nclass UpdateRequest {\n  constructor(position, context, versionId) {\n    this.position = position;\n    this.context = context;\n    this.versionId = versionId;\n  }\n  satisfies(other) {\n    return this.position.equals(other.position) && equalsIfDefined(this.context.selectedSuggestionInfo, other.context.selectedSuggestionInfo, itemEquals()) && (other.context.triggerKind === InlineCompletionTriggerKind.Automatic || this.context.triggerKind === InlineCompletionTriggerKind.Explicit) && this.versionId === other.versionId;\n  }\n}\nclass UpdateOperation {\n  constructor(request, cancellationTokenSource, promise) {\n    this.request = request;\n    this.cancellationTokenSource = cancellationTokenSource;\n    this.promise = promise;\n  }\n  dispose() {\n    this.cancellationTokenSource.cancel();\n  }\n}\nexport class UpToDateInlineCompletions {\n  get inlineCompletions() {\n    return this._inlineCompletions;\n  }\n  constructor(inlineCompletionProviderResult, request, _textModel, _versionId) {\n    this.inlineCompletionProviderResult = inlineCompletionProviderResult;\n    this.request = request;\n    this._textModel = _textModel;\n    this._versionId = _versionId;\n    this._refCount = 1;\n    this._prependedInlineCompletionItems = [];\n    const ids = _textModel.deltaDecorations([], inlineCompletionProviderResult.completions.map(i => ({\n      range: i.range,\n      options: {\n        description: 'inline-completion-tracking-range'\n      }\n    })));\n    this._inlineCompletions = inlineCompletionProviderResult.completions.map((i, index) => new InlineCompletionWithUpdatedRange(i, ids[index], this._textModel, this._versionId));\n  }\n  clone() {\n    this._refCount++;\n    return this;\n  }\n  dispose() {\n    this._refCount--;\n    if (this._refCount === 0) {\n      setTimeout(() => {\n        // To fix https://github.com/microsoft/vscode/issues/188348\n        if (!this._textModel.isDisposed()) {\n          // This is just cleanup. It's ok if it happens with a delay.\n          this._textModel.deltaDecorations(this._inlineCompletions.map(i => i.decorationId), []);\n        }\n      }, 0);\n      this.inlineCompletionProviderResult.dispose();\n      for (const i of this._prependedInlineCompletionItems) {\n        i.source.removeRef();\n      }\n    }\n  }\n  prepend(inlineCompletion, range, addRefToSource) {\n    if (addRefToSource) {\n      inlineCompletion.source.addRef();\n    }\n    const id = this._textModel.deltaDecorations([], [{\n      range,\n      options: {\n        description: 'inline-completion-tracking-range'\n      }\n    }])[0];\n    this._inlineCompletions.unshift(new InlineCompletionWithUpdatedRange(inlineCompletion, id, this._textModel, this._versionId));\n    this._prependedInlineCompletionItems.push(inlineCompletion);\n  }\n}\nexport class InlineCompletionWithUpdatedRange {\n  get forwardStable() {\n    return this.inlineCompletion.source.inlineCompletions.enableForwardStability ?? false;\n  }\n  constructor(inlineCompletion, decorationId, _textModel, _modelVersion) {\n    this.inlineCompletion = inlineCompletion;\n    this.decorationId = decorationId;\n    this._textModel = _textModel;\n    this._modelVersion = _modelVersion;\n    this.semanticId = JSON.stringify([this.inlineCompletion.filterText, this.inlineCompletion.insertText, this.inlineCompletion.range.getStartPosition().toString()]);\n    this._updatedRange = derivedOpts({\n      owner: this,\n      equalsFn: Range.equalsRange\n    }, reader => {\n      this._modelVersion.read(reader);\n      return this._textModel.getDecorationRange(this.decorationId);\n    });\n  }\n  toInlineCompletion(reader) {\n    return this.inlineCompletion.withRange(this._updatedRange.read(reader) ?? emptyRange);\n  }\n  toSingleTextEdit(reader) {\n    return new SingleTextEdit(this._updatedRange.read(reader) ?? emptyRange, this.inlineCompletion.insertText);\n  }\n  isVisible(model, cursorPosition, reader) {\n    const minimizedReplacement = singleTextRemoveCommonPrefix(this._toFilterTextReplacement(reader), model);\n    const updatedRange = this._updatedRange.read(reader);\n    if (!updatedRange || !this.inlineCompletion.range.getStartPosition().equals(updatedRange.getStartPosition()) || cursorPosition.lineNumber !== minimizedReplacement.range.startLineNumber) {\n      return false;\n    }\n    // We might consider comparing by .toLowerText, but this requires GhostTextReplacement\n    const originalValue = model.getValueInRange(minimizedReplacement.range, 1 /* EndOfLinePreference.LF */);\n    const filterText = minimizedReplacement.text;\n    const cursorPosIndex = Math.max(0, cursorPosition.column - minimizedReplacement.range.startColumn);\n    let filterTextBefore = filterText.substring(0, cursorPosIndex);\n    let filterTextAfter = filterText.substring(cursorPosIndex);\n    let originalValueBefore = originalValue.substring(0, cursorPosIndex);\n    let originalValueAfter = originalValue.substring(cursorPosIndex);\n    const originalValueIndent = model.getLineIndentColumn(minimizedReplacement.range.startLineNumber);\n    if (minimizedReplacement.range.startColumn <= originalValueIndent) {\n      // Remove indentation\n      originalValueBefore = originalValueBefore.trimStart();\n      if (originalValueBefore.length === 0) {\n        originalValueAfter = originalValueAfter.trimStart();\n      }\n      filterTextBefore = filterTextBefore.trimStart();\n      if (filterTextBefore.length === 0) {\n        filterTextAfter = filterTextAfter.trimStart();\n      }\n    }\n    return filterTextBefore.startsWith(originalValueBefore) && !!matchesSubString(originalValueAfter, filterTextAfter);\n  }\n  canBeReused(model, position) {\n    const updatedRange = this._updatedRange.read(undefined);\n    const result = !!updatedRange && updatedRange.containsPosition(position) && this.isVisible(model, position, undefined) && TextLength.ofRange(updatedRange).isGreaterThanOrEqualTo(TextLength.ofRange(this.inlineCompletion.range));\n    return result;\n  }\n  _toFilterTextReplacement(reader) {\n    return new SingleTextEdit(this._updatedRange.read(reader) ?? emptyRange, this.inlineCompletion.filterText);\n  }\n}\nconst emptyRange = new Range(1, 1, 1, 1);", "map": {"version": 3, "names": ["__decorate", "decorators", "target", "key", "desc", "c", "arguments", "length", "r", "Object", "getOwnPropertyDescriptor", "d", "Reflect", "decorate", "i", "defineProperty", "__param", "paramIndex", "decorator", "CancellationTokenSource", "equalsIfDefined", "itemEquals", "matchesSubString", "Disposable", "MutableDisposable", "derivedOpts", "disposableObservableValue", "transaction", "Range", "SingleTextEdit", "TextLength", "InlineCompletionTriggerKind", "ILanguageConfigurationService", "ILanguageFeaturesService", "provideInlineCompletions", "singleTextRemoveCommonPrefix", "InlineCompletionsSource", "constructor", "textModel", "versionId", "_debounceValue", "languageFeaturesService", "languageConfigurationService", "_updateOperation", "_register", "inlineCompletions", "undefined", "suggestWidgetInlineCompletions", "onDidChangeContent", "clear", "fetch", "position", "context", "activeInlineCompletion", "_this", "request", "UpdateRequest", "getVersionId", "selectedSuggestionInfo", "value", "satisfies", "promise", "get", "Promise", "resolve", "updateOngoing", "source", "_asyncToGenerator", "shouldDebounce", "trigger<PERSON>ind", "Automatic", "wait", "token", "isCancellationRequested", "_store", "isDisposed", "startTime", "Date", "updatedCompletions", "inlineCompletionsProvider", "endTime", "update", "getTime", "completions", "UpToDateInlineCompletions", "asInlineCompletion", "toInlineCompletion", "canBeReused", "has", "prepend", "inlineCompletion", "range", "tx", "set", "updateOperation", "UpdateOperation", "clearSuggestWidgetInlineCompletions", "cancelUpdate", "ms", "cancellationToken", "handle", "setTimeout", "dispose", "onCancellationRequested", "clearTimeout", "other", "equals", "Explicit", "cancellationTokenSource", "cancel", "_inlineCompletions", "inlineCompletionProviderResult", "_textModel", "_versionId", "_refCount", "_prependedInlineCompletionItems", "ids", "deltaDecorations", "map", "options", "description", "index", "InlineCompletionWithUpdatedRange", "clone", "decorationId", "removeRef", "addRefToSource", "addRef", "id", "unshift", "push", "forwardStable", "enableForwardStability", "_modelVersion", "semanticId", "JSON", "stringify", "filterText", "insertText", "getStartPosition", "toString", "_updated<PERSON><PERSON>e", "owner", "equalsFn", "equalsRange", "reader", "read", "getDecorationRange", "<PERSON><PERSON><PERSON><PERSON>", "emptyRange", "toSingleTextEdit", "isVisible", "model", "cursorPosition", "minimizedReplacement", "_toFilterTextReplacement", "updatedRange", "lineNumber", "startLineNumber", "originalValue", "getValueInRange", "text", "cursorPosIndex", "Math", "max", "column", "startColumn", "filterTextBefore", "substring", "filterTextAfter", "originalValueBefore", "originalValueAfter", "originalValueIndent", "getLineIndentColumn", "trimStart", "startsWith", "result", "containsPosition", "ofRange", "isGreaterThanOrEqualTo"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/contrib/inlineCompletions/browser/model/inlineCompletionsSource.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = (this && this.__param) || function (paramIndex, decorator) {\n    return function (target, key) { decorator(target, key, paramIndex); }\n};\nimport { CancellationTokenSource } from '../../../../../base/common/cancellation.js';\nimport { equalsIfDefined, itemEquals } from '../../../../../base/common/equals.js';\nimport { matchesSubString } from '../../../../../base/common/filters.js';\nimport { Disposable, MutableDisposable } from '../../../../../base/common/lifecycle.js';\nimport { derivedOpts, disposableObservableValue, transaction } from '../../../../../base/common/observable.js';\nimport { Range } from '../../../../common/core/range.js';\nimport { SingleTextEdit } from '../../../../common/core/textEdit.js';\nimport { TextLength } from '../../../../common/core/textLength.js';\nimport { InlineCompletionTriggerKind } from '../../../../common/languages.js';\nimport { ILanguageConfigurationService } from '../../../../common/languages/languageConfigurationRegistry.js';\nimport { ILanguageFeaturesService } from '../../../../common/services/languageFeatures.js';\nimport { provideInlineCompletions } from './provideInlineCompletions.js';\nimport { singleTextRemoveCommonPrefix } from './singleTextEdit.js';\nlet InlineCompletionsSource = class InlineCompletionsSource extends Disposable {\n    constructor(textModel, versionId, _debounceValue, languageFeaturesService, languageConfigurationService) {\n        super();\n        this.textModel = textModel;\n        this.versionId = versionId;\n        this._debounceValue = _debounceValue;\n        this.languageFeaturesService = languageFeaturesService;\n        this.languageConfigurationService = languageConfigurationService;\n        this._updateOperation = this._register(new MutableDisposable());\n        this.inlineCompletions = disposableObservableValue('inlineCompletions', undefined);\n        this.suggestWidgetInlineCompletions = disposableObservableValue('suggestWidgetInlineCompletions', undefined);\n        this._register(this.textModel.onDidChangeContent(() => {\n            this._updateOperation.clear();\n        }));\n    }\n    fetch(position, context, activeInlineCompletion) {\n        const request = new UpdateRequest(position, context, this.textModel.getVersionId());\n        const target = context.selectedSuggestionInfo ? this.suggestWidgetInlineCompletions : this.inlineCompletions;\n        if (this._updateOperation.value?.request.satisfies(request)) {\n            return this._updateOperation.value.promise;\n        }\n        else if (target.get()?.request.satisfies(request)) {\n            return Promise.resolve(true);\n        }\n        const updateOngoing = !!this._updateOperation.value;\n        this._updateOperation.clear();\n        const source = new CancellationTokenSource();\n        const promise = (async () => {\n            const shouldDebounce = updateOngoing || context.triggerKind === InlineCompletionTriggerKind.Automatic;\n            if (shouldDebounce) {\n                // This debounces the operation\n                await wait(this._debounceValue.get(this.textModel), source.token);\n            }\n            if (source.token.isCancellationRequested || this._store.isDisposed || this.textModel.getVersionId() !== request.versionId) {\n                return false;\n            }\n            const startTime = new Date();\n            const updatedCompletions = await provideInlineCompletions(this.languageFeaturesService.inlineCompletionsProvider, position, this.textModel, context, source.token, this.languageConfigurationService);\n            if (source.token.isCancellationRequested || this._store.isDisposed || this.textModel.getVersionId() !== request.versionId) {\n                return false;\n            }\n            const endTime = new Date();\n            this._debounceValue.update(this.textModel, endTime.getTime() - startTime.getTime());\n            const completions = new UpToDateInlineCompletions(updatedCompletions, request, this.textModel, this.versionId);\n            if (activeInlineCompletion) {\n                const asInlineCompletion = activeInlineCompletion.toInlineCompletion(undefined);\n                if (activeInlineCompletion.canBeReused(this.textModel, position) && !updatedCompletions.has(asInlineCompletion)) {\n                    completions.prepend(activeInlineCompletion.inlineCompletion, asInlineCompletion.range, true);\n                }\n            }\n            this._updateOperation.clear();\n            transaction(tx => {\n                /** @description Update completions with provider result */\n                target.set(completions, tx);\n            });\n            return true;\n        })();\n        const updateOperation = new UpdateOperation(request, source, promise);\n        this._updateOperation.value = updateOperation;\n        return promise;\n    }\n    clear(tx) {\n        this._updateOperation.clear();\n        this.inlineCompletions.set(undefined, tx);\n        this.suggestWidgetInlineCompletions.set(undefined, tx);\n    }\n    clearSuggestWidgetInlineCompletions(tx) {\n        if (this._updateOperation.value?.request.context.selectedSuggestionInfo) {\n            this._updateOperation.clear();\n        }\n        this.suggestWidgetInlineCompletions.set(undefined, tx);\n    }\n    cancelUpdate() {\n        this._updateOperation.clear();\n    }\n};\nInlineCompletionsSource = __decorate([\n    __param(3, ILanguageFeaturesService),\n    __param(4, ILanguageConfigurationService)\n], InlineCompletionsSource);\nexport { InlineCompletionsSource };\nfunction wait(ms, cancellationToken) {\n    return new Promise(resolve => {\n        let d = undefined;\n        const handle = setTimeout(() => {\n            if (d) {\n                d.dispose();\n            }\n            resolve();\n        }, ms);\n        if (cancellationToken) {\n            d = cancellationToken.onCancellationRequested(() => {\n                clearTimeout(handle);\n                if (d) {\n                    d.dispose();\n                }\n                resolve();\n            });\n        }\n    });\n}\nclass UpdateRequest {\n    constructor(position, context, versionId) {\n        this.position = position;\n        this.context = context;\n        this.versionId = versionId;\n    }\n    satisfies(other) {\n        return this.position.equals(other.position)\n            && equalsIfDefined(this.context.selectedSuggestionInfo, other.context.selectedSuggestionInfo, itemEquals())\n            && (other.context.triggerKind === InlineCompletionTriggerKind.Automatic\n                || this.context.triggerKind === InlineCompletionTriggerKind.Explicit)\n            && this.versionId === other.versionId;\n    }\n}\nclass UpdateOperation {\n    constructor(request, cancellationTokenSource, promise) {\n        this.request = request;\n        this.cancellationTokenSource = cancellationTokenSource;\n        this.promise = promise;\n    }\n    dispose() {\n        this.cancellationTokenSource.cancel();\n    }\n}\nexport class UpToDateInlineCompletions {\n    get inlineCompletions() { return this._inlineCompletions; }\n    constructor(inlineCompletionProviderResult, request, _textModel, _versionId) {\n        this.inlineCompletionProviderResult = inlineCompletionProviderResult;\n        this.request = request;\n        this._textModel = _textModel;\n        this._versionId = _versionId;\n        this._refCount = 1;\n        this._prependedInlineCompletionItems = [];\n        const ids = _textModel.deltaDecorations([], inlineCompletionProviderResult.completions.map(i => ({\n            range: i.range,\n            options: {\n                description: 'inline-completion-tracking-range'\n            },\n        })));\n        this._inlineCompletions = inlineCompletionProviderResult.completions.map((i, index) => new InlineCompletionWithUpdatedRange(i, ids[index], this._textModel, this._versionId));\n    }\n    clone() {\n        this._refCount++;\n        return this;\n    }\n    dispose() {\n        this._refCount--;\n        if (this._refCount === 0) {\n            setTimeout(() => {\n                // To fix https://github.com/microsoft/vscode/issues/188348\n                if (!this._textModel.isDisposed()) {\n                    // This is just cleanup. It's ok if it happens with a delay.\n                    this._textModel.deltaDecorations(this._inlineCompletions.map(i => i.decorationId), []);\n                }\n            }, 0);\n            this.inlineCompletionProviderResult.dispose();\n            for (const i of this._prependedInlineCompletionItems) {\n                i.source.removeRef();\n            }\n        }\n    }\n    prepend(inlineCompletion, range, addRefToSource) {\n        if (addRefToSource) {\n            inlineCompletion.source.addRef();\n        }\n        const id = this._textModel.deltaDecorations([], [{\n                range,\n                options: {\n                    description: 'inline-completion-tracking-range'\n                },\n            }])[0];\n        this._inlineCompletions.unshift(new InlineCompletionWithUpdatedRange(inlineCompletion, id, this._textModel, this._versionId));\n        this._prependedInlineCompletionItems.push(inlineCompletion);\n    }\n}\nexport class InlineCompletionWithUpdatedRange {\n    get forwardStable() {\n        return this.inlineCompletion.source.inlineCompletions.enableForwardStability ?? false;\n    }\n    constructor(inlineCompletion, decorationId, _textModel, _modelVersion) {\n        this.inlineCompletion = inlineCompletion;\n        this.decorationId = decorationId;\n        this._textModel = _textModel;\n        this._modelVersion = _modelVersion;\n        this.semanticId = JSON.stringify([\n            this.inlineCompletion.filterText,\n            this.inlineCompletion.insertText,\n            this.inlineCompletion.range.getStartPosition().toString()\n        ]);\n        this._updatedRange = derivedOpts({ owner: this, equalsFn: Range.equalsRange }, reader => {\n            this._modelVersion.read(reader);\n            return this._textModel.getDecorationRange(this.decorationId);\n        });\n    }\n    toInlineCompletion(reader) {\n        return this.inlineCompletion.withRange(this._updatedRange.read(reader) ?? emptyRange);\n    }\n    toSingleTextEdit(reader) {\n        return new SingleTextEdit(this._updatedRange.read(reader) ?? emptyRange, this.inlineCompletion.insertText);\n    }\n    isVisible(model, cursorPosition, reader) {\n        const minimizedReplacement = singleTextRemoveCommonPrefix(this._toFilterTextReplacement(reader), model);\n        const updatedRange = this._updatedRange.read(reader);\n        if (!updatedRange\n            || !this.inlineCompletion.range.getStartPosition().equals(updatedRange.getStartPosition())\n            || cursorPosition.lineNumber !== minimizedReplacement.range.startLineNumber) {\n            return false;\n        }\n        // We might consider comparing by .toLowerText, but this requires GhostTextReplacement\n        const originalValue = model.getValueInRange(minimizedReplacement.range, 1 /* EndOfLinePreference.LF */);\n        const filterText = minimizedReplacement.text;\n        const cursorPosIndex = Math.max(0, cursorPosition.column - minimizedReplacement.range.startColumn);\n        let filterTextBefore = filterText.substring(0, cursorPosIndex);\n        let filterTextAfter = filterText.substring(cursorPosIndex);\n        let originalValueBefore = originalValue.substring(0, cursorPosIndex);\n        let originalValueAfter = originalValue.substring(cursorPosIndex);\n        const originalValueIndent = model.getLineIndentColumn(minimizedReplacement.range.startLineNumber);\n        if (minimizedReplacement.range.startColumn <= originalValueIndent) {\n            // Remove indentation\n            originalValueBefore = originalValueBefore.trimStart();\n            if (originalValueBefore.length === 0) {\n                originalValueAfter = originalValueAfter.trimStart();\n            }\n            filterTextBefore = filterTextBefore.trimStart();\n            if (filterTextBefore.length === 0) {\n                filterTextAfter = filterTextAfter.trimStart();\n            }\n        }\n        return filterTextBefore.startsWith(originalValueBefore)\n            && !!matchesSubString(originalValueAfter, filterTextAfter);\n    }\n    canBeReused(model, position) {\n        const updatedRange = this._updatedRange.read(undefined);\n        const result = !!updatedRange\n            && updatedRange.containsPosition(position)\n            && this.isVisible(model, position, undefined)\n            && TextLength.ofRange(updatedRange).isGreaterThanOrEqualTo(TextLength.ofRange(this.inlineCompletion.range));\n        return result;\n    }\n    _toFilterTextReplacement(reader) {\n        return new SingleTextEdit(this._updatedRange.read(reader) ?? emptyRange, this.inlineCompletion.filterText);\n    }\n}\nconst emptyRange = new Range(1, 1, 1, 1);\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA,IAAIA,UAAU,GAAI,IAAI,IAAI,IAAI,CAACA,UAAU,IAAK,UAAUC,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAE;EACnF,IAAIC,CAAC,GAAGC,SAAS,CAACC,MAAM;IAAEC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGH,MAAM,GAAGE,IAAI,KAAK,IAAI,GAAGA,IAAI,GAAGK,MAAM,CAACC,wBAAwB,CAACR,MAAM,EAAEC,GAAG,CAAC,GAAGC,IAAI;IAAEO,CAAC;EAC5H,IAAI,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,CAACC,QAAQ,KAAK,UAAU,EAAEL,CAAC,GAAGI,OAAO,CAACC,QAAQ,CAACZ,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,CAAC,CAAC,KAC1H,KAAK,IAAIU,CAAC,GAAGb,UAAU,CAACM,MAAM,GAAG,CAAC,EAAEO,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE,IAAIH,CAAC,GAAGV,UAAU,CAACa,CAAC,CAAC,EAAEN,CAAC,GAAG,CAACH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACH,CAAC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACT,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,GAAGG,CAAC,CAACT,MAAM,EAAEC,GAAG,CAAC,KAAKK,CAAC;EACjJ,OAAOH,CAAC,GAAG,CAAC,IAAIG,CAAC,IAAIC,MAAM,CAACM,cAAc,CAACb,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,EAAEA,CAAC;AACjE,CAAC;AACD,IAAIQ,OAAO,GAAI,IAAI,IAAI,IAAI,CAACA,OAAO,IAAK,UAAUC,UAAU,EAAEC,SAAS,EAAE;EACrE,OAAO,UAAUhB,MAAM,EAAEC,GAAG,EAAE;IAAEe,SAAS,CAAChB,MAAM,EAAEC,GAAG,EAAEc,UAAU,CAAC;EAAE,CAAC;AACzE,CAAC;AACD,SAASE,uBAAuB,QAAQ,4CAA4C;AACpF,SAASC,eAAe,EAAEC,UAAU,QAAQ,sCAAsC;AAClF,SAASC,gBAAgB,QAAQ,uCAAuC;AACxE,SAASC,UAAU,EAAEC,iBAAiB,QAAQ,yCAAyC;AACvF,SAASC,WAAW,EAAEC,yBAAyB,EAAEC,WAAW,QAAQ,0CAA0C;AAC9G,SAASC,KAAK,QAAQ,kCAAkC;AACxD,SAASC,cAAc,QAAQ,qCAAqC;AACpE,SAASC,UAAU,QAAQ,uCAAuC;AAClE,SAASC,2BAA2B,QAAQ,iCAAiC;AAC7E,SAASC,6BAA6B,QAAQ,+DAA+D;AAC7G,SAASC,wBAAwB,QAAQ,iDAAiD;AAC1F,SAASC,wBAAwB,QAAQ,+BAA+B;AACxE,SAASC,4BAA4B,QAAQ,qBAAqB;AAClE,IAAIC,uBAAuB,GAAG,MAAMA,uBAAuB,SAASb,UAAU,CAAC;EAC3Ec,WAAWA,CAACC,SAAS,EAAEC,SAAS,EAAEC,cAAc,EAAEC,uBAAuB,EAAEC,4BAA4B,EAAE;IACrG,KAAK,CAAC,CAAC;IACP,IAAI,CAACJ,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,uBAAuB,GAAGA,uBAAuB;IACtD,IAAI,CAACC,4BAA4B,GAAGA,4BAA4B;IAChE,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACC,SAAS,CAAC,IAAIpB,iBAAiB,CAAC,CAAC,CAAC;IAC/D,IAAI,CAACqB,iBAAiB,GAAGnB,yBAAyB,CAAC,mBAAmB,EAAEoB,SAAS,CAAC;IAClF,IAAI,CAACC,8BAA8B,GAAGrB,yBAAyB,CAAC,gCAAgC,EAAEoB,SAAS,CAAC;IAC5G,IAAI,CAACF,SAAS,CAAC,IAAI,CAACN,SAAS,CAACU,kBAAkB,CAAC,MAAM;MACnD,IAAI,CAACL,gBAAgB,CAACM,KAAK,CAAC,CAAC;IACjC,CAAC,CAAC,CAAC;EACP;EACAC,KAAKA,CAACC,QAAQ,EAAEC,OAAO,EAAEC,sBAAsB,EAAE;IAAA,IAAAC,KAAA;IAC7C,MAAMC,OAAO,GAAG,IAAIC,aAAa,CAACL,QAAQ,EAAEC,OAAO,EAAE,IAAI,CAACd,SAAS,CAACmB,YAAY,CAAC,CAAC,CAAC;IACnF,MAAMvD,MAAM,GAAGkD,OAAO,CAACM,sBAAsB,GAAG,IAAI,CAACX,8BAA8B,GAAG,IAAI,CAACF,iBAAiB;IAC5G,IAAI,IAAI,CAACF,gBAAgB,CAACgB,KAAK,EAAEJ,OAAO,CAACK,SAAS,CAACL,OAAO,CAAC,EAAE;MACzD,OAAO,IAAI,CAACZ,gBAAgB,CAACgB,KAAK,CAACE,OAAO;IAC9C,CAAC,MACI,IAAI3D,MAAM,CAAC4D,GAAG,CAAC,CAAC,EAAEP,OAAO,CAACK,SAAS,CAACL,OAAO,CAAC,EAAE;MAC/C,OAAOQ,OAAO,CAACC,OAAO,CAAC,IAAI,CAAC;IAChC;IACA,MAAMC,aAAa,GAAG,CAAC,CAAC,IAAI,CAACtB,gBAAgB,CAACgB,KAAK;IACnD,IAAI,CAAChB,gBAAgB,CAACM,KAAK,CAAC,CAAC;IAC7B,MAAMiB,MAAM,GAAG,IAAI/C,uBAAuB,CAAC,CAAC;IAC5C,MAAM0C,OAAO,GAAGM,iBAAA,CAAC,aAAY;MACzB,MAAMC,cAAc,GAAGH,aAAa,IAAIb,OAAO,CAACiB,WAAW,KAAKtC,2BAA2B,CAACuC,SAAS;MACrG,IAAIF,cAAc,EAAE;QAChB;QACA,MAAMG,IAAI,CAACjB,KAAI,CAACd,cAAc,CAACsB,GAAG,CAACR,KAAI,CAAChB,SAAS,CAAC,EAAE4B,MAAM,CAACM,KAAK,CAAC;MACrE;MACA,IAAIN,MAAM,CAACM,KAAK,CAACC,uBAAuB,IAAInB,KAAI,CAACoB,MAAM,CAACC,UAAU,IAAIrB,KAAI,CAAChB,SAAS,CAACmB,YAAY,CAAC,CAAC,KAAKF,OAAO,CAAChB,SAAS,EAAE;QACvH,OAAO,KAAK;MAChB;MACA,MAAMqC,SAAS,GAAG,IAAIC,IAAI,CAAC,CAAC;MAC5B,MAAMC,kBAAkB,SAAS5C,wBAAwB,CAACoB,KAAI,CAACb,uBAAuB,CAACsC,yBAAyB,EAAE5B,QAAQ,EAAEG,KAAI,CAAChB,SAAS,EAAEc,OAAO,EAAEc,MAAM,CAACM,KAAK,EAAElB,KAAI,CAACZ,4BAA4B,CAAC;MACrM,IAAIwB,MAAM,CAACM,KAAK,CAACC,uBAAuB,IAAInB,KAAI,CAACoB,MAAM,CAACC,UAAU,IAAIrB,KAAI,CAAChB,SAAS,CAACmB,YAAY,CAAC,CAAC,KAAKF,OAAO,CAAChB,SAAS,EAAE;QACvH,OAAO,KAAK;MAChB;MACA,MAAMyC,OAAO,GAAG,IAAIH,IAAI,CAAC,CAAC;MAC1BvB,KAAI,CAACd,cAAc,CAACyC,MAAM,CAAC3B,KAAI,CAAChB,SAAS,EAAE0C,OAAO,CAACE,OAAO,CAAC,CAAC,GAAGN,SAAS,CAACM,OAAO,CAAC,CAAC,CAAC;MACnF,MAAMC,WAAW,GAAG,IAAIC,yBAAyB,CAACN,kBAAkB,EAAEvB,OAAO,EAAED,KAAI,CAAChB,SAAS,EAAEgB,KAAI,CAACf,SAAS,CAAC;MAC9G,IAAIc,sBAAsB,EAAE;QACxB,MAAMgC,kBAAkB,GAAGhC,sBAAsB,CAACiC,kBAAkB,CAACxC,SAAS,CAAC;QAC/E,IAAIO,sBAAsB,CAACkC,WAAW,CAACjC,KAAI,CAAChB,SAAS,EAAEa,QAAQ,CAAC,IAAI,CAAC2B,kBAAkB,CAACU,GAAG,CAACH,kBAAkB,CAAC,EAAE;UAC7GF,WAAW,CAACM,OAAO,CAACpC,sBAAsB,CAACqC,gBAAgB,EAAEL,kBAAkB,CAACM,KAAK,EAAE,IAAI,CAAC;QAChG;MACJ;MACArC,KAAI,CAACX,gBAAgB,CAACM,KAAK,CAAC,CAAC;MAC7BtB,WAAW,CAACiE,EAAE,IAAI;QACd;QACA1F,MAAM,CAAC2F,GAAG,CAACV,WAAW,EAAES,EAAE,CAAC;MAC/B,CAAC,CAAC;MACF,OAAO,IAAI;IACf,CAAC,EAAE,CAAC;IACJ,MAAME,eAAe,GAAG,IAAIC,eAAe,CAACxC,OAAO,EAAEW,MAAM,EAAEL,OAAO,CAAC;IACrE,IAAI,CAAClB,gBAAgB,CAACgB,KAAK,GAAGmC,eAAe;IAC7C,OAAOjC,OAAO;EAClB;EACAZ,KAAKA,CAAC2C,EAAE,EAAE;IACN,IAAI,CAACjD,gBAAgB,CAACM,KAAK,CAAC,CAAC;IAC7B,IAAI,CAACJ,iBAAiB,CAACgD,GAAG,CAAC/C,SAAS,EAAE8C,EAAE,CAAC;IACzC,IAAI,CAAC7C,8BAA8B,CAAC8C,GAAG,CAAC/C,SAAS,EAAE8C,EAAE,CAAC;EAC1D;EACAI,mCAAmCA,CAACJ,EAAE,EAAE;IACpC,IAAI,IAAI,CAACjD,gBAAgB,CAACgB,KAAK,EAAEJ,OAAO,CAACH,OAAO,CAACM,sBAAsB,EAAE;MACrE,IAAI,CAACf,gBAAgB,CAACM,KAAK,CAAC,CAAC;IACjC;IACA,IAAI,CAACF,8BAA8B,CAAC8C,GAAG,CAAC/C,SAAS,EAAE8C,EAAE,CAAC;EAC1D;EACAK,YAAYA,CAAA,EAAG;IACX,IAAI,CAACtD,gBAAgB,CAACM,KAAK,CAAC,CAAC;EACjC;AACJ,CAAC;AACDb,uBAAuB,GAAGpC,UAAU,CAAC,CACjCgB,OAAO,CAAC,CAAC,EAAEiB,wBAAwB,CAAC,EACpCjB,OAAO,CAAC,CAAC,EAAEgB,6BAA6B,CAAC,CAC5C,EAAEI,uBAAuB,CAAC;AAC3B,SAASA,uBAAuB;AAChC,SAASmC,IAAIA,CAAC2B,EAAE,EAAEC,iBAAiB,EAAE;EACjC,OAAO,IAAIpC,OAAO,CAACC,OAAO,IAAI;IAC1B,IAAIrD,CAAC,GAAGmC,SAAS;IACjB,MAAMsD,MAAM,GAAGC,UAAU,CAAC,MAAM;MAC5B,IAAI1F,CAAC,EAAE;QACHA,CAAC,CAAC2F,OAAO,CAAC,CAAC;MACf;MACAtC,OAAO,CAAC,CAAC;IACb,CAAC,EAAEkC,EAAE,CAAC;IACN,IAAIC,iBAAiB,EAAE;MACnBxF,CAAC,GAAGwF,iBAAiB,CAACI,uBAAuB,CAAC,MAAM;QAChDC,YAAY,CAACJ,MAAM,CAAC;QACpB,IAAIzF,CAAC,EAAE;UACHA,CAAC,CAAC2F,OAAO,CAAC,CAAC;QACf;QACAtC,OAAO,CAAC,CAAC;MACb,CAAC,CAAC;IACN;EACJ,CAAC,CAAC;AACN;AACA,MAAMR,aAAa,CAAC;EAChBnB,WAAWA,CAACc,QAAQ,EAAEC,OAAO,EAAEb,SAAS,EAAE;IACtC,IAAI,CAACY,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACb,SAAS,GAAGA,SAAS;EAC9B;EACAqB,SAASA,CAAC6C,KAAK,EAAE;IACb,OAAO,IAAI,CAACtD,QAAQ,CAACuD,MAAM,CAACD,KAAK,CAACtD,QAAQ,CAAC,IACpC/B,eAAe,CAAC,IAAI,CAACgC,OAAO,CAACM,sBAAsB,EAAE+C,KAAK,CAACrD,OAAO,CAACM,sBAAsB,EAAErC,UAAU,CAAC,CAAC,CAAC,KACvGoF,KAAK,CAACrD,OAAO,CAACiB,WAAW,KAAKtC,2BAA2B,CAACuC,SAAS,IAChE,IAAI,CAAClB,OAAO,CAACiB,WAAW,KAAKtC,2BAA2B,CAAC4E,QAAQ,CAAC,IACtE,IAAI,CAACpE,SAAS,KAAKkE,KAAK,CAAClE,SAAS;EAC7C;AACJ;AACA,MAAMwD,eAAe,CAAC;EAClB1D,WAAWA,CAACkB,OAAO,EAAEqD,uBAAuB,EAAE/C,OAAO,EAAE;IACnD,IAAI,CAACN,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACqD,uBAAuB,GAAGA,uBAAuB;IACtD,IAAI,CAAC/C,OAAO,GAAGA,OAAO;EAC1B;EACAyC,OAAOA,CAAA,EAAG;IACN,IAAI,CAACM,uBAAuB,CAACC,MAAM,CAAC,CAAC;EACzC;AACJ;AACA,OAAO,MAAMzB,yBAAyB,CAAC;EACnC,IAAIvC,iBAAiBA,CAAA,EAAG;IAAE,OAAO,IAAI,CAACiE,kBAAkB;EAAE;EAC1DzE,WAAWA,CAAC0E,8BAA8B,EAAExD,OAAO,EAAEyD,UAAU,EAAEC,UAAU,EAAE;IACzE,IAAI,CAACF,8BAA8B,GAAGA,8BAA8B;IACpE,IAAI,CAACxD,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACyD,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,SAAS,GAAG,CAAC;IAClB,IAAI,CAACC,+BAA+B,GAAG,EAAE;IACzC,MAAMC,GAAG,GAAGJ,UAAU,CAACK,gBAAgB,CAAC,EAAE,EAAEN,8BAA8B,CAAC5B,WAAW,CAACmC,GAAG,CAACxG,CAAC,KAAK;MAC7F6E,KAAK,EAAE7E,CAAC,CAAC6E,KAAK;MACd4B,OAAO,EAAE;QACLC,WAAW,EAAE;MACjB;IACJ,CAAC,CAAC,CAAC,CAAC;IACJ,IAAI,CAACV,kBAAkB,GAAGC,8BAA8B,CAAC5B,WAAW,CAACmC,GAAG,CAAC,CAACxG,CAAC,EAAE2G,KAAK,KAAK,IAAIC,gCAAgC,CAAC5G,CAAC,EAAEsG,GAAG,CAACK,KAAK,CAAC,EAAE,IAAI,CAACT,UAAU,EAAE,IAAI,CAACC,UAAU,CAAC,CAAC;EACjL;EACAU,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACT,SAAS,EAAE;IAChB,OAAO,IAAI;EACf;EACAZ,OAAOA,CAAA,EAAG;IACN,IAAI,CAACY,SAAS,EAAE;IAChB,IAAI,IAAI,CAACA,SAAS,KAAK,CAAC,EAAE;MACtBb,UAAU,CAAC,MAAM;QACb;QACA,IAAI,CAAC,IAAI,CAACW,UAAU,CAACrC,UAAU,CAAC,CAAC,EAAE;UAC/B;UACA,IAAI,CAACqC,UAAU,CAACK,gBAAgB,CAAC,IAAI,CAACP,kBAAkB,CAACQ,GAAG,CAACxG,CAAC,IAAIA,CAAC,CAAC8G,YAAY,CAAC,EAAE,EAAE,CAAC;QAC1F;MACJ,CAAC,EAAE,CAAC,CAAC;MACL,IAAI,CAACb,8BAA8B,CAACT,OAAO,CAAC,CAAC;MAC7C,KAAK,MAAMxF,CAAC,IAAI,IAAI,CAACqG,+BAA+B,EAAE;QAClDrG,CAAC,CAACoD,MAAM,CAAC2D,SAAS,CAAC,CAAC;MACxB;IACJ;EACJ;EACApC,OAAOA,CAACC,gBAAgB,EAAEC,KAAK,EAAEmC,cAAc,EAAE;IAC7C,IAAIA,cAAc,EAAE;MAChBpC,gBAAgB,CAACxB,MAAM,CAAC6D,MAAM,CAAC,CAAC;IACpC;IACA,MAAMC,EAAE,GAAG,IAAI,CAAChB,UAAU,CAACK,gBAAgB,CAAC,EAAE,EAAE,CAAC;MACzC1B,KAAK;MACL4B,OAAO,EAAE;QACLC,WAAW,EAAE;MACjB;IACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,IAAI,CAACV,kBAAkB,CAACmB,OAAO,CAAC,IAAIP,gCAAgC,CAAChC,gBAAgB,EAAEsC,EAAE,EAAE,IAAI,CAAChB,UAAU,EAAE,IAAI,CAACC,UAAU,CAAC,CAAC;IAC7H,IAAI,CAACE,+BAA+B,CAACe,IAAI,CAACxC,gBAAgB,CAAC;EAC/D;AACJ;AACA,OAAO,MAAMgC,gCAAgC,CAAC;EAC1C,IAAIS,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACzC,gBAAgB,CAACxB,MAAM,CAACrB,iBAAiB,CAACuF,sBAAsB,IAAI,KAAK;EACzF;EACA/F,WAAWA,CAACqD,gBAAgB,EAAEkC,YAAY,EAAEZ,UAAU,EAAEqB,aAAa,EAAE;IACnE,IAAI,CAAC3C,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACkC,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACZ,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACqB,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,UAAU,GAAGC,IAAI,CAACC,SAAS,CAAC,CAC7B,IAAI,CAAC9C,gBAAgB,CAAC+C,UAAU,EAChC,IAAI,CAAC/C,gBAAgB,CAACgD,UAAU,EAChC,IAAI,CAAChD,gBAAgB,CAACC,KAAK,CAACgD,gBAAgB,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAC5D,CAAC;IACF,IAAI,CAACC,aAAa,GAAGpH,WAAW,CAAC;MAAEqH,KAAK,EAAE,IAAI;MAAEC,QAAQ,EAAEnH,KAAK,CAACoH;IAAY,CAAC,EAAEC,MAAM,IAAI;MACrF,IAAI,CAACZ,aAAa,CAACa,IAAI,CAACD,MAAM,CAAC;MAC/B,OAAO,IAAI,CAACjC,UAAU,CAACmC,kBAAkB,CAAC,IAAI,CAACvB,YAAY,CAAC;IAChE,CAAC,CAAC;EACN;EACAtC,kBAAkBA,CAAC2D,MAAM,EAAE;IACvB,OAAO,IAAI,CAACvD,gBAAgB,CAAC0D,SAAS,CAAC,IAAI,CAACP,aAAa,CAACK,IAAI,CAACD,MAAM,CAAC,IAAII,UAAU,CAAC;EACzF;EACAC,gBAAgBA,CAACL,MAAM,EAAE;IACrB,OAAO,IAAIpH,cAAc,CAAC,IAAI,CAACgH,aAAa,CAACK,IAAI,CAACD,MAAM,CAAC,IAAII,UAAU,EAAE,IAAI,CAAC3D,gBAAgB,CAACgD,UAAU,CAAC;EAC9G;EACAa,SAASA,CAACC,KAAK,EAAEC,cAAc,EAAER,MAAM,EAAE;IACrC,MAAMS,oBAAoB,GAAGvH,4BAA4B,CAAC,IAAI,CAACwH,wBAAwB,CAACV,MAAM,CAAC,EAAEO,KAAK,CAAC;IACvG,MAAMI,YAAY,GAAG,IAAI,CAACf,aAAa,CAACK,IAAI,CAACD,MAAM,CAAC;IACpD,IAAI,CAACW,YAAY,IACV,CAAC,IAAI,CAAClE,gBAAgB,CAACC,KAAK,CAACgD,gBAAgB,CAAC,CAAC,CAACjC,MAAM,CAACkD,YAAY,CAACjB,gBAAgB,CAAC,CAAC,CAAC,IACvFc,cAAc,CAACI,UAAU,KAAKH,oBAAoB,CAAC/D,KAAK,CAACmE,eAAe,EAAE;MAC7E,OAAO,KAAK;IAChB;IACA;IACA,MAAMC,aAAa,GAAGP,KAAK,CAACQ,eAAe,CAACN,oBAAoB,CAAC/D,KAAK,EAAE,CAAC,CAAC,4BAA4B,CAAC;IACvG,MAAM8C,UAAU,GAAGiB,oBAAoB,CAACO,IAAI;IAC5C,MAAMC,cAAc,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEX,cAAc,CAACY,MAAM,GAAGX,oBAAoB,CAAC/D,KAAK,CAAC2E,WAAW,CAAC;IAClG,IAAIC,gBAAgB,GAAG9B,UAAU,CAAC+B,SAAS,CAAC,CAAC,EAAEN,cAAc,CAAC;IAC9D,IAAIO,eAAe,GAAGhC,UAAU,CAAC+B,SAAS,CAACN,cAAc,CAAC;IAC1D,IAAIQ,mBAAmB,GAAGX,aAAa,CAACS,SAAS,CAAC,CAAC,EAAEN,cAAc,CAAC;IACpE,IAAIS,kBAAkB,GAAGZ,aAAa,CAACS,SAAS,CAACN,cAAc,CAAC;IAChE,MAAMU,mBAAmB,GAAGpB,KAAK,CAACqB,mBAAmB,CAACnB,oBAAoB,CAAC/D,KAAK,CAACmE,eAAe,CAAC;IACjG,IAAIJ,oBAAoB,CAAC/D,KAAK,CAAC2E,WAAW,IAAIM,mBAAmB,EAAE;MAC/D;MACAF,mBAAmB,GAAGA,mBAAmB,CAACI,SAAS,CAAC,CAAC;MACrD,IAAIJ,mBAAmB,CAACnK,MAAM,KAAK,CAAC,EAAE;QAClCoK,kBAAkB,GAAGA,kBAAkB,CAACG,SAAS,CAAC,CAAC;MACvD;MACAP,gBAAgB,GAAGA,gBAAgB,CAACO,SAAS,CAAC,CAAC;MAC/C,IAAIP,gBAAgB,CAAChK,MAAM,KAAK,CAAC,EAAE;QAC/BkK,eAAe,GAAGA,eAAe,CAACK,SAAS,CAAC,CAAC;MACjD;IACJ;IACA,OAAOP,gBAAgB,CAACQ,UAAU,CAACL,mBAAmB,CAAC,IAChD,CAAC,CAACpJ,gBAAgB,CAACqJ,kBAAkB,EAAEF,eAAe,CAAC;EAClE;EACAlF,WAAWA,CAACiE,KAAK,EAAErG,QAAQ,EAAE;IACzB,MAAMyG,YAAY,GAAG,IAAI,CAACf,aAAa,CAACK,IAAI,CAACpG,SAAS,CAAC;IACvD,MAAMkI,MAAM,GAAG,CAAC,CAACpB,YAAY,IACtBA,YAAY,CAACqB,gBAAgB,CAAC9H,QAAQ,CAAC,IACvC,IAAI,CAACoG,SAAS,CAACC,KAAK,EAAErG,QAAQ,EAAEL,SAAS,CAAC,IAC1ChB,UAAU,CAACoJ,OAAO,CAACtB,YAAY,CAAC,CAACuB,sBAAsB,CAACrJ,UAAU,CAACoJ,OAAO,CAAC,IAAI,CAACxF,gBAAgB,CAACC,KAAK,CAAC,CAAC;IAC/G,OAAOqF,MAAM;EACjB;EACArB,wBAAwBA,CAACV,MAAM,EAAE;IAC7B,OAAO,IAAIpH,cAAc,CAAC,IAAI,CAACgH,aAAa,CAACK,IAAI,CAACD,MAAM,CAAC,IAAII,UAAU,EAAE,IAAI,CAAC3D,gBAAgB,CAAC+C,UAAU,CAAC;EAC9G;AACJ;AACA,MAAMY,UAAU,GAAG,IAAIzH,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}