{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = this && this.__param || function (paramIndex, decorator) {\n  return function (target, key) {\n    decorator(target, key, paramIndex);\n  };\n};\nvar QuickInputController_1;\nimport * as dom from '../../../base/browser/dom.js';\nimport { ActionBar } from '../../../base/browser/ui/actionbar/actionbar.js';\nimport { Button } from '../../../base/browser/ui/button/button.js';\nimport { CountBadge } from '../../../base/browser/ui/countBadge/countBadge.js';\nimport { ProgressBar } from '../../../base/browser/ui/progressbar/progressbar.js';\nimport { CancellationToken } from '../../../base/common/cancellation.js';\nimport { Emitter, Event } from '../../../base/common/event.js';\nimport { Disposable, dispose } from '../../../base/common/lifecycle.js';\nimport Severity from '../../../base/common/severity.js';\nimport { localize } from '../../../nls.js';\nimport { QuickInputHideReason } from '../common/quickInput.js';\nimport { QuickInputBox } from './quickInputBox.js';\nimport { QuickPick, backButton, InputBox, InQuickInputContextKey, QuickInputTypeContextKey, EndOfQuickInputBoxContextKey } from './quickInput.js';\nimport { ILayoutService } from '../../layout/browser/layoutService.js';\nimport { mainWindow } from '../../../base/browser/window.js';\nimport { IInstantiationService } from '../../instantiation/common/instantiation.js';\nimport { QuickInputTree } from './quickInputTree.js';\nimport { IContextKeyService } from '../../contextkey/common/contextkey.js';\nimport './quickInputActions.js';\nconst $ = dom.$;\nlet QuickInputController = /*#__PURE__*/(() => {\n  let QuickInputController = class QuickInputController extends Disposable {\n    static {\n      QuickInputController_1 = this;\n    }\n    static {\n      this.MAX_WIDTH = 600;\n    } // Max total width of quick input widget\n    get currentQuickInput() {\n      return this.controller ?? undefined;\n    }\n    get container() {\n      return this._container;\n    }\n    constructor(options, layoutService, instantiationService, contextKeyService) {\n      super();\n      this.options = options;\n      this.layoutService = layoutService;\n      this.instantiationService = instantiationService;\n      this.contextKeyService = contextKeyService;\n      this.enabled = true;\n      this.onDidAcceptEmitter = this._register(new Emitter());\n      this.onDidCustomEmitter = this._register(new Emitter());\n      this.onDidTriggerButtonEmitter = this._register(new Emitter());\n      this.keyMods = {\n        ctrlCmd: false,\n        alt: false\n      };\n      this.controller = null;\n      this.onShowEmitter = this._register(new Emitter());\n      this.onShow = this.onShowEmitter.event;\n      this.onHideEmitter = this._register(new Emitter());\n      this.onHide = this.onHideEmitter.event;\n      this.inQuickInputContext = InQuickInputContextKey.bindTo(this.contextKeyService);\n      this.quickInputTypeContext = QuickInputTypeContextKey.bindTo(this.contextKeyService);\n      this.endOfQuickInputBoxContext = EndOfQuickInputBoxContextKey.bindTo(this.contextKeyService);\n      this.idPrefix = options.idPrefix;\n      this._container = options.container;\n      this.styles = options.styles;\n      this._register(Event.runAndSubscribe(dom.onDidRegisterWindow, ({\n        window,\n        disposables\n      }) => this.registerKeyModsListeners(window, disposables), {\n        window: mainWindow,\n        disposables: this._store\n      }));\n      this._register(dom.onWillUnregisterWindow(window => {\n        if (this.ui && dom.getWindow(this.ui.container) === window) {\n          // The window this quick input is contained in is about to\n          // close, so we have to make sure to reparent it back to an\n          // existing parent to not loose functionality.\n          // (https://github.com/microsoft/vscode/issues/195870)\n          this.reparentUI(this.layoutService.mainContainer);\n          this.layout(this.layoutService.mainContainerDimension, this.layoutService.mainContainerOffset.quickPickTop);\n        }\n      }));\n    }\n    registerKeyModsListeners(window, disposables) {\n      const listener = e => {\n        this.keyMods.ctrlCmd = e.ctrlKey || e.metaKey;\n        this.keyMods.alt = e.altKey;\n      };\n      for (const event of [dom.EventType.KEY_DOWN, dom.EventType.KEY_UP, dom.EventType.MOUSE_DOWN]) {\n        disposables.add(dom.addDisposableListener(window, event, listener, true));\n      }\n    }\n    getUI(showInActiveContainer) {\n      if (this.ui) {\n        // In order to support aux windows, re-parent the controller\n        // if the original event is from a different document\n        if (showInActiveContainer) {\n          if (dom.getWindow(this._container) !== dom.getWindow(this.layoutService.activeContainer)) {\n            this.reparentUI(this.layoutService.activeContainer);\n            this.layout(this.layoutService.activeContainerDimension, this.layoutService.activeContainerOffset.quickPickTop);\n          }\n        }\n        return this.ui;\n      }\n      const container = dom.append(this._container, $('.quick-input-widget.show-file-icons'));\n      container.tabIndex = -1;\n      container.style.display = 'none';\n      const styleSheet = dom.createStyleSheet(container);\n      const titleBar = dom.append(container, $('.quick-input-titlebar'));\n      const leftActionBar = this._register(new ActionBar(titleBar, {\n        hoverDelegate: this.options.hoverDelegate\n      }));\n      leftActionBar.domNode.classList.add('quick-input-left-action-bar');\n      const title = dom.append(titleBar, $('.quick-input-title'));\n      const rightActionBar = this._register(new ActionBar(titleBar, {\n        hoverDelegate: this.options.hoverDelegate\n      }));\n      rightActionBar.domNode.classList.add('quick-input-right-action-bar');\n      const headerContainer = dom.append(container, $('.quick-input-header'));\n      const checkAll = dom.append(headerContainer, $('input.quick-input-check-all'));\n      checkAll.type = 'checkbox';\n      checkAll.setAttribute('aria-label', localize('quickInput.checkAll', \"Toggle all checkboxes\"));\n      this._register(dom.addStandardDisposableListener(checkAll, dom.EventType.CHANGE, e => {\n        const checked = checkAll.checked;\n        list.setAllVisibleChecked(checked);\n      }));\n      this._register(dom.addDisposableListener(checkAll, dom.EventType.CLICK, e => {\n        if (e.x || e.y) {\n          // Avoid 'click' triggered by 'space'...\n          inputBox.setFocus();\n        }\n      }));\n      const description2 = dom.append(headerContainer, $('.quick-input-description'));\n      const inputContainer = dom.append(headerContainer, $('.quick-input-and-message'));\n      const filterContainer = dom.append(inputContainer, $('.quick-input-filter'));\n      const inputBox = this._register(new QuickInputBox(filterContainer, this.styles.inputBox, this.styles.toggle));\n      inputBox.setAttribute('aria-describedby', `${this.idPrefix}message`);\n      const visibleCountContainer = dom.append(filterContainer, $('.quick-input-visible-count'));\n      visibleCountContainer.setAttribute('aria-live', 'polite');\n      visibleCountContainer.setAttribute('aria-atomic', 'true');\n      const visibleCount = new CountBadge(visibleCountContainer, {\n        countFormat: localize({\n          key: 'quickInput.visibleCount',\n          comment: ['This tells the user how many items are shown in a list of items to select from. The items can be anything. Currently not visible, but read by screen readers.']\n        }, \"{0} Results\")\n      }, this.styles.countBadge);\n      const countContainer = dom.append(filterContainer, $('.quick-input-count'));\n      countContainer.setAttribute('aria-live', 'polite');\n      const count = new CountBadge(countContainer, {\n        countFormat: localize({\n          key: 'quickInput.countSelected',\n          comment: ['This tells the user how many items are selected in a list of items to select from. The items can be anything.']\n        }, \"{0} Selected\")\n      }, this.styles.countBadge);\n      const inlineActionBar = this._register(new ActionBar(headerContainer, {\n        hoverDelegate: this.options.hoverDelegate\n      }));\n      inlineActionBar.domNode.classList.add('quick-input-inline-action-bar');\n      const okContainer = dom.append(headerContainer, $('.quick-input-action'));\n      const ok = this._register(new Button(okContainer, this.styles.button));\n      ok.label = localize('ok', \"OK\");\n      this._register(ok.onDidClick(e => {\n        this.onDidAcceptEmitter.fire();\n      }));\n      const customButtonContainer = dom.append(headerContainer, $('.quick-input-action'));\n      const customButton = this._register(new Button(customButtonContainer, {\n        ...this.styles.button,\n        supportIcons: true\n      }));\n      customButton.label = localize('custom', \"Custom\");\n      this._register(customButton.onDidClick(e => {\n        this.onDidCustomEmitter.fire();\n      }));\n      const message = dom.append(inputContainer, $(`#${this.idPrefix}message.quick-input-message`));\n      const progressBar = this._register(new ProgressBar(container, this.styles.progressBar));\n      progressBar.getContainer().classList.add('quick-input-progress');\n      const widget = dom.append(container, $('.quick-input-html-widget'));\n      widget.tabIndex = -1;\n      const description1 = dom.append(container, $('.quick-input-description'));\n      const listId = this.idPrefix + 'list';\n      const list = this._register(this.instantiationService.createInstance(QuickInputTree, container, this.options.hoverDelegate, this.options.linkOpenerDelegate, listId));\n      inputBox.setAttribute('aria-controls', listId);\n      this._register(list.onDidChangeFocus(() => {\n        inputBox.setAttribute('aria-activedescendant', list.getActiveDescendant() ?? '');\n      }));\n      this._register(list.onChangedAllVisibleChecked(checked => {\n        checkAll.checked = checked;\n      }));\n      this._register(list.onChangedVisibleCount(c => {\n        visibleCount.setCount(c);\n      }));\n      this._register(list.onChangedCheckedCount(c => {\n        count.setCount(c);\n      }));\n      this._register(list.onLeave(() => {\n        // Defer to avoid the input field reacting to the triggering key.\n        // TODO@TylerLeonhardt https://github.com/microsoft/vscode/issues/203675\n        setTimeout(() => {\n          if (!this.controller) {\n            return;\n          }\n          inputBox.setFocus();\n          if (this.controller instanceof QuickPick && this.controller.canSelectMany) {\n            list.clearFocus();\n          }\n        }, 0);\n      }));\n      const focusTracker = dom.trackFocus(container);\n      this._register(focusTracker);\n      this._register(dom.addDisposableListener(container, dom.EventType.FOCUS, e => {\n        const ui = this.getUI();\n        if (dom.isAncestor(e.relatedTarget, ui.inputContainer)) {\n          const value = ui.inputBox.isSelectionAtEnd();\n          if (this.endOfQuickInputBoxContext.get() !== value) {\n            this.endOfQuickInputBoxContext.set(value);\n          }\n        }\n        // Ignore focus events within container\n        if (dom.isAncestor(e.relatedTarget, ui.container)) {\n          return;\n        }\n        this.inQuickInputContext.set(true);\n        this.previousFocusElement = dom.isHTMLElement(e.relatedTarget) ? e.relatedTarget : undefined;\n      }, true));\n      this._register(focusTracker.onDidBlur(() => {\n        if (!this.getUI().ignoreFocusOut && !this.options.ignoreFocusOut()) {\n          this.hide(QuickInputHideReason.Blur);\n        }\n        this.inQuickInputContext.set(false);\n        this.endOfQuickInputBoxContext.set(false);\n        this.previousFocusElement = undefined;\n      }));\n      this._register(inputBox.onKeyDown(_ => {\n        const value = this.getUI().inputBox.isSelectionAtEnd();\n        if (this.endOfQuickInputBoxContext.get() !== value) {\n          this.endOfQuickInputBoxContext.set(value);\n        }\n      }));\n      this._register(dom.addDisposableListener(container, dom.EventType.FOCUS, e => {\n        inputBox.setFocus();\n      }));\n      // TODO: Turn into commands instead of handling KEY_DOWN\n      // Keybindings for the quickinput widget as a whole\n      this._register(dom.addStandardDisposableListener(container, dom.EventType.KEY_DOWN, event => {\n        if (dom.isAncestor(event.target, widget)) {\n          return; // Ignore event if target is inside widget to allow the widget to handle the event.\n        }\n        switch (event.keyCode) {\n          case 3 /* KeyCode.Enter */:\n            dom.EventHelper.stop(event, true);\n            if (this.enabled) {\n              this.onDidAcceptEmitter.fire();\n            }\n            break;\n          case 9 /* KeyCode.Escape */:\n            dom.EventHelper.stop(event, true);\n            this.hide(QuickInputHideReason.Gesture);\n            break;\n          case 2 /* KeyCode.Tab */:\n            if (!event.altKey && !event.ctrlKey && !event.metaKey) {\n              // detect only visible actions\n              const selectors = ['.quick-input-list .monaco-action-bar .always-visible', '.quick-input-list-entry:hover .monaco-action-bar', '.monaco-list-row.focused .monaco-action-bar'];\n              if (container.classList.contains('show-checkboxes')) {\n                selectors.push('input');\n              } else {\n                selectors.push('input[type=text]');\n              }\n              if (this.getUI().list.displayed) {\n                selectors.push('.monaco-list');\n              }\n              // focus links if there are any\n              if (this.getUI().message) {\n                selectors.push('.quick-input-message a');\n              }\n              if (this.getUI().widget) {\n                if (dom.isAncestor(event.target, this.getUI().widget)) {\n                  // let the widget control tab\n                  break;\n                }\n                selectors.push('.quick-input-html-widget');\n              }\n              const stops = container.querySelectorAll(selectors.join(', '));\n              if (event.shiftKey && event.target === stops[0]) {\n                // Clear the focus from the list in order to allow\n                // screen readers to read operations in the input box.\n                dom.EventHelper.stop(event, true);\n                list.clearFocus();\n              } else if (!event.shiftKey && dom.isAncestor(event.target, stops[stops.length - 1])) {\n                dom.EventHelper.stop(event, true);\n                stops[0].focus();\n              }\n            }\n            break;\n          case 10 /* KeyCode.Space */:\n            if (event.ctrlKey) {\n              dom.EventHelper.stop(event, true);\n              this.getUI().list.toggleHover();\n            }\n            break;\n        }\n      }));\n      this.ui = {\n        container,\n        styleSheet,\n        leftActionBar,\n        titleBar,\n        title,\n        description1,\n        description2,\n        widget,\n        rightActionBar,\n        inlineActionBar,\n        checkAll,\n        inputContainer,\n        filterContainer,\n        inputBox,\n        visibleCountContainer,\n        visibleCount,\n        countContainer,\n        count,\n        okContainer,\n        ok,\n        message,\n        customButtonContainer,\n        customButton,\n        list,\n        progressBar,\n        onDidAccept: this.onDidAcceptEmitter.event,\n        onDidCustom: this.onDidCustomEmitter.event,\n        onDidTriggerButton: this.onDidTriggerButtonEmitter.event,\n        ignoreFocusOut: false,\n        keyMods: this.keyMods,\n        show: controller => this.show(controller),\n        hide: () => this.hide(),\n        setVisibilities: visibilities => this.setVisibilities(visibilities),\n        setEnabled: enabled => this.setEnabled(enabled),\n        setContextKey: contextKey => this.options.setContextKey(contextKey),\n        linkOpenerDelegate: content => this.options.linkOpenerDelegate(content)\n      };\n      this.updateStyles();\n      return this.ui;\n    }\n    reparentUI(container) {\n      if (this.ui) {\n        this._container = container;\n        dom.append(this._container, this.ui.container);\n      }\n    }\n    pick(picks, options = {}, token = CancellationToken.None) {\n      return new Promise((doResolve, reject) => {\n        let resolve = result => {\n          resolve = doResolve;\n          options.onKeyMods?.(input.keyMods);\n          doResolve(result);\n        };\n        if (token.isCancellationRequested) {\n          resolve(undefined);\n          return;\n        }\n        const input = this.createQuickPick({\n          useSeparators: true\n        });\n        let activeItem;\n        const disposables = [input, input.onDidAccept(() => {\n          if (input.canSelectMany) {\n            resolve(input.selectedItems.slice());\n            input.hide();\n          } else {\n            const result = input.activeItems[0];\n            if (result) {\n              resolve(result);\n              input.hide();\n            }\n          }\n        }), input.onDidChangeActive(items => {\n          const focused = items[0];\n          if (focused && options.onDidFocus) {\n            options.onDidFocus(focused);\n          }\n        }), input.onDidChangeSelection(items => {\n          if (!input.canSelectMany) {\n            const result = items[0];\n            if (result) {\n              resolve(result);\n              input.hide();\n            }\n          }\n        }), input.onDidTriggerItemButton(event => options.onDidTriggerItemButton && options.onDidTriggerItemButton({\n          ...event,\n          removeItem: () => {\n            const index = input.items.indexOf(event.item);\n            if (index !== -1) {\n              const items = input.items.slice();\n              const removed = items.splice(index, 1);\n              const activeItems = input.activeItems.filter(activeItem => activeItem !== removed[0]);\n              const keepScrollPositionBefore = input.keepScrollPosition;\n              input.keepScrollPosition = true;\n              input.items = items;\n              if (activeItems) {\n                input.activeItems = activeItems;\n              }\n              input.keepScrollPosition = keepScrollPositionBefore;\n            }\n          }\n        })), input.onDidTriggerSeparatorButton(event => options.onDidTriggerSeparatorButton?.(event)), input.onDidChangeValue(value => {\n          if (activeItem && !value && (input.activeItems.length !== 1 || input.activeItems[0] !== activeItem)) {\n            input.activeItems = [activeItem];\n          }\n        }), token.onCancellationRequested(() => {\n          input.hide();\n        }), input.onDidHide(() => {\n          dispose(disposables);\n          resolve(undefined);\n        })];\n        input.title = options.title;\n        if (options.value) {\n          input.value = options.value;\n        }\n        input.canSelectMany = !!options.canPickMany;\n        input.placeholder = options.placeHolder;\n        input.ignoreFocusOut = !!options.ignoreFocusLost;\n        input.matchOnDescription = !!options.matchOnDescription;\n        input.matchOnDetail = !!options.matchOnDetail;\n        input.matchOnLabel = options.matchOnLabel === undefined || options.matchOnLabel; // default to true\n        input.quickNavigate = options.quickNavigate;\n        input.hideInput = !!options.hideInput;\n        input.contextKey = options.contextKey;\n        input.busy = true;\n        Promise.all([picks, options.activeItem]).then(([items, _activeItem]) => {\n          activeItem = _activeItem;\n          input.busy = false;\n          input.items = items;\n          if (input.canSelectMany) {\n            input.selectedItems = items.filter(item => item.type !== 'separator' && item.picked);\n          }\n          if (activeItem) {\n            input.activeItems = [activeItem];\n          }\n        });\n        input.show();\n        Promise.resolve(picks).then(undefined, err => {\n          reject(err);\n          input.hide();\n        });\n      });\n    }\n    createQuickPick(options = {\n      useSeparators: false\n    }) {\n      const ui = this.getUI(true);\n      return new QuickPick(ui);\n    }\n    createInputBox() {\n      const ui = this.getUI(true);\n      return new InputBox(ui);\n    }\n    show(controller) {\n      const ui = this.getUI(true);\n      this.onShowEmitter.fire();\n      const oldController = this.controller;\n      this.controller = controller;\n      oldController?.didHide();\n      this.setEnabled(true);\n      ui.leftActionBar.clear();\n      ui.title.textContent = '';\n      ui.description1.textContent = '';\n      ui.description2.textContent = '';\n      dom.reset(ui.widget);\n      ui.rightActionBar.clear();\n      ui.inlineActionBar.clear();\n      ui.checkAll.checked = false;\n      // ui.inputBox.value = ''; Avoid triggering an event.\n      ui.inputBox.placeholder = '';\n      ui.inputBox.password = false;\n      ui.inputBox.showDecoration(Severity.Ignore);\n      ui.visibleCount.setCount(0);\n      ui.count.setCount(0);\n      dom.reset(ui.message);\n      ui.progressBar.stop();\n      ui.list.setElements([]);\n      ui.list.matchOnDescription = false;\n      ui.list.matchOnDetail = false;\n      ui.list.matchOnLabel = true;\n      ui.list.sortByLabel = true;\n      ui.ignoreFocusOut = false;\n      ui.inputBox.toggles = undefined;\n      const backKeybindingLabel = this.options.backKeybindingLabel();\n      backButton.tooltip = backKeybindingLabel ? localize('quickInput.backWithKeybinding', \"Back ({0})\", backKeybindingLabel) : localize('quickInput.back', \"Back\");\n      ui.container.style.display = '';\n      this.updateLayout();\n      ui.inputBox.setFocus();\n      this.quickInputTypeContext.set(controller.type);\n    }\n    isVisible() {\n      return !!this.ui && this.ui.container.style.display !== 'none';\n    }\n    setVisibilities(visibilities) {\n      const ui = this.getUI();\n      ui.title.style.display = visibilities.title ? '' : 'none';\n      ui.description1.style.display = visibilities.description && (visibilities.inputBox || visibilities.checkAll) ? '' : 'none';\n      ui.description2.style.display = visibilities.description && !(visibilities.inputBox || visibilities.checkAll) ? '' : 'none';\n      ui.checkAll.style.display = visibilities.checkAll ? '' : 'none';\n      ui.inputContainer.style.display = visibilities.inputBox ? '' : 'none';\n      ui.filterContainer.style.display = visibilities.inputBox ? '' : 'none';\n      ui.visibleCountContainer.style.display = visibilities.visibleCount ? '' : 'none';\n      ui.countContainer.style.display = visibilities.count ? '' : 'none';\n      ui.okContainer.style.display = visibilities.ok ? '' : 'none';\n      ui.customButtonContainer.style.display = visibilities.customButton ? '' : 'none';\n      ui.message.style.display = visibilities.message ? '' : 'none';\n      ui.progressBar.getContainer().style.display = visibilities.progressBar ? '' : 'none';\n      ui.list.displayed = !!visibilities.list;\n      ui.container.classList.toggle('show-checkboxes', !!visibilities.checkBox);\n      ui.container.classList.toggle('hidden-input', !visibilities.inputBox && !visibilities.description);\n      this.updateLayout(); // TODO\n    }\n    setEnabled(enabled) {\n      if (enabled !== this.enabled) {\n        this.enabled = enabled;\n        for (const item of this.getUI().leftActionBar.viewItems) {\n          item.action.enabled = enabled;\n        }\n        for (const item of this.getUI().rightActionBar.viewItems) {\n          item.action.enabled = enabled;\n        }\n        this.getUI().checkAll.disabled = !enabled;\n        this.getUI().inputBox.enabled = enabled;\n        this.getUI().ok.enabled = enabled;\n        this.getUI().list.enabled = enabled;\n      }\n    }\n    hide(reason) {\n      const controller = this.controller;\n      if (!controller) {\n        return;\n      }\n      controller.willHide(reason);\n      const container = this.ui?.container;\n      const focusChanged = container && !dom.isAncestorOfActiveElement(container);\n      this.controller = null;\n      this.onHideEmitter.fire();\n      if (container) {\n        container.style.display = 'none';\n      }\n      if (!focusChanged) {\n        let currentElement = this.previousFocusElement;\n        while (currentElement && !currentElement.offsetParent) {\n          currentElement = currentElement.parentElement ?? undefined;\n        }\n        if (currentElement?.offsetParent) {\n          currentElement.focus();\n          this.previousFocusElement = undefined;\n        } else {\n          this.options.returnFocus();\n        }\n      }\n      controller.didHide(reason);\n    }\n    layout(dimension, titleBarOffset) {\n      this.dimension = dimension;\n      this.titleBarOffset = titleBarOffset;\n      this.updateLayout();\n    }\n    updateLayout() {\n      if (this.ui && this.isVisible()) {\n        this.ui.container.style.top = `${this.titleBarOffset}px`;\n        const style = this.ui.container.style;\n        const width = Math.min(this.dimension.width * 0.62 /* golden cut */, QuickInputController_1.MAX_WIDTH);\n        style.width = width + 'px';\n        style.marginLeft = '-' + width / 2 + 'px';\n        this.ui.inputBox.layout();\n        this.ui.list.layout(this.dimension && this.dimension.height * 0.4);\n      }\n    }\n    applyStyles(styles) {\n      this.styles = styles;\n      this.updateStyles();\n    }\n    updateStyles() {\n      if (this.ui) {\n        const {\n          quickInputTitleBackground,\n          quickInputBackground,\n          quickInputForeground,\n          widgetBorder,\n          widgetShadow\n        } = this.styles.widget;\n        this.ui.titleBar.style.backgroundColor = quickInputTitleBackground ?? '';\n        this.ui.container.style.backgroundColor = quickInputBackground ?? '';\n        this.ui.container.style.color = quickInputForeground ?? '';\n        this.ui.container.style.border = widgetBorder ? `1px solid ${widgetBorder}` : '';\n        this.ui.container.style.boxShadow = widgetShadow ? `0 0 8px 2px ${widgetShadow}` : '';\n        this.ui.list.style(this.styles.list);\n        const content = [];\n        if (this.styles.pickerGroup.pickerGroupBorder) {\n          content.push(`.quick-input-list .quick-input-list-entry { border-top-color:  ${this.styles.pickerGroup.pickerGroupBorder}; }`);\n        }\n        if (this.styles.pickerGroup.pickerGroupForeground) {\n          content.push(`.quick-input-list .quick-input-list-separator { color:  ${this.styles.pickerGroup.pickerGroupForeground}; }`);\n        }\n        if (this.styles.pickerGroup.pickerGroupForeground) {\n          content.push(`.quick-input-list .quick-input-list-separator-as-item { color: var(--vscode-descriptionForeground); }`);\n        }\n        if (this.styles.keybindingLabel.keybindingLabelBackground || this.styles.keybindingLabel.keybindingLabelBorder || this.styles.keybindingLabel.keybindingLabelBottomBorder || this.styles.keybindingLabel.keybindingLabelShadow || this.styles.keybindingLabel.keybindingLabelForeground) {\n          content.push('.quick-input-list .monaco-keybinding > .monaco-keybinding-key {');\n          if (this.styles.keybindingLabel.keybindingLabelBackground) {\n            content.push(`background-color: ${this.styles.keybindingLabel.keybindingLabelBackground};`);\n          }\n          if (this.styles.keybindingLabel.keybindingLabelBorder) {\n            // Order matters here. `border-color` must come before `border-bottom-color`.\n            content.push(`border-color: ${this.styles.keybindingLabel.keybindingLabelBorder};`);\n          }\n          if (this.styles.keybindingLabel.keybindingLabelBottomBorder) {\n            content.push(`border-bottom-color: ${this.styles.keybindingLabel.keybindingLabelBottomBorder};`);\n          }\n          if (this.styles.keybindingLabel.keybindingLabelShadow) {\n            content.push(`box-shadow: inset 0 -1px 0 ${this.styles.keybindingLabel.keybindingLabelShadow};`);\n          }\n          if (this.styles.keybindingLabel.keybindingLabelForeground) {\n            content.push(`color: ${this.styles.keybindingLabel.keybindingLabelForeground};`);\n          }\n          content.push('}');\n        }\n        const newStyles = content.join('\\n');\n        if (newStyles !== this.ui.styleSheet.textContent) {\n          this.ui.styleSheet.textContent = newStyles;\n        }\n      }\n    }\n  };\n  return QuickInputController;\n})();\nQuickInputController = QuickInputController_1 = __decorate([__param(1, ILayoutService), __param(2, IInstantiationService), __param(3, IContextKeyService)], QuickInputController);\nexport { QuickInputController };", "map": {"version": 3, "names": ["__decorate", "decorators", "target", "key", "desc", "c", "arguments", "length", "r", "Object", "getOwnPropertyDescriptor", "d", "Reflect", "decorate", "i", "defineProperty", "__param", "paramIndex", "decorator", "QuickInputController_1", "dom", "ActionBar", "<PERSON><PERSON>", "Count<PERSON>adge", "ProgressBar", "CancellationToken", "Emitter", "Event", "Disposable", "dispose", "Severity", "localize", "QuickInputHideReason", "QuickInputBox", "QuickPick", "backButton", "InputBox", "InQuickInputContextKey", "QuickInputTypeContextKey", "EndOfQuickInputBoxContextKey", "ILayoutService", "mainWindow", "IInstantiationService", "QuickInputTree", "IContextKeyService", "$", "QuickInputController", "MAX_WIDTH", "currentQuickInput", "controller", "undefined", "container", "_container", "constructor", "options", "layoutService", "instantiationService", "contextKeyService", "enabled", "onDidAcceptEmitter", "_register", "onDidCustomEmitter", "onDidTriggerButtonEmitter", "keyMods", "ctrlCmd", "alt", "onShowEmitter", "onShow", "event", "onHideEmitter", "onHide", "inQuickInputContext", "bindTo", "quickInputTypeContext", "endOfQuickInputBoxContext", "idPrefix", "styles", "runAndSubscribe", "onDidRegisterWindow", "window", "disposables", "registerKeyModsListeners", "_store", "onWillUnregisterWindow", "ui", "getWindow", "reparentUI", "mainContainer", "layout", "mainContainerDimension", "mainContainerOffset", "quickPickTop", "listener", "e", "ctrl<PERSON>ey", "metaKey", "altKey", "EventType", "KEY_DOWN", "KEY_UP", "MOUSE_DOWN", "add", "addDisposableListener", "getUI", "showInActiveContainer", "activeContainer", "activeContainerDimension", "activeContainerOffset", "append", "tabIndex", "style", "display", "styleSheet", "createStyleSheet", "titleBar", "leftActionBar", "hoverDelegate", "domNode", "classList", "title", "rightActionBar", "headerContainer", "checkAll", "type", "setAttribute", "addStandardDisposableListener", "CHANGE", "checked", "list", "setAllVisibleChecked", "CLICK", "x", "y", "inputBox", "setFocus", "description2", "inputContainer", "filterContainer", "toggle", "visibleCountContainer", "visibleCount", "countFormat", "comment", "countBadge", "<PERSON><PERSON><PERSON><PERSON>", "count", "inlineActionBar", "okContainer", "ok", "button", "label", "onDidClick", "fire", "customButtonContainer", "customButton", "supportIcons", "message", "progressBar", "getContainer", "widget", "description1", "listId", "createInstance", "linkOpenerDelegate", "onDidChangeFocus", "getActiveDescendant", "onChangedAllVisibleChecked", "onChangedVisibleCount", "setCount", "onChangedCheckedCount", "onLeave", "setTimeout", "canSelectMany", "clearFocus", "focusTracker", "trackFocus", "FOCUS", "isAncestor", "relatedTarget", "value", "isSelectionAtEnd", "get", "set", "previousFocusElement", "isHTMLElement", "onDidBlur", "ignoreFocusOut", "hide", "Blur", "onKeyDown", "_", "keyCode", "EventHelper", "stop", "Gesture", "selectors", "contains", "push", "displayed", "stops", "querySelectorAll", "join", "shift<PERSON>ey", "focus", "toggleHover", "onDidAccept", "onDidCustom", "onDidTriggerButton", "show", "setVisibilities", "visibilities", "setEnabled", "setContextKey", "<PERSON><PERSON>ey", "content", "updateStyles", "pick", "picks", "token", "None", "Promise", "doResolve", "reject", "resolve", "result", "onKeyMods", "input", "isCancellationRequested", "createQuickPick", "useSeparators", "activeItem", "selectedItems", "slice", "activeItems", "onDidChangeActive", "items", "focused", "onDidFocus", "onDidChangeSelection", "onDidTriggerItemButton", "removeItem", "index", "indexOf", "item", "removed", "splice", "filter", "keepScrollPositionBefore", "keepScrollPosition", "onDidTriggerSeparatorButton", "onDidChangeValue", "onCancellationRequested", "onDidHide", "canPickMany", "placeholder", "placeHolder", "ignoreFocusLost", "matchOnDescription", "matchOnDetail", "matchOnLabel", "quickNavigate", "hideInput", "busy", "all", "then", "_activeItem", "picked", "err", "createInputBox", "oldController", "didHide", "clear", "textContent", "reset", "password", "showDecoration", "Ignore", "setElements", "sortByLabel", "toggles", "backKeybindingLabel", "tooltip", "updateLayout", "isVisible", "description", "checkBox", "viewItems", "action", "disabled", "reason", "willHide", "focusChanged", "isAncestorOfActiveElement", "currentElement", "offsetParent", "parentElement", "returnFocus", "dimension", "titleBarOffset", "top", "width", "Math", "min", "marginLeft", "height", "applyStyles", "quickInputTitleBackground", "quickInputBackground", "quickInputForeground", "widgetBorder", "widgetShadow", "backgroundColor", "color", "border", "boxShadow", "pickerGroup", "pickerGroupBorder", "pickerGroupForeground", "keybinding<PERSON>abel", "keybindingLabelBackground", "keybindingLabelBorder", "keybindingLabelBottomBorder", "keybindingLabelShadow", "keybindingLabelForeground", "newStyles"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/platform/quickinput/browser/quickInputController.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = (this && this.__param) || function (paramIndex, decorator) {\n    return function (target, key) { decorator(target, key, paramIndex); }\n};\nvar QuickInputController_1;\nimport * as dom from '../../../base/browser/dom.js';\nimport { ActionBar } from '../../../base/browser/ui/actionbar/actionbar.js';\nimport { Button } from '../../../base/browser/ui/button/button.js';\nimport { CountBadge } from '../../../base/browser/ui/countBadge/countBadge.js';\nimport { ProgressBar } from '../../../base/browser/ui/progressbar/progressbar.js';\nimport { CancellationToken } from '../../../base/common/cancellation.js';\nimport { Emitter, Event } from '../../../base/common/event.js';\nimport { Disposable, dispose } from '../../../base/common/lifecycle.js';\nimport Severity from '../../../base/common/severity.js';\nimport { localize } from '../../../nls.js';\nimport { QuickInputHideReason } from '../common/quickInput.js';\nimport { QuickInputBox } from './quickInputBox.js';\nimport { QuickPick, backButton, InputBox, InQuickInputContextKey, QuickInputTypeContextKey, EndOfQuickInputBoxContextKey } from './quickInput.js';\nimport { ILayoutService } from '../../layout/browser/layoutService.js';\nimport { mainWindow } from '../../../base/browser/window.js';\nimport { IInstantiationService } from '../../instantiation/common/instantiation.js';\nimport { QuickInputTree } from './quickInputTree.js';\nimport { IContextKeyService } from '../../contextkey/common/contextkey.js';\nimport './quickInputActions.js';\nconst $ = dom.$;\nlet QuickInputController = class QuickInputController extends Disposable {\n    static { QuickInputController_1 = this; }\n    static { this.MAX_WIDTH = 600; } // Max total width of quick input widget\n    get currentQuickInput() { return this.controller ?? undefined; }\n    get container() { return this._container; }\n    constructor(options, layoutService, instantiationService, contextKeyService) {\n        super();\n        this.options = options;\n        this.layoutService = layoutService;\n        this.instantiationService = instantiationService;\n        this.contextKeyService = contextKeyService;\n        this.enabled = true;\n        this.onDidAcceptEmitter = this._register(new Emitter());\n        this.onDidCustomEmitter = this._register(new Emitter());\n        this.onDidTriggerButtonEmitter = this._register(new Emitter());\n        this.keyMods = { ctrlCmd: false, alt: false };\n        this.controller = null;\n        this.onShowEmitter = this._register(new Emitter());\n        this.onShow = this.onShowEmitter.event;\n        this.onHideEmitter = this._register(new Emitter());\n        this.onHide = this.onHideEmitter.event;\n        this.inQuickInputContext = InQuickInputContextKey.bindTo(this.contextKeyService);\n        this.quickInputTypeContext = QuickInputTypeContextKey.bindTo(this.contextKeyService);\n        this.endOfQuickInputBoxContext = EndOfQuickInputBoxContextKey.bindTo(this.contextKeyService);\n        this.idPrefix = options.idPrefix;\n        this._container = options.container;\n        this.styles = options.styles;\n        this._register(Event.runAndSubscribe(dom.onDidRegisterWindow, ({ window, disposables }) => this.registerKeyModsListeners(window, disposables), { window: mainWindow, disposables: this._store }));\n        this._register(dom.onWillUnregisterWindow(window => {\n            if (this.ui && dom.getWindow(this.ui.container) === window) {\n                // The window this quick input is contained in is about to\n                // close, so we have to make sure to reparent it back to an\n                // existing parent to not loose functionality.\n                // (https://github.com/microsoft/vscode/issues/195870)\n                this.reparentUI(this.layoutService.mainContainer);\n                this.layout(this.layoutService.mainContainerDimension, this.layoutService.mainContainerOffset.quickPickTop);\n            }\n        }));\n    }\n    registerKeyModsListeners(window, disposables) {\n        const listener = (e) => {\n            this.keyMods.ctrlCmd = e.ctrlKey || e.metaKey;\n            this.keyMods.alt = e.altKey;\n        };\n        for (const event of [dom.EventType.KEY_DOWN, dom.EventType.KEY_UP, dom.EventType.MOUSE_DOWN]) {\n            disposables.add(dom.addDisposableListener(window, event, listener, true));\n        }\n    }\n    getUI(showInActiveContainer) {\n        if (this.ui) {\n            // In order to support aux windows, re-parent the controller\n            // if the original event is from a different document\n            if (showInActiveContainer) {\n                if (dom.getWindow(this._container) !== dom.getWindow(this.layoutService.activeContainer)) {\n                    this.reparentUI(this.layoutService.activeContainer);\n                    this.layout(this.layoutService.activeContainerDimension, this.layoutService.activeContainerOffset.quickPickTop);\n                }\n            }\n            return this.ui;\n        }\n        const container = dom.append(this._container, $('.quick-input-widget.show-file-icons'));\n        container.tabIndex = -1;\n        container.style.display = 'none';\n        const styleSheet = dom.createStyleSheet(container);\n        const titleBar = dom.append(container, $('.quick-input-titlebar'));\n        const leftActionBar = this._register(new ActionBar(titleBar, { hoverDelegate: this.options.hoverDelegate }));\n        leftActionBar.domNode.classList.add('quick-input-left-action-bar');\n        const title = dom.append(titleBar, $('.quick-input-title'));\n        const rightActionBar = this._register(new ActionBar(titleBar, { hoverDelegate: this.options.hoverDelegate }));\n        rightActionBar.domNode.classList.add('quick-input-right-action-bar');\n        const headerContainer = dom.append(container, $('.quick-input-header'));\n        const checkAll = dom.append(headerContainer, $('input.quick-input-check-all'));\n        checkAll.type = 'checkbox';\n        checkAll.setAttribute('aria-label', localize('quickInput.checkAll', \"Toggle all checkboxes\"));\n        this._register(dom.addStandardDisposableListener(checkAll, dom.EventType.CHANGE, e => {\n            const checked = checkAll.checked;\n            list.setAllVisibleChecked(checked);\n        }));\n        this._register(dom.addDisposableListener(checkAll, dom.EventType.CLICK, e => {\n            if (e.x || e.y) { // Avoid 'click' triggered by 'space'...\n                inputBox.setFocus();\n            }\n        }));\n        const description2 = dom.append(headerContainer, $('.quick-input-description'));\n        const inputContainer = dom.append(headerContainer, $('.quick-input-and-message'));\n        const filterContainer = dom.append(inputContainer, $('.quick-input-filter'));\n        const inputBox = this._register(new QuickInputBox(filterContainer, this.styles.inputBox, this.styles.toggle));\n        inputBox.setAttribute('aria-describedby', `${this.idPrefix}message`);\n        const visibleCountContainer = dom.append(filterContainer, $('.quick-input-visible-count'));\n        visibleCountContainer.setAttribute('aria-live', 'polite');\n        visibleCountContainer.setAttribute('aria-atomic', 'true');\n        const visibleCount = new CountBadge(visibleCountContainer, { countFormat: localize({ key: 'quickInput.visibleCount', comment: ['This tells the user how many items are shown in a list of items to select from. The items can be anything. Currently not visible, but read by screen readers.'] }, \"{0} Results\") }, this.styles.countBadge);\n        const countContainer = dom.append(filterContainer, $('.quick-input-count'));\n        countContainer.setAttribute('aria-live', 'polite');\n        const count = new CountBadge(countContainer, { countFormat: localize({ key: 'quickInput.countSelected', comment: ['This tells the user how many items are selected in a list of items to select from. The items can be anything.'] }, \"{0} Selected\") }, this.styles.countBadge);\n        const inlineActionBar = this._register(new ActionBar(headerContainer, { hoverDelegate: this.options.hoverDelegate }));\n        inlineActionBar.domNode.classList.add('quick-input-inline-action-bar');\n        const okContainer = dom.append(headerContainer, $('.quick-input-action'));\n        const ok = this._register(new Button(okContainer, this.styles.button));\n        ok.label = localize('ok', \"OK\");\n        this._register(ok.onDidClick(e => {\n            this.onDidAcceptEmitter.fire();\n        }));\n        const customButtonContainer = dom.append(headerContainer, $('.quick-input-action'));\n        const customButton = this._register(new Button(customButtonContainer, { ...this.styles.button, supportIcons: true }));\n        customButton.label = localize('custom', \"Custom\");\n        this._register(customButton.onDidClick(e => {\n            this.onDidCustomEmitter.fire();\n        }));\n        const message = dom.append(inputContainer, $(`#${this.idPrefix}message.quick-input-message`));\n        const progressBar = this._register(new ProgressBar(container, this.styles.progressBar));\n        progressBar.getContainer().classList.add('quick-input-progress');\n        const widget = dom.append(container, $('.quick-input-html-widget'));\n        widget.tabIndex = -1;\n        const description1 = dom.append(container, $('.quick-input-description'));\n        const listId = this.idPrefix + 'list';\n        const list = this._register(this.instantiationService.createInstance(QuickInputTree, container, this.options.hoverDelegate, this.options.linkOpenerDelegate, listId));\n        inputBox.setAttribute('aria-controls', listId);\n        this._register(list.onDidChangeFocus(() => {\n            inputBox.setAttribute('aria-activedescendant', list.getActiveDescendant() ?? '');\n        }));\n        this._register(list.onChangedAllVisibleChecked(checked => {\n            checkAll.checked = checked;\n        }));\n        this._register(list.onChangedVisibleCount(c => {\n            visibleCount.setCount(c);\n        }));\n        this._register(list.onChangedCheckedCount(c => {\n            count.setCount(c);\n        }));\n        this._register(list.onLeave(() => {\n            // Defer to avoid the input field reacting to the triggering key.\n            // TODO@TylerLeonhardt https://github.com/microsoft/vscode/issues/203675\n            setTimeout(() => {\n                if (!this.controller) {\n                    return;\n                }\n                inputBox.setFocus();\n                if (this.controller instanceof QuickPick && this.controller.canSelectMany) {\n                    list.clearFocus();\n                }\n            }, 0);\n        }));\n        const focusTracker = dom.trackFocus(container);\n        this._register(focusTracker);\n        this._register(dom.addDisposableListener(container, dom.EventType.FOCUS, e => {\n            const ui = this.getUI();\n            if (dom.isAncestor(e.relatedTarget, ui.inputContainer)) {\n                const value = ui.inputBox.isSelectionAtEnd();\n                if (this.endOfQuickInputBoxContext.get() !== value) {\n                    this.endOfQuickInputBoxContext.set(value);\n                }\n            }\n            // Ignore focus events within container\n            if (dom.isAncestor(e.relatedTarget, ui.container)) {\n                return;\n            }\n            this.inQuickInputContext.set(true);\n            this.previousFocusElement = dom.isHTMLElement(e.relatedTarget) ? e.relatedTarget : undefined;\n        }, true));\n        this._register(focusTracker.onDidBlur(() => {\n            if (!this.getUI().ignoreFocusOut && !this.options.ignoreFocusOut()) {\n                this.hide(QuickInputHideReason.Blur);\n            }\n            this.inQuickInputContext.set(false);\n            this.endOfQuickInputBoxContext.set(false);\n            this.previousFocusElement = undefined;\n        }));\n        this._register(inputBox.onKeyDown(_ => {\n            const value = this.getUI().inputBox.isSelectionAtEnd();\n            if (this.endOfQuickInputBoxContext.get() !== value) {\n                this.endOfQuickInputBoxContext.set(value);\n            }\n        }));\n        this._register(dom.addDisposableListener(container, dom.EventType.FOCUS, (e) => {\n            inputBox.setFocus();\n        }));\n        // TODO: Turn into commands instead of handling KEY_DOWN\n        // Keybindings for the quickinput widget as a whole\n        this._register(dom.addStandardDisposableListener(container, dom.EventType.KEY_DOWN, (event) => {\n            if (dom.isAncestor(event.target, widget)) {\n                return; // Ignore event if target is inside widget to allow the widget to handle the event.\n            }\n            switch (event.keyCode) {\n                case 3 /* KeyCode.Enter */:\n                    dom.EventHelper.stop(event, true);\n                    if (this.enabled) {\n                        this.onDidAcceptEmitter.fire();\n                    }\n                    break;\n                case 9 /* KeyCode.Escape */:\n                    dom.EventHelper.stop(event, true);\n                    this.hide(QuickInputHideReason.Gesture);\n                    break;\n                case 2 /* KeyCode.Tab */:\n                    if (!event.altKey && !event.ctrlKey && !event.metaKey) {\n                        // detect only visible actions\n                        const selectors = [\n                            '.quick-input-list .monaco-action-bar .always-visible',\n                            '.quick-input-list-entry:hover .monaco-action-bar',\n                            '.monaco-list-row.focused .monaco-action-bar'\n                        ];\n                        if (container.classList.contains('show-checkboxes')) {\n                            selectors.push('input');\n                        }\n                        else {\n                            selectors.push('input[type=text]');\n                        }\n                        if (this.getUI().list.displayed) {\n                            selectors.push('.monaco-list');\n                        }\n                        // focus links if there are any\n                        if (this.getUI().message) {\n                            selectors.push('.quick-input-message a');\n                        }\n                        if (this.getUI().widget) {\n                            if (dom.isAncestor(event.target, this.getUI().widget)) {\n                                // let the widget control tab\n                                break;\n                            }\n                            selectors.push('.quick-input-html-widget');\n                        }\n                        const stops = container.querySelectorAll(selectors.join(', '));\n                        if (event.shiftKey && event.target === stops[0]) {\n                            // Clear the focus from the list in order to allow\n                            // screen readers to read operations in the input box.\n                            dom.EventHelper.stop(event, true);\n                            list.clearFocus();\n                        }\n                        else if (!event.shiftKey && dom.isAncestor(event.target, stops[stops.length - 1])) {\n                            dom.EventHelper.stop(event, true);\n                            stops[0].focus();\n                        }\n                    }\n                    break;\n                case 10 /* KeyCode.Space */:\n                    if (event.ctrlKey) {\n                        dom.EventHelper.stop(event, true);\n                        this.getUI().list.toggleHover();\n                    }\n                    break;\n            }\n        }));\n        this.ui = {\n            container,\n            styleSheet,\n            leftActionBar,\n            titleBar,\n            title,\n            description1,\n            description2,\n            widget,\n            rightActionBar,\n            inlineActionBar,\n            checkAll,\n            inputContainer,\n            filterContainer,\n            inputBox,\n            visibleCountContainer,\n            visibleCount,\n            countContainer,\n            count,\n            okContainer,\n            ok,\n            message,\n            customButtonContainer,\n            customButton,\n            list,\n            progressBar,\n            onDidAccept: this.onDidAcceptEmitter.event,\n            onDidCustom: this.onDidCustomEmitter.event,\n            onDidTriggerButton: this.onDidTriggerButtonEmitter.event,\n            ignoreFocusOut: false,\n            keyMods: this.keyMods,\n            show: controller => this.show(controller),\n            hide: () => this.hide(),\n            setVisibilities: visibilities => this.setVisibilities(visibilities),\n            setEnabled: enabled => this.setEnabled(enabled),\n            setContextKey: contextKey => this.options.setContextKey(contextKey),\n            linkOpenerDelegate: content => this.options.linkOpenerDelegate(content)\n        };\n        this.updateStyles();\n        return this.ui;\n    }\n    reparentUI(container) {\n        if (this.ui) {\n            this._container = container;\n            dom.append(this._container, this.ui.container);\n        }\n    }\n    pick(picks, options = {}, token = CancellationToken.None) {\n        return new Promise((doResolve, reject) => {\n            let resolve = (result) => {\n                resolve = doResolve;\n                options.onKeyMods?.(input.keyMods);\n                doResolve(result);\n            };\n            if (token.isCancellationRequested) {\n                resolve(undefined);\n                return;\n            }\n            const input = this.createQuickPick({ useSeparators: true });\n            let activeItem;\n            const disposables = [\n                input,\n                input.onDidAccept(() => {\n                    if (input.canSelectMany) {\n                        resolve(input.selectedItems.slice());\n                        input.hide();\n                    }\n                    else {\n                        const result = input.activeItems[0];\n                        if (result) {\n                            resolve(result);\n                            input.hide();\n                        }\n                    }\n                }),\n                input.onDidChangeActive(items => {\n                    const focused = items[0];\n                    if (focused && options.onDidFocus) {\n                        options.onDidFocus(focused);\n                    }\n                }),\n                input.onDidChangeSelection(items => {\n                    if (!input.canSelectMany) {\n                        const result = items[0];\n                        if (result) {\n                            resolve(result);\n                            input.hide();\n                        }\n                    }\n                }),\n                input.onDidTriggerItemButton(event => options.onDidTriggerItemButton && options.onDidTriggerItemButton({\n                    ...event,\n                    removeItem: () => {\n                        const index = input.items.indexOf(event.item);\n                        if (index !== -1) {\n                            const items = input.items.slice();\n                            const removed = items.splice(index, 1);\n                            const activeItems = input.activeItems.filter(activeItem => activeItem !== removed[0]);\n                            const keepScrollPositionBefore = input.keepScrollPosition;\n                            input.keepScrollPosition = true;\n                            input.items = items;\n                            if (activeItems) {\n                                input.activeItems = activeItems;\n                            }\n                            input.keepScrollPosition = keepScrollPositionBefore;\n                        }\n                    }\n                })),\n                input.onDidTriggerSeparatorButton(event => options.onDidTriggerSeparatorButton?.(event)),\n                input.onDidChangeValue(value => {\n                    if (activeItem && !value && (input.activeItems.length !== 1 || input.activeItems[0] !== activeItem)) {\n                        input.activeItems = [activeItem];\n                    }\n                }),\n                token.onCancellationRequested(() => {\n                    input.hide();\n                }),\n                input.onDidHide(() => {\n                    dispose(disposables);\n                    resolve(undefined);\n                }),\n            ];\n            input.title = options.title;\n            if (options.value) {\n                input.value = options.value;\n            }\n            input.canSelectMany = !!options.canPickMany;\n            input.placeholder = options.placeHolder;\n            input.ignoreFocusOut = !!options.ignoreFocusLost;\n            input.matchOnDescription = !!options.matchOnDescription;\n            input.matchOnDetail = !!options.matchOnDetail;\n            input.matchOnLabel = (options.matchOnLabel === undefined) || options.matchOnLabel; // default to true\n            input.quickNavigate = options.quickNavigate;\n            input.hideInput = !!options.hideInput;\n            input.contextKey = options.contextKey;\n            input.busy = true;\n            Promise.all([picks, options.activeItem])\n                .then(([items, _activeItem]) => {\n                activeItem = _activeItem;\n                input.busy = false;\n                input.items = items;\n                if (input.canSelectMany) {\n                    input.selectedItems = items.filter(item => item.type !== 'separator' && item.picked);\n                }\n                if (activeItem) {\n                    input.activeItems = [activeItem];\n                }\n            });\n            input.show();\n            Promise.resolve(picks).then(undefined, err => {\n                reject(err);\n                input.hide();\n            });\n        });\n    }\n    createQuickPick(options = { useSeparators: false }) {\n        const ui = this.getUI(true);\n        return new QuickPick(ui);\n    }\n    createInputBox() {\n        const ui = this.getUI(true);\n        return new InputBox(ui);\n    }\n    show(controller) {\n        const ui = this.getUI(true);\n        this.onShowEmitter.fire();\n        const oldController = this.controller;\n        this.controller = controller;\n        oldController?.didHide();\n        this.setEnabled(true);\n        ui.leftActionBar.clear();\n        ui.title.textContent = '';\n        ui.description1.textContent = '';\n        ui.description2.textContent = '';\n        dom.reset(ui.widget);\n        ui.rightActionBar.clear();\n        ui.inlineActionBar.clear();\n        ui.checkAll.checked = false;\n        // ui.inputBox.value = ''; Avoid triggering an event.\n        ui.inputBox.placeholder = '';\n        ui.inputBox.password = false;\n        ui.inputBox.showDecoration(Severity.Ignore);\n        ui.visibleCount.setCount(0);\n        ui.count.setCount(0);\n        dom.reset(ui.message);\n        ui.progressBar.stop();\n        ui.list.setElements([]);\n        ui.list.matchOnDescription = false;\n        ui.list.matchOnDetail = false;\n        ui.list.matchOnLabel = true;\n        ui.list.sortByLabel = true;\n        ui.ignoreFocusOut = false;\n        ui.inputBox.toggles = undefined;\n        const backKeybindingLabel = this.options.backKeybindingLabel();\n        backButton.tooltip = backKeybindingLabel ? localize('quickInput.backWithKeybinding', \"Back ({0})\", backKeybindingLabel) : localize('quickInput.back', \"Back\");\n        ui.container.style.display = '';\n        this.updateLayout();\n        ui.inputBox.setFocus();\n        this.quickInputTypeContext.set(controller.type);\n    }\n    isVisible() {\n        return !!this.ui && this.ui.container.style.display !== 'none';\n    }\n    setVisibilities(visibilities) {\n        const ui = this.getUI();\n        ui.title.style.display = visibilities.title ? '' : 'none';\n        ui.description1.style.display = visibilities.description && (visibilities.inputBox || visibilities.checkAll) ? '' : 'none';\n        ui.description2.style.display = visibilities.description && !(visibilities.inputBox || visibilities.checkAll) ? '' : 'none';\n        ui.checkAll.style.display = visibilities.checkAll ? '' : 'none';\n        ui.inputContainer.style.display = visibilities.inputBox ? '' : 'none';\n        ui.filterContainer.style.display = visibilities.inputBox ? '' : 'none';\n        ui.visibleCountContainer.style.display = visibilities.visibleCount ? '' : 'none';\n        ui.countContainer.style.display = visibilities.count ? '' : 'none';\n        ui.okContainer.style.display = visibilities.ok ? '' : 'none';\n        ui.customButtonContainer.style.display = visibilities.customButton ? '' : 'none';\n        ui.message.style.display = visibilities.message ? '' : 'none';\n        ui.progressBar.getContainer().style.display = visibilities.progressBar ? '' : 'none';\n        ui.list.displayed = !!visibilities.list;\n        ui.container.classList.toggle('show-checkboxes', !!visibilities.checkBox);\n        ui.container.classList.toggle('hidden-input', !visibilities.inputBox && !visibilities.description);\n        this.updateLayout(); // TODO\n    }\n    setEnabled(enabled) {\n        if (enabled !== this.enabled) {\n            this.enabled = enabled;\n            for (const item of this.getUI().leftActionBar.viewItems) {\n                item.action.enabled = enabled;\n            }\n            for (const item of this.getUI().rightActionBar.viewItems) {\n                item.action.enabled = enabled;\n            }\n            this.getUI().checkAll.disabled = !enabled;\n            this.getUI().inputBox.enabled = enabled;\n            this.getUI().ok.enabled = enabled;\n            this.getUI().list.enabled = enabled;\n        }\n    }\n    hide(reason) {\n        const controller = this.controller;\n        if (!controller) {\n            return;\n        }\n        controller.willHide(reason);\n        const container = this.ui?.container;\n        const focusChanged = container && !dom.isAncestorOfActiveElement(container);\n        this.controller = null;\n        this.onHideEmitter.fire();\n        if (container) {\n            container.style.display = 'none';\n        }\n        if (!focusChanged) {\n            let currentElement = this.previousFocusElement;\n            while (currentElement && !currentElement.offsetParent) {\n                currentElement = currentElement.parentElement ?? undefined;\n            }\n            if (currentElement?.offsetParent) {\n                currentElement.focus();\n                this.previousFocusElement = undefined;\n            }\n            else {\n                this.options.returnFocus();\n            }\n        }\n        controller.didHide(reason);\n    }\n    layout(dimension, titleBarOffset) {\n        this.dimension = dimension;\n        this.titleBarOffset = titleBarOffset;\n        this.updateLayout();\n    }\n    updateLayout() {\n        if (this.ui && this.isVisible()) {\n            this.ui.container.style.top = `${this.titleBarOffset}px`;\n            const style = this.ui.container.style;\n            const width = Math.min(this.dimension.width * 0.62 /* golden cut */, QuickInputController_1.MAX_WIDTH);\n            style.width = width + 'px';\n            style.marginLeft = '-' + (width / 2) + 'px';\n            this.ui.inputBox.layout();\n            this.ui.list.layout(this.dimension && this.dimension.height * 0.4);\n        }\n    }\n    applyStyles(styles) {\n        this.styles = styles;\n        this.updateStyles();\n    }\n    updateStyles() {\n        if (this.ui) {\n            const { quickInputTitleBackground, quickInputBackground, quickInputForeground, widgetBorder, widgetShadow, } = this.styles.widget;\n            this.ui.titleBar.style.backgroundColor = quickInputTitleBackground ?? '';\n            this.ui.container.style.backgroundColor = quickInputBackground ?? '';\n            this.ui.container.style.color = quickInputForeground ?? '';\n            this.ui.container.style.border = widgetBorder ? `1px solid ${widgetBorder}` : '';\n            this.ui.container.style.boxShadow = widgetShadow ? `0 0 8px 2px ${widgetShadow}` : '';\n            this.ui.list.style(this.styles.list);\n            const content = [];\n            if (this.styles.pickerGroup.pickerGroupBorder) {\n                content.push(`.quick-input-list .quick-input-list-entry { border-top-color:  ${this.styles.pickerGroup.pickerGroupBorder}; }`);\n            }\n            if (this.styles.pickerGroup.pickerGroupForeground) {\n                content.push(`.quick-input-list .quick-input-list-separator { color:  ${this.styles.pickerGroup.pickerGroupForeground}; }`);\n            }\n            if (this.styles.pickerGroup.pickerGroupForeground) {\n                content.push(`.quick-input-list .quick-input-list-separator-as-item { color: var(--vscode-descriptionForeground); }`);\n            }\n            if (this.styles.keybindingLabel.keybindingLabelBackground ||\n                this.styles.keybindingLabel.keybindingLabelBorder ||\n                this.styles.keybindingLabel.keybindingLabelBottomBorder ||\n                this.styles.keybindingLabel.keybindingLabelShadow ||\n                this.styles.keybindingLabel.keybindingLabelForeground) {\n                content.push('.quick-input-list .monaco-keybinding > .monaco-keybinding-key {');\n                if (this.styles.keybindingLabel.keybindingLabelBackground) {\n                    content.push(`background-color: ${this.styles.keybindingLabel.keybindingLabelBackground};`);\n                }\n                if (this.styles.keybindingLabel.keybindingLabelBorder) {\n                    // Order matters here. `border-color` must come before `border-bottom-color`.\n                    content.push(`border-color: ${this.styles.keybindingLabel.keybindingLabelBorder};`);\n                }\n                if (this.styles.keybindingLabel.keybindingLabelBottomBorder) {\n                    content.push(`border-bottom-color: ${this.styles.keybindingLabel.keybindingLabelBottomBorder};`);\n                }\n                if (this.styles.keybindingLabel.keybindingLabelShadow) {\n                    content.push(`box-shadow: inset 0 -1px 0 ${this.styles.keybindingLabel.keybindingLabelShadow};`);\n                }\n                if (this.styles.keybindingLabel.keybindingLabelForeground) {\n                    content.push(`color: ${this.styles.keybindingLabel.keybindingLabelForeground};`);\n                }\n                content.push('}');\n            }\n            const newStyles = content.join('\\n');\n            if (newStyles !== this.ui.styleSheet.textContent) {\n                this.ui.styleSheet.textContent = newStyles;\n            }\n        }\n    }\n};\nQuickInputController = QuickInputController_1 = __decorate([\n    __param(1, ILayoutService),\n    __param(2, IInstantiationService),\n    __param(3, IContextKeyService)\n], QuickInputController);\nexport { QuickInputController };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,IAAIA,UAAU,GAAI,IAAI,IAAI,IAAI,CAACA,UAAU,IAAK,UAAUC,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAE;EACnF,IAAIC,CAAC,GAAGC,SAAS,CAACC,MAAM;IAAEC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGH,MAAM,GAAGE,IAAI,KAAK,IAAI,GAAGA,IAAI,GAAGK,MAAM,CAACC,wBAAwB,CAACR,MAAM,EAAEC,GAAG,CAAC,GAAGC,IAAI;IAAEO,CAAC;EAC5H,IAAI,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,CAACC,QAAQ,KAAK,UAAU,EAAEL,CAAC,GAAGI,OAAO,CAACC,QAAQ,CAACZ,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,CAAC,CAAC,KAC1H,KAAK,IAAIU,CAAC,GAAGb,UAAU,CAACM,MAAM,GAAG,CAAC,EAAEO,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE,IAAIH,CAAC,GAAGV,UAAU,CAACa,CAAC,CAAC,EAAEN,CAAC,GAAG,CAACH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACH,CAAC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACT,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,GAAGG,CAAC,CAACT,MAAM,EAAEC,GAAG,CAAC,KAAKK,CAAC;EACjJ,OAAOH,CAAC,GAAG,CAAC,IAAIG,CAAC,IAAIC,MAAM,CAACM,cAAc,CAACb,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,EAAEA,CAAC;AACjE,CAAC;AACD,IAAIQ,OAAO,GAAI,IAAI,IAAI,IAAI,CAACA,OAAO,IAAK,UAAUC,UAAU,EAAEC,SAAS,EAAE;EACrE,OAAO,UAAUhB,MAAM,EAAEC,GAAG,EAAE;IAAEe,SAAS,CAAChB,MAAM,EAAEC,GAAG,EAAEc,UAAU,CAAC;EAAE,CAAC;AACzE,CAAC;AACD,IAAIE,sBAAsB;AAC1B,OAAO,KAAKC,GAAG,MAAM,8BAA8B;AACnD,SAASC,SAAS,QAAQ,iDAAiD;AAC3E,SAASC,MAAM,QAAQ,2CAA2C;AAClE,SAASC,UAAU,QAAQ,mDAAmD;AAC9E,SAASC,WAAW,QAAQ,qDAAqD;AACjF,SAASC,iBAAiB,QAAQ,sCAAsC;AACxE,SAASC,OAAO,EAAEC,KAAK,QAAQ,+BAA+B;AAC9D,SAASC,UAAU,EAAEC,OAAO,QAAQ,mCAAmC;AACvE,OAAOC,QAAQ,MAAM,kCAAkC;AACvD,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,oBAAoB,QAAQ,yBAAyB;AAC9D,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,SAAS,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,sBAAsB,EAAEC,wBAAwB,EAAEC,4BAA4B,QAAQ,iBAAiB;AACjJ,SAASC,cAAc,QAAQ,uCAAuC;AACtE,SAASC,UAAU,QAAQ,iCAAiC;AAC5D,SAASC,qBAAqB,QAAQ,6CAA6C;AACnF,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,kBAAkB,QAAQ,uCAAuC;AAC1E,OAAO,wBAAwB;AAC/B,MAAMC,CAAC,GAAGzB,GAAG,CAACyB,CAAC;AACf,IAAIC,oBAAoB;EAAA,IAApBA,oBAAoB,GAAG,MAAMA,oBAAoB,SAASlB,UAAU,CAAC;IACrE;MAAST,sBAAsB,GAAG,IAAI;IAAE;IACxC;MAAS,IAAI,CAAC4B,SAAS,GAAG,GAAG;IAAE,CAAC,CAAC;IACjC,IAAIC,iBAAiBA,CAAA,EAAG;MAAE,OAAO,IAAI,CAACC,UAAU,IAAIC,SAAS;IAAE;IAC/D,IAAIC,SAASA,CAAA,EAAG;MAAE,OAAO,IAAI,CAACC,UAAU;IAAE;IAC1CC,WAAWA,CAACC,OAAO,EAAEC,aAAa,EAAEC,oBAAoB,EAAEC,iBAAiB,EAAE;MACzE,KAAK,CAAC,CAAC;MACP,IAAI,CAACH,OAAO,GAAGA,OAAO;MACtB,IAAI,CAACC,aAAa,GAAGA,aAAa;MAClC,IAAI,CAACC,oBAAoB,GAAGA,oBAAoB;MAChD,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;MAC1C,IAAI,CAACC,OAAO,GAAG,IAAI;MACnB,IAAI,CAACC,kBAAkB,GAAG,IAAI,CAACC,SAAS,CAAC,IAAIlC,OAAO,CAAC,CAAC,CAAC;MACvD,IAAI,CAACmC,kBAAkB,GAAG,IAAI,CAACD,SAAS,CAAC,IAAIlC,OAAO,CAAC,CAAC,CAAC;MACvD,IAAI,CAACoC,yBAAyB,GAAG,IAAI,CAACF,SAAS,CAAC,IAAIlC,OAAO,CAAC,CAAC,CAAC;MAC9D,IAAI,CAACqC,OAAO,GAAG;QAAEC,OAAO,EAAE,KAAK;QAAEC,GAAG,EAAE;MAAM,CAAC;MAC7C,IAAI,CAAChB,UAAU,GAAG,IAAI;MACtB,IAAI,CAACiB,aAAa,GAAG,IAAI,CAACN,SAAS,CAAC,IAAIlC,OAAO,CAAC,CAAC,CAAC;MAClD,IAAI,CAACyC,MAAM,GAAG,IAAI,CAACD,aAAa,CAACE,KAAK;MACtC,IAAI,CAACC,aAAa,GAAG,IAAI,CAACT,SAAS,CAAC,IAAIlC,OAAO,CAAC,CAAC,CAAC;MAClD,IAAI,CAAC4C,MAAM,GAAG,IAAI,CAACD,aAAa,CAACD,KAAK;MACtC,IAAI,CAACG,mBAAmB,GAAGlC,sBAAsB,CAACmC,MAAM,CAAC,IAAI,CAACf,iBAAiB,CAAC;MAChF,IAAI,CAACgB,qBAAqB,GAAGnC,wBAAwB,CAACkC,MAAM,CAAC,IAAI,CAACf,iBAAiB,CAAC;MACpF,IAAI,CAACiB,yBAAyB,GAAGnC,4BAA4B,CAACiC,MAAM,CAAC,IAAI,CAACf,iBAAiB,CAAC;MAC5F,IAAI,CAACkB,QAAQ,GAAGrB,OAAO,CAACqB,QAAQ;MAChC,IAAI,CAACvB,UAAU,GAAGE,OAAO,CAACH,SAAS;MACnC,IAAI,CAACyB,MAAM,GAAGtB,OAAO,CAACsB,MAAM;MAC5B,IAAI,CAAChB,SAAS,CAACjC,KAAK,CAACkD,eAAe,CAACzD,GAAG,CAAC0D,mBAAmB,EAAE,CAAC;QAAEC,MAAM;QAAEC;MAAY,CAAC,KAAK,IAAI,CAACC,wBAAwB,CAACF,MAAM,EAAEC,WAAW,CAAC,EAAE;QAAED,MAAM,EAAEtC,UAAU;QAAEuC,WAAW,EAAE,IAAI,CAACE;MAAO,CAAC,CAAC,CAAC;MACjM,IAAI,CAACtB,SAAS,CAACxC,GAAG,CAAC+D,sBAAsB,CAACJ,MAAM,IAAI;QAChD,IAAI,IAAI,CAACK,EAAE,IAAIhE,GAAG,CAACiE,SAAS,CAAC,IAAI,CAACD,EAAE,CAACjC,SAAS,CAAC,KAAK4B,MAAM,EAAE;UACxD;UACA;UACA;UACA;UACA,IAAI,CAACO,UAAU,CAAC,IAAI,CAAC/B,aAAa,CAACgC,aAAa,CAAC;UACjD,IAAI,CAACC,MAAM,CAAC,IAAI,CAACjC,aAAa,CAACkC,sBAAsB,EAAE,IAAI,CAAClC,aAAa,CAACmC,mBAAmB,CAACC,YAAY,CAAC;QAC/G;MACJ,CAAC,CAAC,CAAC;IACP;IACAV,wBAAwBA,CAACF,MAAM,EAAEC,WAAW,EAAE;MAC1C,MAAMY,QAAQ,GAAIC,CAAC,IAAK;QACpB,IAAI,CAAC9B,OAAO,CAACC,OAAO,GAAG6B,CAAC,CAACC,OAAO,IAAID,CAAC,CAACE,OAAO;QAC7C,IAAI,CAAChC,OAAO,CAACE,GAAG,GAAG4B,CAAC,CAACG,MAAM;MAC/B,CAAC;MACD,KAAK,MAAM5B,KAAK,IAAI,CAAChD,GAAG,CAAC6E,SAAS,CAACC,QAAQ,EAAE9E,GAAG,CAAC6E,SAAS,CAACE,MAAM,EAAE/E,GAAG,CAAC6E,SAAS,CAACG,UAAU,CAAC,EAAE;QAC1FpB,WAAW,CAACqB,GAAG,CAACjF,GAAG,CAACkF,qBAAqB,CAACvB,MAAM,EAAEX,KAAK,EAAEwB,QAAQ,EAAE,IAAI,CAAC,CAAC;MAC7E;IACJ;IACAW,KAAKA,CAACC,qBAAqB,EAAE;MACzB,IAAI,IAAI,CAACpB,EAAE,EAAE;QACT;QACA;QACA,IAAIoB,qBAAqB,EAAE;UACvB,IAAIpF,GAAG,CAACiE,SAAS,CAAC,IAAI,CAACjC,UAAU,CAAC,KAAKhC,GAAG,CAACiE,SAAS,CAAC,IAAI,CAAC9B,aAAa,CAACkD,eAAe,CAAC,EAAE;YACtF,IAAI,CAACnB,UAAU,CAAC,IAAI,CAAC/B,aAAa,CAACkD,eAAe,CAAC;YACnD,IAAI,CAACjB,MAAM,CAAC,IAAI,CAACjC,aAAa,CAACmD,wBAAwB,EAAE,IAAI,CAACnD,aAAa,CAACoD,qBAAqB,CAAChB,YAAY,CAAC;UACnH;QACJ;QACA,OAAO,IAAI,CAACP,EAAE;MAClB;MACA,MAAMjC,SAAS,GAAG/B,GAAG,CAACwF,MAAM,CAAC,IAAI,CAACxD,UAAU,EAAEP,CAAC,CAAC,qCAAqC,CAAC,CAAC;MACvFM,SAAS,CAAC0D,QAAQ,GAAG,CAAC,CAAC;MACvB1D,SAAS,CAAC2D,KAAK,CAACC,OAAO,GAAG,MAAM;MAChC,MAAMC,UAAU,GAAG5F,GAAG,CAAC6F,gBAAgB,CAAC9D,SAAS,CAAC;MAClD,MAAM+D,QAAQ,GAAG9F,GAAG,CAACwF,MAAM,CAACzD,SAAS,EAAEN,CAAC,CAAC,uBAAuB,CAAC,CAAC;MAClE,MAAMsE,aAAa,GAAG,IAAI,CAACvD,SAAS,CAAC,IAAIvC,SAAS,CAAC6F,QAAQ,EAAE;QAAEE,aAAa,EAAE,IAAI,CAAC9D,OAAO,CAAC8D;MAAc,CAAC,CAAC,CAAC;MAC5GD,aAAa,CAACE,OAAO,CAACC,SAAS,CAACjB,GAAG,CAAC,6BAA6B,CAAC;MAClE,MAAMkB,KAAK,GAAGnG,GAAG,CAACwF,MAAM,CAACM,QAAQ,EAAErE,CAAC,CAAC,oBAAoB,CAAC,CAAC;MAC3D,MAAM2E,cAAc,GAAG,IAAI,CAAC5D,SAAS,CAAC,IAAIvC,SAAS,CAAC6F,QAAQ,EAAE;QAAEE,aAAa,EAAE,IAAI,CAAC9D,OAAO,CAAC8D;MAAc,CAAC,CAAC,CAAC;MAC7GI,cAAc,CAACH,OAAO,CAACC,SAAS,CAACjB,GAAG,CAAC,8BAA8B,CAAC;MACpE,MAAMoB,eAAe,GAAGrG,GAAG,CAACwF,MAAM,CAACzD,SAAS,EAAEN,CAAC,CAAC,qBAAqB,CAAC,CAAC;MACvE,MAAM6E,QAAQ,GAAGtG,GAAG,CAACwF,MAAM,CAACa,eAAe,EAAE5E,CAAC,CAAC,6BAA6B,CAAC,CAAC;MAC9E6E,QAAQ,CAACC,IAAI,GAAG,UAAU;MAC1BD,QAAQ,CAACE,YAAY,CAAC,YAAY,EAAE7F,QAAQ,CAAC,qBAAqB,EAAE,uBAAuB,CAAC,CAAC;MAC7F,IAAI,CAAC6B,SAAS,CAACxC,GAAG,CAACyG,6BAA6B,CAACH,QAAQ,EAAEtG,GAAG,CAAC6E,SAAS,CAAC6B,MAAM,EAAEjC,CAAC,IAAI;QAClF,MAAMkC,OAAO,GAAGL,QAAQ,CAACK,OAAO;QAChCC,IAAI,CAACC,oBAAoB,CAACF,OAAO,CAAC;MACtC,CAAC,CAAC,CAAC;MACH,IAAI,CAACnE,SAAS,CAACxC,GAAG,CAACkF,qBAAqB,CAACoB,QAAQ,EAAEtG,GAAG,CAAC6E,SAAS,CAACiC,KAAK,EAAErC,CAAC,IAAI;QACzE,IAAIA,CAAC,CAACsC,CAAC,IAAItC,CAAC,CAACuC,CAAC,EAAE;UAAE;UACdC,QAAQ,CAACC,QAAQ,CAAC,CAAC;QACvB;MACJ,CAAC,CAAC,CAAC;MACH,MAAMC,YAAY,GAAGnH,GAAG,CAACwF,MAAM,CAACa,eAAe,EAAE5E,CAAC,CAAC,0BAA0B,CAAC,CAAC;MAC/E,MAAM2F,cAAc,GAAGpH,GAAG,CAACwF,MAAM,CAACa,eAAe,EAAE5E,CAAC,CAAC,0BAA0B,CAAC,CAAC;MACjF,MAAM4F,eAAe,GAAGrH,GAAG,CAACwF,MAAM,CAAC4B,cAAc,EAAE3F,CAAC,CAAC,qBAAqB,CAAC,CAAC;MAC5E,MAAMwF,QAAQ,GAAG,IAAI,CAACzE,SAAS,CAAC,IAAI3B,aAAa,CAACwG,eAAe,EAAE,IAAI,CAAC7D,MAAM,CAACyD,QAAQ,EAAE,IAAI,CAACzD,MAAM,CAAC8D,MAAM,CAAC,CAAC;MAC7GL,QAAQ,CAACT,YAAY,CAAC,kBAAkB,EAAE,GAAG,IAAI,CAACjD,QAAQ,SAAS,CAAC;MACpE,MAAMgE,qBAAqB,GAAGvH,GAAG,CAACwF,MAAM,CAAC6B,eAAe,EAAE5F,CAAC,CAAC,4BAA4B,CAAC,CAAC;MAC1F8F,qBAAqB,CAACf,YAAY,CAAC,WAAW,EAAE,QAAQ,CAAC;MACzDe,qBAAqB,CAACf,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;MACzD,MAAMgB,YAAY,GAAG,IAAIrH,UAAU,CAACoH,qBAAqB,EAAE;QAAEE,WAAW,EAAE9G,QAAQ,CAAC;UAAE5B,GAAG,EAAE,yBAAyB;UAAE2I,OAAO,EAAE,CAAC,+JAA+J;QAAE,CAAC,EAAE,aAAa;MAAE,CAAC,EAAE,IAAI,CAAClE,MAAM,CAACmE,UAAU,CAAC;MAC5U,MAAMC,cAAc,GAAG5H,GAAG,CAACwF,MAAM,CAAC6B,eAAe,EAAE5F,CAAC,CAAC,oBAAoB,CAAC,CAAC;MAC3EmG,cAAc,CAACpB,YAAY,CAAC,WAAW,EAAE,QAAQ,CAAC;MAClD,MAAMqB,KAAK,GAAG,IAAI1H,UAAU,CAACyH,cAAc,EAAE;QAAEH,WAAW,EAAE9G,QAAQ,CAAC;UAAE5B,GAAG,EAAE,0BAA0B;UAAE2I,OAAO,EAAE,CAAC,+GAA+G;QAAE,CAAC,EAAE,cAAc;MAAE,CAAC,EAAE,IAAI,CAAClE,MAAM,CAACmE,UAAU,CAAC;MAChR,MAAMG,eAAe,GAAG,IAAI,CAACtF,SAAS,CAAC,IAAIvC,SAAS,CAACoG,eAAe,EAAE;QAAEL,aAAa,EAAE,IAAI,CAAC9D,OAAO,CAAC8D;MAAc,CAAC,CAAC,CAAC;MACrH8B,eAAe,CAAC7B,OAAO,CAACC,SAAS,CAACjB,GAAG,CAAC,+BAA+B,CAAC;MACtE,MAAM8C,WAAW,GAAG/H,GAAG,CAACwF,MAAM,CAACa,eAAe,EAAE5E,CAAC,CAAC,qBAAqB,CAAC,CAAC;MACzE,MAAMuG,EAAE,GAAG,IAAI,CAACxF,SAAS,CAAC,IAAItC,MAAM,CAAC6H,WAAW,EAAE,IAAI,CAACvE,MAAM,CAACyE,MAAM,CAAC,CAAC;MACtED,EAAE,CAACE,KAAK,GAAGvH,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC;MAC/B,IAAI,CAAC6B,SAAS,CAACwF,EAAE,CAACG,UAAU,CAAC1D,CAAC,IAAI;QAC9B,IAAI,CAAClC,kBAAkB,CAAC6F,IAAI,CAAC,CAAC;MAClC,CAAC,CAAC,CAAC;MACH,MAAMC,qBAAqB,GAAGrI,GAAG,CAACwF,MAAM,CAACa,eAAe,EAAE5E,CAAC,CAAC,qBAAqB,CAAC,CAAC;MACnF,MAAM6G,YAAY,GAAG,IAAI,CAAC9F,SAAS,CAAC,IAAItC,MAAM,CAACmI,qBAAqB,EAAE;QAAE,GAAG,IAAI,CAAC7E,MAAM,CAACyE,MAAM;QAAEM,YAAY,EAAE;MAAK,CAAC,CAAC,CAAC;MACrHD,YAAY,CAACJ,KAAK,GAAGvH,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC;MACjD,IAAI,CAAC6B,SAAS,CAAC8F,YAAY,CAACH,UAAU,CAAC1D,CAAC,IAAI;QACxC,IAAI,CAAChC,kBAAkB,CAAC2F,IAAI,CAAC,CAAC;MAClC,CAAC,CAAC,CAAC;MACH,MAAMI,OAAO,GAAGxI,GAAG,CAACwF,MAAM,CAAC4B,cAAc,EAAE3F,CAAC,CAAC,IAAI,IAAI,CAAC8B,QAAQ,6BAA6B,CAAC,CAAC;MAC7F,MAAMkF,WAAW,GAAG,IAAI,CAACjG,SAAS,CAAC,IAAIpC,WAAW,CAAC2B,SAAS,EAAE,IAAI,CAACyB,MAAM,CAACiF,WAAW,CAAC,CAAC;MACvFA,WAAW,CAACC,YAAY,CAAC,CAAC,CAACxC,SAAS,CAACjB,GAAG,CAAC,sBAAsB,CAAC;MAChE,MAAM0D,MAAM,GAAG3I,GAAG,CAACwF,MAAM,CAACzD,SAAS,EAAEN,CAAC,CAAC,0BAA0B,CAAC,CAAC;MACnEkH,MAAM,CAAClD,QAAQ,GAAG,CAAC,CAAC;MACpB,MAAMmD,YAAY,GAAG5I,GAAG,CAACwF,MAAM,CAACzD,SAAS,EAAEN,CAAC,CAAC,0BAA0B,CAAC,CAAC;MACzE,MAAMoH,MAAM,GAAG,IAAI,CAACtF,QAAQ,GAAG,MAAM;MACrC,MAAMqD,IAAI,GAAG,IAAI,CAACpE,SAAS,CAAC,IAAI,CAACJ,oBAAoB,CAAC0G,cAAc,CAACvH,cAAc,EAAEQ,SAAS,EAAE,IAAI,CAACG,OAAO,CAAC8D,aAAa,EAAE,IAAI,CAAC9D,OAAO,CAAC6G,kBAAkB,EAAEF,MAAM,CAAC,CAAC;MACrK5B,QAAQ,CAACT,YAAY,CAAC,eAAe,EAAEqC,MAAM,CAAC;MAC9C,IAAI,CAACrG,SAAS,CAACoE,IAAI,CAACoC,gBAAgB,CAAC,MAAM;QACvC/B,QAAQ,CAACT,YAAY,CAAC,uBAAuB,EAAEI,IAAI,CAACqC,mBAAmB,CAAC,CAAC,IAAI,EAAE,CAAC;MACpF,CAAC,CAAC,CAAC;MACH,IAAI,CAACzG,SAAS,CAACoE,IAAI,CAACsC,0BAA0B,CAACvC,OAAO,IAAI;QACtDL,QAAQ,CAACK,OAAO,GAAGA,OAAO;MAC9B,CAAC,CAAC,CAAC;MACH,IAAI,CAACnE,SAAS,CAACoE,IAAI,CAACuC,qBAAqB,CAAClK,CAAC,IAAI;QAC3CuI,YAAY,CAAC4B,QAAQ,CAACnK,CAAC,CAAC;MAC5B,CAAC,CAAC,CAAC;MACH,IAAI,CAACuD,SAAS,CAACoE,IAAI,CAACyC,qBAAqB,CAACpK,CAAC,IAAI;QAC3C4I,KAAK,CAACuB,QAAQ,CAACnK,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC;MACH,IAAI,CAACuD,SAAS,CAACoE,IAAI,CAAC0C,OAAO,CAAC,MAAM;QAC9B;QACA;QACAC,UAAU,CAAC,MAAM;UACb,IAAI,CAAC,IAAI,CAAC1H,UAAU,EAAE;YAClB;UACJ;UACAoF,QAAQ,CAACC,QAAQ,CAAC,CAAC;UACnB,IAAI,IAAI,CAACrF,UAAU,YAAYf,SAAS,IAAI,IAAI,CAACe,UAAU,CAAC2H,aAAa,EAAE;YACvE5C,IAAI,CAAC6C,UAAU,CAAC,CAAC;UACrB;QACJ,CAAC,EAAE,CAAC,CAAC;MACT,CAAC,CAAC,CAAC;MACH,MAAMC,YAAY,GAAG1J,GAAG,CAAC2J,UAAU,CAAC5H,SAAS,CAAC;MAC9C,IAAI,CAACS,SAAS,CAACkH,YAAY,CAAC;MAC5B,IAAI,CAAClH,SAAS,CAACxC,GAAG,CAACkF,qBAAqB,CAACnD,SAAS,EAAE/B,GAAG,CAAC6E,SAAS,CAAC+E,KAAK,EAAEnF,CAAC,IAAI;QAC1E,MAAMT,EAAE,GAAG,IAAI,CAACmB,KAAK,CAAC,CAAC;QACvB,IAAInF,GAAG,CAAC6J,UAAU,CAACpF,CAAC,CAACqF,aAAa,EAAE9F,EAAE,CAACoD,cAAc,CAAC,EAAE;UACpD,MAAM2C,KAAK,GAAG/F,EAAE,CAACiD,QAAQ,CAAC+C,gBAAgB,CAAC,CAAC;UAC5C,IAAI,IAAI,CAAC1G,yBAAyB,CAAC2G,GAAG,CAAC,CAAC,KAAKF,KAAK,EAAE;YAChD,IAAI,CAACzG,yBAAyB,CAAC4G,GAAG,CAACH,KAAK,CAAC;UAC7C;QACJ;QACA;QACA,IAAI/J,GAAG,CAAC6J,UAAU,CAACpF,CAAC,CAACqF,aAAa,EAAE9F,EAAE,CAACjC,SAAS,CAAC,EAAE;UAC/C;QACJ;QACA,IAAI,CAACoB,mBAAmB,CAAC+G,GAAG,CAAC,IAAI,CAAC;QAClC,IAAI,CAACC,oBAAoB,GAAGnK,GAAG,CAACoK,aAAa,CAAC3F,CAAC,CAACqF,aAAa,CAAC,GAAGrF,CAAC,CAACqF,aAAa,GAAGhI,SAAS;MAChG,CAAC,EAAE,IAAI,CAAC,CAAC;MACT,IAAI,CAACU,SAAS,CAACkH,YAAY,CAACW,SAAS,CAAC,MAAM;QACxC,IAAI,CAAC,IAAI,CAAClF,KAAK,CAAC,CAAC,CAACmF,cAAc,IAAI,CAAC,IAAI,CAACpI,OAAO,CAACoI,cAAc,CAAC,CAAC,EAAE;UAChE,IAAI,CAACC,IAAI,CAAC3J,oBAAoB,CAAC4J,IAAI,CAAC;QACxC;QACA,IAAI,CAACrH,mBAAmB,CAAC+G,GAAG,CAAC,KAAK,CAAC;QACnC,IAAI,CAAC5G,yBAAyB,CAAC4G,GAAG,CAAC,KAAK,CAAC;QACzC,IAAI,CAACC,oBAAoB,GAAGrI,SAAS;MACzC,CAAC,CAAC,CAAC;MACH,IAAI,CAACU,SAAS,CAACyE,QAAQ,CAACwD,SAAS,CAACC,CAAC,IAAI;QACnC,MAAMX,KAAK,GAAG,IAAI,CAAC5E,KAAK,CAAC,CAAC,CAAC8B,QAAQ,CAAC+C,gBAAgB,CAAC,CAAC;QACtD,IAAI,IAAI,CAAC1G,yBAAyB,CAAC2G,GAAG,CAAC,CAAC,KAAKF,KAAK,EAAE;UAChD,IAAI,CAACzG,yBAAyB,CAAC4G,GAAG,CAACH,KAAK,CAAC;QAC7C;MACJ,CAAC,CAAC,CAAC;MACH,IAAI,CAACvH,SAAS,CAACxC,GAAG,CAACkF,qBAAqB,CAACnD,SAAS,EAAE/B,GAAG,CAAC6E,SAAS,CAAC+E,KAAK,EAAGnF,CAAC,IAAK;QAC5EwC,QAAQ,CAACC,QAAQ,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC;MACH;MACA;MACA,IAAI,CAAC1E,SAAS,CAACxC,GAAG,CAACyG,6BAA6B,CAAC1E,SAAS,EAAE/B,GAAG,CAAC6E,SAAS,CAACC,QAAQ,EAAG9B,KAAK,IAAK;QAC3F,IAAIhD,GAAG,CAAC6J,UAAU,CAAC7G,KAAK,CAAClE,MAAM,EAAE6J,MAAM,CAAC,EAAE;UACtC,OAAO,CAAC;QACZ;QACA,QAAQ3F,KAAK,CAAC2H,OAAO;UACjB,KAAK,CAAC,CAAC;YACH3K,GAAG,CAAC4K,WAAW,CAACC,IAAI,CAAC7H,KAAK,EAAE,IAAI,CAAC;YACjC,IAAI,IAAI,CAACV,OAAO,EAAE;cACd,IAAI,CAACC,kBAAkB,CAAC6F,IAAI,CAAC,CAAC;YAClC;YACA;UACJ,KAAK,CAAC,CAAC;YACHpI,GAAG,CAAC4K,WAAW,CAACC,IAAI,CAAC7H,KAAK,EAAE,IAAI,CAAC;YACjC,IAAI,CAACuH,IAAI,CAAC3J,oBAAoB,CAACkK,OAAO,CAAC;YACvC;UACJ,KAAK,CAAC,CAAC;YACH,IAAI,CAAC9H,KAAK,CAAC4B,MAAM,IAAI,CAAC5B,KAAK,CAAC0B,OAAO,IAAI,CAAC1B,KAAK,CAAC2B,OAAO,EAAE;cACnD;cACA,MAAMoG,SAAS,GAAG,CACd,sDAAsD,EACtD,kDAAkD,EAClD,6CAA6C,CAChD;cACD,IAAIhJ,SAAS,CAACmE,SAAS,CAAC8E,QAAQ,CAAC,iBAAiB,CAAC,EAAE;gBACjDD,SAAS,CAACE,IAAI,CAAC,OAAO,CAAC;cAC3B,CAAC,MACI;gBACDF,SAAS,CAACE,IAAI,CAAC,kBAAkB,CAAC;cACtC;cACA,IAAI,IAAI,CAAC9F,KAAK,CAAC,CAAC,CAACyB,IAAI,CAACsE,SAAS,EAAE;gBAC7BH,SAAS,CAACE,IAAI,CAAC,cAAc,CAAC;cAClC;cACA;cACA,IAAI,IAAI,CAAC9F,KAAK,CAAC,CAAC,CAACqD,OAAO,EAAE;gBACtBuC,SAAS,CAACE,IAAI,CAAC,wBAAwB,CAAC;cAC5C;cACA,IAAI,IAAI,CAAC9F,KAAK,CAAC,CAAC,CAACwD,MAAM,EAAE;gBACrB,IAAI3I,GAAG,CAAC6J,UAAU,CAAC7G,KAAK,CAAClE,MAAM,EAAE,IAAI,CAACqG,KAAK,CAAC,CAAC,CAACwD,MAAM,CAAC,EAAE;kBACnD;kBACA;gBACJ;gBACAoC,SAAS,CAACE,IAAI,CAAC,0BAA0B,CAAC;cAC9C;cACA,MAAME,KAAK,GAAGpJ,SAAS,CAACqJ,gBAAgB,CAACL,SAAS,CAACM,IAAI,CAAC,IAAI,CAAC,CAAC;cAC9D,IAAIrI,KAAK,CAACsI,QAAQ,IAAItI,KAAK,CAAClE,MAAM,KAAKqM,KAAK,CAAC,CAAC,CAAC,EAAE;gBAC7C;gBACA;gBACAnL,GAAG,CAAC4K,WAAW,CAACC,IAAI,CAAC7H,KAAK,EAAE,IAAI,CAAC;gBACjC4D,IAAI,CAAC6C,UAAU,CAAC,CAAC;cACrB,CAAC,MACI,IAAI,CAACzG,KAAK,CAACsI,QAAQ,IAAItL,GAAG,CAAC6J,UAAU,CAAC7G,KAAK,CAAClE,MAAM,EAAEqM,KAAK,CAACA,KAAK,CAAChM,MAAM,GAAG,CAAC,CAAC,CAAC,EAAE;gBAC/Ea,GAAG,CAAC4K,WAAW,CAACC,IAAI,CAAC7H,KAAK,EAAE,IAAI,CAAC;gBACjCmI,KAAK,CAAC,CAAC,CAAC,CAACI,KAAK,CAAC,CAAC;cACpB;YACJ;YACA;UACJ,KAAK,EAAE,CAAC;YACJ,IAAIvI,KAAK,CAAC0B,OAAO,EAAE;cACf1E,GAAG,CAAC4K,WAAW,CAACC,IAAI,CAAC7H,KAAK,EAAE,IAAI,CAAC;cACjC,IAAI,CAACmC,KAAK,CAAC,CAAC,CAACyB,IAAI,CAAC4E,WAAW,CAAC,CAAC;YACnC;YACA;QACR;MACJ,CAAC,CAAC,CAAC;MACH,IAAI,CAACxH,EAAE,GAAG;QACNjC,SAAS;QACT6D,UAAU;QACVG,aAAa;QACbD,QAAQ;QACRK,KAAK;QACLyC,YAAY;QACZzB,YAAY;QACZwB,MAAM;QACNvC,cAAc;QACd0B,eAAe;QACfxB,QAAQ;QACRc,cAAc;QACdC,eAAe;QACfJ,QAAQ;QACRM,qBAAqB;QACrBC,YAAY;QACZI,cAAc;QACdC,KAAK;QACLE,WAAW;QACXC,EAAE;QACFQ,OAAO;QACPH,qBAAqB;QACrBC,YAAY;QACZ1B,IAAI;QACJ6B,WAAW;QACXgD,WAAW,EAAE,IAAI,CAAClJ,kBAAkB,CAACS,KAAK;QAC1C0I,WAAW,EAAE,IAAI,CAACjJ,kBAAkB,CAACO,KAAK;QAC1C2I,kBAAkB,EAAE,IAAI,CAACjJ,yBAAyB,CAACM,KAAK;QACxDsH,cAAc,EAAE,KAAK;QACrB3H,OAAO,EAAE,IAAI,CAACA,OAAO;QACrBiJ,IAAI,EAAE/J,UAAU,IAAI,IAAI,CAAC+J,IAAI,CAAC/J,UAAU,CAAC;QACzC0I,IAAI,EAAEA,CAAA,KAAM,IAAI,CAACA,IAAI,CAAC,CAAC;QACvBsB,eAAe,EAAEC,YAAY,IAAI,IAAI,CAACD,eAAe,CAACC,YAAY,CAAC;QACnEC,UAAU,EAAEzJ,OAAO,IAAI,IAAI,CAACyJ,UAAU,CAACzJ,OAAO,CAAC;QAC/C0J,aAAa,EAAEC,UAAU,IAAI,IAAI,CAAC/J,OAAO,CAAC8J,aAAa,CAACC,UAAU,CAAC;QACnElD,kBAAkB,EAAEmD,OAAO,IAAI,IAAI,CAAChK,OAAO,CAAC6G,kBAAkB,CAACmD,OAAO;MAC1E,CAAC;MACD,IAAI,CAACC,YAAY,CAAC,CAAC;MACnB,OAAO,IAAI,CAACnI,EAAE;IAClB;IACAE,UAAUA,CAACnC,SAAS,EAAE;MAClB,IAAI,IAAI,CAACiC,EAAE,EAAE;QACT,IAAI,CAAChC,UAAU,GAAGD,SAAS;QAC3B/B,GAAG,CAACwF,MAAM,CAAC,IAAI,CAACxD,UAAU,EAAE,IAAI,CAACgC,EAAE,CAACjC,SAAS,CAAC;MAClD;IACJ;IACAqK,IAAIA,CAACC,KAAK,EAAEnK,OAAO,GAAG,CAAC,CAAC,EAAEoK,KAAK,GAAGjM,iBAAiB,CAACkM,IAAI,EAAE;MACtD,OAAO,IAAIC,OAAO,CAAC,CAACC,SAAS,EAAEC,MAAM,KAAK;QACtC,IAAIC,OAAO,GAAIC,MAAM,IAAK;UACtBD,OAAO,GAAGF,SAAS;UACnBvK,OAAO,CAAC2K,SAAS,GAAGC,KAAK,CAACnK,OAAO,CAAC;UAClC8J,SAAS,CAACG,MAAM,CAAC;QACrB,CAAC;QACD,IAAIN,KAAK,CAACS,uBAAuB,EAAE;UAC/BJ,OAAO,CAAC7K,SAAS,CAAC;UAClB;QACJ;QACA,MAAMgL,KAAK,GAAG,IAAI,CAACE,eAAe,CAAC;UAAEC,aAAa,EAAE;QAAK,CAAC,CAAC;QAC3D,IAAIC,UAAU;QACd,MAAMtJ,WAAW,GAAG,CAChBkJ,KAAK,EACLA,KAAK,CAACrB,WAAW,CAAC,MAAM;UACpB,IAAIqB,KAAK,CAACtD,aAAa,EAAE;YACrBmD,OAAO,CAACG,KAAK,CAACK,aAAa,CAACC,KAAK,CAAC,CAAC,CAAC;YACpCN,KAAK,CAACvC,IAAI,CAAC,CAAC;UAChB,CAAC,MACI;YACD,MAAMqC,MAAM,GAAGE,KAAK,CAACO,WAAW,CAAC,CAAC,CAAC;YACnC,IAAIT,MAAM,EAAE;cACRD,OAAO,CAACC,MAAM,CAAC;cACfE,KAAK,CAACvC,IAAI,CAAC,CAAC;YAChB;UACJ;QACJ,CAAC,CAAC,EACFuC,KAAK,CAACQ,iBAAiB,CAACC,KAAK,IAAI;UAC7B,MAAMC,OAAO,GAAGD,KAAK,CAAC,CAAC,CAAC;UACxB,IAAIC,OAAO,IAAItL,OAAO,CAACuL,UAAU,EAAE;YAC/BvL,OAAO,CAACuL,UAAU,CAACD,OAAO,CAAC;UAC/B;QACJ,CAAC,CAAC,EACFV,KAAK,CAACY,oBAAoB,CAACH,KAAK,IAAI;UAChC,IAAI,CAACT,KAAK,CAACtD,aAAa,EAAE;YACtB,MAAMoD,MAAM,GAAGW,KAAK,CAAC,CAAC,CAAC;YACvB,IAAIX,MAAM,EAAE;cACRD,OAAO,CAACC,MAAM,CAAC;cACfE,KAAK,CAACvC,IAAI,CAAC,CAAC;YAChB;UACJ;QACJ,CAAC,CAAC,EACFuC,KAAK,CAACa,sBAAsB,CAAC3K,KAAK,IAAId,OAAO,CAACyL,sBAAsB,IAAIzL,OAAO,CAACyL,sBAAsB,CAAC;UACnG,GAAG3K,KAAK;UACR4K,UAAU,EAAEA,CAAA,KAAM;YACd,MAAMC,KAAK,GAAGf,KAAK,CAACS,KAAK,CAACO,OAAO,CAAC9K,KAAK,CAAC+K,IAAI,CAAC;YAC7C,IAAIF,KAAK,KAAK,CAAC,CAAC,EAAE;cACd,MAAMN,KAAK,GAAGT,KAAK,CAACS,KAAK,CAACH,KAAK,CAAC,CAAC;cACjC,MAAMY,OAAO,GAAGT,KAAK,CAACU,MAAM,CAACJ,KAAK,EAAE,CAAC,CAAC;cACtC,MAAMR,WAAW,GAAGP,KAAK,CAACO,WAAW,CAACa,MAAM,CAAChB,UAAU,IAAIA,UAAU,KAAKc,OAAO,CAAC,CAAC,CAAC,CAAC;cACrF,MAAMG,wBAAwB,GAAGrB,KAAK,CAACsB,kBAAkB;cACzDtB,KAAK,CAACsB,kBAAkB,GAAG,IAAI;cAC/BtB,KAAK,CAACS,KAAK,GAAGA,KAAK;cACnB,IAAIF,WAAW,EAAE;gBACbP,KAAK,CAACO,WAAW,GAAGA,WAAW;cACnC;cACAP,KAAK,CAACsB,kBAAkB,GAAGD,wBAAwB;YACvD;UACJ;QACJ,CAAC,CAAC,CAAC,EACHrB,KAAK,CAACuB,2BAA2B,CAACrL,KAAK,IAAId,OAAO,CAACmM,2BAA2B,GAAGrL,KAAK,CAAC,CAAC,EACxF8J,KAAK,CAACwB,gBAAgB,CAACvE,KAAK,IAAI;UAC5B,IAAImD,UAAU,IAAI,CAACnD,KAAK,KAAK+C,KAAK,CAACO,WAAW,CAAClO,MAAM,KAAK,CAAC,IAAI2N,KAAK,CAACO,WAAW,CAAC,CAAC,CAAC,KAAKH,UAAU,CAAC,EAAE;YACjGJ,KAAK,CAACO,WAAW,GAAG,CAACH,UAAU,CAAC;UACpC;QACJ,CAAC,CAAC,EACFZ,KAAK,CAACiC,uBAAuB,CAAC,MAAM;UAChCzB,KAAK,CAACvC,IAAI,CAAC,CAAC;QAChB,CAAC,CAAC,EACFuC,KAAK,CAAC0B,SAAS,CAAC,MAAM;UAClB/N,OAAO,CAACmD,WAAW,CAAC;UACpB+I,OAAO,CAAC7K,SAAS,CAAC;QACtB,CAAC,CAAC,CACL;QACDgL,KAAK,CAAC3G,KAAK,GAAGjE,OAAO,CAACiE,KAAK;QAC3B,IAAIjE,OAAO,CAAC6H,KAAK,EAAE;UACf+C,KAAK,CAAC/C,KAAK,GAAG7H,OAAO,CAAC6H,KAAK;QAC/B;QACA+C,KAAK,CAACtD,aAAa,GAAG,CAAC,CAACtH,OAAO,CAACuM,WAAW;QAC3C3B,KAAK,CAAC4B,WAAW,GAAGxM,OAAO,CAACyM,WAAW;QACvC7B,KAAK,CAACxC,cAAc,GAAG,CAAC,CAACpI,OAAO,CAAC0M,eAAe;QAChD9B,KAAK,CAAC+B,kBAAkB,GAAG,CAAC,CAAC3M,OAAO,CAAC2M,kBAAkB;QACvD/B,KAAK,CAACgC,aAAa,GAAG,CAAC,CAAC5M,OAAO,CAAC4M,aAAa;QAC7ChC,KAAK,CAACiC,YAAY,GAAI7M,OAAO,CAAC6M,YAAY,KAAKjN,SAAS,IAAKI,OAAO,CAAC6M,YAAY,CAAC,CAAC;QACnFjC,KAAK,CAACkC,aAAa,GAAG9M,OAAO,CAAC8M,aAAa;QAC3ClC,KAAK,CAACmC,SAAS,GAAG,CAAC,CAAC/M,OAAO,CAAC+M,SAAS;QACrCnC,KAAK,CAACb,UAAU,GAAG/J,OAAO,CAAC+J,UAAU;QACrCa,KAAK,CAACoC,IAAI,GAAG,IAAI;QACjB1C,OAAO,CAAC2C,GAAG,CAAC,CAAC9C,KAAK,EAAEnK,OAAO,CAACgL,UAAU,CAAC,CAAC,CACnCkC,IAAI,CAAC,CAAC,CAAC7B,KAAK,EAAE8B,WAAW,CAAC,KAAK;UAChCnC,UAAU,GAAGmC,WAAW;UACxBvC,KAAK,CAACoC,IAAI,GAAG,KAAK;UAClBpC,KAAK,CAACS,KAAK,GAAGA,KAAK;UACnB,IAAIT,KAAK,CAACtD,aAAa,EAAE;YACrBsD,KAAK,CAACK,aAAa,GAAGI,KAAK,CAACW,MAAM,CAACH,IAAI,IAAIA,IAAI,CAACxH,IAAI,KAAK,WAAW,IAAIwH,IAAI,CAACuB,MAAM,CAAC;UACxF;UACA,IAAIpC,UAAU,EAAE;YACZJ,KAAK,CAACO,WAAW,GAAG,CAACH,UAAU,CAAC;UACpC;QACJ,CAAC,CAAC;QACFJ,KAAK,CAAClB,IAAI,CAAC,CAAC;QACZY,OAAO,CAACG,OAAO,CAACN,KAAK,CAAC,CAAC+C,IAAI,CAACtN,SAAS,EAAEyN,GAAG,IAAI;UAC1C7C,MAAM,CAAC6C,GAAG,CAAC;UACXzC,KAAK,CAACvC,IAAI,CAAC,CAAC;QAChB,CAAC,CAAC;MACN,CAAC,CAAC;IACN;IACAyC,eAAeA,CAAC9K,OAAO,GAAG;MAAE+K,aAAa,EAAE;IAAM,CAAC,EAAE;MAChD,MAAMjJ,EAAE,GAAG,IAAI,CAACmB,KAAK,CAAC,IAAI,CAAC;MAC3B,OAAO,IAAIrE,SAAS,CAACkD,EAAE,CAAC;IAC5B;IACAwL,cAAcA,CAAA,EAAG;MACb,MAAMxL,EAAE,GAAG,IAAI,CAACmB,KAAK,CAAC,IAAI,CAAC;MAC3B,OAAO,IAAInE,QAAQ,CAACgD,EAAE,CAAC;IAC3B;IACA4H,IAAIA,CAAC/J,UAAU,EAAE;MACb,MAAMmC,EAAE,GAAG,IAAI,CAACmB,KAAK,CAAC,IAAI,CAAC;MAC3B,IAAI,CAACrC,aAAa,CAACsF,IAAI,CAAC,CAAC;MACzB,MAAMqH,aAAa,GAAG,IAAI,CAAC5N,UAAU;MACrC,IAAI,CAACA,UAAU,GAAGA,UAAU;MAC5B4N,aAAa,EAAEC,OAAO,CAAC,CAAC;MACxB,IAAI,CAAC3D,UAAU,CAAC,IAAI,CAAC;MACrB/H,EAAE,CAAC+B,aAAa,CAAC4J,KAAK,CAAC,CAAC;MACxB3L,EAAE,CAACmC,KAAK,CAACyJ,WAAW,GAAG,EAAE;MACzB5L,EAAE,CAAC4E,YAAY,CAACgH,WAAW,GAAG,EAAE;MAChC5L,EAAE,CAACmD,YAAY,CAACyI,WAAW,GAAG,EAAE;MAChC5P,GAAG,CAAC6P,KAAK,CAAC7L,EAAE,CAAC2E,MAAM,CAAC;MACpB3E,EAAE,CAACoC,cAAc,CAACuJ,KAAK,CAAC,CAAC;MACzB3L,EAAE,CAAC8D,eAAe,CAAC6H,KAAK,CAAC,CAAC;MAC1B3L,EAAE,CAACsC,QAAQ,CAACK,OAAO,GAAG,KAAK;MAC3B;MACA3C,EAAE,CAACiD,QAAQ,CAACyH,WAAW,GAAG,EAAE;MAC5B1K,EAAE,CAACiD,QAAQ,CAAC6I,QAAQ,GAAG,KAAK;MAC5B9L,EAAE,CAACiD,QAAQ,CAAC8I,cAAc,CAACrP,QAAQ,CAACsP,MAAM,CAAC;MAC3ChM,EAAE,CAACwD,YAAY,CAAC4B,QAAQ,CAAC,CAAC,CAAC;MAC3BpF,EAAE,CAAC6D,KAAK,CAACuB,QAAQ,CAAC,CAAC,CAAC;MACpBpJ,GAAG,CAAC6P,KAAK,CAAC7L,EAAE,CAACwE,OAAO,CAAC;MACrBxE,EAAE,CAACyE,WAAW,CAACoC,IAAI,CAAC,CAAC;MACrB7G,EAAE,CAAC4C,IAAI,CAACqJ,WAAW,CAAC,EAAE,CAAC;MACvBjM,EAAE,CAAC4C,IAAI,CAACiI,kBAAkB,GAAG,KAAK;MAClC7K,EAAE,CAAC4C,IAAI,CAACkI,aAAa,GAAG,KAAK;MAC7B9K,EAAE,CAAC4C,IAAI,CAACmI,YAAY,GAAG,IAAI;MAC3B/K,EAAE,CAAC4C,IAAI,CAACsJ,WAAW,GAAG,IAAI;MAC1BlM,EAAE,CAACsG,cAAc,GAAG,KAAK;MACzBtG,EAAE,CAACiD,QAAQ,CAACkJ,OAAO,GAAGrO,SAAS;MAC/B,MAAMsO,mBAAmB,GAAG,IAAI,CAAClO,OAAO,CAACkO,mBAAmB,CAAC,CAAC;MAC9DrP,UAAU,CAACsP,OAAO,GAAGD,mBAAmB,GAAGzP,QAAQ,CAAC,+BAA+B,EAAE,YAAY,EAAEyP,mBAAmB,CAAC,GAAGzP,QAAQ,CAAC,iBAAiB,EAAE,MAAM,CAAC;MAC7JqD,EAAE,CAACjC,SAAS,CAAC2D,KAAK,CAACC,OAAO,GAAG,EAAE;MAC/B,IAAI,CAAC2K,YAAY,CAAC,CAAC;MACnBtM,EAAE,CAACiD,QAAQ,CAACC,QAAQ,CAAC,CAAC;MACtB,IAAI,CAAC7D,qBAAqB,CAAC6G,GAAG,CAACrI,UAAU,CAAC0E,IAAI,CAAC;IACnD;IACAgK,SAASA,CAAA,EAAG;MACR,OAAO,CAAC,CAAC,IAAI,CAACvM,EAAE,IAAI,IAAI,CAACA,EAAE,CAACjC,SAAS,CAAC2D,KAAK,CAACC,OAAO,KAAK,MAAM;IAClE;IACAkG,eAAeA,CAACC,YAAY,EAAE;MAC1B,MAAM9H,EAAE,GAAG,IAAI,CAACmB,KAAK,CAAC,CAAC;MACvBnB,EAAE,CAACmC,KAAK,CAACT,KAAK,CAACC,OAAO,GAAGmG,YAAY,CAAC3F,KAAK,GAAG,EAAE,GAAG,MAAM;MACzDnC,EAAE,CAAC4E,YAAY,CAAClD,KAAK,CAACC,OAAO,GAAGmG,YAAY,CAAC0E,WAAW,KAAK1E,YAAY,CAAC7E,QAAQ,IAAI6E,YAAY,CAACxF,QAAQ,CAAC,GAAG,EAAE,GAAG,MAAM;MAC1HtC,EAAE,CAACmD,YAAY,CAACzB,KAAK,CAACC,OAAO,GAAGmG,YAAY,CAAC0E,WAAW,IAAI,EAAE1E,YAAY,CAAC7E,QAAQ,IAAI6E,YAAY,CAACxF,QAAQ,CAAC,GAAG,EAAE,GAAG,MAAM;MAC3HtC,EAAE,CAACsC,QAAQ,CAACZ,KAAK,CAACC,OAAO,GAAGmG,YAAY,CAACxF,QAAQ,GAAG,EAAE,GAAG,MAAM;MAC/DtC,EAAE,CAACoD,cAAc,CAAC1B,KAAK,CAACC,OAAO,GAAGmG,YAAY,CAAC7E,QAAQ,GAAG,EAAE,GAAG,MAAM;MACrEjD,EAAE,CAACqD,eAAe,CAAC3B,KAAK,CAACC,OAAO,GAAGmG,YAAY,CAAC7E,QAAQ,GAAG,EAAE,GAAG,MAAM;MACtEjD,EAAE,CAACuD,qBAAqB,CAAC7B,KAAK,CAACC,OAAO,GAAGmG,YAAY,CAACtE,YAAY,GAAG,EAAE,GAAG,MAAM;MAChFxD,EAAE,CAAC4D,cAAc,CAAClC,KAAK,CAACC,OAAO,GAAGmG,YAAY,CAACjE,KAAK,GAAG,EAAE,GAAG,MAAM;MAClE7D,EAAE,CAAC+D,WAAW,CAACrC,KAAK,CAACC,OAAO,GAAGmG,YAAY,CAAC9D,EAAE,GAAG,EAAE,GAAG,MAAM;MAC5DhE,EAAE,CAACqE,qBAAqB,CAAC3C,KAAK,CAACC,OAAO,GAAGmG,YAAY,CAACxD,YAAY,GAAG,EAAE,GAAG,MAAM;MAChFtE,EAAE,CAACwE,OAAO,CAAC9C,KAAK,CAACC,OAAO,GAAGmG,YAAY,CAACtD,OAAO,GAAG,EAAE,GAAG,MAAM;MAC7DxE,EAAE,CAACyE,WAAW,CAACC,YAAY,CAAC,CAAC,CAAChD,KAAK,CAACC,OAAO,GAAGmG,YAAY,CAACrD,WAAW,GAAG,EAAE,GAAG,MAAM;MACpFzE,EAAE,CAAC4C,IAAI,CAACsE,SAAS,GAAG,CAAC,CAACY,YAAY,CAAClF,IAAI;MACvC5C,EAAE,CAACjC,SAAS,CAACmE,SAAS,CAACoB,MAAM,CAAC,iBAAiB,EAAE,CAAC,CAACwE,YAAY,CAAC2E,QAAQ,CAAC;MACzEzM,EAAE,CAACjC,SAAS,CAACmE,SAAS,CAACoB,MAAM,CAAC,cAAc,EAAE,CAACwE,YAAY,CAAC7E,QAAQ,IAAI,CAAC6E,YAAY,CAAC0E,WAAW,CAAC;MAClG,IAAI,CAACF,YAAY,CAAC,CAAC,CAAC,CAAC;IACzB;IACAvE,UAAUA,CAACzJ,OAAO,EAAE;MAChB,IAAIA,OAAO,KAAK,IAAI,CAACA,OAAO,EAAE;QAC1B,IAAI,CAACA,OAAO,GAAGA,OAAO;QACtB,KAAK,MAAMyL,IAAI,IAAI,IAAI,CAAC5I,KAAK,CAAC,CAAC,CAACY,aAAa,CAAC2K,SAAS,EAAE;UACrD3C,IAAI,CAAC4C,MAAM,CAACrO,OAAO,GAAGA,OAAO;QACjC;QACA,KAAK,MAAMyL,IAAI,IAAI,IAAI,CAAC5I,KAAK,CAAC,CAAC,CAACiB,cAAc,CAACsK,SAAS,EAAE;UACtD3C,IAAI,CAAC4C,MAAM,CAACrO,OAAO,GAAGA,OAAO;QACjC;QACA,IAAI,CAAC6C,KAAK,CAAC,CAAC,CAACmB,QAAQ,CAACsK,QAAQ,GAAG,CAACtO,OAAO;QACzC,IAAI,CAAC6C,KAAK,CAAC,CAAC,CAAC8B,QAAQ,CAAC3E,OAAO,GAAGA,OAAO;QACvC,IAAI,CAAC6C,KAAK,CAAC,CAAC,CAAC6C,EAAE,CAAC1F,OAAO,GAAGA,OAAO;QACjC,IAAI,CAAC6C,KAAK,CAAC,CAAC,CAACyB,IAAI,CAACtE,OAAO,GAAGA,OAAO;MACvC;IACJ;IACAiI,IAAIA,CAACsG,MAAM,EAAE;MACT,MAAMhP,UAAU,GAAG,IAAI,CAACA,UAAU;MAClC,IAAI,CAACA,UAAU,EAAE;QACb;MACJ;MACAA,UAAU,CAACiP,QAAQ,CAACD,MAAM,CAAC;MAC3B,MAAM9O,SAAS,GAAG,IAAI,CAACiC,EAAE,EAAEjC,SAAS;MACpC,MAAMgP,YAAY,GAAGhP,SAAS,IAAI,CAAC/B,GAAG,CAACgR,yBAAyB,CAACjP,SAAS,CAAC;MAC3E,IAAI,CAACF,UAAU,GAAG,IAAI;MACtB,IAAI,CAACoB,aAAa,CAACmF,IAAI,CAAC,CAAC;MACzB,IAAIrG,SAAS,EAAE;QACXA,SAAS,CAAC2D,KAAK,CAACC,OAAO,GAAG,MAAM;MACpC;MACA,IAAI,CAACoL,YAAY,EAAE;QACf,IAAIE,cAAc,GAAG,IAAI,CAAC9G,oBAAoB;QAC9C,OAAO8G,cAAc,IAAI,CAACA,cAAc,CAACC,YAAY,EAAE;UACnDD,cAAc,GAAGA,cAAc,CAACE,aAAa,IAAIrP,SAAS;QAC9D;QACA,IAAImP,cAAc,EAAEC,YAAY,EAAE;UAC9BD,cAAc,CAAC1F,KAAK,CAAC,CAAC;UACtB,IAAI,CAACpB,oBAAoB,GAAGrI,SAAS;QACzC,CAAC,MACI;UACD,IAAI,CAACI,OAAO,CAACkP,WAAW,CAAC,CAAC;QAC9B;MACJ;MACAvP,UAAU,CAAC6N,OAAO,CAACmB,MAAM,CAAC;IAC9B;IACAzM,MAAMA,CAACiN,SAAS,EAAEC,cAAc,EAAE;MAC9B,IAAI,CAACD,SAAS,GAAGA,SAAS;MAC1B,IAAI,CAACC,cAAc,GAAGA,cAAc;MACpC,IAAI,CAAChB,YAAY,CAAC,CAAC;IACvB;IACAA,YAAYA,CAAA,EAAG;MACX,IAAI,IAAI,CAACtM,EAAE,IAAI,IAAI,CAACuM,SAAS,CAAC,CAAC,EAAE;QAC7B,IAAI,CAACvM,EAAE,CAACjC,SAAS,CAAC2D,KAAK,CAAC6L,GAAG,GAAG,GAAG,IAAI,CAACD,cAAc,IAAI;QACxD,MAAM5L,KAAK,GAAG,IAAI,CAAC1B,EAAE,CAACjC,SAAS,CAAC2D,KAAK;QACrC,MAAM8L,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAC,IAAI,CAACL,SAAS,CAACG,KAAK,GAAG,IAAI,CAAC,kBAAkBzR,sBAAsB,CAAC4B,SAAS,CAAC;QACtG+D,KAAK,CAAC8L,KAAK,GAAGA,KAAK,GAAG,IAAI;QAC1B9L,KAAK,CAACiM,UAAU,GAAG,GAAG,GAAIH,KAAK,GAAG,CAAE,GAAG,IAAI;QAC3C,IAAI,CAACxN,EAAE,CAACiD,QAAQ,CAAC7C,MAAM,CAAC,CAAC;QACzB,IAAI,CAACJ,EAAE,CAAC4C,IAAI,CAACxC,MAAM,CAAC,IAAI,CAACiN,SAAS,IAAI,IAAI,CAACA,SAAS,CAACO,MAAM,GAAG,GAAG,CAAC;MACtE;IACJ;IACAC,WAAWA,CAACrO,MAAM,EAAE;MAChB,IAAI,CAACA,MAAM,GAAGA,MAAM;MACpB,IAAI,CAAC2I,YAAY,CAAC,CAAC;IACvB;IACAA,YAAYA,CAAA,EAAG;MACX,IAAI,IAAI,CAACnI,EAAE,EAAE;QACT,MAAM;UAAE8N,yBAAyB;UAAEC,oBAAoB;UAAEC,oBAAoB;UAAEC,YAAY;UAAEC;QAAc,CAAC,GAAG,IAAI,CAAC1O,MAAM,CAACmF,MAAM;QACjI,IAAI,CAAC3E,EAAE,CAAC8B,QAAQ,CAACJ,KAAK,CAACyM,eAAe,GAAGL,yBAAyB,IAAI,EAAE;QACxE,IAAI,CAAC9N,EAAE,CAACjC,SAAS,CAAC2D,KAAK,CAACyM,eAAe,GAAGJ,oBAAoB,IAAI,EAAE;QACpE,IAAI,CAAC/N,EAAE,CAACjC,SAAS,CAAC2D,KAAK,CAAC0M,KAAK,GAAGJ,oBAAoB,IAAI,EAAE;QAC1D,IAAI,CAAChO,EAAE,CAACjC,SAAS,CAAC2D,KAAK,CAAC2M,MAAM,GAAGJ,YAAY,GAAG,aAAaA,YAAY,EAAE,GAAG,EAAE;QAChF,IAAI,CAACjO,EAAE,CAACjC,SAAS,CAAC2D,KAAK,CAAC4M,SAAS,GAAGJ,YAAY,GAAG,eAAeA,YAAY,EAAE,GAAG,EAAE;QACrF,IAAI,CAAClO,EAAE,CAAC4C,IAAI,CAAClB,KAAK,CAAC,IAAI,CAAClC,MAAM,CAACoD,IAAI,CAAC;QACpC,MAAMsF,OAAO,GAAG,EAAE;QAClB,IAAI,IAAI,CAAC1I,MAAM,CAAC+O,WAAW,CAACC,iBAAiB,EAAE;UAC3CtG,OAAO,CAACjB,IAAI,CAAC,kEAAkE,IAAI,CAACzH,MAAM,CAAC+O,WAAW,CAACC,iBAAiB,KAAK,CAAC;QAClI;QACA,IAAI,IAAI,CAAChP,MAAM,CAAC+O,WAAW,CAACE,qBAAqB,EAAE;UAC/CvG,OAAO,CAACjB,IAAI,CAAC,2DAA2D,IAAI,CAACzH,MAAM,CAAC+O,WAAW,CAACE,qBAAqB,KAAK,CAAC;QAC/H;QACA,IAAI,IAAI,CAACjP,MAAM,CAAC+O,WAAW,CAACE,qBAAqB,EAAE;UAC/CvG,OAAO,CAACjB,IAAI,CAAC,uGAAuG,CAAC;QACzH;QACA,IAAI,IAAI,CAACzH,MAAM,CAACkP,eAAe,CAACC,yBAAyB,IACrD,IAAI,CAACnP,MAAM,CAACkP,eAAe,CAACE,qBAAqB,IACjD,IAAI,CAACpP,MAAM,CAACkP,eAAe,CAACG,2BAA2B,IACvD,IAAI,CAACrP,MAAM,CAACkP,eAAe,CAACI,qBAAqB,IACjD,IAAI,CAACtP,MAAM,CAACkP,eAAe,CAACK,yBAAyB,EAAE;UACvD7G,OAAO,CAACjB,IAAI,CAAC,iEAAiE,CAAC;UAC/E,IAAI,IAAI,CAACzH,MAAM,CAACkP,eAAe,CAACC,yBAAyB,EAAE;YACvDzG,OAAO,CAACjB,IAAI,CAAC,qBAAqB,IAAI,CAACzH,MAAM,CAACkP,eAAe,CAACC,yBAAyB,GAAG,CAAC;UAC/F;UACA,IAAI,IAAI,CAACnP,MAAM,CAACkP,eAAe,CAACE,qBAAqB,EAAE;YACnD;YACA1G,OAAO,CAACjB,IAAI,CAAC,iBAAiB,IAAI,CAACzH,MAAM,CAACkP,eAAe,CAACE,qBAAqB,GAAG,CAAC;UACvF;UACA,IAAI,IAAI,CAACpP,MAAM,CAACkP,eAAe,CAACG,2BAA2B,EAAE;YACzD3G,OAAO,CAACjB,IAAI,CAAC,wBAAwB,IAAI,CAACzH,MAAM,CAACkP,eAAe,CAACG,2BAA2B,GAAG,CAAC;UACpG;UACA,IAAI,IAAI,CAACrP,MAAM,CAACkP,eAAe,CAACI,qBAAqB,EAAE;YACnD5G,OAAO,CAACjB,IAAI,CAAC,8BAA8B,IAAI,CAACzH,MAAM,CAACkP,eAAe,CAACI,qBAAqB,GAAG,CAAC;UACpG;UACA,IAAI,IAAI,CAACtP,MAAM,CAACkP,eAAe,CAACK,yBAAyB,EAAE;YACvD7G,OAAO,CAACjB,IAAI,CAAC,UAAU,IAAI,CAACzH,MAAM,CAACkP,eAAe,CAACK,yBAAyB,GAAG,CAAC;UACpF;UACA7G,OAAO,CAACjB,IAAI,CAAC,GAAG,CAAC;QACrB;QACA,MAAM+H,SAAS,GAAG9G,OAAO,CAACb,IAAI,CAAC,IAAI,CAAC;QACpC,IAAI2H,SAAS,KAAK,IAAI,CAAChP,EAAE,CAAC4B,UAAU,CAACgK,WAAW,EAAE;UAC9C,IAAI,CAAC5L,EAAE,CAAC4B,UAAU,CAACgK,WAAW,GAAGoD,SAAS;QAC9C;MACJ;IACJ;EACJ,CAAC;EAAA,OAjkBGtR,oBAAoB;AAAA,IAikBvB;AACDA,oBAAoB,GAAG3B,sBAAsB,GAAGnB,UAAU,CAAC,CACvDgB,OAAO,CAAC,CAAC,EAAEwB,cAAc,CAAC,EAC1BxB,OAAO,CAAC,CAAC,EAAE0B,qBAAqB,CAAC,EACjC1B,OAAO,CAAC,CAAC,EAAE4B,kBAAkB,CAAC,CACjC,EAAEE,oBAAoB,CAAC;AACxB,SAASA,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}