{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as arrays from './arrays.js';\n/**\n * Compares two items for equality using strict equality.\n*/\nexport const strictEquals = (a, b) => a === b;\n/**\n * Checks if the items of two arrays are equal.\n * By default, strict equality is used to compare elements, but a custom equality comparer can be provided.\n */\nexport function itemsEquals(itemEquals = strictEquals) {\n  return (a, b) => arrays.equals(a, b, itemEquals);\n}\n/**\n * Uses `item.equals(other)` to determine equality.\n */\nexport function itemEquals() {\n  return (a, b) => a.equals(b);\n}\nexport function equalsIfDefined(equalsOrV1, v2, equals) {\n  if (equals !== undefined) {\n    const v1 = equalsOrV1;\n    if (v1 === undefined || v1 === null || v2 === undefined || v2 === null) {\n      return v2 === v1;\n    }\n    return equals(v1, v2);\n  } else {\n    const equals = equalsOrV1;\n    return (v1, v2) => {\n      if (v1 === undefined || v1 === null || v2 === undefined || v2 === null) {\n        return v2 === v1;\n      }\n      return equals(v1, v2);\n    };\n  }\n}\n/**\n * Drills into arrays (items ordered) and objects (keys unordered) and uses strict equality on everything else.\n*/\nexport function structuralEquals(a, b) {\n  if (a === b) {\n    return true;\n  }\n  if (Array.isArray(a) && Array.isArray(b)) {\n    if (a.length !== b.length) {\n      return false;\n    }\n    for (let i = 0; i < a.length; i++) {\n      if (!structuralEquals(a[i], b[i])) {\n        return false;\n      }\n    }\n    return true;\n  }\n  if (a && typeof a === 'object' && b && typeof b === 'object') {\n    if (Object.getPrototypeOf(a) === Object.prototype && Object.getPrototypeOf(b) === Object.prototype) {\n      const aObj = a;\n      const bObj = b;\n      const keysA = Object.keys(aObj);\n      const keysB = Object.keys(bObj);\n      const keysBSet = new Set(keysB);\n      if (keysA.length !== keysB.length) {\n        return false;\n      }\n      for (const key of keysA) {\n        if (!keysBSet.has(key)) {\n          return false;\n        }\n        if (!structuralEquals(aObj[key], bObj[key])) {\n          return false;\n        }\n      }\n      return true;\n    }\n  }\n  return false;\n}\nconst objIds = new WeakMap();", "map": {"version": 3, "names": ["arrays", "strictEquals", "a", "b", "itemsEquals", "itemEquals", "equals", "equalsIfDefined", "equalsOrV1", "v2", "undefined", "v1", "structuralEquals", "Array", "isArray", "length", "i", "Object", "getPrototypeOf", "prototype", "a<PERSON>bj", "bObj", "keysA", "keys", "keysB", "keysBSet", "Set", "key", "has", "objIds", "WeakMap"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/common/equals.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as arrays from './arrays.js';\n/**\n * Compares two items for equality using strict equality.\n*/\nexport const strictEquals = (a, b) => a === b;\n/**\n * Checks if the items of two arrays are equal.\n * By default, strict equality is used to compare elements, but a custom equality comparer can be provided.\n */\nexport function itemsEquals(itemEquals = strictEquals) {\n    return (a, b) => arrays.equals(a, b, itemEquals);\n}\n/**\n * Uses `item.equals(other)` to determine equality.\n */\nexport function itemEquals() {\n    return (a, b) => a.equals(b);\n}\nexport function equalsIfDefined(equalsOrV1, v2, equals) {\n    if (equals !== undefined) {\n        const v1 = equalsOrV1;\n        if (v1 === undefined || v1 === null || v2 === undefined || v2 === null) {\n            return v2 === v1;\n        }\n        return equals(v1, v2);\n    }\n    else {\n        const equals = equalsOrV1;\n        return (v1, v2) => {\n            if (v1 === undefined || v1 === null || v2 === undefined || v2 === null) {\n                return v2 === v1;\n            }\n            return equals(v1, v2);\n        };\n    }\n}\n/**\n * Drills into arrays (items ordered) and objects (keys unordered) and uses strict equality on everything else.\n*/\nexport function structuralEquals(a, b) {\n    if (a === b) {\n        return true;\n    }\n    if (Array.isArray(a) && Array.isArray(b)) {\n        if (a.length !== b.length) {\n            return false;\n        }\n        for (let i = 0; i < a.length; i++) {\n            if (!structuralEquals(a[i], b[i])) {\n                return false;\n            }\n        }\n        return true;\n    }\n    if (a && typeof a === 'object' && b && typeof b === 'object') {\n        if (Object.getPrototypeOf(a) === Object.prototype && Object.getPrototypeOf(b) === Object.prototype) {\n            const aObj = a;\n            const bObj = b;\n            const keysA = Object.keys(aObj);\n            const keysB = Object.keys(bObj);\n            const keysBSet = new Set(keysB);\n            if (keysA.length !== keysB.length) {\n                return false;\n            }\n            for (const key of keysA) {\n                if (!keysBSet.has(key)) {\n                    return false;\n                }\n                if (!structuralEquals(aObj[key], bObj[key])) {\n                    return false;\n                }\n            }\n            return true;\n        }\n    }\n    return false;\n}\nconst objIds = new WeakMap();\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,aAAa;AACrC;AACA;AACA;AACA,OAAO,MAAMC,YAAY,GAAGA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,KAAKC,CAAC;AAC7C;AACA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAACC,UAAU,GAAGJ,YAAY,EAAE;EACnD,OAAO,CAACC,CAAC,EAAEC,CAAC,KAAKH,MAAM,CAACM,MAAM,CAACJ,CAAC,EAAEC,CAAC,EAAEE,UAAU,CAAC;AACpD;AACA;AACA;AACA;AACA,OAAO,SAASA,UAAUA,CAAA,EAAG;EACzB,OAAO,CAACH,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACI,MAAM,CAACH,CAAC,CAAC;AAChC;AACA,OAAO,SAASI,eAAeA,CAACC,UAAU,EAAEC,EAAE,EAAEH,MAAM,EAAE;EACpD,IAAIA,MAAM,KAAKI,SAAS,EAAE;IACtB,MAAMC,EAAE,GAAGH,UAAU;IACrB,IAAIG,EAAE,KAAKD,SAAS,IAAIC,EAAE,KAAK,IAAI,IAAIF,EAAE,KAAKC,SAAS,IAAID,EAAE,KAAK,IAAI,EAAE;MACpE,OAAOA,EAAE,KAAKE,EAAE;IACpB;IACA,OAAOL,MAAM,CAACK,EAAE,EAAEF,EAAE,CAAC;EACzB,CAAC,MACI;IACD,MAAMH,MAAM,GAAGE,UAAU;IACzB,OAAO,CAACG,EAAE,EAAEF,EAAE,KAAK;MACf,IAAIE,EAAE,KAAKD,SAAS,IAAIC,EAAE,KAAK,IAAI,IAAIF,EAAE,KAAKC,SAAS,IAAID,EAAE,KAAK,IAAI,EAAE;QACpE,OAAOA,EAAE,KAAKE,EAAE;MACpB;MACA,OAAOL,MAAM,CAACK,EAAE,EAAEF,EAAE,CAAC;IACzB,CAAC;EACL;AACJ;AACA;AACA;AACA;AACA,OAAO,SAASG,gBAAgBA,CAACV,CAAC,EAAEC,CAAC,EAAE;EACnC,IAAID,CAAC,KAAKC,CAAC,EAAE;IACT,OAAO,IAAI;EACf;EACA,IAAIU,KAAK,CAACC,OAAO,CAACZ,CAAC,CAAC,IAAIW,KAAK,CAACC,OAAO,CAACX,CAAC,CAAC,EAAE;IACtC,IAAID,CAAC,CAACa,MAAM,KAAKZ,CAAC,CAACY,MAAM,EAAE;MACvB,OAAO,KAAK;IAChB;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGd,CAAC,CAACa,MAAM,EAAEC,CAAC,EAAE,EAAE;MAC/B,IAAI,CAACJ,gBAAgB,CAACV,CAAC,CAACc,CAAC,CAAC,EAAEb,CAAC,CAACa,CAAC,CAAC,CAAC,EAAE;QAC/B,OAAO,KAAK;MAChB;IACJ;IACA,OAAO,IAAI;EACf;EACA,IAAId,CAAC,IAAI,OAAOA,CAAC,KAAK,QAAQ,IAAIC,CAAC,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;IAC1D,IAAIc,MAAM,CAACC,cAAc,CAAChB,CAAC,CAAC,KAAKe,MAAM,CAACE,SAAS,IAAIF,MAAM,CAACC,cAAc,CAACf,CAAC,CAAC,KAAKc,MAAM,CAACE,SAAS,EAAE;MAChG,MAAMC,IAAI,GAAGlB,CAAC;MACd,MAAMmB,IAAI,GAAGlB,CAAC;MACd,MAAMmB,KAAK,GAAGL,MAAM,CAACM,IAAI,CAACH,IAAI,CAAC;MAC/B,MAAMI,KAAK,GAAGP,MAAM,CAACM,IAAI,CAACF,IAAI,CAAC;MAC/B,MAAMI,QAAQ,GAAG,IAAIC,GAAG,CAACF,KAAK,CAAC;MAC/B,IAAIF,KAAK,CAACP,MAAM,KAAKS,KAAK,CAACT,MAAM,EAAE;QAC/B,OAAO,KAAK;MAChB;MACA,KAAK,MAAMY,GAAG,IAAIL,KAAK,EAAE;QACrB,IAAI,CAACG,QAAQ,CAACG,GAAG,CAACD,GAAG,CAAC,EAAE;UACpB,OAAO,KAAK;QAChB;QACA,IAAI,CAACf,gBAAgB,CAACQ,IAAI,CAACO,GAAG,CAAC,EAAEN,IAAI,CAACM,GAAG,CAAC,CAAC,EAAE;UACzC,OAAO,KAAK;QAChB;MACJ;MACA,OAAO,IAAI;IACf;EACJ;EACA,OAAO,KAAK;AAChB;AACA,MAAME,MAAM,GAAG,IAAIC,OAAO,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}