{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { localize2 } from '../../../nls.js';\nexport const Categories = Object.freeze({\n  View: localize2('view', 'View'),\n  Help: localize2('help', 'Help'),\n  Test: localize2('test', 'Test'),\n  File: localize2('file', 'File'),\n  Preferences: localize2('preferences', 'Preferences'),\n  Developer: localize2({\n    key: 'developer',\n    comment: ['A developer on Code itself or someone diagnosing issues in Code']\n  }, \"Developer\")\n});", "map": {"version": 3, "names": ["localize2", "Categories", "Object", "freeze", "View", "Help", "Test", "File", "Preferences", "Developer", "key", "comment"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/platform/action/common/actionCommonCategories.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { localize2 } from '../../../nls.js';\nexport const Categories = Object.freeze({\n    View: localize2('view', 'View'),\n    Help: localize2('help', 'Help'),\n    Test: localize2('test', 'Test'),\n    File: localize2('file', 'File'),\n    Preferences: localize2('preferences', 'Preferences'),\n    Developer: localize2({ key: 'developer', comment: ['A developer on Code itself or someone diagnosing issues in Code'] }, \"Developer\"),\n});\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,iBAAiB;AAC3C,OAAO,MAAMC,UAAU,GAAGC,MAAM,CAACC,MAAM,CAAC;EACpCC,IAAI,EAAEJ,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC;EAC/BK,IAAI,EAAEL,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC;EAC/BM,IAAI,EAAEN,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC;EAC/BO,IAAI,EAAEP,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC;EAC/BQ,WAAW,EAAER,SAAS,CAAC,aAAa,EAAE,aAAa,CAAC;EACpDS,SAAS,EAAET,SAAS,CAAC;IAAEU,GAAG,EAAE,WAAW;IAAEC,OAAO,EAAE,CAAC,iEAAiE;EAAE,CAAC,EAAE,WAAW;AACxI,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}