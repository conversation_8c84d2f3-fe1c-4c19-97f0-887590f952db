{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport function showHistoryKeybindingHint(keybindingService) {\n  return keybindingService.lookupKeybinding('history.showPrevious')?.getElectronAccelerator() === 'Up' && keybindingService.lookupKeybinding('history.showNext')?.getElectronAccelerator() === 'Down';\n}", "map": {"version": 3, "names": ["showHistoryKeybindingHint", "keybindingService", "lookupKeybinding", "getElectronAccelerator"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/platform/history/browser/historyWidgetKeybindingHint.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport function showHistoryKeybindingHint(keybindingService) {\n    return keybindingService.lookupKeybinding('history.showPrevious')?.getElectronAccelerator() === 'Up' && keybindingService.lookupKeybinding('history.showNext')?.getElectronAccelerator() === 'Down';\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,SAASA,yBAAyBA,CAACC,iBAAiB,EAAE;EACzD,OAAOA,iBAAiB,CAACC,gBAAgB,CAAC,sBAAsB,CAAC,EAAEC,sBAAsB,CAAC,CAAC,KAAK,IAAI,IAAIF,iBAAiB,CAACC,gBAAgB,CAAC,kBAAkB,CAAC,EAAEC,sBAAsB,CAAC,CAAC,KAAK,MAAM;AACvM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}