{"ast": null, "code": "import _asyncToGenerator from \"C:/console/aava-ui-web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { CancellationToken } from '../../../../base/common/cancellation.js';\nimport { illegalArgument, onUnexpectedExternalError } from '../../../../base/common/errors.js';\nimport { URI } from '../../../../base/common/uri.js';\nimport { Range } from '../../../common/core/range.js';\nimport { IModelService } from '../../../common/services/model.js';\nimport { CommandsRegistry } from '../../../../platform/commands/common/commands.js';\nimport { ILanguageFeaturesService } from '../../../common/services/languageFeatures.js';\nimport { DefaultDocumentColorProvider } from './defaultDocumentColorProvider.js';\nimport { IConfigurationService } from '../../../../platform/configuration/common/configuration.js';\nexport function getColors(_x, _x2, _x3) {\n  return _getColors.apply(this, arguments);\n}\nfunction _getColors() {\n  _getColors = _asyncToGenerator(function* (colorProviderRegistry, model, token, isDefaultColorDecoratorsEnabled = true) {\n    return _findColorData(new ColorDataCollector(), colorProviderRegistry, model, token, isDefaultColorDecoratorsEnabled);\n  });\n  return _getColors.apply(this, arguments);\n}\nexport function getColorPresentations(model, colorInfo, provider, token) {\n  return Promise.resolve(provider.provideColorPresentations(model, colorInfo, token));\n}\nclass ColorDataCollector {\n  constructor() {}\n  compute(provider, model, token, colors) {\n    return _asyncToGenerator(function* () {\n      const documentColors = yield provider.provideDocumentColors(model, token);\n      if (Array.isArray(documentColors)) {\n        for (const colorInfo of documentColors) {\n          colors.push({\n            colorInfo,\n            provider\n          });\n        }\n      }\n      return Array.isArray(documentColors);\n    })();\n  }\n}\nclass ExtColorDataCollector {\n  constructor() {}\n  compute(provider, model, token, colors) {\n    return _asyncToGenerator(function* () {\n      const documentColors = yield provider.provideDocumentColors(model, token);\n      if (Array.isArray(documentColors)) {\n        for (const colorInfo of documentColors) {\n          colors.push({\n            range: colorInfo.range,\n            color: [colorInfo.color.red, colorInfo.color.green, colorInfo.color.blue, colorInfo.color.alpha]\n          });\n        }\n      }\n      return Array.isArray(documentColors);\n    })();\n  }\n}\nclass ColorPresentationsCollector {\n  constructor(colorInfo) {\n    this.colorInfo = colorInfo;\n  }\n  compute(provider, model, _token, colors) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const documentColors = yield provider.provideColorPresentations(model, _this.colorInfo, CancellationToken.None);\n      if (Array.isArray(documentColors)) {\n        colors.push(...documentColors);\n      }\n      return Array.isArray(documentColors);\n    })();\n  }\n}\nfunction _findColorData(_x4, _x5, _x6, _x7, _x8) {\n  return _findColorData2.apply(this, arguments);\n}\nfunction _findColorData2() {\n  _findColorData2 = _asyncToGenerator(function* (collector, colorProviderRegistry, model, token, isDefaultColorDecoratorsEnabled) {\n    let validDocumentColorProviderFound = false;\n    let defaultProvider;\n    const colorData = [];\n    const documentColorProviders = colorProviderRegistry.ordered(model);\n    for (let i = documentColorProviders.length - 1; i >= 0; i--) {\n      const provider = documentColorProviders[i];\n      if (provider instanceof DefaultDocumentColorProvider) {\n        defaultProvider = provider;\n      } else {\n        try {\n          if (yield collector.compute(provider, model, token, colorData)) {\n            validDocumentColorProviderFound = true;\n          }\n        } catch (e) {\n          onUnexpectedExternalError(e);\n        }\n      }\n    }\n    if (validDocumentColorProviderFound) {\n      return colorData;\n    }\n    if (defaultProvider && isDefaultColorDecoratorsEnabled) {\n      yield collector.compute(defaultProvider, model, token, colorData);\n      return colorData;\n    }\n    return [];\n  });\n  return _findColorData2.apply(this, arguments);\n}\nfunction _setupColorCommand(accessor, resource) {\n  const {\n    colorProvider: colorProviderRegistry\n  } = accessor.get(ILanguageFeaturesService);\n  const model = accessor.get(IModelService).getModel(resource);\n  if (!model) {\n    throw illegalArgument();\n  }\n  const isDefaultColorDecoratorsEnabled = accessor.get(IConfigurationService).getValue('editor.defaultColorDecorators', {\n    resource\n  });\n  return {\n    model,\n    colorProviderRegistry,\n    isDefaultColorDecoratorsEnabled\n  };\n}\nCommandsRegistry.registerCommand('_executeDocumentColorProvider', function (accessor, ...args) {\n  const [resource] = args;\n  if (!(resource instanceof URI)) {\n    throw illegalArgument();\n  }\n  const {\n    model,\n    colorProviderRegistry,\n    isDefaultColorDecoratorsEnabled\n  } = _setupColorCommand(accessor, resource);\n  return _findColorData(new ExtColorDataCollector(), colorProviderRegistry, model, CancellationToken.None, isDefaultColorDecoratorsEnabled);\n});\nCommandsRegistry.registerCommand('_executeColorPresentationProvider', function (accessor, ...args) {\n  const [color, context] = args;\n  const {\n    uri,\n    range\n  } = context;\n  if (!(uri instanceof URI) || !Array.isArray(color) || color.length !== 4 || !Range.isIRange(range)) {\n    throw illegalArgument();\n  }\n  const {\n    model,\n    colorProviderRegistry,\n    isDefaultColorDecoratorsEnabled\n  } = _setupColorCommand(accessor, uri);\n  const [red, green, blue, alpha] = color;\n  return _findColorData(new ColorPresentationsCollector({\n    range: range,\n    color: {\n      red,\n      green,\n      blue,\n      alpha\n    }\n  }), colorProviderRegistry, model, CancellationToken.None, isDefaultColorDecoratorsEnabled);\n});", "map": {"version": 3, "names": ["CancellationToken", "illegalArgument", "onUnexpectedExternalError", "URI", "Range", "IModelService", "CommandsRegistry", "ILanguageFeaturesService", "DefaultDocumentColorProvider", "IConfigurationService", "getColors", "_x", "_x2", "_x3", "_getColors", "apply", "arguments", "_asyncToGenerator", "colorProviderRegistry", "model", "token", "isDefaultColorDecoratorsEnabled", "_findColorData", "ColorDataCollector", "getColorPresentations", "colorInfo", "provider", "Promise", "resolve", "provideColorPresentations", "constructor", "compute", "colors", "documentColors", "provideDocumentColors", "Array", "isArray", "push", "ExtColorDataCollector", "range", "color", "red", "green", "blue", "alpha", "ColorPresentationsCollector", "_token", "_this", "None", "_x4", "_x5", "_x6", "_x7", "_x8", "_findColorData2", "collector", "validDocumentColorProviderFound", "defaultProvider", "colorData", "documentColorProviders", "ordered", "i", "length", "e", "_setupColorCommand", "accessor", "resource", "colorProvider", "get", "getModel", "getValue", "registerCommand", "args", "context", "uri", "isIRange"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/contrib/colorPicker/browser/color.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { CancellationToken } from '../../../../base/common/cancellation.js';\nimport { illegalArgument, onUnexpectedExternalError } from '../../../../base/common/errors.js';\nimport { URI } from '../../../../base/common/uri.js';\nimport { Range } from '../../../common/core/range.js';\nimport { IModelService } from '../../../common/services/model.js';\nimport { CommandsRegistry } from '../../../../platform/commands/common/commands.js';\nimport { ILanguageFeaturesService } from '../../../common/services/languageFeatures.js';\nimport { DefaultDocumentColorProvider } from './defaultDocumentColorProvider.js';\nimport { IConfigurationService } from '../../../../platform/configuration/common/configuration.js';\nexport async function getColors(colorProviderRegistry, model, token, isDefaultColorDecoratorsEnabled = true) {\n    return _findColorData(new ColorDataCollector(), colorProviderRegistry, model, token, isDefaultColorDecoratorsEnabled);\n}\nexport function getColorPresentations(model, colorInfo, provider, token) {\n    return Promise.resolve(provider.provideColorPresentations(model, colorInfo, token));\n}\nclass ColorDataCollector {\n    constructor() { }\n    async compute(provider, model, token, colors) {\n        const documentColors = await provider.provideDocumentColors(model, token);\n        if (Array.isArray(documentColors)) {\n            for (const colorInfo of documentColors) {\n                colors.push({ colorInfo, provider });\n            }\n        }\n        return Array.isArray(documentColors);\n    }\n}\nclass ExtColorDataCollector {\n    constructor() { }\n    async compute(provider, model, token, colors) {\n        const documentColors = await provider.provideDocumentColors(model, token);\n        if (Array.isArray(documentColors)) {\n            for (const colorInfo of documentColors) {\n                colors.push({ range: colorInfo.range, color: [colorInfo.color.red, colorInfo.color.green, colorInfo.color.blue, colorInfo.color.alpha] });\n            }\n        }\n        return Array.isArray(documentColors);\n    }\n}\nclass ColorPresentationsCollector {\n    constructor(colorInfo) {\n        this.colorInfo = colorInfo;\n    }\n    async compute(provider, model, _token, colors) {\n        const documentColors = await provider.provideColorPresentations(model, this.colorInfo, CancellationToken.None);\n        if (Array.isArray(documentColors)) {\n            colors.push(...documentColors);\n        }\n        return Array.isArray(documentColors);\n    }\n}\nasync function _findColorData(collector, colorProviderRegistry, model, token, isDefaultColorDecoratorsEnabled) {\n    let validDocumentColorProviderFound = false;\n    let defaultProvider;\n    const colorData = [];\n    const documentColorProviders = colorProviderRegistry.ordered(model);\n    for (let i = documentColorProviders.length - 1; i >= 0; i--) {\n        const provider = documentColorProviders[i];\n        if (provider instanceof DefaultDocumentColorProvider) {\n            defaultProvider = provider;\n        }\n        else {\n            try {\n                if (await collector.compute(provider, model, token, colorData)) {\n                    validDocumentColorProviderFound = true;\n                }\n            }\n            catch (e) {\n                onUnexpectedExternalError(e);\n            }\n        }\n    }\n    if (validDocumentColorProviderFound) {\n        return colorData;\n    }\n    if (defaultProvider && isDefaultColorDecoratorsEnabled) {\n        await collector.compute(defaultProvider, model, token, colorData);\n        return colorData;\n    }\n    return [];\n}\nfunction _setupColorCommand(accessor, resource) {\n    const { colorProvider: colorProviderRegistry } = accessor.get(ILanguageFeaturesService);\n    const model = accessor.get(IModelService).getModel(resource);\n    if (!model) {\n        throw illegalArgument();\n    }\n    const isDefaultColorDecoratorsEnabled = accessor.get(IConfigurationService).getValue('editor.defaultColorDecorators', { resource });\n    return { model, colorProviderRegistry, isDefaultColorDecoratorsEnabled };\n}\nCommandsRegistry.registerCommand('_executeDocumentColorProvider', function (accessor, ...args) {\n    const [resource] = args;\n    if (!(resource instanceof URI)) {\n        throw illegalArgument();\n    }\n    const { model, colorProviderRegistry, isDefaultColorDecoratorsEnabled } = _setupColorCommand(accessor, resource);\n    return _findColorData(new ExtColorDataCollector(), colorProviderRegistry, model, CancellationToken.None, isDefaultColorDecoratorsEnabled);\n});\nCommandsRegistry.registerCommand('_executeColorPresentationProvider', function (accessor, ...args) {\n    const [color, context] = args;\n    const { uri, range } = context;\n    if (!(uri instanceof URI) || !Array.isArray(color) || color.length !== 4 || !Range.isIRange(range)) {\n        throw illegalArgument();\n    }\n    const { model, colorProviderRegistry, isDefaultColorDecoratorsEnabled } = _setupColorCommand(accessor, uri);\n    const [red, green, blue, alpha] = color;\n    return _findColorData(new ColorPresentationsCollector({ range: range, color: { red, green, blue, alpha } }), colorProviderRegistry, model, CancellationToken.None, isDefaultColorDecoratorsEnabled);\n});\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA,SAASA,iBAAiB,QAAQ,yCAAyC;AAC3E,SAASC,eAAe,EAAEC,yBAAyB,QAAQ,mCAAmC;AAC9F,SAASC,GAAG,QAAQ,gCAAgC;AACpD,SAASC,KAAK,QAAQ,+BAA+B;AACrD,SAASC,aAAa,QAAQ,mCAAmC;AACjE,SAASC,gBAAgB,QAAQ,kDAAkD;AACnF,SAASC,wBAAwB,QAAQ,8CAA8C;AACvF,SAASC,4BAA4B,QAAQ,mCAAmC;AAChF,SAASC,qBAAqB,QAAQ,4DAA4D;AAClG,gBAAsBC,SAASA,CAAAC,EAAA,EAAAC,GAAA,EAAAC,GAAA;EAAA,OAAAC,UAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAE9B,SAAAF,WAAA;EAAAA,UAAA,GAAAG,iBAAA,CAFM,WAAyBC,qBAAqB,EAAEC,KAAK,EAAEC,KAAK,EAAEC,+BAA+B,GAAG,IAAI,EAAE;IACzG,OAAOC,cAAc,CAAC,IAAIC,kBAAkB,CAAC,CAAC,EAAEL,qBAAqB,EAAEC,KAAK,EAAEC,KAAK,EAAEC,+BAA+B,CAAC;EACzH,CAAC;EAAA,OAAAP,UAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AACD,OAAO,SAASQ,qBAAqBA,CAACL,KAAK,EAAEM,SAAS,EAAEC,QAAQ,EAAEN,KAAK,EAAE;EACrE,OAAOO,OAAO,CAACC,OAAO,CAACF,QAAQ,CAACG,yBAAyB,CAACV,KAAK,EAAEM,SAAS,EAAEL,KAAK,CAAC,CAAC;AACvF;AACA,MAAMG,kBAAkB,CAAC;EACrBO,WAAWA,CAAA,EAAG,CAAE;EACVC,OAAOA,CAACL,QAAQ,EAAEP,KAAK,EAAEC,KAAK,EAAEY,MAAM,EAAE;IAAA,OAAAf,iBAAA;MAC1C,MAAMgB,cAAc,SAASP,QAAQ,CAACQ,qBAAqB,CAACf,KAAK,EAAEC,KAAK,CAAC;MACzE,IAAIe,KAAK,CAACC,OAAO,CAACH,cAAc,CAAC,EAAE;QAC/B,KAAK,MAAMR,SAAS,IAAIQ,cAAc,EAAE;UACpCD,MAAM,CAACK,IAAI,CAAC;YAAEZ,SAAS;YAAEC;UAAS,CAAC,CAAC;QACxC;MACJ;MACA,OAAOS,KAAK,CAACC,OAAO,CAACH,cAAc,CAAC;IAAC;EACzC;AACJ;AACA,MAAMK,qBAAqB,CAAC;EACxBR,WAAWA,CAAA,EAAG,CAAE;EACVC,OAAOA,CAACL,QAAQ,EAAEP,KAAK,EAAEC,KAAK,EAAEY,MAAM,EAAE;IAAA,OAAAf,iBAAA;MAC1C,MAAMgB,cAAc,SAASP,QAAQ,CAACQ,qBAAqB,CAACf,KAAK,EAAEC,KAAK,CAAC;MACzE,IAAIe,KAAK,CAACC,OAAO,CAACH,cAAc,CAAC,EAAE;QAC/B,KAAK,MAAMR,SAAS,IAAIQ,cAAc,EAAE;UACpCD,MAAM,CAACK,IAAI,CAAC;YAAEE,KAAK,EAAEd,SAAS,CAACc,KAAK;YAAEC,KAAK,EAAE,CAACf,SAAS,CAACe,KAAK,CAACC,GAAG,EAAEhB,SAAS,CAACe,KAAK,CAACE,KAAK,EAAEjB,SAAS,CAACe,KAAK,CAACG,IAAI,EAAElB,SAAS,CAACe,KAAK,CAACI,KAAK;UAAE,CAAC,CAAC;QAC7I;MACJ;MACA,OAAOT,KAAK,CAACC,OAAO,CAACH,cAAc,CAAC;IAAC;EACzC;AACJ;AACA,MAAMY,2BAA2B,CAAC;EAC9Bf,WAAWA,CAACL,SAAS,EAAE;IACnB,IAAI,CAACA,SAAS,GAAGA,SAAS;EAC9B;EACMM,OAAOA,CAACL,QAAQ,EAAEP,KAAK,EAAE2B,MAAM,EAAEd,MAAM,EAAE;IAAA,IAAAe,KAAA;IAAA,OAAA9B,iBAAA;MAC3C,MAAMgB,cAAc,SAASP,QAAQ,CAACG,yBAAyB,CAACV,KAAK,EAAE4B,KAAI,CAACtB,SAAS,EAAEzB,iBAAiB,CAACgD,IAAI,CAAC;MAC9G,IAAIb,KAAK,CAACC,OAAO,CAACH,cAAc,CAAC,EAAE;QAC/BD,MAAM,CAACK,IAAI,CAAC,GAAGJ,cAAc,CAAC;MAClC;MACA,OAAOE,KAAK,CAACC,OAAO,CAACH,cAAc,CAAC;IAAC;EACzC;AACJ;AAAC,SACcX,cAAcA,CAAA2B,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;EAAA,OAAAC,eAAA,CAAAvC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAsC,gBAAA;EAAAA,eAAA,GAAArC,iBAAA,CAA7B,WAA8BsC,SAAS,EAAErC,qBAAqB,EAAEC,KAAK,EAAEC,KAAK,EAAEC,+BAA+B,EAAE;IAC3G,IAAImC,+BAA+B,GAAG,KAAK;IAC3C,IAAIC,eAAe;IACnB,MAAMC,SAAS,GAAG,EAAE;IACpB,MAAMC,sBAAsB,GAAGzC,qBAAqB,CAAC0C,OAAO,CAACzC,KAAK,CAAC;IACnE,KAAK,IAAI0C,CAAC,GAAGF,sBAAsB,CAACG,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MACzD,MAAMnC,QAAQ,GAAGiC,sBAAsB,CAACE,CAAC,CAAC;MAC1C,IAAInC,QAAQ,YAAYlB,4BAA4B,EAAE;QAClDiD,eAAe,GAAG/B,QAAQ;MAC9B,CAAC,MACI;QACD,IAAI;UACA,UAAU6B,SAAS,CAACxB,OAAO,CAACL,QAAQ,EAAEP,KAAK,EAAEC,KAAK,EAAEsC,SAAS,CAAC,EAAE;YAC5DF,+BAA+B,GAAG,IAAI;UAC1C;QACJ,CAAC,CACD,OAAOO,CAAC,EAAE;UACN7D,yBAAyB,CAAC6D,CAAC,CAAC;QAChC;MACJ;IACJ;IACA,IAAIP,+BAA+B,EAAE;MACjC,OAAOE,SAAS;IACpB;IACA,IAAID,eAAe,IAAIpC,+BAA+B,EAAE;MACpD,MAAMkC,SAAS,CAACxB,OAAO,CAAC0B,eAAe,EAAEtC,KAAK,EAAEC,KAAK,EAAEsC,SAAS,CAAC;MACjE,OAAOA,SAAS;IACpB;IACA,OAAO,EAAE;EACb,CAAC;EAAA,OAAAJ,eAAA,CAAAvC,KAAA,OAAAC,SAAA;AAAA;AACD,SAASgD,kBAAkBA,CAACC,QAAQ,EAAEC,QAAQ,EAAE;EAC5C,MAAM;IAAEC,aAAa,EAAEjD;EAAsB,CAAC,GAAG+C,QAAQ,CAACG,GAAG,CAAC7D,wBAAwB,CAAC;EACvF,MAAMY,KAAK,GAAG8C,QAAQ,CAACG,GAAG,CAAC/D,aAAa,CAAC,CAACgE,QAAQ,CAACH,QAAQ,CAAC;EAC5D,IAAI,CAAC/C,KAAK,EAAE;IACR,MAAMlB,eAAe,CAAC,CAAC;EAC3B;EACA,MAAMoB,+BAA+B,GAAG4C,QAAQ,CAACG,GAAG,CAAC3D,qBAAqB,CAAC,CAAC6D,QAAQ,CAAC,+BAA+B,EAAE;IAAEJ;EAAS,CAAC,CAAC;EACnI,OAAO;IAAE/C,KAAK;IAAED,qBAAqB;IAAEG;EAAgC,CAAC;AAC5E;AACAf,gBAAgB,CAACiE,eAAe,CAAC,+BAA+B,EAAE,UAAUN,QAAQ,EAAE,GAAGO,IAAI,EAAE;EAC3F,MAAM,CAACN,QAAQ,CAAC,GAAGM,IAAI;EACvB,IAAI,EAAEN,QAAQ,YAAY/D,GAAG,CAAC,EAAE;IAC5B,MAAMF,eAAe,CAAC,CAAC;EAC3B;EACA,MAAM;IAAEkB,KAAK;IAAED,qBAAqB;IAAEG;EAAgC,CAAC,GAAG2C,kBAAkB,CAACC,QAAQ,EAAEC,QAAQ,CAAC;EAChH,OAAO5C,cAAc,CAAC,IAAIgB,qBAAqB,CAAC,CAAC,EAAEpB,qBAAqB,EAAEC,KAAK,EAAEnB,iBAAiB,CAACgD,IAAI,EAAE3B,+BAA+B,CAAC;AAC7I,CAAC,CAAC;AACFf,gBAAgB,CAACiE,eAAe,CAAC,mCAAmC,EAAE,UAAUN,QAAQ,EAAE,GAAGO,IAAI,EAAE;EAC/F,MAAM,CAAChC,KAAK,EAAEiC,OAAO,CAAC,GAAGD,IAAI;EAC7B,MAAM;IAAEE,GAAG;IAAEnC;EAAM,CAAC,GAAGkC,OAAO;EAC9B,IAAI,EAAEC,GAAG,YAAYvE,GAAG,CAAC,IAAI,CAACgC,KAAK,CAACC,OAAO,CAACI,KAAK,CAAC,IAAIA,KAAK,CAACsB,MAAM,KAAK,CAAC,IAAI,CAAC1D,KAAK,CAACuE,QAAQ,CAACpC,KAAK,CAAC,EAAE;IAChG,MAAMtC,eAAe,CAAC,CAAC;EAC3B;EACA,MAAM;IAAEkB,KAAK;IAAED,qBAAqB;IAAEG;EAAgC,CAAC,GAAG2C,kBAAkB,CAACC,QAAQ,EAAES,GAAG,CAAC;EAC3G,MAAM,CAACjC,GAAG,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,CAAC,GAAGJ,KAAK;EACvC,OAAOlB,cAAc,CAAC,IAAIuB,2BAA2B,CAAC;IAAEN,KAAK,EAAEA,KAAK;IAAEC,KAAK,EAAE;MAAEC,GAAG;MAAEC,KAAK;MAAEC,IAAI;MAAEC;IAAM;EAAE,CAAC,CAAC,EAAE1B,qBAAqB,EAAEC,KAAK,EAAEnB,iBAAiB,CAACgD,IAAI,EAAE3B,+BAA+B,CAAC;AACvM,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}