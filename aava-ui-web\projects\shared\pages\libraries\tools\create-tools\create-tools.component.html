<main-layout [showRightPane]="true" leftPaneTitle="Tool Description" rightPaneTitle="Tool Playground">
  <ng-container right-header>
    <div class="right-header-content">
      <span class="pane-title">Tool Playground</span>
      <ng-container *ngIf="isEditMode && !isExecuteMode && toolId">
        <ava-button label="Send for Approval" variant="primary" size="small" class="approval-btn"
          (userClick)="handleApproval()"></ava-button>
      </ng-container>
    </div>
  </ng-container>

  <!-- Custom header content for center pane -->
  <div center-header class="center-header-buttons">


    <ava-button label="{{ labels.exit }}" variant="secondary" size="small" [customStyles]="{
        'border': '2px solid transparent',
        'background-image': 'linear-gradient(#ffffff, #ffffff), linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',
        'background-origin': 'border-box',
        'background-clip': 'padding-box, border-box',
        '--button-effect-color': '33, 90, 214'
      }" (userClick)="onExit()"></ava-button>

    <ng-container *ngIf="!isEditMode && !isExecuteMode">
      <ava-button
        label="Save"
        variant="primary"
        size="small"
        [customStyles]="{
          background: 'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',
          '--button-effect-color': '33, 90, 214'
        }" (userClick)="onSave()"></ava-button>
    </ng-container>

    <ng-container *ngIf="isEditMode && !isExecuteMode">
      <ava-button
        label="Update"
        variant="primary"
        size="small"
        [customStyles]="{
          background: 'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',
          '--button-effect-color': '33, 90, 214'
        }"
        (userClick)="onUpdate()"
      ></ava-button>
    </ng-container>
  </div>

  <!-- Left Pane: Tool Description -->
  <div left class="tool-description-pane">
    <div class="form-field-group">
      <ava-textbox [formControl]="getControl('name')" [label]="labels.toolName" id="toolName" name="toolName"
        placeholder="Enter tool name" variant="primary" size="md" [fullWidth]="true" [required]="true"
        [readonly]="isFieldsDisabled"></ava-textbox>
    </div>

    <div class="form-field-group">
      <ava-textbox [formControl]="getControl('toolClassName')" [label]="labels.toolClassName" id="toolClassName"
        name="toolClassName" placeholder="Enter tool class" variant="primary" size="md" [fullWidth]="true"
        [required]="true" [readonly]="isFieldsDisabled"></ava-textbox>
    </div>

    <div class="form-field-group">
      <ava-textarea id="description" name="description" [label]="labels.description"
        [formControl]="getControl('description')" placeholder="Enter description" [rows]="6" size="md"
        [fullWidth]="true" [required]="true" [readonly]="isFieldsDisabled"></ava-textarea>
    </div>
  </div>

  <!-- Center Pane: Tool Editor -->
  <div center class="tool-editor-pane">
    <!-- Custom Header with Title and Action Buttons -->
    <div class="custom-center-header">
      <span class="pane-title">Tool Editor</span>
      <hr>
      <div class="header-buttons d-flex">
        <ava-button label="{{ labels.askAva}}" variant="secondary" size="small" [customStyles]="{
            'border': '2px solid transparent',
            'background-image': 'linear-gradient(#ffffff, #ffffff), linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',
            'background-origin': 'border-box',
            'background-clip': 'padding-box, border-box',
            '--button-effect-color': '33, 90, 214'
          }"  iconName="WandSparkles"
          (userClick)="toggleAskAvaModal()">
        </ava-button>
        <ava-button label="{{ labels.exit }}" variant="secondary" size="small" [customStyles]="{
            'border': '2px solid transparent',
            'background-image': 'linear-gradient(#ffffff, #ffffff), linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',
            'background-origin': 'border-box',
            'background-clip': 'padding-box, border-box',
            '--button-effect-color': '33, 90, 214'
          }" (userClick)="onExit()"></ava-button>

        <ng-container *ngIf="!isEditMode && !isExecuteMode">
          <ava-button
            label="Save & Send Approval"
            variant="primary"
            size="small"
            [customStyles]="{
              background: 'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',
              '--button-effect-color': '33, 90, 214'
            }" (userClick)="confirmSave()"></ava-button>
        </ng-container>

        <ng-container *ngIf="isEditMode && !isExecuteMode">
          <ava-button
            label="Update & Send Approval"
            variant="primary"
            size="small"
            [customStyles]="{
              background: 'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',
              '--button-effect-color': '33, 90, 214'
            }"
            (userClick)="confirmUpdate()"
          ></ava-button>
        </ng-container>
      </div>
    </div>

    <!-- Code Editor -->
    <div class="editor-container">
      <app-code-editor #codeEditor placeholder="{{ placeholder }}" language="python"
        [Control]="getControl('classDefinition')" customCssClass="tools-monaco-editor"
        (primaryButtonSelected)="validateCode()" footerText="{{ labels.note }}" [actionButtons]="editorActions"
        (actionButtonClicked)="onEditorAction($event)" [readonly]="isFieldsDisabled"></app-code-editor>
    </div>

    <!-- Tool Compiler -->
    <div class="validation-output-section">
      <app-code-editor
        [title]="validationOutputEditorConfig.title"
        [language]="validationOutputEditorConfig.language"
        [theme]="validationOutputEditorConfig.theme"
        [readonly]="validationOutputEditorConfig.readOnly"
        [height]="validationOutputEditorConfig.height"
        [value]="showValidationOutput ? validationOutput : ''"
        [placeholder]="validationOutputEditorConfig.placeholder"
        customCssClass="validation-json-editor"
      ></app-code-editor>
    </div>
  </div>

  <!-- Right Pane: Tool Playground -->
  <div right class="tool-playground-pane">
    <div class="playground-container">
      <app-playground
        [promptOptions]="promptOptions"
        [messages]="chatMessages"
        [isLoading]="isProcessingChat"
        [showChatInteractionToggles]="false"
        [showAiPrincipleToggle]="true"
        [showDropdown]="false"
        [showAgentNameInput]="true"
        [displayedAgentName]="getToolDisplayName()"
        [agentNamePlaceholder]="'Current Tool Name'"
        [showApprovalButton]="false"
        (promptChange)="onPromptChanged($event)"
        (messageSent)="handleChatMessage($event)"
        (approvalRequested)="handleApproval()"
      ></app-playground>
    </div>
  </div>
</main-layout>


<!-- Ask AVA Wrapper -->
<app-ask-ava-wrapper [show]="showAskAvaModal" [prompt]="prompt" [isLoading]="isLoading" [showOutput]="showToolOutput"
  (oNClickGenerate)="onClickGenerate($event)" (oNClickClosed)="onClose()" (oNClickUse)="onUse()" (oNClickReset)="onReset()"
  (oNClickCancel)="onCancle()">
  <form [formGroup]="outputToolForm">
    <div class="row g-2">
      <div class="col-md-6">
        <ava-textbox [formControl]="getOutputControl('name')" [label]="labels.toolName" id="toolName" name="toolName"
          placeholder="Enter tool name" variant="primary" size="md" [readonly]="true">
        </ava-textbox>
      </div>
      <div class="col-md-6">
        <ava-textbox [formControl]="getOutputControl('toolClassName')" [label]="labels.toolClassName" id="toolClassName"
          name="toolClassName" placeholder="Enter tool class" variant="primary" size="md" [readonly]="true">
        </ava-textbox>
      </div>
      <div class="col-md-12">
        <ava-textarea id="description" name="description" [label]="labels.description"
          [formControl]="getOutputControl('description')" placeholder="Enter description" [rows]="4" size="md"
          [fullWidth]="true" [readonly]="true">
        </ava-textarea>
      </div>
      <div class="col-md-12">
        <app-code-editor id="outputEditor" #codeEditor title="{{ labels.toolClassDefinition }}"
          language="python" [value]="getOutputControl('classDefinition').value"
          customCssClass="tools-monaco-editor" footerText="{{ labels.note }}"
          [readonly]="true">
        </app-code-editor>
      </div>
    </div>
  </form>
</app-ask-ava-wrapper>