{"ast": null, "code": "export default function (x, y) {\n  if (isNaN(x = +x) || isNaN(y = +y)) return this; // ignore invalid points\n\n  var x0 = this._x0,\n    y0 = this._y0,\n    x1 = this._x1,\n    y1 = this._y1;\n\n  // If the quadtree has no extent, initialize them.\n  // Integer extent are necessary so that if we later double the extent,\n  // the existing quadrant boundaries don’t change due to floating point error!\n  if (isNaN(x0)) {\n    x1 = (x0 = Math.floor(x)) + 1;\n    y1 = (y0 = Math.floor(y)) + 1;\n  }\n\n  // Otherwise, double repeatedly to cover.\n  else {\n    var z = x1 - x0 || 1,\n      node = this._root,\n      parent,\n      i;\n    while (x0 > x || x >= x1 || y0 > y || y >= y1) {\n      i = (y < y0) << 1 | x < x0;\n      parent = new Array(4), parent[i] = node, node = parent, z *= 2;\n      switch (i) {\n        case 0:\n          x1 = x0 + z, y1 = y0 + z;\n          break;\n        case 1:\n          x0 = x1 - z, y1 = y0 + z;\n          break;\n        case 2:\n          x1 = x0 + z, y0 = y1 - z;\n          break;\n        case 3:\n          x0 = x1 - z, y0 = y1 - z;\n          break;\n      }\n    }\n    if (this._root && this._root.length) this._root = node;\n  }\n  this._x0 = x0;\n  this._y0 = y0;\n  this._x1 = x1;\n  this._y1 = y1;\n  return this;\n}", "map": {"version": 3, "names": ["x", "y", "isNaN", "x0", "_x0", "y0", "_y0", "x1", "_x1", "y1", "_y1", "Math", "floor", "z", "node", "_root", "parent", "i", "Array", "length"], "sources": ["C:/console/aava-ui-web/node_modules/d3-quadtree/src/cover.js"], "sourcesContent": ["export default function(x, y) {\n  if (isNaN(x = +x) || isNaN(y = +y)) return this; // ignore invalid points\n\n  var x0 = this._x0,\n      y0 = this._y0,\n      x1 = this._x1,\n      y1 = this._y1;\n\n  // If the quadtree has no extent, initialize them.\n  // Integer extent are necessary so that if we later double the extent,\n  // the existing quadrant boundaries don’t change due to floating point error!\n  if (isNaN(x0)) {\n    x1 = (x0 = Math.floor(x)) + 1;\n    y1 = (y0 = Math.floor(y)) + 1;\n  }\n\n  // Otherwise, double repeatedly to cover.\n  else {\n    var z = x1 - x0 || 1,\n        node = this._root,\n        parent,\n        i;\n\n    while (x0 > x || x >= x1 || y0 > y || y >= y1) {\n      i = (y < y0) << 1 | (x < x0);\n      parent = new Array(4), parent[i] = node, node = parent, z *= 2;\n      switch (i) {\n        case 0: x1 = x0 + z, y1 = y0 + z; break;\n        case 1: x0 = x1 - z, y1 = y0 + z; break;\n        case 2: x1 = x0 + z, y0 = y1 - z; break;\n        case 3: x0 = x1 - z, y0 = y1 - z; break;\n      }\n    }\n\n    if (this._root && this._root.length) this._root = node;\n  }\n\n  this._x0 = x0;\n  this._y0 = y0;\n  this._x1 = x1;\n  this._y1 = y1;\n  return this;\n}\n"], "mappings": "AAAA,eAAe,UAASA,CAAC,EAAEC,CAAC,EAAE;EAC5B,IAAIC,KAAK,CAACF,CAAC,GAAG,CAACA,CAAC,CAAC,IAAIE,KAAK,CAACD,CAAC,GAAG,CAACA,CAAC,CAAC,EAAE,OAAO,IAAI,CAAC,CAAC;;EAEjD,IAAIE,EAAE,GAAG,IAAI,CAACC,GAAG;IACbC,EAAE,GAAG,IAAI,CAACC,GAAG;IACbC,EAAE,GAAG,IAAI,CAACC,GAAG;IACbC,EAAE,GAAG,IAAI,CAACC,GAAG;;EAEjB;EACA;EACA;EACA,IAAIR,KAAK,CAACC,EAAE,CAAC,EAAE;IACbI,EAAE,GAAG,CAACJ,EAAE,GAAGQ,IAAI,CAACC,KAAK,CAACZ,CAAC,CAAC,IAAI,CAAC;IAC7BS,EAAE,GAAG,CAACJ,EAAE,GAAGM,IAAI,CAACC,KAAK,CAACX,CAAC,CAAC,IAAI,CAAC;EAC/B;;EAEA;EAAA,KACK;IACH,IAAIY,CAAC,GAAGN,EAAE,GAAGJ,EAAE,IAAI,CAAC;MAChBW,IAAI,GAAG,IAAI,CAACC,KAAK;MACjBC,MAAM;MACNC,CAAC;IAEL,OAAOd,EAAE,GAAGH,CAAC,IAAIA,CAAC,IAAIO,EAAE,IAAIF,EAAE,GAAGJ,CAAC,IAAIA,CAAC,IAAIQ,EAAE,EAAE;MAC7CQ,CAAC,GAAG,CAAChB,CAAC,GAAGI,EAAE,KAAK,CAAC,GAAIL,CAAC,GAAGG,EAAG;MAC5Ba,MAAM,GAAG,IAAIE,KAAK,CAAC,CAAC,CAAC,EAAEF,MAAM,CAACC,CAAC,CAAC,GAAGH,IAAI,EAAEA,IAAI,GAAGE,MAAM,EAAEH,CAAC,IAAI,CAAC;MAC9D,QAAQI,CAAC;QACP,KAAK,CAAC;UAAEV,EAAE,GAAGJ,EAAE,GAAGU,CAAC,EAAEJ,EAAE,GAAGJ,EAAE,GAAGQ,CAAC;UAAE;QAClC,KAAK,CAAC;UAAEV,EAAE,GAAGI,EAAE,GAAGM,CAAC,EAAEJ,EAAE,GAAGJ,EAAE,GAAGQ,CAAC;UAAE;QAClC,KAAK,CAAC;UAAEN,EAAE,GAAGJ,EAAE,GAAGU,CAAC,EAAER,EAAE,GAAGI,EAAE,GAAGI,CAAC;UAAE;QAClC,KAAK,CAAC;UAAEV,EAAE,GAAGI,EAAE,GAAGM,CAAC,EAAER,EAAE,GAAGI,EAAE,GAAGI,CAAC;UAAE;MACpC;IACF;IAEA,IAAI,IAAI,CAACE,KAAK,IAAI,IAAI,CAACA,KAAK,CAACI,MAAM,EAAE,IAAI,CAACJ,KAAK,GAAGD,IAAI;EACxD;EAEA,IAAI,CAACV,GAAG,GAAGD,EAAE;EACb,IAAI,CAACG,GAAG,GAAGD,EAAE;EACb,IAAI,CAACG,GAAG,GAAGD,EAAE;EACb,IAAI,CAACG,GAAG,GAAGD,EAAE;EACb,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}