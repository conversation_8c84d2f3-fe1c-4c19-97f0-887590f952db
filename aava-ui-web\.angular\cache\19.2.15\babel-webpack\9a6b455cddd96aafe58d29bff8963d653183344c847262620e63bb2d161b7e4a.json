{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Event } from '../event.js';\nimport { DisposableStore, toDisposable } from '../lifecycle.js';\nimport { BaseObservable, ConvenientObservable, _setKeepObserved, _setRecomputeInitiallyAndOnChange, subtransaction, transaction } from './base.js';\nimport { DebugNameData } from './debugName.js';\nimport { derived, derivedOpts } from './derived.js';\nimport { getLogger } from './logging.js';\nimport { strictEquals } from '../equals.js';\n/**\n * Represents an efficient observable whose value never changes.\n */\nexport function constObservable(value) {\n  return new ConstObservable(value);\n}\nclass ConstObservable extends ConvenientObservable {\n  constructor(value) {\n    super();\n    this.value = value;\n  }\n  get debugName() {\n    return this.toString();\n  }\n  get() {\n    return this.value;\n  }\n  addObserver(observer) {\n    // NO OP\n  }\n  removeObserver(observer) {\n    // NO OP\n  }\n  toString() {\n    return `Const: ${this.value}`;\n  }\n}\nexport function observableFromEvent(...args) {\n  let owner;\n  let event;\n  let getValue;\n  if (args.length === 3) {\n    [owner, event, getValue] = args;\n  } else {\n    [event, getValue] = args;\n  }\n  return new FromEventObservable(new DebugNameData(owner, undefined, getValue), event, getValue, () => FromEventObservable.globalTransaction, strictEquals);\n}\nexport function observableFromEventOpts(options, event, getValue) {\n  return new FromEventObservable(new DebugNameData(options.owner, options.debugName, options.debugReferenceFn ?? getValue), event, getValue, () => FromEventObservable.globalTransaction, options.equalsFn ?? strictEquals);\n}\nexport class FromEventObservable extends BaseObservable {\n  constructor(_debugNameData, event, _getValue, _getTransaction, _equalityComparator) {\n    super();\n    this._debugNameData = _debugNameData;\n    this.event = event;\n    this._getValue = _getValue;\n    this._getTransaction = _getTransaction;\n    this._equalityComparator = _equalityComparator;\n    this.hasValue = false;\n    this.handleEvent = args => {\n      const newValue = this._getValue(args);\n      const oldValue = this.value;\n      const didChange = !this.hasValue || !this._equalityComparator(oldValue, newValue);\n      let didRunTransaction = false;\n      if (didChange) {\n        this.value = newValue;\n        if (this.hasValue) {\n          didRunTransaction = true;\n          subtransaction(this._getTransaction(), tx => {\n            getLogger()?.handleFromEventObservableTriggered(this, {\n              oldValue,\n              newValue,\n              change: undefined,\n              didChange,\n              hadValue: this.hasValue\n            });\n            for (const o of this.observers) {\n              tx.updateObserver(o, this);\n              o.handleChange(this, undefined);\n            }\n          }, () => {\n            const name = this.getDebugName();\n            return 'Event fired' + (name ? `: ${name}` : '');\n          });\n        }\n        this.hasValue = true;\n      }\n      if (!didRunTransaction) {\n        getLogger()?.handleFromEventObservableTriggered(this, {\n          oldValue,\n          newValue,\n          change: undefined,\n          didChange,\n          hadValue: this.hasValue\n        });\n      }\n    };\n  }\n  getDebugName() {\n    return this._debugNameData.getDebugName(this);\n  }\n  get debugName() {\n    const name = this.getDebugName();\n    return 'From Event' + (name ? `: ${name}` : '');\n  }\n  onFirstObserverAdded() {\n    this.subscription = this.event(this.handleEvent);\n  }\n  onLastObserverRemoved() {\n    this.subscription.dispose();\n    this.subscription = undefined;\n    this.hasValue = false;\n    this.value = undefined;\n  }\n  get() {\n    if (this.subscription) {\n      if (!this.hasValue) {\n        this.handleEvent(undefined);\n      }\n      return this.value;\n    } else {\n      // no cache, as there are no subscribers to keep it updated\n      const value = this._getValue(undefined);\n      return value;\n    }\n  }\n}\n(function (observableFromEvent) {\n  observableFromEvent.Observer = FromEventObservable;\n  function batchEventsGlobally(tx, fn) {\n    let didSet = false;\n    if (FromEventObservable.globalTransaction === undefined) {\n      FromEventObservable.globalTransaction = tx;\n      didSet = true;\n    }\n    try {\n      fn();\n    } finally {\n      if (didSet) {\n        FromEventObservable.globalTransaction = undefined;\n      }\n    }\n  }\n  observableFromEvent.batchEventsGlobally = batchEventsGlobally;\n})(observableFromEvent || (observableFromEvent = {}));\nexport function observableSignalFromEvent(debugName, event) {\n  return new FromEventObservableSignal(debugName, event);\n}\nclass FromEventObservableSignal extends BaseObservable {\n  constructor(debugName, event) {\n    super();\n    this.debugName = debugName;\n    this.event = event;\n    this.handleEvent = () => {\n      transaction(tx => {\n        for (const o of this.observers) {\n          tx.updateObserver(o, this);\n          o.handleChange(this, undefined);\n        }\n      }, () => this.debugName);\n    };\n  }\n  onFirstObserverAdded() {\n    this.subscription = this.event(this.handleEvent);\n  }\n  onLastObserverRemoved() {\n    this.subscription.dispose();\n    this.subscription = undefined;\n  }\n  get() {\n    // NO OP\n  }\n}\nexport function observableSignal(debugNameOrOwner) {\n  if (typeof debugNameOrOwner === 'string') {\n    return new ObservableSignal(debugNameOrOwner);\n  } else {\n    return new ObservableSignal(undefined, debugNameOrOwner);\n  }\n}\nclass ObservableSignal extends BaseObservable {\n  get debugName() {\n    return new DebugNameData(this._owner, this._debugName, undefined).getDebugName(this) ?? 'Observable Signal';\n  }\n  toString() {\n    return this.debugName;\n  }\n  constructor(_debugName, _owner) {\n    super();\n    this._debugName = _debugName;\n    this._owner = _owner;\n  }\n  trigger(tx, change) {\n    if (!tx) {\n      transaction(tx => {\n        this.trigger(tx, change);\n      }, () => `Trigger signal ${this.debugName}`);\n      return;\n    }\n    for (const o of this.observers) {\n      tx.updateObserver(o, this);\n      o.handleChange(this, change);\n    }\n  }\n  get() {\n    // NO OP\n  }\n}\n/**\n * This makes sure the observable is being observed and keeps its cache alive.\n */\nexport function keepObserved(observable) {\n  const o = new KeepAliveObserver(false, undefined);\n  observable.addObserver(o);\n  return toDisposable(() => {\n    observable.removeObserver(o);\n  });\n}\n_setKeepObserved(keepObserved);\n/**\n * This converts the given observable into an autorun.\n */\nexport function recomputeInitiallyAndOnChange(observable, handleValue) {\n  const o = new KeepAliveObserver(true, handleValue);\n  observable.addObserver(o);\n  if (handleValue) {\n    handleValue(observable.get());\n  } else {\n    observable.reportChanges();\n  }\n  return toDisposable(() => {\n    observable.removeObserver(o);\n  });\n}\n_setRecomputeInitiallyAndOnChange(recomputeInitiallyAndOnChange);\nexport class KeepAliveObserver {\n  constructor(_forceRecompute, _handleValue) {\n    this._forceRecompute = _forceRecompute;\n    this._handleValue = _handleValue;\n    this._counter = 0;\n  }\n  beginUpdate(observable) {\n    this._counter++;\n  }\n  endUpdate(observable) {\n    this._counter--;\n    if (this._counter === 0 && this._forceRecompute) {\n      if (this._handleValue) {\n        this._handleValue(observable.get());\n      } else {\n        observable.reportChanges();\n      }\n    }\n  }\n  handlePossibleChange(observable) {\n    // NO OP\n  }\n  handleChange(observable, change) {\n    // NO OP\n  }\n}\nexport function derivedObservableWithCache(owner, computeFn) {\n  let lastValue = undefined;\n  const observable = derivedOpts({\n    owner,\n    debugReferenceFn: computeFn\n  }, reader => {\n    lastValue = computeFn(reader, lastValue);\n    return lastValue;\n  });\n  return observable;\n}\nexport function derivedObservableWithWritableCache(owner, computeFn) {\n  let lastValue = undefined;\n  const onChange = observableSignal('derivedObservableWithWritableCache');\n  const observable = derived(owner, reader => {\n    onChange.read(reader);\n    lastValue = computeFn(reader, lastValue);\n    return lastValue;\n  });\n  return Object.assign(observable, {\n    clearCache: tx => {\n      lastValue = undefined;\n      onChange.trigger(tx);\n    },\n    setCache: (newValue, tx) => {\n      lastValue = newValue;\n      onChange.trigger(tx);\n    }\n  });\n}\n/**\n * When the items array changes, referential equal items are not mapped again.\n */\nexport function mapObservableArrayCached(owner, items, map, keySelector) {\n  let m = new ArrayMap(map, keySelector);\n  const self = derivedOpts({\n    debugReferenceFn: map,\n    owner,\n    onLastObserverRemoved: () => {\n      m.dispose();\n      m = new ArrayMap(map);\n    }\n  }, reader => {\n    m.setItems(items.read(reader));\n    return m.getItems();\n  });\n  return self;\n}\nclass ArrayMap {\n  constructor(_map, _keySelector) {\n    this._map = _map;\n    this._keySelector = _keySelector;\n    this._cache = new Map();\n    this._items = [];\n  }\n  dispose() {\n    this._cache.forEach(entry => entry.store.dispose());\n    this._cache.clear();\n  }\n  setItems(items) {\n    const newItems = [];\n    const itemsToRemove = new Set(this._cache.keys());\n    for (const item of items) {\n      const key = this._keySelector ? this._keySelector(item) : item;\n      let entry = this._cache.get(key);\n      if (!entry) {\n        const store = new DisposableStore();\n        const out = this._map(item, store);\n        entry = {\n          out,\n          store\n        };\n        this._cache.set(key, entry);\n      } else {\n        itemsToRemove.delete(key);\n      }\n      newItems.push(entry.out);\n    }\n    for (const item of itemsToRemove) {\n      const entry = this._cache.get(item);\n      entry.store.dispose();\n      this._cache.delete(item);\n    }\n    this._items = newItems;\n  }\n  getItems() {\n    return this._items;\n  }\n}\nexport class ValueWithChangeEventFromObservable {\n  constructor(observable) {\n    this.observable = observable;\n  }\n  get onDidChange() {\n    return Event.fromObservableLight(this.observable);\n  }\n  get value() {\n    return this.observable.get();\n  }\n}\nexport function observableFromValueWithChangeEvent(owner, value) {\n  if (value instanceof ValueWithChangeEventFromObservable) {\n    return value.observable;\n  }\n  return observableFromEvent(owner, value.onDidChange, () => value.value);\n}\n/**\n * Works like a derived.\n * However, if the value is not undefined, it is cached and will not be recomputed anymore.\n * In that case, the derived will unsubscribe from its dependencies.\n*/\nexport function derivedConstOnceDefined(owner, fn) {\n  return derivedObservableWithCache(owner, (reader, lastValue) => lastValue ?? fn(reader));\n}", "map": {"version": 3, "names": ["Event", "DisposableStore", "toDisposable", "BaseObservable", "ConvenientObservable", "_setKeepObserved", "_setRecomputeInitiallyAndOnChange", "subtransaction", "transaction", "DebugNameData", "derived", "derivedOpts", "<PERSON><PERSON><PERSON><PERSON>", "strictEquals", "constObservable", "value", "ConstObservable", "constructor", "debugName", "toString", "get", "addObserver", "observer", "removeObserver", "observableFromEvent", "args", "owner", "event", "getValue", "length", "FromEventObservable", "undefined", "globalTransaction", "observableFromEventOpts", "options", "debugReferenceFn", "equalsFn", "_debugNameData", "_getValue", "_getTransaction", "_equalityComparator", "hasValue", "handleEvent", "newValue", "oldValue", "<PERSON><PERSON><PERSON><PERSON>", "didRunTransaction", "tx", "handleFromEventObservableTriggered", "change", "hadValue", "o", "observers", "updateObserver", "handleChange", "name", "getDebugName", "onFirstObserverAdded", "subscription", "onLastObserverRemoved", "dispose", "Observer", "batchEventsGlobally", "fn", "didSet", "observableSignalFromEvent", "FromEventObservableSignal", "observableSignal", "debugNameOrOwner", "ObservableSignal", "_owner", "_debugName", "trigger", "keepObserved", "observable", "KeepAliveObserver", "recomputeInitiallyAndOnChange", "handleValue", "reportChanges", "_forceRecompute", "_handleValue", "_counter", "beginUpdate", "endUpdate", "handlePossibleChange", "derivedObservableWithCache", "computeFn", "lastValue", "reader", "derivedObservableWithWritableCache", "onChange", "read", "Object", "assign", "clearCache", "setCache", "mapObservableArrayCached", "items", "map", "keySelector", "m", "ArrayMap", "self", "setItems", "getItems", "_map", "_keySelector", "_cache", "Map", "_items", "for<PERSON>ach", "entry", "store", "clear", "newItems", "itemsToRemove", "Set", "keys", "item", "key", "out", "set", "delete", "push", "ValueWithChangeEventFromObservable", "onDidChange", "fromObservableLight", "observableFromValueWithChangeEvent", "derivedConstOnceDefined"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/common/observableInternal/utils.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Event } from '../event.js';\nimport { DisposableStore, toDisposable } from '../lifecycle.js';\nimport { BaseObservable, ConvenientObservable, _setKeepObserved, _setRecomputeInitiallyAndOnChange, subtransaction, transaction } from './base.js';\nimport { DebugNameData } from './debugName.js';\nimport { derived, derivedOpts } from './derived.js';\nimport { getLogger } from './logging.js';\nimport { strictEquals } from '../equals.js';\n/**\n * Represents an efficient observable whose value never changes.\n */\nexport function constObservable(value) {\n    return new ConstObservable(value);\n}\nclass ConstObservable extends ConvenientObservable {\n    constructor(value) {\n        super();\n        this.value = value;\n    }\n    get debugName() {\n        return this.toString();\n    }\n    get() {\n        return this.value;\n    }\n    addObserver(observer) {\n        // NO OP\n    }\n    removeObserver(observer) {\n        // NO OP\n    }\n    toString() {\n        return `Const: ${this.value}`;\n    }\n}\nexport function observableFromEvent(...args) {\n    let owner;\n    let event;\n    let getValue;\n    if (args.length === 3) {\n        [owner, event, getValue] = args;\n    }\n    else {\n        [event, getValue] = args;\n    }\n    return new FromEventObservable(new DebugNameData(owner, undefined, getValue), event, getValue, () => FromEventObservable.globalTransaction, strictEquals);\n}\nexport function observableFromEventOpts(options, event, getValue) {\n    return new FromEventObservable(new DebugNameData(options.owner, options.debugName, options.debugReferenceFn ?? getValue), event, getValue, () => FromEventObservable.globalTransaction, options.equalsFn ?? strictEquals);\n}\nexport class FromEventObservable extends BaseObservable {\n    constructor(_debugNameData, event, _getValue, _getTransaction, _equalityComparator) {\n        super();\n        this._debugNameData = _debugNameData;\n        this.event = event;\n        this._getValue = _getValue;\n        this._getTransaction = _getTransaction;\n        this._equalityComparator = _equalityComparator;\n        this.hasValue = false;\n        this.handleEvent = (args) => {\n            const newValue = this._getValue(args);\n            const oldValue = this.value;\n            const didChange = !this.hasValue || !(this._equalityComparator(oldValue, newValue));\n            let didRunTransaction = false;\n            if (didChange) {\n                this.value = newValue;\n                if (this.hasValue) {\n                    didRunTransaction = true;\n                    subtransaction(this._getTransaction(), (tx) => {\n                        getLogger()?.handleFromEventObservableTriggered(this, { oldValue, newValue, change: undefined, didChange, hadValue: this.hasValue });\n                        for (const o of this.observers) {\n                            tx.updateObserver(o, this);\n                            o.handleChange(this, undefined);\n                        }\n                    }, () => {\n                        const name = this.getDebugName();\n                        return 'Event fired' + (name ? `: ${name}` : '');\n                    });\n                }\n                this.hasValue = true;\n            }\n            if (!didRunTransaction) {\n                getLogger()?.handleFromEventObservableTriggered(this, { oldValue, newValue, change: undefined, didChange, hadValue: this.hasValue });\n            }\n        };\n    }\n    getDebugName() {\n        return this._debugNameData.getDebugName(this);\n    }\n    get debugName() {\n        const name = this.getDebugName();\n        return 'From Event' + (name ? `: ${name}` : '');\n    }\n    onFirstObserverAdded() {\n        this.subscription = this.event(this.handleEvent);\n    }\n    onLastObserverRemoved() {\n        this.subscription.dispose();\n        this.subscription = undefined;\n        this.hasValue = false;\n        this.value = undefined;\n    }\n    get() {\n        if (this.subscription) {\n            if (!this.hasValue) {\n                this.handleEvent(undefined);\n            }\n            return this.value;\n        }\n        else {\n            // no cache, as there are no subscribers to keep it updated\n            const value = this._getValue(undefined);\n            return value;\n        }\n    }\n}\n(function (observableFromEvent) {\n    observableFromEvent.Observer = FromEventObservable;\n    function batchEventsGlobally(tx, fn) {\n        let didSet = false;\n        if (FromEventObservable.globalTransaction === undefined) {\n            FromEventObservable.globalTransaction = tx;\n            didSet = true;\n        }\n        try {\n            fn();\n        }\n        finally {\n            if (didSet) {\n                FromEventObservable.globalTransaction = undefined;\n            }\n        }\n    }\n    observableFromEvent.batchEventsGlobally = batchEventsGlobally;\n})(observableFromEvent || (observableFromEvent = {}));\nexport function observableSignalFromEvent(debugName, event) {\n    return new FromEventObservableSignal(debugName, event);\n}\nclass FromEventObservableSignal extends BaseObservable {\n    constructor(debugName, event) {\n        super();\n        this.debugName = debugName;\n        this.event = event;\n        this.handleEvent = () => {\n            transaction((tx) => {\n                for (const o of this.observers) {\n                    tx.updateObserver(o, this);\n                    o.handleChange(this, undefined);\n                }\n            }, () => this.debugName);\n        };\n    }\n    onFirstObserverAdded() {\n        this.subscription = this.event(this.handleEvent);\n    }\n    onLastObserverRemoved() {\n        this.subscription.dispose();\n        this.subscription = undefined;\n    }\n    get() {\n        // NO OP\n    }\n}\nexport function observableSignal(debugNameOrOwner) {\n    if (typeof debugNameOrOwner === 'string') {\n        return new ObservableSignal(debugNameOrOwner);\n    }\n    else {\n        return new ObservableSignal(undefined, debugNameOrOwner);\n    }\n}\nclass ObservableSignal extends BaseObservable {\n    get debugName() {\n        return new DebugNameData(this._owner, this._debugName, undefined).getDebugName(this) ?? 'Observable Signal';\n    }\n    toString() {\n        return this.debugName;\n    }\n    constructor(_debugName, _owner) {\n        super();\n        this._debugName = _debugName;\n        this._owner = _owner;\n    }\n    trigger(tx, change) {\n        if (!tx) {\n            transaction(tx => {\n                this.trigger(tx, change);\n            }, () => `Trigger signal ${this.debugName}`);\n            return;\n        }\n        for (const o of this.observers) {\n            tx.updateObserver(o, this);\n            o.handleChange(this, change);\n        }\n    }\n    get() {\n        // NO OP\n    }\n}\n/**\n * This makes sure the observable is being observed and keeps its cache alive.\n */\nexport function keepObserved(observable) {\n    const o = new KeepAliveObserver(false, undefined);\n    observable.addObserver(o);\n    return toDisposable(() => {\n        observable.removeObserver(o);\n    });\n}\n_setKeepObserved(keepObserved);\n/**\n * This converts the given observable into an autorun.\n */\nexport function recomputeInitiallyAndOnChange(observable, handleValue) {\n    const o = new KeepAliveObserver(true, handleValue);\n    observable.addObserver(o);\n    if (handleValue) {\n        handleValue(observable.get());\n    }\n    else {\n        observable.reportChanges();\n    }\n    return toDisposable(() => {\n        observable.removeObserver(o);\n    });\n}\n_setRecomputeInitiallyAndOnChange(recomputeInitiallyAndOnChange);\nexport class KeepAliveObserver {\n    constructor(_forceRecompute, _handleValue) {\n        this._forceRecompute = _forceRecompute;\n        this._handleValue = _handleValue;\n        this._counter = 0;\n    }\n    beginUpdate(observable) {\n        this._counter++;\n    }\n    endUpdate(observable) {\n        this._counter--;\n        if (this._counter === 0 && this._forceRecompute) {\n            if (this._handleValue) {\n                this._handleValue(observable.get());\n            }\n            else {\n                observable.reportChanges();\n            }\n        }\n    }\n    handlePossibleChange(observable) {\n        // NO OP\n    }\n    handleChange(observable, change) {\n        // NO OP\n    }\n}\nexport function derivedObservableWithCache(owner, computeFn) {\n    let lastValue = undefined;\n    const observable = derivedOpts({ owner, debugReferenceFn: computeFn }, reader => {\n        lastValue = computeFn(reader, lastValue);\n        return lastValue;\n    });\n    return observable;\n}\nexport function derivedObservableWithWritableCache(owner, computeFn) {\n    let lastValue = undefined;\n    const onChange = observableSignal('derivedObservableWithWritableCache');\n    const observable = derived(owner, reader => {\n        onChange.read(reader);\n        lastValue = computeFn(reader, lastValue);\n        return lastValue;\n    });\n    return Object.assign(observable, {\n        clearCache: (tx) => {\n            lastValue = undefined;\n            onChange.trigger(tx);\n        },\n        setCache: (newValue, tx) => {\n            lastValue = newValue;\n            onChange.trigger(tx);\n        }\n    });\n}\n/**\n * When the items array changes, referential equal items are not mapped again.\n */\nexport function mapObservableArrayCached(owner, items, map, keySelector) {\n    let m = new ArrayMap(map, keySelector);\n    const self = derivedOpts({\n        debugReferenceFn: map,\n        owner,\n        onLastObserverRemoved: () => {\n            m.dispose();\n            m = new ArrayMap(map);\n        }\n    }, (reader) => {\n        m.setItems(items.read(reader));\n        return m.getItems();\n    });\n    return self;\n}\nclass ArrayMap {\n    constructor(_map, _keySelector) {\n        this._map = _map;\n        this._keySelector = _keySelector;\n        this._cache = new Map();\n        this._items = [];\n    }\n    dispose() {\n        this._cache.forEach(entry => entry.store.dispose());\n        this._cache.clear();\n    }\n    setItems(items) {\n        const newItems = [];\n        const itemsToRemove = new Set(this._cache.keys());\n        for (const item of items) {\n            const key = this._keySelector ? this._keySelector(item) : item;\n            let entry = this._cache.get(key);\n            if (!entry) {\n                const store = new DisposableStore();\n                const out = this._map(item, store);\n                entry = { out, store };\n                this._cache.set(key, entry);\n            }\n            else {\n                itemsToRemove.delete(key);\n            }\n            newItems.push(entry.out);\n        }\n        for (const item of itemsToRemove) {\n            const entry = this._cache.get(item);\n            entry.store.dispose();\n            this._cache.delete(item);\n        }\n        this._items = newItems;\n    }\n    getItems() {\n        return this._items;\n    }\n}\nexport class ValueWithChangeEventFromObservable {\n    constructor(observable) {\n        this.observable = observable;\n    }\n    get onDidChange() {\n        return Event.fromObservableLight(this.observable);\n    }\n    get value() {\n        return this.observable.get();\n    }\n}\nexport function observableFromValueWithChangeEvent(owner, value) {\n    if (value instanceof ValueWithChangeEventFromObservable) {\n        return value.observable;\n    }\n    return observableFromEvent(owner, value.onDidChange, () => value.value);\n}\n/**\n * Works like a derived.\n * However, if the value is not undefined, it is cached and will not be recomputed anymore.\n * In that case, the derived will unsubscribe from its dependencies.\n*/\nexport function derivedConstOnceDefined(owner, fn) {\n    return derivedObservableWithCache(owner, (reader, lastValue) => lastValue ?? fn(reader));\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,KAAK,QAAQ,aAAa;AACnC,SAASC,eAAe,EAAEC,YAAY,QAAQ,iBAAiB;AAC/D,SAASC,cAAc,EAAEC,oBAAoB,EAAEC,gBAAgB,EAAEC,iCAAiC,EAAEC,cAAc,EAAEC,WAAW,QAAQ,WAAW;AAClJ,SAASC,aAAa,QAAQ,gBAAgB;AAC9C,SAASC,OAAO,EAAEC,WAAW,QAAQ,cAAc;AACnD,SAASC,SAAS,QAAQ,cAAc;AACxC,SAASC,YAAY,QAAQ,cAAc;AAC3C;AACA;AACA;AACA,OAAO,SAASC,eAAeA,CAACC,KAAK,EAAE;EACnC,OAAO,IAAIC,eAAe,CAACD,KAAK,CAAC;AACrC;AACA,MAAMC,eAAe,SAASZ,oBAAoB,CAAC;EAC/Ca,WAAWA,CAACF,KAAK,EAAE;IACf,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,KAAK,GAAGA,KAAK;EACtB;EACA,IAAIG,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACC,QAAQ,CAAC,CAAC;EAC1B;EACAC,GAAGA,CAAA,EAAG;IACF,OAAO,IAAI,CAACL,KAAK;EACrB;EACAM,WAAWA,CAACC,QAAQ,EAAE;IAClB;EAAA;EAEJC,cAAcA,CAACD,QAAQ,EAAE;IACrB;EAAA;EAEJH,QAAQA,CAAA,EAAG;IACP,OAAO,UAAU,IAAI,CAACJ,KAAK,EAAE;EACjC;AACJ;AACA,OAAO,SAASS,mBAAmBA,CAAC,GAAGC,IAAI,EAAE;EACzC,IAAIC,KAAK;EACT,IAAIC,KAAK;EACT,IAAIC,QAAQ;EACZ,IAAIH,IAAI,CAACI,MAAM,KAAK,CAAC,EAAE;IACnB,CAACH,KAAK,EAAEC,KAAK,EAAEC,QAAQ,CAAC,GAAGH,IAAI;EACnC,CAAC,MACI;IACD,CAACE,KAAK,EAAEC,QAAQ,CAAC,GAAGH,IAAI;EAC5B;EACA,OAAO,IAAIK,mBAAmB,CAAC,IAAIrB,aAAa,CAACiB,KAAK,EAAEK,SAAS,EAAEH,QAAQ,CAAC,EAAED,KAAK,EAAEC,QAAQ,EAAE,MAAME,mBAAmB,CAACE,iBAAiB,EAAEnB,YAAY,CAAC;AAC7J;AACA,OAAO,SAASoB,uBAAuBA,CAACC,OAAO,EAAEP,KAAK,EAAEC,QAAQ,EAAE;EAC9D,OAAO,IAAIE,mBAAmB,CAAC,IAAIrB,aAAa,CAACyB,OAAO,CAACR,KAAK,EAAEQ,OAAO,CAAChB,SAAS,EAAEgB,OAAO,CAACC,gBAAgB,IAAIP,QAAQ,CAAC,EAAED,KAAK,EAAEC,QAAQ,EAAE,MAAME,mBAAmB,CAACE,iBAAiB,EAAEE,OAAO,CAACE,QAAQ,IAAIvB,YAAY,CAAC;AAC7N;AACA,OAAO,MAAMiB,mBAAmB,SAAS3B,cAAc,CAAC;EACpDc,WAAWA,CAACoB,cAAc,EAAEV,KAAK,EAAEW,SAAS,EAAEC,eAAe,EAAEC,mBAAmB,EAAE;IAChF,KAAK,CAAC,CAAC;IACP,IAAI,CAACH,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACV,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACW,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,WAAW,GAAIjB,IAAI,IAAK;MACzB,MAAMkB,QAAQ,GAAG,IAAI,CAACL,SAAS,CAACb,IAAI,CAAC;MACrC,MAAMmB,QAAQ,GAAG,IAAI,CAAC7B,KAAK;MAC3B,MAAM8B,SAAS,GAAG,CAAC,IAAI,CAACJ,QAAQ,IAAI,CAAE,IAAI,CAACD,mBAAmB,CAACI,QAAQ,EAAED,QAAQ,CAAE;MACnF,IAAIG,iBAAiB,GAAG,KAAK;MAC7B,IAAID,SAAS,EAAE;QACX,IAAI,CAAC9B,KAAK,GAAG4B,QAAQ;QACrB,IAAI,IAAI,CAACF,QAAQ,EAAE;UACfK,iBAAiB,GAAG,IAAI;UACxBvC,cAAc,CAAC,IAAI,CAACgC,eAAe,CAAC,CAAC,EAAGQ,EAAE,IAAK;YAC3CnC,SAAS,CAAC,CAAC,EAAEoC,kCAAkC,CAAC,IAAI,EAAE;cAAEJ,QAAQ;cAAED,QAAQ;cAAEM,MAAM,EAAElB,SAAS;cAAEc,SAAS;cAAEK,QAAQ,EAAE,IAAI,CAACT;YAAS,CAAC,CAAC;YACpI,KAAK,MAAMU,CAAC,IAAI,IAAI,CAACC,SAAS,EAAE;cAC5BL,EAAE,CAACM,cAAc,CAACF,CAAC,EAAE,IAAI,CAAC;cAC1BA,CAAC,CAACG,YAAY,CAAC,IAAI,EAAEvB,SAAS,CAAC;YACnC;UACJ,CAAC,EAAE,MAAM;YACL,MAAMwB,IAAI,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;YAChC,OAAO,aAAa,IAAID,IAAI,GAAG,KAAKA,IAAI,EAAE,GAAG,EAAE,CAAC;UACpD,CAAC,CAAC;QACN;QACA,IAAI,CAACd,QAAQ,GAAG,IAAI;MACxB;MACA,IAAI,CAACK,iBAAiB,EAAE;QACpBlC,SAAS,CAAC,CAAC,EAAEoC,kCAAkC,CAAC,IAAI,EAAE;UAAEJ,QAAQ;UAAED,QAAQ;UAAEM,MAAM,EAAElB,SAAS;UAAEc,SAAS;UAAEK,QAAQ,EAAE,IAAI,CAACT;QAAS,CAAC,CAAC;MACxI;IACJ,CAAC;EACL;EACAe,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAACnB,cAAc,CAACmB,YAAY,CAAC,IAAI,CAAC;EACjD;EACA,IAAItC,SAASA,CAAA,EAAG;IACZ,MAAMqC,IAAI,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IAChC,OAAO,YAAY,IAAID,IAAI,GAAG,KAAKA,IAAI,EAAE,GAAG,EAAE,CAAC;EACnD;EACAE,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACC,YAAY,GAAG,IAAI,CAAC/B,KAAK,CAAC,IAAI,CAACe,WAAW,CAAC;EACpD;EACAiB,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAACD,YAAY,CAACE,OAAO,CAAC,CAAC;IAC3B,IAAI,CAACF,YAAY,GAAG3B,SAAS;IAC7B,IAAI,CAACU,QAAQ,GAAG,KAAK;IACrB,IAAI,CAAC1B,KAAK,GAAGgB,SAAS;EAC1B;EACAX,GAAGA,CAAA,EAAG;IACF,IAAI,IAAI,CAACsC,YAAY,EAAE;MACnB,IAAI,CAAC,IAAI,CAACjB,QAAQ,EAAE;QAChB,IAAI,CAACC,WAAW,CAACX,SAAS,CAAC;MAC/B;MACA,OAAO,IAAI,CAAChB,KAAK;IACrB,CAAC,MACI;MACD;MACA,MAAMA,KAAK,GAAG,IAAI,CAACuB,SAAS,CAACP,SAAS,CAAC;MACvC,OAAOhB,KAAK;IAChB;EACJ;AACJ;AACA,CAAC,UAAUS,mBAAmB,EAAE;EAC5BA,mBAAmB,CAACqC,QAAQ,GAAG/B,mBAAmB;EAClD,SAASgC,mBAAmBA,CAACf,EAAE,EAAEgB,EAAE,EAAE;IACjC,IAAIC,MAAM,GAAG,KAAK;IAClB,IAAIlC,mBAAmB,CAACE,iBAAiB,KAAKD,SAAS,EAAE;MACrDD,mBAAmB,CAACE,iBAAiB,GAAGe,EAAE;MAC1CiB,MAAM,GAAG,IAAI;IACjB;IACA,IAAI;MACAD,EAAE,CAAC,CAAC;IACR,CAAC,SACO;MACJ,IAAIC,MAAM,EAAE;QACRlC,mBAAmB,CAACE,iBAAiB,GAAGD,SAAS;MACrD;IACJ;EACJ;EACAP,mBAAmB,CAACsC,mBAAmB,GAAGA,mBAAmB;AACjE,CAAC,EAAEtC,mBAAmB,KAAKA,mBAAmB,GAAG,CAAC,CAAC,CAAC,CAAC;AACrD,OAAO,SAASyC,yBAAyBA,CAAC/C,SAAS,EAAES,KAAK,EAAE;EACxD,OAAO,IAAIuC,yBAAyB,CAAChD,SAAS,EAAES,KAAK,CAAC;AAC1D;AACA,MAAMuC,yBAAyB,SAAS/D,cAAc,CAAC;EACnDc,WAAWA,CAACC,SAAS,EAAES,KAAK,EAAE;IAC1B,KAAK,CAAC,CAAC;IACP,IAAI,CAACT,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACS,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACe,WAAW,GAAG,MAAM;MACrBlC,WAAW,CAAEuC,EAAE,IAAK;QAChB,KAAK,MAAMI,CAAC,IAAI,IAAI,CAACC,SAAS,EAAE;UAC5BL,EAAE,CAACM,cAAc,CAACF,CAAC,EAAE,IAAI,CAAC;UAC1BA,CAAC,CAACG,YAAY,CAAC,IAAI,EAAEvB,SAAS,CAAC;QACnC;MACJ,CAAC,EAAE,MAAM,IAAI,CAACb,SAAS,CAAC;IAC5B,CAAC;EACL;EACAuC,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACC,YAAY,GAAG,IAAI,CAAC/B,KAAK,CAAC,IAAI,CAACe,WAAW,CAAC;EACpD;EACAiB,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAACD,YAAY,CAACE,OAAO,CAAC,CAAC;IAC3B,IAAI,CAACF,YAAY,GAAG3B,SAAS;EACjC;EACAX,GAAGA,CAAA,EAAG;IACF;EAAA;AAER;AACA,OAAO,SAAS+C,gBAAgBA,CAACC,gBAAgB,EAAE;EAC/C,IAAI,OAAOA,gBAAgB,KAAK,QAAQ,EAAE;IACtC,OAAO,IAAIC,gBAAgB,CAACD,gBAAgB,CAAC;EACjD,CAAC,MACI;IACD,OAAO,IAAIC,gBAAgB,CAACtC,SAAS,EAAEqC,gBAAgB,CAAC;EAC5D;AACJ;AACA,MAAMC,gBAAgB,SAASlE,cAAc,CAAC;EAC1C,IAAIe,SAASA,CAAA,EAAG;IACZ,OAAO,IAAIT,aAAa,CAAC,IAAI,CAAC6D,MAAM,EAAE,IAAI,CAACC,UAAU,EAAExC,SAAS,CAAC,CAACyB,YAAY,CAAC,IAAI,CAAC,IAAI,mBAAmB;EAC/G;EACArC,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACD,SAAS;EACzB;EACAD,WAAWA,CAACsD,UAAU,EAAED,MAAM,EAAE;IAC5B,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACD,MAAM,GAAGA,MAAM;EACxB;EACAE,OAAOA,CAACzB,EAAE,EAAEE,MAAM,EAAE;IAChB,IAAI,CAACF,EAAE,EAAE;MACLvC,WAAW,CAACuC,EAAE,IAAI;QACd,IAAI,CAACyB,OAAO,CAACzB,EAAE,EAAEE,MAAM,CAAC;MAC5B,CAAC,EAAE,MAAM,kBAAkB,IAAI,CAAC/B,SAAS,EAAE,CAAC;MAC5C;IACJ;IACA,KAAK,MAAMiC,CAAC,IAAI,IAAI,CAACC,SAAS,EAAE;MAC5BL,EAAE,CAACM,cAAc,CAACF,CAAC,EAAE,IAAI,CAAC;MAC1BA,CAAC,CAACG,YAAY,CAAC,IAAI,EAAEL,MAAM,CAAC;IAChC;EACJ;EACA7B,GAAGA,CAAA,EAAG;IACF;EAAA;AAER;AACA;AACA;AACA;AACA,OAAO,SAASqD,YAAYA,CAACC,UAAU,EAAE;EACrC,MAAMvB,CAAC,GAAG,IAAIwB,iBAAiB,CAAC,KAAK,EAAE5C,SAAS,CAAC;EACjD2C,UAAU,CAACrD,WAAW,CAAC8B,CAAC,CAAC;EACzB,OAAOjD,YAAY,CAAC,MAAM;IACtBwE,UAAU,CAACnD,cAAc,CAAC4B,CAAC,CAAC;EAChC,CAAC,CAAC;AACN;AACA9C,gBAAgB,CAACoE,YAAY,CAAC;AAC9B;AACA;AACA;AACA,OAAO,SAASG,6BAA6BA,CAACF,UAAU,EAAEG,WAAW,EAAE;EACnE,MAAM1B,CAAC,GAAG,IAAIwB,iBAAiB,CAAC,IAAI,EAAEE,WAAW,CAAC;EAClDH,UAAU,CAACrD,WAAW,CAAC8B,CAAC,CAAC;EACzB,IAAI0B,WAAW,EAAE;IACbA,WAAW,CAACH,UAAU,CAACtD,GAAG,CAAC,CAAC,CAAC;EACjC,CAAC,MACI;IACDsD,UAAU,CAACI,aAAa,CAAC,CAAC;EAC9B;EACA,OAAO5E,YAAY,CAAC,MAAM;IACtBwE,UAAU,CAACnD,cAAc,CAAC4B,CAAC,CAAC;EAChC,CAAC,CAAC;AACN;AACA7C,iCAAiC,CAACsE,6BAA6B,CAAC;AAChE,OAAO,MAAMD,iBAAiB,CAAC;EAC3B1D,WAAWA,CAAC8D,eAAe,EAAEC,YAAY,EAAE;IACvC,IAAI,CAACD,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,QAAQ,GAAG,CAAC;EACrB;EACAC,WAAWA,CAACR,UAAU,EAAE;IACpB,IAAI,CAACO,QAAQ,EAAE;EACnB;EACAE,SAASA,CAACT,UAAU,EAAE;IAClB,IAAI,CAACO,QAAQ,EAAE;IACf,IAAI,IAAI,CAACA,QAAQ,KAAK,CAAC,IAAI,IAAI,CAACF,eAAe,EAAE;MAC7C,IAAI,IAAI,CAACC,YAAY,EAAE;QACnB,IAAI,CAACA,YAAY,CAACN,UAAU,CAACtD,GAAG,CAAC,CAAC,CAAC;MACvC,CAAC,MACI;QACDsD,UAAU,CAACI,aAAa,CAAC,CAAC;MAC9B;IACJ;EACJ;EACAM,oBAAoBA,CAACV,UAAU,EAAE;IAC7B;EAAA;EAEJpB,YAAYA,CAACoB,UAAU,EAAEzB,MAAM,EAAE;IAC7B;EAAA;AAER;AACA,OAAO,SAASoC,0BAA0BA,CAAC3D,KAAK,EAAE4D,SAAS,EAAE;EACzD,IAAIC,SAAS,GAAGxD,SAAS;EACzB,MAAM2C,UAAU,GAAG/D,WAAW,CAAC;IAAEe,KAAK;IAAES,gBAAgB,EAAEmD;EAAU,CAAC,EAAEE,MAAM,IAAI;IAC7ED,SAAS,GAAGD,SAAS,CAACE,MAAM,EAAED,SAAS,CAAC;IACxC,OAAOA,SAAS;EACpB,CAAC,CAAC;EACF,OAAOb,UAAU;AACrB;AACA,OAAO,SAASe,kCAAkCA,CAAC/D,KAAK,EAAE4D,SAAS,EAAE;EACjE,IAAIC,SAAS,GAAGxD,SAAS;EACzB,MAAM2D,QAAQ,GAAGvB,gBAAgB,CAAC,oCAAoC,CAAC;EACvE,MAAMO,UAAU,GAAGhE,OAAO,CAACgB,KAAK,EAAE8D,MAAM,IAAI;IACxCE,QAAQ,CAACC,IAAI,CAACH,MAAM,CAAC;IACrBD,SAAS,GAAGD,SAAS,CAACE,MAAM,EAAED,SAAS,CAAC;IACxC,OAAOA,SAAS;EACpB,CAAC,CAAC;EACF,OAAOK,MAAM,CAACC,MAAM,CAACnB,UAAU,EAAE;IAC7BoB,UAAU,EAAG/C,EAAE,IAAK;MAChBwC,SAAS,GAAGxD,SAAS;MACrB2D,QAAQ,CAAClB,OAAO,CAACzB,EAAE,CAAC;IACxB,CAAC;IACDgD,QAAQ,EAAEA,CAACpD,QAAQ,EAAEI,EAAE,KAAK;MACxBwC,SAAS,GAAG5C,QAAQ;MACpB+C,QAAQ,CAAClB,OAAO,CAACzB,EAAE,CAAC;IACxB;EACJ,CAAC,CAAC;AACN;AACA;AACA;AACA;AACA,OAAO,SAASiD,wBAAwBA,CAACtE,KAAK,EAAEuE,KAAK,EAAEC,GAAG,EAAEC,WAAW,EAAE;EACrE,IAAIC,CAAC,GAAG,IAAIC,QAAQ,CAACH,GAAG,EAAEC,WAAW,CAAC;EACtC,MAAMG,IAAI,GAAG3F,WAAW,CAAC;IACrBwB,gBAAgB,EAAE+D,GAAG;IACrBxE,KAAK;IACLiC,qBAAqB,EAAEA,CAAA,KAAM;MACzByC,CAAC,CAACxC,OAAO,CAAC,CAAC;MACXwC,CAAC,GAAG,IAAIC,QAAQ,CAACH,GAAG,CAAC;IACzB;EACJ,CAAC,EAAGV,MAAM,IAAK;IACXY,CAAC,CAACG,QAAQ,CAACN,KAAK,CAACN,IAAI,CAACH,MAAM,CAAC,CAAC;IAC9B,OAAOY,CAAC,CAACI,QAAQ,CAAC,CAAC;EACvB,CAAC,CAAC;EACF,OAAOF,IAAI;AACf;AACA,MAAMD,QAAQ,CAAC;EACXpF,WAAWA,CAACwF,IAAI,EAAEC,YAAY,EAAE;IAC5B,IAAI,CAACD,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,MAAM,GAAG,IAAIC,GAAG,CAAC,CAAC;IACvB,IAAI,CAACC,MAAM,GAAG,EAAE;EACpB;EACAjD,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC+C,MAAM,CAACG,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACpD,OAAO,CAAC,CAAC,CAAC;IACnD,IAAI,CAAC+C,MAAM,CAACM,KAAK,CAAC,CAAC;EACvB;EACAV,QAAQA,CAACN,KAAK,EAAE;IACZ,MAAMiB,QAAQ,GAAG,EAAE;IACnB,MAAMC,aAAa,GAAG,IAAIC,GAAG,CAAC,IAAI,CAACT,MAAM,CAACU,IAAI,CAAC,CAAC,CAAC;IACjD,KAAK,MAAMC,IAAI,IAAIrB,KAAK,EAAE;MACtB,MAAMsB,GAAG,GAAG,IAAI,CAACb,YAAY,GAAG,IAAI,CAACA,YAAY,CAACY,IAAI,CAAC,GAAGA,IAAI;MAC9D,IAAIP,KAAK,GAAG,IAAI,CAACJ,MAAM,CAACvF,GAAG,CAACmG,GAAG,CAAC;MAChC,IAAI,CAACR,KAAK,EAAE;QACR,MAAMC,KAAK,GAAG,IAAI/G,eAAe,CAAC,CAAC;QACnC,MAAMuH,GAAG,GAAG,IAAI,CAACf,IAAI,CAACa,IAAI,EAAEN,KAAK,CAAC;QAClCD,KAAK,GAAG;UAAES,GAAG;UAAER;QAAM,CAAC;QACtB,IAAI,CAACL,MAAM,CAACc,GAAG,CAACF,GAAG,EAAER,KAAK,CAAC;MAC/B,CAAC,MACI;QACDI,aAAa,CAACO,MAAM,CAACH,GAAG,CAAC;MAC7B;MACAL,QAAQ,CAACS,IAAI,CAACZ,KAAK,CAACS,GAAG,CAAC;IAC5B;IACA,KAAK,MAAMF,IAAI,IAAIH,aAAa,EAAE;MAC9B,MAAMJ,KAAK,GAAG,IAAI,CAACJ,MAAM,CAACvF,GAAG,CAACkG,IAAI,CAAC;MACnCP,KAAK,CAACC,KAAK,CAACpD,OAAO,CAAC,CAAC;MACrB,IAAI,CAAC+C,MAAM,CAACe,MAAM,CAACJ,IAAI,CAAC;IAC5B;IACA,IAAI,CAACT,MAAM,GAAGK,QAAQ;EAC1B;EACAV,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACK,MAAM;EACtB;AACJ;AACA,OAAO,MAAMe,kCAAkC,CAAC;EAC5C3G,WAAWA,CAACyD,UAAU,EAAE;IACpB,IAAI,CAACA,UAAU,GAAGA,UAAU;EAChC;EACA,IAAImD,WAAWA,CAAA,EAAG;IACd,OAAO7H,KAAK,CAAC8H,mBAAmB,CAAC,IAAI,CAACpD,UAAU,CAAC;EACrD;EACA,IAAI3D,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAAC2D,UAAU,CAACtD,GAAG,CAAC,CAAC;EAChC;AACJ;AACA,OAAO,SAAS2G,kCAAkCA,CAACrG,KAAK,EAAEX,KAAK,EAAE;EAC7D,IAAIA,KAAK,YAAY6G,kCAAkC,EAAE;IACrD,OAAO7G,KAAK,CAAC2D,UAAU;EAC3B;EACA,OAAOlD,mBAAmB,CAACE,KAAK,EAAEX,KAAK,CAAC8G,WAAW,EAAE,MAAM9G,KAAK,CAACA,KAAK,CAAC;AAC3E;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASiH,uBAAuBA,CAACtG,KAAK,EAAEqC,EAAE,EAAE;EAC/C,OAAOsB,0BAA0B,CAAC3D,KAAK,EAAE,CAAC8D,MAAM,EAAED,SAAS,KAAKA,SAAS,IAAIxB,EAAE,CAACyB,MAAM,CAAC,CAAC;AAC5F", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}