{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport function clamp(value, min, max) {\n  return Math.min(Math.max(value, min), max);\n}\nexport class MovingAverage {\n  constructor() {\n    this._n = 1;\n    this._val = 0;\n  }\n  update(value) {\n    this._val = this._val + (value - this._val) / this._n;\n    this._n += 1;\n    return this._val;\n  }\n  get value() {\n    return this._val;\n  }\n}\nexport class SlidingWindowAverage {\n  constructor(size) {\n    this._n = 0;\n    this._val = 0;\n    this._values = [];\n    this._index = 0;\n    this._sum = 0;\n    this._values = new Array(size);\n    this._values.fill(0, 0, size);\n  }\n  update(value) {\n    const oldValue = this._values[this._index];\n    this._values[this._index] = value;\n    this._index = (this._index + 1) % this._values.length;\n    this._sum -= oldValue;\n    this._sum += value;\n    if (this._n < this._values.length) {\n      this._n += 1;\n    }\n    this._val = this._sum / this._n;\n    return this._val;\n  }\n  get value() {\n    return this._val;\n  }\n}", "map": {"version": 3, "names": ["clamp", "value", "min", "max", "Math", "MovingAverage", "constructor", "_n", "_val", "update", "SlidingWindowAverage", "size", "_values", "_index", "_sum", "Array", "fill", "oldValue", "length"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/common/numbers.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport function clamp(value, min, max) {\n    return Math.min(Math.max(value, min), max);\n}\nexport class MovingAverage {\n    constructor() {\n        this._n = 1;\n        this._val = 0;\n    }\n    update(value) {\n        this._val = this._val + (value - this._val) / this._n;\n        this._n += 1;\n        return this._val;\n    }\n    get value() {\n        return this._val;\n    }\n}\nexport class SlidingWindowAverage {\n    constructor(size) {\n        this._n = 0;\n        this._val = 0;\n        this._values = [];\n        this._index = 0;\n        this._sum = 0;\n        this._values = new Array(size);\n        this._values.fill(0, 0, size);\n    }\n    update(value) {\n        const oldValue = this._values[this._index];\n        this._values[this._index] = value;\n        this._index = (this._index + 1) % this._values.length;\n        this._sum -= oldValue;\n        this._sum += value;\n        if (this._n < this._values.length) {\n            this._n += 1;\n        }\n        this._val = this._sum / this._n;\n        return this._val;\n    }\n    get value() {\n        return this._val;\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,SAASA,KAAKA,CAACC,KAAK,EAAEC,GAAG,EAAEC,GAAG,EAAE;EACnC,OAAOC,IAAI,CAACF,GAAG,CAACE,IAAI,CAACD,GAAG,CAACF,KAAK,EAAEC,GAAG,CAAC,EAAEC,GAAG,CAAC;AAC9C;AACA,OAAO,MAAME,aAAa,CAAC;EACvBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,EAAE,GAAG,CAAC;IACX,IAAI,CAACC,IAAI,GAAG,CAAC;EACjB;EACAC,MAAMA,CAACR,KAAK,EAAE;IACV,IAAI,CAACO,IAAI,GAAG,IAAI,CAACA,IAAI,GAAG,CAACP,KAAK,GAAG,IAAI,CAACO,IAAI,IAAI,IAAI,CAACD,EAAE;IACrD,IAAI,CAACA,EAAE,IAAI,CAAC;IACZ,OAAO,IAAI,CAACC,IAAI;EACpB;EACA,IAAIP,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACO,IAAI;EACpB;AACJ;AACA,OAAO,MAAME,oBAAoB,CAAC;EAC9BJ,WAAWA,CAACK,IAAI,EAAE;IACd,IAAI,CAACJ,EAAE,GAAG,CAAC;IACX,IAAI,CAACC,IAAI,GAAG,CAAC;IACb,IAAI,CAACI,OAAO,GAAG,EAAE;IACjB,IAAI,CAACC,MAAM,GAAG,CAAC;IACf,IAAI,CAACC,IAAI,GAAG,CAAC;IACb,IAAI,CAACF,OAAO,GAAG,IAAIG,KAAK,CAACJ,IAAI,CAAC;IAC9B,IAAI,CAACC,OAAO,CAACI,IAAI,CAAC,CAAC,EAAE,CAAC,EAAEL,IAAI,CAAC;EACjC;EACAF,MAAMA,CAACR,KAAK,EAAE;IACV,MAAMgB,QAAQ,GAAG,IAAI,CAACL,OAAO,CAAC,IAAI,CAACC,MAAM,CAAC;IAC1C,IAAI,CAACD,OAAO,CAAC,IAAI,CAACC,MAAM,CAAC,GAAGZ,KAAK;IACjC,IAAI,CAACY,MAAM,GAAG,CAAC,IAAI,CAACA,MAAM,GAAG,CAAC,IAAI,IAAI,CAACD,OAAO,CAACM,MAAM;IACrD,IAAI,CAACJ,IAAI,IAAIG,QAAQ;IACrB,IAAI,CAACH,IAAI,IAAIb,KAAK;IAClB,IAAI,IAAI,CAACM,EAAE,GAAG,IAAI,CAACK,OAAO,CAACM,MAAM,EAAE;MAC/B,IAAI,CAACX,EAAE,IAAI,CAAC;IAChB;IACA,IAAI,CAACC,IAAI,GAAG,IAAI,CAACM,IAAI,GAAG,IAAI,CAACP,EAAE;IAC/B,OAAO,IAAI,CAACC,IAAI;EACpB;EACA,IAAIP,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACO,IAAI;EACpB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}