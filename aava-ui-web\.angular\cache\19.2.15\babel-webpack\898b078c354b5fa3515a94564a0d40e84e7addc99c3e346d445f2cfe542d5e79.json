{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { equals } from '../../../../../base/common/arrays.js';\nimport { splitLines } from '../../../../../base/common/strings.js';\nimport { Position } from '../../../../common/core/position.js';\nimport { Range } from '../../../../common/core/range.js';\nimport { SingleTextEdit, TextEdit } from '../../../../common/core/textEdit.js';\nexport class GhostText {\n  constructor(lineNumber, parts) {\n    this.lineNumber = lineNumber;\n    this.parts = parts;\n  }\n  equals(other) {\n    return this.lineNumber === other.lineNumber && this.parts.length === other.parts.length && this.parts.every((part, index) => part.equals(other.parts[index]));\n  }\n  renderForScreenReader(lineText) {\n    if (this.parts.length === 0) {\n      return '';\n    }\n    const lastPart = this.parts[this.parts.length - 1];\n    const cappedLineText = lineText.substr(0, lastPart.column - 1);\n    const text = new TextEdit([...this.parts.map(p => new SingleTextEdit(Range.fromPositions(new Position(1, p.column)), p.lines.join('\\n')))]).applyToString(cappedLineText);\n    return text.substring(this.parts[0].column - 1);\n  }\n  isEmpty() {\n    return this.parts.every(p => p.lines.length === 0);\n  }\n  get lineCount() {\n    return 1 + this.parts.reduce((r, p) => r + p.lines.length - 1, 0);\n  }\n}\nexport class GhostTextPart {\n  constructor(column, text,\n  /**\n   * Indicates if this part is a preview of an inline suggestion when a suggestion is previewed.\n  */\n  preview) {\n    this.column = column;\n    this.text = text;\n    this.preview = preview;\n    this.lines = splitLines(this.text);\n  }\n  equals(other) {\n    return this.column === other.column && this.lines.length === other.lines.length && this.lines.every((line, index) => line === other.lines[index]);\n  }\n}\nexport class GhostTextReplacement {\n  constructor(lineNumber, columnRange, text, additionalReservedLineCount = 0) {\n    this.lineNumber = lineNumber;\n    this.columnRange = columnRange;\n    this.text = text;\n    this.additionalReservedLineCount = additionalReservedLineCount;\n    this.parts = [new GhostTextPart(this.columnRange.endColumnExclusive, this.text, false)];\n    this.newLines = splitLines(this.text);\n  }\n  renderForScreenReader(_lineText) {\n    return this.newLines.join('\\n');\n  }\n  get lineCount() {\n    return this.newLines.length;\n  }\n  isEmpty() {\n    return this.parts.every(p => p.lines.length === 0);\n  }\n  equals(other) {\n    return this.lineNumber === other.lineNumber && this.columnRange.equals(other.columnRange) && this.newLines.length === other.newLines.length && this.newLines.every((line, index) => line === other.newLines[index]) && this.additionalReservedLineCount === other.additionalReservedLineCount;\n  }\n}\nexport function ghostTextsOrReplacementsEqual(a, b) {\n  return equals(a, b, ghostTextOrReplacementEquals);\n}\nexport function ghostTextOrReplacementEquals(a, b) {\n  if (a === b) {\n    return true;\n  }\n  if (!a || !b) {\n    return false;\n  }\n  if (a instanceof GhostText && b instanceof GhostText) {\n    return a.equals(b);\n  }\n  if (a instanceof GhostTextReplacement && b instanceof GhostTextReplacement) {\n    return a.equals(b);\n  }\n  return false;\n}", "map": {"version": 3, "names": ["equals", "splitLines", "Position", "Range", "SingleTextEdit", "TextEdit", "GhostText", "constructor", "lineNumber", "parts", "other", "length", "every", "part", "index", "renderForScreenReader", "lineText", "lastPart", "cappedLineText", "substr", "column", "text", "map", "p", "fromPositions", "lines", "join", "applyToString", "substring", "isEmpty", "lineCount", "reduce", "r", "GhostTextPart", "preview", "line", "GhostTextReplacement", "columnRange", "additionalReservedLineCount", "endColumnExclusive", "newLines", "_lineText", "ghostTextsOrReplacementsEqual", "a", "b", "ghostTextOrReplacementEquals"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/contrib/inlineCompletions/browser/model/ghostText.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { equals } from '../../../../../base/common/arrays.js';\nimport { splitLines } from '../../../../../base/common/strings.js';\nimport { Position } from '../../../../common/core/position.js';\nimport { Range } from '../../../../common/core/range.js';\nimport { SingleTextEdit, TextEdit } from '../../../../common/core/textEdit.js';\nexport class GhostText {\n    constructor(lineNumber, parts) {\n        this.lineNumber = lineNumber;\n        this.parts = parts;\n    }\n    equals(other) {\n        return this.lineNumber === other.lineNumber &&\n            this.parts.length === other.parts.length &&\n            this.parts.every((part, index) => part.equals(other.parts[index]));\n    }\n    renderForScreenReader(lineText) {\n        if (this.parts.length === 0) {\n            return '';\n        }\n        const lastPart = this.parts[this.parts.length - 1];\n        const cappedLineText = lineText.substr(0, lastPart.column - 1);\n        const text = new TextEdit([\n            ...this.parts.map(p => new SingleTextEdit(Range.fromPositions(new Position(1, p.column)), p.lines.join('\\n'))),\n        ]).applyToString(cappedLineText);\n        return text.substring(this.parts[0].column - 1);\n    }\n    isEmpty() {\n        return this.parts.every(p => p.lines.length === 0);\n    }\n    get lineCount() {\n        return 1 + this.parts.reduce((r, p) => r + p.lines.length - 1, 0);\n    }\n}\nexport class GhostTextPart {\n    constructor(column, text, \n    /**\n     * Indicates if this part is a preview of an inline suggestion when a suggestion is previewed.\n    */\n    preview) {\n        this.column = column;\n        this.text = text;\n        this.preview = preview;\n        this.lines = splitLines(this.text);\n    }\n    equals(other) {\n        return this.column === other.column &&\n            this.lines.length === other.lines.length &&\n            this.lines.every((line, index) => line === other.lines[index]);\n    }\n}\nexport class GhostTextReplacement {\n    constructor(lineNumber, columnRange, text, additionalReservedLineCount = 0) {\n        this.lineNumber = lineNumber;\n        this.columnRange = columnRange;\n        this.text = text;\n        this.additionalReservedLineCount = additionalReservedLineCount;\n        this.parts = [\n            new GhostTextPart(this.columnRange.endColumnExclusive, this.text, false),\n        ];\n        this.newLines = splitLines(this.text);\n    }\n    renderForScreenReader(_lineText) {\n        return this.newLines.join('\\n');\n    }\n    get lineCount() {\n        return this.newLines.length;\n    }\n    isEmpty() {\n        return this.parts.every(p => p.lines.length === 0);\n    }\n    equals(other) {\n        return this.lineNumber === other.lineNumber &&\n            this.columnRange.equals(other.columnRange) &&\n            this.newLines.length === other.newLines.length &&\n            this.newLines.every((line, index) => line === other.newLines[index]) &&\n            this.additionalReservedLineCount === other.additionalReservedLineCount;\n    }\n}\nexport function ghostTextsOrReplacementsEqual(a, b) {\n    return equals(a, b, ghostTextOrReplacementEquals);\n}\nexport function ghostTextOrReplacementEquals(a, b) {\n    if (a === b) {\n        return true;\n    }\n    if (!a || !b) {\n        return false;\n    }\n    if (a instanceof GhostText && b instanceof GhostText) {\n        return a.equals(b);\n    }\n    if (a instanceof GhostTextReplacement && b instanceof GhostTextReplacement) {\n        return a.equals(b);\n    }\n    return false;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,MAAM,QAAQ,sCAAsC;AAC7D,SAASC,UAAU,QAAQ,uCAAuC;AAClE,SAASC,QAAQ,QAAQ,qCAAqC;AAC9D,SAASC,KAAK,QAAQ,kCAAkC;AACxD,SAASC,cAAc,EAAEC,QAAQ,QAAQ,qCAAqC;AAC9E,OAAO,MAAMC,SAAS,CAAC;EACnBC,WAAWA,CAACC,UAAU,EAAEC,KAAK,EAAE;IAC3B,IAAI,CAACD,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,KAAK,GAAGA,KAAK;EACtB;EACAT,MAAMA,CAACU,KAAK,EAAE;IACV,OAAO,IAAI,CAACF,UAAU,KAAKE,KAAK,CAACF,UAAU,IACvC,IAAI,CAACC,KAAK,CAACE,MAAM,KAAKD,KAAK,CAACD,KAAK,CAACE,MAAM,IACxC,IAAI,CAACF,KAAK,CAACG,KAAK,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAKD,IAAI,CAACb,MAAM,CAACU,KAAK,CAACD,KAAK,CAACK,KAAK,CAAC,CAAC,CAAC;EAC1E;EACAC,qBAAqBA,CAACC,QAAQ,EAAE;IAC5B,IAAI,IAAI,CAACP,KAAK,CAACE,MAAM,KAAK,CAAC,EAAE;MACzB,OAAO,EAAE;IACb;IACA,MAAMM,QAAQ,GAAG,IAAI,CAACR,KAAK,CAAC,IAAI,CAACA,KAAK,CAACE,MAAM,GAAG,CAAC,CAAC;IAClD,MAAMO,cAAc,GAAGF,QAAQ,CAACG,MAAM,CAAC,CAAC,EAAEF,QAAQ,CAACG,MAAM,GAAG,CAAC,CAAC;IAC9D,MAAMC,IAAI,GAAG,IAAIhB,QAAQ,CAAC,CACtB,GAAG,IAAI,CAACI,KAAK,CAACa,GAAG,CAACC,CAAC,IAAI,IAAInB,cAAc,CAACD,KAAK,CAACqB,aAAa,CAAC,IAAItB,QAAQ,CAAC,CAAC,EAAEqB,CAAC,CAACH,MAAM,CAAC,CAAC,EAAEG,CAAC,CAACE,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CACjH,CAAC,CAACC,aAAa,CAACT,cAAc,CAAC;IAChC,OAAOG,IAAI,CAACO,SAAS,CAAC,IAAI,CAACnB,KAAK,CAAC,CAAC,CAAC,CAACW,MAAM,GAAG,CAAC,CAAC;EACnD;EACAS,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACpB,KAAK,CAACG,KAAK,CAACW,CAAC,IAAIA,CAAC,CAACE,KAAK,CAACd,MAAM,KAAK,CAAC,CAAC;EACtD;EACA,IAAImB,SAASA,CAAA,EAAG;IACZ,OAAO,CAAC,GAAG,IAAI,CAACrB,KAAK,CAACsB,MAAM,CAAC,CAACC,CAAC,EAAET,CAAC,KAAKS,CAAC,GAAGT,CAAC,CAACE,KAAK,CAACd,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC;EACrE;AACJ;AACA,OAAO,MAAMsB,aAAa,CAAC;EACvB1B,WAAWA,CAACa,MAAM,EAAEC,IAAI;EACxB;AACJ;AACA;EACIa,OAAO,EAAE;IACL,IAAI,CAACd,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACa,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACT,KAAK,GAAGxB,UAAU,CAAC,IAAI,CAACoB,IAAI,CAAC;EACtC;EACArB,MAAMA,CAACU,KAAK,EAAE;IACV,OAAO,IAAI,CAACU,MAAM,KAAKV,KAAK,CAACU,MAAM,IAC/B,IAAI,CAACK,KAAK,CAACd,MAAM,KAAKD,KAAK,CAACe,KAAK,CAACd,MAAM,IACxC,IAAI,CAACc,KAAK,CAACb,KAAK,CAAC,CAACuB,IAAI,EAAErB,KAAK,KAAKqB,IAAI,KAAKzB,KAAK,CAACe,KAAK,CAACX,KAAK,CAAC,CAAC;EACtE;AACJ;AACA,OAAO,MAAMsB,oBAAoB,CAAC;EAC9B7B,WAAWA,CAACC,UAAU,EAAE6B,WAAW,EAAEhB,IAAI,EAAEiB,2BAA2B,GAAG,CAAC,EAAE;IACxE,IAAI,CAAC9B,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAAC6B,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAAChB,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACiB,2BAA2B,GAAGA,2BAA2B;IAC9D,IAAI,CAAC7B,KAAK,GAAG,CACT,IAAIwB,aAAa,CAAC,IAAI,CAACI,WAAW,CAACE,kBAAkB,EAAE,IAAI,CAAClB,IAAI,EAAE,KAAK,CAAC,CAC3E;IACD,IAAI,CAACmB,QAAQ,GAAGvC,UAAU,CAAC,IAAI,CAACoB,IAAI,CAAC;EACzC;EACAN,qBAAqBA,CAAC0B,SAAS,EAAE;IAC7B,OAAO,IAAI,CAACD,QAAQ,CAACd,IAAI,CAAC,IAAI,CAAC;EACnC;EACA,IAAII,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACU,QAAQ,CAAC7B,MAAM;EAC/B;EACAkB,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACpB,KAAK,CAACG,KAAK,CAACW,CAAC,IAAIA,CAAC,CAACE,KAAK,CAACd,MAAM,KAAK,CAAC,CAAC;EACtD;EACAX,MAAMA,CAACU,KAAK,EAAE;IACV,OAAO,IAAI,CAACF,UAAU,KAAKE,KAAK,CAACF,UAAU,IACvC,IAAI,CAAC6B,WAAW,CAACrC,MAAM,CAACU,KAAK,CAAC2B,WAAW,CAAC,IAC1C,IAAI,CAACG,QAAQ,CAAC7B,MAAM,KAAKD,KAAK,CAAC8B,QAAQ,CAAC7B,MAAM,IAC9C,IAAI,CAAC6B,QAAQ,CAAC5B,KAAK,CAAC,CAACuB,IAAI,EAAErB,KAAK,KAAKqB,IAAI,KAAKzB,KAAK,CAAC8B,QAAQ,CAAC1B,KAAK,CAAC,CAAC,IACpE,IAAI,CAACwB,2BAA2B,KAAK5B,KAAK,CAAC4B,2BAA2B;EAC9E;AACJ;AACA,OAAO,SAASI,6BAA6BA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAChD,OAAO5C,MAAM,CAAC2C,CAAC,EAAEC,CAAC,EAAEC,4BAA4B,CAAC;AACrD;AACA,OAAO,SAASA,4BAA4BA,CAACF,CAAC,EAAEC,CAAC,EAAE;EAC/C,IAAID,CAAC,KAAKC,CAAC,EAAE;IACT,OAAO,IAAI;EACf;EACA,IAAI,CAACD,CAAC,IAAI,CAACC,CAAC,EAAE;IACV,OAAO,KAAK;EAChB;EACA,IAAID,CAAC,YAAYrC,SAAS,IAAIsC,CAAC,YAAYtC,SAAS,EAAE;IAClD,OAAOqC,CAAC,CAAC3C,MAAM,CAAC4C,CAAC,CAAC;EACtB;EACA,IAAID,CAAC,YAAYP,oBAAoB,IAAIQ,CAAC,YAAYR,oBAAoB,EAAE;IACxE,OAAOO,CAAC,CAAC3C,MAAM,CAAC4C,CAAC,CAAC;EACtB;EACA,OAAO,KAAK;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}