{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { SelectBoxList } from './selectBoxCustom.js';\nimport { SelectBoxNative } from './selectBoxNative.js';\nimport { Widget } from '../widget.js';\nimport { isMacintosh } from '../../../common/platform.js';\nimport './selectBox.css';\nexport class SelectBox extends Widget {\n  constructor(options, selected, contextViewProvider, styles, selectBoxOptions) {\n    super();\n    // Default to native SelectBox for OSX unless overridden\n    if (isMacintosh && !selectBoxOptions?.useCustomDrawn) {\n      this.selectBoxDelegate = new SelectBoxNative(options, selected, styles, selectBoxOptions);\n    } else {\n      this.selectBoxDelegate = new SelectBoxList(options, selected, contextViewProvider, styles, selectBoxOptions);\n    }\n    this._register(this.selectBoxDelegate);\n  }\n  // Public SelectBox Methods - routed through delegate interface\n  get onDidSelect() {\n    return this.selectBoxDelegate.onDidSelect;\n  }\n  setOptions(options, selected) {\n    this.selectBoxDelegate.setOptions(options, selected);\n  }\n  select(index) {\n    this.selectBoxDelegate.select(index);\n  }\n  focus() {\n    this.selectBoxDelegate.focus();\n  }\n  blur() {\n    this.selectBoxDelegate.blur();\n  }\n  setFocusable(focusable) {\n    this.selectBoxDelegate.setFocusable(focusable);\n  }\n  render(container) {\n    this.selectBoxDelegate.render(container);\n  }\n}", "map": {"version": 3, "names": ["SelectBoxList", "SelectBoxNative", "Widget", "isMacintosh", "SelectBox", "constructor", "options", "selected", "contextView<PERSON>rovider", "styles", "selectBoxOptions", "useCustomDrawn", "selectBoxDelegate", "_register", "onDidSelect", "setOptions", "select", "index", "focus", "blur", "setFocusable", "focusable", "render", "container"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/browser/ui/selectBox/selectBox.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { SelectBoxList } from './selectBoxCustom.js';\nimport { SelectBoxNative } from './selectBoxNative.js';\nimport { Widget } from '../widget.js';\nimport { isMacintosh } from '../../../common/platform.js';\nimport './selectBox.css';\nexport class SelectBox extends Widget {\n    constructor(options, selected, contextViewProvider, styles, selectBoxOptions) {\n        super();\n        // Default to native SelectBox for OSX unless overridden\n        if (isMacintosh && !selectBoxOptions?.useCustomDrawn) {\n            this.selectBoxDelegate = new SelectBoxNative(options, selected, styles, selectBoxOptions);\n        }\n        else {\n            this.selectBoxDelegate = new SelectBoxList(options, selected, contextViewProvider, styles, selectBoxOptions);\n        }\n        this._register(this.selectBoxDelegate);\n    }\n    // Public SelectBox Methods - routed through delegate interface\n    get onDidSelect() {\n        return this.selectBoxDelegate.onDidSelect;\n    }\n    setOptions(options, selected) {\n        this.selectBoxDelegate.setOptions(options, selected);\n    }\n    select(index) {\n        this.selectBoxDelegate.select(index);\n    }\n    focus() {\n        this.selectBoxDelegate.focus();\n    }\n    blur() {\n        this.selectBoxDelegate.blur();\n    }\n    setFocusable(focusable) {\n        this.selectBoxDelegate.setFocusable(focusable);\n    }\n    render(container) {\n        this.selectBoxDelegate.render(container);\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,aAAa,QAAQ,sBAAsB;AACpD,SAASC,eAAe,QAAQ,sBAAsB;AACtD,SAASC,MAAM,QAAQ,cAAc;AACrC,SAASC,WAAW,QAAQ,6BAA6B;AACzD,OAAO,iBAAiB;AACxB,OAAO,MAAMC,SAAS,SAASF,MAAM,CAAC;EAClCG,WAAWA,CAACC,OAAO,EAAEC,QAAQ,EAAEC,mBAAmB,EAAEC,MAAM,EAAEC,gBAAgB,EAAE;IAC1E,KAAK,CAAC,CAAC;IACP;IACA,IAAIP,WAAW,IAAI,CAACO,gBAAgB,EAAEC,cAAc,EAAE;MAClD,IAAI,CAACC,iBAAiB,GAAG,IAAIX,eAAe,CAACK,OAAO,EAAEC,QAAQ,EAAEE,MAAM,EAAEC,gBAAgB,CAAC;IAC7F,CAAC,MACI;MACD,IAAI,CAACE,iBAAiB,GAAG,IAAIZ,aAAa,CAACM,OAAO,EAAEC,QAAQ,EAAEC,mBAAmB,EAAEC,MAAM,EAAEC,gBAAgB,CAAC;IAChH;IACA,IAAI,CAACG,SAAS,CAAC,IAAI,CAACD,iBAAiB,CAAC;EAC1C;EACA;EACA,IAAIE,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACF,iBAAiB,CAACE,WAAW;EAC7C;EACAC,UAAUA,CAACT,OAAO,EAAEC,QAAQ,EAAE;IAC1B,IAAI,CAACK,iBAAiB,CAACG,UAAU,CAACT,OAAO,EAAEC,QAAQ,CAAC;EACxD;EACAS,MAAMA,CAACC,KAAK,EAAE;IACV,IAAI,CAACL,iBAAiB,CAACI,MAAM,CAACC,KAAK,CAAC;EACxC;EACAC,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACN,iBAAiB,CAACM,KAAK,CAAC,CAAC;EAClC;EACAC,IAAIA,CAAA,EAAG;IACH,IAAI,CAACP,iBAAiB,CAACO,IAAI,CAAC,CAAC;EACjC;EACAC,YAAYA,CAACC,SAAS,EAAE;IACpB,IAAI,CAACT,iBAAiB,CAACQ,YAAY,CAACC,SAAS,CAAC;EAClD;EACAC,MAAMA,CAACC,SAAS,EAAE;IACd,IAAI,CAACX,iBAAiB,CAACU,MAAM,CAACC,SAAS,CAAC;EAC5C;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}