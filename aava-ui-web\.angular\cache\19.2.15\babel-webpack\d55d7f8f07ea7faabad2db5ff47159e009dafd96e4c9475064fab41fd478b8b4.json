{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Disposable } from '../../../base/common/lifecycle.js';\nexport class TextModelPart extends Disposable {\n  constructor() {\n    super(...arguments);\n    this._isDisposed = false;\n  }\n  dispose() {\n    super.dispose();\n    this._isDisposed = true;\n  }\n  assertNotDisposed() {\n    if (this._isDisposed) {\n      throw new Error('TextModelPart is disposed!');\n    }\n  }\n}", "map": {"version": 3, "names": ["Disposable", "TextModelPart", "constructor", "arguments", "_isDisposed", "dispose", "assertNotDisposed", "Error"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/model/textModelPart.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Disposable } from '../../../base/common/lifecycle.js';\nexport class TextModelPart extends Disposable {\n    constructor() {\n        super(...arguments);\n        this._isDisposed = false;\n    }\n    dispose() {\n        super.dispose();\n        this._isDisposed = true;\n    }\n    assertNotDisposed() {\n        if (this._isDisposed) {\n            throw new Error('TextModelPart is disposed!');\n        }\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,UAAU,QAAQ,mCAAmC;AAC9D,OAAO,MAAMC,aAAa,SAASD,UAAU,CAAC;EAC1CE,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACC,WAAW,GAAG,KAAK;EAC5B;EACAC,OAAOA,CAAA,EAAG;IACN,KAAK,CAACA,OAAO,CAAC,CAAC;IACf,IAAI,CAACD,WAAW,GAAG,IAAI;EAC3B;EACAE,iBAAiBA,CAAA,EAAG;IAChB,IAAI,IAAI,CAACF,WAAW,EAAE;MAClB,MAAM,IAAIG,KAAK,CAAC,4BAA4B,CAAC;IACjD;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}