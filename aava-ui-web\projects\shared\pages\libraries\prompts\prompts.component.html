<div id="prompts-container" class="container-fluid">
  <div id="search-filter-container" class="row g-3">
    <div class="col-12 col-md-8 col-lg-9 col-xl-10 search-section">
      <form [formGroup]="searchForm">
        <ava-textbox
          placeholder='Search "Prompts"'
          hoverEffect="glow"
          pressedEffect="solid"
          formControlName="search"
        >
          <ava-icon
            slot="icon-start"
            iconName="search"
            [iconSize]="16"
            iconColor="var(--color-brand-primary)"
          >
          </ava-icon>
        </ava-textbox>
      </form>
    </div>
    <div class="col-12 col-md-4 col-lg-3 col-xl-2 action-buttons">
      <ava-dropdown
        dropdownTitle="choose prompt"
        [options]="simpleOptions"
        (selectionChange)="onSelectionChange($event)"
      >
      </ava-dropdown>
    </div>
  </div>

  <div id="prompts-card-container" class="row g-3">
    <ava-text-card
      class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-3 col-xxl-2 mt-5"
      [type]="'create'"
      [iconName]="'plus'"
      iconColor="#144692"
      [title]="promptLabels.createPrompt"
      (cardClick)="onCreatePrompt()"
      [isLoading]="isLoading"
    >
    </ava-text-card>

    <!-- No Results Message -->
    <div
      class="col-12 d-flex justify-content-center align-items-center py-5"
      *ngIf="!isLoading && filteredPrompts.length === 0"
    >
      <div class="text-center">
        <h5 class="text-muted">{{ promptLabels.noResults }}</h5>
      </div>
    </div>

    <ng-container
      *ngFor="
        let prompt of isLoading && displayedPrompts.length === 0
          ? cardSkeletonPlaceholders
          : displayedPrompts
      "
    >
      <ava-console-card
        class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-3 col-xxl-2 mt-5"
        [title]="prompt?.title"
        [description]="prompt?.description"
        categoryIcon="plus"
        categoryTitle="Prompts"
        categoryValue="42"
        [author]="prompt?.owner || 'AAVA'"
        [date]="prompt?.createdDate | timeAgo"
        [actions]="defaultActions"
        (actionClick)="onActionClick($event, prompt.id)"
        [skeleton]="isLoading"
      >
      </ava-console-card>
    </ng-container>
  </div>

  <!-- Pagination Footer -->
  <div class="row" *ngIf="filteredPrompts.length > 0">
    <div class="col-12 d-flex justify-content-center mt-4">
      <app-page-footer
        [totalItems]="filteredPrompts.length + 1"
        [currentPage]="currentPage"
        [itemsPerPage]="itemsPerPage"
        (pageChange)="onPageChange($event)"
      ></app-page-footer>
    </div>
  </div>
</div>


