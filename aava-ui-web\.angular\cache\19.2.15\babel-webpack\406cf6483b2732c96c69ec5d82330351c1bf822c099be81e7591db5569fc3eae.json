{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { TimeoutTimer } from '../../../common/async.js';\nimport { Disposable } from '../../../common/lifecycle.js';\nexport class ScrollbarVisibilityController extends Disposable {\n  constructor(visibility, visibleClassName, invisibleClassName) {\n    super();\n    this._visibility = visibility;\n    this._visibleClassName = visibleClassName;\n    this._invisibleClassName = invisibleClassName;\n    this._domNode = null;\n    this._isVisible = false;\n    this._isNeeded = false;\n    this._rawShouldBeVisible = false;\n    this._shouldBeVisible = false;\n    this._revealTimer = this._register(new TimeoutTimer());\n  }\n  setVisibility(visibility) {\n    if (this._visibility !== visibility) {\n      this._visibility = visibility;\n      this._updateShouldBeVisible();\n    }\n  }\n  // ----------------- Hide / Reveal\n  setShouldBeVisible(rawShouldBeVisible) {\n    this._rawShouldBeVisible = rawShouldBeVisible;\n    this._updateShouldBeVisible();\n  }\n  _applyVisibilitySetting() {\n    if (this._visibility === 2 /* ScrollbarVisibility.Hidden */) {\n      return false;\n    }\n    if (this._visibility === 3 /* ScrollbarVisibility.Visible */) {\n      return true;\n    }\n    return this._rawShouldBeVisible;\n  }\n  _updateShouldBeVisible() {\n    const shouldBeVisible = this._applyVisibilitySetting();\n    if (this._shouldBeVisible !== shouldBeVisible) {\n      this._shouldBeVisible = shouldBeVisible;\n      this.ensureVisibility();\n    }\n  }\n  setIsNeeded(isNeeded) {\n    if (this._isNeeded !== isNeeded) {\n      this._isNeeded = isNeeded;\n      this.ensureVisibility();\n    }\n  }\n  setDomNode(domNode) {\n    this._domNode = domNode;\n    this._domNode.setClassName(this._invisibleClassName);\n    // Now that the flags & the dom node are in a consistent state, ensure the Hidden/Visible configuration\n    this.setShouldBeVisible(false);\n  }\n  ensureVisibility() {\n    if (!this._isNeeded) {\n      // Nothing to be rendered\n      this._hide(false);\n      return;\n    }\n    if (this._shouldBeVisible) {\n      this._reveal();\n    } else {\n      this._hide(true);\n    }\n  }\n  _reveal() {\n    if (this._isVisible) {\n      return;\n    }\n    this._isVisible = true;\n    // The CSS animation doesn't play otherwise\n    this._revealTimer.setIfNotSet(() => {\n      this._domNode?.setClassName(this._visibleClassName);\n    }, 0);\n  }\n  _hide(withFadeAway) {\n    this._revealTimer.cancel();\n    if (!this._isVisible) {\n      return;\n    }\n    this._isVisible = false;\n    this._domNode?.setClassName(this._invisibleClassName + (withFadeAway ? ' fade' : ''));\n  }\n}", "map": {"version": 3, "names": ["TimeoutTimer", "Disposable", "ScrollbarVisibilityController", "constructor", "visibility", "visibleClassName", "invisibleClassName", "_visibility", "_visibleClassName", "_invisibleClassName", "_domNode", "_isVisible", "_isNeeded", "_rawShouldBeVisible", "_shouldBeVisible", "_revealTimer", "_register", "setVisibility", "_updateShouldBeVisible", "setShouldBeVisible", "rawShouldBeVisible", "_applyVisibilitySetting", "shouldBeVisible", "ensureVisibility", "setIsNeeded", "isNeeded", "setDomNode", "domNode", "setClassName", "_hide", "_reveal", "setIfNotSet", "withFadeAway", "cancel"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/browser/ui/scrollbar/scrollbarVisibilityController.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { TimeoutTimer } from '../../../common/async.js';\nimport { Disposable } from '../../../common/lifecycle.js';\nexport class ScrollbarVisibilityController extends Disposable {\n    constructor(visibility, visibleClassName, invisibleClassName) {\n        super();\n        this._visibility = visibility;\n        this._visibleClassName = visibleClassName;\n        this._invisibleClassName = invisibleClassName;\n        this._domNode = null;\n        this._isVisible = false;\n        this._isNeeded = false;\n        this._rawShouldBeVisible = false;\n        this._shouldBeVisible = false;\n        this._revealTimer = this._register(new TimeoutTimer());\n    }\n    setVisibility(visibility) {\n        if (this._visibility !== visibility) {\n            this._visibility = visibility;\n            this._updateShouldBeVisible();\n        }\n    }\n    // ----------------- Hide / Reveal\n    setShouldBeVisible(rawShouldBeVisible) {\n        this._rawShouldBeVisible = rawShouldBeVisible;\n        this._updateShouldBeVisible();\n    }\n    _applyVisibilitySetting() {\n        if (this._visibility === 2 /* ScrollbarVisibility.Hidden */) {\n            return false;\n        }\n        if (this._visibility === 3 /* ScrollbarVisibility.Visible */) {\n            return true;\n        }\n        return this._rawShouldBeVisible;\n    }\n    _updateShouldBeVisible() {\n        const shouldBeVisible = this._applyVisibilitySetting();\n        if (this._shouldBeVisible !== shouldBeVisible) {\n            this._shouldBeVisible = shouldBeVisible;\n            this.ensureVisibility();\n        }\n    }\n    setIsNeeded(isNeeded) {\n        if (this._isNeeded !== isNeeded) {\n            this._isNeeded = isNeeded;\n            this.ensureVisibility();\n        }\n    }\n    setDomNode(domNode) {\n        this._domNode = domNode;\n        this._domNode.setClassName(this._invisibleClassName);\n        // Now that the flags & the dom node are in a consistent state, ensure the Hidden/Visible configuration\n        this.setShouldBeVisible(false);\n    }\n    ensureVisibility() {\n        if (!this._isNeeded) {\n            // Nothing to be rendered\n            this._hide(false);\n            return;\n        }\n        if (this._shouldBeVisible) {\n            this._reveal();\n        }\n        else {\n            this._hide(true);\n        }\n    }\n    _reveal() {\n        if (this._isVisible) {\n            return;\n        }\n        this._isVisible = true;\n        // The CSS animation doesn't play otherwise\n        this._revealTimer.setIfNotSet(() => {\n            this._domNode?.setClassName(this._visibleClassName);\n        }, 0);\n    }\n    _hide(withFadeAway) {\n        this._revealTimer.cancel();\n        if (!this._isVisible) {\n            return;\n        }\n        this._isVisible = false;\n        this._domNode?.setClassName(this._invisibleClassName + (withFadeAway ? ' fade' : ''));\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,YAAY,QAAQ,0BAA0B;AACvD,SAASC,UAAU,QAAQ,8BAA8B;AACzD,OAAO,MAAMC,6BAA6B,SAASD,UAAU,CAAC;EAC1DE,WAAWA,CAACC,UAAU,EAAEC,gBAAgB,EAAEC,kBAAkB,EAAE;IAC1D,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,WAAW,GAAGH,UAAU;IAC7B,IAAI,CAACI,iBAAiB,GAAGH,gBAAgB;IACzC,IAAI,CAACI,mBAAmB,GAAGH,kBAAkB;IAC7C,IAAI,CAACI,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,YAAY,GAAG,IAAI,CAACC,SAAS,CAAC,IAAIhB,YAAY,CAAC,CAAC,CAAC;EAC1D;EACAiB,aAAaA,CAACb,UAAU,EAAE;IACtB,IAAI,IAAI,CAACG,WAAW,KAAKH,UAAU,EAAE;MACjC,IAAI,CAACG,WAAW,GAAGH,UAAU;MAC7B,IAAI,CAACc,sBAAsB,CAAC,CAAC;IACjC;EACJ;EACA;EACAC,kBAAkBA,CAACC,kBAAkB,EAAE;IACnC,IAAI,CAACP,mBAAmB,GAAGO,kBAAkB;IAC7C,IAAI,CAACF,sBAAsB,CAAC,CAAC;EACjC;EACAG,uBAAuBA,CAAA,EAAG;IACtB,IAAI,IAAI,CAACd,WAAW,KAAK,CAAC,CAAC,kCAAkC;MACzD,OAAO,KAAK;IAChB;IACA,IAAI,IAAI,CAACA,WAAW,KAAK,CAAC,CAAC,mCAAmC;MAC1D,OAAO,IAAI;IACf;IACA,OAAO,IAAI,CAACM,mBAAmB;EACnC;EACAK,sBAAsBA,CAAA,EAAG;IACrB,MAAMI,eAAe,GAAG,IAAI,CAACD,uBAAuB,CAAC,CAAC;IACtD,IAAI,IAAI,CAACP,gBAAgB,KAAKQ,eAAe,EAAE;MAC3C,IAAI,CAACR,gBAAgB,GAAGQ,eAAe;MACvC,IAAI,CAACC,gBAAgB,CAAC,CAAC;IAC3B;EACJ;EACAC,WAAWA,CAACC,QAAQ,EAAE;IAClB,IAAI,IAAI,CAACb,SAAS,KAAKa,QAAQ,EAAE;MAC7B,IAAI,CAACb,SAAS,GAAGa,QAAQ;MACzB,IAAI,CAACF,gBAAgB,CAAC,CAAC;IAC3B;EACJ;EACAG,UAAUA,CAACC,OAAO,EAAE;IAChB,IAAI,CAACjB,QAAQ,GAAGiB,OAAO;IACvB,IAAI,CAACjB,QAAQ,CAACkB,YAAY,CAAC,IAAI,CAACnB,mBAAmB,CAAC;IACpD;IACA,IAAI,CAACU,kBAAkB,CAAC,KAAK,CAAC;EAClC;EACAI,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAAC,IAAI,CAACX,SAAS,EAAE;MACjB;MACA,IAAI,CAACiB,KAAK,CAAC,KAAK,CAAC;MACjB;IACJ;IACA,IAAI,IAAI,CAACf,gBAAgB,EAAE;MACvB,IAAI,CAACgB,OAAO,CAAC,CAAC;IAClB,CAAC,MACI;MACD,IAAI,CAACD,KAAK,CAAC,IAAI,CAAC;IACpB;EACJ;EACAC,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACnB,UAAU,EAAE;MACjB;IACJ;IACA,IAAI,CAACA,UAAU,GAAG,IAAI;IACtB;IACA,IAAI,CAACI,YAAY,CAACgB,WAAW,CAAC,MAAM;MAChC,IAAI,CAACrB,QAAQ,EAAEkB,YAAY,CAAC,IAAI,CAACpB,iBAAiB,CAAC;IACvD,CAAC,EAAE,CAAC,CAAC;EACT;EACAqB,KAAKA,CAACG,YAAY,EAAE;IAChB,IAAI,CAACjB,YAAY,CAACkB,MAAM,CAAC,CAAC;IAC1B,IAAI,CAAC,IAAI,CAACtB,UAAU,EAAE;MAClB;IACJ;IACA,IAAI,CAACA,UAAU,GAAG,KAAK;IACvB,IAAI,CAACD,QAAQ,EAAEkB,YAAY,CAAC,IAAI,CAACnB,mBAAmB,IAAIuB,YAAY,GAAG,OAAO,GAAG,EAAE,CAAC,CAAC;EACzF;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}