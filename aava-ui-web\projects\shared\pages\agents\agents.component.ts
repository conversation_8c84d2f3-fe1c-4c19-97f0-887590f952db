import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { PageFooterComponent } from '../../components/page-footer/page-footer.component';
import { PaginationService } from '../../services/pagination.service';
import { AgentServiceService } from './services/agent-service.service';
import agentsConfigData from './constants/agents.json';
import { combineLatest, debounceTime, distinctUntilChanged, startWith, Subject, takeUntil } from 'rxjs';

import {
  AvaTextboxComponent,
  DropdownComponent,
  DropdownOption,
  IconComponent,
  TextCardComponent,
  ButtonComponent,
  DialogService  
} from '@ava/play-comp-library';
import { LucideAngularModule } from 'lucide-angular';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import {
  ConsoleCardAction,
  ConsoleCardComponent,
} from '../../components/console-card/console-card.component';
import { TimeAgoPipe } from '../../pipes/time-ago.pipe';
import { DebouncedSearchService } from '../../services/debounced-search.service';

@Component({
  selector: 'shared-agents',
  standalone: true,
  imports: [
    CommonModule,
    PageFooterComponent,
    TextCardComponent,
    AvaTextboxComponent,
    DropdownComponent,
    LucideAngularModule,
    IconComponent,
    ReactiveFormsModule,
    ButtonComponent,
    ConsoleCardComponent,
    TimeAgoPipe,  
  ],
  providers:[DialogService],
  templateUrl: './agents.component.html',
  styleUrl: './agents.component.scss',
})
export class AgentsComponent implements OnInit {
  defaultActions: ConsoleCardAction[] = [
    {
      id: 'duplicate',
      icon: 'copy',
      label: 'Duplicate',
      tooltip: 'Duplicate',
    },
    {
      id: 'edit',
      icon: 'edit',
      label: 'Edit item',
      tooltip: 'Edit',
    },
    {
      id: 'delete',
      icon: 'trash',
      label: 'Delete item',
      tooltip: 'Delete',
    },
    {
      id: 'run',
      icon: 'play',
      label: 'Run application',
      tooltip: 'Run',
      isPrimary: true,
    },
  ];
  allAgents: any[] = [];
  displayedAgents: any[] = [];
  totalRecords: any[] = [];
  allAgentsData: any[] = [];
  individualAgentsData: any[] = [];
  collaborativeAgentsData: any[] = [];
  originalAllAgentsData: any[] = [];
  originalIndividualAgentsData: any[] = [];
  originalCollaborativeAgentsData: any[] = [];
  agentsConfig = agentsConfigData;
  isLoading: boolean = false;
  error: string | null = null;
  currentPage: number = 1;
  itemsPerPage: number = agentsConfigData.pagination.itemsPerPage;
  totalPages: number = 1;
  collaborativeTotalPages: number = 1;
  totalNoOfRecords: number = 0;
  destroy$ = new Subject<void>();
  selectedFilterChange = new Subject<'all' | 'individual' | 'collaborative'>();
  agentsOptions: DropdownOption[] = [
    { name: 'All', value: 'all' },
    { name: 'Owned by me', value: 'owned' },
    { name: 'Experience', value: 'experience' },
    { name: 'Product', value: 'product' },
    { name: 'Data', value: 'data' },
    { name: 'Finops', value: 'finops' },
    { name: 'Quality Engineering', value: 'quality' },
    { name: 'Platform', value: 'platform' },
  ];
  selectedFilter: 'all' | 'individual' | 'collaborative' = 'individual';
  selectedData: any = null;
  searchForm!: FormGroup;
  cardSkeletonPlaceholders = Array(11);
  agentToDelete: any = null; 
  constructor(
    private agentService: AgentServiceService,
    private debouncedSearchService: DebouncedSearchService,
    private router: Router,
    private fb: FormBuilder,
    private dialogService: DialogService 
  ) {
    this.searchForm = this.fb.group({
      search: [''],
    });
  }

    ngOnInit(): void {
    if (this.selectedFilter === 'collaborative') {
      this.fetchCollaborativeAgents();
    } else {
      this.fetchIndividualAgents();
    }

    combineLatest([
      this.searchForm.get('search')!.valueChanges.pipe(
        debounceTime(600),
        distinctUntilChanged(),
        startWith('')
      ),
      this.selectedFilterChange.pipe(startWith(this.selectedFilter)),
    ])
      .pipe(takeUntil(this.destroy$))
      .subscribe(([searchTermRaw, filterType]) => {
        const searchTerm = searchTermRaw?.trim().toLowerCase();

        if (filterType === 'individual' || filterType === 'collaborative') {
          if (!searchTerm) {
            this.fetchDefaultDataFor(filterType);
          } else {
            this.isLoading = true;
            this.debouncedSearchService.triggerSearch(
              searchTerm,
              'agents',
              filterType
            );
          }
        }
      });

    this.debouncedSearchService.searchResults$
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          const results = this.extractAgentArray(response);
          const formatted = results.map((agent: any, index: number) => ({
            ...agent,
            id: agent.useCaseId?.toString() || agent.id?.toString() || index.toString(),
            title: agent.useCaseName || agent.name || 'Unnamed',
            name: agent.useCaseName || agent.name || 'Unnamed',
            description: agent.useCaseDetails || agent.description || agent.goal || 'No description',
            createdDate: this.formatDate(agent.created_at || agent.createdAt || agent.modifiedAt),
            userCount: agent.users || 0,
            type: this.selectedFilter,
          }));

          if (this.selectedFilter === 'individual') {
            this.individualAgentsData = formatted;
          } else {
            this.collaborativeAgentsData = formatted;
          }

          this.totalRecords = formatted;
          this.updateDisplayedAgents();
          this.isLoading = false;
        },
        error: () => {
          this.error = 'Something went wrong during search.';
          this.isLoading = false;
        },
      });
  }

  fetchDefaultDataFor(filter: string): void {
    switch (filter) {
      case 'individual':
        this.fetchIndividualAgents();
        break;
      case 'collaborative':
        this.fetchCollaborativeAgents();
        break;
    }
  }

  get shouldShowPagination(): boolean {
    if (this.selectedFilter === 'collaborative') {
      return this.totalNoOfRecords > 0;
    }
    return this.filteredAgents.length > 0;
  }

  get totalItemsForPagination(): number {
    if (this.selectedFilter === 'collaborative') {
      return this.totalNoOfRecords + 1;
    }
    return this.filteredAgents.length + 1;
  }

  get filteredAgents(): any[] {
    switch (this.selectedFilter) {
      case 'individual':
        return this.individualAgentsData;
      case 'collaborative':
        return this.collaborativeAgentsData;
      case 'all':
        return this.allAgentsData;
      default:
        return this.totalRecords;
    }
  }

  onActionClick(
    event: { actionId: string; action: ConsoleCardAction },
    agentId: string,
  ): void {
    switch (event.actionId) {
      case 'delete':
        this.confirmDeleteAgent(agentId);
        break;
      case 'edit':
        this.editAgent(agentId);
        break;
      case 'duplicate':
        this.duplicateAgent(agentId);
        break;
      case 'run':
        this.executeAgent(agentId);
        break;
      default:
        break;
    }
  }

  onFilterChange(filter: 'all' | 'individual' | 'collaborative'): void {
    this.selectedFilter = filter;
        this.selectedFilterChange.next(filter);
    this.searchForm.get('search')?.setValue('');

    this.currentPage = 1;
    if (filter !== 'collaborative') {
      this.totalNoOfRecords = 0;
    }

    this.selectedFilter === 'individual'
      ? this.fetchIndividualAgents()
      : this.fetchCollaborativeAgents();
  }

  onSelectionChange(data: any) {
    this.selectedData = data;
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    this.selectedFilter === 'collaborative'
      ? this.fetchCollaborativeAgents()
      : this.updateDisplayedAgents();
  }

  onCreateAgent(): void {
    this.router.navigate([agentsConfigData.navigation.createRoute]);
  }

  private fetchCollaborativeAgents(): void {
    this.isLoading = true;
    this.agentService
      .getCollaborativeAgentsPaginated(this.currentPage, this.itemsPerPage)
      .subscribe({
        next: (response) => {
          this.totalNoOfRecords = response?.totalNoOfRecords ?? 0;
          const agentsList = this.extractAgentArray(response);
          const mappedAgents = agentsList.map((agent: any, index: number) => ({
            ...agent,
            id: agent.id?.toString() || `collab-${index}`,
            title: agent.name || agent.agentDetails || 'Unnamed Agent',
            name: agent.name || agent.agentDetails || 'Unnamed Agent',
            description:
              agent.description ||
              agent.agentDetails ||
              agent.goal ||
              'No description',
            createdDate: this.formatDate(
              agent.modifiedAt || agent.createdAt || new Date(),
            ),
            userCount: agent.users || 0,
            type: 'collaborative',
            createdBy: agent.createdBy || '',
            role: agent.role || '',
            backstory: agent.backstory || '',
            expectedOutput: agent.expectedOutput || '',
          }));
          this.collaborativeTotalPages = Math.ceil(
            this.totalNoOfRecords / this.itemsPerPage,
          );
          this.totalPages = this.collaborativeTotalPages;
          this.collaborativeAgentsData = mappedAgents;
          this.displayedAgents = mappedAgents;
          this.totalRecords = mappedAgents;
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error fetching collaborative agents:', error);
          this.error = error.message || 'Failed to load collaborative agents';
          this.isLoading = false;
        },
      });
  }

  private fetchIndividualAgents(): void {
    this.isLoading = true;
    this.agentService.getAllIndividualAgents().subscribe({
      next: (response) => {
        const agentsList = response?.individualAgents ?? response ?? [];
        const mappedAgents = agentsList.map((agent: any) => ({
          ...agent,
          id: agent.useCaseId?.toString() || agent.id?.toString() || '0',
          title: agent.useCaseName || agent.name || 'Unnamed Agent',
          name: agent.useCaseName || agent.name || 'Unnamed Agent',
          description:
            agent.useCaseDetails || agent.description || 'No description',
          createdDate: this.formatDate(agent.created_at || agent.createdAt),
          userCount: agent.users || 0,
          type: 'individual',
        }));
        this.originalIndividualAgentsData = mappedAgents;
        this.individualAgentsData = mappedAgents;
        this.totalRecords = mappedAgents;
        this.updateDisplayedAgents();
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error fetching individual agents:', error);
        this.error = error.message || 'Failed to load individual agents';
        this.isLoading = false;
      },
    });
  }

  private formatDate(dateInput: string | Date): string {
    const date = new Date(dateInput);
    return `${date.getMonth() + 1}/${date.getDate()}/${date.getFullYear()}`;
  }

  private extractAgentArray(response: any): any[] {
    if (!response) return [];
    const possibleArrays = [
      response.agentDetails,
      response.data,
      response.agents,
      response.collaborativeAgents,
    ];
    for (const arr of possibleArrays) {
      if (Array.isArray(arr)) return arr;
    }
    if (Array.isArray(response)) return response;
    const firstArrayKey = Object.keys(response).find((key) =>
      Array.isArray(response[key]),
    );
    return firstArrayKey ? response[firstArrayKey] : [];
  }

  private confirmDeleteAgent(agentId: string): void {
    const dataMap: Record<string, any[]> = {
      all: this.allAgentsData,
      individual: this.individualAgentsData,
      collaborative: this.collaborativeAgentsData,
    };
    const filteredAgents = dataMap[this.selectedFilter] ?? [];
    const agentObj = filteredAgents.find((agent) => agent.id === agentId) ||
                   this.allAgents.find((agent: any) => agent.id === agentId);
    
    if (!agentObj) return;

    this.dialogService.confirmation({
      title: 'Delete Agent',
      message: `Are you sure you want to delete "${agentObj.name || 'this agent'}"? This action cannot be undone.`,
      confirmButtonText: 'Delete',
      cancelButtonText: 'Cancel',
      destructive: true
    }).then(result => {
      if (result.confirmed) {
        this.deleteAgent(agentId);
      }
    });
  }

  private deleteAgent(agentId: string): void {
    const agent = this.getAgentById(agentId);
    if (!agent) return;

    const { name: agentName, type: agentType } = agent;
    const deleteApi$ = agentType === 'collaborative'
      ? this.agentService.deleteCollaborativeAgent(agentId)
      : this.agentService.deleteAgent(agentId);

    // Show loading dialog
    const loadingDialog = this.dialogService.loading({
      title: 'Deleting Agent',
      message: 'Please wait while we delete the agent...',
      showProgress: true
    });

    deleteApi$.subscribe({
      next: (response: any) => {
        this.removeAgentFromData(agentId);
        this.updateDisplayedAgents();
        
        // Close loading dialog
        this.dialogService.close();
        
        // Show success dialog
        this.dialogService.success({
          title: 'Agent Deleted',
          message: response?.message || `Agent "${agentName}" has been deleted successfully.`,
        }).then(() => {
          console.log('Agent deletion completed');
        });
      },
      error: (error: any) => {
        console.error('Error deleting agent:', error);
        
        // Close loading dialog
        this.dialogService.close();
        
        // Show error dialog with retry option
        this.dialogService.error({
          title: 'Error',
          message: 'Failed to delete agent. Please try again.',
          showRetryButton: true,
          retryButtonText: 'Ok'
        }).then(result => {
          if (result.action === 'retry') {
            this.deleteAgent(agentId); // Retry deletion
          }
        });
      },
    });
  }

  private editAgent(agentId: string): void {
    const dataMap: Record<string, any[]> = {
      all: this.allAgentsData,
      individual: this.individualAgentsData,
      collaborative: this.collaborativeAgentsData,
    };
    const filteredAgents = dataMap[this.selectedFilter] ?? [];
    let agent =
      filteredAgents.find((a) => a.id === agentId) ||
      this.allAgents.find((a) => a.id === agentId);
    if (!agent) {
      return;
    }
    let agentType = agent.type;
    const isCollaborative =
      this.selectedFilter === 'collaborative' ||
      agent.role ||
      agent.goal ||
      agent.backstory ||
      agent.tools;
    agentType = agentType ?? (isCollaborative ? 'collaborative' : 'individual');
    this.router.navigate(['/build/agents', agentType], {
      queryParams: {
        id: agentId,
        mode: 'edit',
      },
    });
  }

  private duplicateAgent(agentId: string): void {
    const dataMap: Record<string, any[]> = {
      all: this.allAgentsData,
      individual: this.individualAgentsData,
      collaborative: this.collaborativeAgentsData,
    };
    const filteredAgents = dataMap[this.selectedFilter] ?? [];
    let agent = filteredAgents.find((a) => a.id === agentId);
    if (!agent && this.selectedFilter !== 'all') {
      agent = this.allAgentsData.find((a) => a.id === agentId);
    }
    if (!agent) {
      return;
    }
    const isCollaborative =
      this.selectedFilter === 'collaborative' ||
      agent.role ||
      agent.goal ||
      agent.backstory ||
      agent.tools;
    const agentType = isCollaborative ? 'collaborative' : 'individual';
    this.router.navigate(['/build/agents', agentType], {
      queryParams: {
        id: agentId,
        mode: 'duplicate',
      },
    });
  }

  private executeAgent(agentId: string): void {
    const dataMap: Record<string, any[]> = {
      all: this.allAgentsData,
      individual: this.individualAgentsData,
      collaborative: this.collaborativeAgentsData,
    };
    const agent =
      dataMap[this.selectedFilter]?.find((a) => a.id === agentId) ||
      this.allAgents.find((a) => a.id === agentId);
    if (!agent) {
      return;
    }
    const isCollaborative = this.isCollaborativeAgent(agent);
    const agentType = isCollaborative ? 'collaborative' : 'individual';

    this.router.navigate(['/build/agents', agentType, 'execute'], {
      queryParams: {
        id: agentId,
      },
    });
  }

  private isCollaborativeAgent(agent: any): boolean {
    return (
      agent?.type === 'collaborative' ||
      this.selectedFilter === 'collaborative' ||
      agent?.tags?.some(
        (tag: { label: string }) => tag.label.toLowerCase() === 'collaborative',
      ) ||
      agent?.userType === 'collaborative' ||
      agent?.agentType === 'collaborative' ||
      Boolean(agent?.role || agent?.goal || agent?.backstory || agent?.tools)
    );
  }

  private updateDisplayedAgents(): void {
    const startIndex = (this.currentPage - 1) * 11;
    const endIndex = startIndex + 11;
    this.displayedAgents = this.filteredAgents.slice(startIndex, endIndex);
    this.totalPages = Math.ceil(this.filteredAgents.length / 11);
  }

  private getAgentById(agentId: string): any {
    const dataMap: Record<string, any[]> = {
      all: this.allAgentsData,
      individual: this.individualAgentsData,
      collaborative: this.collaborativeAgentsData,
    };
    return dataMap[this.selectedFilter]?.find((a) => a.id === agentId) || null;
  }

  private removeAgentFromData(agentId: string): void {
    const removeFrom = (list: any[]) =>
      list.filter((agent) => agent.id !== agentId);
    switch (this.selectedFilter) {
      case 'all':
        this.allAgentsData = removeFrom(this.allAgentsData);
        this.originalAllAgentsData = removeFrom(this.originalAllAgentsData);
        break;
      case 'individual':
        this.individualAgentsData = removeFrom(this.individualAgentsData);
        this.originalIndividualAgentsData = removeFrom(
          this.originalIndividualAgentsData,
        );
        break;
      case 'collaborative':
        this.collaborativeAgentsData = removeFrom(this.collaborativeAgentsData);
        this.originalCollaborativeAgentsData = removeFrom(
          this.originalCollaborativeAgentsData,
        );
        break;
    }
    this.allAgents = removeFrom(this.allAgents);
    this.totalRecords = removeFrom(this.totalRecords);
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

}
