{"ast": null, "code": "import _asyncToGenerator from \"C:/console/aava-ui-web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { CancellationError, onUnexpectedExternalError } from '../../../../base/common/errors.js';\nimport { DisposableStore } from '../../../../base/common/lifecycle.js';\nimport { Position } from '../../../common/core/position.js';\nimport { Range } from '../../../common/core/range.js';\nimport { Schemas } from '../../../../base/common/network.js';\nimport { URI } from '../../../../base/common/uri.js';\nexport class InlayHintAnchor {\n  constructor(range, direction) {\n    this.range = range;\n    this.direction = direction;\n  }\n}\nexport class InlayHintItem {\n  constructor(hint, anchor, provider) {\n    this.hint = hint;\n    this.anchor = anchor;\n    this.provider = provider;\n    this._isResolved = false;\n  }\n  with(delta) {\n    const result = new InlayHintItem(this.hint, delta.anchor, this.provider);\n    result._isResolved = this._isResolved;\n    result._currentResolve = this._currentResolve;\n    return result;\n  }\n  resolve(token) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (typeof _this.provider.resolveInlayHint !== 'function') {\n        return;\n      }\n      if (_this._currentResolve) {\n        // wait for an active resolve operation and try again\n        // when that's done.\n        yield _this._currentResolve;\n        if (token.isCancellationRequested) {\n          return;\n        }\n        return _this.resolve(token);\n      }\n      if (!_this._isResolved) {\n        _this._currentResolve = _this._doResolve(token).finally(() => _this._currentResolve = undefined);\n      }\n      yield _this._currentResolve;\n    })();\n  }\n  _doResolve(token) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const newHint = yield Promise.resolve(_this2.provider.resolveInlayHint(_this2.hint, token));\n        _this2.hint.tooltip = newHint?.tooltip ?? _this2.hint.tooltip;\n        _this2.hint.label = newHint?.label ?? _this2.hint.label;\n        _this2.hint.textEdits = newHint?.textEdits ?? _this2.hint.textEdits;\n        _this2._isResolved = true;\n      } catch (err) {\n        onUnexpectedExternalError(err);\n        _this2._isResolved = false;\n      }\n    })();\n  }\n}\nexport class InlayHintsFragments {\n  static {\n    this._emptyInlayHintList = Object.freeze({\n      dispose() {},\n      hints: []\n    });\n  }\n  static create(registry, model, ranges, token) {\n    return _asyncToGenerator(function* () {\n      const data = [];\n      const promises = registry.ordered(model).reverse().map(provider => ranges.map(/*#__PURE__*/function () {\n        var _ref = _asyncToGenerator(function* (range) {\n          try {\n            const result = yield provider.provideInlayHints(model, range, token);\n            if (result?.hints.length || provider.onDidChangeInlayHints) {\n              data.push([result ?? InlayHintsFragments._emptyInlayHintList, provider]);\n            }\n          } catch (err) {\n            onUnexpectedExternalError(err);\n          }\n        });\n        return function (_x) {\n          return _ref.apply(this, arguments);\n        };\n      }()));\n      yield Promise.all(promises.flat());\n      if (token.isCancellationRequested || model.isDisposed()) {\n        throw new CancellationError();\n      }\n      return new InlayHintsFragments(ranges, data, model);\n    })();\n  }\n  constructor(ranges, data, model) {\n    this._disposables = new DisposableStore();\n    this.ranges = ranges;\n    this.provider = new Set();\n    const items = [];\n    for (const [list, provider] of data) {\n      this._disposables.add(list);\n      this.provider.add(provider);\n      for (const hint of list.hints) {\n        // compute the range to which the item should be attached to\n        const position = model.validatePosition(hint.position);\n        let direction = 'before';\n        const wordRange = InlayHintsFragments._getRangeAtPosition(model, position);\n        let range;\n        if (wordRange.getStartPosition().isBefore(position)) {\n          range = Range.fromPositions(wordRange.getStartPosition(), position);\n          direction = 'after';\n        } else {\n          range = Range.fromPositions(position, wordRange.getEndPosition());\n          direction = 'before';\n        }\n        items.push(new InlayHintItem(hint, new InlayHintAnchor(range, direction), provider));\n      }\n    }\n    this.items = items.sort((a, b) => Position.compare(a.hint.position, b.hint.position));\n  }\n  dispose() {\n    this._disposables.dispose();\n  }\n  static _getRangeAtPosition(model, position) {\n    const line = position.lineNumber;\n    const word = model.getWordAtPosition(position);\n    if (word) {\n      // always prefer the word range\n      return new Range(line, word.startColumn, line, word.endColumn);\n    }\n    model.tokenization.tokenizeIfCheap(line);\n    const tokens = model.tokenization.getLineTokens(line);\n    const offset = position.column - 1;\n    const idx = tokens.findTokenIndexAtOffset(offset);\n    let start = tokens.getStartOffset(idx);\n    let end = tokens.getEndOffset(idx);\n    if (end - start === 1) {\n      // single character token, when at its end try leading/trailing token instead\n      if (start === offset && idx > 1) {\n        // leading token\n        start = tokens.getStartOffset(idx - 1);\n        end = tokens.getEndOffset(idx - 1);\n      } else if (end === offset && idx < tokens.getCount() - 1) {\n        // trailing token\n        start = tokens.getStartOffset(idx + 1);\n        end = tokens.getEndOffset(idx + 1);\n      }\n    }\n    return new Range(line, start + 1, line, end + 1);\n  }\n}\nexport function asCommandLink(command) {\n  return URI.from({\n    scheme: Schemas.command,\n    path: command.id,\n    query: command.arguments && encodeURIComponent(JSON.stringify(command.arguments))\n  }).toString();\n}", "map": {"version": 3, "names": ["CancellationError", "onUnexpectedExternalError", "DisposableStore", "Position", "Range", "<PERSON><PERSON><PERSON>", "URI", "InlayHintAnchor", "constructor", "range", "direction", "InlayHintItem", "hint", "anchor", "provider", "_isResolved", "with", "delta", "result", "_currentResolve", "resolve", "token", "_this", "_asyncToGenerator", "resolveInlayHint", "isCancellationRequested", "_doResolve", "finally", "undefined", "_this2", "newHint", "Promise", "tooltip", "label", "textEdits", "err", "InlayHintsFragments", "_emptyInlayHintList", "Object", "freeze", "dispose", "hints", "create", "registry", "model", "ranges", "data", "promises", "ordered", "reverse", "map", "_ref", "provideInlayHints", "length", "onDidChangeInlayHints", "push", "_x", "apply", "arguments", "all", "flat", "isDisposed", "_disposables", "Set", "items", "list", "add", "position", "validatePosition", "wordRange", "_getRangeAtPosition", "getStartPosition", "isBefore", "fromPositions", "getEndPosition", "sort", "a", "b", "compare", "line", "lineNumber", "word", "getWordAtPosition", "startColumn", "endColumn", "tokenization", "tokenizeIfCheap", "tokens", "getLineTokens", "offset", "column", "idx", "findTokenIndexAtOffset", "start", "getStartOffset", "end", "getEndOffset", "getCount", "asCommandLink", "command", "from", "scheme", "path", "id", "query", "encodeURIComponent", "JSON", "stringify", "toString"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/contrib/inlayHints/browser/inlayHints.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { CancellationError, onUnexpectedExternalError } from '../../../../base/common/errors.js';\nimport { DisposableStore } from '../../../../base/common/lifecycle.js';\nimport { Position } from '../../../common/core/position.js';\nimport { Range } from '../../../common/core/range.js';\nimport { Schemas } from '../../../../base/common/network.js';\nimport { URI } from '../../../../base/common/uri.js';\nexport class InlayHintAnchor {\n    constructor(range, direction) {\n        this.range = range;\n        this.direction = direction;\n    }\n}\nexport class InlayHintItem {\n    constructor(hint, anchor, provider) {\n        this.hint = hint;\n        this.anchor = anchor;\n        this.provider = provider;\n        this._isResolved = false;\n    }\n    with(delta) {\n        const result = new InlayHintItem(this.hint, delta.anchor, this.provider);\n        result._isResolved = this._isResolved;\n        result._currentResolve = this._currentResolve;\n        return result;\n    }\n    async resolve(token) {\n        if (typeof this.provider.resolveInlayHint !== 'function') {\n            return;\n        }\n        if (this._currentResolve) {\n            // wait for an active resolve operation and try again\n            // when that's done.\n            await this._currentResolve;\n            if (token.isCancellationRequested) {\n                return;\n            }\n            return this.resolve(token);\n        }\n        if (!this._isResolved) {\n            this._currentResolve = this._doResolve(token)\n                .finally(() => this._currentResolve = undefined);\n        }\n        await this._currentResolve;\n    }\n    async _doResolve(token) {\n        try {\n            const newHint = await Promise.resolve(this.provider.resolveInlayHint(this.hint, token));\n            this.hint.tooltip = newHint?.tooltip ?? this.hint.tooltip;\n            this.hint.label = newHint?.label ?? this.hint.label;\n            this.hint.textEdits = newHint?.textEdits ?? this.hint.textEdits;\n            this._isResolved = true;\n        }\n        catch (err) {\n            onUnexpectedExternalError(err);\n            this._isResolved = false;\n        }\n    }\n}\nexport class InlayHintsFragments {\n    static { this._emptyInlayHintList = Object.freeze({ dispose() { }, hints: [] }); }\n    static async create(registry, model, ranges, token) {\n        const data = [];\n        const promises = registry.ordered(model).reverse().map(provider => ranges.map(async (range) => {\n            try {\n                const result = await provider.provideInlayHints(model, range, token);\n                if (result?.hints.length || provider.onDidChangeInlayHints) {\n                    data.push([result ?? InlayHintsFragments._emptyInlayHintList, provider]);\n                }\n            }\n            catch (err) {\n                onUnexpectedExternalError(err);\n            }\n        }));\n        await Promise.all(promises.flat());\n        if (token.isCancellationRequested || model.isDisposed()) {\n            throw new CancellationError();\n        }\n        return new InlayHintsFragments(ranges, data, model);\n    }\n    constructor(ranges, data, model) {\n        this._disposables = new DisposableStore();\n        this.ranges = ranges;\n        this.provider = new Set();\n        const items = [];\n        for (const [list, provider] of data) {\n            this._disposables.add(list);\n            this.provider.add(provider);\n            for (const hint of list.hints) {\n                // compute the range to which the item should be attached to\n                const position = model.validatePosition(hint.position);\n                let direction = 'before';\n                const wordRange = InlayHintsFragments._getRangeAtPosition(model, position);\n                let range;\n                if (wordRange.getStartPosition().isBefore(position)) {\n                    range = Range.fromPositions(wordRange.getStartPosition(), position);\n                    direction = 'after';\n                }\n                else {\n                    range = Range.fromPositions(position, wordRange.getEndPosition());\n                    direction = 'before';\n                }\n                items.push(new InlayHintItem(hint, new InlayHintAnchor(range, direction), provider));\n            }\n        }\n        this.items = items.sort((a, b) => Position.compare(a.hint.position, b.hint.position));\n    }\n    dispose() {\n        this._disposables.dispose();\n    }\n    static _getRangeAtPosition(model, position) {\n        const line = position.lineNumber;\n        const word = model.getWordAtPosition(position);\n        if (word) {\n            // always prefer the word range\n            return new Range(line, word.startColumn, line, word.endColumn);\n        }\n        model.tokenization.tokenizeIfCheap(line);\n        const tokens = model.tokenization.getLineTokens(line);\n        const offset = position.column - 1;\n        const idx = tokens.findTokenIndexAtOffset(offset);\n        let start = tokens.getStartOffset(idx);\n        let end = tokens.getEndOffset(idx);\n        if (end - start === 1) {\n            // single character token, when at its end try leading/trailing token instead\n            if (start === offset && idx > 1) {\n                // leading token\n                start = tokens.getStartOffset(idx - 1);\n                end = tokens.getEndOffset(idx - 1);\n            }\n            else if (end === offset && idx < tokens.getCount() - 1) {\n                // trailing token\n                start = tokens.getStartOffset(idx + 1);\n                end = tokens.getEndOffset(idx + 1);\n            }\n        }\n        return new Range(line, start + 1, line, end + 1);\n    }\n}\nexport function asCommandLink(command) {\n    return URI.from({\n        scheme: Schemas.command,\n        path: command.id,\n        query: command.arguments && encodeURIComponent(JSON.stringify(command.arguments))\n    }).toString();\n}\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA,SAASA,iBAAiB,EAAEC,yBAAyB,QAAQ,mCAAmC;AAChG,SAASC,eAAe,QAAQ,sCAAsC;AACtE,SAASC,QAAQ,QAAQ,kCAAkC;AAC3D,SAASC,KAAK,QAAQ,+BAA+B;AACrD,SAASC,OAAO,QAAQ,oCAAoC;AAC5D,SAASC,GAAG,QAAQ,gCAAgC;AACpD,OAAO,MAAMC,eAAe,CAAC;EACzBC,WAAWA,CAACC,KAAK,EAAEC,SAAS,EAAE;IAC1B,IAAI,CAACD,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,SAAS,GAAGA,SAAS;EAC9B;AACJ;AACA,OAAO,MAAMC,aAAa,CAAC;EACvBH,WAAWA,CAACI,IAAI,EAAEC,MAAM,EAAEC,QAAQ,EAAE;IAChC,IAAI,CAACF,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,WAAW,GAAG,KAAK;EAC5B;EACAC,IAAIA,CAACC,KAAK,EAAE;IACR,MAAMC,MAAM,GAAG,IAAIP,aAAa,CAAC,IAAI,CAACC,IAAI,EAAEK,KAAK,CAACJ,MAAM,EAAE,IAAI,CAACC,QAAQ,CAAC;IACxEI,MAAM,CAACH,WAAW,GAAG,IAAI,CAACA,WAAW;IACrCG,MAAM,CAACC,eAAe,GAAG,IAAI,CAACA,eAAe;IAC7C,OAAOD,MAAM;EACjB;EACME,OAAOA,CAACC,KAAK,EAAE;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACjB,IAAI,OAAOD,KAAI,CAACR,QAAQ,CAACU,gBAAgB,KAAK,UAAU,EAAE;QACtD;MACJ;MACA,IAAIF,KAAI,CAACH,eAAe,EAAE;QACtB;QACA;QACA,MAAMG,KAAI,CAACH,eAAe;QAC1B,IAAIE,KAAK,CAACI,uBAAuB,EAAE;UAC/B;QACJ;QACA,OAAOH,KAAI,CAACF,OAAO,CAACC,KAAK,CAAC;MAC9B;MACA,IAAI,CAACC,KAAI,CAACP,WAAW,EAAE;QACnBO,KAAI,CAACH,eAAe,GAAGG,KAAI,CAACI,UAAU,CAACL,KAAK,CAAC,CACxCM,OAAO,CAAC,MAAML,KAAI,CAACH,eAAe,GAAGS,SAAS,CAAC;MACxD;MACA,MAAMN,KAAI,CAACH,eAAe;IAAC;EAC/B;EACMO,UAAUA,CAACL,KAAK,EAAE;IAAA,IAAAQ,MAAA;IAAA,OAAAN,iBAAA;MACpB,IAAI;QACA,MAAMO,OAAO,SAASC,OAAO,CAACX,OAAO,CAACS,MAAI,CAACf,QAAQ,CAACU,gBAAgB,CAACK,MAAI,CAACjB,IAAI,EAAES,KAAK,CAAC,CAAC;QACvFQ,MAAI,CAACjB,IAAI,CAACoB,OAAO,GAAGF,OAAO,EAAEE,OAAO,IAAIH,MAAI,CAACjB,IAAI,CAACoB,OAAO;QACzDH,MAAI,CAACjB,IAAI,CAACqB,KAAK,GAAGH,OAAO,EAAEG,KAAK,IAAIJ,MAAI,CAACjB,IAAI,CAACqB,KAAK;QACnDJ,MAAI,CAACjB,IAAI,CAACsB,SAAS,GAAGJ,OAAO,EAAEI,SAAS,IAAIL,MAAI,CAACjB,IAAI,CAACsB,SAAS;QAC/DL,MAAI,CAACd,WAAW,GAAG,IAAI;MAC3B,CAAC,CACD,OAAOoB,GAAG,EAAE;QACRlC,yBAAyB,CAACkC,GAAG,CAAC;QAC9BN,MAAI,CAACd,WAAW,GAAG,KAAK;MAC5B;IAAC;EACL;AACJ;AACA,OAAO,MAAMqB,mBAAmB,CAAC;EAC7B;IAAS,IAAI,CAACC,mBAAmB,GAAGC,MAAM,CAACC,MAAM,CAAC;MAAEC,OAAOA,CAAA,EAAG,CAAE,CAAC;MAAEC,KAAK,EAAE;IAAG,CAAC,CAAC;EAAE;EACjF,OAAaC,MAAMA,CAACC,QAAQ,EAAEC,KAAK,EAAEC,MAAM,EAAExB,KAAK,EAAE;IAAA,OAAAE,iBAAA;MAChD,MAAMuB,IAAI,GAAG,EAAE;MACf,MAAMC,QAAQ,GAAGJ,QAAQ,CAACK,OAAO,CAACJ,KAAK,CAAC,CAACK,OAAO,CAAC,CAAC,CAACC,GAAG,CAACpC,QAAQ,IAAI+B,MAAM,CAACK,GAAG;QAAA,IAAAC,IAAA,GAAA5B,iBAAA,CAAC,WAAOd,KAAK,EAAK;UAC3F,IAAI;YACA,MAAMS,MAAM,SAASJ,QAAQ,CAACsC,iBAAiB,CAACR,KAAK,EAAEnC,KAAK,EAAEY,KAAK,CAAC;YACpE,IAAIH,MAAM,EAAEuB,KAAK,CAACY,MAAM,IAAIvC,QAAQ,CAACwC,qBAAqB,EAAE;cACxDR,IAAI,CAACS,IAAI,CAAC,CAACrC,MAAM,IAAIkB,mBAAmB,CAACC,mBAAmB,EAAEvB,QAAQ,CAAC,CAAC;YAC5E;UACJ,CAAC,CACD,OAAOqB,GAAG,EAAE;YACRlC,yBAAyB,CAACkC,GAAG,CAAC;UAClC;QACJ,CAAC;QAAA,iBAAAqB,EAAA;UAAA,OAAAL,IAAA,CAAAM,KAAA,OAAAC,SAAA;QAAA;MAAA,IAAC,CAAC;MACH,MAAM3B,OAAO,CAAC4B,GAAG,CAACZ,QAAQ,CAACa,IAAI,CAAC,CAAC,CAAC;MAClC,IAAIvC,KAAK,CAACI,uBAAuB,IAAImB,KAAK,CAACiB,UAAU,CAAC,CAAC,EAAE;QACrD,MAAM,IAAI7D,iBAAiB,CAAC,CAAC;MACjC;MACA,OAAO,IAAIoC,mBAAmB,CAACS,MAAM,EAAEC,IAAI,EAAEF,KAAK,CAAC;IAAC;EACxD;EACApC,WAAWA,CAACqC,MAAM,EAAEC,IAAI,EAAEF,KAAK,EAAE;IAC7B,IAAI,CAACkB,YAAY,GAAG,IAAI5D,eAAe,CAAC,CAAC;IACzC,IAAI,CAAC2C,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC/B,QAAQ,GAAG,IAAIiD,GAAG,CAAC,CAAC;IACzB,MAAMC,KAAK,GAAG,EAAE;IAChB,KAAK,MAAM,CAACC,IAAI,EAAEnD,QAAQ,CAAC,IAAIgC,IAAI,EAAE;MACjC,IAAI,CAACgB,YAAY,CAACI,GAAG,CAACD,IAAI,CAAC;MAC3B,IAAI,CAACnD,QAAQ,CAACoD,GAAG,CAACpD,QAAQ,CAAC;MAC3B,KAAK,MAAMF,IAAI,IAAIqD,IAAI,CAACxB,KAAK,EAAE;QAC3B;QACA,MAAM0B,QAAQ,GAAGvB,KAAK,CAACwB,gBAAgB,CAACxD,IAAI,CAACuD,QAAQ,CAAC;QACtD,IAAIzD,SAAS,GAAG,QAAQ;QACxB,MAAM2D,SAAS,GAAGjC,mBAAmB,CAACkC,mBAAmB,CAAC1B,KAAK,EAAEuB,QAAQ,CAAC;QAC1E,IAAI1D,KAAK;QACT,IAAI4D,SAAS,CAACE,gBAAgB,CAAC,CAAC,CAACC,QAAQ,CAACL,QAAQ,CAAC,EAAE;UACjD1D,KAAK,GAAGL,KAAK,CAACqE,aAAa,CAACJ,SAAS,CAACE,gBAAgB,CAAC,CAAC,EAAEJ,QAAQ,CAAC;UACnEzD,SAAS,GAAG,OAAO;QACvB,CAAC,MACI;UACDD,KAAK,GAAGL,KAAK,CAACqE,aAAa,CAACN,QAAQ,EAAEE,SAAS,CAACK,cAAc,CAAC,CAAC,CAAC;UACjEhE,SAAS,GAAG,QAAQ;QACxB;QACAsD,KAAK,CAACT,IAAI,CAAC,IAAI5C,aAAa,CAACC,IAAI,EAAE,IAAIL,eAAe,CAACE,KAAK,EAAEC,SAAS,CAAC,EAAEI,QAAQ,CAAC,CAAC;MACxF;IACJ;IACA,IAAI,CAACkD,KAAK,GAAGA,KAAK,CAACW,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK1E,QAAQ,CAAC2E,OAAO,CAACF,CAAC,CAAChE,IAAI,CAACuD,QAAQ,EAAEU,CAAC,CAACjE,IAAI,CAACuD,QAAQ,CAAC,CAAC;EACzF;EACA3B,OAAOA,CAAA,EAAG;IACN,IAAI,CAACsB,YAAY,CAACtB,OAAO,CAAC,CAAC;EAC/B;EACA,OAAO8B,mBAAmBA,CAAC1B,KAAK,EAAEuB,QAAQ,EAAE;IACxC,MAAMY,IAAI,GAAGZ,QAAQ,CAACa,UAAU;IAChC,MAAMC,IAAI,GAAGrC,KAAK,CAACsC,iBAAiB,CAACf,QAAQ,CAAC;IAC9C,IAAIc,IAAI,EAAE;MACN;MACA,OAAO,IAAI7E,KAAK,CAAC2E,IAAI,EAAEE,IAAI,CAACE,WAAW,EAAEJ,IAAI,EAAEE,IAAI,CAACG,SAAS,CAAC;IAClE;IACAxC,KAAK,CAACyC,YAAY,CAACC,eAAe,CAACP,IAAI,CAAC;IACxC,MAAMQ,MAAM,GAAG3C,KAAK,CAACyC,YAAY,CAACG,aAAa,CAACT,IAAI,CAAC;IACrD,MAAMU,MAAM,GAAGtB,QAAQ,CAACuB,MAAM,GAAG,CAAC;IAClC,MAAMC,GAAG,GAAGJ,MAAM,CAACK,sBAAsB,CAACH,MAAM,CAAC;IACjD,IAAII,KAAK,GAAGN,MAAM,CAACO,cAAc,CAACH,GAAG,CAAC;IACtC,IAAII,GAAG,GAAGR,MAAM,CAACS,YAAY,CAACL,GAAG,CAAC;IAClC,IAAII,GAAG,GAAGF,KAAK,KAAK,CAAC,EAAE;MACnB;MACA,IAAIA,KAAK,KAAKJ,MAAM,IAAIE,GAAG,GAAG,CAAC,EAAE;QAC7B;QACAE,KAAK,GAAGN,MAAM,CAACO,cAAc,CAACH,GAAG,GAAG,CAAC,CAAC;QACtCI,GAAG,GAAGR,MAAM,CAACS,YAAY,CAACL,GAAG,GAAG,CAAC,CAAC;MACtC,CAAC,MACI,IAAII,GAAG,KAAKN,MAAM,IAAIE,GAAG,GAAGJ,MAAM,CAACU,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAE;QACpD;QACAJ,KAAK,GAAGN,MAAM,CAACO,cAAc,CAACH,GAAG,GAAG,CAAC,CAAC;QACtCI,GAAG,GAAGR,MAAM,CAACS,YAAY,CAACL,GAAG,GAAG,CAAC,CAAC;MACtC;IACJ;IACA,OAAO,IAAIvF,KAAK,CAAC2E,IAAI,EAAEc,KAAK,GAAG,CAAC,EAAEd,IAAI,EAAEgB,GAAG,GAAG,CAAC,CAAC;EACpD;AACJ;AACA,OAAO,SAASG,aAAaA,CAACC,OAAO,EAAE;EACnC,OAAO7F,GAAG,CAAC8F,IAAI,CAAC;IACZC,MAAM,EAAEhG,OAAO,CAAC8F,OAAO;IACvBG,IAAI,EAAEH,OAAO,CAACI,EAAE;IAChBC,KAAK,EAAEL,OAAO,CAACzC,SAAS,IAAI+C,kBAAkB,CAACC,IAAI,CAACC,SAAS,CAACR,OAAO,CAACzC,SAAS,CAAC;EACpF,CAAC,CAAC,CAACkD,QAAQ,CAAC,CAAC;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}