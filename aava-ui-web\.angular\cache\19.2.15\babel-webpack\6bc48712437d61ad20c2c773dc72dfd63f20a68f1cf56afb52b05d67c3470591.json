{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n// allow-any-unicode-comment-file\n/**\n * Gets alternative Korean characters for the character code. This will return the ascii\n * character code(s) that a Hangul character may have been input with using a qwerty layout.\n *\n * This only aims to cover modern (not archaic) Hangul syllables.\n *\n * @param code The character code to get alternate characters for\n */\nexport function getKoreanAltChars(code) {\n  const result = disassembleKorean(code);\n  if (result && result.length > 0) {\n    return new Uint32Array(result);\n  }\n  return undefined;\n}\nlet codeBufferLength = 0;\nconst codeBuffer = new Uint32Array(10);\nfunction disassembleKorean(code) {\n  codeBufferLength = 0;\n  // Initial consonants (초성)\n  getCodesFromArray(code, modernConsonants, 4352 /* HangulRangeStartCode.InitialConsonant */);\n  if (codeBufferLength > 0) {\n    return codeBuffer.subarray(0, codeBufferLength);\n  }\n  // Vowels (중성)\n  getCodesFromArray(code, modernVowels, 4449 /* HangulRangeStartCode.Vowel */);\n  if (codeBufferLength > 0) {\n    return codeBuffer.subarray(0, codeBufferLength);\n  }\n  // Final consonants (종성)\n  getCodesFromArray(code, modernFinalConsonants, 4520 /* HangulRangeStartCode.FinalConsonant */);\n  if (codeBufferLength > 0) {\n    return codeBuffer.subarray(0, codeBufferLength);\n  }\n  // Hangul Compatibility Jamo\n  getCodesFromArray(code, compatibilityJamo, 12593 /* HangulRangeStartCode.CompatibilityJamo */);\n  if (codeBufferLength) {\n    return codeBuffer.subarray(0, codeBufferLength);\n  }\n  // Hangul Syllables\n  if (code >= 0xAC00 && code <= 0xD7A3) {\n    const hangulIndex = code - 0xAC00;\n    const vowelAndFinalConsonantProduct = hangulIndex % 588;\n    // 0-based starting at 0x1100\n    const initialConsonantIndex = Math.floor(hangulIndex / 588);\n    // 0-based starting at 0x1161\n    const vowelIndex = Math.floor(vowelAndFinalConsonantProduct / 28);\n    // 0-based starting at 0x11A8\n    // Subtract 1 as the standard algorithm uses the 0 index to represent no\n    // final consonant\n    const finalConsonantIndex = vowelAndFinalConsonantProduct % 28 - 1;\n    if (initialConsonantIndex < modernConsonants.length) {\n      getCodesFromArray(initialConsonantIndex, modernConsonants, 0);\n    } else if (4352 /* HangulRangeStartCode.InitialConsonant */ + initialConsonantIndex - 12593 /* HangulRangeStartCode.CompatibilityJamo */ < compatibilityJamo.length) {\n      getCodesFromArray(4352 /* HangulRangeStartCode.InitialConsonant */ + initialConsonantIndex, compatibilityJamo, 12593 /* HangulRangeStartCode.CompatibilityJamo */);\n    }\n    if (vowelIndex < modernVowels.length) {\n      getCodesFromArray(vowelIndex, modernVowels, 0);\n    } else if (4449 /* HangulRangeStartCode.Vowel */ + vowelIndex - 12593 /* HangulRangeStartCode.CompatibilityJamo */ < compatibilityJamo.length) {\n      getCodesFromArray(4449 /* HangulRangeStartCode.Vowel */ + vowelIndex - 12593 /* HangulRangeStartCode.CompatibilityJamo */, compatibilityJamo, 12593 /* HangulRangeStartCode.CompatibilityJamo */);\n    }\n    if (finalConsonantIndex >= 0) {\n      if (finalConsonantIndex < modernFinalConsonants.length) {\n        getCodesFromArray(finalConsonantIndex, modernFinalConsonants, 0);\n      } else if (4520 /* HangulRangeStartCode.FinalConsonant */ + finalConsonantIndex - 12593 /* HangulRangeStartCode.CompatibilityJamo */ < compatibilityJamo.length) {\n        getCodesFromArray(4520 /* HangulRangeStartCode.FinalConsonant */ + finalConsonantIndex - 12593 /* HangulRangeStartCode.CompatibilityJamo */, compatibilityJamo, 12593 /* HangulRangeStartCode.CompatibilityJamo */);\n      }\n    }\n    if (codeBufferLength > 0) {\n      return codeBuffer.subarray(0, codeBufferLength);\n    }\n  }\n  return undefined;\n}\nfunction getCodesFromArray(code, array, arrayStartIndex) {\n  // Verify the code is within the array's range\n  if (code >= arrayStartIndex && code < arrayStartIndex + array.length) {\n    addCodesToBuffer(array[code - arrayStartIndex]);\n  }\n}\nfunction addCodesToBuffer(codes) {\n  // NUL is ignored, this is used for archaic characters to avoid using a Map\n  // for the data\n  if (codes === 0 /* AsciiCode.NUL */) {\n    return;\n  }\n  // Number stored in format: OptionalThirdCode << 16 | OptionalSecondCode << 8 | Code\n  codeBuffer[codeBufferLength++] = codes & 0xFF;\n  if (codes >> 8) {\n    codeBuffer[codeBufferLength++] = codes >> 8 & 0xFF;\n  }\n  if (codes >> 16) {\n    codeBuffer[codeBufferLength++] = codes >> 16 & 0xFF;\n  }\n}\n/**\n * Hangul Jamo - Modern consonants #1\n *\n * Range U+1100..U+1112\n *\n * |        | 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | A | B | C | D | E | F |\n * |--------|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|\n * | U+110x | ᄀ | ᄁ | ᄂ | ᄃ | ᄄ | ᄅ | ᄆ | ᄇ | ᄈ | ᄉ | ᄊ | ᄋ | ᄌ | ᄍ | ᄎ | ᄏ |\n * | U+111x | ᄐ | ᄑ | ᄒ |\n */\nconst modernConsonants = new Uint8Array([114 /* AsciiCode.r */,\n// ㄱ\n82 /* AsciiCode.R */,\n// ㄲ\n115 /* AsciiCode.s */,\n// ㄴ\n101 /* AsciiCode.e */,\n// ㄷ\n69 /* AsciiCode.E */,\n// ㄸ\n102 /* AsciiCode.f */,\n// ㄹ\n97 /* AsciiCode.a */,\n// ㅁ\n113 /* AsciiCode.q */,\n// ㅂ\n81 /* AsciiCode.Q */,\n// ㅃ\n116 /* AsciiCode.t */,\n// ㅅ\n84 /* AsciiCode.T */,\n// ㅆ\n100 /* AsciiCode.d */,\n// ㅇ\n119 /* AsciiCode.w */,\n// ㅈ\n87 /* AsciiCode.W */,\n// ㅉ\n99 /* AsciiCode.c */,\n// ㅊ\n122 /* AsciiCode.z */,\n// ㅋ\n120 /* AsciiCode.x */,\n// ㅌ\n118 /* AsciiCode.v */,\n// ㅍ\n103 /* AsciiCode.g */ // ㅎ\n]);\n/**\n * Hangul Jamo - Modern Vowels\n *\n * Range U+1161..U+1175\n *\n * |        | 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | A | B | C | D | E | F |\n * |--------|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|\n * | U+116x |   | ᅡ | ᅢ | ᅣ | ᅤ | ᅥ | ᅦ | ᅧ | ᅨ | ᅩ | ᅪ | ᅫ | ᅬ | ᅭ | ᅮ | ᅯ |\n * | U+117x | ᅰ | ᅱ | ᅲ | ᅳ | ᅴ | ᅵ |\n */\nconst modernVowels = new Uint16Array([107 /* AsciiCode.k */,\n//  -> ㅏ\n111 /* AsciiCode.o */,\n//  -> ㅐ\n105 /* AsciiCode.i */,\n//  -> ㅑ\n79 /* AsciiCode.O */,\n//  -> ㅒ\n106 /* AsciiCode.j */,\n//  -> ㅓ\n112 /* AsciiCode.p */,\n//  -> ㅔ\n117 /* AsciiCode.u */,\n//  -> ㅕ\n80 /* AsciiCode.P */,\n//  -> ㅖ\n104 /* AsciiCode.h */,\n//  -> ㅗ\n27496 /* AsciiCodeCombo.hk */,\n//  -> ㅘ\n28520 /* AsciiCodeCombo.ho */,\n//  -> ㅙ\n27752 /* AsciiCodeCombo.hl */,\n//  -> ㅚ\n121 /* AsciiCode.y */,\n//  -> ㅛ\n110 /* AsciiCode.n */,\n//  -> ㅜ\n27246 /* AsciiCodeCombo.nj */,\n//  -> ㅝ\n28782 /* AsciiCodeCombo.np */,\n//  -> ㅞ\n27758 /* AsciiCodeCombo.nl */,\n//  -> ㅟ\n98 /* AsciiCode.b */,\n//  -> ㅠ\n109 /* AsciiCode.m */,\n//  -> ㅡ\n27757 /* AsciiCodeCombo.ml */,\n//  -> ㅢ\n108 /* AsciiCode.l */ //  -> ㅣ\n]);\n/**\n * Hangul Jamo - Modern Consonants #2\n *\n * Range U+11A8..U+11C2\n *\n * |        | 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | A | B | C | D | E | F |\n * |--------|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|\n * | U+11Ax |   |   |   |   |   |   |   |   | ᆨ | ᆩ | ᆪ | ᆫ | ᆬ | ᆭ | ᆮ | ᆯ |\n * | U+11Bx | ᆰ | ᆱ | ᆲ | ᆳ | ᆴ | ᆵ | ᆶ | ᆷ | ᆸ | ᆹ | ᆺ | ᆻ | ᆼ | ᆽ | ᆾ | ᆿ |\n * | U+11Cx | ᇀ | ᇁ | ᇂ |\n */\nconst modernFinalConsonants = new Uint16Array([114 /* AsciiCode.r */,\n// ㄱ\n82 /* AsciiCode.R */,\n// ㄲ\n29810 /* AsciiCodeCombo.rt */,\n// ㄳ\n115 /* AsciiCode.s */,\n// ㄴ\n30579 /* AsciiCodeCombo.sw */,\n// ㄵ\n26483 /* AsciiCodeCombo.sg */,\n// ㄶ\n101 /* AsciiCode.e */,\n// ㄷ\n102 /* AsciiCode.f */,\n// ㄹ\n29286 /* AsciiCodeCombo.fr */,\n// ㄺ\n24934 /* AsciiCodeCombo.fa */,\n// ㄻ\n29030 /* AsciiCodeCombo.fq */,\n// ㄼ\n29798 /* AsciiCodeCombo.ft */,\n// ㄽ\n30822 /* AsciiCodeCombo.fx */,\n// ㄾ\n30310 /* AsciiCodeCombo.fv */,\n// ㄿ\n26470 /* AsciiCodeCombo.fg */,\n// ㅀ\n97 /* AsciiCode.a */,\n// ㅁ\n113 /* AsciiCode.q */,\n// ㅂ\n29809 /* AsciiCodeCombo.qt */,\n// ㅄ\n116 /* AsciiCode.t */,\n// ㅅ\n84 /* AsciiCode.T */,\n// ㅆ\n100 /* AsciiCode.d */,\n// ㅇ\n119 /* AsciiCode.w */,\n// ㅈ\n99 /* AsciiCode.c */,\n// ㅊ\n122 /* AsciiCode.z */,\n// ㅋ\n120 /* AsciiCode.x */,\n// ㅌ\n118 /* AsciiCode.v */,\n// ㅍ\n103 /* AsciiCode.g */ // ㅎ\n]);\n/**\n * Hangul Compatibility Jamo\n *\n * Range U+3131..U+318F\n *\n * This includes range includes archaic jamo which we don't consider, these are\n * given the NUL character code in order to be ignored.\n *\n * |        | 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | A | B | C | D | E | F |\n * |--------|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|\n * | U+313x |   | ㄱ | ㄲ | ㄳ | ㄴ | ㄵ | ㄶ | ㄷ | ㄸ | ㄹ | ㄺ | ㄻ | ㄼ | ㄽ | ㄾ | ㄿ |\n * | U+314x | ㅀ | ㅁ | ㅂ | ㅃ | ㅄ | ㅅ | ㅆ | ㅇ | ㅈ | ㅉ | ㅊ | ㅋ | ㅌ | ㅍ | ㅎ | ㅏ |\n * | U+315x | ㅐ | ㅑ | ㅒ | ㅓ | ㅔ | ㅕ | ㅖ | ㅗ | ㅘ | ㅙ | ㅚ | ㅛ | ㅜ | ㅝ | ㅞ | ㅟ |\n * | U+316x | ㅠ | ㅡ | ㅢ | ㅣ | HF | ㅥ | ㅦ | ㅧ | ㅨ | ㅩ | ㅪ | ㅫ | ㅬ | ㅭ | ㅮ | ㅯ |\n * | U+317x | ㅰ | ㅱ | ㅲ | ㅳ | ㅴ | ㅵ | ㅶ | ㅷ | ㅸ | ㅹ | ㅺ | ㅻ | ㅼ | ㅽ | ㅾ | ㅿ |\n * | U+318x | ㆀ | ㆁ | ㆂ | ㆃ | ㆄ | ㆅ | ㆆ | ㆇ | ㆈ | ㆉ | ㆊ | ㆋ | ㆌ | ㆍ | ㆎ |\n */\nconst compatibilityJamo = new Uint16Array([114 /* AsciiCode.r */,\n// ㄱ\n82 /* AsciiCode.R */,\n// ㄲ\n29810 /* AsciiCodeCombo.rt */,\n// ㄳ\n115 /* AsciiCode.s */,\n// ㄴ\n30579 /* AsciiCodeCombo.sw */,\n// ㄵ\n26483 /* AsciiCodeCombo.sg */,\n// ㄶ\n101 /* AsciiCode.e */,\n// ㄷ\n69 /* AsciiCode.E */,\n// ㄸ\n102 /* AsciiCode.f */,\n// ㄹ\n29286 /* AsciiCodeCombo.fr */,\n// ㄺ\n24934 /* AsciiCodeCombo.fa */,\n// ㄻ\n29030 /* AsciiCodeCombo.fq */,\n// ㄼ\n29798 /* AsciiCodeCombo.ft */,\n// ㄽ\n30822 /* AsciiCodeCombo.fx */,\n// ㄾ\n30310 /* AsciiCodeCombo.fv */,\n// ㄿ\n26470 /* AsciiCodeCombo.fg */,\n// ㅀ\n97 /* AsciiCode.a */,\n// ㅁ\n113 /* AsciiCode.q */,\n// ㅂ\n81 /* AsciiCode.Q */,\n// ㅃ\n29809 /* AsciiCodeCombo.qt */,\n// ㅄ\n116 /* AsciiCode.t */,\n// ㅅ\n84 /* AsciiCode.T */,\n// ㅆ\n100 /* AsciiCode.d */,\n// ㅇ\n119 /* AsciiCode.w */,\n// ㅈ\n87 /* AsciiCode.W */,\n// ㅉ\n99 /* AsciiCode.c */,\n// ㅊ\n122 /* AsciiCode.z */,\n// ㅋ\n120 /* AsciiCode.x */,\n// ㅌ\n118 /* AsciiCode.v */,\n// ㅍ\n103 /* AsciiCode.g */,\n// ㅎ\n107 /* AsciiCode.k */,\n// ㅏ\n111 /* AsciiCode.o */,\n// ㅐ\n105 /* AsciiCode.i */,\n// ㅑ\n79 /* AsciiCode.O */,\n// ㅒ\n106 /* AsciiCode.j */,\n// ㅓ\n112 /* AsciiCode.p */,\n// ㅔ\n117 /* AsciiCode.u */,\n// ㅕ\n80 /* AsciiCode.P */,\n// ㅖ\n104 /* AsciiCode.h */,\n// ㅗ\n27496 /* AsciiCodeCombo.hk */,\n// ㅘ\n28520 /* AsciiCodeCombo.ho */,\n// ㅙ\n27752 /* AsciiCodeCombo.hl */,\n// ㅚ\n121 /* AsciiCode.y */,\n// ㅛ\n110 /* AsciiCode.n */,\n// ㅜ\n27246 /* AsciiCodeCombo.nj */,\n// ㅝ\n28782 /* AsciiCodeCombo.np */,\n// ㅞ\n27758 /* AsciiCodeCombo.nl */,\n// ㅟ\n98 /* AsciiCode.b */,\n// ㅠ\n109 /* AsciiCode.m */,\n// ㅡ\n27757 /* AsciiCodeCombo.ml */,\n// ㅢ\n108 /* AsciiCode.l */ // ㅣ\n// HF: Hangul Filler (everything after this is archaic)\n// ㅥ\n// ㅦ\n// ㅧ\n// ㅨ\n// ㅩ\n// ㅪ\n// ㅫ\n// ㅬ\n// ㅮ\n// ㅯ\n// ㅰ\n// ㅱ\n// ㅲ\n// ㅳ\n// ㅴ\n// ㅵ\n// ㅶ\n// ㅷ\n// ㅸ\n// ㅹ\n// ㅺ\n// ㅻ\n// ㅼ\n// ㅽ\n// ㅾ\n// ㅿ\n// ㆀ\n// ㆁ\n// ㆂ\n// ㆃ\n// ㆄ\n// ㆅ\n// ㆆ\n// ㆇ\n// ㆈ\n// ㆉ\n// ㆊ\n// ㆋ\n// ㆌ\n// ㆍ\n// ㆎ\n]);", "map": {"version": 3, "names": ["getKoreanAltChars", "code", "result", "disassembleKorean", "length", "Uint32Array", "undefined", "codeBufferLength", "codeBuffer", "getCodesFromArray", "modernConsonants", "subarray", "modernVowels", "modernFinalConsonants", "compatibilityJamo", "hangulIndex", "vowelAndFinalConsonantProduct", "initialConsonantIndex", "Math", "floor", "vowelIndex", "finalConsonantIndex", "array", "arrayStartIndex", "addCodesToBuffer", "codes", "Uint8Array", "Uint16Array"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/common/naturalLanguage/korean.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n// allow-any-unicode-comment-file\n/**\n * Gets alternative Korean characters for the character code. This will return the ascii\n * character code(s) that a Hangul character may have been input with using a qwerty layout.\n *\n * This only aims to cover modern (not archaic) Hangul syllables.\n *\n * @param code The character code to get alternate characters for\n */\nexport function getKoreanAltChars(code) {\n    const result = disassembleKorean(code);\n    if (result && result.length > 0) {\n        return new Uint32Array(result);\n    }\n    return undefined;\n}\nlet codeBufferLength = 0;\nconst codeBuffer = new Uint32Array(10);\nfunction disassembleKorean(code) {\n    codeBufferLength = 0;\n    // Initial consonants (초성)\n    getCodesFromArray(code, modernConsonants, 4352 /* HangulRangeStartCode.InitialConsonant */);\n    if (codeBufferLength > 0) {\n        return codeBuffer.subarray(0, codeBufferLength);\n    }\n    // Vowels (중성)\n    getCodesFromArray(code, modernVowels, 4449 /* HangulRangeStartCode.Vowel */);\n    if (codeBufferLength > 0) {\n        return codeBuffer.subarray(0, codeBufferLength);\n    }\n    // Final consonants (종성)\n    getCodesFromArray(code, modernFinalConsonants, 4520 /* HangulRangeStartCode.FinalConsonant */);\n    if (codeBufferLength > 0) {\n        return codeBuffer.subarray(0, codeBufferLength);\n    }\n    // Hangul Compatibility Jamo\n    getCodesFromArray(code, compatibilityJamo, 12593 /* HangulRangeStartCode.CompatibilityJamo */);\n    if (codeBufferLength) {\n        return codeBuffer.subarray(0, codeBufferLength);\n    }\n    // Hangul Syllables\n    if (code >= 0xAC00 && code <= 0xD7A3) {\n        const hangulIndex = code - 0xAC00;\n        const vowelAndFinalConsonantProduct = hangulIndex % 588;\n        // 0-based starting at 0x1100\n        const initialConsonantIndex = Math.floor(hangulIndex / 588);\n        // 0-based starting at 0x1161\n        const vowelIndex = Math.floor(vowelAndFinalConsonantProduct / 28);\n        // 0-based starting at 0x11A8\n        // Subtract 1 as the standard algorithm uses the 0 index to represent no\n        // final consonant\n        const finalConsonantIndex = vowelAndFinalConsonantProduct % 28 - 1;\n        if (initialConsonantIndex < modernConsonants.length) {\n            getCodesFromArray(initialConsonantIndex, modernConsonants, 0);\n        }\n        else if (4352 /* HangulRangeStartCode.InitialConsonant */ + initialConsonantIndex - 12593 /* HangulRangeStartCode.CompatibilityJamo */ < compatibilityJamo.length) {\n            getCodesFromArray(4352 /* HangulRangeStartCode.InitialConsonant */ + initialConsonantIndex, compatibilityJamo, 12593 /* HangulRangeStartCode.CompatibilityJamo */);\n        }\n        if (vowelIndex < modernVowels.length) {\n            getCodesFromArray(vowelIndex, modernVowels, 0);\n        }\n        else if (4449 /* HangulRangeStartCode.Vowel */ + vowelIndex - 12593 /* HangulRangeStartCode.CompatibilityJamo */ < compatibilityJamo.length) {\n            getCodesFromArray(4449 /* HangulRangeStartCode.Vowel */ + vowelIndex - 12593 /* HangulRangeStartCode.CompatibilityJamo */, compatibilityJamo, 12593 /* HangulRangeStartCode.CompatibilityJamo */);\n        }\n        if (finalConsonantIndex >= 0) {\n            if (finalConsonantIndex < modernFinalConsonants.length) {\n                getCodesFromArray(finalConsonantIndex, modernFinalConsonants, 0);\n            }\n            else if (4520 /* HangulRangeStartCode.FinalConsonant */ + finalConsonantIndex - 12593 /* HangulRangeStartCode.CompatibilityJamo */ < compatibilityJamo.length) {\n                getCodesFromArray(4520 /* HangulRangeStartCode.FinalConsonant */ + finalConsonantIndex - 12593 /* HangulRangeStartCode.CompatibilityJamo */, compatibilityJamo, 12593 /* HangulRangeStartCode.CompatibilityJamo */);\n            }\n        }\n        if (codeBufferLength > 0) {\n            return codeBuffer.subarray(0, codeBufferLength);\n        }\n    }\n    return undefined;\n}\nfunction getCodesFromArray(code, array, arrayStartIndex) {\n    // Verify the code is within the array's range\n    if (code >= arrayStartIndex && code < arrayStartIndex + array.length) {\n        addCodesToBuffer(array[code - arrayStartIndex]);\n    }\n}\nfunction addCodesToBuffer(codes) {\n    // NUL is ignored, this is used for archaic characters to avoid using a Map\n    // for the data\n    if (codes === 0 /* AsciiCode.NUL */) {\n        return;\n    }\n    // Number stored in format: OptionalThirdCode << 16 | OptionalSecondCode << 8 | Code\n    codeBuffer[codeBufferLength++] = codes & 0xFF;\n    if (codes >> 8) {\n        codeBuffer[codeBufferLength++] = (codes >> 8) & 0xFF;\n    }\n    if (codes >> 16) {\n        codeBuffer[codeBufferLength++] = (codes >> 16) & 0xFF;\n    }\n}\n/**\n * Hangul Jamo - Modern consonants #1\n *\n * Range U+1100..U+1112\n *\n * |        | 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | A | B | C | D | E | F |\n * |--------|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|\n * | U+110x | ᄀ | ᄁ | ᄂ | ᄃ | ᄄ | ᄅ | ᄆ | ᄇ | ᄈ | ᄉ | ᄊ | ᄋ | ᄌ | ᄍ | ᄎ | ᄏ |\n * | U+111x | ᄐ | ᄑ | ᄒ |\n */\nconst modernConsonants = new Uint8Array([\n    114 /* AsciiCode.r */, // ㄱ\n    82 /* AsciiCode.R */, // ㄲ\n    115 /* AsciiCode.s */, // ㄴ\n    101 /* AsciiCode.e */, // ㄷ\n    69 /* AsciiCode.E */, // ㄸ\n    102 /* AsciiCode.f */, // ㄹ\n    97 /* AsciiCode.a */, // ㅁ\n    113 /* AsciiCode.q */, // ㅂ\n    81 /* AsciiCode.Q */, // ㅃ\n    116 /* AsciiCode.t */, // ㅅ\n    84 /* AsciiCode.T */, // ㅆ\n    100 /* AsciiCode.d */, // ㅇ\n    119 /* AsciiCode.w */, // ㅈ\n    87 /* AsciiCode.W */, // ㅉ\n    99 /* AsciiCode.c */, // ㅊ\n    122 /* AsciiCode.z */, // ㅋ\n    120 /* AsciiCode.x */, // ㅌ\n    118 /* AsciiCode.v */, // ㅍ\n    103 /* AsciiCode.g */, // ㅎ\n]);\n/**\n * Hangul Jamo - Modern Vowels\n *\n * Range U+1161..U+1175\n *\n * |        | 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | A | B | C | D | E | F |\n * |--------|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|\n * | U+116x |   | ᅡ | ᅢ | ᅣ | ᅤ | ᅥ | ᅦ | ᅧ | ᅨ | ᅩ | ᅪ | ᅫ | ᅬ | ᅭ | ᅮ | ᅯ |\n * | U+117x | ᅰ | ᅱ | ᅲ | ᅳ | ᅴ | ᅵ |\n */\nconst modernVowels = new Uint16Array([\n    107 /* AsciiCode.k */, //  -> ㅏ\n    111 /* AsciiCode.o */, //  -> ㅐ\n    105 /* AsciiCode.i */, //  -> ㅑ\n    79 /* AsciiCode.O */, //  -> ㅒ\n    106 /* AsciiCode.j */, //  -> ㅓ\n    112 /* AsciiCode.p */, //  -> ㅔ\n    117 /* AsciiCode.u */, //  -> ㅕ\n    80 /* AsciiCode.P */, //  -> ㅖ\n    104 /* AsciiCode.h */, //  -> ㅗ\n    27496 /* AsciiCodeCombo.hk */, //  -> ㅘ\n    28520 /* AsciiCodeCombo.ho */, //  -> ㅙ\n    27752 /* AsciiCodeCombo.hl */, //  -> ㅚ\n    121 /* AsciiCode.y */, //  -> ㅛ\n    110 /* AsciiCode.n */, //  -> ㅜ\n    27246 /* AsciiCodeCombo.nj */, //  -> ㅝ\n    28782 /* AsciiCodeCombo.np */, //  -> ㅞ\n    27758 /* AsciiCodeCombo.nl */, //  -> ㅟ\n    98 /* AsciiCode.b */, //  -> ㅠ\n    109 /* AsciiCode.m */, //  -> ㅡ\n    27757 /* AsciiCodeCombo.ml */, //  -> ㅢ\n    108 /* AsciiCode.l */, //  -> ㅣ\n]);\n/**\n * Hangul Jamo - Modern Consonants #2\n *\n * Range U+11A8..U+11C2\n *\n * |        | 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | A | B | C | D | E | F |\n * |--------|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|\n * | U+11Ax |   |   |   |   |   |   |   |   | ᆨ | ᆩ | ᆪ | ᆫ | ᆬ | ᆭ | ᆮ | ᆯ |\n * | U+11Bx | ᆰ | ᆱ | ᆲ | ᆳ | ᆴ | ᆵ | ᆶ | ᆷ | ᆸ | ᆹ | ᆺ | ᆻ | ᆼ | ᆽ | ᆾ | ᆿ |\n * | U+11Cx | ᇀ | ᇁ | ᇂ |\n */\nconst modernFinalConsonants = new Uint16Array([\n    114 /* AsciiCode.r */, // ㄱ\n    82 /* AsciiCode.R */, // ㄲ\n    29810 /* AsciiCodeCombo.rt */, // ㄳ\n    115 /* AsciiCode.s */, // ㄴ\n    30579 /* AsciiCodeCombo.sw */, // ㄵ\n    26483 /* AsciiCodeCombo.sg */, // ㄶ\n    101 /* AsciiCode.e */, // ㄷ\n    102 /* AsciiCode.f */, // ㄹ\n    29286 /* AsciiCodeCombo.fr */, // ㄺ\n    24934 /* AsciiCodeCombo.fa */, // ㄻ\n    29030 /* AsciiCodeCombo.fq */, // ㄼ\n    29798 /* AsciiCodeCombo.ft */, // ㄽ\n    30822 /* AsciiCodeCombo.fx */, // ㄾ\n    30310 /* AsciiCodeCombo.fv */, // ㄿ\n    26470 /* AsciiCodeCombo.fg */, // ㅀ\n    97 /* AsciiCode.a */, // ㅁ\n    113 /* AsciiCode.q */, // ㅂ\n    29809 /* AsciiCodeCombo.qt */, // ㅄ\n    116 /* AsciiCode.t */, // ㅅ\n    84 /* AsciiCode.T */, // ㅆ\n    100 /* AsciiCode.d */, // ㅇ\n    119 /* AsciiCode.w */, // ㅈ\n    99 /* AsciiCode.c */, // ㅊ\n    122 /* AsciiCode.z */, // ㅋ\n    120 /* AsciiCode.x */, // ㅌ\n    118 /* AsciiCode.v */, // ㅍ\n    103 /* AsciiCode.g */, // ㅎ\n]);\n/**\n * Hangul Compatibility Jamo\n *\n * Range U+3131..U+318F\n *\n * This includes range includes archaic jamo which we don't consider, these are\n * given the NUL character code in order to be ignored.\n *\n * |        | 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | A | B | C | D | E | F |\n * |--------|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|\n * | U+313x |   | ㄱ | ㄲ | ㄳ | ㄴ | ㄵ | ㄶ | ㄷ | ㄸ | ㄹ | ㄺ | ㄻ | ㄼ | ㄽ | ㄾ | ㄿ |\n * | U+314x | ㅀ | ㅁ | ㅂ | ㅃ | ㅄ | ㅅ | ㅆ | ㅇ | ㅈ | ㅉ | ㅊ | ㅋ | ㅌ | ㅍ | ㅎ | ㅏ |\n * | U+315x | ㅐ | ㅑ | ㅒ | ㅓ | ㅔ | ㅕ | ㅖ | ㅗ | ㅘ | ㅙ | ㅚ | ㅛ | ㅜ | ㅝ | ㅞ | ㅟ |\n * | U+316x | ㅠ | ㅡ | ㅢ | ㅣ | HF | ㅥ | ㅦ | ㅧ | ㅨ | ㅩ | ㅪ | ㅫ | ㅬ | ㅭ | ㅮ | ㅯ |\n * | U+317x | ㅰ | ㅱ | ㅲ | ㅳ | ㅴ | ㅵ | ㅶ | ㅷ | ㅸ | ㅹ | ㅺ | ㅻ | ㅼ | ㅽ | ㅾ | ㅿ |\n * | U+318x | ㆀ | ㆁ | ㆂ | ㆃ | ㆄ | ㆅ | ㆆ | ㆇ | ㆈ | ㆉ | ㆊ | ㆋ | ㆌ | ㆍ | ㆎ |\n */\nconst compatibilityJamo = new Uint16Array([\n    114 /* AsciiCode.r */, // ㄱ\n    82 /* AsciiCode.R */, // ㄲ\n    29810 /* AsciiCodeCombo.rt */, // ㄳ\n    115 /* AsciiCode.s */, // ㄴ\n    30579 /* AsciiCodeCombo.sw */, // ㄵ\n    26483 /* AsciiCodeCombo.sg */, // ㄶ\n    101 /* AsciiCode.e */, // ㄷ\n    69 /* AsciiCode.E */, // ㄸ\n    102 /* AsciiCode.f */, // ㄹ\n    29286 /* AsciiCodeCombo.fr */, // ㄺ\n    24934 /* AsciiCodeCombo.fa */, // ㄻ\n    29030 /* AsciiCodeCombo.fq */, // ㄼ\n    29798 /* AsciiCodeCombo.ft */, // ㄽ\n    30822 /* AsciiCodeCombo.fx */, // ㄾ\n    30310 /* AsciiCodeCombo.fv */, // ㄿ\n    26470 /* AsciiCodeCombo.fg */, // ㅀ\n    97 /* AsciiCode.a */, // ㅁ\n    113 /* AsciiCode.q */, // ㅂ\n    81 /* AsciiCode.Q */, // ㅃ\n    29809 /* AsciiCodeCombo.qt */, // ㅄ\n    116 /* AsciiCode.t */, // ㅅ\n    84 /* AsciiCode.T */, // ㅆ\n    100 /* AsciiCode.d */, // ㅇ\n    119 /* AsciiCode.w */, // ㅈ\n    87 /* AsciiCode.W */, // ㅉ\n    99 /* AsciiCode.c */, // ㅊ\n    122 /* AsciiCode.z */, // ㅋ\n    120 /* AsciiCode.x */, // ㅌ\n    118 /* AsciiCode.v */, // ㅍ\n    103 /* AsciiCode.g */, // ㅎ\n    107 /* AsciiCode.k */, // ㅏ\n    111 /* AsciiCode.o */, // ㅐ\n    105 /* AsciiCode.i */, // ㅑ\n    79 /* AsciiCode.O */, // ㅒ\n    106 /* AsciiCode.j */, // ㅓ\n    112 /* AsciiCode.p */, // ㅔ\n    117 /* AsciiCode.u */, // ㅕ\n    80 /* AsciiCode.P */, // ㅖ\n    104 /* AsciiCode.h */, // ㅗ\n    27496 /* AsciiCodeCombo.hk */, // ㅘ\n    28520 /* AsciiCodeCombo.ho */, // ㅙ\n    27752 /* AsciiCodeCombo.hl */, // ㅚ\n    121 /* AsciiCode.y */, // ㅛ\n    110 /* AsciiCode.n */, // ㅜ\n    27246 /* AsciiCodeCombo.nj */, // ㅝ\n    28782 /* AsciiCodeCombo.np */, // ㅞ\n    27758 /* AsciiCodeCombo.nl */, // ㅟ\n    98 /* AsciiCode.b */, // ㅠ\n    109 /* AsciiCode.m */, // ㅡ\n    27757 /* AsciiCodeCombo.ml */, // ㅢ\n    108 /* AsciiCode.l */, // ㅣ\n    // HF: Hangul Filler (everything after this is archaic)\n    // ㅥ\n    // ㅦ\n    // ㅧ\n    // ㅨ\n    // ㅩ\n    // ㅪ\n    // ㅫ\n    // ㅬ\n    // ㅮ\n    // ㅯ\n    // ㅰ\n    // ㅱ\n    // ㅲ\n    // ㅳ\n    // ㅴ\n    // ㅵ\n    // ㅶ\n    // ㅷ\n    // ㅸ\n    // ㅹ\n    // ㅺ\n    // ㅻ\n    // ㅼ\n    // ㅽ\n    // ㅾ\n    // ㅿ\n    // ㆀ\n    // ㆁ\n    // ㆂ\n    // ㆃ\n    // ㆄ\n    // ㆅ\n    // ㆆ\n    // ㆇ\n    // ㆈ\n    // ㆉ\n    // ㆊ\n    // ㆋ\n    // ㆌ\n    // ㆍ\n    // ㆎ\n]);\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,iBAAiBA,CAACC,IAAI,EAAE;EACpC,MAAMC,MAAM,GAAGC,iBAAiB,CAACF,IAAI,CAAC;EACtC,IAAIC,MAAM,IAAIA,MAAM,CAACE,MAAM,GAAG,CAAC,EAAE;IAC7B,OAAO,IAAIC,WAAW,CAACH,MAAM,CAAC;EAClC;EACA,OAAOI,SAAS;AACpB;AACA,IAAIC,gBAAgB,GAAG,CAAC;AACxB,MAAMC,UAAU,GAAG,IAAIH,WAAW,CAAC,EAAE,CAAC;AACtC,SAASF,iBAAiBA,CAACF,IAAI,EAAE;EAC7BM,gBAAgB,GAAG,CAAC;EACpB;EACAE,iBAAiB,CAACR,IAAI,EAAES,gBAAgB,EAAE,IAAI,CAAC,2CAA2C,CAAC;EAC3F,IAAIH,gBAAgB,GAAG,CAAC,EAAE;IACtB,OAAOC,UAAU,CAACG,QAAQ,CAAC,CAAC,EAAEJ,gBAAgB,CAAC;EACnD;EACA;EACAE,iBAAiB,CAACR,IAAI,EAAEW,YAAY,EAAE,IAAI,CAAC,gCAAgC,CAAC;EAC5E,IAAIL,gBAAgB,GAAG,CAAC,EAAE;IACtB,OAAOC,UAAU,CAACG,QAAQ,CAAC,CAAC,EAAEJ,gBAAgB,CAAC;EACnD;EACA;EACAE,iBAAiB,CAACR,IAAI,EAAEY,qBAAqB,EAAE,IAAI,CAAC,yCAAyC,CAAC;EAC9F,IAAIN,gBAAgB,GAAG,CAAC,EAAE;IACtB,OAAOC,UAAU,CAACG,QAAQ,CAAC,CAAC,EAAEJ,gBAAgB,CAAC;EACnD;EACA;EACAE,iBAAiB,CAACR,IAAI,EAAEa,iBAAiB,EAAE,KAAK,CAAC,4CAA4C,CAAC;EAC9F,IAAIP,gBAAgB,EAAE;IAClB,OAAOC,UAAU,CAACG,QAAQ,CAAC,CAAC,EAAEJ,gBAAgB,CAAC;EACnD;EACA;EACA,IAAIN,IAAI,IAAI,MAAM,IAAIA,IAAI,IAAI,MAAM,EAAE;IAClC,MAAMc,WAAW,GAAGd,IAAI,GAAG,MAAM;IACjC,MAAMe,6BAA6B,GAAGD,WAAW,GAAG,GAAG;IACvD;IACA,MAAME,qBAAqB,GAAGC,IAAI,CAACC,KAAK,CAACJ,WAAW,GAAG,GAAG,CAAC;IAC3D;IACA,MAAMK,UAAU,GAAGF,IAAI,CAACC,KAAK,CAACH,6BAA6B,GAAG,EAAE,CAAC;IACjE;IACA;IACA;IACA,MAAMK,mBAAmB,GAAGL,6BAA6B,GAAG,EAAE,GAAG,CAAC;IAClE,IAAIC,qBAAqB,GAAGP,gBAAgB,CAACN,MAAM,EAAE;MACjDK,iBAAiB,CAACQ,qBAAqB,EAAEP,gBAAgB,EAAE,CAAC,CAAC;IACjE,CAAC,MACI,IAAI,IAAI,CAAC,8CAA8CO,qBAAqB,GAAG,KAAK,CAAC,+CAA+CH,iBAAiB,CAACV,MAAM,EAAE;MAC/JK,iBAAiB,CAAC,IAAI,CAAC,8CAA8CQ,qBAAqB,EAAEH,iBAAiB,EAAE,KAAK,CAAC,4CAA4C,CAAC;IACtK;IACA,IAAIM,UAAU,GAAGR,YAAY,CAACR,MAAM,EAAE;MAClCK,iBAAiB,CAACW,UAAU,EAAER,YAAY,EAAE,CAAC,CAAC;IAClD,CAAC,MACI,IAAI,IAAI,CAAC,mCAAmCQ,UAAU,GAAG,KAAK,CAAC,+CAA+CN,iBAAiB,CAACV,MAAM,EAAE;MACzIK,iBAAiB,CAAC,IAAI,CAAC,mCAAmCW,UAAU,GAAG,KAAK,CAAC,8CAA8CN,iBAAiB,EAAE,KAAK,CAAC,4CAA4C,CAAC;IACrM;IACA,IAAIO,mBAAmB,IAAI,CAAC,EAAE;MAC1B,IAAIA,mBAAmB,GAAGR,qBAAqB,CAACT,MAAM,EAAE;QACpDK,iBAAiB,CAACY,mBAAmB,EAAER,qBAAqB,EAAE,CAAC,CAAC;MACpE,CAAC,MACI,IAAI,IAAI,CAAC,4CAA4CQ,mBAAmB,GAAG,KAAK,CAAC,+CAA+CP,iBAAiB,CAACV,MAAM,EAAE;QAC3JK,iBAAiB,CAAC,IAAI,CAAC,4CAA4CY,mBAAmB,GAAG,KAAK,CAAC,8CAA8CP,iBAAiB,EAAE,KAAK,CAAC,4CAA4C,CAAC;MACvN;IACJ;IACA,IAAIP,gBAAgB,GAAG,CAAC,EAAE;MACtB,OAAOC,UAAU,CAACG,QAAQ,CAAC,CAAC,EAAEJ,gBAAgB,CAAC;IACnD;EACJ;EACA,OAAOD,SAAS;AACpB;AACA,SAASG,iBAAiBA,CAACR,IAAI,EAAEqB,KAAK,EAAEC,eAAe,EAAE;EACrD;EACA,IAAItB,IAAI,IAAIsB,eAAe,IAAItB,IAAI,GAAGsB,eAAe,GAAGD,KAAK,CAAClB,MAAM,EAAE;IAClEoB,gBAAgB,CAACF,KAAK,CAACrB,IAAI,GAAGsB,eAAe,CAAC,CAAC;EACnD;AACJ;AACA,SAASC,gBAAgBA,CAACC,KAAK,EAAE;EAC7B;EACA;EACA,IAAIA,KAAK,KAAK,CAAC,CAAC,qBAAqB;IACjC;EACJ;EACA;EACAjB,UAAU,CAACD,gBAAgB,EAAE,CAAC,GAAGkB,KAAK,GAAG,IAAI;EAC7C,IAAIA,KAAK,IAAI,CAAC,EAAE;IACZjB,UAAU,CAACD,gBAAgB,EAAE,CAAC,GAAIkB,KAAK,IAAI,CAAC,GAAI,IAAI;EACxD;EACA,IAAIA,KAAK,IAAI,EAAE,EAAE;IACbjB,UAAU,CAACD,gBAAgB,EAAE,CAAC,GAAIkB,KAAK,IAAI,EAAE,GAAI,IAAI;EACzD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMf,gBAAgB,GAAG,IAAIgB,UAAU,CAAC,CACpC,GAAG,CAAC;AAAmB;AACvB,EAAE,CAAC;AAAmB;AACtB,GAAG,CAAC;AAAmB;AACvB,GAAG,CAAC;AAAmB;AACvB,EAAE,CAAC;AAAmB;AACtB,GAAG,CAAC;AAAmB;AACvB,EAAE,CAAC;AAAmB;AACtB,GAAG,CAAC;AAAmB;AACvB,EAAE,CAAC;AAAmB;AACtB,GAAG,CAAC;AAAmB;AACvB,EAAE,CAAC;AAAmB;AACtB,GAAG,CAAC;AAAmB;AACvB,GAAG,CAAC;AAAmB;AACvB,EAAE,CAAC;AAAmB;AACtB,EAAE,CAAC;AAAmB;AACtB,GAAG,CAAC;AAAmB;AACvB,GAAG,CAAC;AAAmB;AACvB,GAAG,CAAC;AAAmB;AACvB,GAAG,CAAC,kBAAmB;AAAA,CAC1B,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMd,YAAY,GAAG,IAAIe,WAAW,CAAC,CACjC,GAAG,CAAC;AAAmB;AACvB,GAAG,CAAC;AAAmB;AACvB,GAAG,CAAC;AAAmB;AACvB,EAAE,CAAC;AAAmB;AACtB,GAAG,CAAC;AAAmB;AACvB,GAAG,CAAC;AAAmB;AACvB,GAAG,CAAC;AAAmB;AACvB,EAAE,CAAC;AAAmB;AACtB,GAAG,CAAC;AAAmB;AACvB,KAAK,CAAC;AAAyB;AAC/B,KAAK,CAAC;AAAyB;AAC/B,KAAK,CAAC;AAAyB;AAC/B,GAAG,CAAC;AAAmB;AACvB,GAAG,CAAC;AAAmB;AACvB,KAAK,CAAC;AAAyB;AAC/B,KAAK,CAAC;AAAyB;AAC/B,KAAK,CAAC;AAAyB;AAC/B,EAAE,CAAC;AAAmB;AACtB,GAAG,CAAC;AAAmB;AACvB,KAAK,CAAC;AAAyB;AAC/B,GAAG,CAAC,kBAAmB;AAAA,CAC1B,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMd,qBAAqB,GAAG,IAAIc,WAAW,CAAC,CAC1C,GAAG,CAAC;AAAmB;AACvB,EAAE,CAAC;AAAmB;AACtB,KAAK,CAAC;AAAyB;AAC/B,GAAG,CAAC;AAAmB;AACvB,KAAK,CAAC;AAAyB;AAC/B,KAAK,CAAC;AAAyB;AAC/B,GAAG,CAAC;AAAmB;AACvB,GAAG,CAAC;AAAmB;AACvB,KAAK,CAAC;AAAyB;AAC/B,KAAK,CAAC;AAAyB;AAC/B,KAAK,CAAC;AAAyB;AAC/B,KAAK,CAAC;AAAyB;AAC/B,KAAK,CAAC;AAAyB;AAC/B,KAAK,CAAC;AAAyB;AAC/B,KAAK,CAAC;AAAyB;AAC/B,EAAE,CAAC;AAAmB;AACtB,GAAG,CAAC;AAAmB;AACvB,KAAK,CAAC;AAAyB;AAC/B,GAAG,CAAC;AAAmB;AACvB,EAAE,CAAC;AAAmB;AACtB,GAAG,CAAC;AAAmB;AACvB,GAAG,CAAC;AAAmB;AACvB,EAAE,CAAC;AAAmB;AACtB,GAAG,CAAC;AAAmB;AACvB,GAAG,CAAC;AAAmB;AACvB,GAAG,CAAC;AAAmB;AACvB,GAAG,CAAC,kBAAmB;AAAA,CAC1B,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMb,iBAAiB,GAAG,IAAIa,WAAW,CAAC,CACtC,GAAG,CAAC;AAAmB;AACvB,EAAE,CAAC;AAAmB;AACtB,KAAK,CAAC;AAAyB;AAC/B,GAAG,CAAC;AAAmB;AACvB,KAAK,CAAC;AAAyB;AAC/B,KAAK,CAAC;AAAyB;AAC/B,GAAG,CAAC;AAAmB;AACvB,EAAE,CAAC;AAAmB;AACtB,GAAG,CAAC;AAAmB;AACvB,KAAK,CAAC;AAAyB;AAC/B,KAAK,CAAC;AAAyB;AAC/B,KAAK,CAAC;AAAyB;AAC/B,KAAK,CAAC;AAAyB;AAC/B,KAAK,CAAC;AAAyB;AAC/B,KAAK,CAAC;AAAyB;AAC/B,KAAK,CAAC;AAAyB;AAC/B,EAAE,CAAC;AAAmB;AACtB,GAAG,CAAC;AAAmB;AACvB,EAAE,CAAC;AAAmB;AACtB,KAAK,CAAC;AAAyB;AAC/B,GAAG,CAAC;AAAmB;AACvB,EAAE,CAAC;AAAmB;AACtB,GAAG,CAAC;AAAmB;AACvB,GAAG,CAAC;AAAmB;AACvB,EAAE,CAAC;AAAmB;AACtB,EAAE,CAAC;AAAmB;AACtB,GAAG,CAAC;AAAmB;AACvB,GAAG,CAAC;AAAmB;AACvB,GAAG,CAAC;AAAmB;AACvB,GAAG,CAAC;AAAmB;AACvB,GAAG,CAAC;AAAmB;AACvB,GAAG,CAAC;AAAmB;AACvB,GAAG,CAAC;AAAmB;AACvB,EAAE,CAAC;AAAmB;AACtB,GAAG,CAAC;AAAmB;AACvB,GAAG,CAAC;AAAmB;AACvB,GAAG,CAAC;AAAmB;AACvB,EAAE,CAAC;AAAmB;AACtB,GAAG,CAAC;AAAmB;AACvB,KAAK,CAAC;AAAyB;AAC/B,KAAK,CAAC;AAAyB;AAC/B,KAAK,CAAC;AAAyB;AAC/B,GAAG,CAAC;AAAmB;AACvB,GAAG,CAAC;AAAmB;AACvB,KAAK,CAAC;AAAyB;AAC/B,KAAK,CAAC;AAAyB;AAC/B,KAAK,CAAC;AAAyB;AAC/B,EAAE,CAAC;AAAmB;AACtB,GAAG,CAAC;AAAmB;AACvB,KAAK,CAAC;AAAyB;AAC/B,GAAG,CAAC,kBAAmB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,CACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}