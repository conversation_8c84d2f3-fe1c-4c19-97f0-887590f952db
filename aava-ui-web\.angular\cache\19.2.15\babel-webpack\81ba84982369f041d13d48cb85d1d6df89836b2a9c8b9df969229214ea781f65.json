{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { BaseObservable, TransactionImpl } from './base.js';\n/**\n * Holds off updating observers until the value is actually read.\n*/\nexport class LazyObservableValue extends BaseObservable {\n  get debugName() {\n    return this._debugNameData.getDebugName(this) ?? 'LazyObservableValue';\n  }\n  constructor(_debugNameData, initialValue, _equalityComparator) {\n    super();\n    this._debugNameData = _debugNameData;\n    this._equalityComparator = _equalityComparator;\n    this._isUpToDate = true;\n    this._deltas = [];\n    this._updateCounter = 0;\n    this._value = initialValue;\n  }\n  get() {\n    this._update();\n    return this._value;\n  }\n  _update() {\n    if (this._isUpToDate) {\n      return;\n    }\n    this._isUpToDate = true;\n    if (this._deltas.length > 0) {\n      for (const observer of this.observers) {\n        for (const change of this._deltas) {\n          observer.handleChange(this, change);\n        }\n      }\n      this._deltas.length = 0;\n    } else {\n      for (const observer of this.observers) {\n        observer.handleChange(this, undefined);\n      }\n    }\n  }\n  _beginUpdate() {\n    this._updateCounter++;\n    if (this._updateCounter === 1) {\n      for (const observer of this.observers) {\n        observer.beginUpdate(this);\n      }\n    }\n  }\n  _endUpdate() {\n    this._updateCounter--;\n    if (this._updateCounter === 0) {\n      this._update();\n      // End update could change the observer list.\n      const observers = [...this.observers];\n      for (const r of observers) {\n        r.endUpdate(this);\n      }\n    }\n  }\n  addObserver(observer) {\n    const shouldCallBeginUpdate = !this.observers.has(observer) && this._updateCounter > 0;\n    super.addObserver(observer);\n    if (shouldCallBeginUpdate) {\n      observer.beginUpdate(this);\n    }\n  }\n  removeObserver(observer) {\n    const shouldCallEndUpdate = this.observers.has(observer) && this._updateCounter > 0;\n    super.removeObserver(observer);\n    if (shouldCallEndUpdate) {\n      // Calling end update after removing the observer makes sure endUpdate cannot be called twice here.\n      observer.endUpdate(this);\n    }\n  }\n  set(value, tx, change) {\n    if (change === undefined && this._equalityComparator(this._value, value)) {\n      return;\n    }\n    let _tx;\n    if (!tx) {\n      tx = _tx = new TransactionImpl(() => {}, () => `Setting ${this.debugName}`);\n    }\n    try {\n      this._isUpToDate = false;\n      this._setValue(value);\n      if (change !== undefined) {\n        this._deltas.push(change);\n      }\n      tx.updateObserver({\n        beginUpdate: () => this._beginUpdate(),\n        endUpdate: () => this._endUpdate(),\n        handleChange: (observable, change) => {},\n        handlePossibleChange: observable => {}\n      }, this);\n      if (this._updateCounter > 1) {\n        // We already started begin/end update, so we need to manually call handlePossibleChange\n        for (const observer of this.observers) {\n          observer.handlePossibleChange(this);\n        }\n      }\n    } finally {\n      if (_tx) {\n        _tx.finish();\n      }\n    }\n  }\n  toString() {\n    return `${this.debugName}: ${this._value}`;\n  }\n  _setValue(newValue) {\n    this._value = newValue;\n  }\n}", "map": {"version": 3, "names": ["BaseObservable", "TransactionImpl", "LazyObservableValue", "debugName", "_debugNameData", "getDebugName", "constructor", "initialValue", "_equalityComparator", "_isUpToDate", "_deltas", "_updateCounter", "_value", "get", "_update", "length", "observer", "observers", "change", "handleChange", "undefined", "_beginUpdate", "beginUpdate", "_endUpdate", "r", "endUpdate", "addObserver", "shouldCallBeginUpdate", "has", "removeObserver", "shouldCallEndUpdate", "set", "value", "tx", "_tx", "_setValue", "push", "updateObserver", "observable", "handlePossibleChange", "finish", "toString", "newValue"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/common/observableInternal/lazyObservableValue.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { BaseObservable, TransactionImpl } from './base.js';\n/**\n * Holds off updating observers until the value is actually read.\n*/\nexport class LazyObservableValue extends BaseObservable {\n    get debugName() {\n        return this._debugNameData.getDebugName(this) ?? 'LazyObservableValue';\n    }\n    constructor(_debugNameData, initialValue, _equalityComparator) {\n        super();\n        this._debugNameData = _debugNameData;\n        this._equalityComparator = _equalityComparator;\n        this._isUpToDate = true;\n        this._deltas = [];\n        this._updateCounter = 0;\n        this._value = initialValue;\n    }\n    get() {\n        this._update();\n        return this._value;\n    }\n    _update() {\n        if (this._isUpToDate) {\n            return;\n        }\n        this._isUpToDate = true;\n        if (this._deltas.length > 0) {\n            for (const observer of this.observers) {\n                for (const change of this._deltas) {\n                    observer.handleChange(this, change);\n                }\n            }\n            this._deltas.length = 0;\n        }\n        else {\n            for (const observer of this.observers) {\n                observer.handleChange(this, undefined);\n            }\n        }\n    }\n    _beginUpdate() {\n        this._updateCounter++;\n        if (this._updateCounter === 1) {\n            for (const observer of this.observers) {\n                observer.beginUpdate(this);\n            }\n        }\n    }\n    _endUpdate() {\n        this._updateCounter--;\n        if (this._updateCounter === 0) {\n            this._update();\n            // End update could change the observer list.\n            const observers = [...this.observers];\n            for (const r of observers) {\n                r.endUpdate(this);\n            }\n        }\n    }\n    addObserver(observer) {\n        const shouldCallBeginUpdate = !this.observers.has(observer) && this._updateCounter > 0;\n        super.addObserver(observer);\n        if (shouldCallBeginUpdate) {\n            observer.beginUpdate(this);\n        }\n    }\n    removeObserver(observer) {\n        const shouldCallEndUpdate = this.observers.has(observer) && this._updateCounter > 0;\n        super.removeObserver(observer);\n        if (shouldCallEndUpdate) {\n            // Calling end update after removing the observer makes sure endUpdate cannot be called twice here.\n            observer.endUpdate(this);\n        }\n    }\n    set(value, tx, change) {\n        if (change === undefined && this._equalityComparator(this._value, value)) {\n            return;\n        }\n        let _tx;\n        if (!tx) {\n            tx = _tx = new TransactionImpl(() => { }, () => `Setting ${this.debugName}`);\n        }\n        try {\n            this._isUpToDate = false;\n            this._setValue(value);\n            if (change !== undefined) {\n                this._deltas.push(change);\n            }\n            tx.updateObserver({\n                beginUpdate: () => this._beginUpdate(),\n                endUpdate: () => this._endUpdate(),\n                handleChange: (observable, change) => { },\n                handlePossibleChange: (observable) => { },\n            }, this);\n            if (this._updateCounter > 1) {\n                // We already started begin/end update, so we need to manually call handlePossibleChange\n                for (const observer of this.observers) {\n                    observer.handlePossibleChange(this);\n                }\n            }\n        }\n        finally {\n            if (_tx) {\n                _tx.finish();\n            }\n        }\n    }\n    toString() {\n        return `${this.debugName}: ${this._value}`;\n    }\n    _setValue(newValue) {\n        this._value = newValue;\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,cAAc,EAAEC,eAAe,QAAQ,WAAW;AAC3D;AACA;AACA;AACA,OAAO,MAAMC,mBAAmB,SAASF,cAAc,CAAC;EACpD,IAAIG,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACC,cAAc,CAACC,YAAY,CAAC,IAAI,CAAC,IAAI,qBAAqB;EAC1E;EACAC,WAAWA,CAACF,cAAc,EAAEG,YAAY,EAAEC,mBAAmB,EAAE;IAC3D,KAAK,CAAC,CAAC;IACP,IAAI,CAACJ,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACI,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACC,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACC,cAAc,GAAG,CAAC;IACvB,IAAI,CAACC,MAAM,GAAGL,YAAY;EAC9B;EACAM,GAAGA,CAAA,EAAG;IACF,IAAI,CAACC,OAAO,CAAC,CAAC;IACd,OAAO,IAAI,CAACF,MAAM;EACtB;EACAE,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACL,WAAW,EAAE;MAClB;IACJ;IACA,IAAI,CAACA,WAAW,GAAG,IAAI;IACvB,IAAI,IAAI,CAACC,OAAO,CAACK,MAAM,GAAG,CAAC,EAAE;MACzB,KAAK,MAAMC,QAAQ,IAAI,IAAI,CAACC,SAAS,EAAE;QACnC,KAAK,MAAMC,MAAM,IAAI,IAAI,CAACR,OAAO,EAAE;UAC/BM,QAAQ,CAACG,YAAY,CAAC,IAAI,EAAED,MAAM,CAAC;QACvC;MACJ;MACA,IAAI,CAACR,OAAO,CAACK,MAAM,GAAG,CAAC;IAC3B,CAAC,MACI;MACD,KAAK,MAAMC,QAAQ,IAAI,IAAI,CAACC,SAAS,EAAE;QACnCD,QAAQ,CAACG,YAAY,CAAC,IAAI,EAAEC,SAAS,CAAC;MAC1C;IACJ;EACJ;EACAC,YAAYA,CAAA,EAAG;IACX,IAAI,CAACV,cAAc,EAAE;IACrB,IAAI,IAAI,CAACA,cAAc,KAAK,CAAC,EAAE;MAC3B,KAAK,MAAMK,QAAQ,IAAI,IAAI,CAACC,SAAS,EAAE;QACnCD,QAAQ,CAACM,WAAW,CAAC,IAAI,CAAC;MAC9B;IACJ;EACJ;EACAC,UAAUA,CAAA,EAAG;IACT,IAAI,CAACZ,cAAc,EAAE;IACrB,IAAI,IAAI,CAACA,cAAc,KAAK,CAAC,EAAE;MAC3B,IAAI,CAACG,OAAO,CAAC,CAAC;MACd;MACA,MAAMG,SAAS,GAAG,CAAC,GAAG,IAAI,CAACA,SAAS,CAAC;MACrC,KAAK,MAAMO,CAAC,IAAIP,SAAS,EAAE;QACvBO,CAAC,CAACC,SAAS,CAAC,IAAI,CAAC;MACrB;IACJ;EACJ;EACAC,WAAWA,CAACV,QAAQ,EAAE;IAClB,MAAMW,qBAAqB,GAAG,CAAC,IAAI,CAACV,SAAS,CAACW,GAAG,CAACZ,QAAQ,CAAC,IAAI,IAAI,CAACL,cAAc,GAAG,CAAC;IACtF,KAAK,CAACe,WAAW,CAACV,QAAQ,CAAC;IAC3B,IAAIW,qBAAqB,EAAE;MACvBX,QAAQ,CAACM,WAAW,CAAC,IAAI,CAAC;IAC9B;EACJ;EACAO,cAAcA,CAACb,QAAQ,EAAE;IACrB,MAAMc,mBAAmB,GAAG,IAAI,CAACb,SAAS,CAACW,GAAG,CAACZ,QAAQ,CAAC,IAAI,IAAI,CAACL,cAAc,GAAG,CAAC;IACnF,KAAK,CAACkB,cAAc,CAACb,QAAQ,CAAC;IAC9B,IAAIc,mBAAmB,EAAE;MACrB;MACAd,QAAQ,CAACS,SAAS,CAAC,IAAI,CAAC;IAC5B;EACJ;EACAM,GAAGA,CAACC,KAAK,EAAEC,EAAE,EAAEf,MAAM,EAAE;IACnB,IAAIA,MAAM,KAAKE,SAAS,IAAI,IAAI,CAACZ,mBAAmB,CAAC,IAAI,CAACI,MAAM,EAAEoB,KAAK,CAAC,EAAE;MACtE;IACJ;IACA,IAAIE,GAAG;IACP,IAAI,CAACD,EAAE,EAAE;MACLA,EAAE,GAAGC,GAAG,GAAG,IAAIjC,eAAe,CAAC,MAAM,CAAE,CAAC,EAAE,MAAM,WAAW,IAAI,CAACE,SAAS,EAAE,CAAC;IAChF;IACA,IAAI;MACA,IAAI,CAACM,WAAW,GAAG,KAAK;MACxB,IAAI,CAAC0B,SAAS,CAACH,KAAK,CAAC;MACrB,IAAId,MAAM,KAAKE,SAAS,EAAE;QACtB,IAAI,CAACV,OAAO,CAAC0B,IAAI,CAAClB,MAAM,CAAC;MAC7B;MACAe,EAAE,CAACI,cAAc,CAAC;QACdf,WAAW,EAAEA,CAAA,KAAM,IAAI,CAACD,YAAY,CAAC,CAAC;QACtCI,SAAS,EAAEA,CAAA,KAAM,IAAI,CAACF,UAAU,CAAC,CAAC;QAClCJ,YAAY,EAAEA,CAACmB,UAAU,EAAEpB,MAAM,KAAK,CAAE,CAAC;QACzCqB,oBAAoB,EAAGD,UAAU,IAAK,CAAE;MAC5C,CAAC,EAAE,IAAI,CAAC;MACR,IAAI,IAAI,CAAC3B,cAAc,GAAG,CAAC,EAAE;QACzB;QACA,KAAK,MAAMK,QAAQ,IAAI,IAAI,CAACC,SAAS,EAAE;UACnCD,QAAQ,CAACuB,oBAAoB,CAAC,IAAI,CAAC;QACvC;MACJ;IACJ,CAAC,SACO;MACJ,IAAIL,GAAG,EAAE;QACLA,GAAG,CAACM,MAAM,CAAC,CAAC;MAChB;IACJ;EACJ;EACAC,QAAQA,CAAA,EAAG;IACP,OAAO,GAAG,IAAI,CAACtC,SAAS,KAAK,IAAI,CAACS,MAAM,EAAE;EAC9C;EACAuB,SAASA,CAACO,QAAQ,EAAE;IAChB,IAAI,CAAC9B,MAAM,GAAG8B,QAAQ;EAC1B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}