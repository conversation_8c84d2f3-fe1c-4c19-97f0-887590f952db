{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Emitter } from '../../base/common/event.js';\nimport { toDisposable } from '../../base/common/lifecycle.js';\nimport { shouldSynchronizeModel } from './model.js';\nimport { score } from './languageSelector.js';\nfunction isExclusive(selector) {\n  if (typeof selector === 'string') {\n    return false;\n  } else if (Array.isArray(selector)) {\n    return selector.every(isExclusive);\n  } else {\n    return !!selector.exclusive; // TODO: microsoft/TypeScript#42768\n  }\n}\nclass MatchCandidate {\n  constructor(uri, languageId, notebookUri, notebookType, recursive) {\n    this.uri = uri;\n    this.languageId = languageId;\n    this.notebookUri = notebookUri;\n    this.notebookType = notebookType;\n    this.recursive = recursive;\n  }\n  equals(other) {\n    return this.notebookType === other.notebookType && this.languageId === other.languageId && this.uri.toString() === other.uri.toString() && this.notebookUri?.toString() === other.notebookUri?.toString() && this.recursive === other.recursive;\n  }\n}\nexport class LanguageFeatureRegistry {\n  constructor(_notebookInfoResolver) {\n    this._notebookInfoResolver = _notebookInfoResolver;\n    this._clock = 0;\n    this._entries = [];\n    this._onDidChange = new Emitter();\n    this.onDidChange = this._onDidChange.event;\n  }\n  register(selector, provider) {\n    let entry = {\n      selector,\n      provider,\n      _score: -1,\n      _time: this._clock++\n    };\n    this._entries.push(entry);\n    this._lastCandidate = undefined;\n    this._onDidChange.fire(this._entries.length);\n    return toDisposable(() => {\n      if (entry) {\n        const idx = this._entries.indexOf(entry);\n        if (idx >= 0) {\n          this._entries.splice(idx, 1);\n          this._lastCandidate = undefined;\n          this._onDidChange.fire(this._entries.length);\n          entry = undefined;\n        }\n      }\n    });\n  }\n  has(model) {\n    return this.all(model).length > 0;\n  }\n  all(model) {\n    if (!model) {\n      return [];\n    }\n    this._updateScores(model, false);\n    const result = [];\n    // from registry\n    for (const entry of this._entries) {\n      if (entry._score > 0) {\n        result.push(entry.provider);\n      }\n    }\n    return result;\n  }\n  ordered(model, recursive = false) {\n    const result = [];\n    this._orderedForEach(model, recursive, entry => result.push(entry.provider));\n    return result;\n  }\n  orderedGroups(model) {\n    const result = [];\n    let lastBucket;\n    let lastBucketScore;\n    this._orderedForEach(model, false, entry => {\n      if (lastBucket && lastBucketScore === entry._score) {\n        lastBucket.push(entry.provider);\n      } else {\n        lastBucketScore = entry._score;\n        lastBucket = [entry.provider];\n        result.push(lastBucket);\n      }\n    });\n    return result;\n  }\n  _orderedForEach(model, recursive, callback) {\n    this._updateScores(model, recursive);\n    for (const entry of this._entries) {\n      if (entry._score > 0) {\n        callback(entry);\n      }\n    }\n  }\n  _updateScores(model, recursive) {\n    const notebookInfo = this._notebookInfoResolver?.(model.uri);\n    // use the uri (scheme, pattern) of the notebook info iff we have one\n    // otherwise it's the model's/document's uri\n    const candidate = notebookInfo ? new MatchCandidate(model.uri, model.getLanguageId(), notebookInfo.uri, notebookInfo.type, recursive) : new MatchCandidate(model.uri, model.getLanguageId(), undefined, undefined, recursive);\n    if (this._lastCandidate?.equals(candidate)) {\n      // nothing has changed\n      return;\n    }\n    this._lastCandidate = candidate;\n    for (const entry of this._entries) {\n      entry._score = score(entry.selector, candidate.uri, candidate.languageId, shouldSynchronizeModel(model), candidate.notebookUri, candidate.notebookType);\n      if (isExclusive(entry.selector) && entry._score > 0) {\n        if (recursive) {\n          entry._score = 0;\n        } else {\n          // support for one exclusive selector that overwrites\n          // any other selector\n          for (const entry of this._entries) {\n            entry._score = 0;\n          }\n          entry._score = 1000;\n          break;\n        }\n      }\n    }\n    // needs sorting\n    this._entries.sort(LanguageFeatureRegistry._compareByScoreAndTime);\n  }\n  static _compareByScoreAndTime(a, b) {\n    if (a._score < b._score) {\n      return 1;\n    } else if (a._score > b._score) {\n      return -1;\n    }\n    // De-prioritize built-in providers\n    if (isBuiltinSelector(a.selector) && !isBuiltinSelector(b.selector)) {\n      return 1;\n    } else if (!isBuiltinSelector(a.selector) && isBuiltinSelector(b.selector)) {\n      return -1;\n    }\n    if (a._time < b._time) {\n      return 1;\n    } else if (a._time > b._time) {\n      return -1;\n    } else {\n      return 0;\n    }\n  }\n}\nfunction isBuiltinSelector(selector) {\n  if (typeof selector === 'string') {\n    return false;\n  }\n  if (Array.isArray(selector)) {\n    return selector.some(isBuiltinSelector);\n  }\n  return Boolean(selector.isBuiltin);\n}", "map": {"version": 3, "names": ["Emitter", "toDisposable", "shouldSynchronizeModel", "score", "isExclusive", "selector", "Array", "isArray", "every", "exclusive", "MatchCandidate", "constructor", "uri", "languageId", "notebookUri", "notebookType", "recursive", "equals", "other", "toString", "LanguageFeatureRegistry", "_notebookInfoResolver", "_clock", "_entries", "_onDidChange", "onDidChange", "event", "register", "provider", "entry", "_score", "_time", "push", "_lastCandidate", "undefined", "fire", "length", "idx", "indexOf", "splice", "has", "model", "all", "_updateScores", "result", "ordered", "_orderedFor<PERSON>ach", "orderedGroups", "lastBucket", "lastBucketScore", "callback", "notebookInfo", "candidate", "getLanguageId", "type", "sort", "_compareByScoreAndTime", "a", "b", "isBuiltinSelector", "some", "Boolean", "isBuiltin"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/languageFeatureRegistry.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Emitter } from '../../base/common/event.js';\nimport { toDisposable } from '../../base/common/lifecycle.js';\nimport { shouldSynchronizeModel } from './model.js';\nimport { score } from './languageSelector.js';\nfunction isExclusive(selector) {\n    if (typeof selector === 'string') {\n        return false;\n    }\n    else if (Array.isArray(selector)) {\n        return selector.every(isExclusive);\n    }\n    else {\n        return !!selector.exclusive; // TODO: microsoft/TypeScript#42768\n    }\n}\nclass MatchCandidate {\n    constructor(uri, languageId, notebookUri, notebookType, recursive) {\n        this.uri = uri;\n        this.languageId = languageId;\n        this.notebookUri = notebookUri;\n        this.notebookType = notebookType;\n        this.recursive = recursive;\n    }\n    equals(other) {\n        return this.notebookType === other.notebookType\n            && this.languageId === other.languageId\n            && this.uri.toString() === other.uri.toString()\n            && this.notebookUri?.toString() === other.notebookUri?.toString()\n            && this.recursive === other.recursive;\n    }\n}\nexport class LanguageFeatureRegistry {\n    constructor(_notebookInfoResolver) {\n        this._notebookInfoResolver = _notebookInfoResolver;\n        this._clock = 0;\n        this._entries = [];\n        this._onDidChange = new Emitter();\n        this.onDidChange = this._onDidChange.event;\n    }\n    register(selector, provider) {\n        let entry = {\n            selector,\n            provider,\n            _score: -1,\n            _time: this._clock++\n        };\n        this._entries.push(entry);\n        this._lastCandidate = undefined;\n        this._onDidChange.fire(this._entries.length);\n        return toDisposable(() => {\n            if (entry) {\n                const idx = this._entries.indexOf(entry);\n                if (idx >= 0) {\n                    this._entries.splice(idx, 1);\n                    this._lastCandidate = undefined;\n                    this._onDidChange.fire(this._entries.length);\n                    entry = undefined;\n                }\n            }\n        });\n    }\n    has(model) {\n        return this.all(model).length > 0;\n    }\n    all(model) {\n        if (!model) {\n            return [];\n        }\n        this._updateScores(model, false);\n        const result = [];\n        // from registry\n        for (const entry of this._entries) {\n            if (entry._score > 0) {\n                result.push(entry.provider);\n            }\n        }\n        return result;\n    }\n    ordered(model, recursive = false) {\n        const result = [];\n        this._orderedForEach(model, recursive, entry => result.push(entry.provider));\n        return result;\n    }\n    orderedGroups(model) {\n        const result = [];\n        let lastBucket;\n        let lastBucketScore;\n        this._orderedForEach(model, false, entry => {\n            if (lastBucket && lastBucketScore === entry._score) {\n                lastBucket.push(entry.provider);\n            }\n            else {\n                lastBucketScore = entry._score;\n                lastBucket = [entry.provider];\n                result.push(lastBucket);\n            }\n        });\n        return result;\n    }\n    _orderedForEach(model, recursive, callback) {\n        this._updateScores(model, recursive);\n        for (const entry of this._entries) {\n            if (entry._score > 0) {\n                callback(entry);\n            }\n        }\n    }\n    _updateScores(model, recursive) {\n        const notebookInfo = this._notebookInfoResolver?.(model.uri);\n        // use the uri (scheme, pattern) of the notebook info iff we have one\n        // otherwise it's the model's/document's uri\n        const candidate = notebookInfo\n            ? new MatchCandidate(model.uri, model.getLanguageId(), notebookInfo.uri, notebookInfo.type, recursive)\n            : new MatchCandidate(model.uri, model.getLanguageId(), undefined, undefined, recursive);\n        if (this._lastCandidate?.equals(candidate)) {\n            // nothing has changed\n            return;\n        }\n        this._lastCandidate = candidate;\n        for (const entry of this._entries) {\n            entry._score = score(entry.selector, candidate.uri, candidate.languageId, shouldSynchronizeModel(model), candidate.notebookUri, candidate.notebookType);\n            if (isExclusive(entry.selector) && entry._score > 0) {\n                if (recursive) {\n                    entry._score = 0;\n                }\n                else {\n                    // support for one exclusive selector that overwrites\n                    // any other selector\n                    for (const entry of this._entries) {\n                        entry._score = 0;\n                    }\n                    entry._score = 1000;\n                    break;\n                }\n            }\n        }\n        // needs sorting\n        this._entries.sort(LanguageFeatureRegistry._compareByScoreAndTime);\n    }\n    static _compareByScoreAndTime(a, b) {\n        if (a._score < b._score) {\n            return 1;\n        }\n        else if (a._score > b._score) {\n            return -1;\n        }\n        // De-prioritize built-in providers\n        if (isBuiltinSelector(a.selector) && !isBuiltinSelector(b.selector)) {\n            return 1;\n        }\n        else if (!isBuiltinSelector(a.selector) && isBuiltinSelector(b.selector)) {\n            return -1;\n        }\n        if (a._time < b._time) {\n            return 1;\n        }\n        else if (a._time > b._time) {\n            return -1;\n        }\n        else {\n            return 0;\n        }\n    }\n}\nfunction isBuiltinSelector(selector) {\n    if (typeof selector === 'string') {\n        return false;\n    }\n    if (Array.isArray(selector)) {\n        return selector.some(isBuiltinSelector);\n    }\n    return Boolean(selector.isBuiltin);\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,OAAO,QAAQ,4BAA4B;AACpD,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,sBAAsB,QAAQ,YAAY;AACnD,SAASC,KAAK,QAAQ,uBAAuB;AAC7C,SAASC,WAAWA,CAACC,QAAQ,EAAE;EAC3B,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;IAC9B,OAAO,KAAK;EAChB,CAAC,MACI,IAAIC,KAAK,CAACC,OAAO,CAACF,QAAQ,CAAC,EAAE;IAC9B,OAAOA,QAAQ,CAACG,KAAK,CAACJ,WAAW,CAAC;EACtC,CAAC,MACI;IACD,OAAO,CAAC,CAACC,QAAQ,CAACI,SAAS,CAAC,CAAC;EACjC;AACJ;AACA,MAAMC,cAAc,CAAC;EACjBC,WAAWA,CAACC,GAAG,EAAEC,UAAU,EAAEC,WAAW,EAAEC,YAAY,EAAEC,SAAS,EAAE;IAC/D,IAAI,CAACJ,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,SAAS,GAAGA,SAAS;EAC9B;EACAC,MAAMA,CAACC,KAAK,EAAE;IACV,OAAO,IAAI,CAACH,YAAY,KAAKG,KAAK,CAACH,YAAY,IACxC,IAAI,CAACF,UAAU,KAAKK,KAAK,CAACL,UAAU,IACpC,IAAI,CAACD,GAAG,CAACO,QAAQ,CAAC,CAAC,KAAKD,KAAK,CAACN,GAAG,CAACO,QAAQ,CAAC,CAAC,IAC5C,IAAI,CAACL,WAAW,EAAEK,QAAQ,CAAC,CAAC,KAAKD,KAAK,CAACJ,WAAW,EAAEK,QAAQ,CAAC,CAAC,IAC9D,IAAI,CAACH,SAAS,KAAKE,KAAK,CAACF,SAAS;EAC7C;AACJ;AACA,OAAO,MAAMI,uBAAuB,CAAC;EACjCT,WAAWA,CAACU,qBAAqB,EAAE;IAC/B,IAAI,CAACA,qBAAqB,GAAGA,qBAAqB;IAClD,IAAI,CAACC,MAAM,GAAG,CAAC;IACf,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,YAAY,GAAG,IAAIxB,OAAO,CAAC,CAAC;IACjC,IAAI,CAACyB,WAAW,GAAG,IAAI,CAACD,YAAY,CAACE,KAAK;EAC9C;EACAC,QAAQA,CAACtB,QAAQ,EAAEuB,QAAQ,EAAE;IACzB,IAAIC,KAAK,GAAG;MACRxB,QAAQ;MACRuB,QAAQ;MACRE,MAAM,EAAE,CAAC,CAAC;MACVC,KAAK,EAAE,IAAI,CAACT,MAAM;IACtB,CAAC;IACD,IAAI,CAACC,QAAQ,CAACS,IAAI,CAACH,KAAK,CAAC;IACzB,IAAI,CAACI,cAAc,GAAGC,SAAS;IAC/B,IAAI,CAACV,YAAY,CAACW,IAAI,CAAC,IAAI,CAACZ,QAAQ,CAACa,MAAM,CAAC;IAC5C,OAAOnC,YAAY,CAAC,MAAM;MACtB,IAAI4B,KAAK,EAAE;QACP,MAAMQ,GAAG,GAAG,IAAI,CAACd,QAAQ,CAACe,OAAO,CAACT,KAAK,CAAC;QACxC,IAAIQ,GAAG,IAAI,CAAC,EAAE;UACV,IAAI,CAACd,QAAQ,CAACgB,MAAM,CAACF,GAAG,EAAE,CAAC,CAAC;UAC5B,IAAI,CAACJ,cAAc,GAAGC,SAAS;UAC/B,IAAI,CAACV,YAAY,CAACW,IAAI,CAAC,IAAI,CAACZ,QAAQ,CAACa,MAAM,CAAC;UAC5CP,KAAK,GAAGK,SAAS;QACrB;MACJ;IACJ,CAAC,CAAC;EACN;EACAM,GAAGA,CAACC,KAAK,EAAE;IACP,OAAO,IAAI,CAACC,GAAG,CAACD,KAAK,CAAC,CAACL,MAAM,GAAG,CAAC;EACrC;EACAM,GAAGA,CAACD,KAAK,EAAE;IACP,IAAI,CAACA,KAAK,EAAE;MACR,OAAO,EAAE;IACb;IACA,IAAI,CAACE,aAAa,CAACF,KAAK,EAAE,KAAK,CAAC;IAChC,MAAMG,MAAM,GAAG,EAAE;IACjB;IACA,KAAK,MAAMf,KAAK,IAAI,IAAI,CAACN,QAAQ,EAAE;MAC/B,IAAIM,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;QAClBc,MAAM,CAACZ,IAAI,CAACH,KAAK,CAACD,QAAQ,CAAC;MAC/B;IACJ;IACA,OAAOgB,MAAM;EACjB;EACAC,OAAOA,CAACJ,KAAK,EAAEzB,SAAS,GAAG,KAAK,EAAE;IAC9B,MAAM4B,MAAM,GAAG,EAAE;IACjB,IAAI,CAACE,eAAe,CAACL,KAAK,EAAEzB,SAAS,EAAEa,KAAK,IAAIe,MAAM,CAACZ,IAAI,CAACH,KAAK,CAACD,QAAQ,CAAC,CAAC;IAC5E,OAAOgB,MAAM;EACjB;EACAG,aAAaA,CAACN,KAAK,EAAE;IACjB,MAAMG,MAAM,GAAG,EAAE;IACjB,IAAII,UAAU;IACd,IAAIC,eAAe;IACnB,IAAI,CAACH,eAAe,CAACL,KAAK,EAAE,KAAK,EAAEZ,KAAK,IAAI;MACxC,IAAImB,UAAU,IAAIC,eAAe,KAAKpB,KAAK,CAACC,MAAM,EAAE;QAChDkB,UAAU,CAAChB,IAAI,CAACH,KAAK,CAACD,QAAQ,CAAC;MACnC,CAAC,MACI;QACDqB,eAAe,GAAGpB,KAAK,CAACC,MAAM;QAC9BkB,UAAU,GAAG,CAACnB,KAAK,CAACD,QAAQ,CAAC;QAC7BgB,MAAM,CAACZ,IAAI,CAACgB,UAAU,CAAC;MAC3B;IACJ,CAAC,CAAC;IACF,OAAOJ,MAAM;EACjB;EACAE,eAAeA,CAACL,KAAK,EAAEzB,SAAS,EAAEkC,QAAQ,EAAE;IACxC,IAAI,CAACP,aAAa,CAACF,KAAK,EAAEzB,SAAS,CAAC;IACpC,KAAK,MAAMa,KAAK,IAAI,IAAI,CAACN,QAAQ,EAAE;MAC/B,IAAIM,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;QAClBoB,QAAQ,CAACrB,KAAK,CAAC;MACnB;IACJ;EACJ;EACAc,aAAaA,CAACF,KAAK,EAAEzB,SAAS,EAAE;IAC5B,MAAMmC,YAAY,GAAG,IAAI,CAAC9B,qBAAqB,GAAGoB,KAAK,CAAC7B,GAAG,CAAC;IAC5D;IACA;IACA,MAAMwC,SAAS,GAAGD,YAAY,GACxB,IAAIzC,cAAc,CAAC+B,KAAK,CAAC7B,GAAG,EAAE6B,KAAK,CAACY,aAAa,CAAC,CAAC,EAAEF,YAAY,CAACvC,GAAG,EAAEuC,YAAY,CAACG,IAAI,EAAEtC,SAAS,CAAC,GACpG,IAAIN,cAAc,CAAC+B,KAAK,CAAC7B,GAAG,EAAE6B,KAAK,CAACY,aAAa,CAAC,CAAC,EAAEnB,SAAS,EAAEA,SAAS,EAAElB,SAAS,CAAC;IAC3F,IAAI,IAAI,CAACiB,cAAc,EAAEhB,MAAM,CAACmC,SAAS,CAAC,EAAE;MACxC;MACA;IACJ;IACA,IAAI,CAACnB,cAAc,GAAGmB,SAAS;IAC/B,KAAK,MAAMvB,KAAK,IAAI,IAAI,CAACN,QAAQ,EAAE;MAC/BM,KAAK,CAACC,MAAM,GAAG3B,KAAK,CAAC0B,KAAK,CAACxB,QAAQ,EAAE+C,SAAS,CAACxC,GAAG,EAAEwC,SAAS,CAACvC,UAAU,EAAEX,sBAAsB,CAACuC,KAAK,CAAC,EAAEW,SAAS,CAACtC,WAAW,EAAEsC,SAAS,CAACrC,YAAY,CAAC;MACvJ,IAAIX,WAAW,CAACyB,KAAK,CAACxB,QAAQ,CAAC,IAAIwB,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;QACjD,IAAId,SAAS,EAAE;UACXa,KAAK,CAACC,MAAM,GAAG,CAAC;QACpB,CAAC,MACI;UACD;UACA;UACA,KAAK,MAAMD,KAAK,IAAI,IAAI,CAACN,QAAQ,EAAE;YAC/BM,KAAK,CAACC,MAAM,GAAG,CAAC;UACpB;UACAD,KAAK,CAACC,MAAM,GAAG,IAAI;UACnB;QACJ;MACJ;IACJ;IACA;IACA,IAAI,CAACP,QAAQ,CAACgC,IAAI,CAACnC,uBAAuB,CAACoC,sBAAsB,CAAC;EACtE;EACA,OAAOA,sBAAsBA,CAACC,CAAC,EAAEC,CAAC,EAAE;IAChC,IAAID,CAAC,CAAC3B,MAAM,GAAG4B,CAAC,CAAC5B,MAAM,EAAE;MACrB,OAAO,CAAC;IACZ,CAAC,MACI,IAAI2B,CAAC,CAAC3B,MAAM,GAAG4B,CAAC,CAAC5B,MAAM,EAAE;MAC1B,OAAO,CAAC,CAAC;IACb;IACA;IACA,IAAI6B,iBAAiB,CAACF,CAAC,CAACpD,QAAQ,CAAC,IAAI,CAACsD,iBAAiB,CAACD,CAAC,CAACrD,QAAQ,CAAC,EAAE;MACjE,OAAO,CAAC;IACZ,CAAC,MACI,IAAI,CAACsD,iBAAiB,CAACF,CAAC,CAACpD,QAAQ,CAAC,IAAIsD,iBAAiB,CAACD,CAAC,CAACrD,QAAQ,CAAC,EAAE;MACtE,OAAO,CAAC,CAAC;IACb;IACA,IAAIoD,CAAC,CAAC1B,KAAK,GAAG2B,CAAC,CAAC3B,KAAK,EAAE;MACnB,OAAO,CAAC;IACZ,CAAC,MACI,IAAI0B,CAAC,CAAC1B,KAAK,GAAG2B,CAAC,CAAC3B,KAAK,EAAE;MACxB,OAAO,CAAC,CAAC;IACb,CAAC,MACI;MACD,OAAO,CAAC;IACZ;EACJ;AACJ;AACA,SAAS4B,iBAAiBA,CAACtD,QAAQ,EAAE;EACjC,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;IAC9B,OAAO,KAAK;EAChB;EACA,IAAIC,KAAK,CAACC,OAAO,CAACF,QAAQ,CAAC,EAAE;IACzB,OAAOA,QAAQ,CAACuD,IAAI,CAACD,iBAAiB,CAAC;EAC3C;EACA,OAAOE,OAAO,CAACxD,QAAQ,CAACyD,SAAS,CAAC;AACtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}