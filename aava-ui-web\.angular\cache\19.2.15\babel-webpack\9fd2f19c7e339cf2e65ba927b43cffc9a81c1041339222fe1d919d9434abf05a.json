{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { LcsDiff } from '../../../../../base/common/diff/diff.js';\nimport { commonPrefixLength, getLeadingWhitespace } from '../../../../../base/common/strings.js';\nimport { Range } from '../../../../common/core/range.js';\nimport { TextLength } from '../../../../common/core/textLength.js';\nimport { SingleTextEdit } from '../../../../common/core/textEdit.js';\nimport { GhostText, GhostTextPart } from './ghostText.js';\nexport function singleTextRemoveCommonPrefix(edit, model, validModelRange) {\n  const modelRange = validModelRange ? edit.range.intersectRanges(validModelRange) : edit.range;\n  if (!modelRange) {\n    return edit;\n  }\n  const valueToReplace = model.getValueInRange(modelRange, 1 /* EndOfLinePreference.LF */);\n  const commonPrefixLen = commonPrefixLength(valueToReplace, edit.text);\n  const start = TextLength.ofText(valueToReplace.substring(0, commonPrefixLen)).addToPosition(edit.range.getStartPosition());\n  const text = edit.text.substring(commonPrefixLen);\n  const range = Range.fromPositions(start, edit.range.getEndPosition());\n  return new SingleTextEdit(range, text);\n}\nexport function singleTextEditAugments(edit, base) {\n  // The augmented completion must replace the base range, but can replace even more\n  return edit.text.startsWith(base.text) && rangeExtends(edit.range, base.range);\n}\n/**\n * @param previewSuffixLength Sets where to split `inlineCompletion.text`.\n * \tIf the text is `hello` and the suffix length is 2, the non-preview part is `hel` and the preview-part is `lo`.\n*/\nexport function computeGhostText(edit, model, mode, cursorPosition, previewSuffixLength = 0) {\n  let e = singleTextRemoveCommonPrefix(edit, model);\n  if (e.range.endLineNumber !== e.range.startLineNumber) {\n    // This edit might span multiple lines, but the first lines must be a common prefix.\n    return undefined;\n  }\n  const sourceLine = model.getLineContent(e.range.startLineNumber);\n  const sourceIndentationLength = getLeadingWhitespace(sourceLine).length;\n  const suggestionTouchesIndentation = e.range.startColumn - 1 <= sourceIndentationLength;\n  if (suggestionTouchesIndentation) {\n    // source:      ··········[······abc]\n    //                         ^^^^^^^^^ inlineCompletion.range\n    //              ^^^^^^^^^^ ^^^^^^ sourceIndentationLength\n    //                         ^^^^^^ replacedIndentation.length\n    //                               ^^^ rangeThatDoesNotReplaceIndentation\n    // inlineCompletion.text: '··foo'\n    //                         ^^ suggestionAddedIndentationLength\n    const suggestionAddedIndentationLength = getLeadingWhitespace(e.text).length;\n    const replacedIndentation = sourceLine.substring(e.range.startColumn - 1, sourceIndentationLength);\n    const [startPosition, endPosition] = [e.range.getStartPosition(), e.range.getEndPosition()];\n    const newStartPosition = startPosition.column + replacedIndentation.length <= endPosition.column ? startPosition.delta(0, replacedIndentation.length) : endPosition;\n    const rangeThatDoesNotReplaceIndentation = Range.fromPositions(newStartPosition, endPosition);\n    const suggestionWithoutIndentationChange = e.text.startsWith(replacedIndentation)\n    // Adds more indentation without changing existing indentation: We can add ghost text for this\n    ? e.text.substring(replacedIndentation.length)\n    // Changes or removes existing indentation. Only add ghost text for the non-indentation part.\n    : e.text.substring(suggestionAddedIndentationLength);\n    e = new SingleTextEdit(rangeThatDoesNotReplaceIndentation, suggestionWithoutIndentationChange);\n  }\n  // This is a single line string\n  const valueToBeReplaced = model.getValueInRange(e.range);\n  const changes = cachingDiff(valueToBeReplaced, e.text);\n  if (!changes) {\n    // No ghost text in case the diff would be too slow to compute\n    return undefined;\n  }\n  const lineNumber = e.range.startLineNumber;\n  const parts = new Array();\n  if (mode === 'prefix') {\n    const filteredChanges = changes.filter(c => c.originalLength === 0);\n    if (filteredChanges.length > 1 || filteredChanges.length === 1 && filteredChanges[0].originalStart !== valueToBeReplaced.length) {\n      // Prefixes only have a single change.\n      return undefined;\n    }\n  }\n  const previewStartInCompletionText = e.text.length - previewSuffixLength;\n  for (const c of changes) {\n    const insertColumn = e.range.startColumn + c.originalStart + c.originalLength;\n    if (mode === 'subwordSmart' && cursorPosition && cursorPosition.lineNumber === e.range.startLineNumber && insertColumn < cursorPosition.column) {\n      // No ghost text before cursor\n      return undefined;\n    }\n    if (c.originalLength > 0) {\n      return undefined;\n    }\n    if (c.modifiedLength === 0) {\n      continue;\n    }\n    const modifiedEnd = c.modifiedStart + c.modifiedLength;\n    const nonPreviewTextEnd = Math.max(c.modifiedStart, Math.min(modifiedEnd, previewStartInCompletionText));\n    const nonPreviewText = e.text.substring(c.modifiedStart, nonPreviewTextEnd);\n    const italicText = e.text.substring(nonPreviewTextEnd, Math.max(c.modifiedStart, modifiedEnd));\n    if (nonPreviewText.length > 0) {\n      parts.push(new GhostTextPart(insertColumn, nonPreviewText, false));\n    }\n    if (italicText.length > 0) {\n      parts.push(new GhostTextPart(insertColumn, italicText, true));\n    }\n  }\n  return new GhostText(lineNumber, parts);\n}\nfunction rangeExtends(extendingRange, rangeToExtend) {\n  return rangeToExtend.getStartPosition().equals(extendingRange.getStartPosition()) && rangeToExtend.getEndPosition().isBeforeOrEqual(extendingRange.getEndPosition());\n}\nlet lastRequest = undefined;\nfunction cachingDiff(originalValue, newValue) {\n  if (lastRequest?.originalValue === originalValue && lastRequest?.newValue === newValue) {\n    return lastRequest?.changes;\n  } else {\n    let changes = smartDiff(originalValue, newValue, true);\n    if (changes) {\n      const deletedChars = deletedCharacters(changes);\n      if (deletedChars > 0) {\n        // For performance reasons, don't compute diff if there is nothing to improve\n        const newChanges = smartDiff(originalValue, newValue, false);\n        if (newChanges && deletedCharacters(newChanges) < deletedChars) {\n          // Disabling smartness seems to be better here\n          changes = newChanges;\n        }\n      }\n    }\n    lastRequest = {\n      originalValue,\n      newValue,\n      changes\n    };\n    return changes;\n  }\n}\nfunction deletedCharacters(changes) {\n  let sum = 0;\n  for (const c of changes) {\n    sum += c.originalLength;\n  }\n  return sum;\n}\n/**\n * When matching `if ()` with `if (f() = 1) { g(); }`,\n * align it like this:        `if (       )`\n * Not like this:\t\t\t  `if (  )`\n * Also not like this:\t\t  `if (             )`.\n *\n * The parenthesis are preprocessed to ensure that they match correctly.\n */\nfunction smartDiff(originalValue, newValue, smartBracketMatching) {\n  if (originalValue.length > 5000 || newValue.length > 5000) {\n    // We don't want to work on strings that are too big\n    return undefined;\n  }\n  function getMaxCharCode(val) {\n    let maxCharCode = 0;\n    for (let i = 0, len = val.length; i < len; i++) {\n      const charCode = val.charCodeAt(i);\n      if (charCode > maxCharCode) {\n        maxCharCode = charCode;\n      }\n    }\n    return maxCharCode;\n  }\n  const maxCharCode = Math.max(getMaxCharCode(originalValue), getMaxCharCode(newValue));\n  function getUniqueCharCode(id) {\n    if (id < 0) {\n      throw new Error('unexpected');\n    }\n    return maxCharCode + id + 1;\n  }\n  function getElements(source) {\n    let level = 0;\n    let group = 0;\n    const characters = new Int32Array(source.length);\n    for (let i = 0, len = source.length; i < len; i++) {\n      // TODO support more brackets\n      if (smartBracketMatching && source[i] === '(') {\n        const id = group * 100 + level;\n        characters[i] = getUniqueCharCode(2 * id);\n        level++;\n      } else if (smartBracketMatching && source[i] === ')') {\n        level = Math.max(level - 1, 0);\n        const id = group * 100 + level;\n        characters[i] = getUniqueCharCode(2 * id + 1);\n        if (level === 0) {\n          group++;\n        }\n      } else {\n        characters[i] = source.charCodeAt(i);\n      }\n    }\n    return characters;\n  }\n  const elements1 = getElements(originalValue);\n  const elements2 = getElements(newValue);\n  return new LcsDiff({\n    getElements: () => elements1\n  }, {\n    getElements: () => elements2\n  }).ComputeDiff(false).changes;\n}", "map": {"version": 3, "names": ["LcsDiff", "commonPrefixLength", "getLeadingWhitespace", "Range", "TextLength", "SingleTextEdit", "GhostText", "GhostTextPart", "singleTextRemoveCommonPrefix", "edit", "model", "validModelRange", "modelRange", "range", "intersectRanges", "valueToReplace", "getValueInRange", "commonPrefixLen", "text", "start", "ofText", "substring", "addToPosition", "getStartPosition", "fromPositions", "getEndPosition", "singleTextEditAugments", "base", "startsWith", "rangeExtends", "computeGhostText", "mode", "cursorPosition", "previewSuffixLength", "e", "endLineNumber", "startLineNumber", "undefined", "sourceLine", "get<PERSON>ineC<PERSON>nt", "sourceIndentationLength", "length", "suggestionTouchesIndentation", "startColumn", "suggestionAddedIndentationLength", "replacedIndentation", "startPosition", "endPosition", "newStartPosition", "column", "delta", "rangeThatDoesNotReplaceIndentation", "suggestionWithoutIndentationChange", "valueToBeReplaced", "changes", "cachingDiff", "lineNumber", "parts", "Array", "filteredChanges", "filter", "c", "original<PERSON>ength", "originalStart", "previewStartInCompletionText", "insertColumn", "<PERSON><PERSON><PERSON><PERSON>", "modifiedEnd", "modifiedStart", "nonPreviewTextEnd", "Math", "max", "min", "nonPreviewText", "italicText", "push", "extendingRange", "rangeToExtend", "equals", "isBeforeOrEqual", "lastRequest", "originalValue", "newValue", "smartDiff", "deletedChars", "deletedCharacters", "newChanges", "sum", "smartBracketMatching", "getMaxCharCode", "val", "maxCharCode", "i", "len", "charCode", "charCodeAt", "getUniqueCharCode", "id", "Error", "getElements", "source", "level", "group", "characters", "Int32Array", "elements1", "elements2", "ComputeDiff"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/contrib/inlineCompletions/browser/model/singleTextEdit.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { LcsDiff } from '../../../../../base/common/diff/diff.js';\nimport { commonPrefixLength, getLeadingWhitespace } from '../../../../../base/common/strings.js';\nimport { Range } from '../../../../common/core/range.js';\nimport { TextLength } from '../../../../common/core/textLength.js';\nimport { SingleTextEdit } from '../../../../common/core/textEdit.js';\nimport { GhostText, GhostTextPart } from './ghostText.js';\nexport function singleTextRemoveCommonPrefix(edit, model, validModelRange) {\n    const modelRange = validModelRange ? edit.range.intersectRanges(validModelRange) : edit.range;\n    if (!modelRange) {\n        return edit;\n    }\n    const valueToReplace = model.getValueInRange(modelRange, 1 /* EndOfLinePreference.LF */);\n    const commonPrefixLen = commonPrefixLength(valueToReplace, edit.text);\n    const start = TextLength.ofText(valueToReplace.substring(0, commonPrefixLen)).addToPosition(edit.range.getStartPosition());\n    const text = edit.text.substring(commonPrefixLen);\n    const range = Range.fromPositions(start, edit.range.getEndPosition());\n    return new SingleTextEdit(range, text);\n}\nexport function singleTextEditAugments(edit, base) {\n    // The augmented completion must replace the base range, but can replace even more\n    return edit.text.startsWith(base.text) && rangeExtends(edit.range, base.range);\n}\n/**\n * @param previewSuffixLength Sets where to split `inlineCompletion.text`.\n * \tIf the text is `hello` and the suffix length is 2, the non-preview part is `hel` and the preview-part is `lo`.\n*/\nexport function computeGhostText(edit, model, mode, cursorPosition, previewSuffixLength = 0) {\n    let e = singleTextRemoveCommonPrefix(edit, model);\n    if (e.range.endLineNumber !== e.range.startLineNumber) {\n        // This edit might span multiple lines, but the first lines must be a common prefix.\n        return undefined;\n    }\n    const sourceLine = model.getLineContent(e.range.startLineNumber);\n    const sourceIndentationLength = getLeadingWhitespace(sourceLine).length;\n    const suggestionTouchesIndentation = e.range.startColumn - 1 <= sourceIndentationLength;\n    if (suggestionTouchesIndentation) {\n        // source:      ··········[······abc]\n        //                         ^^^^^^^^^ inlineCompletion.range\n        //              ^^^^^^^^^^ ^^^^^^ sourceIndentationLength\n        //                         ^^^^^^ replacedIndentation.length\n        //                               ^^^ rangeThatDoesNotReplaceIndentation\n        // inlineCompletion.text: '··foo'\n        //                         ^^ suggestionAddedIndentationLength\n        const suggestionAddedIndentationLength = getLeadingWhitespace(e.text).length;\n        const replacedIndentation = sourceLine.substring(e.range.startColumn - 1, sourceIndentationLength);\n        const [startPosition, endPosition] = [e.range.getStartPosition(), e.range.getEndPosition()];\n        const newStartPosition = startPosition.column + replacedIndentation.length <= endPosition.column\n            ? startPosition.delta(0, replacedIndentation.length)\n            : endPosition;\n        const rangeThatDoesNotReplaceIndentation = Range.fromPositions(newStartPosition, endPosition);\n        const suggestionWithoutIndentationChange = e.text.startsWith(replacedIndentation)\n            // Adds more indentation without changing existing indentation: We can add ghost text for this\n            ? e.text.substring(replacedIndentation.length)\n            // Changes or removes existing indentation. Only add ghost text for the non-indentation part.\n            : e.text.substring(suggestionAddedIndentationLength);\n        e = new SingleTextEdit(rangeThatDoesNotReplaceIndentation, suggestionWithoutIndentationChange);\n    }\n    // This is a single line string\n    const valueToBeReplaced = model.getValueInRange(e.range);\n    const changes = cachingDiff(valueToBeReplaced, e.text);\n    if (!changes) {\n        // No ghost text in case the diff would be too slow to compute\n        return undefined;\n    }\n    const lineNumber = e.range.startLineNumber;\n    const parts = new Array();\n    if (mode === 'prefix') {\n        const filteredChanges = changes.filter(c => c.originalLength === 0);\n        if (filteredChanges.length > 1 || filteredChanges.length === 1 && filteredChanges[0].originalStart !== valueToBeReplaced.length) {\n            // Prefixes only have a single change.\n            return undefined;\n        }\n    }\n    const previewStartInCompletionText = e.text.length - previewSuffixLength;\n    for (const c of changes) {\n        const insertColumn = e.range.startColumn + c.originalStart + c.originalLength;\n        if (mode === 'subwordSmart' && cursorPosition && cursorPosition.lineNumber === e.range.startLineNumber && insertColumn < cursorPosition.column) {\n            // No ghost text before cursor\n            return undefined;\n        }\n        if (c.originalLength > 0) {\n            return undefined;\n        }\n        if (c.modifiedLength === 0) {\n            continue;\n        }\n        const modifiedEnd = c.modifiedStart + c.modifiedLength;\n        const nonPreviewTextEnd = Math.max(c.modifiedStart, Math.min(modifiedEnd, previewStartInCompletionText));\n        const nonPreviewText = e.text.substring(c.modifiedStart, nonPreviewTextEnd);\n        const italicText = e.text.substring(nonPreviewTextEnd, Math.max(c.modifiedStart, modifiedEnd));\n        if (nonPreviewText.length > 0) {\n            parts.push(new GhostTextPart(insertColumn, nonPreviewText, false));\n        }\n        if (italicText.length > 0) {\n            parts.push(new GhostTextPart(insertColumn, italicText, true));\n        }\n    }\n    return new GhostText(lineNumber, parts);\n}\nfunction rangeExtends(extendingRange, rangeToExtend) {\n    return rangeToExtend.getStartPosition().equals(extendingRange.getStartPosition())\n        && rangeToExtend.getEndPosition().isBeforeOrEqual(extendingRange.getEndPosition());\n}\nlet lastRequest = undefined;\nfunction cachingDiff(originalValue, newValue) {\n    if (lastRequest?.originalValue === originalValue && lastRequest?.newValue === newValue) {\n        return lastRequest?.changes;\n    }\n    else {\n        let changes = smartDiff(originalValue, newValue, true);\n        if (changes) {\n            const deletedChars = deletedCharacters(changes);\n            if (deletedChars > 0) {\n                // For performance reasons, don't compute diff if there is nothing to improve\n                const newChanges = smartDiff(originalValue, newValue, false);\n                if (newChanges && deletedCharacters(newChanges) < deletedChars) {\n                    // Disabling smartness seems to be better here\n                    changes = newChanges;\n                }\n            }\n        }\n        lastRequest = {\n            originalValue,\n            newValue,\n            changes\n        };\n        return changes;\n    }\n}\nfunction deletedCharacters(changes) {\n    let sum = 0;\n    for (const c of changes) {\n        sum += c.originalLength;\n    }\n    return sum;\n}\n/**\n * When matching `if ()` with `if (f() = 1) { g(); }`,\n * align it like this:        `if (       )`\n * Not like this:\t\t\t  `if (  )`\n * Also not like this:\t\t  `if (             )`.\n *\n * The parenthesis are preprocessed to ensure that they match correctly.\n */\nfunction smartDiff(originalValue, newValue, smartBracketMatching) {\n    if (originalValue.length > 5000 || newValue.length > 5000) {\n        // We don't want to work on strings that are too big\n        return undefined;\n    }\n    function getMaxCharCode(val) {\n        let maxCharCode = 0;\n        for (let i = 0, len = val.length; i < len; i++) {\n            const charCode = val.charCodeAt(i);\n            if (charCode > maxCharCode) {\n                maxCharCode = charCode;\n            }\n        }\n        return maxCharCode;\n    }\n    const maxCharCode = Math.max(getMaxCharCode(originalValue), getMaxCharCode(newValue));\n    function getUniqueCharCode(id) {\n        if (id < 0) {\n            throw new Error('unexpected');\n        }\n        return maxCharCode + id + 1;\n    }\n    function getElements(source) {\n        let level = 0;\n        let group = 0;\n        const characters = new Int32Array(source.length);\n        for (let i = 0, len = source.length; i < len; i++) {\n            // TODO support more brackets\n            if (smartBracketMatching && source[i] === '(') {\n                const id = group * 100 + level;\n                characters[i] = getUniqueCharCode(2 * id);\n                level++;\n            }\n            else if (smartBracketMatching && source[i] === ')') {\n                level = Math.max(level - 1, 0);\n                const id = group * 100 + level;\n                characters[i] = getUniqueCharCode(2 * id + 1);\n                if (level === 0) {\n                    group++;\n                }\n            }\n            else {\n                characters[i] = source.charCodeAt(i);\n            }\n        }\n        return characters;\n    }\n    const elements1 = getElements(originalValue);\n    const elements2 = getElements(newValue);\n    return new LcsDiff({ getElements: () => elements1 }, { getElements: () => elements2 }).ComputeDiff(false).changes;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,OAAO,QAAQ,yCAAyC;AACjE,SAASC,kBAAkB,EAAEC,oBAAoB,QAAQ,uCAAuC;AAChG,SAASC,KAAK,QAAQ,kCAAkC;AACxD,SAASC,UAAU,QAAQ,uCAAuC;AAClE,SAASC,cAAc,QAAQ,qCAAqC;AACpE,SAASC,SAAS,EAAEC,aAAa,QAAQ,gBAAgB;AACzD,OAAO,SAASC,4BAA4BA,CAACC,IAAI,EAAEC,KAAK,EAAEC,eAAe,EAAE;EACvE,MAAMC,UAAU,GAAGD,eAAe,GAAGF,IAAI,CAACI,KAAK,CAACC,eAAe,CAACH,eAAe,CAAC,GAAGF,IAAI,CAACI,KAAK;EAC7F,IAAI,CAACD,UAAU,EAAE;IACb,OAAOH,IAAI;EACf;EACA,MAAMM,cAAc,GAAGL,KAAK,CAACM,eAAe,CAACJ,UAAU,EAAE,CAAC,CAAC,4BAA4B,CAAC;EACxF,MAAMK,eAAe,GAAGhB,kBAAkB,CAACc,cAAc,EAAEN,IAAI,CAACS,IAAI,CAAC;EACrE,MAAMC,KAAK,GAAGf,UAAU,CAACgB,MAAM,CAACL,cAAc,CAACM,SAAS,CAAC,CAAC,EAAEJ,eAAe,CAAC,CAAC,CAACK,aAAa,CAACb,IAAI,CAACI,KAAK,CAACU,gBAAgB,CAAC,CAAC,CAAC;EAC1H,MAAML,IAAI,GAAGT,IAAI,CAACS,IAAI,CAACG,SAAS,CAACJ,eAAe,CAAC;EACjD,MAAMJ,KAAK,GAAGV,KAAK,CAACqB,aAAa,CAACL,KAAK,EAAEV,IAAI,CAACI,KAAK,CAACY,cAAc,CAAC,CAAC,CAAC;EACrE,OAAO,IAAIpB,cAAc,CAACQ,KAAK,EAAEK,IAAI,CAAC;AAC1C;AACA,OAAO,SAASQ,sBAAsBA,CAACjB,IAAI,EAAEkB,IAAI,EAAE;EAC/C;EACA,OAAOlB,IAAI,CAACS,IAAI,CAACU,UAAU,CAACD,IAAI,CAACT,IAAI,CAAC,IAAIW,YAAY,CAACpB,IAAI,CAACI,KAAK,EAAEc,IAAI,CAACd,KAAK,CAAC;AAClF;AACA;AACA;AACA;AACA;AACA,OAAO,SAASiB,gBAAgBA,CAACrB,IAAI,EAAEC,KAAK,EAAEqB,IAAI,EAAEC,cAAc,EAAEC,mBAAmB,GAAG,CAAC,EAAE;EACzF,IAAIC,CAAC,GAAG1B,4BAA4B,CAACC,IAAI,EAAEC,KAAK,CAAC;EACjD,IAAIwB,CAAC,CAACrB,KAAK,CAACsB,aAAa,KAAKD,CAAC,CAACrB,KAAK,CAACuB,eAAe,EAAE;IACnD;IACA,OAAOC,SAAS;EACpB;EACA,MAAMC,UAAU,GAAG5B,KAAK,CAAC6B,cAAc,CAACL,CAAC,CAACrB,KAAK,CAACuB,eAAe,CAAC;EAChE,MAAMI,uBAAuB,GAAGtC,oBAAoB,CAACoC,UAAU,CAAC,CAACG,MAAM;EACvE,MAAMC,4BAA4B,GAAGR,CAAC,CAACrB,KAAK,CAAC8B,WAAW,GAAG,CAAC,IAAIH,uBAAuB;EACvF,IAAIE,4BAA4B,EAAE;IAC9B;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAME,gCAAgC,GAAG1C,oBAAoB,CAACgC,CAAC,CAAChB,IAAI,CAAC,CAACuB,MAAM;IAC5E,MAAMI,mBAAmB,GAAGP,UAAU,CAACjB,SAAS,CAACa,CAAC,CAACrB,KAAK,CAAC8B,WAAW,GAAG,CAAC,EAAEH,uBAAuB,CAAC;IAClG,MAAM,CAACM,aAAa,EAAEC,WAAW,CAAC,GAAG,CAACb,CAAC,CAACrB,KAAK,CAACU,gBAAgB,CAAC,CAAC,EAAEW,CAAC,CAACrB,KAAK,CAACY,cAAc,CAAC,CAAC,CAAC;IAC3F,MAAMuB,gBAAgB,GAAGF,aAAa,CAACG,MAAM,GAAGJ,mBAAmB,CAACJ,MAAM,IAAIM,WAAW,CAACE,MAAM,GAC1FH,aAAa,CAACI,KAAK,CAAC,CAAC,EAAEL,mBAAmB,CAACJ,MAAM,CAAC,GAClDM,WAAW;IACjB,MAAMI,kCAAkC,GAAGhD,KAAK,CAACqB,aAAa,CAACwB,gBAAgB,EAAED,WAAW,CAAC;IAC7F,MAAMK,kCAAkC,GAAGlB,CAAC,CAAChB,IAAI,CAACU,UAAU,CAACiB,mBAAmB;IAC5E;IAAA,EACEX,CAAC,CAAChB,IAAI,CAACG,SAAS,CAACwB,mBAAmB,CAACJ,MAAM;IAC7C;IAAA,EACEP,CAAC,CAAChB,IAAI,CAACG,SAAS,CAACuB,gCAAgC,CAAC;IACxDV,CAAC,GAAG,IAAI7B,cAAc,CAAC8C,kCAAkC,EAAEC,kCAAkC,CAAC;EAClG;EACA;EACA,MAAMC,iBAAiB,GAAG3C,KAAK,CAACM,eAAe,CAACkB,CAAC,CAACrB,KAAK,CAAC;EACxD,MAAMyC,OAAO,GAAGC,WAAW,CAACF,iBAAiB,EAAEnB,CAAC,CAAChB,IAAI,CAAC;EACtD,IAAI,CAACoC,OAAO,EAAE;IACV;IACA,OAAOjB,SAAS;EACpB;EACA,MAAMmB,UAAU,GAAGtB,CAAC,CAACrB,KAAK,CAACuB,eAAe;EAC1C,MAAMqB,KAAK,GAAG,IAAIC,KAAK,CAAC,CAAC;EACzB,IAAI3B,IAAI,KAAK,QAAQ,EAAE;IACnB,MAAM4B,eAAe,GAAGL,OAAO,CAACM,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,cAAc,KAAK,CAAC,CAAC;IACnE,IAAIH,eAAe,CAAClB,MAAM,GAAG,CAAC,IAAIkB,eAAe,CAAClB,MAAM,KAAK,CAAC,IAAIkB,eAAe,CAAC,CAAC,CAAC,CAACI,aAAa,KAAKV,iBAAiB,CAACZ,MAAM,EAAE;MAC7H;MACA,OAAOJ,SAAS;IACpB;EACJ;EACA,MAAM2B,4BAA4B,GAAG9B,CAAC,CAAChB,IAAI,CAACuB,MAAM,GAAGR,mBAAmB;EACxE,KAAK,MAAM4B,CAAC,IAAIP,OAAO,EAAE;IACrB,MAAMW,YAAY,GAAG/B,CAAC,CAACrB,KAAK,CAAC8B,WAAW,GAAGkB,CAAC,CAACE,aAAa,GAAGF,CAAC,CAACC,cAAc;IAC7E,IAAI/B,IAAI,KAAK,cAAc,IAAIC,cAAc,IAAIA,cAAc,CAACwB,UAAU,KAAKtB,CAAC,CAACrB,KAAK,CAACuB,eAAe,IAAI6B,YAAY,GAAGjC,cAAc,CAACiB,MAAM,EAAE;MAC5I;MACA,OAAOZ,SAAS;IACpB;IACA,IAAIwB,CAAC,CAACC,cAAc,GAAG,CAAC,EAAE;MACtB,OAAOzB,SAAS;IACpB;IACA,IAAIwB,CAAC,CAACK,cAAc,KAAK,CAAC,EAAE;MACxB;IACJ;IACA,MAAMC,WAAW,GAAGN,CAAC,CAACO,aAAa,GAAGP,CAAC,CAACK,cAAc;IACtD,MAAMG,iBAAiB,GAAGC,IAAI,CAACC,GAAG,CAACV,CAAC,CAACO,aAAa,EAAEE,IAAI,CAACE,GAAG,CAACL,WAAW,EAAEH,4BAA4B,CAAC,CAAC;IACxG,MAAMS,cAAc,GAAGvC,CAAC,CAAChB,IAAI,CAACG,SAAS,CAACwC,CAAC,CAACO,aAAa,EAAEC,iBAAiB,CAAC;IAC3E,MAAMK,UAAU,GAAGxC,CAAC,CAAChB,IAAI,CAACG,SAAS,CAACgD,iBAAiB,EAAEC,IAAI,CAACC,GAAG,CAACV,CAAC,CAACO,aAAa,EAAED,WAAW,CAAC,CAAC;IAC9F,IAAIM,cAAc,CAAChC,MAAM,GAAG,CAAC,EAAE;MAC3BgB,KAAK,CAACkB,IAAI,CAAC,IAAIpE,aAAa,CAAC0D,YAAY,EAAEQ,cAAc,EAAE,KAAK,CAAC,CAAC;IACtE;IACA,IAAIC,UAAU,CAACjC,MAAM,GAAG,CAAC,EAAE;MACvBgB,KAAK,CAACkB,IAAI,CAAC,IAAIpE,aAAa,CAAC0D,YAAY,EAAES,UAAU,EAAE,IAAI,CAAC,CAAC;IACjE;EACJ;EACA,OAAO,IAAIpE,SAAS,CAACkD,UAAU,EAAEC,KAAK,CAAC;AAC3C;AACA,SAAS5B,YAAYA,CAAC+C,cAAc,EAAEC,aAAa,EAAE;EACjD,OAAOA,aAAa,CAACtD,gBAAgB,CAAC,CAAC,CAACuD,MAAM,CAACF,cAAc,CAACrD,gBAAgB,CAAC,CAAC,CAAC,IAC1EsD,aAAa,CAACpD,cAAc,CAAC,CAAC,CAACsD,eAAe,CAACH,cAAc,CAACnD,cAAc,CAAC,CAAC,CAAC;AAC1F;AACA,IAAIuD,WAAW,GAAG3C,SAAS;AAC3B,SAASkB,WAAWA,CAAC0B,aAAa,EAAEC,QAAQ,EAAE;EAC1C,IAAIF,WAAW,EAAEC,aAAa,KAAKA,aAAa,IAAID,WAAW,EAAEE,QAAQ,KAAKA,QAAQ,EAAE;IACpF,OAAOF,WAAW,EAAE1B,OAAO;EAC/B,CAAC,MACI;IACD,IAAIA,OAAO,GAAG6B,SAAS,CAACF,aAAa,EAAEC,QAAQ,EAAE,IAAI,CAAC;IACtD,IAAI5B,OAAO,EAAE;MACT,MAAM8B,YAAY,GAAGC,iBAAiB,CAAC/B,OAAO,CAAC;MAC/C,IAAI8B,YAAY,GAAG,CAAC,EAAE;QAClB;QACA,MAAME,UAAU,GAAGH,SAAS,CAACF,aAAa,EAAEC,QAAQ,EAAE,KAAK,CAAC;QAC5D,IAAII,UAAU,IAAID,iBAAiB,CAACC,UAAU,CAAC,GAAGF,YAAY,EAAE;UAC5D;UACA9B,OAAO,GAAGgC,UAAU;QACxB;MACJ;IACJ;IACAN,WAAW,GAAG;MACVC,aAAa;MACbC,QAAQ;MACR5B;IACJ,CAAC;IACD,OAAOA,OAAO;EAClB;AACJ;AACA,SAAS+B,iBAAiBA,CAAC/B,OAAO,EAAE;EAChC,IAAIiC,GAAG,GAAG,CAAC;EACX,KAAK,MAAM1B,CAAC,IAAIP,OAAO,EAAE;IACrBiC,GAAG,IAAI1B,CAAC,CAACC,cAAc;EAC3B;EACA,OAAOyB,GAAG;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASJ,SAASA,CAACF,aAAa,EAAEC,QAAQ,EAAEM,oBAAoB,EAAE;EAC9D,IAAIP,aAAa,CAACxC,MAAM,GAAG,IAAI,IAAIyC,QAAQ,CAACzC,MAAM,GAAG,IAAI,EAAE;IACvD;IACA,OAAOJ,SAAS;EACpB;EACA,SAASoD,cAAcA,CAACC,GAAG,EAAE;IACzB,IAAIC,WAAW,GAAG,CAAC;IACnB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGH,GAAG,CAACjD,MAAM,EAAEmD,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;MAC5C,MAAME,QAAQ,GAAGJ,GAAG,CAACK,UAAU,CAACH,CAAC,CAAC;MAClC,IAAIE,QAAQ,GAAGH,WAAW,EAAE;QACxBA,WAAW,GAAGG,QAAQ;MAC1B;IACJ;IACA,OAAOH,WAAW;EACtB;EACA,MAAMA,WAAW,GAAGrB,IAAI,CAACC,GAAG,CAACkB,cAAc,CAACR,aAAa,CAAC,EAAEQ,cAAc,CAACP,QAAQ,CAAC,CAAC;EACrF,SAASc,iBAAiBA,CAACC,EAAE,EAAE;IAC3B,IAAIA,EAAE,GAAG,CAAC,EAAE;MACR,MAAM,IAAIC,KAAK,CAAC,YAAY,CAAC;IACjC;IACA,OAAOP,WAAW,GAAGM,EAAE,GAAG,CAAC;EAC/B;EACA,SAASE,WAAWA,CAACC,MAAM,EAAE;IACzB,IAAIC,KAAK,GAAG,CAAC;IACb,IAAIC,KAAK,GAAG,CAAC;IACb,MAAMC,UAAU,GAAG,IAAIC,UAAU,CAACJ,MAAM,CAAC3D,MAAM,CAAC;IAChD,KAAK,IAAImD,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGO,MAAM,CAAC3D,MAAM,EAAEmD,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;MAC/C;MACA,IAAIJ,oBAAoB,IAAIY,MAAM,CAACR,CAAC,CAAC,KAAK,GAAG,EAAE;QAC3C,MAAMK,EAAE,GAAGK,KAAK,GAAG,GAAG,GAAGD,KAAK;QAC9BE,UAAU,CAACX,CAAC,CAAC,GAAGI,iBAAiB,CAAC,CAAC,GAAGC,EAAE,CAAC;QACzCI,KAAK,EAAE;MACX,CAAC,MACI,IAAIb,oBAAoB,IAAIY,MAAM,CAACR,CAAC,CAAC,KAAK,GAAG,EAAE;QAChDS,KAAK,GAAG/B,IAAI,CAACC,GAAG,CAAC8B,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC;QAC9B,MAAMJ,EAAE,GAAGK,KAAK,GAAG,GAAG,GAAGD,KAAK;QAC9BE,UAAU,CAACX,CAAC,CAAC,GAAGI,iBAAiB,CAAC,CAAC,GAAGC,EAAE,GAAG,CAAC,CAAC;QAC7C,IAAII,KAAK,KAAK,CAAC,EAAE;UACbC,KAAK,EAAE;QACX;MACJ,CAAC,MACI;QACDC,UAAU,CAACX,CAAC,CAAC,GAAGQ,MAAM,CAACL,UAAU,CAACH,CAAC,CAAC;MACxC;IACJ;IACA,OAAOW,UAAU;EACrB;EACA,MAAME,SAAS,GAAGN,WAAW,CAAClB,aAAa,CAAC;EAC5C,MAAMyB,SAAS,GAAGP,WAAW,CAACjB,QAAQ,CAAC;EACvC,OAAO,IAAIlF,OAAO,CAAC;IAAEmG,WAAW,EAAEA,CAAA,KAAMM;EAAU,CAAC,EAAE;IAAEN,WAAW,EAAEA,CAAA,KAAMO;EAAU,CAAC,CAAC,CAACC,WAAW,CAAC,KAAK,CAAC,CAACrD,OAAO;AACrH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}