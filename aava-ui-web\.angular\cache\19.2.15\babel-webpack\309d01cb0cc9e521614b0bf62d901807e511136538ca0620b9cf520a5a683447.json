{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { CursorState, SingleCursorState } from '../cursorCommon.js';\nimport { Position } from '../core/position.js';\nimport { Range } from '../core/range.js';\nimport { Selection } from '../core/selection.js';\n/**\n * Represents a single cursor.\n*/\nexport class Cursor {\n  constructor(context) {\n    this._selTrackedRange = null;\n    this._trackSelection = true;\n    this._setState(context, new SingleCursorState(new Range(1, 1, 1, 1), 0 /* SelectionStartKind.Simple */, 0, new Position(1, 1), 0), new SingleCursorState(new Range(1, 1, 1, 1), 0 /* SelectionStartKind.Simple */, 0, new Position(1, 1), 0));\n  }\n  dispose(context) {\n    this._removeTrackedRange(context);\n  }\n  startTrackingSelection(context) {\n    this._trackSelection = true;\n    this._updateTrackedRange(context);\n  }\n  stopTrackingSelection(context) {\n    this._trackSelection = false;\n    this._removeTrackedRange(context);\n  }\n  _updateTrackedRange(context) {\n    if (!this._trackSelection) {\n      // don't track the selection\n      return;\n    }\n    this._selTrackedRange = context.model._setTrackedRange(this._selTrackedRange, this.modelState.selection, 0 /* TrackedRangeStickiness.AlwaysGrowsWhenTypingAtEdges */);\n  }\n  _removeTrackedRange(context) {\n    this._selTrackedRange = context.model._setTrackedRange(this._selTrackedRange, null, 0 /* TrackedRangeStickiness.AlwaysGrowsWhenTypingAtEdges */);\n  }\n  asCursorState() {\n    return new CursorState(this.modelState, this.viewState);\n  }\n  readSelectionFromMarkers(context) {\n    const range = context.model._getTrackedRange(this._selTrackedRange);\n    if (this.modelState.selection.isEmpty() && !range.isEmpty()) {\n      // Avoid selecting text when recovering from markers\n      return Selection.fromRange(range.collapseToEnd(), this.modelState.selection.getDirection());\n    }\n    return Selection.fromRange(range, this.modelState.selection.getDirection());\n  }\n  ensureValidState(context) {\n    this._setState(context, this.modelState, this.viewState);\n  }\n  setState(context, modelState, viewState) {\n    this._setState(context, modelState, viewState);\n  }\n  static _validatePositionWithCache(viewModel, position, cacheInput, cacheOutput) {\n    if (position.equals(cacheInput)) {\n      return cacheOutput;\n    }\n    return viewModel.normalizePosition(position, 2 /* PositionAffinity.None */);\n  }\n  static _validateViewState(viewModel, viewState) {\n    const position = viewState.position;\n    const sStartPosition = viewState.selectionStart.getStartPosition();\n    const sEndPosition = viewState.selectionStart.getEndPosition();\n    const validPosition = viewModel.normalizePosition(position, 2 /* PositionAffinity.None */);\n    const validSStartPosition = this._validatePositionWithCache(viewModel, sStartPosition, position, validPosition);\n    const validSEndPosition = this._validatePositionWithCache(viewModel, sEndPosition, sStartPosition, validSStartPosition);\n    if (position.equals(validPosition) && sStartPosition.equals(validSStartPosition) && sEndPosition.equals(validSEndPosition)) {\n      // fast path: the state is valid\n      return viewState;\n    }\n    return new SingleCursorState(Range.fromPositions(validSStartPosition, validSEndPosition), viewState.selectionStartKind, viewState.selectionStartLeftoverVisibleColumns + sStartPosition.column - validSStartPosition.column, validPosition, viewState.leftoverVisibleColumns + position.column - validPosition.column);\n  }\n  _setState(context, modelState, viewState) {\n    if (viewState) {\n      viewState = Cursor._validateViewState(context.viewModel, viewState);\n    }\n    if (!modelState) {\n      if (!viewState) {\n        return;\n      }\n      // We only have the view state => compute the model state\n      const selectionStart = context.model.validateRange(context.coordinatesConverter.convertViewRangeToModelRange(viewState.selectionStart));\n      const position = context.model.validatePosition(context.coordinatesConverter.convertViewPositionToModelPosition(viewState.position));\n      modelState = new SingleCursorState(selectionStart, viewState.selectionStartKind, viewState.selectionStartLeftoverVisibleColumns, position, viewState.leftoverVisibleColumns);\n    } else {\n      // Validate new model state\n      const selectionStart = context.model.validateRange(modelState.selectionStart);\n      const selectionStartLeftoverVisibleColumns = modelState.selectionStart.equalsRange(selectionStart) ? modelState.selectionStartLeftoverVisibleColumns : 0;\n      const position = context.model.validatePosition(modelState.position);\n      const leftoverVisibleColumns = modelState.position.equals(position) ? modelState.leftoverVisibleColumns : 0;\n      modelState = new SingleCursorState(selectionStart, modelState.selectionStartKind, selectionStartLeftoverVisibleColumns, position, leftoverVisibleColumns);\n    }\n    if (!viewState) {\n      // We only have the model state => compute the view state\n      const viewSelectionStart1 = context.coordinatesConverter.convertModelPositionToViewPosition(new Position(modelState.selectionStart.startLineNumber, modelState.selectionStart.startColumn));\n      const viewSelectionStart2 = context.coordinatesConverter.convertModelPositionToViewPosition(new Position(modelState.selectionStart.endLineNumber, modelState.selectionStart.endColumn));\n      const viewSelectionStart = new Range(viewSelectionStart1.lineNumber, viewSelectionStart1.column, viewSelectionStart2.lineNumber, viewSelectionStart2.column);\n      const viewPosition = context.coordinatesConverter.convertModelPositionToViewPosition(modelState.position);\n      viewState = new SingleCursorState(viewSelectionStart, modelState.selectionStartKind, modelState.selectionStartLeftoverVisibleColumns, viewPosition, modelState.leftoverVisibleColumns);\n    } else {\n      // Validate new view state\n      const viewSelectionStart = context.coordinatesConverter.validateViewRange(viewState.selectionStart, modelState.selectionStart);\n      const viewPosition = context.coordinatesConverter.validateViewPosition(viewState.position, modelState.position);\n      viewState = new SingleCursorState(viewSelectionStart, modelState.selectionStartKind, modelState.selectionStartLeftoverVisibleColumns, viewPosition, modelState.leftoverVisibleColumns);\n    }\n    this.modelState = modelState;\n    this.viewState = viewState;\n    this._updateTrackedRange(context);\n  }\n}", "map": {"version": 3, "names": ["CursorState", "SingleCursorState", "Position", "Range", "Selection", "<PERSON><PERSON><PERSON>", "constructor", "context", "_selTrackedRange", "_trackSelection", "_setState", "dispose", "_removeTrackedRange", "startTrackingSelection", "_updateTrackedRange", "stopTrackingSelection", "model", "_setTrackedRange", "modelState", "selection", "asCursorState", "viewState", "readSelectionFromMarkers", "range", "_getTrackedRange", "isEmpty", "fromRange", "collapseToEnd", "getDirection", "ensureValidState", "setState", "_validatePositionWithCache", "viewModel", "position", "cacheInput", "cacheOutput", "equals", "normalizePosition", "_validateViewState", "sStartPosition", "selectionStart", "getStartPosition", "sEndPosition", "getEndPosition", "validPosition", "validSStartPosition", "validSEndPosition", "fromPositions", "selectionStartKind", "selectionStartLeftoverVisibleColumns", "column", "leftoverVisibleColumns", "validate<PERSON><PERSON><PERSON>", "coordinatesConverter", "convertViewRangeToModelRange", "validatePosition", "convertViewPositionToModelPosition", "equalsRange", "viewSelectionStart1", "convertModelPositionToViewPosition", "startLineNumber", "startColumn", "viewSelectionStart2", "endLineNumber", "endColumn", "viewSelectionStart", "lineNumber", "viewPosition", "validate<PERSON>iewRange", "validateViewPosition"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/cursor/oneCursor.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { CursorState, SingleCursorState } from '../cursorCommon.js';\nimport { Position } from '../core/position.js';\nimport { Range } from '../core/range.js';\nimport { Selection } from '../core/selection.js';\n/**\n * Represents a single cursor.\n*/\nexport class Cursor {\n    constructor(context) {\n        this._selTrackedRange = null;\n        this._trackSelection = true;\n        this._setState(context, new SingleCursorState(new Range(1, 1, 1, 1), 0 /* SelectionStartKind.Simple */, 0, new Position(1, 1), 0), new SingleCursorState(new Range(1, 1, 1, 1), 0 /* SelectionStartKind.Simple */, 0, new Position(1, 1), 0));\n    }\n    dispose(context) {\n        this._removeTrackedRange(context);\n    }\n    startTrackingSelection(context) {\n        this._trackSelection = true;\n        this._updateTrackedRange(context);\n    }\n    stopTrackingSelection(context) {\n        this._trackSelection = false;\n        this._removeTrackedRange(context);\n    }\n    _updateTrackedRange(context) {\n        if (!this._trackSelection) {\n            // don't track the selection\n            return;\n        }\n        this._selTrackedRange = context.model._setTrackedRange(this._selTrackedRange, this.modelState.selection, 0 /* TrackedRangeStickiness.AlwaysGrowsWhenTypingAtEdges */);\n    }\n    _removeTrackedRange(context) {\n        this._selTrackedRange = context.model._setTrackedRange(this._selTrackedRange, null, 0 /* TrackedRangeStickiness.AlwaysGrowsWhenTypingAtEdges */);\n    }\n    asCursorState() {\n        return new CursorState(this.modelState, this.viewState);\n    }\n    readSelectionFromMarkers(context) {\n        const range = context.model._getTrackedRange(this._selTrackedRange);\n        if (this.modelState.selection.isEmpty() && !range.isEmpty()) {\n            // Avoid selecting text when recovering from markers\n            return Selection.fromRange(range.collapseToEnd(), this.modelState.selection.getDirection());\n        }\n        return Selection.fromRange(range, this.modelState.selection.getDirection());\n    }\n    ensureValidState(context) {\n        this._setState(context, this.modelState, this.viewState);\n    }\n    setState(context, modelState, viewState) {\n        this._setState(context, modelState, viewState);\n    }\n    static _validatePositionWithCache(viewModel, position, cacheInput, cacheOutput) {\n        if (position.equals(cacheInput)) {\n            return cacheOutput;\n        }\n        return viewModel.normalizePosition(position, 2 /* PositionAffinity.None */);\n    }\n    static _validateViewState(viewModel, viewState) {\n        const position = viewState.position;\n        const sStartPosition = viewState.selectionStart.getStartPosition();\n        const sEndPosition = viewState.selectionStart.getEndPosition();\n        const validPosition = viewModel.normalizePosition(position, 2 /* PositionAffinity.None */);\n        const validSStartPosition = this._validatePositionWithCache(viewModel, sStartPosition, position, validPosition);\n        const validSEndPosition = this._validatePositionWithCache(viewModel, sEndPosition, sStartPosition, validSStartPosition);\n        if (position.equals(validPosition) && sStartPosition.equals(validSStartPosition) && sEndPosition.equals(validSEndPosition)) {\n            // fast path: the state is valid\n            return viewState;\n        }\n        return new SingleCursorState(Range.fromPositions(validSStartPosition, validSEndPosition), viewState.selectionStartKind, viewState.selectionStartLeftoverVisibleColumns + sStartPosition.column - validSStartPosition.column, validPosition, viewState.leftoverVisibleColumns + position.column - validPosition.column);\n    }\n    _setState(context, modelState, viewState) {\n        if (viewState) {\n            viewState = Cursor._validateViewState(context.viewModel, viewState);\n        }\n        if (!modelState) {\n            if (!viewState) {\n                return;\n            }\n            // We only have the view state => compute the model state\n            const selectionStart = context.model.validateRange(context.coordinatesConverter.convertViewRangeToModelRange(viewState.selectionStart));\n            const position = context.model.validatePosition(context.coordinatesConverter.convertViewPositionToModelPosition(viewState.position));\n            modelState = new SingleCursorState(selectionStart, viewState.selectionStartKind, viewState.selectionStartLeftoverVisibleColumns, position, viewState.leftoverVisibleColumns);\n        }\n        else {\n            // Validate new model state\n            const selectionStart = context.model.validateRange(modelState.selectionStart);\n            const selectionStartLeftoverVisibleColumns = modelState.selectionStart.equalsRange(selectionStart) ? modelState.selectionStartLeftoverVisibleColumns : 0;\n            const position = context.model.validatePosition(modelState.position);\n            const leftoverVisibleColumns = modelState.position.equals(position) ? modelState.leftoverVisibleColumns : 0;\n            modelState = new SingleCursorState(selectionStart, modelState.selectionStartKind, selectionStartLeftoverVisibleColumns, position, leftoverVisibleColumns);\n        }\n        if (!viewState) {\n            // We only have the model state => compute the view state\n            const viewSelectionStart1 = context.coordinatesConverter.convertModelPositionToViewPosition(new Position(modelState.selectionStart.startLineNumber, modelState.selectionStart.startColumn));\n            const viewSelectionStart2 = context.coordinatesConverter.convertModelPositionToViewPosition(new Position(modelState.selectionStart.endLineNumber, modelState.selectionStart.endColumn));\n            const viewSelectionStart = new Range(viewSelectionStart1.lineNumber, viewSelectionStart1.column, viewSelectionStart2.lineNumber, viewSelectionStart2.column);\n            const viewPosition = context.coordinatesConverter.convertModelPositionToViewPosition(modelState.position);\n            viewState = new SingleCursorState(viewSelectionStart, modelState.selectionStartKind, modelState.selectionStartLeftoverVisibleColumns, viewPosition, modelState.leftoverVisibleColumns);\n        }\n        else {\n            // Validate new view state\n            const viewSelectionStart = context.coordinatesConverter.validateViewRange(viewState.selectionStart, modelState.selectionStart);\n            const viewPosition = context.coordinatesConverter.validateViewPosition(viewState.position, modelState.position);\n            viewState = new SingleCursorState(viewSelectionStart, modelState.selectionStartKind, modelState.selectionStartLeftoverVisibleColumns, viewPosition, modelState.leftoverVisibleColumns);\n        }\n        this.modelState = modelState;\n        this.viewState = viewState;\n        this._updateTrackedRange(context);\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,WAAW,EAAEC,iBAAiB,QAAQ,oBAAoB;AACnE,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,SAAS,QAAQ,sBAAsB;AAChD;AACA;AACA;AACA,OAAO,MAAMC,MAAM,CAAC;EAChBC,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACC,SAAS,CAACH,OAAO,EAAE,IAAIN,iBAAiB,CAAC,IAAIE,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,iCAAiC,CAAC,EAAE,IAAID,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,IAAID,iBAAiB,CAAC,IAAIE,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,iCAAiC,CAAC,EAAE,IAAID,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACjP;EACAS,OAAOA,CAACJ,OAAO,EAAE;IACb,IAAI,CAACK,mBAAmB,CAACL,OAAO,CAAC;EACrC;EACAM,sBAAsBA,CAACN,OAAO,EAAE;IAC5B,IAAI,CAACE,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACK,mBAAmB,CAACP,OAAO,CAAC;EACrC;EACAQ,qBAAqBA,CAACR,OAAO,EAAE;IAC3B,IAAI,CAACE,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACG,mBAAmB,CAACL,OAAO,CAAC;EACrC;EACAO,mBAAmBA,CAACP,OAAO,EAAE;IACzB,IAAI,CAAC,IAAI,CAACE,eAAe,EAAE;MACvB;MACA;IACJ;IACA,IAAI,CAACD,gBAAgB,GAAGD,OAAO,CAACS,KAAK,CAACC,gBAAgB,CAAC,IAAI,CAACT,gBAAgB,EAAE,IAAI,CAACU,UAAU,CAACC,SAAS,EAAE,CAAC,CAAC,yDAAyD,CAAC;EACzK;EACAP,mBAAmBA,CAACL,OAAO,EAAE;IACzB,IAAI,CAACC,gBAAgB,GAAGD,OAAO,CAACS,KAAK,CAACC,gBAAgB,CAAC,IAAI,CAACT,gBAAgB,EAAE,IAAI,EAAE,CAAC,CAAC,yDAAyD,CAAC;EACpJ;EACAY,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAIpB,WAAW,CAAC,IAAI,CAACkB,UAAU,EAAE,IAAI,CAACG,SAAS,CAAC;EAC3D;EACAC,wBAAwBA,CAACf,OAAO,EAAE;IAC9B,MAAMgB,KAAK,GAAGhB,OAAO,CAACS,KAAK,CAACQ,gBAAgB,CAAC,IAAI,CAAChB,gBAAgB,CAAC;IACnE,IAAI,IAAI,CAACU,UAAU,CAACC,SAAS,CAACM,OAAO,CAAC,CAAC,IAAI,CAACF,KAAK,CAACE,OAAO,CAAC,CAAC,EAAE;MACzD;MACA,OAAOrB,SAAS,CAACsB,SAAS,CAACH,KAAK,CAACI,aAAa,CAAC,CAAC,EAAE,IAAI,CAACT,UAAU,CAACC,SAAS,CAACS,YAAY,CAAC,CAAC,CAAC;IAC/F;IACA,OAAOxB,SAAS,CAACsB,SAAS,CAACH,KAAK,EAAE,IAAI,CAACL,UAAU,CAACC,SAAS,CAACS,YAAY,CAAC,CAAC,CAAC;EAC/E;EACAC,gBAAgBA,CAACtB,OAAO,EAAE;IACtB,IAAI,CAACG,SAAS,CAACH,OAAO,EAAE,IAAI,CAACW,UAAU,EAAE,IAAI,CAACG,SAAS,CAAC;EAC5D;EACAS,QAAQA,CAACvB,OAAO,EAAEW,UAAU,EAAEG,SAAS,EAAE;IACrC,IAAI,CAACX,SAAS,CAACH,OAAO,EAAEW,UAAU,EAAEG,SAAS,CAAC;EAClD;EACA,OAAOU,0BAA0BA,CAACC,SAAS,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,WAAW,EAAE;IAC5E,IAAIF,QAAQ,CAACG,MAAM,CAACF,UAAU,CAAC,EAAE;MAC7B,OAAOC,WAAW;IACtB;IACA,OAAOH,SAAS,CAACK,iBAAiB,CAACJ,QAAQ,EAAE,CAAC,CAAC,2BAA2B,CAAC;EAC/E;EACA,OAAOK,kBAAkBA,CAACN,SAAS,EAAEX,SAAS,EAAE;IAC5C,MAAMY,QAAQ,GAAGZ,SAAS,CAACY,QAAQ;IACnC,MAAMM,cAAc,GAAGlB,SAAS,CAACmB,cAAc,CAACC,gBAAgB,CAAC,CAAC;IAClE,MAAMC,YAAY,GAAGrB,SAAS,CAACmB,cAAc,CAACG,cAAc,CAAC,CAAC;IAC9D,MAAMC,aAAa,GAAGZ,SAAS,CAACK,iBAAiB,CAACJ,QAAQ,EAAE,CAAC,CAAC,2BAA2B,CAAC;IAC1F,MAAMY,mBAAmB,GAAG,IAAI,CAACd,0BAA0B,CAACC,SAAS,EAAEO,cAAc,EAAEN,QAAQ,EAAEW,aAAa,CAAC;IAC/G,MAAME,iBAAiB,GAAG,IAAI,CAACf,0BAA0B,CAACC,SAAS,EAAEU,YAAY,EAAEH,cAAc,EAAEM,mBAAmB,CAAC;IACvH,IAAIZ,QAAQ,CAACG,MAAM,CAACQ,aAAa,CAAC,IAAIL,cAAc,CAACH,MAAM,CAACS,mBAAmB,CAAC,IAAIH,YAAY,CAACN,MAAM,CAACU,iBAAiB,CAAC,EAAE;MACxH;MACA,OAAOzB,SAAS;IACpB;IACA,OAAO,IAAIpB,iBAAiB,CAACE,KAAK,CAAC4C,aAAa,CAACF,mBAAmB,EAAEC,iBAAiB,CAAC,EAAEzB,SAAS,CAAC2B,kBAAkB,EAAE3B,SAAS,CAAC4B,oCAAoC,GAAGV,cAAc,CAACW,MAAM,GAAGL,mBAAmB,CAACK,MAAM,EAAEN,aAAa,EAAEvB,SAAS,CAAC8B,sBAAsB,GAAGlB,QAAQ,CAACiB,MAAM,GAAGN,aAAa,CAACM,MAAM,CAAC;EAC1T;EACAxC,SAASA,CAACH,OAAO,EAAEW,UAAU,EAAEG,SAAS,EAAE;IACtC,IAAIA,SAAS,EAAE;MACXA,SAAS,GAAGhB,MAAM,CAACiC,kBAAkB,CAAC/B,OAAO,CAACyB,SAAS,EAAEX,SAAS,CAAC;IACvE;IACA,IAAI,CAACH,UAAU,EAAE;MACb,IAAI,CAACG,SAAS,EAAE;QACZ;MACJ;MACA;MACA,MAAMmB,cAAc,GAAGjC,OAAO,CAACS,KAAK,CAACoC,aAAa,CAAC7C,OAAO,CAAC8C,oBAAoB,CAACC,4BAA4B,CAACjC,SAAS,CAACmB,cAAc,CAAC,CAAC;MACvI,MAAMP,QAAQ,GAAG1B,OAAO,CAACS,KAAK,CAACuC,gBAAgB,CAAChD,OAAO,CAAC8C,oBAAoB,CAACG,kCAAkC,CAACnC,SAAS,CAACY,QAAQ,CAAC,CAAC;MACpIf,UAAU,GAAG,IAAIjB,iBAAiB,CAACuC,cAAc,EAAEnB,SAAS,CAAC2B,kBAAkB,EAAE3B,SAAS,CAAC4B,oCAAoC,EAAEhB,QAAQ,EAAEZ,SAAS,CAAC8B,sBAAsB,CAAC;IAChL,CAAC,MACI;MACD;MACA,MAAMX,cAAc,GAAGjC,OAAO,CAACS,KAAK,CAACoC,aAAa,CAAClC,UAAU,CAACsB,cAAc,CAAC;MAC7E,MAAMS,oCAAoC,GAAG/B,UAAU,CAACsB,cAAc,CAACiB,WAAW,CAACjB,cAAc,CAAC,GAAGtB,UAAU,CAAC+B,oCAAoC,GAAG,CAAC;MACxJ,MAAMhB,QAAQ,GAAG1B,OAAO,CAACS,KAAK,CAACuC,gBAAgB,CAACrC,UAAU,CAACe,QAAQ,CAAC;MACpE,MAAMkB,sBAAsB,GAAGjC,UAAU,CAACe,QAAQ,CAACG,MAAM,CAACH,QAAQ,CAAC,GAAGf,UAAU,CAACiC,sBAAsB,GAAG,CAAC;MAC3GjC,UAAU,GAAG,IAAIjB,iBAAiB,CAACuC,cAAc,EAAEtB,UAAU,CAAC8B,kBAAkB,EAAEC,oCAAoC,EAAEhB,QAAQ,EAAEkB,sBAAsB,CAAC;IAC7J;IACA,IAAI,CAAC9B,SAAS,EAAE;MACZ;MACA,MAAMqC,mBAAmB,GAAGnD,OAAO,CAAC8C,oBAAoB,CAACM,kCAAkC,CAAC,IAAIzD,QAAQ,CAACgB,UAAU,CAACsB,cAAc,CAACoB,eAAe,EAAE1C,UAAU,CAACsB,cAAc,CAACqB,WAAW,CAAC,CAAC;MAC3L,MAAMC,mBAAmB,GAAGvD,OAAO,CAAC8C,oBAAoB,CAACM,kCAAkC,CAAC,IAAIzD,QAAQ,CAACgB,UAAU,CAACsB,cAAc,CAACuB,aAAa,EAAE7C,UAAU,CAACsB,cAAc,CAACwB,SAAS,CAAC,CAAC;MACvL,MAAMC,kBAAkB,GAAG,IAAI9D,KAAK,CAACuD,mBAAmB,CAACQ,UAAU,EAAER,mBAAmB,CAACR,MAAM,EAAEY,mBAAmB,CAACI,UAAU,EAAEJ,mBAAmB,CAACZ,MAAM,CAAC;MAC5J,MAAMiB,YAAY,GAAG5D,OAAO,CAAC8C,oBAAoB,CAACM,kCAAkC,CAACzC,UAAU,CAACe,QAAQ,CAAC;MACzGZ,SAAS,GAAG,IAAIpB,iBAAiB,CAACgE,kBAAkB,EAAE/C,UAAU,CAAC8B,kBAAkB,EAAE9B,UAAU,CAAC+B,oCAAoC,EAAEkB,YAAY,EAAEjD,UAAU,CAACiC,sBAAsB,CAAC;IAC1L,CAAC,MACI;MACD;MACA,MAAMc,kBAAkB,GAAG1D,OAAO,CAAC8C,oBAAoB,CAACe,iBAAiB,CAAC/C,SAAS,CAACmB,cAAc,EAAEtB,UAAU,CAACsB,cAAc,CAAC;MAC9H,MAAM2B,YAAY,GAAG5D,OAAO,CAAC8C,oBAAoB,CAACgB,oBAAoB,CAAChD,SAAS,CAACY,QAAQ,EAAEf,UAAU,CAACe,QAAQ,CAAC;MAC/GZ,SAAS,GAAG,IAAIpB,iBAAiB,CAACgE,kBAAkB,EAAE/C,UAAU,CAAC8B,kBAAkB,EAAE9B,UAAU,CAAC+B,oCAAoC,EAAEkB,YAAY,EAAEjD,UAAU,CAACiC,sBAAsB,CAAC;IAC1L;IACA,IAAI,CAACjC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACG,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACP,mBAAmB,CAACP,OAAO,CAAC;EACrC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}