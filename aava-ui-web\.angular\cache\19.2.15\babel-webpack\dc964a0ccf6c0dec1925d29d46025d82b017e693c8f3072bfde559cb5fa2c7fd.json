{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = this && this.__param || function (paramIndex, decorator) {\n  return function (target, key) {\n    decorator(target, key, paramIndex);\n  };\n};\nvar InlineEditHintsContentWidget_1;\nimport { h } from '../../../../base/browser/dom.js';\nimport { KeybindingLabel, unthemedKeybindingLabelOptions } from '../../../../base/browser/ui/keybindingLabel/keybindingLabel.js';\nimport { Separator } from '../../../../base/common/actions.js';\nimport { equals } from '../../../../base/common/arrays.js';\nimport { Disposable, toDisposable } from '../../../../base/common/lifecycle.js';\nimport { autorun, autorunWithStore, derived, observableFromEvent } from '../../../../base/common/observable.js';\nimport { OS } from '../../../../base/common/platform.js';\nimport './inlineEditHintsWidget.css';\nimport { Position } from '../../../common/core/position.js';\nimport { MenuEntryActionViewItem, createAndFillInActionBarActions } from '../../../../platform/actions/browser/menuEntryActionViewItem.js';\nimport { WorkbenchToolBar } from '../../../../platform/actions/browser/toolbar.js';\nimport { IMenuService, MenuId, MenuItemAction } from '../../../../platform/actions/common/actions.js';\nimport { ICommandService } from '../../../../platform/commands/common/commands.js';\nimport { IContextKeyService } from '../../../../platform/contextkey/common/contextkey.js';\nimport { IContextMenuService } from '../../../../platform/contextview/browser/contextView.js';\nimport { IInstantiationService } from '../../../../platform/instantiation/common/instantiation.js';\nimport { IKeybindingService } from '../../../../platform/keybinding/common/keybinding.js';\nimport { ITelemetryService } from '../../../../platform/telemetry/common/telemetry.js';\nlet InlineEditHintsWidget = class InlineEditHintsWidget extends Disposable {\n  constructor(editor, model, instantiationService) {\n    super();\n    this.editor = editor;\n    this.model = model;\n    this.instantiationService = instantiationService;\n    this.alwaysShowToolbar = observableFromEvent(this, this.editor.onDidChangeConfiguration, () => this.editor.getOption(63 /* EditorOption.inlineEdit */).showToolbar === 'always');\n    this.sessionPosition = undefined;\n    this.position = derived(this, reader => {\n      const ghostText = this.model.read(reader)?.model.ghostText.read(reader);\n      if (!this.alwaysShowToolbar.read(reader) || !ghostText || ghostText.parts.length === 0) {\n        this.sessionPosition = undefined;\n        return null;\n      }\n      const firstColumn = ghostText.parts[0].column;\n      if (this.sessionPosition && this.sessionPosition.lineNumber !== ghostText.lineNumber) {\n        this.sessionPosition = undefined;\n      }\n      const position = new Position(ghostText.lineNumber, Math.min(firstColumn, this.sessionPosition?.column ?? Number.MAX_SAFE_INTEGER));\n      this.sessionPosition = position;\n      return position;\n    });\n    this._register(autorunWithStore((reader, store) => {\n      /** @description setup content widget */\n      const model = this.model.read(reader);\n      if (!model || !this.alwaysShowToolbar.read(reader)) {\n        return;\n      }\n      const contentWidget = store.add(this.instantiationService.createInstance(InlineEditHintsContentWidget, this.editor, true, this.position));\n      editor.addContentWidget(contentWidget);\n      store.add(toDisposable(() => editor.removeContentWidget(contentWidget)));\n    }));\n  }\n};\nInlineEditHintsWidget = __decorate([__param(2, IInstantiationService)], InlineEditHintsWidget);\nexport { InlineEditHintsWidget };\nlet InlineEditHintsContentWidget = /*#__PURE__*/(() => {\n  let InlineEditHintsContentWidget = class InlineEditHintsContentWidget extends Disposable {\n    static {\n      InlineEditHintsContentWidget_1 = this;\n    }\n    static {\n      this._dropDownVisible = false;\n    }\n    static {\n      this.id = 0;\n    }\n    constructor(editor, withBorder, _position, instantiationService, _contextKeyService, _menuService) {\n      super();\n      this.editor = editor;\n      this.withBorder = withBorder;\n      this._position = _position;\n      this._contextKeyService = _contextKeyService;\n      this._menuService = _menuService;\n      this.id = `InlineEditHintsContentWidget${InlineEditHintsContentWidget_1.id++}`;\n      this.allowEditorOverflow = true;\n      this.suppressMouseDown = false;\n      this.nodes = h('div.inlineEditHints', {\n        className: this.withBorder ? '.withBorder' : ''\n      }, [h('div@toolBar')]);\n      this.inlineCompletionsActionsMenus = this._register(this._menuService.createMenu(MenuId.InlineEditActions, this._contextKeyService));\n      this.toolBar = this._register(instantiationService.createInstance(CustomizedMenuWorkbenchToolBar, this.nodes.toolBar, this.editor, MenuId.InlineEditToolbar, {\n        menuOptions: {\n          renderShortTitle: true\n        },\n        toolbarOptions: {\n          primaryGroup: g => g.startsWith('primary')\n        },\n        actionViewItemProvider: (action, options) => {\n          if (action instanceof MenuItemAction) {\n            return instantiationService.createInstance(StatusBarViewItem, action, undefined);\n          }\n          return undefined;\n        },\n        telemetrySource: 'InlineEditToolbar'\n      }));\n      this._register(this.toolBar.onDidChangeDropdownVisibility(e => {\n        InlineEditHintsContentWidget_1._dropDownVisible = e;\n      }));\n      this._register(autorun(reader => {\n        /** @description update position */\n        this._position.read(reader);\n        this.editor.layoutContentWidget(this);\n      }));\n      this._register(autorun(reader => {\n        /** @description actions menu */\n        const extraActions = [];\n        for (const [_, group] of this.inlineCompletionsActionsMenus.getActions()) {\n          for (const action of group) {\n            if (action instanceof MenuItemAction) {\n              extraActions.push(action);\n            }\n          }\n        }\n        if (extraActions.length > 0) {\n          extraActions.unshift(new Separator());\n        }\n        this.toolBar.setAdditionalSecondaryActions(extraActions);\n      }));\n    }\n    getId() {\n      return this.id;\n    }\n    getDomNode() {\n      return this.nodes.root;\n    }\n    getPosition() {\n      return {\n        position: this._position.get(),\n        preference: [1 /* ContentWidgetPositionPreference.ABOVE */, 2 /* ContentWidgetPositionPreference.BELOW */],\n        positionAffinity: 3 /* PositionAffinity.LeftOfInjectedText */\n      };\n    }\n  };\n  return InlineEditHintsContentWidget;\n})();\nInlineEditHintsContentWidget = InlineEditHintsContentWidget_1 = __decorate([__param(3, IInstantiationService), __param(4, IContextKeyService), __param(5, IMenuService)], InlineEditHintsContentWidget);\nexport { InlineEditHintsContentWidget };\nclass StatusBarViewItem extends MenuEntryActionViewItem {\n  updateLabel() {\n    const kb = this._keybindingService.lookupKeybinding(this._action.id, this._contextKeyService);\n    if (!kb) {\n      return super.updateLabel();\n    }\n    if (this.label) {\n      const div = h('div.keybinding').root;\n      const k = this._register(new KeybindingLabel(div, OS, {\n        disableTitle: true,\n        ...unthemedKeybindingLabelOptions\n      }));\n      k.set(kb);\n      this.label.textContent = this._action.label;\n      this.label.appendChild(div);\n      this.label.classList.add('inlineEditStatusBarItemLabel');\n    }\n  }\n  updateTooltip() {\n    // NOOP, disable tooltip\n  }\n}\nlet CustomizedMenuWorkbenchToolBar = class CustomizedMenuWorkbenchToolBar extends WorkbenchToolBar {\n  constructor(container, editor, menuId, options2, menuService, contextKeyService, contextMenuService, keybindingService, commandService, telemetryService) {\n    super(container, {\n      resetMenu: menuId,\n      ...options2\n    }, menuService, contextKeyService, contextMenuService, keybindingService, commandService, telemetryService);\n    this.editor = editor;\n    this.menuId = menuId;\n    this.options2 = options2;\n    this.menuService = menuService;\n    this.contextKeyService = contextKeyService;\n    this.menu = this._store.add(this.menuService.createMenu(this.menuId, this.contextKeyService, {\n      emitEventsForSubmenuChanges: true\n    }));\n    this.additionalActions = [];\n    this.prependedPrimaryActions = [];\n    this._store.add(this.menu.onDidChange(() => this.updateToolbar()));\n    this._store.add(this.editor.onDidChangeCursorPosition(() => this.updateToolbar()));\n    this.updateToolbar();\n  }\n  updateToolbar() {\n    const primary = [];\n    const secondary = [];\n    createAndFillInActionBarActions(this.menu, this.options2?.menuOptions, {\n      primary,\n      secondary\n    }, this.options2?.toolbarOptions?.primaryGroup, this.options2?.toolbarOptions?.shouldInlineSubmenu, this.options2?.toolbarOptions?.useSeparatorsInPrimaryActions);\n    secondary.push(...this.additionalActions);\n    primary.unshift(...this.prependedPrimaryActions);\n    this.setActions(primary, secondary);\n  }\n  setAdditionalSecondaryActions(actions) {\n    if (equals(this.additionalActions, actions, (a, b) => a === b)) {\n      return;\n    }\n    this.additionalActions = actions;\n    this.updateToolbar();\n  }\n};\nCustomizedMenuWorkbenchToolBar = __decorate([__param(4, IMenuService), __param(5, IContextKeyService), __param(6, IContextMenuService), __param(7, IKeybindingService), __param(8, ICommandService), __param(9, ITelemetryService)], CustomizedMenuWorkbenchToolBar);\nexport { CustomizedMenuWorkbenchToolBar };", "map": {"version": 3, "names": ["__decorate", "decorators", "target", "key", "desc", "c", "arguments", "length", "r", "Object", "getOwnPropertyDescriptor", "d", "Reflect", "decorate", "i", "defineProperty", "__param", "paramIndex", "decorator", "InlineEditHintsContentWidget_1", "h", "KeybindingLabel", "unthemedKeybindingLabelOptions", "Separator", "equals", "Disposable", "toDisposable", "autorun", "autorunWithStore", "derived", "observableFromEvent", "OS", "Position", "MenuEntryActionViewItem", "createAndFillInActionBarActions", "WorkbenchToolBar", "IMenuService", "MenuId", "MenuItemAction", "ICommandService", "IContextKeyService", "IContextMenuService", "IInstantiationService", "IKeybindingService", "ITelemetryService", "InlineEditHintsWidget", "constructor", "editor", "model", "instantiationService", "alwaysShowToolbar", "onDidChangeConfiguration", "getOption", "showToolbar", "sessionPosition", "undefined", "position", "reader", "ghostText", "read", "parts", "firstColumn", "column", "lineNumber", "Math", "min", "Number", "MAX_SAFE_INTEGER", "_register", "store", "contentWidget", "add", "createInstance", "InlineEditHintsContentWidget", "addContentWidget", "removeContentWidget", "_dropDownVisible", "id", "withB<PERSON>er", "_position", "_contextKeyService", "_menuService", "allowEditorOverflow", "suppressMouseDown", "nodes", "className", "inlineCompletionsActionsMenus", "createMenu", "InlineEditActions", "toolBar", "CustomizedMenuWorkbenchToolBar", "InlineEditToolbar", "menuOptions", "renderShortTitle", "toolbarOptions", "primaryGroup", "g", "startsWith", "actionViewItemProvider", "action", "options", "StatusBarViewItem", "telemetrySource", "onDidChangeDropdownVisibility", "e", "layoutContentWidget", "extraActions", "_", "group", "getActions", "push", "unshift", "setAdditionalSecondaryActions", "getId", "getDomNode", "root", "getPosition", "get", "preference", "positionAffinity", "updateLabel", "kb", "_keybindingService", "lookupKeybinding", "_action", "label", "div", "k", "disable<PERSON><PERSON>le", "set", "textContent", "append<PERSON><PERSON><PERSON>", "classList", "updateTooltip", "container", "menuId", "options2", "menuService", "contextKeyService", "contextMenuService", "keybindingService", "commandService", "telemetryService", "resetMenu", "menu", "_store", "emitEventsForSubmenuChanges", "additionalActions", "prependedPrimaryActions", "onDidChange", "updateToolbar", "onDidChangeCursorPosition", "primary", "secondary", "shouldInlineSubmenu", "useSeparatorsInPrimaryActions", "setActions", "actions", "a", "b"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/contrib/inlineEdit/browser/inlineEditHintsWidget.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = (this && this.__param) || function (paramIndex, decorator) {\n    return function (target, key) { decorator(target, key, paramIndex); }\n};\nvar InlineEditHintsContentWidget_1;\nimport { h } from '../../../../base/browser/dom.js';\nimport { KeybindingLabel, unthemedKeybindingLabelOptions } from '../../../../base/browser/ui/keybindingLabel/keybindingLabel.js';\nimport { Separator } from '../../../../base/common/actions.js';\nimport { equals } from '../../../../base/common/arrays.js';\nimport { Disposable, toDisposable } from '../../../../base/common/lifecycle.js';\nimport { autorun, autorunWithStore, derived, observableFromEvent } from '../../../../base/common/observable.js';\nimport { OS } from '../../../../base/common/platform.js';\nimport './inlineEditHintsWidget.css';\nimport { Position } from '../../../common/core/position.js';\nimport { MenuEntryActionViewItem, createAndFillInActionBarActions } from '../../../../platform/actions/browser/menuEntryActionViewItem.js';\nimport { WorkbenchToolBar } from '../../../../platform/actions/browser/toolbar.js';\nimport { IMenuService, MenuId, MenuItemAction } from '../../../../platform/actions/common/actions.js';\nimport { ICommandService } from '../../../../platform/commands/common/commands.js';\nimport { IContextKeyService } from '../../../../platform/contextkey/common/contextkey.js';\nimport { IContextMenuService } from '../../../../platform/contextview/browser/contextView.js';\nimport { IInstantiationService } from '../../../../platform/instantiation/common/instantiation.js';\nimport { IKeybindingService } from '../../../../platform/keybinding/common/keybinding.js';\nimport { ITelemetryService } from '../../../../platform/telemetry/common/telemetry.js';\nlet InlineEditHintsWidget = class InlineEditHintsWidget extends Disposable {\n    constructor(editor, model, instantiationService) {\n        super();\n        this.editor = editor;\n        this.model = model;\n        this.instantiationService = instantiationService;\n        this.alwaysShowToolbar = observableFromEvent(this, this.editor.onDidChangeConfiguration, () => this.editor.getOption(63 /* EditorOption.inlineEdit */).showToolbar === 'always');\n        this.sessionPosition = undefined;\n        this.position = derived(this, reader => {\n            const ghostText = this.model.read(reader)?.model.ghostText.read(reader);\n            if (!this.alwaysShowToolbar.read(reader) || !ghostText || ghostText.parts.length === 0) {\n                this.sessionPosition = undefined;\n                return null;\n            }\n            const firstColumn = ghostText.parts[0].column;\n            if (this.sessionPosition && this.sessionPosition.lineNumber !== ghostText.lineNumber) {\n                this.sessionPosition = undefined;\n            }\n            const position = new Position(ghostText.lineNumber, Math.min(firstColumn, this.sessionPosition?.column ?? Number.MAX_SAFE_INTEGER));\n            this.sessionPosition = position;\n            return position;\n        });\n        this._register(autorunWithStore((reader, store) => {\n            /** @description setup content widget */\n            const model = this.model.read(reader);\n            if (!model || !this.alwaysShowToolbar.read(reader)) {\n                return;\n            }\n            const contentWidget = store.add(this.instantiationService.createInstance(InlineEditHintsContentWidget, this.editor, true, this.position));\n            editor.addContentWidget(contentWidget);\n            store.add(toDisposable(() => editor.removeContentWidget(contentWidget)));\n        }));\n    }\n};\nInlineEditHintsWidget = __decorate([\n    __param(2, IInstantiationService)\n], InlineEditHintsWidget);\nexport { InlineEditHintsWidget };\nlet InlineEditHintsContentWidget = class InlineEditHintsContentWidget extends Disposable {\n    static { InlineEditHintsContentWidget_1 = this; }\n    static { this._dropDownVisible = false; }\n    static { this.id = 0; }\n    constructor(editor, withBorder, _position, instantiationService, _contextKeyService, _menuService) {\n        super();\n        this.editor = editor;\n        this.withBorder = withBorder;\n        this._position = _position;\n        this._contextKeyService = _contextKeyService;\n        this._menuService = _menuService;\n        this.id = `InlineEditHintsContentWidget${InlineEditHintsContentWidget_1.id++}`;\n        this.allowEditorOverflow = true;\n        this.suppressMouseDown = false;\n        this.nodes = h('div.inlineEditHints', { className: this.withBorder ? '.withBorder' : '' }, [\n            h('div@toolBar'),\n        ]);\n        this.inlineCompletionsActionsMenus = this._register(this._menuService.createMenu(MenuId.InlineEditActions, this._contextKeyService));\n        this.toolBar = this._register(instantiationService.createInstance(CustomizedMenuWorkbenchToolBar, this.nodes.toolBar, this.editor, MenuId.InlineEditToolbar, {\n            menuOptions: { renderShortTitle: true },\n            toolbarOptions: { primaryGroup: g => g.startsWith('primary') },\n            actionViewItemProvider: (action, options) => {\n                if (action instanceof MenuItemAction) {\n                    return instantiationService.createInstance(StatusBarViewItem, action, undefined);\n                }\n                return undefined;\n            },\n            telemetrySource: 'InlineEditToolbar',\n        }));\n        this._register(this.toolBar.onDidChangeDropdownVisibility(e => {\n            InlineEditHintsContentWidget_1._dropDownVisible = e;\n        }));\n        this._register(autorun(reader => {\n            /** @description update position */\n            this._position.read(reader);\n            this.editor.layoutContentWidget(this);\n        }));\n        this._register(autorun(reader => {\n            /** @description actions menu */\n            const extraActions = [];\n            for (const [_, group] of this.inlineCompletionsActionsMenus.getActions()) {\n                for (const action of group) {\n                    if (action instanceof MenuItemAction) {\n                        extraActions.push(action);\n                    }\n                }\n            }\n            if (extraActions.length > 0) {\n                extraActions.unshift(new Separator());\n            }\n            this.toolBar.setAdditionalSecondaryActions(extraActions);\n        }));\n    }\n    getId() { return this.id; }\n    getDomNode() {\n        return this.nodes.root;\n    }\n    getPosition() {\n        return {\n            position: this._position.get(),\n            preference: [1 /* ContentWidgetPositionPreference.ABOVE */, 2 /* ContentWidgetPositionPreference.BELOW */],\n            positionAffinity: 3 /* PositionAffinity.LeftOfInjectedText */,\n        };\n    }\n};\nInlineEditHintsContentWidget = InlineEditHintsContentWidget_1 = __decorate([\n    __param(3, IInstantiationService),\n    __param(4, IContextKeyService),\n    __param(5, IMenuService)\n], InlineEditHintsContentWidget);\nexport { InlineEditHintsContentWidget };\nclass StatusBarViewItem extends MenuEntryActionViewItem {\n    updateLabel() {\n        const kb = this._keybindingService.lookupKeybinding(this._action.id, this._contextKeyService);\n        if (!kb) {\n            return super.updateLabel();\n        }\n        if (this.label) {\n            const div = h('div.keybinding').root;\n            const k = this._register(new KeybindingLabel(div, OS, { disableTitle: true, ...unthemedKeybindingLabelOptions }));\n            k.set(kb);\n            this.label.textContent = this._action.label;\n            this.label.appendChild(div);\n            this.label.classList.add('inlineEditStatusBarItemLabel');\n        }\n    }\n    updateTooltip() {\n        // NOOP, disable tooltip\n    }\n}\nlet CustomizedMenuWorkbenchToolBar = class CustomizedMenuWorkbenchToolBar extends WorkbenchToolBar {\n    constructor(container, editor, menuId, options2, menuService, contextKeyService, contextMenuService, keybindingService, commandService, telemetryService) {\n        super(container, { resetMenu: menuId, ...options2 }, menuService, contextKeyService, contextMenuService, keybindingService, commandService, telemetryService);\n        this.editor = editor;\n        this.menuId = menuId;\n        this.options2 = options2;\n        this.menuService = menuService;\n        this.contextKeyService = contextKeyService;\n        this.menu = this._store.add(this.menuService.createMenu(this.menuId, this.contextKeyService, { emitEventsForSubmenuChanges: true }));\n        this.additionalActions = [];\n        this.prependedPrimaryActions = [];\n        this._store.add(this.menu.onDidChange(() => this.updateToolbar()));\n        this._store.add(this.editor.onDidChangeCursorPosition(() => this.updateToolbar()));\n        this.updateToolbar();\n    }\n    updateToolbar() {\n        const primary = [];\n        const secondary = [];\n        createAndFillInActionBarActions(this.menu, this.options2?.menuOptions, { primary, secondary }, this.options2?.toolbarOptions?.primaryGroup, this.options2?.toolbarOptions?.shouldInlineSubmenu, this.options2?.toolbarOptions?.useSeparatorsInPrimaryActions);\n        secondary.push(...this.additionalActions);\n        primary.unshift(...this.prependedPrimaryActions);\n        this.setActions(primary, secondary);\n    }\n    setAdditionalSecondaryActions(actions) {\n        if (equals(this.additionalActions, actions, (a, b) => a === b)) {\n            return;\n        }\n        this.additionalActions = actions;\n        this.updateToolbar();\n    }\n};\nCustomizedMenuWorkbenchToolBar = __decorate([\n    __param(4, IMenuService),\n    __param(5, IContextKeyService),\n    __param(6, IContextMenuService),\n    __param(7, IKeybindingService),\n    __param(8, ICommandService),\n    __param(9, ITelemetryService)\n], CustomizedMenuWorkbenchToolBar);\nexport { CustomizedMenuWorkbenchToolBar };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,IAAIA,UAAU,GAAI,IAAI,IAAI,IAAI,CAACA,UAAU,IAAK,UAAUC,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAE;EACnF,IAAIC,CAAC,GAAGC,SAAS,CAACC,MAAM;IAAEC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGH,MAAM,GAAGE,IAAI,KAAK,IAAI,GAAGA,IAAI,GAAGK,MAAM,CAACC,wBAAwB,CAACR,MAAM,EAAEC,GAAG,CAAC,GAAGC,IAAI;IAAEO,CAAC;EAC5H,IAAI,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,CAACC,QAAQ,KAAK,UAAU,EAAEL,CAAC,GAAGI,OAAO,CAACC,QAAQ,CAACZ,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,CAAC,CAAC,KAC1H,KAAK,IAAIU,CAAC,GAAGb,UAAU,CAACM,MAAM,GAAG,CAAC,EAAEO,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE,IAAIH,CAAC,GAAGV,UAAU,CAACa,CAAC,CAAC,EAAEN,CAAC,GAAG,CAACH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACH,CAAC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACT,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,GAAGG,CAAC,CAACT,MAAM,EAAEC,GAAG,CAAC,KAAKK,CAAC;EACjJ,OAAOH,CAAC,GAAG,CAAC,IAAIG,CAAC,IAAIC,MAAM,CAACM,cAAc,CAACb,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,EAAEA,CAAC;AACjE,CAAC;AACD,IAAIQ,OAAO,GAAI,IAAI,IAAI,IAAI,CAACA,OAAO,IAAK,UAAUC,UAAU,EAAEC,SAAS,EAAE;EACrE,OAAO,UAAUhB,MAAM,EAAEC,GAAG,EAAE;IAAEe,SAAS,CAAChB,MAAM,EAAEC,GAAG,EAAEc,UAAU,CAAC;EAAE,CAAC;AACzE,CAAC;AACD,IAAIE,8BAA8B;AAClC,SAASC,CAAC,QAAQ,iCAAiC;AACnD,SAASC,eAAe,EAAEC,8BAA8B,QAAQ,gEAAgE;AAChI,SAASC,SAAS,QAAQ,oCAAoC;AAC9D,SAASC,MAAM,QAAQ,mCAAmC;AAC1D,SAASC,UAAU,EAAEC,YAAY,QAAQ,sCAAsC;AAC/E,SAASC,OAAO,EAAEC,gBAAgB,EAAEC,OAAO,EAAEC,mBAAmB,QAAQ,uCAAuC;AAC/G,SAASC,EAAE,QAAQ,qCAAqC;AACxD,OAAO,6BAA6B;AACpC,SAASC,QAAQ,QAAQ,kCAAkC;AAC3D,SAASC,uBAAuB,EAAEC,+BAA+B,QAAQ,iEAAiE;AAC1I,SAASC,gBAAgB,QAAQ,iDAAiD;AAClF,SAASC,YAAY,EAAEC,MAAM,EAAEC,cAAc,QAAQ,gDAAgD;AACrG,SAASC,eAAe,QAAQ,kDAAkD;AAClF,SAASC,kBAAkB,QAAQ,sDAAsD;AACzF,SAASC,mBAAmB,QAAQ,yDAAyD;AAC7F,SAASC,qBAAqB,QAAQ,4DAA4D;AAClG,SAASC,kBAAkB,QAAQ,sDAAsD;AACzF,SAASC,iBAAiB,QAAQ,oDAAoD;AACtF,IAAIC,qBAAqB,GAAG,MAAMA,qBAAqB,SAASpB,UAAU,CAAC;EACvEqB,WAAWA,CAACC,MAAM,EAAEC,KAAK,EAAEC,oBAAoB,EAAE;IAC7C,KAAK,CAAC,CAAC;IACP,IAAI,CAACF,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,oBAAoB,GAAGA,oBAAoB;IAChD,IAAI,CAACC,iBAAiB,GAAGpB,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAACiB,MAAM,CAACI,wBAAwB,EAAE,MAAM,IAAI,CAACJ,MAAM,CAACK,SAAS,CAAC,EAAE,CAAC,6BAA6B,CAAC,CAACC,WAAW,KAAK,QAAQ,CAAC;IAChL,IAAI,CAACC,eAAe,GAAGC,SAAS;IAChC,IAAI,CAACC,QAAQ,GAAG3B,OAAO,CAAC,IAAI,EAAE4B,MAAM,IAAI;MACpC,MAAMC,SAAS,GAAG,IAAI,CAACV,KAAK,CAACW,IAAI,CAACF,MAAM,CAAC,EAAET,KAAK,CAACU,SAAS,CAACC,IAAI,CAACF,MAAM,CAAC;MACvE,IAAI,CAAC,IAAI,CAACP,iBAAiB,CAACS,IAAI,CAACF,MAAM,CAAC,IAAI,CAACC,SAAS,IAAIA,SAAS,CAACE,KAAK,CAACrD,MAAM,KAAK,CAAC,EAAE;QACpF,IAAI,CAAC+C,eAAe,GAAGC,SAAS;QAChC,OAAO,IAAI;MACf;MACA,MAAMM,WAAW,GAAGH,SAAS,CAACE,KAAK,CAAC,CAAC,CAAC,CAACE,MAAM;MAC7C,IAAI,IAAI,CAACR,eAAe,IAAI,IAAI,CAACA,eAAe,CAACS,UAAU,KAAKL,SAAS,CAACK,UAAU,EAAE;QAClF,IAAI,CAACT,eAAe,GAAGC,SAAS;MACpC;MACA,MAAMC,QAAQ,GAAG,IAAIxB,QAAQ,CAAC0B,SAAS,CAACK,UAAU,EAAEC,IAAI,CAACC,GAAG,CAACJ,WAAW,EAAE,IAAI,CAACP,eAAe,EAAEQ,MAAM,IAAII,MAAM,CAACC,gBAAgB,CAAC,CAAC;MACnI,IAAI,CAACb,eAAe,GAAGE,QAAQ;MAC/B,OAAOA,QAAQ;IACnB,CAAC,CAAC;IACF,IAAI,CAACY,SAAS,CAACxC,gBAAgB,CAAC,CAAC6B,MAAM,EAAEY,KAAK,KAAK;MAC/C;MACA,MAAMrB,KAAK,GAAG,IAAI,CAACA,KAAK,CAACW,IAAI,CAACF,MAAM,CAAC;MACrC,IAAI,CAACT,KAAK,IAAI,CAAC,IAAI,CAACE,iBAAiB,CAACS,IAAI,CAACF,MAAM,CAAC,EAAE;QAChD;MACJ;MACA,MAAMa,aAAa,GAAGD,KAAK,CAACE,GAAG,CAAC,IAAI,CAACtB,oBAAoB,CAACuB,cAAc,CAACC,4BAA4B,EAAE,IAAI,CAAC1B,MAAM,EAAE,IAAI,EAAE,IAAI,CAACS,QAAQ,CAAC,CAAC;MACzIT,MAAM,CAAC2B,gBAAgB,CAACJ,aAAa,CAAC;MACtCD,KAAK,CAACE,GAAG,CAAC7C,YAAY,CAAC,MAAMqB,MAAM,CAAC4B,mBAAmB,CAACL,aAAa,CAAC,CAAC,CAAC;IAC5E,CAAC,CAAC,CAAC;EACP;AACJ,CAAC;AACDzB,qBAAqB,GAAG7C,UAAU,CAAC,CAC/BgB,OAAO,CAAC,CAAC,EAAE0B,qBAAqB,CAAC,CACpC,EAAEG,qBAAqB,CAAC;AACzB,SAASA,qBAAqB;AAC9B,IAAI4B,4BAA4B;EAAA,IAA5BA,4BAA4B,GAAG,MAAMA,4BAA4B,SAAShD,UAAU,CAAC;IACrF;MAASN,8BAA8B,GAAG,IAAI;IAAE;IAChD;MAAS,IAAI,CAACyD,gBAAgB,GAAG,KAAK;IAAE;IACxC;MAAS,IAAI,CAACC,EAAE,GAAG,CAAC;IAAE;IACtB/B,WAAWA,CAACC,MAAM,EAAE+B,UAAU,EAAEC,SAAS,EAAE9B,oBAAoB,EAAE+B,kBAAkB,EAAEC,YAAY,EAAE;MAC/F,KAAK,CAAC,CAAC;MACP,IAAI,CAAClC,MAAM,GAAGA,MAAM;MACpB,IAAI,CAAC+B,UAAU,GAAGA,UAAU;MAC5B,IAAI,CAACC,SAAS,GAAGA,SAAS;MAC1B,IAAI,CAACC,kBAAkB,GAAGA,kBAAkB;MAC5C,IAAI,CAACC,YAAY,GAAGA,YAAY;MAChC,IAAI,CAACJ,EAAE,GAAG,+BAA+B1D,8BAA8B,CAAC0D,EAAE,EAAE,EAAE;MAC9E,IAAI,CAACK,mBAAmB,GAAG,IAAI;MAC/B,IAAI,CAACC,iBAAiB,GAAG,KAAK;MAC9B,IAAI,CAACC,KAAK,GAAGhE,CAAC,CAAC,qBAAqB,EAAE;QAAEiE,SAAS,EAAE,IAAI,CAACP,UAAU,GAAG,aAAa,GAAG;MAAG,CAAC,EAAE,CACvF1D,CAAC,CAAC,aAAa,CAAC,CACnB,CAAC;MACF,IAAI,CAACkE,6BAA6B,GAAG,IAAI,CAAClB,SAAS,CAAC,IAAI,CAACa,YAAY,CAACM,UAAU,CAAClD,MAAM,CAACmD,iBAAiB,EAAE,IAAI,CAACR,kBAAkB,CAAC,CAAC;MACpI,IAAI,CAACS,OAAO,GAAG,IAAI,CAACrB,SAAS,CAACnB,oBAAoB,CAACuB,cAAc,CAACkB,8BAA8B,EAAE,IAAI,CAACN,KAAK,CAACK,OAAO,EAAE,IAAI,CAAC1C,MAAM,EAAEV,MAAM,CAACsD,iBAAiB,EAAE;QACzJC,WAAW,EAAE;UAAEC,gBAAgB,EAAE;QAAK,CAAC;QACvCC,cAAc,EAAE;UAAEC,YAAY,EAAEC,CAAC,IAAIA,CAAC,CAACC,UAAU,CAAC,SAAS;QAAE,CAAC;QAC9DC,sBAAsB,EAAEA,CAACC,MAAM,EAAEC,OAAO,KAAK;UACzC,IAAID,MAAM,YAAY7D,cAAc,EAAE;YAClC,OAAOW,oBAAoB,CAACuB,cAAc,CAAC6B,iBAAiB,EAAEF,MAAM,EAAE5C,SAAS,CAAC;UACpF;UACA,OAAOA,SAAS;QACpB,CAAC;QACD+C,eAAe,EAAE;MACrB,CAAC,CAAC,CAAC;MACH,IAAI,CAAClC,SAAS,CAAC,IAAI,CAACqB,OAAO,CAACc,6BAA6B,CAACC,CAAC,IAAI;QAC3DrF,8BAA8B,CAACyD,gBAAgB,GAAG4B,CAAC;MACvD,CAAC,CAAC,CAAC;MACH,IAAI,CAACpC,SAAS,CAACzC,OAAO,CAAC8B,MAAM,IAAI;QAC7B;QACA,IAAI,CAACsB,SAAS,CAACpB,IAAI,CAACF,MAAM,CAAC;QAC3B,IAAI,CAACV,MAAM,CAAC0D,mBAAmB,CAAC,IAAI,CAAC;MACzC,CAAC,CAAC,CAAC;MACH,IAAI,CAACrC,SAAS,CAACzC,OAAO,CAAC8B,MAAM,IAAI;QAC7B;QACA,MAAMiD,YAAY,GAAG,EAAE;QACvB,KAAK,MAAM,CAACC,CAAC,EAAEC,KAAK,CAAC,IAAI,IAAI,CAACtB,6BAA6B,CAACuB,UAAU,CAAC,CAAC,EAAE;UACtE,KAAK,MAAMV,MAAM,IAAIS,KAAK,EAAE;YACxB,IAAIT,MAAM,YAAY7D,cAAc,EAAE;cAClCoE,YAAY,CAACI,IAAI,CAACX,MAAM,CAAC;YAC7B;UACJ;QACJ;QACA,IAAIO,YAAY,CAACnG,MAAM,GAAG,CAAC,EAAE;UACzBmG,YAAY,CAACK,OAAO,CAAC,IAAIxF,SAAS,CAAC,CAAC,CAAC;QACzC;QACA,IAAI,CAACkE,OAAO,CAACuB,6BAA6B,CAACN,YAAY,CAAC;MAC5D,CAAC,CAAC,CAAC;IACP;IACAO,KAAKA,CAAA,EAAG;MAAE,OAAO,IAAI,CAACpC,EAAE;IAAE;IAC1BqC,UAAUA,CAAA,EAAG;MACT,OAAO,IAAI,CAAC9B,KAAK,CAAC+B,IAAI;IAC1B;IACAC,WAAWA,CAAA,EAAG;MACV,OAAO;QACH5D,QAAQ,EAAE,IAAI,CAACuB,SAAS,CAACsC,GAAG,CAAC,CAAC;QAC9BC,UAAU,EAAE,CAAC,CAAC,CAAC,6CAA6C,CAAC,CAAC,4CAA4C;QAC1GC,gBAAgB,EAAE,CAAC,CAAC;MACxB,CAAC;IACL;EACJ,CAAC;EAAA,OAhEG9C,4BAA4B;AAAA,IAgE/B;AACDA,4BAA4B,GAAGtD,8BAA8B,GAAGnB,UAAU,CAAC,CACvEgB,OAAO,CAAC,CAAC,EAAE0B,qBAAqB,CAAC,EACjC1B,OAAO,CAAC,CAAC,EAAEwB,kBAAkB,CAAC,EAC9BxB,OAAO,CAAC,CAAC,EAAEoB,YAAY,CAAC,CAC3B,EAAEqC,4BAA4B,CAAC;AAChC,SAASA,4BAA4B;AACrC,MAAM4B,iBAAiB,SAASpE,uBAAuB,CAAC;EACpDuF,WAAWA,CAAA,EAAG;IACV,MAAMC,EAAE,GAAG,IAAI,CAACC,kBAAkB,CAACC,gBAAgB,CAAC,IAAI,CAACC,OAAO,CAAC/C,EAAE,EAAE,IAAI,CAACG,kBAAkB,CAAC;IAC7F,IAAI,CAACyC,EAAE,EAAE;MACL,OAAO,KAAK,CAACD,WAAW,CAAC,CAAC;IAC9B;IACA,IAAI,IAAI,CAACK,KAAK,EAAE;MACZ,MAAMC,GAAG,GAAG1G,CAAC,CAAC,gBAAgB,CAAC,CAAC+F,IAAI;MACpC,MAAMY,CAAC,GAAG,IAAI,CAAC3D,SAAS,CAAC,IAAI/C,eAAe,CAACyG,GAAG,EAAE/F,EAAE,EAAE;QAAEiG,YAAY,EAAE,IAAI;QAAE,GAAG1G;MAA+B,CAAC,CAAC,CAAC;MACjHyG,CAAC,CAACE,GAAG,CAACR,EAAE,CAAC;MACT,IAAI,CAACI,KAAK,CAACK,WAAW,GAAG,IAAI,CAACN,OAAO,CAACC,KAAK;MAC3C,IAAI,CAACA,KAAK,CAACM,WAAW,CAACL,GAAG,CAAC;MAC3B,IAAI,CAACD,KAAK,CAACO,SAAS,CAAC7D,GAAG,CAAC,8BAA8B,CAAC;IAC5D;EACJ;EACA8D,aAAaA,CAAA,EAAG;IACZ;EAAA;AAER;AACA,IAAI3C,8BAA8B,GAAG,MAAMA,8BAA8B,SAASvD,gBAAgB,CAAC;EAC/FW,WAAWA,CAACwF,SAAS,EAAEvF,MAAM,EAAEwF,MAAM,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,iBAAiB,EAAEC,kBAAkB,EAAEC,iBAAiB,EAAEC,cAAc,EAAEC,gBAAgB,EAAE;IACtJ,KAAK,CAACR,SAAS,EAAE;MAAES,SAAS,EAAER,MAAM;MAAE,GAAGC;IAAS,CAAC,EAAEC,WAAW,EAAEC,iBAAiB,EAAEC,kBAAkB,EAAEC,iBAAiB,EAAEC,cAAc,EAAEC,gBAAgB,CAAC;IAC7J,IAAI,CAAC/F,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACwF,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACM,IAAI,GAAG,IAAI,CAACC,MAAM,CAAC1E,GAAG,CAAC,IAAI,CAACkE,WAAW,CAAClD,UAAU,CAAC,IAAI,CAACgD,MAAM,EAAE,IAAI,CAACG,iBAAiB,EAAE;MAAEQ,2BAA2B,EAAE;IAAK,CAAC,CAAC,CAAC;IACpI,IAAI,CAACC,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACC,uBAAuB,GAAG,EAAE;IACjC,IAAI,CAACH,MAAM,CAAC1E,GAAG,CAAC,IAAI,CAACyE,IAAI,CAACK,WAAW,CAAC,MAAM,IAAI,CAACC,aAAa,CAAC,CAAC,CAAC,CAAC;IAClE,IAAI,CAACL,MAAM,CAAC1E,GAAG,CAAC,IAAI,CAACxB,MAAM,CAACwG,yBAAyB,CAAC,MAAM,IAAI,CAACD,aAAa,CAAC,CAAC,CAAC,CAAC;IAClF,IAAI,CAACA,aAAa,CAAC,CAAC;EACxB;EACAA,aAAaA,CAAA,EAAG;IACZ,MAAME,OAAO,GAAG,EAAE;IAClB,MAAMC,SAAS,GAAG,EAAE;IACpBvH,+BAA+B,CAAC,IAAI,CAAC8G,IAAI,EAAE,IAAI,CAACR,QAAQ,EAAE5C,WAAW,EAAE;MAAE4D,OAAO;MAAEC;IAAU,CAAC,EAAE,IAAI,CAACjB,QAAQ,EAAE1C,cAAc,EAAEC,YAAY,EAAE,IAAI,CAACyC,QAAQ,EAAE1C,cAAc,EAAE4D,mBAAmB,EAAE,IAAI,CAAClB,QAAQ,EAAE1C,cAAc,EAAE6D,6BAA6B,CAAC;IAC7PF,SAAS,CAAC3C,IAAI,CAAC,GAAG,IAAI,CAACqC,iBAAiB,CAAC;IACzCK,OAAO,CAACzC,OAAO,CAAC,GAAG,IAAI,CAACqC,uBAAuB,CAAC;IAChD,IAAI,CAACQ,UAAU,CAACJ,OAAO,EAAEC,SAAS,CAAC;EACvC;EACAzC,6BAA6BA,CAAC6C,OAAO,EAAE;IACnC,IAAIrI,MAAM,CAAC,IAAI,CAAC2H,iBAAiB,EAAEU,OAAO,EAAE,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,KAAKC,CAAC,CAAC,EAAE;MAC5D;IACJ;IACA,IAAI,CAACZ,iBAAiB,GAAGU,OAAO;IAChC,IAAI,CAACP,aAAa,CAAC,CAAC;EACxB;AACJ,CAAC;AACD5D,8BAA8B,GAAG1F,UAAU,CAAC,CACxCgB,OAAO,CAAC,CAAC,EAAEoB,YAAY,CAAC,EACxBpB,OAAO,CAAC,CAAC,EAAEwB,kBAAkB,CAAC,EAC9BxB,OAAO,CAAC,CAAC,EAAEyB,mBAAmB,CAAC,EAC/BzB,OAAO,CAAC,CAAC,EAAE2B,kBAAkB,CAAC,EAC9B3B,OAAO,CAAC,CAAC,EAAEuB,eAAe,CAAC,EAC3BvB,OAAO,CAAC,CAAC,EAAE4B,iBAAiB,CAAC,CAChC,EAAE8C,8BAA8B,CAAC;AAClC,SAASA,8BAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}