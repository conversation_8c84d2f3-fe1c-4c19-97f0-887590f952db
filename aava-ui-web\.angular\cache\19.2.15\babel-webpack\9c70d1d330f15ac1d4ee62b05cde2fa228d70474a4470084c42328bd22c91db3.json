{"ast": null, "code": "export default function* () {\n  for (var groups = this._groups, j = 0, m = groups.length; j < m; ++j) {\n    for (var group = groups[j], i = 0, n = group.length, node; i < n; ++i) {\n      if (node = group[i]) yield node;\n    }\n  }\n}", "map": {"version": 3, "names": ["groups", "_groups", "j", "m", "length", "group", "i", "n", "node"], "sources": ["C:/console/aava-ui-web/node_modules/d3-selection/src/selection/iterator.js"], "sourcesContent": ["export default function*() {\n  for (var groups = this._groups, j = 0, m = groups.length; j < m; ++j) {\n    for (var group = groups[j], i = 0, n = group.length, node; i < n; ++i) {\n      if (node = group[i]) yield node;\n    }\n  }\n}\n"], "mappings": "AAAA,eAAe,aAAY;EACzB,KAAK,IAAIA,MAAM,GAAG,IAAI,CAACC,OAAO,EAAEC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGH,MAAM,CAACI,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;IACpE,KAAK,IAAIG,KAAK,GAAGL,MAAM,CAACE,CAAC,CAAC,EAAEI,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGF,KAAK,CAACD,MAAM,EAAEI,IAAI,EAAEF,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;MACrE,IAAIE,IAAI,GAAGH,KAAK,CAACC,CAAC,CAAC,EAAE,MAAME,IAAI;IACjC;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}