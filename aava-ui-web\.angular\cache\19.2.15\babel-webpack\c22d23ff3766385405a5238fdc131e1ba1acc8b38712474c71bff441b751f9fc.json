{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport const inlineEditAcceptId = 'editor.action.inlineEdit.accept';\nexport const inlineEditRejectId = 'editor.action.inlineEdit.reject';\nexport const inlineEditJumpToId = 'editor.action.inlineEdit.jumpTo';\nexport const inlineEditJumpBackId = 'editor.action.inlineEdit.jumpBack';", "map": {"version": 3, "names": ["inlineEditAcceptId", "inlineEditRejectId", "inlineEditJumpToId", "inlineEditJumpBackId"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/contrib/inlineEdit/browser/commandIds.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport const inlineEditAcceptId = 'editor.action.inlineEdit.accept';\nexport const inlineEditRejectId = 'editor.action.inlineEdit.reject';\nexport const inlineEditJumpToId = 'editor.action.inlineEdit.jumpTo';\nexport const inlineEditJumpBackId = 'editor.action.inlineEdit.jumpBack';\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,MAAMA,kBAAkB,GAAG,iCAAiC;AACnE,OAAO,MAAMC,kBAAkB,GAAG,iCAAiC;AACnE,OAAO,MAAMC,kBAAkB,GAAG,iCAAiC;AACnE,OAAO,MAAMC,oBAAoB,GAAG,mCAAmC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}