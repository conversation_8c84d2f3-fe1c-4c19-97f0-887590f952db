{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport let TokenStorageService = /*#__PURE__*/(() => {\n  class TokenStorageService {\n    ACCESS_TOKEN_KEY = 'access_token';\n    REFRESH_TOKEN_KEY = 'refresh_token';\n    DA_NAME_KEY = 'da_name';\n    DA_USERNAME_KEY = 'da_username';\n    DA_ID_TOKEN = 'id_token';\n    LOGIN_TYPE_KEY = 'login_type';\n    getCurrentDomain() {\n      return window.location.hostname;\n    }\n    storeLoginType(loginType) {\n      this.setCookie(this.LOGIN_TYPE_KEY, loginType);\n    }\n    getLoginType() {\n      const loginType = this.getCookie(this.LOGIN_TYPE_KEY);\n      return loginType === 'sso' || loginType === 'basic' ? loginType : null;\n    }\n    setCookie(cname, cvalue, exseconds) {\n      const d = new Date();\n      let expires = \"\";\n      if (exseconds) {\n        d.setTime(d.getTime() + exseconds * 1000);\n        expires = \"expires=\" + d.toUTCString();\n      }\n      let secureFlag = window.location.protocol === \"https:\" ? \";Secure\" : \"\";\n      let sameSiteFlag = \";SameSite=Strict\";\n      let domainFlag = `;domain=${this.getCurrentDomain()}`;\n      document.cookie = `${cname}=${cvalue};${expires};path=/;${secureFlag}${sameSiteFlag}${domainFlag}`;\n    }\n    getCookie(name) {\n      const match = document.cookie.match(new RegExp(`(^| )${name}=([^;]+)`));\n      return match ? decodeURIComponent(match[2]) : null;\n    }\n    deleteCookie(name) {\n      let domainFlag = `;domain=${this.getCurrentDomain()}`;\n      document.cookie = `${name}=; path=/; max-age=0; Secure; SameSite=Strict${domainFlag}`;\n    }\n    storeTokens(accessToken, refreshToken, expiresInSeconds) {\n      this.setCookie(this.ACCESS_TOKEN_KEY, accessToken, expiresInSeconds);\n      this.setCookie(this.REFRESH_TOKEN_KEY, refreshToken);\n    }\n    storeAccessToken(accessToken, expiresInSeconds) {\n      this.setCookie(this.ACCESS_TOKEN_KEY, accessToken, expiresInSeconds);\n    }\n    storeDaInfo(daName, daUsername, idToken) {\n      this.setCookie(this.DA_NAME_KEY, daName);\n      this.setCookie(this.DA_USERNAME_KEY, daUsername);\n      this.setCookie(this.DA_ID_TOKEN, idToken);\n    }\n    getAccessToken() {\n      return this.getCookie(this.ACCESS_TOKEN_KEY);\n    }\n    getRefreshToken() {\n      return this.getCookie(this.REFRESH_TOKEN_KEY);\n    }\n    getDaName() {\n      return this.getCookie(this.DA_NAME_KEY);\n    }\n    getDaUsername() {\n      return this.getCookie(this.DA_USERNAME_KEY);\n    }\n    getIdToken() {\n      return this.getCookie(this.DA_ID_TOKEN);\n    }\n    clearTokens() {\n      this.deleteCookie(this.ACCESS_TOKEN_KEY);\n      this.deleteCookie(this.REFRESH_TOKEN_KEY);\n      this.deleteCookie(this.DA_NAME_KEY);\n      this.deleteCookie(this.DA_USERNAME_KEY);\n      this.deleteCookie(this.DA_ID_TOKEN);\n      this.deleteCookie(this.LOGIN_TYPE_KEY);\n    }\n    static ɵfac = function TokenStorageService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TokenStorageService)();\n    };\n    static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: TokenStorageService,\n      factory: TokenStorageService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return TokenStorageService;\n})();", "map": {"version": 3, "names": ["TokenStorageService", "ACCESS_TOKEN_KEY", "REFRESH_TOKEN_KEY", "DA_NAME_KEY", "DA_USERNAME_KEY", "DA_ID_TOKEN", "LOGIN_TYPE_KEY", "getCurrentDomain", "window", "location", "hostname", "storeLoginType", "loginType", "<PERSON><PERSON><PERSON><PERSON>", "getLoginType", "<PERSON><PERSON><PERSON><PERSON>", "cname", "cvalue", "exseconds", "d", "Date", "expires", "setTime", "getTime", "toUTCString", "secureFlag", "protocol", "sameSiteFlag", "domainFlag", "document", "cookie", "name", "match", "RegExp", "decodeURIComponent", "deleteC<PERSON>ie", "storeTokens", "accessToken", "refreshToken", "expiresInSeconds", "storeAccessToken", "storeDaInfo", "<PERSON><PERSON><PERSON>", "daUsername", "idToken", "getAccessToken", "getRefreshToken", "getDaName", "getDaUsername", "getIdToken", "clearTokens", "factory", "ɵfac", "providedIn"], "sources": ["C:\\console\\aava-ui-web\\projects\\marketing\\src\\app\\shared\\auth\\services\\token-storage.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class TokenStorageService {\r\n  private ACCESS_TOKEN_KEY = 'access_token';\r\n  private REFRESH_TOKEN_KEY = 'refresh_token';\r\n  private DA_NAME_KEY = 'da_name';\r\n  private DA_USERNAME_KEY = 'da_username';\r\n  private DA_ID_TOKEN = 'id_token';\r\n  private LOGIN_TYPE_KEY = 'login_type';\r\n\r\n  private getCurrentDomain(): string {\r\n    return window.location.hostname;\r\n  }\r\n\r\n  public storeLoginType(loginType: 'sso' | 'basic'): void {\r\n    this.setCookie(this.LOGIN_TYPE_KEY, loginType);\r\n  }\r\n\r\n  public getLoginType(): 'sso' | 'basic' | null {\r\n    const loginType = this.getCookie(this.LOGIN_TYPE_KEY);\r\n    return loginType === 'sso' || loginType === 'basic' ? loginType : null;\r\n  }\r\n\r\n  public setCookie(cname: string, cvalue: string, exseconds?: number): void {\r\n    const d = new Date();\r\n    let expires = \"\";\r\n    if (exseconds) {\r\n      d.setTime(d.getTime() + (exseconds * 1000));\r\n      expires = \"expires=\" + d.toUTCString();\r\n    }\r\n    let secureFlag = window.location.protocol === \"https:\" ? \";Secure\" : \"\";\r\n    let sameSiteFlag = \";SameSite=Strict\";\r\n    let domainFlag = `;domain=${this.getCurrentDomain()}`;\r\n    document.cookie = `${cname}=${cvalue};${expires};path=/;${secureFlag}${sameSiteFlag}${domainFlag}`;\r\n  }\r\n\r\n  public getCookie(name: string): string | null {\r\n    const match = document.cookie.match(new RegExp(`(^| )${name}=([^;]+)`));\r\n    return match ? decodeURIComponent(match[2]) : null;\r\n  }\r\n\r\n  public deleteCookie(name: string): void {\r\n    let domainFlag = `;domain=${this.getCurrentDomain()}`;\r\n    document.cookie = `${name}=; path=/; max-age=0; Secure; SameSite=Strict${domainFlag}`;\r\n  }\r\n\r\n  storeTokens(accessToken: string, refreshToken: string, expiresInSeconds: number): void {\r\n    this.setCookie(this.ACCESS_TOKEN_KEY, accessToken, expiresInSeconds);\r\n    this.setCookie(this.REFRESH_TOKEN_KEY, refreshToken);\r\n  }\r\n\r\n  storeAccessToken(accessToken: string, expiresInSeconds?: number): void {\r\n    this.setCookie(this.ACCESS_TOKEN_KEY, accessToken, expiresInSeconds);\r\n  }\r\n\r\n  storeDaInfo(daName: string, daUsername: string, idToken: string): void {\r\n    this.setCookie(this.DA_NAME_KEY, daName);\r\n    this.setCookie(this.DA_USERNAME_KEY, daUsername);\r\n    this.setCookie(this.DA_ID_TOKEN, idToken);\r\n  }\r\n\r\n  getAccessToken(): string | null {\r\n    return this.getCookie(this.ACCESS_TOKEN_KEY);\r\n  }\r\n\r\n  getRefreshToken(): string | null {\r\n    return this.getCookie(this.REFRESH_TOKEN_KEY);\r\n  }\r\n\r\n  getDaName(): string | null {\r\n    return this.getCookie(this.DA_NAME_KEY);\r\n  }\r\n\r\n  getDaUsername(): string | null {\r\n    return this.getCookie(this.DA_USERNAME_KEY);\r\n  }\r\n\r\n  getIdToken(): string | null {\r\n    return this.getCookie(this.DA_ID_TOKEN);\r\n  }\r\n\r\n  clearTokens(): void {\r\n    this.deleteCookie(this.ACCESS_TOKEN_KEY);\r\n    this.deleteCookie(this.REFRESH_TOKEN_KEY);\r\n    this.deleteCookie(this.DA_NAME_KEY);\r\n    this.deleteCookie(this.DA_USERNAME_KEY);\r\n    this.deleteCookie(this.DA_ID_TOKEN);\r\n    this.deleteCookie(this.LOGIN_TYPE_KEY);\r\n  }\r\n} "], "mappings": ";AAKA,WAAaA,mBAAmB;EAA1B,MAAOA,mBAAmB;IACtBC,gBAAgB,GAAG,cAAc;IACjCC,iBAAiB,GAAG,eAAe;IACnCC,WAAW,GAAG,SAAS;IACvBC,eAAe,GAAG,aAAa;IAC/BC,WAAW,GAAG,UAAU;IACxBC,cAAc,GAAG,YAAY;IAE7BC,gBAAgBA,CAAA;MACtB,OAAOC,MAAM,CAACC,QAAQ,CAACC,QAAQ;IACjC;IAEOC,cAAcA,CAACC,SAA0B;MAC9C,IAAI,CAACC,SAAS,CAAC,IAAI,CAACP,cAAc,EAAEM,SAAS,CAAC;IAChD;IAEOE,YAAYA,CAAA;MACjB,MAAMF,SAAS,GAAG,IAAI,CAACG,SAAS,CAAC,IAAI,CAACT,cAAc,CAAC;MACrD,OAAOM,SAAS,KAAK,KAAK,IAAIA,SAAS,KAAK,OAAO,GAAGA,SAAS,GAAG,IAAI;IACxE;IAEOC,SAASA,CAACG,KAAa,EAAEC,MAAc,EAAEC,SAAkB;MAChE,MAAMC,CAAC,GAAG,IAAIC,IAAI,EAAE;MACpB,IAAIC,OAAO,GAAG,EAAE;MAChB,IAAIH,SAAS,EAAE;QACbC,CAAC,CAACG,OAAO,CAACH,CAAC,CAACI,OAAO,EAAE,GAAIL,SAAS,GAAG,IAAK,CAAC;QAC3CG,OAAO,GAAG,UAAU,GAAGF,CAAC,CAACK,WAAW,EAAE;MACxC;MACA,IAAIC,UAAU,GAAGjB,MAAM,CAACC,QAAQ,CAACiB,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,EAAE;MACvE,IAAIC,YAAY,GAAG,kBAAkB;MACrC,IAAIC,UAAU,GAAG,WAAW,IAAI,CAACrB,gBAAgB,EAAE,EAAE;MACrDsB,QAAQ,CAACC,MAAM,GAAG,GAAGd,KAAK,IAAIC,MAAM,IAAII,OAAO,WAAWI,UAAU,GAAGE,YAAY,GAAGC,UAAU,EAAE;IACpG;IAEOb,SAASA,CAACgB,IAAY;MAC3B,MAAMC,KAAK,GAAGH,QAAQ,CAACC,MAAM,CAACE,KAAK,CAAC,IAAIC,MAAM,CAAC,QAAQF,IAAI,UAAU,CAAC,CAAC;MACvE,OAAOC,KAAK,GAAGE,kBAAkB,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;IACpD;IAEOG,YAAYA,CAACJ,IAAY;MAC9B,IAAIH,UAAU,GAAG,WAAW,IAAI,CAACrB,gBAAgB,EAAE,EAAE;MACrDsB,QAAQ,CAACC,MAAM,GAAG,GAAGC,IAAI,gDAAgDH,UAAU,EAAE;IACvF;IAEAQ,WAAWA,CAACC,WAAmB,EAAEC,YAAoB,EAAEC,gBAAwB;MAC7E,IAAI,CAAC1B,SAAS,CAAC,IAAI,CAACZ,gBAAgB,EAAEoC,WAAW,EAAEE,gBAAgB,CAAC;MACpE,IAAI,CAAC1B,SAAS,CAAC,IAAI,CAACX,iBAAiB,EAAEoC,YAAY,CAAC;IACtD;IAEAE,gBAAgBA,CAACH,WAAmB,EAAEE,gBAAyB;MAC7D,IAAI,CAAC1B,SAAS,CAAC,IAAI,CAACZ,gBAAgB,EAAEoC,WAAW,EAAEE,gBAAgB,CAAC;IACtE;IAEAE,WAAWA,CAACC,MAAc,EAAEC,UAAkB,EAAEC,OAAe;MAC7D,IAAI,CAAC/B,SAAS,CAAC,IAAI,CAACV,WAAW,EAAEuC,MAAM,CAAC;MACxC,IAAI,CAAC7B,SAAS,CAAC,IAAI,CAACT,eAAe,EAAEuC,UAAU,CAAC;MAChD,IAAI,CAAC9B,SAAS,CAAC,IAAI,CAACR,WAAW,EAAEuC,OAAO,CAAC;IAC3C;IAEAC,cAAcA,CAAA;MACZ,OAAO,IAAI,CAAC9B,SAAS,CAAC,IAAI,CAACd,gBAAgB,CAAC;IAC9C;IAEA6C,eAAeA,CAAA;MACb,OAAO,IAAI,CAAC/B,SAAS,CAAC,IAAI,CAACb,iBAAiB,CAAC;IAC/C;IAEA6C,SAASA,CAAA;MACP,OAAO,IAAI,CAAChC,SAAS,CAAC,IAAI,CAACZ,WAAW,CAAC;IACzC;IAEA6C,aAAaA,CAAA;MACX,OAAO,IAAI,CAACjC,SAAS,CAAC,IAAI,CAACX,eAAe,CAAC;IAC7C;IAEA6C,UAAUA,CAAA;MACR,OAAO,IAAI,CAAClC,SAAS,CAAC,IAAI,CAACV,WAAW,CAAC;IACzC;IAEA6C,WAAWA,CAAA;MACT,IAAI,CAACf,YAAY,CAAC,IAAI,CAAClC,gBAAgB,CAAC;MACxC,IAAI,CAACkC,YAAY,CAAC,IAAI,CAACjC,iBAAiB,CAAC;MACzC,IAAI,CAACiC,YAAY,CAAC,IAAI,CAAChC,WAAW,CAAC;MACnC,IAAI,CAACgC,YAAY,CAAC,IAAI,CAAC/B,eAAe,CAAC;MACvC,IAAI,CAAC+B,YAAY,CAAC,IAAI,CAAC9B,WAAW,CAAC;MACnC,IAAI,CAAC8B,YAAY,CAAC,IAAI,CAAC7B,cAAc,CAAC;IACxC;;uCAtFWN,mBAAmB;IAAA;;aAAnBA,mBAAmB;MAAAmD,OAAA,EAAnBnD,mBAAmB,CAAAoD,IAAA;MAAAC,UAAA,EAFlB;IAAM;;SAEPrD,mBAAmB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}