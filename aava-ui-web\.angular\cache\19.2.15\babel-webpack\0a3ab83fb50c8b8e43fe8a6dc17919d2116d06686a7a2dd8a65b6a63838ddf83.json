{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { autorun } from './autorun.js';\nimport { observableValue, transaction } from './base.js';\nimport { CancellationError } from '../errors.js';\n/**\n * A promise whose state is observable.\n */\nexport class ObservablePromise {\n  static fromFn(fn) {\n    return new ObservablePromise(fn());\n  }\n  constructor(promise) {\n    this._value = observableValue(this, undefined);\n    /**\n     * The current state of the promise.\n     * Is `undefined` if the promise didn't resolve yet.\n     */\n    this.promiseResult = this._value;\n    this.promise = promise.then(value => {\n      transaction(tx => {\n        /** @description onPromiseResolved */\n        this._value.set(new PromiseResult(value, undefined), tx);\n      });\n      return value;\n    }, error => {\n      transaction(tx => {\n        /** @description onPromiseRejected */\n        this._value.set(new PromiseResult(undefined, error), tx);\n      });\n      throw error;\n    });\n  }\n}\nexport class PromiseResult {\n  constructor(\n  /**\n   * The value of the resolved promise.\n   * Undefined if the promise rejected.\n   */\n  data,\n  /**\n   * The error in case of a rejected promise.\n   * Undefined if the promise resolved.\n   */\n  error) {\n    this.data = data;\n    this.error = error;\n  }\n}\nexport function waitForState(observable, predicate, isError, cancellationToken) {\n  if (!predicate) {\n    predicate = state => state !== null && state !== undefined;\n  }\n  return new Promise((resolve, reject) => {\n    let isImmediateRun = true;\n    let shouldDispose = false;\n    const stateObs = observable.map(state => {\n      /** @description waitForState.state */\n      return {\n        isFinished: predicate(state),\n        error: isError ? isError(state) : false,\n        state\n      };\n    });\n    const d = autorun(reader => {\n      /** @description waitForState */\n      const {\n        isFinished,\n        error,\n        state\n      } = stateObs.read(reader);\n      if (isFinished || error) {\n        if (isImmediateRun) {\n          // The variable `d` is not initialized yet\n          shouldDispose = true;\n        } else {\n          d.dispose();\n        }\n        if (error) {\n          reject(error === true ? state : error);\n        } else {\n          resolve(state);\n        }\n      }\n    });\n    if (cancellationToken) {\n      const dc = cancellationToken.onCancellationRequested(() => {\n        d.dispose();\n        dc.dispose();\n        reject(new CancellationError());\n      });\n      if (cancellationToken.isCancellationRequested) {\n        d.dispose();\n        dc.dispose();\n        reject(new CancellationError());\n        return;\n      }\n    }\n    isImmediateRun = false;\n    if (shouldDispose) {\n      d.dispose();\n    }\n  });\n}", "map": {"version": 3, "names": ["autorun", "observableValue", "transaction", "CancellationError", "ObservablePromise", "fromFn", "fn", "constructor", "promise", "_value", "undefined", "promiseResult", "then", "value", "tx", "set", "PromiseResult", "error", "data", "waitForState", "observable", "predicate", "isError", "cancellationToken", "state", "Promise", "resolve", "reject", "isImmediateRun", "shouldDispose", "stateObs", "map", "isFinished", "d", "reader", "read", "dispose", "dc", "onCancellationRequested", "isCancellationRequested"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/common/observableInternal/promise.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { autorun } from './autorun.js';\nimport { observableValue, transaction } from './base.js';\nimport { CancellationError } from '../errors.js';\n/**\n * A promise whose state is observable.\n */\nexport class ObservablePromise {\n    static fromFn(fn) {\n        return new ObservablePromise(fn());\n    }\n    constructor(promise) {\n        this._value = observableValue(this, undefined);\n        /**\n         * The current state of the promise.\n         * Is `undefined` if the promise didn't resolve yet.\n         */\n        this.promiseResult = this._value;\n        this.promise = promise.then(value => {\n            transaction(tx => {\n                /** @description onPromiseResolved */\n                this._value.set(new PromiseResult(value, undefined), tx);\n            });\n            return value;\n        }, error => {\n            transaction(tx => {\n                /** @description onPromiseRejected */\n                this._value.set(new PromiseResult(undefined, error), tx);\n            });\n            throw error;\n        });\n    }\n}\nexport class PromiseResult {\n    constructor(\n    /**\n     * The value of the resolved promise.\n     * Undefined if the promise rejected.\n     */\n    data, \n    /**\n     * The error in case of a rejected promise.\n     * Undefined if the promise resolved.\n     */\n    error) {\n        this.data = data;\n        this.error = error;\n    }\n}\nexport function waitForState(observable, predicate, isError, cancellationToken) {\n    if (!predicate) {\n        predicate = state => state !== null && state !== undefined;\n    }\n    return new Promise((resolve, reject) => {\n        let isImmediateRun = true;\n        let shouldDispose = false;\n        const stateObs = observable.map(state => {\n            /** @description waitForState.state */\n            return {\n                isFinished: predicate(state),\n                error: isError ? isError(state) : false,\n                state\n            };\n        });\n        const d = autorun(reader => {\n            /** @description waitForState */\n            const { isFinished, error, state } = stateObs.read(reader);\n            if (isFinished || error) {\n                if (isImmediateRun) {\n                    // The variable `d` is not initialized yet\n                    shouldDispose = true;\n                }\n                else {\n                    d.dispose();\n                }\n                if (error) {\n                    reject(error === true ? state : error);\n                }\n                else {\n                    resolve(state);\n                }\n            }\n        });\n        if (cancellationToken) {\n            const dc = cancellationToken.onCancellationRequested(() => {\n                d.dispose();\n                dc.dispose();\n                reject(new CancellationError());\n            });\n            if (cancellationToken.isCancellationRequested) {\n                d.dispose();\n                dc.dispose();\n                reject(new CancellationError());\n                return;\n            }\n        }\n        isImmediateRun = false;\n        if (shouldDispose) {\n            d.dispose();\n        }\n    });\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,eAAe,EAAEC,WAAW,QAAQ,WAAW;AACxD,SAASC,iBAAiB,QAAQ,cAAc;AAChD;AACA;AACA;AACA,OAAO,MAAMC,iBAAiB,CAAC;EAC3B,OAAOC,MAAMA,CAACC,EAAE,EAAE;IACd,OAAO,IAAIF,iBAAiB,CAACE,EAAE,CAAC,CAAC,CAAC;EACtC;EACAC,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAI,CAACC,MAAM,GAAGR,eAAe,CAAC,IAAI,EAAES,SAAS,CAAC;IAC9C;AACR;AACA;AACA;IACQ,IAAI,CAACC,aAAa,GAAG,IAAI,CAACF,MAAM;IAChC,IAAI,CAACD,OAAO,GAAGA,OAAO,CAACI,IAAI,CAACC,KAAK,IAAI;MACjCX,WAAW,CAACY,EAAE,IAAI;QACd;QACA,IAAI,CAACL,MAAM,CAACM,GAAG,CAAC,IAAIC,aAAa,CAACH,KAAK,EAAEH,SAAS,CAAC,EAAEI,EAAE,CAAC;MAC5D,CAAC,CAAC;MACF,OAAOD,KAAK;IAChB,CAAC,EAAEI,KAAK,IAAI;MACRf,WAAW,CAACY,EAAE,IAAI;QACd;QACA,IAAI,CAACL,MAAM,CAACM,GAAG,CAAC,IAAIC,aAAa,CAACN,SAAS,EAAEO,KAAK,CAAC,EAAEH,EAAE,CAAC;MAC5D,CAAC,CAAC;MACF,MAAMG,KAAK;IACf,CAAC,CAAC;EACN;AACJ;AACA,OAAO,MAAMD,aAAa,CAAC;EACvBT,WAAWA;EACX;AACJ;AACA;AACA;EACIW,IAAI;EACJ;AACJ;AACA;AACA;EACID,KAAK,EAAE;IACH,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACD,KAAK,GAAGA,KAAK;EACtB;AACJ;AACA,OAAO,SAASE,YAAYA,CAACC,UAAU,EAAEC,SAAS,EAAEC,OAAO,EAAEC,iBAAiB,EAAE;EAC5E,IAAI,CAACF,SAAS,EAAE;IACZA,SAAS,GAAGG,KAAK,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKd,SAAS;EAC9D;EACA,OAAO,IAAIe,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACpC,IAAIC,cAAc,GAAG,IAAI;IACzB,IAAIC,aAAa,GAAG,KAAK;IACzB,MAAMC,QAAQ,GAAGV,UAAU,CAACW,GAAG,CAACP,KAAK,IAAI;MACrC;MACA,OAAO;QACHQ,UAAU,EAAEX,SAAS,CAACG,KAAK,CAAC;QAC5BP,KAAK,EAAEK,OAAO,GAAGA,OAAO,CAACE,KAAK,CAAC,GAAG,KAAK;QACvCA;MACJ,CAAC;IACL,CAAC,CAAC;IACF,MAAMS,CAAC,GAAGjC,OAAO,CAACkC,MAAM,IAAI;MACxB;MACA,MAAM;QAAEF,UAAU;QAAEf,KAAK;QAAEO;MAAM,CAAC,GAAGM,QAAQ,CAACK,IAAI,CAACD,MAAM,CAAC;MAC1D,IAAIF,UAAU,IAAIf,KAAK,EAAE;QACrB,IAAIW,cAAc,EAAE;UAChB;UACAC,aAAa,GAAG,IAAI;QACxB,CAAC,MACI;UACDI,CAAC,CAACG,OAAO,CAAC,CAAC;QACf;QACA,IAAInB,KAAK,EAAE;UACPU,MAAM,CAACV,KAAK,KAAK,IAAI,GAAGO,KAAK,GAAGP,KAAK,CAAC;QAC1C,CAAC,MACI;UACDS,OAAO,CAACF,KAAK,CAAC;QAClB;MACJ;IACJ,CAAC,CAAC;IACF,IAAID,iBAAiB,EAAE;MACnB,MAAMc,EAAE,GAAGd,iBAAiB,CAACe,uBAAuB,CAAC,MAAM;QACvDL,CAAC,CAACG,OAAO,CAAC,CAAC;QACXC,EAAE,CAACD,OAAO,CAAC,CAAC;QACZT,MAAM,CAAC,IAAIxB,iBAAiB,CAAC,CAAC,CAAC;MACnC,CAAC,CAAC;MACF,IAAIoB,iBAAiB,CAACgB,uBAAuB,EAAE;QAC3CN,CAAC,CAACG,OAAO,CAAC,CAAC;QACXC,EAAE,CAACD,OAAO,CAAC,CAAC;QACZT,MAAM,CAAC,IAAIxB,iBAAiB,CAAC,CAAC,CAAC;QAC/B;MACJ;IACJ;IACAyB,cAAc,GAAG,KAAK;IACtB,IAAIC,aAAa,EAAE;MACfI,CAAC,CAACG,OAAO,CAAC,CAAC;IACf;EACJ,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}