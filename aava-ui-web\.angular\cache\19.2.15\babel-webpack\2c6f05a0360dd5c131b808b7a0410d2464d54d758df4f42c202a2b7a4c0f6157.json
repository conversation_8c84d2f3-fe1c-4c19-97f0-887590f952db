{"ast": null, "code": "var __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = this && this.__param || function (paramIndex, decorator) {\n  return function (target, key) {\n    decorator(target, key, paramIndex);\n  };\n};\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as dom from '../../../../base/browser/dom.js';\nimport { HoverAction } from '../../../../base/browser/ui/hover/hoverWidget.js';\nimport { Disposable } from '../../../../base/common/lifecycle.js';\nimport { IKeybindingService } from '../../../../platform/keybinding/common/keybinding.js';\nconst $ = dom.$;\nlet EditorHoverStatusBar = class EditorHoverStatusBar extends Disposable {\n  get hasContent() {\n    return this._hasContent;\n  }\n  constructor(_keybindingService) {\n    super();\n    this._keybindingService = _keybindingService;\n    this.actions = [];\n    this._hasContent = false;\n    this.hoverElement = $('div.hover-row.status-bar');\n    this.hoverElement.tabIndex = 0;\n    this.actionsElement = dom.append(this.hoverElement, $('div.actions'));\n  }\n  addAction(actionOptions) {\n    const keybinding = this._keybindingService.lookupKeybinding(actionOptions.commandId);\n    const keybindingLabel = keybinding ? keybinding.getLabel() : null;\n    this._hasContent = true;\n    const action = this._register(HoverAction.render(this.actionsElement, actionOptions, keybindingLabel));\n    this.actions.push(action);\n    return action;\n  }\n  append(element) {\n    const result = dom.append(this.actionsElement, element);\n    this._hasContent = true;\n    return result;\n  }\n};\nEditorHoverStatusBar = __decorate([__param(0, IKeybindingService)], EditorHoverStatusBar);\nexport { EditorHoverStatusBar };", "map": {"version": 3, "names": ["__decorate", "decorators", "target", "key", "desc", "c", "arguments", "length", "r", "Object", "getOwnPropertyDescriptor", "d", "Reflect", "decorate", "i", "defineProperty", "__param", "paramIndex", "decorator", "dom", "HoverAction", "Disposable", "IKeybindingService", "$", "EditorHoverStatusBar", "<PERSON><PERSON><PERSON><PERSON>", "_hasContent", "constructor", "_keybindingService", "actions", "hoverElement", "tabIndex", "actionsElement", "append", "addAction", "actionOptions", "keybinding", "lookupKeybinding", "commandId", "keybinding<PERSON>abel", "get<PERSON><PERSON><PERSON>", "action", "_register", "render", "push", "element", "result"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/contrib/hover/browser/contentHoverStatusBar.js"], "sourcesContent": ["var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = (this && this.__param) || function (paramIndex, decorator) {\n    return function (target, key) { decorator(target, key, paramIndex); }\n};\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as dom from '../../../../base/browser/dom.js';\nimport { HoverAction } from '../../../../base/browser/ui/hover/hoverWidget.js';\nimport { Disposable } from '../../../../base/common/lifecycle.js';\nimport { IKeybindingService } from '../../../../platform/keybinding/common/keybinding.js';\nconst $ = dom.$;\nlet EditorHoverStatusBar = class EditorHoverStatusBar extends Disposable {\n    get hasContent() {\n        return this._hasContent;\n    }\n    constructor(_keybindingService) {\n        super();\n        this._keybindingService = _keybindingService;\n        this.actions = [];\n        this._hasContent = false;\n        this.hoverElement = $('div.hover-row.status-bar');\n        this.hoverElement.tabIndex = 0;\n        this.actionsElement = dom.append(this.hoverElement, $('div.actions'));\n    }\n    addAction(actionOptions) {\n        const keybinding = this._keybindingService.lookupKeybinding(actionOptions.commandId);\n        const keybindingLabel = keybinding ? keybinding.getLabel() : null;\n        this._hasContent = true;\n        const action = this._register(HoverAction.render(this.actionsElement, actionOptions, keybindingLabel));\n        this.actions.push(action);\n        return action;\n    }\n    append(element) {\n        const result = dom.append(this.actionsElement, element);\n        this._hasContent = true;\n        return result;\n    }\n};\nEditorHoverStatusBar = __decorate([\n    __param(0, IKeybindingService)\n], EditorHoverStatusBar);\nexport { EditorHoverStatusBar };\n"], "mappings": "AAAA,IAAIA,UAAU,GAAI,IAAI,IAAI,IAAI,CAACA,UAAU,IAAK,UAAUC,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAE;EACnF,IAAIC,CAAC,GAAGC,SAAS,CAACC,MAAM;IAAEC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGH,MAAM,GAAGE,IAAI,KAAK,IAAI,GAAGA,IAAI,GAAGK,MAAM,CAACC,wBAAwB,CAACR,MAAM,EAAEC,GAAG,CAAC,GAAGC,IAAI;IAAEO,CAAC;EAC5H,IAAI,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,CAACC,QAAQ,KAAK,UAAU,EAAEL,CAAC,GAAGI,OAAO,CAACC,QAAQ,CAACZ,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,CAAC,CAAC,KAC1H,KAAK,IAAIU,CAAC,GAAGb,UAAU,CAACM,MAAM,GAAG,CAAC,EAAEO,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE,IAAIH,CAAC,GAAGV,UAAU,CAACa,CAAC,CAAC,EAAEN,CAAC,GAAG,CAACH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACH,CAAC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACT,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,GAAGG,CAAC,CAACT,MAAM,EAAEC,GAAG,CAAC,KAAKK,CAAC;EACjJ,OAAOH,CAAC,GAAG,CAAC,IAAIG,CAAC,IAAIC,MAAM,CAACM,cAAc,CAACb,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,EAAEA,CAAC;AACjE,CAAC;AACD,IAAIQ,OAAO,GAAI,IAAI,IAAI,IAAI,CAACA,OAAO,IAAK,UAAUC,UAAU,EAAEC,SAAS,EAAE;EACrE,OAAO,UAAUhB,MAAM,EAAEC,GAAG,EAAE;IAAEe,SAAS,CAAChB,MAAM,EAAEC,GAAG,EAAEc,UAAU,CAAC;EAAE,CAAC;AACzE,CAAC;AACD;AACA;AACA;AACA;AACA,OAAO,KAAKE,GAAG,MAAM,iCAAiC;AACtD,SAASC,WAAW,QAAQ,kDAAkD;AAC9E,SAASC,UAAU,QAAQ,sCAAsC;AACjE,SAASC,kBAAkB,QAAQ,sDAAsD;AACzF,MAAMC,CAAC,GAAGJ,GAAG,CAACI,CAAC;AACf,IAAIC,oBAAoB,GAAG,MAAMA,oBAAoB,SAASH,UAAU,CAAC;EACrE,IAAII,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACC,WAAW;EAC3B;EACAC,WAAWA,CAACC,kBAAkB,EAAE;IAC5B,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACH,WAAW,GAAG,KAAK;IACxB,IAAI,CAACI,YAAY,GAAGP,CAAC,CAAC,0BAA0B,CAAC;IACjD,IAAI,CAACO,YAAY,CAACC,QAAQ,GAAG,CAAC;IAC9B,IAAI,CAACC,cAAc,GAAGb,GAAG,CAACc,MAAM,CAAC,IAAI,CAACH,YAAY,EAAEP,CAAC,CAAC,aAAa,CAAC,CAAC;EACzE;EACAW,SAASA,CAACC,aAAa,EAAE;IACrB,MAAMC,UAAU,GAAG,IAAI,CAACR,kBAAkB,CAACS,gBAAgB,CAACF,aAAa,CAACG,SAAS,CAAC;IACpF,MAAMC,eAAe,GAAGH,UAAU,GAAGA,UAAU,CAACI,QAAQ,CAAC,CAAC,GAAG,IAAI;IACjE,IAAI,CAACd,WAAW,GAAG,IAAI;IACvB,MAAMe,MAAM,GAAG,IAAI,CAACC,SAAS,CAACtB,WAAW,CAACuB,MAAM,CAAC,IAAI,CAACX,cAAc,EAAEG,aAAa,EAAEI,eAAe,CAAC,CAAC;IACtG,IAAI,CAACV,OAAO,CAACe,IAAI,CAACH,MAAM,CAAC;IACzB,OAAOA,MAAM;EACjB;EACAR,MAAMA,CAACY,OAAO,EAAE;IACZ,MAAMC,MAAM,GAAG3B,GAAG,CAACc,MAAM,CAAC,IAAI,CAACD,cAAc,EAAEa,OAAO,CAAC;IACvD,IAAI,CAACnB,WAAW,GAAG,IAAI;IACvB,OAAOoB,MAAM;EACjB;AACJ,CAAC;AACDtB,oBAAoB,GAAGxB,UAAU,CAAC,CAC9BgB,OAAO,CAAC,CAAC,EAAEM,kBAAkB,CAAC,CACjC,EAAEE,oBAAoB,CAAC;AACxB,SAASA,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}