{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { createFastDomNode } from '../../../base/browser/fastDomNode.js';\nimport { createTrustedTypesPolicy } from '../../../base/browser/trustedTypes.js';\nimport { BugIndicatingError } from '../../../base/common/errors.js';\nimport { StringBuilder } from '../../common/core/stringBuilder.js';\nexport class RenderedLinesCollection {\n  constructor(_lineFactory) {\n    this._lineFactory = _lineFactory;\n    this._set(1, []);\n  }\n  flush() {\n    this._set(1, []);\n  }\n  _set(rendLineNumberStart, lines) {\n    this._lines = lines;\n    this._rendLineNumberStart = rendLineNumberStart;\n  }\n  _get() {\n    return {\n      rendLineNumberStart: this._rendLineNumberStart,\n      lines: this._lines\n    };\n  }\n  /**\n   * @returns Inclusive line number that is inside this collection\n   */\n  getStartLineNumber() {\n    return this._rendLineNumberStart;\n  }\n  /**\n   * @returns Inclusive line number that is inside this collection\n   */\n  getEndLineNumber() {\n    return this._rendLineNumberStart + this._lines.length - 1;\n  }\n  getCount() {\n    return this._lines.length;\n  }\n  getLine(lineNumber) {\n    const lineIndex = lineNumber - this._rendLineNumberStart;\n    if (lineIndex < 0 || lineIndex >= this._lines.length) {\n      throw new BugIndicatingError('Illegal value for lineNumber');\n    }\n    return this._lines[lineIndex];\n  }\n  /**\n   * @returns Lines that were removed from this collection\n   */\n  onLinesDeleted(deleteFromLineNumber, deleteToLineNumber) {\n    if (this.getCount() === 0) {\n      // no lines\n      return null;\n    }\n    const startLineNumber = this.getStartLineNumber();\n    const endLineNumber = this.getEndLineNumber();\n    if (deleteToLineNumber < startLineNumber) {\n      // deleting above the viewport\n      const deleteCnt = deleteToLineNumber - deleteFromLineNumber + 1;\n      this._rendLineNumberStart -= deleteCnt;\n      return null;\n    }\n    if (deleteFromLineNumber > endLineNumber) {\n      // deleted below the viewport\n      return null;\n    }\n    // Record what needs to be deleted\n    let deleteStartIndex = 0;\n    let deleteCount = 0;\n    for (let lineNumber = startLineNumber; lineNumber <= endLineNumber; lineNumber++) {\n      const lineIndex = lineNumber - this._rendLineNumberStart;\n      if (deleteFromLineNumber <= lineNumber && lineNumber <= deleteToLineNumber) {\n        // this is a line to be deleted\n        if (deleteCount === 0) {\n          // this is the first line to be deleted\n          deleteStartIndex = lineIndex;\n          deleteCount = 1;\n        } else {\n          deleteCount++;\n        }\n      }\n    }\n    // Adjust this._rendLineNumberStart for lines deleted above\n    if (deleteFromLineNumber < startLineNumber) {\n      // Something was deleted above\n      let deleteAboveCount = 0;\n      if (deleteToLineNumber < startLineNumber) {\n        // the entire deleted lines are above\n        deleteAboveCount = deleteToLineNumber - deleteFromLineNumber + 1;\n      } else {\n        deleteAboveCount = startLineNumber - deleteFromLineNumber;\n      }\n      this._rendLineNumberStart -= deleteAboveCount;\n    }\n    const deleted = this._lines.splice(deleteStartIndex, deleteCount);\n    return deleted;\n  }\n  onLinesChanged(changeFromLineNumber, changeCount) {\n    const changeToLineNumber = changeFromLineNumber + changeCount - 1;\n    if (this.getCount() === 0) {\n      // no lines\n      return false;\n    }\n    const startLineNumber = this.getStartLineNumber();\n    const endLineNumber = this.getEndLineNumber();\n    let someoneNotified = false;\n    for (let changedLineNumber = changeFromLineNumber; changedLineNumber <= changeToLineNumber; changedLineNumber++) {\n      if (changedLineNumber >= startLineNumber && changedLineNumber <= endLineNumber) {\n        // Notify the line\n        this._lines[changedLineNumber - this._rendLineNumberStart].onContentChanged();\n        someoneNotified = true;\n      }\n    }\n    return someoneNotified;\n  }\n  onLinesInserted(insertFromLineNumber, insertToLineNumber) {\n    if (this.getCount() === 0) {\n      // no lines\n      return null;\n    }\n    const insertCnt = insertToLineNumber - insertFromLineNumber + 1;\n    const startLineNumber = this.getStartLineNumber();\n    const endLineNumber = this.getEndLineNumber();\n    if (insertFromLineNumber <= startLineNumber) {\n      // inserting above the viewport\n      this._rendLineNumberStart += insertCnt;\n      return null;\n    }\n    if (insertFromLineNumber > endLineNumber) {\n      // inserting below the viewport\n      return null;\n    }\n    if (insertCnt + insertFromLineNumber > endLineNumber) {\n      // insert inside the viewport in such a way that all remaining lines are pushed outside\n      const deleted = this._lines.splice(insertFromLineNumber - this._rendLineNumberStart, endLineNumber - insertFromLineNumber + 1);\n      return deleted;\n    }\n    // insert inside the viewport, push out some lines, but not all remaining lines\n    const newLines = [];\n    for (let i = 0; i < insertCnt; i++) {\n      newLines[i] = this._lineFactory.createLine();\n    }\n    const insertIndex = insertFromLineNumber - this._rendLineNumberStart;\n    const beforeLines = this._lines.slice(0, insertIndex);\n    const afterLines = this._lines.slice(insertIndex, this._lines.length - insertCnt);\n    const deletedLines = this._lines.slice(this._lines.length - insertCnt, this._lines.length);\n    this._lines = beforeLines.concat(newLines).concat(afterLines);\n    return deletedLines;\n  }\n  onTokensChanged(ranges) {\n    if (this.getCount() === 0) {\n      // no lines\n      return false;\n    }\n    const startLineNumber = this.getStartLineNumber();\n    const endLineNumber = this.getEndLineNumber();\n    let notifiedSomeone = false;\n    for (let i = 0, len = ranges.length; i < len; i++) {\n      const rng = ranges[i];\n      if (rng.toLineNumber < startLineNumber || rng.fromLineNumber > endLineNumber) {\n        // range outside viewport\n        continue;\n      }\n      const from = Math.max(startLineNumber, rng.fromLineNumber);\n      const to = Math.min(endLineNumber, rng.toLineNumber);\n      for (let lineNumber = from; lineNumber <= to; lineNumber++) {\n        const lineIndex = lineNumber - this._rendLineNumberStart;\n        this._lines[lineIndex].onTokensChanged();\n        notifiedSomeone = true;\n      }\n    }\n    return notifiedSomeone;\n  }\n}\nexport class VisibleLinesCollection {\n  constructor(_lineFactory) {\n    this._lineFactory = _lineFactory;\n    this.domNode = this._createDomNode();\n    this._linesCollection = new RenderedLinesCollection(this._lineFactory);\n  }\n  _createDomNode() {\n    const domNode = createFastDomNode(document.createElement('div'));\n    domNode.setClassName('view-layer');\n    domNode.setPosition('absolute');\n    domNode.domNode.setAttribute('role', 'presentation');\n    domNode.domNode.setAttribute('aria-hidden', 'true');\n    return domNode;\n  }\n  // ---- begin view event handlers\n  onConfigurationChanged(e) {\n    if (e.hasChanged(146 /* EditorOption.layoutInfo */)) {\n      return true;\n    }\n    return false;\n  }\n  onFlushed(e) {\n    this._linesCollection.flush();\n    // No need to clear the dom node because a full .innerHTML will occur in ViewLayerRenderer._render\n    return true;\n  }\n  onLinesChanged(e) {\n    return this._linesCollection.onLinesChanged(e.fromLineNumber, e.count);\n  }\n  onLinesDeleted(e) {\n    const deleted = this._linesCollection.onLinesDeleted(e.fromLineNumber, e.toLineNumber);\n    if (deleted) {\n      // Remove from DOM\n      for (let i = 0, len = deleted.length; i < len; i++) {\n        const lineDomNode = deleted[i].getDomNode();\n        lineDomNode?.remove();\n      }\n    }\n    return true;\n  }\n  onLinesInserted(e) {\n    const deleted = this._linesCollection.onLinesInserted(e.fromLineNumber, e.toLineNumber);\n    if (deleted) {\n      // Remove from DOM\n      for (let i = 0, len = deleted.length; i < len; i++) {\n        const lineDomNode = deleted[i].getDomNode();\n        lineDomNode?.remove();\n      }\n    }\n    return true;\n  }\n  onScrollChanged(e) {\n    return e.scrollTopChanged;\n  }\n  onTokensChanged(e) {\n    return this._linesCollection.onTokensChanged(e.ranges);\n  }\n  onZonesChanged(e) {\n    return true;\n  }\n  // ---- end view event handlers\n  getStartLineNumber() {\n    return this._linesCollection.getStartLineNumber();\n  }\n  getEndLineNumber() {\n    return this._linesCollection.getEndLineNumber();\n  }\n  getVisibleLine(lineNumber) {\n    return this._linesCollection.getLine(lineNumber);\n  }\n  renderLines(viewportData) {\n    const inp = this._linesCollection._get();\n    const renderer = new ViewLayerRenderer(this.domNode.domNode, this._lineFactory, viewportData);\n    const ctx = {\n      rendLineNumberStart: inp.rendLineNumberStart,\n      lines: inp.lines,\n      linesLength: inp.lines.length\n    };\n    // Decide if this render will do a single update (single large .innerHTML) or many updates (inserting/removing dom nodes)\n    const resCtx = renderer.render(ctx, viewportData.startLineNumber, viewportData.endLineNumber, viewportData.relativeVerticalOffset);\n    this._linesCollection._set(resCtx.rendLineNumberStart, resCtx.lines);\n  }\n}\nclass ViewLayerRenderer {\n  static {\n    this._ttPolicy = createTrustedTypesPolicy('editorViewLayer', {\n      createHTML: value => value\n    });\n  }\n  constructor(_domNode, _lineFactory, _viewportData) {\n    this._domNode = _domNode;\n    this._lineFactory = _lineFactory;\n    this._viewportData = _viewportData;\n  }\n  render(inContext, startLineNumber, stopLineNumber, deltaTop) {\n    const ctx = {\n      rendLineNumberStart: inContext.rendLineNumberStart,\n      lines: inContext.lines.slice(0),\n      linesLength: inContext.linesLength\n    };\n    if (ctx.rendLineNumberStart + ctx.linesLength - 1 < startLineNumber || stopLineNumber < ctx.rendLineNumberStart) {\n      // There is no overlap whatsoever\n      ctx.rendLineNumberStart = startLineNumber;\n      ctx.linesLength = stopLineNumber - startLineNumber + 1;\n      ctx.lines = [];\n      for (let x = startLineNumber; x <= stopLineNumber; x++) {\n        ctx.lines[x - startLineNumber] = this._lineFactory.createLine();\n      }\n      this._finishRendering(ctx, true, deltaTop);\n      return ctx;\n    }\n    // Update lines which will remain untouched\n    this._renderUntouchedLines(ctx, Math.max(startLineNumber - ctx.rendLineNumberStart, 0), Math.min(stopLineNumber - ctx.rendLineNumberStart, ctx.linesLength - 1), deltaTop, startLineNumber);\n    if (ctx.rendLineNumberStart > startLineNumber) {\n      // Insert lines before\n      const fromLineNumber = startLineNumber;\n      const toLineNumber = Math.min(stopLineNumber, ctx.rendLineNumberStart - 1);\n      if (fromLineNumber <= toLineNumber) {\n        this._insertLinesBefore(ctx, fromLineNumber, toLineNumber, deltaTop, startLineNumber);\n        ctx.linesLength += toLineNumber - fromLineNumber + 1;\n      }\n    } else if (ctx.rendLineNumberStart < startLineNumber) {\n      // Remove lines before\n      const removeCnt = Math.min(ctx.linesLength, startLineNumber - ctx.rendLineNumberStart);\n      if (removeCnt > 0) {\n        this._removeLinesBefore(ctx, removeCnt);\n        ctx.linesLength -= removeCnt;\n      }\n    }\n    ctx.rendLineNumberStart = startLineNumber;\n    if (ctx.rendLineNumberStart + ctx.linesLength - 1 < stopLineNumber) {\n      // Insert lines after\n      const fromLineNumber = ctx.rendLineNumberStart + ctx.linesLength;\n      const toLineNumber = stopLineNumber;\n      if (fromLineNumber <= toLineNumber) {\n        this._insertLinesAfter(ctx, fromLineNumber, toLineNumber, deltaTop, startLineNumber);\n        ctx.linesLength += toLineNumber - fromLineNumber + 1;\n      }\n    } else if (ctx.rendLineNumberStart + ctx.linesLength - 1 > stopLineNumber) {\n      // Remove lines after\n      const fromLineNumber = Math.max(0, stopLineNumber - ctx.rendLineNumberStart + 1);\n      const toLineNumber = ctx.linesLength - 1;\n      const removeCnt = toLineNumber - fromLineNumber + 1;\n      if (removeCnt > 0) {\n        this._removeLinesAfter(ctx, removeCnt);\n        ctx.linesLength -= removeCnt;\n      }\n    }\n    this._finishRendering(ctx, false, deltaTop);\n    return ctx;\n  }\n  _renderUntouchedLines(ctx, startIndex, endIndex, deltaTop, deltaLN) {\n    const rendLineNumberStart = ctx.rendLineNumberStart;\n    const lines = ctx.lines;\n    for (let i = startIndex; i <= endIndex; i++) {\n      const lineNumber = rendLineNumberStart + i;\n      lines[i].layoutLine(lineNumber, deltaTop[lineNumber - deltaLN], this._viewportData.lineHeight);\n    }\n  }\n  _insertLinesBefore(ctx, fromLineNumber, toLineNumber, deltaTop, deltaLN) {\n    const newLines = [];\n    let newLinesLen = 0;\n    for (let lineNumber = fromLineNumber; lineNumber <= toLineNumber; lineNumber++) {\n      newLines[newLinesLen++] = this._lineFactory.createLine();\n    }\n    ctx.lines = newLines.concat(ctx.lines);\n  }\n  _removeLinesBefore(ctx, removeCount) {\n    for (let i = 0; i < removeCount; i++) {\n      const lineDomNode = ctx.lines[i].getDomNode();\n      lineDomNode?.remove();\n    }\n    ctx.lines.splice(0, removeCount);\n  }\n  _insertLinesAfter(ctx, fromLineNumber, toLineNumber, deltaTop, deltaLN) {\n    const newLines = [];\n    let newLinesLen = 0;\n    for (let lineNumber = fromLineNumber; lineNumber <= toLineNumber; lineNumber++) {\n      newLines[newLinesLen++] = this._lineFactory.createLine();\n    }\n    ctx.lines = ctx.lines.concat(newLines);\n  }\n  _removeLinesAfter(ctx, removeCount) {\n    const removeIndex = ctx.linesLength - removeCount;\n    for (let i = 0; i < removeCount; i++) {\n      const lineDomNode = ctx.lines[removeIndex + i].getDomNode();\n      lineDomNode?.remove();\n    }\n    ctx.lines.splice(removeIndex, removeCount);\n  }\n  _finishRenderingNewLines(ctx, domNodeIsEmpty, newLinesHTML, wasNew) {\n    if (ViewLayerRenderer._ttPolicy) {\n      newLinesHTML = ViewLayerRenderer._ttPolicy.createHTML(newLinesHTML);\n    }\n    const lastChild = this._domNode.lastChild;\n    if (domNodeIsEmpty || !lastChild) {\n      this._domNode.innerHTML = newLinesHTML; // explains the ugly casts -> https://github.com/microsoft/vscode/issues/106396#issuecomment-692625393;\n    } else {\n      lastChild.insertAdjacentHTML('afterend', newLinesHTML);\n    }\n    let currChild = this._domNode.lastChild;\n    for (let i = ctx.linesLength - 1; i >= 0; i--) {\n      const line = ctx.lines[i];\n      if (wasNew[i]) {\n        line.setDomNode(currChild);\n        currChild = currChild.previousSibling;\n      }\n    }\n  }\n  _finishRenderingInvalidLines(ctx, invalidLinesHTML, wasInvalid) {\n    const hugeDomNode = document.createElement('div');\n    if (ViewLayerRenderer._ttPolicy) {\n      invalidLinesHTML = ViewLayerRenderer._ttPolicy.createHTML(invalidLinesHTML);\n    }\n    hugeDomNode.innerHTML = invalidLinesHTML;\n    for (let i = 0; i < ctx.linesLength; i++) {\n      const line = ctx.lines[i];\n      if (wasInvalid[i]) {\n        const source = hugeDomNode.firstChild;\n        const lineDomNode = line.getDomNode();\n        lineDomNode.parentNode.replaceChild(source, lineDomNode);\n        line.setDomNode(source);\n      }\n    }\n  }\n  static {\n    this._sb = new StringBuilder(100000);\n  }\n  _finishRendering(ctx, domNodeIsEmpty, deltaTop) {\n    const sb = ViewLayerRenderer._sb;\n    const linesLength = ctx.linesLength;\n    const lines = ctx.lines;\n    const rendLineNumberStart = ctx.rendLineNumberStart;\n    const wasNew = [];\n    {\n      sb.reset();\n      let hadNewLine = false;\n      for (let i = 0; i < linesLength; i++) {\n        const line = lines[i];\n        wasNew[i] = false;\n        const lineDomNode = line.getDomNode();\n        if (lineDomNode) {\n          // line is not new\n          continue;\n        }\n        const renderResult = line.renderLine(i + rendLineNumberStart, deltaTop[i], this._viewportData.lineHeight, this._viewportData, sb);\n        if (!renderResult) {\n          // line does not need rendering\n          continue;\n        }\n        wasNew[i] = true;\n        hadNewLine = true;\n      }\n      if (hadNewLine) {\n        this._finishRenderingNewLines(ctx, domNodeIsEmpty, sb.build(), wasNew);\n      }\n    }\n    {\n      sb.reset();\n      let hadInvalidLine = false;\n      const wasInvalid = [];\n      for (let i = 0; i < linesLength; i++) {\n        const line = lines[i];\n        wasInvalid[i] = false;\n        if (wasNew[i]) {\n          // line was new\n          continue;\n        }\n        const renderResult = line.renderLine(i + rendLineNumberStart, deltaTop[i], this._viewportData.lineHeight, this._viewportData, sb);\n        if (!renderResult) {\n          // line does not need rendering\n          continue;\n        }\n        wasInvalid[i] = true;\n        hadInvalidLine = true;\n      }\n      if (hadInvalidLine) {\n        this._finishRenderingInvalidLines(ctx, sb.build(), wasInvalid);\n      }\n    }\n  }\n}", "map": {"version": 3, "names": ["createFastDomNode", "createTrustedTypesPolicy", "BugIndicatingError", "StringBuilder", "RenderedLinesCollection", "constructor", "_lineFactory", "_set", "flush", "rendLineNumberStart", "lines", "_lines", "_rendLineNumberStart", "_get", "getStartLineNumber", "getEndLineNumber", "length", "getCount", "getLine", "lineNumber", "lineIndex", "onLinesDeleted", "deleteFromLineNumber", "deleteToLineNumber", "startLineNumber", "endLineNumber", "deleteCnt", "deleteStartIndex", "deleteCount", "deleteAboveCount", "deleted", "splice", "onLinesChanged", "changeFromLineNumber", "changeCount", "changeToLineNumber", "someoneNotified", "changedLineNumber", "onContentChanged", "onLinesInserted", "insertFromLineNumber", "insertToLineNumber", "insertCnt", "newLines", "i", "createLine", "insertIndex", "beforeLines", "slice", "afterLines", "deletedLines", "concat", "onTokensChanged", "ranges", "notifiedSomeone", "len", "rng", "toLineNumber", "fromLineNumber", "from", "Math", "max", "to", "min", "VisibleLinesCollection", "domNode", "_createDomNode", "_linesCollection", "document", "createElement", "setClassName", "setPosition", "setAttribute", "onConfigurationChanged", "e", "has<PERSON><PERSON>ed", "onFlushed", "count", "lineDomNode", "getDomNode", "remove", "onScrollChanged", "scrollTopChanged", "onZonesChanged", "getVisibleLine", "renderLines", "viewportData", "inp", "renderer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ctx", "linesLength", "resCtx", "render", "relativeVerticalOffset", "_ttPolicy", "createHTML", "value", "_domNode", "_viewportData", "inContext", "stopLineNumber", "deltaTop", "x", "_finishRendering", "_renderUntouchedLines", "_insertLinesBefore", "removeCnt", "_removeLinesBefore", "_insertLinesAfter", "_removeLinesAfter", "startIndex", "endIndex", "deltaLN", "layoutLine", "lineHeight", "newLinesLen", "removeCount", "removeIndex", "_finishRenderingNewLines", "domNodeIsEmpty", "newLinesHTML", "wasNew", "<PERSON><PERSON><PERSON><PERSON>", "innerHTML", "insertAdjacentHTML", "curr<PERSON><PERSON><PERSON>", "line", "setDomNode", "previousSibling", "_finishRenderingInvalidLines", "invalidLinesHTML", "wasInvalid", "hugeDomNode", "source", "<PERSON><PERSON><PERSON><PERSON>", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "_sb", "sb", "reset", "hadNewLine", "renderResult", "renderLine", "build", "hadInvalidLine"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/browser/view/viewLayer.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { createFastDomNode } from '../../../base/browser/fastDomNode.js';\nimport { createTrustedTypesPolicy } from '../../../base/browser/trustedTypes.js';\nimport { BugIndicatingError } from '../../../base/common/errors.js';\nimport { StringBuilder } from '../../common/core/stringBuilder.js';\nexport class RenderedLinesCollection {\n    constructor(_lineFactory) {\n        this._lineFactory = _lineFactory;\n        this._set(1, []);\n    }\n    flush() {\n        this._set(1, []);\n    }\n    _set(rendLineNumberStart, lines) {\n        this._lines = lines;\n        this._rendLineNumberStart = rendLineNumberStart;\n    }\n    _get() {\n        return {\n            rendLineNumberStart: this._rendLineNumberStart,\n            lines: this._lines\n        };\n    }\n    /**\n     * @returns Inclusive line number that is inside this collection\n     */\n    getStartLineNumber() {\n        return this._rendLineNumberStart;\n    }\n    /**\n     * @returns Inclusive line number that is inside this collection\n     */\n    getEndLineNumber() {\n        return this._rendLineNumberStart + this._lines.length - 1;\n    }\n    getCount() {\n        return this._lines.length;\n    }\n    getLine(lineNumber) {\n        const lineIndex = lineNumber - this._rendLineNumberStart;\n        if (lineIndex < 0 || lineIndex >= this._lines.length) {\n            throw new BugIndicatingError('Illegal value for lineNumber');\n        }\n        return this._lines[lineIndex];\n    }\n    /**\n     * @returns Lines that were removed from this collection\n     */\n    onLinesDeleted(deleteFromLineNumber, deleteToLineNumber) {\n        if (this.getCount() === 0) {\n            // no lines\n            return null;\n        }\n        const startLineNumber = this.getStartLineNumber();\n        const endLineNumber = this.getEndLineNumber();\n        if (deleteToLineNumber < startLineNumber) {\n            // deleting above the viewport\n            const deleteCnt = deleteToLineNumber - deleteFromLineNumber + 1;\n            this._rendLineNumberStart -= deleteCnt;\n            return null;\n        }\n        if (deleteFromLineNumber > endLineNumber) {\n            // deleted below the viewport\n            return null;\n        }\n        // Record what needs to be deleted\n        let deleteStartIndex = 0;\n        let deleteCount = 0;\n        for (let lineNumber = startLineNumber; lineNumber <= endLineNumber; lineNumber++) {\n            const lineIndex = lineNumber - this._rendLineNumberStart;\n            if (deleteFromLineNumber <= lineNumber && lineNumber <= deleteToLineNumber) {\n                // this is a line to be deleted\n                if (deleteCount === 0) {\n                    // this is the first line to be deleted\n                    deleteStartIndex = lineIndex;\n                    deleteCount = 1;\n                }\n                else {\n                    deleteCount++;\n                }\n            }\n        }\n        // Adjust this._rendLineNumberStart for lines deleted above\n        if (deleteFromLineNumber < startLineNumber) {\n            // Something was deleted above\n            let deleteAboveCount = 0;\n            if (deleteToLineNumber < startLineNumber) {\n                // the entire deleted lines are above\n                deleteAboveCount = deleteToLineNumber - deleteFromLineNumber + 1;\n            }\n            else {\n                deleteAboveCount = startLineNumber - deleteFromLineNumber;\n            }\n            this._rendLineNumberStart -= deleteAboveCount;\n        }\n        const deleted = this._lines.splice(deleteStartIndex, deleteCount);\n        return deleted;\n    }\n    onLinesChanged(changeFromLineNumber, changeCount) {\n        const changeToLineNumber = changeFromLineNumber + changeCount - 1;\n        if (this.getCount() === 0) {\n            // no lines\n            return false;\n        }\n        const startLineNumber = this.getStartLineNumber();\n        const endLineNumber = this.getEndLineNumber();\n        let someoneNotified = false;\n        for (let changedLineNumber = changeFromLineNumber; changedLineNumber <= changeToLineNumber; changedLineNumber++) {\n            if (changedLineNumber >= startLineNumber && changedLineNumber <= endLineNumber) {\n                // Notify the line\n                this._lines[changedLineNumber - this._rendLineNumberStart].onContentChanged();\n                someoneNotified = true;\n            }\n        }\n        return someoneNotified;\n    }\n    onLinesInserted(insertFromLineNumber, insertToLineNumber) {\n        if (this.getCount() === 0) {\n            // no lines\n            return null;\n        }\n        const insertCnt = insertToLineNumber - insertFromLineNumber + 1;\n        const startLineNumber = this.getStartLineNumber();\n        const endLineNumber = this.getEndLineNumber();\n        if (insertFromLineNumber <= startLineNumber) {\n            // inserting above the viewport\n            this._rendLineNumberStart += insertCnt;\n            return null;\n        }\n        if (insertFromLineNumber > endLineNumber) {\n            // inserting below the viewport\n            return null;\n        }\n        if (insertCnt + insertFromLineNumber > endLineNumber) {\n            // insert inside the viewport in such a way that all remaining lines are pushed outside\n            const deleted = this._lines.splice(insertFromLineNumber - this._rendLineNumberStart, endLineNumber - insertFromLineNumber + 1);\n            return deleted;\n        }\n        // insert inside the viewport, push out some lines, but not all remaining lines\n        const newLines = [];\n        for (let i = 0; i < insertCnt; i++) {\n            newLines[i] = this._lineFactory.createLine();\n        }\n        const insertIndex = insertFromLineNumber - this._rendLineNumberStart;\n        const beforeLines = this._lines.slice(0, insertIndex);\n        const afterLines = this._lines.slice(insertIndex, this._lines.length - insertCnt);\n        const deletedLines = this._lines.slice(this._lines.length - insertCnt, this._lines.length);\n        this._lines = beforeLines.concat(newLines).concat(afterLines);\n        return deletedLines;\n    }\n    onTokensChanged(ranges) {\n        if (this.getCount() === 0) {\n            // no lines\n            return false;\n        }\n        const startLineNumber = this.getStartLineNumber();\n        const endLineNumber = this.getEndLineNumber();\n        let notifiedSomeone = false;\n        for (let i = 0, len = ranges.length; i < len; i++) {\n            const rng = ranges[i];\n            if (rng.toLineNumber < startLineNumber || rng.fromLineNumber > endLineNumber) {\n                // range outside viewport\n                continue;\n            }\n            const from = Math.max(startLineNumber, rng.fromLineNumber);\n            const to = Math.min(endLineNumber, rng.toLineNumber);\n            for (let lineNumber = from; lineNumber <= to; lineNumber++) {\n                const lineIndex = lineNumber - this._rendLineNumberStart;\n                this._lines[lineIndex].onTokensChanged();\n                notifiedSomeone = true;\n            }\n        }\n        return notifiedSomeone;\n    }\n}\nexport class VisibleLinesCollection {\n    constructor(_lineFactory) {\n        this._lineFactory = _lineFactory;\n        this.domNode = this._createDomNode();\n        this._linesCollection = new RenderedLinesCollection(this._lineFactory);\n    }\n    _createDomNode() {\n        const domNode = createFastDomNode(document.createElement('div'));\n        domNode.setClassName('view-layer');\n        domNode.setPosition('absolute');\n        domNode.domNode.setAttribute('role', 'presentation');\n        domNode.domNode.setAttribute('aria-hidden', 'true');\n        return domNode;\n    }\n    // ---- begin view event handlers\n    onConfigurationChanged(e) {\n        if (e.hasChanged(146 /* EditorOption.layoutInfo */)) {\n            return true;\n        }\n        return false;\n    }\n    onFlushed(e) {\n        this._linesCollection.flush();\n        // No need to clear the dom node because a full .innerHTML will occur in ViewLayerRenderer._render\n        return true;\n    }\n    onLinesChanged(e) {\n        return this._linesCollection.onLinesChanged(e.fromLineNumber, e.count);\n    }\n    onLinesDeleted(e) {\n        const deleted = this._linesCollection.onLinesDeleted(e.fromLineNumber, e.toLineNumber);\n        if (deleted) {\n            // Remove from DOM\n            for (let i = 0, len = deleted.length; i < len; i++) {\n                const lineDomNode = deleted[i].getDomNode();\n                lineDomNode?.remove();\n            }\n        }\n        return true;\n    }\n    onLinesInserted(e) {\n        const deleted = this._linesCollection.onLinesInserted(e.fromLineNumber, e.toLineNumber);\n        if (deleted) {\n            // Remove from DOM\n            for (let i = 0, len = deleted.length; i < len; i++) {\n                const lineDomNode = deleted[i].getDomNode();\n                lineDomNode?.remove();\n            }\n        }\n        return true;\n    }\n    onScrollChanged(e) {\n        return e.scrollTopChanged;\n    }\n    onTokensChanged(e) {\n        return this._linesCollection.onTokensChanged(e.ranges);\n    }\n    onZonesChanged(e) {\n        return true;\n    }\n    // ---- end view event handlers\n    getStartLineNumber() {\n        return this._linesCollection.getStartLineNumber();\n    }\n    getEndLineNumber() {\n        return this._linesCollection.getEndLineNumber();\n    }\n    getVisibleLine(lineNumber) {\n        return this._linesCollection.getLine(lineNumber);\n    }\n    renderLines(viewportData) {\n        const inp = this._linesCollection._get();\n        const renderer = new ViewLayerRenderer(this.domNode.domNode, this._lineFactory, viewportData);\n        const ctx = {\n            rendLineNumberStart: inp.rendLineNumberStart,\n            lines: inp.lines,\n            linesLength: inp.lines.length\n        };\n        // Decide if this render will do a single update (single large .innerHTML) or many updates (inserting/removing dom nodes)\n        const resCtx = renderer.render(ctx, viewportData.startLineNumber, viewportData.endLineNumber, viewportData.relativeVerticalOffset);\n        this._linesCollection._set(resCtx.rendLineNumberStart, resCtx.lines);\n    }\n}\nclass ViewLayerRenderer {\n    static { this._ttPolicy = createTrustedTypesPolicy('editorViewLayer', { createHTML: value => value }); }\n    constructor(_domNode, _lineFactory, _viewportData) {\n        this._domNode = _domNode;\n        this._lineFactory = _lineFactory;\n        this._viewportData = _viewportData;\n    }\n    render(inContext, startLineNumber, stopLineNumber, deltaTop) {\n        const ctx = {\n            rendLineNumberStart: inContext.rendLineNumberStart,\n            lines: inContext.lines.slice(0),\n            linesLength: inContext.linesLength\n        };\n        if ((ctx.rendLineNumberStart + ctx.linesLength - 1 < startLineNumber) || (stopLineNumber < ctx.rendLineNumberStart)) {\n            // There is no overlap whatsoever\n            ctx.rendLineNumberStart = startLineNumber;\n            ctx.linesLength = stopLineNumber - startLineNumber + 1;\n            ctx.lines = [];\n            for (let x = startLineNumber; x <= stopLineNumber; x++) {\n                ctx.lines[x - startLineNumber] = this._lineFactory.createLine();\n            }\n            this._finishRendering(ctx, true, deltaTop);\n            return ctx;\n        }\n        // Update lines which will remain untouched\n        this._renderUntouchedLines(ctx, Math.max(startLineNumber - ctx.rendLineNumberStart, 0), Math.min(stopLineNumber - ctx.rendLineNumberStart, ctx.linesLength - 1), deltaTop, startLineNumber);\n        if (ctx.rendLineNumberStart > startLineNumber) {\n            // Insert lines before\n            const fromLineNumber = startLineNumber;\n            const toLineNumber = Math.min(stopLineNumber, ctx.rendLineNumberStart - 1);\n            if (fromLineNumber <= toLineNumber) {\n                this._insertLinesBefore(ctx, fromLineNumber, toLineNumber, deltaTop, startLineNumber);\n                ctx.linesLength += toLineNumber - fromLineNumber + 1;\n            }\n        }\n        else if (ctx.rendLineNumberStart < startLineNumber) {\n            // Remove lines before\n            const removeCnt = Math.min(ctx.linesLength, startLineNumber - ctx.rendLineNumberStart);\n            if (removeCnt > 0) {\n                this._removeLinesBefore(ctx, removeCnt);\n                ctx.linesLength -= removeCnt;\n            }\n        }\n        ctx.rendLineNumberStart = startLineNumber;\n        if (ctx.rendLineNumberStart + ctx.linesLength - 1 < stopLineNumber) {\n            // Insert lines after\n            const fromLineNumber = ctx.rendLineNumberStart + ctx.linesLength;\n            const toLineNumber = stopLineNumber;\n            if (fromLineNumber <= toLineNumber) {\n                this._insertLinesAfter(ctx, fromLineNumber, toLineNumber, deltaTop, startLineNumber);\n                ctx.linesLength += toLineNumber - fromLineNumber + 1;\n            }\n        }\n        else if (ctx.rendLineNumberStart + ctx.linesLength - 1 > stopLineNumber) {\n            // Remove lines after\n            const fromLineNumber = Math.max(0, stopLineNumber - ctx.rendLineNumberStart + 1);\n            const toLineNumber = ctx.linesLength - 1;\n            const removeCnt = toLineNumber - fromLineNumber + 1;\n            if (removeCnt > 0) {\n                this._removeLinesAfter(ctx, removeCnt);\n                ctx.linesLength -= removeCnt;\n            }\n        }\n        this._finishRendering(ctx, false, deltaTop);\n        return ctx;\n    }\n    _renderUntouchedLines(ctx, startIndex, endIndex, deltaTop, deltaLN) {\n        const rendLineNumberStart = ctx.rendLineNumberStart;\n        const lines = ctx.lines;\n        for (let i = startIndex; i <= endIndex; i++) {\n            const lineNumber = rendLineNumberStart + i;\n            lines[i].layoutLine(lineNumber, deltaTop[lineNumber - deltaLN], this._viewportData.lineHeight);\n        }\n    }\n    _insertLinesBefore(ctx, fromLineNumber, toLineNumber, deltaTop, deltaLN) {\n        const newLines = [];\n        let newLinesLen = 0;\n        for (let lineNumber = fromLineNumber; lineNumber <= toLineNumber; lineNumber++) {\n            newLines[newLinesLen++] = this._lineFactory.createLine();\n        }\n        ctx.lines = newLines.concat(ctx.lines);\n    }\n    _removeLinesBefore(ctx, removeCount) {\n        for (let i = 0; i < removeCount; i++) {\n            const lineDomNode = ctx.lines[i].getDomNode();\n            lineDomNode?.remove();\n        }\n        ctx.lines.splice(0, removeCount);\n    }\n    _insertLinesAfter(ctx, fromLineNumber, toLineNumber, deltaTop, deltaLN) {\n        const newLines = [];\n        let newLinesLen = 0;\n        for (let lineNumber = fromLineNumber; lineNumber <= toLineNumber; lineNumber++) {\n            newLines[newLinesLen++] = this._lineFactory.createLine();\n        }\n        ctx.lines = ctx.lines.concat(newLines);\n    }\n    _removeLinesAfter(ctx, removeCount) {\n        const removeIndex = ctx.linesLength - removeCount;\n        for (let i = 0; i < removeCount; i++) {\n            const lineDomNode = ctx.lines[removeIndex + i].getDomNode();\n            lineDomNode?.remove();\n        }\n        ctx.lines.splice(removeIndex, removeCount);\n    }\n    _finishRenderingNewLines(ctx, domNodeIsEmpty, newLinesHTML, wasNew) {\n        if (ViewLayerRenderer._ttPolicy) {\n            newLinesHTML = ViewLayerRenderer._ttPolicy.createHTML(newLinesHTML);\n        }\n        const lastChild = this._domNode.lastChild;\n        if (domNodeIsEmpty || !lastChild) {\n            this._domNode.innerHTML = newLinesHTML; // explains the ugly casts -> https://github.com/microsoft/vscode/issues/106396#issuecomment-692625393;\n        }\n        else {\n            lastChild.insertAdjacentHTML('afterend', newLinesHTML);\n        }\n        let currChild = this._domNode.lastChild;\n        for (let i = ctx.linesLength - 1; i >= 0; i--) {\n            const line = ctx.lines[i];\n            if (wasNew[i]) {\n                line.setDomNode(currChild);\n                currChild = currChild.previousSibling;\n            }\n        }\n    }\n    _finishRenderingInvalidLines(ctx, invalidLinesHTML, wasInvalid) {\n        const hugeDomNode = document.createElement('div');\n        if (ViewLayerRenderer._ttPolicy) {\n            invalidLinesHTML = ViewLayerRenderer._ttPolicy.createHTML(invalidLinesHTML);\n        }\n        hugeDomNode.innerHTML = invalidLinesHTML;\n        for (let i = 0; i < ctx.linesLength; i++) {\n            const line = ctx.lines[i];\n            if (wasInvalid[i]) {\n                const source = hugeDomNode.firstChild;\n                const lineDomNode = line.getDomNode();\n                lineDomNode.parentNode.replaceChild(source, lineDomNode);\n                line.setDomNode(source);\n            }\n        }\n    }\n    static { this._sb = new StringBuilder(100000); }\n    _finishRendering(ctx, domNodeIsEmpty, deltaTop) {\n        const sb = ViewLayerRenderer._sb;\n        const linesLength = ctx.linesLength;\n        const lines = ctx.lines;\n        const rendLineNumberStart = ctx.rendLineNumberStart;\n        const wasNew = [];\n        {\n            sb.reset();\n            let hadNewLine = false;\n            for (let i = 0; i < linesLength; i++) {\n                const line = lines[i];\n                wasNew[i] = false;\n                const lineDomNode = line.getDomNode();\n                if (lineDomNode) {\n                    // line is not new\n                    continue;\n                }\n                const renderResult = line.renderLine(i + rendLineNumberStart, deltaTop[i], this._viewportData.lineHeight, this._viewportData, sb);\n                if (!renderResult) {\n                    // line does not need rendering\n                    continue;\n                }\n                wasNew[i] = true;\n                hadNewLine = true;\n            }\n            if (hadNewLine) {\n                this._finishRenderingNewLines(ctx, domNodeIsEmpty, sb.build(), wasNew);\n            }\n        }\n        {\n            sb.reset();\n            let hadInvalidLine = false;\n            const wasInvalid = [];\n            for (let i = 0; i < linesLength; i++) {\n                const line = lines[i];\n                wasInvalid[i] = false;\n                if (wasNew[i]) {\n                    // line was new\n                    continue;\n                }\n                const renderResult = line.renderLine(i + rendLineNumberStart, deltaTop[i], this._viewportData.lineHeight, this._viewportData, sb);\n                if (!renderResult) {\n                    // line does not need rendering\n                    continue;\n                }\n                wasInvalid[i] = true;\n                hadInvalidLine = true;\n            }\n            if (hadInvalidLine) {\n                this._finishRenderingInvalidLines(ctx, sb.build(), wasInvalid);\n            }\n        }\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,iBAAiB,QAAQ,sCAAsC;AACxE,SAASC,wBAAwB,QAAQ,uCAAuC;AAChF,SAASC,kBAAkB,QAAQ,gCAAgC;AACnE,SAASC,aAAa,QAAQ,oCAAoC;AAClE,OAAO,MAAMC,uBAAuB,CAAC;EACjCC,WAAWA,CAACC,YAAY,EAAE;IACtB,IAAI,CAACA,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC;EACpB;EACAC,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACD,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC;EACpB;EACAA,IAAIA,CAACE,mBAAmB,EAAEC,KAAK,EAAE;IAC7B,IAAI,CAACC,MAAM,GAAGD,KAAK;IACnB,IAAI,CAACE,oBAAoB,GAAGH,mBAAmB;EACnD;EACAI,IAAIA,CAAA,EAAG;IACH,OAAO;MACHJ,mBAAmB,EAAE,IAAI,CAACG,oBAAoB;MAC9CF,KAAK,EAAE,IAAI,CAACC;IAChB,CAAC;EACL;EACA;AACJ;AACA;EACIG,kBAAkBA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACF,oBAAoB;EACpC;EACA;AACJ;AACA;EACIG,gBAAgBA,CAAA,EAAG;IACf,OAAO,IAAI,CAACH,oBAAoB,GAAG,IAAI,CAACD,MAAM,CAACK,MAAM,GAAG,CAAC;EAC7D;EACAC,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACN,MAAM,CAACK,MAAM;EAC7B;EACAE,OAAOA,CAACC,UAAU,EAAE;IAChB,MAAMC,SAAS,GAAGD,UAAU,GAAG,IAAI,CAACP,oBAAoB;IACxD,IAAIQ,SAAS,GAAG,CAAC,IAAIA,SAAS,IAAI,IAAI,CAACT,MAAM,CAACK,MAAM,EAAE;MAClD,MAAM,IAAId,kBAAkB,CAAC,8BAA8B,CAAC;IAChE;IACA,OAAO,IAAI,CAACS,MAAM,CAACS,SAAS,CAAC;EACjC;EACA;AACJ;AACA;EACIC,cAAcA,CAACC,oBAAoB,EAAEC,kBAAkB,EAAE;IACrD,IAAI,IAAI,CAACN,QAAQ,CAAC,CAAC,KAAK,CAAC,EAAE;MACvB;MACA,OAAO,IAAI;IACf;IACA,MAAMO,eAAe,GAAG,IAAI,CAACV,kBAAkB,CAAC,CAAC;IACjD,MAAMW,aAAa,GAAG,IAAI,CAACV,gBAAgB,CAAC,CAAC;IAC7C,IAAIQ,kBAAkB,GAAGC,eAAe,EAAE;MACtC;MACA,MAAME,SAAS,GAAGH,kBAAkB,GAAGD,oBAAoB,GAAG,CAAC;MAC/D,IAAI,CAACV,oBAAoB,IAAIc,SAAS;MACtC,OAAO,IAAI;IACf;IACA,IAAIJ,oBAAoB,GAAGG,aAAa,EAAE;MACtC;MACA,OAAO,IAAI;IACf;IACA;IACA,IAAIE,gBAAgB,GAAG,CAAC;IACxB,IAAIC,WAAW,GAAG,CAAC;IACnB,KAAK,IAAIT,UAAU,GAAGK,eAAe,EAAEL,UAAU,IAAIM,aAAa,EAAEN,UAAU,EAAE,EAAE;MAC9E,MAAMC,SAAS,GAAGD,UAAU,GAAG,IAAI,CAACP,oBAAoB;MACxD,IAAIU,oBAAoB,IAAIH,UAAU,IAAIA,UAAU,IAAII,kBAAkB,EAAE;QACxE;QACA,IAAIK,WAAW,KAAK,CAAC,EAAE;UACnB;UACAD,gBAAgB,GAAGP,SAAS;UAC5BQ,WAAW,GAAG,CAAC;QACnB,CAAC,MACI;UACDA,WAAW,EAAE;QACjB;MACJ;IACJ;IACA;IACA,IAAIN,oBAAoB,GAAGE,eAAe,EAAE;MACxC;MACA,IAAIK,gBAAgB,GAAG,CAAC;MACxB,IAAIN,kBAAkB,GAAGC,eAAe,EAAE;QACtC;QACAK,gBAAgB,GAAGN,kBAAkB,GAAGD,oBAAoB,GAAG,CAAC;MACpE,CAAC,MACI;QACDO,gBAAgB,GAAGL,eAAe,GAAGF,oBAAoB;MAC7D;MACA,IAAI,CAACV,oBAAoB,IAAIiB,gBAAgB;IACjD;IACA,MAAMC,OAAO,GAAG,IAAI,CAACnB,MAAM,CAACoB,MAAM,CAACJ,gBAAgB,EAAEC,WAAW,CAAC;IACjE,OAAOE,OAAO;EAClB;EACAE,cAAcA,CAACC,oBAAoB,EAAEC,WAAW,EAAE;IAC9C,MAAMC,kBAAkB,GAAGF,oBAAoB,GAAGC,WAAW,GAAG,CAAC;IACjE,IAAI,IAAI,CAACjB,QAAQ,CAAC,CAAC,KAAK,CAAC,EAAE;MACvB;MACA,OAAO,KAAK;IAChB;IACA,MAAMO,eAAe,GAAG,IAAI,CAACV,kBAAkB,CAAC,CAAC;IACjD,MAAMW,aAAa,GAAG,IAAI,CAACV,gBAAgB,CAAC,CAAC;IAC7C,IAAIqB,eAAe,GAAG,KAAK;IAC3B,KAAK,IAAIC,iBAAiB,GAAGJ,oBAAoB,EAAEI,iBAAiB,IAAIF,kBAAkB,EAAEE,iBAAiB,EAAE,EAAE;MAC7G,IAAIA,iBAAiB,IAAIb,eAAe,IAAIa,iBAAiB,IAAIZ,aAAa,EAAE;QAC5E;QACA,IAAI,CAACd,MAAM,CAAC0B,iBAAiB,GAAG,IAAI,CAACzB,oBAAoB,CAAC,CAAC0B,gBAAgB,CAAC,CAAC;QAC7EF,eAAe,GAAG,IAAI;MAC1B;IACJ;IACA,OAAOA,eAAe;EAC1B;EACAG,eAAeA,CAACC,oBAAoB,EAAEC,kBAAkB,EAAE;IACtD,IAAI,IAAI,CAACxB,QAAQ,CAAC,CAAC,KAAK,CAAC,EAAE;MACvB;MACA,OAAO,IAAI;IACf;IACA,MAAMyB,SAAS,GAAGD,kBAAkB,GAAGD,oBAAoB,GAAG,CAAC;IAC/D,MAAMhB,eAAe,GAAG,IAAI,CAACV,kBAAkB,CAAC,CAAC;IACjD,MAAMW,aAAa,GAAG,IAAI,CAACV,gBAAgB,CAAC,CAAC;IAC7C,IAAIyB,oBAAoB,IAAIhB,eAAe,EAAE;MACzC;MACA,IAAI,CAACZ,oBAAoB,IAAI8B,SAAS;MACtC,OAAO,IAAI;IACf;IACA,IAAIF,oBAAoB,GAAGf,aAAa,EAAE;MACtC;MACA,OAAO,IAAI;IACf;IACA,IAAIiB,SAAS,GAAGF,oBAAoB,GAAGf,aAAa,EAAE;MAClD;MACA,MAAMK,OAAO,GAAG,IAAI,CAACnB,MAAM,CAACoB,MAAM,CAACS,oBAAoB,GAAG,IAAI,CAAC5B,oBAAoB,EAAEa,aAAa,GAAGe,oBAAoB,GAAG,CAAC,CAAC;MAC9H,OAAOV,OAAO;IAClB;IACA;IACA,MAAMa,QAAQ,GAAG,EAAE;IACnB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,SAAS,EAAEE,CAAC,EAAE,EAAE;MAChCD,QAAQ,CAACC,CAAC,CAAC,GAAG,IAAI,CAACtC,YAAY,CAACuC,UAAU,CAAC,CAAC;IAChD;IACA,MAAMC,WAAW,GAAGN,oBAAoB,GAAG,IAAI,CAAC5B,oBAAoB;IACpE,MAAMmC,WAAW,GAAG,IAAI,CAACpC,MAAM,CAACqC,KAAK,CAAC,CAAC,EAAEF,WAAW,CAAC;IACrD,MAAMG,UAAU,GAAG,IAAI,CAACtC,MAAM,CAACqC,KAAK,CAACF,WAAW,EAAE,IAAI,CAACnC,MAAM,CAACK,MAAM,GAAG0B,SAAS,CAAC;IACjF,MAAMQ,YAAY,GAAG,IAAI,CAACvC,MAAM,CAACqC,KAAK,CAAC,IAAI,CAACrC,MAAM,CAACK,MAAM,GAAG0B,SAAS,EAAE,IAAI,CAAC/B,MAAM,CAACK,MAAM,CAAC;IAC1F,IAAI,CAACL,MAAM,GAAGoC,WAAW,CAACI,MAAM,CAACR,QAAQ,CAAC,CAACQ,MAAM,CAACF,UAAU,CAAC;IAC7D,OAAOC,YAAY;EACvB;EACAE,eAAeA,CAACC,MAAM,EAAE;IACpB,IAAI,IAAI,CAACpC,QAAQ,CAAC,CAAC,KAAK,CAAC,EAAE;MACvB;MACA,OAAO,KAAK;IAChB;IACA,MAAMO,eAAe,GAAG,IAAI,CAACV,kBAAkB,CAAC,CAAC;IACjD,MAAMW,aAAa,GAAG,IAAI,CAACV,gBAAgB,CAAC,CAAC;IAC7C,IAAIuC,eAAe,GAAG,KAAK;IAC3B,KAAK,IAAIV,CAAC,GAAG,CAAC,EAAEW,GAAG,GAAGF,MAAM,CAACrC,MAAM,EAAE4B,CAAC,GAAGW,GAAG,EAAEX,CAAC,EAAE,EAAE;MAC/C,MAAMY,GAAG,GAAGH,MAAM,CAACT,CAAC,CAAC;MACrB,IAAIY,GAAG,CAACC,YAAY,GAAGjC,eAAe,IAAIgC,GAAG,CAACE,cAAc,GAAGjC,aAAa,EAAE;QAC1E;QACA;MACJ;MACA,MAAMkC,IAAI,GAAGC,IAAI,CAACC,GAAG,CAACrC,eAAe,EAAEgC,GAAG,CAACE,cAAc,CAAC;MAC1D,MAAMI,EAAE,GAAGF,IAAI,CAACG,GAAG,CAACtC,aAAa,EAAE+B,GAAG,CAACC,YAAY,CAAC;MACpD,KAAK,IAAItC,UAAU,GAAGwC,IAAI,EAAExC,UAAU,IAAI2C,EAAE,EAAE3C,UAAU,EAAE,EAAE;QACxD,MAAMC,SAAS,GAAGD,UAAU,GAAG,IAAI,CAACP,oBAAoB;QACxD,IAAI,CAACD,MAAM,CAACS,SAAS,CAAC,CAACgC,eAAe,CAAC,CAAC;QACxCE,eAAe,GAAG,IAAI;MAC1B;IACJ;IACA,OAAOA,eAAe;EAC1B;AACJ;AACA,OAAO,MAAMU,sBAAsB,CAAC;EAChC3D,WAAWA,CAACC,YAAY,EAAE;IACtB,IAAI,CAACA,YAAY,GAAGA,YAAY;IAChC,IAAI,CAAC2D,OAAO,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACpC,IAAI,CAACC,gBAAgB,GAAG,IAAI/D,uBAAuB,CAAC,IAAI,CAACE,YAAY,CAAC;EAC1E;EACA4D,cAAcA,CAAA,EAAG;IACb,MAAMD,OAAO,GAAGjE,iBAAiB,CAACoE,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC,CAAC;IAChEJ,OAAO,CAACK,YAAY,CAAC,YAAY,CAAC;IAClCL,OAAO,CAACM,WAAW,CAAC,UAAU,CAAC;IAC/BN,OAAO,CAACA,OAAO,CAACO,YAAY,CAAC,MAAM,EAAE,cAAc,CAAC;IACpDP,OAAO,CAACA,OAAO,CAACO,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;IACnD,OAAOP,OAAO;EAClB;EACA;EACAQ,sBAAsBA,CAACC,CAAC,EAAE;IACtB,IAAIA,CAAC,CAACC,UAAU,CAAC,GAAG,CAAC,6BAA6B,CAAC,EAAE;MACjD,OAAO,IAAI;IACf;IACA,OAAO,KAAK;EAChB;EACAC,SAASA,CAACF,CAAC,EAAE;IACT,IAAI,CAACP,gBAAgB,CAAC3D,KAAK,CAAC,CAAC;IAC7B;IACA,OAAO,IAAI;EACf;EACAwB,cAAcA,CAAC0C,CAAC,EAAE;IACd,OAAO,IAAI,CAACP,gBAAgB,CAACnC,cAAc,CAAC0C,CAAC,CAAChB,cAAc,EAAEgB,CAAC,CAACG,KAAK,CAAC;EAC1E;EACAxD,cAAcA,CAACqD,CAAC,EAAE;IACd,MAAM5C,OAAO,GAAG,IAAI,CAACqC,gBAAgB,CAAC9C,cAAc,CAACqD,CAAC,CAAChB,cAAc,EAAEgB,CAAC,CAACjB,YAAY,CAAC;IACtF,IAAI3B,OAAO,EAAE;MACT;MACA,KAAK,IAAIc,CAAC,GAAG,CAAC,EAAEW,GAAG,GAAGzB,OAAO,CAACd,MAAM,EAAE4B,CAAC,GAAGW,GAAG,EAAEX,CAAC,EAAE,EAAE;QAChD,MAAMkC,WAAW,GAAGhD,OAAO,CAACc,CAAC,CAAC,CAACmC,UAAU,CAAC,CAAC;QAC3CD,WAAW,EAAEE,MAAM,CAAC,CAAC;MACzB;IACJ;IACA,OAAO,IAAI;EACf;EACAzC,eAAeA,CAACmC,CAAC,EAAE;IACf,MAAM5C,OAAO,GAAG,IAAI,CAACqC,gBAAgB,CAAC5B,eAAe,CAACmC,CAAC,CAAChB,cAAc,EAAEgB,CAAC,CAACjB,YAAY,CAAC;IACvF,IAAI3B,OAAO,EAAE;MACT;MACA,KAAK,IAAIc,CAAC,GAAG,CAAC,EAAEW,GAAG,GAAGzB,OAAO,CAACd,MAAM,EAAE4B,CAAC,GAAGW,GAAG,EAAEX,CAAC,EAAE,EAAE;QAChD,MAAMkC,WAAW,GAAGhD,OAAO,CAACc,CAAC,CAAC,CAACmC,UAAU,CAAC,CAAC;QAC3CD,WAAW,EAAEE,MAAM,CAAC,CAAC;MACzB;IACJ;IACA,OAAO,IAAI;EACf;EACAC,eAAeA,CAACP,CAAC,EAAE;IACf,OAAOA,CAAC,CAACQ,gBAAgB;EAC7B;EACA9B,eAAeA,CAACsB,CAAC,EAAE;IACf,OAAO,IAAI,CAACP,gBAAgB,CAACf,eAAe,CAACsB,CAAC,CAACrB,MAAM,CAAC;EAC1D;EACA8B,cAAcA,CAACT,CAAC,EAAE;IACd,OAAO,IAAI;EACf;EACA;EACA5D,kBAAkBA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACqD,gBAAgB,CAACrD,kBAAkB,CAAC,CAAC;EACrD;EACAC,gBAAgBA,CAAA,EAAG;IACf,OAAO,IAAI,CAACoD,gBAAgB,CAACpD,gBAAgB,CAAC,CAAC;EACnD;EACAqE,cAAcA,CAACjE,UAAU,EAAE;IACvB,OAAO,IAAI,CAACgD,gBAAgB,CAACjD,OAAO,CAACC,UAAU,CAAC;EACpD;EACAkE,WAAWA,CAACC,YAAY,EAAE;IACtB,MAAMC,GAAG,GAAG,IAAI,CAACpB,gBAAgB,CAACtD,IAAI,CAAC,CAAC;IACxC,MAAM2E,QAAQ,GAAG,IAAIC,iBAAiB,CAAC,IAAI,CAACxB,OAAO,CAACA,OAAO,EAAE,IAAI,CAAC3D,YAAY,EAAEgF,YAAY,CAAC;IAC7F,MAAMI,GAAG,GAAG;MACRjF,mBAAmB,EAAE8E,GAAG,CAAC9E,mBAAmB;MAC5CC,KAAK,EAAE6E,GAAG,CAAC7E,KAAK;MAChBiF,WAAW,EAAEJ,GAAG,CAAC7E,KAAK,CAACM;IAC3B,CAAC;IACD;IACA,MAAM4E,MAAM,GAAGJ,QAAQ,CAACK,MAAM,CAACH,GAAG,EAAEJ,YAAY,CAAC9D,eAAe,EAAE8D,YAAY,CAAC7D,aAAa,EAAE6D,YAAY,CAACQ,sBAAsB,CAAC;IAClI,IAAI,CAAC3B,gBAAgB,CAAC5D,IAAI,CAACqF,MAAM,CAACnF,mBAAmB,EAAEmF,MAAM,CAAClF,KAAK,CAAC;EACxE;AACJ;AACA,MAAM+E,iBAAiB,CAAC;EACpB;IAAS,IAAI,CAACM,SAAS,GAAG9F,wBAAwB,CAAC,iBAAiB,EAAE;MAAE+F,UAAU,EAAEC,KAAK,IAAIA;IAAM,CAAC,CAAC;EAAE;EACvG5F,WAAWA,CAAC6F,QAAQ,EAAE5F,YAAY,EAAE6F,aAAa,EAAE;IAC/C,IAAI,CAACD,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAAC5F,YAAY,GAAGA,YAAY;IAChC,IAAI,CAAC6F,aAAa,GAAGA,aAAa;EACtC;EACAN,MAAMA,CAACO,SAAS,EAAE5E,eAAe,EAAE6E,cAAc,EAAEC,QAAQ,EAAE;IACzD,MAAMZ,GAAG,GAAG;MACRjF,mBAAmB,EAAE2F,SAAS,CAAC3F,mBAAmB;MAClDC,KAAK,EAAE0F,SAAS,CAAC1F,KAAK,CAACsC,KAAK,CAAC,CAAC,CAAC;MAC/B2C,WAAW,EAAES,SAAS,CAACT;IAC3B,CAAC;IACD,IAAKD,GAAG,CAACjF,mBAAmB,GAAGiF,GAAG,CAACC,WAAW,GAAG,CAAC,GAAGnE,eAAe,IAAM6E,cAAc,GAAGX,GAAG,CAACjF,mBAAoB,EAAE;MACjH;MACAiF,GAAG,CAACjF,mBAAmB,GAAGe,eAAe;MACzCkE,GAAG,CAACC,WAAW,GAAGU,cAAc,GAAG7E,eAAe,GAAG,CAAC;MACtDkE,GAAG,CAAChF,KAAK,GAAG,EAAE;MACd,KAAK,IAAI6F,CAAC,GAAG/E,eAAe,EAAE+E,CAAC,IAAIF,cAAc,EAAEE,CAAC,EAAE,EAAE;QACpDb,GAAG,CAAChF,KAAK,CAAC6F,CAAC,GAAG/E,eAAe,CAAC,GAAG,IAAI,CAAClB,YAAY,CAACuC,UAAU,CAAC,CAAC;MACnE;MACA,IAAI,CAAC2D,gBAAgB,CAACd,GAAG,EAAE,IAAI,EAAEY,QAAQ,CAAC;MAC1C,OAAOZ,GAAG;IACd;IACA;IACA,IAAI,CAACe,qBAAqB,CAACf,GAAG,EAAE9B,IAAI,CAACC,GAAG,CAACrC,eAAe,GAAGkE,GAAG,CAACjF,mBAAmB,EAAE,CAAC,CAAC,EAAEmD,IAAI,CAACG,GAAG,CAACsC,cAAc,GAAGX,GAAG,CAACjF,mBAAmB,EAAEiF,GAAG,CAACC,WAAW,GAAG,CAAC,CAAC,EAAEW,QAAQ,EAAE9E,eAAe,CAAC;IAC3L,IAAIkE,GAAG,CAACjF,mBAAmB,GAAGe,eAAe,EAAE;MAC3C;MACA,MAAMkC,cAAc,GAAGlC,eAAe;MACtC,MAAMiC,YAAY,GAAGG,IAAI,CAACG,GAAG,CAACsC,cAAc,EAAEX,GAAG,CAACjF,mBAAmB,GAAG,CAAC,CAAC;MAC1E,IAAIiD,cAAc,IAAID,YAAY,EAAE;QAChC,IAAI,CAACiD,kBAAkB,CAAChB,GAAG,EAAEhC,cAAc,EAAED,YAAY,EAAE6C,QAAQ,EAAE9E,eAAe,CAAC;QACrFkE,GAAG,CAACC,WAAW,IAAIlC,YAAY,GAAGC,cAAc,GAAG,CAAC;MACxD;IACJ,CAAC,MACI,IAAIgC,GAAG,CAACjF,mBAAmB,GAAGe,eAAe,EAAE;MAChD;MACA,MAAMmF,SAAS,GAAG/C,IAAI,CAACG,GAAG,CAAC2B,GAAG,CAACC,WAAW,EAAEnE,eAAe,GAAGkE,GAAG,CAACjF,mBAAmB,CAAC;MACtF,IAAIkG,SAAS,GAAG,CAAC,EAAE;QACf,IAAI,CAACC,kBAAkB,CAAClB,GAAG,EAAEiB,SAAS,CAAC;QACvCjB,GAAG,CAACC,WAAW,IAAIgB,SAAS;MAChC;IACJ;IACAjB,GAAG,CAACjF,mBAAmB,GAAGe,eAAe;IACzC,IAAIkE,GAAG,CAACjF,mBAAmB,GAAGiF,GAAG,CAACC,WAAW,GAAG,CAAC,GAAGU,cAAc,EAAE;MAChE;MACA,MAAM3C,cAAc,GAAGgC,GAAG,CAACjF,mBAAmB,GAAGiF,GAAG,CAACC,WAAW;MAChE,MAAMlC,YAAY,GAAG4C,cAAc;MACnC,IAAI3C,cAAc,IAAID,YAAY,EAAE;QAChC,IAAI,CAACoD,iBAAiB,CAACnB,GAAG,EAAEhC,cAAc,EAAED,YAAY,EAAE6C,QAAQ,EAAE9E,eAAe,CAAC;QACpFkE,GAAG,CAACC,WAAW,IAAIlC,YAAY,GAAGC,cAAc,GAAG,CAAC;MACxD;IACJ,CAAC,MACI,IAAIgC,GAAG,CAACjF,mBAAmB,GAAGiF,GAAG,CAACC,WAAW,GAAG,CAAC,GAAGU,cAAc,EAAE;MACrE;MACA,MAAM3C,cAAc,GAAGE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEwC,cAAc,GAAGX,GAAG,CAACjF,mBAAmB,GAAG,CAAC,CAAC;MAChF,MAAMgD,YAAY,GAAGiC,GAAG,CAACC,WAAW,GAAG,CAAC;MACxC,MAAMgB,SAAS,GAAGlD,YAAY,GAAGC,cAAc,GAAG,CAAC;MACnD,IAAIiD,SAAS,GAAG,CAAC,EAAE;QACf,IAAI,CAACG,iBAAiB,CAACpB,GAAG,EAAEiB,SAAS,CAAC;QACtCjB,GAAG,CAACC,WAAW,IAAIgB,SAAS;MAChC;IACJ;IACA,IAAI,CAACH,gBAAgB,CAACd,GAAG,EAAE,KAAK,EAAEY,QAAQ,CAAC;IAC3C,OAAOZ,GAAG;EACd;EACAe,qBAAqBA,CAACf,GAAG,EAAEqB,UAAU,EAAEC,QAAQ,EAAEV,QAAQ,EAAEW,OAAO,EAAE;IAChE,MAAMxG,mBAAmB,GAAGiF,GAAG,CAACjF,mBAAmB;IACnD,MAAMC,KAAK,GAAGgF,GAAG,CAAChF,KAAK;IACvB,KAAK,IAAIkC,CAAC,GAAGmE,UAAU,EAAEnE,CAAC,IAAIoE,QAAQ,EAAEpE,CAAC,EAAE,EAAE;MACzC,MAAMzB,UAAU,GAAGV,mBAAmB,GAAGmC,CAAC;MAC1ClC,KAAK,CAACkC,CAAC,CAAC,CAACsE,UAAU,CAAC/F,UAAU,EAAEmF,QAAQ,CAACnF,UAAU,GAAG8F,OAAO,CAAC,EAAE,IAAI,CAACd,aAAa,CAACgB,UAAU,CAAC;IAClG;EACJ;EACAT,kBAAkBA,CAAChB,GAAG,EAAEhC,cAAc,EAAED,YAAY,EAAE6C,QAAQ,EAAEW,OAAO,EAAE;IACrE,MAAMtE,QAAQ,GAAG,EAAE;IACnB,IAAIyE,WAAW,GAAG,CAAC;IACnB,KAAK,IAAIjG,UAAU,GAAGuC,cAAc,EAAEvC,UAAU,IAAIsC,YAAY,EAAEtC,UAAU,EAAE,EAAE;MAC5EwB,QAAQ,CAACyE,WAAW,EAAE,CAAC,GAAG,IAAI,CAAC9G,YAAY,CAACuC,UAAU,CAAC,CAAC;IAC5D;IACA6C,GAAG,CAAChF,KAAK,GAAGiC,QAAQ,CAACQ,MAAM,CAACuC,GAAG,CAAChF,KAAK,CAAC;EAC1C;EACAkG,kBAAkBA,CAAClB,GAAG,EAAE2B,WAAW,EAAE;IACjC,KAAK,IAAIzE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyE,WAAW,EAAEzE,CAAC,EAAE,EAAE;MAClC,MAAMkC,WAAW,GAAGY,GAAG,CAAChF,KAAK,CAACkC,CAAC,CAAC,CAACmC,UAAU,CAAC,CAAC;MAC7CD,WAAW,EAAEE,MAAM,CAAC,CAAC;IACzB;IACAU,GAAG,CAAChF,KAAK,CAACqB,MAAM,CAAC,CAAC,EAAEsF,WAAW,CAAC;EACpC;EACAR,iBAAiBA,CAACnB,GAAG,EAAEhC,cAAc,EAAED,YAAY,EAAE6C,QAAQ,EAAEW,OAAO,EAAE;IACpE,MAAMtE,QAAQ,GAAG,EAAE;IACnB,IAAIyE,WAAW,GAAG,CAAC;IACnB,KAAK,IAAIjG,UAAU,GAAGuC,cAAc,EAAEvC,UAAU,IAAIsC,YAAY,EAAEtC,UAAU,EAAE,EAAE;MAC5EwB,QAAQ,CAACyE,WAAW,EAAE,CAAC,GAAG,IAAI,CAAC9G,YAAY,CAACuC,UAAU,CAAC,CAAC;IAC5D;IACA6C,GAAG,CAAChF,KAAK,GAAGgF,GAAG,CAAChF,KAAK,CAACyC,MAAM,CAACR,QAAQ,CAAC;EAC1C;EACAmE,iBAAiBA,CAACpB,GAAG,EAAE2B,WAAW,EAAE;IAChC,MAAMC,WAAW,GAAG5B,GAAG,CAACC,WAAW,GAAG0B,WAAW;IACjD,KAAK,IAAIzE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyE,WAAW,EAAEzE,CAAC,EAAE,EAAE;MAClC,MAAMkC,WAAW,GAAGY,GAAG,CAAChF,KAAK,CAAC4G,WAAW,GAAG1E,CAAC,CAAC,CAACmC,UAAU,CAAC,CAAC;MAC3DD,WAAW,EAAEE,MAAM,CAAC,CAAC;IACzB;IACAU,GAAG,CAAChF,KAAK,CAACqB,MAAM,CAACuF,WAAW,EAAED,WAAW,CAAC;EAC9C;EACAE,wBAAwBA,CAAC7B,GAAG,EAAE8B,cAAc,EAAEC,YAAY,EAAEC,MAAM,EAAE;IAChE,IAAIjC,iBAAiB,CAACM,SAAS,EAAE;MAC7B0B,YAAY,GAAGhC,iBAAiB,CAACM,SAAS,CAACC,UAAU,CAACyB,YAAY,CAAC;IACvE;IACA,MAAME,SAAS,GAAG,IAAI,CAACzB,QAAQ,CAACyB,SAAS;IACzC,IAAIH,cAAc,IAAI,CAACG,SAAS,EAAE;MAC9B,IAAI,CAACzB,QAAQ,CAAC0B,SAAS,GAAGH,YAAY,CAAC,CAAC;IAC5C,CAAC,MACI;MACDE,SAAS,CAACE,kBAAkB,CAAC,UAAU,EAAEJ,YAAY,CAAC;IAC1D;IACA,IAAIK,SAAS,GAAG,IAAI,CAAC5B,QAAQ,CAACyB,SAAS;IACvC,KAAK,IAAI/E,CAAC,GAAG8C,GAAG,CAACC,WAAW,GAAG,CAAC,EAAE/C,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC3C,MAAMmF,IAAI,GAAGrC,GAAG,CAAChF,KAAK,CAACkC,CAAC,CAAC;MACzB,IAAI8E,MAAM,CAAC9E,CAAC,CAAC,EAAE;QACXmF,IAAI,CAACC,UAAU,CAACF,SAAS,CAAC;QAC1BA,SAAS,GAAGA,SAAS,CAACG,eAAe;MACzC;IACJ;EACJ;EACAC,4BAA4BA,CAACxC,GAAG,EAAEyC,gBAAgB,EAAEC,UAAU,EAAE;IAC5D,MAAMC,WAAW,GAAGjE,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IACjD,IAAIoB,iBAAiB,CAACM,SAAS,EAAE;MAC7BoC,gBAAgB,GAAG1C,iBAAiB,CAACM,SAAS,CAACC,UAAU,CAACmC,gBAAgB,CAAC;IAC/E;IACAE,WAAW,CAACT,SAAS,GAAGO,gBAAgB;IACxC,KAAK,IAAIvF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8C,GAAG,CAACC,WAAW,EAAE/C,CAAC,EAAE,EAAE;MACtC,MAAMmF,IAAI,GAAGrC,GAAG,CAAChF,KAAK,CAACkC,CAAC,CAAC;MACzB,IAAIwF,UAAU,CAACxF,CAAC,CAAC,EAAE;QACf,MAAM0F,MAAM,GAAGD,WAAW,CAACE,UAAU;QACrC,MAAMzD,WAAW,GAAGiD,IAAI,CAAChD,UAAU,CAAC,CAAC;QACrCD,WAAW,CAAC0D,UAAU,CAACC,YAAY,CAACH,MAAM,EAAExD,WAAW,CAAC;QACxDiD,IAAI,CAACC,UAAU,CAACM,MAAM,CAAC;MAC3B;IACJ;EACJ;EACA;IAAS,IAAI,CAACI,GAAG,GAAG,IAAIvI,aAAa,CAAC,MAAM,CAAC;EAAE;EAC/CqG,gBAAgBA,CAACd,GAAG,EAAE8B,cAAc,EAAElB,QAAQ,EAAE;IAC5C,MAAMqC,EAAE,GAAGlD,iBAAiB,CAACiD,GAAG;IAChC,MAAM/C,WAAW,GAAGD,GAAG,CAACC,WAAW;IACnC,MAAMjF,KAAK,GAAGgF,GAAG,CAAChF,KAAK;IACvB,MAAMD,mBAAmB,GAAGiF,GAAG,CAACjF,mBAAmB;IACnD,MAAMiH,MAAM,GAAG,EAAE;IACjB;MACIiB,EAAE,CAACC,KAAK,CAAC,CAAC;MACV,IAAIC,UAAU,GAAG,KAAK;MACtB,KAAK,IAAIjG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+C,WAAW,EAAE/C,CAAC,EAAE,EAAE;QAClC,MAAMmF,IAAI,GAAGrH,KAAK,CAACkC,CAAC,CAAC;QACrB8E,MAAM,CAAC9E,CAAC,CAAC,GAAG,KAAK;QACjB,MAAMkC,WAAW,GAAGiD,IAAI,CAAChD,UAAU,CAAC,CAAC;QACrC,IAAID,WAAW,EAAE;UACb;UACA;QACJ;QACA,MAAMgE,YAAY,GAAGf,IAAI,CAACgB,UAAU,CAACnG,CAAC,GAAGnC,mBAAmB,EAAE6F,QAAQ,CAAC1D,CAAC,CAAC,EAAE,IAAI,CAACuD,aAAa,CAACgB,UAAU,EAAE,IAAI,CAAChB,aAAa,EAAEwC,EAAE,CAAC;QACjI,IAAI,CAACG,YAAY,EAAE;UACf;UACA;QACJ;QACApB,MAAM,CAAC9E,CAAC,CAAC,GAAG,IAAI;QAChBiG,UAAU,GAAG,IAAI;MACrB;MACA,IAAIA,UAAU,EAAE;QACZ,IAAI,CAACtB,wBAAwB,CAAC7B,GAAG,EAAE8B,cAAc,EAAEmB,EAAE,CAACK,KAAK,CAAC,CAAC,EAAEtB,MAAM,CAAC;MAC1E;IACJ;IACA;MACIiB,EAAE,CAACC,KAAK,CAAC,CAAC;MACV,IAAIK,cAAc,GAAG,KAAK;MAC1B,MAAMb,UAAU,GAAG,EAAE;MACrB,KAAK,IAAIxF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+C,WAAW,EAAE/C,CAAC,EAAE,EAAE;QAClC,MAAMmF,IAAI,GAAGrH,KAAK,CAACkC,CAAC,CAAC;QACrBwF,UAAU,CAACxF,CAAC,CAAC,GAAG,KAAK;QACrB,IAAI8E,MAAM,CAAC9E,CAAC,CAAC,EAAE;UACX;UACA;QACJ;QACA,MAAMkG,YAAY,GAAGf,IAAI,CAACgB,UAAU,CAACnG,CAAC,GAAGnC,mBAAmB,EAAE6F,QAAQ,CAAC1D,CAAC,CAAC,EAAE,IAAI,CAACuD,aAAa,CAACgB,UAAU,EAAE,IAAI,CAAChB,aAAa,EAAEwC,EAAE,CAAC;QACjI,IAAI,CAACG,YAAY,EAAE;UACf;UACA;QACJ;QACAV,UAAU,CAACxF,CAAC,CAAC,GAAG,IAAI;QACpBqG,cAAc,GAAG,IAAI;MACzB;MACA,IAAIA,cAAc,EAAE;QAChB,IAAI,CAACf,4BAA4B,CAACxC,GAAG,EAAEiD,EAAE,CAACK,KAAK,CAAC,CAAC,EAAEZ,UAAU,CAAC;MAClE;IACJ;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}