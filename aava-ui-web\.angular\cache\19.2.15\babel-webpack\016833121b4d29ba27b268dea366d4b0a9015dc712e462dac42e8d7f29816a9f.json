{"ast": null, "code": "import { Subscription } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../services/auth.service\";\nexport let CallbackComponent = /*#__PURE__*/(() => {\n  class CallbackComponent {\n    route;\n    router;\n    authService;\n    subscription = new Subscription();\n    constructor(route, router, authService) {\n      this.route = route;\n      this.router = router;\n      this.authService = authService;\n    }\n    ngOnInit() {\n      const refreshToken = this.route.snapshot.queryParams['refresh_token'];\n      const code = this.route.snapshot.queryParams['code'];\n      const error = this.route.snapshot.queryParams['error'];\n      if (error) {\n        console.error('Azure AD returned an error:', error);\n        this.router.navigate(['/login'], {\n          state: {\n            error: `Azure AD login failed: ${this.route.snapshot.queryParams['error_description'] || error}`\n          }\n        });\n        return;\n      }\n      if (refreshToken) {\n        this.handleTokenRefresh(refreshToken);\n      } else if (code) {\n        console.log('Authorization code received, length:', code.length);\n        this.handleCodeExchange(code);\n      } else {\n        console.warn('No authorization code or refresh token found in callback URL');\n      }\n    }\n    handleTokenRefresh(refreshToken) {\n      const refreshSub = this.authService.refreshToken(refreshToken).subscribe({\n        next: () => {\n          const redirectUrl = this.authService.getPostLoginRedirectUrl();\n          this.router.navigate([redirectUrl]);\n        },\n        error: err => {\n          console.error('Token refresh failed:', err);\n          this.router.navigate(['/login']);\n        }\n      });\n      this.subscription.add(refreshSub);\n    }\n    handleCodeExchange(code) {\n      const exchangeSub = this.authService.exchangeCodeForToken(code).subscribe({\n        next: () => {\n          const redirectUrl = this.authService.getPostLoginRedirectUrl();\n          this.router.navigate([redirectUrl]);\n          console.log('Token exchange successful');\n        },\n        error: err => {\n          console.error('Token exchange failed:', err);\n          this.router.navigate(['/login']);\n        }\n      });\n      this.subscription.add(exchangeSub);\n    }\n    ngOnDestroy() {\n      this.subscription.unsubscribe();\n    }\n    static ɵfac = function CallbackComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CallbackComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.AuthService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CallbackComponent,\n      selectors: [[\"app-callback\"]],\n      decls: 0,\n      vars: 0,\n      template: function CallbackComponent_Template(rf, ctx) {},\n      encapsulation: 2\n    });\n  }\n  return CallbackComponent;\n})();", "map": {"version": 3, "names": ["Subscription", "CallbackComponent", "route", "router", "authService", "subscription", "constructor", "ngOnInit", "refreshToken", "snapshot", "queryParams", "code", "error", "console", "navigate", "state", "handleTokenRefresh", "log", "length", "handleCodeExchange", "warn", "refreshSub", "subscribe", "next", "redirectUrl", "getPostLoginRedirectUrl", "err", "add", "exchangeSub", "exchangeCodeForToken", "ngOnDestroy", "unsubscribe", "i0", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "AuthService", "selectors", "decls", "vars", "template", "CallbackComponent_Template", "rf", "ctx", "encapsulation"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\auth\\components\\callback\\callback.component.ts"], "sourcesContent": ["import { Component, OnIni<PERSON>, <PERSON><PERSON><PERSON>roy } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { Subscription } from 'rxjs';\r\nimport { AuthService } from '../../services/auth.service';\r\n\r\n@Component({\r\n  selector: 'app-callback',\r\n  template: '',\r\n})\r\nexport class CallbackComponent implements OnInit, OnDestroy {\r\n  private subscription: Subscription = new Subscription();\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private authService: AuthService,\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    const refreshToken = this.route.snapshot.queryParams['refresh_token'];\r\n    const code = this.route.snapshot.queryParams['code'];\r\n    const error = this.route.snapshot.queryParams['error'];\r\n\r\n    if (error) {\r\n      console.error('Azure AD returned an error:', error);\r\n      this.router.navigate(['/login'], {\r\n        state: {\r\n          error: `Azure AD login failed: ${this.route.snapshot.queryParams['error_description'] || error}`,\r\n        },\r\n      });\r\n      return;\r\n    }\r\n\r\n    if (refreshToken) {\r\n      this.handleTokenRefresh(refreshToken);\r\n    } else if (code) {\r\n      console.log('Authorization code received, length:', code.length);\r\n      this.handleCodeExchange(code);\r\n    } else {\r\n      console.warn(\r\n        'No authorization code or refresh token found in callback URL',\r\n      );\r\n    }\r\n  }\r\n\r\n  private handleTokenRefresh(refreshToken: string): void {\r\n    const refreshSub = this.authService.refreshToken(refreshToken).subscribe({\r\n      next: () => {\r\n        const redirectUrl = this.authService.getPostLoginRedirectUrl();\r\n        this.router.navigate([redirectUrl]);\r\n      },\r\n      error: (err) => {\r\n        console.error('Token refresh failed:', err);\r\n        this.router.navigate(['/login']);\r\n      },\r\n    });\r\n    this.subscription.add(refreshSub);\r\n  }\r\n\r\n  private handleCodeExchange(code: string): void {\r\n    const exchangeSub = this.authService\r\n      .exchangeCodeForToken(code)\r\n      .subscribe({\r\n        next: () => {\r\n          const redirectUrl = this.authService.getPostLoginRedirectUrl();\r\n          this.router.navigate([redirectUrl]);\r\n          console.log('Token exchange successful');\r\n        },\r\n        error: (err) => {\r\n          console.error('Token exchange failed:', err);\r\n          this.router.navigate(['/login']);\r\n        },\r\n      });\r\n    this.subscription.add(exchangeSub);\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.subscription.unsubscribe();\r\n  }\r\n}\r\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,MAAM;;;;AAOnC,WAAaC,iBAAiB;EAAxB,MAAOA,iBAAiB;IAIlBC,KAAA;IACAC,MAAA;IACAC,WAAA;IALFC,YAAY,GAAiB,IAAIL,YAAY,EAAE;IAEvDM,YACUJ,KAAqB,EACrBC,MAAc,EACdC,WAAwB;MAFxB,KAAAF,KAAK,GAALA,KAAK;MACL,KAAAC,MAAM,GAANA,MAAM;MACN,KAAAC,WAAW,GAAXA,WAAW;IAClB;IAEHG,QAAQA,CAAA;MACN,MAAMC,YAAY,GAAG,IAAI,CAACN,KAAK,CAACO,QAAQ,CAACC,WAAW,CAAC,eAAe,CAAC;MACrE,MAAMC,IAAI,GAAG,IAAI,CAACT,KAAK,CAACO,QAAQ,CAACC,WAAW,CAAC,MAAM,CAAC;MACpD,MAAME,KAAK,GAAG,IAAI,CAACV,KAAK,CAACO,QAAQ,CAACC,WAAW,CAAC,OAAO,CAAC;MAEtD,IAAIE,KAAK,EAAE;QACTC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnD,IAAI,CAACT,MAAM,CAACW,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE;UAC/BC,KAAK,EAAE;YACLH,KAAK,EAAE,0BAA0B,IAAI,CAACV,KAAK,CAACO,QAAQ,CAACC,WAAW,CAAC,mBAAmB,CAAC,IAAIE,KAAK;;SAEjG,CAAC;QACF;MACF;MAEA,IAAIJ,YAAY,EAAE;QAChB,IAAI,CAACQ,kBAAkB,CAACR,YAAY,CAAC;MACvC,CAAC,MAAM,IAAIG,IAAI,EAAE;QACfE,OAAO,CAACI,GAAG,CAAC,sCAAsC,EAAEN,IAAI,CAACO,MAAM,CAAC;QAChE,IAAI,CAACC,kBAAkB,CAACR,IAAI,CAAC;MAC/B,CAAC,MAAM;QACLE,OAAO,CAACO,IAAI,CACV,8DAA8D,CAC/D;MACH;IACF;IAEQJ,kBAAkBA,CAACR,YAAoB;MAC7C,MAAMa,UAAU,GAAG,IAAI,CAACjB,WAAW,CAACI,YAAY,CAACA,YAAY,CAAC,CAACc,SAAS,CAAC;QACvEC,IAAI,EAAEA,CAAA,KAAK;UACT,MAAMC,WAAW,GAAG,IAAI,CAACpB,WAAW,CAACqB,uBAAuB,EAAE;UAC9D,IAAI,CAACtB,MAAM,CAACW,QAAQ,CAAC,CAACU,WAAW,CAAC,CAAC;QACrC,CAAC;QACDZ,KAAK,EAAGc,GAAG,IAAI;UACbb,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEc,GAAG,CAAC;UAC3C,IAAI,CAACvB,MAAM,CAACW,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;QAClC;OACD,CAAC;MACF,IAAI,CAACT,YAAY,CAACsB,GAAG,CAACN,UAAU,CAAC;IACnC;IAEQF,kBAAkBA,CAACR,IAAY;MACrC,MAAMiB,WAAW,GAAG,IAAI,CAACxB,WAAW,CACjCyB,oBAAoB,CAAClB,IAAI,CAAC,CAC1BW,SAAS,CAAC;QACTC,IAAI,EAAEA,CAAA,KAAK;UACT,MAAMC,WAAW,GAAG,IAAI,CAACpB,WAAW,CAACqB,uBAAuB,EAAE;UAC9D,IAAI,CAACtB,MAAM,CAACW,QAAQ,CAAC,CAACU,WAAW,CAAC,CAAC;UACnCX,OAAO,CAACI,GAAG,CAAC,2BAA2B,CAAC;QAC1C,CAAC;QACDL,KAAK,EAAGc,GAAG,IAAI;UACbb,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEc,GAAG,CAAC;UAC5C,IAAI,CAACvB,MAAM,CAACW,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;QAClC;OACD,CAAC;MACJ,IAAI,CAACT,YAAY,CAACsB,GAAG,CAACC,WAAW,CAAC;IACpC;IAEAE,WAAWA,CAAA;MACT,IAAI,CAACzB,YAAY,CAAC0B,WAAW,EAAE;IACjC;;uCArEW9B,iBAAiB,EAAA+B,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAAJ,EAAA,CAAAC,iBAAA,CAAAI,EAAA,CAAAC,WAAA;IAAA;;YAAjBrC,iBAAiB;MAAAsC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;MAAAC,aAAA;IAAA;;SAAjB7C,iBAAiB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}