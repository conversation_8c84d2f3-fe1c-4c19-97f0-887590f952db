{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport class BasicInplaceReplace {\n  constructor() {\n    this._defaultValueSet = [['true', 'false'], ['True', 'False'], ['Private', 'Public', 'Friend', 'ReadOnly', 'Partial', 'Protected', 'WriteOnly'], ['public', 'protected', 'private']];\n  }\n  static {\n    this.INSTANCE = new BasicInplaceReplace();\n  }\n  navigateValueSet(range1, text1, range2, text2, up) {\n    if (range1 && text1) {\n      const result = this.doNavigateValueSet(text1, up);\n      if (result) {\n        return {\n          range: range1,\n          value: result\n        };\n      }\n    }\n    if (range2 && text2) {\n      const result = this.doNavigateValueSet(text2, up);\n      if (result) {\n        return {\n          range: range2,\n          value: result\n        };\n      }\n    }\n    return null;\n  }\n  doNavigateValueSet(text, up) {\n    const numberResult = this.numberReplace(text, up);\n    if (numberResult !== null) {\n      return numberResult;\n    }\n    return this.textReplace(text, up);\n  }\n  numberReplace(value, up) {\n    const precision = Math.pow(10, value.length - (value.lastIndexOf('.') + 1));\n    let n1 = Number(value);\n    const n2 = parseFloat(value);\n    if (!isNaN(n1) && !isNaN(n2) && n1 === n2) {\n      if (n1 === 0 && !up) {\n        return null; // don't do negative\n        //\t\t\t} else if(n1 === 9 && up) {\n        //\t\t\t\treturn null; // don't insert 10 into a number\n      } else {\n        n1 = Math.floor(n1 * precision);\n        n1 += up ? precision : -precision;\n        return String(n1 / precision);\n      }\n    }\n    return null;\n  }\n  textReplace(value, up) {\n    return this.valueSetsReplace(this._defaultValueSet, value, up);\n  }\n  valueSetsReplace(valueSets, value, up) {\n    let result = null;\n    for (let i = 0, len = valueSets.length; result === null && i < len; i++) {\n      result = this.valueSetReplace(valueSets[i], value, up);\n    }\n    return result;\n  }\n  valueSetReplace(valueSet, value, up) {\n    let idx = valueSet.indexOf(value);\n    if (idx >= 0) {\n      idx += up ? +1 : -1;\n      if (idx < 0) {\n        idx = valueSet.length - 1;\n      } else {\n        idx %= valueSet.length;\n      }\n      return valueSet[idx];\n    }\n    return null;\n  }\n}", "map": {"version": 3, "names": ["BasicInplaceReplace", "constructor", "_defaultValueSet", "INSTANCE", "navigateValueSet", "range1", "text1", "range2", "text2", "up", "result", "doNavigateValueSet", "range", "value", "text", "numberResult", "numberReplace", "textReplace", "precision", "Math", "pow", "length", "lastIndexOf", "n1", "Number", "n2", "parseFloat", "isNaN", "floor", "String", "valueSetsReplace", "valueSets", "i", "len", "valueSetReplace", "valueSet", "idx", "indexOf"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/languages/supports/inplaceReplaceSupport.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport class BasicInplaceReplace {\n    constructor() {\n        this._defaultValueSet = [\n            ['true', 'false'],\n            ['True', 'False'],\n            ['Private', 'Public', 'Friend', 'ReadOnly', 'Partial', 'Protected', 'WriteOnly'],\n            ['public', 'protected', 'private'],\n        ];\n    }\n    static { this.INSTANCE = new BasicInplaceReplace(); }\n    navigateValueSet(range1, text1, range2, text2, up) {\n        if (range1 && text1) {\n            const result = this.doNavigateValueSet(text1, up);\n            if (result) {\n                return {\n                    range: range1,\n                    value: result\n                };\n            }\n        }\n        if (range2 && text2) {\n            const result = this.doNavigateValueSet(text2, up);\n            if (result) {\n                return {\n                    range: range2,\n                    value: result\n                };\n            }\n        }\n        return null;\n    }\n    doNavigateValueSet(text, up) {\n        const numberResult = this.numberReplace(text, up);\n        if (numberResult !== null) {\n            return numberResult;\n        }\n        return this.textReplace(text, up);\n    }\n    numberReplace(value, up) {\n        const precision = Math.pow(10, value.length - (value.lastIndexOf('.') + 1));\n        let n1 = Number(value);\n        const n2 = parseFloat(value);\n        if (!isNaN(n1) && !isNaN(n2) && n1 === n2) {\n            if (n1 === 0 && !up) {\n                return null; // don't do negative\n                //\t\t\t} else if(n1 === 9 && up) {\n                //\t\t\t\treturn null; // don't insert 10 into a number\n            }\n            else {\n                n1 = Math.floor(n1 * precision);\n                n1 += up ? precision : -precision;\n                return String(n1 / precision);\n            }\n        }\n        return null;\n    }\n    textReplace(value, up) {\n        return this.valueSetsReplace(this._defaultValueSet, value, up);\n    }\n    valueSetsReplace(valueSets, value, up) {\n        let result = null;\n        for (let i = 0, len = valueSets.length; result === null && i < len; i++) {\n            result = this.valueSetReplace(valueSets[i], value, up);\n        }\n        return result;\n    }\n    valueSetReplace(valueSet, value, up) {\n        let idx = valueSet.indexOf(value);\n        if (idx >= 0) {\n            idx += up ? +1 : -1;\n            if (idx < 0) {\n                idx = valueSet.length - 1;\n            }\n            else {\n                idx %= valueSet.length;\n            }\n            return valueSet[idx];\n        }\n        return null;\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,MAAMA,mBAAmB,CAAC;EAC7BC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,gBAAgB,GAAG,CACpB,CAAC,MAAM,EAAE,OAAO,CAAC,EACjB,CAAC,MAAM,EAAE,OAAO,CAAC,EACjB,CAAC,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,WAAW,EAAE,WAAW,CAAC,EAChF,CAAC,QAAQ,EAAE,WAAW,EAAE,SAAS,CAAC,CACrC;EACL;EACA;IAAS,IAAI,CAACC,QAAQ,GAAG,IAAIH,mBAAmB,CAAC,CAAC;EAAE;EACpDI,gBAAgBA,CAACC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAEC,EAAE,EAAE;IAC/C,IAAIJ,MAAM,IAAIC,KAAK,EAAE;MACjB,MAAMI,MAAM,GAAG,IAAI,CAACC,kBAAkB,CAACL,KAAK,EAAEG,EAAE,CAAC;MACjD,IAAIC,MAAM,EAAE;QACR,OAAO;UACHE,KAAK,EAAEP,MAAM;UACbQ,KAAK,EAAEH;QACX,CAAC;MACL;IACJ;IACA,IAAIH,MAAM,IAAIC,KAAK,EAAE;MACjB,MAAME,MAAM,GAAG,IAAI,CAACC,kBAAkB,CAACH,KAAK,EAAEC,EAAE,CAAC;MACjD,IAAIC,MAAM,EAAE;QACR,OAAO;UACHE,KAAK,EAAEL,MAAM;UACbM,KAAK,EAAEH;QACX,CAAC;MACL;IACJ;IACA,OAAO,IAAI;EACf;EACAC,kBAAkBA,CAACG,IAAI,EAAEL,EAAE,EAAE;IACzB,MAAMM,YAAY,GAAG,IAAI,CAACC,aAAa,CAACF,IAAI,EAAEL,EAAE,CAAC;IACjD,IAAIM,YAAY,KAAK,IAAI,EAAE;MACvB,OAAOA,YAAY;IACvB;IACA,OAAO,IAAI,CAACE,WAAW,CAACH,IAAI,EAAEL,EAAE,CAAC;EACrC;EACAO,aAAaA,CAACH,KAAK,EAAEJ,EAAE,EAAE;IACrB,MAAMS,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,EAAE,EAAEP,KAAK,CAACQ,MAAM,IAAIR,KAAK,CAACS,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IAC3E,IAAIC,EAAE,GAAGC,MAAM,CAACX,KAAK,CAAC;IACtB,MAAMY,EAAE,GAAGC,UAAU,CAACb,KAAK,CAAC;IAC5B,IAAI,CAACc,KAAK,CAACJ,EAAE,CAAC,IAAI,CAACI,KAAK,CAACF,EAAE,CAAC,IAAIF,EAAE,KAAKE,EAAE,EAAE;MACvC,IAAIF,EAAE,KAAK,CAAC,IAAI,CAACd,EAAE,EAAE;QACjB,OAAO,IAAI,CAAC,CAAC;QACb;QACA;MACJ,CAAC,MACI;QACDc,EAAE,GAAGJ,IAAI,CAACS,KAAK,CAACL,EAAE,GAAGL,SAAS,CAAC;QAC/BK,EAAE,IAAId,EAAE,GAAGS,SAAS,GAAG,CAACA,SAAS;QACjC,OAAOW,MAAM,CAACN,EAAE,GAAGL,SAAS,CAAC;MACjC;IACJ;IACA,OAAO,IAAI;EACf;EACAD,WAAWA,CAACJ,KAAK,EAAEJ,EAAE,EAAE;IACnB,OAAO,IAAI,CAACqB,gBAAgB,CAAC,IAAI,CAAC5B,gBAAgB,EAAEW,KAAK,EAAEJ,EAAE,CAAC;EAClE;EACAqB,gBAAgBA,CAACC,SAAS,EAAElB,KAAK,EAAEJ,EAAE,EAAE;IACnC,IAAIC,MAAM,GAAG,IAAI;IACjB,KAAK,IAAIsB,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGF,SAAS,CAACV,MAAM,EAAEX,MAAM,KAAK,IAAI,IAAIsB,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;MACrEtB,MAAM,GAAG,IAAI,CAACwB,eAAe,CAACH,SAAS,CAACC,CAAC,CAAC,EAAEnB,KAAK,EAAEJ,EAAE,CAAC;IAC1D;IACA,OAAOC,MAAM;EACjB;EACAwB,eAAeA,CAACC,QAAQ,EAAEtB,KAAK,EAAEJ,EAAE,EAAE;IACjC,IAAI2B,GAAG,GAAGD,QAAQ,CAACE,OAAO,CAACxB,KAAK,CAAC;IACjC,IAAIuB,GAAG,IAAI,CAAC,EAAE;MACVA,GAAG,IAAI3B,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;MACnB,IAAI2B,GAAG,GAAG,CAAC,EAAE;QACTA,GAAG,GAAGD,QAAQ,CAACd,MAAM,GAAG,CAAC;MAC7B,CAAC,MACI;QACDe,GAAG,IAAID,QAAQ,CAACd,MAAM;MAC1B;MACA,OAAOc,QAAQ,CAACC,GAAG,CAAC;IACxB;IACA,OAAO,IAAI;EACf;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}