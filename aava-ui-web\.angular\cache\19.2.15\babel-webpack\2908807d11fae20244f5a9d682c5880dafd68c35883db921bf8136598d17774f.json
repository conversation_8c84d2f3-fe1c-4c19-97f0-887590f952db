{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Color, HSLA } from '../../../base/common/color.js';\nfunction _parseCaptureGroups(captureGroups) {\n  const values = [];\n  for (const captureGroup of captureGroups) {\n    const parsedNumber = Number(captureGroup);\n    if (parsedNumber || parsedNumber === 0 && captureGroup.replace(/\\s/g, '') !== '') {\n      values.push(parsedNumber);\n    }\n  }\n  return values;\n}\nfunction _toIColor(r, g, b, a) {\n  return {\n    red: r / 255,\n    blue: b / 255,\n    green: g / 255,\n    alpha: a\n  };\n}\nfunction _findRange(model, match) {\n  const index = match.index;\n  const length = match[0].length;\n  if (!index) {\n    return;\n  }\n  const startPosition = model.positionAt(index);\n  const range = {\n    startLineNumber: startPosition.lineNumber,\n    startColumn: startPosition.column,\n    endLineNumber: startPosition.lineNumber,\n    endColumn: startPosition.column + length\n  };\n  return range;\n}\nfunction _findHexColorInformation(range, hexValue) {\n  if (!range) {\n    return;\n  }\n  const parsedHexColor = Color.Format.CSS.parseHex(hexValue);\n  if (!parsedHexColor) {\n    return;\n  }\n  return {\n    range: range,\n    color: _toIColor(parsedHexColor.rgba.r, parsedHexColor.rgba.g, parsedHexColor.rgba.b, parsedHexColor.rgba.a)\n  };\n}\nfunction _findRGBColorInformation(range, matches, isAlpha) {\n  if (!range || matches.length !== 1) {\n    return;\n  }\n  const match = matches[0];\n  const captureGroups = match.values();\n  const parsedRegex = _parseCaptureGroups(captureGroups);\n  return {\n    range: range,\n    color: _toIColor(parsedRegex[0], parsedRegex[1], parsedRegex[2], isAlpha ? parsedRegex[3] : 1)\n  };\n}\nfunction _findHSLColorInformation(range, matches, isAlpha) {\n  if (!range || matches.length !== 1) {\n    return;\n  }\n  const match = matches[0];\n  const captureGroups = match.values();\n  const parsedRegex = _parseCaptureGroups(captureGroups);\n  const colorEquivalent = new Color(new HSLA(parsedRegex[0], parsedRegex[1] / 100, parsedRegex[2] / 100, isAlpha ? parsedRegex[3] : 1));\n  return {\n    range: range,\n    color: _toIColor(colorEquivalent.rgba.r, colorEquivalent.rgba.g, colorEquivalent.rgba.b, colorEquivalent.rgba.a)\n  };\n}\nfunction _findMatches(model, regex) {\n  if (typeof model === 'string') {\n    return [...model.matchAll(regex)];\n  } else {\n    return model.findMatches(regex);\n  }\n}\nfunction computeColors(model) {\n  const result = [];\n  // Early validation for RGB and HSL\n  const initialValidationRegex = /\\b(rgb|rgba|hsl|hsla)(\\([0-9\\s,.\\%]*\\))|(#)([A-Fa-f0-9]{3})\\b|(#)([A-Fa-f0-9]{4})\\b|(#)([A-Fa-f0-9]{6})\\b|(#)([A-Fa-f0-9]{8})\\b/gm;\n  const initialValidationMatches = _findMatches(model, initialValidationRegex);\n  // Potential colors have been found, validate the parameters\n  if (initialValidationMatches.length > 0) {\n    for (const initialMatch of initialValidationMatches) {\n      const initialCaptureGroups = initialMatch.filter(captureGroup => captureGroup !== undefined);\n      const colorScheme = initialCaptureGroups[1];\n      const colorParameters = initialCaptureGroups[2];\n      if (!colorParameters) {\n        continue;\n      }\n      let colorInformation;\n      if (colorScheme === 'rgb') {\n        const regexParameters = /^\\(\\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\\s*,\\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\\s*,\\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\\s*\\)$/gm;\n        colorInformation = _findRGBColorInformation(_findRange(model, initialMatch), _findMatches(colorParameters, regexParameters), false);\n      } else if (colorScheme === 'rgba') {\n        const regexParameters = /^\\(\\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\\s*,\\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\\s*,\\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\\s*,\\s*(0[.][0-9]+|[.][0-9]+|[01][.]|[01])\\s*\\)$/gm;\n        colorInformation = _findRGBColorInformation(_findRange(model, initialMatch), _findMatches(colorParameters, regexParameters), true);\n      } else if (colorScheme === 'hsl') {\n        const regexParameters = /^\\(\\s*(36[0]|3[0-5][0-9]|[12][0-9][0-9]|[1-9]?[0-9])\\s*,\\s*(100|\\d{1,2}[.]\\d*|\\d{1,2})%\\s*,\\s*(100|\\d{1,2}[.]\\d*|\\d{1,2})%\\s*\\)$/gm;\n        colorInformation = _findHSLColorInformation(_findRange(model, initialMatch), _findMatches(colorParameters, regexParameters), false);\n      } else if (colorScheme === 'hsla') {\n        const regexParameters = /^\\(\\s*(36[0]|3[0-5][0-9]|[12][0-9][0-9]|[1-9]?[0-9])\\s*,\\s*(100|\\d{1,2}[.]\\d*|\\d{1,2})%\\s*,\\s*(100|\\d{1,2}[.]\\d*|\\d{1,2})%\\s*,\\s*(0[.][0-9]+|[.][0-9]+|[01][.]|[01])\\s*\\)$/gm;\n        colorInformation = _findHSLColorInformation(_findRange(model, initialMatch), _findMatches(colorParameters, regexParameters), true);\n      } else if (colorScheme === '#') {\n        colorInformation = _findHexColorInformation(_findRange(model, initialMatch), colorScheme + colorParameters);\n      }\n      if (colorInformation) {\n        result.push(colorInformation);\n      }\n    }\n  }\n  return result;\n}\n/**\n * Returns an array of all default document colors in the provided document\n */\nexport function computeDefaultDocumentColors(model) {\n  if (!model || typeof model.getValue !== 'function' || typeof model.positionAt !== 'function') {\n    // Unknown caller!\n    return [];\n  }\n  return computeColors(model);\n}", "map": {"version": 3, "names": ["Color", "HSLA", "_parseCaptureGroups", "captureGroups", "values", "captureGroup", "parsedNumber", "Number", "replace", "push", "_toIColor", "r", "g", "b", "a", "red", "blue", "green", "alpha", "_find<PERSON>ange", "model", "match", "index", "length", "startPosition", "positionAt", "range", "startLineNumber", "lineNumber", "startColumn", "column", "endLineNumber", "endColumn", "_findHexColorInformation", "hexValue", "parsedHexColor", "Format", "CSS", "parseHex", "color", "rgba", "_findRGBColorInformation", "matches", "isAlpha", "parsedRegex", "_findHSLColorInformation", "colorEquivalent", "_findMatches", "regex", "matchAll", "findMatches", "computeColors", "result", "initialValidationRegex", "initialValidationMatches", "initialMatch", "initialCaptureGroups", "filter", "undefined", "colorScheme", "colorParameters", "colorInformation", "regexParameters", "computeDefaultDocumentColors", "getValue"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/languages/defaultDocumentColorsComputer.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { Color, HSLA } from '../../../base/common/color.js';\nfunction _parseCaptureGroups(captureGroups) {\n    const values = [];\n    for (const captureGroup of captureGroups) {\n        const parsedNumber = Number(captureGroup);\n        if (parsedNumber || parsedNumber === 0 && captureGroup.replace(/\\s/g, '') !== '') {\n            values.push(parsedNumber);\n        }\n    }\n    return values;\n}\nfunction _toIColor(r, g, b, a) {\n    return {\n        red: r / 255,\n        blue: b / 255,\n        green: g / 255,\n        alpha: a\n    };\n}\nfunction _findRange(model, match) {\n    const index = match.index;\n    const length = match[0].length;\n    if (!index) {\n        return;\n    }\n    const startPosition = model.positionAt(index);\n    const range = {\n        startLineNumber: startPosition.lineNumber,\n        startColumn: startPosition.column,\n        endLineNumber: startPosition.lineNumber,\n        endColumn: startPosition.column + length\n    };\n    return range;\n}\nfunction _findHexColorInformation(range, hexValue) {\n    if (!range) {\n        return;\n    }\n    const parsedHexColor = Color.Format.CSS.parseHex(hexValue);\n    if (!parsedHexColor) {\n        return;\n    }\n    return {\n        range: range,\n        color: _toIColor(parsedHexColor.rgba.r, parsedHexColor.rgba.g, parsedHexColor.rgba.b, parsedHexColor.rgba.a)\n    };\n}\nfunction _findRGBColorInformation(range, matches, isAlpha) {\n    if (!range || matches.length !== 1) {\n        return;\n    }\n    const match = matches[0];\n    const captureGroups = match.values();\n    const parsedRegex = _parseCaptureGroups(captureGroups);\n    return {\n        range: range,\n        color: _toIColor(parsedRegex[0], parsedRegex[1], parsedRegex[2], isAlpha ? parsedRegex[3] : 1)\n    };\n}\nfunction _findHSLColorInformation(range, matches, isAlpha) {\n    if (!range || matches.length !== 1) {\n        return;\n    }\n    const match = matches[0];\n    const captureGroups = match.values();\n    const parsedRegex = _parseCaptureGroups(captureGroups);\n    const colorEquivalent = new Color(new HSLA(parsedRegex[0], parsedRegex[1] / 100, parsedRegex[2] / 100, isAlpha ? parsedRegex[3] : 1));\n    return {\n        range: range,\n        color: _toIColor(colorEquivalent.rgba.r, colorEquivalent.rgba.g, colorEquivalent.rgba.b, colorEquivalent.rgba.a)\n    };\n}\nfunction _findMatches(model, regex) {\n    if (typeof model === 'string') {\n        return [...model.matchAll(regex)];\n    }\n    else {\n        return model.findMatches(regex);\n    }\n}\nfunction computeColors(model) {\n    const result = [];\n    // Early validation for RGB and HSL\n    const initialValidationRegex = /\\b(rgb|rgba|hsl|hsla)(\\([0-9\\s,.\\%]*\\))|(#)([A-Fa-f0-9]{3})\\b|(#)([A-Fa-f0-9]{4})\\b|(#)([A-Fa-f0-9]{6})\\b|(#)([A-Fa-f0-9]{8})\\b/gm;\n    const initialValidationMatches = _findMatches(model, initialValidationRegex);\n    // Potential colors have been found, validate the parameters\n    if (initialValidationMatches.length > 0) {\n        for (const initialMatch of initialValidationMatches) {\n            const initialCaptureGroups = initialMatch.filter(captureGroup => captureGroup !== undefined);\n            const colorScheme = initialCaptureGroups[1];\n            const colorParameters = initialCaptureGroups[2];\n            if (!colorParameters) {\n                continue;\n            }\n            let colorInformation;\n            if (colorScheme === 'rgb') {\n                const regexParameters = /^\\(\\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\\s*,\\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\\s*,\\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\\s*\\)$/gm;\n                colorInformation = _findRGBColorInformation(_findRange(model, initialMatch), _findMatches(colorParameters, regexParameters), false);\n            }\n            else if (colorScheme === 'rgba') {\n                const regexParameters = /^\\(\\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\\s*,\\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\\s*,\\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\\s*,\\s*(0[.][0-9]+|[.][0-9]+|[01][.]|[01])\\s*\\)$/gm;\n                colorInformation = _findRGBColorInformation(_findRange(model, initialMatch), _findMatches(colorParameters, regexParameters), true);\n            }\n            else if (colorScheme === 'hsl') {\n                const regexParameters = /^\\(\\s*(36[0]|3[0-5][0-9]|[12][0-9][0-9]|[1-9]?[0-9])\\s*,\\s*(100|\\d{1,2}[.]\\d*|\\d{1,2})%\\s*,\\s*(100|\\d{1,2}[.]\\d*|\\d{1,2})%\\s*\\)$/gm;\n                colorInformation = _findHSLColorInformation(_findRange(model, initialMatch), _findMatches(colorParameters, regexParameters), false);\n            }\n            else if (colorScheme === 'hsla') {\n                const regexParameters = /^\\(\\s*(36[0]|3[0-5][0-9]|[12][0-9][0-9]|[1-9]?[0-9])\\s*,\\s*(100|\\d{1,2}[.]\\d*|\\d{1,2})%\\s*,\\s*(100|\\d{1,2}[.]\\d*|\\d{1,2})%\\s*,\\s*(0[.][0-9]+|[.][0-9]+|[01][.]|[01])\\s*\\)$/gm;\n                colorInformation = _findHSLColorInformation(_findRange(model, initialMatch), _findMatches(colorParameters, regexParameters), true);\n            }\n            else if (colorScheme === '#') {\n                colorInformation = _findHexColorInformation(_findRange(model, initialMatch), colorScheme + colorParameters);\n            }\n            if (colorInformation) {\n                result.push(colorInformation);\n            }\n        }\n    }\n    return result;\n}\n/**\n * Returns an array of all default document colors in the provided document\n */\nexport function computeDefaultDocumentColors(model) {\n    if (!model || typeof model.getValue !== 'function' || typeof model.positionAt !== 'function') {\n        // Unknown caller!\n        return [];\n    }\n    return computeColors(model);\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,KAAK,EAAEC,IAAI,QAAQ,+BAA+B;AAC3D,SAASC,mBAAmBA,CAACC,aAAa,EAAE;EACxC,MAAMC,MAAM,GAAG,EAAE;EACjB,KAAK,MAAMC,YAAY,IAAIF,aAAa,EAAE;IACtC,MAAMG,YAAY,GAAGC,MAAM,CAACF,YAAY,CAAC;IACzC,IAAIC,YAAY,IAAIA,YAAY,KAAK,CAAC,IAAID,YAAY,CAACG,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,EAAE,EAAE;MAC9EJ,MAAM,CAACK,IAAI,CAACH,YAAY,CAAC;IAC7B;EACJ;EACA,OAAOF,MAAM;AACjB;AACA,SAASM,SAASA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAC3B,OAAO;IACHC,GAAG,EAAEJ,CAAC,GAAG,GAAG;IACZK,IAAI,EAAEH,CAAC,GAAG,GAAG;IACbI,KAAK,EAAEL,CAAC,GAAG,GAAG;IACdM,KAAK,EAAEJ;EACX,CAAC;AACL;AACA,SAASK,UAAUA,CAACC,KAAK,EAAEC,KAAK,EAAE;EAC9B,MAAMC,KAAK,GAAGD,KAAK,CAACC,KAAK;EACzB,MAAMC,MAAM,GAAGF,KAAK,CAAC,CAAC,CAAC,CAACE,MAAM;EAC9B,IAAI,CAACD,KAAK,EAAE;IACR;EACJ;EACA,MAAME,aAAa,GAAGJ,KAAK,CAACK,UAAU,CAACH,KAAK,CAAC;EAC7C,MAAMI,KAAK,GAAG;IACVC,eAAe,EAAEH,aAAa,CAACI,UAAU;IACzCC,WAAW,EAAEL,aAAa,CAACM,MAAM;IACjCC,aAAa,EAAEP,aAAa,CAACI,UAAU;IACvCI,SAAS,EAAER,aAAa,CAACM,MAAM,GAAGP;EACtC,CAAC;EACD,OAAOG,KAAK;AAChB;AACA,SAASO,wBAAwBA,CAACP,KAAK,EAAEQ,QAAQ,EAAE;EAC/C,IAAI,CAACR,KAAK,EAAE;IACR;EACJ;EACA,MAAMS,cAAc,GAAGnC,KAAK,CAACoC,MAAM,CAACC,GAAG,CAACC,QAAQ,CAACJ,QAAQ,CAAC;EAC1D,IAAI,CAACC,cAAc,EAAE;IACjB;EACJ;EACA,OAAO;IACHT,KAAK,EAAEA,KAAK;IACZa,KAAK,EAAE7B,SAAS,CAACyB,cAAc,CAACK,IAAI,CAAC7B,CAAC,EAAEwB,cAAc,CAACK,IAAI,CAAC5B,CAAC,EAAEuB,cAAc,CAACK,IAAI,CAAC3B,CAAC,EAAEsB,cAAc,CAACK,IAAI,CAAC1B,CAAC;EAC/G,CAAC;AACL;AACA,SAAS2B,wBAAwBA,CAACf,KAAK,EAAEgB,OAAO,EAAEC,OAAO,EAAE;EACvD,IAAI,CAACjB,KAAK,IAAIgB,OAAO,CAACnB,MAAM,KAAK,CAAC,EAAE;IAChC;EACJ;EACA,MAAMF,KAAK,GAAGqB,OAAO,CAAC,CAAC,CAAC;EACxB,MAAMvC,aAAa,GAAGkB,KAAK,CAACjB,MAAM,CAAC,CAAC;EACpC,MAAMwC,WAAW,GAAG1C,mBAAmB,CAACC,aAAa,CAAC;EACtD,OAAO;IACHuB,KAAK,EAAEA,KAAK;IACZa,KAAK,EAAE7B,SAAS,CAACkC,WAAW,CAAC,CAAC,CAAC,EAAEA,WAAW,CAAC,CAAC,CAAC,EAAEA,WAAW,CAAC,CAAC,CAAC,EAAED,OAAO,GAAGC,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC;EACjG,CAAC;AACL;AACA,SAASC,wBAAwBA,CAACnB,KAAK,EAAEgB,OAAO,EAAEC,OAAO,EAAE;EACvD,IAAI,CAACjB,KAAK,IAAIgB,OAAO,CAACnB,MAAM,KAAK,CAAC,EAAE;IAChC;EACJ;EACA,MAAMF,KAAK,GAAGqB,OAAO,CAAC,CAAC,CAAC;EACxB,MAAMvC,aAAa,GAAGkB,KAAK,CAACjB,MAAM,CAAC,CAAC;EACpC,MAAMwC,WAAW,GAAG1C,mBAAmB,CAACC,aAAa,CAAC;EACtD,MAAM2C,eAAe,GAAG,IAAI9C,KAAK,CAAC,IAAIC,IAAI,CAAC2C,WAAW,CAAC,CAAC,CAAC,EAAEA,WAAW,CAAC,CAAC,CAAC,GAAG,GAAG,EAAEA,WAAW,CAAC,CAAC,CAAC,GAAG,GAAG,EAAED,OAAO,GAAGC,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;EACrI,OAAO;IACHlB,KAAK,EAAEA,KAAK;IACZa,KAAK,EAAE7B,SAAS,CAACoC,eAAe,CAACN,IAAI,CAAC7B,CAAC,EAAEmC,eAAe,CAACN,IAAI,CAAC5B,CAAC,EAAEkC,eAAe,CAACN,IAAI,CAAC3B,CAAC,EAAEiC,eAAe,CAACN,IAAI,CAAC1B,CAAC;EACnH,CAAC;AACL;AACA,SAASiC,YAAYA,CAAC3B,KAAK,EAAE4B,KAAK,EAAE;EAChC,IAAI,OAAO5B,KAAK,KAAK,QAAQ,EAAE;IAC3B,OAAO,CAAC,GAAGA,KAAK,CAAC6B,QAAQ,CAACD,KAAK,CAAC,CAAC;EACrC,CAAC,MACI;IACD,OAAO5B,KAAK,CAAC8B,WAAW,CAACF,KAAK,CAAC;EACnC;AACJ;AACA,SAASG,aAAaA,CAAC/B,KAAK,EAAE;EAC1B,MAAMgC,MAAM,GAAG,EAAE;EACjB;EACA,MAAMC,sBAAsB,GAAG,mIAAmI;EAClK,MAAMC,wBAAwB,GAAGP,YAAY,CAAC3B,KAAK,EAAEiC,sBAAsB,CAAC;EAC5E;EACA,IAAIC,wBAAwB,CAAC/B,MAAM,GAAG,CAAC,EAAE;IACrC,KAAK,MAAMgC,YAAY,IAAID,wBAAwB,EAAE;MACjD,MAAME,oBAAoB,GAAGD,YAAY,CAACE,MAAM,CAACpD,YAAY,IAAIA,YAAY,KAAKqD,SAAS,CAAC;MAC5F,MAAMC,WAAW,GAAGH,oBAAoB,CAAC,CAAC,CAAC;MAC3C,MAAMI,eAAe,GAAGJ,oBAAoB,CAAC,CAAC,CAAC;MAC/C,IAAI,CAACI,eAAe,EAAE;QAClB;MACJ;MACA,IAAIC,gBAAgB;MACpB,IAAIF,WAAW,KAAK,KAAK,EAAE;QACvB,MAAMG,eAAe,GAAG,8KAA8K;QACtMD,gBAAgB,GAAGpB,wBAAwB,CAACtB,UAAU,CAACC,KAAK,EAAEmC,YAAY,CAAC,EAAER,YAAY,CAACa,eAAe,EAAEE,eAAe,CAAC,EAAE,KAAK,CAAC;MACvI,CAAC,MACI,IAAIH,WAAW,KAAK,MAAM,EAAE;QAC7B,MAAMG,eAAe,GAAG,wNAAwN;QAChPD,gBAAgB,GAAGpB,wBAAwB,CAACtB,UAAU,CAACC,KAAK,EAAEmC,YAAY,CAAC,EAAER,YAAY,CAACa,eAAe,EAAEE,eAAe,CAAC,EAAE,IAAI,CAAC;MACtI,CAAC,MACI,IAAIH,WAAW,KAAK,KAAK,EAAE;QAC5B,MAAMG,eAAe,GAAG,oIAAoI;QAC5JD,gBAAgB,GAAGhB,wBAAwB,CAAC1B,UAAU,CAACC,KAAK,EAAEmC,YAAY,CAAC,EAAER,YAAY,CAACa,eAAe,EAAEE,eAAe,CAAC,EAAE,KAAK,CAAC;MACvI,CAAC,MACI,IAAIH,WAAW,KAAK,MAAM,EAAE;QAC7B,MAAMG,eAAe,GAAG,8KAA8K;QACtMD,gBAAgB,GAAGhB,wBAAwB,CAAC1B,UAAU,CAACC,KAAK,EAAEmC,YAAY,CAAC,EAAER,YAAY,CAACa,eAAe,EAAEE,eAAe,CAAC,EAAE,IAAI,CAAC;MACtI,CAAC,MACI,IAAIH,WAAW,KAAK,GAAG,EAAE;QAC1BE,gBAAgB,GAAG5B,wBAAwB,CAACd,UAAU,CAACC,KAAK,EAAEmC,YAAY,CAAC,EAAEI,WAAW,GAAGC,eAAe,CAAC;MAC/G;MACA,IAAIC,gBAAgB,EAAE;QAClBT,MAAM,CAAC3C,IAAI,CAACoD,gBAAgB,CAAC;MACjC;IACJ;EACJ;EACA,OAAOT,MAAM;AACjB;AACA;AACA;AACA;AACA,OAAO,SAASW,4BAA4BA,CAAC3C,KAAK,EAAE;EAChD,IAAI,CAACA,KAAK,IAAI,OAAOA,KAAK,CAAC4C,QAAQ,KAAK,UAAU,IAAI,OAAO5C,KAAK,CAACK,UAAU,KAAK,UAAU,EAAE;IAC1F;IACA,OAAO,EAAE;EACb;EACA,OAAO0B,aAAa,CAAC/B,KAAK,CAAC;AAC/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}