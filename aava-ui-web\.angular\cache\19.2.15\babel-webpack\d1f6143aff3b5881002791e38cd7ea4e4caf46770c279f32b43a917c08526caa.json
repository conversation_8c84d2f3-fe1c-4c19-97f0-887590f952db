{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { createStyleSheet2 } from './dom.js';\nimport { DisposableStore } from '../common/lifecycle.js';\nimport { autorun } from '../common/observable.js';\nexport function createStyleSheetFromObservable(css) {\n  const store = new DisposableStore();\n  const w = store.add(createStyleSheet2());\n  store.add(autorun(reader => {\n    w.setStyle(css.read(reader));\n  }));\n  return store;\n}", "map": {"version": 3, "names": ["createStyleSheet2", "DisposableStore", "autorun", "createStyleSheetFromObservable", "css", "store", "w", "add", "reader", "setStyle", "read"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/browser/domObservable.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { createStyleSheet2 } from './dom.js';\nimport { DisposableStore } from '../common/lifecycle.js';\nimport { autorun } from '../common/observable.js';\nexport function createStyleSheetFromObservable(css) {\n    const store = new DisposableStore();\n    const w = store.add(createStyleSheet2());\n    store.add(autorun(reader => {\n        w.setStyle(css.read(reader));\n    }));\n    return store;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,iBAAiB,QAAQ,UAAU;AAC5C,SAASC,eAAe,QAAQ,wBAAwB;AACxD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAO,SAASC,8BAA8BA,CAACC,GAAG,EAAE;EAChD,MAAMC,KAAK,GAAG,IAAIJ,eAAe,CAAC,CAAC;EACnC,MAAMK,CAAC,GAAGD,KAAK,CAACE,GAAG,CAACP,iBAAiB,CAAC,CAAC,CAAC;EACxCK,KAAK,CAACE,GAAG,CAACL,OAAO,CAACM,MAAM,IAAI;IACxBF,CAAC,CAACG,QAAQ,CAACL,GAAG,CAACM,IAAI,CAACF,MAAM,CAAC,CAAC;EAChC,CAAC,CAAC,CAAC;EACH,OAAOH,KAAK;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}