{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = this && this.__param || function (paramIndex, decorator) {\n  return function (target, key) {\n    decorator(target, key, paramIndex);\n  };\n};\nimport { $, append, hide, show } from '../../../../base/browser/dom.js';\nimport { IconLabel } from '../../../../base/browser/ui/iconLabel/iconLabel.js';\nimport { Codicon } from '../../../../base/common/codicons.js';\nimport { ThemeIcon } from '../../../../base/common/themables.js';\nimport { Emitter } from '../../../../base/common/event.js';\nimport { createMatches } from '../../../../base/common/filters.js';\nimport { DisposableStore } from '../../../../base/common/lifecycle.js';\nimport { URI } from '../../../../base/common/uri.js';\nimport { CompletionItemKinds } from '../../../common/languages.js';\nimport { getIconClasses } from '../../../common/services/getIconClasses.js';\nimport { IModelService } from '../../../common/services/model.js';\nimport { ILanguageService } from '../../../common/languages/language.js';\nimport * as nls from '../../../../nls.js';\nimport { FileKind } from '../../../../platform/files/common/files.js';\nimport { registerIcon } from '../../../../platform/theme/common/iconRegistry.js';\nimport { IThemeService } from '../../../../platform/theme/common/themeService.js';\nimport { canExpandCompletionItem } from './suggestWidgetDetails.js';\nexport function getAriaId(index) {\n  return `suggest-aria-id:${index}`;\n}\nconst suggestMoreInfoIcon = registerIcon('suggest-more-info', Codicon.chevronRight, nls.localize('suggestMoreInfoIcon', 'Icon for more information in the suggest widget.'));\nconst _completionItemColor = new class ColorExtractor {\n  static {\n    this._regexRelaxed = /(#([\\da-fA-F]{3}){1,2}|(rgb|hsl)a\\(\\s*(\\d{1,3}%?\\s*,\\s*){3}(1|0?\\.\\d+)\\)|(rgb|hsl)\\(\\s*\\d{1,3}%?(\\s*,\\s*\\d{1,3}%?){2}\\s*\\))/;\n  }\n  static {\n    this._regexStrict = new RegExp(`^${ColorExtractor._regexRelaxed.source}$`, 'i');\n  }\n  extract(item, out) {\n    if (item.textLabel.match(ColorExtractor._regexStrict)) {\n      out[0] = item.textLabel;\n      return true;\n    }\n    if (item.completion.detail && item.completion.detail.match(ColorExtractor._regexStrict)) {\n      out[0] = item.completion.detail;\n      return true;\n    }\n    if (item.completion.documentation) {\n      const value = typeof item.completion.documentation === 'string' ? item.completion.documentation : item.completion.documentation.value;\n      const match = ColorExtractor._regexRelaxed.exec(value);\n      if (match && (match.index === 0 || match.index + match[0].length === value.length)) {\n        out[0] = match[0];\n        return true;\n      }\n    }\n    return false;\n  }\n}();\nlet ItemRenderer = class ItemRenderer {\n  constructor(_editor, _modelService, _languageService, _themeService) {\n    this._editor = _editor;\n    this._modelService = _modelService;\n    this._languageService = _languageService;\n    this._themeService = _themeService;\n    this._onDidToggleDetails = new Emitter();\n    this.onDidToggleDetails = this._onDidToggleDetails.event;\n    this.templateId = 'suggestion';\n  }\n  dispose() {\n    this._onDidToggleDetails.dispose();\n  }\n  renderTemplate(container) {\n    const disposables = new DisposableStore();\n    const root = container;\n    root.classList.add('show-file-icons');\n    const icon = append(container, $('.icon'));\n    const colorspan = append(icon, $('span.colorspan'));\n    const text = append(container, $('.contents'));\n    const main = append(text, $('.main'));\n    const iconContainer = append(main, $('.icon-label.codicon'));\n    const left = append(main, $('span.left'));\n    const right = append(main, $('span.right'));\n    const iconLabel = new IconLabel(left, {\n      supportHighlights: true,\n      supportIcons: true\n    });\n    disposables.add(iconLabel);\n    const parametersLabel = append(left, $('span.signature-label'));\n    const qualifierLabel = append(left, $('span.qualifier-label'));\n    const detailsLabel = append(right, $('span.details-label'));\n    const readMore = append(right, $('span.readMore' + ThemeIcon.asCSSSelector(suggestMoreInfoIcon)));\n    readMore.title = nls.localize('readMore', \"Read More\");\n    const configureFont = () => {\n      const options = this._editor.getOptions();\n      const fontInfo = options.get(50 /* EditorOption.fontInfo */);\n      const fontFamily = fontInfo.getMassagedFontFamily();\n      const fontFeatureSettings = fontInfo.fontFeatureSettings;\n      const fontSize = options.get(120 /* EditorOption.suggestFontSize */) || fontInfo.fontSize;\n      const lineHeight = options.get(121 /* EditorOption.suggestLineHeight */) || fontInfo.lineHeight;\n      const fontWeight = fontInfo.fontWeight;\n      const letterSpacing = fontInfo.letterSpacing;\n      const fontSizePx = `${fontSize}px`;\n      const lineHeightPx = `${lineHeight}px`;\n      const letterSpacingPx = `${letterSpacing}px`;\n      root.style.fontSize = fontSizePx;\n      root.style.fontWeight = fontWeight;\n      root.style.letterSpacing = letterSpacingPx;\n      main.style.fontFamily = fontFamily;\n      main.style.fontFeatureSettings = fontFeatureSettings;\n      main.style.lineHeight = lineHeightPx;\n      icon.style.height = lineHeightPx;\n      icon.style.width = lineHeightPx;\n      readMore.style.height = lineHeightPx;\n      readMore.style.width = lineHeightPx;\n    };\n    return {\n      root,\n      left,\n      right,\n      icon,\n      colorspan,\n      iconLabel,\n      iconContainer,\n      parametersLabel,\n      qualifierLabel,\n      detailsLabel,\n      readMore,\n      disposables,\n      configureFont\n    };\n  }\n  renderElement(element, index, data) {\n    data.configureFont();\n    const {\n      completion\n    } = element;\n    data.root.id = getAriaId(index);\n    data.colorspan.style.backgroundColor = '';\n    const labelOptions = {\n      labelEscapeNewLines: true,\n      matches: createMatches(element.score)\n    };\n    const color = [];\n    if (completion.kind === 19 /* CompletionItemKind.Color */ && _completionItemColor.extract(element, color)) {\n      // special logic for 'color' completion items\n      data.icon.className = 'icon customcolor';\n      data.iconContainer.className = 'icon hide';\n      data.colorspan.style.backgroundColor = color[0];\n    } else if (completion.kind === 20 /* CompletionItemKind.File */ && this._themeService.getFileIconTheme().hasFileIcons) {\n      // special logic for 'file' completion items\n      data.icon.className = 'icon hide';\n      data.iconContainer.className = 'icon hide';\n      const labelClasses = getIconClasses(this._modelService, this._languageService, URI.from({\n        scheme: 'fake',\n        path: element.textLabel\n      }), FileKind.FILE);\n      const detailClasses = getIconClasses(this._modelService, this._languageService, URI.from({\n        scheme: 'fake',\n        path: completion.detail\n      }), FileKind.FILE);\n      labelOptions.extraClasses = labelClasses.length > detailClasses.length ? labelClasses : detailClasses;\n    } else if (completion.kind === 23 /* CompletionItemKind.Folder */ && this._themeService.getFileIconTheme().hasFolderIcons) {\n      // special logic for 'folder' completion items\n      data.icon.className = 'icon hide';\n      data.iconContainer.className = 'icon hide';\n      labelOptions.extraClasses = [getIconClasses(this._modelService, this._languageService, URI.from({\n        scheme: 'fake',\n        path: element.textLabel\n      }), FileKind.FOLDER), getIconClasses(this._modelService, this._languageService, URI.from({\n        scheme: 'fake',\n        path: completion.detail\n      }), FileKind.FOLDER)].flat();\n    } else {\n      // normal icon\n      data.icon.className = 'icon hide';\n      data.iconContainer.className = '';\n      data.iconContainer.classList.add('suggest-icon', ...ThemeIcon.asClassNameArray(CompletionItemKinds.toIcon(completion.kind)));\n    }\n    if (completion.tags && completion.tags.indexOf(1 /* CompletionItemTag.Deprecated */) >= 0) {\n      labelOptions.extraClasses = (labelOptions.extraClasses || []).concat(['deprecated']);\n      labelOptions.matches = [];\n    }\n    data.iconLabel.setLabel(element.textLabel, undefined, labelOptions);\n    if (typeof completion.label === 'string') {\n      data.parametersLabel.textContent = '';\n      data.detailsLabel.textContent = stripNewLines(completion.detail || '');\n      data.root.classList.add('string-label');\n    } else {\n      data.parametersLabel.textContent = stripNewLines(completion.label.detail || '');\n      data.detailsLabel.textContent = stripNewLines(completion.label.description || '');\n      data.root.classList.remove('string-label');\n    }\n    if (this._editor.getOption(119 /* EditorOption.suggest */).showInlineDetails) {\n      show(data.detailsLabel);\n    } else {\n      hide(data.detailsLabel);\n    }\n    if (canExpandCompletionItem(element)) {\n      data.right.classList.add('can-expand-details');\n      show(data.readMore);\n      data.readMore.onmousedown = e => {\n        e.stopPropagation();\n        e.preventDefault();\n      };\n      data.readMore.onclick = e => {\n        e.stopPropagation();\n        e.preventDefault();\n        this._onDidToggleDetails.fire();\n      };\n    } else {\n      data.right.classList.remove('can-expand-details');\n      hide(data.readMore);\n      data.readMore.onmousedown = null;\n      data.readMore.onclick = null;\n    }\n  }\n  disposeTemplate(templateData) {\n    templateData.disposables.dispose();\n  }\n};\nItemRenderer = __decorate([__param(1, IModelService), __param(2, ILanguageService), __param(3, IThemeService)], ItemRenderer);\nexport { ItemRenderer };\nfunction stripNewLines(str) {\n  return str.replace(/\\r\\n|\\r|\\n/g, '');\n}", "map": {"version": 3, "names": ["__decorate", "decorators", "target", "key", "desc", "c", "arguments", "length", "r", "Object", "getOwnPropertyDescriptor", "d", "Reflect", "decorate", "i", "defineProperty", "__param", "paramIndex", "decorator", "$", "append", "hide", "show", "IconLabel", "Codicon", "ThemeIcon", "Emitter", "createMatches", "DisposableStore", "URI", "CompletionItemKinds", "getIconClasses", "IModelService", "ILanguageService", "nls", "FileKind", "registerIcon", "IThemeService", "canExpandCompletionItem", "getAriaId", "index", "suggestMoreInfoIcon", "chevronRight", "localize", "_completionItemColor", "ColorExtractor", "_regexRelaxed", "_regexStrict", "RegExp", "source", "extract", "item", "out", "textLabel", "match", "completion", "detail", "documentation", "value", "exec", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "_editor", "_modelService", "_languageService", "_themeService", "_onDidToggleDetails", "onDidToggleDetails", "event", "templateId", "dispose", "renderTemplate", "container", "disposables", "root", "classList", "add", "icon", "colorspan", "text", "main", "iconContainer", "left", "right", "iconLabel", "supportHighlights", "supportIcons", "parametersLabel", "qualifier<PERSON><PERSON><PERSON>", "detailsLabel", "readMore", "asCSSSelector", "title", "configureFont", "options", "getOptions", "fontInfo", "get", "fontFamily", "getMassagedFontFamily", "fontFeatureSettings", "fontSize", "lineHeight", "fontWeight", "letterSpacing", "fontSizePx", "lineHeightPx", "letterSpacingPx", "style", "height", "width", "renderElement", "element", "data", "id", "backgroundColor", "labelOptions", "labelEscapeNewLines", "matches", "score", "color", "kind", "className", "getFileIconTheme", "hasFileIcons", "labelClasses", "from", "scheme", "path", "FILE", "detailClasses", "extraClasses", "hasFolderIcons", "FOLDER", "flat", "asClassNameArray", "toIcon", "tags", "indexOf", "concat", "<PERSON><PERSON><PERSON><PERSON>", "undefined", "label", "textContent", "stripNewLines", "description", "remove", "getOption", "showInlineDetails", "onmousedown", "e", "stopPropagation", "preventDefault", "onclick", "fire", "disposeTemplate", "templateData", "str", "replace"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/contrib/suggest/browser/suggestWidgetRenderer.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = (this && this.__param) || function (paramIndex, decorator) {\n    return function (target, key) { decorator(target, key, paramIndex); }\n};\nimport { $, append, hide, show } from '../../../../base/browser/dom.js';\nimport { IconLabel } from '../../../../base/browser/ui/iconLabel/iconLabel.js';\nimport { Codicon } from '../../../../base/common/codicons.js';\nimport { ThemeIcon } from '../../../../base/common/themables.js';\nimport { Emitter } from '../../../../base/common/event.js';\nimport { createMatches } from '../../../../base/common/filters.js';\nimport { DisposableStore } from '../../../../base/common/lifecycle.js';\nimport { URI } from '../../../../base/common/uri.js';\nimport { CompletionItemKinds } from '../../../common/languages.js';\nimport { getIconClasses } from '../../../common/services/getIconClasses.js';\nimport { IModelService } from '../../../common/services/model.js';\nimport { ILanguageService } from '../../../common/languages/language.js';\nimport * as nls from '../../../../nls.js';\nimport { FileKind } from '../../../../platform/files/common/files.js';\nimport { registerIcon } from '../../../../platform/theme/common/iconRegistry.js';\nimport { IThemeService } from '../../../../platform/theme/common/themeService.js';\nimport { canExpandCompletionItem } from './suggestWidgetDetails.js';\nexport function getAriaId(index) {\n    return `suggest-aria-id:${index}`;\n}\nconst suggestMoreInfoIcon = registerIcon('suggest-more-info', Codicon.chevronRight, nls.localize('suggestMoreInfoIcon', 'Icon for more information in the suggest widget.'));\nconst _completionItemColor = new class ColorExtractor {\n    static { this._regexRelaxed = /(#([\\da-fA-F]{3}){1,2}|(rgb|hsl)a\\(\\s*(\\d{1,3}%?\\s*,\\s*){3}(1|0?\\.\\d+)\\)|(rgb|hsl)\\(\\s*\\d{1,3}%?(\\s*,\\s*\\d{1,3}%?){2}\\s*\\))/; }\n    static { this._regexStrict = new RegExp(`^${ColorExtractor._regexRelaxed.source}$`, 'i'); }\n    extract(item, out) {\n        if (item.textLabel.match(ColorExtractor._regexStrict)) {\n            out[0] = item.textLabel;\n            return true;\n        }\n        if (item.completion.detail && item.completion.detail.match(ColorExtractor._regexStrict)) {\n            out[0] = item.completion.detail;\n            return true;\n        }\n        if (item.completion.documentation) {\n            const value = typeof item.completion.documentation === 'string'\n                ? item.completion.documentation\n                : item.completion.documentation.value;\n            const match = ColorExtractor._regexRelaxed.exec(value);\n            if (match && (match.index === 0 || match.index + match[0].length === value.length)) {\n                out[0] = match[0];\n                return true;\n            }\n        }\n        return false;\n    }\n};\nlet ItemRenderer = class ItemRenderer {\n    constructor(_editor, _modelService, _languageService, _themeService) {\n        this._editor = _editor;\n        this._modelService = _modelService;\n        this._languageService = _languageService;\n        this._themeService = _themeService;\n        this._onDidToggleDetails = new Emitter();\n        this.onDidToggleDetails = this._onDidToggleDetails.event;\n        this.templateId = 'suggestion';\n    }\n    dispose() {\n        this._onDidToggleDetails.dispose();\n    }\n    renderTemplate(container) {\n        const disposables = new DisposableStore();\n        const root = container;\n        root.classList.add('show-file-icons');\n        const icon = append(container, $('.icon'));\n        const colorspan = append(icon, $('span.colorspan'));\n        const text = append(container, $('.contents'));\n        const main = append(text, $('.main'));\n        const iconContainer = append(main, $('.icon-label.codicon'));\n        const left = append(main, $('span.left'));\n        const right = append(main, $('span.right'));\n        const iconLabel = new IconLabel(left, { supportHighlights: true, supportIcons: true });\n        disposables.add(iconLabel);\n        const parametersLabel = append(left, $('span.signature-label'));\n        const qualifierLabel = append(left, $('span.qualifier-label'));\n        const detailsLabel = append(right, $('span.details-label'));\n        const readMore = append(right, $('span.readMore' + ThemeIcon.asCSSSelector(suggestMoreInfoIcon)));\n        readMore.title = nls.localize('readMore', \"Read More\");\n        const configureFont = () => {\n            const options = this._editor.getOptions();\n            const fontInfo = options.get(50 /* EditorOption.fontInfo */);\n            const fontFamily = fontInfo.getMassagedFontFamily();\n            const fontFeatureSettings = fontInfo.fontFeatureSettings;\n            const fontSize = options.get(120 /* EditorOption.suggestFontSize */) || fontInfo.fontSize;\n            const lineHeight = options.get(121 /* EditorOption.suggestLineHeight */) || fontInfo.lineHeight;\n            const fontWeight = fontInfo.fontWeight;\n            const letterSpacing = fontInfo.letterSpacing;\n            const fontSizePx = `${fontSize}px`;\n            const lineHeightPx = `${lineHeight}px`;\n            const letterSpacingPx = `${letterSpacing}px`;\n            root.style.fontSize = fontSizePx;\n            root.style.fontWeight = fontWeight;\n            root.style.letterSpacing = letterSpacingPx;\n            main.style.fontFamily = fontFamily;\n            main.style.fontFeatureSettings = fontFeatureSettings;\n            main.style.lineHeight = lineHeightPx;\n            icon.style.height = lineHeightPx;\n            icon.style.width = lineHeightPx;\n            readMore.style.height = lineHeightPx;\n            readMore.style.width = lineHeightPx;\n        };\n        return { root, left, right, icon, colorspan, iconLabel, iconContainer, parametersLabel, qualifierLabel, detailsLabel, readMore, disposables, configureFont };\n    }\n    renderElement(element, index, data) {\n        data.configureFont();\n        const { completion } = element;\n        data.root.id = getAriaId(index);\n        data.colorspan.style.backgroundColor = '';\n        const labelOptions = {\n            labelEscapeNewLines: true,\n            matches: createMatches(element.score)\n        };\n        const color = [];\n        if (completion.kind === 19 /* CompletionItemKind.Color */ && _completionItemColor.extract(element, color)) {\n            // special logic for 'color' completion items\n            data.icon.className = 'icon customcolor';\n            data.iconContainer.className = 'icon hide';\n            data.colorspan.style.backgroundColor = color[0];\n        }\n        else if (completion.kind === 20 /* CompletionItemKind.File */ && this._themeService.getFileIconTheme().hasFileIcons) {\n            // special logic for 'file' completion items\n            data.icon.className = 'icon hide';\n            data.iconContainer.className = 'icon hide';\n            const labelClasses = getIconClasses(this._modelService, this._languageService, URI.from({ scheme: 'fake', path: element.textLabel }), FileKind.FILE);\n            const detailClasses = getIconClasses(this._modelService, this._languageService, URI.from({ scheme: 'fake', path: completion.detail }), FileKind.FILE);\n            labelOptions.extraClasses = labelClasses.length > detailClasses.length ? labelClasses : detailClasses;\n        }\n        else if (completion.kind === 23 /* CompletionItemKind.Folder */ && this._themeService.getFileIconTheme().hasFolderIcons) {\n            // special logic for 'folder' completion items\n            data.icon.className = 'icon hide';\n            data.iconContainer.className = 'icon hide';\n            labelOptions.extraClasses = [\n                getIconClasses(this._modelService, this._languageService, URI.from({ scheme: 'fake', path: element.textLabel }), FileKind.FOLDER),\n                getIconClasses(this._modelService, this._languageService, URI.from({ scheme: 'fake', path: completion.detail }), FileKind.FOLDER)\n            ].flat();\n        }\n        else {\n            // normal icon\n            data.icon.className = 'icon hide';\n            data.iconContainer.className = '';\n            data.iconContainer.classList.add('suggest-icon', ...ThemeIcon.asClassNameArray(CompletionItemKinds.toIcon(completion.kind)));\n        }\n        if (completion.tags && completion.tags.indexOf(1 /* CompletionItemTag.Deprecated */) >= 0) {\n            labelOptions.extraClasses = (labelOptions.extraClasses || []).concat(['deprecated']);\n            labelOptions.matches = [];\n        }\n        data.iconLabel.setLabel(element.textLabel, undefined, labelOptions);\n        if (typeof completion.label === 'string') {\n            data.parametersLabel.textContent = '';\n            data.detailsLabel.textContent = stripNewLines(completion.detail || '');\n            data.root.classList.add('string-label');\n        }\n        else {\n            data.parametersLabel.textContent = stripNewLines(completion.label.detail || '');\n            data.detailsLabel.textContent = stripNewLines(completion.label.description || '');\n            data.root.classList.remove('string-label');\n        }\n        if (this._editor.getOption(119 /* EditorOption.suggest */).showInlineDetails) {\n            show(data.detailsLabel);\n        }\n        else {\n            hide(data.detailsLabel);\n        }\n        if (canExpandCompletionItem(element)) {\n            data.right.classList.add('can-expand-details');\n            show(data.readMore);\n            data.readMore.onmousedown = e => {\n                e.stopPropagation();\n                e.preventDefault();\n            };\n            data.readMore.onclick = e => {\n                e.stopPropagation();\n                e.preventDefault();\n                this._onDidToggleDetails.fire();\n            };\n        }\n        else {\n            data.right.classList.remove('can-expand-details');\n            hide(data.readMore);\n            data.readMore.onmousedown = null;\n            data.readMore.onclick = null;\n        }\n    }\n    disposeTemplate(templateData) {\n        templateData.disposables.dispose();\n    }\n};\nItemRenderer = __decorate([\n    __param(1, IModelService),\n    __param(2, ILanguageService),\n    __param(3, IThemeService)\n], ItemRenderer);\nexport { ItemRenderer };\nfunction stripNewLines(str) {\n    return str.replace(/\\r\\n|\\r|\\n/g, '');\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,IAAIA,UAAU,GAAI,IAAI,IAAI,IAAI,CAACA,UAAU,IAAK,UAAUC,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAE;EACnF,IAAIC,CAAC,GAAGC,SAAS,CAACC,MAAM;IAAEC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGH,MAAM,GAAGE,IAAI,KAAK,IAAI,GAAGA,IAAI,GAAGK,MAAM,CAACC,wBAAwB,CAACR,MAAM,EAAEC,GAAG,CAAC,GAAGC,IAAI;IAAEO,CAAC;EAC5H,IAAI,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,CAACC,QAAQ,KAAK,UAAU,EAAEL,CAAC,GAAGI,OAAO,CAACC,QAAQ,CAACZ,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,CAAC,CAAC,KAC1H,KAAK,IAAIU,CAAC,GAAGb,UAAU,CAACM,MAAM,GAAG,CAAC,EAAEO,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE,IAAIH,CAAC,GAAGV,UAAU,CAACa,CAAC,CAAC,EAAEN,CAAC,GAAG,CAACH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACH,CAAC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACT,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,GAAGG,CAAC,CAACT,MAAM,EAAEC,GAAG,CAAC,KAAKK,CAAC;EACjJ,OAAOH,CAAC,GAAG,CAAC,IAAIG,CAAC,IAAIC,MAAM,CAACM,cAAc,CAACb,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,EAAEA,CAAC;AACjE,CAAC;AACD,IAAIQ,OAAO,GAAI,IAAI,IAAI,IAAI,CAACA,OAAO,IAAK,UAAUC,UAAU,EAAEC,SAAS,EAAE;EACrE,OAAO,UAAUhB,MAAM,EAAEC,GAAG,EAAE;IAAEe,SAAS,CAAChB,MAAM,EAAEC,GAAG,EAAEc,UAAU,CAAC;EAAE,CAAC;AACzE,CAAC;AACD,SAASE,CAAC,EAAEC,MAAM,EAAEC,IAAI,EAAEC,IAAI,QAAQ,iCAAiC;AACvE,SAASC,SAAS,QAAQ,oDAAoD;AAC9E,SAASC,OAAO,QAAQ,qCAAqC;AAC7D,SAASC,SAAS,QAAQ,sCAAsC;AAChE,SAASC,OAAO,QAAQ,kCAAkC;AAC1D,SAASC,aAAa,QAAQ,oCAAoC;AAClE,SAASC,eAAe,QAAQ,sCAAsC;AACtE,SAASC,GAAG,QAAQ,gCAAgC;AACpD,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,cAAc,QAAQ,4CAA4C;AAC3E,SAASC,aAAa,QAAQ,mCAAmC;AACjE,SAASC,gBAAgB,QAAQ,uCAAuC;AACxE,OAAO,KAAKC,GAAG,MAAM,oBAAoB;AACzC,SAASC,QAAQ,QAAQ,4CAA4C;AACrE,SAASC,YAAY,QAAQ,mDAAmD;AAChF,SAASC,aAAa,QAAQ,mDAAmD;AACjF,SAASC,uBAAuB,QAAQ,2BAA2B;AACnE,OAAO,SAASC,SAASA,CAACC,KAAK,EAAE;EAC7B,OAAO,mBAAmBA,KAAK,EAAE;AACrC;AACA,MAAMC,mBAAmB,GAAGL,YAAY,CAAC,mBAAmB,EAAEZ,OAAO,CAACkB,YAAY,EAAER,GAAG,CAACS,QAAQ,CAAC,qBAAqB,EAAE,kDAAkD,CAAC,CAAC;AAC5K,MAAMC,oBAAoB,GAAG,IAAI,MAAMC,cAAc,CAAC;EAClD;IAAS,IAAI,CAACC,aAAa,GAAG,6HAA6H;EAAE;EAC7J;IAAS,IAAI,CAACC,YAAY,GAAG,IAAIC,MAAM,CAAC,IAAIH,cAAc,CAACC,aAAa,CAACG,MAAM,GAAG,EAAE,GAAG,CAAC;EAAE;EAC1FC,OAAOA,CAACC,IAAI,EAAEC,GAAG,EAAE;IACf,IAAID,IAAI,CAACE,SAAS,CAACC,KAAK,CAACT,cAAc,CAACE,YAAY,CAAC,EAAE;MACnDK,GAAG,CAAC,CAAC,CAAC,GAAGD,IAAI,CAACE,SAAS;MACvB,OAAO,IAAI;IACf;IACA,IAAIF,IAAI,CAACI,UAAU,CAACC,MAAM,IAAIL,IAAI,CAACI,UAAU,CAACC,MAAM,CAACF,KAAK,CAACT,cAAc,CAACE,YAAY,CAAC,EAAE;MACrFK,GAAG,CAAC,CAAC,CAAC,GAAGD,IAAI,CAACI,UAAU,CAACC,MAAM;MAC/B,OAAO,IAAI;IACf;IACA,IAAIL,IAAI,CAACI,UAAU,CAACE,aAAa,EAAE;MAC/B,MAAMC,KAAK,GAAG,OAAOP,IAAI,CAACI,UAAU,CAACE,aAAa,KAAK,QAAQ,GACzDN,IAAI,CAACI,UAAU,CAACE,aAAa,GAC7BN,IAAI,CAACI,UAAU,CAACE,aAAa,CAACC,KAAK;MACzC,MAAMJ,KAAK,GAAGT,cAAc,CAACC,aAAa,CAACa,IAAI,CAACD,KAAK,CAAC;MACtD,IAAIJ,KAAK,KAAKA,KAAK,CAACd,KAAK,KAAK,CAAC,IAAIc,KAAK,CAACd,KAAK,GAAGc,KAAK,CAAC,CAAC,CAAC,CAAC/C,MAAM,KAAKmD,KAAK,CAACnD,MAAM,CAAC,EAAE;QAChF6C,GAAG,CAAC,CAAC,CAAC,GAAGE,KAAK,CAAC,CAAC,CAAC;QACjB,OAAO,IAAI;MACf;IACJ;IACA,OAAO,KAAK;EAChB;AACJ,CAAC,CAAD,CAAC;AACD,IAAIM,YAAY,GAAG,MAAMA,YAAY,CAAC;EAClCC,WAAWA,CAACC,OAAO,EAAEC,aAAa,EAAEC,gBAAgB,EAAEC,aAAa,EAAE;IACjE,IAAI,CAACH,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,mBAAmB,GAAG,IAAIxC,OAAO,CAAC,CAAC;IACxC,IAAI,CAACyC,kBAAkB,GAAG,IAAI,CAACD,mBAAmB,CAACE,KAAK;IACxD,IAAI,CAACC,UAAU,GAAG,YAAY;EAClC;EACAC,OAAOA,CAAA,EAAG;IACN,IAAI,CAACJ,mBAAmB,CAACI,OAAO,CAAC,CAAC;EACtC;EACAC,cAAcA,CAACC,SAAS,EAAE;IACtB,MAAMC,WAAW,GAAG,IAAI7C,eAAe,CAAC,CAAC;IACzC,MAAM8C,IAAI,GAAGF,SAAS;IACtBE,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,iBAAiB,CAAC;IACrC,MAAMC,IAAI,GAAGzD,MAAM,CAACoD,SAAS,EAAErD,CAAC,CAAC,OAAO,CAAC,CAAC;IAC1C,MAAM2D,SAAS,GAAG1D,MAAM,CAACyD,IAAI,EAAE1D,CAAC,CAAC,gBAAgB,CAAC,CAAC;IACnD,MAAM4D,IAAI,GAAG3D,MAAM,CAACoD,SAAS,EAAErD,CAAC,CAAC,WAAW,CAAC,CAAC;IAC9C,MAAM6D,IAAI,GAAG5D,MAAM,CAAC2D,IAAI,EAAE5D,CAAC,CAAC,OAAO,CAAC,CAAC;IACrC,MAAM8D,aAAa,GAAG7D,MAAM,CAAC4D,IAAI,EAAE7D,CAAC,CAAC,qBAAqB,CAAC,CAAC;IAC5D,MAAM+D,IAAI,GAAG9D,MAAM,CAAC4D,IAAI,EAAE7D,CAAC,CAAC,WAAW,CAAC,CAAC;IACzC,MAAMgE,KAAK,GAAG/D,MAAM,CAAC4D,IAAI,EAAE7D,CAAC,CAAC,YAAY,CAAC,CAAC;IAC3C,MAAMiE,SAAS,GAAG,IAAI7D,SAAS,CAAC2D,IAAI,EAAE;MAAEG,iBAAiB,EAAE,IAAI;MAAEC,YAAY,EAAE;IAAK,CAAC,CAAC;IACtFb,WAAW,CAACG,GAAG,CAACQ,SAAS,CAAC;IAC1B,MAAMG,eAAe,GAAGnE,MAAM,CAAC8D,IAAI,EAAE/D,CAAC,CAAC,sBAAsB,CAAC,CAAC;IAC/D,MAAMqE,cAAc,GAAGpE,MAAM,CAAC8D,IAAI,EAAE/D,CAAC,CAAC,sBAAsB,CAAC,CAAC;IAC9D,MAAMsE,YAAY,GAAGrE,MAAM,CAAC+D,KAAK,EAAEhE,CAAC,CAAC,oBAAoB,CAAC,CAAC;IAC3D,MAAMuE,QAAQ,GAAGtE,MAAM,CAAC+D,KAAK,EAAEhE,CAAC,CAAC,eAAe,GAAGM,SAAS,CAACkE,aAAa,CAAClD,mBAAmB,CAAC,CAAC,CAAC;IACjGiD,QAAQ,CAACE,KAAK,GAAG1D,GAAG,CAACS,QAAQ,CAAC,UAAU,EAAE,WAAW,CAAC;IACtD,MAAMkD,aAAa,GAAGA,CAAA,KAAM;MACxB,MAAMC,OAAO,GAAG,IAAI,CAAChC,OAAO,CAACiC,UAAU,CAAC,CAAC;MACzC,MAAMC,QAAQ,GAAGF,OAAO,CAACG,GAAG,CAAC,EAAE,CAAC,2BAA2B,CAAC;MAC5D,MAAMC,UAAU,GAAGF,QAAQ,CAACG,qBAAqB,CAAC,CAAC;MACnD,MAAMC,mBAAmB,GAAGJ,QAAQ,CAACI,mBAAmB;MACxD,MAAMC,QAAQ,GAAGP,OAAO,CAACG,GAAG,CAAC,GAAG,CAAC,kCAAkC,CAAC,IAAID,QAAQ,CAACK,QAAQ;MACzF,MAAMC,UAAU,GAAGR,OAAO,CAACG,GAAG,CAAC,GAAG,CAAC,oCAAoC,CAAC,IAAID,QAAQ,CAACM,UAAU;MAC/F,MAAMC,UAAU,GAAGP,QAAQ,CAACO,UAAU;MACtC,MAAMC,aAAa,GAAGR,QAAQ,CAACQ,aAAa;MAC5C,MAAMC,UAAU,GAAG,GAAGJ,QAAQ,IAAI;MAClC,MAAMK,YAAY,GAAG,GAAGJ,UAAU,IAAI;MACtC,MAAMK,eAAe,GAAG,GAAGH,aAAa,IAAI;MAC5C9B,IAAI,CAACkC,KAAK,CAACP,QAAQ,GAAGI,UAAU;MAChC/B,IAAI,CAACkC,KAAK,CAACL,UAAU,GAAGA,UAAU;MAClC7B,IAAI,CAACkC,KAAK,CAACJ,aAAa,GAAGG,eAAe;MAC1C3B,IAAI,CAAC4B,KAAK,CAACV,UAAU,GAAGA,UAAU;MAClClB,IAAI,CAAC4B,KAAK,CAACR,mBAAmB,GAAGA,mBAAmB;MACpDpB,IAAI,CAAC4B,KAAK,CAACN,UAAU,GAAGI,YAAY;MACpC7B,IAAI,CAAC+B,KAAK,CAACC,MAAM,GAAGH,YAAY;MAChC7B,IAAI,CAAC+B,KAAK,CAACE,KAAK,GAAGJ,YAAY;MAC/BhB,QAAQ,CAACkB,KAAK,CAACC,MAAM,GAAGH,YAAY;MACpChB,QAAQ,CAACkB,KAAK,CAACE,KAAK,GAAGJ,YAAY;IACvC,CAAC;IACD,OAAO;MAAEhC,IAAI;MAAEQ,IAAI;MAAEC,KAAK;MAAEN,IAAI;MAAEC,SAAS;MAAEM,SAAS;MAAEH,aAAa;MAAEM,eAAe;MAAEC,cAAc;MAAEC,YAAY;MAAEC,QAAQ;MAAEjB,WAAW;MAAEoB;IAAc,CAAC;EAChK;EACAkB,aAAaA,CAACC,OAAO,EAAExE,KAAK,EAAEyE,IAAI,EAAE;IAChCA,IAAI,CAACpB,aAAa,CAAC,CAAC;IACpB,MAAM;MAAEtC;IAAW,CAAC,GAAGyD,OAAO;IAC9BC,IAAI,CAACvC,IAAI,CAACwC,EAAE,GAAG3E,SAAS,CAACC,KAAK,CAAC;IAC/ByE,IAAI,CAACnC,SAAS,CAAC8B,KAAK,CAACO,eAAe,GAAG,EAAE;IACzC,MAAMC,YAAY,GAAG;MACjBC,mBAAmB,EAAE,IAAI;MACzBC,OAAO,EAAE3F,aAAa,CAACqF,OAAO,CAACO,KAAK;IACxC,CAAC;IACD,MAAMC,KAAK,GAAG,EAAE;IAChB,IAAIjE,UAAU,CAACkE,IAAI,KAAK,EAAE,CAAC,kCAAkC7E,oBAAoB,CAACM,OAAO,CAAC8D,OAAO,EAAEQ,KAAK,CAAC,EAAE;MACvG;MACAP,IAAI,CAACpC,IAAI,CAAC6C,SAAS,GAAG,kBAAkB;MACxCT,IAAI,CAAChC,aAAa,CAACyC,SAAS,GAAG,WAAW;MAC1CT,IAAI,CAACnC,SAAS,CAAC8B,KAAK,CAACO,eAAe,GAAGK,KAAK,CAAC,CAAC,CAAC;IACnD,CAAC,MACI,IAAIjE,UAAU,CAACkE,IAAI,KAAK,EAAE,CAAC,iCAAiC,IAAI,CAACxD,aAAa,CAAC0D,gBAAgB,CAAC,CAAC,CAACC,YAAY,EAAE;MACjH;MACAX,IAAI,CAACpC,IAAI,CAAC6C,SAAS,GAAG,WAAW;MACjCT,IAAI,CAAChC,aAAa,CAACyC,SAAS,GAAG,WAAW;MAC1C,MAAMG,YAAY,GAAG9F,cAAc,CAAC,IAAI,CAACgC,aAAa,EAAE,IAAI,CAACC,gBAAgB,EAAEnC,GAAG,CAACiG,IAAI,CAAC;QAAEC,MAAM,EAAE,MAAM;QAAEC,IAAI,EAAEhB,OAAO,CAAC3D;MAAU,CAAC,CAAC,EAAElB,QAAQ,CAAC8F,IAAI,CAAC;MACpJ,MAAMC,aAAa,GAAGnG,cAAc,CAAC,IAAI,CAACgC,aAAa,EAAE,IAAI,CAACC,gBAAgB,EAAEnC,GAAG,CAACiG,IAAI,CAAC;QAAEC,MAAM,EAAE,MAAM;QAAEC,IAAI,EAAEzE,UAAU,CAACC;MAAO,CAAC,CAAC,EAAErB,QAAQ,CAAC8F,IAAI,CAAC;MACrJb,YAAY,CAACe,YAAY,GAAGN,YAAY,CAACtH,MAAM,GAAG2H,aAAa,CAAC3H,MAAM,GAAGsH,YAAY,GAAGK,aAAa;IACzG,CAAC,MACI,IAAI3E,UAAU,CAACkE,IAAI,KAAK,EAAE,CAAC,mCAAmC,IAAI,CAACxD,aAAa,CAAC0D,gBAAgB,CAAC,CAAC,CAACS,cAAc,EAAE;MACrH;MACAnB,IAAI,CAACpC,IAAI,CAAC6C,SAAS,GAAG,WAAW;MACjCT,IAAI,CAAChC,aAAa,CAACyC,SAAS,GAAG,WAAW;MAC1CN,YAAY,CAACe,YAAY,GAAG,CACxBpG,cAAc,CAAC,IAAI,CAACgC,aAAa,EAAE,IAAI,CAACC,gBAAgB,EAAEnC,GAAG,CAACiG,IAAI,CAAC;QAAEC,MAAM,EAAE,MAAM;QAAEC,IAAI,EAAEhB,OAAO,CAAC3D;MAAU,CAAC,CAAC,EAAElB,QAAQ,CAACkG,MAAM,CAAC,EACjItG,cAAc,CAAC,IAAI,CAACgC,aAAa,EAAE,IAAI,CAACC,gBAAgB,EAAEnC,GAAG,CAACiG,IAAI,CAAC;QAAEC,MAAM,EAAE,MAAM;QAAEC,IAAI,EAAEzE,UAAU,CAACC;MAAO,CAAC,CAAC,EAAErB,QAAQ,CAACkG,MAAM,CAAC,CACpI,CAACC,IAAI,CAAC,CAAC;IACZ,CAAC,MACI;MACD;MACArB,IAAI,CAACpC,IAAI,CAAC6C,SAAS,GAAG,WAAW;MACjCT,IAAI,CAAChC,aAAa,CAACyC,SAAS,GAAG,EAAE;MACjCT,IAAI,CAAChC,aAAa,CAACN,SAAS,CAACC,GAAG,CAAC,cAAc,EAAE,GAAGnD,SAAS,CAAC8G,gBAAgB,CAACzG,mBAAmB,CAAC0G,MAAM,CAACjF,UAAU,CAACkE,IAAI,CAAC,CAAC,CAAC;IAChI;IACA,IAAIlE,UAAU,CAACkF,IAAI,IAAIlF,UAAU,CAACkF,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC,kCAAkC,CAAC,IAAI,CAAC,EAAE;MACvFtB,YAAY,CAACe,YAAY,GAAG,CAACf,YAAY,CAACe,YAAY,IAAI,EAAE,EAAEQ,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC;MACpFvB,YAAY,CAACE,OAAO,GAAG,EAAE;IAC7B;IACAL,IAAI,CAAC7B,SAAS,CAACwD,QAAQ,CAAC5B,OAAO,CAAC3D,SAAS,EAAEwF,SAAS,EAAEzB,YAAY,CAAC;IACnE,IAAI,OAAO7D,UAAU,CAACuF,KAAK,KAAK,QAAQ,EAAE;MACtC7B,IAAI,CAAC1B,eAAe,CAACwD,WAAW,GAAG,EAAE;MACrC9B,IAAI,CAACxB,YAAY,CAACsD,WAAW,GAAGC,aAAa,CAACzF,UAAU,CAACC,MAAM,IAAI,EAAE,CAAC;MACtEyD,IAAI,CAACvC,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,cAAc,CAAC;IAC3C,CAAC,MACI;MACDqC,IAAI,CAAC1B,eAAe,CAACwD,WAAW,GAAGC,aAAa,CAACzF,UAAU,CAACuF,KAAK,CAACtF,MAAM,IAAI,EAAE,CAAC;MAC/EyD,IAAI,CAACxB,YAAY,CAACsD,WAAW,GAAGC,aAAa,CAACzF,UAAU,CAACuF,KAAK,CAACG,WAAW,IAAI,EAAE,CAAC;MACjFhC,IAAI,CAACvC,IAAI,CAACC,SAAS,CAACuE,MAAM,CAAC,cAAc,CAAC;IAC9C;IACA,IAAI,IAAI,CAACpF,OAAO,CAACqF,SAAS,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAACC,iBAAiB,EAAE;MAC1E9H,IAAI,CAAC2F,IAAI,CAACxB,YAAY,CAAC;IAC3B,CAAC,MACI;MACDpE,IAAI,CAAC4F,IAAI,CAACxB,YAAY,CAAC;IAC3B;IACA,IAAInD,uBAAuB,CAAC0E,OAAO,CAAC,EAAE;MAClCC,IAAI,CAAC9B,KAAK,CAACR,SAAS,CAACC,GAAG,CAAC,oBAAoB,CAAC;MAC9CtD,IAAI,CAAC2F,IAAI,CAACvB,QAAQ,CAAC;MACnBuB,IAAI,CAACvB,QAAQ,CAAC2D,WAAW,GAAGC,CAAC,IAAI;QAC7BA,CAAC,CAACC,eAAe,CAAC,CAAC;QACnBD,CAAC,CAACE,cAAc,CAAC,CAAC;MACtB,CAAC;MACDvC,IAAI,CAACvB,QAAQ,CAAC+D,OAAO,GAAGH,CAAC,IAAI;QACzBA,CAAC,CAACC,eAAe,CAAC,CAAC;QACnBD,CAAC,CAACE,cAAc,CAAC,CAAC;QAClB,IAAI,CAACtF,mBAAmB,CAACwF,IAAI,CAAC,CAAC;MACnC,CAAC;IACL,CAAC,MACI;MACDzC,IAAI,CAAC9B,KAAK,CAACR,SAAS,CAACuE,MAAM,CAAC,oBAAoB,CAAC;MACjD7H,IAAI,CAAC4F,IAAI,CAACvB,QAAQ,CAAC;MACnBuB,IAAI,CAACvB,QAAQ,CAAC2D,WAAW,GAAG,IAAI;MAChCpC,IAAI,CAACvB,QAAQ,CAAC+D,OAAO,GAAG,IAAI;IAChC;EACJ;EACAE,eAAeA,CAACC,YAAY,EAAE;IAC1BA,YAAY,CAACnF,WAAW,CAACH,OAAO,CAAC,CAAC;EACtC;AACJ,CAAC;AACDV,YAAY,GAAG5D,UAAU,CAAC,CACtBgB,OAAO,CAAC,CAAC,EAAEgB,aAAa,CAAC,EACzBhB,OAAO,CAAC,CAAC,EAAEiB,gBAAgB,CAAC,EAC5BjB,OAAO,CAAC,CAAC,EAAEqB,aAAa,CAAC,CAC5B,EAAEuB,YAAY,CAAC;AAChB,SAASA,YAAY;AACrB,SAASoF,aAAaA,CAACa,GAAG,EAAE;EACxB,OAAOA,GAAG,CAACC,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC;AACzC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}