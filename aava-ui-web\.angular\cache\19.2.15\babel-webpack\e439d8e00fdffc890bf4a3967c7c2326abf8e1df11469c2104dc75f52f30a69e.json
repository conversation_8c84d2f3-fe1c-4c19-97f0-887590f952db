{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { assertFn, checkAdjacentItems } from '../../../base/common/assert.js';\nimport { BugIndicatingError } from '../../../base/common/errors.js';\nimport { Position } from './position.js';\nimport { PositionOffsetTransformer } from './positionToOffset.js';\nimport { Range } from './range.js';\nimport { TextLength } from './textLength.js';\nexport class TextEdit {\n  constructor(edits) {\n    this.edits = edits;\n    assertFn(() => checkAdjacentItems(edits, (a, b) => a.range.getEndPosition().isBeforeOrEqual(b.range.getStartPosition())));\n  }\n  apply(text) {\n    let result = '';\n    let lastEditEnd = new Position(1, 1);\n    for (const edit of this.edits) {\n      const editRange = edit.range;\n      const editStart = editRange.getStartPosition();\n      const editEnd = editRange.getEndPosition();\n      const r = rangeFromPositions(lastEditEnd, editStart);\n      if (!r.isEmpty()) {\n        result += text.getValueOfRange(r);\n      }\n      result += edit.text;\n      lastEditEnd = editEnd;\n    }\n    const r = rangeFromPositions(lastEditEnd, text.endPositionExclusive);\n    if (!r.isEmpty()) {\n      result += text.getValueOfRange(r);\n    }\n    return result;\n  }\n  applyToString(str) {\n    const strText = new StringText(str);\n    return this.apply(strText);\n  }\n  getNewRanges() {\n    const newRanges = [];\n    let previousEditEndLineNumber = 0;\n    let lineOffset = 0;\n    let columnOffset = 0;\n    for (const edit of this.edits) {\n      const textLength = TextLength.ofText(edit.text);\n      const newRangeStart = Position.lift({\n        lineNumber: edit.range.startLineNumber + lineOffset,\n        column: edit.range.startColumn + (edit.range.startLineNumber === previousEditEndLineNumber ? columnOffset : 0)\n      });\n      const newRange = textLength.createRange(newRangeStart);\n      newRanges.push(newRange);\n      lineOffset = newRange.endLineNumber - edit.range.endLineNumber;\n      columnOffset = newRange.endColumn - edit.range.endColumn;\n      previousEditEndLineNumber = edit.range.endLineNumber;\n    }\n    return newRanges;\n  }\n}\nexport class SingleTextEdit {\n  constructor(range, text) {\n    this.range = range;\n    this.text = text;\n  }\n  toSingleEditOperation() {\n    return {\n      range: this.range,\n      text: this.text\n    };\n  }\n}\nfunction rangeFromPositions(start, end) {\n  if (start.lineNumber === end.lineNumber && start.column === Number.MAX_SAFE_INTEGER) {\n    return Range.fromPositions(end, end);\n  } else if (!start.isBeforeOrEqual(end)) {\n    throw new BugIndicatingError('start must be before end');\n  }\n  return new Range(start.lineNumber, start.column, end.lineNumber, end.column);\n}\nexport class AbstractText {\n  get endPositionExclusive() {\n    return this.length.addToPosition(new Position(1, 1));\n  }\n}\nexport class StringText extends AbstractText {\n  constructor(value) {\n    super();\n    this.value = value;\n    this._t = new PositionOffsetTransformer(this.value);\n  }\n  getValueOfRange(range) {\n    return this._t.getOffsetRange(range).substring(this.value);\n  }\n  get length() {\n    return this._t.textLength;\n  }\n}", "map": {"version": 3, "names": ["assertFn", "checkAdjacentItems", "BugIndicatingError", "Position", "PositionOffsetTransformer", "Range", "TextLength", "TextEdit", "constructor", "edits", "a", "b", "range", "getEndPosition", "isBeforeOrEqual", "getStartPosition", "apply", "text", "result", "lastEditEnd", "edit", "edit<PERSON>ange", "editStart", "editEnd", "r", "rangeFromPositions", "isEmpty", "getValueOfRange", "endPositionExclusive", "applyToString", "str", "strText", "StringText", "getNewRanges", "newRang<PERSON>", "previousEditEndLineNumber", "lineOffset", "columnOffset", "textLength", "ofText", "newRangeStart", "lift", "lineNumber", "startLineNumber", "column", "startColumn", "newRange", "createRange", "push", "endLineNumber", "endColumn", "SingleTextEdit", "toSingleEditOperation", "start", "end", "Number", "MAX_SAFE_INTEGER", "fromPositions", "AbstractText", "length", "addToPosition", "value", "_t", "getOffsetRange", "substring"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/core/textEdit.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { assertFn, checkAdjacentItems } from '../../../base/common/assert.js';\nimport { BugIndicatingError } from '../../../base/common/errors.js';\nimport { Position } from './position.js';\nimport { PositionOffsetTransformer } from './positionToOffset.js';\nimport { Range } from './range.js';\nimport { TextLength } from './textLength.js';\nexport class TextEdit {\n    constructor(edits) {\n        this.edits = edits;\n        assertFn(() => checkAdjacentItems(edits, (a, b) => a.range.getEndPosition().isBeforeOrEqual(b.range.getStartPosition())));\n    }\n    apply(text) {\n        let result = '';\n        let lastEditEnd = new Position(1, 1);\n        for (const edit of this.edits) {\n            const editRange = edit.range;\n            const editStart = editRange.getStartPosition();\n            const editEnd = editRange.getEndPosition();\n            const r = rangeFromPositions(lastEditEnd, editStart);\n            if (!r.isEmpty()) {\n                result += text.getValueOfRange(r);\n            }\n            result += edit.text;\n            lastEditEnd = editEnd;\n        }\n        const r = rangeFromPositions(lastEditEnd, text.endPositionExclusive);\n        if (!r.isEmpty()) {\n            result += text.getValueOfRange(r);\n        }\n        return result;\n    }\n    applyToString(str) {\n        const strText = new StringText(str);\n        return this.apply(strText);\n    }\n    getNewRanges() {\n        const newRanges = [];\n        let previousEditEndLineNumber = 0;\n        let lineOffset = 0;\n        let columnOffset = 0;\n        for (const edit of this.edits) {\n            const textLength = TextLength.ofText(edit.text);\n            const newRangeStart = Position.lift({\n                lineNumber: edit.range.startLineNumber + lineOffset,\n                column: edit.range.startColumn + (edit.range.startLineNumber === previousEditEndLineNumber ? columnOffset : 0)\n            });\n            const newRange = textLength.createRange(newRangeStart);\n            newRanges.push(newRange);\n            lineOffset = newRange.endLineNumber - edit.range.endLineNumber;\n            columnOffset = newRange.endColumn - edit.range.endColumn;\n            previousEditEndLineNumber = edit.range.endLineNumber;\n        }\n        return newRanges;\n    }\n}\nexport class SingleTextEdit {\n    constructor(range, text) {\n        this.range = range;\n        this.text = text;\n    }\n    toSingleEditOperation() {\n        return {\n            range: this.range,\n            text: this.text,\n        };\n    }\n}\nfunction rangeFromPositions(start, end) {\n    if (start.lineNumber === end.lineNumber && start.column === Number.MAX_SAFE_INTEGER) {\n        return Range.fromPositions(end, end);\n    }\n    else if (!start.isBeforeOrEqual(end)) {\n        throw new BugIndicatingError('start must be before end');\n    }\n    return new Range(start.lineNumber, start.column, end.lineNumber, end.column);\n}\nexport class AbstractText {\n    get endPositionExclusive() {\n        return this.length.addToPosition(new Position(1, 1));\n    }\n}\nexport class StringText extends AbstractText {\n    constructor(value) {\n        super();\n        this.value = value;\n        this._t = new PositionOffsetTransformer(this.value);\n    }\n    getValueOfRange(range) {\n        return this._t.getOffsetRange(range).substring(this.value);\n    }\n    get length() {\n        return this._t.textLength;\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,QAAQ,EAAEC,kBAAkB,QAAQ,gCAAgC;AAC7E,SAASC,kBAAkB,QAAQ,gCAAgC;AACnE,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,yBAAyB,QAAQ,uBAAuB;AACjE,SAASC,KAAK,QAAQ,YAAY;AAClC,SAASC,UAAU,QAAQ,iBAAiB;AAC5C,OAAO,MAAMC,QAAQ,CAAC;EAClBC,WAAWA,CAACC,KAAK,EAAE;IACf,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClBT,QAAQ,CAAC,MAAMC,kBAAkB,CAACQ,KAAK,EAAE,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACE,KAAK,CAACC,cAAc,CAAC,CAAC,CAACC,eAAe,CAACH,CAAC,CAACC,KAAK,CAACG,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7H;EACAC,KAAKA,CAACC,IAAI,EAAE;IACR,IAAIC,MAAM,GAAG,EAAE;IACf,IAAIC,WAAW,GAAG,IAAIhB,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;IACpC,KAAK,MAAMiB,IAAI,IAAI,IAAI,CAACX,KAAK,EAAE;MAC3B,MAAMY,SAAS,GAAGD,IAAI,CAACR,KAAK;MAC5B,MAAMU,SAAS,GAAGD,SAAS,CAACN,gBAAgB,CAAC,CAAC;MAC9C,MAAMQ,OAAO,GAAGF,SAAS,CAACR,cAAc,CAAC,CAAC;MAC1C,MAAMW,CAAC,GAAGC,kBAAkB,CAACN,WAAW,EAAEG,SAAS,CAAC;MACpD,IAAI,CAACE,CAAC,CAACE,OAAO,CAAC,CAAC,EAAE;QACdR,MAAM,IAAID,IAAI,CAACU,eAAe,CAACH,CAAC,CAAC;MACrC;MACAN,MAAM,IAAIE,IAAI,CAACH,IAAI;MACnBE,WAAW,GAAGI,OAAO;IACzB;IACA,MAAMC,CAAC,GAAGC,kBAAkB,CAACN,WAAW,EAAEF,IAAI,CAACW,oBAAoB,CAAC;IACpE,IAAI,CAACJ,CAAC,CAACE,OAAO,CAAC,CAAC,EAAE;MACdR,MAAM,IAAID,IAAI,CAACU,eAAe,CAACH,CAAC,CAAC;IACrC;IACA,OAAON,MAAM;EACjB;EACAW,aAAaA,CAACC,GAAG,EAAE;IACf,MAAMC,OAAO,GAAG,IAAIC,UAAU,CAACF,GAAG,CAAC;IACnC,OAAO,IAAI,CAACd,KAAK,CAACe,OAAO,CAAC;EAC9B;EACAE,YAAYA,CAAA,EAAG;IACX,MAAMC,SAAS,GAAG,EAAE;IACpB,IAAIC,yBAAyB,GAAG,CAAC;IACjC,IAAIC,UAAU,GAAG,CAAC;IAClB,IAAIC,YAAY,GAAG,CAAC;IACpB,KAAK,MAAMjB,IAAI,IAAI,IAAI,CAACX,KAAK,EAAE;MAC3B,MAAM6B,UAAU,GAAGhC,UAAU,CAACiC,MAAM,CAACnB,IAAI,CAACH,IAAI,CAAC;MAC/C,MAAMuB,aAAa,GAAGrC,QAAQ,CAACsC,IAAI,CAAC;QAChCC,UAAU,EAAEtB,IAAI,CAACR,KAAK,CAAC+B,eAAe,GAAGP,UAAU;QACnDQ,MAAM,EAAExB,IAAI,CAACR,KAAK,CAACiC,WAAW,IAAIzB,IAAI,CAACR,KAAK,CAAC+B,eAAe,KAAKR,yBAAyB,GAAGE,YAAY,GAAG,CAAC;MACjH,CAAC,CAAC;MACF,MAAMS,QAAQ,GAAGR,UAAU,CAACS,WAAW,CAACP,aAAa,CAAC;MACtDN,SAAS,CAACc,IAAI,CAACF,QAAQ,CAAC;MACxBV,UAAU,GAAGU,QAAQ,CAACG,aAAa,GAAG7B,IAAI,CAACR,KAAK,CAACqC,aAAa;MAC9DZ,YAAY,GAAGS,QAAQ,CAACI,SAAS,GAAG9B,IAAI,CAACR,KAAK,CAACsC,SAAS;MACxDf,yBAAyB,GAAGf,IAAI,CAACR,KAAK,CAACqC,aAAa;IACxD;IACA,OAAOf,SAAS;EACpB;AACJ;AACA,OAAO,MAAMiB,cAAc,CAAC;EACxB3C,WAAWA,CAACI,KAAK,EAAEK,IAAI,EAAE;IACrB,IAAI,CAACL,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACK,IAAI,GAAGA,IAAI;EACpB;EACAmC,qBAAqBA,CAAA,EAAG;IACpB,OAAO;MACHxC,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBK,IAAI,EAAE,IAAI,CAACA;IACf,CAAC;EACL;AACJ;AACA,SAASQ,kBAAkBA,CAAC4B,KAAK,EAAEC,GAAG,EAAE;EACpC,IAAID,KAAK,CAACX,UAAU,KAAKY,GAAG,CAACZ,UAAU,IAAIW,KAAK,CAACT,MAAM,KAAKW,MAAM,CAACC,gBAAgB,EAAE;IACjF,OAAOnD,KAAK,CAACoD,aAAa,CAACH,GAAG,EAAEA,GAAG,CAAC;EACxC,CAAC,MACI,IAAI,CAACD,KAAK,CAACvC,eAAe,CAACwC,GAAG,CAAC,EAAE;IAClC,MAAM,IAAIpD,kBAAkB,CAAC,0BAA0B,CAAC;EAC5D;EACA,OAAO,IAAIG,KAAK,CAACgD,KAAK,CAACX,UAAU,EAAEW,KAAK,CAACT,MAAM,EAAEU,GAAG,CAACZ,UAAU,EAAEY,GAAG,CAACV,MAAM,CAAC;AAChF;AACA,OAAO,MAAMc,YAAY,CAAC;EACtB,IAAI9B,oBAAoBA,CAAA,EAAG;IACvB,OAAO,IAAI,CAAC+B,MAAM,CAACC,aAAa,CAAC,IAAIzD,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACxD;AACJ;AACA,OAAO,MAAM6B,UAAU,SAAS0B,YAAY,CAAC;EACzClD,WAAWA,CAACqD,KAAK,EAAE;IACf,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,EAAE,GAAG,IAAI1D,yBAAyB,CAAC,IAAI,CAACyD,KAAK,CAAC;EACvD;EACAlC,eAAeA,CAACf,KAAK,EAAE;IACnB,OAAO,IAAI,CAACkD,EAAE,CAACC,cAAc,CAACnD,KAAK,CAAC,CAACoD,SAAS,CAAC,IAAI,CAACH,KAAK,CAAC;EAC9D;EACA,IAAIF,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACG,EAAE,CAACxB,UAAU;EAC7B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}