{"ast": null, "code": "function count(node) {\n  var sum = 0,\n    children = node.children,\n    i = children && children.length;\n  if (!i) sum = 1;else while (--i >= 0) sum += children[i].value;\n  node.value = sum;\n}\nexport default function () {\n  return this.eachAfter(count);\n}", "map": {"version": 3, "names": ["count", "node", "sum", "children", "i", "length", "value", "eachAfter"], "sources": ["C:/console/aava-ui-web/node_modules/d3-hierarchy/src/hierarchy/count.js"], "sourcesContent": ["function count(node) {\n  var sum = 0,\n      children = node.children,\n      i = children && children.length;\n  if (!i) sum = 1;\n  else while (--i >= 0) sum += children[i].value;\n  node.value = sum;\n}\n\nexport default function() {\n  return this.eachAfter(count);\n}\n"], "mappings": "AAAA,SAASA,KAAKA,CAACC,IAAI,EAAE;EACnB,IAAIC,GAAG,GAAG,CAAC;IACPC,QAAQ,GAAGF,IAAI,CAACE,QAAQ;IACxBC,CAAC,GAAGD,QAAQ,IAAIA,QAAQ,CAACE,MAAM;EACnC,IAAI,CAACD,CAAC,EAAEF,GAAG,GAAG,CAAC,CAAC,KACX,OAAO,EAAEE,CAAC,IAAI,CAAC,EAAEF,GAAG,IAAIC,QAAQ,CAACC,CAAC,CAAC,CAACE,KAAK;EAC9CL,IAAI,CAACK,KAAK,GAAGJ,GAAG;AAClB;AAEA,eAAe,YAAW;EACxB,OAAO,IAAI,CAACK,SAAS,CAACP,KAAK,CAAC;AAC9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}