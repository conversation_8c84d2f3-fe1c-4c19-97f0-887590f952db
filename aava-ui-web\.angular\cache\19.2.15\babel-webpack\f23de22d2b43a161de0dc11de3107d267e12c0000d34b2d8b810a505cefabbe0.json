{"ast": null, "code": "import _asyncToGenerator from \"C:/console/aava-ui-web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = this && this.__param || function (paramIndex, decorator) {\n  return function (target, key) {\n    decorator(target, key, paramIndex);\n  };\n};\nimport { Disposable, DisposableStore, toDisposable } from '../../../../base/common/lifecycle.js';\nimport { ILanguageFeaturesService } from '../../../common/services/languageFeatures.js';\nimport { CancellationTokenSource } from '../../../../base/common/cancellation.js';\nimport { RunOnceScheduler } from '../../../../base/common/async.js';\nimport { binarySearch } from '../../../../base/common/arrays.js';\nimport { Emitter } from '../../../../base/common/event.js';\nimport { ILanguageConfigurationService } from '../../../common/languages/languageConfigurationRegistry.js';\nimport { StickyModelProvider } from './stickyScrollModelProvider.js';\nexport class StickyLineCandidate {\n  constructor(startLineNumber, endLineNumber, nestingDepth) {\n    this.startLineNumber = startLineNumber;\n    this.endLineNumber = endLineNumber;\n    this.nestingDepth = nestingDepth;\n  }\n}\nlet StickyLineCandidateProvider = class StickyLineCandidateProvider extends Disposable {\n  constructor(editor, _languageFeaturesService, _languageConfigurationService) {\n    super();\n    this._languageFeaturesService = _languageFeaturesService;\n    this._languageConfigurationService = _languageConfigurationService;\n    this._onDidChangeStickyScroll = this._register(new Emitter());\n    this.onDidChangeStickyScroll = this._onDidChangeStickyScroll.event;\n    this._model = null;\n    this._cts = null;\n    this._stickyModelProvider = null;\n    this._editor = editor;\n    this._sessionStore = this._register(new DisposableStore());\n    this._updateSoon = this._register(new RunOnceScheduler(() => this.update(), 50));\n    this._register(this._editor.onDidChangeConfiguration(e => {\n      if (e.hasChanged(116 /* EditorOption.stickyScroll */)) {\n        this.readConfiguration();\n      }\n    }));\n    this.readConfiguration();\n  }\n  readConfiguration() {\n    this._sessionStore.clear();\n    const options = this._editor.getOption(116 /* EditorOption.stickyScroll */);\n    if (!options.enabled) {\n      return;\n    }\n    this._sessionStore.add(this._editor.onDidChangeModel(() => {\n      // We should not show an old model for a different file, it will always be wrong.\n      // So we clear the model here immediately and then trigger an update.\n      this._model = null;\n      this.updateStickyModelProvider();\n      this._onDidChangeStickyScroll.fire();\n      this.update();\n    }));\n    this._sessionStore.add(this._editor.onDidChangeHiddenAreas(() => this.update()));\n    this._sessionStore.add(this._editor.onDidChangeModelContent(() => this._updateSoon.schedule()));\n    this._sessionStore.add(this._languageFeaturesService.documentSymbolProvider.onDidChange(() => this.update()));\n    this._sessionStore.add(toDisposable(() => {\n      this._stickyModelProvider?.dispose();\n      this._stickyModelProvider = null;\n    }));\n    this.updateStickyModelProvider();\n    this.update();\n  }\n  getVersionId() {\n    return this._model?.version;\n  }\n  updateStickyModelProvider() {\n    this._stickyModelProvider?.dispose();\n    this._stickyModelProvider = null;\n    const editor = this._editor;\n    if (editor.hasModel()) {\n      this._stickyModelProvider = new StickyModelProvider(editor, () => this._updateSoon.schedule(), this._languageConfigurationService, this._languageFeaturesService);\n    }\n  }\n  update() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this._cts?.dispose(true);\n      _this._cts = new CancellationTokenSource();\n      yield _this.updateStickyModel(_this._cts.token);\n      _this._onDidChangeStickyScroll.fire();\n    })();\n  }\n  updateStickyModel(token) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this2._editor.hasModel() || !_this2._stickyModelProvider || _this2._editor.getModel().isTooLargeForTokenization()) {\n        _this2._model = null;\n        return;\n      }\n      const model = yield _this2._stickyModelProvider.update(token);\n      if (token.isCancellationRequested) {\n        // the computation was canceled, so do not overwrite the model\n        return;\n      }\n      _this2._model = model;\n    })();\n  }\n  updateIndex(index) {\n    if (index === -1) {\n      index = 0;\n    } else if (index < 0) {\n      index = -index - 2;\n    }\n    return index;\n  }\n  getCandidateStickyLinesIntersectingFromStickyModel(range, outlineModel, result, depth, lastStartLineNumber) {\n    if (outlineModel.children.length === 0) {\n      return;\n    }\n    let lastLine = lastStartLineNumber;\n    const childrenStartLines = [];\n    for (let i = 0; i < outlineModel.children.length; i++) {\n      const child = outlineModel.children[i];\n      if (child.range) {\n        childrenStartLines.push(child.range.startLineNumber);\n      }\n    }\n    const lowerBound = this.updateIndex(binarySearch(childrenStartLines, range.startLineNumber, (a, b) => {\n      return a - b;\n    }));\n    const upperBound = this.updateIndex(binarySearch(childrenStartLines, range.startLineNumber + depth, (a, b) => {\n      return a - b;\n    }));\n    for (let i = lowerBound; i <= upperBound; i++) {\n      const child = outlineModel.children[i];\n      if (!child) {\n        return;\n      }\n      if (child.range) {\n        const childStartLine = child.range.startLineNumber;\n        const childEndLine = child.range.endLineNumber;\n        if (range.startLineNumber <= childEndLine + 1 && childStartLine - 1 <= range.endLineNumber && childStartLine !== lastLine) {\n          lastLine = childStartLine;\n          result.push(new StickyLineCandidate(childStartLine, childEndLine - 1, depth + 1));\n          this.getCandidateStickyLinesIntersectingFromStickyModel(range, child, result, depth + 1, childStartLine);\n        }\n      } else {\n        this.getCandidateStickyLinesIntersectingFromStickyModel(range, child, result, depth, lastStartLineNumber);\n      }\n    }\n  }\n  getCandidateStickyLinesIntersecting(range) {\n    if (!this._model?.element) {\n      return [];\n    }\n    let stickyLineCandidates = [];\n    this.getCandidateStickyLinesIntersectingFromStickyModel(range, this._model.element, stickyLineCandidates, 0, -1);\n    const hiddenRanges = this._editor._getViewModel()?.getHiddenAreas();\n    if (hiddenRanges) {\n      for (const hiddenRange of hiddenRanges) {\n        stickyLineCandidates = stickyLineCandidates.filter(stickyLine => !(stickyLine.startLineNumber >= hiddenRange.startLineNumber && stickyLine.endLineNumber <= hiddenRange.endLineNumber + 1));\n      }\n    }\n    return stickyLineCandidates;\n  }\n};\nStickyLineCandidateProvider = __decorate([__param(1, ILanguageFeaturesService), __param(2, ILanguageConfigurationService)], StickyLineCandidateProvider);\nexport { StickyLineCandidateProvider };", "map": {"version": 3, "names": ["__decorate", "decorators", "target", "key", "desc", "c", "arguments", "length", "r", "Object", "getOwnPropertyDescriptor", "d", "Reflect", "decorate", "i", "defineProperty", "__param", "paramIndex", "decorator", "Disposable", "DisposableStore", "toDisposable", "ILanguageFeaturesService", "CancellationTokenSource", "RunOnceScheduler", "binarySearch", "Emitter", "ILanguageConfigurationService", "StickyModelProvider", "StickyLineCandidate", "constructor", "startLineNumber", "endLineNumber", "<PERSON><PERSON><PERSON><PERSON>", "StickyLineCandidateProvider", "editor", "_languageFeaturesService", "_languageConfigurationService", "_onDidChangeStickyScroll", "_register", "onDidChangeStickyScroll", "event", "_model", "_cts", "_stickyModelProvider", "_editor", "_sessionStore", "_updateSoon", "update", "onDidChangeConfiguration", "e", "has<PERSON><PERSON>ed", "readConfiguration", "clear", "options", "getOption", "enabled", "add", "onDidChangeModel", "updateStickyModelProvider", "fire", "onDidChangeHiddenAreas", "onDidChangeModelContent", "schedule", "documentSymbolProvider", "onDidChange", "dispose", "getVersionId", "version", "hasModel", "_this", "_asyncToGenerator", "updateStickyModel", "token", "_this2", "getModel", "isTooLargeForTokenization", "model", "isCancellationRequested", "updateIndex", "index", "getCandidateStickyLinesIntersectingFromStickyModel", "range", "outlineModel", "result", "depth", "lastStartLineNumber", "children", "lastLine", "childrenStartLines", "child", "push", "lowerBound", "a", "b", "upperBound", "childStartLine", "childEndLine", "getCandidateStickyLinesIntersecting", "element", "stickyLineCandidates", "hiddenRanges", "_getViewModel", "getHiddenAreas", "hiddenRange", "filter", "stickyLine"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/contrib/stickyScroll/browser/stickyScrollProvider.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = (this && this.__param) || function (paramIndex, decorator) {\n    return function (target, key) { decorator(target, key, paramIndex); }\n};\nimport { Disposable, DisposableStore, toDisposable } from '../../../../base/common/lifecycle.js';\nimport { ILanguageFeaturesService } from '../../../common/services/languageFeatures.js';\nimport { CancellationTokenSource } from '../../../../base/common/cancellation.js';\nimport { RunOnceScheduler } from '../../../../base/common/async.js';\nimport { binarySearch } from '../../../../base/common/arrays.js';\nimport { Emitter } from '../../../../base/common/event.js';\nimport { ILanguageConfigurationService } from '../../../common/languages/languageConfigurationRegistry.js';\nimport { StickyModelProvider } from './stickyScrollModelProvider.js';\nexport class StickyLineCandidate {\n    constructor(startLineNumber, endLineNumber, nestingDepth) {\n        this.startLineNumber = startLineNumber;\n        this.endLineNumber = endLineNumber;\n        this.nestingDepth = nestingDepth;\n    }\n}\nlet StickyLineCandidateProvider = class StickyLineCandidateProvider extends Disposable {\n    constructor(editor, _languageFeaturesService, _languageConfigurationService) {\n        super();\n        this._languageFeaturesService = _languageFeaturesService;\n        this._languageConfigurationService = _languageConfigurationService;\n        this._onDidChangeStickyScroll = this._register(new Emitter());\n        this.onDidChangeStickyScroll = this._onDidChangeStickyScroll.event;\n        this._model = null;\n        this._cts = null;\n        this._stickyModelProvider = null;\n        this._editor = editor;\n        this._sessionStore = this._register(new DisposableStore());\n        this._updateSoon = this._register(new RunOnceScheduler(() => this.update(), 50));\n        this._register(this._editor.onDidChangeConfiguration(e => {\n            if (e.hasChanged(116 /* EditorOption.stickyScroll */)) {\n                this.readConfiguration();\n            }\n        }));\n        this.readConfiguration();\n    }\n    readConfiguration() {\n        this._sessionStore.clear();\n        const options = this._editor.getOption(116 /* EditorOption.stickyScroll */);\n        if (!options.enabled) {\n            return;\n        }\n        this._sessionStore.add(this._editor.onDidChangeModel(() => {\n            // We should not show an old model for a different file, it will always be wrong.\n            // So we clear the model here immediately and then trigger an update.\n            this._model = null;\n            this.updateStickyModelProvider();\n            this._onDidChangeStickyScroll.fire();\n            this.update();\n        }));\n        this._sessionStore.add(this._editor.onDidChangeHiddenAreas(() => this.update()));\n        this._sessionStore.add(this._editor.onDidChangeModelContent(() => this._updateSoon.schedule()));\n        this._sessionStore.add(this._languageFeaturesService.documentSymbolProvider.onDidChange(() => this.update()));\n        this._sessionStore.add(toDisposable(() => {\n            this._stickyModelProvider?.dispose();\n            this._stickyModelProvider = null;\n        }));\n        this.updateStickyModelProvider();\n        this.update();\n    }\n    getVersionId() {\n        return this._model?.version;\n    }\n    updateStickyModelProvider() {\n        this._stickyModelProvider?.dispose();\n        this._stickyModelProvider = null;\n        const editor = this._editor;\n        if (editor.hasModel()) {\n            this._stickyModelProvider = new StickyModelProvider(editor, () => this._updateSoon.schedule(), this._languageConfigurationService, this._languageFeaturesService);\n        }\n    }\n    async update() {\n        this._cts?.dispose(true);\n        this._cts = new CancellationTokenSource();\n        await this.updateStickyModel(this._cts.token);\n        this._onDidChangeStickyScroll.fire();\n    }\n    async updateStickyModel(token) {\n        if (!this._editor.hasModel() || !this._stickyModelProvider || this._editor.getModel().isTooLargeForTokenization()) {\n            this._model = null;\n            return;\n        }\n        const model = await this._stickyModelProvider.update(token);\n        if (token.isCancellationRequested) {\n            // the computation was canceled, so do not overwrite the model\n            return;\n        }\n        this._model = model;\n    }\n    updateIndex(index) {\n        if (index === -1) {\n            index = 0;\n        }\n        else if (index < 0) {\n            index = -index - 2;\n        }\n        return index;\n    }\n    getCandidateStickyLinesIntersectingFromStickyModel(range, outlineModel, result, depth, lastStartLineNumber) {\n        if (outlineModel.children.length === 0) {\n            return;\n        }\n        let lastLine = lastStartLineNumber;\n        const childrenStartLines = [];\n        for (let i = 0; i < outlineModel.children.length; i++) {\n            const child = outlineModel.children[i];\n            if (child.range) {\n                childrenStartLines.push(child.range.startLineNumber);\n            }\n        }\n        const lowerBound = this.updateIndex(binarySearch(childrenStartLines, range.startLineNumber, (a, b) => { return a - b; }));\n        const upperBound = this.updateIndex(binarySearch(childrenStartLines, range.startLineNumber + depth, (a, b) => { return a - b; }));\n        for (let i = lowerBound; i <= upperBound; i++) {\n            const child = outlineModel.children[i];\n            if (!child) {\n                return;\n            }\n            if (child.range) {\n                const childStartLine = child.range.startLineNumber;\n                const childEndLine = child.range.endLineNumber;\n                if (range.startLineNumber <= childEndLine + 1 && childStartLine - 1 <= range.endLineNumber && childStartLine !== lastLine) {\n                    lastLine = childStartLine;\n                    result.push(new StickyLineCandidate(childStartLine, childEndLine - 1, depth + 1));\n                    this.getCandidateStickyLinesIntersectingFromStickyModel(range, child, result, depth + 1, childStartLine);\n                }\n            }\n            else {\n                this.getCandidateStickyLinesIntersectingFromStickyModel(range, child, result, depth, lastStartLineNumber);\n            }\n        }\n    }\n    getCandidateStickyLinesIntersecting(range) {\n        if (!this._model?.element) {\n            return [];\n        }\n        let stickyLineCandidates = [];\n        this.getCandidateStickyLinesIntersectingFromStickyModel(range, this._model.element, stickyLineCandidates, 0, -1);\n        const hiddenRanges = this._editor._getViewModel()?.getHiddenAreas();\n        if (hiddenRanges) {\n            for (const hiddenRange of hiddenRanges) {\n                stickyLineCandidates = stickyLineCandidates.filter(stickyLine => !(stickyLine.startLineNumber >= hiddenRange.startLineNumber && stickyLine.endLineNumber <= hiddenRange.endLineNumber + 1));\n            }\n        }\n        return stickyLineCandidates;\n    }\n};\nStickyLineCandidateProvider = __decorate([\n    __param(1, ILanguageFeaturesService),\n    __param(2, ILanguageConfigurationService)\n], StickyLineCandidateProvider);\nexport { StickyLineCandidateProvider };\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA,IAAIA,UAAU,GAAI,IAAI,IAAI,IAAI,CAACA,UAAU,IAAK,UAAUC,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAE;EACnF,IAAIC,CAAC,GAAGC,SAAS,CAACC,MAAM;IAAEC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGH,MAAM,GAAGE,IAAI,KAAK,IAAI,GAAGA,IAAI,GAAGK,MAAM,CAACC,wBAAwB,CAACR,MAAM,EAAEC,GAAG,CAAC,GAAGC,IAAI;IAAEO,CAAC;EAC5H,IAAI,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,CAACC,QAAQ,KAAK,UAAU,EAAEL,CAAC,GAAGI,OAAO,CAACC,QAAQ,CAACZ,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,CAAC,CAAC,KAC1H,KAAK,IAAIU,CAAC,GAAGb,UAAU,CAACM,MAAM,GAAG,CAAC,EAAEO,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE,IAAIH,CAAC,GAAGV,UAAU,CAACa,CAAC,CAAC,EAAEN,CAAC,GAAG,CAACH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACH,CAAC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACT,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,GAAGG,CAAC,CAACT,MAAM,EAAEC,GAAG,CAAC,KAAKK,CAAC;EACjJ,OAAOH,CAAC,GAAG,CAAC,IAAIG,CAAC,IAAIC,MAAM,CAACM,cAAc,CAACb,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,EAAEA,CAAC;AACjE,CAAC;AACD,IAAIQ,OAAO,GAAI,IAAI,IAAI,IAAI,CAACA,OAAO,IAAK,UAAUC,UAAU,EAAEC,SAAS,EAAE;EACrE,OAAO,UAAUhB,MAAM,EAAEC,GAAG,EAAE;IAAEe,SAAS,CAAChB,MAAM,EAAEC,GAAG,EAAEc,UAAU,CAAC;EAAE,CAAC;AACzE,CAAC;AACD,SAASE,UAAU,EAAEC,eAAe,EAAEC,YAAY,QAAQ,sCAAsC;AAChG,SAASC,wBAAwB,QAAQ,8CAA8C;AACvF,SAASC,uBAAuB,QAAQ,yCAAyC;AACjF,SAASC,gBAAgB,QAAQ,kCAAkC;AACnE,SAASC,YAAY,QAAQ,mCAAmC;AAChE,SAASC,OAAO,QAAQ,kCAAkC;AAC1D,SAASC,6BAA6B,QAAQ,4DAA4D;AAC1G,SAASC,mBAAmB,QAAQ,gCAAgC;AACpE,OAAO,MAAMC,mBAAmB,CAAC;EAC7BC,WAAWA,CAACC,eAAe,EAAEC,aAAa,EAAEC,YAAY,EAAE;IACtD,IAAI,CAACF,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,YAAY,GAAGA,YAAY;EACpC;AACJ;AACA,IAAIC,2BAA2B,GAAG,MAAMA,2BAA2B,SAASf,UAAU,CAAC;EACnFW,WAAWA,CAACK,MAAM,EAAEC,wBAAwB,EAAEC,6BAA6B,EAAE;IACzE,KAAK,CAAC,CAAC;IACP,IAAI,CAACD,wBAAwB,GAAGA,wBAAwB;IACxD,IAAI,CAACC,6BAA6B,GAAGA,6BAA6B;IAClE,IAAI,CAACC,wBAAwB,GAAG,IAAI,CAACC,SAAS,CAAC,IAAIb,OAAO,CAAC,CAAC,CAAC;IAC7D,IAAI,CAACc,uBAAuB,GAAG,IAAI,CAACF,wBAAwB,CAACG,KAAK;IAClE,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,IAAI,GAAG,IAAI;IAChB,IAAI,CAACC,oBAAoB,GAAG,IAAI;IAChC,IAAI,CAACC,OAAO,GAAGV,MAAM;IACrB,IAAI,CAACW,aAAa,GAAG,IAAI,CAACP,SAAS,CAAC,IAAInB,eAAe,CAAC,CAAC,CAAC;IAC1D,IAAI,CAAC2B,WAAW,GAAG,IAAI,CAACR,SAAS,CAAC,IAAIf,gBAAgB,CAAC,MAAM,IAAI,CAACwB,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IAChF,IAAI,CAACT,SAAS,CAAC,IAAI,CAACM,OAAO,CAACI,wBAAwB,CAACC,CAAC,IAAI;MACtD,IAAIA,CAAC,CAACC,UAAU,CAAC,GAAG,CAAC,+BAA+B,CAAC,EAAE;QACnD,IAAI,CAACC,iBAAiB,CAAC,CAAC;MAC5B;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,CAACA,iBAAiB,CAAC,CAAC;EAC5B;EACAA,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACN,aAAa,CAACO,KAAK,CAAC,CAAC;IAC1B,MAAMC,OAAO,GAAG,IAAI,CAACT,OAAO,CAACU,SAAS,CAAC,GAAG,CAAC,+BAA+B,CAAC;IAC3E,IAAI,CAACD,OAAO,CAACE,OAAO,EAAE;MAClB;IACJ;IACA,IAAI,CAACV,aAAa,CAACW,GAAG,CAAC,IAAI,CAACZ,OAAO,CAACa,gBAAgB,CAAC,MAAM;MACvD;MACA;MACA,IAAI,CAAChB,MAAM,GAAG,IAAI;MAClB,IAAI,CAACiB,yBAAyB,CAAC,CAAC;MAChC,IAAI,CAACrB,wBAAwB,CAACsB,IAAI,CAAC,CAAC;MACpC,IAAI,CAACZ,MAAM,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC;IACH,IAAI,CAACF,aAAa,CAACW,GAAG,CAAC,IAAI,CAACZ,OAAO,CAACgB,sBAAsB,CAAC,MAAM,IAAI,CAACb,MAAM,CAAC,CAAC,CAAC,CAAC;IAChF,IAAI,CAACF,aAAa,CAACW,GAAG,CAAC,IAAI,CAACZ,OAAO,CAACiB,uBAAuB,CAAC,MAAM,IAAI,CAACf,WAAW,CAACgB,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC/F,IAAI,CAACjB,aAAa,CAACW,GAAG,CAAC,IAAI,CAACrB,wBAAwB,CAAC4B,sBAAsB,CAACC,WAAW,CAAC,MAAM,IAAI,CAACjB,MAAM,CAAC,CAAC,CAAC,CAAC;IAC7G,IAAI,CAACF,aAAa,CAACW,GAAG,CAACpC,YAAY,CAAC,MAAM;MACtC,IAAI,CAACuB,oBAAoB,EAAEsB,OAAO,CAAC,CAAC;MACpC,IAAI,CAACtB,oBAAoB,GAAG,IAAI;IACpC,CAAC,CAAC,CAAC;IACH,IAAI,CAACe,yBAAyB,CAAC,CAAC;IAChC,IAAI,CAACX,MAAM,CAAC,CAAC;EACjB;EACAmB,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAACzB,MAAM,EAAE0B,OAAO;EAC/B;EACAT,yBAAyBA,CAAA,EAAG;IACxB,IAAI,CAACf,oBAAoB,EAAEsB,OAAO,CAAC,CAAC;IACpC,IAAI,CAACtB,oBAAoB,GAAG,IAAI;IAChC,MAAMT,MAAM,GAAG,IAAI,CAACU,OAAO;IAC3B,IAAIV,MAAM,CAACkC,QAAQ,CAAC,CAAC,EAAE;MACnB,IAAI,CAACzB,oBAAoB,GAAG,IAAIhB,mBAAmB,CAACO,MAAM,EAAE,MAAM,IAAI,CAACY,WAAW,CAACgB,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC1B,6BAA6B,EAAE,IAAI,CAACD,wBAAwB,CAAC;IACrK;EACJ;EACMY,MAAMA,CAAA,EAAG;IAAA,IAAAsB,KAAA;IAAA,OAAAC,iBAAA;MACXD,KAAI,CAAC3B,IAAI,EAAEuB,OAAO,CAAC,IAAI,CAAC;MACxBI,KAAI,CAAC3B,IAAI,GAAG,IAAIpB,uBAAuB,CAAC,CAAC;MACzC,MAAM+C,KAAI,CAACE,iBAAiB,CAACF,KAAI,CAAC3B,IAAI,CAAC8B,KAAK,CAAC;MAC7CH,KAAI,CAAChC,wBAAwB,CAACsB,IAAI,CAAC,CAAC;IAAC;EACzC;EACMY,iBAAiBA,CAACC,KAAK,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAAH,iBAAA;MAC3B,IAAI,CAACG,MAAI,CAAC7B,OAAO,CAACwB,QAAQ,CAAC,CAAC,IAAI,CAACK,MAAI,CAAC9B,oBAAoB,IAAI8B,MAAI,CAAC7B,OAAO,CAAC8B,QAAQ,CAAC,CAAC,CAACC,yBAAyB,CAAC,CAAC,EAAE;QAC/GF,MAAI,CAAChC,MAAM,GAAG,IAAI;QAClB;MACJ;MACA,MAAMmC,KAAK,SAASH,MAAI,CAAC9B,oBAAoB,CAACI,MAAM,CAACyB,KAAK,CAAC;MAC3D,IAAIA,KAAK,CAACK,uBAAuB,EAAE;QAC/B;QACA;MACJ;MACAJ,MAAI,CAAChC,MAAM,GAAGmC,KAAK;IAAC;EACxB;EACAE,WAAWA,CAACC,KAAK,EAAE;IACf,IAAIA,KAAK,KAAK,CAAC,CAAC,EAAE;MACdA,KAAK,GAAG,CAAC;IACb,CAAC,MACI,IAAIA,KAAK,GAAG,CAAC,EAAE;MAChBA,KAAK,GAAG,CAACA,KAAK,GAAG,CAAC;IACtB;IACA,OAAOA,KAAK;EAChB;EACAC,kDAAkDA,CAACC,KAAK,EAAEC,YAAY,EAAEC,MAAM,EAAEC,KAAK,EAAEC,mBAAmB,EAAE;IACxG,IAAIH,YAAY,CAACI,QAAQ,CAAChF,MAAM,KAAK,CAAC,EAAE;MACpC;IACJ;IACA,IAAIiF,QAAQ,GAAGF,mBAAmB;IAClC,MAAMG,kBAAkB,GAAG,EAAE;IAC7B,KAAK,IAAI3E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqE,YAAY,CAACI,QAAQ,CAAChF,MAAM,EAAEO,CAAC,EAAE,EAAE;MACnD,MAAM4E,KAAK,GAAGP,YAAY,CAACI,QAAQ,CAACzE,CAAC,CAAC;MACtC,IAAI4E,KAAK,CAACR,KAAK,EAAE;QACbO,kBAAkB,CAACE,IAAI,CAACD,KAAK,CAACR,KAAK,CAACnD,eAAe,CAAC;MACxD;IACJ;IACA,MAAM6D,UAAU,GAAG,IAAI,CAACb,WAAW,CAACtD,YAAY,CAACgE,kBAAkB,EAAEP,KAAK,CAACnD,eAAe,EAAE,CAAC8D,CAAC,EAAEC,CAAC,KAAK;MAAE,OAAOD,CAAC,GAAGC,CAAC;IAAE,CAAC,CAAC,CAAC;IACzH,MAAMC,UAAU,GAAG,IAAI,CAAChB,WAAW,CAACtD,YAAY,CAACgE,kBAAkB,EAAEP,KAAK,CAACnD,eAAe,GAAGsD,KAAK,EAAE,CAACQ,CAAC,EAAEC,CAAC,KAAK;MAAE,OAAOD,CAAC,GAAGC,CAAC;IAAE,CAAC,CAAC,CAAC;IACjI,KAAK,IAAIhF,CAAC,GAAG8E,UAAU,EAAE9E,CAAC,IAAIiF,UAAU,EAAEjF,CAAC,EAAE,EAAE;MAC3C,MAAM4E,KAAK,GAAGP,YAAY,CAACI,QAAQ,CAACzE,CAAC,CAAC;MACtC,IAAI,CAAC4E,KAAK,EAAE;QACR;MACJ;MACA,IAAIA,KAAK,CAACR,KAAK,EAAE;QACb,MAAMc,cAAc,GAAGN,KAAK,CAACR,KAAK,CAACnD,eAAe;QAClD,MAAMkE,YAAY,GAAGP,KAAK,CAACR,KAAK,CAAClD,aAAa;QAC9C,IAAIkD,KAAK,CAACnD,eAAe,IAAIkE,YAAY,GAAG,CAAC,IAAID,cAAc,GAAG,CAAC,IAAId,KAAK,CAAClD,aAAa,IAAIgE,cAAc,KAAKR,QAAQ,EAAE;UACvHA,QAAQ,GAAGQ,cAAc;UACzBZ,MAAM,CAACO,IAAI,CAAC,IAAI9D,mBAAmB,CAACmE,cAAc,EAAEC,YAAY,GAAG,CAAC,EAAEZ,KAAK,GAAG,CAAC,CAAC,CAAC;UACjF,IAAI,CAACJ,kDAAkD,CAACC,KAAK,EAAEQ,KAAK,EAAEN,MAAM,EAAEC,KAAK,GAAG,CAAC,EAAEW,cAAc,CAAC;QAC5G;MACJ,CAAC,MACI;QACD,IAAI,CAACf,kDAAkD,CAACC,KAAK,EAAEQ,KAAK,EAAEN,MAAM,EAAEC,KAAK,EAAEC,mBAAmB,CAAC;MAC7G;IACJ;EACJ;EACAY,mCAAmCA,CAAChB,KAAK,EAAE;IACvC,IAAI,CAAC,IAAI,CAACxC,MAAM,EAAEyD,OAAO,EAAE;MACvB,OAAO,EAAE;IACb;IACA,IAAIC,oBAAoB,GAAG,EAAE;IAC7B,IAAI,CAACnB,kDAAkD,CAACC,KAAK,EAAE,IAAI,CAACxC,MAAM,CAACyD,OAAO,EAAEC,oBAAoB,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAChH,MAAMC,YAAY,GAAG,IAAI,CAACxD,OAAO,CAACyD,aAAa,CAAC,CAAC,EAAEC,cAAc,CAAC,CAAC;IACnE,IAAIF,YAAY,EAAE;MACd,KAAK,MAAMG,WAAW,IAAIH,YAAY,EAAE;QACpCD,oBAAoB,GAAGA,oBAAoB,CAACK,MAAM,CAACC,UAAU,IAAI,EAAEA,UAAU,CAAC3E,eAAe,IAAIyE,WAAW,CAACzE,eAAe,IAAI2E,UAAU,CAAC1E,aAAa,IAAIwE,WAAW,CAACxE,aAAa,GAAG,CAAC,CAAC,CAAC;MAC/L;IACJ;IACA,OAAOoE,oBAAoB;EAC/B;AACJ,CAAC;AACDlE,2BAA2B,GAAGlC,UAAU,CAAC,CACrCgB,OAAO,CAAC,CAAC,EAAEM,wBAAwB,CAAC,EACpCN,OAAO,CAAC,CAAC,EAAEW,6BAA6B,CAAC,CAC5C,EAAEO,2BAA2B,CAAC;AAC/B,SAASA,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}