{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport class StickyRange {\n  constructor(startLineNumber, endLineNumber) {\n    this.startLineNumber = startLineNumber;\n    this.endLineNumber = endLineNumber;\n  }\n}\nexport class StickyElement {\n  constructor(\n  /**\n   * Range of line numbers spanned by the current scope\n   */\n  range,\n  /**\n   * Must be sorted by start line number\n  */\n  children,\n  /**\n   * Parent sticky outline element\n   */\n  parent) {\n    this.range = range;\n    this.children = children;\n    this.parent = parent;\n  }\n}\nexport class StickyModel {\n  constructor(uri, version, element, outlineProviderId) {\n    this.uri = uri;\n    this.version = version;\n    this.element = element;\n    this.outlineProviderId = outlineProviderId;\n  }\n}", "map": {"version": 3, "names": ["StickyRange", "constructor", "startLineNumber", "endLineNumber", "StickyElement", "range", "children", "parent", "StickyModel", "uri", "version", "element", "outlineProviderId"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/contrib/stickyScroll/browser/stickyScrollElement.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport class StickyRange {\n    constructor(startLineNumber, endLineNumber) {\n        this.startLineNumber = startLineNumber;\n        this.endLineNumber = endLineNumber;\n    }\n}\nexport class StickyElement {\n    constructor(\n    /**\n     * Range of line numbers spanned by the current scope\n     */\n    range, \n    /**\n     * Must be sorted by start line number\n    */\n    children, \n    /**\n     * Parent sticky outline element\n     */\n    parent) {\n        this.range = range;\n        this.children = children;\n        this.parent = parent;\n    }\n}\nexport class StickyModel {\n    constructor(uri, version, element, outlineProviderId) {\n        this.uri = uri;\n        this.version = version;\n        this.element = element;\n        this.outlineProviderId = outlineProviderId;\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,MAAMA,WAAW,CAAC;EACrBC,WAAWA,CAACC,eAAe,EAAEC,aAAa,EAAE;IACxC,IAAI,CAACD,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,aAAa,GAAGA,aAAa;EACtC;AACJ;AACA,OAAO,MAAMC,aAAa,CAAC;EACvBH,WAAWA;EACX;AACJ;AACA;EACII,KAAK;EACL;AACJ;AACA;EACIC,QAAQ;EACR;AACJ;AACA;EACIC,MAAM,EAAE;IACJ,IAAI,CAACF,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,MAAM,GAAGA,MAAM;EACxB;AACJ;AACA,OAAO,MAAMC,WAAW,CAAC;EACrBP,WAAWA,CAACQ,GAAG,EAAEC,OAAO,EAAEC,OAAO,EAAEC,iBAAiB,EAAE;IAClD,IAAI,CAACH,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;EAC9C;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}