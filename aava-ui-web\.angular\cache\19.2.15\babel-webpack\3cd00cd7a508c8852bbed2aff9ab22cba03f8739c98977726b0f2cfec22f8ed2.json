{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as arrays from '../../../base/common/arrays.js';\nimport { Position } from '../core/position.js';\nimport { ContiguousTokensEditing, EMPTY_LINE_TOKENS, toUint32Array } from './contiguousTokensEditing.js';\nimport { LineTokens } from './lineTokens.js';\nimport { TokenMetadata } from '../encodedTokenAttributes.js';\n/**\n * Represents contiguous tokens in a text model.\n */\nexport class ContiguousTokensStore {\n  constructor(languageIdCodec) {\n    this._lineTokens = [];\n    this._len = 0;\n    this._languageIdCodec = languageIdCodec;\n  }\n  flush() {\n    this._lineTokens = [];\n    this._len = 0;\n  }\n  get hasTokens() {\n    return this._lineTokens.length > 0;\n  }\n  getTokens(topLevelLanguageId, lineIndex, lineText) {\n    let rawLineTokens = null;\n    if (lineIndex < this._len) {\n      rawLineTokens = this._lineTokens[lineIndex];\n    }\n    if (rawLineTokens !== null && rawLineTokens !== EMPTY_LINE_TOKENS) {\n      return new LineTokens(toUint32Array(rawLineTokens), lineText, this._languageIdCodec);\n    }\n    const lineTokens = new Uint32Array(2);\n    lineTokens[0] = lineText.length;\n    lineTokens[1] = getDefaultMetadata(this._languageIdCodec.encodeLanguageId(topLevelLanguageId));\n    return new LineTokens(lineTokens, lineText, this._languageIdCodec);\n  }\n  static _massageTokens(topLevelLanguageId, lineTextLength, _tokens) {\n    const tokens = _tokens ? toUint32Array(_tokens) : null;\n    if (lineTextLength === 0) {\n      let hasDifferentLanguageId = false;\n      if (tokens && tokens.length > 1) {\n        hasDifferentLanguageId = TokenMetadata.getLanguageId(tokens[1]) !== topLevelLanguageId;\n      }\n      if (!hasDifferentLanguageId) {\n        return EMPTY_LINE_TOKENS;\n      }\n    }\n    if (!tokens || tokens.length === 0) {\n      const tokens = new Uint32Array(2);\n      tokens[0] = lineTextLength;\n      tokens[1] = getDefaultMetadata(topLevelLanguageId);\n      return tokens.buffer;\n    }\n    // Ensure the last token covers the end of the text\n    tokens[tokens.length - 2] = lineTextLength;\n    if (tokens.byteOffset === 0 && tokens.byteLength === tokens.buffer.byteLength) {\n      // Store directly the ArrayBuffer pointer to save an object\n      return tokens.buffer;\n    }\n    return tokens;\n  }\n  _ensureLine(lineIndex) {\n    while (lineIndex >= this._len) {\n      this._lineTokens[this._len] = null;\n      this._len++;\n    }\n  }\n  _deleteLines(start, deleteCount) {\n    if (deleteCount === 0) {\n      return;\n    }\n    if (start + deleteCount > this._len) {\n      deleteCount = this._len - start;\n    }\n    this._lineTokens.splice(start, deleteCount);\n    this._len -= deleteCount;\n  }\n  _insertLines(insertIndex, insertCount) {\n    if (insertCount === 0) {\n      return;\n    }\n    const lineTokens = [];\n    for (let i = 0; i < insertCount; i++) {\n      lineTokens[i] = null;\n    }\n    this._lineTokens = arrays.arrayInsert(this._lineTokens, insertIndex, lineTokens);\n    this._len += insertCount;\n  }\n  setTokens(topLevelLanguageId, lineIndex, lineTextLength, _tokens, checkEquality) {\n    const tokens = ContiguousTokensStore._massageTokens(this._languageIdCodec.encodeLanguageId(topLevelLanguageId), lineTextLength, _tokens);\n    this._ensureLine(lineIndex);\n    const oldTokens = this._lineTokens[lineIndex];\n    this._lineTokens[lineIndex] = tokens;\n    if (checkEquality) {\n      return !ContiguousTokensStore._equals(oldTokens, tokens);\n    }\n    return false;\n  }\n  static _equals(_a, _b) {\n    if (!_a || !_b) {\n      return !_a && !_b;\n    }\n    const a = toUint32Array(_a);\n    const b = toUint32Array(_b);\n    if (a.length !== b.length) {\n      return false;\n    }\n    for (let i = 0, len = a.length; i < len; i++) {\n      if (a[i] !== b[i]) {\n        return false;\n      }\n    }\n    return true;\n  }\n  //#region Editing\n  acceptEdit(range, eolCount, firstLineLength) {\n    this._acceptDeleteRange(range);\n    this._acceptInsertText(new Position(range.startLineNumber, range.startColumn), eolCount, firstLineLength);\n  }\n  _acceptDeleteRange(range) {\n    const firstLineIndex = range.startLineNumber - 1;\n    if (firstLineIndex >= this._len) {\n      return;\n    }\n    if (range.startLineNumber === range.endLineNumber) {\n      if (range.startColumn === range.endColumn) {\n        // Nothing to delete\n        return;\n      }\n      this._lineTokens[firstLineIndex] = ContiguousTokensEditing.delete(this._lineTokens[firstLineIndex], range.startColumn - 1, range.endColumn - 1);\n      return;\n    }\n    this._lineTokens[firstLineIndex] = ContiguousTokensEditing.deleteEnding(this._lineTokens[firstLineIndex], range.startColumn - 1);\n    const lastLineIndex = range.endLineNumber - 1;\n    let lastLineTokens = null;\n    if (lastLineIndex < this._len) {\n      lastLineTokens = ContiguousTokensEditing.deleteBeginning(this._lineTokens[lastLineIndex], range.endColumn - 1);\n    }\n    // Take remaining text on last line and append it to remaining text on first line\n    this._lineTokens[firstLineIndex] = ContiguousTokensEditing.append(this._lineTokens[firstLineIndex], lastLineTokens);\n    // Delete middle lines\n    this._deleteLines(range.startLineNumber, range.endLineNumber - range.startLineNumber);\n  }\n  _acceptInsertText(position, eolCount, firstLineLength) {\n    if (eolCount === 0 && firstLineLength === 0) {\n      // Nothing to insert\n      return;\n    }\n    const lineIndex = position.lineNumber - 1;\n    if (lineIndex >= this._len) {\n      return;\n    }\n    if (eolCount === 0) {\n      // Inserting text on one line\n      this._lineTokens[lineIndex] = ContiguousTokensEditing.insert(this._lineTokens[lineIndex], position.column - 1, firstLineLength);\n      return;\n    }\n    this._lineTokens[lineIndex] = ContiguousTokensEditing.deleteEnding(this._lineTokens[lineIndex], position.column - 1);\n    this._lineTokens[lineIndex] = ContiguousTokensEditing.insert(this._lineTokens[lineIndex], position.column - 1, firstLineLength);\n    this._insertLines(position.lineNumber, eolCount);\n  }\n  //#endregion\n  setMultilineTokens(tokens, textModel) {\n    if (tokens.length === 0) {\n      return {\n        changes: []\n      };\n    }\n    const ranges = [];\n    for (let i = 0, len = tokens.length; i < len; i++) {\n      const element = tokens[i];\n      let minChangedLineNumber = 0;\n      let maxChangedLineNumber = 0;\n      let hasChange = false;\n      for (let lineNumber = element.startLineNumber; lineNumber <= element.endLineNumber; lineNumber++) {\n        if (hasChange) {\n          this.setTokens(textModel.getLanguageId(), lineNumber - 1, textModel.getLineLength(lineNumber), element.getLineTokens(lineNumber), false);\n          maxChangedLineNumber = lineNumber;\n        } else {\n          const lineHasChange = this.setTokens(textModel.getLanguageId(), lineNumber - 1, textModel.getLineLength(lineNumber), element.getLineTokens(lineNumber), true);\n          if (lineHasChange) {\n            hasChange = true;\n            minChangedLineNumber = lineNumber;\n            maxChangedLineNumber = lineNumber;\n          }\n        }\n      }\n      if (hasChange) {\n        ranges.push({\n          fromLineNumber: minChangedLineNumber,\n          toLineNumber: maxChangedLineNumber\n        });\n      }\n    }\n    return {\n      changes: ranges\n    };\n  }\n}\nfunction getDefaultMetadata(topLevelLanguageId) {\n  return (topLevelLanguageId << 0 /* MetadataConsts.LANGUAGEID_OFFSET */ | 0 /* StandardTokenType.Other */ << 8 /* MetadataConsts.TOKEN_TYPE_OFFSET */ | 0 /* FontStyle.None */ << 11 /* MetadataConsts.FONT_STYLE_OFFSET */ | 1 /* ColorId.DefaultForeground */ << 15 /* MetadataConsts.FOREGROUND_OFFSET */ | 2 /* ColorId.DefaultBackground */ << 24 /* MetadataConsts.BACKGROUND_OFFSET */\n  // If there is no grammar, we just take a guess and try to match brackets.\n  | 1024 /* MetadataConsts.BALANCED_BRACKETS_MASK */) >>> 0;\n}", "map": {"version": 3, "names": ["arrays", "Position", "ContiguousTokensEditing", "EMPTY_LINE_TOKENS", "toUint32Array", "LineTokens", "TokenMetadata", "ContiguousTokensStore", "constructor", "languageIdCodec", "_lineTokens", "_len", "_languageIdCodec", "flush", "hasTokens", "length", "getTokens", "topLevelLanguageId", "lineIndex", "lineText", "rawLineTokens", "lineTokens", "Uint32Array", "getDefaultMetadata", "encodeLanguageId", "_massageTokens", "lineTextLength", "_tokens", "tokens", "hasDifferentLanguageId", "getLanguageId", "buffer", "byteOffset", "byteLength", "_ensureLine", "_deleteLines", "start", "deleteCount", "splice", "_insertLines", "insertIndex", "insertCount", "i", "arrayInsert", "setTokens", "checkEquality", "oldTokens", "_equals", "_a", "_b", "a", "b", "len", "acceptEdit", "range", "eolCount", "firstLine<PERSON>ength", "_acceptDeleteRange", "_acceptInsertText", "startLineNumber", "startColumn", "firstLineIndex", "endLineNumber", "endColumn", "delete", "deleteEnding", "lastLineIndex", "lastLineTokens", "deleteBeginning", "append", "position", "lineNumber", "insert", "column", "setMultilineTokens", "textModel", "changes", "ranges", "element", "minChangedLineNumber", "maxChangedLineNumber", "hasChange", "getLine<PERSON><PERSON>th", "getLineTokens", "lineHasChange", "push", "fromLineNumber", "toLineNumber"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/tokens/contiguousTokensStore.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as arrays from '../../../base/common/arrays.js';\nimport { Position } from '../core/position.js';\nimport { ContiguousTokensEditing, EMPTY_LINE_TOKENS, toUint32Array } from './contiguousTokensEditing.js';\nimport { LineTokens } from './lineTokens.js';\nimport { TokenMetadata } from '../encodedTokenAttributes.js';\n/**\n * Represents contiguous tokens in a text model.\n */\nexport class ContiguousTokensStore {\n    constructor(languageIdCodec) {\n        this._lineTokens = [];\n        this._len = 0;\n        this._languageIdCodec = languageIdCodec;\n    }\n    flush() {\n        this._lineTokens = [];\n        this._len = 0;\n    }\n    get hasTokens() {\n        return this._lineTokens.length > 0;\n    }\n    getTokens(topLevelLanguageId, lineIndex, lineText) {\n        let rawLineTokens = null;\n        if (lineIndex < this._len) {\n            rawLineTokens = this._lineTokens[lineIndex];\n        }\n        if (rawLineTokens !== null && rawLineTokens !== EMPTY_LINE_TOKENS) {\n            return new LineTokens(toUint32Array(rawLineTokens), lineText, this._languageIdCodec);\n        }\n        const lineTokens = new Uint32Array(2);\n        lineTokens[0] = lineText.length;\n        lineTokens[1] = getDefaultMetadata(this._languageIdCodec.encodeLanguageId(topLevelLanguageId));\n        return new LineTokens(lineTokens, lineText, this._languageIdCodec);\n    }\n    static _massageTokens(topLevelLanguageId, lineTextLength, _tokens) {\n        const tokens = _tokens ? toUint32Array(_tokens) : null;\n        if (lineTextLength === 0) {\n            let hasDifferentLanguageId = false;\n            if (tokens && tokens.length > 1) {\n                hasDifferentLanguageId = (TokenMetadata.getLanguageId(tokens[1]) !== topLevelLanguageId);\n            }\n            if (!hasDifferentLanguageId) {\n                return EMPTY_LINE_TOKENS;\n            }\n        }\n        if (!tokens || tokens.length === 0) {\n            const tokens = new Uint32Array(2);\n            tokens[0] = lineTextLength;\n            tokens[1] = getDefaultMetadata(topLevelLanguageId);\n            return tokens.buffer;\n        }\n        // Ensure the last token covers the end of the text\n        tokens[tokens.length - 2] = lineTextLength;\n        if (tokens.byteOffset === 0 && tokens.byteLength === tokens.buffer.byteLength) {\n            // Store directly the ArrayBuffer pointer to save an object\n            return tokens.buffer;\n        }\n        return tokens;\n    }\n    _ensureLine(lineIndex) {\n        while (lineIndex >= this._len) {\n            this._lineTokens[this._len] = null;\n            this._len++;\n        }\n    }\n    _deleteLines(start, deleteCount) {\n        if (deleteCount === 0) {\n            return;\n        }\n        if (start + deleteCount > this._len) {\n            deleteCount = this._len - start;\n        }\n        this._lineTokens.splice(start, deleteCount);\n        this._len -= deleteCount;\n    }\n    _insertLines(insertIndex, insertCount) {\n        if (insertCount === 0) {\n            return;\n        }\n        const lineTokens = [];\n        for (let i = 0; i < insertCount; i++) {\n            lineTokens[i] = null;\n        }\n        this._lineTokens = arrays.arrayInsert(this._lineTokens, insertIndex, lineTokens);\n        this._len += insertCount;\n    }\n    setTokens(topLevelLanguageId, lineIndex, lineTextLength, _tokens, checkEquality) {\n        const tokens = ContiguousTokensStore._massageTokens(this._languageIdCodec.encodeLanguageId(topLevelLanguageId), lineTextLength, _tokens);\n        this._ensureLine(lineIndex);\n        const oldTokens = this._lineTokens[lineIndex];\n        this._lineTokens[lineIndex] = tokens;\n        if (checkEquality) {\n            return !ContiguousTokensStore._equals(oldTokens, tokens);\n        }\n        return false;\n    }\n    static _equals(_a, _b) {\n        if (!_a || !_b) {\n            return !_a && !_b;\n        }\n        const a = toUint32Array(_a);\n        const b = toUint32Array(_b);\n        if (a.length !== b.length) {\n            return false;\n        }\n        for (let i = 0, len = a.length; i < len; i++) {\n            if (a[i] !== b[i]) {\n                return false;\n            }\n        }\n        return true;\n    }\n    //#region Editing\n    acceptEdit(range, eolCount, firstLineLength) {\n        this._acceptDeleteRange(range);\n        this._acceptInsertText(new Position(range.startLineNumber, range.startColumn), eolCount, firstLineLength);\n    }\n    _acceptDeleteRange(range) {\n        const firstLineIndex = range.startLineNumber - 1;\n        if (firstLineIndex >= this._len) {\n            return;\n        }\n        if (range.startLineNumber === range.endLineNumber) {\n            if (range.startColumn === range.endColumn) {\n                // Nothing to delete\n                return;\n            }\n            this._lineTokens[firstLineIndex] = ContiguousTokensEditing.delete(this._lineTokens[firstLineIndex], range.startColumn - 1, range.endColumn - 1);\n            return;\n        }\n        this._lineTokens[firstLineIndex] = ContiguousTokensEditing.deleteEnding(this._lineTokens[firstLineIndex], range.startColumn - 1);\n        const lastLineIndex = range.endLineNumber - 1;\n        let lastLineTokens = null;\n        if (lastLineIndex < this._len) {\n            lastLineTokens = ContiguousTokensEditing.deleteBeginning(this._lineTokens[lastLineIndex], range.endColumn - 1);\n        }\n        // Take remaining text on last line and append it to remaining text on first line\n        this._lineTokens[firstLineIndex] = ContiguousTokensEditing.append(this._lineTokens[firstLineIndex], lastLineTokens);\n        // Delete middle lines\n        this._deleteLines(range.startLineNumber, range.endLineNumber - range.startLineNumber);\n    }\n    _acceptInsertText(position, eolCount, firstLineLength) {\n        if (eolCount === 0 && firstLineLength === 0) {\n            // Nothing to insert\n            return;\n        }\n        const lineIndex = position.lineNumber - 1;\n        if (lineIndex >= this._len) {\n            return;\n        }\n        if (eolCount === 0) {\n            // Inserting text on one line\n            this._lineTokens[lineIndex] = ContiguousTokensEditing.insert(this._lineTokens[lineIndex], position.column - 1, firstLineLength);\n            return;\n        }\n        this._lineTokens[lineIndex] = ContiguousTokensEditing.deleteEnding(this._lineTokens[lineIndex], position.column - 1);\n        this._lineTokens[lineIndex] = ContiguousTokensEditing.insert(this._lineTokens[lineIndex], position.column - 1, firstLineLength);\n        this._insertLines(position.lineNumber, eolCount);\n    }\n    //#endregion\n    setMultilineTokens(tokens, textModel) {\n        if (tokens.length === 0) {\n            return { changes: [] };\n        }\n        const ranges = [];\n        for (let i = 0, len = tokens.length; i < len; i++) {\n            const element = tokens[i];\n            let minChangedLineNumber = 0;\n            let maxChangedLineNumber = 0;\n            let hasChange = false;\n            for (let lineNumber = element.startLineNumber; lineNumber <= element.endLineNumber; lineNumber++) {\n                if (hasChange) {\n                    this.setTokens(textModel.getLanguageId(), lineNumber - 1, textModel.getLineLength(lineNumber), element.getLineTokens(lineNumber), false);\n                    maxChangedLineNumber = lineNumber;\n                }\n                else {\n                    const lineHasChange = this.setTokens(textModel.getLanguageId(), lineNumber - 1, textModel.getLineLength(lineNumber), element.getLineTokens(lineNumber), true);\n                    if (lineHasChange) {\n                        hasChange = true;\n                        minChangedLineNumber = lineNumber;\n                        maxChangedLineNumber = lineNumber;\n                    }\n                }\n            }\n            if (hasChange) {\n                ranges.push({ fromLineNumber: minChangedLineNumber, toLineNumber: maxChangedLineNumber, });\n            }\n        }\n        return { changes: ranges };\n    }\n}\nfunction getDefaultMetadata(topLevelLanguageId) {\n    return ((topLevelLanguageId << 0 /* MetadataConsts.LANGUAGEID_OFFSET */)\n        | (0 /* StandardTokenType.Other */ << 8 /* MetadataConsts.TOKEN_TYPE_OFFSET */)\n        | (0 /* FontStyle.None */ << 11 /* MetadataConsts.FONT_STYLE_OFFSET */)\n        | (1 /* ColorId.DefaultForeground */ << 15 /* MetadataConsts.FOREGROUND_OFFSET */)\n        | (2 /* ColorId.DefaultBackground */ << 24 /* MetadataConsts.BACKGROUND_OFFSET */)\n        // If there is no grammar, we just take a guess and try to match brackets.\n        | (1024 /* MetadataConsts.BALANCED_BRACKETS_MASK */)) >>> 0;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,gCAAgC;AACxD,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,uBAAuB,EAAEC,iBAAiB,EAAEC,aAAa,QAAQ,8BAA8B;AACxG,SAASC,UAAU,QAAQ,iBAAiB;AAC5C,SAASC,aAAa,QAAQ,8BAA8B;AAC5D;AACA;AACA;AACA,OAAO,MAAMC,qBAAqB,CAAC;EAC/BC,WAAWA,CAACC,eAAe,EAAE;IACzB,IAAI,CAACC,WAAW,GAAG,EAAE;IACrB,IAAI,CAACC,IAAI,GAAG,CAAC;IACb,IAAI,CAACC,gBAAgB,GAAGH,eAAe;EAC3C;EACAI,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACH,WAAW,GAAG,EAAE;IACrB,IAAI,CAACC,IAAI,GAAG,CAAC;EACjB;EACA,IAAIG,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACJ,WAAW,CAACK,MAAM,GAAG,CAAC;EACtC;EACAC,SAASA,CAACC,kBAAkB,EAAEC,SAAS,EAAEC,QAAQ,EAAE;IAC/C,IAAIC,aAAa,GAAG,IAAI;IACxB,IAAIF,SAAS,GAAG,IAAI,CAACP,IAAI,EAAE;MACvBS,aAAa,GAAG,IAAI,CAACV,WAAW,CAACQ,SAAS,CAAC;IAC/C;IACA,IAAIE,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAKjB,iBAAiB,EAAE;MAC/D,OAAO,IAAIE,UAAU,CAACD,aAAa,CAACgB,aAAa,CAAC,EAAED,QAAQ,EAAE,IAAI,CAACP,gBAAgB,CAAC;IACxF;IACA,MAAMS,UAAU,GAAG,IAAIC,WAAW,CAAC,CAAC,CAAC;IACrCD,UAAU,CAAC,CAAC,CAAC,GAAGF,QAAQ,CAACJ,MAAM;IAC/BM,UAAU,CAAC,CAAC,CAAC,GAAGE,kBAAkB,CAAC,IAAI,CAACX,gBAAgB,CAACY,gBAAgB,CAACP,kBAAkB,CAAC,CAAC;IAC9F,OAAO,IAAIZ,UAAU,CAACgB,UAAU,EAAEF,QAAQ,EAAE,IAAI,CAACP,gBAAgB,CAAC;EACtE;EACA,OAAOa,cAAcA,CAACR,kBAAkB,EAAES,cAAc,EAAEC,OAAO,EAAE;IAC/D,MAAMC,MAAM,GAAGD,OAAO,GAAGvB,aAAa,CAACuB,OAAO,CAAC,GAAG,IAAI;IACtD,IAAID,cAAc,KAAK,CAAC,EAAE;MACtB,IAAIG,sBAAsB,GAAG,KAAK;MAClC,IAAID,MAAM,IAAIA,MAAM,CAACb,MAAM,GAAG,CAAC,EAAE;QAC7Bc,sBAAsB,GAAIvB,aAAa,CAACwB,aAAa,CAACF,MAAM,CAAC,CAAC,CAAC,CAAC,KAAKX,kBAAmB;MAC5F;MACA,IAAI,CAACY,sBAAsB,EAAE;QACzB,OAAO1B,iBAAiB;MAC5B;IACJ;IACA,IAAI,CAACyB,MAAM,IAAIA,MAAM,CAACb,MAAM,KAAK,CAAC,EAAE;MAChC,MAAMa,MAAM,GAAG,IAAIN,WAAW,CAAC,CAAC,CAAC;MACjCM,MAAM,CAAC,CAAC,CAAC,GAAGF,cAAc;MAC1BE,MAAM,CAAC,CAAC,CAAC,GAAGL,kBAAkB,CAACN,kBAAkB,CAAC;MAClD,OAAOW,MAAM,CAACG,MAAM;IACxB;IACA;IACAH,MAAM,CAACA,MAAM,CAACb,MAAM,GAAG,CAAC,CAAC,GAAGW,cAAc;IAC1C,IAAIE,MAAM,CAACI,UAAU,KAAK,CAAC,IAAIJ,MAAM,CAACK,UAAU,KAAKL,MAAM,CAACG,MAAM,CAACE,UAAU,EAAE;MAC3E;MACA,OAAOL,MAAM,CAACG,MAAM;IACxB;IACA,OAAOH,MAAM;EACjB;EACAM,WAAWA,CAAChB,SAAS,EAAE;IACnB,OAAOA,SAAS,IAAI,IAAI,CAACP,IAAI,EAAE;MAC3B,IAAI,CAACD,WAAW,CAAC,IAAI,CAACC,IAAI,CAAC,GAAG,IAAI;MAClC,IAAI,CAACA,IAAI,EAAE;IACf;EACJ;EACAwB,YAAYA,CAACC,KAAK,EAAEC,WAAW,EAAE;IAC7B,IAAIA,WAAW,KAAK,CAAC,EAAE;MACnB;IACJ;IACA,IAAID,KAAK,GAAGC,WAAW,GAAG,IAAI,CAAC1B,IAAI,EAAE;MACjC0B,WAAW,GAAG,IAAI,CAAC1B,IAAI,GAAGyB,KAAK;IACnC;IACA,IAAI,CAAC1B,WAAW,CAAC4B,MAAM,CAACF,KAAK,EAAEC,WAAW,CAAC;IAC3C,IAAI,CAAC1B,IAAI,IAAI0B,WAAW;EAC5B;EACAE,YAAYA,CAACC,WAAW,EAAEC,WAAW,EAAE;IACnC,IAAIA,WAAW,KAAK,CAAC,EAAE;MACnB;IACJ;IACA,MAAMpB,UAAU,GAAG,EAAE;IACrB,KAAK,IAAIqB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,WAAW,EAAEC,CAAC,EAAE,EAAE;MAClCrB,UAAU,CAACqB,CAAC,CAAC,GAAG,IAAI;IACxB;IACA,IAAI,CAAChC,WAAW,GAAGV,MAAM,CAAC2C,WAAW,CAAC,IAAI,CAACjC,WAAW,EAAE8B,WAAW,EAAEnB,UAAU,CAAC;IAChF,IAAI,CAACV,IAAI,IAAI8B,WAAW;EAC5B;EACAG,SAASA,CAAC3B,kBAAkB,EAAEC,SAAS,EAAEQ,cAAc,EAAEC,OAAO,EAAEkB,aAAa,EAAE;IAC7E,MAAMjB,MAAM,GAAGrB,qBAAqB,CAACkB,cAAc,CAAC,IAAI,CAACb,gBAAgB,CAACY,gBAAgB,CAACP,kBAAkB,CAAC,EAAES,cAAc,EAAEC,OAAO,CAAC;IACxI,IAAI,CAACO,WAAW,CAAChB,SAAS,CAAC;IAC3B,MAAM4B,SAAS,GAAG,IAAI,CAACpC,WAAW,CAACQ,SAAS,CAAC;IAC7C,IAAI,CAACR,WAAW,CAACQ,SAAS,CAAC,GAAGU,MAAM;IACpC,IAAIiB,aAAa,EAAE;MACf,OAAO,CAACtC,qBAAqB,CAACwC,OAAO,CAACD,SAAS,EAAElB,MAAM,CAAC;IAC5D;IACA,OAAO,KAAK;EAChB;EACA,OAAOmB,OAAOA,CAACC,EAAE,EAAEC,EAAE,EAAE;IACnB,IAAI,CAACD,EAAE,IAAI,CAACC,EAAE,EAAE;MACZ,OAAO,CAACD,EAAE,IAAI,CAACC,EAAE;IACrB;IACA,MAAMC,CAAC,GAAG9C,aAAa,CAAC4C,EAAE,CAAC;IAC3B,MAAMG,CAAC,GAAG/C,aAAa,CAAC6C,EAAE,CAAC;IAC3B,IAAIC,CAAC,CAACnC,MAAM,KAAKoC,CAAC,CAACpC,MAAM,EAAE;MACvB,OAAO,KAAK;IAChB;IACA,KAAK,IAAI2B,CAAC,GAAG,CAAC,EAAEU,GAAG,GAAGF,CAAC,CAACnC,MAAM,EAAE2B,CAAC,GAAGU,GAAG,EAAEV,CAAC,EAAE,EAAE;MAC1C,IAAIQ,CAAC,CAACR,CAAC,CAAC,KAAKS,CAAC,CAACT,CAAC,CAAC,EAAE;QACf,OAAO,KAAK;MAChB;IACJ;IACA,OAAO,IAAI;EACf;EACA;EACAW,UAAUA,CAACC,KAAK,EAAEC,QAAQ,EAAEC,eAAe,EAAE;IACzC,IAAI,CAACC,kBAAkB,CAACH,KAAK,CAAC;IAC9B,IAAI,CAACI,iBAAiB,CAAC,IAAIzD,QAAQ,CAACqD,KAAK,CAACK,eAAe,EAAEL,KAAK,CAACM,WAAW,CAAC,EAAEL,QAAQ,EAAEC,eAAe,CAAC;EAC7G;EACAC,kBAAkBA,CAACH,KAAK,EAAE;IACtB,MAAMO,cAAc,GAAGP,KAAK,CAACK,eAAe,GAAG,CAAC;IAChD,IAAIE,cAAc,IAAI,IAAI,CAAClD,IAAI,EAAE;MAC7B;IACJ;IACA,IAAI2C,KAAK,CAACK,eAAe,KAAKL,KAAK,CAACQ,aAAa,EAAE;MAC/C,IAAIR,KAAK,CAACM,WAAW,KAAKN,KAAK,CAACS,SAAS,EAAE;QACvC;QACA;MACJ;MACA,IAAI,CAACrD,WAAW,CAACmD,cAAc,CAAC,GAAG3D,uBAAuB,CAAC8D,MAAM,CAAC,IAAI,CAACtD,WAAW,CAACmD,cAAc,CAAC,EAAEP,KAAK,CAACM,WAAW,GAAG,CAAC,EAAEN,KAAK,CAACS,SAAS,GAAG,CAAC,CAAC;MAC/I;IACJ;IACA,IAAI,CAACrD,WAAW,CAACmD,cAAc,CAAC,GAAG3D,uBAAuB,CAAC+D,YAAY,CAAC,IAAI,CAACvD,WAAW,CAACmD,cAAc,CAAC,EAAEP,KAAK,CAACM,WAAW,GAAG,CAAC,CAAC;IAChI,MAAMM,aAAa,GAAGZ,KAAK,CAACQ,aAAa,GAAG,CAAC;IAC7C,IAAIK,cAAc,GAAG,IAAI;IACzB,IAAID,aAAa,GAAG,IAAI,CAACvD,IAAI,EAAE;MAC3BwD,cAAc,GAAGjE,uBAAuB,CAACkE,eAAe,CAAC,IAAI,CAAC1D,WAAW,CAACwD,aAAa,CAAC,EAAEZ,KAAK,CAACS,SAAS,GAAG,CAAC,CAAC;IAClH;IACA;IACA,IAAI,CAACrD,WAAW,CAACmD,cAAc,CAAC,GAAG3D,uBAAuB,CAACmE,MAAM,CAAC,IAAI,CAAC3D,WAAW,CAACmD,cAAc,CAAC,EAAEM,cAAc,CAAC;IACnH;IACA,IAAI,CAAChC,YAAY,CAACmB,KAAK,CAACK,eAAe,EAAEL,KAAK,CAACQ,aAAa,GAAGR,KAAK,CAACK,eAAe,CAAC;EACzF;EACAD,iBAAiBA,CAACY,QAAQ,EAAEf,QAAQ,EAAEC,eAAe,EAAE;IACnD,IAAID,QAAQ,KAAK,CAAC,IAAIC,eAAe,KAAK,CAAC,EAAE;MACzC;MACA;IACJ;IACA,MAAMtC,SAAS,GAAGoD,QAAQ,CAACC,UAAU,GAAG,CAAC;IACzC,IAAIrD,SAAS,IAAI,IAAI,CAACP,IAAI,EAAE;MACxB;IACJ;IACA,IAAI4C,QAAQ,KAAK,CAAC,EAAE;MAChB;MACA,IAAI,CAAC7C,WAAW,CAACQ,SAAS,CAAC,GAAGhB,uBAAuB,CAACsE,MAAM,CAAC,IAAI,CAAC9D,WAAW,CAACQ,SAAS,CAAC,EAAEoD,QAAQ,CAACG,MAAM,GAAG,CAAC,EAAEjB,eAAe,CAAC;MAC/H;IACJ;IACA,IAAI,CAAC9C,WAAW,CAACQ,SAAS,CAAC,GAAGhB,uBAAuB,CAAC+D,YAAY,CAAC,IAAI,CAACvD,WAAW,CAACQ,SAAS,CAAC,EAAEoD,QAAQ,CAACG,MAAM,GAAG,CAAC,CAAC;IACpH,IAAI,CAAC/D,WAAW,CAACQ,SAAS,CAAC,GAAGhB,uBAAuB,CAACsE,MAAM,CAAC,IAAI,CAAC9D,WAAW,CAACQ,SAAS,CAAC,EAAEoD,QAAQ,CAACG,MAAM,GAAG,CAAC,EAAEjB,eAAe,CAAC;IAC/H,IAAI,CAACjB,YAAY,CAAC+B,QAAQ,CAACC,UAAU,EAAEhB,QAAQ,CAAC;EACpD;EACA;EACAmB,kBAAkBA,CAAC9C,MAAM,EAAE+C,SAAS,EAAE;IAClC,IAAI/C,MAAM,CAACb,MAAM,KAAK,CAAC,EAAE;MACrB,OAAO;QAAE6D,OAAO,EAAE;MAAG,CAAC;IAC1B;IACA,MAAMC,MAAM,GAAG,EAAE;IACjB,KAAK,IAAInC,CAAC,GAAG,CAAC,EAAEU,GAAG,GAAGxB,MAAM,CAACb,MAAM,EAAE2B,CAAC,GAAGU,GAAG,EAAEV,CAAC,EAAE,EAAE;MAC/C,MAAMoC,OAAO,GAAGlD,MAAM,CAACc,CAAC,CAAC;MACzB,IAAIqC,oBAAoB,GAAG,CAAC;MAC5B,IAAIC,oBAAoB,GAAG,CAAC;MAC5B,IAAIC,SAAS,GAAG,KAAK;MACrB,KAAK,IAAIV,UAAU,GAAGO,OAAO,CAACnB,eAAe,EAAEY,UAAU,IAAIO,OAAO,CAAChB,aAAa,EAAES,UAAU,EAAE,EAAE;QAC9F,IAAIU,SAAS,EAAE;UACX,IAAI,CAACrC,SAAS,CAAC+B,SAAS,CAAC7C,aAAa,CAAC,CAAC,EAAEyC,UAAU,GAAG,CAAC,EAAEI,SAAS,CAACO,aAAa,CAACX,UAAU,CAAC,EAAEO,OAAO,CAACK,aAAa,CAACZ,UAAU,CAAC,EAAE,KAAK,CAAC;UACxIS,oBAAoB,GAAGT,UAAU;QACrC,CAAC,MACI;UACD,MAAMa,aAAa,GAAG,IAAI,CAACxC,SAAS,CAAC+B,SAAS,CAAC7C,aAAa,CAAC,CAAC,EAAEyC,UAAU,GAAG,CAAC,EAAEI,SAAS,CAACO,aAAa,CAACX,UAAU,CAAC,EAAEO,OAAO,CAACK,aAAa,CAACZ,UAAU,CAAC,EAAE,IAAI,CAAC;UAC7J,IAAIa,aAAa,EAAE;YACfH,SAAS,GAAG,IAAI;YAChBF,oBAAoB,GAAGR,UAAU;YACjCS,oBAAoB,GAAGT,UAAU;UACrC;QACJ;MACJ;MACA,IAAIU,SAAS,EAAE;QACXJ,MAAM,CAACQ,IAAI,CAAC;UAAEC,cAAc,EAAEP,oBAAoB;UAAEQ,YAAY,EAAEP;QAAsB,CAAC,CAAC;MAC9F;IACJ;IACA,OAAO;MAAEJ,OAAO,EAAEC;IAAO,CAAC;EAC9B;AACJ;AACA,SAAStD,kBAAkBA,CAACN,kBAAkB,EAAE;EAC5C,OAAO,CAAEA,kBAAkB,IAAI,CAAC,CAAC,yCAC1B,CAAC,CAAC,iCAAiC,CAAC,CAAC,sCAAuC,GAC5E,CAAC,CAAC,wBAAwB,EAAE,CAAC,sCAAuC,GACpE,CAAC,CAAC,mCAAmC,EAAE,CAAC,sCAAuC,GAC/E,CAAC,CAAC,mCAAmC,EAAE,CAAC;EAC3C;EAAA,EACG,IAAI,CAAC,2CAA4C,MAAM,CAAC;AACnE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}