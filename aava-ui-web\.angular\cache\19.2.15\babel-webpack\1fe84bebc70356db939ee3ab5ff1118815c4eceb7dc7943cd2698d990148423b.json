{"ast": null, "code": "import sparse from \"./sparse.js\";\nimport { Selection } from \"./index.js\";\nexport default function () {\n  return new Selection(this._exit || this._groups.map(sparse), this._parents);\n}", "map": {"version": 3, "names": ["sparse", "Selection", "_exit", "_groups", "map", "_parents"], "sources": ["C:/console/aava-ui-web/node_modules/d3-selection/src/selection/exit.js"], "sourcesContent": ["import sparse from \"./sparse.js\";\nimport {Selection} from \"./index.js\";\n\nexport default function() {\n  return new Selection(this._exit || this._groups.map(sparse), this._parents);\n}\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,aAAa;AAChC,SAAQC,SAAS,QAAO,YAAY;AAEpC,eAAe,YAAW;EACxB,OAAO,IAAIA,SAAS,CAAC,IAAI,CAACC,KAAK,IAAI,IAAI,CAACC,OAAO,CAACC,GAAG,CAACJ,MAAM,CAAC,EAAE,IAAI,CAACK,QAAQ,CAAC;AAC7E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}