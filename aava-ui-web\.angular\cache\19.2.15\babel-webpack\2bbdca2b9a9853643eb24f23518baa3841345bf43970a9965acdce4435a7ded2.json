{"ast": null, "code": "function textInterpolate(i) {\n  return function (t) {\n    this.textContent = i.call(this, t);\n  };\n}\nfunction textTween(value) {\n  var t0, i0;\n  function tween() {\n    var i = value.apply(this, arguments);\n    if (i !== i0) t0 = (i0 = i) && textInterpolate(i);\n    return t0;\n  }\n  tween._value = value;\n  return tween;\n}\nexport default function (value) {\n  var key = \"text\";\n  if (arguments.length < 1) return (key = this.tween(key)) && key._value;\n  if (value == null) return this.tween(key, null);\n  if (typeof value !== \"function\") throw new Error();\n  return this.tween(key, textTween(value));\n}", "map": {"version": 3, "names": ["textInterpolate", "i", "t", "textContent", "call", "textTween", "value", "t0", "i0", "tween", "apply", "arguments", "_value", "key", "length", "Error"], "sources": ["C:/console/aava-ui-web/node_modules/d3-transition/src/transition/textTween.js"], "sourcesContent": ["function textInterpolate(i) {\n  return function(t) {\n    this.textContent = i.call(this, t);\n  };\n}\n\nfunction textTween(value) {\n  var t0, i0;\n  function tween() {\n    var i = value.apply(this, arguments);\n    if (i !== i0) t0 = (i0 = i) && textInterpolate(i);\n    return t0;\n  }\n  tween._value = value;\n  return tween;\n}\n\nexport default function(value) {\n  var key = \"text\";\n  if (arguments.length < 1) return (key = this.tween(key)) && key._value;\n  if (value == null) return this.tween(key, null);\n  if (typeof value !== \"function\") throw new Error;\n  return this.tween(key, textTween(value));\n}\n"], "mappings": "AAAA,SAASA,eAAeA,CAACC,CAAC,EAAE;EAC1B,OAAO,UAASC,CAAC,EAAE;IACjB,IAAI,CAACC,WAAW,GAAGF,CAAC,CAACG,IAAI,CAAC,IAAI,EAAEF,CAAC,CAAC;EACpC,CAAC;AACH;AAEA,SAASG,SAASA,CAACC,KAAK,EAAE;EACxB,IAAIC,EAAE,EAAEC,EAAE;EACV,SAASC,KAAKA,CAAA,EAAG;IACf,IAAIR,CAAC,GAAGK,KAAK,CAACI,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IACpC,IAAIV,CAAC,KAAKO,EAAE,EAAED,EAAE,GAAG,CAACC,EAAE,GAAGP,CAAC,KAAKD,eAAe,CAACC,CAAC,CAAC;IACjD,OAAOM,EAAE;EACX;EACAE,KAAK,CAACG,MAAM,GAAGN,KAAK;EACpB,OAAOG,KAAK;AACd;AAEA,eAAe,UAASH,KAAK,EAAE;EAC7B,IAAIO,GAAG,GAAG,MAAM;EAChB,IAAIF,SAAS,CAACG,MAAM,GAAG,CAAC,EAAE,OAAO,CAACD,GAAG,GAAG,IAAI,CAACJ,KAAK,CAACI,GAAG,CAAC,KAAKA,GAAG,CAACD,MAAM;EACtE,IAAIN,KAAK,IAAI,IAAI,EAAE,OAAO,IAAI,CAACG,KAAK,CAACI,GAAG,EAAE,IAAI,CAAC;EAC/C,IAAI,OAAOP,KAAK,KAAK,UAAU,EAAE,MAAM,IAAIS,KAAK,CAAD,CAAC;EAChD,OAAO,IAAI,CAACN,KAAK,CAACI,GAAG,EAAER,SAAS,CAACC,KAAK,CAAC,CAAC;AAC1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}