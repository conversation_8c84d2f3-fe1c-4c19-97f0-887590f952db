{"ast": null, "code": "import { CommonModule, formatDate } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { AvaTagComponent, DropdownComponent, LinkComponent } from '@ava/play-comp-library';\nimport { ACTIVE_MONITORING_OPTIONS, ActiveMonitorings, APIKeys, DASHBOARD_CARD_DETAILS } from './constants/dashoard.constant';\nimport approvalText from '../approval/constants/approval.json';\nimport { DashboardImgCardComponent } from '../../shared/components/dashboard-img-card/dashboard-img-card.component';\nimport { DashboardTxtCardComponent } from '../../shared/components/dashboard-txt-card/dashboard-txt-card.component';\nimport { DashboardAgentMonitoringCardComponent } from '../../shared/components/dashboard-agent-monitoring-card/dashboard-agent-monitoring-card.component';\nimport { DashboardApprovalCardComponent } from '../../shared/components/dashboard-approval-card/dashboard-approval-card.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../shared/services/shared-api-service.service\";\nimport * as i3 from \"../../shared/services/approval.service\";\nimport * as i4 from \"@angular/common\";\nfunction DashboardComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵelement(1, \"app-dashboard-txt-card\", 26);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const details_r1 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"title\", details_r1.title)(\"value\", details_r1.value)(\"subtitle\", details_r1.subtitle);\n  }\n}\nfunction DashboardComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"app-dashboard-agent-monitoring-card\", 27);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const activity_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"activity\", activity_r2);\n  }\n}\nfunction DashboardComponent_ava_tag_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ava-tag\", 28);\n    i0.ɵɵlistener(\"clicked\", function DashboardComponent_ava_tag_25_Template_ava_tag_clicked_0_listener() {\n      const tab_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.updatedSelectedApprovalTab(tab_r4));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tab_r4 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"label\", tab_r4)(\"pill\", true)(\"customStyle\", ctx_r4.selectedApprovalTab === tab_r4 ? ctx_r4.agentApprovalsSelectedPillStyle : ctx_r4.agentApprovalsPillStyle);\n  }\n}\nfunction DashboardComponent_For_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"app-dashboard-approval-card\", 29);\n    i0.ɵɵlistener(\"testClick\", function DashboardComponent_For_28_Template_app_dashboard_approval_card_testClick_1_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onTestClick($event));\n    })(\"sendBackClick\", function DashboardComponent_For_28_Template_app_dashboard_approval_card_sendBackClick_1_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSendBackClick($event));\n    })(\"approveClick\", function DashboardComponent_For_28_Template_app_dashboard_approval_card_approveClick_1_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.approveItem($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r7 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"title\", item_r7.title)(\"email\", item_r7.requestedBy)(\"date\", item_r7.requestedAt)(\"type\", item_r7.type)(\"id\", item_r7.id)(\"rowData\", item_r7.rawData);\n  }\n}\nexport let DashboardComponent = /*#__PURE__*/(() => {\n  class DashboardComponent {\n    router;\n    apiService;\n    approvalService;\n    options = ACTIVE_MONITORING_OPTIONS;\n    labels = approvalText.labels;\n    dashboardDetails = [\n      // {\n      //   icon: '',\n      //   title: '',\n      //   value: 0,\n      //   subtitle: '',\n      //   badge: '',\n      // },\n      // {\n      //   icon: 'trending-up',\n      //   title: 'Active Workflows',\n      //   value: 80,\n      //   subtitle: 'Agents actively running',\n      //   badge: 'S',\n      // },\n      // {\n      //   icon: 'trending-up',\n      //   title: 'Active Agents',\n      //   value: 1240,\n      //   subtitle: 'Agents active',\n      //   badge: 'S',\n      // },\n      // {\n      //   icon: 'trending-up',\n      //   title: 'Agents Approval',\n      //   value: 120,\n      //   subtitle: 'Agents actively running',\n      //   badge: 'S',\n      // },\n      // {\n      //   icon: 'trending-up',\n      //   title: 'Active Workflows',\n      //   value: 80,\n      //   subtitle: 'Agents actively running',\n      //   badge: 'S',\n      // },\n    ];\n    // Dashboard metrics\n    totalAgents = 0;\n    newAgentsCreated = 50;\n    totalWorkflows = 124;\n    newWorkflowsCreated = 50;\n    totalUsers = 300;\n    newUsersAdded = 50;\n    selectedActiveMonitoring = ActiveMonitorings.tools;\n    activityMonitoringCount = 5;\n    // Current user info (would come from auth service in real app)\n    currentUser = {\n      name: 'Akash Raj'\n    };\n    // Footer info\n    currentYear = new Date().getFullYear();\n    // Model usage data\n    modelUsage = [{\n      id: '1',\n      name: 'GPT 3',\n      publisher: {\n        name: 'Open AI',\n        logo: 'assets/images/logos/openai-logo.png'\n      },\n      agentsCount: 48\n    }, {\n      id: '2',\n      name: 'Claude 2',\n      publisher: {\n        name: 'Anthropic',\n        logo: 'assets/images/logos/anthropic-logo.png'\n      },\n      agentsCount: 24\n    }, {\n      id: '3',\n      name: 'Gemini',\n      publisher: {\n        name: 'Google',\n        logo: 'assets/images/logos/google-logo.png'\n      },\n      agentsCount: 20\n    }, {\n      id: '4',\n      name: 'GPT-4',\n      publisher: {\n        name: 'Open AI',\n        logo: 'assets/images/logos/openai-logo.png'\n      },\n      agentsCount: 8\n    }];\n    // Pending approvals\n    pendingApprovals = [{\n      id: '1',\n      name: 'Test Ruby to Springboot',\n      type: 'migration'\n    }, {\n      id: '2',\n      name: 'Customer Support Chatbot',\n      type: 'agent'\n    }, {\n      id: '3',\n      name: 'Invoice Processing & Approval',\n      type: 'workflow'\n    }, {\n      id: '4',\n      name: 'Invoice Processing & Approval',\n      type: 'workflow'\n    }, {\n      id: '5',\n      name: 'AI-Powered Code Review Assistant',\n      type: 'agent'\n    }, {\n      id: '6',\n      name: 'AI-Powered Code Review Assistant',\n      type: 'agent'\n    }, {\n      id: '7',\n      name: 'AI-Powered Code Review Assistant',\n      type: 'agent'\n    }, {\n      id: '8',\n      name: 'AI-Powered Code Review Assistant',\n      type: 'agent'\n    }];\n    activityMonitoring = [\n      // {\n      //   agentName: 'Agent Name will be here',\n      //   status: 'Active',\n      //   user: 'Michael Scott',\n      //   date: '12 June 2025',\n      //   totalRuns: 264,\n      // },\n      // {\n      //   agentName: 'Agent Name will be here',\n      //   status: 'Active',\n      //   user: 'Michael Scott',\n      //   date: '12 June 2025',\n      //   totalRuns: 264,\n      // },\n      // {\n      //   agentName: 'Agent Name will be here',\n      //   status: 'Active',\n      //   user: 'Michael Scott',\n      //   date: '12 June 2025',\n      //   totalRuns: 264,\n      // },\n      // {\n      //   agentName: 'Agent Name will be here',\n      //   status: 'Active',\n      //   user: 'Michael Scott',\n      //   date: '12 June 2025',\n      //   totalRuns: 264,\n      // },\n      // {\n      //   agentName: 'Agent Name will be here',\n      //   status: 'Active',\n      //   user: 'Michael Scott',\n      //   date: '12 June 2025',\n      //   totalRuns: 264,\n      // },\n    ];\n    workflowApprovals = [];\n    approvalTabs = ['Agents', 'Workflow', 'Tools'];\n    selectedApprovalTab = 'Agents';\n    approvalData = [];\n    constructor(router, apiService, approvalService) {\n      this.router = router;\n      this.apiService = apiService;\n      this.approvalService = approvalService;\n    }\n    // seprating the tool and agents.\n    activityMonitoringTools = [];\n    activityMonitoringAgents = [];\n    setActiveMonitoringData() {\n      // INsted of re-formating, just switch the values\n      if (this.selectedActiveMonitoring === ActiveMonitorings.tools) {\n        this.activityMonitoring = this.activityMonitoringTools;\n      } else {\n        this.activityMonitoring = this.activityMonitoringAgents;\n      }\n    }\n    mapToActivityMonitoringItem = (item, nameKey, usageKey) => ({\n      agentName: item[nameKey] || '',\n      totalRuns: Number(item[usageKey]) || 0,\n      status: '',\n      user: '',\n      date: ''\n    });\n    toRequestStatus(value) {\n      return value === 'approved' || value === 'rejected' || value === 'review' ? value : 'review';\n    }\n    initApiCalls() {\n      const date = new Date();\n      const dateEnd = this.apiService.formatDate(date);\n      date.setDate(1);\n      const dateStart = this.apiService.formatDate(date);\n      this.apiService.getCollabrativeAgentAnalytics(dateStart, dateEnd).subscribe(response => {\n        // setting dashboard card values\n        this.dashboardDetails = DASHBOARD_CARD_DETAILS.map(cardDetail => {\n          cardDetail.value = response[cardDetail.field] || this.totalAgents;\n          return cardDetail;\n        });\n        // Active Monitoring\n        // Extracting tools and agents to seprate varibales to reduce frequent re-formatig as the dropdown value changes\n        this.activityMonitoringTools = response[APIKeys.toolUsage].slice(0, this.activityMonitoringCount).map(toolUsage => this.mapToActivityMonitoringItem(toolUsage, APIKeys.toolName, APIKeys.usageCount));\n        this.activityMonitoringAgents = response[APIKeys.agentMetrics].slice(0, this.activityMonitoringCount).map(agentMetric => this.mapToActivityMonitoringItem(agentMetric, APIKeys.agentName, APIKeys.workflowCount));\n        this.setActiveMonitoringData();\n      });\n      this.approvalService.getAllReviewAgents(1, 100, false).subscribe(res => {\n        const agentReviewDetails = res?.agentReviewDetails || [];\n        // Extracting agents which are under review\n        this.totalAgents = agentReviewDetails.filter(agentReviewDetail => agentReviewDetail.status !== 'approved')?.length || 0;\n        // If this API call is late then will set approval count here.\n        const toolCardDetial = this.dashboardDetails.at(-1);\n        if (toolCardDetial) {\n          toolCardDetial.value = this.totalAgents;\n        }\n      });\n      this.approvalService.getAllReviewWorkflows(1, 5, false).subscribe(response => {\n        const type = 'workflow';\n        this.workflowApprovals = response.workflowReviewDetails?.map(req => {\n          const statusIcons = {\n            approved: 'circle-check-big',\n            rejected: 'circle-x',\n            review: 'clock'\n          };\n          const statusTexts = {\n            approved: this.labels.approved,\n            rejected: this.labels.rejected,\n            review: this.labels.review\n          };\n          const statusKey = this.toRequestStatus(req?.status);\n          let specificId = 0;\n          let title = '';\n          specificId = req.workflowId;\n          title = req.workflowName;\n          return {\n            id: req.id,\n            refId: specificId,\n            type: type,\n            session1: {\n              title: title,\n              labels: [{\n                name: type,\n                color: 'success',\n                background: 'red',\n                type: 'normal'\n              }, {\n                name: req.changeRequestType,\n                color: req.changeRequestType === 'update' ? 'error' : 'info',\n                background: 'red',\n                type: 'pill'\n              }]\n            },\n            session2: [{\n              name: type,\n              color: 'default',\n              background: 'red',\n              type: 'normal'\n            }, {\n              name: req.status,\n              color: 'default',\n              background: 'red',\n              type: 'normal'\n            }],\n            session3: [{\n              iconName: 'user',\n              label: req.requestedBy\n            }, {\n              iconName: 'calendar-days',\n              label: formatDate(req?.requestedAt, 'dd MMM yyyy', 'en-IN')\n            }],\n            session4: {\n              status: statusTexts[statusKey],\n              iconName: statusIcons[statusKey]\n            }\n          };\n        });\n      });\n    }\n    ngOnInit() {\n      // The layout is now managed with fixed heights in CSS\n      // No need for recalculateLayout\n      this.initApiCalls();\n      this.getAllReviewAgents();\n    }\n    // Navigate to a route\n    navigateTo(route) {\n      this.router.navigate([route]);\n    }\n    // Test method to demonstrate loader functionality\n    testLoader() {\n      this.apiService.getConfigLabels().subscribe({\n        next: response => {},\n        error: error => {}\n      });\n    }\n    uClick(index) {\n      this.router.navigate(['/approval']);\n    }\n    onSelectionChange(data) {\n      this.selectedActiveMonitoring = data.selectedValue;\n      this.setActiveMonitoringData();\n    }\n    onQuickActionClick(action) {\n      console.log('Quick action clicked:', action);\n      // Handle navigation or action based on action.id\n      switch (action.id) {\n        case 'build-agent':\n          this.router.navigate(['/build/agents/create']);\n          break;\n        case 'build-workflow':\n          this.router.navigate(['/build/workflows/create']);\n          break;\n        case 'create-prompt':\n          this.router.navigate(['/libraries/prompts/create']);\n          break;\n        case 'create-tool':\n          this.router.navigate(['/libraries/tools/create']);\n          break;\n        case 'create-guardrail':\n          this.router.navigate(['/libraries/guardrails/create']);\n          break;\n        case 'create-knowledge-base':\n          this.router.navigate(['/libraries/knowledge-base/create']);\n          break;\n        default:\n          console.log('Unknown action:', action.id);\n      }\n    }\n    // Common pill base style\n    basePillStyle = {\n      'border-radius': '20px',\n      padding: '0px 20px',\n      height: '32px',\n      display: 'flex',\n      'justify-content': 'center'\n    };\n    // Custom pill style for Agent Approvals card\n    agentApprovalsPillStyle = {\n      ...this.basePillStyle,\n      'background-color': '#fff',\n      color: '#2D3036',\n      border: '1px solid #2563EB'\n    };\n    // Selected state pill style\n    agentApprovalsSelectedPillStyle = {\n      ...this.basePillStyle,\n      'background-color': '#2563EB',\n      color: '#FFFFFF'\n    };\n    updatedSelectedApprovalTab(tab) {\n      this.selectedApprovalTab = tab;\n      if (tab == 'Agents') {\n        this.getAllReviewAgents();\n      } else if (tab == 'Workflow') {\n        this.getAllReviewWorkflows();\n      } else if (tab == 'Tools') {\n        this.getAllReviewTools();\n      }\n    }\n    getAllReviewAgents() {\n      this.approvalService.getAllReviewAgents(1, 3, false).subscribe(response => {\n        this.approvalData = (response?.agentReviewDetails || []).map(item => ({\n          id: item.id,\n          title: item.agentName,\n          requestedBy: item.requestedBy,\n          requestedAt: formatDate(item.requestedAt, 'dd MMM yyyy', 'en-IN'),\n          type: 'agent',\n          rawData: item\n        }));\n      });\n    }\n    getAllReviewWorkflows() {\n      this.approvalService.getAllReviewWorkflows(1, 3, false).subscribe(response => {\n        this.approvalData = (response?.workflowReviewDetails || []).map(item => ({\n          title: item.workflowName,\n          requestedBy: item.requestedBy,\n          requestedAt: formatDate(item.requestedAt, 'dd MMM yyyy', 'en-IN'),\n          type: 'workflow',\n          rawData: item\n        }));\n      });\n    }\n    getAllReviewTools() {\n      this.approvalService.getAllReviewTools(1, 3, false).subscribe(response => {\n        this.approvalData = (response?.userToolReviewDetails || []).map(item => ({\n          title: item.toolName,\n          requestedBy: item.requestedBy,\n          requestedAt: formatDate(item.requestedAt, 'dd MMM yyyy', 'en-IN'),\n          type: 'tool',\n          rawData: item\n        }));\n      });\n    }\n    approveItem(item) {\n      // let approvalCall$;\n      // switch (type) {\n      //   case 'agent':\n      //     approvalCall$ = this.approvalService.approveAgent(item.id, entityId, status, reviewedBy);\n      //     break;\n      //   case 'workflow':\n      //     approvalCall$ = this.approvalService.approveWorkflow(id, entityId, status, reviewedBy);\n      //     break;\n      //   case 'tool':\n      //     approvalCall$ = this.approvalService.approveTool(id, entityId, status, reviewedBy);\n      //     break;\n      //   default:\n      //     console.error(`Unknown approval type: ${type}`);\n      //     return;\n      // }\n      // approvalCall$.subscribe(() => {\n      //   console.log(`${type} approved successfully`);\n      //   // optionally refresh the data here\n      // });\n    }\n    onTestClick(event) {}\n    onSendBackClick(event) {}\n    goToAnalytics() {\n      this.router.navigate(['/console/analytics']);\n    }\n    static ɵfac = function DashboardComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DashboardComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.SharedApiServiceService), i0.ɵɵdirectiveInject(i3.ApprovalService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DashboardComponent,\n      selectors: [[\"app-dashboard\"]],\n      decls: 31,\n      vars: 5,\n      consts: [[\"id\", \"dasboard-container\", 1, \"container-fluid\"], [\"id\", \"dashboard-cards-container\", 1, \"row\"], [1, \"col-md-8\", \"d-flex\", \"flex-column\", \"gap-2\"], [1, \"d-flex\", \"flex-column\"], [1, \"d-flex\", \"flex-wrap\", \"txt-card-wrapper\"], [\"class\", \"col-3\", 4, \"ngFor\", \"ngForOf\"], [\"id\", \"activity-monitoring-container\", 1, \"col-4\"], [1, \"active-monitoring-wrapper\"], [1, \"row\", \"active-monitoring\"], [1, \"col-5\", \"active-monitoring-text\"], [1, \"col-5\"], [\"dropdownTitle\", \"Agents\", 1, \"a-m-dropdown\", 3, \"selectionChange\", \"options\", \"search\"], [1, \"col-2\"], [\"label\", \"View All\", \"color\", \"#3B3F46\"], [1, \"d-flex\", \"flex-column\", \"gap-4\", \"active-monitoring-list\"], [4, \"ngFor\", \"ngForOf\"], [\"id\", \"dasborad-bottom-container\", 1, \"row\"], [\"id\", \"high-prioirty-container\", 1, \"col-12\"], [1, \"box-wrapper\", \"high-priority-wrapper\", \"p-4\"], [1, \"col-12\", \"agent-approvals-cards-header\", \"header-section\", \"mb-4\"], [1, \"d-flex\", \"gap-2\"], [\"color\", \"custom\", 3, \"label\", \"pill\", \"customStyle\", \"clicked\", 4, \"ngFor\", \"ngForOf\"], [1, \"row\", \"g-2\", \"overflow-y-scroll\"], [1, \"col-12\", \"col-md-4\"], [1, \"agent-approvals-cards-footer\", \"d-flex\", \"justify-content-end\"], [1, \"col-3\"], [3, \"title\", \"value\", \"subtitle\"], [3, \"activity\"], [\"color\", \"custom\", 3, \"clicked\", \"label\", \"pill\", \"customStyle\"], [3, \"testClick\", \"sendBackClick\", \"approveClick\", \"title\", \"email\", \"date\", \"type\", \"id\", \"rowData\"]],\n      template: function DashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelement(4, \"app-dashboard-img-card\");\n          i0.ɵɵelementStart(5, \"div\", 4);\n          i0.ɵɵtemplate(6, DashboardComponent_div_6_Template, 2, 3, \"div\", 5);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(7, \"div\", 6)(8, \"div\", 7)(9, \"div\", 8)(10, \"div\", 9);\n          i0.ɵɵtext(11, \"Active Monitoring\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"div\", 10)(13, \"ava-dropdown\", 11);\n          i0.ɵɵlistener(\"selectionChange\", function DashboardComponent_Template_ava_dropdown_selectionChange_13_listener($event) {\n            return ctx.onSelectionChange($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(14, \"div\", 12);\n          i0.ɵɵelement(15, \"ava-link\", 13);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"div\", 14);\n          i0.ɵɵtemplate(17, DashboardComponent_div_17_Template, 2, 1, \"div\", 15);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(18, \"div\", 16)(19, \"div\", 17)(20, \"div\", 18)(21, \"div\", 19)(22, \"span\");\n          i0.ɵɵtext(23, \" Approvals \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"div\", 20);\n          i0.ɵɵtemplate(25, DashboardComponent_ava_tag_25_Template, 1, 3, \"ava-tag\", 21);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(26, \"div\", 22);\n          i0.ɵɵrepeaterCreate(27, DashboardComponent_For_28_Template, 2, 6, \"div\", 23, i0.ɵɵrepeaterTrackByIndex);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"div\", 24);\n          i0.ɵɵelement(30, \"ava-link\", 13);\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngForOf\", ctx.dashboardDetails);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"options\", ctx.options)(\"search\", false);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngForOf\", ctx.activityMonitoring);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngForOf\", ctx.approvalTabs);\n          i0.ɵɵadvance(2);\n          i0.ɵɵrepeater(ctx.approvalData);\n        }\n      },\n      dependencies: [CommonModule, i4.NgForOf, RouterModule, DropdownComponent, AvaTagComponent, LinkComponent, DashboardImgCardComponent, DashboardTxtCardComponent, DashboardAgentMonitoringCardComponent, DashboardApprovalCardComponent],\n      styles: [\"#dasboard-container[_ngcontent-%COMP%] {\\n  margin-top: 12px;\\n}\\n\\n#dasborad-bottom-container[_ngcontent-%COMP%] {\\n  margin-top: 2rem;\\n}\\n\\n.fw-600[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n}\\n\\n.activity-monitoring-status-label[_ngcontent-%COMP%] {\\n  text-align: end;\\n}\\n\\n.font-12[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n}\\n\\n.active-monitoring[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n\\n.active-monitoring-text[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: #3B3F46;\\n}\\n\\n  .ava-dropdown {\\n  width: 100% !important;\\n}\\n\\n#dasborad-bottom-container[_ngcontent-%COMP%] {\\n  height: 30rem;\\n}\\n\\n#activity-monitoring-container[_ngcontent-%COMP%] {\\n  padding: 0px 0px 0px 10px;\\n}\\n\\n#high-prioirty-container[_ngcontent-%COMP%]   .high-priority-wrapper[_ngcontent-%COMP%] {\\n  padding: 12px;\\n}\\n\\n.box-wrapper[_ngcontent-%COMP%] {\\n  border-radius: 24px;\\n  background: #fff;\\n}\\n\\n.active-monitoring-list[_ngcontent-%COMP%] {\\n  height: 440px;\\n  overflow-y: auto;\\n  overflow-x: hidden;\\n  padding-bottom: 24px;\\n}\\n\\n.active-monitoring-wrapper[_ngcontent-%COMP%] {\\n  border-radius: 24px;\\n  padding: 24px;\\n  background: #fff;\\n}\\n.active-monitoring-wrapper[_ngcontent-%COMP%]   ava-dropdown[_ngcontent-%COMP%] {\\n  position: relative;\\n  top: -3px;\\n}\\n\\n.create-type[_ngcontent-%COMP%]   .ava-card-container[_ngcontent-%COMP%]   .ava-card.card[_ngcontent-%COMP%]::before {\\n  left: 16px;\\n}\\n.create-type[_ngcontent-%COMP%]   .ava-card-container[_ngcontent-%COMP%]   .ava-card.card[_ngcontent-%COMP%]::after {\\n  right: -16px;\\n}\\n\\n.approval-card-wrapper[_ngcontent-%COMP%] {\\n  margin: 10px;\\n}\\n\\n.header-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  width: 100%;\\n}\\n\\n.agent-approvals-cards-header[_ngcontent-%COMP%] {\\n  height: 36px;\\n  font-family: Mulish;\\n  font-weight: 700;\\n  font-style: Bold;\\n  font-size: 24px;\\n  color: #3B3F46;\\n}\\n\\n.agent-approvals-cards-footer[_ngcontent-%COMP%] {\\n  height: 20px;\\n  margin-top: 24px;\\n}\\n\\n.txt-card-wrapper[_ngcontent-%COMP%] {\\n  margin-top: 24px;\\n  margin-right: 5px;\\n}\\n\\n  .ava-dropdown button {\\n  border-radius: 200px !important;\\n  border-color: #BDBDBD !important;\\n}\\n  .ava-dropdown .dropdown-toggle {\\n  width: auto !important;\\n  gap: 5px;\\n  padding: 0px 16px !important;\\n  margin-bottom: 0px !important;\\n}\\n  .ava-dropdown .dropdown-toggle span {\\n  max-width: fit-content !important;\\n  color: #3B3F46 !important;\\n  font-family: Mulish !important;\\n  font-weight: 700 !important;\\n  font-size: 14px !important;\\n}\\n  .ava-dropdown .dropdown-toggle svg {\\n  stroke: #3B3F46;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return DashboardComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "formatDate", "RouterModule", "AvaTagComponent", "DropdownComponent", "LinkComponent", "ACTIVE_MONITORING_OPTIONS", "ActiveMonitorings", "APIKeys", "DASHBOARD_CARD_DETAILS", "approvalText", "DashboardImgCardComponent", "DashboardTxtCardComponent", "DashboardAgentMonitoringCardComponent", "DashboardApprovalCardComponent", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "details_r1", "title", "value", "subtitle", "activity_r2", "ɵɵlistener", "DashboardComponent_ava_tag_25_Template_ava_tag_clicked_0_listener", "tab_r4", "ɵɵrestoreView", "_r3", "$implicit", "ctx_r4", "ɵɵnextContext", "ɵɵresetView", "updatedSelectedApprovalTab", "selectedApprovalTab", "agentApprovalsSelectedPillStyle", "agentApprovalsPillStyle", "DashboardComponent_For_28_Template_app_dashboard_approval_card_testClick_1_listener", "$event", "_r6", "onTestClick", "DashboardComponent_For_28_Template_app_dashboard_approval_card_sendBackClick_1_listener", "onSendBackClick", "DashboardComponent_For_28_Template_app_dashboard_approval_card_approveClick_1_listener", "approveItem", "item_r7", "requestedBy", "requestedAt", "type", "id", "rawData", "DashboardComponent", "router", "apiService", "approvalService", "options", "labels", "dashboardDetails", "totalAgents", "newAgentsCreated", "totalWorkflows", "newWorkflowsCreated", "totalUsers", "newUsersAdded", "selectedActiveMonitoring", "tools", "activityMonitoringCount", "currentUser", "name", "currentYear", "Date", "getFullYear", "modelUsage", "publisher", "logo", "agentsCount", "pendingApprovals", "activityMonitoring", "workflowApprovals", "approvalTabs", "approvalData", "constructor", "activityMonitoringTools", "activityMonitoringAgents", "setActiveMonitoringData", "mapToActivityMonitoringItem", "item", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "totalRuns", "Number", "status", "user", "date", "toRequestStatus", "initApiCalls", "dateEnd", "setDate", "dateStart", "getCollabrativeAgentAnalytics", "subscribe", "response", "map", "cardDetail", "field", "toolUsage", "slice", "toolName", "usageCount", "agentMetrics", "agentMetric", "workflowCount", "getAllReviewAgents", "res", "agentReviewDetails", "filter", "agentReviewDetail", "length", "toolCardDetial", "at", "getAllReviewWorkflows", "workflowReviewDetails", "req", "statusIcons", "approved", "rejected", "review", "statusTexts", "statusKey", "specificId", "workflowId", "workflowName", "refId", "session1", "color", "background", "changeRequestType", "session2", "session3", "iconName", "label", "session4", "ngOnInit", "navigateTo", "route", "navigate", "<PERSON><PERSON><PERSON><PERSON>", "getConfigLabels", "next", "error", "uClick", "index", "onSelectionChange", "data", "selected<PERSON><PERSON><PERSON>", "onQuickActionClick", "action", "console", "log", "basePillStyle", "padding", "height", "display", "border", "tab", "getAllReviewTools", "userToolReviewDetails", "event", "goToAnalytics", "ɵɵdirectiveInject", "i1", "Router", "i2", "SharedApiServiceService", "i3", "ApprovalService", "selectors", "decls", "vars", "consts", "template", "DashboardComponent_Template", "rf", "ctx", "ɵɵtemplate", "DashboardComponent_div_6_Template", "ɵɵtext", "DashboardComponent_Template_ava_dropdown_selectionChange_13_listener", "DashboardComponent_div_17_Template", "DashboardComponent_ava_tag_25_Template", "ɵɵrepeaterCreate", "DashboardComponent_For_28_Template", "ɵɵrepeaterTrackByIndex", "ɵɵrepeater", "i4", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\console\\src\\app\\pages\\dashboard\\dashboard.component.ts", "C:\\console\\aava-ui-web\\projects\\console\\src\\app\\pages\\dashboard\\dashboard.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule, formatDate } from '@angular/common';\r\nimport { RouterModule } from '@angular/router';\r\nimport { Router } from '@angular/router';\r\nimport { SharedApiServiceService } from '../../shared/services/shared-api-service.service';\r\nimport {\r\n  AvaTagComponent,\r\n  DropdownComponent,\r\n  DropdownOption,\r\n  LinkComponent,\r\n} from '@ava/play-comp-library';\r\nimport {\r\n  ActivityMonitoringI,\r\n  DashboardDetailI,\r\n} from './models/dashboard.model';\r\nimport { ApprovalService } from '../../shared/services/approval.service';\r\nimport {\r\n  ACTIVE_MONITORING_OPTIONS,\r\n  ActiveMonitorings,\r\n  APIKeys,\r\n  DASHBOARD_CARD_DETAILS,\r\n} from './constants/dashoard.constant';\r\nimport { RequestStatus } from '../approval/approval.component';\r\nimport approvalText from '../approval/constants/approval.json';\r\nimport { DashboardImgCardComponent } from '../../shared/components/dashboard-img-card/dashboard-img-card.component';\r\nimport { DashboardTxtCardComponent } from '../../shared/components/dashboard-txt-card/dashboard-txt-card.component';\r\nimport { DashboardAgentMonitoringCardComponent } from '../../shared/components/dashboard-agent-monitoring-card/dashboard-agent-monitoring-card.component';\r\nimport { DashboardApprovalCardComponent } from '../../shared/components/dashboard-approval-card/dashboard-approval-card.component';\r\n\r\n// Interfaces\r\ninterface UserLog {\r\n  id: string;\r\n  username: string;\r\n  avatar: string;\r\n  securityToken: string;\r\n  status: 'Active' | 'Inactive' | 'Pending';\r\n}\r\n\r\ninterface ModelUsage {\r\n  id: string;\r\n  name: string;\r\n  publisher: {\r\n    name: string;\r\n    logo: string;\r\n  };\r\n  agentsCount: number;\r\n}\r\n\r\ninterface PendingApproval {\r\n  id: string;\r\n  name: string;\r\n  type: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-dashboard',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    RouterModule,\r\n    DropdownComponent,\r\n    AvaTagComponent,\r\n    LinkComponent,\r\n    DashboardImgCardComponent,\r\n    DashboardTxtCardComponent,\r\n    DashboardAgentMonitoringCardComponent,\r\n    DashboardApprovalCardComponent,\r\n  ],\r\n  templateUrl: './dashboard.component.html',\r\n  styleUrl: './dashboard.component.scss',\r\n})\r\nexport class DashboardComponent implements OnInit {\r\n  options: DropdownOption[] = ACTIVE_MONITORING_OPTIONS;\r\n  public labels: any = approvalText.labels;\r\n\r\n  dashboardDetails: DashboardDetailI[] = [\r\n    // {\r\n    //   icon: '',\r\n    //   title: '',\r\n    //   value: 0,\r\n    //   subtitle: '',\r\n    //   badge: '',\r\n    // },\r\n    // {\r\n    //   icon: 'trending-up',\r\n    //   title: 'Active Workflows',\r\n    //   value: 80,\r\n    //   subtitle: 'Agents actively running',\r\n    //   badge: 'S',\r\n    // },\r\n    // {\r\n    //   icon: 'trending-up',\r\n    //   title: 'Active Agents',\r\n    //   value: 1240,\r\n    //   subtitle: 'Agents active',\r\n    //   badge: 'S',\r\n    // },\r\n    // {\r\n    //   icon: 'trending-up',\r\n    //   title: 'Agents Approval',\r\n    //   value: 120,\r\n    //   subtitle: 'Agents actively running',\r\n    //   badge: 'S',\r\n    // },\r\n    // {\r\n    //   icon: 'trending-up',\r\n    //   title: 'Active Workflows',\r\n    //   value: 80,\r\n    //   subtitle: 'Agents actively running',\r\n    //   badge: 'S',\r\n    // },\r\n  ];\r\n  // Dashboard metrics\r\n  totalAgents: number = 0;\r\n  newAgentsCreated: number = 50;\r\n\r\n  totalWorkflows: number = 124;\r\n  newWorkflowsCreated: number = 50;\r\n\r\n  totalUsers: number = 300;\r\n  newUsersAdded: number = 50;\r\n\r\n  selectedActiveMonitoring = ActiveMonitorings.tools;\r\n\r\n  activityMonitoringCount = 5;\r\n\r\n  // Current user info (would come from auth service in real app)\r\n  currentUser: { name: string } = { name: 'Akash Raj' };\r\n\r\n  // Footer info\r\n  currentYear: number = new Date().getFullYear();\r\n\r\n  // Model usage data\r\n  modelUsage: ModelUsage[] = [\r\n    {\r\n      id: '1',\r\n      name: 'GPT 3',\r\n      publisher: {\r\n        name: 'Open AI',\r\n        logo: 'assets/images/logos/openai-logo.png',\r\n      },\r\n      agentsCount: 48,\r\n    },\r\n    {\r\n      id: '2',\r\n      name: 'Claude 2',\r\n      publisher: {\r\n        name: 'Anthropic',\r\n        logo: 'assets/images/logos/anthropic-logo.png',\r\n      },\r\n      agentsCount: 24,\r\n    },\r\n    {\r\n      id: '3',\r\n      name: 'Gemini',\r\n      publisher: {\r\n        name: 'Google',\r\n        logo: 'assets/images/logos/google-logo.png',\r\n      },\r\n      agentsCount: 20,\r\n    },\r\n    {\r\n      id: '4',\r\n      name: 'GPT-4',\r\n      publisher: {\r\n        name: 'Open AI',\r\n        logo: 'assets/images/logos/openai-logo.png',\r\n      },\r\n      agentsCount: 8,\r\n    },\r\n  ];\r\n\r\n  // Pending approvals\r\n  pendingApprovals: PendingApproval[] = [\r\n    {\r\n      id: '1',\r\n      name: 'Test Ruby to Springboot',\r\n      type: 'migration',\r\n    },\r\n    {\r\n      id: '2',\r\n      name: 'Customer Support Chatbot',\r\n      type: 'agent',\r\n    },\r\n    {\r\n      id: '3',\r\n      name: 'Invoice Processing & Approval',\r\n      type: 'workflow',\r\n    },\r\n    {\r\n      id: '4',\r\n      name: 'Invoice Processing & Approval',\r\n      type: 'workflow',\r\n    },\r\n    {\r\n      id: '5',\r\n      name: 'AI-Powered Code Review Assistant',\r\n      type: 'agent',\r\n    },\r\n    {\r\n      id: '6',\r\n      name: 'AI-Powered Code Review Assistant',\r\n      type: 'agent',\r\n    },\r\n    {\r\n      id: '7',\r\n      name: 'AI-Powered Code Review Assistant',\r\n      type: 'agent',\r\n    },\r\n    {\r\n      id: '8',\r\n      name: 'AI-Powered Code Review Assistant',\r\n      type: 'agent',\r\n    },\r\n  ];\r\n\r\n  activityMonitoring: ActivityMonitoringI[] = [\r\n    // {\r\n    //   agentName: 'Agent Name will be here',\r\n    //   status: 'Active',\r\n    //   user: 'Michael Scott',\r\n    //   date: '12 June 2025',\r\n    //   totalRuns: 264,\r\n    // },\r\n    // {\r\n    //   agentName: 'Agent Name will be here',\r\n    //   status: 'Active',\r\n    //   user: 'Michael Scott',\r\n    //   date: '12 June 2025',\r\n    //   totalRuns: 264,\r\n    // },\r\n    // {\r\n    //   agentName: 'Agent Name will be here',\r\n    //   status: 'Active',\r\n    //   user: 'Michael Scott',\r\n    //   date: '12 June 2025',\r\n    //   totalRuns: 264,\r\n    // },\r\n    // {\r\n    //   agentName: 'Agent Name will be here',\r\n    //   status: 'Active',\r\n    //   user: 'Michael Scott',\r\n    //   date: '12 June 2025',\r\n    //   totalRuns: 264,\r\n    // },\r\n    // {\r\n    //   agentName: 'Agent Name will be here',\r\n    //   status: 'Active',\r\n    //   user: 'Michael Scott',\r\n    //   date: '12 June 2025',\r\n    //   totalRuns: 264,\r\n    // },\r\n  ];\r\n  workflowApprovals: any[] = [];\r\n  approvalTabs: string[] = ['Agents', 'Workflow', 'Tools'];\r\n  selectedApprovalTab: string = 'Agents';\r\n  approvalData: any[] = []\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private apiService: SharedApiServiceService,\r\n    private approvalService: ApprovalService,\r\n  ) { }\r\n\r\n  // seprating the tool and agents.\r\n  activityMonitoringTools: ActivityMonitoringI[] = [];\r\n  activityMonitoringAgents: ActivityMonitoringI[] = [];\r\n\r\n  setActiveMonitoringData() {\r\n    // INsted of re-formating, just switch the values\r\n    if (this.selectedActiveMonitoring === ActiveMonitorings.tools) {\r\n      this.activityMonitoring = this.activityMonitoringTools;\r\n    } else {\r\n      this.activityMonitoring = this.activityMonitoringAgents;\r\n    }\r\n  }\r\n\r\n  mapToActivityMonitoringItem = (\r\n    item: any,\r\n    nameKey: APIKeys,\r\n    usageKey: APIKeys,\r\n  ): ActivityMonitoringI => ({\r\n    agentName: item[nameKey] || '',\r\n    totalRuns: Number(item[usageKey]) || 0,\r\n    status: '',\r\n    user: '',\r\n    date: '',\r\n  });\r\n\r\n  public toRequestStatus(value: string | null | undefined): RequestStatus {\r\n    return value === 'approved' || value === 'rejected' || value === 'review'\r\n      ? value\r\n      : 'review';\r\n  }\r\n\r\n  initApiCalls() {\r\n    const date = new Date();\r\n    const dateEnd = this.apiService.formatDate(date);\r\n    date.setDate(1);\r\n    const dateStart = this.apiService.formatDate(date);\r\n    this.apiService\r\n      .getCollabrativeAgentAnalytics(dateStart, dateEnd)\r\n      .subscribe((response: Record<string, any>) => {\r\n        // setting dashboard card values\r\n        this.dashboardDetails = DASHBOARD_CARD_DETAILS.map((cardDetail) => {\r\n          cardDetail.value =\r\n            (response[cardDetail.field] as number) || this.totalAgents;\r\n          return cardDetail as DashboardDetailI;\r\n        });\r\n\r\n        // Active Monitoring\r\n        // Extracting tools and agents to seprate varibales to reduce frequent re-formatig as the dropdown value changes\r\n        this.activityMonitoringTools = (response[APIKeys.toolUsage] as any[])\r\n          .slice(0, this.activityMonitoringCount)\r\n          .map((toolUsage) =>\r\n            this.mapToActivityMonitoringItem(\r\n              toolUsage,\r\n              APIKeys.toolName,\r\n              APIKeys.usageCount,\r\n            ),\r\n          );\r\n\r\n        this.activityMonitoringAgents = (\r\n          response[APIKeys.agentMetrics] as any[]\r\n        )\r\n          .slice(0, this.activityMonitoringCount)\r\n          .map((agentMetric) =>\r\n            this.mapToActivityMonitoringItem(\r\n              agentMetric,\r\n              APIKeys.agentName,\r\n              APIKeys.workflowCount,\r\n            ),\r\n          );\r\n\r\n        this.setActiveMonitoringData();\r\n      });\r\n\r\n    this.approvalService.getAllReviewAgents(1, 100, false).subscribe((res) => {\r\n      const agentReviewDetails = res?.agentReviewDetails || [];\r\n      // Extracting agents which are under review\r\n      this.totalAgents =\r\n        agentReviewDetails.filter(\r\n          (agentReviewDetail: any) => agentReviewDetail.status !== 'approved',\r\n        )?.length || 0;\r\n\r\n      // If this API call is late then will set approval count here.\r\n      const toolCardDetial = this.dashboardDetails.at(-1);\r\n      if (toolCardDetial) {\r\n        toolCardDetial.value = this.totalAgents;\r\n      }\r\n    });\r\n\r\n    this.approvalService\r\n      .getAllReviewWorkflows(1, 5, false)\r\n      .subscribe((response) => {\r\n        const type = 'workflow';\r\n\r\n        this.workflowApprovals = response.workflowReviewDetails?.map(\r\n          (req: any) => {\r\n            const statusIcons: Record<RequestStatus, string> = {\r\n              approved: 'circle-check-big',\r\n              rejected: 'circle-x',\r\n              review: 'clock',\r\n            };\r\n            const statusTexts: Record<RequestStatus, string> = {\r\n              approved: this.labels.approved,\r\n              rejected: this.labels.rejected,\r\n              review: this.labels.review,\r\n            };\r\n            const statusKey = this.toRequestStatus(req?.status);\r\n            let specificId = 0;\r\n            let title = '';\r\n\r\n            specificId = req.workflowId;\r\n            title = req.workflowName;\r\n\r\n            return {\r\n              id: req.id,\r\n              refId: specificId,\r\n              type: type,\r\n              session1: {\r\n                title: title,\r\n                labels: [\r\n                  {\r\n                    name: type,\r\n                    color: 'success',\r\n                    background: 'red',\r\n                    type: 'normal',\r\n                  },\r\n                  {\r\n                    name: req.changeRequestType,\r\n                    color:\r\n                      req.changeRequestType === 'update' ? 'error' : 'info',\r\n                    background: 'red',\r\n                    type: 'pill',\r\n                  },\r\n                ],\r\n              },\r\n              session2: [\r\n                {\r\n                  name: type,\r\n                  color: 'default',\r\n                  background: 'red',\r\n                  type: 'normal',\r\n                },\r\n                {\r\n                  name: req.status,\r\n                  color: 'default',\r\n                  background: 'red',\r\n                  type: 'normal',\r\n                },\r\n              ],\r\n              session3: [\r\n                {\r\n                  iconName: 'user',\r\n                  label: req.requestedBy,\r\n                },\r\n                {\r\n                  iconName: 'calendar-days',\r\n                  label: formatDate(req?.requestedAt, 'dd MMM yyyy', 'en-IN'),\r\n                },\r\n              ],\r\n              session4: {\r\n                status: statusTexts[statusKey],\r\n                iconName: statusIcons[statusKey],\r\n              },\r\n            };\r\n          },\r\n        );\r\n      });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    // The layout is now managed with fixed heights in CSS\r\n    // No need for recalculateLayout\r\n\r\n    this.initApiCalls();\r\n    this.getAllReviewAgents();\r\n  }\r\n\r\n  // Navigate to a route\r\n  navigateTo(route: string): void {\r\n    this.router.navigate([route]);\r\n  }\r\n\r\n  // Test method to demonstrate loader functionality\r\n  testLoader(): void {\r\n    this.apiService.getConfigLabels().subscribe({\r\n      next: (response) => {},\r\n      error: (error) => {},\r\n    });\r\n  }\r\n\r\n  uClick(index: any) {\r\n    this.router.navigate(['/approval']);\r\n  }\r\n\r\n  onSelectionChange(data: any) {\r\n    this.selectedActiveMonitoring = data.selectedValue;\r\n    this.setActiveMonitoringData();\r\n  }\r\n\r\n  onQuickActionClick(action: any) {\r\n    console.log('Quick action clicked:', action);\r\n    // Handle navigation or action based on action.id\r\n    switch (action.id) {\r\n      case 'build-agent':\r\n        this.router.navigate(['/build/agents/create']);\r\n        break;\r\n      case 'build-workflow':\r\n        this.router.navigate(['/build/workflows/create']);\r\n        break;\r\n      case 'create-prompt':\r\n        this.router.navigate(['/libraries/prompts/create']);\r\n        break;\r\n      case 'create-tool':\r\n        this.router.navigate(['/libraries/tools/create']);\r\n        break;\r\n      case 'create-guardrail':\r\n        this.router.navigate(['/libraries/guardrails/create']);\r\n        break;\r\n      case 'create-knowledge-base':\r\n        this.router.navigate(['/libraries/knowledge-base/create']);\r\n        break;\r\n      default:\r\n        console.log('Unknown action:', action.id);\r\n    }\r\n  }\r\n\r\n  // Common pill base style\r\n  private readonly basePillStyle: Record<string, string> = {\r\n    'border-radius': '20px',\r\n    padding: '0px 20px',\r\n    height: '32px',\r\n    display: 'flex',\r\n    'justify-content': 'center',\r\n  };\r\n\r\n  // Custom pill style for Agent Approvals card\r\n  agentApprovalsPillStyle: Record<string, string> = {\r\n    ...this.basePillStyle,\r\n    'background-color': '#fff',\r\n    color: '#2D3036',\r\n    border: '1px solid #2563EB',\r\n  };\r\n\r\n  // Selected state pill style\r\n  agentApprovalsSelectedPillStyle: Record<string, string> = {\r\n    ...this.basePillStyle,\r\n    'background-color': '#2563EB',\r\n    color: '#FFFFFF',\r\n  };\r\n\r\n  updatedSelectedApprovalTab(tab: string) {\r\n    this.selectedApprovalTab = tab;\r\n    if (tab == 'Agents') {\r\n      this.getAllReviewAgents();\r\n    } else if (tab == 'Workflow') {\r\n      this.getAllReviewWorkflows();\r\n    } else if (tab == 'Tools') {\r\n      this.getAllReviewTools();\r\n    }\r\n  }\r\n\r\n  getAllReviewAgents() {\r\n    this.approvalService.getAllReviewAgents(1, 3, false).subscribe((response) => {\r\n      this.approvalData = (response?.agentReviewDetails || []).map((item: any) => ({\r\n        id: item.id,\r\n        title: item.agentName,\r\n        requestedBy: item.requestedBy,\r\n        requestedAt: formatDate(item.requestedAt, 'dd MMM yyyy', 'en-IN'),\r\n        type: 'agent',\r\n        rawData: item,\r\n      }));\r\n    });\r\n  }\r\n\r\n  getAllReviewWorkflows() {\r\n    this.approvalService.getAllReviewWorkflows(1, 3, false).subscribe((response) => {\r\n      this.approvalData = (response?.workflowReviewDetails || []).map((item: any) => ({\r\n        title: item.workflowName,\r\n        requestedBy: item.requestedBy,\r\n        requestedAt: formatDate(item.requestedAt, 'dd MMM yyyy', 'en-IN'),\r\n        type: 'workflow',\r\n        rawData: item,\r\n      }));\r\n    });\r\n  }\r\n\r\n  getAllReviewTools() {\r\n    this.approvalService.getAllReviewTools(1, 3, false).subscribe((response) => {\r\n      this.approvalData = (response?.userToolReviewDetails || []).map((item: any) => ({\r\n        title: item.toolName,\r\n        requestedBy: item.requestedBy,\r\n        requestedAt: formatDate(item.requestedAt, 'dd MMM yyyy', 'en-IN'),\r\n        type: 'tool',\r\n        rawData: item,\r\n      }));\r\n    });\r\n  }\r\n\r\n  approveItem( item:any) {\r\n    // let approvalCall$;\r\n    // switch (type) {\r\n    //   case 'agent':\r\n    //     approvalCall$ = this.approvalService.approveAgent(item.id, entityId, status, reviewedBy);\r\n    //     break;\r\n    //   case 'workflow':\r\n    //     approvalCall$ = this.approvalService.approveWorkflow(id, entityId, status, reviewedBy);\r\n    //     break;\r\n    //   case 'tool':\r\n    //     approvalCall$ = this.approvalService.approveTool(id, entityId, status, reviewedBy);\r\n    //     break;\r\n    //   default:\r\n    //     console.error(`Unknown approval type: ${type}`);\r\n    //     return;\r\n    // }\r\n\r\n    // approvalCall$.subscribe(() => {\r\n    //   console.log(`${type} approved successfully`);\r\n    //   // optionally refresh the data here\r\n    // });\r\n  }\r\n\r\n  onTestClick(event: any){}\r\n\r\n  onSendBackClick(event: any){}\r\n\r\n  goToAnalytics() {\r\n    this.router.navigate(['/console/analytics']);\r\n  }\r\n}\r\n", "<div id=\"dasboard-container\" class=\"container-fluid\">\r\n  <div id=\"dashboard-cards-container\" class=\"row\">\r\n    <div class=\"col-md-8 d-flex flex-column gap-2\">\r\n      <div class=\"d-flex flex-column\">\r\n        <app-dashboard-img-card></app-dashboard-img-card>\r\n        <div class=\"d-flex flex-wrap txt-card-wrapper\">\r\n          <div *ngFor=\"let details of dashboardDetails\" class=\"col-3\">\r\n            <app-dashboard-txt-card\r\n              [title]=\"details.title\"\r\n              [value]=\"details.value\"\r\n              [subtitle]=\"details.subtitle\"\r\n            >\r\n            </app-dashboard-txt-card>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div id=\"activity-monitoring-container\" class=\"col-4\">\r\n      <div class=\"active-monitoring-wrapper\">\r\n        <div class=\"row active-monitoring\">\r\n          <div class=\"col-5 active-monitoring-text\">Active Monitoring</div>\r\n          <div class=\"col-5\">\r\n            <ava-dropdown\r\n              class=\"a-m-dropdown\"\r\n              dropdownTitle=\"Agents\"\r\n              [options]=\"options\"\r\n              [search]=\"false\"\r\n              (selectionChange)=\"onSelectionChange($event)\"\r\n            >\r\n            </ava-dropdown>\r\n          </div>\r\n          <div class=\"col-2\">\r\n            <ava-link label=\"View All\" color=\"#3B3F46\"></ava-link>\r\n          </div>\r\n        </div>\r\n        <div class=\"d-flex flex-column gap-4 active-monitoring-list\">\r\n          <div *ngFor=\"let activity of activityMonitoring\">\r\n            <app-dashboard-agent-monitoring-card\r\n              [activity]=\"activity\"\r\n            ></app-dashboard-agent-monitoring-card>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n  <div id=\"dasborad-bottom-container\" class=\"row\">\r\n    <div id=\"high-prioirty-container\" class=\"col-12\">\r\n      <div class=\"box-wrapper high-priority-wrapper p-4\">\r\n        <div class=\"col-12 agent-approvals-cards-header header-section mb-4\">\r\n          <span> Approvals </span>\r\n          <div class=\"d-flex gap-2\">\r\n            <ava-tag\r\n              *ngFor=\"let tab of approvalTabs\"\r\n              [label]=\"tab\"\r\n              [pill]=\"true\"\r\n              color=\"custom\"\r\n              (clicked)=\"updatedSelectedApprovalTab(tab)\"\r\n              [customStyle]=\"\r\n                selectedApprovalTab === tab\r\n                  ? agentApprovalsSelectedPillStyle\r\n                  : agentApprovalsPillStyle\r\n              \"\r\n            >\r\n            </ava-tag>\r\n          </div>\r\n        </div>\r\n        <div class=\"row g-2 overflow-y-scroll\">\r\n          @for (item of approvalData; track $index) {\r\n            <div class=\"col-12 col-md-4\">\r\n              <app-dashboard-approval-card\r\n                [title]=\"item.title\"\r\n                [email]=\"item.requestedBy\"\r\n                [date]=\"item.requestedAt\"\r\n                [type]=\"item.type\"\r\n                [id]=\"item.id\"\r\n                [rowData]=\"item.rawData\"\r\n                (testClick)=\"onTestClick($event)\"\r\n                (sendBackClick)=\"onSendBackClick($event)\"\r\n                (approveClick)=\"approveItem($event)\"\r\n              >\r\n              </app-dashboard-approval-card>\r\n            </div>\r\n          }\r\n        </div>\r\n        <div class=\"agent-approvals-cards-footer d-flex justify-content-end\">\r\n          <ava-link label=\"View All\" color=\"#3B3F46\"></ava-link>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AACA,SAASA,YAAY,EAAEC,UAAU,QAAQ,iBAAiB;AAC1D,SAASC,YAAY,QAAQ,iBAAiB;AAG9C,SACEC,eAAe,EACfC,iBAAiB,EAEjBC,aAAa,QACR,wBAAwB;AAM/B,SACEC,yBAAyB,EACzBC,iBAAiB,EACjBC,OAAO,EACPC,sBAAsB,QACjB,+BAA+B;AAEtC,OAAOC,YAAY,MAAM,qCAAqC;AAC9D,SAASC,yBAAyB,QAAQ,yEAAyE;AACnH,SAASC,yBAAyB,QAAQ,yEAAyE;AACnH,SAASC,qCAAqC,QAAQ,mGAAmG;AACzJ,SAASC,8BAA8B,QAAQ,mFAAmF;;;;;;;;ICrBxHC,EAAA,CAAAC,cAAA,cAA4D;IAC1DD,EAAA,CAAAE,SAAA,iCAKyB;IAC3BF,EAAA,CAAAG,YAAA,EAAM;;;;IALFH,EAAA,CAAAI,SAAA,EAAuB;IAEvBJ,EAFA,CAAAK,UAAA,UAAAC,UAAA,CAAAC,KAAA,CAAuB,UAAAD,UAAA,CAAAE,KAAA,CACA,aAAAF,UAAA,CAAAG,QAAA,CACM;;;;;IA0BjCT,EAAA,CAAAC,cAAA,UAAiD;IAC/CD,EAAA,CAAAE,SAAA,8CAEuC;IACzCF,EAAA,CAAAG,YAAA,EAAM;;;;IAFFH,EAAA,CAAAI,SAAA,EAAqB;IAArBJ,EAAA,CAAAK,UAAA,aAAAK,WAAA,CAAqB;;;;;;IAavBV,EAAA,CAAAC,cAAA,kBAWC;IANCD,EAAA,CAAAW,UAAA,qBAAAC,kEAAA;MAAA,MAAAC,MAAA,GAAAb,EAAA,CAAAc,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAAWF,MAAA,CAAAG,0BAAA,CAAAP,MAAA,CAA+B;IAAA,EAAC;IAO7Cb,EAAA,CAAAG,YAAA,EAAU;;;;;IANRH,EAJA,CAAAK,UAAA,UAAAQ,MAAA,CAAa,cACA,gBAAAI,MAAA,CAAAI,mBAAA,KAAAR,MAAA,GAAAI,MAAA,CAAAK,+BAAA,GAAAL,MAAA,CAAAM,uBAAA,CAOZ;;;;;;IAQDvB,EADF,CAAAC,cAAA,cAA6B,sCAW1B;IADCD,EAFA,CAAAW,UAAA,uBAAAa,oFAAAC,MAAA;MAAAzB,EAAA,CAAAc,aAAA,CAAAY,GAAA;MAAA,MAAAT,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAAaF,MAAA,CAAAU,WAAA,CAAAF,MAAA,CAAmB;IAAA,EAAC,2BAAAG,wFAAAH,MAAA;MAAAzB,EAAA,CAAAc,aAAA,CAAAY,GAAA;MAAA,MAAAT,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAChBF,MAAA,CAAAY,eAAA,CAAAJ,MAAA,CAAuB;IAAA,EAAC,0BAAAK,uFAAAL,MAAA;MAAAzB,EAAA,CAAAc,aAAA,CAAAY,GAAA;MAAA,MAAAT,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CACzBF,MAAA,CAAAc,WAAA,CAAAN,MAAA,CAAmB;IAAA,EAAC;IAGxCzB,EADE,CAAAG,YAAA,EAA8B,EAC1B;;;;IAXFH,EAAA,CAAAI,SAAA,EAAoB;IAKpBJ,EALA,CAAAK,UAAA,UAAA2B,OAAA,CAAAzB,KAAA,CAAoB,UAAAyB,OAAA,CAAAC,WAAA,CACM,SAAAD,OAAA,CAAAE,WAAA,CACD,SAAAF,OAAA,CAAAG,IAAA,CACP,OAAAH,OAAA,CAAAI,EAAA,CACJ,YAAAJ,OAAA,CAAAK,OAAA,CACU;;;ADJxC,WAAaC,kBAAkB;EAAzB,MAAOA,kBAAkB;IA4LnBC,MAAA;IACAC,UAAA;IACAC,eAAA;IA7LVC,OAAO,GAAqBnD,yBAAyB;IAC9CoD,MAAM,GAAQhD,YAAY,CAACgD,MAAM;IAExCC,gBAAgB,GAAuB;MACrC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IAAA,CACD;IACD;IACAC,WAAW,GAAW,CAAC;IACvBC,gBAAgB,GAAW,EAAE;IAE7BC,cAAc,GAAW,GAAG;IAC5BC,mBAAmB,GAAW,EAAE;IAEhCC,UAAU,GAAW,GAAG;IACxBC,aAAa,GAAW,EAAE;IAE1BC,wBAAwB,GAAG3D,iBAAiB,CAAC4D,KAAK;IAElDC,uBAAuB,GAAG,CAAC;IAE3B;IACAC,WAAW,GAAqB;MAAEC,IAAI,EAAE;IAAW,CAAE;IAErD;IACAC,WAAW,GAAW,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE;IAE9C;IACAC,UAAU,GAAiB,CACzB;MACEvB,EAAE,EAAE,GAAG;MACPmB,IAAI,EAAE,OAAO;MACbK,SAAS,EAAE;QACTL,IAAI,EAAE,SAAS;QACfM,IAAI,EAAE;OACP;MACDC,WAAW,EAAE;KACd,EACD;MACE1B,EAAE,EAAE,GAAG;MACPmB,IAAI,EAAE,UAAU;MAChBK,SAAS,EAAE;QACTL,IAAI,EAAE,WAAW;QACjBM,IAAI,EAAE;OACP;MACDC,WAAW,EAAE;KACd,EACD;MACE1B,EAAE,EAAE,GAAG;MACPmB,IAAI,EAAE,QAAQ;MACdK,SAAS,EAAE;QACTL,IAAI,EAAE,QAAQ;QACdM,IAAI,EAAE;OACP;MACDC,WAAW,EAAE;KACd,EACD;MACE1B,EAAE,EAAE,GAAG;MACPmB,IAAI,EAAE,OAAO;MACbK,SAAS,EAAE;QACTL,IAAI,EAAE,SAAS;QACfM,IAAI,EAAE;OACP;MACDC,WAAW,EAAE;KACd,CACF;IAED;IACAC,gBAAgB,GAAsB,CACpC;MACE3B,EAAE,EAAE,GAAG;MACPmB,IAAI,EAAE,yBAAyB;MAC/BpB,IAAI,EAAE;KACP,EACD;MACEC,EAAE,EAAE,GAAG;MACPmB,IAAI,EAAE,0BAA0B;MAChCpB,IAAI,EAAE;KACP,EACD;MACEC,EAAE,EAAE,GAAG;MACPmB,IAAI,EAAE,+BAA+B;MACrCpB,IAAI,EAAE;KACP,EACD;MACEC,EAAE,EAAE,GAAG;MACPmB,IAAI,EAAE,+BAA+B;MACrCpB,IAAI,EAAE;KACP,EACD;MACEC,EAAE,EAAE,GAAG;MACPmB,IAAI,EAAE,kCAAkC;MACxCpB,IAAI,EAAE;KACP,EACD;MACEC,EAAE,EAAE,GAAG;MACPmB,IAAI,EAAE,kCAAkC;MACxCpB,IAAI,EAAE;KACP,EACD;MACEC,EAAE,EAAE,GAAG;MACPmB,IAAI,EAAE,kCAAkC;MACxCpB,IAAI,EAAE;KACP,EACD;MACEC,EAAE,EAAE,GAAG;MACPmB,IAAI,EAAE,kCAAkC;MACxCpB,IAAI,EAAE;KACP,CACF;IAED6B,kBAAkB,GAA0B;MAC1C;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IAAA,CACD;IACDC,iBAAiB,GAAU,EAAE;IAC7BC,YAAY,GAAa,CAAC,QAAQ,EAAE,UAAU,EAAE,OAAO,CAAC;IACxD7C,mBAAmB,GAAW,QAAQ;IACtC8C,YAAY,GAAU,EAAE;IAExBC,YACU7B,MAAc,EACdC,UAAmC,EACnCC,eAAgC;MAFhC,KAAAF,MAAM,GAANA,MAAM;MACN,KAAAC,UAAU,GAAVA,UAAU;MACV,KAAAC,eAAe,GAAfA,eAAe;IACrB;IAEJ;IACA4B,uBAAuB,GAA0B,EAAE;IACnDC,wBAAwB,GAA0B,EAAE;IAEpDC,uBAAuBA,CAAA;MACrB;MACA,IAAI,IAAI,CAACpB,wBAAwB,KAAK3D,iBAAiB,CAAC4D,KAAK,EAAE;QAC7D,IAAI,CAACY,kBAAkB,GAAG,IAAI,CAACK,uBAAuB;MACxD,CAAC,MAAM;QACL,IAAI,CAACL,kBAAkB,GAAG,IAAI,CAACM,wBAAwB;MACzD;IACF;IAEAE,2BAA2B,GAAGA,CAC5BC,IAAS,EACTC,OAAgB,EAChBC,QAAiB,MACQ;MACzBC,SAAS,EAAEH,IAAI,CAACC,OAAO,CAAC,IAAI,EAAE;MAC9BG,SAAS,EAAEC,MAAM,CAACL,IAAI,CAACE,QAAQ,CAAC,CAAC,IAAI,CAAC;MACtCI,MAAM,EAAE,EAAE;MACVC,IAAI,EAAE,EAAE;MACRC,IAAI,EAAE;KACP,CAAC;IAEKC,eAAeA,CAAC1E,KAAgC;MACrD,OAAOA,KAAK,KAAK,UAAU,IAAIA,KAAK,KAAK,UAAU,IAAIA,KAAK,KAAK,QAAQ,GACrEA,KAAK,GACL,QAAQ;IACd;IAEA2E,YAAYA,CAAA;MACV,MAAMF,IAAI,GAAG,IAAIxB,IAAI,EAAE;MACvB,MAAM2B,OAAO,GAAG,IAAI,CAAC5C,UAAU,CAACtD,UAAU,CAAC+F,IAAI,CAAC;MAChDA,IAAI,CAACI,OAAO,CAAC,CAAC,CAAC;MACf,MAAMC,SAAS,GAAG,IAAI,CAAC9C,UAAU,CAACtD,UAAU,CAAC+F,IAAI,CAAC;MAClD,IAAI,CAACzC,UAAU,CACZ+C,6BAA6B,CAACD,SAAS,EAAEF,OAAO,CAAC,CACjDI,SAAS,CAAEC,QAA6B,IAAI;QAC3C;QACA,IAAI,CAAC7C,gBAAgB,GAAGlD,sBAAsB,CAACgG,GAAG,CAAEC,UAAU,IAAI;UAChEA,UAAU,CAACnF,KAAK,GACbiF,QAAQ,CAACE,UAAU,CAACC,KAAK,CAAY,IAAI,IAAI,CAAC/C,WAAW;UAC5D,OAAO8C,UAA8B;QACvC,CAAC,CAAC;QAEF;QACA;QACA,IAAI,CAACtB,uBAAuB,GAAIoB,QAAQ,CAAChG,OAAO,CAACoG,SAAS,CAAW,CAClEC,KAAK,CAAC,CAAC,EAAE,IAAI,CAACzC,uBAAuB,CAAC,CACtCqC,GAAG,CAAEG,SAAS,IACb,IAAI,CAACrB,2BAA2B,CAC9BqB,SAAS,EACTpG,OAAO,CAACsG,QAAQ,EAChBtG,OAAO,CAACuG,UAAU,CACnB,CACF;QAEH,IAAI,CAAC1B,wBAAwB,GAC3BmB,QAAQ,CAAChG,OAAO,CAACwG,YAAY,CAC9B,CACEH,KAAK,CAAC,CAAC,EAAE,IAAI,CAACzC,uBAAuB,CAAC,CACtCqC,GAAG,CAAEQ,WAAW,IACf,IAAI,CAAC1B,2BAA2B,CAC9B0B,WAAW,EACXzG,OAAO,CAACmF,SAAS,EACjBnF,OAAO,CAAC0G,aAAa,CACtB,CACF;QAEH,IAAI,CAAC5B,uBAAuB,EAAE;MAChC,CAAC,CAAC;MAEJ,IAAI,CAAC9B,eAAe,CAAC2D,kBAAkB,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAACZ,SAAS,CAAEa,GAAG,IAAI;QACvE,MAAMC,kBAAkB,GAAGD,GAAG,EAAEC,kBAAkB,IAAI,EAAE;QACxD;QACA,IAAI,CAACzD,WAAW,GACdyD,kBAAkB,CAACC,MAAM,CACtBC,iBAAsB,IAAKA,iBAAiB,CAACzB,MAAM,KAAK,UAAU,CACpE,EAAE0B,MAAM,IAAI,CAAC;QAEhB;QACA,MAAMC,cAAc,GAAG,IAAI,CAAC9D,gBAAgB,CAAC+D,EAAE,CAAC,CAAC,CAAC,CAAC;QACnD,IAAID,cAAc,EAAE;UAClBA,cAAc,CAAClG,KAAK,GAAG,IAAI,CAACqC,WAAW;QACzC;MACF,CAAC,CAAC;MAEF,IAAI,CAACJ,eAAe,CACjBmE,qBAAqB,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAClCpB,SAAS,CAAEC,QAAQ,IAAI;QACtB,MAAMtD,IAAI,GAAG,UAAU;QAEvB,IAAI,CAAC8B,iBAAiB,GAAGwB,QAAQ,CAACoB,qBAAqB,EAAEnB,GAAG,CACzDoB,GAAQ,IAAI;UACX,MAAMC,WAAW,GAAkC;YACjDC,QAAQ,EAAE,kBAAkB;YAC5BC,QAAQ,EAAE,UAAU;YACpBC,MAAM,EAAE;WACT;UACD,MAAMC,WAAW,GAAkC;YACjDH,QAAQ,EAAE,IAAI,CAACrE,MAAM,CAACqE,QAAQ;YAC9BC,QAAQ,EAAE,IAAI,CAACtE,MAAM,CAACsE,QAAQ;YAC9BC,MAAM,EAAE,IAAI,CAACvE,MAAM,CAACuE;WACrB;UACD,MAAME,SAAS,GAAG,IAAI,CAAClC,eAAe,CAAC4B,GAAG,EAAE/B,MAAM,CAAC;UACnD,IAAIsC,UAAU,GAAG,CAAC;UAClB,IAAI9G,KAAK,GAAG,EAAE;UAEd8G,UAAU,GAAGP,GAAG,CAACQ,UAAU;UAC3B/G,KAAK,GAAGuG,GAAG,CAACS,YAAY;UAExB,OAAO;YACLnF,EAAE,EAAE0E,GAAG,CAAC1E,EAAE;YACVoF,KAAK,EAAEH,UAAU;YACjBlF,IAAI,EAAEA,IAAI;YACVsF,QAAQ,EAAE;cACRlH,KAAK,EAAEA,KAAK;cACZoC,MAAM,EAAE,CACN;gBACEY,IAAI,EAAEpB,IAAI;gBACVuF,KAAK,EAAE,SAAS;gBAChBC,UAAU,EAAE,KAAK;gBACjBxF,IAAI,EAAE;eACP,EACD;gBACEoB,IAAI,EAAEuD,GAAG,CAACc,iBAAiB;gBAC3BF,KAAK,EACHZ,GAAG,CAACc,iBAAiB,KAAK,QAAQ,GAAG,OAAO,GAAG,MAAM;gBACvDD,UAAU,EAAE,KAAK;gBACjBxF,IAAI,EAAE;eACP;aAEJ;YACD0F,QAAQ,EAAE,CACR;cACEtE,IAAI,EAAEpB,IAAI;cACVuF,KAAK,EAAE,SAAS;cAChBC,UAAU,EAAE,KAAK;cACjBxF,IAAI,EAAE;aACP,EACD;cACEoB,IAAI,EAAEuD,GAAG,CAAC/B,MAAM;cAChB2C,KAAK,EAAE,SAAS;cAChBC,UAAU,EAAE,KAAK;cACjBxF,IAAI,EAAE;aACP,CACF;YACD2F,QAAQ,EAAE,CACR;cACEC,QAAQ,EAAE,MAAM;cAChBC,KAAK,EAAElB,GAAG,CAAC7E;aACZ,EACD;cACE8F,QAAQ,EAAE,eAAe;cACzBC,KAAK,EAAE9I,UAAU,CAAC4H,GAAG,EAAE5E,WAAW,EAAE,aAAa,EAAE,OAAO;aAC3D,CACF;YACD+F,QAAQ,EAAE;cACRlD,MAAM,EAAEoC,WAAW,CAACC,SAAS,CAAC;cAC9BW,QAAQ,EAAEhB,WAAW,CAACK,SAAS;;WAElC;QACH,CAAC,CACF;MACH,CAAC,CAAC;IACN;IAEAc,QAAQA,CAAA;MACN;MACA;MAEA,IAAI,CAAC/C,YAAY,EAAE;MACnB,IAAI,CAACiB,kBAAkB,EAAE;IAC3B;IAEA;IACA+B,UAAUA,CAACC,KAAa;MACtB,IAAI,CAAC7F,MAAM,CAAC8F,QAAQ,CAAC,CAACD,KAAK,CAAC,CAAC;IAC/B;IAEA;IACAE,UAAUA,CAAA;MACR,IAAI,CAAC9F,UAAU,CAAC+F,eAAe,EAAE,CAAC/C,SAAS,CAAC;QAC1CgD,IAAI,EAAG/C,QAAQ,IAAI,CAAE,CAAC;QACtBgD,KAAK,EAAGA,KAAK,IAAI,CAAE;OACpB,CAAC;IACJ;IAEAC,MAAMA,CAACC,KAAU;MACf,IAAI,CAACpG,MAAM,CAAC8F,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;IACrC;IAEAO,iBAAiBA,CAACC,IAAS;MACzB,IAAI,CAAC1F,wBAAwB,GAAG0F,IAAI,CAACC,aAAa;MAClD,IAAI,CAACvE,uBAAuB,EAAE;IAChC;IAEAwE,kBAAkBA,CAACC,MAAW;MAC5BC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEF,MAAM,CAAC;MAC5C;MACA,QAAQA,MAAM,CAAC5G,EAAE;QACf,KAAK,aAAa;UAChB,IAAI,CAACG,MAAM,CAAC8F,QAAQ,CAAC,CAAC,sBAAsB,CAAC,CAAC;UAC9C;QACF,KAAK,gBAAgB;UACnB,IAAI,CAAC9F,MAAM,CAAC8F,QAAQ,CAAC,CAAC,yBAAyB,CAAC,CAAC;UACjD;QACF,KAAK,eAAe;UAClB,IAAI,CAAC9F,MAAM,CAAC8F,QAAQ,CAAC,CAAC,2BAA2B,CAAC,CAAC;UACnD;QACF,KAAK,aAAa;UAChB,IAAI,CAAC9F,MAAM,CAAC8F,QAAQ,CAAC,CAAC,yBAAyB,CAAC,CAAC;UACjD;QACF,KAAK,kBAAkB;UACrB,IAAI,CAAC9F,MAAM,CAAC8F,QAAQ,CAAC,CAAC,8BAA8B,CAAC,CAAC;UACtD;QACF,KAAK,uBAAuB;UAC1B,IAAI,CAAC9F,MAAM,CAAC8F,QAAQ,CAAC,CAAC,kCAAkC,CAAC,CAAC;UAC1D;QACF;UACEY,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEF,MAAM,CAAC5G,EAAE,CAAC;MAC7C;IACF;IAEA;IACiB+G,aAAa,GAA2B;MACvD,eAAe,EAAE,MAAM;MACvBC,OAAO,EAAE,UAAU;MACnBC,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE,MAAM;MACf,iBAAiB,EAAE;KACpB;IAED;IACA/H,uBAAuB,GAA2B;MAChD,GAAG,IAAI,CAAC4H,aAAa;MACrB,kBAAkB,EAAE,MAAM;MAC1BzB,KAAK,EAAE,SAAS;MAChB6B,MAAM,EAAE;KACT;IAED;IACAjI,+BAA+B,GAA2B;MACxD,GAAG,IAAI,CAAC6H,aAAa;MACrB,kBAAkB,EAAE,SAAS;MAC7BzB,KAAK,EAAE;KACR;IAEDtG,0BAA0BA,CAACoI,GAAW;MACpC,IAAI,CAACnI,mBAAmB,GAAGmI,GAAG;MAC9B,IAAIA,GAAG,IAAI,QAAQ,EAAE;QACnB,IAAI,CAACpD,kBAAkB,EAAE;MAC3B,CAAC,MAAM,IAAIoD,GAAG,IAAI,UAAU,EAAE;QAC5B,IAAI,CAAC5C,qBAAqB,EAAE;MAC9B,CAAC,MAAM,IAAI4C,GAAG,IAAI,OAAO,EAAE;QACzB,IAAI,CAACC,iBAAiB,EAAE;MAC1B;IACF;IAEArD,kBAAkBA,CAAA;MAChB,IAAI,CAAC3D,eAAe,CAAC2D,kBAAkB,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAACZ,SAAS,CAAEC,QAAQ,IAAI;QAC1E,IAAI,CAACtB,YAAY,GAAG,CAACsB,QAAQ,EAAEa,kBAAkB,IAAI,EAAE,EAAEZ,GAAG,CAAEjB,IAAS,KAAM;UAC3ErC,EAAE,EAAEqC,IAAI,CAACrC,EAAE;UACX7B,KAAK,EAAEkE,IAAI,CAACG,SAAS;UACrB3C,WAAW,EAAEwC,IAAI,CAACxC,WAAW;UAC7BC,WAAW,EAAEhD,UAAU,CAACuF,IAAI,CAACvC,WAAW,EAAE,aAAa,EAAE,OAAO,CAAC;UACjEC,IAAI,EAAE,OAAO;UACbE,OAAO,EAAEoC;SACV,CAAC,CAAC;MACL,CAAC,CAAC;IACJ;IAEAmC,qBAAqBA,CAAA;MACnB,IAAI,CAACnE,eAAe,CAACmE,qBAAqB,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAACpB,SAAS,CAAEC,QAAQ,IAAI;QAC7E,IAAI,CAACtB,YAAY,GAAG,CAACsB,QAAQ,EAAEoB,qBAAqB,IAAI,EAAE,EAAEnB,GAAG,CAAEjB,IAAS,KAAM;UAC9ElE,KAAK,EAAEkE,IAAI,CAAC8C,YAAY;UACxBtF,WAAW,EAAEwC,IAAI,CAACxC,WAAW;UAC7BC,WAAW,EAAEhD,UAAU,CAACuF,IAAI,CAACvC,WAAW,EAAE,aAAa,EAAE,OAAO,CAAC;UACjEC,IAAI,EAAE,UAAU;UAChBE,OAAO,EAAEoC;SACV,CAAC,CAAC;MACL,CAAC,CAAC;IACJ;IAEAgF,iBAAiBA,CAAA;MACf,IAAI,CAAChH,eAAe,CAACgH,iBAAiB,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAACjE,SAAS,CAAEC,QAAQ,IAAI;QACzE,IAAI,CAACtB,YAAY,GAAG,CAACsB,QAAQ,EAAEiE,qBAAqB,IAAI,EAAE,EAAEhE,GAAG,CAAEjB,IAAS,KAAM;UAC9ElE,KAAK,EAAEkE,IAAI,CAACsB,QAAQ;UACpB9D,WAAW,EAAEwC,IAAI,CAACxC,WAAW;UAC7BC,WAAW,EAAEhD,UAAU,CAACuF,IAAI,CAACvC,WAAW,EAAE,aAAa,EAAE,OAAO,CAAC;UACjEC,IAAI,EAAE,MAAM;UACZE,OAAO,EAAEoC;SACV,CAAC,CAAC;MACL,CAAC,CAAC;IACJ;IAEA1C,WAAWA,CAAE0C,IAAQ;MACnB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MAEA;MACA;MACA;MACA;IAAA;IAGF9C,WAAWA,CAACgI,KAAU,GAAE;IAExB9H,eAAeA,CAAC8H,KAAU,GAAE;IAE5BC,aAAaA,CAAA;MACX,IAAI,CAACrH,MAAM,CAAC8F,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CAAC;IAC9C;;uCAvgBW/F,kBAAkB,EAAAtC,EAAA,CAAA6J,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAA/J,EAAA,CAAA6J,iBAAA,CAAAG,EAAA,CAAAC,uBAAA,GAAAjK,EAAA,CAAA6J,iBAAA,CAAAK,EAAA,CAAAC,eAAA;IAAA;;YAAlB7H,kBAAkB;MAAA8H,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCpEzB1K,EAHN,CAAAC,cAAA,aAAqD,aACH,aACC,aACb;UAC9BD,EAAA,CAAAE,SAAA,6BAAiD;UACjDF,EAAA,CAAAC,cAAA,aAA+C;UAC7CD,EAAA,CAAA4K,UAAA,IAAAC,iCAAA,iBAA4D;UAUlE7K,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;UAIAH,EAHN,CAAAC,cAAA,aAAsD,aACb,aACF,cACS;UAAAD,EAAA,CAAA8K,MAAA,yBAAiB;UAAA9K,EAAA,CAAAG,YAAA,EAAM;UAE/DH,EADF,CAAAC,cAAA,eAAmB,wBAOhB;UADCD,EAAA,CAAAW,UAAA,6BAAAoK,qEAAAtJ,MAAA;YAAA,OAAmBkJ,GAAA,CAAA/B,iBAAA,CAAAnH,MAAA,CAAyB;UAAA,EAAC;UAGjDzB,EADE,CAAAG,YAAA,EAAe,EACX;UACNH,EAAA,CAAAC,cAAA,eAAmB;UACjBD,EAAA,CAAAE,SAAA,oBAAsD;UAE1DF,EADE,CAAAG,YAAA,EAAM,EACF;UACNH,EAAA,CAAAC,cAAA,eAA6D;UAC3DD,EAAA,CAAA4K,UAAA,KAAAI,kCAAA,kBAAiD;UAQzDhL,EAHM,CAAAG,YAAA,EAAM,EACF,EACF,EACF;UAKEH,EAJR,CAAAC,cAAA,eAAgD,eACG,eACI,eACoB,YAC7D;UAACD,EAAA,CAAA8K,MAAA,mBAAU;UAAA9K,EAAA,CAAAG,YAAA,EAAO;UACxBH,EAAA,CAAAC,cAAA,eAA0B;UACxBD,EAAA,CAAA4K,UAAA,KAAAK,sCAAA,sBAWC;UAGLjL,EADE,CAAAG,YAAA,EAAM,EACF;UACNH,EAAA,CAAAC,cAAA,eAAuC;UACrCD,EAAA,CAAAkL,gBAAA,KAAAC,kCAAA,mBAAAnL,EAAA,CAAAoL,sBAAA,CAeC;UACHpL,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAAqE;UACnED,EAAA,CAAAE,SAAA,oBAAsD;UAKhEF,EAJQ,CAAAG,YAAA,EAAM,EACF,EACF,EACF,EACF;;;UApF6BH,EAAA,CAAAI,SAAA,GAAmB;UAAnBJ,EAAA,CAAAK,UAAA,YAAAsK,GAAA,CAAA/H,gBAAA,CAAmB;UAmBxC5C,EAAA,CAAAI,SAAA,GAAmB;UACnBJ,EADA,CAAAK,UAAA,YAAAsK,GAAA,CAAAjI,OAAA,CAAmB,iBACH;UAUM1C,EAAA,CAAAI,SAAA,GAAqB;UAArBJ,EAAA,CAAAK,UAAA,YAAAsK,GAAA,CAAA3G,kBAAA,CAAqB;UAgB3BhE,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAAK,UAAA,YAAAsK,GAAA,CAAAzG,YAAA,CAAe;UAenClE,EAAA,CAAAI,SAAA,GAeC;UAfDJ,EAAA,CAAAqL,UAAA,CAAAV,GAAA,CAAAxG,YAAA,CAeC;;;qBDxBPlF,YAAY,EAAAqM,EAAA,CAAAC,OAAA,EACZpM,YAAY,EACZE,iBAAiB,EACjBD,eAAe,EACfE,aAAa,EACbM,yBAAyB,EACzBC,yBAAyB,EACzBC,qCAAqC,EACrCC,8BAA8B;MAAAyL,MAAA;IAAA;;SAKrBlJ,kBAAkB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}