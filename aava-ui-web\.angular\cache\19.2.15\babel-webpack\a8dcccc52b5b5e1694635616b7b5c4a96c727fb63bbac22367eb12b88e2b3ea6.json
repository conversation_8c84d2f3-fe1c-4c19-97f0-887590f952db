{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, FormControl, Validators } from '@angular/forms';\nimport { MOCK_PROMPTS } from '@shared/mock-data/prompt-mock-data';\nimport { Subscription } from 'rxjs';\nimport promptsLabels from '../constants/prompts.json';\nimport { AvaTextareaComponent, AvaTextboxComponent, ButtonComponent, AccordionComponent, DropdownComponent } from '@ava/play-comp-library';\nimport { SharedNavItemComponent } from '@shared/components/nav-item/nav-item.component';\nimport { LucideAngularModule } from 'lucide-angular';\nimport { AskAvaWrapperComponent } from '@shared/components/ask-ava-wrapper/ask-ava-wrapper.component';\nimport { PromptsModes, USE_CASE_IDENTIFIER } from '../constants/constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@shared/services/tool-execution/tool-execution.service\";\nimport * as i4 from \"@shared/services/prompts.service\";\nimport * as i5 from \"@shared/services/prompt-enhance.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"lucide-angular\";\nconst _c0 = [\"accordion\"];\nconst _c1 = a0 => ({\n  \"three-column-layout\": a0\n});\nconst _c2 = () => ({\n  \"border\": \"2px solid transparent\",\n  \"background-image\": \"linear-gradient(#ffffff, #ffffff), linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)\",\n  \"background-origin\": \"border-box\",\n  \"background-clip\": \"padding-box, border-box\",\n  \"--button-effect-color\": \"33, 90, 214\"\n});\nconst _c3 = () => ({\n  background: \"linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)\",\n  \"--button-effect-color\": \"33, 90, 214\"\n});\nfunction CreatePromptsComponent_span_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 29);\n    i0.ɵɵtext(1, \"Prompt Description\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CreatePromptsComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵelement(1, \"ava-textbox\", 31)(2, \"ava-textarea\", 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r0.promptLabels.promptName);\n    i0.ɵɵpropertyInterpolate1(\"placeholder\", \"\", ctx_r0.promptLabels.pleaseEnterPromptName, \" \");\n    i0.ɵɵproperty(\"formControl\", ctx_r0.getControl(\"name\"))(\"error\", ctx_r0.getFieldError(\"name\"))(\"required\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r0.promptLabels.description);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", ctx_r0.promptLabels.placeholderDescription);\n    i0.ɵɵproperty(\"formControl\", ctx_r0.getControl(\"promptDescription\"))(\"rows\", 6)(\"error\", ctx_r0.getFieldError(\"promptDescription\"))(\"required\", true);\n  }\n}\nfunction CreatePromptsComponent_ava_button_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ava-button\", 33);\n    i0.ɵɵlistener(\"userClick\", function CreatePromptsComponent_ava_button_15_Template_ava_button_userClick_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onCancel());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CreatePromptsComponent_ava_button_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ava-button\", 34);\n    i0.ɵɵlistener(\"userClick\", function CreatePromptsComponent_ava_button_16_Template_ava_button_userClick_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.toggleAskAvaModal());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r0.promptLabels.askAva);\n  }\n}\nfunction CreatePromptsComponent_ava_button_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ava-button\", 35);\n    i0.ɵɵlistener(\"userClick\", function CreatePromptsComponent_ava_button_17_Template_ava_button_userClick_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.isEditMode ? ctx_r0.confirmUpdate() : ctx_r0.confirmSave());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isSaveDisabled())(\"label\", ctx_r0.isEditMode ? \"Update\" : \"Save\")(\"customStyles\", i0.ɵɵpureFunction0(3, _c3));\n  }\n}\nfunction CreatePromptsComponent_shared_nav_item_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"shared-nav-item\", 36);\n    i0.ɵɵlistener(\"selectEvent\", function CreatePromptsComponent_shared_nav_item_23_Template_shared_nav_item_selectEvent_0_listener() {\n      const tab_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onTabSelected(tab_r6.value));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tab_r6 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isEditMode || ctx_r0.isViewMode)(\"label\", tab_r6.label)(\"selected\", ctx_r0.selectedTab === tab_r6.value)(\"hasDropdown\", false)(\"dropdownOpen\", false);\n  }\n}\nfunction CreatePromptsComponent_ng_container_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 37);\n    i0.ɵɵelement(2, \"ava-textarea\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r0.promptLabels.freeformPrompt);\n    i0.ɵɵproperty(\"formControl\", ctx_r0.getControl(\"promptTask\"))(\"rows\", 8)(\"error\", ctx_r0.getFieldError(\"promptTask\"))(\"fullWidth\", true)(\"required\", true);\n  }\n}\nfunction CreatePromptsComponent_ng_container_27_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"div\", 41);\n    i0.ɵɵelement(2, \"ava-textarea\", 50);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r0.promptLabels.intermediateSteps);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", ctx_r0.promptLabels.placeholderIntermediateSteps);\n    i0.ɵɵproperty(\"formControl\", ctx_r0.getControl(\"intermediateSteps\"))(\"rows\", 4)(\"fullWidth\", true)(\"error\", ctx_r0.getFieldError(\"intermediateSteps\"))(\"required\", true);\n  }\n}\nfunction CreatePromptsComponent_ng_container_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 39)(2, \"div\", 40)(3, \"div\", 41)(4, \"div\", 42);\n    i0.ɵɵelement(5, \"ava-textbox\", 43);\n    i0.ɵɵelementStart(6, \"ava-dropdown\", 44);\n    i0.ɵɵlistener(\"selectionChange\", function CreatePromptsComponent_ng_container_27_Template_ava_dropdown_selectionChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onPromptTypeChange($event));\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(7, \"div\", 40)(8, \"div\", 41);\n    i0.ɵɵelement(9, \"ava-textarea\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 41);\n    i0.ɵɵelement(11, \"ava-textarea\", 46);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 40)(13, \"div\", 41);\n    i0.ɵɵelement(14, \"ava-textarea\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 41);\n    i0.ɵɵelement(16, \"ava-textarea\", 48);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(17, CreatePromptsComponent_ng_container_27_div_17_Template, 3, 7, \"div\", 49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r0.promptLabels.role);\n    i0.ɵɵproperty(\"formControl\", ctx_r0.getControl(\"role\"))(\"disabled\", ctx_r0.isViewMode)(\"error\", ctx_r0.getFieldError(\"role\"))(\"required\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"options\", ctx_r0.promptTypeOptions)(\"selectedValue\", ctx_r0.selectedDropdownValue)(\"required\", true)(\"error\", ctx_r0.getFieldError(\"promptType\"))(\"disabled\", ctx_r0.isViewMode);\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate(\"name\", ctx_r0.promptLabels.goal);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", ctx_r0.promptLabels.placeholderGoal);\n    i0.ɵɵproperty(\"formControl\", ctx_r0.getControl(\"goal\"))(\"rows\", 4)(\"error\", ctx_r0.getFieldError(\"goal\"))(\"fullWidth\", true)(\"required\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r0.promptLabels.description);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", ctx_r0.promptLabels.placeholderDescription);\n    i0.ɵɵproperty(\"formControl\", ctx_r0.getControl(\"description\"))(\"rows\", 4)(\"fullWidth\", true)(\"error\", ctx_r0.getFieldError(\"description\"))(\"required\", true);\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r0.promptLabels.backstory);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", ctx_r0.promptLabels.placeholderBackStory);\n    i0.ɵɵproperty(\"formControl\", ctx_r0.getControl(\"backstory\"))(\"rows\", 4)(\"fullWidth\", true)(\"error\", ctx_r0.getFieldError(\"backstory\"))(\"required\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r0.promptLabels.expectedOutput);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", ctx_r0.promptLabels.placeholderExpectedOutput);\n    i0.ɵɵproperty(\"formControl\", ctx_r0.getControl(\"expectedOutput\"))(\"rows\", 4)(\"fullWidth\", true)(\"error\", ctx_r0.getFieldError(\"expectedOutput\"))(\"required\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.getControl(\"promptType\").value && ctx_r0.getControl(\"promptType\").value.toLowerCase() === \"chain of thought\");\n  }\n}\nfunction CreatePromptsComponent_div_30_div_1_div_8_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 68)(1, \"ava-button\", 69);\n    i0.ɵɵlistener(\"userClick\", function CreatePromptsComponent_div_30_div_1_div_8_div_5_Template_ava_button_userClick_1_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const i_r10 = i0.ɵɵnextContext().index;\n      const ctx_r0 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r0.removeExample(i_r10));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r0.promptLabels.remove);\n  }\n}\nfunction CreatePromptsComponent_div_30_div_1_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64)(1, \"div\", 41);\n    i0.ɵɵelement(2, \"ava-textarea\", 65);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 41);\n    i0.ɵɵelement(4, \"ava-textarea\", 66);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, CreatePromptsComponent_div_30_div_1_div_8_div_5_Template, 2, 1, \"div\", 67);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r10 = ctx.index;\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"formGroupName\", i_r10);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"name\", ctx_r0.promptLabels.input);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", ctx_r0.promptLabels.placeholderInput);\n    i0.ɵɵproperty(\"formControlName\", \"input\")(\"rows\", 2)(\"fullWidth\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"name\", ctx_r0.promptLabels.output);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", ctx_r0.promptLabels.placeholderOutput);\n    i0.ɵɵproperty(\"formControlName\", \"output\")(\"rows\", 2)(\"fullWidth\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r10 > 0);\n  }\n}\nfunction CreatePromptsComponent_div_30_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 53)(1, \"ava-accordion\", 54, 0)(3, \"div\", 55);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 56)(6, \"div\", 57)(7, \"div\", 58);\n    i0.ɵɵtemplate(8, CreatePromptsComponent_div_30_div_1_div_8_Template, 6, 12, \"div\", 59);\n    i0.ɵɵelementStart(9, \"div\", 60)(10, \"ava-button\", 61);\n    i0.ɵɵlistener(\"userClick\", function CreatePromptsComponent_div_30_div_1_Template_ava_button_userClick_10_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.addExample());\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 62);\n    i0.ɵɵelement(12, \"ava-textarea\", 63);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"expanded\", false)(\"animation\", true)(\"controlled\", false);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.promptLabels.addExamples, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.examples.controls);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r0.promptLabels.addExamples);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r0.promptLabels.additionalConsideration);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", ctx_r0.promptLabels.placeholderAdditionalConsideration);\n    i0.ɵɵproperty(\"formControl\", ctx_r0.getControl(\"addConsideration\"))(\"rows\", 2)(\"fullWidth\", true);\n  }\n}\nfunction CreatePromptsComponent_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtemplate(1, CreatePromptsComponent_div_30_div_1_Template, 13, 11, \"div\", 52);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.getControl(\"promptType\").value || ctx_r0.getControl(\"promptType\").value.toLowerCase() !== \"zero shot\");\n  }\n}\nfunction CreatePromptsComponent_ng_container_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"ava-textarea\", 70);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r0.promptLabels.freeformPrompt);\n    i0.ɵɵproperty(\"formControl\", ctx_r0.getOutPutControl(\"promptTask\"))(\"rows\", 8)(\"fullWidth\", true)(\"readonly\", true);\n  }\n}\nfunction CreatePromptsComponent_ng_container_34_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 79);\n    i0.ɵɵelement(1, \"ava-textarea\", 80);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r0.promptLabels.intermediateSteps);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", ctx_r0.promptLabels.placeholderIntermediateSteps);\n    i0.ɵɵproperty(\"formControl\", ctx_r0.getOutPutControl(\"intermediateSteps\"))(\"rows\", 4)(\"fullWidth\", true)(\"readonly\", true);\n  }\n}\nfunction CreatePromptsComponent_ng_container_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 71)(2, \"div\", 72);\n    i0.ɵɵelement(3, \"ava-textbox\", 73);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 72);\n    i0.ɵɵelement(5, \"ava-textarea\", 74);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 72);\n    i0.ɵɵelement(7, \"ava-textarea\", 75);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 72);\n    i0.ɵɵelement(9, \"ava-textarea\", 76);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 72);\n    i0.ɵɵelement(11, \"ava-textarea\", 77);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, CreatePromptsComponent_ng_container_34_div_12_Template, 2, 6, \"div\", 78);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r0.promptLabels.role);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", ctx_r0.promptLabels.placeholderRole);\n    i0.ɵɵproperty(\"formControl\", ctx_r0.getOutPutControl(\"role\"))(\"readonly\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", ctx_r0.promptLabels.placeholderGoal);\n    i0.ɵɵproperty(\"formControl\", ctx_r0.getOutPutControl(\"goal\"))(\"rows\", 4)(\"fullWidth\", true)(\"readonly\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r0.promptLabels.description);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", ctx_r0.promptLabels.placeholderDescription);\n    i0.ɵɵproperty(\"formControl\", ctx_r0.getOutPutControl(\"description\"))(\"rows\", 4)(\"fullWidth\", true)(\"readonly\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r0.promptLabels.backstory);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", ctx_r0.promptLabels.placeholderBackStory);\n    i0.ɵɵproperty(\"formControl\", ctx_r0.getOutPutControl(\"backstory\"))(\"rows\", 4)(\"fullWidth\", true)(\"readonly\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r0.promptLabels.expectedOutput);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", ctx_r0.promptLabels.placeholderExpectedOutput);\n    i0.ɵɵproperty(\"formControl\", ctx_r0.getOutPutControl(\"expectedOutput\"))(\"rows\", 4)(\"fullWidth\", true)(\"readonly\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.getOutPutControl(\"promptType\").value && ctx_r0.getOutPutControl(\"promptType\").value.toLowerCase() === \"chain of thought\");\n  }\n}\nexport let CreatePromptsComponent = /*#__PURE__*/(() => {\n  class CreatePromptsComponent {\n    fb;\n    router;\n    route;\n    toolExecutionService;\n    promptsService;\n    cdr;\n    promptGenerateService;\n    isViewMode = false;\n    promptTypeOptions = [];\n    promptRoleOptions = [];\n    selectedPromptType = '';\n    selectedDropdownValue = ''; // Add this property to track selected value\n    selectedRoleValue = '';\n    previousAccordionState = false;\n    showSuccessPopup = false;\n    submissionSuccess = false;\n    popupTitle = '';\n    popupMessage = '';\n    formChanged = false;\n    isPatching = false;\n    iconName = 'check-circle';\n    accordionRef;\n    // Labels used across the Prompt UI components (titles)\n    promptLabels = promptsLabels.labels;\n    promptTabs = [{\n      label: 'Freeform',\n      value: 'freeform'\n    }, {\n      label: 'Template',\n      value: 'template'\n    }];\n    showAskAvaModal = false;\n    showPromptOutput = false;\n    isLoading = false;\n    selectedTab = 'freeform';\n    onTabSelected(tabValue) {\n      this.selectedTab = tabValue;\n      const promptTaskControl = this.promptForm.get('promptTask');\n      if (tabValue === 'template') {\n        promptTaskControl?.clearValidators();\n      } else if (tabValue === 'freeform') {\n        promptTaskControl?.setValidators([Validators.required]);\n      }\n      promptTaskControl?.updateValueAndValidity();\n    }\n    onPromptTypeChange(event) {\n      // Handle dropdown selection change\n      if (event && event.selectedOptions && event.selectedOptions.length > 0) {\n        const selectedValue = event.selectedOptions[0].value;\n        this.promptForm.get('promptType')?.setValue(selectedValue, {\n          emitEvent: false\n        });\n        this.selectedDropdownValue = selectedValue;\n      }\n    }\n    // Mode flags\n    categoryId = null;\n    promptId = null;\n    isEditMode = false;\n    isExecuteMode = false;\n    showChatInterface = false;\n    isLeftCollapsed = false;\n    toggleLeftPanel() {\n      this.isLeftCollapsed = !this.isLeftCollapsed;\n    }\n    promptForm;\n    promptsOutputForm;\n    // Chat interface properties\n    chatMessages = [];\n    isProcessingChat = false;\n    // Subscription\n    executionSubscription = new Subscription();\n    prompt = new FormControl('');\n    constructor(fb, router, route, toolExecutionService, promptsService, cdr, promptGenerateService) {\n      this.fb = fb;\n      this.router = router;\n      this.route = route;\n      this.toolExecutionService = toolExecutionService;\n      this.promptsService = promptsService;\n      this.cdr = cdr;\n      this.promptGenerateService = promptGenerateService;\n      this.promptForm = this.createPromtForm();\n      this.promptsOutputForm = this.createPromtForm();\n    }\n    promptCache = {\n      template: '',\n      freeform: ''\n    };\n    createPromtForm() {\n      return this.fb.group({\n        // Prompt details\n        name: [''],\n        promptDescription: [''],\n        // Prompt task (for freeform)\n        promptTask: ['', Validators.required],\n        // Right side (Template configuration fields)\n        role: ['', Validators.required],\n        backstory: [''],\n        description: [''],\n        goal: [''],\n        expectedOutput: [''],\n        // Prompt Type Dropdown\n        promptType: [''],\n        // Examples (these fields are still in the form group, but no longer rendered in HTML)\n        examples: this.fb.array([this.fb.group({\n          input: [''],\n          output: ['']\n        })]),\n        addConsideration: [''],\n        // Steps (these fields are still in the form group, but no longer rendered in HTML)\n        step1Instruction: [''],\n        // Add intermediateSteps to the form group\n        intermediateSteps: ['']\n      });\n    }\n    ngOnInit() {\n      this.promptForm.valueChanges.subscribe(() => {\n        if (!this.isPatching) {\n          this.formChanged = true;\n        }\n      });\n      // First load dropdown options\n      this.promptsService.fetchPromptTypeDropdown().subscribe({\n        next: options => {\n          if (options && options.length && options[0].name && options[0].value) {\n            this.promptRoleOptions = options;\n          } else if (options && typeof options === 'object' && !Array.isArray(options)) {\n            this.promptRoleOptions = Object.keys(options).map(key => ({\n              name: options[key],\n              value: options[key]\n            }));\n          } else {\n            this.promptRoleOptions = [];\n          }\n        }\n      });\n      this.promptsService.fetchPromptTypeDropdown().subscribe({\n        next: options => {\n          if (options && options.length && options[0].name && options[0].value) {\n            this.promptTypeOptions = options;\n          } else if (options && typeof options === 'object' && !Array.isArray(options)) {\n            this.promptTypeOptions = Object.keys(options).map(key => ({\n              name: options[key],\n              value: options[key]\n            }));\n          } else {\n            this.promptTypeOptions = [];\n          }\n          // After dropdown options are loaded, check if we need to load prompt data\n          this.promptId = this.route.snapshot.paramMap.get('id');\n          const executeParam = this.route.snapshot.queryParamMap.get('execute');\n          const viewParam = this.route.snapshot.queryParamMap.get('view');\n          this.isEditMode = !!this.promptId;\n          this.isExecuteMode = executeParam === 'true';\n          this.isViewMode = viewParam === 'true';\n          this.showChatInterface = this.isExecuteMode;\n          if (this.isEditMode && this.promptId) {\n            this.loadPromptById(this.promptId);\n          } else {\n            // If not in edit mode but we have a stored prompt type, try to set it\n            this.trySetStoredPromptType();\n          }\n          // If form is already patched but dropdown options were loaded later, try to update the dropdown\n          this.tryUpdateDropdownAfterFormPatch();\n        },\n        error: err => {\n          console.error('Dropdown API error:', err);\n          // Even if dropdown fails, still try to load prompt data\n          this.promptId = this.route.snapshot.paramMap.get('id');\n          const executeParam = this.route.snapshot.queryParamMap.get('execute');\n          const viewParam = this.route.snapshot.queryParamMap.get('view');\n          setTimeout(() => {\n            this.isEditMode = !!this.promptId;\n            this.cdr.detectChanges(); // Optional: force update after flag is set\n          });\n          this.isExecuteMode = executeParam === 'true';\n          this.isViewMode = viewParam === 'true';\n          this.showChatInterface = this.isExecuteMode;\n          if (this.isEditMode && this.promptId) {\n            this.loadPromptById(this.promptId);\n          }\n        }\n      });\n      // Debug: log value changes for promptType\n      this.promptForm.get('promptType')?.valueChanges.subscribe(val => {\n        // Update the selected dropdown value when form control changes\n        this.selectedDropdownValue = val || '';\n        console.log('Form control value changed:', val, 'selectedDropdownValue:', this.selectedDropdownValue);\n      });\n      // Handle execute mode (this doesn't depend on dropdown options)\n      const executeParam = this.route.snapshot.queryParamMap.get('execute');\n      if (this.isExecuteMode && this.promptId) {\n        this.chatMessages = [{\n          from: 'ai',\n          text: 'Hi Akash, this is the prompt testing'\n        }, {\n          from: 'user',\n          text: 'Test this input'\n        }, {\n          from: 'ai',\n          text: 'Here is the output'\n        }];\n        setTimeout(() => {\n          this.toolExecutionService.startExecution(this.promptId, this.chatMessages);\n          this.executionSubscription = this.toolExecutionService.getExecutionState().subscribe(state => {\n            if (state.isExecuting && state.toolId === this.promptId) {\n              this.chatMessages = state.chatMessages;\n            }\n          });\n        }, 100);\n      }\n    }\n    // Add a property to store the prompt type for delayed setting\n    storedPromptType = '';\n    // Method to try setting stored prompt type when dropdown options are loaded\n    trySetStoredPromptType() {\n      if (this.storedPromptType && this.promptTypeOptions.length > 0) {\n        const match = this.findMatchingDropdownOption(this.storedPromptType);\n        if (match) {\n          // Set the value immediately\n          this.promptForm.get('promptType')?.setValue(match.value, {\n            emitEvent: false\n          });\n          this.selectedDropdownValue = match.value;\n          // Also set it after a delay to ensure the dropdown is rendered\n          setTimeout(() => {\n            this.promptForm.get('promptType')?.setValue(match.value, {\n              emitEvent: false\n            });\n            this.selectedDropdownValue = match.value;\n          }, 200);\n          this.storedPromptType = ''; // Clear after setting\n        }\n      }\n    }\n    // Method to try updating dropdown after form is already patched\n    tryUpdateDropdownAfterFormPatch() {\n      const currentValue = this.promptForm.get('promptType')?.value;\n      if (currentValue && this.promptTypeOptions.length > 0) {\n        // Check if the current value is not in the dropdown options\n        const isValueInOptions = this.promptTypeOptions.some(opt => opt.value === currentValue || opt.name === currentValue);\n        if (!isValueInOptions) {\n          // Try to find a matching option\n          const match = this.findMatchingDropdownOption(currentValue);\n          if (match) {\n            // Set the value immediately\n            this.promptForm.get('promptType')?.setValue(match.value, {\n              emitEvent: false\n            });\n            this.selectedDropdownValue = match.value;\n            // Also set it after a delay to ensure the dropdown is rendered\n            setTimeout(() => {\n              this.promptForm.get('promptType')?.setValue(match.value, {\n                emitEvent: false\n              });\n              this.selectedDropdownValue = match.value;\n            }, 200);\n          }\n        }\n      }\n    }\n    // Helper method to find matching dropdown option\n    findMatchingDropdownOption(promptType) {\n      if (!promptType || this.promptTypeOptions.length === 0) {\n        return null;\n      }\n      // Try exact match first (case-insensitive)\n      let match = this.promptTypeOptions.find(opt => opt.value.toLowerCase() === promptType.toLowerCase());\n      // If no exact match, try matching by name\n      if (!match) {\n        match = this.promptTypeOptions.find(opt => opt.name.toLowerCase() === promptType.toLowerCase());\n      }\n      // If still no match, try partial matching\n      if (!match) {\n        match = this.promptTypeOptions.find(opt => opt.value.toLowerCase().includes(promptType.toLowerCase()) || opt.name.toLowerCase().includes(promptType.toLowerCase()) || promptType.toLowerCase().includes(opt.value.toLowerCase()) || promptType.toLowerCase().includes(opt.name.toLowerCase()));\n      }\n      return match || null;\n    }\n    loadPromptById(promptId) {\n      this.promptsService.getPromptById(promptId).subscribe({\n        next: prompt => {\n          if (prompt) {\n            this.categoryId = prompt.categoryId;\n            this.patchPromptForm(prompt);\n          } else {}\n        },\n        error: err => {\n          console.error('Error fetching prompt:', err);\n        }\n      });\n    }\n    patchPromptForm(prompt) {\n      this.isPatching = true;\n      // After form is fully patched\n      setTimeout(() => {\n        this.isPatching = false;\n        this.formChanged = false; // form hasn't changed yet\n      }, 0);\n      if (this.isViewMode) {\n        setTimeout(() => {\n          this.promptForm.disable({\n            emitEvent: false\n          });\n        }, 0);\n      }\n      // Set selected tab first based on type\n      this.selectedTab = prompt.type === 'free form' ? 'freeform' : 'template';\n      const roleFromAPI = prompt.role; // e.g., \"Developer\"\n      const matchedRole = this.promptRoleOptions.find(opt => opt.name.toLowerCase() === roleFromAPI?.toLowerCase());\n      this.promptForm.patchValue({\n        role: matchedRole?.value\n      });\n      this.selectedRoleValue = matchedRole?.value ?? '';\n      // Improved dropdown type matching logic\n      let dropdownType = '';\n      if (prompt.type) {\n        if (this.promptTypeOptions.length > 0) {\n          // Dropdown options are available, try to find a match\n          const match = this.findMatchingDropdownOption(prompt.type);\n          if (match) {\n            dropdownType = match.value;\n          } else {\n            // If no match found, use the original type as fallback\n            dropdownType = prompt.type;\n            console.warn(`No matching dropdown option found for prompt type: \"${prompt.type}\". Available options:`, this.promptTypeOptions);\n          }\n        } else {\n          // Dropdown options not loaded yet, store the type for later\n          this.storedPromptType = prompt.type;\n          dropdownType = prompt.type;\n        }\n      } else {\n        dropdownType = prompt.type || '';\n      }\n      // Patch only the relevant fields based on the selected tab/type\n      const patchObj = {\n        name: prompt.name || '',\n        role: prompt.role || '',\n        goal: prompt.goal || '',\n        backstory: prompt.backstory || '',\n        expectedOutput: prompt.expectedOutput || '',\n        intermediateSteps: prompt.intermediateSteps || '',\n        promptType: dropdownType\n      };\n      if (this.selectedTab === 'freeform') {\n        patchObj.promptTask = prompt.prompt || '';\n        patchObj.promptDescription = prompt.promptDescription || '';\n        // Clear template fields\n        patchObj.description = prompt.description;\n      } else if (this.selectedTab === 'template') {\n        patchObj.description = prompt.description || '';\n        patchObj.promptDescription = prompt.promptDescription || '';\n        // Clear freeform fields\n        patchObj.promptTask = '';\n      }\n      this.promptForm.patchValue(patchObj);\n      // Update the selected dropdown value\n      this.selectedDropdownValue = dropdownType;\n      console.log('patchPromptForm - dropdownType:', dropdownType, 'selectedDropdownValue:', this.selectedDropdownValue);\n      // If we're in template mode and have a dropdown type, ensure it's set after a delay\n      if (this.selectedTab === 'template' && dropdownType) {\n        setTimeout(() => {\n          this.promptForm.get('promptType')?.setValue(dropdownType, {\n            emitEvent: false\n          });\n          this.selectedDropdownValue = dropdownType;\n          console.log('After delay - dropdownType:', dropdownType, 'selectedDropdownValue:', this.selectedDropdownValue);\n        }, 100);\n      }\n      // Patch examples if available\n      if (prompt.examples && Array.isArray(prompt.examples)) {\n        const exampleArray = this.promptForm.get('examples');\n        // Clear existing examples\n        exampleArray.clear();\n        // Push each example into the form array\n        prompt.examples.forEach(ex => {\n          exampleArray.push(this.fb.group({\n            input: [ex.exampleInput || ''],\n            output: [ex.exampleOutput || '']\n          }));\n        });\n      }\n    }\n    selectTab(tab) {\n      this.selectedTab = tab;\n    }\n    ngOnDestroy() {\n      // Clean up subscription\n      if (this.executionSubscription) {\n        this.executionSubscription.unsubscribe();\n      }\n    }\n    onSave() {\n      this.formChanged = false;\n      if (this.isViewMode) {\n        return;\n      }\n      const nameControl = this.promptForm.get('name');\n      const promptTaskControl = this.promptForm.get('promptTask');\n      const promptTypeControl = this.promptForm.get('promptType');\n      const promptDescControl = this.promptForm.get('promptDescription');\n      const expectedOutputControl = this.promptForm.get('expectedOutput');\n      const roleControl = this.promptForm.get('role');\n      const goalControl = this.promptForm.get('goal');\n      const backstoryControl = this.promptForm.get('backstory');\n      const stepsControl = this.promptForm.get('intermediateSteps');\n      //  Clear all validators first\n      stepsControl?.clearValidators();\n      nameControl?.clearValidators();\n      promptTaskControl?.clearValidators();\n      promptTypeControl?.clearValidators();\n      promptDescControl?.clearValidators();\n      expectedOutputControl?.clearValidators();\n      roleControl?.clearValidators();\n      goalControl?.clearValidators();\n      backstoryControl?.clearValidators();\n      // Apply required validators\n      nameControl?.setValidators([Validators.required]);\n      promptDescControl?.setValidators([Validators.required]);\n      if (this.selectedTab === 'template') {\n        promptDescControl?.setValidators([Validators.required]);\n        expectedOutputControl?.setValidators([Validators.required]);\n        roleControl?.setValidators([Validators.required]);\n        goalControl?.setValidators([Validators.required]);\n        backstoryControl?.setValidators([Validators.required]);\n        promptTypeControl?.setValidators([Validators.required]);\n        promptTaskControl?.clearValidators();\n        promptTaskControl?.setValue('');\n      } else if (this.selectedTab === 'freeform') {\n        promptTaskControl?.setValidators([Validators.required]);\n        this.promptForm.get('promptType')?.setValue('free form', {\n          emitEvent: false\n        });\n      }\n      //  Update value and validity\n      nameControl?.updateValueAndValidity();\n      promptTaskControl?.updateValueAndValidity();\n      promptTypeControl?.updateValueAndValidity();\n      promptDescControl?.updateValueAndValidity();\n      expectedOutputControl?.updateValueAndValidity();\n      roleControl?.updateValueAndValidity();\n      goalControl?.updateValueAndValidity();\n      backstoryControl?.updateValueAndValidity();\n      if (this.promptForm.value.promptType === 'Chain of Thought') {\n        stepsControl?.setValidators([Validators.required]);\n      }\n      stepsControl?.updateValueAndValidity();\n      //  If invalid, mark touched and exit\n      if (!this.promptForm.valid) {\n        this.promptForm.markAllAsTouched();\n        return;\n      }\n      const formValues = this.promptForm.value;\n      const returnPage = this.route.snapshot.queryParamMap.get('returnPage');\n      const pageNumber = returnPage ? parseInt(returnPage) : 1;\n      // Use the original case from dropdown for type\n      const promptType = formValues.promptType || '';\n      // Build payload for each type\n      let createPayload = {\n        categoryId: this.categoryId ?? 1,\n        // fallback to 1 if null\n        type: promptType,\n        name: formValues.name,\n        role: formValues.role,\n        goal: formValues.goal,\n        backstory: formValues.backstory,\n        description: formValues.description,\n        promptDescription: formValues.promptDescription,\n        expectedOutput: formValues.expectedOutput,\n        // For template prompts (including Zero Shot), use templateDescription as prompt\n        prompt: this.selectedTab === 'template' ? formValues.description : formValues.promptTask || ''\n      };\n      if (promptType === 'Zero Shot') {} else if (promptType === 'One Shot/Multi Shot' || promptType === 'One Shot' || promptType === 'Multi Shot') {\n        // Send all examples, including empty ones\n        createPayload.examples = this.examples.getRawValue().map(e => ({\n          exampleInput: e.input,\n          exampleOutput: e.output\n        }));\n        createPayload.additionalConsideration = formValues.addConsideration;\n      } else if (promptType === 'Chain of Thought') {\n        createPayload.intermediateSteps = formValues.intermediateSteps;\n        // Send all examples, including empty ones\n        createPayload.examples = this.examples.getRawValue().map(e => ({\n          exampleInput: e.input,\n          exampleOutput: e.output\n        }));\n        createPayload.additionalConsideration = formValues.addConsideration;\n      } else if (promptType === 'Free Form') {\n        // For free form, only keep type, name, and prompt\n        createPayload = {\n          type: promptType,\n          name: formValues.name,\n          prompt: formValues.promptTask || ''\n        };\n      }\n      // Remove undefined fields\n      Object.keys(createPayload).forEach(key => {\n        if (createPayload[key] === undefined) {\n          delete createPayload[key];\n        }\n      });\n      if (this.isEditMode && this.promptId) {\n        // For edit, ensure id is present in payload\n        const editPayload = {\n          ...createPayload,\n          id: this.promptId\n        };\n        this.promptsService.editPrompt(editPayload).subscribe({\n          next: info => {\n            this.iconName = 'check-circle';\n            this.showSuccessPopup = true;\n            this.popupMessage = info?.info?.message || info.message;\n            this.submissionSuccess = true;\n          },\n          error: error => {\n            this.iconName = 'alert-circle';\n            this.popupMessage = error?.error?.message || error.message;\n            this.showSuccessPopup = true;\n            this.submissionSuccess = false;\n          }\n        });\n      } else {\n        this.promptsService.addPrompt(createPayload).subscribe({\n          next: info => {\n            this.iconName = 'check-circle';\n            this.showSuccessPopup = true;\n            this.popupMessage = info?.info?.message || info.message;\n            this.submissionSuccess = true;\n            // Reset examples array after successful save to a single empty group\n            const examplesArray = this.promptForm.get('examples');\n            while (examplesArray.length > 0) {\n              examplesArray.removeAt(0);\n            }\n            examplesArray.push(this.fb.group({\n              input: [''],\n              output: ['']\n            }));\n          },\n          error: error => {\n            this.iconName = 'alert-circle';\n            this.popupMessage = error?.error?.message || error.message;\n            this.showSuccessPopup = true;\n            this.submissionSuccess = false;\n          }\n        });\n      }\n    }\n    onExecute() {\n      if (this.promptId) {\n        // If we're already in execute mode with chat interface showing\n        if (this.isExecuteMode && this.showChatInterface) {\n          // Process the execution\n        } else {\n          // Set flags to show chat interface\n          this.isExecuteMode = true;\n          this.showChatInterface = true;\n          // Set the initial messages\n          this.chatMessages = [{\n            from: 'ai',\n            text: 'Hi Akash, this is the prompt testing'\n          }, {\n            from: 'user',\n            text: 'Test this input'\n          }, {\n            from: 'ai',\n            text: 'Here is the output'\n          }];\n          // Delay starting the execution service slightly to allow UI to update\n          setTimeout(() => {\n            this.toolExecutionService.startExecution(this.promptId, this.chatMessages);\n          }, 100);\n        }\n      }\n    }\n    onExit() {\n      // If we're in execute mode with chat interface showing\n      if (this.isExecuteMode && this.isEditMode) {\n        // Return to edit mode without chat interface\n        this.isExecuteMode = false;\n        this.showChatInterface = false;\n        this.toolExecutionService.stopExecution();\n      } else {\n        // Get the return page if available\n        const returnPage = this.route.snapshot.queryParamMap.get('returnPage');\n        const pageNumber = returnPage ? parseInt(returnPage) : 1;\n        // Exit to prompts list at correct page\n        this.router.navigate(['/libraries/prompts'], {\n          queryParams: {\n            page: pageNumber\n          }\n        });\n      }\n    }\n    // Helper method to get form controls easily from the template\n    getControl(name) {\n      return this.promptForm.get(name);\n    }\n    getOutPutControl(name) {\n      return this.promptsOutputForm.get(name);\n    }\n    // Handle file attachment for the prompt task\n    handleAttachment() {\n      // Implement file attachment logic here\n    }\n    // Analyze the prompt task\n    analyzePrompt() {\n      // Implement prompt analysis logic here\n    }\n    // Load prompt data from mock data\n    loadPromptData(promptId) {\n      // In a real app, this would use data fetched from a service\n      const prompt = MOCK_PROMPTS?.find(p => p.id === promptId);\n      if (prompt) {\n        // Set form values based on the prompt data\n        this.promptForm.get('name')?.setValue(prompt.title);\n        this.promptForm.get('description')?.setValue(`This is the ${prompt.title} description.`);\n        // Set filter data based on the prompt properties\n        this.promptForm.get('organization')?.setValue('Ascendion');\n        this.promptForm.get('domain')?.setValue(prompt.department || 'AI Research');\n        this.promptForm.get('project')?.setValue(prompt.project || 'Prompt Engineering');\n        this.promptForm.get('team')?.setValue('NLP Team');\n        // Set sample values for other fields\n        this.promptForm.get('promptTask')?.setValue('Generate a conversation about AI safety');\n        this.promptForm.get('role')?.setValue('AI Assistant');\n        this.promptForm.get('goal')?.setValue('Help users understand AI safety concepts');\n        this.promptForm.get('backstory')?.setValue('You are an expert in AI safety and ethics');\n        this.promptForm.get('expectedOutput')?.setValue('A clear, informative conversation about AI safety');\n      }\n    }\n    // Handle chat messages\n    handleChatMessage(message) {\n      if (this.promptId && this.isExecuteMode) {\n        this.isProcessingChat = true;\n        this.chatMessages.push({\n          from: 'user',\n          text: message\n        });\n        // this.previewPrompt();\n        // Process through the service - it will handle adding user and AI messages\n        // this.toolExecutionService.processUserMessage(message);\n        // Reset loading state after a delay that matches the service's response time\n        setTimeout(() => {\n          this.isProcessingChat = false;\n        }, 1000);\n      }\n    }\n    get examples() {\n      return this.promptForm.get('examples');\n    }\n    addExample() {\n      this.examples.push(this.fb.group({\n        id: [null],\n        input: [''],\n        output: ['']\n      }));\n    }\n    removeExample(index) {\n      // Only remove if it's not the first one\n      if (index > 0) {\n        this.examples.removeAt(index);\n      }\n    }\n    //   private previewPrompt(): void {\n    //   const payload = {\n    //     role: this.getControl('role').value,\n    //     goal: this.getControl('goal').value,\n    //     backstory: this.getControl('backstory').value,\n    //     description: this.getControl('description').value,\n    //     expectedOutput: this.getControl('expectedOutput').value\n    //   };\n    //   this.promptsService.previewPrompt(payload).subscribe({\n    //     next: (response) => {\n    //       console.log('Preview API Response:', response);\n    //       const previewResponseText = typeof response === 'string'\n    //         ? response\n    //         : JSON.stringify(response, null, 2); // fallback for object\n    //       // Add preview as AI message to chat\n    //       this.chatMessages.push({\n    //         from: 'ai',\n    //         text: previewResponseText\n    //       });\n    //       this.isProcessingChat = false;\n    //     },\n    //     error: (err) => {\n    //       console.error('Error during preview prompt:', err);\n    //       this.chatMessages.push({\n    //         from: 'ai',\n    //         text: 'An error occurred while generating the preview.'\n    //       });\n    //       this.isProcessingChat = false;\n    //     }\n    //   });\n    // }\n    regenerate() {\n      const previousPrompt = this.promptCache[this.selectedTab];\n      if (previousPrompt) {\n        this.prompt.setValue(previousPrompt);\n        this.toggleAskAvaModal();\n        this.onClickGenerate(previousPrompt);\n      }\n    }\n    ngAfterViewChecked() {\n      if (!this.accordionRef) return;\n      const currentExpanded = this.accordionRef.expanded;\n      if (this.previousAccordionState && !currentExpanded) {\n        setTimeout(() => {\n          this.clearExtraExamples();\n        });\n      }\n      this.previousAccordionState = currentExpanded;\n    }\n    clearExtraExamples() {\n      const examplesArray = this.promptForm.get('examples');\n      while (examplesArray.length > 1) {\n        examplesArray.removeAt(1);\n      }\n      examplesArray.at(0).get('input')?.reset();\n      examplesArray.at(0).get('output')?.reset();\n    }\n    // Get error message for a specific form field\n    getFieldError(fieldName) {\n      const field = this.promptForm.get(fieldName);\n      // Custom label mapping\n      const customLabels = {\n        name: 'Prompt Name',\n        description: 'Description',\n        promptTask: 'Freeform Prompt',\n        role: 'Role',\n        goal: 'Goal',\n        promptDescription: 'Description',\n        backstory: 'Backstory',\n        expectedOutput: 'ExpectedOutput',\n        promptType: 'PromptType'\n      };\n      const formattedFieldName = customLabels[fieldName] || fieldName;\n      if (field && field.invalid && (field.touched || field.dirty)) {\n        if (field.errors?.['required']) {\n          return `${formattedFieldName} is required`;\n        }\n        if (field.errors?.['minlength']) {\n          return `${formattedFieldName} must be at least ${field.errors['minlength'].requiredLength} characters long`;\n        }\n      } else if (fieldName == 'modelType' || fieldName == 'apiVersion') {\n        if (field && field.errors?.['required']) {\n          return `${formattedFieldName} is required`;\n        }\n      }\n      return '';\n    }\n    isSaveDisabled() {\n      const promptTaskControl = this.promptForm.get('promptTask');\n      // Set promptTask validators based on selected tab\n      if (this.selectedTab === 'template') {\n        promptTaskControl?.clearValidators();\n      } else if (this.selectedTab === 'freeform') {\n        promptTaskControl?.setValidators([Validators.required]);\n      }\n      promptTaskControl?.updateValueAndValidity({\n        onlySelf: true,\n        emitEvent: false\n      });\n      return this.promptForm.invalid || !this.formChanged;\n    }\n    // Handle success popup confirmation\n    onSuccessConfirm() {\n      this.closeSuccessPopup();\n    }\n    // Hide success popup and navigate if successful\n    closeSuccessPopup() {\n      this.showSuccessPopup = false;\n      this.popupTitle = '';\n      this.popupMessage = '';\n      if (this.submissionSuccess) {\n        this.router.navigate(['/libraries/prompts']);\n      }\n    }\n    toggleAskAvaModal(show = true) {\n      this.showAskAvaModal = show;\n    }\n    onClickGenerate(prompt = this.prompt.value) {\n      const isTabFreeForm = this.selectedTab === 'freeform';\n      const mode = isTabFreeForm ? PromptsModes.useCaseCreation : PromptsModes.agnetJsonCreation;\n      const useCaseIdentifier = mode + USE_CASE_IDENTIFIER;\n      this.isLoading = true;\n      this.promptGenerateService.modelApi(prompt, mode, false, useCaseIdentifier).subscribe({\n        next: res => {\n          if (isTabFreeForm) {\n            this.promptsOutputForm.patchValue({\n              promptTask: res?.response?.choices[0]?.text\n            });\n            console.log(this.promptsOutputForm);\n          } else {\n            let generatedPrompt = {};\n            try {\n              generatedPrompt = JSON.parse(res?.response?.choices[0]?.text);\n            } catch (error) {\n              return;\n            }\n            const {\n              ROLE,\n              GOAL,\n              BACKSTORY,\n              DESCRIPTION,\n              OUTPUT\n            } = generatedPrompt;\n            this.promptsOutputForm.patchValue({\n              role: ROLE,\n              goal: GOAL,\n              backstory: BACKSTORY,\n              description: DESCRIPTION,\n              expectedOutput: OUTPUT\n            });\n          }\n          this.showPromptOutput = true;\n          this.isLoading = false;\n        },\n        error: () => {\n          this.isLoading = false;\n        }\n      });\n    }\n    onUse() {\n      const formValue = this.promptsOutputForm.getRawValue();\n      this.promptCache[this.selectedTab] = this.prompt.value || '';\n      this.promptForm.patchValue(formValue);\n      this.promptsOutputForm.reset();\n      this.prompt.reset('');\n      this.showPromptOutput = false;\n      this.toggleAskAvaModal(false);\n    }\n    onReset() {\n      this.promptsOutputForm.reset();\n      this.showPromptOutput = false;\n    }\n    onCancle() {\n      this.prompt.reset('');\n      this.onReset();\n      this.toggleAskAvaModal(false);\n    }\n    onClose() {\n      this.toggleAskAvaModal(false);\n      this.onCancle();\n    }\n    onCancel() {\n      this.router.navigate(['/libraries/prompts']);\n    }\n    get actionButtonLabel() {\n      return this.isEditMode ? 'Update' : 'Save';\n    }\n    static ɵfac = function CreatePromptsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CreatePromptsComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.ToolExecutionService), i0.ɵɵdirectiveInject(i4.PromptsService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i5.PromptEnhanceService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CreatePromptsComponent,\n      selectors: [[\"app-create-prompts\"]],\n      viewQuery: function CreatePromptsComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.accordionRef = _t.first);\n        }\n      },\n      decls: 35,\n      vars: 26,\n      consts: [[\"accordion\", \"\"], [1, \"page-title\"], [1, \"create-prompts-container\"], [3, \"formGroup\"], [1, \"form-layout\", 3, \"ngClass\"], [1, \"left-column\"], [1, \"left-header\"], [\"name\", \"panel-left\", 1, \"collapse-icon\", 3, \"click\"], [\"class\", \"left-title\", 4, \"ngIf\"], [\"class\", \"card-content\", 4, \"ngIf\"], [1, \"middle-column\"], [1, \"prompt_header\"], [1, \"section-title\"], [1, \"actions\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [\"label\", \"Back\", \"variant\", \"secondary\", 3, \"userClick\", 4, \"ngIf\"], [\"variant\", \"secondary\", \"size\", \"medium\", \"iconName\", \"WandSparkles\", 3, \"label\", \"userClick\", 4, \"ngIf\"], [\"variant\", \"primary\", \"size\", \"medium\", 3, \"disabled\", \"label\", \"customStyles\", \"userClick\", 4, \"ngIf\"], [1, \"tab-container\"], [1, \"tab-heading\"], [1, \"tabs-wrapper\"], [1, \"pill-tabs-container\"], [3, \"disabled\", \"label\", \"selected\", \"hasDropdown\", \"dropdownOpen\", \"selectEvent\", 4, \"ngFor\", \"ngForOf\"], [1, \"form-container\"], [1, \"tab-content\"], [4, \"ngIf\"], [1, \"regenerate-button-wrapper\"], [\"label\", \"Regenerate\", \"variant\", \"secondary\", \"size\", \"medium\", \"iconName\", \"rotate-cw\", \"iconPosition\", \"right\", 3, \"userClick\", \"customStyles\"], [\"class\", \"optional-sections\", 4, \"ngIf\"], [3, \"oNClickGenerate\", \"oNClickClosed\", \"oNClickUse\", \"oNClickReset\", \"oNClickCancel\", \"show\", \"prompt\", \"isLoading\", \"showOutput\"], [1, \"left-title\"], [1, \"card-content\"], [\"id\", \"promptName\", \"name\", \"promptName\", 3, \"formControl\", \"label\", \"placeholder\", \"error\", \"required\"], [\"id\", \"description\", \"name\", \"description\", \"size\", \"md\", 3, \"label\", \"formControl\", \"placeholder\", \"rows\", \"error\", \"required\"], [\"label\", \"Back\", \"variant\", \"secondary\", 3, \"userClick\"], [\"variant\", \"secondary\", \"size\", \"medium\", \"iconName\", \"WandSparkles\", 3, \"userClick\", \"label\"], [\"variant\", \"primary\", \"size\", \"medium\", 3, \"userClick\", \"disabled\", \"label\", \"customStyles\"], [3, \"selectEvent\", \"disabled\", \"label\", \"selected\", \"hasDropdown\", \"dropdownOpen\"], [1, \"freeform-content-wrapper\"], [\"id\", \"freeformPrompt\", \"name\", \"freeformPrompt\", \"placeholder\", \"Please enter the prompt task\", \"size\", \"md\", 3, \"label\", \"formControl\", \"rows\", \"error\", \"fullWidth\", \"required\"], [1, \"template-form\"], [1, \"fields-row\"], [1, \"field-col\"], [1, \"role-dropdown-row\"], [\"formControlName\", \"role\", \"id\", \"role\", \"name\", \"role\", 1, \"prompt-type-dropdown-flex\", \"role-dropdown-style\", 3, \"formControl\", \"label\", \"disabled\", \"error\", \"required\"], [\"formControlName\", \"promptType\", \"label\", \"Prompt Type\", \"id\", \"promptType\", \"name\", \"promptType\", \"placeholder\", \"Select Prompt Type\", 1, \"prompt-type-dropdown-flex\", \"role-dropdown-style\", 3, \"selectionChange\", \"options\", \"selectedValue\", \"required\", \"error\", \"disabled\"], [\"label\", \"Goal\", \"id\", \"goal\", \"size\", \"md\", 3, \"formControl\", \"name\", \"placeholder\", \"rows\", \"error\", \"fullWidth\", \"required\"], [\"id\", \"templateDescription\", \"name\", \"templateDescription\", \"size\", \"md\", 3, \"label\", \"formControl\", \"placeholder\", \"rows\", \"fullWidth\", \"error\", \"required\"], [\"id\", \"backstory\", \"name\", \"backstory\", \"size\", \"md\", 3, \"label\", \"formControl\", \"placeholder\", \"rows\", \"fullWidth\", \"error\", \"required\"], [\"id\", \"expectedOutput\", \"name\", \"expectedOutput\", \"size\", \"md\", 3, \"formControl\", \"label\", \"placeholder\", \"rows\", \"fullWidth\", \"error\", \"required\"], [\"class\", \"fields-row\", 4, \"ngIf\"], [\"id\", \"intermediateSteps\", \"name\", \"intermediateSteps\", \"size\", \"md\", 3, \"label\", \"formControl\", \"placeholder\", \"rows\", \"fullWidth\", \"error\", \"required\"], [1, \"optional-sections\"], [\"class\", \"examples-accordion-wrapper\", 4, \"ngIf\"], [1, \"examples-accordion-wrapper\"], [\"iconClosed\", \"chevron-down\", \"iconOpen\", \"chevron-up\", \"iconPosition\", \"right\", \"type\", \"default\", 3, \"expanded\", \"animation\", \"controlled\"], [\"header\", \"\"], [\"content\", \"\"], [1, \"example\"], [\"formArrayName\", \"examples\", 1, \"example-content\"], [\"class\", \"example-group\", 3, \"formGroupName\", 4, \"ngFor\", \"ngForOf\"], [1, \"example-actions\"], [\"variant\", \"secondary\", \"size\", \"medium\", \"iconName\", \"plus\", \"iconPosition\", \"right\", 3, \"userClick\", \"label\"], [1, \"add-consideration\"], [\"id\", \"addConsideration\", \"name\", \"addConsideration\", \"variant\", \"primary\", \"size\", \"md\", 3, \"formControl\", \"label\", \"placeholder\", \"rows\", \"fullWidth\"], [1, \"example-group\", 3, \"formGroupName\"], [\"label\", \"Input\", \"id\", \"input\", \"variant\", \"primary\", \"size\", \"md\", 3, \"formControlName\", \"name\", \"placeholder\", \"rows\", \"fullWidth\"], [\"label\", \"Output\", \"id\", \"output\", \"variant\", \"primary\", \"size\", \"md\", 3, \"formControlName\", \"name\", \"placeholder\", \"rows\", \"fullWidth\"], [\"class\", \"example-remove\", 4, \"ngIf\"], [1, \"example-remove\"], [\"variant\", \"secondary\", \"size\", \"medium\", \"iconName\", \"trash\", \"iconPosition\", \"left\", 3, \"userClick\", \"label\"], [\"id\", \"freeformPrompt\", \"name\", \"freeformPrompt\", \"placeholder\", \"Please enter the prompt task\", \"size\", \"md\", 3, \"label\", \"formControl\", \"rows\", \"fullWidth\", \"readonly\"], [1, \"row\", \"g-2\"], [1, \"col-md-6\"], [\"id\", \"role\", \"name\", \"role\", \"variant\", \"primary\", \"size\", \"md\", 3, \"formControl\", \"label\", \"placeholder\", \"readonly\"], [\"label\", \"Goal\", \"id\", \"goal\", \"name\", \"goal\", \"size\", \"md\", 3, \"formControl\", \"placeholder\", \"rows\", \"fullWidth\", \"readonly\"], [\"id\", \"templateDescription\", \"name\", \"templateDescription\", \"size\", \"md\", 3, \"label\", \"formControl\", \"placeholder\", \"rows\", \"fullWidth\", \"readonly\"], [\"id\", \"backstory\", \"name\", \"backstory\", \"size\", \"md\", 3, \"label\", \"formControl\", \"placeholder\", \"rows\", \"fullWidth\", \"readonly\"], [\"id\", \"expectedOutput\", \"name\", \"expectedOutput\", \"size\", \"md\", 3, \"formControl\", \"label\", \"placeholder\", \"rows\", \"fullWidth\", \"readonly\"], [\"class\", \"col-md-12\", 4, \"ngIf\"], [1, \"col-md-12\"], [\"id\", \"intermediateSteps\", \"name\", \"intermediateSteps\", \"size\", \"md\", 3, \"label\", \"formControl\", \"placeholder\", \"rows\", \"fullWidth\", \"readonly\"]],\n      template: function CreatePromptsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"p\", 1);\n          i0.ɵɵtext(1, \"Prompt Configuration\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(2, \"div\", 2)(3, \"form\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"lucide-icon\", 7);\n          i0.ɵɵlistener(\"click\", function CreatePromptsComponent_Template_lucide_icon_click_7_listener() {\n            return ctx.toggleLeftPanel();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(8, CreatePromptsComponent_span_8_Template, 2, 0, \"span\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(9, CreatePromptsComponent_div_9_Template, 3, 12, \"div\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"div\", 10)(11, \"div\", 11)(12, \"h3\", 12);\n          i0.ɵɵtext(13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"div\", 13);\n          i0.ɵɵtemplate(15, CreatePromptsComponent_ava_button_15_Template, 1, 0, \"ava-button\", 14)(16, CreatePromptsComponent_ava_button_16_Template, 1, 1, \"ava-button\", 15)(17, CreatePromptsComponent_ava_button_17_Template, 1, 4, \"ava-button\", 16);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"div\", 17)(19, \"h3\", 18);\n          i0.ɵɵtext(20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"div\", 19)(22, \"div\", 20);\n          i0.ɵɵtemplate(23, CreatePromptsComponent_shared_nav_item_23_Template, 1, 5, \"shared-nav-item\", 21);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(24, \"div\", 22)(25, \"div\", 23);\n          i0.ɵɵtemplate(26, CreatePromptsComponent_ng_container_26_Template, 3, 6, \"ng-container\", 24)(27, CreatePromptsComponent_ng_container_27_Template, 18, 39, \"ng-container\", 24);\n          i0.ɵɵelementStart(28, \"div\", 25)(29, \"ava-button\", 26);\n          i0.ɵɵlistener(\"userClick\", function CreatePromptsComponent_Template_ava_button_userClick_29_listener() {\n            return ctx.regenerate();\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(30, CreatePromptsComponent_div_30_Template, 2, 1, \"div\", 27);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(31, \"app-ask-ava-wrapper\", 28);\n          i0.ɵɵlistener(\"oNClickGenerate\", function CreatePromptsComponent_Template_app_ask_ava_wrapper_oNClickGenerate_31_listener() {\n            return ctx.onClickGenerate();\n          })(\"oNClickClosed\", function CreatePromptsComponent_Template_app_ask_ava_wrapper_oNClickClosed_31_listener() {\n            return ctx.onClose();\n          })(\"oNClickUse\", function CreatePromptsComponent_Template_app_ask_ava_wrapper_oNClickUse_31_listener() {\n            return ctx.onUse();\n          })(\"oNClickReset\", function CreatePromptsComponent_Template_app_ask_ava_wrapper_oNClickReset_31_listener() {\n            return ctx.onReset();\n          })(\"oNClickCancel\", function CreatePromptsComponent_Template_app_ask_ava_wrapper_oNClickCancel_31_listener() {\n            return ctx.onCancle();\n          });\n          i0.ɵɵelementStart(32, \"form\", 3);\n          i0.ɵɵtemplate(33, CreatePromptsComponent_ng_container_33_Template, 2, 5, \"ng-container\", 24)(34, CreatePromptsComponent_ng_container_34_Template, 13, 28, \"ng-container\", 24);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"formGroup\", ctx.promptForm);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(23, _c1, ctx.isExecuteMode && ctx.showChatInterface));\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"collapsed\", ctx.isLeftCollapsed);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLeftCollapsed);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLeftCollapsed);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.promptLabels.promptConfiguration);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isViewMode);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isViewMode);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isViewMode);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.promptLabels.chooseTheTypeOfPrompt);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.promptTabs);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedTab === \"freeform\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedTab === \"template\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"customStyles\", i0.ɵɵpureFunction0(25, _c2));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedTab === \"template\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"show\", ctx.showAskAvaModal)(\"prompt\", ctx.prompt)(\"isLoading\", ctx.isLoading)(\"showOutput\", ctx.showPromptOutput);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"formGroup\", ctx.promptsOutputForm);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedTab === \"freeform\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedTab === \"template\");\n        }\n      },\n      dependencies: [CommonModule, i6.NgClass, i6.NgForOf, i6.NgIf, ReactiveFormsModule, i1.ɵNgNoValidate, i1.NgControlStatus, i1.NgControlStatusGroup, i1.RequiredValidator, i1.FormControlDirective, i1.FormGroupDirective, i1.FormControlName, i1.FormGroupName, i1.FormArrayName, AvaTextboxComponent, AvaTextareaComponent, ButtonComponent, SharedNavItemComponent, AccordionComponent, DropdownComponent, LucideAngularModule, i7.LucideAngularComponent, AskAvaWrapperComponent],\n      styles: [\"@charset \\\"UTF-8\\\";\\n.role-dropdown-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  align-items: flex-end;\\n  margin-top: 8px;\\n}\\n\\n.role-dropdown-style[_ngcontent-%COMP%] {\\n  flex: 1 1 0;\\n}\\n\\n.create-prompts-container[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100vh;\\n  min-height: 0;\\n  display: flex;\\n  flex-direction: column;\\n  background-color: transparent;\\n}\\n\\n.page-title[_ngcontent-%COMP%] {\\n  font-weight: 700;\\n  font-size: 24px;\\n  font-weight: bold;\\n}\\n\\n.form-layout[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: row;\\n  gap: 0;\\n  padding: 0;\\n  flex: 1 1 0;\\n  min-height: 0;\\n  overflow: hidden;\\n  height: 100%;\\n  border: 1px solid #e1e4e8;\\n  background: #ffffff;\\n}\\n\\n.left-column[_ngcontent-%COMP%], \\n.middle-column[_ngcontent-%COMP%], \\n.chat-column[_ngcontent-%COMP%] {\\n  box-sizing: border-box;\\n}\\n\\n.left-column[_ngcontent-%COMP%] {\\n  width: 340px;\\n  min-width: 60px;\\n  max-width: 340px;\\n  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  height: 100vh;\\n  overflow: hidden;\\n  position: relative;\\n  background: #f8f9fa;\\n  border-right: 1px solid #e1e4e8;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.left-column.collapsed[_ngcontent-%COMP%] {\\n  width: 48px;\\n  min-width: 48px;\\n  max-width: 48px;\\n}\\n\\n.collapse-icon[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 16px;\\n  right: 8px;\\n  width: 24px;\\n  height: 24px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background: #fff;\\n  border-radius: 50%;\\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);\\n  cursor: pointer;\\n  z-index: 2;\\n  font-size: 18px;\\n  border: 1px solid #e1e4e8;\\n}\\n\\n.left-column.collapsed[_ngcontent-%COMP%]   .collapse-icon[_ngcontent-%COMP%] {\\n  right: 12px;\\n}\\n\\n.left-column[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%] {\\n  flex: 1 1 0;\\n  min-height: 0;\\n  overflow-y: auto;\\n  padding: 1rem;\\n}\\n\\n.middle-column[_ngcontent-%COMP%] {\\n  flex: 1 1 0;\\n  min-width: 0;\\n  min-height: 0;\\n  display: flex;\\n  flex-direction: column;\\n  padding: 0.5rem 1rem;\\n}\\n\\n.chat-column[_ngcontent-%COMP%] {\\n  width: 400px;\\n  min-width: 400px;\\n  background-color: #f8f8f8;\\n  border-left: 1px solid #ddd;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.prompt_header[_ngcontent-%COMP%] {\\n  width: 100%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  margin-bottom: 20px;\\n}\\n\\n.freeform-content-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n}\\n\\n  ava-textarea.input-box {\\n  width: 100%;\\n  min-height: 200px;\\n  height: auto;\\n  resize: vertical;\\n}\\n\\n[_nghost-%COMP%]     .ava-textarea--primary .ava-textarea__container {\\n  border-color: #e1e4e8 !important;\\n}\\n\\n@media (max-width: 600px) {\\n    ava-textarea.input-box {\\n    min-height: 150px;\\n  }\\n}\\n@media (min-width: 1200px) {\\n    ava-textarea.input-box {\\n    min-height: 250px;\\n  }\\n}\\n.regenerate-button-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  margin-top: 1rem;\\n  margin-bottom: 0;\\n}\\n\\n.role-textbox[_ngcontent-%COMP%] {\\n  width: 40%;\\n  flex-shrink: 0;\\n}\\n\\n.fields-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 20px;\\n  margin-bottom: 20px;\\n}\\n\\n.fields-row1[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 20px;\\n  margin-bottom: 20px;\\n  height: 10rem;\\n  border-bottom: 1px solid #e1e4e8;\\n}\\n\\n.add-consideration[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n  height: 10rem;\\n  padding-top: 20px;\\n  border-top: 1px solid #e1e4e8;\\n}\\n\\n.field-col[_ngcontent-%COMP%] {\\n  flex: 1 1 45%;\\n  min-width: 400px;\\n}\\n\\n.optional-sections[_ngcontent-%COMP%] {\\n  margin-top: 0;\\n}\\n.optional-sections[_ngcontent-%COMP%]   ava-accordion[_ngcontent-%COMP%]     .accordion-container .accordion-body {\\n  padding: 0%;\\n}\\n\\n.form-container[_ngcontent-%COMP%] {\\n  overflow-y: auto;\\n  scroll-width: none;\\n}\\n\\n.accordion-section[_ngcontent-%COMP%] {\\n  border: 1px solid #e1e4e8;\\n  border-radius: 6px;\\n  margin-bottom: 20px;\\n}\\n\\n.chat-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  min-height: 400px;\\n  border-radius: 12px;\\n  overflow: hidden;\\n  position: relative;\\n}\\n\\n.playground-title[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 500;\\n  margin-bottom: 10px;\\n}\\n\\n.chat-column-content[_ngcontent-%COMP%] {\\n  flex: 1 1 0;\\n  min-height: 0;\\n  overflow-y: auto;\\n  scrollbar-width: none;\\n  -ms-overflow-style: none;\\n}\\n\\n.chat-column-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  display: none;\\n}\\n\\n.tabs-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n}\\n\\n.tab-heading[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  margin-bottom: 12px;\\n  text-align: center;\\n}\\n\\n.tab-container[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n\\n\\n\\n.pill-tabs-container[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  border-radius: 999px;\\n  overflow: hidden;\\n  border: 1px solid #e1e4e8;\\n  background-color: #f8f9fa;\\n  padding: 4px;\\n  gap: 0;\\n}\\n\\n.pill-tab-button[_ngcontent-%COMP%] {\\n  border-radius: 999px;\\n  margin: 0;\\n  border: none;\\n  font-weight: 500;\\n  padding: 10px 20px;\\n  background-color: transparent;\\n  color: #6c757d;\\n  transition: all 0.2s ease;\\n  cursor: pointer;\\n  font-size: 14px;\\n  min-width: 80px;\\n  text-align: center;\\n}\\n\\n.pill-tab-button[_ngcontent-%COMP%]:hover:not(.active) {\\n  background-color: rgba(101, 102, 205, 0.05);\\n  color: #6566CD;\\n}\\n\\n.pill-tab-button.active[_ngcontent-%COMP%] {\\n  background-color: #6566CD;\\n  color: white;\\n  font-weight: 600;\\n  box-shadow: 0 2px 4px rgba(101, 102, 205, 0.3);\\n}\\n\\n.item-icon[_ngcontent-%COMP%] {\\n  display: none !important;\\n}\\n\\n.example-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 1.5rem;\\n}\\n.example-content[_ngcontent-%COMP%]   .example-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  align-items: flex-start;\\n  gap: 1rem;\\n  padding-bottom: 1.5rem;\\n  border-bottom: 1px solid #e1e4e8;\\n  position: relative;\\n}\\n.example-content[_ngcontent-%COMP%]   .example-group[_ngcontent-%COMP%]   .field-col[_ngcontent-%COMP%] {\\n  flex: 1 1 45%;\\n}\\n.example-content[_ngcontent-%COMP%]   .example-group[_ngcontent-%COMP%]   .example-remove[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n  margin-top: auto;\\n}\\n.example-content[_ngcontent-%COMP%]   .example-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-start;\\n  margin-top: 1rem;\\n}\\n\\n.examples-accordion-wrapper[_ngcontent-%COMP%] {\\n  margin-top: 1rem;\\n}\\n\\n.examples-accordion-wrapper[_ngcontent-%COMP%]     .accordion-header {\\n  min-width: 100% !important;\\n}\\n\\n[_nghost-%COMP%]     ava-accordion .accordion-container {\\n  max-width: none !important;\\n  width: 100% !important;\\n}\\n\\n[_nghost-%COMP%]     ava-accordion .accordion-container .accordion-content {\\n  max-height: 100%;\\n  overflow-y: auto;\\n}\\n\\n  #description .ava-textarea__label {\\n  margin-top: 16px;\\n}\\n\\n.left-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  height: 48px;\\n  padding: 0 16px;\\n  background: #fff;\\n  border-bottom: 1px solid #e1e4e8;\\n  z-index: 2;\\n}\\n\\n.right-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  height: 48px;\\n  padding: 0 16px;\\n  background: #fff;\\n  border-bottom: 1px solid #e1e4e8;\\n  z-index: 2;\\n}\\n\\n.tab-content[_ngcontent-%COMP%] {\\n  flex: 1 1 0;\\n  min-height: 0;\\n}\\n\\n.tab-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  display: none;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3Byb2plY3RzL3NoYXJlZC9wYWdlcy9saWJyYXJpZXMvcHJvbXB0cy9jcmVhdGUtcHJvbXB0cy9jcmVhdGUtcHJvbXB0cy5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSxnQkFBZ0I7QUFDaEI7RUFDRSxhQUFBO0VBQ0EsU0FBQTtFQUNBLHFCQUFBO0VBQ0EsZUFBQTtBQUNGOztBQUdBO0VBQ0UsV0FBQTtBQUFGOztBQUdBO0VBQ0UsV0FBQTtFQUNBLGFBQUE7RUFDQSxhQUFBO0VBQ0EsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsNkJBQUE7QUFBRjs7QUFHQTtFQUNFLGdCQUFBO0VBQ0EsZUFBQTtFQUNBLGlCQUFBO0FBQUY7O0FBR0E7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxNQUFBO0VBQ0EsVUFBQTtFQUNBLFdBQUE7RUFDQSxhQUFBO0VBQ0EsZ0JBQUE7RUFDQSxZQUFBO0VBQ0EseUJBQUE7RUFDQSxtQkFBQTtBQUFGOztBQUdBOzs7RUFJRSxzQkFBQTtBQURGOztBQUlBO0VBQ0UsWUFBQTtFQUNBLGVBQUE7RUFDQSxnQkFBQTtFQUNBLG1EQUFBO0VBQ0EsYUFBQTtFQUNBLGdCQUFBO0VBQ0Esa0JBQUE7RUFDQSxtQkFBQTtFQUNBLCtCQUFBO0VBQ0EsYUFBQTtFQUNBLHNCQUFBO0FBREY7O0FBR0E7RUFDRSxXQUFBO0VBQ0EsZUFBQTtFQUNBLGVBQUE7QUFBRjs7QUFFQTtFQUNFLGtCQUFBO0VBQ0EsU0FBQTtFQUNBLFVBQUE7RUFDQSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxrQkFBQTtFQUNBLHlDQUFBO0VBQ0EsZUFBQTtFQUNBLFVBQUE7RUFDQSxlQUFBO0VBQ0EseUJBQUE7QUFDRjs7QUFDQTtFQUNFLFdBQUE7QUFFRjs7QUFBQTtFQUNFLFdBQUE7RUFDQSxhQUFBO0VBQ0EsZ0JBQUE7RUFDQSxhQUFBO0FBR0Y7O0FBQUE7RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGFBQUE7RUFDQSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSxvQkFBQTtBQUdGOztBQUFBO0VBQ0UsWUFBQTtFQUNBLGdCQUFBO0VBQ0EseUJBQUE7RUFDQSwyQkFBQTtFQUNBLGFBQUE7RUFDQSxzQkFBQTtBQUdGOztBQUFBO0VBQ0UsV0FBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLDhCQUFBO0VBQ0EsbUJBQUE7QUFHRjs7QUFBQTtFQUNFLGFBQUE7RUFDQSxzQkFBQTtFQUNBLFNBQUE7QUFHRjs7QUFBQTtFQUNFLFdBQUE7RUFDQSxpQkFBQTtFQUNBLFlBQUE7RUFDQSxnQkFBQTtBQUdGOztBQUFBO0VBQ0UsZ0NBQUE7QUFHRjs7QUFDQTtFQUNFO0lBQ0UsaUJBQUE7RUFFRjtBQUNGO0FBQ0E7RUFDRTtJQUNFLGlCQUFBO0VBQ0Y7QUFDRjtBQUVBO0VBQ0UsYUFBQTtFQUNBLHlCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxnQkFBQTtBQUFGOztBQVlBO0VBQ0UsVUFBQTtFQUNBLGNBQUE7QUFURjs7QUFhQTtFQUNFLGFBQUE7RUFDQSxlQUFBO0VBQ0EsU0FBQTtFQUNBLG1CQUFBO0FBVkY7O0FBYUE7RUFDRSxhQUFBO0VBQ0EsZUFBQTtFQUNBLFNBQUE7RUFDQSxtQkFBQTtFQUNBLGFBQUE7RUFDQSxnQ0FBQTtBQVZGOztBQWFBO0VBQ0UsZ0JBQUE7RUFDQSxhQUFBO0VBQ0EsaUJBQUE7RUFDQSw2QkFBQTtBQVZGOztBQWVBO0VBQ0UsYUFBQTtFQUNBLGdCQUFBO0FBWkY7O0FBZUE7RUFDRSxhQUFBO0FBWkY7QUFjRTtFQUNFLFdBQUE7QUFaSjs7QUFvQkE7RUFDRSxnQkFBQTtFQUNBLGtCQUFBO0FBakJGOztBQW9CQTtFQUNFLHlCQUFBO0VBQ0Esa0JBQUE7RUFDQSxtQkFBQTtBQWpCRjs7QUFvQkE7RUFDRSxPQUFBO0VBQ0EsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsaUJBQUE7RUFDQSxtQkFBQTtFQUNBLGdCQUFBO0VBQ0Esa0JBQUE7QUFqQkY7O0FBb0JBO0VBQ0UsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsbUJBQUE7QUFqQkY7O0FBb0JBO0VBQ0UsV0FBQTtFQUNBLGFBQUE7RUFDQSxnQkFBQTtFQUNBLHFCQUFBO0VBQ0Esd0JBQUE7QUFqQkY7O0FBbUJBO0VBQ0UsYUFBQTtBQWhCRjs7QUFtQkE7RUFDRSxhQUFBO0VBQ0EsdUJBQUE7QUFoQkY7O0FBbUJBO0VBQ0UsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsbUJBQUE7RUFDQSxrQkFBQTtBQWhCRjs7QUFtQkE7RUFDRSxtQkFBQTtBQWhCRjs7QUFtQkEsOEJBQUE7QUFDQTtFQUNFLG9CQUFBO0VBQ0Esb0JBQUE7RUFDQSxnQkFBQTtFQUNBLHlCQUFBO0VBQ0EseUJBQUE7RUFDQSxZQUFBO0VBQ0EsTUFBQTtBQWhCRjs7QUFtQkE7RUFDRSxvQkFBQTtFQUNBLFNBQUE7RUFDQSxZQUFBO0VBQ0EsZ0JBQUE7RUFDQSxrQkFBQTtFQUNBLDZCQUFBO0VBQ0EsY0FBQTtFQUNBLHlCQUFBO0VBQ0EsZUFBQTtFQUNBLGVBQUE7RUFDQSxlQUFBO0VBQ0Esa0JBQUE7QUFoQkY7O0FBbUJBO0VBQ0UsMkNBQUE7RUFDQSxjQUFBO0FBaEJGOztBQW1CQTtFQUNFLHlCQUFBO0VBQ0EsWUFBQTtFQUNBLGdCQUFBO0VBQ0EsOENBQUE7QUFoQkY7O0FBbUJBO0VBQ0Usd0JBQUE7QUFoQkY7O0FBbUJBO0VBQ0UsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsV0FBQTtBQWhCRjtBQWtCRTtFQUNFLGFBQUE7RUFDQSxlQUFBO0VBQ0EsdUJBQUE7RUFDQSxTQUFBO0VBQ0Esc0JBQUE7RUFDQSxnQ0FBQTtFQUNBLGtCQUFBO0FBaEJKO0FBa0JJO0VBQ0UsYUFBQTtBQWhCTjtBQW1CSTtFQUNFLGlCQUFBO0VBQ0EsZ0JBQUE7QUFqQk47QUFxQkU7RUFDRSxhQUFBO0VBQ0EsMkJBQUE7RUFDQSxnQkFBQTtBQW5CSjs7QUF1QkE7RUFDRSxnQkFBQTtBQXBCRjs7QUF1QkE7RUFDRSwwQkFBQTtBQXBCRjs7QUF1QkE7RUFDRSwwQkFBQTtFQUNBLHNCQUFBO0FBcEJGOztBQXVCQTtFQUNFLGdCQUFBO0VBQ0EsZ0JBQUE7QUFwQkY7O0FBdUJBO0VBQ0UsZ0JBQUE7QUFwQkY7O0FBdUJBO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsWUFBQTtFQUNBLGVBQUE7RUFDQSxnQkFBQTtFQUNBLGdDQUFBO0VBQ0EsVUFBQTtBQXBCRjs7QUF1QkE7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxZQUFBO0VBQ0EsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsZ0NBQUE7RUFDQSxVQUFBO0FBcEJGOztBQXVCQTtFQUNFLFdBQUE7RUFDQSxhQUFBO0FBcEJGOztBQXlCQTtFQUNFLGFBQUE7QUF0QkYiLCJzb3VyY2VzQ29udGVudCI6WyIvLyBMYXlvdXQgZm9yIHJvbGUgYW5kIGRyb3Bkb3duIHNpZGUgYnkgc2lkZVxyXG4ucm9sZS1kcm9wZG93bi1yb3cge1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgZ2FwOiAxNnB4O1xyXG4gIGFsaWduLWl0ZW1zOiBmbGV4LWVuZDtcclxuICBtYXJnaW4tdG9wOiA4cHg7XHJcbn1cclxuXHJcbi8vIE1hdGNoIHRleHRib3ggYW5kIGRyb3Bkb3duIHN0eWxlXHJcbi5yb2xlLWRyb3Bkb3duLXN0eWxlIHtcclxuICBmbGV4OiAxIDEgMDtcclxufVxyXG5cclxuLmNyZWF0ZS1wcm9tcHRzLWNvbnRhaW5lciB7XHJcbiAgd2lkdGg6IDEwMCU7XHJcbiAgaGVpZ2h0OiAxMDB2aDtcclxuICBtaW4taGVpZ2h0OiAwO1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiB0cmFuc3BhcmVudDtcclxufVxyXG5cclxuLnBhZ2UtdGl0bGUge1xyXG4gIGZvbnQtd2VpZ2h0OiA3MDA7XHJcbiAgZm9udC1zaXplOiAyNHB4O1xyXG4gIGZvbnQtd2VpZ2h0OiBib2xkO1xyXG59XHJcblxyXG4uZm9ybS1sYXlvdXQge1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgZmxleC1kaXJlY3Rpb246IHJvdztcclxuICBnYXA6IDA7XHJcbiAgcGFkZGluZzogMDtcclxuICBmbGV4OiAxIDEgMDtcclxuICBtaW4taGVpZ2h0OiAwO1xyXG4gIG92ZXJmbG93OiBoaWRkZW47XHJcbiAgaGVpZ2h0OiAxMDAlO1xyXG4gIGJvcmRlcjogMXB4IHNvbGlkICNlMWU0ZTg7XHJcbiAgYmFja2dyb3VuZDogI2ZmZmZmZjtcclxufVxyXG5cclxuLmxlZnQtY29sdW1uLFxyXG4ubWlkZGxlLWNvbHVtbixcclxuLmNoYXQtY29sdW1uIHtcclxuICAvL3BhZGRpbmc6IDIwcHg7XHJcbiAgYm94LXNpemluZzogYm9yZGVyLWJveDtcclxufVxyXG5cclxuLmxlZnQtY29sdW1uIHtcclxuICB3aWR0aDogMzQwcHg7XHJcbiAgbWluLXdpZHRoOiA2MHB4O1xyXG4gIG1heC13aWR0aDogMzQwcHg7XHJcbiAgdHJhbnNpdGlvbjogd2lkdGggMC4zcyBjdWJpYy1iZXppZXIoMC40LDAsMC4yLDEpO1xyXG4gIGhlaWdodDogMTAwdmg7XHJcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcclxuICBwb3NpdGlvbjogcmVsYXRpdmU7XHJcbiAgYmFja2dyb3VuZDogI2Y4ZjlmYTtcclxuICBib3JkZXItcmlnaHQ6IDFweCBzb2xpZCAjZTFlNGU4O1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxufVxyXG4ubGVmdC1jb2x1bW4uY29sbGFwc2VkIHtcclxuICB3aWR0aDogNDhweDtcclxuICBtaW4td2lkdGg6IDQ4cHg7XHJcbiAgbWF4LXdpZHRoOiA0OHB4O1xyXG59XHJcbi5jb2xsYXBzZS1pY29uIHtcclxuICBwb3NpdGlvbjogYWJzb2x1dGU7XHJcbiAgdG9wOiAxNnB4O1xyXG4gIHJpZ2h0OiA4cHg7XHJcbiAgd2lkdGg6IDI0cHg7XHJcbiAgaGVpZ2h0OiAyNHB4O1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxuICBiYWNrZ3JvdW5kOiAjZmZmO1xyXG4gIGJvcmRlci1yYWRpdXM6IDUwJTtcclxuICBib3gtc2hhZG93OiAwIDFweCA0cHggcmdiYSgwLDAsMCwwLjA2KTtcclxuICBjdXJzb3I6IHBvaW50ZXI7XHJcbiAgei1pbmRleDogMjtcclxuICBmb250LXNpemU6IDE4cHg7XHJcbiAgYm9yZGVyOiAxcHggc29saWQgI2UxZTRlODtcclxufVxyXG4ubGVmdC1jb2x1bW4uY29sbGFwc2VkIC5jb2xsYXBzZS1pY29uIHtcclxuICByaWdodDogMTJweDtcclxufVxyXG4ubGVmdC1jb2x1bW4gLmNhcmQtY29udGVudCB7XHJcbiAgZmxleDogMSAxIDA7XHJcbiAgbWluLWhlaWdodDogMDtcclxuICBvdmVyZmxvdy15OiBhdXRvO1xyXG4gIHBhZGRpbmc6IDFyZW07XHJcbn1cclxuXHJcbi5taWRkbGUtY29sdW1uIHtcclxuICBmbGV4OiAxIDEgMDtcclxuICBtaW4td2lkdGg6IDA7XHJcbiAgbWluLWhlaWdodDogMDtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbiAgcGFkZGluZzogMC41cmVtIDFyZW07O1xyXG59XHJcblxyXG4uY2hhdC1jb2x1bW4ge1xyXG4gIHdpZHRoOiA0MDBweDtcclxuICBtaW4td2lkdGg6IDQwMHB4O1xyXG4gIGJhY2tncm91bmQtY29sb3I6ICNmOGY4Zjg7XHJcbiAgYm9yZGVyLWxlZnQ6IDFweCBzb2xpZCAjZGRkO1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxufVxyXG5cclxuLnByb21wdF9oZWFkZXIge1xyXG4gIHdpZHRoOiAxMDAlO1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XHJcbiAgbWFyZ2luLWJvdHRvbTogMjBweDtcclxufVxyXG5cclxuLmZyZWVmb3JtLWNvbnRlbnQtd3JhcHBlciB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gIGdhcDogMTZweDtcclxufVxyXG5cclxuOjpuZy1kZWVwIGF2YS10ZXh0YXJlYS5pbnB1dC1ib3gge1xyXG4gIHdpZHRoOiAxMDAlO1xyXG4gIG1pbi1oZWlnaHQ6IDIwMHB4O1xyXG4gIGhlaWdodDogYXV0bztcclxuICByZXNpemU6IHZlcnRpY2FsO1xyXG59XHJcblxyXG46aG9zdCA6Om5nLWRlZXAgLmF2YS10ZXh0YXJlYS0tcHJpbWFyeSAuYXZhLXRleHRhcmVhX19jb250YWluZXIge1xyXG4gIGJvcmRlci1jb2xvcjogI2UxZTRlOCAhaW1wb3J0YW50OyAvLyBZb3VyIG92ZXJyaWRlIGNvbG9yXHJcbn1cclxuXHJcblxyXG5AbWVkaWEgKG1heC13aWR0aDogNjAwcHgpIHtcclxuICA6Om5nLWRlZXAgYXZhLXRleHRhcmVhLmlucHV0LWJveCB7XHJcbiAgICBtaW4taGVpZ2h0OiAxNTBweDtcclxuICB9XHJcbn1cclxuXHJcbkBtZWRpYSAobWluLXdpZHRoOiAxMjAwcHgpIHtcclxuICA6Om5nLWRlZXAgYXZhLXRleHRhcmVhLmlucHV0LWJveCB7XHJcbiAgICBtaW4taGVpZ2h0OiAyNTBweDtcclxuICB9XHJcbn1cclxuXHJcbi5yZWdlbmVyYXRlLWJ1dHRvbi13cmFwcGVyIHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGp1c3RpZnktY29udGVudDogZmxleC1lbmQ7XHJcbiAgbWFyZ2luLXRvcDogMXJlbTtcclxuICBtYXJnaW4tYm90dG9tOiAwO1xyXG59XHJcblxyXG4vLyAucmVnZW5lcmF0ZS1idXR0b24ge1xyXG4vLyAgIGRpc3BsYXk6IGlubGluZS1mbGV4O1xyXG4vLyAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbi8vICAgZ2FwOiAwcHg7XHJcbi8vICAgYm9yZGVyLXJhZGl1czogNnB4O1xyXG4vLyAgIGJvcmRlcjogbm9uZTtcclxuLy8gICBjdXJzb3I6IHBvaW50ZXI7XHJcbi8vIH1cclxuXHJcbi5yb2xlLXRleHRib3gge1xyXG4gIHdpZHRoOiA0MCU7XHJcbiAgZmxleC1zaHJpbms6IDA7XHJcblxyXG59XHJcblxyXG4uZmllbGRzLXJvdyB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBmbGV4LXdyYXA6IHdyYXA7XHJcbiAgZ2FwOiAyMHB4O1xyXG4gIG1hcmdpbi1ib3R0b206IDIwcHg7XHJcbn1cclxuXHJcbi5maWVsZHMtcm93MSB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBmbGV4LXdyYXA6IHdyYXA7XHJcbiAgZ2FwOiAyMHB4O1xyXG4gIG1hcmdpbi1ib3R0b206IDIwcHg7XHJcbiAgaGVpZ2h0OiAxMHJlbTtcclxuICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2UxZTRlODtcclxufVxyXG5cclxuLmFkZC1jb25zaWRlcmF0aW9uIHtcclxuICBtYXJnaW4tdG9wOiAyMHB4O1xyXG4gIGhlaWdodDogMTByZW07XHJcbiAgcGFkZGluZy10b3A6IDIwcHg7XHJcbiAgYm9yZGVyLXRvcDogMXB4IHNvbGlkICNlMWU0ZTg7XHJcblxyXG59XHJcblxyXG5cclxuLmZpZWxkLWNvbCB7XHJcbiAgZmxleDogMSAxIDQ1JTtcclxuICBtaW4td2lkdGg6IDQwMHB4O1xyXG59XHJcblxyXG4ub3B0aW9uYWwtc2VjdGlvbnMge1xyXG4gIG1hcmdpbi10b3A6IDA7XHJcblxyXG4gIGF2YS1hY2NvcmRpb24gOjpuZy1kZWVwIC5hY2NvcmRpb24tY29udGFpbmVyIC5hY2NvcmRpb24tYm9keSB7XHJcbiAgICBwYWRkaW5nOiAwJTtcclxuICB9XHJcblxyXG4gIC8vIGF2YS1hY2NvcmRpb24gOjpuZy1kZWVwIC5hY2NvcmRpb24tY29udGFpbmVyIC5hY2NvcmRpb24tY29udGVudCB7XHJcbiAgLy8gICAvL3BhZGRpbmctdG9wOiAyNnB4O1xyXG4gIC8vIH1cclxufVxyXG5cclxuLmZvcm0tY29udGFpbmVye1xyXG4gIG92ZXJmbG93LXk6YXV0bztcclxuICBzY3JvbGwtd2lkdGg6bm9uZTtcclxufVxyXG5cclxuLmFjY29yZGlvbi1zZWN0aW9uIHtcclxuICBib3JkZXI6IDFweCBzb2xpZCAjZTFlNGU4O1xyXG4gIGJvcmRlci1yYWRpdXM6IDZweDtcclxuICBtYXJnaW4tYm90dG9tOiAyMHB4O1xyXG59XHJcblxyXG4uY2hhdC1jb250YWluZXIge1xyXG4gIGZsZXg6IDE7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gIG1pbi1oZWlnaHQ6IDQwMHB4O1xyXG4gIGJvcmRlci1yYWRpdXM6IDEycHg7XHJcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcclxuICBwb3NpdGlvbjogcmVsYXRpdmU7XHJcbn1cclxuXHJcbi5wbGF5Z3JvdW5kLXRpdGxlIHtcclxuICBmb250LXNpemU6IDE2cHg7XHJcbiAgZm9udC13ZWlnaHQ6IDUwMDtcclxuICBtYXJnaW4tYm90dG9tOiAxMHB4O1xyXG59XHJcblxyXG4uY2hhdC1jb2x1bW4tY29udGVudCB7XHJcbiAgZmxleDogMSAxIDA7XHJcbiAgbWluLWhlaWdodDogMDtcclxuICBvdmVyZmxvdy15OiBhdXRvO1xyXG4gIHNjcm9sbGJhci13aWR0aDogbm9uZTtcclxuICAtbXMtb3ZlcmZsb3ctc3R5bGU6IG5vbmU7XHJcbn1cclxuLmNoYXQtY29sdW1uLWNvbnRlbnQ6Oi13ZWJraXQtc2Nyb2xsYmFyIHtcclxuICBkaXNwbGF5OiBub25lO1xyXG59XHJcblxyXG4udGFicy13cmFwcGVyIHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xyXG59XHJcblxyXG4udGFiLWhlYWRpbmcge1xyXG4gIGZvbnQtc2l6ZTogMTZweDtcclxuICBmb250LXdlaWdodDogNjAwO1xyXG4gIG1hcmdpbi1ib3R0b206IDEycHg7XHJcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xyXG59XHJcblxyXG4udGFiLWNvbnRhaW5lciB7XHJcbiAgbWFyZ2luLWJvdHRvbTogMjBweDtcclxufVxyXG5cclxuLyogw6LCnMKFIEN1c3RvbSBQaWxsIFRhYnMgU3R5bGVzICovXHJcbi5waWxsLXRhYnMtY29udGFpbmVyIHtcclxuICBkaXNwbGF5OiBpbmxpbmUtZmxleDtcclxuICBib3JkZXItcmFkaXVzOiA5OTlweDtcclxuICBvdmVyZmxvdzogaGlkZGVuO1xyXG4gIGJvcmRlcjogMXB4IHNvbGlkICNlMWU0ZTg7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogI2Y4ZjlmYTtcclxuICBwYWRkaW5nOiA0cHg7XHJcbiAgZ2FwOiAwO1xyXG59XHJcblxyXG4ucGlsbC10YWItYnV0dG9uIHtcclxuICBib3JkZXItcmFkaXVzOiA5OTlweDtcclxuICBtYXJnaW46IDA7XHJcbiAgYm9yZGVyOiBub25lO1xyXG4gIGZvbnQtd2VpZ2h0OiA1MDA7XHJcbiAgcGFkZGluZzogMTBweCAyMHB4O1xyXG4gIGJhY2tncm91bmQtY29sb3I6IHRyYW5zcGFyZW50O1xyXG4gIGNvbG9yOiAjNmM3NTdkO1xyXG4gIHRyYW5zaXRpb246IGFsbCAwLjJzIGVhc2U7XHJcbiAgY3Vyc29yOiBwb2ludGVyO1xyXG4gIGZvbnQtc2l6ZTogMTRweDtcclxuICBtaW4td2lkdGg6IDgwcHg7XHJcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xyXG59XHJcblxyXG4ucGlsbC10YWItYnV0dG9uOmhvdmVyOm5vdCguYWN0aXZlKSB7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgxMDEsIDEwMiwgMjA1LCAwLjA1KTtcclxuICBjb2xvcjogIzY1NjZDRDtcclxufVxyXG5cclxuLnBpbGwtdGFiLWJ1dHRvbi5hY3RpdmUge1xyXG4gIGJhY2tncm91bmQtY29sb3I6ICM2NTY2Q0Q7XHJcbiAgY29sb3I6IHdoaXRlO1xyXG4gIGZvbnQtd2VpZ2h0OiA2MDA7XHJcbiAgYm94LXNoYWRvdzogMCAycHggNHB4IHJnYmEoMTAxLCAxMDIsIDIwNSwgMC4zKTtcclxufVxyXG5cclxuLml0ZW0taWNvbiB7XHJcbiAgZGlzcGxheTogbm9uZSAhaW1wb3J0YW50OyAvLyBIaWRlIHRoZSBpY29uXHJcbn1cclxuXHJcbi5leGFtcGxlLWNvbnRlbnQge1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxuICBnYXA6IDEuNXJlbTtcclxuXHJcbiAgLmV4YW1wbGUtZ3JvdXAge1xyXG4gICAgZGlzcGxheTogZmxleDtcclxuICAgIGZsZXgtd3JhcDogd3JhcDtcclxuICAgIGFsaWduLWl0ZW1zOiBmbGV4LXN0YXJ0O1xyXG4gICAgZ2FwOiAxcmVtO1xyXG4gICAgcGFkZGluZy1ib3R0b206IDEuNXJlbTtcclxuICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZTFlNGU4O1xyXG4gICAgcG9zaXRpb246IHJlbGF0aXZlO1xyXG5cclxuICAgIC5maWVsZC1jb2wge1xyXG4gICAgICBmbGV4OiAxIDEgNDUlO1xyXG4gICAgfVxyXG5cclxuICAgIC5leGFtcGxlLXJlbW92ZSB7XHJcbiAgICAgIG1hcmdpbi1sZWZ0OiBhdXRvO1xyXG4gICAgICBtYXJnaW4tdG9wOiBhdXRvO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLmV4YW1wbGUtYWN0aW9ucyB7XHJcbiAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAganVzdGlmeS1jb250ZW50OiBmbGV4LXN0YXJ0OyAvLyBBZGQgRXhhbXBsZSBsZWZ0IGFsaWduZWRcclxuICAgIG1hcmdpbi10b3A6IDFyZW07XHJcbiAgfVxyXG59XHJcblxyXG4uZXhhbXBsZXMtYWNjb3JkaW9uLXdyYXBwZXIge1xyXG4gIG1hcmdpbi10b3A6IDFyZW07XHJcbn1cclxuXHJcbi5leGFtcGxlcy1hY2NvcmRpb24td3JhcHBlciA6Om5nLWRlZXAgLmFjY29yZGlvbi1oZWFkZXIge1xyXG4gIG1pbi13aWR0aDogMTAwJSAhaW1wb3J0YW50O1xyXG59XHJcblxyXG46aG9zdCA6Om5nLWRlZXAgYXZhLWFjY29yZGlvbiAuYWNjb3JkaW9uLWNvbnRhaW5lciB7XHJcbiAgbWF4LXdpZHRoOiBub25lICFpbXBvcnRhbnQ7XHJcbiAgd2lkdGg6IDEwMCUgIWltcG9ydGFudDsgLy8gcmVpbmZvcmNlcyBmdWxsIHdpZHRoXHJcbn1cclxuXHJcbjpob3N0IDo6bmctZGVlcCBhdmEtYWNjb3JkaW9uIC5hY2NvcmRpb24tY29udGFpbmVyIC5hY2NvcmRpb24tY29udGVudCB7XHJcbiAgbWF4LWhlaWdodDogMTAwJTtcclxuICBvdmVyZmxvdy15OiBhdXRvO1xyXG59XHJcblxyXG46Om5nLWRlZXAgI2Rlc2NyaXB0aW9uIC5hdmEtdGV4dGFyZWFfX2xhYmVsIHtcclxuICBtYXJnaW4tdG9wOiAxNnB4O1xyXG59XHJcblxyXG4ubGVmdC1oZWFkZXIge1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICBoZWlnaHQ6IDQ4cHg7XHJcbiAgcGFkZGluZzogMCAxNnB4O1xyXG4gIGJhY2tncm91bmQ6ICNmZmY7XHJcbiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlMWU0ZTg7XHJcbiAgei1pbmRleDogMjtcclxufVxyXG5cclxuLnJpZ2h0LWhlYWRlciB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gIGhlaWdodDogNDhweDtcclxuICBwYWRkaW5nOiAwIDE2cHg7XHJcbiAgYmFja2dyb3VuZDogI2ZmZjtcclxuICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2UxZTRlODtcclxuICB6LWluZGV4OiAyO1xyXG59XHJcblxyXG4udGFiLWNvbnRlbnQge1xyXG4gIGZsZXg6IDEgMSAwO1xyXG4gIG1pbi1oZWlnaHQ6IDA7XHJcbiAgLy9vdmVyZmxvdy15OiBhdXRvO1xyXG4gIC8vc2Nyb2xsYmFyLXdpZHRoOiBub25lO1xyXG4gIC8vLW1zLW92ZXJmbG93LXN0eWxlOiBub25lO1xyXG59XHJcbi50YWItY29udGVudDo6LXdlYmtpdC1zY3JvbGxiYXIge1xyXG4gIGRpc3BsYXk6IG5vbmU7XHJcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n  return CreatePromptsComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "ReactiveFormsModule", "FormControl", "Validators", "MOCK_PROMPTS", "Subscription", "prompts<PERSON><PERSON><PERSON>", "AvaTextareaComponent", "AvaTextboxComponent", "ButtonComponent", "AccordionComponent", "DropdownComponent", "SharedNavItemComponent", "LucideAngularModule", "AskAvaWrapperComponent", "PromptsModes", "USE_CASE_IDENTIFIER", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵpropertyInterpolate", "ctx_r0", "prompt<PERSON><PERSON><PERSON>", "promptName", "ɵɵpropertyInterpolate1", "pleaseEnterPromptName", "ɵɵproperty", "getControl", "getFieldError", "description", "placeholderDescription", "ɵɵlistener", "CreatePromptsComponent_ava_button_15_Template_ava_button_userClick_0_listener", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "ɵɵresetView", "onCancel", "CreatePromptsComponent_ava_button_16_Template_ava_button_userClick_0_listener", "_r3", "toggleAskAvaModal", "askAva", "CreatePromptsComponent_ava_button_17_Template_ava_button_userClick_0_listener", "_r4", "isEditMode", "confirmUpdate", "confirmSave", "isSaveDisabled", "ɵɵpureFunction0", "_c3", "CreatePromptsComponent_shared_nav_item_23_Template_shared_nav_item_selectEvent_0_listener", "tab_r6", "_r5", "$implicit", "onTabSelected", "value", "isViewMode", "label", "selectedTab", "ɵɵelementContainerStart", "freeformPrompt", "intermediateSteps", "placeholderIntermediateSteps", "CreatePromptsComponent_ng_container_27_Template_ava_dropdown_selectionChange_6_listener", "$event", "_r7", "onPromptTypeChange", "ɵɵtemplate", "CreatePromptsComponent_ng_container_27_div_17_Template", "role", "promptTypeOptions", "selectedDropdownValue", "goal", "placeholder<PERSON><PERSON><PERSON>", "backstory", "placeholderBackStory", "expectedOutput", "placeholderExpectedOutput", "toLowerCase", "CreatePromptsComponent_div_30_div_1_div_8_div_5_Template_ava_button_userClick_1_listener", "_r9", "i_r10", "index", "remove<PERSON><PERSON><PERSON>", "remove", "CreatePromptsComponent_div_30_div_1_div_8_div_5_Template", "input", "placeholderInput", "output", "placeholder<PERSON>ut<PERSON>", "CreatePromptsComponent_div_30_div_1_div_8_Template", "CreatePromptsComponent_div_30_div_1_Template_ava_button_userClick_10_listener", "_r8", "addExample", "ɵɵtextInterpolate1", "addExamples", "examples", "controls", "additionalConsideration", "placeholderAdditionalConsideration", "CreatePromptsComponent_div_30_div_1_Template", "getOutPutControl", "CreatePromptsComponent_ng_container_34_div_12_Template", "placeholder<PERSON><PERSON>", "CreatePromptsComponent", "fb", "router", "route", "toolExecutionService", "promptsService", "cdr", "promptGenerateService", "promptRoleOptions", "selectedPromptType", "selected<PERSON>ole<PERSON><PERSON>ue", "previousAccordionState", "showSuccessPopup", "submissionSuccess", "popupTitle", "popupMessage", "formChanged", "isPatching", "iconName", "accordionRef", "labels", "promptTabs", "showAskAvaModal", "showPromptOutput", "isLoading", "tabValue", "promptTaskControl", "promptForm", "get", "clearValidators", "setValidators", "required", "updateValueAndValidity", "event", "selectedOptions", "length", "selected<PERSON><PERSON><PERSON>", "setValue", "emitEvent", "categoryId", "promptId", "isExecuteMode", "showChatInterface", "isLeftCollapsed", "toggleLeftPanel", "promptsOutputForm", "chatMessages", "isProcessingChat", "executionSubscription", "prompt", "constructor", "createPromtForm", "promptCache", "template", "freeform", "group", "name", "promptDescription", "promptTask", "promptType", "array", "addConsideration", "step1Instruction", "ngOnInit", "valueChanges", "subscribe", "fetchPromptTypeDropdown", "next", "options", "Array", "isArray", "Object", "keys", "map", "key", "snapshot", "paramMap", "executeParam", "queryParamMap", "viewParam", "loadPromptById", "trySetStoredPromptType", "tryUpdateDropdownAfterFormPatch", "error", "err", "console", "setTimeout", "detectChanges", "val", "log", "from", "text", "startExecution", "getExecutionState", "state", "isExecuting", "toolId", "storedPromptType", "match", "findMatchingDropdownOption", "currentValue", "isValueInOptions", "some", "opt", "find", "includes", "getPromptById", "patchPromptForm", "disable", "type", "roleFromAPI", "matchedRole", "patchValue", "dropdownType", "warn", "patchObj", "exampleArray", "clear", "for<PERSON>ach", "ex", "push", "exampleInput", "exampleOutput", "selectTab", "tab", "ngOnDestroy", "unsubscribe", "onSave", "nameControl", "promptTypeControl", "promptDescControl", "expectedOutputControl", "roleControl", "goalControl", "backstoryControl", "stepsControl", "valid", "mark<PERSON>llAsTouched", "formValues", "returnPage", "pageNumber", "parseInt", "createPayload", "getRawValue", "e", "undefined", "editPayload", "id", "editPrompt", "info", "message", "addPrompt", "examplesArray", "removeAt", "onExecute", "onExit", "stopExecution", "navigate", "queryParams", "page", "handleAttachment", "analyzePrompt", "loadPromptData", "p", "title", "department", "project", "handleChatMessage", "regenerate", "previousPrompt", "onClickGenerate", "ngAfterViewChecked", "currentExpanded", "expanded", "clearExtraExamples", "at", "reset", "fieldName", "field", "customLabels", "formattedFieldName", "invalid", "touched", "dirty", "errors", "<PERSON><PERSON><PERSON><PERSON>", "onlySelf", "onSuccessConfirm", "closeSuccessPopup", "show", "isTabFreeForm", "mode", "useCaseCreation", "agnetJsonCreation", "useCaseIdentifier", "modelApi", "res", "response", "choices", "generatedPrompt", "JSON", "parse", "ROLE", "GOAL", "BACKSTORY", "DESCRIPTION", "OUTPUT", "onUse", "formValue", "onReset", "onCancle", "onClose", "actionButtonLabel", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "Router", "ActivatedRoute", "i3", "ToolExecutionService", "i4", "PromptsService", "ChangeDetectorRef", "i5", "PromptEnhanceService", "selectors", "viewQuery", "CreatePromptsComponent_Query", "rf", "ctx", "CreatePromptsComponent_Template_lucide_icon_click_7_listener", "CreatePromptsComponent_span_8_Template", "CreatePromptsComponent_div_9_Template", "CreatePromptsComponent_ava_button_15_Template", "CreatePromptsComponent_ava_button_16_Template", "CreatePromptsComponent_ava_button_17_Template", "CreatePromptsComponent_shared_nav_item_23_Template", "CreatePromptsComponent_ng_container_26_Template", "CreatePromptsComponent_ng_container_27_Template", "CreatePromptsComponent_Template_ava_button_userClick_29_listener", "CreatePromptsComponent_div_30_Template", "CreatePromptsComponent_Template_app_ask_ava_wrapper_oNClickGenerate_31_listener", "CreatePromptsComponent_Template_app_ask_ava_wrapper_oNClickClosed_31_listener", "CreatePromptsComponent_Template_app_ask_ava_wrapper_oNClickUse_31_listener", "CreatePromptsComponent_Template_app_ask_ava_wrapper_oNClickReset_31_listener", "CreatePromptsComponent_Template_app_ask_ava_wrapper_oNClickCancel_31_listener", "CreatePromptsComponent_ng_container_33_Template", "CreatePromptsComponent_ng_container_34_Template", "ɵɵpureFunction1", "_c1", "ɵɵclassProp", "ɵɵtextInterpolate", "promptConfiguration", "chooseTheTypeOfPrompt", "_c2", "i6", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "ɵNgNoValidate", "NgControlStatus", "NgControlStatusGroup", "RequiredValidator", "FormControlDirective", "FormGroupDirective", "FormControlName", "FormGroupName", "FormArrayName", "i7", "LucideAngularComponent", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\pages\\libraries\\prompts\\create-prompts\\create-prompts.component.ts", "C:\\console\\aava-ui-web\\projects\\shared\\pages\\libraries\\prompts\\create-prompts\\create-prompts.component.html"], "sourcesContent": ["import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { ChangeDetectorRef } from '@angular/core';\r\nimport {\r\n  FormBuilder,\r\n  FormGroup,\r\n  ReactiveFormsModule,\r\n  FormControl,\r\n  FormArray,\r\n  Validators,\r\n} from '@angular/forms';\r\nimport { Router, ActivatedRoute } from '@angular/router';\r\nimport { ChatMessage } from '@shared/components/chat-window/chat-window.component';\r\nimport { MOCK_PROMPTS } from '@shared/mock-data/prompt-mock-data';\r\nimport { ToolExecutionService } from '@shared/services/tool-execution/tool-execution.service';\r\nimport { Subscription } from 'rxjs';\r\nimport promptsLabels from '../constants/prompts.json';\r\nimport {\r\n  AvaTextareaComponent,\r\n  AvaTextboxComponent,\r\n  ButtonComponent,\r\n  AccordionComponent,\r\n  PopupComponent,\r\n  DropdownComponent,\r\n} from '@ava/play-comp-library';\r\nimport { SharedNavItemComponent } from '@shared/components/nav-item/nav-item.component';\r\nimport { PromptsService } from '@shared/services/prompts.service';\r\nimport { LucideAngularModule } from 'lucide-angular';\r\nimport { AskAvaWrapperComponent } from '@shared/components/ask-ava-wrapper/ask-ava-wrapper.component';\r\nimport { PromptEnhanceService } from '@shared/services/prompt-enhance.service';\r\nimport { PromptsModes, USE_CASE_IDENTIFIER } from '../constants/constants';\r\n@Component({\r\n  selector: 'app-create-prompts',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    ReactiveFormsModule,\r\n    AvaTextboxComponent,\r\n    AvaTextareaComponent,\r\n    ButtonComponent,\r\n    SharedNavItemComponent,\r\n    AccordionComponent,\r\n    PopupComponent,\r\n    DropdownComponent,\r\n    LucideAngularModule,\r\n    AskAvaWrapperComponent,\r\n  ],\r\n  templateUrl: './create-prompts.component.html',\r\n  styleUrls: ['./create-prompts.component.scss'],\r\n})\r\nexport class CreatePromptsComponent implements OnInit, OnDestroy {\r\n  isViewMode: boolean = false;\r\n  promptTypeOptions: { name: string; value: string }[] = [];\r\n  promptRoleOptions: { name: string; value: string }[] = [];\r\n  selectedPromptType: string = '';\r\n  selectedDropdownValue: string = ''; // Add this property to track selected value\r\n  selectedRoleValue: string = '';\r\n  previousAccordionState: boolean = false;\r\n  showSuccessPopup = false;\r\n  submissionSuccess = false;\r\n  popupTitle: string = '';\r\n  popupMessage: string = ''\r\n  formChanged = false;\r\n  private isPatching = false;\r\n\r\n  iconName = 'check-circle';\r\n  @ViewChild('accordion') accordionRef!: AccordionComponent;\r\n  // Labels used across the Prompt UI components (titles)\r\n  public promptLabels = promptsLabels.labels;\r\n  promptTabs = [\r\n    { label: 'Freeform', value: 'freeform' },\r\n    { label: 'Template', value: 'template' },\r\n  ];\r\n\r\n  showAskAvaModal = false;\r\n  showPromptOutput = false;\r\n  isLoading = false;\r\n\r\n\r\n  selectedTab: string = 'freeform';\r\n\r\n  onTabSelected(tabValue: string): void {\r\n    this.selectedTab = tabValue;\r\n    const promptTaskControl = this.promptForm.get('promptTask');\r\n    if (tabValue === 'template') {\r\n      promptTaskControl?.clearValidators();\r\n    } else if (tabValue === 'freeform') {\r\n      promptTaskControl?.setValidators([Validators.required]);\r\n    }\r\n\r\n    promptTaskControl?.updateValueAndValidity();\r\n  }\r\n\r\n\r\n  onPromptTypeChange(event: any): void {\r\n    // Handle dropdown selection change\r\n    if (event && event.selectedOptions && event.selectedOptions.length > 0) {\r\n      const selectedValue = event.selectedOptions[0].value;\r\n      this.promptForm\r\n        .get('promptType')\r\n        ?.setValue(selectedValue, { emitEvent: false });\r\n      this.selectedDropdownValue = selectedValue;\r\n    }\r\n  }\r\n\r\n  // Mode flags\r\n  categoryId: number | null = null;\r\n  promptId: string | null = null;\r\n  isEditMode: boolean = false;\r\n  isExecuteMode: boolean = false;\r\n  showChatInterface: boolean = false;\r\n  isLeftCollapsed = false;\r\n\r\n  toggleLeftPanel() {\r\n    this.isLeftCollapsed = !this.isLeftCollapsed;\r\n  }\r\n\r\n  promptForm: FormGroup;\r\n  promptsOutputForm: FormGroup;\r\n\r\n  // Chat interface properties\r\n  chatMessages: ChatMessage[] = [];\r\n  isProcessingChat: boolean = false;\r\n\r\n  // Subscription\r\n  private executionSubscription: Subscription = new Subscription();\r\n  prompt = new FormControl('');\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private router: Router,\r\n    private route: ActivatedRoute,\r\n    private toolExecutionService: ToolExecutionService,\r\n    private promptsService: PromptsService,\r\n    private cdr: ChangeDetectorRef,\r\n    private promptGenerateService: PromptEnhanceService,\r\n  ) {\r\n    this.promptForm = this.createPromtForm();\r\n    this.promptsOutputForm = this.createPromtForm();\r\n  }\r\n\r\n  promptCache: Record<string, string> = {\r\n    template: '',\r\n    freeform: '',\r\n  };\r\n\r\n  private createPromtForm(): FormGroup {\r\n    return this.fb.group({\r\n      // Prompt details\r\n      name: [''],\r\n      promptDescription: [''],\r\n      // Prompt task (for freeform)\r\n      promptTask: ['', Validators.required],\r\n      // Right side (Template configuration fields)\r\n      role: ['', Validators.required],\r\n      backstory: [''],\r\n      description: [''],\r\n      goal: [''],\r\n      expectedOutput: [''],\r\n      // Prompt Type Dropdown\r\n      promptType: [''],\r\n      // Examples (these fields are still in the form group, but no longer rendered in HTML)\r\n      examples: this.fb.array([\r\n        this.fb.group({\r\n          input: [''],\r\n          output: [''],\r\n        }),\r\n      ]),\r\n      addConsideration: [''],\r\n      // Steps (these fields are still in the form group, but no longer rendered in HTML)\r\n      step1Instruction: [''],\r\n      // Add intermediateSteps to the form group\r\n      intermediateSteps: [''],\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.promptForm.valueChanges.subscribe(() => {\r\n      if (!this.isPatching) {\r\n        this.formChanged = true;\r\n      }\r\n    });\r\n    // First load dropdown options\r\n\r\n\r\n    this.promptsService.fetchPromptTypeDropdown().subscribe({\r\n      next: (options) => {\r\n        if (options && options.length && options[0].name && options[0].value) {\r\n          this.promptRoleOptions = options;\r\n        } else if (\r\n          options &&\r\n          typeof options === 'object' &&\r\n          !Array.isArray(options)\r\n        ) {\r\n          this.promptRoleOptions = Object.keys(options).map((key) => ({\r\n            name: options[key],\r\n            value: options[key],\r\n          }));\r\n        } else {\r\n          this.promptRoleOptions = [];\r\n        }\r\n\r\n      }\r\n    })\r\n    this.promptsService.fetchPromptTypeDropdown().subscribe({\r\n      next: (options) => {\r\n        if (options && options.length && options[0].name && options[0].value) {\r\n          this.promptTypeOptions = options;\r\n        } else if (\r\n          options &&\r\n          typeof options === 'object' &&\r\n          !Array.isArray(options)\r\n        ) {\r\n          this.promptTypeOptions = Object.keys(options).map((key) => ({\r\n            name: options[key],\r\n            value: options[key],\r\n          }));\r\n        } else {\r\n          this.promptTypeOptions = [];\r\n        }\r\n\r\n        // After dropdown options are loaded, check if we need to load prompt data\r\n        this.promptId = this.route.snapshot.paramMap.get('id');\r\n        const executeParam = this.route.snapshot.queryParamMap.get('execute');\r\n        const viewParam = this.route.snapshot.queryParamMap.get('view');\r\n\r\n        this.isEditMode = !!this.promptId;\r\n        this.isExecuteMode = executeParam === 'true';\r\n        this.isViewMode = viewParam === 'true';\r\n        this.showChatInterface = this.isExecuteMode;\r\n\r\n        if (this.isEditMode && this.promptId) {\r\n          this.loadPromptById(this.promptId);\r\n        } else {\r\n          // If not in edit mode but we have a stored prompt type, try to set it\r\n          this.trySetStoredPromptType();\r\n        }\r\n\r\n        // If form is already patched but dropdown options were loaded later, try to update the dropdown\r\n        this.tryUpdateDropdownAfterFormPatch();\r\n      },\r\n      error: (err) => {\r\n        console.error('Dropdown API error:', err);\r\n        // Even if dropdown fails, still try to load prompt data\r\n        this.promptId = this.route.snapshot.paramMap.get('id');\r\n        const executeParam = this.route.snapshot.queryParamMap.get('execute');\r\n        const viewParam = this.route.snapshot.queryParamMap.get('view');\r\n\r\n        setTimeout(() => {\r\n          this.isEditMode = !!this.promptId;\r\n          this.cdr.detectChanges(); // Optional: force update after flag is set\r\n        });\r\n\r\n        this.isExecuteMode = executeParam === 'true';\r\n        this.isViewMode = viewParam === 'true';\r\n        this.showChatInterface = this.isExecuteMode;\r\n\r\n        if (this.isEditMode && this.promptId) {\r\n          this.loadPromptById(this.promptId);\r\n        }\r\n      },\r\n    });\r\n\r\n    // Debug: log value changes for promptType\r\n    this.promptForm.get('promptType')?.valueChanges.subscribe((val) => {\r\n      // Update the selected dropdown value when form control changes\r\n      this.selectedDropdownValue = val || '';\r\n      console.log(\r\n        'Form control value changed:',\r\n        val,\r\n        'selectedDropdownValue:',\r\n        this.selectedDropdownValue,\r\n      );\r\n    });\r\n\r\n    // Handle execute mode (this doesn't depend on dropdown options)\r\n    const executeParam = this.route.snapshot.queryParamMap.get('execute');\r\n    if (this.isExecuteMode && this.promptId) {\r\n      this.chatMessages = [\r\n        { from: 'ai', text: 'Hi Akash, this is the prompt testing' },\r\n        { from: 'user', text: 'Test this input' },\r\n        { from: 'ai', text: 'Here is the output' },\r\n      ];\r\n\r\n      setTimeout(() => {\r\n        this.toolExecutionService.startExecution(\r\n          this.promptId!,\r\n          this.chatMessages,\r\n        );\r\n        this.executionSubscription = this.toolExecutionService\r\n          .getExecutionState()\r\n          .subscribe((state) => {\r\n            if (state.isExecuting && state.toolId === this.promptId) {\r\n              this.chatMessages = state.chatMessages;\r\n            }\r\n          });\r\n      }, 100);\r\n    }\r\n  }\r\n\r\n  // Add a property to store the prompt type for delayed setting\r\n  private storedPromptType: string = '';\r\n\r\n  // Method to try setting stored prompt type when dropdown options are loaded\r\n  private trySetStoredPromptType(): void {\r\n    if (this.storedPromptType && this.promptTypeOptions.length > 0) {\r\n      const match = this.findMatchingDropdownOption(this.storedPromptType);\r\n      if (match) {\r\n        // Set the value immediately\r\n        this.promptForm\r\n          .get('promptType')\r\n          ?.setValue(match.value, { emitEvent: false });\r\n        this.selectedDropdownValue = match.value;\r\n\r\n        // Also set it after a delay to ensure the dropdown is rendered\r\n        setTimeout(() => {\r\n          this.promptForm\r\n            .get('promptType')\r\n            ?.setValue(match.value, { emitEvent: false });\r\n          this.selectedDropdownValue = match.value;\r\n        }, 200);\r\n\r\n        this.storedPromptType = ''; // Clear after setting\r\n      }\r\n    }\r\n  }\r\n\r\n  // Method to try updating dropdown after form is already patched\r\n  private tryUpdateDropdownAfterFormPatch(): void {\r\n    const currentValue = this.promptForm.get('promptType')?.value;\r\n    if (currentValue && this.promptTypeOptions.length > 0) {\r\n      // Check if the current value is not in the dropdown options\r\n      const isValueInOptions = this.promptTypeOptions.some(\r\n        (opt) => opt.value === currentValue || opt.name === currentValue,\r\n      );\r\n\r\n      if (!isValueInOptions) {\r\n        // Try to find a matching option\r\n        const match = this.findMatchingDropdownOption(currentValue);\r\n        if (match) {\r\n          // Set the value immediately\r\n          this.promptForm\r\n            .get('promptType')\r\n            ?.setValue(match.value, { emitEvent: false });\r\n          this.selectedDropdownValue = match.value;\r\n\r\n          // Also set it after a delay to ensure the dropdown is rendered\r\n          setTimeout(() => {\r\n            this.promptForm\r\n              .get('promptType')\r\n              ?.setValue(match.value, { emitEvent: false });\r\n            this.selectedDropdownValue = match.value;\r\n          }, 200);\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // Helper method to find matching dropdown option\r\n  private findMatchingDropdownOption(promptType: string): { name: string, value: string } | null {\r\n\r\n    if (!promptType || this.promptTypeOptions.length === 0) {\r\n      return null;\r\n    }\r\n\r\n    // Try exact match first (case-insensitive)\r\n    let match = this.promptTypeOptions.find(\r\n      (opt) => opt.value.toLowerCase() === promptType.toLowerCase(),\r\n    );\r\n\r\n    // If no exact match, try matching by name\r\n    if (!match) {\r\n      match = this.promptTypeOptions.find(\r\n        (opt) => opt.name.toLowerCase() === promptType.toLowerCase(),\r\n      );\r\n    }\r\n\r\n    // If still no match, try partial matching\r\n    if (!match) {\r\n      match = this.promptTypeOptions.find(\r\n        opt => opt.value.toLowerCase().includes(promptType.toLowerCase()) ||\r\n          opt.name.toLowerCase().includes(promptType.toLowerCase()) ||\r\n          promptType.toLowerCase().includes(opt.value.toLowerCase()) ||\r\n          promptType.toLowerCase().includes(opt.name.toLowerCase())\r\n      );\r\n    }\r\n\r\n    return match || null;\r\n  }\r\n\r\n  private loadPromptById(promptId: string): void {\r\n    this.promptsService.getPromptById(promptId).subscribe({\r\n      next: (prompt) => {\r\n        if (prompt) {\r\n          this.categoryId = prompt.categoryId;\r\n          this.patchPromptForm(prompt);\r\n        } else {\r\n        }\r\n      },\r\n      error: (err) => {\r\n        console.error('Error fetching prompt:', err);\r\n      },\r\n    });\r\n  }\r\n\r\n  private patchPromptForm(prompt: any): void {\r\n    this.isPatching = true;\r\n    // After form is fully patched\r\n    setTimeout(() => {\r\n      this.isPatching = false;\r\n      this.formChanged = false; // form hasn't changed yet\r\n    }, 0);\r\n\r\n    if (this.isViewMode) {\r\n      setTimeout(() => {\r\n        this.promptForm.disable({ emitEvent: false });\r\n      }, 0);\r\n    }\r\n\r\n    // Set selected tab first based on type\r\n    this.selectedTab = prompt.type === 'free form' ? 'freeform' : 'template';\r\n\r\n    const roleFromAPI = prompt.role; // e.g., \"Developer\"\r\n\r\n    const matchedRole = this.promptRoleOptions.find(\r\n      (opt) => opt.name.toLowerCase() === roleFromAPI?.toLowerCase()\r\n    );\r\n\r\n    this.promptForm.patchValue({\r\n      role: matchedRole?.value\r\n    });\r\n    this.selectedRoleValue = matchedRole?.value ?? '';\r\n\r\n    // Improved dropdown type matching logic\r\n    let dropdownType = '';\r\n    if (prompt.type) {\r\n      if (this.promptTypeOptions.length > 0) {\r\n        // Dropdown options are available, try to find a match\r\n        const match = this.findMatchingDropdownOption(prompt.type);\r\n        if (match) {\r\n          dropdownType = match.value;\r\n        } else {\r\n          // If no match found, use the original type as fallback\r\n          dropdownType = prompt.type;\r\n          console.warn(\r\n            `No matching dropdown option found for prompt type: \"${prompt.type}\". Available options:`,\r\n            this.promptTypeOptions,\r\n          );\r\n        }\r\n      } else {\r\n        // Dropdown options not loaded yet, store the type for later\r\n        this.storedPromptType = prompt.type;\r\n        dropdownType = prompt.type;\r\n      }\r\n    } else {\r\n      dropdownType = prompt.type || '';\r\n    }\r\n\r\n    // Patch only the relevant fields based on the selected tab/type\r\n    const patchObj: any = {\r\n      name: prompt.name || '',\r\n      role: prompt.role || '',\r\n      goal: prompt.goal || '',\r\n      backstory: prompt.backstory || '',\r\n      expectedOutput: prompt.expectedOutput || '',\r\n      intermediateSteps: prompt.intermediateSteps || '',\r\n      promptType: dropdownType,\r\n    };\r\n\r\n    if (this.selectedTab === 'freeform') {\r\n      patchObj.promptTask = prompt.prompt || '';\r\n      patchObj.promptDescription = prompt.promptDescription || '';\r\n      // Clear template fields\r\n      patchObj.description = prompt.description;\r\n    } else if (this.selectedTab === 'template') {\r\n      patchObj.description = prompt.description || '';\r\n      patchObj.promptDescription = prompt.promptDescription || '';\r\n      // Clear freeform fields\r\n      patchObj.promptTask = '';\r\n    }\r\n\r\n    this.promptForm.patchValue(patchObj);\r\n\r\n    // Update the selected dropdown value\r\n    this.selectedDropdownValue = dropdownType;\r\n    console.log('patchPromptForm - dropdownType:', dropdownType, 'selectedDropdownValue:', this.selectedDropdownValue);\r\n\r\n    // If we're in template mode and have a dropdown type, ensure it's set after a delay\r\n    if (this.selectedTab === 'template' && dropdownType) {\r\n      setTimeout(() => {\r\n        this.promptForm\r\n          .get('promptType')\r\n          ?.setValue(dropdownType, { emitEvent: false });\r\n        this.selectedDropdownValue = dropdownType;\r\n        console.log(\r\n          'After delay - dropdownType:',\r\n          dropdownType,\r\n          'selectedDropdownValue:',\r\n          this.selectedDropdownValue,\r\n        );\r\n      }, 100);\r\n    }\r\n\r\n    // Patch examples if available\r\n    if (prompt.examples && Array.isArray(prompt.examples)) {\r\n      const exampleArray = this.promptForm.get('examples') as FormArray;\r\n\r\n      // Clear existing examples\r\n      exampleArray.clear();\r\n\r\n      // Push each example into the form array\r\n      prompt.examples.forEach((ex: any) => {\r\n        exampleArray.push(\r\n          this.fb.group({\r\n            input: [ex.exampleInput || ''],\r\n            output: [ex.exampleOutput || ''],\r\n          }),\r\n        );\r\n      });\r\n    }\r\n  }\r\n\r\n  selectTab(tab: 'freeform' | 'template' | string) {\r\n    this.selectedTab = tab as 'freeform' | 'template';\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    // Clean up subscription\r\n    if (this.executionSubscription) {\r\n      this.executionSubscription.unsubscribe();\r\n    }\r\n  }\r\n\r\n  onSave(): void {\r\n    this.formChanged = false;\r\n\r\n    if (this.isViewMode) {\r\n      return;\r\n    }\r\n    const nameControl = this.promptForm.get('name');\r\n    const promptTaskControl = this.promptForm.get('promptTask');\r\n    const promptTypeControl = this.promptForm.get('promptType');\r\n    const promptDescControl = this.promptForm.get('promptDescription');\r\n    const expectedOutputControl = this.promptForm.get('expectedOutput');\r\n    const roleControl = this.promptForm.get('role');\r\n    const goalControl = this.promptForm.get('goal');\r\n    const backstoryControl = this.promptForm.get('backstory');\r\n    const stepsControl = this.promptForm.get('intermediateSteps');\r\n    //  Clear all validators first\r\n    stepsControl?.clearValidators();\r\n    nameControl?.clearValidators();\r\n    promptTaskControl?.clearValidators();\r\n    promptTypeControl?.clearValidators();\r\n    promptDescControl?.clearValidators();\r\n    expectedOutputControl?.clearValidators();\r\n    roleControl?.clearValidators();\r\n    goalControl?.clearValidators();\r\n    backstoryControl?.clearValidators();\r\n\r\n    // Apply required validators\r\n    nameControl?.setValidators([Validators.required]);\r\n    promptDescControl?.setValidators([Validators.required]);\r\n    \r\n    if (this.selectedTab === 'template') {\r\n      promptDescControl?.setValidators([Validators.required]);\r\n      expectedOutputControl?.setValidators([Validators.required]);\r\n      roleControl?.setValidators([Validators.required]);\r\n      goalControl?.setValidators([Validators.required]);\r\n      backstoryControl?.setValidators([Validators.required]);\r\n      promptTypeControl?.setValidators([Validators.required]);\r\n      promptTaskControl?.clearValidators();\r\n      promptTaskControl?.setValue('');\r\n    } else if (this.selectedTab === 'freeform') {\r\n      promptTaskControl?.setValidators([Validators.required]);\r\n      this.promptForm.get('promptType')?.setValue('free form', { emitEvent: false });\r\n    }\r\n\r\n    //  Update value and validity\r\n    nameControl?.updateValueAndValidity();\r\n    promptTaskControl?.updateValueAndValidity();\r\n    promptTypeControl?.updateValueAndValidity();\r\n    promptDescControl?.updateValueAndValidity();\r\n    expectedOutputControl?.updateValueAndValidity();\r\n    roleControl?.updateValueAndValidity();\r\n    goalControl?.updateValueAndValidity();\r\n    backstoryControl?.updateValueAndValidity();\r\n\r\n    if (this.promptForm.value.promptType === 'Chain of Thought') {\r\n      stepsControl?.setValidators([Validators.required]);\r\n    }\r\n    stepsControl?.updateValueAndValidity();\r\n\r\n    //  If invalid, mark touched and exit\r\n    if (!this.promptForm.valid) {\r\n      this.promptForm.markAllAsTouched();\r\n      return;\r\n    }\r\n    const formValues = this.promptForm.value;\r\n    const returnPage = this.route.snapshot.queryParamMap.get('returnPage');\r\n    const pageNumber = returnPage ? parseInt(returnPage) : 1;\r\n\r\n    // Use the original case from dropdown for type\r\n    const promptType = formValues.promptType || '';\r\n\r\n    // Build payload for each type\r\n    let createPayload: any = {\r\n      categoryId: this.categoryId ?? 1, // fallback to 1 if null\r\n      type: promptType,\r\n      name: formValues.name,\r\n      role: formValues.role,\r\n      goal: formValues.goal,\r\n      backstory: formValues.backstory,\r\n      description: formValues.description,\r\n      promptDescription: formValues.promptDescription,\r\n      expectedOutput: formValues.expectedOutput,\r\n      // For template prompts (including Zero Shot), use templateDescription as prompt\r\n      prompt:\r\n        this.selectedTab === 'template'\r\n          ? formValues.description\r\n          : formValues.promptTask || '',\r\n    };\r\n\r\n    if (promptType === 'Zero Shot') {\r\n    } else if (\r\n      promptType === 'One Shot/Multi Shot' ||\r\n      promptType === 'One Shot' ||\r\n      promptType === 'Multi Shot'\r\n    ) {\r\n      // Send all examples, including empty ones\r\n      createPayload.examples = this.examples.getRawValue().map((e) => ({\r\n        exampleInput: e.input,\r\n        exampleOutput: e.output,\r\n      }));\r\n      createPayload.additionalConsideration = formValues.addConsideration;\r\n    } else if (promptType === 'Chain of Thought') {\r\n      createPayload.intermediateSteps = formValues.intermediateSteps;\r\n      // Send all examples, including empty ones\r\n      createPayload.examples = this.examples.getRawValue().map((e) => ({\r\n        exampleInput: e.input,\r\n        exampleOutput: e.output,\r\n      }));\r\n      createPayload.additionalConsideration = formValues.addConsideration;\r\n    } else if (promptType === 'Free Form') {\r\n      // For free form, only keep type, name, and prompt\r\n      createPayload = {\r\n        type: promptType,\r\n        name: formValues.name,\r\n        prompt: formValues.promptTask || '',\r\n      };\r\n    }\r\n\r\n    // Remove undefined fields\r\n    Object.keys(createPayload).forEach((key) => {\r\n      if (createPayload[key] === undefined) {\r\n        delete createPayload[key];\r\n      }\r\n    });\r\n\r\n    if (this.isEditMode && this.promptId) {\r\n      // For edit, ensure id is present in payload\r\n      const editPayload = { ...createPayload, id: this.promptId };\r\n      this.promptsService.editPrompt(editPayload).subscribe({\r\n        next: (info) => {\r\n          this.iconName = 'check-circle';\r\n          this.showSuccessPopup = true;\r\n          this.popupMessage = info?.info?.message || info.message;\r\n          this.submissionSuccess = true;\r\n        },\r\n        error: (error) => {\r\n          this.iconName = 'alert-circle';\r\n          this.popupMessage = error?.error?.message || error.message;\r\n          this.showSuccessPopup = true;\r\n          this.submissionSuccess = false;\r\n        },\r\n      });\r\n    } else {\r\n      this.promptsService.addPrompt(createPayload).subscribe({\r\n        next: (info) => {\r\n          this.iconName = 'check-circle';\r\n          this.showSuccessPopup = true;\r\n          this.popupMessage = info?.info?.message || info.message;\r\n          this.submissionSuccess = true;\r\n          // Reset examples array after successful save to a single empty group\r\n          const examplesArray = this.promptForm.get('examples') as FormArray;\r\n          while (examplesArray.length > 0) {\r\n            examplesArray.removeAt(0);\r\n          }\r\n          examplesArray.push(this.fb.group({ input: [''], output: [''] }));\r\n        },\r\n        error: (error) => {\r\n          this.iconName = 'alert-circle';\r\n          this.popupMessage = error?.error?.message || error.message;\r\n          this.showSuccessPopup = true;\r\n          this.submissionSuccess = false;\r\n        },\r\n      });\r\n    }\r\n  }\r\n\r\n  onExecute(): void {\r\n    if (this.promptId) {\r\n      // If we're already in execute mode with chat interface showing\r\n      if (this.isExecuteMode && this.showChatInterface) {\r\n        // Process the execution\r\n      } else {\r\n        // Set flags to show chat interface\r\n        this.isExecuteMode = true;\r\n        this.showChatInterface = true;\r\n\r\n        // Set the initial messages\r\n        this.chatMessages = [\r\n          {\r\n            from: 'ai',\r\n            text: 'Hi Akash, this is the prompt testing',\r\n          },\r\n          {\r\n            from: 'user',\r\n            text: 'Test this input',\r\n          },\r\n          {\r\n            from: 'ai',\r\n            text: 'Here is the output',\r\n          },\r\n        ];\r\n\r\n        // Delay starting the execution service slightly to allow UI to update\r\n        setTimeout(() => {\r\n          this.toolExecutionService.startExecution(\r\n            this.promptId!,\r\n            this.chatMessages,\r\n          );\r\n        }, 100);\r\n      }\r\n    }\r\n  }\r\n\r\n  onExit(): void {\r\n    // If we're in execute mode with chat interface showing\r\n    if (this.isExecuteMode && this.isEditMode) {\r\n      // Return to edit mode without chat interface\r\n      this.isExecuteMode = false;\r\n      this.showChatInterface = false;\r\n      this.toolExecutionService.stopExecution();\r\n    } else {\r\n      // Get the return page if available\r\n      const returnPage = this.route.snapshot.queryParamMap.get('returnPage');\r\n      const pageNumber = returnPage ? parseInt(returnPage) : 1;\r\n\r\n      // Exit to prompts list at correct page\r\n      this.router.navigate(['/libraries/prompts'], {\r\n        queryParams: { page: pageNumber },\r\n      });\r\n    }\r\n  }\r\n\r\n  // Helper method to get form controls easily from the template\r\n  getControl(name: string): FormControl {\r\n    return this.promptForm.get(name) as FormControl;\r\n  }\r\n\r\n  getOutPutControl(name: string): FormControl {\r\n    return this.promptsOutputForm.get(name) as FormControl;\r\n  }\r\n\r\n  // Handle file attachment for the prompt task\r\n  handleAttachment(): void {\r\n    // Implement file attachment logic here\r\n  }\r\n\r\n  // Analyze the prompt task\r\n  analyzePrompt(): void {\r\n    // Implement prompt analysis logic here\r\n  }\r\n\r\n  // Load prompt data from mock data\r\n  loadPromptData(promptId: string): void {\r\n    // In a real app, this would use data fetched from a service\r\n    const prompt = MOCK_PROMPTS?.find((p) => p.id === promptId);\r\n\r\n    if (prompt) {\r\n      // Set form values based on the prompt data\r\n      this.promptForm.get('name')?.setValue(prompt.title);\r\n      this.promptForm\r\n        .get('description')\r\n        ?.setValue(`This is the ${prompt.title} description.`);\r\n\r\n      // Set filter data based on the prompt properties\r\n      this.promptForm.get('organization')?.setValue('Ascendion');\r\n      this.promptForm\r\n        .get('domain')\r\n        ?.setValue(prompt.department || 'AI Research');\r\n      this.promptForm\r\n        .get('project')\r\n        ?.setValue(prompt.project || 'Prompt Engineering');\r\n      this.promptForm.get('team')?.setValue('NLP Team');\r\n\r\n      // Set sample values for other fields\r\n      this.promptForm\r\n        .get('promptTask')\r\n        ?.setValue('Generate a conversation about AI safety');\r\n      this.promptForm.get('role')?.setValue('AI Assistant');\r\n      this.promptForm\r\n        .get('goal')\r\n        ?.setValue('Help users understand AI safety concepts');\r\n      this.promptForm\r\n        .get('backstory')\r\n        ?.setValue('You are an expert in AI safety and ethics');\r\n      this.promptForm\r\n        .get('expectedOutput')\r\n        ?.setValue('A clear, informative conversation about AI safety');\r\n    }\r\n  }\r\n\r\n  // Handle chat messages\r\n  handleChatMessage(message: string): void {\r\n    if (this.promptId && this.isExecuteMode) {\r\n      this.isProcessingChat = true;\r\n\r\n      this.chatMessages.push({\r\n        from: 'user',\r\n        text: message,\r\n      });\r\n      // this.previewPrompt();\r\n      // Process through the service - it will handle adding user and AI messages\r\n      // this.toolExecutionService.processUserMessage(message);\r\n\r\n      // Reset loading state after a delay that matches the service's response time\r\n      setTimeout(() => {\r\n        this.isProcessingChat = false;\r\n      }, 1000);\r\n    }\r\n  }\r\n  get examples(): FormArray {\r\n    return this.promptForm.get('examples') as FormArray;\r\n  }\r\n\r\n  addExample(): void {\r\n    this.examples.push(\r\n      this.fb.group({\r\n        id: [null],\r\n        input: [''],\r\n        output: [''],\r\n      }),\r\n    );\r\n  }\r\n\r\n  removeExample(index: number): void {\r\n    // Only remove if it's not the first one\r\n    if (index > 0) {\r\n      this.examples.removeAt(index);\r\n    }\r\n  }\r\n  //   private previewPrompt(): void {\r\n  //   const payload = {\r\n  //     role: this.getControl('role').value,\r\n  //     goal: this.getControl('goal').value,\r\n  //     backstory: this.getControl('backstory').value,\r\n  //     description: this.getControl('description').value,\r\n  //     expectedOutput: this.getControl('expectedOutput').value\r\n  //   };\r\n\r\n  //   this.promptsService.previewPrompt(payload).subscribe({\r\n  //     next: (response) => {\r\n  //       console.log('Preview API Response:', response);\r\n\r\n  //       const previewResponseText = typeof response === 'string'\r\n  //         ? response\r\n  //         : JSON.stringify(response, null, 2); // fallback for object\r\n\r\n  //       // Add preview as AI message to chat\r\n  //       this.chatMessages.push({\r\n  //         from: 'ai',\r\n  //         text: previewResponseText\r\n  //       });\r\n\r\n  //       this.isProcessingChat = false;\r\n  //     },\r\n  //     error: (err) => {\r\n  //       console.error('Error during preview prompt:', err);\r\n  //       this.chatMessages.push({\r\n  //         from: 'ai',\r\n  //         text: 'An error occurred while generating the preview.'\r\n  //       });\r\n  //       this.isProcessingChat = false;\r\n  //     }\r\n  //   });\r\n  // }\r\n\r\n  regenerate() {\r\n    const previousPrompt = this.promptCache[this.selectedTab];\r\n    if (previousPrompt) {\r\n      this.prompt.setValue(previousPrompt);\r\n      this.toggleAskAvaModal();\r\n      this.onClickGenerate(previousPrompt);\r\n    }\r\n  }\r\n  ngAfterViewChecked(): void {\r\n    if (!this.accordionRef) return;\r\n\r\n    const currentExpanded = this.accordionRef.expanded;\r\n\r\n    if (this.previousAccordionState && !currentExpanded) {\r\n      setTimeout(() => {\r\n        this.clearExtraExamples();\r\n      });\r\n    }\r\n\r\n    this.previousAccordionState = currentExpanded;\r\n  }\r\n\r\n  clearExtraExamples(): void {\r\n    const examplesArray = this.promptForm.get('examples') as FormArray;\r\n    while (examplesArray.length > 1) {\r\n      examplesArray.removeAt(1);\r\n    }\r\n    examplesArray.at(0).get('input')?.reset();\r\n    examplesArray.at(0).get('output')?.reset();\r\n  }\r\n\r\n  // Get error message for a specific form field\r\n  getFieldError(fieldName: string): string {\r\n    const field = this.promptForm.get(fieldName);\r\n    // Custom label mapping\r\n    const customLabels: Record<string, string> = {\r\n      name: 'Prompt Name',\r\n      description: 'Description',\r\n      promptTask: 'Freeform Prompt',\r\n      role: 'Role',\r\n      goal: 'Goal',\r\n      promptDescription: 'Description',\r\n      backstory: 'Backstory',\r\n      expectedOutput: 'ExpectedOutput',\r\n      promptType: 'PromptType'\r\n    };\r\n\r\n    const formattedFieldName = customLabels[fieldName] || fieldName;\r\n\r\n    if (field && field.invalid && (field.touched || field.dirty)) {\r\n      if (field.errors?.['required']) {\r\n        return `${formattedFieldName} is required`;\r\n      }\r\n      if (field.errors?.['minlength']) {\r\n        return `${formattedFieldName} must be at least ${field.errors['minlength'].requiredLength} characters long`;\r\n      }\r\n    } else if (fieldName == 'modelType' || fieldName == 'apiVersion') {\r\n      if (field && field.errors?.['required']) {\r\n        return `${formattedFieldName} is required`;\r\n      }\r\n    }\r\n\r\n    return '';\r\n  }\r\n\r\n  isSaveDisabled(): boolean {\r\n    const promptTaskControl = this.promptForm.get('promptTask');\r\n\r\n    // Set promptTask validators based on selected tab\r\n    if (this.selectedTab === 'template') {\r\n      promptTaskControl?.clearValidators();\r\n    } else if (this.selectedTab === 'freeform') {\r\n      promptTaskControl?.setValidators([Validators.required]);\r\n    }\r\n\r\n    promptTaskControl?.updateValueAndValidity({ onlySelf: true, emitEvent: false });\r\n\r\n    return this.promptForm.invalid || !this.formChanged;\r\n  }\r\n\r\n\r\n  // Handle success popup confirmation\r\n  onSuccessConfirm(): void {\r\n    this.closeSuccessPopup();\r\n  }\r\n\r\n  // Hide success popup and navigate if successful\r\n  closeSuccessPopup(): void {\r\n    this.showSuccessPopup = false;\r\n    this.popupTitle = '';\r\n    this.popupMessage = '';\r\n    if (this.submissionSuccess) {\r\n      this.router.navigate(['/libraries/prompts']);\r\n    }\r\n  }\r\n\r\n  toggleAskAvaModal(show = true) {\r\n    this.showAskAvaModal = show;\r\n  }\r\n\r\n  onClickGenerate(prompt = this.prompt.value) {\r\n    const isTabFreeForm = this.selectedTab === 'freeform';\r\n    const mode = isTabFreeForm\r\n      ? PromptsModes.useCaseCreation\r\n      : PromptsModes.agnetJsonCreation;\r\n    const useCaseIdentifier = mode + USE_CASE_IDENTIFIER;\r\n    this.isLoading = true;\r\n    this.promptGenerateService\r\n      .modelApi(prompt, mode, false, useCaseIdentifier)\r\n      .subscribe({\r\n        next: (res) => {\r\n          if (isTabFreeForm) {\r\n            this.promptsOutputForm.patchValue({\r\n              promptTask: res?.response?.choices[0]?.text,\r\n            });\r\n\r\n            console.log(this.promptsOutputForm);\r\n          } else {\r\n            let generatedPrompt: any = {};\r\n            try {\r\n              generatedPrompt = JSON.parse(res?.response?.choices[0]?.text);\r\n            } catch (error) {\r\n              return;\r\n            }\r\n            const { ROLE, GOAL, BACKSTORY, DESCRIPTION, OUTPUT } =\r\n              generatedPrompt;\r\n            this.promptsOutputForm.patchValue({\r\n              role: ROLE,\r\n              goal: GOAL,\r\n              backstory: BACKSTORY,\r\n              description: DESCRIPTION,\r\n              expectedOutput: OUTPUT,\r\n            });\r\n          }\r\n\r\n          this.showPromptOutput = true;\r\n          this.isLoading = false;\r\n        },\r\n        error: () => {\r\n          this.isLoading = false;\r\n        },\r\n      });\r\n  }\r\n\r\n  onUse() {\r\n    const formValue = this.promptsOutputForm.getRawValue();\r\n    this.promptCache[this.selectedTab] = this.prompt.value || '';\r\n    this.promptForm.patchValue(formValue);\r\n    this.promptsOutputForm.reset();\r\n    this.prompt.reset('');\r\n    this.showPromptOutput = false;\r\n    this.toggleAskAvaModal(false);\r\n  }\r\n\r\n  onReset() {\r\n    this.promptsOutputForm.reset();\r\n    this.showPromptOutput = false;\r\n  }\r\n\r\n  onCancle() {\r\n    this.prompt.reset('');\r\n    this.onReset();\r\n    this.toggleAskAvaModal(false);\r\n  }\r\n\r\n  onClose() {\r\n    this.toggleAskAvaModal(false);\r\n    this.onCancle();\r\n  }\r\n  onCancel(): void {\r\n    this.router.navigate(['/libraries/prompts']);\r\n  }\r\n\r\n  get actionButtonLabel(): string {\r\n    return this.isEditMode ? 'Update' : 'Save';\r\n  }\r\n}\r\n", "<p class=\"page-title\">Prompt Configuration</p>\r\n<div class=\"create-prompts-container\">\r\n  <form [formGroup]=\"promptForm\">\r\n    <div class=\"form-layout\" [ngClass]=\"{ 'three-column-layout': isExecuteMode && showChatInterface }\">\r\n      <!-- Left Column -->\r\n      <div class=\"left-column\" [class.collapsed]=\"isLeftCollapsed\">\r\n        <div class=\"left-header\">\r\n          <lucide-icon name=\"panel-left\" class=\"collapse-icon\" (click)=\"toggleLeftPanel()\"></lucide-icon>\r\n          <span class=\"left-title\" *ngIf=\"!isLeftCollapsed\">Prompt Description</span>\r\n        </div>\r\n        <div class=\"card-content\" *ngIf=\"!isLeftCollapsed\">\r\n          <ava-textbox [formControl]=\"getControl('name')\" label=\"{{ promptLabels.promptName }}\" id=\"promptName\"\r\n            name=\"promptName\" placeholder=\"{{ promptLabels.pleaseEnterPromptName }} \" [error]=\"getFieldError('name')\"\r\n            [required]=\"true\">\r\n          </ava-textbox>\r\n\r\n          <ava-textarea id=\"description\" name=\"description\" label=\"{{ promptLabels.description }}\"\r\n            [formControl]=\"getControl('promptDescription')\" placeholder=\"{{ promptLabels.placeholderDescription }}\"\r\n            [rows]=\"6\" size=\"md\" [error]=\"getFieldError('promptDescription')\" [required]=\"true\">\r\n          </ava-textarea>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Middle Column -->\r\n      <div class=\"middle-column\">\r\n        <div class=\"prompt_header\">\r\n          <h3 class=\"section-title\">{{ promptLabels.promptConfiguration }}</h3>\r\n          <div class=\"actions d-flex justify-content-between align-items-center\">\r\n            <ava-button *ngIf=\"isViewMode\" label=\"Back\" variant=\"secondary\" (userClick)=\"onCancel()\">\r\n            </ava-button>\r\n            <ava-button *ngIf=\"!isViewMode\" label=\"{{ promptLabels.askAva }}\" variant=\"secondary\" size=\"medium\"\r\n              iconName=\"WandSparkles\" (userClick)=\"toggleAskAvaModal()\">\r\n            </ava-button>\r\n            <ava-button *ngIf=\"!isViewMode\" [disabled]=\"isSaveDisabled()\" [label]=\"isEditMode ? 'Update' : 'Save'\" variant=\"primary\" size=\"medium\"\r\n              [customStyles]=\"{\r\n              background:\r\n                'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',\r\n              '--button-effect-color': '33, 90, 214',\r\n            }\" (userClick)=\"isEditMode ? confirmUpdate() : confirmSave()\">\r\n            </ava-button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Toggle Tabs -->\r\n        <div class=\"tab-container\">\r\n          <h3 class=\"tab-heading\">{{ promptLabels.chooseTheTypeOfPrompt }}</h3>\r\n          <div class=\"tabs-wrapper\">\r\n            <div class=\"pill-tabs-container\">\r\n              <shared-nav-item *ngFor=\"let tab of promptTabs\" [disabled]=\"isEditMode || isViewMode\" [label]=\"tab.label\" [selected]=\"selectedTab === tab.value\"\r\n                [hasDropdown]=\"false\" [dropdownOpen]=\"false\" (selectEvent)=\"onTabSelected(tab.value)\">\r\n              </shared-nav-item>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"form-container\">\r\n          <!-- Tab Content -->\r\n          <div class=\"tab-content\">\r\n            <!-- Freeform Tab -->\r\n            <ng-container *ngIf=\"selectedTab === 'freeform'\">\r\n              <div class=\"freeform-content-wrapper\">\r\n                <ava-textarea id=\"freeformPrompt\" name=\"freeformPrompt\" label=\"{{ promptLabels.freeformPrompt }}\"\r\n                  [formControl]=\"getControl('promptTask')\" placeholder=\"Please enter the prompt task\" [rows]=\"8\"\r\n                  size=\"md\" [error]=\"getFieldError('promptTask')\" [fullWidth]=\"true\" [required]=\"true\">\r\n                </ava-textarea>\r\n              </div>\r\n            </ng-container>\r\n\r\n            <!-- Template Tab -->\r\n            <ng-container *ngIf=\"selectedTab === 'template'\">\r\n              <div class=\"template-form\">\r\n                <div class=\"fields-row\">\r\n                  <div class=\"field-col\">\r\n                    <div class=\"role-dropdown-row\">   \r\n                      <ava-textbox formControlName=\"role\" [formControl]=\"getControl('role')\" id=\"role\" name=\"role\"\r\n                        label=\"{{ promptLabels.role }}\" [disabled]=\"isViewMode\" [error]=\"getFieldError('role')\"\r\n                        [required]=\"true\" class=\"prompt-type-dropdown-flex role-dropdown-style\">\r\n                      </ava-textbox>\r\n                      <ava-dropdown formControlName=\"promptType\" label=\"Prompt Type\" id=\"promptType\" name=\"promptType\"\r\n                        [options]=\"promptTypeOptions\" [selectedValue]=\"selectedDropdownValue\"\r\n                        placeholder=\"Select Prompt Type\" [required]=\"true\" [error]=\"getFieldError('promptType')\"\r\n                        (selectionChange)=\"onPromptTypeChange($event)\" [disabled]=\"isViewMode\"\r\n                        class=\"prompt-type-dropdown-flex role-dropdown-style\">\r\n                      </ava-dropdown>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <div class=\"fields-row\">\r\n                  <div class=\"field-col\">\r\n                    <ava-textarea [formControl]=\"getControl('goal')\" label=\"Goal\" id=\"goal\"\r\n                      name=\"{{ promptLabels.goal }}\" placeholder=\"{{ promptLabels.placeholderGoal }}\" size=\"md\"\r\n                      [rows]=\"4\" [error]=\"getFieldError('goal')\" [fullWidth]=\"true\" [required]=\"true\">\r\n                    </ava-textarea>\r\n                  </div>\r\n                  <div class=\"field-col\">\r\n                    <ava-textarea id=\"templateDescription\" name=\"templateDescription\"\r\n                      label=\"{{ promptLabels.description }}\" [formControl]=\"getControl('description')\"\r\n                      placeholder=\"{{ promptLabels.placeholderDescription }}\" [rows]=\"4\" size=\"md\" [fullWidth]=\"true\"\r\n                      [error]=\"getFieldError('description')\" [required]=\"true\">\r\n                    </ava-textarea>\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"fields-row\">\r\n                  <div class=\"field-col\">\r\n                    <ava-textarea id=\"backstory\" name=\"backstory\" label=\"{{ promptLabels.backstory }}\"\r\n                      [formControl]=\"getControl('backstory')\" placeholder=\"{{ promptLabels.placeholderBackStory }}\"\r\n                      [rows]=\"4\" size=\"md\" [fullWidth]=\"true\" [error]=\"getFieldError('backstory')\" [required]=\"true\">\r\n                    </ava-textarea>\r\n                  </div>\r\n                  <div class=\"field-col\">\r\n                    <ava-textarea [formControl]=\"getControl('expectedOutput')\" label=\"{{ promptLabels.expectedOutput }}\"\r\n                      id=\"expectedOutput\" name=\"expectedOutput\"\r\n                      placeholder=\"{{ promptLabels.placeholderExpectedOutput }}\" size=\"md\" [rows]=\"4\" [fullWidth]=\"true\"\r\n                      [error]=\"getFieldError('expectedOutput')\" [required]=\"true\">\r\n                    </ava-textarea>\r\n                  </div>\r\n                </div>\r\n                <div class=\"fields-row\" *ngIf=\"\r\n                    getControl('promptType').value &&\r\n                    getControl('promptType').value.toLowerCase() ===\r\n                      'chain of thought'\r\n                  \">\r\n                  <div class=\"field-col\">\r\n                    <ava-textarea id=\"intermediateSteps\" name=\"intermediateSteps\"\r\n                      label=\"{{ promptLabels.intermediateSteps }}\" [formControl]=\"getControl('intermediateSteps')\"\r\n                      placeholder=\"{{\r\n                        promptLabels.placeholderIntermediateSteps\r\n                      }}\" [rows]=\"4\" size=\"md\" [fullWidth]=\"true\" [error]=\"getFieldError('intermediateSteps')\"\r\n                      [required]=\"true\">\r\n                    </ava-textarea>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </ng-container>\r\n            <div class=\"regenerate-button-wrapper\">\r\n              <ava-button label=\"Regenerate\" variant=\"secondary\" size=\"medium\" iconName=\"rotate-cw\"\r\n                (userClick)=\"regenerate()\" iconPosition=\"right\" [customStyles]=\"{\r\n                  'border': '2px solid transparent',\r\n                  'background-image': 'linear-gradient(#ffffff, #ffffff), linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',\r\n                  'background-origin': 'border-box',\r\n                  'background-clip': 'padding-box, border-box',\r\n                  '--button-effect-color': '33, 90, 214'\r\n                }\">\r\n\r\n              </ava-button>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Optional Sections -->\r\n          <div class=\"optional-sections\" *ngIf=\"selectedTab === 'template'\">\r\n            <div class=\"examples-accordion-wrapper\" *ngIf=\"\r\n                !getControl('promptType').value ||\r\n                getControl('promptType').value.toLowerCase() !== 'zero shot'\r\n              \">\r\n              <ava-accordion #accordion [expanded]=\"false\" [animation]=\"true\" [controlled]=\"false\"\r\n                iconClosed=\"chevron-down\" iconOpen=\"chevron-up\" iconPosition=\"right\" type=\"default\">\r\n                <div header>\r\n                  {{ promptLabels.addExamples }}\r\n                </div>\r\n                <div content>\r\n                  <div class=\"example\">\r\n                    <div class=\"example-content\" formArrayName=\"examples\">\r\n                      <div class=\"example-group\" *ngFor=\"let example of examples.controls; let i = index\"\r\n                        [formGroupName]=\"i\">\r\n                        <div class=\"field-col\">\r\n                          <ava-textarea [formControlName]=\"'input'\" label=\"Input\" name=\"{{ promptLabels.input }}\"\r\n                            id=\"input\" placeholder=\"{{ promptLabels.placeholderInput }}\" variant=\"primary\" [rows]=\"2\"\r\n                            size=\"md\" [fullWidth]=\"true\">\r\n                          </ava-textarea>\r\n                        </div>\r\n                        <div class=\"field-col\">\r\n                          <ava-textarea [formControlName]=\"'output'\" label=\"Output\" name=\"{{ promptLabels.output }}\"\r\n                            id=\"output\" placeholder=\"{{ promptLabels.placeholderOutput }}\" variant=\"primary\" [rows]=\"2\"\r\n                            size=\"md\" [fullWidth]=\"true\">\r\n                          </ava-textarea>\r\n                        </div>\r\n                        <!-- Remove button down and right-aligned -->\r\n                        <div class=\"example-remove\" *ngIf=\"i > 0\">\r\n                          <ava-button label=\"{{ promptLabels.remove }}\" variant=\"secondary\" size=\"medium\"\r\n                            iconName=\"trash\" iconPosition=\"left\" (userClick)=\"removeExample(i)\">\r\n                          </ava-button>\r\n                        </div>\r\n                      </div>\r\n                      <!-- Add button aligned to the left below the example group -->\r\n                      <div class=\"example-actions\">\r\n                        <ava-button label=\"{{ promptLabels.addExamples }}\" variant=\"secondary\" size=\"medium\"\r\n                          iconName=\"plus\" iconPosition=\"right\" (userClick)=\"addExample()\">\r\n                        </ava-button>\r\n                      </div>\r\n                    </div>\r\n                    <div class=\"add-consideration\">\r\n                      <ava-textarea [formControl]=\"getControl('addConsideration')\"\r\n                        label=\"{{ promptLabels.additionalConsideration }}\" id=\"addConsideration\" name=\"addConsideration\"\r\n                        placeholder=\"{{\r\n                          promptLabels.placeholderAdditionalConsideration\r\n                        }}\" variant=\"primary\" [rows]=\"2\" size=\"md\" [fullWidth]=\"true\">\r\n                      </ava-textarea>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </ava-accordion>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <!-- Right Column (if present) -->\r\n      <!-- <div class=\"chat-column\" *ngIf=\"isExecuteMode && showChatInterface\">\r\n        <div class=\"right-header\">\r\n          <span class=\"right-title\">Prompt Playground</span>\r\n        </div>\r\n        <div class=\"chat-column-content\">\r\n         \r\n        </div>\r\n      </div> -->\r\n    </div>\r\n  </form>\r\n</div>\r\n\r\n<app-ask-ava-wrapper [show]=\"showAskAvaModal\" [prompt]=\"prompt\" [isLoading]=\"isLoading\" [showOutput]=\"showPromptOutput\"\r\n  (oNClickGenerate)=\"onClickGenerate()\" (oNClickClosed)=\"onClose()\" (oNClickUse)=\"onUse()\" (oNClickReset)=\"onReset()\"\r\n  (oNClickCancel)=\"onCancle()\">\r\n  <form [formGroup]=\"promptsOutputForm\">\r\n    <ng-container *ngIf=\"selectedTab === 'freeform'\">\r\n      <ava-textarea id=\"freeformPrompt\" name=\"freeformPrompt\" label=\"{{ promptLabels.freeformPrompt }}\"\r\n        [formControl]=\"getOutPutControl('promptTask')\" placeholder=\"Please enter the prompt task\" [rows]=\"8\" size=\"md\"\r\n        [fullWidth]=\"true\" [readonly]=\"true\">\r\n      </ava-textarea>\r\n    </ng-container>\r\n    <!-- Template Tab -->\r\n    <ng-container *ngIf=\"selectedTab === 'template'\">\r\n      <div class=\"row g-2\">\r\n        <div class=\"col-md-6\">\r\n          <ava-textbox [formControl]=\"getOutPutControl('role')\" label=\"{{ promptLabels.role }}\" id=\"role\" name=\"role\"\r\n            placeholder=\"{{ promptLabels.placeholderRole }}\" variant=\"primary\" size=\"md\" [readonly]=\"true\">\r\n          </ava-textbox>\r\n        </div>\r\n        <!-- <div class=\"col-md-6\">\r\n                    <ava-dropdown formControlName=\"promptType\" label=\"Prompt Type\" id=\"promptType\" name=\"promptType\"\r\n                      [options]=\"promptTypeOptions\" [selectedValue]=\"selectedDropdownValue\"\r\n                      placeholder=\"Select Prompt Type\" (selectionChange)=\"onPromptTypeChange($event)\"\r\n                      [disabled]=\"isViewMode\">\r\n                    </ava-dropdown>\r\n                  </div> -->\r\n        <div class=\"col-md-6\">\r\n          <ava-textarea [formControl]=\"getOutPutControl('goal')\" label=\"Goal\" id=\"goal\" name=\"goal\"\r\n            placeholder=\"{{ promptLabels.placeholderGoal }}\" size=\"md\" [rows]=\"4\" [fullWidth]=\"true\" [readonly]=\"true\">\r\n          </ava-textarea>\r\n        </div>\r\n        <div class=\"col-md-6\">\r\n          <ava-textarea id=\"templateDescription\" name=\"templateDescription\" label=\"{{ promptLabels.description }}\"\r\n            [formControl]=\"getOutPutControl('description')\" placeholder=\"{{ promptLabels.placeholderDescription }}\"\r\n            [rows]=\"4\" size=\"md\" [fullWidth]=\"true\" [readonly]=\"true\">\r\n          </ava-textarea>\r\n        </div>\r\n        <div class=\"col-md-6\">\r\n          <ava-textarea id=\"backstory\" name=\"backstory\" label=\"{{ promptLabels.backstory }}\"\r\n            [formControl]=\"getOutPutControl('backstory')\" placeholder=\"{{ promptLabels.placeholderBackStory }}\"\r\n            [rows]=\"4\" size=\"md\" [fullWidth]=\"true\" [readonly]=\"true\">\r\n          </ava-textarea>\r\n        </div>\r\n        <div class=\"col-md-6\">\r\n          <ava-textarea [formControl]=\"getOutPutControl('expectedOutput')\" label=\"{{ promptLabels.expectedOutput }}\"\r\n            id=\"expectedOutput\" name=\"expectedOutput\" placeholder=\"{{ promptLabels.placeholderExpectedOutput }}\"\r\n            size=\"md\" [rows]=\"4\" [fullWidth]=\"true\" [readonly]=\"true\">\r\n          </ava-textarea>\r\n        </div>\r\n        <div class=\"col-md-12\" *ngIf=\"\r\n                      getOutPutControl('promptType').value &&\r\n                      getOutPutControl('promptType').value.toLowerCase() ===\r\n                        'chain of thought'\r\n                    \">\r\n          <ava-textarea id=\"intermediateSteps\" name=\"intermediateSteps\" label=\"{{ promptLabels.intermediateSteps }}\"\r\n            [formControl]=\"getOutPutControl('intermediateSteps')\" placeholder=\"{{\r\n                        promptLabels.placeholderIntermediateSteps\r\n                      }}\" [rows]=\"4\" size=\"md\" [fullWidth]=\"true\" [readonly]=\"true\">\r\n          </ava-textarea>\r\n        </div>\r\n      </div>\r\n    </ng-container>\r\n  </form>\r\n</app-ask-ava-wrapper>"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAGEC,mBAAmB,EACnBC,WAAW,EAEXC,UAAU,QACL,gBAAgB;AAGvB,SAASC,YAAY,QAAQ,oCAAoC;AAEjE,SAASC,YAAY,QAAQ,MAAM;AACnC,OAAOC,aAAa,MAAM,2BAA2B;AACrD,SACEC,oBAAoB,EACpBC,mBAAmB,EACnBC,eAAe,EACfC,kBAAkB,EAElBC,iBAAiB,QACZ,wBAAwB;AAC/B,SAASC,sBAAsB,QAAQ,gDAAgD;AAEvF,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,sBAAsB,QAAQ,8DAA8D;AAErG,SAASC,YAAY,EAAEC,mBAAmB,QAAQ,wBAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;ICtBhEC,EAAA,CAAAC,cAAA,eAAkD;IAAAD,EAAA,CAAAE,MAAA,yBAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAE7EH,EAAA,CAAAC,cAAA,cAAmD;IAMjDD,EALA,CAAAI,SAAA,sBAGc,uBAKC;IACjBJ,EAAA,CAAAG,YAAA,EAAM;;;;IAT4CH,EAAA,CAAAK,SAAA,EAAqC;IAArCL,EAAA,CAAAM,qBAAA,UAAAC,MAAA,CAAAC,YAAA,CAAAC,UAAA,CAAqC;IACjET,EAAA,CAAAU,sBAAA,oBAAAH,MAAA,CAAAC,YAAA,CAAAG,qBAAA,MAAuD;IACzEX,EAFW,CAAAY,UAAA,gBAAAL,MAAA,CAAAM,UAAA,SAAkC,UAAAN,MAAA,CAAAO,aAAA,SAC4D,kBACxF;IAG+Bd,EAAA,CAAAK,SAAA,EAAsC;IAAtCL,EAAA,CAAAM,qBAAA,UAAAC,MAAA,CAAAC,YAAA,CAAAO,WAAA,CAAsC;IACtCf,EAAA,CAAAM,qBAAA,gBAAAC,MAAA,CAAAC,YAAA,CAAAQ,sBAAA,CAAuD;IACrChB,EADlE,CAAAY,UAAA,gBAAAL,MAAA,CAAAM,UAAA,sBAA+C,WACrC,UAAAN,MAAA,CAAAO,aAAA,sBAAuD,kBAAkB;;;;;;IAUnFd,EAAA,CAAAC,cAAA,qBAAyF;IAAzBD,EAAA,CAAAiB,UAAA,uBAAAC,8EAAA;MAAAlB,EAAA,CAAAmB,aAAA,CAAAC,GAAA;MAAA,MAAAb,MAAA,GAAAP,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAAaf,MAAA,CAAAgB,QAAA,EAAU;IAAA,EAAC;IACxFvB,EAAA,CAAAG,YAAA,EAAa;;;;;;IACbH,EAAA,CAAAC,cAAA,qBAC4D;IAAlCD,EAAA,CAAAiB,UAAA,uBAAAO,8EAAA;MAAAxB,EAAA,CAAAmB,aAAA,CAAAM,GAAA;MAAA,MAAAlB,MAAA,GAAAP,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAAaf,MAAA,CAAAmB,iBAAA,EAAmB;IAAA,EAAC;IAC3D1B,EAAA,CAAAG,YAAA,EAAa;;;;IAFmBH,EAAA,CAAAM,qBAAA,UAAAC,MAAA,CAAAC,YAAA,CAAAmB,MAAA,CAAiC;;;;;;IAGjE3B,EAAA,CAAAC,cAAA,qBAK8D;IAA3DD,EAAA,CAAAiB,UAAA,uBAAAW,8EAAA;MAAA5B,EAAA,CAAAmB,aAAA,CAAAU,GAAA;MAAA,MAAAtB,MAAA,GAAAP,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAAAf,MAAA,CAAAuB,UAAA,GAA0BvB,MAAA,CAAAwB,aAAA,EAAe,GAAGxB,MAAA,CAAAyB,WAAA,EAAa;IAAA,EAAC;IAC7DhC,EAAA,CAAAG,YAAA,EAAa;;;;IALXH,EAD8B,CAAAY,UAAA,aAAAL,MAAA,CAAA0B,cAAA,GAA6B,UAAA1B,MAAA,CAAAuB,UAAA,qBAAyC,iBAAA9B,EAAA,CAAAkC,eAAA,IAAAC,GAAA,EAKpG;;;;;;IAUAnC,EAAA,CAAAC,cAAA,0BACwF;IAAzCD,EAAA,CAAAiB,UAAA,yBAAAmB,0FAAA;MAAA,MAAAC,MAAA,GAAArC,EAAA,CAAAmB,aAAA,CAAAmB,GAAA,EAAAC,SAAA;MAAA,MAAAhC,MAAA,GAAAP,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAAef,MAAA,CAAAiC,aAAA,CAAAH,MAAA,CAAAI,KAAA,CAAwB;IAAA,EAAC;IACvFzC,EAAA,CAAAG,YAAA,EAAkB;;;;;IADMH,EADwB,CAAAY,UAAA,aAAAL,MAAA,CAAAuB,UAAA,IAAAvB,MAAA,CAAAmC,UAAA,CAAqC,UAAAL,MAAA,CAAAM,KAAA,CAAoB,aAAApC,MAAA,CAAAqC,WAAA,KAAAP,MAAA,CAAAI,KAAA,CAAuC,sBACzH,uBAAuB;;;;;IAShDzC,EAAA,CAAA6C,uBAAA,GAAiD;IAC/C7C,EAAA,CAAAC,cAAA,cAAsC;IACpCD,EAAA,CAAAI,SAAA,uBAGe;IACjBJ,EAAA,CAAAG,YAAA,EAAM;;;;;IAJoDH,EAAA,CAAAK,SAAA,GAAyC;IAAzCL,EAAA,CAAAM,qBAAA,UAAAC,MAAA,CAAAC,YAAA,CAAAsC,cAAA,CAAyC;IAE5B9C,EADnE,CAAAY,UAAA,gBAAAL,MAAA,CAAAM,UAAA,eAAwC,WAAsD,UAAAN,MAAA,CAAAO,aAAA,eAC/C,mBAAmB,kBAAkB;;;;;IA4DpFd,EALF,CAAAC,cAAA,cAII,cACqB;IACrBD,EAAA,CAAAI,SAAA,uBAMe;IAEnBJ,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAPAH,EAAA,CAAAK,SAAA,GAA4C;IAA5CL,EAAA,CAAAM,qBAAA,UAAAC,MAAA,CAAAC,YAAA,CAAAuC,iBAAA,CAA4C;IAC5C/C,EAAA,CAAAM,qBAAA,gBAAAC,MAAA,CAAAC,YAAA,CAAAwC,4BAAA,CAEG;IACHhD,EAJ6C,CAAAY,UAAA,gBAAAL,MAAA,CAAAM,UAAA,sBAA+C,WAG9E,mBAA6B,UAAAN,MAAA,CAAAO,aAAA,sBAA6C,kBACvE;;;;;;IA5D3Bd,EAAA,CAAA6C,uBAAA,GAAiD;IAIzC7C,EAHN,CAAAC,cAAA,cAA2B,cACD,cACC,cACU;IAC7BD,EAAA,CAAAI,SAAA,sBAGc;IACdJ,EAAA,CAAAC,cAAA,uBAIwD;IADtDD,EAAA,CAAAiB,UAAA,6BAAAgC,wFAAAC,MAAA;MAAAlD,EAAA,CAAAmB,aAAA,CAAAgC,GAAA;MAAA,MAAA5C,MAAA,GAAAP,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAAmBf,MAAA,CAAA6C,kBAAA,CAAAF,MAAA,CAA0B;IAAA,EAAC;IAKtDlD,EAHM,CAAAG,YAAA,EAAe,EACX,EACF,EACF;IAEJH,EADF,CAAAC,cAAA,cAAwB,cACC;IACrBD,EAAA,CAAAI,SAAA,uBAGe;IACjBJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAI,SAAA,wBAIe;IAEnBJ,EADE,CAAAG,YAAA,EAAM,EACF;IAGJH,EADF,CAAAC,cAAA,eAAwB,eACC;IACrBD,EAAA,CAAAI,SAAA,wBAGe;IACjBJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAI,SAAA,wBAIe;IAEnBJ,EADE,CAAAG,YAAA,EAAM,EACF;IACNH,EAAA,CAAAqD,UAAA,KAAAC,sDAAA,kBAII;IAWNtD,EAAA,CAAAG,YAAA,EAAM;;;;;IA1DIH,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAM,qBAAA,UAAAC,MAAA,CAAAC,YAAA,CAAA+C,IAAA,CAA+B;IAC/BvD,EAFkC,CAAAY,UAAA,gBAAAL,MAAA,CAAAM,UAAA,SAAkC,aAAAN,MAAA,CAAAmC,UAAA,CACb,UAAAnC,MAAA,CAAAO,aAAA,SAAgC,kBACtE;IAGjBd,EAAA,CAAAK,SAAA,EAA6B;IAEkBL,EAF/C,CAAAY,UAAA,YAAAL,MAAA,CAAAiD,iBAAA,CAA6B,kBAAAjD,MAAA,CAAAkD,qBAAA,CAAwC,kBACnB,UAAAlD,MAAA,CAAAO,aAAA,eAAsC,aAAAP,MAAA,CAAAmC,UAAA,CAClB;IASxE1C,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAAM,qBAAA,SAAAC,MAAA,CAAAC,YAAA,CAAAkD,IAAA,CAA8B;IAAC1D,EAAA,CAAAM,qBAAA,gBAAAC,MAAA,CAAAC,YAAA,CAAAmD,eAAA,CAAgD;IACjB3D,EAFlD,CAAAY,UAAA,gBAAAL,MAAA,CAAAM,UAAA,SAAkC,WAEpC,UAAAN,MAAA,CAAAO,aAAA,SAAgC,mBAAmB,kBAAkB;IAK/Ed,EAAA,CAAAK,SAAA,GAAsC;IAAtCL,EAAA,CAAAM,qBAAA,UAAAC,MAAA,CAAAC,YAAA,CAAAO,WAAA,CAAsC;IACtCf,EAAA,CAAAM,qBAAA,gBAAAC,MAAA,CAAAC,YAAA,CAAAQ,sBAAA,CAAuD;IAChBhB,EAFA,CAAAY,UAAA,gBAAAL,MAAA,CAAAM,UAAA,gBAAyC,WACd,mBAA6B,UAAAN,MAAA,CAAAO,aAAA,gBACzD,kBAAkB;IAOZd,EAAA,CAAAK,SAAA,GAAoC;IAApCL,EAAA,CAAAM,qBAAA,UAAAC,MAAA,CAAAC,YAAA,CAAAoD,SAAA,CAAoC;IACxC5D,EAAA,CAAAM,qBAAA,gBAAAC,MAAA,CAAAC,YAAA,CAAAqD,oBAAA,CAAqD;IAChB7D,EAD7E,CAAAY,UAAA,gBAAAL,MAAA,CAAAM,UAAA,cAAuC,WAC7B,mBAA6B,UAAAN,MAAA,CAAAO,aAAA,cAAqC,kBAAkB;IAIrCd,EAAA,CAAAK,SAAA,GAAyC;IAAzCL,EAAA,CAAAM,qBAAA,UAAAC,MAAA,CAAAC,YAAA,CAAAsD,cAAA,CAAyC;IAElG9D,EAAA,CAAAM,qBAAA,gBAAAC,MAAA,CAAAC,YAAA,CAAAuD,yBAAA,CAA0D;IAChB/D,EAH9B,CAAAY,UAAA,gBAAAL,MAAA,CAAAM,UAAA,mBAA4C,WAEuB,mBAAmB,UAAAN,MAAA,CAAAO,aAAA,mBACzD,kBAAkB;IAIxCd,EAAA,CAAAK,SAAA,EAIxB;IAJwBL,EAAA,CAAAY,UAAA,SAAAL,MAAA,CAAAM,UAAA,eAAA4B,KAAA,IAAAlC,MAAA,CAAAM,UAAA,eAAA4B,KAAA,CAAAuB,WAAA,0BAIxB;;;;;;IAyDShE,EADF,CAAAC,cAAA,cAA0C,qBAE8B;IAA/BD,EAAA,CAAAiB,UAAA,uBAAAgD,yFAAA;MAAAjE,EAAA,CAAAmB,aAAA,CAAA+C,GAAA;MAAA,MAAAC,KAAA,GAAAnE,EAAA,CAAAqB,aAAA,GAAA+C,KAAA;MAAA,MAAA7D,MAAA,GAAAP,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAAaf,MAAA,CAAA8D,aAAA,CAAAF,KAAA,CAAgB;IAAA,EAAC;IAEvEnE,EADE,CAAAG,YAAA,EAAa,EACT;;;;IAHQH,EAAA,CAAAK,SAAA,EAAiC;IAAjCL,EAAA,CAAAM,qBAAA,UAAAC,MAAA,CAAAC,YAAA,CAAA8D,MAAA,CAAiC;;;;;IAd/CtE,EAFF,CAAAC,cAAA,cACsB,cACG;IACrBD,EAAA,CAAAI,SAAA,uBAGe;IACjBJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAuB;IACrBD,EAAA,CAAAI,SAAA,uBAGe;IACjBJ,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAqD,UAAA,IAAAkB,wDAAA,kBAA0C;IAK5CvE,EAAA,CAAAG,YAAA,EAAM;;;;;IAnBJH,EAAA,CAAAY,UAAA,kBAAAuD,KAAA,CAAmB;IAEuCnE,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAM,qBAAA,SAAAC,MAAA,CAAAC,YAAA,CAAAgE,KAAA,CAA+B;IAC1ExE,EAAA,CAAAM,qBAAA,gBAAAC,MAAA,CAAAC,YAAA,CAAAiE,gBAAA,CAAiD;IAClDzE,EAFE,CAAAY,UAAA,4BAA2B,WACkD,mBAC7D;IAI4BZ,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAM,qBAAA,SAAAC,MAAA,CAAAC,YAAA,CAAAkE,MAAA,CAAgC;IAC5E1E,EAAA,CAAAM,qBAAA,gBAAAC,MAAA,CAAAC,YAAA,CAAAmE,iBAAA,CAAkD;IACpD3E,EAFE,CAAAY,UAAA,6BAA4B,WACmD,mBAC/D;IAIHZ,EAAA,CAAAK,SAAA,EAAW;IAAXL,EAAA,CAAAY,UAAA,SAAAuD,KAAA,KAAW;;;;;;IArBhDnE,EANJ,CAAAC,cAAA,cAGI,2BAEoF,cACxE;IACVD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAGFH,EAFJ,CAAAC,cAAA,cAAa,cACU,cACmC;IACpDD,EAAA,CAAAqD,UAAA,IAAAuB,kDAAA,mBACsB;IAsBpB5E,EADF,CAAAC,cAAA,cAA6B,sBAEuC;IAA3BD,EAAA,CAAAiB,UAAA,uBAAA4D,8EAAA;MAAA7E,EAAA,CAAAmB,aAAA,CAAA2D,GAAA;MAAA,MAAAvE,MAAA,GAAAP,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAAaf,MAAA,CAAAwE,UAAA,EAAY;IAAA,EAAC;IAGrE/E,EAFI,CAAAG,YAAA,EAAa,EACT,EACF;IACNH,EAAA,CAAAC,cAAA,eAA+B;IAC7BD,EAAA,CAAAI,SAAA,wBAKe;IAKzBJ,EAJQ,CAAAG,YAAA,EAAM,EACF,EACF,EACQ,EACZ;;;;IA/CsBH,EAAA,CAAAK,SAAA,EAAkB;IAAoBL,EAAtC,CAAAY,UAAA,mBAAkB,mBAAmB,qBAAqB;IAGhFZ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAgF,kBAAA,MAAAzE,MAAA,CAAAC,YAAA,CAAAyE,WAAA,MACF;IAIqDjF,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAAY,UAAA,YAAAL,MAAA,CAAA2E,QAAA,CAAAC,QAAA,CAAsB;IAuBvDnF,EAAA,CAAAK,SAAA,GAAsC;IAAtCL,EAAA,CAAAM,qBAAA,UAAAC,MAAA,CAAAC,YAAA,CAAAyE,WAAA,CAAsC;IAOlDjF,EAAA,CAAAK,SAAA,GAAkD;IAAlDL,EAAA,CAAAM,qBAAA,UAAAC,MAAA,CAAAC,YAAA,CAAA4E,uBAAA,CAAkD;IAClDpF,EAAA,CAAAM,qBAAA,gBAAAC,MAAA,CAAAC,YAAA,CAAA6E,kCAAA,CAEG;IAAwCrF,EAJ/B,CAAAY,UAAA,gBAAAL,MAAA,CAAAM,UAAA,qBAA8C,WAI1B,mBAA6B;;;;;IA9C3Eb,EAAA,CAAAC,cAAA,cAAkE;IAChED,EAAA,CAAAqD,UAAA,IAAAiC,4CAAA,oBAGI;IAiDNtF,EAAA,CAAAG,YAAA,EAAM;;;;IApDqCH,EAAA,CAAAK,SAAA,EAGvC;IAHuCL,EAAA,CAAAY,UAAA,UAAAL,MAAA,CAAAM,UAAA,eAAA4B,KAAA,IAAAlC,MAAA,CAAAM,UAAA,eAAA4B,KAAA,CAAAuB,WAAA,mBAGvC;;;;;IAqEVhE,EAAA,CAAA6C,uBAAA,GAAiD;IAC/C7C,EAAA,CAAAI,SAAA,uBAGe;;;;;IAHyCJ,EAAA,CAAAK,SAAA,EAAyC;IAAzCL,EAAA,CAAAM,qBAAA,UAAAC,MAAA,CAAAC,YAAA,CAAAsC,cAAA,CAAyC;IAE5E9C,EADnB,CAAAY,UAAA,gBAAAL,MAAA,CAAAgF,gBAAA,eAA8C,WAAsD,mBAClF,kBAAkB;;;;;IAyCpCvF,EAAA,CAAAC,cAAA,cAIc;IACZD,EAAA,CAAAI,SAAA,uBAIe;IACjBJ,EAAA,CAAAG,YAAA,EAAM;;;;IAL0DH,EAAA,CAAAK,SAAA,EAA4C;IAA5CL,EAAA,CAAAM,qBAAA,UAAAC,MAAA,CAAAC,YAAA,CAAAuC,iBAAA,CAA4C;IAClD/C,EAAA,CAAAM,qBAAA,gBAAAC,MAAA,CAAAC,YAAA,CAAAwC,4BAAA,CAEzC;IAAyChD,EAFtD,CAAAY,UAAA,gBAAAL,MAAA,CAAAgF,gBAAA,sBAAqD,WAE7B,mBAA6B,kBAAkB;;;;;IA7C/EvF,EAAA,CAAA6C,uBAAA,GAAiD;IAE7C7C,EADF,CAAAC,cAAA,cAAqB,cACG;IACpBD,EAAA,CAAAI,SAAA,sBAEc;IAChBJ,EAAA,CAAAG,YAAA,EAAM;IAQNH,EAAA,CAAAC,cAAA,cAAsB;IACpBD,EAAA,CAAAI,SAAA,uBAEe;IACjBJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAsB;IACpBD,EAAA,CAAAI,SAAA,uBAGe;IACjBJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAsB;IACpBD,EAAA,CAAAI,SAAA,uBAGe;IACjBJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAsB;IACpBD,EAAA,CAAAI,SAAA,wBAGe;IACjBJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAqD,UAAA,KAAAmC,sDAAA,kBAIc;IAOhBxF,EAAA,CAAAG,YAAA,EAAM;;;;;IA7CoDH,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAM,qBAAA,UAAAC,MAAA,CAAAC,YAAA,CAAA+C,IAAA,CAA+B;IACnFvD,EAAA,CAAAM,qBAAA,gBAAAC,MAAA,CAAAC,YAAA,CAAAiF,eAAA,CAAgD;IAA6BzF,EADlE,CAAAY,UAAA,gBAAAL,MAAA,CAAAgF,gBAAA,SAAwC,kBAC2C;IAY9FvF,EAAA,CAAAK,SAAA,GAAgD;IAAhDL,EAAA,CAAAM,qBAAA,gBAAAC,MAAA,CAAAC,YAAA,CAAAmD,eAAA,CAAgD;IAAyC3D,EAD7E,CAAAY,UAAA,gBAAAL,MAAA,CAAAgF,gBAAA,SAAwC,WACiB,mBAAmB,kBAAkB;IAI1CvF,EAAA,CAAAK,SAAA,GAAsC;IAAtCL,EAAA,CAAAM,qBAAA,UAAAC,MAAA,CAAAC,YAAA,CAAAO,WAAA,CAAsC;IACtDf,EAAA,CAAAM,qBAAA,gBAAAC,MAAA,CAAAC,YAAA,CAAAQ,sBAAA,CAAuD;IAC/DhB,EADxC,CAAAY,UAAA,gBAAAL,MAAA,CAAAgF,gBAAA,gBAA+C,WACrC,mBAA6B,kBAAkB;IAIbvF,EAAA,CAAAK,SAAA,GAAoC;IAApCL,EAAA,CAAAM,qBAAA,UAAAC,MAAA,CAAAC,YAAA,CAAAoD,SAAA,CAAoC;IAClC5D,EAAA,CAAAM,qBAAA,gBAAAC,MAAA,CAAAC,YAAA,CAAAqD,oBAAA,CAAqD;IAC3D7D,EADxC,CAAAY,UAAA,gBAAAL,MAAA,CAAAgF,gBAAA,cAA6C,WACnC,mBAA6B,kBAAkB;IAIMvF,EAAA,CAAAK,SAAA,GAAyC;IAAzCL,EAAA,CAAAM,qBAAA,UAAAC,MAAA,CAAAC,YAAA,CAAAsD,cAAA,CAAyC;IAC9D9D,EAAA,CAAAM,qBAAA,gBAAAC,MAAA,CAAAC,YAAA,CAAAuD,yBAAA,CAA0D;IAC5D/D,EAF5B,CAAAY,UAAA,gBAAAL,MAAA,CAAAgF,gBAAA,mBAAkD,WAE1C,mBAAmB,kBAAkB;IAGrCvF,EAAA,CAAAK,SAAA,EAIb;IAJaL,EAAA,CAAAY,UAAA,SAAAL,MAAA,CAAAgF,gBAAA,eAAA9C,KAAA,IAAAlC,MAAA,CAAAgF,gBAAA,eAAA9C,KAAA,CAAAuB,WAAA,0BAIb;;;AD5NnB,WAAa0B,sBAAsB;EAA7B,MAAOA,sBAAsB;IA+EvBC,EAAA;IACAC,MAAA;IACAC,KAAA;IACAC,oBAAA;IACAC,cAAA;IACAC,GAAA;IACAC,qBAAA;IApFVvD,UAAU,GAAY,KAAK;IAC3Bc,iBAAiB,GAAsC,EAAE;IACzD0C,iBAAiB,GAAsC,EAAE;IACzDC,kBAAkB,GAAW,EAAE;IAC/B1C,qBAAqB,GAAW,EAAE,CAAC,CAAC;IACpC2C,iBAAiB,GAAW,EAAE;IAC9BC,sBAAsB,GAAY,KAAK;IACvCC,gBAAgB,GAAG,KAAK;IACxBC,iBAAiB,GAAG,KAAK;IACzBC,UAAU,GAAW,EAAE;IACvBC,YAAY,GAAW,EAAE;IACzBC,WAAW,GAAG,KAAK;IACXC,UAAU,GAAG,KAAK;IAE1BC,QAAQ,GAAG,cAAc;IACDC,YAAY;IACpC;IACOrG,YAAY,GAAGnB,aAAa,CAACyH,MAAM;IAC1CC,UAAU,GAAG,CACX;MAAEpE,KAAK,EAAE,UAAU;MAAEF,KAAK,EAAE;IAAU,CAAE,EACxC;MAAEE,KAAK,EAAE,UAAU;MAAEF,KAAK,EAAE;IAAU,CAAE,CACzC;IAEDuE,eAAe,GAAG,KAAK;IACvBC,gBAAgB,GAAG,KAAK;IACxBC,SAAS,GAAG,KAAK;IAGjBtE,WAAW,GAAW,UAAU;IAEhCJ,aAAaA,CAAC2E,QAAgB;MAC5B,IAAI,CAACvE,WAAW,GAAGuE,QAAQ;MAC3B,MAAMC,iBAAiB,GAAG,IAAI,CAACC,UAAU,CAACC,GAAG,CAAC,YAAY,CAAC;MAC3D,IAAIH,QAAQ,KAAK,UAAU,EAAE;QAC3BC,iBAAiB,EAAEG,eAAe,EAAE;MACtC,CAAC,MAAM,IAAIJ,QAAQ,KAAK,UAAU,EAAE;QAClCC,iBAAiB,EAAEI,aAAa,CAAC,CAACtI,UAAU,CAACuI,QAAQ,CAAC,CAAC;MACzD;MAEAL,iBAAiB,EAAEM,sBAAsB,EAAE;IAC7C;IAGAtE,kBAAkBA,CAACuE,KAAU;MAC3B;MACA,IAAIA,KAAK,IAAIA,KAAK,CAACC,eAAe,IAAID,KAAK,CAACC,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACtE,MAAMC,aAAa,GAAGH,KAAK,CAACC,eAAe,CAAC,CAAC,CAAC,CAACnF,KAAK;QACpD,IAAI,CAAC4E,UAAU,CACZC,GAAG,CAAC,YAAY,CAAC,EAChBS,QAAQ,CAACD,aAAa,EAAE;UAAEE,SAAS,EAAE;QAAK,CAAE,CAAC;QACjD,IAAI,CAACvE,qBAAqB,GAAGqE,aAAa;MAC5C;IACF;IAEA;IACAG,UAAU,GAAkB,IAAI;IAChCC,QAAQ,GAAkB,IAAI;IAC9BpG,UAAU,GAAY,KAAK;IAC3BqG,aAAa,GAAY,KAAK;IAC9BC,iBAAiB,GAAY,KAAK;IAClCC,eAAe,GAAG,KAAK;IAEvBC,eAAeA,CAAA;MACb,IAAI,CAACD,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;IAC9C;IAEAhB,UAAU;IACVkB,iBAAiB;IAEjB;IACAC,YAAY,GAAkB,EAAE;IAChCC,gBAAgB,GAAY,KAAK;IAEjC;IACQC,qBAAqB,GAAiB,IAAItJ,YAAY,EAAE;IAChEuJ,MAAM,GAAG,IAAI1J,WAAW,CAAC,EAAE,CAAC;IAE5B2J,YACUjD,EAAe,EACfC,MAAc,EACdC,KAAqB,EACrBC,oBAA0C,EAC1CC,cAA8B,EAC9BC,GAAsB,EACtBC,qBAA2C;MAN3C,KAAAN,EAAE,GAAFA,EAAE;MACF,KAAAC,MAAM,GAANA,MAAM;MACN,KAAAC,KAAK,GAALA,KAAK;MACL,KAAAC,oBAAoB,GAApBA,oBAAoB;MACpB,KAAAC,cAAc,GAAdA,cAAc;MACd,KAAAC,GAAG,GAAHA,GAAG;MACH,KAAAC,qBAAqB,GAArBA,qBAAqB;MAE7B,IAAI,CAACoB,UAAU,GAAG,IAAI,CAACwB,eAAe,EAAE;MACxC,IAAI,CAACN,iBAAiB,GAAG,IAAI,CAACM,eAAe,EAAE;IACjD;IAEAC,WAAW,GAA2B;MACpCC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE;KACX;IAEOH,eAAeA,CAAA;MACrB,OAAO,IAAI,CAAClD,EAAE,CAACsD,KAAK,CAAC;QACnB;QACAC,IAAI,EAAE,CAAC,EAAE,CAAC;QACVC,iBAAiB,EAAE,CAAC,EAAE,CAAC;QACvB;QACAC,UAAU,EAAE,CAAC,EAAE,EAAElK,UAAU,CAACuI,QAAQ,CAAC;QACrC;QACAlE,IAAI,EAAE,CAAC,EAAE,EAAErE,UAAU,CAACuI,QAAQ,CAAC;QAC/B7D,SAAS,EAAE,CAAC,EAAE,CAAC;QACf7C,WAAW,EAAE,CAAC,EAAE,CAAC;QACjB2C,IAAI,EAAE,CAAC,EAAE,CAAC;QACVI,cAAc,EAAE,CAAC,EAAE,CAAC;QACpB;QACAuF,UAAU,EAAE,CAAC,EAAE,CAAC;QAChB;QACAnE,QAAQ,EAAE,IAAI,CAACS,EAAE,CAAC2D,KAAK,CAAC,CACtB,IAAI,CAAC3D,EAAE,CAACsD,KAAK,CAAC;UACZzE,KAAK,EAAE,CAAC,EAAE,CAAC;UACXE,MAAM,EAAE,CAAC,EAAE;SACZ,CAAC,CACH,CAAC;QACF6E,gBAAgB,EAAE,CAAC,EAAE,CAAC;QACtB;QACAC,gBAAgB,EAAE,CAAC,EAAE,CAAC;QACtB;QACAzG,iBAAiB,EAAE,CAAC,EAAE;OACvB,CAAC;IACJ;IAEA0G,QAAQA,CAAA;MACN,IAAI,CAACpC,UAAU,CAACqC,YAAY,CAACC,SAAS,CAAC,MAAK;QAC1C,IAAI,CAAC,IAAI,CAAChD,UAAU,EAAE;UACpB,IAAI,CAACD,WAAW,GAAG,IAAI;QACzB;MACF,CAAC,CAAC;MACF;MAGA,IAAI,CAACX,cAAc,CAAC6D,uBAAuB,EAAE,CAACD,SAAS,CAAC;QACtDE,IAAI,EAAGC,OAAO,IAAI;UAChB,IAAIA,OAAO,IAAIA,OAAO,CAACjC,MAAM,IAAIiC,OAAO,CAAC,CAAC,CAAC,CAACZ,IAAI,IAAIY,OAAO,CAAC,CAAC,CAAC,CAACrH,KAAK,EAAE;YACpE,IAAI,CAACyD,iBAAiB,GAAG4D,OAAO;UAClC,CAAC,MAAM,IACLA,OAAO,IACP,OAAOA,OAAO,KAAK,QAAQ,IAC3B,CAACC,KAAK,CAACC,OAAO,CAACF,OAAO,CAAC,EACvB;YACA,IAAI,CAAC5D,iBAAiB,GAAG+D,MAAM,CAACC,IAAI,CAACJ,OAAO,CAAC,CAACK,GAAG,CAAEC,GAAG,KAAM;cAC1DlB,IAAI,EAAEY,OAAO,CAACM,GAAG,CAAC;cAClB3H,KAAK,EAAEqH,OAAO,CAACM,GAAG;aACnB,CAAC,CAAC;UACL,CAAC,MAAM;YACL,IAAI,CAAClE,iBAAiB,GAAG,EAAE;UAC7B;QAEF;OACD,CAAC;MACF,IAAI,CAACH,cAAc,CAAC6D,uBAAuB,EAAE,CAACD,SAAS,CAAC;QACtDE,IAAI,EAAGC,OAAO,IAAI;UAChB,IAAIA,OAAO,IAAIA,OAAO,CAACjC,MAAM,IAAIiC,OAAO,CAAC,CAAC,CAAC,CAACZ,IAAI,IAAIY,OAAO,CAAC,CAAC,CAAC,CAACrH,KAAK,EAAE;YACpE,IAAI,CAACe,iBAAiB,GAAGsG,OAAO;UAClC,CAAC,MAAM,IACLA,OAAO,IACP,OAAOA,OAAO,KAAK,QAAQ,IAC3B,CAACC,KAAK,CAACC,OAAO,CAACF,OAAO,CAAC,EACvB;YACA,IAAI,CAACtG,iBAAiB,GAAGyG,MAAM,CAACC,IAAI,CAACJ,OAAO,CAAC,CAACK,GAAG,CAAEC,GAAG,KAAM;cAC1DlB,IAAI,EAAEY,OAAO,CAACM,GAAG,CAAC;cAClB3H,KAAK,EAAEqH,OAAO,CAACM,GAAG;aACnB,CAAC,CAAC;UACL,CAAC,MAAM;YACL,IAAI,CAAC5G,iBAAiB,GAAG,EAAE;UAC7B;UAEA;UACA,IAAI,CAAC0E,QAAQ,GAAG,IAAI,CAACrC,KAAK,CAACwE,QAAQ,CAACC,QAAQ,CAAChD,GAAG,CAAC,IAAI,CAAC;UACtD,MAAMiD,YAAY,GAAG,IAAI,CAAC1E,KAAK,CAACwE,QAAQ,CAACG,aAAa,CAAClD,GAAG,CAAC,SAAS,CAAC;UACrE,MAAMmD,SAAS,GAAG,IAAI,CAAC5E,KAAK,CAACwE,QAAQ,CAACG,aAAa,CAAClD,GAAG,CAAC,MAAM,CAAC;UAE/D,IAAI,CAACxF,UAAU,GAAG,CAAC,CAAC,IAAI,CAACoG,QAAQ;UACjC,IAAI,CAACC,aAAa,GAAGoC,YAAY,KAAK,MAAM;UAC5C,IAAI,CAAC7H,UAAU,GAAG+H,SAAS,KAAK,MAAM;UACtC,IAAI,CAACrC,iBAAiB,GAAG,IAAI,CAACD,aAAa;UAE3C,IAAI,IAAI,CAACrG,UAAU,IAAI,IAAI,CAACoG,QAAQ,EAAE;YACpC,IAAI,CAACwC,cAAc,CAAC,IAAI,CAACxC,QAAQ,CAAC;UACpC,CAAC,MAAM;YACL;YACA,IAAI,CAACyC,sBAAsB,EAAE;UAC/B;UAEA;UACA,IAAI,CAACC,+BAA+B,EAAE;QACxC,CAAC;QACDC,KAAK,EAAGC,GAAG,IAAI;UACbC,OAAO,CAACF,KAAK,CAAC,qBAAqB,EAAEC,GAAG,CAAC;UACzC;UACA,IAAI,CAAC5C,QAAQ,GAAG,IAAI,CAACrC,KAAK,CAACwE,QAAQ,CAACC,QAAQ,CAAChD,GAAG,CAAC,IAAI,CAAC;UACtD,MAAMiD,YAAY,GAAG,IAAI,CAAC1E,KAAK,CAACwE,QAAQ,CAACG,aAAa,CAAClD,GAAG,CAAC,SAAS,CAAC;UACrE,MAAMmD,SAAS,GAAG,IAAI,CAAC5E,KAAK,CAACwE,QAAQ,CAACG,aAAa,CAAClD,GAAG,CAAC,MAAM,CAAC;UAE/D0D,UAAU,CAAC,MAAK;YACd,IAAI,CAAClJ,UAAU,GAAG,CAAC,CAAC,IAAI,CAACoG,QAAQ;YACjC,IAAI,CAAClC,GAAG,CAACiF,aAAa,EAAE,CAAC,CAAC;UAC5B,CAAC,CAAC;UAEF,IAAI,CAAC9C,aAAa,GAAGoC,YAAY,KAAK,MAAM;UAC5C,IAAI,CAAC7H,UAAU,GAAG+H,SAAS,KAAK,MAAM;UACtC,IAAI,CAACrC,iBAAiB,GAAG,IAAI,CAACD,aAAa;UAE3C,IAAI,IAAI,CAACrG,UAAU,IAAI,IAAI,CAACoG,QAAQ,EAAE;YACpC,IAAI,CAACwC,cAAc,CAAC,IAAI,CAACxC,QAAQ,CAAC;UACpC;QACF;OACD,CAAC;MAEF;MACA,IAAI,CAACb,UAAU,CAACC,GAAG,CAAC,YAAY,CAAC,EAAEoC,YAAY,CAACC,SAAS,CAAEuB,GAAG,IAAI;QAChE;QACA,IAAI,CAACzH,qBAAqB,GAAGyH,GAAG,IAAI,EAAE;QACtCH,OAAO,CAACI,GAAG,CACT,6BAA6B,EAC7BD,GAAG,EACH,wBAAwB,EACxB,IAAI,CAACzH,qBAAqB,CAC3B;MACH,CAAC,CAAC;MAEF;MACA,MAAM8G,YAAY,GAAG,IAAI,CAAC1E,KAAK,CAACwE,QAAQ,CAACG,aAAa,CAAClD,GAAG,CAAC,SAAS,CAAC;MACrE,IAAI,IAAI,CAACa,aAAa,IAAI,IAAI,CAACD,QAAQ,EAAE;QACvC,IAAI,CAACM,YAAY,GAAG,CAClB;UAAE4C,IAAI,EAAE,IAAI;UAAEC,IAAI,EAAE;QAAsC,CAAE,EAC5D;UAAED,IAAI,EAAE,MAAM;UAAEC,IAAI,EAAE;QAAiB,CAAE,EACzC;UAAED,IAAI,EAAE,IAAI;UAAEC,IAAI,EAAE;QAAoB,CAAE,CAC3C;QAEDL,UAAU,CAAC,MAAK;UACd,IAAI,CAAClF,oBAAoB,CAACwF,cAAc,CACtC,IAAI,CAACpD,QAAS,EACd,IAAI,CAACM,YAAY,CAClB;UACD,IAAI,CAACE,qBAAqB,GAAG,IAAI,CAAC5C,oBAAoB,CACnDyF,iBAAiB,EAAE,CACnB5B,SAAS,CAAE6B,KAAK,IAAI;YACnB,IAAIA,KAAK,CAACC,WAAW,IAAID,KAAK,CAACE,MAAM,KAAK,IAAI,CAACxD,QAAQ,EAAE;cACvD,IAAI,CAACM,YAAY,GAAGgD,KAAK,CAAChD,YAAY;YACxC;UACF,CAAC,CAAC;QACN,CAAC,EAAE,GAAG,CAAC;MACT;IACF;IAEA;IACQmD,gBAAgB,GAAW,EAAE;IAErC;IACQhB,sBAAsBA,CAAA;MAC5B,IAAI,IAAI,CAACgB,gBAAgB,IAAI,IAAI,CAACnI,iBAAiB,CAACqE,MAAM,GAAG,CAAC,EAAE;QAC9D,MAAM+D,KAAK,GAAG,IAAI,CAACC,0BAA0B,CAAC,IAAI,CAACF,gBAAgB,CAAC;QACpE,IAAIC,KAAK,EAAE;UACT;UACA,IAAI,CAACvE,UAAU,CACZC,GAAG,CAAC,YAAY,CAAC,EAChBS,QAAQ,CAAC6D,KAAK,CAACnJ,KAAK,EAAE;YAAEuF,SAAS,EAAE;UAAK,CAAE,CAAC;UAC/C,IAAI,CAACvE,qBAAqB,GAAGmI,KAAK,CAACnJ,KAAK;UAExC;UACAuI,UAAU,CAAC,MAAK;YACd,IAAI,CAAC3D,UAAU,CACZC,GAAG,CAAC,YAAY,CAAC,EAChBS,QAAQ,CAAC6D,KAAK,CAACnJ,KAAK,EAAE;cAAEuF,SAAS,EAAE;YAAK,CAAE,CAAC;YAC/C,IAAI,CAACvE,qBAAqB,GAAGmI,KAAK,CAACnJ,KAAK;UAC1C,CAAC,EAAE,GAAG,CAAC;UAEP,IAAI,CAACkJ,gBAAgB,GAAG,EAAE,CAAC,CAAC;QAC9B;MACF;IACF;IAEA;IACQf,+BAA+BA,CAAA;MACrC,MAAMkB,YAAY,GAAG,IAAI,CAACzE,UAAU,CAACC,GAAG,CAAC,YAAY,CAAC,EAAE7E,KAAK;MAC7D,IAAIqJ,YAAY,IAAI,IAAI,CAACtI,iBAAiB,CAACqE,MAAM,GAAG,CAAC,EAAE;QACrD;QACA,MAAMkE,gBAAgB,GAAG,IAAI,CAACvI,iBAAiB,CAACwI,IAAI,CACjDC,GAAG,IAAKA,GAAG,CAACxJ,KAAK,KAAKqJ,YAAY,IAAIG,GAAG,CAAC/C,IAAI,KAAK4C,YAAY,CACjE;QAED,IAAI,CAACC,gBAAgB,EAAE;UACrB;UACA,MAAMH,KAAK,GAAG,IAAI,CAACC,0BAA0B,CAACC,YAAY,CAAC;UAC3D,IAAIF,KAAK,EAAE;YACT;YACA,IAAI,CAACvE,UAAU,CACZC,GAAG,CAAC,YAAY,CAAC,EAChBS,QAAQ,CAAC6D,KAAK,CAACnJ,KAAK,EAAE;cAAEuF,SAAS,EAAE;YAAK,CAAE,CAAC;YAC/C,IAAI,CAACvE,qBAAqB,GAAGmI,KAAK,CAACnJ,KAAK;YAExC;YACAuI,UAAU,CAAC,MAAK;cACd,IAAI,CAAC3D,UAAU,CACZC,GAAG,CAAC,YAAY,CAAC,EAChBS,QAAQ,CAAC6D,KAAK,CAACnJ,KAAK,EAAE;gBAAEuF,SAAS,EAAE;cAAK,CAAE,CAAC;cAC/C,IAAI,CAACvE,qBAAqB,GAAGmI,KAAK,CAACnJ,KAAK;YAC1C,CAAC,EAAE,GAAG,CAAC;UACT;QACF;MACF;IACF;IAEA;IACQoJ,0BAA0BA,CAACxC,UAAkB;MAEnD,IAAI,CAACA,UAAU,IAAI,IAAI,CAAC7F,iBAAiB,CAACqE,MAAM,KAAK,CAAC,EAAE;QACtD,OAAO,IAAI;MACb;MAEA;MACA,IAAI+D,KAAK,GAAG,IAAI,CAACpI,iBAAiB,CAAC0I,IAAI,CACpCD,GAAG,IAAKA,GAAG,CAACxJ,KAAK,CAACuB,WAAW,EAAE,KAAKqF,UAAU,CAACrF,WAAW,EAAE,CAC9D;MAED;MACA,IAAI,CAAC4H,KAAK,EAAE;QACVA,KAAK,GAAG,IAAI,CAACpI,iBAAiB,CAAC0I,IAAI,CAChCD,GAAG,IAAKA,GAAG,CAAC/C,IAAI,CAAClF,WAAW,EAAE,KAAKqF,UAAU,CAACrF,WAAW,EAAE,CAC7D;MACH;MAEA;MACA,IAAI,CAAC4H,KAAK,EAAE;QACVA,KAAK,GAAG,IAAI,CAACpI,iBAAiB,CAAC0I,IAAI,CACjCD,GAAG,IAAIA,GAAG,CAACxJ,KAAK,CAACuB,WAAW,EAAE,CAACmI,QAAQ,CAAC9C,UAAU,CAACrF,WAAW,EAAE,CAAC,IAC/DiI,GAAG,CAAC/C,IAAI,CAAClF,WAAW,EAAE,CAACmI,QAAQ,CAAC9C,UAAU,CAACrF,WAAW,EAAE,CAAC,IACzDqF,UAAU,CAACrF,WAAW,EAAE,CAACmI,QAAQ,CAACF,GAAG,CAACxJ,KAAK,CAACuB,WAAW,EAAE,CAAC,IAC1DqF,UAAU,CAACrF,WAAW,EAAE,CAACmI,QAAQ,CAACF,GAAG,CAAC/C,IAAI,CAAClF,WAAW,EAAE,CAAC,CAC5D;MACH;MAEA,OAAO4H,KAAK,IAAI,IAAI;IACtB;IAEQlB,cAAcA,CAACxC,QAAgB;MACrC,IAAI,CAACnC,cAAc,CAACqG,aAAa,CAAClE,QAAQ,CAAC,CAACyB,SAAS,CAAC;QACpDE,IAAI,EAAGlB,MAAM,IAAI;UACf,IAAIA,MAAM,EAAE;YACV,IAAI,CAACV,UAAU,GAAGU,MAAM,CAACV,UAAU;YACnC,IAAI,CAACoE,eAAe,CAAC1D,MAAM,CAAC;UAC9B,CAAC,MAAM,CACP;QACF,CAAC;QACDkC,KAAK,EAAGC,GAAG,IAAI;UACbC,OAAO,CAACF,KAAK,CAAC,wBAAwB,EAAEC,GAAG,CAAC;QAC9C;OACD,CAAC;IACJ;IAEQuB,eAAeA,CAAC1D,MAAW;MACjC,IAAI,CAAChC,UAAU,GAAG,IAAI;MACtB;MACAqE,UAAU,CAAC,MAAK;QACd,IAAI,CAACrE,UAAU,GAAG,KAAK;QACvB,IAAI,CAACD,WAAW,GAAG,KAAK,CAAC,CAAC;MAC5B,CAAC,EAAE,CAAC,CAAC;MAEL,IAAI,IAAI,CAAChE,UAAU,EAAE;QACnBsI,UAAU,CAAC,MAAK;UACd,IAAI,CAAC3D,UAAU,CAACiF,OAAO,CAAC;YAAEtE,SAAS,EAAE;UAAK,CAAE,CAAC;QAC/C,CAAC,EAAE,CAAC,CAAC;MACP;MAEA;MACA,IAAI,CAACpF,WAAW,GAAG+F,MAAM,CAAC4D,IAAI,KAAK,WAAW,GAAG,UAAU,GAAG,UAAU;MAExE,MAAMC,WAAW,GAAG7D,MAAM,CAACpF,IAAI,CAAC,CAAC;MAEjC,MAAMkJ,WAAW,GAAG,IAAI,CAACvG,iBAAiB,CAACgG,IAAI,CAC5CD,GAAG,IAAKA,GAAG,CAAC/C,IAAI,CAAClF,WAAW,EAAE,KAAKwI,WAAW,EAAExI,WAAW,EAAE,CAC/D;MAED,IAAI,CAACqD,UAAU,CAACqF,UAAU,CAAC;QACzBnJ,IAAI,EAAEkJ,WAAW,EAAEhK;OACpB,CAAC;MACF,IAAI,CAAC2D,iBAAiB,GAAGqG,WAAW,EAAEhK,KAAK,IAAI,EAAE;MAEjD;MACA,IAAIkK,YAAY,GAAG,EAAE;MACrB,IAAIhE,MAAM,CAAC4D,IAAI,EAAE;QACf,IAAI,IAAI,CAAC/I,iBAAiB,CAACqE,MAAM,GAAG,CAAC,EAAE;UACrC;UACA,MAAM+D,KAAK,GAAG,IAAI,CAACC,0BAA0B,CAAClD,MAAM,CAAC4D,IAAI,CAAC;UAC1D,IAAIX,KAAK,EAAE;YACTe,YAAY,GAAGf,KAAK,CAACnJ,KAAK;UAC5B,CAAC,MAAM;YACL;YACAkK,YAAY,GAAGhE,MAAM,CAAC4D,IAAI;YAC1BxB,OAAO,CAAC6B,IAAI,CACV,uDAAuDjE,MAAM,CAAC4D,IAAI,uBAAuB,EACzF,IAAI,CAAC/I,iBAAiB,CACvB;UACH;QACF,CAAC,MAAM;UACL;UACA,IAAI,CAACmI,gBAAgB,GAAGhD,MAAM,CAAC4D,IAAI;UACnCI,YAAY,GAAGhE,MAAM,CAAC4D,IAAI;QAC5B;MACF,CAAC,MAAM;QACLI,YAAY,GAAGhE,MAAM,CAAC4D,IAAI,IAAI,EAAE;MAClC;MAEA;MACA,MAAMM,QAAQ,GAAQ;QACpB3D,IAAI,EAAEP,MAAM,CAACO,IAAI,IAAI,EAAE;QACvB3F,IAAI,EAAEoF,MAAM,CAACpF,IAAI,IAAI,EAAE;QACvBG,IAAI,EAAEiF,MAAM,CAACjF,IAAI,IAAI,EAAE;QACvBE,SAAS,EAAE+E,MAAM,CAAC/E,SAAS,IAAI,EAAE;QACjCE,cAAc,EAAE6E,MAAM,CAAC7E,cAAc,IAAI,EAAE;QAC3Cf,iBAAiB,EAAE4F,MAAM,CAAC5F,iBAAiB,IAAI,EAAE;QACjDsG,UAAU,EAAEsD;OACb;MAED,IAAI,IAAI,CAAC/J,WAAW,KAAK,UAAU,EAAE;QACnCiK,QAAQ,CAACzD,UAAU,GAAGT,MAAM,CAACA,MAAM,IAAI,EAAE;QACzCkE,QAAQ,CAAC1D,iBAAiB,GAAGR,MAAM,CAACQ,iBAAiB,IAAI,EAAE;QAC3D;QACA0D,QAAQ,CAAC9L,WAAW,GAAG4H,MAAM,CAAC5H,WAAW;MAC3C,CAAC,MAAM,IAAI,IAAI,CAAC6B,WAAW,KAAK,UAAU,EAAE;QAC1CiK,QAAQ,CAAC9L,WAAW,GAAG4H,MAAM,CAAC5H,WAAW,IAAI,EAAE;QAC/C8L,QAAQ,CAAC1D,iBAAiB,GAAGR,MAAM,CAACQ,iBAAiB,IAAI,EAAE;QAC3D;QACA0D,QAAQ,CAACzD,UAAU,GAAG,EAAE;MAC1B;MAEA,IAAI,CAAC/B,UAAU,CAACqF,UAAU,CAACG,QAAQ,CAAC;MAEpC;MACA,IAAI,CAACpJ,qBAAqB,GAAGkJ,YAAY;MACzC5B,OAAO,CAACI,GAAG,CAAC,iCAAiC,EAAEwB,YAAY,EAAE,wBAAwB,EAAE,IAAI,CAAClJ,qBAAqB,CAAC;MAElH;MACA,IAAI,IAAI,CAACb,WAAW,KAAK,UAAU,IAAI+J,YAAY,EAAE;QACnD3B,UAAU,CAAC,MAAK;UACd,IAAI,CAAC3D,UAAU,CACZC,GAAG,CAAC,YAAY,CAAC,EAChBS,QAAQ,CAAC4E,YAAY,EAAE;YAAE3E,SAAS,EAAE;UAAK,CAAE,CAAC;UAChD,IAAI,CAACvE,qBAAqB,GAAGkJ,YAAY;UACzC5B,OAAO,CAACI,GAAG,CACT,6BAA6B,EAC7BwB,YAAY,EACZ,wBAAwB,EACxB,IAAI,CAAClJ,qBAAqB,CAC3B;QACH,CAAC,EAAE,GAAG,CAAC;MACT;MAEA;MACA,IAAIkF,MAAM,CAACzD,QAAQ,IAAI6E,KAAK,CAACC,OAAO,CAACrB,MAAM,CAACzD,QAAQ,CAAC,EAAE;QACrD,MAAM4H,YAAY,GAAG,IAAI,CAACzF,UAAU,CAACC,GAAG,CAAC,UAAU,CAAc;QAEjE;QACAwF,YAAY,CAACC,KAAK,EAAE;QAEpB;QACApE,MAAM,CAACzD,QAAQ,CAAC8H,OAAO,CAAEC,EAAO,IAAI;UAClCH,YAAY,CAACI,IAAI,CACf,IAAI,CAACvH,EAAE,CAACsD,KAAK,CAAC;YACZzE,KAAK,EAAE,CAACyI,EAAE,CAACE,YAAY,IAAI,EAAE,CAAC;YAC9BzI,MAAM,EAAE,CAACuI,EAAE,CAACG,aAAa,IAAI,EAAE;WAChC,CAAC,CACH;QACH,CAAC,CAAC;MACJ;IACF;IAEAC,SAASA,CAACC,GAAqC;MAC7C,IAAI,CAAC1K,WAAW,GAAG0K,GAA8B;IACnD;IAEAC,WAAWA,CAAA;MACT;MACA,IAAI,IAAI,CAAC7E,qBAAqB,EAAE;QAC9B,IAAI,CAACA,qBAAqB,CAAC8E,WAAW,EAAE;MAC1C;IACF;IAEAC,MAAMA,CAAA;MACJ,IAAI,CAAC/G,WAAW,GAAG,KAAK;MAExB,IAAI,IAAI,CAAChE,UAAU,EAAE;QACnB;MACF;MACA,MAAMgL,WAAW,GAAG,IAAI,CAACrG,UAAU,CAACC,GAAG,CAAC,MAAM,CAAC;MAC/C,MAAMF,iBAAiB,GAAG,IAAI,CAACC,UAAU,CAACC,GAAG,CAAC,YAAY,CAAC;MAC3D,MAAMqG,iBAAiB,GAAG,IAAI,CAACtG,UAAU,CAACC,GAAG,CAAC,YAAY,CAAC;MAC3D,MAAMsG,iBAAiB,GAAG,IAAI,CAACvG,UAAU,CAACC,GAAG,CAAC,mBAAmB,CAAC;MAClE,MAAMuG,qBAAqB,GAAG,IAAI,CAACxG,UAAU,CAACC,GAAG,CAAC,gBAAgB,CAAC;MACnE,MAAMwG,WAAW,GAAG,IAAI,CAACzG,UAAU,CAACC,GAAG,CAAC,MAAM,CAAC;MAC/C,MAAMyG,WAAW,GAAG,IAAI,CAAC1G,UAAU,CAACC,GAAG,CAAC,MAAM,CAAC;MAC/C,MAAM0G,gBAAgB,GAAG,IAAI,CAAC3G,UAAU,CAACC,GAAG,CAAC,WAAW,CAAC;MACzD,MAAM2G,YAAY,GAAG,IAAI,CAAC5G,UAAU,CAACC,GAAG,CAAC,mBAAmB,CAAC;MAC7D;MACA2G,YAAY,EAAE1G,eAAe,EAAE;MAC/BmG,WAAW,EAAEnG,eAAe,EAAE;MAC9BH,iBAAiB,EAAEG,eAAe,EAAE;MACpCoG,iBAAiB,EAAEpG,eAAe,EAAE;MACpCqG,iBAAiB,EAAErG,eAAe,EAAE;MACpCsG,qBAAqB,EAAEtG,eAAe,EAAE;MACxCuG,WAAW,EAAEvG,eAAe,EAAE;MAC9BwG,WAAW,EAAExG,eAAe,EAAE;MAC9ByG,gBAAgB,EAAEzG,eAAe,EAAE;MAEnC;MACAmG,WAAW,EAAElG,aAAa,CAAC,CAACtI,UAAU,CAACuI,QAAQ,CAAC,CAAC;MACjDmG,iBAAiB,EAAEpG,aAAa,CAAC,CAACtI,UAAU,CAACuI,QAAQ,CAAC,CAAC;MAEvD,IAAI,IAAI,CAAC7E,WAAW,KAAK,UAAU,EAAE;QACnCgL,iBAAiB,EAAEpG,aAAa,CAAC,CAACtI,UAAU,CAACuI,QAAQ,CAAC,CAAC;QACvDoG,qBAAqB,EAAErG,aAAa,CAAC,CAACtI,UAAU,CAACuI,QAAQ,CAAC,CAAC;QAC3DqG,WAAW,EAAEtG,aAAa,CAAC,CAACtI,UAAU,CAACuI,QAAQ,CAAC,CAAC;QACjDsG,WAAW,EAAEvG,aAAa,CAAC,CAACtI,UAAU,CAACuI,QAAQ,CAAC,CAAC;QACjDuG,gBAAgB,EAAExG,aAAa,CAAC,CAACtI,UAAU,CAACuI,QAAQ,CAAC,CAAC;QACtDkG,iBAAiB,EAAEnG,aAAa,CAAC,CAACtI,UAAU,CAACuI,QAAQ,CAAC,CAAC;QACvDL,iBAAiB,EAAEG,eAAe,EAAE;QACpCH,iBAAiB,EAAEW,QAAQ,CAAC,EAAE,CAAC;MACjC,CAAC,MAAM,IAAI,IAAI,CAACnF,WAAW,KAAK,UAAU,EAAE;QAC1CwE,iBAAiB,EAAEI,aAAa,CAAC,CAACtI,UAAU,CAACuI,QAAQ,CAAC,CAAC;QACvD,IAAI,CAACJ,UAAU,CAACC,GAAG,CAAC,YAAY,CAAC,EAAES,QAAQ,CAAC,WAAW,EAAE;UAAEC,SAAS,EAAE;QAAK,CAAE,CAAC;MAChF;MAEA;MACA0F,WAAW,EAAEhG,sBAAsB,EAAE;MACrCN,iBAAiB,EAAEM,sBAAsB,EAAE;MAC3CiG,iBAAiB,EAAEjG,sBAAsB,EAAE;MAC3CkG,iBAAiB,EAAElG,sBAAsB,EAAE;MAC3CmG,qBAAqB,EAAEnG,sBAAsB,EAAE;MAC/CoG,WAAW,EAAEpG,sBAAsB,EAAE;MACrCqG,WAAW,EAAErG,sBAAsB,EAAE;MACrCsG,gBAAgB,EAAEtG,sBAAsB,EAAE;MAE1C,IAAI,IAAI,CAACL,UAAU,CAAC5E,KAAK,CAAC4G,UAAU,KAAK,kBAAkB,EAAE;QAC3D4E,YAAY,EAAEzG,aAAa,CAAC,CAACtI,UAAU,CAACuI,QAAQ,CAAC,CAAC;MACpD;MACAwG,YAAY,EAAEvG,sBAAsB,EAAE;MAEtC;MACA,IAAI,CAAC,IAAI,CAACL,UAAU,CAAC6G,KAAK,EAAE;QAC1B,IAAI,CAAC7G,UAAU,CAAC8G,gBAAgB,EAAE;QAClC;MACF;MACA,MAAMC,UAAU,GAAG,IAAI,CAAC/G,UAAU,CAAC5E,KAAK;MACxC,MAAM4L,UAAU,GAAG,IAAI,CAACxI,KAAK,CAACwE,QAAQ,CAACG,aAAa,CAAClD,GAAG,CAAC,YAAY,CAAC;MACtE,MAAMgH,UAAU,GAAGD,UAAU,GAAGE,QAAQ,CAACF,UAAU,CAAC,GAAG,CAAC;MAExD;MACA,MAAMhF,UAAU,GAAG+E,UAAU,CAAC/E,UAAU,IAAI,EAAE;MAE9C;MACA,IAAImF,aAAa,GAAQ;QACvBvG,UAAU,EAAE,IAAI,CAACA,UAAU,IAAI,CAAC;QAAE;QAClCsE,IAAI,EAAElD,UAAU;QAChBH,IAAI,EAAEkF,UAAU,CAAClF,IAAI;QACrB3F,IAAI,EAAE6K,UAAU,CAAC7K,IAAI;QACrBG,IAAI,EAAE0K,UAAU,CAAC1K,IAAI;QACrBE,SAAS,EAAEwK,UAAU,CAACxK,SAAS;QAC/B7C,WAAW,EAAEqN,UAAU,CAACrN,WAAW;QACnCoI,iBAAiB,EAAEiF,UAAU,CAACjF,iBAAiB;QAC/CrF,cAAc,EAAEsK,UAAU,CAACtK,cAAc;QACzC;QACA6E,MAAM,EACJ,IAAI,CAAC/F,WAAW,KAAK,UAAU,GAC3BwL,UAAU,CAACrN,WAAW,GACtBqN,UAAU,CAAChF,UAAU,IAAI;OAChC;MAED,IAAIC,UAAU,KAAK,WAAW,EAAE,CAChC,CAAC,MAAM,IACLA,UAAU,KAAK,qBAAqB,IACpCA,UAAU,KAAK,UAAU,IACzBA,UAAU,KAAK,YAAY,EAC3B;QACA;QACAmF,aAAa,CAACtJ,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACuJ,WAAW,EAAE,CAACtE,GAAG,CAAEuE,CAAC,KAAM;UAC/DvB,YAAY,EAAEuB,CAAC,CAAClK,KAAK;UACrB4I,aAAa,EAAEsB,CAAC,CAAChK;SAClB,CAAC,CAAC;QACH8J,aAAa,CAACpJ,uBAAuB,GAAGgJ,UAAU,CAAC7E,gBAAgB;MACrE,CAAC,MAAM,IAAIF,UAAU,KAAK,kBAAkB,EAAE;QAC5CmF,aAAa,CAACzL,iBAAiB,GAAGqL,UAAU,CAACrL,iBAAiB;QAC9D;QACAyL,aAAa,CAACtJ,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACuJ,WAAW,EAAE,CAACtE,GAAG,CAAEuE,CAAC,KAAM;UAC/DvB,YAAY,EAAEuB,CAAC,CAAClK,KAAK;UACrB4I,aAAa,EAAEsB,CAAC,CAAChK;SAClB,CAAC,CAAC;QACH8J,aAAa,CAACpJ,uBAAuB,GAAGgJ,UAAU,CAAC7E,gBAAgB;MACrE,CAAC,MAAM,IAAIF,UAAU,KAAK,WAAW,EAAE;QACrC;QACAmF,aAAa,GAAG;UACdjC,IAAI,EAAElD,UAAU;UAChBH,IAAI,EAAEkF,UAAU,CAAClF,IAAI;UACrBP,MAAM,EAAEyF,UAAU,CAAChF,UAAU,IAAI;SAClC;MACH;MAEA;MACAa,MAAM,CAACC,IAAI,CAACsE,aAAa,CAAC,CAACxB,OAAO,CAAE5C,GAAG,IAAI;QACzC,IAAIoE,aAAa,CAACpE,GAAG,CAAC,KAAKuE,SAAS,EAAE;UACpC,OAAOH,aAAa,CAACpE,GAAG,CAAC;QAC3B;MACF,CAAC,CAAC;MAEF,IAAI,IAAI,CAACtI,UAAU,IAAI,IAAI,CAACoG,QAAQ,EAAE;QACpC;QACA,MAAM0G,WAAW,GAAG;UAAE,GAAGJ,aAAa;UAAEK,EAAE,EAAE,IAAI,CAAC3G;QAAQ,CAAE;QAC3D,IAAI,CAACnC,cAAc,CAAC+I,UAAU,CAACF,WAAW,CAAC,CAACjF,SAAS,CAAC;UACpDE,IAAI,EAAGkF,IAAI,IAAI;YACb,IAAI,CAACnI,QAAQ,GAAG,cAAc;YAC9B,IAAI,CAACN,gBAAgB,GAAG,IAAI;YAC5B,IAAI,CAACG,YAAY,GAAGsI,IAAI,EAAEA,IAAI,EAAEC,OAAO,IAAID,IAAI,CAACC,OAAO;YACvD,IAAI,CAACzI,iBAAiB,GAAG,IAAI;UAC/B,CAAC;UACDsE,KAAK,EAAGA,KAAK,IAAI;YACf,IAAI,CAACjE,QAAQ,GAAG,cAAc;YAC9B,IAAI,CAACH,YAAY,GAAGoE,KAAK,EAAEA,KAAK,EAAEmE,OAAO,IAAInE,KAAK,CAACmE,OAAO;YAC1D,IAAI,CAAC1I,gBAAgB,GAAG,IAAI;YAC5B,IAAI,CAACC,iBAAiB,GAAG,KAAK;UAChC;SACD,CAAC;MACJ,CAAC,MAAM;QACL,IAAI,CAACR,cAAc,CAACkJ,SAAS,CAACT,aAAa,CAAC,CAAC7E,SAAS,CAAC;UACrDE,IAAI,EAAGkF,IAAI,IAAI;YACb,IAAI,CAACnI,QAAQ,GAAG,cAAc;YAC9B,IAAI,CAACN,gBAAgB,GAAG,IAAI;YAC5B,IAAI,CAACG,YAAY,GAAGsI,IAAI,EAAEA,IAAI,EAAEC,OAAO,IAAID,IAAI,CAACC,OAAO;YACvD,IAAI,CAACzI,iBAAiB,GAAG,IAAI;YAC7B;YACA,MAAM2I,aAAa,GAAG,IAAI,CAAC7H,UAAU,CAACC,GAAG,CAAC,UAAU,CAAc;YAClE,OAAO4H,aAAa,CAACrH,MAAM,GAAG,CAAC,EAAE;cAC/BqH,aAAa,CAACC,QAAQ,CAAC,CAAC,CAAC;YAC3B;YACAD,aAAa,CAAChC,IAAI,CAAC,IAAI,CAACvH,EAAE,CAACsD,KAAK,CAAC;cAAEzE,KAAK,EAAE,CAAC,EAAE,CAAC;cAAEE,MAAM,EAAE,CAAC,EAAE;YAAC,CAAE,CAAC,CAAC;UAClE,CAAC;UACDmG,KAAK,EAAGA,KAAK,IAAI;YACf,IAAI,CAACjE,QAAQ,GAAG,cAAc;YAC9B,IAAI,CAACH,YAAY,GAAGoE,KAAK,EAAEA,KAAK,EAAEmE,OAAO,IAAInE,KAAK,CAACmE,OAAO;YAC1D,IAAI,CAAC1I,gBAAgB,GAAG,IAAI;YAC5B,IAAI,CAACC,iBAAiB,GAAG,KAAK;UAChC;SACD,CAAC;MACJ;IACF;IAEA6I,SAASA,CAAA;MACP,IAAI,IAAI,CAAClH,QAAQ,EAAE;QACjB;QACA,IAAI,IAAI,CAACC,aAAa,IAAI,IAAI,CAACC,iBAAiB,EAAE;UAChD;QAAA,CACD,MAAM;UACL;UACA,IAAI,CAACD,aAAa,GAAG,IAAI;UACzB,IAAI,CAACC,iBAAiB,GAAG,IAAI;UAE7B;UACA,IAAI,CAACI,YAAY,GAAG,CAClB;YACE4C,IAAI,EAAE,IAAI;YACVC,IAAI,EAAE;WACP,EACD;YACED,IAAI,EAAE,MAAM;YACZC,IAAI,EAAE;WACP,EACD;YACED,IAAI,EAAE,IAAI;YACVC,IAAI,EAAE;WACP,CACF;UAED;UACAL,UAAU,CAAC,MAAK;YACd,IAAI,CAAClF,oBAAoB,CAACwF,cAAc,CACtC,IAAI,CAACpD,QAAS,EACd,IAAI,CAACM,YAAY,CAClB;UACH,CAAC,EAAE,GAAG,CAAC;QACT;MACF;IACF;IAEA6G,MAAMA,CAAA;MACJ;MACA,IAAI,IAAI,CAAClH,aAAa,IAAI,IAAI,CAACrG,UAAU,EAAE;QACzC;QACA,IAAI,CAACqG,aAAa,GAAG,KAAK;QAC1B,IAAI,CAACC,iBAAiB,GAAG,KAAK;QAC9B,IAAI,CAACtC,oBAAoB,CAACwJ,aAAa,EAAE;MAC3C,CAAC,MAAM;QACL;QACA,MAAMjB,UAAU,GAAG,IAAI,CAACxI,KAAK,CAACwE,QAAQ,CAACG,aAAa,CAAClD,GAAG,CAAC,YAAY,CAAC;QACtE,MAAMgH,UAAU,GAAGD,UAAU,GAAGE,QAAQ,CAACF,UAAU,CAAC,GAAG,CAAC;QAExD;QACA,IAAI,CAACzI,MAAM,CAAC2J,QAAQ,CAAC,CAAC,oBAAoB,CAAC,EAAE;UAC3CC,WAAW,EAAE;YAAEC,IAAI,EAAEnB;UAAU;SAChC,CAAC;MACJ;IACF;IAEA;IACAzN,UAAUA,CAACqI,IAAY;MACrB,OAAO,IAAI,CAAC7B,UAAU,CAACC,GAAG,CAAC4B,IAAI,CAAgB;IACjD;IAEA3D,gBAAgBA,CAAC2D,IAAY;MAC3B,OAAO,IAAI,CAACX,iBAAiB,CAACjB,GAAG,CAAC4B,IAAI,CAAgB;IACxD;IAEA;IACAwG,gBAAgBA,CAAA;MACd;IAAA;IAGF;IACAC,aAAaA,CAAA;MACX;IAAA;IAGF;IACAC,cAAcA,CAAC1H,QAAgB;MAC7B;MACA,MAAMS,MAAM,GAAGxJ,YAAY,EAAE+M,IAAI,CAAE2D,CAAC,IAAKA,CAAC,CAAChB,EAAE,KAAK3G,QAAQ,CAAC;MAE3D,IAAIS,MAAM,EAAE;QACV;QACA,IAAI,CAACtB,UAAU,CAACC,GAAG,CAAC,MAAM,CAAC,EAAES,QAAQ,CAACY,MAAM,CAACmH,KAAK,CAAC;QACnD,IAAI,CAACzI,UAAU,CACZC,GAAG,CAAC,aAAa,CAAC,EACjBS,QAAQ,CAAC,eAAeY,MAAM,CAACmH,KAAK,eAAe,CAAC;QAExD;QACA,IAAI,CAACzI,UAAU,CAACC,GAAG,CAAC,cAAc,CAAC,EAAES,QAAQ,CAAC,WAAW,CAAC;QAC1D,IAAI,CAACV,UAAU,CACZC,GAAG,CAAC,QAAQ,CAAC,EACZS,QAAQ,CAACY,MAAM,CAACoH,UAAU,IAAI,aAAa,CAAC;QAChD,IAAI,CAAC1I,UAAU,CACZC,GAAG,CAAC,SAAS,CAAC,EACbS,QAAQ,CAACY,MAAM,CAACqH,OAAO,IAAI,oBAAoB,CAAC;QACpD,IAAI,CAAC3I,UAAU,CAACC,GAAG,CAAC,MAAM,CAAC,EAAES,QAAQ,CAAC,UAAU,CAAC;QAEjD;QACA,IAAI,CAACV,UAAU,CACZC,GAAG,CAAC,YAAY,CAAC,EAChBS,QAAQ,CAAC,yCAAyC,CAAC;QACvD,IAAI,CAACV,UAAU,CAACC,GAAG,CAAC,MAAM,CAAC,EAAES,QAAQ,CAAC,cAAc,CAAC;QACrD,IAAI,CAACV,UAAU,CACZC,GAAG,CAAC,MAAM,CAAC,EACVS,QAAQ,CAAC,0CAA0C,CAAC;QACxD,IAAI,CAACV,UAAU,CACZC,GAAG,CAAC,WAAW,CAAC,EACfS,QAAQ,CAAC,2CAA2C,CAAC;QACzD,IAAI,CAACV,UAAU,CACZC,GAAG,CAAC,gBAAgB,CAAC,EACpBS,QAAQ,CAAC,mDAAmD,CAAC;MACnE;IACF;IAEA;IACAkI,iBAAiBA,CAACjB,OAAe;MAC/B,IAAI,IAAI,CAAC9G,QAAQ,IAAI,IAAI,CAACC,aAAa,EAAE;QACvC,IAAI,CAACM,gBAAgB,GAAG,IAAI;QAE5B,IAAI,CAACD,YAAY,CAAC0E,IAAI,CAAC;UACrB9B,IAAI,EAAE,MAAM;UACZC,IAAI,EAAE2D;SACP,CAAC;QACF;QACA;QACA;QAEA;QACAhE,UAAU,CAAC,MAAK;UACd,IAAI,CAACvC,gBAAgB,GAAG,KAAK;QAC/B,CAAC,EAAE,IAAI,CAAC;MACV;IACF;IACA,IAAIvD,QAAQA,CAAA;MACV,OAAO,IAAI,CAACmC,UAAU,CAACC,GAAG,CAAC,UAAU,CAAc;IACrD;IAEAvC,UAAUA,CAAA;MACR,IAAI,CAACG,QAAQ,CAACgI,IAAI,CAChB,IAAI,CAACvH,EAAE,CAACsD,KAAK,CAAC;QACZ4F,EAAE,EAAE,CAAC,IAAI,CAAC;QACVrK,KAAK,EAAE,CAAC,EAAE,CAAC;QACXE,MAAM,EAAE,CAAC,EAAE;OACZ,CAAC,CACH;IACH;IAEAL,aAAaA,CAACD,KAAa;MACzB;MACA,IAAIA,KAAK,GAAG,CAAC,EAAE;QACb,IAAI,CAACc,QAAQ,CAACiK,QAAQ,CAAC/K,KAAK,CAAC;MAC/B;IACF;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IAEA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA8L,UAAUA,CAAA;MACR,MAAMC,cAAc,GAAG,IAAI,CAACrH,WAAW,CAAC,IAAI,CAAClG,WAAW,CAAC;MACzD,IAAIuN,cAAc,EAAE;QAClB,IAAI,CAACxH,MAAM,CAACZ,QAAQ,CAACoI,cAAc,CAAC;QACpC,IAAI,CAACzO,iBAAiB,EAAE;QACxB,IAAI,CAAC0O,eAAe,CAACD,cAAc,CAAC;MACtC;IACF;IACAE,kBAAkBA,CAAA;MAChB,IAAI,CAAC,IAAI,CAACxJ,YAAY,EAAE;MAExB,MAAMyJ,eAAe,GAAG,IAAI,CAACzJ,YAAY,CAAC0J,QAAQ;MAElD,IAAI,IAAI,CAAClK,sBAAsB,IAAI,CAACiK,eAAe,EAAE;QACnDtF,UAAU,CAAC,MAAK;UACd,IAAI,CAACwF,kBAAkB,EAAE;QAC3B,CAAC,CAAC;MACJ;MAEA,IAAI,CAACnK,sBAAsB,GAAGiK,eAAe;IAC/C;IAEAE,kBAAkBA,CAAA;MAChB,MAAMtB,aAAa,GAAG,IAAI,CAAC7H,UAAU,CAACC,GAAG,CAAC,UAAU,CAAc;MAClE,OAAO4H,aAAa,CAACrH,MAAM,GAAG,CAAC,EAAE;QAC/BqH,aAAa,CAACC,QAAQ,CAAC,CAAC,CAAC;MAC3B;MACAD,aAAa,CAACuB,EAAE,CAAC,CAAC,CAAC,CAACnJ,GAAG,CAAC,OAAO,CAAC,EAAEoJ,KAAK,EAAE;MACzCxB,aAAa,CAACuB,EAAE,CAAC,CAAC,CAAC,CAACnJ,GAAG,CAAC,QAAQ,CAAC,EAAEoJ,KAAK,EAAE;IAC5C;IAEA;IACA5P,aAAaA,CAAC6P,SAAiB;MAC7B,MAAMC,KAAK,GAAG,IAAI,CAACvJ,UAAU,CAACC,GAAG,CAACqJ,SAAS,CAAC;MAC5C;MACA,MAAME,YAAY,GAA2B;QAC3C3H,IAAI,EAAE,aAAa;QACnBnI,WAAW,EAAE,aAAa;QAC1BqI,UAAU,EAAE,iBAAiB;QAC7B7F,IAAI,EAAE,MAAM;QACZG,IAAI,EAAE,MAAM;QACZyF,iBAAiB,EAAE,aAAa;QAChCvF,SAAS,EAAE,WAAW;QACtBE,cAAc,EAAE,gBAAgB;QAChCuF,UAAU,EAAE;OACb;MAED,MAAMyH,kBAAkB,GAAGD,YAAY,CAACF,SAAS,CAAC,IAAIA,SAAS;MAE/D,IAAIC,KAAK,IAAIA,KAAK,CAACG,OAAO,KAAKH,KAAK,CAACI,OAAO,IAAIJ,KAAK,CAACK,KAAK,CAAC,EAAE;QAC5D,IAAIL,KAAK,CAACM,MAAM,GAAG,UAAU,CAAC,EAAE;UAC9B,OAAO,GAAGJ,kBAAkB,cAAc;QAC5C;QACA,IAAIF,KAAK,CAACM,MAAM,GAAG,WAAW,CAAC,EAAE;UAC/B,OAAO,GAAGJ,kBAAkB,qBAAqBF,KAAK,CAACM,MAAM,CAAC,WAAW,CAAC,CAACC,cAAc,kBAAkB;QAC7G;MACF,CAAC,MAAM,IAAIR,SAAS,IAAI,WAAW,IAAIA,SAAS,IAAI,YAAY,EAAE;QAChE,IAAIC,KAAK,IAAIA,KAAK,CAACM,MAAM,GAAG,UAAU,CAAC,EAAE;UACvC,OAAO,GAAGJ,kBAAkB,cAAc;QAC5C;MACF;MAEA,OAAO,EAAE;IACX;IAEA7O,cAAcA,CAAA;MACZ,MAAMmF,iBAAiB,GAAG,IAAI,CAACC,UAAU,CAACC,GAAG,CAAC,YAAY,CAAC;MAE3D;MACA,IAAI,IAAI,CAAC1E,WAAW,KAAK,UAAU,EAAE;QACnCwE,iBAAiB,EAAEG,eAAe,EAAE;MACtC,CAAC,MAAM,IAAI,IAAI,CAAC3E,WAAW,KAAK,UAAU,EAAE;QAC1CwE,iBAAiB,EAAEI,aAAa,CAAC,CAACtI,UAAU,CAACuI,QAAQ,CAAC,CAAC;MACzD;MAEAL,iBAAiB,EAAEM,sBAAsB,CAAC;QAAE0J,QAAQ,EAAE,IAAI;QAAEpJ,SAAS,EAAE;MAAK,CAAE,CAAC;MAE/E,OAAO,IAAI,CAACX,UAAU,CAAC0J,OAAO,IAAI,CAAC,IAAI,CAACrK,WAAW;IACrD;IAGA;IACA2K,gBAAgBA,CAAA;MACd,IAAI,CAACC,iBAAiB,EAAE;IAC1B;IAEA;IACAA,iBAAiBA,CAAA;MACf,IAAI,CAAChL,gBAAgB,GAAG,KAAK;MAC7B,IAAI,CAACE,UAAU,GAAG,EAAE;MACpB,IAAI,CAACC,YAAY,GAAG,EAAE;MACtB,IAAI,IAAI,CAACF,iBAAiB,EAAE;QAC1B,IAAI,CAACX,MAAM,CAAC2J,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CAAC;MAC9C;IACF;IAEA7N,iBAAiBA,CAAC6P,IAAI,GAAG,IAAI;MAC3B,IAAI,CAACvK,eAAe,GAAGuK,IAAI;IAC7B;IAEAnB,eAAeA,CAACzH,MAAM,GAAG,IAAI,CAACA,MAAM,CAAClG,KAAK;MACxC,MAAM+O,aAAa,GAAG,IAAI,CAAC5O,WAAW,KAAK,UAAU;MACrD,MAAM6O,IAAI,GAAGD,aAAa,GACtB1R,YAAY,CAAC4R,eAAe,GAC5B5R,YAAY,CAAC6R,iBAAiB;MAClC,MAAMC,iBAAiB,GAAGH,IAAI,GAAG1R,mBAAmB;MACpD,IAAI,CAACmH,SAAS,GAAG,IAAI;MACrB,IAAI,CAACjB,qBAAqB,CACvB4L,QAAQ,CAAClJ,MAAM,EAAE8I,IAAI,EAAE,KAAK,EAAEG,iBAAiB,CAAC,CAChDjI,SAAS,CAAC;QACTE,IAAI,EAAGiI,GAAG,IAAI;UACZ,IAAIN,aAAa,EAAE;YACjB,IAAI,CAACjJ,iBAAiB,CAACmE,UAAU,CAAC;cAChCtD,UAAU,EAAE0I,GAAG,EAAEC,QAAQ,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAE3G;aACxC,CAAC;YAEFN,OAAO,CAACI,GAAG,CAAC,IAAI,CAAC5C,iBAAiB,CAAC;UACrC,CAAC,MAAM;YACL,IAAI0J,eAAe,GAAQ,EAAE;YAC7B,IAAI;cACFA,eAAe,GAAGC,IAAI,CAACC,KAAK,CAACL,GAAG,EAAEC,QAAQ,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAE3G,IAAI,CAAC;YAC/D,CAAC,CAAC,OAAOR,KAAK,EAAE;cACd;YACF;YACA,MAAM;cAAEuH,IAAI;cAAEC,IAAI;cAAEC,SAAS;cAAEC,WAAW;cAAEC;YAAM,CAAE,GAClDP,eAAe;YACjB,IAAI,CAAC1J,iBAAiB,CAACmE,UAAU,CAAC;cAChCnJ,IAAI,EAAE6O,IAAI;cACV1O,IAAI,EAAE2O,IAAI;cACVzO,SAAS,EAAE0O,SAAS;cACpBvR,WAAW,EAAEwR,WAAW;cACxBzO,cAAc,EAAE0O;aACjB,CAAC;UACJ;UAEA,IAAI,CAACvL,gBAAgB,GAAG,IAAI;UAC5B,IAAI,CAACC,SAAS,GAAG,KAAK;QACxB,CAAC;QACD2D,KAAK,EAAEA,CAAA,KAAK;UACV,IAAI,CAAC3D,SAAS,GAAG,KAAK;QACxB;OACD,CAAC;IACN;IAEAuL,KAAKA,CAAA;MACH,MAAMC,SAAS,GAAG,IAAI,CAACnK,iBAAiB,CAACkG,WAAW,EAAE;MACtD,IAAI,CAAC3F,WAAW,CAAC,IAAI,CAAClG,WAAW,CAAC,GAAG,IAAI,CAAC+F,MAAM,CAAClG,KAAK,IAAI,EAAE;MAC5D,IAAI,CAAC4E,UAAU,CAACqF,UAAU,CAACgG,SAAS,CAAC;MACrC,IAAI,CAACnK,iBAAiB,CAACmI,KAAK,EAAE;MAC9B,IAAI,CAAC/H,MAAM,CAAC+H,KAAK,CAAC,EAAE,CAAC;MACrB,IAAI,CAACzJ,gBAAgB,GAAG,KAAK;MAC7B,IAAI,CAACvF,iBAAiB,CAAC,KAAK,CAAC;IAC/B;IAEAiR,OAAOA,CAAA;MACL,IAAI,CAACpK,iBAAiB,CAACmI,KAAK,EAAE;MAC9B,IAAI,CAACzJ,gBAAgB,GAAG,KAAK;IAC/B;IAEA2L,QAAQA,CAAA;MACN,IAAI,CAACjK,MAAM,CAAC+H,KAAK,CAAC,EAAE,CAAC;MACrB,IAAI,CAACiC,OAAO,EAAE;MACd,IAAI,CAACjR,iBAAiB,CAAC,KAAK,CAAC;IAC/B;IAEAmR,OAAOA,CAAA;MACL,IAAI,CAACnR,iBAAiB,CAAC,KAAK,CAAC;MAC7B,IAAI,CAACkR,QAAQ,EAAE;IACjB;IACArR,QAAQA,CAAA;MACN,IAAI,CAACqE,MAAM,CAAC2J,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CAAC;IAC9C;IAEA,IAAIuD,iBAAiBA,CAAA;MACnB,OAAO,IAAI,CAAChR,UAAU,GAAG,QAAQ,GAAG,MAAM;IAC5C;;uCAr/BW4D,sBAAsB,EAAA1F,EAAA,CAAA+S,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAjT,EAAA,CAAA+S,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAnT,EAAA,CAAA+S,iBAAA,CAAAG,EAAA,CAAAE,cAAA,GAAApT,EAAA,CAAA+S,iBAAA,CAAAM,EAAA,CAAAC,oBAAA,GAAAtT,EAAA,CAAA+S,iBAAA,CAAAQ,EAAA,CAAAC,cAAA,GAAAxT,EAAA,CAAA+S,iBAAA,CAAA/S,EAAA,CAAAyT,iBAAA,GAAAzT,EAAA,CAAA+S,iBAAA,CAAAW,EAAA,CAAAC,oBAAA;IAAA;;YAAtBjO,sBAAsB;MAAAkO,SAAA;MAAAC,SAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;UClDnC/T,EAAA,CAAAC,cAAA,WAAsB;UAAAD,EAAA,CAAAE,MAAA,2BAAoB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAOpCH,EANV,CAAAC,cAAA,aAAsC,cACL,aACsE,aAEpC,aAClC,qBAC0D;UAA5BD,EAAA,CAAAiB,UAAA,mBAAAgT,6DAAA;YAAA,OAASD,GAAA,CAAA1L,eAAA,EAAiB;UAAA,EAAC;UAACtI,EAAA,CAAAG,YAAA,EAAc;UAC/FH,EAAA,CAAAqD,UAAA,IAAA6Q,sCAAA,kBAAkD;UACpDlU,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAqD,UAAA,IAAA8Q,qCAAA,kBAAmD;UAWrDnU,EAAA,CAAAG,YAAA,EAAM;UAKFH,EAFJ,CAAAC,cAAA,eAA2B,eACE,cACC;UAAAD,EAAA,CAAAE,MAAA,IAAsC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrEH,EAAA,CAAAC,cAAA,eAAuE;UAMrED,EALA,CAAAqD,UAAA,KAAA+Q,6CAAA,yBAAyF,KAAAC,6CAAA,yBAG7B,KAAAC,6CAAA,yBAOE;UAGlEtU,EADE,CAAAG,YAAA,EAAM,EACF;UAIJH,EADF,CAAAC,cAAA,eAA2B,cACD;UAAAD,EAAA,CAAAE,MAAA,IAAwC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAEnEH,EADF,CAAAC,cAAA,eAA0B,eACS;UAC/BD,EAAA,CAAAqD,UAAA,KAAAkR,kDAAA,8BACwF;UAI9FvU,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;UAGJH,EAFF,CAAAC,cAAA,eAA4B,eAED;UAYvBD,EAVA,CAAAqD,UAAA,KAAAmR,+CAAA,2BAAiD,KAAAC,+CAAA,6BAUA;UAmE/CzU,EADF,CAAAC,cAAA,eAAuC,sBAQhC;UANHD,EAAA,CAAAiB,UAAA,uBAAAyT,iEAAA;YAAA,OAAaV,GAAA,CAAA9D,UAAA,EAAY;UAAA,EAAC;UAUhClQ,EAFI,CAAAG,YAAA,EAAa,EACT,EACF;UAGNH,EAAA,CAAAqD,UAAA,KAAAsR,sCAAA,kBAAkE;UAmE5E3U,EAbQ,CAAAG,YAAA,EAAM,EACF,EAUF,EACD,EACH;UAENH,EAAA,CAAAC,cAAA,+BAE+B;UAA7BD,EADA,CAAAiB,UAAA,6BAAA2T,gFAAA;YAAA,OAAmBZ,GAAA,CAAA5D,eAAA,EAAiB;UAAA,EAAC,2BAAAyE,8EAAA;YAAA,OAAkBb,GAAA,CAAAnB,OAAA,EAAS;UAAA,EAAC,wBAAAiC,2EAAA;YAAA,OAAed,GAAA,CAAAvB,KAAA,EAAO;UAAA,EAAC,0BAAAsC,6EAAA;YAAA,OAAiBf,GAAA,CAAArB,OAAA,EAAS;UAAA,EAAC,2BAAAqC,8EAAA;YAAA,OAClGhB,GAAA,CAAApB,QAAA,EAAU;UAAA,EAAC;UAC5B5S,EAAA,CAAAC,cAAA,eAAsC;UAQpCD,EAPA,CAAAqD,UAAA,KAAA4R,+CAAA,2BAAiD,KAAAC,+CAAA,6BAOA;UAmDrDlV,EADE,CAAAG,YAAA,EAAO,EACa;;;UAtRdH,EAAA,CAAAK,SAAA,GAAwB;UAAxBL,EAAA,CAAAY,UAAA,cAAAoT,GAAA,CAAA3M,UAAA,CAAwB;UACHrH,EAAA,CAAAK,SAAA,EAAyE;UAAzEL,EAAA,CAAAY,UAAA,YAAAZ,EAAA,CAAAmV,eAAA,KAAAC,GAAA,EAAApB,GAAA,CAAA7L,aAAA,IAAA6L,GAAA,CAAA5L,iBAAA,EAAyE;UAEvEpI,EAAA,CAAAK,SAAA,EAAmC;UAAnCL,EAAA,CAAAqV,WAAA,cAAArB,GAAA,CAAA3L,eAAA,CAAmC;UAG9BrI,EAAA,CAAAK,SAAA,GAAsB;UAAtBL,EAAA,CAAAY,UAAA,UAAAoT,GAAA,CAAA3L,eAAA,CAAsB;UAEvBrI,EAAA,CAAAK,SAAA,EAAsB;UAAtBL,EAAA,CAAAY,UAAA,UAAAoT,GAAA,CAAA3L,eAAA,CAAsB;UAgBrBrI,EAAA,CAAAK,SAAA,GAAsC;UAAtCL,EAAA,CAAAsV,iBAAA,CAAAtB,GAAA,CAAAxT,YAAA,CAAA+U,mBAAA,CAAsC;UAEjDvV,EAAA,CAAAK,SAAA,GAAgB;UAAhBL,EAAA,CAAAY,UAAA,SAAAoT,GAAA,CAAAtR,UAAA,CAAgB;UAEhB1C,EAAA,CAAAK,SAAA,EAAiB;UAAjBL,EAAA,CAAAY,UAAA,UAAAoT,GAAA,CAAAtR,UAAA,CAAiB;UAGjB1C,EAAA,CAAAK,SAAA,EAAiB;UAAjBL,EAAA,CAAAY,UAAA,UAAAoT,GAAA,CAAAtR,UAAA,CAAiB;UAYR1C,EAAA,CAAAK,SAAA,GAAwC;UAAxCL,EAAA,CAAAsV,iBAAA,CAAAtB,GAAA,CAAAxT,YAAA,CAAAgV,qBAAA,CAAwC;UAG3BxV,EAAA,CAAAK,SAAA,GAAa;UAAbL,EAAA,CAAAY,UAAA,YAAAoT,GAAA,CAAAjN,UAAA,CAAa;UAUjC/G,EAAA,CAAAK,SAAA,GAAgC;UAAhCL,EAAA,CAAAY,UAAA,SAAAoT,GAAA,CAAApR,WAAA,gBAAgC;UAUhC5C,EAAA,CAAAK,SAAA,EAAgC;UAAhCL,EAAA,CAAAY,UAAA,SAAAoT,GAAA,CAAApR,WAAA,gBAAgC;UAoEK5C,EAAA,CAAAK,SAAA,GAM9C;UAN8CL,EAAA,CAAAY,UAAA,iBAAAZ,EAAA,CAAAkC,eAAA,KAAAuT,GAAA,EAM9C;UAOwBzV,EAAA,CAAAK,SAAA,EAAgC;UAAhCL,EAAA,CAAAY,UAAA,SAAAoT,GAAA,CAAApR,WAAA,gBAAgC;UAqErD5C,EAAA,CAAAK,SAAA,EAAwB;UAA2CL,EAAnE,CAAAY,UAAA,SAAAoT,GAAA,CAAAhN,eAAA,CAAwB,WAAAgN,GAAA,CAAArL,MAAA,CAAkB,cAAAqL,GAAA,CAAA9M,SAAA,CAAwB,eAAA8M,GAAA,CAAA/M,gBAAA,CAAgC;UAG/GjH,EAAA,CAAAK,SAAA,EAA+B;UAA/BL,EAAA,CAAAY,UAAA,cAAAoT,GAAA,CAAAzL,iBAAA,CAA+B;UACpBvI,EAAA,CAAAK,SAAA,EAAgC;UAAhCL,EAAA,CAAAY,UAAA,SAAAoT,GAAA,CAAApR,WAAA,gBAAgC;UAOhC5C,EAAA,CAAAK,SAAA,EAAgC;UAAhCL,EAAA,CAAAY,UAAA,SAAAoT,GAAA,CAAApR,WAAA,gBAAgC;;;qBDlM/C7D,YAAY,EAAA2W,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EACZ7W,mBAAmB,EAAAgU,EAAA,CAAA8C,aAAA,EAAA9C,EAAA,CAAA+C,eAAA,EAAA/C,EAAA,CAAAgD,oBAAA,EAAAhD,EAAA,CAAAiD,iBAAA,EAAAjD,EAAA,CAAAkD,oBAAA,EAAAlD,EAAA,CAAAmD,kBAAA,EAAAnD,EAAA,CAAAoD,eAAA,EAAApD,EAAA,CAAAqD,aAAA,EAAArD,EAAA,CAAAsD,aAAA,EACnB/W,mBAAmB,EACnBD,oBAAoB,EACpBE,eAAe,EACfG,sBAAsB,EACtBF,kBAAkB,EAElBC,iBAAiB,EACjBE,mBAAmB,EAAA2W,EAAA,CAAAC,sBAAA,EACnB3W,sBAAsB;MAAA4W,MAAA;IAAA;;SAKb/Q,sBAAsB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}