{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { isMacintosh } from '../../../base/common/platform.js';\nimport { localize } from '../../../nls.js';\nimport { ContextKeyExpr } from '../../contextkey/common/contextkey.js';\nimport { InputFocusedContext } from '../../contextkey/common/contextkeys.js';\nimport { KeybindingsRegistry } from '../../keybinding/common/keybindingsRegistry.js';\nimport { endOfQuickInputBoxContext, inQuickInputContext, quickInputTypeContextKeyValue } from './quickInput.js';\nimport { IQuickInputService, QuickPickFocus } from '../common/quickInput.js';\nconst defaultCommandAndKeybindingRule = {\n  weight: 200 /* KeybindingWeight.WorkbenchContrib */,\n  when: ContextKeyExpr.and(ContextKeyExpr.equals(quickInputTypeContextKeyValue, \"quickPick\" /* QuickInputType.QuickPick */), inQuickInputContext),\n  metadata: {\n    description: localize('quickPick', \"Used while in the context of the quick pick. If you change one keybinding for this command, you should change all of the other keybindings (modifier variants) of this command as well.\")\n  }\n};\nfunction registerQuickPickCommandAndKeybindingRule(rule, options = {}) {\n  KeybindingsRegistry.registerCommandAndKeybindingRule({\n    ...defaultCommandAndKeybindingRule,\n    ...rule,\n    secondary: getSecondary(rule.primary, rule.secondary ?? [], options)\n  });\n}\nconst ctrlKeyMod = isMacintosh ? 256 /* KeyMod.WinCtrl */ : 2048 /* KeyMod.CtrlCmd */;\n// This function will generate all the combinations of keybindings for the given primary keybinding\nfunction getSecondary(primary, secondary, options = {}) {\n  if (options.withAltMod) {\n    secondary.push(512 /* KeyMod.Alt */ + primary);\n  }\n  if (options.withCtrlMod) {\n    secondary.push(ctrlKeyMod + primary);\n    if (options.withAltMod) {\n      secondary.push(512 /* KeyMod.Alt */ + ctrlKeyMod + primary);\n    }\n  }\n  if (options.withCmdMod && isMacintosh) {\n    secondary.push(2048 /* KeyMod.CtrlCmd */ + primary);\n    if (options.withCtrlMod) {\n      secondary.push(2048 /* KeyMod.CtrlCmd */ + 256 /* KeyMod.WinCtrl */ + primary);\n    }\n    if (options.withAltMod) {\n      secondary.push(2048 /* KeyMod.CtrlCmd */ + 512 /* KeyMod.Alt */ + primary);\n      if (options.withCtrlMod) {\n        secondary.push(2048 /* KeyMod.CtrlCmd */ + 512 /* KeyMod.Alt */ + 256 /* KeyMod.WinCtrl */ + primary);\n      }\n    }\n  }\n  return secondary;\n}\n//#region Navigation\nfunction focusHandler(focus, focusOnQuickNatigate) {\n  return accessor => {\n    // Assuming this is a quick pick due to above when clause\n    const currentQuickPick = accessor.get(IQuickInputService).currentQuickInput;\n    if (!currentQuickPick) {\n      return;\n    }\n    if (focusOnQuickNatigate && currentQuickPick.quickNavigate) {\n      return currentQuickPick.focus(focusOnQuickNatigate);\n    }\n    return currentQuickPick.focus(focus);\n  };\n}\nregisterQuickPickCommandAndKeybindingRule({\n  id: 'quickInput.pageNext',\n  primary: 12 /* KeyCode.PageDown */,\n  handler: focusHandler(QuickPickFocus.NextPage)\n}, {\n  withAltMod: true,\n  withCtrlMod: true,\n  withCmdMod: true\n});\nregisterQuickPickCommandAndKeybindingRule({\n  id: 'quickInput.pagePrevious',\n  primary: 11 /* KeyCode.PageUp */,\n  handler: focusHandler(QuickPickFocus.PreviousPage)\n}, {\n  withAltMod: true,\n  withCtrlMod: true,\n  withCmdMod: true\n});\nregisterQuickPickCommandAndKeybindingRule({\n  id: 'quickInput.first',\n  primary: ctrlKeyMod + 14 /* KeyCode.Home */,\n  handler: focusHandler(QuickPickFocus.First)\n}, {\n  withAltMod: true,\n  withCmdMod: true\n});\nregisterQuickPickCommandAndKeybindingRule({\n  id: 'quickInput.last',\n  primary: ctrlKeyMod + 13 /* KeyCode.End */,\n  handler: focusHandler(QuickPickFocus.Last)\n}, {\n  withAltMod: true,\n  withCmdMod: true\n});\nregisterQuickPickCommandAndKeybindingRule({\n  id: 'quickInput.next',\n  primary: 18 /* KeyCode.DownArrow */,\n  handler: focusHandler(QuickPickFocus.Next)\n}, {\n  withCtrlMod: true\n});\nregisterQuickPickCommandAndKeybindingRule({\n  id: 'quickInput.previous',\n  primary: 16 /* KeyCode.UpArrow */,\n  handler: focusHandler(QuickPickFocus.Previous)\n}, {\n  withCtrlMod: true\n});\n// The next & previous separator commands are interesting because if we are in quick access mode, we are already holding a modifier key down.\n// In this case, we want that modifier key+up/down to navigate to the next/previous item, not the next/previous separator.\n// To handle this, we have a separate command for navigating to the next/previous separator when we are not in quick access mode.\n// If, however, we are in quick access mode, and you hold down an additional modifier key, we will navigate to the next/previous separator.\nconst nextSeparatorFallbackDesc = localize('quickInput.nextSeparatorWithQuickAccessFallback', \"If we're in quick access mode, this will navigate to the next item. If we are not in quick access mode, this will navigate to the next separator.\");\nconst prevSeparatorFallbackDesc = localize('quickInput.previousSeparatorWithQuickAccessFallback', \"If we're in quick access mode, this will navigate to the previous item. If we are not in quick access mode, this will navigate to the previous separator.\");\nif (isMacintosh) {\n  registerQuickPickCommandAndKeybindingRule({\n    id: 'quickInput.nextSeparatorWithQuickAccessFallback',\n    primary: 2048 /* KeyMod.CtrlCmd */ + 18 /* KeyCode.DownArrow */,\n    handler: focusHandler(QuickPickFocus.NextSeparator, QuickPickFocus.Next),\n    metadata: {\n      description: nextSeparatorFallbackDesc\n    }\n  });\n  registerQuickPickCommandAndKeybindingRule({\n    id: 'quickInput.nextSeparator',\n    primary: 2048 /* KeyMod.CtrlCmd */ + 512 /* KeyMod.Alt */ + 18 /* KeyCode.DownArrow */,\n\n    // Since macOS has the cmd key as the primary modifier, we need to add this additional\n    // keybinding to capture cmd+ctrl+upArrow\n    secondary: [2048 /* KeyMod.CtrlCmd */ + 256 /* KeyMod.WinCtrl */ + 18 /* KeyCode.DownArrow */],\n    handler: focusHandler(QuickPickFocus.NextSeparator)\n  }, {\n    withCtrlMod: true\n  });\n  registerQuickPickCommandAndKeybindingRule({\n    id: 'quickInput.previousSeparatorWithQuickAccessFallback',\n    primary: 2048 /* KeyMod.CtrlCmd */ + 16 /* KeyCode.UpArrow */,\n    handler: focusHandler(QuickPickFocus.PreviousSeparator, QuickPickFocus.Previous),\n    metadata: {\n      description: prevSeparatorFallbackDesc\n    }\n  });\n  registerQuickPickCommandAndKeybindingRule({\n    id: 'quickInput.previousSeparator',\n    primary: 2048 /* KeyMod.CtrlCmd */ + 512 /* KeyMod.Alt */ + 16 /* KeyCode.UpArrow */,\n\n    // Since macOS has the cmd key as the primary modifier, we need to add this additional\n    // keybinding to capture cmd+ctrl+upArrow\n    secondary: [2048 /* KeyMod.CtrlCmd */ + 256 /* KeyMod.WinCtrl */ + 16 /* KeyCode.UpArrow */],\n    handler: focusHandler(QuickPickFocus.PreviousSeparator)\n  }, {\n    withCtrlMod: true\n  });\n} else {\n  registerQuickPickCommandAndKeybindingRule({\n    id: 'quickInput.nextSeparatorWithQuickAccessFallback',\n    primary: 512 /* KeyMod.Alt */ + 18 /* KeyCode.DownArrow */,\n    handler: focusHandler(QuickPickFocus.NextSeparator, QuickPickFocus.Next),\n    metadata: {\n      description: nextSeparatorFallbackDesc\n    }\n  });\n  registerQuickPickCommandAndKeybindingRule({\n    id: 'quickInput.nextSeparator',\n    primary: 2048 /* KeyMod.CtrlCmd */ + 512 /* KeyMod.Alt */ + 18 /* KeyCode.DownArrow */,\n    handler: focusHandler(QuickPickFocus.NextSeparator)\n  });\n  registerQuickPickCommandAndKeybindingRule({\n    id: 'quickInput.previousSeparatorWithQuickAccessFallback',\n    primary: 512 /* KeyMod.Alt */ + 16 /* KeyCode.UpArrow */,\n    handler: focusHandler(QuickPickFocus.PreviousSeparator, QuickPickFocus.Previous),\n    metadata: {\n      description: prevSeparatorFallbackDesc\n    }\n  });\n  registerQuickPickCommandAndKeybindingRule({\n    id: 'quickInput.previousSeparator',\n    primary: 2048 /* KeyMod.CtrlCmd */ + 512 /* KeyMod.Alt */ + 16 /* KeyCode.UpArrow */,\n    handler: focusHandler(QuickPickFocus.PreviousSeparator)\n  });\n}\n//#endregion\n//#region Accept\nregisterQuickPickCommandAndKeybindingRule({\n  id: 'quickInput.acceptInBackground',\n  // If we are in the quick pick but the input box is not focused or our cursor is at the end of the input box\n  when: ContextKeyExpr.and(defaultCommandAndKeybindingRule.when, ContextKeyExpr.or(InputFocusedContext.negate(), endOfQuickInputBoxContext)),\n  primary: 17 /* KeyCode.RightArrow */,\n\n  // Need a little extra weight to ensure this keybinding is preferred over the default cmd+alt+right arrow keybinding\n  // https://github.com/microsoft/vscode/blob/1451e4fbbbf074a4355cc537c35b547b80ce1c52/src/vs/workbench/browser/parts/editor/editorActions.ts#L1178-L1195\n  weight: 200 /* KeybindingWeight.WorkbenchContrib */ + 50,\n  handler: accessor => {\n    const currentQuickPick = accessor.get(IQuickInputService).currentQuickInput;\n    currentQuickPick?.accept(true);\n  }\n}, {\n  withAltMod: true,\n  withCtrlMod: true,\n  withCmdMod: true\n});", "map": {"version": 3, "names": ["isMacintosh", "localize", "ContextKeyExpr", "InputFocusedContext", "KeybindingsRegistry", "endOfQuickInputBoxContext", "inQuickInputContext", "quickInputTypeContextKeyValue", "IQuickInputService", "QuickPickFocus", "defaultCommandAndKeybindingRule", "weight", "when", "and", "equals", "metadata", "description", "registerQuickPickCommandAndKeybindingRule", "rule", "options", "registerCommandAndKeybindingRule", "secondary", "getSecondary", "primary", "ctrlKeyMod", "withAltMod", "push", "withCtrlMod", "withCmdMod", "focusHandler", "focus", "focusOnQuickNatigate", "accessor", "currentQuickPick", "get", "currentQuickInput", "quickNavigate", "id", "handler", "NextPage", "PreviousPage", "First", "Last", "Next", "Previous", "nextSeparatorFallbackDesc", "prevSeparatorFallbackDesc", "NextSeparator", "PreviousSeparator", "or", "negate", "accept"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/platform/quickinput/browser/quickInputActions.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { isMacintosh } from '../../../base/common/platform.js';\nimport { localize } from '../../../nls.js';\nimport { ContextKeyExpr } from '../../contextkey/common/contextkey.js';\nimport { InputFocusedContext } from '../../contextkey/common/contextkeys.js';\nimport { KeybindingsRegistry } from '../../keybinding/common/keybindingsRegistry.js';\nimport { endOfQuickInputBoxContext, inQuickInputContext, quickInputTypeContextKeyValue } from './quickInput.js';\nimport { IQuickInputService, QuickPickFocus } from '../common/quickInput.js';\nconst defaultCommandAndKeybindingRule = {\n    weight: 200 /* KeybindingWeight.WorkbenchContrib */,\n    when: ContextKeyExpr.and(ContextKeyExpr.equals(quickInputTypeContextKeyValue, \"quickPick\" /* QuickInputType.QuickPick */), inQuickInputContext),\n    metadata: { description: localize('quickPick', \"Used while in the context of the quick pick. If you change one keybinding for this command, you should change all of the other keybindings (modifier variants) of this command as well.\") }\n};\nfunction registerQuickPickCommandAndKeybindingRule(rule, options = {}) {\n    KeybindingsRegistry.registerCommandAndKeybindingRule({\n        ...defaultCommandAndKeybindingRule,\n        ...rule,\n        secondary: getSecondary(rule.primary, rule.secondary ?? [], options)\n    });\n}\nconst ctrlKeyMod = isMacintosh ? 256 /* KeyMod.WinCtrl */ : 2048 /* KeyMod.CtrlCmd */;\n// This function will generate all the combinations of keybindings for the given primary keybinding\nfunction getSecondary(primary, secondary, options = {}) {\n    if (options.withAltMod) {\n        secondary.push(512 /* KeyMod.Alt */ + primary);\n    }\n    if (options.withCtrlMod) {\n        secondary.push(ctrlKeyMod + primary);\n        if (options.withAltMod) {\n            secondary.push(512 /* KeyMod.Alt */ + ctrlKeyMod + primary);\n        }\n    }\n    if (options.withCmdMod && isMacintosh) {\n        secondary.push(2048 /* KeyMod.CtrlCmd */ + primary);\n        if (options.withCtrlMod) {\n            secondary.push(2048 /* KeyMod.CtrlCmd */ + 256 /* KeyMod.WinCtrl */ + primary);\n        }\n        if (options.withAltMod) {\n            secondary.push(2048 /* KeyMod.CtrlCmd */ + 512 /* KeyMod.Alt */ + primary);\n            if (options.withCtrlMod) {\n                secondary.push(2048 /* KeyMod.CtrlCmd */ + 512 /* KeyMod.Alt */ + 256 /* KeyMod.WinCtrl */ + primary);\n            }\n        }\n    }\n    return secondary;\n}\n//#region Navigation\nfunction focusHandler(focus, focusOnQuickNatigate) {\n    return accessor => {\n        // Assuming this is a quick pick due to above when clause\n        const currentQuickPick = accessor.get(IQuickInputService).currentQuickInput;\n        if (!currentQuickPick) {\n            return;\n        }\n        if (focusOnQuickNatigate && currentQuickPick.quickNavigate) {\n            return currentQuickPick.focus(focusOnQuickNatigate);\n        }\n        return currentQuickPick.focus(focus);\n    };\n}\nregisterQuickPickCommandAndKeybindingRule({ id: 'quickInput.pageNext', primary: 12 /* KeyCode.PageDown */, handler: focusHandler(QuickPickFocus.NextPage) }, { withAltMod: true, withCtrlMod: true, withCmdMod: true });\nregisterQuickPickCommandAndKeybindingRule({ id: 'quickInput.pagePrevious', primary: 11 /* KeyCode.PageUp */, handler: focusHandler(QuickPickFocus.PreviousPage) }, { withAltMod: true, withCtrlMod: true, withCmdMod: true });\nregisterQuickPickCommandAndKeybindingRule({ id: 'quickInput.first', primary: ctrlKeyMod + 14 /* KeyCode.Home */, handler: focusHandler(QuickPickFocus.First) }, { withAltMod: true, withCmdMod: true });\nregisterQuickPickCommandAndKeybindingRule({ id: 'quickInput.last', primary: ctrlKeyMod + 13 /* KeyCode.End */, handler: focusHandler(QuickPickFocus.Last) }, { withAltMod: true, withCmdMod: true });\nregisterQuickPickCommandAndKeybindingRule({ id: 'quickInput.next', primary: 18 /* KeyCode.DownArrow */, handler: focusHandler(QuickPickFocus.Next) }, { withCtrlMod: true });\nregisterQuickPickCommandAndKeybindingRule({ id: 'quickInput.previous', primary: 16 /* KeyCode.UpArrow */, handler: focusHandler(QuickPickFocus.Previous) }, { withCtrlMod: true });\n// The next & previous separator commands are interesting because if we are in quick access mode, we are already holding a modifier key down.\n// In this case, we want that modifier key+up/down to navigate to the next/previous item, not the next/previous separator.\n// To handle this, we have a separate command for navigating to the next/previous separator when we are not in quick access mode.\n// If, however, we are in quick access mode, and you hold down an additional modifier key, we will navigate to the next/previous separator.\nconst nextSeparatorFallbackDesc = localize('quickInput.nextSeparatorWithQuickAccessFallback', \"If we're in quick access mode, this will navigate to the next item. If we are not in quick access mode, this will navigate to the next separator.\");\nconst prevSeparatorFallbackDesc = localize('quickInput.previousSeparatorWithQuickAccessFallback', \"If we're in quick access mode, this will navigate to the previous item. If we are not in quick access mode, this will navigate to the previous separator.\");\nif (isMacintosh) {\n    registerQuickPickCommandAndKeybindingRule({\n        id: 'quickInput.nextSeparatorWithQuickAccessFallback',\n        primary: 2048 /* KeyMod.CtrlCmd */ + 18 /* KeyCode.DownArrow */,\n        handler: focusHandler(QuickPickFocus.NextSeparator, QuickPickFocus.Next),\n        metadata: { description: nextSeparatorFallbackDesc }\n    });\n    registerQuickPickCommandAndKeybindingRule({\n        id: 'quickInput.nextSeparator',\n        primary: 2048 /* KeyMod.CtrlCmd */ + 512 /* KeyMod.Alt */ + 18 /* KeyCode.DownArrow */,\n        // Since macOS has the cmd key as the primary modifier, we need to add this additional\n        // keybinding to capture cmd+ctrl+upArrow\n        secondary: [2048 /* KeyMod.CtrlCmd */ + 256 /* KeyMod.WinCtrl */ + 18 /* KeyCode.DownArrow */],\n        handler: focusHandler(QuickPickFocus.NextSeparator)\n    }, { withCtrlMod: true });\n    registerQuickPickCommandAndKeybindingRule({\n        id: 'quickInput.previousSeparatorWithQuickAccessFallback',\n        primary: 2048 /* KeyMod.CtrlCmd */ + 16 /* KeyCode.UpArrow */,\n        handler: focusHandler(QuickPickFocus.PreviousSeparator, QuickPickFocus.Previous),\n        metadata: { description: prevSeparatorFallbackDesc }\n    });\n    registerQuickPickCommandAndKeybindingRule({\n        id: 'quickInput.previousSeparator',\n        primary: 2048 /* KeyMod.CtrlCmd */ + 512 /* KeyMod.Alt */ + 16 /* KeyCode.UpArrow */,\n        // Since macOS has the cmd key as the primary modifier, we need to add this additional\n        // keybinding to capture cmd+ctrl+upArrow\n        secondary: [2048 /* KeyMod.CtrlCmd */ + 256 /* KeyMod.WinCtrl */ + 16 /* KeyCode.UpArrow */],\n        handler: focusHandler(QuickPickFocus.PreviousSeparator)\n    }, { withCtrlMod: true });\n}\nelse {\n    registerQuickPickCommandAndKeybindingRule({\n        id: 'quickInput.nextSeparatorWithQuickAccessFallback',\n        primary: 512 /* KeyMod.Alt */ + 18 /* KeyCode.DownArrow */,\n        handler: focusHandler(QuickPickFocus.NextSeparator, QuickPickFocus.Next),\n        metadata: { description: nextSeparatorFallbackDesc }\n    });\n    registerQuickPickCommandAndKeybindingRule({\n        id: 'quickInput.nextSeparator',\n        primary: 2048 /* KeyMod.CtrlCmd */ + 512 /* KeyMod.Alt */ + 18 /* KeyCode.DownArrow */,\n        handler: focusHandler(QuickPickFocus.NextSeparator)\n    });\n    registerQuickPickCommandAndKeybindingRule({\n        id: 'quickInput.previousSeparatorWithQuickAccessFallback',\n        primary: 512 /* KeyMod.Alt */ + 16 /* KeyCode.UpArrow */,\n        handler: focusHandler(QuickPickFocus.PreviousSeparator, QuickPickFocus.Previous),\n        metadata: { description: prevSeparatorFallbackDesc }\n    });\n    registerQuickPickCommandAndKeybindingRule({\n        id: 'quickInput.previousSeparator',\n        primary: 2048 /* KeyMod.CtrlCmd */ + 512 /* KeyMod.Alt */ + 16 /* KeyCode.UpArrow */,\n        handler: focusHandler(QuickPickFocus.PreviousSeparator)\n    });\n}\n//#endregion\n//#region Accept\nregisterQuickPickCommandAndKeybindingRule({\n    id: 'quickInput.acceptInBackground',\n    // If we are in the quick pick but the input box is not focused or our cursor is at the end of the input box\n    when: ContextKeyExpr.and(defaultCommandAndKeybindingRule.when, ContextKeyExpr.or(InputFocusedContext.negate(), endOfQuickInputBoxContext)),\n    primary: 17 /* KeyCode.RightArrow */,\n    // Need a little extra weight to ensure this keybinding is preferred over the default cmd+alt+right arrow keybinding\n    // https://github.com/microsoft/vscode/blob/1451e4fbbbf074a4355cc537c35b547b80ce1c52/src/vs/workbench/browser/parts/editor/editorActions.ts#L1178-L1195\n    weight: 200 /* KeybindingWeight.WorkbenchContrib */ + 50,\n    handler: (accessor) => {\n        const currentQuickPick = accessor.get(IQuickInputService).currentQuickInput;\n        currentQuickPick?.accept(true);\n    },\n}, { withAltMod: true, withCtrlMod: true, withCmdMod: true });\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,WAAW,QAAQ,kCAAkC;AAC9D,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,cAAc,QAAQ,uCAAuC;AACtE,SAASC,mBAAmB,QAAQ,wCAAwC;AAC5E,SAASC,mBAAmB,QAAQ,gDAAgD;AACpF,SAASC,yBAAyB,EAAEC,mBAAmB,EAAEC,6BAA6B,QAAQ,iBAAiB;AAC/G,SAASC,kBAAkB,EAAEC,cAAc,QAAQ,yBAAyB;AAC5E,MAAMC,+BAA+B,GAAG;EACpCC,MAAM,EAAE,GAAG,CAAC;EACZC,IAAI,EAAEV,cAAc,CAACW,GAAG,CAACX,cAAc,CAACY,MAAM,CAACP,6BAA6B,EAAE,WAAW,CAAC,8BAA8B,CAAC,EAAED,mBAAmB,CAAC;EAC/IS,QAAQ,EAAE;IAAEC,WAAW,EAAEf,QAAQ,CAAC,WAAW,EAAE,yLAAyL;EAAE;AAC9O,CAAC;AACD,SAASgB,yCAAyCA,CAACC,IAAI,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;EACnEf,mBAAmB,CAACgB,gCAAgC,CAAC;IACjD,GAAGV,+BAA+B;IAClC,GAAGQ,IAAI;IACPG,SAAS,EAAEC,YAAY,CAACJ,IAAI,CAACK,OAAO,EAAEL,IAAI,CAACG,SAAS,IAAI,EAAE,EAAEF,OAAO;EACvE,CAAC,CAAC;AACN;AACA,MAAMK,UAAU,GAAGxB,WAAW,GAAG,GAAG,CAAC,uBAAuB,IAAI,CAAC;AACjE;AACA,SAASsB,YAAYA,CAACC,OAAO,EAAEF,SAAS,EAAEF,OAAO,GAAG,CAAC,CAAC,EAAE;EACpD,IAAIA,OAAO,CAACM,UAAU,EAAE;IACpBJ,SAAS,CAACK,IAAI,CAAC,GAAG,CAAC,mBAAmBH,OAAO,CAAC;EAClD;EACA,IAAIJ,OAAO,CAACQ,WAAW,EAAE;IACrBN,SAAS,CAACK,IAAI,CAACF,UAAU,GAAGD,OAAO,CAAC;IACpC,IAAIJ,OAAO,CAACM,UAAU,EAAE;MACpBJ,SAAS,CAACK,IAAI,CAAC,GAAG,CAAC,mBAAmBF,UAAU,GAAGD,OAAO,CAAC;IAC/D;EACJ;EACA,IAAIJ,OAAO,CAACS,UAAU,IAAI5B,WAAW,EAAE;IACnCqB,SAAS,CAACK,IAAI,CAAC,IAAI,CAAC,uBAAuBH,OAAO,CAAC;IACnD,IAAIJ,OAAO,CAACQ,WAAW,EAAE;MACrBN,SAAS,CAACK,IAAI,CAAC,IAAI,CAAC,uBAAuB,GAAG,CAAC,uBAAuBH,OAAO,CAAC;IAClF;IACA,IAAIJ,OAAO,CAACM,UAAU,EAAE;MACpBJ,SAAS,CAACK,IAAI,CAAC,IAAI,CAAC,uBAAuB,GAAG,CAAC,mBAAmBH,OAAO,CAAC;MAC1E,IAAIJ,OAAO,CAACQ,WAAW,EAAE;QACrBN,SAAS,CAACK,IAAI,CAAC,IAAI,CAAC,uBAAuB,GAAG,CAAC,mBAAmB,GAAG,CAAC,uBAAuBH,OAAO,CAAC;MACzG;IACJ;EACJ;EACA,OAAOF,SAAS;AACpB;AACA;AACA,SAASQ,YAAYA,CAACC,KAAK,EAAEC,oBAAoB,EAAE;EAC/C,OAAOC,QAAQ,IAAI;IACf;IACA,MAAMC,gBAAgB,GAAGD,QAAQ,CAACE,GAAG,CAAC1B,kBAAkB,CAAC,CAAC2B,iBAAiB;IAC3E,IAAI,CAACF,gBAAgB,EAAE;MACnB;IACJ;IACA,IAAIF,oBAAoB,IAAIE,gBAAgB,CAACG,aAAa,EAAE;MACxD,OAAOH,gBAAgB,CAACH,KAAK,CAACC,oBAAoB,CAAC;IACvD;IACA,OAAOE,gBAAgB,CAACH,KAAK,CAACA,KAAK,CAAC;EACxC,CAAC;AACL;AACAb,yCAAyC,CAAC;EAAEoB,EAAE,EAAE,qBAAqB;EAAEd,OAAO,EAAE,EAAE,CAAC;EAAwBe,OAAO,EAAET,YAAY,CAACpB,cAAc,CAAC8B,QAAQ;AAAE,CAAC,EAAE;EAAEd,UAAU,EAAE,IAAI;EAAEE,WAAW,EAAE,IAAI;EAAEC,UAAU,EAAE;AAAK,CAAC,CAAC;AACvNX,yCAAyC,CAAC;EAAEoB,EAAE,EAAE,yBAAyB;EAAEd,OAAO,EAAE,EAAE,CAAC;EAAsBe,OAAO,EAAET,YAAY,CAACpB,cAAc,CAAC+B,YAAY;AAAE,CAAC,EAAE;EAAEf,UAAU,EAAE,IAAI;EAAEE,WAAW,EAAE,IAAI;EAAEC,UAAU,EAAE;AAAK,CAAC,CAAC;AAC7NX,yCAAyC,CAAC;EAAEoB,EAAE,EAAE,kBAAkB;EAAEd,OAAO,EAAEC,UAAU,GAAG,EAAE,CAAC;EAAoBc,OAAO,EAAET,YAAY,CAACpB,cAAc,CAACgC,KAAK;AAAE,CAAC,EAAE;EAAEhB,UAAU,EAAE,IAAI;EAAEG,UAAU,EAAE;AAAK,CAAC,CAAC;AACvMX,yCAAyC,CAAC;EAAEoB,EAAE,EAAE,iBAAiB;EAAEd,OAAO,EAAEC,UAAU,GAAG,EAAE,CAAC;EAAmBc,OAAO,EAAET,YAAY,CAACpB,cAAc,CAACiC,IAAI;AAAE,CAAC,EAAE;EAAEjB,UAAU,EAAE,IAAI;EAAEG,UAAU,EAAE;AAAK,CAAC,CAAC;AACpMX,yCAAyC,CAAC;EAAEoB,EAAE,EAAE,iBAAiB;EAAEd,OAAO,EAAE,EAAE,CAAC;EAAyBe,OAAO,EAAET,YAAY,CAACpB,cAAc,CAACkC,IAAI;AAAE,CAAC,EAAE;EAAEhB,WAAW,EAAE;AAAK,CAAC,CAAC;AAC5KV,yCAAyC,CAAC;EAAEoB,EAAE,EAAE,qBAAqB;EAAEd,OAAO,EAAE,EAAE,CAAC;EAAuBe,OAAO,EAAET,YAAY,CAACpB,cAAc,CAACmC,QAAQ;AAAE,CAAC,EAAE;EAAEjB,WAAW,EAAE;AAAK,CAAC,CAAC;AAClL;AACA;AACA;AACA;AACA,MAAMkB,yBAAyB,GAAG5C,QAAQ,CAAC,iDAAiD,EAAE,mJAAmJ,CAAC;AAClP,MAAM6C,yBAAyB,GAAG7C,QAAQ,CAAC,qDAAqD,EAAE,2JAA2J,CAAC;AAC9P,IAAID,WAAW,EAAE;EACbiB,yCAAyC,CAAC;IACtCoB,EAAE,EAAE,iDAAiD;IACrDd,OAAO,EAAE,IAAI,CAAC,uBAAuB,EAAE,CAAC;IACxCe,OAAO,EAAET,YAAY,CAACpB,cAAc,CAACsC,aAAa,EAAEtC,cAAc,CAACkC,IAAI,CAAC;IACxE5B,QAAQ,EAAE;MAAEC,WAAW,EAAE6B;IAA0B;EACvD,CAAC,CAAC;EACF5B,yCAAyC,CAAC;IACtCoB,EAAE,EAAE,0BAA0B;IAC9Bd,OAAO,EAAE,IAAI,CAAC,uBAAuB,GAAG,CAAC,mBAAmB,EAAE,CAAC;;IAC/D;IACA;IACAF,SAAS,EAAE,CAAC,IAAI,CAAC,uBAAuB,GAAG,CAAC,uBAAuB,EAAE,CAAC,wBAAwB;IAC9FiB,OAAO,EAAET,YAAY,CAACpB,cAAc,CAACsC,aAAa;EACtD,CAAC,EAAE;IAAEpB,WAAW,EAAE;EAAK,CAAC,CAAC;EACzBV,yCAAyC,CAAC;IACtCoB,EAAE,EAAE,qDAAqD;IACzDd,OAAO,EAAE,IAAI,CAAC,uBAAuB,EAAE,CAAC;IACxCe,OAAO,EAAET,YAAY,CAACpB,cAAc,CAACuC,iBAAiB,EAAEvC,cAAc,CAACmC,QAAQ,CAAC;IAChF7B,QAAQ,EAAE;MAAEC,WAAW,EAAE8B;IAA0B;EACvD,CAAC,CAAC;EACF7B,yCAAyC,CAAC;IACtCoB,EAAE,EAAE,8BAA8B;IAClCd,OAAO,EAAE,IAAI,CAAC,uBAAuB,GAAG,CAAC,mBAAmB,EAAE,CAAC;;IAC/D;IACA;IACAF,SAAS,EAAE,CAAC,IAAI,CAAC,uBAAuB,GAAG,CAAC,uBAAuB,EAAE,CAAC,sBAAsB;IAC5FiB,OAAO,EAAET,YAAY,CAACpB,cAAc,CAACuC,iBAAiB;EAC1D,CAAC,EAAE;IAAErB,WAAW,EAAE;EAAK,CAAC,CAAC;AAC7B,CAAC,MACI;EACDV,yCAAyC,CAAC;IACtCoB,EAAE,EAAE,iDAAiD;IACrDd,OAAO,EAAE,GAAG,CAAC,mBAAmB,EAAE,CAAC;IACnCe,OAAO,EAAET,YAAY,CAACpB,cAAc,CAACsC,aAAa,EAAEtC,cAAc,CAACkC,IAAI,CAAC;IACxE5B,QAAQ,EAAE;MAAEC,WAAW,EAAE6B;IAA0B;EACvD,CAAC,CAAC;EACF5B,yCAAyC,CAAC;IACtCoB,EAAE,EAAE,0BAA0B;IAC9Bd,OAAO,EAAE,IAAI,CAAC,uBAAuB,GAAG,CAAC,mBAAmB,EAAE,CAAC;IAC/De,OAAO,EAAET,YAAY,CAACpB,cAAc,CAACsC,aAAa;EACtD,CAAC,CAAC;EACF9B,yCAAyC,CAAC;IACtCoB,EAAE,EAAE,qDAAqD;IACzDd,OAAO,EAAE,GAAG,CAAC,mBAAmB,EAAE,CAAC;IACnCe,OAAO,EAAET,YAAY,CAACpB,cAAc,CAACuC,iBAAiB,EAAEvC,cAAc,CAACmC,QAAQ,CAAC;IAChF7B,QAAQ,EAAE;MAAEC,WAAW,EAAE8B;IAA0B;EACvD,CAAC,CAAC;EACF7B,yCAAyC,CAAC;IACtCoB,EAAE,EAAE,8BAA8B;IAClCd,OAAO,EAAE,IAAI,CAAC,uBAAuB,GAAG,CAAC,mBAAmB,EAAE,CAAC;IAC/De,OAAO,EAAET,YAAY,CAACpB,cAAc,CAACuC,iBAAiB;EAC1D,CAAC,CAAC;AACN;AACA;AACA;AACA/B,yCAAyC,CAAC;EACtCoB,EAAE,EAAE,+BAA+B;EACnC;EACAzB,IAAI,EAAEV,cAAc,CAACW,GAAG,CAACH,+BAA+B,CAACE,IAAI,EAAEV,cAAc,CAAC+C,EAAE,CAAC9C,mBAAmB,CAAC+C,MAAM,CAAC,CAAC,EAAE7C,yBAAyB,CAAC,CAAC;EAC1IkB,OAAO,EAAE,EAAE,CAAC;;EACZ;EACA;EACAZ,MAAM,EAAE,GAAG,CAAC,0CAA0C,EAAE;EACxD2B,OAAO,EAAGN,QAAQ,IAAK;IACnB,MAAMC,gBAAgB,GAAGD,QAAQ,CAACE,GAAG,CAAC1B,kBAAkB,CAAC,CAAC2B,iBAAiB;IAC3EF,gBAAgB,EAAEkB,MAAM,CAAC,IAAI,CAAC;EAClC;AACJ,CAAC,EAAE;EAAE1B,UAAU,EAAE,IAAI;EAAEE,WAAW,EAAE,IAAI;EAAEC,UAAU,EAAE;AAAK,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}