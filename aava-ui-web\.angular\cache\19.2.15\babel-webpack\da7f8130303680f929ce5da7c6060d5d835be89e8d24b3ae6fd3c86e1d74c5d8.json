{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { buildReplaceStringWithCasePreserved } from '../../../../base/common/search.js';\n/**\n * Assigned when the replace pattern is entirely static.\n */\nclass StaticValueReplacePattern {\n  constructor(staticValue) {\n    this.staticValue = staticValue;\n    this.kind = 0 /* ReplacePatternKind.StaticValue */;\n  }\n}\n/**\n * Assigned when the replace pattern has replacement patterns.\n */\nclass DynamicPiecesReplacePattern {\n  constructor(pieces) {\n    this.pieces = pieces;\n    this.kind = 1 /* ReplacePatternKind.DynamicPieces */;\n  }\n}\nexport class ReplacePattern {\n  static fromStaticValue(value) {\n    return new ReplacePattern([ReplacePiece.staticValue(value)]);\n  }\n  get hasReplacementPatterns() {\n    return this._state.kind === 1 /* ReplacePatternKind.DynamicPieces */;\n  }\n  constructor(pieces) {\n    if (!pieces || pieces.length === 0) {\n      this._state = new StaticValueReplacePattern('');\n    } else if (pieces.length === 1 && pieces[0].staticValue !== null) {\n      this._state = new StaticValueReplacePattern(pieces[0].staticValue);\n    } else {\n      this._state = new DynamicPiecesReplacePattern(pieces);\n    }\n  }\n  buildReplaceString(matches, preserveCase) {\n    if (this._state.kind === 0 /* ReplacePatternKind.StaticValue */) {\n      if (preserveCase) {\n        return buildReplaceStringWithCasePreserved(matches, this._state.staticValue);\n      } else {\n        return this._state.staticValue;\n      }\n    }\n    let result = '';\n    for (let i = 0, len = this._state.pieces.length; i < len; i++) {\n      const piece = this._state.pieces[i];\n      if (piece.staticValue !== null) {\n        // static value ReplacePiece\n        result += piece.staticValue;\n        continue;\n      }\n      // match index ReplacePiece\n      let match = ReplacePattern._substitute(piece.matchIndex, matches);\n      if (piece.caseOps !== null && piece.caseOps.length > 0) {\n        const repl = [];\n        const lenOps = piece.caseOps.length;\n        let opIdx = 0;\n        for (let idx = 0, len = match.length; idx < len; idx++) {\n          if (opIdx >= lenOps) {\n            repl.push(match.slice(idx));\n            break;\n          }\n          switch (piece.caseOps[opIdx]) {\n            case 'U':\n              repl.push(match[idx].toUpperCase());\n              break;\n            case 'u':\n              repl.push(match[idx].toUpperCase());\n              opIdx++;\n              break;\n            case 'L':\n              repl.push(match[idx].toLowerCase());\n              break;\n            case 'l':\n              repl.push(match[idx].toLowerCase());\n              opIdx++;\n              break;\n            default:\n              repl.push(match[idx]);\n          }\n        }\n        match = repl.join('');\n      }\n      result += match;\n    }\n    return result;\n  }\n  static _substitute(matchIndex, matches) {\n    if (matches === null) {\n      return '';\n    }\n    if (matchIndex === 0) {\n      return matches[0];\n    }\n    let remainder = '';\n    while (matchIndex > 0) {\n      if (matchIndex < matches.length) {\n        // A match can be undefined\n        const match = matches[matchIndex] || '';\n        return match + remainder;\n      }\n      remainder = String(matchIndex % 10) + remainder;\n      matchIndex = Math.floor(matchIndex / 10);\n    }\n    return '$' + remainder;\n  }\n}\n/**\n * A replace piece can either be a static string or an index to a specific match.\n */\nexport class ReplacePiece {\n  static staticValue(value) {\n    return new ReplacePiece(value, -1, null);\n  }\n  static caseOps(index, caseOps) {\n    return new ReplacePiece(null, index, caseOps);\n  }\n  constructor(staticValue, matchIndex, caseOps) {\n    this.staticValue = staticValue;\n    this.matchIndex = matchIndex;\n    if (!caseOps || caseOps.length === 0) {\n      this.caseOps = null;\n    } else {\n      this.caseOps = caseOps.slice(0);\n    }\n  }\n}\nclass ReplacePieceBuilder {\n  constructor(source) {\n    this._source = source;\n    this._lastCharIndex = 0;\n    this._result = [];\n    this._resultLen = 0;\n    this._currentStaticPiece = '';\n  }\n  emitUnchanged(toCharIndex) {\n    this._emitStatic(this._source.substring(this._lastCharIndex, toCharIndex));\n    this._lastCharIndex = toCharIndex;\n  }\n  emitStatic(value, toCharIndex) {\n    this._emitStatic(value);\n    this._lastCharIndex = toCharIndex;\n  }\n  _emitStatic(value) {\n    if (value.length === 0) {\n      return;\n    }\n    this._currentStaticPiece += value;\n  }\n  emitMatchIndex(index, toCharIndex, caseOps) {\n    if (this._currentStaticPiece.length !== 0) {\n      this._result[this._resultLen++] = ReplacePiece.staticValue(this._currentStaticPiece);\n      this._currentStaticPiece = '';\n    }\n    this._result[this._resultLen++] = ReplacePiece.caseOps(index, caseOps);\n    this._lastCharIndex = toCharIndex;\n  }\n  finalize() {\n    this.emitUnchanged(this._source.length);\n    if (this._currentStaticPiece.length !== 0) {\n      this._result[this._resultLen++] = ReplacePiece.staticValue(this._currentStaticPiece);\n      this._currentStaticPiece = '';\n    }\n    return new ReplacePattern(this._result);\n  }\n}\n/**\n * \\n\t\t\t=> inserts a LF\n * \\t\t\t\t=> inserts a TAB\n * \\\\\t\t\t=> inserts a \"\\\".\n * \\u\t\t\t=> upper-cases one character in a match.\n * \\U\t\t\t=> upper-cases ALL remaining characters in a match.\n * \\l\t\t\t=> lower-cases one character in a match.\n * \\L\t\t\t=> lower-cases ALL remaining characters in a match.\n * $$\t\t\t=> inserts a \"$\".\n * $& and $0\t=> inserts the matched substring.\n * $n\t\t\t=> Where n is a non-negative integer lesser than 100, inserts the nth parenthesized submatch string\n * everything else stays untouched\n *\n * Also see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/replace#Specifying_a_string_as_a_parameter\n */\nexport function parseReplaceString(replaceString) {\n  if (!replaceString || replaceString.length === 0) {\n    return new ReplacePattern(null);\n  }\n  const caseOps = [];\n  const result = new ReplacePieceBuilder(replaceString);\n  for (let i = 0, len = replaceString.length; i < len; i++) {\n    const chCode = replaceString.charCodeAt(i);\n    if (chCode === 92 /* CharCode.Backslash */) {\n      // move to next char\n      i++;\n      if (i >= len) {\n        // string ends with a \\\n        break;\n      }\n      const nextChCode = replaceString.charCodeAt(i);\n      // let replaceWithCharacter: string | null = null;\n      switch (nextChCode) {\n        case 92 /* CharCode.Backslash */:\n          // \\\\ => inserts a \"\\\"\n          result.emitUnchanged(i - 1);\n          result.emitStatic('\\\\', i + 1);\n          break;\n        case 110 /* CharCode.n */:\n          // \\n => inserts a LF\n          result.emitUnchanged(i - 1);\n          result.emitStatic('\\n', i + 1);\n          break;\n        case 116 /* CharCode.t */:\n          // \\t => inserts a TAB\n          result.emitUnchanged(i - 1);\n          result.emitStatic('\\t', i + 1);\n          break;\n        // Case modification of string replacements, patterned after Boost, but only applied\n        // to the replacement text, not subsequent content.\n        case 117 /* CharCode.u */:\n        // \\u => upper-cases one character.\n        case 85 /* CharCode.U */:\n        // \\U => upper-cases ALL following characters.\n        case 108 /* CharCode.l */:\n        // \\l => lower-cases one character.\n        case 76 /* CharCode.L */:\n          // \\L => lower-cases ALL following characters.\n          result.emitUnchanged(i - 1);\n          result.emitStatic('', i + 1);\n          caseOps.push(String.fromCharCode(nextChCode));\n          break;\n      }\n      continue;\n    }\n    if (chCode === 36 /* CharCode.DollarSign */) {\n      // move to next char\n      i++;\n      if (i >= len) {\n        // string ends with a $\n        break;\n      }\n      const nextChCode = replaceString.charCodeAt(i);\n      if (nextChCode === 36 /* CharCode.DollarSign */) {\n        // $$ => inserts a \"$\"\n        result.emitUnchanged(i - 1);\n        result.emitStatic('$', i + 1);\n        continue;\n      }\n      if (nextChCode === 48 /* CharCode.Digit0 */ || nextChCode === 38 /* CharCode.Ampersand */) {\n        // $& and $0 => inserts the matched substring.\n        result.emitUnchanged(i - 1);\n        result.emitMatchIndex(0, i + 1, caseOps);\n        caseOps.length = 0;\n        continue;\n      }\n      if (49 /* CharCode.Digit1 */ <= nextChCode && nextChCode <= 57 /* CharCode.Digit9 */) {\n        // $n\n        let matchIndex = nextChCode - 48 /* CharCode.Digit0 */;\n        // peek next char to probe for $nn\n        if (i + 1 < len) {\n          const nextNextChCode = replaceString.charCodeAt(i + 1);\n          if (48 /* CharCode.Digit0 */ <= nextNextChCode && nextNextChCode <= 57 /* CharCode.Digit9 */) {\n            // $nn\n            // move to next char\n            i++;\n            matchIndex = matchIndex * 10 + (nextNextChCode - 48 /* CharCode.Digit0 */);\n            result.emitUnchanged(i - 2);\n            result.emitMatchIndex(matchIndex, i + 1, caseOps);\n            caseOps.length = 0;\n            continue;\n          }\n        }\n        result.emitUnchanged(i - 1);\n        result.emitMatchIndex(matchIndex, i + 1, caseOps);\n        caseOps.length = 0;\n        continue;\n      }\n    }\n  }\n  return result.finalize();\n}", "map": {"version": 3, "names": ["buildReplaceStringWithCasePreserved", "StaticValueReplacePattern", "constructor", "staticValue", "kind", "DynamicPiecesReplacePattern", "pieces", "ReplacePattern", "fromStaticValue", "value", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hasReplacementPatterns", "_state", "length", "buildReplaceString", "matches", "preserveCase", "result", "i", "len", "piece", "match", "_substitute", "matchIndex", "caseOps", "repl", "lenOps", "opIdx", "idx", "push", "slice", "toUpperCase", "toLowerCase", "join", "remainder", "String", "Math", "floor", "index", "ReplacePieceBuilder", "source", "_source", "_lastCharIndex", "_result", "_resultLen", "_currentStaticPiece", "emitUnchanged", "toCharIndex", "_emitStatic", "substring", "emitStatic", "emitMatchIndex", "finalize", "parseReplaceString", "replaceString", "chCode", "charCodeAt", "nextChCode", "fromCharCode", "nextNextChCode"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/contrib/find/browser/replacePattern.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { buildReplaceStringWithCasePreserved } from '../../../../base/common/search.js';\n/**\n * Assigned when the replace pattern is entirely static.\n */\nclass StaticValueReplacePattern {\n    constructor(staticValue) {\n        this.staticValue = staticValue;\n        this.kind = 0 /* ReplacePatternKind.StaticValue */;\n    }\n}\n/**\n * Assigned when the replace pattern has replacement patterns.\n */\nclass DynamicPiecesReplacePattern {\n    constructor(pieces) {\n        this.pieces = pieces;\n        this.kind = 1 /* ReplacePatternKind.DynamicPieces */;\n    }\n}\nexport class ReplacePattern {\n    static fromStaticValue(value) {\n        return new ReplacePattern([ReplacePiece.staticValue(value)]);\n    }\n    get hasReplacementPatterns() {\n        return (this._state.kind === 1 /* ReplacePatternKind.DynamicPieces */);\n    }\n    constructor(pieces) {\n        if (!pieces || pieces.length === 0) {\n            this._state = new StaticValueReplacePattern('');\n        }\n        else if (pieces.length === 1 && pieces[0].staticValue !== null) {\n            this._state = new StaticValueReplacePattern(pieces[0].staticValue);\n        }\n        else {\n            this._state = new DynamicPiecesReplacePattern(pieces);\n        }\n    }\n    buildReplaceString(matches, preserveCase) {\n        if (this._state.kind === 0 /* ReplacePatternKind.StaticValue */) {\n            if (preserveCase) {\n                return buildReplaceStringWithCasePreserved(matches, this._state.staticValue);\n            }\n            else {\n                return this._state.staticValue;\n            }\n        }\n        let result = '';\n        for (let i = 0, len = this._state.pieces.length; i < len; i++) {\n            const piece = this._state.pieces[i];\n            if (piece.staticValue !== null) {\n                // static value ReplacePiece\n                result += piece.staticValue;\n                continue;\n            }\n            // match index ReplacePiece\n            let match = ReplacePattern._substitute(piece.matchIndex, matches);\n            if (piece.caseOps !== null && piece.caseOps.length > 0) {\n                const repl = [];\n                const lenOps = piece.caseOps.length;\n                let opIdx = 0;\n                for (let idx = 0, len = match.length; idx < len; idx++) {\n                    if (opIdx >= lenOps) {\n                        repl.push(match.slice(idx));\n                        break;\n                    }\n                    switch (piece.caseOps[opIdx]) {\n                        case 'U':\n                            repl.push(match[idx].toUpperCase());\n                            break;\n                        case 'u':\n                            repl.push(match[idx].toUpperCase());\n                            opIdx++;\n                            break;\n                        case 'L':\n                            repl.push(match[idx].toLowerCase());\n                            break;\n                        case 'l':\n                            repl.push(match[idx].toLowerCase());\n                            opIdx++;\n                            break;\n                        default:\n                            repl.push(match[idx]);\n                    }\n                }\n                match = repl.join('');\n            }\n            result += match;\n        }\n        return result;\n    }\n    static _substitute(matchIndex, matches) {\n        if (matches === null) {\n            return '';\n        }\n        if (matchIndex === 0) {\n            return matches[0];\n        }\n        let remainder = '';\n        while (matchIndex > 0) {\n            if (matchIndex < matches.length) {\n                // A match can be undefined\n                const match = (matches[matchIndex] || '');\n                return match + remainder;\n            }\n            remainder = String(matchIndex % 10) + remainder;\n            matchIndex = Math.floor(matchIndex / 10);\n        }\n        return '$' + remainder;\n    }\n}\n/**\n * A replace piece can either be a static string or an index to a specific match.\n */\nexport class ReplacePiece {\n    static staticValue(value) {\n        return new ReplacePiece(value, -1, null);\n    }\n    static caseOps(index, caseOps) {\n        return new ReplacePiece(null, index, caseOps);\n    }\n    constructor(staticValue, matchIndex, caseOps) {\n        this.staticValue = staticValue;\n        this.matchIndex = matchIndex;\n        if (!caseOps || caseOps.length === 0) {\n            this.caseOps = null;\n        }\n        else {\n            this.caseOps = caseOps.slice(0);\n        }\n    }\n}\nclass ReplacePieceBuilder {\n    constructor(source) {\n        this._source = source;\n        this._lastCharIndex = 0;\n        this._result = [];\n        this._resultLen = 0;\n        this._currentStaticPiece = '';\n    }\n    emitUnchanged(toCharIndex) {\n        this._emitStatic(this._source.substring(this._lastCharIndex, toCharIndex));\n        this._lastCharIndex = toCharIndex;\n    }\n    emitStatic(value, toCharIndex) {\n        this._emitStatic(value);\n        this._lastCharIndex = toCharIndex;\n    }\n    _emitStatic(value) {\n        if (value.length === 0) {\n            return;\n        }\n        this._currentStaticPiece += value;\n    }\n    emitMatchIndex(index, toCharIndex, caseOps) {\n        if (this._currentStaticPiece.length !== 0) {\n            this._result[this._resultLen++] = ReplacePiece.staticValue(this._currentStaticPiece);\n            this._currentStaticPiece = '';\n        }\n        this._result[this._resultLen++] = ReplacePiece.caseOps(index, caseOps);\n        this._lastCharIndex = toCharIndex;\n    }\n    finalize() {\n        this.emitUnchanged(this._source.length);\n        if (this._currentStaticPiece.length !== 0) {\n            this._result[this._resultLen++] = ReplacePiece.staticValue(this._currentStaticPiece);\n            this._currentStaticPiece = '';\n        }\n        return new ReplacePattern(this._result);\n    }\n}\n/**\n * \\n\t\t\t=> inserts a LF\n * \\t\t\t\t=> inserts a TAB\n * \\\\\t\t\t=> inserts a \"\\\".\n * \\u\t\t\t=> upper-cases one character in a match.\n * \\U\t\t\t=> upper-cases ALL remaining characters in a match.\n * \\l\t\t\t=> lower-cases one character in a match.\n * \\L\t\t\t=> lower-cases ALL remaining characters in a match.\n * $$\t\t\t=> inserts a \"$\".\n * $& and $0\t=> inserts the matched substring.\n * $n\t\t\t=> Where n is a non-negative integer lesser than 100, inserts the nth parenthesized submatch string\n * everything else stays untouched\n *\n * Also see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/replace#Specifying_a_string_as_a_parameter\n */\nexport function parseReplaceString(replaceString) {\n    if (!replaceString || replaceString.length === 0) {\n        return new ReplacePattern(null);\n    }\n    const caseOps = [];\n    const result = new ReplacePieceBuilder(replaceString);\n    for (let i = 0, len = replaceString.length; i < len; i++) {\n        const chCode = replaceString.charCodeAt(i);\n        if (chCode === 92 /* CharCode.Backslash */) {\n            // move to next char\n            i++;\n            if (i >= len) {\n                // string ends with a \\\n                break;\n            }\n            const nextChCode = replaceString.charCodeAt(i);\n            // let replaceWithCharacter: string | null = null;\n            switch (nextChCode) {\n                case 92 /* CharCode.Backslash */:\n                    // \\\\ => inserts a \"\\\"\n                    result.emitUnchanged(i - 1);\n                    result.emitStatic('\\\\', i + 1);\n                    break;\n                case 110 /* CharCode.n */:\n                    // \\n => inserts a LF\n                    result.emitUnchanged(i - 1);\n                    result.emitStatic('\\n', i + 1);\n                    break;\n                case 116 /* CharCode.t */:\n                    // \\t => inserts a TAB\n                    result.emitUnchanged(i - 1);\n                    result.emitStatic('\\t', i + 1);\n                    break;\n                // Case modification of string replacements, patterned after Boost, but only applied\n                // to the replacement text, not subsequent content.\n                case 117 /* CharCode.u */:\n                // \\u => upper-cases one character.\n                case 85 /* CharCode.U */:\n                // \\U => upper-cases ALL following characters.\n                case 108 /* CharCode.l */:\n                // \\l => lower-cases one character.\n                case 76 /* CharCode.L */:\n                    // \\L => lower-cases ALL following characters.\n                    result.emitUnchanged(i - 1);\n                    result.emitStatic('', i + 1);\n                    caseOps.push(String.fromCharCode(nextChCode));\n                    break;\n            }\n            continue;\n        }\n        if (chCode === 36 /* CharCode.DollarSign */) {\n            // move to next char\n            i++;\n            if (i >= len) {\n                // string ends with a $\n                break;\n            }\n            const nextChCode = replaceString.charCodeAt(i);\n            if (nextChCode === 36 /* CharCode.DollarSign */) {\n                // $$ => inserts a \"$\"\n                result.emitUnchanged(i - 1);\n                result.emitStatic('$', i + 1);\n                continue;\n            }\n            if (nextChCode === 48 /* CharCode.Digit0 */ || nextChCode === 38 /* CharCode.Ampersand */) {\n                // $& and $0 => inserts the matched substring.\n                result.emitUnchanged(i - 1);\n                result.emitMatchIndex(0, i + 1, caseOps);\n                caseOps.length = 0;\n                continue;\n            }\n            if (49 /* CharCode.Digit1 */ <= nextChCode && nextChCode <= 57 /* CharCode.Digit9 */) {\n                // $n\n                let matchIndex = nextChCode - 48 /* CharCode.Digit0 */;\n                // peek next char to probe for $nn\n                if (i + 1 < len) {\n                    const nextNextChCode = replaceString.charCodeAt(i + 1);\n                    if (48 /* CharCode.Digit0 */ <= nextNextChCode && nextNextChCode <= 57 /* CharCode.Digit9 */) {\n                        // $nn\n                        // move to next char\n                        i++;\n                        matchIndex = matchIndex * 10 + (nextNextChCode - 48 /* CharCode.Digit0 */);\n                        result.emitUnchanged(i - 2);\n                        result.emitMatchIndex(matchIndex, i + 1, caseOps);\n                        caseOps.length = 0;\n                        continue;\n                    }\n                }\n                result.emitUnchanged(i - 1);\n                result.emitMatchIndex(matchIndex, i + 1, caseOps);\n                caseOps.length = 0;\n                continue;\n            }\n        }\n    }\n    return result.finalize();\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,mCAAmC,QAAQ,mCAAmC;AACvF;AACA;AACA;AACA,MAAMC,yBAAyB,CAAC;EAC5BC,WAAWA,CAACC,WAAW,EAAE;IACrB,IAAI,CAACA,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,IAAI,GAAG,CAAC,CAAC;EAClB;AACJ;AACA;AACA;AACA;AACA,MAAMC,2BAA2B,CAAC;EAC9BH,WAAWA,CAACI,MAAM,EAAE;IAChB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACF,IAAI,GAAG,CAAC,CAAC;EAClB;AACJ;AACA,OAAO,MAAMG,cAAc,CAAC;EACxB,OAAOC,eAAeA,CAACC,KAAK,EAAE;IAC1B,OAAO,IAAIF,cAAc,CAAC,CAACG,YAAY,CAACP,WAAW,CAACM,KAAK,CAAC,CAAC,CAAC;EAChE;EACA,IAAIE,sBAAsBA,CAAA,EAAG;IACzB,OAAQ,IAAI,CAACC,MAAM,CAACR,IAAI,KAAK,CAAC,CAAC;EACnC;EACAF,WAAWA,CAACI,MAAM,EAAE;IAChB,IAAI,CAACA,MAAM,IAAIA,MAAM,CAACO,MAAM,KAAK,CAAC,EAAE;MAChC,IAAI,CAACD,MAAM,GAAG,IAAIX,yBAAyB,CAAC,EAAE,CAAC;IACnD,CAAC,MACI,IAAIK,MAAM,CAACO,MAAM,KAAK,CAAC,IAAIP,MAAM,CAAC,CAAC,CAAC,CAACH,WAAW,KAAK,IAAI,EAAE;MAC5D,IAAI,CAACS,MAAM,GAAG,IAAIX,yBAAyB,CAACK,MAAM,CAAC,CAAC,CAAC,CAACH,WAAW,CAAC;IACtE,CAAC,MACI;MACD,IAAI,CAACS,MAAM,GAAG,IAAIP,2BAA2B,CAACC,MAAM,CAAC;IACzD;EACJ;EACAQ,kBAAkBA,CAACC,OAAO,EAAEC,YAAY,EAAE;IACtC,IAAI,IAAI,CAACJ,MAAM,CAACR,IAAI,KAAK,CAAC,CAAC,sCAAsC;MAC7D,IAAIY,YAAY,EAAE;QACd,OAAOhB,mCAAmC,CAACe,OAAO,EAAE,IAAI,CAACH,MAAM,CAACT,WAAW,CAAC;MAChF,CAAC,MACI;QACD,OAAO,IAAI,CAACS,MAAM,CAACT,WAAW;MAClC;IACJ;IACA,IAAIc,MAAM,GAAG,EAAE;IACf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAG,IAAI,CAACP,MAAM,CAACN,MAAM,CAACO,MAAM,EAAEK,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;MAC3D,MAAME,KAAK,GAAG,IAAI,CAACR,MAAM,CAACN,MAAM,CAACY,CAAC,CAAC;MACnC,IAAIE,KAAK,CAACjB,WAAW,KAAK,IAAI,EAAE;QAC5B;QACAc,MAAM,IAAIG,KAAK,CAACjB,WAAW;QAC3B;MACJ;MACA;MACA,IAAIkB,KAAK,GAAGd,cAAc,CAACe,WAAW,CAACF,KAAK,CAACG,UAAU,EAAER,OAAO,CAAC;MACjE,IAAIK,KAAK,CAACI,OAAO,KAAK,IAAI,IAAIJ,KAAK,CAACI,OAAO,CAACX,MAAM,GAAG,CAAC,EAAE;QACpD,MAAMY,IAAI,GAAG,EAAE;QACf,MAAMC,MAAM,GAAGN,KAAK,CAACI,OAAO,CAACX,MAAM;QACnC,IAAIc,KAAK,GAAG,CAAC;QACb,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAET,GAAG,GAAGE,KAAK,CAACR,MAAM,EAAEe,GAAG,GAAGT,GAAG,EAAES,GAAG,EAAE,EAAE;UACpD,IAAID,KAAK,IAAID,MAAM,EAAE;YACjBD,IAAI,CAACI,IAAI,CAACR,KAAK,CAACS,KAAK,CAACF,GAAG,CAAC,CAAC;YAC3B;UACJ;UACA,QAAQR,KAAK,CAACI,OAAO,CAACG,KAAK,CAAC;YACxB,KAAK,GAAG;cACJF,IAAI,CAACI,IAAI,CAACR,KAAK,CAACO,GAAG,CAAC,CAACG,WAAW,CAAC,CAAC,CAAC;cACnC;YACJ,KAAK,GAAG;cACJN,IAAI,CAACI,IAAI,CAACR,KAAK,CAACO,GAAG,CAAC,CAACG,WAAW,CAAC,CAAC,CAAC;cACnCJ,KAAK,EAAE;cACP;YACJ,KAAK,GAAG;cACJF,IAAI,CAACI,IAAI,CAACR,KAAK,CAACO,GAAG,CAAC,CAACI,WAAW,CAAC,CAAC,CAAC;cACnC;YACJ,KAAK,GAAG;cACJP,IAAI,CAACI,IAAI,CAACR,KAAK,CAACO,GAAG,CAAC,CAACI,WAAW,CAAC,CAAC,CAAC;cACnCL,KAAK,EAAE;cACP;YACJ;cACIF,IAAI,CAACI,IAAI,CAACR,KAAK,CAACO,GAAG,CAAC,CAAC;UAC7B;QACJ;QACAP,KAAK,GAAGI,IAAI,CAACQ,IAAI,CAAC,EAAE,CAAC;MACzB;MACAhB,MAAM,IAAII,KAAK;IACnB;IACA,OAAOJ,MAAM;EACjB;EACA,OAAOK,WAAWA,CAACC,UAAU,EAAER,OAAO,EAAE;IACpC,IAAIA,OAAO,KAAK,IAAI,EAAE;MAClB,OAAO,EAAE;IACb;IACA,IAAIQ,UAAU,KAAK,CAAC,EAAE;MAClB,OAAOR,OAAO,CAAC,CAAC,CAAC;IACrB;IACA,IAAImB,SAAS,GAAG,EAAE;IAClB,OAAOX,UAAU,GAAG,CAAC,EAAE;MACnB,IAAIA,UAAU,GAAGR,OAAO,CAACF,MAAM,EAAE;QAC7B;QACA,MAAMQ,KAAK,GAAIN,OAAO,CAACQ,UAAU,CAAC,IAAI,EAAG;QACzC,OAAOF,KAAK,GAAGa,SAAS;MAC5B;MACAA,SAAS,GAAGC,MAAM,CAACZ,UAAU,GAAG,EAAE,CAAC,GAAGW,SAAS;MAC/CX,UAAU,GAAGa,IAAI,CAACC,KAAK,CAACd,UAAU,GAAG,EAAE,CAAC;IAC5C;IACA,OAAO,GAAG,GAAGW,SAAS;EAC1B;AACJ;AACA;AACA;AACA;AACA,OAAO,MAAMxB,YAAY,CAAC;EACtB,OAAOP,WAAWA,CAACM,KAAK,EAAE;IACtB,OAAO,IAAIC,YAAY,CAACD,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC;EAC5C;EACA,OAAOe,OAAOA,CAACc,KAAK,EAAEd,OAAO,EAAE;IAC3B,OAAO,IAAId,YAAY,CAAC,IAAI,EAAE4B,KAAK,EAAEd,OAAO,CAAC;EACjD;EACAtB,WAAWA,CAACC,WAAW,EAAEoB,UAAU,EAAEC,OAAO,EAAE;IAC1C,IAAI,CAACrB,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACoB,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,OAAO,IAAIA,OAAO,CAACX,MAAM,KAAK,CAAC,EAAE;MAClC,IAAI,CAACW,OAAO,GAAG,IAAI;IACvB,CAAC,MACI;MACD,IAAI,CAACA,OAAO,GAAGA,OAAO,CAACM,KAAK,CAAC,CAAC,CAAC;IACnC;EACJ;AACJ;AACA,MAAMS,mBAAmB,CAAC;EACtBrC,WAAWA,CAACsC,MAAM,EAAE;IAChB,IAAI,CAACC,OAAO,GAAGD,MAAM;IACrB,IAAI,CAACE,cAAc,GAAG,CAAC;IACvB,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACC,UAAU,GAAG,CAAC;IACnB,IAAI,CAACC,mBAAmB,GAAG,EAAE;EACjC;EACAC,aAAaA,CAACC,WAAW,EAAE;IACvB,IAAI,CAACC,WAAW,CAAC,IAAI,CAACP,OAAO,CAACQ,SAAS,CAAC,IAAI,CAACP,cAAc,EAAEK,WAAW,CAAC,CAAC;IAC1E,IAAI,CAACL,cAAc,GAAGK,WAAW;EACrC;EACAG,UAAUA,CAACzC,KAAK,EAAEsC,WAAW,EAAE;IAC3B,IAAI,CAACC,WAAW,CAACvC,KAAK,CAAC;IACvB,IAAI,CAACiC,cAAc,GAAGK,WAAW;EACrC;EACAC,WAAWA,CAACvC,KAAK,EAAE;IACf,IAAIA,KAAK,CAACI,MAAM,KAAK,CAAC,EAAE;MACpB;IACJ;IACA,IAAI,CAACgC,mBAAmB,IAAIpC,KAAK;EACrC;EACA0C,cAAcA,CAACb,KAAK,EAAES,WAAW,EAAEvB,OAAO,EAAE;IACxC,IAAI,IAAI,CAACqB,mBAAmB,CAAChC,MAAM,KAAK,CAAC,EAAE;MACvC,IAAI,CAAC8B,OAAO,CAAC,IAAI,CAACC,UAAU,EAAE,CAAC,GAAGlC,YAAY,CAACP,WAAW,CAAC,IAAI,CAAC0C,mBAAmB,CAAC;MACpF,IAAI,CAACA,mBAAmB,GAAG,EAAE;IACjC;IACA,IAAI,CAACF,OAAO,CAAC,IAAI,CAACC,UAAU,EAAE,CAAC,GAAGlC,YAAY,CAACc,OAAO,CAACc,KAAK,EAAEd,OAAO,CAAC;IACtE,IAAI,CAACkB,cAAc,GAAGK,WAAW;EACrC;EACAK,QAAQA,CAAA,EAAG;IACP,IAAI,CAACN,aAAa,CAAC,IAAI,CAACL,OAAO,CAAC5B,MAAM,CAAC;IACvC,IAAI,IAAI,CAACgC,mBAAmB,CAAChC,MAAM,KAAK,CAAC,EAAE;MACvC,IAAI,CAAC8B,OAAO,CAAC,IAAI,CAACC,UAAU,EAAE,CAAC,GAAGlC,YAAY,CAACP,WAAW,CAAC,IAAI,CAAC0C,mBAAmB,CAAC;MACpF,IAAI,CAACA,mBAAmB,GAAG,EAAE;IACjC;IACA,OAAO,IAAItC,cAAc,CAAC,IAAI,CAACoC,OAAO,CAAC;EAC3C;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASU,kBAAkBA,CAACC,aAAa,EAAE;EAC9C,IAAI,CAACA,aAAa,IAAIA,aAAa,CAACzC,MAAM,KAAK,CAAC,EAAE;IAC9C,OAAO,IAAIN,cAAc,CAAC,IAAI,CAAC;EACnC;EACA,MAAMiB,OAAO,GAAG,EAAE;EAClB,MAAMP,MAAM,GAAG,IAAIsB,mBAAmB,CAACe,aAAa,CAAC;EACrD,KAAK,IAAIpC,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGmC,aAAa,CAACzC,MAAM,EAAEK,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;IACtD,MAAMqC,MAAM,GAAGD,aAAa,CAACE,UAAU,CAACtC,CAAC,CAAC;IAC1C,IAAIqC,MAAM,KAAK,EAAE,CAAC,0BAA0B;MACxC;MACArC,CAAC,EAAE;MACH,IAAIA,CAAC,IAAIC,GAAG,EAAE;QACV;QACA;MACJ;MACA,MAAMsC,UAAU,GAAGH,aAAa,CAACE,UAAU,CAACtC,CAAC,CAAC;MAC9C;MACA,QAAQuC,UAAU;QACd,KAAK,EAAE,CAAC;UACJ;UACAxC,MAAM,CAAC6B,aAAa,CAAC5B,CAAC,GAAG,CAAC,CAAC;UAC3BD,MAAM,CAACiC,UAAU,CAAC,IAAI,EAAEhC,CAAC,GAAG,CAAC,CAAC;UAC9B;QACJ,KAAK,GAAG,CAAC;UACL;UACAD,MAAM,CAAC6B,aAAa,CAAC5B,CAAC,GAAG,CAAC,CAAC;UAC3BD,MAAM,CAACiC,UAAU,CAAC,IAAI,EAAEhC,CAAC,GAAG,CAAC,CAAC;UAC9B;QACJ,KAAK,GAAG,CAAC;UACL;UACAD,MAAM,CAAC6B,aAAa,CAAC5B,CAAC,GAAG,CAAC,CAAC;UAC3BD,MAAM,CAACiC,UAAU,CAAC,IAAI,EAAEhC,CAAC,GAAG,CAAC,CAAC;UAC9B;QACJ;QACA;QACA,KAAK,GAAG,CAAC;QACT;QACA,KAAK,EAAE,CAAC;QACR;QACA,KAAK,GAAG,CAAC;QACT;QACA,KAAK,EAAE,CAAC;UACJ;UACAD,MAAM,CAAC6B,aAAa,CAAC5B,CAAC,GAAG,CAAC,CAAC;UAC3BD,MAAM,CAACiC,UAAU,CAAC,EAAE,EAAEhC,CAAC,GAAG,CAAC,CAAC;UAC5BM,OAAO,CAACK,IAAI,CAACM,MAAM,CAACuB,YAAY,CAACD,UAAU,CAAC,CAAC;UAC7C;MACR;MACA;IACJ;IACA,IAAIF,MAAM,KAAK,EAAE,CAAC,2BAA2B;MACzC;MACArC,CAAC,EAAE;MACH,IAAIA,CAAC,IAAIC,GAAG,EAAE;QACV;QACA;MACJ;MACA,MAAMsC,UAAU,GAAGH,aAAa,CAACE,UAAU,CAACtC,CAAC,CAAC;MAC9C,IAAIuC,UAAU,KAAK,EAAE,CAAC,2BAA2B;QAC7C;QACAxC,MAAM,CAAC6B,aAAa,CAAC5B,CAAC,GAAG,CAAC,CAAC;QAC3BD,MAAM,CAACiC,UAAU,CAAC,GAAG,EAAEhC,CAAC,GAAG,CAAC,CAAC;QAC7B;MACJ;MACA,IAAIuC,UAAU,KAAK,EAAE,CAAC,yBAAyBA,UAAU,KAAK,EAAE,CAAC,0BAA0B;QACvF;QACAxC,MAAM,CAAC6B,aAAa,CAAC5B,CAAC,GAAG,CAAC,CAAC;QAC3BD,MAAM,CAACkC,cAAc,CAAC,CAAC,EAAEjC,CAAC,GAAG,CAAC,EAAEM,OAAO,CAAC;QACxCA,OAAO,CAACX,MAAM,GAAG,CAAC;QAClB;MACJ;MACA,IAAI,EAAE,CAAC,yBAAyB4C,UAAU,IAAIA,UAAU,IAAI,EAAE,CAAC,uBAAuB;QAClF;QACA,IAAIlC,UAAU,GAAGkC,UAAU,GAAG,EAAE,CAAC;QACjC;QACA,IAAIvC,CAAC,GAAG,CAAC,GAAGC,GAAG,EAAE;UACb,MAAMwC,cAAc,GAAGL,aAAa,CAACE,UAAU,CAACtC,CAAC,GAAG,CAAC,CAAC;UACtD,IAAI,EAAE,CAAC,yBAAyByC,cAAc,IAAIA,cAAc,IAAI,EAAE,CAAC,uBAAuB;YAC1F;YACA;YACAzC,CAAC,EAAE;YACHK,UAAU,GAAGA,UAAU,GAAG,EAAE,IAAIoC,cAAc,GAAG,EAAE,CAAC,sBAAsB;YAC1E1C,MAAM,CAAC6B,aAAa,CAAC5B,CAAC,GAAG,CAAC,CAAC;YAC3BD,MAAM,CAACkC,cAAc,CAAC5B,UAAU,EAAEL,CAAC,GAAG,CAAC,EAAEM,OAAO,CAAC;YACjDA,OAAO,CAACX,MAAM,GAAG,CAAC;YAClB;UACJ;QACJ;QACAI,MAAM,CAAC6B,aAAa,CAAC5B,CAAC,GAAG,CAAC,CAAC;QAC3BD,MAAM,CAACkC,cAAc,CAAC5B,UAAU,EAAEL,CAAC,GAAG,CAAC,EAAEM,OAAO,CAAC;QACjDA,OAAO,CAACX,MAAM,GAAG,CAAC;QAClB;MACJ;IACJ;EACJ;EACA,OAAOI,MAAM,CAACmC,QAAQ,CAAC,CAAC;AAC5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}