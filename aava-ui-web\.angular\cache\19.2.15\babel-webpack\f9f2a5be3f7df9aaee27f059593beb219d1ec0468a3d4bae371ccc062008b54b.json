{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { registerSingleton } from '../../../platform/instantiation/common/extensions.js';\nimport { createDecorator } from '../../../platform/instantiation/common/instantiation.js';\nimport { TreeViewsDnDService } from './treeViewsDnd.js';\nexport const ITreeViewsDnDService = createDecorator('treeViewsDndService');\nregisterSing<PERSON>(ITreeViewsDnDService, TreeViewsDnDService, 1 /* InstantiationType.Delayed */);", "map": {"version": 3, "names": ["registerSingleton", "createDecorator", "TreeViewsDnDService", "ITreeViewsDnDService"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/services/treeViewsDndService.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { registerSingleton } from '../../../platform/instantiation/common/extensions.js';\nimport { createDecorator } from '../../../platform/instantiation/common/instantiation.js';\nimport { TreeViewsDnDService } from './treeViewsDnd.js';\nexport const ITreeViewsDnDService = createDecorator('treeViewsDndService');\nregisterSing<PERSON>(ITreeViewsDnDService, TreeViewsDnDService, 1 /* InstantiationType.Delayed */);\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,iBAAiB,QAAQ,sDAAsD;AACxF,SAASC,eAAe,QAAQ,yDAAyD;AACzF,SAASC,mBAAmB,QAAQ,mBAAmB;AACvD,OAAO,MAAMC,oBAAoB,GAAGF,eAAe,CAAC,qBAAqB,CAAC;AAC1ED,iBAAiB,CAACG,oBAAoB,EAAED,mBAAmB,EAAE,CAAC,CAAC,+BAA+B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}