{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nfunction countMapFrom(values) {\n  const map = new Map();\n  for (const value of values) {\n    map.set(value, (map.get(value) ?? 0) + 1);\n  }\n  return map;\n}\n/**\n * Implementation of tf-idf (term frequency-inverse document frequency) for a set of\n * documents where each document contains one or more chunks of text.\n * Each document is identified by a key, and the score for each document is computed\n * by taking the max score over all the chunks in the document.\n */\nexport class TfIdfCalculator {\n  constructor() {\n    /**\n     * Total number of chunks\n     */\n    this.chunkCount = 0;\n    this.chunkOccurrences = new Map();\n    this.documents = new Map();\n  }\n  calculateScores(query, token) {\n    const embedding = this.computeEmbedding(query);\n    const idfCache = new Map();\n    const scores = [];\n    // For each document, generate one score\n    for (const [key, doc] of this.documents) {\n      if (token.isCancellationRequested) {\n        return [];\n      }\n      for (const chunk of doc.chunks) {\n        const score = this.computeSimilarityScore(chunk, embedding, idfCache);\n        if (score > 0) {\n          scores.push({\n            key,\n            score\n          });\n        }\n      }\n    }\n    return scores;\n  }\n  /**\n   * Count how many times each term (word) appears in a string.\n   */\n  static termFrequencies(input) {\n    return countMapFrom(TfIdfCalculator.splitTerms(input));\n  }\n  /**\n   * Break a string into terms (words).\n   */\n  static *splitTerms(input) {\n    const normalize = word => word.toLowerCase();\n    // Only match on words that are at least 3 characters long and start with a letter\n    for (const [word] of input.matchAll(/\\b\\p{Letter}[\\p{Letter}\\d]{2,}\\b/gu)) {\n      yield normalize(word);\n      const camelParts = word.replace(/([a-z])([A-Z])/g, '$1 $2').split(/\\s+/g);\n      if (camelParts.length > 1) {\n        for (const part of camelParts) {\n          // Require at least 3 letters in the parts of a camel case word\n          if (part.length > 2 && /\\p{Letter}{3,}/gu.test(part)) {\n            yield normalize(part);\n          }\n        }\n      }\n    }\n  }\n  updateDocuments(documents) {\n    for (const {\n      key\n    } of documents) {\n      this.deleteDocument(key);\n    }\n    for (const doc of documents) {\n      const chunks = [];\n      for (const text of doc.textChunks) {\n        // TODO: See if we can compute the tf lazily\n        // The challenge is that we need to also update the `chunkOccurrences`\n        // and all of those updates need to get flushed before the real TF-IDF of\n        // anything is computed.\n        const tf = TfIdfCalculator.termFrequencies(text);\n        // Update occurrences list\n        for (const term of tf.keys()) {\n          this.chunkOccurrences.set(term, (this.chunkOccurrences.get(term) ?? 0) + 1);\n        }\n        chunks.push({\n          text,\n          tf\n        });\n      }\n      this.chunkCount += chunks.length;\n      this.documents.set(doc.key, {\n        chunks\n      });\n    }\n    return this;\n  }\n  deleteDocument(key) {\n    const doc = this.documents.get(key);\n    if (!doc) {\n      return;\n    }\n    this.documents.delete(key);\n    this.chunkCount -= doc.chunks.length;\n    // Update term occurrences for the document\n    for (const chunk of doc.chunks) {\n      for (const term of chunk.tf.keys()) {\n        const currentOccurrences = this.chunkOccurrences.get(term);\n        if (typeof currentOccurrences === 'number') {\n          const newOccurrences = currentOccurrences - 1;\n          if (newOccurrences <= 0) {\n            this.chunkOccurrences.delete(term);\n          } else {\n            this.chunkOccurrences.set(term, newOccurrences);\n          }\n        }\n      }\n    }\n  }\n  computeSimilarityScore(chunk, queryEmbedding, idfCache) {\n    // Compute the dot product between the chunk's embedding and the query embedding\n    // Note that the chunk embedding is computed lazily on a per-term basis.\n    // This lets us skip a large number of calculations because the majority\n    // of chunks do not share any terms with the query.\n    let sum = 0;\n    for (const [term, termTfidf] of Object.entries(queryEmbedding)) {\n      const chunkTf = chunk.tf.get(term);\n      if (!chunkTf) {\n        // Term does not appear in chunk so it has no contribution\n        continue;\n      }\n      let chunkIdf = idfCache.get(term);\n      if (typeof chunkIdf !== 'number') {\n        chunkIdf = this.computeIdf(term);\n        idfCache.set(term, chunkIdf);\n      }\n      const chunkTfidf = chunkTf * chunkIdf;\n      sum += chunkTfidf * termTfidf;\n    }\n    return sum;\n  }\n  computeEmbedding(input) {\n    const tf = TfIdfCalculator.termFrequencies(input);\n    return this.computeTfidf(tf);\n  }\n  computeIdf(term) {\n    const chunkOccurrences = this.chunkOccurrences.get(term) ?? 0;\n    return chunkOccurrences > 0 ? Math.log((this.chunkCount + 1) / chunkOccurrences) : 0;\n  }\n  computeTfidf(termFrequencies) {\n    const embedding = Object.create(null);\n    for (const [word, occurrences] of termFrequencies) {\n      const idf = this.computeIdf(word);\n      if (idf > 0) {\n        embedding[word] = occurrences * idf;\n      }\n    }\n    return embedding;\n  }\n}\n/**\n * Normalize the scores to be between 0 and 1 and sort them decending.\n * @param scores array of scores from {@link TfIdfCalculator.calculateScores}\n * @returns normalized scores\n */\nexport function normalizeTfIdfScores(scores) {\n  // copy of scores\n  const result = scores.slice(0);\n  // sort descending\n  result.sort((a, b) => b.score - a.score);\n  // normalize\n  const max = result[0]?.score ?? 0;\n  if (max > 0) {\n    for (const score of result) {\n      score.score /= max;\n    }\n  }\n  return result;\n}", "map": {"version": 3, "names": ["countMapFrom", "values", "map", "Map", "value", "set", "get", "TfIdfCalculator", "constructor", "chunkCount", "chunkOccurrences", "documents", "calculateScores", "query", "token", "embedding", "computeEmbedding", "idfCache", "scores", "key", "doc", "isCancellationRequested", "chunk", "chunks", "score", "computeSimilarityScore", "push", "termFrequencies", "input", "splitTerms", "normalize", "word", "toLowerCase", "matchAll", "camelParts", "replace", "split", "length", "part", "test", "updateDocuments", "deleteDocument", "text", "textChunks", "tf", "term", "keys", "delete", "currentOccurrences", "newOccurrences", "queryEmbe<PERSON>", "sum", "termTfidf", "Object", "entries", "chunkTf", "chunkIdf", "computeIdf", "chunkTfidf", "computeTfidf", "Math", "log", "create", "occurrences", "idf", "normalizeTfIdfScores", "result", "slice", "sort", "a", "b", "max"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/common/tfIdf.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nfunction countMapFrom(values) {\n    const map = new Map();\n    for (const value of values) {\n        map.set(value, (map.get(value) ?? 0) + 1);\n    }\n    return map;\n}\n/**\n * Implementation of tf-idf (term frequency-inverse document frequency) for a set of\n * documents where each document contains one or more chunks of text.\n * Each document is identified by a key, and the score for each document is computed\n * by taking the max score over all the chunks in the document.\n */\nexport class TfIdfCalculator {\n    constructor() {\n        /**\n         * Total number of chunks\n         */\n        this.chunkCount = 0;\n        this.chunkOccurrences = new Map();\n        this.documents = new Map();\n    }\n    calculateScores(query, token) {\n        const embedding = this.computeEmbedding(query);\n        const idfCache = new Map();\n        const scores = [];\n        // For each document, generate one score\n        for (const [key, doc] of this.documents) {\n            if (token.isCancellationRequested) {\n                return [];\n            }\n            for (const chunk of doc.chunks) {\n                const score = this.computeSimilarityScore(chunk, embedding, idfCache);\n                if (score > 0) {\n                    scores.push({ key, score });\n                }\n            }\n        }\n        return scores;\n    }\n    /**\n     * Count how many times each term (word) appears in a string.\n     */\n    static termFrequencies(input) {\n        return countMapFrom(TfIdfCalculator.splitTerms(input));\n    }\n    /**\n     * Break a string into terms (words).\n     */\n    static *splitTerms(input) {\n        const normalize = (word) => word.toLowerCase();\n        // Only match on words that are at least 3 characters long and start with a letter\n        for (const [word] of input.matchAll(/\\b\\p{Letter}[\\p{Letter}\\d]{2,}\\b/gu)) {\n            yield normalize(word);\n            const camelParts = word.replace(/([a-z])([A-Z])/g, '$1 $2').split(/\\s+/g);\n            if (camelParts.length > 1) {\n                for (const part of camelParts) {\n                    // Require at least 3 letters in the parts of a camel case word\n                    if (part.length > 2 && /\\p{Letter}{3,}/gu.test(part)) {\n                        yield normalize(part);\n                    }\n                }\n            }\n        }\n    }\n    updateDocuments(documents) {\n        for (const { key } of documents) {\n            this.deleteDocument(key);\n        }\n        for (const doc of documents) {\n            const chunks = [];\n            for (const text of doc.textChunks) {\n                // TODO: See if we can compute the tf lazily\n                // The challenge is that we need to also update the `chunkOccurrences`\n                // and all of those updates need to get flushed before the real TF-IDF of\n                // anything is computed.\n                const tf = TfIdfCalculator.termFrequencies(text);\n                // Update occurrences list\n                for (const term of tf.keys()) {\n                    this.chunkOccurrences.set(term, (this.chunkOccurrences.get(term) ?? 0) + 1);\n                }\n                chunks.push({ text, tf });\n            }\n            this.chunkCount += chunks.length;\n            this.documents.set(doc.key, { chunks });\n        }\n        return this;\n    }\n    deleteDocument(key) {\n        const doc = this.documents.get(key);\n        if (!doc) {\n            return;\n        }\n        this.documents.delete(key);\n        this.chunkCount -= doc.chunks.length;\n        // Update term occurrences for the document\n        for (const chunk of doc.chunks) {\n            for (const term of chunk.tf.keys()) {\n                const currentOccurrences = this.chunkOccurrences.get(term);\n                if (typeof currentOccurrences === 'number') {\n                    const newOccurrences = currentOccurrences - 1;\n                    if (newOccurrences <= 0) {\n                        this.chunkOccurrences.delete(term);\n                    }\n                    else {\n                        this.chunkOccurrences.set(term, newOccurrences);\n                    }\n                }\n            }\n        }\n    }\n    computeSimilarityScore(chunk, queryEmbedding, idfCache) {\n        // Compute the dot product between the chunk's embedding and the query embedding\n        // Note that the chunk embedding is computed lazily on a per-term basis.\n        // This lets us skip a large number of calculations because the majority\n        // of chunks do not share any terms with the query.\n        let sum = 0;\n        for (const [term, termTfidf] of Object.entries(queryEmbedding)) {\n            const chunkTf = chunk.tf.get(term);\n            if (!chunkTf) {\n                // Term does not appear in chunk so it has no contribution\n                continue;\n            }\n            let chunkIdf = idfCache.get(term);\n            if (typeof chunkIdf !== 'number') {\n                chunkIdf = this.computeIdf(term);\n                idfCache.set(term, chunkIdf);\n            }\n            const chunkTfidf = chunkTf * chunkIdf;\n            sum += chunkTfidf * termTfidf;\n        }\n        return sum;\n    }\n    computeEmbedding(input) {\n        const tf = TfIdfCalculator.termFrequencies(input);\n        return this.computeTfidf(tf);\n    }\n    computeIdf(term) {\n        const chunkOccurrences = this.chunkOccurrences.get(term) ?? 0;\n        return chunkOccurrences > 0\n            ? Math.log((this.chunkCount + 1) / chunkOccurrences)\n            : 0;\n    }\n    computeTfidf(termFrequencies) {\n        const embedding = Object.create(null);\n        for (const [word, occurrences] of termFrequencies) {\n            const idf = this.computeIdf(word);\n            if (idf > 0) {\n                embedding[word] = occurrences * idf;\n            }\n        }\n        return embedding;\n    }\n}\n/**\n * Normalize the scores to be between 0 and 1 and sort them decending.\n * @param scores array of scores from {@link TfIdfCalculator.calculateScores}\n * @returns normalized scores\n */\nexport function normalizeTfIdfScores(scores) {\n    // copy of scores\n    const result = scores.slice(0);\n    // sort descending\n    result.sort((a, b) => b.score - a.score);\n    // normalize\n    const max = result[0]?.score ?? 0;\n    if (max > 0) {\n        for (const score of result) {\n            score.score /= max;\n        }\n    }\n    return result;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,YAAYA,CAACC,MAAM,EAAE;EAC1B,MAAMC,GAAG,GAAG,IAAIC,GAAG,CAAC,CAAC;EACrB,KAAK,MAAMC,KAAK,IAAIH,MAAM,EAAE;IACxBC,GAAG,CAACG,GAAG,CAACD,KAAK,EAAE,CAACF,GAAG,CAACI,GAAG,CAACF,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;EAC7C;EACA,OAAOF,GAAG;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMK,eAAe,CAAC;EACzBC,WAAWA,CAAA,EAAG;IACV;AACR;AACA;IACQ,IAAI,CAACC,UAAU,GAAG,CAAC;IACnB,IAAI,CAACC,gBAAgB,GAAG,IAAIP,GAAG,CAAC,CAAC;IACjC,IAAI,CAACQ,SAAS,GAAG,IAAIR,GAAG,CAAC,CAAC;EAC9B;EACAS,eAAeA,CAACC,KAAK,EAAEC,KAAK,EAAE;IAC1B,MAAMC,SAAS,GAAG,IAAI,CAACC,gBAAgB,CAACH,KAAK,CAAC;IAC9C,MAAMI,QAAQ,GAAG,IAAId,GAAG,CAAC,CAAC;IAC1B,MAAMe,MAAM,GAAG,EAAE;IACjB;IACA,KAAK,MAAM,CAACC,GAAG,EAAEC,GAAG,CAAC,IAAI,IAAI,CAACT,SAAS,EAAE;MACrC,IAAIG,KAAK,CAACO,uBAAuB,EAAE;QAC/B,OAAO,EAAE;MACb;MACA,KAAK,MAAMC,KAAK,IAAIF,GAAG,CAACG,MAAM,EAAE;QAC5B,MAAMC,KAAK,GAAG,IAAI,CAACC,sBAAsB,CAACH,KAAK,EAAEP,SAAS,EAAEE,QAAQ,CAAC;QACrE,IAAIO,KAAK,GAAG,CAAC,EAAE;UACXN,MAAM,CAACQ,IAAI,CAAC;YAAEP,GAAG;YAAEK;UAAM,CAAC,CAAC;QAC/B;MACJ;IACJ;IACA,OAAON,MAAM;EACjB;EACA;AACJ;AACA;EACI,OAAOS,eAAeA,CAACC,KAAK,EAAE;IAC1B,OAAO5B,YAAY,CAACO,eAAe,CAACsB,UAAU,CAACD,KAAK,CAAC,CAAC;EAC1D;EACA;AACJ;AACA;EACI,QAAQC,UAAUA,CAACD,KAAK,EAAE;IACtB,MAAME,SAAS,GAAIC,IAAI,IAAKA,IAAI,CAACC,WAAW,CAAC,CAAC;IAC9C;IACA,KAAK,MAAM,CAACD,IAAI,CAAC,IAAIH,KAAK,CAACK,QAAQ,CAAC,oCAAoC,CAAC,EAAE;MACvE,MAAMH,SAAS,CAACC,IAAI,CAAC;MACrB,MAAMG,UAAU,GAAGH,IAAI,CAACI,OAAO,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAACC,KAAK,CAAC,MAAM,CAAC;MACzE,IAAIF,UAAU,CAACG,MAAM,GAAG,CAAC,EAAE;QACvB,KAAK,MAAMC,IAAI,IAAIJ,UAAU,EAAE;UAC3B;UACA,IAAII,IAAI,CAACD,MAAM,GAAG,CAAC,IAAI,kBAAkB,CAACE,IAAI,CAACD,IAAI,CAAC,EAAE;YAClD,MAAMR,SAAS,CAACQ,IAAI,CAAC;UACzB;QACJ;MACJ;IACJ;EACJ;EACAE,eAAeA,CAAC7B,SAAS,EAAE;IACvB,KAAK,MAAM;MAAEQ;IAAI,CAAC,IAAIR,SAAS,EAAE;MAC7B,IAAI,CAAC8B,cAAc,CAACtB,GAAG,CAAC;IAC5B;IACA,KAAK,MAAMC,GAAG,IAAIT,SAAS,EAAE;MACzB,MAAMY,MAAM,GAAG,EAAE;MACjB,KAAK,MAAMmB,IAAI,IAAItB,GAAG,CAACuB,UAAU,EAAE;QAC/B;QACA;QACA;QACA;QACA,MAAMC,EAAE,GAAGrC,eAAe,CAACoB,eAAe,CAACe,IAAI,CAAC;QAChD;QACA,KAAK,MAAMG,IAAI,IAAID,EAAE,CAACE,IAAI,CAAC,CAAC,EAAE;UAC1B,IAAI,CAACpC,gBAAgB,CAACL,GAAG,CAACwC,IAAI,EAAE,CAAC,IAAI,CAACnC,gBAAgB,CAACJ,GAAG,CAACuC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/E;QACAtB,MAAM,CAACG,IAAI,CAAC;UAAEgB,IAAI;UAAEE;QAAG,CAAC,CAAC;MAC7B;MACA,IAAI,CAACnC,UAAU,IAAIc,MAAM,CAACc,MAAM;MAChC,IAAI,CAAC1B,SAAS,CAACN,GAAG,CAACe,GAAG,CAACD,GAAG,EAAE;QAAEI;MAAO,CAAC,CAAC;IAC3C;IACA,OAAO,IAAI;EACf;EACAkB,cAAcA,CAACtB,GAAG,EAAE;IAChB,MAAMC,GAAG,GAAG,IAAI,CAACT,SAAS,CAACL,GAAG,CAACa,GAAG,CAAC;IACnC,IAAI,CAACC,GAAG,EAAE;MACN;IACJ;IACA,IAAI,CAACT,SAAS,CAACoC,MAAM,CAAC5B,GAAG,CAAC;IAC1B,IAAI,CAACV,UAAU,IAAIW,GAAG,CAACG,MAAM,CAACc,MAAM;IACpC;IACA,KAAK,MAAMf,KAAK,IAAIF,GAAG,CAACG,MAAM,EAAE;MAC5B,KAAK,MAAMsB,IAAI,IAAIvB,KAAK,CAACsB,EAAE,CAACE,IAAI,CAAC,CAAC,EAAE;QAChC,MAAME,kBAAkB,GAAG,IAAI,CAACtC,gBAAgB,CAACJ,GAAG,CAACuC,IAAI,CAAC;QAC1D,IAAI,OAAOG,kBAAkB,KAAK,QAAQ,EAAE;UACxC,MAAMC,cAAc,GAAGD,kBAAkB,GAAG,CAAC;UAC7C,IAAIC,cAAc,IAAI,CAAC,EAAE;YACrB,IAAI,CAACvC,gBAAgB,CAACqC,MAAM,CAACF,IAAI,CAAC;UACtC,CAAC,MACI;YACD,IAAI,CAACnC,gBAAgB,CAACL,GAAG,CAACwC,IAAI,EAAEI,cAAc,CAAC;UACnD;QACJ;MACJ;IACJ;EACJ;EACAxB,sBAAsBA,CAACH,KAAK,EAAE4B,cAAc,EAAEjC,QAAQ,EAAE;IACpD;IACA;IACA;IACA;IACA,IAAIkC,GAAG,GAAG,CAAC;IACX,KAAK,MAAM,CAACN,IAAI,EAAEO,SAAS,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACJ,cAAc,CAAC,EAAE;MAC5D,MAAMK,OAAO,GAAGjC,KAAK,CAACsB,EAAE,CAACtC,GAAG,CAACuC,IAAI,CAAC;MAClC,IAAI,CAACU,OAAO,EAAE;QACV;QACA;MACJ;MACA,IAAIC,QAAQ,GAAGvC,QAAQ,CAACX,GAAG,CAACuC,IAAI,CAAC;MACjC,IAAI,OAAOW,QAAQ,KAAK,QAAQ,EAAE;QAC9BA,QAAQ,GAAG,IAAI,CAACC,UAAU,CAACZ,IAAI,CAAC;QAChC5B,QAAQ,CAACZ,GAAG,CAACwC,IAAI,EAAEW,QAAQ,CAAC;MAChC;MACA,MAAME,UAAU,GAAGH,OAAO,GAAGC,QAAQ;MACrCL,GAAG,IAAIO,UAAU,GAAGN,SAAS;IACjC;IACA,OAAOD,GAAG;EACd;EACAnC,gBAAgBA,CAACY,KAAK,EAAE;IACpB,MAAMgB,EAAE,GAAGrC,eAAe,CAACoB,eAAe,CAACC,KAAK,CAAC;IACjD,OAAO,IAAI,CAAC+B,YAAY,CAACf,EAAE,CAAC;EAChC;EACAa,UAAUA,CAACZ,IAAI,EAAE;IACb,MAAMnC,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAACJ,GAAG,CAACuC,IAAI,CAAC,IAAI,CAAC;IAC7D,OAAOnC,gBAAgB,GAAG,CAAC,GACrBkD,IAAI,CAACC,GAAG,CAAC,CAAC,IAAI,CAACpD,UAAU,GAAG,CAAC,IAAIC,gBAAgB,CAAC,GAClD,CAAC;EACX;EACAiD,YAAYA,CAAChC,eAAe,EAAE;IAC1B,MAAMZ,SAAS,GAAGsC,MAAM,CAACS,MAAM,CAAC,IAAI,CAAC;IACrC,KAAK,MAAM,CAAC/B,IAAI,EAAEgC,WAAW,CAAC,IAAIpC,eAAe,EAAE;MAC/C,MAAMqC,GAAG,GAAG,IAAI,CAACP,UAAU,CAAC1B,IAAI,CAAC;MACjC,IAAIiC,GAAG,GAAG,CAAC,EAAE;QACTjD,SAAS,CAACgB,IAAI,CAAC,GAAGgC,WAAW,GAAGC,GAAG;MACvC;IACJ;IACA,OAAOjD,SAAS;EACpB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASkD,oBAAoBA,CAAC/C,MAAM,EAAE;EACzC;EACA,MAAMgD,MAAM,GAAGhD,MAAM,CAACiD,KAAK,CAAC,CAAC,CAAC;EAC9B;EACAD,MAAM,CAACE,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAAC9C,KAAK,GAAG6C,CAAC,CAAC7C,KAAK,CAAC;EACxC;EACA,MAAM+C,GAAG,GAAGL,MAAM,CAAC,CAAC,CAAC,EAAE1C,KAAK,IAAI,CAAC;EACjC,IAAI+C,GAAG,GAAG,CAAC,EAAE;IACT,KAAK,MAAM/C,KAAK,IAAI0C,MAAM,EAAE;MACxB1C,KAAK,CAACA,KAAK,IAAI+C,GAAG;IACtB;EACJ;EACA,OAAOL,MAAM;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}