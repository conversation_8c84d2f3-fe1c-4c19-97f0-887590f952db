{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nlet globalObservableLogger;\nexport function setLogger(logger) {\n  globalObservableLogger = logger;\n}\nexport function getLogger() {\n  return globalObservableLogger;\n}\nexport class ConsoleObservableLogger {\n  constructor() {\n    this.indentation = 0;\n    this.changedObservablesSets = new WeakMap();\n  }\n  textToConsoleArgs(text) {\n    return consoleTextToArgs([normalText(repeat('|  ', this.indentation)), text]);\n  }\n  formatInfo(info) {\n    if (!info.hadValue) {\n      return [normalText(` `), styled(formatValue(info.newValue, 60), {\n        color: 'green'\n      }), normalText(` (initial)`)];\n    }\n    return info.didChange ? [normalText(` `), styled(formatValue(info.oldValue, 70), {\n      color: 'red',\n      strikeThrough: true\n    }), normalText(` `), styled(formatValue(info.newValue, 60), {\n      color: 'green'\n    })] : [normalText(` (unchanged)`)];\n  }\n  handleObservableChanged(observable, info) {\n    console.log(...this.textToConsoleArgs([formatKind('observable value changed'), styled(observable.debugName, {\n      color: 'BlueViolet'\n    }), ...this.formatInfo(info)]));\n  }\n  formatChanges(changes) {\n    if (changes.size === 0) {\n      return undefined;\n    }\n    return styled(' (changed deps: ' + [...changes].map(o => o.debugName).join(', ') + ')', {\n      color: 'gray'\n    });\n  }\n  handleDerivedCreated(derived) {\n    const existingHandleChange = derived.handleChange;\n    this.changedObservablesSets.set(derived, new Set());\n    derived.handleChange = (observable, change) => {\n      this.changedObservablesSets.get(derived).add(observable);\n      return existingHandleChange.apply(derived, [observable, change]);\n    };\n  }\n  handleDerivedRecomputed(derived, info) {\n    const changedObservables = this.changedObservablesSets.get(derived);\n    console.log(...this.textToConsoleArgs([formatKind('derived recomputed'), styled(derived.debugName, {\n      color: 'BlueViolet'\n    }), ...this.formatInfo(info), this.formatChanges(changedObservables), {\n      data: [{\n        fn: derived._debugNameData.referenceFn ?? derived._computeFn\n      }]\n    }]));\n    changedObservables.clear();\n  }\n  handleFromEventObservableTriggered(observable, info) {\n    console.log(...this.textToConsoleArgs([formatKind('observable from event triggered'), styled(observable.debugName, {\n      color: 'BlueViolet'\n    }), ...this.formatInfo(info), {\n      data: [{\n        fn: observable._getValue\n      }]\n    }]));\n  }\n  handleAutorunCreated(autorun) {\n    const existingHandleChange = autorun.handleChange;\n    this.changedObservablesSets.set(autorun, new Set());\n    autorun.handleChange = (observable, change) => {\n      this.changedObservablesSets.get(autorun).add(observable);\n      return existingHandleChange.apply(autorun, [observable, change]);\n    };\n  }\n  handleAutorunTriggered(autorun) {\n    const changedObservables = this.changedObservablesSets.get(autorun);\n    console.log(...this.textToConsoleArgs([formatKind('autorun'), styled(autorun.debugName, {\n      color: 'BlueViolet'\n    }), this.formatChanges(changedObservables), {\n      data: [{\n        fn: autorun._debugNameData.referenceFn ?? autorun._runFn\n      }]\n    }]));\n    changedObservables.clear();\n    this.indentation++;\n  }\n  handleAutorunFinished(autorun) {\n    this.indentation--;\n  }\n  handleBeginTransaction(transaction) {\n    let transactionName = transaction.getDebugName();\n    if (transactionName === undefined) {\n      transactionName = '';\n    }\n    console.log(...this.textToConsoleArgs([formatKind('transaction'), styled(transactionName, {\n      color: 'BlueViolet'\n    }), {\n      data: [{\n        fn: transaction._fn\n      }]\n    }]));\n    this.indentation++;\n  }\n  handleEndTransaction() {\n    this.indentation--;\n  }\n}\nfunction consoleTextToArgs(text) {\n  const styles = new Array();\n  const data = [];\n  let firstArg = '';\n  function process(t) {\n    if ('length' in t) {\n      for (const item of t) {\n        if (item) {\n          process(item);\n        }\n      }\n    } else if ('text' in t) {\n      firstArg += `%c${t.text}`;\n      styles.push(t.style);\n      if (t.data) {\n        data.push(...t.data);\n      }\n    } else if ('data' in t) {\n      data.push(...t.data);\n    }\n  }\n  process(text);\n  const result = [firstArg, ...styles];\n  result.push(...data);\n  return result;\n}\nfunction normalText(text) {\n  return styled(text, {\n    color: 'black'\n  });\n}\nfunction formatKind(kind) {\n  return styled(padStr(`${kind}: `, 10), {\n    color: 'black',\n    bold: true\n  });\n}\nfunction styled(text, options = {\n  color: 'black'\n}) {\n  function objToCss(styleObj) {\n    return Object.entries(styleObj).reduce((styleString, [propName, propValue]) => {\n      return `${styleString}${propName}:${propValue};`;\n    }, '');\n  }\n  const style = {\n    color: options.color\n  };\n  if (options.strikeThrough) {\n    style['text-decoration'] = 'line-through';\n  }\n  if (options.bold) {\n    style['font-weight'] = 'bold';\n  }\n  return {\n    text,\n    style: objToCss(style)\n  };\n}\nfunction formatValue(value, availableLen) {\n  switch (typeof value) {\n    case 'number':\n      return '' + value;\n    case 'string':\n      if (value.length + 2 <= availableLen) {\n        return `\"${value}\"`;\n      }\n      return `\"${value.substr(0, availableLen - 7)}\"+...`;\n    case 'boolean':\n      return value ? 'true' : 'false';\n    case 'undefined':\n      return 'undefined';\n    case 'object':\n      if (value === null) {\n        return 'null';\n      }\n      if (Array.isArray(value)) {\n        return formatArray(value, availableLen);\n      }\n      return formatObject(value, availableLen);\n    case 'symbol':\n      return value.toString();\n    case 'function':\n      return `[[Function${value.name ? ' ' + value.name : ''}]]`;\n    default:\n      return '' + value;\n  }\n}\nfunction formatArray(value, availableLen) {\n  let result = '[ ';\n  let first = true;\n  for (const val of value) {\n    if (!first) {\n      result += ', ';\n    }\n    if (result.length - 5 > availableLen) {\n      result += '...';\n      break;\n    }\n    first = false;\n    result += `${formatValue(val, availableLen - result.length)}`;\n  }\n  result += ' ]';\n  return result;\n}\nfunction formatObject(value, availableLen) {\n  let result = '{ ';\n  let first = true;\n  for (const [key, val] of Object.entries(value)) {\n    if (!first) {\n      result += ', ';\n    }\n    if (result.length - 5 > availableLen) {\n      result += '...';\n      break;\n    }\n    first = false;\n    result += `${key}: ${formatValue(val, availableLen - result.length)}`;\n  }\n  result += ' }';\n  return result;\n}\nfunction repeat(str, count) {\n  let result = '';\n  for (let i = 1; i <= count; i++) {\n    result += str;\n  }\n  return result;\n}\nfunction padStr(str, length) {\n  while (str.length < length) {\n    str += ' ';\n  }\n  return str;\n}", "map": {"version": 3, "names": ["globalObservableLogger", "<PERSON><PERSON><PERSON><PERSON>", "logger", "<PERSON><PERSON><PERSON><PERSON>", "ConsoleObservableLogger", "constructor", "indentation", "changedObservablesSets", "WeakMap", "textToConsoleArgs", "text", "consoleTextToArgs", "normalText", "repeat", "formatInfo", "info", "hadValue", "styled", "formatValue", "newValue", "color", "<PERSON><PERSON><PERSON><PERSON>", "oldValue", "strikeThrough", "handleObservableChanged", "observable", "console", "log", "formatKind", "debugName", "formatChanges", "changes", "size", "undefined", "map", "o", "join", "handleDerivedCreated", "derived", "existingHandleChange", "handleChange", "set", "Set", "change", "get", "add", "apply", "handleDerivedRecomputed", "changedObservables", "data", "fn", "_debugNameData", "referenceFn", "_computeFn", "clear", "handleFromEventObservableTriggered", "_getValue", "handleAutorunCreated", "autorun", "handleAutorunTriggered", "_runFn", "handleAutorunFinished", "handleBeginTransaction", "transaction", "transactionName", "getDebugName", "_fn", "handleEndTransaction", "styles", "Array", "firstArg", "process", "t", "item", "push", "style", "result", "kind", "padStr", "bold", "options", "objToCss", "styleObj", "Object", "entries", "reduce", "styleString", "propName", "propValue", "value", "availableLen", "length", "substr", "isArray", "formatArray", "formatObject", "toString", "name", "first", "val", "key", "str", "count", "i"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/base/common/observableInternal/logging.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nlet globalObservableLogger;\nexport function setLogger(logger) {\n    globalObservableLogger = logger;\n}\nexport function getLogger() {\n    return globalObservableLogger;\n}\nexport class ConsoleObservableLogger {\n    constructor() {\n        this.indentation = 0;\n        this.changedObservablesSets = new WeakMap();\n    }\n    textToConsoleArgs(text) {\n        return consoleTextToArgs([\n            normalText(repeat('|  ', this.indentation)),\n            text,\n        ]);\n    }\n    formatInfo(info) {\n        if (!info.hadValue) {\n            return [\n                normalText(` `),\n                styled(formatValue(info.newValue, 60), {\n                    color: 'green',\n                }),\n                normalText(` (initial)`),\n            ];\n        }\n        return info.didChange\n            ? [\n                normalText(` `),\n                styled(formatValue(info.oldValue, 70), {\n                    color: 'red',\n                    strikeThrough: true,\n                }),\n                normalText(` `),\n                styled(formatValue(info.newValue, 60), {\n                    color: 'green',\n                }),\n            ]\n            : [normalText(` (unchanged)`)];\n    }\n    handleObservableChanged(observable, info) {\n        console.log(...this.textToConsoleArgs([\n            formatKind('observable value changed'),\n            styled(observable.debugName, { color: 'BlueViolet' }),\n            ...this.formatInfo(info),\n        ]));\n    }\n    formatChanges(changes) {\n        if (changes.size === 0) {\n            return undefined;\n        }\n        return styled(' (changed deps: ' +\n            [...changes].map((o) => o.debugName).join(', ') +\n            ')', { color: 'gray' });\n    }\n    handleDerivedCreated(derived) {\n        const existingHandleChange = derived.handleChange;\n        this.changedObservablesSets.set(derived, new Set());\n        derived.handleChange = (observable, change) => {\n            this.changedObservablesSets.get(derived).add(observable);\n            return existingHandleChange.apply(derived, [observable, change]);\n        };\n    }\n    handleDerivedRecomputed(derived, info) {\n        const changedObservables = this.changedObservablesSets.get(derived);\n        console.log(...this.textToConsoleArgs([\n            formatKind('derived recomputed'),\n            styled(derived.debugName, { color: 'BlueViolet' }),\n            ...this.formatInfo(info),\n            this.formatChanges(changedObservables),\n            { data: [{ fn: derived._debugNameData.referenceFn ?? derived._computeFn }] }\n        ]));\n        changedObservables.clear();\n    }\n    handleFromEventObservableTriggered(observable, info) {\n        console.log(...this.textToConsoleArgs([\n            formatKind('observable from event triggered'),\n            styled(observable.debugName, { color: 'BlueViolet' }),\n            ...this.formatInfo(info),\n            { data: [{ fn: observable._getValue }] }\n        ]));\n    }\n    handleAutorunCreated(autorun) {\n        const existingHandleChange = autorun.handleChange;\n        this.changedObservablesSets.set(autorun, new Set());\n        autorun.handleChange = (observable, change) => {\n            this.changedObservablesSets.get(autorun).add(observable);\n            return existingHandleChange.apply(autorun, [observable, change]);\n        };\n    }\n    handleAutorunTriggered(autorun) {\n        const changedObservables = this.changedObservablesSets.get(autorun);\n        console.log(...this.textToConsoleArgs([\n            formatKind('autorun'),\n            styled(autorun.debugName, { color: 'BlueViolet' }),\n            this.formatChanges(changedObservables),\n            { data: [{ fn: autorun._debugNameData.referenceFn ?? autorun._runFn }] }\n        ]));\n        changedObservables.clear();\n        this.indentation++;\n    }\n    handleAutorunFinished(autorun) {\n        this.indentation--;\n    }\n    handleBeginTransaction(transaction) {\n        let transactionName = transaction.getDebugName();\n        if (transactionName === undefined) {\n            transactionName = '';\n        }\n        console.log(...this.textToConsoleArgs([\n            formatKind('transaction'),\n            styled(transactionName, { color: 'BlueViolet' }),\n            { data: [{ fn: transaction._fn }] }\n        ]));\n        this.indentation++;\n    }\n    handleEndTransaction() {\n        this.indentation--;\n    }\n}\nfunction consoleTextToArgs(text) {\n    const styles = new Array();\n    const data = [];\n    let firstArg = '';\n    function process(t) {\n        if ('length' in t) {\n            for (const item of t) {\n                if (item) {\n                    process(item);\n                }\n            }\n        }\n        else if ('text' in t) {\n            firstArg += `%c${t.text}`;\n            styles.push(t.style);\n            if (t.data) {\n                data.push(...t.data);\n            }\n        }\n        else if ('data' in t) {\n            data.push(...t.data);\n        }\n    }\n    process(text);\n    const result = [firstArg, ...styles];\n    result.push(...data);\n    return result;\n}\nfunction normalText(text) {\n    return styled(text, { color: 'black' });\n}\nfunction formatKind(kind) {\n    return styled(padStr(`${kind}: `, 10), { color: 'black', bold: true });\n}\nfunction styled(text, options = {\n    color: 'black',\n}) {\n    function objToCss(styleObj) {\n        return Object.entries(styleObj).reduce((styleString, [propName, propValue]) => {\n            return `${styleString}${propName}:${propValue};`;\n        }, '');\n    }\n    const style = {\n        color: options.color,\n    };\n    if (options.strikeThrough) {\n        style['text-decoration'] = 'line-through';\n    }\n    if (options.bold) {\n        style['font-weight'] = 'bold';\n    }\n    return {\n        text,\n        style: objToCss(style),\n    };\n}\nfunction formatValue(value, availableLen) {\n    switch (typeof value) {\n        case 'number':\n            return '' + value;\n        case 'string':\n            if (value.length + 2 <= availableLen) {\n                return `\"${value}\"`;\n            }\n            return `\"${value.substr(0, availableLen - 7)}\"+...`;\n        case 'boolean':\n            return value ? 'true' : 'false';\n        case 'undefined':\n            return 'undefined';\n        case 'object':\n            if (value === null) {\n                return 'null';\n            }\n            if (Array.isArray(value)) {\n                return formatArray(value, availableLen);\n            }\n            return formatObject(value, availableLen);\n        case 'symbol':\n            return value.toString();\n        case 'function':\n            return `[[Function${value.name ? ' ' + value.name : ''}]]`;\n        default:\n            return '' + value;\n    }\n}\nfunction formatArray(value, availableLen) {\n    let result = '[ ';\n    let first = true;\n    for (const val of value) {\n        if (!first) {\n            result += ', ';\n        }\n        if (result.length - 5 > availableLen) {\n            result += '...';\n            break;\n        }\n        first = false;\n        result += `${formatValue(val, availableLen - result.length)}`;\n    }\n    result += ' ]';\n    return result;\n}\nfunction formatObject(value, availableLen) {\n    let result = '{ ';\n    let first = true;\n    for (const [key, val] of Object.entries(value)) {\n        if (!first) {\n            result += ', ';\n        }\n        if (result.length - 5 > availableLen) {\n            result += '...';\n            break;\n        }\n        first = false;\n        result += `${key}: ${formatValue(val, availableLen - result.length)}`;\n    }\n    result += ' }';\n    return result;\n}\nfunction repeat(str, count) {\n    let result = '';\n    for (let i = 1; i <= count; i++) {\n        result += str;\n    }\n    return result;\n}\nfunction padStr(str, length) {\n    while (str.length < length) {\n        str += ' ';\n    }\n    return str;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,IAAIA,sBAAsB;AAC1B,OAAO,SAASC,SAASA,CAACC,MAAM,EAAE;EAC9BF,sBAAsB,GAAGE,MAAM;AACnC;AACA,OAAO,SAASC,SAASA,CAAA,EAAG;EACxB,OAAOH,sBAAsB;AACjC;AACA,OAAO,MAAMI,uBAAuB,CAAC;EACjCC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,WAAW,GAAG,CAAC;IACpB,IAAI,CAACC,sBAAsB,GAAG,IAAIC,OAAO,CAAC,CAAC;EAC/C;EACAC,iBAAiBA,CAACC,IAAI,EAAE;IACpB,OAAOC,iBAAiB,CAAC,CACrBC,UAAU,CAACC,MAAM,CAAC,KAAK,EAAE,IAAI,CAACP,WAAW,CAAC,CAAC,EAC3CI,IAAI,CACP,CAAC;EACN;EACAI,UAAUA,CAACC,IAAI,EAAE;IACb,IAAI,CAACA,IAAI,CAACC,QAAQ,EAAE;MAChB,OAAO,CACHJ,UAAU,CAAC,GAAG,CAAC,EACfK,MAAM,CAACC,WAAW,CAACH,IAAI,CAACI,QAAQ,EAAE,EAAE,CAAC,EAAE;QACnCC,KAAK,EAAE;MACX,CAAC,CAAC,EACFR,UAAU,CAAC,YAAY,CAAC,CAC3B;IACL;IACA,OAAOG,IAAI,CAACM,SAAS,GACf,CACET,UAAU,CAAC,GAAG,CAAC,EACfK,MAAM,CAACC,WAAW,CAACH,IAAI,CAACO,QAAQ,EAAE,EAAE,CAAC,EAAE;MACnCF,KAAK,EAAE,KAAK;MACZG,aAAa,EAAE;IACnB,CAAC,CAAC,EACFX,UAAU,CAAC,GAAG,CAAC,EACfK,MAAM,CAACC,WAAW,CAACH,IAAI,CAACI,QAAQ,EAAE,EAAE,CAAC,EAAE;MACnCC,KAAK,EAAE;IACX,CAAC,CAAC,CACL,GACC,CAACR,UAAU,CAAC,cAAc,CAAC,CAAC;EACtC;EACAY,uBAAuBA,CAACC,UAAU,EAAEV,IAAI,EAAE;IACtCW,OAAO,CAACC,GAAG,CAAC,GAAG,IAAI,CAAClB,iBAAiB,CAAC,CAClCmB,UAAU,CAAC,0BAA0B,CAAC,EACtCX,MAAM,CAACQ,UAAU,CAACI,SAAS,EAAE;MAAET,KAAK,EAAE;IAAa,CAAC,CAAC,EACrD,GAAG,IAAI,CAACN,UAAU,CAACC,IAAI,CAAC,CAC3B,CAAC,CAAC;EACP;EACAe,aAAaA,CAACC,OAAO,EAAE;IACnB,IAAIA,OAAO,CAACC,IAAI,KAAK,CAAC,EAAE;MACpB,OAAOC,SAAS;IACpB;IACA,OAAOhB,MAAM,CAAC,kBAAkB,GAC5B,CAAC,GAAGc,OAAO,CAAC,CAACG,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACN,SAAS,CAAC,CAACO,IAAI,CAAC,IAAI,CAAC,GAC/C,GAAG,EAAE;MAAEhB,KAAK,EAAE;IAAO,CAAC,CAAC;EAC/B;EACAiB,oBAAoBA,CAACC,OAAO,EAAE;IAC1B,MAAMC,oBAAoB,GAAGD,OAAO,CAACE,YAAY;IACjD,IAAI,CAACjC,sBAAsB,CAACkC,GAAG,CAACH,OAAO,EAAE,IAAII,GAAG,CAAC,CAAC,CAAC;IACnDJ,OAAO,CAACE,YAAY,GAAG,CAACf,UAAU,EAAEkB,MAAM,KAAK;MAC3C,IAAI,CAACpC,sBAAsB,CAACqC,GAAG,CAACN,OAAO,CAAC,CAACO,GAAG,CAACpB,UAAU,CAAC;MACxD,OAAOc,oBAAoB,CAACO,KAAK,CAACR,OAAO,EAAE,CAACb,UAAU,EAAEkB,MAAM,CAAC,CAAC;IACpE,CAAC;EACL;EACAI,uBAAuBA,CAACT,OAAO,EAAEvB,IAAI,EAAE;IACnC,MAAMiC,kBAAkB,GAAG,IAAI,CAACzC,sBAAsB,CAACqC,GAAG,CAACN,OAAO,CAAC;IACnEZ,OAAO,CAACC,GAAG,CAAC,GAAG,IAAI,CAAClB,iBAAiB,CAAC,CAClCmB,UAAU,CAAC,oBAAoB,CAAC,EAChCX,MAAM,CAACqB,OAAO,CAACT,SAAS,EAAE;MAAET,KAAK,EAAE;IAAa,CAAC,CAAC,EAClD,GAAG,IAAI,CAACN,UAAU,CAACC,IAAI,CAAC,EACxB,IAAI,CAACe,aAAa,CAACkB,kBAAkB,CAAC,EACtC;MAAEC,IAAI,EAAE,CAAC;QAAEC,EAAE,EAAEZ,OAAO,CAACa,cAAc,CAACC,WAAW,IAAId,OAAO,CAACe;MAAW,CAAC;IAAE,CAAC,CAC/E,CAAC,CAAC;IACHL,kBAAkB,CAACM,KAAK,CAAC,CAAC;EAC9B;EACAC,kCAAkCA,CAAC9B,UAAU,EAAEV,IAAI,EAAE;IACjDW,OAAO,CAACC,GAAG,CAAC,GAAG,IAAI,CAAClB,iBAAiB,CAAC,CAClCmB,UAAU,CAAC,iCAAiC,CAAC,EAC7CX,MAAM,CAACQ,UAAU,CAACI,SAAS,EAAE;MAAET,KAAK,EAAE;IAAa,CAAC,CAAC,EACrD,GAAG,IAAI,CAACN,UAAU,CAACC,IAAI,CAAC,EACxB;MAAEkC,IAAI,EAAE,CAAC;QAAEC,EAAE,EAAEzB,UAAU,CAAC+B;MAAU,CAAC;IAAE,CAAC,CAC3C,CAAC,CAAC;EACP;EACAC,oBAAoBA,CAACC,OAAO,EAAE;IAC1B,MAAMnB,oBAAoB,GAAGmB,OAAO,CAAClB,YAAY;IACjD,IAAI,CAACjC,sBAAsB,CAACkC,GAAG,CAACiB,OAAO,EAAE,IAAIhB,GAAG,CAAC,CAAC,CAAC;IACnDgB,OAAO,CAAClB,YAAY,GAAG,CAACf,UAAU,EAAEkB,MAAM,KAAK;MAC3C,IAAI,CAACpC,sBAAsB,CAACqC,GAAG,CAACc,OAAO,CAAC,CAACb,GAAG,CAACpB,UAAU,CAAC;MACxD,OAAOc,oBAAoB,CAACO,KAAK,CAACY,OAAO,EAAE,CAACjC,UAAU,EAAEkB,MAAM,CAAC,CAAC;IACpE,CAAC;EACL;EACAgB,sBAAsBA,CAACD,OAAO,EAAE;IAC5B,MAAMV,kBAAkB,GAAG,IAAI,CAACzC,sBAAsB,CAACqC,GAAG,CAACc,OAAO,CAAC;IACnEhC,OAAO,CAACC,GAAG,CAAC,GAAG,IAAI,CAAClB,iBAAiB,CAAC,CAClCmB,UAAU,CAAC,SAAS,CAAC,EACrBX,MAAM,CAACyC,OAAO,CAAC7B,SAAS,EAAE;MAAET,KAAK,EAAE;IAAa,CAAC,CAAC,EAClD,IAAI,CAACU,aAAa,CAACkB,kBAAkB,CAAC,EACtC;MAAEC,IAAI,EAAE,CAAC;QAAEC,EAAE,EAAEQ,OAAO,CAACP,cAAc,CAACC,WAAW,IAAIM,OAAO,CAACE;MAAO,CAAC;IAAE,CAAC,CAC3E,CAAC,CAAC;IACHZ,kBAAkB,CAACM,KAAK,CAAC,CAAC;IAC1B,IAAI,CAAChD,WAAW,EAAE;EACtB;EACAuD,qBAAqBA,CAACH,OAAO,EAAE;IAC3B,IAAI,CAACpD,WAAW,EAAE;EACtB;EACAwD,sBAAsBA,CAACC,WAAW,EAAE;IAChC,IAAIC,eAAe,GAAGD,WAAW,CAACE,YAAY,CAAC,CAAC;IAChD,IAAID,eAAe,KAAK/B,SAAS,EAAE;MAC/B+B,eAAe,GAAG,EAAE;IACxB;IACAtC,OAAO,CAACC,GAAG,CAAC,GAAG,IAAI,CAAClB,iBAAiB,CAAC,CAClCmB,UAAU,CAAC,aAAa,CAAC,EACzBX,MAAM,CAAC+C,eAAe,EAAE;MAAE5C,KAAK,EAAE;IAAa,CAAC,CAAC,EAChD;MAAE6B,IAAI,EAAE,CAAC;QAAEC,EAAE,EAAEa,WAAW,CAACG;MAAI,CAAC;IAAE,CAAC,CACtC,CAAC,CAAC;IACH,IAAI,CAAC5D,WAAW,EAAE;EACtB;EACA6D,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAAC7D,WAAW,EAAE;EACtB;AACJ;AACA,SAASK,iBAAiBA,CAACD,IAAI,EAAE;EAC7B,MAAM0D,MAAM,GAAG,IAAIC,KAAK,CAAC,CAAC;EAC1B,MAAMpB,IAAI,GAAG,EAAE;EACf,IAAIqB,QAAQ,GAAG,EAAE;EACjB,SAASC,OAAOA,CAACC,CAAC,EAAE;IAChB,IAAI,QAAQ,IAAIA,CAAC,EAAE;MACf,KAAK,MAAMC,IAAI,IAAID,CAAC,EAAE;QAClB,IAAIC,IAAI,EAAE;UACNF,OAAO,CAACE,IAAI,CAAC;QACjB;MACJ;IACJ,CAAC,MACI,IAAI,MAAM,IAAID,CAAC,EAAE;MAClBF,QAAQ,IAAI,KAAKE,CAAC,CAAC9D,IAAI,EAAE;MACzB0D,MAAM,CAACM,IAAI,CAACF,CAAC,CAACG,KAAK,CAAC;MACpB,IAAIH,CAAC,CAACvB,IAAI,EAAE;QACRA,IAAI,CAACyB,IAAI,CAAC,GAAGF,CAAC,CAACvB,IAAI,CAAC;MACxB;IACJ,CAAC,MACI,IAAI,MAAM,IAAIuB,CAAC,EAAE;MAClBvB,IAAI,CAACyB,IAAI,CAAC,GAAGF,CAAC,CAACvB,IAAI,CAAC;IACxB;EACJ;EACAsB,OAAO,CAAC7D,IAAI,CAAC;EACb,MAAMkE,MAAM,GAAG,CAACN,QAAQ,EAAE,GAAGF,MAAM,CAAC;EACpCQ,MAAM,CAACF,IAAI,CAAC,GAAGzB,IAAI,CAAC;EACpB,OAAO2B,MAAM;AACjB;AACA,SAAShE,UAAUA,CAACF,IAAI,EAAE;EACtB,OAAOO,MAAM,CAACP,IAAI,EAAE;IAAEU,KAAK,EAAE;EAAQ,CAAC,CAAC;AAC3C;AACA,SAASQ,UAAUA,CAACiD,IAAI,EAAE;EACtB,OAAO5D,MAAM,CAAC6D,MAAM,CAAC,GAAGD,IAAI,IAAI,EAAE,EAAE,CAAC,EAAE;IAAEzD,KAAK,EAAE,OAAO;IAAE2D,IAAI,EAAE;EAAK,CAAC,CAAC;AAC1E;AACA,SAAS9D,MAAMA,CAACP,IAAI,EAAEsE,OAAO,GAAG;EAC5B5D,KAAK,EAAE;AACX,CAAC,EAAE;EACC,SAAS6D,QAAQA,CAACC,QAAQ,EAAE;IACxB,OAAOC,MAAM,CAACC,OAAO,CAACF,QAAQ,CAAC,CAACG,MAAM,CAAC,CAACC,WAAW,EAAE,CAACC,QAAQ,EAAEC,SAAS,CAAC,KAAK;MAC3E,OAAO,GAAGF,WAAW,GAAGC,QAAQ,IAAIC,SAAS,GAAG;IACpD,CAAC,EAAE,EAAE,CAAC;EACV;EACA,MAAMb,KAAK,GAAG;IACVvD,KAAK,EAAE4D,OAAO,CAAC5D;EACnB,CAAC;EACD,IAAI4D,OAAO,CAACzD,aAAa,EAAE;IACvBoD,KAAK,CAAC,iBAAiB,CAAC,GAAG,cAAc;EAC7C;EACA,IAAIK,OAAO,CAACD,IAAI,EAAE;IACdJ,KAAK,CAAC,aAAa,CAAC,GAAG,MAAM;EACjC;EACA,OAAO;IACHjE,IAAI;IACJiE,KAAK,EAAEM,QAAQ,CAACN,KAAK;EACzB,CAAC;AACL;AACA,SAASzD,WAAWA,CAACuE,KAAK,EAAEC,YAAY,EAAE;EACtC,QAAQ,OAAOD,KAAK;IAChB,KAAK,QAAQ;MACT,OAAO,EAAE,GAAGA,KAAK;IACrB,KAAK,QAAQ;MACT,IAAIA,KAAK,CAACE,MAAM,GAAG,CAAC,IAAID,YAAY,EAAE;QAClC,OAAO,IAAID,KAAK,GAAG;MACvB;MACA,OAAO,IAAIA,KAAK,CAACG,MAAM,CAAC,CAAC,EAAEF,YAAY,GAAG,CAAC,CAAC,OAAO;IACvD,KAAK,SAAS;MACV,OAAOD,KAAK,GAAG,MAAM,GAAG,OAAO;IACnC,KAAK,WAAW;MACZ,OAAO,WAAW;IACtB,KAAK,QAAQ;MACT,IAAIA,KAAK,KAAK,IAAI,EAAE;QAChB,OAAO,MAAM;MACjB;MACA,IAAIpB,KAAK,CAACwB,OAAO,CAACJ,KAAK,CAAC,EAAE;QACtB,OAAOK,WAAW,CAACL,KAAK,EAAEC,YAAY,CAAC;MAC3C;MACA,OAAOK,YAAY,CAACN,KAAK,EAAEC,YAAY,CAAC;IAC5C,KAAK,QAAQ;MACT,OAAOD,KAAK,CAACO,QAAQ,CAAC,CAAC;IAC3B,KAAK,UAAU;MACX,OAAO,aAAaP,KAAK,CAACQ,IAAI,GAAG,GAAG,GAAGR,KAAK,CAACQ,IAAI,GAAG,EAAE,IAAI;IAC9D;MACI,OAAO,EAAE,GAAGR,KAAK;EACzB;AACJ;AACA,SAASK,WAAWA,CAACL,KAAK,EAAEC,YAAY,EAAE;EACtC,IAAId,MAAM,GAAG,IAAI;EACjB,IAAIsB,KAAK,GAAG,IAAI;EAChB,KAAK,MAAMC,GAAG,IAAIV,KAAK,EAAE;IACrB,IAAI,CAACS,KAAK,EAAE;MACRtB,MAAM,IAAI,IAAI;IAClB;IACA,IAAIA,MAAM,CAACe,MAAM,GAAG,CAAC,GAAGD,YAAY,EAAE;MAClCd,MAAM,IAAI,KAAK;MACf;IACJ;IACAsB,KAAK,GAAG,KAAK;IACbtB,MAAM,IAAI,GAAG1D,WAAW,CAACiF,GAAG,EAAET,YAAY,GAAGd,MAAM,CAACe,MAAM,CAAC,EAAE;EACjE;EACAf,MAAM,IAAI,IAAI;EACd,OAAOA,MAAM;AACjB;AACA,SAASmB,YAAYA,CAACN,KAAK,EAAEC,YAAY,EAAE;EACvC,IAAId,MAAM,GAAG,IAAI;EACjB,IAAIsB,KAAK,GAAG,IAAI;EAChB,KAAK,MAAM,CAACE,GAAG,EAAED,GAAG,CAAC,IAAIhB,MAAM,CAACC,OAAO,CAACK,KAAK,CAAC,EAAE;IAC5C,IAAI,CAACS,KAAK,EAAE;MACRtB,MAAM,IAAI,IAAI;IAClB;IACA,IAAIA,MAAM,CAACe,MAAM,GAAG,CAAC,GAAGD,YAAY,EAAE;MAClCd,MAAM,IAAI,KAAK;MACf;IACJ;IACAsB,KAAK,GAAG,KAAK;IACbtB,MAAM,IAAI,GAAGwB,GAAG,KAAKlF,WAAW,CAACiF,GAAG,EAAET,YAAY,GAAGd,MAAM,CAACe,MAAM,CAAC,EAAE;EACzE;EACAf,MAAM,IAAI,IAAI;EACd,OAAOA,MAAM;AACjB;AACA,SAAS/D,MAAMA,CAACwF,GAAG,EAAEC,KAAK,EAAE;EACxB,IAAI1B,MAAM,GAAG,EAAE;EACf,KAAK,IAAI2B,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAID,KAAK,EAAEC,CAAC,EAAE,EAAE;IAC7B3B,MAAM,IAAIyB,GAAG;EACjB;EACA,OAAOzB,MAAM;AACjB;AACA,SAASE,MAAMA,CAACuB,GAAG,EAAEV,MAAM,EAAE;EACzB,OAAOU,GAAG,CAACV,MAAM,GAAGA,MAAM,EAAE;IACxBU,GAAG,IAAI,GAAG;EACd;EACA,OAAOA,GAAG;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}