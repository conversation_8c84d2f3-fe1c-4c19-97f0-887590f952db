{"ast": null, "code": "import _asyncToGenerator from \"C:/console/aava-ui-web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = this && this.__param || function (paramIndex, decorator) {\n  return function (target, key) {\n    decorator(target, key, paramIndex);\n  };\n};\nvar QuickPickItemElementRenderer_1;\nimport * as dom from '../../../base/browser/dom.js';\nimport { Emitter, Event, EventBufferer } from '../../../base/common/event.js';\nimport { localize } from '../../../nls.js';\nimport { IInstantiationService } from '../../instantiation/common/instantiation.js';\nimport { WorkbenchObjectTree } from '../../list/browser/listService.js';\nimport { IThemeService } from '../../theme/common/themeService.js';\nimport { Disposable, DisposableStore } from '../../../base/common/lifecycle.js';\nimport { QuickPickFocus } from '../common/quickInput.js';\nimport { StandardKeyboardEvent } from '../../../base/browser/keyboardEvent.js';\nimport { OS } from '../../../base/common/platform.js';\nimport { memoize } from '../../../base/common/decorators.js';\nimport { IconLabel } from '../../../base/browser/ui/iconLabel/iconLabel.js';\nimport { KeybindingLabel } from '../../../base/browser/ui/keybindingLabel/keybindingLabel.js';\nimport { ActionBar } from '../../../base/browser/ui/actionbar/actionbar.js';\nimport { isDark } from '../../theme/common/theme.js';\nimport { URI } from '../../../base/common/uri.js';\nimport { quickInputButtonToAction } from './quickInputUtils.js';\nimport { Lazy } from '../../../base/common/lazy.js';\nimport { getCodiconAriaLabel, matchesFuzzyIconAware, parseLabelWithIcons } from '../../../base/common/iconLabels.js';\nimport { compareAnything } from '../../../base/common/comparers.js';\nimport { ltrim } from '../../../base/common/strings.js';\nimport { RenderIndentGuides } from '../../../base/browser/ui/tree/abstractTree.js';\nimport { ThrottledDelayer } from '../../../base/common/async.js';\nimport { isCancellationError } from '../../../base/common/errors.js';\nimport { IAccessibilityService } from '../../accessibility/common/accessibility.js';\nimport { observableValue, observableValueOpts, transaction } from '../../../base/common/observable.js';\nimport { equals } from '../../../base/common/arrays.js';\nconst $ = dom.$;\nclass BaseQuickPickItemElement {\n  constructor(index, hasCheckbox, mainItem) {\n    this.index = index;\n    this.hasCheckbox = hasCheckbox;\n    this._hidden = false;\n    this._init = new Lazy(() => {\n      const saneLabel = mainItem.label ?? '';\n      const saneSortLabel = parseLabelWithIcons(saneLabel).text.trim();\n      const saneAriaLabel = mainItem.ariaLabel || [saneLabel, this.saneDescription, this.saneDetail].map(s => getCodiconAriaLabel(s)).filter(s => !!s).join(', ');\n      return {\n        saneLabel,\n        saneSortLabel,\n        saneAriaLabel\n      };\n    });\n    this._saneDescription = mainItem.description;\n    this._saneTooltip = mainItem.tooltip;\n  }\n  // #region Lazy Getters\n  get saneLabel() {\n    return this._init.value.saneLabel;\n  }\n  get saneSortLabel() {\n    return this._init.value.saneSortLabel;\n  }\n  get saneAriaLabel() {\n    return this._init.value.saneAriaLabel;\n  }\n  get element() {\n    return this._element;\n  }\n  set element(value) {\n    this._element = value;\n  }\n  get hidden() {\n    return this._hidden;\n  }\n  set hidden(value) {\n    this._hidden = value;\n  }\n  get saneDescription() {\n    return this._saneDescription;\n  }\n  set saneDescription(value) {\n    this._saneDescription = value;\n  }\n  get saneDetail() {\n    return this._saneDetail;\n  }\n  set saneDetail(value) {\n    this._saneDetail = value;\n  }\n  get saneTooltip() {\n    return this._saneTooltip;\n  }\n  set saneTooltip(value) {\n    this._saneTooltip = value;\n  }\n  get labelHighlights() {\n    return this._labelHighlights;\n  }\n  set labelHighlights(value) {\n    this._labelHighlights = value;\n  }\n  get descriptionHighlights() {\n    return this._descriptionHighlights;\n  }\n  set descriptionHighlights(value) {\n    this._descriptionHighlights = value;\n  }\n  get detailHighlights() {\n    return this._detailHighlights;\n  }\n  set detailHighlights(value) {\n    this._detailHighlights = value;\n  }\n}\nclass QuickPickItemElement extends BaseQuickPickItemElement {\n  constructor(index, hasCheckbox, fireButtonTriggered, _onChecked, item, _separator) {\n    super(index, hasCheckbox, item);\n    this.fireButtonTriggered = fireButtonTriggered;\n    this._onChecked = _onChecked;\n    this.item = item;\n    this._separator = _separator;\n    this._checked = false;\n    this.onChecked = hasCheckbox ? Event.map(Event.filter(this._onChecked.event, e => e.element === this), e => e.checked) : Event.None;\n    this._saneDetail = item.detail;\n    this._labelHighlights = item.highlights?.label;\n    this._descriptionHighlights = item.highlights?.description;\n    this._detailHighlights = item.highlights?.detail;\n  }\n  get separator() {\n    return this._separator;\n  }\n  set separator(value) {\n    this._separator = value;\n  }\n  get checked() {\n    return this._checked;\n  }\n  set checked(value) {\n    if (value !== this._checked) {\n      this._checked = value;\n      this._onChecked.fire({\n        element: this,\n        checked: value\n      });\n    }\n  }\n  get checkboxDisabled() {\n    return !!this.item.disabled;\n  }\n}\nvar QuickPickSeparatorFocusReason = /*#__PURE__*/function (QuickPickSeparatorFocusReason) {\n  /**\n   * No item is hovered or active\n   */\n  QuickPickSeparatorFocusReason[QuickPickSeparatorFocusReason[\"NONE\"] = 0] = \"NONE\";\n  /**\n   * Some item within this section is hovered\n   */\n  QuickPickSeparatorFocusReason[QuickPickSeparatorFocusReason[\"MOUSE_HOVER\"] = 1] = \"MOUSE_HOVER\";\n  /**\n   * Some item within this section is active\n   */\n  QuickPickSeparatorFocusReason[QuickPickSeparatorFocusReason[\"ACTIVE_ITEM\"] = 2] = \"ACTIVE_ITEM\";\n  return QuickPickSeparatorFocusReason;\n}(QuickPickSeparatorFocusReason || {});\nclass QuickPickSeparatorElement extends BaseQuickPickItemElement {\n  constructor(index, fireSeparatorButtonTriggered, separator) {\n    super(index, false, separator);\n    this.fireSeparatorButtonTriggered = fireSeparatorButtonTriggered;\n    this.separator = separator;\n    this.children = new Array();\n    /**\n     * If this item is >0, it means that there is some item in the list that is either:\n     * * hovered over\n     * * active\n     */\n    this.focusInsideSeparator = QuickPickSeparatorFocusReason.NONE;\n  }\n}\nclass QuickInputItemDelegate {\n  getHeight(element) {\n    if (element instanceof QuickPickSeparatorElement) {\n      return 30;\n    }\n    return element.saneDetail ? 44 : 22;\n  }\n  getTemplateId(element) {\n    if (element instanceof QuickPickItemElement) {\n      return QuickPickItemElementRenderer.ID;\n    } else {\n      return QuickPickSeparatorElementRenderer.ID;\n    }\n  }\n}\nclass QuickInputAccessibilityProvider {\n  getWidgetAriaLabel() {\n    return localize('quickInput', \"Quick Input\");\n  }\n  getAriaLabel(element) {\n    return element.separator?.label ? `${element.saneAriaLabel}, ${element.separator.label}` : element.saneAriaLabel;\n  }\n  getWidgetRole() {\n    return 'listbox';\n  }\n  getRole(element) {\n    return element.hasCheckbox ? 'checkbox' : 'option';\n  }\n  isChecked(element) {\n    if (!element.hasCheckbox || !(element instanceof QuickPickItemElement)) {\n      return undefined;\n    }\n    return {\n      get value() {\n        return element.checked;\n      },\n      onDidChange: e => element.onChecked(() => e())\n    };\n  }\n}\nclass BaseQuickInputListRenderer {\n  constructor(hoverDelegate) {\n    this.hoverDelegate = hoverDelegate;\n  }\n  // TODO: only do the common stuff here and have a subclass handle their specific stuff\n  renderTemplate(container) {\n    const data = Object.create(null);\n    data.toDisposeElement = new DisposableStore();\n    data.toDisposeTemplate = new DisposableStore();\n    data.entry = dom.append(container, $('.quick-input-list-entry'));\n    // Checkbox\n    const label = dom.append(data.entry, $('label.quick-input-list-label'));\n    data.toDisposeTemplate.add(dom.addStandardDisposableListener(label, dom.EventType.CLICK, e => {\n      if (!data.checkbox.offsetParent) {\n        // If checkbox not visible:\n        e.preventDefault(); // Prevent toggle of checkbox when it is immediately shown afterwards. #91740\n      }\n    }));\n    data.checkbox = dom.append(label, $('input.quick-input-list-checkbox'));\n    data.checkbox.type = 'checkbox';\n    // Rows\n    const rows = dom.append(label, $('.quick-input-list-rows'));\n    const row1 = dom.append(rows, $('.quick-input-list-row'));\n    const row2 = dom.append(rows, $('.quick-input-list-row'));\n    // Label\n    data.label = new IconLabel(row1, {\n      supportHighlights: true,\n      supportDescriptionHighlights: true,\n      supportIcons: true,\n      hoverDelegate: this.hoverDelegate\n    });\n    data.toDisposeTemplate.add(data.label);\n    data.icon = dom.prepend(data.label.element, $('.quick-input-list-icon'));\n    // Keybinding\n    const keybindingContainer = dom.append(row1, $('.quick-input-list-entry-keybinding'));\n    data.keybinding = new KeybindingLabel(keybindingContainer, OS);\n    data.toDisposeTemplate.add(data.keybinding);\n    // Detail\n    const detailContainer = dom.append(row2, $('.quick-input-list-label-meta'));\n    data.detail = new IconLabel(detailContainer, {\n      supportHighlights: true,\n      supportIcons: true,\n      hoverDelegate: this.hoverDelegate\n    });\n    data.toDisposeTemplate.add(data.detail);\n    // Separator\n    data.separator = dom.append(data.entry, $('.quick-input-list-separator'));\n    // Actions\n    data.actionBar = new ActionBar(data.entry, this.hoverDelegate ? {\n      hoverDelegate: this.hoverDelegate\n    } : undefined);\n    data.actionBar.domNode.classList.add('quick-input-list-entry-action-bar');\n    data.toDisposeTemplate.add(data.actionBar);\n    return data;\n  }\n  disposeTemplate(data) {\n    data.toDisposeElement.dispose();\n    data.toDisposeTemplate.dispose();\n  }\n  disposeElement(_element, _index, data) {\n    data.toDisposeElement.clear();\n    data.actionBar.clear();\n  }\n}\nlet QuickPickItemElementRenderer = /*#__PURE__*/(() => {\n  let QuickPickItemElementRenderer = class QuickPickItemElementRenderer extends BaseQuickInputListRenderer {\n    static {\n      QuickPickItemElementRenderer_1 = this;\n    }\n    static {\n      this.ID = 'quickpickitem';\n    }\n    constructor(hoverDelegate, themeService) {\n      super(hoverDelegate);\n      this.themeService = themeService;\n      // Follow what we do in the separator renderer\n      this._itemsWithSeparatorsFrequency = new Map();\n    }\n    get templateId() {\n      return QuickPickItemElementRenderer_1.ID;\n    }\n    renderTemplate(container) {\n      const data = super.renderTemplate(container);\n      data.toDisposeTemplate.add(dom.addStandardDisposableListener(data.checkbox, dom.EventType.CHANGE, e => {\n        data.element.checked = data.checkbox.checked;\n      }));\n      return data;\n    }\n    renderElement(node, index, data) {\n      const element = node.element;\n      data.element = element;\n      element.element = data.entry ?? undefined;\n      const mainItem = element.item;\n      data.checkbox.checked = element.checked;\n      data.toDisposeElement.add(element.onChecked(checked => data.checkbox.checked = checked));\n      data.checkbox.disabled = element.checkboxDisabled;\n      const {\n        labelHighlights,\n        descriptionHighlights,\n        detailHighlights\n      } = element;\n      // Icon\n      if (mainItem.iconPath) {\n        const icon = isDark(this.themeService.getColorTheme().type) ? mainItem.iconPath.dark : mainItem.iconPath.light ?? mainItem.iconPath.dark;\n        const iconUrl = URI.revive(icon);\n        data.icon.className = 'quick-input-list-icon';\n        data.icon.style.backgroundImage = dom.asCSSUrl(iconUrl);\n      } else {\n        data.icon.style.backgroundImage = '';\n        data.icon.className = mainItem.iconClass ? `quick-input-list-icon ${mainItem.iconClass}` : '';\n      }\n      // Label\n      let descriptionTitle;\n      // if we have a tooltip, that will be the hover,\n      // with the saneDescription as fallback if it\n      // is defined\n      if (!element.saneTooltip && element.saneDescription) {\n        descriptionTitle = {\n          markdown: {\n            value: element.saneDescription,\n            supportThemeIcons: true\n          },\n          markdownNotSupportedFallback: element.saneDescription\n        };\n      }\n      const options = {\n        matches: labelHighlights || [],\n        // If we have a tooltip, we want that to be shown and not any other hover\n        descriptionTitle,\n        descriptionMatches: descriptionHighlights || [],\n        labelEscapeNewLines: true\n      };\n      options.extraClasses = mainItem.iconClasses;\n      options.italic = mainItem.italic;\n      options.strikethrough = mainItem.strikethrough;\n      data.entry.classList.remove('quick-input-list-separator-as-item');\n      data.label.setLabel(element.saneLabel, element.saneDescription, options);\n      // Keybinding\n      data.keybinding.set(mainItem.keybinding);\n      // Detail\n      if (element.saneDetail) {\n        let title;\n        // If we have a tooltip, we want that to be shown and not any other hover\n        if (!element.saneTooltip) {\n          title = {\n            markdown: {\n              value: element.saneDetail,\n              supportThemeIcons: true\n            },\n            markdownNotSupportedFallback: element.saneDetail\n          };\n        }\n        data.detail.element.style.display = '';\n        data.detail.setLabel(element.saneDetail, undefined, {\n          matches: detailHighlights,\n          title,\n          labelEscapeNewLines: true\n        });\n      } else {\n        data.detail.element.style.display = 'none';\n      }\n      // Separator\n      if (element.separator?.label) {\n        data.separator.textContent = element.separator.label;\n        data.separator.style.display = '';\n        this.addItemWithSeparator(element);\n      } else {\n        data.separator.style.display = 'none';\n      }\n      data.entry.classList.toggle('quick-input-list-separator-border', !!element.separator);\n      // Actions\n      const buttons = mainItem.buttons;\n      if (buttons && buttons.length) {\n        data.actionBar.push(buttons.map((button, index) => quickInputButtonToAction(button, `id-${index}`, () => element.fireButtonTriggered({\n          button,\n          item: element.item\n        }))), {\n          icon: true,\n          label: false\n        });\n        data.entry.classList.add('has-actions');\n      } else {\n        data.entry.classList.remove('has-actions');\n      }\n    }\n    disposeElement(element, _index, data) {\n      this.removeItemWithSeparator(element.element);\n      super.disposeElement(element, _index, data);\n    }\n    isItemWithSeparatorVisible(item) {\n      return this._itemsWithSeparatorsFrequency.has(item);\n    }\n    addItemWithSeparator(item) {\n      this._itemsWithSeparatorsFrequency.set(item, (this._itemsWithSeparatorsFrequency.get(item) || 0) + 1);\n    }\n    removeItemWithSeparator(item) {\n      const frequency = this._itemsWithSeparatorsFrequency.get(item) || 0;\n      if (frequency > 1) {\n        this._itemsWithSeparatorsFrequency.set(item, frequency - 1);\n      } else {\n        this._itemsWithSeparatorsFrequency.delete(item);\n      }\n    }\n  };\n  return QuickPickItemElementRenderer;\n})();\nQuickPickItemElementRenderer = QuickPickItemElementRenderer_1 = __decorate([__param(1, IThemeService)], QuickPickItemElementRenderer);\nlet QuickPickSeparatorElementRenderer = /*#__PURE__*/(() => {\n  class QuickPickSeparatorElementRenderer extends BaseQuickInputListRenderer {\n    constructor() {\n      super(...arguments);\n      // This is a frequency map because sticky scroll re-uses the same renderer to render a second\n      // instance of the same separator.\n      this._visibleSeparatorsFrequency = new Map();\n    }\n    static {\n      this.ID = 'quickpickseparator';\n    }\n    get templateId() {\n      return QuickPickSeparatorElementRenderer.ID;\n    }\n    get visibleSeparators() {\n      return [...this._visibleSeparatorsFrequency.keys()];\n    }\n    isSeparatorVisible(separator) {\n      return this._visibleSeparatorsFrequency.has(separator);\n    }\n    renderTemplate(container) {\n      const data = super.renderTemplate(container);\n      data.checkbox.style.display = 'none';\n      return data;\n    }\n    renderElement(node, index, data) {\n      const element = node.element;\n      data.element = element;\n      element.element = data.entry ?? undefined;\n      element.element.classList.toggle('focus-inside', !!element.focusInsideSeparator);\n      const mainItem = element.separator;\n      const {\n        labelHighlights,\n        descriptionHighlights,\n        detailHighlights\n      } = element;\n      // Icon\n      data.icon.style.backgroundImage = '';\n      data.icon.className = '';\n      // Label\n      let descriptionTitle;\n      // if we have a tooltip, that will be the hover,\n      // with the saneDescription as fallback if it\n      // is defined\n      if (!element.saneTooltip && element.saneDescription) {\n        descriptionTitle = {\n          markdown: {\n            value: element.saneDescription,\n            supportThemeIcons: true\n          },\n          markdownNotSupportedFallback: element.saneDescription\n        };\n      }\n      const options = {\n        matches: labelHighlights || [],\n        // If we have a tooltip, we want that to be shown and not any other hover\n        descriptionTitle,\n        descriptionMatches: descriptionHighlights || [],\n        labelEscapeNewLines: true\n      };\n      data.entry.classList.add('quick-input-list-separator-as-item');\n      data.label.setLabel(element.saneLabel, element.saneDescription, options);\n      // Detail\n      if (element.saneDetail) {\n        let title;\n        // If we have a tooltip, we want that to be shown and not any other hover\n        if (!element.saneTooltip) {\n          title = {\n            markdown: {\n              value: element.saneDetail,\n              supportThemeIcons: true\n            },\n            markdownNotSupportedFallback: element.saneDetail\n          };\n        }\n        data.detail.element.style.display = '';\n        data.detail.setLabel(element.saneDetail, undefined, {\n          matches: detailHighlights,\n          title,\n          labelEscapeNewLines: true\n        });\n      } else {\n        data.detail.element.style.display = 'none';\n      }\n      // Separator\n      data.separator.style.display = 'none';\n      data.entry.classList.add('quick-input-list-separator-border');\n      // Actions\n      const buttons = mainItem.buttons;\n      if (buttons && buttons.length) {\n        data.actionBar.push(buttons.map((button, index) => quickInputButtonToAction(button, `id-${index}`, () => element.fireSeparatorButtonTriggered({\n          button,\n          separator: element.separator\n        }))), {\n          icon: true,\n          label: false\n        });\n        data.entry.classList.add('has-actions');\n      } else {\n        data.entry.classList.remove('has-actions');\n      }\n      this.addSeparator(element);\n    }\n    disposeElement(element, _index, data) {\n      this.removeSeparator(element.element);\n      if (!this.isSeparatorVisible(element.element)) {\n        element.element.element?.classList.remove('focus-inside');\n      }\n      super.disposeElement(element, _index, data);\n    }\n    addSeparator(separator) {\n      this._visibleSeparatorsFrequency.set(separator, (this._visibleSeparatorsFrequency.get(separator) || 0) + 1);\n    }\n    removeSeparator(separator) {\n      const frequency = this._visibleSeparatorsFrequency.get(separator) || 0;\n      if (frequency > 1) {\n        this._visibleSeparatorsFrequency.set(separator, frequency - 1);\n      } else {\n        this._visibleSeparatorsFrequency.delete(separator);\n      }\n    }\n  }\n  return QuickPickSeparatorElementRenderer;\n})();\nlet QuickInputTree = class QuickInputTree extends Disposable {\n  constructor(parent, hoverDelegate, linkOpenerDelegate, id, instantiationService, accessibilityService) {\n    super();\n    this.parent = parent;\n    this.hoverDelegate = hoverDelegate;\n    this.linkOpenerDelegate = linkOpenerDelegate;\n    this.accessibilityService = accessibilityService;\n    //#region QuickInputTree Events\n    this._onKeyDown = new Emitter();\n    this._onLeave = new Emitter();\n    /**\n     * Event that is fired when the tree would no longer have focus.\n    */\n    this.onLeave = this._onLeave.event;\n    this._visibleCountObservable = observableValue('VisibleCount', 0);\n    this.onChangedVisibleCount = Event.fromObservable(this._visibleCountObservable, this._store);\n    this._allVisibleCheckedObservable = observableValue('AllVisibleChecked', false);\n    this.onChangedAllVisibleChecked = Event.fromObservable(this._allVisibleCheckedObservable, this._store);\n    this._checkedCountObservable = observableValue('CheckedCount', 0);\n    this.onChangedCheckedCount = Event.fromObservable(this._checkedCountObservable, this._store);\n    this._checkedElementsObservable = observableValueOpts({\n      equalsFn: equals\n    }, new Array());\n    this.onChangedCheckedElements = Event.fromObservable(this._checkedElementsObservable, this._store);\n    this._onButtonTriggered = new Emitter();\n    this.onButtonTriggered = this._onButtonTriggered.event;\n    this._onSeparatorButtonTriggered = new Emitter();\n    this.onSeparatorButtonTriggered = this._onSeparatorButtonTriggered.event;\n    this._elementChecked = new Emitter();\n    this._elementCheckedEventBufferer = new EventBufferer();\n    //#endregion\n    this._hasCheckboxes = false;\n    this._inputElements = new Array();\n    this._elementTree = new Array();\n    this._itemElements = new Array();\n    // Elements that apply to the current set of elements\n    this._elementDisposable = this._register(new DisposableStore());\n    this._matchOnDescription = false;\n    this._matchOnDetail = false;\n    this._matchOnLabel = true;\n    this._matchOnLabelMode = 'fuzzy';\n    this._sortByLabel = true;\n    this._shouldLoop = true;\n    this._container = dom.append(this.parent, $('.quick-input-list'));\n    this._separatorRenderer = new QuickPickSeparatorElementRenderer(hoverDelegate);\n    this._itemRenderer = instantiationService.createInstance(QuickPickItemElementRenderer, hoverDelegate);\n    this._tree = this._register(instantiationService.createInstance(WorkbenchObjectTree, 'QuickInput', this._container, new QuickInputItemDelegate(), [this._itemRenderer, this._separatorRenderer], {\n      filter: {\n        filter(element) {\n          return element.hidden ? 0 /* TreeVisibility.Hidden */ : element instanceof QuickPickSeparatorElement ? 2 /* TreeVisibility.Recurse */ : 1 /* TreeVisibility.Visible */;\n        }\n      },\n      sorter: {\n        compare: (element, otherElement) => {\n          if (!this.sortByLabel || !this._lastQueryString) {\n            return 0;\n          }\n          const normalizedSearchValue = this._lastQueryString.toLowerCase();\n          return compareEntries(element, otherElement, normalizedSearchValue);\n        }\n      },\n      accessibilityProvider: new QuickInputAccessibilityProvider(),\n      setRowLineHeight: false,\n      multipleSelectionSupport: false,\n      hideTwistiesOfChildlessElements: true,\n      renderIndentGuides: RenderIndentGuides.None,\n      findWidgetEnabled: false,\n      indent: 0,\n      horizontalScrolling: false,\n      allowNonCollapsibleParents: true,\n      alwaysConsumeMouseWheel: true\n    }));\n    this._tree.getHTMLElement().id = id;\n    this._registerListeners();\n  }\n  //#region public getters/setters\n  get onDidChangeFocus() {\n    return Event.map(this._tree.onDidChangeFocus, e => e.elements.filter(e => e instanceof QuickPickItemElement).map(e => e.item), this._store);\n  }\n  get onDidChangeSelection() {\n    return Event.map(this._tree.onDidChangeSelection, e => ({\n      items: e.elements.filter(e => e instanceof QuickPickItemElement).map(e => e.item),\n      event: e.browserEvent\n    }), this._store);\n  }\n  get displayed() {\n    return this._container.style.display !== 'none';\n  }\n  set displayed(value) {\n    this._container.style.display = value ? '' : 'none';\n  }\n  get scrollTop() {\n    return this._tree.scrollTop;\n  }\n  set scrollTop(scrollTop) {\n    this._tree.scrollTop = scrollTop;\n  }\n  get ariaLabel() {\n    return this._tree.ariaLabel;\n  }\n  set ariaLabel(label) {\n    this._tree.ariaLabel = label ?? '';\n  }\n  set enabled(value) {\n    this._tree.getHTMLElement().style.pointerEvents = value ? '' : 'none';\n  }\n  get matchOnDescription() {\n    return this._matchOnDescription;\n  }\n  set matchOnDescription(value) {\n    this._matchOnDescription = value;\n  }\n  get matchOnDetail() {\n    return this._matchOnDetail;\n  }\n  set matchOnDetail(value) {\n    this._matchOnDetail = value;\n  }\n  get matchOnLabel() {\n    return this._matchOnLabel;\n  }\n  set matchOnLabel(value) {\n    this._matchOnLabel = value;\n  }\n  get matchOnLabelMode() {\n    return this._matchOnLabelMode;\n  }\n  set matchOnLabelMode(value) {\n    this._matchOnLabelMode = value;\n  }\n  get sortByLabel() {\n    return this._sortByLabel;\n  }\n  set sortByLabel(value) {\n    this._sortByLabel = value;\n  }\n  get shouldLoop() {\n    return this._shouldLoop;\n  }\n  set shouldLoop(value) {\n    this._shouldLoop = value;\n  }\n  //#endregion\n  //#region register listeners\n  _registerListeners() {\n    this._registerOnKeyDown();\n    this._registerOnContainerClick();\n    this._registerOnMouseMiddleClick();\n    this._registerOnTreeModelChanged();\n    this._registerOnElementChecked();\n    this._registerOnContextMenu();\n    this._registerHoverListeners();\n    this._registerSelectionChangeListener();\n    this._registerSeparatorActionShowingListeners();\n  }\n  _registerOnKeyDown() {\n    // TODO: Should this be added at a higher level?\n    this._register(this._tree.onKeyDown(e => {\n      const event = new StandardKeyboardEvent(e);\n      switch (event.keyCode) {\n        case 10 /* KeyCode.Space */:\n          this.toggleCheckbox();\n          break;\n      }\n      this._onKeyDown.fire(event);\n    }));\n  }\n  _registerOnContainerClick() {\n    this._register(dom.addDisposableListener(this._container, dom.EventType.CLICK, e => {\n      if (e.x || e.y) {\n        // Avoid 'click' triggered by 'space' on checkbox.\n        this._onLeave.fire();\n      }\n    }));\n  }\n  _registerOnMouseMiddleClick() {\n    this._register(dom.addDisposableListener(this._container, dom.EventType.AUXCLICK, e => {\n      if (e.button === 1) {\n        this._onLeave.fire();\n      }\n    }));\n  }\n  _registerOnTreeModelChanged() {\n    this._register(this._tree.onDidChangeModel(() => {\n      const visibleCount = this._itemElements.filter(e => !e.hidden).length;\n      this._visibleCountObservable.set(visibleCount, undefined);\n      if (this._hasCheckboxes) {\n        this._updateCheckedObservables();\n      }\n    }));\n  }\n  _registerOnElementChecked() {\n    // Only fire the last event when buffered\n    this._register(this._elementCheckedEventBufferer.wrapEvent(this._elementChecked.event, (_, e) => e)(_ => this._updateCheckedObservables()));\n  }\n  _registerOnContextMenu() {\n    this._register(this._tree.onContextMenu(e => {\n      if (e.element) {\n        e.browserEvent.preventDefault();\n        // we want to treat a context menu event as\n        // a gesture to open the item at the index\n        // since we do not have any context menu\n        // this enables for example macOS to Ctrl-\n        // click on an item to open it.\n        this._tree.setSelection([e.element]);\n      }\n    }));\n  }\n  _registerHoverListeners() {\n    var _this = this;\n    const delayer = this._register(new ThrottledDelayer(this.hoverDelegate.delay));\n    this._register(this._tree.onMouseOver(/*#__PURE__*/function () {\n      var _ref = _asyncToGenerator(function* (e) {\n        // If we hover over an anchor element, we don't want to show the hover because\n        // the anchor may have a tooltip that we want to show instead.\n        if (dom.isHTMLAnchorElement(e.browserEvent.target)) {\n          delayer.cancel();\n          return;\n        }\n        if (\n        // anchors are an exception as called out above so we skip them here\n        !dom.isHTMLAnchorElement(e.browserEvent.relatedTarget) &&\n        // check if the mouse is still over the same element\n        dom.isAncestor(e.browserEvent.relatedTarget, e.element?.element)) {\n          return;\n        }\n        try {\n          yield delayer.trigger(/*#__PURE__*/_asyncToGenerator(function* () {\n            if (e.element instanceof QuickPickItemElement) {\n              _this.showHover(e.element);\n            }\n          }));\n        } catch (e) {\n          // Ignore cancellation errors due to mouse out\n          if (!isCancellationError(e)) {\n            throw e;\n          }\n        }\n      });\n      return function (_x) {\n        return _ref.apply(this, arguments);\n      };\n    }()));\n    this._register(this._tree.onMouseOut(e => {\n      // onMouseOut triggers every time a new element has been moused over\n      // even if it's on the same list item. We only want one event, so we\n      // check if the mouse is still over the same element.\n      if (dom.isAncestor(e.browserEvent.relatedTarget, e.element?.element)) {\n        return;\n      }\n      delayer.cancel();\n    }));\n  }\n  /**\n   * Register's focus change and mouse events so that we can track when items inside of a\n   * separator's section are focused or hovered so that we can display the separator's actions\n   */\n  _registerSeparatorActionShowingListeners() {\n    this._register(this._tree.onDidChangeFocus(e => {\n      const parent = e.elements[0] ? this._tree.getParentElement(e.elements[0])\n      // treat null as focus lost and when we have no separators\n      : null;\n      for (const separator of this._separatorRenderer.visibleSeparators) {\n        const value = separator === parent;\n        // get bitness of ACTIVE_ITEM and check if it changed\n        const currentActive = !!(separator.focusInsideSeparator & QuickPickSeparatorFocusReason.ACTIVE_ITEM);\n        if (currentActive !== value) {\n          if (value) {\n            separator.focusInsideSeparator |= QuickPickSeparatorFocusReason.ACTIVE_ITEM;\n          } else {\n            separator.focusInsideSeparator &= ~QuickPickSeparatorFocusReason.ACTIVE_ITEM;\n          }\n          this._tree.rerender(separator);\n        }\n      }\n    }));\n    this._register(this._tree.onMouseOver(e => {\n      const parent = e.element ? this._tree.getParentElement(e.element) : null;\n      for (const separator of this._separatorRenderer.visibleSeparators) {\n        if (separator !== parent) {\n          continue;\n        }\n        const currentMouse = !!(separator.focusInsideSeparator & QuickPickSeparatorFocusReason.MOUSE_HOVER);\n        if (!currentMouse) {\n          separator.focusInsideSeparator |= QuickPickSeparatorFocusReason.MOUSE_HOVER;\n          this._tree.rerender(separator);\n        }\n      }\n    }));\n    this._register(this._tree.onMouseOut(e => {\n      const parent = e.element ? this._tree.getParentElement(e.element) : null;\n      for (const separator of this._separatorRenderer.visibleSeparators) {\n        if (separator !== parent) {\n          continue;\n        }\n        const currentMouse = !!(separator.focusInsideSeparator & QuickPickSeparatorFocusReason.MOUSE_HOVER);\n        if (currentMouse) {\n          separator.focusInsideSeparator &= ~QuickPickSeparatorFocusReason.MOUSE_HOVER;\n          this._tree.rerender(separator);\n        }\n      }\n    }));\n  }\n  _registerSelectionChangeListener() {\n    // When the user selects a separator, the separator will move to the top and focus will be\n    // set to the first element after the separator.\n    this._register(this._tree.onDidChangeSelection(e => {\n      const elementsWithoutSeparators = e.elements.filter(e => e instanceof QuickPickItemElement);\n      if (elementsWithoutSeparators.length !== e.elements.length) {\n        if (e.elements.length === 1 && e.elements[0] instanceof QuickPickSeparatorElement) {\n          this._tree.setFocus([e.elements[0].children[0]]);\n          this._tree.reveal(e.elements[0], 0);\n        }\n        this._tree.setSelection(elementsWithoutSeparators);\n      }\n    }));\n  }\n  //#endregion\n  //#region public methods\n  setAllVisibleChecked(checked) {\n    this._elementCheckedEventBufferer.bufferEvents(() => {\n      this._itemElements.forEach(element => {\n        if (!element.hidden && !element.checkboxDisabled) {\n          // Would fire an event if we didn't beffer the events\n          element.checked = checked;\n        }\n      });\n    });\n  }\n  setElements(inputElements) {\n    this._elementDisposable.clear();\n    this._lastQueryString = undefined;\n    this._inputElements = inputElements;\n    this._hasCheckboxes = this.parent.classList.contains('show-checkboxes');\n    let currentSeparatorElement;\n    this._itemElements = new Array();\n    this._elementTree = inputElements.reduce((result, item, index) => {\n      let element;\n      if (item.type === 'separator') {\n        if (!item.buttons) {\n          // This separator will be rendered as a part of the list item\n          return result;\n        }\n        currentSeparatorElement = new QuickPickSeparatorElement(index, e => this._onSeparatorButtonTriggered.fire(e), item);\n        element = currentSeparatorElement;\n      } else {\n        const previous = index > 0 ? inputElements[index - 1] : undefined;\n        let separator;\n        if (previous && previous.type === 'separator' && !previous.buttons) {\n          // Found an inline separator so we clear out the current separator element\n          currentSeparatorElement = undefined;\n          separator = previous;\n        }\n        const qpi = new QuickPickItemElement(index, this._hasCheckboxes, e => this._onButtonTriggered.fire(e), this._elementChecked, item, separator);\n        this._itemElements.push(qpi);\n        if (currentSeparatorElement) {\n          currentSeparatorElement.children.push(qpi);\n          return result;\n        }\n        element = qpi;\n      }\n      result.push(element);\n      return result;\n    }, new Array());\n    this._setElementsToTree(this._elementTree);\n    // Accessibility hack, unfortunately on next tick\n    // https://github.com/microsoft/vscode/issues/211976\n    if (this.accessibilityService.isScreenReaderOptimized()) {\n      setTimeout(() => {\n        const focusedElement = this._tree.getHTMLElement().querySelector(`.monaco-list-row.focused`);\n        const parent = focusedElement?.parentNode;\n        if (focusedElement && parent) {\n          const nextSibling = focusedElement.nextSibling;\n          focusedElement.remove();\n          parent.insertBefore(focusedElement, nextSibling);\n        }\n      }, 0);\n    }\n  }\n  setFocusedElements(items) {\n    const elements = items.map(item => this._itemElements.find(e => e.item === item)).filter(e => !!e).filter(e => !e.hidden);\n    this._tree.setFocus(elements);\n    if (items.length > 0) {\n      const focused = this._tree.getFocus()[0];\n      if (focused) {\n        this._tree.reveal(focused);\n      }\n    }\n  }\n  getActiveDescendant() {\n    return this._tree.getHTMLElement().getAttribute('aria-activedescendant');\n  }\n  setSelectedElements(items) {\n    const elements = items.map(item => this._itemElements.find(e => e.item === item)).filter(e => !!e);\n    this._tree.setSelection(elements);\n  }\n  getCheckedElements() {\n    return this._itemElements.filter(e => e.checked).map(e => e.item);\n  }\n  setCheckedElements(items) {\n    this._elementCheckedEventBufferer.bufferEvents(() => {\n      const checked = new Set();\n      for (const item of items) {\n        checked.add(item);\n      }\n      for (const element of this._itemElements) {\n        // Would fire an event if we didn't beffer the events\n        element.checked = checked.has(element.item);\n      }\n    });\n  }\n  focus(what) {\n    if (!this._itemElements.length) {\n      return;\n    }\n    if (what === QuickPickFocus.Second && this._itemElements.length < 2) {\n      what = QuickPickFocus.First;\n    }\n    switch (what) {\n      case QuickPickFocus.First:\n        this._tree.scrollTop = 0;\n        this._tree.focusFirst(undefined, e => e.element instanceof QuickPickItemElement);\n        break;\n      case QuickPickFocus.Second:\n        {\n          this._tree.scrollTop = 0;\n          let isSecondItem = false;\n          this._tree.focusFirst(undefined, e => {\n            if (!(e.element instanceof QuickPickItemElement)) {\n              return false;\n            }\n            if (isSecondItem) {\n              return true;\n            }\n            isSecondItem = !isSecondItem;\n            return false;\n          });\n          break;\n        }\n      case QuickPickFocus.Last:\n        this._tree.scrollTop = this._tree.scrollHeight;\n        this._tree.focusLast(undefined, e => e.element instanceof QuickPickItemElement);\n        break;\n      case QuickPickFocus.Next:\n        {\n          const prevFocus = this._tree.getFocus();\n          this._tree.focusNext(undefined, this._shouldLoop, undefined, e => {\n            if (!(e.element instanceof QuickPickItemElement)) {\n              return false;\n            }\n            this._tree.reveal(e.element);\n            return true;\n          });\n          const currentFocus = this._tree.getFocus();\n          if (prevFocus.length && prevFocus[0] === currentFocus[0] && prevFocus[0] === this._itemElements[this._itemElements.length - 1]) {\n            this._onLeave.fire();\n          }\n          break;\n        }\n      case QuickPickFocus.Previous:\n        {\n          const prevFocus = this._tree.getFocus();\n          this._tree.focusPrevious(undefined, this._shouldLoop, undefined, e => {\n            if (!(e.element instanceof QuickPickItemElement)) {\n              return false;\n            }\n            const parent = this._tree.getParentElement(e.element);\n            if (parent === null || parent.children[0] !== e.element) {\n              this._tree.reveal(e.element);\n            } else {\n              // Only if we are the first child of a separator do we reveal the separator\n              this._tree.reveal(parent);\n            }\n            return true;\n          });\n          const currentFocus = this._tree.getFocus();\n          if (prevFocus.length && prevFocus[0] === currentFocus[0] && prevFocus[0] === this._itemElements[0]) {\n            this._onLeave.fire();\n          }\n          break;\n        }\n      case QuickPickFocus.NextPage:\n        this._tree.focusNextPage(undefined, e => {\n          if (!(e.element instanceof QuickPickItemElement)) {\n            return false;\n          }\n          this._tree.reveal(e.element);\n          return true;\n        });\n        break;\n      case QuickPickFocus.PreviousPage:\n        this._tree.focusPreviousPage(undefined, e => {\n          if (!(e.element instanceof QuickPickItemElement)) {\n            return false;\n          }\n          const parent = this._tree.getParentElement(e.element);\n          if (parent === null || parent.children[0] !== e.element) {\n            this._tree.reveal(e.element);\n          } else {\n            this._tree.reveal(parent);\n          }\n          return true;\n        });\n        break;\n      case QuickPickFocus.NextSeparator:\n        {\n          let foundSeparatorAsItem = false;\n          const before = this._tree.getFocus()[0];\n          this._tree.focusNext(undefined, true, undefined, e => {\n            if (foundSeparatorAsItem) {\n              // This should be the index right after the separator so it\n              // is the item we want to focus.\n              return true;\n            }\n            if (e.element instanceof QuickPickSeparatorElement) {\n              foundSeparatorAsItem = true;\n              // If the separator is visible, then we should just reveal its first child so it's not as jarring.\n              if (this._separatorRenderer.isSeparatorVisible(e.element)) {\n                this._tree.reveal(e.element.children[0]);\n              } else {\n                // If the separator is not visible, then we should\n                // push it up to the top of the list.\n                this._tree.reveal(e.element, 0);\n              }\n            } else if (e.element instanceof QuickPickItemElement) {\n              if (e.element.separator) {\n                if (this._itemRenderer.isItemWithSeparatorVisible(e.element)) {\n                  this._tree.reveal(e.element);\n                } else {\n                  this._tree.reveal(e.element, 0);\n                }\n                return true;\n              } else if (e.element === this._elementTree[0]) {\n                // We should stop at the first item in the list if it's a regular item.\n                this._tree.reveal(e.element, 0);\n                return true;\n              }\n            }\n            return false;\n          });\n          const after = this._tree.getFocus()[0];\n          if (before === after) {\n            // If we didn't move, then we should just move to the end\n            // of the list.\n            this._tree.scrollTop = this._tree.scrollHeight;\n            this._tree.focusLast(undefined, e => e.element instanceof QuickPickItemElement);\n          }\n          break;\n        }\n      case QuickPickFocus.PreviousSeparator:\n        {\n          let focusElement;\n          // If we are already sitting on an inline separator, then we\n          // have already found the _current_ separator and need to\n          // move to the previous one.\n          let foundSeparator = !!this._tree.getFocus()[0]?.separator;\n          this._tree.focusPrevious(undefined, true, undefined, e => {\n            if (e.element instanceof QuickPickSeparatorElement) {\n              if (foundSeparator) {\n                if (!focusElement) {\n                  if (this._separatorRenderer.isSeparatorVisible(e.element)) {\n                    this._tree.reveal(e.element);\n                  } else {\n                    this._tree.reveal(e.element, 0);\n                  }\n                  focusElement = e.element.children[0];\n                }\n              } else {\n                foundSeparator = true;\n              }\n            } else if (e.element instanceof QuickPickItemElement) {\n              if (!focusElement) {\n                if (e.element.separator) {\n                  if (this._itemRenderer.isItemWithSeparatorVisible(e.element)) {\n                    this._tree.reveal(e.element);\n                  } else {\n                    this._tree.reveal(e.element, 0);\n                  }\n                  focusElement = e.element;\n                } else if (e.element === this._elementTree[0]) {\n                  // We should stop at the first item in the list if it's a regular item.\n                  this._tree.reveal(e.element, 0);\n                  return true;\n                }\n              }\n            }\n            return false;\n          });\n          if (focusElement) {\n            this._tree.setFocus([focusElement]);\n          }\n          break;\n        }\n    }\n  }\n  clearFocus() {\n    this._tree.setFocus([]);\n  }\n  domFocus() {\n    this._tree.domFocus();\n  }\n  layout(maxHeight) {\n    this._tree.getHTMLElement().style.maxHeight = maxHeight ? `${\n    // Make sure height aligns with list item heights\n    Math.floor(maxHeight / 44) * 44\n    // Add some extra height so that it's clear there's more to scroll\n    + 6}px` : '';\n    this._tree.layout();\n  }\n  filter(query) {\n    this._lastQueryString = query;\n    if (!(this._sortByLabel || this._matchOnLabel || this._matchOnDescription || this._matchOnDetail)) {\n      this._tree.layout();\n      return false;\n    }\n    const queryWithWhitespace = query;\n    query = query.trim();\n    // Reset filtering\n    if (!query || !(this.matchOnLabel || this.matchOnDescription || this.matchOnDetail)) {\n      this._itemElements.forEach(element => {\n        element.labelHighlights = undefined;\n        element.descriptionHighlights = undefined;\n        element.detailHighlights = undefined;\n        element.hidden = false;\n        const previous = element.index && this._inputElements[element.index - 1];\n        if (element.item) {\n          element.separator = previous && previous.type === 'separator' && !previous.buttons ? previous : undefined;\n        }\n      });\n    }\n    // Filter by value (since we support icons in labels, use $(..) aware fuzzy matching)\n    else {\n      let currentSeparator;\n      this._itemElements.forEach(element => {\n        let labelHighlights;\n        if (this.matchOnLabelMode === 'fuzzy') {\n          labelHighlights = this.matchOnLabel ? matchesFuzzyIconAware(query, parseLabelWithIcons(element.saneLabel)) ?? undefined : undefined;\n        } else {\n          labelHighlights = this.matchOnLabel ? matchesContiguousIconAware(queryWithWhitespace, parseLabelWithIcons(element.saneLabel)) ?? undefined : undefined;\n        }\n        const descriptionHighlights = this.matchOnDescription ? matchesFuzzyIconAware(query, parseLabelWithIcons(element.saneDescription || '')) ?? undefined : undefined;\n        const detailHighlights = this.matchOnDetail ? matchesFuzzyIconAware(query, parseLabelWithIcons(element.saneDetail || '')) ?? undefined : undefined;\n        if (labelHighlights || descriptionHighlights || detailHighlights) {\n          element.labelHighlights = labelHighlights;\n          element.descriptionHighlights = descriptionHighlights;\n          element.detailHighlights = detailHighlights;\n          element.hidden = false;\n        } else {\n          element.labelHighlights = undefined;\n          element.descriptionHighlights = undefined;\n          element.detailHighlights = undefined;\n          element.hidden = element.item ? !element.item.alwaysShow : true;\n        }\n        // Ensure separators are filtered out first before deciding if we need to bring them back\n        if (element.item) {\n          element.separator = undefined;\n        } else if (element.separator) {\n          element.hidden = true;\n        }\n        // we can show the separator unless the list gets sorted by match\n        if (!this.sortByLabel) {\n          const previous = element.index && this._inputElements[element.index - 1] || undefined;\n          if (previous?.type === 'separator' && !previous.buttons) {\n            currentSeparator = previous;\n          }\n          if (currentSeparator && !element.hidden) {\n            element.separator = currentSeparator;\n            currentSeparator = undefined;\n          }\n        }\n      });\n    }\n    this._setElementsToTree(this._sortByLabel && query\n    // We don't render any separators if we're sorting so just render the elements\n    ? this._itemElements\n    // Render the full tree\n    : this._elementTree);\n    this._tree.layout();\n    return true;\n  }\n  toggleCheckbox() {\n    this._elementCheckedEventBufferer.bufferEvents(() => {\n      const elements = this._tree.getFocus().filter(e => e instanceof QuickPickItemElement);\n      const allChecked = this._allVisibleChecked(elements);\n      for (const element of elements) {\n        if (!element.checkboxDisabled) {\n          // Would fire an event if we didn't have the flag set\n          element.checked = !allChecked;\n        }\n      }\n    });\n  }\n  style(styles) {\n    this._tree.style(styles);\n  }\n  toggleHover() {\n    const focused = this._tree.getFocus()[0];\n    if (!focused?.saneTooltip || !(focused instanceof QuickPickItemElement)) {\n      return;\n    }\n    // if there's a hover already, hide it (toggle off)\n    if (this._lastHover && !this._lastHover.isDisposed) {\n      this._lastHover.dispose();\n      return;\n    }\n    // If there is no hover, show it (toggle on)\n    this.showHover(focused);\n    const store = new DisposableStore();\n    store.add(this._tree.onDidChangeFocus(e => {\n      if (e.elements[0] instanceof QuickPickItemElement) {\n        this.showHover(e.elements[0]);\n      }\n    }));\n    if (this._lastHover) {\n      store.add(this._lastHover);\n    }\n    this._elementDisposable.add(store);\n  }\n  //#endregion\n  //#region private methods\n  _setElementsToTree(elements) {\n    const treeElements = new Array();\n    for (const element of elements) {\n      if (element instanceof QuickPickSeparatorElement) {\n        treeElements.push({\n          element,\n          collapsible: false,\n          collapsed: false,\n          children: element.children.map(e => ({\n            element: e,\n            collapsible: false,\n            collapsed: false\n          }))\n        });\n      } else {\n        treeElements.push({\n          element,\n          collapsible: false,\n          collapsed: false\n        });\n      }\n    }\n    this._tree.setChildren(null, treeElements);\n  }\n  _allVisibleChecked(elements, whenNoneVisible = true) {\n    for (let i = 0, n = elements.length; i < n; i++) {\n      const element = elements[i];\n      if (!element.hidden) {\n        if (!element.checked) {\n          return false;\n        } else {\n          whenNoneVisible = true;\n        }\n      }\n    }\n    return whenNoneVisible;\n  }\n  _updateCheckedObservables() {\n    transaction(tx => {\n      this._allVisibleCheckedObservable.set(this._allVisibleChecked(this._itemElements, false), tx);\n      const checkedCount = this._itemElements.filter(element => element.checked).length;\n      this._checkedCountObservable.set(checkedCount, tx);\n      this._checkedElementsObservable.set(this.getCheckedElements(), tx);\n    });\n  }\n  /**\n   * Disposes of the hover and shows a new one for the given index if it has a tooltip.\n   * @param element The element to show the hover for\n   */\n  showHover(element) {\n    if (this._lastHover && !this._lastHover.isDisposed) {\n      this.hoverDelegate.onDidHideHover?.();\n      this._lastHover?.dispose();\n    }\n    if (!element.element || !element.saneTooltip) {\n      return;\n    }\n    this._lastHover = this.hoverDelegate.showHover({\n      content: element.saneTooltip,\n      target: element.element,\n      linkHandler: url => {\n        this.linkOpenerDelegate(url);\n      },\n      appearance: {\n        showPointer: true\n      },\n      container: this._container,\n      position: {\n        hoverPosition: 1 /* HoverPosition.RIGHT */\n      }\n    }, false);\n  }\n};\n__decorate([memoize], QuickInputTree.prototype, \"onDidChangeFocus\", null);\n__decorate([memoize], QuickInputTree.prototype, \"onDidChangeSelection\", null);\nQuickInputTree = __decorate([__param(4, IInstantiationService), __param(5, IAccessibilityService)], QuickInputTree);\nexport { QuickInputTree };\nfunction matchesContiguousIconAware(query, target) {\n  const {\n    text,\n    iconOffsets\n  } = target;\n  // Return early if there are no icon markers in the word to match against\n  if (!iconOffsets || iconOffsets.length === 0) {\n    return matchesContiguous(query, text);\n  }\n  // Trim the word to match against because it could have leading\n  // whitespace now if the word started with an icon\n  const wordToMatchAgainstWithoutIconsTrimmed = ltrim(text, ' ');\n  const leadingWhitespaceOffset = text.length - wordToMatchAgainstWithoutIconsTrimmed.length;\n  // match on value without icon\n  const matches = matchesContiguous(query, wordToMatchAgainstWithoutIconsTrimmed);\n  // Map matches back to offsets with icon and trimming\n  if (matches) {\n    for (const match of matches) {\n      const iconOffset = iconOffsets[match.start + leadingWhitespaceOffset] /* icon offsets at index */ + leadingWhitespaceOffset /* overall leading whitespace offset */;\n      match.start += iconOffset;\n      match.end += iconOffset;\n    }\n  }\n  return matches;\n}\nfunction matchesContiguous(word, wordToMatchAgainst) {\n  const matchIndex = wordToMatchAgainst.toLowerCase().indexOf(word.toLowerCase());\n  if (matchIndex !== -1) {\n    return [{\n      start: matchIndex,\n      end: matchIndex + word.length\n    }];\n  }\n  return null;\n}\nfunction compareEntries(elementA, elementB, lookFor) {\n  const labelHighlightsA = elementA.labelHighlights || [];\n  const labelHighlightsB = elementB.labelHighlights || [];\n  if (labelHighlightsA.length && !labelHighlightsB.length) {\n    return -1;\n  }\n  if (!labelHighlightsA.length && labelHighlightsB.length) {\n    return 1;\n  }\n  if (labelHighlightsA.length === 0 && labelHighlightsB.length === 0) {\n    return 0;\n  }\n  return compareAnything(elementA.saneSortLabel, elementB.saneSortLabel, lookFor);\n}", "map": {"version": 3, "names": ["__decorate", "decorators", "target", "key", "desc", "c", "arguments", "length", "r", "Object", "getOwnPropertyDescriptor", "d", "Reflect", "decorate", "i", "defineProperty", "__param", "paramIndex", "decorator", "QuickPickItemElementRenderer_1", "dom", "Emitter", "Event", "EventBuff<PERSON>", "localize", "IInstantiationService", "WorkbenchObjectTree", "IThemeService", "Disposable", "DisposableStore", "QuickPickFocus", "StandardKeyboardEvent", "OS", "memoize", "IconLabel", "KeybindingLabel", "ActionBar", "isDark", "URI", "quickInputButtonToAction", "Lazy", "getCodiconAriaLabel", "matchesFuzzyIconAware", "parseLabelWithIcons", "compareAnything", "ltrim", "RenderIndentGuides", "Thrott<PERSON><PERSON><PERSON><PERSON>", "isCancellationError", "IAccessibilityService", "observableValue", "observableValueOpts", "transaction", "equals", "$", "BaseQuickPickItemElement", "constructor", "index", "hasCheckbox", "mainItem", "_hidden", "_init", "saneLabel", "label", "saneSortLabel", "text", "trim", "saneAriaLabel", "aria<PERSON><PERSON><PERSON>", "saneDescription", "saneDetail", "map", "s", "filter", "join", "_saneDescription", "description", "_saneTooltip", "tooltip", "value", "element", "_element", "hidden", "_saneDetail", "saneTooltip", "labelHighlights", "_labelHighlights", "descriptionHighlights", "_descriptionHigh<PERSON>", "detailHighlights", "_detailHighlights", "QuickPickItemElement", "fireButtonTriggered", "_onChecked", "item", "_separator", "_checked", "onChecked", "event", "e", "checked", "None", "detail", "highlights", "separator", "fire", "checkboxDisabled", "disabled", "QuickPickSeparatorFocusReason", "QuickPickSeparatorElement", "fireSeparatorButtonTriggered", "children", "Array", "focusInsideSeparator", "NONE", "QuickInputItemDelegate", "getHeight", "getTemplateId", "QuickPickItemElement<PERSON><PERSON><PERSON>", "ID", "QuickPickSeparatorE<PERSON><PERSON><PERSON><PERSON>", "QuickInputAccessibilityProvider", "getWidgetAriaLabel", "getAriaLabel", "getWidgetRole", "getRole", "isChecked", "undefined", "onDidChange", "BaseQuickInputList<PERSON><PERSON><PERSON>", "hoverDelegate", "renderTemplate", "container", "data", "create", "toDisposeElement", "toDisposeTemplate", "entry", "append", "add", "addStandardDisposableListener", "EventType", "CLICK", "checkbox", "offsetParent", "preventDefault", "type", "rows", "row1", "row2", "supportHighlights", "supportDescriptionHighlights", "supportIcons", "icon", "prepend", "keybindingContainer", "keybinding", "detailContainer", "actionBar", "domNode", "classList", "disposeTemplate", "dispose", "disposeElement", "_index", "clear", "themeService", "_itemsWithSeparatorsFrequency", "Map", "templateId", "CHANGE", "renderElement", "node", "iconPath", "getColorTheme", "dark", "light", "iconUrl", "revive", "className", "style", "backgroundImage", "asCSSUrl", "iconClass", "descriptionTitle", "markdown", "supportThemeIcons", "markdownNotSupportedFallback", "options", "matches", "descriptionMatches", "labelEscapeNewLines", "extraClasses", "iconClasses", "italic", "strikethrough", "remove", "<PERSON><PERSON><PERSON><PERSON>", "set", "title", "display", "textContent", "addItemWithSeparator", "toggle", "buttons", "push", "button", "removeItemWithSeparator", "isItemWithSeparatorVisible", "has", "get", "frequency", "delete", "_visibleSeparatorsFrequency", "visibleSeparators", "keys", "isSeparatorVisible", "addSeparator", "removeSeparator", "QuickInputTree", "parent", "linkOpenerDelegate", "id", "instantiationService", "accessibilityService", "_onKeyDown", "_onLeave", "onLeave", "_visibleCountObservable", "onChangedVisibleCount", "fromObservable", "_store", "_allVisibleCheckedObservable", "onChangedAllVisibleChecked", "_checkedCountObservable", "onChangedCheckedCount", "_checkedElementsObservable", "equalsFn", "onChangedCheckedElements", "_onButtonTriggered", "onButtonTriggered", "_onSeparatorButtonTriggered", "onSeparatorButtonTriggered", "_elementChecked", "_elementCheckedEventBufferer", "_hasCheckboxes", "_inputElements", "_elementTree", "_itemElements", "_elementDisposable", "_register", "_matchOnDescription", "_matchOnDetail", "_matchOnLabel", "_matchOnLabelMode", "_sortByLabel", "_shouldLoop", "_container", "_separator<PERSON><PERSON><PERSON>", "_item<PERSON><PERSON>er", "createInstance", "_tree", "sorter", "compare", "otherElement", "sortByLabel", "_lastQueryString", "normalizedSearchValue", "toLowerCase", "compareEntries", "accessibilityProvider", "setRowLineHeight", "multipleSelectionSupport", "hideTwistiesOfChildlessElements", "renderIndentGuides", "findWidgetEnabled", "indent", "horizontalScrolling", "allowNonCollapsibleParents", "alwaysConsumeMouseWheel", "getHTMLElement", "_registerListeners", "onDidChangeFocus", "elements", "onDidChangeSelection", "items", "browserEvent", "displayed", "scrollTop", "enabled", "pointerEvents", "matchOnDescription", "matchOnDetail", "matchOnLabel", "matchOnLabelMode", "shouldLoop", "_registerOnKeyDown", "_registerOnContainerClick", "_registerOnMouseMiddleClick", "_registerOnTreeModelChanged", "_registerOnElementChecked", "_registerOnContextMenu", "_registerHoverListeners", "_registerSelectionChangeListener", "_registerSeparatorActionShowingListeners", "onKeyDown", "keyCode", "toggleCheckbox", "addDisposableListener", "x", "y", "AUXCLICK", "onDidChangeModel", "visibleCount", "_updateCheckedObservables", "wrapEvent", "_", "onContextMenu", "setSelection", "_this", "delayer", "delay", "onMouseOver", "_ref", "_asyncToGenerator", "isHTMLAnchorElement", "cancel", "relatedTarget", "isAncestor", "trigger", "showHover", "_x", "apply", "onMouseOut", "getParentElement", "currentActive", "ACTIVE_ITEM", "rerender", "currentMouse", "MOUSE_HOVER", "elementsWithoutSeparators", "setFocus", "reveal", "setAllVisibleChecked", "bufferEvents", "for<PERSON>ach", "setElements", "inputElements", "contains", "currentSeparatorElement", "reduce", "result", "previous", "qpi", "_setElementsToTree", "isScreenReaderOptimized", "setTimeout", "focusedElement", "querySelector", "parentNode", "nextS<PERSON>ling", "insertBefore", "setFocusedElements", "find", "focused", "getFocus", "getActiveDescendant", "getAttribute", "setSelectedElements", "getCheckedElements", "setCheckedElements", "Set", "focus", "what", "Second", "First", "focusFirst", "isSecondItem", "Last", "scrollHeight", "focusLast", "Next", "prevFocus", "focusNext", "currentFocus", "Previous", "focusPrevious", "NextPage", "focusNextPage", "PreviousPage", "focusPreviousPage", "NextSeparator", "foundSeparatorAsItem", "before", "after", "PreviousSeparator", "focusElement", "found<PERSON>ep<PERSON><PERSON>", "clearFocus", "domFocus", "layout", "maxHeight", "Math", "floor", "query", "queryWithWhitespace", "currentSeparator", "matchesContiguousIconAware", "alwaysShow", "allChecked", "_allVisibleChecked", "styles", "toggleHover", "_lastHover", "isDisposed", "store", "treeElements", "collapsible", "collapsed", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "whenNoneVisible", "n", "tx", "checkedCount", "onDidHideHover", "content", "linkHandler", "url", "appearance", "showPointer", "position", "hoverPosition", "prototype", "iconOffsets", "matchesContiguous", "wordToMatchAgainstWithoutIconsTrimmed", "leadingWhitespaceOffset", "match", "iconOffset", "start", "end", "word", "wordToMatchAgainst", "matchIndex", "indexOf", "elementA", "elementB", "lookFor", "labelHighlightsA", "labelHighlightsB"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/platform/quickinput/browser/quickInputTree.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nvar __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __param = (this && this.__param) || function (paramIndex, decorator) {\n    return function (target, key) { decorator(target, key, paramIndex); }\n};\nvar QuickPickItemElementRenderer_1;\nimport * as dom from '../../../base/browser/dom.js';\nimport { Emitter, Event, EventBufferer } from '../../../base/common/event.js';\nimport { localize } from '../../../nls.js';\nimport { IInstantiationService } from '../../instantiation/common/instantiation.js';\nimport { WorkbenchObjectTree } from '../../list/browser/listService.js';\nimport { IThemeService } from '../../theme/common/themeService.js';\nimport { Disposable, DisposableStore } from '../../../base/common/lifecycle.js';\nimport { QuickPickFocus } from '../common/quickInput.js';\nimport { StandardKeyboardEvent } from '../../../base/browser/keyboardEvent.js';\nimport { OS } from '../../../base/common/platform.js';\nimport { memoize } from '../../../base/common/decorators.js';\nimport { IconLabel } from '../../../base/browser/ui/iconLabel/iconLabel.js';\nimport { KeybindingLabel } from '../../../base/browser/ui/keybindingLabel/keybindingLabel.js';\nimport { ActionBar } from '../../../base/browser/ui/actionbar/actionbar.js';\nimport { isDark } from '../../theme/common/theme.js';\nimport { URI } from '../../../base/common/uri.js';\nimport { quickInputButtonToAction } from './quickInputUtils.js';\nimport { Lazy } from '../../../base/common/lazy.js';\nimport { getCodiconAriaLabel, matchesFuzzyIconAware, parseLabelWithIcons } from '../../../base/common/iconLabels.js';\nimport { compareAnything } from '../../../base/common/comparers.js';\nimport { ltrim } from '../../../base/common/strings.js';\nimport { RenderIndentGuides } from '../../../base/browser/ui/tree/abstractTree.js';\nimport { ThrottledDelayer } from '../../../base/common/async.js';\nimport { isCancellationError } from '../../../base/common/errors.js';\nimport { IAccessibilityService } from '../../accessibility/common/accessibility.js';\nimport { observableValue, observableValueOpts, transaction } from '../../../base/common/observable.js';\nimport { equals } from '../../../base/common/arrays.js';\nconst $ = dom.$;\nclass BaseQuickPickItemElement {\n    constructor(index, hasCheckbox, mainItem) {\n        this.index = index;\n        this.hasCheckbox = hasCheckbox;\n        this._hidden = false;\n        this._init = new Lazy(() => {\n            const saneLabel = mainItem.label ?? '';\n            const saneSortLabel = parseLabelWithIcons(saneLabel).text.trim();\n            const saneAriaLabel = mainItem.ariaLabel || [saneLabel, this.saneDescription, this.saneDetail]\n                .map(s => getCodiconAriaLabel(s))\n                .filter(s => !!s)\n                .join(', ');\n            return {\n                saneLabel,\n                saneSortLabel,\n                saneAriaLabel\n            };\n        });\n        this._saneDescription = mainItem.description;\n        this._saneTooltip = mainItem.tooltip;\n    }\n    // #region Lazy Getters\n    get saneLabel() {\n        return this._init.value.saneLabel;\n    }\n    get saneSortLabel() {\n        return this._init.value.saneSortLabel;\n    }\n    get saneAriaLabel() {\n        return this._init.value.saneAriaLabel;\n    }\n    get element() {\n        return this._element;\n    }\n    set element(value) {\n        this._element = value;\n    }\n    get hidden() {\n        return this._hidden;\n    }\n    set hidden(value) {\n        this._hidden = value;\n    }\n    get saneDescription() {\n        return this._saneDescription;\n    }\n    set saneDescription(value) {\n        this._saneDescription = value;\n    }\n    get saneDetail() {\n        return this._saneDetail;\n    }\n    set saneDetail(value) {\n        this._saneDetail = value;\n    }\n    get saneTooltip() {\n        return this._saneTooltip;\n    }\n    set saneTooltip(value) {\n        this._saneTooltip = value;\n    }\n    get labelHighlights() {\n        return this._labelHighlights;\n    }\n    set labelHighlights(value) {\n        this._labelHighlights = value;\n    }\n    get descriptionHighlights() {\n        return this._descriptionHighlights;\n    }\n    set descriptionHighlights(value) {\n        this._descriptionHighlights = value;\n    }\n    get detailHighlights() {\n        return this._detailHighlights;\n    }\n    set detailHighlights(value) {\n        this._detailHighlights = value;\n    }\n}\nclass QuickPickItemElement extends BaseQuickPickItemElement {\n    constructor(index, hasCheckbox, fireButtonTriggered, _onChecked, item, _separator) {\n        super(index, hasCheckbox, item);\n        this.fireButtonTriggered = fireButtonTriggered;\n        this._onChecked = _onChecked;\n        this.item = item;\n        this._separator = _separator;\n        this._checked = false;\n        this.onChecked = hasCheckbox\n            ? Event.map(Event.filter(this._onChecked.event, e => e.element === this), e => e.checked)\n            : Event.None;\n        this._saneDetail = item.detail;\n        this._labelHighlights = item.highlights?.label;\n        this._descriptionHighlights = item.highlights?.description;\n        this._detailHighlights = item.highlights?.detail;\n    }\n    get separator() {\n        return this._separator;\n    }\n    set separator(value) {\n        this._separator = value;\n    }\n    get checked() {\n        return this._checked;\n    }\n    set checked(value) {\n        if (value !== this._checked) {\n            this._checked = value;\n            this._onChecked.fire({ element: this, checked: value });\n        }\n    }\n    get checkboxDisabled() {\n        return !!this.item.disabled;\n    }\n}\nvar QuickPickSeparatorFocusReason;\n(function (QuickPickSeparatorFocusReason) {\n    /**\n     * No item is hovered or active\n     */\n    QuickPickSeparatorFocusReason[QuickPickSeparatorFocusReason[\"NONE\"] = 0] = \"NONE\";\n    /**\n     * Some item within this section is hovered\n     */\n    QuickPickSeparatorFocusReason[QuickPickSeparatorFocusReason[\"MOUSE_HOVER\"] = 1] = \"MOUSE_HOVER\";\n    /**\n     * Some item within this section is active\n     */\n    QuickPickSeparatorFocusReason[QuickPickSeparatorFocusReason[\"ACTIVE_ITEM\"] = 2] = \"ACTIVE_ITEM\";\n})(QuickPickSeparatorFocusReason || (QuickPickSeparatorFocusReason = {}));\nclass QuickPickSeparatorElement extends BaseQuickPickItemElement {\n    constructor(index, fireSeparatorButtonTriggered, separator) {\n        super(index, false, separator);\n        this.fireSeparatorButtonTriggered = fireSeparatorButtonTriggered;\n        this.separator = separator;\n        this.children = new Array();\n        /**\n         * If this item is >0, it means that there is some item in the list that is either:\n         * * hovered over\n         * * active\n         */\n        this.focusInsideSeparator = QuickPickSeparatorFocusReason.NONE;\n    }\n}\nclass QuickInputItemDelegate {\n    getHeight(element) {\n        if (element instanceof QuickPickSeparatorElement) {\n            return 30;\n        }\n        return element.saneDetail ? 44 : 22;\n    }\n    getTemplateId(element) {\n        if (element instanceof QuickPickItemElement) {\n            return QuickPickItemElementRenderer.ID;\n        }\n        else {\n            return QuickPickSeparatorElementRenderer.ID;\n        }\n    }\n}\nclass QuickInputAccessibilityProvider {\n    getWidgetAriaLabel() {\n        return localize('quickInput', \"Quick Input\");\n    }\n    getAriaLabel(element) {\n        return element.separator?.label\n            ? `${element.saneAriaLabel}, ${element.separator.label}`\n            : element.saneAriaLabel;\n    }\n    getWidgetRole() {\n        return 'listbox';\n    }\n    getRole(element) {\n        return element.hasCheckbox ? 'checkbox' : 'option';\n    }\n    isChecked(element) {\n        if (!element.hasCheckbox || !(element instanceof QuickPickItemElement)) {\n            return undefined;\n        }\n        return {\n            get value() { return element.checked; },\n            onDidChange: e => element.onChecked(() => e()),\n        };\n    }\n}\nclass BaseQuickInputListRenderer {\n    constructor(hoverDelegate) {\n        this.hoverDelegate = hoverDelegate;\n    }\n    // TODO: only do the common stuff here and have a subclass handle their specific stuff\n    renderTemplate(container) {\n        const data = Object.create(null);\n        data.toDisposeElement = new DisposableStore();\n        data.toDisposeTemplate = new DisposableStore();\n        data.entry = dom.append(container, $('.quick-input-list-entry'));\n        // Checkbox\n        const label = dom.append(data.entry, $('label.quick-input-list-label'));\n        data.toDisposeTemplate.add(dom.addStandardDisposableListener(label, dom.EventType.CLICK, e => {\n            if (!data.checkbox.offsetParent) { // If checkbox not visible:\n                e.preventDefault(); // Prevent toggle of checkbox when it is immediately shown afterwards. #91740\n            }\n        }));\n        data.checkbox = dom.append(label, $('input.quick-input-list-checkbox'));\n        data.checkbox.type = 'checkbox';\n        // Rows\n        const rows = dom.append(label, $('.quick-input-list-rows'));\n        const row1 = dom.append(rows, $('.quick-input-list-row'));\n        const row2 = dom.append(rows, $('.quick-input-list-row'));\n        // Label\n        data.label = new IconLabel(row1, { supportHighlights: true, supportDescriptionHighlights: true, supportIcons: true, hoverDelegate: this.hoverDelegate });\n        data.toDisposeTemplate.add(data.label);\n        data.icon = dom.prepend(data.label.element, $('.quick-input-list-icon'));\n        // Keybinding\n        const keybindingContainer = dom.append(row1, $('.quick-input-list-entry-keybinding'));\n        data.keybinding = new KeybindingLabel(keybindingContainer, OS);\n        data.toDisposeTemplate.add(data.keybinding);\n        // Detail\n        const detailContainer = dom.append(row2, $('.quick-input-list-label-meta'));\n        data.detail = new IconLabel(detailContainer, { supportHighlights: true, supportIcons: true, hoverDelegate: this.hoverDelegate });\n        data.toDisposeTemplate.add(data.detail);\n        // Separator\n        data.separator = dom.append(data.entry, $('.quick-input-list-separator'));\n        // Actions\n        data.actionBar = new ActionBar(data.entry, this.hoverDelegate ? { hoverDelegate: this.hoverDelegate } : undefined);\n        data.actionBar.domNode.classList.add('quick-input-list-entry-action-bar');\n        data.toDisposeTemplate.add(data.actionBar);\n        return data;\n    }\n    disposeTemplate(data) {\n        data.toDisposeElement.dispose();\n        data.toDisposeTemplate.dispose();\n    }\n    disposeElement(_element, _index, data) {\n        data.toDisposeElement.clear();\n        data.actionBar.clear();\n    }\n}\nlet QuickPickItemElementRenderer = class QuickPickItemElementRenderer extends BaseQuickInputListRenderer {\n    static { QuickPickItemElementRenderer_1 = this; }\n    static { this.ID = 'quickpickitem'; }\n    constructor(hoverDelegate, themeService) {\n        super(hoverDelegate);\n        this.themeService = themeService;\n        // Follow what we do in the separator renderer\n        this._itemsWithSeparatorsFrequency = new Map();\n    }\n    get templateId() {\n        return QuickPickItemElementRenderer_1.ID;\n    }\n    renderTemplate(container) {\n        const data = super.renderTemplate(container);\n        data.toDisposeTemplate.add(dom.addStandardDisposableListener(data.checkbox, dom.EventType.CHANGE, e => {\n            data.element.checked = data.checkbox.checked;\n        }));\n        return data;\n    }\n    renderElement(node, index, data) {\n        const element = node.element;\n        data.element = element;\n        element.element = data.entry ?? undefined;\n        const mainItem = element.item;\n        data.checkbox.checked = element.checked;\n        data.toDisposeElement.add(element.onChecked(checked => data.checkbox.checked = checked));\n        data.checkbox.disabled = element.checkboxDisabled;\n        const { labelHighlights, descriptionHighlights, detailHighlights } = element;\n        // Icon\n        if (mainItem.iconPath) {\n            const icon = isDark(this.themeService.getColorTheme().type) ? mainItem.iconPath.dark : (mainItem.iconPath.light ?? mainItem.iconPath.dark);\n            const iconUrl = URI.revive(icon);\n            data.icon.className = 'quick-input-list-icon';\n            data.icon.style.backgroundImage = dom.asCSSUrl(iconUrl);\n        }\n        else {\n            data.icon.style.backgroundImage = '';\n            data.icon.className = mainItem.iconClass ? `quick-input-list-icon ${mainItem.iconClass}` : '';\n        }\n        // Label\n        let descriptionTitle;\n        // if we have a tooltip, that will be the hover,\n        // with the saneDescription as fallback if it\n        // is defined\n        if (!element.saneTooltip && element.saneDescription) {\n            descriptionTitle = {\n                markdown: {\n                    value: element.saneDescription,\n                    supportThemeIcons: true\n                },\n                markdownNotSupportedFallback: element.saneDescription\n            };\n        }\n        const options = {\n            matches: labelHighlights || [],\n            // If we have a tooltip, we want that to be shown and not any other hover\n            descriptionTitle,\n            descriptionMatches: descriptionHighlights || [],\n            labelEscapeNewLines: true\n        };\n        options.extraClasses = mainItem.iconClasses;\n        options.italic = mainItem.italic;\n        options.strikethrough = mainItem.strikethrough;\n        data.entry.classList.remove('quick-input-list-separator-as-item');\n        data.label.setLabel(element.saneLabel, element.saneDescription, options);\n        // Keybinding\n        data.keybinding.set(mainItem.keybinding);\n        // Detail\n        if (element.saneDetail) {\n            let title;\n            // If we have a tooltip, we want that to be shown and not any other hover\n            if (!element.saneTooltip) {\n                title = {\n                    markdown: {\n                        value: element.saneDetail,\n                        supportThemeIcons: true\n                    },\n                    markdownNotSupportedFallback: element.saneDetail\n                };\n            }\n            data.detail.element.style.display = '';\n            data.detail.setLabel(element.saneDetail, undefined, {\n                matches: detailHighlights,\n                title,\n                labelEscapeNewLines: true\n            });\n        }\n        else {\n            data.detail.element.style.display = 'none';\n        }\n        // Separator\n        if (element.separator?.label) {\n            data.separator.textContent = element.separator.label;\n            data.separator.style.display = '';\n            this.addItemWithSeparator(element);\n        }\n        else {\n            data.separator.style.display = 'none';\n        }\n        data.entry.classList.toggle('quick-input-list-separator-border', !!element.separator);\n        // Actions\n        const buttons = mainItem.buttons;\n        if (buttons && buttons.length) {\n            data.actionBar.push(buttons.map((button, index) => quickInputButtonToAction(button, `id-${index}`, () => element.fireButtonTriggered({ button, item: element.item }))), { icon: true, label: false });\n            data.entry.classList.add('has-actions');\n        }\n        else {\n            data.entry.classList.remove('has-actions');\n        }\n    }\n    disposeElement(element, _index, data) {\n        this.removeItemWithSeparator(element.element);\n        super.disposeElement(element, _index, data);\n    }\n    isItemWithSeparatorVisible(item) {\n        return this._itemsWithSeparatorsFrequency.has(item);\n    }\n    addItemWithSeparator(item) {\n        this._itemsWithSeparatorsFrequency.set(item, (this._itemsWithSeparatorsFrequency.get(item) || 0) + 1);\n    }\n    removeItemWithSeparator(item) {\n        const frequency = this._itemsWithSeparatorsFrequency.get(item) || 0;\n        if (frequency > 1) {\n            this._itemsWithSeparatorsFrequency.set(item, frequency - 1);\n        }\n        else {\n            this._itemsWithSeparatorsFrequency.delete(item);\n        }\n    }\n};\nQuickPickItemElementRenderer = QuickPickItemElementRenderer_1 = __decorate([\n    __param(1, IThemeService)\n], QuickPickItemElementRenderer);\nclass QuickPickSeparatorElementRenderer extends BaseQuickInputListRenderer {\n    constructor() {\n        super(...arguments);\n        // This is a frequency map because sticky scroll re-uses the same renderer to render a second\n        // instance of the same separator.\n        this._visibleSeparatorsFrequency = new Map();\n    }\n    static { this.ID = 'quickpickseparator'; }\n    get templateId() {\n        return QuickPickSeparatorElementRenderer.ID;\n    }\n    get visibleSeparators() {\n        return [...this._visibleSeparatorsFrequency.keys()];\n    }\n    isSeparatorVisible(separator) {\n        return this._visibleSeparatorsFrequency.has(separator);\n    }\n    renderTemplate(container) {\n        const data = super.renderTemplate(container);\n        data.checkbox.style.display = 'none';\n        return data;\n    }\n    renderElement(node, index, data) {\n        const element = node.element;\n        data.element = element;\n        element.element = data.entry ?? undefined;\n        element.element.classList.toggle('focus-inside', !!element.focusInsideSeparator);\n        const mainItem = element.separator;\n        const { labelHighlights, descriptionHighlights, detailHighlights } = element;\n        // Icon\n        data.icon.style.backgroundImage = '';\n        data.icon.className = '';\n        // Label\n        let descriptionTitle;\n        // if we have a tooltip, that will be the hover,\n        // with the saneDescription as fallback if it\n        // is defined\n        if (!element.saneTooltip && element.saneDescription) {\n            descriptionTitle = {\n                markdown: {\n                    value: element.saneDescription,\n                    supportThemeIcons: true\n                },\n                markdownNotSupportedFallback: element.saneDescription\n            };\n        }\n        const options = {\n            matches: labelHighlights || [],\n            // If we have a tooltip, we want that to be shown and not any other hover\n            descriptionTitle,\n            descriptionMatches: descriptionHighlights || [],\n            labelEscapeNewLines: true\n        };\n        data.entry.classList.add('quick-input-list-separator-as-item');\n        data.label.setLabel(element.saneLabel, element.saneDescription, options);\n        // Detail\n        if (element.saneDetail) {\n            let title;\n            // If we have a tooltip, we want that to be shown and not any other hover\n            if (!element.saneTooltip) {\n                title = {\n                    markdown: {\n                        value: element.saneDetail,\n                        supportThemeIcons: true\n                    },\n                    markdownNotSupportedFallback: element.saneDetail\n                };\n            }\n            data.detail.element.style.display = '';\n            data.detail.setLabel(element.saneDetail, undefined, {\n                matches: detailHighlights,\n                title,\n                labelEscapeNewLines: true\n            });\n        }\n        else {\n            data.detail.element.style.display = 'none';\n        }\n        // Separator\n        data.separator.style.display = 'none';\n        data.entry.classList.add('quick-input-list-separator-border');\n        // Actions\n        const buttons = mainItem.buttons;\n        if (buttons && buttons.length) {\n            data.actionBar.push(buttons.map((button, index) => quickInputButtonToAction(button, `id-${index}`, () => element.fireSeparatorButtonTriggered({ button, separator: element.separator }))), { icon: true, label: false });\n            data.entry.classList.add('has-actions');\n        }\n        else {\n            data.entry.classList.remove('has-actions');\n        }\n        this.addSeparator(element);\n    }\n    disposeElement(element, _index, data) {\n        this.removeSeparator(element.element);\n        if (!this.isSeparatorVisible(element.element)) {\n            element.element.element?.classList.remove('focus-inside');\n        }\n        super.disposeElement(element, _index, data);\n    }\n    addSeparator(separator) {\n        this._visibleSeparatorsFrequency.set(separator, (this._visibleSeparatorsFrequency.get(separator) || 0) + 1);\n    }\n    removeSeparator(separator) {\n        const frequency = this._visibleSeparatorsFrequency.get(separator) || 0;\n        if (frequency > 1) {\n            this._visibleSeparatorsFrequency.set(separator, frequency - 1);\n        }\n        else {\n            this._visibleSeparatorsFrequency.delete(separator);\n        }\n    }\n}\nlet QuickInputTree = class QuickInputTree extends Disposable {\n    constructor(parent, hoverDelegate, linkOpenerDelegate, id, instantiationService, accessibilityService) {\n        super();\n        this.parent = parent;\n        this.hoverDelegate = hoverDelegate;\n        this.linkOpenerDelegate = linkOpenerDelegate;\n        this.accessibilityService = accessibilityService;\n        //#region QuickInputTree Events\n        this._onKeyDown = new Emitter();\n        this._onLeave = new Emitter();\n        /**\n         * Event that is fired when the tree would no longer have focus.\n        */\n        this.onLeave = this._onLeave.event;\n        this._visibleCountObservable = observableValue('VisibleCount', 0);\n        this.onChangedVisibleCount = Event.fromObservable(this._visibleCountObservable, this._store);\n        this._allVisibleCheckedObservable = observableValue('AllVisibleChecked', false);\n        this.onChangedAllVisibleChecked = Event.fromObservable(this._allVisibleCheckedObservable, this._store);\n        this._checkedCountObservable = observableValue('CheckedCount', 0);\n        this.onChangedCheckedCount = Event.fromObservable(this._checkedCountObservable, this._store);\n        this._checkedElementsObservable = observableValueOpts({ equalsFn: equals }, new Array());\n        this.onChangedCheckedElements = Event.fromObservable(this._checkedElementsObservable, this._store);\n        this._onButtonTriggered = new Emitter();\n        this.onButtonTriggered = this._onButtonTriggered.event;\n        this._onSeparatorButtonTriggered = new Emitter();\n        this.onSeparatorButtonTriggered = this._onSeparatorButtonTriggered.event;\n        this._elementChecked = new Emitter();\n        this._elementCheckedEventBufferer = new EventBufferer();\n        //#endregion\n        this._hasCheckboxes = false;\n        this._inputElements = new Array();\n        this._elementTree = new Array();\n        this._itemElements = new Array();\n        // Elements that apply to the current set of elements\n        this._elementDisposable = this._register(new DisposableStore());\n        this._matchOnDescription = false;\n        this._matchOnDetail = false;\n        this._matchOnLabel = true;\n        this._matchOnLabelMode = 'fuzzy';\n        this._sortByLabel = true;\n        this._shouldLoop = true;\n        this._container = dom.append(this.parent, $('.quick-input-list'));\n        this._separatorRenderer = new QuickPickSeparatorElementRenderer(hoverDelegate);\n        this._itemRenderer = instantiationService.createInstance(QuickPickItemElementRenderer, hoverDelegate);\n        this._tree = this._register(instantiationService.createInstance((WorkbenchObjectTree), 'QuickInput', this._container, new QuickInputItemDelegate(), [this._itemRenderer, this._separatorRenderer], {\n            filter: {\n                filter(element) {\n                    return element.hidden\n                        ? 0 /* TreeVisibility.Hidden */\n                        : element instanceof QuickPickSeparatorElement\n                            ? 2 /* TreeVisibility.Recurse */\n                            : 1 /* TreeVisibility.Visible */;\n                },\n            },\n            sorter: {\n                compare: (element, otherElement) => {\n                    if (!this.sortByLabel || !this._lastQueryString) {\n                        return 0;\n                    }\n                    const normalizedSearchValue = this._lastQueryString.toLowerCase();\n                    return compareEntries(element, otherElement, normalizedSearchValue);\n                },\n            },\n            accessibilityProvider: new QuickInputAccessibilityProvider(),\n            setRowLineHeight: false,\n            multipleSelectionSupport: false,\n            hideTwistiesOfChildlessElements: true,\n            renderIndentGuides: RenderIndentGuides.None,\n            findWidgetEnabled: false,\n            indent: 0,\n            horizontalScrolling: false,\n            allowNonCollapsibleParents: true,\n            alwaysConsumeMouseWheel: true\n        }));\n        this._tree.getHTMLElement().id = id;\n        this._registerListeners();\n    }\n    //#region public getters/setters\n    get onDidChangeFocus() {\n        return Event.map(this._tree.onDidChangeFocus, e => e.elements.filter((e) => e instanceof QuickPickItemElement).map(e => e.item), this._store);\n    }\n    get onDidChangeSelection() {\n        return Event.map(this._tree.onDidChangeSelection, e => ({\n            items: e.elements.filter((e) => e instanceof QuickPickItemElement).map(e => e.item),\n            event: e.browserEvent\n        }), this._store);\n    }\n    get displayed() {\n        return this._container.style.display !== 'none';\n    }\n    set displayed(value) {\n        this._container.style.display = value ? '' : 'none';\n    }\n    get scrollTop() {\n        return this._tree.scrollTop;\n    }\n    set scrollTop(scrollTop) {\n        this._tree.scrollTop = scrollTop;\n    }\n    get ariaLabel() {\n        return this._tree.ariaLabel;\n    }\n    set ariaLabel(label) {\n        this._tree.ariaLabel = label ?? '';\n    }\n    set enabled(value) {\n        this._tree.getHTMLElement().style.pointerEvents = value ? '' : 'none';\n    }\n    get matchOnDescription() {\n        return this._matchOnDescription;\n    }\n    set matchOnDescription(value) {\n        this._matchOnDescription = value;\n    }\n    get matchOnDetail() {\n        return this._matchOnDetail;\n    }\n    set matchOnDetail(value) {\n        this._matchOnDetail = value;\n    }\n    get matchOnLabel() {\n        return this._matchOnLabel;\n    }\n    set matchOnLabel(value) {\n        this._matchOnLabel = value;\n    }\n    get matchOnLabelMode() {\n        return this._matchOnLabelMode;\n    }\n    set matchOnLabelMode(value) {\n        this._matchOnLabelMode = value;\n    }\n    get sortByLabel() {\n        return this._sortByLabel;\n    }\n    set sortByLabel(value) {\n        this._sortByLabel = value;\n    }\n    get shouldLoop() {\n        return this._shouldLoop;\n    }\n    set shouldLoop(value) {\n        this._shouldLoop = value;\n    }\n    //#endregion\n    //#region register listeners\n    _registerListeners() {\n        this._registerOnKeyDown();\n        this._registerOnContainerClick();\n        this._registerOnMouseMiddleClick();\n        this._registerOnTreeModelChanged();\n        this._registerOnElementChecked();\n        this._registerOnContextMenu();\n        this._registerHoverListeners();\n        this._registerSelectionChangeListener();\n        this._registerSeparatorActionShowingListeners();\n    }\n    _registerOnKeyDown() {\n        // TODO: Should this be added at a higher level?\n        this._register(this._tree.onKeyDown(e => {\n            const event = new StandardKeyboardEvent(e);\n            switch (event.keyCode) {\n                case 10 /* KeyCode.Space */:\n                    this.toggleCheckbox();\n                    break;\n            }\n            this._onKeyDown.fire(event);\n        }));\n    }\n    _registerOnContainerClick() {\n        this._register(dom.addDisposableListener(this._container, dom.EventType.CLICK, e => {\n            if (e.x || e.y) { // Avoid 'click' triggered by 'space' on checkbox.\n                this._onLeave.fire();\n            }\n        }));\n    }\n    _registerOnMouseMiddleClick() {\n        this._register(dom.addDisposableListener(this._container, dom.EventType.AUXCLICK, e => {\n            if (e.button === 1) {\n                this._onLeave.fire();\n            }\n        }));\n    }\n    _registerOnTreeModelChanged() {\n        this._register(this._tree.onDidChangeModel(() => {\n            const visibleCount = this._itemElements.filter(e => !e.hidden).length;\n            this._visibleCountObservable.set(visibleCount, undefined);\n            if (this._hasCheckboxes) {\n                this._updateCheckedObservables();\n            }\n        }));\n    }\n    _registerOnElementChecked() {\n        // Only fire the last event when buffered\n        this._register(this._elementCheckedEventBufferer.wrapEvent(this._elementChecked.event, (_, e) => e)(_ => this._updateCheckedObservables()));\n    }\n    _registerOnContextMenu() {\n        this._register(this._tree.onContextMenu(e => {\n            if (e.element) {\n                e.browserEvent.preventDefault();\n                // we want to treat a context menu event as\n                // a gesture to open the item at the index\n                // since we do not have any context menu\n                // this enables for example macOS to Ctrl-\n                // click on an item to open it.\n                this._tree.setSelection([e.element]);\n            }\n        }));\n    }\n    _registerHoverListeners() {\n        const delayer = this._register(new ThrottledDelayer(this.hoverDelegate.delay));\n        this._register(this._tree.onMouseOver(async (e) => {\n            // If we hover over an anchor element, we don't want to show the hover because\n            // the anchor may have a tooltip that we want to show instead.\n            if (dom.isHTMLAnchorElement(e.browserEvent.target)) {\n                delayer.cancel();\n                return;\n            }\n            if (\n            // anchors are an exception as called out above so we skip them here\n            !(dom.isHTMLAnchorElement(e.browserEvent.relatedTarget)) &&\n                // check if the mouse is still over the same element\n                dom.isAncestor(e.browserEvent.relatedTarget, e.element?.element)) {\n                return;\n            }\n            try {\n                await delayer.trigger(async () => {\n                    if (e.element instanceof QuickPickItemElement) {\n                        this.showHover(e.element);\n                    }\n                });\n            }\n            catch (e) {\n                // Ignore cancellation errors due to mouse out\n                if (!isCancellationError(e)) {\n                    throw e;\n                }\n            }\n        }));\n        this._register(this._tree.onMouseOut(e => {\n            // onMouseOut triggers every time a new element has been moused over\n            // even if it's on the same list item. We only want one event, so we\n            // check if the mouse is still over the same element.\n            if (dom.isAncestor(e.browserEvent.relatedTarget, e.element?.element)) {\n                return;\n            }\n            delayer.cancel();\n        }));\n    }\n    /**\n     * Register's focus change and mouse events so that we can track when items inside of a\n     * separator's section are focused or hovered so that we can display the separator's actions\n     */\n    _registerSeparatorActionShowingListeners() {\n        this._register(this._tree.onDidChangeFocus(e => {\n            const parent = e.elements[0]\n                ? this._tree.getParentElement(e.elements[0])\n                // treat null as focus lost and when we have no separators\n                : null;\n            for (const separator of this._separatorRenderer.visibleSeparators) {\n                const value = separator === parent;\n                // get bitness of ACTIVE_ITEM and check if it changed\n                const currentActive = !!(separator.focusInsideSeparator & QuickPickSeparatorFocusReason.ACTIVE_ITEM);\n                if (currentActive !== value) {\n                    if (value) {\n                        separator.focusInsideSeparator |= QuickPickSeparatorFocusReason.ACTIVE_ITEM;\n                    }\n                    else {\n                        separator.focusInsideSeparator &= ~QuickPickSeparatorFocusReason.ACTIVE_ITEM;\n                    }\n                    this._tree.rerender(separator);\n                }\n            }\n        }));\n        this._register(this._tree.onMouseOver(e => {\n            const parent = e.element\n                ? this._tree.getParentElement(e.element)\n                : null;\n            for (const separator of this._separatorRenderer.visibleSeparators) {\n                if (separator !== parent) {\n                    continue;\n                }\n                const currentMouse = !!(separator.focusInsideSeparator & QuickPickSeparatorFocusReason.MOUSE_HOVER);\n                if (!currentMouse) {\n                    separator.focusInsideSeparator |= QuickPickSeparatorFocusReason.MOUSE_HOVER;\n                    this._tree.rerender(separator);\n                }\n            }\n        }));\n        this._register(this._tree.onMouseOut(e => {\n            const parent = e.element\n                ? this._tree.getParentElement(e.element)\n                : null;\n            for (const separator of this._separatorRenderer.visibleSeparators) {\n                if (separator !== parent) {\n                    continue;\n                }\n                const currentMouse = !!(separator.focusInsideSeparator & QuickPickSeparatorFocusReason.MOUSE_HOVER);\n                if (currentMouse) {\n                    separator.focusInsideSeparator &= ~QuickPickSeparatorFocusReason.MOUSE_HOVER;\n                    this._tree.rerender(separator);\n                }\n            }\n        }));\n    }\n    _registerSelectionChangeListener() {\n        // When the user selects a separator, the separator will move to the top and focus will be\n        // set to the first element after the separator.\n        this._register(this._tree.onDidChangeSelection(e => {\n            const elementsWithoutSeparators = e.elements.filter((e) => e instanceof QuickPickItemElement);\n            if (elementsWithoutSeparators.length !== e.elements.length) {\n                if (e.elements.length === 1 && e.elements[0] instanceof QuickPickSeparatorElement) {\n                    this._tree.setFocus([e.elements[0].children[0]]);\n                    this._tree.reveal(e.elements[0], 0);\n                }\n                this._tree.setSelection(elementsWithoutSeparators);\n            }\n        }));\n    }\n    //#endregion\n    //#region public methods\n    setAllVisibleChecked(checked) {\n        this._elementCheckedEventBufferer.bufferEvents(() => {\n            this._itemElements.forEach(element => {\n                if (!element.hidden && !element.checkboxDisabled) {\n                    // Would fire an event if we didn't beffer the events\n                    element.checked = checked;\n                }\n            });\n        });\n    }\n    setElements(inputElements) {\n        this._elementDisposable.clear();\n        this._lastQueryString = undefined;\n        this._inputElements = inputElements;\n        this._hasCheckboxes = this.parent.classList.contains('show-checkboxes');\n        let currentSeparatorElement;\n        this._itemElements = new Array();\n        this._elementTree = inputElements.reduce((result, item, index) => {\n            let element;\n            if (item.type === 'separator') {\n                if (!item.buttons) {\n                    // This separator will be rendered as a part of the list item\n                    return result;\n                }\n                currentSeparatorElement = new QuickPickSeparatorElement(index, e => this._onSeparatorButtonTriggered.fire(e), item);\n                element = currentSeparatorElement;\n            }\n            else {\n                const previous = index > 0 ? inputElements[index - 1] : undefined;\n                let separator;\n                if (previous && previous.type === 'separator' && !previous.buttons) {\n                    // Found an inline separator so we clear out the current separator element\n                    currentSeparatorElement = undefined;\n                    separator = previous;\n                }\n                const qpi = new QuickPickItemElement(index, this._hasCheckboxes, e => this._onButtonTriggered.fire(e), this._elementChecked, item, separator);\n                this._itemElements.push(qpi);\n                if (currentSeparatorElement) {\n                    currentSeparatorElement.children.push(qpi);\n                    return result;\n                }\n                element = qpi;\n            }\n            result.push(element);\n            return result;\n        }, new Array());\n        this._setElementsToTree(this._elementTree);\n        // Accessibility hack, unfortunately on next tick\n        // https://github.com/microsoft/vscode/issues/211976\n        if (this.accessibilityService.isScreenReaderOptimized()) {\n            setTimeout(() => {\n                const focusedElement = this._tree.getHTMLElement().querySelector(`.monaco-list-row.focused`);\n                const parent = focusedElement?.parentNode;\n                if (focusedElement && parent) {\n                    const nextSibling = focusedElement.nextSibling;\n                    focusedElement.remove();\n                    parent.insertBefore(focusedElement, nextSibling);\n                }\n            }, 0);\n        }\n    }\n    setFocusedElements(items) {\n        const elements = items.map(item => this._itemElements.find(e => e.item === item))\n            .filter((e) => !!e)\n            .filter(e => !e.hidden);\n        this._tree.setFocus(elements);\n        if (items.length > 0) {\n            const focused = this._tree.getFocus()[0];\n            if (focused) {\n                this._tree.reveal(focused);\n            }\n        }\n    }\n    getActiveDescendant() {\n        return this._tree.getHTMLElement().getAttribute('aria-activedescendant');\n    }\n    setSelectedElements(items) {\n        const elements = items.map(item => this._itemElements.find(e => e.item === item))\n            .filter((e) => !!e);\n        this._tree.setSelection(elements);\n    }\n    getCheckedElements() {\n        return this._itemElements.filter(e => e.checked)\n            .map(e => e.item);\n    }\n    setCheckedElements(items) {\n        this._elementCheckedEventBufferer.bufferEvents(() => {\n            const checked = new Set();\n            for (const item of items) {\n                checked.add(item);\n            }\n            for (const element of this._itemElements) {\n                // Would fire an event if we didn't beffer the events\n                element.checked = checked.has(element.item);\n            }\n        });\n    }\n    focus(what) {\n        if (!this._itemElements.length) {\n            return;\n        }\n        if (what === QuickPickFocus.Second && this._itemElements.length < 2) {\n            what = QuickPickFocus.First;\n        }\n        switch (what) {\n            case QuickPickFocus.First:\n                this._tree.scrollTop = 0;\n                this._tree.focusFirst(undefined, (e) => e.element instanceof QuickPickItemElement);\n                break;\n            case QuickPickFocus.Second: {\n                this._tree.scrollTop = 0;\n                let isSecondItem = false;\n                this._tree.focusFirst(undefined, (e) => {\n                    if (!(e.element instanceof QuickPickItemElement)) {\n                        return false;\n                    }\n                    if (isSecondItem) {\n                        return true;\n                    }\n                    isSecondItem = !isSecondItem;\n                    return false;\n                });\n                break;\n            }\n            case QuickPickFocus.Last:\n                this._tree.scrollTop = this._tree.scrollHeight;\n                this._tree.focusLast(undefined, (e) => e.element instanceof QuickPickItemElement);\n                break;\n            case QuickPickFocus.Next: {\n                const prevFocus = this._tree.getFocus();\n                this._tree.focusNext(undefined, this._shouldLoop, undefined, (e) => {\n                    if (!(e.element instanceof QuickPickItemElement)) {\n                        return false;\n                    }\n                    this._tree.reveal(e.element);\n                    return true;\n                });\n                const currentFocus = this._tree.getFocus();\n                if (prevFocus.length && prevFocus[0] === currentFocus[0] && prevFocus[0] === this._itemElements[this._itemElements.length - 1]) {\n                    this._onLeave.fire();\n                }\n                break;\n            }\n            case QuickPickFocus.Previous: {\n                const prevFocus = this._tree.getFocus();\n                this._tree.focusPrevious(undefined, this._shouldLoop, undefined, (e) => {\n                    if (!(e.element instanceof QuickPickItemElement)) {\n                        return false;\n                    }\n                    const parent = this._tree.getParentElement(e.element);\n                    if (parent === null || parent.children[0] !== e.element) {\n                        this._tree.reveal(e.element);\n                    }\n                    else {\n                        // Only if we are the first child of a separator do we reveal the separator\n                        this._tree.reveal(parent);\n                    }\n                    return true;\n                });\n                const currentFocus = this._tree.getFocus();\n                if (prevFocus.length && prevFocus[0] === currentFocus[0] && prevFocus[0] === this._itemElements[0]) {\n                    this._onLeave.fire();\n                }\n                break;\n            }\n            case QuickPickFocus.NextPage:\n                this._tree.focusNextPage(undefined, (e) => {\n                    if (!(e.element instanceof QuickPickItemElement)) {\n                        return false;\n                    }\n                    this._tree.reveal(e.element);\n                    return true;\n                });\n                break;\n            case QuickPickFocus.PreviousPage:\n                this._tree.focusPreviousPage(undefined, (e) => {\n                    if (!(e.element instanceof QuickPickItemElement)) {\n                        return false;\n                    }\n                    const parent = this._tree.getParentElement(e.element);\n                    if (parent === null || parent.children[0] !== e.element) {\n                        this._tree.reveal(e.element);\n                    }\n                    else {\n                        this._tree.reveal(parent);\n                    }\n                    return true;\n                });\n                break;\n            case QuickPickFocus.NextSeparator: {\n                let foundSeparatorAsItem = false;\n                const before = this._tree.getFocus()[0];\n                this._tree.focusNext(undefined, true, undefined, (e) => {\n                    if (foundSeparatorAsItem) {\n                        // This should be the index right after the separator so it\n                        // is the item we want to focus.\n                        return true;\n                    }\n                    if (e.element instanceof QuickPickSeparatorElement) {\n                        foundSeparatorAsItem = true;\n                        // If the separator is visible, then we should just reveal its first child so it's not as jarring.\n                        if (this._separatorRenderer.isSeparatorVisible(e.element)) {\n                            this._tree.reveal(e.element.children[0]);\n                        }\n                        else {\n                            // If the separator is not visible, then we should\n                            // push it up to the top of the list.\n                            this._tree.reveal(e.element, 0);\n                        }\n                    }\n                    else if (e.element instanceof QuickPickItemElement) {\n                        if (e.element.separator) {\n                            if (this._itemRenderer.isItemWithSeparatorVisible(e.element)) {\n                                this._tree.reveal(e.element);\n                            }\n                            else {\n                                this._tree.reveal(e.element, 0);\n                            }\n                            return true;\n                        }\n                        else if (e.element === this._elementTree[0]) {\n                            // We should stop at the first item in the list if it's a regular item.\n                            this._tree.reveal(e.element, 0);\n                            return true;\n                        }\n                    }\n                    return false;\n                });\n                const after = this._tree.getFocus()[0];\n                if (before === after) {\n                    // If we didn't move, then we should just move to the end\n                    // of the list.\n                    this._tree.scrollTop = this._tree.scrollHeight;\n                    this._tree.focusLast(undefined, (e) => e.element instanceof QuickPickItemElement);\n                }\n                break;\n            }\n            case QuickPickFocus.PreviousSeparator: {\n                let focusElement;\n                // If we are already sitting on an inline separator, then we\n                // have already found the _current_ separator and need to\n                // move to the previous one.\n                let foundSeparator = !!this._tree.getFocus()[0]?.separator;\n                this._tree.focusPrevious(undefined, true, undefined, (e) => {\n                    if (e.element instanceof QuickPickSeparatorElement) {\n                        if (foundSeparator) {\n                            if (!focusElement) {\n                                if (this._separatorRenderer.isSeparatorVisible(e.element)) {\n                                    this._tree.reveal(e.element);\n                                }\n                                else {\n                                    this._tree.reveal(e.element, 0);\n                                }\n                                focusElement = e.element.children[0];\n                            }\n                        }\n                        else {\n                            foundSeparator = true;\n                        }\n                    }\n                    else if (e.element instanceof QuickPickItemElement) {\n                        if (!focusElement) {\n                            if (e.element.separator) {\n                                if (this._itemRenderer.isItemWithSeparatorVisible(e.element)) {\n                                    this._tree.reveal(e.element);\n                                }\n                                else {\n                                    this._tree.reveal(e.element, 0);\n                                }\n                                focusElement = e.element;\n                            }\n                            else if (e.element === this._elementTree[0]) {\n                                // We should stop at the first item in the list if it's a regular item.\n                                this._tree.reveal(e.element, 0);\n                                return true;\n                            }\n                        }\n                    }\n                    return false;\n                });\n                if (focusElement) {\n                    this._tree.setFocus([focusElement]);\n                }\n                break;\n            }\n        }\n    }\n    clearFocus() {\n        this._tree.setFocus([]);\n    }\n    domFocus() {\n        this._tree.domFocus();\n    }\n    layout(maxHeight) {\n        this._tree.getHTMLElement().style.maxHeight = maxHeight ? `${\n        // Make sure height aligns with list item heights\n        Math.floor(maxHeight / 44) * 44\n            // Add some extra height so that it's clear there's more to scroll\n            + 6}px` : '';\n        this._tree.layout();\n    }\n    filter(query) {\n        this._lastQueryString = query;\n        if (!(this._sortByLabel || this._matchOnLabel || this._matchOnDescription || this._matchOnDetail)) {\n            this._tree.layout();\n            return false;\n        }\n        const queryWithWhitespace = query;\n        query = query.trim();\n        // Reset filtering\n        if (!query || !(this.matchOnLabel || this.matchOnDescription || this.matchOnDetail)) {\n            this._itemElements.forEach(element => {\n                element.labelHighlights = undefined;\n                element.descriptionHighlights = undefined;\n                element.detailHighlights = undefined;\n                element.hidden = false;\n                const previous = element.index && this._inputElements[element.index - 1];\n                if (element.item) {\n                    element.separator = previous && previous.type === 'separator' && !previous.buttons ? previous : undefined;\n                }\n            });\n        }\n        // Filter by value (since we support icons in labels, use $(..) aware fuzzy matching)\n        else {\n            let currentSeparator;\n            this._itemElements.forEach(element => {\n                let labelHighlights;\n                if (this.matchOnLabelMode === 'fuzzy') {\n                    labelHighlights = this.matchOnLabel ? matchesFuzzyIconAware(query, parseLabelWithIcons(element.saneLabel)) ?? undefined : undefined;\n                }\n                else {\n                    labelHighlights = this.matchOnLabel ? matchesContiguousIconAware(queryWithWhitespace, parseLabelWithIcons(element.saneLabel)) ?? undefined : undefined;\n                }\n                const descriptionHighlights = this.matchOnDescription ? matchesFuzzyIconAware(query, parseLabelWithIcons(element.saneDescription || '')) ?? undefined : undefined;\n                const detailHighlights = this.matchOnDetail ? matchesFuzzyIconAware(query, parseLabelWithIcons(element.saneDetail || '')) ?? undefined : undefined;\n                if (labelHighlights || descriptionHighlights || detailHighlights) {\n                    element.labelHighlights = labelHighlights;\n                    element.descriptionHighlights = descriptionHighlights;\n                    element.detailHighlights = detailHighlights;\n                    element.hidden = false;\n                }\n                else {\n                    element.labelHighlights = undefined;\n                    element.descriptionHighlights = undefined;\n                    element.detailHighlights = undefined;\n                    element.hidden = element.item ? !element.item.alwaysShow : true;\n                }\n                // Ensure separators are filtered out first before deciding if we need to bring them back\n                if (element.item) {\n                    element.separator = undefined;\n                }\n                else if (element.separator) {\n                    element.hidden = true;\n                }\n                // we can show the separator unless the list gets sorted by match\n                if (!this.sortByLabel) {\n                    const previous = element.index && this._inputElements[element.index - 1] || undefined;\n                    if (previous?.type === 'separator' && !previous.buttons) {\n                        currentSeparator = previous;\n                    }\n                    if (currentSeparator && !element.hidden) {\n                        element.separator = currentSeparator;\n                        currentSeparator = undefined;\n                    }\n                }\n            });\n        }\n        this._setElementsToTree(this._sortByLabel && query\n            // We don't render any separators if we're sorting so just render the elements\n            ? this._itemElements\n            // Render the full tree\n            : this._elementTree);\n        this._tree.layout();\n        return true;\n    }\n    toggleCheckbox() {\n        this._elementCheckedEventBufferer.bufferEvents(() => {\n            const elements = this._tree.getFocus().filter((e) => e instanceof QuickPickItemElement);\n            const allChecked = this._allVisibleChecked(elements);\n            for (const element of elements) {\n                if (!element.checkboxDisabled) {\n                    // Would fire an event if we didn't have the flag set\n                    element.checked = !allChecked;\n                }\n            }\n        });\n    }\n    style(styles) {\n        this._tree.style(styles);\n    }\n    toggleHover() {\n        const focused = this._tree.getFocus()[0];\n        if (!focused?.saneTooltip || !(focused instanceof QuickPickItemElement)) {\n            return;\n        }\n        // if there's a hover already, hide it (toggle off)\n        if (this._lastHover && !this._lastHover.isDisposed) {\n            this._lastHover.dispose();\n            return;\n        }\n        // If there is no hover, show it (toggle on)\n        this.showHover(focused);\n        const store = new DisposableStore();\n        store.add(this._tree.onDidChangeFocus(e => {\n            if (e.elements[0] instanceof QuickPickItemElement) {\n                this.showHover(e.elements[0]);\n            }\n        }));\n        if (this._lastHover) {\n            store.add(this._lastHover);\n        }\n        this._elementDisposable.add(store);\n    }\n    //#endregion\n    //#region private methods\n    _setElementsToTree(elements) {\n        const treeElements = new Array();\n        for (const element of elements) {\n            if (element instanceof QuickPickSeparatorElement) {\n                treeElements.push({\n                    element,\n                    collapsible: false,\n                    collapsed: false,\n                    children: element.children.map(e => ({\n                        element: e,\n                        collapsible: false,\n                        collapsed: false,\n                    })),\n                });\n            }\n            else {\n                treeElements.push({\n                    element,\n                    collapsible: false,\n                    collapsed: false,\n                });\n            }\n        }\n        this._tree.setChildren(null, treeElements);\n    }\n    _allVisibleChecked(elements, whenNoneVisible = true) {\n        for (let i = 0, n = elements.length; i < n; i++) {\n            const element = elements[i];\n            if (!element.hidden) {\n                if (!element.checked) {\n                    return false;\n                }\n                else {\n                    whenNoneVisible = true;\n                }\n            }\n        }\n        return whenNoneVisible;\n    }\n    _updateCheckedObservables() {\n        transaction((tx) => {\n            this._allVisibleCheckedObservable.set(this._allVisibleChecked(this._itemElements, false), tx);\n            const checkedCount = this._itemElements.filter(element => element.checked).length;\n            this._checkedCountObservable.set(checkedCount, tx);\n            this._checkedElementsObservable.set(this.getCheckedElements(), tx);\n        });\n    }\n    /**\n     * Disposes of the hover and shows a new one for the given index if it has a tooltip.\n     * @param element The element to show the hover for\n     */\n    showHover(element) {\n        if (this._lastHover && !this._lastHover.isDisposed) {\n            this.hoverDelegate.onDidHideHover?.();\n            this._lastHover?.dispose();\n        }\n        if (!element.element || !element.saneTooltip) {\n            return;\n        }\n        this._lastHover = this.hoverDelegate.showHover({\n            content: element.saneTooltip,\n            target: element.element,\n            linkHandler: (url) => {\n                this.linkOpenerDelegate(url);\n            },\n            appearance: {\n                showPointer: true,\n            },\n            container: this._container,\n            position: {\n                hoverPosition: 1 /* HoverPosition.RIGHT */\n            }\n        }, false);\n    }\n};\n__decorate([\n    memoize\n], QuickInputTree.prototype, \"onDidChangeFocus\", null);\n__decorate([\n    memoize\n], QuickInputTree.prototype, \"onDidChangeSelection\", null);\nQuickInputTree = __decorate([\n    __param(4, IInstantiationService),\n    __param(5, IAccessibilityService)\n], QuickInputTree);\nexport { QuickInputTree };\nfunction matchesContiguousIconAware(query, target) {\n    const { text, iconOffsets } = target;\n    // Return early if there are no icon markers in the word to match against\n    if (!iconOffsets || iconOffsets.length === 0) {\n        return matchesContiguous(query, text);\n    }\n    // Trim the word to match against because it could have leading\n    // whitespace now if the word started with an icon\n    const wordToMatchAgainstWithoutIconsTrimmed = ltrim(text, ' ');\n    const leadingWhitespaceOffset = text.length - wordToMatchAgainstWithoutIconsTrimmed.length;\n    // match on value without icon\n    const matches = matchesContiguous(query, wordToMatchAgainstWithoutIconsTrimmed);\n    // Map matches back to offsets with icon and trimming\n    if (matches) {\n        for (const match of matches) {\n            const iconOffset = iconOffsets[match.start + leadingWhitespaceOffset] /* icon offsets at index */ + leadingWhitespaceOffset /* overall leading whitespace offset */;\n            match.start += iconOffset;\n            match.end += iconOffset;\n        }\n    }\n    return matches;\n}\nfunction matchesContiguous(word, wordToMatchAgainst) {\n    const matchIndex = wordToMatchAgainst.toLowerCase().indexOf(word.toLowerCase());\n    if (matchIndex !== -1) {\n        return [{ start: matchIndex, end: matchIndex + word.length }];\n    }\n    return null;\n}\nfunction compareEntries(elementA, elementB, lookFor) {\n    const labelHighlightsA = elementA.labelHighlights || [];\n    const labelHighlightsB = elementB.labelHighlights || [];\n    if (labelHighlightsA.length && !labelHighlightsB.length) {\n        return -1;\n    }\n    if (!labelHighlightsA.length && labelHighlightsB.length) {\n        return 1;\n    }\n    if (labelHighlightsA.length === 0 && labelHighlightsB.length === 0) {\n        return 0;\n    }\n    return compareAnything(elementA.saneSortLabel, elementB.saneSortLabel, lookFor);\n}\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA,IAAIA,UAAU,GAAI,IAAI,IAAI,IAAI,CAACA,UAAU,IAAK,UAAUC,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAE;EACnF,IAAIC,CAAC,GAAGC,SAAS,CAACC,MAAM;IAAEC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGH,MAAM,GAAGE,IAAI,KAAK,IAAI,GAAGA,IAAI,GAAGK,MAAM,CAACC,wBAAwB,CAACR,MAAM,EAAEC,GAAG,CAAC,GAAGC,IAAI;IAAEO,CAAC;EAC5H,IAAI,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,CAACC,QAAQ,KAAK,UAAU,EAAEL,CAAC,GAAGI,OAAO,CAACC,QAAQ,CAACZ,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,CAAC,CAAC,KAC1H,KAAK,IAAIU,CAAC,GAAGb,UAAU,CAACM,MAAM,GAAG,CAAC,EAAEO,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE,IAAIH,CAAC,GAAGV,UAAU,CAACa,CAAC,CAAC,EAAEN,CAAC,GAAG,CAACH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACH,CAAC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACT,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,GAAGG,CAAC,CAACT,MAAM,EAAEC,GAAG,CAAC,KAAKK,CAAC;EACjJ,OAAOH,CAAC,GAAG,CAAC,IAAIG,CAAC,IAAIC,MAAM,CAACM,cAAc,CAACb,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,EAAEA,CAAC;AACjE,CAAC;AACD,IAAIQ,OAAO,GAAI,IAAI,IAAI,IAAI,CAACA,OAAO,IAAK,UAAUC,UAAU,EAAEC,SAAS,EAAE;EACrE,OAAO,UAAUhB,MAAM,EAAEC,GAAG,EAAE;IAAEe,SAAS,CAAChB,MAAM,EAAEC,GAAG,EAAEc,UAAU,CAAC;EAAE,CAAC;AACzE,CAAC;AACD,IAAIE,8BAA8B;AAClC,OAAO,KAAKC,GAAG,MAAM,8BAA8B;AACnD,SAASC,OAAO,EAAEC,KAAK,EAAEC,aAAa,QAAQ,+BAA+B;AAC7E,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,qBAAqB,QAAQ,6CAA6C;AACnF,SAASC,mBAAmB,QAAQ,mCAAmC;AACvE,SAASC,aAAa,QAAQ,oCAAoC;AAClE,SAASC,UAAU,EAAEC,eAAe,QAAQ,mCAAmC;AAC/E,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,qBAAqB,QAAQ,wCAAwC;AAC9E,SAASC,EAAE,QAAQ,kCAAkC;AACrD,SAASC,OAAO,QAAQ,oCAAoC;AAC5D,SAASC,SAAS,QAAQ,iDAAiD;AAC3E,SAASC,eAAe,QAAQ,6DAA6D;AAC7F,SAASC,SAAS,QAAQ,iDAAiD;AAC3E,SAASC,MAAM,QAAQ,6BAA6B;AACpD,SAASC,GAAG,QAAQ,6BAA6B;AACjD,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,SAASC,IAAI,QAAQ,8BAA8B;AACnD,SAASC,mBAAmB,EAAEC,qBAAqB,EAAEC,mBAAmB,QAAQ,oCAAoC;AACpH,SAASC,eAAe,QAAQ,mCAAmC;AACnE,SAASC,KAAK,QAAQ,iCAAiC;AACvD,SAASC,kBAAkB,QAAQ,+CAA+C;AAClF,SAASC,gBAAgB,QAAQ,+BAA+B;AAChE,SAASC,mBAAmB,QAAQ,gCAAgC;AACpE,SAASC,qBAAqB,QAAQ,6CAA6C;AACnF,SAASC,eAAe,EAAEC,mBAAmB,EAAEC,WAAW,QAAQ,oCAAoC;AACtG,SAASC,MAAM,QAAQ,gCAAgC;AACvD,MAAMC,CAAC,GAAGlC,GAAG,CAACkC,CAAC;AACf,MAAMC,wBAAwB,CAAC;EAC3BC,WAAWA,CAACC,KAAK,EAAEC,WAAW,EAAEC,QAAQ,EAAE;IACtC,IAAI,CAACF,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACE,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,KAAK,GAAG,IAAIrB,IAAI,CAAC,MAAM;MACxB,MAAMsB,SAAS,GAAGH,QAAQ,CAACI,KAAK,IAAI,EAAE;MACtC,MAAMC,aAAa,GAAGrB,mBAAmB,CAACmB,SAAS,CAAC,CAACG,IAAI,CAACC,IAAI,CAAC,CAAC;MAChE,MAAMC,aAAa,GAAGR,QAAQ,CAACS,SAAS,IAAI,CAACN,SAAS,EAAE,IAAI,CAACO,eAAe,EAAE,IAAI,CAACC,UAAU,CAAC,CACzFC,GAAG,CAACC,CAAC,IAAI/B,mBAAmB,CAAC+B,CAAC,CAAC,CAAC,CAChCC,MAAM,CAACD,CAAC,IAAI,CAAC,CAACA,CAAC,CAAC,CAChBE,IAAI,CAAC,IAAI,CAAC;MACf,OAAO;QACHZ,SAAS;QACTE,aAAa;QACbG;MACJ,CAAC;IACL,CAAC,CAAC;IACF,IAAI,CAACQ,gBAAgB,GAAGhB,QAAQ,CAACiB,WAAW;IAC5C,IAAI,CAACC,YAAY,GAAGlB,QAAQ,CAACmB,OAAO;EACxC;EACA;EACA,IAAIhB,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACD,KAAK,CAACkB,KAAK,CAACjB,SAAS;EACrC;EACA,IAAIE,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACH,KAAK,CAACkB,KAAK,CAACf,aAAa;EACzC;EACA,IAAIG,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACN,KAAK,CAACkB,KAAK,CAACZ,aAAa;EACzC;EACA,IAAIa,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACC,QAAQ;EACxB;EACA,IAAID,OAAOA,CAACD,KAAK,EAAE;IACf,IAAI,CAACE,QAAQ,GAAGF,KAAK;EACzB;EACA,IAAIG,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACtB,OAAO;EACvB;EACA,IAAIsB,MAAMA,CAACH,KAAK,EAAE;IACd,IAAI,CAACnB,OAAO,GAAGmB,KAAK;EACxB;EACA,IAAIV,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACM,gBAAgB;EAChC;EACA,IAAIN,eAAeA,CAACU,KAAK,EAAE;IACvB,IAAI,CAACJ,gBAAgB,GAAGI,KAAK;EACjC;EACA,IAAIT,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACa,WAAW;EAC3B;EACA,IAAIb,UAAUA,CAACS,KAAK,EAAE;IAClB,IAAI,CAACI,WAAW,GAAGJ,KAAK;EAC5B;EACA,IAAIK,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACP,YAAY;EAC5B;EACA,IAAIO,WAAWA,CAACL,KAAK,EAAE;IACnB,IAAI,CAACF,YAAY,GAAGE,KAAK;EAC7B;EACA,IAAIM,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACC,gBAAgB;EAChC;EACA,IAAID,eAAeA,CAACN,KAAK,EAAE;IACvB,IAAI,CAACO,gBAAgB,GAAGP,KAAK;EACjC;EACA,IAAIQ,qBAAqBA,CAAA,EAAG;IACxB,OAAO,IAAI,CAACC,sBAAsB;EACtC;EACA,IAAID,qBAAqBA,CAACR,KAAK,EAAE;IAC7B,IAAI,CAACS,sBAAsB,GAAGT,KAAK;EACvC;EACA,IAAIU,gBAAgBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACC,iBAAiB;EACjC;EACA,IAAID,gBAAgBA,CAACV,KAAK,EAAE;IACxB,IAAI,CAACW,iBAAiB,GAAGX,KAAK;EAClC;AACJ;AACA,MAAMY,oBAAoB,SAASpC,wBAAwB,CAAC;EACxDC,WAAWA,CAACC,KAAK,EAAEC,WAAW,EAAEkC,mBAAmB,EAAEC,UAAU,EAAEC,IAAI,EAAEC,UAAU,EAAE;IAC/E,KAAK,CAACtC,KAAK,EAAEC,WAAW,EAAEoC,IAAI,CAAC;IAC/B,IAAI,CAACF,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,SAAS,GAAGvC,WAAW,GACtBpC,KAAK,CAACiD,GAAG,CAACjD,KAAK,CAACmD,MAAM,CAAC,IAAI,CAACoB,UAAU,CAACK,KAAK,EAAEC,CAAC,IAAIA,CAAC,CAACnB,OAAO,KAAK,IAAI,CAAC,EAAEmB,CAAC,IAAIA,CAAC,CAACC,OAAO,CAAC,GACvF9E,KAAK,CAAC+E,IAAI;IAChB,IAAI,CAAClB,WAAW,GAAGW,IAAI,CAACQ,MAAM;IAC9B,IAAI,CAAChB,gBAAgB,GAAGQ,IAAI,CAACS,UAAU,EAAExC,KAAK;IAC9C,IAAI,CAACyB,sBAAsB,GAAGM,IAAI,CAACS,UAAU,EAAE3B,WAAW;IAC1D,IAAI,CAACc,iBAAiB,GAAGI,IAAI,CAACS,UAAU,EAAED,MAAM;EACpD;EACA,IAAIE,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACT,UAAU;EAC1B;EACA,IAAIS,SAASA,CAACzB,KAAK,EAAE;IACjB,IAAI,CAACgB,UAAU,GAAGhB,KAAK;EAC3B;EACA,IAAIqB,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACJ,QAAQ;EACxB;EACA,IAAII,OAAOA,CAACrB,KAAK,EAAE;IACf,IAAIA,KAAK,KAAK,IAAI,CAACiB,QAAQ,EAAE;MACzB,IAAI,CAACA,QAAQ,GAAGjB,KAAK;MACrB,IAAI,CAACc,UAAU,CAACY,IAAI,CAAC;QAAEzB,OAAO,EAAE,IAAI;QAAEoB,OAAO,EAAErB;MAAM,CAAC,CAAC;IAC3D;EACJ;EACA,IAAI2B,gBAAgBA,CAAA,EAAG;IACnB,OAAO,CAAC,CAAC,IAAI,CAACZ,IAAI,CAACa,QAAQ;EAC/B;AACJ;AACA,IAAIC,6BAA6B,gBAChC,UAAUA,6BAA6B,EAAE;EACtC;AACJ;AACA;EACIA,6BAA6B,CAACA,6BAA6B,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EACjF;AACJ;AACA;EACIA,6BAA6B,CAACA,6BAA6B,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,GAAG,aAAa;EAC/F;AACJ;AACA;EACIA,6BAA6B,CAACA,6BAA6B,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,GAAG,aAAa;EAAC,OAZzFA,6BAA6B;AAaxC,CAAC,CAAEA,6BAA6B,IAAqC,CAAC,CAAE,CAdvC;AAejC,MAAMC,yBAAyB,SAAStD,wBAAwB,CAAC;EAC7DC,WAAWA,CAACC,KAAK,EAAEqD,4BAA4B,EAAEN,SAAS,EAAE;IACxD,KAAK,CAAC/C,KAAK,EAAE,KAAK,EAAE+C,SAAS,CAAC;IAC9B,IAAI,CAACM,4BAA4B,GAAGA,4BAA4B;IAChE,IAAI,CAACN,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACO,QAAQ,GAAG,IAAIC,KAAK,CAAC,CAAC;IAC3B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,oBAAoB,GAAGL,6BAA6B,CAACM,IAAI;EAClE;AACJ;AACA,MAAMC,sBAAsB,CAAC;EACzBC,SAASA,CAACpC,OAAO,EAAE;IACf,IAAIA,OAAO,YAAY6B,yBAAyB,EAAE;MAC9C,OAAO,EAAE;IACb;IACA,OAAO7B,OAAO,CAACV,UAAU,GAAG,EAAE,GAAG,EAAE;EACvC;EACA+C,aAAaA,CAACrC,OAAO,EAAE;IACnB,IAAIA,OAAO,YAAYW,oBAAoB,EAAE;MACzC,OAAO2B,4BAA4B,CAACC,EAAE;IAC1C,CAAC,MACI;MACD,OAAOC,iCAAiC,CAACD,EAAE;IAC/C;EACJ;AACJ;AACA,MAAME,+BAA+B,CAAC;EAClCC,kBAAkBA,CAAA,EAAG;IACjB,OAAOlG,QAAQ,CAAC,YAAY,EAAE,aAAa,CAAC;EAChD;EACAmG,YAAYA,CAAC3C,OAAO,EAAE;IAClB,OAAOA,OAAO,CAACwB,SAAS,EAAEzC,KAAK,GACzB,GAAGiB,OAAO,CAACb,aAAa,KAAKa,OAAO,CAACwB,SAAS,CAACzC,KAAK,EAAE,GACtDiB,OAAO,CAACb,aAAa;EAC/B;EACAyD,aAAaA,CAAA,EAAG;IACZ,OAAO,SAAS;EACpB;EACAC,OAAOA,CAAC7C,OAAO,EAAE;IACb,OAAOA,OAAO,CAACtB,WAAW,GAAG,UAAU,GAAG,QAAQ;EACtD;EACAoE,SAASA,CAAC9C,OAAO,EAAE;IACf,IAAI,CAACA,OAAO,CAACtB,WAAW,IAAI,EAAEsB,OAAO,YAAYW,oBAAoB,CAAC,EAAE;MACpE,OAAOoC,SAAS;IACpB;IACA,OAAO;MACH,IAAIhD,KAAKA,CAAA,EAAG;QAAE,OAAOC,OAAO,CAACoB,OAAO;MAAE,CAAC;MACvC4B,WAAW,EAAE7B,CAAC,IAAInB,OAAO,CAACiB,SAAS,CAAC,MAAME,CAAC,CAAC,CAAC;IACjD,CAAC;EACL;AACJ;AACA,MAAM8B,0BAA0B,CAAC;EAC7BzE,WAAWA,CAAC0E,aAAa,EAAE;IACvB,IAAI,CAACA,aAAa,GAAGA,aAAa;EACtC;EACA;EACAC,cAAcA,CAACC,SAAS,EAAE;IACtB,MAAMC,IAAI,GAAG5H,MAAM,CAAC6H,MAAM,CAAC,IAAI,CAAC;IAChCD,IAAI,CAACE,gBAAgB,GAAG,IAAI1G,eAAe,CAAC,CAAC;IAC7CwG,IAAI,CAACG,iBAAiB,GAAG,IAAI3G,eAAe,CAAC,CAAC;IAC9CwG,IAAI,CAACI,KAAK,GAAGrH,GAAG,CAACsH,MAAM,CAACN,SAAS,EAAE9E,CAAC,CAAC,yBAAyB,CAAC,CAAC;IAChE;IACA,MAAMS,KAAK,GAAG3C,GAAG,CAACsH,MAAM,CAACL,IAAI,CAACI,KAAK,EAAEnF,CAAC,CAAC,8BAA8B,CAAC,CAAC;IACvE+E,IAAI,CAACG,iBAAiB,CAACG,GAAG,CAACvH,GAAG,CAACwH,6BAA6B,CAAC7E,KAAK,EAAE3C,GAAG,CAACyH,SAAS,CAACC,KAAK,EAAE3C,CAAC,IAAI;MAC1F,IAAI,CAACkC,IAAI,CAACU,QAAQ,CAACC,YAAY,EAAE;QAAE;QAC/B7C,CAAC,CAAC8C,cAAc,CAAC,CAAC,CAAC,CAAC;MACxB;IACJ,CAAC,CAAC,CAAC;IACHZ,IAAI,CAACU,QAAQ,GAAG3H,GAAG,CAACsH,MAAM,CAAC3E,KAAK,EAAET,CAAC,CAAC,iCAAiC,CAAC,CAAC;IACvE+E,IAAI,CAACU,QAAQ,CAACG,IAAI,GAAG,UAAU;IAC/B;IACA,MAAMC,IAAI,GAAG/H,GAAG,CAACsH,MAAM,CAAC3E,KAAK,EAAET,CAAC,CAAC,wBAAwB,CAAC,CAAC;IAC3D,MAAM8F,IAAI,GAAGhI,GAAG,CAACsH,MAAM,CAACS,IAAI,EAAE7F,CAAC,CAAC,uBAAuB,CAAC,CAAC;IACzD,MAAM+F,IAAI,GAAGjI,GAAG,CAACsH,MAAM,CAACS,IAAI,EAAE7F,CAAC,CAAC,uBAAuB,CAAC,CAAC;IACzD;IACA+E,IAAI,CAACtE,KAAK,GAAG,IAAI7B,SAAS,CAACkH,IAAI,EAAE;MAAEE,iBAAiB,EAAE,IAAI;MAAEC,4BAA4B,EAAE,IAAI;MAAEC,YAAY,EAAE,IAAI;MAAEtB,aAAa,EAAE,IAAI,CAACA;IAAc,CAAC,CAAC;IACxJG,IAAI,CAACG,iBAAiB,CAACG,GAAG,CAACN,IAAI,CAACtE,KAAK,CAAC;IACtCsE,IAAI,CAACoB,IAAI,GAAGrI,GAAG,CAACsI,OAAO,CAACrB,IAAI,CAACtE,KAAK,CAACiB,OAAO,EAAE1B,CAAC,CAAC,wBAAwB,CAAC,CAAC;IACxE;IACA,MAAMqG,mBAAmB,GAAGvI,GAAG,CAACsH,MAAM,CAACU,IAAI,EAAE9F,CAAC,CAAC,oCAAoC,CAAC,CAAC;IACrF+E,IAAI,CAACuB,UAAU,GAAG,IAAIzH,eAAe,CAACwH,mBAAmB,EAAE3H,EAAE,CAAC;IAC9DqG,IAAI,CAACG,iBAAiB,CAACG,GAAG,CAACN,IAAI,CAACuB,UAAU,CAAC;IAC3C;IACA,MAAMC,eAAe,GAAGzI,GAAG,CAACsH,MAAM,CAACW,IAAI,EAAE/F,CAAC,CAAC,8BAA8B,CAAC,CAAC;IAC3E+E,IAAI,CAAC/B,MAAM,GAAG,IAAIpE,SAAS,CAAC2H,eAAe,EAAE;MAAEP,iBAAiB,EAAE,IAAI;MAAEE,YAAY,EAAE,IAAI;MAAEtB,aAAa,EAAE,IAAI,CAACA;IAAc,CAAC,CAAC;IAChIG,IAAI,CAACG,iBAAiB,CAACG,GAAG,CAACN,IAAI,CAAC/B,MAAM,CAAC;IACvC;IACA+B,IAAI,CAAC7B,SAAS,GAAGpF,GAAG,CAACsH,MAAM,CAACL,IAAI,CAACI,KAAK,EAAEnF,CAAC,CAAC,6BAA6B,CAAC,CAAC;IACzE;IACA+E,IAAI,CAACyB,SAAS,GAAG,IAAI1H,SAAS,CAACiG,IAAI,CAACI,KAAK,EAAE,IAAI,CAACP,aAAa,GAAG;MAAEA,aAAa,EAAE,IAAI,CAACA;IAAc,CAAC,GAAGH,SAAS,CAAC;IAClHM,IAAI,CAACyB,SAAS,CAACC,OAAO,CAACC,SAAS,CAACrB,GAAG,CAAC,mCAAmC,CAAC;IACzEN,IAAI,CAACG,iBAAiB,CAACG,GAAG,CAACN,IAAI,CAACyB,SAAS,CAAC;IAC1C,OAAOzB,IAAI;EACf;EACA4B,eAAeA,CAAC5B,IAAI,EAAE;IAClBA,IAAI,CAACE,gBAAgB,CAAC2B,OAAO,CAAC,CAAC;IAC/B7B,IAAI,CAACG,iBAAiB,CAAC0B,OAAO,CAAC,CAAC;EACpC;EACAC,cAAcA,CAAClF,QAAQ,EAAEmF,MAAM,EAAE/B,IAAI,EAAE;IACnCA,IAAI,CAACE,gBAAgB,CAAC8B,KAAK,CAAC,CAAC;IAC7BhC,IAAI,CAACyB,SAAS,CAACO,KAAK,CAAC,CAAC;EAC1B;AACJ;AACA,IAAI/C,4BAA4B;EAAA,IAA5BA,4BAA4B,GAAG,MAAMA,4BAA4B,SAASW,0BAA0B,CAAC;IACrG;MAAS9G,8BAA8B,GAAG,IAAI;IAAE;IAChD;MAAS,IAAI,CAACoG,EAAE,GAAG,eAAe;IAAE;IACpC/D,WAAWA,CAAC0E,aAAa,EAAEoC,YAAY,EAAE;MACrC,KAAK,CAACpC,aAAa,CAAC;MACpB,IAAI,CAACoC,YAAY,GAAGA,YAAY;MAChC;MACA,IAAI,CAACC,6BAA6B,GAAG,IAAIC,GAAG,CAAC,CAAC;IAClD;IACA,IAAIC,UAAUA,CAAA,EAAG;MACb,OAAOtJ,8BAA8B,CAACoG,EAAE;IAC5C;IACAY,cAAcA,CAACC,SAAS,EAAE;MACtB,MAAMC,IAAI,GAAG,KAAK,CAACF,cAAc,CAACC,SAAS,CAAC;MAC5CC,IAAI,CAACG,iBAAiB,CAACG,GAAG,CAACvH,GAAG,CAACwH,6BAA6B,CAACP,IAAI,CAACU,QAAQ,EAAE3H,GAAG,CAACyH,SAAS,CAAC6B,MAAM,EAAEvE,CAAC,IAAI;QACnGkC,IAAI,CAACrD,OAAO,CAACoB,OAAO,GAAGiC,IAAI,CAACU,QAAQ,CAAC3C,OAAO;MAChD,CAAC,CAAC,CAAC;MACH,OAAOiC,IAAI;IACf;IACAsC,aAAaA,CAACC,IAAI,EAAEnH,KAAK,EAAE4E,IAAI,EAAE;MAC7B,MAAMrD,OAAO,GAAG4F,IAAI,CAAC5F,OAAO;MAC5BqD,IAAI,CAACrD,OAAO,GAAGA,OAAO;MACtBA,OAAO,CAACA,OAAO,GAAGqD,IAAI,CAACI,KAAK,IAAIV,SAAS;MACzC,MAAMpE,QAAQ,GAAGqB,OAAO,CAACc,IAAI;MAC7BuC,IAAI,CAACU,QAAQ,CAAC3C,OAAO,GAAGpB,OAAO,CAACoB,OAAO;MACvCiC,IAAI,CAACE,gBAAgB,CAACI,GAAG,CAAC3D,OAAO,CAACiB,SAAS,CAACG,OAAO,IAAIiC,IAAI,CAACU,QAAQ,CAAC3C,OAAO,GAAGA,OAAO,CAAC,CAAC;MACxFiC,IAAI,CAACU,QAAQ,CAACpC,QAAQ,GAAG3B,OAAO,CAAC0B,gBAAgB;MACjD,MAAM;QAAErB,eAAe;QAAEE,qBAAqB;QAAEE;MAAiB,CAAC,GAAGT,OAAO;MAC5E;MACA,IAAIrB,QAAQ,CAACkH,QAAQ,EAAE;QACnB,MAAMpB,IAAI,GAAGpH,MAAM,CAAC,IAAI,CAACiI,YAAY,CAACQ,aAAa,CAAC,CAAC,CAAC5B,IAAI,CAAC,GAAGvF,QAAQ,CAACkH,QAAQ,CAACE,IAAI,GAAIpH,QAAQ,CAACkH,QAAQ,CAACG,KAAK,IAAIrH,QAAQ,CAACkH,QAAQ,CAACE,IAAK;QAC1I,MAAME,OAAO,GAAG3I,GAAG,CAAC4I,MAAM,CAACzB,IAAI,CAAC;QAChCpB,IAAI,CAACoB,IAAI,CAAC0B,SAAS,GAAG,uBAAuB;QAC7C9C,IAAI,CAACoB,IAAI,CAAC2B,KAAK,CAACC,eAAe,GAAGjK,GAAG,CAACkK,QAAQ,CAACL,OAAO,CAAC;MAC3D,CAAC,MACI;QACD5C,IAAI,CAACoB,IAAI,CAAC2B,KAAK,CAACC,eAAe,GAAG,EAAE;QACpChD,IAAI,CAACoB,IAAI,CAAC0B,SAAS,GAAGxH,QAAQ,CAAC4H,SAAS,GAAG,yBAAyB5H,QAAQ,CAAC4H,SAAS,EAAE,GAAG,EAAE;MACjG;MACA;MACA,IAAIC,gBAAgB;MACpB;MACA;MACA;MACA,IAAI,CAACxG,OAAO,CAACI,WAAW,IAAIJ,OAAO,CAACX,eAAe,EAAE;QACjDmH,gBAAgB,GAAG;UACfC,QAAQ,EAAE;YACN1G,KAAK,EAAEC,OAAO,CAACX,eAAe;YAC9BqH,iBAAiB,EAAE;UACvB,CAAC;UACDC,4BAA4B,EAAE3G,OAAO,CAACX;QAC1C,CAAC;MACL;MACA,MAAMuH,OAAO,GAAG;QACZC,OAAO,EAAExG,eAAe,IAAI,EAAE;QAC9B;QACAmG,gBAAgB;QAChBM,kBAAkB,EAAEvG,qBAAqB,IAAI,EAAE;QAC/CwG,mBAAmB,EAAE;MACzB,CAAC;MACDH,OAAO,CAACI,YAAY,GAAGrI,QAAQ,CAACsI,WAAW;MAC3CL,OAAO,CAACM,MAAM,GAAGvI,QAAQ,CAACuI,MAAM;MAChCN,OAAO,CAACO,aAAa,GAAGxI,QAAQ,CAACwI,aAAa;MAC9C9D,IAAI,CAACI,KAAK,CAACuB,SAAS,CAACoC,MAAM,CAAC,oCAAoC,CAAC;MACjE/D,IAAI,CAACtE,KAAK,CAACsI,QAAQ,CAACrH,OAAO,CAAClB,SAAS,EAAEkB,OAAO,CAACX,eAAe,EAAEuH,OAAO,CAAC;MACxE;MACAvD,IAAI,CAACuB,UAAU,CAAC0C,GAAG,CAAC3I,QAAQ,CAACiG,UAAU,CAAC;MACxC;MACA,IAAI5E,OAAO,CAACV,UAAU,EAAE;QACpB,IAAIiI,KAAK;QACT;QACA,IAAI,CAACvH,OAAO,CAACI,WAAW,EAAE;UACtBmH,KAAK,GAAG;YACJd,QAAQ,EAAE;cACN1G,KAAK,EAAEC,OAAO,CAACV,UAAU;cACzBoH,iBAAiB,EAAE;YACvB,CAAC;YACDC,4BAA4B,EAAE3G,OAAO,CAACV;UAC1C,CAAC;QACL;QACA+D,IAAI,CAAC/B,MAAM,CAACtB,OAAO,CAACoG,KAAK,CAACoB,OAAO,GAAG,EAAE;QACtCnE,IAAI,CAAC/B,MAAM,CAAC+F,QAAQ,CAACrH,OAAO,CAACV,UAAU,EAAEyD,SAAS,EAAE;UAChD8D,OAAO,EAAEpG,gBAAgB;UACzB8G,KAAK;UACLR,mBAAmB,EAAE;QACzB,CAAC,CAAC;MACN,CAAC,MACI;QACD1D,IAAI,CAAC/B,MAAM,CAACtB,OAAO,CAACoG,KAAK,CAACoB,OAAO,GAAG,MAAM;MAC9C;MACA;MACA,IAAIxH,OAAO,CAACwB,SAAS,EAAEzC,KAAK,EAAE;QAC1BsE,IAAI,CAAC7B,SAAS,CAACiG,WAAW,GAAGzH,OAAO,CAACwB,SAAS,CAACzC,KAAK;QACpDsE,IAAI,CAAC7B,SAAS,CAAC4E,KAAK,CAACoB,OAAO,GAAG,EAAE;QACjC,IAAI,CAACE,oBAAoB,CAAC1H,OAAO,CAAC;MACtC,CAAC,MACI;QACDqD,IAAI,CAAC7B,SAAS,CAAC4E,KAAK,CAACoB,OAAO,GAAG,MAAM;MACzC;MACAnE,IAAI,CAACI,KAAK,CAACuB,SAAS,CAAC2C,MAAM,CAAC,mCAAmC,EAAE,CAAC,CAAC3H,OAAO,CAACwB,SAAS,CAAC;MACrF;MACA,MAAMoG,OAAO,GAAGjJ,QAAQ,CAACiJ,OAAO;MAChC,IAAIA,OAAO,IAAIA,OAAO,CAACrM,MAAM,EAAE;QAC3B8H,IAAI,CAACyB,SAAS,CAAC+C,IAAI,CAACD,OAAO,CAACrI,GAAG,CAAC,CAACuI,MAAM,EAAErJ,KAAK,KAAKlB,wBAAwB,CAACuK,MAAM,EAAE,MAAMrJ,KAAK,EAAE,EAAE,MAAMuB,OAAO,CAACY,mBAAmB,CAAC;UAAEkH,MAAM;UAAEhH,IAAI,EAAEd,OAAO,CAACc;QAAK,CAAC,CAAC,CAAC,CAAC,EAAE;UAAE2D,IAAI,EAAE,IAAI;UAAE1F,KAAK,EAAE;QAAM,CAAC,CAAC;QACrMsE,IAAI,CAACI,KAAK,CAACuB,SAAS,CAACrB,GAAG,CAAC,aAAa,CAAC;MAC3C,CAAC,MACI;QACDN,IAAI,CAACI,KAAK,CAACuB,SAAS,CAACoC,MAAM,CAAC,aAAa,CAAC;MAC9C;IACJ;IACAjC,cAAcA,CAACnF,OAAO,EAAEoF,MAAM,EAAE/B,IAAI,EAAE;MAClC,IAAI,CAAC0E,uBAAuB,CAAC/H,OAAO,CAACA,OAAO,CAAC;MAC7C,KAAK,CAACmF,cAAc,CAACnF,OAAO,EAAEoF,MAAM,EAAE/B,IAAI,CAAC;IAC/C;IACA2E,0BAA0BA,CAAClH,IAAI,EAAE;MAC7B,OAAO,IAAI,CAACyE,6BAA6B,CAAC0C,GAAG,CAACnH,IAAI,CAAC;IACvD;IACA4G,oBAAoBA,CAAC5G,IAAI,EAAE;MACvB,IAAI,CAACyE,6BAA6B,CAAC+B,GAAG,CAACxG,IAAI,EAAE,CAAC,IAAI,CAACyE,6BAA6B,CAAC2C,GAAG,CAACpH,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACzG;IACAiH,uBAAuBA,CAACjH,IAAI,EAAE;MAC1B,MAAMqH,SAAS,GAAG,IAAI,CAAC5C,6BAA6B,CAAC2C,GAAG,CAACpH,IAAI,CAAC,IAAI,CAAC;MACnE,IAAIqH,SAAS,GAAG,CAAC,EAAE;QACf,IAAI,CAAC5C,6BAA6B,CAAC+B,GAAG,CAACxG,IAAI,EAAEqH,SAAS,GAAG,CAAC,CAAC;MAC/D,CAAC,MACI;QACD,IAAI,CAAC5C,6BAA6B,CAAC6C,MAAM,CAACtH,IAAI,CAAC;MACnD;IACJ;EACJ,CAAC;EAAA,OAjIGwB,4BAA4B;AAAA,IAiI/B;AACDA,4BAA4B,GAAGnG,8BAA8B,GAAGnB,UAAU,CAAC,CACvEgB,OAAO,CAAC,CAAC,EAAEW,aAAa,CAAC,CAC5B,EAAE2F,4BAA4B,CAAC;AAAC,IAC3BE,iCAAiC;EAAvC,MAAMA,iCAAiC,SAASS,0BAA0B,CAAC;IACvEzE,WAAWA,CAAA,EAAG;MACV,KAAK,CAAC,GAAGlD,SAAS,CAAC;MACnB;MACA;MACA,IAAI,CAAC+M,2BAA2B,GAAG,IAAI7C,GAAG,CAAC,CAAC;IAChD;IACA;MAAS,IAAI,CAACjD,EAAE,GAAG,oBAAoB;IAAE;IACzC,IAAIkD,UAAUA,CAAA,EAAG;MACb,OAAOjD,iCAAiC,CAACD,EAAE;IAC/C;IACA,IAAI+F,iBAAiBA,CAAA,EAAG;MACpB,OAAO,CAAC,GAAG,IAAI,CAACD,2BAA2B,CAACE,IAAI,CAAC,CAAC,CAAC;IACvD;IACAC,kBAAkBA,CAAChH,SAAS,EAAE;MAC1B,OAAO,IAAI,CAAC6G,2BAA2B,CAACJ,GAAG,CAACzG,SAAS,CAAC;IAC1D;IACA2B,cAAcA,CAACC,SAAS,EAAE;MACtB,MAAMC,IAAI,GAAG,KAAK,CAACF,cAAc,CAACC,SAAS,CAAC;MAC5CC,IAAI,CAACU,QAAQ,CAACqC,KAAK,CAACoB,OAAO,GAAG,MAAM;MACpC,OAAOnE,IAAI;IACf;IACAsC,aAAaA,CAACC,IAAI,EAAEnH,KAAK,EAAE4E,IAAI,EAAE;MAC7B,MAAMrD,OAAO,GAAG4F,IAAI,CAAC5F,OAAO;MAC5BqD,IAAI,CAACrD,OAAO,GAAGA,OAAO;MACtBA,OAAO,CAACA,OAAO,GAAGqD,IAAI,CAACI,KAAK,IAAIV,SAAS;MACzC/C,OAAO,CAACA,OAAO,CAACgF,SAAS,CAAC2C,MAAM,CAAC,cAAc,EAAE,CAAC,CAAC3H,OAAO,CAACiC,oBAAoB,CAAC;MAChF,MAAMtD,QAAQ,GAAGqB,OAAO,CAACwB,SAAS;MAClC,MAAM;QAAEnB,eAAe;QAAEE,qBAAqB;QAAEE;MAAiB,CAAC,GAAGT,OAAO;MAC5E;MACAqD,IAAI,CAACoB,IAAI,CAAC2B,KAAK,CAACC,eAAe,GAAG,EAAE;MACpChD,IAAI,CAACoB,IAAI,CAAC0B,SAAS,GAAG,EAAE;MACxB;MACA,IAAIK,gBAAgB;MACpB;MACA;MACA;MACA,IAAI,CAACxG,OAAO,CAACI,WAAW,IAAIJ,OAAO,CAACX,eAAe,EAAE;QACjDmH,gBAAgB,GAAG;UACfC,QAAQ,EAAE;YACN1G,KAAK,EAAEC,OAAO,CAACX,eAAe;YAC9BqH,iBAAiB,EAAE;UACvB,CAAC;UACDC,4BAA4B,EAAE3G,OAAO,CAACX;QAC1C,CAAC;MACL;MACA,MAAMuH,OAAO,GAAG;QACZC,OAAO,EAAExG,eAAe,IAAI,EAAE;QAC9B;QACAmG,gBAAgB;QAChBM,kBAAkB,EAAEvG,qBAAqB,IAAI,EAAE;QAC/CwG,mBAAmB,EAAE;MACzB,CAAC;MACD1D,IAAI,CAACI,KAAK,CAACuB,SAAS,CAACrB,GAAG,CAAC,oCAAoC,CAAC;MAC9DN,IAAI,CAACtE,KAAK,CAACsI,QAAQ,CAACrH,OAAO,CAAClB,SAAS,EAAEkB,OAAO,CAACX,eAAe,EAAEuH,OAAO,CAAC;MACxE;MACA,IAAI5G,OAAO,CAACV,UAAU,EAAE;QACpB,IAAIiI,KAAK;QACT;QACA,IAAI,CAACvH,OAAO,CAACI,WAAW,EAAE;UACtBmH,KAAK,GAAG;YACJd,QAAQ,EAAE;cACN1G,KAAK,EAAEC,OAAO,CAACV,UAAU;cACzBoH,iBAAiB,EAAE;YACvB,CAAC;YACDC,4BAA4B,EAAE3G,OAAO,CAACV;UAC1C,CAAC;QACL;QACA+D,IAAI,CAAC/B,MAAM,CAACtB,OAAO,CAACoG,KAAK,CAACoB,OAAO,GAAG,EAAE;QACtCnE,IAAI,CAAC/B,MAAM,CAAC+F,QAAQ,CAACrH,OAAO,CAACV,UAAU,EAAEyD,SAAS,EAAE;UAChD8D,OAAO,EAAEpG,gBAAgB;UACzB8G,KAAK;UACLR,mBAAmB,EAAE;QACzB,CAAC,CAAC;MACN,CAAC,MACI;QACD1D,IAAI,CAAC/B,MAAM,CAACtB,OAAO,CAACoG,KAAK,CAACoB,OAAO,GAAG,MAAM;MAC9C;MACA;MACAnE,IAAI,CAAC7B,SAAS,CAAC4E,KAAK,CAACoB,OAAO,GAAG,MAAM;MACrCnE,IAAI,CAACI,KAAK,CAACuB,SAAS,CAACrB,GAAG,CAAC,mCAAmC,CAAC;MAC7D;MACA,MAAMiE,OAAO,GAAGjJ,QAAQ,CAACiJ,OAAO;MAChC,IAAIA,OAAO,IAAIA,OAAO,CAACrM,MAAM,EAAE;QAC3B8H,IAAI,CAACyB,SAAS,CAAC+C,IAAI,CAACD,OAAO,CAACrI,GAAG,CAAC,CAACuI,MAAM,EAAErJ,KAAK,KAAKlB,wBAAwB,CAACuK,MAAM,EAAE,MAAMrJ,KAAK,EAAE,EAAE,MAAMuB,OAAO,CAAC8B,4BAA4B,CAAC;UAAEgG,MAAM;UAAEtG,SAAS,EAAExB,OAAO,CAACwB;QAAU,CAAC,CAAC,CAAC,CAAC,EAAE;UAAEiD,IAAI,EAAE,IAAI;UAAE1F,KAAK,EAAE;QAAM,CAAC,CAAC;QACxNsE,IAAI,CAACI,KAAK,CAACuB,SAAS,CAACrB,GAAG,CAAC,aAAa,CAAC;MAC3C,CAAC,MACI;QACDN,IAAI,CAACI,KAAK,CAACuB,SAAS,CAACoC,MAAM,CAAC,aAAa,CAAC;MAC9C;MACA,IAAI,CAACqB,YAAY,CAACzI,OAAO,CAAC;IAC9B;IACAmF,cAAcA,CAACnF,OAAO,EAAEoF,MAAM,EAAE/B,IAAI,EAAE;MAClC,IAAI,CAACqF,eAAe,CAAC1I,OAAO,CAACA,OAAO,CAAC;MACrC,IAAI,CAAC,IAAI,CAACwI,kBAAkB,CAACxI,OAAO,CAACA,OAAO,CAAC,EAAE;QAC3CA,OAAO,CAACA,OAAO,CAACA,OAAO,EAAEgF,SAAS,CAACoC,MAAM,CAAC,cAAc,CAAC;MAC7D;MACA,KAAK,CAACjC,cAAc,CAACnF,OAAO,EAAEoF,MAAM,EAAE/B,IAAI,CAAC;IAC/C;IACAoF,YAAYA,CAACjH,SAAS,EAAE;MACpB,IAAI,CAAC6G,2BAA2B,CAACf,GAAG,CAAC9F,SAAS,EAAE,CAAC,IAAI,CAAC6G,2BAA2B,CAACH,GAAG,CAAC1G,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC/G;IACAkH,eAAeA,CAAClH,SAAS,EAAE;MACvB,MAAM2G,SAAS,GAAG,IAAI,CAACE,2BAA2B,CAACH,GAAG,CAAC1G,SAAS,CAAC,IAAI,CAAC;MACtE,IAAI2G,SAAS,GAAG,CAAC,EAAE;QACf,IAAI,CAACE,2BAA2B,CAACf,GAAG,CAAC9F,SAAS,EAAE2G,SAAS,GAAG,CAAC,CAAC;MAClE,CAAC,MACI;QACD,IAAI,CAACE,2BAA2B,CAACD,MAAM,CAAC5G,SAAS,CAAC;MACtD;IACJ;EACJ;EAAC,OA/GKgB,iCAAiC;AAAA;AAgHvC,IAAImG,cAAc,GAAG,MAAMA,cAAc,SAAS/L,UAAU,CAAC;EACzD4B,WAAWA,CAACoK,MAAM,EAAE1F,aAAa,EAAE2F,kBAAkB,EAAEC,EAAE,EAAEC,oBAAoB,EAAEC,oBAAoB,EAAE;IACnG,KAAK,CAAC,CAAC;IACP,IAAI,CAACJ,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC1F,aAAa,GAAGA,aAAa;IAClC,IAAI,CAAC2F,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACG,oBAAoB,GAAGA,oBAAoB;IAChD;IACA,IAAI,CAACC,UAAU,GAAG,IAAI5M,OAAO,CAAC,CAAC;IAC/B,IAAI,CAAC6M,QAAQ,GAAG,IAAI7M,OAAO,CAAC,CAAC;IAC7B;AACR;AACA;IACQ,IAAI,CAAC8M,OAAO,GAAG,IAAI,CAACD,QAAQ,CAAChI,KAAK;IAClC,IAAI,CAACkI,uBAAuB,GAAGlL,eAAe,CAAC,cAAc,EAAE,CAAC,CAAC;IACjE,IAAI,CAACmL,qBAAqB,GAAG/M,KAAK,CAACgN,cAAc,CAAC,IAAI,CAACF,uBAAuB,EAAE,IAAI,CAACG,MAAM,CAAC;IAC5F,IAAI,CAACC,4BAA4B,GAAGtL,eAAe,CAAC,mBAAmB,EAAE,KAAK,CAAC;IAC/E,IAAI,CAACuL,0BAA0B,GAAGnN,KAAK,CAACgN,cAAc,CAAC,IAAI,CAACE,4BAA4B,EAAE,IAAI,CAACD,MAAM,CAAC;IACtG,IAAI,CAACG,uBAAuB,GAAGxL,eAAe,CAAC,cAAc,EAAE,CAAC,CAAC;IACjE,IAAI,CAACyL,qBAAqB,GAAGrN,KAAK,CAACgN,cAAc,CAAC,IAAI,CAACI,uBAAuB,EAAE,IAAI,CAACH,MAAM,CAAC;IAC5F,IAAI,CAACK,0BAA0B,GAAGzL,mBAAmB,CAAC;MAAE0L,QAAQ,EAAExL;IAAO,CAAC,EAAE,IAAI2D,KAAK,CAAC,CAAC,CAAC;IACxF,IAAI,CAAC8H,wBAAwB,GAAGxN,KAAK,CAACgN,cAAc,CAAC,IAAI,CAACM,0BAA0B,EAAE,IAAI,CAACL,MAAM,CAAC;IAClG,IAAI,CAACQ,kBAAkB,GAAG,IAAI1N,OAAO,CAAC,CAAC;IACvC,IAAI,CAAC2N,iBAAiB,GAAG,IAAI,CAACD,kBAAkB,CAAC7I,KAAK;IACtD,IAAI,CAAC+I,2BAA2B,GAAG,IAAI5N,OAAO,CAAC,CAAC;IAChD,IAAI,CAAC6N,0BAA0B,GAAG,IAAI,CAACD,2BAA2B,CAAC/I,KAAK;IACxE,IAAI,CAACiJ,eAAe,GAAG,IAAI9N,OAAO,CAAC,CAAC;IACpC,IAAI,CAAC+N,4BAA4B,GAAG,IAAI7N,aAAa,CAAC,CAAC;IACvD;IACA,IAAI,CAAC8N,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACC,cAAc,GAAG,IAAItI,KAAK,CAAC,CAAC;IACjC,IAAI,CAACuI,YAAY,GAAG,IAAIvI,KAAK,CAAC,CAAC;IAC/B,IAAI,CAACwI,aAAa,GAAG,IAAIxI,KAAK,CAAC,CAAC;IAChC;IACA,IAAI,CAACyI,kBAAkB,GAAG,IAAI,CAACC,SAAS,CAAC,IAAI7N,eAAe,CAAC,CAAC,CAAC;IAC/D,IAAI,CAAC8N,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACC,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,iBAAiB,GAAG,OAAO;IAChC,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,UAAU,GAAG7O,GAAG,CAACsH,MAAM,CAAC,IAAI,CAACkF,MAAM,EAAEtK,CAAC,CAAC,mBAAmB,CAAC,CAAC;IACjE,IAAI,CAAC4M,kBAAkB,GAAG,IAAI1I,iCAAiC,CAACU,aAAa,CAAC;IAC9E,IAAI,CAACiI,aAAa,GAAGpC,oBAAoB,CAACqC,cAAc,CAAC9I,4BAA4B,EAAEY,aAAa,CAAC;IACrG,IAAI,CAACmI,KAAK,GAAG,IAAI,CAACX,SAAS,CAAC3B,oBAAoB,CAACqC,cAAc,CAAE1O,mBAAmB,EAAG,YAAY,EAAE,IAAI,CAACuO,UAAU,EAAE,IAAI9I,sBAAsB,CAAC,CAAC,EAAE,CAAC,IAAI,CAACgJ,aAAa,EAAE,IAAI,CAACD,kBAAkB,CAAC,EAAE;MAC/LzL,MAAM,EAAE;QACJA,MAAMA,CAACO,OAAO,EAAE;UACZ,OAAOA,OAAO,CAACE,MAAM,GACf,CAAC,CAAC,8BACFF,OAAO,YAAY6B,yBAAyB,GACxC,CAAC,CAAC,+BACF,CAAC,CAAC;QAChB;MACJ,CAAC;MACDyJ,MAAM,EAAE;QACJC,OAAO,EAAEA,CAACvL,OAAO,EAAEwL,YAAY,KAAK;UAChC,IAAI,CAAC,IAAI,CAACC,WAAW,IAAI,CAAC,IAAI,CAACC,gBAAgB,EAAE;YAC7C,OAAO,CAAC;UACZ;UACA,MAAMC,qBAAqB,GAAG,IAAI,CAACD,gBAAgB,CAACE,WAAW,CAAC,CAAC;UACjE,OAAOC,cAAc,CAAC7L,OAAO,EAAEwL,YAAY,EAAEG,qBAAqB,CAAC;QACvE;MACJ,CAAC;MACDG,qBAAqB,EAAE,IAAIrJ,+BAA+B,CAAC,CAAC;MAC5DsJ,gBAAgB,EAAE,KAAK;MACvBC,wBAAwB,EAAE,KAAK;MAC/BC,+BAA+B,EAAE,IAAI;MACrCC,kBAAkB,EAAEpO,kBAAkB,CAACuD,IAAI;MAC3C8K,iBAAiB,EAAE,KAAK;MACxBC,MAAM,EAAE,CAAC;MACTC,mBAAmB,EAAE,KAAK;MAC1BC,0BAA0B,EAAE,IAAI;MAChCC,uBAAuB,EAAE;IAC7B,CAAC,CAAC,CAAC;IACH,IAAI,CAAClB,KAAK,CAACmB,cAAc,CAAC,CAAC,CAAC1D,EAAE,GAAGA,EAAE;IACnC,IAAI,CAAC2D,kBAAkB,CAAC,CAAC;EAC7B;EACA;EACA,IAAIC,gBAAgBA,CAAA,EAAG;IACnB,OAAOpQ,KAAK,CAACiD,GAAG,CAAC,IAAI,CAAC8L,KAAK,CAACqB,gBAAgB,EAAEvL,CAAC,IAAIA,CAAC,CAACwL,QAAQ,CAAClN,MAAM,CAAE0B,CAAC,IAAKA,CAAC,YAAYR,oBAAoB,CAAC,CAACpB,GAAG,CAAC4B,CAAC,IAAIA,CAAC,CAACL,IAAI,CAAC,EAAE,IAAI,CAACyI,MAAM,CAAC;EACjJ;EACA,IAAIqD,oBAAoBA,CAAA,EAAG;IACvB,OAAOtQ,KAAK,CAACiD,GAAG,CAAC,IAAI,CAAC8L,KAAK,CAACuB,oBAAoB,EAAEzL,CAAC,KAAK;MACpD0L,KAAK,EAAE1L,CAAC,CAACwL,QAAQ,CAAClN,MAAM,CAAE0B,CAAC,IAAKA,CAAC,YAAYR,oBAAoB,CAAC,CAACpB,GAAG,CAAC4B,CAAC,IAAIA,CAAC,CAACL,IAAI,CAAC;MACnFI,KAAK,EAAEC,CAAC,CAAC2L;IACb,CAAC,CAAC,EAAE,IAAI,CAACvD,MAAM,CAAC;EACpB;EACA,IAAIwD,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAC9B,UAAU,CAAC7E,KAAK,CAACoB,OAAO,KAAK,MAAM;EACnD;EACA,IAAIuF,SAASA,CAAChN,KAAK,EAAE;IACjB,IAAI,CAACkL,UAAU,CAAC7E,KAAK,CAACoB,OAAO,GAAGzH,KAAK,GAAG,EAAE,GAAG,MAAM;EACvD;EACA,IAAIiN,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAC3B,KAAK,CAAC2B,SAAS;EAC/B;EACA,IAAIA,SAASA,CAACA,SAAS,EAAE;IACrB,IAAI,CAAC3B,KAAK,CAAC2B,SAAS,GAAGA,SAAS;EACpC;EACA,IAAI5N,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACiM,KAAK,CAACjM,SAAS;EAC/B;EACA,IAAIA,SAASA,CAACL,KAAK,EAAE;IACjB,IAAI,CAACsM,KAAK,CAACjM,SAAS,GAAGL,KAAK,IAAI,EAAE;EACtC;EACA,IAAIkO,OAAOA,CAAClN,KAAK,EAAE;IACf,IAAI,CAACsL,KAAK,CAACmB,cAAc,CAAC,CAAC,CAACpG,KAAK,CAAC8G,aAAa,GAAGnN,KAAK,GAAG,EAAE,GAAG,MAAM;EACzE;EACA,IAAIoN,kBAAkBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAACxC,mBAAmB;EACnC;EACA,IAAIwC,kBAAkBA,CAACpN,KAAK,EAAE;IAC1B,IAAI,CAAC4K,mBAAmB,GAAG5K,KAAK;EACpC;EACA,IAAIqN,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACxC,cAAc;EAC9B;EACA,IAAIwC,aAAaA,CAACrN,KAAK,EAAE;IACrB,IAAI,CAAC6K,cAAc,GAAG7K,KAAK;EAC/B;EACA,IAAIsN,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACxC,aAAa;EAC7B;EACA,IAAIwC,YAAYA,CAACtN,KAAK,EAAE;IACpB,IAAI,CAAC8K,aAAa,GAAG9K,KAAK;EAC9B;EACA,IAAIuN,gBAAgBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACxC,iBAAiB;EACjC;EACA,IAAIwC,gBAAgBA,CAACvN,KAAK,EAAE;IACxB,IAAI,CAAC+K,iBAAiB,GAAG/K,KAAK;EAClC;EACA,IAAI0L,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACV,YAAY;EAC5B;EACA,IAAIU,WAAWA,CAAC1L,KAAK,EAAE;IACnB,IAAI,CAACgL,YAAY,GAAGhL,KAAK;EAC7B;EACA,IAAIwN,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACvC,WAAW;EAC3B;EACA,IAAIuC,UAAUA,CAACxN,KAAK,EAAE;IAClB,IAAI,CAACiL,WAAW,GAAGjL,KAAK;EAC5B;EACA;EACA;EACA0M,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACe,kBAAkB,CAAC,CAAC;IACzB,IAAI,CAACC,yBAAyB,CAAC,CAAC;IAChC,IAAI,CAACC,2BAA2B,CAAC,CAAC;IAClC,IAAI,CAACC,2BAA2B,CAAC,CAAC;IAClC,IAAI,CAACC,yBAAyB,CAAC,CAAC;IAChC,IAAI,CAACC,sBAAsB,CAAC,CAAC;IAC7B,IAAI,CAACC,uBAAuB,CAAC,CAAC;IAC9B,IAAI,CAACC,gCAAgC,CAAC,CAAC;IACvC,IAAI,CAACC,wCAAwC,CAAC,CAAC;EACnD;EACAR,kBAAkBA,CAAA,EAAG;IACjB;IACA,IAAI,CAAC9C,SAAS,CAAC,IAAI,CAACW,KAAK,CAAC4C,SAAS,CAAC9M,CAAC,IAAI;MACrC,MAAMD,KAAK,GAAG,IAAInE,qBAAqB,CAACoE,CAAC,CAAC;MAC1C,QAAQD,KAAK,CAACgN,OAAO;QACjB,KAAK,EAAE,CAAC;UACJ,IAAI,CAACC,cAAc,CAAC,CAAC;UACrB;MACR;MACA,IAAI,CAAClF,UAAU,CAACxH,IAAI,CAACP,KAAK,CAAC;IAC/B,CAAC,CAAC,CAAC;EACP;EACAuM,yBAAyBA,CAAA,EAAG;IACxB,IAAI,CAAC/C,SAAS,CAACtO,GAAG,CAACgS,qBAAqB,CAAC,IAAI,CAACnD,UAAU,EAAE7O,GAAG,CAACyH,SAAS,CAACC,KAAK,EAAE3C,CAAC,IAAI;MAChF,IAAIA,CAAC,CAACkN,CAAC,IAAIlN,CAAC,CAACmN,CAAC,EAAE;QAAE;QACd,IAAI,CAACpF,QAAQ,CAACzH,IAAI,CAAC,CAAC;MACxB;IACJ,CAAC,CAAC,CAAC;EACP;EACAiM,2BAA2BA,CAAA,EAAG;IAC1B,IAAI,CAAChD,SAAS,CAACtO,GAAG,CAACgS,qBAAqB,CAAC,IAAI,CAACnD,UAAU,EAAE7O,GAAG,CAACyH,SAAS,CAAC0K,QAAQ,EAAEpN,CAAC,IAAI;MACnF,IAAIA,CAAC,CAAC2G,MAAM,KAAK,CAAC,EAAE;QAChB,IAAI,CAACoB,QAAQ,CAACzH,IAAI,CAAC,CAAC;MACxB;IACJ,CAAC,CAAC,CAAC;EACP;EACAkM,2BAA2BA,CAAA,EAAG;IAC1B,IAAI,CAACjD,SAAS,CAAC,IAAI,CAACW,KAAK,CAACmD,gBAAgB,CAAC,MAAM;MAC7C,MAAMC,YAAY,GAAG,IAAI,CAACjE,aAAa,CAAC/K,MAAM,CAAC0B,CAAC,IAAI,CAACA,CAAC,CAACjB,MAAM,CAAC,CAAC3E,MAAM;MACrE,IAAI,CAAC6N,uBAAuB,CAAC9B,GAAG,CAACmH,YAAY,EAAE1L,SAAS,CAAC;MACzD,IAAI,IAAI,CAACsH,cAAc,EAAE;QACrB,IAAI,CAACqE,yBAAyB,CAAC,CAAC;MACpC;IACJ,CAAC,CAAC,CAAC;EACP;EACAd,yBAAyBA,CAAA,EAAG;IACxB;IACA,IAAI,CAAClD,SAAS,CAAC,IAAI,CAACN,4BAA4B,CAACuE,SAAS,CAAC,IAAI,CAACxE,eAAe,CAACjJ,KAAK,EAAE,CAAC0N,CAAC,EAAEzN,CAAC,KAAKA,CAAC,CAAC,CAACyN,CAAC,IAAI,IAAI,CAACF,yBAAyB,CAAC,CAAC,CAAC,CAAC;EAC/I;EACAb,sBAAsBA,CAAA,EAAG;IACrB,IAAI,CAACnD,SAAS,CAAC,IAAI,CAACW,KAAK,CAACwD,aAAa,CAAC1N,CAAC,IAAI;MACzC,IAAIA,CAAC,CAACnB,OAAO,EAAE;QACXmB,CAAC,CAAC2L,YAAY,CAAC7I,cAAc,CAAC,CAAC;QAC/B;QACA;QACA;QACA;QACA;QACA,IAAI,CAACoH,KAAK,CAACyD,YAAY,CAAC,CAAC3N,CAAC,CAACnB,OAAO,CAAC,CAAC;MACxC;IACJ,CAAC,CAAC,CAAC;EACP;EACA8N,uBAAuBA,CAAA,EAAG;IAAA,IAAAiB,KAAA;IACtB,MAAMC,OAAO,GAAG,IAAI,CAACtE,SAAS,CAAC,IAAI3M,gBAAgB,CAAC,IAAI,CAACmF,aAAa,CAAC+L,KAAK,CAAC,CAAC;IAC9E,IAAI,CAACvE,SAAS,CAAC,IAAI,CAACW,KAAK,CAAC6D,WAAW;MAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAC,WAAOjO,CAAC,EAAK;QAC/C;QACA;QACA,IAAI/E,GAAG,CAACiT,mBAAmB,CAAClO,CAAC,CAAC2L,YAAY,CAAC5R,MAAM,CAAC,EAAE;UAChD8T,OAAO,CAACM,MAAM,CAAC,CAAC;UAChB;QACJ;QACA;QACA;QACA,CAAElT,GAAG,CAACiT,mBAAmB,CAAClO,CAAC,CAAC2L,YAAY,CAACyC,aAAa,CAAE;QACpD;QACAnT,GAAG,CAACoT,UAAU,CAACrO,CAAC,CAAC2L,YAAY,CAACyC,aAAa,EAAEpO,CAAC,CAACnB,OAAO,EAAEA,OAAO,CAAC,EAAE;UAClE;QACJ;QACA,IAAI;UACA,MAAMgP,OAAO,CAACS,OAAO,cAAAL,iBAAA,CAAC,aAAY;YAC9B,IAAIjO,CAAC,CAACnB,OAAO,YAAYW,oBAAoB,EAAE;cAC3CoO,KAAI,CAACW,SAAS,CAACvO,CAAC,CAACnB,OAAO,CAAC;YAC7B;UACJ,CAAC,EAAC;QACN,CAAC,CACD,OAAOmB,CAAC,EAAE;UACN;UACA,IAAI,CAACnD,mBAAmB,CAACmD,CAAC,CAAC,EAAE;YACzB,MAAMA,CAAC;UACX;QACJ;MACJ,CAAC;MAAA,iBAAAwO,EAAA;QAAA,OAAAR,IAAA,CAAAS,KAAA,OAAAtU,SAAA;MAAA;IAAA,IAAC,CAAC;IACH,IAAI,CAACoP,SAAS,CAAC,IAAI,CAACW,KAAK,CAACwE,UAAU,CAAC1O,CAAC,IAAI;MACtC;MACA;MACA;MACA,IAAI/E,GAAG,CAACoT,UAAU,CAACrO,CAAC,CAAC2L,YAAY,CAACyC,aAAa,EAAEpO,CAAC,CAACnB,OAAO,EAAEA,OAAO,CAAC,EAAE;QAClE;MACJ;MACAgP,OAAO,CAACM,MAAM,CAAC,CAAC;IACpB,CAAC,CAAC,CAAC;EACP;EACA;AACJ;AACA;AACA;EACItB,wCAAwCA,CAAA,EAAG;IACvC,IAAI,CAACtD,SAAS,CAAC,IAAI,CAACW,KAAK,CAACqB,gBAAgB,CAACvL,CAAC,IAAI;MAC5C,MAAMyH,MAAM,GAAGzH,CAAC,CAACwL,QAAQ,CAAC,CAAC,CAAC,GACtB,IAAI,CAACtB,KAAK,CAACyE,gBAAgB,CAAC3O,CAAC,CAACwL,QAAQ,CAAC,CAAC,CAAC;MAC3C;MAAA,EACE,IAAI;MACV,KAAK,MAAMnL,SAAS,IAAI,IAAI,CAAC0J,kBAAkB,CAAC5C,iBAAiB,EAAE;QAC/D,MAAMvI,KAAK,GAAGyB,SAAS,KAAKoH,MAAM;QAClC;QACA,MAAMmH,aAAa,GAAG,CAAC,EAAEvO,SAAS,CAACS,oBAAoB,GAAGL,6BAA6B,CAACoO,WAAW,CAAC;QACpG,IAAID,aAAa,KAAKhQ,KAAK,EAAE;UACzB,IAAIA,KAAK,EAAE;YACPyB,SAAS,CAACS,oBAAoB,IAAIL,6BAA6B,CAACoO,WAAW;UAC/E,CAAC,MACI;YACDxO,SAAS,CAACS,oBAAoB,IAAI,CAACL,6BAA6B,CAACoO,WAAW;UAChF;UACA,IAAI,CAAC3E,KAAK,CAAC4E,QAAQ,CAACzO,SAAS,CAAC;QAClC;MACJ;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,CAACkJ,SAAS,CAAC,IAAI,CAACW,KAAK,CAAC6D,WAAW,CAAC/N,CAAC,IAAI;MACvC,MAAMyH,MAAM,GAAGzH,CAAC,CAACnB,OAAO,GAClB,IAAI,CAACqL,KAAK,CAACyE,gBAAgB,CAAC3O,CAAC,CAACnB,OAAO,CAAC,GACtC,IAAI;MACV,KAAK,MAAMwB,SAAS,IAAI,IAAI,CAAC0J,kBAAkB,CAAC5C,iBAAiB,EAAE;QAC/D,IAAI9G,SAAS,KAAKoH,MAAM,EAAE;UACtB;QACJ;QACA,MAAMsH,YAAY,GAAG,CAAC,EAAE1O,SAAS,CAACS,oBAAoB,GAAGL,6BAA6B,CAACuO,WAAW,CAAC;QACnG,IAAI,CAACD,YAAY,EAAE;UACf1O,SAAS,CAACS,oBAAoB,IAAIL,6BAA6B,CAACuO,WAAW;UAC3E,IAAI,CAAC9E,KAAK,CAAC4E,QAAQ,CAACzO,SAAS,CAAC;QAClC;MACJ;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,CAACkJ,SAAS,CAAC,IAAI,CAACW,KAAK,CAACwE,UAAU,CAAC1O,CAAC,IAAI;MACtC,MAAMyH,MAAM,GAAGzH,CAAC,CAACnB,OAAO,GAClB,IAAI,CAACqL,KAAK,CAACyE,gBAAgB,CAAC3O,CAAC,CAACnB,OAAO,CAAC,GACtC,IAAI;MACV,KAAK,MAAMwB,SAAS,IAAI,IAAI,CAAC0J,kBAAkB,CAAC5C,iBAAiB,EAAE;QAC/D,IAAI9G,SAAS,KAAKoH,MAAM,EAAE;UACtB;QACJ;QACA,MAAMsH,YAAY,GAAG,CAAC,EAAE1O,SAAS,CAACS,oBAAoB,GAAGL,6BAA6B,CAACuO,WAAW,CAAC;QACnG,IAAID,YAAY,EAAE;UACd1O,SAAS,CAACS,oBAAoB,IAAI,CAACL,6BAA6B,CAACuO,WAAW;UAC5E,IAAI,CAAC9E,KAAK,CAAC4E,QAAQ,CAACzO,SAAS,CAAC;QAClC;MACJ;IACJ,CAAC,CAAC,CAAC;EACP;EACAuM,gCAAgCA,CAAA,EAAG;IAC/B;IACA;IACA,IAAI,CAACrD,SAAS,CAAC,IAAI,CAACW,KAAK,CAACuB,oBAAoB,CAACzL,CAAC,IAAI;MAChD,MAAMiP,yBAAyB,GAAGjP,CAAC,CAACwL,QAAQ,CAAClN,MAAM,CAAE0B,CAAC,IAAKA,CAAC,YAAYR,oBAAoB,CAAC;MAC7F,IAAIyP,yBAAyB,CAAC7U,MAAM,KAAK4F,CAAC,CAACwL,QAAQ,CAACpR,MAAM,EAAE;QACxD,IAAI4F,CAAC,CAACwL,QAAQ,CAACpR,MAAM,KAAK,CAAC,IAAI4F,CAAC,CAACwL,QAAQ,CAAC,CAAC,CAAC,YAAY9K,yBAAyB,EAAE;UAC/E,IAAI,CAACwJ,KAAK,CAACgF,QAAQ,CAAC,CAAClP,CAAC,CAACwL,QAAQ,CAAC,CAAC,CAAC,CAAC5K,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;UAChD,IAAI,CAACsJ,KAAK,CAACiF,MAAM,CAACnP,CAAC,CAACwL,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACvC;QACA,IAAI,CAACtB,KAAK,CAACyD,YAAY,CAACsB,yBAAyB,CAAC;MACtD;IACJ,CAAC,CAAC,CAAC;EACP;EACA;EACA;EACAG,oBAAoBA,CAACnP,OAAO,EAAE;IAC1B,IAAI,CAACgJ,4BAA4B,CAACoG,YAAY,CAAC,MAAM;MACjD,IAAI,CAAChG,aAAa,CAACiG,OAAO,CAACzQ,OAAO,IAAI;QAClC,IAAI,CAACA,OAAO,CAACE,MAAM,IAAI,CAACF,OAAO,CAAC0B,gBAAgB,EAAE;UAC9C;UACA1B,OAAO,CAACoB,OAAO,GAAGA,OAAO;QAC7B;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACAsP,WAAWA,CAACC,aAAa,EAAE;IACvB,IAAI,CAAClG,kBAAkB,CAACpF,KAAK,CAAC,CAAC;IAC/B,IAAI,CAACqG,gBAAgB,GAAG3I,SAAS;IACjC,IAAI,CAACuH,cAAc,GAAGqG,aAAa;IACnC,IAAI,CAACtG,cAAc,GAAG,IAAI,CAACzB,MAAM,CAAC5D,SAAS,CAAC4L,QAAQ,CAAC,iBAAiB,CAAC;IACvE,IAAIC,uBAAuB;IAC3B,IAAI,CAACrG,aAAa,GAAG,IAAIxI,KAAK,CAAC,CAAC;IAChC,IAAI,CAACuI,YAAY,GAAGoG,aAAa,CAACG,MAAM,CAAC,CAACC,MAAM,EAAEjQ,IAAI,EAAErC,KAAK,KAAK;MAC9D,IAAIuB,OAAO;MACX,IAAIc,IAAI,CAACoD,IAAI,KAAK,WAAW,EAAE;QAC3B,IAAI,CAACpD,IAAI,CAAC8G,OAAO,EAAE;UACf;UACA,OAAOmJ,MAAM;QACjB;QACAF,uBAAuB,GAAG,IAAIhP,yBAAyB,CAACpD,KAAK,EAAE0C,CAAC,IAAI,IAAI,CAAC8I,2BAA2B,CAACxI,IAAI,CAACN,CAAC,CAAC,EAAEL,IAAI,CAAC;QACnHd,OAAO,GAAG6Q,uBAAuB;MACrC,CAAC,MACI;QACD,MAAMG,QAAQ,GAAGvS,KAAK,GAAG,CAAC,GAAGkS,aAAa,CAAClS,KAAK,GAAG,CAAC,CAAC,GAAGsE,SAAS;QACjE,IAAIvB,SAAS;QACb,IAAIwP,QAAQ,IAAIA,QAAQ,CAAC9M,IAAI,KAAK,WAAW,IAAI,CAAC8M,QAAQ,CAACpJ,OAAO,EAAE;UAChE;UACAiJ,uBAAuB,GAAG9N,SAAS;UACnCvB,SAAS,GAAGwP,QAAQ;QACxB;QACA,MAAMC,GAAG,GAAG,IAAItQ,oBAAoB,CAAClC,KAAK,EAAE,IAAI,CAAC4L,cAAc,EAAElJ,CAAC,IAAI,IAAI,CAAC4I,kBAAkB,CAACtI,IAAI,CAACN,CAAC,CAAC,EAAE,IAAI,CAACgJ,eAAe,EAAErJ,IAAI,EAAEU,SAAS,CAAC;QAC7I,IAAI,CAACgJ,aAAa,CAAC3C,IAAI,CAACoJ,GAAG,CAAC;QAC5B,IAAIJ,uBAAuB,EAAE;UACzBA,uBAAuB,CAAC9O,QAAQ,CAAC8F,IAAI,CAACoJ,GAAG,CAAC;UAC1C,OAAOF,MAAM;QACjB;QACA/Q,OAAO,GAAGiR,GAAG;MACjB;MACAF,MAAM,CAAClJ,IAAI,CAAC7H,OAAO,CAAC;MACpB,OAAO+Q,MAAM;IACjB,CAAC,EAAE,IAAI/O,KAAK,CAAC,CAAC,CAAC;IACf,IAAI,CAACkP,kBAAkB,CAAC,IAAI,CAAC3G,YAAY,CAAC;IAC1C;IACA;IACA,IAAI,IAAI,CAACvB,oBAAoB,CAACmI,uBAAuB,CAAC,CAAC,EAAE;MACrDC,UAAU,CAAC,MAAM;QACb,MAAMC,cAAc,GAAG,IAAI,CAAChG,KAAK,CAACmB,cAAc,CAAC,CAAC,CAAC8E,aAAa,CAAC,0BAA0B,CAAC;QAC5F,MAAM1I,MAAM,GAAGyI,cAAc,EAAEE,UAAU;QACzC,IAAIF,cAAc,IAAIzI,MAAM,EAAE;UAC1B,MAAM4I,WAAW,GAAGH,cAAc,CAACG,WAAW;UAC9CH,cAAc,CAACjK,MAAM,CAAC,CAAC;UACvBwB,MAAM,CAAC6I,YAAY,CAACJ,cAAc,EAAEG,WAAW,CAAC;QACpD;MACJ,CAAC,EAAE,CAAC,CAAC;IACT;EACJ;EACAE,kBAAkBA,CAAC7E,KAAK,EAAE;IACtB,MAAMF,QAAQ,GAAGE,KAAK,CAACtN,GAAG,CAACuB,IAAI,IAAI,IAAI,CAAC0J,aAAa,CAACmH,IAAI,CAACxQ,CAAC,IAAIA,CAAC,CAACL,IAAI,KAAKA,IAAI,CAAC,CAAC,CAC5ErB,MAAM,CAAE0B,CAAC,IAAK,CAAC,CAACA,CAAC,CAAC,CAClB1B,MAAM,CAAC0B,CAAC,IAAI,CAACA,CAAC,CAACjB,MAAM,CAAC;IAC3B,IAAI,CAACmL,KAAK,CAACgF,QAAQ,CAAC1D,QAAQ,CAAC;IAC7B,IAAIE,KAAK,CAACtR,MAAM,GAAG,CAAC,EAAE;MAClB,MAAMqW,OAAO,GAAG,IAAI,CAACvG,KAAK,CAACwG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;MACxC,IAAID,OAAO,EAAE;QACT,IAAI,CAACvG,KAAK,CAACiF,MAAM,CAACsB,OAAO,CAAC;MAC9B;IACJ;EACJ;EACAE,mBAAmBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACzG,KAAK,CAACmB,cAAc,CAAC,CAAC,CAACuF,YAAY,CAAC,uBAAuB,CAAC;EAC5E;EACAC,mBAAmBA,CAACnF,KAAK,EAAE;IACvB,MAAMF,QAAQ,GAAGE,KAAK,CAACtN,GAAG,CAACuB,IAAI,IAAI,IAAI,CAAC0J,aAAa,CAACmH,IAAI,CAACxQ,CAAC,IAAIA,CAAC,CAACL,IAAI,KAAKA,IAAI,CAAC,CAAC,CAC5ErB,MAAM,CAAE0B,CAAC,IAAK,CAAC,CAACA,CAAC,CAAC;IACvB,IAAI,CAACkK,KAAK,CAACyD,YAAY,CAACnC,QAAQ,CAAC;EACrC;EACAsF,kBAAkBA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACzH,aAAa,CAAC/K,MAAM,CAAC0B,CAAC,IAAIA,CAAC,CAACC,OAAO,CAAC,CAC3C7B,GAAG,CAAC4B,CAAC,IAAIA,CAAC,CAACL,IAAI,CAAC;EACzB;EACAoR,kBAAkBA,CAACrF,KAAK,EAAE;IACtB,IAAI,CAACzC,4BAA4B,CAACoG,YAAY,CAAC,MAAM;MACjD,MAAMpP,OAAO,GAAG,IAAI+Q,GAAG,CAAC,CAAC;MACzB,KAAK,MAAMrR,IAAI,IAAI+L,KAAK,EAAE;QACtBzL,OAAO,CAACuC,GAAG,CAAC7C,IAAI,CAAC;MACrB;MACA,KAAK,MAAMd,OAAO,IAAI,IAAI,CAACwK,aAAa,EAAE;QACtC;QACAxK,OAAO,CAACoB,OAAO,GAAGA,OAAO,CAAC6G,GAAG,CAACjI,OAAO,CAACc,IAAI,CAAC;MAC/C;IACJ,CAAC,CAAC;EACN;EACAsR,KAAKA,CAACC,IAAI,EAAE;IACR,IAAI,CAAC,IAAI,CAAC7H,aAAa,CAACjP,MAAM,EAAE;MAC5B;IACJ;IACA,IAAI8W,IAAI,KAAKvV,cAAc,CAACwV,MAAM,IAAI,IAAI,CAAC9H,aAAa,CAACjP,MAAM,GAAG,CAAC,EAAE;MACjE8W,IAAI,GAAGvV,cAAc,CAACyV,KAAK;IAC/B;IACA,QAAQF,IAAI;MACR,KAAKvV,cAAc,CAACyV,KAAK;QACrB,IAAI,CAAClH,KAAK,CAAC2B,SAAS,GAAG,CAAC;QACxB,IAAI,CAAC3B,KAAK,CAACmH,UAAU,CAACzP,SAAS,EAAG5B,CAAC,IAAKA,CAAC,CAACnB,OAAO,YAAYW,oBAAoB,CAAC;QAClF;MACJ,KAAK7D,cAAc,CAACwV,MAAM;QAAE;UACxB,IAAI,CAACjH,KAAK,CAAC2B,SAAS,GAAG,CAAC;UACxB,IAAIyF,YAAY,GAAG,KAAK;UACxB,IAAI,CAACpH,KAAK,CAACmH,UAAU,CAACzP,SAAS,EAAG5B,CAAC,IAAK;YACpC,IAAI,EAAEA,CAAC,CAACnB,OAAO,YAAYW,oBAAoB,CAAC,EAAE;cAC9C,OAAO,KAAK;YAChB;YACA,IAAI8R,YAAY,EAAE;cACd,OAAO,IAAI;YACf;YACAA,YAAY,GAAG,CAACA,YAAY;YAC5B,OAAO,KAAK;UAChB,CAAC,CAAC;UACF;QACJ;MACA,KAAK3V,cAAc,CAAC4V,IAAI;QACpB,IAAI,CAACrH,KAAK,CAAC2B,SAAS,GAAG,IAAI,CAAC3B,KAAK,CAACsH,YAAY;QAC9C,IAAI,CAACtH,KAAK,CAACuH,SAAS,CAAC7P,SAAS,EAAG5B,CAAC,IAAKA,CAAC,CAACnB,OAAO,YAAYW,oBAAoB,CAAC;QACjF;MACJ,KAAK7D,cAAc,CAAC+V,IAAI;QAAE;UACtB,MAAMC,SAAS,GAAG,IAAI,CAACzH,KAAK,CAACwG,QAAQ,CAAC,CAAC;UACvC,IAAI,CAACxG,KAAK,CAAC0H,SAAS,CAAChQ,SAAS,EAAE,IAAI,CAACiI,WAAW,EAAEjI,SAAS,EAAG5B,CAAC,IAAK;YAChE,IAAI,EAAEA,CAAC,CAACnB,OAAO,YAAYW,oBAAoB,CAAC,EAAE;cAC9C,OAAO,KAAK;YAChB;YACA,IAAI,CAAC0K,KAAK,CAACiF,MAAM,CAACnP,CAAC,CAACnB,OAAO,CAAC;YAC5B,OAAO,IAAI;UACf,CAAC,CAAC;UACF,MAAMgT,YAAY,GAAG,IAAI,CAAC3H,KAAK,CAACwG,QAAQ,CAAC,CAAC;UAC1C,IAAIiB,SAAS,CAACvX,MAAM,IAAIuX,SAAS,CAAC,CAAC,CAAC,KAAKE,YAAY,CAAC,CAAC,CAAC,IAAIF,SAAS,CAAC,CAAC,CAAC,KAAK,IAAI,CAACtI,aAAa,CAAC,IAAI,CAACA,aAAa,CAACjP,MAAM,GAAG,CAAC,CAAC,EAAE;YAC5H,IAAI,CAAC2N,QAAQ,CAACzH,IAAI,CAAC,CAAC;UACxB;UACA;QACJ;MACA,KAAK3E,cAAc,CAACmW,QAAQ;QAAE;UAC1B,MAAMH,SAAS,GAAG,IAAI,CAACzH,KAAK,CAACwG,QAAQ,CAAC,CAAC;UACvC,IAAI,CAACxG,KAAK,CAAC6H,aAAa,CAACnQ,SAAS,EAAE,IAAI,CAACiI,WAAW,EAAEjI,SAAS,EAAG5B,CAAC,IAAK;YACpE,IAAI,EAAEA,CAAC,CAACnB,OAAO,YAAYW,oBAAoB,CAAC,EAAE;cAC9C,OAAO,KAAK;YAChB;YACA,MAAMiI,MAAM,GAAG,IAAI,CAACyC,KAAK,CAACyE,gBAAgB,CAAC3O,CAAC,CAACnB,OAAO,CAAC;YACrD,IAAI4I,MAAM,KAAK,IAAI,IAAIA,MAAM,CAAC7G,QAAQ,CAAC,CAAC,CAAC,KAAKZ,CAAC,CAACnB,OAAO,EAAE;cACrD,IAAI,CAACqL,KAAK,CAACiF,MAAM,CAACnP,CAAC,CAACnB,OAAO,CAAC;YAChC,CAAC,MACI;cACD;cACA,IAAI,CAACqL,KAAK,CAACiF,MAAM,CAAC1H,MAAM,CAAC;YAC7B;YACA,OAAO,IAAI;UACf,CAAC,CAAC;UACF,MAAMoK,YAAY,GAAG,IAAI,CAAC3H,KAAK,CAACwG,QAAQ,CAAC,CAAC;UAC1C,IAAIiB,SAAS,CAACvX,MAAM,IAAIuX,SAAS,CAAC,CAAC,CAAC,KAAKE,YAAY,CAAC,CAAC,CAAC,IAAIF,SAAS,CAAC,CAAC,CAAC,KAAK,IAAI,CAACtI,aAAa,CAAC,CAAC,CAAC,EAAE;YAChG,IAAI,CAACtB,QAAQ,CAACzH,IAAI,CAAC,CAAC;UACxB;UACA;QACJ;MACA,KAAK3E,cAAc,CAACqW,QAAQ;QACxB,IAAI,CAAC9H,KAAK,CAAC+H,aAAa,CAACrQ,SAAS,EAAG5B,CAAC,IAAK;UACvC,IAAI,EAAEA,CAAC,CAACnB,OAAO,YAAYW,oBAAoB,CAAC,EAAE;YAC9C,OAAO,KAAK;UAChB;UACA,IAAI,CAAC0K,KAAK,CAACiF,MAAM,CAACnP,CAAC,CAACnB,OAAO,CAAC;UAC5B,OAAO,IAAI;QACf,CAAC,CAAC;QACF;MACJ,KAAKlD,cAAc,CAACuW,YAAY;QAC5B,IAAI,CAAChI,KAAK,CAACiI,iBAAiB,CAACvQ,SAAS,EAAG5B,CAAC,IAAK;UAC3C,IAAI,EAAEA,CAAC,CAACnB,OAAO,YAAYW,oBAAoB,CAAC,EAAE;YAC9C,OAAO,KAAK;UAChB;UACA,MAAMiI,MAAM,GAAG,IAAI,CAACyC,KAAK,CAACyE,gBAAgB,CAAC3O,CAAC,CAACnB,OAAO,CAAC;UACrD,IAAI4I,MAAM,KAAK,IAAI,IAAIA,MAAM,CAAC7G,QAAQ,CAAC,CAAC,CAAC,KAAKZ,CAAC,CAACnB,OAAO,EAAE;YACrD,IAAI,CAACqL,KAAK,CAACiF,MAAM,CAACnP,CAAC,CAACnB,OAAO,CAAC;UAChC,CAAC,MACI;YACD,IAAI,CAACqL,KAAK,CAACiF,MAAM,CAAC1H,MAAM,CAAC;UAC7B;UACA,OAAO,IAAI;QACf,CAAC,CAAC;QACF;MACJ,KAAK9L,cAAc,CAACyW,aAAa;QAAE;UAC/B,IAAIC,oBAAoB,GAAG,KAAK;UAChC,MAAMC,MAAM,GAAG,IAAI,CAACpI,KAAK,CAACwG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;UACvC,IAAI,CAACxG,KAAK,CAAC0H,SAAS,CAAChQ,SAAS,EAAE,IAAI,EAAEA,SAAS,EAAG5B,CAAC,IAAK;YACpD,IAAIqS,oBAAoB,EAAE;cACtB;cACA;cACA,OAAO,IAAI;YACf;YACA,IAAIrS,CAAC,CAACnB,OAAO,YAAY6B,yBAAyB,EAAE;cAChD2R,oBAAoB,GAAG,IAAI;cAC3B;cACA,IAAI,IAAI,CAACtI,kBAAkB,CAAC1C,kBAAkB,CAACrH,CAAC,CAACnB,OAAO,CAAC,EAAE;gBACvD,IAAI,CAACqL,KAAK,CAACiF,MAAM,CAACnP,CAAC,CAACnB,OAAO,CAAC+B,QAAQ,CAAC,CAAC,CAAC,CAAC;cAC5C,CAAC,MACI;gBACD;gBACA;gBACA,IAAI,CAACsJ,KAAK,CAACiF,MAAM,CAACnP,CAAC,CAACnB,OAAO,EAAE,CAAC,CAAC;cACnC;YACJ,CAAC,MACI,IAAImB,CAAC,CAACnB,OAAO,YAAYW,oBAAoB,EAAE;cAChD,IAAIQ,CAAC,CAACnB,OAAO,CAACwB,SAAS,EAAE;gBACrB,IAAI,IAAI,CAAC2J,aAAa,CAACnD,0BAA0B,CAAC7G,CAAC,CAACnB,OAAO,CAAC,EAAE;kBAC1D,IAAI,CAACqL,KAAK,CAACiF,MAAM,CAACnP,CAAC,CAACnB,OAAO,CAAC;gBAChC,CAAC,MACI;kBACD,IAAI,CAACqL,KAAK,CAACiF,MAAM,CAACnP,CAAC,CAACnB,OAAO,EAAE,CAAC,CAAC;gBACnC;gBACA,OAAO,IAAI;cACf,CAAC,MACI,IAAImB,CAAC,CAACnB,OAAO,KAAK,IAAI,CAACuK,YAAY,CAAC,CAAC,CAAC,EAAE;gBACzC;gBACA,IAAI,CAACc,KAAK,CAACiF,MAAM,CAACnP,CAAC,CAACnB,OAAO,EAAE,CAAC,CAAC;gBAC/B,OAAO,IAAI;cACf;YACJ;YACA,OAAO,KAAK;UAChB,CAAC,CAAC;UACF,MAAM0T,KAAK,GAAG,IAAI,CAACrI,KAAK,CAACwG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;UACtC,IAAI4B,MAAM,KAAKC,KAAK,EAAE;YAClB;YACA;YACA,IAAI,CAACrI,KAAK,CAAC2B,SAAS,GAAG,IAAI,CAAC3B,KAAK,CAACsH,YAAY;YAC9C,IAAI,CAACtH,KAAK,CAACuH,SAAS,CAAC7P,SAAS,EAAG5B,CAAC,IAAKA,CAAC,CAACnB,OAAO,YAAYW,oBAAoB,CAAC;UACrF;UACA;QACJ;MACA,KAAK7D,cAAc,CAAC6W,iBAAiB;QAAE;UACnC,IAAIC,YAAY;UAChB;UACA;UACA;UACA,IAAIC,cAAc,GAAG,CAAC,CAAC,IAAI,CAACxI,KAAK,CAACwG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAErQ,SAAS;UAC1D,IAAI,CAAC6J,KAAK,CAAC6H,aAAa,CAACnQ,SAAS,EAAE,IAAI,EAAEA,SAAS,EAAG5B,CAAC,IAAK;YACxD,IAAIA,CAAC,CAACnB,OAAO,YAAY6B,yBAAyB,EAAE;cAChD,IAAIgS,cAAc,EAAE;gBAChB,IAAI,CAACD,YAAY,EAAE;kBACf,IAAI,IAAI,CAAC1I,kBAAkB,CAAC1C,kBAAkB,CAACrH,CAAC,CAACnB,OAAO,CAAC,EAAE;oBACvD,IAAI,CAACqL,KAAK,CAACiF,MAAM,CAACnP,CAAC,CAACnB,OAAO,CAAC;kBAChC,CAAC,MACI;oBACD,IAAI,CAACqL,KAAK,CAACiF,MAAM,CAACnP,CAAC,CAACnB,OAAO,EAAE,CAAC,CAAC;kBACnC;kBACA4T,YAAY,GAAGzS,CAAC,CAACnB,OAAO,CAAC+B,QAAQ,CAAC,CAAC,CAAC;gBACxC;cACJ,CAAC,MACI;gBACD8R,cAAc,GAAG,IAAI;cACzB;YACJ,CAAC,MACI,IAAI1S,CAAC,CAACnB,OAAO,YAAYW,oBAAoB,EAAE;cAChD,IAAI,CAACiT,YAAY,EAAE;gBACf,IAAIzS,CAAC,CAACnB,OAAO,CAACwB,SAAS,EAAE;kBACrB,IAAI,IAAI,CAAC2J,aAAa,CAACnD,0BAA0B,CAAC7G,CAAC,CAACnB,OAAO,CAAC,EAAE;oBAC1D,IAAI,CAACqL,KAAK,CAACiF,MAAM,CAACnP,CAAC,CAACnB,OAAO,CAAC;kBAChC,CAAC,MACI;oBACD,IAAI,CAACqL,KAAK,CAACiF,MAAM,CAACnP,CAAC,CAACnB,OAAO,EAAE,CAAC,CAAC;kBACnC;kBACA4T,YAAY,GAAGzS,CAAC,CAACnB,OAAO;gBAC5B,CAAC,MACI,IAAImB,CAAC,CAACnB,OAAO,KAAK,IAAI,CAACuK,YAAY,CAAC,CAAC,CAAC,EAAE;kBACzC;kBACA,IAAI,CAACc,KAAK,CAACiF,MAAM,CAACnP,CAAC,CAACnB,OAAO,EAAE,CAAC,CAAC;kBAC/B,OAAO,IAAI;gBACf;cACJ;YACJ;YACA,OAAO,KAAK;UAChB,CAAC,CAAC;UACF,IAAI4T,YAAY,EAAE;YACd,IAAI,CAACvI,KAAK,CAACgF,QAAQ,CAAC,CAACuD,YAAY,CAAC,CAAC;UACvC;UACA;QACJ;IACJ;EACJ;EACAE,UAAUA,CAAA,EAAG;IACT,IAAI,CAACzI,KAAK,CAACgF,QAAQ,CAAC,EAAE,CAAC;EAC3B;EACA0D,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC1I,KAAK,CAAC0I,QAAQ,CAAC,CAAC;EACzB;EACAC,MAAMA,CAACC,SAAS,EAAE;IACd,IAAI,CAAC5I,KAAK,CAACmB,cAAc,CAAC,CAAC,CAACpG,KAAK,CAAC6N,SAAS,GAAGA,SAAS,GAAG;IAC1D;IACAC,IAAI,CAACC,KAAK,CAACF,SAAS,GAAG,EAAE,CAAC,GAAG;IACzB;IAAA,EACE,CAAC,IAAI,GAAG,EAAE;IAChB,IAAI,CAAC5I,KAAK,CAAC2I,MAAM,CAAC,CAAC;EACvB;EACAvU,MAAMA,CAAC2U,KAAK,EAAE;IACV,IAAI,CAAC1I,gBAAgB,GAAG0I,KAAK;IAC7B,IAAI,EAAE,IAAI,CAACrJ,YAAY,IAAI,IAAI,CAACF,aAAa,IAAI,IAAI,CAACF,mBAAmB,IAAI,IAAI,CAACC,cAAc,CAAC,EAAE;MAC/F,IAAI,CAACS,KAAK,CAAC2I,MAAM,CAAC,CAAC;MACnB,OAAO,KAAK;IAChB;IACA,MAAMK,mBAAmB,GAAGD,KAAK;IACjCA,KAAK,GAAGA,KAAK,CAAClV,IAAI,CAAC,CAAC;IACpB;IACA,IAAI,CAACkV,KAAK,IAAI,EAAE,IAAI,CAAC/G,YAAY,IAAI,IAAI,CAACF,kBAAkB,IAAI,IAAI,CAACC,aAAa,CAAC,EAAE;MACjF,IAAI,CAAC5C,aAAa,CAACiG,OAAO,CAACzQ,OAAO,IAAI;QAClCA,OAAO,CAACK,eAAe,GAAG0C,SAAS;QACnC/C,OAAO,CAACO,qBAAqB,GAAGwC,SAAS;QACzC/C,OAAO,CAACS,gBAAgB,GAAGsC,SAAS;QACpC/C,OAAO,CAACE,MAAM,GAAG,KAAK;QACtB,MAAM8Q,QAAQ,GAAGhR,OAAO,CAACvB,KAAK,IAAI,IAAI,CAAC6L,cAAc,CAACtK,OAAO,CAACvB,KAAK,GAAG,CAAC,CAAC;QACxE,IAAIuB,OAAO,CAACc,IAAI,EAAE;UACdd,OAAO,CAACwB,SAAS,GAAGwP,QAAQ,IAAIA,QAAQ,CAAC9M,IAAI,KAAK,WAAW,IAAI,CAAC8M,QAAQ,CAACpJ,OAAO,GAAGoJ,QAAQ,GAAGjO,SAAS;QAC7G;MACJ,CAAC,CAAC;IACN;IACA;IAAA,KACK;MACD,IAAIuR,gBAAgB;MACpB,IAAI,CAAC9J,aAAa,CAACiG,OAAO,CAACzQ,OAAO,IAAI;QAClC,IAAIK,eAAe;QACnB,IAAI,IAAI,CAACiN,gBAAgB,KAAK,OAAO,EAAE;UACnCjN,eAAe,GAAG,IAAI,CAACgN,YAAY,GAAG3P,qBAAqB,CAAC0W,KAAK,EAAEzW,mBAAmB,CAACqC,OAAO,CAAClB,SAAS,CAAC,CAAC,IAAIiE,SAAS,GAAGA,SAAS;QACvI,CAAC,MACI;UACD1C,eAAe,GAAG,IAAI,CAACgN,YAAY,GAAGkH,0BAA0B,CAACF,mBAAmB,EAAE1W,mBAAmB,CAACqC,OAAO,CAAClB,SAAS,CAAC,CAAC,IAAIiE,SAAS,GAAGA,SAAS;QAC1J;QACA,MAAMxC,qBAAqB,GAAG,IAAI,CAAC4M,kBAAkB,GAAGzP,qBAAqB,CAAC0W,KAAK,EAAEzW,mBAAmB,CAACqC,OAAO,CAACX,eAAe,IAAI,EAAE,CAAC,CAAC,IAAI0D,SAAS,GAAGA,SAAS;QACjK,MAAMtC,gBAAgB,GAAG,IAAI,CAAC2M,aAAa,GAAG1P,qBAAqB,CAAC0W,KAAK,EAAEzW,mBAAmB,CAACqC,OAAO,CAACV,UAAU,IAAI,EAAE,CAAC,CAAC,IAAIyD,SAAS,GAAGA,SAAS;QAClJ,IAAI1C,eAAe,IAAIE,qBAAqB,IAAIE,gBAAgB,EAAE;UAC9DT,OAAO,CAACK,eAAe,GAAGA,eAAe;UACzCL,OAAO,CAACO,qBAAqB,GAAGA,qBAAqB;UACrDP,OAAO,CAACS,gBAAgB,GAAGA,gBAAgB;UAC3CT,OAAO,CAACE,MAAM,GAAG,KAAK;QAC1B,CAAC,MACI;UACDF,OAAO,CAACK,eAAe,GAAG0C,SAAS;UACnC/C,OAAO,CAACO,qBAAqB,GAAGwC,SAAS;UACzC/C,OAAO,CAACS,gBAAgB,GAAGsC,SAAS;UACpC/C,OAAO,CAACE,MAAM,GAAGF,OAAO,CAACc,IAAI,GAAG,CAACd,OAAO,CAACc,IAAI,CAAC0T,UAAU,GAAG,IAAI;QACnE;QACA;QACA,IAAIxU,OAAO,CAACc,IAAI,EAAE;UACdd,OAAO,CAACwB,SAAS,GAAGuB,SAAS;QACjC,CAAC,MACI,IAAI/C,OAAO,CAACwB,SAAS,EAAE;UACxBxB,OAAO,CAACE,MAAM,GAAG,IAAI;QACzB;QACA;QACA,IAAI,CAAC,IAAI,CAACuL,WAAW,EAAE;UACnB,MAAMuF,QAAQ,GAAGhR,OAAO,CAACvB,KAAK,IAAI,IAAI,CAAC6L,cAAc,CAACtK,OAAO,CAACvB,KAAK,GAAG,CAAC,CAAC,IAAIsE,SAAS;UACrF,IAAIiO,QAAQ,EAAE9M,IAAI,KAAK,WAAW,IAAI,CAAC8M,QAAQ,CAACpJ,OAAO,EAAE;YACrD0M,gBAAgB,GAAGtD,QAAQ;UAC/B;UACA,IAAIsD,gBAAgB,IAAI,CAACtU,OAAO,CAACE,MAAM,EAAE;YACrCF,OAAO,CAACwB,SAAS,GAAG8S,gBAAgB;YACpCA,gBAAgB,GAAGvR,SAAS;UAChC;QACJ;MACJ,CAAC,CAAC;IACN;IACA,IAAI,CAACmO,kBAAkB,CAAC,IAAI,CAACnG,YAAY,IAAIqJ;IACzC;IAAA,EACE,IAAI,CAAC5J;IACP;IAAA,EACE,IAAI,CAACD,YAAY,CAAC;IACxB,IAAI,CAACc,KAAK,CAAC2I,MAAM,CAAC,CAAC;IACnB,OAAO,IAAI;EACf;EACA7F,cAAcA,CAAA,EAAG;IACb,IAAI,CAAC/D,4BAA4B,CAACoG,YAAY,CAAC,MAAM;MACjD,MAAM7D,QAAQ,GAAG,IAAI,CAACtB,KAAK,CAACwG,QAAQ,CAAC,CAAC,CAACpS,MAAM,CAAE0B,CAAC,IAAKA,CAAC,YAAYR,oBAAoB,CAAC;MACvF,MAAM8T,UAAU,GAAG,IAAI,CAACC,kBAAkB,CAAC/H,QAAQ,CAAC;MACpD,KAAK,MAAM3M,OAAO,IAAI2M,QAAQ,EAAE;QAC5B,IAAI,CAAC3M,OAAO,CAAC0B,gBAAgB,EAAE;UAC3B;UACA1B,OAAO,CAACoB,OAAO,GAAG,CAACqT,UAAU;QACjC;MACJ;IACJ,CAAC,CAAC;EACN;EACArO,KAAKA,CAACuO,MAAM,EAAE;IACV,IAAI,CAACtJ,KAAK,CAACjF,KAAK,CAACuO,MAAM,CAAC;EAC5B;EACAC,WAAWA,CAAA,EAAG;IACV,MAAMhD,OAAO,GAAG,IAAI,CAACvG,KAAK,CAACwG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;IACxC,IAAI,CAACD,OAAO,EAAExR,WAAW,IAAI,EAAEwR,OAAO,YAAYjR,oBAAoB,CAAC,EAAE;MACrE;IACJ;IACA;IACA,IAAI,IAAI,CAACkU,UAAU,IAAI,CAAC,IAAI,CAACA,UAAU,CAACC,UAAU,EAAE;MAChD,IAAI,CAACD,UAAU,CAAC3P,OAAO,CAAC,CAAC;MACzB;IACJ;IACA;IACA,IAAI,CAACwK,SAAS,CAACkC,OAAO,CAAC;IACvB,MAAMmD,KAAK,GAAG,IAAIlY,eAAe,CAAC,CAAC;IACnCkY,KAAK,CAACpR,GAAG,CAAC,IAAI,CAAC0H,KAAK,CAACqB,gBAAgB,CAACvL,CAAC,IAAI;MACvC,IAAIA,CAAC,CAACwL,QAAQ,CAAC,CAAC,CAAC,YAAYhM,oBAAoB,EAAE;QAC/C,IAAI,CAAC+O,SAAS,CAACvO,CAAC,CAACwL,QAAQ,CAAC,CAAC,CAAC,CAAC;MACjC;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,IAAI,CAACkI,UAAU,EAAE;MACjBE,KAAK,CAACpR,GAAG,CAAC,IAAI,CAACkR,UAAU,CAAC;IAC9B;IACA,IAAI,CAACpK,kBAAkB,CAAC9G,GAAG,CAACoR,KAAK,CAAC;EACtC;EACA;EACA;EACA7D,kBAAkBA,CAACvE,QAAQ,EAAE;IACzB,MAAMqI,YAAY,GAAG,IAAIhT,KAAK,CAAC,CAAC;IAChC,KAAK,MAAMhC,OAAO,IAAI2M,QAAQ,EAAE;MAC5B,IAAI3M,OAAO,YAAY6B,yBAAyB,EAAE;QAC9CmT,YAAY,CAACnN,IAAI,CAAC;UACd7H,OAAO;UACPiV,WAAW,EAAE,KAAK;UAClBC,SAAS,EAAE,KAAK;UAChBnT,QAAQ,EAAE/B,OAAO,CAAC+B,QAAQ,CAACxC,GAAG,CAAC4B,CAAC,KAAK;YACjCnB,OAAO,EAAEmB,CAAC;YACV8T,WAAW,EAAE,KAAK;YAClBC,SAAS,EAAE;UACf,CAAC,CAAC;QACN,CAAC,CAAC;MACN,CAAC,MACI;QACDF,YAAY,CAACnN,IAAI,CAAC;UACd7H,OAAO;UACPiV,WAAW,EAAE,KAAK;UAClBC,SAAS,EAAE;QACf,CAAC,CAAC;MACN;IACJ;IACA,IAAI,CAAC7J,KAAK,CAAC8J,WAAW,CAAC,IAAI,EAAEH,YAAY,CAAC;EAC9C;EACAN,kBAAkBA,CAAC/H,QAAQ,EAAEyI,eAAe,GAAG,IAAI,EAAE;IACjD,KAAK,IAAItZ,CAAC,GAAG,CAAC,EAAEuZ,CAAC,GAAG1I,QAAQ,CAACpR,MAAM,EAAEO,CAAC,GAAGuZ,CAAC,EAAEvZ,CAAC,EAAE,EAAE;MAC7C,MAAMkE,OAAO,GAAG2M,QAAQ,CAAC7Q,CAAC,CAAC;MAC3B,IAAI,CAACkE,OAAO,CAACE,MAAM,EAAE;QACjB,IAAI,CAACF,OAAO,CAACoB,OAAO,EAAE;UAClB,OAAO,KAAK;QAChB,CAAC,MACI;UACDgU,eAAe,GAAG,IAAI;QAC1B;MACJ;IACJ;IACA,OAAOA,eAAe;EAC1B;EACA1G,yBAAyBA,CAAA,EAAG;IACxBtQ,WAAW,CAAEkX,EAAE,IAAK;MAChB,IAAI,CAAC9L,4BAA4B,CAAClC,GAAG,CAAC,IAAI,CAACoN,kBAAkB,CAAC,IAAI,CAAClK,aAAa,EAAE,KAAK,CAAC,EAAE8K,EAAE,CAAC;MAC7F,MAAMC,YAAY,GAAG,IAAI,CAAC/K,aAAa,CAAC/K,MAAM,CAACO,OAAO,IAAIA,OAAO,CAACoB,OAAO,CAAC,CAAC7F,MAAM;MACjF,IAAI,CAACmO,uBAAuB,CAACpC,GAAG,CAACiO,YAAY,EAAED,EAAE,CAAC;MAClD,IAAI,CAAC1L,0BAA0B,CAACtC,GAAG,CAAC,IAAI,CAAC2K,kBAAkB,CAAC,CAAC,EAAEqD,EAAE,CAAC;IACtE,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACI5F,SAASA,CAAC1P,OAAO,EAAE;IACf,IAAI,IAAI,CAAC6U,UAAU,IAAI,CAAC,IAAI,CAACA,UAAU,CAACC,UAAU,EAAE;MAChD,IAAI,CAAC5R,aAAa,CAACsS,cAAc,GAAG,CAAC;MACrC,IAAI,CAACX,UAAU,EAAE3P,OAAO,CAAC,CAAC;IAC9B;IACA,IAAI,CAAClF,OAAO,CAACA,OAAO,IAAI,CAACA,OAAO,CAACI,WAAW,EAAE;MAC1C;IACJ;IACA,IAAI,CAACyU,UAAU,GAAG,IAAI,CAAC3R,aAAa,CAACwM,SAAS,CAAC;MAC3C+F,OAAO,EAAEzV,OAAO,CAACI,WAAW;MAC5BlF,MAAM,EAAE8E,OAAO,CAACA,OAAO;MACvB0V,WAAW,EAAGC,GAAG,IAAK;QAClB,IAAI,CAAC9M,kBAAkB,CAAC8M,GAAG,CAAC;MAChC,CAAC;MACDC,UAAU,EAAE;QACRC,WAAW,EAAE;MACjB,CAAC;MACDzS,SAAS,EAAE,IAAI,CAAC6H,UAAU;MAC1B6K,QAAQ,EAAE;QACNC,aAAa,EAAE,CAAC,CAAC;MACrB;IACJ,CAAC,EAAE,KAAK,CAAC;EACb;AACJ,CAAC;AACD/a,UAAU,CAAC,CACPiC,OAAO,CACV,EAAE0L,cAAc,CAACqN,SAAS,EAAE,kBAAkB,EAAE,IAAI,CAAC;AACtDhb,UAAU,CAAC,CACPiC,OAAO,CACV,EAAE0L,cAAc,CAACqN,SAAS,EAAE,sBAAsB,EAAE,IAAI,CAAC;AAC1DrN,cAAc,GAAG3N,UAAU,CAAC,CACxBgB,OAAO,CAAC,CAAC,EAAES,qBAAqB,CAAC,EACjCT,OAAO,CAAC,CAAC,EAAEiC,qBAAqB,CAAC,CACpC,EAAE0K,cAAc,CAAC;AAClB,SAASA,cAAc;AACvB,SAAS4L,0BAA0BA,CAACH,KAAK,EAAElZ,MAAM,EAAE;EAC/C,MAAM;IAAE+D,IAAI;IAAEgX;EAAY,CAAC,GAAG/a,MAAM;EACpC;EACA,IAAI,CAAC+a,WAAW,IAAIA,WAAW,CAAC1a,MAAM,KAAK,CAAC,EAAE;IAC1C,OAAO2a,iBAAiB,CAAC9B,KAAK,EAAEnV,IAAI,CAAC;EACzC;EACA;EACA;EACA,MAAMkX,qCAAqC,GAAGtY,KAAK,CAACoB,IAAI,EAAE,GAAG,CAAC;EAC9D,MAAMmX,uBAAuB,GAAGnX,IAAI,CAAC1D,MAAM,GAAG4a,qCAAqC,CAAC5a,MAAM;EAC1F;EACA,MAAMsL,OAAO,GAAGqP,iBAAiB,CAAC9B,KAAK,EAAE+B,qCAAqC,CAAC;EAC/E;EACA,IAAItP,OAAO,EAAE;IACT,KAAK,MAAMwP,KAAK,IAAIxP,OAAO,EAAE;MACzB,MAAMyP,UAAU,GAAGL,WAAW,CAACI,KAAK,CAACE,KAAK,GAAGH,uBAAuB,CAAC,CAAC,8BAA8BA,uBAAuB,CAAC;MAC5HC,KAAK,CAACE,KAAK,IAAID,UAAU;MACzBD,KAAK,CAACG,GAAG,IAAIF,UAAU;IAC3B;EACJ;EACA,OAAOzP,OAAO;AAClB;AACA,SAASqP,iBAAiBA,CAACO,IAAI,EAAEC,kBAAkB,EAAE;EACjD,MAAMC,UAAU,GAAGD,kBAAkB,CAAC9K,WAAW,CAAC,CAAC,CAACgL,OAAO,CAACH,IAAI,CAAC7K,WAAW,CAAC,CAAC,CAAC;EAC/E,IAAI+K,UAAU,KAAK,CAAC,CAAC,EAAE;IACnB,OAAO,CAAC;MAAEJ,KAAK,EAAEI,UAAU;MAAEH,GAAG,EAAEG,UAAU,GAAGF,IAAI,CAAClb;IAAO,CAAC,CAAC;EACjE;EACA,OAAO,IAAI;AACf;AACA,SAASsQ,cAAcA,CAACgL,QAAQ,EAAEC,QAAQ,EAAEC,OAAO,EAAE;EACjD,MAAMC,gBAAgB,GAAGH,QAAQ,CAACxW,eAAe,IAAI,EAAE;EACvD,MAAM4W,gBAAgB,GAAGH,QAAQ,CAACzW,eAAe,IAAI,EAAE;EACvD,IAAI2W,gBAAgB,CAACzb,MAAM,IAAI,CAAC0b,gBAAgB,CAAC1b,MAAM,EAAE;IACrD,OAAO,CAAC,CAAC;EACb;EACA,IAAI,CAACyb,gBAAgB,CAACzb,MAAM,IAAI0b,gBAAgB,CAAC1b,MAAM,EAAE;IACrD,OAAO,CAAC;EACZ;EACA,IAAIyb,gBAAgB,CAACzb,MAAM,KAAK,CAAC,IAAI0b,gBAAgB,CAAC1b,MAAM,KAAK,CAAC,EAAE;IAChE,OAAO,CAAC;EACZ;EACA,OAAOqC,eAAe,CAACiZ,QAAQ,CAAC7X,aAAa,EAAE8X,QAAQ,CAAC9X,aAAa,EAAE+X,OAAO,CAAC;AACnF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}