{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport class CursorContext {\n  constructor(model, viewModel, coordinatesConverter, cursorConfig) {\n    this._cursorContextBrand = undefined;\n    this.model = model;\n    this.viewModel = viewModel;\n    this.coordinatesConverter = coordinatesConverter;\n    this.cursorConfig = cursorConfig;\n  }\n}", "map": {"version": 3, "names": ["CursorContext", "constructor", "model", "viewModel", "coordinatesConverter", "cursorConfig", "_cursorContextBrand", "undefined"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/common/cursor/cursorContext.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport class CursorContext {\n    constructor(model, viewModel, coordinatesConverter, cursorConfig) {\n        this._cursorContextBrand = undefined;\n        this.model = model;\n        this.viewModel = viewModel;\n        this.coordinatesConverter = coordinatesConverter;\n        this.cursorConfig = cursorConfig;\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,MAAMA,aAAa,CAAC;EACvBC,WAAWA,CAACC,KAAK,EAAEC,SAAS,EAAEC,oBAAoB,EAAEC,YAAY,EAAE;IAC9D,IAAI,CAACC,mBAAmB,GAAGC,SAAS;IACpC,IAAI,CAACL,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,oBAAoB,GAAGA,oBAAoB;IAChD,IAAI,CAACC,YAAY,GAAGA,YAAY;EACpC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}