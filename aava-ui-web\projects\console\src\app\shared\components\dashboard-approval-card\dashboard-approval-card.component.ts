import { Component, EventEmitter, Input, Output } from '@angular/core';
import { ApprovalCardComponent, AvaTagComponent, IconComponent, ButtonComponent } from "@ava/play-comp-library";

@Component({
  selector: 'app-dashboard-approval-card',
  imports: [ApprovalCardComponent, AvaTagComponent, IconComponent, ButtonComponent],
  templateUrl: './dashboard-approval-card.component.html',
  styleUrl: './dashboard-approval-card.component.scss'
})
export class DashboardApprovalCardComponent {
  @Input() title = ""
  @Input() avatarUrl = "assets/images/avatar.png"
  @Input() date = "12 May 2025"
  @Input() email = '<EMAIL>'
  @Input() type = ''
  @Input() id = ''
  @Input() rowData: any
  @Output() testClick = new EventEmitter<number>();
  @Output() sendBackClick = new EventEmitter<number>();
  @Output() approveClick = new EventEmitter<number>();




  approvalBtnCustomStyles: Record<string, string> = {
    'background': 'linear-gradient(130.87deg, #0084FF 33.91%, #03BDD4 100%)',
  };

  onApproveClick(rowData: any) {
    this.approveClick.emit(rowData);
  }

  onSendBackClick(rowData: any) {
    this.sendBackClick.emit(rowData);
  }

  onTestClick() {
    this.testClick.emit(this.rowData);
  }

}
