{"ast": null, "code": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { EditorCommand, registerEditorCommand } from '../../../browser/editorExtensions.js';\nimport { IContextKeyService, RawContextKey } from '../../../../platform/contextkey/common/contextkey.js';\nimport { CancellationTokenSource } from '../../../../base/common/cancellation.js';\nimport { LinkedList } from '../../../../base/common/linkedList.js';\nimport { createDecorator } from '../../../../platform/instantiation/common/instantiation.js';\nimport { registerSingleton } from '../../../../platform/instantiation/common/extensions.js';\nimport { localize } from '../../../../nls.js';\nconst IEditorCancellationTokens = createDecorator('IEditorCancelService');\nconst ctxCancellableOperation = new RawContextKey('cancellableOperation', false, localize('cancellableOperation', 'Whether the editor runs a cancellable operation, e.g. like \\'Peek References\\''));\nregisterSingleton(IEditorCancellationTokens, class {\n  constructor() {\n    this._tokens = new WeakMap();\n  }\n  add(editor, cts) {\n    let data = this._tokens.get(editor);\n    if (!data) {\n      data = editor.invokeWithinContext(accessor => {\n        const key = ctxCancellableOperation.bindTo(accessor.get(IContextKeyService));\n        const tokens = new LinkedList();\n        return {\n          key,\n          tokens\n        };\n      });\n      this._tokens.set(editor, data);\n    }\n    let removeFn;\n    data.key.set(true);\n    removeFn = data.tokens.push(cts);\n    return () => {\n      // remove w/o cancellation\n      if (removeFn) {\n        removeFn();\n        data.key.set(!data.tokens.isEmpty());\n        removeFn = undefined;\n      }\n    };\n  }\n  cancel(editor) {\n    const data = this._tokens.get(editor);\n    if (!data) {\n      return;\n    }\n    // remove with cancellation\n    const cts = data.tokens.pop();\n    if (cts) {\n      cts.cancel();\n      data.key.set(!data.tokens.isEmpty());\n    }\n  }\n}, 1 /* InstantiationType.Delayed */);\nexport class EditorKeybindingCancellationTokenSource extends CancellationTokenSource {\n  constructor(editor, parent) {\n    super(parent);\n    this.editor = editor;\n    this._unregister = editor.invokeWithinContext(accessor => accessor.get(IEditorCancellationTokens).add(editor, this));\n  }\n  dispose() {\n    this._unregister();\n    super.dispose();\n  }\n}\nregisterEditorCommand(new class extends EditorCommand {\n  constructor() {\n    super({\n      id: 'editor.cancelOperation',\n      kbOpts: {\n        weight: 100 /* KeybindingWeight.EditorContrib */,\n        primary: 9 /* KeyCode.Escape */\n      },\n      precondition: ctxCancellableOperation\n    });\n  }\n  runEditorCommand(accessor, editor) {\n    accessor.get(IEditorCancellationTokens).cancel(editor);\n  }\n}());", "map": {"version": 3, "names": ["EditorCommand", "registerEditorCommand", "IContextKeyService", "RawContextKey", "CancellationTokenSource", "LinkedList", "createDecorator", "registerSingleton", "localize", "IEditorCancellationTokens", "ctxCancellableOperation", "constructor", "_tokens", "WeakMap", "add", "editor", "cts", "data", "get", "invokeWithinContext", "accessor", "key", "bindTo", "tokens", "set", "removeFn", "push", "isEmpty", "undefined", "cancel", "pop", "EditorKeybindingCancellationTokenSource", "parent", "_unregister", "dispose", "id", "kbOpts", "weight", "primary", "precondition", "runEditorCommand"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/contrib/editorState/browser/keybindingCancellation.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { EditorCommand, registerEditorCommand } from '../../../browser/editorExtensions.js';\nimport { IContextKeyService, RawContextKey } from '../../../../platform/contextkey/common/contextkey.js';\nimport { CancellationTokenSource } from '../../../../base/common/cancellation.js';\nimport { LinkedList } from '../../../../base/common/linkedList.js';\nimport { createDecorator } from '../../../../platform/instantiation/common/instantiation.js';\nimport { registerSingleton } from '../../../../platform/instantiation/common/extensions.js';\nimport { localize } from '../../../../nls.js';\nconst IEditorCancellationTokens = createDecorator('IEditorCancelService');\nconst ctxCancellableOperation = new RawContextKey('cancellableOperation', false, localize('cancellableOperation', 'Whether the editor runs a cancellable operation, e.g. like \\'Peek References\\''));\nregisterSingleton(IEditorCancellationTokens, class {\n    constructor() {\n        this._tokens = new WeakMap();\n    }\n    add(editor, cts) {\n        let data = this._tokens.get(editor);\n        if (!data) {\n            data = editor.invokeWithinContext(accessor => {\n                const key = ctxCancellableOperation.bindTo(accessor.get(IContextKeyService));\n                const tokens = new LinkedList();\n                return { key, tokens };\n            });\n            this._tokens.set(editor, data);\n        }\n        let removeFn;\n        data.key.set(true);\n        removeFn = data.tokens.push(cts);\n        return () => {\n            // remove w/o cancellation\n            if (removeFn) {\n                removeFn();\n                data.key.set(!data.tokens.isEmpty());\n                removeFn = undefined;\n            }\n        };\n    }\n    cancel(editor) {\n        const data = this._tokens.get(editor);\n        if (!data) {\n            return;\n        }\n        // remove with cancellation\n        const cts = data.tokens.pop();\n        if (cts) {\n            cts.cancel();\n            data.key.set(!data.tokens.isEmpty());\n        }\n    }\n}, 1 /* InstantiationType.Delayed */);\nexport class EditorKeybindingCancellationTokenSource extends CancellationTokenSource {\n    constructor(editor, parent) {\n        super(parent);\n        this.editor = editor;\n        this._unregister = editor.invokeWithinContext(accessor => accessor.get(IEditorCancellationTokens).add(editor, this));\n    }\n    dispose() {\n        this._unregister();\n        super.dispose();\n    }\n}\nregisterEditorCommand(new class extends EditorCommand {\n    constructor() {\n        super({\n            id: 'editor.cancelOperation',\n            kbOpts: {\n                weight: 100 /* KeybindingWeight.EditorContrib */,\n                primary: 9 /* KeyCode.Escape */\n            },\n            precondition: ctxCancellableOperation\n        });\n    }\n    runEditorCommand(accessor, editor) {\n        accessor.get(IEditorCancellationTokens).cancel(editor);\n    }\n});\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,aAAa,EAAEC,qBAAqB,QAAQ,sCAAsC;AAC3F,SAASC,kBAAkB,EAAEC,aAAa,QAAQ,sDAAsD;AACxG,SAASC,uBAAuB,QAAQ,yCAAyC;AACjF,SAASC,UAAU,QAAQ,uCAAuC;AAClE,SAASC,eAAe,QAAQ,4DAA4D;AAC5F,SAASC,iBAAiB,QAAQ,yDAAyD;AAC3F,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,MAAMC,yBAAyB,GAAGH,eAAe,CAAC,sBAAsB,CAAC;AACzE,MAAMI,uBAAuB,GAAG,IAAIP,aAAa,CAAC,sBAAsB,EAAE,KAAK,EAAEK,QAAQ,CAAC,sBAAsB,EAAE,gFAAgF,CAAC,CAAC;AACpMD,iBAAiB,CAACE,yBAAyB,EAAE,MAAM;EAC/CE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,OAAO,GAAG,IAAIC,OAAO,CAAC,CAAC;EAChC;EACAC,GAAGA,CAACC,MAAM,EAAEC,GAAG,EAAE;IACb,IAAIC,IAAI,GAAG,IAAI,CAACL,OAAO,CAACM,GAAG,CAACH,MAAM,CAAC;IACnC,IAAI,CAACE,IAAI,EAAE;MACPA,IAAI,GAAGF,MAAM,CAACI,mBAAmB,CAACC,QAAQ,IAAI;QAC1C,MAAMC,GAAG,GAAGX,uBAAuB,CAACY,MAAM,CAACF,QAAQ,CAACF,GAAG,CAAChB,kBAAkB,CAAC,CAAC;QAC5E,MAAMqB,MAAM,GAAG,IAAIlB,UAAU,CAAC,CAAC;QAC/B,OAAO;UAAEgB,GAAG;UAAEE;QAAO,CAAC;MAC1B,CAAC,CAAC;MACF,IAAI,CAACX,OAAO,CAACY,GAAG,CAACT,MAAM,EAAEE,IAAI,CAAC;IAClC;IACA,IAAIQ,QAAQ;IACZR,IAAI,CAACI,GAAG,CAACG,GAAG,CAAC,IAAI,CAAC;IAClBC,QAAQ,GAAGR,IAAI,CAACM,MAAM,CAACG,IAAI,CAACV,GAAG,CAAC;IAChC,OAAO,MAAM;MACT;MACA,IAAIS,QAAQ,EAAE;QACVA,QAAQ,CAAC,CAAC;QACVR,IAAI,CAACI,GAAG,CAACG,GAAG,CAAC,CAACP,IAAI,CAACM,MAAM,CAACI,OAAO,CAAC,CAAC,CAAC;QACpCF,QAAQ,GAAGG,SAAS;MACxB;IACJ,CAAC;EACL;EACAC,MAAMA,CAACd,MAAM,EAAE;IACX,MAAME,IAAI,GAAG,IAAI,CAACL,OAAO,CAACM,GAAG,CAACH,MAAM,CAAC;IACrC,IAAI,CAACE,IAAI,EAAE;MACP;IACJ;IACA;IACA,MAAMD,GAAG,GAAGC,IAAI,CAACM,MAAM,CAACO,GAAG,CAAC,CAAC;IAC7B,IAAId,GAAG,EAAE;MACLA,GAAG,CAACa,MAAM,CAAC,CAAC;MACZZ,IAAI,CAACI,GAAG,CAACG,GAAG,CAAC,CAACP,IAAI,CAACM,MAAM,CAACI,OAAO,CAAC,CAAC,CAAC;IACxC;EACJ;AACJ,CAAC,EAAE,CAAC,CAAC,+BAA+B,CAAC;AACrC,OAAO,MAAMI,uCAAuC,SAAS3B,uBAAuB,CAAC;EACjFO,WAAWA,CAACI,MAAM,EAAEiB,MAAM,EAAE;IACxB,KAAK,CAACA,MAAM,CAAC;IACb,IAAI,CAACjB,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACkB,WAAW,GAAGlB,MAAM,CAACI,mBAAmB,CAACC,QAAQ,IAAIA,QAAQ,CAACF,GAAG,CAACT,yBAAyB,CAAC,CAACK,GAAG,CAACC,MAAM,EAAE,IAAI,CAAC,CAAC;EACxH;EACAmB,OAAOA,CAAA,EAAG;IACN,IAAI,CAACD,WAAW,CAAC,CAAC;IAClB,KAAK,CAACC,OAAO,CAAC,CAAC;EACnB;AACJ;AACAjC,qBAAqB,CAAC,IAAI,cAAcD,aAAa,CAAC;EAClDW,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC;MACFwB,EAAE,EAAE,wBAAwB;MAC5BC,MAAM,EAAE;QACJC,MAAM,EAAE,GAAG,CAAC;QACZC,OAAO,EAAE,CAAC,CAAC;MACf,CAAC;MACDC,YAAY,EAAE7B;IAClB,CAAC,CAAC;EACN;EACA8B,gBAAgBA,CAACpB,QAAQ,EAAEL,MAAM,EAAE;IAC/BK,QAAQ,CAACF,GAAG,CAACT,yBAAyB,CAAC,CAACoB,MAAM,CAACd,MAAM,CAAC;EAC1D;AACJ,CAAC,CAAD,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}